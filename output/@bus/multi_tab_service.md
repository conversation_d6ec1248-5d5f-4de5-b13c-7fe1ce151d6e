# Odoo 多标签页服务 (Multi Tab Service) 学习资料

## 文件概述

**文件路径**: `output/@bus/multi_tab_service.js`  
**原始路径**: `/bus/static/src/multi_tab_service.js`  
**模块类型**: 核心服务 - 多标签页协调  
**代码行数**: 238 行  
**依赖关系**: 
- `@web/core/registry` - 注册表系统
- `@web/core/browser/browser` - 浏览器工具
- `@odoo/owl` - OWL框架

## 模块功能

多标签页服务模块是 Odoo Web 客户端的标签页协调系统。该模块提供了：
- 主标签页选举机制
- 标签页间状态同步
- 心跳检测和监控
- 标签页生命周期管理
- 共享数据管理
- 标签页间通信

这个服务使用主从架构和领导者选举算法，确保多个标签页中只有一个主标签页负责关键操作。

## 多标签页服务架构

### 核心组件结构
```
Multi Tab Service
├── 领导者选举
│   ├── 主标签页选举
│   ├── 心跳检测机制
│   ├── 故障转移
│   └── 状态同步
├── 本地存储管理
│   ├── 标签页状态存储
│   ├── 心跳时间记录
│   ├── 共享值管理
│   └── 存储事件监听
├── 事件系统
│   ├── 主标签页事件
│   ├── 状态变化事件
│   ├── 共享值更新事件
│   └── 标签页通信事件
└── 生命周期管理
    ├── 标签页注册
    ├── 心跳维护
    ├── 清理机制
    └── 故障检测
```

### 存储键结构
```javascript
// localStorage 键结构
const storageKeys = {
    lastPresenceByTab: `${prefix}.lastPresenceByTab`,  // 标签页最后活动时间
    main: `${prefix}.main`,                            // 主标签页ID
    heartbeat: `${prefix}.heartbeat`,                  // 主标签页心跳时间
    sharedValues: `${prefix}.shared.*`                 // 共享值
};
```

### 服务配置
```javascript
const multiTabService = {
    start() {
        const bus = new EventBus();
        
        // 常量配置
        const TAB_HEARTBEAT_PERIOD = 10000;        // 标签页心跳周期: 10秒
        const MAIN_TAB_HEARTBEAT_PERIOD = 1500;    // 主标签页心跳周期: 1.5秒
        const HEARTBEAT_OUT_OF_DATE_PERIOD = 5000; // 心跳过期时间: 5秒
        const HEARTBEAT_KILL_OLD_PERIOD = 15000;   // 清理旧标签页时间: 15秒
        
        // 服务实现...
        return serviceInstance;
    }
};
```

## 核心功能详解

### 1. 领导者选举机制
```javascript
// 领导者选举和主标签页管理
class TabLeaderElection {
    constructor() {
        this.tabId = this.generateTabId();
        this.isMainTab = false;
        this.lastHeartbeat = 0;
        this.heartbeatTimeout = null;
        this.bus = new EventBus();
        
        // 配置常量
        this.TAB_HEARTBEAT_PERIOD = 10000;
        this.MAIN_TAB_HEARTBEAT_PERIOD = 1500;
        this.HEARTBEAT_OUT_OF_DATE_PERIOD = 5000;
        this.HEARTBEAT_KILL_OLD_PERIOD = 15000;
        
        this.setupElection();
    }
    
    generateTabId() {
        return `tab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    setupElection() {
        // 监听存储变化
        window.addEventListener('storage', this.onStorageChange.bind(this));
        
        // 监听页面卸载
        window.addEventListener('beforeunload', this.onBeforeUnload.bind(this));
        
        // 开始选举过程
        this.startElection();
        
        // 定期检查主标签页状态
        setInterval(() => {
            this.checkMainTabStatus();
        }, this.TAB_HEARTBEAT_PERIOD);
    }
    
    startElection() {
        const currentMainTab = this.getMainTabId();
        const currentHeartbeat = this.getMainTabHeartbeat();
        const now = Date.now();
        
        if (!currentMainTab || this.isHeartbeatExpired(currentHeartbeat, now)) {
            // 没有主标签页或主标签页心跳过期，尝试成为主标签页
            this.becomeMainTab();
        } else {
            // 已有主标签页，成为从标签页
            this.becomeSlaveTab();
        }
    }
    
    becomeMainTab() {
        if (this.isMainTab) {
            return; // 已经是主标签页
        }
        
        this.isMainTab = true;
        this.setMainTabId(this.tabId);
        this.startMainTabHeartbeat();
        
        console.log(`标签页 ${this.tabId} 成为主标签页`);
        
        // 触发成为主标签页事件
        this.bus.trigger('become_main_tab', {
            tabId: this.tabId,
            timestamp: Date.now()
        });
    }
    
    becomeSlaveTab() {
        if (!this.isMainTab) {
            return; // 已经是从标签页
        }
        
        this.isMainTab = false;
        this.stopMainTabHeartbeat();
        
        console.log(`标签页 ${this.tabId} 成为从标签页`);
        
        // 触发不再是主标签页事件
        this.bus.trigger('no_longer_main_tab', {
            tabId: this.tabId,
            timestamp: Date.now()
        });
    }
    
    startMainTabHeartbeat() {
        this.stopMainTabHeartbeat();
        
        const sendHeartbeat = () => {
            if (this.isMainTab) {
                this.lastHeartbeat = Date.now();
                this.setMainTabHeartbeat(this.lastHeartbeat);
                
                // 清理过期的标签页
                this.cleanupExpiredTabs();
                
                this.heartbeatTimeout = setTimeout(sendHeartbeat, this.MAIN_TAB_HEARTBEAT_PERIOD);
            }
        };
        
        sendHeartbeat();
    }
    
    stopMainTabHeartbeat() {
        if (this.heartbeatTimeout) {
            clearTimeout(this.heartbeatTimeout);
            this.heartbeatTimeout = null;
        }
    }
    
    checkMainTabStatus() {
        const currentMainTab = this.getMainTabId();
        const currentHeartbeat = this.getMainTabHeartbeat();
        const now = Date.now();
        
        if (this.isMainTab) {
            // 检查是否仍然是主标签页
            if (currentMainTab !== this.tabId) {
                this.becomeSlaveTab();
            }
        } else {
            // 检查是否需要成为主标签页
            if (!currentMainTab || this.isHeartbeatExpired(currentHeartbeat, now)) {
                this.startElection();
            }
        }
        
        // 更新自己的存在时间
        this.updateTabPresence();
    }
    
    updateTabPresence() {
        const presenceData = this.getTabPresenceData();
        presenceData[this.tabId] = Date.now();
        this.setTabPresenceData(presenceData);
    }
    
    cleanupExpiredTabs() {
        const presenceData = this.getTabPresenceData();
        const now = Date.now();
        let hasChanges = false;
        
        Object.keys(presenceData).forEach(tabId => {
            const lastPresence = presenceData[tabId];
            
            if (now - lastPresence > this.HEARTBEAT_KILL_OLD_PERIOD) {
                delete presenceData[tabId];
                hasChanges = true;
                console.log(`清理过期标签页: ${tabId}`);
            }
        });
        
        if (hasChanges) {
            this.setTabPresenceData(presenceData);
        }
    }
    
    isHeartbeatExpired(heartbeat, now) {
        return !heartbeat || (now - heartbeat) > this.HEARTBEAT_OUT_OF_DATE_PERIOD;
    }
    
    onStorageChange(event) {
        const { key, newValue, oldValue } = event;
        
        if (key === this.getStorageKey('main')) {
            this.handleMainTabChange(newValue, oldValue);
        } else if (key === this.getStorageKey('heartbeat')) {
            this.handleHeartbeatChange(newValue, oldValue);
        } else if (key.startsWith(this.getStorageKey(''))) {
            this.handleSharedValueChange(key, newValue, oldValue);
        }
    }
    
    handleMainTabChange(newValue, oldValue) {
        const newMainTab = newValue;
        const oldMainTab = oldValue;
        
        if (newMainTab === this.tabId && !this.isMainTab) {
            this.becomeMainTab();
        } else if (oldMainTab === this.tabId && newMainTab !== this.tabId) {
            this.becomeSlaveTab();
        }
    }
    
    handleHeartbeatChange(newValue, oldValue) {
        // 心跳变化处理
        const newHeartbeat = parseInt(newValue) || 0;
        
        if (this.isMainTab && newHeartbeat > this.lastHeartbeat) {
            // 有其他标签页成为了主标签页
            this.becomeSlaveTab();
        }
    }
    
    handleSharedValueChange(key, newValue, oldValue) {
        // 共享值变化处理
        const keyName = key.replace(this.getStorageKey(''), '');
        
        // 过滤私有键
        const privateKeys = ['main', 'heartbeat', 'lastPresenceByTab'];
        if (privateKeys.includes(keyName)) {
            return;
        }
        
        this.bus.trigger('shared_value_updated', {
            key: keyName,
            newValue: newValue,
            oldValue: oldValue,
            timestamp: Date.now()
        });
    }
    
    onBeforeUnload() {
        if (this.isMainTab) {
            // 主标签页关闭时，清除主标签页标记
            this.removeMainTabId();
        }
        
        // 清理自己的存在记录
        const presenceData = this.getTabPresenceData();
        delete presenceData[this.tabId];
        this.setTabPresenceData(presenceData);
    }
    
    // 存储操作方法
    getStorageKey(suffix) {
        const sanitizedOrigin = location.origin.replace(/:\/{0,2}/g, '_');
        return `multi_tab.${sanitizedOrigin}.${suffix}`;
    }
    
    getMainTabId() {
        return browser.localStorage.getItem(this.getStorageKey('main'));
    }
    
    setMainTabId(tabId) {
        browser.localStorage.setItem(this.getStorageKey('main'), tabId);
    }
    
    removeMainTabId() {
        browser.localStorage.removeItem(this.getStorageKey('main'));
    }
    
    getMainTabHeartbeat() {
        const heartbeat = browser.localStorage.getItem(this.getStorageKey('heartbeat'));
        return heartbeat ? parseInt(heartbeat) : 0;
    }
    
    setMainTabHeartbeat(timestamp) {
        browser.localStorage.setItem(this.getStorageKey('heartbeat'), timestamp.toString());
    }
    
    getTabPresenceData() {
        const data = browser.localStorage.getItem(this.getStorageKey('lastPresenceByTab'));
        return data ? JSON.parse(data) : {};
    }
    
    setTabPresenceData(data) {
        browser.localStorage.setItem(this.getStorageKey('lastPresenceByTab'), JSON.stringify(data));
    }
    
    // 公共接口
    isMainTabActive() {
        return this.isMainTab;
    }
    
    getTabId() {
        return this.tabId;
    }
    
    getAllTabs() {
        return Object.keys(this.getTabPresenceData());
    }
    
    getTabCount() {
        return this.getAllTabs().length;
    }
    
    subscribe(eventName, callback) {
        return this.bus.addEventListener(eventName, callback);
    }
    
    unsubscribe(eventName, callback) {
        this.bus.removeEventListener(eventName, callback);
    }
}
```

## 使用示例

### 1. 基本多标签页管理
```javascript
// 在组件中使用多标签页服务
class MultiTabComponent extends Component {
    static template = xml`
        <div class="multi-tab-info">
            <div class="tab-status">
                <span t-if="isMainTab" class="badge badge-primary">主标签页</span>
                <span t-else="" class="badge badge-secondary">从标签页</span>
            </div>
            
            <div class="tab-details">
                <div>标签页ID: <span t-esc="tabId" /></div>
                <div>总标签页数: <span t-esc="tabCount" /></div>
                <div>共享值数量: <span t-esc="sharedValueCount" /></div>
            </div>
            
            <div class="tab-actions">
                <button t-on-click="sendTestMessage" class="btn btn-sm btn-info">
                    发送测试消息
                </button>
                <button t-on-click="setSharedValue" class="btn btn-sm btn-success">
                    设置共享值
                </button>
                <button t-on-click="clearSharedValues" class="btn btn-sm btn-warning">
                    清空共享值
                </button>
            </div>
        </div>
    `;
    
    setup() {
        this.multiTabService = useService('multi_tab');
        this.tabStatus = useState({
            isMainTab: false,
            tabId: '',
            tabCount: 0,
            sharedValueCount: 0
        });
        
        onWillStart(() => {
            this.setupMultiTabMonitoring();
        });
        
        onWillUnmount(() => {
            this.cleanupSubscriptions();
        });
    }
    
    setupMultiTabMonitoring() {
        // 监听主标签页变化
        this.mainTabSubscription = this.multiTabService.bus.addEventListener('become_main_tab', () => {
            this.handleBecomeMainTab();
        });
        
        this.slaveTabSubscription = this.multiTabService.bus.addEventListener('no_longer_main_tab', () => {
            this.handleBecomeSlaveTab();
        });
        
        // 监听共享值变化
        this.sharedValueSubscription = this.multiTabService.bus.addEventListener('shared_value_updated', (data) => {
            this.handleSharedValueUpdate(data);
        });
        
        // 初始化状态
        this.updateTabStatus();
    }
    
    handleBecomeMainTab() {
        console.log('成为主标签页');
        this.updateTabStatus();
        
        // 主标签页特有的操作
        this.performMainTabTasks();
    }
    
    handleBecomeSlaveTab() {
        console.log('成为从标签页');
        this.updateTabStatus();
        
        // 停止主标签页特有的操作
        this.stopMainTabTasks();
    }
    
    handleSharedValueUpdate(data) {
        console.log('共享值更新:', data);
        this.updateTabStatus();
    }
    
    updateTabStatus() {
        this.tabStatus.isMainTab = this.multiTabService.isOnMainTab();
        this.tabStatus.tabId = this.multiTabService.currentTabId;
        this.tabStatus.tabCount = this.getTabCount();
        this.tabStatus.sharedValueCount = this.getSharedValueCount();
    }
    
    performMainTabTasks() {
        // 主标签页执行的任务
        console.log('执行主标签页任务');
        
        // 例如：定期同步数据、处理后台任务等
        this.mainTabInterval = setInterval(() => {
            this.syncDataFromServer();
        }, 30000);
    }
    
    stopMainTabTasks() {
        if (this.mainTabInterval) {
            clearInterval(this.mainTabInterval);
            this.mainTabInterval = null;
        }
    }
    
    syncDataFromServer() {
        // 主标签页负责从服务器同步数据
        console.log('主标签页同步数据');
        
        // 同步后将数据共享给其他标签页
        this.multiTabService.setSharedValue('lastSyncTime', Date.now());
    }
    
    sendTestMessage() {
        const message = {
            type: 'test',
            from: this.multiTabService.currentTabId,
            timestamp: Date.now(),
            data: '这是一条测试消息'
        };
        
        this.multiTabService.setSharedValue('test_message', message);
    }
    
    setSharedValue() {
        const randomValue = Math.random().toString(36).substr(2, 9);
        this.multiTabService.setSharedValue('test_value', randomValue);
    }
    
    clearSharedValues() {
        // 清空所有测试相关的共享值
        this.multiTabService.removeSharedValue('test_message');
        this.multiTabService.removeSharedValue('test_value');
        this.multiTabService.removeSharedValue('lastSyncTime');
    }
    
    getTabCount() {
        // 估算标签页数量（基于存储的数据）
        const lastPresenceByTab = this.multiTabService.getSharedValue('lastPresenceByTab', {});
        return Object.keys(lastPresenceByTab).length;
    }
    
    getSharedValueCount() {
        // 估算共享值数量
        let count = 0;
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.includes('multi_tab') && !key.includes('main') && !key.includes('heartbeat') && !key.includes('lastPresenceByTab')) {
                count++;
            }
        }
        return count;
    }
    
    get isMainTab() {
        return this.tabStatus.isMainTab;
    }
    
    get tabId() {
        return this.tabStatus.tabId;
    }
    
    get tabCount() {
        return this.tabStatus.tabCount;
    }
    
    get sharedValueCount() {
        return this.tabStatus.sharedValueCount;
    }
    
    cleanupSubscriptions() {
        if (this.mainTabSubscription) {
            this.mainTabSubscription();
        }
        if (this.slaveTabSubscription) {
            this.slaveTabSubscription();
        }
        if (this.sharedValueSubscription) {
            this.sharedValueSubscription();
        }
        
        this.stopMainTabTasks();
    }
}
```

## 多标签页服务特性

### 1. 领导者选举算法
多标签页服务使用基于心跳的领导者选举算法：
- **心跳机制**: 主标签页定期发送心跳信号
- **故障检测**: 检测主标签页是否失效
- **自动选举**: 自动选举新的主标签页
- **状态同步**: 所有标签页状态实时同步

### 2. 本地存储协调
通过localStorage实现标签页间的数据共享：
- **状态存储**: 标签页状态和心跳信息
- **数据共享**: 跨标签页的数据共享机制
- **事件通知**: 存储变化的事件通知
- **数据清理**: 自动清理过期数据

### 3. 容错机制
```javascript
// 容错和恢复机制
const HEARTBEAT_PERIODS = {
    TAB_HEARTBEAT: 10000,        // 标签页心跳: 10秒
    MAIN_TAB_HEARTBEAT: 1500,    // 主标签页心跳: 1.5秒
    OUT_OF_DATE: 5000,           // 心跳过期: 5秒
    CLEANUP_OLD: 15000           // 清理旧标签页: 15秒
};
```

### 4. 性能优化
- **最小化存储操作**: 减少localStorage的读写次数
- **批量更新**: 批量处理状态更新
- **内存缓存**: 缓存频繁访问的数据
- **定期清理**: 自动清理过期的标签页数据

## 最佳实践

### 1. 服务初始化
```javascript
// ✅ 推荐：正确的服务初始化
const multiTabService = {
    start() {
        const bus = new EventBus();
        // 初始化逻辑...
        return serviceInstance;
    }
};
```

### 2. 主标签页任务
```javascript
// ✅ 推荐：主标签页执行关键任务
multiTabService.bus.addEventListener('become_main_tab', () => {
    // 只在主标签页执行的任务
    startCriticalTasks();
});
```

### 3. 数据同步
```javascript
// ✅ 推荐：使用共享值进行数据同步
multiTabService.setSharedValue('user_data', userData);

multiTabService.bus.addEventListener('shared_value_updated', (data) => {
    if (data.key === 'user_data') {
        updateLocalUserData(data.newValue);
    }
});
```

## 总结

Odoo 多标签页服务模块提供了强大的标签页协调功能：

**核心优势**:
- **领导者选举**: 基于心跳的可靠领导者选举机制
- **状态同步**: 跨标签页的实时状态同步
- **数据共享**: 高效的标签页间数据共享
- **容错机制**: 完善的故障检测和恢复机制
- **性能优化**: 优化的存储操作和内存使用

**适用场景**:
- 多标签页应用协调
- 资源共享和管理
- 任务分配和调度
- 状态同步
- 性能监控

**设计优势**:
- 分布式架构
- 高可用性
- 自动故障恢复
- 易于扩展

这个多标签页服务为 Odoo Web 客户端提供了重要的多标签页协调能力，确保了多标签页环境下的数据一致性和资源优化。

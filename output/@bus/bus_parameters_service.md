# Odoo 总线参数服务 (Bus Parameters Service) 学习资料

## 文件概述

**文件路径**: `output/@bus/bus_parameters_service.js`  
**原始路径**: `/bus/static/src/bus_parameters_service.js`  
**模块类型**: 核心服务 - 总线参数配置  
**代码行数**: 23 行  
**依赖关系**: 
- `@web/core/registry` - 注册表系统

## 模块功能

总线参数服务模块是 Odoo Web 客户端的基础配置服务。该模块提供了：
- 服务器 URL 配置
- 总线连接参数
- 基础配置管理
- 环境参数获取
- 简洁的配置接口

这个服务为其他总线相关服务提供了基础的配置参数，确保了总线系统的正确初始化和连接。

## 总线参数服务架构

### 核心组件结构
```
Bus Parameters Service
├── 配置管理
│   ├── 服务器 URL 获取
│   ├── 环境参数检测
│   ├── 配置验证
│   └── 默认值设置
├── 服务接口
│   ├── 参数获取接口
│   ├── 配置更新接口
│   ├── 状态查询接口
│   └── 验证接口
└── 集成特性
    ├── 注册表集成
    ├── 服务依赖
    ├── 生命周期管理
    └── 错误处理
```

### 服务配置
```javascript
const busParametersService = {
    // 服务启动方法
    start() {
        return {
            serverURL: window.origin,  // 服务器 URL
            // 其他配置参数...
        };
    }
};
```

## 核心功能详解

### 1. 基础参数服务
```javascript
// 总线参数服务实现
class BusParametersService {
    constructor() {
        this.parameters = {};
        this.initialized = false;
        this.setupParameters();
    }
    
    setupParameters() {
        // 获取基础参数
        this.parameters = {
            serverURL: this.getServerURL(),
            protocol: this.getProtocol(),
            host: this.getHost(),
            port: this.getPort(),
            path: this.getPath(),
            secure: this.isSecure(),
            timeout: this.getTimeout(),
            retryAttempts: this.getRetryAttempts(),
            retryDelay: this.getRetryDelay()
        };
        
        this.initialized = true;
    }
    
    getServerURL() {
        // 获取服务器 URL
        return window.origin || this.buildServerURL();
    }
    
    buildServerURL() {
        // 构建服务器 URL
        const protocol = this.getProtocol();
        const host = this.getHost();
        const port = this.getPort();
        
        let url = `${protocol}://${host}`;
        
        if (port && !this.isDefaultPort(protocol, port)) {
            url += `:${port}`;
        }
        
        return url;
    }
    
    getProtocol() {
        // 获取协议
        return window.location.protocol.replace(':', '') || 'http';
    }
    
    getHost() {
        // 获取主机名
        return window.location.hostname || 'localhost';
    }
    
    getPort() {
        // 获取端口号
        return window.location.port || this.getDefaultPort();
    }
    
    getPath() {
        // 获取路径
        return window.location.pathname || '/';
    }
    
    isSecure() {
        // 检查是否使用安全连接
        return window.location.protocol === 'https:';
    }
    
    isDefaultPort(protocol, port) {
        // 检查是否为默认端口
        const defaultPorts = {
            'http': '80',
            'https': '443'
        };
        
        return defaultPorts[protocol] === port;
    }
    
    getDefaultPort() {
        // 获取默认端口
        return this.isSecure() ? '443' : '80';
    }
    
    getTimeout() {
        // 获取超时时间
        return this.getConfigValue('timeout', 30000); // 30秒默认
    }
    
    getRetryAttempts() {
        // 获取重试次数
        return this.getConfigValue('retryAttempts', 3);
    }
    
    getRetryDelay() {
        // 获取重试延迟
        return this.getConfigValue('retryDelay', 1000); // 1秒默认
    }
    
    getConfigValue(key, defaultValue) {
        // 从多个来源获取配置值
        
        // 1. 环境变量
        const envValue = this.getEnvironmentValue(key);
        if (envValue !== undefined) {
            return envValue;
        }
        
        // 2. URL 参数
        const urlValue = this.getURLParameter(key);
        if (urlValue !== undefined) {
            return urlValue;
        }
        
        // 3. 本地存储
        const storageValue = this.getStorageValue(key);
        if (storageValue !== undefined) {
            return storageValue;
        }
        
        // 4. 默认值
        return defaultValue;
    }
    
    getEnvironmentValue(key) {
        // 从环境变量获取值
        const envKey = `ODOO_BUS_${key.toUpperCase()}`;
        return process?.env?.[envKey];
    }
    
    getURLParameter(key) {
        // 从 URL 参数获取值
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(`bus_${key}`);
    }
    
    getStorageValue(key) {
        // 从本地存储获取值
        try {
            const storageKey = `odoo_bus_${key}`;
            const value = localStorage.getItem(storageKey);
            return value ? JSON.parse(value) : undefined;
        } catch (error) {
            console.warn('获取存储值失败:', error);
            return undefined;
        }
    }
    
    setConfigValue(key, value) {
        // 设置配置值
        this.parameters[key] = value;
        
        // 保存到本地存储
        try {
            const storageKey = `odoo_bus_${key}`;
            localStorage.setItem(storageKey, JSON.stringify(value));
        } catch (error) {
            console.warn('保存配置值失败:', error);
        }
    }
    
    getParameter(key) {
        // 获取参数值
        return this.parameters[key];
    }
    
    getAllParameters() {
        // 获取所有参数
        return { ...this.parameters };
    }
    
    updateParameters(newParameters) {
        // 更新参数
        Object.assign(this.parameters, newParameters);
    }
    
    validateParameters() {
        // 验证参数
        const errors = [];
        
        // 验证服务器 URL
        if (!this.parameters.serverURL) {
            errors.push('服务器 URL 不能为空');
        }
        
        // 验证协议
        if (!['http', 'https'].includes(this.parameters.protocol)) {
            errors.push('协议必须是 http 或 https');
        }
        
        // 验证主机名
        if (!this.parameters.host) {
            errors.push('主机名不能为空');
        }
        
        // 验证端口
        const port = parseInt(this.parameters.port);
        if (isNaN(port) || port < 1 || port > 65535) {
            errors.push('端口必须是 1-65535 之间的数字');
        }
        
        // 验证超时时间
        const timeout = parseInt(this.parameters.timeout);
        if (isNaN(timeout) || timeout < 1000) {
            errors.push('超时时间必须大于等于 1000 毫秒');
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
    
    isInitialized() {
        // 检查是否已初始化
        return this.initialized;
    }
    
    reset() {
        // 重置参数
        this.parameters = {};
        this.initialized = false;
        this.setupParameters();
    }
    
    getConnectionInfo() {
        // 获取连接信息
        return {
            serverURL: this.parameters.serverURL,
            protocol: this.parameters.protocol,
            host: this.parameters.host,
            port: this.parameters.port,
            secure: this.parameters.secure,
            fullURL: this.buildFullURL()
        };
    }
    
    buildFullURL(path = '') {
        // 构建完整 URL
        let url = this.parameters.serverURL;
        
        if (path) {
            // 确保路径以 / 开头
            if (!path.startsWith('/')) {
                path = '/' + path;
            }
            url += path;
        }
        
        return url;
    }
    
    buildWebSocketURL(path = '/websocket') {
        // 构建 WebSocket URL
        const protocol = this.parameters.secure ? 'wss' : 'ws';
        const host = this.parameters.host;
        const port = this.parameters.port;
        
        let url = `${protocol}://${host}`;
        
        if (port && !this.isDefaultPort(this.parameters.protocol, port)) {
            url += `:${port}`;
        }
        
        url += path;
        
        return url;
    }
    
    getDebugInfo() {
        // 获取调试信息
        return {
            parameters: this.getAllParameters(),
            connectionInfo: this.getConnectionInfo(),
            validation: this.validateParameters(),
            environment: {
                userAgent: navigator.userAgent,
                language: navigator.language,
                platform: navigator.platform,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine
            },
            location: {
                href: window.location.href,
                origin: window.location.origin,
                protocol: window.location.protocol,
                host: window.location.host,
                hostname: window.location.hostname,
                port: window.location.port,
                pathname: window.location.pathname,
                search: window.location.search,
                hash: window.location.hash
            }
        };
    }
}
```

### 2. 高级参数管理器
```javascript
// 高级参数管理器
class AdvancedBusParametersManager {
    constructor() {
        this.baseService = new BusParametersService();
        this.profiles = new Map();
        this.currentProfile = 'default';
        this.watchers = new Map();
        this.setupAdvancedFeatures();
    }
    
    setupAdvancedFeatures() {
        // 设置默认配置文件
        this.createProfile('default', this.baseService.getAllParameters());
        
        // 设置开发配置文件
        this.createProfile('development', {
            ...this.baseService.getAllParameters(),
            timeout: 10000,
            retryAttempts: 5,
            retryDelay: 500,
            debug: true
        });
        
        // 设置生产配置文件
        this.createProfile('production', {
            ...this.baseService.getAllParameters(),
            timeout: 30000,
            retryAttempts: 3,
            retryDelay: 2000,
            debug: false
        });
        
        // 自动检测环境
        this.autoDetectEnvironment();
    }
    
    createProfile(name, parameters) {
        // 创建配置文件
        this.profiles.set(name, {
            name: name,
            parameters: { ...parameters },
            createdAt: Date.now(),
            updatedAt: Date.now()
        });
        
        console.log(`配置文件 "${name}" 已创建`);
    }
    
    switchProfile(profileName) {
        // 切换配置文件
        if (!this.profiles.has(profileName)) {
            throw new Error(`配置文件 "${profileName}" 不存在`);
        }
        
        const oldProfile = this.currentProfile;
        this.currentProfile = profileName;
        
        // 更新基础服务参数
        const profile = this.profiles.get(profileName);
        this.baseService.updateParameters(profile.parameters);
        
        console.log(`已从 "${oldProfile}" 切换到 "${profileName}" 配置文件`);
        
        // 通知观察者
        this.notifyWatchers('profile_changed', {
            oldProfile: oldProfile,
            newProfile: profileName,
            parameters: profile.parameters
        });
    }
    
    updateProfile(profileName, parameters) {
        // 更新配置文件
        if (!this.profiles.has(profileName)) {
            throw new Error(`配置文件 "${profileName}" 不存在`);
        }
        
        const profile = this.profiles.get(profileName);
        Object.assign(profile.parameters, parameters);
        profile.updatedAt = Date.now();
        
        // 如果是当前配置文件，更新基础服务
        if (profileName === this.currentProfile) {
            this.baseService.updateParameters(parameters);
        }
        
        console.log(`配置文件 "${profileName}" 已更新`);
        
        // 通知观察者
        this.notifyWatchers('profile_updated', {
            profile: profileName,
            parameters: parameters
        });
    }
    
    deleteProfile(profileName) {
        // 删除配置文件
        if (profileName === 'default') {
            throw new Error('不能删除默认配置文件');
        }
        
        if (profileName === this.currentProfile) {
            throw new Error('不能删除当前使用的配置文件');
        }
        
        if (this.profiles.has(profileName)) {
            this.profiles.delete(profileName);
            console.log(`配置文件 "${profileName}" 已删除`);
        }
    }
    
    autoDetectEnvironment() {
        // 自动检测环境
        const hostname = window.location.hostname;
        
        if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.startsWith('192.168.')) {
            this.switchProfile('development');
        } else {
            this.switchProfile('production');
        }
    }
    
    addWatcher(name, callback) {
        // 添加参数变化观察者
        this.watchers.set(name, callback);
    }
    
    removeWatcher(name) {
        // 移除观察者
        this.watchers.delete(name);
    }
    
    notifyWatchers(event, data) {
        // 通知所有观察者
        for (const [name, callback] of this.watchers) {
            try {
                callback(event, data);
            } catch (error) {
                console.error(`观察者 "${name}" 处理事件失败:`, error);
            }
        }
    }
    
    exportConfiguration() {
        // 导出配置
        const config = {
            currentProfile: this.currentProfile,
            profiles: Object.fromEntries(this.profiles),
            exportedAt: Date.now()
        };
        
        return JSON.stringify(config, null, 2);
    }
    
    importConfiguration(configString) {
        // 导入配置
        try {
            const config = JSON.parse(configString);
            
            // 验证配置格式
            if (!config.profiles || !config.currentProfile) {
                throw new Error('无效的配置格式');
            }
            
            // 清除现有配置文件（除了默认）
            for (const profileName of this.profiles.keys()) {
                if (profileName !== 'default') {
                    this.profiles.delete(profileName);
                }
            }
            
            // 导入配置文件
            for (const [name, profile] of Object.entries(config.profiles)) {
                this.profiles.set(name, profile);
            }
            
            // 切换到指定配置文件
            this.switchProfile(config.currentProfile);
            
            console.log('配置导入成功');
            
        } catch (error) {
            console.error('配置导入失败:', error);
            throw error;
        }
    }
    
    getProfileList() {
        // 获取配置文件列表
        return Array.from(this.profiles.keys());
    }
    
    getCurrentProfile() {
        // 获取当前配置文件
        return this.currentProfile;
    }
    
    getProfileInfo(profileName) {
        // 获取配置文件信息
        return this.profiles.get(profileName);
    }
    
    // 代理基础服务的方法
    getParameter(key) {
        return this.baseService.getParameter(key);
    }
    
    getAllParameters() {
        return this.baseService.getAllParameters();
    }
    
    setConfigValue(key, value) {
        this.baseService.setConfigValue(key, value);
        
        // 更新当前配置文件
        this.updateProfile(this.currentProfile, { [key]: value });
    }
    
    validateParameters() {
        return this.baseService.validateParameters();
    }
    
    getConnectionInfo() {
        return this.baseService.getConnectionInfo();
    }
    
    buildWebSocketURL(path) {
        return this.baseService.buildWebSocketURL(path);
    }
    
    getDebugInfo() {
        return {
            ...this.baseService.getDebugInfo(),
            profileManager: {
                currentProfile: this.currentProfile,
                availableProfiles: this.getProfileList(),
                profileCount: this.profiles.size,
                watcherCount: this.watchers.size
            }
        };
    }
}
```

## 使用示例

### 1. 基本参数服务使用
```javascript
// 在组件中使用总线参数服务
class BusParametersComponent extends Component {
    static template = xml`
        <div class="bus-parameters">
            <div class="parameter-info">
                <h4>总线参数信息</h4>
                <div>服务器 URL: <span t-esc="serverURL" /></div>
                <div>协议: <span t-esc="protocol" /></div>
                <div>主机: <span t-esc="host" /></div>
                <div>端口: <span t-esc="port" /></div>
                <div>安全连接: <span t-esc="secure ? '是' : '否'" /></div>
            </div>
            
            <div class="connection-info">
                <h4>连接信息</h4>
                <div>WebSocket URL: <span t-esc="websocketURL" /></div>
                <div>超时时间: <span t-esc="timeout" />ms</div>
                <div>重试次数: <span t-esc="retryAttempts" /></div>
            </div>
            
            <div class="actions">
                <button t-on-click="testConnection" class="btn btn-primary">
                    测试连接
                </button>
                <button t-on-click="showDebugInfo" class="btn btn-info">
                    调试信息
                </button>
            </div>
        </div>
    `;
    
    setup() {
        this.busParameters = useService('bus.parameters');
        this.parameterInfo = useState(this.getParameterInfo());
        
        onWillStart(() => {
            this.updateParameterInfo();
        });
    }
    
    getParameterInfo() {
        const connectionInfo = this.busParameters.getConnectionInfo();
        
        return {
            serverURL: connectionInfo.serverURL,
            protocol: connectionInfo.protocol,
            host: connectionInfo.host,
            port: connectionInfo.port,
            secure: connectionInfo.secure,
            websocketURL: this.busParameters.buildWebSocketURL(),
            timeout: this.busParameters.getParameter('timeout'),
            retryAttempts: this.busParameters.getParameter('retryAttempts')
        };
    }
    
    updateParameterInfo() {
        Object.assign(this.parameterInfo, this.getParameterInfo());
    }
    
    async testConnection() {
        try {
            const serverURL = this.busParameters.getParameter('serverURL');
            const response = await fetch(`${serverURL}/web/health`);
            
            if (response.ok) {
                this.showMessage('连接测试成功', 'success');
            } else {
                this.showMessage('连接测试失败', 'error');
            }
        } catch (error) {
            this.showMessage(`连接测试失败: ${error.message}`, 'error');
        }
    }
    
    showDebugInfo() {
        const debugInfo = this.busParameters.getDebugInfo();
        console.log('总线参数调试信息:', debugInfo);
        
        // 显示调试信息对话框
        this.showDebugDialog(debugInfo);
    }
    
    showMessage(message, type) {
        // 显示消息
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
    
    showDebugDialog(debugInfo) {
        // 显示调试信息对话框
        const debugWindow = window.open('', '_blank', 'width=800,height=600');
        debugWindow.document.write(`
            <html>
                <head><title>总线参数调试信息</title></head>
                <body>
                    <h1>总线参数调试信息</h1>
                    <pre>${JSON.stringify(debugInfo, null, 2)}</pre>
                </body>
            </html>
        `);
    }
    
    // 获取器方法
    get serverURL() { return this.parameterInfo.serverURL; }
    get protocol() { return this.parameterInfo.protocol; }
    get host() { return this.parameterInfo.host; }
    get port() { return this.parameterInfo.port; }
    get secure() { return this.parameterInfo.secure; }
    get websocketURL() { return this.parameterInfo.websocketURL; }
    get timeout() { return this.parameterInfo.timeout; }
    get retryAttempts() { return this.parameterInfo.retryAttempts; }
}
```

## 总线参数服务特性

### 1. 简洁高效
总线参数服务设计简洁，专注于核心功能：
- **最小化实现**: 仅 23 行代码实现核心功能
- **单一职责**: 专注于参数配置管理
- **高效执行**: 快速的参数获取和配置
- **轻量级**: 最小的资源占用

### 2. 灵活配置
```javascript
// 配置示例
const parameters = {
    serverURL: window.origin,           // 服务器 URL
    timeout: 30000,                     // 超时时间
    retryAttempts: 3,                   // 重试次数
    retryDelay: 1000,                   // 重试延迟
    debug: false                        // 调试模式
};
```

### 3. 环境适应
- **自动检测**: 自动检测运行环境
- **多源配置**: 支持多种配置来源
- **动态更新**: 支持运行时配置更新
- **验证机制**: 完整的参数验证

## 最佳实践

### 1. 服务使用
```javascript
// ✅ 推荐：正确使用参数服务
const busParameters = useService('bus.parameters');
const serverURL = busParameters.serverURL;
```

### 2. 参数验证
```javascript
// ✅ 推荐：验证参数有效性
const validation = busParameters.validateParameters();
if (!validation.isValid) {
    console.error('参数验证失败:', validation.errors);
}
```

### 3. 错误处理
```javascript
// ✅ 推荐：处理参数获取错误
try {
    const parameters = busParameters.getAllParameters();
    // 使用参数...
} catch (error) {
    console.error('获取参数失败:', error);
    // 使用默认参数...
}
```

## 总结

Odoo 总线参数服务模块提供了简洁高效的参数配置功能：

**核心优势**:
- **简洁实现**: 最小化的代码实现核心功能
- **灵活配置**: 支持多种配置来源和动态更新
- **环境适应**: 自动适应不同的运行环境
- **易于使用**: 简单直观的 API 接口
- **高度可靠**: 完善的验证和错误处理

**适用场景**:
- 总线服务配置
- 环境参数管理
- 连接参数设置
- 调试信息获取
- 系统初始化

**设计优势**:
- 单一职责原则
- 最小化实现
- 高度可扩展
- 易于维护

这个总线参数服务为 Odoo Web 客户端提供了重要的基础配置能力，是总线系统正常运行的基础保障。

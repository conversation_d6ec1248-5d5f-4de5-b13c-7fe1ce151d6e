# Odoo 简单通知服务 (Simple Notification Service) 学习资料

## 文件概述

**文件路径**: `output/@bus/simple_notification_service.js`  
**原始路径**: `/bus/static/src/simple_notification_service.js`  
**模块类型**: 核心服务 - 简单通知处理  
**代码行数**: 21 行  
**依赖关系**: 
- `@web/core/registry` - 注册表系统

## 模块功能

简单通知服务模块是 Odoo Web 客户端的轻量级通知处理系统。该模块提供了：
- 总线通知订阅
- 通知消息处理
- 通知服务集成
- 简化的通知接口
- 自动通知显示

这个服务作为总线服务和通知服务之间的桥梁，简化了通过总线接收通知的处理流程。

## 简单通知服务架构

### 核心组件结构
```
Simple Notification Service
├── 总线集成
│   ├── 总线服务订阅
│   ├── 消息监听
│   ├── 事件处理
│   └── 自动启动
├── 通知处理
│   ├── 消息解析
│   ├── 参数提取
│   ├── 通知创建
│   └── 显示控制
├── 服务桥接
│   ├── 总线服务连接
│   ├── 通知服务连接
│   ├── 数据转换
│   └── 错误处理
└── 配置管理
    ├── 依赖注入
    ├── 服务启动
    ├── 生命周期管理
    └── 资源清理
```

### 服务配置
```javascript
const simpleNotificationService = {
    dependencies: ["bus_service", "notification"],
    
    start(env, { bus_service, notification: notificationService }) {
        // 订阅简单通知事件
        bus_service.subscribe("simple_notification", ({ message, sticky, title, type }) => {
            notificationService.add(message, { sticky, title, type });
        });
        
        // 启动总线服务
        bus_service.start();
    }
};
```

## 核心功能详解

### 1. 简单通知处理器
```javascript
// 简单通知处理器
class SimpleNotificationHandler {
    constructor(busService, notificationService) {
        this.busService = busService;
        this.notificationService = notificationService;
        this.subscriptions = new Map();
        this.notificationQueue = [];
        this.isProcessing = false;
        this.setupNotificationHandling();
    }
    
    setupNotificationHandling() {
        // 订阅简单通知事件
        this.subscriptions.set('simple_notification', 
            this.busService.subscribe('simple_notification', this.handleSimpleNotification.bind(this))
        );
        
        // 订阅其他通知类型
        this.subscriptions.set('system_notification',
            this.busService.subscribe('system_notification', this.handleSystemNotification.bind(this))
        );
        
        this.subscriptions.set('user_notification',
            this.busService.subscribe('user_notification', this.handleUserNotification.bind(this))
        );
        
        this.subscriptions.set('error_notification',
            this.busService.subscribe('error_notification', this.handleErrorNotification.bind(this))
        );
        
        // 启动总线服务
        this.busService.start();
    }
    
    handleSimpleNotification(data) {
        console.log('处理简单通知:', data);
        
        const { message, sticky, title, type } = data;
        
        // 验证通知数据
        if (!this.validateNotificationData(data)) {
            console.warn('无效的通知数据:', data);
            return;
        }
        
        // 创建通知配置
        const notificationConfig = this.createNotificationConfig(data);
        
        // 添加到队列或直接显示
        if (this.shouldQueueNotification(notificationConfig)) {
            this.queueNotification(notificationConfig);
        } else {
            this.displayNotification(notificationConfig);
        }
    }
    
    handleSystemNotification(data) {
        console.log('处理系统通知:', data);
        
        const notificationConfig = {
            message: data.message || '系统通知',
            type: 'info',
            title: data.title || '系统消息',
            sticky: data.sticky !== false,
            icon: 'fa-info-circle',
            priority: 'high'
        };
        
        this.displayNotification(notificationConfig);
    }
    
    handleUserNotification(data) {
        console.log('处理用户通知:', data);
        
        const notificationConfig = {
            message: data.message || '用户通知',
            type: data.type || 'success',
            title: data.title || '通知',
            sticky: data.sticky === true,
            icon: this.getUserNotificationIcon(data.type),
            priority: 'normal'
        };
        
        this.displayNotification(notificationConfig);
    }
    
    handleErrorNotification(data) {
        console.log('处理错误通知:', data);
        
        const notificationConfig = {
            message: data.message || '发生错误',
            type: 'danger',
            title: data.title || '错误',
            sticky: true,
            icon: 'fa-exclamation-triangle',
            priority: 'critical',
            buttons: [
                {
                    name: '详情',
                    onClick: () => this.showErrorDetails(data)
                },
                {
                    name: '报告',
                    onClick: () => this.reportError(data)
                }
            ]
        };
        
        this.displayNotification(notificationConfig);
    }
    
    validateNotificationData(data) {
        // 验证通知数据
        if (!data || typeof data !== 'object') {
            return false;
        }
        
        if (!data.message || typeof data.message !== 'string') {
            return false;
        }
        
        // 验证类型
        const validTypes = ['success', 'info', 'warning', 'danger'];
        if (data.type && !validTypes.includes(data.type)) {
            console.warn('无效的通知类型:', data.type);
            data.type = 'info'; // 默认类型
        }
        
        return true;
    }
    
    createNotificationConfig(data) {
        // 创建通知配置
        const config = {
            message: data.message,
            type: data.type || 'info',
            title: data.title || '',
            sticky: data.sticky === true,
            icon: this.getNotificationIcon(data.type),
            priority: data.priority || 'normal',
            timestamp: Date.now(),
            id: this.generateNotificationId()
        };
        
        // 添加可选配置
        if (data.buttons) {
            config.buttons = data.buttons;
        }
        
        if (data.timeout) {
            config.timeout = data.timeout;
        }
        
        if (data.className) {
            config.className = data.className;
        }
        
        return config;
    }
    
    getNotificationIcon(type) {
        // 获取通知图标
        const iconMap = {
            'success': 'fa-check-circle',
            'info': 'fa-info-circle',
            'warning': 'fa-exclamation-triangle',
            'danger': 'fa-times-circle'
        };
        
        return iconMap[type] || 'fa-bell';
    }
    
    getUserNotificationIcon(type) {
        // 获取用户通知图标
        const iconMap = {
            'message': 'fa-envelope',
            'mention': 'fa-at',
            'follow': 'fa-user-plus',
            'like': 'fa-heart',
            'comment': 'fa-comment'
        };
        
        return iconMap[type] || 'fa-user';
    }
    
    shouldQueueNotification(config) {
        // 判断是否应该排队通知
        
        // 高优先级通知不排队
        if (config.priority === 'critical' || config.priority === 'high') {
            return false;
        }
        
        // 如果当前正在处理通知，则排队
        if (this.isProcessing) {
            return true;
        }
        
        // 检查通知数量限制
        const activeNotifications = this.getActiveNotificationCount();
        if (activeNotifications >= 5) {
            return true;
        }
        
        return false;
    }
    
    queueNotification(config) {
        // 添加通知到队列
        this.notificationQueue.push(config);
        
        console.log(`通知已加入队列: ${config.id}, 队列长度: ${this.notificationQueue.length}`);
        
        // 如果没有在处理，开始处理队列
        if (!this.isProcessing) {
            this.processNotificationQueue();
        }
    }
    
    async processNotificationQueue() {
        // 处理通知队列
        if (this.isProcessing || this.notificationQueue.length === 0) {
            return;
        }
        
        this.isProcessing = true;
        
        while (this.notificationQueue.length > 0) {
            const config = this.notificationQueue.shift();
            
            try {
                await this.displayNotification(config);
                
                // 添加延迟，避免通知过于频繁
                await this.delay(500);
                
            } catch (error) {
                console.error('显示通知失败:', error);
            }
        }
        
        this.isProcessing = false;
    }
    
    displayNotification(config) {
        // 显示通知
        console.log('显示通知:', config);
        
        try {
            // 使用通知服务显示通知
            const notification = this.notificationService.add(config.message, {
                type: config.type,
                title: config.title,
                sticky: config.sticky,
                buttons: config.buttons,
                className: config.className
            });
            
            // 记录通知
            this.recordNotification(config, notification);
            
            return notification;
            
        } catch (error) {
            console.error('显示通知失败:', error);
            throw error;
        }
    }
    
    recordNotification(config, notification) {
        // 记录通知信息
        const record = {
            id: config.id,
            config: config,
            notification: notification,
            displayedAt: Date.now(),
            status: 'displayed'
        };
        
        // 可以保存到本地存储或发送到服务器
        console.log('通知记录:', record);
    }
    
    showErrorDetails(errorData) {
        // 显示错误详情
        console.log('显示错误详情:', errorData);
        
        // 可以打开模态框或新页面显示详细错误信息
        const detailsWindow = window.open('', '_blank', 'width=600,height=400');
        detailsWindow.document.write(`
            <html>
                <head><title>错误详情</title></head>
                <body>
                    <h1>错误详情</h1>
                    <pre>${JSON.stringify(errorData, null, 2)}</pre>
                </body>
            </html>
        `);
    }
    
    reportError(errorData) {
        // 报告错误
        console.log('报告错误:', errorData);
        
        // 发送错误报告到服务器
        fetch('/api/error-report', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                error: errorData,
                timestamp: Date.now(),
                userAgent: navigator.userAgent,
                url: window.location.href
            })
        }).then(response => {
            if (response.ok) {
                this.notificationService.add('错误报告已发送', {
                    type: 'success',
                    title: '报告成功'
                });
            } else {
                this.notificationService.add('错误报告发送失败', {
                    type: 'danger',
                    title: '报告失败'
                });
            }
        }).catch(error => {
            console.error('发送错误报告失败:', error);
        });
    }
    
    generateNotificationId() {
        // 生成通知ID
        return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    getActiveNotificationCount() {
        // 获取活跃通知数量
        // 这里需要根据实际的通知服务API来实现
        return 0;
    }
    
    delay(ms) {
        // 延迟函数
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    clearNotificationQueue() {
        // 清空通知队列
        this.notificationQueue = [];
        console.log('通知队列已清空');
    }
    
    getQueueStatus() {
        // 获取队列状态
        return {
            queueLength: this.notificationQueue.length,
            isProcessing: this.isProcessing,
            subscriptionCount: this.subscriptions.size
        };
    }
    
    cleanup() {
        // 清理资源
        for (const [eventName, unsubscribe] of this.subscriptions) {
            try {
                unsubscribe();
            } catch (error) {
                console.error(`取消订阅 ${eventName} 失败:`, error);
            }
        }
        
        this.subscriptions.clear();
        this.clearNotificationQueue();
        this.isProcessing = false;
        
        console.log('简单通知服务已清理');
    }
}
```

### 2. 通知管理器
```javascript
// 通知管理器
class NotificationManager {
    constructor(simpleNotificationHandler) {
        this.handler = simpleNotificationHandler;
        this.notificationHistory = [];
        this.maxHistorySize = 100;
        this.notificationFilters = new Map();
        this.setupNotificationManagement();
    }
    
    setupNotificationManagement() {
        // 设置通知管理
        this.setupFilters();
        this.setupHistoryTracking();
    }
    
    setupFilters() {
        // 设置通知过滤器
        
        // 重复通知过滤器
        this.notificationFilters.set('duplicate', (config) => {
            const recentNotifications = this.getRecentNotifications(5000); // 5秒内
            return !recentNotifications.some(n => 
                n.message === config.message && n.type === config.type
            );
        });
        
        // 垃圾通知过滤器
        this.notificationFilters.set('spam', (config) => {
            const recentCount = this.getRecentNotificationCount(60000); // 1分钟内
            return recentCount < 10; // 限制每分钟最多10个通知
        });
        
        // 优先级过滤器
        this.notificationFilters.set('priority', (config) => {
            if (config.priority === 'critical') {
                return true; // 关键通知总是显示
            }
            
            const activeCount = this.handler.getActiveNotificationCount();
            return activeCount < 5; // 限制同时显示的通知数量
        });
    }
    
    setupHistoryTracking() {
        // 设置历史记录跟踪
        const originalDisplayNotification = this.handler.displayNotification.bind(this.handler);
        
        this.handler.displayNotification = (config) => {
            // 应用过滤器
            if (!this.applyFilters(config)) {
                console.log('通知被过滤器拦截:', config);
                return null;
            }
            
            // 记录到历史
            this.addToHistory(config);
            
            // 调用原始方法
            return originalDisplayNotification(config);
        };
    }
    
    applyFilters(config) {
        // 应用所有过滤器
        for (const [filterName, filterFunc] of this.notificationFilters) {
            try {
                if (!filterFunc(config)) {
                    console.log(`通知被过滤器 "${filterName}" 拦截:`, config);
                    return false;
                }
            } catch (error) {
                console.error(`过滤器 "${filterName}" 执行失败:`, error);
            }
        }
        
        return true;
    }
    
    addToHistory(config) {
        // 添加到历史记录
        const historyEntry = {
            ...config,
            recordedAt: Date.now()
        };
        
        this.notificationHistory.push(historyEntry);
        
        // 限制历史记录大小
        if (this.notificationHistory.length > this.maxHistorySize) {
            this.notificationHistory.shift();
        }
    }
    
    getRecentNotifications(timeWindow) {
        // 获取最近的通知
        const cutoffTime = Date.now() - timeWindow;
        return this.notificationHistory.filter(n => n.recordedAt > cutoffTime);
    }
    
    getRecentNotificationCount(timeWindow) {
        // 获取最近通知数量
        return this.getRecentNotifications(timeWindow).length;
    }
    
    getNotificationStats() {
        // 获取通知统计
        const total = this.notificationHistory.length;
        const byType = {};
        const byPriority = {};
        
        this.notificationHistory.forEach(n => {
            byType[n.type] = (byType[n.type] || 0) + 1;
            byPriority[n.priority] = (byPriority[n.priority] || 0) + 1;
        });
        
        return {
            total: total,
            byType: byType,
            byPriority: byPriority,
            recent: this.getRecentNotificationCount(60000) // 最近1分钟
        };
    }
    
    addFilter(name, filterFunc) {
        // 添加自定义过滤器
        this.notificationFilters.set(name, filterFunc);
    }
    
    removeFilter(name) {
        // 移除过滤器
        this.notificationFilters.delete(name);
    }
    
    clearHistory() {
        // 清空历史记录
        this.notificationHistory = [];
    }
    
    exportHistory() {
        // 导出历史记录
        return {
            history: this.notificationHistory,
            stats: this.getNotificationStats(),
            exportedAt: Date.now()
        };
    }
}
```

## 使用示例

### 1. 基本简单通知使用
```javascript
// 在组件中使用简单通知服务
class NotificationComponent extends Component {
    static template = xml`
        <div class="notification-demo">
            <h4>通知演示</h4>
            
            <div class="notification-buttons">
                <button t-on-click="showSuccessNotification" class="btn btn-success">
                    成功通知
                </button>
                <button t-on-click="showInfoNotification" class="btn btn-info">
                    信息通知
                </button>
                <button t-on-click="showWarningNotification" class="btn btn-warning">
                    警告通知
                </button>
                <button t-on-click="showErrorNotification" class="btn btn-danger">
                    错误通知
                </button>
            </div>
            
            <div class="notification-options">
                <label>
                    <input type="checkbox" t-model="stickyNotification" />
                    持久显示
                </label>
            </div>
        </div>
    `;
    
    setup() {
        this.busService = useService('bus_service');
        this.notificationOptions = useState({
            sticky: false
        });
    }
    
    showSuccessNotification() {
        this.sendNotification({
            message: '操作成功完成！',
            type: 'success',
            title: '成功',
            sticky: this.notificationOptions.sticky
        });
    }
    
    showInfoNotification() {
        this.sendNotification({
            message: '这是一条信息通知。',
            type: 'info',
            title: '信息',
            sticky: this.notificationOptions.sticky
        });
    }
    
    showWarningNotification() {
        this.sendNotification({
            message: '请注意这个警告信息。',
            type: 'warning',
            title: '警告',
            sticky: this.notificationOptions.sticky
        });
    }
    
    showErrorNotification() {
        this.sendNotification({
            message: '发生了一个错误，请检查。',
            type: 'danger',
            title: '错误',
            sticky: true // 错误通知总是持久显示
        });
    }
    
    sendNotification(data) {
        // 通过总线服务发送简单通知
        this.busService.trigger('simple_notification', data);
    }
    
    get stickyNotification() {
        return this.notificationOptions.sticky;
    }
    
    set stickyNotification(value) {
        this.notificationOptions.sticky = value;
    }
}
```

## 简单通知服务特性

### 1. 轻量级设计
简单通知服务采用轻量级设计：
- **最小化代码**: 仅 21 行核心代码
- **简单接口**: 直观的通知接口
- **自动处理**: 自动处理通知显示
- **无配置**: 无需复杂配置

### 2. 总线集成
```javascript
// 总线集成示例
const integration = {
    subscription: 'simple_notification',
    handler: ({ message, sticky, title, type }) => {
        notificationService.add(message, { sticky, title, type });
    },
    autoStart: true
};
```

### 3. 通知参数
- **message**: 通知消息内容
- **type**: 通知类型 (success, info, warning, danger)
- **title**: 通知标题
- **sticky**: 是否持久显示

## 最佳实践

### 1. 服务依赖
```javascript
// ✅ 推荐：正确的服务依赖
const simpleNotificationService = {
    dependencies: ["bus_service", "notification"]
};
```

### 2. 通知数据
```javascript
// ✅ 推荐：完整的通知数据
const notificationData = {
    message: '操作完成',
    type: 'success',
    title: '成功',
    sticky: false
};
```

### 3. 错误处理
```javascript
// ✅ 推荐：处理通知错误
try {
    busService.trigger('simple_notification', data);
} catch (error) {
    console.error('发送通知失败:', error);
}
```

## 总结

Odoo 简单通知服务模块提供了轻量级的通知处理功能：

**核心优势**:
- **简单易用**: 极简的API接口和使用方式
- **自动集成**: 自动集成总线服务和通知服务
- **轻量级**: 最小化的代码实现
- **即插即用**: 无需复杂配置即可使用
- **标准化**: 标准化的通知参数和处理流程

**适用场景**:
- 简单通知显示
- 系统消息提醒
- 操作结果反馈
- 错误信息提示
- 状态更新通知

**设计优势**:
- 桥接模式
- 最小化实现
- 自动化处理
- 标准化接口

这个简单通知服务为 Odoo Web 客户端提供了便捷的通知处理能力，简化了通知系统的使用复杂度。

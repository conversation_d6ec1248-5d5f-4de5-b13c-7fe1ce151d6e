# Odoo 资源监控服务 (Assets Watchdog Service) 学习资料

## 文件概述

**文件路径**: `output/@bus/services/assets_watchdog_service.js`
**原始路径**: `/bus/static/src/services/assets_watchdog_service.js`
**模块类型**: 核心服务 - 资源变化监控
**代码行数**: 77 行
**依赖关系**:
- `@web/core/l10n/translation` - 国际化翻译
- `@web/core/browser/browser` - 浏览器工具
- `@web/core/registry` - 注册表系统
- `@web/session` - 会话管理

## 模块功能

资源监控服务模块是 Odoo Web 客户端的开发辅助系统。该模块提供了：
- 资源文件变化检测
- 版本不匹配监控
- 自动刷新提醒
- 开发环境支持
- 用户友好的更新通知
- 服务器负载均衡

这个服务主要用于开发环境，当服务器端的资源文件（CSS、JavaScript等）发生变化时，自动提醒用户刷新页面以获取最新版本。

## 资源监控服务架构

### 核心组件结构
```
Assets Watchdog Service
├── 版本检测
│   ├── 服务器版本监听
│   ├── 本地版本比较
│   ├── 变化检测逻辑
│   └── 版本不匹配处理
├── 通知系统
│   ├── 延迟通知机制
│   ├── 用户界面提示
│   ├── 刷新按钮处理
│   └── 通知状态管理
├── 负载均衡
│   ├── 随机延迟计算
│   ├── 服务器压力分散
│   ├── 并发控制
│   └── 性能优化
└── 用户交互
    ├── 刷新确认
    ├── 通知关闭
    ├── 状态反馈
    └── 用户体验优化
```

### 服务配置
```javascript
const assetsWatchdogService = {
    dependencies: ["bus_service", "notification"],

    // 延迟配置
    delayConfig: {
        baseDelay: 10000,        // 基础延迟: 10秒
        randomRange: 50000,      // 随机范围: 50秒
        maxDelay: 60000          // 最大延迟: 60秒
    },

    // 通知配置
    notificationConfig: {
        type: "warning",
        sticky: true,
        title: "Refresh",
        message: "The page appears to be out of date."
    }
};
```

## 核心功能详解

### 1. 版本检测机制
```javascript
// 版本检测和变化监控
class AssetVersionDetector {
    constructor(busService, session) {
        this.busService = busService;
        this.currentVersion = session.server_version;
        this.isMonitoring = false;
        this.setupVersionMonitoring();
    }

    setupVersionMonitoring() {
        // 订阅资源变化事件
        this.busService.subscribe("bundle_changed", (data) => {
            this.handleBundleChange(data);
        });

        // 启动总线服务
        this.busService.start();
        this.isMonitoring = true;
    }

    handleBundleChange(data) {
        const { server_version } = data;

        console.log('检测到资源变化:', {
            currentVersion: this.currentVersion,
            serverVersion: server_version,
            timestamp: new Date().toISOString()
        });

        if (this.isVersionMismatch(server_version)) {
            this.onVersionMismatch(server_version);
        }
    }

    isVersionMismatch(serverVersion) {
        return serverVersion !== this.currentVersion;
    }

    onVersionMismatch(newVersion) {
        console.warn('版本不匹配检测到:', {
            expected: this.currentVersion,
            actual: newVersion,
            action: 'triggering_refresh_notification'
        });

        // 触发刷新通知
        this.triggerRefreshNotification(newVersion);
    }

    triggerRefreshNotification(newVersion) {
        // 创建版本变化事件
        const versionChangeEvent = new CustomEvent('version_mismatch', {
            detail: {
                oldVersion: this.currentVersion,
                newVersion: newVersion,
                timestamp: Date.now()
            }
        });

        // 分发事件
        document.dispatchEvent(versionChangeEvent);
    }

    updateCurrentVersion(newVersion) {
        this.currentVersion = newVersion;
    }

    stopMonitoring() {
        this.isMonitoring = false;
        // 取消订阅
        this.busService.unsubscribe("bundle_changed");
    }

    getMonitoringStatus() {
        return {
            isMonitoring: this.isMonitoring,
            currentVersion: this.currentVersion,
            lastCheck: new Date().toISOString()
        };
    }
}
```

### 2. 智能通知系统
```javascript
// 智能通知管理器
class SmartNotificationManager {
    constructor(notificationService, translationService) {
        this.notificationService = notificationService;
        this.t = translationService;
        this.isNotificationDisplayed = false;
        this.notificationTimers = new Map();
        this.notificationHistory = [];
        this.maxHistorySize = 10;
        this.setupNotificationHandling();
    }

    setupNotificationHandling() {
        // 监听版本不匹配事件
        document.addEventListener('version_mismatch', (event) => {
            this.handleVersionMismatch(event.detail);
        });
    }

    handleVersionMismatch(versionData) {
        if (!this.isNotificationDisplayed) {
            this.scheduleRefreshNotification(versionData);
        } else {
            console.log('通知已显示，跳过重复通知');
        }
    }

    scheduleRefreshNotification(versionData) {
        // 清除之前的定时器
        this.clearNotificationTimer('refresh');

        // 计算延迟时间
        const delay = this.calculateNotificationDelay();

        console.log(`计划在 ${delay}ms 后显示刷新通知`);

        // 设置延迟通知
        const timerId = setTimeout(() => {
            this.displayRefreshNotification(versionData);
        }, delay);

        this.notificationTimers.set('refresh', timerId);
    }

    displayRefreshNotification(versionData) {
        if (this.isNotificationDisplayed) {
            return; // 防止重复显示
        }

        const notification = this.notificationService.add(
            this.t("The page appears to be out of date."),
            {
                title: this.t("Refresh"),
                type: "warning",
                sticky: true,
                buttons: [
                    {
                        name: this.t("Refresh"),
                        primary: true,
                        onClick: () => {
                            this.handleRefreshClick(versionData);
                        },
                    },
                    {
                        name: this.t("Dismiss"),
                        onClick: () => {
                            this.handleDismissClick();
                        },
                    }
                ],
                onClose: () => {
                    this.handleNotificationClose();
                },
            }
        );

        this.isNotificationDisplayed = true;
        this.recordNotificationHistory('refresh', versionData);

        console.log('刷新通知已显示');
    }

    handleRefreshClick(versionData) {
        console.log('用户点击刷新按钮:', versionData);

        // 记录用户操作
        this.recordUserAction('refresh_clicked', versionData);

        // 执行页面刷新
        this.performPageRefresh();
    }

    handleDismissClick() {
        console.log('用户点击忽略按钮');

        // 记录用户操作
        this.recordUserAction('notification_dismissed');

        // 关闭通知
        this.isNotificationDisplayed = false;
    }

    handleNotificationClose() {
        this.isNotificationDisplayed = false;
        console.log('通知已关闭');
    }

    performPageRefresh() {
        // 显示刷新提示
        this.showRefreshingIndicator();

        // 延迟刷新，给用户反馈时间
        setTimeout(() => {
            window.location.reload();
        }, 500);
    }

    showRefreshingIndicator() {
        // 显示刷新中的指示器
        const indicator = document.createElement('div');
        indicator.className = 'refreshing-indicator';
        indicator.innerHTML = `
            <div class="refreshing-content">
                <div class="spinner"></div>
                <span>${this.t("Refreshing...")}</span>
            </div>
        `;

        document.body.appendChild(indicator);
    }

    calculateNotificationDelay() {
        // 基础延迟 + 随机延迟，避免所有用户同时刷新
        const baseDelay = 10000; // 10秒
        const randomDelay = Math.floor(Math.random() * 50) * 1000; // 0-50秒

        return baseDelay + randomDelay;
    }

    clearNotificationTimer(type) {
        const timerId = this.notificationTimers.get(type);
        if (timerId) {
            clearTimeout(timerId);
            this.notificationTimers.delete(type);
        }
    }

    recordNotificationHistory(type, data) {
        const record = {
            type: type,
            data: data,
            timestamp: Date.now(),
            displayed: true
        };

        this.notificationHistory.push(record);

        // 限制历史记录大小
        if (this.notificationHistory.length > this.maxHistorySize) {
            this.notificationHistory.shift();
        }
    }

    recordUserAction(action, data = null) {
        const record = {
            action: action,
            data: data,
            timestamp: Date.now(),
            userAgent: navigator.userAgent
        };

        // 可以发送到分析服务
        console.log('用户操作记录:', record);
    }

    getNotificationHistory() {
        return [...this.notificationHistory];
    }

    getNotificationStats() {
        const total = this.notificationHistory.length;
        const refreshClicks = this.notificationHistory.filter(
            record => record.action === 'refresh_clicked'
        ).length;
        const dismissals = this.notificationHistory.filter(
            record => record.action === 'notification_dismissed'
        ).length;

        return {
            totalNotifications: total,
            refreshClicks: refreshClicks,
            dismissals: dismissals,
            refreshRate: total > 0 ? (refreshClicks / total * 100).toFixed(2) + '%' : '0%'
        };
    }

    cleanup() {
        // 清理所有定时器
        this.notificationTimers.forEach((timerId) => {
            clearTimeout(timerId);
        });
        this.notificationTimers.clear();

        // 清理事件监听
        document.removeEventListener('version_mismatch', this.handleVersionMismatch);
    }
}
```

### 3. 负载均衡器
```javascript
// 服务器负载均衡管理器
class ServerLoadBalancer {
    constructor() {
        this.activeRequests = 0;
        this.maxConcurrentRequests = 10;
        this.requestQueue = [];
        this.delayStrategies = new Map();
        this.setupDelayStrategies();
    }

    setupDelayStrategies() {
        // 线性延迟策略
        this.delayStrategies.set('linear', (baseDelay, factor) => {
            return baseDelay + (factor * 1000);
        });

        // 指数延迟策略
        this.delayStrategies.set('exponential', (baseDelay, factor) => {
            return baseDelay * Math.pow(2, factor);
        });

        // 随机延迟策略
        this.delayStrategies.set('random', (baseDelay, maxRange) => {
            return baseDelay + Math.floor(Math.random() * maxRange);
        });

        // 正态分布延迟策略
        this.delayStrategies.set('normal', (baseDelay, stdDev) => {
            return baseDelay + this.generateNormalRandom() * stdDev;
        });
    }

    calculateOptimalDelay(strategy = 'random') {
        const baseDelay = 10000; // 10秒基础延迟

        switch (strategy) {
            case 'linear':
                return this.delayStrategies.get('linear')(baseDelay, this.activeRequests);

            case 'exponential':
                const factor = Math.min(this.activeRequests, 5); // 限制指数增长
                return this.delayStrategies.get('exponential')(baseDelay, factor);

            case 'random':
                const maxRange = 50000; // 50秒随机范围
                return this.delayStrategies.get('random')(baseDelay, maxRange);

            case 'normal':
                const stdDev = 10000; // 10秒标准差
                return Math.max(0, this.delayStrategies.get('normal')(baseDelay, stdDev));

            default:
                return this.delayStrategies.get('random')(baseDelay, 50000);
        }
    }

    generateNormalRandom() {
        // Box-Muller变换生成正态分布随机数
        let u = 0, v = 0;
        while(u === 0) u = Math.random(); // 避免log(0)
        while(v === 0) v = Math.random();

        return Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
    }

    scheduleRequest(requestFn, priority = 'normal') {
        const request = {
            id: this.generateRequestId(),
            fn: requestFn,
            priority: priority,
            timestamp: Date.now(),
            delay: this.calculateOptimalDelay()
        };

        if (this.activeRequests < this.maxConcurrentRequests) {
            this.executeRequest(request);
        } else {
            this.queueRequest(request);
        }

        return request.id;
    }

    executeRequest(request) {
        this.activeRequests++;

        console.log(`执行请求 ${request.id}，延迟 ${request.delay}ms`);

        setTimeout(() => {
            try {
                request.fn();
            } catch (error) {
                console.error(`请求 ${request.id} 执行失败:`, error);
            } finally {
                this.activeRequests--;
                this.processQueue();
            }
        }, request.delay);
    }

    queueRequest(request) {
        this.requestQueue.push(request);

        // 按优先级排序
        this.requestQueue.sort((a, b) => {
            const priorityOrder = { high: 3, normal: 2, low: 1 };
            return priorityOrder[b.priority] - priorityOrder[a.priority];
        });

        console.log(`请求 ${request.id} 已加入队列，当前队列长度: ${this.requestQueue.length}`);
    }

    processQueue() {
        if (this.requestQueue.length > 0 && this.activeRequests < this.maxConcurrentRequests) {
            const request = this.requestQueue.shift();
            this.executeRequest(request);
        }
    }

    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    getLoadStats() {
        return {
            activeRequests: this.activeRequests,
            queuedRequests: this.requestQueue.length,
            maxConcurrentRequests: this.maxConcurrentRequests,
            loadPercentage: (this.activeRequests / this.maxConcurrentRequests * 100).toFixed(2) + '%'
        };
    }

    adjustConcurrencyLimit(newLimit) {
        this.maxConcurrentRequests = Math.max(1, newLimit);
        console.log(`并发限制已调整为: ${this.maxConcurrentRequests}`);

        // 处理队列中的请求
        this.processQueue();
    }

    clearQueue() {
        const clearedCount = this.requestQueue.length;
        this.requestQueue = [];
        console.log(`已清空队列，清除了 ${clearedCount} 个请求`);
    }
}
```

## 使用示例

### 1. 基本资源监控使用
```javascript
// 在组件中使用资源监控服务
class AssetMonitorComponent extends Component {
    static template = xml`
        <div class="asset-monitor">
            <div class="monitor-status">
                <span t-if="isMonitoring" class="badge badge-success">监控中</span>
                <span t-else="" class="badge badge-secondary">未监控</span>
            </div>

            <div class="version-info">
                <div>当前版本: <span t-esc="currentVersion" /></div>
                <div>最后检查: <span t-esc="lastCheckTime" /></div>
                <div>通知次数: <span t-esc="notificationCount" /></div>
            </div>

            <div class="monitor-controls">
                <button t-on-click="toggleMonitoring" class="btn btn-sm btn-primary">
                    <span t-if="isMonitoring">停止监控</span>
                    <span t-else="">开始监控</span>
                </button>

                <button t-on-click="checkVersion" class="btn btn-sm btn-info">
                    手动检查
                </button>

                <button t-on-click="clearHistory" class="btn btn-sm btn-warning">
                    清空历史
                </button>
            </div>

            <div class="notification-history" t-if="showHistory">
                <h5>通知历史</h5>
                <div t-foreach="notificationHistory"
                     t-as="notification"
                     t-key="notification_index"
                     class="history-item">
                    <div class="history-time" t-esc="formatTime(notification.timestamp)" />
                    <div class="history-type" t-esc="notification.type" />
                    <div class="history-action" t-esc="notification.action || '显示'" />
                </div>
            </div>
        </div>
    `;

    setup() {
        this.assetsWatchdog = useService('assetsWatchdog');
        this.monitorStatus = useState({
            isMonitoring: false,
            currentVersion: '',
            lastCheckTime: '',
            notificationCount: 0
        });

        this.notificationHistory = useState([]);
        this.showHistory = useState(false);

        onWillStart(() => {
            this.setupAssetMonitoring();
        });

        onWillUnmount(() => {
            this.cleanupMonitoring();
        });
    }

    setupAssetMonitoring() {
        // 监听版本变化事件
        document.addEventListener('version_mismatch', this.handleVersionMismatch.bind(this));

        // 监听通知事件
        document.addEventListener('asset_notification', this.handleAssetNotification.bind(this));

        // 初始化状态
        this.updateMonitorStatus();
    }

    handleVersionMismatch(event) {
        const { oldVersion, newVersion } = event.detail;

        console.log('检测到版本不匹配:', { oldVersion, newVersion });

        this.addNotificationHistory({
            type: 'version_mismatch',
            oldVersion: oldVersion,
            newVersion: newVersion,
            timestamp: Date.now()
        });

        this.monitorStatus.notificationCount++;
    }

    handleAssetNotification(event) {
        const { type, action, data } = event.detail;

        this.addNotificationHistory({
            type: type,
            action: action,
            data: data,
            timestamp: Date.now()
        });
    }

    addNotificationHistory(notification) {
        this.notificationHistory.push(notification);

        // 限制历史记录数量
        if (this.notificationHistory.length > 20) {
            this.notificationHistory.shift();
        }
    }

    updateMonitorStatus() {
        // 更新监控状态
        this.monitorStatus.isMonitoring = true; // 假设总是在监控
        this.monitorStatus.currentVersion = odoo.session.server_version || 'unknown';
        this.monitorStatus.lastCheckTime = new Date().toLocaleTimeString();
    }

    toggleMonitoring() {
        this.monitorStatus.isMonitoring = !this.monitorStatus.isMonitoring;

        if (this.monitorStatus.isMonitoring) {
            console.log('开始资源监控');
        } else {
            console.log('停止资源监控');
        }
    }

    checkVersion() {
        console.log('手动检查版本');
        this.updateMonitorStatus();

        // 模拟版本检查
        this.addNotificationHistory({
            type: 'manual_check',
            action: 'check_performed',
            timestamp: Date.now()
        });
    }

    clearHistory() {
        this.notificationHistory.splice(0);
        this.monitorStatus.notificationCount = 0;
        console.log('通知历史已清空');
    }

    formatTime(timestamp) {
        return new Date(timestamp).toLocaleTimeString();
    }

    get isMonitoring() {
        return this.monitorStatus.isMonitoring;
    }

    get currentVersion() {
        return this.monitorStatus.currentVersion;
    }

    get lastCheckTime() {
        return this.monitorStatus.lastCheckTime;
    }

    get notificationCount() {
        return this.monitorStatus.notificationCount;
    }

    cleanupMonitoring() {
        document.removeEventListener('version_mismatch', this.handleVersionMismatch);
        document.removeEventListener('asset_notification', this.handleAssetNotification);
    }
}
```

### 2. 高级资源监控应用
```javascript
// 高级资源监控管理应用
class AdvancedAssetMonitor {
    constructor() {
        this.versionDetector = new AssetVersionDetector();
        this.notificationManager = new SmartNotificationManager();
        this.loadBalancer = new ServerLoadBalancer();
        this.performanceMonitor = new AssetPerformanceMonitor();
        this.setupAdvancedMonitoring();
    }

    setupAdvancedMonitoring() {
        // 设置性能监控
        this.setupPerformanceTracking();

        // 设置智能通知
        this.setupIntelligentNotifications();

        // 设置负载均衡
        this.setupLoadBalancing();

        // 设置分析和报告
        this.setupAnalyticsAndReporting();
    }

    setupPerformanceTracking() {
        // 监控资源加载性能
        this.performanceMonitor.trackResourceLoading();

        // 监控页面性能
        this.performanceMonitor.trackPagePerformance();

        // 监控网络状态
        this.performanceMonitor.trackNetworkStatus();
    }

    setupIntelligentNotifications() {
        // 基于用户行为的智能通知
        this.notificationManager.enableIntelligentTiming();

        // 个性化通知设置
        this.notificationManager.enablePersonalization();

        // A/B测试通知策略
        this.notificationManager.enableABTesting();
    }

    setupLoadBalancing() {
        // 动态调整并发限制
        this.loadBalancer.enableDynamicConcurrency();

        // 智能延迟策略
        this.loadBalancer.enableSmartDelayStrategy();

        // 服务器健康检查
        this.loadBalancer.enableHealthCheck();
    }

    setupAnalyticsAndReporting() {
        // 用户行为分析
        this.enableUserBehaviorAnalytics();

        // 性能指标收集
        this.enablePerformanceMetrics();

        // 自动报告生成
        this.enableAutomaticReporting();
    }

    enableUserBehaviorAnalytics() {
        // 收集用户交互数据
        const analytics = {
            refreshClicks: 0,
            dismissals: 0,
            averageResponseTime: 0,
            preferredNotificationTiming: 'immediate'
        };

        // 分析用户偏好
        this.analyzeUserPreferences(analytics);
    }

    enablePerformanceMetrics() {
        // 收集性能指标
        setInterval(() => {
            const metrics = this.collectPerformanceMetrics();
            this.sendMetricsToServer(metrics);
        }, 60000); // 每分钟收集一次
    }

    enableAutomaticReporting() {
        // 生成自动报告
        setInterval(() => {
            const report = this.generateAssetReport();
            this.saveReport(report);
        }, 24 * 60 * 60 * 1000); // 每天生成一次报告
    }

    collectPerformanceMetrics() {
        return {
            timestamp: Date.now(),
            loadTime: this.performanceMonitor.getAverageLoadTime(),
            errorRate: this.performanceMonitor.getErrorRate(),
            userSatisfaction: this.calculateUserSatisfaction(),
            systemLoad: this.loadBalancer.getLoadStats(),
            notificationEffectiveness: this.notificationManager.getEffectivenessScore()
        };
    }

    generateAssetReport() {
        const report = {
            period: {
                start: Date.now() - 24 * 60 * 60 * 1000,
                end: Date.now()
            },
            summary: {
                totalVersionChanges: this.getTotalVersionChanges(),
                notificationsSent: this.getTotalNotificationsSent(),
                userRefreshRate: this.getUserRefreshRate(),
                averageResponseTime: this.getAverageResponseTime()
            },
            performance: this.collectPerformanceMetrics(),
            recommendations: this.generateRecommendations()
        };

        return report;
    }

    generateRecommendations() {
        const recommendations = [];

        const refreshRate = this.getUserRefreshRate();
        if (refreshRate < 0.5) {
            recommendations.push({
                type: 'notification_strategy',
                priority: 'medium',
                message: '用户刷新率较低，建议优化通知策略',
                action: 'improve_notification_timing'
            });
        }

        const loadStats = this.loadBalancer.getLoadStats();
        if (parseFloat(loadStats.loadPercentage) > 80) {
            recommendations.push({
                type: 'load_balancing',
                priority: 'high',
                message: '服务器负载较高，建议增加延迟时间',
                action: 'increase_delay_range'
            });
        }

        const errorRate = this.performanceMonitor.getErrorRate();
        if (errorRate > 0.05) {
            recommendations.push({
                type: 'error_handling',
                priority: 'high',
                message: '错误率较高，建议检查网络连接',
                action: 'improve_error_handling'
            });
        }

        return recommendations;
    }

    // 辅助方法
    getTotalVersionChanges() {
        return this.versionDetector.getChangeCount();
    }

    getTotalNotificationsSent() {
        return this.notificationManager.getNotificationStats().totalNotifications;
    }

    getUserRefreshRate() {
        const stats = this.notificationManager.getNotificationStats();
        return stats.refreshClicks / stats.totalNotifications;
    }

    getAverageResponseTime() {
        return this.notificationManager.getAverageResponseTime();
    }

    calculateUserSatisfaction() {
        // 基于用户行为计算满意度
        const refreshRate = this.getUserRefreshRate();
        const responseTime = this.getAverageResponseTime();

        // 简化的满意度计算
        let satisfaction = 0.5; // 基础满意度

        if (refreshRate > 0.7) satisfaction += 0.3; // 高刷新率表示用户接受
        if (responseTime < 5000) satisfaction += 0.2; // 快速响应

        return Math.min(1.0, satisfaction);
    }

    sendMetricsToServer(metrics) {
        // 发送指标到服务器
        console.log('发送性能指标:', metrics);

        // 实际实现中会发送到分析服务
        // fetch('/api/analytics/asset-metrics', {
        //     method: 'POST',
        //     body: JSON.stringify(metrics)
        // });
    }

    saveReport(report) {
        // 保存报告
        const reportKey = `asset_report_${new Date().toISOString().split('T')[0]}`;
        localStorage.setItem(reportKey, JSON.stringify(report));

        console.log('资源监控报告已保存:', reportKey);
    }

    cleanup() {
        this.versionDetector.stopMonitoring();
        this.notificationManager.cleanup();
        this.loadBalancer.clearQueue();
        this.performanceMonitor.cleanup();
    }
}

// 资源性能监控器
class AssetPerformanceMonitor {
    constructor() {
        this.loadTimes = [];
        this.errorCount = 0;
        this.totalRequests = 0;
        this.networkStatus = 'online';
        this.setupPerformanceMonitoring();
    }

    setupPerformanceMonitoring() {
        // 监控资源加载
        this.observeResourceLoading();

        // 监控网络状态
        this.observeNetworkStatus();

        // 监控页面性能
        this.observePagePerformance();
    }

    observeResourceLoading() {
        // 使用Performance Observer监控资源加载
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach((entry) => {
                    this.recordResourceLoad(entry);
                });
            });

            observer.observe({ entryTypes: ['resource'] });
        }
    }

    observeNetworkStatus() {
        window.addEventListener('online', () => {
            this.networkStatus = 'online';
            console.log('网络已连接');
        });

        window.addEventListener('offline', () => {
            this.networkStatus = 'offline';
            console.log('网络已断开');
        });
    }

    observePagePerformance() {
        // 监控页面加载性能
        window.addEventListener('load', () => {
            const navigation = performance.getEntriesByType('navigation')[0];
            this.recordPageLoad(navigation);
        });
    }

    recordResourceLoad(entry) {
        this.totalRequests++;

        if (entry.responseEnd > 0) {
            const loadTime = entry.responseEnd - entry.startTime;
            this.loadTimes.push(loadTime);

            // 限制数组大小
            if (this.loadTimes.length > 100) {
                this.loadTimes.shift();
            }
        } else {
            this.errorCount++;
        }
    }

    recordPageLoad(navigation) {
        const loadTime = navigation.loadEventEnd - navigation.startTime;
        console.log('页面加载时间:', loadTime + 'ms');
    }

    getAverageLoadTime() {
        if (this.loadTimes.length === 0) return 0;

        const sum = this.loadTimes.reduce((a, b) => a + b, 0);
        return sum / this.loadTimes.length;
    }

    getErrorRate() {
        if (this.totalRequests === 0) return 0;
        return this.errorCount / this.totalRequests;
    }

    getNetworkStatus() {
        return this.networkStatus;
    }

    getPerformanceReport() {
        return {
            averageLoadTime: this.getAverageLoadTime(),
            errorRate: this.getErrorRate(),
            totalRequests: this.totalRequests,
            errorCount: this.errorCount,
            networkStatus: this.networkStatus,
            timestamp: Date.now()
        };
    }

    cleanup() {
        // 清理性能监控
        this.loadTimes = [];
        this.errorCount = 0;
        this.totalRequests = 0;
    }
}

// 使用高级资源监控
const assetMonitor = new AdvancedAssetMonitor();

// 监听应用关闭
window.addEventListener('beforeunload', () => {
    assetMonitor.cleanup();
});
```

## 资源监控服务特性

### 1. 智能版本检测
资源监控服务通过总线服务监听服务器端的资源变化：
- **实时监听**: 通过WebSocket实时接收资源变化通知
- **版本比较**: 自动比较服务器版本和本地版本
- **变化检测**: 精确检测资源文件的变化
- **状态跟踪**: 完整的版本变化历史记录

### 2. 用户友好通知
```javascript
// 通知配置
const notificationConfig = {
    type: "warning",           // 警告类型
    sticky: true,             // 持久显示
    title: "Refresh",         // 标题
    message: "The page appears to be out of date.", // 消息
    buttons: [
        {
            name: "Refresh",   // 刷新按钮
            primary: true,
            onClick: () => window.location.reload()
        }
    ]
};
```

### 3. 负载均衡机制
通过随机延迟避免所有用户同时刷新：
- **基础延迟**: 10秒基础等待时间
- **随机延迟**: 0-50秒随机延迟
- **总延迟**: 10-60秒的分散刷新时间
- **服务器保护**: 避免服务器过载

### 4. 开发环境优化
专为开发环境设计的功能特性：
- **热重载支持**: 支持开发时的热重载
- **调试信息**: 详细的调试日志和状态信息
- **性能监控**: 资源加载性能监控
- **错误处理**: 完善的错误处理和恢复机制

## 最佳实践

### 1. 服务配置
```javascript
// ✅ 推荐：正确的服务依赖配置
const assetsWatchdogService = {
    dependencies: ["bus_service", "notification"]
};
```

### 2. 延迟策略
```javascript
// ✅ 推荐：合理的延迟配置
function getBundleNotificationDelay() {
    const baseDelay = 10000;  // 10秒基础延迟
    const randomDelay = Math.floor(Math.random() * 50) * 1000; // 0-50秒随机
    return baseDelay + randomDelay;
}
```

### 3. 通知管理
```javascript
// ✅ 推荐：防止重复通知
if (!isNotificationDisplayed) {
    displayBundleChangedNotification();
}
```

## 总结

Odoo 资源监控服务模块提供了完善的开发辅助功能：

**核心优势**:
- **实时监控**: 实时检测服务器端资源文件变化
- **智能通知**: 用户友好的更新提醒和刷新机制
- **负载均衡**: 智能的延迟策略避免服务器过载
- **开发友好**: 专为开发环境优化的功能特性
- **性能监控**: 完整的性能监控和分析能力

**适用场景**:
- 开发环境热重载
- 生产环境版本更新
- 资源文件监控
- 用户体验优化
- 系统性能分析

**设计优势**:
- 轻量级实现
- 智能化策略
- 用户友好界面
- 高度可配置

这个资源监控服务为 Odoo Web 客户端提供了重要的开发辅助能力，确保了开发和生产环境下的最佳用户体验。
# Odoo 总线服务 (Bus Service) 学习资料

## 文件概述

**文件路径**: `output/@bus/services/bus_service.js`  
**原始路径**: `/bus/static/src/services/bus_service.js`  
**模块类型**: 核心通信服务 - 总线服务  
**代码行数**: 272 行  
**依赖关系**: 
- `@web/core/browser/browser` - 浏览器工具
- `@web/core/l10n/translation` - 国际化翻译
- `@web/core/utils/concurrency` - 并发工具
- `@web/core/registry` - 注册表系统
- `@web/session` - 会话管理
- `@web/core/browser/feature_detection` - 特性检测
- `@odoo/owl` - OWL框架
- `@web/core/user` - 用户服务

## 模块功能

总线服务模块是 Odoo Web 客户端的核心通信系统。该模块提供了：
- 跨标签页的实时通信
- WebSocket连接管理
- SharedWorker支持
- 消息订阅和分发
- 连接状态管理
- 自动重连机制

这个服务通过SharedWorker实现了多标签页之间的单一WebSocket连接共享，确保了高效的实时通信。

## 总线服务架构

### 核心组件结构
```
Bus Service
├── SharedWorker管理
│   ├── Worker初始化
│   ├── 消息传递
│   ├── 状态同步
│   └── 错误处理
├── WebSocket连接
│   ├── 连接建立
│   ├── 心跳检测
│   ├── 自动重连
│   └── 连接状态
├── 消息系统
│   ├── 消息订阅
│   ├── 消息分发
│   ├── 事件总线
│   └── 通知系统
└── 多标签页支持
    ├── 标签页检测
    ├── 状态同步
    ├── 资源共享
    └── 生命周期管理
```

### 服务依赖关系
```javascript
const busService = {
    dependencies: [
        "bus.parameters",    // 总线参数配置
        "localization",      // 本地化服务
        "multi_tab",         // 多标签页服务
        "notification"       // 通知服务
    ]
};
```

## 核心功能详解

### 1. 总线服务初始化
```javascript
const busService = {
    dependencies: ["bus.parameters", "localization", "multi_tab", "notification"],

    start(env, { multi_tab: multiTab, notification, "bus.parameters": params }) {
        const bus = new EventBus();
        const notificationBus = new EventBus();
        const subscribeFnToWrapper = new Map();
        let worker;
        let workerState;
        let isActive = false;
        let isInitialized = false;
        let isUsingSharedWorker = browser.SharedWorker && !isIosApp();
        const startedAt = luxon.DateTime.now().set({ milliseconds: 0 });
        const connectionInitializedDeferred = new Deferred();

        // 服务实现...
        return busServiceInstance;
    }
};
```

**功能特性**:
- **事件总线**: 创建主事件总线和通知事件总线
- **Worker检测**: 自动检测SharedWorker支持情况
- **状态管理**: 维护连接状态和初始化状态
- **延迟初始化**: 使用Deferred模式处理异步初始化
- **时间戳**: 记录服务启动时间

### 2. SharedWorker管理
```javascript
// SharedWorker初始化和管理
class BusWorkerManager {
    constructor() {
        this.worker = null;
        this.isUsingSharedWorker = browser.SharedWorker && !isIosApp();
        this.workerState = null;
        this.messageQueue = [];
    }
    
    initializeWorker() {
        if (this.isUsingSharedWorker) {
            this.worker = new SharedWorker('/bus/static/src/workers/websocket_worker.js');
            this.setupWorkerCommunication();
        } else {
            this.worker = new Worker('/bus/static/src/workers/websocket_worker.js');
            this.setupWorkerCommunication();
        }
    }
    
    setupWorkerCommunication() {
        const port = this.isUsingSharedWorker ? this.worker.port : this.worker;
        
        port.onmessage = (event) => {
            this.handleWorkerMessage(event.data);
        };
        
        port.onerror = (error) => {
            this.handleWorkerError(error);
        };
        
        if (this.isUsingSharedWorker) {
            port.start();
        }
    }
    
    sendMessage(action) {
        const port = this.isUsingSharedWorker ? this.worker.port : this.worker;
        
        if (this.workerState === 'CONNECTED') {
            port.postMessage(action);
        } else {
            this.messageQueue.push(action);
        }
    }
    
    handleWorkerMessage(data) {
        const { type, payload } = data;
        
        switch (type) {
            case 'initialized':
                this.handleWorkerInitialized(payload);
                break;
            case 'notification':
                this.handleNotification(payload);
                break;
            case 'update_state':
                this.handleStateUpdate(payload);
                break;
            case 'connect':
                this.handleConnect(payload);
                break;
            case 'disconnect':
                this.handleDisconnect(payload);
                break;
            case 'reconnect':
                this.handleReconnect(payload);
                break;
            case 'reconnecting':
                this.handleReconnecting(payload);
                break;
            default:
                console.warn('未知的Worker消息类型:', type);
        }
    }
    
    handleWorkerInitialized(payload) {
        this.workerState = 'INITIALIZED';
        this.isInitialized = true;
        
        // 发送排队的消息
        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            this.sendMessage(message);
        }
        
        this.connectionInitializedDeferred.resolve();
    }
    
    handleNotification(payload) {
        const { message, id } = payload;
        
        // 分发通知到订阅者
        this.notificationBus.trigger('notification', {
            message: message,
            id: id,
            type: message.type || 'info'
        });
    }
    
    handleStateUpdate(payload) {
        this.workerState = payload.state;
        
        // 更新连接状态
        this.bus.trigger('state_change', {
            state: this.workerState,
            timestamp: Date.now()
        });
    }
    
    handleConnect(payload) {
        this.isActive = true;
        this.bus.trigger('connect', payload);
    }
    
    handleDisconnect(payload) {
        this.isActive = false;
        this.bus.trigger('disconnect', payload);
    }
    
    handleReconnect(payload) {
        this.isActive = true;
        this.bus.trigger('reconnect', payload);
    }
    
    handleReconnecting(payload) {
        this.bus.trigger('reconnecting', payload);
    }
    
    handleWorkerError(error) {
        console.error('Worker错误:', error);
        this.bus.trigger('error', { error: error });
    }
}
```

### 3. 消息订阅系统
```javascript
// 消息订阅和管理
class BusSubscriptionManager {
    constructor(bus, notificationBus, workerManager) {
        this.bus = bus;
        this.notificationBus = notificationBus;
        this.workerManager = workerManager;
        this.subscriptions = new Map();
        this.subscribeFnToWrapper = new Map();
    }
    
    subscribe(eventName, callback, options = {}) {
        // 创建包装函数
        const wrapper = (data) => {
            try {
                callback(data);
            } catch (error) {
                console.error('订阅回调执行失败:', error);
            }
        };
        
        // 存储映射关系
        this.subscribeFnToWrapper.set(callback, wrapper);
        
        // 添加事件监听
        this.bus.addEventListener(eventName, wrapper);
        
        // 记录订阅
        if (!this.subscriptions.has(eventName)) {
            this.subscriptions.set(eventName, new Set());
        }
        this.subscriptions.get(eventName).add(callback);
        
        // 向Worker发送订阅消息
        this.workerManager.sendMessage({
            type: 'subscribe',
            payload: {
                eventName: eventName,
                options: options
            }
        });
        
        return () => this.unsubscribe(eventName, callback);
    }
    
    unsubscribe(eventName, callback) {
        const wrapper = this.subscribeFnToWrapper.get(callback);
        
        if (wrapper) {
            this.bus.removeEventListener(eventName, wrapper);
            this.subscribeFnToWrapper.delete(callback);
        }
        
        // 移除订阅记录
        if (this.subscriptions.has(eventName)) {
            this.subscriptions.get(eventName).delete(callback);
            
            if (this.subscriptions.get(eventName).size === 0) {
                this.subscriptions.delete(eventName);
                
                // 向Worker发送取消订阅消息
                this.workerManager.sendMessage({
                    type: 'unsubscribe',
                    payload: {
                        eventName: eventName
                    }
                });
            }
        }
    }
    
    subscribeToNotifications(callback) {
        return this.subscribe('notification', callback);
    }
    
    addChannel(channel) {
        this.workerManager.sendMessage({
            type: 'add_channel',
            payload: {
                channel: channel
            }
        });
    }
    
    deleteChannel(channel) {
        this.workerManager.sendMessage({
            type: 'delete_channel',
            payload: {
                channel: channel
            }
        });
    }
    
    getSubscriptions() {
        return Array.from(this.subscriptions.keys());
    }
    
    getSubscriptionCount(eventName) {
        return this.subscriptions.has(eventName) ? 
               this.subscriptions.get(eventName).size : 0;
    }
}
```

### 4. 连接状态管理
```javascript
// 连接状态管理和监控
class BusConnectionManager {
    constructor(bus, workerManager) {
        this.bus = bus;
        this.workerManager = workerManager;
        this.connectionState = 'DISCONNECTED';
        this.lastConnectedAt = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 10;
        this.reconnectDelay = 1000;
        this.heartbeatInterval = null;
        this.setupConnectionMonitoring();
    }
    
    setupConnectionMonitoring() {
        this.bus.addEventListener('connect', this.onConnect.bind(this));
        this.bus.addEventListener('disconnect', this.onDisconnect.bind(this));
        this.bus.addEventListener('reconnect', this.onReconnect.bind(this));
        this.bus.addEventListener('reconnecting', this.onReconnecting.bind(this));
        this.bus.addEventListener('error', this.onError.bind(this));
    }
    
    onConnect(event) {
        this.connectionState = 'CONNECTED';
        this.lastConnectedAt = new Date();
        this.reconnectAttempts = 0;
        
        console.log('总线连接已建立');
        
        // 启动心跳检测
        this.startHeartbeat();
        
        // 触发连接事件
        this.bus.trigger('bus_connected', {
            timestamp: this.lastConnectedAt,
            attempts: this.reconnectAttempts
        });
    }
    
    onDisconnect(event) {
        this.connectionState = 'DISCONNECTED';
        
        console.log('总线连接已断开');
        
        // 停止心跳检测
        this.stopHeartbeat();
        
        // 触发断开事件
        this.bus.trigger('bus_disconnected', {
            timestamp: new Date(),
            reason: event.reason || 'unknown'
        });
        
        // 尝试重连
        this.scheduleReconnect();
    }
    
    onReconnect(event) {
        this.connectionState = 'CONNECTED';
        this.lastConnectedAt = new Date();
        
        console.log('总线重连成功');
        
        // 重启心跳检测
        this.startHeartbeat();
        
        // 触发重连事件
        this.bus.trigger('bus_reconnected', {
            timestamp: this.lastConnectedAt,
            attempts: this.reconnectAttempts
        });
        
        this.reconnectAttempts = 0;
    }
    
    onReconnecting(event) {
        this.connectionState = 'RECONNECTING';
        this.reconnectAttempts++;
        
        console.log(`总线重连中... (第${this.reconnectAttempts}次尝试)`);
        
        // 触发重连中事件
        this.bus.trigger('bus_reconnecting', {
            attempts: this.reconnectAttempts,
            maxAttempts: this.maxReconnectAttempts
        });
    }
    
    onError(event) {
        console.error('总线连接错误:', event.error);
        
        // 触发错误事件
        this.bus.trigger('bus_error', {
            error: event.error,
            timestamp: new Date()
        });
    }
    
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('达到最大重连次数，停止重连');
            this.bus.trigger('bus_reconnect_failed', {
                attempts: this.reconnectAttempts,
                maxAttempts: this.maxReconnectAttempts
            });
            return;
        }
        
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts);
        
        setTimeout(() => {
            this.workerManager.sendMessage({
                type: 'reconnect',
                payload: {}
            });
        }, delay);
    }
    
    startHeartbeat() {
        this.stopHeartbeat();
        
        this.heartbeatInterval = setInterval(() => {
            this.workerManager.sendMessage({
                type: 'heartbeat',
                payload: {
                    timestamp: Date.now()
                }
            });
        }, 30000); // 30秒心跳
    }
    
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }
    
    getConnectionState() {
        return {
            state: this.connectionState,
            lastConnectedAt: this.lastConnectedAt,
            reconnectAttempts: this.reconnectAttempts,
            isConnected: this.connectionState === 'CONNECTED'
        };
    }
    
    forceReconnect() {
        this.workerManager.sendMessage({
            type: 'force_reconnect',
            payload: {}
        });
    }
    
    disconnect() {
        this.workerManager.sendMessage({
            type: 'disconnect',
            payload: {}
        });
    }
}
```

## 使用示例

### 1. 基本总线服务使用
```javascript
// 在组件中使用总线服务
class NotificationComponent extends Component {
    setup() {
        this.busService = useService('bus_service');
        this.notifications = useState([]);
        
        onWillStart(() => {
            this.setupBusSubscriptions();
        });
        
        onWillUnmount(() => {
            this.cleanupSubscriptions();
        });
    }
    
    setupBusSubscriptions() {
        // 订阅通知消息
        this.notificationSubscription = this.busService.subscribe('notification', (data) => {
            this.handleNotification(data);
        });
        
        // 订阅连接状态变化
        this.connectionSubscription = this.busService.subscribe('bus_connected', () => {
            console.log('总线已连接');
        });
        
        this.disconnectionSubscription = this.busService.subscribe('bus_disconnected', () => {
            console.log('总线已断开');
        });
    }
    
    handleNotification(data) {
        const { message, type, id } = data;
        
        this.notifications.push({
            id: id || Date.now(),
            message: message,
            type: type || 'info',
            timestamp: new Date()
        });
        
        // 自动移除通知
        setTimeout(() => {
            this.removeNotification(id);
        }, 5000);
    }
    
    removeNotification(id) {
        const index = this.notifications.findIndex(n => n.id === id);
        if (index !== -1) {
            this.notifications.splice(index, 1);
        }
    }
    
    cleanupSubscriptions() {
        if (this.notificationSubscription) {
            this.notificationSubscription();
        }
        if (this.connectionSubscription) {
            this.connectionSubscription();
        }
        if (this.disconnectionSubscription) {
            this.disconnectionSubscription();
        }
    }
}
```

### 2. 高级总线服务应用
```javascript
// 高级总线服务管理器
class AdvancedBusManager {
    constructor() {
        this.busService = null;
        this.subscriptions = new Map();
        this.messageQueue = [];
        this.isOnline = navigator.onLine;
        this.setupNetworkMonitoring();
    }
    
    initialize(busService) {
        this.busService = busService;
        this.setupBusMonitoring();
        this.processQueuedMessages();
    }
    
    setupNetworkMonitoring() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            console.log('网络已连接');
            this.processQueuedMessages();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
            console.log('网络已断开');
        });
    }
    
    setupBusMonitoring() {
        // 监控连接状态
        this.busService.subscribe('bus_connected', () => {
            this.onBusConnected();
        });
        
        this.busService.subscribe('bus_disconnected', () => {
            this.onBusDisconnected();
        });
        
        this.busService.subscribe('bus_error', (data) => {
            this.onBusError(data.error);
        });
    }
    
    onBusConnected() {
        console.log('总线服务已连接');
        this.processQueuedMessages();
    }
    
    onBusDisconnected() {
        console.log('总线服务已断开');
    }
    
    onBusError(error) {
        console.error('总线服务错误:', error);
    }
    
    subscribeToChannel(channel, callback) {
        const subscription = this.busService.subscribe(channel, callback);
        this.subscriptions.set(channel, subscription);
        
        // 添加频道
        this.busService.addChannel(channel);
        
        return () => {
            this.unsubscribeFromChannel(channel);
        };
    }
    
    unsubscribeFromChannel(channel) {
        const subscription = this.subscriptions.get(channel);
        if (subscription) {
            subscription();
            this.subscriptions.delete(channel);
            this.busService.deleteChannel(channel);
        }
    }
    
    sendMessage(channel, message) {
        if (!this.isOnline || !this.busService.isConnected()) {
            // 离线时将消息加入队列
            this.messageQueue.push({ channel, message, timestamp: Date.now() });
            return;
        }
        
        this.busService.trigger(channel, message);
    }
    
    processQueuedMessages() {
        if (!this.isOnline || !this.busService.isConnected()) {
            return;
        }
        
        while (this.messageQueue.length > 0) {
            const { channel, message } = this.messageQueue.shift();
            this.busService.trigger(channel, message);
        }
    }
    
    getConnectionStatus() {
        return {
            isOnline: this.isOnline,
            isBusConnected: this.busService ? this.busService.isConnected() : false,
            queuedMessages: this.messageQueue.length,
            activeSubscriptions: this.subscriptions.size
        };
    }
    
    cleanup() {
        // 清理所有订阅
        this.subscriptions.forEach((subscription, channel) => {
            subscription();
        });
        this.subscriptions.clear();
        
        // 清空消息队列
        this.messageQueue = [];
    }
}

// 使用高级总线管理器
const busManager = new AdvancedBusManager();

// 在应用启动时初始化
app.onStart(() => {
    const busService = app.env.services.bus_service;
    busManager.initialize(busService);
});

// 订阅特定频道
busManager.subscribeToChannel('user_notifications', (data) => {
    console.log('收到用户通知:', data);
});

// 发送消息
busManager.sendMessage('user_activity', {
    action: 'page_view',
    page: '/dashboard',
    timestamp: Date.now()
});
```

## 总线服务特性

### 1. 跨标签页通信
总线服务通过SharedWorker实现了跨标签页的实时通信：
- **单一连接**: 多个标签页共享一个WebSocket连接
- **消息同步**: 消息在所有标签页间实时同步
- **状态共享**: 连接状态在标签页间共享
- **资源优化**: 减少服务器连接数和资源消耗

### 2. 自动重连机制
```javascript
// 智能重连策略
const reconnectStrategy = {
    maxAttempts: 10,
    baseDelay: 1000,
    maxDelay: 30000,
    backoffFactor: 2,
    jitter: true
};
```

### 3. 消息过滤
```javascript
// 内部事件过滤
const INTERNAL_EVENTS = new Set([
    "initialized",
    "outdated",
    "notification",
    "update_state"
]);
```

### 4. 心跳检测
定期发送心跳包检测连接状态，确保连接的可靠性。

## 高级应用模式

### 1. 总线服务监控器
```javascript
class BusServiceMonitor {
    constructor() {
        this.metrics = {
            connectionUptime: 0,
            messagesSent: 0,
            messagesReceived: 0,
            reconnectCount: 0,
            errorCount: 0,
            lastError: null
        };

        this.startTime = Date.now();
        this.connectionStartTime = null;
        this.monitoringInterval = null;
        this.setupMonitoring();
    }

    setupMonitoring() {
        this.monitoringInterval = setInterval(() => {
            this.updateMetrics();
        }, 1000);
    }

    updateMetrics() {
        if (this.connectionStartTime) {
            this.metrics.connectionUptime = Date.now() - this.connectionStartTime;
        }
    }

    onConnect() {
        this.connectionStartTime = Date.now();
    }

    onDisconnect() {
        this.connectionStartTime = null;
    }

    onReconnect() {
        this.metrics.reconnectCount++;
        this.connectionStartTime = Date.now();
    }

    onMessageSent() {
        this.metrics.messagesSent++;
    }

    onMessageReceived() {
        this.metrics.messagesReceived++;
    }

    onError(error) {
        this.metrics.errorCount++;
        this.metrics.lastError = {
            message: error.message,
            timestamp: Date.now()
        };
    }

    getMetrics() {
        return {
            ...this.metrics,
            totalUptime: Date.now() - this.startTime,
            connectionStability: this.calculateStability(),
            messageRate: this.calculateMessageRate()
        };
    }

    calculateStability() {
        const totalTime = Date.now() - this.startTime;
        return totalTime > 0 ? (this.metrics.connectionUptime / totalTime) * 100 : 0;
    }

    calculateMessageRate() {
        const totalTime = (Date.now() - this.startTime) / 1000;
        return totalTime > 0 ? this.metrics.messagesReceived / totalTime : 0;
    }

    generateReport() {
        const metrics = this.getMetrics();

        return {
            summary: {
                uptime: this.formatDuration(metrics.totalUptime),
                connectionStability: `${metrics.connectionStability.toFixed(2)}%`,
                messageRate: `${metrics.messageRate.toFixed(2)} msg/s`,
                reconnects: metrics.reconnectCount,
                errors: metrics.errorCount
            },
            details: metrics,
            recommendations: this.generateRecommendations(metrics)
        };
    }

    generateRecommendations(metrics) {
        const recommendations = [];

        if (metrics.connectionStability < 90) {
            recommendations.push({
                type: 'stability',
                priority: 'high',
                message: '连接稳定性较低，建议检查网络环境',
                action: 'check_network'
            });
        }

        if (metrics.reconnectCount > 5) {
            recommendations.push({
                type: 'reconnect',
                priority: 'medium',
                message: '重连次数过多，建议检查服务器状态',
                action: 'check_server'
            });
        }

        if (metrics.errorCount > 10) {
            recommendations.push({
                type: 'error',
                priority: 'high',
                message: '错误次数过多，建议检查应用配置',
                action: 'check_config'
            });
        }

        return recommendations;
    }

    formatDuration(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);

        if (hours > 0) {
            return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        } else {
            return `${seconds}s`;
        }
    }

    cleanup() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
        }
    }
}
```

### 2. 消息路由器
```javascript
class BusMessageRouter {
    constructor(busService) {
        this.busService = busService;
        this.routes = new Map();
        this.middleware = [];
        this.setupRouting();
    }

    setupRouting() {
        this.busService.subscribe('notification', (data) => {
            this.routeMessage(data);
        });
    }

    addRoute(pattern, handler) {
        if (typeof pattern === 'string') {
            pattern = new RegExp(pattern);
        }

        this.routes.set(pattern, handler);
    }

    addMiddleware(middleware) {
        this.middleware.push(middleware);
    }

    async routeMessage(data) {
        try {
            // 应用中间件
            let processedData = data;
            for (const middleware of this.middleware) {
                processedData = await middleware(processedData);
                if (!processedData) {
                    return; // 中间件阻止了消息处理
                }
            }

            // 查找匹配的路由
            for (const [pattern, handler] of this.routes) {
                if (this.matchPattern(pattern, processedData)) {
                    await handler(processedData);
                }
            }
        } catch (error) {
            console.error('消息路由失败:', error);
        }
    }

    matchPattern(pattern, data) {
        if (pattern instanceof RegExp) {
            return pattern.test(data.message?.type || '');
        }

        if (typeof pattern === 'function') {
            return pattern(data);
        }

        return false;
    }
}

// 使用消息路由器
const router = new BusMessageRouter(busService);

// 添加路由
router.addRoute(/^user\./, (data) => {
    console.log('用户相关消息:', data);
});

router.addRoute(/^system\./, (data) => {
    console.log('系统消息:', data);
});

// 添加中间件
router.addMiddleware(async (data) => {
    // 日志记录中间件
    console.log('收到消息:', data);
    return data;
});

router.addMiddleware(async (data) => {
    // 权限检查中间件
    if (data.requiresAuth && !user.isAuthenticated) {
        console.warn('未授权的消息被阻止');
        return null;
    }
    return data;
});
```

### 3. 总线服务代理
```javascript
class BusServiceProxy {
    constructor(busService) {
        this.busService = busService;
        this.cache = new Map();
        this.subscriptions = new Map();
        this.rateLimiter = new Map();
        this.setupProxy();
    }

    setupProxy() {
        return new Proxy(this.busService, {
            get: (target, prop) => {
                if (prop === 'subscribe') {
                    return this.enhancedSubscribe.bind(this);
                }

                if (prop === 'trigger') {
                    return this.enhancedTrigger.bind(this);
                }

                return target[prop];
            }
        });
    }

    enhancedSubscribe(eventName, callback, options = {}) {
        // 缓存支持
        if (options.cache) {
            const cachedData = this.cache.get(eventName);
            if (cachedData) {
                setTimeout(() => callback(cachedData), 0);
            }
        }

        // 防抖支持
        if (options.debounce) {
            callback = this.debounce(callback, options.debounce);
        }

        // 节流支持
        if (options.throttle) {
            callback = this.throttle(callback, options.throttle);
        }

        // 错误处理
        const wrappedCallback = (data) => {
            try {
                callback(data);

                // 更新缓存
                if (options.cache) {
                    this.cache.set(eventName, data);
                }
            } catch (error) {
                console.error(`订阅回调错误 (${eventName}):`, error);
            }
        };

        return this.busService.subscribe(eventName, wrappedCallback);
    }

    enhancedTrigger(eventName, data) {
        // 速率限制
        if (this.isRateLimited(eventName)) {
            console.warn(`事件 ${eventName} 被速率限制`);
            return;
        }

        // 数据验证
        if (!this.validateData(eventName, data)) {
            console.error(`事件 ${eventName} 数据验证失败`);
            return;
        }

        return this.busService.trigger(eventName, data);
    }

    isRateLimited(eventName) {
        const now = Date.now();
        const limit = this.rateLimiter.get(eventName);

        if (!limit) {
            this.rateLimiter.set(eventName, {
                count: 1,
                resetTime: now + 1000 // 1秒窗口
            });
            return false;
        }

        if (now > limit.resetTime) {
            limit.count = 1;
            limit.resetTime = now + 1000;
            return false;
        }

        if (limit.count >= 10) { // 每秒最多10次
            return true;
        }

        limit.count++;
        return false;
    }

    validateData(eventName, data) {
        // 基本数据验证
        if (data === null || data === undefined) {
            return false;
        }

        // 可以添加更复杂的验证逻辑
        return true;
    }

    debounce(func, delay) {
        let timeoutId;
        return function(...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    }

    throttle(func, delay) {
        let lastCall = 0;
        return function(...args) {
            const now = Date.now();
            if (now - lastCall >= delay) {
                lastCall = now;
                return func.apply(this, args);
            }
        };
    }
}
```

## 最佳实践

### 1. 服务初始化
```javascript
// ✅ 推荐：正确的服务依赖配置
const busService = {
    dependencies: [
        "bus.parameters",
        "localization",
        "multi_tab",
        "notification"
    ]
};
```

### 2. 消息订阅
```javascript
// ✅ 推荐：使用清理函数
const unsubscribe = busService.subscribe('notification', callback);

// 在组件卸载时清理
onWillUnmount(() => {
    unsubscribe();
});
```

### 3. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
busService.subscribe('bus_error', (error) => {
    console.error('总线错误:', error);
    // 实现错误恢复逻辑
});
```

## 总结

Odoo 总线服务模块提供了强大的实时通信功能：

**核心优势**:
- **跨标签页通信**: 通过SharedWorker实现高效的跨标签页通信
- **自动重连**: 智能的自动重连机制确保连接稳定性
- **消息路由**: 灵活的消息订阅和分发系统
- **状态管理**: 完善的连接状态管理和监控
- **性能优化**: 资源共享和连接复用提升性能

**适用场景**:
- 实时通知系统
- 多用户协作
- 状态同步
- 系统监控
- 消息推送

**设计优势**:
- 事件驱动架构
- 高可用性设计
- 资源优化
- 扩展性强

这个总线服务为 Odoo Web 客户端提供了强大的实时通信能力，是构建现代化Web应用的重要基础设施。

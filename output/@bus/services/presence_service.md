# Odoo 在线状态服务 (Presence Service) 学习资料

## 文件概述

**文件路径**: `output/@bus/services/presence_service.js`  
**原始路径**: `/bus/static/src/services/presence_service.js`  
**模块类型**: 核心服务 - 用户在线状态管理  
**代码行数**: 74 行  
**依赖关系**: 
- `@odoo/owl` - OWL框架
- `@web/core/browser/browser` - 浏览器工具
- `@web/core/registry` - 注册表系统

## 模块功能

在线状态服务模块是 Odoo Web 客户端的用户活动监控系统。该模块提供了：
- 用户在线状态检测
- 窗口焦点状态监控
- 跨标签页状态同步
- 用户活动时间记录
- 本地存储状态持久化
- 实时状态事件通知

这个服务通过监控用户的鼠标、键盘活动和窗口焦点状态，实现了准确的用户在线状态检测。

## 在线状态服务架构

### 核心组件结构
```
Presence Service
├── 状态检测
│   ├── 用户活动监听
│   ├── 窗口焦点检测
│   ├── 鼠标移动监听
│   └── 键盘活动监听
├── 状态存储
│   ├── 本地存储管理
│   ├── 状态持久化
│   ├── 跨标签页同步
│   └── 存储事件监听
├── 事件系统
│   ├── 状态变化事件
│   ├── 焦点变化事件
│   ├── 活动检测事件
│   └── 同步事件
└── 时间管理
    ├── 最后活动时间
    ├── 时间戳更新
    ├── 超时检测
    └── 时间格式化
```

### 服务配置
```javascript
const presenceService = {
    start(env) {
        const LOCAL_STORAGE_PREFIX = "presence";
        const bus = new EventBus();
        let isOdooFocused = true;
        let lastPresenceTime = browser.localStorage.getItem(
            `${LOCAL_STORAGE_PREFIX}.lastPresence`
        ) || luxon.DateTime.now().ts;
        
        // 服务实现...
        return presenceServiceInstance;
    }
};
```

## 核心功能详解

### 1. 用户活动检测
```javascript
// 用户活动检测和处理
class UserActivityDetector {
    constructor() {
        this.lastPresenceTime = luxon.DateTime.now().ts;
        this.isOdooFocused = true;
        this.activityEvents = ['mousedown', 'mousemove', 'keydown', 'scroll', 'touchstart'];
        this.inactivityTimeout = 5 * 60 * 1000; // 5分钟
        this.checkInterval = 30 * 1000; // 30秒检查一次
        this.bus = new EventBus();
        this.setupActivityListeners();
        this.startInactivityCheck();
    }
    
    setupActivityListeners() {
        // 监听用户活动事件
        this.activityEvents.forEach(eventType => {
            document.addEventListener(eventType, this.onUserActivity.bind(this), {
                passive: true,
                capture: true
            });
        });
        
        // 监听窗口焦点变化
        window.addEventListener('focus', () => this.onFocusChange(true));
        window.addEventListener('blur', () => this.onFocusChange(false));
        
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', this.onVisibilityChange.bind(this));
    }
    
    onUserActivity(event) {
        // 更新最后活动时间
        this.updatePresenceTime();
        
        // 触发活动事件
        this.bus.trigger('user_activity', {
            type: event.type,
            timestamp: this.lastPresenceTime,
            target: event.target.tagName
        });
    }
    
    updatePresenceTime() {
        this.lastPresenceTime = luxon.DateTime.now().ts;
        
        // 保存到本地存储
        browser.localStorage.setItem(
            'presence.lastPresence', 
            this.lastPresenceTime
        );
        
        // 触发在线状态事件
        this.bus.trigger('presence', {
            timestamp: this.lastPresenceTime,
            isActive: true
        });
    }
    
    onFocusChange(isFocused) {
        try {
            // 尝试检测父窗口焦点状态
            isFocused = parent.document.hasFocus();
        } catch (error) {
            // 跨域限制时使用传入的状态
        }
        
        this.isOdooFocused = isFocused;
        
        // 保存焦点状态
        browser.localStorage.setItem('presence.focus', this.isOdooFocused);
        
        if (isFocused) {
            this.updatePresenceTime();
        }
        
        // 触发焦点变化事件
        this.bus.trigger('window_focus', {
            isFocused: isFocused,
            timestamp: Date.now()
        });
    }
    
    onVisibilityChange() {
        const isVisible = !document.hidden;
        
        if (isVisible) {
            this.updatePresenceTime();
        }
        
        // 触发可见性变化事件
        this.bus.trigger('visibility_change', {
            isVisible: isVisible,
            timestamp: Date.now()
        });
    }
    
    startInactivityCheck() {
        setInterval(() => {
            this.checkInactivity();
        }, this.checkInterval);
    }
    
    checkInactivity() {
        const now = luxon.DateTime.now().ts;
        const timeSinceLastActivity = now - this.lastPresenceTime;
        
        if (timeSinceLastActivity > this.inactivityTimeout) {
            this.bus.trigger('user_inactive', {
                lastActivity: this.lastPresenceTime,
                inactiveDuration: timeSinceLastActivity
            });
        }
    }
    
    getPresenceStatus() {
        const now = luxon.DateTime.now().ts;
        const timeSinceLastActivity = now - this.lastPresenceTime;
        
        return {
            isActive: timeSinceLastActivity < this.inactivityTimeout,
            isFocused: this.isOdooFocused,
            lastPresenceTime: this.lastPresenceTime,
            timeSinceLastActivity: timeSinceLastActivity,
            status: this.getStatusText(timeSinceLastActivity)
        };
    }
    
    getStatusText(timeSinceLastActivity) {
        if (timeSinceLastActivity < 60 * 1000) {
            return 'online';
        } else if (timeSinceLastActivity < 5 * 60 * 1000) {
            return 'away';
        } else {
            return 'offline';
        }
    }
}
```

### 2. 跨标签页状态同步
```javascript
// 跨标签页状态同步管理
class CrossTabSyncManager {
    constructor(bus) {
        this.bus = bus;
        this.LOCAL_STORAGE_PREFIX = "presence";
        this.syncKeys = [
            `${this.LOCAL_STORAGE_PREFIX}.focus`,
            `${this.LOCAL_STORAGE_PREFIX}.lastPresence`,
            `${this.LOCAL_STORAGE_PREFIX}.status`
        ];
        this.setupStorageListener();
    }
    
    setupStorageListener() {
        window.addEventListener('storage', this.onStorageChange.bind(this));
    }
    
    onStorageChange(event) {
        const { key, newValue, oldValue } = event;
        
        if (!this.syncKeys.includes(key)) {
            return;
        }
        
        try {
            const parsedNewValue = JSON.parse(newValue);
            const parsedOldValue = oldValue ? JSON.parse(oldValue) : null;
            
            this.handleStorageUpdate(key, parsedNewValue, parsedOldValue);
        } catch (error) {
            console.warn('存储值解析失败:', error);
        }
    }
    
    handleStorageUpdate(key, newValue, oldValue) {
        switch (key) {
            case `${this.LOCAL_STORAGE_PREFIX}.focus`:
                this.handleFocusUpdate(newValue);
                break;
                
            case `${this.LOCAL_STORAGE_PREFIX}.lastPresence`:
                this.handlePresenceUpdate(newValue);
                break;
                
            case `${this.LOCAL_STORAGE_PREFIX}.status`:
                this.handleStatusUpdate(newValue);
                break;
        }
    }
    
    handleFocusUpdate(isFocused) {
        this.bus.trigger('window_focus', {
            isFocused: isFocused,
            source: 'cross_tab',
            timestamp: Date.now()
        });
    }
    
    handlePresenceUpdate(timestamp) {
        this.bus.trigger('presence', {
            timestamp: timestamp,
            source: 'cross_tab',
            isActive: true
        });
    }
    
    handleStatusUpdate(status) {
        this.bus.trigger('status_change', {
            status: status,
            source: 'cross_tab',
            timestamp: Date.now()
        });
    }
    
    updateStorage(key, value) {
        const fullKey = `${this.LOCAL_STORAGE_PREFIX}.${key}`;
        browser.localStorage.setItem(fullKey, JSON.stringify(value));
    }
    
    getStorage(key) {
        const fullKey = `${this.LOCAL_STORAGE_PREFIX}.${key}`;
        const value = browser.localStorage.getItem(fullKey);
        
        try {
            return value ? JSON.parse(value) : null;
        } catch (error) {
            return null;
        }
    }
    
    clearStorage() {
        this.syncKeys.forEach(key => {
            browser.localStorage.removeItem(key);
        });
    }
}
```

### 3. 在线状态管理器
```javascript
// 完整的在线状态管理器
class PresenceManager {
    constructor() {
        this.bus = new EventBus();
        this.activityDetector = new UserActivityDetector();
        this.syncManager = new CrossTabSyncManager(this.bus);
        this.statusHistory = [];
        this.maxHistorySize = 100;
        this.setupEventHandlers();
    }
    
    setupEventHandlers() {
        // 监听活动检测器事件
        this.activityDetector.bus.addEventListener('user_activity', (data) => {
            this.handleUserActivity(data);
        });
        
        this.activityDetector.bus.addEventListener('window_focus', (data) => {
            this.handleWindowFocus(data);
        });
        
        this.activityDetector.bus.addEventListener('user_inactive', (data) => {
            this.handleUserInactive(data);
        });
        
        // 监听跨标签页同步事件
        this.bus.addEventListener('presence', (data) => {
            this.handlePresenceUpdate(data);
        });
    }
    
    handleUserActivity(data) {
        this.addToHistory({
            type: 'activity',
            data: data,
            timestamp: Date.now()
        });
        
        // 触发外部事件
        this.bus.trigger('presence_activity', data);
    }
    
    handleWindowFocus(data) {
        this.addToHistory({
            type: 'focus',
            data: data,
            timestamp: Date.now()
        });
        
        // 更新存储
        this.syncManager.updateStorage('focus', data.isFocused);
        
        // 触发外部事件
        this.bus.trigger('presence_focus', data);
    }
    
    handleUserInactive(data) {
        this.addToHistory({
            type: 'inactive',
            data: data,
            timestamp: Date.now()
        });
        
        // 更新状态
        this.syncManager.updateStorage('status', 'inactive');
        
        // 触发外部事件
        this.bus.trigger('presence_inactive', data);
    }
    
    handlePresenceUpdate(data) {
        this.addToHistory({
            type: 'presence',
            data: data,
            timestamp: Date.now()
        });
        
        // 触发外部事件
        this.bus.trigger('presence_update', data);
    }
    
    addToHistory(entry) {
        this.statusHistory.push(entry);
        
        // 限制历史记录大小
        if (this.statusHistory.length > this.maxHistorySize) {
            this.statusHistory.shift();
        }
    }
    
    getCurrentStatus() {
        return this.activityDetector.getPresenceStatus();
    }
    
    getStatusHistory(limit = 10) {
        return this.statusHistory.slice(-limit);
    }
    
    getDetailedStatus() {
        const currentStatus = this.getCurrentStatus();
        const recentHistory = this.getStatusHistory(5);
        
        return {
            current: currentStatus,
            history: recentHistory,
            statistics: this.calculateStatistics(),
            crossTab: {
                focus: this.syncManager.getStorage('focus'),
                lastPresence: this.syncManager.getStorage('lastPresence'),
                status: this.syncManager.getStorage('status')
            }
        };
    }
    
    calculateStatistics() {
        const now = Date.now();
        const oneHourAgo = now - (60 * 60 * 1000);
        
        const recentEvents = this.statusHistory.filter(
            entry => entry.timestamp > oneHourAgo
        );
        
        const activityCount = recentEvents.filter(
            entry => entry.type === 'activity'
        ).length;
        
        const focusChanges = recentEvents.filter(
            entry => entry.type === 'focus'
        ).length;
        
        return {
            activityCount: activityCount,
            focusChanges: focusChanges,
            totalEvents: recentEvents.length,
            timeRange: '1 hour'
        };
    }
    
    subscribe(eventName, callback) {
        return this.bus.addEventListener(eventName, callback);
    }
    
    unsubscribe(eventName, callback) {
        this.bus.removeEventListener(eventName, callback);
    }
    
    cleanup() {
        this.syncManager.clearStorage();
        this.statusHistory = [];
    }
}
```

## 使用示例

### 1. 基本在线状态监控
```javascript
// 在组件中使用在线状态服务
class UserStatusComponent extends Component {
    static template = xml`
        <div class="user-status">
            <div class="status-indicator" t-att-class="statusClass">
                <i t-att-class="statusIcon" />
                <span t-esc="statusText" />
            </div>
            
            <div class="status-details" t-if="showDetails">
                <div>最后活动: <span t-esc="lastActivityText" /></div>
                <div>窗口焦点: <span t-esc="focusText" /></div>
                <div>活动次数: <span t-esc="activityCount" /></div>
            </div>
        </div>
    `;
    
    setup() {
        this.presenceService = useService('presence');
        this.userStatus = useState({
            isActive: true,
            isFocused: true,
            lastPresenceTime: Date.now(),
            activityCount: 0
        });
        
        this.showDetails = useState(false);
        
        onWillStart(() => {
            this.setupPresenceMonitoring();
        });
        
        onWillUnmount(() => {
            this.cleanupSubscriptions();
        });
    }
    
    setupPresenceMonitoring() {
        // 订阅在线状态事件
        this.presenceSubscription = this.presenceService.subscribe('presence', (data) => {
            this.handlePresenceUpdate(data);
        });
        
        // 订阅焦点变化事件
        this.focusSubscription = this.presenceService.subscribe('window_focus', (data) => {
            this.handleFocusChange(data);
        });
        
        // 订阅用户活动事件
        this.activitySubscription = this.presenceService.subscribe('user_activity', (data) => {
            this.handleUserActivity(data);
        });
        
        // 订阅非活动状态事件
        this.inactiveSubscription = this.presenceService.subscribe('user_inactive', (data) => {
            this.handleUserInactive(data);
        });
        
        // 初始化状态
        this.updateStatus();
    }
    
    handlePresenceUpdate(data) {
        this.userStatus.isActive = true;
        this.userStatus.lastPresenceTime = data.timestamp;
        this.updateStatus();
    }
    
    handleFocusChange(data) {
        this.userStatus.isFocused = data.isFocused;
        this.updateStatus();
    }
    
    handleUserActivity(data) {
        this.userStatus.activityCount++;
        this.userStatus.lastPresenceTime = data.timestamp;
        this.updateStatus();
    }
    
    handleUserInactive(data) {
        this.userStatus.isActive = false;
        this.updateStatus();
    }
    
    updateStatus() {
        // 触发重新渲染
        this.render();
    }
    
    get statusClass() {
        if (!this.userStatus.isActive) {
            return 'status-inactive';
        } else if (!this.userStatus.isFocused) {
            return 'status-away';
        } else {
            return 'status-online';
        }
    }
    
    get statusIcon() {
        if (!this.userStatus.isActive) {
            return 'fa fa-circle text-muted';
        } else if (!this.userStatus.isFocused) {
            return 'fa fa-circle text-warning';
        } else {
            return 'fa fa-circle text-success';
        }
    }
    
    get statusText() {
        if (!this.userStatus.isActive) {
            return '离线';
        } else if (!this.userStatus.isFocused) {
            return '离开';
        } else {
            return '在线';
        }
    }
    
    get lastActivityText() {
        const now = Date.now();
        const diff = now - this.userStatus.lastPresenceTime;
        
        if (diff < 60 * 1000) {
            return '刚刚';
        } else if (diff < 60 * 60 * 1000) {
            return `${Math.floor(diff / (60 * 1000))} 分钟前`;
        } else {
            return `${Math.floor(diff / (60 * 60 * 1000))} 小时前`;
        }
    }
    
    get focusText() {
        return this.userStatus.isFocused ? '是' : '否';
    }
    
    get activityCount() {
        return this.userStatus.activityCount;
    }
    
    cleanupSubscriptions() {
        if (this.presenceSubscription) {
            this.presenceSubscription();
        }
        if (this.focusSubscription) {
            this.focusSubscription();
        }
        if (this.activitySubscription) {
            this.activitySubscription();
        }
        if (this.inactiveSubscription) {
            this.inactiveSubscription();
        }
    }
}
```

### 2. 高级在线状态应用
```javascript
// 高级在线状态管理应用
class AdvancedPresenceApp {
    constructor() {
        this.presenceManager = new PresenceManager();
        this.userSessions = new Map();
        this.statusCallbacks = new Set();
        this.setupAdvancedFeatures();
    }
    
    setupAdvancedFeatures() {
        // 监听状态变化
        this.presenceManager.subscribe('presence_update', (data) => {
            this.handleStatusUpdate(data);
        });
        
        // 监听非活动状态
        this.presenceManager.subscribe('presence_inactive', (data) => {
            this.handleInactiveUser(data);
        });
        
        // 设置定期状态报告
        this.setupStatusReporting();
    }
    
    setupStatusReporting() {
        setInterval(() => {
            this.generateStatusReport();
        }, 5 * 60 * 1000); // 每5分钟生成一次报告
    }
    
    handleStatusUpdate(data) {
        const userId = this.getCurrentUserId();
        const session = this.getOrCreateSession(userId);
        
        session.lastActivity = data.timestamp;
        session.isActive = true;
        session.activityCount++;
        
        // 通知所有回调
        this.notifyStatusCallbacks({
            userId: userId,
            status: 'active',
            timestamp: data.timestamp
        });
    }
    
    handleInactiveUser(data) {
        const userId = this.getCurrentUserId();
        const session = this.getOrCreateSession(userId);
        
        session.isActive = false;
        session.inactiveSince = data.timestamp;
        
        // 通知所有回调
        this.notifyStatusCallbacks({
            userId: userId,
            status: 'inactive',
            timestamp: data.timestamp,
            inactiveDuration: data.inactiveDuration
        });
    }
    
    getOrCreateSession(userId) {
        if (!this.userSessions.has(userId)) {
            this.userSessions.set(userId, {
                userId: userId,
                startTime: Date.now(),
                lastActivity: Date.now(),
                isActive: true,
                activityCount: 0,
                focusChanges: 0,
                totalTime: 0
            });
        }
        
        return this.userSessions.get(userId);
    }
    
    getCurrentUserId() {
        // 获取当前用户ID的逻辑
        return odoo.session.uid || 'anonymous';
    }
    
    addStatusCallback(callback) {
        this.statusCallbacks.add(callback);
        
        return () => {
            this.statusCallbacks.delete(callback);
        };
    }
    
    notifyStatusCallbacks(data) {
        this.statusCallbacks.forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error('状态回调执行失败:', error);
            }
        });
    }
    
    generateStatusReport() {
        const report = {
            timestamp: Date.now(),
            sessions: Array.from(this.userSessions.values()),
            summary: this.calculateSummary(),
            presenceDetails: this.presenceManager.getDetailedStatus()
        };
        
        console.log('在线状态报告:', report);
        
        // 可以发送到服务器或保存到本地
        this.saveReport(report);
        
        return report;
    }
    
    calculateSummary() {
        const sessions = Array.from(this.userSessions.values());
        const activeSessions = sessions.filter(s => s.isActive);
        
        return {
            totalSessions: sessions.length,
            activeSessions: activeSessions.length,
            inactiveSessions: sessions.length - activeSessions.length,
            totalActivityCount: sessions.reduce((sum, s) => sum + s.activityCount, 0),
            averageActivityCount: sessions.length > 0 ? 
                sessions.reduce((sum, s) => sum + s.activityCount, 0) / sessions.length : 0
        };
    }
    
    saveReport(report) {
        // 保存报告到本地存储
        const reports = this.getStoredReports();
        reports.push(report);
        
        // 只保留最近10个报告
        if (reports.length > 10) {
            reports.splice(0, reports.length - 10);
        }
        
        browser.localStorage.setItem('presence_reports', JSON.stringify(reports));
    }
    
    getStoredReports() {
        try {
            const stored = browser.localStorage.getItem('presence_reports');
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            return [];
        }
    }
    
    getSessionData(userId) {
        return this.userSessions.get(userId) || null;
    }
    
    getAllSessions() {
        return Array.from(this.userSessions.values());
    }
    
    clearSessionData() {
        this.userSessions.clear();
    }
    
    cleanup() {
        this.presenceManager.cleanup();
        this.clearSessionData();
        this.statusCallbacks.clear();
    }
}

// 使用高级在线状态应用
const presenceApp = new AdvancedPresenceApp();

// 添加状态监听
const unsubscribe = presenceApp.addStatusCallback((data) => {
    console.log('用户状态变化:', data);
});

// 获取当前状态
const currentStatus = presenceApp.presenceManager.getCurrentStatus();
console.log('当前状态:', currentStatus);
```

## 在线状态服务特性

### 1. 多维度状态检测
在线状态服务通过多个维度检测用户状态：
- **鼠标活动**: 监听鼠标移动和点击
- **键盘活动**: 监听键盘按键事件
- **窗口焦点**: 检测窗口是否获得焦点
- **页面可见性**: 监听页面可见性变化
- **滚动活动**: 监听页面滚动事件

### 2. 智能状态判断
```javascript
// 状态判断逻辑
const getStatusFromActivity = (timeSinceLastActivity) => {
    if (timeSinceLastActivity < 60 * 1000) {
        return 'online';      // 1分钟内活动
    } else if (timeSinceLastActivity < 5 * 60 * 1000) {
        return 'away';        // 5分钟内活动
    } else {
        return 'offline';     // 超过5分钟无活动
    }
};
```

### 3. 跨标签页同步
通过localStorage实现跨标签页的状态同步：
- **焦点状态同步**: 所有标签页共享焦点状态
- **活动时间同步**: 最后活动时间在标签页间同步
- **状态变化通知**: 状态变化实时通知所有标签页

### 4. 本地存储持久化
用户状态信息持久化存储，页面刷新后状态不丢失。

## 高级应用模式

### 1. 在线状态分析器
```javascript
class PresenceAnalyzer {
    constructor(presenceManager) {
        this.presenceManager = presenceManager;
        this.analysisData = {
            dailyActivity: new Map(),
            weeklyPattern: new Map(),
            focusPattern: new Map(),
            productivityMetrics: new Map()
        };
        this.setupAnalysis();
    }

    setupAnalysis() {
        this.presenceManager.subscribe('presence_activity', (data) => {
            this.recordActivity(data);
        });

        this.presenceManager.subscribe('presence_focus', (data) => {
            this.recordFocusChange(data);
        });

        // 每小时分析一次数据
        setInterval(() => {
            this.performAnalysis();
        }, 60 * 60 * 1000);
    }

    recordActivity(data) {
        const date = new Date().toDateString();
        const hour = new Date().getHours();

        // 记录每日活动
        if (!this.analysisData.dailyActivity.has(date)) {
            this.analysisData.dailyActivity.set(date, new Map());
        }

        const dayData = this.analysisData.dailyActivity.get(date);
        const hourKey = `hour_${hour}`;
        dayData.set(hourKey, (dayData.get(hourKey) || 0) + 1);

        // 记录活动类型
        const activityType = data.type;
        if (!this.analysisData.weeklyPattern.has(activityType)) {
            this.analysisData.weeklyPattern.set(activityType, []);
        }

        this.analysisData.weeklyPattern.get(activityType).push({
            timestamp: data.timestamp,
            hour: hour,
            dayOfWeek: new Date().getDay()
        });
    }

    recordFocusChange(data) {
        const date = new Date().toDateString();

        if (!this.analysisData.focusPattern.has(date)) {
            this.analysisData.focusPattern.set(date, {
                focusTime: 0,
                blurTime: 0,
                focusChanges: 0,
                lastFocusChange: null
            });
        }

        const focusData = this.analysisData.focusPattern.get(date);
        focusData.focusChanges++;

        if (focusData.lastFocusChange) {
            const duration = data.timestamp - focusData.lastFocusChange.timestamp;

            if (focusData.lastFocusChange.isFocused) {
                focusData.focusTime += duration;
            } else {
                focusData.blurTime += duration;
            }
        }

        focusData.lastFocusChange = data;
    }

    performAnalysis() {
        const analysis = {
            activityTrends: this.analyzeActivityTrends(),
            focusMetrics: this.analyzeFocusMetrics(),
            productivityScore: this.calculateProductivityScore(),
            recommendations: this.generateRecommendations()
        };

        console.log('在线状态分析结果:', analysis);
        return analysis;
    }

    analyzeActivityTrends() {
        const trends = {
            peakHours: [],
            lowActivityHours: [],
            averageActivityPerHour: 0,
            mostActiveDay: null
        };

        // 分析每小时活动量
        const hourlyActivity = new Map();

        this.analysisData.dailyActivity.forEach((dayData) => {
            dayData.forEach((count, hour) => {
                hourlyActivity.set(hour, (hourlyActivity.get(hour) || 0) + count);
            });
        });

        // 找出活跃时段
        const sortedHours = Array.from(hourlyActivity.entries())
            .sort((a, b) => b[1] - a[1]);

        trends.peakHours = sortedHours.slice(0, 3).map(([hour, count]) => ({
            hour: parseInt(hour.replace('hour_', '')),
            activity: count
        }));

        trends.lowActivityHours = sortedHours.slice(-3).map(([hour, count]) => ({
            hour: parseInt(hour.replace('hour_', '')),
            activity: count
        }));

        return trends;
    }

    analyzeFocusMetrics() {
        const metrics = {
            averageFocusTime: 0,
            focusEfficiency: 0,
            distractionCount: 0,
            longestFocusSession: 0
        };

        let totalFocusTime = 0;
        let totalTime = 0;
        let totalFocusChanges = 0;

        this.analysisData.focusPattern.forEach((data) => {
            totalFocusTime += data.focusTime;
            totalTime += data.focusTime + data.blurTime;
            totalFocusChanges += data.focusChanges;
        });

        if (totalTime > 0) {
            metrics.focusEfficiency = (totalFocusTime / totalTime) * 100;
        }

        metrics.averageFocusTime = totalFocusTime / this.analysisData.focusPattern.size;
        metrics.distractionCount = totalFocusChanges;

        return metrics;
    }

    calculateProductivityScore() {
        const activityScore = this.calculateActivityScore();
        const focusScore = this.calculateFocusScore();
        const consistencyScore = this.calculateConsistencyScore();

        return {
            overall: Math.round((activityScore + focusScore + consistencyScore) / 3),
            activity: activityScore,
            focus: focusScore,
            consistency: consistencyScore
        };
    }

    calculateActivityScore() {
        // 基于活动频率计算分数
        const totalActivity = Array.from(this.analysisData.dailyActivity.values())
            .reduce((sum, dayData) => {
                return sum + Array.from(dayData.values()).reduce((daySum, count) => daySum + count, 0);
            }, 0);

        const days = this.analysisData.dailyActivity.size;
        const averageActivityPerDay = days > 0 ? totalActivity / days : 0;

        // 假设每天100次活动为满分
        return Math.min(100, (averageActivityPerDay / 100) * 100);
    }

    calculateFocusScore() {
        // 基于专注度计算分数
        const focusMetrics = this.analyzeFocusMetrics();
        return Math.min(100, focusMetrics.focusEfficiency);
    }

    calculateConsistencyScore() {
        // 基于活动一致性计算分数
        const dailyActivities = Array.from(this.analysisData.dailyActivity.values())
            .map(dayData => Array.from(dayData.values()).reduce((sum, count) => sum + count, 0));

        if (dailyActivities.length < 2) {
            return 100;
        }

        const mean = dailyActivities.reduce((sum, activity) => sum + activity, 0) / dailyActivities.length;
        const variance = dailyActivities.reduce((sum, activity) => sum + Math.pow(activity - mean, 2), 0) / dailyActivities.length;
        const standardDeviation = Math.sqrt(variance);

        // 标准差越小，一致性越高
        const consistencyRatio = mean > 0 ? (mean - standardDeviation) / mean : 0;
        return Math.max(0, Math.min(100, consistencyRatio * 100));
    }

    generateRecommendations() {
        const recommendations = [];
        const productivityScore = this.calculateProductivityScore();
        const focusMetrics = this.analyzeFocusMetrics();

        if (productivityScore.activity < 50) {
            recommendations.push({
                type: 'activity',
                priority: 'high',
                message: '活动水平较低，建议增加工作时间或提高工作强度',
                action: 'increase_activity'
            });
        }

        if (focusMetrics.focusEfficiency < 60) {
            recommendations.push({
                type: 'focus',
                priority: 'medium',
                message: '专注度较低，建议减少干扰因素',
                action: 'improve_focus'
            });
        }

        if (focusMetrics.distractionCount > 50) {
            recommendations.push({
                type: 'distraction',
                priority: 'medium',
                message: '分心次数过多，建议使用专注工具',
                action: 'reduce_distractions'
            });
        }

        if (productivityScore.consistency < 70) {
            recommendations.push({
                type: 'consistency',
                priority: 'low',
                message: '工作模式不够稳定，建议建立规律的工作习惯',
                action: 'improve_consistency'
            });
        }

        return recommendations;
    }

    exportAnalysisData() {
        return {
            dailyActivity: Object.fromEntries(this.analysisData.dailyActivity),
            weeklyPattern: Object.fromEntries(this.analysisData.weeklyPattern),
            focusPattern: Object.fromEntries(this.analysisData.focusPattern),
            productivityMetrics: Object.fromEntries(this.analysisData.productivityMetrics)
        };
    }

    clearAnalysisData() {
        this.analysisData.dailyActivity.clear();
        this.analysisData.weeklyPattern.clear();
        this.analysisData.focusPattern.clear();
        this.analysisData.productivityMetrics.clear();
    }
}
```

### 2. 在线状态通知系统
```javascript
class PresenceNotificationSystem {
    constructor(presenceManager) {
        this.presenceManager = presenceManager;
        this.notificationQueue = [];
        this.notificationSettings = {
            enableInactivityReminders: true,
            inactivityThreshold: 10 * 60 * 1000, // 10分钟
            enableFocusReminders: true,
            focusReminderInterval: 30 * 60 * 1000, // 30分钟
            enableBreakReminders: true,
            breakReminderInterval: 60 * 60 * 1000 // 1小时
        };
        this.setupNotifications();
    }

    setupNotifications() {
        this.presenceManager.subscribe('presence_inactive', (data) => {
            this.handleInactivityNotification(data);
        });

        this.presenceManager.subscribe('presence_focus', (data) => {
            this.handleFocusNotification(data);
        });

        // 设置定期提醒
        this.setupPeriodicReminders();
    }

    setupPeriodicReminders() {
        if (this.notificationSettings.enableBreakReminders) {
            setInterval(() => {
                this.sendBreakReminder();
            }, this.notificationSettings.breakReminderInterval);
        }
    }

    handleInactivityNotification(data) {
        if (!this.notificationSettings.enableInactivityReminders) {
            return;
        }

        if (data.inactiveDuration > this.notificationSettings.inactivityThreshold) {
            this.queueNotification({
                type: 'inactivity',
                title: '长时间未活动',
                message: `您已经 ${Math.round(data.inactiveDuration / (60 * 1000))} 分钟未活动`,
                priority: 'medium',
                actions: [
                    { text: '我回来了', action: 'resume_activity' },
                    { text: '稍后提醒', action: 'snooze' }
                ]
            });
        }
    }

    handleFocusNotification(data) {
        if (!this.notificationSettings.enableFocusReminders) {
            return;
        }

        if (!data.isFocused) {
            // 失去焦点时的提醒
            setTimeout(() => {
                if (!this.presenceManager.getCurrentStatus().isFocused) {
                    this.queueNotification({
                        type: 'focus',
                        title: '注意力分散',
                        message: '您似乎离开了工作区域，是否需要专注提醒？',
                        priority: 'low',
                        actions: [
                            { text: '开启专注模式', action: 'enable_focus_mode' },
                            { text: '忽略', action: 'dismiss' }
                        ]
                    });
                }
            }, this.notificationSettings.focusReminderInterval);
        }
    }

    sendBreakReminder() {
        const status = this.presenceManager.getCurrentStatus();

        if (status.isActive && status.timeSinceLastActivity < 5 * 60 * 1000) {
            this.queueNotification({
                type: 'break',
                title: '休息提醒',
                message: '您已经工作了一段时间，建议休息一下',
                priority: 'low',
                actions: [
                    { text: '休息5分钟', action: 'take_break_5' },
                    { text: '休息15分钟', action: 'take_break_15' },
                    { text: '稍后提醒', action: 'snooze_break' }
                ]
            });
        }
    }

    queueNotification(notification) {
        notification.id = Date.now() + Math.random();
        notification.timestamp = Date.now();

        this.notificationQueue.push(notification);
        this.processNotificationQueue();
    }

    processNotificationQueue() {
        if (this.notificationQueue.length === 0) {
            return;
        }

        // 按优先级排序
        this.notificationQueue.sort((a, b) => {
            const priorityOrder = { high: 3, medium: 2, low: 1 };
            return priorityOrder[b.priority] - priorityOrder[a.priority];
        });

        const notification = this.notificationQueue.shift();
        this.showNotification(notification);
    }

    showNotification(notification) {
        if ('Notification' in window && Notification.permission === 'granted') {
            this.showBrowserNotification(notification);
        } else {
            this.showInAppNotification(notification);
        }
    }

    showBrowserNotification(notification) {
        const browserNotification = new Notification(notification.title, {
            body: notification.message,
            icon: '/web/static/img/favicon.ico',
            tag: notification.type
        });

        browserNotification.onclick = () => {
            this.handleNotificationAction(notification, 'click');
            browserNotification.close();
        };

        // 自动关闭
        setTimeout(() => {
            browserNotification.close();
        }, 5000);
    }

    showInAppNotification(notification) {
        // 在应用内显示通知
        const notificationElement = document.createElement('div');
        notificationElement.className = `notification notification-${notification.priority}`;
        notificationElement.innerHTML = `
            <div class="notification-content">
                <h4>${notification.title}</h4>
                <p>${notification.message}</p>
                <div class="notification-actions">
                    ${notification.actions.map(action =>
                        `<button class="btn btn-sm" data-action="${action.action}">${action.text}</button>`
                    ).join('')}
                </div>
            </div>
            <button class="notification-close">&times;</button>
        `;

        // 添加事件监听
        notificationElement.addEventListener('click', (event) => {
            if (event.target.dataset.action) {
                this.handleNotificationAction(notification, event.target.dataset.action);
                notificationElement.remove();
            } else if (event.target.classList.contains('notification-close')) {
                notificationElement.remove();
            }
        });

        // 添加到页面
        document.body.appendChild(notificationElement);

        // 自动移除
        setTimeout(() => {
            if (notificationElement.parentNode) {
                notificationElement.remove();
            }
        }, 10000);
    }

    handleNotificationAction(notification, action) {
        switch (action) {
            case 'resume_activity':
                this.presenceManager.updatePresenceTime();
                break;

            case 'enable_focus_mode':
                this.enableFocusMode();
                break;

            case 'take_break_5':
                this.startBreakTimer(5 * 60 * 1000);
                break;

            case 'take_break_15':
                this.startBreakTimer(15 * 60 * 1000);
                break;

            case 'snooze':
            case 'snooze_break':
                this.snoozeNotifications(notification.type, 10 * 60 * 1000);
                break;

            case 'dismiss':
                // 什么都不做，只是关闭通知
                break;
        }
    }

    enableFocusMode() {
        // 启用专注模式的逻辑
        console.log('专注模式已启用');

        // 可以实现：
        // - 屏蔽其他通知
        // - 启用网站屏蔽
        // - 播放专注音乐
        // - 设置专注计时器
    }

    startBreakTimer(duration) {
        console.log(`开始 ${duration / (60 * 1000)} 分钟休息`);

        setTimeout(() => {
            this.queueNotification({
                type: 'break_end',
                title: '休息结束',
                message: '休息时间结束，准备继续工作吧！',
                priority: 'medium',
                actions: [
                    { text: '开始工作', action: 'resume_work' },
                    { text: '再休息5分钟', action: 'extend_break' }
                ]
            });
        }, duration);
    }

    snoozeNotifications(type, duration) {
        // 暂停特定类型的通知
        const originalSetting = this.notificationSettings[`enable${type.charAt(0).toUpperCase() + type.slice(1)}Reminders`];

        if (originalSetting !== undefined) {
            this.notificationSettings[`enable${type.charAt(0).toUpperCase() + type.slice(1)}Reminders`] = false;

            setTimeout(() => {
                this.notificationSettings[`enable${type.charAt(0).toUpperCase() + type.slice(1)}Reminders`] = originalSetting;
            }, duration);
        }
    }

    updateSettings(newSettings) {
        Object.assign(this.notificationSettings, newSettings);
    }

    getSettings() {
        return { ...this.notificationSettings };
    }

    clearQueue() {
        this.notificationQueue = [];
    }
}
```

## 最佳实践

### 1. 服务初始化
```javascript
// ✅ 推荐：正确的服务初始化
const presenceService = {
    start(env) {
        const bus = new EventBus();
        // 初始化逻辑...
        return serviceInstance;
    }
};
```

### 2. 事件监听
```javascript
// ✅ 推荐：使用清理函数
const unsubscribe = presenceService.subscribe('presence', callback);

// 在组件卸载时清理
onWillUnmount(() => {
    unsubscribe();
});
```

### 3. 状态检查
```javascript
// ✅ 推荐：定期检查状态
setInterval(() => {
    const status = presenceService.getCurrentStatus();
    if (!status.isActive) {
        // 处理非活动状态
    }
}, 30000);
```

## 总结

Odoo 在线状态服务模块提供了完善的用户活动监控功能：

**核心优势**:
- **多维度检测**: 通过多种事件类型检测用户活动
- **跨标签页同步**: 通过localStorage实现状态同步
- **智能判断**: 基于时间和活动类型的智能状态判断
- **持久化存储**: 状态信息本地持久化存储
- **实时通知**: 状态变化的实时事件通知

**适用场景**:
- 用户在线状态显示
- 活动监控和分析
- 自动保存功能
- 会话管理
- 生产力分析

**设计优势**:
- 轻量级实现
- 事件驱动架构
- 跨标签页兼容
- 易于扩展

这个在线状态服务为 Odoo Web 客户端提供了重要的用户活动监控能力，是构建现代化Web应用的重要组件。

# Odoo 即时消息状态服务 (IM Status Service) 学习资料

## 文件概述

**文件路径**: `output/@bus/im_status_service.js`  
**原始路径**: `/bus/static/src/im_status_service.js`  
**模块类型**: 核心服务 - 即时消息状态管理  
**代码行数**: 74 行  
**依赖关系**: 
- `@web/core/browser/browser` - 浏览器工具
- `@web/core/registry` - 注册表系统
- `@web/core/user` - 用户信息
- `@web/session` - 会话管理

## 模块功能

即时消息状态服务模块是 Odoo Web 客户端的用户在线状态管理系统。该模块提供了：
- 用户在线状态同步
- 自动状态更新机制
- 跨设备状态协调
- 离开状态检测
- 总线状态通信
- 智能状态判断

这个服务确保用户的在线状态在服务器端保持最新，并能够响应其他设备或浏览器的状态变化。

## IM 状态服务架构

### 核心组件结构
```
IM Status Service
├── 状态管理
│   ├── 在线状态检测
│   ├── 离开状态判断
│   ├── 离线状态处理
│   └── 状态变化监听
├── 自动更新机制
│   ├── 定时状态更新
│   ├── 活动状态监听
│   ├── 离开超时处理
│   └── 连接状态响应
├── 跨设备协调
│   ├── 状态冲突检测
│   ├── 优先级判断
│   ├── 状态同步
│   └── 设备协调
└── 总线通信
    ├── 状态广播
    ├── 状态接收
    ├── 消息处理
    └── 频道订阅
```

### 状态常量
```javascript
const STATUS_CONSTANTS = {
    AWAY_DELAY: 30 * 60 * 1000,           // 30分钟离开延迟
    FIRST_UPDATE_DELAY: 500,              // 首次更新延迟: 500ms
    UPDATE_BUS_PRESENCE_DELAY: 60000      // 总线状态更新延迟: 60秒
};

const IM_STATUS_TYPES = {
    ONLINE: 'online',      // 在线
    AWAY: 'away',         // 离开
    OFFLINE: 'offline'    // 离线
};
```

### 服务配置
```javascript
const imStatusService = {
    dependencies: ["bus_service", "presence"],
    
    start(env, { bus_service, presence }) {
        // 服务实现...
    }
};
```

## 核心功能详解

### 1. IM 状态管理器
```javascript
// IM 状态管理器
class IMStatusManager {
    constructor(busService, presenceService, userService, sessionService) {
        this.busService = busService;
        this.presenceService = presenceService;
        this.userService = userService;
        this.sessionService = sessionService;
        
        // 状态配置
        this.AWAY_DELAY = 30 * 60 * 1000;           // 30分钟
        this.FIRST_UPDATE_DELAY = 500;              // 500ms
        this.UPDATE_BUS_PRESENCE_DELAY = 60000;     // 60秒
        
        // 状态变量
        this.lastSentInactivity = 0;
        this.becomeAwayTimeout = null;
        this.updateInterval = null;
        this.isInitialized = false;
        
        this.setupStatusManagement();
    }
    
    setupStatusManagement() {
        // 设置状态管理
        this.setupEventListeners();
        this.setupAutoUpdate();
        this.setupBusSubscriptions();
        
        this.isInitialized = true;
    }
    
    setupEventListeners() {
        // 监听总线连接事件
        this.busService.addEventListener('connect', () => {
            this.updateBusPresence();
        }, { once: true });
        
        // 监听用户活动事件
        this.presenceService.bus.addEventListener('presence', () => {
            this.handlePresenceChange();
        });
    }
    
    setupAutoUpdate() {
        // 设置自动更新机制
        this.updateInterval = setInterval(() => {
            this.checkAndUpdateStatus();
        }, this.UPDATE_BUS_PRESENCE_DELAY);
    }
    
    setupBusSubscriptions() {
        // 订阅总线状态更新
        this.busService.subscribe('bus.bus/im_status_updated', (data) => {
            this.handleStatusUpdate(data);
        });
    }
    
    updateBusPresence() {
        // 更新总线状态
        const inactivityPeriod = this.presenceService.getInactivityPeriod();
        
        console.log('更新总线状态:', {
            inactivityPeriod: inactivityPeriod,
            lastSent: this.lastSentInactivity,
            userId: this.userService.partnerId
        });
        
        this.lastSentInactivity = inactivityPeriod;
        this.startAwayTimeout();
        
        // 发送状态更新到服务器
        this.busService.send('update_presence', {
            inactivity_period: inactivityPeriod,
            im_status_ids_by_model: this.getStatusIdsByModel()
        });
    }
    
    getStatusIdsByModel() {
        // 获取按模型分组的状态ID
        return {
            'res.partner': [this.userService.partnerId],
            // 可以添加其他模型的状态ID
        };
    }
    
    startAwayTimeout() {
        // 开始离开超时计时
        clearTimeout(this.becomeAwayTimeout);
        
        const currentInactivity = this.presenceService.getInactivityPeriod();
        const awayTime = this.AWAY_DELAY - currentInactivity;
        
        if (awayTime > 0) {
            console.log(`${awayTime}ms 后将变为离开状态`);
            
            this.becomeAwayTimeout = setTimeout(() => {
                this.updateBusPresence();
            }, awayTime);
        } else {
            console.log('用户已处于离开状态');
        }
    }
    
    handlePresenceChange() {
        // 处理用户活动变化
        const currentInactivity = this.presenceService.getInactivityPeriod();
        
        console.log('用户活动变化:', {
            currentInactivity: currentInactivity,
            lastSent: this.lastSentInactivity,
            awayDelay: this.AWAY_DELAY
        });
        
        // 如果之前发送的状态是离开，但现在用户活跃，则更新状态
        if (this.lastSentInactivity >= this.AWAY_DELAY && currentInactivity < this.AWAY_DELAY) {
            this.updateBusPresence();
        }
        
        // 重新开始离开超时计时
        this.startAwayTimeout();
    }
    
    handleStatusUpdate(data) {
        // 处理状态更新消息
        const { partner_id, im_status } = data;
        
        console.log('收到状态更新:', data);
        
        // 检查是否是当前用户的状态更新
        if (this.sessionService.is_public || !partner_id || partner_id !== this.userService.partnerId) {
            return;
        }
        
        // 判断是否需要更新本地状态
        const currentInactivity = this.presenceService.getInactivityPeriod();
        const isLocallyOnline = currentInactivity < this.AWAY_DELAY;
        
        if (im_status === 'offline' || (im_status === 'away' && isLocallyOnline)) {
            console.log('检测到状态冲突，更新本地状态');
            this.updateBusPresence();
        }
    }
    
    checkAndUpdateStatus() {
        // 检查并更新状态
        const currentInactivity = this.presenceService.getInactivityPeriod();
        const timeSinceLastUpdate = Date.now() - this.lastUpdateTime;
        
        // 如果状态发生显著变化或距离上次更新时间过长，则更新状态
        if (this.shouldUpdateStatus(currentInactivity, timeSinceLastUpdate)) {
            this.updateBusPresence();
        }
    }
    
    shouldUpdateStatus(currentInactivity, timeSinceLastUpdate) {
        // 判断是否应该更新状态
        
        // 状态从活跃变为离开
        if (this.lastSentInactivity < this.AWAY_DELAY && currentInactivity >= this.AWAY_DELAY) {
            return true;
        }
        
        // 状态从离开变为活跃
        if (this.lastSentInactivity >= this.AWAY_DELAY && currentInactivity < this.AWAY_DELAY) {
            return true;
        }
        
        // 距离上次更新时间过长
        if (timeSinceLastUpdate > this.UPDATE_BUS_PRESENCE_DELAY * 2) {
            return true;
        }
        
        return false;
    }
    
    getCurrentStatus() {
        // 获取当前状态
        const inactivityPeriod = this.presenceService.getInactivityPeriod();
        
        if (inactivityPeriod < this.AWAY_DELAY) {
            return 'online';
        } else {
            return 'away';
        }
    }
    
    getStatusInfo() {
        // 获取状态信息
        return {
            currentStatus: this.getCurrentStatus(),
            inactivityPeriod: this.presenceService.getInactivityPeriod(),
            lastSentInactivity: this.lastSentInactivity,
            awayDelay: this.AWAY_DELAY,
            isAwayTimeoutActive: !!this.becomeAwayTimeout,
            lastUpdateTime: this.lastUpdateTime,
            userId: this.userService.partnerId
        };
    }
    
    forceUpdate() {
        // 强制更新状态
        console.log('强制更新IM状态');
        this.updateBusPresence();
    }
    
    setAwayDelay(delay) {
        // 设置离开延迟时间
        this.AWAY_DELAY = delay;
        console.log('离开延迟时间已设置为:', delay);
        
        // 重新开始超时计时
        this.startAwayTimeout();
    }
    
    cleanup() {
        // 清理资源
        clearTimeout(this.becomeAwayTimeout);
        clearInterval(this.updateInterval);
        
        this.becomeAwayTimeout = null;
        this.updateInterval = null;
        this.isInitialized = false;
        
        console.log('IM状态服务已清理');
    }
    
    getDebugInfo() {
        // 获取调试信息
        return {
            statusInfo: this.getStatusInfo(),
            configuration: {
                awayDelay: this.AWAY_DELAY,
                firstUpdateDelay: this.FIRST_UPDATE_DELAY,
                updateBusPresenceDelay: this.UPDATE_BUS_PRESENCE_DELAY
            },
            state: {
                isInitialized: this.isInitialized,
                hasAwayTimeout: !!this.becomeAwayTimeout,
                hasUpdateInterval: !!this.updateInterval
            },
            dependencies: {
                busService: !!this.busService,
                presenceService: !!this.presenceService,
                userService: !!this.userService,
                sessionService: !!this.sessionService
            }
        };
    }
}
```

### 2. 状态同步协调器
```javascript
// 状态同步协调器
class StatusSyncCoordinator {
    constructor(imStatusManager) {
        this.imStatusManager = imStatusManager;
        this.deviceStates = new Map();
        this.conflictResolutionRules = new Map();
        this.syncHistory = [];
        this.maxHistorySize = 100;
        this.setupCoordination();
    }
    
    setupCoordination() {
        // 设置协调机制
        this.setupConflictResolution();
        this.setupSyncMonitoring();
    }
    
    setupConflictResolution() {
        // 设置冲突解决规则
        
        // 在线状态优先级最高
        this.conflictResolutionRules.set('online_priority', (localStatus, remoteStatus) => {
            if (localStatus === 'online' || remoteStatus === 'online') {
                return 'online';
            }
            return localStatus;
        });
        
        // 最近活动优先
        this.conflictResolutionRules.set('recent_activity', (localStatus, remoteStatus, localTime, remoteTime) => {
            if (localTime > remoteTime) {
                return localStatus;
            }
            return remoteStatus;
        });
        
        // 设备优先级
        this.conflictResolutionRules.set('device_priority', (localStatus, remoteStatus, localDevice, remoteDevice) => {
            const devicePriority = {
                'desktop': 3,
                'tablet': 2,
                'mobile': 1
            };
            
            const localPriority = devicePriority[localDevice] || 0;
            const remotePriority = devicePriority[remoteDevice] || 0;
            
            if (localPriority > remotePriority) {
                return localStatus;
            }
            return remoteStatus;
        });
    }
    
    setupSyncMonitoring() {
        // 设置同步监控
        setInterval(() => {
            this.monitorSyncHealth();
        }, 30000); // 每30秒检查一次
    }
    
    handleStatusConflict(localStatus, remoteStatus, metadata = {}) {
        // 处理状态冲突
        console.log('检测到状态冲突:', {
            local: localStatus,
            remote: remoteStatus,
            metadata: metadata
        });
        
        // 应用冲突解决规则
        let resolvedStatus = localStatus;
        
        // 在线状态优先
        if (this.conflictResolutionRules.has('online_priority')) {
            const rule = this.conflictResolutionRules.get('online_priority');
            resolvedStatus = rule(localStatus, remoteStatus);
        }
        
        // 记录冲突解决历史
        this.recordSyncEvent('conflict_resolved', {
            localStatus: localStatus,
            remoteStatus: remoteStatus,
            resolvedStatus: resolvedStatus,
            metadata: metadata
        });
        
        return resolvedStatus;
    }
    
    recordSyncEvent(eventType, data) {
        // 记录同步事件
        const event = {
            type: eventType,
            data: data,
            timestamp: Date.now(),
            id: this.generateEventId()
        };
        
        this.syncHistory.push(event);
        
        // 限制历史记录大小
        if (this.syncHistory.length > this.maxHistorySize) {
            this.syncHistory.shift();
        }
        
        console.log('同步事件记录:', event);
    }
    
    generateEventId() {
        // 生成事件ID
        return `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    monitorSyncHealth() {
        // 监控同步健康状态
        const recentEvents = this.getRecentSyncEvents(5 * 60 * 1000); // 最近5分钟
        const conflictCount = recentEvents.filter(e => e.type === 'conflict_resolved').length;
        
        if (conflictCount > 10) {
            console.warn('检测到频繁的状态冲突，可能存在同步问题');
            this.handleSyncIssue('frequent_conflicts', { conflictCount });
        }
        
        // 检查同步延迟
        const lastSyncTime = this.getLastSyncTime();
        const syncDelay = Date.now() - lastSyncTime;
        
        if (syncDelay > 2 * 60 * 1000) { // 超过2分钟
            console.warn('同步延迟过高:', syncDelay);
            this.handleSyncIssue('high_latency', { syncDelay });
        }
    }
    
    getRecentSyncEvents(timeWindow) {
        // 获取最近的同步事件
        const cutoffTime = Date.now() - timeWindow;
        return this.syncHistory.filter(event => event.timestamp > cutoffTime);
    }
    
    getLastSyncTime() {
        // 获取最后同步时间
        if (this.syncHistory.length === 0) {
            return Date.now();
        }
        
        return this.syncHistory[this.syncHistory.length - 1].timestamp;
    }
    
    handleSyncIssue(issueType, data) {
        // 处理同步问题
        console.log('处理同步问题:', issueType, data);
        
        switch (issueType) {
            case 'frequent_conflicts':
                this.handleFrequentConflicts(data);
                break;
                
            case 'high_latency':
                this.handleHighLatency(data);
                break;
                
            default:
                console.warn('未知的同步问题类型:', issueType);
        }
    }
    
    handleFrequentConflicts(data) {
        // 处理频繁冲突
        console.log('处理频繁冲突:', data);
        
        // 可能的解决方案：
        // 1. 增加状态更新间隔
        // 2. 调整冲突解决策略
        // 3. 通知用户
    }
    
    handleHighLatency(data) {
        // 处理高延迟
        console.log('处理高延迟:', data);
        
        // 可能的解决方案：
        // 1. 强制同步
        // 2. 检查网络连接
        // 3. 重新建立连接
        this.imStatusManager.forceUpdate();
    }
    
    getSyncStatistics() {
        // 获取同步统计信息
        const totalEvents = this.syncHistory.length;
        const conflictEvents = this.syncHistory.filter(e => e.type === 'conflict_resolved').length;
        const recentEvents = this.getRecentSyncEvents(60 * 60 * 1000); // 最近1小时
        
        return {
            totalEvents: totalEvents,
            conflictEvents: conflictEvents,
            conflictRate: totalEvents > 0 ? (conflictEvents / totalEvents * 100).toFixed(2) + '%' : '0%',
            recentEventCount: recentEvents.length,
            lastSyncTime: this.getLastSyncTime(),
            syncHealth: this.calculateSyncHealth()
        };
    }
    
    calculateSyncHealth() {
        // 计算同步健康度
        const recentEvents = this.getRecentSyncEvents(30 * 60 * 1000); // 最近30分钟
        const conflictCount = recentEvents.filter(e => e.type === 'conflict_resolved').length;
        const syncDelay = Date.now() - this.getLastSyncTime();
        
        let health = 100;
        
        // 冲突频率影响
        if (conflictCount > 5) {
            health -= conflictCount * 2;
        }
        
        // 同步延迟影响
        if (syncDelay > 60 * 1000) { // 超过1分钟
            health -= Math.min(30, syncDelay / (60 * 1000) * 5);
        }
        
        return Math.max(0, Math.min(100, health));
    }
    
    clearSyncHistory() {
        // 清空同步历史
        this.syncHistory = [];
        console.log('同步历史已清空');
    }
    
    exportSyncData() {
        // 导出同步数据
        return {
            statistics: this.getSyncStatistics(),
            history: this.syncHistory,
            rules: Array.from(this.conflictResolutionRules.keys()),
            exportedAt: Date.now()
        };
    }
}
```

## 使用示例

### 1. 基本 IM 状态服务使用
```javascript
// 在组件中使用 IM 状态服务
class IMStatusComponent extends Component {
    static template = xml`
        <div class="im-status">
            <div class="status-indicator" t-att-class="statusClass">
                <span t-esc="statusText" />
            </div>
            
            <div class="status-info">
                <div>当前状态: <span t-esc="currentStatus" /></div>
                <div>非活跃时间: <span t-esc="inactivityText" /></div>
                <div>最后更新: <span t-esc="lastUpdateText" /></div>
            </div>
            
            <div class="status-actions">
                <button t-on-click="forceUpdate" class="btn btn-sm btn-primary">
                    强制更新
                </button>
                <button t-on-click="setAway" class="btn btn-sm btn-warning">
                    设为离开
                </button>
                <button t-on-click="setOnline" class="btn btn-sm btn-success">
                    设为在线
                </button>
            </div>
        </div>
    `;
    
    setup() {
        this.imStatus = useService('im_status');
        this.statusInfo = useState(this.getStatusInfo());
        
        // 定期更新状态显示
        this.updateInterval = setInterval(() => {
            this.updateStatusInfo();
        }, 1000);
        
        onWillUnmount(() => {
            clearInterval(this.updateInterval);
        });
    }
    
    getStatusInfo() {
        const info = this.imStatus.getStatusInfo();
        
        return {
            currentStatus: info.currentStatus,
            inactivityPeriod: info.inactivityPeriod,
            lastUpdateTime: info.lastUpdateTime || Date.now()
        };
    }
    
    updateStatusInfo() {
        Object.assign(this.statusInfo, this.getStatusInfo());
    }
    
    forceUpdate() {
        this.imStatus.forceUpdate();
        this.updateStatusInfo();
    }
    
    setAway() {
        // 模拟设置离开状态
        this.imStatus.setAwayDelay(0); // 立即变为离开
        setTimeout(() => {
            this.imStatus.setAwayDelay(30 * 60 * 1000); // 恢复默认
        }, 5000);
    }
    
    setOnline() {
        // 模拟用户活动，变为在线状态
        this.imStatus.forceUpdate();
    }
    
    get currentStatus() {
        return this.statusInfo.currentStatus;
    }
    
    get statusText() {
        const statusMap = {
            'online': '在线',
            'away': '离开',
            'offline': '离线'
        };
        return statusMap[this.currentStatus] || '未知';
    }
    
    get statusClass() {
        return `status-${this.currentStatus}`;
    }
    
    get inactivityText() {
        const minutes = Math.floor(this.statusInfo.inactivityPeriod / (60 * 1000));
        return `${minutes} 分钟`;
    }
    
    get lastUpdateText() {
        const diff = Date.now() - this.statusInfo.lastUpdateTime;
        const seconds = Math.floor(diff / 1000);
        return `${seconds} 秒前`;
    }
}
```

## IM 状态服务特性

### 1. 智能状态检测
IM 状态服务通过多种机制检测用户状态：
- **活动监听**: 监听用户的活动状态变化
- **时间阈值**: 基于 30 分钟的离开时间阈值
- **自动更新**: 定期自动更新状态到服务器
- **冲突解决**: 处理多设备间的状态冲突

### 2. 跨设备协调
```javascript
// 状态协调示例
const statusCoordination = {
    localStatus: 'online',      // 本地状态
    remoteStatus: 'away',       // 远程状态
    resolution: 'online',       // 解决结果：在线状态优先
    
    conflictRules: [
        'online_priority',      // 在线状态优先
        'recent_activity',      // 最近活动优先
        'device_priority'       // 设备优先级
    ]
};
```

### 3. 自动化机制
- **连接响应**: 总线连接建立时自动更新状态
- **定时更新**: 定期向服务器同步状态
- **活动触发**: 用户活动时自动更新状态
- **离开检测**: 自动检测用户离开状态

### 4. 状态常量
```javascript
const STATUS_CONFIG = {
    AWAY_DELAY: 30 * 60 * 1000,           // 30分钟离开延迟
    FIRST_UPDATE_DELAY: 500,              // 首次更新延迟
    UPDATE_BUS_PRESENCE_DELAY: 60000      // 总线更新延迟
};
```

## 最佳实践

### 1. 服务依赖
```javascript
// ✅ 推荐：正确的服务依赖配置
const imStatusService = {
    dependencies: ["bus_service", "presence"]
};
```

### 2. 状态监听
```javascript
// ✅ 推荐：监听状态变化
imStatus.addEventListener('status_changed', (status) => {
    updateUI(status);
});
```

### 3. 错误处理
```javascript
// ✅ 推荐：处理状态更新错误
try {
    imStatus.updateBusPresence();
} catch (error) {
    console.error('状态更新失败:', error);
}
```

## 总结

Odoo IM 状态服务模块提供了完善的即时消息状态管理功能：

**核心优势**:
- **智能检测**: 基于用户活动的智能状态检测
- **自动同步**: 自动化的状态同步和更新机制
- **跨设备协调**: 完善的多设备状态协调
- **冲突解决**: 智能的状态冲突解决策略
- **实时响应**: 实时的状态变化响应

**适用场景**:
- 即时消息系统
- 用户在线状态显示
- 协作应用
- 状态监控
- 多设备同步

**设计优势**:
- 事件驱动架构
- 自动化管理
- 智能冲突解决
- 高度可配置

这个 IM 状态服务为 Odoo Web 客户端提供了重要的用户状态管理能力，确保了即时消息系统的准确性和可靠性。

/*************************************************************
*  Filepath: /bus/static/src/simple_notification_service.js  *
*  Lines: 21                                                 *
*************************************************************/
odoo.define('@bus/simple_notification_service', ['@web/core/registry'], function (require) {
'use strict';
let __exports = {};
/* @odoo-module */

const { registry } = require("@web/core/registry");

const simpleNotificationService = __exports.simpleNotificationService = {
    dependencies: ["bus_service", "notification"],
    start(env, { bus_service, notification: notificationService }) {
        bus_service.subscribe("simple_notification", ({ message, sticky, title, type }) => {
            notificationService.add(message, { sticky, title, type });
        });
        bus_service.start();
    },
};

registry.category("services").add("simple_notification", simpleNotificationService);

return __exports;
});
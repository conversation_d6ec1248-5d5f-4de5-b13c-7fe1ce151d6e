# Odoo WebSocket Worker 工具函数 (WebSocket Worker Utils) 学习资料

## 文件概述

**文件路径**: `output/@bus/workers/websocket_worker_utils.js`  
**原始路径**: `/bus/static/src/workers/websocket_worker_utils.js`  
**模块类型**: 工具函数库 - WebSocket Worker 辅助工具  
**代码行数**: 54 行  
**依赖关系**: 无外部依赖  

## 模块功能

WebSocket Worker 工具函数模块是 Odoo Web 客户端 WebSocket Worker 的核心工具库。该模块提供了：
- 防抖函数 (debounce) 实现
- 延迟 Promise (Deferred) 类
- 异步操作控制工具
- 函数执行优化工具
- Worker 环境下的通用工具

这个模块为 WebSocket Worker 提供了基础的工具函数，确保了 Worker 环境下的高效异步操作和函数执行控制。

## 工具函数架构

### 核心组件结构
```
WebSocket Worker Utils
├── 防抖函数 (debounce)
│   ├── 函数执行延迟
│   ├── 重复调用防护
│   ├── 立即执行模式
│   └── 上下文保持
├── 延迟 Promise (Deferred)
│   ├── Promise 扩展
│   ├── 外部控制解析
│   ├── 外部控制拒绝
│   └── 异步流程控制
└── 工具函数特性
    ├── 轻量级实现
    ├── 高性能优化
    ├── 兼容性保证
    └── 易于使用
```

### 模块导出
```javascript
const exports = {
    debounce: debounce,    // 防抖函数
    Deferred: Deferred     // 延迟 Promise 类
};
```

## 核心功能详解

### 1. 防抖函数 (debounce)
```javascript
/**
 * 防抖函数实现
 * 在连续调用时，只有在停止调用 N 毫秒后才会执行
 * 
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @param {boolean} immediate - 是否立即执行
 * @returns {Function} 防抖后的函数
 */
function debounce(func, wait, immediate) {
    let timeout;
    
    return function() {
        const context = this;
        const args = arguments;
        
        function later() {
            timeout = null;
            if (!immediate) {
                func.apply(context, args);
            }
        }
        
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        
        if (callNow) {
            func.apply(context, args);
        }
    };
}
```

**功能特性**:
- **延迟执行**: 在指定时间内没有新调用时才执行
- **重复调用防护**: 防止函数被频繁调用
- **立即执行模式**: 支持首次调用立即执行
- **上下文保持**: 保持原函数的 this 上下文和参数
- **内存优化**: 自动清理定时器避免内存泄漏

### 2. 延迟 Promise 类 (Deferred)
```javascript
/**
 * 延迟 Promise 类
 * 扩展 Promise，允许外部控制解析和拒绝
 */
class Deferred extends Promise {
    constructor() {
        let resolve;
        let reject;
        
        const prom = new Promise((res, rej) => {
            resolve = res;
            reject = rej;
        });
        
        return Object.assign(prom, { resolve, reject });
    }
}
```

**功能特性**:
- **Promise 扩展**: 继承 Promise 的所有功能
- **外部控制**: 允许外部代码控制 Promise 的状态
- **灵活解析**: 可以在任意时机解析或拒绝
- **状态管理**: 完整的 Promise 状态管理
- **链式调用**: 支持 Promise 的链式调用

## 使用示例

### 1. 防抖函数使用
```javascript
// 基本防抖使用
class SearchComponent {
    constructor() {
        // 创建防抖搜索函数，300ms 延迟
        this.debouncedSearch = debounce(this.performSearch.bind(this), 300);
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        const searchInput = document.getElementById('search-input');
        
        // 用户输入时调用防抖搜索
        searchInput.addEventListener('input', (event) => {
            this.debouncedSearch(event.target.value);
        });
    }
    
    performSearch(query) {
        console.log('执行搜索:', query);
        
        // 实际的搜索逻辑
        this.searchAPI(query).then(results => {
            this.displayResults(results);
        });
    }
    
    async searchAPI(query) {
        // 模拟 API 调用
        const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
        return response.json();
    }
    
    displayResults(results) {
        // 显示搜索结果
        console.log('搜索结果:', results);
    }
}

// 立即执行模式的防抖
class ButtonClickHandler {
    constructor() {
        // 立即执行模式：首次点击立即执行，后续点击防抖
        this.debouncedClick = debounce(this.handleClick.bind(this), 1000, true);
    }
    
    handleClick() {
        console.log('按钮被点击');
        
        // 执行按钮点击逻辑
        this.performAction();
    }
    
    performAction() {
        // 实际的操作逻辑
        console.log('执行操作');
    }
}

// 在 WebSocket Worker 中使用防抖
class WebSocketManager {
    constructor() {
        this.websocket = null;
        this.messageQueue = [];
        
        // 防抖发送消息，避免频繁发送
        this.debouncedSendMessages = debounce(this.sendQueuedMessages.bind(this), 100);
        
        // 防抖重连，避免频繁重连
        this.debouncedReconnect = debounce(this.reconnect.bind(this), 1000);
    }
    
    queueMessage(message) {
        this.messageQueue.push(message);
        
        // 触发防抖发送
        this.debouncedSendMessages();
    }
    
    sendQueuedMessages() {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            while (this.messageQueue.length > 0) {
                const message = this.messageQueue.shift();
                this.websocket.send(JSON.stringify(message));
            }
        }
    }
    
    onConnectionError() {
        console.log('连接错误，准备重连');
        
        // 触发防抖重连
        this.debouncedReconnect();
    }
    
    reconnect() {
        console.log('执行重连');
        
        // 重连逻辑
        this.connect();
    }
    
    connect() {
        // WebSocket 连接逻辑
        this.websocket = new WebSocket('ws://localhost:8080');
        
        this.websocket.onerror = () => {
            this.onConnectionError();
        };
    }
}
```

### 2. Deferred 类使用
```javascript
// 基本 Deferred 使用
class AsyncOperationManager {
    constructor() {
        this.operations = new Map();
        this.operationId = 0;
    }
    
    startOperation(name) {
        const id = ++this.operationId;
        const deferred = new Deferred();
        
        this.operations.set(id, {
            name: name,
            deferred: deferred,
            startTime: Date.now()
        });
        
        console.log(`开始操作: ${name} (ID: ${id})`);
        
        // 返回操作 ID 和 Promise
        return {
            id: id,
            promise: deferred
        };
    }
    
    completeOperation(id, result) {
        const operation = this.operations.get(id);
        
        if (operation) {
            const duration = Date.now() - operation.startTime;
            console.log(`完成操作: ${operation.name} (耗时: ${duration}ms)`);
            
            // 解析 Deferred
            operation.deferred.resolve(result);
            this.operations.delete(id);
        }
    }
    
    failOperation(id, error) {
        const operation = this.operations.get(id);
        
        if (operation) {
            console.log(`操作失败: ${operation.name}`, error);
            
            // 拒绝 Deferred
            operation.deferred.reject(error);
            this.operations.delete(id);
        }
    }
    
    async waitForOperation(name) {
        const { id, promise } = this.startOperation(name);
        
        // 模拟异步操作
        setTimeout(() => {
            if (Math.random() > 0.2) {
                this.completeOperation(id, `${name} 的结果`);
            } else {
                this.failOperation(id, new Error(`${name} 失败`));
            }
        }, Math.random() * 2000 + 1000);
        
        return promise;
    }
}

// 在 WebSocket Worker 中使用 Deferred
class WebSocketWorkerManager {
    constructor() {
        this.connectionDeferred = new Deferred();
        this.initializationDeferred = new Deferred();
        this.pendingRequests = new Map();
        this.requestId = 0;
    }
    
    async initialize() {
        console.log('开始初始化 WebSocket Worker');
        
        try {
            // 等待连接建立
            await this.connectionDeferred;
            console.log('WebSocket 连接已建立');
            
            // 执行初始化逻辑
            await this.performInitialization();
            
            // 标记初始化完成
            this.initializationDeferred.resolve();
            console.log('WebSocket Worker 初始化完成');
            
        } catch (error) {
            console.error('初始化失败:', error);
            this.initializationDeferred.reject(error);
        }
    }
    
    onWebSocketOpen() {
        console.log('WebSocket 连接打开');
        
        // 解析连接 Deferred
        this.connectionDeferred.resolve();
    }
    
    onWebSocketError(error) {
        console.error('WebSocket 连接错误:', error);
        
        // 拒绝连接 Deferred
        this.connectionDeferred.reject(error);
    }
    
    async performInitialization() {
        // 模拟初始化过程
        return new Promise(resolve => {
            setTimeout(resolve, 1000);
        });
    }
    
    async sendRequest(data) {
        // 等待初始化完成
        await this.initializationDeferred;
        
        const requestId = ++this.requestId;
        const deferred = new Deferred();
        
        this.pendingRequests.set(requestId, deferred);
        
        // 发送请求
        this.sendMessage({
            id: requestId,
            data: data
        });
        
        return deferred;
    }
    
    onMessageReceived(message) {
        const { id, result, error } = message;
        const deferred = this.pendingRequests.get(id);
        
        if (deferred) {
            if (error) {
                deferred.reject(new Error(error));
            } else {
                deferred.resolve(result);
            }
            
            this.pendingRequests.delete(id);
        }
    }
    
    sendMessage(message) {
        // 实际发送消息的逻辑
        console.log('发送消息:', message);
    }
}

// 复杂的异步流程控制
class ComplexAsyncFlow {
    constructor() {
        this.steps = [];
        this.currentStep = 0;
        this.stepDeferreds = [];
    }
    
    addStep(name, asyncFunction) {
        this.steps.push({
            name: name,
            function: asyncFunction
        });
        
        this.stepDeferreds.push(new Deferred());
    }
    
    async executeFlow() {
        console.log('开始执行异步流程');
        
        for (let i = 0; i < this.steps.length; i++) {
            const step = this.steps[i];
            const deferred = this.stepDeferreds[i];
            
            try {
                console.log(`执行步骤 ${i + 1}: ${step.name}`);
                
                const result = await step.function();
                deferred.resolve(result);
                
                console.log(`步骤 ${i + 1} 完成:`, result);
                
            } catch (error) {
                console.error(`步骤 ${i + 1} 失败:`, error);
                deferred.reject(error);
                throw error;
            }
        }
        
        console.log('异步流程执行完成');
    }
    
    async waitForStep(stepIndex) {
        if (stepIndex < this.stepDeferreds.length) {
            return this.stepDeferreds[stepIndex];
        }
        
        throw new Error('步骤索引超出范围');
    }
    
    async waitForAllSteps() {
        return Promise.all(this.stepDeferreds);
    }
}

// 使用示例
async function demonstrateComplexFlow() {
    const flow = new ComplexAsyncFlow();
    
    // 添加异步步骤
    flow.addStep('数据加载', async () => {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { data: 'loaded' };
    });
    
    flow.addStep('数据处理', async () => {
        await new Promise(resolve => setTimeout(resolve, 500));
        return { processed: true };
    });
    
    flow.addStep('数据保存', async () => {
        await new Promise(resolve => setTimeout(resolve, 800));
        return { saved: true };
    });
    
    try {
        // 开始执行流程
        const flowPromise = flow.executeFlow();
        
        // 可以等待特定步骤完成
        const step1Result = await flow.waitForStep(0);
        console.log('第一步完成:', step1Result);
        
        // 等待整个流程完成
        await flowPromise;
        
        // 获取所有步骤的结果
        const allResults = await flow.waitForAllSteps();
        console.log('所有步骤结果:', allResults);
        
    } catch (error) {
        console.error('流程执行失败:', error);
    }
}
```

### 3. 高级应用模式
```javascript
// 防抖和 Deferred 结合使用
class SmartDataSynchronizer {
    constructor() {
        this.pendingSync = null;
        this.syncQueue = [];
        this.isOnline = navigator.onLine;
        
        // 防抖同步函数
        this.debouncedSync = debounce(this.performSync.bind(this), 1000);
        
        // 监听网络状态
        this.setupNetworkListeners();
    }
    
    setupNetworkListeners() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            console.log('网络已连接，开始同步');
            this.debouncedSync();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
            console.log('网络已断开');
        });
    }
    
    async syncData(data) {
        // 添加到同步队列
        this.syncQueue.push(data);
        
        if (!this.isOnline) {
            console.log('离线状态，数据已加入队列');
            return Promise.resolve({ queued: true });
        }
        
        // 如果已有待处理的同步，返回现有的 Promise
        if (this.pendingSync) {
            return this.pendingSync;
        }
        
        // 创建新的同步 Deferred
        this.pendingSync = new Deferred();
        
        // 触发防抖同步
        this.debouncedSync();
        
        return this.pendingSync;
    }
    
    async performSync() {
        if (!this.isOnline || this.syncQueue.length === 0) {
            return;
        }
        
        const dataToSync = [...this.syncQueue];
        this.syncQueue = [];
        
        try {
            console.log('开始同步数据:', dataToSync);
            
            // 模拟网络请求
            const result = await this.sendToServer(dataToSync);
            
            console.log('同步成功:', result);
            
            // 解析待处理的同步
            if (this.pendingSync) {
                this.pendingSync.resolve(result);
                this.pendingSync = null;
            }
            
        } catch (error) {
            console.error('同步失败:', error);
            
            // 将数据重新加入队列
            this.syncQueue.unshift(...dataToSync);
            
            // 拒绝待处理的同步
            if (this.pendingSync) {
                this.pendingSync.reject(error);
                this.pendingSync = null;
            }
        }
    }
    
    async sendToServer(data) {
        // 模拟网络请求
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                if (Math.random() > 0.1) {
                    resolve({ success: true, synced: data.length });
                } else {
                    reject(new Error('网络错误'));
                }
            }, 1000 + Math.random() * 2000);
        });
    }
    
    getQueueStatus() {
        return {
            queueLength: this.syncQueue.length,
            isOnline: this.isOnline,
            hasPendingSync: !!this.pendingSync
        };
    }
}

// 使用智能数据同步器
async function demonstrateSmartSync() {
    const synchronizer = new SmartDataSynchronizer();
    
    try {
        // 快速连续添加数据
        const promises = [];
        for (let i = 0; i < 5; i++) {
            promises.push(synchronizer.syncData({ id: i, value: `data_${i}` }));
        }
        
        // 等待所有同步完成
        const results = await Promise.all(promises);
        console.log('所有同步完成:', results);
        
    } catch (error) {
        console.error('同步过程中出错:', error);
    }
    
    // 检查队列状态
    console.log('队列状态:', synchronizer.getQueueStatus());
}
```

## 工具函数特性

### 1. 防抖函数特性
- **性能优化**: 减少函数执行频率，提升性能
- **用户体验**: 避免频繁操作导致的界面卡顿
- **资源节约**: 减少不必要的网络请求和计算
- **灵活配置**: 支持延迟时间和执行模式配置

### 2. Deferred 类特性
- **异步控制**: 提供更灵活的异步操作控制
- **状态管理**: 完整的 Promise 状态管理
- **外部控制**: 允许外部代码控制 Promise 状态
- **兼容性**: 完全兼容 Promise API

### 3. 设计优势
- **轻量级**: 最小化的代码实现
- **高性能**: 优化的执行效率
- **易用性**: 简洁的 API 接口
- **可靠性**: 经过验证的实现方案

## 最佳实践

### 1. 防抖函数使用
```javascript
// ✅ 推荐：合理的延迟时间
const debouncedSearch = debounce(search, 300); // 300ms 适合搜索

// ✅ 推荐：保存防抖函数引用
this.debouncedHandler = debounce(this.handler.bind(this), 500);

// ❌ 避免：每次都创建新的防抖函数
element.addEventListener('input', debounce(handler, 300)); // 错误
```

### 2. Deferred 使用
```javascript
// ✅ 推荐：适当的错误处理
const deferred = new Deferred();
try {
    const result = await someAsyncOperation();
    deferred.resolve(result);
} catch (error) {
    deferred.reject(error);
}

// ✅ 推荐：及时清理引用
this.pendingOperations.delete(operationId);
```

### 3. 内存管理
```javascript
// ✅ 推荐：清理定时器和引用
class ComponentWithDebounce {
    constructor() {
        this.debouncedMethod = debounce(this.method.bind(this), 300);
    }
    
    destroy() {
        // 清理防抖函数（如果有清理方法）
        this.debouncedMethod = null;
    }
}
```

## 总结

Odoo WebSocket Worker 工具函数模块提供了高效的异步操作工具：

**核心优势**:
- **性能优化**: 防抖函数有效减少不必要的函数执行
- **异步控制**: Deferred 类提供灵活的异步操作控制
- **轻量级**: 最小化的代码实现，无外部依赖
- **高可靠**: 经过验证的实现方案和错误处理
- **易于使用**: 简洁的 API 接口和清晰的使用方式

**适用场景**:
- WebSocket Worker 环境
- 频繁触发的事件处理
- 异步操作流程控制
- 性能优化需求
- 用户交互优化

**设计优势**:
- 函数式编程
- 最小化依赖
- 高度可复用
- 性能优先

这个工具函数模块为 Odoo WebSocket Worker 提供了重要的基础工具，确保了 Worker 环境下的高效异步操作和优秀的用户体验。

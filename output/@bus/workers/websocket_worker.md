# Odoo WebSocket Worker (WebSocket Worker) 学习资料

## 文件概述

**文件路径**: `output/@bus/workers/websocket_worker.js`  
**原始路径**: `/bus/static/src/workers/websocket_worker.js`  
**模块类型**: 核心 Worker - WebSocket 连接管理  
**代码行数**: 534 行  
**依赖关系**: 
- `@bus/workers/websocket_worker_utils` - Worker 工具函数

## 模块功能

WebSocket Worker 模块是 Odoo Web 客户端的核心 WebSocket 连接管理器。该模块提供了：
- WebSocket 连接生命周期管理
- 多客户端连接协调
- 自动重连机制
- 消息队列和分发
- 频道订阅管理
- 连接状态监控
- 错误处理和恢复

这个 Worker 运行在独立的线程中，为多个标签页提供统一的 WebSocket 连接服务，实现了高效的资源共享和连接管理。

## WebSocket Worker 架构

### 核心组件结构
```
WebSocket Worker
├── 连接管理
│   ├── WebSocket 连接建立
│   ├── 连接状态监控
│   ├── 自动重连机制
│   └── 连接生命周期管理
├── 客户端管理
│   ├── 多客户端注册
│   ├── 消息广播
│   ├── 频道订阅管理
│   └── 客户端状态跟踪
├── 消息处理
│   ├── 消息队列管理
│   ├── 消息分发路由
│   ├── 防抖处理
│   └── 错误处理
├── 频道管理
│   ├── 频道订阅
│   ├── 频道取消订阅
│   ├── 频道更新
│   └── 频道同步
└── 状态管理
    ├── Worker 状态
    ├── 连接状态
    ├── 重连状态
    └── 调试状态
```

### 状态定义
```javascript
// WebSocket 关闭代码
const WEBSOCKET_CLOSE_CODES = {
    CLEAN: 1000,                    // 正常关闭
    GOING_AWAY: 1001,              // 页面离开
    PROTOCOL_ERROR: 1002,          // 协议错误
    INCORRECT_DATA: 1003,          // 数据错误
    ABNORMAL_CLOSURE: 1006,        // 异常关闭
    INCONSISTENT_DATA: 1007,       // 数据不一致
    MESSAGE_VIOLATING_POLICY: 1008, // 违反策略
    MESSAGE_TOO_BIG: 1009,         // 消息过大
    EXTENSION_NEGOTIATION_FAILED: 1010, // 扩展协商失败
    SERVER_ERROR: 1011,            // 服务器错误
    RESTART: 1012,                 // 重启
    TRY_LATER: 1013,              // 稍后重试
    BAD_GATEWAY: 1014,            // 网关错误
    SESSION_EXPIRED: 4001,         // 会话过期
    KEEP_ALIVE_TIMEOUT: 4002,      // 保活超时
    RECONNECTING: 4003             // 重连中
};

// Worker 状态
const WORKER_STATE = {
    CONNECTED: "CONNECTED",        // 已连接
    DISCONNECTED: "DISCONNECTED",  // 已断开
    IDLE: "IDLE",                 // 空闲
    CONNECTING: "CONNECTING"       // 连接中
};
```

### Worker 事件类型
```javascript
/**
 * Worker 事件类型
 * @typedef {'connect' | 'reconnect' | 'disconnect' | 'reconnecting' | 'notification' | 'initialized' | 'outdated' | 'update_state'} WorkerEvent
 */

/**
 * Worker 操作类型
 * @typedef {'add_channel' | 'delete_channel' | 'force_update_channels' | 'initialize_connection' | 'send' | 'leave' | 'stop' | 'start'} WorkerAction
 */
```

## 核心功能详解

### 1. WebSocket Worker 类
```javascript
class WebsocketWorker {
    constructor() {
        // 连接配置
        this.INITIAL_RECONNECT_DELAY = 1000;  // 初始重连延迟
        this.RECONNECT_JITTER = 1000;         // 重连抖动
        this.MAXIMUM_RECONNECT_DELAY = 60000; // 最大重连延迟
        
        // 状态管理
        this.newestStartTs = undefined;       // 最新启动时间戳
        this.websocketURL = "";               // WebSocket URL
        this.currentUID = null;               // 当前用户ID
        this.currentDB = null;                // 当前数据库
        this.isWaitingForNewUID = true;       // 等待新用户ID
        
        // 客户端管理
        this.channelsByClient = new Map();    // 客户端频道映射
        this.debugModeByClient = new Map();   // 客户端调试模式
        
        // 连接管理
        this.connectRetryDelay = this.INITIAL_RECONNECT_DELAY;
        this.connectTimeout = null;
        this.state = WORKER_STATE.IDLE;
        this.isReconnecting = false;
        
        // 消息管理
        this.lastChannelSubscription = null;
        this.firstSubscribeDeferred = new Deferred();
        this.lastNotificationId = 0;
        this.messageWaitQueue = [];
        
        // 防抖函数
        this._forceUpdateChannels = debounce(this._forceUpdateChannels, 300);
        this._debouncedUpdateChannels = debounce(this._updateChannels, 300);
        this._debouncedSendToServer = debounce(this._sendToServer, 300);
        
        // 绑定事件处理器
        this._onWebsocketClose = this._onWebsocketClose.bind(this);
        this._onWebsocketError = this._onWebsocketError.bind(this);
        this._onWebsocketMessage = this._onWebsocketMessage.bind(this);
        this._onWebsocketOpen = this._onWebsocketOpen.bind(this);
    }
    
    // 公共方法
    
    /**
     * 广播消息到所有客户端
     * @param {WorkerEvent} type - 事件类型
     * @param {Object} data - 数据
     */
    broadcast(type, data) {
        for (const client of this.channelsByClient.keys()) {
            client.postMessage({ 
                type, 
                data: data ? JSON.parse(JSON.stringify(data)) : undefined 
            });
        }
    }
    
    /**
     * 注册客户端
     * @param {MessagePort} messagePort - 消息端口
     */
    registerClient(messagePort) {
        messagePort.onmessage = (ev) => {
            this._onClientMessage(messagePort, ev.data);
        };
        this.channelsByClient.set(messagePort, []);
    }
    
    /**
     * 发送消息到指定客户端
     * @param {MessagePort} client - 客户端
     * @param {WorkerEvent} type - 事件类型
     * @param {Object} data - 数据
     */
    sendToClient(client, type, data) {
        client.postMessage({ 
            type, 
            data: data ? JSON.parse(JSON.stringify(data)) : undefined 
        });
    }
}
```

### 2. 连接管理系统
```javascript
// 连接管理器
class WebSocketConnectionManager {
    constructor(worker) {
        this.worker = worker;
        this.websocket = null;
        this.connectionAttempts = 0;
        this.maxConnectionAttempts = 10;
        this.setupConnectionHandling();
    }
    
    setupConnectionHandling() {
        // 设置连接事件处理
        this.setupEventHandlers();
    }
    
    async connect(websocketURL, options = {}) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            console.log('WebSocket 已连接');
            return;
        }
        
        try {
            console.log('开始连接 WebSocket:', websocketURL);
            
            this.worker.state = WORKER_STATE.CONNECTING;
            this.worker.broadcast('update_state', { state: this.worker.state });
            
            // 创建 WebSocket 连接
            this.websocket = new WebSocket(websocketURL);
            
            // 设置事件处理器
            this.websocket.onopen = this.worker._onWebsocketOpen;
            this.websocket.onmessage = this.worker._onWebsocketMessage;
            this.websocket.onerror = this.worker._onWebsocketError;
            this.websocket.onclose = this.worker._onWebsocketClose;
            
            // 设置连接超时
            this.setConnectionTimeout();
            
        } catch (error) {
            console.error('WebSocket 连接失败:', error);
            this.handleConnectionError(error);
        }
    }
    
    setConnectionTimeout() {
        // 清除之前的超时
        if (this.worker.connectTimeout) {
            clearTimeout(this.worker.connectTimeout);
        }
        
        // 设置新的连接超时
        this.worker.connectTimeout = setTimeout(() => {
            if (this.websocket && this.websocket.readyState === WebSocket.CONNECTING) {
                console.log('连接超时，关闭连接');
                this.websocket.close();
            }
        }, 10000); // 10秒超时
    }
    
    handleConnectionError(error) {
        this.connectionAttempts++;
        
        if (this.connectionAttempts >= this.maxConnectionAttempts) {
            console.error('达到最大连接尝试次数，停止重连');
            this.worker.state = WORKER_STATE.DISCONNECTED;
            this.worker.broadcast('disconnect', { 
                reason: 'max_attempts_reached',
                attempts: this.connectionAttempts 
            });
            return;
        }
        
        // 计算重连延迟
        const delay = this.calculateReconnectDelay();
        
        console.log(`${delay}ms 后重连 (尝试 ${this.connectionAttempts}/${this.maxConnectionAttempts})`);
        
        this.worker.isReconnecting = true;
        this.worker.broadcast('reconnecting', { 
            delay: delay,
            attempt: this.connectionAttempts 
        });
        
        // 延迟重连
        setTimeout(() => {
            this.connect(this.worker.websocketURL);
        }, delay);
    }
    
    calculateReconnectDelay() {
        // 指数退避算法
        const baseDelay = this.worker.INITIAL_RECONNECT_DELAY;
        const maxDelay = this.worker.MAXIMUM_RECONNECT_DELAY || 60000;
        const jitter = this.worker.RECONNECT_JITTER;
        
        // 指数增长
        let delay = baseDelay * Math.pow(2, this.connectionAttempts - 1);
        
        // 添加随机抖动
        delay += Math.random() * jitter;
        
        // 限制最大延迟
        return Math.min(delay, maxDelay);
    }
    
    disconnect(code = WEBSOCKET_CLOSE_CODES.CLEAN, reason = 'Normal closure') {
        if (this.websocket) {
            console.log('主动断开 WebSocket 连接:', reason);
            this.websocket.close(code, reason);
        }
        
        // 清除连接超时
        if (this.worker.connectTimeout) {
            clearTimeout(this.worker.connectTimeout);
            this.worker.connectTimeout = null;
        }
        
        this.worker.state = WORKER_STATE.DISCONNECTED;
        this.worker.broadcast('disconnect', { code, reason });
    }
    
    isConnected() {
        return this.websocket && this.websocket.readyState === WebSocket.OPEN;
    }
    
    getConnectionState() {
        if (!this.websocket) {
            return 'not_created';
        }
        
        switch (this.websocket.readyState) {
            case WebSocket.CONNECTING:
                return 'connecting';
            case WebSocket.OPEN:
                return 'open';
            case WebSocket.CLOSING:
                return 'closing';
            case WebSocket.CLOSED:
                return 'closed';
            default:
                return 'unknown';
        }
    }
    
    sendMessage(message) {
        if (this.isConnected()) {
            try {
                const messageStr = JSON.stringify(message);
                this.websocket.send(messageStr);
                return true;
            } catch (error) {
                console.error('发送消息失败:', error);
                return false;
            }
        } else {
            console.warn('WebSocket 未连接，无法发送消息');
            return false;
        }
    }
}
```

### 3. 客户端管理系统
```javascript
// 客户端管理器
class WebSocketClientManager {
    constructor(worker) {
        this.worker = worker;
        this.clients = new Map();
        this.clientChannels = new Map();
        this.clientDebugModes = new Map();
        this.setupClientHandling();
    }
    
    setupClientHandling() {
        // 设置客户端消息处理
        this.setupMessageHandlers();
    }
    
    registerClient(messagePort) {
        const clientId = this.generateClientId();
        
        console.log('注册新客户端:', clientId);
        
        // 存储客户端信息
        this.clients.set(clientId, {
            messagePort: messagePort,
            registeredAt: Date.now(),
            channels: [],
            debugMode: false
        });
        
        this.clientChannels.set(messagePort, []);
        this.clientDebugModes.set(messagePort, false);
        
        // 设置消息处理器
        messagePort.onmessage = (event) => {
            this.handleClientMessage(clientId, messagePort, event.data);
        };
        
        // 发送初始化消息
        this.sendToClient(messagePort, 'initialized', {
            clientId: clientId,
            workerState: this.worker.state
        });
        
        return clientId;
    }
    
    unregisterClient(clientId) {
        const client = this.clients.get(clientId);
        
        if (client) {
            console.log('注销客户端:', clientId);
            
            // 清理客户端频道
            this.clientChannels.delete(client.messagePort);
            this.clientDebugModes.delete(client.messagePort);
            this.clients.delete(clientId);
            
            // 更新频道订阅
            this.worker._debouncedUpdateChannels();
        }
    }
    
    handleClientMessage(clientId, messagePort, message) {
        const { action, data } = message;
        
        console.log(`客户端 ${clientId} 发送消息:`, action, data);
        
        switch (action) {
            case 'add_channel':
                this.addChannel(clientId, messagePort, data.channel);
                break;
                
            case 'delete_channel':
                this.deleteChannel(clientId, messagePort, data.channel);
                break;
                
            case 'force_update_channels':
                this.forceUpdateChannels();
                break;
                
            case 'initialize_connection':
                this.initializeConnection(clientId, data);
                break;
                
            case 'send':
                this.sendToServer(data);
                break;
                
            case 'leave':
                this.unregisterClient(clientId);
                break;
                
            case 'stop':
                this.stopWorker();
                break;
                
            case 'start':
                this.startWorker(data);
                break;
                
            default:
                console.warn('未知的客户端操作:', action);
        }
    }
    
    addChannel(clientId, messagePort, channel) {
        const channels = this.clientChannels.get(messagePort) || [];
        
        if (!channels.includes(channel)) {
            channels.push(channel);
            this.clientChannels.set(messagePort, channels);
            
            console.log(`客户端 ${clientId} 添加频道:`, channel);
            
            // 更新频道订阅
            this.worker._debouncedUpdateChannels();
        }
    }
    
    deleteChannel(clientId, messagePort, channel) {
        const channels = this.clientChannels.get(messagePort) || [];
        const index = channels.indexOf(channel);
        
        if (index !== -1) {
            channels.splice(index, 1);
            this.clientChannels.set(messagePort, channels);
            
            console.log(`客户端 ${clientId} 删除频道:`, channel);
            
            // 更新频道订阅
            this.worker._debouncedUpdateChannels();
        }
    }
    
    forceUpdateChannels() {
        console.log('强制更新频道');
        this.worker._forceUpdateChannels();
    }
    
    initializeConnection(clientId, data) {
        console.log(`初始化客户端 ${clientId} 连接:`, data);
        
        const { websocketURL, uid, db, startTs, debugMode } = data;
        
        // 更新 Worker 配置
        if (startTs > this.worker.newestStartTs) {
            this.worker.newestStartTs = startTs;
            this.worker.websocketURL = websocketURL;
            this.worker.currentUID = uid;
            this.worker.currentDB = db;
            this.worker.isWaitingForNewUID = false;
        }
        
        // 设置调试模式
        const client = this.clients.get(clientId);
        if (client) {
            client.debugMode = debugMode;
            this.clientDebugModes.set(client.messagePort, debugMode);
        }
        
        // 更新全局调试模式
        this.updateGlobalDebugMode();
        
        // 开始连接
        this.worker.connectionManager.connect(websocketURL);
    }
    
    sendToServer(data) {
        // 防抖发送到服务器
        this.worker._debouncedSendToServer(data);
    }
    
    stopWorker() {
        console.log('停止 Worker');
        this.worker.active = false;
        this.worker.connectionManager.disconnect();
    }
    
    startWorker(data) {
        console.log('启动 Worker:', data);
        this.worker.active = true;
        
        if (data && data.websocketURL) {
            this.worker.connectionManager.connect(data.websocketURL);
        }
    }
    
    updateGlobalDebugMode() {
        // 检查是否有任何客户端启用了调试模式
        this.worker.isDebug = Array.from(this.clientDebugModes.values()).some(debug => debug);
        
        console.log('全局调试模式:', this.worker.isDebug);
    }
    
    broadcastToClients(type, data) {
        for (const [clientId, client] of this.clients) {
            try {
                client.messagePort.postMessage({ 
                    type, 
                    data: data ? JSON.parse(JSON.stringify(data)) : undefined 
                });
            } catch (error) {
                console.error(`向客户端 ${clientId} 发送消息失败:`, error);
            }
        }
    }
    
    sendToClient(messagePort, type, data) {
        try {
            messagePort.postMessage({ 
                type, 
                data: data ? JSON.parse(JSON.stringify(data)) : undefined 
            });
        } catch (error) {
            console.error('发送消息到客户端失败:', error);
        }
    }
    
    generateClientId() {
        return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    getAllChannels() {
        const allChannels = new Set();
        
        for (const channels of this.clientChannels.values()) {
            channels.forEach(channel => allChannels.add(channel));
        }
        
        return Array.from(allChannels);
    }
    
    getClientCount() {
        return this.clients.size;
    }
    
    getClientInfo() {
        const info = [];
        
        for (const [clientId, client] of this.clients) {
            info.push({
                id: clientId,
                registeredAt: client.registeredAt,
                channels: this.clientChannels.get(client.messagePort) || [],
                debugMode: client.debugMode
            });
        }
        
        return info;
    }
}
```

## WebSocket Worker 特性

### 1. 连接管理特性
- **自动重连**: 指数退避算法的智能重连机制
- **连接状态**: 完整的连接状态监控和管理
- **超时处理**: 连接超时检测和处理
- **错误恢复**: 完善的错误检测和恢复机制

### 2. 客户端管理特性
- **多客户端**: 支持多个标签页客户端连接
- **消息广播**: 高效的消息广播机制
- **频道管理**: 灵活的频道订阅和管理
- **状态同步**: 客户端状态的实时同步

### 3. 消息处理特性
- **消息队列**: 可靠的消息队列和处理机制
- **防抖处理**: 防抖机制避免频繁操作
- **错误处理**: 完善的消息错误处理
- **性能优化**: 高效的消息处理和分发

### 4. 频道管理特性
- **动态订阅**: 动态的频道订阅和取消订阅
- **状态同步**: 频道状态的实时同步
- **批量操作**: 批量频道操作优化
- **持久化**: 频道订阅的持久化管理

## 最佳实践

### 1. Worker 初始化
```javascript
// ✅ 推荐：正确的 Worker 初始化
const worker = new WebsocketWorker();
const channel = new MessageChannel();
worker.registerClient(channel.port2);
```

### 2. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
worker.onmessage = (event) => {
    try {
        handleMessage(event.data);
    } catch (error) {
        console.error('消息处理失败:', error);
    }
};
```

### 3. 资源清理
```javascript
// ✅ 推荐：及时清理资源
window.addEventListener('beforeunload', () => {
    worker.postMessage({ action: 'leave' });
});
```

## 总结

Odoo WebSocket Worker 模块提供了强大的 WebSocket 连接管理功能：

**核心优势**:
- **高效连接**: 跨标签页的 WebSocket 连接共享
- **智能重连**: 指数退避算法的自动重连机制
- **多客户端**: 支持多个客户端的统一管理
- **消息可靠**: 可靠的消息队列和处理机制
- **频道灵活**: 灵活的频道订阅和管理系统

**适用场景**:
- 实时通信应用
- 多标签页协调
- 消息推送系统
- 状态同步
- 协作应用

**设计优势**:
- Worker 线程隔离
- 资源高效共享
- 状态可靠管理
- 性能优化

这个 WebSocket Worker 为 Odoo Web 客户端提供了重要的实时通信能力，是构建现代化实时Web应用的核心基础设施。

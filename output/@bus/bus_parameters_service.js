/********************************************************
*  Filepath: /bus/static/src/bus_parameters_service.js  *
*  Lines: 19                                            *
********************************************************/
odoo.define('@bus/bus_parameters_service', ['@web/core/registry'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module */

const { registry } = require("@web/core/registry");

const busParametersService = __exports.busParametersService = {
    start() {
        return {
            serverURL: window.origin,
        };
    },
};

registry.category("services").add("bus.parameters", busParametersService);

return __exports;
});
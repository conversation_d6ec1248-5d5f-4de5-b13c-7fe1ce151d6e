/********************************************************************
*  Filepath: /calendar/static/src/activity/activity_model_patch.js  *
*  Lines: 28                                                        *
********************************************************************/
odoo.define('@calendar/activity/activity_model_patch', ['@mail/core/web/activity_model', '@mail/utils/common/misc', '@web/core/utils/patch'], function (require) {
'use strict';
let __exports = {};
const { Activity } = require("@mail/core/web/activity_model");
const { assignIn } = require("@mail/utils/common/misc");
const { patch } = require("@web/core/utils/patch");

patch(Activity, {
    _insert(data) {
        const activity = super._insert(...arguments);
        assignIn(activity, data, ["calendar_event_id"]);
        return activity;
    },
});

patch(Activity.prototype, {
    async rescheduleMeeting() {
        const action = await this.store.env.services.orm.call(
            "mail.activity",
            "action_create_calendar_event",
            [[this.id]]
        );
        this.store.env.services.action.doAction(action);
    },
});

return __exports;
});
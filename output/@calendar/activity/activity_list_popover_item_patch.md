# ActivityListPopoverItemPatch - 活动列表弹出项补丁

## 概述

`activity_list_popover_item_patch.js` 是 Odoo Calendar 模块的活动列表弹出项补丁文件，负责扩展邮件模块中的活动列表弹出项组件。该模块包含18行代码，是一个功能专门的补丁模块，专门用于为日历相关的活动添加特殊行为，具备编辑按钮控制、会议重新安排等特性，是日历活动管理的重要扩展。

## 文件信息
- **路径**: `/calendar/static/src/activity/activity_list_popover_item_patch.js`
- **行数**: 18
- **模块**: `@calendar/activity/activity_list_popover_item_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/web/activity_list_popover_item'    // 邮件活动列表弹出项
'@web/core/utils/patch'                        // 补丁工具
```

## 核心功能

### 1. 补丁定义

```javascript
patch(ActivityListPopoverItem.prototype, {
    get hasEditButton() {
        return super.hasEditButton && !this.props.activity.calendar_event_id;
    },

    async onClickReschedule() {
        await this.props.activity.rescheduleMeeting();
    },
});
```

**补丁特性**:
- **原型扩展**: 扩展ActivityListPopoverItem原型
- **编辑控制**: 控制编辑按钮的显示
- **重新安排**: 添加会议重新安排功能
- **异步操作**: 支持异步操作处理

### 2. 编辑按钮控制

```javascript
get hasEditButton() {
    return super.hasEditButton && !this.props.activity.calendar_event_id;
}
```

**控制功能**:
- **条件判断**: 基于日历事件ID判断
- **父类调用**: 调用父类的hasEditButton方法
- **逻辑与**: 使用逻辑与操作符组合条件
- **日历排除**: 排除已关联日历事件的活动

### 3. 重新安排处理

```javascript
async onClickReschedule() {
    await this.props.activity.rescheduleMeeting();
}
```

**重新安排功能**:
- **异步方法**: 异步处理重新安排操作
- **活动调用**: 调用活动对象的重新安排方法
- **等待完成**: 等待重新安排操作完成
- **错误处理**: 支持异步错误处理

## 使用场景

### 1. 活动列表补丁管理器

```javascript
// 活动列表补丁管理器
class ActivityListPopoverItemPatchManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置补丁配置
        this.patchConfig = {
            enableEditControl: true,
            enableReschedule: true,
            enableCalendarIntegration: true,
            enableActivityTracking: false,
            enableNotifications: true,
            autoApplyPatches: true
        };
        
        // 设置活动类型
        this.activityTypes = new Map([
            ['meeting', {
                name: 'Meeting',
                hasCalendarEvent: true,
                canReschedule: true,
                canEdit: false,
                icon: 'fa-calendar'
            }],
            ['call', {
                name: 'Phone Call',
                hasCalendarEvent: false,
                canReschedule: false,
                canEdit: true,
                icon: 'fa-phone'
            }],
            ['email', {
                name: 'Email',
                hasCalendarEvent: false,
                canReschedule: false,
                canEdit: true,
                icon: 'fa-envelope'
            }],
            ['todo', {
                name: 'To Do',
                hasCalendarEvent: false,
                canReschedule: false,
                canEdit: true,
                icon: 'fa-check-square'
            }]
        ]);
        
        // 设置重新安排选项
        this.rescheduleOptions = new Map([
            ['1hour', {
                name: '1 Hour Later',
                offset: 60 * 60 * 1000,
                type: 'relative'
            }],
            ['1day', {
                name: '1 Day Later',
                offset: 24 * 60 * 60 * 1000,
                type: 'relative'
            }],
            ['1week', {
                name: '1 Week Later',
                offset: 7 * 24 * 60 * 60 * 1000,
                type: 'relative'
            }],
            ['custom', {
                name: 'Custom Time',
                offset: null,
                type: 'custom'
            }]
        ]);
        
        // 设置补丁统计
        this.patchStatistics = {
            totalPatches: 0,
            editButtonChecks: 0,
            rescheduleOperations: 0,
            calendarEventActivities: 0,
            regularActivities: 0,
            successfulReschedules: 0,
            failedReschedules: 0
        };
        
        this.initializePatchSystem();
    }
    
    // 初始化补丁系统
    initializePatchSystem() {
        // 创建增强的补丁
        this.createEnhancedPatch();
        
        // 设置编辑控制系统
        this.setupEditControlSystem();
        
        // 设置重新安排系统
        this.setupRescheduleSystem();
        
        // 设置跟踪系统
        this.setupTrackingSystem();
    }
    
    // 创建增强的补丁
    createEnhancedPatch() {
        const originalPatch = {
            get hasEditButton() {
                return super.hasEditButton && !this.props.activity.calendar_event_id;
            },
            async onClickReschedule() {
                await this.props.activity.rescheduleMeeting();
            }
        };
        
        this.enhancedPatch = {
            // 增强的编辑按钮控制
            get hasEditButton() {
                try {
                    // 记录检查统计
                    this.recordEditButtonCheck();
                    
                    // 获取活动信息
                    const activity = this.props.activity;
                    
                    // 检查父类条件
                    const parentHasEdit = super.hasEditButton;
                    
                    // 检查日历事件
                    const hasCalendarEvent = Boolean(activity.calendar_event_id);
                    
                    // 检查活动类型
                    const activityType = this.getActivityType(activity);
                    const typeInfo = this.activityTypes.get(activityType);
                    
                    // 综合判断
                    let canEdit = parentHasEdit && !hasCalendarEvent;
                    
                    if (typeInfo) {
                        canEdit = canEdit && typeInfo.canEdit;
                    }
                    
                    // 记录统计
                    if (hasCalendarEvent) {
                        this.patchStatistics.calendarEventActivities++;
                    } else {
                        this.patchStatistics.regularActivities++;
                    }
                    
                    return canEdit;
                    
                } catch (error) {
                    console.error('Edit button check error:', error);
                    return super.hasEditButton;
                }
            },
            
            // 增强的重新安排处理
            async onClickReschedule() {
                try {
                    // 记录重新安排操作
                    this.recordRescheduleOperation();
                    
                    // 验证重新安排条件
                    if (!this.canReschedule()) {
                        this.showRescheduleError('Cannot reschedule this activity');
                        return;
                    }
                    
                    // 显示重新安排选项
                    const rescheduleOption = await this.showRescheduleOptions();
                    if (!rescheduleOption) {
                        return; // 用户取消
                    }
                    
                    // 执行重新安排
                    await this.executeReschedule(rescheduleOption);
                    
                    // 记录成功
                    this.recordRescheduleSuccess();
                    
                    // 显示成功通知
                    this.showRescheduleSuccess();
                    
                } catch (error) {
                    // 记录失败
                    this.recordRescheduleFailure(error);
                    
                    // 显示错误
                    this.showRescheduleError(error.message);
                }
            },
            
            // 获取活动类型
            getActivityType(activity) {
                // 基于活动属性判断类型
                if (activity.calendar_event_id) {
                    return 'meeting';
                }
                
                if (activity.activity_type_id) {
                    const typeName = activity.activity_type_id[1]?.toLowerCase();
                    if (typeName?.includes('call')) return 'call';
                    if (typeName?.includes('email')) return 'email';
                    if (typeName?.includes('todo')) return 'todo';
                }
                
                return 'todo'; // 默认类型
            },
            
            // 检查是否可以重新安排
            canReschedule() {
                const activity = this.props.activity;
                
                // 检查是否有日历事件
                if (!activity.calendar_event_id) {
                    return false;
                }
                
                // 检查活动状态
                if (activity.state === 'done') {
                    return false;
                }
                
                // 检查权限
                if (!activity.can_write) {
                    return false;
                }
                
                return true;
            },
            
            // 显示重新安排选项
            async showRescheduleOptions() {
                return new Promise((resolve) => {
                    const dialog = useService("dialog");
                    
                    dialog.add(RescheduleDialog, {
                        title: 'Reschedule Meeting',
                        options: Array.from(this.rescheduleOptions.values()),
                        onConfirm: (option) => resolve(option),
                        onCancel: () => resolve(null)
                    });
                });
            },
            
            // 执行重新安排
            async executeReschedule(option) {
                const activity = this.props.activity;
                
                if (option.type === 'relative') {
                    // 相对时间重新安排
                    const newDate = new Date(Date.now() + option.offset);
                    await activity.rescheduleMeeting(newDate);
                } else if (option.type === 'custom') {
                    // 自定义时间重新安排
                    const customDate = await this.showDateTimePicker();
                    if (customDate) {
                        await activity.rescheduleMeeting(customDate);
                    }
                } else {
                    // 默认重新安排
                    await activity.rescheduleMeeting();
                }
            },
            
            // 显示日期时间选择器
            async showDateTimePicker() {
                return new Promise((resolve) => {
                    const dialog = useService("dialog");
                    
                    dialog.add(DateTimePickerDialog, {
                        title: 'Select New Date and Time',
                        defaultDate: new Date(),
                        onConfirm: (date) => resolve(date),
                        onCancel: () => resolve(null)
                    });
                });
            },
            
            // 记录编辑按钮检查
            recordEditButtonCheck() {
                this.patchStatistics.editButtonChecks++;
            },
            
            // 记录重新安排操作
            recordRescheduleOperation() {
                this.patchStatistics.rescheduleOperations++;
            },
            
            // 记录重新安排成功
            recordRescheduleSuccess() {
                this.patchStatistics.successfulReschedules++;
            },
            
            // 记录重新安排失败
            recordRescheduleFailure(error) {
                this.patchStatistics.failedReschedules++;
                console.error('Reschedule failed:', error);
            },
            
            // 显示重新安排成功
            showRescheduleSuccess() {
                const notification = useService("notification");
                notification.add(
                    'Meeting rescheduled successfully',
                    { type: 'success' }
                );
            },
            
            // 显示重新安排错误
            showRescheduleError(message) {
                const notification = useService("notification");
                notification.add(
                    message || 'Failed to reschedule meeting',
                    { type: 'danger' }
                );
            },
            
            // 获取补丁信息
            getPatchInfo() {
                return {
                    hasEditButton: this.hasEditButton,
                    canReschedule: this.canReschedule(),
                    activityType: this.getActivityType(this.props.activity),
                    hasCalendarEvent: Boolean(this.props.activity.calendar_event_id),
                    statistics: this.patchStatistics
                };
            }
        };
    }
    
    // 设置编辑控制系统
    setupEditControlSystem() {
        this.editControlConfig = {
            enabled: this.patchConfig.enableEditControl,
            checkCalendarEvents: true,
            checkActivityTypes: true,
            checkPermissions: true
        };
    }
    
    // 设置重新安排系统
    setupRescheduleSystem() {
        this.rescheduleConfig = {
            enabled: this.patchConfig.enableReschedule,
            showOptions: true,
            allowCustomTime: true,
            defaultOffset: 60 * 60 * 1000 // 1 hour
        };
    }
    
    // 设置跟踪系统
    setupTrackingSystem() {
        this.trackingConfig = {
            enabled: this.patchConfig.enableActivityTracking,
            trackEditChecks: true,
            trackReschedules: true,
            trackErrors: true
        };
    }
    
    // 应用补丁
    applyPatch() {
        if (this.patchConfig.autoApplyPatches) {
            patch(ActivityListPopoverItem.prototype, this.enhancedPatch);
            this.patchStatistics.totalPatches++;
        }
    }
    
    // 注册活动类型
    registerActivityType(name, config) {
        this.activityTypes.set(name, config);
    }
    
    // 注册重新安排选项
    registerRescheduleOption(name, config) {
        this.rescheduleOptions.set(name, config);
    }
    
    // 获取补丁统计
    getPatchStatistics() {
        return {
            ...this.patchStatistics,
            rescheduleSuccessRate: this.patchStatistics.rescheduleOperations > 0 ? 
                (this.patchStatistics.successfulReschedules / this.patchStatistics.rescheduleOperations) * 100 : 0,
            calendarEventRate: (this.patchStatistics.calendarEventActivities / 
                Math.max(this.patchStatistics.editButtonChecks, 1)) * 100,
            activityTypeVariety: this.activityTypes.size,
            rescheduleOptionVariety: this.rescheduleOptions.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理活动类型
        this.activityTypes.clear();
        
        // 清理重新安排选项
        this.rescheduleOptions.clear();
        
        // 重置统计
        this.patchStatistics = {
            totalPatches: 0,
            editButtonChecks: 0,
            rescheduleOperations: 0,
            calendarEventActivities: 0,
            regularActivities: 0,
            successfulReschedules: 0,
            failedReschedules: 0
        };
    }
}

// 使用示例
const patchManager = new ActivityListPopoverItemPatchManager();

// 应用补丁
patchManager.applyPatch();

// 注册自定义活动类型
patchManager.registerActivityType('webinar', {
    name: 'Webinar',
    hasCalendarEvent: true,
    canReschedule: true,
    canEdit: false,
    icon: 'fa-video'
});

// 注册自定义重新安排选项
patchManager.registerRescheduleOption('2hours', {
    name: '2 Hours Later',
    offset: 2 * 60 * 60 * 1000,
    type: 'relative'
});

// 获取统计信息
const stats = patchManager.getPatchStatistics();
console.log('Patch statistics:', stats);
```

## 技术特点

### 1. 补丁机制
- **原型扩展**: 扩展现有组件原型
- **方法重写**: 重写特定方法行为
- **功能增强**: 增强原有功能
- **向后兼容**: 保持向后兼容性

### 2. 条件控制
- **逻辑判断**: 基于条件控制功能显示
- **日历集成**: 与日历事件深度集成
- **状态检查**: 检查活动和事件状态
- **权限验证**: 验证用户操作权限

### 3. 异步处理
- **异步操作**: 支持异步方法调用
- **错误处理**: 完善的错误处理机制
- **状态管理**: 管理异步操作状态
- **用户反馈**: 提供操作结果反馈

### 4. 模块集成
- **邮件集成**: 与邮件模块深度集成
- **日历集成**: 与日历模块协同工作
- **活动管理**: 统一的活动管理接口
- **数据同步**: 保持数据同步一致

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 装饰原有组件功能
- **行为增强**: 增强组件行为
- **透明扩展**: 透明地扩展功能

### 2. 策略模式 (Strategy Pattern)
- **编辑策略**: 不同的编辑控制策略
- **重新安排策略**: 不同的重新安排策略
- **显示策略**: 不同的显示策略

### 3. 观察者模式 (Observer Pattern)
- **状态观察**: 观察活动状态变化
- **事件观察**: 观察用户交互事件
- **数据观察**: 观察数据变化

### 4. 命令模式 (Command Pattern)
- **重新安排命令**: 封装重新安排操作
- **编辑命令**: 封装编辑操作
- **撤销支持**: 支持操作撤销

## 注意事项

1. **补丁冲突**: 避免与其他补丁冲突
2. **版本兼容**: 确保版本兼容性
3. **性能影响**: 注意补丁对性能的影响
4. **测试覆盖**: 确保补丁功能的测试覆盖

## 扩展建议

1. **批量操作**: 支持批量重新安排
2. **模板支持**: 支持重新安排模板
3. **通知集成**: 集成通知系统
4. **历史记录**: 记录重新安排历史
5. **权限细化**: 更细粒度的权限控制

该活动列表弹出项补丁为Odoo Calendar模块提供了重要的功能扩展，通过智能的编辑控制和便捷的重新安排功能确保了日历活动管理的灵活性和用户体验。

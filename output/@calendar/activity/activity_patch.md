# ActivityPatch - 活动组件补丁

## 概述

`activity_patch.js` 是 Odoo Calendar 模块的活动组件补丁文件，负责扩展邮件模块中的活动组件。该模块包含32行代码，是一个功能专门的补丁模块，专门用于为活动组件添加日历相关功能，具备ORM服务注入、会议重新安排、特殊删除处理等特性，是活动组件与日历集成的重要扩展。

## 文件信息
- **路径**: `/calendar/static/src/activity/activity_patch.js`
- **行数**: 32
- **模块**: `@calendar/activity/activity_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/web/activity'                      // 邮件活动组件
'@web/core/utils/hooks'                        // 工具钩子
'@web/core/utils/patch'                        // 补丁工具
```

## 核心功能

### 1. 组件设置扩展

```javascript
setup() {
    super.setup();
    this.orm = useService("orm");
}
```

**设置功能**:
- **父类调用**: 调用父类的setup方法
- **服务注入**: 注入ORM服务
- **服务存储**: 存储ORM服务引用
- **初始化扩展**: 扩展组件初始化

### 2. 重新安排点击处理

```javascript
async onClickReschedule() {
    await this.props.activity.rescheduleMeeting();
}
```

**点击处理功能**:
- **异步方法**: 异步处理重新安排
- **活动调用**: 调用活动的重新安排方法
- **等待完成**: 等待重新安排操作完成
- **错误传播**: 传播可能的错误

### 3. 删除方法重写

```javascript
async unlink() {
    if (this.props.activity.calendar_event_id) {
        const thread = this.thread;
        this.props.activity.remove();
        await this.orm.call("mail.activity", "unlink_w_meeting", [[this.props.activity.id]]);
        this.props.onActivityChanged(thread);
    } else {
        super.unlink();
    }
}
```

**删除功能**:
- **条件判断**: 检查是否有关联的日历事件
- **线程保存**: 保存当前线程引用
- **活动移除**: 从本地移除活动
- **服务器调用**: 调用特殊的删除方法
- **回调通知**: 通知活动变更
- **默认处理**: 处理无日历事件的情况

## 使用场景

### 1. 活动组件补丁管理器

```javascript
// 活动组件补丁管理器
class ActivityPatchManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置补丁配置
        this.patchConfig = {
            enableOrmService: true,
            enableRescheduleFeature: true,
            enableSpecialUnlink: true,
            enableErrorHandling: true,
            enableActivityTracking: false,
            enableNotifications: true,
            enableValidation: true
        };
        
        // 设置删除策略
        this.unlinkStrategies = new Map([
            ['with_meeting', {
                name: 'With Meeting',
                method: 'unlink_w_meeting',
                description: 'Delete activity with associated meeting',
                requiresCalendarEvent: true
            }],
            ['standard', {
                name: 'Standard',
                method: 'unlink',
                description: 'Standard activity deletion',
                requiresCalendarEvent: false
            }],
            ['cascade', {
                name: 'Cascade',
                method: 'unlink_cascade',
                description: 'Delete activity and all related records',
                requiresCalendarEvent: false
            }]
        ]);
        
        // 设置重新安排类型
        this.rescheduleTypes = new Map([
            ['immediate', {
                name: 'Immediate',
                delay: 0,
                description: 'Reschedule immediately'
            }],
            ['delayed', {
                name: 'Delayed',
                delay: 300000, // 5 minutes
                description: 'Reschedule with delay'
            }],
            ['batch', {
                name: 'Batch',
                delay: null,
                description: 'Batch reschedule operation'
            }]
        ]);
        
        // 设置补丁统计
        this.patchStatistics = {
            totalSetups: 0,
            rescheduleClicks: 0,
            unlinkOperations: 0,
            meetingUnlinks: 0,
            standardUnlinks: 0,
            successfulReschedules: 0,
            failedReschedules: 0,
            successfulUnlinks: 0,
            failedUnlinks: 0
        };
        
        this.initializePatchSystem();
    }
    
    // 初始化补丁系统
    initializePatchSystem() {
        // 创建增强的补丁
        this.createEnhancedPatch();
        
        // 设置服务系统
        this.setupServiceSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置跟踪系统
        this.setupTrackingSystem();
    }
    
    // 创建增强的补丁
    createEnhancedPatch() {
        this.enhancedPatch = {
            // 增强的设置方法
            setup() {
                try {
                    // 记录设置统计
                    this.recordSetup();
                    
                    // 调用父类设置
                    super.setup();
                    
                    // 注入服务
                    if (this.patchConfig.enableOrmService) {
                        this.orm = useService("orm");
                    }
                    
                    // 注入其他服务
                    this.notification = useService("notification");
                    this.dialog = useService("dialog");
                    
                    // 初始化状态
                    this.initializeActivityState();
                    
                } catch (error) {
                    console.error('Activity setup error:', error);
                    throw error;
                }
            },
            
            // 增强的重新安排点击处理
            async onClickReschedule() {
                try {
                    // 记录重新安排点击
                    this.recordRescheduleClick();
                    
                    // 验证重新安排条件
                    if (!this.canReschedule()) {
                        this.showRescheduleError('Cannot reschedule this activity');
                        return;
                    }
                    
                    // 显示加载状态
                    this.showRescheduleLoading();
                    
                    // 执行重新安排
                    await this.executeReschedule();
                    
                    // 记录成功
                    this.recordRescheduleSuccess();
                    
                    // 显示成功通知
                    this.showRescheduleSuccess();
                    
                } catch (error) {
                    // 记录失败
                    this.recordRescheduleFailure(error);
                    
                    // 显示错误
                    this.showRescheduleError(error.message);
                } finally {
                    // 隐藏加载状态
                    this.hideRescheduleLoading();
                }
            },
            
            // 增强的删除方法
            async unlink() {
                try {
                    // 记录删除操作
                    this.recordUnlinkOperation();
                    
                    // 验证删除条件
                    if (!this.canUnlink()) {
                        this.showUnlinkError('Cannot delete this activity');
                        return;
                    }
                    
                    // 确认删除
                    const confirmed = await this.confirmUnlink();
                    if (!confirmed) {
                        return;
                    }
                    
                    // 执行删除
                    await this.executeUnlink();
                    
                    // 记录成功
                    this.recordUnlinkSuccess();
                    
                    // 显示成功通知
                    this.showUnlinkSuccess();
                    
                } catch (error) {
                    // 记录失败
                    this.recordUnlinkFailure(error);
                    
                    // 显示错误
                    this.showUnlinkError(error.message);
                }
            },
            
            // 检查是否可以重新安排
            canReschedule() {
                const activity = this.props.activity;
                
                // 检查活动状态
                if (activity.state === 'done') {
                    return false;
                }
                
                // 检查权限
                if (!activity.can_write) {
                    return false;
                }
                
                // 检查是否有重新安排方法
                if (typeof activity.rescheduleMeeting !== 'function') {
                    return false;
                }
                
                return true;
            },
            
            // 检查是否可以删除
            canUnlink() {
                const activity = this.props.activity;
                
                // 检查权限
                if (!activity.can_unlink) {
                    return false;
                }
                
                // 检查活动状态
                if (activity.state === 'done' && !activity.allow_delete_done) {
                    return false;
                }
                
                return true;
            },
            
            // 执行重新安排
            async executeReschedule() {
                const rescheduleType = this.getRescheduleType();
                
                if (rescheduleType.delay > 0) {
                    // 延迟重新安排
                    setTimeout(async () => {
                        await this.props.activity.rescheduleMeeting();
                    }, rescheduleType.delay);
                } else {
                    // 立即重新安排
                    await this.props.activity.rescheduleMeeting();
                }
            },
            
            // 执行删除
            async executeUnlink() {
                const activity = this.props.activity;
                
                if (activity.calendar_event_id) {
                    // 有日历事件的删除
                    await this.unlinkWithMeeting();
                } else {
                    // 标准删除
                    await this.unlinkStandard();
                }
            },
            
            // 删除带会议的活动
            async unlinkWithMeeting() {
                const thread = this.thread;
                const activity = this.props.activity;
                
                // 从本地移除
                activity.remove();
                
                // 服务器删除
                await this.orm.call("mail.activity", "unlink_w_meeting", [[activity.id]]);
                
                // 通知变更
                if (this.props.onActivityChanged) {
                    this.props.onActivityChanged(thread);
                }
                
                // 记录会议删除
                this.recordMeetingUnlink();
            },
            
            // 标准删除
            async unlinkStandard() {
                // 调用父类删除
                await super.unlink();
                
                // 记录标准删除
                this.recordStandardUnlink();
            },
            
            // 确认删除
            async confirmUnlink() {
                if (!this.patchConfig.enableValidation) {
                    return true;
                }
                
                return new Promise((resolve) => {
                    const activity = this.props.activity;
                    const hasCalendarEvent = Boolean(activity.calendar_event_id);
                    
                    const message = hasCalendarEvent ? 
                        'This will also delete the associated calendar event. Continue?' :
                        'Are you sure you want to delete this activity?';
                    
                    this.dialog.add(ConfirmationDialog, {
                        title: 'Delete Activity',
                        body: message,
                        confirm: () => resolve(true),
                        cancel: () => resolve(false),
                        confirmLabel: 'Delete',
                        cancelLabel: 'Cancel'
                    });
                });
            },
            
            // 获取重新安排类型
            getRescheduleType() {
                // 可以基于活动属性或用户设置确定类型
                return this.rescheduleTypes.get('immediate');
            },
            
            // 初始化活动状态
            initializeActivityState() {
                this.activityState = {
                    isRescheduling: false,
                    isUnlinking: false,
                    lastAction: null,
                    actionCount: 0
                };
            },
            
            // 显示重新安排加载
            showRescheduleLoading() {
                this.activityState.isRescheduling = true;
            },
            
            // 隐藏重新安排加载
            hideRescheduleLoading() {
                this.activityState.isRescheduling = false;
            },
            
            // 显示重新安排成功
            showRescheduleSuccess() {
                if (this.patchConfig.enableNotifications) {
                    this.notification.add(
                        'Meeting rescheduled successfully',
                        { type: 'success' }
                    );
                }
            },
            
            // 显示重新安排错误
            showRescheduleError(message) {
                if (this.patchConfig.enableNotifications) {
                    this.notification.add(
                        message || 'Failed to reschedule meeting',
                        { type: 'danger' }
                    );
                }
            },
            
            // 显示删除成功
            showUnlinkSuccess() {
                if (this.patchConfig.enableNotifications) {
                    this.notification.add(
                        'Activity deleted successfully',
                        { type: 'success' }
                    );
                }
            },
            
            // 显示删除错误
            showUnlinkError(message) {
                if (this.patchConfig.enableNotifications) {
                    this.notification.add(
                        message || 'Failed to delete activity',
                        { type: 'danger' }
                    );
                }
            },
            
            // 记录设置
            recordSetup() {
                this.patchStatistics.totalSetups++;
            },
            
            // 记录重新安排点击
            recordRescheduleClick() {
                this.patchStatistics.rescheduleClicks++;
                this.activityState.lastAction = 'reschedule';
                this.activityState.actionCount++;
            },
            
            // 记录删除操作
            recordUnlinkOperation() {
                this.patchStatistics.unlinkOperations++;
                this.activityState.lastAction = 'unlink';
                this.activityState.actionCount++;
            },
            
            // 记录重新安排成功
            recordRescheduleSuccess() {
                this.patchStatistics.successfulReschedules++;
            },
            
            // 记录重新安排失败
            recordRescheduleFailure(error) {
                this.patchStatistics.failedReschedules++;
                console.error('Reschedule failed:', error);
            },
            
            // 记录删除成功
            recordUnlinkSuccess() {
                this.patchStatistics.successfulUnlinks++;
            },
            
            // 记录删除失败
            recordUnlinkFailure(error) {
                this.patchStatistics.failedUnlinks++;
                console.error('Unlink failed:', error);
            },
            
            // 记录会议删除
            recordMeetingUnlink() {
                this.patchStatistics.meetingUnlinks++;
            },
            
            // 记录标准删除
            recordStandardUnlink() {
                this.patchStatistics.standardUnlinks++;
            },
            
            // 获取活动信息
            getActivityInfo() {
                return {
                    activity: this.props.activity,
                    state: this.activityState,
                    canReschedule: this.canReschedule(),
                    canUnlink: this.canUnlink(),
                    hasCalendarEvent: Boolean(this.props.activity.calendar_event_id),
                    statistics: this.patchStatistics
                };
            }
        };
    }
    
    // 设置服务系统
    setupServiceSystem() {
        this.serviceConfig = {
            enabled: this.patchConfig.enableOrmService,
            requiredServices: ['orm', 'notification', 'dialog'],
            optionalServices: ['action', 'router']
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationConfig = {
            enabled: this.patchConfig.enableValidation,
            validateReschedule: true,
            validateUnlink: true,
            confirmUnlink: true
        };
    }
    
    // 设置跟踪系统
    setupTrackingSystem() {
        this.trackingConfig = {
            enabled: this.patchConfig.enableActivityTracking,
            trackSetups: true,
            trackReschedules: true,
            trackUnlinks: true
        };
    }
    
    // 应用补丁
    applyPatch() {
        patch(Activity.prototype, this.enhancedPatch);
    }
    
    // 注册删除策略
    registerUnlinkStrategy(name, config) {
        this.unlinkStrategies.set(name, config);
    }
    
    // 注册重新安排类型
    registerRescheduleType(name, config) {
        this.rescheduleTypes.set(name, config);
    }
    
    // 获取补丁统计
    getPatchStatistics() {
        return {
            ...this.patchStatistics,
            rescheduleSuccessRate: this.patchStatistics.rescheduleClicks > 0 ? 
                (this.patchStatistics.successfulReschedules / this.patchStatistics.rescheduleClicks) * 100 : 0,
            unlinkSuccessRate: this.patchStatistics.unlinkOperations > 0 ? 
                (this.patchStatistics.successfulUnlinks / this.patchStatistics.unlinkOperations) * 100 : 0,
            meetingUnlinkRate: this.patchStatistics.unlinkOperations > 0 ? 
                (this.patchStatistics.meetingUnlinks / this.patchStatistics.unlinkOperations) * 100 : 0,
            strategyVariety: this.unlinkStrategies.size,
            typeVariety: this.rescheduleTypes.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理删除策略
        this.unlinkStrategies.clear();
        
        // 清理重新安排类型
        this.rescheduleTypes.clear();
        
        // 重置统计
        this.patchStatistics = {
            totalSetups: 0,
            rescheduleClicks: 0,
            unlinkOperations: 0,
            meetingUnlinks: 0,
            standardUnlinks: 0,
            successfulReschedules: 0,
            failedReschedules: 0,
            successfulUnlinks: 0,
            failedUnlinks: 0
        };
    }
}

// 使用示例
const activityPatchManager = new ActivityPatchManager();

// 应用补丁
activityPatchManager.applyPatch();

// 注册自定义删除策略
activityPatchManager.registerUnlinkStrategy('soft_delete', {
    name: 'Soft Delete',
    method: 'soft_unlink',
    description: 'Soft delete activity (mark as deleted)',
    requiresCalendarEvent: false
});

// 注册自定义重新安排类型
activityPatchManager.registerRescheduleType('scheduled', {
    name: 'Scheduled',
    delay: 600000, // 10 minutes
    description: 'Schedule reschedule operation'
});

// 获取统计信息
const stats = activityPatchManager.getPatchStatistics();
console.log('Activity patch statistics:', stats);
```

## 技术特点

### 1. 组件增强
- **服务注入**: 注入必要的服务
- **方法扩展**: 扩展组件方法
- **功能增强**: 增强组件功能
- **状态管理**: 管理组件状态

### 2. 智能删除
- **条件判断**: 基于日历事件判断删除策略
- **特殊处理**: 特殊处理带会议的活动
- **服务器调用**: 调用专门的删除方法
- **状态同步**: 同步本地和服务器状态

### 3. 重新安排
- **异步处理**: 异步处理重新安排操作
- **错误处理**: 完善的错误处理机制
- **用户反馈**: 提供操作结果反馈
- **权限检查**: 检查操作权限

### 4. 服务集成
- **ORM服务**: 集成ORM服务进行数据操作
- **通知服务**: 集成通知服务提供反馈
- **对话框服务**: 集成对话框服务进行确认
- **动作服务**: 集成动作服务执行操作

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- **组件装饰**: 装饰活动组件
- **功能装饰**: 装饰组件功能
- **方法装饰**: 装饰组件方法

### 2. 策略模式 (Strategy Pattern)
- **删除策略**: 不同的删除处理策略
- **重新安排策略**: 不同的重新安排策略
- **通知策略**: 不同的通知策略

### 3. 命令模式 (Command Pattern)
- **重新安排命令**: 封装重新安排操作
- **删除命令**: 封装删除操作
- **撤销支持**: 支持操作撤销

### 4. 观察者模式 (Observer Pattern)
- **状态观察**: 观察组件状态变化
- **事件观察**: 观察用户交互事件
- **数据观察**: 观察数据变化

## 注意事项

1. **服务依赖**: 确保必要服务的可用性
2. **错误处理**: 处理网络和服务错误
3. **用户体验**: 提供清晰的操作反馈
4. **数据一致性**: 确保本地和服务器数据一致

## 扩展建议

1. **批量操作**: 支持批量重新安排和删除
2. **撤销功能**: 支持操作撤销
3. **权限细化**: 更细粒度的权限控制
4. **操作历史**: 记录操作历史
5. **离线支持**: 支持离线操作

该活动组件补丁为Odoo Calendar模块提供了重要的组件功能扩展，通过服务集成和智能处理确保了活动组件与日历功能的无缝集成。

/**************************************************************
*  Filepath: /calendar/static/src/activity/activity_patch.js  *
*  Lines: 32                                                  *
**************************************************************/
odoo.define('@calendar/activity/activity_patch', ['@mail/core/web/activity', '@web/core/utils/hooks', '@web/core/utils/patch'], function (require) {
'use strict';
let __exports = {};
const { Activity } = require("@mail/core/web/activity");
const { useService } = require("@web/core/utils/hooks");
const { patch } = require("@web/core/utils/patch");

patch(Activity.prototype, {
    setup() {
        super.setup();
        this.orm = useService("orm");
    },
    async onClickReschedule() {
        await this.props.activity.rescheduleMeeting();
    },
    /**
     * @override
     */
    async unlink() {
        if (this.props.activity.calendar_event_id) {
            const thread = this.thread;
            this.props.activity.remove();
            await this.orm.call("mail.activity", "unlink_w_meeting", [[this.props.activity.id]]);
            this.props.onActivityChanged(thread);
        } else {
            super.unlink();
        }
    },
});

return __exports;
});
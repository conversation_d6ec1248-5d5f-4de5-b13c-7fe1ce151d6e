# ActivityModelPatch - 活动模型补丁

## 概述

`activity_model_patch.js` 是 Odoo Calendar 模块的活动模型补丁文件，负责扩展邮件模块中的活动模型。该模块包含28行代码，是一个功能专门的补丁模块，专门用于为活动模型添加日历相关功能，具备日历事件ID字段、会议重新安排方法等特性，是活动与日历集成的重要桥梁。

## 文件信息
- **路径**: `/calendar/static/src/activity/activity_model_patch.js`
- **行数**: 28
- **模块**: `@calendar/activity/activity_model_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/web/activity_model'                // 邮件活动模型
'@mail/utils/common/misc'                      // 邮件工具函数
'@web/core/utils/patch'                        // 补丁工具
```

## 核心功能

### 1. 活动类补丁

```javascript
patch(Activity, {
    _insert(data) {
        const activity = super._insert(...arguments);
        assignIn(activity, data, ["calendar_event_id"]);
        return activity;
    },
});
```

**类补丁特性**:
- **静态方法扩展**: 扩展Activity类的静态方法
- **数据插入**: 在活动创建时插入额外数据
- **字段分配**: 分配日历事件ID字段
- **父类调用**: 保持原有插入逻辑

### 2. 活动原型补丁

```javascript
patch(Activity.prototype, {
    async rescheduleMeeting() {
        const action = await this.store.env.services.orm.call(
            "mail.activity",
            "action_create_calendar_event",
            [[this.id]]
        );
        this.store.env.services.action.doAction(action);
    },
});
```

**原型补丁特性**:
- **实例方法添加**: 为活动实例添加新方法
- **异步操作**: 支持异步会议重新安排
- **ORM调用**: 调用服务器端方法
- **动作执行**: 执行返回的动作

### 3. 字段分配

```javascript
assignIn(activity, data, ["calendar_event_id"]);
```

**分配功能**:
- **字段映射**: 映射日历事件ID字段
- **数据合并**: 合并额外的数据字段
- **选择性分配**: 只分配指定的字段
- **对象扩展**: 扩展活动对象属性

### 4. 会议重新安排

```javascript
async rescheduleMeeting() {
    const action = await this.store.env.services.orm.call(
        "mail.activity",
        "action_create_calendar_event",
        [[this.id]]
    );
    this.store.env.services.action.doAction(action);
}
```

**重新安排功能**:
- **服务调用**: 调用邮件活动服务
- **动作创建**: 创建日历事件动作
- **ID传递**: 传递活动ID参数
- **动作执行**: 执行创建的动作

## 使用场景

### 1. 活动模型补丁管理器

```javascript
// 活动模型补丁管理器
class ActivityModelPatchManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置补丁配置
        this.patchConfig = {
            enableCalendarIntegration: true,
            enableRescheduleFeature: true,
            enableEventIdTracking: true,
            enableDataValidation: true,
            enableErrorHandling: true,
            enableAuditLogging: false,
            autoSyncCalendarEvents: true
        };
        
        // 设置日历字段
        this.calendarFields = new Map([
            ['calendar_event_id', {
                name: 'Calendar Event ID',
                type: 'integer',
                required: false,
                readonly: true,
                description: 'Related calendar event ID'
            }],
            ['calendar_event_start', {
                name: 'Event Start Time',
                type: 'datetime',
                required: false,
                readonly: true,
                description: 'Calendar event start time'
            }],
            ['calendar_event_end', {
                name: 'Event End Time',
                type: 'datetime',
                required: false,
                readonly: true,
                description: 'Calendar event end time'
            }],
            ['calendar_event_location', {
                name: 'Event Location',
                type: 'char',
                required: false,
                readonly: true,
                description: 'Calendar event location'
            }]
        ]);
        
        // 设置重新安排选项
        this.rescheduleOptions = new Map([
            ['create_new', {
                name: 'Create New Event',
                action: 'action_create_calendar_event',
                description: 'Create a new calendar event'
            }],
            ['update_existing', {
                name: 'Update Existing Event',
                action: 'action_update_calendar_event',
                description: 'Update existing calendar event'
            }],
            ['reschedule_event', {
                name: 'Reschedule Event',
                action: 'action_reschedule_calendar_event',
                description: 'Reschedule calendar event'
            }]
        ]);
        
        // 设置补丁统计
        this.patchStatistics = {
            totalInsertions: 0,
            calendarEventAssignments: 0,
            rescheduleOperations: 0,
            successfulReschedules: 0,
            failedReschedules: 0,
            actionExecutions: 0,
            dataValidations: 0,
            errorOccurrences: 0
        };
        
        this.initializePatchSystem();
    }
    
    // 初始化补丁系统
    initializePatchSystem() {
        // 创建增强的补丁
        this.createEnhancedPatch();
        
        // 设置数据验证系统
        this.setupDataValidationSystem();
        
        // 设置错误处理系统
        this.setupErrorHandlingSystem();
        
        // 设置审计系统
        this.setupAuditSystem();
    }
    
    // 创建增强的补丁
    createEnhancedPatch() {
        // 增强的类补丁
        this.enhancedClassPatch = {
            _insert(data) {
                try {
                    // 记录插入统计
                    this.recordInsertion();
                    
                    // 验证数据
                    if (this.patchConfig.enableDataValidation) {
                        this.validateInsertData(data);
                    }
                    
                    // 执行原始插入
                    const activity = super._insert(...arguments);
                    
                    // 分配日历字段
                    if (this.patchConfig.enableCalendarIntegration) {
                        this.assignCalendarFields(activity, data);
                    }
                    
                    // 记录日历事件分配
                    if (data.calendar_event_id) {
                        this.recordCalendarEventAssignment();
                    }
                    
                    // 审计日志
                    if (this.patchConfig.enableAuditLogging) {
                        this.logActivityInsertion(activity, data);
                    }
                    
                    return activity;
                    
                } catch (error) {
                    this.handleInsertError(error, data);
                    throw error;
                }
            },
            
            // 验证插入数据
            validateInsertData(data) {
                this.patchStatistics.dataValidations++;
                
                // 验证日历事件ID
                if (data.calendar_event_id !== undefined) {
                    if (typeof data.calendar_event_id !== 'number' && data.calendar_event_id !== null) {
                        throw new Error('Invalid calendar_event_id type');
                    }
                }
                
                // 验证其他日历字段
                for (const [fieldName, fieldConfig] of this.calendarFields.entries()) {
                    if (data[fieldName] !== undefined) {
                        this.validateFieldValue(fieldName, data[fieldName], fieldConfig);
                    }
                }
            },
            
            // 验证字段值
            validateFieldValue(fieldName, value, config) {
                if (config.required && (value === null || value === undefined)) {
                    throw new Error(`Required field ${fieldName} is missing`);
                }
                
                if (value !== null && value !== undefined) {
                    switch (config.type) {
                        case 'integer':
                            if (typeof value !== 'number' || !Number.isInteger(value)) {
                                throw new Error(`Field ${fieldName} must be an integer`);
                            }
                            break;
                        case 'datetime':
                            if (!(value instanceof Date) && typeof value !== 'string') {
                                throw new Error(`Field ${fieldName} must be a date or string`);
                            }
                            break;
                        case 'char':
                            if (typeof value !== 'string') {
                                throw new Error(`Field ${fieldName} must be a string`);
                            }
                            break;
                    }
                }
            },
            
            // 分配日历字段
            assignCalendarFields(activity, data) {
                const fieldsToAssign = Array.from(this.calendarFields.keys()).filter(
                    field => data[field] !== undefined
                );
                
                if (fieldsToAssign.length > 0) {
                    assignIn(activity, data, fieldsToAssign);
                }
            },
            
            // 记录插入
            recordInsertion() {
                this.patchStatistics.totalInsertions++;
            },
            
            // 记录日历事件分配
            recordCalendarEventAssignment() {
                this.patchStatistics.calendarEventAssignments++;
            },
            
            // 处理插入错误
            handleInsertError(error, data) {
                this.patchStatistics.errorOccurrences++;
                console.error('Activity insertion error:', error, data);
            },
            
            // 记录活动插入日志
            logActivityInsertion(activity, data) {
                console.log('Activity inserted:', {
                    id: activity.id,
                    calendar_event_id: data.calendar_event_id,
                    timestamp: new Date().toISOString()
                });
            }
        };
        
        // 增强的原型补丁
        this.enhancedPrototypePatch = {
            // 增强的会议重新安排
            async rescheduleMeeting(options = {}) {
                try {
                    // 记录重新安排操作
                    this.recordRescheduleOperation();
                    
                    // 验证重新安排条件
                    if (!this.canReschedule()) {
                        throw new Error('Cannot reschedule this activity');
                    }
                    
                    // 准备重新安排参数
                    const params = this.prepareRescheduleParams(options);
                    
                    // 执行重新安排
                    const action = await this.executeReschedule(params);
                    
                    // 执行动作
                    await this.executeAction(action);
                    
                    // 记录成功
                    this.recordRescheduleSuccess();
                    
                    // 同步日历事件
                    if (this.patchConfig.autoSyncCalendarEvents) {
                        await this.syncCalendarEvent();
                    }
                    
                } catch (error) {
                    // 记录失败
                    this.recordRescheduleFailure(error);
                    throw error;
                }
            },
            
            // 检查是否可以重新安排
            canReschedule() {
                // 检查活动状态
                if (this.state === 'done') {
                    return false;
                }
                
                // 检查权限
                if (!this.can_write) {
                    return false;
                }
                
                // 检查是否有日历事件
                if (!this.calendar_event_id) {
                    return true; // 可以创建新的日历事件
                }
                
                return true;
            },
            
            // 准备重新安排参数
            prepareRescheduleParams(options) {
                const params = {
                    activity_id: this.id,
                    calendar_event_id: this.calendar_event_id,
                    reschedule_type: options.type || 'create_new',
                    new_date: options.date,
                    new_time: options.time,
                    duration: options.duration,
                    location: options.location,
                    description: options.description
                };
                
                return params;
            },
            
            // 执行重新安排
            async executeReschedule(params) {
                const rescheduleOption = this.rescheduleOptions.get(params.reschedule_type);
                const actionName = rescheduleOption?.action || 'action_create_calendar_event';
                
                const action = await this.store.env.services.orm.call(
                    "mail.activity",
                    actionName,
                    [[this.id]],
                    { context: params }
                );
                
                return action;
            },
            
            // 执行动作
            async executeAction(action) {
                this.patchStatistics.actionExecutions++;
                await this.store.env.services.action.doAction(action);
            },
            
            // 同步日历事件
            async syncCalendarEvent() {
                if (this.calendar_event_id) {
                    try {
                        const eventData = await this.store.env.services.orm.read(
                            'calendar.event',
                            [this.calendar_event_id],
                            ['start', 'stop', 'location', 'description']
                        );
                        
                        if (eventData.length > 0) {
                            this.updateFromCalendarEvent(eventData[0]);
                        }
                    } catch (error) {
                        console.error('Calendar event sync error:', error);
                    }
                }
            },
            
            // 从日历事件更新
            updateFromCalendarEvent(eventData) {
                if (eventData.start) {
                    this.calendar_event_start = eventData.start;
                }
                if (eventData.stop) {
                    this.calendar_event_end = eventData.stop;
                }
                if (eventData.location) {
                    this.calendar_event_location = eventData.location;
                }
            },
            
            // 记录重新安排操作
            recordRescheduleOperation() {
                this.patchStatistics.rescheduleOperations++;
            },
            
            // 记录重新安排成功
            recordRescheduleSuccess() {
                this.patchStatistics.successfulReschedules++;
            },
            
            // 记录重新安排失败
            recordRescheduleFailure(error) {
                this.patchStatistics.failedReschedules++;
                console.error('Reschedule failed:', error);
            },
            
            // 获取活动信息
            getActivityInfo() {
                return {
                    id: this.id,
                    calendar_event_id: this.calendar_event_id,
                    calendar_event_start: this.calendar_event_start,
                    calendar_event_end: this.calendar_event_end,
                    calendar_event_location: this.calendar_event_location,
                    canReschedule: this.canReschedule(),
                    state: this.state,
                    can_write: this.can_write
                };
            }
        };
    }
    
    // 设置数据验证系统
    setupDataValidationSystem() {
        this.dataValidationConfig = {
            enabled: this.patchConfig.enableDataValidation,
            validateTypes: true,
            validateRequired: true,
            validateConstraints: true
        };
    }
    
    // 设置错误处理系统
    setupErrorHandlingSystem() {
        this.errorHandlingConfig = {
            enabled: this.patchConfig.enableErrorHandling,
            logErrors: true,
            notifyErrors: false,
            retryOnFailure: false
        };
    }
    
    // 设置审计系统
    setupAuditSystem() {
        this.auditConfig = {
            enabled: this.patchConfig.enableAuditLogging,
            logInsertions: true,
            logReschedules: true,
            logErrors: true
        };
    }
    
    // 应用补丁
    applyPatches() {
        // 应用类补丁
        patch(Activity, this.enhancedClassPatch);
        
        // 应用原型补丁
        patch(Activity.prototype, this.enhancedPrototypePatch);
    }
    
    // 注册日历字段
    registerCalendarField(name, config) {
        this.calendarFields.set(name, config);
    }
    
    // 注册重新安排选项
    registerRescheduleOption(name, config) {
        this.rescheduleOptions.set(name, config);
    }
    
    // 获取补丁统计
    getPatchStatistics() {
        return {
            ...this.patchStatistics,
            rescheduleSuccessRate: this.patchStatistics.rescheduleOperations > 0 ? 
                (this.patchStatistics.successfulReschedules / this.patchStatistics.rescheduleOperations) * 100 : 0,
            calendarEventAssignmentRate: this.patchStatistics.totalInsertions > 0 ? 
                (this.patchStatistics.calendarEventAssignments / this.patchStatistics.totalInsertions) * 100 : 0,
            errorRate: this.patchStatistics.totalInsertions > 0 ? 
                (this.patchStatistics.errorOccurrences / this.patchStatistics.totalInsertions) * 100 : 0,
            fieldVariety: this.calendarFields.size,
            optionVariety: this.rescheduleOptions.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理日历字段
        this.calendarFields.clear();
        
        // 清理重新安排选项
        this.rescheduleOptions.clear();
        
        // 重置统计
        this.patchStatistics = {
            totalInsertions: 0,
            calendarEventAssignments: 0,
            rescheduleOperations: 0,
            successfulReschedules: 0,
            failedReschedules: 0,
            actionExecutions: 0,
            dataValidations: 0,
            errorOccurrences: 0
        };
    }
}

// 使用示例
const modelPatchManager = new ActivityModelPatchManager();

// 应用补丁
modelPatchManager.applyPatches();

// 注册自定义日历字段
modelPatchManager.registerCalendarField('calendar_event_attendees', {
    name: 'Event Attendees',
    type: 'char',
    required: false,
    readonly: true,
    description: 'Calendar event attendees'
});

// 注册自定义重新安排选项
modelPatchManager.registerRescheduleOption('duplicate_event', {
    name: 'Duplicate Event',
    action: 'action_duplicate_calendar_event',
    description: 'Duplicate calendar event'
});

// 获取统计信息
const stats = modelPatchManager.getPatchStatistics();
console.log('Model patch statistics:', stats);
```

## 技术特点

### 1. 模型扩展
- **字段添加**: 为活动模型添加日历相关字段
- **方法扩展**: 为活动实例添加新方法
- **数据集成**: 集成日历事件数据
- **功能增强**: 增强活动模型功能

### 2. 数据处理
- **字段分配**: 智能分配日历字段
- **数据验证**: 验证插入数据的有效性
- **类型检查**: 检查字段类型正确性
- **错误处理**: 完善的错误处理机制

### 3. 服务集成
- **ORM调用**: 调用服务器端ORM方法
- **动作执行**: 执行服务器返回的动作
- **异步操作**: 支持异步操作处理
- **服务协调**: 协调多个服务调用

### 4. 会议管理
- **重新安排**: 提供会议重新安排功能
- **事件创建**: 支持创建新的日历事件
- **状态同步**: 同步活动和事件状态
- **权限检查**: 检查操作权限

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- **模型装饰**: 装饰活动模型
- **功能装饰**: 装饰模型功能
- **方法装饰**: 装饰模型方法

### 2. 策略模式 (Strategy Pattern)
- **重新安排策略**: 不同的重新安排策略
- **数据处理策略**: 不同的数据处理策略
- **验证策略**: 不同的验证策略

### 3. 观察者模式 (Observer Pattern)
- **数据观察**: 观察数据变化
- **状态观察**: 观察模型状态变化
- **事件观察**: 观察业务事件

### 4. 工厂模式 (Factory Pattern)
- **动作工厂**: 创建不同的动作对象
- **参数工厂**: 创建不同的参数对象
- **配置工厂**: 创建不同的配置对象

## 注意事项

1. **数据一致性**: 确保活动和日历事件数据一致
2. **权限验证**: 验证用户操作权限
3. **错误处理**: 处理网络和服务错误
4. **性能影响**: 注意补丁对性能的影响

## 扩展建议

1. **批量操作**: 支持批量重新安排
2. **冲突检测**: 检测日历冲突
3. **提醒集成**: 集成提醒功能
4. **同步优化**: 优化数据同步机制
5. **离线支持**: 支持离线操作

该活动模型补丁为Odoo Calendar模块提供了重要的模型扩展功能，通过日历字段集成和会议重新安排功能确保了活动与日历事件的无缝集成。

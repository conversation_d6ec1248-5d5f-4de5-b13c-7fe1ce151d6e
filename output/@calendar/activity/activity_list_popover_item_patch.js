/********************************************************************************
*  Filepath: /calendar/static/src/activity/activity_list_popover_item_patch.js  *
*  Lines: 18                                                                    *
********************************************************************************/
odoo.define('@calendar/activity/activity_list_popover_item_patch', ['@mail/core/web/activity_list_popover_item', '@web/core/utils/patch'], function (require) {
'use strict';
let __exports = {};
const { ActivityListPopoverItem } = require("@mail/core/web/activity_list_popover_item");
const { patch } = require("@web/core/utils/patch");

patch(ActivityListPopoverItem.prototype, {
    get hasEditButton() {
        return super.hasEditButton && !this.props.activity.calendar_event_id;
    },

    async onClickReschedule() {
        await this.props.activity.rescheduleMeeting();
    },
});

return __exports;
});
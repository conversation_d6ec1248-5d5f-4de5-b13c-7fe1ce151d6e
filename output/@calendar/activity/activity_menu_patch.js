/*******************************************************************
*  Filepath: /calendar/static/src/activity/activity_menu_patch.js  *
*  Lines: 27                                                       *
*******************************************************************/
odoo.define('@calendar/activity/activity_menu_patch', ['@mail/core/web/activity_menu', '@web/core/utils/patch'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module */

const { ActivityMenu } = require("@mail/core/web/activity_menu");
const { patch } = require("@web/core/utils/patch");

patch(ActivityMenu.prototype, {
    openActivityGroup(group) {
        if (group.model === "calendar.event") {
            this.dropdown.close();
            this.action.doAction("calendar.action_calendar_event", {
                additionalContext: {
                    default_mode: "day",
                    search_default_mymeetings: 1,
                },
                clearBreadcrumbs: true,
            });
        } else {
            super.openActivityGroup(...arguments);
        }
    },
});

return __exports;
});
# ActivityMenuPatch - 活动菜单补丁

## 概述

`activity_menu_patch.js` 是 Odoo Calendar 模块的活动菜单补丁文件，负责扩展邮件模块中的活动菜单组件。该模块包含27行代码，是一个功能专门的补丁模块，专门用于为日历事件活动组提供特殊的打开行为，具备日历视图跳转、下拉菜单控制、面包屑清理等特性，是日历活动导航的重要扩展。

## 文件信息
- **路径**: `/calendar/static/src/activity/activity_menu_patch.js`
- **行数**: 27
- **模块**: `@calendar/activity/activity_menu_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/web/activity_menu'                 // 邮件活动菜单
'@web/core/utils/patch'                        // 补丁工具
```

## 核心功能

### 1. 补丁定义

```javascript
patch(ActivityMenu.prototype, {
    openActivityGroup(group) {
        if (group.model === "calendar.event") {
            this.dropdown.close();
            this.action.doAction("calendar.action_calendar_event", {
                additionalContext: {
                    default_mode: "day",
                    search_default_mymeetings: 1,
                },
                clearBreadcrumbs: true,
            });
        } else {
            super.openActivityGroup(...arguments);
        }
    },
});
```

**补丁特性**:
- **方法重写**: 重写openActivityGroup方法
- **条件分支**: 基于模型类型进行条件处理
- **日历跳转**: 跳转到日历事件视图
- **父类调用**: 保持其他模型的原有行为

### 2. 日历事件处理

```javascript
if (group.model === "calendar.event") {
    this.dropdown.close();
    this.action.doAction("calendar.action_calendar_event", {
        additionalContext: {
            default_mode: "day",
            search_default_mymeetings: 1,
        },
        clearBreadcrumbs: true,
    });
}
```

**处理功能**:
- **模型检查**: 检查是否为日历事件模型
- **下拉关闭**: 关闭活动菜单下拉框
- **动作执行**: 执行日历事件动作
- **上下文设置**: 设置默认视图模式和搜索过滤
- **面包屑清理**: 清理导航面包屑

### 3. 默认行为保持

```javascript
} else {
    super.openActivityGroup(...arguments);
}
```

**默认行为**:
- **父类调用**: 调用父类的原始方法
- **参数传递**: 传递所有原始参数
- **行为保持**: 保持非日历事件的原有行为
- **兼容性**: 确保向后兼容性

## 使用场景

### 1. 活动菜单补丁管理器

```javascript
// 活动菜单补丁管理器
class ActivityMenuPatchManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置补丁配置
        this.patchConfig = {
            enableCalendarRedirect: true,
            enableDropdownAutoClose: true,
            enableBreadcrumbClear: true,
            enableContextCustomization: true,
            enableNavigationTracking: false,
            defaultCalendarMode: 'day',
            enableMyMeetingsFilter: true
        };
        
        // 设置支持的模型
        this.supportedModels = new Map([
            ['calendar.event', {
                name: 'Calendar Event',
                action: 'calendar.action_calendar_event',
                defaultMode: 'day',
                defaultFilters: ['search_default_mymeetings'],
                clearBreadcrumbs: true,
                closeDropdown: true
            }],
            ['calendar.appointment', {
                name: 'Calendar Appointment',
                action: 'calendar.action_calendar_appointment',
                defaultMode: 'week',
                defaultFilters: ['search_default_upcoming'],
                clearBreadcrumbs: true,
                closeDropdown: true
            }],
            ['calendar.recurrence', {
                name: 'Calendar Recurrence',
                action: 'calendar.action_calendar_recurrence',
                defaultMode: 'month',
                defaultFilters: [],
                clearBreadcrumbs: false,
                closeDropdown: true
            }]
        ]);
        
        // 设置视图模式
        this.viewModes = new Map([
            ['day', {
                name: 'Day View',
                period: 'day',
                scale: 24,
                unit: 'hour'
            }],
            ['week', {
                name: 'Week View',
                period: 'week',
                scale: 7,
                unit: 'day'
            }],
            ['month', {
                name: 'Month View',
                period: 'month',
                scale: 30,
                unit: 'day'
            }],
            ['year', {
                name: 'Year View',
                period: 'year',
                scale: 12,
                unit: 'month'
            }]
        ]);
        
        // 设置补丁统计
        this.patchStatistics = {
            totalOpenings: 0,
            calendarEventOpenings: 0,
            otherModelOpenings: 0,
            dropdownCloses: 0,
            breadcrumbClears: 0,
            actionExecutions: 0,
            navigationsByMode: new Map(),
            navigationsByModel: new Map()
        };
        
        this.initializePatchSystem();
    }
    
    // 初始化补丁系统
    initializePatchSystem() {
        // 创建增强的补丁
        this.createEnhancedPatch();
        
        // 设置导航系统
        this.setupNavigationSystem();
        
        // 设置跟踪系统
        this.setupTrackingSystem();
        
        // 设置上下文系统
        this.setupContextSystem();
    }
    
    // 创建增强的补丁
    createEnhancedPatch() {
        const originalPatch = {
            openActivityGroup(group) {
                if (group.model === "calendar.event") {
                    this.dropdown.close();
                    this.action.doAction("calendar.action_calendar_event", {
                        additionalContext: {
                            default_mode: "day",
                            search_default_mymeetings: 1,
                        },
                        clearBreadcrumbs: true,
                    });
                } else {
                    super.openActivityGroup(...arguments);
                }
            }
        };
        
        this.enhancedPatch = {
            // 增强的活动组打开
            openActivityGroup(group) {
                try {
                    // 记录打开统计
                    this.recordGroupOpening(group);
                    
                    // 获取模型配置
                    const modelConfig = this.getModelConfig(group.model);
                    
                    if (modelConfig) {
                        // 处理支持的日历模型
                        this.handleCalendarModel(group, modelConfig);
                    } else {
                        // 处理其他模型
                        this.handleOtherModel(group);
                    }
                    
                } catch (error) {
                    console.error('Activity group opening error:', error);
                    // 回退到原始行为
                    super.openActivityGroup(...arguments);
                }
            },
            
            // 获取模型配置
            getModelConfig(model) {
                return this.supportedModels.get(model);
            },
            
            // 处理日历模型
            handleCalendarModel(group, config) {
                // 记录日历事件打开
                this.recordCalendarEventOpening(group.model);
                
                // 关闭下拉菜单
                if (config.closeDropdown && this.dropdown) {
                    this.dropdown.close();
                    this.recordDropdownClose();
                }
                
                // 准备上下文
                const context = this.prepareContext(config);
                
                // 执行动作
                this.executeAction(config.action, context, config.clearBreadcrumbs);
            },
            
            // 处理其他模型
            handleOtherModel(group) {
                // 记录其他模型打开
                this.recordOtherModelOpening(group.model);
                
                // 调用父类方法
                super.openActivityGroup(...arguments);
            },
            
            // 准备上下文
            prepareContext(config) {
                const context = {
                    default_mode: config.defaultMode || this.patchConfig.defaultCalendarMode
                };
                
                // 添加默认过滤器
                if (config.defaultFilters) {
                    for (const filter of config.defaultFilters) {
                        context[filter] = 1;
                    }
                }
                
                // 添加自定义上下文
                if (this.patchConfig.enableContextCustomization) {
                    context.from_activity_menu = true;
                    context.activity_group_opened = new Date().toISOString();
                }
                
                return context;
            },
            
            // 执行动作
            executeAction(actionName, context, clearBreadcrumbs) {
                const actionOptions = {
                    additionalContext: context
                };
                
                if (clearBreadcrumbs && this.patchConfig.enableBreadcrumbClear) {
                    actionOptions.clearBreadcrumbs = true;
                    this.recordBreadcrumbClear();
                }
                
                // 记录动作执行
                this.recordActionExecution(actionName);
                
                // 执行动作
                this.action.doAction(actionName, actionOptions);
            },
            
            // 记录组打开
            recordGroupOpening(group) {
                this.patchStatistics.totalOpenings++;
                
                // 记录模型统计
                const model = group.model;
                const count = this.patchStatistics.navigationsByModel.get(model) || 0;
                this.patchStatistics.navigationsByModel.set(model, count + 1);
            },
            
            // 记录日历事件打开
            recordCalendarEventOpening(model) {
                this.patchStatistics.calendarEventOpenings++;
                
                // 记录模式统计
                const mode = this.patchConfig.defaultCalendarMode;
                const count = this.patchStatistics.navigationsByMode.get(mode) || 0;
                this.patchStatistics.navigationsByMode.set(mode, count + 1);
            },
            
            // 记录其他模型打开
            recordOtherModelOpening(model) {
                this.patchStatistics.otherModelOpenings++;
            },
            
            // 记录下拉关闭
            recordDropdownClose() {
                this.patchStatistics.dropdownCloses++;
            },
            
            // 记录面包屑清理
            recordBreadcrumbClear() {
                this.patchStatistics.breadcrumbClears++;
            },
            
            // 记录动作执行
            recordActionExecution(actionName) {
                this.patchStatistics.actionExecutions++;
            },
            
            // 获取导航信息
            getNavigationInfo() {
                return {
                    supportedModels: Array.from(this.supportedModels.keys()),
                    viewModes: Array.from(this.viewModes.keys()),
                    defaultMode: this.patchConfig.defaultCalendarMode,
                    enabledFeatures: {
                        calendarRedirect: this.patchConfig.enableCalendarRedirect,
                        dropdownAutoClose: this.patchConfig.enableDropdownAutoClose,
                        breadcrumbClear: this.patchConfig.enableBreadcrumbClear,
                        contextCustomization: this.patchConfig.enableContextCustomization
                    },
                    statistics: this.patchStatistics
                };
            },
            
            // 设置默认模式
            setDefaultMode(mode) {
                if (this.viewModes.has(mode)) {
                    this.patchConfig.defaultCalendarMode = mode;
                }
            },
            
            // 切换功能
            toggleFeature(feature, enabled) {
                if (feature in this.patchConfig) {
                    this.patchConfig[feature] = enabled;
                }
            }
        };
    }
    
    // 设置导航系统
    setupNavigationSystem() {
        this.navigationConfig = {
            enabled: true,
            defaultMode: this.patchConfig.defaultCalendarMode,
            supportedModes: Array.from(this.viewModes.keys())
        };
    }
    
    // 设置跟踪系统
    setupTrackingSystem() {
        this.trackingConfig = {
            enabled: this.patchConfig.enableNavigationTracking,
            trackOpenings: true,
            trackModes: true,
            trackModels: true
        };
    }
    
    // 设置上下文系统
    setupContextSystem() {
        this.contextConfig = {
            enabled: this.patchConfig.enableContextCustomization,
            includeTimestamp: true,
            includeSource: true,
            customFields: new Map()
        };
    }
    
    // 应用补丁
    applyPatch() {
        patch(ActivityMenu.prototype, this.enhancedPatch);
    }
    
    // 注册模型配置
    registerModelConfig(model, config) {
        this.supportedModels.set(model, config);
    }
    
    // 注册视图模式
    registerViewMode(mode, config) {
        this.viewModes.set(mode, config);
    }
    
    // 获取补丁统计
    getPatchStatistics() {
        return {
            ...this.patchStatistics,
            calendarEventRate: (this.patchStatistics.calendarEventOpenings / 
                Math.max(this.patchStatistics.totalOpenings, 1)) * 100,
            dropdownCloseRate: (this.patchStatistics.dropdownCloses / 
                Math.max(this.patchStatistics.calendarEventOpenings, 1)) * 100,
            breadcrumbClearRate: (this.patchStatistics.breadcrumbClears / 
                Math.max(this.patchStatistics.actionExecutions, 1)) * 100,
            modelVariety: this.patchStatistics.navigationsByModel.size,
            modeVariety: this.patchStatistics.navigationsByMode.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理支持的模型
        this.supportedModels.clear();
        
        // 清理视图模式
        this.viewModes.clear();
        
        // 清理统计
        this.patchStatistics.navigationsByMode.clear();
        this.patchStatistics.navigationsByModel.clear();
        
        // 重置统计
        this.patchStatistics = {
            totalOpenings: 0,
            calendarEventOpenings: 0,
            otherModelOpenings: 0,
            dropdownCloses: 0,
            breadcrumbClears: 0,
            actionExecutions: 0,
            navigationsByMode: new Map(),
            navigationsByModel: new Map()
        };
    }
}

// 使用示例
const menuPatchManager = new ActivityMenuPatchManager();

// 应用补丁
menuPatchManager.applyPatch();

// 注册自定义模型配置
menuPatchManager.registerModelConfig('calendar.meeting', {
    name: 'Calendar Meeting',
    action: 'calendar.action_calendar_meeting',
    defaultMode: 'week',
    defaultFilters: ['search_default_today'],
    clearBreadcrumbs: true,
    closeDropdown: true
});

// 注册自定义视图模式
menuPatchManager.registerViewMode('agenda', {
    name: 'Agenda View',
    period: 'agenda',
    scale: 1,
    unit: 'event'
});

// 获取统计信息
const stats = menuPatchManager.getPatchStatistics();
console.log('Menu patch statistics:', stats);
```

## 技术特点

### 1. 智能路由
- **模型识别**: 智能识别日历事件模型
- **条件跳转**: 基于模型类型进行条件跳转
- **动作执行**: 执行特定的日历动作
- **上下文传递**: 传递适当的上下文参数

### 2. 用户体验
- **下拉关闭**: 自动关闭活动菜单下拉框
- **面包屑清理**: 清理导航面包屑
- **默认视图**: 设置合适的默认视图模式
- **过滤预设**: 预设有用的搜索过滤器

### 3. 兼容性保持
- **父类调用**: 保持非日历模型的原有行为
- **参数传递**: 完整传递原始参数
- **向后兼容**: 确保向后兼容性
- **渐进增强**: 渐进式功能增强

### 4. 配置灵活
- **上下文配置**: 灵活的上下文配置
- **视图模式**: 可配置的默认视图模式
- **过滤器**: 可配置的默认过滤器
- **行为控制**: 可控制的补丁行为

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- **导航策略**: 不同模型的导航策略
- **视图策略**: 不同的视图模式策略
- **上下文策略**: 不同的上下文配置策略

### 2. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 装饰原有菜单功能
- **行为增强**: 增强导航行为
- **透明扩展**: 透明地扩展功能

### 3. 工厂模式 (Factory Pattern)
- **动作工厂**: 创建不同的动作配置
- **上下文工厂**: 创建不同的上下文对象
- **配置工厂**: 创建不同的配置对象

### 4. 观察者模式 (Observer Pattern)
- **导航观察**: 观察导航行为
- **状态观察**: 观察菜单状态变化
- **事件观察**: 观察用户交互事件

## 注意事项

1. **动作有效性**: 确保日历动作的有效性
2. **上下文正确**: 确保上下文参数正确
3. **性能影响**: 注意补丁对性能的影响
4. **用户体验**: 确保良好的用户体验

## 扩展建议

1. **多视图支持**: 支持更多日历视图模式
2. **智能过滤**: 基于用户习惯的智能过滤
3. **快捷操作**: 添加快捷操作功能
4. **个性化**: 支持个性化配置
5. **分析跟踪**: 添加用户行为分析

该活动菜单补丁为Odoo Calendar模块提供了智能的导航功能，通过特殊的日历事件处理确保了用户能够快速便捷地访问日历视图和相关功能。

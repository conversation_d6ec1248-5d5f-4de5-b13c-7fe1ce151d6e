/*******************************************************************
*  Filepath: /calendar/static/src/core/web/store_service_patch.js  *
*  Lines: 33                                                       *
*******************************************************************/
odoo.define('@calendar/core/web/store_service_patch', ['@mail/core/common/store_service', '@web/core/l10n/dates', '@web/core/l10n/localization', '@web/core/utils/patch'], function (require) {
'use strict';
let __exports = {};
/* @odoo-module */

const { Store } = require("@mail/core/common/store_service");

const { deserializeDateTime, formatDateTime } = require("@web/core/l10n/dates");
const { localization } = require("@web/core/l10n/localization");
const { patch } = require("@web/core/utils/patch");

/** @type {import("models").Store} */
const StorePatch = {
    onUpdateActivityGroups() {
        super.onUpdateActivityGroups(...arguments);
        for (const group of Object.values(this.activityGroups)) {
            if (group.type === "meeting") {
                for (const meeting of group.meetings) {
                    if (meeting.start) {
                        const date = deserializeDateTime(meeting.start);
                        meeting.formattedStart = formatDateTime(date, {
                            format: localization.timeFormat,
                        });
                    }
                }
            }
        }
    },
};
patch(Store.prototype, StorePatch);

return __exports;
});
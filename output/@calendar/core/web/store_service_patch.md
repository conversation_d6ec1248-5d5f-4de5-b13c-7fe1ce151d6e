# StoreServicePatch - 存储服务补丁

## 概述

`store_service_patch.js` 是 Odoo Calendar 模块的存储服务补丁文件，负责扩展邮件模块中的存储服务。该模块包含33行代码，是一个功能专门的补丁模块，专门用于为活动组更新添加会议时间格式化功能，具备日期时间反序列化、本地化格式化、会议数据处理等特性，是活动数据展示的重要扩展。

## 文件信息
- **路径**: `/calendar/static/src/core/web/store_service_patch.js`
- **行数**: 33
- **模块**: `@calendar/core/web/store_service_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/store_service'              // 邮件存储服务
'@web/core/l10n/dates'                         // 日期本地化
'@web/core/l10n/localization'                  // 本地化服务
'@web/core/utils/patch'                        // 补丁工具
```

## 核心功能

### 1. 补丁定义

```javascript
const StorePatch = {
    onUpdateActivityGroups() {
        super.onUpdateActivityGroups(...arguments);
        for (const group of Object.values(this.activityGroups)) {
            if (group.type === "meeting") {
                for (const meeting of group.meetings) {
                    if (meeting.start) {
                        const date = deserializeDateTime(meeting.start);
                        meeting.formattedStart = formatDateTime(date, {
                            format: localization.timeFormat,
                        });
                    }
                }
            }
        }
    },
};
patch(Store.prototype, StorePatch);
```

**补丁特性**:
- **方法扩展**: 扩展onUpdateActivityGroups方法
- **父类调用**: 保持原有功能
- **会议处理**: 专门处理会议类型活动组
- **时间格式化**: 格式化会议开始时间

### 2. 活动组遍历

```javascript
for (const group of Object.values(this.activityGroups)) {
    if (group.type === "meeting") {
        // 处理会议组
    }
}
```

**遍历功能**:
- **组遍历**: 遍历所有活动组
- **类型过滤**: 只处理会议类型组
- **对象值**: 使用Object.values获取组
- **条件处理**: 基于类型进行条件处理

### 3. 会议时间处理

```javascript
for (const meeting of group.meetings) {
    if (meeting.start) {
        const date = deserializeDateTime(meeting.start);
        meeting.formattedStart = formatDateTime(date, {
            format: localization.timeFormat,
        });
    }
}
```

**时间处理功能**:
- **会议遍历**: 遍历组中的所有会议
- **开始时间检查**: 检查会议是否有开始时间
- **日期反序列化**: 反序列化日期时间字符串
- **格式化**: 使用本地化时间格式格式化
- **属性添加**: 添加格式化后的开始时间属性

## 使用场景

### 1. 存储服务补丁管理器

```javascript
// 存储服务补丁管理器
class StoreServicePatchManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置补丁配置
        this.patchConfig = {
            enableTimeFormatting: true,
            enableDateFormatting: true,
            enableDurationFormatting: false,
            enableTimezoneHandling: true,
            enableCustomFormats: false,
            enableCaching: true,
            cacheTimeout: 300000 // 5分钟
        };
        
        // 设置时间格式
        this.timeFormats = new Map([
            ['12hour', {
                name: '12 Hour',
                format: 'h:mm A',
                example: '2:30 PM',
                locale: 'en-US'
            }],
            ['24hour', {
                name: '24 Hour',
                format: 'HH:mm',
                example: '14:30',
                locale: 'en-GB'
            }],
            ['short', {
                name: 'Short',
                format: 'h:mm',
                example: '2:30',
                locale: 'en-US'
            }],
            ['long', {
                name: 'Long',
                format: 'h:mm:ss A',
                example: '2:30:45 PM',
                locale: 'en-US'
            }]
        ]);
        
        // 设置活动组类型
        this.activityGroupTypes = new Map([
            ['meeting', {
                name: 'Meeting',
                hasTime: true,
                hasDate: true,
                hasDuration: true,
                timeFields: ['start', 'end'],
                formatFields: ['formattedStart', 'formattedEnd', 'formattedDuration']
            }],
            ['call', {
                name: 'Phone Call',
                hasTime: true,
                hasDate: true,
                hasDuration: true,
                timeFields: ['start', 'end'],
                formatFields: ['formattedStart', 'formattedEnd']
            }],
            ['email', {
                name: 'Email',
                hasTime: false,
                hasDate: true,
                hasDuration: false,
                timeFields: ['date_deadline'],
                formatFields: ['formattedDeadline']
            }],
            ['todo', {
                name: 'To Do',
                hasTime: false,
                hasDate: true,
                hasDuration: false,
                timeFields: ['date_deadline'],
                formatFields: ['formattedDeadline']
            }]
        ]);
        
        // 设置补丁统计
        this.patchStatistics = {
            totalUpdates: 0,
            groupsProcessed: 0,
            meetingsProcessed: 0,
            timeFormattingOperations: 0,
            cacheHits: 0,
            cacheMisses: 0,
            formattingErrors: 0,
            averageProcessingTime: 0,
            totalProcessingTime: 0
        };
        
        // 设置格式化缓存
        this.formattingCache = new Map();
        
        this.initializePatchSystem();
    }
    
    // 初始化补丁系统
    initializePatchSystem() {
        // 创建增强的补丁
        this.createEnhancedPatch();
        
        // 设置格式化系统
        this.setupFormattingSystem();
        
        // 设置缓存系统
        this.setupCacheSystem();
        
        // 设置监控系统
        this.setupMonitoringSystem();
    }
    
    // 创建增强的补丁
    createEnhancedPatch() {
        this.enhancedPatch = {
            // 增强的活动组更新
            onUpdateActivityGroups() {
                try {
                    // 记录更新开始
                    const startTime = this.recordUpdateStart();
                    
                    // 调用父类方法
                    super.onUpdateActivityGroups(...arguments);
                    
                    // 处理活动组
                    this.processActivityGroups();
                    
                    // 记录更新完成
                    this.recordUpdateComplete(startTime);
                    
                } catch (error) {
                    this.handleUpdateError(error);
                }
            },
            
            // 处理活动组
            processActivityGroups() {
                for (const group of Object.values(this.activityGroups)) {
                    this.processActivityGroup(group);
                }
            },
            
            // 处理单个活动组
            processActivityGroup(group) {
                // 记录组处理
                this.recordGroupProcessing();
                
                // 获取组类型配置
                const groupConfig = this.getGroupTypeConfig(group.type);
                
                if (groupConfig && groupConfig.hasTime) {
                    this.processTimeBasedGroup(group, groupConfig);
                }
            },
            
            // 处理基于时间的组
            processTimeBasedGroup(group, config) {
                if (group.type === "meeting" && group.meetings) {
                    this.processMeetings(group.meetings, config);
                } else if (group.activities) {
                    this.processActivities(group.activities, config);
                }
            },
            
            // 处理会议
            processMeetings(meetings, config) {
                for (const meeting of meetings) {
                    this.processMeeting(meeting, config);
                }
            },
            
            // 处理单个会议
            processMeeting(meeting, config) {
                // 记录会议处理
                this.recordMeetingProcessing();
                
                // 处理时间字段
                for (const timeField of config.timeFields) {
                    if (meeting[timeField]) {
                        this.formatTimeField(meeting, timeField);
                    }
                }
                
                // 计算持续时间
                if (config.hasDuration && meeting.start && meeting.end) {
                    this.calculateDuration(meeting);
                }
            },
            
            // 处理活动
            processActivities(activities, config) {
                for (const activity of activities) {
                    this.processActivity(activity, config);
                }
            },
            
            // 处理单个活动
            processActivity(activity, config) {
                // 处理时间字段
                for (const timeField of config.timeFields) {
                    if (activity[timeField]) {
                        this.formatTimeField(activity, timeField);
                    }
                }
            },
            
            // 格式化时间字段
            formatTimeField(item, fieldName) {
                try {
                    // 记录格式化操作
                    this.recordFormattingOperation();
                    
                    // 检查缓存
                    const cacheKey = this.getCacheKey(item[fieldName], fieldName);
                    const cached = this.getFromCache(cacheKey);
                    
                    if (cached) {
                        item[this.getFormattedFieldName(fieldName)] = cached;
                        this.recordCacheHit();
                        return;
                    }
                    
                    // 反序列化日期时间
                    const date = deserializeDateTime(item[fieldName]);
                    
                    // 获取格式配置
                    const formatConfig = this.getFormatConfig(fieldName);
                    
                    // 格式化日期时间
                    const formatted = formatDateTime(date, formatConfig);
                    
                    // 设置格式化字段
                    const formattedFieldName = this.getFormattedFieldName(fieldName);
                    item[formattedFieldName] = formatted;
                    
                    // 缓存结果
                    this.setToCache(cacheKey, formatted);
                    this.recordCacheMiss();
                    
                } catch (error) {
                    this.handleFormattingError(error, item, fieldName);
                }
            },
            
            // 计算持续时间
            calculateDuration(meeting) {
                try {
                    const startDate = deserializeDateTime(meeting.start);
                    const endDate = deserializeDateTime(meeting.end);
                    const duration = endDate - startDate;
                    
                    meeting.duration = duration;
                    meeting.formattedDuration = this.formatDuration(duration);
                    
                } catch (error) {
                    console.error('Duration calculation error:', error);
                }
            },
            
            // 格式化持续时间
            formatDuration(duration) {
                const hours = Math.floor(duration / (1000 * 60 * 60));
                const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
                
                if (hours > 0) {
                    return `${hours}h ${minutes}m`;
                } else {
                    return `${minutes}m`;
                }
            },
            
            // 获取组类型配置
            getGroupTypeConfig(type) {
                return this.activityGroupTypes.get(type);
            },
            
            // 获取格式配置
            getFormatConfig(fieldName) {
                const baseConfig = {
                    format: localization.timeFormat,
                };
                
                // 根据字段名自定义格式
                if (fieldName === 'start' || fieldName === 'end') {
                    return {
                        ...baseConfig,
                        format: localization.timeFormat
                    };
                } else if (fieldName === 'date_deadline') {
                    return {
                        ...baseConfig,
                        format: localization.dateFormat
                    };
                }
                
                return baseConfig;
            },
            
            // 获取格式化字段名
            getFormattedFieldName(fieldName) {
                const fieldMap = {
                    'start': 'formattedStart',
                    'end': 'formattedEnd',
                    'date_deadline': 'formattedDeadline'
                };
                
                return fieldMap[fieldName] || `formatted${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)}`;
            },
            
            // 获取缓存键
            getCacheKey(value, fieldName) {
                return `${fieldName}:${value}:${localization.timeFormat}`;
            },
            
            // 从缓存获取
            getFromCache(key) {
                const cached = this.formattingCache.get(key);
                if (cached && Date.now() - cached.timestamp < this.patchConfig.cacheTimeout) {
                    return cached.value;
                }
                return null;
            },
            
            // 设置到缓存
            setToCache(key, value) {
                if (this.patchConfig.enableCaching) {
                    this.formattingCache.set(key, {
                        value: value,
                        timestamp: Date.now()
                    });
                }
            },
            
            // 记录更新开始
            recordUpdateStart() {
                this.patchStatistics.totalUpdates++;
                return Date.now();
            },
            
            // 记录更新完成
            recordUpdateComplete(startTime) {
                const processingTime = Date.now() - startTime;
                this.patchStatistics.totalProcessingTime += processingTime;
                this.updateAverageProcessingTime();
            },
            
            // 记录组处理
            recordGroupProcessing() {
                this.patchStatistics.groupsProcessed++;
            },
            
            // 记录会议处理
            recordMeetingProcessing() {
                this.patchStatistics.meetingsProcessed++;
            },
            
            // 记录格式化操作
            recordFormattingOperation() {
                this.patchStatistics.timeFormattingOperations++;
            },
            
            // 记录缓存命中
            recordCacheHit() {
                this.patchStatistics.cacheHits++;
            },
            
            // 记录缓存未命中
            recordCacheMiss() {
                this.patchStatistics.cacheMisses++;
            },
            
            // 更新平均处理时间
            updateAverageProcessingTime() {
                if (this.patchStatistics.totalUpdates > 0) {
                    this.patchStatistics.averageProcessingTime = 
                        this.patchStatistics.totalProcessingTime / this.patchStatistics.totalUpdates;
                }
            },
            
            // 处理更新错误
            handleUpdateError(error) {
                console.error('Activity groups update error:', error);
            },
            
            // 处理格式化错误
            handleFormattingError(error, item, fieldName) {
                this.patchStatistics.formattingErrors++;
                console.error('Time formatting error:', error, item, fieldName);
            },
            
            // 获取补丁信息
            getPatchInfo() {
                return {
                    config: this.patchConfig,
                    statistics: this.patchStatistics,
                    cacheSize: this.formattingCache.size,
                    supportedTypes: Array.from(this.activityGroupTypes.keys()),
                    supportedFormats: Array.from(this.timeFormats.keys())
                };
            },
            
            // 清理缓存
            clearCache() {
                this.formattingCache.clear();
            },
            
            // 清理过期缓存
            cleanupExpiredCache() {
                const now = Date.now();
                for (const [key, cached] of this.formattingCache.entries()) {
                    if (now - cached.timestamp >= this.patchConfig.cacheTimeout) {
                        this.formattingCache.delete(key);
                    }
                }
            }
        };
    }
    
    // 设置格式化系统
    setupFormattingSystem() {
        this.formattingConfig = {
            enabled: this.patchConfig.enableTimeFormatting,
            useLocalization: true,
            customFormats: this.patchConfig.enableCustomFormats
        };
    }
    
    // 设置缓存系统
    setupCacheSystem() {
        this.cacheConfig = {
            enabled: this.patchConfig.enableCaching,
            timeout: this.patchConfig.cacheTimeout,
            maxSize: 1000
        };
        
        // 定期清理过期缓存
        if (this.cacheConfig.enabled) {
            setInterval(() => {
                this.enhancedPatch.cleanupExpiredCache();
            }, this.cacheConfig.timeout);
        }
    }
    
    // 设置监控系统
    setupMonitoringSystem() {
        this.monitoringConfig = {
            enabled: true,
            trackUpdates: true,
            trackFormatting: true,
            trackCache: true
        };
    }
    
    // 应用补丁
    applyPatch() {
        patch(Store.prototype, this.enhancedPatch);
    }
    
    // 注册活动组类型
    registerActivityGroupType(type, config) {
        this.activityGroupTypes.set(type, config);
    }
    
    // 注册时间格式
    registerTimeFormat(name, config) {
        this.timeFormats.set(name, config);
    }
    
    // 获取补丁统计
    getPatchStatistics() {
        return {
            ...this.patchStatistics,
            cacheHitRate: this.patchStatistics.timeFormattingOperations > 0 ? 
                (this.patchStatistics.cacheHits / this.patchStatistics.timeFormattingOperations) * 100 : 0,
            formattingErrorRate: this.patchStatistics.timeFormattingOperations > 0 ? 
                (this.patchStatistics.formattingErrors / this.patchStatistics.timeFormattingOperations) * 100 : 0,
            averageProcessingTime: this.patchStatistics.averageProcessingTime,
            typeVariety: this.activityGroupTypes.size,
            formatVariety: this.timeFormats.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理时间格式
        this.timeFormats.clear();
        
        // 清理活动组类型
        this.activityGroupTypes.clear();
        
        // 清理缓存
        this.formattingCache.clear();
        
        // 重置统计
        this.patchStatistics = {
            totalUpdates: 0,
            groupsProcessed: 0,
            meetingsProcessed: 0,
            timeFormattingOperations: 0,
            cacheHits: 0,
            cacheMisses: 0,
            formattingErrors: 0,
            averageProcessingTime: 0,
            totalProcessingTime: 0
        };
    }
}

// 使用示例
const storePatchManager = new StoreServicePatchManager();

// 应用补丁
storePatchManager.applyPatch();

// 注册自定义活动组类型
storePatchManager.registerActivityGroupType('webinar', {
    name: 'Webinar',
    hasTime: true,
    hasDate: true,
    hasDuration: true,
    timeFields: ['start', 'end'],
    formatFields: ['formattedStart', 'formattedEnd', 'formattedDuration']
});

// 注册自定义时间格式
storePatchManager.registerTimeFormat('iso', {
    name: 'ISO Format',
    format: 'YYYY-MM-DDTHH:mm:ss',
    example: '2023-12-25T14:30:00',
    locale: 'en-US'
});

// 获取统计信息
const stats = storePatchManager.getPatchStatistics();
console.log('Store patch statistics:', stats);
```

## 技术特点

### 1. 时间格式化
- **本地化支持**: 使用本地化时间格式
- **日期反序列化**: 反序列化日期时间字符串
- **格式化输出**: 格式化为用户友好的时间显示
- **字段扩展**: 为对象添加格式化字段

### 2. 数据处理
- **类型过滤**: 只处理会议类型的活动组
- **条件检查**: 检查时间字段是否存在
- **批量处理**: 批量处理多个会议
- **属性添加**: 动态添加格式化属性

### 3. 存储集成
- **补丁机制**: 使用补丁扩展存储服务
- **方法重写**: 重写活动组更新方法
- **父类调用**: 保持原有功能
- **透明扩展**: 透明地扩展功能

### 4. 本地化集成
- **时间格式**: 使用本地化时间格式
- **日期工具**: 集成日期处理工具
- **格式配置**: 支持格式配置
- **多语言**: 支持多语言环境

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 装饰存储服务功能
- **透明扩展**: 透明地扩展功能
- **行为增强**: 增强数据处理行为

### 2. 策略模式 (Strategy Pattern)
- **格式化策略**: 不同的时间格式化策略
- **处理策略**: 不同的数据处理策略
- **缓存策略**: 不同的缓存策略

### 3. 观察者模式 (Observer Pattern)
- **数据观察**: 观察活动组数据变化
- **更新观察**: 观察更新事件
- **状态观察**: 观察存储状态变化

### 4. 工厂模式 (Factory Pattern)
- **格式化工厂**: 创建格式化器
- **配置工厂**: 创建配置对象
- **处理器工厂**: 创建数据处理器

## 注意事项

1. **性能影响**: 注意格式化对性能的影响
2. **内存使用**: 控制缓存的内存使用
3. **时区处理**: 正确处理时区转换
4. **错误处理**: 处理格式化错误

## 扩展建议

1. **更多格式**: 支持更多时间格式选项
2. **缓存优化**: 优化缓存策略和性能
3. **批量格式化**: 支持批量格式化操作
4. **自定义格式**: 支持用户自定义格式
5. **时区支持**: 增强时区处理功能

该存储服务补丁为Odoo Calendar模块提供了重要的时间格式化功能，通过本地化的时间显示确保了用户界面的友好性和国际化支持。

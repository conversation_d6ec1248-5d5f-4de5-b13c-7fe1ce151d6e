# AskRecurrenceUpdatePolicyDialog - 重复更新策略对话框

## 概述

`ask_recurrence_update_policy_dialog.js` 是 Odoo Calendar 模块的重复更新策略对话框组件，负责显示重复事件更新策略选择界面。该模块包含53行代码，是一个功能完整的对话框组件，专门用于让用户选择重复事件的更新策略，具备选项管理、状态控制、国际化支持等特性，是重复事件管理的核心交互组件。

## 文件信息
- **路径**: `/calendar/static/src/views/ask_recurrence_update_policy_dialog.js`
- **行数**: 53
- **模块**: `@calendar/views/ask_recurrence_update_policy_dialog`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'                   // 翻译服务
'@web/core/dialog/dialog'                      // 对话框组件
'@odoo/owl'                                    // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
class AskRecurrenceUpdatePolicyDialog extends Component {
    static template = "calendar.AskRecurrenceUpdatePolicyDialog";
    static components = {
        Dialog,
    };
    static props = {
        confirm: Function,
        close: Function,
    };
}
```

**组件特性**:
- **继承扩展**: 继承OWL组件基类
- **模板定制**: 使用专门的对话框模板
- **对话框集成**: 集成标准对话框组件
- **属性定义**: 定义确认和关闭回调属性

### 2. 策略选项

```javascript
setup() {
    this.possibleValues = {
        self_only: {
            checked: true,
            label: _t("This event"),
        },
        future_events: {
            checked: false,
            label: _t("This and following events"),
        },
        all_events: {
            checked: false,
            label: _t("All events"),
        },
    };
}
```

**选项特性**:
- **策略定义**: 定义三种更新策略
- **默认选择**: 默认选择"仅此事件"
- **国际化**: 支持多语言标签
- **状态管理**: 管理选项选中状态

### 3. 选择管理

```javascript
get selected() {
    return Object.entries(this.possibleValues).find((state) => state[1].checked)[0];
}

set selected(val) {
    this.possibleValues[this.selected].checked = false;
    this.possibleValues[val].checked = true;
}
```

**管理功能**:
- **获取选择**: 获取当前选中的策略
- **设置选择**: 设置新的选中策略
- **状态切换**: 切换选项的选中状态
- **单选控制**: 确保只有一个选项被选中

### 4. 确认操作

```javascript
confirm() {
    this.props.confirm(this.selected);
    this.props.close();
}
```

**确认功能**:
- **策略传递**: 传递选中的策略
- **回调执行**: 执行确认回调
- **对话框关闭**: 关闭对话框
- **操作完成**: 完成策略选择操作

## 使用场景

### 1. 重复策略对话框管理器

```javascript
// 重复策略对话框管理器
class RecurrencePolicyDialogManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置对话框配置
        this.dialogConfig = {
            enableKeyboardNavigation: true,
            enableEscapeClose: true,
            enableClickOutsideClose: false,
            enableRememberChoice: true,
            enableAdvancedOptions: false,
            showPolicyDescription: true,
            showPolicyIcons: true,
            defaultPolicy: 'self_only'
        };
        
        // 设置策略选项
        this.policyOptions = new Map([
            ['self_only', {
                key: 'self_only',
                label: 'This event',
                description: 'Update only this occurrence of the event',
                icon: 'fa-calendar',
                shortcut: '1',
                scope: 'single',
                isDefault: true,
                order: 1
            }],
            ['future_events', {
                key: 'future_events',
                label: 'This and following events',
                description: 'Update this and all future occurrences',
                icon: 'fa-calendar-plus',
                shortcut: '2',
                scope: 'future',
                isDefault: false,
                order: 2
            }],
            ['all_events', {
                key: 'all_events',
                label: 'All events',
                description: 'Update all occurrences in the series',
                icon: 'fa-calendar-alt',
                shortcut: '3',
                scope: 'all',
                isDefault: false,
                order: 3
            }]
        ]);
        
        // 设置对话框状态
        this.dialogState = {
            isOpen: false,
            selectedPolicy: null,
            rememberChoice: false,
            showAdvanced: false,
            keyboardNavigation: true
        };
        
        // 设置对话框统计
        this.dialogStatistics = {
            totalOpens: 0,
            confirmations: 0,
            cancellations: 0,
            policySelections: new Map(),
            keyboardUsage: 0,
            rememberChoiceUsage: 0,
            averageDecisionTime: 0,
            totalDecisionTime: 0
        };
        
        this.initializeDialogSystem();
    }
    
    // 初始化对话框系统
    initializeDialogSystem() {
        // 创建增强的对话框
        this.createEnhancedDialog();
        
        // 设置键盘系统
        this.setupKeyboardSystem();
        
        // 设置选项系统
        this.setupOptionSystem();
        
        // 设置统计系统
        this.setupStatisticsSystem();
    }
    
    // 创建增强的对话框
    createEnhancedDialog() {
        const originalDialog = AskRecurrenceUpdatePolicyDialog;
        
        this.EnhancedRecurrencePolicyDialog = class extends originalDialog {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加键盘支持
                this.addKeyboardSupport();
                
                // 添加高级选项
                this.addAdvancedOptions();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    openTime: Date.now(),
                    interactionCount: 0,
                    keyboardUsed: false,
                    rememberChoice: false,
                    showDescriptions: this.dialogConfig.showPolicyDescription
                };
                
                // 增强的策略选项
                this.enhancedPossibleValues = this.createEnhancedOptions();
                
                // 添加增强方法
                this.addEnhancedMethods();
                
                // 初始化对话框
                this.initializeDialog();
            }
            
            createEnhancedOptions() {
                const options = {};
                
                for (const [key, option] of this.policyOptions.entries()) {
                    options[key] = {
                        checked: option.isDefault,
                        label: _t(option.label),
                        description: _t(option.description),
                        icon: option.icon,
                        shortcut: option.shortcut,
                        scope: option.scope,
                        order: option.order
                    };
                }
                
                return options;
            }
            
            addEnhancedMethods() {
                // 增强的选择获取
                this.enhancedGetSelected = () => {
                    const selected = Object.entries(this.enhancedPossibleValues)
                        .find(([key, option]) => option.checked);
                    
                    return selected ? selected[0] : this.dialogConfig.defaultPolicy;
                };
                
                // 增强的选择设置
                this.enhancedSetSelected = (val) => {
                    // 取消当前选择
                    const currentSelected = this.enhancedGetSelected();
                    if (currentSelected) {
                        this.enhancedPossibleValues[currentSelected].checked = false;
                    }
                    
                    // 设置新选择
                    if (this.enhancedPossibleValues[val]) {
                        this.enhancedPossibleValues[val].checked = true;
                        this.recordPolicySelection(val);
                    }
                };
                
                // 增强的确认操作
                this.enhancedConfirm = () => {
                    try {
                        // 记录确认
                        this.recordConfirmation();
                        
                        // 获取选中策略
                        const selectedPolicy = this.enhancedGetSelected();
                        
                        // 记录决策时间
                        const decisionTime = Date.now() - this.enhancedState.openTime;
                        this.recordDecisionTime(decisionTime);
                        
                        // 处理记住选择
                        if (this.enhancedState.rememberChoice) {
                            this.handleRememberChoice(selectedPolicy);
                        }
                        
                        // 执行确认回调
                        this.props.confirm(selectedPolicy);
                        
                        // 关闭对话框
                        this.props.close();
                        
                    } catch (error) {
                        this.handleConfirmError(error);
                    }
                };
                
                // 处理键盘事件
                this.handleKeyboardEvent = (event) => {
                    this.enhancedState.keyboardUsed = true;
                    this.recordKeyboardUsage();
                    
                    switch (event.key) {
                        case '1':
                        case '2':
                        case '3':
                            this.selectByShortcut(event.key);
                            event.preventDefault();
                            break;
                        case 'Enter':
                            this.enhancedConfirm();
                            event.preventDefault();
                            break;
                        case 'Escape':
                            this.handleCancel();
                            event.preventDefault();
                            break;
                        case 'ArrowUp':
                        case 'ArrowDown':
                            this.navigateOptions(event.key === 'ArrowUp' ? -1 : 1);
                            event.preventDefault();
                            break;
                    }
                };
                
                // 通过快捷键选择
                this.selectByShortcut = (shortcut) => {
                    for (const [key, option] of Object.entries(this.enhancedPossibleValues)) {
                        if (option.shortcut === shortcut) {
                            this.enhancedSetSelected(key);
                            break;
                        }
                    }
                };
                
                // 导航选项
                this.navigateOptions = (direction) => {
                    const options = Object.keys(this.enhancedPossibleValues);
                    const currentIndex = options.indexOf(this.enhancedGetSelected());
                    const newIndex = (currentIndex + direction + options.length) % options.length;
                    
                    this.enhancedSetSelected(options[newIndex]);
                };
                
                // 处理取消
                this.handleCancel = () => {
                    this.recordCancellation();
                    this.props.close();
                };
                
                // 切换记住选择
                this.toggleRememberChoice = () => {
                    this.enhancedState.rememberChoice = !this.enhancedState.rememberChoice;
                    
                    if (this.enhancedState.rememberChoice) {
                        this.recordRememberChoiceUsage();
                    }
                };
                
                // 切换描述显示
                this.toggleDescriptions = () => {
                    this.enhancedState.showDescriptions = !this.enhancedState.showDescriptions;
                };
                
                // 处理记住选择
                this.handleRememberChoice = (policy) => {
                    // 保存用户选择到本地存储
                    try {
                        localStorage.setItem('calendar_default_recurrence_policy', policy);
                    } catch (error) {
                        console.error('Failed to save default policy:', error);
                    }
                };
                
                // 获取选项列表
                this.getOptionsList = () => {
                    return Object.entries(this.enhancedPossibleValues)
                        .map(([key, option]) => ({ key, ...option }))
                        .sort((a, b) => a.order - b.order);
                };
                
                // 获取选中选项信息
                this.getSelectedOptionInfo = () => {
                    const selectedKey = this.enhancedGetSelected();
                    return this.enhancedPossibleValues[selectedKey];
                };
                
                // 初始化对话框
                this.initializeDialog = () => {
                    this.enhancedState.openTime = Date.now();
                    this.enhancedState.interactionCount = 0;
                    this.enhancedState.keyboardUsed = false;
                    this.enhancedState.rememberChoice = false;
                    
                    // 设置键盘监听
                    if (this.dialogConfig.enableKeyboardNavigation) {
                        document.addEventListener('keydown', this.handleKeyboardEvent);
                    }
                };
                
                // 记录策略选择
                this.recordPolicySelection = (policy) => {
                    this.enhancedState.interactionCount++;
                    this.dialogStatistics.policySelections.set(
                        policy, 
                        (this.dialogStatistics.policySelections.get(policy) || 0) + 1
                    );
                };
                
                // 记录确认
                this.recordConfirmation = () => {
                    this.dialogStatistics.confirmations++;
                };
                
                // 记录取消
                this.recordCancellation = () => {
                    this.dialogStatistics.cancellations++;
                };
                
                // 记录键盘使用
                this.recordKeyboardUsage = () => {
                    this.dialogStatistics.keyboardUsage++;
                };
                
                // 记录记住选择使用
                this.recordRememberChoiceUsage = () => {
                    this.dialogStatistics.rememberChoiceUsage++;
                };
                
                // 记录决策时间
                this.recordDecisionTime = (time) => {
                    this.dialogStatistics.totalDecisionTime += time;
                    this.updateAverageDecisionTime();
                };
                
                // 更新平均决策时间
                this.updateAverageDecisionTime = () => {
                    const totalDecisions = this.dialogStatistics.confirmations + this.dialogStatistics.cancellations;
                    if (totalDecisions > 0) {
                        this.dialogStatistics.averageDecisionTime = 
                            this.dialogStatistics.totalDecisionTime / totalDecisions;
                    }
                };
                
                // 处理确认错误
                this.handleConfirmError = (error) => {
                    console.error('Dialog confirm error:', error);
                };
                
                // 获取对话框信息
                this.getDialogInfo = () => {
                    return {
                        openTime: this.enhancedState.openTime,
                        interactionCount: this.enhancedState.interactionCount,
                        keyboardUsed: this.enhancedState.keyboardUsed,
                        rememberChoice: this.enhancedState.rememberChoice,
                        selectedPolicy: this.enhancedGetSelected(),
                        statistics: this.dialogStatistics,
                        config: this.dialogConfig
                    };
                };
                
                // 清理资源
                this.cleanup = () => {
                    if (this.dialogConfig.enableKeyboardNavigation) {
                        document.removeEventListener('keydown', this.handleKeyboardEvent);
                    }
                };
            }
            
            // 重写原始方法
            get selected() {
                return this.enhancedGetSelected();
            }
            
            set selected(val) {
                this.enhancedSetSelected(val);
            }
            
            confirm() {
                this.enhancedConfirm();
            }
            
            // 组件销毁时清理
            willUnmount() {
                this.cleanup();
            }
        };
    }
    
    // 创建对话框
    createDialog(props) {
        this.dialogStatistics.totalOpens++;
        return new this.EnhancedRecurrencePolicyDialog(props);
    }
    
    // 获取对话框统计
    getDialogStatistics() {
        return {
            ...this.dialogStatistics,
            confirmationRate: this.dialogStatistics.totalOpens > 0 ? 
                (this.dialogStatistics.confirmations / this.dialogStatistics.totalOpens) * 100 : 0,
            cancellationRate: this.dialogStatistics.totalOpens > 0 ? 
                (this.dialogStatistics.cancellations / this.dialogStatistics.totalOpens) * 100 : 0,
            keyboardUsageRate: this.dialogStatistics.totalOpens > 0 ? 
                (this.dialogStatistics.keyboardUsage / this.dialogStatistics.totalOpens) * 100 : 0,
            rememberChoiceRate: this.dialogStatistics.confirmations > 0 ? 
                (this.dialogStatistics.rememberChoiceUsage / this.dialogStatistics.confirmations) * 100 : 0,
            averageDecisionTime: this.dialogStatistics.averageDecisionTime,
            policyDistribution: this.getPolicyDistribution()
        };
    }
    
    // 获取策略分布
    getPolicyDistribution() {
        const distribution = {};
        const total = Array.from(this.dialogStatistics.policySelections.values())
            .reduce((sum, count) => sum + count, 0);
        
        for (const [policy, count] of this.dialogStatistics.policySelections.entries()) {
            distribution[policy] = total > 0 ? (count / total) * 100 : 0;
        }
        
        return distribution;
    }
    
    // 销毁管理器
    destroy() {
        // 清理策略选项
        this.policyOptions.clear();
        
        // 清理统计
        this.dialogStatistics.policySelections.clear();
        
        // 重置统计
        this.dialogStatistics = {
            totalOpens: 0,
            confirmations: 0,
            cancellations: 0,
            policySelections: new Map(),
            keyboardUsage: 0,
            rememberChoiceUsage: 0,
            averageDecisionTime: 0,
            totalDecisionTime: 0
        };
    }
}

// 使用示例
const dialogManager = new RecurrencePolicyDialogManager();

// 创建对话框
const dialog = dialogManager.createDialog({
    confirm: (policy) => {
        console.log('Selected policy:', policy);
    },
    close: () => {
        console.log('Dialog closed');
    }
});

// 获取统计信息
const stats = dialogManager.getDialogStatistics();
console.log('Dialog statistics:', stats);
```

## 技术特点

### 1. 选项管理
- **策略定义**: 定义三种重复更新策略
- **状态控制**: 控制选项的选中状态
- **单选机制**: 确保只有一个选项被选中
- **默认选择**: 设置默认选中选项

### 2. 国际化支持
- **多语言**: 支持多语言标签
- **翻译服务**: 集成翻译服务
- **本地化**: 支持本地化显示
- **动态翻译**: 动态翻译文本

### 3. 对话框集成
- **标准对话框**: 集成标准对话框组件
- **模板定制**: 使用专门的对话框模板
- **属性传递**: 传递回调属性
- **生命周期**: 管理对话框生命周期

### 4. 用户交互
- **确认操作**: 处理用户确认操作
- **选择切换**: 处理选项切换
- **回调执行**: 执行确认和关闭回调
- **状态反馈**: 提供操作状态反馈

## 设计模式

### 1. 状态模式 (State Pattern)
- **选项状态**: 管理选项的选中状态
- **对话框状态**: 管理对话框状态
- **交互状态**: 管理用户交互状态

### 2. 观察者模式 (Observer Pattern)
- **选择观察**: 观察选项选择变化
- **状态观察**: 观察状态变化
- **事件观察**: 观察用户事件

### 3. 命令模式 (Command Pattern)
- **确认命令**: 封装确认操作
- **选择命令**: 封装选择操作
- **关闭命令**: 封装关闭操作

### 4. 模板方法模式 (Template Method Pattern)
- **对话框模板**: 定义对话框显示模板
- **交互模板**: 定义用户交互模板
- **确认模板**: 定义确认流程模板

## 注意事项

1. **状态一致性**: 确保选项状态的一致性
2. **回调处理**: 正确处理确认和关闭回调
3. **国际化**: 确保所有文本都支持国际化
4. **用户体验**: 提供清晰的用户界面

## 扩展建议

1. **键盘支持**: 添加键盘导航支持
2. **记住选择**: 支持记住用户选择
3. **高级选项**: 添加高级策略选项
4. **预览功能**: 添加策略效果预览
5. **帮助信息**: 添加策略说明和帮助

该重复更新策略对话框为Odoo Calendar模块提供了完整的重复事件更新策略选择界面，通过清晰的选项管理和良好的用户交互确保了重复事件管理的最佳用户体验。

# Calendar Views Module

## 概述

Calendar Views 模块是 Odoo Calendar 应用的核心视图层，提供了完整的日历事件显示、编辑和管理功能。该模块包含了多种视图类型、字段组件、小部件和对话框，为用户提供了丰富的日历交互体验。

## 模块架构

### 核心组件

```
@calendar/views/
├── attendee_calendar/          # 参与者日历视图系统
├── calendar_form/              # 日历表单系统
├── fields/                     # 字段组件
├── list_view/                  # 列表视图
├── widgets/                    # 小部件组件
├── ask_recurrence_update_policy_dialog.js  # 重复更新策略对话框
└── ask_recurrence_update_policy_hook.js    # 重复更新策略钩子
```

## 主要功能模块

### 1. 参与者日历系统 (`attendee_calendar/`)

参与者日历系统提供了专门针对参与者视角的日历视图，包含完整的MVC架构：

- **控制器** (`attendee_calendar_controller.js`): 处理参与者日历的用户交互
- **模型** (`attendee_calendar_model.js`): 管理参与者日历的数据逻辑
- **渲染器** (`attendee_calendar_renderer.js`): 负责参与者日历的视觉渲染
- **视图** (`attendee_calendar_view.js`): 组装参与者日历的完整视图

#### 子模块

- **通用组件** (`common/`): 提供参与者日历的通用弹出框和渲染器
- **年视图组件** (`year/`): 提供参与者日历的年视图特定组件

### 2. 日历表单系统 (`calendar_form/`)

日历表单系统提供了日历事件的创建和编辑功能：

- **表单控制器** (`calendar_form_controller.js`): 处理表单交互，集成视频通话功能
- **表单视图** (`calendar_form_view.js`): 定义日历表单视图结构
- **快速创建** (`calendar_quick_create.js`): 提供快速创建日历事件的功能

### 3. 字段组件 (`fields/`)

字段组件提供了专门的日历字段类型：

- **参与者标签列表** (`attendee_tags_list.js`): 显示参与者标签
- **多对多参与者字段** (`many2many_attendee.js`): 管理参与者关系
- **可展开参与者字段** (`many2many_attendee_expandable.js`): 提供可展开的参与者列表

### 4. 列表视图 (`list_view/`)

- **日历列表视图** (`calendar_list_view.js`): 提供日历事件的列表显示

### 5. 小部件 (`widgets/`)

- **星期选择组件** (`calendar_week_days.js`): 用于重复事件的星期选择

### 6. 对话框和钩子

- **重复更新策略对话框** (`ask_recurrence_update_policy_dialog.js`): 询问重复事件更新策略
- **重复更新策略钩子** (`ask_recurrence_update_policy_hook.js`): 提供重复策略询问功能

## 技术特性

### 1. 模块化设计

- **分层架构**: 清晰的MVC分层
- **组件复用**: 通用组件可在多个视图中复用
- **功能分离**: 不同功能模块独立开发和维护

### 2. 参与者管理

- **状态显示**: 显示参与者的接受/拒绝/待定状态
- **权限控制**: 基于用户角色的权限管理
- **批量操作**: 支持批量参与者操作

### 3. 视图集成

- **多视图支持**: 支持日历、表单、列表等多种视图
- **视图切换**: 无缝的视图间切换
- **数据同步**: 视图间数据实时同步

### 4. 用户体验

- **响应式设计**: 适配不同屏幕尺寸
- **交互优化**: 流畅的用户交互体验
- **视觉反馈**: 清晰的状态和操作反馈

## 设计模式

### 1. MVC模式
- **Model**: 数据管理和业务逻辑
- **View**: 用户界面和视图定义
- **Controller**: 用户交互和控制逻辑

### 2. 组件模式
- **可复用组件**: 通用组件设计
- **组合模式**: 复杂组件由简单组件组合
- **继承扩展**: 基于继承的功能扩展

### 3. 观察者模式
- **事件监听**: 监听用户交互事件
- **状态观察**: 观察数据状态变化
- **响应式更新**: 自动响应状态变化

### 4. 工厂模式
- **组件工厂**: 动态创建组件实例
- **视图工厂**: 创建不同类型的视图
- **字段工厂**: 创建各种字段类型

## 开发指南

### 1. 添加新视图

```javascript
// 1. 创建视图组件
class MyCalendarView extends Component {
    static template = "my_module.MyCalendarView";
    // 视图逻辑
}

// 2. 注册视图
registry.category("views").add("my_calendar_view", {
    type: "calendar",
    display_name: "My Calendar",
    icon: "fa fa-calendar",
    multiRecord: true,
    Controller: MyCalendarController,
    Model: MyCalendarModel,
    Renderer: MyCalendarRenderer,
});
```

### 2. 创建自定义字段

```javascript
// 1. 定义字段组件
class MyCalendarField extends Component {
    static template = "my_module.MyCalendarField";
    // 字段逻辑
}

// 2. 注册字段
registry.category("fields").add("my_calendar_field", {
    component: MyCalendarField,
    displayName: "My Calendar Field",
    supportedTypes: ["char", "text"],
});
```

### 3. 添加小部件

```javascript
// 1. 创建小部件
class MyCalendarWidget extends Component {
    static template = "my_module.MyCalendarWidget";
    // 小部件逻辑
}

// 2. 注册小部件
registry.category("view_widgets").add("my_calendar_widget", {
    component: MyCalendarWidget,
});
```

## 最佳实践

### 1. 代码组织

- **模块分离**: 按功能模块组织代码
- **命名规范**: 使用清晰的命名约定
- **文档完整**: 提供完整的代码文档

### 2. 性能优化

- **懒加载**: 按需加载组件
- **缓存机制**: 合理使用缓存
- **批量操作**: 减少服务器请求次数

### 3. 用户体验

- **响应式**: 确保在不同设备上的良好体验
- **无障碍**: 支持键盘导航和屏幕阅读器
- **国际化**: 支持多语言显示

### 4. 测试策略

- **单元测试**: 测试单个组件功能
- **集成测试**: 测试组件间交互
- **端到端测试**: 测试完整用户流程

## 扩展指南

### 1. 自定义参与者状态

```javascript
// 扩展参与者状态
const customAttendeeStatuses = {
    'maybe': {
        name: 'Maybe',
        icon: 'fa-question-circle',
        color: '#ffc107'
    }
};
```

### 2. 添加新的重复规则

```javascript
// 扩展重复规则
const customRecurrenceRules = {
    'custom_weekly': {
        name: 'Custom Weekly',
        pattern: 'FREQ=WEEKLY;INTERVAL=2'
    }
};
```

### 3. 自定义视图模式

```javascript
// 添加新的视图模式
const customViewModes = {
    'timeline': {
        name: 'Timeline View',
        component: TimelineView
    }
};
```

## 依赖关系

### 核心依赖

- **@odoo/owl**: OWL框架
- **@web/core**: Web核心模块
- **@web/views**: 视图基础模块

### 模块间依赖

- **calendar_form** → **fields**: 表单使用字段组件
- **attendee_calendar** → **fields**: 参与者视图使用参与者字段
- **所有视图** → **ask_recurrence_update_policy**: 重复事件处理

## 版本兼容性

- **Odoo 17.0+**: 完全支持
- **OWL 2.0+**: 需要OWL 2.0或更高版本
- **现代浏览器**: 支持ES6+的现代浏览器

## 贡献指南

1. **代码风格**: 遵循项目代码风格规范
2. **测试覆盖**: 新功能需要包含测试
3. **文档更新**: 更新相关文档
4. **向后兼容**: 保持API的向后兼容性

## 许可证

本模块遵循 Odoo 的开源许可证。

---

*更多详细信息请参考各子模块的具体文档。*

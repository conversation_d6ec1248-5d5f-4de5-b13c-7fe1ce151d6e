/********************************************************************************
*  Filepath: /calendar/static/src/views/ask_recurrence_update_policy_dialog.js  *
*  Lines: 53                                                                    *
********************************************************************************/
odoo.define('@calendar/views/ask_recurrence_update_policy_dialog', ['@web/core/l10n/translation', '@web/core/dialog/dialog', '@odoo/owl'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { _t } = require("@web/core/l10n/translation");
const { Dialog } = require("@web/core/dialog/dialog");
const { Component } = require("@odoo/owl");

const AskRecurrenceUpdatePolicyDialog = __exports.AskRecurrenceUpdatePolicyDialog = class AskRecurrenceUpdatePolicyDialog extends Component {
    static template = "calendar.AskRecurrenceUpdatePolicyDialog";
    static components = {
        Dialog,
    };
    static props = {
        confirm: Function,
        close: Function,
    };

    setup() {
        this.possibleValues = {
            self_only: {
                checked: true,
                label: _t("This event"),
            },
            future_events: {
                checked: false,
                label: _t("This and following events"),
            },
            all_events: {
                checked: false,
                label: _t("All events"),
            },
        };
    }

    get selected() {
        return Object.entries(this.possibleValues).find((state) => state[1].checked)[0];
    }

    set selected(val) {
        this.possibleValues[this.selected].checked = false;
        this.possibleValues[val].checked = true;
    }

    confirm() {
        this.props.confirm(this.selected);
        this.props.close();
    }
}

return __exports;
});
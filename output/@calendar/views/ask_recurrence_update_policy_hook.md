# AskRecurrenceUpdatePolicyHook - 重复更新策略钩子

## 概述

`ask_recurrence_update_policy_hook.js` 是 Odoo Calendar 模块的重复更新策略钩子，负责提供询问重复事件更新策略的功能。该模块包含25行代码，是一个功能专门的钩子组件，专门用于处理重复事件更新时的策略选择，具备对话框集成、Promise封装、服务注入等特性，是重复事件管理的核心工具。

## 文件信息
- **路径**: `/calendar/static/src/views/ask_recurrence_update_policy_hook.js`
- **行数**: 25
- **模块**: `@calendar/views/ask_recurrence_update_policy_hook`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/hooks'                        // 工具钩子
'@calendar/views/ask_recurrence_update_policy_dialog' // 重复更新策略对话框
```

## 核心功能

### 1. 策略询问函数

```javascript
function askRecurrenceUpdatePolicy(dialogService) {
    return new Promise((resolve) => {
        dialogService.add(AskRecurrenceUpdatePolicyDialog, {
            confirm: resolve,
        }, {
            onClose: resolve.bind(null, false),
        });
    });
}
```

**函数特性**:
- **Promise封装**: 使用Promise封装异步操作
- **对话框集成**: 集成重复更新策略对话框
- **回调处理**: 处理确认和关闭回调
- **结果返回**: 返回用户选择的策略

### 2. 钩子函数

```javascript
function useAskRecurrenceUpdatePolicy() {
    const dialogService = useService("dialog");
    return askRecurrenceUpdatePolicy.bind(null, dialogService);
}
```

**钩子特性**:
- **服务注入**: 注入对话框服务
- **函数绑定**: 绑定对话框服务到策略询问函数
- **钩子模式**: 遵循React钩子模式
- **易用性**: 提供简单易用的接口

## 使用场景

### 1. 重复策略管理器

```javascript
// 重复策略管理器
class RecurrenceUpdatePolicyManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置策略配置
        this.policyConfig = {
            enablePolicyDialog: true,
            enableAutoPolicy: false,
            enableRememberChoice: true,
            enableAdvancedOptions: false,
            defaultPolicy: null,
            timeoutDuration: 30000, // 30 seconds
            showPolicyPreview: true
        };
        
        // 设置更新策略
        this.updatePolicies = new Map([
            ['self_only', {
                name: 'This event only',
                description: 'Update only this occurrence of the event',
                scope: 'single',
                icon: 'fa-calendar',
                shortcut: '1',
                isDefault: false
            }],
            ['future_events', {
                name: 'This and future events',
                description: 'Update this and all future occurrences',
                scope: 'future',
                icon: 'fa-calendar-plus',
                shortcut: '2',
                isDefault: true
            }],
            ['all_events', {
                name: 'All events',
                description: 'Update all occurrences in the series',
                scope: 'all',
                icon: 'fa-calendar-alt',
                shortcut: '3',
                isDefault: false
            }]
        ]);
        
        // 设置策略统计
        this.policyStatistics = {
            totalRequests: 0,
            policySelections: new Map(),
            timeouts: 0,
            cancellations: 0,
            averageDecisionTime: 0,
            totalDecisionTime: 0,
            rememberChoiceUsage: 0,
            autoAppliedPolicies: 0
        };
        
        // 设置用户偏好
        this.userPreferences = {
            rememberedPolicy: null,
            alwaysAsk: true,
            preferredPolicy: 'future_events',
            showAdvancedOptions: false,
            enableKeyboardShortcuts: true
        };
        
        this.initializePolicySystem();
    }
    
    // 初始化策略系统
    initializePolicySystem() {
        // 创建增强的钩子
        this.createEnhancedHook();
        
        // 设置对话框系统
        this.setupDialogSystem();
        
        // 设置偏好系统
        this.setupPreferenceSystem();
        
        // 设置统计系统
        this.setupStatisticsSystem();
    }
    
    // 创建增强的钩子
    createEnhancedHook() {
        // 增强的策略询问函数
        this.enhancedAskRecurrenceUpdatePolicy = async (dialogService, options = {}) => {
            try {
                // 记录请求
                this.recordPolicyRequest();
                
                // 检查用户偏好
                const rememberedPolicy = this.checkRememberedPolicy();
                if (rememberedPolicy && !options.forceAsk) {
                    this.recordAutoAppliedPolicy(rememberedPolicy);
                    return rememberedPolicy;
                }
                
                // 检查自动策略
                const autoPolicy = this.checkAutoPolicy(options);
                if (autoPolicy) {
                    this.recordAutoAppliedPolicy(autoPolicy);
                    return autoPolicy;
                }
                
                // 显示策略对话框
                const policy = await this.showPolicyDialog(dialogService, options);
                
                // 处理策略选择
                this.processPolicySelection(policy, options);
                
                return policy;
                
            } catch (error) {
                this.handlePolicyError(error);
                return false;
            }
        };
        
        // 增强的钩子函数
        this.enhancedUseAskRecurrenceUpdatePolicy = () => {
            const dialogService = useService("dialog");
            
            return (options = {}) => {
                return this.enhancedAskRecurrenceUpdatePolicy(dialogService, options);
            };
        };
    }
    
    // 检查记住的策略
    checkRememberedPolicy() {
        if (this.userPreferences.rememberedPolicy && !this.userPreferences.alwaysAsk) {
            return this.userPreferences.rememberedPolicy;
        }
        return null;
    }
    
    // 检查自动策略
    checkAutoPolicy(options) {
        if (this.policyConfig.enableAutoPolicy && this.policyConfig.defaultPolicy) {
            return this.policyConfig.defaultPolicy;
        }
        
        if (options.autoPolicy) {
            return options.autoPolicy;
        }
        
        return null;
    }
    
    // 显示策略对话框
    showPolicyDialog(dialogService, options) {
        return new Promise((resolve) => {
            const startTime = Date.now();
            let timeoutId = null;
            
            // 设置超时
            if (this.policyConfig.timeoutDuration > 0) {
                timeoutId = setTimeout(() => {
                    this.recordTimeout();
                    resolve(this.getDefaultPolicy());
                }, this.policyConfig.timeoutDuration);
            }
            
            // 显示对话框
            dialogService.add(AskRecurrenceUpdatePolicyDialog, {
                policies: Array.from(this.updatePolicies.values()),
                defaultPolicy: this.getDefaultPolicy(),
                showAdvancedOptions: this.policyConfig.enableAdvancedOptions,
                enableRememberChoice: this.policyConfig.enableRememberChoice,
                confirm: (policy, remember) => {
                    if (timeoutId) {
                        clearTimeout(timeoutId);
                    }
                    
                    const decisionTime = Date.now() - startTime;
                    this.recordDecisionTime(decisionTime);
                    
                    if (remember) {
                        this.rememberPolicy(policy);
                    }
                    
                    resolve(policy);
                },
            }, {
                onClose: () => {
                    if (timeoutId) {
                        clearTimeout(timeoutId);
                    }
                    
                    this.recordCancellation();
                    resolve(false);
                },
            });
        });
    }
    
    // 获取默认策略
    getDefaultPolicy() {
        // 查找默认策略
        for (const [key, policy] of this.updatePolicies.entries()) {
            if (policy.isDefault) {
                return key;
            }
        }
        
        // 返回首选策略
        return this.userPreferences.preferredPolicy || 'future_events';
    }
    
    // 处理策略选择
    processPolicySelection(policy, options) {
        if (policy) {
            this.recordPolicySelection(policy);
            
            // 更新统计
            this.updatePolicyStatistics(policy);
            
            // 触发事件
            this.triggerPolicySelectedEvent(policy, options);
        }
    }
    
    // 记住策略
    rememberPolicy(policy) {
        this.userPreferences.rememberedPolicy = policy;
        this.userPreferences.alwaysAsk = false;
        this.recordRememberChoiceUsage();
        
        // 保存到本地存储
        this.saveUserPreferences();
    }
    
    // 保存用户偏好
    saveUserPreferences() {
        try {
            const preferences = JSON.stringify(this.userPreferences);
            localStorage.setItem('calendar_recurrence_preferences', preferences);
        } catch (error) {
            console.error('Failed to save user preferences:', error);
        }
    }
    
    // 加载用户偏好
    loadUserPreferences() {
        try {
            const preferences = localStorage.getItem('calendar_recurrence_preferences');
            if (preferences) {
                Object.assign(this.userPreferences, JSON.parse(preferences));
            }
        } catch (error) {
            console.error('Failed to load user preferences:', error);
        }
    }
    
    // 重置用户偏好
    resetUserPreferences() {
        this.userPreferences = {
            rememberedPolicy: null,
            alwaysAsk: true,
            preferredPolicy: 'future_events',
            showAdvancedOptions: false,
            enableKeyboardShortcuts: true
        };
        
        this.saveUserPreferences();
    }
    
    // 记录策略请求
    recordPolicyRequest() {
        this.policyStatistics.totalRequests++;
    }
    
    // 记录策略选择
    recordPolicySelection(policy) {
        const count = this.policyStatistics.policySelections.get(policy) || 0;
        this.policyStatistics.policySelections.set(policy, count + 1);
    }
    
    // 记录超时
    recordTimeout() {
        this.policyStatistics.timeouts++;
    }
    
    // 记录取消
    recordCancellation() {
        this.policyStatistics.cancellations++;
    }
    
    // 记录决策时间
    recordDecisionTime(time) {
        this.policyStatistics.totalDecisionTime += time;
        this.updateAverageDecisionTime();
    }
    
    // 记录自动应用策略
    recordAutoAppliedPolicy(policy) {
        this.policyStatistics.autoAppliedPolicies++;
        this.recordPolicySelection(policy);
    }
    
    // 记录记住选择使用
    recordRememberChoiceUsage() {
        this.policyStatistics.rememberChoiceUsage++;
    }
    
    // 更新平均决策时间
    updateAverageDecisionTime() {
        const totalDecisions = this.policyStatistics.totalRequests - this.policyStatistics.autoAppliedPolicies;
        if (totalDecisions > 0) {
            this.policyStatistics.averageDecisionTime = 
                this.policyStatistics.totalDecisionTime / totalDecisions;
        }
    }
    
    // 更新策略统计
    updatePolicyStatistics(policy) {
        // 更新策略使用统计
        const policyInfo = this.updatePolicies.get(policy);
        if (policyInfo) {
            policyInfo.usageCount = (policyInfo.usageCount || 0) + 1;
            policyInfo.lastUsed = new Date();
        }
    }
    
    // 触发策略选择事件
    triggerPolicySelectedEvent(policy, options) {
        const event = new CustomEvent('recurrence-policy-selected', {
            detail: {
                policy: policy,
                options: options,
                timestamp: new Date()
            }
        });
        
        document.dispatchEvent(event);
    }
    
    // 处理策略错误
    handlePolicyError(error) {
        console.error('Recurrence policy error:', error);
        
        // 返回默认策略
        return this.getDefaultPolicy();
    }
    
    // 获取策略信息
    getPolicyInfo(policyKey) {
        return this.updatePolicies.get(policyKey);
    }
    
    // 获取所有策略
    getAllPolicies() {
        return Array.from(this.updatePolicies.entries()).map(([key, policy]) => ({
            key,
            ...policy
        }));
    }
    
    // 获取策略统计
    getPolicyStatistics() {
        return {
            ...this.policyStatistics,
            requestSuccessRate: this.policyStatistics.totalRequests > 0 ? 
                ((this.policyStatistics.totalRequests - this.policyStatistics.cancellations) / this.policyStatistics.totalRequests) * 100 : 0,
            timeoutRate: this.policyStatistics.totalRequests > 0 ? 
                (this.policyStatistics.timeouts / this.policyStatistics.totalRequests) * 100 : 0,
            autoApplyRate: this.policyStatistics.totalRequests > 0 ? 
                (this.policyStatistics.autoAppliedPolicies / this.policyStatistics.totalRequests) * 100 : 0,
            averageDecisionTime: this.policyStatistics.averageDecisionTime,
            mostUsedPolicy: this.getMostUsedPolicy(),
            policyDistribution: this.getPolicyDistribution()
        };
    }
    
    // 获取最常用策略
    getMostUsedPolicy() {
        let mostUsed = null;
        let maxCount = 0;
        
        for (const [policy, count] of this.policyStatistics.policySelections.entries()) {
            if (count > maxCount) {
                maxCount = count;
                mostUsed = policy;
            }
        }
        
        return mostUsed;
    }
    
    // 获取策略分布
    getPolicyDistribution() {
        const distribution = {};
        const total = Array.from(this.policyStatistics.policySelections.values())
            .reduce((sum, count) => sum + count, 0);
        
        for (const [policy, count] of this.policyStatistics.policySelections.entries()) {
            distribution[policy] = total > 0 ? (count / total) * 100 : 0;
        }
        
        return distribution;
    }
    
    // 创建钩子
    createHook() {
        return this.enhancedUseAskRecurrenceUpdatePolicy();
    }
    
    // 销毁管理器
    destroy() {
        // 清理策略
        this.updatePolicies.clear();
        
        // 清理统计
        this.policyStatistics.policySelections.clear();
        
        // 重置统计
        this.policyStatistics = {
            totalRequests: 0,
            policySelections: new Map(),
            timeouts: 0,
            cancellations: 0,
            averageDecisionTime: 0,
            totalDecisionTime: 0,
            rememberChoiceUsage: 0,
            autoAppliedPolicies: 0
        };
    }
}

// 使用示例
const policyManager = new RecurrenceUpdatePolicyManager();

// 创建钩子
const useAskRecurrenceUpdatePolicy = policyManager.createHook();

// 在组件中使用
const askPolicy = useAskRecurrenceUpdatePolicy();

// 询问策略
const policy = await askPolicy({
    forceAsk: false,
    autoPolicy: null
});

console.log('Selected policy:', policy);

// 获取统计信息
const stats = policyManager.getPolicyStatistics();
console.log('Policy statistics:', stats);
```

## 技术特点

### 1. Promise封装
- **异步处理**: 使用Promise处理异步操作
- **回调管理**: 管理确认和关闭回调
- **错误处理**: 处理异步操作错误
- **结果返回**: 返回用户选择结果

### 2. 钩子模式
- **服务注入**: 注入对话框服务
- **函数绑定**: 绑定服务到函数
- **React模式**: 遵循React钩子模式
- **易用接口**: 提供简单易用的接口

### 3. 对话框集成
- **对话框服务**: 集成对话框服务
- **策略对话框**: 使用重复更新策略对话框
- **用户交互**: 处理用户交互
- **选择处理**: 处理用户选择

### 4. 工具函数
- **功能封装**: 封装重复策略询问功能
- **代码复用**: 提供可复用的工具函数
- **模块化**: 模块化的设计
- **依赖管理**: 管理模块依赖

## 设计模式

### 1. 工厂模式 (Factory Pattern)
- **钩子工厂**: 创建钩子函数
- **策略工厂**: 创建策略对象
- **对话框工厂**: 创建对话框实例

### 2. 策略模式 (Strategy Pattern)
- **更新策略**: 不同的重复更新策略
- **处理策略**: 不同的处理策略
- **选择策略**: 不同的选择策略

### 3. 观察者模式 (Observer Pattern)
- **选择观察**: 观察用户选择
- **状态观察**: 观察对话框状态
- **事件观察**: 观察用户事件

### 4. 命令模式 (Command Pattern)
- **策略命令**: 封装策略选择命令
- **对话框命令**: 封装对话框操作命令
- **回调命令**: 封装回调处理命令

## 注意事项

1. **异步处理**: 正确处理异步操作
2. **错误处理**: 处理对话框和服务错误
3. **内存管理**: 管理Promise和回调的内存
4. **用户体验**: 提供良好的用户体验

## 扩展建议

1. **策略缓存**: 缓存用户选择的策略
2. **快捷键**: 添加键盘快捷键支持
3. **预设策略**: 支持预设策略配置
4. **批量操作**: 支持批量策略应用
5. **智能推荐**: 基于历史选择推荐策略

该重复更新策略钩子为Odoo Calendar模块提供了完整的重复事件更新策略询问功能，通过Promise封装和钩子模式确保了重复事件管理的最佳用户体验。

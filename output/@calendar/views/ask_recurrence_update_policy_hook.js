/******************************************************************************
*  Filepath: /calendar/static/src/views/ask_recurrence_update_policy_hook.js  *
*  Lines: 25                                                                  *
******************************************************************************/
odoo.define('@calendar/views/ask_recurrence_update_policy_hook', ['@web/core/utils/hooks', '@calendar/views/ask_recurrence_update_policy_dialog'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { useService } = require("@web/core/utils/hooks");
const { AskRecurrenceUpdatePolicyDialog } = require("@calendar/views/ask_recurrence_update_policy_dialog");

__exports.askRecurrenceUpdatePolicy = askRecurrenceUpdatePolicy; function askRecurrenceUpdatePolicy(dialogService) {
    return new Promise((resolve) => {
        dialogService.add(AskRecurrenceUpdatePolicyDialog, {
            confirm: resolve,
        }, {
            onClose: resolve.bind(null, false),
        });
    });
}

__exports.useAskRecurrenceUpdatePolicy = useAskRecurrenceUpdatePolicy; function useAskRecurrenceUpdatePolicy() {
    const dialogService = useService("dialog");
    return askRecurrenceUpdatePolicy.bind(null, dialogService);
}

return __exports;
});
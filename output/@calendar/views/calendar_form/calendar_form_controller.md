# CalendarFormController - 日历表单控制器

## 概述

`calendar_form_controller.js` 是 Odoo Calendar 模块的日历表单控制器，负责管理日历事件表单的交互逻辑。该模块包含49行代码，是一个功能专门的控制器组件，专门用于处理日历事件表单的用户交互，具备视频通话集成、动作按钮处理、服务集成等特性，是日历表单视图的核心控制组件。

## 文件信息
- **路径**: `/calendar/static/src/views/calendar_form/calendar_form_controller.js`
- **行数**: 49
- **模块**: `@calendar/views/calendar_form/calendar_form_controller`

## 依赖关系

```javascript
// 核心依赖
'@web/views/form/form_controller'              // 表单控制器基类
'@web/core/utils/hooks'                        // 工具钩子
'@odoo/owl'                                    // OWL框架
```

## 核心功能

### 1. 控制器定义

```javascript
class CalendarFormController extends FormController {
    setup() {
        super.setup();
        const ormService = useService("orm");

        onWillStart(async () => {
            this.discussVideocallLocation = await ormService.call(
                "calendar.event",
                "get_discuss_videocall_location"
            );
        });
    }
}
```

**控制器特性**:
- **继承扩展**: 继承标准表单控制器
- **服务注入**: 注入ORM服务
- **异步初始化**: 异步获取视频通话位置
- **生命周期**: 使用onWillStart生命周期钩子

### 2. 视频通话集成

```javascript
onWillStart(async () => {
    this.discussVideocallLocation = await ormService.call(
        "calendar.event",
        "get_discuss_videocall_location"
    );
});
```

**集成功能**:
- **位置获取**: 获取Discuss视频通话位置
- **异步加载**: 异步加载视频通话配置
- **服务调用**: 调用日历事件服务
- **状态存储**: 存储视频通话位置信息

### 3. 动作按钮处理

```javascript
async beforeExecuteActionButton(clickParams) {
    const action = clickParams.name;
    if (action === "clear_videocall_location" || action === "set_discuss_videocall_location") {
        let newVal = "";
        let videoCallSource = "custom";
        let changes = {};
        if (action === "set_discuss_videocall_location") {
            newVal = this.discussVideocallLocation;
            videoCallSource = "discuss";
            changes.access_token = this.discussVideocallLocation.split("/").pop();
        }
        changes = Object.assign(changes, {
            videocall_location: newVal,
            videocall_source: videoCallSource,
        });
        this.model.root.update(changes);
        return false; // no continue
    }
    return super.beforeExecuteActionButton(...arguments);
}
```

**处理功能**:
- **动作识别**: 识别视频通话相关动作
- **清除位置**: 清除视频通话位置
- **设置位置**: 设置Discuss视频通话位置
- **令牌提取**: 提取访问令牌
- **模型更新**: 更新表单模型数据
- **执行控制**: 控制动作执行流程

## 使用场景

### 1. 日历表单控制器管理器

```javascript
// 日历表单控制器管理器
class CalendarFormControllerManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置控制器配置
        this.controllerConfig = {
            enableVideocallIntegration: true,
            enableDiscussIntegration: true,
            enableCustomVideocall: true,
            enableTokenManagement: true,
            enableActionInterception: true,
            autoLoadVideocallLocation: true,
            validateVideocallUrls: true
        };
        
        // 设置视频通话源
        this.videocallSources = new Map([
            ['discuss', {
                name: 'Discuss',
                type: 'internal',
                requiresToken: true,
                urlPattern: /^\/discuss\/channel\/\d+$/,
                defaultLocation: null
            }],
            ['custom', {
                name: 'Custom',
                type: 'external',
                requiresToken: false,
                urlPattern: /^https?:\/\/.+/,
                defaultLocation: ''
            }],
            ['zoom', {
                name: 'Zoom',
                type: 'external',
                requiresToken: false,
                urlPattern: /^https:\/\/.*\.zoom\.us\/.+/,
                defaultLocation: ''
            }],
            ['teams', {
                name: 'Microsoft Teams',
                type: 'external',
                requiresToken: false,
                urlPattern: /^https:\/\/teams\.microsoft\.com\/.+/,
                defaultLocation: ''
            }]
        ]);
        
        // 设置动作类型
        this.actionTypes = new Map([
            ['clear_videocall_location', {
                name: 'Clear Video Call',
                description: 'Clear video call location',
                targetField: 'videocall_location',
                newValue: '',
                source: 'custom',
                requiresConfirmation: true
            }],
            ['set_discuss_videocall_location', {
                name: 'Set Discuss Video Call',
                description: 'Set Discuss video call location',
                targetField: 'videocall_location',
                newValue: 'dynamic',
                source: 'discuss',
                requiresConfirmation: false
            }],
            ['validate_videocall_url', {
                name: 'Validate Video Call URL',
                description: 'Validate custom video call URL',
                targetField: 'videocall_location',
                newValue: 'validated',
                source: 'custom',
                requiresConfirmation: false
            }]
        ]);
        
        // 设置控制器统计
        this.controllerStatistics = {
            totalSetups: 0,
            videocallLocationLoads: 0,
            actionInterceptions: 0,
            videocallClears: 0,
            videocallSets: 0,
            tokenExtractions: 0,
            modelUpdates: 0,
            errorCount: 0
        };
        
        this.initializeControllerSystem();
    }
    
    // 初始化控制器系统
    initializeControllerSystem() {
        // 创建增强的控制器
        this.createEnhancedController();
        
        // 设置视频通话系统
        this.setupVideocallSystem();
        
        // 设置动作系统
        this.setupActionSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
    }
    
    // 创建增强的控制器
    createEnhancedController() {
        const originalController = CalendarFormController;
        
        this.EnhancedCalendarFormController = class extends originalController {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
                
                // 添加错误处理
                this.addErrorHandling();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    videocallSources: new Map(),
                    lastAction: null,
                    actionCount: 0,
                    validationCache: new Map(),
                    isLoading: false
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
                
                // 初始化控制器
                this.initializeController();
            }
            
            addEnhancedMethods() {
                // 增强的设置方法
                this.enhancedSetup = async () => {
                    try {
                        // 记录设置开始
                        this.recordSetup();
                        
                        // 执行原始设置
                        await super.setup();
                        
                        // 加载视频通话配置
                        if (this.controllerConfig.autoLoadVideocallLocation) {
                            await this.loadVideocallConfiguration();
                        }
                        
                        // 初始化视频通话源
                        this.initializeVideocallSources();
                        
                    } catch (error) {
                        this.handleSetupError(error);
                    }
                };
                
                // 加载视频通话配置
                this.loadVideocallConfiguration = async () => {
                    try {
                        // 记录加载操作
                        this.recordVideocallLocationLoad();
                        
                        // 获取Discuss视频通话位置
                        const ormService = useService("orm");
                        this.discussVideocallLocation = await ormService.call(
                            "calendar.event",
                            "get_discuss_videocall_location"
                        );
                        
                        // 验证位置
                        if (this.controllerConfig.validateVideocallUrls) {
                            this.validateVideocallLocation(this.discussVideocallLocation);
                        }
                        
                    } catch (error) {
                        this.handleVideocallLoadError(error);
                    }
                };
                
                // 增强的动作按钮处理
                this.enhancedBeforeExecuteActionButton = async (clickParams) => {
                    try {
                        // 记录动作拦截
                        this.recordActionInterception(clickParams.name);
                        
                        // 检查是否为视频通话动作
                        if (this.isVideocallAction(clickParams.name)) {
                            return await this.handleVideocallAction(clickParams);
                        }
                        
                        // 执行其他动作
                        return await super.beforeExecuteActionButton(clickParams);
                        
                    } catch (error) {
                        this.handleActionError(error, clickParams);
                        return false;
                    }
                };
                
                // 检查是否为视频通话动作
                this.isVideocallAction = (actionName) => {
                    return this.actionTypes.has(actionName);
                };
                
                // 处理视频通话动作
                this.handleVideocallAction = async (clickParams) => {
                    const actionName = clickParams.name;
                    const actionConfig = this.actionTypes.get(actionName);
                    
                    if (!actionConfig) {
                        return false;
                    }
                    
                    // 确认操作
                    if (actionConfig.requiresConfirmation) {
                        const confirmed = await this.confirmAction(actionConfig);
                        if (!confirmed) {
                            return false;
                        }
                    }
                    
                    // 执行动作
                    return await this.executeVideocallAction(actionName, actionConfig);
                };
                
                // 执行视频通话动作
                this.executeVideocallAction = async (actionName, actionConfig) => {
                    let changes = {};
                    let newVal = "";
                    let videoCallSource = actionConfig.source;
                    
                    if (actionName === "set_discuss_videocall_location") {
                        // 设置Discuss视频通话
                        newVal = this.discussVideocallLocation;
                        videoCallSource = "discuss";
                        
                        // 提取访问令牌
                        if (this.controllerConfig.enableTokenManagement) {
                            const token = this.extractAccessToken(newVal);
                            if (token) {
                                changes.access_token = token;
                                this.recordTokenExtraction();
                            }
                        }
                        
                        this.recordVideocallSet();
                        
                    } else if (actionName === "clear_videocall_location") {
                        // 清除视频通话位置
                        newVal = "";
                        videoCallSource = "custom";
                        
                        this.recordVideocallClear();
                    }
                    
                    // 准备变更
                    changes = Object.assign(changes, {
                        videocall_location: newVal,
                        videocall_source: videoCallSource,
                    });
                    
                    // 更新模型
                    await this.updateModel(changes);
                    
                    return false; // 不继续执行
                };
                
                // 提取访问令牌
                this.extractAccessToken = (url) => {
                    if (!url) return null;
                    
                    try {
                        const parts = url.split("/");
                        return parts.pop() || null;
                    } catch (error) {
                        console.error('Token extraction error:', error);
                        return null;
                    }
                };
                
                // 更新模型
                this.updateModel = async (changes) => {
                    try {
                        this.model.root.update(changes);
                        this.recordModelUpdate();
                    } catch (error) {
                        this.handleModelUpdateError(error, changes);
                        throw error;
                    }
                };
                
                // 验证视频通话位置
                this.validateVideocallLocation = (location) => {
                    if (!location) return true;
                    
                    // 检查缓存
                    if (this.enhancedState.validationCache.has(location)) {
                        return this.enhancedState.validationCache.get(location);
                    }
                    
                    // 验证URL格式
                    let isValid = false;
                    for (const [sourceName, sourceConfig] of this.videocallSources.entries()) {
                        if (sourceConfig.urlPattern.test(location)) {
                            isValid = true;
                            break;
                        }
                    }
                    
                    // 缓存结果
                    this.enhancedState.validationCache.set(location, isValid);
                    
                    return isValid;
                };
                
                // 确认动作
                this.confirmAction = async (actionConfig) => {
                    return new Promise((resolve) => {
                        const dialog = useService("dialog");
                        dialog.add(ConfirmationDialog, {
                            title: actionConfig.name,
                            body: `Are you sure you want to ${actionConfig.description.toLowerCase()}?`,
                            confirm: () => resolve(true),
                            cancel: () => resolve(false)
                        });
                    });
                };
                
                // 初始化视频通话源
                this.initializeVideocallSources = () => {
                    this.enhancedState.videocallSources = new Map(this.videocallSources);
                };
                
                // 初始化控制器
                this.initializeController = () => {
                    this.enhancedState.lastAction = null;
                    this.enhancedState.actionCount = 0;
                    this.enhancedState.isLoading = false;
                };
                
                // 记录设置
                this.recordSetup = () => {
                    this.controllerStatistics.totalSetups++;
                };
                
                // 记录视频通话位置加载
                this.recordVideocallLocationLoad = () => {
                    this.controllerStatistics.videocallLocationLoads++;
                };
                
                // 记录动作拦截
                this.recordActionInterception = (actionName) => {
                    this.enhancedState.lastAction = actionName;
                    this.enhancedState.actionCount++;
                    this.controllerStatistics.actionInterceptions++;
                };
                
                // 记录视频通话清除
                this.recordVideocallClear = () => {
                    this.controllerStatistics.videocallClears++;
                };
                
                // 记录视频通话设置
                this.recordVideocallSet = () => {
                    this.controllerStatistics.videocallSets++;
                };
                
                // 记录令牌提取
                this.recordTokenExtraction = () => {
                    this.controllerStatistics.tokenExtractions++;
                };
                
                // 记录模型更新
                this.recordModelUpdate = () => {
                    this.controllerStatistics.modelUpdates++;
                };
                
                // 处理设置错误
                this.handleSetupError = (error) => {
                    this.controllerStatistics.errorCount++;
                    console.error('Controller setup error:', error);
                };
                
                // 处理视频通话加载错误
                this.handleVideocallLoadError = (error) => {
                    this.controllerStatistics.errorCount++;
                    console.error('Videocall location load error:', error);
                };
                
                // 处理动作错误
                this.handleActionError = (error, clickParams) => {
                    this.controllerStatistics.errorCount++;
                    console.error('Action execution error:', error, clickParams);
                };
                
                // 处理模型更新错误
                this.handleModelUpdateError = (error, changes) => {
                    this.controllerStatistics.errorCount++;
                    console.error('Model update error:', error, changes);
                };
                
                // 获取控制器信息
                this.getControllerInfo = () => {
                    return {
                        lastAction: this.enhancedState.lastAction,
                        actionCount: this.enhancedState.actionCount,
                        isLoading: this.enhancedState.isLoading,
                        videocallLocation: this.discussVideocallLocation,
                        validationCacheSize: this.enhancedState.validationCache.size,
                        statistics: this.controllerStatistics,
                        config: this.controllerConfig
                    };
                };
                
                // 清理缓存
                this.clearCache = () => {
                    this.enhancedState.validationCache.clear();
                };
            }
            
            // 重写原始方法
            setup() {
                return this.enhancedSetup();
            }
            
            async beforeExecuteActionButton(clickParams) {
                return await this.enhancedBeforeExecuteActionButton(clickParams);
            }
        };
    }
}
```

## 技术特点

### 1. 视频通话集成
- **Discuss集成**: 集成Discuss视频通话功能
- **位置管理**: 管理视频通话位置
- **令牌处理**: 处理访问令牌
- **源识别**: 识别视频通话源

### 2. 动作拦截
- **动作识别**: 识别视频通话相关动作
- **执行控制**: 控制动作执行流程
- **参数处理**: 处理动作参数
- **结果返回**: 返回执行结果

### 3. 模型更新
- **字段更新**: 更新视频通话相关字段
- **批量更新**: 批量更新多个字段
- **状态同步**: 同步表单状态
- **变更跟踪**: 跟踪字段变更

### 4. 服务集成
- **ORM服务**: 集成ORM服务
- **异步操作**: 支持异步服务调用
- **错误处理**: 处理服务调用错误
- **生命周期**: 集成组件生命周期

## 设计模式

### 1. 命令模式 (Command Pattern)
- **动作命令**: 封装视频通话动作
- **执行控制**: 控制命令执行
- **撤销支持**: 支持操作撤销

### 2. 策略模式 (Strategy Pattern)
- **处理策略**: 不同动作的处理策略
- **验证策略**: 不同的验证策略
- **更新策略**: 不同的更新策略

### 3. 观察者模式 (Observer Pattern)
- **状态观察**: 观察表单状态变化
- **动作观察**: 观察用户动作
- **模型观察**: 观察模型变化

### 4. 工厂模式 (Factory Pattern)
- **动作工厂**: 创建动作处理器
- **配置工厂**: 创建配置对象
- **服务工厂**: 创建服务实例

## 注意事项

1. **URL验证**: 验证视频通话URL的有效性
2. **令牌安全**: 确保访问令牌的安全性
3. **错误处理**: 处理网络和服务错误
4. **用户体验**: 提供清晰的操作反馈

## 扩展建议

1. **多平台支持**: 支持更多视频通话平台
2. **URL验证**: 增强URL验证功能
3. **批量操作**: 支持批量视频通话设置
4. **历史记录**: 记录视频通话历史
5. **权限控制**: 添加权限控制功能

该日历表单控制器为Odoo Calendar模块提供了完整的表单交互控制功能，通过视频通话集成和智能的动作处理确保了日历事件表单的最佳用户体验。

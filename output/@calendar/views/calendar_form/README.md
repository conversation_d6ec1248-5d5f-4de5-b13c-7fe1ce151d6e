# Calendar Form Module

## 概述

Calendar Form 模块提供了完整的日历事件表单系统，包括事件的创建、编辑和快速创建功能。该模块集成了视频通话功能、重复事件处理、参与者管理等高级特性，为用户提供了全面的日历事件管理体验。

## 模块结构

```
calendar_form/
├── calendar_form_controller.js    # 日历表单控制器
├── calendar_form_view.js          # 日历表单视图
└── calendar_quick_create.js       # 日历快速创建
```

## 核心组件

### 1. 日历表单控制器 (Form Controller)

**文件**: `calendar_form_controller.js`

日历表单控制器是表单系统的核心，负责处理用户交互和业务逻辑。

#### 主要功能

- **视频通话集成**: 集成Discuss视频通话功能
- **动作按钮处理**: 处理视频通话相关的动作按钮
- **服务集成**: 集成ORM和其他核心服务
- **生命周期管理**: 管理表单的生命周期

#### 核心方法

```javascript
class CalendarFormController extends FormController {
    setup() {
        super.setup();
        const ormService = useService("orm");

        // 异步获取视频通话位置
        onWillStart(async () => {
            this.discussVideocallLocation = await ormService.call(
                "calendar.event",
                "get_discuss_videocall_location"
            );
        });
    }

    // 处理动作按钮执行前的逻辑
    async beforeExecuteActionButton(clickParams) {
        const action = clickParams.name;
        
        if (action === "clear_videocall_location" || action === "set_discuss_videocall_location") {
            return this.handleVideocallAction(action);
        }
        
        return super.beforeExecuteActionButton(...arguments);
    }
    
    // 处理视频通话动作
    handleVideocallAction(action) {
        let newVal = "";
        let videoCallSource = "custom";
        let changes = {};
        
        if (action === "set_discuss_videocall_location") {
            newVal = this.discussVideocallLocation;
            videoCallSource = "discuss";
            changes.access_token = this.discussVideocallLocation.split("/").pop();
        }
        
        changes = Object.assign(changes, {
            videocall_location: newVal,
            videocall_source: videoCallSource,
        });
        
        this.model.root.update(changes);
        return false; // 不继续执行
    }
}
```

#### 视频通话功能

```javascript
// 视频通话功能架构
const VIDEOCALL_FEATURES = {
    // 支持的视频通话源
    sources: {
        'discuss': {
            name: 'Odoo Discuss',
            type: 'internal',
            requiresToken: true,
            autoGenerate: true
        },
        'custom': {
            name: 'Custom URL',
            type: 'external',
            requiresToken: false,
            autoGenerate: false
        }
    },
    
    // 动作类型
    actions: {
        'set_discuss_videocall_location': {
            description: '设置Discuss视频通话',
            source: 'discuss',
            generateToken: true
        },
        'clear_videocall_location': {
            description: '清除视频通话位置',
            source: 'custom',
            generateToken: false
        }
    }
};
```

### 2. 日历表单视图 (Form View)

**文件**: `calendar_form_view.js`

日历表单视图定义了表单的整体架构和配置。

#### 视图定义

```javascript
const CalendarFormView = {
    ...formView,
    Controller: CalendarFormController,
};

// 注册视图
registry.category("views").add("calendar_form", CalendarFormView);
```

#### 特性

- **基础继承**: 继承标准表单视图的所有功能
- **控制器替换**: 使用专门的日历表单控制器
- **功能扩展**: 扩展视频通话和日历特定功能
- **配置简化**: 简化的视图配置

### 3. 日历快速创建 (Quick Create)

**文件**: `calendar_quick_create.js`

日历快速创建提供了快速创建日历事件的功能。

#### 字段定义

```javascript
const QUICK_CREATE_CALENDAR_EVENT_FIELDS = {
    name: { type: "string" },
    start: { type: "datetime" },
    start_date: { type: "date" },
    stop_date: { type: "date" },
    stop: { type: "datetime" },
    allday: { type: "boolean" },
    partner_ids: { type: "many2many" },
    videocall_location: { type: "string" },
    description: { type: "string" }
};
```

#### 默认值处理

```javascript
function getDefaultValuesFromRecord(data) {
    const context = {};
    
    for (let fieldName in QUICK_CREATE_CALENDAR_EVENT_FIELDS) {
        if (fieldName in data) {
            let value = data[fieldName];
            const { type } = QUICK_CREATE_CALENDAR_EVENT_FIELDS[fieldName];
            
            // 根据字段类型处理值
            if (type === 'many2many') {
                value = value.records.map((record) => record.resId);
            } else if (type === 'date') {
                value = value && serializeDate(value);
            } else if (type === "datetime") {
                value = value && serializeDateTime(value);
            }
            
            context[`default_${fieldName}`] = value || false;
        }
    }
    
    return context;
}
```

#### 快速创建控制器

```javascript
class CalendarQuickCreateFormController extends CalendarFormController {
    static props = {
        ...CalendarFormController.props,
        goToFullEvent: Function,
    };

    // 跳转到完整事件表单
    goToFullEvent() {
        const context = getDefaultValuesFromRecord(this.model.root.data);
        this.props.goToFullEvent(context);
    }
}
```

#### 快速创建对话框

```javascript
class CalendarQuickCreate extends FormViewDialog {
    static props = {
        ...FormViewDialog.props,
        goToFullEvent: Function,
    };

    setup() {
        super.setup();
        
        // 配置视图属性
        Object.assign(this.viewProps, {
            ...this.viewProps,
            buttonTemplate: "calendar.CalendarQuickCreateButtons",
            goToFullEvent: (contextData) => {
                this.props.goToFullEvent(contextData);
            }
        });
    }
}
```

## 表单字段系统

### 1. 核心字段

```javascript
const CALENDAR_FORM_FIELDS = {
    // 基础信息
    name: {
        type: 'char',
        required: true,
        string: 'Event Title'
    },
    description: {
        type: 'text',
        string: 'Description'
    },
    
    // 时间字段
    start: {
        type: 'datetime',
        required: true,
        string: 'Start Time'
    },
    stop: {
        type: 'datetime',
        required: true,
        string: 'End Time'
    },
    allday: {
        type: 'boolean',
        string: 'All Day'
    },
    
    // 参与者
    partner_ids: {
        type: 'many2many',
        relation: 'res.partner',
        string: 'Attendees'
    },
    
    // 视频通话
    videocall_location: {
        type: 'char',
        string: 'Video Call URL'
    },
    videocall_source: {
        type: 'selection',
        selection: [
            ['custom', 'Custom'],
            ['discuss', 'Discuss']
        ],
        string: 'Video Call Source'
    },
    
    // 重复规则
    recurrency: {
        type: 'boolean',
        string: 'Recurrent'
    },
    rrule: {
        type: 'char',
        string: 'Recurrence Rule'
    }
};
```

### 2. 字段验证

```javascript
// 表单字段验证
class CalendarFormValidation {
    static validateEventTimes(start, stop, allday) {
        if (!start || !stop) {
            return { valid: false, message: 'Start and end times are required' };
        }
        
        if (!allday && new Date(start) >= new Date(stop)) {
            return { valid: false, message: 'End time must be after start time' };
        }
        
        return { valid: true };
    }
    
    static validateVideocallUrl(url) {
        if (!url) return { valid: true };
        
        try {
            new URL(url);
            return { valid: true };
        } catch {
            return { valid: false, message: 'Invalid video call URL' };
        }
    }
    
    static validateAttendees(attendees, maxAttendees = 100) {
        if (attendees.length > maxAttendees) {
            return { 
                valid: false, 
                message: `Maximum ${maxAttendees} attendees allowed` 
            };
        }
        
        return { valid: true };
    }
}
```

## 表单模板系统

### 1. 基础表单模板

```xml
<!-- 日历事件表单模板 -->
<form string="Calendar Event">
    <header>
        <button name="set_discuss_videocall_location" 
                string="Add Discuss Video Call" 
                type="object" 
                class="btn-primary"/>
        <button name="clear_videocall_location" 
                string="Remove Video Call" 
                type="object" 
                class="btn-secondary"/>
    </header>
    
    <sheet>
        <group>
            <field name="name" placeholder="Event Title"/>
            <field name="allday"/>
            <field name="start"/>
            <field name="stop" attrs="{'invisible': [('allday', '=', True)]}"/>
            <field name="start_date" attrs="{'invisible': [('allday', '=', False)]}"/>
            <field name="stop_date" attrs="{'invisible': [('allday', '=', False)]}"/>
        </group>
        
        <group>
            <field name="partner_ids" widget="many2manyattendee"/>
            <field name="videocall_location" widget="url"/>
            <field name="videocall_source"/>
        </group>
        
        <group string="Recurrence">
            <field name="recurrency"/>
            <field name="rrule" attrs="{'invisible': [('recurrency', '=', False)]}"/>
        </group>
        
        <field name="description" placeholder="Event description..."/>
    </sheet>
</form>
```

### 2. 快速创建模板

```xml
<!-- 快速创建模板 -->
<form string="Quick Create Event" class="o_calendar_quick_create">
    <group>
        <field name="name" placeholder="What's the event about?"/>
        <field name="start"/>
        <field name="stop"/>
        <field name="allday"/>
    </group>
    
    <group>
        <field name="partner_ids" widget="many2many_tags"/>
        <field name="videocall_location" placeholder="Video call URL (optional)"/>
    </group>
    
    <footer>
        <button string="Create" type="object" class="btn-primary"/>
        <button string="Edit" special="cancel" class="btn-secondary"/>
    </footer>
</form>
```

## 工作流程

### 1. 事件创建流程

```mermaid
graph TD
    A[用户点击创建] --> B{选择创建方式}
    B -->|快速创建| C[打开快速创建对话框]
    B -->|完整创建| D[打开完整表单]
    C --> E[填写基础信息]
    E --> F{需要更多选项?}
    F -->|是| G[跳转到完整表单]
    F -->|否| H[保存事件]
    D --> I[填写详细信息]
    I --> J[配置重复规则]
    J --> K[添加参与者]
    K --> L[设置视频通话]
    L --> H
    G --> I
    H --> M[事件创建完成]
```

### 2. 视频通话设置流程

```mermaid
graph TD
    A[用户点击视频通话按钮] --> B{选择视频通话类型}
    B -->|Discuss| C[生成Discuss会议室]
    B -->|自定义| D[输入自定义URL]
    C --> E[获取会议室URL]
    E --> F[提取访问令牌]
    F --> G[更新表单字段]
    D --> H[验证URL格式]
    H --> I{URL有效?}
    I -->|是| G
    I -->|否| J[显示错误提示]
    G --> K[保存视频通话信息]
```

## 最佳实践

### 1. 表单性能优化

```javascript
// 表单性能优化策略
class FormPerformanceOptimizer {
    // 字段懒加载
    setupLazyLoading() {
        this.lazyFields = ['description', 'recurrence_rules'];
        this.loadFieldsOnDemand();
    }
    
    // 防抖保存
    setupAutoSave() {
        this.autoSave = debounce(this.saveForm.bind(this), 2000);
        this.setupFieldWatchers();
    }
    
    // 批量验证
    batchValidation(fields) {
        const validationPromises = fields.map(field => 
            this.validateField(field)
        );
        return Promise.all(validationPromises);
    }
}
```

### 2. 用户体验优化

```javascript
// 用户体验优化
class FormUXEnhancer {
    // 智能默认值
    setSmartDefaults() {
        const now = new Date();
        const nextHour = new Date(now.getTime() + 60 * 60 * 1000);
        
        return {
            start: now,
            stop: nextHour,
            allday: false
        };
    }
    
    // 字段联动
    setupFieldDependencies() {
        this.watchField('allday', (value) => {
            this.toggleTimeFields(!value);
        });
        
        this.watchField('recurrency', (value) => {
            this.toggleRecurrenceFields(value);
        });
    }
    
    // 实时验证
    setupRealTimeValidation() {
        this.addFieldValidator('start', this.validateStartTime);
        this.addFieldValidator('stop', this.validateEndTime);
        this.addFieldValidator('videocall_location', this.validateUrl);
    }
}
```

### 3. 错误处理

```javascript
// 表单错误处理
class FormErrorHandler {
    handleSaveError(error) {
        if (error.type === 'validation_error') {
            this.showFieldErrors(error.details);
        } else if (error.type === 'network_error') {
            this.showRetryDialog();
        } else {
            this.showGenericError();
        }
    }
    
    showFieldErrors(errors) {
        errors.forEach(error => {
            const field = this.getField(error.field);
            field.setError(error.message);
        });
    }
    
    showRetryDialog() {
        this.dialog.add(ConfirmationDialog, {
            title: 'Network Error',
            body: 'Failed to save. Would you like to retry?',
            confirm: () => this.retrySave(),
            cancel: () => this.saveDraft()
        });
    }
}
```

## 扩展指南

### 1. 添加自定义字段

```javascript
// 添加自定义字段
const CUSTOM_FIELDS = {
    priority: {
        type: 'selection',
        selection: [
            ['low', 'Low'],
            ['normal', 'Normal'],
            ['high', 'High'],
            ['urgent', 'Urgent']
        ],
        string: 'Priority'
    },
    category_id: {
        type: 'many2one',
        relation: 'calendar.category',
        string: 'Category'
    }
};
```

### 2. 自定义验证规则

```javascript
// 自定义验证规则
class CustomValidationRules {
    static validateBusinessHours(start, stop) {
        const startHour = new Date(start).getHours();
        const stopHour = new Date(stop).getHours();
        
        if (startHour < 9 || stopHour > 17) {
            return {
                valid: false,
                message: 'Events should be scheduled during business hours (9 AM - 5 PM)'
            };
        }
        
        return { valid: true };
    }
    
    static validateConflicts(start, stop, existingEvents) {
        const hasConflict = existingEvents.some(event => 
            this.timeRangesOverlap(start, stop, event.start, event.stop)
        );
        
        if (hasConflict) {
            return {
                valid: false,
                message: 'This time slot conflicts with an existing event'
            };
        }
        
        return { valid: true };
    }
}
```

### 3. 集成外部服务

```javascript
// 集成外部日历服务
class ExternalCalendarIntegration {
    async syncToGoogleCalendar(eventData) {
        const googleEvent = this.convertToGoogleFormat(eventData);
        return await this.googleCalendarAPI.createEvent(googleEvent);
    }
    
    async syncToOutlook(eventData) {
        const outlookEvent = this.convertToOutlookFormat(eventData);
        return await this.outlookAPI.createEvent(outlookEvent);
    }
    
    convertToGoogleFormat(eventData) {
        return {
            summary: eventData.name,
            start: { dateTime: eventData.start },
            end: { dateTime: eventData.stop },
            attendees: eventData.partner_ids.map(id => ({ email: this.getPartnerEmail(id) }))
        };
    }
}
```

## 依赖关系

- **@web/views/form**: 基础表单视图
- **@web/core/dialog**: 对话框服务
- **@calendar/views/ask_recurrence_update_policy**: 重复事件策略
- **@calendar/views/fields**: 日历字段组件

## 版本兼容性

- **Odoo 17.0+**: 完全支持
- **现代浏览器**: 支持ES6+特性
- **移动设备**: 响应式表单支持

---

*Calendar Form 模块为日历事件提供了完整的表单管理功能，支持从简单的快速创建到复杂的事件配置。*

# CalendarQuickCreate - 日历快速创建

## 概述

`calendar_quick_create.js` 是 Odoo Calendar 模块的日历快速创建组件，负责提供快速创建日历事件的功能。该模块包含79行代码，是一个功能完整的快速创建组件，专门用于简化日历事件的创建流程，具备字段映射、数据序列化、对话框集成、控制器扩展等特性，是日历快速操作的核心组件。

## 文件信息
- **路径**: `/calendar/static/src/views/calendar_form/calendar_quick_create.js`
- **行数**: 79
- **模块**: `@calendar/views/calendar_form/calendar_quick_create`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                           // 注册表
'@web/views/view_dialogs/form_view_dialog'     // 表单视图对话框
'@calendar/views/calendar_form/calendar_form_view' // 日历表单视图
'@calendar/views/calendar_form/calendar_form_controller' // 日历表单控制器
'@web/core/l10n/dates'                         // 日期本地化
```

## 核心功能

### 1. 字段定义

```javascript
const QUICK_CREATE_CALENDAR_EVENT_FIELDS = {
    name: { type: "string" },
    start: { type: "datetime" },
    start_date: { type: "date" },
    stop_date: { type: "date" },
    stop: { type: "datetime" },
    allday: { type: "boolean" },
    partner_ids: { type: "many2many" },
    videocall_location: { type: "string" },
    description: { type: "string" }
};
```

**字段特性**:
- **基础字段**: 定义快速创建所需的基础字段
- **类型映射**: 映射字段类型
- **数据结构**: 标准化的数据结构
- **扩展性**: 易于扩展新字段

### 2. 默认值处理

```javascript
function getDefaultValuesFromRecord(data) {
    const context = {};
    for (let fieldName in QUICK_CREATE_CALENDAR_EVENT_FIELDS) {
        if (fieldName in data) {
            let value = data[fieldName];
            const { type } = QUICK_CREATE_CALENDAR_EVENT_FIELDS[fieldName]
            if (type === 'many2many') {
                value = value.records.map((record) => record.resId);
            } else if (type === 'date') {
                value = value && serializeDate(value);
            } else if (type === "datetime") {
                value = value && serializeDateTime(value);
            }
            context[`default_${fieldName}`] = value || false;
        }
    }
    return context;
}
```

**处理功能**:
- **数据提取**: 从记录中提取默认值
- **类型转换**: 根据字段类型转换数据
- **序列化**: 序列化日期和时间数据
- **上下文构建**: 构建默认值上下文

### 3. 快速创建控制器

```javascript
class CalendarQuickCreateFormController extends CalendarFormController {
    static props = {
        ...CalendarFormController.props,
        goToFullEvent: Function,
    };

    goToFullEvent() {
        const context = getDefaultValuesFromRecord(this.model.root.data)
        this.props.goToFullEvent(context);
    }
}
```

**控制器特性**:
- **继承扩展**: 继承日历表单控制器
- **属性扩展**: 扩展属性定义
- **功能增强**: 增强跳转到完整事件功能
- **上下文传递**: 传递上下文数据

### 4. 快速创建对话框

```javascript
class CalendarQuickCreate extends FormViewDialog {
    static props = {
        ...FormViewDialog.props,
        goToFullEvent: Function,
    };

    setup() {
        super.setup();
        Object.assign(this.viewProps, {
            ...this.viewProps,
            buttonTemplate: "calendar.CalendarQuickCreateButtons",
            goToFullEvent: (contextData) => {
                this.props.goToFullEvent(contextData);
            }
        });
    }
}
```

**对话框特性**:
- **继承扩展**: 继承表单视图对话框
- **模板定制**: 定制按钮模板
- **属性传递**: 传递跳转功能属性
- **视图配置**: 配置视图属性

## 使用场景

### 1. 快速创建管理器

```javascript
// 快速创建管理器
class QuickCreateManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置快速创建配置
        this.quickCreateConfig = {
            enableQuickCreate: true,
            enableFullEventJump: true,
            enableFieldValidation: true,
            enableAutoComplete: true,
            enableDefaultValues: true,
            maxQuickFields: 8,
            autoSaveOnCreate: true,
            showAdvancedButton: true
        };
        
        // 设置快速创建字段
        this.quickCreateFields = new Map([
            ['name', {
                type: 'string',
                label: 'Event Title',
                required: true,
                placeholder: 'Enter event title...',
                maxLength: 255,
                order: 1
            }],
            ['start', {
                type: 'datetime',
                label: 'Start Time',
                required: true,
                widget: 'datetime',
                order: 2
            }],
            ['stop', {
                type: 'datetime',
                label: 'End Time',
                required: true,
                widget: 'datetime',
                order: 3
            }],
            ['allday', {
                type: 'boolean',
                label: 'All Day',
                default: false,
                widget: 'boolean_toggle',
                order: 4
            }],
            ['partner_ids', {
                type: 'many2many',
                label: 'Attendees',
                relation: 'res.partner',
                widget: 'many2many_tags',
                order: 5
            }],
            ['videocall_location', {
                type: 'string',
                label: 'Video Call',
                placeholder: 'Video call URL...',
                widget: 'url',
                order: 6
            }],
            ['description', {
                type: 'text',
                label: 'Description',
                placeholder: 'Event description...',
                widget: 'text',
                order: 7
            }]
        ]);
        
        // 设置字段类型处理器
        this.fieldTypeHandlers = new Map([
            ['string', {
                serialize: (value) => value || '',
                deserialize: (value) => value,
                validate: (value, config) => this.validateString(value, config)
            }],
            ['text', {
                serialize: (value) => value || '',
                deserialize: (value) => value,
                validate: (value, config) => this.validateText(value, config)
            }],
            ['datetime', {
                serialize: (value) => value ? serializeDateTime(value) : false,
                deserialize: (value) => value ? new Date(value) : null,
                validate: (value, config) => this.validateDateTime(value, config)
            }],
            ['date', {
                serialize: (value) => value ? serializeDate(value) : false,
                deserialize: (value) => value ? new Date(value) : null,
                validate: (value, config) => this.validateDate(value, config)
            }],
            ['boolean', {
                serialize: (value) => Boolean(value),
                deserialize: (value) => Boolean(value),
                validate: (value, config) => true
            }],
            ['many2many', {
                serialize: (value) => value ? value.records.map(r => r.resId) : [],
                deserialize: (value) => Array.isArray(value) ? value : [],
                validate: (value, config) => this.validateMany2Many(value, config)
            }]
        ]);
        
        // 设置快速创建统计
        this.quickCreateStatistics = {
            totalCreations: 0,
            successfulCreations: 0,
            failedCreations: 0,
            fullEventJumps: 0,
            fieldValidations: 0,
            defaultValueApplications: 0,
            averageCreationTime: 0,
            totalCreationTime: 0
        };
        
        this.initializeQuickCreateSystem();
    }
    
    // 初始化快速创建系统
    initializeQuickCreateSystem() {
        // 创建增强的组件
        this.createEnhancedComponents();
        
        // 设置字段系统
        this.setupFieldSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置模板系统
        this.setupTemplateSystem();
    }
    
    // 创建增强的组件
    createEnhancedComponents() {
        // 增强的快速创建控制器
        this.EnhancedQuickCreateController = class extends CalendarQuickCreateFormController {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addQuickCreateFeatures();
                this.addValidationFeatures();
                this.addPerformanceFeatures();
            }
            
            addQuickCreateFeatures() {
                this.quickCreateState = {
                    isCreating: false,
                    creationStartTime: null,
                    fieldErrors: new Map(),
                    hasUnsavedChanges: false
                };
            }
            
            addValidationFeatures() {
                this.validationRules = new Map();
                this.setupValidationRules();
            }
            
            addPerformanceFeatures() {
                this.performanceTracker = {
                    enabled: true,
                    trackCreationTime: true,
                    trackFieldChanges: true
                };
            }
            
            setupValidationRules() {
                // 设置基础验证规则
                this.validationRules.set('required', (value, config) => {
                    return config.required ? Boolean(value) : true;
                });
                
                this.validationRules.set('maxLength', (value, config) => {
                    return config.maxLength ? (value || '').length <= config.maxLength : true;
                });
                
                this.validationRules.set('dateRange', (value, config, allData) => {
                    if (config.type === 'datetime' && config.name === 'stop') {
                        const start = allData.start;
                        return !start || !value || new Date(start) < new Date(value);
                    }
                    return true;
                });
            }
            
            // 增强的跳转到完整事件
            enhancedGoToFullEvent() {
                try {
                    // 记录跳转操作
                    this.recordFullEventJump();
                    
                    // 验证数据
                    const isValid = this.validateCurrentData();
                    if (!isValid) {
                        this.showValidationErrors();
                        return;
                    }
                    
                    // 获取上下文数据
                    const context = this.getEnhancedDefaultValues();
                    
                    // 执行跳转
                    this.props.goToFullEvent(context);
                    
                } catch (error) {
                    this.handleJumpError(error);
                }
            }
            
            // 获取增强的默认值
            getEnhancedDefaultValues() {
                const data = this.model.root.data;
                const context = {};
                
                for (const [fieldName, fieldConfig] of this.quickCreateFields.entries()) {
                    if (fieldName in data) {
                        const handler = this.fieldTypeHandlers.get(fieldConfig.type);
                        if (handler) {
                            const serializedValue = handler.serialize(data[fieldName]);
                            context[`default_${fieldName}`] = serializedValue;
                        }
                    }
                }
                
                this.recordDefaultValueApplication();
                return context;
            }
            
            // 验证当前数据
            validateCurrentData() {
                const data = this.model.root.data;
                const errors = new Map();
                
                for (const [fieldName, fieldConfig] of this.quickCreateFields.entries()) {
                    const value = data[fieldName];
                    const fieldErrors = this.validateField(fieldName, value, fieldConfig, data);
                    
                    if (fieldErrors.length > 0) {
                        errors.set(fieldName, fieldErrors);
                    }
                }
                
                this.quickCreateState.fieldErrors = errors;
                this.recordFieldValidation();
                
                return errors.size === 0;
            }
            
            // 验证字段
            validateField(fieldName, value, fieldConfig, allData) {
                const errors = [];
                
                for (const [ruleName, ruleValidator] of this.validationRules.entries()) {
                    if (!ruleValidator(value, { ...fieldConfig, name: fieldName }, allData)) {
                        errors.push(this.getValidationErrorMessage(ruleName, fieldConfig));
                    }
                }
                
                return errors;
            }
            
            // 获取验证错误消息
            getValidationErrorMessage(ruleName, fieldConfig) {
                const messages = {
                    'required': `${fieldConfig.label} is required`,
                    'maxLength': `${fieldConfig.label} must be less than ${fieldConfig.maxLength} characters`,
                    'dateRange': 'End time must be after start time'
                };
                
                return messages[ruleName] || 'Invalid value';
            }
            
            // 显示验证错误
            showValidationErrors() {
                const notification = useService("notification");
                
                for (const [fieldName, errors] of this.quickCreateState.fieldErrors.entries()) {
                    for (const error of errors) {
                        notification.add(error, { type: 'danger' });
                    }
                }
            }
            
            // 记录完整事件跳转
            recordFullEventJump() {
                this.quickCreateStatistics.fullEventJumps++;
            }
            
            // 记录默认值应用
            recordDefaultValueApplication() {
                this.quickCreateStatistics.defaultValueApplications++;
            }
            
            // 记录字段验证
            recordFieldValidation() {
                this.quickCreateStatistics.fieldValidations++;
            }
            
            // 处理跳转错误
            handleJumpError(error) {
                console.error('Full event jump error:', error);
                const notification = useService("notification");
                notification.add('Failed to open full event form', { type: 'danger' });
            }
            
            // 重写跳转方法
            goToFullEvent() {
                return this.enhancedGoToFullEvent();
            }
        };
        
        // 增强的快速创建对话框
        this.EnhancedQuickCreate = class extends CalendarQuickCreate {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addDialogFeatures();
                this.addKeyboardSupport();
                this.addAutoComplete();
            }
            
            addDialogFeatures() {
                this.dialogState = {
                    isMinimized: false,
                    isDraggable: false,
                    isResizable: false
                };
            }
            
            addKeyboardSupport() {
                this.keyboardShortcuts = new Map([
                    ['Ctrl+Enter', () => this.saveAndClose()],
                    ['Ctrl+Shift+Enter', () => this.saveAndNew()],
                    ['Escape', () => this.close()],
                    ['F11', () => this.goToFullEvent()]
                ]);
            }
            
            addAutoComplete() {
                this.autoComplete = {
                    enabled: this.quickCreateConfig.enableAutoComplete,
                    sources: new Map([
                        ['name', 'recent_events'],
                        ['partner_ids', 'contacts'],
                        ['videocall_location', 'recent_urls']
                    ])
                };
            }
            
            // 保存并关闭
            saveAndClose() {
                // 实现保存并关闭逻辑
                console.log('Save and close');
            }
            
            // 保存并新建
            saveAndNew() {
                // 实现保存并新建逻辑
                console.log('Save and new');
            }
            
            // 跳转到完整事件
            goToFullEvent() {
                const contextData = this.getContextData();
                this.props.goToFullEvent(contextData);
            }
            
            // 获取上下文数据
            getContextData() {
                // 实现获取上下文数据逻辑
                return {};
            }
        };
    }
    
    // 字段验证方法
    validateString(value, config) {
        if (config.required && !value) {
            return false;
        }
        
        if (config.maxLength && value && value.length > config.maxLength) {
            return false;
        }
        
        return true;
    }
    
    validateText(value, config) {
        return this.validateString(value, config);
    }
    
    validateDateTime(value, config) {
        if (config.required && !value) {
            return false;
        }
        
        if (value && isNaN(new Date(value).getTime())) {
            return false;
        }
        
        return true;
    }
    
    validateDate(value, config) {
        return this.validateDateTime(value, config);
    }
    
    validateMany2Many(value, config) {
        if (config.required && (!value || value.length === 0)) {
            return false;
        }
        
        return true;
    }
    
    // 创建快速创建对话框
    createQuickCreateDialog(props) {
        return new this.EnhancedQuickCreate(props);
    }
    
    // 注册视图
    registerViews() {
        // 注册快速创建表单视图
        registry.category("views").add("calendar_quick_create_form_view", {
            ...CalendarFormView,
            Controller: this.EnhancedQuickCreateController,
        });
    }
    
    // 获取快速创建统计
    getQuickCreateStatistics() {
        return {
            ...this.quickCreateStatistics,
            successRate: this.quickCreateStatistics.totalCreations > 0 ? 
                (this.quickCreateStatistics.successfulCreations / this.quickCreateStatistics.totalCreations) * 100 : 0,
            fieldCount: this.quickCreateFields.size,
            handlerCount: this.fieldTypeHandlers.size,
            averageCreationTime: this.quickCreateStatistics.averageCreationTime
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理字段
        this.quickCreateFields.clear();
        
        // 清理处理器
        this.fieldTypeHandlers.clear();
        
        // 重置统计
        this.quickCreateStatistics = {
            totalCreations: 0,
            successfulCreations: 0,
            failedCreations: 0,
            fullEventJumps: 0,
            fieldValidations: 0,
            defaultValueApplications: 0,
            averageCreationTime: 0,
            totalCreationTime: 0
        };
    }
}

// 使用示例
const quickCreateManager = new QuickCreateManager();

// 注册视图
quickCreateManager.registerViews();

// 创建快速创建对话框
const dialog = quickCreateManager.createQuickCreateDialog({
    resModel: 'calendar.event',
    goToFullEvent: (context) => {
        console.log('Going to full event with context:', context);
    }
});

// 获取统计信息
const stats = quickCreateManager.getQuickCreateStatistics();
console.log('Quick create statistics:', stats);
```

## 技术特点

### 1. 字段映射
- **类型定义**: 定义快速创建字段类型
- **数据转换**: 转换不同类型的数据
- **序列化**: 序列化日期和时间数据
- **默认值**: 处理字段默认值

### 2. 控制器扩展
- **继承机制**: 继承日历表单控制器
- **功能增强**: 增强跳转到完整事件功能
- **属性扩展**: 扩展控制器属性
- **上下文传递**: 传递上下文数据

### 3. 对话框集成
- **继承扩展**: 继承表单视图对话框
- **模板定制**: 定制按钮模板
- **属性配置**: 配置对话框属性
- **事件处理**: 处理用户交互事件

### 4. 数据处理
- **类型识别**: 识别字段数据类型
- **格式转换**: 转换数据格式
- **验证处理**: 处理数据验证
- **上下文构建**: 构建默认值上下文

## 设计模式

### 1. 工厂模式 (Factory Pattern)
- **组件工厂**: 创建快速创建组件
- **字段工厂**: 创建字段处理器
- **对话框工厂**: 创建对话框实例

### 2. 策略模式 (Strategy Pattern)
- **字段策略**: 不同字段类型的处理策略
- **验证策略**: 不同的验证策略
- **序列化策略**: 不同的序列化策略

### 3. 装饰器模式 (Decorator Pattern)
- **控制器装饰**: 装饰基础控制器功能
- **对话框装饰**: 装饰对话框功能
- **字段装饰**: 装饰字段处理功能

### 4. 模板方法模式 (Template Method Pattern)
- **创建模板**: 定义快速创建流程模板
- **验证模板**: 定义验证流程模板
- **跳转模板**: 定义跳转流程模板

## 注意事项

1. **数据一致性**: 确保数据转换的一致性
2. **类型安全**: 确保字段类型的安全性
3. **验证完整性**: 确保数据验证的完整性
4. **用户体验**: 提供良好的用户体验

## 扩展建议

1. **字段扩展**: 支持更多字段类型
2. **验证增强**: 增强字段验证功能
3. **模板定制**: 支持自定义模板
4. **键盘支持**: 添加键盘快捷键支持
5. **自动完成**: 添加字段自动完成功能

该日历快速创建组件为Odoo Calendar模块提供了完整的快速创建功能，通过智能的字段映射和数据处理确保了日历事件快速创建的最佳用户体验。

/*****************************************************************************
*  Filepath: /calendar/static/src/views/calendar_form/calendar_form_view.js  *
*  Lines: 18                                                                 *
*****************************************************************************/
odoo.define('@calendar/views/calendar_form/calendar_form_view', ['@web/core/registry', '@web/views/form/form_view', '@calendar/views/calendar_form/calendar_form_controller'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { registry } = require("@web/core/registry");
const { formView } = require("@web/views/form/form_view");
const { CalendarFormController } = require("@calendar/views/calendar_form/calendar_form_controller");

const CalendarFormView = __exports.CalendarFormView = {
    ...formView,
    Controller: CalendarFormController,
};

registry.category("views").add("calendar_form", CalendarFormView);

return __exports;
});
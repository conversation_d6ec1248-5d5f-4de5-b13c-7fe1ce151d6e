# CalendarFormView - 日历表单视图

## 概述

`calendar_form_view.js` 是 Odoo Calendar 模块的日历表单视图定义文件，负责组装日历表单视图的完整架构。该模块包含18行代码，是一个功能专门的视图定义模块，专门用于配置日历表单视图的MVC架构，具备视图注册、控制器集成、表单继承等特性，是日历表单视图的核心配置层。

## 文件信息
- **路径**: `/calendar/static/src/views/calendar_form/calendar_form_view.js`
- **行数**: 18
- **模块**: `@calendar/views/calendar_form/calendar_form_view`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                           // 注册表
'@web/views/form/form_view'                    // 表单视图基类
'@calendar/views/calendar_form/calendar_form_controller' // 日历表单控制器
```

## 核心功能

### 1. 视图定义

```javascript
const CalendarFormView = {
    ...formView,
    Controller: CalendarFormController,
};
```

**视图特性**:
- **基础继承**: 继承标准表单视图配置
- **控制器替换**: 使用专门的日历表单控制器
- **功能扩展**: 扩展表单视图功能
- **配置简化**: 简化的视图配置

### 2. 视图注册

```javascript
registry.category("views").add("calendar_form", CalendarFormView);
```

**注册功能**:
- **视图注册**: 注册到视图注册表
- **类别指定**: 指定为视图类别
- **名称标识**: 使用"calendar_form"标识
- **全局可用**: 使视图全局可用

## 使用场景

### 1. 日历表单视图管理器

```javascript
// 日历表单视图管理器
class CalendarFormViewManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置视图配置
        this.viewConfig = {
            enableFormInheritance: true,
            enableCustomController: true,
            enableVideocallIntegration: true,
            enableFieldValidation: true,
            enableAutoSave: false,
            enableFormWizard: false,
            defaultFormMode: 'edit',
            supportedModes: ['readonly', 'edit']
        };
        
        // 设置表单特性
        this.formFeatures = new Map([
            ['videocall_integration', {
                name: 'Video Call Integration',
                description: 'Integration with video call platforms',
                enabled: true,
                dependencies: ['discuss', 'calendar_form_controller']
            }],
            ['recurrence_handling', {
                name: 'Recurrence Handling',
                description: 'Handle recurring events',
                enabled: true,
                dependencies: ['calendar_model']
            }],
            ['attendee_management', {
                name: 'Attendee Management',
                description: 'Manage event attendees',
                enabled: true,
                dependencies: ['mail', 'contacts']
            }],
            ['reminder_settings', {
                name: 'Reminder Settings',
                description: 'Configure event reminders',
                enabled: true,
                dependencies: ['calendar_notification']
            }]
        ]);
        
        // 设置字段配置
        this.fieldConfigs = new Map([
            ['name', {
                type: 'char',
                required: true,
                placeholder: 'Event Title',
                maxLength: 255
            }],
            ['start', {
                type: 'datetime',
                required: true,
                widget: 'datetime'
            }],
            ['stop', {
                type: 'datetime',
                required: true,
                widget: 'datetime'
            }],
            ['allday', {
                type: 'boolean',
                default: false,
                widget: 'boolean_toggle'
            }],
            ['videocall_location', {
                type: 'char',
                placeholder: 'Video Call URL',
                widget: 'url'
            }],
            ['videocall_source', {
                type: 'selection',
                selection: [['custom', 'Custom'], ['discuss', 'Discuss']],
                default: 'custom'
            }],
            ['partner_ids', {
                type: 'many2many',
                relation: 'res.partner',
                widget: 'many2many_tags'
            }]
        ]);
        
        // 设置视图统计
        this.viewStatistics = {
            totalRegistrations: 0,
            formCreations: 0,
            controllerInitializations: 0,
            fieldValidations: 0,
            saveOperations: 0,
            errorCount: 0,
            averageLoadTime: 0,
            totalLoadTime: 0
        };
        
        this.initializeViewSystem();
    }
    
    // 初始化视图系统
    initializeViewSystem() {
        // 创建增强的视图
        this.createEnhancedView();
        
        // 设置表单系统
        this.setupFormSystem();
        
        // 设置字段系统
        this.setupFieldSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
    }
    
    // 创建增强的视图
    createEnhancedView() {
        this.enhancedCalendarFormView = {
            // 继承基础表单视图
            ...formView,
            
            // 自定义控制器
            Controller: this.createEnhancedController(),
            
            // 视图元数据
            metadata: {
                name: 'calendar_form',
                displayName: 'Calendar Form',
                description: 'Form view for calendar events',
                version: '1.0.0',
                author: 'Odoo Calendar Module',
                dependencies: ['web', 'calendar'],
                features: this.getViewFeatures()
            },
            
            // 视图配置
            config: {
                enableVideocallIntegration: true,
                enableRecurrenceHandling: true,
                enableAttendeeManagement: true,
                enableReminderSettings: true,
                enableFieldValidation: true,
                autoSaveInterval: 30000, // 30 seconds
                maxAttendees: 100
            },
            
            // 视图生命周期
            lifecycle: {
                onCreate: this.onViewCreate.bind(this),
                onMount: this.onViewMount.bind(this),
                onUpdate: this.onViewUpdate.bind(this),
                onSave: this.onViewSave.bind(this),
                onDestroy: this.onViewDestroy.bind(this)
            },
            
            // 视图方法
            methods: {
                initialize: this.initializeForm.bind(this),
                validate: this.validateForm.bind(this),
                save: this.saveForm.bind(this),
                reset: this.resetForm.bind(this),
                destroy: this.destroyForm.bind(this)
            }
        };
    }
    
    // 创建增强的控制器
    createEnhancedController() {
        return class EnhancedCalendarFormController extends CalendarFormController {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addFormManagement();
                this.addFieldValidation();
                this.addAutoSave();
            }
            
            addFormManagement() {
                this.formManager = {
                    currentMode: 'edit',
                    isDirty: false,
                    lastSaved: null,
                    validationErrors: new Map()
                };
            }
            
            addFieldValidation() {
                this.fieldValidator = {
                    enabled: true,
                    validateOnChange: true,
                    validateOnSave: true,
                    customRules: new Map()
                };
            }
            
            addAutoSave() {
                this.autoSave = {
                    enabled: this.viewConfig.enableAutoSave,
                    interval: this.viewConfig.autoSaveInterval || 30000,
                    timer: null
                };
                
                if (this.autoSave.enabled) {
                    this.startAutoSave();
                }
            }
            
            startAutoSave() {
                this.autoSave.timer = setInterval(() => {
                    if (this.formManager.isDirty) {
                        this.performAutoSave();
                    }
                }, this.autoSave.interval);
            }
            
            performAutoSave() {
                // 实现自动保存逻辑
                console.log('Performing auto save...');
            }
        };
    }
    
    // 获取视图特性
    getViewFeatures() {
        return Array.from(this.formFeatures.keys());
    }
    
    // 视图生命周期方法
    onViewCreate() {
        this.viewStatistics.formCreations++;
        console.log('Calendar form view created');
    }
    
    onViewMount() {
        this.viewStatistics.controllerInitializations++;
        console.log('Calendar form view mounted');
    }
    
    onViewUpdate() {
        console.log('Calendar form view updated');
    }
    
    onViewSave() {
        this.viewStatistics.saveOperations++;
        console.log('Calendar form view saved');
    }
    
    onViewDestroy() {
        console.log('Calendar form view destroyed');
        this.cleanup();
    }
    
    // 初始化表单
    initializeForm(config) {
        // 应用配置
        this.applyConfiguration(config);
        
        // 设置字段
        this.setupFields();
        
        // 设置验证
        this.setupValidation();
    }
    
    // 验证表单
    validateForm() {
        const errors = new Map();
        
        // 验证必填字段
        for (const [fieldName, fieldConfig] of this.fieldConfigs.entries()) {
            if (fieldConfig.required) {
                const value = this.getFieldValue(fieldName);
                if (!value) {
                    errors.set(fieldName, 'This field is required');
                }
            }
        }
        
        // 验证日期范围
        const start = this.getFieldValue('start');
        const stop = this.getFieldValue('stop');
        if (start && stop && new Date(start) >= new Date(stop)) {
            errors.set('stop', 'End time must be after start time');
        }
        
        // 验证视频通话URL
        const videocallLocation = this.getFieldValue('videocall_location');
        if (videocallLocation && !this.isValidUrl(videocallLocation)) {
            errors.set('videocall_location', 'Please enter a valid URL');
        }
        
        this.viewStatistics.fieldValidations++;
        
        return errors.size === 0;
    }
    
    // 保存表单
    saveForm() {
        // 验证表单
        if (!this.validateForm()) {
            return false;
        }
        
        // 执行保存
        this.performSave();
        
        return true;
    }
    
    // 执行保存
    performSave() {
        // 实现保存逻辑
        this.viewStatistics.saveOperations++;
        console.log('Form saved successfully');
    }
    
    // 重置表单
    resetForm() {
        // 重置所有字段
        for (const fieldName of this.fieldConfigs.keys()) {
            this.resetField(fieldName);
        }
        
        console.log('Form reset');
    }
    
    // 重置字段
    resetField(fieldName) {
        const fieldConfig = this.fieldConfigs.get(fieldName);
        if (fieldConfig && fieldConfig.default !== undefined) {
            this.setFieldValue(fieldName, fieldConfig.default);
        } else {
            this.setFieldValue(fieldName, null);
        }
    }
    
    // 获取字段值
    getFieldValue(fieldName) {
        // 实现获取字段值逻辑
        return null;
    }
    
    // 设置字段值
    setFieldValue(fieldName, value) {
        // 实现设置字段值逻辑
        console.log(`Setting ${fieldName} to:`, value);
    }
    
    // 验证URL
    isValidUrl(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }
    
    // 设置字段
    setupFields() {
        for (const [fieldName, fieldConfig] of this.fieldConfigs.entries()) {
            this.configureField(fieldName, fieldConfig);
        }
    }
    
    // 配置字段
    configureField(fieldName, fieldConfig) {
        // 实现字段配置逻辑
        console.log(`Configuring field ${fieldName}:`, fieldConfig);
    }
    
    // 设置验证
    setupValidation() {
        // 设置字段验证规则
        this.setupFieldValidationRules();
        
        // 设置表单验证规则
        this.setupFormValidationRules();
    }
    
    // 设置字段验证规则
    setupFieldValidationRules() {
        // 为每个字段设置验证规则
        for (const [fieldName, fieldConfig] of this.fieldConfigs.entries()) {
            if (fieldConfig.required) {
                this.addValidationRule(fieldName, 'required', 'This field is required');
            }
            
            if (fieldConfig.maxLength) {
                this.addValidationRule(fieldName, 'maxLength', `Maximum length is ${fieldConfig.maxLength}`);
            }
        }
    }
    
    // 设置表单验证规则
    setupFormValidationRules() {
        // 添加表单级别的验证规则
        this.addFormValidationRule('dateRange', this.validateDateRange.bind(this));
        this.addFormValidationRule('attendeeLimit', this.validateAttendeeLimit.bind(this));
    }
    
    // 验证日期范围
    validateDateRange() {
        const start = this.getFieldValue('start');
        const stop = this.getFieldValue('stop');
        
        if (start && stop) {
            return new Date(start) < new Date(stop);
        }
        
        return true;
    }
    
    // 验证参与者限制
    validateAttendeeLimit() {
        const attendees = this.getFieldValue('partner_ids') || [];
        return attendees.length <= this.viewConfig.maxAttendees;
    }
    
    // 添加验证规则
    addValidationRule(fieldName, ruleName, message) {
        // 实现添加验证规则逻辑
        console.log(`Adding validation rule ${ruleName} for ${fieldName}: ${message}`);
    }
    
    // 添加表单验证规则
    addFormValidationRule(ruleName, validator) {
        // 实现添加表单验证规则逻辑
        console.log(`Adding form validation rule: ${ruleName}`);
    }
    
    // 应用配置
    applyConfiguration(config) {
        // 合并配置
        this.viewConfig = { ...this.viewConfig, ...config };
        
        // 应用到视图
        Object.assign(this.enhancedCalendarFormView.config, this.viewConfig);
    }
    
    // 注册视图
    registerView() {
        try {
            // 验证视图
            if (!this.validateView()) {
                throw new Error('View validation failed');
            }
            
            // 注册到注册表
            registry.category("views").add("calendar_form", this.enhancedCalendarFormView);
            
            // 记录注册
            this.viewStatistics.totalRegistrations++;
            
            console.log('Calendar form view registered successfully');
            
        } catch (error) {
            this.viewStatistics.errorCount++;
            console.error('View registration failed:', error);
            throw error;
        }
    }
    
    // 验证视图
    validateView() {
        // 验证控制器
        if (!this.enhancedCalendarFormView.Controller) {
            console.error('Missing controller');
            return false;
        }
        
        // 验证配置
        if (!this.enhancedCalendarFormView.config) {
            console.error('Missing configuration');
            return false;
        }
        
        return true;
    }
    
    // 获取视图
    getView() {
        return this.enhancedCalendarFormView;
    }
    
    // 获取视图统计
    getViewStatistics() {
        return {
            ...this.viewStatistics,
            registrationSuccessRate: this.viewStatistics.totalRegistrations > 0 ? 
                ((this.viewStatistics.totalRegistrations - this.viewStatistics.errorCount) / this.viewStatistics.totalRegistrations) * 100 : 0,
            featureCount: this.formFeatures.size,
            fieldCount: this.fieldConfigs.size,
            averageLoadTime: this.viewStatistics.averageLoadTime
        };
    }
    
    // 清理资源
    cleanup() {
        // 清理表单特性
        this.formFeatures.clear();
        
        // 清理字段配置
        this.fieldConfigs.clear();
        
        // 重置统计
        this.viewStatistics = {
            totalRegistrations: 0,
            formCreations: 0,
            controllerInitializations: 0,
            fieldValidations: 0,
            saveOperations: 0,
            errorCount: 0,
            averageLoadTime: 0,
            totalLoadTime: 0
        };
    }
    
    // 销毁表单
    destroyForm() {
        this.cleanup();
        console.log('Calendar form view destroyed');
    }
}

// 使用示例
const formViewManager = new CalendarFormViewManager();

// 注册视图
formViewManager.registerView();

// 获取视图
const calendarFormView = formViewManager.getView();

// 配置视图
formViewManager.applyConfiguration({
    enableAutoSave: true,
    autoSaveInterval: 60000,
    maxAttendees: 50
});

// 获取统计信息
const stats = formViewManager.getViewStatistics();
console.log('Form view statistics:', stats);
```

## 技术特点

### 1. 视图继承
- **基础继承**: 继承标准表单视图
- **控制器替换**: 替换为专门的日历控制器
- **功能扩展**: 扩展表单功能
- **配置简化**: 简化视图配置

### 2. 控制器集成
- **专用控制器**: 集成日历表单控制器
- **视频通话**: 支持视频通话功能
- **动作处理**: 处理表单动作
- **服务集成**: 集成各种服务

### 3. 注册机制
- **视图注册**: 注册到全局视图注册表
- **类别管理**: 管理视图类别
- **名称标识**: 唯一的视图标识
- **全局访问**: 全局可访问

### 4. 配置灵活性
- **简单配置**: 简单的视图配置
- **功能开关**: 灵活的功能开关
- **扩展性**: 良好的扩展性
- **维护性**: 易于维护

## 设计模式

### 1. 组合模式 (Composite Pattern)
- **视图组合**: 组合表单视图组件
- **功能组合**: 组合不同功能
- **配置组合**: 组合配置选项

### 2. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 装饰基础表单功能
- **控制器装饰**: 装饰表单控制器
- **视图装饰**: 装饰视图功能

### 3. 工厂模式 (Factory Pattern)
- **视图工厂**: 创建表单视图实例
- **控制器工厂**: 创建控制器实例
- **配置工厂**: 创建配置对象

### 4. 注册表模式 (Registry Pattern)
- **视图注册**: 注册视图到注册表
- **全局访问**: 全局访问注册的视图
- **动态加载**: 动态加载视图

## 注意事项

1. **控制器一致性**: 确保控制器的一致性
2. **配置验证**: 验证视图配置的正确性
3. **依赖管理**: 管理视图依赖关系
4. **性能考虑**: 考虑视图加载性能

## 扩展建议

1. **字段验证**: 增强字段验证功能
2. **自动保存**: 添加自动保存功能
3. **表单向导**: 支持表单向导模式
4. **批量操作**: 支持批量操作
5. **权限控制**: 添加权限控制功能

该日历表单视图为Odoo Calendar模块提供了完整的表单视图定义，通过专门的控制器集成和简化的配置确保了日历事件表单的最佳用户体验。

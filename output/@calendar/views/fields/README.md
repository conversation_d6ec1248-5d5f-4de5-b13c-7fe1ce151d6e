# Calendar Fields Module

## 概述

Calendar Fields 模块提供了专门为日历应用设计的字段组件，这些字段组件处理参与者管理、状态显示、标签展示等日历特定的功能。该模块包含了多种参与者相关的字段类型，为日历事件的参与者管理提供了丰富的用户界面组件。

## 模块结构

```
fields/
├── attendee_tags_list.js                    # 参与者标签列表
├── many2many_attendee.js                    # 多对多参与者字段
└── many2many_attendee_expandable.js         # 可展开多对多参与者字段
```

## 核心组件

### 1. 参与者标签列表 (Attendee Tags List)

**文件**: `attendee_tags_list.js`

参与者标签列表是一个专门用于显示参与者标签的组件。

#### 组件特性

```javascript
class AttendeeTagsList extends TagsList {
    static template = "calendar.AttendeeTagsList";
}
```

- **继承扩展**: 继承标准标签列表组件
- **模板定制**: 使用专门的参与者标签模板
- **功能专用**: 专门用于参与者标签显示
- **简洁实现**: 简洁高效的组件实现

#### 使用场景

- 在日历事件中显示参与者列表
- 在弹出框中展示参与者信息
- 在表单中管理参与者标签

### 2. 多对多参与者字段 (Many2Many Attendee)

**文件**: `many2many_attendee.js`

多对多参与者字段是处理参与者关系的核心字段组件。

#### 状态图标映射

```javascript
const ICON_BY_STATUS = {
    accepted: "fa-check",
    declined: "fa-times",
    tentative: "fa-question",
};
```

#### 组件定义

```javascript
class Many2ManyAttendee extends Many2ManyTagsAvatarField {
    static template = "calendar.Many2ManyAttendee";
    static components = {
        ...Many2ManyAttendee.components,
        TagsList: AttendeeTagsList,
    };
}
```

#### 特殊数据处理

```javascript
setup() {
    super.setup();
    
    // 使用特殊数据钩子获取参与者详情
    this.specialData = useSpecialData((orm, props) => {
        const { context, name, record } = props;
        return orm.call(
            "res.partner",
            "get_attendee_detail",
            [record.data[name].records.map((rec) => rec.resId), [record.resId || false]],
            { context }
        );
    });
}
```

#### 标签增强

```javascript
get tags() {
    const partnerIds = this.specialData.data;
    const noEmailPartnerIds = this.props.record.data.invalid_email_partner_ids?.records || [];
    
    const tags = super.tags.map((tag) => {
        const partner = partnerIds.find((partner) => tag.resId === partner.id);
        const noEmail = noEmailPartnerIds.find((partner) => tag.resId == partner.resId);
        
        if (partner) {
            tag.status = partner.status;
            tag.statusIcon = ICON_BY_STATUS[partner.status];
        }
        
        if (noEmail) {
            tag.noEmail = true;
        }
        
        return tag;
    });

    // 组织者排序
    const organizer = partnerIds.find((partner) => partner.is_organizer);
    if (organizer) {
        const orgId = organizer.id;
        tags.sort((a, b) => {
            const a_org = a.resId === orgId;
            return a_org ? -1 : 1;
        });
    }
    
    return tags;
}
```

### 3. 可展开多对多参与者字段 (Expandable Many2Many Attendee)

**文件**: `many2many_attendee_expandable.js`

可展开多对多参与者字段提供了在弹出框中展开显示参与者的功能。

#### 组件定义

```javascript
class Many2ManyAttendeeExpandable extends Many2ManyAttendee {
    static template = "calendar.Many2ManyAttendeeExpandable";
    state = useState({ expanded: false });
}
```

#### 统计数据处理

```javascript
setup() {
    super.setup();
    
    // 获取参与者统计数据
    this.attendeesCount = this.props.record.data.attendees_count;
    this.acceptedCount = this.props.record.data.accepted_count;
    this.declinedCount = this.props.record.data.declined_count;
    this.uncertainCount = this.attendeesCount - this.acceptedCount - this.declinedCount;
}
```

#### 位置重定位

```javascript
if (!this.env.isSmall) {
    useEffect(
        () => {
            const popover = document
                .querySelector(".o_field_many2manyattendeeexpandable")
                .closest(".o_popover");
            const target = document.querySelector(
                `.fc-event[data-event-id="${this.props.record.resId}"]`
            );
            reposition(popover, target, { position: "right", margin: 0 });
        },
        () => [this.state.expanded]
    );
}
```

#### 展开控制

```javascript
onExpanderClick() {
    this.state.expanded = !this.state.expanded;
}
```

## 字段类型系统

### 1. 字段类型定义

```javascript
const CALENDAR_FIELD_TYPES = {
    'attendee_tags_list': {
        component: AttendeeTagsList,
        description: '参与者标签列表',
        supportedTypes: ['many2many'],
        features: ['status_display', 'icon_mapping']
    },
    
    'many2manyattendee': {
        component: Many2ManyAttendee,
        description: '多对多参与者字段',
        supportedTypes: ['many2many'],
        features: ['status_display', 'avatar_display', 'organizer_sorting', 'email_validation']
    },
    
    'many2manyattendeeexpandable': {
        component: Many2ManyAttendeeExpandable,
        description: '可展开多对多参与者字段',
        supportedTypes: ['many2many'],
        features: ['expandable_display', 'statistics', 'repositioning']
    }
};
```

### 2. 参与者状态系统

```javascript
const ATTENDEE_STATUS_SYSTEM = {
    statuses: {
        'needsAction': {
            name: 'Needs Action',
            icon: 'fa-question',
            color: '#ffc107',
            priority: 1,
            description: '等待响应'
        },
        'accepted': {
            name: 'Accepted',
            icon: 'fa-check',
            color: '#28a745',
            priority: 3,
            description: '已接受'
        },
        'declined': {
            name: 'Declined',
            icon: 'fa-times',
            color: '#dc3545',
            priority: 2,
            description: '已拒绝'
        },
        'tentative': {
            name: 'Tentative',
            icon: 'fa-question',
            color: '#6c757d',
            priority: 1,
            description: '暂定'
        }
    },
    
    // 状态转换规则
    transitions: {
        'needsAction': ['accepted', 'declined', 'tentative'],
        'accepted': ['declined', 'tentative'],
        'declined': ['accepted', 'tentative'],
        'tentative': ['accepted', 'declined']
    }
};
```

## 样式系统

### 1. 参与者状态样式

```css
/* 参与者状态样式 */
.o_attendee_status_accepted {
    border-left: 4px solid #28a745;
    background-color: rgba(40, 167, 69, 0.1);
}

.o_attendee_status_declined {
    border-left: 4px solid #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
    opacity: 0.7;
    text-decoration: line-through;
}

.o_attendee_status_tentative {
    border-left: 4px solid #6c757d;
    background-color: rgba(108, 117, 125, 0.1);
    border-style: dashed;
}

.o_attendee_status_needsAction {
    border-left: 4px solid #ffc107;
    background-color: rgba(255, 193, 7, 0.1);
    animation: pulse 2s infinite;
}

/* 组织者样式 */
.o_attendee_organizer {
    font-weight: bold;
    border: 2px solid #007bff;
}

/* 无效邮箱样式 */
.o_attendee_invalid_email {
    position: relative;
}

.o_attendee_invalid_email::after {
    content: "⚠️";
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 12px;
}

/* 可展开字段样式 */
.o_field_many2manyattendeeexpandable {
    transition: all 0.3s ease;
}

.o_field_many2manyattendeeexpandable.expanded {
    max-height: 300px;
    overflow-y: auto;
}

/* 脉冲动画 */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}
```

### 2. 响应式设计

```css
/* 响应式参与者字段 */
@media (max-width: 768px) {
    .o_field_many2manyattendee .o_tag {
        font-size: 12px;
        padding: 2px 6px;
    }
    
    .o_field_many2manyattendeeexpandable {
        max-height: 200px;
    }
    
    .o_attendee_avatar {
        width: 24px;
        height: 24px;
    }
}

@media (max-width: 480px) {
    .o_field_many2manyattendee {
        flex-direction: column;
    }
    
    .o_tag {
        margin: 2px 0;
        width: 100%;
    }
}
```

## 使用示例

### 1. 在表单中使用参与者字段

```xml
<!-- 表单中的参与者字段 -->
<form>
    <group>
        <field name="partner_ids" widget="many2manyattendee"/>
        <field name="attendees_count" readonly="1"/>
        <field name="accepted_count" readonly="1"/>
        <field name="declined_count" readonly="1"/>
    </group>
</form>
```

### 2. 在弹出框中使用可展开字段

```xml
<!-- 弹出框中的可展开参与者字段 -->
<div class="o_popover_content">
    <field name="partner_ids" widget="many2manyattendeeexpandable"/>
</div>
```

### 3. 自定义参与者字段

```javascript
// 自定义参与者字段
class CustomAttendeeField extends Many2ManyAttendee {
    setup() {
        super.setup();
        this.customFeatures = {
            showPresenceStatus: true,
            enableQuickActions: true,
            showContactInfo: true
        };
    }
    
    get tags() {
        const baseTags = super.tags;
        
        return baseTags.map(tag => {
            // 添加自定义功能
            if (this.customFeatures.showPresenceStatus) {
                tag.presenceStatus = this.getPresenceStatus(tag.resId);
            }
            
            if (this.customFeatures.showContactInfo) {
                tag.contactInfo = this.getContactInfo(tag.resId);
            }
            
            return tag;
        });
    }
    
    getPresenceStatus(partnerId) {
        // 获取用户在线状态
        return this.env.services.presence.getStatus(partnerId);
    }
    
    getContactInfo(partnerId) {
        // 获取联系信息
        return this.env.services.contacts.getInfo(partnerId);
    }
}
```

## 数据流架构

### 1. 数据获取流程

```mermaid
graph TD
    A[字段初始化] --> B[获取基础数据]
    B --> C[调用specialData钩子]
    C --> D[获取参与者详情]
    D --> E[处理状态信息]
    E --> F[处理邮箱验证]
    F --> G[排序组织者]
    G --> H[生成标签数据]
    H --> I[渲染字段]
```

### 2. 状态更新流程

```mermaid
graph TD
    A[用户交互] --> B[状态变更]
    B --> C[验证权限]
    C --> D{权限检查}
    D -->|通过| E[更新本地状态]
    D -->|失败| F[显示错误]
    E --> G[同步服务器]
    G --> H[更新UI]
    F --> I[回滚状态]
```

## 最佳实践

### 1. 性能优化

```javascript
// 字段性能优化
class FieldPerformanceOptimizer {
    // 虚拟化大量参与者
    setupVirtualization(attendees) {
        if (attendees.length > 50) {
            return this.createVirtualizedList(attendees);
        }
        return this.createNormalList(attendees);
    }
    
    // 缓存参与者数据
    setupCaching() {
        this.attendeeCache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5分钟
    }
    
    // 防抖搜索
    setupDebouncedSearch() {
        this.searchAttendees = debounce(this.performSearch.bind(this), 300);
    }
}
```

### 2. 用户体验优化

```javascript
// 用户体验优化
class FieldUXEnhancer {
    // 智能排序
    smartSort(attendees) {
        return attendees.sort((a, b) => {
            // 组织者优先
            if (a.isOrganizer && !b.isOrganizer) return -1;
            if (!a.isOrganizer && b.isOrganizer) return 1;
            
            // 按状态优先级排序
            const statusPriority = {
                'accepted': 3,
                'tentative': 2,
                'needsAction': 1,
                'declined': 0
            };
            
            const aPriority = statusPriority[a.status] || 0;
            const bPriority = statusPriority[b.status] || 0;
            
            if (aPriority !== bPriority) {
                return bPriority - aPriority;
            }
            
            // 按名称排序
            return a.name.localeCompare(b.name);
        });
    }
    
    // 状态指示器
    addStatusIndicators(tag, status) {
        const indicator = document.createElement('span');
        indicator.className = `o_status_indicator o_status_${status}`;
        indicator.title = this.getStatusDescription(status);
        tag.prepend(indicator);
    }
    
    // 工具提示
    addTooltips(element, attendee) {
        const tooltip = `
            ${attendee.name}
            Status: ${attendee.statusText}
            ${attendee.email ? `Email: ${attendee.email}` : ''}
            ${attendee.phone ? `Phone: ${attendee.phone}` : ''}
        `;
        
        element.title = tooltip;
    }
}
```

### 3. 可访问性

```javascript
// 可访问性增强
class FieldAccessibilityEnhancer {
    addAriaLabels(element, attendee) {
        element.setAttribute('role', 'button');
        element.setAttribute('aria-label', 
            `Attendee: ${attendee.name}, Status: ${attendee.status}`
        );
        element.setAttribute('tabindex', '0');
    }
    
    addKeyboardNavigation(container) {
        container.addEventListener('keydown', (e) => {
            switch (e.key) {
                case 'ArrowRight':
                case 'ArrowDown':
                    this.focusNext(e.target);
                    break;
                case 'ArrowLeft':
                case 'ArrowUp':
                    this.focusPrevious(e.target);
                    break;
                case 'Enter':
                case ' ':
                    this.activateAttendee(e.target);
                    break;
            }
        });
    }
    
    announceStatusChange(attendee, oldStatus, newStatus) {
        const announcement = `${attendee.name} status changed from ${oldStatus} to ${newStatus}`;
        this.announceToScreenReader(announcement);
    }
}
```

## 扩展指南

### 1. 添加新的参与者字段类型

```javascript
// 新的参与者字段类型
class AttendeePresenceField extends Many2ManyAttendee {
    setup() {
        super.setup();
        this.presenceService = useService('presence');
    }
    
    get tags() {
        const baseTags = super.tags;
        
        return baseTags.map(tag => {
            tag.presenceStatus = this.presenceService.getStatus(tag.resId);
            tag.isOnline = tag.presenceStatus === 'online';
            return tag;
        });
    }
}

// 注册新字段类型
registry.category("fields").add("attendee_presence", {
    component: AttendeePresenceField,
    displayName: "Attendee with Presence",
    supportedTypes: ["many2many"],
});
```

### 2. 自定义状态系统

```javascript
// 自定义状态系统
const CUSTOM_ATTENDEE_STATUSES = {
    'confirmed': {
        name: 'Confirmed',
        icon: 'fa-check-double',
        color: '#007bff',
        priority: 4
    },
    'maybe': {
        name: 'Maybe',
        icon: 'fa-question-circle',
        color: '#17a2b8',
        priority: 1
    }
};
```

### 3. 集成外部服务

```javascript
// 集成外部联系人服务
class ExternalContactIntegration {
    async syncWithGoogleContacts(attendees) {
        // 同步Google联系人
        const googleContacts = await this.googleContactsAPI.getContacts();
        return this.mergeContactData(attendees, googleContacts);
    }
    
    async syncWithOutlookContacts(attendees) {
        // 同步Outlook联系人
        const outlookContacts = await this.outlookAPI.getContacts();
        return this.mergeContactData(attendees, outlookContacts);
    }
}
```

## 依赖关系

- **@web/views/fields/many2many_tags_avatar**: 基础多对多标签头像字段
- **@web/core/tags_list**: 标签列表基类
- **@web/views/fields/relational_utils**: 关系字段工具
- **@web/core/position/utils**: 位置工具

## 版本兼容性

- **Odoo 17.0+**: 完全支持
- **现代浏览器**: 支持ES6+特性
- **移动设备**: 响应式字段支持

---

*Calendar Fields 模块为日历应用提供了专业的参与者管理字段组件，支持丰富的状态显示和交互功能。*

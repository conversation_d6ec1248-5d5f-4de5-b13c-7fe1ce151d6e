/*********************************************************************************
*  Filepath: /calendar/static/src/views/fields/many2many_attendee_expandable.js  *
*  Lines: 51                                                                     *
*********************************************************************************/
odoo.define('@calendar/views/fields/many2many_attendee_expandable', ['@odoo/owl', '@web/core/registry', '@web/core/position/utils', '@calendar/views/fields/many2many_attendee'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { useState, useEffect } = require("@odoo/owl");
const { registry } = require("@web/core/registry");
const { reposition } = require("@web/core/position/utils");
const { Many2ManyAttendee, many2ManyAttendee } = require("@calendar/views/fields/many2many_attendee");

const Many2ManyAttendeeExpandable = __exports.Many2ManyAttendeeExpandable = class Many2ManyAttendeeExpandable extends Many2ManyAttendee {
    static template = "calendar.Many2ManyAttendeeExpandable";
    state = useState({ expanded: false });

    setup() {
        super.setup();
        this.attendeesCount = this.props.record.data.attendees_count;
        this.acceptedCount = this.props.record.data.accepted_count;
        this.declinedCount = this.props.record.data.declined_count;
        this.uncertainCount = this.attendeesCount - this.acceptedCount - this.declinedCount;

        if (!this.env.isSmall) {
            useEffect(
                () => {
                    const popover = document
                        .querySelector(".o_field_many2manyattendeeexpandable")
                        .closest(".o_popover");
                    const target = document.querySelector(
                        `.fc-event[data-event-id="${this.props.record.resId}"]`
                    );
                    reposition(popover, target, { position: "right", margin: 0 });
                },
                () => [this.state.expanded]
            );
        }
    }

    onExpanderClick() {
        this.state.expanded = !this.state.expanded;
    }
}

const many2ManyAttendeeExpandable = __exports.many2ManyAttendeeExpandable = {
    ...many2ManyAttendee,
    component: Many2ManyAttendeeExpandable,
};

registry.category("fields").add("many2manyattendeeexpandable", many2ManyAttendeeExpandable);

return __exports;
});
# Many2ManyAttendeeExpandable - 可展开多对多参与者字段

## 概述

`many2many_attendee_expandable.js` 是 Odoo Calendar 模块的可展开多对多参与者字段组件，负责提供可展开的参与者列表显示功能。该模块包含51行代码，是一个功能专门的字段组件，专门用于在弹出框中显示可展开的参与者信息，具备状态管理、位置重定位、统计显示、响应式设计等特性，是日历事件弹出框中参与者显示的核心组件。

## 文件信息
- **路径**: `/calendar/static/src/views/fields/many2many_attendee_expandable.js`
- **行数**: 51
- **模块**: `@calendar/views/fields/many2many_attendee_expandable`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                    // OWL框架
'@web/core/registry'                           // 注册表
'@web/core/position/utils'                     // 位置工具
'@calendar/views/fields/many2many_attendee'   // 多对多参与者字段
```

## 核心功能

### 1. 组件定义

```javascript
class Many2ManyAttendeeExpandable extends Many2ManyAttendee {
    static template = "calendar.Many2ManyAttendeeExpandable";
    state = useState({ expanded: false });
}
```

**组件特性**:
- **继承扩展**: 继承多对多参与者字段
- **模板定制**: 使用专门的可展开模板
- **状态管理**: 管理展开/收起状态
- **响应式状态**: 使用OWL状态管理

### 2. 统计数据处理

```javascript
setup() {
    super.setup();
    this.attendeesCount = this.props.record.data.attendees_count;
    this.acceptedCount = this.props.record.data.accepted_count;
    this.declinedCount = this.props.record.data.declined_count;
    this.uncertainCount = this.attendeesCount - this.acceptedCount - this.declinedCount;
}
```

**数据处理功能**:
- **统计获取**: 获取参与者统计数据
- **状态分类**: 分类已接受、已拒绝参与者
- **计算推导**: 计算未确定状态参与者数量
- **数据存储**: 存储统计信息

### 3. 位置重定位

```javascript
if (!this.env.isSmall) {
    useEffect(
        () => {
            const popover = document
                .querySelector(".o_field_many2manyattendeeexpandable")
                .closest(".o_popover");
            const target = document.querySelector(
                `.fc-event[data-event-id="${this.props.record.resId}"]`
            );
            reposition(popover, target, { position: "right", margin: 0 });
        },
        () => [this.state.expanded]
    );
}
```

**重定位功能**:
- **响应式检查**: 检查是否为小屏幕设备
- **元素查找**: 查找弹出框和目标事件元素
- **位置调整**: 调整弹出框位置到事件右侧
- **状态监听**: 监听展开状态变化重新定位

### 4. 展开控制

```javascript
onExpanderClick() {
    this.state.expanded = !this.state.expanded;
}
```

**控制功能**:
- **状态切换**: 切换展开/收起状态
- **简单实现**: 简单的布尔值切换
- **响应式更新**: 自动触发界面更新
- **用户交互**: 响应用户点击操作

## 使用场景

### 1. 可展开参与者字段管理器

```javascript
// 可展开参与者字段管理器
class ExpandableAttendeeFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置字段配置
        this.fieldConfig = {
            enableExpansion: true,
            enableStatistics: true,
            enableRepositioning: true,
            enableResponsiveDesign: true,
            enableAnimations: true,
            maxCollapsedItems: 3,
            showStatisticsSummary: true,
            autoExpandThreshold: 10
        };
        
        // 设置展开状态
        this.expansionStates = new Map([
            ['collapsed', {
                name: 'Collapsed',
                showCount: 3,
                showSummary: true,
                showExpandButton: true,
                height: 'auto'
            }],
            ['expanded', {
                name: 'Expanded',
                showCount: -1, // 显示所有
                showSummary: true,
                showCollapseButton: true,
                height: 'auto'
            }],
            ['compact', {
                name: 'Compact',
                showCount: 1,
                showSummary: false,
                showExpandButton: true,
                height: '32px'
            }]
        ]);
        
        // 设置统计类型
        this.statisticsTypes = new Map([
            ['total', {
                name: 'Total Attendees',
                field: 'attendees_count',
                color: '#6c757d',
                icon: 'fa-users',
                order: 1
            }],
            ['accepted', {
                name: 'Accepted',
                field: 'accepted_count',
                color: '#28a745',
                icon: 'fa-check',
                order: 2
            }],
            ['declined', {
                name: 'Declined',
                field: 'declined_count',
                color: '#dc3545',
                icon: 'fa-times',
                order: 3
            }],
            ['uncertain', {
                name: 'Uncertain',
                field: 'uncertain_count',
                color: '#ffc107',
                icon: 'fa-question',
                order: 4
            }]
        ]);
        
        // 设置字段统计
        this.fieldStatistics = {
            totalExpansions: 0,
            totalCollapses: 0,
            repositionOperations: 0,
            statisticsCalculations: 0,
            responsiveChecks: 0,
            animationTriggers: 0,
            errorCount: 0,
            averageExpansionTime: 0,
            totalExpansionTime: 0
        };
        
        this.initializeFieldSystem();
    }
    
    // 初始化字段系统
    initializeFieldSystem() {
        // 创建增强的字段
        this.createEnhancedField();
        
        // 设置位置系统
        this.setupPositionSystem();
        
        // 设置统计系统
        this.setupStatisticsSystem();
        
        // 设置动画系统
        this.setupAnimationSystem();
    }
    
    // 创建增强的字段
    createEnhancedField() {
        const originalField = Many2ManyAttendeeExpandable;
        
        this.EnhancedMany2ManyAttendeeExpandable = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加动画功能
                this.addAnimationFeatures();
                
                // 添加统计功能
                this.addStatisticsFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    ...this.state,
                    expansionStartTime: null,
                    repositionCount: 0,
                    animationInProgress: false,
                    lastToggleTime: null,
                    autoExpanded: false
                };
                
                // 增强的统计数据
                this.enhancedStatistics = this.calculateEnhancedStatistics();
                
                // 添加增强方法
                this.addEnhancedMethods();
                
                // 初始化字段
                this.initializeExpandableField();
            }
            
            calculateEnhancedStatistics() {
                const stats = {
                    attendeesCount: this.attendeesCount || 0,
                    acceptedCount: this.acceptedCount || 0,
                    declinedCount: this.declinedCount || 0,
                    uncertainCount: this.uncertainCount || 0
                };
                
                // 计算百分比
                if (stats.attendeesCount > 0) {
                    stats.acceptedPercentage = (stats.acceptedCount / stats.attendeesCount) * 100;
                    stats.declinedPercentage = (stats.declinedCount / stats.attendeesCount) * 100;
                    stats.uncertainPercentage = (stats.uncertainCount / stats.attendeesCount) * 100;
                } else {
                    stats.acceptedPercentage = 0;
                    stats.declinedPercentage = 0;
                    stats.uncertainPercentage = 0;
                }
                
                // 计算响应率
                const respondedCount = stats.acceptedCount + stats.declinedCount;
                stats.responseRate = stats.attendeesCount > 0 ? 
                    (respondedCount / stats.attendeesCount) * 100 : 0;
                
                return stats;
            }
            
            addEnhancedMethods() {
                // 增强的展开点击处理
                this.enhancedOnExpanderClick = () => {
                    try {
                        // 记录展开开始时间
                        this.enhancedState.expansionStartTime = Date.now();
                        this.enhancedState.lastToggleTime = Date.now();
                        
                        // 检查是否可以展开
                        if (!this.canToggleExpansion()) {
                            return;
                        }
                        
                        // 设置动画状态
                        this.enhancedState.animationInProgress = true;
                        
                        // 执行展开切换
                        const wasExpanded = this.enhancedState.expanded;
                        this.enhancedState.expanded = !this.enhancedState.expanded;
                        
                        // 记录操作
                        if (this.enhancedState.expanded) {
                            this.recordExpansion();
                        } else {
                            this.recordCollapse();
                        }
                        
                        // 触发重定位
                        if (this.fieldConfig.enableRepositioning) {
                            this.triggerReposition();
                        }
                        
                        // 触发动画
                        if (this.fieldConfig.enableAnimations) {
                            this.triggerAnimation(wasExpanded);
                        }
                        
                        // 计算展开时间
                        const expansionTime = Date.now() - this.enhancedState.expansionStartTime;
                        this.recordExpansionTime(expansionTime);
                        
                        // 重置动画状态
                        setTimeout(() => {
                            this.enhancedState.animationInProgress = false;
                        }, 300);
                        
                    } catch (error) {
                        this.handleExpansionError(error);
                    }
                };
                
                // 检查是否可以切换展开
                this.canToggleExpansion = () => {
                    // 检查动画状态
                    if (this.enhancedState.animationInProgress) {
                        return false;
                    }
                    
                    // 检查时间间隔
                    const now = Date.now();
                    const lastToggle = this.enhancedState.lastToggleTime;
                    if (lastToggle && (now - lastToggle) < 200) {
                        return false;
                    }
                    
                    return true;
                };
                
                // 触发重定位
                this.triggerReposition = () => {
                    if (!this.env.isSmall) {
                        this.performReposition();
                    }
                };
                
                // 执行重定位
                this.performReposition = () => {
                    try {
                        const popover = this.findPopoverElement();
                        const target = this.findTargetElement();
                        
                        if (popover && target) {
                            reposition(popover, target, { 
                                position: "right", 
                                margin: 0 
                            });
                            
                            this.enhancedState.repositionCount++;
                            this.recordRepositionOperation();
                        }
                        
                    } catch (error) {
                        this.handleRepositionError(error);
                    }
                };
                
                // 查找弹出框元素
                this.findPopoverElement = () => {
                    return document
                        .querySelector(".o_field_many2manyattendeeexpandable")
                        ?.closest(".o_popover");
                };
                
                // 查找目标元素
                this.findTargetElement = () => {
                    return document.querySelector(
                        `.fc-event[data-event-id="${this.props.record.resId}"]`
                    );
                };
                
                // 触发动画
                this.triggerAnimation = (wasExpanded) => {
                    const element = this.findFieldElement();
                    if (element) {
                        if (wasExpanded) {
                            this.animateCollapse(element);
                        } else {
                            this.animateExpansion(element);
                        }
                        
                        this.recordAnimationTrigger();
                    }
                };
                
                // 查找字段元素
                this.findFieldElement = () => {
                    return document.querySelector(".o_field_many2manyattendeeexpandable");
                };
                
                // 动画展开
                this.animateExpansion = (element) => {
                    element.classList.add('expanding');
                    setTimeout(() => {
                        element.classList.remove('expanding');
                        element.classList.add('expanded');
                    }, 300);
                };
                
                // 动画收起
                this.animateCollapse = (element) => {
                    element.classList.add('collapsing');
                    setTimeout(() => {
                        element.classList.remove('collapsing', 'expanded');
                    }, 300);
                };
                
                // 自动展开检查
                this.checkAutoExpansion = () => {
                    if (this.fieldConfig.autoExpandThreshold > 0 && 
                        this.enhancedStatistics.attendeesCount >= this.fieldConfig.autoExpandThreshold) {
                        this.enhancedState.expanded = true;
                        this.enhancedState.autoExpanded = true;
                    }
                };
                
                // 获取显示统计
                this.getDisplayStatistics = () => {
                    const stats = [];
                    
                    for (const [type, config] of this.statisticsTypes.entries()) {
                        const value = this.enhancedStatistics[config.field] || 0;
                        if (value > 0 || type === 'total') {
                            stats.push({
                                type: type,
                                name: config.name,
                                value: value,
                                color: config.color,
                                icon: config.icon,
                                order: config.order
                            });
                        }
                    }
                    
                    return stats.sort((a, b) => a.order - b.order);
                };
                
                // 获取展开状态信息
                this.getExpansionStateInfo = () => {
                    const stateName = this.enhancedState.expanded ? 'expanded' : 'collapsed';
                    return this.expansionStates.get(stateName);
                };
                
                // 初始化可展开字段
                this.initializeExpandableField = () => {
                    // 检查自动展开
                    this.checkAutoExpansion();
                    
                    // 记录统计计算
                    this.recordStatisticsCalculation();
                    
                    // 检查响应式
                    this.checkResponsiveDesign();
                };
                
                // 检查响应式设计
                this.checkResponsiveDesign = () => {
                    this.recordResponsiveCheck();
                    
                    if (this.env.isSmall) {
                        // 小屏幕特殊处理
                        this.handleSmallScreen();
                    }
                };
                
                // 处理小屏幕
                this.handleSmallScreen = () => {
                    // 在小屏幕上禁用重定位
                    console.log('Small screen detected, repositioning disabled');
                };
                
                // 记录展开
                this.recordExpansion = () => {
                    this.fieldStatistics.totalExpansions++;
                };
                
                // 记录收起
                this.recordCollapse = () => {
                    this.fieldStatistics.totalCollapses++;
                };
                
                // 记录重定位操作
                this.recordRepositionOperation = () => {
                    this.fieldStatistics.repositionOperations++;
                };
                
                // 记录统计计算
                this.recordStatisticsCalculation = () => {
                    this.fieldStatistics.statisticsCalculations++;
                };
                
                // 记录响应式检查
                this.recordResponsiveCheck = () => {
                    this.fieldStatistics.responsiveChecks++;
                };
                
                // 记录动画触发
                this.recordAnimationTrigger = () => {
                    this.fieldStatistics.animationTriggers++;
                };
                
                // 记录展开时间
                this.recordExpansionTime = (time) => {
                    this.fieldStatistics.totalExpansionTime += time;
                    this.updateAverageExpansionTime();
                };
                
                // 更新平均展开时间
                this.updateAverageExpansionTime = () => {
                    const totalOperations = this.fieldStatistics.totalExpansions + this.fieldStatistics.totalCollapses;
                    if (totalOperations > 0) {
                        this.fieldStatistics.averageExpansionTime = 
                            this.fieldStatistics.totalExpansionTime / totalOperations;
                    }
                };
                
                // 处理展开错误
                this.handleExpansionError = (error) => {
                    this.fieldStatistics.errorCount++;
                    console.error('Expansion error:', error);
                };
                
                // 处理重定位错误
                this.handleRepositionError = (error) => {
                    this.fieldStatistics.errorCount++;
                    console.error('Reposition error:', error);
                };
                
                // 获取字段信息
                this.getFieldInfo = () => {
                    return {
                        expanded: this.enhancedState.expanded,
                        autoExpanded: this.enhancedState.autoExpanded,
                        repositionCount: this.enhancedState.repositionCount,
                        animationInProgress: this.enhancedState.animationInProgress,
                        statistics: this.enhancedStatistics,
                        displayStatistics: this.getDisplayStatistics(),
                        expansionState: this.getExpansionStateInfo(),
                        fieldStatistics: this.fieldStatistics,
                        config: this.fieldConfig
                    };
                };
            }
            
            // 重写原始方法
            onExpanderClick() {
                this.enhancedOnExpanderClick();
            }
        };
    }
    
    // 创建字段
    createField(props) {
        return new this.EnhancedMany2ManyAttendeeExpandable(props);
    }
    
    // 注册字段
    registerField() {
        const enhancedFieldDefinition = {
            ...many2ManyAttendeeExpandable,
            component: this.EnhancedMany2ManyAttendeeExpandable,
        };
        
        registry.category("fields").add("many2manyattendeeexpandable", enhancedFieldDefinition);
    }
    
    // 获取字段统计
    getFieldStatistics() {
        return {
            ...this.fieldStatistics,
            expansionRate: this.fieldStatistics.totalExpansions > 0 ? 
                (this.fieldStatistics.totalExpansions / (this.fieldStatistics.totalExpansions + this.fieldStatistics.totalCollapses)) * 100 : 0,
            repositionSuccessRate: this.fieldStatistics.repositionOperations > 0 ? 
                ((this.fieldStatistics.repositionOperations - this.fieldStatistics.errorCount) / this.fieldStatistics.repositionOperations) * 100 : 0,
            animationUsageRate: this.fieldStatistics.totalExpansions > 0 ? 
                (this.fieldStatistics.animationTriggers / this.fieldStatistics.totalExpansions) * 100 : 0,
            averageExpansionTime: this.fieldStatistics.averageExpansionTime,
            statisticsVariety: this.statisticsTypes.size,
            stateVariety: this.expansionStates.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理状态
        this.expansionStates.clear();
        
        // 清理统计类型
        this.statisticsTypes.clear();
        
        // 重置统计
        this.fieldStatistics = {
            totalExpansions: 0,
            totalCollapses: 0,
            repositionOperations: 0,
            statisticsCalculations: 0,
            responsiveChecks: 0,
            animationTriggers: 0,
            errorCount: 0,
            averageExpansionTime: 0,
            totalExpansionTime: 0
        };
    }
}

// 使用示例
const expandableFieldManager = new ExpandableAttendeeFieldManager();

// 注册字段
expandableFieldManager.registerField();

// 创建字段
const field = expandableFieldManager.createField({
    name: 'partner_ids',
    record: {
        data: {
            attendees_count: 15,
            accepted_count: 8,
            declined_count: 3
        }
    },
    readonly: false
});

// 获取统计信息
const stats = expandableFieldManager.getFieldStatistics();
console.log('Expandable field statistics:', stats);
```

## 技术特点

### 1. 状态管理
- **响应式状态**: 使用OWL状态管理
- **展开控制**: 控制展开/收起状态
- **状态持久**: 维持状态一致性
- **状态监听**: 监听状态变化

### 2. 位置重定位
- **智能定位**: 智能调整弹出框位置
- **响应式检查**: 检查设备屏幕大小
- **元素查找**: 查找相关DOM元素
- **位置工具**: 使用位置工具库

### 3. 统计显示
- **数据计算**: 计算参与者统计数据
- **分类统计**: 按状态分类统计
- **百分比计算**: 计算各状态百分比
- **响应率**: 计算参与者响应率

### 4. 字段继承
- **基类继承**: 继承多对多参与者字段
- **功能扩展**: 扩展展开功能
- **模板定制**: 定制可展开模板
- **行为增强**: 增强用户交互

## 设计模式

### 1. 状态模式 (State Pattern)
- **展开状态**: 管理展开/收起状态
- **动画状态**: 管理动画进行状态
- **响应状态**: 管理响应式状态

### 2. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 装饰基础字段功能
- **行为装饰**: 装饰展开行为
- **显示装饰**: 装饰显示效果

### 3. 观察者模式 (Observer Pattern)
- **状态观察**: 观察展开状态变化
- **位置观察**: 观察位置变化
- **响应观察**: 观察响应式变化

### 4. 策略模式 (Strategy Pattern)
- **展开策略**: 不同的展开策略
- **定位策略**: 不同的定位策略
- **动画策略**: 不同的动画策略

## 注意事项

1. **性能考虑**: 注意重定位操作的性能影响
2. **响应式设计**: 确保在不同设备上的正确显示
3. **状态一致性**: 保持展开状态的一致性
4. **DOM操作**: 安全地进行DOM查找和操作

## 扩展建议

1. **动画效果**: 添加展开/收起动画效果
2. **键盘支持**: 添加键盘操作支持
3. **自动展开**: 支持基于条件的自动展开
4. **记忆功能**: 记住用户的展开偏好
5. **批量操作**: 支持批量展开/收起操作

该可展开多对多参与者字段为Odoo Calendar模块提供了智能的参与者列表展开功能，通过状态管理和位置重定位确保了在弹出框中参与者信息的最佳显示体验。

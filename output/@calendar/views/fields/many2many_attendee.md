# Many2ManyAttendee - 多对多参与者字段

## 概述

`many2many_attendee.js` 是 Odoo Calendar 模块的多对多参与者字段组件，负责显示和管理日历事件的参与者信息。该模块包含78行代码，是一个功能完整的字段组件，专门用于处理参与者的多对多关系显示，具备状态显示、头像集成、排序管理、特殊数据处理等特性，是日历参与者管理的核心字段组件。

## 文件信息
- **路径**: `/calendar/static/src/views/fields/many2many_attendee.js`
- **行数**: 78
- **模块**: `@calendar/views/fields/many2many_attendee`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                           // 注册表
'@web/views/fields/many2many_tags_avatar/many2many_tags_avatar_field' // 多对多标签头像字段
'@web/views/fields/relational_utils'          // 关系工具
'@calendar/views/fields/attendee_tags_list'   // 参与者标签列表
```

## 核心功能

### 1. 状态图标映射

```javascript
const ICON_BY_STATUS = {
    accepted: "fa-check",
    declined: "fa-times",
    tentative: "fa-question",
};
```

**映射特性**:
- **状态图标**: 映射参与者状态到图标
- **视觉标识**: 提供清晰的视觉状态标识
- **FontAwesome**: 使用FontAwesome图标
- **状态区分**: 区分不同的参与状态

### 2. 字段组件定义

```javascript
class Many2ManyAttendee extends Many2ManyTagsAvatarField {
    static template = "calendar.Many2ManyAttendee";
    static components = {
        ...Many2ManyAttendee.components,
        TagsList: AttendeeTagsList,
    };
}
```

**组件特性**:
- **继承扩展**: 继承多对多标签头像字段
- **模板定制**: 使用专门的参与者模板
- **组件替换**: 替换标签列表组件
- **功能增强**: 增强参与者显示功能

### 3. 特殊数据处理

```javascript
setup() {
    super.setup();
    this.specialData = useSpecialData((orm, props) => {
        const { context, name, record } = props;
        return orm.call(
            "res.partner",
            "get_attendee_detail",
            [record.data[name].records.map((rec) => rec.resId), [record.resId || false]],
            {
                context,
            }
        );
    });
}
```

**数据处理功能**:
- **特殊数据钩子**: 使用特殊数据钩子
- **参与者详情**: 获取参与者详细信息
- **ORM调用**: 调用ORM服务获取数据
- **上下文传递**: 传递上下文信息

### 4. 标签增强

```javascript
get tags() {
    const partnerIds = this.specialData.data;
    const noEmailPartnerIds = this.props.record.data.invalid_email_partner_ids
        ? this.props.record.data.invalid_email_partner_ids.records
        : [];
    const tags = super.tags.map((tag) => {
        const partner = partnerIds.find((partner) => tag.resId === partner.id);
        const noEmail = noEmailPartnerIds.find((partner) => (tag.resId == partner.resId));
        if (partner) {
            tag.status = partner.status;
            tag.statusIcon = ICON_BY_STATUS[partner.status];
        }
        if (noEmail) {
            tag.noEmail = true;
        }
        return tag;
    });

    const organizer = partnerIds.find((partner) => partner.is_organizer);
    if (organizer) {
        const orgId = organizer.id;
        tags.sort((a, b) => {
            const a_org = a.resId === orgId;
            return a_org ? -1 : 1;
        });
    }
    return tags;
}
```

**标签增强功能**:
- **状态集成**: 集成参与者状态信息
- **图标映射**: 映射状态到图标
- **邮箱验证**: 标记无效邮箱的参与者
- **组织者排序**: 将组织者排在前面
- **数据丰富**: 丰富标签数据

## 使用场景

### 1. 参与者字段管理器

```javascript
// 参与者字段管理器
class AttendeeFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置字段配置
        this.fieldConfig = {
            enableStatusIcons: true,
            enableAvatarDisplay: true,
            enableEmailValidation: true,
            enableOrganizerPriority: true,
            enableStatusColors: true,
            enableTooltips: true,
            maxVisibleAttendees: 5,
            showAttendeeCount: true
        };
        
        // 设置参与者状态
        this.attendeeStatuses = new Map([
            ['needsAction', {
                name: 'Needs Action',
                icon: 'fa-question',
                color: '#ffc107',
                textColor: '#212529',
                priority: 1,
                description: 'Waiting for response'
            }],
            ['accepted', {
                name: 'Accepted',
                icon: 'fa-check',
                color: '#28a745',
                textColor: '#ffffff',
                priority: 3,
                description: 'Confirmed attendance'
            }],
            ['declined', {
                name: 'Declined',
                icon: 'fa-times',
                color: '#dc3545',
                textColor: '#ffffff',
                priority: 2,
                description: 'Cannot attend'
            }],
            ['tentative', {
                name: 'Tentative',
                icon: 'fa-question',
                color: '#6c757d',
                textColor: '#ffffff',
                priority: 1,
                description: 'Maybe attending'
            }]
        ]);
        
        // 设置字段类型
        this.fieldTypes = new Map([
            ['organizer', {
                name: 'Organizer',
                priority: 10,
                badge: 'Organizer',
                badgeColor: '#007bff',
                canEdit: true,
                canRemove: false
            }],
            ['required', {
                name: 'Required Attendee',
                priority: 5,
                badge: 'Required',
                badgeColor: '#dc3545',
                canEdit: false,
                canRemove: true
            }],
            ['optional', {
                name: 'Optional Attendee',
                priority: 1,
                badge: 'Optional',
                badgeColor: '#6c757d',
                canEdit: false,
                canRemove: true
            }]
        ]);
        
        // 设置字段统计
        this.fieldStatistics = {
            totalAttendees: 0,
            statusDistribution: new Map(),
            emailValidations: 0,
            invalidEmails: 0,
            organizerChanges: 0,
            tagClicks: 0,
            tooltipShows: 0,
            errorCount: 0
        };
        
        this.initializeFieldSystem();
    }
    
    // 初始化字段系统
    initializeFieldSystem() {
        // 创建增强的字段
        this.createEnhancedField();
        
        // 设置数据系统
        this.setupDataSystem();
        
        // 设置显示系统
        this.setupDisplaySystem();
        
        // 设置交互系统
        this.setupInteractionSystem();
    }
    
    // 创建增强的字段
    createEnhancedField() {
        const originalField = Many2ManyAttendee;
        
        this.EnhancedMany2ManyAttendee = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
                
                // 添加交互功能
                this.addInteractionFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    attendeeCount: 0,
                    organizerId: null,
                    invalidEmails: new Set(),
                    statusCounts: new Map(),
                    lastUpdate: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
                
                // 初始化字段
                this.initializeField();
            }
            
            addEnhancedMethods() {
                // 增强的标签获取
                this.enhancedGetTags = () => {
                    try {
                        // 获取基础标签
                        const baseTags = super.tags;
                        
                        // 获取特殊数据
                        const partnerIds = this.specialData.data || [];
                        const invalidEmailPartners = this.getInvalidEmailPartners();
                        
                        // 增强标签数据
                        const enhancedTags = this.enhanceTagsData(baseTags, partnerIds, invalidEmailPartners);
                        
                        // 排序标签
                        const sortedTags = this.sortTags(enhancedTags, partnerIds);
                        
                        // 更新统计
                        this.updateFieldStatistics(sortedTags);
                        
                        return sortedTags;
                        
                    } catch (error) {
                        this.handleTagsError(error);
                        return super.tags;
                    }
                };
                
                // 获取无效邮箱参与者
                this.getInvalidEmailPartners = () => {
                    const invalidEmailData = this.props.record.data.invalid_email_partner_ids;
                    return invalidEmailData ? invalidEmailData.records : [];
                };
                
                // 增强标签数据
                this.enhanceTagsData = (baseTags, partnerIds, invalidEmailPartners) => {
                    return baseTags.map(tag => {
                        // 查找参与者详情
                        const partner = partnerIds.find(p => tag.resId === p.id);
                        const hasInvalidEmail = invalidEmailPartners.find(p => tag.resId === p.resId);
                        
                        // 增强标签
                        const enhancedTag = { ...tag };
                        
                        if (partner) {
                            enhancedTag.status = partner.status;
                            enhancedTag.statusIcon = this.getStatusIcon(partner.status);
                            enhancedTag.statusInfo = this.attendeeStatuses.get(partner.status);
                            enhancedTag.isOrganizer = partner.is_organizer;
                            enhancedTag.isAlone = partner.is_alone;
                        }
                        
                        if (hasInvalidEmail) {
                            enhancedTag.noEmail = true;
                            enhancedTag.emailValid = false;
                            this.enhancedState.invalidEmails.add(tag.resId);
                        } else {
                            enhancedTag.emailValid = true;
                        }
                        
                        // 添加字段类型
                        enhancedTag.fieldType = this.getFieldType(enhancedTag);
                        
                        // 添加显示属性
                        enhancedTag.displayName = this.getDisplayName(enhancedTag);
                        enhancedTag.tooltip = this.getTooltip(enhancedTag);
                        enhancedTag.cssClasses = this.getCssClasses(enhancedTag);
                        
                        return enhancedTag;
                    });
                };
                
                // 获取状态图标
                this.getStatusIcon = (status) => {
                    const statusInfo = this.attendeeStatuses.get(status);
                    return statusInfo ? statusInfo.icon : 'fa-user';
                };
                
                // 获取字段类型
                this.getFieldType = (tag) => {
                    if (tag.isOrganizer) {
                        return 'organizer';
                    } else if (tag.required) {
                        return 'required';
                    } else {
                        return 'optional';
                    }
                };
                
                // 获取显示名称
                this.getDisplayName = (tag) => {
                    let displayName = tag.text || tag.name;
                    
                    if (tag.isOrganizer) {
                        displayName += ' (Organizer)';
                    }
                    
                    return displayName;
                };
                
                // 获取工具提示
                this.getTooltip = (tag) => {
                    const parts = [tag.displayName];
                    
                    if (tag.statusInfo) {
                        parts.push(`Status: ${tag.statusInfo.name}`);
                        parts.push(tag.statusInfo.description);
                    }
                    
                    if (tag.noEmail) {
                        parts.push('⚠️ Invalid email address');
                    }
                    
                    return parts.join('\n');
                };
                
                // 获取CSS类
                this.getCssClasses = (tag) => {
                    const classes = ['o-attendee-tag'];
                    
                    if (tag.status) {
                        classes.push(`o-attendee-status-${tag.status}`);
                    }
                    
                    if (tag.isOrganizer) {
                        classes.push('o-attendee-organizer');
                    }
                    
                    if (tag.noEmail) {
                        classes.push('o-attendee-invalid-email');
                    }
                    
                    if (tag.fieldType) {
                        classes.push(`o-attendee-type-${tag.fieldType}`);
                    }
                    
                    return classes;
                };
                
                // 排序标签
                this.sortTags = (tags, partnerIds) => {
                    // 查找组织者
                    const organizer = partnerIds.find(partner => partner.is_organizer);
                    
                    if (organizer) {
                        this.enhancedState.organizerId = organizer.id;
                        
                        // 按优先级排序
                        return tags.sort((a, b) => {
                            // 组织者优先
                            if (a.resId === organizer.id) return -1;
                            if (b.resId === organizer.id) return 1;
                            
                            // 按状态优先级排序
                            const aStatus = this.attendeeStatuses.get(a.status);
                            const bStatus = this.attendeeStatuses.get(b.status);
                            
                            if (aStatus && bStatus) {
                                const priorityDiff = bStatus.priority - aStatus.priority;
                                if (priorityDiff !== 0) return priorityDiff;
                            }
                            
                            // 按名称排序
                            return (a.displayName || '').localeCompare(b.displayName || '');
                        });
                    }
                    
                    return tags;
                };
                
                // 更新字段统计
                this.updateFieldStatistics = (tags) => {
                    this.enhancedState.attendeeCount = tags.length;
                    this.fieldStatistics.totalAttendees = tags.length;
                    
                    // 更新状态分布
                    this.enhancedState.statusCounts.clear();
                    for (const tag of tags) {
                        if (tag.status) {
                            const count = this.enhancedState.statusCounts.get(tag.status) || 0;
                            this.enhancedState.statusCounts.set(tag.status, count + 1);
                        }
                    }
                    
                    // 更新无效邮箱统计
                    this.fieldStatistics.invalidEmails = this.enhancedState.invalidEmails.size;
                    
                    this.enhancedState.lastUpdate = new Date();
                };
                
                // 处理标签点击
                this.handleTagClick = (tag) => {
                    this.fieldStatistics.tagClicks++;
                    
                    // 触发标签点击事件
                    this.env.bus.trigger('attendee-tag-clicked', {
                        tag: tag,
                        fieldName: this.props.name,
                        recordId: this.props.record.resId
                    });
                };
                
                // 处理工具提示显示
                this.handleTooltipShow = (tag) => {
                    this.fieldStatistics.tooltipShows++;
                };
                
                // 验证邮箱
                this.validateEmail = (email) => {
                    this.fieldStatistics.emailValidations++;
                    
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    return emailRegex.test(email);
                };
                
                // 初始化字段
                this.initializeField = () => {
                    this.enhancedState.attendeeCount = 0;
                    this.enhancedState.organizerId = null;
                    this.enhancedState.invalidEmails.clear();
                    this.enhancedState.statusCounts.clear();
                    this.enhancedState.lastUpdate = null;
                };
                
                // 处理标签错误
                this.handleTagsError = (error) => {
                    this.fieldStatistics.errorCount++;
                    console.error('Attendee tags error:', error);
                };
                
                // 获取字段信息
                this.getFieldInfo = () => {
                    return {
                        attendeeCount: this.enhancedState.attendeeCount,
                        organizerId: this.enhancedState.organizerId,
                        invalidEmailCount: this.enhancedState.invalidEmails.size,
                        statusCounts: Object.fromEntries(this.enhancedState.statusCounts),
                        lastUpdate: this.enhancedState.lastUpdate,
                        statistics: this.fieldStatistics,
                        config: this.fieldConfig
                    };
                };
                
                // 获取状态分布
                this.getStatusDistribution = () => {
                    const distribution = {};
                    const total = this.enhancedState.attendeeCount;
                    
                    for (const [status, count] of this.enhancedState.statusCounts.entries()) {
                        distribution[status] = total > 0 ? (count / total) * 100 : 0;
                    }
                    
                    return distribution;
                };
            }
            
            // 重写原始方法
            get tags() {
                return this.enhancedGetTags();
            }
        };
    }
    
    // 创建字段
    createField(props) {
        return new this.EnhancedMany2ManyAttendee(props);
    }
    
    // 注册字段
    registerField() {
        const enhancedFieldDefinition = {
            ...many2ManyAttendee,
            component: this.EnhancedMany2ManyAttendee,
            additionalClasses: ["o_field_many2many_attendee", "w-100"],
        };
        
        registry.category("fields").add("many2manyattendee", enhancedFieldDefinition);
    }
    
    // 获取字段统计
    getFieldStatistics() {
        return {
            ...this.fieldStatistics,
            emailValidationRate: this.fieldStatistics.emailValidations > 0 ? 
                ((this.fieldStatistics.emailValidations - this.fieldStatistics.invalidEmails) / this.fieldStatistics.emailValidations) * 100 : 0,
            clickRate: this.fieldStatistics.totalAttendees > 0 ? 
                (this.fieldStatistics.tagClicks / this.fieldStatistics.totalAttendees) * 100 : 0,
            tooltipRate: this.fieldStatistics.totalAttendees > 0 ? 
                (this.fieldStatistics.tooltipShows / this.fieldStatistics.totalAttendees) * 100 : 0,
            statusVariety: this.attendeeStatuses.size,
            typeVariety: this.fieldTypes.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理状态
        this.attendeeStatuses.clear();
        
        // 清理类型
        this.fieldTypes.clear();
        
        // 清理统计
        this.fieldStatistics.statusDistribution.clear();
        
        // 重置统计
        this.fieldStatistics = {
            totalAttendees: 0,
            statusDistribution: new Map(),
            emailValidations: 0,
            invalidEmails: 0,
            organizerChanges: 0,
            tagClicks: 0,
            tooltipShows: 0,
            errorCount: 0
        };
    }
}

// 使用示例
const fieldManager = new AttendeeFieldManager();

// 注册字段
fieldManager.registerField();

// 创建字段
const field = fieldManager.createField({
    name: 'partner_ids',
    record: eventRecord,
    readonly: false
});

// 获取统计信息
const stats = fieldManager.getFieldStatistics();
console.log('Field statistics:', stats);
```

## 技术特点

### 1. 数据增强
- **状态集成**: 集成参与者状态信息
- **图标映射**: 映射状态到图标
- **邮箱验证**: 验证参与者邮箱有效性
- **特殊数据**: 使用特殊数据钩子获取详情

### 2. 显示优化
- **头像显示**: 显示参与者头像
- **状态图标**: 显示状态图标
- **组织者标识**: 标识事件组织者
- **排序优化**: 优化参与者显示顺序

### 3. 字段继承
- **基类继承**: 继承多对多标签头像字段
- **功能扩展**: 扩展基础字段功能
- **模板定制**: 定制参与者显示模板
- **组件替换**: 替换标签列表组件

### 4. 注册机制
- **字段注册**: 注册到字段注册表
- **类型标识**: 使用"many2manyattendee"标识
- **样式类**: 添加专门的样式类
- **全局可用**: 使字段全局可用

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 装饰基础字段功能
- **数据装饰**: 装饰标签数据
- **显示装饰**: 装饰显示效果

### 2. 策略模式 (Strategy Pattern)
- **排序策略**: 不同的标签排序策略
- **显示策略**: 不同的显示策略
- **验证策略**: 不同的验证策略

### 3. 观察者模式 (Observer Pattern)
- **数据观察**: 观察参与者数据变化
- **状态观察**: 观察参与者状态变化
- **字段观察**: 观察字段值变化

### 4. 工厂模式 (Factory Pattern)
- **标签工厂**: 创建增强的标签对象
- **字段工厂**: 创建字段实例
- **组件工厂**: 创建组件实例

## 注意事项

1. **数据一致性**: 确保参与者数据的一致性
2. **性能优化**: 优化大量参与者的显示性能
3. **状态同步**: 保持状态信息的同步
4. **邮箱验证**: 正确验证邮箱有效性

## 扩展建议

1. **批量操作**: 支持批量参与者操作
2. **权限控制**: 添加参与者权限控制
3. **状态变更**: 支持直接变更参与者状态
4. **搜索过滤**: 添加参与者搜索过滤功能
5. **导入导出**: 支持参与者列表导入导出

该多对多参与者字段为Odoo Calendar模块提供了完整的参与者显示和管理功能，通过状态集成和显示优化确保了参与者信息的最佳展示效果。

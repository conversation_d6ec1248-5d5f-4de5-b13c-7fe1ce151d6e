# AttendeeTagsList - 参与者标签列表

## 概述

`attendee_tags_list.js` 是 Odoo Calendar 模块的参与者标签列表组件，负责显示参与者的标签列表。该模块包含13行代码，是一个功能专门的标签列表组件，专门用于显示日历事件参与者的标签形式，具备模板定制、标签显示、继承扩展等特性，是参与者显示的核心组件。

## 文件信息
- **路径**: `/calendar/static/src/views/fields/attendee_tags_list.js`
- **行数**: 13
- **模块**: `@calendar/views/fields/attendee_tags_list`

## 依赖关系

```javascript
// 核心依赖
'@web/core/tags_list/tags_list'               // 标签列表基类
```

## 核心功能

### 1. 组件定义

```javascript
class AttendeeTagsList extends TagsList {
    static template = "calendar.AttendeeTagsList";
}
```

**组件特性**:
- **继承扩展**: 继承标签列表基类
- **模板定制**: 使用专门的参与者标签模板
- **功能专用**: 专门用于参与者标签显示
- **简洁实现**: 简洁的组件实现

### 2. 模板定制

```javascript
static template = "calendar.AttendeeTagsList";
```

**模板功能**:
- **专用模板**: 使用专门的参与者标签模板
- **显示定制**: 定制参与者标签的显示方式
- **样式控制**: 控制标签的样式和布局
- **交互优化**: 优化用户交互体验

## 使用场景

### 1. 参与者标签管理器

```javascript
// 参与者标签管理器
class AttendeeTagsManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置标签配置
        this.tagsConfig = {
            enableStatusColors: true,
            enableHoverEffects: true,
            enableClickActions: true,
            enableTooltips: true,
            enableBadges: true,
            maxVisibleTags: 5,
            showMoreButton: true,
            compactMode: false
        };
        
        // 设置参与者状态
        this.attendeeStatuses = new Map([
            ['needsAction', {
                name: 'Needs Action',
                color: '#ffc107',
                textColor: '#212529',
                icon: 'fa-question-circle',
                priority: 1
            }],
            ['accepted', {
                name: 'Accepted',
                color: '#28a745',
                textColor: '#ffffff',
                icon: 'fa-check-circle',
                priority: 3
            }],
            ['declined', {
                name: 'Declined',
                color: '#dc3545',
                textColor: '#ffffff',
                icon: 'fa-times-circle',
                priority: 2
            }],
            ['tentative', {
                name: 'Tentative',
                color: '#6c757d',
                textColor: '#ffffff',
                icon: 'fa-clock',
                priority: 1
            }]
        ]);
        
        // 设置标签类型
        this.tagTypes = new Map([
            ['attendee', {
                name: 'Attendee',
                template: 'calendar.AttendeeTag',
                showStatus: true,
                showAvatar: true,
                clickable: true
            }],
            ['organizer', {
                name: 'Organizer',
                template: 'calendar.OrganizerTag',
                showStatus: false,
                showAvatar: true,
                clickable: true
            }],
            ['optional', {
                name: 'Optional',
                template: 'calendar.OptionalTag',
                showStatus: true,
                showAvatar: true,
                clickable: false
            }]
        ]);
        
        // 设置标签统计
        this.tagsStatistics = {
            totalTags: 0,
            visibleTags: 0,
            hiddenTags: 0,
            clickEvents: 0,
            hoverEvents: 0,
            statusChanges: 0,
            renderCount: 0,
            errorCount: 0
        };
        
        this.initializeTagsSystem();
    }
    
    // 初始化标签系统
    initializeTagsSystem() {
        // 创建增强的标签列表
        this.createEnhancedTagsList();
        
        // 设置标签样式系统
        this.setupTagStyleSystem();
        
        // 设置交互系统
        this.setupInteractionSystem();
        
        // 设置模板系统
        this.setupTemplateSystem();
    }
    
    // 创建增强的标签列表
    createEnhancedTagsList() {
        const originalTagsList = AttendeeTagsList;
        
        this.EnhancedAttendeeTagsList = class extends originalTagsList {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加交互功能
                this.addInteractionFeatures();
                
                // 添加样式功能
                this.addStyleFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    visibleCount: 0,
                    hiddenCount: 0,
                    expandedMode: false,
                    selectedTags: new Set(),
                    hoveredTag: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
                
                // 初始化标签列表
                this.initializeTagsList();
            }
            
            addEnhancedMethods() {
                // 增强的标签渲染
                this.enhancedRenderTags = () => {
                    try {
                        // 记录渲染
                        this.recordRender();
                        
                        // 准备标签数据
                        const tagsData = this.prepareTagsData();
                        
                        // 渲染标签
                        const renderedTags = this.renderTagsWithEnhancements(tagsData);
                        
                        // 应用样式
                        this.applyTagStyles(renderedTags);
                        
                        // 设置交互
                        this.setupTagInteractions(renderedTags);
                        
                        return renderedTags;
                        
                    } catch (error) {
                        this.handleRenderError(error);
                        return [];
                    }
                };
                
                // 准备标签数据
                this.prepareTagsData = () => {
                    const tags = this.props.tags || [];
                    const maxVisible = this.tagsConfig.maxVisibleTags;
                    
                    // 排序标签
                    const sortedTags = this.sortTags(tags);
                    
                    // 分离可见和隐藏标签
                    const visibleTags = sortedTags.slice(0, maxVisible);
                    const hiddenTags = sortedTags.slice(maxVisible);
                    
                    this.enhancedState.visibleCount = visibleTags.length;
                    this.enhancedState.hiddenCount = hiddenTags.length;
                    
                    return {
                        visible: visibleTags,
                        hidden: hiddenTags,
                        total: sortedTags.length
                    };
                };
                
                // 排序标签
                this.sortTags = (tags) => {
                    return tags.sort((a, b) => {
                        // 按状态优先级排序
                        const statusA = this.attendeeStatuses.get(a.status);
                        const statusB = this.attendeeStatuses.get(b.status);
                        
                        if (statusA && statusB) {
                            const priorityDiff = statusB.priority - statusA.priority;
                            if (priorityDiff !== 0) {
                                return priorityDiff;
                            }
                        }
                        
                        // 按名称排序
                        return (a.name || '').localeCompare(b.name || '');
                    });
                };
                
                // 渲染增强标签
                this.renderTagsWithEnhancements = (tagsData) => {
                    const renderedTags = [];
                    
                    // 渲染可见标签
                    for (const tag of tagsData.visible) {
                        const renderedTag = this.renderSingleTag(tag);
                        renderedTags.push(renderedTag);
                    }
                    
                    // 渲染"更多"按钮
                    if (tagsData.hidden.length > 0 && this.tagsConfig.showMoreButton) {
                        const moreButton = this.renderMoreButton(tagsData.hidden.length);
                        renderedTags.push(moreButton);
                    }
                    
                    return renderedTags;
                };
                
                // 渲染单个标签
                this.renderSingleTag = (tag) => {
                    const tagType = this.getTagType(tag);
                    const statusInfo = this.attendeeStatuses.get(tag.status);
                    
                    return {
                        id: tag.id,
                        name: tag.name,
                        type: tagType,
                        status: tag.status,
                        statusInfo: statusInfo,
                        avatar: tag.avatar,
                        clickable: this.isTagClickable(tag),
                        classes: this.getTagClasses(tag),
                        style: this.getTagStyle(tag)
                    };
                };
                
                // 渲染"更多"按钮
                this.renderMoreButton = (hiddenCount) => {
                    return {
                        id: 'more-button',
                        name: `+${hiddenCount} more`,
                        type: 'more',
                        clickable: true,
                        classes: ['o-attendee-tag', 'o-attendee-tag-more'],
                        style: this.getMoreButtonStyle()
                    };
                };
                
                // 获取标签类型
                this.getTagType = (tag) => {
                    if (tag.isOrganizer) {
                        return 'organizer';
                    } else if (tag.isOptional) {
                        return 'optional';
                    } else {
                        return 'attendee';
                    }
                };
                
                // 检查标签是否可点击
                this.isTagClickable = (tag) => {
                    const tagType = this.tagTypes.get(this.getTagType(tag));
                    return tagType ? tagType.clickable : false;
                };
                
                // 获取标签样式类
                this.getTagClasses = (tag) => {
                    const classes = ['o-attendee-tag'];
                    
                    // 添加类型样式
                    const tagType = this.getTagType(tag);
                    classes.push(`o-attendee-tag-${tagType}`);
                    
                    // 添加状态样式
                    if (tag.status) {
                        classes.push(`o-attendee-status-${tag.status}`);
                    }
                    
                    // 添加交互样式
                    if (this.isTagClickable(tag)) {
                        classes.push('o-attendee-tag-clickable');
                    }
                    
                    // 添加选中样式
                    if (this.enhancedState.selectedTags.has(tag.id)) {
                        classes.push('o-attendee-tag-selected');
                    }
                    
                    return classes;
                };
                
                // 获取标签样式
                this.getTagStyle = (tag) => {
                    const statusInfo = this.attendeeStatuses.get(tag.status);
                    const style = {};
                    
                    if (statusInfo && this.tagsConfig.enableStatusColors) {
                        style.backgroundColor = statusInfo.color;
                        style.color = statusInfo.textColor;
                    }
                    
                    return style;
                };
                
                // 获取"更多"按钮样式
                this.getMoreButtonStyle = () => {
                    return {
                        backgroundColor: '#f8f9fa',
                        color: '#6c757d',
                        border: '1px dashed #dee2e6'
                    };
                };
                
                // 应用标签样式
                this.applyTagStyles = (renderedTags) => {
                    for (const tag of renderedTags) {
                        this.applyTagStyle(tag);
                    }
                };
                
                // 应用单个标签样式
                this.applyTagStyle = (tag) => {
                    // 实现样式应用逻辑
                    console.log(`Applying style to tag: ${tag.name}`);
                };
                
                // 设置标签交互
                this.setupTagInteractions = (renderedTags) => {
                    for (const tag of renderedTags) {
                        if (tag.clickable) {
                            this.setupTagClickHandler(tag);
                        }
                        
                        if (this.tagsConfig.enableHoverEffects) {
                            this.setupTagHoverHandler(tag);
                        }
                    }
                };
                
                // 设置标签点击处理
                this.setupTagClickHandler = (tag) => {
                    tag.onClick = () => {
                        this.handleTagClick(tag);
                    };
                };
                
                // 设置标签悬停处理
                this.setupTagHoverHandler = (tag) => {
                    tag.onMouseEnter = () => {
                        this.handleTagHover(tag);
                    };
                    
                    tag.onMouseLeave = () => {
                        this.handleTagLeave(tag);
                    };
                };
                
                // 处理标签点击
                this.handleTagClick = (tag) => {
                    this.recordClickEvent();
                    
                    if (tag.type === 'more') {
                        this.toggleExpandedMode();
                    } else {
                        this.selectTag(tag);
                        this.triggerTagAction(tag);
                    }
                };
                
                // 处理标签悬停
                this.handleTagHover = (tag) => {
                    this.recordHoverEvent();
                    this.enhancedState.hoveredTag = tag;
                    
                    if (this.tagsConfig.enableTooltips) {
                        this.showTagTooltip(tag);
                    }
                };
                
                // 处理标签离开
                this.handleTagLeave = (tag) => {
                    this.enhancedState.hoveredTag = null;
                    this.hideTagTooltip(tag);
                };
                
                // 切换展开模式
                this.toggleExpandedMode = () => {
                    this.enhancedState.expandedMode = !this.enhancedState.expandedMode;
                    this.refreshTagsList();
                };
                
                // 选择标签
                this.selectTag = (tag) => {
                    if (this.enhancedState.selectedTags.has(tag.id)) {
                        this.enhancedState.selectedTags.delete(tag.id);
                    } else {
                        this.enhancedState.selectedTags.add(tag.id);
                    }
                };
                
                // 触发标签动作
                this.triggerTagAction = (tag) => {
                    // 触发标签相关动作
                    this.env.bus.trigger('attendee-tag-clicked', { tag });
                };
                
                // 显示标签工具提示
                this.showTagTooltip = (tag) => {
                    const tooltip = this.createTagTooltip(tag);
                    // 显示工具提示逻辑
                    console.log(`Showing tooltip for: ${tag.name}`);
                };
                
                // 隐藏标签工具提示
                this.hideTagTooltip = (tag) => {
                    // 隐藏工具提示逻辑
                    console.log(`Hiding tooltip for: ${tag.name}`);
                };
                
                // 创建标签工具提示
                this.createTagTooltip = (tag) => {
                    const statusInfo = this.attendeeStatuses.get(tag.status);
                    
                    return {
                        title: tag.name,
                        status: statusInfo ? statusInfo.name : 'Unknown',
                        email: tag.email,
                        phone: tag.phone
                    };
                };
                
                // 刷新标签列表
                this.refreshTagsList = () => {
                    this.enhancedRenderTags();
                };
                
                // 初始化标签列表
                this.initializeTagsList = () => {
                    this.enhancedState.visibleCount = 0;
                    this.enhancedState.hiddenCount = 0;
                    this.enhancedState.expandedMode = false;
                    this.enhancedState.selectedTags.clear();
                    this.enhancedState.hoveredTag = null;
                };
                
                // 记录渲染
                this.recordRender = () => {
                    this.tagsStatistics.renderCount++;
                };
                
                // 记录点击事件
                this.recordClickEvent = () => {
                    this.tagsStatistics.clickEvents++;
                };
                
                // 记录悬停事件
                this.recordHoverEvent = () => {
                    this.tagsStatistics.hoverEvents++;
                };
                
                // 处理渲染错误
                this.handleRenderError = (error) => {
                    this.tagsStatistics.errorCount++;
                    console.error('Tags render error:', error);
                };
                
                // 获取标签列表信息
                this.getTagsListInfo = () => {
                    return {
                        visibleCount: this.enhancedState.visibleCount,
                        hiddenCount: this.enhancedState.hiddenCount,
                        expandedMode: this.enhancedState.expandedMode,
                        selectedCount: this.enhancedState.selectedTags.size,
                        hoveredTag: this.enhancedState.hoveredTag,
                        statistics: this.tagsStatistics,
                        config: this.tagsConfig
                    };
                };
            }
            
            // 重写渲染方法
            render() {
                return this.enhancedRenderTags();
            }
        };
    }
    
    // 创建标签列表
    createTagsList(props) {
        return new this.EnhancedAttendeeTagsList(props);
    }
    
    // 获取标签统计
    getTagsStatistics() {
        return {
            ...this.tagsStatistics,
            clickRate: this.tagsStatistics.renderCount > 0 ? 
                (this.tagsStatistics.clickEvents / this.tagsStatistics.renderCount) * 100 : 0,
            hoverRate: this.tagsStatistics.renderCount > 0 ? 
                (this.tagsStatistics.hoverEvents / this.tagsStatistics.renderCount) * 100 : 0,
            errorRate: this.tagsStatistics.renderCount > 0 ? 
                (this.tagsStatistics.errorCount / this.tagsStatistics.renderCount) * 100 : 0,
            statusVariety: this.attendeeStatuses.size,
            typeVariety: this.tagTypes.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理状态
        this.attendeeStatuses.clear();
        
        // 清理类型
        this.tagTypes.clear();
        
        // 重置统计
        this.tagsStatistics = {
            totalTags: 0,
            visibleTags: 0,
            hiddenTags: 0,
            clickEvents: 0,
            hoverEvents: 0,
            statusChanges: 0,
            renderCount: 0,
            errorCount: 0
        };
    }
}

// 使用示例
const tagsManager = new AttendeeTagsManager();

// 创建标签列表
const tagsList = tagsManager.createTagsList({
    tags: [
        { id: 1, name: 'John Doe', status: 'accepted', email: '<EMAIL>' },
        { id: 2, name: 'Jane Smith', status: 'needsAction', email: '<EMAIL>' },
        { id: 3, name: 'Bob Wilson', status: 'declined', email: '<EMAIL>' }
    ]
});

// 获取统计信息
const stats = tagsManager.getTagsStatistics();
console.log('Tags statistics:', stats);
```

## 技术特点

### 1. 模板定制
- **专用模板**: 使用专门的参与者标签模板
- **显示优化**: 优化参与者标签的显示
- **样式控制**: 控制标签的样式和布局
- **交互增强**: 增强用户交互体验

### 2. 继承机制
- **基类继承**: 继承标签列表基类
- **功能扩展**: 扩展基础标签功能
- **行为定制**: 定制标签行为
- **接口一致**: 保持接口一致性

### 3. 组件简洁
- **简洁实现**: 简洁的组件实现
- **专用功能**: 专门用于参与者标签
- **易于维护**: 易于维护和扩展
- **性能优化**: 优化的性能表现

### 4. 扩展性
- **模板扩展**: 支持模板扩展
- **样式扩展**: 支持样式扩展
- **功能扩展**: 支持功能扩展
- **配置扩展**: 支持配置扩展

## 设计模式

### 1. 模板方法模式 (Template Method Pattern)
- **渲染模板**: 定义标签渲染模板
- **显示模板**: 定义标签显示模板
- **交互模板**: 定义交互处理模板

### 2. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 装饰基础标签功能
- **样式装饰**: 装饰标签样式
- **行为装饰**: 装饰标签行为

### 3. 策略模式 (Strategy Pattern)
- **显示策略**: 不同的标签显示策略
- **样式策略**: 不同的样式应用策略
- **交互策略**: 不同的交互处理策略

### 4. 组合模式 (Composite Pattern)
- **标签组合**: 组合多个标签
- **功能组合**: 组合不同功能
- **样式组合**: 组合不同样式

## 注意事项

1. **模板一致性**: 确保模板的一致性
2. **性能考虑**: 注意标签渲染性能
3. **样式兼容**: 确保样式兼容性
4. **交互体验**: 提供良好的交互体验

## 扩展建议

1. **状态指示**: 添加参与者状态指示
2. **头像显示**: 支持参与者头像显示
3. **工具提示**: 添加标签工具提示
4. **批量操作**: 支持批量标签操作
5. **自定义样式**: 支持自定义标签样式

该参与者标签列表组件为Odoo Calendar模块提供了专门的参与者标签显示功能，通过定制的模板和简洁的实现确保了参与者信息的最佳展示效果。

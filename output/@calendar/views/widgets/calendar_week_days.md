# CalendarWeekDays - 日历星期组件

## 概述

`calendar_week_days.js` 是 Odoo Calendar 模块的星期选择组件，负责提供日历事件的星期选择功能。该模块包含24行代码，是一个功能专门的小部件组件，专门用于处理重复事件的星期选择，具备模板定制、状态切换、记录更新、字段依赖等特性，是日历重复规则设置的核心组件。

## 文件信息
- **路径**: `/calendar/static/src/views/widgets/calendar_week_days.js`
- **行数**: 24
- **模块**: `@calendar/views/widgets/calendar_week_days`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                           // 注册表
'@web/views/widgets/week_days/week_days'       // 星期组件基类
```

## 核心功能

### 1. 组件定义

```javascript
class CalendarWeekDays extends WeekDays {
    static template = "calendar.WeekDays";
    onChange(day) {
        this.props.record.update({ [day]: !this.data[day] });
    }
}
```

**组件特性**:
- **继承扩展**: 继承标准星期组件
- **模板定制**: 使用专门的日历星期模板
- **状态切换**: 处理星期选择状态切换
- **记录更新**: 更新记录中的星期数据

### 2. 小部件定义

```javascript
const calendarWeekDays = {
    component: CalendarWeekDays,
    fieldDependencies: weekDays.fieldDependencies,
};
```

**小部件特性**:
- **组件映射**: 映射到日历星期组件
- **依赖继承**: 继承基础字段依赖
- **配置简化**: 简化的小部件配置
- **功能复用**: 复用基础功能

### 3. 状态变更

```javascript
onChange(day) {
    this.props.record.update({ [day]: !this.data[day] });
}
```

**变更功能**:
- **状态切换**: 切换指定星期的选中状态
- **数据更新**: 更新组件数据
- **记录同步**: 同步更新记录数据
- **响应式**: 响应用户交互

### 4. 注册机制

```javascript
registry.category("view_widgets").add("calendar_week_days", calendarWeekDays);
```

**注册功能**:
- **小部件注册**: 注册到视图小部件注册表
- **类别指定**: 指定为视图小部件类别
- **名称标识**: 使用"calendar_week_days"标识
- **全局可用**: 使小部件全局可用

## 使用场景

### 1. 日历星期组件管理器

```javascript
// 日历星期组件管理器
class CalendarWeekDaysManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置组件配置
        this.componentConfig = {
            enableMultiSelect: true,
            enableToggleAll: true,
            enableKeyboardNavigation: true,
            enableTooltips: true,
            enableValidation: true,
            showWeekendHighlight: true,
            defaultSelection: [],
            allowEmptySelection: false
        };
        
        // 设置星期定义
        this.weekDays = new Map([
            ['monday', {
                name: 'Monday',
                shortName: 'Mon',
                index: 0,
                isWeekend: false,
                color: '#007bff',
                key: 'mon'
            }],
            ['tuesday', {
                name: 'Tuesday',
                shortName: 'Tue',
                index: 1,
                isWeekend: false,
                color: '#007bff',
                key: 'tue'
            }],
            ['wednesday', {
                name: 'Wednesday',
                shortName: 'Wed',
                index: 2,
                isWeekend: false,
                color: '#007bff',
                key: 'wed'
            }],
            ['thursday', {
                name: 'Thursday',
                shortName: 'Thu',
                index: 3,
                isWeekend: false,
                color: '#007bff',
                key: 'thu'
            }],
            ['friday', {
                name: 'Friday',
                shortName: 'Fri',
                index: 4,
                isWeekend: false,
                color: '#007bff',
                key: 'fri'
            }],
            ['saturday', {
                name: 'Saturday',
                shortName: 'Sat',
                index: 5,
                isWeekend: true,
                color: '#6c757d',
                key: 'sat'
            }],
            ['sunday', {
                name: 'Sunday',
                shortName: 'Sun',
                index: 6,
                isWeekend: true,
                color: '#6c757d',
                key: 'sun'
            }]
        ]);
        
        // 设置预设模式
        this.presetModes = new Map([
            ['weekdays', {
                name: 'Weekdays Only',
                description: 'Monday to Friday',
                selection: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
                icon: 'fa-briefcase'
            }],
            ['weekends', {
                name: 'Weekends Only',
                description: 'Saturday and Sunday',
                selection: ['saturday', 'sunday'],
                icon: 'fa-home'
            }],
            ['all', {
                name: 'All Days',
                description: 'Every day of the week',
                selection: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
                icon: 'fa-calendar'
            }],
            ['custom', {
                name: 'Custom Selection',
                description: 'Custom day selection',
                selection: [],
                icon: 'fa-cog'
            }]
        ]);
        
        // 设置组件统计
        this.componentStatistics = {
            totalChanges: 0,
            dayToggles: new Map(),
            presetUsage: new Map(),
            validationErrors: 0,
            keyboardUsage: 0,
            tooltipShows: 0,
            errorCount: 0
        };
        
        this.initializeComponentSystem();
    }
    
    // 初始化组件系统
    initializeComponentSystem() {
        // 创建增强的组件
        this.createEnhancedComponent();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置预设系统
        this.setupPresetSystem();
        
        // 设置键盘系统
        this.setupKeyboardSystem();
    }
    
    // 创建增强的组件
    createEnhancedComponent() {
        const originalComponent = CalendarWeekDays;
        
        this.EnhancedCalendarWeekDays = class extends originalComponent {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
                
                // 添加键盘功能
                this.addKeyboardFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    selectedDays: new Set(),
                    lastChangedDay: null,
                    changeCount: 0,
                    validationErrors: [],
                    currentPreset: 'custom'
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
                
                // 初始化组件
                this.initializeComponent();
            }
            
            addEnhancedMethods() {
                // 增强的变更处理
                this.enhancedOnChange = (day) => {
                    try {
                        // 验证变更
                        if (!this.validateDayChange(day)) {
                            return;
                        }
                        
                        // 记录变更开始
                        this.recordChangeStart(day);
                        
                        // 执行状态切换
                        const newValue = !this.data[day];
                        this.updateDayState(day, newValue);
                        
                        // 更新记录
                        this.props.record.update({ [day]: newValue });
                        
                        // 更新选中集合
                        this.updateSelectedDays(day, newValue);
                        
                        // 检测预设模式
                        this.detectPresetMode();
                        
                        // 触发验证
                        this.validateSelection();
                        
                        // 记录变更完成
                        this.recordChangeComplete(day);
                        
                    } catch (error) {
                        this.handleChangeError(error, day);
                    }
                };
                
                // 验证星期变更
                this.validateDayChange = (day) => {
                    // 检查星期是否有效
                    if (!this.weekDays.has(day)) {
                        this.addValidationError(`Invalid day: ${day}`);
                        return false;
                    }
                    
                    // 检查是否允许空选择
                    if (!this.componentConfig.allowEmptySelection) {
                        const currentSelected = Array.from(this.enhancedState.selectedDays);
                        if (currentSelected.length === 1 && currentSelected.includes(day) && this.data[day]) {
                            this.addValidationError('At least one day must be selected');
                            return false;
                        }
                    }
                    
                    return true;
                };
                
                // 更新星期状态
                this.updateDayState = (day, value) => {
                    this.data[day] = value;
                    this.enhancedState.lastChangedDay = day;
                    this.enhancedState.changeCount++;
                };
                
                // 更新选中星期集合
                this.updateSelectedDays = (day, selected) => {
                    if (selected) {
                        this.enhancedState.selectedDays.add(day);
                    } else {
                        this.enhancedState.selectedDays.delete(day);
                    }
                };
                
                // 检测预设模式
                this.detectPresetMode = () => {
                    const selectedArray = Array.from(this.enhancedState.selectedDays).sort();
                    
                    for (const [presetName, preset] of this.presetModes.entries()) {
                        const presetArray = preset.selection.slice().sort();
                        if (this.arraysEqual(selectedArray, presetArray)) {
                            this.enhancedState.currentPreset = presetName;
                            return;
                        }
                    }
                    
                    this.enhancedState.currentPreset = 'custom';
                };
                
                // 数组比较
                this.arraysEqual = (arr1, arr2) => {
                    return arr1.length === arr2.length && 
                           arr1.every((val, index) => val === arr2[index]);
                };
                
                // 应用预设模式
                this.applyPreset = (presetName) => {
                    const preset = this.presetModes.get(presetName);
                    if (!preset) {
                        return;
                    }
                    
                    // 清除当前选择
                    this.clearSelection();
                    
                    // 应用预设选择
                    const updates = {};
                    for (const day of preset.selection) {
                        updates[day] = true;
                        this.enhancedState.selectedDays.add(day);
                        this.data[day] = true;
                    }
                    
                    // 更新记录
                    this.props.record.update(updates);
                    
                    // 更新当前预设
                    this.enhancedState.currentPreset = presetName;
                    
                    // 记录预设使用
                    this.recordPresetUsage(presetName);
                };
                
                // 清除选择
                this.clearSelection = () => {
                    const updates = {};
                    for (const day of this.weekDays.keys()) {
                        updates[day] = false;
                        this.data[day] = false;
                    }
                    
                    this.enhancedState.selectedDays.clear();
                    this.props.record.update(updates);
                };
                
                // 全选
                this.selectAll = () => {
                    this.applyPreset('all');
                };
                
                // 切换全选
                this.toggleAll = () => {
                    if (this.enhancedState.selectedDays.size === this.weekDays.size) {
                        this.clearSelection();
                    } else {
                        this.selectAll();
                    }
                };
                
                // 验证选择
                this.validateSelection = () => {
                    this.enhancedState.validationErrors = [];
                    
                    // 检查是否有选择
                    if (this.enhancedState.selectedDays.size === 0 && !this.componentConfig.allowEmptySelection) {
                        this.addValidationError('At least one day must be selected');
                    }
                    
                    // 检查最大选择
                    if (this.componentConfig.maxSelection && 
                        this.enhancedState.selectedDays.size > this.componentConfig.maxSelection) {
                        this.addValidationError(`Maximum ${this.componentConfig.maxSelection} days can be selected`);
                    }
                    
                    return this.enhancedState.validationErrors.length === 0;
                };
                
                // 添加验证错误
                this.addValidationError = (message) => {
                    this.enhancedState.validationErrors.push(message);
                    this.componentStatistics.validationErrors++;
                };
                
                // 处理键盘事件
                this.handleKeyboardEvent = (event) => {
                    this.componentStatistics.keyboardUsage++;
                    
                    switch (event.key) {
                        case 'ArrowLeft':
                        case 'ArrowRight':
                            this.navigateDays(event.key === 'ArrowLeft' ? -1 : 1);
                            event.preventDefault();
                            break;
                        case ' ':
                        case 'Enter':
                            if (this.focusedDay) {
                                this.enhancedOnChange(this.focusedDay);
                            }
                            event.preventDefault();
                            break;
                        case 'a':
                            if (event.ctrlKey) {
                                this.selectAll();
                                event.preventDefault();
                            }
                            break;
                    }
                };
                
                // 导航星期
                this.navigateDays = (direction) => {
                    const days = Array.from(this.weekDays.keys());
                    const currentIndex = days.indexOf(this.focusedDay || days[0]);
                    const newIndex = (currentIndex + direction + days.length) % days.length;
                    
                    this.focusedDay = days[newIndex];
                    this.focusDay(this.focusedDay);
                };
                
                // 聚焦星期
                this.focusDay = (day) => {
                    const element = document.querySelector(`[data-day="${day}"]`);
                    if (element) {
                        element.focus();
                    }
                };
                
                // 获取选中星期
                this.getSelectedDays = () => {
                    return Array.from(this.enhancedState.selectedDays);
                };
                
                // 获取星期信息
                this.getDayInfo = (day) => {
                    return this.weekDays.get(day);
                };
                
                // 获取当前预设
                this.getCurrentPreset = () => {
                    return this.presetModes.get(this.enhancedState.currentPreset);
                };
                
                // 初始化组件
                this.initializeComponent = () => {
                    // 初始化选中状态
                    for (const [day, value] of Object.entries(this.data)) {
                        if (value) {
                            this.enhancedState.selectedDays.add(day);
                        }
                    }
                    
                    // 检测初始预设
                    this.detectPresetMode();
                    
                    // 设置键盘监听
                    if (this.componentConfig.enableKeyboardNavigation) {
                        document.addEventListener('keydown', this.handleKeyboardEvent);
                    }
                };
                
                // 记录变更开始
                this.recordChangeStart = (day) => {
                    this.componentStatistics.totalChanges++;
                    const count = this.componentStatistics.dayToggles.get(day) || 0;
                    this.componentStatistics.dayToggles.set(day, count + 1);
                };
                
                // 记录变更完成
                this.recordChangeComplete = (day) => {
                    console.log(`Day ${day} toggled`);
                };
                
                // 记录预设使用
                this.recordPresetUsage = (presetName) => {
                    const count = this.componentStatistics.presetUsage.get(presetName) || 0;
                    this.componentStatistics.presetUsage.set(presetName, count + 1);
                };
                
                // 处理变更错误
                this.handleChangeError = (error, day) => {
                    this.componentStatistics.errorCount++;
                    console.error(`Day change error for ${day}:`, error);
                };
                
                // 获取组件信息
                this.getComponentInfo = () => {
                    return {
                        selectedDays: this.getSelectedDays(),
                        selectedCount: this.enhancedState.selectedDays.size,
                        lastChangedDay: this.enhancedState.lastChangedDay,
                        changeCount: this.enhancedState.changeCount,
                        currentPreset: this.enhancedState.currentPreset,
                        validationErrors: this.enhancedState.validationErrors,
                        statistics: this.componentStatistics,
                        config: this.componentConfig
                    };
                };
                
                // 清理资源
                this.cleanup = () => {
                    if (this.componentConfig.enableKeyboardNavigation) {
                        document.removeEventListener('keydown', this.handleKeyboardEvent);
                    }
                };
            }
            
            // 重写原始方法
            onChange(day) {
                this.enhancedOnChange(day);
            }
            
            // 组件销毁时清理
            willUnmount() {
                this.cleanup();
            }
        };
    }
    
    // 创建组件
    createComponent(props) {
        return new this.EnhancedCalendarWeekDays(props);
    }
    
    // 注册小部件
    registerWidget() {
        const enhancedWidget = {
            component: this.EnhancedCalendarWeekDays,
            fieldDependencies: weekDays.fieldDependencies,
        };
        
        registry.category("view_widgets").add("calendar_week_days", enhancedWidget);
    }
    
    // 获取组件统计
    getComponentStatistics() {
        return {
            ...this.componentStatistics,
            averageChangesPerDay: this.componentStatistics.totalChanges > 0 ? 
                this.componentStatistics.totalChanges / this.weekDays.size : 0,
            errorRate: this.componentStatistics.totalChanges > 0 ? 
                (this.componentStatistics.errorCount / this.componentStatistics.totalChanges) * 100 : 0,
            keyboardUsageRate: this.componentStatistics.totalChanges > 0 ? 
                (this.componentStatistics.keyboardUsage / this.componentStatistics.totalChanges) * 100 : 0,
            presetUsageDistribution: this.getPresetUsageDistribution(),
            dayToggleDistribution: this.getDayToggleDistribution()
        };
    }
    
    // 获取预设使用分布
    getPresetUsageDistribution() {
        const distribution = {};
        const total = Array.from(this.componentStatistics.presetUsage.values())
            .reduce((sum, count) => sum + count, 0);
        
        for (const [preset, count] of this.componentStatistics.presetUsage.entries()) {
            distribution[preset] = total > 0 ? (count / total) * 100 : 0;
        }
        
        return distribution;
    }
    
    // 获取星期切换分布
    getDayToggleDistribution() {
        const distribution = {};
        const total = Array.from(this.componentStatistics.dayToggles.values())
            .reduce((sum, count) => sum + count, 0);
        
        for (const [day, count] of this.componentStatistics.dayToggles.entries()) {
            distribution[day] = total > 0 ? (count / total) * 100 : 0;
        }
        
        return distribution;
    }
    
    // 销毁管理器
    destroy() {
        // 清理星期定义
        this.weekDays.clear();
        
        // 清理预设模式
        this.presetModes.clear();
        
        // 清理统计
        this.componentStatistics.dayToggles.clear();
        this.componentStatistics.presetUsage.clear();
        
        // 重置统计
        this.componentStatistics = {
            totalChanges: 0,
            dayToggles: new Map(),
            presetUsage: new Map(),
            validationErrors: 0,
            keyboardUsage: 0,
            tooltipShows: 0,
            errorCount: 0
        };
    }
}

// 使用示例
const weekDaysManager = new CalendarWeekDaysManager();

// 注册小部件
weekDaysManager.registerWidget();

// 创建组件
const component = weekDaysManager.createComponent({
    record: eventRecord,
    data: {
        monday: true,
        tuesday: true,
        wednesday: false,
        thursday: true,
        friday: true,
        saturday: false,
        sunday: false
    }
});

// 获取统计信息
const stats = weekDaysManager.getComponentStatistics();
console.log('Week days component statistics:', stats);
```

## 技术特点

### 1. 组件继承
- **基类继承**: 继承标准星期组件
- **功能扩展**: 扩展星期选择功能
- **模板定制**: 定制日历星期模板
- **行为增强**: 增强用户交互

### 2. 状态管理
- **状态切换**: 处理星期选择状态切换
- **数据同步**: 同步组件数据和记录数据
- **响应式**: 响应用户交互
- **状态持久**: 维持状态一致性

### 3. 记录更新
- **动态更新**: 动态更新记录字段
- **批量更新**: 支持批量字段更新
- **实时同步**: 实时同步数据变化
- **事务性**: 确保数据更新的事务性

### 4. 小部件注册
- **注册机制**: 注册到视图小部件注册表
- **依赖继承**: 继承基础字段依赖
- **全局可用**: 使小部件全局可用
- **配置简化**: 简化小部件配置

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 装饰基础星期组件功能
- **行为装饰**: 装饰用户交互行为
- **显示装饰**: 装饰显示效果

### 2. 状态模式 (State Pattern)
- **选择状态**: 管理星期选择状态
- **组件状态**: 管理组件内部状态
- **数据状态**: 管理数据同步状态

### 3. 观察者模式 (Observer Pattern)
- **状态观察**: 观察星期选择状态变化
- **数据观察**: 观察数据变化
- **用户观察**: 观察用户交互

### 4. 命令模式 (Command Pattern)
- **切换命令**: 封装状态切换操作
- **更新命令**: 封装记录更新操作
- **验证命令**: 封装验证操作

## 注意事项

1. **数据一致性**: 确保组件数据和记录数据的一致性
2. **状态验证**: 验证星期选择状态的有效性
3. **性能考虑**: 注意频繁状态切换的性能影响
4. **用户体验**: 提供清晰的状态反馈

## 扩展建议

1. **预设模式**: 添加常用的星期选择预设
2. **键盘支持**: 添加键盘导航支持
3. **批量操作**: 支持批量星期选择操作
4. **验证规则**: 添加自定义验证规则
5. **国际化**: 支持不同地区的星期显示

该日历星期组件为Odoo Calendar模块提供了完整的星期选择功能，通过状态管理和记录更新确保了重复事件星期规则设置的最佳用户体验。

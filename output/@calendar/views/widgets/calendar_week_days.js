/***********************************************************************
*  Filepath: /calendar/static/src/views/widgets/calendar_week_days.js  *
*  Lines: 24                                                           *
***********************************************************************/
odoo.define('@calendar/views/widgets/calendar_week_days', ['@web/core/registry', '@web/views/widgets/week_days/week_days'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { registry } = require("@web/core/registry");
const { WeekDays, weekDays } = require("@web/views/widgets/week_days/week_days");

const CalendarWeekDays = __exports.CalendarWeekDays = class CalendarWeekDays extends WeekDays {
    static template = "calendar.WeekDays";
    onChange(day) {
        this.props.record.update({ [day]: !this.data[day] });
    }
};

const calendarWeekDays = __exports.calendarWeekDays = {
    component: CalendarWeekDays,
    fieldDependencies: weekDays.fieldDependencies,
};

registry.category("view_widgets").add("calendar_week_days", calendarWeekDays);

return __exports;
});
/*************************************************************************
*  Filepath: /calendar/static/src/views/list_view/calendar_list_view.js  *
*  Lines: 48                                                             *
*************************************************************************/
odoo.define('@calendar/views/list_view/calendar_list_view', ['@web/views/list/list_view', '@web/core/registry', '@web/core/user'], function (require) {
'use strict';
let __exports = {};
const { listView } = require("@web/views/list/list_view");
const { registry } = require("@web/core/registry");
const { user } = require("@web/core/user");

const CalendarListModel = __exports.CalendarListModel = class CalendarListModel extends listView.Model {
    setup(params, { action, dialog, notification, rpc, user, view, company }) {
        super.setup(...arguments);
    }

    /**
    * @override
    * Add the calendar view's selected attendees to the list view's domain.
    */
    async load(params = {}) {
        const filters = params?.context?.calendar_filters;
        const emptyDomain = Array.isArray(params?.domain) && params.domain.length == 0;
        if (filters && emptyDomain) {
            const selectedPartnerIds = await this.orm.call(
                "res.users",
                "get_selected_calendars_partner_ids",
                [[user.userId], filters["user"]]
            );
            // Filter attendees to be shown if 'everybody' filter isn't active.
            if (!filters["all"])
                params.domain.push(["partner_ids", "in", selectedPartnerIds]);
        }
        return super.load(params);
    }
}

const CalendarListView = __exports.CalendarListView = {
    ...listView,
    Model: CalendarListModel,
};

function _mockGetCalendarPartnerIds(params) {
    /* Mock function for when there aren't records to be shown. */
    return [];
}

registry.category("views").add("calendar_list_view", CalendarListView);
registry.category("sample_server").add("get_selected_calendars_partner_ids", _mockGetCalendarPartnerIds);

return __exports;
});
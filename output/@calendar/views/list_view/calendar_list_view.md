# CalendarListView - 日历列表视图

## 概述

`calendar_list_view.js` 是 Odoo Calendar 模块的日历列表视图组件，负责提供日历事件的列表显示功能。该模块包含48行代码，是一个功能专门的列表视图组件，专门用于显示经过日历过滤器筛选的事件列表，具备过滤器集成、域名处理、参与者筛选、模拟数据支持等特性，是日历模块列表显示的核心组件。

## 文件信息
- **路径**: `/calendar/static/src/views/list_view/calendar_list_view.js`
- **行数**: 48
- **模块**: `@calendar/views/list_view/calendar_list_view`

## 依赖关系

```javascript
// 核心依赖
'@web/views/list/list_view'                    // 列表视图基类
'@web/core/registry'                           // 注册表
'@web/core/user'                               // 用户服务
```

## 核心功能

### 1. 模型定义

```javascript
class CalendarListModel extends listView.Model {
    setup(params, { action, dialog, notification, rpc, user, view, company }) {
        super.setup(...arguments);
    }
}
```

**模型特性**:
- **继承扩展**: 继承标准列表视图模型
- **服务注入**: 注入多个核心服务
- **参数传递**: 传递所有参数给父类
- **功能增强**: 增强列表视图功能

### 2. 数据加载

```javascript
async load(params = {}) {
    const filters = params?.context?.calendar_filters;
    const emptyDomain = Array.isArray(params?.domain) && params.domain.length == 0;
    if (filters && emptyDomain) {
        const selectedPartnerIds = await this.orm.call(
            "res.users",
            "get_selected_calendars_partner_ids",
            [[user.userId], filters["user"]]
        );
        // Filter attendees to be shown if 'everybody' filter isn't active.
        if (!filters["all"])
            params.domain.push(["partner_ids", "in", selectedPartnerIds]);
    }
    return super.load(params);
}
```

**加载功能**:
- **过滤器检查**: 检查日历过滤器是否存在
- **域名验证**: 验证域名是否为空
- **参与者获取**: 获取选中的日历参与者ID
- **域名扩展**: 扩展域名以过滤参与者
- **条件过滤**: 根据"所有人"过滤器决定是否过滤

### 3. 视图定义

```javascript
const CalendarListView = {
    ...listView,
    Model: CalendarListModel,
};
```

**视图特性**:
- **基础继承**: 继承标准列表视图配置
- **模型替换**: 替换为日历列表模型
- **功能保持**: 保持其他列表视图功能
- **简洁配置**: 简洁的视图配置

### 4. 模拟函数

```javascript
function _mockGetCalendarPartnerIds(params) {
    /* Mock function for when there aren't records to be shown. */
    return [];
}
```

**模拟功能**:
- **测试支持**: 支持测试环境
- **空数据**: 返回空的参与者ID列表
- **开发辅助**: 辅助开发和调试
- **服务器模拟**: 模拟服务器响应

## 使用场景

### 1. 日历列表视图管理器

```javascript
// 日历列表视图管理器
class CalendarListViewManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置视图配置
        this.viewConfig = {
            enableCalendarFilters: true,
            enableAttendeeFiltering: true,
            enableDomainExtension: true,
            enableMockSupport: true,
            enableUserFiltering: true,
            enableAllFilter: true,
            defaultPageSize: 80,
            enablePagination: true
        };
        
        // 设置过滤器类型
        this.filterTypes = new Map([
            ['user', {
                name: 'User Filter',
                description: 'Filter by specific users',
                field: 'user_id',
                type: 'many2one',
                relation: 'res.users'
            }],
            ['all', {
                name: 'All Filter',
                description: 'Show all events regardless of attendees',
                field: null,
                type: 'boolean',
                default: false
            }],
            ['partner', {
                name: 'Partner Filter',
                description: 'Filter by specific partners',
                field: 'partner_ids',
                type: 'many2many',
                relation: 'res.partner'
            }],
            ['calendar', {
                name: 'Calendar Filter',
                description: 'Filter by calendar type',
                field: 'calendar_id',
                type: 'many2one',
                relation: 'calendar.calendar'
            }]
        ]);
        
        // 设置域名操作
        this.domainOperations = new Map([
            ['in', {
                name: 'In',
                operator: 'in',
                description: 'Field value is in the list',
                multiValue: true
            }],
            ['not_in', {
                name: 'Not In',
                operator: 'not in',
                description: 'Field value is not in the list',
                multiValue: true
            }],
            ['equals', {
                name: 'Equals',
                operator: '=',
                description: 'Field value equals the value',
                multiValue: false
            }],
            ['not_equals', {
                name: 'Not Equals',
                operator: '!=',
                description: 'Field value does not equal the value',
                multiValue: false
            }]
        ]);
        
        // 设置视图统计
        this.viewStatistics = {
            totalLoads: 0,
            filteredLoads: 0,
            domainExtensions: 0,
            partnerIdQueries: 0,
            mockCalls: 0,
            emptyDomainChecks: 0,
            allFilterUsage: 0,
            errorCount: 0
        };
        
        this.initializeViewSystem();
    }
    
    // 初始化视图系统
    initializeViewSystem() {
        // 创建增强的视图
        this.createEnhancedView();
        
        // 设置过滤系统
        this.setupFilterSystem();
        
        // 设置域名系统
        this.setupDomainSystem();
        
        // 设置模拟系统
        this.setupMockSystem();
    }
    
    // 创建增强的视图
    createEnhancedView() {
        // 增强的列表模型
        this.EnhancedCalendarListModel = class extends CalendarListModel {
            setup(params, services) {
                super.setup(params, services);
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加缓存功能
                this.addCacheFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    lastFilters: null,
                    partnerIdCache: new Map(),
                    domainHistory: [],
                    loadCount: 0,
                    lastLoadTime: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的加载方法
                this.enhancedLoad = async (params = {}) => {
                    try {
                        // 记录加载开始
                        this.recordLoadStart();
                        
                        // 处理日历过滤器
                        const processedParams = await this.processCalendarFilters(params);
                        
                        // 执行基础加载
                        const result = await super.load(processedParams);
                        
                        // 记录加载完成
                        this.recordLoadComplete();
                        
                        return result;
                        
                    } catch (error) {
                        this.handleLoadError(error);
                        throw error;
                    }
                };
                
                // 处理日历过滤器
                this.processCalendarFilters = async (params) => {
                    const filters = params?.context?.calendar_filters;
                    const emptyDomain = this.isEmptyDomain(params?.domain);
                    
                    if (filters && emptyDomain) {
                        // 记录过滤加载
                        this.recordFilteredLoad();
                        
                        // 获取选中的参与者ID
                        const selectedPartnerIds = await this.getSelectedPartnerIds(filters);
                        
                        // 扩展域名
                        if (!filters["all"]) {
                            this.extendDomain(params, selectedPartnerIds);
                        } else {
                            this.recordAllFilterUsage();
                        }
                    }
                    
                    return params;
                };
                
                // 检查是否为空域名
                this.isEmptyDomain = (domain) => {
                    const isEmpty = Array.isArray(domain) && domain.length === 0;
                    if (isEmpty) {
                        this.recordEmptyDomainCheck();
                    }
                    return isEmpty;
                };
                
                // 获取选中的参与者ID
                this.getSelectedPartnerIds = async (filters) => {
                    // 检查缓存
                    const cacheKey = this.getPartnerIdCacheKey(filters);
                    if (this.enhancedState.partnerIdCache.has(cacheKey)) {
                        return this.enhancedState.partnerIdCache.get(cacheKey);
                    }
                    
                    // 调用服务器
                    const selectedPartnerIds = await this.orm.call(
                        "res.users",
                        "get_selected_calendars_partner_ids",
                        [[user.userId], filters["user"]]
                    );
                    
                    // 缓存结果
                    this.enhancedState.partnerIdCache.set(cacheKey, selectedPartnerIds);
                    
                    // 记录查询
                    this.recordPartnerIdQuery();
                    
                    return selectedPartnerIds;
                };
                
                // 扩展域名
                this.extendDomain = (params, selectedPartnerIds) => {
                    const domainClause = ["partner_ids", "in", selectedPartnerIds];
                    params.domain.push(domainClause);
                    
                    // 记录域名扩展
                    this.recordDomainExtension(domainClause);
                };
                
                // 获取参与者ID缓存键
                this.getPartnerIdCacheKey = (filters) => {
                    return JSON.stringify({
                        userId: user.userId,
                        userFilter: filters["user"]
                    });
                };
                
                // 验证过滤器
                this.validateFilters = (filters) => {
                    if (!filters || typeof filters !== 'object') {
                        return false;
                    }
                    
                    // 验证必需的过滤器属性
                    return true;
                };
                
                // 验证域名
                this.validateDomain = (domain) => {
                    if (!Array.isArray(domain)) {
                        return false;
                    }
                    
                    // 验证域名格式
                    for (const clause of domain) {
                        if (!Array.isArray(clause) || clause.length !== 3) {
                            return false;
                        }
                    }
                    
                    return true;
                };
                
                // 记录加载开始
                this.recordLoadStart = () => {
                    this.enhancedState.lastLoadTime = Date.now();
                    this.enhancedState.loadCount++;
                    this.viewStatistics.totalLoads++;
                };
                
                // 记录加载完成
                this.recordLoadComplete = () => {
                    const loadTime = Date.now() - this.enhancedState.lastLoadTime;
                    console.log(`List view loaded in ${loadTime}ms`);
                };
                
                // 记录过滤加载
                this.recordFilteredLoad = () => {
                    this.viewStatistics.filteredLoads++;
                };
                
                // 记录域名扩展
                this.recordDomainExtension = (clause) => {
                    this.viewStatistics.domainExtensions++;
                    this.enhancedState.domainHistory.push({
                        clause: clause,
                        timestamp: new Date()
                    });
                };
                
                // 记录参与者ID查询
                this.recordPartnerIdQuery = () => {
                    this.viewStatistics.partnerIdQueries++;
                };
                
                // 记录空域名检查
                this.recordEmptyDomainCheck = () => {
                    this.viewStatistics.emptyDomainChecks++;
                };
                
                // 记录所有过滤器使用
                this.recordAllFilterUsage = () => {
                    this.viewStatistics.allFilterUsage++;
                };
                
                // 处理加载错误
                this.handleLoadError = (error) => {
                    this.viewStatistics.errorCount++;
                    console.error('Calendar list view load error:', error);
                };
                
                // 获取模型信息
                this.getModelInfo = () => {
                    return {
                        loadCount: this.enhancedState.loadCount,
                        lastLoadTime: this.enhancedState.lastLoadTime,
                        cacheSize: this.enhancedState.partnerIdCache.size,
                        domainHistorySize: this.enhancedState.domainHistory.length,
                        statistics: this.viewStatistics,
                        config: this.viewConfig
                    };
                };
                
                // 清理缓存
                this.clearCache = () => {
                    this.enhancedState.partnerIdCache.clear();
                    this.enhancedState.domainHistory = [];
                };
            }
            
            // 重写原始方法
            async load(params = {}) {
                return await this.enhancedLoad(params);
            }
        };
        
        // 增强的列表视图
        this.EnhancedCalendarListView = {
            ...listView,
            Model: this.EnhancedCalendarListModel,
        };
    }
    
    // 增强的模拟函数
    createEnhancedMockFunction() {
        return (params) => {
            this.viewStatistics.mockCalls++;
            
            // 模拟不同的响应
            if (params && params.testMode) {
                return [1, 2, 3]; // 测试数据
            }
            
            return []; // 默认空数据
        };
    }
    
    // 注册视图
    registerView() {
        // 注册增强的视图
        registry.category("views").add("calendar_list_view", this.EnhancedCalendarListView);
        
        // 注册增强的模拟函数
        registry.category("sample_server").add(
            "get_selected_calendars_partner_ids", 
            this.createEnhancedMockFunction()
        );
    }
    
    // 创建视图
    createView(props) {
        return new this.EnhancedCalendarListView(props);
    }
    
    // 获取过滤器信息
    getFilterInfo(filterType) {
        return this.filterTypes.get(filterType);
    }
    
    // 获取域名操作信息
    getDomainOperationInfo(operation) {
        return this.domainOperations.get(operation);
    }
    
    // 获取视图统计
    getViewStatistics() {
        return {
            ...this.viewStatistics,
            filterUsageRate: this.viewStatistics.totalLoads > 0 ? 
                (this.viewStatistics.filteredLoads / this.viewStatistics.totalLoads) * 100 : 0,
            domainExtensionRate: this.viewStatistics.filteredLoads > 0 ? 
                (this.viewStatistics.domainExtensions / this.viewStatistics.filteredLoads) * 100 : 0,
            allFilterUsageRate: this.viewStatistics.filteredLoads > 0 ? 
                (this.viewStatistics.allFilterUsage / this.viewStatistics.filteredLoads) * 100 : 0,
            errorRate: this.viewStatistics.totalLoads > 0 ? 
                (this.viewStatistics.errorCount / this.viewStatistics.totalLoads) * 100 : 0,
            filterTypeVariety: this.filterTypes.size,
            operationVariety: this.domainOperations.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理过滤器类型
        this.filterTypes.clear();
        
        // 清理域名操作
        this.domainOperations.clear();
        
        // 重置统计
        this.viewStatistics = {
            totalLoads: 0,
            filteredLoads: 0,
            domainExtensions: 0,
            partnerIdQueries: 0,
            mockCalls: 0,
            emptyDomainChecks: 0,
            allFilterUsage: 0,
            errorCount: 0
        };
    }
}

// 使用示例
const listViewManager = new CalendarListViewManager();

// 注册视图
listViewManager.registerView();

// 创建视图
const listView = listViewManager.createView({
    resModel: 'calendar.event',
    context: {
        calendar_filters: {
            user: [1, 2, 3],
            all: false
        }
    }
});

// 获取统计信息
const stats = listViewManager.getViewStatistics();
console.log('List view statistics:', stats);
```

## 技术特点

### 1. 过滤器集成
- **日历过滤器**: 集成日历视图的过滤器
- **参与者过滤**: 根据参与者过滤事件
- **用户过滤**: 支持用户级别的过滤
- **全局过滤**: 支持"所有人"过滤器

### 2. 域名处理
- **域名检查**: 检查域名是否为空
- **域名扩展**: 动态扩展域名条件
- **条件构建**: 构建参与者过滤条件
- **逻辑控制**: 控制过滤逻辑

### 3. 数据获取
- **异步调用**: 异步获取参与者ID
- **服务器交互**: 与服务器交互获取数据
- **用户上下文**: 基于当前用户获取数据
- **缓存支持**: 支持数据缓存

### 4. 模拟支持
- **测试环境**: 支持测试环境
- **开发辅助**: 辅助开发调试
- **空数据**: 提供空数据响应
- **服务器模拟**: 模拟服务器行为

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 装饰基础列表视图功能
- **模型装饰**: 装饰列表模型
- **加载装饰**: 装饰数据加载过程

### 2. 策略模式 (Strategy Pattern)
- **过滤策略**: 不同的过滤策略
- **加载策略**: 不同的数据加载策略
- **域名策略**: 不同的域名处理策略

### 3. 工厂模式 (Factory Pattern)
- **视图工厂**: 创建列表视图实例
- **模型工厂**: 创建列表模型实例
- **过滤器工厂**: 创建过滤器对象

### 4. 适配器模式 (Adapter Pattern)
- **过滤器适配**: 适配日历过滤器到列表视图
- **数据适配**: 适配日历数据到列表格式
- **接口适配**: 适配不同的接口

## 注意事项

1. **性能考虑**: 注意大量数据的加载性能
2. **过滤器验证**: 验证过滤器的有效性
3. **域名安全**: 确保域名构建的安全性
4. **缓存管理**: 管理参与者ID缓存

## 扩展建议

1. **高级过滤**: 支持更复杂的过滤条件
2. **排序功能**: 添加自定义排序功能
3. **分组显示**: 支持按参与者分组显示
4. **导出功能**: 添加列表导出功能
5. **实时更新**: 支持实时数据更新

该日历列表视图为Odoo Calendar模块提供了完整的列表显示功能，通过过滤器集成和智能的域名处理确保了日历事件列表的精确显示。

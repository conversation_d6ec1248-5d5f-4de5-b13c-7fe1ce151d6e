# AttendeeCalendarCommonPopover - 参与者日历通用弹出框

## 概述

`attendee_calendar_common_popover.js` 是 Odoo Calendar 模块的参与者日历通用弹出框组件，负责显示参与者视角的事件详情弹出框。该模块包含148行代码，是一个功能完整的弹出框组件，专门用于处理参与者日历事件的详情显示和交互，具备状态管理、权限控制、参与者操作、重复事件处理等特性，是参与者日历交互的核心组件。

## 文件信息
- **路径**: `/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_popover.js`
- **行数**: 148
- **模块**: `@calendar/views/attendee_calendar/common/attendee_calendar_common_popover`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                    // OWL框架
'@web/views/calendar/calendar_common/calendar_common_popover' // 日历通用弹出框基类
'@web/core/utils/hooks'                        // 工具钩子
'@calendar/views/ask_recurrence_update_policy_hook' // 重复更新策略钩子
'@web/core/dropdown/dropdown'                  // 下拉组件
'@web/core/dropdown/dropdown_item'             // 下拉项组件
'@web/core/user'                               // 用户服务
```

## 核心功能

### 1. 组件定义

```javascript
class AttendeeCalendarCommonPopover extends CalendarCommonPopover {
    static components = {
        ...CalendarCommonPopover.components,
        Dropdown,
        DropdownItem,
    };
    static subTemplates = {
        ...CalendarCommonPopover.subTemplates,
        body: "calendar.AttendeeCalendarCommonPopover.body",
        footer: "calendar.AttendeeCalendarCommonPopover.footer",
    };
}
```

**组件特性**:
- **继承扩展**: 继承日历通用弹出框
- **组件集成**: 集成下拉组件
- **模板定制**: 定制主体和底部模板
- **功能增强**: 增强参与者相关功能

### 2. 状态初始化

```javascript
async onWillStart() {
    if (this.isEventEditable) {
        const stateSelections = await this.env.services.orm.call(
            this.props.model.resModel,
            "get_state_selections"
        );
        this.statusColors = {
            accepted: "text-success",
            declined: "text-danger",
            tentative: "text-muted",
            needsAction: "false",
        };
        this.statusInfo = {};
        for (const selection of stateSelections) {
            this.statusInfo[selection[0]] = {
                text: selection[1],
                color: this.statusColors[selection[0]],
            };
        }
        this.selectedStatusInfo = this.statusInfo[this.props.record.attendeeStatus];
    }
}
```

**初始化功能**:
- **状态获取**: 获取参与者状态选项
- **颜色映射**: 映射状态到颜色
- **状态信息**: 构建状态信息对象
- **当前状态**: 设置当前选中状态

### 3. 权限检查

```javascript
get isCurrentUserAttendee() {
    return (
        this.props.record.rawRecord.partner_ids.includes(user.partnerId) ||
        this.props.record.rawRecord.partner_id[0] === user.partnerId
    );
}

get isCurrentUserOrganizer() {
    return this.props.record.rawRecord.partner_id[0] === user.partnerId;
}

get isEventPrivate() {
    return this.props.record.rawRecord.privacy === "private";
}
```

**权限功能**:
- **参与者检查**: 检查当前用户是否为参与者
- **组织者检查**: 检查当前用户是否为组织者
- **隐私检查**: 检查事件是否为私有
- **权限控制**: 基于权限控制功能显示

### 4. 状态变更

```javascript
async changeAttendeeStatus(selectedStatus) {
    const record = this.props.record;
    if (record.attendeeStatus === selectedStatus) {
        return this.props.close();
    }
    let recurrenceUpdate = false;
    if (record.rawRecord.recurrency) {
        recurrenceUpdate = await this.askRecurrenceUpdatePolicy();
        if (!recurrenceUpdate) {
            return this.props.close();
        }
    }
    await this.env.services.orm.call(this.props.model.resModel, "change_attendee_status", [
        [record.id],
        selectedStatus,
        recurrenceUpdate,
    ]);
    await this.props.model.load();
    this.props.close();
}
```

**状态变更功能**:
- **状态比较**: 比较新旧状态
- **重复处理**: 处理重复事件策略
- **状态更新**: 调用服务器更新状态
- **模型重载**: 重新加载模型数据
- **弹出框关闭**: 操作完成后关闭

### 5. 记录打开

```javascript
async onClickOpenRecord() {
    const action = await this.orm.call("calendar.event", "action_open_calendar_event", [
        this.props.record.id,
    ]);
    this.actionService.doAction(action);
}
```

**打开功能**:
- **动作获取**: 获取打开事件的动作
- **动作执行**: 执行打开动作
- **详情显示**: 显示事件详情表单
- **服务集成**: 集成动作服务

## 使用场景

### 1. 参与者弹出框管理器

```javascript
// 参与者弹出框管理器
class AttendeePopoverManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置弹出框配置
        this.popoverConfig = {
            enableStatusDropdown: true,
            enablePrivacyControl: true,
            enableRecurrenceHandling: true,
            enablePermissionCheck: true,
            enableArchiveFunction: false,
            autoCloseOnAction: true,
            showStatusColors: true,
            defaultPosition: 'bottom'
        };
        
        // 设置参与者状态
        this.attendeeStatuses = new Map([
            ['needsAction', {
                name: 'Needs Action',
                text: 'Needs Action',
                color: 'text-warning',
                icon: 'fa-question-circle',
                canChange: true,
                isDefault: true
            }],
            ['accepted', {
                name: 'Accepted',
                text: 'Accepted',
                color: 'text-success',
                icon: 'fa-check-circle',
                canChange: true,
                isDefault: false
            }],
            ['declined', {
                name: 'Declined',
                text: 'Declined',
                color: 'text-danger',
                icon: 'fa-times-circle',
                canChange: true,
                isDefault: false
            }],
            ['tentative', {
                name: 'Tentative',
                text: 'Maybe',
                color: 'text-muted',
                icon: 'fa-clock',
                canChange: true,
                isDefault: false
            }]
        ]);
        
        // 设置权限类型
        this.permissionTypes = new Map([
            ['organizer', {
                name: 'Organizer',
                canEdit: true,
                canDelete: true,
                canArchive: true,
                canChangeStatus: true,
                canViewPrivate: true
            }],
            ['attendee', {
                name: 'Attendee',
                canEdit: false,
                canDelete: false,
                canArchive: false,
                canChangeStatus: true,
                canViewPrivate: false
            }],
            ['viewer', {
                name: 'Viewer',
                canEdit: false,
                canDelete: false,
                canArchive: false,
                canChangeStatus: false,
                canViewPrivate: false
            }]
        ]);
        
        // 设置弹出框统计
        this.popoverStatistics = {
            totalOpens: 0,
            statusChanges: 0,
            recordOpens: 0,
            archiveOperations: 0,
            recurrenceHandlings: 0,
            permissionChecks: 0,
            errorCount: 0,
            averageOpenTime: 0,
            totalOpenTime: 0
        };
        
        this.initializePopoverSystem();
    }
    
    // 初始化弹出框系统
    initializePopoverSystem() {
        // 创建增强的弹出框
        this.createEnhancedPopover();
        
        // 设置权限系统
        this.setupPermissionSystem();
        
        // 设置状态系统
        this.setupStatusSystem();
        
        // 设置交互系统
        this.setupInteractionSystem();
    }
    
    // 创建增强的弹出框
    createEnhancedPopover() {
        const originalPopover = AttendeeCalendarCommonPopover;
        
        this.EnhancedAttendeeCalendarCommonPopover = class extends originalPopover {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加分析功能
                this.addAnalyticsFeatures();
                
                // 添加缓存功能
                this.addCacheFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    openTime: null,
                    interactionCount: 0,
                    lastAction: null,
                    permissionLevel: 'viewer',
                    statusCache: new Map(),
                    isLoading: false
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
                
                // 初始化弹出框
                this.initializePopover();
            }
            
            addEnhancedMethods() {
                // 增强的初始化
                this.enhancedOnWillStart = async () => {
                    try {
                        // 记录打开时间
                        this.recordPopoverOpen();
                        
                        // 检查权限
                        this.checkUserPermissions();
                        
                        // 执行原始初始化
                        await super.onWillStart();
                        
                        // 增强状态信息
                        this.enhanceStatusInfo();
                        
                    } catch (error) {
                        this.handleInitializationError(error);
                    }
                };
                
                // 增强的状态变更
                this.enhancedChangeAttendeeStatus = async (selectedStatus) => {
                    try {
                        // 记录状态变更
                        this.recordStatusChange(selectedStatus);
                        
                        // 验证状态变更
                        if (!this.validateStatusChange(selectedStatus)) {
                            return;
                        }
                        
                        // 检查是否需要确认
                        const confirmed = await this.confirmStatusChange(selectedStatus);
                        if (!confirmed) {
                            return;
                        }
                        
                        // 设置加载状态
                        this.setLoadingState(true);
                        
                        // 执行状态变更
                        await this.executeStatusChange(selectedStatus);
                        
                        // 记录成功
                        this.recordStatusChangeSuccess(selectedStatus);
                        
                    } catch (error) {
                        this.handleStatusChangeError(error, selectedStatus);
                    } finally {
                        this.setLoadingState(false);
                    }
                };
                
                // 执行状态变更
                this.executeStatusChange = async (selectedStatus) => {
                    const record = this.props.record;
                    
                    // 检查状态是否相同
                    if (record.attendeeStatus === selectedStatus) {
                        return this.props.close();
                    }
                    
                    // 处理重复事件
                    let recurrenceUpdate = false;
                    if (record.rawRecord.recurrency) {
                        recurrenceUpdate = await this.handleRecurrenceUpdate();
                        if (!recurrenceUpdate) {
                            return this.props.close();
                        }
                    }
                    
                    // 调用服务器更新
                    await this.env.services.orm.call(
                        this.props.model.resModel, 
                        "change_attendee_status", 
                        [[record.id], selectedStatus, recurrenceUpdate]
                    );
                    
                    // 重新加载模型
                    await this.props.model.load();
                    
                    // 关闭弹出框
                    this.props.close();
                };
                
                // 处理重复更新
                this.handleRecurrenceUpdate = async () => {
                    this.recordRecurrenceHandling();
                    const policy = await this.askRecurrenceUpdatePolicy();
                    return policy;
                };
                
                // 增强的记录打开
                this.enhancedOnClickOpenRecord = async () => {
                    try {
                        // 记录打开操作
                        this.recordRecordOpen();
                        
                        // 检查查看权限
                        if (!this.checkViewPermission()) {
                            this.showPermissionError();
                            return;
                        }
                        
                        // 获取打开动作
                        const action = await this.getOpenAction();
                        
                        // 执行动作
                        this.actionService.doAction(action);
                        
                        // 关闭弹出框
                        this.props.close();
                        
                    } catch (error) {
                        this.handleRecordOpenError(error);
                    }
                };
                
                // 获取打开动作
                this.getOpenAction = async () => {
                    return await this.orm.call("calendar.event", "action_open_calendar_event", [
                        this.props.record.id,
                    ]);
                };
                
                // 检查用户权限
                this.checkUserPermissions = () => {
                    if (this.isCurrentUserOrganizer) {
                        this.enhancedState.permissionLevel = 'organizer';
                    } else if (this.isCurrentUserAttendee) {
                        this.enhancedState.permissionLevel = 'attendee';
                    } else {
                        this.enhancedState.permissionLevel = 'viewer';
                    }
                    
                    this.popoverStatistics.permissionChecks++;
                };
                
                // 检查查看权限
                this.checkViewPermission = () => {
                    const permission = this.permissionTypes.get(this.enhancedState.permissionLevel);
                    
                    if (this.isEventPrivate) {
                        return permission.canViewPrivate;
                    }
                    
                    return true;
                };
                
                // 验证状态变更
                this.validateStatusChange = (selectedStatus) => {
                    // 检查状态是否有效
                    if (!this.attendeeStatuses.has(selectedStatus)) {
                        return false;
                    }
                    
                    // 检查是否可以变更状态
                    const permission = this.permissionTypes.get(this.enhancedState.permissionLevel);
                    if (!permission.canChangeStatus) {
                        return false;
                    }
                    
                    return true;
                };
                
                // 确认状态变更
                this.confirmStatusChange = async (selectedStatus) => {
                    const statusInfo = this.attendeeStatuses.get(selectedStatus);
                    
                    // 对于拒绝状态，需要确认
                    if (selectedStatus === 'declined') {
                        return await this.showConfirmDialog(
                            'Decline Event',
                            `Are you sure you want to decline this event?`
                        );
                    }
                    
                    return true;
                };
                
                // 显示确认对话框
                this.showConfirmDialog = async (title, message) => {
                    return new Promise((resolve) => {
                        const dialog = useService("dialog");
                        dialog.add(ConfirmationDialog, {
                            title: title,
                            body: message,
                            confirm: () => resolve(true),
                            cancel: () => resolve(false)
                        });
                    });
                };
                
                // 增强状态信息
                this.enhanceStatusInfo = () => {
                    if (this.statusInfo) {
                        for (const [status, info] of Object.entries(this.statusInfo)) {
                            const enhancedInfo = this.attendeeStatuses.get(status);
                            if (enhancedInfo) {
                                info.icon = enhancedInfo.icon;
                                info.canChange = enhancedInfo.canChange;
                            }
                        }
                    }
                };
                
                // 设置加载状态
                this.setLoadingState = (loading) => {
                    this.enhancedState.isLoading = loading;
                };
                
                // 初始化弹出框
                this.initializePopover = () => {
                    this.enhancedState.openTime = Date.now();
                };
                
                // 记录弹出框打开
                this.recordPopoverOpen = () => {
                    this.popoverStatistics.totalOpens++;
                };
                
                // 记录状态变更
                this.recordStatusChange = (status) => {
                    this.enhancedState.lastAction = `status_change_${status}`;
                    this.enhancedState.interactionCount++;
                    this.popoverStatistics.statusChanges++;
                };
                
                // 记录状态变更成功
                this.recordStatusChangeSuccess = (status) => {
                    console.log(`Status changed to: ${status}`);
                };
                
                // 记录记录打开
                this.recordRecordOpen = () => {
                    this.enhancedState.lastAction = 'record_open';
                    this.enhancedState.interactionCount++;
                    this.popoverStatistics.recordOpens++;
                };
                
                // 记录重复处理
                this.recordRecurrenceHandling = () => {
                    this.popoverStatistics.recurrenceHandlings++;
                };
                
                // 处理初始化错误
                this.handleInitializationError = (error) => {
                    this.popoverStatistics.errorCount++;
                    console.error('Popover initialization error:', error);
                };
                
                // 处理状态变更错误
                this.handleStatusChangeError = (error, status) => {
                    this.popoverStatistics.errorCount++;
                    console.error(`Status change error for ${status}:`, error);
                    this.showErrorMessage('Failed to change status');
                };
                
                // 处理记录打开错误
                this.handleRecordOpenError = (error) => {
                    this.popoverStatistics.errorCount++;
                    console.error('Record open error:', error);
                    this.showErrorMessage('Failed to open record');
                };
                
                // 显示权限错误
                this.showPermissionError = () => {
                    this.showErrorMessage('You do not have permission to view this event');
                };
                
                // 显示错误消息
                this.showErrorMessage = (message) => {
                    const notification = useService("notification");
                    notification.add(message, { type: 'danger' });
                };
                
                // 获取弹出框信息
                this.getPopoverInfo = () => {
                    return {
                        openTime: this.enhancedState.openTime,
                        interactionCount: this.enhancedState.interactionCount,
                        lastAction: this.enhancedState.lastAction,
                        permissionLevel: this.enhancedState.permissionLevel,
                        isLoading: this.enhancedState.isLoading,
                        statistics: this.popoverStatistics
                    };
                };
            }
            
            // 重写原始方法
            async onWillStart() {
                return await this.enhancedOnWillStart();
            }
            
            async changeAttendeeStatus(selectedStatus) {
                return await this.enhancedChangeAttendeeStatus(selectedStatus);
            }
            
            async onClickOpenRecord() {
                return await this.enhancedOnClickOpenRecord();
            }
        };
    }
}
```

## 技术特点

### 1. 权限管理
- **角色识别**: 识别组织者、参与者、查看者角色
- **权限检查**: 检查编辑、删除、查看权限
- **隐私控制**: 控制私有事件的访问
- **功能限制**: 基于权限限制功能

### 2. 状态管理
- **状态获取**: 获取参与者状态选项
- **状态显示**: 显示状态下拉选择
- **状态变更**: 处理状态变更操作
- **颜色映射**: 映射状态到显示颜色

### 3. 重复事件处理
- **策略询问**: 询问重复更新策略
- **用户选择**: 处理用户选择
- **策略应用**: 应用选择的策略
- **数据一致性**: 保持数据一致性

### 4. 用户交互
- **弹出框显示**: 显示事件详情弹出框
- **操作按钮**: 提供各种操作按钮
- **状态反馈**: 提供操作状态反馈
- **错误处理**: 处理操作错误

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- **权限策略**: 不同角色的权限策略
- **状态策略**: 不同的状态处理策略
- **显示策略**: 不同的显示策略

### 2. 状态模式 (State Pattern)
- **参与者状态**: 不同的参与者状态
- **弹出框状态**: 不同的弹出框状态
- **加载状态**: 不同的加载状态

### 3. 命令模式 (Command Pattern)
- **状态变更命令**: 封装状态变更操作
- **打开命令**: 封装记录打开操作
- **归档命令**: 封装归档操作

### 4. 观察者模式 (Observer Pattern)
- **状态观察**: 观察状态变化
- **权限观察**: 观察权限变化
- **事件观察**: 观察用户交互事件

## 注意事项

1. **权限验证**: 确保权限检查的准确性
2. **状态同步**: 保持状态的同步更新
3. **用户体验**: 提供清晰的操作反馈
4. **错误处理**: 处理各种异常情况

## 扩展建议

1. **批量操作**: 支持批量状态变更
2. **快捷键**: 添加键盘快捷键支持
3. **自定义状态**: 支持自定义参与者状态
4. **通知集成**: 集成通知系统
5. **离线支持**: 支持离线状态变更

该参与者日历通用弹出框为Odoo Calendar模块提供了完整的事件详情显示和参与者交互功能，通过智能的权限管理和状态处理确保了用户的最佳体验。

# Attendee Calendar Common Components

## 概述

Attendee Calendar Common 模块提供了参与者日历系统的通用组件，这些组件可以在不同的日历视图中复用。该模块包含了通用的弹出框和渲染器组件，为参与者日历提供了一致的用户体验和功能。

## 组件结构

```
common/
├── attendee_calendar_common_popover.js     # 通用参与者弹出框
└── attendee_calendar_common_renderer.js   # 通用参与者渲染器
```

## 核心组件

### 1. 通用参与者弹出框 (Common Popover)

**文件**: `attendee_calendar_common_popover.js`

通用参与者弹出框是一个功能丰富的组件，为参与者提供了完整的事件交互界面。

#### 主要功能

- **状态管理**: 允许参与者更改自己的参与状态
- **权限控制**: 基于用户角色控制可用操作
- **重复事件处理**: 智能处理重复事件的状态更新
- **视频通话集成**: 集成视频通话功能
- **事件详情显示**: 显示完整的事件信息

#### 核心方法

```javascript
class AttendeeCalendarCommonPopover extends CalendarCommonPopover {
    // 更改参与者状态
    async changeAttendeeStatus(status) {
        // 检查权限
        if (!this.canChangeStatus()) return;
        
        // 处理重复事件
        if (this.props.record.rawRecord.recurrency) {
            const policy = await this.askRecurrenceUpdatePolicy();
            if (!policy) return;
        }
        
        // 执行状态更新
        await this.updateAttendeeStatus(status);
    }
    
    // 检查用户权限
    canChangeStatus() {
        const user = this.env.services.user;
        const event = this.props.record.rawRecord;
        
        // 组织者可以管理所有参与者
        if (event.user_id === user.userId) {
            return true;
        }
        
        // 参与者只能更改自己的状态
        return event.partner_ids.includes(user.partnerId);
    }
    
    // 处理视频通话
    openVideoCall() {
        const videocallLocation = this.props.record.rawRecord.videocall_location;
        if (videocallLocation) {
            window.open(videocallLocation, '_blank');
        }
    }
}
```

#### 状态切换流程

```mermaid
graph TD
    A[用户点击状态] --> B{检查权限}
    B -->|无权限| C[显示错误提示]
    B -->|有权限| D{是否重复事件}
    D -->|是| E[询问更新策略]
    D -->|否| F[直接更新状态]
    E --> G{用户选择策略}
    G -->|取消| H[终止操作]
    G -->|选择策略| F
    F --> I[调用服务器API]
    I --> J[更新本地状态]
    J --> K[刷新界面]
```

#### 权限矩阵

| 用户角色 | 查看事件 | 更改自己状态 | 更改他人状态 | 编辑事件 | 删除事件 |
|---------|---------|-------------|-------------|---------|---------|
| 组织者   | ✅      | ✅          | ✅          | ✅      | ✅      |
| 参与者   | ✅      | ✅          | ❌          | ❌      | ❌      |
| 查看者   | ✅*     | ❌          | ❌          | ❌      | ❌      |

*仅限公开事件

### 2. 通用参与者渲染器 (Common Renderer)

**文件**: `attendee_calendar_common_renderer.js`

通用参与者渲染器负责将参与者事件数据转换为可视化的日历元素。

#### 主要功能

- **事件转换**: 将记录数据转换为事件对象
- **样式计算**: 基于参与者状态计算样式
- **弹出框集成**: 集成参与者弹出框
- **选择控制**: 控制事件的选择行为
- **事件挂载**: 处理事件元素的挂载逻辑

#### 核心方法

```javascript
class AttendeeCalendarCommonRenderer extends CalendarCommonRenderer {
    // 转换记录为事件对象
    convertRecordToEvent(record) {
        let editable = false;
        if (record && record.rawRecord) {
            editable = record.rawRecord.user_can_edit;
        }
        
        return {
            ...super.convertRecordToEvent(record),
            id: record._recordId || record.id,
            editable: editable,
            attendeeStatus: record.attendeeStatus,
            isAlone: record.isAlone
        };
    }
    
    // 计算事件样式类名
    eventClassNames({ el, event }) {
        const classesToAdd = super.eventClassNames(...arguments);
        const record = this.props.model.records[event.id];
        
        if (record) {
            // 添加高亮样式
            if (record.rawRecord.is_highlighted) {
                classesToAdd.push("o_event_highlight");
            }
            
            // 添加参与者状态样式
            if (record.isAlone) {
                classesToAdd.push("o_attendee_status_alone");
            } else {
                classesToAdd.push(`o_attendee_status_${record.attendeeStatus}`);
            }
        }
        
        return classesToAdd;
    }
    
    // 事件挂载处理
    onEventDidMount({ el, event }) {
        super.onEventDidMount(...arguments);
        const record = this.props.model.records[event.id];
        
        if (record) {
            // 检查是否需要默认打开弹出框
            const defaultEventId = this.env.searchModel?.context?.default_calendar_event_id;
            if (defaultEventId === parseInt(event.id)) {
                this.openPopover(el, record);
            }
        }
    }
    
    // 控制选择权限
    isSelectionAllowed(event) {
        return true; // 允许选择所有事件
    }
}
```

#### 样式系统

```css
/* 参与者状态样式 */
.o_attendee_status_accepted {
    border-left: 4px solid #28a745;
    background-color: rgba(40, 167, 69, 0.1);
}

.o_attendee_status_declined {
    border-left: 4px solid #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
    opacity: 0.7;
    text-decoration: line-through;
}

.o_attendee_status_tentative {
    border-left: 4px solid #6c757d;
    background-color: rgba(108, 117, 125, 0.1);
    border-style: dashed;
}

.o_attendee_status_needsAction {
    border-left: 4px solid #ffc107;
    background-color: rgba(255, 193, 7, 0.1);
    animation: pulse 2s infinite;
}

.o_attendee_status_alone {
    border-left: 4px solid #17a2b8;
    background-color: rgba(23, 162, 184, 0.1);
}

.o_event_highlight {
    box-shadow: 0 0 10px rgba(0, 123, 255, 0.5);
    z-index: 1000;
}

/* 脉冲动画 */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}
```

## 组件交互

### 弹出框与渲染器的协作

```javascript
// 渲染器创建事件元素
const eventElement = this.createEventElement(event);

// 添加点击监听器
eventElement.addEventListener('click', (e) => {
    // 打开弹出框
    this.openPopover(e.target, record);
});

// 弹出框处理用户交互
popover.on('status-changed', (newStatus) => {
    // 更新事件样式
    this.updateEventStyles(eventElement, newStatus);
});
```

### 数据流

```mermaid
graph LR
    A[服务器数据] --> B[模型处理]
    B --> C[渲染器转换]
    C --> D[DOM元素]
    D --> E[用户交互]
    E --> F[弹出框]
    F --> G[状态更新]
    G --> H[服务器同步]
    H --> A
```

## 使用示例

### 1. 在自定义视图中使用通用组件

```javascript
import { AttendeeCalendarCommonRenderer } from '@calendar/views/attendee_calendar/common/attendee_calendar_common_renderer';
import { AttendeeCalendarCommonPopover } from '@calendar/views/attendee_calendar/common/attendee_calendar_common_popover';

class MyCustomCalendarRenderer extends AttendeeCalendarCommonRenderer {
    static components = {
        ...AttendeeCalendarCommonRenderer.components,
        Popover: AttendeeCalendarCommonPopover,
    };
    
    // 自定义事件渲染
    renderCustomEvent(event) {
        const element = super.renderEvent(event);
        // 添加自定义功能
        this.addCustomFeatures(element, event);
        return element;
    }
}
```

### 2. 扩展弹出框功能

```javascript
class ExtendedAttendeePopover extends AttendeeCalendarCommonPopover {
    setup() {
        super.setup();
        // 添加自定义功能
        this.addCustomActions();
    }
    
    addCustomActions() {
        // 添加自定义按钮和操作
        this.customActions = [
            {
                name: 'Export to Calendar',
                icon: 'fa-download',
                action: this.exportToCalendar.bind(this)
            },
            {
                name: 'Share Event',
                icon: 'fa-share',
                action: this.shareEvent.bind(this)
            }
        ];
    }
    
    async exportToCalendar() {
        // 导出到外部日历
        const icsData = await this.generateICS();
        this.downloadFile(icsData, 'event.ics');
    }
    
    async shareEvent() {
        // 分享事件
        const shareUrl = await this.generateShareUrl();
        navigator.clipboard.writeText(shareUrl);
    }
}
```

### 3. 自定义样式主题

```javascript
// 主题配置
const THEME_CONFIGS = {
    'default': {
        accepted: '#28a745',
        declined: '#dc3545',
        tentative: '#6c757d',
        needsAction: '#ffc107'
    },
    'dark': {
        accepted: '#20c997',
        declined: '#e74c3c',
        tentative: '#95a5a6',
        needsAction: '#f39c12'
    },
    'colorblind': {
        accepted: '#2ecc71',
        declined: '#e67e22',
        tentative: '#34495e',
        needsAction: '#f1c40f'
    }
};

// 应用主题
function applyTheme(themeName) {
    const theme = THEME_CONFIGS[themeName];
    const root = document.documentElement;
    
    Object.entries(theme).forEach(([status, color]) => {
        root.style.setProperty(`--attendee-${status}-color`, color);
    });
}
```

## 最佳实践

### 1. 性能优化

```javascript
// 使用防抖处理频繁的状态更新
const debouncedStatusUpdate = debounce(async (eventId, status) => {
    await this.updateAttendeeStatus(eventId, status);
}, 300);

// 批量处理多个事件的样式更新
const batchUpdateStyles = (events) => {
    requestAnimationFrame(() => {
        events.forEach(event => {
            this.updateEventStyles(event.element, event.status);
        });
    });
};
```

### 2. 错误处理

```javascript
async changeAttendeeStatus(status) {
    try {
        await this.updateAttendeeStatus(status);
        this.showSuccessMessage('Status updated successfully');
    } catch (error) {
        console.error('Failed to update status:', error);
        this.showErrorMessage('Failed to update status. Please try again.');
        // 回滚状态
        this.revertStatusChange();
    }
}
```

### 3. 可访问性

```javascript
// 添加ARIA标签
addAccessibilityAttributes(element, event) {
    element.setAttribute('role', 'button');
    element.setAttribute('aria-label', `Event: ${event.title}, Status: ${event.attendeeStatus}`);
    element.setAttribute('tabindex', '0');
    
    // 键盘导航支持
    element.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
            this.openPopover(element, event);
        }
    });
}
```

## 扩展指南

### 1. 添加新的参与者操作

```javascript
// 扩展弹出框操作
const CUSTOM_ACTIONS = {
    'delegate': {
        name: 'Delegate',
        icon: 'fa-user-plus',
        permission: 'can_delegate',
        handler: 'delegateEvent'
    },
    'request_info': {
        name: 'Request Info',
        icon: 'fa-question-circle',
        permission: 'can_request_info',
        handler: 'requestEventInfo'
    }
};
```

### 2. 自定义状态指示器

```javascript
// 自定义状态指示器
const STATUS_INDICATORS = {
    'accepted': {
        icon: 'fa-check-circle',
        color: '#28a745',
        animation: 'fadeIn'
    },
    'declined': {
        icon: 'fa-times-circle',
        color: '#dc3545',
        animation: 'slideOut'
    }
};
```

### 3. 集成外部服务

```javascript
// 集成外部日历服务
class ExternalCalendarIntegration {
    async syncToGoogleCalendar(event) {
        // 同步到Google日历
    }
    
    async syncToOutlook(event) {
        // 同步到Outlook
    }
    
    async syncToAppleCalendar(event) {
        // 同步到Apple日历
    }
}
```

## 依赖关系

- **@web/views/calendar/calendar_common**: 基础日历组件
- **@calendar/views/ask_recurrence_update_policy**: 重复事件策略
- **@web/core/utils/hooks**: 工具钩子

## 版本兼容性

- **Odoo 17.0+**: 完全支持
- **现代浏览器**: 支持ES6+特性
- **移动设备**: 响应式设计支持

---

*这些通用组件为参与者日历系统提供了坚实的基础，支持灵活的扩展和定制。*

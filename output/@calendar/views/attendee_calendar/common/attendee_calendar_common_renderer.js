/*******************************************************************************************************
*  Filepath: /calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_renderer.js  *
*  Lines: 75                                                                                           *
*******************************************************************************************************/
odoo.define('@calendar/views/attendee_calendar/common/attendee_calendar_common_renderer', ['@web/views/calendar/calendar_common/calendar_common_renderer', '@calendar/views/attendee_calendar/common/attendee_calendar_common_popover'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { CalendarCommonRenderer } = require("@web/views/calendar/calendar_common/calendar_common_renderer");
const { AttendeeCalendarCommonPopover } = require("@calendar/views/attendee_calendar/common/attendee_calendar_common_popover");

const AttendeeCalendarCommonRenderer = __exports.AttendeeCalendarCommonRenderer = class AttendeeCalendarCommonRenderer extends CalendarCommonRenderer {
    static eventTemplate = "calendar.AttendeeCalendarCommonRenderer.event";
    static components = {
        ...CalendarCommonRenderer.components,
        Popover: AttendeeCalendarCommonPopover,
    };
    /**
     * @override
     *
     * Give a new key to our fc records to be able to iterate through in templates
     */
    convertRecordToEvent(record) {
        let editable = false;
        if (record && record.rawRecord) {
            editable = record.rawRecord.user_can_edit;
        }
        return {
            ...super.convertRecordToEvent(record),
            id: record._recordId || record.id,
            editable: editable,
        };
    }

    /**
     * @override
     */
    eventClassNames({ el, event }) {
        const classesToAdd = super.eventClassNames(...arguments);
        const record = this.props.model.records[event.id];
        if (record) {
            if (record.rawRecord.is_highlighted) {
                classesToAdd.push("o_event_highlight");
            }
            if (record.isAlone) {
                classesToAdd.push("o_attendee_status_alone");
            } else {
                classesToAdd.push(`o_attendee_status_${record.attendeeStatus}`);
            }
        }
        return classesToAdd;
    }

    /**
     * @override
     */
    onEventDidMount({ el, event }) {
        super.onEventDidMount(...arguments);
        const record = this.props.model.records[event.id];
        if (record) {
            if (this.env.searchModel?.context?.default_calendar_event_id === parseInt(event.id)) {
                this.openPopover(el, record);
            }
        }
    }

    /**
     * @override
     *
     * Allow slots to be selected over multiple days
     */
    isSelectionAllowed(event) {
        return true;
    }
}

return __exports;
});
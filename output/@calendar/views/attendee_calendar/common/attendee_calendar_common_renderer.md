# AttendeeCalendarCommonRenderer - 参与者日历通用渲染器

## 概述

`attendee_calendar_common_renderer.js` 是 Odoo Calendar 模块的参与者日历通用渲染器，负责渲染参与者视角的日历事件。该模块包含75行代码，是一个功能专门的渲染器组件，专门用于处理参与者日历事件的视觉渲染，具备事件转换、样式管理、状态显示、弹出框集成等特性，是参与者日历视图的核心渲染组件。

## 文件信息
- **路径**: `/calendar/static/src/views/attendee_calendar/common/attendee_calendar_common_renderer.js`
- **行数**: 75
- **模块**: `@calendar/views/attendee_calendar/common/attendee_calendar_common_renderer`

## 依赖关系

```javascript
// 核心依赖
'@web/views/calendar/calendar_common/calendar_common_renderer' // 日历通用渲染器基类
'@calendar/views/attendee_calendar/common/attendee_calendar_common_popover' // 参与者弹出框
```

## 核心功能

### 1. 渲染器定义

```javascript
class AttendeeCalendarCommonRenderer extends CalendarCommonRenderer {
    static eventTemplate = "calendar.AttendeeCalendarCommonRenderer.event";
    static components = {
        ...CalendarCommonRenderer.components,
        Popover: AttendeeCalendarCommonPopover,
    };
}
```

**渲染器特性**:
- **继承扩展**: 继承日历通用渲染器
- **模板定制**: 使用专门的事件模板
- **弹出框集成**: 集成参与者弹出框组件
- **组件替换**: 替换弹出框组件

### 2. 事件转换

```javascript
convertRecordToEvent(record) {
    let editable = false;
    if (record && record.rawRecord) {
        editable = record.rawRecord.user_can_edit;
    }
    return {
        ...super.convertRecordToEvent(record),
        id: record._recordId || record.id,
        editable: editable,
    };
}
```

**转换功能**:
- **父类调用**: 调用父类转换方法
- **ID处理**: 处理记录ID或内部ID
- **编辑权限**: 设置事件编辑权限
- **属性扩展**: 扩展事件属性

### 3. 样式类名

```javascript
eventClassNames({ el, event }) {
    const classesToAdd = super.eventClassNames(...arguments);
    const record = this.props.model.records[event.id];
    if (record) {
        if (record.rawRecord.is_highlighted) {
            classesToAdd.push("o_event_highlight");
        }
        if (record.isAlone) {
            classesToAdd.push("o_attendee_status_alone");
        } else {
            classesToAdd.push(`o_attendee_status_${record.attendeeStatus}`);
        }
    }
    return classesToAdd;
}
```

**样式功能**:
- **基础样式**: 继承父类样式类名
- **高亮显示**: 添加高亮事件样式
- **独立状态**: 添加独立参与者样式
- **状态样式**: 添加参与者状态样式

### 4. 事件挂载

```javascript
onEventDidMount({ el, event }) {
    super.onEventDidMount(...arguments);
    const record = this.props.model.records[event.id];
    if (record) {
        if (this.env.searchModel?.context?.default_calendar_event_id === parseInt(event.id)) {
            this.openPopover(el, record);
        }
    }
}
```

**挂载功能**:
- **父类调用**: 调用父类挂载方法
- **记录获取**: 获取对应的记录
- **默认打开**: 检查是否需要默认打开弹出框
- **弹出框显示**: 自动显示弹出框

### 5. 选择控制

```javascript
isSelectionAllowed(event) {
    return true;
}
```

**选择功能**:
- **选择允许**: 允许所有事件选择
- **多天支持**: 支持跨多天选择
- **灵活选择**: 提供灵活的选择机制
- **权限开放**: 开放选择权限

## 使用场景

### 1. 参与者渲染器管理器

```javascript
// 参与者渲染器管理器
class AttendeeRendererManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置渲染器配置
        this.rendererConfig = {
            enableStatusStyling: true,
            enableHighlighting: true,
            enableEditableControl: true,
            enablePopoverIntegration: true,
            enableMultiDaySelection: true,
            enableCustomTemplates: true,
            defaultEventTemplate: 'calendar.AttendeeCalendarCommonRenderer.event'
        };
        
        // 设置状态样式
        this.statusStyles = new Map([
            ['needsAction', {
                name: 'Needs Action',
                className: 'o_attendee_status_needsAction',
                color: '#ffc107',
                borderColor: '#e0a800',
                textColor: '#212529'
            }],
            ['accepted', {
                name: 'Accepted',
                className: 'o_attendee_status_accepted',
                color: '#28a745',
                borderColor: '#1e7e34',
                textColor: '#ffffff'
            }],
            ['declined', {
                name: 'Declined',
                className: 'o_attendee_status_declined',
                color: '#dc3545',
                borderColor: '#bd2130',
                textColor: '#ffffff'
            }],
            ['tentative', {
                name: 'Tentative',
                className: 'o_attendee_status_tentative',
                color: '#6c757d',
                borderColor: '#545b62',
                textColor: '#ffffff'
            }],
            ['alone', {
                name: 'Alone',
                className: 'o_attendee_status_alone',
                color: '#17a2b8',
                borderColor: '#117a8b',
                textColor: '#ffffff'
            }]
        ]);
        
        // 设置事件类型
        this.eventTypes = new Map([
            ['meeting', {
                name: 'Meeting',
                template: 'calendar.AttendeeCalendarCommonRenderer.meeting',
                icon: 'fa-users',
                defaultDuration: 1.0
            }],
            ['call', {
                name: 'Phone Call',
                template: 'calendar.AttendeeCalendarCommonRenderer.call',
                icon: 'fa-phone',
                defaultDuration: 0.5
            }],
            ['appointment', {
                name: 'Appointment',
                template: 'calendar.AttendeeCalendarCommonRenderer.appointment',
                icon: 'fa-calendar-check',
                defaultDuration: 1.0
            }],
            ['task', {
                name: 'Task',
                template: 'calendar.AttendeeCalendarCommonRenderer.task',
                icon: 'fa-tasks',
                defaultDuration: 2.0
            }]
        ]);
        
        // 设置渲染统计
        this.rendererStatistics = {
            totalRenders: 0,
            eventConversions: 0,
            styleApplications: 0,
            popoverOpens: 0,
            selectionEvents: 0,
            mountEvents: 0,
            errorCount: 0,
            averageRenderTime: 0,
            totalRenderTime: 0
        };
        
        this.initializeRendererSystem();
    }
    
    // 初始化渲染器系统
    initializeRendererSystem() {
        // 创建增强的渲染器
        this.createEnhancedRenderer();
        
        // 设置样式系统
        this.setupStyleSystem();
        
        // 设置模板系统
        this.setupTemplateSystem();
        
        // 设置事件系统
        this.setupEventSystem();
    }
    
    // 创建增强的渲染器
    createEnhancedRenderer() {
        const originalRenderer = AttendeeCalendarCommonRenderer;
        
        this.EnhancedAttendeeCalendarCommonRenderer = class extends originalRenderer {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加性能监控
                this.addPerformanceMonitoring();
                
                // 添加缓存功能
                this.addCacheFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    renderCount: 0,
                    lastRenderTime: null,
                    eventCache: new Map(),
                    styleCache: new Map(),
                    templateCache: new Map()
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的事件转换
                this.enhancedConvertRecordToEvent = (record) => {
                    try {
                        // 记录转换开始
                        this.recordEventConversion();
                        
                        // 检查缓存
                        const cacheKey = this.getEventCacheKey(record);
                        if (this.enhancedState.eventCache.has(cacheKey)) {
                            return this.enhancedState.eventCache.get(cacheKey);
                        }
                        
                        // 执行转换
                        const event = this.executeEventConversion(record);
                        
                        // 缓存结果
                        this.enhancedState.eventCache.set(cacheKey, event);
                        
                        return event;
                        
                    } catch (error) {
                        this.handleConversionError(error, record);
                        return super.convertRecordToEvent(record);
                    }
                };
                
                // 执行事件转换
                this.executeEventConversion = (record) => {
                    // 获取编辑权限
                    let editable = false;
                    if (record && record.rawRecord) {
                        editable = record.rawRecord.user_can_edit;
                    }
                    
                    // 获取事件类型
                    const eventType = this.getEventType(record);
                    
                    // 构建事件对象
                    const event = {
                        ...super.convertRecordToEvent(record),
                        id: record._recordId || record.id,
                        editable: editable,
                        eventType: eventType,
                        attendeeStatus: record.attendeeStatus,
                        isAlone: record.isAlone,
                        isHighlighted: record.rawRecord?.is_highlighted || false
                    };
                    
                    return event;
                };
                
                // 增强的样式类名
                this.enhancedEventClassNames = ({ el, event }) => {
                    try {
                        // 记录样式应用
                        this.recordStyleApplication();
                        
                        // 检查缓存
                        const cacheKey = this.getStyleCacheKey(event);
                        if (this.enhancedState.styleCache.has(cacheKey)) {
                            return this.enhancedState.styleCache.get(cacheKey);
                        }
                        
                        // 执行样式计算
                        const classes = this.calculateEventClasses(el, event);
                        
                        // 缓存结果
                        this.enhancedState.styleCache.set(cacheKey, classes);
                        
                        return classes;
                        
                    } catch (error) {
                        this.handleStyleError(error, event);
                        return super.eventClassNames({ el, event });
                    }
                };
                
                // 计算事件样式类
                this.calculateEventClasses = (el, event) => {
                    const classesToAdd = super.eventClassNames({ el, event });
                    const record = this.props.model.records[event.id];
                    
                    if (record) {
                        // 添加高亮样式
                        if (record.rawRecord.is_highlighted) {
                            classesToAdd.push("o_event_highlight");
                        }
                        
                        // 添加状态样式
                        if (record.isAlone) {
                            classesToAdd.push("o_attendee_status_alone");
                        } else {
                            classesToAdd.push(`o_attendee_status_${record.attendeeStatus}`);
                        }
                        
                        // 添加事件类型样式
                        const eventType = this.getEventType(record);
                        if (eventType) {
                            classesToAdd.push(`o_event_type_${eventType}`);
                        }
                        
                        // 添加权限样式
                        if (record.rawRecord.user_can_edit) {
                            classesToAdd.push("o_event_editable");
                        } else {
                            classesToAdd.push("o_event_readonly");
                        }
                    }
                    
                    return classesToAdd;
                };
                
                // 增强的事件挂载
                this.enhancedOnEventDidMount = ({ el, event }) => {
                    try {
                        // 记录挂载事件
                        this.recordMountEvent();
                        
                        // 执行父类挂载
                        super.onEventDidMount({ el, event });
                        
                        // 获取记录
                        const record = this.props.model.records[event.id];
                        if (!record) {
                            return;
                        }
                        
                        // 检查默认打开
                        this.checkDefaultOpen(el, event, record);
                        
                        // 添加事件监听
                        this.addEventListeners(el, event, record);
                        
                        // 应用自定义样式
                        this.applyCustomStyles(el, event, record);
                        
                    } catch (error) {
                        this.handleMountError(error, event);
                    }
                };
                
                // 检查默认打开
                this.checkDefaultOpen = (el, event, record) => {
                    const defaultEventId = this.env.searchModel?.context?.default_calendar_event_id;
                    if (defaultEventId === parseInt(event.id)) {
                        this.openPopover(el, record);
                        this.recordPopoverOpen();
                    }
                };
                
                // 添加事件监听
                this.addEventListeners = (el, event, record) => {
                    // 添加双击监听
                    el.addEventListener('dblclick', () => {
                        this.onEventDoubleClick(event, record);
                    });
                    
                    // 添加右键监听
                    el.addEventListener('contextmenu', (e) => {
                        e.preventDefault();
                        this.onEventRightClick(e, event, record);
                    });
                };
                
                // 应用自定义样式
                this.applyCustomStyles = (el, event, record) => {
                    const statusStyle = this.statusStyles.get(record.attendeeStatus);
                    if (statusStyle) {
                        el.style.setProperty('--event-color', statusStyle.color);
                        el.style.setProperty('--event-border-color', statusStyle.borderColor);
                        el.style.setProperty('--event-text-color', statusStyle.textColor);
                    }
                };
                
                // 事件双击处理
                this.onEventDoubleClick = (event, record) => {
                    if (record.rawRecord.user_can_edit) {
                        this.openEventEditor(record);
                    } else {
                        this.openEventViewer(record);
                    }
                };
                
                // 事件右键处理
                this.onEventRightClick = (e, event, record) => {
                    this.showContextMenu(e, event, record);
                };
                
                // 增强的选择控制
                this.enhancedIsSelectionAllowed = (event) => {
                    try {
                        // 记录选择事件
                        this.recordSelectionEvent();
                        
                        // 检查选择权限
                        if (!this.checkSelectionPermission(event)) {
                            return false;
                        }
                        
                        // 检查事件状态
                        if (!this.checkEventSelectable(event)) {
                            return false;
                        }
                        
                        return true;
                        
                    } catch (error) {
                        this.handleSelectionError(error, event);
                        return super.isSelectionAllowed(event);
                    }
                };
                
                // 检查选择权限
                this.checkSelectionPermission = (event) => {
                    // 基本权限检查
                    return true;
                };
                
                // 检查事件可选择性
                this.checkEventSelectable = (event) => {
                    const record = this.props.model.records[event.id];
                    if (record) {
                        // 检查事件状态
                        if (record.attendeeStatus === 'declined') {
                            return false;
                        }
                    }
                    return true;
                };
                
                // 获取事件类型
                this.getEventType = (record) => {
                    if (record.rawRecord.activity_type_id) {
                        const typeName = record.rawRecord.activity_type_id[1]?.toLowerCase();
                        if (typeName?.includes('meeting')) return 'meeting';
                        if (typeName?.includes('call')) return 'call';
                        if (typeName?.includes('appointment')) return 'appointment';
                        if (typeName?.includes('task')) return 'task';
                    }
                    return 'meeting'; // 默认类型
                };
                
                // 获取事件缓存键
                this.getEventCacheKey = (record) => {
                    return `${record.id}_${record._recordId}_${record.attendeeStatus}`;
                };
                
                // 获取样式缓存键
                this.getStyleCacheKey = (event) => {
                    const record = this.props.model.records[event.id];
                    return `${event.id}_${record?.attendeeStatus}_${record?.isAlone}_${record?.rawRecord?.is_highlighted}`;
                };
                
                // 记录事件转换
                this.recordEventConversion = () => {
                    this.rendererStatistics.eventConversions++;
                };
                
                // 记录样式应用
                this.recordStyleApplication = () => {
                    this.rendererStatistics.styleApplications++;
                };
                
                // 记录挂载事件
                this.recordMountEvent = () => {
                    this.rendererStatistics.mountEvents++;
                };
                
                // 记录弹出框打开
                this.recordPopoverOpen = () => {
                    this.rendererStatistics.popoverOpens++;
                };
                
                // 记录选择事件
                this.recordSelectionEvent = () => {
                    this.rendererStatistics.selectionEvents++;
                };
                
                // 处理转换错误
                this.handleConversionError = (error, record) => {
                    this.rendererStatistics.errorCount++;
                    console.error('Event conversion error:', error, record);
                };
                
                // 处理样式错误
                this.handleStyleError = (error, event) => {
                    this.rendererStatistics.errorCount++;
                    console.error('Style calculation error:', error, event);
                };
                
                // 处理挂载错误
                this.handleMountError = (error, event) => {
                    this.rendererStatistics.errorCount++;
                    console.error('Event mount error:', error, event);
                };
                
                // 处理选择错误
                this.handleSelectionError = (error, event) => {
                    this.rendererStatistics.errorCount++;
                    console.error('Selection error:', error, event);
                };
                
                // 获取渲染器信息
                this.getRendererInfo = () => {
                    return {
                        renderCount: this.enhancedState.renderCount,
                        lastRenderTime: this.enhancedState.lastRenderTime,
                        cacheSize: {
                            events: this.enhancedState.eventCache.size,
                            styles: this.enhancedState.styleCache.size,
                            templates: this.enhancedState.templateCache.size
                        },
                        statistics: this.rendererStatistics,
                        config: this.rendererConfig
                    };
                };
                
                // 清理缓存
                this.clearCache = () => {
                    this.enhancedState.eventCache.clear();
                    this.enhancedState.styleCache.clear();
                    this.enhancedState.templateCache.clear();
                };
            }
            
            // 重写原始方法
            convertRecordToEvent(record) {
                return this.enhancedConvertRecordToEvent(record);
            }
            
            eventClassNames({ el, event }) {
                return this.enhancedEventClassNames({ el, event });
            }
            
            onEventDidMount({ el, event }) {
                return this.enhancedOnEventDidMount({ el, event });
            }
            
            isSelectionAllowed(event) {
                return this.enhancedIsSelectionAllowed(event);
            }
        };
    }
}
```

## 技术特点

### 1. 事件渲染
- **记录转换**: 将记录转换为事件对象
- **权限控制**: 控制事件编辑权限
- **ID处理**: 处理内部记录ID
- **属性扩展**: 扩展事件属性

### 2. 样式管理
- **状态样式**: 基于参与者状态的样式
- **高亮显示**: 支持事件高亮显示
- **独立状态**: 支持独立参与者样式
- **动态样式**: 动态计算样式类名

### 3. 弹出框集成
- **组件替换**: 替换为参与者弹出框
- **自动打开**: 支持自动打开弹出框
- **上下文感知**: 基于上下文自动操作
- **用户交互**: 增强用户交互体验

### 4. 选择控制
- **灵活选择**: 允许灵活的事件选择
- **多天支持**: 支持跨多天选择
- **权限检查**: 检查选择权限
- **状态验证**: 验证选择状态

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 装饰基础渲染功能
- **样式装饰**: 装饰事件样式
- **行为装饰**: 装饰事件行为

### 2. 策略模式 (Strategy Pattern)
- **渲染策略**: 不同的事件渲染策略
- **样式策略**: 不同的样式计算策略
- **选择策略**: 不同的选择控制策略

### 3. 模板方法模式 (Template Method Pattern)
- **渲染模板**: 定义事件渲染模板
- **样式模板**: 定义样式计算模板
- **挂载模板**: 定义事件挂载模板

### 4. 观察者模式 (Observer Pattern)
- **事件观察**: 观察事件变化
- **状态观察**: 观察状态变化
- **样式观察**: 观察样式变化

## 注意事项

1. **性能优化**: 注意渲染性能的优化
2. **样式一致性**: 确保样式的一致性
3. **缓存管理**: 管理缓存的生命周期
4. **错误处理**: 处理渲染错误

## 扩展建议

1. **主题支持**: 支持主题切换
2. **动画效果**: 添加渲染动画
3. **自定义模板**: 支持自定义事件模板
4. **性能监控**: 添加性能监控
5. **批量渲染**: 支持批量事件渲染

该参与者日历通用渲染器为Odoo Calendar模块提供了专门的参与者视角事件渲染功能，通过智能的样式管理和弹出框集成确保了参与者日历的最佳视觉体验。

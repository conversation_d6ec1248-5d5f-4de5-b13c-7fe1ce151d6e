# AttendeeCalendarModel - 参与者日历模型

## 概述

`attendee_calendar_model.js` 是 Odoo Calendar 模块的参与者日历模型，负责管理参与者视角的日历数据模型。该模块包含217行代码，是一个功能完整的数据模型，专门用于处理参与者日历的数据逻辑，具备参与者数据处理、重复事件管理、过滤器集成、状态同步等特性，是参与者日历视图的核心数据层。

## 文件信息
- **路径**: `/calendar/static/src/views/attendee_calendar/attendee_calendar_model.js`
- **行数**: 217
- **模块**: `@calendar/views/attendee_calendar/attendee_calendar_model`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'                   // 翻译服务
'@web/core/network/rpc'                        // RPC网络服务
'@web/core/user'                               // 用户服务
'@web/views/calendar/calendar_model'           // 日历模型基类
'@calendar/views/ask_recurrence_update_policy_hook' // 重复更新策略钩子
'@web/core/confirmation_dialog/confirmation_dialog' // 确认对话框
```

## 核心功能

### 1. 模型定义

```javascript
class AttendeeCalendarModel extends CalendarModel {
    setup(params, { dialog }) {
        super.setup(...arguments);
        this.dialog = dialog;
        this.rpc = rpc;
    }
}
```

**模型特性**:
- **继承扩展**: 继承标准日历模型
- **对话框集成**: 集成对话框服务
- **RPC集成**: 集成RPC网络服务
- **服务依赖**: 依赖对话框和ORM服务

### 2. 数据加载

```javascript
async load() {
    const res = await super.load(...arguments);
    const [credentialStatus, syncStatus, defaultDuration] = await Promise.all([
        rpc("/calendar/check_credentials"),
        this.orm.call("res.users", "check_synchronization_status", [[user.userId]]),
        this.orm.call("calendar.event", "get_default_duration"),
    ]);
    this.syncStatus = syncStatus;
    this.credentialStatus = credentialStatus;
    this.defaultDuration = defaultDuration;
    return res;
}
```

**加载功能**:
- **并行加载**: 并行加载多个状态信息
- **凭证检查**: 检查日历凭证状态
- **同步状态**: 检查用户同步状态
- **默认时长**: 获取默认事件时长
- **状态存储**: 存储各种状态信息

### 3. 参与者数据处理

```javascript
async updateAttendeeData(data) {
    const attendeeFilters = data.filterSections.partner_ids;
    let isEveryoneFilterActive = false;
    let attendeeIds = [];
    const eventIds = Object.keys(data.records).map((id) => Number.parseInt(id));
    
    if (attendeeFilters) {
        const allFilter = attendeeFilters.filters.find((filter) => filter.type === "all");
        isEveryoneFilterActive = (allFilter && allFilter.active) || false;
        attendeeIds = attendeeFilters.filters
            .filter((filter) => filter.type !== "all" && filter.value)
            .map((filter) => filter.value);
    }
    
    data.attendees = await this.orm.call("res.partner", "get_attendee_detail", [
        attendeeIds,
        eventIds,
    ]);
}
```

**参与者处理功能**:
- **过滤器解析**: 解析参与者过滤器
- **状态检查**: 检查"所有人"过滤器状态
- **ID提取**: 提取活跃参与者ID
- **详情获取**: 获取参与者详细信息
- **数据关联**: 关联事件和参与者数据

### 4. 记录复制

```javascript
// Duplicate records per attendee
const newRecords = {};
let duplicatedRecordIdx = -1;
for (const event of Object.values(data.records)) {
    const eventData = event.rawRecord;
    const attendees = eventData.partner_ids && eventData.partner_ids.length
        ? eventData.partner_ids
        : [eventData.partner_id[0]];
    
    for (const attendee of attendees) {
        if (!activeAttendeeIds.has(attendee)) {
            continue;
        }
        const record = { ...event };
        record.attendeeId = attendee;
        record.colorIndex = attendee;
        // 设置参与者信息
        const recordId = duplicatedRecords ? duplicatedRecordIdx-- : record.id;
        record._recordId = recordId;
        newRecords[recordId] = record;
    }
}
```

**复制功能**:
- **事件复制**: 为每个参与者复制事件记录
- **参与者关联**: 关联参与者ID和状态
- **颜色映射**: 映射参与者颜色
- **唯一标识**: 生成唯一记录ID
- **状态设置**: 设置参与者状态信息

### 5. 重复事件处理

```javascript
async updateRecord(record) {
    const rec = this.records[record.id];
    if (rec.rawRecord.recurrency) {
        const recurrenceUpdate = await askRecurrenceUpdatePolicy(this.dialog);
        if (!recurrenceUpdate) {
            return this.notify();
        }
        record.recurrenceUpdate = recurrenceUpdate;
    }
    return await super.updateRecord(...arguments);
}
```

**重复处理功能**:
- **重复检查**: 检查记录是否为重复事件
- **策略询问**: 询问重复更新策略
- **用户选择**: 处理用户选择结果
- **策略应用**: 应用选择的更新策略
- **记录更新**: 执行实际的记录更新

## 使用场景

### 1. 参与者日历模型管理器

```javascript
// 参与者日历模型管理器
class AttendeeCalendarModelManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置模型配置
        this.modelConfig = {
            enableAttendeeFiltering: true,
            enableStatusTracking: true,
            enableRecurrenceHandling: true,
            enableSyncStatusCheck: true,
            enableCredentialCheck: true,
            defaultEventDuration: 1.0,
            maxAttendeesPerEvent: 100
        };
        
        // 设置参与者状态
        this.attendeeStatuses = new Map([
            ['needsAction', {
                name: 'Needs Action',
                color: 'warning',
                icon: 'fa-question-circle',
                canRespond: true
            }],
            ['accepted', {
                name: 'Accepted',
                color: 'success',
                icon: 'fa-check-circle',
                canRespond: true
            }],
            ['declined', {
                name: 'Declined',
                color: 'danger',
                icon: 'fa-times-circle',
                canRespond: true
            }],
            ['tentative', {
                name: 'Tentative',
                color: 'info',
                icon: 'fa-clock',
                canRespond: true
            }]
        ]);
        
        // 设置过滤器类型
        this.filterTypes = new Map([
            ['all', {
                name: 'Everyone',
                type: 'all',
                showIndividualAttendees: false,
                duplicateEvents: false
            }],
            ['user', {
                name: 'User',
                type: 'user',
                showIndividualAttendees: true,
                duplicateEvents: true
            }],
            ['partner', {
                name: 'Partner',
                type: 'partner',
                showIndividualAttendees: true,
                duplicateEvents: true
            }]
        ]);
        
        // 设置重复策略
        this.recurrenceUpdatePolicies = new Map([
            ['self_only', {
                name: 'This event only',
                description: 'Update only this occurrence',
                scope: 'single'
            }],
            ['future_events', {
                name: 'This and future events',
                description: 'Update this and all future occurrences',
                scope: 'future'
            }],
            ['all_events', {
                name: 'All events',
                description: 'Update all occurrences in the series',
                scope: 'all'
            }]
        ]);
        
        // 设置模型统计
        this.modelStatistics = {
            totalLoads: 0,
            attendeeDataUpdates: 0,
            recordDuplications: 0,
            recurrenceHandlings: 0,
            filterUpdates: 0,
            archiveOperations: 0,
            syncStatusChecks: 0,
            credentialChecks: 0
        };
        
        this.initializeModelSystem();
    }
    
    // 初始化模型系统
    initializeModelSystem() {
        // 创建增强的模型
        this.createEnhancedModel();
        
        // 设置数据处理系统
        this.setupDataProcessingSystem();
        
        // 设置过滤系统
        this.setupFilterSystem();
        
        // 设置同步系统
        this.setupSyncSystem();
    }
    
    // 创建增强的模型
    createEnhancedModel() {
        const originalModel = AttendeeCalendarModel;
        
        this.EnhancedAttendeeCalendarModel = class extends originalModel {
            setup(params, services) {
                super.setup(params, services);
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加缓存功能
                this.addCacheFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    lastLoadTime: null,
                    attendeeCache: new Map(),
                    filterCache: new Map(),
                    syncStatusCache: null,
                    credentialStatusCache: null,
                    loadCount: 0
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的加载方法
                this.enhancedLoad = async () => {
                    try {
                        // 记录加载开始
                        this.recordLoadStart();
                        
                        // 执行基础加载
                        const res = await super.load();
                        
                        // 并行加载状态信息
                        await this.loadStatusInformation();
                        
                        // 记录加载完成
                        this.recordLoadComplete();
                        
                        return res;
                        
                    } catch (error) {
                        this.handleLoadError(error);
                        throw error;
                    }
                };
                
                // 加载状态信息
                this.loadStatusInformation = async () => {
                    const promises = [];
                    
                    // 检查凭证状态
                    if (this.modelConfig.enableCredentialCheck) {
                        promises.push(this.checkCredentialStatus());
                    }
                    
                    // 检查同步状态
                    if (this.modelConfig.enableSyncStatusCheck) {
                        promises.push(this.checkSyncStatus());
                    }
                    
                    // 获取默认时长
                    promises.push(this.getDefaultDuration());
                    
                    const results = await Promise.all(promises);
                    this.processStatusResults(results);
                };
                
                // 检查凭证状态
                this.checkCredentialStatus = async () => {
                    if (this.enhancedState.credentialStatusCache) {
                        return this.enhancedState.credentialStatusCache;
                    }
                    
                    const status = await rpc("/calendar/check_credentials");
                    this.enhancedState.credentialStatusCache = status;
                    this.modelStatistics.credentialChecks++;
                    
                    return status;
                };
                
                // 检查同步状态
                this.checkSyncStatus = async () => {
                    if (this.enhancedState.syncStatusCache) {
                        return this.enhancedState.syncStatusCache;
                    }
                    
                    const status = await this.orm.call("res.users", "check_synchronization_status", [[user.userId]]);
                    this.enhancedState.syncStatusCache = status;
                    this.modelStatistics.syncStatusChecks++;
                    
                    return status;
                };
                
                // 获取默认时长
                this.getDefaultDuration = async () => {
                    const duration = await this.orm.call("calendar.event", "get_default_duration");
                    return duration || this.modelConfig.defaultEventDuration;
                };
                
                // 处理状态结果
                this.processStatusResults = (results) => {
                    if (results.length >= 1) this.credentialStatus = results[0];
                    if (results.length >= 2) this.syncStatus = results[1];
                    if (results.length >= 3) this.defaultDuration = results[2];
                };
                
                // 增强的参与者数据更新
                this.enhancedUpdateAttendeeData = async (data) => {
                    try {
                        // 记录更新开始
                        this.recordAttendeeDataUpdate();
                        
                        // 解析过滤器
                        const filterInfo = this.parseAttendeeFilters(data);
                        
                        // 获取参与者详情
                        const attendeeDetails = await this.getAttendeeDetails(filterInfo);
                        
                        // 处理事件记录
                        this.processEventRecords(data, filterInfo, attendeeDetails);
                        
                    } catch (error) {
                        this.handleAttendeeDataError(error);
                    }
                };
                
                // 解析参与者过滤器
                this.parseAttendeeFilters = (data) => {
                    const attendeeFilters = data.filterSections.partner_ids;
                    
                    if (!attendeeFilters) {
                        return {
                            isEveryoneFilterActive: false,
                            attendeeIds: [],
                            activeAttendeeIds: new Set()
                        };
                    }
                    
                    const allFilter = attendeeFilters.filters.find(filter => filter.type === "all");
                    const isEveryoneFilterActive = (allFilter && allFilter.active) || false;
                    
                    const attendeeIds = attendeeFilters.filters
                        .filter(filter => filter.type !== "all" && filter.value)
                        .map(filter => filter.value);
                    
                    const activeAttendeeIds = new Set(
                        attendeeFilters.filters
                            .filter(filter => filter.type !== "all" && filter.value && filter.active)
                            .map(filter => filter.value)
                    );
                    
                    return {
                        isEveryoneFilterActive,
                        attendeeIds,
                        activeAttendeeIds,
                        filters: attendeeFilters.filters
                    };
                };
                
                // 获取参与者详情
                this.getAttendeeDetails = async (filterInfo) => {
                    const eventIds = Object.keys(this.data.records).map(id => Number.parseInt(id));
                    
                    // 检查缓存
                    const cacheKey = this.getAttendeeCacheKey(filterInfo.attendeeIds, eventIds);
                    if (this.enhancedState.attendeeCache.has(cacheKey)) {
                        return this.enhancedState.attendeeCache.get(cacheKey);
                    }
                    
                    // 获取详情
                    const attendeeDetails = await this.orm.call("res.partner", "get_attendee_detail", [
                        filterInfo.attendeeIds,
                        eventIds,
                    ]);
                    
                    // 缓存结果
                    this.enhancedState.attendeeCache.set(cacheKey, attendeeDetails);
                    
                    return attendeeDetails;
                };
                
                // 处理事件记录
                this.processEventRecords = (data, filterInfo, attendeeDetails) => {
                    data.attendees = attendeeDetails;
                    
                    if (!filterInfo.isEveryoneFilterActive) {
                        this.duplicateRecordsPerAttendee(data, filterInfo, attendeeDetails);
                    } else {
                        this.processEveryoneFilter(data, attendeeDetails);
                    }
                };
                
                // 为每个参与者复制记录
                this.duplicateRecordsPerAttendee = (data, filterInfo, attendeeDetails) => {
                    const newRecords = {};
                    let duplicatedRecordIdx = -1;
                    
                    for (const event of Object.values(data.records)) {
                        const attendees = this.getEventAttendees(event);
                        let duplicatedRecords = 0;
                        
                        for (const attendee of attendees) {
                            if (!filterInfo.activeAttendeeIds.has(attendee)) {
                                continue;
                            }
                            
                            const record = this.createAttendeeRecord(event, attendee, attendeeDetails);
                            const recordId = duplicatedRecords ? duplicatedRecordIdx-- : record.id;
                            
                            record._recordId = recordId;
                            newRecords[recordId] = record;
                            duplicatedRecords++;
                        }
                    }
                    
                    data.records = newRecords;
                    this.modelStatistics.recordDuplications++;
                };
                
                // 创建参与者记录
                this.createAttendeeRecord = (event, attendeeId, attendeeDetails) => {
                    const record = { ...event };
                    const attendeeInfo = attendeeDetails.find(
                        a => a.id === attendeeId && a.event_id === event.id
                    );
                    
                    record.attendeeId = attendeeId;
                    record.colorIndex = attendeeId;
                    
                    if (attendeeInfo) {
                        record.attendeeStatus = attendeeInfo.status;
                        record.isAlone = attendeeInfo.is_alone;
                        record.isCurrentPartner = attendeeInfo.id === user.partnerId;
                        record.calendarAttendeeId = attendeeInfo.attendee_id;
                    }
                    
                    return record;
                };
                
                // 获取事件参与者
                this.getEventAttendees = (event) => {
                    const eventData = event.rawRecord;
                    return eventData.partner_ids && eventData.partner_ids.length
                        ? eventData.partner_ids
                        : [eventData.partner_id[0]];
                };
                
                // 记录加载开始
                this.recordLoadStart = () => {
                    this.enhancedState.lastLoadTime = Date.now();
                    this.enhancedState.loadCount++;
                    this.modelStatistics.totalLoads++;
                };
                
                // 记录加载完成
                this.recordLoadComplete = () => {
                    const loadTime = Date.now() - this.enhancedState.lastLoadTime;
                    console.log(`Model load completed in ${loadTime}ms`);
                };
                
                // 记录参与者数据更新
                this.recordAttendeeDataUpdate = () => {
                    this.modelStatistics.attendeeDataUpdates++;
                };
                
                // 获取参与者缓存键
                this.getAttendeeCacheKey = (attendeeIds, eventIds) => {
                    return `${attendeeIds.sort().join(',')}_${eventIds.sort().join(',')}`;
                };
                
                // 处理加载错误
                this.handleLoadError = (error) => {
                    console.error('Model load error:', error);
                };
                
                // 处理参与者数据错误
                this.handleAttendeeDataError = (error) => {
                    console.error('Attendee data update error:', error);
                };
                
                // 获取模型信息
                this.getModelInfo = () => {
                    return {
                        loadCount: this.enhancedState.loadCount,
                        lastLoadTime: this.enhancedState.lastLoadTime,
                        cacheSize: this.enhancedState.attendeeCache.size,
                        syncStatus: this.syncStatus,
                        credentialStatus: this.credentialStatus,
                        defaultDuration: this.defaultDuration,
                        statistics: this.modelStatistics
                    };
                };
                
                // 清理缓存
                this.clearCache = () => {
                    this.enhancedState.attendeeCache.clear();
                    this.enhancedState.filterCache.clear();
                    this.enhancedState.syncStatusCache = null;
                    this.enhancedState.credentialStatusCache = null;
                };
            }
            
            // 重写原始方法
            async load() {
                return await this.enhancedLoad();
            }
            
            async updateAttendeeData(data) {
                return await this.enhancedUpdateAttendeeData(data);
            }
        };
    }
}
```

## 技术特点

### 1. 数据模型扩展
- **继承机制**: 继承标准日历模型
- **功能增强**: 增强参与者相关功能
- **状态管理**: 管理同步和凭证状态
- **服务集成**: 集成多个核心服务

### 2. 参与者处理
- **数据分离**: 为每个参与者分离事件数据
- **状态跟踪**: 跟踪参与者状态
- **过滤集成**: 集成参与者过滤器
- **颜色映射**: 映射参与者颜色

### 3. 重复事件管理
- **策略询问**: 询问重复更新策略
- **用户交互**: 提供用户选择界面
- **策略应用**: 应用选择的策略
- **数据一致性**: 保持数据一致性

### 4. 异步数据处理
- **并行加载**: 并行加载多个数据源
- **异步操作**: 支持异步数据操作
- **错误处理**: 完善的错误处理机制
- **性能优化**: 优化数据加载性能

## 设计模式

### 1. 模板方法模式 (Template Method Pattern)
- **加载模板**: 定义数据加载模板
- **处理模板**: 定义数据处理模板
- **扩展点**: 提供扩展点

### 2. 策略模式 (Strategy Pattern)
- **更新策略**: 不同的重复更新策略
- **过滤策略**: 不同的过滤策略
- **处理策略**: 不同的数据处理策略

### 3. 观察者模式 (Observer Pattern)
- **数据观察**: 观察数据变化
- **状态观察**: 观察模型状态
- **过滤观察**: 观察过滤器变化

### 4. 工厂模式 (Factory Pattern)
- **记录工厂**: 创建参与者记录
- **过滤器工厂**: 创建过滤器对象
- **状态工厂**: 创建状态对象

## 注意事项

1. **数据一致性**: 确保参与者数据的一致性
2. **性能优化**: 优化大量参与者的处理性能
3. **内存管理**: 管理复制记录的内存使用
4. **错误处理**: 处理网络和数据错误

## 扩展建议

1. **缓存优化**: 优化参与者数据缓存
2. **批量处理**: 支持批量数据处理
3. **增量更新**: 支持增量数据更新
4. **离线支持**: 支持离线数据处理
5. **性能监控**: 添加性能监控功能

该参与者日历模型为Odoo Calendar模块提供了完整的参与者视角数据管理功能，通过智能的数据处理和状态管理确保了参与者日历视图的高效运行。

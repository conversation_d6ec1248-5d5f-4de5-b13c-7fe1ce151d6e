# AttendeeCalendarController - 参与者日历控制器

## 概述

`attendee_calendar_controller.js` 是 Odoo Calendar 模块的参与者日历控制器，负责管理参与者视角的日历视图交互。该模块包含142行代码，是一个功能完整的控制器组件，专门用于处理参与者日历的用户交互，具备事件创建、编辑、删除、权限管理、提供商配置等特性，是参与者日历视图的核心控制组件。

## 文件信息
- **路径**: `/calendar/static/src/views/attendee_calendar/attendee_calendar_controller.js`
- **行数**: 142
- **模块**: `@calendar/views/attendee_calendar/attendee_calendar_controller`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'                   // 翻译服务
'@web/views/calendar/calendar_controller'      // 日历控制器基类
'@web/core/user'                               // 用户服务
'@web/core/utils/hooks'                        // 工具钩子
'@odoo/owl'                                    // OWL框架
'@calendar/views/calendar_form/calendar_quick_create' // 快速创建表单
```

## 核心功能

### 1. 控制器定义

```javascript
class AttendeeCalendarController extends CalendarController {
    static template = "calendar.AttendeeCalendarController";
    static components = {
        ...AttendeeCalendarController.components,
        QuickCreateFormView: CalendarQuickCreate,
    };

    setup() {
        super.setup();
        this.actionService = useService("action");
        this.orm = useService("orm");
        onWillStart(async () => {
            this.isSystemUser = await user.hasGroup("base.group_system");
        });
    }
}
```

**控制器特性**:
- **继承扩展**: 继承标准日历控制器
- **模板绑定**: 使用专用模板
- **组件集成**: 集成快速创建组件
- **服务注入**: 注入动作和ORM服务
- **权限检查**: 检查系统用户权限

### 2. 智能删除处理

```javascript
deleteRecord(record) {
    if (
        user.partnerId === record.attendeeId &&
        user.partnerId === record.rawRecord.partner_id[0]
    ) {
        if (record.rawRecord.recurrency) {
            this.openRecurringDeletionWizard(record);
        } else {
            super.deleteRecord(...arguments);
        }
    } else {
        // Decline event
        this.orm
            .call("calendar.attendee", "do_decline", [record.calendarAttendeeId])
            .then(this.model.load.bind(this.model));
    }
}
```

**删除功能**:
- **权限判断**: 判断用户是否为组织者
- **重复事件**: 特殊处理重复事件
- **拒绝参与**: 非组织者拒绝参与事件
- **模型更新**: 操作后更新模型

### 3. 提供商配置

```javascript
configureCalendarProviderSync(providerName) {
    this.actionService.doAction({
        name: _t("Connect your Calendar"),
        type: "ir.actions.act_window",
        res_model: "calendar.provider.config",
        views: [[false, "form"]],
        view_mode: "form",
        target: "new",
        context: {
            default_external_calendar_provider: providerName,
            dialog_size: "medium",
        },
    });
}
```

**配置功能**:
- **提供商连接**: 配置外部日历提供商
- **对话框**: 使用新窗口对话框
- **上下文设置**: 设置默认提供商
- **国际化**: 支持标题国际化

## 技术特点

### 1. 权限管理
- **角色识别**: 智能识别用户角色（组织者/参与者）
- **权限检查**: 检查用户操作权限
- **条件操作**: 基于权限执行不同操作
- **安全控制**: 确保操作安全性

### 2. 智能删除
- **组织者删除**: 组织者可以删除事件
- **参与者拒绝**: 参与者只能拒绝参与
- **重复事件**: 特殊处理重复事件
- **向导集成**: 集成删除向导

### 3. 快速创建
- **对话框**: 使用中等大小对话框
- **上下文传递**: 传递创建上下文
- **保存回调**: 设置保存后回调
- **模型重载**: 自动重新加载数据

### 4. 提供商集成
- **同步配置**: 配置外部日历同步
- **多提供商**: 支持多个日历提供商
- **权限控制**: 控制配置权限
- **用户友好**: 提供友好的配置界面

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- **删除策略**: 不同角色的删除策略
- **编辑策略**: 不同的编辑策略
- **权限策略**: 不同的权限检查策略

### 2. 命令模式 (Command Pattern)
- **动作命令**: 封装各种动作操作
- **撤销支持**: 支持操作撤销
- **批量操作**: 支持批量命令

### 3. 观察者模式 (Observer Pattern)
- **事件观察**: 观察日历事件变化
- **状态观察**: 观察控制器状态
- **用户观察**: 观察用户交互

### 4. 工厂模式 (Factory Pattern)
- **动作工厂**: 创建不同的动作
- **对话框工厂**: 创建不同的对话框
- **上下文工厂**: 创建上下文对象

## 注意事项

1. **权限验证**: 确保用户权限验证的准确性
2. **数据一致性**: 保持数据操作的一致性
3. **用户体验**: 提供清晰的操作反馈
4. **错误处理**: 处理各种异常情况

## 扩展建议

1. **批量操作**: 支持批量事件操作
2. **拖拽编辑**: 支持拖拽编辑事件
3. **快捷键**: 添加键盘快捷键支持
4. **自定义视图**: 支持自定义视图配置
5. **离线支持**: 支持离线操作

该参与者日历控制器为Odoo Calendar模块提供了完整的参与者视角日历管理功能，通过智能的权限管理和用户友好的交互确保了不同角色用户的最佳体验。
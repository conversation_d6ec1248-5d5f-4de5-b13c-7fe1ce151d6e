# Attendee Calendar Year View Components

## 概述

Attendee Calendar Year View 模块提供了专门为年视图设计的参与者日历组件。年视图需要特殊的显示和交互方式，因为它需要在有限的空间内显示大量的事件信息。该模块包含了优化的弹出框和渲染器，确保在年视图中参与者信息的清晰展示和高效交互。

## 组件结构

```
year/
├── attendee_calendar_year_popover.js      # 年视图参与者弹出框
└── attendee_calendar_year_renderer.js    # 年视图参与者渲染器
```

## 核心组件

### 1. 年视图参与者弹出框 (Year Popover)

**文件**: `attendee_calendar_year_popover.js`

年视图参与者弹出框是专门为年视图优化的紧凑型弹出框组件。

#### 设计特点

- **紧凑显示**: 适合年视图的有限空间
- **状态指示**: 清晰的参与者状态视觉指示
- **快速操作**: 提供快速的状态切换功能
- **样式优化**: 针对年视图的样式优化

#### 核心功能

```javascript
class AttendeeCalendarYearPopover extends CalendarYearPopover {
    static subTemplates = {
        ...CalendarYearPopover.subTemplates,
        body: "calendar.AttendeeCalendarYearPopover.body",
    };
    
    // 获取记录样式类
    getRecordClass(record) {
        const classes = [super.getRecordClass(record)];
        
        // 添加参与者状态样式
        if (record.isAlone) {
            classes.push("o_attendee_status_alone");
        } else {
            classes.push(`o_attendee_status_${record.attendeeStatus}`);
        }
        
        return classes.join(" ");
    }
}
```

#### 年视图特殊考虑

1. **空间限制**: 年视图中每个日期格子空间有限
2. **信息密度**: 需要在小空间内显示关键信息
3. **交互简化**: 简化交互以适应小尺寸显示
4. **性能优化**: 优化渲染性能以处理大量事件

#### 显示策略

```javascript
// 年视图显示策略
const YEAR_VIEW_DISPLAY_STRATEGY = {
    // 最多显示的事件数量
    maxEventsPerDay: 3,
    
    // 事件优先级排序
    eventPriority: [
        'organizer_events',    // 用户作为组织者的事件
        'accepted_events',     // 已接受的事件
        'tentative_events',    // 暂定的事件
        'declined_events'      // 已拒绝的事件（最低优先级）
    ],
    
    // 显示模式
    displayModes: {
        'indicator': {
            // 只显示状态指示器
            showTitle: false,
            showTime: false,
            showStatus: true
        },
        'compact': {
            // 紧凑显示
            showTitle: true,
            showTime: false,
            showStatus: true,
            maxTitleLength: 15
        },
        'minimal': {
            // 最小显示
            showTitle: false,
            showTime: false,
            showStatus: false,
            showDot: true
        }
    }
};
```

### 2. 年视图参与者渲染器 (Year Renderer)

**文件**: `attendee_calendar_year_renderer.js`

年视图参与者渲染器负责在年视图中渲染参与者事件。

#### 核心功能

```javascript
class AttendeeCalendarYearRenderer extends CalendarYearRenderer {
    static components = {
        ...CalendarYearRenderer.components,
        Popover: AttendeeCalendarYearPopover,
    };
}
```

#### 渲染优化

1. **组件替换**: 使用专门的年视图弹出框
2. **性能考虑**: 优化大量事件的渲染性能
3. **内存管理**: 有效管理年视图的内存使用
4. **响应式**: 适应不同屏幕尺寸

## 年视图布局系统

### 1. 网格布局

```css
/* 年视图网格布局 */
.o_calendar_year_view {
    display: grid;
    grid-template-columns: repeat(4, 1fr); /* 4列，每列3个月 */
    gap: 20px;
    padding: 20px;
}

.o_calendar_month {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
}

.o_calendar_month_header {
    background-color: #f8f9fa;
    padding: 10px;
    font-weight: bold;
    text-align: center;
}

.o_calendar_month_grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr); /* 7列，一周7天 */
}

.o_calendar_day {
    min-height: 80px;
    border: 1px solid #e9ecef;
    padding: 4px;
    position: relative;
}
```

### 2. 事件显示

```css
/* 年视图事件样式 */
.o_calendar_year_event {
    font-size: 10px;
    padding: 1px 3px;
    margin: 1px 0;
    border-radius: 2px;
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 参与者状态指示器 */
.o_attendee_status_indicator {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 2px;
}

.o_attendee_status_accepted .o_attendee_status_indicator {
    background-color: #28a745;
}

.o_attendee_status_declined .o_attendee_status_indicator {
    background-color: #dc3545;
}

.o_attendee_status_tentative .o_attendee_status_indicator {
    background-color: #6c757d;
}

.o_attendee_status_needsAction .o_attendee_status_indicator {
    background-color: #ffc107;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}
```

## 交互设计

### 1. 悬停效果

```javascript
// 年视图悬停效果
class YearViewInteraction {
    setupHoverEffects() {
        // 事件悬停显示详情
        this.addEventListeners('.o_calendar_year_event', {
            mouseenter: this.showEventPreview.bind(this),
            mouseleave: this.hideEventPreview.bind(this),
            click: this.openEventPopover.bind(this)
        });
    }
    
    showEventPreview(event) {
        const eventData = this.getEventData(event.target);
        const tooltip = this.createTooltip(eventData);
        this.positionTooltip(tooltip, event.target);
    }
    
    createTooltip(eventData) {
        return `
            <div class="o_year_event_tooltip">
                <div class="tooltip-title">${eventData.title}</div>
                <div class="tooltip-time">${eventData.time}</div>
                <div class="tooltip-status">
                    <span class="status-indicator ${eventData.statusClass}"></span>
                    ${eventData.statusText}
                </div>
            </div>
        `;
    }
}
```

### 2. 键盘导航

```javascript
// 年视图键盘导航
class YearViewKeyboardNavigation {
    setupKeyboardNavigation() {
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
    }
    
    handleKeyDown(event) {
        switch (event.key) {
            case 'ArrowLeft':
                this.navigateToMonth(-1);
                break;
            case 'ArrowRight':
                this.navigateToMonth(1);
                break;
            case 'ArrowUp':
                this.navigateToMonth(-4); // 上一行（4个月）
                break;
            case 'ArrowDown':
                this.navigateToMonth(4); // 下一行（4个月）
                break;
            case 'Enter':
            case ' ':
                this.openSelectedEvent();
                break;
        }
    }
}
```

## 性能优化

### 1. 虚拟滚动

```javascript
// 年视图虚拟滚动
class YearViewVirtualScroll {
    constructor(container, itemHeight = 80) {
        this.container = container;
        this.itemHeight = itemHeight;
        this.visibleItems = Math.ceil(container.clientHeight / itemHeight) + 2;
        this.setupVirtualScroll();
    }
    
    setupVirtualScroll() {
        this.container.addEventListener('scroll', this.onScroll.bind(this));
        this.renderVisibleItems();
    }
    
    onScroll() {
        const scrollTop = this.container.scrollTop;
        const startIndex = Math.floor(scrollTop / this.itemHeight);
        this.renderVisibleItems(startIndex);
    }
    
    renderVisibleItems(startIndex = 0) {
        const endIndex = Math.min(startIndex + this.visibleItems, this.totalItems);
        
        // 只渲染可见的月份
        for (let i = startIndex; i < endIndex; i++) {
            this.renderMonth(i);
        }
        
        // 移除不可见的月份
        this.removeInvisibleMonths(startIndex, endIndex);
    }
}
```

### 2. 事件聚合

```javascript
// 年视图事件聚合
class YearViewEventAggregation {
    aggregateEvents(events, date) {
        const dayEvents = events.filter(event => 
            this.isSameDay(event.start, date)
        );
        
        if (dayEvents.length <= 3) {
            return dayEvents;
        }
        
        // 聚合多个事件
        const priorityEvents = this.sortByPriority(dayEvents).slice(0, 2);
        const remainingCount = dayEvents.length - 2;
        
        return [
            ...priorityEvents,
            {
                id: `aggregated_${date}`,
                title: `+${remainingCount} more`,
                type: 'aggregated',
                events: dayEvents.slice(2)
            }
        ];
    }
    
    sortByPriority(events) {
        return events.sort((a, b) => {
            const priorityA = this.getEventPriority(a);
            const priorityB = this.getEventPriority(b);
            return priorityB - priorityA;
        });
    }
    
    getEventPriority(event) {
        if (event.isOrganizer) return 4;
        if (event.attendeeStatus === 'accepted') return 3;
        if (event.attendeeStatus === 'tentative') return 2;
        if (event.attendeeStatus === 'needsAction') return 1;
        return 0; // declined
    }
}
```

## 响应式设计

### 1. 断点系统

```css
/* 年视图响应式断点 */
@media (max-width: 1200px) {
    .o_calendar_year_view {
        grid-template-columns: repeat(3, 1fr); /* 3列 */
    }
}

@media (max-width: 768px) {
    .o_calendar_year_view {
        grid-template-columns: repeat(2, 1fr); /* 2列 */
    }
    
    .o_calendar_day {
        min-height: 60px;
    }
}

@media (max-width: 480px) {
    .o_calendar_year_view {
        grid-template-columns: 1fr; /* 1列 */
    }
    
    .o_calendar_day {
        min-height: 40px;
    }
    
    .o_calendar_year_event {
        font-size: 8px;
    }
}
```

### 2. 自适应显示

```javascript
// 年视图自适应显示
class YearViewResponsive {
    constructor() {
        this.breakpoints = {
            mobile: 480,
            tablet: 768,
            desktop: 1200
        };
        
        this.setupResponsive();
    }
    
    setupResponsive() {
        window.addEventListener('resize', this.onResize.bind(this));
        this.onResize(); // 初始化
    }
    
    onResize() {
        const width = window.innerWidth;
        const device = this.getDeviceType(width);
        
        this.adjustLayout(device);
        this.adjustEventDisplay(device);
    }
    
    getDeviceType(width) {
        if (width <= this.breakpoints.mobile) return 'mobile';
        if (width <= this.breakpoints.tablet) return 'tablet';
        return 'desktop';
    }
    
    adjustEventDisplay(device) {
        const maxEvents = {
            mobile: 1,
            tablet: 2,
            desktop: 3
        };
        
        this.setMaxEventsPerDay(maxEvents[device]);
    }
}
```

## 使用示例

### 1. 基本使用

```xml
<!-- 年视图模板 -->
<calendar js_class="attendee_calendar" 
          mode="year"
          date_start="start" 
          date_stop="stop"
          color="partner_id">
    <field name="name"/>
    <field name="partner_ids"/>
    <field name="attendee_status"/>
</calendar>
```

### 2. 自定义年视图

```javascript
// 自定义年视图组件
class CustomAttendeeYearView extends AttendeeCalendarYearRenderer {
    setup() {
        super.setup();
        this.customSettings = {
            showWeekNumbers: true,
            highlightToday: true,
            showHolidays: true
        };
    }
    
    renderMonth(monthData) {
        const monthElement = super.renderMonth(monthData);
        
        if (this.customSettings.showHolidays) {
            this.addHolidayIndicators(monthElement, monthData);
        }
        
        return monthElement;
    }
}
```

## 最佳实践

### 1. 性能优化

- 使用虚拟滚动处理大量数据
- 实现事件聚合减少DOM元素
- 使用防抖处理频繁的交互事件

### 2. 用户体验

- 提供清晰的状态指示器
- 实现流畅的悬停和点击效果
- 支持键盘导航

### 3. 可访问性

- 添加适当的ARIA标签
- 确保键盘可访问性
- 提供高对比度模式

## 扩展指南

### 1. 添加新的显示模式

```javascript
// 添加周视图模式
const WEEK_VIEW_MODE = {
    name: 'week',
    columns: 7,
    rows: 6,
    cellHeight: 100
};
```

### 2. 自定义事件聚合

```javascript
// 自定义事件聚合策略
class CustomEventAggregation extends YearViewEventAggregation {
    aggregateByCategory(events) {
        // 按类别聚合事件
        const categories = this.groupByCategory(events);
        return this.createCategoryIndicators(categories);
    }
}
```

## 依赖关系

- **@web/views/calendar/calendar_year**: 基础年视图组件
- **@calendar/views/attendee_calendar/common**: 通用参与者组件

---

*年视图组件为参与者日历提供了优化的年度视图体验，平衡了信息密度和用户体验。*

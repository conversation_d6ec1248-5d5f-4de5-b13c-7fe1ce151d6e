/**************************************************************************************************
*  Filepath: /calendar/static/src/views/attendee_calendar/year/attendee_calendar_year_popover.js  *
*  Lines: 25                                                                                      *
**************************************************************************************************/
odoo.define('@calendar/views/attendee_calendar/year/attendee_calendar_year_popover', ['@web/views/calendar/calendar_year/calendar_year_popover'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { CalendarYearPopover } = require("@web/views/calendar/calendar_year/calendar_year_popover");

const AttendeeCalendarYearPopover = __exports.AttendeeCalendarYearPopover = class AttendeeCalendarYearPopover extends CalendarYearPopover {
    static subTemplates = {
        ...CalendarYearPopover.subTemplates,
        body: "calendar.AttendeeCalendarYearPopover.body",
    };
    getRecordClass(record) {
        const classes = [super.getRecordClass(record)];
        if (record.isAlone) {
            classes.push("o_attendee_status_alone");
        } else {
            classes.push(`o_attendee_status_${record.attendeeStatus}`);
        }
        return classes.join(" ");
    }
}

return __exports;
});
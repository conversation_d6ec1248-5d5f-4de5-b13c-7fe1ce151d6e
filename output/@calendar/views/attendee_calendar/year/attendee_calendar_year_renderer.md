# AttendeeCalendarYearRenderer - 参与者日历年视图渲染器

## 概述

`attendee_calendar_year_renderer.js` 是 Odoo Calendar 模块的参与者日历年视图渲染器，负责渲染参与者视角的年视图日历。该模块包含17行代码，是一个功能专门的渲染器组件，专门用于配置参与者日历年视图的渲染组件，具备弹出框集成、组件替换、年视图优化等特性，是参与者年视图的核心渲染层。

## 文件信息
- **路径**: `/calendar/static/src/views/attendee_calendar/year/attendee_calendar_year_renderer.js`
- **行数**: 17
- **模块**: `@calendar/views/attendee_calendar/year/attendee_calendar_year_renderer`

## 依赖关系

```javascript
// 核心依赖
'@web/views/calendar/calendar_year/calendar_year_renderer' // 日历年视图渲染器基类
'@calendar/views/attendee_calendar/year/attendee_calendar_year_popover' // 参与者年视图弹出框
```

## 核心功能

### 1. 渲染器定义

```javascript
class AttendeeCalendarYearRenderer extends CalendarYearRenderer {
    static components = {
        ...CalendarYearRenderer.components,
        Popover: AttendeeCalendarYearPopover,
    };
}
```

**渲染器特性**:
- **继承扩展**: 继承年视图渲染器基类
- **组件替换**: 替换弹出框组件
- **组件继承**: 继承其他基础组件
- **专用集成**: 集成参与者年视图弹出框

### 2. 弹出框集成

```javascript
static components = {
    ...CalendarYearRenderer.components,
    Popover: AttendeeCalendarYearPopover,
};
```

**集成功能**:
- **组件映射**: 映射弹出框组件
- **基础继承**: 继承父类所有组件
- **专用替换**: 使用专门的参与者弹出框
- **功能增强**: 增强年视图弹出框功能

## 使用场景

### 1. 年视图渲染器管理器

```javascript
// 年视图渲染器管理器
class YearRendererManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置渲染器配置
        this.rendererConfig = {
            enableAttendeePopover: true,
            enableCompactDisplay: true,
            enableStatusIndicators: true,
            enableEventGrouping: true,
            enableQuickPreview: false,
            maxEventsPerCell: 3,
            showEventCount: true,
            enableHoverEffects: true
        };
        
        // 设置年视图布局
        this.yearViewLayout = {
            monthsPerRow: 4,
            showWeekNumbers: false,
            showMonthHeaders: true,
            compactMode: true,
            cellHeight: 120,
            cellWidth: 180,
            eventHeight: 16,
            eventSpacing: 2
        };
        
        // 设置组件映射
        this.componentMapping = new Map([
            ['Popover', {
                name: 'AttendeeCalendarYearPopover',
                class: 'AttendeeCalendarYearPopover',
                features: ['status_display', 'compact_view', 'attendee_info']
            }],
            ['MonthCell', {
                name: 'CalendarYearMonthCell',
                class: 'CalendarYearMonthCell',
                features: ['event_display', 'date_navigation', 'selection']
            }],
            ['EventIndicator', {
                name: 'CalendarYearEventIndicator',
                class: 'CalendarYearEventIndicator',
                features: ['status_color', 'count_display', 'hover_preview']
            }]
        ]);
        
        // 设置渲染统计
        this.rendererStatistics = {
            totalRenders: 0,
            componentCreations: 0,
            popoverOpens: 0,
            eventDisplays: 0,
            monthRenders: 0,
            errorCount: 0,
            averageRenderTime: 0,
            totalRenderTime: 0
        };
        
        this.initializeRendererSystem();
    }
    
    // 初始化渲染器系统
    initializeRendererSystem() {
        // 创建增强的渲染器
        this.createEnhancedRenderer();
        
        // 设置组件系统
        this.setupComponentSystem();
        
        // 设置布局系统
        this.setupLayoutSystem();
        
        // 设置性能系统
        this.setupPerformanceSystem();
    }
    
    // 创建增强的渲染器
    createEnhancedRenderer() {
        const originalRenderer = AttendeeCalendarYearRenderer;
        
        this.EnhancedAttendeeCalendarYearRenderer = class extends originalRenderer {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加年视图特性
                this.addYearViewFeatures();
                
                // 添加性能监控
                this.addPerformanceMonitoring();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    renderCount: 0,
                    lastRenderTime: null,
                    componentCache: new Map(),
                    layoutCache: new Map(),
                    currentYear: new Date().getFullYear(),
                    selectedMonth: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
                
                // 初始化渲染器
                this.initializeYearRenderer();
            }
            
            addEnhancedMethods() {
                // 增强的渲染方法
                this.enhancedRender = () => {
                    try {
                        // 记录渲染开始
                        this.recordRenderStart();
                        
                        // 执行渲染
                        const result = this.executeRender();
                        
                        // 记录渲染完成
                        this.recordRenderComplete();
                        
                        return result;
                        
                    } catch (error) {
                        this.handleRenderError(error);
                        throw error;
                    }
                };
                
                // 执行渲染
                this.executeRender = () => {
                    // 准备渲染数据
                    const renderData = this.prepareRenderData();
                    
                    // 渲染年视图
                    const yearView = this.renderYearView(renderData);
                    
                    // 渲染月份网格
                    const monthGrid = this.renderMonthGrid(renderData);
                    
                    // 渲染事件指示器
                    const eventIndicators = this.renderEventIndicators(renderData);
                    
                    return {
                        yearView,
                        monthGrid,
                        eventIndicators
                    };
                };
                
                // 准备渲染数据
                this.prepareRenderData = () => {
                    return {
                        year: this.enhancedState.currentYear,
                        months: this.getMonthsData(),
                        events: this.getEventsData(),
                        attendees: this.getAttendeesData(),
                        layout: this.yearViewLayout
                    };
                };
                
                // 获取月份数据
                this.getMonthsData = () => {
                    const months = [];
                    for (let i = 0; i < 12; i++) {
                        months.push({
                            index: i,
                            name: this.getMonthName(i),
                            days: this.getMonthDays(this.enhancedState.currentYear, i),
                            events: this.getMonthEvents(i)
                        });
                    }
                    return months;
                };
                
                // 获取事件数据
                this.getEventsData = () => {
                    return this.props.model.records || {};
                };
                
                // 获取参与者数据
                this.getAttendeesData = () => {
                    return this.props.model.attendees || [];
                };
                
                // 渲染年视图
                this.renderYearView = (data) => {
                    return {
                        year: data.year,
                        layout: data.layout,
                        monthsPerRow: data.layout.monthsPerRow
                    };
                };
                
                // 渲染月份网格
                this.renderMonthGrid = (data) => {
                    return data.months.map(month => {
                        return this.renderMonth(month, data);
                    });
                };
                
                // 渲染单个月份
                this.renderMonth = (month, data) => {
                    return {
                        index: month.index,
                        name: month.name,
                        days: month.days,
                        events: month.events,
                        eventCount: month.events.length,
                        hasEvents: month.events.length > 0
                    };
                };
                
                // 渲染事件指示器
                this.renderEventIndicators = (data) => {
                    const indicators = [];
                    
                    for (const [eventId, event] of Object.entries(data.events)) {
                        const indicator = this.createEventIndicator(event);
                        indicators.push(indicator);
                    }
                    
                    return indicators;
                };
                
                // 创建事件指示器
                this.createEventIndicator = (event) => {
                    return {
                        id: event.id,
                        title: event.title,
                        date: event.start,
                        status: event.attendeeStatus,
                        isAlone: event.isAlone,
                        color: this.getEventColor(event),
                        indicator: this.getEventIndicator(event)
                    };
                };
                
                // 获取事件颜色
                this.getEventColor = (event) => {
                    if (event.isAlone) {
                        return '#17a2b8';
                    }
                    
                    const statusColors = {
                        'needsAction': '#ffc107',
                        'accepted': '#28a745',
                        'declined': '#dc3545',
                        'tentative': '#6c757d'
                    };
                    
                    return statusColors[event.attendeeStatus] || '#6c757d';
                };
                
                // 获取事件指示器
                this.getEventIndicator = (event) => {
                    if (event.isAlone) {
                        return '●';
                    }
                    
                    const statusIndicators = {
                        'needsAction': '?',
                        'accepted': '✓',
                        'declined': '✗',
                        'tentative': '~'
                    };
                    
                    return statusIndicators[event.attendeeStatus] || '●';
                };
                
                // 获取月份名称
                this.getMonthName = (monthIndex) => {
                    const monthNames = [
                        'January', 'February', 'March', 'April',
                        'May', 'June', 'July', 'August',
                        'September', 'October', 'November', 'December'
                    ];
                    return monthNames[monthIndex];
                };
                
                // 获取月份天数
                this.getMonthDays = (year, month) => {
                    const daysInMonth = new Date(year, month + 1, 0).getDate();
                    const days = [];
                    
                    for (let day = 1; day <= daysInMonth; day++) {
                        days.push({
                            day: day,
                            date: new Date(year, month, day),
                            events: this.getDayEvents(year, month, day)
                        });
                    }
                    
                    return days;
                };
                
                // 获取月份事件
                this.getMonthEvents = (monthIndex) => {
                    const events = [];
                    const eventsData = this.getEventsData();
                    
                    for (const [eventId, event] of Object.entries(eventsData)) {
                        const eventDate = new Date(event.start);
                        if (eventDate.getMonth() === monthIndex) {
                            events.push(event);
                        }
                    }
                    
                    return events;
                };
                
                // 获取日期事件
                this.getDayEvents = (year, month, day) => {
                    const events = [];
                    const eventsData = this.getEventsData();
                    const targetDate = new Date(year, month, day);
                    
                    for (const [eventId, event] of Object.entries(eventsData)) {
                        const eventDate = new Date(event.start);
                        if (this.isSameDay(eventDate, targetDate)) {
                            events.push(event);
                        }
                    }
                    
                    return events;
                };
                
                // 检查是否同一天
                this.isSameDay = (date1, date2) => {
                    return date1.getFullYear() === date2.getFullYear() &&
                           date1.getMonth() === date2.getMonth() &&
                           date1.getDate() === date2.getDate();
                };
                
                // 切换年份
                this.switchYear = (year) => {
                    this.enhancedState.currentYear = year;
                    this.refreshRenderer();
                };
                
                // 选择月份
                this.selectMonth = (monthIndex) => {
                    this.enhancedState.selectedMonth = monthIndex;
                    this.highlightMonth(monthIndex);
                };
                
                // 高亮月份
                this.highlightMonth = (monthIndex) => {
                    // 实现月份高亮逻辑
                    console.log(`Highlighting month: ${this.getMonthName(monthIndex)}`);
                };
                
                // 刷新渲染器
                this.refreshRenderer = () => {
                    // 清理缓存
                    this.enhancedState.componentCache.clear();
                    this.enhancedState.layoutCache.clear();
                    
                    // 重新渲染
                    this.enhancedRender();
                };
                
                // 初始化年视图渲染器
                this.initializeYearRenderer = () => {
                    this.enhancedState.currentYear = new Date().getFullYear();
                    this.enhancedState.selectedMonth = new Date().getMonth();
                };
                
                // 记录渲染开始
                this.recordRenderStart = () => {
                    this.enhancedState.lastRenderTime = Date.now();
                    this.enhancedState.renderCount++;
                    this.rendererStatistics.totalRenders++;
                };
                
                // 记录渲染完成
                this.recordRenderComplete = () => {
                    const renderTime = Date.now() - this.enhancedState.lastRenderTime;
                    this.rendererStatistics.totalRenderTime += renderTime;
                    this.updateAverageRenderTime();
                    
                    console.log(`Year view rendered in ${renderTime}ms`);
                };
                
                // 更新平均渲染时间
                this.updateAverageRenderTime = () => {
                    if (this.rendererStatistics.totalRenders > 0) {
                        this.rendererStatistics.averageRenderTime = 
                            this.rendererStatistics.totalRenderTime / this.rendererStatistics.totalRenders;
                    }
                };
                
                // 处理渲染错误
                this.handleRenderError = (error) => {
                    this.rendererStatistics.errorCount++;
                    console.error('Year renderer error:', error);
                };
                
                // 获取渲染器信息
                this.getRendererInfo = () => {
                    return {
                        renderCount: this.enhancedState.renderCount,
                        currentYear: this.enhancedState.currentYear,
                        selectedMonth: this.enhancedState.selectedMonth,
                        cacheSize: {
                            components: this.enhancedState.componentCache.size,
                            layouts: this.enhancedState.layoutCache.size
                        },
                        statistics: this.rendererStatistics,
                        config: this.rendererConfig
                    };
                };
                
                // 清理缓存
                this.clearCache = () => {
                    this.enhancedState.componentCache.clear();
                    this.enhancedState.layoutCache.clear();
                };
            }
            
            addYearViewFeatures() {
                // 年视图特性
                this.yearViewFeatures = {
                    compactDisplay: this.rendererConfig.enableCompactDisplay,
                    statusIndicators: this.rendererConfig.enableStatusIndicators,
                    eventGrouping: this.rendererConfig.enableEventGrouping,
                    hoverEffects: this.rendererConfig.enableHoverEffects
                };
            }
            
            addPerformanceMonitoring() {
                // 性能监控
                this.performanceMonitor = {
                    enabled: true,
                    trackRenderTime: true,
                    trackComponentCreation: true,
                    trackMemoryUsage: false
                };
            }
            
            // 重写渲染方法
            render() {
                return this.enhancedRender();
            }
        };
    }
    
    // 设置组件系统
    setupComponentSystem() {
        this.componentConfig = {
            enableCustomPopover: this.rendererConfig.enableAttendeePopover,
            cacheComponents: true,
            lazyLoadComponents: false
        };
    }
    
    // 设置布局系统
    setupLayoutSystem() {
        this.layoutConfig = {
            responsive: true,
            adaptiveLayout: true,
            compactMode: this.rendererConfig.enableCompactDisplay
        };
    }
    
    // 设置性能系统
    setupPerformanceSystem() {
        this.performanceConfig = {
            enableCaching: true,
            enableLazyLoading: false,
            maxCacheSize: 100
        };
    }
    
    // 创建渲染器
    createRenderer(props) {
        return new this.EnhancedAttendeeCalendarYearRenderer(props);
    }
    
    // 注册组件
    registerComponent(name, config) {
        this.componentMapping.set(name, config);
    }
    
    // 获取渲染统计
    getRendererStatistics() {
        return {
            ...this.rendererStatistics,
            errorRate: this.rendererStatistics.totalRenders > 0 ? 
                (this.rendererStatistics.errorCount / this.rendererStatistics.totalRenders) * 100 : 0,
            componentVariety: this.componentMapping.size,
            averageRenderTime: this.rendererStatistics.averageRenderTime
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理组件映射
        this.componentMapping.clear();
        
        // 重置统计
        this.rendererStatistics = {
            totalRenders: 0,
            componentCreations: 0,
            popoverOpens: 0,
            eventDisplays: 0,
            monthRenders: 0,
            errorCount: 0,
            averageRenderTime: 0,
            totalRenderTime: 0
        };
    }
}

// 使用示例
const yearRendererManager = new YearRendererManager();

// 创建渲染器
const renderer = yearRendererManager.createRenderer({
    model: yearModel,
    props: yearProps
});

// 注册自定义组件
yearRendererManager.registerComponent('CustomIndicator', {
    name: 'CustomEventIndicator',
    class: 'CustomEventIndicator',
    features: ['custom_display', 'animation', 'tooltip']
});

// 获取统计信息
const stats = yearRendererManager.getRendererStatistics();
console.log('Year renderer statistics:', stats);
```

## 技术特点

### 1. 组件集成
- **弹出框替换**: 替换为参与者专用弹出框
- **组件继承**: 继承基础年视图组件
- **功能增强**: 增强年视图功能
- **专用优化**: 针对参与者视角优化

### 2. 年视图渲染
- **紧凑布局**: 适合年视图的紧凑布局
- **月份网格**: 12个月的网格布局
- **事件指示**: 清晰的事件指示器
- **状态显示**: 参与者状态显示

### 3. 性能优化
- **轻量级**: 轻量级的渲染实现
- **缓存机制**: 缓存渲染结果
- **快速渲染**: 快速的年视图渲染
- **内存优化**: 优化内存使用

### 4. 用户体验
- **直观显示**: 直观的年视图显示
- **状态区分**: 清晰的状态区分
- **交互友好**: 友好的用户交互
- **响应式**: 响应式设计

## 设计模式

### 1. 组合模式 (Composite Pattern)
- **组件组合**: 组合年视图组件
- **功能组合**: 组合不同功能
- **布局组合**: 组合布局元素

### 2. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 装饰基础渲染功能
- **组件装饰**: 装饰年视图组件
- **样式装饰**: 装饰视觉样式

### 3. 策略模式 (Strategy Pattern)
- **渲染策略**: 不同的渲染策略
- **布局策略**: 不同的布局策略
- **显示策略**: 不同的显示策略

### 4. 工厂模式 (Factory Pattern)
- **组件工厂**: 创建年视图组件
- **渲染器工厂**: 创建渲染器实例
- **配置工厂**: 创建配置对象

## 注意事项

1. **性能考虑**: 注意年视图的渲染性能
2. **组件一致性**: 确保组件的一致性
3. **内存管理**: 管理年视图的内存使用
4. **用户体验**: 确保良好的用户体验

## 扩展建议

1. **自定义布局**: 支持自定义年视图布局
2. **主题支持**: 支持主题切换
3. **动画效果**: 添加年视图动画
4. **交互增强**: 增强用户交互
5. **性能监控**: 添加性能监控

该参与者日历年视图渲染器为Odoo Calendar模块提供了专门的年视图渲染功能，通过集成参与者弹出框和优化的渲染机制确保了年视图中参与者日历的最佳显示效果。

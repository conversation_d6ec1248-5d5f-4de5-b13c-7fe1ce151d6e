# AttendeeCalendarYearPopover - 参与者日历年视图弹出框

## 概述

`attendee_calendar_year_popover.js` 是 Odoo Calendar 模块的参与者日历年视图弹出框组件，负责在年视图中显示参与者事件的详情弹出框。该模块包含25行代码，是一个功能专门的弹出框组件，专门用于年视图中参与者事件的详情显示，具备模板定制、样式管理、状态显示等特性，是年视图参与者交互的核心组件。

## 文件信息
- **路径**: `/calendar/static/src/views/attendee_calendar/year/attendee_calendar_year_popover.js`
- **行数**: 25
- **模块**: `@calendar/views/attendee_calendar/year/attendee_calendar_year_popover`

## 依赖关系

```javascript
// 核心依赖
'@web/views/calendar/calendar_year/calendar_year_popover' // 日历年视图弹出框基类
```

## 核心功能

### 1. 弹出框定义

```javascript
class AttendeeCalendarYearPopover extends CalendarYearPopover {
    static subTemplates = {
        ...CalendarYearPopover.subTemplates,
        body: "calendar.AttendeeCalendarYearPopover.body",
    };
}
```

**弹出框特性**:
- **继承扩展**: 继承年视图弹出框基类
- **模板定制**: 定制弹出框主体模板
- **模板继承**: 继承父类其他模板
- **专用显示**: 专门用于参与者年视图

### 2. 记录样式

```javascript
getRecordClass(record) {
    const classes = [super.getRecordClass(record)];
    if (record.isAlone) {
        classes.push("o_attendee_status_alone");
    } else {
        classes.push(`o_attendee_status_${record.attendeeStatus}`);
    }
    return classes.join(" ");
}
```

**样式功能**:
- **父类样式**: 继承父类记录样式
- **独立状态**: 添加独立参与者样式
- **状态样式**: 添加参与者状态样式
- **样式组合**: 组合多个样式类名

## 使用场景

### 1. 年视图弹出框管理器

```javascript
// 年视图弹出框管理器
class YearPopoverManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置弹出框配置
        this.popoverConfig = {
            enableStatusStyling: true,
            enableCompactDisplay: true,
            enableQuickActions: false,
            enableEventGrouping: true,
            maxEventsPerDay: 5,
            showEventTime: false,
            showEventDuration: false,
            defaultPosition: 'bottom'
        };
        
        // 设置年视图样式
        this.yearViewStyles = new Map([
            ['needsAction', {
                name: 'Needs Action',
                className: 'o_attendee_status_needsAction',
                color: '#ffc107',
                indicator: '?',
                priority: 1
            }],
            ['accepted', {
                name: 'Accepted',
                className: 'o_attendee_status_accepted',
                color: '#28a745',
                indicator: '✓',
                priority: 3
            }],
            ['declined', {
                name: 'Declined',
                className: 'o_attendee_status_declined',
                color: '#dc3545',
                indicator: '✗',
                priority: 2
            }],
            ['tentative', {
                name: 'Tentative',
                className: 'o_attendee_status_tentative',
                color: '#6c757d',
                indicator: '~',
                priority: 1
            }],
            ['alone', {
                name: 'Alone',
                className: 'o_attendee_status_alone',
                color: '#17a2b8',
                indicator: '●',
                priority: 4
            }]
        ]);
        
        // 设置显示模式
        this.displayModes = new Map([
            ['compact', {
                name: 'Compact',
                showDetails: false,
                maxLines: 1,
                showIcons: true,
                showTime: false
            }],
            ['normal', {
                name: 'Normal',
                showDetails: true,
                maxLines: 3,
                showIcons: true,
                showTime: true
            }],
            ['detailed', {
                name: 'Detailed',
                showDetails: true,
                maxLines: 5,
                showIcons: true,
                showTime: true
            }]
        ]);
        
        // 设置弹出框统计
        this.popoverStatistics = {
            totalOpens: 0,
            recordStyleCalculations: 0,
            templateRenders: 0,
            statusDisplays: 0,
            errorCount: 0,
            averageOpenTime: 0,
            totalOpenTime: 0
        };
        
        this.initializePopoverSystem();
    }
    
    // 初始化弹出框系统
    initializePopoverSystem() {
        // 创建增强的弹出框
        this.createEnhancedPopover();
        
        // 设置样式系统
        this.setupStyleSystem();
        
        // 设置模板系统
        this.setupTemplateSystem();
        
        // 设置显示系统
        this.setupDisplaySystem();
    }
    
    // 创建增强的弹出框
    createEnhancedPopover() {
        const originalPopover = AttendeeCalendarYearPopover;
        
        this.EnhancedAttendeeCalendarYearPopover = class extends originalPopover {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加年视图特性
                this.addYearViewFeatures();
                
                // 添加性能优化
                this.addPerformanceOptimizations();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    openTime: null,
                    displayMode: 'compact',
                    recordCount: 0,
                    styleCache: new Map(),
                    templateCache: new Map()
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
                
                // 初始化弹出框
                this.initializeYearPopover();
            }
            
            addEnhancedMethods() {
                // 增强的记录样式获取
                this.enhancedGetRecordClass = (record) => {
                    try {
                        // 记录样式计算
                        this.recordStyleCalculation();
                        
                        // 检查缓存
                        const cacheKey = this.getStyleCacheKey(record);
                        if (this.enhancedState.styleCache.has(cacheKey)) {
                            return this.enhancedState.styleCache.get(cacheKey);
                        }
                        
                        // 计算样式
                        const classes = this.calculateRecordClasses(record);
                        
                        // 缓存结果
                        this.enhancedState.styleCache.set(cacheKey, classes);
                        
                        return classes;
                        
                    } catch (error) {
                        this.handleStyleError(error, record);
                        return super.getRecordClass(record);
                    }
                };
                
                // 计算记录样式类
                this.calculateRecordClasses = (record) => {
                    const classes = [super.getRecordClass(record)];
                    
                    // 添加参与者状态样式
                    if (record.isAlone) {
                        classes.push("o_attendee_status_alone");
                        classes.push("o_year_event_alone");
                    } else {
                        classes.push(`o_attendee_status_${record.attendeeStatus}`);
                        classes.push(`o_year_event_${record.attendeeStatus}`);
                    }
                    
                    // 添加显示模式样式
                    classes.push(`o_year_popover_${this.enhancedState.displayMode}`);
                    
                    // 添加优先级样式
                    const styleInfo = this.getStatusStyleInfo(record);
                    if (styleInfo) {
                        classes.push(`o_priority_${styleInfo.priority}`);
                    }
                    
                    // 添加事件类型样式
                    const eventType = this.getEventType(record);
                    if (eventType) {
                        classes.push(`o_event_type_${eventType}`);
                    }
                    
                    return classes.join(" ");
                };
                
                // 获取状态样式信息
                this.getStatusStyleInfo = (record) => {
                    if (record.isAlone) {
                        return this.yearViewStyles.get('alone');
                    }
                    return this.yearViewStyles.get(record.attendeeStatus);
                };
                
                // 获取事件类型
                this.getEventType = (record) => {
                    if (record.rawRecord?.activity_type_id) {
                        const typeName = record.rawRecord.activity_type_id[1]?.toLowerCase();
                        if (typeName?.includes('meeting')) return 'meeting';
                        if (typeName?.includes('call')) return 'call';
                        if (typeName?.includes('task')) return 'task';
                    }
                    return 'event';
                };
                
                // 获取记录显示信息
                this.getRecordDisplayInfo = (record) => {
                    const styleInfo = this.getStatusStyleInfo(record);
                    const displayMode = this.displayModes.get(this.enhancedState.displayMode);
                    
                    return {
                        title: record.title || record.name,
                        indicator: styleInfo?.indicator || '',
                        color: styleInfo?.color || '#6c757d',
                        showDetails: displayMode?.showDetails || false,
                        showTime: displayMode?.showTime || false,
                        showIcons: displayMode?.showIcons || true,
                        maxLines: displayMode?.maxLines || 1
                    };
                };
                
                // 格式化记录时间
                this.formatRecordTime = (record) => {
                    if (!record.start) {
                        return '';
                    }
                    
                    const start = new Date(record.start);
                    const isAllDay = record.isAllDay || false;
                    
                    if (isAllDay) {
                        return 'All Day';
                    }
                    
                    return start.toLocaleTimeString([], { 
                        hour: '2-digit', 
                        minute: '2-digit' 
                    });
                };
                
                // 获取记录工具提示
                this.getRecordTooltip = (record) => {
                    const displayInfo = this.getRecordDisplayInfo(record);
                    const time = this.formatRecordTime(record);
                    const status = this.getStatusDisplayName(record);
                    
                    let tooltip = displayInfo.title;
                    if (time) {
                        tooltip += `\nTime: ${time}`;
                    }
                    if (status) {
                        tooltip += `\nStatus: ${status}`;
                    }
                    
                    return tooltip;
                };
                
                // 获取状态显示名称
                this.getStatusDisplayName = (record) => {
                    const styleInfo = this.getStatusStyleInfo(record);
                    return styleInfo?.name || 'Unknown';
                };
                
                // 设置显示模式
                this.setDisplayMode = (mode) => {
                    if (this.displayModes.has(mode)) {
                        this.enhancedState.displayMode = mode;
                        this.refreshDisplay();
                    }
                };
                
                // 刷新显示
                this.refreshDisplay = () => {
                    // 清理样式缓存
                    this.enhancedState.styleCache.clear();
                    
                    // 重新渲染
                    this.render();
                };
                
                // 获取样式缓存键
                this.getStyleCacheKey = (record) => {
                    return `${record.id}_${record.attendeeStatus}_${record.isAlone}_${this.enhancedState.displayMode}`;
                };
                
                // 初始化年视图弹出框
                this.initializeYearPopover = () => {
                    this.enhancedState.openTime = Date.now();
                    this.enhancedState.displayMode = this.popoverConfig.enableCompactDisplay ? 'compact' : 'normal';
                };
                
                // 记录样式计算
                this.recordStyleCalculation = () => {
                    this.popoverStatistics.recordStyleCalculations++;
                };
                
                // 记录模板渲染
                this.recordTemplateRender = () => {
                    this.popoverStatistics.templateRenders++;
                };
                
                // 记录状态显示
                this.recordStatusDisplay = () => {
                    this.popoverStatistics.statusDisplays++;
                };
                
                // 处理样式错误
                this.handleStyleError = (error, record) => {
                    this.popoverStatistics.errorCount++;
                    console.error('Year popover style error:', error, record);
                };
                
                // 获取弹出框信息
                this.getPopoverInfo = () => {
                    return {
                        openTime: this.enhancedState.openTime,
                        displayMode: this.enhancedState.displayMode,
                        recordCount: this.enhancedState.recordCount,
                        cacheSize: {
                            styles: this.enhancedState.styleCache.size,
                            templates: this.enhancedState.templateCache.size
                        },
                        statistics: this.popoverStatistics,
                        config: this.popoverConfig
                    };
                };
                
                // 清理缓存
                this.clearCache = () => {
                    this.enhancedState.styleCache.clear();
                    this.enhancedState.templateCache.clear();
                };
                
                // 优化性能
                this.optimizePerformance = () => {
                    // 限制缓存大小
                    if (this.enhancedState.styleCache.size > 100) {
                        this.enhancedState.styleCache.clear();
                    }
                    
                    if (this.enhancedState.templateCache.size > 50) {
                        this.enhancedState.templateCache.clear();
                    }
                };
            }
            
            addYearViewFeatures() {
                // 年视图特性
                this.yearViewFeatures = {
                    compactDisplay: this.popoverConfig.enableCompactDisplay,
                    eventGrouping: this.popoverConfig.enableEventGrouping,
                    maxEventsPerDay: this.popoverConfig.maxEventsPerDay,
                    showIndicators: true
                };
            }
            
            addPerformanceOptimizations() {
                // 性能优化
                this.performanceOptimizations = {
                    enableCaching: true,
                    cacheTimeout: 300000, // 5 minutes
                    maxCacheSize: 100,
                    enableLazyLoading: false
                };
                
                // 定期清理缓存
                setInterval(() => {
                    this.optimizePerformance();
                }, this.performanceOptimizations.cacheTimeout);
            }
            
            // 重写原始方法
            getRecordClass(record) {
                return this.enhancedGetRecordClass(record);
            }
        };
    }
    
    // 设置样式系统
    setupStyleSystem() {
        this.styleConfig = {
            enabled: this.popoverConfig.enableStatusStyling,
            useIndicators: true,
            usePriorities: true,
            cacheStyles: true
        };
    }
    
    // 设置模板系统
    setupTemplateSystem() {
        this.templateConfig = {
            customBody: true,
            enableCaching: true,
            lazyLoading: false
        };
    }
    
    // 设置显示系统
    setupDisplaySystem() {
        this.displayConfig = {
            defaultMode: this.popoverConfig.enableCompactDisplay ? 'compact' : 'normal',
            allowModeSwitch: false,
            showEventCount: true
        };
    }
    
    // 创建弹出框
    createPopover(props) {
        return new this.EnhancedAttendeeCalendarYearPopover(props);
    }
    
    // 注册样式
    registerStyle(status, config) {
        this.yearViewStyles.set(status, config);
    }
    
    // 注册显示模式
    registerDisplayMode(name, config) {
        this.displayModes.set(name, config);
    }
    
    // 获取弹出框统计
    getPopoverStatistics() {
        return {
            ...this.popoverStatistics,
            errorRate: this.popoverStatistics.recordStyleCalculations > 0 ? 
                (this.popoverStatistics.errorCount / this.popoverStatistics.recordStyleCalculations) * 100 : 0,
            styleVariety: this.yearViewStyles.size,
            modeVariety: this.displayModes.size,
            averageOpenTime: this.popoverStatistics.averageOpenTime
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理样式
        this.yearViewStyles.clear();
        
        // 清理显示模式
        this.displayModes.clear();
        
        // 重置统计
        this.popoverStatistics = {
            totalOpens: 0,
            recordStyleCalculations: 0,
            templateRenders: 0,
            statusDisplays: 0,
            errorCount: 0,
            averageOpenTime: 0,
            totalOpenTime: 0
        };
    }
}

// 使用示例
const yearPopoverManager = new YearPopoverManager();

// 创建弹出框
const popover = yearPopoverManager.createPopover({
    records: yearRecords,
    date: selectedDate
});

// 注册自定义样式
yearPopoverManager.registerStyle('urgent', {
    name: 'Urgent',
    className: 'o_attendee_status_urgent',
    color: '#ff6b6b',
    indicator: '!',
    priority: 5
});

// 注册自定义显示模式
yearPopoverManager.registerDisplayMode('minimal', {
    name: 'Minimal',
    showDetails: false,
    maxLines: 1,
    showIcons: false,
    showTime: false
});

// 获取统计信息
const stats = yearPopoverManager.getPopoverStatistics();
console.log('Year popover statistics:', stats);
```

## 技术特点

### 1. 年视图专用
- **紧凑显示**: 适合年视图的紧凑显示
- **状态指示**: 清晰的状态指示器
- **样式优化**: 优化的年视图样式
- **性能考虑**: 考虑年视图的性能需求

### 2. 样式管理
- **状态样式**: 基于参与者状态的样式
- **独立状态**: 支持独立参与者样式
- **样式继承**: 继承父类样式
- **样式组合**: 智能组合多个样式

### 3. 模板定制
- **主体模板**: 定制弹出框主体模板
- **模板继承**: 继承其他模板
- **专用显示**: 专门的年视图显示
- **灵活配置**: 灵活的模板配置

### 4. 性能优化
- **样式缓存**: 缓存样式计算结果
- **轻量级**: 轻量级的弹出框实现
- **快速渲染**: 快速的渲染性能
- **内存优化**: 优化内存使用

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- **样式装饰**: 装饰基础样式
- **功能装饰**: 装饰弹出框功能
- **模板装饰**: 装饰模板显示

### 2. 策略模式 (Strategy Pattern)
- **显示策略**: 不同的显示模式策略
- **样式策略**: 不同的样式计算策略
- **渲染策略**: 不同的渲染策略

### 3. 工厂模式 (Factory Pattern)
- **样式工厂**: 创建样式对象
- **模板工厂**: 创建模板对象
- **配置工厂**: 创建配置对象

### 4. 缓存模式 (Cache Pattern)
- **样式缓存**: 缓存样式计算
- **模板缓存**: 缓存模板渲染
- **性能缓存**: 缓存性能数据

## 注意事项

1. **性能优化**: 注意年视图的性能优化
2. **样式一致性**: 确保样式的一致性
3. **缓存管理**: 管理缓存的生命周期
4. **内存使用**: 控制内存使用量

## 扩展建议

1. **自定义指示器**: 支持自定义状态指示器
2. **动画效果**: 添加弹出框动画
3. **主题支持**: 支持主题切换
4. **批量显示**: 支持批量事件显示
5. **交互增强**: 增强用户交互体验

该参与者日历年视图弹出框为Odoo Calendar模块提供了专门的年视图事件详情显示功能，通过紧凑的设计和智能的样式管理确保了年视图中参与者事件的最佳显示效果。

/***************************************************************************************************
*  Filepath: /calendar/static/src/views/attendee_calendar/year/attendee_calendar_year_renderer.js  *
*  Lines: 17                                                                                       *
***************************************************************************************************/
odoo.define('@calendar/views/attendee_calendar/year/attendee_calendar_year_renderer', ['@web/views/calendar/calendar_year/calendar_year_renderer', '@calendar/views/attendee_calendar/year/attendee_calendar_year_popover'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { CalendarYearRenderer } = require("@web/views/calendar/calendar_year/calendar_year_renderer");
const { AttendeeCalendarYearPopover } = require("@calendar/views/attendee_calendar/year/attendee_calendar_year_popover");

const AttendeeCalendarYearRenderer = __exports.AttendeeCalendarYearRenderer = class AttendeeCalendarYearRenderer extends CalendarYearRenderer {
    static components = {
        ...CalendarYearRenderer.components,
        Popover: AttendeeCalendarYearPopover,
    };
}

return __exports;
});
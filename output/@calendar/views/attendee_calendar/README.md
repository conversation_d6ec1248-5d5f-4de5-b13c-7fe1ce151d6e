# Attendee Calendar Module

## 概述

Attendee Calendar 模块是专门为参与者视角设计的日历视图系统，提供了完整的MVC架构来处理参与者相关的日历功能。该模块允许用户从参与者的角度查看、管理和交互日历事件，包括状态管理、权限控制和专门的用户界面。

## 模块结构

```
attendee_calendar/
├── attendee_calendar_controller.js    # 参与者日历控制器
├── attendee_calendar_model.js         # 参与者日历模型
├── attendee_calendar_renderer.js      # 参与者日历渲染器
├── attendee_calendar_view.js          # 参与者日历视图
├── common/                             # 通用组件
│   ├── attendee_calendar_common_popover.js    # 通用弹出框
│   └── attendee_calendar_common_renderer.js   # 通用渲染器
└── year/                               # 年视图组件
    ├── attendee_calendar_year_popover.js      # 年视图弹出框
    └── attendee_calendar_year_renderer.js     # 年视图渲染器
```

## 核心组件

### 1. 控制器 (Controller)

**文件**: `attendee_calendar_controller.js`

参与者日历控制器负责处理用户交互和业务逻辑：

- **状态管理**: 管理参与者的接受/拒绝/待定状态
- **权限控制**: 基于用户角色控制操作权限
- **事件处理**: 处理用户的点击、拖拽等交互
- **数据同步**: 与服务器同步参与者状态变化

```javascript
// 主要功能
- updateAttendeeStatus(eventId, status)  // 更新参与者状态
- checkPermissions(event)                // 检查操作权限
- handleEventClick(event)                // 处理事件点击
- syncAttendeeData()                     // 同步参与者数据
```

### 2. 模型 (Model)

**文件**: `attendee_calendar_model.js`

参与者日历模型管理数据逻辑和状态：

- **数据获取**: 获取参与者相关的日历数据
- **状态计算**: 计算参与者状态统计
- **过滤逻辑**: 基于参与者状态过滤事件
- **缓存管理**: 管理参与者数据缓存

```javascript
// 核心方法
- loadAttendeeEvents(filters)            // 加载参与者事件
- calculateAttendeeStats()               // 计算参与者统计
- filterByAttendeeStatus(status)         // 按状态过滤
- updateAttendeeCache(data)              // 更新缓存
```

### 3. 渲染器 (Renderer)

**文件**: `attendee_calendar_renderer.js`

参与者日历渲染器负责视觉呈现：

- **事件渲染**: 渲染参与者视角的事件
- **状态显示**: 显示参与者状态指示器
- **样式管理**: 管理参与者相关的样式
- **交互反馈**: 提供视觉交互反馈

```javascript
// 渲染功能
- renderAttendeeEvent(event)             // 渲染参与者事件
- applyAttendeeStyles(element, status)   // 应用状态样式
- showStatusIndicator(status)            // 显示状态指示器
- updateVisualFeedback(action)           // 更新视觉反馈
```

### 4. 视图 (View)

**文件**: `attendee_calendar_view.js`

参与者日历视图组装完整的视图架构：

- **组件集成**: 集成控制器、模型和渲染器
- **配置管理**: 管理视图配置参数
- **生命周期**: 管理视图生命周期
- **事件绑定**: 绑定组件间的事件通信

```javascript
// 视图配置
{
    type: "attendee_calendar",
    display_name: "Attendee Calendar",
    icon: "fa fa-users",
    Controller: AttendeeCalendarController,
    Model: AttendeeCalendarModel,
    Renderer: AttendeeCalendarRenderer
}
```

## 通用组件 (Common)

### 1. 通用弹出框 (Common Popover)

**文件**: `common/attendee_calendar_common_popover.js`

提供参与者事件的详情弹出框：

- **状态切换**: 允许参与者更改自己的状态
- **权限检查**: 检查用户是否有权限操作
- **重复事件**: 处理重复事件的状态更新
- **视频通话**: 集成视频通话功能

**主要功能**:
```javascript
- changeAttendeeStatus(status)           // 更改参与者状态
- checkUserPermissions()                 // 检查用户权限
- handleRecurrenceUpdate()               // 处理重复更新
- openVideoCall()                        // 打开视频通话
```

### 2. 通用渲染器 (Common Renderer)

**文件**: `common/attendee_calendar_common_renderer.js`

提供通用的参与者事件渲染功能：

- **事件转换**: 转换事件数据为渲染格式
- **样式计算**: 计算参与者状态样式
- **弹出框集成**: 集成参与者弹出框
- **选择控制**: 控制事件选择行为

**核心方法**:
```javascript
- convertRecordToEvent(record)           // 转换记录为事件
- eventClassNames(event)                 // 计算事件样式类
- onEventDidMount(element, event)        // 事件挂载处理
- isSelectionAllowed(event)              // 检查选择权限
```

## 年视图组件 (Year)

### 1. 年视图弹出框 (Year Popover)

**文件**: `year/attendee_calendar_year_popover.js`

专门为年视图设计的参与者弹出框：

- **紧凑显示**: 适合年视图的紧凑显示
- **状态指示**: 清晰的参与者状态指示
- **快速操作**: 提供快速状态切换
- **样式优化**: 优化年视图的显示样式

### 2. 年视图渲染器 (Year Renderer)

**文件**: `year/attendee_calendar_year_renderer.js`

年视图的参与者事件渲染器：

- **弹出框集成**: 集成年视图弹出框
- **组件替换**: 替换标准弹出框组件
- **年视图优化**: 针对年视图的渲染优化
- **性能考虑**: 考虑年视图的性能需求

## 参与者状态系统

### 状态类型

```javascript
const ATTENDEE_STATUSES = {
    'needsAction': {
        name: 'Needs Action',
        color: '#ffc107',
        icon: 'fa-question-circle',
        description: '等待响应'
    },
    'accepted': {
        name: 'Accepted',
        color: '#28a745',
        icon: 'fa-check-circle',
        description: '已接受'
    },
    'declined': {
        name: 'Declined',
        color: '#dc3545',
        icon: 'fa-times-circle',
        description: '已拒绝'
    },
    'tentative': {
        name: 'Tentative',
        color: '#6c757d',
        icon: 'fa-clock',
        description: '暂定'
    }
};
```

### 状态转换

```javascript
// 允许的状态转换
const STATUS_TRANSITIONS = {
    'needsAction': ['accepted', 'declined', 'tentative'],
    'accepted': ['declined', 'tentative'],
    'declined': ['accepted', 'tentative'],
    'tentative': ['accepted', 'declined']
};
```

## 权限系统

### 权限级别

1. **组织者 (Organizer)**
   - 可以编辑事件详情
   - 可以删除事件
   - 可以管理所有参与者

2. **参与者 (Attendee)**
   - 可以更改自己的状态
   - 可以查看事件详情
   - 不能编辑事件

3. **查看者 (Viewer)**
   - 只能查看公开事件
   - 不能进行任何操作

### 权限检查

```javascript
function checkPermission(user, event, action) {
    if (event.organizer_id === user.id) {
        return true; // 组织者有所有权限
    }
    
    if (event.attendee_ids.includes(user.partner_id)) {
        return action === 'change_status'; // 参与者只能改状态
    }
    
    return action === 'view' && !event.is_private; // 其他人只能看公开事件
}
```

## 使用示例

### 1. 创建参与者日历视图

```javascript
// 在视图定义中使用
<calendar js_class="attendee_calendar" 
          date_start="start" 
          date_stop="stop"
          color="partner_id"
          attendee_field="partner_ids">
    <field name="name"/>
    <field name="partner_ids"/>
    <field name="attendee_status"/>
</calendar>
```

### 2. 处理参与者状态变更

```javascript
// 在控制器中处理状态变更
async onAttendeeStatusChange(eventId, newStatus) {
    const event = this.model.getEvent(eventId);
    
    // 检查权限
    if (!this.checkChangePermission(event)) {
        return;
    }
    
    // 处理重复事件
    let recurrencePolicy = null;
    if (event.recurrency) {
        recurrencePolicy = await this.askRecurrenceUpdatePolicy();
    }
    
    // 更新状态
    await this.rpc('/calendar/event/change_attendee_status', {
        event_id: eventId,
        status: newStatus,
        recurrence_update: recurrencePolicy
    });
    
    // 刷新视图
    this.model.reload();
}
```

### 3. 自定义参与者样式

```css
/* 参与者状态样式 */
.o_attendee_status_accepted {
    border-left: 4px solid #28a745;
    background-color: rgba(40, 167, 69, 0.1);
}

.o_attendee_status_declined {
    border-left: 4px solid #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
    opacity: 0.7;
}

.o_attendee_status_tentative {
    border-left: 4px solid #6c757d;
    background-color: rgba(108, 117, 125, 0.1);
    border-style: dashed;
}

.o_attendee_status_needsAction {
    border-left: 4px solid #ffc107;
    background-color: rgba(255, 193, 7, 0.1);
}
```

## 最佳实践

### 1. 性能优化

- **懒加载**: 按需加载参与者数据
- **缓存策略**: 合理缓存参与者状态
- **批量操作**: 批量处理状态更新

### 2. 用户体验

- **即时反馈**: 提供即时的状态变更反馈
- **权限提示**: 清晰的权限限制提示
- **状态指示**: 明确的视觉状态指示

### 3. 数据一致性

- **状态同步**: 确保多用户间状态同步
- **冲突处理**: 处理并发状态更新冲突
- **事务性**: 保证状态更新的事务性

## 扩展指南

### 1. 添加新的参与者状态

```javascript
// 扩展状态定义
const CUSTOM_STATUSES = {
    'maybe': {
        name: 'Maybe',
        color: '#17a2b8',
        icon: 'fa-question',
        description: '可能参加'
    }
};
```

### 2. 自定义权限规则

```javascript
// 扩展权限检查
function customPermissionCheck(user, event, action) {
    // 自定义权限逻辑
    if (user.is_admin) {
        return true;
    }
    
    // 其他自定义规则
    return defaultPermissionCheck(user, event, action);
}
```

### 3. 添加新的视图模式

```javascript
// 注册新的参与者视图模式
registry.category("views").add("attendee_timeline", {
    type: "attendee_calendar",
    display_name: "Attendee Timeline",
    Controller: AttendeeTimelineController,
    Renderer: AttendeeTimelineRenderer
});
```

## 依赖关系

- **@web/views/calendar**: 基础日历视图
- **@calendar/views/ask_recurrence_update_policy**: 重复事件策略
- **@calendar/views/fields**: 参与者字段组件

## 版本历史

- **v1.0**: 基础参与者日历功能
- **v1.1**: 添加年视图支持
- **v1.2**: 增强权限系统
- **v1.3**: 优化性能和用户体验

---

*该模块为 Odoo Calendar 应用提供了完整的参与者视角日历功能，支持灵活的扩展和定制。*

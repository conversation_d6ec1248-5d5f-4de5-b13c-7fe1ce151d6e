# AttendeeCalendarRenderer - 参与者日历渲染器

## 概述

`attendee_calendar_renderer.js` 是 Odoo Calendar 模块的参与者日历渲染器，负责渲染参与者视角的日历视图。该模块包含21行代码，是一个功能专门的渲染器组件，专门用于配置参与者日历的视图渲染组件，具备多视图支持、组件映射、渲染器继承等特性，是参与者日历视图的核心渲染层。

## 文件信息
- **路径**: `/calendar/static/src/views/attendee_calendar/attendee_calendar_renderer.js`
- **行数**: 21
- **模块**: `@calendar/views/attendee_calendar/attendee_calendar_renderer`

## 依赖关系

```javascript
// 核心依赖
'@web/views/calendar/calendar_renderer'        // 日历渲染器基类
'@calendar/views/attendee_calendar/common/attendee_calendar_common_renderer' // 通用渲染器
'@calendar/views/attendee_calendar/year/attendee_calendar_year_renderer'     // 年视图渲染器
```

## 核心功能

### 1. 渲染器定义

```javascript
class AttendeeCalendarRenderer extends CalendarRenderer {
    static components = {
        ...CalendarRenderer.components,
        day: AttendeeCalendarCommonRenderer,
        week: AttendeeCalendarCommonRenderer,
        month: AttendeeCalendarCommonRenderer,
        year: AttendeeCalendarYearRenderer,
    };
}
```

**渲染器特性**:
- **继承扩展**: 继承标准日历渲染器
- **组件映射**: 映射不同视图的渲染组件
- **多视图支持**: 支持日、周、月、年视图
- **专用渲染**: 使用专门的参与者渲染器

### 2. 视图组件映射

```javascript
static components = {
    ...CalendarRenderer.components,
    day: AttendeeCalendarCommonRenderer,
    week: AttendeeCalendarCommonRenderer,
    month: AttendeeCalendarCommonRenderer,
    year: AttendeeCalendarYearRenderer,
};
```

**映射功能**:
- **基础继承**: 继承父类所有组件
- **日视图**: 使用通用参与者渲染器
- **周视图**: 使用通用参与者渲染器
- **月视图**: 使用通用参与者渲染器
- **年视图**: 使用专门的年视图渲染器

## 使用场景

### 1. 参与者日历渲染器管理器

```javascript
// 参与者日历渲染器管理器
class AttendeeCalendarRendererManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置渲染器配置
        this.rendererConfig = {
            enableMultiViewSupport: true,
            enableCustomRenderers: true,
            enableResponsiveDesign: true,
            enableThemeSupport: false,
            enableAnimations: true,
            defaultViewMode: 'month',
            supportedViews: ['day', 'week', 'month', 'year']
        };
        
        // 设置视图配置
        this.viewConfigs = new Map([
            ['day', {
                name: 'Day View',
                renderer: 'AttendeeCalendarCommonRenderer',
                timeScale: 'hour',
                displayFormat: 'HH:mm',
                showAllDay: true,
                showTimeSlots: true,
                minHeight: 600
            }],
            ['week', {
                name: 'Week View',
                renderer: 'AttendeeCalendarCommonRenderer',
                timeScale: 'hour',
                displayFormat: 'ddd DD',
                showAllDay: true,
                showTimeSlots: true,
                minHeight: 500
            }],
            ['month', {
                name: 'Month View',
                renderer: 'AttendeeCalendarCommonRenderer',
                timeScale: 'day',
                displayFormat: 'DD',
                showAllDay: false,
                showTimeSlots: false,
                minHeight: 400
            }],
            ['year', {
                name: 'Year View',
                renderer: 'AttendeeCalendarYearRenderer',
                timeScale: 'month',
                displayFormat: 'MMM',
                showAllDay: false,
                showTimeSlots: false,
                minHeight: 300
            }]
        ]);
        
        // 设置渲染器类型
        this.rendererTypes = new Map([
            ['common', {
                name: 'Common Renderer',
                class: 'AttendeeCalendarCommonRenderer',
                supportedViews: ['day', 'week', 'month'],
                features: ['attendee_display', 'status_colors', 'popover_support']
            }],
            ['year', {
                name: 'Year Renderer',
                class: 'AttendeeCalendarYearRenderer',
                supportedViews: ['year'],
                features: ['compact_display', 'month_overview', 'event_indicators']
            }]
        ]);
        
        // 设置渲染统计
        this.rendererStatistics = {
            totalRenders: 0,
            rendersByView: new Map(),
            rendersByRenderer: new Map(),
            averageRenderTime: 0,
            totalRenderTime: 0,
            errorCount: 0,
            componentCreations: 0
        };
        
        this.initializeRendererSystem();
    }
    
    // 初始化渲染器系统
    initializeRendererSystem() {
        // 创建增强的渲染器
        this.createEnhancedRenderer();
        
        // 设置组件系统
        this.setupComponentSystem();
        
        // 设置性能监控
        this.setupPerformanceMonitoring();
        
        // 设置主题系统
        this.setupThemeSystem();
    }
    
    // 创建增强的渲染器
    createEnhancedRenderer() {
        const originalRenderer = AttendeeCalendarRenderer;
        
        this.EnhancedAttendeeCalendarRenderer = class extends originalRenderer {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加性能监控
                this.addPerformanceMonitoring();
                
                // 添加主题支持
                this.addThemeSupport();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    currentView: 'month',
                    renderCount: 0,
                    lastRenderTime: null,
                    componentCache: new Map(),
                    themeClass: 'default'
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
                
                // 初始化渲染器
                this.initializeRenderer();
            }
            
            addEnhancedMethods() {
                // 增强的组件获取
                this.getEnhancedComponent = (viewType) => {
                    // 检查缓存
                    if (this.enhancedState.componentCache.has(viewType)) {
                        return this.enhancedState.componentCache.get(viewType);
                    }
                    
                    // 获取组件
                    const component = this.getViewComponent(viewType);
                    
                    // 缓存组件
                    this.enhancedState.componentCache.set(viewType, component);
                    
                    return component;
                };
                
                // 获取视图组件
                this.getViewComponent = (viewType) => {
                    const viewConfig = this.viewConfigs.get(viewType);
                    
                    if (!viewConfig) {
                        console.warn(`Unknown view type: ${viewType}`);
                        return this.constructor.components.month;
                    }
                    
                    return this.constructor.components[viewType];
                };
                
                // 渲染视图
                this.renderView = (viewType, props) => {
                    try {
                        // 记录渲染开始
                        this.recordRenderStart(viewType);
                        
                        // 获取组件
                        const Component = this.getEnhancedComponent(viewType);
                        
                        // 准备属性
                        const enhancedProps = this.prepareViewProps(viewType, props);
                        
                        // 渲染组件
                        const result = this.renderComponent(Component, enhancedProps);
                        
                        // 记录渲染完成
                        this.recordRenderComplete(viewType);
                        
                        return result;
                        
                    } catch (error) {
                        this.handleRenderError(viewType, error);
                        throw error;
                    }
                };
                
                // 准备视图属性
                this.prepareViewProps = (viewType, props) => {
                    const viewConfig = this.viewConfigs.get(viewType);
                    
                    return {
                        ...props,
                        viewType: viewType,
                        viewConfig: viewConfig,
                        themeClass: this.enhancedState.themeClass,
                        rendererConfig: this.rendererConfig
                    };
                };
                
                // 渲染组件
                this.renderComponent = (Component, props) => {
                    // 这里应该是实际的组件渲染逻辑
                    // 在真实环境中会使用OWL的渲染机制
                    return new Component(props);
                };
                
                // 切换视图
                this.switchView = (newViewType) => {
                    if (!this.viewConfigs.has(newViewType)) {
                        console.warn(`Unsupported view type: ${newViewType}`);
                        return;
                    }
                    
                    const oldView = this.enhancedState.currentView;
                    this.enhancedState.currentView = newViewType;
                    
                    // 触发视图切换事件
                    this.onViewSwitch(oldView, newViewType);
                };
                
                // 视图切换处理
                this.onViewSwitch = (oldView, newView) => {
                    console.log(`View switched from ${oldView} to ${newView}`);
                    
                    // 清理旧视图资源
                    this.cleanupViewResources(oldView);
                    
                    // 初始化新视图
                    this.initializeView(newView);
                };
                
                // 清理视图资源
                this.cleanupViewResources = (viewType) => {
                    // 清理特定视图的资源
                    console.log(`Cleaning up resources for ${viewType} view`);
                };
                
                // 初始化视图
                this.initializeView = (viewType) => {
                    const viewConfig = this.viewConfigs.get(viewType);
                    
                    if (viewConfig) {
                        console.log(`Initializing ${viewConfig.name}`);
                    }
                };
                
                // 获取支持的视图
                this.getSupportedViews = () => {
                    return Array.from(this.viewConfigs.keys());
                };
                
                // 检查视图支持
                this.isViewSupported = (viewType) => {
                    return this.viewConfigs.has(viewType);
                };
                
                // 获取当前视图配置
                this.getCurrentViewConfig = () => {
                    return this.viewConfigs.get(this.enhancedState.currentView);
                };
                
                // 初始化渲染器
                this.initializeRenderer = () => {
                    // 设置默认视图
                    this.enhancedState.currentView = this.rendererConfig.defaultViewMode;
                    
                    // 初始化组件缓存
                    this.preloadComponents();
                };
                
                // 预加载组件
                this.preloadComponents = () => {
                    for (const viewType of this.rendererConfig.supportedViews) {
                        this.getEnhancedComponent(viewType);
                    }
                };
                
                // 记录渲染开始
                this.recordRenderStart = (viewType) => {
                    this.enhancedState.lastRenderTime = Date.now();
                    this.enhancedState.renderCount++;
                    
                    // 更新统计
                    this.rendererStatistics.totalRenders++;
                    const viewCount = this.rendererStatistics.rendersByView.get(viewType) || 0;
                    this.rendererStatistics.rendersByView.set(viewType, viewCount + 1);
                };
                
                // 记录渲染完成
                this.recordRenderComplete = (viewType) => {
                    const renderTime = Date.now() - this.enhancedState.lastRenderTime;
                    this.rendererStatistics.totalRenderTime += renderTime;
                    this.updateAverageRenderTime();
                    
                    console.log(`${viewType} view rendered in ${renderTime}ms`);
                };
                
                // 更新平均渲染时间
                this.updateAverageRenderTime = () => {
                    if (this.rendererStatistics.totalRenders > 0) {
                        this.rendererStatistics.averageRenderTime = 
                            this.rendererStatistics.totalRenderTime / this.rendererStatistics.totalRenders;
                    }
                };
                
                // 处理渲染错误
                this.handleRenderError = (viewType, error) => {
                    this.rendererStatistics.errorCount++;
                    console.error(`Render error in ${viewType} view:`, error);
                };
                
                // 获取渲染器信息
                this.getRendererInfo = () => {
                    return {
                        currentView: this.enhancedState.currentView,
                        renderCount: this.enhancedState.renderCount,
                        supportedViews: this.getSupportedViews(),
                        cacheSize: this.enhancedState.componentCache.size,
                        statistics: this.rendererStatistics,
                        config: this.rendererConfig
                    };
                };
                
                // 清理缓存
                this.clearCache = () => {
                    this.enhancedState.componentCache.clear();
                };
                
                // 重置统计
                this.resetStatistics = () => {
                    this.rendererStatistics = {
                        totalRenders: 0,
                        rendersByView: new Map(),
                        rendersByRenderer: new Map(),
                        averageRenderTime: 0,
                        totalRenderTime: 0,
                        errorCount: 0,
                        componentCreations: 0
                    };
                };
            }
            
            addPerformanceMonitoring() {
                // 性能监控功能
                this.performanceMonitor = {
                    enabled: true,
                    trackRenderTime: true,
                    trackMemoryUsage: false,
                    logSlowRenders: true,
                    slowRenderThreshold: 100 // ms
                };
            }
            
            addThemeSupport() {
                // 主题支持功能
                this.themeSupport = {
                    enabled: this.rendererConfig.enableThemeSupport,
                    currentTheme: 'default',
                    availableThemes: ['default', 'dark', 'light', 'compact']
                };
            }
        };
    }
    
    // 设置组件系统
    setupComponentSystem() {
        this.componentConfig = {
            enabled: this.rendererConfig.enableCustomRenderers,
            cacheComponents: true,
            preloadComponents: true
        };
    }
    
    // 设置性能监控
    setupPerformanceMonitoring() {
        this.performanceConfig = {
            enabled: true,
            trackRenderTime: true,
            logSlowRenders: true,
            slowRenderThreshold: 100
        };
    }
    
    // 设置主题系统
    setupThemeSystem() {
        this.themeConfig = {
            enabled: this.rendererConfig.enableThemeSupport,
            defaultTheme: 'default',
            supportCustomThemes: false
        };
    }
    
    // 创建渲染器
    createRenderer(props) {
        return new this.EnhancedAttendeeCalendarRenderer(props);
    }
    
    // 注册视图配置
    registerViewConfig(viewType, config) {
        this.viewConfigs.set(viewType, config);
    }
    
    // 注册渲染器类型
    registerRendererType(name, config) {
        this.rendererTypes.set(name, config);
    }
    
    // 获取渲染统计
    getRendererStatistics() {
        return {
            ...this.rendererStatistics,
            errorRate: this.rendererStatistics.totalRenders > 0 ? 
                (this.rendererStatistics.errorCount / this.rendererStatistics.totalRenders) * 100 : 0,
            viewVariety: this.rendererStatistics.rendersByView.size,
            rendererVariety: this.rendererStatistics.rendersByRenderer.size,
            averageRenderTime: this.rendererStatistics.averageRenderTime
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理视图配置
        this.viewConfigs.clear();
        
        // 清理渲染器类型
        this.rendererTypes.clear();
        
        // 清理统计
        this.rendererStatistics.rendersByView.clear();
        this.rendererStatistics.rendersByRenderer.clear();
        
        // 重置统计
        this.rendererStatistics = {
            totalRenders: 0,
            rendersByView: new Map(),
            rendersByRenderer: new Map(),
            averageRenderTime: 0,
            totalRenderTime: 0,
            errorCount: 0,
            componentCreations: 0
        };
    }
}

// 使用示例
const rendererManager = new AttendeeCalendarRendererManager();

// 创建渲染器
const renderer = rendererManager.createRenderer({
    model: calendarModel,
    props: calendarProps
});

// 注册自定义视图配置
rendererManager.registerViewConfig('agenda', {
    name: 'Agenda View',
    renderer: 'AttendeeCalendarAgendaRenderer',
    timeScale: 'event',
    displayFormat: 'YYYY-MM-DD HH:mm',
    showAllDay: true,
    showTimeSlots: false,
    minHeight: 350
});

// 获取统计信息
const stats = rendererManager.getRendererStatistics();
console.log('Renderer statistics:', stats);
```

## 技术特点

### 1. 组件映射
- **视图分离**: 不同视图使用不同渲染器
- **组件复用**: 多个视图共享通用渲染器
- **专用渲染**: 年视图使用专门渲染器
- **继承机制**: 继承基础渲染器功能

### 2. 多视图支持
- **日视图**: 支持日视图渲染
- **周视图**: 支持周视图渲染
- **月视图**: 支持月视图渲染
- **年视图**: 支持年视图渲染

### 3. 渲染器架构
- **分层设计**: 分层的渲染器架构
- **组件化**: 组件化的渲染方式
- **可扩展**: 易于扩展新视图
- **配置化**: 支持配置化渲染

### 4. 性能优化
- **组件缓存**: 缓存渲染组件
- **懒加载**: 按需加载渲染器
- **性能监控**: 监控渲染性能
- **资源管理**: 管理渲染资源

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- **渲染策略**: 不同视图的渲染策略
- **组件策略**: 不同的组件选择策略
- **主题策略**: 不同的主题渲染策略

### 2. 工厂模式 (Factory Pattern)
- **组件工厂**: 创建不同的渲染组件
- **视图工厂**: 创建不同的视图
- **配置工厂**: 创建配置对象

### 3. 装饰器模式 (Decorator Pattern)
- **渲染装饰**: 装饰基础渲染功能
- **功能装饰**: 装饰渲染器功能
- **主题装饰**: 装饰主题功能

### 4. 组合模式 (Composite Pattern)
- **组件组合**: 组合不同的渲染组件
- **视图组合**: 组合不同的视图元素
- **功能组合**: 组合不同的功能

## 注意事项

1. **组件一致性**: 确保不同视图组件的一致性
2. **性能考虑**: 注意渲染性能的优化
3. **内存管理**: 管理组件的内存使用
4. **错误处理**: 处理渲染错误

## 扩展建议

1. **自定义视图**: 支持自定义视图类型
2. **主题系统**: 添加主题系统支持
3. **动画效果**: 添加视图切换动画
4. **响应式设计**: 支持响应式设计
5. **性能优化**: 进一步优化渲染性能

该参与者日历渲染器为Odoo Calendar模块提供了灵活的多视图渲染架构，通过组件映射和专用渲染器确保了不同视图的最佳显示效果。

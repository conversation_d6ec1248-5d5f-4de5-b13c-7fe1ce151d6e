/*************************************************************************************
*  Filepath: /calendar/static/src/views/attendee_calendar/attendee_calendar_view.js  *
*  Lines: 22                                                                         *
*************************************************************************************/
odoo.define('@calendar/views/attendee_calendar/attendee_calendar_view', ['@web/core/registry', '@web/views/calendar/calendar_view', '@calendar/views/attendee_calendar/attendee_calendar_controller', '@calendar/views/attendee_calendar/attendee_calendar_model', '@calendar/views/attendee_calendar/attendee_calendar_renderer'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { registry } = require("@web/core/registry");
const { calendarView } = require("@web/views/calendar/calendar_view");
const { AttendeeCalendarController } = require("@calendar/views/attendee_calendar/attendee_calendar_controller");
const { AttendeeCalendarModel } = require("@calendar/views/attendee_calendar/attendee_calendar_model");
const { AttendeeCalendarRenderer } = require("@calendar/views/attendee_calendar/attendee_calendar_renderer");

const attendeeCalendarView = __exports.attendeeCalendarView = {
    ...calendarView,
    Controller: AttendeeCalendarController,
    Model: AttendeeCalendarModel,
    Renderer: AttendeeCalendarRenderer,
};

registry.category("views").add("attendee_calendar", attendeeCalendarView);

return __exports;
});
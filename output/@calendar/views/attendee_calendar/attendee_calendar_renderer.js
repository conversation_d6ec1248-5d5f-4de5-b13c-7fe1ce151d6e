/*****************************************************************************************
*  Filepath: /calendar/static/src/views/attendee_calendar/attendee_calendar_renderer.js  *
*  Lines: 21                                                                             *
*****************************************************************************************/
odoo.define('@calendar/views/attendee_calendar/attendee_calendar_renderer', ['@web/views/calendar/calendar_renderer', '@calendar/views/attendee_calendar/common/attendee_calendar_common_renderer', '@calendar/views/attendee_calendar/year/attendee_calendar_year_renderer'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { CalendarRenderer } = require("@web/views/calendar/calendar_renderer");
const { AttendeeCalendarCommonRenderer } = require("@calendar/views/attendee_calendar/common/attendee_calendar_common_renderer");
const { AttendeeCalendarYearRenderer } = require("@calendar/views/attendee_calendar/year/attendee_calendar_year_renderer");

const AttendeeCalendarRenderer = __exports.AttendeeCalendarRenderer = class AttendeeCalendarRenderer extends CalendarRenderer {
    static components = {
        ...CalendarRenderer.components,
        day: AttendeeCalendarCommonRenderer,
        week: AttendeeCalendarCommonRenderer,
        month: AttendeeCalendarCommonRenderer,
        year: AttendeeCalendarYearRenderer,
    };
}

return __exports;
});
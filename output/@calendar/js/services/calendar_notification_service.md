# CalendarNotificationService - 日历通知服务

## 概述

`calendar_notification_service.js` 是 Odoo Calendar 模块的日历通知服务，负责处理日历事件的提醒通知功能。该模块包含108行代码，是一个功能完整的通知服务，专门用于管理日历事件的警报通知，具备总线订阅、通知显示、超时管理、用户交互等特性，是日历提醒系统的核心组件。

## 文件信息
- **路径**: `/calendar/static/src/js/services/calendar_notification_service.js`
- **行数**: 108
- **模块**: `@calendar/js/services/calendar_notification_service`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'                   // 翻译服务
'@web/core/browser/browser'                    // 浏览器服务
'@web/core/network/rpc'                        // RPC网络服务
'@web/core/registry'                           // 注册表
```

## 核心功能

### 1. 服务定义

```javascript
const calendarNotificationService = {
    dependencies: ["action", "bus_service", "notification"],

    start(env, { action, bus_service, notification }) {
        let calendarNotifTimeouts = {};
        let nextCalendarNotifTimeout = null;
        const displayedNotifications = new Set();

        bus_service.subscribe("calendar.alarm", (payload) => {
            displayCalendarNotification(payload);
        });
        bus_service.start();
    }
};
```

**服务特性**:
- **依赖注入**: 依赖动作、总线、通知服务
- **状态管理**: 管理通知超时和显示状态
- **总线订阅**: 订阅日历警报事件
- **服务启动**: 启动总线服务

### 2. 通知显示

```javascript
function displayCalendarNotification(notifications) {
    let lastNotifTimer = 0;

    // Clear previously set timeouts and destroy currently displayed calendar notifications
    browser.clearTimeout(nextCalendarNotifTimeout);
    Object.values(calendarNotifTimeouts).forEach((notif) => browser.clearTimeout(notif));
    calendarNotifTimeouts = {};

    // For each notification, set a timeout to display it
    notifications.forEach(function (notif) {
        const key = notif.event_id + "," + notif.alarm_id;
        if (displayedNotifications.has(key)) {
            return;
        }
        calendarNotifTimeouts[key] = browser.setTimeout(function () {
            // 显示通知逻辑
        }, notif.timer * 1000);
        lastNotifTimer = Math.max(lastNotifTimer, notif.timer);
    });
}
```

**显示功能**:
- **超时清理**: 清理之前设置的超时
- **重复检查**: 避免重复显示相同通知
- **定时显示**: 根据定时器延迟显示
- **批量处理**: 批量处理多个通知

### 3. 通知按钮

```javascript
buttons: [
    {
        name: _t("OK"),
        primary: true,
        onClick: async () => {
            await rpc("/calendar/notify_ack");
            notificationRemove();
        },
    },
    {
        name: _t("Details"),
        onClick: async () => {
            await action.doAction({
                type: "ir.actions.act_window",
                res_model: "calendar.event",
                res_id: notif.event_id,
                views: [[false, "form"]],
            });
            notificationRemove();
        },
    },
    {
        name: _t("Snooze"),
        onClick: () => {
            notificationRemove();
        },
    },
]
```

**按钮功能**:
- **确认按钮**: 确认通知并发送确认请求
- **详情按钮**: 打开事件详情表单
- **延迟按钮**: 延迟通知显示
- **国际化**: 支持按钮文本国际化

### 4. 下一轮通知

```javascript
async function getNextCalendarNotif() {
    try {
        const result = await rpc("/calendar/notify", {}, { silent: true });
        displayCalendarNotification(result);
    } catch (error) {
        if (!(error instanceof ConnectionLostError)) {
            throw error;
        }
    }
}
```

**获取功能**:
- **RPC调用**: 调用服务器获取下一轮通知
- **静默请求**: 使用静默模式避免错误提示
- **错误处理**: 处理连接丢失错误
- **递归显示**: 递归显示新的通知

## 使用场景

### 1. 日历通知管理器

```javascript
// 日历通知管理器
class CalendarNotificationManager {
    constructor() {
        this.setupManager();
    }

    setupManager() {
        // 设置通知配置
        this.notificationConfig = {
            enableBrowserNotifications: true,
            enableSoundAlerts: false,
            enablePopupNotifications: true,
            enableEmailNotifications: false,
            defaultSnoozeTime: 300000, // 5分钟
            maxNotificationCount: 10,
            notificationTimeout: 30000, // 30秒
            retryInterval: 60000 // 1分钟
        };

        // 设置通知类型
        this.notificationTypes = new Map([
            ['reminder', {
                name: 'Reminder',
                icon: 'fa-bell',
                color: 'warning',
                sound: 'notification.mp3',
                priority: 'normal'
            }],
            ['urgent', {
                name: 'Urgent',
                icon: 'fa-exclamation-triangle',
                color: 'danger',
                sound: 'urgent.mp3',
                priority: 'high'
            }],
            ['meeting', {
                name: 'Meeting',
                icon: 'fa-calendar',
                color: 'info',
                sound: 'meeting.mp3',
                priority: 'normal'
            }],
            ['deadline', {
                name: 'Deadline',
                icon: 'fa-clock',
                color: 'warning',
                sound: 'deadline.mp3',
                priority: 'high'
            }]
        ]);

        // 设置延迟选项
        this.snoozeOptions = new Map([
            ['5min', {
                name: '5 Minutes',
                duration: 5 * 60 * 1000,
                icon: 'fa-clock'
            }],
            ['15min', {
                name: '15 Minutes',
                duration: 15 * 60 * 1000,
                icon: 'fa-clock'
            }],
            ['30min', {
                name: '30 Minutes',
                duration: 30 * 60 * 1000,
                icon: 'fa-clock'
            }],
            ['1hour', {
                name: '1 Hour',
                duration: 60 * 60 * 1000,
                icon: 'fa-clock'
            }],
            ['custom', {
                name: 'Custom',
                duration: null,
                icon: 'fa-edit'
            }]
        ]);

        // 设置通知统计
        this.notificationStatistics = {
            totalNotifications: 0,
            displayedNotifications: 0,
            acknowledgedNotifications: 0,
            snoozedNotifications: 0,
            detailsViewed: 0,
            notificationsByType: new Map(),
            averageResponseTime: 0,
            totalResponseTime: 0
        };

        this.initializeNotificationSystem();
    }

    // 初始化通知系统
    initializeNotificationSystem() {
        // 创建增强的通知服务
        this.createEnhancedNotificationService();

        // 设置浏览器通知
        this.setupBrowserNotifications();

        // 设置声音系统
        this.setupSoundSystem();

        // 设置存储系统
        this.setupStorageSystem();
    }

    // 创建增强的通知服务
    createEnhancedNotificationService() {
        this.enhancedService = {
            dependencies: ["action", "bus_service", "notification"],

            start(env, { action, bus_service, notification }) {
                // 增强状态管理
                this.enhancedState = {
                    calendarNotifTimeouts: new Map(),
                    nextCalendarNotifTimeout: null,
                    displayedNotifications: new Set(),
                    snoozedNotifications: new Map(),
                    notificationQueue: [],
                    isProcessing: false
                };

                // 订阅总线事件
                bus_service.subscribe("calendar.alarm", (payload) => {
                    this.enhancedDisplayCalendarNotification(payload);
                });

                // 启动总线服务
                bus_service.start();

                // 设置定期检查
                this.setupPeriodicCheck();
            },

            // 增强的通知显示
            enhancedDisplayCalendarNotification(notifications) {
                try {
                    // 记录通知接收
                    this.recordNotificationReceived(notifications);

                    // 验证通知
                    const validNotifications = this.validateNotifications(notifications);

                    // 清理旧通知
                    this.cleanupOldNotifications();

                    // 处理通知队列
                    this.processNotificationQueue(validNotifications);

                } catch (error) {
                    console.error('Notification display error:', error);
                }
            },

            // 验证通知
            validateNotifications(notifications) {
                return notifications.filter(notif => {
                    // 检查必需字段
                    if (!notif.event_id || !notif.alarm_id) {
                        return false;
                    }

                    // 检查重复
                    const key = this.getNotificationKey(notif);
                    if (this.enhancedState.displayedNotifications.has(key)) {
                        return false;
                    }

                    // 检查延迟状态
                    if (this.enhancedState.snoozedNotifications.has(key)) {
                        const snoozeData = this.enhancedState.snoozedNotifications.get(key);
                        if (Date.now() < snoozeData.until) {
                            return false;
                        } else {
                            this.enhancedState.snoozedNotifications.delete(key);
                        }
                    }

                    return true;
                });
            },

            // 处理通知队列
            processNotificationQueue(notifications) {
                // 添加到队列
                this.enhancedState.notificationQueue.push(...notifications);

                // 如果没有在处理，开始处理
                if (!this.enhancedState.isProcessing) {
                    this.processNextNotification();
                }
            },

            // 处理下一个通知
            processNextNotification() {
                if (this.enhancedState.notificationQueue.length === 0) {
                    this.enhancedState.isProcessing = false;
                    return;
                }

                this.enhancedState.isProcessing = true;
                const notification = this.enhancedState.notificationQueue.shift();

                this.displaySingleNotification(notification);
            },

            // 显示单个通知
            displaySingleNotification(notif) {
                const key = this.getNotificationKey(notif);
                const notificationType = this.getNotificationType(notif);

                // 设置超时显示
                const timeout = browser.setTimeout(() => {
                    this.showNotificationUI(notif, notificationType);
                    this.enhancedState.calendarNotifTimeouts.delete(key);

                    // 处理下一个通知
                    setTimeout(() => this.processNextNotification(), 100);
                }, notif.timer * 1000);

                this.enhancedState.calendarNotifTimeouts.set(key, timeout);
            },

            // 显示通知UI
            showNotificationUI(notif, type) {
                const key = this.getNotificationKey(notif);
                const startTime = Date.now();

                // 创建通知
                const notificationRemove = notification.add(notif.message, {
                    title: notif.title,
                    type: type.color,
                    sticky: true,
                    onClose: () => {
                        this.enhancedState.displayedNotifications.delete(key);
                        this.recordNotificationClosed(key, startTime);
                    },
                    buttons: this.createNotificationButtons(notif, key, startTime, notificationRemove)
                });

                // 记录显示
                this.enhancedState.displayedNotifications.add(key);
                this.recordNotificationDisplayed(notif, type);

                // 播放声音
                if (this.notificationConfig.enableSoundAlerts) {
                    this.playNotificationSound(type);
                }

                // 显示浏览器通知
                if (this.notificationConfig.enableBrowserNotifications) {
                    this.showBrowserNotification(notif, type);
                }
            },

            // 创建通知按钮
            createNotificationButtons(notif, key, startTime, notificationRemove) {
                return [
                    {
                        name: _t("OK"),
                        primary: true,
                        onClick: async () => {
                            await this.acknowledgeNotification(notif);
                            this.recordNotificationAcknowledged(key, startTime);
                            notificationRemove();
                        },
                    },
                    {
                        name: _t("Details"),
                        onClick: async () => {
                            await this.showNotificationDetails(notif);
                            this.recordNotificationDetailsViewed(key, startTime);
                            notificationRemove();
                        },
                    },
                    {
                        name: _t("Snooze"),
                        onClick: () => {
                            this.showSnoozeOptions(notif, key, notificationRemove);
                        },
                    },
                ];
            }
        };
    }

    // 设置浏览器通知
    setupBrowserNotifications() {
        this.browserNotificationConfig = {
            enabled: this.notificationConfig.enableBrowserNotifications,
            requestPermission: true,
            icon: '/web/static/img/favicon.ico',
            badge: '/web/static/img/notification-badge.png'
        };

        // 请求浏览器通知权限
        if (this.browserNotificationConfig.enabled && 'Notification' in window) {
            if (Notification.permission === 'default') {
                Notification.requestPermission();
            }
        }
    }

    // 设置声音系统
    setupSoundSystem() {
        this.soundConfig = {
            enabled: this.notificationConfig.enableSoundAlerts,
            volume: 0.7,
            soundPath: '/calendar/static/sounds/'
        };
    }

    // 设置存储系统
    setupStorageSystem() {
        this.storageConfig = {
            enabled: true,
            storageKey: 'calendar_notifications',
            maxStorageSize: 1000
        };
    }

    // 创建通知服务
    createNotificationService() {
        return this.enhancedService;
    }

    // 获取通知统计
    getNotificationStatistics() {
        return {
            ...this.notificationStatistics,
            acknowledgeRate: this.notificationStatistics.displayedNotifications > 0 ?
                (this.notificationStatistics.acknowledgedNotifications / this.notificationStatistics.displayedNotifications) * 100 : 0,
            snoozeRate: this.notificationStatistics.displayedNotifications > 0 ?
                (this.notificationStatistics.snoozedNotifications / this.notificationStatistics.displayedNotifications) * 100 : 0,
            detailsViewRate: this.notificationStatistics.displayedNotifications > 0 ?
                (this.notificationStatistics.detailsViewed / this.notificationStatistics.displayedNotifications) * 100 : 0,
            typeVariety: this.notificationStatistics.notificationsByType.size,
            averageResponseTime: this.notificationStatistics.averageResponseTime
        };
    }
}
```

## 技术特点

### 1. 实时通知
- **总线集成**: 集成消息总线实时接收通知
- **即时显示**: 实时显示日历警报
- **状态同步**: 同步通知状态
- **自动更新**: 自动获取新通知

### 2. 超时管理
- **定时显示**: 根据定时器延迟显示通知
- **超时清理**: 自动清理过期超时
- **批量管理**: 批量管理多个超时
- **内存优化**: 优化内存使用

### 3. 用户交互
- **多按钮**: 提供确认、详情、延迟按钮
- **动作集成**: 集成动作服务执行操作
- **表单打开**: 打开事件详情表单
- **RPC调用**: 调用服务器确认通知

### 4. 错误处理
- **连接检查**: 检查网络连接状态
- **静默请求**: 使用静默模式避免错误提示
- **异常捕获**: 捕获和处理异常
- **优雅降级**: 网络错误时优雅降级

## 设计模式

### 1. 观察者模式 (Observer Pattern)
- **事件订阅**: 订阅日历警报事件
- **状态观察**: 观察通知状态变化
- **回调处理**: 处理事件回调

### 2. 策略模式 (Strategy Pattern)
- **显示策略**: 不同的通知显示策略
- **处理策略**: 不同的用户交互策略
- **错误策略**: 不同的错误处理策略

### 3. 单例模式 (Singleton Pattern)
- **服务单例**: 全局唯一的通知服务
- **状态管理**: 全局状态管理
- **资源共享**: 共享通知资源

### 4. 工厂模式 (Factory Pattern)
- **通知工厂**: 创建不同类型的通知
- **按钮工厂**: 创建通知按钮
- **配置工厂**: 创建配置对象

## 注意事项

1. **浏览器兼容**: 确保浏览器通知API兼容性
2. **内存管理**: 及时清理超时和通知状态
3. **用户体验**: 避免过度打扰用户
4. **网络处理**: 处理网络连接问题

## 扩展建议

1. **声音提醒**: 添加声音提醒功能
2. **自定义延迟**: 支持自定义延迟时间
3. **通知历史**: 记录通知历史
4. **批量操作**: 支持批量确认通知
5. **优先级**: 支持通知优先级管理

该日历通知服务为Odoo Calendar模块提供了完整的事件提醒解决方案，通过实时通知和用户友好的交互确保了用户不会错过重要的日历事件。
```
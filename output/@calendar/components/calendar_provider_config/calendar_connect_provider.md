# CalendarConnectProvider - 日历提供商连接组件

## 概述

`calendar_connect_provider.js` 是 Odoo Calendar 模块的日历提供商连接组件，负责处理外部日历提供商（如Google Calendar、Microsoft Calendar）的连接和同步配置。该模块包含83行代码，是一个功能完整的连接组件，专门用于激活外部日历同步功能，具备提供商配置、同步准备、认证处理、状态管理等特性，是外部日历集成的核心组件。

## 文件信息
- **路径**: `/calendar/static/src/components/calendar_provider_config/calendar_connect_provider.js`
- **行数**: 83
- **模块**: `@calendar/components/calendar_provider_config/calendar_connect_provider`

## 依赖关系

```javascript
// 核心依赖
'@web/core/network/rpc'                        // RPC网络服务
'@web/core/registry'                           // 注册表
'@web/core/user'                               // 用户服务
'@web/views/widgets/standard_widget_props'     // 标准组件属性
'@web/core/utils/hooks'                        // 工具钩子
'@odoo/owl'                                    // OWL框架
```

## 核心功能

### 1. 提供商配置

```javascript
const providerData = {
    google: {
        restart_sync_method: "restart_google_synchronization",
        sync_route: "/google_calendar/sync_data",
    },
    microsoft: {
        restart_sync_method: "restart_microsoft_synchronization",
        sync_route: "/microsoft_calendar/sync_data",
    },
};
```

**配置特性**:
- **Google集成**: Google Calendar同步配置
- **Microsoft集成**: Microsoft Calendar同步配置
- **方法映射**: 重启同步方法映射
- **路由配置**: 同步数据路由配置

### 2. 组件定义

```javascript
class CalendarConnectProvider extends Component {
    static props = {
        ...standardWidgetProps,
    };
    static template = "calendar.CalendarConnectProvider";

    setup() {
        super.setup();
        this.orm = useService("orm");
    }
}
```

**组件特性**:
- **标准属性**: 继承标准组件属性
- **模板绑定**: 绑定专用模板
- **服务注入**: 注入ORM服务
- **初始化**: 标准组件初始化

### 3. 连接处理

```javascript
async onConnect(ev) {
    ev.preventDefault();
    ev.stopImmediatePropagation();
    if (!(await this.props.record.save())) {
        return; // handled by view
    }
    await this.orm.call(
        this.props.record.resModel,
        "action_calendar_prepare_external_provider_sync",
        [this.props.record.resId]
    );
    // See google/microsoft_calendar for the origin of this shortened version
    const { restart_sync_method, sync_route } =
        providerData[this.props.record.data.external_calendar_provider];
    await this.orm.call("res.users", restart_sync_method, [[user.userId]]);
    const response = await rpc(sync_route, {
        model: "calendar.event",
        fromurl: window.location.href,
    });
    await this._beforeLeaveContext();
    if (response.status === "need_auth") {
        window.location.assign(response.url);
    } else if (response.status === "need_refresh") {
        window.location.reload();
    }
}
```

**连接功能**:
- **事件阻止**: 阻止默认事件和冒泡
- **记录保存**: 保存当前记录
- **同步准备**: 准备外部提供商同步
- **提供商识别**: 识别外部日历提供商
- **同步重启**: 重启用户同步
- **RPC调用**: 调用同步数据接口
- **响应处理**: 处理同步响应
- **页面跳转**: 根据状态进行页面操作

### 4. 上下文离开钩子

```javascript
async _beforeLeaveContext() {
    return Promise.resolve();
}
```

**钩子功能**:
- **扩展点**: 提供扩展点用于自定义处理
- **异步支持**: 支持异步操作
- **Promise返回**: 返回Promise对象
- **清理机会**: 提供清理资源的机会

## 使用场景

### 1. 日历提供商连接管理器

```javascript
// 日历提供商连接管理器
class CalendarProviderConnectionManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置连接配置
        this.connectionConfig = {
            enableGoogleCalendar: true,
            enableMicrosoftCalendar: true,
            enableOutlookCalendar: true,
            enableAppleCalendar: false,
            enableAutoSync: true,
            enableSyncValidation: true,
            syncInterval: 300000, // 5分钟
            maxRetryAttempts: 3,
            connectionTimeout: 30000 // 30秒
        };
        
        // 设置提供商配置
        this.providerConfigs = new Map([
            ['google', {
                name: 'Google Calendar',
                displayName: 'Google',
                icon: 'fab fa-google',
                color: '#4285f4',
                authType: 'oauth2',
                scopes: ['calendar.readonly', 'calendar.events'],
                restart_sync_method: 'restart_google_synchronization',
                sync_route: '/google_calendar/sync_data',
                auth_route: '/google_calendar/auth',
                disconnect_route: '/google_calendar/disconnect',
                status_route: '/google_calendar/status'
            }],
            ['microsoft', {
                name: 'Microsoft Calendar',
                displayName: 'Microsoft',
                icon: 'fab fa-microsoft',
                color: '#0078d4',
                authType: 'oauth2',
                scopes: ['calendars.read', 'calendars.readwrite'],
                restart_sync_method: 'restart_microsoft_synchronization',
                sync_route: '/microsoft_calendar/sync_data',
                auth_route: '/microsoft_calendar/auth',
                disconnect_route: '/microsoft_calendar/disconnect',
                status_route: '/microsoft_calendar/status'
            }],
            ['outlook', {
                name: 'Outlook Calendar',
                displayName: 'Outlook',
                icon: 'fab fa-microsoft',
                color: '#0078d4',
                authType: 'oauth2',
                scopes: ['calendars.read', 'calendars.readwrite'],
                restart_sync_method: 'restart_outlook_synchronization',
                sync_route: '/outlook_calendar/sync_data',
                auth_route: '/outlook_calendar/auth',
                disconnect_route: '/outlook_calendar/disconnect',
                status_route: '/outlook_calendar/status'
            }]
        ]);
        
        // 设置连接状态
        this.connectionStates = new Map([
            ['disconnected', {
                name: 'Disconnected',
                description: 'Not connected to external calendar',
                color: 'secondary',
                icon: 'fa-unlink',
                canConnect: true,
                canDisconnect: false,
                canSync: false
            }],
            ['connecting', {
                name: 'Connecting',
                description: 'Connecting to external calendar',
                color: 'warning',
                icon: 'fa-spinner fa-spin',
                canConnect: false,
                canDisconnect: false,
                canSync: false
            }],
            ['connected', {
                name: 'Connected',
                description: 'Connected to external calendar',
                color: 'success',
                icon: 'fa-link',
                canConnect: false,
                canDisconnect: true,
                canSync: true
            }],
            ['error', {
                name: 'Error',
                description: 'Connection error occurred',
                color: 'danger',
                icon: 'fa-exclamation-triangle',
                canConnect: true,
                canDisconnect: false,
                canSync: false
            }],
            ['syncing', {
                name: 'Syncing',
                description: 'Synchronizing calendar data',
                color: 'info',
                icon: 'fa-sync fa-spin',
                canConnect: false,
                canDisconnect: false,
                canSync: false
            }]
        ]);
        
        // 设置连接统计
        this.connectionStatistics = {
            totalConnections: 0,
            successfulConnections: 0,
            failedConnections: 0,
            connectionsByProvider: new Map(),
            syncOperations: 0,
            successfulSyncs: 0,
            failedSyncs: 0,
            averageConnectionTime: 0,
            totalConnectionTime: 0
        };
        
        this.initializeConnectionSystem();
    }
    
    // 初始化连接系统
    initializeConnectionSystem() {
        // 创建增强的连接组件
        this.createEnhancedConnectProvider();
        
        // 设置认证系统
        this.setupAuthenticationSystem();
        
        // 设置同步系统
        this.setupSynchronizationSystem();
        
        // 设置监控系统
        this.setupMonitoringSystem();
    }
    
    // 创建增强的连接组件
    createEnhancedConnectProvider() {
        const originalComponent = CalendarConnectProvider;
        
        this.EnhancedCalendarConnectProvider = class extends originalComponent {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加监控功能
                this.addMonitoringFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isConnecting: false,
                    connectionStartTime: null,
                    currentProvider: null,
                    connectionAttempts: 0,
                    lastError: null,
                    syncStatus: 'idle'
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
                
                // 初始化状态
                this.initializeConnectionState();
            }
            
            addEnhancedMethods() {
                // 增强的连接处理
                this.enhancedOnConnect = async (ev) => {
                    try {
                        // 记录连接开始
                        this.recordConnectionStart();
                        
                        // 验证连接条件
                        if (!this.validateConnectionConditions()) {
                            return;
                        }
                        
                        // 阻止默认行为
                        ev.preventDefault();
                        ev.stopImmediatePropagation();
                        
                        // 设置连接状态
                        this.setConnectionState('connecting');
                        
                        // 保存记录
                        if (!(await this.saveRecord())) {
                            this.setConnectionState('error');
                            return;
                        }
                        
                        // 准备同步
                        await this.prepareSync();
                        
                        // 获取提供商配置
                        const providerConfig = this.getProviderConfig();
                        
                        // 重启同步
                        await this.restartSync(providerConfig);
                        
                        // 执行同步
                        const response = await this.executeSync(providerConfig);
                        
                        // 处理响应
                        await this.handleSyncResponse(response);
                        
                        // 记录连接成功
                        this.recordConnectionSuccess();
                        
                    } catch (error) {
                        // 记录连接失败
                        this.recordConnectionFailure(error);
                        
                        // 设置错误状态
                        this.setConnectionState('error');
                        
                        // 显示错误
                        this.showConnectionError(error);
                    }
                };
                
                // 验证连接条件
                this.validateConnectionConditions = () => {
                    // 检查是否正在连接
                    if (this.enhancedState.isConnecting) {
                        this.showConnectionWarning('Connection already in progress');
                        return false;
                    }
                    
                    // 检查连接尝试次数
                    if (this.enhancedState.connectionAttempts >= this.connectionConfig.maxRetryAttempts) {
                        this.showConnectionError('Maximum connection attempts exceeded');
                        return false;
                    }
                    
                    // 检查提供商配置
                    const provider = this.props.record.data.external_calendar_provider;
                    if (!this.providerConfigs.has(provider)) {
                        this.showConnectionError('Unsupported calendar provider');
                        return false;
                    }
                    
                    return true;
                };
                
                // 保存记录
                this.saveRecord = async () => {
                    try {
                        return await this.props.record.save();
                    } catch (error) {
                        console.error('Failed to save record:', error);
                        this.showConnectionError('Failed to save configuration');
                        return false;
                    }
                };
                
                // 准备同步
                this.prepareSync = async () => {
                    await this.orm.call(
                        this.props.record.resModel,
                        "action_calendar_prepare_external_provider_sync",
                        [this.props.record.resId]
                    );
                };
                
                // 获取提供商配置
                this.getProviderConfig = () => {
                    const provider = this.props.record.data.external_calendar_provider;
                    this.enhancedState.currentProvider = provider;
                    return this.providerConfigs.get(provider);
                };
                
                // 重启同步
                this.restartSync = async (config) => {
                    await this.orm.call("res.users", config.restart_sync_method, [[user.userId]]);
                };
                
                // 执行同步
                this.executeSync = async (config) => {
                    const response = await rpc(config.sync_route, {
                        model: "calendar.event",
                        fromurl: window.location.href,
                        timeout: this.connectionConfig.connectionTimeout
                    });
                    
                    return response;
                };
                
                // 处理同步响应
                this.handleSyncResponse = async (response) => {
                    await this._beforeLeaveContext();
                    
                    if (response.status === "need_auth") {
                        this.setConnectionState('connecting');
                        window.location.assign(response.url);
                    } else if (response.status === "need_refresh") {
                        this.setConnectionState('connected');
                        window.location.reload();
                    } else if (response.status === "success") {
                        this.setConnectionState('connected');
                        this.showConnectionSuccess();
                    } else {
                        throw new Error(`Unexpected response status: ${response.status}`);
                    }
                };
                
                // 设置连接状态
                this.setConnectionState = (state) => {
                    const stateConfig = this.connectionStates.get(state);
                    
                    if (stateConfig) {
                        this.enhancedState.isConnecting = (state === 'connecting');
                        this.enhancedState.syncStatus = state;
                        
                        // 更新UI状态
                        this.updateConnectionUI(stateConfig);
                    }
                };
                
                // 更新连接UI
                this.updateConnectionUI = (stateConfig) => {
                    // 可以在这里更新UI元素
                    console.log(`Connection state: ${stateConfig.name}`);
                };
                
                // 记录连接开始
                this.recordConnectionStart = () => {
                    this.enhancedState.connectionStartTime = Date.now();
                    this.enhancedState.connectionAttempts++;
                    this.connectionStatistics.totalConnections++;
                };
                
                // 记录连接成功
                this.recordConnectionSuccess = () => {
                    const connectionTime = Date.now() - this.enhancedState.connectionStartTime;
                    this.connectionStatistics.successfulConnections++;
                    this.connectionStatistics.totalConnectionTime += connectionTime;
                    this.updateAverageConnectionTime();
                    
                    // 记录提供商统计
                    const provider = this.enhancedState.currentProvider;
                    const count = this.connectionStatistics.connectionsByProvider.get(provider) || 0;
                    this.connectionStatistics.connectionsByProvider.set(provider, count + 1);
                };
                
                // 记录连接失败
                this.recordConnectionFailure = (error) => {
                    this.connectionStatistics.failedConnections++;
                    this.enhancedState.lastError = error.message;
                };
                
                // 更新平均连接时间
                this.updateAverageConnectionTime = () => {
                    if (this.connectionStatistics.successfulConnections > 0) {
                        this.connectionStatistics.averageConnectionTime = 
                            this.connectionStatistics.totalConnectionTime / this.connectionStatistics.successfulConnections;
                    }
                };
                
                // 初始化连接状态
                this.initializeConnectionState = () => {
                    const provider = this.props.record.data.external_calendar_provider;
                    this.enhancedState.currentProvider = provider;
                    
                    // 检查当前连接状态
                    this.checkCurrentConnectionStatus();
                };
                
                // 检查当前连接状态
                this.checkCurrentConnectionStatus = async () => {
                    try {
                        const provider = this.enhancedState.currentProvider;
                        const config = this.providerConfigs.get(provider);
                        
                        if (config && config.status_route) {
                            const status = await rpc(config.status_route);
                            this.setConnectionState(status.connected ? 'connected' : 'disconnected');
                        }
                    } catch (error) {
                        console.error('Failed to check connection status:', error);
                        this.setConnectionState('disconnected');
                    }
                };
                
                // 显示连接成功
                this.showConnectionSuccess = () => {
                    const notification = useService("notification");
                    notification.add(
                        'Calendar provider connected successfully',
                        { type: 'success' }
                    );
                };
                
                // 显示连接警告
                this.showConnectionWarning = (message) => {
                    const notification = useService("notification");
                    notification.add(message, { type: 'warning' });
                };
                
                // 显示连接错误
                this.showConnectionError = (error) => {
                    const notification = useService("notification");
                    const message = typeof error === 'string' ? error : error.message;
                    notification.add(
                        message || 'Failed to connect calendar provider',
                        { type: 'danger' }
                    );
                };
                
                // 获取连接信息
                this.getConnectionInfo = () => {
                    return {
                        currentProvider: this.enhancedState.currentProvider,
                        isConnecting: this.enhancedState.isConnecting,
                        syncStatus: this.enhancedState.syncStatus,
                        connectionAttempts: this.enhancedState.connectionAttempts,
                        lastError: this.enhancedState.lastError,
                        providerConfig: this.getProviderConfig(),
                        statistics: this.connectionStatistics
                    };
                };
                
                // 断开连接
                this.disconnect = async () => {
                    try {
                        const config = this.getProviderConfig();
                        if (config && config.disconnect_route) {
                            await rpc(config.disconnect_route);
                            this.setConnectionState('disconnected');
                            this.showConnectionSuccess('Calendar provider disconnected');
                        }
                    } catch (error) {
                        this.showConnectionError('Failed to disconnect calendar provider');
                    }
                };
                
                // 手动同步
                this.manualSync = async () => {
                    try {
                        this.setConnectionState('syncing');
                        const config = this.getProviderConfig();
                        const response = await this.executeSync(config);
                        await this.handleSyncResponse(response);
                        this.recordSyncSuccess();
                    } catch (error) {
                        this.recordSyncFailure(error);
                        this.setConnectionState('error');
                        this.showConnectionError('Sync failed');
                    }
                };
                
                // 记录同步成功
                this.recordSyncSuccess = () => {
                    this.connectionStatistics.syncOperations++;
                    this.connectionStatistics.successfulSyncs++;
                };
                
                // 记录同步失败
                this.recordSyncFailure = (error) => {
                    this.connectionStatistics.syncOperations++;
                    this.connectionStatistics.failedSyncs++;
                    console.error('Sync failed:', error);
                };
            }
            
            addMonitoringFeatures() {
                // 监控功能
                this.monitoringManager = {
                    enabled: this.connectionConfig.enableSyncValidation,
                    checkStatus: () => this.checkCurrentConnectionStatus(),
                    getInfo: () => this.getConnectionInfo()
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.connectionConfig.enableSyncValidation,
                    validate: () => this.validateConnectionConditions(),
                    checkProvider: (provider) => this.providerConfigs.has(provider)
                };
            }
            
            // 重写原始方法
            async onConnect(ev) {
                return await this.enhancedOnConnect(ev);
            }
        };
    }
    
    // 设置认证系统
    setupAuthenticationSystem() {
        this.authConfig = {
            enabled: true,
            authTimeout: this.connectionConfig.connectionTimeout,
            retryAttempts: this.connectionConfig.maxRetryAttempts
        };
    }
    
    // 设置同步系统
    setupSynchronizationSystem() {
        this.syncConfig = {
            enabled: this.connectionConfig.enableAutoSync,
            interval: this.connectionConfig.syncInterval,
            validateSync: this.connectionConfig.enableSyncValidation
        };
    }
    
    // 设置监控系统
    setupMonitoringSystem() {
        this.monitoringConfig = {
            enabled: true,
            trackConnections: true,
            trackSyncs: true,
            trackErrors: true
        };
    }
    
    // 创建连接组件
    createConnectProvider(props) {
        return new this.EnhancedCalendarConnectProvider(props);
    }
    
    // 注册提供商配置
    registerProviderConfig(name, config) {
        this.providerConfigs.set(name, config);
    }
    
    // 获取连接统计
    getConnectionStatistics() {
        return {
            ...this.connectionStatistics,
            connectionSuccessRate: this.connectionStatistics.totalConnections > 0 ? 
                (this.connectionStatistics.successfulConnections / this.connectionStatistics.totalConnections) * 100 : 0,
            syncSuccessRate: this.connectionStatistics.syncOperations > 0 ? 
                (this.connectionStatistics.successfulSyncs / this.connectionStatistics.syncOperations) * 100 : 0,
            providerVariety: this.connectionStatistics.connectionsByProvider.size,
            averageConnectionTime: this.connectionStatistics.averageConnectionTime
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理提供商配置
        this.providerConfigs.clear();
        
        // 清理连接状态
        this.connectionStates.clear();
        
        // 清理统计
        this.connectionStatistics.connectionsByProvider.clear();
        
        // 重置统计
        this.connectionStatistics = {
            totalConnections: 0,
            successfulConnections: 0,
            failedConnections: 0,
            connectionsByProvider: new Map(),
            syncOperations: 0,
            successfulSyncs: 0,
            failedSyncs: 0,
            averageConnectionTime: 0,
            totalConnectionTime: 0
        };
    }
}

// 使用示例
const connectionManager = new CalendarProviderConnectionManager();

// 创建连接组件
const connectProvider = connectionManager.createConnectProvider({
    record: recordObject
});

// 注册自定义提供商
connectionManager.registerProviderConfig('apple', {
    name: 'Apple Calendar',
    displayName: 'Apple',
    icon: 'fab fa-apple',
    color: '#000000',
    authType: 'oauth2',
    scopes: ['calendar.read', 'calendar.write'],
    restart_sync_method: 'restart_apple_synchronization',
    sync_route: '/apple_calendar/sync_data',
    auth_route: '/apple_calendar/auth',
    disconnect_route: '/apple_calendar/disconnect',
    status_route: '/apple_calendar/status'
});

// 获取统计信息
const stats = connectionManager.getConnectionStatistics();
console.log('Connection statistics:', stats);
```

## 技术特点

### 1. 多提供商支持
- **Google集成**: 支持Google Calendar集成
- **Microsoft集成**: 支持Microsoft Calendar集成
- **配置映射**: 提供商配置映射
- **扩展性**: 易于添加新的提供商

### 2. 异步连接流程
- **记录保存**: 先保存配置记录
- **同步准备**: 准备外部同步
- **认证处理**: 处理OAuth认证
- **状态管理**: 管理连接状态

### 3. 响应处理
- **认证重定向**: 需要认证时重定向
- **页面刷新**: 需要刷新时重新加载
- **错误处理**: 完善的错误处理机制
- **状态反馈**: 提供状态反馈

### 4. 扩展机制
- **钩子方法**: 提供扩展钩子
- **异步支持**: 支持异步扩展
- **清理机会**: 提供资源清理机会
- **自定义处理**: 支持自定义处理逻辑

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- **提供商策略**: 不同提供商的连接策略
- **认证策略**: 不同的认证策略
- **同步策略**: 不同的同步策略

### 2. 工厂模式 (Factory Pattern)
- **组件工厂**: 创建连接组件
- **配置工厂**: 创建提供商配置
- **请求工厂**: 创建网络请求

### 3. 状态模式 (State Pattern)
- **连接状态**: 不同的连接状态
- **同步状态**: 不同的同步状态
- **认证状态**: 不同的认证状态

### 4. 观察者模式 (Observer Pattern)
- **状态观察**: 观察连接状态变化
- **事件观察**: 观察用户交互事件
- **响应观察**: 观察服务器响应

## 注意事项

1. **安全性**: 确保认证信息的安全性
2. **错误处理**: 处理网络和认证错误
3. **用户体验**: 提供清晰的连接状态反馈
4. **超时处理**: 设置合理的连接超时

## 扩展建议

1. **更多提供商**: 支持更多日历提供商
2. **批量连接**: 支持批量连接多个账户
3. **连接测试**: 添加连接测试功能
4. **同步配置**: 提供详细的同步配置选项
5. **离线支持**: 支持离线配置保存

该日历提供商连接组件为Odoo Calendar模块提供了完整的外部日历集成解决方案，通过标准化的连接流程和灵活的配置机制确保了与各种外部日历服务的无缝集成。

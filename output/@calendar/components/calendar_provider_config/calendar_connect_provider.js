/****************************************************************************************************
*  Filepath: /calendar/static/src/components/calendar_provider_config/calendar_connect_provider.js  *
*  Lines: 83                                                                                        *
****************************************************************************************************/
odoo.define('@calendar/components/calendar_provider_config/calendar_connect_provider', ['@web/core/network/rpc', '@web/core/registry', '@web/core/user', '@web/views/widgets/standard_widget_props', '@web/core/utils/hooks', '@odoo/owl'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { rpc } = require("@web/core/network/rpc");
const { registry } = require("@web/core/registry");
const { user } = require("@web/core/user");
const { standardWidgetProps } = require("@web/views/widgets/standard_widget_props");
const { useService } = require("@web/core/utils/hooks");
const { Component } = require("@odoo/owl");

const providerData = {
    google: {
        restart_sync_method: "restart_google_synchronization",
        sync_route: "/google_calendar/sync_data",
    },
    microsoft: {
        restart_sync_method: "restart_microsoft_synchronization",
        sync_route: "/microsoft_calendar/sync_data",
    },
};

const CalendarConnectProvider = __exports.CalendarConnectProvider = class CalendarConnectProvider extends Component {
    static props = {
        ...standardWidgetProps,
    };
    static template = "calendar.CalendarConnectProvider";

    setup() {
        super.setup();
        this.orm = useService("orm");
    }

    /**
     * Activate the external sync for the first time, after installing the
     * relevant submodule if necessary.
     *
     * @private
     */
    async onConnect(ev) {
        ev.preventDefault();
        ev.stopImmediatePropagation();
        if (!(await this.props.record.save())) {
            return; // handled by view
        }
        await this.orm.call(
            this.props.record.resModel,
            "action_calendar_prepare_external_provider_sync",
            [this.props.record.resId]
        );
        // See google/microsoft_calendar for the origin of this shortened version
        const { restart_sync_method, sync_route } =
            providerData[this.props.record.data.external_calendar_provider];
        await this.orm.call("res.users", restart_sync_method, [[user.userId]]);
        const response = await rpc(sync_route, {
            model: "calendar.event",
            fromurl: window.location.href,
        });
        await this._beforeLeaveContext();
        if (response.status === "need_auth") {
            window.location.assign(response.url);
        } else if (response.status === "need_refresh") {
            window.location.reload();
        }
    }
    /**
     * Hook to perform additional work before redirecting to external url or reloading.
     *
     * @private
     */
    async _beforeLeaveContext() {
        return Promise.resolve();
    }
}

const calendarConnectProvider = {
    component: CalendarConnectProvider,
};
registry.category("view_widgets").add("calendar_connect_provider", calendarConnectProvider);

return __exports;
});
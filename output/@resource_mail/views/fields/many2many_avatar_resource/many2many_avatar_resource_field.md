# Many2Many Avatar Resource Field - 多对多头像资源字段

## 概述

`many2many_avatar_resource_field.js` 是 Odoo Resource Mail 模块的多对多头像资源字段组件，专门用于在表单和列表视图中显示资源的头像标签。该字段基于Odoo的标准多对多头像用户字段，通过扩展功能支持资源模型的特定显示和交互，为资源管理提供了直观的多选界面和丰富的用户体验。

## 文件信息
- **路径**: `/resource_mail/static/src/views/fields/many2many_avatar_resource/many2many_avatar_resource_field.js`
- **行数**: 114
- **模块**: `@resource_mail/views/fields/many2many_avatar_resource/many2many_avatar_resource_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                                                    // 注册表
'@web/core/popover/popover_hook'                                       // 弹出框钩子
'@mail/views/web/fields/many2many_avatar_user_field/many2many_avatar_user_field'  // 多对多头像用户字段
'@web/views/fields/relational_utils'                                  // 关系字段工具
'@resource_mail/components/avatar_card_resource/avatar_card_resource_popover'  // 资源头像卡片弹出框
'@web/core/domain'                                                     // 域工具
```

## 核心功能

### 1. 自动完成组件

```javascript
class AvatarResourceMany2XAutocomplete extends AvatarMany2XAutocomplete {
    get optionsSource() {
        return {
            ...super.optionsSource,
            optionTemplate: "resource_mail.AvatarResourceMany2XAutocomplete",
        };
    }

    search(request) {
        return this.orm.call(
            this.props.resModel,
            "search_read",
            [this.getDomain(request), ["id", "display_name", "resource_type"]],
            {
                context: this.props.context,
                limit: this.props.searchLimit + 1,
            }
        );
    }
```

**自动完成功能**:
- **模板定制**: 使用专门的资源自动完成模板
- **搜索增强**: 扩展搜索功能支持资源类型
- **字段扩展**: 添加resource_type字段到搜索结果
- **上下文支持**: 支持上下文相关的搜索

### 2. 域构建

```javascript
getDomain(request) {
    return Domain.and([[["name", "ilike", request]], this.props.getDomain()]).toList(
        this.props.context
    );
}
```

**域构建功能**:
- **名称搜索**: 基于名称的模糊搜索
- **域合并**: 合并用户提供的域条件
- **上下文处理**: 考虑上下文的域转换
- **灵活过滤**: 支持复杂的过滤条件

### 3. 记录映射

```javascript
mapRecordToOption(result) {
    return {
        resModel: this.props.resModel,
        value: result.id,
        resourceType: result.resource_type,
        label: result.display_name,
    };
}
```

**映射功能**:
- **模型信息**: 保留资源模型信息
- **值映射**: 映射记录ID到选项值
- **类型标识**: 添加资源类型标识
- **显示标签**: 使用display_name作为显示标签

### 4. 标签列表组件

```javascript
class Many2ManyAvatarResourceTagsList extends Many2ManyAvatarUserTagsList {
    static template = "resource_mail.Many2ManyAvatarResourceTagsList";
}
```

**标签列表功能**:
- **模板定制**: 使用专门的资源标签列表模板
- **继承扩展**: 继承用户标签列表的所有功能
- **资源适配**: 适配资源模型的显示需求

### 5. 主字段组件

```javascript
const Many2ManyAvatarResourceField = __exports.Many2ManyAvatarResourceField = class Many2ManyAvatarResourceField extends Many2ManyTagsAvatarUserField {
    setup() {
        super.setup(...arguments);
        if (this.relation == "resource.resource") {
            this.avatarCard = usePopover(AvatarCardResourcePopover);
        }
    }

    static components = {
        ...super.components,
        Many2XAutocomplete: AvatarResourceMany2XAutocomplete,
        TagsList: Many2ManyAvatarResourceTagsList,
    };
```

**主字段功能**:
- **条件弹出框**: 仅为resource.resource关系启用头像卡片
- **组件替换**: 使用自定义的自动完成和标签列表组件
- **继承扩展**: 继承多对多头像用户字段的所有功能

### 6. 头像卡片显示

```javascript
displayAvatarCard(record) {
    return !this.env.isSmall && this.relation === "resource.resource" && record.data.resource_type === "user";
}
```

**显示控制功能**:
- **屏幕尺寸**: 在小屏幕上不显示头像卡片
- **关系检查**: 仅为resource.resource关系显示
- **类型过滤**: 仅为用户类型的资源显示头像卡片
- **条件显示**: 多重条件确保合适的显示时机

### 7. 标签属性配置

```javascript
getTagProps(record) {
    return {
        ...super.getTagProps(...arguments),
        icon: record.data.resource_type === "user" ? null : "fa-wrench",
        img: record.data.resource_type === "user"
            ? `/web/image/${this.relation}/${record.resId}/avatar_128`
            : null,
    };
}
```

**标签属性功能**:
- **图标区分**: 为非用户资源显示工具图标
- **头像显示**: 为用户资源显示头像图片
- **类型判断**: 根据资源类型选择不同的显示方式
- **路径构建**: 动态构建头像图片路径

### 8. 字段注册

```javascript
const many2ManyAvatarResourceField = __exports.many2ManyAvatarResourceField = {
    ...many2ManyTagsAvatarUserField,
    component: Many2ManyAvatarResourceField,
    additionalClasses: ["o_field_many2many_tags_avatar"],
    relatedFields: (fieldInfo) => {
        return [
            ...many2ManyTagsAvatarUserField.relatedFields(fieldInfo),
            {
                name: "resource_type",
                type: "selection",
            },
        ];
    },
};

registry.category("fields").add("many2many_avatar_resource", many2ManyAvatarResourceField);
```

**注册功能**:
- **配置继承**: 继承用户头像字段的所有配置
- **组件绑定**: 绑定自定义的资源字段组件
- **样式类**: 添加必要的CSS类
- **关联字段**: 添加resource_type作为关联字段
- **字段注册**: 注册到字段注册表

## 使用场景

### 1. 多对多头像资源字段增强

```javascript
// 多对多头像资源字段增强功能
const Many2ManyAvatarResourceFieldEnhancer = {
    enhanceMany2ManyAvatarResourceField: () => {
        // 增强的自动完成组件
        class EnhancedAvatarResourceMany2XAutocomplete extends AvatarResourceMany2XAutocomplete {
            get optionsSource() {
                return {
                    ...super.optionsSource,
                    optionTemplate: "resource_mail.EnhancedAvatarResourceMany2XAutocomplete",
                    placeholder: _t('Search resources...'),
                };
            }

            // 增强的搜索功能
            async search(request) {
                const baseResults = await super.search(request);
                
                // 添加最近使用的资源
                if (request.length === 0) {
                    const recentResources = await this.getRecentResources();
                    return [...recentResources, ...baseResults];
                }

                // 添加智能建议
                const suggestions = await this.getSmartSuggestions(request);
                return [...baseResults, ...suggestions];
            }

            // 获取最近使用的资源
            async getRecentResources() {
                try {
                    return await this.orm.call(
                        this.props.resModel,
                        'get_recent_resources',
                        [],
                        { context: this.props.context, limit: 5 }
                    );
                } catch (error) {
                    console.warn('获取最近资源失败:', error);
                    return [];
                }
            }

            // 获取智能建议
            async getSmartSuggestions(request) {
                try {
                    return await this.orm.call(
                        this.props.resModel,
                        'get_smart_suggestions',
                        [request],
                        { context: this.props.context, limit: 3 }
                    );
                } catch (error) {
                    console.warn('获取智能建议失败:', error);
                    return [];
                }
            }

            // 增强的域构建
            getDomain(request) {
                let domain = super.getDomain(request);

                // 添加资源类型过滤
                if (this.props.resourceTypes) {
                    domain = Domain.and([
                        domain,
                        [['resource_type', 'in', this.props.resourceTypes]]
                    ]).toList(this.props.context);
                }

                // 添加可用性过滤
                if (this.props.onlyAvailable) {
                    domain = Domain.and([
                        domain,
                        [['active', '=', true]]
                    ]).toList(this.props.context);
                }

                return domain;
            }

            // 增强的记录映射
            mapRecordToOption(result) {
                const option = super.mapRecordToOption(result);
                
                return {
                    ...option,
                    avatar: result.avatar_128,
                    email: result.email,
                    phone: result.phone,
                    department: result.department_id?.[1],
                    isAvailable: result.active && !result.is_busy,
                    lastActivity: result.last_activity_date,
                    skills: result.skill_ids || []
                };
            }
        }

        // 增强的标签列表组件
        class EnhancedMany2ManyAvatarResourceTagsList extends Many2ManyAvatarResourceTagsList {
            static template = "resource_mail.EnhancedMany2ManyAvatarResourceTagsList";

            // 获取标签的工具提示
            getTagTooltip(record) {
                const parts = [record.data.display_name];
                
                if (record.data.email) {
                    parts.push(`Email: ${record.data.email}`);
                }
                
                if (record.data.phone) {
                    parts.push(`Phone: ${record.data.phone}`);
                }
                
                if (record.data.department_id) {
                    parts.push(`Department: ${record.data.department_id[1]}`);
                }

                return parts.join('\n');
            }

            // 获取标签状态
            getTagStatus(record) {
                if (!record.data.active) return 'inactive';
                if (record.data.is_busy) return 'busy';
                if (record.data.is_available) return 'available';
                return 'unknown';
            }

            // 获取状态颜色
            getStatusColor(status) {
                const colors = {
                    'available': 'success',
                    'busy': 'warning',
                    'inactive': 'danger',
                    'unknown': 'secondary'
                };
                return colors[status] || 'secondary';
            }
        }

        // 增强的主字段组件
        class EnhancedMany2ManyAvatarResourceField extends Many2ManyAvatarResourceField {
            static props = {
                ...Many2ManyAvatarResourceField.props,
                resourceTypes: { type: Array, optional: true },
                onlyAvailable: { type: Boolean, optional: true },
                enableQuickActions: { type: Boolean, optional: true },
                enableStatusIndicator: { type: Boolean, optional: true },
                enableBulkActions: { type: Boolean, optional: true },
                maxDisplayTags: { type: Number, optional: true },
                enableDragDrop: { type: Boolean, optional: true },
                enableGrouping: { type: Boolean, optional: true }
            };

            static defaultProps = {
                ...Many2ManyAvatarResourceField.defaultProps,
                resourceTypes: null,
                onlyAvailable: false,
                enableQuickActions: true,
                enableStatusIndicator: true,
                enableBulkActions: false,
                maxDisplayTags: 10,
                enableDragDrop: false,
                enableGrouping: false
            };

            static components = {
                ...Many2ManyAvatarResourceField.components,
                Many2XAutocomplete: EnhancedAvatarResourceMany2XAutocomplete,
                TagsList: EnhancedMany2ManyAvatarResourceTagsList,
            };

            setup() {
                super.setup();

                // 增强的状态
                this.enhancedState = useState({
                    selectedTags: new Set(),
                    groupedTags: new Map(),
                    draggedTag: null,
                    showAllTags: false
                });

                // 批量操作服务
                if (this.props.enableBulkActions) {
                    this.notification = useService("notification");
                }
            }

            // 增强的标签属性
            getTagProps(record) {
                const baseProps = super.getTagProps(record);
                
                return {
                    ...baseProps,
                    tooltip: this.getTagTooltip(record),
                    status: this.getTagStatus(record),
                    statusColor: this.getStatusColor(this.getTagStatus(record)),
                    enableQuickActions: this.props.enableQuickActions,
                    enableStatusIndicator: this.props.enableStatusIndicator,
                    onQuickAction: (action) => this.handleQuickAction(record, action),
                    onSelect: () => this.toggleTagSelection(record),
                    isSelected: this.enhancedState.selectedTags.has(record.resId)
                };
            }

            // 获取标签工具提示
            getTagTooltip(record) {
                const parts = [record.data.display_name];
                
                if (record.data.email) {
                    parts.push(`📧 ${record.data.email}`);
                }
                
                if (record.data.phone) {
                    parts.push(`📞 ${record.data.phone}`);
                }
                
                if (record.data.department_id) {
                    parts.push(`🏢 ${record.data.department_id[1]}`);
                }

                if (record.data.resource_type) {
                    parts.push(`🔧 ${record.data.resource_type}`);
                }

                return parts.join('\n');
            }

            // 获取标签状态
            getTagStatus(record) {
                if (!record.data.active) return 'inactive';
                if (record.data.is_busy) return 'busy';
                if (record.data.is_available) return 'available';
                return 'unknown';
            }

            // 获取状态颜色
            getStatusColor(status) {
                const colors = {
                    'available': 'success',
                    'busy': 'warning',
                    'inactive': 'danger',
                    'unknown': 'secondary'
                };
                return colors[status] || 'secondary';
            }

            // 处理快速操作
            handleQuickAction(record, action) {
                switch (action) {
                    case 'message':
                        this.sendMessage(record);
                        break;
                    case 'call':
                        this.makeCall(record);
                        break;
                    case 'email':
                        this.sendEmail(record);
                        break;
                    case 'view':
                        this.viewRecord(record);
                        break;
                    case 'remove':
                        this.removeTag(record);
                        break;
                }
            }

            // 发送消息
            sendMessage(record) {
                if (record.data.user_id) {
                    this.env.services.action.doAction({
                        type: 'ir.actions.act_window',
                        res_model: 'mail.compose.message',
                        view_mode: 'form',
                        views: [[false, 'form']],
                        target: 'new',
                        context: {
                            default_partner_ids: [record.data.partner_id?.[0]].filter(Boolean)
                        }
                    });
                }
            }

            // 拨打电话
            makeCall(record) {
                if (record.data.phone) {
                    window.open(`tel:${record.data.phone}`);
                }
            }

            // 发送邮件
            sendEmail(record) {
                if (record.data.email) {
                    window.open(`mailto:${record.data.email}`);
                }
            }

            // 查看记录
            viewRecord(record) {
                this.env.services.action.doAction({
                    type: 'ir.actions.act_window',
                    res_model: this.relation,
                    res_id: record.resId,
                    view_mode: 'form',
                    views: [[false, 'form']],
                    target: 'new'
                });
            }

            // 移除标签
            removeTag(record) {
                const currentValue = this.props.record.data[this.props.name] || [];
                const newValue = currentValue.filter(id => id !== record.resId);
                this.props.record.update({ [this.props.name]: newValue });
            }

            // 切换标签选择
            toggleTagSelection(record) {
                if (!this.props.enableBulkActions) return;

                const selected = this.enhancedState.selectedTags;
                if (selected.has(record.resId)) {
                    selected.delete(record.resId);
                } else {
                    selected.add(record.resId);
                }
            }

            // 批量操作
            performBulkAction(action) {
                const selectedIds = Array.from(this.enhancedState.selectedTags);
                if (selectedIds.length === 0) {
                    this.notification.add(_t('Please select resources first'), { type: 'warning' });
                    return;
                }

                switch (action) {
                    case 'remove_all':
                        this.bulkRemove(selectedIds);
                        break;
                    case 'message_all':
                        this.bulkMessage(selectedIds);
                        break;
                    case 'export':
                        this.bulkExport(selectedIds);
                        break;
                }
            }

            // 批量移除
            bulkRemove(selectedIds) {
                const currentValue = this.props.record.data[this.props.name] || [];
                const newValue = currentValue.filter(id => !selectedIds.includes(id));
                this.props.record.update({ [this.props.name]: newValue });
                this.enhancedState.selectedTags.clear();
            }

            // 批量发送消息
            bulkMessage(selectedIds) {
                const records = this.props.record.data[this.props.name + '_records'] || [];
                const selectedRecords = records.filter(r => selectedIds.includes(r.resId));
                const partnerIds = selectedRecords
                    .map(r => r.data.partner_id?.[0])
                    .filter(Boolean);

                if (partnerIds.length > 0) {
                    this.env.services.action.doAction({
                        type: 'ir.actions.act_window',
                        res_model: 'mail.compose.message',
                        view_mode: 'form',
                        views: [[false, 'form']],
                        target: 'new',
                        context: {
                            default_partner_ids: partnerIds
                        }
                    });
                }
            }

            // 批量导出
            bulkExport(selectedIds) {
                this.env.services.action.doAction({
                    type: 'ir.actions.act_url',
                    url: `/web/export/csv?model=${this.relation}&ids=${selectedIds.join(',')}`
                });
            }

            // 获取显示的标签
            get displayedTags() {
                const allTags = this.props.record.data[this.props.name + '_records'] || [];
                
                if (this.enhancedState.showAllTags || allTags.length <= this.props.maxDisplayTags) {
                    return allTags;
                }
                
                return allTags.slice(0, this.props.maxDisplayTags);
            }

            // 获取隐藏的标签数量
            get hiddenTagsCount() {
                const allTags = this.props.record.data[this.props.name + '_records'] || [];
                return Math.max(0, allTags.length - this.props.maxDisplayTags);
            }

            // 切换显示所有标签
            toggleShowAllTags() {
                this.enhancedState.showAllTags = !this.enhancedState.showAllTags;
            }

            // 获取批量操作
            get bulkActions() {
                if (!this.props.enableBulkActions || this.enhancedState.selectedTags.size === 0) {
                    return [];
                }

                return [
                    {
                        name: 'remove_all',
                        label: _t('Remove Selected'),
                        icon: 'fa-trash',
                        callback: () => this.performBulkAction('remove_all')
                    },
                    {
                        name: 'message_all',
                        label: _t('Message Selected'),
                        icon: 'fa-envelope',
                        callback: () => this.performBulkAction('message_all')
                    },
                    {
                        name: 'export',
                        label: _t('Export Selected'),
                        icon: 'fa-download',
                        callback: () => this.performBulkAction('export')
                    }
                ];
            }
        }

        // 增强的字段配置
        const enhancedMany2ManyAvatarResourceField = {
            ...many2ManyAvatarResourceField,
            component: EnhancedMany2ManyAvatarResourceField,
            relatedFields: (fieldInfo) => {
                return [
                    ...many2ManyAvatarResourceField.relatedFields(fieldInfo),
                    { name: "email", type: "char" },
                    { name: "phone", type: "char" },
                    { name: "department_id", type: "many2one" },
                    { name: "is_available", type: "boolean" },
                    { name: "is_busy", type: "boolean" },
                    { name: "last_activity_date", type: "datetime" },
                    { name: "skill_ids", type: "many2many" }
                ];
            },
            extractProps: ({ attrs, field }) => ({
                ...many2ManyAvatarResourceField.extractProps({ attrs, field }),
                resourceTypes: attrs.resource_types,
                onlyAvailable: attrs.only_available,
                enableQuickActions: attrs.enable_quick_actions,
                enableStatusIndicator: attrs.enable_status_indicator,
                enableBulkActions: attrs.enable_bulk_actions,
                maxDisplayTags: attrs.max_display_tags,
                enableDragDrop: attrs.enable_drag_drop,
                enableGrouping: attrs.enable_grouping
            })
        };

        // 注册增强的字段
        registry.category("fields").add("enhanced_many2many_avatar_resource", enhancedMany2ManyAvatarResourceField, { force: true });

        return {
            component: EnhancedMany2ManyAvatarResourceField,
            fieldConfig: enhancedMany2ManyAvatarResourceField
        };
    }
};

// 应用多对多头像资源字段增强
const enhancedField = Many2ManyAvatarResourceFieldEnhancer.enhanceMany2ManyAvatarResourceField();

// 导出增强的字段
__exports.enhancedField = enhancedField;
```

## 技术特点

### 1. 组件继承
- 继承多对多头像用户字段的所有功能
- 保持与邮件系统的兼容性
- 扩展资源特有功能

### 2. 智能搜索
- 支持名称模糊搜索
- 集成域过滤功能
- 扩展字段支持

### 3. 视觉区分
- 根据资源类型显示不同图标
- 用户资源显示头像
- 非用户资源显示工具图标

### 4. 交互增强
- 条件性头像卡片显示
- 响应式设计支持
- 丰富的用户交互

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 在标准多对多字段基础上添加资源功能
- 保持原有接口不变

### 2. 组合模式 (Composite Pattern)
- 组合多个子组件
- 统一的字段接口

### 3. 策略模式 (Strategy Pattern)
- 根据资源类型选择不同的显示策略
- 灵活的搜索策略

## 注意事项

1. **性能优化**: 避免加载不必要的关联字段
2. **用户体验**: 提供直观的资源选择界面
3. **数据一致性**: 确保资源类型的正确识别
4. **响应式设计**: 适配不同屏幕尺寸

## 扩展建议

1. **批量操作**: 支持批量的资源操作
2. **拖拽排序**: 支持标签的拖拽排序
3. **分组显示**: 按资源类型分组显示
4. **状态指示**: 显示资源的在线状态
5. **快速操作**: 添加常用的快速操作按钮

该多对多头像资源字段为Odoo资源邮件系统提供了重要的多选界面功能，通过扩展标准多对多字段实现了资源特有的显示和交互体验。

# Many2One Avatar Resource Field - 多对一头像资源字段

## 概述

`many2one_avatar_resource_field.js` 是 Odoo Resource Mail 模块的多对一头像资源字段组件，专门用于在表单和看板视图中显示单个资源的头像选择器。该字段基于Odoo的标准多对一头像用户字段，通过扩展功能支持资源模型的特定显示和交互，为资源管理提供了直观的单选界面和丰富的用户体验。

## 文件信息
- **路径**: `/resource_mail/static/src/views/fields/many2one_avatar_resource/many2one_avatar_resource_field.js`
- **行数**: 57
- **模块**: `@resource_mail/views/fields/many2one_avatar_resource/many2one_avatar_resource_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                                                    // 注册表
'@web/core/popover/popover_hook'                                       // 弹出框钩子
'@mail/views/web/fields/many2one_avatar_user_field/many2one_avatar_user_field'  // 多对一头像用户字段
'@resource_mail/components/avatar_card_resource/avatar_card_resource_popover'    // 资源头像卡片弹出框
```

## 核心功能

### 1. 扩展混入函数

```javascript
const ExtendMany2OneAvatarToResource = (T) => (class extends T {
    // We choose to extend Many2One_avatar_user instead of patching it as field dependencies need to be added on the widget to manage resources
    setup() {
        super.setup();
        this.avatarCard = usePopover(AvatarCardResourcePopover);
    }

    get displayAvatarCard() {
        return !this.env.isSmall && this.relation === "resource.resource" && this.props.record.data.resource_type === "user";
    }
});
```

**扩展混入功能**:
- **高阶组件**: 使用高阶组件模式扩展现有字段
- **弹出框集成**: 集成资源头像卡片弹出框
- **条件显示**: 根据环境和资源类型控制头像卡片显示
- **继承保持**: 保持原有字段的所有功能

### 2. 表单字段组件

```javascript
const Many2OneAvatarResourceField = __exports.Many2OneAvatarResourceField = class Many2OneAvatarResourceField extends ExtendMany2OneAvatarToResource(Many2OneAvatarUserField) {
    static template = "resource_mail.Many2OneAvatarResourceField";
}
```

**表单字段功能**:
- **模板定制**: 使用专门的资源头像字段模板
- **混入应用**: 应用资源扩展混入
- **继承链**: 继承多对一头像用户字段的所有功能

### 3. 字段配置

```javascript
const many2OneAvatarResourceField = __exports.many2OneAvatarResourceField = {
    ...many2OneAvatarUserField,
    component: Many2OneAvatarResourceField,
    fieldDependencies: [
        {
            name: "resource_type", //to add in model that will use this widget for m2o field related to resource.resource record (as related field is only supported for x2m)
            type: "selection",
        },
    ],
};
```

**配置功能**:
- **配置继承**: 继承用户头像字段的所有配置
- **组件绑定**: 绑定自定义的资源字段组件
- **字段依赖**: 添加resource_type作为字段依赖
- **类型声明**: 声明resource_type为选择类型字段

### 4. 看板字段组件

```javascript
const KanbanMany2OneAvatarResourceField = __exports.KanbanMany2OneAvatarResourceField = class KanbanMany2OneAvatarResourceField extends ExtendMany2OneAvatarToResource(Many2OneAvatarResourceField) {
    static template = "resource_mail.KanbanMany2OneAvatarResourceField";
}
```

**看板字段功能**:
- **看板模板**: 使用专门的看板头像字段模板
- **双重扩展**: 同时应用资源扩展和表单字段扩展
- **视图适配**: 适配看板视图的显示需求

### 5. 字段注册

```javascript
registry.category("fields").add("many2one_avatar_resource", many2OneAvatarResourceField);

const kanbanMany2OneAvatarResourceField = __exports.kanbanMany2OneAvatarResourceField = {
    ...many2OneAvatarResourceField,
    component: KanbanMany2OneAvatarResourceField,
};

registry.category("fields").add("kanban.many2one_avatar_resource", kanbanMany2OneAvatarResourceField);
```

**注册功能**:
- **表单字段注册**: 注册表单视图的资源字段
- **看板字段注册**: 注册看板视图的资源字段
- **命名约定**: 遵循Odoo的字段命名约定
- **配置复用**: 看板字段复用表单字段配置

## 使用场景

### 1. 多对一头像资源字段增强

```javascript
// 多对一头像资源字段增强功能
const Many2OneAvatarResourceFieldEnhancer = {
    enhanceMany2OneAvatarResourceField: () => {
        // 增强的扩展混入函数
        const EnhancedExtendMany2OneAvatarToResource = (T) => (class extends T {
            setup() {
                super.setup();
                
                // 增强的弹出框配置
                this.avatarCard = usePopover(AvatarCardResourcePopover, {
                    position: "bottom",
                    animation: true,
                    delay: { show: 500, hide: 100 }
                });

                // 状态管理
                this.enhancedState = useState({
                    isHovered: false,
                    showQuickActions: false,
                    lastInteraction: null
                });

                // 服务注入
                this.notification = useService("notification");
                this.actionService = useService("action");
            }

            // 增强的头像卡片显示逻辑
            get displayAvatarCard() {
                const baseCondition = super.displayAvatarCard;
                
                // 添加额外的显示条件
                if (!baseCondition) return false;
                
                // 检查用户权限
                if (!this.hasViewPermission()) return false;
                
                // 检查资源状态
                if (!this.isResourceActive()) return false;
                
                return true;
            }

            // 检查查看权限
            hasViewPermission() {
                // 这里可以添加权限检查逻辑
                return true;
            }

            // 检查资源是否活跃
            isResourceActive() {
                return this.props.record.data.active !== false;
            }

            // 增强的头像点击处理
            onAvatarClick(ev) {
                if (super.onAvatarClick) {
                    super.onAvatarClick(ev);
                }

                // 记录交互
                this.enhancedState.lastInteraction = Date.now();

                // 触发自定义事件
                this.env.bus.trigger('avatar-clicked', {
                    resourceId: this.props.record.data[this.props.name]?.[0],
                    resourceType: this.props.record.data.resource_type,
                    timestamp: Date.now()
                });
            }

            // 头像悬停处理
            onAvatarHover(isHovered) {
                this.enhancedState.isHovered = isHovered;
                
                if (isHovered) {
                    // 预加载头像卡片数据
                    this.preloadAvatarCardData();
                }
            }

            // 预加载头像卡片数据
            async preloadAvatarCardData() {
                const resourceId = this.props.record.data[this.props.name]?.[0];
                if (!resourceId) return;

                try {
                    // 预加载资源详细信息
                    await this.orm.read('resource.resource', [resourceId], [
                        'name', 'email', 'phone', 'user_id', 'resource_type', 'active'
                    ]);
                } catch (error) {
                    console.warn('预加载头像卡片数据失败:', error);
                }
            }

            // 获取头像URL
            get avatarUrl() {
                const resourceId = this.props.record.data[this.props.name]?.[0];
                const resourceType = this.props.record.data.resource_type;
                
                if (!resourceId) return null;
                
                if (resourceType === 'user') {
                    return `/web/image/resource.resource/${resourceId}/avatar_128`;
                } else {
                    return null; // 非用户资源不显示头像
                }
            }

            // 获取资源图标
            get resourceIcon() {
                const resourceType = this.props.record.data.resource_type;
                
                const iconMap = {
                    'user': 'fa-user',
                    'material': 'fa-wrench',
                    'equipment': 'fa-cogs',
                    'room': 'fa-building',
                    'vehicle': 'fa-car'
                };
                
                return iconMap[resourceType] || 'fa-question-circle';
            }

            // 获取资源状态
            get resourceStatus() {
                const record = this.props.record.data;
                
                if (!record.active) return 'inactive';
                if (record.is_available) return 'available';
                if (record.is_busy) return 'busy';
                return 'unknown';
            }

            // 获取状态颜色
            get statusColor() {
                const statusColors = {
                    'available': 'success',
                    'busy': 'warning',
                    'inactive': 'danger',
                    'unknown': 'secondary'
                };
                return statusColors[this.resourceStatus] || 'secondary';
            }

            // 获取快速操作
            get quickActions() {
                if (!this.enhancedState.showQuickActions) return [];

                const actions = [];
                const record = this.props.record.data;
                const resourceType = record.resource_type;

                // 查看详情
                actions.push({
                    name: 'view',
                    label: _t('View Details'),
                    icon: 'fa-info-circle',
                    callback: () => this.viewResourceDetails()
                });

                // 用户特有操作
                if (resourceType === 'user' && record.user_id) {
                    actions.push({
                        name: 'message',
                        label: _t('Send Message'),
                        icon: 'fa-comment',
                        callback: () => this.sendMessage()
                    });

                    if (record.email) {
                        actions.push({
                            name: 'email',
                            label: _t('Send Email'),
                            icon: 'fa-envelope',
                            callback: () => this.sendEmail()
                        });
                    }

                    if (record.phone) {
                        actions.push({
                            name: 'call',
                            label: _t('Call'),
                            icon: 'fa-phone',
                            callback: () => this.makeCall()
                        });
                    }
                }

                // 编辑操作
                if (this.hasEditPermission()) {
                    actions.push({
                        name: 'edit',
                        label: _t('Edit'),
                        icon: 'fa-edit',
                        callback: () => this.editResource()
                    });
                }

                return actions;
            }

            // 查看资源详情
            viewResourceDetails() {
                const resourceId = this.props.record.data[this.props.name]?.[0];
                if (!resourceId) return;

                this.actionService.doAction({
                    type: 'ir.actions.act_window',
                    res_model: 'resource.resource',
                    res_id: resourceId,
                    view_mode: 'form',
                    views: [[false, 'form']],
                    target: 'new'
                });
            }

            // 发送消息
            sendMessage() {
                const record = this.props.record.data;
                const userId = record.user_id?.[0];
                
                if (userId) {
                    this.env.services.discuss.openChat({ userId });
                }
            }

            // 发送邮件
            sendEmail() {
                const record = this.props.record.data;
                if (record.email) {
                    window.open(`mailto:${record.email}`);
                }
            }

            // 拨打电话
            makeCall() {
                const record = this.props.record.data;
                if (record.phone) {
                    window.open(`tel:${record.phone}`);
                }
            }

            // 编辑资源
            editResource() {
                const resourceId = this.props.record.data[this.props.name]?.[0];
                if (!resourceId) return;

                this.actionService.doAction({
                    type: 'ir.actions.act_window',
                    res_model: 'resource.resource',
                    res_id: resourceId,
                    view_mode: 'form',
                    views: [[false, 'form']],
                    target: 'current'
                });
            }

            // 检查编辑权限
            hasEditPermission() {
                // 这里可以添加编辑权限检查逻辑
                return true;
            }

            // 切换快速操作显示
            toggleQuickActions() {
                this.enhancedState.showQuickActions = !this.enhancedState.showQuickActions;
            }

            // 执行快速操作
            executeQuickAction(actionName) {
                const action = this.quickActions.find(a => a.name === actionName);
                if (action && action.callback) {
                    action.callback();
                    this.enhancedState.showQuickActions = false;
                }
            }
        });

        // 增强的表单字段组件
        class EnhancedMany2OneAvatarResourceField extends EnhancedExtendMany2OneAvatarToResource(Many2OneAvatarUserField) {
            static template = "resource_mail.EnhancedMany2OneAvatarResourceField";
            
            static props = {
                ...Many2OneAvatarUserField.props,
                enableQuickActions: { type: Boolean, optional: true },
                enableStatusIndicator: { type: Boolean, optional: true },
                enableHoverPreview: { type: Boolean, optional: true },
                enableClickActions: { type: Boolean, optional: true },
                customActions: { type: Array, optional: true }
            };

            static defaultProps = {
                ...Many2OneAvatarUserField.defaultProps,
                enableQuickActions: true,
                enableStatusIndicator: true,
                enableHoverPreview: true,
                enableClickActions: true,
                customActions: []
            };
        }

        // 增强的看板字段组件
        class EnhancedKanbanMany2OneAvatarResourceField extends EnhancedExtendMany2OneAvatarToResource(EnhancedMany2OneAvatarResourceField) {
            static template = "resource_mail.EnhancedKanbanMany2OneAvatarResourceField";
        }

        // 增强的字段配置
        const enhancedMany2OneAvatarResourceField = {
            ...many2OneAvatarResourceField,
            component: EnhancedMany2OneAvatarResourceField,
            fieldDependencies: [
                ...many2OneAvatarResourceField.fieldDependencies,
                { name: "active", type: "boolean" },
                { name: "email", type: "char" },
                { name: "phone", type: "char" },
                { name: "is_available", type: "boolean" },
                { name: "is_busy", type: "boolean" },
                { name: "user_id", type: "many2one" }
            ],
            extractProps: ({ attrs, field }) => ({
                ...many2OneAvatarResourceField.extractProps({ attrs, field }),
                enableQuickActions: attrs.enable_quick_actions,
                enableStatusIndicator: attrs.enable_status_indicator,
                enableHoverPreview: attrs.enable_hover_preview,
                enableClickActions: attrs.enable_click_actions,
                customActions: attrs.custom_actions
            })
        };

        const enhancedKanbanMany2OneAvatarResourceField = {
            ...enhancedMany2OneAvatarResourceField,
            component: EnhancedKanbanMany2OneAvatarResourceField,
        };

        // 注册增强的字段
        registry.category("fields").add("enhanced_many2one_avatar_resource", enhancedMany2OneAvatarResourceField, { force: true });
        registry.category("fields").add("kanban.enhanced_many2one_avatar_resource", enhancedKanbanMany2OneAvatarResourceField, { force: true });

        return {
            formComponent: EnhancedMany2OneAvatarResourceField,
            kanbanComponent: EnhancedKanbanMany2OneAvatarResourceField,
            formFieldConfig: enhancedMany2OneAvatarResourceField,
            kanbanFieldConfig: enhancedKanbanMany2OneAvatarResourceField
        };
    }
};

// 应用多对一头像资源字段增强
const enhancedFields = Many2OneAvatarResourceFieldEnhancer.enhanceMany2OneAvatarResourceField();

// 导出增强的字段
__exports.enhancedFields = enhancedFields;
```

## 技术特点

### 1. 高阶组件模式
- 使用高阶组件扩展现有字段
- 保持原有功能的完整性
- 灵活的扩展机制

### 2. 模板分离
- 表单和看板使用不同模板
- 适配不同视图的显示需求
- 清晰的视图分离

### 3. 字段依赖
- 明确声明字段依赖关系
- 确保必要数据的可用性
- 支持资源类型判断

### 4. 条件显示
- 智能的头像卡片显示逻辑
- 考虑屏幕尺寸和资源类型
- 优化的用户体验

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 在标准多对一字段基础上添加资源功能
- 保持原有接口不变

### 2. 混入模式 (Mixin Pattern)
- 使用混入函数扩展多个组件
- 代码复用和功能共享

### 3. 工厂模式 (Factory Pattern)
- 高阶组件作为组件工厂
- 动态创建扩展组件

## 注意事项

1. **字段依赖**: 确保resource_type字段在模型中可用
2. **权限控制**: 考虑用户的查看和编辑权限
3. **性能优化**: 避免不必要的数据加载
4. **响应式设计**: 适配不同屏幕尺寸

## 扩展建议

1. **快速操作**: 添加常用的快速操作按钮
2. **状态指示**: 显示资源的实时状态
3. **预览功能**: 支持悬停预览资源信息
4. **自定义操作**: 支持用户自定义操作
5. **批量选择**: 在列表视图中支持批量选择

该多对一头像资源字段为Odoo资源邮件系统提供了重要的单选界面功能，通过扩展标准多对一字段实现了资源特有的显示和交互体验。

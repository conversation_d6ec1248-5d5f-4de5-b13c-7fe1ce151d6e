# Avatar Card Resource Popover - 资源头像卡片弹出框

## 概述

`avatar_card_resource_popover.js` 是 Odoo Resource Mail 模块的资源头像卡片弹出框组件，专门用于在邮件和讨论系统中显示资源相关的用户信息。该组件基于Odoo的标准头像卡片弹出框，通过扩展功能支持资源模型的特定字段和行为，为资源管理中的用户交互提供了丰富的信息展示和操作功能。

## 文件信息
- **路径**: `/resource_mail/static/src/components/avatar_card_resource/avatar_card_resource_popover.js`
- **行数**: 73
- **模块**: `@resource_mail/components/avatar_card_resource/avatar_card_resource_popover`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                                    // OWL框架
'@web/core/utils/hooks'                                        // 钩子工具
'@mail/core/web/open_chat_hook'                               // 打开聊天钩子
'@mail/discuss/web/avatar_card/avatar_card_popover'           // 标准头像卡片弹出框
```

## 核心功能

### 1. 组件定义

```javascript
const AvatarCardResourcePopover = __exports.AvatarCardResourcePopover = class AvatarCardResourcePopover extends AvatarCardPopover {
    static template = "resource_mail.AvatarCardResourcePopover";

    static props = {
        ...AvatarCardPopover.props,
        recordModel: {
            type: String,
            optional: true,
        },
    };

    static defaultProps = {
        ...AvatarCardPopover.defaultProps,
        recordModel: "resource.resource",
    };
```

**组件特性**:
- **继承扩展**: 继承AvatarCardPopover的所有功能
- **模板定制**: 使用专门的资源头像卡片模板
- **属性扩展**: 添加recordModel属性支持不同的资源模型
- **默认配置**: 设置默认的资源模型为"resource.resource"

### 2. 组件初始化

```javascript
setup() {
    this.orm = useService("orm");
    this.actionService = useService("action");
    this.openChat = useOpenChat("res.users");
    onWillStart(this.onWillStart);
}
```

**初始化功能**:
- **服务注入**: 注入ORM和动作服务
- **聊天功能**: 初始化打开聊天功能
- **生命周期**: 设置组件启动前的钩子
- **数据准备**: 准备组件渲染所需的数据

### 3. 数据加载

```javascript
async onWillStart() {
    [this.record] = await this.orm.read(this.props.recordModel, [this.props.id], this.fieldNames);
    await Promise.all(this.loadAdditionalData());
}

loadAdditionalData() {
    // To use when overriden in other modules to load additional data, returns promise(s)
    return [];
}
```

**数据加载功能**:
- **记录读取**: 从指定模型读取记录数据
- **字段选择**: 使用fieldNames指定需要的字段
- **扩展加载**: 提供loadAdditionalData方法供子类扩展
- **异步处理**: 支持异步的数据加载和处理

### 4. 字段配置

```javascript
get fieldNames() {
    const excludedFields = new Set(["partner_id"]);
    return super.fieldNames
        .concat(["user_id", "resource_type"])
        .filter((field) => !excludedFields.has(field));
}
```

**字段配置功能**:
- **字段继承**: 继承父类的字段配置
- **字段添加**: 添加资源特有的字段（user_id, resource_type）
- **字段排除**: 排除不需要的字段（partner_id）
- **字段过滤**: 使用过滤器确保字段的正确性

### 5. 数据访问器

```javascript
get email() {
    return this.record.email;
}

get phone() {
    return this.record.phone;
}

get displayAvatar() {
    return this.record.user_id?.length;
}

get showViewProfileBtn() {
    return false;
}

get userId() {
    return this.record.user_id[0];
}
```

**访问器功能**:
- **邮箱获取**: 获取资源的邮箱地址
- **电话获取**: 获取资源的电话号码
- **头像显示**: 根据用户ID判断是否显示头像
- **按钮控制**: 控制查看资料按钮的显示
- **用户ID**: 获取关联的用户ID

## 使用场景

### 1. 资源头像卡片弹出框增强

```javascript
// 资源头像卡片弹出框增强功能
const AvatarCardResourcePopoverEnhancer = {
    enhanceAvatarCardResourcePopover: () => {
        // 增强的资源头像卡片弹出框
        class EnhancedAvatarCardResourcePopover extends AvatarCardResourcePopover {
            static template = "resource_mail.EnhancedAvatarCardResourcePopover";
            
            static props = {
                ...AvatarCardResourcePopover.props,
                enableResourceDetails: { type: Boolean, optional: true },
                enableQuickActions: { type: Boolean, optional: true },
                enableStatusIndicator: { type: Boolean, optional: true },
                enableSkillsDisplay: { type: Boolean, optional: true },
                enableAvailabilityInfo: { type: Boolean, optional: true },
                enableProjectHistory: { type: Boolean, optional: true },
                customFields: { type: Array, optional: true },
                enableRealTimeUpdates: { type: Boolean, optional: true }
            };

            static defaultProps = {
                ...AvatarCardResourcePopover.defaultProps,
                enableResourceDetails: true,
                enableQuickActions: true,
                enableStatusIndicator: true,
                enableSkillsDisplay: false,
                enableAvailabilityInfo: false,
                enableProjectHistory: false,
                customFields: [],
                enableRealTimeUpdates: false
            };

            setup() {
                super.setup();

                // 增强的配置选项
                this.enhancedConfig = {
                    enableResourceDetails: this.props.enableResourceDetails,
                    enableQuickActions: this.props.enableQuickActions,
                    enableStatusIndicator: this.props.enableStatusIndicator,
                    enableSkillsDisplay: this.props.enableSkillsDisplay,
                    enableAvailabilityInfo: this.props.enableAvailabilityInfo,
                    enableProjectHistory: this.props.enableProjectHistory,
                    customFields: this.props.customFields,
                    enableRealTimeUpdates: this.props.enableRealTimeUpdates
                };

                // 增强的状态
                this.enhancedState = useState({
                    resourceDetails: null,
                    skills: [],
                    availability: null,
                    projectHistory: [],
                    realTimeData: null,
                    isLoading: false,
                    lastUpdated: null
                });

                // 实时更新服务
                if (this.enhancedConfig.enableRealTimeUpdates) {
                    this.busService = useService("bus_service");
                    this.setupRealTimeUpdates();
                }

                // 通知服务
                this.notification = useService("notification");
            }

            // 增强的数据加载
            async onWillStart() {
                await super.onWillStart();
                
                if (this.enhancedConfig.enableResourceDetails) {
                    await this.loadResourceDetails();
                }

                if (this.enhancedConfig.enableSkillsDisplay) {
                    await this.loadResourceSkills();
                }

                if (this.enhancedConfig.enableAvailabilityInfo) {
                    await this.loadAvailabilityInfo();
                }

                if (this.enhancedConfig.enableProjectHistory) {
                    await this.loadProjectHistory();
                }
            }

            // 增强的字段配置
            get fieldNames() {
                let fields = super.fieldNames;

                // 添加资源详情字段
                if (this.enhancedConfig.enableResourceDetails) {
                    fields = fields.concat([
                        'department_id',
                        'job_title',
                        'manager_id',
                        'work_location',
                        'hire_date',
                        'employee_type'
                    ]);
                }

                // 添加状态字段
                if (this.enhancedConfig.enableStatusIndicator) {
                    fields = fields.concat([
                        'active',
                        'resource_calendar_id',
                        'tz'
                    ]);
                }

                // 添加自定义字段
                if (this.enhancedConfig.customFields.length > 0) {
                    fields = fields.concat(this.enhancedConfig.customFields);
                }

                return fields;
            }

            // 加载资源详情
            async loadResourceDetails() {
                try {
                    this.enhancedState.isLoading = true;
                    
                    const details = await this.orm.call(
                        this.props.recordModel,
                        'get_resource_details',
                        [this.props.id]
                    );

                    this.enhancedState.resourceDetails = details;
                } catch (error) {
                    console.error('加载资源详情失败:', error);
                } finally {
                    this.enhancedState.isLoading = false;
                }
            }

            // 加载资源技能
            async loadResourceSkills() {
                try {
                    const skills = await this.orm.call(
                        'hr.skill',
                        'search_read',
                        [[['employee_ids', 'in', [this.userId]]]],
                        { fields: ['name', 'level_progress', 'skill_type_id'] }
                    );

                    this.enhancedState.skills = skills;
                } catch (error) {
                    console.error('加载资源技能失败:', error);
                }
            }

            // 加载可用性信息
            async loadAvailabilityInfo() {
                try {
                    const availability = await this.orm.call(
                        this.props.recordModel,
                        'get_availability_info',
                        [this.props.id],
                        {
                            start_date: moment().format('YYYY-MM-DD'),
                            end_date: moment().add(30, 'days').format('YYYY-MM-DD')
                        }
                    );

                    this.enhancedState.availability = availability;
                } catch (error) {
                    console.error('加载可用性信息失败:', error);
                }
            }

            // 加载项目历史
            async loadProjectHistory() {
                try {
                    const history = await this.orm.call(
                        'project.project',
                        'search_read',
                        [[['user_id', '=', this.userId]]],
                        { 
                            fields: ['name', 'date_start', 'date', 'stage_id'],
                            limit: 5,
                            order: 'date_start desc'
                        }
                    );

                    this.enhancedState.projectHistory = history;
                } catch (error) {
                    console.error('加载项目历史失败:', error);
                }
            }

            // 设置实时更新
            setupRealTimeUpdates() {
                this.busService.addEventListener('notification', (event) => {
                    const { type, payload } = event.detail;
                    
                    if (type === 'resource_update' && payload.resource_id === this.props.id) {
                        this.handleRealTimeUpdate(payload);
                    }
                });

                // 定期更新
                this.updateInterval = setInterval(() => {
                    this.refreshData();
                }, 60000); // 每分钟更新一次
            }

            // 处理实时更新
            handleRealTimeUpdate(payload) {
                this.enhancedState.realTimeData = payload;
                this.enhancedState.lastUpdated = Date.now();

                // 根据更新类型处理
                switch (payload.update_type) {
                    case 'status_change':
                        this.handleStatusChange(payload);
                        break;
                    case 'availability_change':
                        this.handleAvailabilityChange(payload);
                        break;
                    case 'project_assignment':
                        this.handleProjectAssignment(payload);
                        break;
                }
            }

            // 处理状态变化
            handleStatusChange(payload) {
                if (this.record) {
                    this.record.active = payload.active;
                    this.record.resource_calendar_id = payload.calendar_id;
                }
            }

            // 处理可用性变化
            handleAvailabilityChange(payload) {
                if (this.enhancedConfig.enableAvailabilityInfo) {
                    this.enhancedState.availability = payload.availability;
                }
            }

            // 处理项目分配
            handleProjectAssignment(payload) {
                if (this.enhancedConfig.enableProjectHistory) {
                    this.loadProjectHistory(); // 重新加载项目历史
                }
            }

            // 刷新数据
            async refreshData() {
                try {
                    // 重新读取基础记录
                    [this.record] = await this.orm.read(
                        this.props.recordModel, 
                        [this.props.id], 
                        this.fieldNames
                    );

                    // 刷新增强数据
                    if (this.enhancedConfig.enableAvailabilityInfo) {
                        await this.loadAvailabilityInfo();
                    }

                    this.enhancedState.lastUpdated = Date.now();
                } catch (error) {
                    console.error('刷新数据失败:', error);
                }
            }

            // 增强的数据访问器
            get resourceDetails() {
                return this.enhancedState.resourceDetails || {};
            }

            get skills() {
                return this.enhancedState.skills || [];
            }

            get availability() {
                return this.enhancedState.availability || {};
            }

            get projectHistory() {
                return this.enhancedState.projectHistory || [];
            }

            get department() {
                return this.record.department_id?.[1] || '';
            }

            get jobTitle() {
                return this.record.job_title || '';
            }

            get manager() {
                return this.record.manager_id?.[1] || '';
            }

            get workLocation() {
                return this.record.work_location || '';
            }

            get hireDate() {
                return this.record.hire_date || '';
            }

            get employeeType() {
                return this.record.employee_type || '';
            }

            get isActive() {
                return this.record.active;
            }

            get timezone() {
                return this.record.tz || 'UTC';
            }

            get currentStatus() {
                if (!this.isActive) return 'inactive';
                if (this.availability?.is_available) return 'available';
                if (this.availability?.is_busy) return 'busy';
                return 'unknown';
            }

            get statusColor() {
                const statusColors = {
                    'available': 'success',
                    'busy': 'warning',
                    'inactive': 'danger',
                    'unknown': 'secondary'
                };
                return statusColors[this.currentStatus] || 'secondary';
            }

            // 快速操作
            get quickActions() {
                if (!this.enhancedConfig.enableQuickActions) return [];

                const actions = [];

                // 发送消息
                if (this.userId) {
                    actions.push({
                        name: 'send_message',
                        label: _t('Send Message'),
                        icon: 'fa-comment',
                        callback: () => this.sendMessage()
                    });
                }

                // 安排会议
                actions.push({
                    name: 'schedule_meeting',
                    label: _t('Schedule Meeting'),
                    icon: 'fa-calendar',
                    callback: () => this.scheduleMeeting()
                });

                // 分配任务
                actions.push({
                    name: 'assign_task',
                    label: _t('Assign Task'),
                    icon: 'fa-tasks',
                    callback: () => this.assignTask()
                });

                // 查看详情
                actions.push({
                    name: 'view_details',
                    label: _t('View Details'),
                    icon: 'fa-info-circle',
                    callback: () => this.viewDetails()
                });

                return actions;
            }

            // 发送消息
            sendMessage() {
                if (this.userId) {
                    this.openChat(this.userId);
                }
            }

            // 安排会议
            scheduleMeeting() {
                this.actionService.doAction({
                    type: 'ir.actions.act_window',
                    res_model: 'calendar.event',
                    view_mode: 'form',
                    views: [[false, 'form']],
                    target: 'new',
                    context: {
                        default_partner_ids: [this.record.partner_id?.[0]].filter(Boolean),
                        default_name: _t('Meeting with %s', this.record.name)
                    }
                });
            }

            // 分配任务
            assignTask() {
                this.actionService.doAction({
                    type: 'ir.actions.act_window',
                    res_model: 'project.task',
                    view_mode: 'form',
                    views: [[false, 'form']],
                    target: 'new',
                    context: {
                        default_user_ids: [this.userId].filter(Boolean),
                        default_name: _t('Task for %s', this.record.name)
                    }
                });
            }

            // 查看详情
            viewDetails() {
                this.actionService.doAction({
                    type: 'ir.actions.act_window',
                    res_model: this.props.recordModel,
                    res_id: this.props.id,
                    view_mode: 'form',
                    views: [[false, 'form']],
                    target: 'new'
                });
            }

            // 执行快速操作
            executeQuickAction(actionName) {
                const action = this.quickActions.find(a => a.name === actionName);
                if (action && action.callback) {
                    action.callback();
                }
            }

            // 格式化日期
            formatDate(date) {
                if (!date) return '';
                return moment(date).format('YYYY-MM-DD');
            }

            // 格式化相对时间
            formatRelativeTime(date) {
                if (!date) return '';
                return moment(date).fromNow();
            }

            // 获取技能等级颜色
            getSkillLevelColor(level) {
                if (level >= 80) return 'success';
                if (level >= 60) return 'info';
                if (level >= 40) return 'warning';
                return 'danger';
            }

            // 组件销毁时清理
            willDestroy() {
                // 清理定时器
                if (this.updateInterval) {
                    clearInterval(this.updateInterval);
                }

                super.willDestroy && super.willDestroy();
            }
        }

        return EnhancedAvatarCardResourcePopover;
    }
};

// 应用资源头像卡片弹出框增强
const enhancedAvatarCardResourcePopover = AvatarCardResourcePopoverEnhancer.enhanceAvatarCardResourcePopover();

// 导出增强的组件
__exports.enhancedAvatarCardResourcePopover = enhancedAvatarCardResourcePopover;
```

## 技术特点

### 1. 组件继承
- 继承AvatarCardPopover的所有功能
- 保持与邮件系统的兼容性
- 扩展资源特有功能

### 2. 数据管理
- 异步的数据加载机制
- 灵活的字段配置
- 扩展性的数据加载接口

### 3. 服务集成
- 集成ORM服务进行数据操作
- 集成动作服务进行界面跳转
- 集成聊天服务进行即时通讯

### 4. 模板定制
- 使用专门的资源头像卡片模板
- 支持资源特有的显示需求
- 灵活的模板配置

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 在标准头像卡片基础上添加资源功能
- 保持原有接口不变

### 2. 模板方法模式 (Template Method Pattern)
- 定义数据加载的基本流程
- 允许子类扩展特定步骤

### 3. 策略模式 (Strategy Pattern)
- 灵活的字段配置策略
- 可配置的显示策略

## 注意事项

1. **数据安全**: 确保只显示用户有权限查看的信息
2. **性能优化**: 避免加载不必要的数据
3. **用户体验**: 提供流畅的弹出框交互
4. **错误处理**: 完善的错误处理和用户反馈

## 扩展建议

1. **实时更新**: 支持资源状态的实时更新
2. **快速操作**: 添加常用的快速操作按钮
3. **详细信息**: 显示更多的资源详细信息
4. **交互增强**: 支持更丰富的用户交互
5. **个性化**: 支持用户个性化的显示配置

该资源头像卡片弹出框为Odoo资源邮件系统提供了重要的用户信息展示功能，通过扩展标准头像卡片实现了资源特有的信息显示和交互体验。

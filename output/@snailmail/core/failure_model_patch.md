# Snailmail Failure Model Patch - 蜗牛邮件失败模型补丁

## 概述

`failure_model_patch.js` 是 Odoo Snailmail 模块的失败模型补丁，专门用于扩展邮件模块的失败模型以支持蜗牛邮件（传统邮件）失败处理。该补丁基于Odoo的补丁机制，为蜗牛邮件类型的失败提供了专门的图标和错误消息，确保蜗牛邮件发送失败时能够提供准确的用户反馈和视觉提示。

## 文件信息
- **路径**: `/snailmail/static/src/core/failure_model_patch.js`
- **行数**: 26
- **模块**: `@snailmail/core/failure_model_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/failure_model'   // 邮件失败模型
'@web/core/l10n/translation'        // 国际化翻译
'@web/core/utils/patch'              // 补丁工具
```

## 核心功能

### 1. 失败模型补丁

```javascript
patch(Failure.prototype, {
    get iconSrc() {
        if (this.type === "snail") {
            return "/snailmail/static/img/snailmail_failure.png";
        }
        return super.iconSrc;
    },
    get body() {
        if (this.type === "snail") {
            return _t("An error occurred when sending a letter with Snailmail.");
        }
        return super.body;
    },
});
```

**补丁功能**:
- **类型检测**: 检测失败类型是否为蜗牛邮件
- **图标定制**: 为蜗牛邮件失败提供专门的PNG图标
- **消息定制**: 为蜗牛邮件失败提供专门的错误消息
- **国际化**: 错误消息支持多语言翻译
- **继承保持**: 非蜗牛邮件类型失败保持原有行为

### 2. 图标源获取

```javascript
get iconSrc() {
    if (this.type === "snail") {
        return "/snailmail/static/img/snailmail_failure.png";
    }
    return super.iconSrc;
}
```

**图标功能**:
- **条件判断**: 根据失败类型返回相应图标
- **蜗牛邮件图标**: 蜗牛邮件失败使用专门的失败图标
- **路径指定**: 指定蜗牛邮件失败图标的完整路径
- **默认行为**: 非蜗牛邮件类型使用父类的图标逻辑

### 3. 错误消息获取

```javascript
get body() {
    if (this.type === "snail") {
        return _t("An error occurred when sending a letter with Snailmail.");
    }
    return super.body;
}
```

**消息功能**:
- **类型特化**: 为蜗牛邮件失败提供特定的错误消息
- **翻译支持**: 使用翻译函数支持多语言
- **用户友好**: 提供清晰易懂的错误描述
- **继承机制**: 保持其他类型失败的原有消息

## 使用场景

### 1. 蜗牛邮件失败处理增强

```javascript
// 蜗牛邮件失败模型补丁增强功能
const SnailmailFailureModelPatchEnhancer = {
    enhanceSnailmailFailureModelPatch: () => {
        // 增强的蜗牛邮件失败模型补丁
        const enhancedFailurePatch = {
            // 增强的图标源获取
            get iconSrc() {
                if (this.type === "snail") {
                    // 根据失败原因返回不同图标
                    switch (this.reason) {
                        case 'address_error':
                            return "/snailmail/static/img/address_failure.png";
                        case 'credit_error':
                            return "/snailmail/static/img/credit_failure.png";
                        case 'format_error':
                            return "/snailmail/static/img/format_failure.png";
                        case 'service_error':
                            return "/snailmail/static/img/service_failure.png";
                        case 'print_error':
                            return "/snailmail/static/img/print_failure.png";
                        default:
                            return "/snailmail/static/img/snailmail_failure.png";
                    }
                }
                return super.iconSrc;
            },
            
            // 增强的错误消息获取
            get body() {
                if (this.type === "snail") {
                    // 根据失败原因返回详细消息
                    switch (this.reason) {
                        case 'address_error':
                            return _t("Letter sending failed: Invalid or incomplete address");
                        case 'credit_error':
                            return _t("Letter sending failed: Insufficient credit balance");
                        case 'format_error':
                            return _t("Letter sending failed: Document format not supported");
                        case 'service_error':
                            return _t("Letter sending failed: Snailmail service unavailable");
                        case 'print_error':
                            return _t("Letter sending failed: Printing error occurred");
                        case 'postal_error':
                            return _t("Letter sending failed: Postal service error");
                        case 'validation_error':
                            return _t("Letter sending failed: Document validation failed");
                        default:
                            return _t("An error occurred when sending a letter with Snailmail.");
                    }
                }
                return super.body;
            },
            
            // 获取失败严重程度
            get severity() {
                if (this.type === "snail") {
                    switch (this.reason) {
                        case 'credit_error':
                        case 'service_error':
                            return 'warning'; // 可恢复的错误
                        case 'address_error':
                        case 'format_error':
                        case 'validation_error':
                            return 'error'; // 需要用户干预的错误
                        case 'print_error':
                        case 'postal_error':
                            return 'info'; // 临时性错误
                        default:
                            return 'error';
                    }
                }
                return super.severity || 'error';
            },
            
            // 获取建议操作
            get suggestedActions() {
                if (this.type === "snail") {
                    const actions = [];
                    
                    switch (this.reason) {
                        case 'credit_error':
                            actions.push({
                                label: _t("Buy Credits"),
                                action: 'buy_credits',
                                icon: 'fa-credit-card',
                                primary: true
                            });
                            break;
                        case 'address_error':
                            actions.push({
                                label: _t("Edit Address"),
                                action: 'edit_address',
                                icon: 'fa-map-marker',
                                primary: true
                            });
                            break;
                        case 'format_error':
                            actions.push({
                                label: _t("Convert Format"),
                                action: 'convert_format',
                                icon: 'fa-file-pdf-o',
                                primary: true
                            });
                            break;
                        case 'service_error':
                        case 'print_error':
                        case 'postal_error':
                            actions.push({
                                label: _t("Retry"),
                                action: 'retry',
                                icon: 'fa-refresh',
                                primary: true
                            });
                            break;
                        case 'validation_error':
                            actions.push({
                                label: _t("Edit Document"),
                                action: 'edit_document',
                                icon: 'fa-edit',
                                primary: true
                            });
                            break;
                    }
                    
                    // 通用操作
                    actions.push({
                        label: _t("View Details"),
                        action: 'view_details',
                        icon: 'fa-info-circle',
                        primary: false
                    });
                    
                    return actions;
                }
                return super.suggestedActions || [];
            },
            
            // 获取失败详细信息
            get details() {
                if (this.type === "snail") {
                    const details = {
                        type: 'Snailmail',
                        timestamp: this.timestamp,
                        recipient: this.recipient,
                        reason: this.reason,
                        errorCode: this.errorCode,
                        provider: this.provider,
                        documentId: this.documentId,
                        pages: this.pages,
                        cost: this.cost
                    };
                    
                    // 添加特定原因的详细信息
                    switch (this.reason) {
                        case 'credit_error':
                            details.currentBalance = this.currentBalance;
                            details.requiredCredits = this.requiredCredits;
                            break;
                        case 'address_error':
                            details.addressIssues = this.addressIssues;
                            details.suggestedAddress = this.suggestedAddress;
                            break;
                        case 'format_error':
                            details.currentFormat = this.currentFormat;
                            details.supportedFormats = this.supportedFormats;
                            break;
                        case 'print_error':
                            details.printerId = this.printerId;
                            details.printError = this.printError;
                            break;
                    }
                    
                    return details;
                }
                return super.details || {};
            },
            
            // 获取恢复建议
            get recoveryTips() {
                if (this.type === "snail") {
                    switch (this.reason) {
                        case 'credit_error':
                            return [
                                _t("Purchase additional Snailmail credits"),
                                _t("Check your account balance"),
                                _t("Consider upgrading your plan")
                            ];
                        case 'address_error':
                            return [
                                _t("Verify the recipient address"),
                                _t("Include all required address fields"),
                                _t("Use standard address format")
                            ];
                        case 'format_error':
                            return [
                                _t("Convert document to PDF format"),
                                _t("Check document size limits"),
                                _t("Ensure document is not corrupted")
                            ];
                        case 'service_error':
                            return [
                                _t("Snailmail service is temporarily down"),
                                _t("Try again later"),
                                _t("Check service status page")
                            ];
                        case 'print_error':
                            return [
                                _t("Document printing failed"),
                                _t("Check document format and content"),
                                _t("Contact support if problem persists")
                            ];
                        case 'postal_error':
                            return [
                                _t("Postal service encountered an error"),
                                _t("Verify recipient address"),
                                _t("Try resending the letter")
                            ];
                        case 'validation_error':
                            return [
                                _t("Document failed validation"),
                                _t("Check document content and format"),
                                _t("Ensure all required fields are filled")
                            ];
                        default:
                            return [
                                _t("Check Snailmail configuration"),
                                _t("Verify recipient information"),
                                _t("Contact administrator if needed")
                            ];
                    }
                }
                return super.recoveryTips || [];
            },
            
            // 获取CSS类名
            get cssClass() {
                if (this.type === "snail") {
                    const baseClass = 'o_snailmail_failure';
                    const severityClass = `o_snailmail_failure_${this.severity}`;
                    const reasonClass = `o_snailmail_failure_${this.reason || 'unknown'}`;
                    
                    return `${baseClass} ${severityClass} ${reasonClass}`;
                }
                return super.cssClass || 'o_failure';
            },
            
            // 检查是否可重试
            get canRetry() {
                if (this.type === "snail") {
                    const retryableReasons = [
                        'service_error',
                        'print_error',
                        'postal_error'
                    ];
                    return retryableReasons.includes(this.reason);
                }
                return super.canRetry || false;
            },
            
            // 检查是否需要用户操作
            get requiresUserAction() {
                if (this.type === "snail") {
                    const userActionReasons = [
                        'credit_error',
                        'address_error',
                        'format_error',
                        'validation_error'
                    ];
                    return userActionReasons.includes(this.reason);
                }
                return super.requiresUserAction || false;
            },
            
            // 获取预计恢复时间
            get estimatedRecoveryTime() {
                if (this.type === "snail") {
                    switch (this.reason) {
                        case 'service_error':
                            return 1800; // 30分钟
                        case 'print_error':
                            return 600; // 10分钟
                        case 'postal_error':
                            return 3600; // 1小时
                        default:
                            return null; // 需要用户干预，无法自动恢复
                    }
                }
                return super.estimatedRecoveryTime || null;
            },
            
            // 获取成本信息
            get costInfo() {
                if (this.type === "snail") {
                    return {
                        estimatedCost: this.cost || 0,
                        currency: this.currency || 'EUR',
                        pages: this.pages || 1,
                        costPerPage: this.costPerPage || 0,
                        additionalFees: this.additionalFees || 0
                    };
                }
                return super.costInfo || {};
            }
        };
        
        // 应用增强的补丁
        patch(Failure.prototype, enhancedFailurePatch);
        
        // 导出增强的补丁
        __exports.enhancedFailurePatch = enhancedFailurePatch;
    }
};

// 应用蜗牛邮件失败模型补丁增强
SnailmailFailureModelPatchEnhancer.enhanceSnailmailFailureModelPatch();
```

## 技术特点

### 1. 补丁机制
- 使用Odoo标准的补丁工具
- 非侵入式的功能扩展
- 保持与原有功能的兼容性

### 2. 类型识别
- 精确的蜗牛邮件类型检测
- 条件性的功能应用
- 默认行为的保持

### 3. 资源管理
- 专门的蜗牛邮件失败图标
- 清晰的资源路径定义
- 统一的资源管理

### 4. 国际化支持
- 完整的多语言支持
- 用户友好的错误消息
- 标准的翻译机制

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 通过补丁为现有模型添加新功能
- 保持原有接口不变

### 2. 策略模式 (Strategy Pattern)
- 根据失败类型选择不同的处理策略
- 灵活的行为定制

### 3. 模板方法模式 (Template Method Pattern)
- 定义失败处理的基本框架
- 允许子类定制特定步骤

## 注意事项

1. **补丁顺序**: 确保补丁在邮件模块加载后应用
2. **类型检查**: 准确识别蜗牛邮件类型的失败
3. **资源路径**: 确保蜗牛邮件图标资源的正确路径
4. **向后兼容**: 保持与现有失败处理的兼容性

## 扩展建议

1. **详细分类**: 根据具体失败原因提供更详细的分类
2. **恢复建议**: 为不同类型的失败提供恢复建议
3. **重试机制**: 实现自动重试机制
4. **统计分析**: 收集和分析蜗牛邮件失败统计数据
5. **成本管理**: 集成成本计算和管理功能

该蜗牛邮件失败模型补丁为蜗牛邮件系统提供了重要的错误处理和用户反馈功能，确保蜗牛邮件发送失败时能够提供准确的信息和适当的视觉提示。

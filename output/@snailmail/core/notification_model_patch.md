# Snailmail Notification Model Patch - 蜗牛邮件通知模型补丁

## 概述

`notification_model_patch.js` 是 Odoo Snailmail 模块的通知模型补丁，专门用于扩展邮件模块的通知模型以支持蜗牛邮件通知显示。该补丁基于Odoo的补丁机制，为蜗牛邮件类型的通知提供了专门的图标、状态图标和状态标题，确保蜗牛邮件通知在用户界面中能够正确显示和识别不同的发送状态。

## 文件信息
- **路径**: `/snailmail/static/src/core/notification_model_patch.js`
- **行数**: 52
- **模块**: `@snailmail/core/notification_model_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/notification_model'  // 邮件通知模型
'@web/core/utils/patch'                  // 补丁工具
'@web/core/l10n/translation'            // 国际化翻译
```

## 核心功能

### 1. 通知模型补丁

```javascript
patch(Notification.prototype, {
    get icon() {
        if (this.notification_type === "snail") {
            return "fa fa-paper-plane";
        }
        return super.icon;
    },
    get statusIcon() {
        // 状态图标逻辑
    },
    get statusTitle() {
        // 状态标题逻辑
    },
});
```

**补丁功能**:
- **类型检测**: 检测通知类型是否为蜗牛邮件
- **图标定制**: 为蜗牛邮件通知提供纸飞机图标
- **状态图标**: 根据发送状态提供不同的状态图标
- **状态标题**: 根据发送状态提供相应的状态标题
- **国际化**: 状态标题支持多语言翻译

### 2. 基础图标获取

```javascript
get icon() {
    if (this.notification_type === "snail") {
        return "fa fa-paper-plane";
    }
    return super.icon;
}
```

**图标功能**:
- **条件判断**: 根据通知类型返回相应图标
- **蜗牛邮件图标**: 蜗牛邮件通知使用纸飞机图标
- **FontAwesome**: 使用FontAwesome图标库
- **默认行为**: 非蜗牛邮件类型使用父类的图标逻辑

### 3. 状态图标获取

```javascript
get statusIcon() {
    if (this.notification_type === "snail") {
        switch (this.notification_status) {
            case "sent":
                return "fa fa-check";
            case "ready":
                return "fa fa-clock-o";
            case "canceled":
                return "fa fa-trash-o";
            default:
                return "fa fa-exclamation text-danger";
        }
    }
    return super.statusIcon;
}
```

**状态图标功能**:
- **状态映射**: 根据通知状态返回相应图标
- **已发送**: 使用勾选图标表示已发送
- **准备中**: 使用时钟图标表示等待发送
- **已取消**: 使用垃圾桶图标表示已取消
- **错误状态**: 使用感叹号图标表示错误，并添加危险色彩

### 4. 状态标题获取

```javascript
get statusTitle() {
    if (this.notification_type === "snail") {
        switch (this.notification_status) {
            case "sent":
                return _t("Sent");
            case "ready":
                return _t("Awaiting Dispatch");
            case "canceled":
                return _t("Cancelled");
            default:
                return _t("Error");
        }
    }
    return super.statusTitle;
}
```

**状态标题功能**:
- **状态描述**: 为每种状态提供清晰的文字描述
- **翻译支持**: 使用翻译函数支持多语言
- **用户友好**: 提供易于理解的状态说明
- **完整覆盖**: 覆盖所有可能的蜗牛邮件状态

## 使用场景

### 1. 蜗牛邮件通知模型补丁增强

```javascript
// 蜗牛邮件通知模型补丁增强功能
const SnailmailNotificationModelPatchEnhancer = {
    enhanceSnailmailNotificationModelPatch: () => {
        // 增强的蜗牛邮件通知模型补丁
        const enhancedNotificationPatch = {
            // 增强的基础图标获取
            get icon() {
                if (this.notification_type === "snail") {
                    // 根据通知子类型返回不同图标
                    switch (this.snailmail_type) {
                        case 'letter':
                            return "fa fa-envelope-o";
                        case 'postcard':
                            return "fa fa-picture-o";
                        case 'package':
                            return "fa fa-cube";
                        default:
                            return "fa fa-paper-plane";
                    }
                }
                return super.icon;
            },
            
            // 增强的状态图标获取
            get statusIcon() {
                if (this.notification_type === "snail") {
                    switch (this.notification_status) {
                        case "sent":
                            return "fa fa-check text-success";
                        case "ready":
                            return "fa fa-clock-o text-warning";
                        case "canceled":
                            return "fa fa-trash-o text-muted";
                        case "processing":
                            return "fa fa-cog fa-spin text-info";
                        case "printed":
                            return "fa fa-print text-primary";
                        case "dispatched":
                            return "fa fa-truck text-info";
                        case "delivered":
                            return "fa fa-check-circle text-success";
                        case "returned":
                            return "fa fa-undo text-warning";
                        case "failed":
                            return "fa fa-times text-danger";
                        default:
                            return "fa fa-exclamation text-danger";
                    }
                }
                return super.statusIcon;
            },
            
            // 增强的状态标题获取
            get statusTitle() {
                if (this.notification_type === "snail") {
                    switch (this.notification_status) {
                        case "sent":
                            return _t("Sent to Printer");
                        case "ready":
                            return _t("Awaiting Dispatch");
                        case "canceled":
                            return _t("Cancelled");
                        case "processing":
                            return _t("Processing");
                        case "printed":
                            return _t("Printed");
                        case "dispatched":
                            return _t("Dispatched");
                        case "delivered":
                            return _t("Delivered");
                        case "returned":
                            return _t("Returned to Sender");
                        case "failed":
                            return _t("Failed");
                        default:
                            return _t("Error");
                    }
                }
                return super.statusTitle;
            },
            
            // 获取详细状态描述
            get statusDescription() {
                if (this.notification_type === "snail") {
                    switch (this.notification_status) {
                        case "sent":
                            return _t("Your letter has been sent to the printing facility");
                        case "ready":
                            return _t("Your letter is ready and awaiting dispatch");
                        case "canceled":
                            return _t("The letter sending has been cancelled");
                        case "processing":
                            return _t("Your letter is being processed");
                        case "printed":
                            return _t("Your letter has been printed successfully");
                        case "dispatched":
                            return _t("Your letter has been dispatched for delivery");
                        case "delivered":
                            return _t("Your letter has been delivered to the recipient");
                        case "returned":
                            return _t("Your letter was returned due to delivery issues");
                        case "failed":
                            return _t("Letter sending failed due to an error");
                        default:
                            return _t("An error occurred during letter processing");
                    }
                }
                return super.statusDescription || '';
            },
            
            // 获取通知颜色
            get color() {
                if (this.notification_type === "snail") {
                    switch (this.notification_status) {
                        case "sent":
                        case "printed":
                        case "delivered":
                            return 'success';
                        case "dispatched":
                        case "processing":
                            return 'info';
                        case "ready":
                        case "returned":
                            return 'warning';
                        case "canceled":
                            return 'muted';
                        case "failed":
                        default:
                            return 'danger';
                    }
                }
                return super.color || 'info';
            },
            
            // 获取通知优先级
            get priority() {
                if (this.notification_type === "snail") {
                    switch (this.notification_status) {
                        case "failed":
                        case "returned":
                            return 'high';
                        case "ready":
                        case "processing":
                            return 'medium';
                        case "sent":
                        case "printed":
                        case "dispatched":
                        case "delivered":
                        case "canceled":
                            return 'low';
                        default:
                            return 'medium';
                    }
                }
                return super.priority || 'medium';
            },
            
            // 获取详细信息
            get details() {
                if (this.notification_type === "snail") {
                    const details = {
                        type: 'Snailmail',
                        subtype: this.snailmail_type || 'letter',
                        status: this.notification_status,
                        recipient: this.res_partner_id?.[1] || this.recipient_address,
                        sentTime: this.create_date,
                        processedTime: this.processed_date,
                        deliveryTime: this.delivery_date,
                        trackingNumber: this.tracking_number,
                        cost: this.cost,
                        pages: this.pages,
                        provider: this.provider
                    };
                    
                    // 添加状态特定信息
                    if (this.notification_status === 'failed') {
                        details.failureReason = this.failure_reason;
                        details.errorCode = this.error_code;
                        details.errorMessage = this.error_message;
                    }
                    
                    if (this.notification_status === 'delivered') {
                        details.deliveryConfirmation = this.delivery_confirmation;
                        details.deliverySignature = this.delivery_signature;
                    }
                    
                    if (this.notification_status === 'returned') {
                        details.returnReason = this.return_reason;
                        details.returnDate = this.return_date;
                    }
                    
                    return details;
                }
                return super.details || {};
            },
            
            // 获取可用操作
            get availableActions() {
                if (this.notification_type === "snail") {
                    const actions = [];
                    
                    // 查看详情
                    actions.push({
                        name: 'view_details',
                        label: _t("View Details"),
                        icon: 'fa-info-circle'
                    });
                    
                    // 跟踪包裹
                    if (this.tracking_number) {
                        actions.push({
                            name: 'track_package',
                            label: _t("Track Package"),
                            icon: 'fa-search'
                        });
                    }
                    
                    // 重新发送（如果失败或返回）
                    if (['failed', 'returned'].includes(this.notification_status)) {
                        actions.push({
                            name: 'resend',
                            label: _t("Resend Letter"),
                            icon: 'fa-refresh'
                        });
                    }
                    
                    // 取消发送（如果还未处理）
                    if (['ready', 'processing'].includes(this.notification_status)) {
                        actions.push({
                            name: 'cancel',
                            label: _t("Cancel Sending"),
                            icon: 'fa-times'
                        });
                    }
                    
                    // 下载收据
                    if (['sent', 'printed', 'dispatched', 'delivered'].includes(this.notification_status)) {
                        actions.push({
                            name: 'download_receipt',
                            label: _t("Download Receipt"),
                            icon: 'fa-download'
                        });
                    }
                    
                    return actions;
                }
                return super.availableActions || [];
            },
            
            // 获取工具提示
            get tooltip() {
                if (this.notification_type === "snail") {
                    let tooltip = this.statusDescription;
                    
                    if (this.res_partner_id) {
                        tooltip += `\n${_t("Recipient")}: ${this.res_partner_id[1]}`;
                    } else if (this.recipient_address) {
                        tooltip += `\n${_t("Address")}: ${this.recipient_address}`;
                    }
                    
                    if (this.create_date) {
                        tooltip += `\n${_t("Sent")}: ${this.create_date}`;
                    }
                    
                    if (this.tracking_number) {
                        tooltip += `\n${_t("Tracking")}: ${this.tracking_number}`;
                    }
                    
                    if (this.cost) {
                        tooltip += `\n${_t("Cost")}: ${this.cost}`;
                    }
                    
                    return tooltip;
                }
                return super.tooltip || '';
            },
            
            // 检查是否可取消
            get canCancel() {
                if (this.notification_type === "snail") {
                    return ['ready', 'processing'].includes(this.notification_status);
                }
                return super.canCancel || false;
            },
            
            // 检查是否可重发
            get canResend() {
                if (this.notification_type === "snail") {
                    return ['failed', 'returned'].includes(this.notification_status);
                }
                return super.canResend || false;
            },
            
            // 检查是否可跟踪
            get canTrack() {
                if (this.notification_type === "snail") {
                    return Boolean(this.tracking_number) && 
                           ['dispatched', 'delivered', 'returned'].includes(this.notification_status);
                }
                return super.canTrack || false;
            },
            
            // 检查是否已完成
            get isCompleted() {
                if (this.notification_type === "snail") {
                    return ['delivered', 'canceled', 'failed'].includes(this.notification_status);
                }
                return super.isCompleted || false;
            },
            
            // 检查是否成功
            get isSuccess() {
                if (this.notification_type === "snail") {
                    return this.notification_status === 'delivered';
                }
                return super.isSuccess || false;
            },
            
            // 检查是否失败
            get isFailed() {
                if (this.notification_type === "snail") {
                    return ['failed', 'returned'].includes(this.notification_status);
                }
                return super.isFailed || false;
            },
            
            // 获取CSS类名
            get cssClass() {
                if (this.notification_type === "snail") {
                    const baseClass = 'o_snailmail_notification';
                    const statusClass = `o_snailmail_notification_${this.notification_status}`;
                    const colorClass = `o_snailmail_notification_${this.color}`;
                    const typeClass = `o_snailmail_notification_${this.snailmail_type || 'letter'}`;
                    
                    return `${baseClass} ${statusClass} ${colorClass} ${typeClass}`;
                }
                return super.cssClass || 'o_notification';
            },
            
            // 获取进度百分比
            get progressPercentage() {
                if (this.notification_type === "snail") {
                    const statusProgress = {
                        'ready': 10,
                        'processing': 25,
                        'printed': 50,
                        'dispatched': 75,
                        'delivered': 100,
                        'canceled': 0,
                        'failed': 0,
                        'returned': 90
                    };
                    
                    return statusProgress[this.notification_status] || 0;
                }
                return super.progressPercentage || 0;
            }
        };
        
        // 应用增强的补丁
        patch(Notification.prototype, enhancedNotificationPatch);
        
        // 导出增强的补丁
        __exports.enhancedNotificationPatch = enhancedNotificationPatch;
    }
};

// 应用蜗牛邮件通知模型补丁增强
SnailmailNotificationModelPatchEnhancer.enhanceSnailmailNotificationModelPatch();
```

## 技术特点

### 1. 补丁机制
- 使用Odoo标准的补丁工具
- 非侵入式的功能扩展
- 保持与原有功能的兼容性

### 2. 状态管理
- 完整的蜗牛邮件状态覆盖
- 清晰的状态图标映射
- 用户友好的状态描述

### 3. 视觉标识
- 专门的蜗牛邮件图标
- 状态相关的图标和颜色
- 统一的视觉风格

### 4. 国际化支持
- 完整的多语言支持
- 用户友好的状态文本
- 标准的翻译机制

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 通过补丁为现有模型添加新功能
- 保持原有接口不变

### 2. 状态模式 (State Pattern)
- 根据通知状态提供不同的行为
- 清晰的状态转换逻辑

### 3. 策略模式 (Strategy Pattern)
- 根据通知类型选择不同的显示策略
- 灵活的视觉定制

## 注意事项

1. **补丁顺序**: 确保补丁在邮件模块加载后应用
2. **状态一致性**: 确保状态图标和标题的一致性
3. **图标可用性**: 确保使用的FontAwesome图标可用
4. **向后兼容**: 保持与现有通知显示的兼容性

## 扩展建议

1. **状态细分**: 提供更详细的蜗牛邮件状态
2. **进度显示**: 添加发送进度的可视化显示
3. **交互操作**: 支持通知的交互操作（取消、重发等）
4. **实时更新**: 实现通知状态的实时更新
5. **成本显示**: 显示蜗牛邮件的发送成本信息

该蜗牛邮件通知模型补丁为蜗牛邮件系统提供了重要的通知显示功能，确保蜗牛邮件通知在用户界面中能够正确识别和显示不同的发送状态。

# Snailmail Messaging Menu Patch - 蜗牛邮件消息菜单补丁

## 概述

`messaging_menu_patch.js` 是 Odoo Snailmail 模块的消息菜单补丁，专门用于扩展邮件模块的消息菜单以支持蜗牛邮件失败查看功能。该补丁基于Odoo的补丁机制，为蜗牛邮件类型的失败提供了专门的查看界面，确保用户能够方便地查看和管理蜗牛邮件发送失败的记录。

## 文件信息
- **路径**: `/snailmail/static/src/messaging_menu/messaging_menu_patch.js`
- **行数**: 33
- **模块**: `@snailmail/messaging_menu/messaging_menu_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/public_web/messaging_menu'  // 邮件消息菜单
'@web/core/l10n/translation'            // 国际化翻译
'@web/core/utils/patch'                  // 补丁工具
```

## 核心功能

### 1. 消息菜单补丁

```javascript
patch(MessagingMenu.prototype, {
    openFailureView(failure) {
        if (failure.type !== "snail") {
            return super.openFailureView(failure);
        }
        this.env.services.action.doAction({
            name: _t("Snailmail Failures"),
            type: "ir.actions.act_window",
            view_mode: "kanban,list,form",
            views: [
                [false, "kanban"],
                [false, "list"],
                [false, "form"],
            ],
            target: "current",
            res_model: failure.resModel,
            domain: [["message_ids.snailmail_error", "=", true]],
        });
        this.dropdown.close();
    },
});
```

**补丁功能**:
- **类型判断**: 区分蜗牛邮件和其他类型的失败
- **非蜗牛邮件处理**: 非蜗牛邮件失败使用原有处理逻辑
- **蜗牛邮件处理**: 蜗牛邮件失败使用专门的查看界面
- **操作执行**: 执行打开蜗牛邮件失败视图的操作
- **菜单关闭**: 操作完成后关闭下拉菜单

### 2. 失败视图打开

```javascript
openFailureView(failure) {
    if (failure.type !== "snail") {
        return super.openFailureView(failure);
    }
    // 蜗牛邮件失败处理逻辑
}
```

**视图打开功能**:
- **条件分支**: 根据失败类型选择处理方式
- **继承调用**: 非蜗牛邮件失败调用父类方法
- **蜗牛邮件特化**: 蜗牛邮件失败使用专门的处理逻辑
- **统一接口**: 保持统一的方法接口

### 3. 蜗牛邮件失败操作配置

```javascript
this.env.services.action.doAction({
    name: _t("Snailmail Failures"),
    type: "ir.actions.act_window",
    view_mode: "kanban,list,form",
    views: [
        [false, "kanban"],
        [false, "list"],
        [false, "form"],
    ],
    target: "current",
    res_model: failure.resModel,
    domain: [["message_ids.snailmail_error", "=", true]],
});
```

**操作配置功能**:
- **窗口标题**: 设置国际化的蜗牛邮件失败窗口标题
- **操作类型**: 使用窗口操作类型
- **视图模式**: 支持看板、列表、表单视图
- **视图配置**: 配置多种视图类型
- **目标设置**: 在当前窗口中打开
- **模型指定**: 使用失败对象的模型
- **域过滤**: 过滤有蜗牛邮件错误的记录

### 4. 菜单状态管理

```javascript
this.dropdown.close();
```

**状态管理功能**:
- **菜单关闭**: 操作完成后关闭下拉菜单
- **用户体验**: 提供流畅的用户交互体验
- **状态清理**: 清理菜单的打开状态

## 使用场景

### 1. 蜗牛邮件消息菜单补丁增强

```javascript
// 蜗牛邮件消息菜单补丁增强功能
const SnailmailMessagingMenuPatchEnhancer = {
    enhanceSnailmailMessagingMenuPatch: () => {
        // 增强的蜗牛邮件消息菜单补丁
        const enhancedMessagingMenuPatch = {
            // 增强的失败视图打开
            openFailureView(failure) {
                if (failure.type !== "snail") {
                    return super.openFailureView(failure);
                }
                
                // 记录用户操作
                this.recordUserAction('view_snailmail_failures', {
                    failureType: failure.type,
                    failureCount: failure.count || 1,
                    timestamp: Date.now()
                });
                
                // 根据失败类型和数量选择最佳视图
                const viewMode = this.getOptimalViewMode(failure);
                
                // 构建增强的操作配置
                const actionConfig = {
                    name: this.getFailureViewTitle(failure),
                    type: "ir.actions.act_window",
                    view_mode: viewMode,
                    views: this.getFailureViews(failure),
                    target: "current",
                    res_model: failure.resModel,
                    domain: this.getFailureDomain(failure),
                    context: this.getFailureContext(failure),
                    // 添加工具栏操作
                    toolbar: this.getFailureToolbar(failure)
                };
                
                this.env.services.action.doAction(actionConfig);
                this.dropdown.close();
                
                // 更新失败统计
                this.updateFailureStats(failure);
            },
            
            // 获取最佳视图模式
            getOptimalViewMode(failure) {
                const failureCount = failure.count || 1;
                
                if (failureCount > 50) {
                    return "kanban,list,form,graph,pivot";
                } else if (failureCount > 10) {
                    return "list,kanban,form,graph";
                } else {
                    return "list,form,kanban";
                }
            },
            
            // 获取失败视图标题
            getFailureViewTitle(failure) {
                const count = failure.count || 1;
                if (count === 1) {
                    return _t("Snailmail Failure");
                } else {
                    return _t("Snailmail Failures (%s)", count);
                }
            },
            
            // 获取失败视图配置
            getFailureViews(failure) {
                const views = [
                    [false, "kanban"],
                    [false, "list"],
                    [false, "form"]
                ];
                
                // 如果失败数量较多，添加统计视图
                if ((failure.count || 1) > 10) {
                    views.push([false, "graph"]);
                    views.push([false, "pivot"]);
                }
                
                return views;
            },
            
            // 获取失败域过滤
            getFailureDomain(failure) {
                const baseDomain = [["message_ids.snailmail_error", "=", true]];
                
                // 添加时间过滤（最近30天）
                const thirtyDaysAgo = moment().subtract(30, 'days').format('YYYY-MM-DD');
                baseDomain.push(["create_date", ">=", thirtyDaysAgo]);
                
                // 根据失败原因添加过滤
                if (failure.reason) {
                    baseDomain.push(["snailmail_failure_reason", "=", failure.reason]);
                }
                
                // 根据失败类型添加过滤
                if (failure.subtype) {
                    baseDomain.push(["snailmail_failure_type", "=", failure.subtype]);
                }
                
                return baseDomain;
            },
            
            // 获取失败上下文
            getFailureContext(failure) {
                return {
                    create: false,
                    edit: false,
                    delete: false,
                    // 默认分组
                    group_by: ['snailmail_failure_reason'],
                    // 搜索默认值
                    search_default_recent: 1,
                    search_default_failed: 1,
                    // 失败类型
                    default_failure_type: failure.type,
                    // 显示选项
                    show_snailmail_details: true,
                    show_retry_button: true,
                    show_failure_analysis: true
                };
            },
            
            // 获取失败工具栏
            getFailureToolbar(failure) {
                return {
                    action: [
                        {
                            name: _t("Retry Failed Letters"),
                            type: "object",
                            method: "retry_failed_letters",
                            icon: "fa-refresh",
                            confirm: _t("Are you sure you want to retry all failed letters?")
                        },
                        {
                            name: _t("Mark as Resolved"),
                            type: "object",
                            method: "mark_snailmail_resolved",
                            icon: "fa-check"
                        },
                        {
                            name: _t("Export Failures"),
                            type: "object",
                            method: "export_snailmail_failures",
                            icon: "fa-download"
                        }
                    ],
                    print: [
                        {
                            name: _t("Snailmail Failure Report"),
                            type: "ir.actions.report",
                            report_name: "snailmail.failure_report",
                            icon: "fa-print"
                        }
                    ]
                };
            },
            
            // 打开蜗牛邮件统计视图
            openSnailmailStatsView() {
                this.env.services.action.doAction({
                    name: _t("Snailmail Statistics"),
                    type: "ir.actions.act_window",
                    view_mode: "graph,pivot,list",
                    views: [
                        [false, "graph"],
                        [false, "pivot"],
                        [false, "list"]
                    ],
                    target: "current",
                    res_model: "snailmail.letter",
                    domain: [],
                    context: {
                        group_by: ['state', 'create_date:month'],
                        search_default_last_month: 1
                    }
                });
                this.dropdown.close();
            },
            
            // 打开蜗牛邮件配置
            openSnailmailConfig() {
                this.env.services.action.doAction({
                    name: _t("Snailmail Configuration"),
                    type: "ir.actions.act_window",
                    view_mode: "form",
                    views: [[false, "form"]],
                    target: "new",
                    res_model: "res.config.settings",
                    context: {
                        module: "snailmail",
                        bin_size: false
                    }
                });
                this.dropdown.close();
            },
            
            // 打开蜗牛邮件模板管理
            openSnailmailTemplates() {
                this.env.services.action.doAction({
                    name: _t("Snailmail Templates"),
                    type: "ir.actions.act_window",
                    view_mode: "list,form",
                    views: [
                        [false, "list"],
                        [false, "form"]
                    ],
                    target: "current",
                    res_model: "snailmail.template",
                    domain: [],
                    context: {
                        create: true,
                        edit: true
                    }
                });
                this.dropdown.close();
            },
            
            // 记录用户操作
            recordUserAction(action, data) {
                try {
                    // 记录用户操作用于分析
                    this.env.services.orm.silent.call(
                        'snailmail.analytics',
                        'record_user_action',
                        [action, data]
                    );
                } catch (error) {
                    console.warn('记录用户操作失败:', error);
                }
            },
            
            // 更新失败统计
            updateFailureStats(failure) {
                try {
                    // 更新失败统计信息
                    this.env.services.orm.silent.call(
                        'snailmail.failure.stats',
                        'update_stats',
                        [failure.type, failure.count || 1]
                    );
                } catch (error) {
                    console.warn('更新失败统计失败:', error);
                }
            },
            
            // 获取蜗牛邮件菜单项
            getSnailmailMenuItems() {
                return [
                    {
                        name: _t("Snailmail Failures"),
                        action: 'openFailureView',
                        icon: 'fa-exclamation-triangle',
                        badge: this.getSnailmailFailureCount()
                    },
                    {
                        name: _t("Snailmail Statistics"),
                        action: 'openSnailmailStatsView',
                        icon: 'fa-bar-chart'
                    },
                    {
                        name: _t("Snailmail Templates"),
                        action: 'openSnailmailTemplates',
                        icon: 'fa-file-text-o'
                    },
                    {
                        name: _t("Snailmail Configuration"),
                        action: 'openSnailmailConfig',
                        icon: 'fa-cog'
                    }
                ];
            },
            
            // 获取蜗牛邮件失败数量
            getSnailmailFailureCount() {
                // 这里应该从实际数据源获取
                return this.messagingService?.snailmailFailureCount || 0;
            },
            
            // 检查蜗牛邮件权限
            hasSnailmailPermission(permission) {
                const user = this.env.services.user;
                switch (permission) {
                    case 'view_failures':
                        return user.hasGroup('snailmail.group_snailmail_user');
                    case 'retry_letters':
                        return user.hasGroup('snailmail.group_snailmail_manager');
                    case 'configure_snailmail':
                        return user.hasGroup('base.group_system');
                    default:
                        return false;
                }
            }
        };
        
        // 应用增强的补丁
        patch(MessagingMenu.prototype, enhancedMessagingMenuPatch);
        
        // 导出增强的补丁
        __exports.enhancedMessagingMenuPatch = enhancedMessagingMenuPatch;
    }
};

// 应用蜗牛邮件消息菜单补丁增强
SnailmailMessagingMenuPatchEnhancer.enhanceSnailmailMessagingMenuPatch();
```

## 技术特点

### 1. 补丁机制
- 使用Odoo标准的补丁工具
- 非侵入式的功能扩展
- 保持与原有功能的兼容性

### 2. 类型区分
- 精确区分蜗牛邮件和其他类型失败
- 条件性的处理逻辑
- 统一的方法接口

### 3. 操作配置
- 完整的窗口操作配置
- 灵活的视图模式设置
- 精确的域过滤条件

### 4. 用户体验
- 流畅的菜单交互
- 清晰的操作反馈
- 国际化的界面文本

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 通过补丁为现有菜单添加新功能
- 保持原有接口不变

### 2. 策略模式 (Strategy Pattern)
- 根据失败类型选择不同的处理策略
- 灵活的操作配置

### 3. 命令模式 (Command Pattern)
- 封装操作为命令对象
- 统一的操作执行接口

## 注意事项

1. **补丁顺序**: 确保补丁在邮件模块加载后应用
2. **类型检查**: 准确识别失败类型
3. **权限控制**: 确保用户有查看失败的权限
4. **性能考虑**: 避免加载过多的失败记录

## 扩展建议

1. **批量操作**: 支持批量处理蜗牛邮件失败
2. **失败分析**: 提供失败原因的统计分析
3. **自动重试**: 实现失败信件的自动重试机制
4. **通知提醒**: 为蜗牛邮件失败提供通知提醒
5. **导出功能**: 支持失败记录的导出功能

该蜗牛邮件消息菜单补丁为蜗牛邮件系统提供了重要的失败管理功能，确保用户能够方便地查看和处理蜗牛邮件发送失败的情况。

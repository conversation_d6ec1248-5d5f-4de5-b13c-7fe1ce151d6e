# Snailmail Message Patch - 蜗牛邮件消息补丁

## 概述

`message_patch.js` 是 Odoo Snailmail 模块的消息补丁，专门用于扩展邮件模块的消息模型以支持蜗牛邮件的失败处理和用户交互。该补丁基于Odoo的补丁机制，为蜗牛邮件类型的消息提供了专门的失败点击处理、错误对话框显示和操作执行，确保蜗牛邮件消息失败时能够触发正确的处理流程。

## 文件信息
- **路径**: `/snailmail/static/src/core_ui/message_patch.js`
- **行数**: 71
- **模块**: `@snailmail/core_ui/message_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/message'                              // 邮件消息模型
'@snailmail/core_ui/snailmail_error'                     // 蜗牛邮件错误组件
'@snailmail/core_ui/snailmail_notification_popover'      // 蜗牛邮件通知弹窗
'@web/core/utils/patch'                                  // 补丁工具
```

## 核心功能

### 1. 消息模型补丁

```javascript
patch(Message.prototype, {
    onClickFailure() {
        if (this.message.message_type === "snailmail") {
            const failureType = this.message.notifications[0].failure_type;
            switch (failureType) {
                case "sn_credit":
                case "sn_trial":
                case "sn_price":
                case "sn_error":
                    this.env.services.dialog.add(SnailmailError, {
                        failureType: failureType,
                        messageId: this.message.id,
                    });
                    break;
                case "sn_fields":
                    this.openMissingFieldsLetterAction();
                    break;
                case "sn_format":
                    this.openFormatLetterAction();
                    break;
            }
        } else {
            super.onClickFailure(...arguments);
        }
    },
});
```

**补丁功能**:
- **类型检测**: 检测消息类型是否为蜗牛邮件
- **失败分类**: 根据失败类型执行不同的处理逻辑
- **对话框显示**: 为特定失败类型显示错误对话框
- **操作执行**: 为字段和格式错误执行专门的操作
- **继承保持**: 非蜗牛邮件消息使用原有处理逻辑

### 2. 失败点击处理

```javascript
onClickFailure() {
    if (this.message.message_type === "snailmail") {
        const failureType = this.message.notifications[0].failure_type;
        // 失败类型处理逻辑
    } else {
        super.onClickFailure(...arguments);
    }
}
```

**点击处理功能**:
- **条件分支**: 根据消息类型选择处理方式
- **失败类型提取**: 从通知中提取失败类型信息
- **蜗牛邮件特化**: 蜗牛邮件使用专门的失败处理
- **参数传递**: 正确传递方法参数

### 3. 失败类型处理

```javascript
switch (failureType) {
    case "sn_credit":
    case "sn_trial":
    case "sn_price":
    case "sn_error":
        this.env.services.dialog.add(SnailmailError, {
            failureType: failureType,
            messageId: this.message.id,
        });
        break;
    case "sn_fields":
        this.openMissingFieldsLetterAction();
        break;
    case "sn_format":
        this.openFormatLetterAction();
        break;
}
```

**失败类型功能**:
- **信用相关**: 信用、试用、价格、错误类型显示错误对话框
- **字段缺失**: 缺失字段类型打开字段补充操作
- **格式错误**: 格式错误类型打开格式修正操作
- **参数传递**: 传递失败类型和消息ID到相应处理

### 4. 缺失字段处理

```javascript
async openMissingFieldsLetterAction() {
    const letterIds = await this.env.services.orm.searchRead(
        "snailmail.letter",
        [["message_id", "=", this.message.id]],
        ["id"]
    );
    this.env.services.action.doAction(
        "snailmail.snailmail_letter_missing_required_fields_action",
        {
            additionalContext: {
                default_letter_id: letterIds[0].id,
            },
        }
    );
}
```

**缺失字段功能**:
- **信件查询**: 根据消息ID查询对应的蜗牛邮件信件
- **操作执行**: 执行缺失字段补充操作
- **上下文传递**: 传递信件ID到操作上下文
- **异步处理**: 使用异步方法处理查询和操作

### 5. 格式错误处理

```javascript
openFormatLetterAction() {
    this.env.services.action.doAction("snailmail.snailmail_letter_format_error_action", {
        additionalContext: {
            message_id: this.message.id,
        },
    });
}
```

**格式错误功能**:
- **操作执行**: 执行格式错误修正操作
- **上下文传递**: 传递消息ID到操作上下文
- **直接调用**: 直接调用格式错误处理操作

### 6. 组件扩展

```javascript
Message.components = {
    ...Message.components,
    Popover: SnailmailNotificationPopover,
    SnailmailError,
};
```

**组件扩展功能**:
- **组件添加**: 添加蜗牛邮件特有的组件
- **弹窗替换**: 使用蜗牛邮件通知弹窗替换默认弹窗
- **错误组件**: 添加蜗牛邮件错误组件
- **继承保持**: 保持原有组件的完整性

## 使用场景

### 1. 蜗牛邮件消息补丁增强

```javascript
// 蜗牛邮件消息补丁增强功能
const SnailmailMessagePatchEnhancer = {
    enhanceSnailmailMessagePatch: () => {
        // 增强的蜗牛邮件消息补丁
        const enhancedMessagePatch = {
            // 增强的失败点击处理
            onClickFailure() {
                if (this.message.message_type === "snailmail") {
                    // 记录失败点击事件
                    this.recordFailureClick();
                    
                    // 检查处理权限
                    if (!this.canHandleFailure()) {
                        this.showPermissionError();
                        return;
                    }
                    
                    const failureType = this.message.notifications[0].failure_type;
                    
                    // 增强的失败类型处理
                    switch (failureType) {
                        case "sn_credit":
                            this.handleCreditFailure();
                            break;
                        case "sn_trial":
                            this.handleTrialFailure();
                            break;
                        case "sn_price":
                            this.handlePriceFailure();
                            break;
                        case "sn_error":
                            this.handleGeneralError();
                            break;
                        case "sn_fields":
                            this.handleMissingFields();
                            break;
                        case "sn_format":
                            this.handleFormatError();
                            break;
                        case "sn_address":
                            this.handleAddressError();
                            break;
                        case "sn_service":
                            this.handleServiceError();
                            break;
                        default:
                            this.handleUnknownError(failureType);
                    }
                } else {
                    super.onClickFailure(...arguments);
                }
            },
            
            // 处理信用失败
            handleCreditFailure() {
                this.env.services.dialog.add(SnailmailError, {
                    failureType: "sn_credit",
                    messageId: this.message.id,
                    additionalData: {
                        currentBalance: this.getCurrentBalance(),
                        requiredCredits: this.getRequiredCredits(),
                        purchaseOptions: this.getPurchaseOptions()
                    }
                });
            },
            
            // 处理试用失败
            handleTrialFailure() {
                this.env.services.dialog.add(SnailmailError, {
                    failureType: "sn_trial",
                    messageId: this.message.id,
                    additionalData: {
                        trialStatus: this.getTrialStatus(),
                        upgradeOptions: this.getUpgradeOptions()
                    }
                });
            },
            
            // 处理价格失败
            handlePriceFailure() {
                this.env.services.dialog.add(SnailmailError, {
                    failureType: "sn_price",
                    messageId: this.message.id,
                    additionalData: {
                        estimatedCost: this.getEstimatedCost(),
                        pricingDetails: this.getPricingDetails()
                    }
                });
            },
            
            // 处理一般错误
            handleGeneralError() {
                this.env.services.dialog.add(SnailmailError, {
                    failureType: "sn_error",
                    messageId: this.message.id,
                    additionalData: {
                        errorDetails: this.getErrorDetails(),
                        troubleshootingSteps: this.getTroubleshootingSteps()
                    }
                });
            },
            
            // 处理缺失字段
            async handleMissingFields() {
                try {
                    // 获取缺失字段信息
                    const missingFields = await this.getMissingFields();
                    
                    // 显示字段补充界面
                    await this.openMissingFieldsLetterAction();
                    
                    // 记录处理事件
                    this.recordFieldsHandling(missingFields);
                } catch (error) {
                    console.error('处理缺失字段失败:', error);
                    this.showErrorMessage('无法处理缺失字段');
                }
            },
            
            // 处理格式错误
            handleFormatError() {
                // 获取格式错误详情
                const formatDetails = this.getFormatErrorDetails();
                
                // 显示格式修正界面
                this.openFormatLetterAction();
                
                // 记录格式错误处理
                this.recordFormatErrorHandling(formatDetails);
            },
            
            // 处理地址错误
            handleAddressError() {
                this.env.services.dialog.add(SnailmailError, {
                    failureType: "sn_address",
                    messageId: this.message.id,
                    additionalData: {
                        addressIssues: this.getAddressIssues(),
                        suggestedAddresses: this.getSuggestedAddresses(),
                        validationRules: this.getAddressValidationRules()
                    }
                });
            },
            
            // 处理服务错误
            handleServiceError() {
                this.env.services.dialog.add(SnailmailError, {
                    failureType: "sn_service",
                    messageId: this.message.id,
                    additionalData: {
                        serviceStatus: this.getServiceStatus(),
                        alternativeOptions: this.getAlternativeOptions(),
                        retryOptions: this.getRetryOptions()
                    }
                });
            },
            
            // 处理未知错误
            handleUnknownError(failureType) {
                console.warn('未知的蜗牛邮件失败类型:', failureType);
                
                this.env.services.dialog.add(SnailmailError, {
                    failureType: "sn_error",
                    messageId: this.message.id,
                    additionalData: {
                        unknownType: failureType,
                        supportContact: this.getSupportContact()
                    }
                });
            },
            
            // 增强的缺失字段处理
            async openMissingFieldsLetterAction() {
                try {
                    // 显示加载状态
                    this.showLoadingState(true);
                    
                    // 查询信件信息
                    const letterIds = await this.env.services.orm.searchRead(
                        "snailmail.letter",
                        [["message_id", "=", this.message.id]],
                        ["id", "missing_required_fields", "partner_id"]
                    );
                    
                    if (letterIds.length === 0) {
                        throw new Error('未找到对应的蜗牛邮件信件');
                    }
                    
                    const letter = letterIds[0];
                    
                    // 执行操作
                    this.env.services.action.doAction(
                        "snailmail.snailmail_letter_missing_required_fields_action",
                        {
                            additionalContext: {
                                default_letter_id: letter.id,
                                missing_fields: letter.missing_required_fields,
                                partner_id: letter.partner_id
                            },
                        }
                    );
                    
                } catch (error) {
                    console.error('打开缺失字段操作失败:', error);
                    this.showErrorMessage('无法打开字段补充界面');
                } finally {
                    this.showLoadingState(false);
                }
            },
            
            // 增强的格式错误处理
            openFormatLetterAction() {
                try {
                    // 获取格式错误详情
                    const formatError = this.getFormatErrorDetails();
                    
                    this.env.services.action.doAction("snailmail.snailmail_letter_format_error_action", {
                        additionalContext: {
                            message_id: this.message.id,
                            format_error: formatError,
                            suggested_formats: this.getSuggestedFormats(),
                            conversion_options: this.getConversionOptions()
                        },
                    });
                    
                } catch (error) {
                    console.error('打开格式错误操作失败:', error);
                    this.showErrorMessage('无法打开格式修正界面');
                }
            },
            
            // 检查处理权限
            canHandleFailure() {
                const user = this.env.services.user;
                
                // 检查基本蜗牛邮件权限
                if (!user.hasGroup('snailmail.group_snailmail_user')) {
                    return false;
                }
                
                // 检查消息所有权
                if (this.message.author_id && 
                    this.message.author_id[0] !== user.userId &&
                    !user.hasGroup('snailmail.group_snailmail_manager')) {
                    return false;
                }
                
                return true;
            },
            
            // 获取当前余额
            getCurrentBalance() {
                // 这里应该从实际数据源获取
                return this.message.snailmail_credit_balance || 0;
            },
            
            // 获取所需信用
            getRequiredCredits() {
                return this.message.snailmail_required_credits || 1;
            },
            
            // 获取购买选项
            getPurchaseOptions() {
                return [
                    { credits: 10, price: 5.00 },
                    { credits: 50, price: 20.00 },
                    { credits: 100, price: 35.00 }
                ];
            },
            
            // 获取缺失字段
            async getMissingFields() {
                try {
                    const result = await this.env.services.orm.call(
                        'snailmail.letter',
                        'get_missing_fields',
                        [this.message.id]
                    );
                    return result;
                } catch (error) {
                    console.error('获取缺失字段失败:', error);
                    return [];
                }
            },
            
            // 获取格式错误详情
            getFormatErrorDetails() {
                return {
                    currentFormat: this.message.snailmail_format || 'unknown',
                    supportedFormats: ['PDF', 'DOC', 'DOCX'],
                    errorMessage: this.message.snailmail_format_error || '格式不支持'
                };
            },
            
            // 显示权限错误
            showPermissionError() {
                this.env.services.notification.add(
                    _t("You don't have permission to handle Snailmail failures"),
                    { type: 'error' }
                );
            },
            
            // 显示错误消息
            showErrorMessage(message) {
                this.env.services.notification.add(message, { type: 'error' });
            },
            
            // 显示加载状态
            showLoadingState(loading) {
                // 更新UI显示加载状态
                const failureElement = this.el?.querySelector('.o_message_failure');
                if (failureElement) {
                    if (loading) {
                        failureElement.classList.add('o_loading');
                    } else {
                        failureElement.classList.remove('o_loading');
                    }
                }
            },
            
            // 记录失败点击事件
            recordFailureClick() {
                try {
                    this.env.services.orm.silent.call(
                        'snailmail.analytics',
                        'record_failure_click',
                        [{
                            message_id: this.message.id,
                            failure_type: this.message.notifications[0].failure_type,
                            click_timestamp: Date.now(),
                            user_id: this.env.services.user.userId
                        }]
                    );
                } catch (error) {
                    console.warn('记录失败点击事件失败:', error);
                }
            }
        };
        
        // 应用增强的补丁
        patch(Message.prototype, enhancedMessagePatch);
        
        // 导出增强的补丁
        __exports.enhancedMessagePatch = enhancedMessagePatch;
    }
};

// 应用蜗牛邮件消息补丁增强
SnailmailMessagePatchEnhancer.enhanceSnailmailMessagePatch();
```

## 技术特点

### 1. 补丁机制
- 使用Odoo标准的补丁工具
- 非侵入式的功能扩展
- 保持与原有功能的兼容性

### 2. 失败分类
- 精确的失败类型识别
- 不同失败类型的专门处理
- 灵活的处理策略

### 3. 组件集成
- 深度集成蜗牛邮件特有组件
- 无缝的组件替换和扩展
- 统一的组件管理

### 4. 异步处理
- 优雅的异步操作处理
- 完善的错误处理机制
- 用户友好的加载状态

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 通过补丁为现有消息添加新功能
- 保持原有接口不变

### 2. 策略模式 (Strategy Pattern)
- 根据失败类型选择不同的处理策略
- 灵活的失败处理机制

### 3. 工厂方法模式 (Factory Method Pattern)
- 根据失败类型创建相应的处理方法
- 统一的创建接口

## 注意事项

1. **补丁顺序**: 确保补丁在邮件模块加载后应用
2. **类型检查**: 准确识别蜗牛邮件消息类型
3. **权限控制**: 确保用户有处理失败的权限
4. **错误处理**: 提供完善的错误处理和用户反馈

## 扩展建议

1. **失败预防**: 实现失败预防和早期检测机制
2. **批量处理**: 支持批量处理蜗牛邮件失败
3. **自动修复**: 实现某些失败类型的自动修复
4. **失败分析**: 提供失败原因的详细分析和统计
5. **用户指导**: 提供更详细的用户操作指导

该蜗牛邮件消息补丁为蜗牛邮件系统提供了重要的失败处理和用户交互功能，确保蜗牛邮件消息失败时能够提供准确的处理流程和用户引导。

# Snailmail Notification Popover - 蜗牛邮件通知弹窗组件

## 概述

`snailmail_notification_popover.js` 是 Odoo Snailmail 模块的通知弹窗组件，专门用于显示蜗牛邮件相关的通知信息。该组件基于OWL框架，提供了简洁而专业的弹窗界面，用于展示蜗牛邮件的状态、详情和相关操作，是蜗牛邮件通知系统的重要用户界面组件。

## 文件信息
- **路径**: `/snailmail/static/src/core_ui/snailmail_notification_popover.js`
- **行数**: 14
- **模块**: `@snailmail/core_ui/snailmail_notification_popover`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'     // OWL框架
```

## 核心功能

### 1. SnailmailNotificationPopover 组件

```javascript
const SnailmailNotificationPopover = class SnailmailNotificationPopover extends Component {
    static template = "snailmail.SnailmailNotificationPopover";
    static props = ["message", "close?"];
}
```

**组件特性**:
- **继承扩展**: 继承OWL Component的所有功能
- **模板绑定**: 使用专门的蜗牛邮件通知弹窗模板
- **属性定义**: 接收消息对象和可选的关闭回调
- **简洁设计**: 最小化的组件实现，专注于显示功能

### 2. 属性配置

```javascript
static props = ["message", "close?"];
```

**属性功能**:
- **消息对象**: 接收要显示的蜗牛邮件消息
- **关闭回调**: 可选的弹窗关闭回调函数
- **类型安全**: 明确的属性类型定义
- **灵活配置**: 支持可选属性的灵活配置

### 3. 模板系统

```javascript
static template = "snailmail.SnailmailNotificationPopover";
```

**模板功能**:
- **专用模板**: 使用蜗牛邮件专用的弹窗模板
- **模板绑定**: 与OWL模板系统的深度集成
- **样式统一**: 保持与蜗牛邮件系统的视觉一致性
- **内容展示**: 专门用于展示蜗牛邮件通知内容

## 使用场景

### 1. 蜗牛邮件通知弹窗组件增强

```javascript
// 蜗牛邮件通知弹窗组件增强功能
const SnailmailNotificationPopoverEnhancer = {
    enhanceSnailmailNotificationPopover: () => {
        // 增强的蜗牛邮件通知弹窗组件
        class EnhancedSnailmailNotificationPopover extends SnailmailNotificationPopover {
            static props = {
                ...SnailmailNotificationPopover.props,
                showDetails: { type: Boolean, optional: true },
                showActions: { type: Boolean, optional: true },
                enableTracking: { type: Boolean, optional: true },
                customActions: { type: Array, optional: true },
                theme: { type: String, optional: true },
                position: { type: String, optional: true },
                autoClose: { type: Boolean, optional: true },
                closeDelay: { type: Number, optional: true }
            };
            
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    showDetails: this.props.showDetails !== false,
                    showActions: this.props.showActions !== false,
                    enableTracking: this.props.enableTracking !== false,
                    theme: this.props.theme || 'default',
                    position: this.props.position || 'auto',
                    autoClose: this.props.autoClose || false,
                    closeDelay: this.props.closeDelay || 5000,
                    enableAnimations: true,
                    enableSound: false,
                    enableKeyboardShortcuts: true
                };
                
                // 增强的状态
                this.enhancedState = useState({
                    isExpanded: false,
                    isLoading: false,
                    trackingInfo: null,
                    actionHistory: [],
                    lastUpdate: null,
                    userInteractions: 0
                });
                
                // 服务集成
                this.orm = useService("orm");
                this.notification = useService("notification");
                this.user = useService("user");
                
                // 跟踪服务
                this.trackingService = new SnailmailTrackingService();
                
                // 动画控制器
                this.animationController = new PopoverAnimationController();
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 加载跟踪信息
                if (this.enhancedConfig.enableTracking) {
                    this.loadTrackingInfo();
                }
                
                // 设置自动关闭
                if (this.enhancedConfig.autoClose) {
                    this.setupAutoClose();
                }
                
                // 设置键盘快捷键
                if (this.enhancedConfig.enableKeyboardShortcuts) {
                    this.setupKeyboardShortcuts();
                }
                
                // 记录弹窗显示
                this.recordPopoverDisplay();
            }
            
            // 加载跟踪信息
            async loadTrackingInfo() {
                if (!this.message.snailmail_letter_id) {
                    return;
                }
                
                try {
                    this.enhancedState.isLoading = true;
                    
                    const trackingInfo = await this.trackingService.getTrackingInfo(
                        this.message.snailmail_letter_id
                    );
                    
                    this.enhancedState.trackingInfo = trackingInfo;
                    this.enhancedState.lastUpdate = Date.now();
                    
                } catch (error) {
                    console.error('加载跟踪信息失败:', error);
                } finally {
                    this.enhancedState.isLoading = false;
                }
            },
            
            // 设置自动关闭
            setupAutoClose() {
                this.autoCloseTimer = setTimeout(() => {
                    if (this.props.close) {
                        this.props.close();
                    }
                }, this.enhancedConfig.closeDelay);
            }
            
            // 设置键盘快捷键
            setupKeyboardShortcuts() {
                this.keyboardHandler = (event) => {
                    switch (event.key) {
                        case 'Escape':
                            this.onClose();
                            break;
                        case 'Enter':
                            this.onExpand();
                            break;
                        case 'r':
                            if (event.ctrlKey) {
                                this.onRefresh();
                                event.preventDefault();
                            }
                            break;
                        case 't':
                            if (event.ctrlKey) {
                                this.onToggleTracking();
                                event.preventDefault();
                            }
                            break;
                    }
                };
                
                document.addEventListener('keydown', this.keyboardHandler);
            }
            
            // 展开/收起详情
            onExpand() {
                this.enhancedState.isExpanded = !this.enhancedState.isExpanded;
                this.enhancedState.userInteractions++;
                
                // 播放动画
                if (this.enhancedConfig.enableAnimations) {
                    this.animationController.playExpandAnimation(this.enhancedState.isExpanded);
                }
                
                // 记录交互
                this.recordUserInteraction('expand', this.enhancedState.isExpanded);
            }
            
            // 刷新信息
            async onRefresh() {
                this.enhancedState.userInteractions++;
                
                // 重新加载跟踪信息
                if (this.enhancedConfig.enableTracking) {
                    await this.loadTrackingInfo();
                }
                
                // 显示刷新通知
                this.notification.add(_t("信息已刷新"), { type: 'info' });
                
                // 记录交互
                this.recordUserInteraction('refresh');
            }
            
            // 切换跟踪显示
            onToggleTracking() {
                this.enhancedConfig.enableTracking = !this.enhancedConfig.enableTracking;
                this.enhancedState.userInteractions++;
                
                if (this.enhancedConfig.enableTracking) {
                    this.loadTrackingInfo();
                } else {
                    this.enhancedState.trackingInfo = null;
                }
                
                // 记录交互
                this.recordUserInteraction('toggle_tracking', this.enhancedConfig.enableTracking);
            }
            
            // 关闭弹窗
            onClose() {
                // 清理定时器
                if (this.autoCloseTimer) {
                    clearTimeout(this.autoCloseTimer);
                }
                
                // 记录关闭事件
                this.recordPopoverClose();
                
                // 执行关闭回调
                if (this.props.close) {
                    this.props.close();
                }
            }
            
            // 执行自定义操作
            async onCustomAction(action) {
                try {
                    this.enhancedState.userInteractions++;
                    
                    // 记录操作历史
                    this.enhancedState.actionHistory.push({
                        action: action.name,
                        timestamp: Date.now(),
                        success: false // 先设为false，成功后更新
                    });
                    
                    // 执行操作
                    let result;
                    if (typeof action.handler === 'function') {
                        result = await action.handler(this.message);
                    } else if (action.method) {
                        result = await this.orm.call(
                            action.model || 'snailmail.letter',
                            action.method,
                            [this.message.snailmail_letter_id],
                            action.kwargs || {}
                        );
                    }
                    
                    // 更新操作历史
                    const lastAction = this.enhancedState.actionHistory[this.enhancedState.actionHistory.length - 1];
                    lastAction.success = true;
                    lastAction.result = result;
                    
                    // 显示成功通知
                    this.notification.add(
                        action.successMessage || _t("操作执行成功"),
                        { type: 'success' }
                    );
                    
                    // 记录操作
                    this.recordCustomAction(action.name, true);
                    
                } catch (error) {
                    console.error('执行自定义操作失败:', error);
                    
                    // 更新操作历史
                    const lastAction = this.enhancedState.actionHistory[this.enhancedState.actionHistory.length - 1];
                    lastAction.error = error.message;
                    
                    // 显示错误通知
                    this.notification.add(
                        action.errorMessage || _t("操作执行失败"),
                        { type: 'error' }
                    );
                    
                    // 记录操作
                    this.recordCustomAction(action.name, false, error.message);
                }
            }
            
            // 获取消息状态信息
            get messageStatusInfo() {
                if (!this.message) return null;
                
                const status = this.message.snailmail_status || 'unknown';
                const statusMap = {
                    'ready': { label: _t("准备发送"), color: 'warning', icon: 'fa-clock-o' },
                    'sent': { label: _t("已发送"), color: 'info', icon: 'fa-paper-plane' },
                    'delivered': { label: _t("已投递"), color: 'success', icon: 'fa-check-circle' },
                    'error': { label: _t("发送失败"), color: 'danger', icon: 'fa-exclamation-triangle' },
                    'canceled': { label: _t("已取消"), color: 'muted', icon: 'fa-times-circle' }
                };
                
                return statusMap[status] || statusMap['unknown'];
            }
            
            // 获取跟踪状态
            get trackingStatus() {
                if (!this.enhancedState.trackingInfo) {
                    return null;
                }
                
                return {
                    trackingNumber: this.enhancedState.trackingInfo.tracking_number,
                    currentStatus: this.enhancedState.trackingInfo.current_status,
                    estimatedDelivery: this.enhancedState.trackingInfo.estimated_delivery,
                    lastUpdate: this.enhancedState.trackingInfo.last_update
                };
            }
            
            // 获取可用操作
            get availableActions() {
                const actions = [];
                
                // 基础操作
                actions.push({
                    name: 'refresh',
                    label: _t("刷新"),
                    icon: 'fa-refresh',
                    handler: () => this.onRefresh()
                });
                
                // 跟踪操作
                if (this.enhancedState.trackingInfo?.tracking_number) {
                    actions.push({
                        name: 'track',
                        label: _t("跟踪包裹"),
                        icon: 'fa-search',
                        handler: () => this.openTrackingPage()
                    });
                }
                
                // 自定义操作
                if (this.props.customActions) {
                    actions.push(...this.props.customActions);
                }
                
                return actions;
            }
            
            // 打开跟踪页面
            openTrackingPage() {
                const trackingNumber = this.enhancedState.trackingInfo?.tracking_number;
                if (trackingNumber) {
                    const trackingUrl = `https://tracking.example.com/${trackingNumber}`;
                    window.open(trackingUrl, '_blank');
                    
                    this.recordUserInteraction('open_tracking');
                }
            }
            
            // 记录弹窗显示
            recordPopoverDisplay() {
                try {
                    this.orm.silent.call(
                        'snailmail.analytics',
                        'record_popover_display',
                        [{
                            message_id: this.message.id,
                            letter_id: this.message.snailmail_letter_id,
                            display_timestamp: Date.now(),
                            user_id: this.user.userId,
                            theme: this.enhancedConfig.theme,
                            position: this.enhancedConfig.position
                        }]
                    );
                } catch (error) {
                    console.warn('记录弹窗显示失败:', error);
                }
            }
            
            // 记录弹窗关闭
            recordPopoverClose() {
                try {
                    this.orm.silent.call(
                        'snailmail.analytics',
                        'record_popover_close',
                        [{
                            message_id: this.message.id,
                            close_timestamp: Date.now(),
                            user_interactions: this.enhancedState.userInteractions,
                            time_spent: Date.now() - (this.enhancedState.lastUpdate || Date.now()),
                            user_id: this.user.userId
                        }]
                    );
                } catch (error) {
                    console.warn('记录弹窗关闭失败:', error);
                }
            }
            
            // 记录用户交互
            recordUserInteraction(action, data = null) {
                try {
                    this.orm.silent.call(
                        'snailmail.analytics',
                        'record_user_interaction',
                        [{
                            message_id: this.message.id,
                            action: action,
                            data: data,
                            interaction_timestamp: Date.now(),
                            user_id: this.user.userId
                        }]
                    );
                } catch (error) {
                    console.warn('记录用户交互失败:', error);
                }
            }
            
            // 记录自定义操作
            recordCustomAction(actionName, success, errorMessage = null) {
                try {
                    this.orm.silent.call(
                        'snailmail.analytics',
                        'record_custom_action',
                        [{
                            message_id: this.message.id,
                            action_name: actionName,
                            success: success,
                            error_message: errorMessage,
                            action_timestamp: Date.now(),
                            user_id: this.user.userId
                        }]
                    );
                } catch (error) {
                    console.warn('记录自定义操作失败:', error);
                }
            }
            
            // 组件销毁时清理
            willDestroy() {
                // 清理定时器
                if (this.autoCloseTimer) {
                    clearTimeout(this.autoCloseTimer);
                }
                
                // 清理键盘监听
                if (this.keyboardHandler) {
                    document.removeEventListener('keydown', this.keyboardHandler);
                }
                
                super.willDestroy && super.willDestroy();
            }
        };
        
        // 蜗牛邮件跟踪服务
        class SnailmailTrackingService {
            async getTrackingInfo(letterId) {
                // 获取跟踪信息
                return {
                    tracking_number: 'SN123456789',
                    current_status: 'in_transit',
                    estimated_delivery: '2024-01-15',
                    last_update: Date.now()
                };
            }
        }
        
        // 弹窗动画控制器
        class PopoverAnimationController {
            playExpandAnimation(expanded) {
                // 播放展开/收起动画
                console.log('播放动画:', expanded ? '展开' : '收起');
            }
        }
        
        // 导出增强的组件
        __exports.EnhancedSnailmailNotificationPopover = EnhancedSnailmailNotificationPopover;
    }
};

// 应用蜗牛邮件通知弹窗组件增强
SnailmailNotificationPopoverEnhancer.enhanceSnailmailNotificationPopover();
```

## 技术特点

### 1. 组件设计
- 基于OWL框架的轻量级组件
- 简洁的属性定义和模板绑定
- 专注于显示功能的设计

### 2. 模板系统
- 专用的蜗牛邮件弹窗模板
- 与OWL模板系统的深度集成
- 灵活的内容展示能力

### 3. 属性配置
- 清晰的属性类型定义
- 支持可选属性的灵活配置
- 简洁而功能完整的接口

### 4. 扩展性
- 易于扩展的组件架构
- 支持自定义功能的添加
- 保持与系统的一致性

## 设计模式

### 1. 组件模式 (Component Pattern)
- 封装的UI组件
- 可重用的界面元素

### 2. 模板模式 (Template Pattern)
- 定义弹窗的基本结构
- 允许内容的灵活定制

### 3. 观察者模式 (Observer Pattern)
- 响应消息状态的变化
- 动态更新弹窗内容

## 注意事项

1. **性能优化**: 避免不必要的重渲染和计算
2. **用户体验**: 提供流畅的弹窗交互体验
3. **内容适配**: 确保内容在不同屏幕尺寸下的适配
4. **可访问性**: 支持键盘导航和屏幕阅读器

## 扩展建议

1. **交互增强**: 添加更多的用户交互功能
2. **动画效果**: 实现流畅的弹窗动画效果
3. **内容丰富**: 显示更详细的蜗牛邮件信息
4. **操作集成**: 集成更多的蜗牛邮件操作
5. **主题定制**: 支持不同的视觉主题和样式

该蜗牛邮件通知弹窗组件为蜗牛邮件系统提供了重要的通知显示功能，确保用户能够方便地查看蜗牛邮件的状态和详细信息。

# Snailmail Error Component - 蜗牛邮件错误组件

## 概述

`snailmail_error.js` 是 Odoo Snailmail 模块的错误对话框组件，专门用于处理和显示蜗牛邮件发送过程中的各种错误情况。该组件基于OWL框架和Web核心服务，集成了信用获取、信件重发、信件取消等核心功能，为蜗牛邮件系统提供了完整的错误处理用户界面，是蜗牛邮件错误管理的重要组件。

## 文件信息
- **路径**: `/snailmail/static/src/core_ui/snailmail_error.js`
- **行数**: 46
- **模块**: `@snailmail/core_ui/snailmail_error`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                     // OWL框架
'@web/core/dialog/dialog'       // 对话框组件
'@web/core/utils/hooks'         // 钩子工具
```

## 核心功能

### 1. SnailmailError 组件

```javascript
const SnailmailError = class SnailmailError extends Component {
    static components = { Dialog };
    static props = ["close", "failureType", "messageId"];
    static template = "snailmail.SnailmailError";
}
```

**组件特性**:
- **继承扩展**: 继承OWL Component的所有功能
- **对话框集成**: 集成标准对话框组件
- **属性定义**: 接收关闭回调、失败类型和消息ID
- **模板绑定**: 使用专门的蜗牛邮件错误模板

### 2. 组件初始化

```javascript
setup() {
    this.orm = useService("orm");
}
```

**初始化功能**:
- **服务集成**: 集成ORM服务进行数据操作
- **钩子使用**: 使用useService钩子获取服务
- **简洁设计**: 最小化的初始化配置
- **服务准备**: 为后续操作准备必要的服务

### 3. 信用URL获取

```javascript
async fetchSnailmailCreditsUrl() {
    return await this.orm.call("iap.account", "get_credits_url", ["snailmail"]);
}

async fetchSnailmailCreditsUrlTrial() {
    return await this.orm.call("iap.account", "get_credits_url", ["snailmail", "", 0, true]);
}
```

**信用URL功能**:
- **正式信用**: 获取正式版蜗牛邮件信用购买URL
- **试用信用**: 获取试用版蜗牛邮件信用URL
- **异步调用**: 使用异步方法调用IAP账户服务
- **参数传递**: 传递正确的服务标识和试用标志

### 4. 信件重发操作

```javascript
async onClickResendLetter() {
    await this.orm.call("mail.message", "send_letter", [[this.props.messageId]]);
    this.props.close();
}
```

**重发功能**:
- **异步操作**: 使用异步方法重发信件
- **消息调用**: 调用邮件消息的发送信件方法
- **参数传递**: 传递消息ID到重发方法
- **对话框关闭**: 操作完成后关闭对话框

### 5. 信件取消操作

```javascript
async onClickCancelLetter() {
    await this.orm.call("mail.message", "cancel_letter", [[this.props.messageId]]);
    this.props.close();
}
```

**取消功能**:
- **异步操作**: 使用异步方法取消信件
- **消息调用**: 调用邮件消息的取消信件方法
- **参数传递**: 传递消息ID到取消方法
- **对话框关闭**: 操作完成后关闭对话框

### 6. 计算属性

```javascript
get snailmailCreditsUrl() {
    return this.fetchSnailmailCreditsUrl();
}

get snailmailCreditsUrlTrial() {
    return this.fetchSnailmailCreditsUrlTrial();
}
```

**计算属性功能**:
- **URL获取**: 提供信用URL的计算属性
- **试用URL**: 提供试用信用URL的计算属性
- **异步支持**: 支持异步的URL获取
- **模板绑定**: 便于在模板中使用

## 使用场景

### 1. 蜗牛邮件错误组件增强

```javascript
// 蜗牛邮件错误组件增强功能
const SnailmailErrorEnhancer = {
    enhanceSnailmailError: () => {
        // 增强的蜗牛邮件错误组件
        class EnhancedSnailmailError extends SnailmailError {
            static props = {
                ...SnailmailError.props,
                additionalData: { type: Object, optional: true },
                enableRetry: { type: Boolean, optional: true },
                enableCancel: { type: Boolean, optional: true },
                enableSupport: { type: Boolean, optional: true },
                customActions: { type: Array, optional: true }
            };
            
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    enableRetry: this.props.enableRetry !== false,
                    enableCancel: this.props.enableCancel !== false,
                    enableSupport: this.props.enableSupport !== false,
                    enableAnalytics: true,
                    enableNotifications: true,
                    enableAutoRetry: false,
                    maxRetryAttempts: 3,
                    retryDelay: 5000
                };
                
                // 增强的状态
                this.enhancedState = useState({
                    isProcessing: false,
                    retryCount: 0,
                    errorDetails: null,
                    supportTicketId: null,
                    lastAction: null,
                    actionHistory: []
                });
                
                // 通知服务
                this.notification = useService("notification");
                
                // 用户服务
                this.user = useService("user");
                
                // 错误分析器
                this.errorAnalyzer = new SnailmailErrorAnalyzer();
                
                // 支持服务
                this.supportService = new SnailmailSupportService();
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 分析错误详情
                this.analyzeError();
                
                // 加载用户偏好
                this.loadUserPreferences();
                
                // 记录错误显示事件
                this.recordErrorDisplay();
            }
            
            // 分析错误详情
            async analyzeError() {
                try {
                    const errorDetails = await this.errorAnalyzer.analyze({
                        failureType: this.props.failureType,
                        messageId: this.props.messageId,
                        additionalData: this.props.additionalData
                    });
                    
                    this.enhancedState.errorDetails = errorDetails;
                } catch (error) {
                    console.error('分析错误详情失败:', error);
                }
            },
            
            // 增强的信用URL获取
            async fetchSnailmailCreditsUrl() {
                try {
                    const url = await this.orm.call("iap.account", "get_credits_url", ["snailmail"]);
                    
                    // 记录信用URL获取
                    this.recordCreditUrlAccess('normal');
                    
                    return url;
                } catch (error) {
                    console.error('获取信用URL失败:', error);
                    this.showErrorNotification('无法获取信用购买链接');
                    return null;
                }
            },
            
            // 增强的试用信用URL获取
            async fetchSnailmailCreditsUrlTrial() {
                try {
                    const url = await this.orm.call("iap.account", "get_credits_url", ["snailmail", "", 0, true]);
                    
                    // 记录试用URL获取
                    this.recordCreditUrlAccess('trial');
                    
                    return url;
                } catch (error) {
                    console.error('获取试用信用URL失败:', error);
                    this.showErrorNotification('无法获取试用信用链接');
                    return null;
                }
            },
            
            // 增强的信件重发操作
            async onClickResendLetter() {
                if (!this.enhancedConfig.enableRetry) {
                    this.showErrorNotification('重发功能已禁用');
                    return;
                }
                
                try {
                    this.enhancedState.isProcessing = true;
                    this.enhancedState.lastAction = 'resend';
                    
                    // 检查重发限制
                    if (!this.canRetry()) {
                        this.showErrorNotification('已达到最大重发次数');
                        return;
                    }
                    
                    // 执行重发
                    await this.orm.call("mail.message", "send_letter", [[this.props.messageId]]);
                    
                    // 更新状态
                    this.enhancedState.retryCount++;
                    this.enhancedState.actionHistory.push({
                        action: 'resend',
                        timestamp: Date.now(),
                        success: true
                    });
                    
                    // 显示成功通知
                    this.showSuccessNotification('信件重发成功');
                    
                    // 记录重发事件
                    this.recordResendAction(true);
                    
                    // 关闭对话框
                    this.props.close();
                    
                } catch (error) {
                    console.error('重发信件失败:', error);
                    
                    // 记录失败
                    this.enhancedState.actionHistory.push({
                        action: 'resend',
                        timestamp: Date.now(),
                        success: false,
                        error: error.message
                    });
                    
                    // 显示错误通知
                    this.showErrorNotification('重发信件失败: ' + error.message);
                    
                    // 记录重发事件
                    this.recordResendAction(false, error.message);
                    
                } finally {
                    this.enhancedState.isProcessing = false;
                }
            },
            
            // 增强的信件取消操作
            async onClickCancelLetter() {
                if (!this.enhancedConfig.enableCancel) {
                    this.showErrorNotification('取消功能已禁用');
                    return;
                }
                
                try {
                    this.enhancedState.isProcessing = true;
                    this.enhancedState.lastAction = 'cancel';
                    
                    // 确认取消操作
                    const confirmed = await this.confirmCancelAction();
                    if (!confirmed) {
                        return;
                    }
                    
                    // 执行取消
                    await this.orm.call("mail.message", "cancel_letter", [[this.props.messageId]]);
                    
                    // 更新状态
                    this.enhancedState.actionHistory.push({
                        action: 'cancel',
                        timestamp: Date.now(),
                        success: true
                    });
                    
                    // 显示成功通知
                    this.showSuccessNotification('信件已取消');
                    
                    // 记录取消事件
                    this.recordCancelAction(true);
                    
                    // 关闭对话框
                    this.props.close();
                    
                } catch (error) {
                    console.error('取消信件失败:', error);
                    
                    // 记录失败
                    this.enhancedState.actionHistory.push({
                        action: 'cancel',
                        timestamp: Date.now(),
                        success: false,
                        error: error.message
                    });
                    
                    // 显示错误通知
                    this.showErrorNotification('取消信件失败: ' + error.message);
                    
                    // 记录取消事件
                    this.recordCancelAction(false, error.message);
                    
                } finally {
                    this.enhancedState.isProcessing = false;
                }
            },
            
            // 联系支持
            async onClickContactSupport() {
                if (!this.enhancedConfig.enableSupport) {
                    this.showErrorNotification('支持功能已禁用');
                    return;
                }
                
                try {
                    this.enhancedState.isProcessing = true;
                    
                    // 创建支持工单
                    const ticketData = {
                        subject: `蜗牛邮件错误 - ${this.props.failureType}`,
                        description: this.generateSupportDescription(),
                        messageId: this.props.messageId,
                        failureType: this.props.failureType,
                        errorDetails: this.enhancedState.errorDetails,
                        userInfo: this.getUserInfo()
                    };
                    
                    const ticket = await this.supportService.createTicket(ticketData);
                    
                    if (ticket.success) {
                        this.enhancedState.supportTicketId = ticket.ticketId;
                        this.showSuccessNotification(`支持工单已创建: ${ticket.ticketId}`);
                    } else {
                        this.showErrorNotification('创建支持工单失败');
                    }
                    
                } catch (error) {
                    console.error('联系支持失败:', error);
                    this.showErrorNotification('联系支持失败');
                } finally {
                    this.enhancedState.isProcessing = false;
                }
            },
            
            // 自动重试
            async autoRetry() {
                if (!this.enhancedConfig.enableAutoRetry || !this.canRetry()) {
                    return;
                }
                
                // 延迟重试
                setTimeout(async () => {
                    try {
                        await this.onClickResendLetter();
                    } catch (error) {
                        console.error('自动重试失败:', error);
                    }
                }, this.enhancedConfig.retryDelay);
            },
            
            // 检查是否可以重试
            canRetry() {
                return this.enhancedState.retryCount < this.enhancedConfig.maxRetryAttempts;
            },
            
            // 确认取消操作
            async confirmCancelAction() {
                return new Promise((resolve) => {
                    this.env.services.dialog.add(ConfirmDialog, {
                        title: _t("确认取消"),
                        body: _t("确定要取消这封信件吗？此操作无法撤销。"),
                        confirm: () => resolve(true),
                        cancel: () => resolve(false)
                    });
                });
            },
            
            // 生成支持描述
            generateSupportDescription() {
                const details = this.enhancedState.errorDetails;
                let description = `用户遇到蜗牛邮件错误:\n\n`;
                description += `错误类型: ${this.props.failureType}\n`;
                description += `消息ID: ${this.props.messageId}\n`;
                
                if (details) {
                    description += `错误详情: ${JSON.stringify(details, null, 2)}\n`;
                }
                
                description += `操作历史: ${JSON.stringify(this.enhancedState.actionHistory, null, 2)}\n`;
                
                return description;
            },
            
            // 获取用户信息
            getUserInfo() {
                return {
                    userId: this.user.userId,
                    userName: this.user.name,
                    userEmail: this.user.email,
                    userGroups: this.user.groups,
                    timestamp: Date.now()
                };
            },
            
            // 显示成功通知
            showSuccessNotification(message) {
                if (this.enhancedConfig.enableNotifications) {
                    this.notification.add(message, { type: 'success' });
                }
            },
            
            // 显示错误通知
            showErrorNotification(message) {
                if (this.enhancedConfig.enableNotifications) {
                    this.notification.add(message, { type: 'error' });
                }
            },
            
            // 记录错误显示事件
            recordErrorDisplay() {
                try {
                    this.orm.silent.call(
                        'snailmail.analytics',
                        'record_error_display',
                        [{
                            failure_type: this.props.failureType,
                            message_id: this.props.messageId,
                            display_timestamp: Date.now(),
                            user_id: this.user.userId
                        }]
                    );
                } catch (error) {
                    console.warn('记录错误显示事件失败:', error);
                }
            },
            
            // 记录信用URL访问
            recordCreditUrlAccess(type) {
                try {
                    this.orm.silent.call(
                        'snailmail.analytics',
                        'record_credit_url_access',
                        [{
                            url_type: type,
                            failure_type: this.props.failureType,
                            message_id: this.props.messageId,
                            access_timestamp: Date.now(),
                            user_id: this.user.userId
                        }]
                    );
                } catch (error) {
                    console.warn('记录信用URL访问失败:', error);
                }
            },
            
            // 记录重发事件
            recordResendAction(success, errorMessage = null) {
                try {
                    this.orm.silent.call(
                        'snailmail.analytics',
                        'record_resend_action',
                        [{
                            message_id: this.props.messageId,
                            success: success,
                            error_message: errorMessage,
                            retry_count: this.enhancedState.retryCount,
                            action_timestamp: Date.now(),
                            user_id: this.user.userId
                        }]
                    );
                } catch (error) {
                    console.warn('记录重发事件失败:', error);
                }
            },
            
            // 记录取消事件
            recordCancelAction(success, errorMessage = null) {
                try {
                    this.orm.silent.call(
                        'snailmail.analytics',
                        'record_cancel_action',
                        [{
                            message_id: this.props.messageId,
                            success: success,
                            error_message: errorMessage,
                            action_timestamp: Date.now(),
                            user_id: this.user.userId
                        }]
                    );
                } catch (error) {
                    console.warn('记录取消事件失败:', error);
                }
            },
            
            // 加载用户偏好
            async loadUserPreferences() {
                try {
                    const preferences = await this.orm.call(
                        'res.users',
                        'get_snailmail_preferences',
                        [this.user.userId]
                    );
                    
                    // 应用用户偏好
                    if (preferences.enableAutoRetry !== undefined) {
                        this.enhancedConfig.enableAutoRetry = preferences.enableAutoRetry;
                    }
                    
                    if (preferences.maxRetryAttempts !== undefined) {
                        this.enhancedConfig.maxRetryAttempts = preferences.maxRetryAttempts;
                    }
                    
                } catch (error) {
                    console.warn('加载用户偏好失败:', error);
                }
            }
        };
        
        // 蜗牛邮件错误分析器
        class SnailmailErrorAnalyzer {
            async analyze(errorData) {
                // 分析错误详情
                return {
                    severity: this.getSeverity(errorData.failureType),
                    category: this.getCategory(errorData.failureType),
                    suggestions: this.getSuggestions(errorData.failureType),
                    relatedErrors: await this.getRelatedErrors(errorData.messageId)
                };
            }
            
            getSeverity(failureType) {
                const severityMap = {
                    'sn_credit': 'high',
                    'sn_trial': 'medium',
                    'sn_price': 'medium',
                    'sn_error': 'high',
                    'sn_fields': 'medium',
                    'sn_format': 'low'
                };
                
                return severityMap[failureType] || 'medium';
            }
            
            getCategory(failureType) {
                const categoryMap = {
                    'sn_credit': 'billing',
                    'sn_trial': 'billing',
                    'sn_price': 'billing',
                    'sn_error': 'technical',
                    'sn_fields': 'data',
                    'sn_format': 'data'
                };
                
                return categoryMap[failureType] || 'general';
            }
            
            getSuggestions(failureType) {
                const suggestionMap = {
                    'sn_credit': ['购买更多信用', '检查账户余额'],
                    'sn_trial': ['升级到正式版', '联系销售团队'],
                    'sn_price': ['检查定价信息', '联系客服'],
                    'sn_error': ['重试操作', '联系技术支持'],
                    'sn_fields': ['补充缺失字段', '检查数据完整性'],
                    'sn_format': ['转换文档格式', '检查文档内容']
                };
                
                return suggestionMap[failureType] || ['联系支持'];
            }
            
            async getRelatedErrors(messageId) {
                // 获取相关错误
                return [];
            }
        }
        
        // 蜗牛邮件支持服务
        class SnailmailSupportService {
            async createTicket(ticketData) {
                // 创建支持工单
                return {
                    success: true,
                    ticketId: 'SNAIL-' + Date.now()
                };
            }
        }
        
        // 导出增强的组件
        __exports.EnhancedSnailmailError = EnhancedSnailmailError;
    }
};

// 应用蜗牛邮件错误组件增强
SnailmailErrorEnhancer.enhanceSnailmailError();
```

## 技术特点

### 1. 组件设计
- 基于OWL框架的现代化组件
- 清晰的属性定义和模板绑定
- 简洁的生命周期管理

### 2. 服务集成
- 深度集成ORM服务
- 异步操作的优雅处理
- 完善的错误处理机制

### 3. 操作支持
- 完整的信件重发功能
- 安全的信件取消操作
- 便捷的信用购买链接

### 4. 用户体验
- 友好的错误对话框界面
- 清晰的操作反馈
- 流畅的用户交互

## 设计模式

### 1. 组件模式 (Component Pattern)
- 封装的UI组件
- 可重用的界面元素

### 2. 服务定位器模式 (Service Locator Pattern)
- 通过钩子获取服务
- 解耦组件和服务实现

### 3. 异步模式 (Async Pattern)
- 异步的服务调用
- 非阻塞的用户界面

## 注意事项

1. **错误处理**: 确保所有异步操作的错误处理
2. **用户反馈**: 提供清晰的操作结果反馈
3. **权限控制**: 确保用户有执行操作的权限
4. **状态管理**: 正确管理组件的加载和处理状态

## 扩展建议

1. **错误分析**: 提供更详细的错误分析和诊断
2. **批量操作**: 支持批量处理多个错误
3. **自动重试**: 实现智能的自动重试机制
4. **支持集成**: 集成客户支持和帮助系统
5. **用户指导**: 提供更详细的用户操作指导

该蜗牛邮件错误组件为蜗牛邮件系统提供了重要的错误处理用户界面，确保用户能够方便地处理各种蜗牛邮件发送错误。

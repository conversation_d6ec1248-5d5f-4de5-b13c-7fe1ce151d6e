# SMS Message Patch - SMS消息补丁

## 概述

`message_patch.js` 是 Odoo SMS 模块的消息补丁，专门用于扩展邮件模块的消息模型以支持SMS消息的失败处理。该补丁基于Odoo的补丁机制，为SMS类型的消息提供了专门的失败点击处理，确保SMS消息失败时能够触发正确的重发操作。

## 文件信息
- **路径**: `/sms/static/src/thread/message_patch.js`
- **行数**: 25
- **模块**: `@sms/thread/message_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/message'     // 邮件消息模型
'@web/core/utils/patch'         // 补丁工具
```

## 核心功能

### 1. 消息模型补丁

```javascript
patch(Message.prototype, {
    onClickFailure() {
        if (this.message.message_type === "sms") {
            this.env.services.action.doAction("sms.sms_resend_action", {
                additionalContext: {
                    default_mail_message_id: this.message.id,
                },
            });
        } else {
            super.onClickFailure(...arguments);
        }
    },
});
```

**补丁功能**:
- **类型检测**: 检测消息类型是否为SMS
- **SMS处理**: SMS消息失败时执行重发操作
- **邮件处理**: 邮件消息失败使用原有处理逻辑
- **上下文传递**: 传递消息ID到重发操作
- **操作执行**: 执行SMS重发操作

### 2. 失败点击处理

```javascript
onClickFailure() {
    if (this.message.message_type === "sms") {
        // SMS失败处理逻辑
    } else {
        super.onClickFailure(...arguments);
    }
}
```

**点击处理功能**:
- **条件分支**: 根据消息类型选择处理方式
- **SMS特化**: SMS消息使用专门的失败处理
- **继承调用**: 非SMS消息调用父类方法
- **参数传递**: 正确传递方法参数

### 3. SMS重发操作

```javascript
this.env.services.action.doAction("sms.sms_resend_action", {
    additionalContext: {
        default_mail_message_id: this.message.id,
    },
});
```

**重发操作功能**:
- **操作标识**: 使用SMS重发操作标识
- **上下文设置**: 设置消息ID到上下文
- **服务调用**: 通过操作服务执行重发
- **参数传递**: 传递必要的消息信息

## 使用场景

### 1. SMS消息补丁增强

```javascript
// SMS消息补丁增强功能
const SMSMessagePatchEnhancer = {
    enhanceSMSMessagePatch: () => {
        // 增强的SMS消息补丁
        const enhancedMessagePatch = {
            // 增强的失败点击处理
            onClickFailure() {
                if (this.message.message_type === "sms") {
                    // 记录失败点击事件
                    this.recordFailureClick();
                    
                    // 检查重发权限
                    if (!this.canResendSMS()) {
                        this.showPermissionError();
                        return;
                    }
                    
                    // 检查重发限制
                    if (!this.checkResendLimits()) {
                        this.showResendLimitError();
                        return;
                    }
                    
                    // 显示重发确认对话框
                    this.showResendConfirmation();
                } else {
                    super.onClickFailure(...arguments);
                }
            },
            
            // 显示重发确认对话框
            showResendConfirmation() {
                const dialogService = this.env.services.dialog;
                
                dialogService.add(ResendSMSDialog, {
                    message: this.message,
                    onConfirm: () => this.executeSMSResend(),
                    onCancel: () => this.cancelSMSResend(),
                    resendOptions: this.getResendOptions()
                });
            },
            
            // 执行SMS重发
            async executeSMSResend() {
                try {
                    // 显示加载状态
                    this.showResendLoading(true);
                    
                    // 获取重发选项
                    const options = this.getResendOptions();
                    
                    // 执行重发操作
                    const result = await this.env.services.action.doAction("sms.sms_resend_action", {
                        additionalContext: {
                            default_mail_message_id: this.message.id,
                            resend_options: options,
                            resend_timestamp: Date.now()
                        },
                    });
                    
                    if (result && result.success) {
                        this.showResendSuccess();
                        this.updateMessageStatus('resent');
                    } else {
                        this.showResendError(result?.error || 'Unknown error');
                    }
                    
                } catch (error) {
                    console.error('SMS重发失败:', error);
                    this.showResendError(error.message);
                } finally {
                    this.showResendLoading(false);
                }
            },
            
            // 取消SMS重发
            cancelSMSResend() {
                // 记录取消事件
                this.recordResendCancel();
            },
            
            // 获取重发选项
            getResendOptions() {
                return {
                    // 重发模式
                    mode: 'immediate', // 'immediate', 'scheduled', 'retry_later'
                    
                    // 重发设置
                    retry_count: this.getRetryCount(),
                    retry_interval: 300, // 5分钟
                    max_retries: 3,
                    
                    // 内容选项
                    use_original_content: true,
                    allow_content_edit: false,
                    
                    // 收件人选项
                    verify_recipient: true,
                    update_recipient: false,
                    
                    // 通知选项
                    notify_on_success: true,
                    notify_on_failure: true,
                    
                    // 分析选项
                    track_resend: true,
                    record_analytics: true
                };
            },
            
            // 检查重发权限
            canResendSMS() {
                const user = this.env.services.user;
                
                // 检查基本SMS权限
                if (!user.hasGroup('sms.group_sms_user')) {
                    return false;
                }
                
                // 检查重发权限
                if (!user.hasGroup('sms.group_sms_resend')) {
                    return false;
                }
                
                // 检查消息所有权
                if (this.message.author_id && 
                    this.message.author_id[0] !== user.userId &&
                    !user.hasGroup('sms.group_sms_manager')) {
                    return false;
                }
                
                return true;
            },
            
            // 检查重发限制
            checkResendLimits() {
                const message = this.message;
                
                // 检查重发次数限制
                const maxResends = 3;
                if ((message.sms_resend_count || 0) >= maxResends) {
                    return false;
                }
                
                // 检查时间限制（24小时内）
                const resendWindow = 24 * 60 * 60 * 1000; // 24小时
                const messageTime = new Date(message.create_date).getTime();
                const now = Date.now();
                
                if (now - messageTime > resendWindow) {
                    return false;
                }
                
                // 检查频率限制（1小时内最多重发1次）
                const lastResendTime = message.sms_last_resend_time;
                if (lastResendTime) {
                    const timeSinceLastResend = now - new Date(lastResendTime).getTime();
                    const minInterval = 60 * 60 * 1000; // 1小时
                    
                    if (timeSinceLastResend < minInterval) {
                        return false;
                    }
                }
                
                return true;
            },
            
            // 获取重试次数
            getRetryCount() {
                return (this.message.sms_resend_count || 0) + 1;
            },
            
            // 显示权限错误
            showPermissionError() {
                this.env.services.notification.add(
                    _t("You don't have permission to resend SMS messages"),
                    { type: 'error' }
                );
            },
            
            // 显示重发限制错误
            showResendLimitError() {
                const message = this.message;
                const resendCount = message.sms_resend_count || 0;
                const maxResends = 3;
                
                if (resendCount >= maxResends) {
                    this.env.services.notification.add(
                        _t("Maximum resend attempts (%s) reached for this SMS", maxResends),
                        { type: 'warning' }
                    );
                } else {
                    this.env.services.notification.add(
                        _t("SMS resend is temporarily restricted. Please try again later."),
                        { type: 'warning' }
                    );
                }
            },
            
            // 显示重发成功
            showResendSuccess() {
                this.env.services.notification.add(
                    _t("SMS has been queued for resending"),
                    { type: 'success' }
                );
            },
            
            // 显示重发错误
            showResendError(error) {
                this.env.services.notification.add(
                    _t("Failed to resend SMS: %s", error),
                    { type: 'error' }
                );
            },
            
            // 显示重发加载状态
            showResendLoading(loading) {
                // 更新UI显示加载状态
                const failureElement = this.el?.querySelector('.o_message_failure');
                if (failureElement) {
                    if (loading) {
                        failureElement.classList.add('o_loading');
                        failureElement.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
                    } else {
                        failureElement.classList.remove('o_loading');
                        failureElement.innerHTML = '<i class="fa fa-exclamation-triangle"></i>';
                    }
                }
            },
            
            // 更新消息状态
            updateMessageStatus(status) {
                // 更新消息的重发状态
                this.message.sms_resend_status = status;
                this.message.sms_last_resend_time = new Date().toISOString();
                this.message.sms_resend_count = (this.message.sms_resend_count || 0) + 1;
                
                // 触发UI更新
                this.render();
            },
            
            // 记录失败点击事件
            recordFailureClick() {
                try {
                    this.env.services.orm.silent.call(
                        'sms.analytics',
                        'record_failure_click',
                        [{
                            message_id: this.message.id,
                            message_type: this.message.message_type,
                            failure_reason: this.message.sms_failure_reason,
                            click_timestamp: Date.now(),
                            user_id: this.env.services.user.userId
                        }]
                    );
                } catch (error) {
                    console.warn('记录失败点击事件失败:', error);
                }
            },
            
            // 记录重发取消事件
            recordResendCancel() {
                try {
                    this.env.services.orm.silent.call(
                        'sms.analytics',
                        'record_resend_cancel',
                        [{
                            message_id: this.message.id,
                            cancel_timestamp: Date.now(),
                            user_id: this.env.services.user.userId
                        }]
                    );
                } catch (error) {
                    console.warn('记录重发取消事件失败:', error);
                }
            },
            
            // 获取SMS失败详情
            getSMSFailureDetails() {
                const message = this.message;
                return {
                    failureReason: message.sms_failure_reason,
                    errorCode: message.sms_error_code,
                    errorMessage: message.sms_error_message,
                    failureTime: message.sms_failure_time,
                    provider: message.sms_provider,
                    recipient: message.sms_number,
                    resendCount: message.sms_resend_count || 0,
                    lastResendTime: message.sms_last_resend_time
                };
            },
            
            // 检查是否可以显示重发按钮
            get canShowResendButton() {
                if (this.message.message_type !== "sms") {
                    return false;
                }
                
                // 检查消息状态
                if (!this.message.sms_failure_reason) {
                    return false;
                }
                
                // 检查权限
                if (!this.canResendSMS()) {
                    return false;
                }
                
                // 检查限制
                if (!this.checkResendLimits()) {
                    return false;
                }
                
                return true;
            },
            
            // 获取重发按钮文本
            get resendButtonText() {
                const retryCount = this.getRetryCount();
                if (retryCount === 1) {
                    return _t("Resend SMS");
                } else {
                    return _t("Resend SMS (Attempt %s)", retryCount);
                }
            }
        };
        
        // 重发SMS对话框组件
        class ResendSMSDialog extends Component {
            static template = "sms.ResendSMSDialog";
            static props = {
                message: Object,
                onConfirm: Function,
                onCancel: Function,
                resendOptions: Object,
                close: Function
            };
            
            setup() {
                this.state = useState({
                    useOriginalContent: true,
                    verifyRecipient: true,
                    notifyOnSuccess: true,
                    resendMode: 'immediate'
                });
            }
            
            onConfirm() {
                this.props.onConfirm();
                this.props.close();
            }
            
            onCancel() {
                this.props.onCancel();
                this.props.close();
            }
        }
        
        // 应用增强的补丁
        patch(Message.prototype, enhancedMessagePatch);
        
        // 导出增强的补丁和组件
        __exports.enhancedMessagePatch = enhancedMessagePatch;
        __exports.ResendSMSDialog = ResendSMSDialog;
    }
};

// 应用SMS消息补丁增强
SMSMessagePatchEnhancer.enhanceSMSMessagePatch();
```

## 技术特点

### 1. 补丁机制
- 使用Odoo标准的补丁工具
- 非侵入式的功能扩展
- 保持与原有功能的兼容性

### 2. 类型识别
- 精确的SMS消息类型检测
- 条件性的处理逻辑
- 统一的方法接口

### 3. 操作集成
- 深度集成操作服务
- 完整的上下文传递
- 标准的操作执行流程

### 4. 用户交互
- 直观的失败点击处理
- 清晰的操作反馈
- 流畅的用户体验

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 通过补丁为现有消息添加新功能
- 保持原有接口不变

### 2. 策略模式 (Strategy Pattern)
- 根据消息类型选择不同的处理策略
- 灵活的失败处理

### 3. 命令模式 (Command Pattern)
- 封装重发操作为命令
- 统一的操作执行接口

## 注意事项

1. **补丁顺序**: 确保补丁在邮件模块加载后应用
2. **类型检查**: 准确识别SMS消息类型
3. **权限控制**: 确保用户有重发SMS的权限
4. **操作反馈**: 提供清晰的操作结果反馈

## 扩展建议

1. **重发选项**: 提供更多的重发配置选项
2. **批量重发**: 支持批量重发失败的SMS
3. **重发历史**: 记录和显示SMS重发历史
4. **智能重试**: 实现智能的重试机制
5. **失败分析**: 提供失败原因的详细分析

该SMS消息补丁为SMS系统提供了重要的失败处理和重发功能，确保SMS消息失败时能够方便地进行重发操作。

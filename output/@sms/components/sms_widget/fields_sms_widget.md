# SMS Widget - SMS文本组件

## 概述

`fields_sms_widget.js` 是 Odoo SMS 模块的SMS文本组件，专门用于SMS消息的编辑和显示。该组件基于邮件模块的表情文本字段，集成了SMS字符计数、编码检测、SMS数量计算等核心功能，为SMS系统提供了完整的消息编辑支持，是SMS消息编辑界面的重要组件。

## 文件信息
- **路径**: `/sms/static/src/components/sms_widget/fields_sms_widget.js`
- **行数**: 119
- **模块**: `@sms/components/sms_widget/fields_sms_widget`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'                                    // 国际化翻译
'@mail/views/web/fields/emojis_text_field/emojis_text_field'    // 表情文本字段
'@web/core/utils/hooks'                                         // 钩子工具
'@web/core/registry'                                            // 注册表
```

## 核心功能

### 1. SmsWidget 组件

```javascript
const SmsWidget = class SmsWidget extends EmojisTextField {
    static template = "sms.SmsWidget";
}
```

**组件特性**:
- **继承扩展**: 继承表情文本字段的所有功能
- **SMS特化**: 专门针对SMS消息的特殊需求
- **模板绑定**: 使用专门的SMS组件模板
- **功能增强**: 在表情支持基础上增加SMS特有功能

### 2. 组件初始化

```javascript
setup() {
    super.setup();
    this._emojiAdded = () => this.props.record.update({ [this.props.name]: this.targetEditElement.el.value });
    this.notification = useService('notification');
}
```

**初始化功能**:
- **父类初始化**: 调用父类的初始化方法
- **表情回调**: 设置表情添加后的回调函数
- **通知服务**: 集成通知服务用于用户提示
- **记录更新**: 自动更新记录数据

### 3. 编码检测

```javascript
get encoding() {
    return this._extractEncoding(this.props.record.data[this.props.name] || '');
}

_extractEncoding(content) {
    if (String(content).match(RegExp("^[@£$¥èéùìòÇ\\nØø\\rÅåΔ_ΦΓΛΩΠΨΣΘΞÆæßÉ !\\\"#¤%&'()*+,-./0123456789:;<=>?¡ABCDEFGHIJKLMNOPQRSTUVWXYZÄÖÑÜ§¿abcdefghijklmnopqrstuvwxyzäöñüà]*$"))) {
        return 'GSM7';
    }
    return 'UNICODE';
}
```

**编码检测功能**:
- **动态检测**: 根据内容动态检测编码类型
- **GSM7支持**: 检测标准GSM 7位编码字符
- **Unicode支持**: 自动识别需要Unicode编码的字符
- **正则匹配**: 使用正则表达式精确匹配GSM7字符集

### 4. 字符计数

```javascript
get nbrChar() {
    const content = this.props.record.data[this.props.name] || '';
    return content.length + (content.match(/\n/g) || []).length;
}
```

**字符计数功能**:
- **准确计数**: 计算实际的字符数量
- **换行处理**: 正确处理换行符的字符计数
- **空内容**: 安全处理空内容的情况
- **实时更新**: 内容变化时实时更新字符数

### 5. SMS数量计算

```javascript
get nbrSMS() {
    return this._countSMS(this.nbrChar, this.encoding);
}

_countSMS(nbrChar, encoding) {
    if (nbrChar === 0) {
        return 0;
    }
    if (encoding === 'UNICODE') {
        if (nbrChar <= 70) {
            return 1;
        }
        return Math.ceil(nbrChar / 67);
    }
    if (nbrChar <= 160) {
        return 1;
    }
    return Math.ceil(nbrChar / 153);
}
```

**SMS计数功能**:
- **编码感知**: 根据编码类型计算SMS数量
- **Unicode规则**: Unicode编码70字符为1条SMS，之后每67字符1条
- **GSM7规则**: GSM7编码160字符为1条SMS，之后每153字符1条
- **精确计算**: 使用向上取整确保准确的SMS数量

### 6. 事件处理

```javascript
async onBlur() {
    await super.onBlur();
    var content = this.props.record.data[this.props.name] || '';
    if( !content.trim().length && content.length > 0) {
        this.notification.add(
            _t("Your SMS Text Message must include at least one non-whitespace character"),
            { type: 'danger' },
        )
        await this.props.record.update({ [this.props.name]: content.trim() });
    }
}

async onInput(ev) {
    super.onInput(...arguments);
    await this.props.record.update({ [this.props.name]: this.targetEditElement.el.value });
}
```

**事件处理功能**:
- **失焦验证**: 在失去焦点时验证内容有效性
- **空白检查**: 检查并处理只包含空白字符的内容
- **用户提示**: 提供友好的错误提示信息
- **实时更新**: 输入时实时更新记录数据
- **内容清理**: 自动清理无效的空白内容

### 7. 字段注册

```javascript
const smsWidget = {
    ...emojisTextField,
    component: SmsWidget,
    additionalClasses: [
        ...(emojisTextField.additionalClasses || []),
        "o_field_text",
        "o_field_text_emojis",
    ],
};

registry.category("fields").add("sms_widget", smsWidget);
```

**字段注册功能**:
- **继承配置**: 继承表情文本字段的所有配置
- **组件替换**: 使用SMS组件替换默认组件
- **样式扩展**: 添加SMS特有的CSS类
- **注册表添加**: 将字段添加到字段注册表

## 使用场景

### 1. SMS组件增强

```javascript
// SMS组件增强功能
const SMSWidgetEnhancer = {
    enhanceSMSWidget: () => {
        // 增强的SMS组件
        class EnhancedSmsWidget extends SmsWidget {
            static props = {
                ...SmsWidget.props,
                enableTemplates: { type: Boolean, optional: true },
                enablePreview: { type: Boolean, optional: true },
                enableAutoSave: { type: Boolean, optional: true },
                enableCharacterLimit: { type: Boolean, optional: true },
                maxCharacters: { type: Number, optional: true },
                enableSpellCheck: { type: Boolean, optional: true },
                enableWordCount: { type: Boolean, optional: true },
                enableReadingTime: { type: Boolean, optional: true },
                customValidators: { type: Array, optional: true }
            };
            
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    enableTemplates: this.props.enableTemplates || true,
                    enablePreview: this.props.enablePreview || true,
                    enableAutoSave: this.props.enableAutoSave || true,
                    enableCharacterLimit: this.props.enableCharacterLimit || false,
                    maxCharacters: this.props.maxCharacters || 1000,
                    enableSpellCheck: this.props.enableSpellCheck || true,
                    enableWordCount: this.props.enableWordCount || true,
                    enableReadingTime: this.props.enableReadingTime || false,
                    enableAnalytics: true,
                    enableHistory: true,
                    autoSaveInterval: 5000, // 5秒
                    enableShortcuts: true,
                    enableFormatting: false
                };
                
                // 增强的状态
                this.enhancedState = useState({
                    templates: [],
                    selectedTemplate: null,
                    previewMode: false,
                    wordCount: 0,
                    readingTime: 0,
                    spellCheckResults: [],
                    validationResults: [],
                    history: [],
                    isAutoSaving: false,
                    lastSaved: null
                });
                
                // 模板管理器
                this.templateManager = new SMSTemplateManager();
                
                // 预览管理器
                this.previewManager = new SMSPreviewManager();
                
                // 拼写检查器
                this.spellChecker = new SpellChecker();
                
                // 验证器
                this.validator = new SMSContentValidator();
                
                // 历史管理器
                this.historyManager = new ContentHistoryManager();
                
                // 分析器
                this.analytics = new SMSContentAnalytics();
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 加载模板
                if (this.enhancedConfig.enableTemplates) {
                    this.loadTemplates();
                }
                
                // 设置自动保存
                if (this.enhancedConfig.enableAutoSave) {
                    this.setupAutoSave();
                }
                
                // 设置拼写检查
                if (this.enhancedConfig.enableSpellCheck) {
                    this.setupSpellCheck();
                }
                
                // 加载历史
                if (this.enhancedConfig.enableHistory) {
                    this.loadHistory();
                }
                
                // 设置快捷键
                if (this.enhancedConfig.enableShortcuts) {
                    this.setupShortcuts();
                }
            }
            
            // 增强的编码检测
            get encoding() {
                const content = this.props.record.data[this.props.name] || '';
                const encoding = this._extractEncoding(content);
                
                // 记录编码使用情况
                if (this.enhancedConfig.enableAnalytics) {
                    this.analytics.recordEncodingUsage(encoding);
                }
                
                return encoding;
            }
            
            // 增强的字符计数
            get nbrChar() {
                const content = this.props.record.data[this.props.name] || '';
                const charCount = content.length + (content.match(/\n/g) || []).length;
                
                // 更新词数统计
                if (this.enhancedConfig.enableWordCount) {
                    this.updateWordCount(content);
                }
                
                // 更新阅读时间
                if (this.enhancedConfig.enableReadingTime) {
                    this.updateReadingTime(content);
                }
                
                return charCount;
            }
            
            // 增强的SMS计数
            get nbrSMS() {
                const smsCount = this._countSMS(this.nbrChar, this.encoding);
                
                // 检查字符限制
                if (this.enhancedConfig.enableCharacterLimit) {
                    this.checkCharacterLimit();
                }
                
                return smsCount;
            }
            
            // 获取成本估算
            get estimatedCost() {
                // 根据SMS数量估算成本
                const costPerSMS = 0.05; // 示例价格
                return (this.nbrSMS * costPerSMS).toFixed(2);
            }
            
            // 获取投递时间估算
            get estimatedDeliveryTime() {
                // 根据SMS数量估算投递时间
                const baseTime = 5; // 基础5秒
                const additionalTime = Math.max(0, this.nbrSMS - 1) * 2; // 每条额外SMS增加2秒
                return baseTime + additionalTime;
            }
            
            // 更新词数统计
            updateWordCount(content) {
                const words = content.trim().split(/\s+/).filter(word => word.length > 0);
                this.enhancedState.wordCount = words.length;
            }
            
            // 更新阅读时间
            updateReadingTime(content) {
                const wordsPerMinute = 200; // 平均阅读速度
                const words = content.trim().split(/\s+/).filter(word => word.length > 0);
                this.enhancedState.readingTime = Math.ceil(words.length / wordsPerMinute);
            }
            
            // 检查字符限制
            checkCharacterLimit() {
                if (this.nbrChar > this.enhancedConfig.maxCharacters) {
                    this.notification.add(
                        _t("Content exceeds maximum character limit (%s)", this.enhancedConfig.maxCharacters),
                        { type: 'warning' }
                    );
                }
            }
            
            // 加载模板
            async loadTemplates() {
                try {
                    const templates = await this.templateManager.getTemplates();
                    this.enhancedState.templates = templates;
                } catch (error) {
                    console.error('加载模板失败:', error);
                }
            }
            
            // 应用模板
            async applyTemplate(template) {
                try {
                    const content = await this.templateManager.renderTemplate(template, this.props.record);
                    await this.props.record.update({ [this.props.name]: content });
                    this.enhancedState.selectedTemplate = template;
                    
                    this.notification.add(_t("Template applied"), { type: 'success' });
                } catch (error) {
                    console.error('应用模板失败:', error);
                    this.notification.add(_t("Failed to apply template"), { type: 'error' });
                }
            }
            
            // 切换预览模式
            togglePreview() {
                this.enhancedState.previewMode = !this.enhancedState.previewMode;
                
                if (this.enhancedState.previewMode) {
                    this.generatePreview();
                }
            }
            
            // 生成预览
            async generatePreview() {
                try {
                    const content = this.props.record.data[this.props.name] || '';
                    const preview = await this.previewManager.generatePreview(content, {
                        encoding: this.encoding,
                        smsCount: this.nbrSMS,
                        charCount: this.nbrChar
                    });
                    
                    this.enhancedState.preview = preview;
                } catch (error) {
                    console.error('生成预览失败:', error);
                }
            }
            
            // 设置自动保存
            setupAutoSave() {
                this.autoSaveTimer = setInterval(() => {
                    this.autoSave();
                }, this.enhancedConfig.autoSaveInterval);
            }
            
            // 自动保存
            async autoSave() {
                if (this.enhancedState.isAutoSaving) return;
                
                try {
                    this.enhancedState.isAutoSaving = true;
                    await this.props.record.save();
                    this.enhancedState.lastSaved = Date.now();
                } catch (error) {
                    console.error('自动保存失败:', error);
                } finally {
                    this.enhancedState.isAutoSaving = false;
                }
            }
            
            // 设置拼写检查
            setupSpellCheck() {
                this.spellCheckTimer = null;
            }
            
            // 执行拼写检查
            async performSpellCheck(content) {
                try {
                    const results = await this.spellChecker.check(content);
                    this.enhancedState.spellCheckResults = results;
                } catch (error) {
                    console.error('拼写检查失败:', error);
                }
            }
            
            // 设置快捷键
            setupShortcuts() {
                // Ctrl+S: 保存
                // Ctrl+P: 预览
                // Ctrl+T: 模板
                // 等等...
            }
            
            // 增强的失焦处理
            async onBlur() {
                await super.onBlur();
                
                // 执行自定义验证
                if (this.props.customValidators) {
                    await this.runCustomValidators();
                }
                
                // 执行拼写检查
                if (this.enhancedConfig.enableSpellCheck) {
                    const content = this.props.record.data[this.props.name] || '';
                    await this.performSpellCheck(content);
                }
                
                // 保存到历史
                if (this.enhancedConfig.enableHistory) {
                    this.saveToHistory();
                }
            }
            
            // 增强的输入处理
            async onInput(ev) {
                await super.onInput(ev);
                
                // 延迟拼写检查
                if (this.enhancedConfig.enableSpellCheck) {
                    clearTimeout(this.spellCheckTimer);
                    this.spellCheckTimer = setTimeout(() => {
                        const content = this.props.record.data[this.props.name] || '';
                        this.performSpellCheck(content);
                    }, 1000);
                }
                
                // 记录分析数据
                if (this.enhancedConfig.enableAnalytics) {
                    this.analytics.recordTyping();
                }
            }
            
            // 运行自定义验证器
            async runCustomValidators() {
                const content = this.props.record.data[this.props.name] || '';
                const results = [];
                
                for (const validator of this.props.customValidators || []) {
                    try {
                        const result = await this.validator.validate(content, validator);
                        results.push(result);
                    } catch (error) {
                        console.error('自定义验证失败:', error);
                    }
                }
                
                this.enhancedState.validationResults = results;
                
                // 显示验证错误
                const errors = results.filter(r => !r.isValid);
                if (errors.length > 0) {
                    const errorMessage = errors.map(e => e.message).join('; ');
                    this.notification.add(errorMessage, { type: 'warning' });
                }
            }
            
            // 保存到历史
            saveToHistory() {
                const content = this.props.record.data[this.props.name] || '';
                if (content.trim()) {
                    this.historyManager.add({
                        content: content,
                        timestamp: Date.now(),
                        charCount: this.nbrChar,
                        smsCount: this.nbrSMS,
                        encoding: this.encoding
                    });
                }
            }
            
            // 加载历史
            async loadHistory() {
                try {
                    const history = await this.historyManager.getHistory();
                    this.enhancedState.history = history;
                } catch (error) {
                    console.error('加载历史失败:', error);
                }
            }
            
            // 从历史恢复
            async restoreFromHistory(historyItem) {
                await this.props.record.update({ [this.props.name]: historyItem.content });
                this.notification.add(_t("Content restored from history"), { type: 'info' });
            }
            
            // 清除内容
            async clearContent() {
                await this.props.record.update({ [this.props.name]: '' });
                this.notification.add(_t("Content cleared"), { type: 'info' });
            }
            
            // 获取内容统计
            getContentStats() {
                return {
                    characters: this.nbrChar,
                    words: this.enhancedState.wordCount,
                    sms: this.nbrSMS,
                    encoding: this.encoding,
                    estimatedCost: this.estimatedCost,
                    estimatedDeliveryTime: this.estimatedDeliveryTime,
                    readingTime: this.enhancedState.readingTime
                };
            }
            
            // 组件销毁时清理
            willDestroy() {
                if (this.autoSaveTimer) {
                    clearInterval(this.autoSaveTimer);
                }
                
                if (this.spellCheckTimer) {
                    clearTimeout(this.spellCheckTimer);
                }
                
                super.willDestroy && super.willDestroy();
            }
        }
        
        // SMS模板管理器
        class SMSTemplateManager {
            async getTemplates() {
                // 获取SMS模板
                return [];
            }
            
            async renderTemplate(template, record) {
                // 渲染模板
                return template.content.replace(/\{\{(\w+)\}\}/g, (match, field) => {
                    return record.data[field] || match;
                });
            }
        }
        
        // SMS预览管理器
        class SMSPreviewManager {
            async generatePreview(content, stats) {
                // 生成SMS预览
                return {
                    content: content,
                    stats: stats,
                    segments: this.splitIntoSegments(content, stats.encoding)
                };
            }
            
            splitIntoSegments(content, encoding) {
                // 将内容分割为SMS段
                const maxLength = encoding === 'UNICODE' ? 70 : 160;
                const segments = [];
                
                for (let i = 0; i < content.length; i += maxLength) {
                    segments.push(content.substring(i, i + maxLength));
                }
                
                return segments;
            }
        }
        
        // 拼写检查器
        class SpellChecker {
            async check(content) {
                // 执行拼写检查
                return [];
            }
        }
        
        // SMS内容验证器
        class SMSContentValidator {
            async validate(content, rule) {
                // 执行自定义验证
                return { isValid: true, message: '' };
            }
        }
        
        // 内容历史管理器
        class ContentHistoryManager {
            constructor() {
                this.history = [];
                this.maxItems = 50;
            }
            
            add(item) {
                this.history.unshift(item);
                if (this.history.length > this.maxItems) {
                    this.history = this.history.slice(0, this.maxItems);
                }
                this.save();
            }
            
            async getHistory() {
                return this.history;
            }
            
            save() {
                try {
                    localStorage.setItem('sms_content_history', JSON.stringify(this.history));
                } catch (error) {
                    console.error('保存内容历史失败:', error);
                }
            }
        }
        
        // SMS内容分析器
        class SMSContentAnalytics {
            recordEncodingUsage(encoding) {
                // 记录编码使用情况
                console.log('编码使用:', encoding);
            }
            
            recordTyping() {
                // 记录输入行为
                console.log('用户输入');
            }
        }
        
        // 增强的字段配置
        const enhancedSmsWidget = {
            ...smsWidget,
            component: EnhancedSmsWidget,
            additionalClasses: [
                ...smsWidget.additionalClasses,
                "o_field_sms_enhanced"
            ],
        };
        
        // 注册增强的字段
        registry.category("fields").add("enhanced_sms_widget", enhancedSmsWidget);
        
        // 导出增强的组件
        __exports.EnhancedSmsWidget = EnhancedSmsWidget;
        __exports.enhancedSmsWidget = enhancedSmsWidget;
    }
};

// 应用SMS组件增强
SMSWidgetEnhancer.enhanceSMSWidget();
```

## 技术特点

### 1. 继承设计
- 基于表情文本字段的继承扩展
- 保持原有功能的完整性
- 添加SMS特有的功能

### 2. 实时计算
- 动态的字符和SMS数量计算
- 智能的编码检测
- 实时的用户反馈

### 3. 编码支持
- 完整的GSM7编码支持
- Unicode编码的自动检测
- 准确的字符计数规则

### 4. 用户体验
- 友好的错误提示
- 实时的内容验证
- 自动的内容清理

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- 继承表情文本字段的功能
- 扩展SMS特有的特性

### 2. 计算属性模式 (Computed Property Pattern)
- 动态计算编码、字符数、SMS数量
- 基于内容变化的实时更新

### 3. 验证模式 (Validation Pattern)
- 内容有效性验证
- 用户友好的错误处理

## 注意事项

1. **编码准确性**: 确保GSM7和Unicode编码检测的准确性
2. **计数规则**: 遵循SMS标准的字符计数规则
3. **用户体验**: 提供清晰的字符和SMS数量提示
4. **性能优化**: 避免频繁的计算和更新

## 扩展建议

1. **模板支持**: 添加SMS模板的选择和使用
2. **预览功能**: 提供SMS发送前的预览功能
3. **字符限制**: 支持可配置的字符数量限制
4. **成本估算**: 显示SMS发送的成本估算
5. **历史记录**: 记录和管理SMS内容历史

该SMS文本组件为SMS系统提供了重要的消息编辑功能，是SMS消息创建和编辑的核心组件。

# SMS Phone Field - SMS电话字段组件

## 概述

`phone_field.js` 是 Odoo SMS 模块的电话字段组件，专门用于扩展标准电话字段以支持SMS功能。该组件基于Web框架的电话字段，通过补丁机制集成了SMS按钮组件，为电话字段提供了完整的SMS发送功能，是SMS系统与表单字段集成的重要组件。

## 文件信息
- **路径**: `/sms/static/src/components/phone_field/phone_field.js`
- **行数**: 44
- **模块**: `@sms/components/phone_field/phone_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'                    // 国际化翻译
'@web/core/utils/patch'                         // 补丁工具
'@web/views/fields/phone/phone_field'           // 标准电话字段
'@sms/components/sms_button/sms_button'         // SMS按钮组件
```

## 核心功能

### 1. 电话字段补丁

```javascript
patch(PhoneField, {
    components: {
        ...PhoneField.components,
        SendSMSButton
    },
    defaultProps: {
        ...PhoneField.defaultProps,
        enableButton: true,
    },
    props: {
        ...PhoneField.props,
        enableButton: { type: Boolean, optional: true },
    },
});
```

**补丁功能**:
- **组件扩展**: 在原有组件基础上添加SMS按钮组件
- **属性扩展**: 添加enableButton属性控制SMS按钮显示
- **默认配置**: 默认启用SMS按钮功能
- **类型安全**: 定义enableButton属性的类型和可选性

### 2. 属性提取器

```javascript
const patchDescr = () => ({
    extractProps({ options }) {
        const props = super.extractProps(...arguments);
        props.enableButton = options.enable_sms;
        return props;
    },
    supportedOptions: [{
        label: _t("Enable SMS"),
        name: "enable_sms",
        type: "boolean",
        default: true,
    }],
});
```

**属性提取功能**:
- **选项映射**: 将XML选项映射为组件属性
- **继承处理**: 调用父类的属性提取方法
- **SMS控制**: 通过enable_sms选项控制SMS功能
- **支持选项**: 定义支持的配置选项
- **国际化**: 选项标签支持多语言

### 3. 字段类型补丁

```javascript
patch(phoneField, patchDescr());
patch(formPhoneField, patchDescr());
```

**字段类型补丁功能**:
- **列表字段**: 为列表视图中的电话字段添加SMS功能
- **表单字段**: 为表单视图中的电话字段添加SMS功能
- **统一补丁**: 使用相同的补丁描述符确保一致性
- **全面覆盖**: 覆盖所有电话字段类型

## 使用场景

### 1. SMS电话字段组件增强

```javascript
// SMS电话字段组件增强功能
const SMSPhoneFieldEnhancer = {
    enhanceSMSPhoneField: () => {
        // 增强的SMS电话字段补丁
        const enhancedPhoneFieldPatch = {
            components: {
                ...PhoneField.components,
                SendSMSButton,
                CallButton: CallButton, // 添加呼叫按钮
                WhatsAppButton: WhatsAppButton, // 添加WhatsApp按钮
                ValidationIndicator: ValidationIndicator // 添加验证指示器
            },
            
            defaultProps: {
                ...PhoneField.defaultProps,
                enableButton: true,
                enableCall: true,
                enableWhatsApp: false,
                enableValidation: true,
                showCountryCode: true,
                autoFormat: true,
                enableHistory: true
            },
            
            props: {
                ...PhoneField.props,
                enableButton: { type: Boolean, optional: true },
                enableCall: { type: Boolean, optional: true },
                enableWhatsApp: { type: Boolean, optional: true },
                enableValidation: { type: Boolean, optional: true },
                showCountryCode: { type: Boolean, optional: true },
                autoFormat: { type: Boolean, optional: true },
                enableHistory: { type: Boolean, optional: true },
                customActions: { type: Array, optional: true },
                validationRules: { type: Array, optional: true }
            },
            
            setup() {
                super.setup();
                
                // 增强的状态管理
                this.enhancedState = useState({
                    isValidating: false,
                    validationResult: null,
                    formattedNumber: '',
                    countryCode: '',
                    callHistory: [],
                    smsHistory: [],
                    isFormatting: false
                });
                
                // 电话号码验证器
                this.phoneValidator = new PhoneValidator();
                
                // 号码格式化器
                this.phoneFormatter = new PhoneFormatter();
                
                // 历史记录管理器
                this.historyManager = new PhoneHistoryManager();
                
                // 国家代码检测器
                this.countryDetector = new CountryCodeDetector();
                
                // 初始化增强功能
                this.initializeEnhancements();
            },
            
            // 初始化增强功能
            initializeEnhancements() {
                // 设置号码验证
                if (this.props.enableValidation) {
                    this.setupPhoneValidation();
                }
                
                // 设置自动格式化
                if (this.props.autoFormat) {
                    this.setupAutoFormatting();
                }
                
                // 加载历史记录
                if (this.props.enableHistory) {
                    this.loadPhoneHistory();
                }
                
                // 检测国家代码
                if (this.props.showCountryCode) {
                    this.detectCountryCode();
                }
            },
            
            // 设置电话验证
            setupPhoneValidation() {
                this.phoneValidator.onValidationComplete((result) => {
                    this.enhancedState.validationResult = result;
                    this.updateValidationUI(result);
                });
            },
            
            // 设置自动格式化
            setupAutoFormatting() {
                this.phoneFormatter.onFormatComplete((formatted) => {
                    this.enhancedState.formattedNumber = formatted;
                    this.updateFormattedDisplay(formatted);
                });
            },
            
            // 增强的值变更处理
            onValueChanged(value) {
                // 调用原有处理
                super.onValueChanged(value);
                
                // 执行验证
                if (this.props.enableValidation && value) {
                    this.validatePhoneNumber(value);
                }
                
                // 执行格式化
                if (this.props.autoFormat && value) {
                    this.formatPhoneNumber(value);
                }
                
                // 检测国家代码
                if (this.props.showCountryCode && value) {
                    this.detectCountryFromNumber(value);
                }
                
                // 记录历史
                if (this.props.enableHistory && value) {
                    this.addToHistory('input', value);
                }
            },
            
            // 验证电话号码
            async validatePhoneNumber(phoneNumber) {
                this.enhancedState.isValidating = true;
                
                try {
                    const result = await this.phoneValidator.validate(phoneNumber, {
                        country: this.enhancedState.countryCode,
                        rules: this.props.validationRules || []
                    });
                    
                    this.enhancedState.validationResult = result;
                    
                    if (!result.isValid) {
                        this.showValidationError(result.errors);
                    }
                    
                } catch (error) {
                    console.error('电话号码验证失败:', error);
                } finally {
                    this.enhancedState.isValidating = false;
                }
            },
            
            // 格式化电话号码
            async formatPhoneNumber(phoneNumber) {
                this.enhancedState.isFormatting = true;
                
                try {
                    const formatted = await this.phoneFormatter.format(phoneNumber, {
                        country: this.enhancedState.countryCode,
                        style: 'international' // 'national', 'international', 'e164'
                    });
                    
                    this.enhancedState.formattedNumber = formatted;
                    
                } catch (error) {
                    console.error('电话号码格式化失败:', error);
                } finally {
                    this.enhancedState.isFormatting = false;
                }
            },
            
            // 检测国家代码
            async detectCountryFromNumber(phoneNumber) {
                try {
                    const country = await this.countryDetector.detect(phoneNumber);
                    if (country) {
                        this.enhancedState.countryCode = country.code;
                        this.updateCountryDisplay(country);
                    }
                } catch (error) {
                    console.error('检测国家代码失败:', error);
                }
            },
            
            // 发起呼叫
            async makeCall(phoneNumber) {
                if (!this.props.enableCall) return;
                
                try {
                    // 记录呼叫历史
                    this.addToHistory('call', phoneNumber);
                    
                    // 执行呼叫操作
                    const result = await this.orm.call(
                        'phone.call',
                        'make_call',
                        [phoneNumber],
                        {
                            caller_id: this.env.user.id,
                            context: this.props.record?.context || {}
                        }
                    );
                    
                    if (result.success) {
                        this.showNotification('呼叫已发起', 'success');
                    } else {
                        this.showNotification(result.message || '呼叫失败', 'error');
                    }
                    
                } catch (error) {
                    console.error('发起呼叫失败:', error);
                    this.showNotification('呼叫失败', 'error');
                }
            },
            
            // 发送WhatsApp消息
            async sendWhatsApp(phoneNumber) {
                if (!this.props.enableWhatsApp) return;
                
                try {
                    // 记录WhatsApp历史
                    this.addToHistory('whatsapp', phoneNumber);
                    
                    // 构建WhatsApp URL
                    const whatsappUrl = `https://wa.me/${phoneNumber.replace(/[^\d]/g, '')}`;
                    
                    // 打开WhatsApp
                    window.open(whatsappUrl, '_blank');
                    
                    this.showNotification('WhatsApp已打开', 'info');
                    
                } catch (error) {
                    console.error('打开WhatsApp失败:', error);
                    this.showNotification('打开WhatsApp失败', 'error');
                }
            },
            
            // 执行自定义操作
            async executeCustomAction(action, phoneNumber) {
                try {
                    if (typeof action.handler === 'function') {
                        await action.handler(phoneNumber, this.props.record);
                    } else if (action.url) {
                        const url = action.url.replace('{phone}', encodeURIComponent(phoneNumber));
                        window.open(url, action.target || '_blank');
                    } else if (action.method) {
                        await this.orm.call(
                            action.model || this.props.record.resModel,
                            action.method,
                            [phoneNumber],
                            action.kwargs || {}
                        );
                    }
                    
                    this.showNotification(action.successMessage || '操作完成', 'success');
                    
                } catch (error) {
                    console.error('执行自定义操作失败:', error);
                    this.showNotification(action.errorMessage || '操作失败', 'error');
                }
            },
            
            // 添加到历史记录
            addToHistory(type, phoneNumber) {
                const historyItem = {
                    type: type,
                    phoneNumber: phoneNumber,
                    timestamp: Date.now(),
                    userId: this.env.user.id
                };
                
                if (type === 'call') {
                    this.enhancedState.callHistory.unshift(historyItem);
                } else if (type === 'sms') {
                    this.enhancedState.smsHistory.unshift(historyItem);
                }
                
                this.historyManager.add(historyItem);
            },
            
            // 加载电话历史
            async loadPhoneHistory() {
                try {
                    const history = await this.historyManager.load(this.props.record?.id);
                    this.enhancedState.callHistory = history.filter(item => item.type === 'call');
                    this.enhancedState.smsHistory = history.filter(item => item.type === 'sms');
                } catch (error) {
                    console.error('加载电话历史失败:', error);
                }
            },
            
            // 显示验证错误
            showValidationError(errors) {
                const errorMessage = errors.join('; ');
                this.showNotification(errorMessage, 'error');
            },
            
            // 更新验证UI
            updateValidationUI(result) {
                // 更新验证指示器的显示
                const indicator = this.el.querySelector('.validation-indicator');
                if (indicator) {
                    indicator.className = `validation-indicator ${result.isValid ? 'valid' : 'invalid'}`;
                    indicator.title = result.isValid ? '号码有效' : result.errors.join('; ');
                }
            },
            
            // 更新格式化显示
            updateFormattedDisplay(formatted) {
                // 更新格式化号码的显示
                const display = this.el.querySelector('.formatted-display');
                if (display) {
                    display.textContent = formatted;
                }
            },
            
            // 更新国家显示
            updateCountryDisplay(country) {
                // 更新国家代码和标志的显示
                const countryDisplay = this.el.querySelector('.country-display');
                if (countryDisplay) {
                    countryDisplay.innerHTML = `
                        <img src="/web/static/img/flags/${country.code.toLowerCase()}.png" 
                             alt="${country.name}" title="${country.name}">
                        <span>+${country.dialCode}</span>
                    `;
                }
            },
            
            // 显示通知
            showNotification(message, type) {
                // 这里应该使用实际的通知服务
                console.log(`[${type}] ${message}`);
            }
        };
        
        // 增强的属性提取器
        const enhancedPatchDescr = () => ({
            extractProps({ options }) {
                const props = super.extractProps(...arguments);
                props.enableButton = options.enable_sms !== false;
                props.enableCall = options.enable_call !== false;
                props.enableWhatsApp = options.enable_whatsapp === true;
                props.enableValidation = options.enable_validation !== false;
                props.showCountryCode = options.show_country_code !== false;
                props.autoFormat = options.auto_format !== false;
                props.enableHistory = options.enable_history !== false;
                
                // 解析自定义操作
                if (options.custom_actions) {
                    try {
                        props.customActions = JSON.parse(options.custom_actions);
                    } catch (error) {
                        console.error('解析自定义操作失败:', error);
                        props.customActions = [];
                    }
                }
                
                // 解析验证规则
                if (options.validation_rules) {
                    try {
                        props.validationRules = JSON.parse(options.validation_rules);
                    } catch (error) {
                        console.error('解析验证规则失败:', error);
                        props.validationRules = [];
                    }
                }
                
                return props;
            },
            
            supportedOptions: [
                {
                    label: _t("Enable SMS"),
                    name: "enable_sms",
                    type: "boolean",
                    default: true,
                },
                {
                    label: _t("Enable Call"),
                    name: "enable_call",
                    type: "boolean",
                    default: true,
                },
                {
                    label: _t("Enable WhatsApp"),
                    name: "enable_whatsapp",
                    type: "boolean",
                    default: false,
                },
                {
                    label: _t("Enable Validation"),
                    name: "enable_validation",
                    type: "boolean",
                    default: true,
                },
                {
                    label: _t("Show Country Code"),
                    name: "show_country_code",
                    type: "boolean",
                    default: true,
                },
                {
                    label: _t("Auto Format"),
                    name: "auto_format",
                    type: "boolean",
                    default: true,
                },
                {
                    label: _t("Enable History"),
                    name: "enable_history",
                    type: "boolean",
                    default: true,
                },
                {
                    label: _t("Custom Actions"),
                    name: "custom_actions",
                    type: "string",
                    default: "[]",
                },
                {
                    label: _t("Validation Rules"),
                    name: "validation_rules",
                    type: "string",
                    default: "[]",
                }
            ],
        });
        
        // 电话号码验证器
        class PhoneValidator {
            constructor() {
                this.listeners = [];
            }
            
            onValidationComplete(callback) {
                this.listeners.push(callback);
            }
            
            async validate(phoneNumber, options = {}) {
                const result = {
                    isValid: true,
                    errors: [],
                    warnings: []
                };
                
                // 基本格式验证
                if (!phoneNumber || phoneNumber.trim() === '') {
                    result.isValid = false;
                    result.errors.push('电话号码不能为空');
                    return result;
                }
                
                // 长度验证
                const cleanNumber = phoneNumber.replace(/[^\d]/g, '');
                if (cleanNumber.length < 7) {
                    result.isValid = false;
                    result.errors.push('电话号码太短');
                } else if (cleanNumber.length > 15) {
                    result.isValid = false;
                    result.errors.push('电话号码太长');
                }
                
                // 格式验证
                const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
                if (!phoneRegex.test(cleanNumber)) {
                    result.isValid = false;
                    result.errors.push('电话号码格式不正确');
                }
                
                // 执行自定义验证规则
                for (const rule of options.rules || []) {
                    const ruleResult = await this.executeValidationRule(rule, phoneNumber);
                    if (!ruleResult.isValid) {
                        result.isValid = false;
                        result.errors.push(...ruleResult.errors);
                    }
                    if (ruleResult.warnings) {
                        result.warnings.push(...ruleResult.warnings);
                    }
                }
                
                // 通知监听器
                this.listeners.forEach(callback => callback(result));
                
                return result;
            }
            
            async executeValidationRule(rule, phoneNumber) {
                // 执行自定义验证规则
                return { isValid: true, errors: [], warnings: [] };
            }
        }
        
        // 电话号码格式化器
        class PhoneFormatter {
            constructor() {
                this.listeners = [];
            }
            
            onFormatComplete(callback) {
                this.listeners.push(callback);
            }
            
            async format(phoneNumber, options = {}) {
                const { country, style = 'international' } = options;
                
                // 清理号码
                const cleanNumber = phoneNumber.replace(/[^\d\+]/g, '');
                
                // 根据样式格式化
                let formatted;
                switch (style) {
                    case 'national':
                        formatted = this.formatNational(cleanNumber, country);
                        break;
                    case 'international':
                        formatted = this.formatInternational(cleanNumber);
                        break;
                    case 'e164':
                        formatted = this.formatE164(cleanNumber);
                        break;
                    default:
                        formatted = cleanNumber;
                }
                
                // 通知监听器
                this.listeners.forEach(callback => callback(formatted));
                
                return formatted;
            }
            
            formatNational(number, country) {
                // 实现国内格式化
                return number;
            }
            
            formatInternational(number) {
                // 实现国际格式化
                if (!number.startsWith('+')) {
                    return '+' + number;
                }
                return number;
            }
            
            formatE164(number) {
                // 实现E164格式化
                return '+' + number.replace(/[^\d]/g, '');
            }
        }
        
        // 电话历史管理器
        class PhoneHistoryManager {
            constructor() {
                this.history = [];
            }
            
            add(item) {
                this.history.unshift(item);
                this.save();
            }
            
            async load(recordId) {
                try {
                    const stored = localStorage.getItem(`phone_history_${recordId}`);
                    if (stored) {
                        this.history = JSON.parse(stored);
                    }
                } catch (error) {
                    console.error('加载电话历史失败:', error);
                }
                return this.history;
            }
            
            save() {
                try {
                    localStorage.setItem('phone_history', JSON.stringify(this.history));
                } catch (error) {
                    console.error('保存电话历史失败:', error);
                }
            }
        }
        
        // 国家代码检测器
        class CountryCodeDetector {
            constructor() {
                this.countryCodes = new Map([
                    ['1', { code: 'US', name: 'United States', dialCode: '1' }],
                    ['86', { code: 'CN', name: 'China', dialCode: '86' }],
                    ['44', { code: 'GB', name: 'United Kingdom', dialCode: '44' }],
                    // 更多国家代码...
                ]);
            }
            
            async detect(phoneNumber) {
                const cleanNumber = phoneNumber.replace(/[^\d]/g, '');
                
                // 尝试匹配国家代码
                for (const [code, country] of this.countryCodes) {
                    if (cleanNumber.startsWith(code)) {
                        return country;
                    }
                }
                
                return null;
            }
        }
        
        // 应用增强的补丁
        patch(PhoneField, enhancedPhoneFieldPatch);
        patch(phoneField, enhancedPatchDescr());
        patch(formPhoneField, enhancedPatchDescr());
        
        // 导出增强的组件
        __exports.enhancedPhoneFieldPatch = enhancedPhoneFieldPatch;
        __exports.enhancedPatchDescr = enhancedPatchDescr;
    }
};

// 应用SMS电话字段组件增强
SMSPhoneFieldEnhancer.enhanceSMSPhoneField();
```

## 技术特点

### 1. 补丁机制
- 使用Odoo的patch工具扩展现有组件
- 保持与原有功能的兼容性
- 非侵入式的功能增强

### 2. 组件集成
- 无缝集成SMS按钮组件
- 保持组件层次结构的清晰
- 统一的属性和事件处理

### 3. 配置灵活性
- 支持通过XML选项控制功能
- 提供默认配置和可选配置
- 国际化的配置选项标签

### 4. 类型安全
- 明确的属性类型定义
- 可选属性的正确标识
- TypeScript友好的代码结构

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 通过补丁为现有组件添加新功能
- 保持原有接口不变

### 2. 组合模式 (Composite Pattern)
- 组合多个组件提供完整功能
- 统一的组件接口

### 3. 策略模式 (Strategy Pattern)
- 可配置的功能启用策略
- 灵活的选项处理

## 注意事项

1. **补丁顺序**: 确保补丁的应用顺序正确
2. **兼容性**: 保持与原有电话字段的兼容性
3. **性能影响**: 注意补丁对性能的影响
4. **测试覆盖**: 确保补丁功能的充分测试

## 扩展建议

1. **验证功能**: 添加电话号码格式验证
2. **国际化**: 支持国际电话号码格式
3. **历史记录**: 记录SMS发送历史
4. **批量操作**: 支持批量SMS发送
5. **模板支持**: 支持SMS模板选择

该SMS电话字段组件为SMS系统提供了重要的字段集成功能，是SMS服务与表单界面集成的核心组件。

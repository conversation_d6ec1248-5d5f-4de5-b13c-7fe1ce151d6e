# SMS Button - SMS按钮组件

## 概述

`sms_button.js` 是 Odoo SMS 模块的SMS按钮组件，专门用于在各种视图中提供SMS发送功能。该组件基于OWL框架和Web核心服务，集成了记录保存、SMS编辑器打开、上下文传递等核心功能，为SMS系统提供了完整的用户界面操作支持，是SMS服务用户交互的重要组件。

## 文件信息
- **路径**: `/sms/static/src/components/sms_button/sms_button.js`
- **行数**: 51
- **模块**: `@sms/components/sms_button/sms_button`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'    // 国际化翻译
'@web/core/user'                // 用户服务
'@web/core/utils/hooks'         // 钩子工具
'@odoo/owl'                     // OWL框架
```

## 核心功能

### 1. SendSMSButton 组件

```javascript
const SendSMSButton = class SendSMSButton extends Component {
    static template = "sms.SendSMSButton";
    static props = ["*"];
}
```

**组件特性**:
- **继承扩展**: 继承OWL Component的所有功能
- **通用属性**: 使用通配符属性接受所有传入的属性
- **模板绑定**: 使用专门的SMS按钮模板
- **灵活配置**: 支持灵活的属性配置

### 2. 组件初始化

```javascript
setup() {
    this.action = useService("action");
    this.title = _t("Send SMS");
}
```

**初始化功能**:
- **服务集成**: 集成操作服务用于执行SMS操作
- **标题设置**: 设置国际化的按钮标题
- **钩子使用**: 使用useService钩子获取服务
- **简洁设计**: 最小化的初始化配置

### 3. 电话链接生成

```javascript
get phoneHref() {
    return "sms:" + this.props.record.data[this.props.name].replace(/\s+/g, "");
}
```

**链接生成功能**:
- **协议支持**: 生成SMS协议链接
- **号码提取**: 从记录数据中提取电话号码
- **格式清理**: 移除电话号码中的空格
- **动态生成**: 基于当前记录动态生成链接

### 4. 点击事件处理

```javascript
async onClick() {
    await this.props.record.save();
    this.action.doAction(
        {
            type: "ir.actions.act_window",
            target: "new",
            name: this.title,
            res_model: "sms.composer",
            views: [[false, "form"]],
            context: {
                ...user.context,
                default_res_model: this.props.record.resModel,
                default_res_id: this.props.record.resId,
                default_number_field_name: this.props.name,
                default_composition_mode: "comment",
            },
        },
        {
            onClose: () => {
                if (status(this) === "destroyed") {
                    return;
                }
                this.props.record.load();
            },
        }
    );
}
```

**点击处理功能**:
- **记录保存**: 在打开SMS编辑器前保存当前记录
- **操作执行**: 执行打开SMS编辑器的操作
- **新窗口**: 在新窗口中打开SMS编辑器
- **上下文传递**: 传递完整的上下文信息
- **关闭回调**: 设置窗口关闭后的回调处理
- **状态检查**: 检查组件状态避免在销毁后操作
- **记录刷新**: 关闭后刷新当前记录

## 使用场景

### 1. SMS按钮组件增强

```javascript
// SMS按钮组件增强功能
const SMSButtonEnhancer = {
    enhanceSMSButton: () => {
        // 增强的SMS按钮组件
        class EnhancedSendSMSButton extends SendSMSButton {
            static props = {
                ...SendSMSButton.props,
                enableQuickSend: { type: Boolean, optional: true },
                enableTemplates: { type: Boolean, optional: true },
                enableHistory: { type: Boolean, optional: true },
                enableValidation: { type: Boolean, optional: true },
                customTemplates: { type: Array, optional: true },
                quickSendText: { type: String, optional: true },
                buttonStyle: { type: String, optional: true },
                buttonSize: { type: String, optional: true },
                showIcon: { type: Boolean, optional: true },
                showText: { type: Boolean, optional: true }
            };
            
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    enableQuickSend: this.props.enableQuickSend || false,
                    enableTemplates: this.props.enableTemplates || true,
                    enableHistory: this.props.enableHistory || true,
                    enableValidation: this.props.enableValidation || true,
                    enableAnalytics: true,
                    enableNotifications: true,
                    enableBulkSend: false,
                    enableScheduling: false,
                    enableDeliveryTracking: true
                };
                
                // 增强的状态
                this.enhancedState = useState({
                    isLoading: false,
                    lastSentTime: null,
                    sendHistory: [],
                    availableTemplates: [],
                    validationResult: null,
                    deliveryStatus: null,
                    quickSendMode: false
                });
                
                // SMS服务
                this.smsService = useService("sms");
                
                // 通知服务
                this.notification = useService("notification");
                
                // 模板管理器
                this.templateManager = new SMSTemplateManager();
                
                // 历史管理器
                this.historyManager = new SMSHistoryManager();
                
                // 验证器
                this.validator = new SMSValidator();
                
                // 分析器
                this.analytics = new SMSAnalytics();
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 加载模板
                if (this.enhancedConfig.enableTemplates) {
                    this.loadTemplates();
                }
                
                // 加载历史
                if (this.enhancedConfig.enableHistory) {
                    this.loadHistory();
                }
                
                // 验证电话号码
                if (this.enhancedConfig.enableValidation) {
                    this.validatePhoneNumber();
                }
            }
            
            // 增强的电话链接生成
            get phoneHref() {
                const phoneNumber = this.props.record.data[this.props.name];
                if (!phoneNumber) return "";
                
                // 清理和格式化号码
                const cleanNumber = phoneNumber.replace(/[^\d\+]/g, "");
                
                // 添加快速发送文本
                if (this.enhancedConfig.enableQuickSend && this.props.quickSendText) {
                    return `sms:${cleanNumber}?body=${encodeURIComponent(this.props.quickSendText)}`;
                }
                
                return `sms:${cleanNumber}`;
            }
            
            // 增强的点击处理
            async onClick() {
                try {
                    this.enhancedState.isLoading = true;
                    
                    // 验证电话号码
                    if (this.enhancedConfig.enableValidation) {
                        const isValid = await this.validatePhoneNumber();
                        if (!isValid) {
                            this.notification.add('电话号码无效', { type: 'error' });
                            return;
                        }
                    }
                    
                    // 记录分析事件
                    if (this.enhancedConfig.enableAnalytics) {
                        this.analytics.recordButtonClick(this.props.record);
                    }
                    
                    // 快速发送模式
                    if (this.enhancedState.quickSendMode && this.props.quickSendText) {
                        await this.quickSend(this.props.quickSendText);
                        return;
                    }
                    
                    // 执行原有点击逻辑
                    await super.onClick();
                    
                } catch (error) {
                    console.error('SMS按钮点击失败:', error);
                    this.notification.add('操作失败', { type: 'error' });
                } finally {
                    this.enhancedState.isLoading = false;
                }
            }
            
            // 快速发送SMS
            async quickSend(message) {
                try {
                    const phoneNumber = this.props.record.data[this.props.name];
                    
                    // 保存记录
                    await this.props.record.save();
                    
                    // 发送SMS
                    const result = await this.smsService.send({
                        phone: phoneNumber,
                        message: message,
                        res_model: this.props.record.resModel,
                        res_id: this.props.record.resId
                    });
                    
                    if (result.success) {
                        this.notification.add('SMS发送成功', { type: 'success' });
                        this.recordSendHistory(message, result);
                        this.enhancedState.lastSentTime = Date.now();
                        
                        // 跟踪投递状态
                        if (this.enhancedConfig.enableDeliveryTracking) {
                            this.trackDelivery(result.message_id);
                        }
                    } else {
                        this.notification.add(result.error || 'SMS发送失败', { type: 'error' });
                    }
                    
                } catch (error) {
                    console.error('快速发送SMS失败:', error);
                    this.notification.add('发送失败', { type: 'error' });
                }
            }
            
            // 使用模板发送
            async sendWithTemplate(template) {
                try {
                    // 渲染模板
                    const message = await this.templateManager.render(template, this.props.record);
                    
                    // 发送SMS
                    await this.quickSend(message);
                    
                } catch (error) {
                    console.error('使用模板发送失败:', error);
                    this.notification.add('模板发送失败', { type: 'error' });
                }
            }
            
            // 批量发送SMS
            async bulkSend(records, message) {
                if (!this.enhancedConfig.enableBulkSend) return;
                
                try {
                    this.enhancedState.isLoading = true;
                    
                    const results = [];
                    for (const record of records) {
                        const phoneNumber = record.data[this.props.name];
                        if (phoneNumber) {
                            const result = await this.smsService.send({
                                phone: phoneNumber,
                                message: message,
                                res_model: record.resModel,
                                res_id: record.resId
                            });
                            results.push(result);
                        }
                    }
                    
                    const successCount = results.filter(r => r.success).length;
                    const failCount = results.length - successCount;
                    
                    this.notification.add(
                        `批量发送完成: ${successCount}成功, ${failCount}失败`,
                        { type: successCount > 0 ? 'success' : 'warning' }
                    );
                    
                } catch (error) {
                    console.error('批量发送失败:', error);
                    this.notification.add('批量发送失败', { type: 'error' });
                } finally {
                    this.enhancedState.isLoading = false;
                }
            }
            
            // 计划发送SMS
            async scheduleSend(message, scheduleTime) {
                if (!this.enhancedConfig.enableScheduling) return;
                
                try {
                    const phoneNumber = this.props.record.data[this.props.name];
                    
                    const result = await this.smsService.schedule({
                        phone: phoneNumber,
                        message: message,
                        schedule_time: scheduleTime,
                        res_model: this.props.record.resModel,
                        res_id: this.props.record.resId
                    });
                    
                    if (result.success) {
                        this.notification.add('SMS已计划发送', { type: 'success' });
                    } else {
                        this.notification.add(result.error || '计划发送失败', { type: 'error' });
                    }
                    
                } catch (error) {
                    console.error('计划发送失败:', error);
                    this.notification.add('计划发送失败', { type: 'error' });
                }
            }
            
            // 验证电话号码
            async validatePhoneNumber() {
                const phoneNumber = this.props.record.data[this.props.name];
                if (!phoneNumber) {
                    this.enhancedState.validationResult = { isValid: false, error: '电话号码为空' };
                    return false;
                }
                
                try {
                    const result = await this.validator.validate(phoneNumber);
                    this.enhancedState.validationResult = result;
                    return result.isValid;
                } catch (error) {
                    console.error('验证电话号码失败:', error);
                    return false;
                }
            }
            
            // 加载模板
            async loadTemplates() {
                try {
                    const templates = await this.templateManager.getTemplates(this.props.record.resModel);
                    this.enhancedState.availableTemplates = templates;
                } catch (error) {
                    console.error('加载模板失败:', error);
                }
            }
            
            // 加载历史
            async loadHistory() {
                try {
                    const history = await this.historyManager.getHistory(
                        this.props.record.resModel,
                        this.props.record.resId
                    );
                    this.enhancedState.sendHistory = history;
                } catch (error) {
                    console.error('加载历史失败:', error);
                }
            }
            
            // 记录发送历史
            recordSendHistory(message, result) {
                const historyItem = {
                    message: message,
                    phone: this.props.record.data[this.props.name],
                    timestamp: Date.now(),
                    status: result.success ? 'sent' : 'failed',
                    message_id: result.message_id
                };
                
                this.enhancedState.sendHistory.unshift(historyItem);
                this.historyManager.add(historyItem);
            }
            
            // 跟踪投递状态
            async trackDelivery(messageId) {
                try {
                    // 定期检查投递状态
                    const checkStatus = async () => {
                        const status = await this.smsService.getDeliveryStatus(messageId);
                        this.enhancedState.deliveryStatus = status;
                        
                        if (status.final) {
                            clearInterval(statusInterval);
                        }
                    };
                    
                    const statusInterval = setInterval(checkStatus, 5000);
                    
                    // 30秒后停止检查
                    setTimeout(() => {
                        clearInterval(statusInterval);
                    }, 30000);
                    
                } catch (error) {
                    console.error('跟踪投递状态失败:', error);
                }
            }
            
            // 切换快速发送模式
            toggleQuickSendMode() {
                this.enhancedState.quickSendMode = !this.enhancedState.quickSendMode;
            }
            
            // 获取按钮样式类
            getButtonClass() {
                const baseClass = 'btn btn-sm';
                const styleClass = this.props.buttonStyle || 'btn-primary';
                const sizeClass = this.props.buttonSize || '';
                const loadingClass = this.enhancedState.isLoading ? 'disabled' : '';
                
                return `${baseClass} ${styleClass} ${sizeClass} ${loadingClass}`.trim();
            }
            
            // 获取按钮标题
            getButtonTitle() {
                if (this.enhancedState.isLoading) {
                    return _t("Sending...");
                }
                
                if (this.enhancedState.quickSendMode) {
                    return _t("Quick Send SMS");
                }
                
                return this.title;
            }
            
            // 获取按钮图标
            getButtonIcon() {
                if (this.enhancedState.isLoading) {
                    return 'fa-spinner fa-spin';
                }
                
                if (this.enhancedState.quickSendMode) {
                    return 'fa-paper-plane';
                }
                
                return 'fa-comment-o';
            }
        }
        
        // SMS模板管理器
        class SMSTemplateManager {
            async getTemplates(model) {
                // 获取SMS模板
                return [];
            }
            
            async render(template, record) {
                // 渲染模板
                return template.body.replace(/\{\{(\w+)\}\}/g, (match, field) => {
                    return record.data[field] || match;
                });
            }
        }
        
        // SMS历史管理器
        class SMSHistoryManager {
            constructor() {
                this.history = [];
            }
            
            add(item) {
                this.history.unshift(item);
                this.save();
            }
            
            async getHistory(model, resId) {
                // 获取SMS历史
                return this.history.filter(item => 
                    item.res_model === model && item.res_id === resId
                );
            }
            
            save() {
                try {
                    localStorage.setItem('sms_history', JSON.stringify(this.history));
                } catch (error) {
                    console.error('保存SMS历史失败:', error);
                }
            }
        }
        
        // SMS验证器
        class SMSValidator {
            async validate(phoneNumber) {
                // 验证电话号码
                const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
                const cleanNumber = phoneNumber.replace(/[^\d\+]/g, '');
                
                return {
                    isValid: phoneRegex.test(cleanNumber),
                    error: phoneRegex.test(cleanNumber) ? null : '电话号码格式不正确'
                };
            }
        }
        
        // SMS分析器
        class SMSAnalytics {
            recordButtonClick(record) {
                // 记录按钮点击事件
                console.log('SMS按钮点击:', record.resModel, record.resId);
            }
            
            recordSend(phoneNumber, message) {
                // 记录SMS发送事件
                console.log('SMS发送:', phoneNumber, message.length);
            }
        }
        
        // 导出增强的组件
        __exports.EnhancedSendSMSButton = EnhancedSendSMSButton;
    }
};

// 应用SMS按钮组件增强
SMSButtonEnhancer.enhanceSMSButton();
```

## 技术特点

### 1. 组件设计
- 基于OWL框架的现代化组件
- 简洁的属性和生命周期管理
- 清晰的职责分离

### 2. 服务集成
- 深度集成操作服务
- 异步操作的优雅处理
- 完善的错误处理机制

### 3. 上下文管理
- 完整的上下文信息传递
- 智能的默认值设置
- 灵活的参数配置

### 4. 生命周期管理
- 组件状态的正确检查
- 资源的及时清理
- 回调的安全执行

## 设计模式

### 1. 组件模式 (Component Pattern)
- 封装的UI组件
- 可重用的界面元素

### 2. 命令模式 (Command Pattern)
- 封装SMS操作为命令
- 统一的操作执行接口

### 3. 观察者模式 (Observer Pattern)
- 窗口关闭事件的监听
- 状态变化的响应

## 注意事项

1. **状态检查**: 确保在组件销毁后不执行操作
2. **记录保存**: 在打开SMS编辑器前保存记录
3. **错误处理**: 提供完善的错误处理和用户反馈
4. **性能优化**: 避免不必要的操作和资源浪费

## 扩展建议

1. **快速发送**: 添加快速发送SMS的功能
2. **模板支持**: 支持SMS模板的选择和使用
3. **历史记录**: 记录和显示SMS发送历史
4. **批量发送**: 支持批量SMS发送功能
5. **状态跟踪**: 跟踪SMS的发送和投递状态

该SMS按钮组件为SMS系统提供了重要的用户界面操作功能，是SMS服务用户交互的核心组件。

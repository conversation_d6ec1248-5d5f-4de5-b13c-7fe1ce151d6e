# SMS Notification Model Patch - SMS通知模型补丁

## 概述

`notification_model_patch.js` 是 Odoo SMS 模块的通知模型补丁，专门用于扩展邮件模块的通知模型以支持SMS通知显示。该补丁基于Odoo的补丁机制，为SMS类型的通知提供了专门的图标和标签，确保SMS通知在用户界面中能够正确显示和识别。

## 文件信息
- **路径**: `/sms/static/src/core/notification_model_patch.js`
- **行数**: 26
- **模块**: `@sms/core/notification_model_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/notification_model'  // 邮件通知模型
'@web/core/l10n/translation'            // 国际化翻译
'@web/core/utils/patch'                  // 补丁工具
```

## 核心功能

### 1. 通知模型补丁

```javascript
patch(Notification.prototype, {
    get icon() {
        if (this.notification_type === "sms") {
            return "fa fa-mobile";
        }
        return super.icon;
    },
    get label() {
        if (this.notification_type === "sms") {
            return _t("SMS");
        }
        return super.label;
    },
});
```

**补丁功能**:
- **类型检测**: 检测通知类型是否为SMS
- **图标定制**: 为SMS通知提供移动设备图标
- **标签定制**: 为SMS通知提供专门的标签
- **国际化**: 标签支持多语言翻译
- **继承保持**: 非SMS类型通知保持原有行为

### 2. 图标获取

```javascript
get icon() {
    if (this.notification_type === "sms") {
        return "fa fa-mobile";
    }
    return super.icon;
}
```

**图标功能**:
- **条件判断**: 根据通知类型返回相应图标
- **SMS图标**: SMS通知使用移动设备图标
- **FontAwesome**: 使用FontAwesome图标库
- **默认行为**: 非SMS类型使用父类的图标逻辑

### 3. 标签获取

```javascript
get label() {
    if (this.notification_type === "sms") {
        return _t("SMS");
    }
    return super.label;
}
```

**标签功能**:
- **类型特化**: 为SMS通知提供特定的标签
- **翻译支持**: 使用翻译函数支持多语言
- **简洁明了**: 提供清晰的SMS标识
- **继承机制**: 保持其他类型通知的原有标签

## 使用场景

### 1. SMS通知模型补丁增强

```javascript
// SMS通知模型补丁增强功能
const SMSNotificationModelPatchEnhancer = {
    enhanceSMSNotificationModelPatch: () => {
        // 增强的SMS通知模型补丁
        const enhancedNotificationPatch = {
            // 增强的图标获取
            get icon() {
                if (this.notification_type === "sms") {
                    // 根据通知状态返回不同图标
                    switch (this.notification_status) {
                        case 'sent':
                            return "fa fa-mobile text-success";
                        case 'delivered':
                            return "fa fa-mobile text-info";
                        case 'failed':
                            return "fa fa-mobile text-danger";
                        case 'pending':
                            return "fa fa-mobile text-warning";
                        case 'bounced':
                            return "fa fa-mobile text-muted";
                        default:
                            return "fa fa-mobile";
                    }
                }
                return super.icon;
            },
            
            // 增强的标签获取
            get label() {
                if (this.notification_type === "sms") {
                    // 根据通知状态返回详细标签
                    switch (this.notification_status) {
                        case 'sent':
                            return _t("SMS Sent");
                        case 'delivered':
                            return _t("SMS Delivered");
                        case 'failed':
                            return _t("SMS Failed");
                        case 'pending':
                            return _t("SMS Pending");
                        case 'bounced':
                            return _t("SMS Bounced");
                        case 'read':
                            return _t("SMS Read");
                        default:
                            return _t("SMS");
                    }
                }
                return super.label;
            },
            
            // 获取通知状态描述
            get statusDescription() {
                if (this.notification_type === "sms") {
                    switch (this.notification_status) {
                        case 'sent':
                            return _t("SMS has been sent successfully");
                        case 'delivered':
                            return _t("SMS has been delivered to recipient");
                        case 'failed':
                            return _t("SMS sending failed");
                        case 'pending':
                            return _t("SMS is pending delivery");
                        case 'bounced':
                            return _t("SMS was bounced back");
                        case 'read':
                            return _t("SMS has been read by recipient");
                        default:
                            return _t("SMS notification");
                    }
                }
                return super.statusDescription || '';
            },
            
            // 获取通知颜色
            get color() {
                if (this.notification_type === "sms") {
                    switch (this.notification_status) {
                        case 'sent':
                        case 'delivered':
                        case 'read':
                            return 'success';
                        case 'failed':
                        case 'bounced':
                            return 'danger';
                        case 'pending':
                            return 'warning';
                        default:
                            return 'info';
                    }
                }
                return super.color || 'info';
            },
            
            // 获取通知优先级
            get priority() {
                if (this.notification_type === "sms") {
                    switch (this.notification_status) {
                        case 'failed':
                        case 'bounced':
                            return 'high';
                        case 'pending':
                            return 'medium';
                        case 'sent':
                        case 'delivered':
                        case 'read':
                            return 'low';
                        default:
                            return 'medium';
                    }
                }
                return super.priority || 'medium';
            },
            
            // 获取详细信息
            get details() {
                if (this.notification_type === "sms") {
                    const details = {
                        type: 'SMS',
                        status: this.notification_status,
                        recipient: this.res_partner_id?.[1] || this.sms_number,
                        sentTime: this.create_date,
                        deliveryTime: this.delivery_date,
                        provider: this.sms_provider,
                        messageId: this.sms_id_external,
                        cost: this.sms_cost
                    };
                    
                    // 添加失败信息
                    if (this.notification_status === 'failed') {
                        details.failureReason = this.failure_reason;
                        details.errorCode = this.error_code;
                        details.errorMessage = this.error_message;
                    }
                    
                    // 添加投递信息
                    if (this.notification_status === 'delivered') {
                        details.deliveryConfirmation = this.delivery_confirmation;
                        details.deliveryTime = this.delivery_date;
                    }
                    
                    return details;
                }
                return super.details || {};
            },
            
            // 获取可用操作
            get availableActions() {
                if (this.notification_type === "sms") {
                    const actions = [];
                    
                    // 查看详情
                    actions.push({
                        name: 'view_details',
                        label: _t("View Details"),
                        icon: 'fa-info-circle'
                    });
                    
                    // 重发SMS（如果失败）
                    if (this.notification_status === 'failed') {
                        actions.push({
                            name: 'resend',
                            label: _t("Resend SMS"),
                            icon: 'fa-refresh'
                        });
                    }
                    
                    // 复制消息
                    actions.push({
                        name: 'copy_message',
                        label: _t("Copy Message"),
                        icon: 'fa-copy'
                    });
                    
                    // 查看对话
                    if (this.res_partner_id) {
                        actions.push({
                            name: 'view_conversation',
                            label: _t("View Conversation"),
                            icon: 'fa-comments'
                        });
                    }
                    
                    return actions;
                }
                return super.availableActions || [];
            },
            
            // 获取工具提示
            get tooltip() {
                if (this.notification_type === "sms") {
                    let tooltip = this.statusDescription;
                    
                    if (this.res_partner_id) {
                        tooltip += `\n${_t("Recipient")}: ${this.res_partner_id[1]}`;
                    } else if (this.sms_number) {
                        tooltip += `\n${_t("Number")}: ${this.sms_number}`;
                    }
                    
                    if (this.create_date) {
                        tooltip += `\n${_t("Sent")}: ${this.create_date}`;
                    }
                    
                    if (this.delivery_date && this.notification_status === 'delivered') {
                        tooltip += `\n${_t("Delivered")}: ${this.delivery_date}`;
                    }
                    
                    if (this.sms_cost) {
                        tooltip += `\n${_t("Cost")}: ${this.sms_cost}`;
                    }
                    
                    return tooltip;
                }
                return super.tooltip || '';
            },
            
            // 检查是否可重发
            get canResend() {
                if (this.notification_type === "sms") {
                    return this.notification_status === 'failed';
                }
                return super.canResend || false;
            },
            
            // 检查是否已读
            get isRead() {
                if (this.notification_type === "sms") {
                    return this.notification_status === 'read';
                }
                return super.isRead || false;
            },
            
            // 检查是否成功
            get isSuccess() {
                if (this.notification_type === "sms") {
                    return ['sent', 'delivered', 'read'].includes(this.notification_status);
                }
                return super.isSuccess || false;
            },
            
            // 检查是否失败
            get isFailed() {
                if (this.notification_type === "sms") {
                    return ['failed', 'bounced'].includes(this.notification_status);
                }
                return super.isFailed || false;
            },
            
            // 检查是否待处理
            get isPending() {
                if (this.notification_type === "sms") {
                    return this.notification_status === 'pending';
                }
                return super.isPending || false;
            },
            
            // 获取CSS类名
            get cssClass() {
                if (this.notification_type === "sms") {
                    const baseClass = 'o_sms_notification';
                    const statusClass = `o_sms_notification_${this.notification_status}`;
                    const colorClass = `o_sms_notification_${this.color}`;
                    
                    return `${baseClass} ${statusClass} ${colorClass}`;
                }
                return super.cssClass || 'o_notification';
            },
            
            // 获取格式化的发送时间
            get formattedSentTime() {
                if (this.notification_type === "sms" && this.create_date) {
                    return moment(this.create_date).format('YYYY-MM-DD HH:mm:ss');
                }
                return super.formattedSentTime || '';
            },
            
            // 获取格式化的投递时间
            get formattedDeliveryTime() {
                if (this.notification_type === "sms" && this.delivery_date) {
                    return moment(this.delivery_date).format('YYYY-MM-DD HH:mm:ss');
                }
                return super.formattedDeliveryTime || '';
            },
            
            // 获取投递延迟时间
            get deliveryDelay() {
                if (this.notification_type === "sms" && 
                    this.create_date && this.delivery_date) {
                    const sent = moment(this.create_date);
                    const delivered = moment(this.delivery_date);
                    return delivered.diff(sent, 'seconds');
                }
                return super.deliveryDelay || null;
            }
        };
        
        // 应用增强的补丁
        patch(Notification.prototype, enhancedNotificationPatch);
        
        // 导出增强的补丁
        __exports.enhancedNotificationPatch = enhancedNotificationPatch;
    }
};

// 应用SMS通知模型补丁增强
SMSNotificationModelPatchEnhancer.enhanceSMSNotificationModelPatch();
```

## 技术特点

### 1. 补丁机制
- 使用Odoo标准的补丁工具
- 非侵入式的功能扩展
- 保持与原有功能的兼容性

### 2. 类型识别
- 精确的SMS类型检测
- 条件性的功能应用
- 默认行为的保持

### 3. 视觉标识
- 专门的SMS图标
- 清晰的标签标识
- 统一的视觉风格

### 4. 国际化支持
- 完整的多语言支持
- 用户友好的标签文本
- 标准的翻译机制

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 通过补丁为现有模型添加新功能
- 保持原有接口不变

### 2. 策略模式 (Strategy Pattern)
- 根据通知类型选择不同的显示策略
- 灵活的视觉定制

### 3. 工厂方法模式 (Factory Method Pattern)
- 根据类型创建相应的图标和标签
- 统一的创建接口

## 注意事项

1. **补丁顺序**: 确保补丁在邮件模块加载后应用
2. **类型检查**: 准确识别SMS类型的通知
3. **图标一致性**: 保持SMS图标的一致性
4. **向后兼容**: 保持与现有通知显示的兼容性

## 扩展建议

1. **状态细分**: 根据SMS发送状态提供更详细的图标
2. **颜色编码**: 使用颜色编码表示不同的通知状态
3. **动画效果**: 为通知状态变化添加动画效果
4. **批量操作**: 支持SMS通知的批量操作
5. **统计信息**: 显示SMS通知的统计信息

该SMS通知模型补丁为SMS系统提供了重要的通知显示功能，确保SMS通知在用户界面中能够正确识别和显示。

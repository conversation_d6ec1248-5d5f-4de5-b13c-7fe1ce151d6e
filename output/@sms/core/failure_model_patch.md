# SMS Failure Model Patch - SMS失败模型补丁

## 概述

`failure_model_patch.js` 是 Odoo SMS 模块的失败模型补丁，专门用于扩展邮件模块的失败模型以支持SMS失败处理。该补丁基于Odoo的补丁机制，为SMS类型的失败提供了专门的图标和错误消息，确保SMS发送失败时能够提供准确的用户反馈和视觉提示。

## 文件信息
- **路径**: `/sms/static/src/core/failure_model_patch.js`
- **行数**: 26
- **模块**: `@sms/core/failure_model_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/failure_model'   // 邮件失败模型
'@web/core/l10n/translation'        // 国际化翻译
'@web/core/utils/patch'              // 补丁工具
```

## 核心功能

### 1. 失败模型补丁

```javascript
patch(Failure.prototype, {
    get iconSrc() {
        if (this.type === "sms") {
            return "/sms/static/img/sms_failure.svg";
        }
        return super.iconSrc;
    },
    get body() {
        if (this.type === "sms") {
            return _t("An error occurred when sending an SMS");
        }
        return super.body;
    },
});
```

**补丁功能**:
- **类型检测**: 检测失败类型是否为SMS
- **图标定制**: 为SMS失败提供专门的SVG图标
- **消息定制**: 为SMS失败提供专门的错误消息
- **国际化**: 错误消息支持多语言翻译
- **继承保持**: 非SMS类型失败保持原有行为

### 2. 图标源获取

```javascript
get iconSrc() {
    if (this.type === "sms") {
        return "/sms/static/img/sms_failure.svg";
    }
    return super.iconSrc;
}
```

**图标功能**:
- **条件判断**: 根据失败类型返回相应图标
- **SMS图标**: SMS失败使用专门的失败图标
- **路径指定**: 指定SMS失败图标的完整路径
- **默认行为**: 非SMS类型使用父类的图标逻辑

### 3. 错误消息获取

```javascript
get body() {
    if (this.type === "sms") {
        return _t("An error occurred when sending an SMS");
    }
    return super.body;
}
```

**消息功能**:
- **类型特化**: 为SMS失败提供特定的错误消息
- **翻译支持**: 使用翻译函数支持多语言
- **用户友好**: 提供清晰易懂的错误描述
- **继承机制**: 保持其他类型失败的原有消息

## 使用场景

### 1. SMS失败模型补丁增强

```javascript
// SMS失败模型补丁增强功能
const SMSFailureModelPatchEnhancer = {
    enhanceSMSFailureModelPatch: () => {
        // 增强的SMS失败模型补丁
        const enhancedFailurePatch = {
            // 增强的图标源获取
            get iconSrc() {
                if (this.type === "sms") {
                    // 根据失败原因返回不同图标
                    switch (this.reason) {
                        case 'insufficient_credit':
                            return "/sms/static/img/sms_credit_failure.svg";
                        case 'invalid_number':
                            return "/sms/static/img/sms_number_failure.svg";
                        case 'network_error':
                            return "/sms/static/img/sms_network_failure.svg";
                        case 'service_unavailable':
                            return "/sms/static/img/sms_service_failure.svg";
                        default:
                            return "/sms/static/img/sms_failure.svg";
                    }
                }
                return super.iconSrc;
            },
            
            // 增强的错误消息获取
            get body() {
                if (this.type === "sms") {
                    // 根据失败原因返回详细消息
                    switch (this.reason) {
                        case 'insufficient_credit':
                            return _t("SMS sending failed: Insufficient credit balance");
                        case 'invalid_number':
                            return _t("SMS sending failed: Invalid phone number format");
                        case 'network_error':
                            return _t("SMS sending failed: Network connection error");
                        case 'service_unavailable':
                            return _t("SMS sending failed: SMS service temporarily unavailable");
                        case 'rate_limit':
                            return _t("SMS sending failed: Rate limit exceeded");
                        case 'blocked_number':
                            return _t("SMS sending failed: Number is blocked or opted out");
                        case 'content_filtered':
                            return _t("SMS sending failed: Message content was filtered");
                        default:
                            return _t("An error occurred when sending an SMS");
                    }
                }
                return super.body;
            },
            
            // 获取失败严重程度
            get severity() {
                if (this.type === "sms") {
                    switch (this.reason) {
                        case 'insufficient_credit':
                        case 'service_unavailable':
                            return 'warning'; // 可恢复的错误
                        case 'invalid_number':
                        case 'blocked_number':
                        case 'content_filtered':
                            return 'error'; // 需要用户干预的错误
                        case 'network_error':
                        case 'rate_limit':
                            return 'info'; // 临时性错误
                        default:
                            return 'error';
                    }
                }
                return super.severity || 'error';
            },
            
            // 获取建议操作
            get suggestedActions() {
                if (this.type === "sms") {
                    const actions = [];
                    
                    switch (this.reason) {
                        case 'insufficient_credit':
                            actions.push({
                                label: _t("Buy Credits"),
                                action: 'buy_credits',
                                icon: 'fa-credit-card',
                                primary: true
                            });
                            break;
                        case 'invalid_number':
                            actions.push({
                                label: _t("Edit Number"),
                                action: 'edit_number',
                                icon: 'fa-edit',
                                primary: true
                            });
                            break;
                        case 'network_error':
                        case 'service_unavailable':
                            actions.push({
                                label: _t("Retry"),
                                action: 'retry',
                                icon: 'fa-refresh',
                                primary: true
                            });
                            break;
                        case 'rate_limit':
                            actions.push({
                                label: _t("Retry Later"),
                                action: 'retry_later',
                                icon: 'fa-clock-o',
                                primary: true
                            });
                            break;
                        case 'content_filtered':
                            actions.push({
                                label: _t("Edit Message"),
                                action: 'edit_message',
                                icon: 'fa-edit',
                                primary: true
                            });
                            break;
                    }
                    
                    // 通用操作
                    actions.push({
                        label: _t("View Details"),
                        action: 'view_details',
                        icon: 'fa-info-circle',
                        primary: false
                    });
                    
                    return actions;
                }
                return super.suggestedActions || [];
            },
            
            // 获取失败详细信息
            get details() {
                if (this.type === "sms") {
                    const details = {
                        type: 'SMS',
                        timestamp: this.timestamp,
                        recipient: this.recipient,
                        reason: this.reason,
                        errorCode: this.errorCode,
                        provider: this.provider
                    };
                    
                    // 添加特定原因的详细信息
                    switch (this.reason) {
                        case 'insufficient_credit':
                            details.currentBalance = this.currentBalance;
                            details.requiredCredits = this.requiredCredits;
                            break;
                        case 'invalid_number':
                            details.numberFormat = this.numberFormat;
                            details.expectedFormat = this.expectedFormat;
                            break;
                        case 'rate_limit':
                            details.currentRate = this.currentRate;
                            details.maxRate = this.maxRate;
                            details.resetTime = this.resetTime;
                            break;
                    }
                    
                    return details;
                }
                return super.details || {};
            },
            
            // 获取恢复建议
            get recoveryTips() {
                if (this.type === "sms") {
                    switch (this.reason) {
                        case 'insufficient_credit':
                            return [
                                _t("Purchase additional SMS credits"),
                                _t("Check your account balance"),
                                _t("Consider upgrading your plan")
                            ];
                        case 'invalid_number':
                            return [
                                _t("Verify the phone number format"),
                                _t("Include country code if missing"),
                                _t("Remove any special characters")
                            ];
                        case 'network_error':
                            return [
                                _t("Check your internet connection"),
                                _t("Try again in a few moments"),
                                _t("Contact support if problem persists")
                            ];
                        case 'service_unavailable':
                            return [
                                _t("SMS service is temporarily down"),
                                _t("Try again later"),
                                _t("Check service status page")
                            ];
                        case 'rate_limit':
                            return [
                                _t("You've exceeded the sending rate limit"),
                                _t("Wait before sending more messages"),
                                _t("Consider spreading sends over time")
                            ];
                        case 'blocked_number':
                            return [
                                _t("Recipient has opted out of SMS"),
                                _t("Verify consent before sending"),
                                _t("Use alternative communication method")
                            ];
                        case 'content_filtered':
                            return [
                                _t("Message content was blocked"),
                                _t("Avoid promotional language"),
                                _t("Review SMS content guidelines")
                            ];
                        default:
                            return [
                                _t("Check SMS configuration"),
                                _t("Verify recipient information"),
                                _t("Contact administrator if needed")
                            ];
                    }
                }
                return super.recoveryTips || [];
            },
            
            // 获取CSS类名
            get cssClass() {
                if (this.type === "sms") {
                    const baseClass = 'o_sms_failure';
                    const severityClass = `o_sms_failure_${this.severity}`;
                    const reasonClass = `o_sms_failure_${this.reason || 'unknown'}`;
                    
                    return `${baseClass} ${severityClass} ${reasonClass}`;
                }
                return super.cssClass || 'o_failure';
            },
            
            // 检查是否可重试
            get canRetry() {
                if (this.type === "sms") {
                    const retryableReasons = [
                        'network_error',
                        'service_unavailable',
                        'rate_limit'
                    ];
                    return retryableReasons.includes(this.reason);
                }
                return super.canRetry || false;
            },
            
            // 检查是否需要用户操作
            get requiresUserAction() {
                if (this.type === "sms") {
                    const userActionReasons = [
                        'insufficient_credit',
                        'invalid_number',
                        'blocked_number',
                        'content_filtered'
                    ];
                    return userActionReasons.includes(this.reason);
                }
                return super.requiresUserAction || false;
            },
            
            // 获取预计恢复时间
            get estimatedRecoveryTime() {
                if (this.type === "sms") {
                    switch (this.reason) {
                        case 'network_error':
                            return 30; // 30秒
                        case 'service_unavailable':
                            return 300; // 5分钟
                        case 'rate_limit':
                            return this.resetTime ? 
                                Math.max(0, this.resetTime - Date.now()) : 
                                3600; // 1小时默认
                        default:
                            return null; // 需要用户干预，无法自动恢复
                    }
                }
                return super.estimatedRecoveryTime || null;
            }
        };
        
        // 应用增强的补丁
        patch(Failure.prototype, enhancedFailurePatch);
        
        // 导出增强的补丁
        __exports.enhancedFailurePatch = enhancedFailurePatch;
    }
};

// 应用SMS失败模型补丁增强
SMSFailureModelPatchEnhancer.enhanceSMSFailureModelPatch();
```

## 技术特点

### 1. 补丁机制
- 使用Odoo标准的补丁工具
- 非侵入式的功能扩展
- 保持与原有功能的兼容性

### 2. 类型识别
- 精确的SMS类型检测
- 条件性的功能应用
- 默认行为的保持

### 3. 资源管理
- 专门的SMS失败图标
- 清晰的资源路径定义
- 统一的资源管理

### 4. 国际化支持
- 完整的多语言支持
- 用户友好的错误消息
- 标准的翻译机制

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 通过补丁为现有模型添加新功能
- 保持原有接口不变

### 2. 策略模式 (Strategy Pattern)
- 根据失败类型选择不同的处理策略
- 灵活的行为定制

### 3. 模板方法模式 (Template Method Pattern)
- 定义失败处理的基本框架
- 允许子类定制特定步骤

## 注意事项

1. **补丁顺序**: 确保补丁在邮件模块加载后应用
2. **类型检查**: 准确识别SMS类型的失败
3. **资源路径**: 确保SMS图标资源的正确路径
4. **向后兼容**: 保持与现有失败处理的兼容性

## 扩展建议

1. **详细分类**: 根据具体失败原因提供更详细的分类
2. **恢复建议**: 为不同类型的失败提供恢复建议
3. **重试机制**: 实现自动重试机制
4. **统计分析**: 收集和分析SMS失败统计数据
5. **通知集成**: 集成通知系统提供失败提醒

该SMS失败模型补丁为SMS系统提供了重要的错误处理和用户反馈功能，确保SMS发送失败时能够提供准确的信息和适当的视觉提示。

# Odoo 剪贴板 Polyfill (Clipboard Polyfill) 学习资料

## 文件概述

**文件路径**: `output/@web/polyfills/clipboard.js`  
**原始路径**: `/web/static/src/polyfills/clipboard.js`  
**模块类型**: Polyfill 模块 - 剪贴板 API 兼容性补丁  
**代码行数**: 108 行  
**依赖关系**: 无外部依赖，纯原生 JavaScript 实现

## 模块功能

剪贴板 Polyfill 模块是 Odoo Web 客户端的浏览器兼容性补丁。该模块提供了：
- 现代剪贴板 API 的向后兼容实现
- ClipboardItem 类的 Polyfill 实现
- clipboard.write() 方法的兼容性补丁
- 多种数据类型的剪贴板支持
- 跨浏览器的统一剪贴板操作接口

这个模块确保了 Odoo 在不完全支持现代剪贴板 API 的浏览器中也能正常工作，特别是针对 Firefox 等浏览器的兼容性问题。

## 剪贴板 API 背景

### 现代剪贴板 API
现代浏览器提供了新的 Clipboard API，包括：
- `navigator.clipboard.write()` - 写入剪贴板
- `navigator.clipboard.read()` - 读取剪贴板
- `ClipboardItem` - 剪贴板项目类
- 支持多种 MIME 类型的数据

### 兼容性问题
- **Firefox**: 部分支持，需要功能标志启用
- **Safari**: 早期版本支持有限
- **Chrome**: 较好支持，但仍有版本差异
- **移动浏览器**: 支持程度不一

### Polyfill 策略
- 检测现有 API 支持程度
- 只补丁缺失的功能
- 使用传统 API 实现现代接口
- 保持 API 一致性

## 核心类详解

### ClipboardItemImpl - 剪贴板项目实现
```javascript
class ClipboardItemImpl {
    constructor(items, options = {}) {
        this.items = items;
        this.options = options;
    }
    
    get presentationStyle() {
        return this.options.presentationStyle;
    }
    
    get types() {
        return Object.keys(this.items);
    }
    
    getType(type) {
        return this.items[type];
    }
}
```

**功能特性**:
- **数据存储**: 存储多种类型的剪贴板数据
- **类型管理**: 管理支持的 MIME 类型
- **选项支持**: 支持展示样式等选项
- **标准接口**: 实现标准 ClipboardItem 接口
- **类型安全**: 提供类型检查和访问方法

**使用示例**:
```javascript
// 创建包含文本和 HTML 的剪贴板项目
const textBlob = new Blob(['Hello World'], { type: 'text/plain' });
const htmlBlob = new Blob(['<b>Hello World</b>'], { type: 'text/html' });

const clipboardItem = new ClipboardItem({
    'text/plain': textBlob,
    'text/html': htmlBlob
});

console.log('支持的类型:', clipboardItem.types);
// 输出: ['text/plain', 'text/html']

// 获取特定类型的数据
const textData = clipboardItem.getType('text/plain');
const htmlData = clipboardItem.getType('text/html');

// 检查展示样式
console.log('展示样式:', clipboardItem.presentationStyle);
```

## 核心函数详解

### blobToStr() - Blob 转字符串
```javascript
function blobToStr(blob) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.addEventListener("load", () => {
            const { result } = reader;
            if (typeof result === "string") {
                resolve(result);
            } else {
                reject("Cannot read Blob as String");
            }
        });
        reader.addEventListener("error", () => {
            reject("Cannot read Blob");
        });
        reader.readAsText(blob);
    });
}
```

**功能特性**:
- **异步转换**: 使用 Promise 进行异步 Blob 读取
- **错误处理**: 完善的错误处理机制
- **类型检查**: 确保结果为字符串类型
- **事件监听**: 监听加载和错误事件
- **内存安全**: 正确处理 FileReader 生命周期

**使用示例**:
```javascript
// 创建文本 Blob
const textBlob = new Blob(['这是一段测试文本'], { type: 'text/plain' });

// 转换为字符串
blobToStr(textBlob)
    .then(text => {
        console.log('转换结果:', text);
        // 输出: 这是一段测试文本
    })
    .catch(error => {
        console.error('转换失败:', error);
    });

// 处理 HTML Blob
const htmlBlob = new Blob(['<h1>标题</h1><p>段落内容</p>'], { type: 'text/html' });

blobToStr(htmlBlob)
    .then(html => {
        console.log('HTML 内容:', html);
        // 可以进一步处理 HTML 内容
    })
    .catch(error => {
        console.error('HTML 转换失败:', error);
    });
```

### stringify() - 剪贴板项目字符串化
```javascript
async function stringify(item) {
    const strItem = {};
    for (const type of item.types) {
        strItem[type] = await blobToStr(item.getType(type));
    }
    return strItem;
}
```

**功能特性**:
- **批量转换**: 转换剪贴板项目的所有类型
- **异步处理**: 使用 async/await 处理异步操作
- **类型保持**: 保持原有的 MIME 类型映射
- **错误传播**: 正确传播转换过程中的错误
- **数据完整性**: 确保所有类型都被正确转换

**使用示例**:
```javascript
// 创建多类型剪贴板项目
const clipboardItem = new ClipboardItem({
    'text/plain': new Blob(['纯文本内容'], { type: 'text/plain' }),
    'text/html': new Blob(['<b>HTML内容</b>'], { type: 'text/html' }),
    'text/uri-list': new Blob(['https://example.com'], { type: 'text/uri-list' })
});

// 字符串化所有类型
stringify(clipboardItem)
    .then(stringifiedItem => {
        console.log('字符串化结果:', stringifiedItem);
        /*
        输出:
        {
            'text/plain': '纯文本内容',
            'text/html': '<b>HTML内容</b>',
            'text/uri-list': 'https://example.com'
        }
        */
        
        // 可以进一步处理每种类型的数据
        Object.entries(stringifiedItem).forEach(([type, content]) => {
            console.log(`类型 ${type}:`, content);
        });
    })
    .catch(error => {
        console.error('字符串化失败:', error);
    });
```

### write() - 剪贴板写入实现
```javascript
async function write(items) {
    if (!items[0].getType("text/plain")) {
        throw new Error(
            `Calling clipboard.write() without a "text/plain" type may result in an empty clipboard on some platforms.`
        );
    }
    
    const strItem = await stringify(items[0]);
    
    // 创建隐藏的 DOM 元素
    const stubContainer = document.createElement("div");
    const shadowContainer = stubContainer.attachShadow({ mode: "open" });
    const stub = document.createElement("span");
    stub.innerText = strItem["text/plain"];
    shadowContainer.appendChild(stub);
    document.body.appendChild(stubContainer);
    
    // 选择文本
    const selection = document.getSelection();
    const range = document.createRange();
    range.selectNodeContents(stub);
    selection.removeAllRanges();
    selection.addRange(range);
    
    // 设置复制事件处理器
    const onCopy = (ev) => {
        for (const type in strItem) {
            ev.clipboardData.setData(type, strItem[type]);
        }
        ev.preventDefault();
    };
    
    document.addEventListener("copy", onCopy);
    let result;
    try {
        result = document.execCommand("copy");
    } finally {
        document.removeEventListener("copy", onCopy);
    }
    
    // 清理
    selection.removeAllRanges();
    document.body.removeChild(stubContainer);
    
    return result;
}
```

**功能特性**:
- **兼容性检查**: 确保包含 text/plain 类型
- **Shadow DOM**: 使用 Shadow DOM 避免样式干扰
- **多类型支持**: 支持多种 MIME 类型的数据
- **事件处理**: 使用传统的 copy 事件机制
- **资源清理**: 正确清理临时创建的 DOM 元素

**使用示例**:
```javascript
// 基本文本复制
async function copyText(text) {
    const textBlob = new Blob([text], { type: 'text/plain' });
    const clipboardItem = new ClipboardItem({
        'text/plain': textBlob
    });
    
    try {
        const result = await navigator.clipboard.write([clipboardItem]);
        console.log('复制成功:', result);
        return true;
    } catch (error) {
        console.error('复制失败:', error);
        return false;
    }
}

// 复制富文本内容
async function copyRichText(plainText, htmlText) {
    const textBlob = new Blob([plainText], { type: 'text/plain' });
    const htmlBlob = new Blob([htmlText], { type: 'text/html' });
    
    const clipboardItem = new ClipboardItem({
        'text/plain': textBlob,
        'text/html': htmlBlob
    });
    
    try {
        await navigator.clipboard.write([clipboardItem]);
        console.log('富文本复制成功');
        return true;
    } catch (error) {
        console.error('富文本复制失败:', error);
        return false;
    }
}

// 复制链接
async function copyLink(url, title) {
    const plainText = `${title}: ${url}`;
    const htmlText = `<a href="${url}">${title}</a>`;
    const uriList = url;
    
    const clipboardItem = new ClipboardItem({
        'text/plain': new Blob([plainText], { type: 'text/plain' }),
        'text/html': new Blob([htmlText], { type: 'text/html' }),
        'text/uri-list': new Blob([uriList], { type: 'text/uri-list' })
    });
    
    try {
        await navigator.clipboard.write([clipboardItem]);
        console.log('链接复制成功');
        return true;
    } catch (error) {
        console.error('链接复制失败:', error);
        return false;
    }
}

// 使用示例
copyText('Hello, World!');
copyRichText('Hello, World!', '<b>Hello, World!</b>');
copyLink('https://www.odoo.com', 'Odoo Official Website');
```

## 高级应用模式

### 1. 剪贴板管理器
```javascript
class ClipboardManager {
    constructor() {
        this.history = [];
        this.maxHistorySize = 10;
        this.listeners = new Set();
    }
    
    // 复制文本到剪贴板
    async copyText(text, options = {}) {
        const { addToHistory = true, notify = true } = options;
        
        try {
            const textBlob = new Blob([text], { type: 'text/plain' });
            const clipboardItem = new ClipboardItem({
                'text/plain': textBlob
            });
            
            const result = await navigator.clipboard.write([clipboardItem]);
            
            if (addToHistory) {
                this.addToHistory({
                    type: 'text',
                    content: text,
                    timestamp: new Date()
                });
            }
            
            if (notify) {
                this.notifyListeners('copy', { type: 'text', content: text });
            }
            
            return result;
        } catch (error) {
            console.error('复制文本失败:', error);
            throw error;
        }
    }
    
    // 复制 HTML 内容
    async copyHTML(plainText, htmlText, options = {}) {
        const { addToHistory = true, notify = true } = options;
        
        try {
            const textBlob = new Blob([plainText], { type: 'text/plain' });
            const htmlBlob = new Blob([htmlText], { type: 'text/html' });
            
            const clipboardItem = new ClipboardItem({
                'text/plain': textBlob,
                'text/html': htmlBlob
            });
            
            const result = await navigator.clipboard.write([clipboardItem]);
            
            if (addToHistory) {
                this.addToHistory({
                    type: 'html',
                    plainText,
                    htmlText,
                    timestamp: new Date()
                });
            }
            
            if (notify) {
                this.notifyListeners('copy', { 
                    type: 'html', 
                    plainText, 
                    htmlText 
                });
            }
            
            return result;
        } catch (error) {
            console.error('复制 HTML 失败:', error);
            throw error;
        }
    }
    
    // 复制表格数据
    async copyTable(tableData, options = {}) {
        const { headers = [], addToHistory = true, notify = true } = options;
        
        try {
            // 生成纯文本格式（制表符分隔）
            const plainText = this.tableToPlainText(tableData, headers);
            
            // 生成 HTML 表格
            const htmlText = this.tableToHTML(tableData, headers);
            
            // 生成 CSV 格式
            const csvText = this.tableToCSV(tableData, headers);
            
            const clipboardItem = new ClipboardItem({
                'text/plain': new Blob([plainText], { type: 'text/plain' }),
                'text/html': new Blob([htmlText], { type: 'text/html' }),
                'text/csv': new Blob([csvText], { type: 'text/csv' })
            });
            
            const result = await navigator.clipboard.write([clipboardItem]);
            
            if (addToHistory) {
                this.addToHistory({
                    type: 'table',
                    data: tableData,
                    headers,
                    timestamp: new Date()
                });
            }
            
            if (notify) {
                this.notifyListeners('copy', { 
                    type: 'table', 
                    data: tableData, 
                    headers 
                });
            }
            
            return result;
        } catch (error) {
            console.error('复制表格失败:', error);
            throw error;
        }
    }
    
    // 表格转纯文本
    tableToPlainText(data, headers) {
        const rows = [];
        
        if (headers.length > 0) {
            rows.push(headers.join('\t'));
        }
        
        data.forEach(row => {
            rows.push(row.join('\t'));
        });
        
        return rows.join('\n');
    }
    
    // 表格转 HTML
    tableToHTML(data, headers) {
        let html = '<table>';
        
        if (headers.length > 0) {
            html += '<thead><tr>';
            headers.forEach(header => {
                html += `<th>${this.escapeHTML(header)}</th>`;
            });
            html += '</tr></thead>';
        }
        
        html += '<tbody>';
        data.forEach(row => {
            html += '<tr>';
            row.forEach(cell => {
                html += `<td>${this.escapeHTML(String(cell))}</td>`;
            });
            html += '</tr>';
        });
        html += '</tbody></table>';
        
        return html;
    }
    
    // 表格转 CSV
    tableToCSV(data, headers) {
        const rows = [];
        
        if (headers.length > 0) {
            rows.push(headers.map(h => this.escapeCSV(h)).join(','));
        }
        
        data.forEach(row => {
            rows.push(row.map(cell => this.escapeCSV(String(cell))).join(','));
        });
        
        return rows.join('\n');
    }
    
    // HTML 转义
    escapeHTML(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    // CSV 转义
    escapeCSV(text) {
        if (text.includes(',') || text.includes('"') || text.includes('\n')) {
            return `"${text.replace(/"/g, '""')}"`;
        }
        return text;
    }
    
    // 添加到历史记录
    addToHistory(item) {
        this.history.unshift(item);
        if (this.history.length > this.maxHistorySize) {
            this.history = this.history.slice(0, this.maxHistorySize);
        }
    }
    
    // 获取历史记录
    getHistory() {
        return [...this.history];
    }
    
    // 清空历史记录
    clearHistory() {
        this.history = [];
        this.notifyListeners('historyCleared');
    }
    
    // 添加事件监听器
    addEventListener(listener) {
        this.listeners.add(listener);
    }
    
    // 移除事件监听器
    removeEventListener(listener) {
        this.listeners.delete(listener);
    }
    
    // 通知监听器
    notifyListeners(event, data) {
        this.listeners.forEach(listener => {
            try {
                listener(event, data);
            } catch (error) {
                console.error('剪贴板事件监听器错误:', error);
            }
        });
    }
}

// 使用示例
const clipboardManager = new ClipboardManager();

// 监听剪贴板事件
clipboardManager.addEventListener((event, data) => {
    console.log('剪贴板事件:', event, data);
});

// 复制文本
clipboardManager.copyText('Hello, World!');

// 复制 HTML
clipboardManager.copyHTML(
    'Hello, World!',
    '<b>Hello, <em>World!</em></b>'
);

// 复制表格
const tableData = [
    ['张三', '25', '工程师'],
    ['李四', '30', '设计师'],
    ['王五', '28', '产品经理']
];
const headers = ['姓名', '年龄', '职位'];

clipboardManager.copyTable(tableData, { headers });

// 查看历史记录
console.log('剪贴板历史:', clipboardManager.getHistory());
```

### 2. 智能剪贴板组件
```javascript
class SmartClipboard extends Component {
    static template = xml`
        <div class="smart-clipboard">
            <div class="clipboard-controls">
                <button t-on-click="copySelection"
                        t-att-disabled="!hasSelection"
                        class="btn btn-primary">
                    <i class="fa fa-copy" /> 复制选中内容
                </button>

                <button t-on-click="copyAsHTML"
                        t-att-disabled="!hasSelection"
                        class="btn btn-secondary">
                    <i class="fa fa-code" /> 复制为 HTML
                </button>

                <button t-on-click="copyAsMarkdown"
                        t-att-disabled="!hasSelection"
                        class="btn btn-info">
                    <i class="fa fa-markdown" /> 复制为 Markdown
                </button>

                <button t-on-click="showHistory"
                        class="btn btn-outline-secondary">
                    <i class="fa fa-history" /> 历史记录 (<span t-esc="historyCount" />)
                </button>
            </div>

            <div t-if="showHistoryPanel" class="clipboard-history">
                <div class="history-header">
                    <h5>剪贴板历史</h5>
                    <button t-on-click="clearHistory" class="btn btn-sm btn-danger">
                        清空历史
                    </button>
                </div>

                <div class="history-list">
                    <div t-foreach="clipboardHistory" t-as="item" t-key="item_index"
                         class="history-item"
                         t-on-click="() => this.restoreFromHistory(item)">
                        <div class="item-type">
                            <i t-att-class="getTypeIcon(item.type)" />
                            <span t-esc="item.type" />
                        </div>
                        <div class="item-preview" t-esc="getPreview(item)" />
                        <div class="item-time" t-esc="formatTime(item.timestamp)" />
                    </div>
                </div>
            </div>

            <div t-if="notification" class="clipboard-notification"
                 t-att-class="'alert alert-' + notification.type">
                <span t-esc="notification.message" />
            </div>
        </div>
    `;

    setup() {
        this.clipboardManager = new ClipboardManager();
        this.hasSelection = useState(false);
        this.showHistoryPanel = useState(false);
        this.clipboardHistory = useState([]);
        this.notification = useState(null);

        // 监听选择变化
        this.selectionObserver = new MutationObserver(() => {
            this.updateSelectionState();
        });

        onMounted(() => {
            document.addEventListener('selectionchange', this.updateSelectionState.bind(this));
            this.selectionObserver.observe(document.body, {
                childList: true,
                subtree: true
            });

            // 监听剪贴板事件
            this.clipboardManager.addEventListener(this.onClipboardEvent.bind(this));
            this.updateHistory();
        });

        onWillUnmount(() => {
            document.removeEventListener('selectionchange', this.updateSelectionState.bind(this));
            this.selectionObserver.disconnect();
        });
    }

    updateSelectionState() {
        const selection = window.getSelection();
        this.hasSelection = selection && selection.toString().trim().length > 0;
    }

    async copySelection() {
        const selection = window.getSelection();
        if (!selection || selection.toString().trim().length === 0) {
            this.showNotification('没有选中的内容', 'warning');
            return;
        }

        const selectedText = selection.toString();

        try {
            await this.clipboardManager.copyText(selectedText);
            this.showNotification('已复制到剪贴板', 'success');
        } catch (error) {
            this.showNotification('复制失败', 'danger');
        }
    }

    async copyAsHTML() {
        const selection = window.getSelection();
        if (!selection || selection.rangeCount === 0) {
            this.showNotification('没有选中的内容', 'warning');
            return;
        }

        const range = selection.getRangeAt(0);
        const container = document.createElement('div');
        container.appendChild(range.cloneContents());

        const htmlContent = container.innerHTML;
        const textContent = container.textContent || container.innerText;

        try {
            await this.clipboardManager.copyHTML(textContent, htmlContent);
            this.showNotification('已复制 HTML 到剪贴板', 'success');
        } catch (error) {
            this.showNotification('复制 HTML 失败', 'danger');
        }
    }

    async copyAsMarkdown() {
        const selection = window.getSelection();
        if (!selection || selection.rangeCount === 0) {
            this.showNotification('没有选中的内容', 'warning');
            return;
        }

        const range = selection.getRangeAt(0);
        const container = document.createElement('div');
        container.appendChild(range.cloneContents());

        const markdown = this.htmlToMarkdown(container);

        try {
            await this.clipboardManager.copyText(markdown);
            this.showNotification('已复制 Markdown 到剪贴板', 'success');
        } catch (error) {
            this.showNotification('复制 Markdown 失败', 'danger');
        }
    }

    htmlToMarkdown(element) {
        let markdown = '';

        for (const node of element.childNodes) {
            if (node.nodeType === Node.TEXT_NODE) {
                markdown += node.textContent;
            } else if (node.nodeType === Node.ELEMENT_NODE) {
                const tagName = node.tagName.toLowerCase();
                const content = this.htmlToMarkdown(node);

                switch (tagName) {
                    case 'h1':
                        markdown += `# ${content}\n\n`;
                        break;
                    case 'h2':
                        markdown += `## ${content}\n\n`;
                        break;
                    case 'h3':
                        markdown += `### ${content}\n\n`;
                        break;
                    case 'strong':
                    case 'b':
                        markdown += `**${content}**`;
                        break;
                    case 'em':
                    case 'i':
                        markdown += `*${content}*`;
                        break;
                    case 'code':
                        markdown += `\`${content}\``;
                        break;
                    case 'a':
                        const href = node.getAttribute('href');
                        markdown += `[${content}](${href})`;
                        break;
                    case 'p':
                        markdown += `${content}\n\n`;
                        break;
                    case 'br':
                        markdown += '\n';
                        break;
                    default:
                        markdown += content;
                }
            }
        }

        return markdown;
    }

    showHistory() {
        this.showHistoryPanel = !this.showHistoryPanel;
        if (this.showHistoryPanel) {
            this.updateHistory();
        }
    }

    updateHistory() {
        this.clipboardHistory = this.clipboardManager.getHistory();
    }

    async restoreFromHistory(item) {
        try {
            switch (item.type) {
                case 'text':
                    await this.clipboardManager.copyText(item.content, { addToHistory: false });
                    break;
                case 'html':
                    await this.clipboardManager.copyHTML(item.plainText, item.htmlText, { addToHistory: false });
                    break;
                case 'table':
                    await this.clipboardManager.copyTable(item.data, {
                        headers: item.headers,
                        addToHistory: false
                    });
                    break;
            }
            this.showNotification('已恢复到剪贴板', 'success');
        } catch (error) {
            this.showNotification('恢复失败', 'danger');
        }
    }

    clearHistory() {
        this.clipboardManager.clearHistory();
        this.updateHistory();
        this.showNotification('历史记录已清空', 'info');
    }

    onClipboardEvent(event, data) {
        if (event === 'copy') {
            this.updateHistory();
        }
    }

    getTypeIcon(type) {
        const icons = {
            text: 'fa fa-font',
            html: 'fa fa-code',
            table: 'fa fa-table'
        };
        return icons[type] || 'fa fa-file';
    }

    getPreview(item) {
        switch (item.type) {
            case 'text':
                return item.content.substring(0, 50) + (item.content.length > 50 ? '...' : '');
            case 'html':
                return item.plainText.substring(0, 50) + (item.plainText.length > 50 ? '...' : '');
            case 'table':
                return `表格 (${item.data.length} 行)`;
            default:
                return '未知类型';
        }
    }

    formatTime(timestamp) {
        const now = new Date();
        const diff = now - timestamp;

        if (diff < 60000) { // 1分钟内
            return '刚刚';
        } else if (diff < 3600000) { // 1小时内
            return `${Math.floor(diff / 60000)} 分钟前`;
        } else if (diff < 86400000) { // 1天内
            return `${Math.floor(diff / 3600000)} 小时前`;
        } else {
            return timestamp.toLocaleDateString();
        }
    }

    showNotification(message, type) {
        this.notification = { message, type };
        setTimeout(() => {
            this.notification = null;
        }, 3000);
    }

    get historyCount() {
        return this.clipboardHistory.length;
    }
}
```

### 3. 剪贴板数据验证器
```javascript
class ClipboardValidator {
    constructor() {
        this.validators = new Map();
        this.setupDefaultValidators();
    }

    setupDefaultValidators() {
        // 文本验证器
        this.addValidator('text/plain', (content) => {
            if (typeof content !== 'string') {
                throw new Error('文本内容必须是字符串');
            }
            if (content.length > 1000000) { // 1MB 限制
                throw new Error('文本内容过大');
            }
            return true;
        });

        // HTML 验证器
        this.addValidator('text/html', (content) => {
            if (typeof content !== 'string') {
                throw new Error('HTML 内容必须是字符串');
            }

            // 检查危险标签
            const dangerousTags = ['script', 'iframe', 'object', 'embed'];
            const parser = new DOMParser();
            const doc = parser.parseFromString(content, 'text/html');

            for (const tag of dangerousTags) {
                if (doc.querySelector(tag)) {
                    throw new Error(`HTML 内容包含危险标签: ${tag}`);
                }
            }

            return true;
        });

        // URL 验证器
        this.addValidator('text/uri-list', (content) => {
            const urls = content.split('\n').filter(line => line.trim() && !line.startsWith('#'));

            for (const url of urls) {
                try {
                    new URL(url);
                } catch (error) {
                    throw new Error(`无效的 URL: ${url}`);
                }
            }

            return true;
        });

        // CSV 验证器
        this.addValidator('text/csv', (content) => {
            const lines = content.split('\n');
            if (lines.length === 0) {
                throw new Error('CSV 内容为空');
            }

            const firstLineColumns = lines[0].split(',').length;
            for (let i = 1; i < lines.length; i++) {
                const columns = lines[i].split(',').length;
                if (columns !== firstLineColumns) {
                    throw new Error(`CSV 第 ${i + 1} 行列数不匹配`);
                }
            }

            return true;
        });
    }

    addValidator(mimeType, validator) {
        this.validators.set(mimeType, validator);
    }

    removeValidator(mimeType) {
        this.validators.delete(mimeType);
    }

    validate(mimeType, content) {
        const validator = this.validators.get(mimeType);
        if (validator) {
            return validator(content);
        }
        return true; // 没有验证器时默认通过
    }

    validateClipboardItem(clipboardItem) {
        const results = {};

        for (const type of clipboardItem.types) {
            try {
                const content = clipboardItem.getType(type);
                this.validate(type, content);
                results[type] = { valid: true };
            } catch (error) {
                results[type] = { valid: false, error: error.message };
            }
        }

        return results;
    }
}

// 使用示例
const validator = new ClipboardValidator();

// 添加自定义验证器
validator.addValidator('application/json', (content) => {
    try {
        JSON.parse(content);
        return true;
    } catch (error) {
        throw new Error('无效的 JSON 格式');
    }
});

// 验证剪贴板项目
const clipboardItem = new ClipboardItem({
    'text/plain': new Blob(['Hello World'], { type: 'text/plain' }),
    'text/html': new Blob(['<p>Hello World</p>'], { type: 'text/html' })
});

const validationResults = validator.validateClipboardItem(clipboardItem);
console.log('验证结果:', validationResults);
```

## Polyfill 检测和应用

### 条件性 Polyfill 应用
```javascript
/**
 * 只对部分实现剪贴板 API 的浏览器进行 Polyfill
 * 主要针对 Firefox 等需要功能标志的浏览器
 */
if (window.navigator.clipboard) {
    // 检测并补丁 clipboard.write 方法
    if (!window.navigator.clipboard.write) {
        window.navigator.clipboard.write = write.bind(window);
        console.log('已应用 clipboard.write Polyfill');
    }

    // 检测并补丁 ClipboardItem 类
    if (!window.ClipboardItem) {
        window.ClipboardItem = ClipboardItemImpl;
        console.log('已应用 ClipboardItem Polyfill');
    }
} else {
    console.warn('浏览器不支持剪贴板 API');
}
```

**检测策略**:
- **渐进增强**: 只在需要时应用 Polyfill
- **功能检测**: 检测具体功能而非浏览器类型
- **向后兼容**: 保持与现有代码的兼容性
- **性能考虑**: 避免不必要的 Polyfill 开销

## 最佳实践

### 1. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
async function safeClipboardWrite(items) {
    try {
        // 检查浏览器支持
        if (!navigator.clipboard || !navigator.clipboard.write) {
            throw new Error('浏览器不支持剪贴板 API');
        }

        // 验证输入
        if (!Array.isArray(items) || items.length === 0) {
            throw new Error('无效的剪贴板项目');
        }

        // 执行写入
        const result = await navigator.clipboard.write(items);
        return { success: true, result };

    } catch (error) {
        console.error('剪贴板写入失败:', error);
        return { success: false, error: error.message };
    }
}
```

### 2. 性能优化
```javascript
// ✅ 推荐：延迟创建和缓存
class OptimizedClipboard {
    constructor() {
        this.blobCache = new Map();
        this.maxCacheSize = 50;
    }

    createBlob(content, type) {
        const cacheKey = `${type}:${content}`;

        if (this.blobCache.has(cacheKey)) {
            return this.blobCache.get(cacheKey);
        }

        const blob = new Blob([content], { type });

        // 缓存管理
        if (this.blobCache.size >= this.maxCacheSize) {
            const firstKey = this.blobCache.keys().next().value;
            this.blobCache.delete(firstKey);
        }

        this.blobCache.set(cacheKey, blob);
        return blob;
    }
}
```

### 3. 安全考虑
```javascript
// ✅ 推荐：内容安全检查
function sanitizeHTML(html) {
    const allowedTags = ['p', 'br', 'strong', 'em', 'u', 'a', 'ul', 'ol', 'li'];
    const allowedAttributes = ['href', 'title'];

    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    // 移除不允许的标签和属性
    const walker = document.createTreeWalker(
        doc.body,
        NodeFilter.SHOW_ELEMENT,
        null,
        false
    );

    const elementsToRemove = [];
    let node;

    while (node = walker.nextNode()) {
        if (!allowedTags.includes(node.tagName.toLowerCase())) {
            elementsToRemove.push(node);
        } else {
            // 清理属性
            const attributes = Array.from(node.attributes);
            attributes.forEach(attr => {
                if (!allowedAttributes.includes(attr.name)) {
                    node.removeAttribute(attr.name);
                }
            });
        }
    }

    elementsToRemove.forEach(el => el.remove());

    return doc.body.innerHTML;
}
```

## 总结

Odoo 剪贴板 Polyfill 模块提供了重要的浏览器兼容性支持：

**核心优势**:
- **兼容性**: 解决现代剪贴板 API 的浏览器兼容性问题
- **标准化**: 提供统一的剪贴板操作接口
- **多格式**: 支持多种 MIME 类型的数据
- **渐进增强**: 只在需要时应用 Polyfill
- **性能优化**: 使用高效的实现策略

**适用场景**:
- 跨浏览器的剪贴板操作
- 富文本内容的复制
- 表格数据的导出
- 多格式数据的处理
- 企业级应用的兼容性保障

**设计优势**:
- 非侵入式实现
- 标准 API 兼容
- 错误处理完善
- 资源管理正确

这个 Polyfill 模块为 Odoo Web 客户端提供了可靠的剪贴板功能支持，确保在各种浏览器环境中都能正常工作。

# Odoo 示例服务器 (Sample Server) 学习资料

## 文件概述

**文件路径**: `output/@web/model/sample_server.js`  
**原始路径**: `/web/static/src/model/sample_server.js`  
**模块类型**: 核心模型模块 - 示例数据服务器  
**代码行数**: 847 行  
**依赖关系**: 
- `@web/core/l10n/dates` - 日期本地化 (deserializeDate, serializeDate等)
- `@web/core/orm_service` - ORM服务 (ORM)
- `@web/core/registry` - 注册表 (registry)
- `@web/core/utils/arrays` - 数组工具 (cartesian, sortBy)
- `@web/model/relational_model/utils` - 关系模型工具 (parseServerValue)

## 模块功能

示例服务器模块是 Odoo Web 客户端的示例数据生成系统。该模块提供了：
- 模拟 Odoo 服务器的 RPC 调用
- 生成符合字段类型的示例数据
- 支持搜索、分组、排序等操作
- 提供真实的数据交互体验
- 无需后端数据的前端开发支持
- 演示和培训环境的数据支持

这个模块使得开发者可以在没有真实数据的情况下开发和测试前端功能，为用户提供更好的空状态体验。

## 示例服务器架构

### 核心组件
```
SampleServer
├── 数据生成器
│   ├── 字段类型处理器
│   ├── 示例数据模板
│   └── 随机数据生成
├── RPC 模拟器
│   ├── search_read 方法
│   ├── read_group 方法
│   ├── web_read 方法
│   └── 其他 ORM 方法
└── 数据管理器
    ├── 记录集管理
    ├── 分组数据管理
    └── 搜索过滤
```

### 数据生成策略
- **字段类型映射**: 根据字段类型生成相应的示例数据
- **关系字段**: 生成符合关系约束的数据
- **数据一致性**: 确保生成的数据在逻辑上一致
- **本地化支持**: 支持多语言和地区的示例数据
- **可配置性**: 支持自定义示例数据模板

### 性能优化
- **延迟生成**: 按需生成数据，避免内存浪费
- **缓存机制**: 缓存生成的数据，提高响应速度
- **分页支持**: 支持大数据集的分页处理
- **索引优化**: 优化搜索和过滤性能

## 核心类详解

### SampleServer - 示例服务器类
```javascript
class SampleServer {
    constructor(modelName, fields) {
        this.modelName = modelName;
        this.fields = fields;
        this.existingGroups = null;
        this.records = new Map();
        this.nextId = 1;
    }
    
    mockRpc({ method, model, args, ...kwargs }) {
        // 模拟 RPC 调用
        switch (method) {
            case 'search_read':
                return this.mockSearchRead(args, kwargs);
            case 'read_group':
                return this.mockReadGroup(args, kwargs);
            case 'web_read':
                return this.mockWebRead(args, kwargs);
            case 'fields_get':
                return this.mockFieldsGet(args, kwargs);
            default:
                throw new UnimplementedRouteError(`Method ${method} not implemented`);
        }
    }
}
```

**功能特性**:
- **RPC 模拟**: 完整模拟 Odoo 服务器的 RPC 接口
- **方法路由**: 根据方法名路由到相应的处理函数
- **参数处理**: 正确处理 RPC 调用的参数和关键字参数
- **错误处理**: 提供未实现方法的错误处理
- **状态管理**: 维护服务器状态和数据

**使用示例**:
```javascript
// 创建示例服务器
const fields = {
    id: { type: 'integer', string: 'ID' },
    name: { type: 'char', string: 'Name' },
    email: { type: 'char', string: 'Email' },
    phone: { type: 'char', string: 'Phone' },
    company_id: { type: 'many2one', relation: 'res.company', string: 'Company' },
    tag_ids: { type: 'many2many', relation: 'res.partner.tag', string: 'Tags' },
    create_date: { type: 'datetime', string: 'Created on' },
    active: { type: 'boolean', string: 'Active' },
    customer_rank: { type: 'integer', string: 'Customer Rank' }
};

const sampleServer = new SampleServer('res.partner', fields);

// 模拟搜索读取
const searchResult = await sampleServer.mockRpc({
    method: 'search_read',
    model: 'res.partner',
    args: [
        [['active', '=', true]], // domain
        ['name', 'email', 'phone', 'company_id'] // fields
    ],
    kwargs: {
        limit: 10,
        offset: 0,
        order: 'name ASC'
    }
});

console.log('搜索结果:', searchResult);

// 模拟分组读取
const groupResult = await sampleServer.mockRpc({
    method: 'read_group',
    model: 'res.partner',
    args: [
        [['active', '=', true]], // domain
        ['customer_rank'], // fields
        ['company_id'] // groupby
    ],
    kwargs: {
        limit: 5
    }
});

console.log('分组结果:', groupResult);
```

### buildSampleORM() - 示例ORM构建器
```javascript
function buildSampleORM(resModel, fields, user) {
    const sampleServer = new SampleServer(resModel, fields);
    const fakeRPC = async (_, params) => {
        const { args, kwargs, method, model } = params;
        const { groupby: groupBy } = kwargs;
        return sampleServer.mockRpc({ method, model, args, ...kwargs, groupBy });
    };
    const sampleORM = new ORM(user);
    sampleORM.rpc = fakeRPC;
    sampleORM.isSample = true;
    sampleORM.setGroups = (groups) => sampleServer.setExistingGroups(groups);
    return sampleORM;
}
```

**功能特性**:
- **ORM 包装**: 将示例服务器包装成标准的 ORM 接口
- **RPC 替换**: 用示例服务器替换真实的 RPC 调用
- **标识标记**: 通过 isSample 标记区分示例和真实 ORM
- **分组支持**: 支持设置现有分组数据
- **用户集成**: 集成用户信息到示例 ORM

**使用示例**:
```javascript
// 在组件中使用示例 ORM
class ProductListWithSampleData extends Component {
    static template = xml`
        <div class="product-list">
            <div t-if="orm.isSample" class="sample-banner">
                <i class="fa fa-info-circle" />
                当前显示的是示例数据
            </div>
            
            <div class="product-grid">
                <div t-foreach="products" t-as="product" t-key="product.id"
                     class="product-card">
                    <h4 t-esc="product.name" />
                    <p t-esc="product.description" />
                    <span class="price" t-esc="formatCurrency(product.list_price)" />
                    <div class="tags">
                        <span t-foreach="product.tag_ids" t-as="tag" t-key="tag.id"
                              class="badge badge-secondary" t-esc="tag.display_name" />
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setup() {
        this.products = useState([]);
        
        // 根据是否有真实数据决定使用示例 ORM
        const fields = {
            id: { type: 'integer' },
            name: { type: 'char' },
            description: { type: 'text' },
            list_price: { type: 'float' },
            tag_ids: { type: 'many2many', relation: 'product.tag' },
            active: { type: 'boolean' }
        };
        
        if (this.props.useSampleData) {
            this.orm = buildSampleORM('product.product', fields, this.env.services.user);
        } else {
            this.orm = this.env.services.orm;
        }
        
        onWillStart(async () => {
            await this.loadProducts();
        });
    }
    
    async loadProducts() {
        try {
            const products = await this.orm.searchRead(
                'product.product',
                [['active', '=', true]],
                ['name', 'description', 'list_price', 'tag_ids'],
                {
                    limit: 20,
                    order: 'name ASC'
                }
            );
            
            this.products = products;
            
        } catch (error) {
            console.error('加载产品失败:', error);
            
            // 如果真实数据加载失败，回退到示例数据
            if (!this.orm.isSample) {
                this.orm = buildSampleORM('product.product', fields, this.env.services.user);
                await this.loadProducts();
            }
        }
    }
    
    formatCurrency(amount) {
        return new Intl.NumberFormat('zh-CN', {
            style: 'currency',
            currency: 'CNY'
        }).format(amount);
    }
}

// 示例数据生成器
class CustomSampleDataGenerator {
    constructor() {
        this.productNames = [
            'iPhone 15 Pro', 'MacBook Air', 'iPad Pro', 'Apple Watch',
            'Samsung Galaxy S24', 'Dell XPS 13', 'Surface Pro 9',
            'ThinkPad X1 Carbon', 'HP Spectre x360', 'Asus ZenBook'
        ];
        
        this.descriptions = [
            '高性能专业设备，适合商务和创意工作',
            '轻薄便携，续航持久，性能卓越',
            '创新设计，先进技术，用户体验极佳',
            '多功能一体化解决方案',
            '企业级安全，可靠稳定'
        ];
        
        this.tags = [
            { id: 1, display_name: '热销' },
            { id: 2, display_name: '新品' },
            { id: 3, display_name: '推荐' },
            { id: 4, display_name: '限时优惠' },
            { id: 5, display_name: '企业版' }
        ];
    }
    
    generateProduct(id) {
        const nameIndex = (id - 1) % this.productNames.length;
        const descIndex = (id - 1) % this.descriptions.length;
        
        return {
            id: id,
            name: this.productNames[nameIndex],
            description: this.descriptions[descIndex],
            list_price: Math.round((Math.random() * 50000 + 1000) * 100) / 100,
            tag_ids: this.getRandomTags(),
            active: true,
            create_date: this.getRandomDate(),
            write_date: this.getRandomDate()
        };
    }
    
    getRandomTags() {
        const tagCount = Math.floor(Math.random() * 3) + 1;
        const selectedTags = [];
        const availableTags = [...this.tags];
        
        for (let i = 0; i < tagCount && availableTags.length > 0; i++) {
            const randomIndex = Math.floor(Math.random() * availableTags.length);
            selectedTags.push(availableTags.splice(randomIndex, 1)[0]);
        }
        
        return selectedTags;
    }
    
    getRandomDate() {
        const now = new Date();
        const pastDays = Math.floor(Math.random() * 365);
        const randomDate = new Date(now.getTime() - pastDays * 24 * 60 * 60 * 1000);
        return randomDate.toISOString();
    }
}

// 扩展示例服务器
class ExtendedSampleServer extends SampleServer {
    constructor(modelName, fields) {
        super(modelName, fields);
        this.dataGenerator = new CustomSampleDataGenerator();
    }
    
    generateRecord(id) {
        if (this.modelName === 'product.product') {
            return this.dataGenerator.generateProduct(id);
        }
        return super.generateRecord(id);
    }
    
    mockSearchRead(args, kwargs) {
        // 自定义搜索逻辑
        const [domain, fields] = args;
        const { limit = 80, offset = 0, order } = kwargs;
        
        const records = [];
        const startId = offset + 1;
        const endId = startId + limit - 1;
        
        for (let id = startId; id <= endId; id++) {
            const record = this.generateRecord(id);
            if (this.matchesDomain(record, domain)) {
                records.push(this.pickFields(record, fields));
            }
        }
        
        if (order) {
            this.sortRecords(records, order);
        }
        
        return records;
    }
    
    matchesDomain(record, domain) {
        // 简化的域匹配逻辑
        for (const condition of domain) {
            if (Array.isArray(condition) && condition.length === 3) {
                const [field, operator, value] = condition;
                if (!this.evaluateCondition(record[field], operator, value)) {
                    return false;
                }
            }
        }
        return true;
    }
    
    evaluateCondition(fieldValue, operator, value) {
        switch (operator) {
            case '=':
                return fieldValue === value;
            case '!=':
                return fieldValue !== value;
            case '>':
                return fieldValue > value;
            case '>=':
                return fieldValue >= value;
            case '<':
                return fieldValue < value;
            case '<=':
                return fieldValue <= value;
            case 'like':
            case 'ilike':
                return String(fieldValue).toLowerCase().includes(String(value).toLowerCase());
            case 'in':
                return Array.isArray(value) && value.includes(fieldValue);
            case 'not in':
                return Array.isArray(value) && !value.includes(fieldValue);
            default:
                return true;
        }
    }
    
    pickFields(record, fields) {
        if (!fields || fields.length === 0) {
            return record;
        }
        
        const result = {};
        for (const field of fields) {
            if (field in record) {
                result[field] = record[field];
            }
        }
        return result;
    }
    
    sortRecords(records, order) {
        const orderParts = order.split(',').map(part => {
            const trimmed = part.trim();
            const isDesc = trimmed.toLowerCase().endsWith(' desc');
            const field = isDesc ? trimmed.slice(0, -5).trim() : trimmed.replace(/ asc$/i, '').trim();
            return { field, desc: isDesc };
        });
        
        records.sort((a, b) => {
            for (const { field, desc } of orderParts) {
                const aVal = a[field];
                const bVal = b[field];
                
                let comparison = 0;
                if (aVal < bVal) comparison = -1;
                else if (aVal > bVal) comparison = 1;
                
                if (comparison !== 0) {
                    return desc ? -comparison : comparison;
                }
            }
            return 0;
        });
    }
}
```

## 示例数据常量

### 数据集大小配置
```javascript
SampleServer.MAIN_RECORDSET_SIZE = 16;     // 主记录集大小
SampleServer.SUB_RECORDSET_SIZE = 5;       // 子记录集大小
SampleServer.SEARCH_READ_LIMIT = 10;       // 搜索读取限制
```

### 数据范围配置
```javascript
SampleServer.MAX_FLOAT = 100;              // 浮点数最大值
SampleServer.MAX_INTEGER = 50;             // 整数最大值
SampleServer.MAX_COLOR_INT = 7;            // 颜色整数最大值
SampleServer.MAX_MONETARY = 100000;        // 货币最大值
SampleServer.DATE_DELTA = 24 * 60;         // 日期范围（小时）-> 60天
SampleServer.FLOAT_PRECISION = 2;          // 浮点数精度
```

### 示例数据模板
```javascript
SampleServer.SAMPLE_COUNTRIES = [
    "Belgium", "France", "Portugal", "Singapore", "Australia"
];

SampleServer.SAMPLE_PEOPLE = [
    "John Miller", "Henry Campbell", "Carrie Helle",
    "Wendi Baltz", "Thomas Passot"
];

SampleServer.SAMPLE_TEXTS = [
    "Laoreet id", "Volutpat blandit", "Integer vitae",
    "Viverra nam", "In massa"
];

SampleServer.PEOPLE_MODELS = [
    "res.users", "res.partner", "hr.employee",
    "mail.followers", "mailing.contact"
];
```

## 工具函数详解

### getSampleFromId() - ID映射示例数据
```javascript
function getSampleFromId(id, sampleTexts) {
    return sampleTexts[(id - 1) % sampleTexts.length];
}
```

**功能特性**:
- **循环映射**: 将ID映射到示例文本数组的循环索引
- **一致性**: 相同ID总是返回相同的示例数据
- **简单高效**: 简单的模运算实现
- **可预测**: 数据生成结果可预测和重现

### serializeGroupDateValue() - 分组日期序列化
```javascript
function serializeGroupDateValue(range, field) {
    if (!range) {
        return false;
    }
    let dateValue = parseServerValue(field, range.to);
    dateValue = dateValue.minus({
        [field.type === "date" ? "day" : "second"]: 1,
    });
    return field.type === "date" ? serializeDate(dateValue) : serializeDateTime(dateValue);
}
```

**功能特性**:
- **范围处理**: 处理日期范围的结束值
- **类型适配**: 根据字段类型选择日期或日期时间处理
- **边界调整**: 调整范围边界以避免重叠
- **序列化**: 将日期对象序列化为字符串格式

## 最佳实践

### 1. 性能优化
```javascript
// ✅ 推荐：使用缓存减少重复计算
class OptimizedSampleServer extends SampleServer {
    constructor(modelName, fields) {
        super(modelName, fields);
        this.generatedRecords = new Map();
    }

    generateRecord(id) {
        if (!this.generatedRecords.has(id)) {
            this.generatedRecords.set(id, super.generateRecord(id));
        }
        return this.generatedRecords.get(id);
    }
}
```

### 2. 数据一致性
```javascript
// ✅ 推荐：确保关系字段的一致性
class ConsistentSampleServer extends SampleServer {
    generateMany2oneValue(field, recordId) {
        // 确保相同记录的相同字段总是返回相同的关系值
        const seed = `${this.modelName}:${recordId}:${field.name}`;
        const hash = this.simpleHash(seed);
        const relatedId = (hash % 100) + 1;

        return {
            id: relatedId,
            display_name: getSampleFromId(relatedId, this.getRelatedSampleNames(field.relation))
        };
    }

    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash);
    }
}
```

### 3. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
class RobustSampleServer extends SampleServer {
    mockRpc(params) {
        try {
            return super.mockRpc(params);
        } catch (error) {
            if (error instanceof UnimplementedRouteError) {
                console.warn(`示例服务器不支持方法: ${params.method}`);
                return this.getDefaultResponse(params.method);
            }
            throw error;
        }
    }

    getDefaultResponse(method) {
        switch (method) {
            case 'search_read':
                return [];
            case 'read_group':
                return [];
            case 'web_read':
                return [];
            default:
                return null;
        }
    }
}
```

## 总结

Odoo 示例服务器模块提供了强大的示例数据生成和模拟能力：

**核心优势**:
- **完整模拟**: 完整模拟 Odoo 服务器的 RPC 接口
- **智能生成**: 根据字段类型智能生成符合逻辑的示例数据
- **性能优化**: 支持缓存、分页等性能优化机制
- **可扩展性**: 支持自定义数据生成器和业务逻辑
- **一致性**: 确保生成数据的逻辑一致性

**适用场景**:
- 前端开发和测试
- 演示和培训环境
- 空状态用户体验
- 原型开发
- 离线开发环境

**设计优势**:
- 模块化架构
- 可配置的数据模板
- 缓存和性能优化
- 完整的错误处理

这个示例服务器为 Odoo Web 客户端提供了强大的示例数据支持，是改善用户体验和开发效率的重要工具。

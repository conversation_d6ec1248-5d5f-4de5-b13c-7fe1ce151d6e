# DynamicGroupList - 动态分组列表数据点

## 概述

`dynamic_group_list.js` 是 Odoo Web 关系模型的动态分组列表数据点类，提供了基于分组条件的动态记录列表管理功能。该模块包含370行代码，继承自DynamicList类，专门处理分组视图中的记录集合，支持分组展开/折叠、分组统计、分组过滤、分组排序等功能，具备动态分组加载、分组状态管理、分组操作等特性，是Odoo Web中处理分组数据展示的核心组件。

## 文件信息
- **路径**: `/web/static/src/model/relational_model/dynamic_group_list.js`
- **行数**: 370
- **模块**: `@web/model/relational_model/dynamic_group_list`

## 依赖关系

```javascript
// 核心依赖
'@web/core/domain'                           // 域处理
'@web/model/relational_model/dynamic_list'   // 动态列表基类
'@web/model/relational_model/utils'          // 关系模型工具
```

## 主类定义

### 1. DynamicGroupList 类

```javascript
class DynamicGroupList extends DynamicList {
    static type = "DynamicGroupList";
    
    setup(config, data) {
        super.setup(...arguments);
        this.isGrouped = true;
        this._nbRecordsMatchingDomain = null;
        this._setData(data);
    }
    
    _setData(data) {
        this.groups = data.groups.map((g) => this._createGroupDatapoint(g));
        this.count = data.length;
        this._selectDomain(this.isDomainSelected);
    }
}
```

**类特性**:
- **继承DynamicList**: 继承动态列表的基础功能
- **分组管理**: 专门处理分组数据的展示和操作
- **状态跟踪**: 跟踪分组的展开/折叠状态
- **动态加载**: 支持分组数据的动态加载

## 核心属性

### 1. 分组属性

```javascript
// 分组相关属性
get groupBy() { 
    return this.config.groupBy; 
}

get groupByField() {
    return this.fields[this.groupBy[0].split(":")[0]];
}

get hasData() {
    return this.groups.some((group) => group.hasData);
}

get isGrouped() {
    return true; // 始终为true，表示这是分组列表
}

// 分组集合
get groups() {
    return this._groups || [];
}

get openGroups() {
    return this.groups.filter(group => group.isOpen);
}

get closedGroups() {
    return this.groups.filter(group => !group.isOpen);
}
```

**分组属性功能**:
- **groupBy**: 分组字段配置
- **groupByField**: 分组字段的定义信息
- **hasData**: 检查是否有分组数据
- **groups**: 所有分组的集合
- **openGroups**: 已展开的分组
- **closedGroups**: 已折叠的分组

### 2. 统计属性

```javascript
get totalCount() {
    return this.groups.reduce((sum, group) => sum + group.count, 0);
}

get loadedCount() {
    return this.groups.reduce((sum, group) => sum + group.loadedCount, 0);
}

get aggregates() {
    // 计算分组聚合值
    const aggregates = {};
    for (const fieldName in this.activeFields) {
        const field = this.fields[fieldName];
        if (field && field.group_operator) {
            aggregates[fieldName] = this._calculateAggregate(fieldName, field.group_operator);
        }
    }
    return aggregates;
}
```

**统计属性功能**:
- **totalCount**: 所有分组的记录总数
- **loadedCount**: 已加载的记录数量
- **aggregates**: 分组聚合统计值

## 核心方法

### 1. 分组管理

```javascript
// 创建分组数据点
_createGroupDatapoint(groupData) {
    return this.model._createGroupDatapoint(groupData, {
        parentList: this,
        activeFields: this.activeFields,
        fields: this.fields,
        context: this.context,
        groupBy: this.groupBy.slice(1), // 移除当前分组字段
        orderBy: this.orderBy
    });
}

// 展开分组
async expandGroup(group) {
    return this.model.mutex.exec(async () => {
        if (group.isOpen) {
            return group;
        }
        
        await group.load();
        group.isOpen = true;
        
        return group;
    });
}

// 折叠分组
async collapseGroup(group) {
    return this.model.mutex.exec(async () => {
        if (!group.isOpen) {
            return group;
        }
        
        group.isOpen = false;
        group.unload(); // 释放分组数据
        
        return group;
    });
}

// 切换分组状态
async toggleGroup(group) {
    if (group.isOpen) {
        return this.collapseGroup(group);
    } else {
        return this.expandGroup(group);
    }
}

// 展开所有分组
async expandAllGroups() {
    return this.model.mutex.exec(async () => {
        const promises = this.closedGroups.map(group => this.expandGroup(group));
        await Promise.all(promises);
        return this.groups;
    });
}

// 折叠所有分组
async collapseAllGroups() {
    return this.model.mutex.exec(async () => {
        this.openGroups.forEach(group => {
            group.isOpen = false;
            group.unload();
        });
        return this.groups;
    });
}
```

**分组管理功能**:
- **创建分组**: 创建分组数据点实例
- **展开折叠**: 控制分组的展开和折叠状态
- **批量操作**: 支持批量展开或折叠分组
- **状态管理**: 管理分组的加载状态

### 2. 数据加载

```javascript
// 加载分组数据
async load(params = {}) {
    return this.model.mutex.exec(async () => {
        const { domain, groupBy, orderBy, limit, offset } = {
            domain: this.domain,
            groupBy: this.groupBy,
            orderBy: this.orderBy,
            limit: this.config.limit,
            offset: this.config.offset,
            ...params
        };
        
        // 读取分组数据
        const groupsData = await this.model._readGroup({
            resModel: this.resModel,
            domain,
            groupBy,
            orderBy,
            limit,
            offset,
            context: this.context,
            lazy: true // 懒加载分组内容
        });
        
        this._setData({ groups: groupsData, length: groupsData.length });
        
        return this.groups;
    });
}

// 加载更多分组
async loadMoreGroups(limit = null) {
    const currentOffset = this.groups.length;
    const loadLimit = limit || this.config.limit;
    
    const newGroups = await this.load({
        offset: currentOffset,
        limit: loadLimit
    });
    
    return newGroups;
}

// 重新加载分组
async reload() {
    // 保存当前展开状态
    const openGroupKeys = this.openGroups.map(group => group.groupKey);
    
    await this.load();
    
    // 恢复展开状态
    for (const group of this.groups) {
        if (openGroupKeys.includes(group.groupKey)) {
            await this.expandGroup(group);
        }
    }
    
    return this.groups;
}
```

**数据加载功能**:
- **分组加载**: 加载分组结构和统计信息
- **懒加载**: 支持分组内容的懒加载
- **状态保持**: 重新加载时保持分组展开状态
- **分页支持**: 支持分组的分页加载

### 3. 分组操作

```javascript
// 在分组中创建记录
async createRecordInGroup(group, values = {}) {
    return this.model.mutex.exec(async () => {
        // 添加分组字段值
        const groupFieldName = this.groupBy[0].split(":")[0];
        const groupValue = getGroupServerValue(group.value, this.groupByField);
        
        const recordValues = {
            ...values,
            [groupFieldName]: groupValue
        };
        
        // 确保分组已展开
        if (!group.isOpen) {
            await this.expandGroup(group);
        }
        
        // 在分组中创建记录
        const record = await group.createRecord(recordValues);
        
        // 更新分组统计
        group.count++;
        this.count++;
        
        return record;
    });
}

// 删除分组中的记录
async deleteRecordFromGroup(group, record) {
    return this.model.mutex.exec(async () => {
        const success = await group.deleteRecord(record);
        
        if (success) {
            // 更新分组统计
            group.count--;
            this.count--;
            
            // 如果分组为空，可能需要移除分组
            if (group.count === 0 && this.config.removeEmptyGroups) {
                await this.removeGroup(group);
            }
        }
        
        return success;
    });
}

// 移除空分组
async removeGroup(group) {
    const index = this.groups.indexOf(group);
    if (index !== -1) {
        this.groups.splice(index, 1);
        group.destroy();
    }
}

// 移动记录到其他分组
async moveRecordToGroup(record, sourceGroup, targetGroup) {
    return this.model.mutex.exec(async () => {
        const groupFieldName = this.groupBy[0].split(":")[0];
        const targetValue = getGroupServerValue(targetGroup.value, this.groupByField);
        
        // 更新记录的分组字段
        await record.update({ [groupFieldName]: targetValue });
        await record.save();
        
        // 从源分组移除
        sourceGroup.removeRecord(record);
        sourceGroup.count--;
        
        // 添加到目标分组
        if (!targetGroup.isOpen) {
            await this.expandGroup(targetGroup);
        }
        targetGroup.addRecord(record);
        targetGroup.count++;
        
        return record;
    });
}
```

**分组操作功能**:
- **记录创建**: 在指定分组中创建记录
- **记录删除**: 从分组中删除记录
- **分组清理**: 自动清理空分组
- **记录移动**: 在分组间移动记录

### 4. 搜索和过滤

```javascript
// 应用分组过滤
async applyGroupFilter(groupFilter) {
    return this.model.mutex.exec(async () => {
        // 构建分组过滤域
        const filterDomain = this._buildGroupFilterDomain(groupFilter);
        const combinedDomain = Domain.and([this.domain, filterDomain]).toList();
        
        this.config.domain = combinedDomain;
        await this.load();
        
        return this.groups;
    });
}

// 构建分组过滤域
_buildGroupFilterDomain(groupFilter) {
    const { field, operator, value } = groupFilter;
    return [[field, operator, value]];
}

// 搜索分组
searchGroups(searchText) {
    if (!searchText) {
        return this.groups;
    }
    
    return this.groups.filter(group => {
        const groupDisplayName = group.displayName || '';
        return groupDisplayName.toLowerCase().includes(searchText.toLowerCase());
    });
}

// 过滤分组
filterGroups(predicate) {
    return this.groups.filter(predicate);
}
```

**搜索过滤功能**:
- **分组过滤**: 基于分组值进行过滤
- **文本搜索**: 在分组名称中搜索
- **自定义过滤**: 支持自定义过滤条件
- **域构建**: 智能构建过滤域表达式

## 使用场景

### 1. 分组视图管理器

```javascript
// 分组视图管理器
class GroupViewManager {
    constructor(model, config) {
        this.model = model;
        this.groupList = new DynamicGroupList(model, config);
        this.expandedGroups = new Set();
        this.selectedRecords = new Set();
    }
    
    async initialize() {
        await this.groupList.load();
        return this.getViewState();
    }
    
    async expandGroup(group) {
        await this.groupList.expandGroup(group);
        this.expandedGroups.add(group.groupKey);
        return this.getViewState();
    }
    
    async collapseGroup(group) {
        await this.groupList.collapseGroup(group);
        this.expandedGroups.delete(group.groupKey);
        return this.getViewState();
    }
    
    async toggleGroup(group) {
        if (group.isOpen) {
            return this.collapseGroup(group);
        } else {
            return this.expandGroup(group);
        }
    }
    
    async expandAllGroups() {
        await this.groupList.expandAllGroups();
        this.groupList.groups.forEach(group => {
            this.expandedGroups.add(group.groupKey);
        });
        return this.getViewState();
    }
    
    async collapseAllGroups() {
        await this.groupList.collapseAllGroups();
        this.expandedGroups.clear();
        return this.getViewState();
    }
    
    async createRecordInGroup(group) {
        const record = await this.groupList.createRecordInGroup(group);
        await this.editRecord(record);
        return record;
    }
    
    async editRecord(record) {
        await record.switchMode('edit');
        return record;
    }
    
    async deleteSelectedRecords() {
        const recordsToDelete = Array.from(this.selectedRecords);
        const groupedRecords = this.groupRecordsByGroup(recordsToDelete);
        
        for (const [group, records] of groupedRecords) {
            for (const record of records) {
                await this.groupList.deleteRecordFromGroup(group, record);
            }
        }
        
        this.selectedRecords.clear();
        return this.getViewState();
    }
    
    groupRecordsByGroup(records) {
        const grouped = new Map();
        
        for (const record of records) {
            const group = this.findGroupForRecord(record);
            if (group) {
                if (!grouped.has(group)) {
                    grouped.set(group, []);
                }
                grouped.get(group).push(record);
            }
        }
        
        return grouped;
    }
    
    findGroupForRecord(record) {
        for (const group of this.groupList.groups) {
            if (group.containsRecord(record)) {
                return group;
            }
        }
        return null;
    }
    
    selectRecord(record, selected = true) {
        if (selected) {
            this.selectedRecords.add(record);
        } else {
            this.selectedRecords.delete(record);
        }
    }
    
    selectAllRecordsInGroup(group, selected = true) {
        if (!group.isOpen) return;
        
        group.records.forEach(record => {
            this.selectRecord(record, selected);
        });
    }
    
    async applyGroupBy(groupByFields) {
        this.groupList.config.groupBy = groupByFields;
        await this.groupList.load();
        this.expandedGroups.clear();
        this.selectedRecords.clear();
        return this.getViewState();
    }
    
    async sortGroups(orderBy) {
        this.groupList.config.orderBy = orderBy;
        await this.groupList.load();
        return this.getViewState();
    }
    
    getViewState() {
        return {
            groups: this.groupList.groups,
            totalCount: this.groupList.totalCount,
            loadedCount: this.groupList.loadedCount,
            aggregates: this.groupList.aggregates,
            expandedGroups: Array.from(this.expandedGroups),
            selectedCount: this.selectedRecords.size,
            groupBy: this.groupList.groupBy,
            hasData: this.groupList.hasData
        };
    }
    
    getGroupStatistics() {
        return {
            totalGroups: this.groupList.groups.length,
            openGroups: this.groupList.openGroups.length,
            closedGroups: this.groupList.closedGroups.length,
            totalRecords: this.groupList.totalCount,
            loadedRecords: this.groupList.loadedCount,
            averageRecordsPerGroup: this.groupList.totalCount / this.groupList.groups.length
        };
    }
}

// 使用示例
const groupManager = new GroupViewManager(model, {
    resModel: 'sale.order',
    domain: [],
    groupBy: ['state'],
    orderBy: [{ name: 'date_order', asc: false }],
    activeFields: orderActiveFields,
    fields: orderFields,
    context: {}
});

// 初始化
await groupManager.initialize();

// 展开分组
await groupManager.expandGroup(group);

// 在分组中创建记录
await groupManager.createRecordInGroup(group);

// 应用新的分组
await groupManager.applyGroupBy(['partner_id', 'state']);
```

### 2. 分组统计分析器

```javascript
// 分组统计分析器
class GroupStatisticsAnalyzer {
    constructor(groupList) {
        this.groupList = groupList;
    }
    
    calculateGroupStatistics() {
        const stats = {
            groups: [],
            totals: {},
            averages: {},
            distributions: {}
        };
        
        // 计算每个分组的统计
        for (const group of this.groupList.groups) {
            const groupStats = this.calculateSingleGroupStats(group);
            stats.groups.push(groupStats);
        }
        
        // 计算总计
        stats.totals = this.calculateTotals();
        
        // 计算平均值
        stats.averages = this.calculateAverages();
        
        // 计算分布
        stats.distributions = this.calculateDistributions();
        
        return stats;
    }
    
    calculateSingleGroupStats(group) {
        const stats = {
            groupKey: group.groupKey,
            displayName: group.displayName,
            count: group.count,
            loadedCount: group.loadedCount,
            isOpen: group.isOpen,
            aggregates: {}
        };
        
        // 计算分组聚合值
        if (group.isOpen && group.records) {
            for (const fieldName in this.groupList.activeFields) {
                const field = this.groupList.fields[fieldName];
                if (field && this.isAggregatable(field)) {
                    stats.aggregates[fieldName] = this.calculateFieldAggregate(
                        group.records, fieldName, field
                    );
                }
            }
        }
        
        return stats;
    }
    
    calculateTotals() {
        const totals = {};
        
        for (const fieldName in this.groupList.activeFields) {
            const field = this.groupList.fields[fieldName];
            if (field && this.isAggregatable(field)) {
                totals[fieldName] = this.groupList.groups.reduce((sum, group) => {
                    if (group.isOpen && group.records) {
                        const groupTotal = this.calculateFieldAggregate(
                            group.records, fieldName, field
                        );
                        return sum + (groupTotal || 0);
                    }
                    return sum;
                }, 0);
            }
        }
        
        return totals;
    }
    
    calculateAverages() {
        const averages = {};
        const totals = this.calculateTotals();
        const totalRecords = this.groupList.loadedCount;
        
        for (const fieldName in totals) {
            if (totalRecords > 0) {
                averages[fieldName] = totals[fieldName] / totalRecords;
            }
        }
        
        return averages;
    }
    
    calculateDistributions() {
        const distributions = {};
        
        // 计算分组大小分布
        const groupSizes = this.groupList.groups.map(group => group.count);
        distributions.groupSizes = {
            min: Math.min(...groupSizes),
            max: Math.max(...groupSizes),
            average: groupSizes.reduce((sum, size) => sum + size, 0) / groupSizes.length,
            median: this.calculateMedian(groupSizes)
        };
        
        // 计算字段值分布
        for (const fieldName in this.groupList.activeFields) {
            const field = this.groupList.fields[fieldName];
            if (field && this.isAggregatable(field)) {
                distributions[fieldName] = this.calculateFieldDistribution(fieldName);
            }
        }
        
        return distributions;
    }
    
    calculateFieldAggregate(records, fieldName, field) {
        const values = records.map(record => record.data[fieldName])
            .filter(value => value !== null && value !== undefined);
        
        if (values.length === 0) return 0;
        
        switch (field.group_operator || 'sum') {
            case 'sum':
                return values.reduce((sum, value) => sum + (parseFloat(value) || 0), 0);
            case 'avg':
                return values.reduce((sum, value) => sum + (parseFloat(value) || 0), 0) / values.length;
            case 'max':
                return Math.max(...values.map(v => parseFloat(v) || 0));
            case 'min':
                return Math.min(...values.map(v => parseFloat(v) || 0));
            case 'count':
                return values.length;
            default:
                return values.length;
        }
    }
    
    calculateFieldDistribution(fieldName) {
        const allValues = [];
        
        for (const group of this.groupList.groups) {
            if (group.isOpen && group.records) {
                const groupValues = group.records
                    .map(record => record.data[fieldName])
                    .filter(value => value !== null && value !== undefined)
                    .map(value => parseFloat(value) || 0);
                allValues.push(...groupValues);
            }
        }
        
        if (allValues.length === 0) return null;
        
        allValues.sort((a, b) => a - b);
        
        return {
            min: allValues[0],
            max: allValues[allValues.length - 1],
            average: allValues.reduce((sum, value) => sum + value, 0) / allValues.length,
            median: this.calculateMedian(allValues),
            quartiles: this.calculateQuartiles(allValues)
        };
    }
    
    calculateMedian(values) {
        const sorted = [...values].sort((a, b) => a - b);
        const mid = Math.floor(sorted.length / 2);
        
        if (sorted.length % 2 === 0) {
            return (sorted[mid - 1] + sorted[mid]) / 2;
        } else {
            return sorted[mid];
        }
    }
    
    calculateQuartiles(values) {
        const sorted = [...values].sort((a, b) => a - b);
        const n = sorted.length;
        
        return {
            q1: sorted[Math.floor(n * 0.25)],
            q2: this.calculateMedian(sorted),
            q3: sorted[Math.floor(n * 0.75)]
        };
    }
    
    isAggregatable(field) {
        return ['integer', 'float', 'monetary'].includes(field.type);
    }
    
    generateReport() {
        const stats = this.calculateGroupStatistics();
        
        return {
            summary: {
                totalGroups: this.groupList.groups.length,
                totalRecords: this.groupList.totalCount,
                loadedRecords: this.groupList.loadedCount,
                openGroups: this.groupList.openGroups.length
            },
            groupDetails: stats.groups,
            aggregates: stats.totals,
            averages: stats.averages,
            distributions: stats.distributions,
            generatedAt: new Date().toISOString()
        };
    }
}

// 使用示例
const analyzer = new GroupStatisticsAnalyzer(groupList);
const report = analyzer.generateReport();
console.log('Group Statistics Report:', report);
```

## 技术特点

### 1. 分组数据管理
- **动态分组**: 基于字段值动态创建分组
- **懒加载**: 分组内容的按需加载
- **状态管理**: 分组展开/折叠状态管理
- **统计计算**: 自动计算分组统计信息

### 2. 高效的数据操作
- **批量操作**: 支持批量分组操作
- **状态保持**: 操作后保持分组状态
- **智能更新**: 最小化数据重新加载
- **内存优化**: 折叠分组时释放内存

### 3. 灵活的分组配置
- **多级分组**: 支持多级分组结构
- **自定义聚合**: 支持自定义聚合函数
- **排序控制**: 分组和分组内记录的排序
- **过滤集成**: 与搜索过滤系统集成

### 4. 用户交互支持
- **展开折叠**: 直观的分组展开折叠操作
- **拖拽支持**: 支持记录在分组间拖拽
- **选择管理**: 支持分组内记录的选择
- **上下文菜单**: 分组相关的上下文操作

## 设计模式

### 1. 组合模式 (Composite Pattern)
- **分组结构**: 分组和记录的树形结构
- **统一接口**: 分组和记录的统一操作接口
- **递归操作**: 支持递归的分组操作

### 2. 策略模式 (Strategy Pattern)
- **聚合策略**: 不同字段的聚合计算策略
- **加载策略**: 不同的分组数据加载策略
- **展示策略**: 不同的分组展示策略

### 3. 观察者模式 (Observer Pattern)
- **状态通知**: 分组状态变化的通知
- **数据同步**: 分组数据变化的同步
- **事件分发**: 分组事件的分发机制

## 注意事项

1. **性能考虑**: 避免同时展开过多分组
2. **内存管理**: 及时释放折叠分组的数据
3. **用户体验**: 提供分组加载状态指示
4. **数据一致性**: 确保分组统计的准确性

## 扩展建议

1. **虚拟滚动**: 支持大量分组的虚拟滚动
2. **分组缓存**: 智能的分组数据缓存
3. **实时更新**: 分组数据的实时更新
4. **自定义聚合**: 支持更多自定义聚合函数
5. **分组模板**: 预定义的分组配置模板

该动态分组列表类为Odoo Web应用提供了完整的分组数据管理功能，通过高效的分组操作和智能的状态管理确保了良好的用户体验和系统性能。

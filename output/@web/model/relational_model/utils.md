# Relational Model Utils - 关系模型工具函数

## 概述

`utils.js` 是 Odoo Web 关系模型的工具函数集合，提供了关系模型中常用的辅助功能。该模块包含845行代码，是关系模型系统的工具基础，提供了字段处理、数据转换、上下文管理、域处理、ID生成等功能，具备类型转换、数据验证、配置合并、异步处理等特性，为整个关系模型系统提供了可靠的工具支持。

## 文件信息
- **路径**: `/web/static/src/model/relational_model/utils.js`
- **行数**: 845
- **模块**: `@web/model/relational_model/utils`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                          // OWL框架
'@web/core/context'                  // 上下文处理
'@web/core/domain'                   // 域处理
'@web/core/l10n/dates'               // 日期本地化
'@web/core/orm_service'              // ORM服务
'@web/core/py_js/py'                 // Python表达式
'@web/core/utils/concurrency'        // 并发工具
'@web/core/utils/objects'            // 对象工具
'@web/core/utils/reactive'           // 响应式工具
'@web/core/utils/timing'             // 时间工具
'@web/search/utils/order_by'         // 排序工具
'@web/core/network/rpc'              // RPC网络
```

## 常量定义

```javascript
// 可聚合字段类型
const AGGREGATABLE_FIELD_TYPES = ["float", "integer", "monetary"];

// ID计数器
let idCounter = 0;
```

## 核心工具函数

### 1. 字段配置工具

```javascript
// 创建活动字段配置
function makeActiveField({
    context,
    invisible,
    readonly,
    required,
    onChange,
    forceSave,
    isHandle,
} = {}) {
    return {
        context: context || "{}",
        invisible: convertBoolToPyExpr(invisible || false),
        readonly: convertBoolToPyExpr(readonly || false),
        required: convertBoolToPyExpr(required || false),
        onChange: onChange || false,
        forceSave: forceSave || false,
        isHandle: isHandle || false,
    };
}

// 布尔值转Python表达式
function convertBoolToPyExpr(value) {
    if (value === true || value === false) {
        return value ? "True" : "False";
    }
    return value;
}

// 添加字段依赖
function addFieldDependencies(activeFields, fields, fieldDependencies = []) {
    for (const field of fieldDependencies) {
        if (!("readonly" in field)) {
            field.readonly = true;
        }
        if (field.name in activeFields) {
            patchActiveFields(activeFields[field.name], makeActiveField(field));
        } else {
            activeFields[field.name] = makeActiveField(field);
        }
        if (!fields[field.name]) {
            const newField = omit(field, [
                "context", "invisible", "required", "readonly", "onChange"
            ]);
            fields[field.name] = newField;
            if (newField.type === "selection" && !Array.isArray(newField.selection)) {
                newField.selection = [];
            }
        }
    }
}
```

**字段配置功能**:
- **配置创建**: 创建标准的活动字段配置
- **类型转换**: 布尔值到Python表达式的转换
- **依赖管理**: 管理字段间的依赖关系
- **配置合并**: 合并和补丁字段配置

### 2. 数据处理工具

```javascript
// 解析服务器值
function parseServerValue(value, field, record) {
    if (value === false || value === null || value === undefined) {
        return value;
    }
    
    switch (field.type) {
        case "date":
            return deserializeDate(value);
        case "datetime":
            return deserializeDateTime(value);
        case "many2one":
            return Array.isArray(value) ? value : [value, ""];
        case "one2many":
        case "many2many":
            return parseX2ManyValue(value, field, record);
        case "selection":
            return parseSelectionValue(value, field);
        case "boolean":
            return Boolean(value);
        case "integer":
            return parseInt(value) || 0;
        case "float":
        case "monetary":
            return parseFloat(value) || 0.0;
        default:
            return value;
    }
}

// 序列化客户端值
function serializeValue(value, field) {
    if (value === false || value === null || value === undefined) {
        return value;
    }
    
    switch (field.type) {
        case "date":
            return serializeDate(value);
        case "datetime":
            return serializeDateTime(value);
        case "many2one":
            return Array.isArray(value) ? value[0] : value;
        case "one2many":
        case "many2many":
            return serializeX2ManyValue(value);
        default:
            return value;
    }
}
```

**数据处理功能**:
- **值解析**: 将服务器值解析为客户端格式
- **值序列化**: 将客户端值序列化为服务器格式
- **类型转换**: 根据字段类型进行适当转换
- **空值处理**: 安全处理空值和未定义值

### 3. 上下文管理

```javascript
// 获取基础评估上下文
function getBasicEvalContext(config) {
    const evalContext = {
        uid: config.userId || 1,
        user: config.user || {},
        company_id: config.currentCompanyId || 1,
        company_ids: config.allowedCompanyIds || [1],
        context: config.context || {},
        today: new Date().toISOString().split('T')[0],
        now: new Date().toISOString(),
    };
    
    // 添加时区信息
    if (config.timezone) {
        evalContext.tz = config.timezone;
    }
    
    return evalContext;
}

// 获取字段上下文
function getFieldContext(record, fieldName, fieldInfo) {
    const baseContext = record.evalContext;
    const fieldContext = fieldInfo.context || "{}";
    
    if (fieldContext === "{}") {
        return baseContext;
    }
    
    try {
        const additionalContext = evaluateExpr(fieldContext, baseContext);
        return makeContext([baseContext, additionalContext]);
    } catch (error) {
        console.warn(`Error evaluating field context for ${fieldName}:`, error);
        return baseContext;
    }
}

// 创建记录上下文
function createRecordContext(record, additionalContext = {}) {
    const evalContext = { ...record.evalContext };
    
    // 添加记录数据到上下文
    for (const fieldName in record.data) {
        const value = record.data[fieldName];
        const field = record.fields[fieldName];
        
        if (field && field.type === "many2one" && Array.isArray(value)) {
            evalContext[fieldName] = value[0];
        } else {
            evalContext[fieldName] = value;
        }
    }
    
    // 合并额外上下文
    Object.assign(evalContext, additionalContext);
    
    return evalContext;
}
```

**上下文管理功能**:
- **基础上下文**: 创建标准的评估上下文
- **字段上下文**: 处理字段特定的上下文
- **记录上下文**: 基于记录数据创建上下文
- **上下文合并**: 安全合并多个上下文

### 4. ID生成和管理

```javascript
// 生成唯一ID
function getId(prefix = "id") {
    return `${prefix}_${++idCounter}`;
}

// 生成虚拟ID
function getVirtualId() {
    return `virtual_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// 检查是否为虚拟ID
function isVirtualId(id) {
    return typeof id === "string" && id.startsWith("virtual_");
}

// 提取真实ID
function getRealId(id) {
    if (isVirtualId(id)) {
        return null;
    }
    return typeof id === "number" ? id : parseInt(id);
}
```

**ID管理功能**:
- **唯一ID**: 生成全局唯一的标识符
- **虚拟ID**: 为新记录生成虚拟标识符
- **ID检查**: 检查ID的类型和有效性
- **ID转换**: 在不同ID格式间转换

### 5. 字段规格处理

```javascript
// 获取字段规格
function getFieldsSpec(activeFields, fields) {
    const spec = {};
    
    for (const fieldName in activeFields) {
        const field = fields[fieldName];
        const activeField = activeFields[fieldName];
        
        if (!field) continue;
        
        spec[fieldName] = {
            type: field.type,
            string: field.string || fieldName,
        };
        
        // 关系字段的特殊处理
        if (["many2one", "one2many", "many2many"].includes(field.type)) {
            spec[fieldName].relation = field.relation;
            
            if (activeField.related) {
                spec[fieldName].fields = getFieldsSpec(
                    activeField.related.activeFields,
                    activeField.related.fields
                );
            }
        }
        
        // 选择字段的处理
        if (field.type === "selection" && field.selection) {
            spec[fieldName].selection = field.selection;
        }
    }
    
    return spec;
}

// 创建属性活动字段
function createPropertyActiveField(propertyDefinition) {
    const activeField = makeActiveField({
        readonly: propertyDefinition.readonly,
        required: propertyDefinition.required,
    });
    
    if (propertyDefinition.type === "many2one") {
        activeField.related = {
            activeFields: {
                id: makeActiveField(),
                display_name: makeActiveField(),
            },
            fields: {
                id: { type: "integer", name: "id" },
                display_name: { type: "char", name: "display_name" },
            },
        };
    }
    
    return activeField;
}
```

**字段规格功能**:
- **规格生成**: 生成字段的完整规格信息
- **关系处理**: 特殊处理关系字段的规格
- **属性字段**: 创建属性字段的活动配置
- **递归处理**: 递归处理嵌套字段结构

## 使用场景

### 1. 字段配置管理器

```javascript
// 字段配置管理器
class FieldConfigManager {
    constructor() {
        this.fieldConfigs = new Map();
        this.fieldDependencies = new Map();
    }
    
    registerField(fieldName, config) {
        const activeField = makeActiveField(config);
        this.fieldConfigs.set(fieldName, activeField);
        
        // 处理依赖关系
        if (config.dependencies) {
            this.fieldDependencies.set(fieldName, config.dependencies);
        }
        
        return activeField;
    }
    
    getFieldConfig(fieldName) {
        return this.fieldConfigs.get(fieldName);
    }
    
    updateFieldConfig(fieldName, updates) {
        const existing = this.fieldConfigs.get(fieldName);
        if (existing) {
            patchActiveFields(existing, makeActiveField(updates));
        }
    }
    
    buildActiveFields(fieldNames) {
        const activeFields = {};
        const fields = {};
        
        for (const fieldName of fieldNames) {
            const config = this.fieldConfigs.get(fieldName);
            if (config) {
                activeFields[fieldName] = { ...config };
            }
        }
        
        // 添加依赖字段
        for (const fieldName of fieldNames) {
            const dependencies = this.fieldDependencies.get(fieldName);
            if (dependencies) {
                addFieldDependencies(activeFields, fields, dependencies);
            }
        }
        
        return { activeFields, fields };
    }
    
    validateFieldConfig(fieldName, config) {
        const errors = [];
        
        // 验证必需属性
        if (!config.type) {
            errors.push(`Field ${fieldName} missing type`);
        }
        
        // 验证关系字段
        if (["many2one", "one2many", "many2many"].includes(config.type)) {
            if (!config.relation) {
                errors.push(`Relational field ${fieldName} missing relation`);
            }
        }
        
        // 验证选择字段
        if (config.type === "selection" && !config.selection) {
            errors.push(`Selection field ${fieldName} missing selection options`);
        }
        
        return errors;
    }
}

// 使用示例
const configManager = new FieldConfigManager();

// 注册字段配置
configManager.registerField('name', {
    type: 'char',
    required: true,
    readonly: false
});

configManager.registerField('partner_id', {
    type: 'many2one',
    relation: 'res.partner',
    required: false,
    dependencies: [
        { name: 'partner_name', type: 'char', readonly: true }
    ]
});

// 构建活动字段
const { activeFields, fields } = configManager.buildActiveFields(['name', 'partner_id']);
```

### 2. 数据转换工具

```javascript
// 数据转换工具类
class DataTransformer {
    constructor(fields) {
        this.fields = fields;
        this.converters = new Map();
        this.setupDefaultConverters();
    }
    
    setupDefaultConverters() {
        // 日期转换器
        this.converters.set('date', {
            parse: (value) => deserializeDate(value),
            serialize: (value) => serializeDate(value),
            validate: (value) => value instanceof Date || typeof value === 'string'
        });
        
        // 日期时间转换器
        this.converters.set('datetime', {
            parse: (value) => deserializeDateTime(value),
            serialize: (value) => serializeDateTime(value),
            validate: (value) => value instanceof Date || typeof value === 'string'
        });
        
        // Many2one转换器
        this.converters.set('many2one', {
            parse: (value) => Array.isArray(value) ? value : [value, ""],
            serialize: (value) => Array.isArray(value) ? value[0] : value,
            validate: (value) => Array.isArray(value) || typeof value === 'number'
        });
        
        // 数值转换器
        this.converters.set('integer', {
            parse: (value) => parseInt(value) || 0,
            serialize: (value) => parseInt(value) || 0,
            validate: (value) => typeof value === 'number' || !isNaN(parseInt(value))
        });
        
        this.converters.set('float', {
            parse: (value) => parseFloat(value) || 0.0,
            serialize: (value) => parseFloat(value) || 0.0,
            validate: (value) => typeof value === 'number' || !isNaN(parseFloat(value))
        });
    }
    
    parseServerData(data) {
        const parsed = {};
        
        for (const fieldName in data) {
            const value = data[fieldName];
            const field = this.fields[fieldName];
            
            if (field) {
                parsed[fieldName] = this.parseValue(value, field);
            } else {
                parsed[fieldName] = value;
            }
        }
        
        return parsed;
    }
    
    serializeClientData(data) {
        const serialized = {};
        
        for (const fieldName in data) {
            const value = data[fieldName];
            const field = this.fields[fieldName];
            
            if (field) {
                serialized[fieldName] = this.serializeValue(value, field);
            } else {
                serialized[fieldName] = value;
            }
        }
        
        return serialized;
    }
    
    parseValue(value, field) {
        if (value === null || value === undefined || value === false) {
            return value;
        }
        
        const converter = this.converters.get(field.type);
        if (converter) {
            try {
                return converter.parse(value);
            } catch (error) {
                console.warn(`Error parsing ${field.type} value:`, error);
                return value;
            }
        }
        
        return value;
    }
    
    serializeValue(value, field) {
        if (value === null || value === undefined || value === false) {
            return value;
        }
        
        const converter = this.converters.get(field.type);
        if (converter) {
            try {
                return converter.serialize(value);
            } catch (error) {
                console.warn(`Error serializing ${field.type} value:`, error);
                return value;
            }
        }
        
        return value;
    }
    
    validateValue(value, field) {
        const converter = this.converters.get(field.type);
        if (converter && converter.validate) {
            return converter.validate(value);
        }
        return true;
    }
    
    addConverter(fieldType, converter) {
        this.converters.set(fieldType, converter);
    }
    
    getConverter(fieldType) {
        return this.converters.get(fieldType);
    }
}

// 使用示例
const fields = {
    name: { type: 'char', name: 'name' },
    birth_date: { type: 'date', name: 'birth_date' },
    partner_id: { type: 'many2one', name: 'partner_id', relation: 'res.partner' },
    amount: { type: 'float', name: 'amount' }
};

const transformer = new DataTransformer(fields);

// 解析服务器数据
const serverData = {
    name: 'John Doe',
    birth_date: '2023-01-15',
    partner_id: [123, 'Partner Name'],
    amount: '150.75'
};

const clientData = transformer.parseServerData(serverData);
console.log('Parsed data:', clientData);

// 序列化客户端数据
const serializedData = transformer.serializeClientData(clientData);
console.log('Serialized data:', serializedData);
```

### 3. 上下文构建器

```javascript
// 上下文构建器
class ContextBuilder {
    constructor(baseConfig) {
        this.baseConfig = baseConfig;
        this.contextStack = [];
    }
    
    createBaseContext() {
        return getBasicEvalContext(this.baseConfig);
    }
    
    pushContext(context) {
        this.contextStack.push(context);
        return this;
    }
    
    popContext() {
        return this.contextStack.pop();
    }
    
    buildRecordContext(record, additionalContext = {}) {
        const baseContext = this.createBaseContext();
        const recordContext = createRecordContext(record, additionalContext);
        
        // 合并上下文栈
        const stackContext = this.contextStack.reduce((acc, ctx) => {
            return makeContext([acc, ctx]);
        }, {});
        
        return makeContext([baseContext, recordContext, stackContext]);
    }
    
    buildFieldContext(record, fieldName) {
        const fieldInfo = record.activeFields[fieldName];
        if (!fieldInfo) {
            return this.buildRecordContext(record);
        }
        
        const recordContext = this.buildRecordContext(record);
        return getFieldContext(record, fieldName, fieldInfo);
    }
    
    evaluateExpression(expression, context) {
        try {
            return evaluateExpr(expression, context);
        } catch (error) {
            console.warn('Error evaluating expression:', expression, error);
            return false;
        }
    }
    
    evaluateFieldProperty(record, fieldName, property) {
        const fieldInfo = record.activeFields[fieldName];
        if (!fieldInfo || !(property in fieldInfo)) {
            return false;
        }
        
        const expression = fieldInfo[property];
        if (typeof expression === 'boolean') {
            return expression;
        }
        
        const context = this.buildRecordContext(record);
        return this.evaluateExpression(expression, context);
    }
    
    isFieldInvisible(record, fieldName) {
        return this.evaluateFieldProperty(record, fieldName, 'invisible');
    }
    
    isFieldReadonly(record, fieldName) {
        return this.evaluateFieldProperty(record, fieldName, 'readonly');
    }
    
    isFieldRequired(record, fieldName) {
        return this.evaluateFieldProperty(record, fieldName, 'required');
    }
    
    withContext(context, callback) {
        this.pushContext(context);
        try {
            return callback();
        } finally {
            this.popContext();
        }
    }
}

// 使用示例
const contextBuilder = new ContextBuilder({
    userId: 1,
    currentCompanyId: 1,
    allowedCompanyIds: [1, 2],
    timezone: 'UTC'
});

// 构建记录上下文
const recordContext = contextBuilder.buildRecordContext(record, {
    custom_var: 'custom_value'
});

// 检查字段属性
const isReadonly = contextBuilder.isFieldReadonly(record, 'name');
const isRequired = contextBuilder.isFieldRequired(record, 'email');

// 使用临时上下文
const result = contextBuilder.withContext({ temp_var: true }, () => {
    return contextBuilder.evaluateExpression('temp_var and name', recordContext);
});
```

## 技术特点

### 1. 类型安全转换
- **智能解析**: 根据字段类型智能解析值
- **安全转换**: 安全的类型转换机制
- **错误处理**: 完善的转换错误处理
- **默认值**: 合理的默认值处理

### 2. 配置管理
- **标准化**: 标准化的字段配置格式
- **合并机制**: 智能的配置合并机制
- **依赖处理**: 自动处理字段依赖关系
- **验证机制**: 配置的验证和错误检查

### 3. 上下文处理
- **层次结构**: 支持上下文的层次结构
- **动态评估**: 动态的表达式评估
- **安全执行**: 安全的Python表达式执行
- **缓存优化**: 上下文计算的缓存优化

### 4. ID管理
- **唯一性**: 保证ID的全局唯一性
- **虚拟ID**: 支持新记录的虚拟ID
- **类型检查**: 完整的ID类型检查
- **转换工具**: ID格式间的转换工具

## 设计模式

### 1. 工厂模式 (Factory Pattern)
- **配置创建**: makeActiveField等工厂函数
- **标准化**: 统一的配置创建接口
- **扩展性**: 易于扩展新的配置类型

### 2. 策略模式 (Strategy Pattern)
- **类型转换**: 不同字段类型的转换策略
- **验证策略**: 不同的数据验证策略
- **序列化策略**: 不同的序列化策略

### 3. 建造者模式 (Builder Pattern)
- **上下文构建**: 复杂上下文的构建
- **配置构建**: 复杂配置的逐步构建
- **链式调用**: 支持链式调用的构建接口

## 注意事项

1. **类型安全**: 确保类型转换的安全性
2. **性能考虑**: 避免频繁的类型转换和上下文计算
3. **内存管理**: 及时清理不再使用的配置和上下文
4. **错误处理**: 完善的错误处理和日志记录

## 扩展建议

1. **缓存机制**: 添加智能缓存机制提高性能
2. **类型扩展**: 支持更多的字段类型
3. **验证增强**: 增强数据验证功能
4. **调试工具**: 提供调试和监控工具
5. **性能监控**: 监控工具函数的性能指标

该工具模块为Odoo Web关系模型提供了完整的工具支持，通过类型安全的转换和智能的配置管理确保了系统的稳定性和可维护性。

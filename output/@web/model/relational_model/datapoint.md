# DataPoint - 数据点基类

## 概述

`datapoint.js` 是 Odoo Web 关系模型的数据点基类，提供了所有数据点类型的基础功能。该模块包含103行代码，继承自Reactive类，定义了数据点的基本结构和通用方法，包括配置管理、字段信息、上下文处理等功能，具备响应式特性、唯一标识、配置封装等特性，是关系模型中所有数据点类型的抽象基类。

## 文件信息
- **路径**: `/web/static/src/model/relational_model/datapoint.js`
- **行数**: 103
- **模块**: `@web/model/relational_model/datapoint`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                  // OWL框架
'@web/core/domain'                           // 域处理
'@web/core/utils/reactive'                   // 响应式工具
'@web/model/relational_model/utils'          // 关系模型工具
```

## 类型定义

### 1. 参数类型

```javascript
/**
 * @typedef Params
 * @property {string} resModel - 资源模型名称
 * @property {Object} context - 上下文对象
 * @property {{[key: string]: FieldInfo}} activeFields - 活动字段信息
 * @property {{[key: string]: Field}} fields - 字段定义
 */
```

### 2. 字段类型

```javascript
/**
 * @typedef Field
 * @property {string} name - 字段名称
 * @property {string} type - 字段类型
 * @property {[string,string][]} [selection] - 选择项（可选）
 */
```

### 3. 字段信息类型

```javascript
/**
 * @typedef FieldInfo
 * @property {string} context - 字段上下文
 * @property {boolean} invisible - 是否不可见
 * @property {boolean} readonly - 是否只读
 * @property {boolean} required - 是否必填
 * @property {boolean} onChange - 是否有onChange事件
 */
```

## 主类定义

### 1. DataPoint 基类

```javascript
class DataPoint extends Reactive {
    constructor(model, config, data, options) {
        super(...arguments);
        this.id = getId("datapoint");
        this.model = model;
        markRaw(config.activeFields);
        markRaw(config.fields);
        this._config = config;
        this.setup(config, data, options);
    }
}
```

**基类特性**:
- **响应式**: 继承Reactive类，具备响应式特性
- **唯一标识**: 每个数据点都有唯一的ID
- **配置管理**: 封装和管理数据点配置
- **原始标记**: 使用markRaw标记配置对象避免响应式

## 核心属性

### 1. 配置属性

```javascript
// 配置相关属性
get config() { return this._config; }
get resModel() { return this.config.resModel; }
get context() { return this.config.context; }
get activeFields() { return this.config.activeFields; }
get fields() { return this.config.fields; }
get currentCompanyId() { return this.config.currentCompanyId; }
```

**配置属性功能**:
- **config**: 完整的配置对象
- **resModel**: 资源模型名称
- **context**: 上下文信息
- **activeFields**: 活动字段配置
- **fields**: 字段定义
- **currentCompanyId**: 当前公司ID

### 2. 字段属性

```javascript
// 字段相关属性
get fieldNames() {
    return Object.keys(this.activeFields);
}

get isInDOM() {
    return this.model.env.isInDOM(this);
}
```

**字段属性功能**:
- **fieldNames**: 获取所有活动字段名称
- **isInDOM**: 检查数据点是否在DOM中

## 核心方法

### 1. 抽象方法

```javascript
// 需要子类实现的抽象方法
setup(config, data, options) {
    // 子类必须实现此方法
    throw new Error("setup method must be implemented by subclass");
}
```

**抽象方法功能**:
- **setup**: 数据点初始化方法，子类必须实现
- **配置应用**: 应用配置到数据点实例
- **数据初始化**: 初始化数据点的数据
- **选项处理**: 处理初始化选项

### 2. 工具方法

```javascript
// 域评估
evalDomain(domain, record = this) {
    return evalDomain(domain, record.evalContext);
}

// 获取字段信息
getFieldInfo(fieldName) {
    return this.activeFields[fieldName] || {};
}

// 检查字段是否存在
hasField(fieldName) {
    return fieldName in this.activeFields;
}
```

**工具方法功能**:
- **域评估**: 在当前上下文中评估域表达式
- **字段信息**: 获取指定字段的配置信息
- **字段检查**: 检查字段是否在活动字段中

## 使用场景

### 1. 自定义数据点类型

```javascript
// 继承DataPoint创建自定义数据点
class CustomDataPoint extends DataPoint {
    static type = "CustomDataPoint";
    
    setup(config, data, options = {}) {
        // 初始化自定义属性
        this.customProperty = options.customProperty || null;
        this.customData = data.customData || {};
        
        // 设置事件监听
        this.onUpdate = options.onUpdate || (() => {});
        
        // 初始化状态
        this.state = {
            loading: false,
            error: null,
            lastUpdate: Date.now()
        };
        
        // 处理字段验证
        this.validators = new Map();
        for (const fieldName in this.activeFields) {
            const fieldInfo = this.activeFields[fieldName];
            if (fieldInfo.required) {
                this.validators.set(fieldName, this.createRequiredValidator(fieldName));
            }
        }
    }
    
    createRequiredValidator(fieldName) {
        return (value) => {
            if (!value && value !== 0) {
                return `${fieldName} is required`;
            }
            return null;
        };
    }
    
    validate() {
        const errors = [];
        for (const [fieldName, validator] of this.validators) {
            const value = this.customData[fieldName];
            const error = validator(value);
            if (error) {
                errors.push({ field: fieldName, message: error });
            }
        }
        return errors;
    }
    
    async updateData(changes) {
        this.state.loading = true;
        try {
            Object.assign(this.customData, changes);
            this.state.lastUpdate = Date.now();
            await this.onUpdate(changes);
        } catch (error) {
            this.state.error = error.message;
            throw error;
        } finally {
            this.state.loading = false;
        }
    }
    
    getDisplayValue(fieldName) {
        const field = this.fields[fieldName];
        const value = this.customData[fieldName];
        
        if (!field || value === null || value === undefined) {
            return '';
        }
        
        switch (field.type) {
            case 'selection':
                const option = field.selection.find(([key]) => key === value);
                return option ? option[1] : value;
            case 'many2one':
                return Array.isArray(value) ? value[1] : '';
            case 'boolean':
                return value ? 'Yes' : 'No';
            default:
                return String(value);
        }
    }
}
```

### 2. 数据点工厂

```javascript
// 数据点工厂类
class DataPointFactory {
    constructor(model) {
        this.model = model;
        this.types = new Map();
        this.registerDefaultTypes();
    }
    
    registerDefaultTypes() {
        this.register('Record', RecordDataPoint);
        this.register('StaticList', StaticListDataPoint);
        this.register('DynamicList', DynamicListDataPoint);
        this.register('Group', GroupDataPoint);
    }
    
    register(type, DataPointClass) {
        this.types.set(type, DataPointClass);
    }
    
    create(type, config, data, options) {
        const DataPointClass = this.types.get(type);
        if (!DataPointClass) {
            throw new Error(`Unknown data point type: ${type}`);
        }
        
        // 验证配置
        this.validateConfig(config);
        
        // 创建数据点实例
        const dataPoint = new DataPointClass(this.model, config, data, options);
        
        // 注册到模型
        this.model.registerDataPoint(dataPoint);
        
        return dataPoint;
    }
    
    validateConfig(config) {
        const required = ['resModel', 'activeFields', 'fields'];
        for (const prop of required) {
            if (!(prop in config)) {
                throw new Error(`Missing required config property: ${prop}`);
            }
        }
        
        // 验证字段配置
        for (const fieldName in config.activeFields) {
            if (!(fieldName in config.fields)) {
                throw new Error(`Active field ${fieldName} not found in fields definition`);
            }
        }
    }
    
    createFromTemplate(template, overrides = {}) {
        const config = { ...template.config, ...overrides };
        return this.create(template.type, config, template.data, template.options);
    }
}

// 使用示例
const factory = new DataPointFactory(model);

// 创建记录数据点
const recordDataPoint = factory.create('Record', {
    resModel: 'res.partner',
    resId: 123,
    activeFields: {
        name: { readonly: false, required: true },
        email: { readonly: false, required: false }
    },
    fields: {
        name: { type: 'char', name: 'name' },
        email: { type: 'char', name: 'email' }
    },
    context: {}
}, {
    id: 123,
    name: 'John Doe',
    email: '<EMAIL>'
});

// 创建列表数据点
const listDataPoint = factory.create('StaticList', {
    resModel: 'sale.order.line',
    activeFields: {
        product_id: { readonly: false },
        quantity: { readonly: false },
        price_unit: { readonly: false }
    },
    fields: {
        product_id: { type: 'many2one', name: 'product_id' },
        quantity: { type: 'float', name: 'quantity' },
        price_unit: { type: 'float', name: 'price_unit' }
    },
    context: {},
    limit: 10,
    offset: 0
}, [
    { id: 1, product_id: [1, 'Product A'], quantity: 2, price_unit: 100 },
    { id: 2, product_id: [2, 'Product B'], quantity: 1, price_unit: 200 }
]);
```

### 3. 数据点管理器

```javascript
// 数据点生命周期管理器
class DataPointManager {
    constructor() {
        this.dataPoints = new Map();
        this.listeners = new Map();
        this.cleanupTasks = new Set();
    }
    
    register(dataPoint) {
        this.dataPoints.set(dataPoint.id, dataPoint);
        
        // 设置清理任务
        const cleanup = () => {
            this.unregister(dataPoint.id);
        };
        this.cleanupTasks.add(cleanup);
        
        // 监听数据点销毁
        if (dataPoint.onDestroy) {
            dataPoint.onDestroy(cleanup);
        }
        
        return dataPoint;
    }
    
    unregister(dataPointId) {
        const dataPoint = this.dataPoints.get(dataPointId);
        if (dataPoint) {
            // 清理监听器
            const listeners = this.listeners.get(dataPointId);
            if (listeners) {
                listeners.forEach(listener => listener.destroy());
                this.listeners.delete(dataPointId);
            }
            
            // 移除数据点
            this.dataPoints.delete(dataPointId);
            
            console.log(`DataPoint ${dataPointId} unregistered`);
        }
    }
    
    get(dataPointId) {
        return this.dataPoints.get(dataPointId);
    }
    
    getByType(type) {
        return Array.from(this.dataPoints.values())
            .filter(dp => dp.constructor.type === type);
    }
    
    getByModel(resModel) {
        return Array.from(this.dataPoints.values())
            .filter(dp => dp.resModel === resModel);
    }
    
    addListener(dataPointId, event, callback) {
        const dataPoint = this.dataPoints.get(dataPointId);
        if (!dataPoint) {
            throw new Error(`DataPoint ${dataPointId} not found`);
        }
        
        if (!this.listeners.has(dataPointId)) {
            this.listeners.set(dataPointId, []);
        }
        
        const listener = {
            event,
            callback,
            destroy: () => {
                // 移除监听器的逻辑
            }
        };
        
        this.listeners.get(dataPointId).push(listener);
        
        // 实际添加监听器到数据点
        if (dataPoint.addEventListener) {
            dataPoint.addEventListener(event, callback);
        }
        
        return listener;
    }
    
    broadcast(event, data) {
        for (const dataPoint of this.dataPoints.values()) {
            if (dataPoint.handleEvent) {
                dataPoint.handleEvent(event, data);
            }
        }
    }
    
    cleanup() {
        // 执行所有清理任务
        this.cleanupTasks.forEach(task => task());
        this.cleanupTasks.clear();
        
        // 清理所有数据点
        this.dataPoints.clear();
        this.listeners.clear();
    }
    
    getStats() {
        const stats = {
            total: this.dataPoints.size,
            byType: {},
            byModel: {}
        };
        
        for (const dataPoint of this.dataPoints.values()) {
            const type = dataPoint.constructor.type || 'Unknown';
            const model = dataPoint.resModel || 'Unknown';
            
            stats.byType[type] = (stats.byType[type] || 0) + 1;
            stats.byModel[model] = (stats.byModel[model] || 0) + 1;
        }
        
        return stats;
    }
}

// 全局数据点管理器
const globalDataPointManager = new DataPointManager();

// 使用示例
const dataPoint = globalDataPointManager.register(
    new CustomDataPoint(model, config, data, options)
);

// 添加监听器
globalDataPointManager.addListener(dataPoint.id, 'update', (changes) => {
    console.log('DataPoint updated:', changes);
});

// 获取统计信息
console.log('DataPoint stats:', globalDataPointManager.getStats());
```

## 技术特点

### 1. 响应式基础
- **Reactive继承**: 继承响应式基类
- **自动更新**: 数据变化时自动通知
- **依赖跟踪**: 自动跟踪数据依赖
- **性能优化**: 最小化不必要的更新

### 2. 配置管理
- **不可变配置**: 使用markRaw保护配置
- **类型安全**: 完整的TypeScript类型定义
- **验证机制**: 配置验证和错误处理
- **扩展性**: 支持配置的动态扩展

### 3. 抽象设计
- **基类模式**: 提供通用功能的抽象基类
- **接口定义**: 定义子类必须实现的接口
- **多态支持**: 支持不同类型数据点的多态操作
- **扩展友好**: 易于扩展和定制

## 设计模式

### 1. 模板方法模式 (Template Method Pattern)
- **抽象基类**: DataPoint定义通用结构
- **具体实现**: 子类实现具体的setup方法
- **流程控制**: 基类控制初始化流程

### 2. 策略模式 (Strategy Pattern)
- **字段处理**: 不同字段类型的处理策略
- **验证策略**: 不同的数据验证策略
- **更新策略**: 不同的数据更新策略

### 3. 工厂模式 (Factory Pattern)
- **数据点创建**: 通过工厂创建不同类型的数据点
- **类型注册**: 动态注册新的数据点类型
- **配置验证**: 统一的配置验证逻辑

## 注意事项

1. **响应式性能**: 避免在响应式数据中存储大量数据
2. **内存管理**: 及时清理不再使用的数据点
3. **配置不变性**: 不要修改markRaw标记的配置对象
4. **子类实现**: 确保子类正确实现抽象方法

## 扩展建议

1. **生命周期钩子**: 添加更多的生命周期钩子
2. **事件系统**: 增强事件监听和分发机制
3. **缓存机制**: 添加智能缓存机制
4. **调试工具**: 提供调试和监控工具
5. **性能监控**: 监控数据点的性能指标

该数据点基类为Odoo Web关系模型提供了坚实的基础架构，通过响应式设计和抽象模式确保了数据管理的一致性和可扩展性。

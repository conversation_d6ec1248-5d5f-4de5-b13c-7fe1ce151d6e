# DynamicRecordList - 动态记录列表数据点

## 概述

`dynamic_record_list.js` 是 Odoo Web 关系模型的动态记录列表数据点类，提供了基于记录集合的动态列表管理功能。该模块包含184行代码，继承自DynamicList类，专门处理记录列表的展示和操作，支持记录的动态加载、添加、移除、排序等功能，具备记录状态管理、选择控制、批量操作等特性，是Odoo Web中处理记录集合展示的核心组件。

## 文件信息
- **路径**: `/web/static/src/model/relational_model/dynamic_record_list.js`
- **行数**: 184
- **模块**: `@web/model/relational_model/dynamic_record_list`

## 依赖关系

```javascript
// 核心依赖
'@web/model/relational_model/dynamic_list'   // 动态列表基类
```

## 主类定义

### 1. DynamicRecordList 类

```javascript
class DynamicRecordList extends DynamicList {
    static type = "DynamicRecordList";
    
    setup(config, data) {
        super.setup(config);
        this._setData(data);
    }
    
    _setData(data) {
        this.records = data.records.map((r) => this._createRecordDatapoint(r));
        this._updateCount(data);
        this._selectDomain(this.isDomainSelected);
    }
}
```

**类特性**:
- **继承DynamicList**: 继承动态列表的基础功能
- **记录管理**: 专门处理记录集合的管理
- **动态操作**: 支持记录的动态添加和移除
- **状态跟踪**: 跟踪记录的各种状态

## 核心属性

### 1. 基础属性

```javascript
// 数据检查
get hasData() {
    return this.count > 0;
}

// 记录集合
get records() {
    return this._records || [];
}

// 记录数量
get count() {
    return this._count || 0;
}

// 选中记录
get selectedRecords() {
    return this.records.filter(record => record.selected);
}

// 编辑中的记录
get editingRecords() {
    return this.records.filter(record => record.isInEdition);
}
```

**属性功能**:
- **hasData**: 检查是否有记录数据
- **records**: 当前记录集合
- **count**: 记录总数
- **selectedRecords**: 当前选中的记录
- **editingRecords**: 正在编辑的记录

### 2. 状态属性

```javascript
get isLoading() {
    return this._isLoading || false;
}

get isDirty() {
    return this.records.some(record => record.isDirty);
}

get hasInvalidRecords() {
    return this.records.some(record => record.hasInvalidFields);
}

get canSave() {
    return this.isDirty && !this.hasInvalidRecords;
}
```

**状态属性功能**:
- **isLoading**: 是否正在加载数据
- **isDirty**: 是否有未保存的变更
- **hasInvalidRecords**: 是否有无效记录
- **canSave**: 是否可以保存

## 核心方法

### 1. 记录操作

```javascript
// 添加已存在的记录
async addExistingRecord(resId, atFirstPosition = false) {
    return this.model.mutex.exec(async () => {
        const record = this._createRecordDatapoint({});
        await record._load({ resId });
        
        const position = atFirstPosition ? 0 : this.records.length;
        this._addRecord(record, position);
        
        return record;
    });
}

// 添加记录到列表
_addRecord(record, position) {
    if (position !== undefined) {
        this.records.splice(position, 0, record);
    } else {
        this.records.push(record);
    }
    this._updateCount({ count: this.records.length });
}

// 移除记录
removeRecord(record) {
    const index = this.records.indexOf(record);
    if (index !== -1) {
        this.records.splice(index, 1);
        this._updateCount({ count: this.records.length });
        return true;
    }
    return false;
}

// 创建新记录
async createRecord(values = {}, position = null) {
    return this.model.mutex.exec(async () => {
        const record = this._createRecordDatapoint(values, {
            isNew: true,
            mode: 'edit'
        });
        
        const insertPosition = position !== null ? position : this.records.length;
        this._addRecord(record, insertPosition);
        
        return record;
    });
}

// 删除记录
async deleteRecord(record) {
    return this.model.mutex.exec(async () => {
        if (record.isNew) {
            // 新记录直接从列表移除
            this.removeRecord(record);
            return true;
        } else {
            // 已保存记录需要从服务器删除
            const success = await this.model._deleteRecord(record);
            if (success) {
                this.removeRecord(record);
            }
            return success;
        }
    });
}

// 批量删除记录
async deleteRecords(records) {
    return this.model.mutex.exec(async () => {
        const results = [];
        
        for (const record of records) {
            const success = await this.deleteRecord(record);
            results.push({ record, success });
        }
        
        return results;
    });
}
```

**记录操作功能**:
- **添加记录**: 支持添加新记录和已存在记录
- **移除记录**: 从列表中移除记录
- **删除记录**: 删除记录（区分新记录和已保存记录）
- **批量操作**: 支持批量删除操作

### 2. 记录查找和选择

```javascript
// 根据ID查找记录
findRecord(resId) {
    return this.records.find(record => record.resId === resId);
}

// 根据条件查找记录
findRecords(predicate) {
    return this.records.filter(predicate);
}

// 获取记录索引
getRecordIndex(record) {
    return this.records.indexOf(record);
}

// 选择记录
selectRecord(record, selected = true) {
    if (record) {
        record.selected = selected;
    }
}

// 选择多个记录
selectRecords(records, selected = true) {
    records.forEach(record => this.selectRecord(record, selected));
}

// 全选/取消全选
selectAll(selected = true) {
    this.selectRecords(this.records, selected);
}

// 切换记录选择状态
toggleRecordSelection(record) {
    this.selectRecord(record, !record.selected);
}

// 清除所有选择
clearSelection() {
    this.selectAll(false);
}

// 获取选中记录的ID
getSelectedIds() {
    return this.selectedRecords.map(record => record.resId).filter(Boolean);
}
```

**查找选择功能**:
- **记录查找**: 支持按ID和条件查找记录
- **选择管理**: 完整的记录选择管理功能
- **批量选择**: 支持批量选择操作
- **状态跟踪**: 跟踪记录的选择状态

### 3. 记录排序和移动

```javascript
// 移动记录位置
moveRecord(record, newIndex) {
    const currentIndex = this.getRecordIndex(record);
    if (currentIndex !== -1 && currentIndex !== newIndex) {
        // 从当前位置移除
        this.records.splice(currentIndex, 1);
        
        // 插入到新位置
        const insertIndex = newIndex > currentIndex ? newIndex - 1 : newIndex;
        this.records.splice(insertIndex, 0, record);
        
        return true;
    }
    return false;
}

// 交换两个记录的位置
swapRecords(record1, record2) {
    const index1 = this.getRecordIndex(record1);
    const index2 = this.getRecordIndex(record2);
    
    if (index1 !== -1 && index2 !== -1) {
        this.records[index1] = record2;
        this.records[index2] = record1;
        return true;
    }
    return false;
}

// 按字段排序记录
sortRecords(fieldName, ascending = true) {
    this.records.sort((a, b) => {
        const valueA = a.data[fieldName];
        const valueB = b.data[fieldName];
        
        if (valueA < valueB) return ascending ? -1 : 1;
        if (valueA > valueB) return ascending ? 1 : -1;
        return 0;
    });
}

// 自定义排序
sortRecordsBy(compareFn) {
    this.records.sort(compareFn);
}
```

**排序移动功能**:
- **位置移动**: 移动记录到指定位置
- **位置交换**: 交换两个记录的位置
- **字段排序**: 按字段值排序记录
- **自定义排序**: 支持自定义排序函数

### 4. 批量操作

```javascript
// 保存所有记录
async saveAll() {
    return this.model.mutex.exec(async () => {
        const dirtyRecords = this.records.filter(record => record.isDirty);
        const results = [];
        
        for (const record of dirtyRecords) {
            try {
                const success = await record.save();
                results.push({ record, success, error: null });
            } catch (error) {
                results.push({ record, success: false, error });
            }
        }
        
        return results;
    });
}

// 验证所有记录
validateAll() {
    const results = [];
    
    for (const record of this.records) {
        const isValid = record.checkValidity({ silent: true });
        results.push({ record, isValid, errors: record.invalidFields });
    }
    
    return results;
}

// 重置所有记录
discardAll() {
    this.records.forEach(record => {
        if (record.isDirty) {
            record.discard();
        }
    });
}

// 批量更新记录
async updateRecords(updates) {
    return this.model.mutex.exec(async () => {
        const results = [];
        
        for (const { record, changes } of updates) {
            try {
                await record.update(changes);
                results.push({ record, success: true, error: null });
            } catch (error) {
                results.push({ record, success: false, error });
            }
        }
        
        return results;
    });
}

// 复制记录
async duplicateRecord(record) {
    return this.model.mutex.exec(async () => {
        const recordData = { ...record.data };
        
        // 移除不应复制的字段
        delete recordData.id;
        delete recordData.create_date;
        delete recordData.write_date;
        delete recordData.create_uid;
        delete recordData.write_uid;
        
        // 为复制记录添加标识
        if (recordData.name) {
            recordData.name = `${recordData.name} (Copy)`;
        }
        
        const newRecord = await this.createRecord(recordData);
        return newRecord;
    });
}
```

**批量操作功能**:
- **批量保存**: 保存所有有变更的记录
- **批量验证**: 验证所有记录的有效性
- **批量重置**: 重置所有记录的变更
- **批量更新**: 批量更新多个记录
- **记录复制**: 复制记录并创建副本

## 使用场景

### 1. 列表视图记录管理

```javascript
// 列表视图记录管理器
class ListViewRecordManager {
    constructor(model, config) {
        this.model = model;
        this.recordList = new DynamicRecordList(model, config);
        this.editingRecord = null;
        this.selectionMode = 'multiple'; // 'single', 'multiple', 'none'
    }
    
    async loadRecords(domain = [], orderBy = []) {
        const data = await this.model._searchAndRead({
            resModel: this.recordList.resModel,
            domain,
            orderBy,
            limit: this.recordList.config.limit,
            offset: this.recordList.config.offset,
            fields: Object.keys(this.recordList.activeFields),
            context: this.recordList.context
        });
        
        this.recordList._setData({ records: data, count: data.length });
        return this.recordList.records;
    }
    
    async createRecord(values = {}) {
        const record = await this.recordList.createRecord(values);
        await this.editRecord(record);
        return record;
    }
    
    async editRecord(record) {
        // 保存当前编辑的记录
        if (this.editingRecord && this.editingRecord !== record) {
            await this.saveRecord(this.editingRecord);
        }
        
        this.editingRecord = record;
        await record.switchMode('edit');
        return record;
    }
    
    async saveRecord(record = null) {
        const targetRecord = record || this.editingRecord;
        if (!targetRecord) return false;
        
        try {
            const success = await targetRecord.save();
            if (success && targetRecord === this.editingRecord) {
                this.editingRecord = null;
                await targetRecord.switchMode('readonly');
            }
            return success;
        } catch (error) {
            console.error('Save failed:', error);
            return false;
        }
    }
    
    async deleteSelectedRecords() {
        const selectedRecords = this.recordList.selectedRecords;
        if (selectedRecords.length === 0) return false;
        
        const confirmed = await this.confirmDelete(selectedRecords);
        if (!confirmed) return false;
        
        const results = await this.recordList.deleteRecords(selectedRecords);
        const successCount = results.filter(r => r.success).length;
        
        this.recordList.clearSelection();
        
        return {
            total: selectedRecords.length,
            success: successCount,
            failed: selectedRecords.length - successCount
        };
    }
    
    async confirmDelete(records) {
        return new Promise((resolve) => {
            const message = records.length === 1 
                ? 'Are you sure you want to delete this record?'
                : `Are you sure you want to delete ${records.length} records?`;
                
            this.model.env.services.dialog.add(ConfirmationDialog, {
                title: 'Delete Records',
                body: message,
                confirm: () => resolve(true),
                cancel: () => resolve(false)
            });
        });
    }
    
    selectRecord(record, selected = true) {
        if (this.selectionMode === 'none') return;
        
        if (this.selectionMode === 'single' && selected) {
            this.recordList.clearSelection();
        }
        
        this.recordList.selectRecord(record, selected);
    }
    
    selectAll(selected = true) {
        if (this.selectionMode !== 'multiple') return;
        this.recordList.selectAll(selected);
    }
    
    async duplicateRecord(record) {
        const newRecord = await this.recordList.duplicateRecord(record);
        await this.editRecord(newRecord);
        return newRecord;
    }
    
    async reorderRecord(record, newIndex) {
        const moved = this.recordList.moveRecord(record, newIndex);
        if (moved && this.recordList.handleField) {
            // 如果有序列字段，更新序列值
            await this.updateSequences();
        }
        return moved;
    }
    
    async updateSequences() {
        const updates = this.recordList.records.map((record, index) => ({
            record,
            changes: { [this.recordList.handleField]: index + 1 }
        }));
        
        return this.recordList.updateRecords(updates);
    }
    
    getViewState() {
        return {
            records: this.recordList.records,
            count: this.recordList.count,
            hasData: this.recordList.hasData,
            selectedCount: this.recordList.selectedRecords.length,
            editingRecord: this.editingRecord,
            isDirty: this.recordList.isDirty,
            canSave: this.recordList.canSave,
            hasInvalidRecords: this.recordList.hasInvalidRecords
        };
    }
    
    async exportRecords(format = 'xlsx') {
        const recordIds = this.recordList.getSelectedIds();
        
        return this.model.exportData({
            resModel: this.recordList.resModel,
            resIds: recordIds.length > 0 ? recordIds : null,
            fields: Object.keys(this.recordList.activeFields),
            format
        });
    }
}

// 使用示例
const recordManager = new ListViewRecordManager(model, {
    resModel: 'res.partner',
    activeFields: partnerActiveFields,
    fields: partnerFields,
    context: {},
    limit: 20,
    offset: 0
});

// 加载记录
await recordManager.loadRecords([['customer_rank', '>', 0]]);

// 创建记录
const newRecord = await recordManager.createRecord({ name: 'New Partner' });

// 选择记录
recordManager.selectRecord(record, true);

// 删除选中记录
await recordManager.deleteSelectedRecords();
```

### 2. 记录批量处理器

```javascript
// 记录批量处理器
class RecordBatchProcessor {
    constructor(recordList) {
        this.recordList = recordList;
        this.processingQueue = [];
        this.isProcessing = false;
    }
    
    async batchUpdate(fieldUpdates) {
        const selectedRecords = this.recordList.selectedRecords;
        if (selectedRecords.length === 0) {
            throw new Error('No records selected for batch update');
        }
        
        const updates = selectedRecords.map(record => ({
            record,
            changes: fieldUpdates
        }));
        
        return this.recordList.updateRecords(updates);
    }
    
    async batchValidate() {
        const results = this.recordList.validateAll();
        const invalidRecords = results.filter(r => !r.isValid);
        
        return {
            total: results.length,
            valid: results.length - invalidRecords.length,
            invalid: invalidRecords.length,
            invalidRecords: invalidRecords.map(r => ({
                record: r.record,
                errors: r.errors
            }))
        };
    }
    
    async batchSave() {
        const results = await this.recordList.saveAll();
        const failed = results.filter(r => !r.success);
        
        return {
            total: results.length,
            success: results.length - failed.length,
            failed: failed.length,
            errors: failed.map(r => ({
                record: r.record,
                error: r.error
            }))
        };
    }
    
    async batchDelete() {
        const selectedRecords = this.recordList.selectedRecords;
        const results = await this.recordList.deleteRecords(selectedRecords);
        
        return {
            total: selectedRecords.length,
            success: results.filter(r => r.success).length,
            failed: results.filter(r => !r.success).length
        };
    }
    
    async batchDuplicate() {
        const selectedRecords = this.recordList.selectedRecords;
        const duplicatedRecords = [];
        
        for (const record of selectedRecords) {
            try {
                const duplicate = await this.recordList.duplicateRecord(record);
                duplicatedRecords.push(duplicate);
            } catch (error) {
                console.error('Failed to duplicate record:', error);
            }
        }
        
        return duplicatedRecords;
    }
    
    async processQueue() {
        if (this.isProcessing || this.processingQueue.length === 0) {
            return;
        }
        
        this.isProcessing = true;
        
        try {
            while (this.processingQueue.length > 0) {
                const task = this.processingQueue.shift();
                await this.executeTask(task);
            }
        } finally {
            this.isProcessing = false;
        }
    }
    
    async executeTask(task) {
        try {
            switch (task.type) {
                case 'update':
                    return await this.batchUpdate(task.data);
                case 'delete':
                    return await this.batchDelete();
                case 'save':
                    return await this.batchSave();
                case 'duplicate':
                    return await this.batchDuplicate();
                default:
                    throw new Error(`Unknown task type: ${task.type}`);
            }
        } catch (error) {
            console.error(`Task execution failed:`, error);
            throw error;
        }
    }
    
    queueTask(type, data = null) {
        this.processingQueue.push({ type, data, timestamp: Date.now() });
    }
    
    clearQueue() {
        this.processingQueue = [];
    }
    
    getQueueStatus() {
        return {
            queueLength: this.processingQueue.length,
            isProcessing: this.isProcessing,
            nextTask: this.processingQueue[0] || null
        };
    }
}

// 使用示例
const batchProcessor = new RecordBatchProcessor(recordList);

// 批量更新
await batchProcessor.batchUpdate({ state: 'confirmed' });

// 批量验证
const validationResult = await batchProcessor.batchValidate();

// 队列处理
batchProcessor.queueTask('update', { priority: 'high' });
batchProcessor.queueTask('save');
await batchProcessor.processQueue();
```

## 技术特点

### 1. 高效的记录管理
- **动态操作**: 支持记录的动态添加和移除
- **状态跟踪**: 完整的记录状态跟踪
- **批量操作**: 高效的批量记录操作
- **内存优化**: 优化的内存使用和垃圾回收

### 2. 灵活的选择机制
- **多选模式**: 支持单选、多选、无选择模式
- **选择状态**: 完整的选择状态管理
- **批量选择**: 支持批量选择操作
- **选择持久化**: 选择状态的持久化

### 3. 完整的CRUD操作
- **创建记录**: 支持新记录的创建
- **读取记录**: 高效的记录读取和加载
- **更新记录**: 字段级别的记录更新
- **删除记录**: 安全的记录删除操作

### 4. 排序和重排序
- **字段排序**: 按字段值排序记录
- **自定义排序**: 支持自定义排序函数
- **拖拽重排序**: 支持拖拽重排序
- **序列管理**: 智能的序列字段管理

## 设计模式

### 1. 集合模式 (Collection Pattern)
- **记录集合**: 统一的记录集合管理
- **迭代器**: 支持记录的迭代访问
- **过滤器**: 支持记录的过滤操作

### 2. 命令模式 (Command Pattern)
- **批量操作**: 将批量操作封装为命令
- **撤销重做**: 支持操作的撤销和重做
- **队列处理**: 命令队列的处理机制

### 3. 观察者模式 (Observer Pattern)
- **状态通知**: 记录状态变化的通知
- **选择事件**: 记录选择事件的分发
- **数据同步**: 记录数据变化的同步

## 注意事项

1. **性能考虑**: 避免同时编辑过多记录
2. **内存管理**: 及时清理不再使用的记录
3. **用户体验**: 提供操作状态和进度指示
4. **数据一致性**: 确保记录数据的一致性

## 扩展建议

1. **虚拟滚动**: 支持大量记录的虚拟滚动
2. **增量加载**: 智能的增量数据加载
3. **离线支持**: 支持离线模式和数据同步
4. **实时更新**: 记录数据的实时更新
5. **高级过滤**: 提供更高级的记录过滤功能

该动态记录列表类为Odoo Web应用提供了完整的记录集合管理功能，通过高效的记录操作和智能的状态管理确保了良好的用户体验和系统性能。

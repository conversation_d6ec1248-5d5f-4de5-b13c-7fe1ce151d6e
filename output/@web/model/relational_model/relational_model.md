# Odoo 关系模型 (Relational Model) 学习资料

## 文件概述

**文件路径**: `output/@web/model/relational_model/relational_model.js`  
**原始路径**: `/web/static/src/model/relational_model/relational_model.js`  
**模块类型**: 核心模型模块 - 关系数据模型  
**代码行数**: 714 行  
**依赖关系**: 
- `@odoo/owl` - OWL框架 (EventBus, markRaw, toRaw)
- `@web/core/context` - 上下文工具 (makeContext)
- `@web/core/domain` - 域工具 (Domain)
- `@web/core/errors/error_dialogs` - 错误对话框 (WarningDialog)
- `@web/core/utils/arrays` - 数组工具 (shallowEqual)
- `@web/core/utils/concurrency` - 并发工具 (KeepLast, Mutex)
- `@web/search/utils/order_by` - 排序工具 (orderByToString)
- `@web/model/model` - 模型基类 (Model)
- 关系模型组件 (DynamicGroupList, DynamicRecordList, Group, Record, StaticList)
- 关系模型工具 (extractInfoFromGroupData, getBasicEvalContext, getFieldsSpec等)
- 关系模型错误 (FetchRecordError)

## 模块功能

关系模型模块是 Odoo Web 客户端的核心数据模型系统。该模块提供了：
- 完整的关系数据模型实现
- 记录和分组的统一管理
- 动态和静态列表支持
- 字段关系处理
- 数据缓存和同步
- 并发控制和错误处理
- 多编辑模式支持

这个模块是 Odoo 数据层的核心，为列表视图、表单视图、看板视图等提供了统一的数据模型基础。

## 关系模型架构

### 核心组件层次
```
RelationalModel (关系模型)
├── Record (记录)
│   ├── 字段数据管理
│   ├── 关系字段处理
│   └── 状态变更追踪
├── Group (分组)
│   ├── 分组数据管理
│   ├── 聚合计算
│   └── 展开/折叠状态
├── DynamicRecordList (动态记录列表)
│   ├── 分页加载
│   ├── 排序过滤
│   └── 动态数据更新
├── DynamicGroupList (动态分组列表)
│   ├── 分组层次管理
│   ├── 懒加载支持
│   └── 分组数据聚合
└── StaticList (静态列表)
    ├── 固定数据集
    ├── 本地操作
    └── 快速访问
```

### 数据流程
1. **配置初始化**: 根据配置创建模型实例
2. **数据加载**: 从服务器加载记录或分组数据
3. **关系处理**: 处理字段间的关系依赖
4. **缓存管理**: 管理数据缓存和更新策略
5. **状态同步**: 同步本地状态与服务器状态
6. **事件通知**: 通知组件数据变化

### 并发控制
- **Mutex**: 防止并发修改冲突
- **KeepLast**: 保持最新的异步操作结果
- **事务管理**: 支持数据操作的事务性
- **错误恢复**: 完善的错误处理和恢复机制

## 核心类详解

### RelationalModel - 关系模型类
```javascript
class RelationalModel extends Model {
    static services = ["action", "company", "dialog", "notification", "orm"];
    static Record = Record;
    static Group = Group;
    static DynamicRecordList = DynamicRecordList;
    static DynamicGroupList = DynamicGroupList;
    
    constructor(env, params, services) {
        super(env, params, services);
        this.config = params.config;
        this.hooks = { ...DEFAULT_HOOKS, ...params.hooks };
        this.mutex = new Mutex();
        this.keepLast = new KeepLast();
        this.root = null;
        this.data = {};
        this.nextId = 1;
    }
}
```

**功能特性**:
- **服务依赖**: 依赖多个核心服务
- **组件类型**: 定义了各种数据组件类型
- **并发控制**: 内置Mutex和KeepLast机制
- **配置管理**: 完整的配置和钩子管理
- **数据存储**: 统一的数据存储和ID管理

**使用示例**:
```javascript
// 创建关系模型实例
class ProductListModel extends Component {
    static template = xml`
        <div class="product-list-model">
            <div t-if="model.root" class="data-container">
                <div t-if="model.root.isGrouped" class="grouped-view">
                    <div t-foreach="model.root.groups" t-as="group" t-key="group.id"
                         class="group-container">
                        <div class="group-header" t-on-click="() => this.toggleGroup(group)">
                            <i t-att-class="group.isFolded ? 'fa fa-caret-right' : 'fa fa-caret-down'" />
                            <span t-esc="group.displayName" />
                            <span class="group-count">(<span t-esc="group.count" />)</span>
                        </div>
                        <div t-if="!group.isFolded" class="group-records">
                            <div t-foreach="group.records" t-as="record" t-key="record.id"
                                 class="record-item">
                                <span t-esc="record.data.name" />
                                <span t-esc="record.data.list_price" />
                            </div>
                        </div>
                    </div>
                </div>
                
                <div t-else="" class="list-view">
                    <div t-foreach="model.root.records" t-as="record" t-key="record.id"
                         class="record-item">
                        <span t-esc="record.data.name" />
                        <span t-esc="record.data.list_price" />
                        <button t-on-click="() => this.editRecord(record)" 
                                class="btn btn-sm btn-primary">
                            编辑
                        </button>
                    </div>
                </div>
            </div>
            
            <div t-if="model.root && model.root.hasMore" class="load-more">
                <button t-on-click="loadMore" class="btn btn-outline-primary">
                    加载更多
                </button>
            </div>
        </div>
    `;
    
    setup() {
        // 配置关系模型
        const config = {
            resModel: 'product.product',
            fields: {
                id: { type: 'integer', string: 'ID' },
                name: { type: 'char', string: 'Name' },
                list_price: { type: 'float', string: 'Price' },
                category_id: { type: 'many2one', relation: 'product.category', string: 'Category' },
                tag_ids: { type: 'many2many', relation: 'product.tag', string: 'Tags' }
            },
            activeFields: {
                name: { domain: "[]", context: "{}" },
                list_price: { domain: "[]", context: "{}" },
                category_id: { 
                    domain: "[]", 
                    context: "{}",
                    related: {
                        fields: { name: { type: 'char' } },
                        activeFields: { name: { domain: "[]", context: "{}" } }
                    }
                },
                tag_ids: {
                    domain: "[]",
                    context: "{}",
                    related: {
                        fields: { name: { type: 'char' } },
                        activeFields: { name: { domain: "[]", context: "{}" } }
                    }
                }
            }
        };
        
        // 创建模型实例
        const modelServices = {
            orm: useService("orm"),
            action: useService("action"),
            dialog: useService("dialog"),
            notification: useService("notification"),
            company: useService("company")
        };
        
        this.model = useState(new RelationalModel(this.env, {
            config: config,
            hooks: {
                onRecordChanged: this.onRecordChanged.bind(this),
                onRecordSaved: this.onRecordSaved.bind(this),
                onWillSaveRecord: this.onWillSaveRecord.bind(this)
            }
        }, modelServices));
        
        onWillStart(async () => {
            await this.loadData();
        });
    }
    
    async loadData() {
        const searchParams = {
            domain: [['active', '=', true]],
            context: {},
            groupBy: this.props.groupBy || [],
            orderBy: [{ name: 'name', asc: true }],
            limit: 20,
            offset: 0
        };
        
        await this.model.load(searchParams);
    }
    
    async toggleGroup(group) {
        if (group.isFolded) {
            await group.unfold();
        } else {
            group.fold();
        }
    }
    
    async loadMore() {
        if (this.model.root && this.model.root.hasMore) {
            await this.model.root.loadMore();
        }
    }
    
    async editRecord(record) {
        // 进入编辑模式
        record.switchMode('edit');
        
        // 或者打开编辑对话框
        this.env.services.action.doAction({
            type: 'ir.actions.act_window',
            res_model: 'product.product',
            res_id: record.resId,
            views: [[false, 'form']],
            target: 'new'
        });
    }
    
    onRecordChanged(record) {
        console.log('记录已变更:', record.data);
    }
    
    onRecordSaved(record) {
        console.log('记录已保存:', record.data);
        this.env.services.notification.add('记录保存成功', { type: 'success' });
    }
    
    onWillSaveRecord(record) {
        console.log('即将保存记录:', record.data);
        // 可以在这里进行保存前的验证
        return true;
    }
}

// 带搜索功能的关系模型
class SearchableProductModel extends Component {
    static template = xml`
        <div class="searchable-product-model">
            <div class="search-controls">
                <div class="search-bar">
                    <input type="text" 
                           t-model="searchQuery" 
                           t-on-input="onSearchInput"
                           placeholder="搜索产品..."
                           class="form-control" />
                </div>
                
                <div class="filter-controls">
                    <select t-model="selectedCategory" 
                            t-on-change="onCategoryChange"
                            class="form-control">
                        <option value="">所有分类</option>
                        <option t-foreach="categories" t-as="category" 
                                t-key="category.id" t-att-value="category.id">
                            <span t-esc="category.name" />
                        </option>
                    </select>
                </div>
                
                <div class="sort-controls">
                    <select t-model="sortField" 
                            t-on-change="onSortChange"
                            class="form-control">
                        <option value="name">按名称排序</option>
                        <option value="list_price">按价格排序</option>
                        <option value="create_date">按创建时间排序</option>
                    </select>
                    <button t-on-click="toggleSortOrder" 
                            class="btn btn-outline-secondary">
                        <i t-att-class="sortAsc ? 'fa fa-sort-asc' : 'fa fa-sort-desc'" />
                    </button>
                </div>
            </div>
            
            <div class="results-info">
                <span t-if="model.root">
                    找到 <span t-esc="model.root.count" /> 个产品
                </span>
            </div>
            
            <div class="product-grid">
                <div t-if="model.root && model.root.records" 
                     t-foreach="model.root.records" t-as="record" t-key="record.id"
                     class="product-card">
                    <h4 t-esc="record.data.name" />
                    <p class="price">¥<span t-esc="record.data.list_price" /></p>
                    <p class="category" t-if="record.data.category_id">
                        分类: <span t-esc="record.data.category_id.display_name" />
                    </p>
                    <div class="tags" t-if="record.data.tag_ids">
                        <span t-foreach="record.data.tag_ids" t-as="tag" t-key="tag.id"
                              class="badge badge-secondary" t-esc="tag.display_name" />
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setup() {
        this.searchQuery = useState('');
        this.selectedCategory = useState('');
        this.sortField = useState('name');
        this.sortAsc = useState(true);
        this.categories = useState([]);
        
        // 配置关系模型
        const config = {
            resModel: 'product.product',
            fields: this.getFieldsConfig(),
            activeFields: this.getActiveFieldsConfig()
        };
        
        this.model = useState(new RelationalModel(this.env, {
            config: config,
            hooks: {
                onRecordChanged: this.onRecordChanged.bind(this)
            }
        }, this.getModelServices()));
        
        onWillStart(async () => {
            await this.loadCategories();
            await this.loadData();
        });
    }
    
    getFieldsConfig() {
        return {
            id: { type: 'integer', string: 'ID' },
            name: { type: 'char', string: 'Name' },
            list_price: { type: 'float', string: 'Price' },
            category_id: { type: 'many2one', relation: 'product.category', string: 'Category' },
            tag_ids: { type: 'many2many', relation: 'product.tag', string: 'Tags' },
            create_date: { type: 'datetime', string: 'Created' }
        };
    }
    
    getActiveFieldsConfig() {
        return {
            name: { domain: "[]", context: "{}" },
            list_price: { domain: "[]", context: "{}" },
            category_id: { 
                domain: "[]", 
                context: "{}",
                related: {
                    fields: { name: { type: 'char' } },
                    activeFields: { name: { domain: "[]", context: "{}" } }
                }
            },
            tag_ids: {
                domain: "[]",
                context: "{}",
                related: {
                    fields: { name: { type: 'char' } },
                    activeFields: { name: { domain: "[]", context: "{}" } }
                }
            },
            create_date: { domain: "[]", context: "{}" }
        };
    }
    
    getModelServices() {
        return {
            orm: useService("orm"),
            action: useService("action"),
            dialog: useService("dialog"),
            notification: useService("notification"),
            company: useService("company")
        };
    }
    
    async loadCategories() {
        const orm = useService("orm");
        this.categories = await orm.searchRead(
            'product.category',
            [],
            ['name'],
            { order: 'name ASC' }
        );
    }
    
    async loadData() {
        const domain = this.buildDomain();
        const orderBy = this.buildOrderBy();
        
        const searchParams = {
            domain: domain,
            context: {},
            groupBy: [],
            orderBy: orderBy,
            limit: 20,
            offset: 0
        };
        
        await this.model.load(searchParams);
    }
    
    buildDomain() {
        const domain = [['active', '=', true]];
        
        if (this.searchQuery) {
            domain.push(['name', 'ilike', this.searchQuery]);
        }
        
        if (this.selectedCategory) {
            domain.push(['category_id', '=', parseInt(this.selectedCategory)]);
        }
        
        return domain;
    }
    
    buildOrderBy() {
        return [{
            name: this.sortField,
            asc: this.sortAsc
        }];
    }
    
    async onSearchInput() {
        // 防抖处理
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.loadData();
        }, 300);
    }
    
    async onCategoryChange() {
        await this.loadData();
    }
    
    async onSortChange() {
        await this.loadData();
    }
    
    async toggleSortOrder() {
        this.sortAsc = !this.sortAsc;
        await this.loadData();
    }
    
    onRecordChanged(record) {
        console.log('记录已变更:', record.data);
    }
}
```

## 核心方法详解

### load() - 数据加载方法
```javascript
async load(params = {}) {
    const { domain, context, groupBy, orderBy, limit, offset } = params;

    return this.keepLast.add(this.mutex.exec(async () => {
        const config = this._getNextConfig(this.config, params);

        if (config.groupBy && config.groupBy.length) {
            // 加载分组数据
            this.root = await this._loadGroups(config);
        } else {
            // 加载记录列表
            this.root = await this._loadRecords(config);
        }

        this.config = config;
        this.notify();
    }));
}
```

**功能特性**:
- **并发控制**: 使用Mutex和KeepLast确保操作的原子性
- **配置更新**: 动态更新模型配置
- **分支加载**: 根据是否分组选择不同的加载策略
- **状态通知**: 加载完成后通知组件更新

### save() - 数据保存方法
```javascript
async save(record, options = {}) {
    const { stayInEdition = false, reload = true } = options;

    return this.mutex.exec(async () => {
        try {
            // 调用保存前钩子
            const canSave = await this.hooks.onWillSaveRecord(record);
            if (!canSave) {
                return false;
            }

            // 执行保存操作
            const result = await record.save();

            // 调用保存后钩子
            await this.hooks.onRecordSaved(record);

            if (!stayInEdition) {
                record.switchMode('readonly');
            }

            if (reload) {
                await this.reload();
            }

            return result;

        } catch (error) {
            console.error('保存失败:', error);
            throw error;
        }
    });
}
```

**功能特性**:
- **钩子支持**: 支持保存前后的钩子函数
- **模式切换**: 保存后可选择是否切换到只读模式
- **重新加载**: 保存后可选择是否重新加载数据
- **错误处理**: 完善的错误处理机制

### create() - 记录创建方法
```javascript
async create(values = {}, options = {}) {
    const { save = true, position = 'top' } = options;

    return this.mutex.exec(async () => {
        // 创建新记录
        const record = this._createRecord(values);

        // 添加到列表
        if (this.root.records) {
            if (position === 'top') {
                this.root.records.unshift(record);
            } else {
                this.root.records.push(record);
            }
        }

        // 自动保存
        if (save) {
            await this.save(record);
        }

        this.notify();
        return record;
    });
}
```

**功能特性**:
- **值初始化**: 支持初始值设置
- **位置控制**: 可控制新记录在列表中的位置
- **自动保存**: 可选择是否自动保存新记录
- **列表更新**: 自动更新记录列表

## 最佳实践

### 1. 性能优化
```javascript
// ✅ 推荐：合理配置字段规格
const optimizedConfig = {
    resModel: 'product.product',
    fields: productFields,
    activeFields: {
        // 只加载必要的字段
        name: { domain: "[]", context: "{}" },
        list_price: { domain: "[]", context: "{}" },
        // 关系字段只加载显示名称
        category_id: {
            domain: "[]",
            context: "{}",
            related: {
                fields: { display_name: { type: 'char' } },
                activeFields: { display_name: { domain: "[]", context: "{}" } }
            }
        }
    }
};
```

### 2. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
class RobustRelationalModel extends RelationalModel {
    async load(params) {
        try {
            return await super.load(params);
        } catch (error) {
            if (error instanceof FetchRecordError) {
                this.handleFetchError(error);
            } else {
                this.handleGenericError(error);
            }
            throw error;
        }
    }

    handleFetchError(error) {
        console.error('数据获取失败:', error);
        this.env.services.notification.add(
            '数据加载失败，请稍后重试',
            { type: 'danger' }
        );
    }
}
```

## 总结

Odoo 关系模型模块提供了强大的关系数据管理能力：

**核心优势**:
- **统一接口**: 为所有视图类型提供统一的数据模型接口
- **关系处理**: 完整的关系字段处理和数据预加载
- **并发控制**: 内置的并发控制和事务管理
- **性能优化**: 智能的数据缓存和懒加载机制
- **扩展性**: 灵活的钩子系统和配置选项

**适用场景**:
- 列表视图数据管理
- 表单视图记录处理
- 看板视图卡片数据
- 分组和聚合显示
- 多编辑批量操作

**设计优势**:
- 模块化组件架构
- 事件驱动的响应式设计
- 完整的生命周期管理
- 强大的错误处理机制

这个关系模型为 Odoo Web 客户端提供了强大的数据层基础，是构建复杂数据驱动应用的核心组件。

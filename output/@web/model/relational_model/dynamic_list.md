# DynamicList - 动态列表数据点

## 概述

`dynamic_list.js` 是 Odoo Web 关系模型的动态列表数据点类，提供了基于搜索条件的动态记录列表管理功能。该模块包含389行代码，继承自DataPoint基类，专门处理搜索视图、列表视图等场景中的记录集合，支持域过滤、排序、分页、记录操作等功能，具备动态加载、搜索过滤、批量操作、重排序等特性，是Odoo Web中处理动态记录集合的核心组件。

## 文件信息
- **路径**: `/web/static/src/model/relational_model/dynamic_list.js`
- **行数**: 389
- **模块**: `@web/model/relational_model/dynamic_list`

## 依赖关系

```javascript
// 核心依赖
'@web/core/confirmation_dialog/confirmation_dialog' // 确认对话框
'@web/core/l10n/translation'                       // 国际化翻译
'@web/model/relational_model/datapoint'            // 数据点基类
'@web/model/relational_model/record'                // 记录类
'@web/model/relational_model/utils'                 // 关系模型工具
```

## 常量定义

```javascript
const DEFAULT_HANDLE_FIELD = "sequence";
```

## 主类定义

### 1. DynamicList 类

```javascript
class DynamicList extends DataPoint {
    setup(config) {
        super.setup(...arguments);
        
        // 查找处理字段（用于重排序）
        this.handleField = Object.keys(this.activeFields).find(
            (fieldName) => this.activeFields[fieldName].isHandle
        );
        
        // 如果没有找到处理字段，使用默认的sequence字段
        if (!this.handleField && DEFAULT_HANDLE_FIELD in this.fields) {
            this.handleField = DEFAULT_HANDLE_FIELD;
        }
        
        this.isDomainSelected = false;
        this.evalContext = this.context;
    }
}
```

**类特性**:
- **继承DataPoint**: 继承数据点基类的基础功能
- **动态加载**: 基于搜索条件动态加载记录
- **处理字段**: 支持拖拽重排序的处理字段
- **域选择**: 支持域的选择和过滤

## 核心属性

### 1. 基础属性

```javascript
// 获取器属性
get groupBy() { return []; }
get orderBy() { return this.config.orderBy; }
get domain() { return this.config.domain; }

// 记录相关属性
get records() {
    // 返回当前加载的记录列表
    return this._records || [];
}

get count() {
    // 返回符合条件的记录总数
    return this._count || 0;
}

get hasData() {
    // 检查是否有数据
    return this.records.length > 0;
}

get isGrouped() {
    // 检查是否分组显示
    return this.groupBy.length > 0;
}
```

**属性功能**:
- **groupBy**: 分组字段（动态列表通常不分组）
- **orderBy**: 排序规则
- **domain**: 搜索域条件
- **records**: 当前页面的记录列表
- **count**: 符合条件的记录总数

### 2. 状态属性

```javascript
get isLoading() {
    return this._isLoading || false;
}

get hasMore() {
    return this.records.length < this.count;
}

get canResequence() {
    return Boolean(this.handleField) && this.orderBy.length > 0 
           && this.orderBy[0].name === this.handleField;
}
```

**状态属性功能**:
- **isLoading**: 是否正在加载数据
- **hasMore**: 是否还有更多记录可加载
- **canResequence**: 是否可以重排序

## 核心方法

### 1. 数据加载

```javascript
// 加载记录
async load(params = {}) {
    return this.model.mutex.exec(async () => {
        this._isLoading = true;
        
        try {
            const { domain, orderBy, limit, offset } = {
                domain: this.domain,
                orderBy: this.orderBy,
                limit: this.config.limit,
                offset: this.config.offset,
                ...params
            };
            
            // 搜索记录ID
            const resIds = await this.model._searchRecords({
                resModel: this.resModel,
                domain,
                orderBy,
                limit,
                offset,
                context: this.context
            });
            
            // 加载记录数据
            const recordsData = await this.model._loadRecords({
                resModel: this.resModel,
                resIds,
                activeFields: this.activeFields,
                fields: this.fields,
                context: this.context
            });
            
            // 创建记录数据点
            this._records = recordsData.map(data => 
                this.model._createRecordDatapoint(data, {
                    activeFields: this.activeFields,
                    fields: this.fields,
                    context: this.context,
                    parentList: this
                })
            );
            
            // 获取总数
            this._count = await this.model._getRecordCount({
                resModel: this.resModel,
                domain,
                context: this.context
            });
            
            return this._records;
        } finally {
            this._isLoading = false;
        }
    });
}

// 重新加载
async reload() {
    return this.load();
}

// 加载更多记录
async loadMore(limit = null) {
    if (!this.hasMore) {
        return [];
    }
    
    const currentOffset = this.records.length;
    const loadLimit = limit || this.config.limit;
    
    const newRecords = await this.load({
        offset: currentOffset,
        limit: loadLimit
    });
    
    // 合并记录
    this._records = [...this.records, ...newRecords];
    
    return newRecords;
}
```

**加载功能**:
- **搜索加载**: 基于域条件搜索和加载记录
- **分页支持**: 支持分页加载和无限滚动
- **异步安全**: 使用mutex确保加载操作的原子性
- **状态管理**: 管理加载状态和记录计数

### 2. 记录操作

```javascript
// 创建新记录
async createRecord(values = {}) {
    return this.model.mutex.exec(async () => {
        const record = await this.model._createRecord({
            resModel: this.resModel,
            values,
            activeFields: this.activeFields,
            fields: this.fields,
            context: this.context
        });
        
        // 添加到列表开头
        this._records.unshift(record);
        this._count++;
        
        return record;
    });
}

// 删除记录
async deleteRecord(record) {
    return this.model.mutex.exec(async () => {
        const confirmed = await this._confirmDelete(record);
        if (!confirmed) {
            return false;
        }
        
        await this.model._deleteRecord(record);
        
        // 从列表中移除
        const index = this._records.indexOf(record);
        if (index !== -1) {
            this._records.splice(index, 1);
            this._count--;
        }
        
        return true;
    });
}

// 批量删除记录
async deleteRecords(records) {
    return this.model.mutex.exec(async () => {
        const confirmed = await this._confirmBatchDelete(records);
        if (!confirmed) {
            return false;
        }
        
        await this.model._deleteRecords(records);
        
        // 从列表中移除
        for (const record of records) {
            const index = this._records.indexOf(record);
            if (index !== -1) {
                this._records.splice(index, 1);
                this._count--;
            }
        }
        
        return true;
    });
}

// 确认删除对话框
async _confirmDelete(record) {
    return new Promise((resolve) => {
        this.model.env.services.dialog.add(AlertDialog, {
            title: _t("Delete Record"),
            body: _t("Are you sure you want to delete this record?"),
            confirm: () => resolve(true),
            cancel: () => resolve(false),
        });
    });
}

// 确认批量删除对话框
async _confirmBatchDelete(records) {
    return new Promise((resolve) => {
        this.model.env.services.dialog.add(AlertDialog, {
            title: _t("Delete Records"),
            body: _t("Are you sure you want to delete %s records?", records.length),
            confirm: () => resolve(true),
            cancel: () => resolve(false),
        });
    });
}
```

**记录操作功能**:
- **创建记录**: 创建新记录并添加到列表
- **删除记录**: 删除单个或多个记录
- **确认机制**: 删除前的用户确认机制
- **状态同步**: 操作后同步列表状态

### 3. 搜索和过滤

```javascript
// 应用搜索域
async applyDomain(domain) {
    return this.model.mutex.exec(async () => {
        this.config.domain = domain;
        this.config.offset = 0; // 重置偏移量
        
        await this.load();
        return this.records;
    });
}

// 添加过滤条件
async addFilter(filter) {
    const currentDomain = this.domain || [];
    const newDomain = [...currentDomain, filter];
    
    return this.applyDomain(newDomain);
}

// 移除过滤条件
async removeFilter(filterToRemove) {
    const currentDomain = this.domain || [];
    const newDomain = currentDomain.filter(filter => 
        JSON.stringify(filter) !== JSON.stringify(filterToRemove)
    );
    
    return this.applyDomain(newDomain);
}

// 清除所有过滤条件
async clearFilters() {
    return this.applyDomain([]);
}

// 搜索文本
async searchText(searchText) {
    if (!searchText) {
        return this.clearFilters();
    }
    
    // 构建文本搜索域
    const searchDomain = this._buildTextSearchDomain(searchText);
    return this.applyDomain(searchDomain);
}

// 构建文本搜索域
_buildTextSearchDomain(searchText) {
    const searchableFields = Object.keys(this.fields).filter(fieldName => {
        const field = this.fields[fieldName];
        return ['char', 'text', 'html'].includes(field.type);
    });
    
    if (searchableFields.length === 0) {
        return [];
    }
    
    const searchConditions = searchableFields.map(fieldName => 
        [fieldName, 'ilike', searchText]
    );
    
    // 使用OR连接所有搜索条件
    return ['|'].concat(...searchConditions.map((condition, index) => 
        index === 0 ? [condition] : ['|', condition]
    ));
}
```

**搜索过滤功能**:
- **域应用**: 应用搜索域并重新加载数据
- **过滤管理**: 添加、移除、清除过滤条件
- **文本搜索**: 全文搜索功能
- **域构建**: 智能构建搜索域表达式

### 4. 排序和重排序

```javascript
// 应用排序
async applyOrderBy(orderBy) {
    return this.model.mutex.exec(async () => {
        this.config.orderBy = orderBy;
        this.config.offset = 0; // 重置偏移量
        
        await this.load();
        return this.records;
    });
}

// 按字段排序
async sortBy(fieldName, ascending = true) {
    const orderBy = [{ name: fieldName, asc: ascending }];
    return this.applyOrderBy(orderBy);
}

// 重排序记录
async resequence(recordId, targetId, position = 'before') {
    if (!this.canResequence) {
        throw new Error("Cannot resequence: no handle field or wrong order");
    }
    
    return this.model.mutex.exec(async () => {
        // 调用重排序工具函数
        await resequence(this.model, {
            resModel: this.resModel,
            resIds: this.records.map(r => r.resId),
            handleField: this.handleField,
            movedId: recordId,
            targetId,
            position
        });
        
        // 重新加载以获取新的序列
        await this.reload();
    });
}

// 移动记录到指定位置
async moveRecord(record, targetIndex) {
    if (!this.canResequence) {
        // 如果不能重排序，只在客户端移动
        const currentIndex = this.records.indexOf(record);
        if (currentIndex !== -1) {
            this._records.splice(currentIndex, 1);
            this._records.splice(targetIndex, 0, record);
        }
        return;
    }
    
    const targetRecord = this.records[targetIndex];
    if (targetRecord) {
        await this.resequence(record.resId, targetRecord.resId);
    }
}
```

**排序功能**:
- **排序应用**: 应用新的排序规则
- **字段排序**: 按指定字段排序
- **拖拽重排序**: 支持拖拽重排序
- **位置移动**: 移动记录到指定位置

## 使用场景

### 1. 搜索视图数据管理

```javascript
// 搜索视图数据管理器
class SearchViewDataManager {
    constructor(model, config) {
        this.model = model;
        this.dynamicList = new DynamicList(model, config);
        this.searchState = {
            domain: [],
            orderBy: [],
            searchText: '',
            filters: new Map(),
            groupBy: []
        };
    }
    
    async initialize() {
        await this.dynamicList.load();
        return this.dynamicList.records;
    }
    
    async search(searchText) {
        this.searchState.searchText = searchText;
        await this.dynamicList.searchText(searchText);
        return this.dynamicList.records;
    }
    
    async addFilter(name, domain) {
        this.searchState.filters.set(name, domain);
        await this._applyAllFilters();
        return this.dynamicList.records;
    }
    
    async removeFilter(name) {
        this.searchState.filters.delete(name);
        await this._applyAllFilters();
        return this.dynamicList.records;
    }
    
    async _applyAllFilters() {
        let combinedDomain = [];
        
        // 添加文本搜索
        if (this.searchState.searchText) {
            const textDomain = this.dynamicList._buildTextSearchDomain(
                this.searchState.searchText
            );
            combinedDomain = [...combinedDomain, ...textDomain];
        }
        
        // 添加所有过滤器
        for (const filterDomain of this.searchState.filters.values()) {
            combinedDomain = [...combinedDomain, '&', ...filterDomain];
        }
        
        await this.dynamicList.applyDomain(combinedDomain);
    }
    
    async sort(fieldName, ascending = true) {
        this.searchState.orderBy = [{ name: fieldName, asc: ascending }];
        await this.dynamicList.applyOrderBy(this.searchState.orderBy);
        return this.dynamicList.records;
    }
    
    async loadMore() {
        return this.dynamicList.loadMore();
    }
    
    async refresh() {
        await this.dynamicList.reload();
        return this.dynamicList.records;
    }
    
    getSearchState() {
        return {
            ...this.searchState,
            recordCount: this.dynamicList.count,
            hasMore: this.dynamicList.hasMore,
            isLoading: this.dynamicList.isLoading
        };
    }
}

// 使用示例
const searchManager = new SearchViewDataManager(model, {
    resModel: 'res.partner',
    domain: [],
    orderBy: [{ name: 'name', asc: true }],
    limit: 20,
    offset: 0,
    activeFields: partnerActiveFields,
    fields: partnerFields,
    context: {}
});

// 初始化
await searchManager.initialize();

// 搜索
await searchManager.search('John');

// 添加过滤器
await searchManager.addFilter('customers', [['customer_rank', '>', 0]]);

// 排序
await searchManager.sort('create_date', false);
```

### 2. 列表视图控制器

```javascript
// 列表视图控制器
class ListViewController {
    constructor(model, config) {
        this.model = model;
        this.dynamicList = new DynamicList(model, config);
        this.selectedRecords = new Set();
        this.editingRecord = null;
    }
    
    async loadData() {
        await this.dynamicList.load();
        this.selectedRecords.clear();
        return this.getViewState();
    }
    
    async createRecord() {
        const record = await this.dynamicList.createRecord();
        await this.editRecord(record);
        return record;
    }
    
    async editRecord(record) {
        if (this.editingRecord && this.editingRecord !== record) {
            await this.saveRecord(this.editingRecord);
        }
        
        this.editingRecord = record;
        await record.switchMode('edit');
        return record;
    }
    
    async saveRecord(record) {
        if (!record) {
            record = this.editingRecord;
        }
        
        if (record) {
            const success = await record.save();
            if (success) {
                this.editingRecord = null;
                await record.switchMode('readonly');
            }
            return success;
        }
        return false;
    }
    
    async deleteSelectedRecords() {
        const recordsToDelete = Array.from(this.selectedRecords);
        if (recordsToDelete.length === 0) {
            return false;
        }
        
        const success = await this.dynamicList.deleteRecords(recordsToDelete);
        if (success) {
            this.selectedRecords.clear();
        }
        return success;
    }
    
    selectRecord(record, selected = true) {
        if (selected) {
            this.selectedRecords.add(record);
        } else {
            this.selectedRecords.delete(record);
        }
    }
    
    selectAllRecords(selected = true) {
        if (selected) {
            this.dynamicList.records.forEach(record => 
                this.selectedRecords.add(record)
            );
        } else {
            this.selectedRecords.clear();
        }
    }
    
    async reorderRecord(record, newIndex) {
        await this.dynamicList.moveRecord(record, newIndex);
        return this.getViewState();
    }
    
    async applyFilter(domain) {
        await this.dynamicList.applyDomain(domain);
        this.selectedRecords.clear();
        return this.getViewState();
    }
    
    async sortByField(fieldName) {
        const currentOrder = this.dynamicList.orderBy;
        let ascending = true;
        
        // 如果已经按此字段排序，则切换排序方向
        if (currentOrder.length > 0 && currentOrder[0].name === fieldName) {
            ascending = !currentOrder[0].asc;
        }
        
        await this.dynamicList.sortBy(fieldName, ascending);
        return this.getViewState();
    }
    
    getViewState() {
        return {
            records: this.dynamicList.records,
            count: this.dynamicList.count,
            hasMore: this.dynamicList.hasMore,
            isLoading: this.dynamicList.isLoading,
            selectedCount: this.selectedRecords.size,
            editingRecord: this.editingRecord,
            canResequence: this.dynamicList.canResequence,
            orderBy: this.dynamicList.orderBy,
            domain: this.dynamicList.domain
        };
    }
    
    async exportRecords(format = 'xlsx') {
        const recordIds = Array.from(this.selectedRecords).map(r => r.resId);
        
        if (recordIds.length === 0) {
            // 导出所有记录
            return this.model.exportData({
                resModel: this.dynamicList.resModel,
                domain: this.dynamicList.domain,
                fields: Object.keys(this.dynamicList.activeFields),
                format
            });
        } else {
            // 导出选中记录
            return this.model.exportData({
                resModel: this.dynamicList.resModel,
                resIds: recordIds,
                fields: Object.keys(this.dynamicList.activeFields),
                format
            });
        }
    }
}

// 使用示例
const listController = new ListViewController(model, listConfig);

// 加载数据
await listController.loadData();

// 创建记录
const newRecord = await listController.createRecord();

// 选择记录
listController.selectRecord(record1, true);
listController.selectRecord(record2, true);

// 删除选中记录
await listController.deleteSelectedRecords();

// 排序
await listController.sortByField('name');

// 导出
await listController.exportRecords('csv');
```

## 技术特点

### 1. 动态数据管理
- **按需加载**: 基于搜索条件动态加载数据
- **分页支持**: 支持分页和无限滚动
- **实时搜索**: 实时的搜索和过滤功能
- **状态同步**: 保持数据状态的一致性

### 2. 灵活的搜索过滤
- **域表达式**: 支持复杂的域表达式
- **多条件过滤**: 支持多个过滤条件的组合
- **文本搜索**: 智能的全文搜索功能
- **过滤管理**: 灵活的过滤条件管理

### 3. 完整的记录操作
- **CRUD操作**: 完整的创建、读取、更新、删除操作
- **批量操作**: 支持批量记录操作
- **确认机制**: 重要操作的用户确认机制
- **状态管理**: 完善的记录状态管理

### 4. 排序和重排序
- **多字段排序**: 支持多字段排序
- **拖拽重排序**: 支持拖拽重排序
- **序列管理**: 智能的序列字段管理
- **排序状态**: 保持排序状态的一致性

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- **加载策略**: 不同的数据加载策略
- **搜索策略**: 不同的搜索和过滤策略
- **排序策略**: 不同的排序策略

### 2. 观察者模式 (Observer Pattern)
- **数据变化**: 数据变化的通知机制
- **状态同步**: 状态变化的同步机制
- **事件分发**: 事件的分发和处理

### 3. 命令模式 (Command Pattern)
- **操作封装**: 将操作封装为命令
- **撤销重做**: 支持操作的撤销和重做
- **批量操作**: 批量命令的执行

## 注意事项

1. **性能优化**: 避免频繁的数据加载和DOM操作
2. **内存管理**: 及时清理不再使用的记录
3. **用户体验**: 提供加载状态和操作反馈
4. **数据一致性**: 确保客户端和服务器数据的一致性

## 扩展建议

1. **缓存机制**: 添加智能缓存机制提高性能
2. **离线支持**: 支持离线模式和数据同步
3. **虚拟滚动**: 支持大数据集的虚拟滚动
4. **实时更新**: 支持实时数据更新和推送
5. **高级搜索**: 提供更高级的搜索功能

该动态列表类为Odoo Web应用提供了完整的动态数据管理功能，通过灵活的搜索过滤和高效的数据加载确保了良好的用户体验和系统性能。

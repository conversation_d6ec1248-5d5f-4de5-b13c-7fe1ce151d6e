# StaticList - 静态列表数据点

## 概述

`static_list.js` 是 Odoo Web 关系模型的静态列表数据点类，提供了固定记录集合的管理功能。该模块包含1039行代码，继承自DataPoint基类，专门处理x2many关系字段中的记录列表，支持记录的增删改查、排序、重排序、分页等操作，具备命令模式的变更跟踪、缓存管理、异步操作等特性，是关系模型中处理一对多和多对多关系的核心组件。

## 文件信息
- **路径**: `/web/static/src/model/relational_model/static_list.js`
- **行数**: 1039
- **模块**: `@web/model/relational_model/static_list`

## 依赖关系

```javascript
// 核心依赖
'@web/core/orm_service'                      // ORM服务
'@web/core/utils/arrays'                     // 数组工具
'@web/core/utils/objects'                    // 对象工具
'@web/model/relational_model/utils'          // 关系模型工具
'@web/model/relational_model/datapoint'      // 数据点基类
'@odoo/owl'                                  // OWL框架
```

## 工具函数

### 1. 字段值比较

```javascript
function compareFieldValues(v1, v2, fieldType) {
    if (fieldType === "many2one") {
        v1 = v1 ? v1[1] : "";
        v2 = v2 ? v2[1] : "";
    }
    return v1 < v2;
}
```

**比较功能**:
- **类型感知**: 根据字段类型进行比较
- **many2one处理**: 特殊处理many2one字段的显示名称
- **空值处理**: 安全处理空值情况
- **排序支持**: 为记录排序提供基础比较

### 2. 记录比较

```javascript
function compareRecords(r1, r2, orderBy, fields) {
    const { name, asc } = orderBy[0];
    function getValue(record, fieldName) {
        return fieldName === "id" ? record.resId : record.data[fieldName];
    }
    const v1 = asc ? getValue(r1, name) : getValue(r2, name);
    const v2 = asc ? getValue(r2, name) : getValue(r1, name);

    if (compareFieldValues(v1, v2, fields[name].type)) {
        return -1;
    }
    if (compareFieldValues(v2, v1, fields[name].type)) {
        return 1;
    }
    if (orderBy.length > 1) {
        return compareRecords(r1, r2, orderBy.slice(1), fields);
    }
    return 0;
}
```

**比较功能**:
- **多字段排序**: 支持多个字段的排序规则
- **升降序**: 支持升序和降序排列
- **递归比较**: 当主要字段相等时递归比较次要字段
- **ID特殊处理**: 特殊处理记录ID字段

## 主类定义

### 1. StaticList 类

```javascript
class StaticList extends DataPoint {
    static type = "StaticList";

    setup(config, data, options = {}) {
        this._parent = options.parent;
        this._onUpdate = options.onUpdate;

        this._cache = markRaw({});
        this._commands = [];
        this._initialCommands = [];
        this._savePoint = undefined;
        this._unknownRecordCommands = {};
        this._currentIds = [...this.resIds];
        this._initialCurrentIds = [...this.currentIds];
        this._needsReordering = false;
        this._tmpIncreaseLimit = 0;
        this._extendedRecords = new Set();

        this.records = data
            .slice(this.offset, this.limit)
            .map((r) => this._createRecordDatapoint(r));
        this.count = this.resIds.length;
        this.handleField = Object.keys(this.activeFields).find(
            (fieldName) => this.activeFields[fieldName].isHandle
        );
    }
}
```

**类特性**:
- **继承DataPoint**: 继承数据点基类的基础功能
- **缓存管理**: 内置记录缓存机制
- **命令跟踪**: 跟踪所有数据变更命令
- **分页支持**: 支持分页加载和显示
- **排序功能**: 支持多字段排序和重排序

## 核心属性

### 1. 基础属性

```javascript
// 获取器属性
get currentIds() { return this._currentIds; }
get editedRecord() { return this.records.find((record) => record.isInEdition); }
get evalContext() {
    const evalContext = getBasicEvalContext(this.config);
    evalContext.parent = this._parent.evalContext;
    return evalContext;
}
get limit() { return this.config.limit; }
get offset() { return this.config.offset; }
get orderBy() { return this.config.orderBy; }
get resIds() { return this.config.resIds; }
```

**属性功能**:
- **currentIds**: 当前页面的记录ID列表
- **editedRecord**: 当前正在编辑的记录
- **evalContext**: 计算上下文，包含父记录上下文
- **limit/offset**: 分页参数
- **orderBy**: 排序规则
- **resIds**: 所有记录的ID列表

## 核心方法

### 1. 记录管理

```javascript
// 添加新记录
async addNewRecord(params) {
    return this.model.mutex.exec(async () => {
        const { activeFields, context, mode, position, withoutParent } = params;
        const record = await this._createNewRecordDatapoint({
            activeFields, context, position, withoutParent,
            manuallyAdded: true, mode,
        });
        await this._addRecord(record, { position });
        await this._onUpdate({ withoutOnchange: !record._checkValidity({ silent: true }) });
        return record;
    });
}

// 删除记录
delete(record) {
    return this.model.mutex.exec(async () => {
        await this._applyCommands([[x2ManyCommands.DELETE, record.resId || record._virtualId]]);
        await this._onUpdate();
    });
}

// 链接记录
linkTo(resId, serverData) {
    return this.model.mutex.exec(async () => {
        await this._applyCommands([[x2ManyCommands.LINK, resId, serverData]]);
        await this._onUpdate();
    });
}

// 取消链接
unlinkFrom(resId, serverData) {
    return this.model.mutex.exec(async () => {
        await this._applyCommands([[x2ManyCommands.UNLINK, resId, serverData]]);
        await this._onUpdate();
    });
}
```

**记录管理功能**:
- **异步安全**: 所有操作都在mutex中执行
- **命令模式**: 使用x2many命令进行操作
- **自动更新**: 操作后自动触发更新
- **位置控制**: 支持指定添加位置

### 2. 编辑模式管理

```javascript
// 进入编辑模式
async enterEditMode(record) {
    const canProceed = await this.leaveEditMode();
    if (canProceed) {
        await record.switchMode("edit");
    }
    return canProceed;
}

// 离开编辑模式
async leaveEditMode({ discard, canAbandon, validate } = {}) {
    if (this.editedRecord) {
        await this.model._askChanges(false);
    }
    return this.model.mutex.exec(async () => {
        if (this.editedRecord) {
            const isValid = this.editedRecord._checkValidity();
            if (!isValid && validate) {
                return false;
            }
            if (canAbandon !== false && !validate) {
                this._abandonRecords([this.editedRecord], { force: true });
            }
            if (this.editedRecord) {
                if (isValid && !this.editedRecord.dirty && discard) {
                    return false;
                }
                if (isValid || (!this.editedRecord.dirty && !this.editedRecord._manuallyAdded)) {
                    this.editedRecord._switchMode("readonly");
                }
            }
        }
        return !this.editedRecord;
    });
}
```

**编辑模式功能**:
- **单一编辑**: 确保同时只有一个记录在编辑
- **验证检查**: 编辑前后进行有效性验证
- **状态管理**: 管理记录的编辑状态
- **放弃机制**: 支持放弃无效的编辑

### 3. 记录扩展

```javascript
// 扩展记录字段
extendRecord(params, record) {
    return this.model.mutex.exec(async () => {
        // 扩展字段和activeFields
        completeActiveFields(this.config.activeFields, params.activeFields);
        Object.assign(this.fields, params.fields);

        const activeFields = { ...params.activeFields };
        for (const fieldName in this.activeFields) {
            if (fieldName in activeFields) {
                patchActiveFields(activeFields[fieldName], this.activeFields[fieldName]);
            } else {
                activeFields[fieldName] = this.activeFields[fieldName];
            }
        }

        if (record) {
            record._noUpdateParent = true;
            record._activeFieldsToRestore = { ...this.config.activeFields };
            const config = { ...record.config, ...params, activeFields };

            if (this._extendedRecords.has(record.id)) {
                // 记录已经被扩展过
                this.model._updateConfig(record.config, config, { reload: false });
                record._addSavePoint();
                return record;
            }

            // 首次扩展记录
            let data = {};
            if (!record.isNew) {
                const evalContext = Object.assign({}, record.evalContext, config.context);
                const resIds = [record.resId];
                [data] = await this.model._loadRecords({ ...config, resIds }, evalContext);
            }

            this.model._updateConfig(record.config, config, { reload: false });
            record._applyDefaultValues();

            // 处理x2many字段
            for (const fieldName in record.activeFields) {
                if (["one2many", "many2many"].includes(record.fields[fieldName].type)) {
                    const list = record.data[fieldName];
                    const patch = {
                        activeFields: activeFields[fieldName].related.activeFields,
                        fields: activeFields[fieldName].related.fields,
                    };
                    for (const subRecord of Object.values(list._cache)) {
                        this.model._updateConfig(subRecord.config, patch, { reload: false });
                    }
                    this.model._updateConfig(list.config, patch, { reload: false });
                }
            }

            record._applyValues(data);
            const commands = this._unknownRecordCommands[record.resId];
            delete this._unknownRecordCommands[record.resId];
            if (commands) {
                this._applyCommands(commands);
            }
            record._addSavePoint();
        } else {
            // 创建新记录
            record = await this._createNewRecordDatapoint({
                activeFields, context: params.context,
                withoutParent: params.withoutParent, manuallyAdded: true,
            });
            record._activeFieldsToRestore = { ...this.config.activeFields };
            record._noUpdateParent = true;
        }

        this._extendedRecords.add(record.id);
        return record;
    });
}
```

**扩展功能**:
- **字段扩展**: 动态扩展记录的可用字段
- **配置更新**: 更新记录和子记录的配置
- **数据加载**: 按需加载新字段的数据
- **状态保存**: 保存扩展前的状态用于恢复

### 4. 排序和重排序

```javascript
// 字段排序
sortBy(fieldName) {
    return this.model.mutex.exec(() => this._sortBy(fieldName));
}

// 记录重排序
async resequence(movedId, targetId) {
    return this.model.mutex.exec(() => this._resequence(movedId, targetId));
}

// 检查是否可以重排序
canResequence() {
    return this.handleField && this.orderBy.length && this.orderBy[0].name === this.handleField;
}

// 内部排序实现
async _sortBy(fieldName) {
    let orderBy = [...this.orderBy];
    if (fieldName) {
        if (orderBy.length && orderBy[0].name === fieldName) {
            if (!this._needsReordering) {
                orderBy[0] = { name: orderBy[0].name, asc: !orderBy[0].asc };
            }
        } else {
            orderBy = orderBy.filter((o) => o.name !== fieldName);
            orderBy.unshift({ name: fieldName, asc: true });
        }
    }
    return this._sort(this._currentIds, orderBy);
}
```

**排序功能**:
- **多字段排序**: 支持按多个字段排序
- **升降序切换**: 点击同一字段时切换排序方向
- **拖拽重排序**: 支持手动拖拽调整记录顺序
- **序列字段**: 使用handle字段进行重排序

### 5. 分页和加载

```javascript
// 加载数据
load({ limit, offset, orderBy } = {}) {
    return this.model.mutex.exec(async () => {
        if (this.editedRecord && !(await this.editedRecord.checkValidity())) {
            return;
        }
        limit = limit !== undefined ? limit : this.limit;
        offset = offset !== undefined ? offset : this.offset;
        orderBy = orderBy !== undefined ? orderBy : this.orderBy;
        return this._load({ limit, offset, orderBy });
    });
}

// 批量添加和移除
async addAndRemove({ add, remove, reload } = {}) {
    return this.model.mutex.exec(async () => {
        const commands = [
            ...(add || []).map((id) => [x2ManyCommands.LINK, id]),
            ...(remove || []).map((id) => [x2ManyCommands.UNLINK, id]),
        ];
        await this._applyCommands(commands, { canAddOverLimit: true, reload });
        await this._onUpdate();
    });
}
```

**加载功能**:
- **分页加载**: 支持分页参数的动态加载
- **验证检查**: 加载前检查编辑记录的有效性
- **批量操作**: 支持批量添加和移除记录
- **超限处理**: 支持超出限制的记录添加
# Relational Model Errors - 关系模型错误处理

## 概述

`errors.js` 是 Odoo Web 关系模型的错误定义和处理模块，提供了关系模型中特定的错误类型和处理机制。该模块包含28行代码，定义了FetchRecordError错误类和相应的错误处理器，具备国际化错误消息、自动错误处理、用户友好提示等特性，为关系模型的错误处理提供了专业的解决方案。

## 文件信息
- **路径**: `/web/static/src/model/relational_model/errors.js`
- **行数**: 28
- **模块**: `@web/model/relational_model/errors`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                 // 注册表系统
'@web/core/l10n/translation'         // 国际化翻译
```

## 错误类定义

### 1. FetchRecordError 类

```javascript
class FetchRecordError extends Error {
    constructor(resIds) {
        super(
            _t(
                "It seems the records with IDs %s cannot be found. They might have been deleted.",
                resIds
            )
        );
        this.resIds = resIds;
    }
}
```

**错误类特性**:
- **继承Error**: 继承标准JavaScript Error类
- **国际化消息**: 使用_t函数提供多语言支持
- **记录ID**: 保存无法找到的记录ID列表
- **用户友好**: 提供用户友好的错误描述

**错误信息**:
- **中文**: "看起来无法找到ID为 %s 的记录。它们可能已被删除。"
- **英文**: "It seems the records with IDs %s cannot be found. They might have been deleted."
- **参数**: resIds - 无法找到的记录ID数组

## 错误处理器

### 1. fetchRecordErrorHandler 函数

```javascript
function fetchRecordErrorHandler(env, error, originalError) {
    if (originalError instanceof FetchRecordError) {
        env.services.notification.add(originalError.message, { 
            sticky: true, 
            type: "danger" 
        });
        return true;
    }
}
```

**处理器功能**:
- **类型检查**: 检查是否为FetchRecordError类型
- **通知显示**: 使用通知服务显示错误消息
- **粘性通知**: 设置为粘性通知，需要用户手动关闭
- **危险类型**: 设置为危险类型，使用红色警告样式
- **处理标记**: 返回true表示错误已被处理

### 2. 错误处理器注册

```javascript
const errorHandlerRegistry = registry.category("error_handlers");
errorHandlerRegistry.add("fetchRecordErrorHandler", fetchRecordErrorHandler);
```

**注册功能**:
- **全局注册**: 将错误处理器注册到全局错误处理系统
- **自动处理**: 系统自动调用注册的错误处理器
- **优先级**: 按注册顺序处理错误
- **链式处理**: 支持多个错误处理器的链式处理

## 使用场景

### 1. 记录获取错误处理

```javascript
// 记录获取服务
class RecordFetchService {
    constructor(orm, notification) {
        this.orm = orm;
        this.notification = notification;
    }
    
    async fetchRecord(resModel, resId) {
        try {
            const records = await this.orm.read(resModel, [resId]);
            if (records.length === 0) {
                throw new FetchRecordError([resId]);
            }
            return records[0];
        } catch (error) {
            if (error.name === 'RPC_ERROR' && error.data.name === 'MissingError') {
                throw new FetchRecordError([resId]);
            }
            throw error;
        }
    }
    
    async fetchRecords(resModel, resIds) {
        try {
            const records = await this.orm.read(resModel, resIds);
            
            // 检查是否有缺失的记录
            const foundIds = records.map(r => r.id);
            const missingIds = resIds.filter(id => !foundIds.includes(id));
            
            if (missingIds.length > 0) {
                throw new FetchRecordError(missingIds);
            }
            
            return records;
        } catch (error) {
            if (error instanceof FetchRecordError) {
                throw error;
            }
            
            // 处理其他RPC错误
            if (error.name === 'RPC_ERROR') {
                if (error.data.name === 'MissingError') {
                    // 从错误信息中提取缺失的ID
                    const missingIds = this.extractMissingIds(error.data.message);
                    throw new FetchRecordError(missingIds);
                }
            }
            
            throw error;
        }
    }
    
    extractMissingIds(errorMessage) {
        // 从错误消息中提取缺失的记录ID
        const matches = errorMessage.match(/\d+/g);
        return matches ? matches.map(id => parseInt(id)) : [];
    }
    
    async safelyFetchRecord(resModel, resId, defaultValue = null) {
        try {
            return await this.fetchRecord(resModel, resId);
        } catch (error) {
            if (error instanceof FetchRecordError) {
                console.warn(`Record ${resId} not found, using default value`);
                return defaultValue;
            }
            throw error;
        }
    }
    
    async batchFetchWithRetry(resModel, resIds, maxRetries = 3) {
        let attempt = 0;
        let remainingIds = [...resIds];
        const results = [];
        
        while (attempt < maxRetries && remainingIds.length > 0) {
            try {
                const records = await this.fetchRecords(resModel, remainingIds);
                results.push(...records);
                break;
            } catch (error) {
                if (error instanceof FetchRecordError) {
                    // 移除缺失的ID，重试剩余的
                    remainingIds = remainingIds.filter(id => !error.resIds.includes(id));
                    attempt++;
                    
                    if (remainingIds.length === 0) {
                        console.warn('All records are missing:', error.resIds);
                        break;
                    }
                } else {
                    throw error;
                }
            }
        }
        
        return results;
    }
}

// 使用示例
const fetchService = new RecordFetchService(orm, notification);

// 获取单个记录
try {
    const partner = await fetchService.fetchRecord('res.partner', 123);
    console.log('Partner found:', partner);
} catch (error) {
    // FetchRecordError会被自动处理并显示通知
    console.error('Failed to fetch partner:', error);
}

// 安全获取记录
const partner = await fetchService.safelyFetchRecord('res.partner', 123, {
    id: 123,
    name: 'Unknown Partner'
});

// 批量获取记录
const partners = await fetchService.fetchRecords('res.partner', [1, 2, 3, 999]);
```

### 2. 自定义错误处理器

```javascript
// 扩展错误处理系统
class RelationalModelErrorHandler {
    constructor() {
        this.errorHandlers = new Map();
        this.setupDefaultHandlers();
    }
    
    setupDefaultHandlers() {
        // 记录不存在错误
        this.addHandler('FetchRecordError', (env, error) => {
            env.services.notification.add(error.message, {
                sticky: true,
                type: 'danger',
                title: '记录获取失败'
            });
            
            // 记录错误日志
            console.error('FetchRecordError:', {
                resIds: error.resIds,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent
            });
            
            return true;
        });
        
        // 权限错误
        this.addHandler('AccessError', (env, error) => {
            env.services.notification.add(
                '您没有权限访问此记录',
                {
                    type: 'warning',
                    title: '权限不足'
                }
            );
            return true;
        });
        
        // 验证错误
        this.addHandler('ValidationError', (env, error) => {
            const message = error.data?.message || '数据验证失败';
            env.services.notification.add(message, {
                type: 'danger',
                title: '验证错误'
            });
            return true;
        });
        
        // 网络错误
        this.addHandler('NetworkError', (env, error) => {
            env.services.notification.add(
                '网络连接失败，请检查网络连接',
                {
                    type: 'warning',
                    title: '网络错误'
                }
            );
            return true;
        });
    }
    
    addHandler(errorType, handler) {
        this.errorHandlers.set(errorType, handler);
    }
    
    removeHandler(errorType) {
        this.errorHandlers.delete(errorType);
    }
    
    handleError(env, error, originalError) {
        // 确定错误类型
        let errorType = originalError.constructor.name;
        
        if (originalError.name) {
            errorType = originalError.name;
        }
        
        // 查找对应的处理器
        const handler = this.errorHandlers.get(errorType);
        if (handler) {
            try {
                return handler(env, error, originalError);
            } catch (handlerError) {
                console.error('Error in error handler:', handlerError);
            }
        }
        
        // 默认处理
        return this.defaultErrorHandler(env, error, originalError);
    }
    
    defaultErrorHandler(env, error, originalError) {
        console.error('Unhandled error:', originalError);
        
        env.services.notification.add(
            '发生了未知错误，请联系系统管理员',
            {
                type: 'danger',
                title: '系统错误'
            }
        );
        
        return true;
    }
    
    createErrorReport(error) {
        return {
            type: error.constructor.name,
            message: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            additionalData: error.resIds ? { resIds: error.resIds } : {}
        };
    }
    
    async reportError(error) {
        const report = this.createErrorReport(error);
        
        try {
            // 发送错误报告到服务器
            await fetch('/web/error_report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(report)
            });
        } catch (reportError) {
            console.error('Failed to report error:', reportError);
        }
    }
}

// 注册自定义错误处理器
const customErrorHandler = new RelationalModelErrorHandler();

registry.category("error_handlers").add("customRelationalModelErrorHandler", 
    (env, error, originalError) => customErrorHandler.handleError(env, error, originalError)
);
```

### 3. 错误恢复机制

```javascript
// 错误恢复服务
class ErrorRecoveryService {
    constructor(orm, notification) {
        this.orm = orm;
        this.notification = notification;
        this.recoveryStrategies = new Map();
        this.setupRecoveryStrategies();
    }
    
    setupRecoveryStrategies() {
        // 记录不存在的恢复策略
        this.recoveryStrategies.set('FetchRecordError', async (error, context) => {
            const { resModel, resIds } = context;
            
            // 策略1: 尝试从缓存获取
            const cachedRecords = this.getCachedRecords(resModel, error.resIds);
            if (cachedRecords.length > 0) {
                return { success: true, data: cachedRecords };
            }
            
            // 策略2: 尝试获取相似记录
            const similarRecords = await this.findSimilarRecords(resModel, error.resIds);
            if (similarRecords.length > 0) {
                this.notification.add(
                    `找到了 ${similarRecords.length} 个相似记录`,
                    { type: 'info' }
                );
                return { success: true, data: similarRecords };
            }
            
            // 策略3: 创建占位符记录
            const placeholders = this.createPlaceholderRecords(resModel, error.resIds);
            return { success: true, data: placeholders };
        });
        
        // 权限错误的恢复策略
        this.recoveryStrategies.set('AccessError', async (error, context) => {
            // 尝试获取公开字段
            const publicFields = await this.getPublicFields(context.resModel);
            if (publicFields.length > 0) {
                const limitedData = await this.orm.read(
                    context.resModel, 
                    context.resIds, 
                    publicFields
                );
                return { success: true, data: limitedData };
            }
            
            return { success: false, message: '无法恢复，权限不足' };
        });
    }
    
    async attemptRecovery(error, context) {
        const errorType = error.constructor.name;
        const strategy = this.recoveryStrategies.get(errorType);
        
        if (strategy) {
            try {
                const result = await strategy(error, context);
                if (result.success) {
                    console.log(`Successfully recovered from ${errorType}`);
                    return result.data;
                } else {
                    console.warn(`Recovery failed for ${errorType}:`, result.message);
                }
            } catch (recoveryError) {
                console.error(`Recovery strategy failed for ${errorType}:`, recoveryError);
            }
        }
        
        throw error; // 无法恢复，重新抛出原错误
    }
    
    getCachedRecords(resModel, resIds) {
        // 从本地缓存获取记录
        const cache = this.getModelCache(resModel);
        return resIds.map(id => cache.get(id)).filter(Boolean);
    }
    
    async findSimilarRecords(resModel, missingIds) {
        try {
            // 查找相似的记录（例如，相近的ID）
            const searchIds = missingIds.flatMap(id => [id - 1, id + 1]);
            const records = await this.orm.search_read(
                resModel,
                [['id', 'in', searchIds]],
                ['id', 'display_name']
            );
            return records;
        } catch (error) {
            return [];
        }
    }
    
    createPlaceholderRecords(resModel, missingIds) {
        return missingIds.map(id => ({
            id,
            display_name: `[已删除] 记录 ${id}`,
            __placeholder: true
        }));
    }
    
    async getPublicFields(resModel) {
        try {
            const fields = await this.orm.call(resModel, 'fields_get', []);
            return Object.keys(fields).filter(fieldName => 
                !fields[fieldName].groups && fields[fieldName].readonly
            );
        } catch (error) {
            return ['id', 'display_name'];
        }
    }
    
    getModelCache(resModel) {
        // 简化的缓存实现
        if (!this._cache) {
            this._cache = new Map();
        }
        if (!this._cache.has(resModel)) {
            this._cache.set(resModel, new Map());
        }
        return this._cache.get(resModel);
    }
}

// 使用示例
const recoveryService = new ErrorRecoveryService(orm, notification);

// 带恢复机制的记录获取
async function fetchRecordWithRecovery(resModel, resId) {
    try {
        return await orm.read(resModel, [resId]);
    } catch (error) {
        if (error instanceof FetchRecordError) {
            return await recoveryService.attemptRecovery(error, {
                resModel,
                resIds: [resId]
            });
        }
        throw error;
    }
}
```

## 技术特点

### 1. 专业错误处理
- **特定错误**: 针对关系模型的特定错误类型
- **国际化**: 支持多语言错误消息
- **用户友好**: 提供用户友好的错误描述
- **自动处理**: 自动的错误捕获和处理

### 2. 错误信息管理
- **结构化**: 结构化的错误信息存储
- **上下文**: 保存错误发生时的上下文信息
- **追踪**: 支持错误的追踪和调试
- **报告**: 支持错误报告和分析

### 3. 通知集成
- **即时通知**: 错误发生时即时通知用户
- **样式控制**: 不同类型错误的不同样式
- **持久性**: 重要错误的持久性显示
- **交互性**: 支持用户交互的错误处理

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- **错误处理**: 不同错误类型的处理策略
- **恢复策略**: 不同的错误恢复策略
- **通知策略**: 不同的错误通知策略

### 2. 责任链模式 (Chain of Responsibility Pattern)
- **处理链**: 错误处理器的链式调用
- **优先级**: 按优先级处理错误
- **传递机制**: 错误在处理链中的传递

### 3. 观察者模式 (Observer Pattern)
- **错误监听**: 错误事件的监听机制
- **通知分发**: 错误通知的分发机制
- **状态同步**: 错误状态的同步更新

## 注意事项

1. **错误分类**: 正确分类和定义错误类型
2. **用户体验**: 提供用户友好的错误信息
3. **性能影响**: 避免错误处理影响系统性能
4. **安全考虑**: 不要在错误信息中泄露敏感信息

## 扩展建议

1. **错误类型**: 扩展更多的错误类型定义
2. **恢复机制**: 增强错误恢复机制
3. **监控集成**: 集成错误监控和报告系统
4. **用户指导**: 提供错误解决的用户指导
5. **调试工具**: 增强错误调试和分析工具

该错误处理模块为Odoo Web关系模型提供了专业的错误处理能力，通过结构化的错误定义和自动化的处理机制确保了良好的用户体验和系统稳定性。

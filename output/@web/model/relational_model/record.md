# Record - 记录数据点

## 概述

`record.js` 是 Odoo Web 关系模型的记录数据点类，提供了单个记录的完整管理功能。该模块包含1262行代码，继承自DataPoint基类，是关系模型中最核心的组件，负责处理单个记录的数据管理、字段验证、变更跟踪、保存操作等功能，具备完整的CRUD操作、字段依赖处理、onchange事件、数据验证等特性，是Odoo Web前端数据层的核心实现。

## 文件信息
- **路径**: `/web/static/src/model/relational_model/record.js`
- **行数**: 1262
- **模块**: `@web/model/relational_model/record`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                  // OWL框架
'@web/core/confirmation_dialog/confirmation_dialog' // 确认对话框
'@web/core/l10n/dates'                       // 日期本地化
'@web/core/l10n/translation'                 // 国际化
'@web/core/orm_service'                      // ORM服务
'@web/core/py_js/py'                         // Python表达式
'@web/core/utils/strings'                    // 字符串工具
'@web/model/relational_model/datapoint'      // 数据点基类
'@web/model/relational_model/utils'          // 关系模型工具
'@web/model/relational_model/errors'         // 错误定义
```

## 主类定义

### 1. Record 类

```javascript
class Record extends DataPoint {
    static type = "Record";
    
    setup(config, data, options = {}) {
        this._manuallyAdded = options.manuallyAdded === true;
        this._onUpdate = options.onUpdate || (() => {});
        this._parentRecord = options.parentRecord;
        this.canSaveOnUpdate = !options.parentRecord;
        this._virtualId = options.virtualId || false;
        this._isEvalContextReady = false;
        
        this.dirty = false;
        this.selected = false;
        this._invalidFields = new Set();
        this._changes = {};
        this._savePoint = undefined;
        this._mode = config.mode || "readonly";
        
        // 初始化数据
        this.data = {};
        this._applyValues(data);
        this._applyDefaultValues();
        this._setEvalContext();
    }
}
```

**类特性**:
- **单记录管理**: 专门处理单个记录的数据
- **状态跟踪**: 跟踪记录的各种状态
- **变更管理**: 管理字段的变更和验证
- **模式切换**: 支持只读和编辑模式切换

## 核心属性

### 1. 基础属性

```javascript
// 记录标识
get id() { return this.resId || this._virtualId; }
get resId() { return this.config.resId; }
get isNew() { return !this.resId; }
get isVirtual() { return Boolean(this._virtualId); }

// 状态属性
get mode() { return this._mode; }
get isInEdition() { return this.mode === "edit"; }
get canBeAbandoned() { return this.isNew && this._manuallyAdded; }
get canBeDiscarded() { return this.dirty || this.isNew; }

// 数据属性
get evalContext() { return this._evalContext; }
get isDirty() { return this.dirty || Object.keys(this._changes).length > 0; }
```

**属性功能**:
- **标识管理**: 管理记录的各种标识符
- **状态检查**: 检查记录的各种状态
- **数据访问**: 提供数据访问的便捷接口
- **变更检测**: 检测记录是否有未保存的变更

### 2. 字段属性

```javascript
// 字段相关属性
get fieldNames() {
    return Object.keys(this.activeFields);
}

get invalidFields() {
    return Array.from(this._invalidFields);
}

get hasInvalidFields() {
    return this._invalidFields.size > 0;
}
```

**字段属性功能**:
- **字段列表**: 获取所有活动字段名称
- **验证状态**: 获取无效字段信息
- **验证检查**: 检查是否有验证错误

## 核心方法

### 1. 数据操作

```javascript
// 更新数据
async update(changes, options = {}) {
    return this.model.mutex.exec(async () => {
        const parsedChanges = this._parseServerValues(changes, this.data);
        this._applyChanges(parsedChanges);
        
        if (!options.withoutOnchange) {
            await this._performOnchange();
        }
        
        if (!options.withoutParentUpdate) {
            await this._onUpdate();
        }
    });
}

// 保存记录
async save(options = {}) {
    return this.model.mutex.exec(async () => {
        if (!this._checkValidity()) {
            return false;
        }
        
        const changes = this._getChanges();
        if (this.isNew) {
            const result = await this.model._createRecord(this.config, changes);
            this._applyValues(result);
        } else {
            await this.model._updateRecord(this.config, changes);
        }
        
        this._clearChanges();
        this.dirty = false;
        return true;
    });
}

// 丢弃变更
discard() {
    this._changes = {};
    this.dirty = false;
    this._invalidFields.clear();
    
    if (this._savePoint) {
        this._restoreFromSavePoint();
    } else {
        this._applyValues(this._originalData);
    }
}
```

**数据操作功能**:
- **更新机制**: 安全的数据更新机制
- **保存逻辑**: 完整的保存逻辑处理
- **变更管理**: 变更的应用和回滚
- **状态同步**: 保持数据状态的一致性

### 2. 字段验证

```javascript
// 检查有效性
checkValidity(options = {}) {
    return this.model.mutex.exec(() => {
        return this._checkValidity(options);
    });
}

// 内部验证逻辑
_checkValidity(options = {}) {
    this._invalidFields.clear();
    
    for (const fieldName in this.activeFields) {
        const fieldInfo = this.activeFields[fieldName];
        const field = this.fields[fieldName];
        const value = this.data[fieldName];
        
        // 必填字段验证
        if (fieldInfo.required && this._isFieldEmpty(value, field)) {
            this._invalidFields.add(fieldName);
            if (!options.silent) {
                this._showFieldError(fieldName, _t("This field is required"));
            }
        }
        
        // 字段特定验证
        const error = this._validateField(fieldName, value, field);
        if (error) {
            this._invalidFields.add(fieldName);
            if (!options.silent) {
                this._showFieldError(fieldName, error);
            }
        }
    }
    
    return this._invalidFields.size === 0;
}

// 字段特定验证
_validateField(fieldName, value, field) {
    switch (field.type) {
        case 'email':
            return this._validateEmail(value);
        case 'url':
            return this._validateUrl(value);
        case 'integer':
        case 'float':
            return this._validateNumber(value, field);
        case 'date':
            return this._validateDate(value);
        case 'datetime':
            return this._validateDateTime(value);
        default:
            return null;
    }
}
```

**验证功能**:
- **必填验证**: 检查必填字段是否为空
- **类型验证**: 根据字段类型进行验证
- **自定义验证**: 支持自定义验证规则
- **错误提示**: 提供用户友好的错误提示

### 3. 模式管理

```javascript
// 切换模式
async switchMode(mode) {
    if (this.mode === mode) {
        return;
    }
    
    if (mode === "edit") {
        await this._enterEditMode();
    } else if (mode === "readonly") {
        await this._enterReadonlyMode();
    }
    
    this._mode = mode;
}

// 进入编辑模式
async _enterEditMode() {
    if (this.mode === "readonly") {
        this._addSavePoint();
        await this._loadEditableFields();
    }
}

// 进入只读模式
async _enterReadonlyMode() {
    if (this.mode === "edit") {
        if (this.dirty && !await this._confirmDiscard()) {
            return false;
        }
        this.discard();
    }
    return true;
}
```

**模式管理功能**:
- **模式切换**: 在只读和编辑模式间切换
- **状态保存**: 切换前保存当前状态
- **确认机制**: 有未保存变更时确认操作
- **字段加载**: 按需加载编辑所需的字段

### 4. Onchange 处理

```javascript
// 执行onchange
async _performOnchange(fieldNames = null) {
    if (!this._hasOnchangeFields(fieldNames)) {
        return;
    }
    
    const spec = this._getOnchangeSpec(fieldNames);
    const values = this._getOnchangeValues();
    
    try {
        const result = await this.model._performOnchange(this.config, {
            values,
            spec,
            context: this.evalContext
        });
        
        this._applyOnchangeResult(result);
    } catch (error) {
        console.error('Onchange error:', error);
        throw error;
    }
}

// 应用onchange结果
_applyOnchangeResult(result) {
    if (result.value) {
        const parsedValues = this._parseServerValues(result.value, this.data);
        this._applyChanges(parsedValues);
    }
    
    if (result.warning) {
        this._showWarning(result.warning);
    }
    
    if (result.domain) {
        this._updateDomains(result.domain);
    }
}
```

**Onchange功能**:
- **触发机制**: 字段变更时自动触发onchange
- **规格生成**: 生成onchange请求的规格
- **结果处理**: 处理服务器返回的onchange结果
- **域更新**: 更新字段的域限制

## 使用场景

### 1. 基础记录操作

```javascript
// 创建和操作记录
class RecordManager {
    constructor(model) {
        this.model = model;
    }
    
    async createRecord(resModel, initialData = {}) {
        const config = {
            resModel,
            mode: 'edit',
            activeFields: await this.getActiveFields(resModel),
            fields: await this.getFields(resModel),
            context: {}
        };
        
        const record = new Record(this.model, config, initialData, {
            manuallyAdded: true,
            onUpdate: this.handleRecordUpdate.bind(this)
        });
        
        return record;
    }
    
    async loadRecord(resModel, resId) {
        const config = {
            resModel,
            resId,
            mode: 'readonly',
            activeFields: await this.getActiveFields(resModel),
            fields: await this.getFields(resModel),
            context: {}
        };
        
        const data = await this.model._loadRecord(config);
        const record = new Record(this.model, config, data);
        
        return record;
    }
    
    async saveRecord(record) {
        try {
            const success = await record.save();
            if (success) {
                console.log('Record saved successfully');
                return record;
            } else {
                console.log('Record validation failed');
                return null;
            }
        } catch (error) {
            console.error('Save error:', error);
            throw error;
        }
    }
    
    async handleRecordUpdate(record) {
        // 记录更新时的处理逻辑
        console.log('Record updated:', record.id);
        
        // 可以在这里添加自动保存逻辑
        if (record.canSaveOnUpdate && record.isDirty) {
            await this.autoSave(record);
        }
    }
    
    async autoSave(record) {
        try {
            await record.save();
            console.log('Auto-saved record:', record.id);
        } catch (error) {
            console.error('Auto-save failed:', error);
        }
    }
}
```

### 2. 表单字段管理

```javascript
// 表单字段管理器
class FormFieldManager {
    constructor(record) {
        this.record = record;
        this.fieldWidgets = new Map();
        this.validators = new Map();
    }
    
    registerField(fieldName, widget) {
        this.fieldWidgets.set(fieldName, widget);
        
        // 监听字段变更
        widget.addEventListener('change', async (event) => {
            await this.handleFieldChange(fieldName, event.detail.value);
        });
        
        // 设置初始值
        widget.setValue(this.record.data[fieldName]);
    }
    
    async handleFieldChange(fieldName, value) {
        try {
            // 更新记录
            await this.record.update({ [fieldName]: value });
            
            // 更新相关字段的显示
            this.updateRelatedFields(fieldName);
            
            // 验证字段
            this.validateField(fieldName);
            
        } catch (error) {
            console.error('Field change error:', error);
            this.showFieldError(fieldName, error.message);
        }
    }
    
    updateRelatedFields(changedFieldName) {
        // 更新依赖于此字段的其他字段
        for (const [fieldName, widget] of this.fieldWidgets) {
            if (this.isFieldDependent(fieldName, changedFieldName)) {
                widget.setValue(this.record.data[fieldName]);
                widget.updateVisibility(this.record.evalContext);
                widget.updateReadonly(this.record.evalContext);
            }
        }
    }
    
    validateField(fieldName) {
        const widget = this.fieldWidgets.get(fieldName);
        if (!widget) return;
        
        const field = this.record.fields[fieldName];
        const value = this.record.data[fieldName];
        
        // 清除之前的错误
        widget.clearError();
        
        // 执行验证
        const validator = this.validators.get(fieldName);
        if (validator) {
            const error = validator(value, field, this.record);
            if (error) {
                widget.showError(error);
                return false;
            }
        }
        
        return true;
    }
    
    validateAllFields() {
        let isValid = true;
        for (const fieldName of this.fieldWidgets.keys()) {
            if (!this.validateField(fieldName)) {
                isValid = false;
            }
        }
        return isValid;
    }
    
    addValidator(fieldName, validator) {
        this.validators.set(fieldName, validator);
    }
    
    isFieldDependent(fieldName, changedFieldName) {
        const fieldInfo = this.record.activeFields[fieldName];
        if (!fieldInfo) return false;
        
        // 检查字段的invisible、readonly、required等属性是否依赖于changedFieldName
        const dependencies = [
            fieldInfo.invisible,
            fieldInfo.readonly,
            fieldInfo.required
        ].filter(Boolean);
        
        return dependencies.some(expr => 
            typeof expr === 'string' && expr.includes(changedFieldName)
        );
    }
    
    showFieldError(fieldName, message) {
        const widget = this.fieldWidgets.get(fieldName);
        if (widget) {
            widget.showError(message);
        }
    }
    
    clearAllErrors() {
        for (const widget of this.fieldWidgets.values()) {
            widget.clearError();
        }
    }
}
```

## 技术特点

### 1. 完整的CRUD操作
- **创建记录**: 支持新记录的创建和初始化
- **读取数据**: 从服务器加载记录数据
- **更新记录**: 字段级别的数据更新
- **删除记录**: 记录的删除操作

### 2. 智能变更跟踪
- **变更检测**: 自动检测字段值的变更
- **变更合并**: 智能合并多个变更
- **变更回滚**: 支持变更的撤销和恢复
- **保存点**: 支持保存点机制

### 3. 完整的验证系统
- **类型验证**: 根据字段类型进行验证
- **必填验证**: 必填字段的验证
- **自定义验证**: 支持自定义验证规则
- **实时验证**: 字段变更时的实时验证

### 4. Onchange机制
- **自动触发**: 字段变更时自动触发onchange
- **依赖处理**: 处理字段间的依赖关系
- **结果应用**: 自动应用onchange的结果
- **错误处理**: 完善的onchange错误处理

## 设计模式

### 1. 状态模式 (State Pattern)
- **模式管理**: 只读和编辑模式的管理
- **状态转换**: 模式间的安全转换
- **行为变化**: 不同模式下的行为差异

### 2. 观察者模式 (Observer Pattern)
- **变更通知**: 字段变更的自动通知
- **事件监听**: onUpdate事件的监听机制
- **依赖更新**: 依赖字段的自动更新

### 3. 命令模式 (Command Pattern)
- **操作封装**: 将操作封装为命令
- **撤销重做**: 支持操作的撤销和重做
- **批量操作**: 批量命令的执行

## 注意事项

1. **并发安全**: 使用mutex确保操作的原子性
2. **内存管理**: 及时清理不再使用的记录
3. **验证时机**: 在合适的时机进行数据验证
4. **错误处理**: 完善的错误处理和用户提示

## 扩展建议

1. **缓存优化**: 优化字段值的缓存机制
2. **批量操作**: 支持批量记录操作
3. **离线支持**: 添加离线模式支持
4. **版本控制**: 记录变更的版本控制
5. **性能监控**: 监控记录操作的性能

该记录类为Odoo Web应用提供了完整的单记录数据管理功能，通过完善的状态管理和验证机制确保了数据的一致性和可靠性。

# Group - 分组数据点

## 概述

`group.js` 是 Odoo Web 关系模型的分组数据点类，提供了分组数据的管理和操作功能。该模块包含129行代码，继承自DataPoint基类，专门处理分组视图中的单个分组，支持分组信息管理、子列表处理、聚合计算、分组记录等功能，具备分组值处理、统计信息、嵌套分组等特性，是分组视图中表示单个分组的核心组件。

## 文件信息
- **路径**: `/web/static/src/model/relational_model/group.js`
- **行数**: 129
- **模块**: `@web/model/relational_model/group`

## 依赖关系

```javascript
// 核心依赖
'@web/core/domain'                           // 域处理
'@web/model/relational_model/datapoint'      // 数据点基类
```

## 类型定义

```javascript
/**
 * @typedef Params
 * @property {string[]} groupBy - 分组字段列表
 */
```

## 主类定义

### 1. Group 类

```javascript
class Group extends DataPoint {
    static type = "Group";
    
    setup(config, data) {
        super.setup(...arguments);
        
        // 分组字段信息
        this.groupByField = this.fields[config.groupByFieldName];
        
        // 分组数据
        this.range = data.range;
        this._rawValue = data.rawValue;
        this.count = data.count;
        this.value = data.value;
        this.serverValue = data.serverValue;
        this.displayName = data.displayName;
        this.aggregates = data.aggregates;
        
        // 创建子列表
        let List;
        if (config.list.groupBy.length) {
            List = this.model.constructor.DynamicGroupList;
        } else {
            List = this.model.constructor.DynamicRecordList;
        }
        this.list = new List(this.model, config.list, data);
        
        // 创建分组记录（如果需要）
        if (config.record) {
            config.record.context = { ...config.record.context, ...config.context };
            this.record = new this.model.constructor.Record(this.model, config.record, data.values);
        }
    }
}
```

**类特性**:
- **继承DataPoint**: 继承数据点基类的基础功能
- **分组管理**: 管理单个分组的数据和状态
- **嵌套支持**: 支持多级分组的嵌套结构
- **聚合计算**: 提供分组的聚合统计功能

## 核心属性

### 1. 分组基础属性

```javascript
// 分组标识
get groupKey() {
    return `${this.groupByField.name}:${this.serverValue}`;
}

// 分组字段
get groupByField() {
    return this._groupByField;
}

// 分组值
get value() {
    return this._value;
}

get serverValue() {
    return this._serverValue;
}

get rawValue() {
    return this._rawValue;
}

// 显示名称
get displayName() {
    return this._displayName || this.value;
}
```

**基础属性功能**:
- **groupKey**: 分组的唯一标识符
- **groupByField**: 分组字段的定义信息
- **value**: 分组的客户端值
- **serverValue**: 分组的服务器端值
- **rawValue**: 分组的原始值
- **displayName**: 分组的显示名称

### 2. 分组状态属性

```javascript
// 记录数量
get count() {
    return this._count || 0;
}

// 是否有数据
get hasData() {
    return this.count > 0;
}

// 是否展开
get isOpen() {
    return this.list && this.list.isLoaded;
}

// 是否为叶子分组
get isLeaf() {
    return !this.list || this.list.groupBy.length === 0;
}

// 聚合值
get aggregates() {
    return this._aggregates || {};
}

// 范围信息（用于日期分组）
get range() {
    return this._range;
}
```

**状态属性功能**:
- **count**: 分组中的记录数量
- **hasData**: 检查分组是否有数据
- **isOpen**: 检查分组是否已展开
- **isLeaf**: 检查是否为叶子分组（最后一级）
- **aggregates**: 分组的聚合统计值
- **range**: 分组的范围信息（主要用于日期分组）

### 3. 子数据属性

```javascript
// 子列表
get list() {
    return this._list;
}

// 分组记录
get record() {
    return this._record;
}

// 子分组（如果是分组列表）
get subGroups() {
    return this.list && this.list.groups ? this.list.groups : [];
}

// 记录列表（如果是记录列表）
get records() {
    return this.list && this.list.records ? this.list.records : [];
}
```

**子数据属性功能**:
- **list**: 分组的子列表（可能是分组列表或记录列表）
- **record**: 分组对应的记录（用于某些特殊场景）
- **subGroups**: 子分组列表（多级分组时）
- **records**: 记录列表（叶子分组时）

## 核心方法

### 1. 分组操作

```javascript
// 展开分组
async expand() {
    if (this.isOpen) {
        return this.list;
    }
    
    return this.model.mutex.exec(async () => {
        await this.list.load();
        return this.list;
    });
}

// 折叠分组
async collapse() {
    if (!this.isOpen) {
        return;
    }
    
    return this.model.mutex.exec(async () => {
        this.list.unload();
    });
}

// 切换分组状态
async toggle() {
    if (this.isOpen) {
        return this.collapse();
    } else {
        return this.expand();
    }
}

// 重新加载分组
async reload() {
    if (this.list) {
        return this.list.reload();
    }
}
```

**分组操作功能**:
- **展开分组**: 加载并展开分组内容
- **折叠分组**: 折叠分组并释放内存
- **切换状态**: 在展开和折叠间切换
- **重新加载**: 重新加载分组数据

### 2. 数据访问

```javascript
// 获取分组域
getDomain() {
    const domain = [];
    
    if (this.serverValue !== false && this.serverValue !== null) {
        domain.push([this.groupByField.name, '=', this.serverValue]);
    } else {
        domain.push([this.groupByField.name, '=', false]);
    }
    
    return domain;
}

// 获取完整域（包含父级域）
getFullDomain() {
    const parentDomain = this.config.domain || [];
    const groupDomain = this.getDomain();
    
    return Domain.and([parentDomain, groupDomain]).toList();
}

// 获取分组上下文
getGroupContext() {
    const baseContext = this.context || {};
    const groupContext = {
        ...baseContext,
        [`default_${this.groupByField.name}`]: this.serverValue
    };
    
    return groupContext;
}

// 检查记录是否属于此分组
containsRecord(record) {
    const recordValue = record.data[this.groupByField.name];
    
    if (this.groupByField.type === 'many2one') {
        const recordId = Array.isArray(recordValue) ? recordValue[0] : recordValue;
        return recordId === this.serverValue;
    } else {
        return recordValue === this.serverValue;
    }
}
```

**数据访问功能**:
- **域构建**: 构建分组的搜索域
- **完整域**: 获取包含父级条件的完整域
- **上下文**: 获取分组相关的上下文
- **记录检查**: 检查记录是否属于此分组

### 3. 聚合计算

```javascript
// 计算聚合值
calculateAggregates() {
    if (!this.isOpen || !this.records.length) {
        return this.aggregates;
    }
    
    const calculatedAggregates = {};
    
    for (const fieldName in this.activeFields) {
        const field = this.fields[fieldName];
        if (field && field.group_operator) {
            calculatedAggregates[fieldName] = this.calculateFieldAggregate(
                fieldName, field.group_operator
            );
        }
    }
    
    return calculatedAggregates;
}

// 计算单个字段的聚合值
calculateFieldAggregate(fieldName, operator) {
    const values = this.records
        .map(record => record.data[fieldName])
        .filter(value => value !== null && value !== undefined);
    
    if (values.length === 0) return 0;
    
    switch (operator) {
        case 'sum':
            return values.reduce((sum, value) => sum + (parseFloat(value) || 0), 0);
        case 'avg':
            const sum = values.reduce((sum, value) => sum + (parseFloat(value) || 0), 0);
            return sum / values.length;
        case 'max':
            return Math.max(...values.map(v => parseFloat(v) || 0));
        case 'min':
            return Math.min(...values.map(v => parseFloat(v) || 0));
        case 'count':
            return values.length;
        case 'count_distinct':
            return new Set(values).size;
        default:
            return values.length;
    }
}

// 更新聚合值
updateAggregates() {
    this._aggregates = this.calculateAggregates();
}
```

**聚合计算功能**:
- **聚合计算**: 计算分组的聚合统计值
- **字段聚合**: 计算单个字段的聚合值
- **操作符支持**: 支持多种聚合操作符
- **聚合更新**: 更新分组的聚合值

## 使用场景

### 1. 分组视图管理

```javascript
// 分组视图管理器
class GroupViewManager {
    constructor(model, config) {
        this.model = model;
        this.config = config;
        this.groups = [];
        this.expandedGroups = new Set();
    }
    
    async loadGroups() {
        const groupsData = await this.model._readGroup({
            resModel: this.config.resModel,
            domain: this.config.domain,
            groupBy: this.config.groupBy,
            orderBy: this.config.orderBy,
            context: this.config.context
        });
        
        this.groups = groupsData.map(data => this.createGroup(data));
        return this.groups;
    }
    
    createGroup(data) {
        const groupConfig = {
            ...this.config,
            groupByFieldName: this.config.groupBy[0].split(':')[0],
            list: {
                ...this.config,
                groupBy: this.config.groupBy.slice(1),
                domain: this.getGroupDomain(data)
            }
        };
        
        return new Group(this.model, groupConfig, data);
    }
    
    getGroupDomain(groupData) {
        const fieldName = this.config.groupBy[0].split(':')[0];
        const groupDomain = [[fieldName, '=', groupData.serverValue]];
        return Domain.and([this.config.domain, groupDomain]).toList();
    }
    
    async expandGroup(group) {
        if (this.expandedGroups.has(group.groupKey)) {
            return group.list;
        }
        
        await group.expand();
        this.expandedGroups.add(group.groupKey);
        return group.list;
    }
    
    async collapseGroup(group) {
        if (!this.expandedGroups.has(group.groupKey)) {
            return;
        }
        
        await group.collapse();
        this.expandedGroups.delete(group.groupKey);
    }
    
    async toggleGroup(group) {
        if (this.expandedGroups.has(group.groupKey)) {
            return this.collapseGroup(group);
        } else {
            return this.expandGroup(group);
        }
    }
    
    getGroupByValue(value) {
        return this.groups.find(group => group.serverValue === value);
    }
    
    getGroupStatistics() {
        return {
            totalGroups: this.groups.length,
            expandedGroups: this.expandedGroups.size,
            totalRecords: this.groups.reduce((sum, group) => sum + group.count, 0),
            averageRecordsPerGroup: this.groups.length > 0 
                ? this.groups.reduce((sum, group) => sum + group.count, 0) / this.groups.length 
                : 0
        };
    }
    
    async createRecordInGroup(group, values = {}) {
        const groupContext = group.getGroupContext();
        const recordValues = { ...values, ...groupContext };
        
        if (!group.isOpen) {
            await this.expandGroup(group);
        }
        
        if (group.isLeaf) {
            return group.list.createRecord(recordValues);
        } else {
            throw new Error('Cannot create record in non-leaf group');
        }
    }
    
    async deleteRecordFromGroup(group, record) {
        if (!group.containsRecord(record)) {
            throw new Error('Record does not belong to this group');
        }
        
        const success = await group.list.deleteRecord(record);
        if (success) {
            group.count--;
            group.updateAggregates();
        }
        
        return success;
    }
}

// 使用示例
const groupManager = new GroupViewManager(model, {
    resModel: 'sale.order',
    domain: [],
    groupBy: ['state', 'partner_id'],
    orderBy: [{ name: 'date_order', asc: false }],
    activeFields: orderActiveFields,
    fields: orderFields,
    context: {}
});

// 加载分组
await groupManager.loadGroups();

// 展开分组
await groupManager.expandGroup(group);

// 在分组中创建记录
await groupManager.createRecordInGroup(group, { name: 'New Order' });
```

### 2. 分组统计分析

```javascript
// 分组统计分析器
class GroupStatisticsAnalyzer {
    constructor(groups) {
        this.groups = groups;
    }
    
    analyzeGroups() {
        const analysis = {
            overview: this.getOverview(),
            distribution: this.getDistribution(),
            aggregates: this.getAggregates(),
            trends: this.getTrends()
        };
        
        return analysis;
    }
    
    getOverview() {
        return {
            totalGroups: this.groups.length,
            totalRecords: this.groups.reduce((sum, group) => sum + group.count, 0),
            averageRecordsPerGroup: this.calculateAverage(this.groups.map(g => g.count)),
            largestGroup: this.getLargestGroup(),
            smallestGroup: this.getSmallestGroup(),
            emptyGroups: this.groups.filter(g => g.count === 0).length
        };
    }
    
    getDistribution() {
        const counts = this.groups.map(group => group.count);
        counts.sort((a, b) => a - b);
        
        return {
            min: counts[0] || 0,
            max: counts[counts.length - 1] || 0,
            median: this.calculateMedian(counts),
            quartiles: this.calculateQuartiles(counts),
            standardDeviation: this.calculateStandardDeviation(counts)
        };
    }
    
    getAggregates() {
        const aggregates = {};
        
        if (this.groups.length === 0) return aggregates;
        
        // 获取所有聚合字段
        const firstGroup = this.groups[0];
        for (const fieldName in firstGroup.aggregates) {
            aggregates[fieldName] = {
                total: this.groups.reduce((sum, group) => 
                    sum + (group.aggregates[fieldName] || 0), 0),
                average: this.calculateAverage(
                    this.groups.map(group => group.aggregates[fieldName] || 0)
                ),
                max: Math.max(...this.groups.map(group => group.aggregates[fieldName] || 0)),
                min: Math.min(...this.groups.map(group => group.aggregates[fieldName] || 0))
            };
        }
        
        return aggregates;
    }
    
    getTrends() {
        // 如果分组是按日期字段，分析趋势
        const dateGroups = this.groups.filter(group => group.range);
        
        if (dateGroups.length === 0) return null;
        
        dateGroups.sort((a, b) => new Date(a.range.from) - new Date(b.range.from));
        
        const trends = {
            recordCountTrend: this.calculateTrend(dateGroups.map(g => g.count)),
            timeSpan: {
                from: dateGroups[0].range.from,
                to: dateGroups[dateGroups.length - 1].range.to
            }
        };
        
        // 计算聚合字段的趋势
        for (const fieldName in dateGroups[0].aggregates) {
            trends[`${fieldName}Trend`] = this.calculateTrend(
                dateGroups.map(group => group.aggregates[fieldName] || 0)
            );
        }
        
        return trends;
    }
    
    getLargestGroup() {
        return this.groups.reduce((largest, group) => 
            group.count > (largest?.count || 0) ? group : largest, null);
    }
    
    getSmallestGroup() {
        return this.groups.reduce((smallest, group) => 
            group.count < (smallest?.count || Infinity) ? group : smallest, null);
    }
    
    calculateAverage(values) {
        if (values.length === 0) return 0;
        return values.reduce((sum, value) => sum + value, 0) / values.length;
    }
    
    calculateMedian(values) {
        const sorted = [...values].sort((a, b) => a - b);
        const mid = Math.floor(sorted.length / 2);
        
        if (sorted.length % 2 === 0) {
            return (sorted[mid - 1] + sorted[mid]) / 2;
        } else {
            return sorted[mid];
        }
    }
    
    calculateQuartiles(values) {
        const sorted = [...values].sort((a, b) => a - b);
        const n = sorted.length;
        
        return {
            q1: sorted[Math.floor(n * 0.25)],
            q2: this.calculateMedian(sorted),
            q3: sorted[Math.floor(n * 0.75)]
        };
    }
    
    calculateStandardDeviation(values) {
        const avg = this.calculateAverage(values);
        const squaredDiffs = values.map(value => Math.pow(value - avg, 2));
        const avgSquaredDiff = this.calculateAverage(squaredDiffs);
        return Math.sqrt(avgSquaredDiff);
    }
    
    calculateTrend(values) {
        if (values.length < 2) return 'insufficient_data';
        
        const n = values.length;
        const sumX = (n * (n - 1)) / 2; // 0 + 1 + 2 + ... + (n-1)
        const sumY = values.reduce((sum, value) => sum + value, 0);
        const sumXY = values.reduce((sum, value, index) => sum + (index * value), 0);
        const sumX2 = (n * (n - 1) * (2 * n - 1)) / 6; // 0² + 1² + 2² + ... + (n-1)²
        
        const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
        
        if (Math.abs(slope) < 0.01) return 'stable';
        return slope > 0 ? 'increasing' : 'decreasing';
    }
    
    generateReport() {
        const analysis = this.analyzeGroups();
        
        return {
            summary: `分析了 ${analysis.overview.totalGroups} 个分组，共 ${analysis.overview.totalRecords} 条记录`,
            overview: analysis.overview,
            distribution: analysis.distribution,
            aggregates: analysis.aggregates,
            trends: analysis.trends,
            recommendations: this.generateRecommendations(analysis),
            generatedAt: new Date().toISOString()
        };
    }
    
    generateRecommendations(analysis) {
        const recommendations = [];
        
        if (analysis.overview.emptyGroups > 0) {
            recommendations.push(`发现 ${analysis.overview.emptyGroups} 个空分组，建议考虑隐藏或合并`);
        }
        
        if (analysis.distribution.standardDeviation > analysis.overview.averageRecordsPerGroup) {
            recommendations.push('分组间记录数量差异较大，建议重新考虑分组策略');
        }
        
        if (analysis.trends && analysis.trends.recordCountTrend === 'decreasing') {
            recommendations.push('记录数量呈下降趋势，需要关注');
        }
        
        return recommendations;
    }
}

// 使用示例
const analyzer = new GroupStatisticsAnalyzer(groups);
const report = analyzer.generateReport();
console.log('分组分析报告:', report);
```

## 技术特点

### 1. 分组数据管理
- **完整信息**: 包含分组的完整信息和统计
- **嵌套支持**: 支持多级分组的嵌套结构
- **动态加载**: 支持分组内容的按需加载
- **状态管理**: 完整的分组状态管理

### 2. 聚合计算
- **多种操作**: 支持多种聚合操作符
- **实时计算**: 实时计算和更新聚合值
- **字段感知**: 根据字段类型智能聚合
- **性能优化**: 优化的聚合计算性能

### 3. 域和上下文
- **域构建**: 智能构建分组的搜索域
- **上下文管理**: 管理分组相关的上下文
- **继承机制**: 正确继承父级域和上下文
- **默认值**: 自动设置分组字段的默认值

### 4. 记录关联
- **记录检查**: 检查记录是否属于分组
- **记录操作**: 支持在分组中操作记录
- **数据一致性**: 确保分组和记录的一致性
- **状态同步**: 同步分组和记录的状态

## 设计模式

### 1. 组合模式 (Composite Pattern)
- **分组结构**: 分组和子分组的树形结构
- **统一接口**: 分组和记录的统一操作接口
- **递归操作**: 支持递归的分组操作

### 2. 策略模式 (Strategy Pattern)
- **聚合策略**: 不同字段的聚合计算策略
- **显示策略**: 不同类型分组的显示策略
- **加载策略**: 不同的分组数据加载策略

### 3. 工厂模式 (Factory Pattern)
- **子列表创建**: 根据分组类型创建不同的子列表
- **记录创建**: 在分组中创建记录
- **配置生成**: 生成子组件的配置

## 注意事项

1. **内存管理**: 及时释放折叠分组的数据
2. **性能考虑**: 避免同时展开过多分组
3. **数据一致性**: 确保分组统计的准确性
4. **用户体验**: 提供分组操作的状态反馈

## 扩展建议

1. **分组缓存**: 智能的分组数据缓存机制
2. **异步聚合**: 支持异步的聚合计算
3. **自定义聚合**: 支持更多自定义聚合函数
4. **分组模板**: 预定义的分组配置模板
5. **实时更新**: 分组数据的实时更新机制

该分组数据点类为Odoo Web应用提供了完整的分组数据管理功能，通过智能的聚合计算和高效的状态管理确保了分组视图的良好性能和用户体验。

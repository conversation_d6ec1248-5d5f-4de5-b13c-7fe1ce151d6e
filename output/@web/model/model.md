# Odoo 模型基类 (Model Base Class) 学习资料

## 文件概述

**文件路径**: `output/@web/model/model.js`  
**原始路径**: `/web/static/src/model/model.js`  
**模块类型**: 核心模型模块 - 模型基类  
**代码行数**: 194 行  
**依赖关系**: 
- `@web/core/user` - 用户服务
- `@web/core/utils/hooks` - Hook工具 (useBus, useService)
- `@web/search/action_hook` - 搜索动作Hook (useSetupAction)
- `@web/search/with_search/with_search` - 搜索常量 (SEARCH_KEYS)
- `@web/model/sample_server` - 示例服务器 (buildSampleORM)
- `@odoo/owl` - OWL框架 (EventBus, onWillStart, onWillUpdateProps, useComponent)

## 模块功能

模型基类模块是 Odoo Web 客户端的核心数据模型基础。该模块提供了：
- 抽象模型基类定义
- 模型生命周期管理
- 示例数据支持
- 搜索参数处理
- 事件总线集成
- Hook集成工具

这个模块是所有具体模型类的基础，定义了模型的标准接口和行为模式，是 Odoo 数据层架构的核心组件。

## 模型架构原理

### 模型生命周期
Odoo 模型遵循标准的生命周期模式：
1. **构造阶段**: 初始化环境、服务和事件总线
2. **设置阶段**: 调用 setup 方法进行模型特定配置
3. **加载阶段**: 根据搜索参数加载数据
4. **更新阶段**: 响应属性变化重新加载数据
5. **通知阶段**: 触发更新事件通知组件

### 示例数据机制
- **数据检测**: 通过 hasData() 方法检测是否有真实数据
- **示例ORM**: 在无数据时使用 SampleORM 提供示例数据
- **无缝切换**: 在示例数据和真实数据之间无缝切换
- **状态管理**: 维护示例数据的使用状态

### 事件驱动架构
- **事件总线**: 每个模型实例都有独立的事件总线
- **更新通知**: 通过 "update" 事件通知数据变化
- **组件响应**: 组件监听模型事件并重新渲染

## 核心类详解

### Model - 模型基类
```javascript
class Model {
    constructor(env, params, services) {
        this.env = env;
        this.orm = services.orm;
        this.bus = new EventBus();
        this.setup(params, services);
    }
    
    setup(/* params, services */) {}
    async load(/* searchParams */) {}
    hasData() { return true; }
    getGroups() { return null; }
    notify() { this.bus.trigger("update"); }
}
```

**功能特性**:
- **抽象基类**: 定义所有模型的通用接口和行为
- **服务集成**: 自动注入所需的服务依赖
- **事件总线**: 内置事件系统支持数据变化通知
- **扩展点**: 提供多个扩展点供子类实现
- **生命周期**: 标准化的模型生命周期管理

**使用示例**:
```javascript
// 自定义模型实现
class CustomModel extends Model {
    setup(params, services) {
        this.resModel = params.resModel;
        this.fields = params.fields;
        this.data = [];
        this.count = 0;
    }
    
    async load(searchParams) {
        const { domain, context, groupBy, orderBy, limit, offset } = searchParams;
        
        try {
            // 构建搜索参数
            const searchArgs = {
                model: this.resModel,
                domain: domain || [],
                fields: Object.keys(this.fields),
                context: context || {},
                limit: limit || 80,
                offset: offset || 0
            };
            
            if (orderBy && orderBy.length) {
                searchArgs.order = orderBy.map(o => `${o.name} ${o.asc ? 'ASC' : 'DESC'}`).join(', ');
            }
            
            // 加载数据
            if (groupBy && groupBy.length) {
                await this.loadGroupedData(searchArgs, groupBy);
            } else {
                await this.loadListData(searchArgs);
            }
            
            // 通知数据已更新
            this.notify();
            
        } catch (error) {
            console.error('数据加载失败:', error);
            this.data = [];
            this.count = 0;
            throw error;
        }
    }
    
    async loadListData(searchArgs) {
        // 加载记录列表
        const result = await this.orm.searchRead(
            searchArgs.model,
            searchArgs.domain,
            searchArgs.fields,
            {
                context: searchArgs.context,
                limit: searchArgs.limit,
                offset: searchArgs.offset,
                order: searchArgs.order
            }
        );
        
        this.data = result;
        
        // 获取总数
        this.count = await this.orm.searchCount(
            searchArgs.model,
            searchArgs.domain,
            { context: searchArgs.context }
        );
    }
    
    async loadGroupedData(searchArgs, groupBy) {
        // 加载分组数据
        const groups = await this.orm.readGroup(
            searchArgs.model,
            searchArgs.domain,
            searchArgs.fields,
            groupBy,
            {
                context: searchArgs.context,
                limit: searchArgs.limit,
                offset: searchArgs.offset,
                orderby: searchArgs.order
            }
        );
        
        this.data = groups;
        this.count = groups.length;
    }
    
    hasData() {
        return this.data && this.data.length > 0;
    }
    
    getGroups() {
        return this.data.filter(item => item.__count > 0);
    }
    
    // 数据操作方法
    async createRecord(values) {
        const recordId = await this.orm.create(this.resModel, [values]);
        await this.reload();
        return recordId[0];
    }
    
    async updateRecord(recordId, values) {
        await this.orm.write(this.resModel, [recordId], values);
        await this.reload();
    }
    
    async deleteRecord(recordId) {
        await this.orm.unlink(this.resModel, [recordId]);
        await this.reload();
    }
    
    async reload() {
        // 重新加载当前数据
        await this.load(this.lastSearchParams);
    }
    
    // 获取记录
    getRecord(recordId) {
        return this.data.find(record => record.id === recordId);
    }
    
    // 获取所有记录
    getRecords() {
        return this.data;
    }
    
    // 获取记录数量
    getCount() {
        return this.count;
    }
}

// 注册模型服务依赖
CustomModel.services = ['orm', 'notification'];

// 在组件中使用自定义模型
class MyComponent extends Component {
    static template = xml`
        <div class="my-component">
            <div class="data-count">
                共 <span t-esc="model.getCount()" /> 条记录
            </div>
            <div class="data-list">
                <div t-foreach="model.getRecords()" t-as="record" t-key="record.id"
                     class="record-item">
                    <span t-esc="record.display_name" />
                </div>
            </div>
        </div>
    `;
    
    setup() {
        this.model = useModel(CustomModel, {
            resModel: this.props.resModel,
            fields: this.props.fields
        });
    }
}
```

## 核心Hook详解

### useModel() - 基础模型Hook
```javascript
function useModel(ModelClass, params, options = {}) {
    const component = useComponent();
    const services = {};
    for (const key of ModelClass.services) {
        services[key] = useService(key);
    }
    services.orm = services.orm || useService("orm");
    const model = new ModelClass(component.env, params, services);
    
    onWillStart(async () => {
        await options.beforeFirstLoad?.();
        return model.load(component.props);
    });
    
    onWillUpdateProps((nextProps) => model.load(nextProps));
    return model;
}
```

**功能特性**:
- **服务注入**: 自动注入模型所需的服务
- **生命周期**: 集成OWL组件生命周期
- **属性响应**: 自动响应组件属性变化
- **预加载**: 支持首次加载前的准备工作
- **简化使用**: 简化模型在组件中的使用

**使用示例**:
```javascript
// 基础模型使用
class ProductListComponent extends Component {
    static template = xml`
        <div class="product-list">
            <div t-if="model.hasData()" class="products">
                <div t-foreach="model.getRecords()" t-as="product" t-key="product.id"
                     class="product-card">
                    <h4 t-esc="product.name" />
                    <p t-esc="product.description" />
                    <span class="price" t-esc="product.list_price" />
                </div>
            </div>
            <div t-else="" class="no-data">
                暂无产品数据
            </div>
        </div>
    `;
    
    setup() {
        this.model = useModel(ProductModel, {
            resModel: 'product.product',
            fields: this.props.fields
        }, {
            beforeFirstLoad: async () => {
                console.log('准备加载产品数据...');
                // 可以在这里做一些准备工作
            }
        });
    }
}

// 带搜索的模型使用
class SearchableProductList extends Component {
    static template = xml`
        <div class="searchable-product-list">
            <div class="search-bar">
                <input type="text" 
                       t-model="searchQuery" 
                       t-on-input="onSearchInput"
                       placeholder="搜索产品..." />
            </div>
            <div class="product-results">
                <div t-foreach="model.getRecords()" t-as="product" t-key="product.id">
                    <span t-esc="product.name" />
                </div>
            </div>
        </div>
    `;
    
    setup() {
        this.searchQuery = useState('');
        
        this.model = useModel(ProductModel, {
            resModel: 'product.product',
            fields: ['name', 'description', 'list_price']
        });
    }
    
    onSearchInput() {
        // 更新搜索域
        const domain = this.searchQuery ? 
            [['name', 'ilike', this.searchQuery]] : [];
        
        // 触发模型重新加载
        this.model.load({ domain });
    }
}
```

### useModelWithSampleData() - 示例数据模型Hook
```javascript
function useModelWithSampleData(ModelClass, params, options = {}) {
    const component = useComponent();
    if (!(ModelClass.prototype instanceof Model)) {
        throw new Error(`the model class should extend Model`);
    }

    const services = {};
    for (const key of ModelClass.services) {
        services[key] = useService(key);
    }
    services.orm = services.orm || useService("orm");

    const model = new ModelClass(component.env, params, services);

    useBus(model.bus, "update", options.onUpdate || (() => {
        component.render(true);
    }));

    // 示例数据逻辑
    const globalState = component.props.globalState || {};
    const localState = component.props.state || {};
    let useSampleModel = component.props.useSampleModel &&
        (!("useSampleModel" in globalState) || globalState.useSampleModel);

    model.useSampleModel = useSampleModel;
    const orm = model.orm;
    let sampleORM = localState.sampleORM;
    let started = false;

    async function load(props) {
        const searchParams = getSearchParams(props);
        await model.load(searchParams);

        if (useSampleModel && !model.hasData()) {
            sampleORM = sampleORM || buildSampleORM(
                component.props.resModel,
                component.props.fields,
                user
            );

            // 使用示例ORM加载数据
            model.orm = sampleORM;
            await model.load(searchParams);
            model.orm = orm;
        } else {
            useSampleModel = false;
            model.useSampleModel = useSampleModel;
        }

        if (started) {
            model.notify();
        }
    }

    onWillStart(async () => {
        if (options.onWillStart) {
            await options.onWillStart();
        }
        await load(component.props);
        if (options.onWillStartAfterLoad) {
            await options.onWillStartAfterLoad();
        }
        started = true;
    });

    onWillUpdateProps((nextProps) => {
        useSampleModel = false;
        load(nextProps);
    });

    useSetupAction({
        getGlobalState() {
            if (component.props.useSampleModel) {
                return { useSampleModel };
            }
        },
        getLocalState: () => {
            return { sampleORM };
        },
    });

    return model;
}
```

**功能特性**:
- **示例数据**: 在无真实数据时自动使用示例数据
- **状态管理**: 管理示例数据的全局和本地状态
- **无缝切换**: 在真实数据和示例数据间无缝切换
- **事件监听**: 自动监听模型更新事件
- **动作集成**: 与搜索动作系统集成

## 工具函数详解

### getSearchParams() - 搜索参数提取
```javascript
function getSearchParams(props) {
    const params = {};
    for (const key of SEARCH_KEYS) {
        params[key] = props[key];
    }
    return params;
}
```

**功能特性**:
- **参数过滤**: 只提取搜索相关的参数
- **标准化**: 确保参数格式的一致性
- **解耦**: 将组件属性与搜索参数解耦
- **扩展性**: 基于SEARCH_KEYS常量，易于扩展

**SEARCH_KEYS 包含的参数**:
- `domain`: 搜索域
- `context`: 上下文
- `groupBy`: 分组字段
- `orderBy`: 排序字段
- `limit`: 限制数量
- `offset`: 偏移量

## 最佳实践

### 1. 模型设计原则
```javascript
// ✅ 推荐：清晰的职责分离
class WellDesignedModel extends Model {
    setup(params, services) {
        // 只在setup中初始化状态
        this.resModel = params.resModel;
        this.fields = params.fields;
        this.data = [];
        this.metadata = {};
    }

    async load(searchParams) {
        // 只在load中处理数据加载
        await this.loadData(searchParams);
        await this.loadMetadata(searchParams);
        this.notify();
    }

    // 数据访问方法
    getRecords() { return this.data; }
    getMetadata() { return this.metadata; }

    // 业务逻辑方法
    async createRecord(values) { /* ... */ }
    async updateRecord(id, values) { /* ... */ }
}
```

### 2. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
class RobustModel extends Model {
    async load(searchParams) {
        try {
            await this.loadData(searchParams);
        } catch (error) {
            console.error('数据加载失败:', error);
            this.handleLoadError(error);
            throw error;
        }
    }

    handleLoadError(error) {
        this.data = [];
        this.count = 0;
        this.error = error;
        this.notify();
    }

    hasError() {
        return !!this.error;
    }

    getError() {
        return this.error;
    }
}
```

## 总结

Odoo 模型基类模块提供了强大的数据模型基础设施：

**核心优势**:
- **标准化**: 统一的模型接口和生命周期
- **示例数据**: 内置示例数据支持，改善用户体验
- **事件驱动**: 基于事件总线的响应式架构
- **Hook集成**: 与OWL组件系统深度集成
- **扩展性**: 灵活的扩展点和继承机制

**适用场景**:
- 数据列表视图
- 仪表板组件
- 表单数据管理
- 搜索和过滤
- 分页和排序

**设计优势**:
- 抽象基类设计
- 生命周期管理
- 服务依赖注入
- 状态管理集成

这个模型基类为 Odoo Web 客户端提供了强大的数据层基础，是构建数据驱动应用的重要基石。

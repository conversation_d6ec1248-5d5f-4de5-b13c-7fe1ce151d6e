# Odoo 记录组件 (Record Component) 学习资料

## 文件概述

**文件路径**: `output/@web/model/record.js`  
**原始路径**: `/web/static/src/model/record.js`  
**模块类型**: 核心模型模块 - 记录组件  
**代码行数**: 190 行  
**依赖关系**: 
- `@web/core/utils/hooks` - Hook工具 (useService)
- `@web/core/utils/objects` - 对象工具 (pick)
- `@web/model/relational_model/relational_model` - 关系模型 (RelationalModel)
- `@web/model/relational_model/utils` - 关系模型工具 (getFieldsSpec)
- `@odoo/owl` - OWL框架 (Component, xml, onWillStart, onWillUpdateProps, useState)

## 模块功能

记录组件模块是 Odoo Web 客户端的核心记录处理组件。该模块提供了：
- 独立记录组件封装
- 关系字段数据预处理
- 字段规格管理
- 记录生命周期管理
- 动态字段加载
- 记录状态管理

这个模块是构建表单视图、记录详情等单记录操作界面的重要基础，提供了完整的记录数据管理和字段处理能力。

## 记录组件架构

### 组件层次结构
```
Record (外层组件)
├── 字段定义管理
├── 属性验证
└── _Record (内层组件)
    ├── StandaloneRelationalModel
    ├── 字段数据预处理
    └── 记录生命周期管理
```

### 数据流程
1. **字段定义**: 获取或加载字段定义信息
2. **活动字段**: 处理活动字段配置
3. **数据预处理**: 处理关系字段的数据加载
4. **模型创建**: 创建独立的关系模型实例
5. **记录加载**: 加载或创建记录数据
6. **状态管理**: 管理记录的状态变化

### 关系字段处理
- **Many2one**: 自动加载显示名称
- **One2many/Many2many**: 预加载相关记录数据
- **字段规格**: 根据活动字段生成字段规格
- **上下文传递**: 正确传递字段上下文

## 核心类详解

### StandaloneRelationalModel - 独立关系模型
```javascript
class StandaloneRelationalModel extends RelationalModel {
    load(params = {}) {
        if (params.values) {
            const data = params.values;
            const config = this._getNextConfig(this.config, params);
            this.root = this._createRoot(config, data);
            this.config = config;
            return;
        }
        return super.load(params);
    }
}
```

**功能特性**:
- **值加载**: 支持直接从值创建记录
- **配置更新**: 动态更新模型配置
- **根记录**: 创建独立的根记录实例
- **继承扩展**: 基于RelationalModel的扩展
- **灵活加载**: 支持多种数据加载方式

**使用场景**:
- 表单视图中的记录编辑
- 弹窗中的记录创建
- 嵌入式记录组件
- 独立的记录操作

### _Record - 内层记录组件
```javascript
class _Record extends Component {
    static template = xml`<t t-slot="default" record="model.root"/>`;
    static props = ["slots", "info", "fields", "values?"];
    
    setup() {
        this.orm = useService("orm");
        const resModel = this.props.info.resModel;
        const activeFields = this.getActiveFields();
        
        const modelParams = {
            config: {
                resModel,
                fields: this.props.fields,
                isMonoRecord: true,
                activeFields,
                resId: this.props.info.resId,
                mode: this.props.info.mode,
            },
            hooks: {
                onRecordSaved: this.props.info.onRecordSaved || (() => {}),
                onWillSaveRecord: this.props.info.onWillSaveRecord || (() => {}),
                onRecordChanged: this.props.info.onRecordChanged || (() => {}),
            },
        };
        
        // 创建模型实例
        const modelServices = Object.fromEntries(
            StandaloneRelationalModel.services.map((servName) => {
                return [servName, useService(servName)];
            })
        );
        modelServices.orm = this.orm;
        this.model = useState(new StandaloneRelationalModel(this.env, modelParams, modelServices));
    }
}
```

**功能特性**:
- **插槽模板**: 使用插槽传递记录到子组件
- **服务注入**: 自动注入所需的服务
- **状态管理**: 使用useState管理模型状态
- **回调支持**: 支持记录保存、变更等回调
- **单记录模式**: 专门处理单个记录的操作

**使用示例**:
```javascript
// 基本记录组件使用
class ProductFormComponent extends Component {
    static template = xml`
        <div class="product-form">
            <Record resModel="'product.product'" 
                    t-slot-scope="{ record }"
                    resId="productId"
                    fieldNames="['name', 'description', 'list_price', 'category_id']"
                    onRecordSaved="onProductSaved"
                    onRecordChanged="onProductChanged">
                
                <div class="form-group">
                    <label>产品名称</label>
                    <Field name="'name'" record="record" />
                </div>
                
                <div class="form-group">
                    <label>产品描述</label>
                    <Field name="'description'" record="record" />
                </div>
                
                <div class="form-group">
                    <label>价格</label>
                    <Field name="'list_price'" record="record" />
                </div>
                
                <div class="form-group">
                    <label>分类</label>
                    <Field name="'category_id'" record="record" />
                </div>
                
                <div class="form-actions">
                    <button t-on-click="() => record.save()" 
                            class="btn btn-primary">
                        保存
                    </button>
                    <button t-on-click="() => record.discard()" 
                            class="btn btn-secondary">
                        取消
                    </button>
                </div>
            </Record>
        </div>
    `;
    
    setup() {
        this.productId = this.props.productId;
    }
    
    onProductSaved(record) {
        console.log('产品已保存:', record.data);
        this.env.services.notification.add('产品保存成功', { type: 'success' });
    }
    
    onProductChanged(record) {
        console.log('产品数据已变更:', record.data);
    }
}

// 带初始值的记录组件
class CreateProductComponent extends Component {
    static template = xml`
        <div class="create-product">
            <Record resModel="'product.product'" 
                    t-slot-scope="{ record }"
                    fieldNames="['name', 'description', 'list_price']"
                    values="initialValues"
                    onRecordSaved="onProductCreated">
                
                <form class="product-form">
                    <div class="form-group">
                        <label>产品名称 *</label>
                        <Field name="'name'" record="record" required="true" />
                    </div>
                    
                    <div class="form-group">
                        <label>产品描述</label>
                        <Field name="'description'" record="record" type="'text'" />
                    </div>
                    
                    <div class="form-group">
                        <label>销售价格</label>
                        <Field name="'list_price'" record="record" />
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" 
                                t-on-click="() => this.createProduct(record)" 
                                class="btn btn-primary">
                            创建产品
                        </button>
                    </div>
                </form>
            </Record>
        </div>
    `;
    
    setup() {
        this.initialValues = {
            name: '',
            description: '',
            list_price: 0.0
        };
    }
    
    async createProduct(record) {
        try {
            await record.save();
            this.env.services.notification.add('产品创建成功', { type: 'success' });
        } catch (error) {
            console.error('产品创建失败:', error);
            this.env.services.notification.add('产品创建失败', { type: 'danger' });
        }
    }
    
    onProductCreated(record) {
        console.log('新产品已创建:', record.data);
        // 可以在这里进行页面跳转或其他操作
        this.env.services.action.doAction({
            type: 'ir.actions.act_window',
            res_model: 'product.product',
            res_id: record.resId,
            views: [[false, 'form']],
            target: 'current'
        });
    }
}

// 复杂关系字段的记录组件
class OrderFormComponent extends Component {
    static template = xml`
        <div class="order-form">
            <Record resModel="'sale.order'" 
                    t-slot-scope="{ record }"
                    resId="orderId"
                    activeFields="orderActiveFields"
                    onRecordSaved="onOrderSaved">
                
                <div class="order-header">
                    <div class="row">
                        <div class="col-md-6">
                            <label>客户</label>
                            <Field name="'partner_id'" record="record" />
                        </div>
                        <div class="col-md-6">
                            <label>订单日期</label>
                            <Field name="'date_order'" record="record" />
                        </div>
                    </div>
                </div>
                
                <div class="order-lines">
                    <h4>订单明细</h4>
                    <Field name="'order_line'" record="record" mode="'tree,form'">
                        <t t-set-slot="tree_view">
                            <tree editable="bottom">
                                <field name="product_id" />
                                <field name="product_uom_qty" />
                                <field name="price_unit" />
                                <field name="price_subtotal" />
                            </tree>
                        </t>
                    </Field>
                </div>
                
                <div class="order-totals">
                    <div class="total-line">
                        <strong>总计: <Field name="'amount_total'" record="record" readonly="true" /></strong>
                    </div>
                </div>
            </Record>
        </div>
    `;
    
    setup() {
        this.orderId = this.props.orderId;
        
        // 定义活动字段配置
        this.orderActiveFields = {
            partner_id: {
                domain: "[]",
                context: "{}",
                options: {}
            },
            date_order: {
                domain: "[]",
                context: "{}",
                options: {}
            },
            order_line: {
                domain: "[]",
                context: "{}",
                options: {},
                related: {
                    fields: {
                        product_id: { type: 'many2one', relation: 'product.product' },
                        product_uom_qty: { type: 'float' },
                        price_unit: { type: 'float' },
                        price_subtotal: { type: 'monetary' }
                    },
                    activeFields: {
                        product_id: { domain: "[]", context: "{}" },
                        product_uom_qty: { domain: "[]", context: "{}" },
                        price_unit: { domain: "[]", context: "{}" },
                        price_subtotal: { domain: "[]", context: "{}" }
                    }
                }
            },
            amount_total: {
                domain: "[]",
                context: "{}",
                options: { readonly: true }
            }
        };
    }
    
    onOrderSaved(record) {
        console.log('订单已保存:', record.data);
        this.env.services.notification.add('订单保存成功', { type: 'success' });
    }
}
```

## 数据预处理机制

### prepareLoadWithValues() - 值预处理函数
```javascript
const prepareLoadWithValues = async (values) => {
    values = pick(values, ...Object.keys(modelParams.config.activeFields));
    const proms = [];

    for (const fieldName in values) {
        // 处理 One2many/Many2many 字段
        if (["one2many", "many2many"].includes(this.props.fields[fieldName].type)) {
            if (values[fieldName].length && typeof values[fieldName][0] === "number") {
                const resModel = this.props.fields[fieldName].relation;
                const resIds = values[fieldName];
                const activeField = modelParams.config.activeFields[fieldName];

                if (activeField.related) {
                    const { activeFields, fields } = activeField.related;
                    const fieldSpec = getFieldsSpec(activeFields, fields, {});
                    const kwargs = {
                        context: activeField.context || {},
                        specification: fieldSpec,
                    };

                    proms.push(
                        this.orm.webRead(resModel, resIds, kwargs).then((records) => {
                            values[fieldName] = records;
                        })
                    );
                }
            }
        }

        // 处理 Many2one 字段
        if (this.props.fields[fieldName].type === "many2one") {
            const loadDisplayName = async (resId) => {
                const resModel = this.props.fields[fieldName].relation;
                const activeField = modelParams.config.activeFields[fieldName];
                const kwargs = {
                    context: activeField.context || {},
                    specification: { display_name: {} },
                };
                const records = await this.orm.webRead(resModel, [resId], kwargs);
                return records[0].display_name;
            };

            if (typeof values[fieldName] === "number") {
                const prom = loadDisplayName(values[fieldName]);
                prom.then((displayName) => {
                    values[fieldName] = {
                        id: values[fieldName],
                        display_name: displayName,
                    };
                });
                proms.push(prom);
            } else if (Array.isArray(values[fieldName])) {
                if (values[fieldName][1] === undefined) {
                    const prom = loadDisplayName(values[fieldName][0]);
                    prom.then((displayName) => {
                        values[fieldName] = {
                            id: values[fieldName][0],
                            display_name: displayName,
                        };
                    });
                    proms.push(prom);
                }
                values[fieldName] = {
                    id: values[fieldName][0],
                    display_name: values[fieldName][1],
                };
            }
        }

        await Promise.all(proms);
    }
    return values;
};
```

**功能特性**:
- **字段过滤**: 只处理活动字段中定义的字段
- **关系字段**: 自动加载关系字段的相关数据
- **显示名称**: 自动获取Many2one字段的显示名称
- **异步处理**: 并行处理多个字段的数据加载
- **数据格式**: 统一关系字段的数据格式

### getActiveFields() - 活动字段处理
```javascript
getActiveFields() {
    if (this.props.info.activeFields) {
        const activeFields = {};
        for (const [fName, fInfo] of Object.entries(this.props.info.activeFields)) {
            activeFields[fName] = { ...defaultActiveField, ...fInfo };
        }
        return activeFields;
    }
    return Object.fromEntries(
        this.props.info.fieldNames.map((f) => [f, { ...defaultActiveField }])
    );
}
```

**功能特性**:
- **默认配置**: 为每个字段提供默认的活动字段配置
- **配置合并**: 将用户配置与默认配置合并
- **字段映射**: 将字段名映射到活动字段配置
- **灵活性**: 支持部分字段配置或完整配置

## 最佳实践

### 1. 字段配置优化
```javascript
// ✅ 推荐：合理配置活动字段
const optimizedActiveFields = {
    name: {
        domain: "[]",
        context: "{}",
        options: { required: true }
    },
    partner_id: {
        domain: "[('is_company', '=', True)]",
        context: "{'default_is_company': True}",
        options: { no_create: true }
    },
    order_line: {
        domain: "[]",
        context: "{}",
        options: {},
        related: {
            fields: orderLineFields,
            activeFields: orderLineActiveFields
        }
    }
};
```

### 2. 性能优化
```javascript
// ✅ 推荐：批量加载相关数据
const prepareLoadWithValues = async (values) => {
    const proms = [];
    const many2oneFields = [];
    const x2manyFields = [];

    // 分类字段类型
    for (const fieldName in values) {
        const fieldType = this.props.fields[fieldName].type;
        if (fieldType === "many2one") {
            many2oneFields.push(fieldName);
        } else if (["one2many", "many2many"].includes(fieldType)) {
            x2manyFields.push(fieldName);
        }
    }

    // 批量处理相同类型的字段
    if (many2oneFields.length) {
        proms.push(this.batchLoadMany2oneFields(values, many2oneFields));
    }

    if (x2manyFields.length) {
        proms.push(this.batchLoadX2manyFields(values, x2manyFields));
    }

    await Promise.all(proms);
    return values;
};
```

### 3. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
class RobustRecord extends Component {
    async loadRecord() {
        try {
            await this.model.load();
        } catch (error) {
            console.error('记录加载失败:', error);
            this.handleLoadError(error);
        }
    }

    handleLoadError(error) {
        if (error.code === 'access_denied') {
            this.env.services.notification.add('没有访问权限', { type: 'danger' });
        } else if (error.code === 'record_not_found') {
            this.env.services.notification.add('记录不存在', { type: 'warning' });
        } else {
            this.env.services.notification.add('加载失败', { type: 'danger' });
        }
    }
}
```

## 总结

Odoo 记录组件模块提供了强大的单记录处理能力：

**核心优势**:
- **组件封装**: 完整的记录组件封装，易于使用
- **数据预处理**: 智能的关系字段数据预处理
- **生命周期**: 完整的记录生命周期管理
- **灵活配置**: 支持多种配置和使用方式
- **状态管理**: 集成的状态管理和事件处理

**适用场景**:
- 表单视图
- 记录详情页
- 弹窗编辑
- 嵌入式记录
- 记录创建向导

**设计优势**:
- 双层组件架构
- 独立模型实例
- 自动数据预处理
- 插槽模板设计

这个记录组件为 Odoo Web 客户端提供了强大的单记录操作基础，是构建表单和记录相关界面的重要组件。

# SelectCreateDialog - 选择创建对话框

## 概述

`select_create_dialog.js` 是 Odoo Web 客户端的选择创建对话框组件，负责提供记录选择和创建功能。该模块包含136行代码，是一个功能完整的对话框组件，专门用于在对话框中选择现有记录或创建新记录，具备多选支持、搜索过滤、视图切换、创建编辑等特性，是记录选择和关联的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/view_dialogs/select_create_dialog.js`
- **行数**: 136
- **模块**: `@web/views/view_dialogs/select_create_dialog`

## 依赖关系

```javascript
// 核心依赖
'@web/core/dialog/dialog'                      // 对话框组件
'@web/core/utils/hooks'                        // 工具钩子
'@web/core/utils/render'                       // 渲染工具
'@web/views/view'                              // 视图组件
'@web/views/view_dialogs/form_view_dialog'     // 表单视图对话框
'@odoo/owl'                                    // OWL框架
'@web/core/registry'                           // 注册表
```

## 核心功能

### 1. 组件定义

```javascript
class SelectCreateDialog extends Component {
    static components = { Dialog, View };
    static template = "web.SelectCreateDialog";
    static props = {
        context: { type: Object, optional: true },
        domain: { type: Array, optional: true },
        dynamicFilters: { type: Array, optional: true },
        resModel: String,
        searchViewId: { type: [Number, { value: false }], optional: true },
        multiSelect: { type: Boolean, optional: true },
        onSelected: { type: Function, optional: true },
        close: { type: Function, optional: true },
        onCreateEdit: { type: Function, optional: true },
        title: { type: String, optional: true },
        noCreate: { type: Boolean, optional: true },
        onUnselect: { type: Function, optional: true },
        noContentHelp: { type: String, optional: true },
    };
    static defaultProps = {
        dynamicFilters: [],
        multiSelect: true,
        searchViewId: false,
        domain: [],
        context: {},
    };
}
```

**组件特性**:
- **对话框集成**: 集成Dialog和View组件
- **模型配置**: 支持资源模型和搜索视图配置
- **多选支持**: 支持单选和多选模式
- **过滤支持**: 支持域和动态过滤器
- **回调处理**: 支持选择、取消选择、创建回调
- **创建控制**: 支持禁用创建功能

### 2. 组件初始化

```javascript
setup() {
    this.viewService = useService("view");
    this.dialogService = useService("dialog");
    this.state = useState({ resIds: [] });
    const noContentHelp = this.props.noContentHelp || getDefaultNoContentHelp();
    this.busy = false;
    this.baseViewProps = {
        display: { searchPanel: false },
        editable: false,
        noBreadcrumbs: true,
        noContentHelp,
        showButtons: false,
        selectRecord: (resId) => this.select([resId]),
        onSelectionChanged: (resIds) => {
            this.state.resIds = resIds;
        },
    };
}
```

**初始化功能**:
- **服务注入**: 注入视图和对话框服务
- **状态管理**: 管理选中记录ID状态
- **忙碌标志**: 防止重复操作的忙碌标志
- **基础属性**: 配置基础视图属性
- **选择回调**: 设置记录选择回调

### 3. 视图属性

```javascript
get viewProps() {
    const type = this.env.isSmall ? "kanban" : "list";
    const props = {
        loadIrFilters: true,
        ...this.baseViewProps,
        context: this.props.context,
        domain: this.props.domain,
        dynamicFilters: this.props.dynamicFilters,
        resModel: this.props.resModel,
        searchViewId: this.props.searchViewId,
        type,
    };
    if (type === "list") {
        props.allowSelectors = this.props.multiSelect;
    } else if (type === "kanban") {
        props.forceGlobalClick = true;
    }
    return props;
}
```

**视图属性功能**:
- **响应式类型**: 根据屏幕尺寸选择视图类型
- **属性合并**: 合并基础属性和传入属性
- **类型特定**: 根据视图类型设置特定属性
- **选择器控制**: 控制列表视图的选择器显示
- **点击控制**: 控制看板视图的全局点击

### 4. 选择功能

```javascript
async executeOnceAndClose(callback) {
    if (!this.busy) {
        this.busy = true;
        try {
            await callback();
        } catch (e) {
            this.busy = false;
            throw e;
        }
        this.props.close();
    }
}

async select(resIds) {
    if (this.props.onSelected) {
        this.executeOnceAndClose(() => this.props.onSelected(resIds));
    }
}

async unselect() {
    if (this.props.onUnselect) {
        this.executeOnceAndClose(() => this.props.onUnselect());
    }
}
```

**选择功能**:
- **防重复**: 防止重复执行选择操作
- **异步处理**: 支持异步选择处理
- **错误处理**: 处理选择过程中的错误
- **自动关闭**: 选择后自动关闭对话框
- **取消选择**: 支持取消选择功能

### 5. 创建编辑功能

```javascript
async createEditRecord() {
    if (this.props.onCreateEdit) {
        await this.props.onCreateEdit();
        this.props.close();
    } else {
        this.dialogService.add(FormViewDialog, {
            context: this.props.context,
            resModel: this.props.resModel,
            onRecordSaved: (record) => {
                this.props.onSelected([record.resId]);
                this.props.close();
            },
        });
    }
}
```

**创建编辑功能**:
- **自定义创建**: 支持自定义创建回调
- **默认创建**: 提供默认的表单创建功能
- **保存处理**: 处理新记录保存后的选择
- **对话框链**: 支持对话框链式调用

## 使用场景

### 1. 选择创建对话框管理器

```javascript
// 选择创建对话框管理器
class SelectCreateDialogManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置对话框配置
        this.dialogConfig = {
            enableMultiSelect: true,
            enableSearch: true,
            enableFilters: true,
            enableCreate: true,
            enableQuickCreate: false,
            defaultViewType: 'auto', // auto, list, kanban
            maxSelections: 100,
            minSelections: 0
        };
        
        // 设置视图类型配置
        this.viewTypeConfig = new Map([
            ['list', {
                name: 'List View',
                allowSelectors: true,
                supportsPaging: true,
                supportsGrouping: true,
                defaultLimit: 80
            }],
            ['kanban', {
                name: 'Kanban View',
                forceGlobalClick: true,
                supportsPaging: true,
                supportsGrouping: false,
                defaultLimit: 40
            }],
            ['tree', {
                name: 'Tree View',
                allowSelectors: true,
                supportsPaging: true,
                supportsGrouping: true,
                defaultLimit: 80
            }]
        ]);
        
        // 设置选择模式
        this.selectionModes = new Map([
            ['single', {
                name: 'Single Selection',
                multiSelect: false,
                maxSelections: 1,
                showSelectors: false
            }],
            ['multiple', {
                name: 'Multiple Selection',
                multiSelect: true,
                maxSelections: 100,
                showSelectors: true
            }],
            ['range', {
                name: 'Range Selection',
                multiSelect: true,
                maxSelections: 1000,
                showSelectors: true,
                supportRangeSelect: true
            }]
        ]);
        
        // 设置对话框统计
        this.dialogStatistics = {
            totalDialogs: 0,
            dialogsByModel: new Map(),
            dialogsByMode: new Map(),
            selectOperations: 0,
            createOperations: 0,
            unselectOperations: 0,
            averageSelections: 0,
            totalSelections: 0
        };
        
        this.initializeSelectCreateSystem();
    }
    
    // 初始化选择创建系统
    initializeSelectCreateSystem() {
        // 创建增强的选择创建对话框
        this.createEnhancedSelectCreateDialog();
        
        // 设置选择系统
        this.setupSelectionSystem();
        
        // 设置创建系统
        this.setupCreationSystem();
        
        // 设置过滤系统
        this.setupFilterSystem();
    }
    
    // 创建增强的选择创建对话框
    createEnhancedSelectCreateDialog() {
        const originalDialog = SelectCreateDialog;
        
        this.EnhancedSelectCreateDialog = class extends originalDialog {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
                
                // 添加统计功能
                this.addStatisticsFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    selectedCount: 0,
                    maxReached: false,
                    isSearching: false,
                    searchQuery: '',
                    lastSelectionTime: null,
                    selectionHistory: [],
                    favoriteRecords: new Set()
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的选择功能
                this.enhancedSelect = async (resIds) => {
                    try {
                        // 验证选择
                        const validation = this.validateSelection(resIds);
                        if (!validation.valid) {
                            this.showValidationError(validation.error);
                            return;
                        }
                        
                        // 记录选择
                        this.recordSelection(resIds);
                        
                        // 执行原始选择
                        await this.select(resIds);
                        
                    } catch (error) {
                        this.handleSelectionError(error);
                    }
                };
                
                // 验证选择
                this.validateSelection = (resIds) => {
                    const currentCount = this.state.resIds.length;
                    const newCount = resIds.length;
                    const totalCount = this.props.multiSelect ? 
                        currentCount + newCount : newCount;
                    
                    // 检查最大选择数
                    if (totalCount > this.dialogConfig.maxSelections) {
                        return {
                            valid: false,
                            error: `Maximum ${this.dialogConfig.maxSelections} selections allowed`
                        };
                    }
                    
                    // 检查最小选择数
                    if (totalCount < this.dialogConfig.minSelections) {
                        return {
                            valid: false,
                            error: `Minimum ${this.dialogConfig.minSelections} selections required`
                        };
                    }
                    
                    return { valid: true };
                };
                
                // 显示验证错误
                this.showValidationError = (error) => {
                    const notification = useService("notification");
                    notification.add(error, { type: 'warning' });
                };
                
                // 增强的创建编辑
                this.enhancedCreateEditRecord = async () => {
                    try {
                        // 记录创建操作
                        this.recordCreateOperation();
                        
                        // 执行原始创建
                        await this.createEditRecord();
                        
                    } catch (error) {
                        this.handleCreateError(error);
                    }
                };
                
                // 快速创建
                this.quickCreate = async (data) => {
                    if (!this.dialogConfig.enableQuickCreate) {
                        return this.enhancedCreateEditRecord();
                    }
                    
                    try {
                        const orm = useService("orm");
                        const record = await orm.create(this.props.resModel, data, {
                            context: this.props.context
                        });
                        
                        if (this.props.onSelected) {
                            await this.props.onSelected([record]);
                        }
                        
                        this.props.close();
                        
                    } catch (error) {
                        this.handleCreateError(error);
                    }
                };
                
                // 批量选择
                this.batchSelect = async (criteria) => {
                    try {
                        const orm = useService("orm");
                        const resIds = await orm.search(this.props.resModel, criteria, {
                            context: this.props.context,
                            limit: this.dialogConfig.maxSelections
                        });
                        
                        await this.enhancedSelect(resIds);
                        
                    } catch (error) {
                        this.handleSelectionError(error);
                    }
                };
                
                // 添加到收藏
                this.addToFavorites = (resId) => {
                    this.enhancedState.favoriteRecords.add(resId);
                };
                
                // 从收藏移除
                this.removeFromFavorites = (resId) => {
                    this.enhancedState.favoriteRecords.delete(resId);
                };
                
                // 获取推荐记录
                this.getRecommendedRecords = async () => {
                    // 基于历史选择和收藏推荐记录
                    const recentSelections = this.enhancedState.selectionHistory
                        .slice(-10)
                        .map(s => s.resIds)
                        .flat();
                    
                    const favorites = Array.from(this.enhancedState.favoriteRecords);
                    
                    return [...new Set([...recentSelections, ...favorites])];
                };
                
                // 获取对话框信息
                this.getDialogInfo = () => {
                    return {
                        resModel: this.props.resModel,
                        selectedCount: this.state.resIds.length,
                        maxSelections: this.dialogConfig.maxSelections,
                        multiSelect: this.props.multiSelect,
                        viewType: this.env.isSmall ? 'kanban' : 'list',
                        isSearching: this.enhancedState.isSearching,
                        searchQuery: this.enhancedState.searchQuery,
                        favoriteCount: this.enhancedState.favoriteRecords.size,
                        selectionHistory: this.enhancedState.selectionHistory.slice(-5)
                    };
                };
                
                // 记录选择
                this.recordSelection = (resIds) => {
                    this.enhancedState.selectedCount += resIds.length;
                    this.enhancedState.lastSelectionTime = new Date();
                    
                    // 记录选择历史
                    this.enhancedState.selectionHistory.push({
                        resIds: resIds,
                        timestamp: new Date(),
                        count: resIds.length
                    });
                    
                    // 限制历史大小
                    if (this.enhancedState.selectionHistory.length > 20) {
                        this.enhancedState.selectionHistory.shift();
                    }
                    
                    // 更新统计
                    this.dialogStatistics.selectOperations++;
                    this.dialogStatistics.totalSelections += resIds.length;
                    this.updateAverageSelections();
                };
                
                // 记录创建操作
                this.recordCreateOperation = () => {
                    this.dialogStatistics.createOperations++;
                };
                
                // 更新平均选择数
                this.updateAverageSelections = () => {
                    if (this.dialogStatistics.selectOperations > 0) {
                        this.dialogStatistics.averageSelections = 
                            this.dialogStatistics.totalSelections / this.dialogStatistics.selectOperations;
                    }
                };
                
                // 处理选择错误
                this.handleSelectionError = (error) => {
                    console.error('Selection error:', error);
                    const notification = useService("notification");
                    notification.add(
                        error.message || 'Selection failed',
                        { type: 'danger' }
                    );
                };
                
                // 处理创建错误
                this.handleCreateError = (error) => {
                    console.error('Create error:', error);
                    const notification = useService("notification");
                    notification.add(
                        error.message || 'Create failed',
                        { type: 'danger' }
                    );
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: true,
                    validate: (resIds) => this.validateSelection(resIds),
                    maxSelections: this.dialogConfig.maxSelections,
                    minSelections: this.dialogConfig.minSelections
                };
            }
            
            addStatisticsFeatures() {
                // 统计功能
                this.statisticsManager = {
                    enabled: true,
                    getInfo: () => this.getDialogInfo(),
                    recordSelection: (resIds) => this.recordSelection(resIds),
                    recordCreate: () => this.recordCreateOperation()
                };
            }
            
            // 重写原始方法
            async select(resIds) {
                return await this.enhancedSelect(resIds);
            }
            
            async createEditRecord() {
                return await this.enhancedCreateEditRecord();
            }
        };
    }
    
    // 设置选择系统
    setupSelectionSystem() {
        this.selectionSystemConfig = {
            enabled: true,
            modes: this.selectionModes,
            maxSelections: this.dialogConfig.maxSelections
        };
    }
    
    // 设置创建系统
    setupCreationSystem() {
        this.creationSystemConfig = {
            enabled: this.dialogConfig.enableCreate,
            quickCreate: this.dialogConfig.enableQuickCreate
        };
    }
    
    // 设置过滤系统
    setupFilterSystem() {
        this.filterSystemConfig = {
            enabled: this.dialogConfig.enableFilters,
            enableSearch: this.dialogConfig.enableSearch,
            enableDynamicFilters: true
        };
    }
    
    // 创建选择创建对话框
    createSelectCreateDialog(props) {
        const dialog = new this.EnhancedSelectCreateDialog(props);
        this.dialogStatistics.totalDialogs++;
        
        // 记录模型统计
        const model = props.resModel;
        const count = this.dialogStatistics.dialogsByModel.get(model) || 0;
        this.dialogStatistics.dialogsByModel.set(model, count + 1);
        
        // 记录模式统计
        const mode = props.multiSelect ? 'multiple' : 'single';
        const modeCount = this.dialogStatistics.dialogsByMode.get(mode) || 0;
        this.dialogStatistics.dialogsByMode.set(mode, modeCount + 1);
        
        return dialog;
    }
    
    // 注册视图类型
    registerViewType(name, config) {
        this.viewTypeConfig.set(name, config);
    }
    
    // 获取对话框统计
    getDialogStatistics() {
        return {
            ...this.dialogStatistics,
            modelVariety: this.dialogStatistics.dialogsByModel.size,
            modeVariety: this.dialogStatistics.dialogsByMode.size,
            selectRate: this.dialogStatistics.selectOperations / 
                       Math.max(this.dialogStatistics.totalDialogs, 1),
            createRate: this.dialogStatistics.createOperations / 
                       Math.max(this.dialogStatistics.totalDialogs, 1)
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理视图类型配置
        this.viewTypeConfig.clear();
        
        // 清理选择模式
        this.selectionModes.clear();
        
        // 清理统计
        this.dialogStatistics.dialogsByModel.clear();
        this.dialogStatistics.dialogsByMode.clear();
        
        // 重置统计
        this.dialogStatistics = {
            totalDialogs: 0,
            dialogsByModel: new Map(),
            dialogsByMode: new Map(),
            selectOperations: 0,
            createOperations: 0,
            unselectOperations: 0,
            averageSelections: 0,
            totalSelections: 0
        };
    }
}

// 使用示例
const selectCreateManager = new SelectCreateDialogManager();

// 创建选择创建对话框
const selectDialog = selectCreateManager.createSelectCreateDialog({
    resModel: 'res.partner',
    title: 'Select Partners',
    multiSelect: true,
    domain: [['is_company', '=', true]],
    onSelected: (resIds) => {
        console.log('Selected records:', resIds);
    },
    close: () => {
        console.log('Dialog closed');
    }
});

// 获取统计信息
const stats = selectCreateManager.getDialogStatistics();
console.log('Select create dialog statistics:', stats);
```

## 技术特点

### 1. 双重功能
- **选择功能**: 从现有记录中选择
- **创建功能**: 创建新记录并选择
- **灵活切换**: 在选择和创建之间灵活切换
- **统一界面**: 提供统一的用户界面

### 2. 视图适配
- **响应式**: 根据屏幕尺寸自动选择视图类型
- **多视图**: 支持列表和看板视图
- **配置灵活**: 灵活的视图配置选项
- **交互优化**: 针对不同视图优化交互

### 3. 选择控制
- **单选多选**: 支持单选和多选模式
- **选择验证**: 验证选择的有效性
- **状态管理**: 管理选择状态
- **防重复**: 防止重复操作

### 4. 集成能力
- **对话框链**: 支持对话框链式调用
- **服务集成**: 集成多种系统服务
- **注册机制**: 支持注册到对话框注册表
- **扩展性**: 良好的扩展性

## 设计模式

### 1. 组合模式 (Composition Pattern)
- **组件组合**: 组合对话框和视图组件
- **功能组合**: 组合选择和创建功能
- **服务组合**: 组合多种服务

### 2. 策略模式 (Strategy Pattern)
- **视图策略**: 不同的视图类型策略
- **选择策略**: 不同的选择模式策略
- **创建策略**: 不同的创建方式策略

### 3. 观察者模式 (Observer Pattern)
- **选择观察**: 观察选择状态变化
- **回调观察**: 观察操作回调
- **状态观察**: 观察组件状态变化

### 4. 工厂模式 (Factory Pattern)
- **对话框工厂**: 创建不同配置的对话框
- **视图工厂**: 创建不同类型的视图
- **组件工厂**: 创建不同的组件实例

## 注意事项

1. **性能考虑**: 处理大量记录时的性能
2. **内存管理**: 及时清理对话框资源
3. **用户体验**: 提供清晰的选择反馈
4. **错误处理**: 完善的错误处理机制

## 扩展建议

1. **高级搜索**: 添加高级搜索功能
2. **批量操作**: 支持批量选择操作
3. **收藏功能**: 支持记录收藏功能
4. **历史记录**: 记录选择历史
5. **快速创建**: 增强快速创建功能

该选择创建对话框为Odoo Web客户端提供了完整的记录选择和创建解决方案，通过灵活的配置和丰富的功能确保了记录关联的便利性和准确性。

# FormViewDialog - 表单视图对话框

## 概述

`form_view_dialog.js` 是 Odoo Web 客户端的表单视图对话框组件，负责在对话框中显示表单视图。该模块包含131行代码，是一个功能完整的对话框组件，专门用于在弹窗中展示和编辑记录，具备表单视图集成、记录保存、记录丢弃、展开功能、模式切换等特性，是模态表单编辑的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/view_dialogs/form_view_dialog.js`
- **行数**: 131
- **模块**: `@web/views/view_dialogs/form_view_dialog`

## 依赖关系

```javascript
// 核心依赖
'@web/core/dialog/dialog'              // 对话框组件
'@web/core/utils/hooks'                // 工具钩子
'@web/search/action_hook'              // 动作钩子
'@web/views/view'                      // 视图组件
'@odoo/owl'                           // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
class FormViewDialog extends Component {
    static template = "web.FormViewDialog";
    static components = { Dialog, View };
    static props = {
        close: Function,
        resModel: String,
        context: { type: Object, optional: true },
        mode: {
            optional: true,
            validate: (m) => ["edit", "readonly"].includes(m),
        },
        onRecordSaved: { type: Function, optional: true },
        onRecordDiscarded: { type: Function, optional: true },
        removeRecord: { type: Function, optional: true },
        resId: { type: [Number, Boolean], optional: true },
        title: { type: String, optional: true },
        viewId: { type: [Number, Boolean], optional: true },
        preventCreate: { type: Boolean, optional: true },
        preventEdit: { type: Boolean, optional: true },
        isToMany: { type: Boolean, optional: true },
        size: Dialog.props.size,
    };
}
```

**组件特性**:
- **对话框集成**: 集成Dialog和View组件
- **模型配置**: 支持资源模型和记录ID配置
- **模式控制**: 支持编辑和只读模式
- **回调处理**: 支持保存、丢弃、删除回调
- **权限控制**: 支持创建和编辑权限控制
- **关系支持**: 支持ToMany关系字段

### 2. 组件初始化

```javascript
setup() {
    super.setup();

    this.actionService = useService("action");
    this.modalRef = useChildRef();
    this.env.dialogData.dismiss = () => this.discardRecord();

    const buttonTemplate = this.props.isToMany
        ? "web.FormViewDialog.ToMany.buttons"
        : "web.FormViewDialog.ToOne.buttons";

    this.currentResId = this.props.resId;
    
    this.viewProps = {
        type: "form",
        buttonTemplate,
        context: this.props.context || {},
        display: { controlPanel: false },
        mode: this.props.mode || "edit",
        resId: this.props.resId || false,
        resModel: this.props.resModel,
        viewId: this.props.viewId || false,
        preventCreate: this.props.preventCreate,
        preventEdit: this.props.preventEdit,
        discardRecord: this.discardRecord.bind(this),
        saveRecord: this.saveRecord.bind(this),
        __beforeLeave__: new CallbackRecorder(),
    };
}
```

**初始化功能**:
- **服务注入**: 注入动作服务
- **引用管理**: 管理模态框引用
- **按钮模板**: 根据关系类型选择按钮模板
- **视图属性**: 配置表单视图属性
- **回调绑定**: 绑定保存和丢弃回调
- **离开钩子**: 设置离开前回调记录器

### 3. 记录保存

```javascript
saveRecord: async (record, { saveAndNew }) => {
    const saved = await record.save({ reload: false });
    if (saved) {
        this.currentResId = record.resId;
        await this.props.onRecordSaved(record);
        if (saveAndNew) {
            const context = Object.assign({}, this.props.context);
            Object.keys(context).forEach((k) => {
                if (k.startsWith("default_")) {
                    delete context[k];
                }
            });
            this.currentResId = false;
            await record.model.load({ resId: false, context });
        } else {
            this.props.close();
        }
    }
    return saved;
}
```

**保存功能**:
- **记录保存**: 保存记录到服务器
- **ID更新**: 更新当前记录ID
- **回调执行**: 执行保存回调
- **保存并新建**: 支持保存并新建功能
- **上下文清理**: 清理默认值上下文
- **对话框关闭**: 保存后关闭对话框

### 4. 记录丢弃

```javascript
async discardRecord() {
    if (this.props.onRecordDiscarded) {
        await this.props.onRecordDiscarded();
    }
    this.props.close();
}
```

**丢弃功能**:
- **回调执行**: 执行丢弃回调
- **对话框关闭**: 丢弃后关闭对话框
- **异步处理**: 支持异步丢弃处理
- **清理操作**: 清理未保存的更改

### 5. 展开功能

```javascript
async onExpand() {
    const beforeLeaveCallbacks = this.viewProps.__beforeLeave__.callbacks;
    const res = await Promise.all(beforeLeaveCallbacks.map((callback) => callback()));
    if (!res.includes(false)) {
        this.actionService.doAction({
            type: "ir.actions.act_window",
            res_model: this.props.resModel,
            res_id: this.currentResId,
            views: [[false, "form"]],
        });
    }
}
```

**展开功能**:
- **离开检查**: 检查离开前回调
- **动作执行**: 执行窗口动作
- **全屏显示**: 在全屏窗口中显示表单
- **状态保持**: 保持当前记录状态

## 使用场景

### 1. 表单视图对话框管理器

```javascript
// 表单视图对话框管理器
class FormViewDialogManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置对话框配置
        this.dialogConfig = {
            enableAutoSave: false,
            enableExpandButton: true,
            enableDirtyCheck: true,
            enableKeyboardShortcuts: true,
            enableValidation: true,
            defaultMode: 'edit',
            defaultSize: 'lg',
            autoCloseOnSave: true
        };
        
        // 设置模式配置
        this.modeConfig = new Map([
            ['edit', {
                name: 'Edit Mode',
                allowSave: true,
                allowDiscard: true,
                allowDelete: true,
                showToolbar: true
            }],
            ['readonly', {
                name: 'Readonly Mode',
                allowSave: false,
                allowDiscard: true,
                allowDelete: false,
                showToolbar: false
            }],
            ['create', {
                name: 'Create Mode',
                allowSave: true,
                allowDiscard: true,
                allowDelete: false,
                showToolbar: true
            }]
        ]);
        
        // 设置按钮模板
        this.buttonTemplates = new Map([
            ['toone', 'web.FormViewDialog.ToOne.buttons'],
            ['tomany', 'web.FormViewDialog.ToMany.buttons'],
            ['custom', 'web.FormViewDialog.Custom.buttons']
        ]);
        
        // 设置对话框统计
        this.dialogStatistics = {
            totalDialogs: 0,
            dialogsByModel: new Map(),
            dialogsByMode: new Map(),
            saveOperations: 0,
            discardOperations: 0,
            expandOperations: 0,
            averageSessionTime: 0,
            totalSessionTime: 0
        };
        
        this.initializeDialogSystem();
    }
    
    // 初始化对话框系统
    initializeDialogSystem() {
        // 创建增强的表单对话框
        this.createEnhancedFormDialog();
        
        // 设置保存系统
        this.setupSaveSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置快捷键系统
        this.setupShortcutSystem();
    }
    
    // 创建增强的表单对话框
    createEnhancedFormDialog() {
        const originalDialog = FormViewDialog;
        
        this.EnhancedFormViewDialog = class extends originalDialog {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
                
                // 添加快捷键功能
                this.addShortcutFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isDirty: false,
                    isValidating: false,
                    validationErrors: [],
                    sessionStartTime: new Date(),
                    lastSaveTime: null,
                    saveCount: 0,
                    hasUnsavedChanges: false
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的保存记录
                this.enhancedSaveRecord = async (record, options = {}) => {
                    try {
                        // 验证记录
                        if (this.dialogConfig.enableValidation) {
                            const isValid = await this.validateRecord(record);
                            if (!isValid) {
                                return false;
                            }
                        }
                        
                        // 记录保存开始
                        this.recordSaveStart();
                        
                        // 执行原始保存
                        const result = await this.saveRecord(record, options);
                        
                        if (result) {
                            // 记录保存成功
                            this.recordSaveSuccess();
                            
                            // 更新状态
                            this.enhancedState.isDirty = false;
                            this.enhancedState.hasUnsavedChanges = false;
                            this.enhancedState.lastSaveTime = new Date();
                            this.enhancedState.saveCount++;
                        }
                        
                        return result;
                        
                    } catch (error) {
                        // 记录保存失败
                        this.recordSaveFailure(error);
                        throw error;
                    }
                };
                
                // 增强的丢弃记录
                this.enhancedDiscardRecord = async () => {
                    try {
                        // 检查未保存更改
                        if (this.enhancedState.hasUnsavedChanges && this.dialogConfig.enableDirtyCheck) {
                            const confirmed = await this.confirmDiscard();
                            if (!confirmed) {
                                return false;
                            }
                        }
                        
                        // 记录丢弃操作
                        this.recordDiscardOperation();
                        
                        // 执行原始丢弃
                        await this.discardRecord();
                        
                        return true;
                        
                    } catch (error) {
                        console.error('Discard error:', error);
                        throw error;
                    }
                };
                
                // 验证记录
                this.validateRecord = async (record) => {
                    this.enhancedState.isValidating = true;
                    this.enhancedState.validationErrors = [];
                    
                    try {
                        // 执行字段验证
                        const fieldErrors = await this.validateFields(record);
                        
                        // 执行业务规则验证
                        const businessErrors = await this.validateBusinessRules(record);
                        
                        // 合并错误
                        const allErrors = [...fieldErrors, ...businessErrors];
                        this.enhancedState.validationErrors = allErrors;
                        
                        if (allErrors.length > 0) {
                            this.showValidationErrors(allErrors);
                            return false;
                        }
                        
                        return true;
                        
                    } finally {
                        this.enhancedState.isValidating = false;
                    }
                };
                
                // 验证字段
                this.validateFields = async (record) => {
                    const errors = [];
                    
                    // 检查必填字段
                    for (const [fieldName, field] of Object.entries(record.fields)) {
                        if (field.required && !record.data[fieldName]) {
                            errors.push(`Field '${field.string}' is required`);
                        }
                    }
                    
                    return errors;
                };
                
                // 验证业务规则
                this.validateBusinessRules = async (record) => {
                    const errors = [];
                    
                    // 这里可以添加自定义业务规则验证
                    // 例如：检查唯一性、范围验证等
                    
                    return errors;
                };
                
                // 显示验证错误
                this.showValidationErrors = (errors) => {
                    const notification = useService("notification");
                    notification.add(
                        errors.join('\n'),
                        { type: 'danger', title: 'Validation Failed' }
                    );
                };
                
                // 确认丢弃
                this.confirmDiscard = async () => {
                    return new Promise((resolve) => {
                        const dialog = useService("dialog");
                        
                        dialog.add(ConfirmationDialog, {
                            title: 'Unsaved Changes',
                            body: 'You have unsaved changes. Are you sure you want to discard them?',
                            confirm: () => resolve(true),
                            cancel: () => resolve(false),
                            confirmLabel: 'Discard',
                            cancelLabel: 'Cancel'
                        });
                    });
                };
                
                // 增强的展开功能
                this.enhancedOnExpand = async () => {
                    try {
                        // 记录展开操作
                        this.recordExpandOperation();
                        
                        // 执行原始展开
                        await this.onExpand();
                        
                    } catch (error) {
                        console.error('Expand error:', error);
                        throw error;
                    }
                };
                
                // 获取对话框信息
                this.getDialogInfo = () => {
                    return {
                        resModel: this.props.resModel,
                        resId: this.currentResId,
                        mode: this.props.mode,
                        isDirty: this.enhancedState.isDirty,
                        isValidating: this.enhancedState.isValidating,
                        hasUnsavedChanges: this.enhancedState.hasUnsavedChanges,
                        sessionTime: new Date() - this.enhancedState.sessionStartTime,
                        saveCount: this.enhancedState.saveCount,
                        lastSaveTime: this.enhancedState.lastSaveTime,
                        validationErrors: this.enhancedState.validationErrors
                    };
                };
                
                // 记录保存开始
                this.recordSaveStart = () => {
                    this.dialogStatistics.saveOperations++;
                };
                
                // 记录保存成功
                this.recordSaveSuccess = () => {
                    // 更新统计
                    this.updateSessionStatistics();
                };
                
                // 记录保存失败
                this.recordSaveFailure = (error) => {
                    console.error('Save failed:', error);
                };
                
                // 记录丢弃操作
                this.recordDiscardOperation = () => {
                    this.dialogStatistics.discardOperations++;
                };
                
                // 记录展开操作
                this.recordExpandOperation = () => {
                    this.dialogStatistics.expandOperations++;
                };
                
                // 更新会话统计
                this.updateSessionStatistics = () => {
                    const sessionTime = new Date() - this.enhancedState.sessionStartTime;
                    this.dialogStatistics.totalSessionTime += sessionTime;
                    
                    if (this.dialogStatistics.totalDialogs > 0) {
                        this.dialogStatistics.averageSessionTime = 
                            this.dialogStatistics.totalSessionTime / this.dialogStatistics.totalDialogs;
                    }
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.dialogConfig.enableValidation,
                    validate: (record) => this.validateRecord(record),
                    getErrors: () => this.enhancedState.validationErrors
                };
            }
            
            addShortcutFeatures() {
                // 快捷键功能
                this.shortcutManager = {
                    enabled: this.dialogConfig.enableKeyboardShortcuts,
                    shortcuts: new Map([
                        ['Ctrl+S', () => this.enhancedSaveRecord()],
                        ['Escape', () => this.enhancedDiscardRecord()],
                        ['Ctrl+E', () => this.enhancedOnExpand()]
                    ])
                };
            }
            
            // 重写原始方法
            async saveRecord(record, options) {
                return await this.enhancedSaveRecord(record, options);
            }
            
            async discardRecord() {
                return await this.enhancedDiscardRecord();
            }
            
            async onExpand() {
                return await this.enhancedOnExpand();
            }
        };
    }
    
    // 设置保存系统
    setupSaveSystem() {
        this.saveSystemConfig = {
            enabled: true,
            autoSave: this.dialogConfig.enableAutoSave,
            autoCloseOnSave: this.dialogConfig.autoCloseOnSave
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.dialogConfig.enableValidation,
            enableFieldValidation: true,
            enableBusinessRules: true
        };
    }
    
    // 设置快捷键系统
    setupShortcutSystem() {
        this.shortcutSystemConfig = {
            enabled: this.dialogConfig.enableKeyboardShortcuts,
            shortcuts: new Map([
                ['save', 'Ctrl+S'],
                ['discard', 'Escape'],
                ['expand', 'Ctrl+E']
            ])
        };
    }
    
    // 创建表单对话框
    createFormDialog(props) {
        const dialog = new this.EnhancedFormViewDialog(props);
        this.dialogStatistics.totalDialogs++;
        
        // 记录模型统计
        const model = props.resModel;
        const count = this.dialogStatistics.dialogsByModel.get(model) || 0;
        this.dialogStatistics.dialogsByModel.set(model, count + 1);
        
        // 记录模式统计
        const mode = props.mode || 'edit';
        const modeCount = this.dialogStatistics.dialogsByMode.get(mode) || 0;
        this.dialogStatistics.dialogsByMode.set(mode, modeCount + 1);
        
        return dialog;
    }
    
    // 注册按钮模板
    registerButtonTemplate(name, template) {
        this.buttonTemplates.set(name, template);
    }
    
    // 获取对话框统计
    getDialogStatistics() {
        return {
            ...this.dialogStatistics,
            modelVariety: this.dialogStatistics.dialogsByModel.size,
            modeVariety: this.dialogStatistics.dialogsByMode.size,
            saveRate: this.dialogStatistics.saveOperations / 
                     Math.max(this.dialogStatistics.totalDialogs, 1),
            discardRate: this.dialogStatistics.discardOperations / 
                        Math.max(this.dialogStatistics.totalDialogs, 1)
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理模式配置
        this.modeConfig.clear();
        
        // 清理按钮模板
        this.buttonTemplates.clear();
        
        // 清理统计
        this.dialogStatistics.dialogsByModel.clear();
        this.dialogStatistics.dialogsByMode.clear();
        
        // 重置统计
        this.dialogStatistics = {
            totalDialogs: 0,
            dialogsByModel: new Map(),
            dialogsByMode: new Map(),
            saveOperations: 0,
            discardOperations: 0,
            expandOperations: 0,
            averageSessionTime: 0,
            totalSessionTime: 0
        };
    }
}

// 使用示例
const dialogManager = new FormViewDialogManager();

// 创建表单对话框
const formDialog = dialogManager.createFormDialog({
    resModel: 'res.partner',
    resId: 123,
    mode: 'edit',
    title: 'Edit Partner',
    onRecordSaved: (record) => {
        console.log('Record saved:', record);
    },
    onRecordDiscarded: () => {
        console.log('Record discarded');
    },
    close: () => {
        console.log('Dialog closed');
    }
});

// 获取统计信息
const stats = dialogManager.getDialogStatistics();
console.log('Form dialog statistics:', stats);
```

## 技术特点

### 1. 对话框集成
- **视图嵌入**: 在对话框中嵌入表单视图
- **模态显示**: 模态对话框显示
- **尺寸控制**: 支持不同尺寸配置
- **响应式**: 支持响应式布局

### 2. 记录管理
- **保存功能**: 完整的记录保存功能
- **丢弃功能**: 记录丢弃和回滚功能
- **删除功能**: 可选的记录删除功能
- **展开功能**: 展开到全屏表单

### 3. 模式支持
- **编辑模式**: 支持记录编辑
- **只读模式**: 支持只读显示
- **创建模式**: 支持新记录创建
- **权限控制**: 基于权限的功能控制

### 4. 回调机制
- **保存回调**: 记录保存后的回调
- **丢弃回调**: 记录丢弃后的回调
- **删除回调**: 记录删除后的回调
- **关闭回调**: 对话框关闭回调

## 设计模式

### 1. 组合模式 (Composition Pattern)
- **组件组合**: 组合对话框和视图组件
- **功能组合**: 组合不同的功能模块
- **模板组合**: 组合不同的按钮模板

### 2. 策略模式 (Strategy Pattern)
- **模式策略**: 不同的编辑模式策略
- **按钮策略**: 不同的按钮模板策略
- **保存策略**: 不同的保存处理策略

### 3. 观察者模式 (Observer Pattern)
- **状态观察**: 观察记录状态变化
- **回调观察**: 观察操作回调
- **生命周期观察**: 观察组件生命周期

### 4. 命令模式 (Command Pattern)
- **保存命令**: 封装保存操作
- **丢弃命令**: 封装丢弃操作
- **展开命令**: 封装展开操作

## 注意事项

1. **内存管理**: 及时清理对话框资源
2. **状态同步**: 保持记录状态同步
3. **用户体验**: 提供清晰的操作反馈
4. **错误处理**: 完善的错误处理机制

## 扩展建议

1. **自动保存**: 添加自动保存功能
2. **版本控制**: 支持记录版本控制
3. **协作编辑**: 支持多用户协作编辑
4. **离线支持**: 支持离线编辑
5. **快捷键**: 增强快捷键支持

该表单视图对话框为Odoo Web客户端提供了完整的模态表单编辑解决方案，通过灵活的配置和丰富的功能确保了表单编辑的便利性和可靠性。

# ExportDataDialog - 数据导出对话框

## 概述

`export_data_dialog.js` 是 Odoo Web 客户端的数据导出对话框组件，负责提供完整的数据导出功能。该模块包含429行代码，是一个功能复杂的对话框组件，专门用于处理数据导出操作，具备字段选择、格式配置、模板管理、拖拽排序、搜索过滤等特性，是数据导出和报表生成的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/view_dialogs/export_data_dialog.js`
- **行数**: 429
- **模块**: `@web/views/view_dialogs/export_data_dialog`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'           // 翻译服务
'@web/core/browser/browser'            // 浏览器服务
'@web/core/checkbox/checkbox'          // 复选框组件
'@web/core/dialog/dialog'              // 对话框组件
'@web/core/network/rpc'                // RPC网络服务
'@web/core/utils/arrays'               // 数组工具
'@web/core/utils/hooks'                // 工具钩子
'@web/core/utils/search'               // 搜索工具
'@web/core/utils/sortable_owl'         // 排序工具
'@web/core/utils/timing'               // 时间工具
'@odoo/owl'                           // OWL框架
```

## 核心组件

### 1. DeleteExportListDialog - 删除导出列表对话框

```javascript
class DeleteExportListDialog extends Component {
    static components = { Dialog };
    static template = "web.DeleteExportListDialog";
    static props = {
        text: String,
        close: Function,
        delete: Function,
    };
    
    async onDelete() {
        await this.props.delete();
        this.props.close();
    }
}
```

**组件特性**:
- **确认删除**: 提供删除确认功能
- **异步操作**: 支持异步删除操作
- **回调处理**: 处理删除和关闭回调
- **简洁设计**: 专注于删除确认功能

### 2. ExportDataItem - 导出数据项组件

```javascript
class ExportDataItem extends Component {
    static template = "web.ExportDataItem";
    static components = { ExportDataItem };
    static props = {
        exportList: { type: Object, optional: true },
        field: { type: Object, optional: true },
        filterSubfields: Function,
        isDebug: Boolean,
        isExpanded: Boolean,
        isFieldExpandable: Function,
        onAdd: Function,
        loadFields: Function,
    };
}
```

**组件特性**:
- **递归组件**: 支持自身递归渲染
- **字段展示**: 展示字段信息和结构
- **展开控制**: 控制字段的展开和折叠
- **添加功能**: 支持字段添加到导出列表
- **调试模式**: 支持调试模式显示

### 3. ExportDataDialog - 主导出对话框

```javascript
class ExportDataDialog extends Component {
    static components = {
        CheckBox,
        Dialog,
        ExportDataItem,
    };
    static template = "web.ExportDataDialog";
    static props = {
        context: { type: Object, optional: true },
        defaultExportList: { type: Array, optional: true },
        download: { type: Boolean, optional: true },
        getExportedData: { type: Function, optional: true },
        ids: { type: Array, optional: true },
        isDomainSelected: { type: Boolean, optional: true },
        root: Object,
        size: { type: String, optional: true },
        title: { type: String, optional: true },
        close: Function,
    };
}
```

**组件特性**:
- **完整功能**: 提供完整的数据导出功能
- **字段管理**: 管理导出字段列表
- **格式选择**: 支持多种导出格式
- **模板系统**: 支持导出模板管理
- **拖拽排序**: 支持字段拖拽排序

## 核心功能

### 1. 组件初始化

```javascript
setup() {
    this.dialog = useService("dialog");
    this.notification = useService("notification");
    this.orm = useService("orm");
    this.draggableRef = useRef("draggable");
    this.exportListRef = useRef("exportList");
    this.searchRef = useRef("search");

    this.knownFields = {};
    this.expandedFields = {};
    this.availableFormats = [];
    this.templates = [];

    this.state = useState({
        exportList: [],
        isCompatible: false,
        isEditingTemplate: false,
        search: [],
        selectedFormat: 0,
        templateId: null,
        isSmall: this.env.isSmall,
        disabled: false,
    });
}
```

**初始化功能**:
- **服务注入**: 注入对话框、通知、ORM等服务
- **引用管理**: 管理DOM元素引用
- **数据结构**: 初始化字段、格式、模板等数据
- **状态管理**: 管理组件状态
- **响应式**: 支持响应式布局

### 2. 字段管理

```javascript
get fieldsAvailable() {
    if (this.searchRef.el && this.searchRef.el.value) {
        return this.state.search.length && Object.values(this.state.search);
    }
    return Object.values(this.knownFields);
}

get rootFields() {
    if (this.searchRef.el && this.searchRef.el.value) {
        const rootFromSearchResults = this.fieldsAvailable.map((f) => {
            if (f.parent) {
                const parentEl = this.knownFields[f.parent.id];
                return this.knownFields[parentEl.parent ? parentEl.parent.id : parentEl.id];
            }
            return this.knownFields[f.id];
        });
        return unique(rootFromSearchResults);
    }
    return this.fieldsAvailable.filter(({ parent }) => !parent);
}
```

**字段管理功能**:
- **字段获取**: 获取可用字段列表
- **搜索过滤**: 基于搜索条件过滤字段
- **层级处理**: 处理字段的层级关系
- **根字段**: 获取根级字段列表

### 3. 拖拽排序

```javascript
useSortable({
    ref: this.draggableRef,
    elements: ".o_export_field",
    enable: !this.state.isSmall,
    cursor: "grabbing",
    onDrop: async ({ element, previous, next }) => {
        const indexes = [element, previous, next].map(
            (e) =>
                e &&
                Object.values(this.state.exportList).findIndex(
                    ({ id }) => id === e.dataset.field_id
                )
        );
        let target;
        if (indexes[0] < indexes[1]) {
            target = previous ? indexes[1] : 0;
        } else {
            target = next ? indexes[2] : this.state.exportList.length - 1;
        }
        this.onDraggingEnd(indexes[0], target);
    },
});
```

**拖拽功能**:
- **可排序**: 支持字段拖拽排序
- **响应式**: 在小屏幕上禁用拖拽
- **位置计算**: 计算拖拽目标位置
- **状态更新**: 更新字段顺序状态

### 4. 导出执行

```javascript
async onExport() {
    if (!this.state.exportList.length) {
        this.notification.add(_t("Please select fields to save export list..."), {
            type: "danger",
        });
        return;
    }
    
    this.state.disabled = true;
    const exportedFields = this.state.exportList.map(({ name, label }) => ({
        name,
        label,
    }));
    
    const params = {
        data: {
            model: this.props.root.resModel,
            fields: exportedFields,
            ids: this.props.ids,
            domain: this.props.isDomainSelected ? this.props.root.domain : [],
            groupby: this.props.root.groupBy,
            context: {
                ...this.props.context,
                import_compat: this.state.isCompatible,
            },
        },
        format: this.availableFormats[this.state.selectedFormat],
    };
    
    if (this.props.getExportedData) {
        const data = await this.props.getExportedData(params);
        this.props.close();
        return data;
    }
    
    await this.env.services.http.post("/web/export/" + params.format, params.data, "file");
    this.props.close();
}
```

**导出功能**:
- **验证检查**: 检查是否选择了导出字段
- **参数构建**: 构建导出参数
- **格式支持**: 支持多种导出格式
- **数据处理**: 处理导出数据
- **文件下载**: 支持文件下载

## 使用场景

### 1. 数据导出管理器

```javascript
// 数据导出管理器
class DataExportManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置导出配置
        this.exportConfig = {
            enableTemplates: true,
            enableDragDrop: true,
            enableSearch: true,
            enableBatchExport: false,
            enableCustomFormats: false,
            maxFieldsPerExport: 100,
            defaultFormat: 'xlsx',
            autoSaveTemplates: true
        };
        
        // 设置支持的导出格式
        this.exportFormats = new Map([
            ['csv', {
                name: 'CSV',
                extension: 'csv',
                mimeType: 'text/csv',
                description: 'Comma Separated Values',
                supportsMultipleSheets: false
            }],
            ['xlsx', {
                name: 'Excel',
                extension: 'xlsx',
                mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                description: 'Microsoft Excel',
                supportsMultipleSheets: true
            }],
            ['pdf', {
                name: 'PDF',
                extension: 'pdf',
                mimeType: 'application/pdf',
                description: 'Portable Document Format',
                supportsMultipleSheets: false
            }],
            ['json', {
                name: 'JSON',
                extension: 'json',
                mimeType: 'application/json',
                description: 'JavaScript Object Notation',
                supportsMultipleSheets: false
            }]
        ]);
        
        // 设置字段类型映射
        this.fieldTypeMapping = new Map([
            ['char', { exportable: true, format: 'text' }],
            ['text', { exportable: true, format: 'text' }],
            ['integer', { exportable: true, format: 'number' }],
            ['float', { exportable: true, format: 'number' }],
            ['boolean', { exportable: true, format: 'boolean' }],
            ['date', { exportable: true, format: 'date' }],
            ['datetime', { exportable: true, format: 'datetime' }],
            ['many2one', { exportable: true, format: 'text' }],
            ['one2many', { exportable: false, format: 'relation' }],
            ['many2many', { exportable: true, format: 'text' }],
            ['binary', { exportable: false, format: 'binary' }]
        ]);
        
        // 设置导出统计
        this.exportStatistics = {
            totalExports: 0,
            exportsByFormat: new Map(),
            exportsByModel: new Map(),
            averageFieldsPerExport: 0,
            totalFieldsExported: 0,
            templatesCreated: 0,
            mostUsedFormat: null,
            mostExportedModel: null
        };
        
        this.initializeExportSystem();
    }
    
    // 初始化导出系统
    initializeExportSystem() {
        // 创建增强的导出对话框
        this.createEnhancedExportDialog();
        
        // 设置模板系统
        this.setupTemplateSystem();
        
        // 设置格式系统
        this.setupFormatSystem();
        
        // 设置字段系统
        this.setupFieldSystem();
    }
    
    // 创建增强的导出对话框
    createEnhancedExportDialog() {
        const originalDialog = ExportDataDialog;
        
        this.EnhancedExportDataDialog = class extends originalDialog {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
                
                // 添加统计功能
                this.addStatisticsFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isValidating: false,
                    validationErrors: [],
                    exportProgress: 0,
                    estimatedTime: 0,
                    lastExportTime: null,
                    favoriteFields: new Set(),
                    recentTemplates: []
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的字段验证
                this.validateExportFields = () => {
                    const errors = [];
                    const { exportList } = this.state;
                    
                    // 检查字段数量
                    if (exportList.length === 0) {
                        errors.push('No fields selected for export');
                    }
                    
                    if (exportList.length > this.exportConfig.maxFieldsPerExport) {
                        errors.push(`Too many fields selected (max: ${this.exportConfig.maxFieldsPerExport})`);
                    }
                    
                    // 检查字段类型
                    for (const field of exportList) {
                        const typeInfo = this.fieldTypeMapping.get(field.type);
                        if (!typeInfo || !typeInfo.exportable) {
                            errors.push(`Field '${field.label}' is not exportable`);
                        }
                    }
                    
                    this.enhancedState.validationErrors = errors;
                    return errors.length === 0;
                };
                
                // 估算导出时间
                this.estimateExportTime = () => {
                    const { exportList } = this.state;
                    const recordCount = this.props.ids?.length || 1000; // 默认估算
                    
                    // 简单的时间估算算法
                    const baseTime = 1000; // 1秒基础时间
                    const fieldFactor = exportList.length * 100; // 每个字段100ms
                    const recordFactor = recordCount * 10; // 每条记录10ms
                    
                    this.enhancedState.estimatedTime = baseTime + fieldFactor + recordFactor;
                };
                
                // 增强的导出执行
                this.enhancedOnExport = async () => {
                    try {
                        // 验证字段
                        if (!this.validateExportFields()) {
                            this.showValidationErrors();
                            return;
                        }
                        
                        // 估算时间
                        this.estimateExportTime();
                        
                        // 记录导出开始
                        this.recordExportStart();
                        
                        // 执行原始导出
                        const result = await this.onExport();
                        
                        // 记录导出成功
                        this.recordExportSuccess();
                        
                        return result;
                        
                    } catch (error) {
                        // 记录导出失败
                        this.recordExportFailure(error);
                        throw error;
                    }
                };
                
                // 显示验证错误
                this.showValidationErrors = () => {
                    const errors = this.enhancedState.validationErrors;
                    this.notification.add(
                        errors.join('\n'),
                        { type: 'danger', title: 'Export Validation Failed' }
                    );
                };
                
                // 添加收藏字段
                this.addFavoriteField = (field) => {
                    this.enhancedState.favoriteFields.add(field.name);
                };
                
                // 移除收藏字段
                this.removeFavoriteField = (field) => {
                    this.enhancedState.favoriteFields.delete(field.name);
                };
                
                // 获取推荐字段
                this.getRecommendedFields = () => {
                    const model = this.props.root.resModel;
                    const commonFields = ['name', 'create_date', 'write_date', 'id'];
                    
                    return this.fieldsAvailable.filter(field => 
                        commonFields.includes(field.name) ||
                        this.enhancedState.favoriteFields.has(field.name)
                    );
                };
                
                // 快速添加推荐字段
                this.addRecommendedFields = () => {
                    const recommended = this.getRecommendedFields();
                    for (const field of recommended) {
                        if (!this.state.exportList.find(f => f.name === field.name)) {
                            this.onAddField(field);
                        }
                    }
                };
                
                // 获取导出信息
                this.getExportInfo = () => {
                    return {
                        fieldCount: this.state.exportList.length,
                        selectedFormat: this.availableFormats[this.state.selectedFormat],
                        estimatedTime: this.enhancedState.estimatedTime,
                        isValid: this.enhancedState.validationErrors.length === 0,
                        validationErrors: this.enhancedState.validationErrors,
                        favoriteFields: Array.from(this.enhancedState.favoriteFields),
                        templateId: this.state.templateId
                    };
                };
                
                // 记录导出开始
                this.recordExportStart = () => {
                    this.exportStatistics.totalExports++;
                    this.enhancedState.lastExportTime = new Date();
                    
                    // 记录格式统计
                    const format = this.availableFormats[this.state.selectedFormat];
                    const count = this.exportStatistics.exportsByFormat.get(format) || 0;
                    this.exportStatistics.exportsByFormat.set(format, count + 1);
                    
                    // 记录模型统计
                    const model = this.props.root.resModel;
                    const modelCount = this.exportStatistics.exportsByModel.get(model) || 0;
                    this.exportStatistics.exportsByModel.set(model, modelCount + 1);
                    
                    // 更新字段统计
                    this.exportStatistics.totalFieldsExported += this.state.exportList.length;
                    this.updateAverageFields();
                };
                
                // 记录导出成功
                this.recordExportSuccess = () => {
                    this.updateMostUsedFormat();
                    this.updateMostExportedModel();
                };
                
                // 记录导出失败
                this.recordExportFailure = (error) => {
                    console.error('Export failed:', error);
                };
                
                // 更新平均字段数
                this.updateAverageFields = () => {
                    if (this.exportStatistics.totalExports > 0) {
                        this.exportStatistics.averageFieldsPerExport = 
                            this.exportStatistics.totalFieldsExported / this.exportStatistics.totalExports;
                    }
                };
                
                // 更新最常用格式
                this.updateMostUsedFormat = () => {
                    let maxCount = 0;
                    let mostUsed = null;
                    
                    for (const [format, count] of this.exportStatistics.exportsByFormat.entries()) {
                        if (count > maxCount) {
                            maxCount = count;
                            mostUsed = format;
                        }
                    }
                    
                    this.exportStatistics.mostUsedFormat = mostUsed;
                };
                
                // 更新最常导出模型
                this.updateMostExportedModel = () => {
                    let maxCount = 0;
                    let mostExported = null;
                    
                    for (const [model, count] of this.exportStatistics.exportsByModel.entries()) {
                        if (count > maxCount) {
                            maxCount = count;
                            mostExported = model;
                        }
                    }
                    
                    this.exportStatistics.mostExportedModel = mostExported;
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: true,
                    validate: () => this.validateExportFields(),
                    getErrors: () => this.enhancedState.validationErrors
                };
            }
            
            addStatisticsFeatures() {
                // 统计功能
                this.statisticsManager = {
                    enabled: true,
                    getInfo: () => this.getExportInfo(),
                    recordStart: () => this.recordExportStart(),
                    recordSuccess: () => this.recordExportSuccess()
                };
            }
            
            // 重写原始方法
            async onExport() {
                return await this.enhancedOnExport();
            }
        };
    }
    
    // 设置模板系统
    setupTemplateSystem() {
        this.templateSystemConfig = {
            enabled: this.exportConfig.enableTemplates,
            autoSave: this.exportConfig.autoSaveTemplates
        };
    }
    
    // 设置格式系统
    setupFormatSystem() {
        this.formatSystemConfig = {
            enabled: true,
            formats: this.exportFormats,
            defaultFormat: this.exportConfig.defaultFormat
        };
    }
    
    // 设置字段系统
    setupFieldSystem() {
        this.fieldSystemConfig = {
            enabled: true,
            typeMapping: this.fieldTypeMapping,
            maxFields: this.exportConfig.maxFieldsPerExport
        };
    }
    
    // 创建导出对话框
    createExportDialog(props) {
        return new this.EnhancedExportDataDialog(props);
    }
    
    // 注册导出格式
    registerExportFormat(name, config) {
        this.exportFormats.set(name, config);
    }
    
    // 获取导出统计
    getExportStatistics() {
        return {
            ...this.exportStatistics,
            formatVariety: this.exportStatistics.exportsByFormat.size,
            modelVariety: this.exportStatistics.exportsByModel.size,
            averageFieldsPerExport: this.exportStatistics.averageFieldsPerExport
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理格式
        this.exportFormats.clear();
        
        // 清理字段类型映射
        this.fieldTypeMapping.clear();
        
        // 清理统计
        this.exportStatistics.exportsByFormat.clear();
        this.exportStatistics.exportsByModel.clear();
        
        // 重置统计
        this.exportStatistics = {
            totalExports: 0,
            exportsByFormat: new Map(),
            exportsByModel: new Map(),
            averageFieldsPerExport: 0,
            totalFieldsExported: 0,
            templatesCreated: 0,
            mostUsedFormat: null,
            mostExportedModel: null
        };
    }
}

// 使用示例
const exportManager = new DataExportManager();

// 创建导出对话框
const exportDialog = exportManager.createExportDialog({
    root: { resModel: 'res.partner', domain: [], groupBy: [] },
    ids: [1, 2, 3],
    context: {},
    close: () => console.log('Dialog closed')
});

// 注册自定义格式
exportManager.registerExportFormat('xml', {
    name: 'XML',
    extension: 'xml',
    mimeType: 'application/xml',
    description: 'Extensible Markup Language',
    supportsMultipleSheets: false
});

// 获取统计信息
const stats = exportManager.getExportStatistics();
console.log('Export statistics:', stats);
```

## 技术特点

### 1. 复杂功能
- **多组件**: 包含多个子组件
- **完整流程**: 提供完整的导出流程
- **丰富交互**: 支持多种用户交互
- **状态管理**: 复杂的状态管理

### 2. 字段管理
- **层级结构**: 处理字段的层级关系
- **搜索过滤**: 支持字段搜索和过滤
- **拖拽排序**: 支持字段拖拽排序
- **类型检查**: 检查字段类型和可导出性

### 3. 模板系统
- **模板管理**: 支持导出模板的创建和管理
- **模板应用**: 支持应用已保存的模板
- **模板编辑**: 支持模板的编辑和删除
- **模板共享**: 支持模板的共享和复用

### 4. 格式支持
- **多格式**: 支持多种导出格式
- **格式配置**: 支持格式特定的配置
- **兼容性**: 支持导入兼容性选项
- **自定义**: 支持自定义导出格式

## 设计模式

### 1. 组合模式 (Composition Pattern)
- **组件组合**: 组合多个子组件
- **功能组合**: 组合不同的功能模块
- **对话框组合**: 组合对话框和内容

### 2. 策略模式 (Strategy Pattern)
- **导出策略**: 不同的导出格式策略
- **字段策略**: 不同的字段处理策略
- **模板策略**: 不同的模板管理策略

### 3. 观察者模式 (Observer Pattern)
- **状态观察**: 观察组件状态变化
- **字段观察**: 观察字段选择变化
- **模板观察**: 观察模板变化

### 4. 工厂模式 (Factory Pattern)
- **组件工厂**: 创建不同的组件实例
- **格式工厂**: 创建不同的导出格式
- **模板工厂**: 创建不同的模板对象

## 注意事项

1. **性能考虑**: 处理大量字段时的性能
2. **内存管理**: 避免内存泄漏
3. **用户体验**: 提供清晰的导出流程
4. **错误处理**: 完善的错误处理机制

## 扩展建议

1. **批量导出**: 支持批量导出功能
2. **定时导出**: 支持定时导出任务
3. **导出历史**: 记录导出历史
4. **权限控制**: 增强权限控制
5. **性能优化**: 优化大数据导出性能

该数据导出对话框为Odoo Web客户端提供了完整的数据导出解决方案，通过丰富的功能和灵活的配置确保了数据导出的便利性和准确性。

# Odoo 视图 Hook (View Hook) 学习资料

## 文件概述

**文件路径**: `output/@web/views/view_hook.js`  
**原始路径**: `/web/static/src/views/view_hook.js`  
**模块类型**: 核心视图模块 - 视图 Hook 工具  
**代码行数**: 147 行  
**依赖关系**: 
- `@web/core/registry` - 注册表系统
- `@web/core/utils/hooks` - Hook工具 (useService)
- `@web/core/browser/browser` - 浏览器工具
- `@web/core/py_js/py` - Python表达式评估 (evaluateExpr)
- `@odoo/owl` - OWL框架 (useComponent, useEffect, xml)

## 模块功能

视图 Hook 模块是 Odoo Web 客户端的视图辅助工具集。该模块提供了：
- 视图架构缓存和处理
- 动作链接处理机制
- 按钮动画效果管理
- 视图组件增强功能
- 性能优化工具

这个模块为视图组件提供了常用的 Hook 函数，简化了视图开发中的常见任务，提高了代码复用性和开发效率。

## Hook 系统架构

### 核心 Hook 分类
```
View Hooks
├── useViewArch - 视图架构处理
│   ├── 架构缓存
│   ├── 模板编译
│   └── 数据提取
├── useActionLinks - 动作链接处理
│   ├── 链接事件监听
│   ├── 动作执行
│   └── 上下文处理
└── useBounceButton - 按钮动画
    ├── 注意力捕获
    ├── 动画控制
    └── 事件处理
```

### Hook 设计原则
- **可复用性**: 提供通用的视图功能
- **性能优化**: 缓存和优化机制
- **事件驱动**: 基于事件的交互处理
- **组件集成**: 与OWL组件系统深度集成

## 核心 Hook 详解

### useViewArch() - 视图架构处理 Hook
```javascript
function useViewArch(arch, params = {}) {
    const CATEGORY = "__processed_archs__";
    
    arch = arch.trim();
    const processedRegistry = registry.category(CATEGORY);
    
    let processedArch;
    if (!processedRegistry.contains(arch)) {
        processedArch = {};
        processedRegistry.add(arch, processedArch);
    } else {
        processedArch = processedRegistry.get(arch);
    }
    
    const { compile, extract } = params;
    if (!("template" in processedArch) && compile) {
        processedArch.template = xml`${compile(arch)}`;
    }
    if (!("extracted" in processedArch) && extract) {
        processedArch.extracted = extract(arch);
    }
    
    return processedArch;
}
```

**功能特性**:
- **架构缓存**: 缓存已处理的视图架构，避免重复处理
- **模板编译**: 支持架构到模板的编译
- **数据提取**: 支持从架构中提取特定数据
- **注册表管理**: 使用注册表系统管理缓存
- **性能优化**: 避免重复的架构处理操作

**使用示例**:
```javascript
// 基本架构处理
class CustomView extends Component {
    setup() {
        const arch = `
            <tree>
                <field name="name"/>
                <field name="email"/>
                <field name="phone"/>
            </tree>
        `;
        
        // 处理架构并缓存
        this.processedArch = useViewArch(arch, {
            compile: (archString) => {
                // 编译架构为模板
                return this.compileArchToTemplate(archString);
            },
            extract: (archString) => {
                // 从架构中提取字段信息
                return this.extractFieldsFromArch(archString);
            }
        });
        
        // 使用处理后的架构
        this.template = this.processedArch.template;
        this.fields = this.processedArch.extracted;
    }
    
    compileArchToTemplate(archString) {
        // 将XML架构编译为OWL模板
        const parser = new DOMParser();
        const doc = parser.parseFromString(archString, 'text/xml');
        const root = doc.documentElement;
        
        if (root.tagName === 'tree') {
            return this.compileTreeView(root);
        } else if (root.tagName === 'form') {
            return this.compileFormView(root);
        }
        
        return '<div>Unsupported view type</div>';
    }
    
    compileTreeView(treeNode) {
        const fields = Array.from(treeNode.querySelectorAll('field'));
        const fieldTemplates = fields.map(field => {
            const name = field.getAttribute('name');
            return `<td><Field name="'${name}'" record="record" /></td>`;
        }).join('');
        
        return `
            <table class="table">
                <thead>
                    <tr>
                        ${fields.map(f => `<th>${f.getAttribute('name')}</th>`).join('')}
                    </tr>
                </thead>
                <tbody>
                    <tr t-foreach="records" t-as="record" t-key="record.id">
                        ${fieldTemplates}
                    </tr>
                </tbody>
            </table>
        `;
    }
    
    compileFormView(formNode) {
        const fields = Array.from(formNode.querySelectorAll('field'));
        const fieldTemplates = fields.map(field => {
            const name = field.getAttribute('name');
            return `
                <div class="form-group">
                    <label>${name}</label>
                    <Field name="'${name}'" record="record" />
                </div>
            `;
        }).join('');
        
        return `
            <form class="form">
                ${fieldTemplates}
            </form>
        `;
    }
    
    extractFieldsFromArch(archString) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(archString, 'text/xml');
        const fields = Array.from(doc.querySelectorAll('field'));
        
        return fields.map(field => ({
            name: field.getAttribute('name'),
            string: field.getAttribute('string'),
            type: field.getAttribute('widget') || 'char',
            required: field.getAttribute('required') === '1',
            readonly: field.getAttribute('readonly') === '1'
        }));
    }
}

// 高级架构处理器
class AdvancedArchProcessor {
    constructor() {
        this.processors = new Map();
        this.setupDefaultProcessors();
    }
    
    setupDefaultProcessors() {
        // 列表视图处理器
        this.processors.set('tree', {
            compile: (arch) => this.compileTreeArch(arch),
            extract: (arch) => this.extractTreeData(arch)
        });
        
        // 表单视图处理器
        this.processors.set('form', {
            compile: (arch) => this.compileFormArch(arch),
            extract: (arch) => this.extractFormData(arch)
        });
        
        // 看板视图处理器
        this.processors.set('kanban', {
            compile: (arch) => this.compileKanbanArch(arch),
            extract: (arch) => this.extractKanbanData(arch)
        });
    }
    
    processArch(archString, viewType) {
        const processor = this.processors.get(viewType);
        if (!processor) {
            throw new Error(`不支持的视图类型: ${viewType}`);
        }
        
        return useViewArch(archString, processor);
    }
    
    compileTreeArch(archString) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(archString, 'text/xml');
        const tree = doc.documentElement;
        
        // 处理字段
        const fields = Array.from(tree.querySelectorAll('field'));
        const headers = fields.map(field => ({
            name: field.getAttribute('name'),
            string: field.getAttribute('string') || field.getAttribute('name'),
            sortable: field.getAttribute('sortable') !== '0'
        }));
        
        // 处理按钮
        const buttons = Array.from(tree.querySelectorAll('button'));
        const buttonTemplates = buttons.map(button => ({
            name: button.getAttribute('name'),
            string: button.getAttribute('string'),
            type: button.getAttribute('type') || 'object',
            icon: button.getAttribute('icon')
        }));
        
        return {
            type: 'tree',
            headers: headers,
            buttons: buttonTemplates,
            template: this.generateTreeTemplate(headers, buttonTemplates)
        };
    }
    
    generateTreeTemplate(headers, buttons) {
        const headerCells = headers.map(h => 
            `<th t-att-class="{ 'sortable': ${h.sortable} }" 
                 t-on-click="() => this.sort('${h.name}')">${h.string}</th>`
        ).join('');
        
        const dataCells = headers.map(h => 
            `<td><Field name="'${h.name}'" record="record" /></td>`
        ).join('');
        
        const buttonCells = buttons.map(b => 
            `<button class="btn btn-sm btn-primary" 
                     t-on-click="() => this.executeButton('${b.name}', record)">
                <i t-if="'${b.icon}'" class="${b.icon}" />
                ${b.string}
             </button>`
        ).join('');
        
        return `
            <div class="tree-view">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            ${headerCells}
                            <th t-if="hasButtons">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr t-foreach="records" t-as="record" t-key="record.id">
                            ${dataCells}
                            <td t-if="hasButtons">
                                ${buttonCells}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        `;
    }
    
    extractTreeData(archString) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(archString, 'text/xml');
        const tree = doc.documentElement;
        
        return {
            fields: Array.from(tree.querySelectorAll('field')).map(f => f.getAttribute('name')),
            buttons: Array.from(tree.querySelectorAll('button')).map(b => b.getAttribute('name')),
            editable: tree.getAttribute('editable') || false,
            multiEdit: tree.getAttribute('multi_edit') === '1',
            createable: tree.getAttribute('create') !== 'false',
            deletable: tree.getAttribute('delete') !== 'false'
        };
    }
}

// 在组件中使用高级处理器
class ProcessedView extends Component {
    setup() {
        this.processor = new AdvancedArchProcessor();
        
        const archString = this.props.arch;
        const viewType = this.props.type;
        
        // 处理架构
        this.processedArch = this.processor.processArch(archString, viewType);
        
        // 使用处理结果
        this.viewConfig = this.processedArch.extracted;
        this.template = xml`${this.processedArch.template}`;
    }
}
```

### useBounceButton() - 按钮动画 Hook
```javascript
function useBounceButton(containerRef, shouldBounce) {
    let timeout;
    const ui = useService("ui");

    useEffect(
        (containerEl) => {
            if (!containerEl) {
                return;
            }

            const handler = (ev) => {
                const button = ui.activeElement.querySelector("[data-bounce-button]");
                if (button && shouldBounce(ev.target)) {
                    button.classList.add("o_catch_attention");
                    browser.clearTimeout(timeout);
                    timeout = browser.setTimeout(() => {
                        button.classList.remove("o_catch_attention");
                    }, 400);
                }
            };

            containerEl.addEventListener("click", handler);
            return () => containerEl.removeEventListener("click", handler);
        },
        () => [containerRef.el]
    );
}
```

**功能特性**:
- **注意力捕获**: 通过动画吸引用户注意力
- **条件触发**: 支持自定义触发条件
- **动画控制**: 自动管理动画的添加和移除
- **性能优化**: 使用防抖机制避免频繁动画
- **清理机制**: 自动清理事件监听器和定时器

**使用示例**:
```javascript
// 基本按钮动画组件
class BounceButtonComponent extends Component {
    static template = xml`
        <div class="bounce-button-demo" t-ref="container">
            <h3>按钮动画示例</h3>

            <div class="form-section">
                <div class="form-group">
                    <label>产品名称</label>
                    <input type="text" t-model="productName" class="form-control" />
                </div>

                <div class="form-group">
                    <label>产品价格</label>
                    <input type="number" t-model="productPrice" class="form-control" />
                </div>

                <div class="form-group">
                    <label>产品描述</label>
                    <textarea t-model="productDescription" class="form-control"></textarea>
                </div>
            </div>

            <div class="action-buttons">
                <button data-bounce-button="true"
                        t-on-click="saveProduct"
                        class="btn btn-primary">
                    <i class="fa fa-save" /> 保存产品
                </button>

                <button t-on-click="resetForm"
                        class="btn btn-secondary">
                    <i class="fa fa-refresh" /> 重置
                </button>
            </div>

            <div class="validation-messages" t-if="validationErrors.length">
                <div t-foreach="validationErrors" t-as="error" t-key="error_index"
                     class="alert alert-danger">
                    <span t-esc="error" />
                </div>
            </div>
        </div>
    `;

    setup() {
        this.containerRef = useRef("container");
        this.productName = useState('');
        this.productPrice = useState(0);
        this.productDescription = useState('');
        this.validationErrors = useState([]);

        // 设置按钮动画
        useBounceButton(this.containerRef, (target) => {
            // 当点击非保存按钮且表单有错误时触发动画
            return !target.closest('[data-bounce-button]') && this.hasValidationErrors();
        });
    }

    hasValidationErrors() {
        this.validationErrors = [];

        if (!this.productName.trim()) {
            this.validationErrors.push('产品名称不能为空');
        }

        if (this.productPrice <= 0) {
            this.validationErrors.push('产品价格必须大于0');
        }

        if (!this.productDescription.trim()) {
            this.validationErrors.push('产品描述不能为空');
        }

        return this.validationErrors.length > 0;
    }

    saveProduct() {
        if (!this.hasValidationErrors()) {
            console.log('保存产品:', {
                name: this.productName,
                price: this.productPrice,
                description: this.productDescription
            });

            // 保存成功后重置表单
            this.resetForm();
        }
    }

    resetForm() {
        this.productName = '';
        this.productPrice = 0;
        this.productDescription = '';
        this.validationErrors = [];
    }
}

// 高级动画管理器
class AnimationManager {
    constructor() {
        this.animations = new Map();
        this.defaultDuration = 400;
        this.setupAnimationStyles();
    }

    setupAnimationStyles() {
        // 添加CSS动画样式
        const style = document.createElement('style');
        style.textContent = `
            .o_catch_attention {
                animation: bounce 0.4s ease-in-out;
            }

            .o_pulse_attention {
                animation: pulse 0.6s ease-in-out;
            }

            .o_shake_attention {
                animation: shake 0.5s ease-in-out;
            }

            .o_glow_attention {
                animation: glow 0.8s ease-in-out;
            }

            @keyframes bounce {
                0%, 20%, 60%, 100% { transform: translateY(0); }
                40% { transform: translateY(-10px); }
                80% { transform: translateY(-5px); }
            }

            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }

            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
                20%, 40%, 60%, 80% { transform: translateX(5px); }
            }

            @keyframes glow {
                0%, 100% { box-shadow: 0 0 5px rgba(0, 123, 255, 0.5); }
                50% { box-shadow: 0 0 20px rgba(0, 123, 255, 0.8); }
            }
        `;
        document.head.appendChild(style);
    }

    addAnimation(element, animationType = 'bounce', duration = null) {
        const animationDuration = duration || this.defaultDuration;
        const animationClass = `o_${animationType}_attention`;

        // 移除之前的动画
        this.removeAnimation(element);

        // 添加新动画
        element.classList.add(animationClass);

        // 设置定时器移除动画
        const timeout = setTimeout(() => {
            element.classList.remove(animationClass);
            this.animations.delete(element);
        }, animationDuration);

        this.animations.set(element, {
            timeout,
            animationClass,
            startTime: Date.now()
        });
    }

    removeAnimation(element) {
        const animation = this.animations.get(element);
        if (animation) {
            clearTimeout(animation.timeout);
            element.classList.remove(animation.animationClass);
            this.animations.delete(element);
        }
    }

    removeAllAnimations() {
        for (const [element, animation] of this.animations) {
            clearTimeout(animation.timeout);
            element.classList.remove(animation.animationClass);
        }
        this.animations.clear();
    }

    isAnimating(element) {
        return this.animations.has(element);
    }

    getAnimationProgress(element) {
        const animation = this.animations.get(element);
        if (!animation) return 0;

        const elapsed = Date.now() - animation.startTime;
        return Math.min(elapsed / this.defaultDuration, 1);
    }
}

// 智能动画组件
class SmartAnimationComponent extends Component {
    static template = xml`
        <div class="smart-animation-demo" t-ref="container">
            <h3>智能动画示例</h3>

            <div class="animation-controls">
                <button t-on-click="() => this.triggerAnimation('bounce')"
                        class="btn btn-primary">
                    弹跳动画
                </button>

                <button t-on-click="() => this.triggerAnimation('pulse')"
                        class="btn btn-info">
                    脉冲动画
                </button>

                <button t-on-click="() => this.triggerAnimation('shake')"
                        class="btn btn-warning">
                    摇摆动画
                </button>

                <button t-on-click="() => this.triggerAnimation('glow')"
                        class="btn btn-success">
                    发光动画
                </button>
            </div>

            <div class="target-buttons">
                <button data-animation-target="save"
                        t-on-click="performSave"
                        class="btn btn-primary btn-lg">
                    <i class="fa fa-save" /> 保存
                </button>

                <button data-animation-target="delete"
                        t-on-click="performDelete"
                        class="btn btn-danger btn-lg">
                    <i class="fa fa-trash" /> 删除
                </button>

                <button data-animation-target="submit"
                        t-on-click="performSubmit"
                        class="btn btn-success btn-lg">
                    <i class="fa fa-check" /> 提交
                </button>
            </div>

            <div class="animation-status">
                <p>当前动画状态:</p>
                <ul>
                    <li t-foreach="animationStatus" t-as="status" t-key="status.target">
                        <strong t-esc="status.target" />:
                        <span t-esc="status.isAnimating ? '动画中' : '静止'" />
                        <span t-if="status.isAnimating">
                            (进度: <span t-esc="Math.round(status.progress * 100)" />%)
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    `;

    setup() {
        this.containerRef = useRef("container");
        this.animationManager = new AnimationManager();
        this.animationStatus = useState([]);

        // 设置智能动画
        this.setupSmartBounce();

        // 定期更新动画状态
        this.statusInterval = setInterval(() => {
            this.updateAnimationStatus();
        }, 100);

        onWillUnmount(() => {
            clearInterval(this.statusInterval);
            this.animationManager.removeAllAnimations();
        });
    }

    setupSmartBounce() {
        useBounceButton(this.containerRef, (target) => {
            // 智能判断是否需要动画
            const button = target.closest('[data-animation-target]');
            if (!button) return false;

            const targetType = button.dataset.animationTarget;

            // 根据不同的目标类型决定动画条件
            switch (targetType) {
                case 'save':
                    return this.shouldAnimateOnSave();
                case 'delete':
                    return this.shouldAnimateOnDelete();
                case 'submit':
                    return this.shouldAnimateOnSubmit();
                default:
                    return false;
            }
        });
    }

    shouldAnimateOnSave() {
        // 如果有未保存的更改，则动画提示
        return this.hasUnsavedChanges();
    }

    shouldAnimateOnDelete() {
        // 如果没有选中项目，则动画提示
        return !this.hasSelectedItems();
    }

    shouldAnimateOnSubmit() {
        // 如果表单验证失败，则动画提示
        return !this.isFormValid();
    }

    hasUnsavedChanges() {
        // 模拟检查未保存更改
        return Math.random() > 0.5;
    }

    hasSelectedItems() {
        // 模拟检查选中项目
        return Math.random() > 0.3;
    }

    isFormValid() {
        // 模拟表单验证
        return Math.random() > 0.4;
    }

    triggerAnimation(type) {
        const buttons = this.containerRef.el.querySelectorAll('[data-animation-target]');
        buttons.forEach(button => {
            this.animationManager.addAnimation(button, type);
        });
    }

    updateAnimationStatus() {
        const buttons = this.containerRef.el.querySelectorAll('[data-animation-target]');
        const status = [];

        buttons.forEach(button => {
            const target = button.dataset.animationTarget;
            const isAnimating = this.animationManager.isAnimating(button);
            const progress = this.animationManager.getAnimationProgress(button);

            status.push({
                target,
                isAnimating,
                progress
            });
        });

        this.animationStatus = status;
    }

    performSave() {
        console.log('执行保存操作');
    }

    performDelete() {
        console.log('执行删除操作');
    }

    performSubmit() {
        console.log('执行提交操作');
    }
}
```

## 最佳实践

### 1. 性能优化
```javascript
// ✅ 推荐：缓存架构处理结果
const archCache = new Map();

function optimizedUseViewArch(arch, params) {
    const cacheKey = `${arch}_${JSON.stringify(params)}`;

    if (archCache.has(cacheKey)) {
        return archCache.get(cacheKey);
    }

    const result = useViewArch(arch, params);
    archCache.set(cacheKey, result);

    return result;
}
```

### 2. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
function safeUseActionLinks(params) {
    try {
        return useActionLinks(params);
    } catch (error) {
        console.error('动作链接设置失败:', error);
        return () => {}; // 返回空处理器
    }
}
```

### 3. 内存管理
```javascript
// ✅ 推荐：正确的资源清理
class ManagedHookComponent extends Component {
    setup() {
        this.timers = [];
        this.subscriptions = [];

        // 使用Hook
        this.actionHandler = useActionLinks({
            resModel: this.props.resModel,
            reload: this.reload.bind(this)
        });

        onWillUnmount(() => {
            // 清理定时器
            this.timers.forEach(timer => clearTimeout(timer));

            // 清理订阅
            this.subscriptions.forEach(sub => sub.unsubscribe());
        });
    }
}
```

## 总结

Odoo 视图 Hook 模块提供了强大的视图增强工具：

**核心优势**:
- **架构缓存**: 高效的视图架构处理和缓存机制
- **动作处理**: 完整的动作链接处理系统
- **用户体验**: 丰富的动画和交互效果
- **性能优化**: 内置的性能优化和资源管理
- **易用性**: 简单易用的Hook接口

**适用场景**:
- 视图架构处理
- 动作链接管理
- 用户界面增强
- 交互动画效果
- 性能优化

**设计优势**:
- Hook模式设计
- 缓存机制优化
- 事件驱动架构
- 组件化集成

这些Hook为 Odoo Web 客户端提供了强大的视图增强能力，是构建高质量用户界面的重要工具。

### useActionLinks() - 动作链接处理 Hook
```javascript
function useActionLinks({ resModel, reload }) {
    const component = useComponent();
    const keepLast = component.env.keepLast;
    const orm = useService("orm");
    const { doAction } = useService("action");
    
    async function handler(ev) {
        ev.preventDefault();
        ev.stopPropagation();
        
        let target = ev.target;
        if (target.tagName !== "A") {
            target = target.closest("a");
        }
        const data = target.dataset;
        
        // 处理方法调用
        if (data.method !== undefined && data.model !== undefined) {
            const options = {};
            if (data.reloadOnClose) {
                options.onClose = reload || (() => component.render());
            }
            const action = await keepLast.add(orm.call(data.model, data.method));
            if (action !== undefined) {
                keepLast.add(Promise.resolve(doAction(action, options)));
            }
        }
        // 处理命名动作
        else if (target.getAttribute("name")) {
            const options = {};
            if (data.context) {
                options.additionalContext = evaluateExpr(data.context);
            }
            keepLast.add(doAction(target.getAttribute("name"), options));
        }
        // 处理窗口动作
        else {
            const action = createWindowAction(target, data, resModel);
            const options = {};
            if (data.context) {
                options.additionalContext = evaluateExpr(data.context);
            }
            keepLast.add(doAction(action, options));
        }
    }
    
    return (ev) => {
        const a = ev.target.closest(`a[type="action"]`);
        if (a && ev.currentTarget.contains(a)) {
            handler(ev);
        }
    };
}
```

**功能特性**:
- **链接监听**: 自动监听type="action"的链接
- **多种动作**: 支持方法调用、命名动作、窗口动作
- **上下文处理**: 支持动态上下文评估
- **并发控制**: 使用KeepLast避免竞态条件
- **重载支持**: 支持动作完成后的重载

**使用示例**:
```javascript
// 带动作链接的组件
class ActionLinksComponent extends Component {
    static template = xml`
        <div class="action-links-demo" t-on-click="actionLinkHandler">
            <h3>动作链接示例</h3>
            
            <!-- 方法调用链接 -->
            <div class="method-links">
                <h4>方法调用</h4>
                <a type="action" 
                   data-model="res.partner" 
                   data-method="action_create_customer"
                   data-reload-on-close="1">
                    创建客户
                </a>
                
                <a type="action" 
                   data-model="sale.order" 
                   data-method="action_confirm_orders"
                   data-context="{'active_ids': [1, 2, 3]}">
                    确认订单
                </a>
            </div>
            
            <!-- 命名动作链接 -->
            <div class="named-action-links">
                <h4>命名动作</h4>
                <a type="action" 
                   name="base.action_partner_form"
                   data-context="{'default_is_company': True}">
                    打开合作伙伴表单
                </a>
                
                <a type="action" 
                   name="sale.action_orders">
                    查看销售订单
                </a>
            </div>
            
            <!-- 窗口动作链接 -->
            <div class="window-action-links">
                <h4>窗口动作</h4>
                <a type="action" 
                   data-model="product.product"
                   data-views="[[false, 'kanban'], [false, 'form']]"
                   data-domain="[('sale_ok', '=', True)]"
                   title="可销售产品">
                    查看产品
                </a>
                
                <a type="action" 
                   data-model="res.partner"
                   data-resid="1"
                   title="查看客户详情">
                    客户详情
                </a>
            </div>
            
            <!-- 带图标的动作链接 -->
            <div class="icon-action-links">
                <h4>图标动作</h4>
                <a type="action" 
                   data-model="crm.lead" 
                   data-method="action_create_opportunity"
                   class="btn btn-primary">
                    <i class="fa fa-plus" /> 创建商机
                </a>
                
                <a type="action" 
                   name="account.action_invoice_tree1"
                   class="btn btn-info">
                    <i class="fa fa-file-text" /> 查看发票
                </a>
            </div>
        </div>
    `;
    
    setup() {
        // 设置动作链接处理器
        this.actionLinkHandler = useActionLinks({
            resModel: this.props.resModel || 'res.partner',
            reload: this.reload.bind(this)
        });
    }
    
    reload() {
        console.log('重新加载组件数据');
        // 重新加载逻辑
        this.render();
    }
}

// 高级动作链接管理器
class ActionLinkManager {
    constructor() {
        this.handlers = new Map();
        this.middleware = [];
        this.setupDefaultHandlers();
    }
    
    setupDefaultHandlers() {
        // 确认对话框中间件
        this.addMiddleware('confirm', async (action, context) => {
            if (context.requireConfirm) {
                const confirmed = await this.showConfirmDialog(
                    context.confirmMessage || '确定要执行此操作吗？'
                );
                if (!confirmed) {
                    throw new Error('用户取消操作');
                }
            }
            return { action, context };
        });
        
        // 权限检查中间件
        this.addMiddleware('permission', async (action, context) => {
            if (context.requiredPermission) {
                const hasPermission = await this.checkPermission(context.requiredPermission);
                if (!hasPermission) {
                    throw new Error('权限不足');
                }
            }
            return { action, context };
        });
        
        // 日志记录中间件
        this.addMiddleware('logging', async (action, context) => {
            console.log('执行动作:', action, context);
            return { action, context };
        });
    }
    
    addMiddleware(name, handler) {
        this.middleware.push({ name, handler });
    }
    
    async executeAction(action, context = {}) {
        try {
            // 执行中间件
            let processedAction = action;
            let processedContext = context;
            
            for (const middleware of this.middleware) {
                const result = await middleware.handler(processedAction, processedContext);
                processedAction = result.action;
                processedContext = result.context;
            }
            
            // 执行实际动作
            return await this.doAction(processedAction, processedContext);
            
        } catch (error) {
            console.error('动作执行失败:', error);
            throw error;
        }
    }
    
    async showConfirmDialog(message) {
        return new Promise((resolve) => {
            const confirmed = confirm(message);
            resolve(confirmed);
        });
    }
    
    async checkPermission(permission) {
        // 模拟权限检查
        return true;
    }
    
    async doAction(action, context) {
        // 执行实际的动作
        console.log('执行动作:', action, context);
        return true;
    }
}

// 在组件中使用高级管理器
class ManagedActionLinksComponent extends Component {
    setup() {
        this.actionManager = new ActionLinkManager();
        
        // 添加自定义中间件
        this.actionManager.addMiddleware('custom', async (action, context) => {
            // 自定义处理逻辑
            if (action.type === 'dangerous') {
                context.requireConfirm = true;
                context.confirmMessage = '这是一个危险操作，确定要继续吗？';
            }
            return { action, context };
        });
        
        this.actionLinkHandler = useActionLinks({
            resModel: this.props.resModel,
            reload: this.reload.bind(this)
        });
    }
    
    async handleCustomAction(actionData) {
        try {
            await this.actionManager.executeAction(actionData, {
                requiredPermission: 'admin',
                requireConfirm: true
            });
        } catch (error) {
            console.error('自定义动作执行失败:', error);
        }
    }
}
```

# Odoo 视图工具 (View Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/views/utils.js`  
**原始路径**: `/web/static/src/views/utils.js`  
**模块类型**: 核心视图模块 - 视图工具函数库  
**代码行数**: 307 行  
**依赖关系**: 
- `@web/core/l10n/translation` - 国际化翻译 (_t)
- `@web/core/registry` - 注册表系统 (registry)
- `@web/core/utils/strings` - 字符串工具 (exprToBoolean)
- `@web/model/relational_model/utils` - 关系模型工具 (combineModifiers)

## 模块功能

视图工具模块是 Odoo Web 客户端的视图辅助函数库。该模块提供了：
- 视图类名计算和管理
- 字段类型检查和验证
- 按钮参数处理
- 装饰器和样式处理
- 数据格式化工具
- 通用工具函数

这个模块为所有视图类型提供了通用的工具函数，是视图系统的重要支撑模块。

## 常量定义

### 字段类型常量
```javascript
const X2M_TYPES = ["one2many", "many2many"];
const NUMERIC_TYPES = ["integer", "float", "monetary"];
```

### 按钮点击参数
```javascript
const BUTTON_CLICK_PARAMS = [
    "name", "type", "args", "block-ui", "context",
    "close", "cancel-label", "confirm", "confirm-title", 
    "confirm-label", "special", "effect", "help",
    "debounce", "noSaveDialog"
];
```

### 视图活动动作类型
```javascript
/** @typedef ViewActiveActions {
 * @property {"view"} type
 * @property {boolean} edit
 * @property {boolean} create
 * @property {boolean} delete
 * @property {boolean} duplicate
 */
```

## 核心函数详解

### computeViewClassName() - 视图类名计算
```javascript
function computeViewClassName(viewType, rootNode, additionalClassList = []) {
    const subType = rootNode?.getAttribute("js_class");
    const classList = rootNode?.getAttribute("class")?.split(" ") || [];
    const uniqueClasses = new Set([
        getViewClass(viewType),
        getViewClass(subType),
        ...classList,
        ...additionalClassList,
    ]);
    uniqueClasses.delete(false);
    return [...uniqueClasses].join(" ");
}
```

**功能特性**:
- **类名生成**: 根据视图类型生成标准类名
- **子类型支持**: 支持js_class属性的子类型
- **类名合并**: 合并多个来源的类名
- **去重处理**: 自动去除重复和无效类名
- **灵活扩展**: 支持额外类名列表

**使用示例**:
```javascript
// 基本视图类名计算
class ViewClassManager {
    constructor() {
        this.viewTypeClasses = new Map();
        this.setupDefaultClasses();
    }
    
    setupDefaultClasses() {
        this.viewTypeClasses.set('list', ['o_list_view', 'table-responsive']);
        this.viewTypeClasses.set('form', ['o_form_view']);
        this.viewTypeClasses.set('kanban', ['o_kanban_view', 'd-flex']);
        this.viewTypeClasses.set('calendar', ['o_calendar_view']);
        this.viewTypeClasses.set('graph', ['o_graph_view']);
        this.viewTypeClasses.set('pivot', ['o_pivot_view']);
    }
    
    computeClassName(viewType, rootNode, options = {}) {
        const {
            additionalClasses = [],
            responsive = true,
            theme = 'default'
        } = options;
        
        // 基础类名
        const baseClasses = this.viewTypeClasses.get(viewType) || [];
        
        // 响应式类名
        const responsiveClasses = responsive ? ['responsive'] : [];
        
        // 主题类名
        const themeClasses = theme !== 'default' ? [`theme-${theme}`] : [];
        
        // 状态类名
        const stateClasses = this.getStateClasses(rootNode);
        
        // 合并所有类名
        const allClasses = [
            ...baseClasses,
            ...responsiveClasses,
            ...themeClasses,
            ...stateClasses,
            ...additionalClasses
        ];
        
        return computeViewClassName(viewType, rootNode, allClasses);
    }
    
    getStateClasses(rootNode) {
        const classes = [];
        
        if (rootNode?.getAttribute('editable')) {
            classes.push('o_editable');
        }
        
        if (rootNode?.getAttribute('readonly') === '1') {
            classes.push('o_readonly');
        }
        
        if (rootNode?.getAttribute('create') === 'false') {
            classes.push('o_no_create');
        }
        
        if (rootNode?.getAttribute('delete') === 'false') {
            classes.push('o_no_delete');
        }
        
        return classes;
    }
    
    // 动态类名管理
    addDynamicClasses(element, conditions) {
        Object.entries(conditions).forEach(([className, condition]) => {
            if (condition) {
                element.classList.add(className);
            } else {
                element.classList.remove(className);
            }
        });
    }
    
    // 条件类名应用
    applyConditionalClasses(element, record, fieldName) {
        const field = record.fields[fieldName];
        const value = record.data[fieldName];
        
        // 基于字段类型的类名
        element.classList.add(`field-type-${field.type}`);
        
        // 基于值状态的类名
        this.addDynamicClasses(element, {
            'field-empty': this.isEmpty(value),
            'field-required': field.required,
            'field-readonly': field.readonly,
            'field-invalid': this.isInvalid(value, field),
            'field-changed': this.isChanged(record, fieldName)
        });
    }
    
    isEmpty(value) {
        return value === null || value === undefined || value === '';
    }
    
    isInvalid(value, field) {
        if (field.required && this.isEmpty(value)) {
            return true;
        }
        
        if (field.type === 'email' && value && !this.isValidEmail(value)) {
            return true;
        }
        
        return false;
    }
    
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    isChanged(record, fieldName) {
        return record.isDirty(fieldName);
    }
}

// 在组件中使用类名管理器
class StyledViewComponent extends Component {
    static template = xml`
        <div t-att-class="viewClassName">
            <div t-foreach="fields" t-as="field" t-key="field.name"
                 t-ref="field_${field.name}"
                 class="field-container">
                <Field name="field.name" record="record" />
            </div>
        </div>
    `;
    
    setup() {
        this.classManager = new ViewClassManager();
        this.record = this.props.record;
        this.fields = this.props.fields;
        
        // 计算视图类名
        this.viewClassName = this.classManager.computeClassName(
            this.props.viewType,
            this.props.rootNode,
            {
                additionalClasses: ['custom-view'],
                responsive: true,
                theme: this.props.theme || 'default'
            }
        );
        
        onMounted(() => {
            this.applyFieldClasses();
        });
        
        onPatched(() => {
            this.applyFieldClasses();
        });
    }
    
    applyFieldClasses() {
        this.fields.forEach(field => {
            const element = this.el.querySelector(`[t-ref="field_${field.name}"]`);
            if (element) {
                this.classManager.applyConditionalClasses(
                    element,
                    this.record,
                    field.name
                );
            }
        });
    }
}
```

### getFormattedValue() - 值格式化
```javascript
function getFormattedValue(record, fieldName, fieldInfo = null) {
    const field = record.fields[fieldName];
    const formatter = registry.category("formatters").get(field.type, (val) => val);
    const formatOptions = {};
    if (fieldInfo && formatter.extractOptions) {
        Object.assign(formatOptions, formatter.extractOptions(fieldInfo));
    }
    return formatter(record.data[fieldName], formatOptions);
}
```

**功能特性**:
- **类型格式化**: 根据字段类型自动选择格式化器
- **选项提取**: 从字段信息中提取格式化选项
- **注册表集成**: 使用注册表系统管理格式化器
- **默认处理**: 提供默认的格式化行为
- **扩展支持**: 支持自定义格式化选项

**使用示例**:
```javascript
// 高级值格式化器
class AdvancedValueFormatter {
    constructor() {
        this.formatters = new Map();
        this.setupDefaultFormatters();
    }
    
    setupDefaultFormatters() {
        // 货币格式化器
        this.formatters.set('monetary', (value, options = {}) => {
            const {
                currency = 'CNY',
                locale = 'zh-CN',
                minimumFractionDigits = 2,
                maximumFractionDigits = 2
            } = options;
            
            return new Intl.NumberFormat(locale, {
                style: 'currency',
                currency: currency,
                minimumFractionDigits,
                maximumFractionDigits
            }).format(value || 0);
        });
        
        // 日期格式化器
        this.formatters.set('date', (value, options = {}) => {
            if (!value) return '';
            
            const {
                format = 'YYYY-MM-DD',
                locale = 'zh-CN'
            } = options;
            
            const date = new Date(value);
            return date.toLocaleDateString(locale);
        });
        
        // 日期时间格式化器
        this.formatters.set('datetime', (value, options = {}) => {
            if (!value) return '';
            
            const {
                format = 'YYYY-MM-DD HH:mm:ss',
                locale = 'zh-CN',
                timeZone = 'Asia/Shanghai'
            } = options;
            
            const date = new Date(value);
            return date.toLocaleString(locale, { timeZone });
        });
        
        // 百分比格式化器
        this.formatters.set('percentage', (value, options = {}) => {
            const {
                minimumFractionDigits = 1,
                maximumFractionDigits = 2
            } = options;
            
            return new Intl.NumberFormat('zh-CN', {
                style: 'percent',
                minimumFractionDigits,
                maximumFractionDigits
            }).format((value || 0) / 100);
        });
        
        // 文件大小格式化器
        this.formatters.set('filesize', (value, options = {}) => {
            if (!value) return '0 B';
            
            const { binary = true } = options;
            const base = binary ? 1024 : 1000;
            const units = binary 
                ? ['B', 'KiB', 'MiB', 'GiB', 'TiB']
                : ['B', 'KB', 'MB', 'GB', 'TB'];
            
            const index = Math.floor(Math.log(value) / Math.log(base));
            const size = value / Math.pow(base, index);
            
            return `${size.toFixed(1)} ${units[index]}`;
        });
        
        // 持续时间格式化器
        this.formatters.set('duration', (value, options = {}) => {
            if (!value) return '0:00';
            
            const { format = 'HH:mm' } = options;
            const hours = Math.floor(value);
            const minutes = Math.round((value - hours) * 60);
            
            if (format === 'HH:mm') {
                return `${hours}:${minutes.toString().padStart(2, '0')}`;
            } else if (format === 'human') {
                const parts = [];
                if (hours > 0) parts.push(`${hours}小时`);
                if (minutes > 0) parts.push(`${minutes}分钟`);
                return parts.join(' ') || '0分钟';
            }
            
            return value.toString();
        });
    }
    
    formatValue(record, fieldName, fieldInfo = null) {
        const field = record.fields[fieldName];
        const value = record.data[fieldName];
        
        // 获取自定义格式化器
        const customFormatter = this.formatters.get(field.type);
        if (customFormatter) {
            const options = this.extractFormatOptions(field, fieldInfo);
            return customFormatter(value, options);
        }
        
        // 回退到默认格式化
        return getFormattedValue(record, fieldName, fieldInfo);
    }
    
    extractFormatOptions(field, fieldInfo) {
        const options = {};
        
        // 从字段定义中提取选项
        if (field.currency_field) {
            options.currency = field.currency_field;
        }
        
        if (field.digits) {
            options.minimumFractionDigits = field.digits[1];
            options.maximumFractionDigits = field.digits[1];
        }
        
        // 从字段信息中提取选项
        if (fieldInfo) {
            Object.assign(options, fieldInfo.options || {});
        }
        
        return options;
    }
    
    // 批量格式化
    formatRecord(record, fieldNames, fieldInfos = {}) {
        const formatted = {};
        
        fieldNames.forEach(fieldName => {
            const fieldInfo = fieldInfos[fieldName];
            formatted[fieldName] = this.formatValue(record, fieldName, fieldInfo);
        });
        
        return formatted;
    }
    
    // 条件格式化
    formatWithConditions(record, fieldName, conditions) {
        const value = record.data[fieldName];
        const field = record.fields[fieldName];
        
        // 检查条件
        for (const condition of conditions) {
            if (this.evaluateCondition(condition.condition, value, record)) {
                return condition.formatter(value, condition.options);
            }
        }
        
        // 默认格式化
        return this.formatValue(record, fieldName);
    }
    
    evaluateCondition(condition, value, record) {
        if (typeof condition === 'function') {
            return condition(value, record);
        }
        
        if (typeof condition === 'object') {
            const { operator, operand } = condition;
            
            switch (operator) {
                case '>': return value > operand;
                case '<': return value < operand;
                case '>=': return value >= operand;
                case '<=': return value <= operand;
                case '==': return value == operand;
                case '===': return value === operand;
                case '!=': return value != operand;
                case '!==': return value !== operand;
                default: return false;
            }
        }
        
        return Boolean(condition);
    }
}

// 在组件中使用高级格式化器
class FormattedFieldComponent extends Component {
    static template = xml`
        <div class="formatted-field">
            <label t-esc="field.string" />
            <div class="field-value" t-esc="formattedValue" />
            <div t-if="hasConditions" class="conditional-value" t-esc="conditionalValue" />
        </div>
    `;
    
    setup() {
        this.formatter = new AdvancedValueFormatter();
        this.field = this.props.field;
        this.record = this.props.record;
        this.fieldName = this.props.fieldName;
        
        // 基本格式化值
        this.formattedValue = this.formatter.formatValue(
            this.record,
            this.fieldName,
            this.props.fieldInfo
        );
        
        // 条件格式化值
        this.hasConditions = Boolean(this.props.conditions);
        if (this.hasConditions) {
            this.conditionalValue = this.formatter.formatWithConditions(
                this.record,
                this.fieldName,
                this.props.conditions
            );
        }
    }
}
```

## 最佳实践

### 1. 性能优化
```javascript
// ✅ 推荐：缓存计算结果
const classNameCache = new Map();

function cachedComputeViewClassName(viewType, rootNode, additionalClassList) {
    const cacheKey = `${viewType}_${rootNode?.outerHTML}_${additionalClassList.join(',')}`;

    if (classNameCache.has(cacheKey)) {
        return classNameCache.get(cacheKey);
    }

    const result = computeViewClassName(viewType, rootNode, additionalClassList);
    classNameCache.set(cacheKey, result);

    return result;
}
```

### 2. 类型安全
```javascript
// ✅ 推荐：类型检查
function safeGetFormattedValue(record, fieldName, fieldInfo) {
    if (!record || !record.fields || !record.data) {
        console.warn('无效的记录对象');
        return '';
    }

    if (!(fieldName in record.fields)) {
        console.warn(`字段 ${fieldName} 不存在`);
        return '';
    }

    return getFormattedValue(record, fieldName, fieldInfo);
}
```

### 3. 错误处理
```javascript
// ✅ 推荐：错误边界
function robustProcessButton(node) {
    try {
        return processButton(node);
    } catch (error) {
        console.error('按钮处理失败:', error);
        return {};
    }
}
```

## 总结

Odoo 视图工具模块提供了丰富的工具函数：

**核心优势**:
- **类名管理**: 智能的视图类名计算和管理
- **值格式化**: 强大的字段值格式化系统
- **类型检查**: 完整的字段类型检查工具
- **装饰器**: 灵活的样式装饰器系统
- **工具函数**: 实用的通用工具函数

**适用场景**:
- 视图样式管理
- 字段值显示
- 按钮参数处理
- 权限控制
- 数据格式化

**设计优势**:
- 函数式设计
- 类型安全
- 性能优化
- 易于扩展

这个工具模块为 Odoo Web 客户端提供了强大的视图开发支持，是构建高质量视图的重要基础。

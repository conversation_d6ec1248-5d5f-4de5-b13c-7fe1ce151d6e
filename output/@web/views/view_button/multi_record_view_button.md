# MultiRecordViewButton - 多记录视图按钮

## 概述

`multi_record_view_button.js` 是 Odoo Web 客户端的多记录视图按钮组件，负责处理作用于多个记录的按钮操作。该模块包含31行代码，是一个功能专门的按钮组件，专门用于处理列表视图中的批量操作按钮，具备多记录选择、域传递、上下文构建、批量执行等特性，是批量操作的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/view_button/multi_record_view_button.js`
- **行数**: 31
- **模块**: `@web/views/view_button/multi_record_view_button`

## 依赖关系

```javascript
// 核心依赖
'@web/views/view_button/view_button'    // 基础视图按钮
```

## 核心功能

### 1. 组件定义

```javascript
const MultiRecordViewButton = class MultiRecordViewButton extends ViewButton {
    static props = [...ViewButton.props, "list", "domain"];
}
```

**组件特性**:
- **继承基类**: 继承ViewButton的所有功能
- **扩展属性**: 添加list和domain属性
- **多记录支持**: 专门处理多记录操作
- **批量操作**: 支持批量操作执行

### 2. 点击处理

```javascript
async onClick() {
    const { clickParams, list } = this.props;
    const resIds = await list.getResIds(true);
    clickParams.buttonContext = {
        active_domain: this.props.domain,
        active_ids: resIds,
        active_model: list.resModel,
    };

    this.env.onClickViewButton({
        clickParams,
        getResParams: () => ({
            context: list.context,
            evalContext: list.evalContext,
            resModel: list.resModel,
            resIds,
        }),
    });
}
```

**点击处理功能**:
- **记录获取**: 获取所有选中的记录ID
- **上下文构建**: 构建包含活动记录信息的上下文
- **域传递**: 传递当前视图的域条件
- **参数封装**: 封装执行所需的所有参数

### 3. 上下文管理

```javascript
clickParams.buttonContext = {
    active_domain: this.props.domain,
    active_ids: resIds,
    active_model: list.resModel,
};
```

**上下文管理功能**:
- **活动域**: 传递当前视图的过滤域
- **活动ID**: 传递选中记录的ID列表
- **活动模型**: 传递当前操作的模型名称
- **标准格式**: 使用Odoo标准的上下文格式

### 4. 参数传递

```javascript
getResParams: () => ({
    context: list.context,
    evalContext: list.evalContext,
    resModel: list.resModel,
    resIds,
})
```

**参数传递功能**:
- **列表上下文**: 传递列表视图的上下文
- **评估上下文**: 传递表达式评估上下文
- **资源模型**: 传递资源模型名称
- **资源ID**: 传递资源ID列表

## 使用场景

### 1. 多记录视图按钮管理器

```javascript
// 多记录视图按钮管理器
class MultiRecordViewButtonManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置多记录按钮配置
        this.multiRecordButtonConfig = {
            enableBatchOperations: true,
            enableSelectionValidation: true,
            enableProgressTracking: true,
            enableConfirmation: true,
            enableErrorHandling: true,
            enableLogging: false,
            maxBatchSize: 1000,
            confirmationThreshold: 10
        };
        
        // 设置批量操作类型
        this.batchOperationTypes = new Map([
            ['delete', {
                name: 'Delete Records',
                requiresConfirmation: true,
                destructive: true,
                icon: 'fa-trash'
            }],
            ['archive', {
                name: 'Archive Records',
                requiresConfirmation: true,
                destructive: false,
                icon: 'fa-archive'
            }],
            ['export', {
                name: 'Export Records',
                requiresConfirmation: false,
                destructive: false,
                icon: 'fa-download'
            }],
            ['duplicate', {
                name: 'Duplicate Records',
                requiresConfirmation: false,
                destructive: false,
                icon: 'fa-copy'
            }]
        ]);
        
        // 设置选择验证规则
        this.selectionValidationRules = {
            enableMinimumSelection: true,
            enableMaximumSelection: true,
            enableTypeValidation: false,
            enableStatusValidation: false,
            minimumCount: 1,
            maximumCount: 1000,
            allowedStatuses: [],
            blockedStatuses: []
        };
        
        // 设置多记录统计
        this.multiRecordStatistics = {
            totalMultiRecordButtons: 0,
            totalBatchOperations: 0,
            operationsByType: new Map(),
            averageRecordsPerOperation: 0,
            totalRecordsProcessed: 0,
            successfulOperations: 0,
            failedOperations: 0,
            mostUsedOperation: null
        };
        
        this.initializeMultiRecordButtonSystem();
    }
    
    // 初始化多记录按钮系统
    initializeMultiRecordButtonSystem() {
        // 创建增强的多记录按钮
        this.createEnhancedMultiRecordButton();
        
        // 设置批量操作系统
        this.setupBatchOperationSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置监控系统
        this.setupMonitoringSystem();
    }
    
    // 创建增强的多记录按钮
    createEnhancedMultiRecordButton() {
        const originalButton = MultiRecordViewButton;
        
        this.EnhancedMultiRecordViewButton = class extends originalButton {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
                
                // 添加监控功能
                this.addMonitoringFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isProcessing: false,
                    selectedCount: 0,
                    processedCount: 0,
                    errorCount: 0,
                    lastOperationTime: null,
                    operationHistory: []
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的点击处理
                this.enhancedOnClick = async () => {
                    try {
                        // 验证选择
                        const validation = await this.validateSelection();
                        if (!validation.valid) {
                            this.showValidationError(validation.errors);
                            return;
                        }
                        
                        // 获取记录
                        const { clickParams, list } = this.props;
                        const resIds = await list.getResIds(true);
                        
                        // 更新状态
                        this.enhancedState.selectedCount = resIds.length;
                        this.enhancedState.isProcessing = true;
                        
                        // 检查确认需求
                        if (this.needsConfirmation(resIds.length)) {
                            const confirmed = await this.showBatchConfirmation(resIds.length);
                            if (!confirmed) {
                                this.enhancedState.isProcessing = false;
                                return;
                            }
                        }
                        
                        // 构建增强的上下文
                        const enhancedContext = this.buildEnhancedContext(resIds, list);
                        clickParams.buttonContext = enhancedContext;
                        
                        // 记录操作开始
                        this.recordOperationStart(resIds.length);
                        
                        // 执行操作
                        await this.executeWithProgress(() => {
                            return this.env.onClickViewButton({
                                clickParams,
                                getResParams: () => ({
                                    context: list.context,
                                    evalContext: list.evalContext,
                                    resModel: list.resModel,
                                    resIds,
                                }),
                            });
                        });
                        
                        // 记录操作成功
                        this.recordOperationSuccess(resIds.length);
                        
                    } catch (error) {
                        // 记录操作失败
                        this.recordOperationFailure(error);
                        
                        // 处理错误
                        await this.handleOperationError(error);
                    } finally {
                        this.enhancedState.isProcessing = false;
                    }
                };
                
                // 验证选择
                this.validateSelection = async () => {
                    const errors = [];
                    const { list } = this.props;
                    const resIds = await list.getResIds(true);
                    
                    // 最小选择验证
                    if (this.selectionValidationRules.enableMinimumSelection) {
                        if (resIds.length < this.selectionValidationRules.minimumCount) {
                            errors.push(`Please select at least ${this.selectionValidationRules.minimumCount} record(s)`);
                        }
                    }
                    
                    // 最大选择验证
                    if (this.selectionValidationRules.enableMaximumSelection) {
                        if (resIds.length > this.selectionValidationRules.maximumCount) {
                            errors.push(`Cannot select more than ${this.selectionValidationRules.maximumCount} record(s)`);
                        }
                    }
                    
                    // 批量大小验证
                    if (resIds.length > this.multiRecordButtonConfig.maxBatchSize) {
                        errors.push(`Batch size cannot exceed ${this.multiRecordButtonConfig.maxBatchSize} records`);
                    }
                    
                    return {
                        valid: errors.length === 0,
                        errors: errors,
                        selectedCount: resIds.length
                    };
                };
                
                // 检查确认需求
                this.needsConfirmation = (recordCount) => {
                    if (!this.multiRecordButtonConfig.enableConfirmation) {
                        return false;
                    }
                    
                    return recordCount >= this.multiRecordButtonConfig.confirmationThreshold;
                };
                
                // 显示批量确认
                this.showBatchConfirmation = async (recordCount) => {
                    return new Promise((resolve) => {
                        const dialog = useService("dialog");
                        
                        dialog.add(ConfirmationDialog, {
                            title: 'Confirm Batch Operation',
                            body: `Are you sure you want to perform this operation on ${recordCount} record(s)?`,
                            confirm: () => resolve(true),
                            cancel: () => resolve(false),
                            confirmLabel: 'Proceed',
                            cancelLabel: 'Cancel'
                        });
                    });
                };
                
                // 构建增强的上下文
                this.buildEnhancedContext = (resIds, list) => {
                    return {
                        active_domain: this.props.domain,
                        active_ids: resIds,
                        active_model: list.resModel,
                        batch_operation: true,
                        batch_size: resIds.length,
                        operation_timestamp: new Date().toISOString(),
                        operation_id: this.generateOperationId()
                    };
                };
                
                // 带进度的执行
                this.executeWithProgress = async (operation) => {
                    if (!this.multiRecordButtonConfig.enableProgressTracking) {
                        return await operation();
                    }
                    
                    // 显示进度指示器
                    const progressIndicator = this.showProgressIndicator();
                    
                    try {
                        const result = await operation();
                        return result;
                    } finally {
                        if (progressIndicator) {
                            progressIndicator.close();
                        }
                    }
                };
                
                // 显示进度指示器
                this.showProgressIndicator = () => {
                    // 实现进度指示器逻辑
                    return {
                        close: () => {
                            // 关闭进度指示器
                        }
                    };
                };
                
                // 显示验证错误
                this.showValidationError = (errors) => {
                    const notification = useService("notification");
                    notification.add(
                        errors.join('\n'),
                        { type: 'danger', title: 'Selection Validation Error' }
                    );
                };
                
                // 处理操作错误
                this.handleOperationError = async (error) => {
                    console.error('Multi-record operation error:', error);
                    
                    if (this.multiRecordButtonConfig.enableErrorHandling) {
                        const notification = useService("notification");
                        notification.add(
                            error.message || 'An error occurred during the batch operation',
                            { type: 'danger', title: 'Batch Operation Error' }
                        );
                    }
                };
                
                // 生成操作ID
                this.generateOperationId = () => {
                    return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                };
                
                // 记录操作开始
                this.recordOperationStart = (recordCount) => {
                    this.multiRecordStatistics.totalBatchOperations++;
                    this.enhancedState.lastOperationTime = new Date();
                    
                    if (this.multiRecordButtonConfig.enableLogging) {
                        console.log(`Batch operation started: ${recordCount} records`);
                    }
                };
                
                // 记录操作成功
                this.recordOperationSuccess = (recordCount) => {
                    this.multiRecordStatistics.successfulOperations++;
                    this.multiRecordStatistics.totalRecordsProcessed += recordCount;
                    this.updateAverageRecordsPerOperation();
                    
                    // 记录操作历史
                    this.enhancedState.operationHistory.unshift({
                        timestamp: new Date(),
                        recordCount: recordCount,
                        success: true
                    });
                    
                    // 限制历史大小
                    if (this.enhancedState.operationHistory.length > 10) {
                        this.enhancedState.operationHistory.pop();
                    }
                };
                
                // 记录操作失败
                this.recordOperationFailure = (error) => {
                    this.multiRecordStatistics.failedOperations++;
                    this.enhancedState.errorCount++;
                    
                    // 记录操作历史
                    this.enhancedState.operationHistory.unshift({
                        timestamp: new Date(),
                        error: error.message,
                        success: false
                    });
                };
                
                // 更新平均记录数
                this.updateAverageRecordsPerOperation = () => {
                    if (this.multiRecordStatistics.successfulOperations > 0) {
                        this.multiRecordStatistics.averageRecordsPerOperation = 
                            this.multiRecordStatistics.totalRecordsProcessed / 
                            this.multiRecordStatistics.successfulOperations;
                    }
                };
                
                // 获取操作信息
                this.getOperationInfo = () => {
                    return {
                        isProcessing: this.enhancedState.isProcessing,
                        selectedCount: this.enhancedState.selectedCount,
                        processedCount: this.enhancedState.processedCount,
                        errorCount: this.enhancedState.errorCount,
                        lastOperationTime: this.enhancedState.lastOperationTime,
                        operationHistory: this.enhancedState.operationHistory.slice(0, 5)
                    };
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.multiRecordButtonConfig.enableSelectionValidation,
                    validate: () => this.validateSelection(),
                    rules: this.selectionValidationRules
                };
            }
            
            addMonitoringFeatures() {
                // 监控功能
                this.monitoringManager = {
                    enabled: this.multiRecordButtonConfig.enableProgressTracking,
                    getInfo: () => this.getOperationInfo(),
                    statistics: this.multiRecordStatistics
                };
            }
            
            // 重写原始方法
            async onClick() {
                return await this.enhancedOnClick();
            }
        };
    }
    
    // 设置批量操作系统
    setupBatchOperationSystem() {
        this.batchOperationSystemConfig = {
            enabled: this.multiRecordButtonConfig.enableBatchOperations,
            types: this.batchOperationTypes,
            maxSize: this.multiRecordButtonConfig.maxBatchSize
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.multiRecordButtonConfig.enableSelectionValidation,
            rules: this.selectionValidationRules
        };
    }
    
    // 设置监控系统
    setupMonitoringSystem() {
        this.monitoringSystemConfig = {
            enabled: this.multiRecordButtonConfig.enableProgressTracking,
            statistics: this.multiRecordStatistics
        };
    }
    
    // 创建多记录按钮
    createMultiRecordButton(props) {
        const button = new this.EnhancedMultiRecordViewButton(props);
        this.multiRecordStatistics.totalMultiRecordButtons++;
        return button;
    }
    
    // 注册批量操作类型
    registerBatchOperationType(name, config) {
        this.batchOperationTypes.set(name, config);
    }
    
    // 获取多记录统计
    getMultiRecordStatistics() {
        return {
            ...this.multiRecordStatistics,
            successRate: (this.multiRecordStatistics.successfulOperations / 
                         Math.max(this.multiRecordStatistics.totalBatchOperations, 1)) * 100,
            failureRate: (this.multiRecordStatistics.failedOperations / 
                         Math.max(this.multiRecordStatistics.totalBatchOperations, 1)) * 100,
            operationTypeVariety: this.multiRecordStatistics.operationsByType.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理批量操作类型
        this.batchOperationTypes.clear();
        
        // 清理统计
        this.multiRecordStatistics.operationsByType.clear();
        
        // 重置统计
        this.multiRecordStatistics = {
            totalMultiRecordButtons: 0,
            totalBatchOperations: 0,
            operationsByType: new Map(),
            averageRecordsPerOperation: 0,
            totalRecordsProcessed: 0,
            successfulOperations: 0,
            failedOperations: 0,
            mostUsedOperation: null
        };
    }
}

// 使用示例
const multiRecordButtonManager = new MultiRecordViewButtonManager();

// 创建多记录按钮
const multiRecordButton = multiRecordButtonManager.createMultiRecordButton({
    list: listComponent,
    domain: [['state', '=', 'draft']],
    clickParams: {
        type: 'object',
        name: 'action_confirm'
    }
});

// 注册自定义批量操作
multiRecordButtonManager.registerBatchOperationType('custom_action', {
    name: 'Custom Action',
    requiresConfirmation: true,
    destructive: false,
    icon: 'fa-cog'
});

// 获取统计信息
const stats = multiRecordButtonManager.getMultiRecordStatistics();
console.log('Multi-record button statistics:', stats);
```

## 技术特点

### 1. 继承设计
- **基类继承**: 继承ViewButton的所有功能
- **属性扩展**: 扩展多记录相关属性
- **方法重写**: 重写点击处理方法
- **功能增强**: 增强批量操作功能

### 2. 批量操作
- **多记录选择**: 处理多个记录的选择
- **批量执行**: 支持批量操作执行
- **上下文传递**: 传递批量操作上下文
- **状态管理**: 管理批量操作状态

### 3. 上下文构建
- **标准格式**: 使用Odoo标准上下文格式
- **完整信息**: 包含所有必要的上下文信息
- **域传递**: 传递当前视图的域条件
- **模型信息**: 传递模型和记录信息

### 4. 简洁实现
- **最小代码**: 极简的代码实现
- **专注功能**: 专注于多记录操作
- **高效执行**: 高效的批量操作执行
- **易于扩展**: 易于扩展和定制

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **功能继承**: 继承基础按钮功能
- **行为扩展**: 扩展多记录行为
- **接口一致**: 保持一致的接口

### 2. 策略模式 (Strategy Pattern)
- **操作策略**: 不同的批量操作策略
- **验证策略**: 不同的选择验证策略
- **执行策略**: 不同的执行策略

### 3. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 装饰基础按钮功能
- **上下文装饰**: 装饰执行上下文
- **行为装饰**: 装饰按钮行为

### 4. 模板方法模式 (Template Method Pattern)
- **执行模板**: 定义批量操作执行模板
- **验证模板**: 定义选择验证模板
- **处理模板**: 定义错误处理模板

## 注意事项

1. **性能考虑**: 处理大量记录时的性能
2. **内存管理**: 避免内存泄漏
3. **用户体验**: 提供清晰的批量操作反馈
4. **错误处理**: 正确处理批量操作错误

## 扩展建议

1. **进度跟踪**: 添加批量操作进度跟踪
2. **部分成功**: 处理部分成功的批量操作
3. **操作历史**: 记录批量操作历史
4. **权限检查**: 增强权限检查机制
5. **性能优化**: 优化大批量操作性能

该多记录视图按钮为Odoo Web客户端提供了完整的批量操作解决方案，通过简洁的设计和强大的功能确保了多记录操作的高效性和可靠性。

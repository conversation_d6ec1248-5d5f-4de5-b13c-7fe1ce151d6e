# ViewButtonHook - 视图按钮钩子

## 概述

`view_button_hook.js` 是 Odoo Web 客户端的视图按钮钩子模块，负责提供按钮执行相关的钩子函数和工具方法。该模块包含150行代码，是一个功能专门的钩子工具模块，专门用于处理按钮执行、状态管理、确认对话框、动作执行等操作，具备按钮禁用、执行回调、确认机制、错误处理等特性，是按钮行为管理的重要工具。

## 文件信息
- **路径**: `/web/static/src/views/view_button/view_button_hook.js`
- **行数**: 150
- **模块**: `@web/views/view_button/view_button_hook`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/hooks'                // 工具钩子
'@web/core/py_js/py'                   // Python表达式解析
'@web/core/confirmation_dialog/confirmation_dialog' // 确认对话框
'@odoo/owl'                           // OWL框架
```

## 核心功能

### 1. 按钮执行回调

```javascript
async function executeButtonCallback(el, fct) {
    let btns = [];
    
    function disableButtons() {
        btns = [
            ...btns,
            ...el.querySelectorAll("button:not([disabled])"),
            ...document.querySelectorAll(".o-overlay-container button:not([disabled])"),
        ];
        for (const btn of btns) {
            btn.setAttribute("disabled", "1");
        }
    }

    function enableButtons() {
        for (const btn of btns) {
            btn.removeAttribute("disabled");
        }
    }

    disableButtons();
    let res;
    try {
        res = await fct();
    } finally {
        enableButtons();
    }
    return res;
}
```

**执行回调功能**:
- **按钮禁用**: 执行期间禁用所有按钮
- **异常安全**: 确保按钮在异常情况下也能重新启用
- **全局禁用**: 禁用页面和覆盖层中的所有按钮
- **状态恢复**: 执行完成后恢复按钮状态

### 2. 工具函数

```javascript
function undefinedAsTrue(val) {
    return typeof val === "undefined" || val;
}
```

**工具函数功能**:
- **默认值处理**: 将undefined值视为true
- **布尔转换**: 简化布尔值判断
- **配置处理**: 处理可选配置参数
- **兼容性**: 提供向后兼容性

### 3. 按钮钩子选项

```javascript
/**
 * @typedef {Object} Options
 * @property {Function} [afterExecuteAction] - 执行动作后的回调
 * @property {Function} [beforeExecuteAction] - 执行动作前的回调
 * @property {Function} [getEvalParams] - 获取评估参数
 * @property {Function} [onClose] - 关闭时的回调
 */
```

**选项配置**:
- **前置回调**: 动作执行前的处理
- **后置回调**: 动作执行后的处理
- **参数获取**: 动态获取评估参数
- **关闭处理**: 处理关闭事件

### 4. 按钮钩子实现

```javascript
function useViewButton(options = {}) {
    const component = useComponent();
    const env = useEnv();
    const dialog = useService("dialog");
    const action = useService("action");
    
    const executeAction = async (actionData, context = {}) => {
        try {
            // 执行前置回调
            if (options.beforeExecuteAction) {
                await options.beforeExecuteAction(actionData, context);
            }
            
            // 执行动作
            const result = await action.doAction(actionData, {
                ...context,
                onClose: options.onClose
            });
            
            // 执行后置回调
            if (options.afterExecuteAction) {
                await options.afterExecuteAction(result, actionData, context);
            }
            
            return result;
            
        } catch (error) {
            console.error('Button action execution error:', error);
            throw error;
        }
    };
    
    return {
        executeAction,
        executeButtonCallback: (fct) => executeButtonCallback(component.el, fct)
    };
}
```

**钩子实现功能**:
- **服务集成**: 集成对话框和动作服务
- **回调管理**: 管理前置和后置回调
- **错误处理**: 统一的错误处理机制
- **上下文传递**: 正确传递执行上下文

## 使用场景

### 1. 视图按钮钩子管理器

```javascript
// 视图按钮钩子管理器
class ViewButtonHookManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置按钮钩子配置
        this.buttonHookConfig = {
            enableGlobalDisable: true,
            enableConfirmation: true,
            enableErrorHandling: true,
            enableLogging: false,
            enableMetrics: true,
            enableRetry: false,
            disableTimeout: 30000, // 30秒超时
            confirmationThreshold: 'destructive' // 确认阈值
        };
        
        // 设置执行选项
        this.executionOptions = {
            enableBeforeCallback: true,
            enableAfterCallback: true,
            enableProgressIndicator: true,
            enableCancellation: false,
            enableBatching: false,
            maxConcurrentExecutions: 1
        };
        
        // 设置确认配置
        this.confirmationConfig = {
            enableForDestructive: true,
            enableForBulk: true,
            enableCustomMessages: true,
            defaultTitle: 'Confirm Action',
            defaultMessage: 'Are you sure you want to proceed?',
            confirmButtonText: 'Confirm',
            cancelButtonText: 'Cancel'
        };
        
        // 设置执行统计
        this.executionStatistics = {
            totalExecutions: 0,
            successfulExecutions: 0,
            failedExecutions: 0,
            averageExecutionTime: 0,
            totalExecutionTime: 0,
            executionsByType: new Map(),
            errorsByType: new Map(),
            mostUsedAction: null
        };
        
        this.initializeButtonHookSystem();
    }
    
    // 初始化按钮钩子系统
    initializeButtonHookSystem() {
        // 创建增强的按钮钩子
        this.createEnhancedButtonHook();
        
        // 设置执行系统
        this.setupExecutionSystem();
        
        // 设置确认系统
        this.setupConfirmationSystem();
        
        // 设置监控系统
        this.setupMonitoringSystem();
    }
    
    // 创建增强的按钮钩子
    createEnhancedButtonHook() {
        this.enhancedUseViewButton = (options = {}) => {
            const component = useComponent();
            const env = useEnv();
            const dialog = useService("dialog");
            const action = useService("action");
            const notification = useService("notification");
            
            // 增强的执行动作
            const enhancedExecuteAction = async (actionData, context = {}) => {
                const startTime = Date.now();
                const executionId = this.generateExecutionId();
                
                try {
                    // 记录执行开始
                    this.recordExecutionStart(executionId, actionData);
                    
                    // 检查确认需求
                    if (this.needsConfirmation(actionData)) {
                        const confirmed = await this.showConfirmation(actionData);
                        if (!confirmed) {
                            return { cancelled: true };
                        }
                    }
                    
                    // 执行前置回调
                    if (options.beforeExecuteAction) {
                        await options.beforeExecuteAction(actionData, context);
                    }
                    
                    // 显示进度指示器
                    let progressIndicator;
                    if (this.executionOptions.enableProgressIndicator) {
                        progressIndicator = this.showProgressIndicator(actionData);
                    }
                    
                    // 执行动作
                    const result = await this.executeWithTimeout(
                        () => action.doAction(actionData, {
                            ...context,
                            onClose: options.onClose
                        }),
                        this.buttonHookConfig.disableTimeout
                    );
                    
                    // 隐藏进度指示器
                    if (progressIndicator) {
                        progressIndicator.close();
                    }
                    
                    // 执行后置回调
                    if (options.afterExecuteAction) {
                        await options.afterExecuteAction(result, actionData, context);
                    }
                    
                    // 记录成功执行
                    this.recordExecutionSuccess(executionId, Date.now() - startTime);
                    
                    return result;
                    
                } catch (error) {
                    // 记录执行失败
                    this.recordExecutionFailure(executionId, error, Date.now() - startTime);
                    
                    // 处理错误
                    await this.handleExecutionError(error, actionData);
                    
                    throw error;
                }
            };
            
            // 增强的按钮回调执行
            const enhancedExecuteButtonCallback = async (fct) => {
                return await this.executeButtonCallbackWithEnhancements(component.el, fct);
            };
            
            return {
                executeAction: enhancedExecuteAction,
                executeButtonCallback: enhancedExecuteButtonCallback,
                needsConfirmation: (actionData) => this.needsConfirmation(actionData),
                showConfirmation: (actionData) => this.showConfirmation(actionData)
            };
        };
    }
    
    // 增强的按钮回调执行
    async executeButtonCallbackWithEnhancements(el, fct) {
        let btns = [];
        let timeoutId;
        
        const disableButtons = () => {
            btns = [
                ...btns,
                ...el.querySelectorAll("button:not([disabled])"),
                ...document.querySelectorAll(".o-overlay-container button:not([disabled])"),
            ];
            
            for (const btn of btns) {
                btn.setAttribute("disabled", "1");
                btn.classList.add("btn-loading");
            }
        };
        
        const enableButtons = () => {
            for (const btn of btns) {
                btn.removeAttribute("disabled");
                btn.classList.remove("btn-loading");
            }
            
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
        };
        
        // 设置超时保护
        const setupTimeout = () => {
            if (this.buttonHookConfig.disableTimeout > 0) {
                timeoutId = setTimeout(() => {
                    console.warn('Button execution timeout, re-enabling buttons');
                    enableButtons();
                }, this.buttonHookConfig.disableTimeout);
            }
        };
        
        disableButtons();
        setupTimeout();
        
        let res;
        try {
            res = await fct();
        } finally {
            enableButtons();
        }
        
        return res;
    }
    
    // 检查是否需要确认
    needsConfirmation(actionData) {
        if (!this.buttonHookConfig.enableConfirmation) {
            return false;
        }
        
        // 检查动作类型
        if (actionData.type === 'ir.actions.act_window_close') {
            return false;
        }
        
        // 检查破坏性操作
        if (this.confirmationConfig.enableForDestructive) {
            const destructiveActions = ['unlink', 'delete', 'remove'];
            if (destructiveActions.some(action => 
                actionData.name?.toLowerCase().includes(action) ||
                actionData.xml_id?.toLowerCase().includes(action)
            )) {
                return true;
            }
        }
        
        // 检查批量操作
        if (this.confirmationConfig.enableForBulk && actionData.context?.active_ids?.length > 1) {
            return true;
        }
        
        // 检查自定义确认标记
        if (actionData.confirm || actionData.context?.confirm) {
            return true;
        }
        
        return false;
    }
    
    // 显示确认对话框
    async showConfirmation(actionData) {
        return new Promise((resolve) => {
            const dialog = useService("dialog");
            
            const title = actionData.confirm_title || 
                         this.confirmationConfig.defaultTitle;
            
            const message = actionData.confirm || 
                           actionData.confirm_message ||
                           this.confirmationConfig.defaultMessage;
            
            dialog.add(ConfirmationDialog, {
                title: title,
                body: message,
                confirm: () => resolve(true),
                cancel: () => resolve(false),
                confirmLabel: this.confirmationConfig.confirmButtonText,
                cancelLabel: this.confirmationConfig.cancelButtonText
            });
        });
    }
    
    // 带超时的执行
    async executeWithTimeout(fn, timeout) {
        if (timeout <= 0) {
            return await fn();
        }
        
        return new Promise(async (resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error(`Execution timeout after ${timeout}ms`));
            }, timeout);
            
            try {
                const result = await fn();
                clearTimeout(timeoutId);
                resolve(result);
            } catch (error) {
                clearTimeout(timeoutId);
                reject(error);
            }
        });
    }
    
    // 显示进度指示器
    showProgressIndicator(actionData) {
        // 实现进度指示器逻辑
        return {
            close: () => {
                // 关闭进度指示器
            }
        };
    }
    
    // 处理执行错误
    async handleExecutionError(error, actionData) {
        if (!this.buttonHookConfig.enableErrorHandling) {
            return;
        }
        
        console.error('Button execution error:', error);
        
        // 显示错误通知
        const notification = useService("notification");
        notification.add(
            error.message || 'An error occurred while executing the action',
            { type: 'danger' }
        );
    }
    
    // 生成执行ID
    generateExecutionId() {
        return `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    // 记录执行开始
    recordExecutionStart(executionId, actionData) {
        this.executionStatistics.totalExecutions++;
        
        if (this.buttonHookConfig.enableLogging) {
            console.log(`Execution started: ${executionId}`, actionData);
        }
    }
    
    // 记录执行成功
    recordExecutionSuccess(executionId, duration) {
        this.executionStatistics.successfulExecutions++;
        this.executionStatistics.totalExecutionTime += duration;
        this.updateAverageExecutionTime();
        
        if (this.buttonHookConfig.enableLogging) {
            console.log(`Execution completed: ${executionId} (${duration}ms)`);
        }
    }
    
    // 记录执行失败
    recordExecutionFailure(executionId, error, duration) {
        this.executionStatistics.failedExecutions++;
        
        const errorType = error.constructor.name;
        const count = this.executionStatistics.errorsByType.get(errorType) || 0;
        this.executionStatistics.errorsByType.set(errorType, count + 1);
        
        if (this.buttonHookConfig.enableLogging) {
            console.error(`Execution failed: ${executionId} (${duration}ms)`, error);
        }
    }
    
    // 更新平均执行时间
    updateAverageExecutionTime() {
        if (this.executionStatistics.successfulExecutions > 0) {
            this.executionStatistics.averageExecutionTime = 
                this.executionStatistics.totalExecutionTime / this.executionStatistics.successfulExecutions;
        }
    }
    
    // 设置执行系统
    setupExecutionSystem() {
        this.executionSystemConfig = {
            enabled: true,
            options: this.executionOptions,
            timeout: this.buttonHookConfig.disableTimeout
        };
    }
    
    // 设置确认系统
    setupConfirmationSystem() {
        this.confirmationSystemConfig = {
            enabled: this.buttonHookConfig.enableConfirmation,
            config: this.confirmationConfig
        };
    }
    
    // 设置监控系统
    setupMonitoringSystem() {
        this.monitoringSystemConfig = {
            enabled: this.buttonHookConfig.enableMetrics,
            statistics: this.executionStatistics
        };
    }
    
    // 获取执行统计
    getExecutionStatistics() {
        return {
            ...this.executionStatistics,
            successRate: (this.executionStatistics.successfulExecutions / 
                         Math.max(this.executionStatistics.totalExecutions, 1)) * 100,
            failureRate: (this.executionStatistics.failedExecutions / 
                         Math.max(this.executionStatistics.totalExecutions, 1)) * 100,
            errorTypeVariety: this.executionStatistics.errorsByType.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理统计
        this.executionStatistics.executionsByType.clear();
        this.executionStatistics.errorsByType.clear();
        
        // 重置统计
        this.executionStatistics = {
            totalExecutions: 0,
            successfulExecutions: 0,
            failedExecutions: 0,
            averageExecutionTime: 0,
            totalExecutionTime: 0,
            executionsByType: new Map(),
            errorsByType: new Map(),
            mostUsedAction: null
        };
    }
}

// 使用示例
const buttonHookManager = new ViewButtonHookManager();

// 使用增强的按钮钩子
const useEnhancedViewButton = buttonHookManager.enhancedUseViewButton({
    beforeExecuteAction: async (actionData, context) => {
        console.log('Before action execution:', actionData);
    },
    afterExecuteAction: async (result, actionData, context) => {
        console.log('After action execution:', result);
    },
    onClose: () => {
        console.log('Action dialog closed');
    }
});

// 获取统计信息
const stats = buttonHookManager.getExecutionStatistics();
console.log('Button hook execution statistics:', stats);
```

## 技术特点

### 1. 状态管理
- **按钮禁用**: 执行期间自动禁用按钮
- **状态恢复**: 异常安全的状态恢复
- **全局控制**: 全局按钮状态控制
- **超时保护**: 防止按钮永久禁用

### 2. 执行控制
- **异步执行**: 支持异步操作执行
- **错误处理**: 完整的错误处理机制
- **回调管理**: 前置和后置回调管理
- **上下文传递**: 正确的上下文传递

### 3. 确认机制
- **条件确认**: 基于条件的确认机制
- **自定义消息**: 支持自定义确认消息
- **用户选择**: 处理用户确认选择
- **取消支持**: 支持操作取消

### 4. 工具函数
- **值处理**: 便捷的值处理函数
- **类型检查**: 类型检查和转换
- **默认值**: 默认值处理
- **兼容性**: 向后兼容性支持

## 设计模式

### 1. 钩子模式 (Hook Pattern)
- **可重用逻辑**: 封装可重用的按钮逻辑
- **状态管理**: 管理按钮相关状态
- **生命周期**: 处理按钮生命周期

### 2. 装饰器模式 (Decorator Pattern)
- **功能增强**: 增强按钮执行功能
- **行为装饰**: 装饰按钮行为
- **状态装饰**: 装饰按钮状态

### 3. 策略模式 (Strategy Pattern)
- **执行策略**: 不同的执行策略
- **确认策略**: 不同的确认策略
- **错误策略**: 不同的错误处理策略

### 4. 观察者模式 (Observer Pattern)
- **状态观察**: 观察按钮状态变化
- **执行观察**: 观察执行过程
- **错误观察**: 观察错误发生

## 注意事项

1. **内存泄漏**: 避免事件监听器内存泄漏
2. **异常处理**: 确保异常情况下的状态恢复
3. **性能影响**: 避免过度的DOM操作
4. **用户体验**: 提供清晰的执行反馈

## 扩展建议

1. **进度跟踪**: 添加执行进度跟踪
2. **批量操作**: 支持批量按钮操作
3. **权限检查**: 集成权限检查机制
4. **日志记录**: 增强日志记录功能
5. **性能监控**: 添加性能监控功能

该视图按钮钩子为Odoo Web客户端提供了完整的按钮执行管理解决方案，通过状态控制和执行管理确保了按钮操作的可靠性和用户体验。

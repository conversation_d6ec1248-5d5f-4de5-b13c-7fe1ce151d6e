# Odoo 标准视图属性 (Standard View Props) 学习资料

## 文件概述

**文件路径**: `output/@web/views/standard_view_props.js`  
**原始路径**: `/web/static/src/views/standard_view_props.js`  
**模块类型**: 核心视图模块 - 标准视图属性定义  
**代码行数**: 46 行  
**依赖关系**: 无外部依赖，纯属性定义模块

## 模块功能

标准视图属性模块是 Odoo Web 客户端的视图属性规范定义。该模块提供了：
- 视图组件的标准属性定义
- 属性类型验证规范
- 可选属性标识
- 视图间的统一接口
- 组件属性的类型安全

这个模块为所有视图组件提供了统一的属性规范，确保视图组件间的一致性和类型安全。

## 标准属性详解

### 核心属性定义
```javascript
const standardViewProps = {
    info: {
        type: Object,
    },
    resModel: String,
    arch: { type: Element },
    bannerRoute: { type: String, optional: true },
    className: { type: String, optional: true },
    comparison: { type: [Object, { value: null }], optional: true },
    context: { type: Object },
    createRecord: { type: Function, optional: true },
    display: { type: Object, optional: true },
    domain: { type: Array },
    fields: { type: Object },
    globalState: { type: Object, optional: true },
    groupBy: { type: Array, element: String },
    limit: { type: Number, optional: true },
    noBreadcrumbs: { type: Boolean, optional: true },
    orderBy: { type: Array, element: Object },
    relatedModels: { type: Object, optional: true },
    resId: { type: [Number, Boolean], optional: true },
    resIds: { type: Array, optional: true },
    searchMenuTypes: { type: Array, element: String },
    selectRecord: { type: Function, optional: true },
    state: { type: Object, optional: true },
    useSampleModel: { type: Boolean },
    updateActionState: { type: Function, optional: true },
};
```

### 必需属性 (Required Props)

#### 1. **info** - 视图信息对象
```javascript
info: { type: Object }
```
**功能**: 包含视图的基本信息和配置
**内容**: 
- 视图ID和类型
- 架构解析信息
- 字段节点信息
- 视图特定配置

#### 2. **resModel** - 资源模型
```javascript
resModel: String
```
**功能**: 指定视图操作的数据模型
**示例**: `'res.partner'`, `'sale.order'`, `'product.product'`

#### 3. **arch** - 视图架构
```javascript
arch: { type: Element }
```
**功能**: 视图的XML架构定义
**类型**: DOM Element对象

#### 4. **context** - 上下文对象
```javascript
context: { type: Object }
```
**功能**: 视图执行的上下文环境
**内容**: 
- 用户偏好设置
- 语言和时区信息
- 默认值和过滤条件

#### 5. **domain** - 域条件
```javascript
domain: { type: Array }
```
**功能**: 数据过滤条件
**格式**: `[['field', 'operator', 'value'], ...]`

#### 6. **fields** - 字段定义
```javascript
fields: { type: Object }
```
**功能**: 模型字段的完整定义
**内容**: 字段类型、属性、关系等信息

#### 7. **groupBy** - 分组字段
```javascript
groupBy: { type: Array, element: String }
```
**功能**: 数据分组的字段列表
**示例**: `['category_id', 'state']`

#### 8. **orderBy** - 排序规则
```javascript
orderBy: { type: Array, element: Object }
```
**功能**: 数据排序规则
**格式**: `[{name: 'field_name', asc: true}, ...]`

#### 9. **searchMenuTypes** - 搜索菜单类型
```javascript
searchMenuTypes: { type: Array, element: String }
```
**功能**: 支持的搜索菜单类型
**选项**: `['filter', 'groupBy', 'favorite', 'comparison']`

#### 10. **useSampleModel** - 使用示例模型
```javascript
useSampleModel: { type: Boolean }
```
**功能**: 是否使用示例数据模型

### 可选属性 (Optional Props)

#### 1. **bannerRoute** - 横幅路由
```javascript
bannerRoute: { type: String, optional: true }
```
**功能**: 横幅显示的路由地址

#### 2. **className** - CSS类名
```javascript
className: { type: String, optional: true }
```
**功能**: 视图容器的额外CSS类名

#### 3. **comparison** - 比较配置
```javascript
comparison: { type: [Object, { value: null }], optional: true }
```
**功能**: 数据比较功能的配置

#### 4. **createRecord** - 创建记录函数
```javascript
createRecord: { type: Function, optional: true }
```
**功能**: 自定义的记录创建函数

#### 5. **display** - 显示配置
```javascript
display: { type: Object, optional: true }
```
**功能**: 视图显示相关的配置选项

#### 6. **globalState** - 全局状态
```javascript
globalState: { type: Object, optional: true }
```
**功能**: 视图的全局状态信息

#### 7. **limit** - 记录限制
```javascript
limit: { type: Number, optional: true }
```
**功能**: 每页显示的记录数量限制

#### 8. **noBreadcrumbs** - 禁用面包屑
```javascript
noBreadcrumbs: { type: Boolean, optional: true }
```
**功能**: 是否禁用面包屑导航

#### 9. **relatedModels** - 关联模型
```javascript
relatedModels: { type: Object, optional: true }
```
**功能**: 相关联的模型定义

#### 10. **resId** - 记录ID
```javascript
resId: { type: [Number, Boolean], optional: true }
```
**功能**: 当前操作的记录ID

#### 11. **resIds** - 记录ID列表
```javascript
resIds: { type: Array, optional: true }
```
**功能**: 批量操作的记录ID列表

#### 12. **selectRecord** - 选择记录函数
```javascript
selectRecord: { type: Function, optional: true }
```
**功能**: 自定义的记录选择函数

#### 13. **state** - 视图状态
```javascript
state: { type: Object, optional: true }
```
**功能**: 视图的当前状态信息

#### 14. **updateActionState** - 更新动作状态函数
```javascript
updateActionState: { type: Function, optional: true }
```
**功能**: 更新动作状态的回调函数

## 使用示例

### 1. 基本视图组件
```javascript
class BasicView extends Component {
    static props = {
        ...standardViewProps,
        // 可以添加视图特定的属性
        customProp: { type: String, optional: true }
    };
    
    setup() {
        // 使用标准属性
        this.resModel = this.props.resModel;
        this.context = this.props.context;
        this.domain = this.props.domain;
        this.fields = this.props.fields;
        
        // 处理可选属性
        this.limit = this.props.limit || 80;
        this.className = this.props.className || '';
        
        console.log('视图信息:', this.props.info);
        console.log('架构:', this.props.arch);
    }
}
```

### 2. 列表视图实现
```javascript
class ListView extends Component {
    static template = xml`
        <div t-att-class="getViewClassName()">
            <div class="list-header">
                <h3 t-esc="getDisplayName()" />
                <div class="list-controls">
                    <button t-if="canCreate" 
                            t-on-click="createRecord"
                            class="btn btn-primary">
                        创建
                    </button>
                </div>
            </div>
            
            <div class="list-content">
                <table class="table">
                    <thead>
                        <tr>
                            <th t-foreach="visibleFields" 
                                t-as="field" 
                                t-key="field.name"
                                t-on-click="() => this.sortBy(field.name)">
                                <span t-esc="field.string" />
                                <i t-if="isSortedBy(field.name)" 
                                   t-att-class="getSortIcon(field.name)" />
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr t-foreach="records" 
                            t-as="record" 
                            t-key="record.id"
                            t-on-click="() => this.selectRecord(record)">
                            <td t-foreach="visibleFields" 
                                t-as="field" 
                                t-key="field.name">
                                <Field name="field.name" 
                                       record="record" 
                                       fieldInfo="field" />
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="list-footer">
                <Pager records="records" 
                       limit="limit" 
                       onPageChange="onPageChange" />
            </div>
        </div>
    `;
    
    static props = {
        ...standardViewProps,
        // 列表视图特定属性
        editable: { type: Boolean, optional: true },
        multiEdit: { type: Boolean, optional: true }
    };
    
    setup() {
        this.orm = useService("orm");
        this.records = useState([]);
        this.sortField = useState(null);
        this.sortOrder = useState('asc');
        
        // 从标准属性获取配置
        this.resModel = this.props.resModel;
        this.domain = this.props.domain;
        this.context = this.props.context;
        this.fields = this.props.fields;
        this.limit = this.props.limit || 80;
        this.orderBy = this.props.orderBy || [];
        
        onWillStart(() => {
            this.loadRecords();
        });
    }
    
    get visibleFields() {
        // 从架构中提取可见字段
        return this.extractFieldsFromArch(this.props.arch);
    }
    
    get canCreate() {
        return this.props.createRecord && 
               !this.props.display?.createButton === false;
    }
    
    getViewClassName() {
        const baseClass = 'o_list_view';
        const customClass = this.props.className || '';
        return `${baseClass} ${customClass}`.trim();
    }
    
    getDisplayName() {
        return this.props.info?.displayName || this.resModel;
    }
    
    async loadRecords() {
        try {
            const records = await this.orm.searchRead(
                this.resModel,
                this.domain,
                Object.keys(this.fields),
                {
                    context: this.context,
                    limit: this.limit,
                    order: this.buildOrderString()
                }
            );
            
            this.records = records;
        } catch (error) {
            console.error('加载记录失败:', error);
        }
    }
    
    buildOrderString() {
        return this.orderBy
            .map(order => `${order.name} ${order.asc ? 'ASC' : 'DESC'}`)
            .join(', ');
    }
    
    createRecord() {
        if (this.props.createRecord) {
            this.props.createRecord();
        }
    }
    
    selectRecord(record) {
        if (this.props.selectRecord) {
            this.props.selectRecord(record);
        }
    }
    
    sortBy(fieldName) {
        if (this.sortField === fieldName) {
            this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortField = fieldName;
            this.sortOrder = 'asc';
        }
        
        // 更新排序并重新加载
        this.updateOrderBy();
        this.loadRecords();
    }
    
    updateOrderBy() {
        if (this.sortField) {
            this.orderBy = [{
                name: this.sortField,
                asc: this.sortOrder === 'asc'
            }];
        }
    }
    
    isSortedBy(fieldName) {
        return this.sortField === fieldName;
    }
    
    getSortIcon(fieldName) {
        if (!this.isSortedBy(fieldName)) return '';
        return this.sortOrder === 'asc' ? 'fa fa-sort-up' : 'fa fa-sort-down';
    }
    
    extractFieldsFromArch(arch) {
        const fields = [];
        const fieldNodes = arch.querySelectorAll('field');
        
        fieldNodes.forEach(node => {
            const fieldName = node.getAttribute('name');
            if (fieldName && this.fields[fieldName]) {
                fields.push({
                    name: fieldName,
                    string: node.getAttribute('string') || this.fields[fieldName].string,
                    type: this.fields[fieldName].type,
                    widget: node.getAttribute('widget'),
                    options: this.parseOptions(node.getAttribute('options'))
                });
            }
        });
        
        return fields;
    }
    
    parseOptions(optionsStr) {
        if (!optionsStr) return {};
        try {
            return JSON.parse(optionsStr);
        } catch (error) {
            return {};
        }
    }
    
    onPageChange(offset) {
        // 处理分页变化
        this.loadRecords();
    }
}
```

## 最佳实践

### 1. 属性继承
```javascript
// ✅ 推荐：正确继承标准属性
class CustomView extends Component {
    static props = {
        ...standardViewProps,
        // 添加自定义属性
        customOption: { type: String, optional: true },
        onCustomEvent: { type: Function, optional: true }
    };
}
```

### 2. 属性默认值
```javascript
// ✅ 推荐：提供合理的默认值
class ViewWithDefaults extends Component {
    setup() {
        // 使用默认值
        this.limit = this.props.limit || 80;
        this.className = this.props.className || '';
        this.noBreadcrumbs = this.props.noBreadcrumbs || false;

        // 处理复杂默认值
        this.display = {
            createButton: true,
            editButton: true,
            deleteButton: true,
            ...this.props.display
        };
    }
}
```

### 3. 属性验证
```javascript
// ✅ 推荐：运行时属性验证
class SafeView extends Component {
    setup() {
        // 开发模式下验证属性
        if (odoo.debug) {
            this.validateProps();
        }
    }

    validateProps() {
        const required = ['resModel', 'arch', 'context', 'domain', 'fields'];

        required.forEach(prop => {
            if (!this.props[prop]) {
                throw new Error(`必需属性 ${prop} 缺失`);
            }
        });
    }
}
```

## 总结

Odoo 标准视图属性模块提供了重要的类型规范：

**核心优势**:
- **类型安全**: 完整的属性类型定义和验证
- **统一接口**: 所有视图组件的统一属性规范
- **可扩展性**: 支持自定义属性的扩展
- **文档化**: 清晰的属性文档和说明
- **开发友好**: 提供良好的开发体验

**适用场景**:
- 视图组件开发
- 属性类型验证
- 接口规范定义
- 代码文档生成
- 开发工具集成

**设计优势**:
- 声明式定义
- 类型安全
- 可选属性支持
- 扩展友好

这个属性定义模块为 Odoo Web 客户端提供了强大的类型安全保障，是构建可靠视图组件的重要基础。

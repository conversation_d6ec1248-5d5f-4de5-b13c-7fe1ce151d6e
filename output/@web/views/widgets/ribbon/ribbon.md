# Ribbon - 功能区组件

## 概述

`ribbon.js` 是 Odoo Web 客户端的功能区组件，负责在表单右上角显示装饰性的功能区标签。该模块包含78行代码，是一个功能完整的装饰组件，专门用于在表单视图中显示状态标签或重要信息，具备文本显示、颜色配置、尺寸适配、工具提示等特性，是用户界面美化和信息展示的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/widgets/ribbon/ribbon.js`
- **行数**: 78
- **模块**: `@web/views/widgets/ribbon/ribbon`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'                   // 翻译服务
'@web/core/registry'                           // 注册表
'@web/views/widgets/standard_widget_props'     // 标准组件属性
'@odoo/owl'                                    // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
class RibbonWidget extends Component {
    static template = "web.Ribbon";
    static props = {
        ...standardWidgetProps,
        text: { type: String },
        title: { type: String, optional: true },
        bgClass: { type: String, optional: true },
    };
    static defaultProps = {
        title: "",
        bgClass: "text-bg-success",
    };
}
```

**组件特性**:
- **标准属性**: 继承标准组件属性
- **文本显示**: 必需的文本内容
- **工具提示**: 可选的工具提示标题
- **背景样式**: 可选的背景颜色类
- **默认样式**: 默认使用成功色背景

### 2. 样式管理

```javascript
get classes() {
    let classes = this.props.bgClass;
    if (this.props.text.length > 15) {
        classes += " o_small";
    } else if (this.props.text.length > 10) {
        classes += " o_medium";
    }
    return classes;
}
```

**样式功能**:
- **基础样式**: 使用配置的背景样式类
- **尺寸适配**: 根据文本长度调整尺寸
- **小尺寸**: 超过15个字符使用小尺寸
- **中尺寸**: 10-15个字符使用中尺寸
- **动态计算**: 动态计算最终样式类

### 3. 属性提取

```javascript
extractProps: ({ attrs }) => {
    return {
        text: attrs.title || attrs.text,
        title: attrs.tooltip,
        bgClass: attrs.bg_color,
    };
}
```

**提取功能**:
- **文本提取**: 优先使用title，回退到text
- **工具提示**: 提取tooltip属性
- **背景色**: 提取bg_color属性
- **属性映射**: 映射XML属性到组件属性

### 4. 支持的属性

```javascript
supportedAttributes: [
    {
        label: _t("Title"),
        name: "title",
        type: "string",
    },
    {
        label: _t("Background color"),
        name: "bg_color",
        type: "string",
    },
    {
        label: _t("Tooltip"),
        name: "tooltip",
        type: "string",
    },
]
```

**属性配置**:
- **标题属性**: 字符串类型的标题
- **背景色**: 字符串类型的背景颜色
- **工具提示**: 字符串类型的工具提示
- **国际化**: 支持属性标签国际化

## 使用场景

### 1. 功能区管理器

```javascript
// 功能区管理器
class RibbonManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置功能区配置
        this.ribbonConfig = {
            enableAnimation: true,
            enableTooltips: true,
            enableCustomColors: true,
            enableTextTruncation: true,
            maxTextLength: 20,
            defaultPosition: 'top-right',
            animationDuration: 300
        };
        
        // 设置预定义颜色
        this.predefinedColors = new Map([
            ['primary', {
                name: 'Primary',
                class: 'text-bg-primary',
                color: '#0d6efd',
                textColor: 'white'
            }],
            ['secondary', {
                name: 'Secondary',
                class: 'text-bg-secondary',
                color: '#6c757d',
                textColor: 'white'
            }],
            ['success', {
                name: 'Success',
                class: 'text-bg-success',
                color: '#198754',
                textColor: 'white'
            }],
            ['danger', {
                name: 'Danger',
                class: 'text-bg-danger',
                color: '#dc3545',
                textColor: 'white'
            }],
            ['warning', {
                name: 'Warning',
                class: 'text-bg-warning',
                color: '#ffc107',
                textColor: 'black'
            }],
            ['info', {
                name: 'Info',
                class: 'text-bg-info',
                color: '#0dcaf0',
                textColor: 'black'
            }],
            ['light', {
                name: 'Light',
                class: 'text-bg-light',
                color: '#f8f9fa',
                textColor: 'black'
            }],
            ['dark', {
                name: 'Dark',
                class: 'text-bg-dark',
                color: '#212529',
                textColor: 'white'
            }]
        ]);
        
        // 设置尺寸配置
        this.sizeConfig = new Map([
            ['small', {
                name: 'Small',
                class: 'o_small',
                maxLength: 15,
                fontSize: '0.75rem'
            }],
            ['medium', {
                name: 'Medium',
                class: 'o_medium',
                maxLength: 10,
                fontSize: '0.875rem'
            }],
            ['large', {
                name: 'Large',
                class: '',
                maxLength: 10,
                fontSize: '1rem'
            }]
        ]);
        
        // 设置功能区统计
        this.ribbonStatistics = {
            totalRibbons: 0,
            ribbonsByColor: new Map(),
            ribbonsBySize: new Map(),
            ribbonsByText: new Map(),
            averageTextLength: 0,
            totalTextLength: 0,
            mostUsedColor: null,
            mostUsedSize: null
        };
        
        this.initializeRibbonSystem();
    }
    
    // 初始化功能区系统
    initializeRibbonSystem() {
        // 创建增强的功能区组件
        this.createEnhancedRibbon();
        
        // 设置颜色系统
        this.setupColorSystem();
        
        // 设置尺寸系统
        this.setupSizeSystem();
        
        // 设置动画系统
        this.setupAnimationSystem();
    }
    
    // 创建增强的功能区组件
    createEnhancedRibbon() {
        const originalComponent = RibbonWidget;
        
        this.EnhancedRibbonWidget = class extends originalComponent {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加动画功能
                this.addAnimationFeatures();
                
                // 添加交互功能
                this.addInteractionFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isVisible: true,
                    isAnimating: false,
                    currentSize: 'large',
                    currentColor: 'success',
                    textLength: 0,
                    truncatedText: '',
                    showFullText: false
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
                
                // 初始化状态
                this.initializeState();
            }
            
            addEnhancedMethods() {
                // 增强的样式计算
                this.enhancedClasses = () => {
                    let classes = this.props.bgClass || 'text-bg-success';
                    const textLength = this.props.text.length;
                    
                    // 计算尺寸
                    if (textLength > 15) {
                        classes += " o_small";
                        this.enhancedState.currentSize = 'small';
                    } else if (textLength > 10) {
                        classes += " o_medium";
                        this.enhancedState.currentSize = 'medium';
                    } else {
                        this.enhancedState.currentSize = 'large';
                    }
                    
                    // 添加动画类
                    if (this.ribbonConfig.enableAnimation) {
                        classes += " o_ribbon_animated";
                    }
                    
                    // 添加可见性类
                    if (!this.enhancedState.isVisible) {
                        classes += " o_ribbon_hidden";
                    }
                    
                    return classes;
                };
                
                // 获取显示文本
                this.getDisplayText = () => {
                    const text = this.props.text;
                    const maxLength = this.ribbonConfig.maxTextLength;
                    
                    if (!this.ribbonConfig.enableTextTruncation || text.length <= maxLength) {
                        return text;
                    }
                    
                    if (this.enhancedState.showFullText) {
                        return text;
                    }
                    
                    return text.substring(0, maxLength - 3) + '...';
                };
                
                // 获取颜色信息
                this.getColorInfo = () => {
                    const bgClass = this.props.bgClass || 'text-bg-success';
                    
                    for (const [colorName, colorInfo] of this.predefinedColors.entries()) {
                        if (bgClass.includes(colorName)) {
                            this.enhancedState.currentColor = colorName;
                            return colorInfo;
                        }
                    }
                    
                    return {
                        name: 'Custom',
                        class: bgClass,
                        color: 'inherit',
                        textColor: 'inherit'
                    };
                };
                
                // 获取尺寸信息
                this.getSizeInfo = () => {
                    const size = this.enhancedState.currentSize;
                    return this.sizeConfig.get(size) || this.sizeConfig.get('large');
                };
                
                // 切换文本显示
                this.toggleTextDisplay = () => {
                    if (this.ribbonConfig.enableTextTruncation) {
                        this.enhancedState.showFullText = !this.enhancedState.showFullText;
                    }
                };
                
                // 显示功能区
                this.show = () => {
                    this.enhancedState.isVisible = true;
                    
                    if (this.ribbonConfig.enableAnimation) {
                        this.animateShow();
                    }
                };
                
                // 隐藏功能区
                this.hide = () => {
                    if (this.ribbonConfig.enableAnimation) {
                        this.animateHide();
                    } else {
                        this.enhancedState.isVisible = false;
                    }
                };
                
                // 显示动画
                this.animateShow = () => {
                    this.enhancedState.isAnimating = true;
                    
                    setTimeout(() => {
                        this.enhancedState.isVisible = true;
                        this.enhancedState.isAnimating = false;
                    }, this.ribbonConfig.animationDuration);
                };
                
                // 隐藏动画
                this.animateHide = () => {
                    this.enhancedState.isAnimating = true;
                    
                    setTimeout(() => {
                        this.enhancedState.isVisible = false;
                        this.enhancedState.isAnimating = false;
                    }, this.ribbonConfig.animationDuration);
                };
                
                // 获取工具提示内容
                this.getTooltipContent = () => {
                    let tooltip = this.props.title || this.props.text;
                    
                    const colorInfo = this.getColorInfo();
                    const sizeInfo = this.getSizeInfo();
                    
                    if (colorInfo.name !== 'Custom') {
                        tooltip += ` (${colorInfo.name})`;
                    }
                    
                    if (this.enhancedState.currentSize !== 'large') {
                        tooltip += ` - ${sizeInfo.name} size`;
                    }
                    
                    return tooltip;
                };
                
                // 初始化状态
                this.initializeState = () => {
                    this.enhancedState.textLength = this.props.text.length;
                    this.enhancedState.truncatedText = this.getDisplayText();
                    
                    // 记录统计
                    this.recordRibbonCreation();
                };
                
                // 记录功能区创建
                this.recordRibbonCreation = () => {
                    this.ribbonStatistics.totalRibbons++;
                    this.ribbonStatistics.totalTextLength += this.enhancedState.textLength;
                    
                    // 记录颜色统计
                    const color = this.enhancedState.currentColor;
                    const colorCount = this.ribbonStatistics.ribbonsByColor.get(color) || 0;
                    this.ribbonStatistics.ribbonsByColor.set(color, colorCount + 1);
                    
                    // 记录尺寸统计
                    const size = this.enhancedState.currentSize;
                    const sizeCount = this.ribbonStatistics.ribbonsBySize.get(size) || 0;
                    this.ribbonStatistics.ribbonsBySize.set(size, sizeCount + 1);
                    
                    // 记录文本统计
                    const text = this.props.text;
                    const textCount = this.ribbonStatistics.ribbonsByText.get(text) || 0;
                    this.ribbonStatistics.ribbonsByText.set(text, textCount + 1);
                    
                    this.updateStatistics();
                };
                
                // 更新统计
                this.updateStatistics = () => {
                    // 更新平均文本长度
                    if (this.ribbonStatistics.totalRibbons > 0) {
                        this.ribbonStatistics.averageTextLength = 
                            this.ribbonStatistics.totalTextLength / this.ribbonStatistics.totalRibbons;
                    }
                    
                    // 更新最常用颜色
                    this.updateMostUsedColor();
                    
                    // 更新最常用尺寸
                    this.updateMostUsedSize();
                };
                
                // 更新最常用颜色
                this.updateMostUsedColor = () => {
                    let maxCount = 0;
                    let mostUsed = null;
                    
                    for (const [color, count] of this.ribbonStatistics.ribbonsByColor.entries()) {
                        if (count > maxCount) {
                            maxCount = count;
                            mostUsed = color;
                        }
                    }
                    
                    this.ribbonStatistics.mostUsedColor = mostUsed;
                };
                
                // 更新最常用尺寸
                this.updateMostUsedSize = () => {
                    let maxCount = 0;
                    let mostUsed = null;
                    
                    for (const [size, count] of this.ribbonStatistics.ribbonsBySize.entries()) {
                        if (count > maxCount) {
                            maxCount = count;
                            mostUsed = size;
                        }
                    }
                    
                    this.ribbonStatistics.mostUsedSize = mostUsed;
                };
                
                // 获取功能区信息
                this.getRibbonInfo = () => {
                    return {
                        text: this.props.text,
                        displayText: this.getDisplayText(),
                        title: this.props.title,
                        bgClass: this.props.bgClass,
                        colorInfo: this.getColorInfo(),
                        sizeInfo: this.getSizeInfo(),
                        isVisible: this.enhancedState.isVisible,
                        isAnimating: this.enhancedState.isAnimating,
                        textLength: this.enhancedState.textLength,
                        currentSize: this.enhancedState.currentSize,
                        currentColor: this.enhancedState.currentColor,
                        tooltip: this.getTooltipContent()
                    };
                };
            }
            
            addAnimationFeatures() {
                // 动画功能
                this.animationManager = {
                    enabled: this.ribbonConfig.enableAnimation,
                    show: () => this.show(),
                    hide: () => this.hide(),
                    duration: this.ribbonConfig.animationDuration
                };
            }
            
            addInteractionFeatures() {
                // 交互功能
                this.interactionManager = {
                    enabled: true,
                    toggleText: () => this.toggleTextDisplay(),
                    getInfo: () => this.getRibbonInfo()
                };
            }
            
            // 重写原始方法
            get classes() {
                return this.enhancedClasses();
            }
        };
    }
    
    // 设置颜色系统
    setupColorSystem() {
        this.colorSystemConfig = {
            enabled: this.ribbonConfig.enableCustomColors,
            predefined: this.predefinedColors,
            defaultColor: 'success'
        };
    }
    
    // 设置尺寸系统
    setupSizeSystem() {
        this.sizeSystemConfig = {
            enabled: true,
            config: this.sizeConfig,
            maxTextLength: this.ribbonConfig.maxTextLength
        };
    }
    
    // 设置动画系统
    setupAnimationSystem() {
        this.animationSystemConfig = {
            enabled: this.ribbonConfig.enableAnimation,
            duration: this.ribbonConfig.animationDuration,
            easing: 'ease-in-out'
        };
    }
    
    // 创建功能区组件
    createRibbon(props) {
        return new this.EnhancedRibbonWidget(props);
    }
    
    // 注册自定义颜色
    registerCustomColor(name, config) {
        this.predefinedColors.set(name, config);
    }
    
    // 获取功能区统计
    getRibbonStatistics() {
        return {
            ...this.ribbonStatistics,
            colorVariety: this.ribbonStatistics.ribbonsByColor.size,
            sizeVariety: this.ribbonStatistics.ribbonsBySize.size,
            textVariety: this.ribbonStatistics.ribbonsByText.size,
            averageTextLength: this.ribbonStatistics.averageTextLength
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理预定义颜色
        this.predefinedColors.clear();
        
        // 清理尺寸配置
        this.sizeConfig.clear();
        
        // 清理统计
        this.ribbonStatistics.ribbonsByColor.clear();
        this.ribbonStatistics.ribbonsBySize.clear();
        this.ribbonStatistics.ribbonsByText.clear();
        
        // 重置统计
        this.ribbonStatistics = {
            totalRibbons: 0,
            ribbonsByColor: new Map(),
            ribbonsBySize: new Map(),
            ribbonsByText: new Map(),
            averageTextLength: 0,
            totalTextLength: 0,
            mostUsedColor: null,
            mostUsedSize: null
        };
    }
}

// 使用示例
const ribbonManager = new RibbonManager();

// 创建功能区组件
const ribbon = ribbonManager.createRibbon({
    text: 'New Feature',
    title: 'This is a new feature',
    bgClass: 'text-bg-primary'
});

// 注册自定义颜色
ribbonManager.registerCustomColor('custom', {
    name: 'Custom',
    class: 'text-bg-custom',
    color: '#ff6b6b',
    textColor: 'white'
});

// 获取统计信息
const stats = ribbonManager.getRibbonStatistics();
console.log('Ribbon statistics:', stats);
```

## 技术特点

### 1. 装饰性设计
- **视觉吸引**: 提供视觉吸引力的装饰元素
- **状态指示**: 用于指示记录状态或重要信息
- **位置固定**: 固定在表单右上角位置
- **非侵入式**: 不影响表单的正常功能

### 2. 样式系统
- **Bootstrap集成**: 完整支持Bootstrap颜色类
- **动态尺寸**: 根据文本长度自动调整尺寸
- **颜色丰富**: 支持多种预定义颜色
- **自定义样式**: 支持自定义背景颜色

### 3. 文本处理
- **长度适配**: 根据文本长度调整显示
- **尺寸分级**: 提供小、中、大三种尺寸
- **文本截断**: 支持长文本截断显示
- **工具提示**: 提供详细的工具提示

### 4. 属性配置
- **灵活配置**: 支持多种属性配置
- **默认值**: 提供合理的默认值
- **属性映射**: 智能的属性映射机制
- **国际化**: 支持属性标签国际化

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- **视觉装饰**: 为表单添加视觉装饰
- **功能装饰**: 装饰表单的信息展示
- **样式装饰**: 装饰组件的外观

### 2. 策略模式 (Strategy Pattern)
- **尺寸策略**: 不同的尺寸计算策略
- **颜色策略**: 不同的颜色应用策略
- **显示策略**: 不同的文本显示策略

### 3. 工厂模式 (Factory Pattern)
- **组件工厂**: 创建功能区组件
- **样式工厂**: 创建样式配置
- **颜色工厂**: 创建颜色配置

### 4. 状态模式 (State Pattern)
- **尺寸状态**: 不同的尺寸状态
- **颜色状态**: 不同的颜色状态
- **显示状态**: 不同的显示状态

## 注意事项

1. **文本长度**: 注意文本长度对显示效果的影响
2. **颜色对比**: 确保文本和背景的对比度
3. **响应式**: 考虑不同屏幕尺寸的显示效果
4. **可访问性**: 确保功能区的可访问性

## 扩展建议

1. **动画效果**: 添加出现和消失动画
2. **交互功能**: 支持点击交互功能
3. **位置配置**: 支持不同位置配置
4. **图标支持**: 添加图标显示支持
5. **主题适配**: 适配不同的UI主题

该功能区组件为Odoo Web客户端提供了优雅的装饰性标签功能，通过丰富的样式配置和智能的尺寸适配确保了视觉效果的美观性和信息展示的有效性。

# NotificationAlert - 通知警告组件

## 概述

`notification_alert.js` 是 Odoo Web 客户端的通知警告组件，负责检测和显示浏览器通知权限状态。该模块包含26行代码，是一个功能简洁的警告组件，专门用于提醒用户浏览器通知被阻止的情况，具备权限检测、状态显示、用户提醒等特性，是用户体验优化的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/widgets/notification_alert/notification_alert.js`
- **行数**: 26
- **模块**: `@web/views/widgets/notification_alert/notification_alert`

## 依赖关系

```javascript
// 核心依赖
'@web/core/browser/browser'                    // 浏览器服务
'@web/core/registry'                           // 注册表
'@web/views/widgets/standard_widget_props'     // 标准组件属性
'@odoo/owl'                                    // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
class NotificationAlert extends Component {
    static props = standardWidgetProps;
    static template = "web.NotificationAlert";
    
    get isNotificationBlocked() {
        return browser.Notification && browser.Notification.permission === "denied";
    }
}
```

**组件特性**:
- **标准属性**: 使用标准组件属性
- **简洁模板**: 使用专门的警告模板
- **权限检测**: 检测浏览器通知权限
- **状态计算**: 计算通知阻止状态

### 2. 权限检测

```javascript
get isNotificationBlocked() {
    return browser.Notification && browser.Notification.permission === "denied";
}
```

**检测功能**:
- **API检查**: 检查浏览器是否支持通知API
- **权限状态**: 检查通知权限状态
- **阻止检测**: 检测通知是否被用户阻止
- **布尔返回**: 返回简单的布尔值

### 3. 组件注册

```javascript
const notificationAlert = {
    component: NotificationAlert,
};

registry.category("view_widgets").add("notification_alert", notificationAlert);
```

**注册功能**:
- **组件注册**: 注册到视图组件注册表
- **简单配置**: 最简单的组件配置
- **无属性提取**: 不需要额外的属性提取
- **直接使用**: 可直接在视图中使用

## 使用场景

### 1. 通知警告管理器

```javascript
// 通知警告管理器
class NotificationAlertManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置通知配置
        this.notificationConfig = {
            enablePermissionCheck: true,
            enableAutoRequest: false,
            enablePersistentAlert: true,
            enableUserGuidance: true,
            checkInterval: 30000, // 30秒检查一次
            maxRetries: 3,
            showInstructions: true
        };
        
        // 设置权限状态
        this.permissionStates = new Map([
            ['default', {
                name: 'Default',
                description: 'User has not been asked for permission',
                canRequest: true,
                showAlert: false,
                color: 'secondary'
            }],
            ['granted', {
                name: 'Granted',
                description: 'User has granted notification permission',
                canRequest: false,
                showAlert: false,
                color: 'success'
            }],
            ['denied', {
                name: 'Denied',
                description: 'User has denied notification permission',
                canRequest: false,
                showAlert: true,
                color: 'danger'
            }]
        ]);
        
        // 设置浏览器支持
        this.browserSupport = new Map([
            ['chrome', { supported: true, minVersion: 22 }],
            ['firefox', { supported: true, minVersion: 22 }],
            ['safari', { supported: true, minVersion: 6 }],
            ['edge', { supported: true, minVersion: 14 }],
            ['opera', { supported: true, minVersion: 25 }],
            ['ie', { supported: false, minVersion: null }]
        ]);
        
        // 设置通知统计
        this.notificationStatistics = {
            totalChecks: 0,
            permissionRequests: 0,
            permissionGrants: 0,
            permissionDenials: 0,
            alertsShown: 0,
            userInteractions: 0,
            currentPermission: 'unknown'
        };
        
        this.initializeNotificationSystem();
    }
    
    // 初始化通知系统
    initializeNotificationSystem() {
        // 创建增强的通知警告组件
        this.createEnhancedNotificationAlert();
        
        // 设置权限系统
        this.setupPermissionSystem();
        
        // 设置监控系统
        this.setupMonitoringSystem();
        
        // 设置指导系统
        this.setupGuidanceSystem();
    }
    
    // 创建增强的通知警告组件
    createEnhancedNotificationAlert() {
        const originalComponent = NotificationAlert;
        
        this.EnhancedNotificationAlert = class extends originalComponent {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加监控功能
                this.addMonitoringFeatures();
                
                // 添加交互功能
                this.addInteractionFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    currentPermission: 'unknown',
                    isSupported: false,
                    lastCheckTime: null,
                    checkCount: 0,
                    userInteracted: false,
                    showInstructions: false
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
                
                // 初始化检查
                this.initializePermissionCheck();
            }
            
            addEnhancedMethods() {
                // 增强的权限检测
                this.enhancedIsNotificationBlocked = () => {
                    this.enhancedState.checkCount++;
                    this.enhancedState.lastCheckTime = new Date();
                    
                    // 检查浏览器支持
                    this.enhancedState.isSupported = this.checkBrowserSupport();
                    
                    if (!this.enhancedState.isSupported) {
                        this.enhancedState.currentPermission = 'unsupported';
                        return false; // 不支持时不显示警告
                    }
                    
                    // 检查权限状态
                    if (browser.Notification) {
                        this.enhancedState.currentPermission = browser.Notification.permission;
                        
                        // 记录统计
                        this.recordPermissionCheck();
                        
                        return browser.Notification.permission === "denied";
                    }
                    
                    this.enhancedState.currentPermission = 'unavailable';
                    return false;
                };
                
                // 检查浏览器支持
                this.checkBrowserSupport = () => {
                    if (!browser.Notification) {
                        return false;
                    }
                    
                    // 检查特定浏览器版本
                    const userAgent = navigator.userAgent.toLowerCase();
                    
                    for (const [browserName, support] of this.browserSupport.entries()) {
                        if (userAgent.includes(browserName)) {
                            return support.supported;
                        }
                    }
                    
                    return true; // 默认支持
                };
                
                // 获取权限状态信息
                this.getPermissionInfo = () => {
                    const permission = this.enhancedState.currentPermission;
                    const stateInfo = this.permissionStates.get(permission);
                    
                    return {
                        permission: permission,
                        isSupported: this.enhancedState.isSupported,
                        isBlocked: permission === 'denied',
                        canRequest: stateInfo?.canRequest || false,
                        shouldShowAlert: stateInfo?.showAlert || false,
                        description: stateInfo?.description || 'Unknown permission state',
                        color: stateInfo?.color || 'secondary'
                    };
                };
                
                // 请求通知权限
                this.requestNotificationPermission = async () => {
                    if (!this.enhancedState.isSupported || !browser.Notification) {
                        return 'unsupported';
                    }
                    
                    if (browser.Notification.permission !== 'default') {
                        return browser.Notification.permission;
                    }
                    
                    try {
                        this.notificationStatistics.permissionRequests++;
                        
                        const permission = await browser.Notification.requestPermission();
                        this.enhancedState.currentPermission = permission;
                        
                        // 记录结果
                        if (permission === 'granted') {
                            this.notificationStatistics.permissionGrants++;
                        } else if (permission === 'denied') {
                            this.notificationStatistics.permissionDenials++;
                        }
                        
                        return permission;
                        
                    } catch (error) {
                        console.error('Failed to request notification permission:', error);
                        return 'error';
                    }
                };
                
                // 显示使用指导
                this.showInstructions = () => {
                    this.enhancedState.showInstructions = true;
                    this.enhancedState.userInteracted = true;
                    this.notificationStatistics.userInteractions++;
                };
                
                // 隐藏使用指导
                this.hideInstructions = () => {
                    this.enhancedState.showInstructions = false;
                };
                
                // 获取浏览器特定指导
                this.getBrowserInstructions = () => {
                    const userAgent = navigator.userAgent.toLowerCase();
                    
                    if (userAgent.includes('chrome')) {
                        return {
                            browser: 'Chrome',
                            steps: [
                                'Click the lock icon in the address bar',
                                'Select "Allow" for notifications',
                                'Refresh the page'
                            ]
                        };
                    } else if (userAgent.includes('firefox')) {
                        return {
                            browser: 'Firefox',
                            steps: [
                                'Click the shield icon in the address bar',
                                'Select "Allow" for notifications',
                                'Refresh the page'
                            ]
                        };
                    } else if (userAgent.includes('safari')) {
                        return {
                            browser: 'Safari',
                            steps: [
                                'Go to Safari > Preferences > Websites',
                                'Select "Notifications" from the left sidebar',
                                'Set this website to "Allow"'
                            ]
                        };
                    }
                    
                    return {
                        browser: 'Browser',
                        steps: [
                            'Look for notification settings in your browser',
                            'Allow notifications for this website',
                            'Refresh the page'
                        ]
                    };
                };
                
                // 测试通知
                this.testNotification = () => {
                    if (this.enhancedState.currentPermission === 'granted') {
                        new browser.Notification('Test Notification', {
                            body: 'Notifications are working correctly!',
                            icon: '/web/static/img/favicon.ico'
                        });
                    }
                };
                
                // 初始化权限检查
                this.initializePermissionCheck = () => {
                    // 立即检查一次
                    this.enhancedIsNotificationBlocked();
                    
                    // 设置定期检查
                    if (this.notificationConfig.checkInterval > 0) {
                        this.checkInterval = setInterval(() => {
                            this.enhancedIsNotificationBlocked();
                        }, this.notificationConfig.checkInterval);
                    }
                };
                
                // 记录权限检查
                this.recordPermissionCheck = () => {
                    this.notificationStatistics.totalChecks++;
                    this.notificationStatistics.currentPermission = this.enhancedState.currentPermission;
                    
                    if (this.enhancedState.currentPermission === 'denied') {
                        this.notificationStatistics.alertsShown++;
                    }
                };
                
                // 获取组件信息
                this.getAlertInfo = () => {
                    return {
                        isSupported: this.enhancedState.isSupported,
                        currentPermission: this.enhancedState.currentPermission,
                        isBlocked: this.enhancedIsNotificationBlocked(),
                        checkCount: this.enhancedState.checkCount,
                        lastCheckTime: this.enhancedState.lastCheckTime,
                        userInteracted: this.enhancedState.userInteracted,
                        showInstructions: this.enhancedState.showInstructions,
                        permissionInfo: this.getPermissionInfo(),
                        browserInstructions: this.getBrowserInstructions()
                    };
                };
                
                // 清理资源
                this.cleanup = () => {
                    if (this.checkInterval) {
                        clearInterval(this.checkInterval);
                        this.checkInterval = null;
                    }
                };
            }
            
            addMonitoringFeatures() {
                // 监控功能
                this.monitoringManager = {
                    enabled: this.notificationConfig.enablePermissionCheck,
                    check: () => this.enhancedIsNotificationBlocked(),
                    getInfo: () => this.getAlertInfo()
                };
            }
            
            addInteractionFeatures() {
                // 交互功能
                this.interactionManager = {
                    enabled: this.notificationConfig.enableUserGuidance,
                    request: () => this.requestNotificationPermission(),
                    showHelp: () => this.showInstructions(),
                    test: () => this.testNotification()
                };
            }
            
            // 重写原始方法
            get isNotificationBlocked() {
                return this.enhancedIsNotificationBlocked();
            }
            
            // 组件销毁时清理
            willUnmount() {
                this.cleanup();
            }
        };
    }
    
    // 设置权限系统
    setupPermissionSystem() {
        this.permissionSystemConfig = {
            enabled: this.notificationConfig.enablePermissionCheck,
            autoRequest: this.notificationConfig.enableAutoRequest,
            maxRetries: this.notificationConfig.maxRetries
        };
    }
    
    // 设置监控系统
    setupMonitoringSystem() {
        this.monitoringSystemConfig = {
            enabled: true,
            interval: this.notificationConfig.checkInterval,
            persistent: this.notificationConfig.enablePersistentAlert
        };
    }
    
    // 设置指导系统
    setupGuidanceSystem() {
        this.guidanceSystemConfig = {
            enabled: this.notificationConfig.enableUserGuidance,
            showInstructions: this.notificationConfig.showInstructions,
            browserSpecific: true
        };
    }
    
    // 创建通知警告组件
    createNotificationAlert(props) {
        return new this.EnhancedNotificationAlert(props);
    }
    
    // 检查全局权限状态
    checkGlobalPermissionStatus() {
        if (!browser.Notification) {
            return 'unsupported';
        }
        
        return browser.Notification.permission;
    }
    
    // 获取通知统计
    getNotificationStatistics() {
        return {
            ...this.notificationStatistics,
            grantRate: this.notificationStatistics.permissionRequests > 0 ? 
                      (this.notificationStatistics.permissionGrants / this.notificationStatistics.permissionRequests) * 100 : 0,
            denialRate: this.notificationStatistics.permissionRequests > 0 ? 
                       (this.notificationStatistics.permissionDenials / this.notificationStatistics.permissionRequests) * 100 : 0,
            interactionRate: this.notificationStatistics.alertsShown > 0 ? 
                            (this.notificationStatistics.userInteractions / this.notificationStatistics.alertsShown) * 100 : 0
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理权限状态
        this.permissionStates.clear();
        
        // 清理浏览器支持
        this.browserSupport.clear();
        
        // 重置统计
        this.notificationStatistics = {
            totalChecks: 0,
            permissionRequests: 0,
            permissionGrants: 0,
            permissionDenials: 0,
            alertsShown: 0,
            userInteractions: 0,
            currentPermission: 'unknown'
        };
    }
}

// 使用示例
const notificationAlertManager = new NotificationAlertManager();

// 创建通知警告组件
const notificationAlert = notificationAlertManager.createNotificationAlert({
    record: recordObject,
    readonly: false
});

// 检查全局权限状态
const permissionStatus = notificationAlertManager.checkGlobalPermissionStatus();
console.log('Notification permission status:', permissionStatus);

// 获取统计信息
const stats = notificationAlertManager.getNotificationStatistics();
console.log('Notification alert statistics:', stats);
```

## 技术特点

### 1. 简洁设计
- **最小代码**: 极简的代码实现
- **专注功能**: 专注于权限检测
- **轻量级**: 无额外依赖
- **高效执行**: 高效的权限检查

### 2. 权限检测
- **API检查**: 检查浏览器通知API支持
- **权限状态**: 检测通知权限状态
- **实时检测**: 实时检测权限变化
- **状态缓存**: 缓存检测结果

### 3. 用户体验
- **及时提醒**: 及时提醒用户权限问题
- **清晰指示**: 提供清晰的状态指示
- **用户指导**: 提供权限设置指导
- **非侵入式**: 非侵入式的提醒方式

### 4. 浏览器兼容
- **跨浏览器**: 支持主流浏览器
- **版本检测**: 检测浏览器版本支持
- **优雅降级**: 不支持时优雅降级
- **兼容性处理**: 处理兼容性问题

## 设计模式

### 1. 单例模式 (Singleton Pattern)
- **权限检测**: 全局唯一的权限检测
- **状态管理**: 全局状态管理
- **配置管理**: 全局配置管理

### 2. 观察者模式 (Observer Pattern)
- **权限观察**: 观察权限状态变化
- **状态观察**: 观察组件状态变化
- **事件观察**: 观察用户交互事件

### 3. 策略模式 (Strategy Pattern)
- **检测策略**: 不同的权限检测策略
- **提醒策略**: 不同的用户提醒策略
- **处理策略**: 不同的错误处理策略

### 4. 状态模式 (State Pattern)
- **权限状态**: 不同的权限状态处理
- **显示状态**: 不同的显示状态
- **交互状态**: 不同的交互状态

## 注意事项

1. **浏览器支持**: 检查浏览器通知API支持
2. **权限变化**: 监听权限状态变化
3. **用户体验**: 避免过度打扰用户
4. **性能影响**: 避免频繁的权限检查

## 扩展建议

1. **权限请求**: 添加权限请求功能
2. **用户指导**: 提供详细的设置指导
3. **统计分析**: 添加权限统计分析
4. **自动重试**: 支持权限请求重试
5. **个性化**: 支持个性化提醒设置

该通知警告组件为Odoo Web客户端提供了简洁有效的通知权限检测功能，通过智能的权限检测和用户友好的提醒确保了通知功能的正常使用。

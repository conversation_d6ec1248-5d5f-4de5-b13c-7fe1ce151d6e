# DocumentationLink - 文档链接组件

## 概述

`documentation_link.js` 是 Odoo Web 客户端的文档链接组件，负责生成指向 Odoo 官方文档的链接。该模块包含62行代码，是一个功能专门的链接组件，专门用于在界面中提供文档链接，具备URL生成、版本适配、样式管理、链接验证等特性，是用户帮助和文档导航的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/widgets/documentation_link/documentation_link.js`
- **行数**: 62
- **模块**: `@web/views/widgets/documentation_link/documentation_link`

## 依赖关系

```javascript
// 核心依赖
'@web/session'                                 // 会话服务
'@web/views/widgets/standard_widget_props'     // 标准组件属性
'@odoo/owl'                                    // OWL框架
'@web/core/registry'                           // 注册表
```

## 核心功能

### 1. 组件定义

```javascript
class DocumentationLink extends Component {
    static template = "web.DocumentationLink";
    static props = {
        ...standardWidgetProps,
        record: { type: Object, optional: 1 },
        path: { type: String },
        label: { type: String, optional: 1 },
        icon: { type: String, optional: 1 },
        alertLink: { type: Boolean, optional: 1 },
    };
}
```

**组件特性**:
- **标准属性**: 继承标准组件属性
- **可选记录**: 记录对象为可选
- **路径配置**: 必需的文档路径
- **标签文本**: 可选的链接标签
- **图标支持**: 可选的图标显示
- **警告链接**: 可选的警告样式

### 2. URL生成

```javascript
get url() {
    if (LINK_REGEX.test(this.props.path)) {
        return this.props.path;
    } else {
        const serverVersion = session.server_version_info.includes("final")
            ? `${session.server_version_info[0]}.${session.server_version_info[1]}`.replace("~", "-")
            : "master";
        return "https://www.odoo.com/documentation/" + serverVersion + this.props.path;
    }
}
```

**URL生成功能**:
- **链接检测**: 检测是否为完整URL
- **版本适配**: 根据服务器版本生成链接
- **路径拼接**: 拼接文档基础URL和路径
- **版本处理**: 处理正式版本和开发版本

### 3. 样式管理

```javascript
get classes() {
    let classes = "o_doc_link me-2";
    if (this.props.alertLink){
        classes += " alert-link";
    }
    return classes;
}
```

**样式功能**:
- **基础样式**: 提供基础文档链接样式
- **间距控制**: 添加右边距
- **警告样式**: 支持警告链接样式
- **动态样式**: 根据属性动态添加样式

### 4. 属性提取

```javascript
const documentationLink = {
    component: DocumentationLink,
    extractProps: ({ attrs }) => {
        const { path, label, icon, alert_link } = attrs;
        return {
            path,
            label,
            icon,
            alertLink: Boolean(alert_link),
        };
    },
    additionalClasses: ["d-inline"],
};
```

**属性提取功能**:
- **路径提取**: 提取文档路径
- **标签提取**: 提取链接标签
- **图标提取**: 提取图标配置
- **布尔转换**: 转换警告链接为布尔值
- **额外样式**: 添加内联显示样式

## 使用场景

### 1. 文档链接管理器

```javascript
// 文档链接管理器
class DocumentationLinkManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置文档链接配置
        this.docLinkConfig = {
            enableVersionDetection: true,
            enableLinkValidation: true,
            enableAnalytics: false,
            enableCaching: true,
            defaultLanguage: 'en',
            fallbackVersion: 'master',
            baseUrl: 'https://www.odoo.com/documentation/'
        };
        
        // 设置文档版本映射
        this.versionMapping = new Map([
            ['18.0', '18.0'],
            ['17.0', '17.0'],
            ['16.0', '16.0'],
            ['15.0', '15.0'],
            ['14.0', '14.0'],
            ['saas-18.1', 'master'],
            ['saas-17.4', 'master'],
            ['alpha', 'master'],
            ['beta', 'master']
        ]);
        
        // 设置文档分类
        this.docCategories = new Map([
            ['user', {
                name: 'User Documentation',
                basePath: '/user/',
                icon: 'fa-user',
                color: 'primary'
            }],
            ['admin', {
                name: 'Administrator Documentation',
                basePath: '/administration/',
                icon: 'fa-cog',
                color: 'warning'
            }],
            ['developer', {
                name: 'Developer Documentation',
                basePath: '/developer/',
                icon: 'fa-code',
                color: 'info'
            }],
            ['api', {
                name: 'API Documentation',
                basePath: '/developer/reference/',
                icon: 'fa-plug',
                color: 'success'
            }]
        ]);
        
        // 设置链接统计
        this.linkStatistics = {
            totalLinks: 0,
            linksByCategory: new Map(),
            linksByVersion: new Map(),
            clickCount: 0,
            mostClickedLink: null,
            averageClicksPerLink: 0
        };
        
        this.initializeDocLinkSystem();
    }
    
    // 初始化文档链接系统
    initializeDocLinkSystem() {
        // 创建增强的文档链接组件
        this.createEnhancedDocumentationLink();
        
        // 设置版本系统
        this.setupVersionSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置分析系统
        this.setupAnalyticsSystem();
    }
    
    // 创建增强的文档链接组件
    createEnhancedDocumentationLink() {
        const originalComponent = DocumentationLink;
        
        this.EnhancedDocumentationLink = class extends originalComponent {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加分析功能
                this.addAnalyticsFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isValidUrl: true,
                    clickCount: 0,
                    lastClickTime: null,
                    category: null,
                    version: null,
                    language: this.docLinkConfig.defaultLanguage
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的URL生成
                this.enhancedUrl = () => {
                    const basePath = this.props.path;
                    
                    // 检查是否为完整URL
                    if (LINK_REGEX.test(basePath)) {
                        return this.validateExternalUrl(basePath);
                    }
                    
                    // 生成Odoo文档URL
                    const version = this.getDocumentationVersion();
                    const language = this.getDocumentationLanguage();
                    const category = this.getDocumentationCategory();
                    
                    let url = this.docLinkConfig.baseUrl;
                    
                    // 添加版本
                    url += version;
                    
                    // 添加语言（如果不是英语）
                    if (language !== 'en') {
                        url += `/${language}`;
                    }
                    
                    // 添加路径
                    url += basePath;
                    
                    // 添加分析参数
                    if (this.docLinkConfig.enableAnalytics) {
                        url += this.getAnalyticsParams();
                    }
                    
                    this.enhancedState.version = version;
                    this.enhancedState.language = language;
                    this.enhancedState.category = category;
                    
                    return url;
                };
                
                // 获取文档版本
                this.getDocumentationVersion = () => {
                    if (!this.docLinkConfig.enableVersionDetection) {
                        return this.docLinkConfig.fallbackVersion;
                    }
                    
                    const serverVersion = session.server_version_info;
                    
                    if (serverVersion.includes("final")) {
                        const version = `${serverVersion[0]}.${serverVersion[1]}`;
                        return this.versionMapping.get(version) || version.replace("~", "-");
                    }
                    
                    return this.docLinkConfig.fallbackVersion;
                };
                
                // 获取文档语言
                this.getDocumentationLanguage = () => {
                    // 可以从用户设置或浏览器语言获取
                    const userLang = session.user_context?.lang || navigator.language;
                    const supportedLangs = ['en', 'fr', 'es', 'de', 'it', 'pt', 'ru', 'zh'];
                    
                    const lang = userLang.split('-')[0];
                    return supportedLangs.includes(lang) ? lang : 'en';
                };
                
                // 获取文档分类
                this.getDocumentationCategory = () => {
                    const path = this.props.path;
                    
                    for (const [categoryName, categoryInfo] of this.docCategories.entries()) {
                        if (path.startsWith(categoryInfo.basePath)) {
                            return categoryName;
                        }
                    }
                    
                    return 'user'; // 默认分类
                };
                
                // 验证外部URL
                this.validateExternalUrl = (url) => {
                    try {
                        new URL(url);
                        this.enhancedState.isValidUrl = true;
                        return url;
                    } catch {
                        this.enhancedState.isValidUrl = false;
                        return '#';
                    }
                };
                
                // 获取分析参数
                this.getAnalyticsParams = () => {
                    const params = new URLSearchParams();
                    params.set('utm_source', 'odoo_web');
                    params.set('utm_medium', 'documentation_link');
                    params.set('utm_campaign', 'help_system');
                    return '?' + params.toString();
                };
                
                // 增强的样式管理
                this.enhancedClasses = () => {
                    let classes = "o_doc_link me-2";
                    
                    // 添加警告样式
                    if (this.props.alertLink) {
                        classes += " alert-link";
                    }
                    
                    // 添加分类样式
                    const category = this.enhancedState.category;
                    if (category) {
                        const categoryInfo = this.docCategories.get(category);
                        if (categoryInfo) {
                            classes += ` text-${categoryInfo.color}`;
                        }
                    }
                    
                    // 添加状态样式
                    if (!this.enhancedState.isValidUrl) {
                        classes += " text-muted";
                    }
                    
                    return classes;
                };
                
                // 处理点击事件
                this.handleClick = () => {
                    this.enhancedState.clickCount++;
                    this.enhancedState.lastClickTime = new Date();
                    
                    // 记录点击统计
                    this.recordClick();
                    
                    // 分析跟踪
                    if (this.docLinkConfig.enableAnalytics) {
                        this.trackClick();
                    }
                };
                
                // 记录点击
                this.recordClick = () => {
                    this.linkStatistics.clickCount++;
                    
                    // 记录分类统计
                    const category = this.enhancedState.category;
                    if (category) {
                        const count = this.linkStatistics.linksByCategory.get(category) || 0;
                        this.linkStatistics.linksByCategory.set(category, count + 1);
                    }
                    
                    // 记录版本统计
                    const version = this.enhancedState.version;
                    if (version) {
                        const count = this.linkStatistics.linksByVersion.get(version) || 0;
                        this.linkStatistics.linksByVersion.set(version, count + 1);
                    }
                    
                    this.updateMostClickedLink();
                };
                
                // 跟踪点击
                this.trackClick = () => {
                    // 这里可以集成分析服务
                    console.log('Documentation link clicked:', {
                        path: this.props.path,
                        url: this.enhancedUrl(),
                        category: this.enhancedState.category,
                        version: this.enhancedState.version
                    });
                };
                
                // 更新最多点击链接
                this.updateMostClickedLink = () => {
                    // 简化实现，实际可以更复杂
                    this.linkStatistics.mostClickedLink = this.props.path;
                    
                    if (this.linkStatistics.totalLinks > 0) {
                        this.linkStatistics.averageClicksPerLink = 
                            this.linkStatistics.clickCount / this.linkStatistics.totalLinks;
                    }
                };
                
                // 获取链接信息
                this.getLinkInfo = () => {
                    return {
                        path: this.props.path,
                        url: this.enhancedUrl(),
                        label: this.props.label,
                        icon: this.props.icon,
                        category: this.enhancedState.category,
                        version: this.enhancedState.version,
                        language: this.enhancedState.language,
                        isValid: this.enhancedState.isValidUrl,
                        clickCount: this.enhancedState.clickCount,
                        lastClickTime: this.enhancedState.lastClickTime
                    };
                };
                
                // 生成工具提示
                this.getTooltip = () => {
                    const category = this.enhancedState.category;
                    const categoryInfo = this.docCategories.get(category);
                    
                    let tooltip = this.props.label || 'Documentation';
                    
                    if (categoryInfo) {
                        tooltip += ` (${categoryInfo.name})`;
                    }
                    
                    if (this.enhancedState.version) {
                        tooltip += ` - Version ${this.enhancedState.version}`;
                    }
                    
                    return tooltip;
                };
            }
            
            addAnalyticsFeatures() {
                // 分析功能
                this.analyticsManager = {
                    enabled: this.docLinkConfig.enableAnalytics,
                    track: () => this.trackClick(),
                    getInfo: () => this.getLinkInfo()
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.docLinkConfig.enableLinkValidation,
                    validate: (url) => this.validateExternalUrl(url),
                    isValid: () => this.enhancedState.isValidUrl
                };
            }
            
            // 重写原始方法
            get url() {
                return this.enhancedUrl();
            }
            
            get classes() {
                return this.enhancedClasses();
            }
        };
    }
    
    // 设置版本系统
    setupVersionSystem() {
        this.versionSystemConfig = {
            enabled: this.docLinkConfig.enableVersionDetection,
            mapping: this.versionMapping,
            fallback: this.docLinkConfig.fallbackVersion
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.docLinkConfig.enableLinkValidation,
            validateExternal: true,
            validateInternal: true
        };
    }
    
    // 设置分析系统
    setupAnalyticsSystem() {
        this.analyticsSystemConfig = {
            enabled: this.docLinkConfig.enableAnalytics,
            trackClicks: true,
            trackCategories: true
        };
    }
    
    // 创建文档链接
    createDocumentationLink(props) {
        const link = new this.EnhancedDocumentationLink(props);
        this.linkStatistics.totalLinks++;
        return link;
    }
    
    // 注册文档分类
    registerDocCategory(name, config) {
        this.docCategories.set(name, config);
    }
    
    // 注册版本映射
    registerVersionMapping(version, mappedVersion) {
        this.versionMapping.set(version, mappedVersion);
    }
    
    // 获取链接统计
    getLinkStatistics() {
        return {
            ...this.linkStatistics,
            categoryVariety: this.linkStatistics.linksByCategory.size,
            versionVariety: this.linkStatistics.linksByVersion.size,
            clickRate: this.linkStatistics.clickCount / 
                      Math.max(this.linkStatistics.totalLinks, 1)
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理版本映射
        this.versionMapping.clear();
        
        // 清理文档分类
        this.docCategories.clear();
        
        // 清理统计
        this.linkStatistics.linksByCategory.clear();
        this.linkStatistics.linksByVersion.clear();
        
        // 重置统计
        this.linkStatistics = {
            totalLinks: 0,
            linksByCategory: new Map(),
            linksByVersion: new Map(),
            clickCount: 0,
            mostClickedLink: null,
            averageClicksPerLink: 0
        };
    }
}

// 使用示例
const docLinkManager = new DocumentationLinkManager();

// 创建文档链接
const docLink = docLinkManager.createDocumentationLink({
    path: '/user/sales/sales/invoicing/overview',
    label: 'Sales Documentation',
    icon: 'fa-book',
    alertLink: false
});

// 注册自定义分类
docLinkManager.registerDocCategory('tutorial', {
    name: 'Tutorial Documentation',
    basePath: '/tutorial/',
    icon: 'fa-graduation-cap',
    color: 'secondary'
});

// 获取统计信息
const stats = docLinkManager.getLinkStatistics();
console.log('Documentation link statistics:', stats);
```

## 技术特点

### 1. 版本适配
- **自动检测**: 自动检测服务器版本
- **版本映射**: 映射版本到文档版本
- **回退机制**: 提供版本回退机制
- **开发版本**: 支持开发版本处理

### 2. URL处理
- **链接检测**: 检测完整URL和相对路径
- **路径拼接**: 智能拼接文档URL
- **参数处理**: 处理URL参数
- **验证机制**: 验证URL有效性

### 3. 样式管理
- **基础样式**: 提供基础链接样式
- **条件样式**: 根据条件添加样式
- **警告模式**: 支持警告链接样式
- **响应式**: 支持响应式设计

### 4. 属性处理
- **标准继承**: 继承标准组件属性
- **可选属性**: 支持多个可选属性
- **类型转换**: 智能类型转换
- **默认值**: 提供合理默认值

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- **URL策略**: 不同的URL生成策略
- **版本策略**: 不同的版本处理策略
- **样式策略**: 不同的样式应用策略

### 2. 工厂模式 (Factory Pattern)
- **链接工厂**: 创建不同类型的链接
- **URL工厂**: 创建不同格式的URL
- **样式工厂**: 创建不同的样式配置

### 3. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 装饰基础链接功能
- **样式装饰**: 装饰链接样式
- **行为装饰**: 装饰链接行为

### 4. 单例模式 (Singleton Pattern)
- **配置管理**: 全局配置管理
- **统计管理**: 全局统计管理
- **缓存管理**: 全局缓存管理

## 注意事项

1. **版本兼容**: 确保版本映射正确
2. **链接有效**: 验证生成的链接有效性
3. **用户体验**: 提供清晰的链接标识
4. **性能考虑**: 避免频繁的URL生成

## 扩展建议

1. **多语言**: 支持多语言文档链接
2. **缓存机制**: 添加URL缓存机制
3. **分析跟踪**: 集成分析跟踪功能
4. **离线检测**: 检测离线状态
5. **预加载**: 支持文档预加载

该文档链接组件为Odoo Web客户端提供了智能的文档链接生成功能，通过版本适配和URL处理确保了用户能够访问到正确的文档资源。

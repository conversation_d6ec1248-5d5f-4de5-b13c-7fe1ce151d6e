# StandardWidgetProps - 标准组件属性

## 概述

`standard_widget_props.js` 是 Odoo Web 客户端的标准组件属性定义模块，负责定义所有组件通用的标准属性。该模块包含10行代码，是一个基础的属性定义模块，专门用于提供组件的标准属性规范，具备属性类型定义、可选性控制、通用性保证等特性，是组件系统的基础模块。

## 文件信息
- **路径**: `/web/static/src/views/widgets/standard_widget_props.js`
- **行数**: 10
- **模块**: `@web/views/widgets/standard_widget_props`

## 依赖关系

```javascript
// 无外部依赖
// 纯属性定义模块
```

## 核心功能

### 1. 标准属性定义

```javascript
const standardWidgetProps = {
    readonly: { type: Boolean, optional: true },
    record: { type: Object },
};
```

**属性定义**:
- **readonly**: 只读状态属性，布尔类型，可选
- **record**: 记录对象属性，对象类型，必需

### 2. 属性特性

**readonly属性**:
- **类型**: Boolean
- **可选性**: 可选(optional: true)
- **用途**: 控制组件的只读状态
- **默认值**: 通常为false

**record属性**:
- **类型**: Object
- **可选性**: 必需
- **用途**: 提供记录数据对象
- **内容**: 包含字段数据、元数据等

## 使用场景

### 1. 标准组件属性管理器

```javascript
// 标准组件属性管理器
class StandardWidgetPropsManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置属性配置
        this.propsConfig = {
            enableValidation: true,
            enableTypeChecking: true,
            enableDefaultValues: true,
            enableInheritance: true,
            strictMode: false
        };
        
        // 设置基础属性类型
        this.basePropertyTypes = new Map([
            ['Boolean', {
                name: 'Boolean',
                validator: (value) => typeof value === 'boolean',
                defaultValue: false,
                serializable: true
            }],
            ['String', {
                name: 'String',
                validator: (value) => typeof value === 'string',
                defaultValue: '',
                serializable: true
            }],
            ['Number', {
                name: 'Number',
                validator: (value) => typeof value === 'number',
                defaultValue: 0,
                serializable: true
            }],
            ['Object', {
                name: 'Object',
                validator: (value) => typeof value === 'object' && value !== null,
                defaultValue: {},
                serializable: true
            }],
            ['Array', {
                name: 'Array',
                validator: (value) => Array.isArray(value),
                defaultValue: [],
                serializable: true
            }],
            ['Function', {
                name: 'Function',
                validator: (value) => typeof value === 'function',
                defaultValue: () => {},
                serializable: false
            }]
        ]);
        
        // 设置扩展属性
        this.extendedProps = new Map([
            ['id', { type: 'String', optional: true, description: 'Component ID' }],
            ['class', { type: 'String', optional: true, description: 'CSS class' }],
            ['style', { type: 'Object', optional: true, description: 'Inline styles' }],
            ['disabled', { type: 'Boolean', optional: true, description: 'Disabled state' }],
            ['visible', { type: 'Boolean', optional: true, description: 'Visibility state' }],
            ['tooltip', { type: 'String', optional: true, description: 'Tooltip text' }],
            ['context', { type: 'Object', optional: true, description: 'Context data' }],
            ['domain', { type: 'Array', optional: true, description: 'Domain filter' }]
        ]);
        
        // 设置属性统计
        this.propsStatistics = {
            totalProps: 0,
            propsByType: new Map(),
            optionalProps: 0,
            requiredProps: 0,
            validationErrors: 0,
            usageCount: new Map()
        };
        
        this.initializePropsSystem();
    }
    
    // 初始化属性系统
    initializePropsSystem() {
        // 创建增强的标准属性
        this.createEnhancedStandardProps();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置继承系统
        this.setupInheritanceSystem();
        
        // 设置默认值系统
        this.setupDefaultValueSystem();
    }
    
    // 创建增强的标准属性
    createEnhancedStandardProps() {
        this.enhancedStandardProps = {
            // 基础标准属性
            ...standardWidgetProps,
            
            // 扩展属性
            id: { type: 'String', optional: true },
            class: { type: 'String', optional: true },
            style: { type: 'Object', optional: true },
            disabled: { type: 'Boolean', optional: true },
            visible: { type: 'Boolean', optional: true },
            tooltip: { type: 'String', optional: true },
            context: { type: 'Object', optional: true },
            domain: { type: 'Array', optional: true },
            
            // 事件属性
            onClick: { type: 'Function', optional: true },
            onChange: { type: 'Function', optional: true },
            onFocus: { type: 'Function', optional: true },
            onBlur: { type: 'Function', optional: true },
            
            // 数据属性
            value: { type: ['String', 'Number', 'Boolean', 'Object'], optional: true },
            defaultValue: { type: ['String', 'Number', 'Boolean', 'Object'], optional: true },
            
            // 验证属性
            required: { type: 'Boolean', optional: true },
            validator: { type: 'Function', optional: true },
            
            // 显示属性
            label: { type: 'String', optional: true },
            placeholder: { type: 'String', optional: true },
            help: { type: 'String', optional: true },
            
            // 行为属性
            autofocus: { type: 'Boolean', optional: true },
            tabindex: { type: 'Number', optional: true }
        };
        
        this.updatePropsStatistics();
    }
    
    // 验证属性
    validateProps(props, propDefinitions) {
        const errors = [];
        
        // 检查必需属性
        for (const [propName, propDef] of Object.entries(propDefinitions)) {
            if (!propDef.optional && !(propName in props)) {
                errors.push(`Required property '${propName}' is missing`);
            }
        }
        
        // 检查属性类型
        for (const [propName, propValue] of Object.entries(props)) {
            const propDef = propDefinitions[propName];
            if (propDef) {
                const isValid = this.validatePropType(propValue, propDef.type);
                if (!isValid) {
                    errors.push(`Property '${propName}' has invalid type`);
                }
            }
        }
        
        if (errors.length > 0) {
            this.propsStatistics.validationErrors += errors.length;
        }
        
        return {
            valid: errors.length === 0,
            errors: errors
        };
    }
    
    // 验证属性类型
    validatePropType(value, type) {
        if (Array.isArray(type)) {
            return type.some(t => this.validateSingleType(value, t));
        }
        return this.validateSingleType(value, type);
    }
    
    // 验证单一类型
    validateSingleType(value, type) {
        const typeInfo = this.basePropertyTypes.get(type);
        if (typeInfo) {
            return typeInfo.validator(value);
        }
        
        // 自定义类型验证
        switch (type) {
            case 'String':
                return typeof value === 'string';
            case 'Number':
                return typeof value === 'number';
            case 'Boolean':
                return typeof value === 'boolean';
            case 'Object':
                return typeof value === 'object' && value !== null;
            case 'Array':
                return Array.isArray(value);
            case 'Function':
                return typeof value === 'function';
            default:
                return true; // 未知类型默认通过
        }
    }
    
    // 获取默认值
    getDefaultValues(propDefinitions) {
        const defaults = {};
        
        for (const [propName, propDef] of Object.entries(propDefinitions)) {
            if (propDef.optional && propDef.defaultValue !== undefined) {
                defaults[propName] = propDef.defaultValue;
            } else if (propDef.type) {
                const typeInfo = this.basePropertyTypes.get(propDef.type);
                if (typeInfo) {
                    defaults[propName] = typeInfo.defaultValue;
                }
            }
        }
        
        return defaults;
    }
    
    // 合并属性
    mergeProps(...propSets) {
        const merged = {};
        
        for (const props of propSets) {
            Object.assign(merged, props);
        }
        
        return merged;
    }
    
    // 继承属性
    inheritProps(baseProps, extendedProps) {
        return {
            ...baseProps,
            ...extendedProps
        };
    }
    
    // 过滤属性
    filterProps(props, filter) {
        const filtered = {};
        
        for (const [propName, propValue] of Object.entries(props)) {
            if (filter(propName, propValue)) {
                filtered[propName] = propValue;
            }
        }
        
        return filtered;
    }
    
    // 转换属性
    transformProps(props, transformer) {
        const transformed = {};
        
        for (const [propName, propValue] of Object.entries(props)) {
            const result = transformer(propName, propValue);
            if (result !== undefined) {
                transformed[propName] = result;
            }
        }
        
        return transformed;
    }
    
    // 序列化属性
    serializeProps(props) {
        const serialized = {};
        
        for (const [propName, propValue] of Object.entries(props)) {
            if (this.isSerializable(propValue)) {
                serialized[propName] = propValue;
            }
        }
        
        return JSON.stringify(serialized);
    }
    
    // 检查是否可序列化
    isSerializable(value) {
        if (typeof value === 'function') {
            return false;
        }
        
        if (typeof value === 'object' && value !== null) {
            try {
                JSON.stringify(value);
                return true;
            } catch {
                return false;
            }
        }
        
        return true;
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.propsConfig.enableValidation,
            strictMode: this.propsConfig.strictMode,
            typeChecking: this.propsConfig.enableTypeChecking
        };
    }
    
    // 设置继承系统
    setupInheritanceSystem() {
        this.inheritanceSystemConfig = {
            enabled: this.propsConfig.enableInheritance,
            allowOverride: true,
            preserveOriginal: false
        };
    }
    
    // 设置默认值系统
    setupDefaultValueSystem() {
        this.defaultValueSystemConfig = {
            enabled: this.propsConfig.enableDefaultValues,
            autoApply: true,
            deepMerge: true
        };
    }
    
    // 注册属性类型
    registerPropertyType(name, config) {
        this.basePropertyTypes.set(name, config);
    }
    
    // 注册扩展属性
    registerExtendedProp(name, definition) {
        this.extendedProps.set(name, definition);
        this.enhancedStandardProps[name] = definition;
        this.updatePropsStatistics();
    }
    
    // 更新属性统计
    updatePropsStatistics() {
        this.propsStatistics.totalProps = Object.keys(this.enhancedStandardProps).length;
        this.propsStatistics.optionalProps = 0;
        this.propsStatistics.requiredProps = 0;
        this.propsStatistics.propsByType.clear();
        
        for (const [propName, propDef] of Object.entries(this.enhancedStandardProps)) {
            if (propDef.optional) {
                this.propsStatistics.optionalProps++;
            } else {
                this.propsStatistics.requiredProps++;
            }
            
            const type = propDef.type || 'Unknown';
            const count = this.propsStatistics.propsByType.get(type) || 0;
            this.propsStatistics.propsByType.set(type, count + 1);
        }
    }
    
    // 获取属性统计
    getPropsStatistics() {
        return {
            ...this.propsStatistics,
            typeVariety: this.propsStatistics.propsByType.size,
            optionalRate: (this.propsStatistics.optionalProps / this.propsStatistics.totalProps) * 100,
            requiredRate: (this.propsStatistics.requiredProps / this.propsStatistics.totalProps) * 100
        };
    }
    
    // 获取标准属性
    getStandardProps() {
        return { ...this.enhancedStandardProps };
    }
    
    // 销毁管理器
    destroy() {
        // 清理属性类型
        this.basePropertyTypes.clear();
        
        // 清理扩展属性
        this.extendedProps.clear();
        
        // 清理统计
        this.propsStatistics.propsByType.clear();
        this.propsStatistics.usageCount.clear();
        
        // 重置统计
        this.propsStatistics = {
            totalProps: 0,
            propsByType: new Map(),
            optionalProps: 0,
            requiredProps: 0,
            validationErrors: 0,
            usageCount: new Map()
        };
    }
}

// 使用示例
const propsManager = new StandardWidgetPropsManager();

// 获取标准属性
const standardProps = propsManager.getStandardProps();
console.log('Standard props:', standardProps);

// 验证属性
const validation = propsManager.validateProps(
    { readonly: true, record: { id: 1, name: 'Test' } },
    standardWidgetProps
);
console.log('Validation result:', validation);

// 注册自定义属性类型
propsManager.registerPropertyType('Email', {
    name: 'Email',
    validator: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
    defaultValue: '',
    serializable: true
});

// 获取统计信息
const stats = propsManager.getPropsStatistics();
console.log('Props statistics:', stats);
```

## 技术特点

### 1. 简洁设计
- **最小定义**: 极简的属性定义
- **核心属性**: 只包含最核心的属性
- **通用性**: 适用于所有组件
- **扩展性**: 易于扩展和继承

### 2. 类型安全
- **类型定义**: 明确的属性类型定义
- **可选性**: 清晰的可选性标记
- **验证支持**: 支持属性验证
- **默认值**: 支持默认值设置

### 3. 标准化
- **统一规范**: 提供统一的属性规范
- **一致性**: 保证组件属性的一致性
- **可预测**: 属性行为可预测
- **文档化**: 良好的文档支持

### 4. 基础性
- **无依赖**: 不依赖其他模块
- **纯定义**: 纯属性定义模块
- **轻量级**: 极轻量的实现
- **高性能**: 无性能开销

## 设计模式

### 1. 配置模式 (Configuration Pattern)
- **属性配置**: 统一的属性配置
- **类型配置**: 类型定义配置
- **验证配置**: 验证规则配置

### 2. 模板模式 (Template Pattern)
- **属性模板**: 标准属性模板
- **继承模板**: 属性继承模板
- **扩展模板**: 属性扩展模板

### 3. 策略模式 (Strategy Pattern)
- **验证策略**: 不同的验证策略
- **类型策略**: 不同的类型处理策略
- **默认值策略**: 不同的默认值策略

### 4. 单例模式 (Singleton Pattern)
- **全局属性**: 全局唯一的属性定义
- **共享配置**: 共享的配置对象
- **统一访问**: 统一的访问接口

## 属性详解

### 1. readonly属性
- **用途**: 控制组件是否为只读状态
- **类型**: Boolean
- **默认值**: false
- **影响**: 影响组件的交互行为

### 2. record属性
- **用途**: 提供记录数据对象
- **类型**: Object
- **必需性**: 必需属性
- **内容**: 包含字段数据、元数据等

## 扩展建议

1. **更多基础属性**: 添加更多通用基础属性
2. **类型验证**: 增强类型验证功能
3. **默认值**: 支持更复杂的默认值
4. **文档生成**: 自动生成属性文档
5. **IDE支持**: 提供IDE智能提示支持

## 注意事项

1. **向后兼容**: 保持向后兼容性
2. **性能影响**: 避免性能开销
3. **类型安全**: 确保类型安全
4. **文档同步**: 保持文档同步

该标准组件属性模块为Odoo Web客户端提供了基础的组件属性规范，通过简洁的定义和清晰的类型确保了组件系统的一致性和可维护性。

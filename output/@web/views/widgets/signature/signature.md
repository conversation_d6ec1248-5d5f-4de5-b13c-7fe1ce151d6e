# Signature - 签名组件

## 概述

`signature.js` 是 Odoo Web 客户端的签名组件，负责提供数字签名功能。该模块包含84行代码，是一个功能完整的签名组件，专门用于在表单中收集和保存用户的数字签名，具备签名对话框、图像上传、字段保存、名称提取等特性，是电子签名和文档认证的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/widgets/signature/signature.js`
- **行数**: 84
- **模块**: `@web/views/widgets/signature/signature`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                           // 注册表
'@web/core/signature/signature_dialog'         // 签名对话框
'@web/core/utils/hooks'                        // 工具钩子
'@web/views/widgets/standard_widget_props'     // 标准组件属性
'@odoo/owl'                                    // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
class SignatureWidget extends Component {
    static template = "web.SignatureWidget";
    static props = {
        ...standardWidgetProps,
        fullName: { type: String, optional: true },
        highlight: { type: Boolean, optional: true },
        string: { type: String },
        signatureField: { type: String, optional: true },
    };
}
```

**组件特性**:
- **标准属性**: 继承标准组件属性
- **全名字段**: 可选的签名者全名字段
- **高亮显示**: 可选的高亮显示模式
- **标签文本**: 必需的组件标签
- **签名字段**: 可选的签名保存字段名

### 2. 组件初始化

```javascript
setup() {
    this.dialogService = useService("dialog");
    this.orm = useService("orm");
}
```

**初始化功能**:
- **对话框服务**: 注入对话框服务
- **ORM服务**: 注入ORM服务用于数据操作
- **服务依赖**: 建立必要的服务依赖

### 3. 签名点击处理

```javascript
onClickSignature() {
    const nameAndSignatureProps = {
        mode: "draw",
        displaySignatureRatio: 3,
        signatureType: "signature",
        noInputName: true,
    };
    
    const { fullName, record } = this.props;
    let defaultName = "";
    if (fullName) {
        let signName;
        const fullNameData = record.data[fullName];
        if (record.fields[fullName].type === "many2one") {
            signName = fullNameData && fullNameData[1];
        } else {
            signName = fullNameData;
        }
        defaultName = signName === "" ? undefined : signName;
    }

    nameAndSignatureProps.defaultFont = this.props.defaultFont;

    const dialogProps = {
        defaultName,
        nameAndSignatureProps,
        uploadSignature: (data) => this.uploadSignature(data),
    };
    this.dialogService.add(SignatureDialog, dialogProps);
}
```

**点击处理功能**:
- **签名配置**: 配置签名对话框参数
- **名称提取**: 从记录中提取签名者名称
- **字段类型**: 处理不同类型的名称字段
- **对话框打开**: 打开签名对话框
- **回调设置**: 设置签名上传回调

### 4. 签名上传

```javascript
async uploadSignature({ signatureImage }) {
    const file = signatureImage.split(",")[1];
    const { model, resModel, resId } = this.props.record;

    await this.env.services.orm.write(resModel, [resId], {
        [this.props.signatureField]: file,
    });
    await this.props.record.load();
    model.notify();
}
```

**上传功能**:
- **图像处理**: 处理Base64编码的签名图像
- **数据保存**: 保存签名数据到指定字段
- **记录更新**: 重新加载记录数据
- **模型通知**: 通知模型数据变更

### 5. 属性提取

```javascript
extractProps: ({ attrs }) => {
    const { full_name: fullName, highlight, signature_field, string } = attrs;
    return {
        fullName,
        highlight: !!highlight,
        string,
        signatureField: signature_field || "signature",
    };
}
```

**提取功能**:
- **名称映射**: 映射full_name属性
- **布尔转换**: 转换highlight为布尔值
- **字段默认**: 提供签名字段默认值
- **属性清理**: 清理和转换属性

## 使用场景

### 1. 签名组件管理器

```javascript
// 签名组件管理器
class SignatureManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置签名配置
        this.signatureConfig = {
            enableDigitalSignature: true,
            enableHandwrittenSignature: true,
            enableTypedSignature: false,
            enableSignatureValidation: true,
            enableSignatureHistory: false,
            maxSignatureSize: 500 * 1024, // 500KB
            signatureFormat: 'png',
            signatureQuality: 0.8,
            defaultSignatureField: 'signature'
        };
        
        // 设置签名类型
        this.signatureTypes = new Map([
            ['draw', {
                name: 'Hand Drawn',
                mode: 'draw',
                description: 'Draw signature with mouse or touch',
                icon: 'fa-pencil-alt'
            }],
            ['type', {
                name: 'Typed',
                mode: 'type',
                description: 'Type signature with selected font',
                icon: 'fa-keyboard'
            }],
            ['upload', {
                name: 'Upload',
                mode: 'upload',
                description: 'Upload signature image file',
                icon: 'fa-upload'
            }]
        ]);
        
        // 设置签名字体
        this.signatureFonts = new Map([
            ['cursive', {
                name: 'Cursive',
                fontFamily: 'cursive',
                style: 'italic',
                weight: 'normal'
            }],
            ['script', {
                name: 'Script',
                fontFamily: 'Brush Script MT, cursive',
                style: 'normal',
                weight: 'normal'
            }],
            ['handwriting', {
                name: 'Handwriting',
                fontFamily: 'Comic Sans MS, cursive',
                style: 'normal',
                weight: 'normal'
            }]
        ]);
        
        // 设置签名统计
        this.signatureStatistics = {
            totalSignatures: 0,
            signaturesByType: new Map(),
            signaturesByField: new Map(),
            averageSignatureSize: 0,
            totalSignatureSize: 0,
            signaturesByUser: new Map(),
            mostUsedType: null,
            mostUsedField: null
        };
        
        this.initializeSignatureSystem();
    }
    
    // 初始化签名系统
    initializeSignatureSystem() {
        // 创建增强的签名组件
        this.createEnhancedSignature();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置历史系统
        this.setupHistorySystem();
        
        // 设置安全系统
        this.setupSecuritySystem();
    }
    
    // 创建增强的签名组件
    createEnhancedSignature() {
        const originalComponent = SignatureWidget;
        
        this.EnhancedSignatureWidget = class extends originalComponent {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
                
                // 添加历史功能
                this.addHistoryFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    hasSignature: false,
                    signatureType: 'draw',
                    signatureSize: 0,
                    signatureTimestamp: null,
                    signatureUser: null,
                    isValidSignature: true,
                    signatureHistory: []
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
                
                // 初始化状态
                this.initializeSignatureState();
            }
            
            addEnhancedMethods() {
                // 增强的签名点击处理
                this.enhancedOnClickSignature = () => {
                    try {
                        // 检查权限
                        if (!this.checkSignaturePermission()) {
                            this.showPermissionError();
                            return;
                        }
                        
                        // 准备签名配置
                        const signatureConfig = this.prepareSignatureConfig();
                        
                        // 打开签名对话框
                        this.openSignatureDialog(signatureConfig);
                        
                    } catch (error) {
                        this.handleSignatureError(error);
                    }
                };
                
                // 检查签名权限
                this.checkSignaturePermission = () => {
                    // 检查记录是否可编辑
                    if (this.props.readonly) {
                        return false;
                    }
                    
                    // 检查字段权限
                    const field = this.props.record.fields[this.props.signatureField];
                    if (field && field.readonly) {
                        return false;
                    }
                    
                    return true;
                };
                
                // 准备签名配置
                this.prepareSignatureConfig = () => {
                    const nameAndSignatureProps = {
                        mode: this.enhancedState.signatureType,
                        displaySignatureRatio: 3,
                        signatureType: "signature",
                        noInputName: true,
                        maxSize: this.signatureConfig.maxSignatureSize,
                        format: this.signatureConfig.signatureFormat,
                        quality: this.signatureConfig.signatureQuality
                    };
                    
                    // 设置默认名称
                    const defaultName = this.extractSignerName();
                    
                    // 设置默认字体
                    if (this.props.defaultFont) {
                        nameAndSignatureProps.defaultFont = this.props.defaultFont;
                    }
                    
                    return {
                        defaultName,
                        nameAndSignatureProps,
                        uploadSignature: (data) => this.enhancedUploadSignature(data)
                    };
                };
                
                // 提取签名者名称
                this.extractSignerName = () => {
                    const { fullName, record } = this.props;
                    
                    if (!fullName) {
                        return "";
                    }
                    
                    const fullNameData = record.data[fullName];
                    
                    if (record.fields[fullName].type === "many2one") {
                        return fullNameData && fullNameData[1] || "";
                    }
                    
                    return fullNameData || "";
                };
                
                // 打开签名对话框
                this.openSignatureDialog = (config) => {
                    this.dialogService.add(SignatureDialog, config);
                };
                
                // 增强的签名上传
                this.enhancedUploadSignature = async ({ signatureImage, signatureType }) => {
                    try {
                        // 验证签名
                        const validation = this.validateSignature(signatureImage);
                        if (!validation.valid) {
                            this.showValidationError(validation.error);
                            return;
                        }
                        
                        // 处理签名图像
                        const processedSignature = this.processSignatureImage(signatureImage);
                        
                        // 保存签名
                        await this.saveSignature(processedSignature);
                        
                        // 记录签名历史
                        if (this.signatureConfig.enableSignatureHistory) {
                            this.recordSignatureHistory(processedSignature, signatureType);
                        }
                        
                        // 更新状态
                        this.updateSignatureState(processedSignature, signatureType);
                        
                        // 记录统计
                        this.recordSignatureStatistics(signatureType);
                        
                    } catch (error) {
                        this.handleUploadError(error);
                    }
                };
                
                // 验证签名
                this.validateSignature = (signatureImage) => {
                    if (!this.signatureConfig.enableSignatureValidation) {
                        return { valid: true };
                    }
                    
                    // 检查图像格式
                    if (!signatureImage.startsWith('data:image/')) {
                        return {
                            valid: false,
                            error: 'Invalid signature image format'
                        };
                    }
                    
                    // 检查图像大小
                    const imageSize = this.calculateImageSize(signatureImage);
                    if (imageSize > this.signatureConfig.maxSignatureSize) {
                        return {
                            valid: false,
                            error: 'Signature image too large'
                        };
                    }
                    
                    return { valid: true };
                };
                
                // 计算图像大小
                this.calculateImageSize = (base64String) => {
                    const base64Data = base64String.split(',')[1];
                    return Math.ceil(base64Data.length * 0.75);
                };
                
                // 处理签名图像
                this.processSignatureImage = (signatureImage) => {
                    // 提取Base64数据
                    const base64Data = signatureImage.split(",")[1];
                    
                    // 添加时间戳和用户信息
                    const metadata = {
                        timestamp: new Date().toISOString(),
                        user: this.env.services.user.userId,
                        size: this.calculateImageSize(signatureImage)
                    };
                    
                    return {
                        data: base64Data,
                        metadata: metadata
                    };
                };
                
                // 保存签名
                this.saveSignature = async (processedSignature) => {
                    const { model, resModel, resId } = this.props.record;
                    
                    await this.env.services.orm.write(resModel, [resId], {
                        [this.props.signatureField]: processedSignature.data,
                    });
                    
                    await this.props.record.load();
                    model.notify();
                };
                
                // 记录签名历史
                this.recordSignatureHistory = (signature, type) => {
                    const historyEntry = {
                        timestamp: signature.metadata.timestamp,
                        user: signature.metadata.user,
                        type: type,
                        size: signature.metadata.size,
                        field: this.props.signatureField
                    };
                    
                    this.enhancedState.signatureHistory.push(historyEntry);
                };
                
                // 更新签名状态
                this.updateSignatureState = (signature, type) => {
                    this.enhancedState.hasSignature = true;
                    this.enhancedState.signatureType = type;
                    this.enhancedState.signatureSize = signature.metadata.size;
                    this.enhancedState.signatureTimestamp = signature.metadata.timestamp;
                    this.enhancedState.signatureUser = signature.metadata.user;
                    this.enhancedState.isValidSignature = true;
                };
                
                // 记录签名统计
                this.recordSignatureStatistics = (type) => {
                    this.signatureStatistics.totalSignatures++;
                    
                    // 记录类型统计
                    const typeCount = this.signatureStatistics.signaturesByType.get(type) || 0;
                    this.signatureStatistics.signaturesByType.set(type, typeCount + 1);
                    
                    // 记录字段统计
                    const field = this.props.signatureField;
                    const fieldCount = this.signatureStatistics.signaturesByField.get(field) || 0;
                    this.signatureStatistics.signaturesByField.set(field, fieldCount + 1);
                    
                    // 记录用户统计
                    const user = this.enhancedState.signatureUser;
                    const userCount = this.signatureStatistics.signaturesByUser.get(user) || 0;
                    this.signatureStatistics.signaturesByUser.set(user, userCount + 1);
                    
                    // 更新大小统计
                    this.signatureStatistics.totalSignatureSize += this.enhancedState.signatureSize;
                    this.updateAverageSize();
                    
                    this.updateMostUsedStatistics();
                };
                
                // 更新平均大小
                this.updateAverageSize = () => {
                    if (this.signatureStatistics.totalSignatures > 0) {
                        this.signatureStatistics.averageSignatureSize = 
                            this.signatureStatistics.totalSignatureSize / this.signatureStatistics.totalSignatures;
                    }
                };
                
                // 更新最常用统计
                this.updateMostUsedStatistics = () => {
                    // 更新最常用类型
                    let maxTypeCount = 0;
                    let mostUsedType = null;
                    
                    for (const [type, count] of this.signatureStatistics.signaturesByType.entries()) {
                        if (count > maxTypeCount) {
                            maxTypeCount = count;
                            mostUsedType = type;
                        }
                    }
                    
                    this.signatureStatistics.mostUsedType = mostUsedType;
                    
                    // 更新最常用字段
                    let maxFieldCount = 0;
                    let mostUsedField = null;
                    
                    for (const [field, count] of this.signatureStatistics.signaturesByField.entries()) {
                        if (count > maxFieldCount) {
                            maxFieldCount = count;
                            mostUsedField = field;
                        }
                    }
                    
                    this.signatureStatistics.mostUsedField = mostUsedField;
                };
                
                // 初始化签名状态
                this.initializeSignatureState = () => {
                    const signatureField = this.props.signatureField;
                    const signatureData = this.props.record.data[signatureField];
                    
                    if (signatureData) {
                        this.enhancedState.hasSignature = true;
                        this.enhancedState.signatureSize = this.calculateImageSize(`data:image/png;base64,${signatureData}`);
                    }
                };
                
                // 显示权限错误
                this.showPermissionError = () => {
                    const notification = useService("notification");
                    notification.add(
                        "You don't have permission to add signature",
                        { type: 'warning' }
                    );
                };
                
                // 显示验证错误
                this.showValidationError = (error) => {
                    const notification = useService("notification");
                    notification.add(error, { type: 'danger' });
                };
                
                // 处理签名错误
                this.handleSignatureError = (error) => {
                    console.error('Signature error:', error);
                    const notification = useService("notification");
                    notification.add(
                        'Failed to open signature dialog',
                        { type: 'danger' }
                    );
                };
                
                // 处理上传错误
                this.handleUploadError = (error) => {
                    console.error('Signature upload error:', error);
                    const notification = useService("notification");
                    notification.add(
                        'Failed to save signature',
                        { type: 'danger' }
                    );
                };
                
                // 获取签名信息
                this.getSignatureInfo = () => {
                    return {
                        hasSignature: this.enhancedState.hasSignature,
                        signatureType: this.enhancedState.signatureType,
                        signatureSize: this.enhancedState.signatureSize,
                        signatureTimestamp: this.enhancedState.signatureTimestamp,
                        signatureUser: this.enhancedState.signatureUser,
                        isValidSignature: this.enhancedState.isValidSignature,
                        signatureField: this.props.signatureField,
                        signerName: this.extractSignerName(),
                        history: this.enhancedState.signatureHistory
                    };
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.signatureConfig.enableSignatureValidation,
                    validate: (signature) => this.validateSignature(signature),
                    checkPermission: () => this.checkSignaturePermission()
                };
            }
            
            addHistoryFeatures() {
                // 历史功能
                this.historyManager = {
                    enabled: this.signatureConfig.enableSignatureHistory,
                    record: (signature, type) => this.recordSignatureHistory(signature, type),
                    getHistory: () => this.enhancedState.signatureHistory
                };
            }
            
            // 重写原始方法
            onClickSignature() {
                this.enhancedOnClickSignature();
            }
            
            async uploadSignature(data) {
                return await this.enhancedUploadSignature(data);
            }
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.signatureConfig.enableSignatureValidation,
            maxSize: this.signatureConfig.maxSignatureSize,
            allowedFormats: ['png', 'jpg', 'jpeg']
        };
    }
    
    // 设置历史系统
    setupHistorySystem() {
        this.historySystemConfig = {
            enabled: this.signatureConfig.enableSignatureHistory,
            maxHistoryEntries: 100,
            includeMetadata: true
        };
    }
    
    // 设置安全系统
    setupSecuritySystem() {
        this.securitySystemConfig = {
            enabled: true,
            encryptSignatures: false,
            auditTrail: true,
            userVerification: true
        };
    }
    
    // 创建签名组件
    createSignature(props) {
        return new this.EnhancedSignatureWidget(props);
    }
    
    // 注册签名类型
    registerSignatureType(name, config) {
        this.signatureTypes.set(name, config);
    }
    
    // 获取签名统计
    getSignatureStatistics() {
        return {
            ...this.signatureStatistics,
            typeVariety: this.signatureStatistics.signaturesByType.size,
            fieldVariety: this.signatureStatistics.signaturesByField.size,
            userVariety: this.signatureStatistics.signaturesByUser.size,
            averageSignatureSize: this.signatureStatistics.averageSignatureSize
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理签名类型
        this.signatureTypes.clear();
        
        // 清理签名字体
        this.signatureFonts.clear();
        
        // 清理统计
        this.signatureStatistics.signaturesByType.clear();
        this.signatureStatistics.signaturesByField.clear();
        this.signatureStatistics.signaturesByUser.clear();
        
        // 重置统计
        this.signatureStatistics = {
            totalSignatures: 0,
            signaturesByType: new Map(),
            signaturesByField: new Map(),
            averageSignatureSize: 0,
            totalSignatureSize: 0,
            signaturesByUser: new Map(),
            mostUsedType: null,
            mostUsedField: null
        };
    }
}

// 使用示例
const signatureManager = new SignatureManager();

// 创建签名组件
const signature = signatureManager.createSignature({
    record: recordObject,
    string: 'Customer Signature',
    fullName: 'customer_name',
    signatureField: 'customer_signature',
    highlight: true
});

// 注册自定义签名类型
signatureManager.registerSignatureType('biometric', {
    name: 'Biometric',
    mode: 'biometric',
    description: 'Biometric signature capture',
    icon: 'fa-fingerprint'
});

// 获取统计信息
const stats = signatureManager.getSignatureStatistics();
console.log('Signature statistics:', stats);
```

## 技术特点

### 1. 数字签名
- **多种模式**: 支持手绘、输入、上传等模式
- **图像处理**: 处理Base64编码的签名图像
- **质量控制**: 控制签名图像质量和大小
- **格式支持**: 支持多种图像格式

### 2. 数据集成
- **字段保存**: 保存签名到指定字段
- **记录更新**: 自动更新记录数据
- **模型通知**: 通知模型数据变更
- **ORM集成**: 深度集成ORM操作

### 3. 用户体验
- **对话框**: 使用专门的签名对话框
- **名称提取**: 智能提取签名者名称
- **字段类型**: 支持不同类型的名称字段
- **权限检查**: 检查签名权限

### 4. 安全性
- **数据验证**: 验证签名数据有效性
- **权限控制**: 控制签名操作权限
- **审计跟踪**: 记录签名操作历史
- **用户验证**: 验证签名用户身份

## 设计模式

### 1. 命令模式 (Command Pattern)
- **签名命令**: 封装签名操作
- **上传命令**: 封装上传操作
- **保存命令**: 封装保存操作

### 2. 策略模式 (Strategy Pattern)
- **签名策略**: 不同的签名模式策略
- **验证策略**: 不同的验证策略
- **保存策略**: 不同的保存策略

### 3. 观察者模式 (Observer Pattern)
- **状态观察**: 观察签名状态变化
- **数据观察**: 观察数据变化
- **事件观察**: 观察用户交互事件

### 4. 工厂模式 (Factory Pattern)
- **组件工厂**: 创建签名组件
- **对话框工厂**: 创建签名对话框
- **验证器工厂**: 创建验证器

## 注意事项

1. **图像大小**: 控制签名图像大小避免性能问题
2. **数据安全**: 确保签名数据的安全性
3. **用户权限**: 检查用户签名权限
4. **浏览器兼容**: 确保跨浏览器兼容性

## 扩展建议

1. **生物识别**: 支持生物识别签名
2. **时间戳**: 添加可信时间戳
3. **加密存储**: 支持签名加密存储
4. **批量签名**: 支持批量签名操作
5. **签名验证**: 添加签名验证功能

该签名组件为Odoo Web客户端提供了完整的数字签名解决方案，通过安全的数据处理和用户友好的界面确保了电子签名的可靠性和易用性。

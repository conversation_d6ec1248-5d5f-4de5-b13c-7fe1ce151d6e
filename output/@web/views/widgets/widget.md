# Widget - 组件基类

## 概述

`widget.js` 是 Odoo Web 客户端的组件基类模块，负责支持在视图架构中渲染 `<widget />` 标签。该模块包含134行代码，是一个核心的组件基类，专门用于处理视图中的组件渲染，具备组件注册、属性解析、动态渲染、样式管理等特性，是视图组件系统的基础模块。

## 文件信息
- **路径**: `/web/static/src/views/widgets/widget.js`
- **行数**: 134
- **模块**: `@web/views/widgets/widget`

## 依赖关系

```javascript
// 核心依赖
'@web/core/py_js/py'                   // Python表达式解析
'@web/core/registry'                   // 注册表
'@odoo/owl'                           // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
class Widget extends Component {
    static template = xml`
        <div t-att-class="classNames" t-att-style="props.style">
            <t t-component="widget.component" t-props="widgetProps" />
        </div>`;
    
    static props = {
        "*": true,
    };
}
```

**组件特性**:
- **动态模板**: 使用动态组件渲染
- **样式支持**: 支持动态样式和类名
- **属性透传**: 支持所有属性透传
- **包装器**: 作为其他组件的包装器

### 2. 注册表验证

```javascript
viewWidgetRegistry.addValidation({
    component: { validate: (c) => c.prototype instanceof Component },
    extractProps: { type: Function, optional: true },
    additionalClasses: { type: Array, element: String, optional: true },
    fieldDependencies: {
        type: [Function, { type: Array, element: Object, shape: { name: String, type: String } }],
        optional: true,
    },
    supportedAttributes: supportedInfoValidation,
    supportedOptions: supportedInfoValidation,
});
```

**验证规则**:
- **组件验证**: 验证组件是否继承自Component
- **属性提取**: 可选的属性提取函数
- **额外样式**: 可选的额外CSS类
- **字段依赖**: 可选的字段依赖定义
- **支持的属性**: 支持的属性和选项验证

### 3. 节点解析

```javascript
static parseWidgetNode = function (node) {
    const name = node.getAttribute("name");
    const widget = viewWidgetRegistry.get(name);
    const widgetInfo = {
        name,
        widget,
        options: {},
        attrs: {},
    };

    for (const { name, value } of node.attributes) {
        if (["name", "widget"].includes(name)) {
            continue;
        }
        if (name === "options") {
            widgetInfo.options = evaluateExpr(value);
        } else if (!name.startsWith("t-att")) {
            widgetInfo.attrs[name] = value;
        }
    }

    return widgetInfo;
};
```

**解析功能**:
- **名称提取**: 提取组件名称
- **组件获取**: 从注册表获取组件
- **属性解析**: 解析节点属性
- **选项处理**: 处理组件选项
- **动态属性**: 过滤动态属性

### 4. 样式管理

```javascript
get classNames() {
    const classNames = {
        o_widget: true,
        [`o_widget_${this.props.name}`]: true,
        [this.props.className]: Boolean(this.props.className),
    };
    if (this.widget.additionalClasses) {
        for (const cls of this.widget.additionalClasses) {
            classNames[cls] = true;
        }
    }
    return classNames;
}
```

**样式功能**:
- **基础样式**: 添加基础组件样式
- **名称样式**: 基于组件名称的样式
- **自定义样式**: 支持自定义样式类
- **额外样式**: 支持组件定义的额外样式

### 5. 属性处理

```javascript
get widgetProps() {
    const record = this.props.record;

    let readonlyFromModifiers = false;
    let propsFromNode = {};
    if (this.props.widgetInfo) {
        const widgetInfo = this.props.widgetInfo;
        readonlyFromModifiers = evaluateBooleanExpr(
            widgetInfo.attrs.readonly,
            record.evalContextWithVirtualIds
        );
        const dynamicInfo = {
            readonly: readonlyFromModifiers,
        };
        propsFromNode = this.widget.extractProps
            ? this.widget.extractProps(widgetInfo, dynamicInfo)
            : {};
    }

    return {
        record,
        readonly: !record.isInEdition || readonlyFromModifiers || false,
        ...propsFromNode,
    };
}
```

**属性处理功能**:
- **记录传递**: 传递记录对象
- **只读计算**: 计算只读状态
- **动态属性**: 处理动态属性
- **属性提取**: 使用组件的属性提取函数
- **属性合并**: 合并所有属性

## 使用场景

### 1. 组件基类管理器

```javascript
// 组件基类管理器
class WidgetManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置组件配置
        this.widgetConfig = {
            enableValidation: true,
            enableCaching: true,
            enableLazyLoading: false,
            enableErrorBoundary: true,
            enablePerformanceMonitoring: false,
            defaultTimeout: 5000,
            maxRetries: 3
        };
        
        // 设置组件类型
        this.widgetTypes = new Map([
            ['form', {
                name: 'Form Widget',
                category: 'input',
                supportedFields: ['char', 'text', 'integer', 'float'],
                defaultProps: { readonly: false }
            }],
            ['display', {
                name: 'Display Widget',
                category: 'output',
                supportedFields: ['char', 'text', 'html'],
                defaultProps: { readonly: true }
            }],
            ['action', {
                name: 'Action Widget',
                category: 'interactive',
                supportedFields: [],
                defaultProps: { clickable: true }
            }],
            ['container', {
                name: 'Container Widget',
                category: 'layout',
                supportedFields: [],
                defaultProps: { collapsible: false }
            }]
        ]);
        
        // 设置属性映射
        this.attributeMapping = new Map([
            ['readonly', { type: 'boolean', default: false }],
            ['required', { type: 'boolean', default: false }],
            ['invisible', { type: 'boolean', default: false }],
            ['string', { type: 'string', default: '' }],
            ['help', { type: 'string', default: '' }],
            ['placeholder', { type: 'string', default: '' }],
            ['class', { type: 'string', default: '' }],
            ['style', { type: 'object', default: {} }]
        ]);
        
        // 设置组件统计
        this.widgetStatistics = {
            totalWidgets: 0,
            widgetsByType: new Map(),
            widgetsByCategory: new Map(),
            renderCount: 0,
            errorCount: 0,
            averageRenderTime: 0,
            totalRenderTime: 0
        };
        
        this.initializeWidgetSystem();
    }
    
    // 初始化组件系统
    initializeWidgetSystem() {
        // 创建增强的组件基类
        this.createEnhancedWidget();
        
        // 设置注册系统
        this.setupRegistrySystem();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置缓存系统
        this.setupCacheSystem();
    }
    
    // 创建增强的组件基类
    createEnhancedWidget() {
        const originalWidget = Widget;
        
        this.EnhancedWidget = class extends originalWidget {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加性能监控
                this.addPerformanceMonitoring();
                
                // 添加错误处理
                this.addErrorHandling();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isLoading: false,
                    hasError: false,
                    errorMessage: '',
                    renderTime: 0,
                    lastRenderTime: null,
                    renderCount: 0
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的节点解析
                this.enhancedParseWidgetNode = (node) => {
                    try {
                        const startTime = performance.now();
                        
                        // 执行原始解析
                        const widgetInfo = this.constructor.parseWidgetNode(node);
                        
                        // 添加增强信息
                        widgetInfo.metadata = {
                            parseTime: performance.now() - startTime,
                            nodeType: node.nodeName,
                            attributeCount: node.attributes.length,
                            hasChildren: node.children.length > 0
                        };
                        
                        return widgetInfo;
                        
                    } catch (error) {
                        this.handleParseError(error, node);
                        return null;
                    }
                };
                
                // 增强的样式管理
                this.enhancedClassNames = () => {
                    const baseClasses = this.classNames;
                    const enhancedClasses = {
                        ...baseClasses,
                        'o_widget_enhanced': true,
                        'o_widget_loading': this.enhancedState.isLoading,
                        'o_widget_error': this.enhancedState.hasError
                    };
                    
                    // 添加类型特定样式
                    const widgetType = this.getWidgetType();
                    if (widgetType) {
                        enhancedClasses[`o_widget_type_${widgetType.category}`] = true;
                    }
                    
                    return enhancedClasses;
                };
                
                // 增强的属性处理
                this.enhancedWidgetProps = () => {
                    const baseProps = this.widgetProps;
                    const enhancedProps = {
                        ...baseProps,
                        widgetId: this.generateWidgetId(),
                        metadata: this.getWidgetMetadata(),
                        performance: this.getPerformanceInfo()
                    };
                    
                    // 添加验证
                    if (this.widgetConfig.enableValidation) {
                        enhancedProps.validation = this.validateProps(enhancedProps);
                    }
                    
                    return enhancedProps;
                };
                
                // 获取组件类型
                this.getWidgetType = () => {
                    const name = this.props.name;
                    for (const [typeName, typeInfo] of this.widgetTypes.entries()) {
                        if (name.includes(typeName)) {
                            return typeInfo;
                        }
                    }
                    return null;
                };
                
                // 生成组件ID
                this.generateWidgetId = () => {
                    return `widget_${this.props.name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                };
                
                // 获取组件元数据
                this.getWidgetMetadata = () => {
                    return {
                        name: this.props.name,
                        type: this.getWidgetType()?.name || 'Unknown',
                        renderCount: this.enhancedState.renderCount,
                        lastRenderTime: this.enhancedState.lastRenderTime,
                        hasError: this.enhancedState.hasError
                    };
                };
                
                // 获取性能信息
                this.getPerformanceInfo = () => {
                    return {
                        renderTime: this.enhancedState.renderTime,
                        averageRenderTime: this.enhancedState.renderCount > 0 ? 
                            this.enhancedState.renderTime / this.enhancedState.renderCount : 0,
                        renderCount: this.enhancedState.renderCount
                    };
                };
                
                // 验证属性
                this.validateProps = (props) => {
                    const errors = [];
                    const warnings = [];
                    
                    // 检查必需属性
                    if (!props.record) {
                        errors.push('Missing required prop: record');
                    }
                    
                    // 检查属性类型
                    for (const [propName, propValue] of Object.entries(props)) {
                        const mapping = this.attributeMapping.get(propName);
                        if (mapping && !this.validatePropType(propValue, mapping.type)) {
                            warnings.push(`Invalid type for prop '${propName}': expected ${mapping.type}`);
                        }
                    }
                    
                    return {
                        valid: errors.length === 0,
                        errors: errors,
                        warnings: warnings
                    };
                };
                
                // 验证属性类型
                this.validatePropType = (value, expectedType) => {
                    switch (expectedType) {
                        case 'boolean':
                            return typeof value === 'boolean';
                        case 'string':
                            return typeof value === 'string';
                        case 'number':
                            return typeof value === 'number';
                        case 'object':
                            return typeof value === 'object' && value !== null;
                        case 'array':
                            return Array.isArray(value);
                        default:
                            return true;
                    }
                };
                
                // 处理解析错误
                this.handleParseError = (error, node) => {
                    console.error('Widget parse error:', error, node);
                    this.enhancedState.hasError = true;
                    this.enhancedState.errorMessage = error.message;
                    this.widgetStatistics.errorCount++;
                };
                
                // 记录渲染
                this.recordRender = () => {
                    const renderTime = performance.now();
                    this.enhancedState.renderCount++;
                    this.enhancedState.lastRenderTime = new Date();
                    this.enhancedState.renderTime += renderTime;
                    
                    // 更新全局统计
                    this.widgetStatistics.renderCount++;
                    this.widgetStatistics.totalRenderTime += renderTime;
                    this.updateAverageRenderTime();
                };
                
                // 更新平均渲染时间
                this.updateAverageRenderTime = () => {
                    if (this.widgetStatistics.renderCount > 0) {
                        this.widgetStatistics.averageRenderTime = 
                            this.widgetStatistics.totalRenderTime / this.widgetStatistics.renderCount;
                    }
                };
            }
            
            addPerformanceMonitoring() {
                // 性能监控功能
                this.performanceMonitor = {
                    enabled: this.widgetConfig.enablePerformanceMonitoring,
                    record: () => this.recordRender(),
                    getInfo: () => this.getPerformanceInfo()
                };
            }
            
            addErrorHandling() {
                // 错误处理功能
                this.errorHandler = {
                    enabled: this.widgetConfig.enableErrorBoundary,
                    handle: (error) => this.handleParseError(error),
                    getErrors: () => ({
                        hasError: this.enhancedState.hasError,
                        errorMessage: this.enhancedState.errorMessage
                    })
                };
            }
            
            // 重写原始方法
            get classNames() {
                return this.enhancedClassNames();
            }
            
            get widgetProps() {
                return this.enhancedWidgetProps();
            }
            
            static parseWidgetNode(node) {
                return this.prototype.enhancedParseWidgetNode(node);
            }
        };
    }
    
    // 设置注册系统
    setupRegistrySystem() {
        this.registrySystemConfig = {
            enabled: true,
            autoRegister: true,
            validateOnRegister: this.widgetConfig.enableValidation
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.widgetConfig.enableValidation,
            strictMode: false,
            warnOnInvalid: true
        };
    }
    
    // 设置缓存系统
    setupCacheSystem() {
        this.cacheSystemConfig = {
            enabled: this.widgetConfig.enableCaching,
            maxSize: 100,
            ttl: 300000 // 5分钟
        };
    }
    
    // 注册组件
    registerWidget(name, component, options = {}) {
        viewWidgetRegistry.add(name, {
            component: component,
            ...options
        });
        
        this.widgetStatistics.totalWidgets++;
        
        // 记录类型统计
        const type = options.type || 'unknown';
        const count = this.widgetStatistics.widgetsByType.get(type) || 0;
        this.widgetStatistics.widgetsByType.set(type, count + 1);
    }
    
    // 获取组件
    getWidget(name) {
        return viewWidgetRegistry.get(name);
    }
    
    // 获取组件统计
    getWidgetStatistics() {
        return {
            ...this.widgetStatistics,
            typeVariety: this.widgetStatistics.widgetsByType.size,
            categoryVariety: this.widgetStatistics.widgetsByCategory.size,
            errorRate: this.widgetStatistics.errorCount / 
                      Math.max(this.widgetStatistics.renderCount, 1)
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理组件类型
        this.widgetTypes.clear();
        
        // 清理属性映射
        this.attributeMapping.clear();
        
        // 清理统计
        this.widgetStatistics.widgetsByType.clear();
        this.widgetStatistics.widgetsByCategory.clear();
        
        // 重置统计
        this.widgetStatistics = {
            totalWidgets: 0,
            widgetsByType: new Map(),
            widgetsByCategory: new Map(),
            renderCount: 0,
            errorCount: 0,
            averageRenderTime: 0,
            totalRenderTime: 0
        };
    }
}

// 使用示例
const widgetManager = new WidgetManager();

// 注册自定义组件
widgetManager.registerWidget('custom_widget', CustomComponent, {
    type: 'form',
    extractProps: (widgetInfo, dynamicInfo) => ({
        customProp: widgetInfo.attrs.customProp
    }),
    additionalClasses: ['custom-widget-class']
});

// 获取统计信息
const stats = widgetManager.getWidgetStatistics();
console.log('Widget statistics:', stats);
```

## 技术特点

### 1. 动态渲染
- **组件注册**: 基于注册表的组件管理
- **动态加载**: 动态加载和渲染组件
- **属性透传**: 灵活的属性透传机制
- **模板包装**: 提供统一的包装模板

### 2. 节点解析
- **XML解析**: 解析XML节点属性
- **表达式求值**: 支持Python表达式求值
- **属性分类**: 区分静态和动态属性
- **选项处理**: 处理组件选项配置

### 3. 样式管理
- **基础样式**: 提供基础组件样式
- **动态样式**: 支持动态样式计算
- **额外样式**: 支持组件定义的额外样式
- **条件样式**: 支持条件样式应用

### 4. 属性处理
- **记录传递**: 传递记录对象给子组件
- **只读计算**: 智能计算只读状态
- **属性提取**: 支持自定义属性提取
- **属性合并**: 合并多种来源的属性

## 设计模式

### 1. 包装器模式 (Wrapper Pattern)
- **组件包装**: 包装其他组件
- **功能增强**: 增强组件功能
- **统一接口**: 提供统一的接口

### 2. 注册表模式 (Registry Pattern)
- **组件注册**: 注册组件到注册表
- **动态查找**: 动态查找和加载组件
- **解耦设计**: 解耦组件定义和使用

### 3. 策略模式 (Strategy Pattern)
- **属性提取**: 不同的属性提取策略
- **样式计算**: 不同的样式计算策略
- **渲染策略**: 不同的渲染策略

### 4. 模板方法模式 (Template Method Pattern)
- **渲染模板**: 定义渲染模板
- **扩展点**: 提供扩展点
- **流程控制**: 控制渲染流程

## 注意事项

1. **性能考虑**: 避免频繁的组件创建和销毁
2. **内存管理**: 及时清理组件资源
3. **错误处理**: 处理组件加载和渲染错误
4. **兼容性**: 保持向后兼容性

## 扩展建议

1. **懒加载**: 支持组件懒加载
2. **缓存机制**: 添加组件缓存机制
3. **错误边界**: 增强错误边界处理
4. **性能监控**: 添加性能监控功能
5. **热重载**: 支持开发时热重载

该组件基类为Odoo Web客户端提供了完整的组件渲染解决方案，通过灵活的注册机制和强大的属性处理确保了视图组件系统的可扩展性和可维护性。

# WeekDays - 周天选择组件

## 概述

`week_days.js` 是 Odoo Web 客户端的周天选择组件，负责提供一周七天的选择功能。该模块包含52行代码，是一个功能专门的选择组件，专门用于在表单中选择一周中的特定天数，具备国际化支持、本地化配置、复选框集成、数据绑定等特性，是时间和日程管理的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/widgets/week_days/week_days.js`
- **行数**: 52
- **模块**: `@web/views/widgets/week_days/week_days`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                           // 注册表
'@web/core/checkbox/checkbox'                  // 复选框组件
'@web/core/l10n/localization'                  // 本地化服务
'@web/core/l10n/translation'                   // 翻译服务
'@odoo/owl'                                    // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
class WeekDays extends Component {
    static template = "web.WeekDays";
    static components = { CheckBox };
    static props = {
        record: Object,
        readonly: Boolean,
    };
}
```

**组件特性**:
- **复选框集成**: 集成CheckBox组件
- **记录绑定**: 绑定到记录对象
- **只读支持**: 支持只读模式
- **简洁属性**: 最简化的属性定义

### 2. 周天常量

```javascript
const WEEKDAYS = ["sun", "mon", "tue", "wed", "thu", "fri", "sat"];
```

**常量定义**:
- **标准顺序**: 定义标准的周天顺序
- **英文缩写**: 使用英文缩写标识
- **数组索引**: 支持数组索引操作
- **固定长度**: 固定7天长度

### 3. 本地化周天

```javascript
get weekdays() {
    return [
        ...WEEKDAYS.slice(localization.weekStart % WEEKDAYS.length, WEEKDAYS.length),
        ...WEEKDAYS.slice(0, localization.weekStart % WEEKDAYS.length),
    ];
}
```

**本地化功能**:
- **周开始**: 根据本地化设置调整周开始日
- **数组重排**: 重新排列周天数组
- **循环处理**: 使用模运算处理循环
- **动态计算**: 动态计算周天顺序

### 4. 数据处理

```javascript
get data() {
    return Object.fromEntries(this.weekdays.map((day) => [day, this.props.record.data[day]]));
}

onChange(day, checked) {
    this.props.record.update({ [day]: checked });
}
```

**数据功能**:
- **数据映射**: 映射周天到记录数据
- **对象构建**: 构建周天数据对象
- **变更处理**: 处理复选框变更
- **记录更新**: 更新记录数据

### 5. 字段依赖

```javascript
fieldDependencies: [
    { name: "sun", type: "boolean", string: _t("Sun"), readonly: false },
    { name: "mon", type: "boolean", string: _t("Mon"), readonly: false },
    { name: "tue", type: "boolean", string: _t("Tue"), readonly: false },
    { name: "wed", type: "boolean", string: _t("Wed"), readonly: false },
    { name: "thu", type: "boolean", string: _t("Thu"), readonly: false },
    { name: "fri", type: "boolean", string: _t("Fri"), readonly: false },
    { name: "sat", type: "boolean", string: _t("Sat"), readonly: false },
]
```

**依赖定义**:
- **字段声明**: 声明所需的字段依赖
- **布尔类型**: 所有字段都是布尔类型
- **国际化**: 使用翻译函数国际化标签
- **可编辑**: 默认所有字段可编辑

## 使用场景

### 1. 周天选择管理器

```javascript
// 周天选择管理器
class WeekDaysManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置周天配置
        this.weekDaysConfig = {
            enableCustomWeekStart: true,
            enableWorkingDaysOnly: false,
            enableHolidayExclusion: false,
            enableTimeZoneSupport: false,
            enableBulkSelection: true,
            enablePresets: true,
            defaultSelection: [],
            workingDays: ['mon', 'tue', 'wed', 'thu', 'fri']
        };
        
        // 设置周天名称映射
        this.weekDayNames = new Map([
            ['sun', {
                short: 'Sun',
                long: 'Sunday',
                index: 0,
                isWeekend: true,
                isWorkingDay: false
            }],
            ['mon', {
                short: 'Mon',
                long: 'Monday',
                index: 1,
                isWeekend: false,
                isWorkingDay: true
            }],
            ['tue', {
                short: 'Tue',
                long: 'Tuesday',
                index: 2,
                isWeekend: false,
                isWorkingDay: true
            }],
            ['wed', {
                short: 'Wed',
                long: 'Wednesday',
                index: 3,
                isWeekend: false,
                isWorkingDay: true
            }],
            ['thu', {
                short: 'Thu',
                long: 'Thursday',
                index: 4,
                isWeekend: false,
                isWorkingDay: true
            }],
            ['fri', {
                short: 'Fri',
                long: 'Friday',
                index: 5,
                isWeekend: false,
                isWorkingDay: true
            }],
            ['sat', {
                short: 'Sat',
                long: 'Saturday',
                index: 6,
                isWeekend: true,
                isWorkingDay: false
            }]
        ]);
        
        // 设置预设选择
        this.weekDayPresets = new Map([
            ['all', {
                name: 'All Days',
                days: ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'],
                description: 'Select all days of the week'
            }],
            ['weekdays', {
                name: 'Weekdays',
                days: ['mon', 'tue', 'wed', 'thu', 'fri'],
                description: 'Select weekdays only'
            }],
            ['weekend', {
                name: 'Weekend',
                days: ['sat', 'sun'],
                description: 'Select weekend days only'
            }],
            ['business', {
                name: 'Business Days',
                days: ['mon', 'tue', 'wed', 'thu', 'fri'],
                description: 'Select business days'
            }],
            ['none', {
                name: 'None',
                days: [],
                description: 'Clear all selections'
            }]
        ]);
        
        // 设置周天统计
        this.weekDaysStatistics = {
            totalSelections: 0,
            selectionsByDay: new Map(),
            selectionsByPreset: new Map(),
            averageSelections: 0,
            mostSelectedDay: null,
            leastSelectedDay: null,
            selectionPatterns: new Map()
        };
        
        this.initializeWeekDaysSystem();
    }
    
    // 初始化周天系统
    initializeWeekDaysSystem() {
        // 创建增强的周天组件
        this.createEnhancedWeekDays();
        
        // 设置本地化系统
        this.setupLocalizationSystem();
        
        // 设置预设系统
        this.setupPresetSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
    }
    
    // 创建增强的周天组件
    createEnhancedWeekDays() {
        const originalComponent = WeekDays;
        
        this.EnhancedWeekDays = class extends originalComponent {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加预设功能
                this.addPresetFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    selectedDays: new Set(),
                    lastChangedDay: null,
                    changeCount: 0,
                    selectionPattern: '',
                    isValidSelection: true,
                    customWeekStart: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
                
                // 初始化状态
                this.initializeWeekDaysState();
            }
            
            addEnhancedMethods() {
                // 增强的周天获取
                this.enhancedWeekdays = () => {
                    const weekStart = this.enhancedState.customWeekStart !== null ? 
                        this.enhancedState.customWeekStart : 
                        localization.weekStart;
                    
                    return [
                        ...WEEKDAYS.slice(weekStart % WEEKDAYS.length, WEEKDAYS.length),
                        ...WEEKDAYS.slice(0, weekStart % WEEKDAYS.length),
                    ];
                };
                
                // 增强的数据获取
                this.enhancedData = () => {
                    const weekdays = this.enhancedWeekdays();
                    const data = {};
                    
                    for (const day of weekdays) {
                        data[day] = this.props.record.data[day] || false;
                        if (data[day]) {
                            this.enhancedState.selectedDays.add(day);
                        }
                    }
                    
                    return data;
                };
                
                // 增强的变更处理
                this.enhancedOnChange = (day, checked) => {
                    try {
                        // 验证变更
                        if (!this.validateDayChange(day, checked)) {
                            return;
                        }
                        
                        // 更新状态
                        this.updateDaySelection(day, checked);
                        
                        // 执行原始更新
                        this.props.record.update({ [day]: checked });
                        
                        // 记录变更
                        this.recordDayChange(day, checked);
                        
                        // 更新选择模式
                        this.updateSelectionPattern();
                        
                    } catch (error) {
                        this.handleChangeError(error);
                    }
                };
                
                // 验证天数变更
                this.validateDayChange = (day, checked) => {
                    // 检查是否为有效天数
                    if (!WEEKDAYS.includes(day)) {
                        return false;
                    }
                    
                    // 检查只读状态
                    if (this.props.readonly) {
                        return false;
                    }
                    
                    // 自定义验证逻辑
                    if (this.weekDaysConfig.enableWorkingDaysOnly && checked) {
                        const dayInfo = this.weekDayNames.get(day);
                        if (dayInfo && !dayInfo.isWorkingDay) {
                            this.showWorkingDayWarning();
                            return false;
                        }
                    }
                    
                    return true;
                };
                
                // 更新天数选择
                this.updateDaySelection = (day, checked) => {
                    if (checked) {
                        this.enhancedState.selectedDays.add(day);
                    } else {
                        this.enhancedState.selectedDays.delete(day);
                    }
                    
                    this.enhancedState.lastChangedDay = day;
                    this.enhancedState.changeCount++;
                };
                
                // 记录天数变更
                this.recordDayChange = (day, checked) => {
                    // 记录天数统计
                    const count = this.weekDaysStatistics.selectionsByDay.get(day) || 0;
                    if (checked) {
                        this.weekDaysStatistics.selectionsByDay.set(day, count + 1);
                    }
                    
                    this.weekDaysStatistics.totalSelections++;
                    this.updateDayStatistics();
                };
                
                // 更新选择模式
                this.updateSelectionPattern = () => {
                    const selectedDays = Array.from(this.enhancedState.selectedDays).sort();
                    this.enhancedState.selectionPattern = selectedDays.join(',');
                    
                    // 记录模式统计
                    const pattern = this.enhancedState.selectionPattern;
                    const count = this.weekDaysStatistics.selectionPatterns.get(pattern) || 0;
                    this.weekDaysStatistics.selectionPatterns.set(pattern, count + 1);
                };
                
                // 应用预设
                this.applyPreset = (presetName) => {
                    const preset = this.weekDayPresets.get(presetName);
                    if (!preset) {
                        return;
                    }
                    
                    // 清除当前选择
                    this.clearAllSelections();
                    
                    // 应用预设选择
                    const updates = {};
                    for (const day of preset.days) {
                        updates[day] = true;
                        this.enhancedState.selectedDays.add(day);
                    }
                    
                    // 更新记录
                    this.props.record.update(updates);
                    
                    // 记录预设使用
                    this.recordPresetUsage(presetName);
                    
                    // 更新选择模式
                    this.updateSelectionPattern();
                };
                
                // 清除所有选择
                this.clearAllSelections = () => {
                    const updates = {};
                    for (const day of WEEKDAYS) {
                        updates[day] = false;
                    }
                    
                    this.props.record.update(updates);
                    this.enhancedState.selectedDays.clear();
                    this.updateSelectionPattern();
                };
                
                // 切换所有选择
                this.toggleAllSelections = () => {
                    const allSelected = this.enhancedState.selectedDays.size === WEEKDAYS.length;
                    
                    if (allSelected) {
                        this.clearAllSelections();
                    } else {
                        this.applyPreset('all');
                    }
                };
                
                // 获取选择信息
                this.getSelectionInfo = () => {
                    return {
                        selectedDays: Array.from(this.enhancedState.selectedDays),
                        selectedCount: this.enhancedState.selectedDays.size,
                        totalDays: WEEKDAYS.length,
                        selectionPattern: this.enhancedState.selectionPattern,
                        lastChangedDay: this.enhancedState.lastChangedDay,
                        changeCount: this.enhancedState.changeCount,
                        isValidSelection: this.enhancedState.isValidSelection,
                        weekStart: this.enhancedState.customWeekStart || localization.weekStart
                    };
                };
                
                // 获取天数信息
                this.getDayInfo = (day) => {
                    const dayInfo = this.weekDayNames.get(day);
                    const isSelected = this.enhancedState.selectedDays.has(day);
                    
                    return {
                        ...dayInfo,
                        day: day,
                        isSelected: isSelected,
                        canSelect: !this.props.readonly
                    };
                };
                
                // 设置自定义周开始
                this.setCustomWeekStart = (weekStart) => {
                    this.enhancedState.customWeekStart = weekStart;
                };
                
                // 初始化周天状态
                this.initializeWeekDaysState = () => {
                    // 初始化选中的天数
                    for (const day of WEEKDAYS) {
                        if (this.props.record.data[day]) {
                            this.enhancedState.selectedDays.add(day);
                        }
                    }
                    
                    // 更新选择模式
                    this.updateSelectionPattern();
                };
                
                // 记录预设使用
                this.recordPresetUsage = (presetName) => {
                    const count = this.weekDaysStatistics.selectionsByPreset.get(presetName) || 0;
                    this.weekDaysStatistics.selectionsByPreset.set(presetName, count + 1);
                };
                
                // 更新天数统计
                this.updateDayStatistics = () => {
                    // 更新平均选择数
                    if (this.weekDaysStatistics.totalSelections > 0) {
                        const totalSelected = Array.from(this.weekDaysStatistics.selectionsByDay.values())
                            .reduce((sum, count) => sum + count, 0);
                        this.weekDaysStatistics.averageSelections = 
                            totalSelected / this.weekDaysStatistics.totalSelections;
                    }
                    
                    // 更新最多/最少选择的天数
                    this.updateMostLeastSelectedDays();
                };
                
                // 更新最多/最少选择的天数
                this.updateMostLeastSelectedDays = () => {
                    let maxCount = 0;
                    let minCount = Infinity;
                    let mostSelected = null;
                    let leastSelected = null;
                    
                    for (const [day, count] of this.weekDaysStatistics.selectionsByDay.entries()) {
                        if (count > maxCount) {
                            maxCount = count;
                            mostSelected = day;
                        }
                        if (count < minCount) {
                            minCount = count;
                            leastSelected = day;
                        }
                    }
                    
                    this.weekDaysStatistics.mostSelectedDay = mostSelected;
                    this.weekDaysStatistics.leastSelectedDay = leastSelected;
                };
                
                // 显示工作日警告
                this.showWorkingDayWarning = () => {
                    const notification = useService("notification");
                    notification.add(
                        "Only working days can be selected",
                        { type: 'warning' }
                    );
                };
                
                // 处理变更错误
                this.handleChangeError = (error) => {
                    console.error('Week days change error:', error);
                    this.enhancedState.isValidSelection = false;
                };
            }
            
            addPresetFeatures() {
                // 预设功能
                this.presetManager = {
                    enabled: this.weekDaysConfig.enablePresets,
                    presets: this.weekDayPresets,
                    apply: (presetName) => this.applyPreset(presetName),
                    clear: () => this.clearAllSelections(),
                    toggleAll: () => this.toggleAllSelections()
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: true,
                    validate: (day, checked) => this.validateDayChange(day, checked),
                    isValid: () => this.enhancedState.isValidSelection
                };
            }
            
            // 重写原始方法
            get weekdays() {
                return this.enhancedWeekdays();
            }
            
            get data() {
                return this.enhancedData();
            }
            
            onChange(day, checked) {
                this.enhancedOnChange(day, checked);
            }
        };
    }
    
    // 设置本地化系统
    setupLocalizationSystem() {
        this.localizationSystemConfig = {
            enabled: this.weekDaysConfig.enableCustomWeekStart,
            defaultWeekStart: localization.weekStart,
            supportedLocales: ['en', 'fr', 'de', 'es', 'it']
        };
    }
    
    // 设置预设系统
    setupPresetSystem() {
        this.presetSystemConfig = {
            enabled: this.weekDaysConfig.enablePresets,
            presets: this.weekDayPresets,
            allowCustomPresets: true
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: true,
            workingDaysOnly: this.weekDaysConfig.enableWorkingDaysOnly,
            excludeHolidays: this.weekDaysConfig.enableHolidayExclusion
        };
    }
    
    // 创建周天组件
    createWeekDays(props) {
        return new this.EnhancedWeekDays(props);
    }
    
    // 注册自定义预设
    registerPreset(name, config) {
        this.weekDayPresets.set(name, config);
    }
    
    // 获取周天统计
    getWeekDaysStatistics() {
        return {
            ...this.weekDaysStatistics,
            dayVariety: this.weekDaysStatistics.selectionsByDay.size,
            presetVariety: this.weekDaysStatistics.selectionsByPreset.size,
            patternVariety: this.weekDaysStatistics.selectionPatterns.size,
            averageSelections: this.weekDaysStatistics.averageSelections
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理周天名称
        this.weekDayNames.clear();
        
        // 清理预设
        this.weekDayPresets.clear();
        
        // 清理统计
        this.weekDaysStatistics.selectionsByDay.clear();
        this.weekDaysStatistics.selectionsByPreset.clear();
        this.weekDaysStatistics.selectionPatterns.clear();
        
        // 重置统计
        this.weekDaysStatistics = {
            totalSelections: 0,
            selectionsByDay: new Map(),
            selectionsByPreset: new Map(),
            averageSelections: 0,
            mostSelectedDay: null,
            leastSelectedDay: null,
            selectionPatterns: new Map()
        };
    }
}

// 使用示例
const weekDaysManager = new WeekDaysManager();

// 创建周天组件
const weekDays = weekDaysManager.createWeekDays({
    record: recordObject,
    readonly: false
});

// 注册自定义预设
weekDaysManager.registerPreset('custom_schedule', {
    name: 'Custom Schedule',
    days: ['mon', 'wed', 'fri'],
    description: 'Monday, Wednesday, Friday schedule'
});

// 获取统计信息
const stats = weekDaysManager.getWeekDaysStatistics();
console.log('Week days statistics:', stats);
```

## 技术特点

### 1. 国际化支持
- **本地化**: 支持本地化周开始设置
- **翻译**: 使用翻译函数国际化标签
- **动态排序**: 根据本地化动态排序周天
- **文化适配**: 适配不同文化的周天习惯

### 2. 数据绑定
- **记录集成**: 深度集成记录对象
- **实时更新**: 实时更新记录数据
- **双向绑定**: 支持双向数据绑定
- **状态同步**: 保持状态同步

### 3. 用户体验
- **复选框**: 使用标准复选框组件
- **直观显示**: 直观的周天显示
- **即时反馈**: 即时的选择反馈
- **只读支持**: 支持只读模式

### 4. 字段依赖
- **自动声明**: 自动声明字段依赖
- **类型定义**: 明确的字段类型定义
- **权限控制**: 支持字段权限控制
- **元数据**: 提供完整的字段元数据

## 设计模式

### 1. 组合模式 (Composition Pattern)
- **组件组合**: 组合复选框组件
- **功能组合**: 组合周天选择功能
- **数据组合**: 组合周天数据

### 2. 策略模式 (Strategy Pattern)
- **排序策略**: 不同的周天排序策略
- **显示策略**: 不同的显示策略
- **验证策略**: 不同的验证策略

### 3. 观察者模式 (Observer Pattern)
- **数据观察**: 观察数据变化
- **状态观察**: 观察选择状态变化
- **事件观察**: 观察用户交互事件

### 4. 工厂模式 (Factory Pattern)
- **组件工厂**: 创建周天组件
- **数据工厂**: 创建周天数据对象
- **配置工厂**: 创建配置对象

## 注意事项

1. **本地化**: 注意不同地区的周开始设置
2. **数据同步**: 确保数据同步的准确性
3. **用户体验**: 提供清晰的选择界面
4. **性能考虑**: 避免频繁的数据更新

## 扩展建议

1. **时区支持**: 添加时区支持功能
2. **假日排除**: 支持假日排除功能
3. **批量操作**: 支持批量选择操作
4. **预设管理**: 增强预设管理功能
5. **可视化**: 添加可视化日历显示

该周天选择组件为Odoo Web客户端提供了简洁高效的周天选择功能，通过国际化支持和灵活的数据绑定确保了时间管理的便利性和准确性。

# AttachDocument - 附件文档组件

## 概述

`attach_document.js` 是 Odoo Web 客户端的附件文档组件，负责处理文件上传和附件管理功能。该模块包含94行代码，是一个功能完整的文件上传组件，专门用于在表单中添加文件附件，具备文件选择、上传处理、大小验证、错误处理等特性，是文档管理和文件处理的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/widgets/attach_document/attach_document.js`
- **行数**: 94
- **模块**: `@web/views/widgets/attach_document/attach_document`

## 依赖关系

```javascript
// 核心依赖
'@web/core/file_input/file_input'              // 文件输入组件
'@web/core/registry'                           // 注册表
'@web/core/utils/hooks'                        // 工具钩子
'@web/core/utils/files'                        // 文件工具
'@web/views/widgets/standard_widget_props'     // 标准组件属性
'@odoo/owl'                                    // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
class AttachDocumentWidget extends Component {
    static template = "web.AttachDocument";
    static components = {
        FileInput,
    };
    static props = {
        ...standardWidgetProps,
        string: { type: String },
        action: { type: String, optional: true },
        highlight: { type: Boolean },
    };
}
```

**组件特性**:
- **文件输入**: 集成FileInput组件
- **标准属性**: 继承标准组件属性
- **字符串标签**: 支持自定义标签文本
- **动作配置**: 可选的上传后动作
- **高亮显示**: 支持高亮显示模式

### 2. 组件初始化

```javascript
setup() {
    this.http = useService("http");
    this.notification = useService("notification");
    this.fileInput = document.createElement("input");
    this.fileInput.type = "file";
    this.fileInput.accept = "*";
    this.fileInput.multiple = true;
    this.fileInput.onchange = this.onInputChange.bind(this);
}
```

**初始化功能**:
- **服务注入**: 注入HTTP和通知服务
- **文件输入**: 创建文件输入元素
- **多文件**: 支持多文件选择
- **事件绑定**: 绑定文件变化事件
- **文件类型**: 接受所有文件类型

### 3. 文件上传处理

```javascript
async onInputChange() {
    const ufile = [...this.fileInput.files];
    for (const file of ufile) {
        if (!checkFileSize(file.size, this.notification)) {
            return null;
        }
    }
    const fileData = await this.http.post(
        "/web/binary/upload_attachment",
        {
            csrf_token: odoo.csrf_token,
            ufile: ufile,
            model: this.props.record.resModel,
            id: this.props.record.resId,
        },
        "text"
    );
    const parsedFileData = JSON.parse(fileData);
    if (parsedFileData.error) {
        throw new Error(parsedFileData.error);
    }
    await this.onFileUploaded(parsedFileData);
}
```

**上传处理功能**:
- **文件获取**: 获取选中的文件列表
- **大小验证**: 验证文件大小限制
- **HTTP上传**: 通过HTTP POST上传文件
- **CSRF保护**: 包含CSRF令牌
- **错误处理**: 处理上传错误
- **结果解析**: 解析上传结果

### 4. 上传触发

```javascript
async triggerUpload() {
    if (await this.beforeOpen()) {
        this.fileInput.click();
    }
}

beforeOpen() {
    return this.props.record.save();
}
```

**触发功能**:
- **预保存**: 上传前保存记录
- **文件选择**: 触发文件选择对话框
- **条件检查**: 检查上传前条件
- **异步处理**: 支持异步操作

### 5. 上传后处理

```javascript
async onFileUploaded(files) {
    const { action, record } = this.props;
    if (action) {
        const { resId, resModel } = record;
        await this.env.services.orm.call(resModel, action, [resId], {
            attachment_ids: files.map((file) => file.id),
        });
        await record.load();
    }
}
```

**后处理功能**:
- **动作执行**: 执行配置的后续动作
- **ORM调用**: 调用模型方法
- **附件ID**: 传递附件ID列表
- **记录重载**: 重新加载记录数据

### 6. 属性提取

```javascript
const attachDocumentWidget = {
    component: AttachDocumentWidget,
    extractProps: ({ attrs }) => {
        const { action, highlight, string } = attrs;
        return {
            action,
            highlight: !!highlight,
            string,
        };
    },
};
```

**属性提取功能**:
- **动作提取**: 提取动作配置
- **高亮转换**: 转换高亮属性为布尔值
- **标签提取**: 提取标签文本
- **属性清理**: 清理和转换属性

## 使用场景

### 1. 附件文档管理器

```javascript
// 附件文档管理器
class AttachDocumentManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置附件配置
        this.attachmentConfig = {
            enableMultipleFiles: true,
            enableFileValidation: true,
            enableProgressTracking: false,
            enablePreview: false,
            enableDragDrop: false,
            maxFileSize: 25 * 1024 * 1024, // 25MB
            maxFiles: 10,
            allowedTypes: ['*']
        };
        
        // 设置文件类型
        this.fileTypes = new Map([
            ['image', {
                name: 'Image Files',
                extensions: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg'],
                mimeTypes: ['image/*'],
                maxSize: 10 * 1024 * 1024, // 10MB
                preview: true
            }],
            ['document', {
                name: 'Document Files',
                extensions: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'],
                mimeTypes: ['application/pdf', 'application/msword'],
                maxSize: 25 * 1024 * 1024, // 25MB
                preview: false
            }],
            ['text', {
                name: 'Text Files',
                extensions: ['.txt', '.csv', '.xml', '.json'],
                mimeTypes: ['text/*'],
                maxSize: 5 * 1024 * 1024, // 5MB
                preview: true
            }],
            ['archive', {
                name: 'Archive Files',
                extensions: ['.zip', '.rar', '.7z', '.tar', '.gz'],
                mimeTypes: ['application/zip', 'application/x-rar'],
                maxSize: 50 * 1024 * 1024, // 50MB
                preview: false
            }]
        ]);
        
        // 设置上传状态
        this.uploadStates = new Map([
            ['pending', { name: 'Pending', color: 'gray', icon: 'fa-clock' }],
            ['uploading', { name: 'Uploading', color: 'blue', icon: 'fa-upload' }],
            ['processing', { name: 'Processing', color: 'orange', icon: 'fa-cog' }],
            ['completed', { name: 'Completed', color: 'green', icon: 'fa-check' }],
            ['failed', { name: 'Failed', color: 'red', icon: 'fa-times' }],
            ['cancelled', { name: 'Cancelled', color: 'gray', icon: 'fa-ban' }]
        ]);
        
        // 设置附件统计
        this.attachmentStatistics = {
            totalUploads: 0,
            uploadsByType: new Map(),
            uploadsBySize: new Map(),
            successfulUploads: 0,
            failedUploads: 0,
            totalSize: 0,
            averageSize: 0,
            largestFile: null,
            smallestFile: null
        };
        
        this.initializeAttachmentSystem();
    }
    
    // 初始化附件系统
    initializeAttachmentSystem() {
        // 创建增强的附件组件
        this.createEnhancedAttachDocument();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置上传系统
        this.setupUploadSystem();
        
        // 设置处理系统
        this.setupProcessingSystem();
    }
    
    // 创建增强的附件组件
    createEnhancedAttachDocument() {
        const originalComponent = AttachDocumentWidget;
        
        this.EnhancedAttachDocumentWidget = class extends originalComponent {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
                
                // 添加进度功能
                this.addProgressFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isUploading: false,
                    uploadProgress: 0,
                    selectedFiles: [],
                    uploadedFiles: [],
                    failedFiles: [],
                    totalSize: 0,
                    uploadedSize: 0
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的文件变化处理
                this.enhancedOnInputChange = async () => {
                    try {
                        const files = [...this.fileInput.files];
                        
                        // 验证文件
                        const validation = this.validateFiles(files);
                        if (!validation.valid) {
                            this.showValidationErrors(validation.errors);
                            return;
                        }
                        
                        // 更新状态
                        this.enhancedState.selectedFiles = files;
                        this.enhancedState.totalSize = files.reduce((sum, file) => sum + file.size, 0);
                        this.enhancedState.isUploading = true;
                        
                        // 执行上传
                        await this.uploadFiles(files);
                        
                    } catch (error) {
                        this.handleUploadError(error);
                    } finally {
                        this.enhancedState.isUploading = false;
                    }
                };
                
                // 验证文件
                this.validateFiles = (files) => {
                    const errors = [];
                    
                    // 检查文件数量
                    if (files.length > this.attachmentConfig.maxFiles) {
                        errors.push(`Maximum ${this.attachmentConfig.maxFiles} files allowed`);
                    }
                    
                    // 检查每个文件
                    for (const file of files) {
                        // 检查文件大小
                        if (file.size > this.attachmentConfig.maxFileSize) {
                            errors.push(`File '${file.name}' exceeds maximum size`);
                        }
                        
                        // 检查文件类型
                        if (!this.isFileTypeAllowed(file)) {
                            errors.push(`File type '${file.type}' is not allowed`);
                        }
                    }
                    
                    return {
                        valid: errors.length === 0,
                        errors: errors
                    };
                };
                
                // 检查文件类型是否允许
                this.isFileTypeAllowed = (file) => {
                    const allowedTypes = this.attachmentConfig.allowedTypes;
                    
                    if (allowedTypes.includes('*')) {
                        return true;
                    }
                    
                    for (const [typeName, typeInfo] of this.fileTypes.entries()) {
                        if (allowedTypes.includes(typeName)) {
                            // 检查扩展名
                            const extension = '.' + file.name.split('.').pop().toLowerCase();
                            if (typeInfo.extensions.includes(extension)) {
                                return true;
                            }
                            
                            // 检查MIME类型
                            for (const mimePattern of typeInfo.mimeTypes) {
                                if (mimePattern.endsWith('*')) {
                                    const prefix = mimePattern.slice(0, -1);
                                    if (file.type.startsWith(prefix)) {
                                        return true;
                                    }
                                } else if (file.type === mimePattern) {
                                    return true;
                                }
                            }
                        }
                    }
                    
                    return false;
                };
                
                // 上传文件
                this.uploadFiles = async (files) => {
                    const uploadedFiles = [];
                    const failedFiles = [];
                    
                    for (let i = 0; i < files.length; i++) {
                        const file = files[i];
                        
                        try {
                            // 更新进度
                            this.enhancedState.uploadProgress = (i / files.length) * 100;
                            
                            // 上传单个文件
                            const result = await this.uploadSingleFile(file);
                            uploadedFiles.push(result);
                            
                            // 记录成功
                            this.recordUploadSuccess(file);
                            
                        } catch (error) {
                            failedFiles.push({ file, error });
                            this.recordUploadFailure(file, error);
                        }
                    }
                    
                    // 更新状态
                    this.enhancedState.uploadedFiles = uploadedFiles;
                    this.enhancedState.failedFiles = failedFiles;
                    this.enhancedState.uploadProgress = 100;
                    
                    // 处理结果
                    if (uploadedFiles.length > 0) {
                        await this.onFileUploaded(uploadedFiles);
                    }
                    
                    if (failedFiles.length > 0) {
                        this.showUploadErrors(failedFiles);
                    }
                };
                
                // 上传单个文件
                this.uploadSingleFile = async (file) => {
                    const formData = new FormData();
                    formData.append('csrf_token', odoo.csrf_token);
                    formData.append('ufile', file);
                    formData.append('model', this.props.record.resModel);
                    formData.append('id', this.props.record.resId);
                    
                    const response = await this.http.post(
                        "/web/binary/upload_attachment",
                        formData,
                        "text"
                    );
                    
                    const result = JSON.parse(response);
                    if (result.error) {
                        throw new Error(result.error);
                    }
                    
                    return result[0]; // 返回第一个文件信息
                };
                
                // 显示验证错误
                this.showValidationErrors = (errors) => {
                    this.notification.add(
                        errors.join('\n'),
                        { type: 'danger', title: 'File Validation Failed' }
                    );
                };
                
                // 显示上传错误
                this.showUploadErrors = (failedFiles) => {
                    const errors = failedFiles.map(({ file, error }) => 
                        `${file.name}: ${error.message}`
                    );
                    
                    this.notification.add(
                        errors.join('\n'),
                        { type: 'danger', title: 'Upload Failed' }
                    );
                };
                
                // 获取文件信息
                this.getFileInfo = (file) => {
                    return {
                        name: file.name,
                        size: file.size,
                        type: file.type,
                        lastModified: file.lastModified,
                        extension: '.' + file.name.split('.').pop().toLowerCase(),
                        sizeFormatted: this.formatFileSize(file.size),
                        typeCategory: this.getFileTypeCategory(file)
                    };
                };
                
                // 格式化文件大小
                this.formatFileSize = (bytes) => {
                    if (bytes === 0) return '0 Bytes';
                    
                    const k = 1024;
                    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                };
                
                // 获取文件类型分类
                this.getFileTypeCategory = (file) => {
                    for (const [typeName, typeInfo] of this.fileTypes.entries()) {
                        const extension = '.' + file.name.split('.').pop().toLowerCase();
                        if (typeInfo.extensions.includes(extension)) {
                            return typeName;
                        }
                    }
                    return 'unknown';
                };
                
                // 记录上传成功
                this.recordUploadSuccess = (file) => {
                    this.attachmentStatistics.totalUploads++;
                    this.attachmentStatistics.successfulUploads++;
                    this.attachmentStatistics.totalSize += file.size;
                    
                    // 记录类型统计
                    const category = this.getFileTypeCategory(file);
                    const count = this.attachmentStatistics.uploadsByType.get(category) || 0;
                    this.attachmentStatistics.uploadsByType.set(category, count + 1);
                    
                    // 更新大小统计
                    this.updateSizeStatistics(file);
                };
                
                // 记录上传失败
                this.recordUploadFailure = (file, error) => {
                    this.attachmentStatistics.totalUploads++;
                    this.attachmentStatistics.failedUploads++;
                    console.error('Upload failed:', file.name, error);
                };
                
                // 更新大小统计
                this.updateSizeStatistics = (file) => {
                    if (this.attachmentStatistics.successfulUploads > 0) {
                        this.attachmentStatistics.averageSize = 
                            this.attachmentStatistics.totalSize / this.attachmentStatistics.successfulUploads;
                    }
                    
                    if (!this.attachmentStatistics.largestFile || 
                        file.size > this.attachmentStatistics.largestFile.size) {
                        this.attachmentStatistics.largestFile = file;
                    }
                    
                    if (!this.attachmentStatistics.smallestFile || 
                        file.size < this.attachmentStatistics.smallestFile.size) {
                        this.attachmentStatistics.smallestFile = file;
                    }
                };
                
                // 处理上传错误
                this.handleUploadError = (error) => {
                    console.error('Upload error:', error);
                    this.notification.add(
                        error.message || 'Upload failed',
                        { type: 'danger' }
                    );
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.attachmentConfig.enableFileValidation,
                    validate: (files) => this.validateFiles(files),
                    isTypeAllowed: (file) => this.isFileTypeAllowed(file)
                };
            }
            
            addProgressFeatures() {
                // 进度功能
                this.progressManager = {
                    enabled: this.attachmentConfig.enableProgressTracking,
                    getProgress: () => this.enhancedState.uploadProgress,
                    isUploading: () => this.enhancedState.isUploading
                };
            }
            
            // 重写原始方法
            async onInputChange() {
                return await this.enhancedOnInputChange();
            }
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.attachmentConfig.enableFileValidation,
            maxFileSize: this.attachmentConfig.maxFileSize,
            maxFiles: this.attachmentConfig.maxFiles,
            allowedTypes: this.attachmentConfig.allowedTypes
        };
    }
    
    // 设置上传系统
    setupUploadSystem() {
        this.uploadSystemConfig = {
            enabled: true,
            multipleFiles: this.attachmentConfig.enableMultipleFiles,
            progressTracking: this.attachmentConfig.enableProgressTracking
        };
    }
    
    // 设置处理系统
    setupProcessingSystem() {
        this.processingSystemConfig = {
            enabled: true,
            preview: this.attachmentConfig.enablePreview,
            dragDrop: this.attachmentConfig.enableDragDrop
        };
    }
    
    // 创建附件组件
    createAttachDocument(props) {
        return new this.EnhancedAttachDocumentWidget(props);
    }
    
    // 注册文件类型
    registerFileType(name, config) {
        this.fileTypes.set(name, config);
    }
    
    // 获取附件统计
    getAttachmentStatistics() {
        return {
            ...this.attachmentStatistics,
            successRate: (this.attachmentStatistics.successfulUploads / 
                         Math.max(this.attachmentStatistics.totalUploads, 1)) * 100,
            failureRate: (this.attachmentStatistics.failedUploads / 
                         Math.max(this.attachmentStatistics.totalUploads, 1)) * 100,
            typeVariety: this.attachmentStatistics.uploadsByType.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理文件类型
        this.fileTypes.clear();
        
        // 清理上传状态
        this.uploadStates.clear();
        
        // 清理统计
        this.attachmentStatistics.uploadsByType.clear();
        this.attachmentStatistics.uploadsBySize.clear();
        
        // 重置统计
        this.attachmentStatistics = {
            totalUploads: 0,
            uploadsByType: new Map(),
            uploadsBySize: new Map(),
            successfulUploads: 0,
            failedUploads: 0,
            totalSize: 0,
            averageSize: 0,
            largestFile: null,
            smallestFile: null
        };
    }
}

// 使用示例
const attachmentManager = new AttachDocumentManager();

// 创建附件组件
const attachDocument = attachmentManager.createAttachDocument({
    record: recordObject,
    string: 'Attach Files',
    action: 'process_attachments',
    highlight: true
});

// 注册自定义文件类型
attachmentManager.registerFileType('video', {
    name: 'Video Files',
    extensions: ['.mp4', '.avi', '.mov', '.wmv'],
    mimeTypes: ['video/*'],
    maxSize: 100 * 1024 * 1024, // 100MB
    preview: false
});

// 获取统计信息
const stats = attachmentManager.getAttachmentStatistics();
console.log('Attachment statistics:', stats);
```

## 技术特点

### 1. 文件处理
- **多文件**: 支持多文件同时上传
- **类型验证**: 验证文件类型和大小
- **进度跟踪**: 跟踪上传进度
- **错误处理**: 完善的错误处理机制

### 2. 用户体验
- **拖拽上传**: 支持拖拽文件上传
- **预览功能**: 支持文件预览
- **进度显示**: 显示上传进度
- **状态反馈**: 提供清晰的状态反馈

### 3. 安全性
- **CSRF保护**: 包含CSRF令牌
- **文件验证**: 验证文件安全性
- **大小限制**: 限制文件大小
- **类型限制**: 限制文件类型

### 4. 集成性
- **记录关联**: 与记录对象关联
- **动作执行**: 支持上传后动作
- **ORM集成**: 集成ORM操作
- **服务集成**: 集成各种服务

## 设计模式

### 1. 组件模式 (Component Pattern)
- **文件组件**: 专门的文件处理组件
- **输入组件**: 集成文件输入组件
- **状态组件**: 管理上传状态

### 2. 策略模式 (Strategy Pattern)
- **上传策略**: 不同的上传处理策略
- **验证策略**: 不同的文件验证策略
- **处理策略**: 不同的后处理策略

### 3. 观察者模式 (Observer Pattern)
- **进度观察**: 观察上传进度
- **状态观察**: 观察上传状态
- **错误观察**: 观察错误状态

### 4. 工厂模式 (Factory Pattern)
- **组件工厂**: 创建附件组件
- **验证工厂**: 创建验证器
- **处理工厂**: 创建处理器

## 注意事项

1. **文件大小**: 注意文件大小限制
2. **服务器配置**: 确保服务器支持文件上传
3. **安全性**: 验证文件安全性
4. **性能**: 处理大文件时的性能

## 扩展建议

1. **拖拽上传**: 添加拖拽上传功能
2. **文件预览**: 支持文件预览功能
3. **批量操作**: 支持批量文件操作
4. **云存储**: 集成云存储服务
5. **压缩功能**: 支持文件压缩

该附件文档组件为Odoo Web客户端提供了完整的文件上传解决方案，通过简洁的接口和强大的功能确保了文件处理的便利性和安全性。

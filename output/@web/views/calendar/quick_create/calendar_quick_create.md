# CalendarQuickCreate - 日历快速创建

## 概述

`calendar_quick_create.js` 是 Odoo Web 客户端日历视图的快速创建组件，提供了快速创建日历事件的功能。该模块包含91行代码，是一个OWL组件，专门用于在日历视图中快速创建新事件，具备对话框界面、自动聚焦、标题输入、记录编辑等特性，是日历视图系统中事件创建的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/calendar/quick_create/calendar_quick_create.js`
- **行数**: 91
- **模块**: `@web/views/calendar/quick_create/calendar_quick_create`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/hooks'          // 工具钩子
'@web/core/dialog/dialog'        // 对话框组件
'@web/core/l10n/translation'     // 翻译服务
'@odoo/owl'                      // OWL框架
```

## 主要组件定义

### 1. CalendarQuickCreate - 日历快速创建

```javascript
class CalendarQuickCreate extends Component {
    static template = "web.CalendarQuickCreate";
    static components = {
        Dialog,
    };
    static props = {
        title: { type: String, optional: true },
        close: Function,
        record: Object,
        model: Object,
        editRecord: Function,
    };

    setup() {
        this.titleRef = useAutofocus({ refName: "title" });
        this.notification = useService("notification");
        this.creatingRecord = false;
    }
}
```

**组件特性**:
- **对话框集成**: 集成Dialog组件
- **专用模板**: 使用快速创建专用模板
- **自动聚焦**: 自动聚焦到标题输入框
- **通知服务**: 集成通知服务

## 核心功能

### 1. 对话框标题

```javascript
get dialogTitle() {
    return _t("New Event");
}
```

**标题功能**:
- **本地化**: 使用本地化的标题文本
- **固定标题**: 显示"新建事件"标题
- **用户友好**: 提供清晰的用户提示
- **多语言**: 支持多语言显示

### 2. 记录标题获取

```javascript
get recordTitle() {
    return this.titleRef.el.value.trim();
}
```

**标题获取功能**:
- **实时获取**: 实时获取输入框的值
- **空格处理**: 自动去除首尾空格
- **引用访问**: 通过ref引用访问DOM元素
- **值验证**: 获取处理后的有效值

### 3. 记录对象构建

```javascript
get record() {
    return {
        ...this.props.record,
        title: this.recordTitle,
    };
}
```

**记录构建功能**:
- **属性合并**: 合并原有记录属性
- **标题更新**: 更新记录标题
- **对象扩展**: 使用扩展运算符合并属性
- **数据准备**: 为保存准备完整的记录数据

### 4. 编辑记录

```javascript
editRecord() {
    this.props.editRecord(this.record);
    this.props.close();
}
```

**编辑功能**:
- **记录传递**: 传递构建的记录对象
- **编辑调用**: 调用父组件的编辑方法
- **对话框关闭**: 编辑后关闭对话框
- **流程完成**: 完成快速创建流程

### 5. 快速保存

```javascript
async quickSave() {
    if (this.creatingRecord) {
        return;
    }
    
    const title = this.recordTitle;
    if (!title) {
        this.notification.add(_t("Please enter a title"), { type: "warning" });
        return;
    }
    
    this.creatingRecord = true;
    
    try {
        const record = this.record;
        await this.props.model.createRecord(record);
        
        this.notification.add(_t("Event created successfully"), { type: "success" });
        this.props.close();
        
    } catch (error) {
        this.notification.add(_t("Failed to create event"), { type: "danger" });
        console.error("Quick create error:", error);
    } finally {
        this.creatingRecord = false;
    }
}
```

**快速保存功能**:
- **重复检查**: 防止重复创建
- **标题验证**: 验证标题是否为空
- **异步创建**: 异步创建记录
- **错误处理**: 完善的错误处理机制

## 使用场景

### 1. 快速创建管理器

```javascript
// 快速创建管理器
class QuickCreateManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置快速创建配置
        this.quickCreateConfig = {
            enableAutoSave: true,
            enableValidation: true,
            enableTemplates: true,
            enableShortcuts: true,
            autoSaveDelay: 2000,
            maxTitleLength: 100
        };
        
        // 设置模板
        this.eventTemplates = new Map();
        
        // 设置验证规则
        this.validationRules = new Map();
        
        // 设置快速创建统计
        this.quickCreateStatistics = {
            totalCreated: 0,
            successfulCreated: 0,
            failedCreated: 0,
            templatesUsed: 0
        };
        
        this.initializeQuickCreateSystem();
    }
    
    // 初始化快速创建系统
    initializeQuickCreateSystem() {
        // 创建增强的快速创建组件
        this.createEnhancedQuickCreate();
        
        // 设置事件模板
        this.setupEventTemplates();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置键盘快捷键
        this.setupKeyboardShortcuts();
    }
    
    // 创建增强的快速创建组件
    createEnhancedQuickCreate() {
        const originalQuickCreate = CalendarQuickCreate;
        
        this.EnhancedQuickCreate = class extends originalQuickCreate {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加模板支持
                this.addTemplateSupport();
                
                // 添加自动保存
                this.addAutoSave();
                
                // 添加验证
                this.addValidation();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = useState({
                    selectedTemplate: null,
                    isAutoSaving: false,
                    validationErrors: [],
                    suggestions: [],
                    showSuggestions: false
                });
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 应用模板
                this.applyTemplate = (template) => {
                    this.enhancedState.selectedTemplate = template.id;
                    
                    // 填充模板数据
                    if (template.title) {
                        this.titleRef.el.value = template.title;
                    }
                    
                    // 更新记录数据
                    Object.assign(this.props.record, template.data);
                    
                    // 统计
                    this.quickCreateStatistics.templatesUsed++;
                };
                
                // 获取建议
                this.getSuggestions = async (query) => {
                    if (!query || query.length < 2) {
                        this.enhancedState.suggestions = [];
                        this.enhancedState.showSuggestions = false;
                        return;
                    }
                    
                    try {
                        const suggestions = await this.loadSuggestions(query);
                        this.enhancedState.suggestions = suggestions;
                        this.enhancedState.showSuggestions = suggestions.length > 0;
                    } catch (error) {
                        console.warn('Failed to load suggestions:', error);
                    }
                };
                
                // 选择建议
                this.selectSuggestion = (suggestion) => {
                    this.titleRef.el.value = suggestion.title;
                    
                    if (suggestion.data) {
                        Object.assign(this.props.record, suggestion.data);
                    }
                    
                    this.enhancedState.showSuggestions = false;
                };
                
                // 清除建议
                this.clearSuggestions = () => {
                    this.enhancedState.suggestions = [];
                    this.enhancedState.showSuggestions = false;
                };
                
                // 验证输入
                this.validateInput = () => {
                    const errors = [];
                    const title = this.recordTitle;
                    
                    // 标题验证
                    if (!title) {
                        errors.push({ field: 'title', message: _t('Title is required') });
                    } else if (title.length > this.quickCreateConfig.maxTitleLength) {
                        errors.push({ 
                            field: 'title', 
                            message: _t('Title is too long (max %s characters)', this.quickCreateConfig.maxTitleLength) 
                        });
                    }
                    
                    // 自定义验证规则
                    for (const [field, rule] of this.validationRules) {
                        const value = this.props.record[field];
                        if (!rule.validate(value)) {
                            errors.push({ field, message: rule.message });
                        }
                    }
                    
                    this.enhancedState.validationErrors = errors;
                    return errors.length === 0;
                };
                
                // 获取字段错误
                this.getFieldError = (field) => {
                    return this.enhancedState.validationErrors.find(error => error.field === field);
                };
            }
            
            addTemplateSupport() {
                // 获取可用模板
                this.getAvailableTemplates = () => {
                    return Array.from(this.eventTemplates.values()).filter(template => {
                        return !template.condition || template.condition(this.props.record);
                    });
                };
                
                // 创建自定义模板
                this.createCustomTemplate = (name, data) => {
                    const template = {
                        id: `custom_${Date.now()}`,
                        name: name,
                        title: this.recordTitle,
                        data: { ...this.props.record },
                        custom: true,
                        createdAt: new Date()
                    };
                    
                    this.eventTemplates.set(template.id, template);
                    
                    // 保存到本地存储
                    this.saveCustomTemplates();
                    
                    return template;
                };
                
                // 删除自定义模板
                this.deleteCustomTemplate = (templateId) => {
                    const template = this.eventTemplates.get(templateId);
                    
                    if (template && template.custom) {
                        this.eventTemplates.delete(templateId);
                        this.saveCustomTemplates();
                        return true;
                    }
                    
                    return false;
                };
                
                // 保存自定义模板
                this.saveCustomTemplates = () => {
                    const customTemplates = Array.from(this.eventTemplates.values())
                        .filter(template => template.custom);
                    
                    localStorage.setItem('calendar_custom_templates', JSON.stringify(customTemplates));
                };
                
                // 加载自定义模板
                this.loadCustomTemplates = () => {
                    try {
                        const saved = localStorage.getItem('calendar_custom_templates');
                        if (saved) {
                            const templates = JSON.parse(saved);
                            for (const template of templates) {
                                this.eventTemplates.set(template.id, template);
                            }
                        }
                    } catch (error) {
                        console.warn('Failed to load custom templates:', error);
                    }
                };
            }
            
            addAutoSave() {
                let autoSaveTimer;
                
                // 开始自动保存
                this.startAutoSave = () => {
                    if (!this.quickCreateConfig.enableAutoSave) return;
                    
                    this.stopAutoSave();
                    
                    autoSaveTimer = setTimeout(() => {
                        this.performAutoSave();
                    }, this.quickCreateConfig.autoSaveDelay);
                };
                
                // 停止自动保存
                this.stopAutoSave = () => {
                    if (autoSaveTimer) {
                        clearTimeout(autoSaveTimer);
                        autoSaveTimer = null;
                    }
                };
                
                // 执行自动保存
                this.performAutoSave = async () => {
                    if (this.creatingRecord || this.enhancedState.isAutoSaving) {
                        return;
                    }
                    
                    const title = this.recordTitle;
                    if (!title) {
                        return;
                    }
                    
                    if (!this.validateInput()) {
                        return;
                    }
                    
                    this.enhancedState.isAutoSaving = true;
                    
                    try {
                        await this.quickSave();
                    } catch (error) {
                        console.warn('Auto save failed:', error);
                    } finally {
                        this.enhancedState.isAutoSaving = false;
                    }
                };
                
                // 重置自动保存
                this.resetAutoSave = () => {
                    this.stopAutoSave();
                    this.startAutoSave();
                };
            }
            
            addValidation() {
                // 实时验证
                this.onInputChange = () => {
                    if (this.quickCreateConfig.enableValidation) {
                        this.validateInput();
                    }
                    
                    // 获取建议
                    this.getSuggestions(this.recordTitle);
                    
                    // 重置自动保存
                    this.resetAutoSave();
                };
                
                // 验证并保存
                this.validateAndSave = async () => {
                    if (!this.validateInput()) {
                        const firstError = this.enhancedState.validationErrors[0];
                        this.notification.add(firstError.message, { type: "warning" });
                        return false;
                    }
                    
                    return await this.quickSave();
                };
            }
            
            // 加载建议
            async loadSuggestions(query) {
                try {
                    const domain = [['name', 'ilike', query]];
                    const suggestions = await this.orm.searchRead(
                        this.props.model.meta.resModel,
                        domain,
                        ['id', 'name', 'description'],
                        { limit: 5 }
                    );
                    
                    return suggestions.map(record => ({
                        id: record.id,
                        title: record.name,
                        description: record.description,
                        data: record
                    }));
                } catch (error) {
                    console.warn('Failed to load suggestions:', error);
                    return [];
                }
            }
            
            // 重写快速保存方法
            async quickSave() {
                this.quickCreateStatistics.totalCreated++;
                
                try {
                    const result = await super.quickSave();
                    this.quickCreateStatistics.successfulCreated++;
                    return result;
                } catch (error) {
                    this.quickCreateStatistics.failedCreated++;
                    throw error;
                }
            }
            
            // 获取统计信息
            getStatistics() {
                return {
                    ...this.quickCreateStatistics,
                    selectedTemplate: this.enhancedState.selectedTemplate,
                    validationErrors: this.enhancedState.validationErrors.length,
                    suggestionsShown: this.enhancedState.showSuggestions
                };
            }
        };
    }
    
    // 设置事件模板
    setupEventTemplates() {
        // 会议模板
        this.eventTemplates.set('meeting', {
            id: 'meeting',
            name: 'Meeting',
            title: 'Team Meeting',
            data: {
                duration: 1, // 1小时
                location: 'Conference Room',
                description: 'Team meeting discussion'
            }
        });
        
        // 任务模板
        this.eventTemplates.set('task', {
            id: 'task',
            name: 'Task',
            title: 'New Task',
            data: {
                duration: 2, // 2小时
                priority: 'normal'
            }
        });
        
        // 约会模板
        this.eventTemplates.set('appointment', {
            id: 'appointment',
            name: 'Appointment',
            title: 'Appointment',
            data: {
                duration: 0.5, // 30分钟
                reminder: true
            }
        });
        
        // 加载自定义模板
        this.loadCustomTemplates();
    }
    
    // 设置验证系统
    setupValidationSystem() {
        // 标题长度验证
        this.validationRules.set('title', {
            validate: (value) => value && value.length <= this.quickCreateConfig.maxTitleLength,
            message: _t('Title is required and must be less than %s characters', this.quickCreateConfig.maxTitleLength)
        });
        
        // 时间验证
        this.validationRules.set('start_date', {
            validate: (value) => value && new Date(value) > new Date(),
            message: _t('Start date must be in the future')
        });
    }
    
    // 设置键盘快捷键
    setupKeyboardShortcuts() {
        this.shortcuts = {
            'Ctrl+Enter': 'quickSave',
            'Ctrl+S': 'quickSave',
            'Escape': 'close',
            'Ctrl+T': 'showTemplates'
        };
    }
    
    // 获取快速创建统计
    getQuickCreateStatistics() {
        return {
            ...this.quickCreateStatistics,
            templateCount: this.eventTemplates.size,
            validationRuleCount: this.validationRules.size,
            shortcutCount: Object.keys(this.shortcuts).length
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理模板
        this.eventTemplates.clear();
        
        // 清理验证规则
        this.validationRules.clear();
        
        // 重置统计
        this.quickCreateStatistics = {
            totalCreated: 0,
            successfulCreated: 0,
            failedCreated: 0,
            templatesUsed: 0
        };
    }
}

// 使用示例
const quickCreateManager = new QuickCreateManager();

// 创建增强的快速创建组件
const EnhancedQuickCreate = quickCreateManager.EnhancedQuickCreate;

// 获取统计信息
const stats = quickCreateManager.getQuickCreateStatistics();
console.log('Quick create statistics:', stats);
```

## 技术特点

### 1. 简化界面
- **最小化**: 最小化的用户界面
- **聚焦输入**: 自动聚焦到关键输入
- **快速操作**: 支持快速的事件创建
- **直观设计**: 直观易用的设计

### 2. 自动化功能
- **自动聚焦**: 自动聚焦到标题输入框
- **自动保存**: 支持自动保存功能
- **智能建议**: 提供智能的输入建议
- **模板支持**: 支持事件模板

### 3. 验证机制
- **实时验证**: 实时验证用户输入
- **错误提示**: 清晰的错误提示信息
- **数据完整性**: 确保数据的完整性
- **用户友好**: 友好的验证体验

### 4. 扩展性
- **模板系统**: 可扩展的模板系统
- **验证规则**: 可配置的验证规则
- **快捷键**: 支持键盘快捷键
- **自定义**: 支持自定义配置

## 设计模式

### 1. 组件模式 (Component Pattern)
- **封装性**: 封装快速创建功能
- **可复用**: 高度可复用的组件
- **接口标准**: 标准化的组件接口

### 2. 模板模式 (Template Pattern)
- **事件模板**: 预定义的事件模板
- **数据填充**: 自动填充模板数据
- **快速创建**: 基于模板快速创建

### 3. 策略模式 (Strategy Pattern)
- **验证策略**: 不同的验证策略
- **保存策略**: 不同的保存策略
- **模板策略**: 不同的模板应用策略

### 4. 观察者模式 (Observer Pattern)
- **输入监听**: 监听用户输入变化
- **状态响应**: 响应组件状态变化
- **自动更新**: 自动更新相关内容

## 注意事项

1. **用户体验**: 确保流畅的用户体验
2. **数据验证**: 确保数据的有效性
3. **错误处理**: 完善的错误处理机制
4. **性能优化**: 避免不必要的操作

## 扩展建议

1. **更多模板**: 添加更多事件模板
2. **智能建议**: 增强智能建议功能
3. **批量创建**: 支持批量事件创建
4. **语音输入**: 添加语音输入功能
5. **AI助手**: 集成AI助手功能

该快速创建组件为Odoo Web客户端提供了高效的事件创建功能，通过简化界面、自动化功能和智能验证确保了用户友好的事件创建体验。

# CalendarRenderer - 日历渲染器

## 概述

`calendar_renderer.js` 是 Odoo Web 客户端日历视图的主渲染器组件，提供了日历视图的渲染和交互功能。该模块包含50行代码，是一个轻量级的OWL组件，专门用于根据不同的时间刻度选择合适的渲染器，具备动态组件选择、滑动手势支持、移动端适配等特性，是日历视图系统中渲染层的核心协调器。

## 文件信息
- **路径**: `/web/static/src/views/calendar/calendar_renderer.js`
- **行数**: 50
- **模块**: `@web/views/calendar/calendar_renderer`

## 依赖关系

```javascript
// 核心依赖
'@web/core/action_swiper/action_swiper'                              // 动作滑动器
'@web/views/calendar/calendar_common/calendar_common_renderer'       // 通用日历渲染器
'@web/views/calendar/calendar_year/calendar_year_renderer'           // 年视图渲染器
'@odoo/owl'                                                          // OWL框架
```

## 主要组件定义

### 1. CalendarRenderer - 日历渲染器

```javascript
class CalendarRenderer extends Component {
    static template = "web.CalendarRenderer";
    static components = {
        day: CalendarCommonRenderer,
        week: CalendarCommonRenderer,
        month: CalendarCommonRenderer,
        year: CalendarYearRenderer,
        ActionSwiper,
    };
    static props = {
        model: Object,
        isWeekendVisible: Boolean,
        createRecord: Function,
        editRecord: Function,
        deleteRecord: Function,
        setDate: Function,
    };

    get calendarComponent() {
        return this.constructor.components[this.props.model.scale];
    }

    get calendarKey() {
        return `${this.props.model.scale}_${this.props.model.date.valueOf()}`;
    }

    get actionSwiperProps() {
        return {
            onLeftSwipe: this.env.isSmall
                ? { action: () => this.props.setDate("next") }
                : undefined,
            onRightSwipe: this.env.isSmall
                ? { action: () => this.props.setDate("previous") }
                : undefined,
            animationOnMove: false,
            animationType: "forwards",
            swipeDistanceRatio: 6,
            swipeInvalid: () => Boolean(document.querySelector(".o_event.fc-mirror")),
        };
    }
}
```

**组件特性**:
- **模板定义**: 使用CalendarRenderer模板
- **动态组件**: 根据刻度动态选择渲染组件
- **属性接口**: 定义清晰的属性接口
- **滑动支持**: 集成滑动手势支持

## 核心功能

### 1. 组件映射

```javascript
static components = {
    day: CalendarCommonRenderer,      // 日视图使用通用渲染器
    week: CalendarCommonRenderer,     // 周视图使用通用渲染器
    month: CalendarCommonRenderer,    // 月视图使用通用渲染器
    year: CalendarYearRenderer,       // 年视图使用专用渲染器
    ActionSwiper,                     // 滑动手势组件
};
```

**组件映射功能**:
- **刻度映射**: 将时间刻度映射到对应的渲染器
- **通用渲染**: 日、周、月视图使用通用渲染器
- **专用渲染**: 年视图使用专门的渲染器
- **手势集成**: 集成滑动手势组件

### 2. 动态组件选择

```javascript
get calendarComponent() {
    return this.constructor.components[this.props.model.scale];
}
```

**动态选择功能**:
- **刻度检测**: 根据模型的当前刻度
- **组件选择**: 选择对应的渲染组件
- **动态切换**: 支持动态切换渲染器
- **类型安全**: 确保组件存在性

### 3. 组件键生成

```javascript
get calendarKey() {
    return `${this.props.model.scale}_${this.props.model.date.valueOf()}`;
}
```

**键生成功能**:
- **唯一标识**: 生成组件的唯一标识
- **刷新控制**: 控制组件的重新渲染
- **状态跟踪**: 跟踪刻度和日期变化
- **性能优化**: 避免不必要的重渲染

### 4. 滑动手势配置

```javascript
get actionSwiperProps() {
    return {
        onLeftSwipe: this.env.isSmall
            ? { action: () => this.props.setDate("next") }
            : undefined,
        onRightSwipe: this.env.isSmall
            ? { action: () => this.props.setDate("previous") }
            : undefined,
        animationOnMove: false,
        animationType: "forwards",
        swipeDistanceRatio: 6,
        swipeInvalid: () => Boolean(document.querySelector(".o_event.fc-mirror")),
    };
}
```

**滑动配置功能**:
- **移动端检测**: 只在小屏幕设备上启用滑动
- **方向映射**: 左滑下一个，右滑上一个
- **动画配置**: 配置滑动动画效果
- **无效检测**: 检测拖拽状态禁用滑动

### 5. 属性接口

```javascript
static props = {
    model: Object,              // 日历数据模型
    isWeekendVisible: Boolean,  // 周末是否可见
    createRecord: Function,     // 创建记录回调
    editRecord: Function,       // 编辑记录回调
    deleteRecord: Function,     // 删除记录回调
    setDate: Function,          // 设置日期回调
};
```

**属性接口功能**:
- **模型传递**: 传递日历数据模型
- **配置选项**: 传递视图配置选项
- **回调函数**: 定义操作回调函数
- **类型定义**: 明确属性类型要求

## 使用场景

### 1. 日历渲染管理器

```javascript
// 日历渲染管理器
class CalendarRenderingManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置渲染配置
        this.renderConfig = {
            enableSwipeGestures: true,
            enableAnimations: true,
            enableResponsiveDesign: true,
            enablePerformanceOptimization: true,
            swipeThreshold: 50,
            animationDuration: 300
        };
        
        // 设置渲染器映射
        this.rendererMapping = new Map([
            ['day', 'CalendarCommonRenderer'],
            ['week', 'CalendarCommonRenderer'],
            ['month', 'CalendarCommonRenderer'],
            ['year', 'CalendarYearRenderer']
        ]);
        
        // 设置渲染缓存
        this.renderCache = new Map();
        
        // 设置性能监控
        this.performanceMetrics = {
            renderCount: 0,
            averageRenderTime: 0,
            cacheHits: 0,
            cacheMisses: 0
        };
        
        this.initializeRenderers();
    }
    
    // 初始化渲染器
    initializeRenderers() {
        // 预加载渲染器组件
        this.preloadRenderers();
        
        // 设置渲染器配置
        this.configureRenderers();
    }
    
    // 预加载渲染器
    async preloadRenderers() {
        const rendererPromises = [];
        
        for (const [scale, rendererName] of this.rendererMapping) {
            rendererPromises.push(this.preloadRenderer(scale, rendererName));
        }
        
        await Promise.all(rendererPromises);
    }
    
    // 预加载单个渲染器
    async preloadRenderer(scale, rendererName) {
        try {
            // 这里可以实现渲染器的预加载逻辑
            console.log(`Preloading renderer for scale: ${scale}`);
        } catch (error) {
            console.warn(`Failed to preload renderer for ${scale}:`, error);
        }
    }
    
    // 配置渲染器
    configureRenderers() {
        // 配置通用渲染器
        this.configureCommonRenderer();
        
        // 配置年视图渲染器
        this.configureYearRenderer();
        
        // 配置滑动手势
        this.configureSwipeGestures();
    }
    
    // 配置通用渲染器
    configureCommonRenderer() {
        const commonConfig = {
            enableEventDragging: true,
            enableEventResizing: true,
            enableTimeSlotSelection: true,
            enableEventCreation: true,
            eventDisplayMode: 'auto',
            timeFormat: 'HH:mm',
            dateFormat: 'YYYY-MM-DD'
        };
        
        this.rendererConfigs = this.rendererConfigs || {};
        this.rendererConfigs.common = commonConfig;
    }
    
    // 配置年视图渲染器
    configureYearRenderer() {
        const yearConfig = {
            enableMonthNavigation: true,
            enableEventIndicators: true,
            enableHeatmap: false,
            monthDisplayMode: 'grid',
            eventIndicatorStyle: 'dot'
        };
        
        this.rendererConfigs = this.rendererConfigs || {};
        this.rendererConfigs.year = yearConfig;
    }
    
    // 配置滑动手势
    configureSwipeGestures() {
        const swipeConfig = {
            enableLeftSwipe: true,
            enableRightSwipe: true,
            enableUpSwipe: false,
            enableDownSwipe: false,
            swipeThreshold: this.renderConfig.swipeThreshold,
            animationDuration: this.renderConfig.animationDuration
        };
        
        this.rendererConfigs = this.rendererConfigs || {};
        this.rendererConfigs.swipe = swipeConfig;
    }
    
    // 创建增强的日历渲染器
    createEnhancedCalendarRenderer() {
        const originalRenderer = CalendarRenderer;
        
        return class EnhancedCalendarRenderer extends originalRenderer {
            setup() {
                super.setup();
                
                // 添加性能监控
                this.addPerformanceMonitoring();
                
                // 添加渲染缓存
                this.addRenderCaching();
                
                // 添加响应式支持
                this.addResponsiveSupport();
                
                // 添加错误处理
                this.addErrorHandling();
            }
            
            addPerformanceMonitoring() {
                // 监控渲染性能
                const originalRender = this.render.bind(this);
                
                this.render = function() {
                    const startTime = performance.now();
                    
                    const result = originalRender();
                    
                    const endTime = performance.now();
                    const renderTime = endTime - startTime;
                    
                    this.recordRenderMetrics(renderTime);
                    
                    return result;
                };
            }
            
            addRenderCaching() {
                // 添加渲染结果缓存
                this.renderCache = new Map();
                
                const originalCalendarComponent = Object.getOwnPropertyDescriptor(
                    Object.getPrototypeOf(this), 'calendarComponent'
                ).get.bind(this);
                
                Object.defineProperty(this, 'calendarComponent', {
                    get: function() {
                        const cacheKey = this.calendarKey;
                        
                        if (this.renderCache.has(cacheKey)) {
                            return this.renderCache.get(cacheKey);
                        }
                        
                        const component = originalCalendarComponent();
                        this.renderCache.set(cacheKey, component);
                        
                        return component;
                    }
                });
            }
            
            addResponsiveSupport() {
                // 添加响应式设计支持
                this.setupResponsiveBreakpoints();
                this.setupOrientationHandling();
            }
            
            setupResponsiveBreakpoints() {
                this.breakpoints = {
                    mobile: 768,
                    tablet: 1024,
                    desktop: 1200
                };
                
                this.currentBreakpoint = this.getCurrentBreakpoint();
                
                // 监听窗口大小变化
                window.addEventListener('resize', this.handleResize.bind(this));
            }
            
            getCurrentBreakpoint() {
                const width = window.innerWidth;
                
                if (width < this.breakpoints.mobile) {
                    return 'mobile';
                } else if (width < this.breakpoints.tablet) {
                    return 'tablet';
                } else {
                    return 'desktop';
                }
            }
            
            handleResize() {
                const newBreakpoint = this.getCurrentBreakpoint();
                
                if (newBreakpoint !== this.currentBreakpoint) {
                    this.currentBreakpoint = newBreakpoint;
                    this.onBreakpointChange(newBreakpoint);
                }
            }
            
            onBreakpointChange(breakpoint) {
                // 响应断点变化
                this.updateSwipeGestures(breakpoint);
                this.updateComponentLayout(breakpoint);
            }
            
            updateSwipeGestures(breakpoint) {
                // 根据断点更新滑动手势
                const enableSwipe = breakpoint === 'mobile';
                
                if (this.actionSwiper) {
                    this.actionSwiper.setEnabled(enableSwipe);
                }
            }
            
            updateComponentLayout(breakpoint) {
                // 根据断点更新组件布局
                const layoutClass = `o_calendar_${breakpoint}`;
                
                if (this.el) {
                    this.el.className = this.el.className.replace(
                        /o_calendar_(mobile|tablet|desktop)/g, 
                        layoutClass
                    );
                    
                    if (!this.el.classList.contains(layoutClass)) {
                        this.el.classList.add(layoutClass);
                    }
                }
            }
            
            setupOrientationHandling() {
                // 处理设备方向变化
                window.addEventListener('orientationchange', () => {
                    setTimeout(() => {
                        this.handleOrientationChange();
                    }, 100);
                });
            }
            
            handleOrientationChange() {
                // 处理方向变化
                this.updateLayout();
                this.refreshRenderer();
            }
            
            addErrorHandling() {
                // 添加错误处理
                this.setupErrorBoundary();
                this.setupFallbackRendering();
            }
            
            setupErrorBoundary() {
                // 设置错误边界
                const originalCalendarComponent = Object.getOwnPropertyDescriptor(
                    Object.getPrototypeOf(this), 'calendarComponent'
                ).get.bind(this);
                
                Object.defineProperty(this, 'calendarComponent', {
                    get: function() {
                        try {
                            return originalCalendarComponent();
                        } catch (error) {
                            console.error('Calendar component error:', error);
                            return this.getFallbackComponent();
                        }
                    }
                });
            }
            
            getFallbackComponent() {
                // 返回回退组件
                return this.constructor.components.day; // 默认使用日视图
            }
            
            setupFallbackRendering() {
                // 设置回退渲染
                this.fallbackRenderer = {
                    render: () => {
                        return '<div class="o_calendar_fallback">Calendar temporarily unavailable</div>';
                    }
                };
            }
            
            recordRenderMetrics(renderTime) {
                // 记录渲染指标
                this.performanceMetrics.renderCount++;
                
                const totalTime = this.performanceMetrics.averageRenderTime * 
                    (this.performanceMetrics.renderCount - 1) + renderTime;
                
                this.performanceMetrics.averageRenderTime = 
                    totalTime / this.performanceMetrics.renderCount;
            }
            
            updateLayout() {
                // 更新布局
                if (this.calendarComponent && this.calendarComponent.updateLayout) {
                    this.calendarComponent.updateLayout();
                }
            }
            
            refreshRenderer() {
                // 刷新渲染器
                this.renderCache.clear();
                this.render();
            }
            
            getPerformanceMetrics() {
                return {
                    ...this.performanceMetrics,
                    cacheSize: this.renderCache.size,
                    currentBreakpoint: this.currentBreakpoint
                };
            }
            
            destroy() {
                // 清理资源
                window.removeEventListener('resize', this.handleResize);
                window.removeEventListener('orientationchange', this.handleOrientationChange);
                
                if (this.renderCache) {
                    this.renderCache.clear();
                }
                
                super.destroy();
            }
        };
    }
    
    // 获取渲染器配置
    getRendererConfig(scale) {
        if (scale === 'year') {
            return this.rendererConfigs.year;
        } else {
            return this.rendererConfigs.common;
        }
    }
    
    // 获取滑动配置
    getSwipeConfig() {
        return this.rendererConfigs.swipe;
    }
    
    // 获取性能指标
    getPerformanceMetrics() {
        return { ...this.performanceMetrics };
    }
    
    // 清理缓存
    clearCache() {
        this.renderCache.clear();
    }
    
    // 销毁管理器
    destroy() {
        // 清理缓存
        this.clearCache();
        
        // 清理配置
        this.rendererConfigs = {};
        
        // 重置指标
        this.performanceMetrics = {
            renderCount: 0,
            averageRenderTime: 0,
            cacheHits: 0,
            cacheMisses: 0
        };
    }
}

// 使用示例
const renderingManager = new CalendarRenderingManager();

// 创建增强的渲染器
const EnhancedCalendarRenderer = renderingManager.createEnhancedCalendarRenderer();

// 获取渲染器配置
const commonConfig = renderingManager.getRendererConfig('week');
const yearConfig = renderingManager.getRendererConfig('year');

// 获取性能指标
const metrics = renderingManager.getPerformanceMetrics();
console.log('Rendering metrics:', metrics);
```

## 技术特点

### 1. 轻量级设计
- **简洁代码**: 仅50行代码实现核心功能
- **清晰职责**: 专注于渲染器选择和协调
- **高效实现**: 高效的组件选择机制
- **最小依赖**: 最少的依赖关系

### 2. 动态组件选择
- **刻度映射**: 根据时间刻度选择渲染器
- **组件复用**: 通用渲染器的复用设计
- **专用支持**: 年视图的专用渲染器
- **类型安全**: 确保组件的存在性

### 3. 移动端支持
- **滑动手势**: 完整的滑动手势支持
- **响应式**: 根据屏幕尺寸调整功能
- **触摸优化**: 优化的触摸交互体验
- **性能考虑**: 考虑移动端性能限制

### 4. 性能优化
- **组件键**: 智能的组件键生成
- **重渲染控制**: 避免不必要的重渲染
- **状态检测**: 检测拖拽状态优化交互
- **缓存机制**: 支持渲染结果缓存

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- **渲染策略**: 不同刻度的渲染策略
- **组件选择**: 动态的组件选择策略
- **交互策略**: 不同设备的交互策略

### 2. 工厂模式 (Factory Pattern)
- **组件工厂**: 根据刻度创建渲染组件
- **配置工厂**: 创建不同的配置对象
- **属性工厂**: 生成组件属性

### 3. 代理模式 (Proxy Pattern)
- **组件代理**: 代理不同的渲染组件
- **属性代理**: 代理组件属性访问
- **方法代理**: 代理组件方法调用

### 4. 适配器模式 (Adapter Pattern)
- **接口适配**: 适配不同渲染器的接口
- **属性适配**: 适配不同的属性格式
- **方法适配**: 适配不同的方法签名

## 注意事项

1. **组件存在性**: 确保映射的组件存在
2. **属性传递**: 正确传递所需的属性
3. **性能影响**: 避免频繁的组件切换
4. **移动端兼容**: 确保移动端的兼容性

## 扩展建议

1. **更多渲染器**: 支持更多类型的渲染器
2. **动画增强**: 增强组件切换动画
3. **性能监控**: 添加渲染性能监控
4. **错误处理**: 增强错误处理和回退机制
5. **可访问性**: 增强可访问性支持

该日历渲染器为Odoo Web客户端提供了灵活的日历视图渲染功能，通过动态组件选择、移动端支持和性能优化确保了不同时间刻度下的优秀渲染体验。

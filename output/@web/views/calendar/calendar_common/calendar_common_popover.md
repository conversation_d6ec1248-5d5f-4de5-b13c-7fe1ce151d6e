# CalendarCommonPopover - 日历通用弹出框

## 概述

`calendar_common_popover.js` 是 Odoo Web 客户端日历视图的通用弹出框组件，提供了日历事件的详细信息显示和快速操作功能。该模块包含124行代码，是一个OWL组件，专门用于在日历事件上显示弹出框，具备事件详情展示、时间格式化、字段渲染、操作按钮等特性，是日历视图系统中事件交互的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/calendar/calendar_common/calendar_common_popover.js`
- **行数**: 124
- **模块**: `@web/views/calendar/calendar_common/calendar_common_popover`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'        // 翻译服务
'@web/core/dialog/dialog'            // 对话框组件
'@web/core/py_js/py'                 // Python表达式解析
'@web/core/l10n/dates'               // 日期本地化
'@web/core/registry'                 // 注册表系统
'@web/views/fields/field'            // 字段组件
'@web/model/record'                  // 记录模型
'@web/views/calendar/utils'          // 日历工具函数
'@odoo/owl'                          // OWL框架
```

## 主要组件定义

### 1. CalendarCommonPopover - 日历通用弹出框

```javascript
class CalendarCommonPopover extends Component {
    static template = "web.CalendarCommonPopover";
    static subTemplates = {
        popover: "web.CalendarCommonPopover.popover",
        body: "web.CalendarCommonPopover.body",
        footer: "web.CalendarCommonPopover.footer",
    };
    static components = {
        Dialog,
        Field,
        Record,
    };
    static props = {
        close: Function,
        record: Object,
        model: Object,
        createRecord: Function,
        deleteRecord: Function,
        editRecord: Function,
    };

    setup() {
        this.time = null;
        this.timeDuration = null;
        this.date = null;
        this.dateDuration = null;

        useExternalListener(window, "pointerdown", (e) => e.preventDefault(), { capture: true });

        this.computeDateTimeAndDuration();
    }
}
```

**组件特性**:
- **模板系统**: 使用主模板和子模板结构
- **子组件**: 集成对话框、字段和记录组件
- **属性接口**: 定义清晰的属性接口
- **事件监听**: 监听全局指针事件

## 核心功能

### 1. 模板结构

```javascript
static subTemplates = {
    popover: "web.CalendarCommonPopover.popover",
    body: "web.CalendarCommonPopover.body",
    footer: "web.CalendarCommonPopover.footer",
};
```

**模板结构功能**:
- **主模板**: 定义弹出框的整体结构
- **弹出框模板**: 定义弹出框容器
- **主体模板**: 定义弹出框内容区域
- **底部模板**: 定义操作按钮区域

### 2. 组件集成

```javascript
static components = {
    Dialog,     // 对话框组件
    Field,      // 字段组件
    Record,     // 记录组件
};
```

**组件集成功能**:
- **Dialog**: 提供对话框功能
- **Field**: 渲染记录字段
- **Record**: 管理记录数据
- **模块化**: 模块化的组件设计

### 3. 属性接口

```javascript
static props = {
    close: Function,        // 关闭回调函数
    record: Object,         // 记录对象
    model: Object,          // 模型对象
    createRecord: Function, // 创建记录回调
    deleteRecord: Function, // 删除记录回调
    editRecord: Function,   // 编辑记录回调
};
```

**属性接口功能**:
- **关闭控制**: 提供弹出框关闭功能
- **数据传递**: 传递记录和模型数据
- **操作回调**: 提供CRUD操作回调
- **类型安全**: 明确属性类型要求

### 4. 初始化设置

```javascript
setup() {
    this.time = null;
    this.timeDuration = null;
    this.date = null;
    this.dateDuration = null;

    useExternalListener(window, "pointerdown", (e) => e.preventDefault(), { capture: true });

    this.computeDateTimeAndDuration();
}
```

**初始化功能**:
- **状态初始化**: 初始化时间和日期状态
- **事件监听**: 监听全局指针事件
- **事件阻止**: 阻止默认的指针事件
- **计算调用**: 计算日期时间和持续时间

### 5. 日期时间计算

```javascript
computeDateTimeAndDuration() {
    const { record, model } = this.props;
    const { fieldMapping } = model.meta;

    // 获取开始和结束时间
    const startField = fieldMapping.date_start;
    const endField = fieldMapping.date_stop || fieldMapping.date_start;

    const start = record.data[startField];
    const end = record.data[endField];

    if (start && end) {
        // 计算日期范围
        this.date = getFormattedDateSpan(start, end);

        // 计算时间信息
        if (start.hasSame(end, "day")) {
            // 同一天的事件
            if (is24HourFormat()) {
                this.time = `${start.toFormat("HH:mm")} - ${end.toFormat("HH:mm")}`;
            } else {
                this.time = `${start.toFormat("h:mm a")} - ${end.toFormat("h:mm a")}`;
            }
        } else {
            // 跨天事件
            this.time = null;
        }

        // 计算持续时间
        const duration = end.diff(start);
        this.timeDuration = duration.toHuman();
    }
}
```

**计算功能**:
- **字段映射**: 使用模型的字段映射获取时间字段
- **日期格式化**: 使用工具函数格式化日期范围
- **时间格式**: 根据本地化设置格式化时间
- **持续时间**: 计算和格式化事件持续时间

## 使用场景

### 1. 弹出框管理器

```javascript
// 弹出框管理器
class PopoverManager {
    constructor() {
        this.setupManager();
    }

    setupManager() {
        // 设置弹出框配置
        this.popoverConfig = {
            enableAutoClose: true,
            enableAnimation: true,
            enableKeyboardNavigation: true,
            enableAccessibility: true,
            autoCloseDelay: 5000,
            animationDuration: 200
        };

        // 设置弹出框实例
        this.popoverInstances = new Map();

        // 设置弹出框模板
        this.popoverTemplates = new Map();

        // 设置弹出框统计
        this.popoverStatistics = {
            totalPopovers: 0,
            activePopovers: 0,
            createdPopovers: 0,
            closedPopovers: 0
        };

        this.initializePopoverSystem();
    }

    // 初始化弹出框系统
    initializePopoverSystem() {
        // 注册默认模板
        this.registerDefaultTemplates();

        // 设置全局事件监听
        this.setupGlobalEventListeners();

        // 设置键盘导航
        this.setupKeyboardNavigation();
    }

    // 注册默认模板
    registerDefaultTemplates() {
        // 事件详情模板
        this.popoverTemplates.set('event_details', {
            template: 'web.CalendarCommonPopover',
            subTemplates: {
                popover: 'web.CalendarCommonPopover.popover',
                body: 'web.CalendarCommonPopover.body',
                footer: 'web.CalendarCommonPopover.footer'
            },
            components: ['Dialog', 'Field', 'Record']
        });

        // 快速预览模板
        this.popoverTemplates.set('quick_preview', {
            template: 'web.CalendarQuickPreview',
            subTemplates: {
                popover: 'web.CalendarQuickPreview.popover',
                body: 'web.CalendarQuickPreview.body'
            },
            components: ['Field']
        });

        // 编辑模板
        this.popoverTemplates.set('edit_form', {
            template: 'web.CalendarEditForm',
            subTemplates: {
                popover: 'web.CalendarEditForm.popover',
                body: 'web.CalendarEditForm.body',
                footer: 'web.CalendarEditForm.footer'
            },
            components: ['Dialog', 'Field', 'Record']
        });
    }

    // 创建增强的弹出框组件
    createEnhancedPopover() {
        const originalPopover = CalendarCommonPopover;

        return class EnhancedCalendarPopover extends originalPopover {
            setup() {
                super.setup();

                // 添加增强功能
                this.addEnhancedFeatures();

                // 添加可访问性支持
                this.addAccessibilitySupport();

                // 添加动画支持
                this.addAnimationSupport();

                // 添加键盘导航
                this.addKeyboardNavigation();
            }

            addEnhancedFeatures() {
                // 添加自动关闭功能
                if (this.popoverConfig.enableAutoClose) {
                    this.setupAutoClose();
                }

                // 添加位置调整
                this.setupPositionAdjustment();

                // 添加大小调整
                this.setupSizeAdjustment();
            }

            setupAutoClose() {
                this.autoCloseTimer = setTimeout(() => {
                    if (this.props.close) {
                        this.props.close();
                    }
                }, this.popoverConfig.autoCloseDelay);

                // 鼠标进入时取消自动关闭
                this.addEventListener('mouseenter', () => {
                    if (this.autoCloseTimer) {
                        clearTimeout(this.autoCloseTimer);
                        this.autoCloseTimer = null;
                    }
                });

                // 鼠标离开时重新设置自动关闭
                this.addEventListener('mouseleave', () => {
                    this.setupAutoClose();
                });
            }

            setupPositionAdjustment() {
                // 动态调整弹出框位置
                this.adjustPosition = () => {
                    const popoverEl = this.el;
                    const rect = popoverEl.getBoundingClientRect();
                    const viewport = {
                        width: window.innerWidth,
                        height: window.innerHeight
                    };

                    // 检查是否超出视口
                    if (rect.right > viewport.width) {
                        popoverEl.style.left = `${viewport.width - rect.width - 10}px`;
                    }

                    if (rect.bottom > viewport.height) {
                        popoverEl.style.top = `${viewport.height - rect.height - 10}px`;
                    }

                    if (rect.left < 0) {
                        popoverEl.style.left = '10px';
                    }

                    if (rect.top < 0) {
                        popoverEl.style.top = '10px';
                    }
                };

                // 在挂载后调整位置
                onMounted(() => {
                    this.adjustPosition();
                });
            }

            setupSizeAdjustment() {
                // 根据内容调整大小
                this.adjustSize = () => {
                    const popoverEl = this.el;
                    const content = popoverEl.querySelector('.popover-body');

                    if (content) {
                        const contentHeight = content.scrollHeight;
                        const maxHeight = window.innerHeight * 0.8;

                        if (contentHeight > maxHeight) {
                            content.style.maxHeight = `${maxHeight}px`;
                            content.style.overflowY = 'auto';
                        }
                    }
                };

                onMounted(() => {
                    this.adjustSize();
                });
            }

            addAccessibilitySupport() {
                // 添加ARIA属性
                this.setupAriaAttributes = () => {
                    const popoverEl = this.el;

                    popoverEl.setAttribute('role', 'dialog');
                    popoverEl.setAttribute('aria-modal', 'true');
                    popoverEl.setAttribute('aria-labelledby', 'popover-title');
                    popoverEl.setAttribute('aria-describedby', 'popover-content');
                };

                // 设置焦点管理
                this.setupFocusManagement = () => {
                    const popoverEl = this.el;
                    const focusableElements = popoverEl.querySelectorAll(
                        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
                    );

                    if (focusableElements.length > 0) {
                        focusableElements[0].focus();
                    }

                    // 焦点陷阱
                    this.setupFocusTrap(focusableElements);
                };

                onMounted(() => {
                    this.setupAriaAttributes();
                    this.setupFocusManagement();
                });
            }

            setupFocusTrap(focusableElements) {
                const firstElement = focusableElements[0];
                const lastElement = focusableElements[focusableElements.length - 1];

                this.addEventListener('keydown', (event) => {
                    if (event.key === 'Tab') {
                        if (event.shiftKey) {
                            // Shift + Tab
                            if (document.activeElement === firstElement) {
                                event.preventDefault();
                                lastElement.focus();
                            }
                        } else {
                            // Tab
                            if (document.activeElement === lastElement) {
                                event.preventDefault();
                                firstElement.focus();
                            }
                        }
                    }
                });
            }

            addAnimationSupport() {
                if (!this.popoverConfig.enableAnimation) return;

                // 进入动画
                this.playEnterAnimation = () => {
                    const popoverEl = this.el;

                    popoverEl.style.opacity = '0';
                    popoverEl.style.transform = 'scale(0.8)';
                    popoverEl.style.transition = `opacity ${this.popoverConfig.animationDuration}ms ease, transform ${this.popoverConfig.animationDuration}ms ease`;

                    requestAnimationFrame(() => {
                        popoverEl.style.opacity = '1';
                        popoverEl.style.transform = 'scale(1)';
                    });
                };

                // 退出动画
                this.playExitAnimation = () => {
                    const popoverEl = this.el;

                    popoverEl.style.transition = `opacity ${this.popoverConfig.animationDuration}ms ease, transform ${this.popoverConfig.animationDuration}ms ease`;
                    popoverEl.style.opacity = '0';
                    popoverEl.style.transform = 'scale(0.8)';

                    setTimeout(() => {
                        if (this.props.close) {
                            this.props.close();
                        }
                    }, this.popoverConfig.animationDuration);
                };

                onMounted(() => {
                    this.playEnterAnimation();
                });
            }

            addKeyboardNavigation() {
                if (!this.popoverConfig.enableKeyboardNavigation) return;

                this.addEventListener('keydown', (event) => {
                    switch (event.key) {
                        case 'Escape':
                            event.preventDefault();
                            if (this.popoverConfig.enableAnimation) {
                                this.playExitAnimation();
                            } else {
                                this.props.close();
                            }
                            break;

                        case 'Enter':
                            if (event.target.tagName === 'BUTTON') {
                                event.target.click();
                            }
                            break;

                        case 'Delete':
                        case 'Backspace':
                            if (event.ctrlKey && this.props.deleteRecord) {
                                event.preventDefault();
                                this.confirmDelete();
                            }
                            break;
                    }
                });
            }

            confirmDelete() {
                const confirmDialog = {
                    title: _t('Delete Event'),
                    body: _t('Are you sure you want to delete this event?'),
                    confirmLabel: _t('Delete'),
                    confirm: () => {
                        this.props.deleteRecord(this.props.record);
                        this.props.close();
                    },
                    cancel: () => {}
                };

                // 显示确认对话框
                this.env.services.dialog.add(ConfirmationDialog, confirmDialog);
            }

            // 重写计算方法以添加更多信息
            computeDateTimeAndDuration() {
                super.computeDateTimeAndDuration();

                // 添加额外的时间信息
                this.addExtendedTimeInfo();

                // 添加重复信息
                this.addRecurrenceInfo();

                // 添加提醒信息
                this.addReminderInfo();
            }

            addExtendedTimeInfo() {
                const { record, model } = this.props;
                const { fieldMapping } = model.meta;

                const startField = fieldMapping.date_start;
                const endField = fieldMapping.date_stop || fieldMapping.date_start;

                const start = record.data[startField];
                const end = record.data[endField];

                if (start && end) {
                    // 添加时区信息
                    this.timezone = start.zoneName;

                    // 添加相对时间
                    this.relativeTime = start.toRelative();

                    // 添加工作日信息
                    this.isWeekend = start.weekday > 5;

                    // 添加持续时间详情
                    const duration = end.diff(start);
                    this.durationDetails = {
                        days: Math.floor(duration.as('days')),
                        hours: Math.floor(duration.as('hours') % 24),
                        minutes: Math.floor(duration.as('minutes') % 60)
                    };
                }
            }

            addRecurrenceInfo() {
                const { record } = this.props;

                // 检查重复字段
                if (record.data.recurrence_id) {
                    this.isRecurring = true;
                    this.recurrenceInfo = {
                        id: record.data.recurrence_id,
                        rule: record.data.rrule,
                        count: record.data.count
                    };
                } else {
                    this.isRecurring = false;
                }
            }

            addReminderInfo() {
                const { record } = this.props;

                // 检查提醒字段
                if (record.data.alarm_ids && record.data.alarm_ids.length > 0) {
                    this.hasReminders = true;
                    this.reminderCount = record.data.alarm_ids.length;
                } else {
                    this.hasReminders = false;
                }
            }

            // 添加操作方法
            onEdit() {
                if (this.props.editRecord) {
                    this.props.editRecord(this.props.record);
                }
            }

            onDelete() {
                this.confirmDelete();
            }

            onDuplicate() {
                if (this.props.createRecord) {
                    const duplicateData = { ...this.props.record.data };
                    delete duplicateData.id;
                    this.props.createRecord(duplicateData);
                }
            }

            // 清理资源
            destroy() {
                if (this.autoCloseTimer) {
                    clearTimeout(this.autoCloseTimer);
                }

                super.destroy();
            }
        };
    }

    // 设置全局事件监听
    setupGlobalEventListeners() {
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            this.adjustAllPopovers();
        });

        // 监听滚动事件
        window.addEventListener('scroll', () => {
            this.adjustAllPopovers();
        });
    }

    // 调整所有弹出框
    adjustAllPopovers() {
        for (const [id, popover] of this.popoverInstances) {
            if (popover.adjustPosition) {
                popover.adjustPosition();
            }
        }
    }

    // 设置键盘导航
    setupKeyboardNavigation() {
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape') {
                this.closeAllPopovers();
            }
        });
    }

    // 关闭所有弹出框
    closeAllPopovers() {
        for (const [id, popover] of this.popoverInstances) {
            if (popover.props.close) {
                popover.props.close();
            }
        }
    }

    // 获取弹出框统计
    getPopoverStatistics() {
        return {
            ...this.popoverStatistics,
            templateCount: this.popoverTemplates.size,
            activeInstances: this.popoverInstances.size
        };
    }

    // 销毁管理器
    destroy() {
        // 关闭所有弹出框
        this.closeAllPopovers();

        // 清理实例
        this.popoverInstances.clear();

        // 清理模板
        this.popoverTemplates.clear();

        // 移除事件监听器
        window.removeEventListener('resize', this.adjustAllPopovers);
        window.removeEventListener('scroll', this.adjustAllPopovers);
        document.removeEventListener('keydown', this.setupKeyboardNavigation);
    }
}

// 使用示例
const popoverManager = new PopoverManager();

// 创建增强的弹出框
const EnhancedPopover = popoverManager.createEnhancedPopover();

// 获取统计信息
const stats = popoverManager.getPopoverStatistics();
console.log('Popover statistics:', stats);
```

## 技术特点

### 1. 组件化设计
- **模块化**: 高度模块化的组件设计
- **可复用**: 可在不同日历视图中复用
- **可扩展**: 支持功能扩展和定制
- **标准化**: 符合OWL组件标准

### 2. 模板系统
- **分层模板**: 主模板和子模板的分层设计
- **灵活布局**: 灵活的布局和样式控制
- **内容分离**: 内容和样式的分离
- **可定制**: 支持模板的定制和替换

### 3. 交互体验
- **事件处理**: 完善的事件处理机制
- **键盘支持**: 支持键盘导航和操作
- **可访问性**: 良好的可访问性支持
- **动画效果**: 平滑的动画过渡效果

### 4. 数据处理
- **时间计算**: 智能的时间和持续时间计算
- **格式化**: 本地化的日期时间格式化
- **字段渲染**: 动态的字段渲染
- **数据绑定**: 响应式的数据绑定

## 设计模式

### 1. 组件模式 (Component Pattern)
- **封装性**: 封装弹出框功能
- **可复用**: 高度可复用的组件
- **接口标准**: 标准化的组件接口

### 2. 模板模式 (Template Pattern)
- **结构定义**: 定义弹出框结构
- **内容填充**: 动态填充内容
- **样式控制**: 控制显示样式

### 3. 观察者模式 (Observer Pattern)
- **事件监听**: 监听用户交互事件
- **状态响应**: 响应数据状态变化
- **自动更新**: 自动更新显示内容

### 4. 策略模式 (Strategy Pattern)
- **显示策略**: 不同的内容显示策略
- **交互策略**: 不同的用户交互策略
- **格式化策略**: 不同的数据格式化策略

## 注意事项

1. **性能优化**: 避免频繁的DOM操作和重渲染
2. **内存管理**: 及时清理事件监听器和定时器
3. **可访问性**: 确保良好的可访问性支持
4. **响应式**: 适配不同屏幕尺寸和设备

## 扩展建议

1. **更多模板**: 支持更多类型的弹出框模板
2. **动画增强**: 增强动画效果和过渡
3. **主题支持**: 支持不同的视觉主题
4. **插件系统**: 支持插件扩展功能
5. **性能优化**: 优化渲染性能和内存使用

该通用弹出框组件为Odoo Web客户端提供了强大的日历事件展示功能，通过模块化设计、模板系统和良好的交互体验确保了用户友好的事件详情查看和操作界面。
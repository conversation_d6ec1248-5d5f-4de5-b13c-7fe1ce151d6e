# Calendar Common Components - 日历通用组件

## 概述

Calendar Common Components 是 Odoo Web 客户端日历视图系统的通用组件模块，提供了在不同日历视图中共享使用的核心组件。该模块包含3个主要组件，总计549行代码，专门用于实现日历视图的通用功能，具备弹出框管理、事件渲染、周列显示等特性，是日历视图系统中可复用组件的核心集合。

## 模块结构

```
calendar_common/
├── calendar_common_popover.js      # 通用弹出框组件 (124行)
├── calendar_common_renderer.js     # 通用渲染器组件 (403行)
└── calendar_common_week_column.js  # 周列组件 (22行)
```

## 组件详解

### 1. CalendarCommonPopover - 通用弹出框

**文件**: `calendar_common_popover.js` (124行)

**功能概述**:
- 显示日历事件的详细信息
- 提供事件的快速操作功能
- 支持字段动态渲染
- 集成时间格式化功能

**核心特性**:
- **模板系统**: 使用主模板和子模板的分层设计
- **组件集成**: 集成Dialog、Field、Record等子组件
- **时间计算**: 智能的日期时间和持续时间计算
- **操作支持**: 支持创建、编辑、删除等CRUD操作

**主要方法**:
```javascript
// 计算日期时间和持续时间
computeDateTimeAndDuration()

// 获取格式化的时间信息
getFormattedTimeInfo()

// 处理事件操作
handleEventOperations()
```

**使用场景**:
- 日历事件详情展示
- 快速事件操作
- 事件信息预览
- 移动端事件查看

### 2. CalendarCommonRenderer - 通用渲染器

**文件**: `calendar_common_renderer.js` (403行)

**功能概述**:
- 集成FullCalendar库进行日历渲染
- 处理事件数据的转换和显示
- 管理用户交互和事件处理
- 提供本地化和配置支持

**核心特性**:
- **FullCalendar集成**: 无缝集成FullCalendar库
- **事件处理**: 完善的事件点击、拖拽、调整处理
- **数据转换**: Odoo记录到FullCalendar事件的转换
- **配置管理**: 统一的FullCalendar配置管理

**主要配置**:
```javascript
// 刻度到FullCalendar视图映射
SCALE_TO_FC_VIEW = {
    day: "timeGridDay",
    week: "timeGridWeek", 
    month: "dayGridMonth"
}

// 小时格式配置
HOUR_FORMATS = {
    12: { hour: "numeric", minute: "2-digit", meridiem: "short" },
    24: { hour: "numeric", minute: "2-digit", hour12: false }
}
```

**事件处理**:
- **事件点击**: 显示事件详情弹出框
- **事件拖拽**: 更新事件时间
- **事件调整**: 修改事件持续时间
- **日期选择**: 创建新事件

**使用场景**:
- 日、周、月视图渲染
- 事件交互处理
- FullCalendar配置
- 本地化支持

### 3. CalendarCommonWeekColumn - 周列组件

**文件**: `calendar_common_week_column.js` (22行)

**功能概述**:
- 在日历视图中添加周数列显示
- 动态创建和插入周列元素
- 应用适当的样式和格式
- 与FullCalendar无缝集成

**核心特性**:
- **轻量级**: 仅22行代码的轻量级实现
- **DOM操作**: 精确的DOM元素操作
- **样式应用**: 自动应用CSS类名和样式
- **FullCalendar集成**: 与FullCalendar结构完美集成

**主要函数**:
```javascript
// 创建周列
makeWeekColumn({ el, showWeek, weekColumn, weekText })
```

**处理流程**:
1. 查找FullCalendar的第一列元素
2. 创建新的周列元素
3. 应用适当的CSS类名
4. 设置周数显示内容
5. 插入到正确的位置

**使用场景**:
- 周视图周数显示
- 月视图周数标识
- 自定义日历布局
- 国际化周数支持

## 技术架构

### 1. 组件层次结构

```
CalendarCommonComponents
├── Popover Layer (弹出框层)
│   ├── Event Details Display
│   ├── Field Rendering
│   ├── Time Formatting
│   └── Action Buttons
├── Renderer Layer (渲染层)
│   ├── FullCalendar Integration
│   ├── Event Data Processing
│   ├── User Interaction Handling
│   └── Localization Support
└── Utility Layer (工具层)
    ├── DOM Manipulation
    ├── Style Application
    ├── Element Creation
    └── Layout Enhancement
```

### 2. 数据流

```
用户交互 → CommonRenderer → FullCalendar
    ↓              ↓
弹出框显示 ← CommonPopover ← 事件数据
    ↓
操作执行 → 数据更新 → 视图刷新
```

### 3. 组件协作

```
CommonRenderer (主渲染器)
    ↓ 调用
CommonPopover (弹出框显示)
    ↓ 使用
WeekColumn (周列增强)
    ↓ 集成
FullCalendar (第三方库)
```

## 设计模式

### 1. 组件模式 (Component Pattern)
- **封装性**: 每个组件封装特定功能
- **可复用**: 组件可在不同视图中复用
- **组合性**: 组件可以组合使用

### 2. 适配器模式 (Adapter Pattern)
- **库集成**: 适配FullCalendar库到Odoo系统
- **数据转换**: 转换Odoo数据格式到FullCalendar格式
- **接口统一**: 提供统一的组件接口

### 3. 装饰器模式 (Decorator Pattern)
- **功能增强**: 增强FullCalendar的基础功能
- **样式装饰**: 添加自定义样式和布局
- **行为扩展**: 扩展用户交互行为

### 4. 工厂模式 (Factory Pattern)
- **组件创建**: 动态创建组件实例
- **配置生成**: 生成不同的配置对象
- **元素工厂**: 创建DOM元素

### 5. 观察者模式 (Observer Pattern)
- **事件监听**: 监听用户交互事件
- **状态同步**: 同步组件状态
- **数据响应**: 响应数据变化

## 核心功能

### 1. 事件管理
- **事件显示**: 在日历中显示事件
- **事件交互**: 处理事件的点击、拖拽等操作
- **事件编辑**: 支持事件的快速编辑
- **事件创建**: 支持新事件的创建

### 2. 时间处理
- **时间格式化**: 本地化的时间格式化
- **时区支持**: 完善的时区处理
- **持续时间**: 计算和显示事件持续时间
- **日期范围**: 处理事件的日期范围

### 3. 用户界面
- **响应式设计**: 适配不同屏幕尺寸
- **交互反馈**: 提供清晰的用户反馈
- **可访问性**: 良好的可访问性支持
- **主题支持**: 支持不同的视觉主题

### 4. 性能优化
- **懒加载**: 按需加载组件和数据
- **缓存机制**: 智能的数据缓存
- **虚拟化**: 大数据量的虚拟化处理
- **防抖节流**: 用户交互的性能优化

## 扩展开发

### 1. 自定义弹出框
```javascript
class CustomPopover extends CalendarCommonPopover {
    // 自定义弹出框逻辑
    customMethod() {
        // 实现自定义功能
    }
}
```

### 2. 自定义渲染器
```javascript
class CustomRenderer extends CalendarCommonRenderer {
    // 重写渲染方法
    getFullCalendarOptions() {
        const options = super.getFullCalendarOptions();
        // 添加自定义配置
        return { ...options, customOption: true };
    }
}
```

### 3. 自定义周列
```javascript
function makeCustomWeekColumn(options) {
    // 调用原始函数
    makeWeekColumn(options);
    
    // 添加自定义功能
    addCustomWeekFeatures(options.el);
}
```

## 配置选项

### 1. 弹出框配置
```javascript
popoverConfig = {
    position: 'right',
    enableAnimation: true,
    enableKeyboard: true,
    autoClose: true
}
```

### 2. 渲染器配置
```javascript
rendererConfig = {
    initialView: 'dayGridMonth',
    headerToolbar: false,
    height: 'auto',
    eventLimit: 5
}
```

### 3. 周列配置
```javascript
weekColumnConfig = {
    showWeek: true,
    weekText: 'Week',
    weekFormat: 'W'
}
```

## 最佳实践

### 1. 组件使用
- 优先使用通用组件而不是重复实现
- 通过配置而不是修改源码来定制功能
- 保持组件的单一职责原则
- 合理使用组件的生命周期

### 2. 性能优化
- 避免频繁的DOM操作
- 使用事件委托处理大量事件
- 合理使用缓存机制
- 及时清理事件监听器

### 3. 代码维护
- 保持代码的可读性和可维护性
- 添加适当的注释和文档
- 遵循统一的代码风格
- 编写单元测试

### 4. 错误处理
- 添加完善的错误处理机制
- 提供友好的错误提示
- 记录和报告错误信息
- 实现错误恢复机制

## 兼容性

### 1. 浏览器支持
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### 2. 移动端支持
- iOS Safari 12+
- Android Chrome 60+
- 响应式设计
- 触摸优化

### 3. 框架兼容
- OWL 2.0+
- FullCalendar 5.0+
- Luxon 2.0+
- 现代ES6+语法

## 总结

Calendar Common Components 提供了日历视图系统的核心通用组件，通过高度的模块化和可复用性设计，为不同的日历视图提供了统一的基础功能。这些组件具有良好的扩展性、性能和用户体验，是构建复杂日历应用的重要基础。

该模块的设计充分考虑了现代Web应用的需求，包括响应式设计、性能优化、可访问性等方面，为开发者提供了强大而灵活的日历组件工具集。

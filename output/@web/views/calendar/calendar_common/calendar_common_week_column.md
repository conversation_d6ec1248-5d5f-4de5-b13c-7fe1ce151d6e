# CalendarCommonWeekColumn - 日历通用周列

## 概述

`calendar_common_week_column.js` 是 Odoo Web 客户端日历视图的周列工具模块，提供了在日历视图中添加周列显示的功能。该模块包含22行代码，是一个轻量级的工具函数，专门用于在FullCalendar的日历视图中动态添加周数列，具备DOM操作、元素创建、样式应用、周数显示等特性，是日历视图系统中周数显示的核心工具。

## 文件信息
- **路径**: `/web/static/src/views/calendar/calendar_common/calendar_common_week_column.js`
- **行数**: 22
- **模块**: `@web/views/calendar/calendar_common/calendar_common_week_column`

## 依赖关系

```javascript
// 无外部依赖
// 纯JavaScript DOM操作实现
```

## 主要函数

### 1. makeWeekColumn - 创建周列

```javascript
function makeWeekColumn({ el, showWeek, weekColumn, weekText }) {
    const firstRows = el.querySelectorAll(".fc-col-header-cell:nth-child(1), .fc-day:nth-child(1)");
    for (const element of firstRows) {
        const newElement = document.createElement("th");
        if (element.classList.contains("fc-col-header-cell")) {
            newElement.classList.add("o-fc-week-header");
            newElement.innerText = weekText;
        } else {
            newElement.classList.add("o-fc-week");
            const weekElement = element.querySelector(".fc-daygrid-week-number");
            weekElement.classList.remove("fc-daygrid-week-number");
            newElement.append(weekElement);
        }
        element.parentElement.insertBefore(newElement, element);
    }
}
```

**函数功能**:
- **DOM查询**: 查找FullCalendar的第一列元素
- **元素创建**: 创建新的周列元素
- **样式应用**: 应用适当的CSS类名
- **内容设置**: 设置周数显示内容

## 核心功能

### 1. 参数接口

```javascript
{ el, showWeek, weekColumn, weekText }
```

**参数功能**:
- **el**: 日历容器元素
- **showWeek**: 是否显示周数的标志
- **weekColumn**: 周列配置对象
- **weekText**: 周列头部文本

### 2. 元素选择

```javascript
const firstRows = el.querySelectorAll(".fc-col-header-cell:nth-child(1), .fc-day:nth-child(1)");
```

**选择功能**:
- **头部单元格**: 选择FullCalendar头部的第一列
- **日期单元格**: 选择日期行的第一列
- **CSS选择器**: 使用nth-child选择器精确定位
- **批量选择**: 一次性选择所有需要处理的元素

### 3. 头部处理

```javascript
if (element.classList.contains("fc-col-header-cell")) {
    newElement.classList.add("o-fc-week-header");
    newElement.innerText = weekText;
}
```

**头部处理功能**:
- **类型检测**: 检测是否为头部单元格
- **样式应用**: 添加周列头部样式类
- **文本设置**: 设置头部显示文本
- **标识区分**: 区分头部和内容区域

### 4. 内容处理

```javascript
else {
    newElement.classList.add("o-fc-week");
    const weekElement = element.querySelector(".fc-daygrid-week-number");
    weekElement.classList.remove("fc-daygrid-week-number");
    newElement.append(weekElement);
}
```

**内容处理功能**:
- **样式应用**: 添加周列内容样式类
- **元素查找**: 查找FullCalendar的周数元素
- **样式移除**: 移除原有的FullCalendar样式
- **元素移动**: 将周数元素移动到新列中

### 5. 元素插入

```javascript
element.parentElement.insertBefore(newElement, element);
```

**插入功能**:
- **位置插入**: 在第一列之前插入新列
- **DOM操作**: 使用insertBefore进行DOM操作
- **结构保持**: 保持原有的表格结构
- **顺序控制**: 确保周列在最左侧

## 使用场景

### 1. 周列管理器

```javascript
// 周列管理器
class WeekColumnManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置周列配置
        this.weekColumnConfig = {
            enableWeekColumn: true,
            enableWeekHeader: true,
            enableCustomWeekText: true,
            enableResponsiveWeekColumn: true,
            weekHeaderText: 'Week',
            weekColumnWidth: '60px',
            weekNumberFormat: 'W'
        };
        
        // 设置周列实例
        this.weekColumnInstances = new Map();
        
        // 设置周列样式
        this.weekColumnStyles = new Map();
        
        // 设置周列统计
        this.weekColumnStatistics = {
            totalColumns: 0,
            activeColumns: 0,
            createdColumns: 0,
            removedColumns: 0
        };
        
        this.initializeWeekColumnSystem();
    }
    
    // 初始化周列系统
    initializeWeekColumnSystem() {
        // 扩展原始函数
        this.enhanceMakeWeekColumn();
        
        // 设置样式管理
        this.setupStyleManagement();
        
        // 设置响应式支持
        this.setupResponsiveSupport();
        
        // 设置国际化支持
        this.setupInternationalizationSupport();
    }
    
    // 增强makeWeekColumn函数
    enhanceMakeWeekColumn() {
        const originalMakeWeekColumn = makeWeekColumn;
        
        // 创建增强版本
        this.makeWeekColumn = (options = {}) => {
            const config = {
                enableValidation: options.enableValidation !== false,
                enableStyling: options.enableStyling !== false,
                enableResponsive: options.enableResponsive !== false,
                enableAnimation: options.enableAnimation !== false,
                ...this.weekColumnConfig,
                ...options
            };
            
            // 验证参数
            if (config.enableValidation) {
                const validation = this.validateWeekColumnOptions(config);
                if (!validation.isValid) {
                    throw new Error(`Invalid week column options: ${validation.error}`);
                }
            }
            
            // 统计
            this.weekColumnStatistics.totalColumns++;
            this.weekColumnStatistics.createdColumns++;
            
            try {
                // 执行原始函数
                originalMakeWeekColumn(config);
                
                // 应用增强功能
                this.applyEnhancements(config);
                
                // 记录实例
                const instanceId = this.generateInstanceId();
                this.weekColumnInstances.set(instanceId, {
                    id: instanceId,
                    config: config,
                    element: config.el,
                    createdAt: Date.now(),
                    status: 'active'
                });
                
                this.weekColumnStatistics.activeColumns++;
                
                return instanceId;
                
            } catch (error) {
                this.handleWeekColumnError(error, config);
                throw error;
            }
        };
    }
    
    // 验证周列选项
    validateWeekColumnOptions(options) {
        const errors = [];
        
        // 验证必需参数
        if (!options.el) {
            errors.push('Element (el) is required');
        }
        
        if (options.el && !(options.el instanceof Element)) {
            errors.push('Element (el) must be a DOM element');
        }
        
        // 验证weekText
        if (options.weekText && typeof options.weekText !== 'string') {
            errors.push('weekText must be a string');
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
    
    // 应用增强功能
    applyEnhancements(config) {
        // 应用自定义样式
        if (config.enableStyling) {
            this.applyCustomStyling(config);
        }
        
        // 应用响应式支持
        if (config.enableResponsive) {
            this.applyResponsiveSupport(config);
        }
        
        // 应用动画效果
        if (config.enableAnimation) {
            this.applyAnimationEffects(config);
        }
        
        // 应用可访问性支持
        this.applyAccessibilitySupport(config);
    }
    
    // 应用自定义样式
    applyCustomStyling(config) {
        const weekColumns = config.el.querySelectorAll('.o-fc-week, .o-fc-week-header');
        
        for (const column of weekColumns) {
            // 设置宽度
            if (config.weekColumnWidth) {
                column.style.width = config.weekColumnWidth;
                column.style.minWidth = config.weekColumnWidth;
            }
            
            // 设置对齐
            column.style.textAlign = 'center';
            column.style.verticalAlign = 'middle';
            
            // 设置边框
            column.style.borderRight = '1px solid #dee2e6';
            
            // 设置背景色
            if (column.classList.contains('o-fc-week-header')) {
                column.style.backgroundColor = '#f8f9fa';
                column.style.fontWeight = 'bold';
            } else {
                column.style.backgroundColor = '#ffffff';
            }
        }
    }
    
    // 应用响应式支持
    applyResponsiveSupport(config) {
        const weekColumns = config.el.querySelectorAll('.o-fc-week, .o-fc-week-header');
        
        // 创建响应式处理器
        const handleResize = () => {
            const containerWidth = config.el.offsetWidth;
            
            for (const column of weekColumns) {
                if (containerWidth < 768) {
                    // 移动端：隐藏或缩小周列
                    column.style.display = config.hideOnMobile ? 'none' : 'table-cell';
                    column.style.width = config.hideOnMobile ? '0' : '40px';
                } else if (containerWidth < 1024) {
                    // 平板端：正常显示
                    column.style.display = 'table-cell';
                    column.style.width = '50px';
                } else {
                    // 桌面端：完整显示
                    column.style.display = 'table-cell';
                    column.style.width = config.weekColumnWidth || '60px';
                }
            }
        };
        
        // 初始调用
        handleResize();
        
        // 监听窗口大小变化
        window.addEventListener('resize', handleResize);
        
        // 保存处理器以便清理
        config._resizeHandler = handleResize;
    }
    
    // 应用动画效果
    applyAnimationEffects(config) {
        const weekColumns = config.el.querySelectorAll('.o-fc-week, .o-fc-week-header');
        
        for (const column of weekColumns) {
            // 设置初始状态
            column.style.opacity = '0';
            column.style.transform = 'translateX(-20px)';
            column.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            
            // 延迟显示动画
            setTimeout(() => {
                column.style.opacity = '1';
                column.style.transform = 'translateX(0)';
            }, 100);
        }
    }
    
    // 应用可访问性支持
    applyAccessibilitySupport(config) {
        const weekColumns = config.el.querySelectorAll('.o-fc-week, .o-fc-week-header');
        
        for (const column of weekColumns) {
            // 设置ARIA属性
            if (column.classList.contains('o-fc-week-header')) {
                column.setAttribute('role', 'columnheader');
                column.setAttribute('scope', 'col');
                column.setAttribute('aria-label', config.weekText || 'Week');
            } else {
                column.setAttribute('role', 'gridcell');
                
                // 获取周数
                const weekNumber = column.textContent.trim();
                if (weekNumber) {
                    column.setAttribute('aria-label', `Week ${weekNumber}`);
                }
            }
        }
    }
    
    // 设置样式管理
    setupStyleManagement() {
        // 创建样式表
        this.createStyleSheet();
        
        // 注册默认样式
        this.registerDefaultStyles();
    }
    
    // 创建样式表
    createStyleSheet() {
        const styleId = 'week-column-styles';
        
        if (!document.getElementById(styleId)) {
            const style = document.createElement('style');
            style.id = styleId;
            style.textContent = this.getDefaultCSS();
            document.head.appendChild(style);
        }
    }
    
    // 获取默认CSS
    getDefaultCSS() {
        return `
            .o-fc-week-header {
                background-color: #f8f9fa;
                font-weight: bold;
                text-align: center;
                vertical-align: middle;
                border-right: 1px solid #dee2e6;
                padding: 8px 4px;
                min-width: 60px;
            }
            
            .o-fc-week {
                background-color: #ffffff;
                text-align: center;
                vertical-align: middle;
                border-right: 1px solid #dee2e6;
                padding: 4px;
                min-width: 60px;
                font-size: 12px;
                color: #6c757d;
            }
            
            .o-fc-week:hover {
                background-color: #f8f9fa;
            }
            
            @media (max-width: 768px) {
                .o-fc-week-header,
                .o-fc-week {
                    min-width: 40px;
                    padding: 4px 2px;
                    font-size: 11px;
                }
            }
            
            @media (max-width: 480px) {
                .o-fc-week-header,
                .o-fc-week {
                    display: none;
                }
            }
        `;
    }
    
    // 注册默认样式
    registerDefaultStyles() {
        // 默认样式主题
        this.weekColumnStyles.set('default', {
            headerBackground: '#f8f9fa',
            headerColor: '#212529',
            cellBackground: '#ffffff',
            cellColor: '#6c757d',
            borderColor: '#dee2e6',
            hoverBackground: '#f8f9fa'
        });
        
        // 深色主题
        this.weekColumnStyles.set('dark', {
            headerBackground: '#343a40',
            headerColor: '#ffffff',
            cellBackground: '#495057',
            cellColor: '#adb5bd',
            borderColor: '#6c757d',
            hoverBackground: '#6c757d'
        });
        
        // 彩色主题
        this.weekColumnStyles.set('colorful', {
            headerBackground: '#007bff',
            headerColor: '#ffffff',
            cellBackground: '#e3f2fd',
            cellColor: '#1976d2',
            borderColor: '#2196f3',
            hoverBackground: '#bbdefb'
        });
    }
    
    // 设置响应式支持
    setupResponsiveSupport() {
        this.breakpoints = {
            mobile: 480,
            tablet: 768,
            desktop: 1024
        };
        
        this.currentBreakpoint = this.getCurrentBreakpoint();
        
        // 监听窗口大小变化
        window.addEventListener('resize', this.handleGlobalResize.bind(this));
    }
    
    // 获取当前断点
    getCurrentBreakpoint() {
        const width = window.innerWidth;
        
        if (width < this.breakpoints.mobile) {
            return 'mobile';
        } else if (width < this.breakpoints.tablet) {
            return 'tablet';
        } else {
            return 'desktop';
        }
    }
    
    // 处理全局大小变化
    handleGlobalResize() {
        const newBreakpoint = this.getCurrentBreakpoint();
        
        if (newBreakpoint !== this.currentBreakpoint) {
            this.currentBreakpoint = newBreakpoint;
            this.onBreakpointChange(newBreakpoint);
        }
    }
    
    // 断点变化处理
    onBreakpointChange(breakpoint) {
        // 更新所有活动的周列
        for (const [instanceId, instance] of this.weekColumnInstances) {
            if (instance.status === 'active') {
                this.updateWeekColumnForBreakpoint(instance, breakpoint);
            }
        }
    }
    
    // 为断点更新周列
    updateWeekColumnForBreakpoint(instance, breakpoint) {
        const weekColumns = instance.element.querySelectorAll('.o-fc-week, .o-fc-week-header');
        
        for (const column of weekColumns) {
            switch (breakpoint) {
                case 'mobile':
                    column.style.display = 'none';
                    break;
                case 'tablet':
                    column.style.display = 'table-cell';
                    column.style.width = '40px';
                    break;
                case 'desktop':
                    column.style.display = 'table-cell';
                    column.style.width = instance.config.weekColumnWidth || '60px';
                    break;
            }
        }
    }
    
    // 设置国际化支持
    setupInternationalizationSupport() {
        this.weekTexts = new Map([
            ['en', 'Week'],
            ['zh', '周'],
            ['es', 'Semana'],
            ['fr', 'Semaine'],
            ['de', 'Woche'],
            ['it', 'Settimana'],
            ['pt', 'Semana'],
            ['ru', 'Неделя'],
            ['ja', '週'],
            ['ko', '주']
        ]);
    }
    
    // 获取本地化周文本
    getLocalizedWeekText(locale = 'en') {
        return this.weekTexts.get(locale) || this.weekTexts.get('en');
    }
    
    // 移除周列
    removeWeekColumn(instanceId) {
        const instance = this.weekColumnInstances.get(instanceId);
        
        if (instance) {
            const weekColumns = instance.element.querySelectorAll('.o-fc-week, .o-fc-week-header');
            
            for (const column of weekColumns) {
                column.remove();
            }
            
            // 清理事件监听器
            if (instance.config._resizeHandler) {
                window.removeEventListener('resize', instance.config._resizeHandler);
            }
            
            // 更新实例状态
            instance.status = 'removed';
            this.weekColumnInstances.delete(instanceId);
            
            // 更新统计
            this.weekColumnStatistics.activeColumns--;
            this.weekColumnStatistics.removedColumns++;
        }
    }
    
    // 应用主题
    applyTheme(instanceId, themeName) {
        const instance = this.weekColumnInstances.get(instanceId);
        const theme = this.weekColumnStyles.get(themeName);
        
        if (instance && theme) {
            const weekColumns = instance.element.querySelectorAll('.o-fc-week, .o-fc-week-header');
            
            for (const column of weekColumns) {
                if (column.classList.contains('o-fc-week-header')) {
                    column.style.backgroundColor = theme.headerBackground;
                    column.style.color = theme.headerColor;
                } else {
                    column.style.backgroundColor = theme.cellBackground;
                    column.style.color = theme.cellColor;
                }
                
                column.style.borderColor = theme.borderColor;
            }
        }
    }
    
    // 处理周列错误
    handleWeekColumnError(error, config) {
        console.error('Week column error:', error);
        
        // 记录错误
        const errorRecord = {
            error: error.message,
            config: config,
            timestamp: Date.now()
        };
        
        // 可以在这里实现错误报告逻辑
        this.reportWeekColumnError(errorRecord);
    }
    
    // 报告周列错误
    reportWeekColumnError(errorRecord) {
        // 实现错误报告逻辑
        console.warn('Week column error reported:', errorRecord);
    }
    
    // 生成实例ID
    generateInstanceId() {
        return `week_column_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    // 获取周列统计
    getWeekColumnStatistics() {
        return {
            ...this.weekColumnStatistics,
            instanceCount: this.weekColumnInstances.size,
            styleCount: this.weekColumnStyles.size,
            currentBreakpoint: this.currentBreakpoint
        };
    }
    
    // 销毁管理器
    destroy() {
        // 移除所有周列
        for (const [instanceId] of this.weekColumnInstances) {
            this.removeWeekColumn(instanceId);
        }
        
        // 清理样式
        this.weekColumnStyles.clear();
        
        // 移除事件监听器
        window.removeEventListener('resize', this.handleGlobalResize);
        
        // 移除样式表
        const styleElement = document.getElementById('week-column-styles');
        if (styleElement) {
            styleElement.remove();
        }
        
        // 重置统计
        this.weekColumnStatistics = {
            totalColumns: 0,
            activeColumns: 0,
            createdColumns: 0,
            removedColumns: 0
        };
    }
}

// 使用示例
const weekColumnManager = new WeekColumnManager();

// 创建周列
const instanceId = weekColumnManager.makeWeekColumn({
    el: calendarElement,
    showWeek: true,
    weekText: weekColumnManager.getLocalizedWeekText('zh'),
    enableResponsive: true,
    enableAnimation: true
});

// 应用主题
weekColumnManager.applyTheme(instanceId, 'dark');

// 获取统计信息
const stats = weekColumnManager.getWeekColumnStatistics();
console.log('Week column statistics:', stats);
```

## 技术特点

### 1. 轻量级实现
- **简洁代码**: 仅22行代码实现核心功能
- **无依赖**: 不依赖任何外部库
- **纯DOM**: 使用纯JavaScript DOM操作
- **高效执行**: 最小的性能开销

### 2. FullCalendar集成
- **无缝集成**: 与FullCalendar无缝集成
- **样式兼容**: 兼容FullCalendar的样式系统
- **结构保持**: 保持原有的表格结构
- **功能增强**: 增强FullCalendar的功能

### 3. DOM操作
- **精确选择**: 使用CSS选择器精确选择元素
- **动态创建**: 动态创建新的DOM元素
- **样式应用**: 应用适当的CSS类名
- **结构插入**: 正确插入到DOM结构中

### 4. 灵活配置
- **参数化**: 支持参数化配置
- **可定制**: 支持自定义文本和样式
- **响应式**: 支持响应式设计
- **国际化**: 支持多语言文本

## 设计模式

### 1. 工具模式 (Utility Pattern)
- **纯函数**: 提供纯函数工具
- **无状态**: 无状态的工具函数
- **可复用**: 高度可复用的工具

### 2. 装饰器模式 (Decorator Pattern)
- **功能增强**: 增强FullCalendar的功能
- **非侵入**: 不修改原有代码
- **动态添加**: 动态添加新功能

### 3. 工厂模式 (Factory Pattern)
- **元素工厂**: 创建DOM元素
- **样式工厂**: 创建样式配置
- **配置工厂**: 创建配置对象

### 4. 策略模式 (Strategy Pattern)
- **处理策略**: 不同元素的处理策略
- **样式策略**: 不同的样式应用策略
- **响应式策略**: 不同设备的响应式策略

## 注意事项

1. **DOM结构**: 确保FullCalendar的DOM结构正确
2. **样式冲突**: 避免与FullCalendar样式冲突
3. **性能影响**: 避免频繁的DOM操作
4. **浏览器兼容**: 确保跨浏览器兼容性

## 扩展建议

1. **更多样式**: 支持更多的样式主题
2. **动画效果**: 添加动画过渡效果
3. **交互功能**: 添加周列的交互功能
4. **可访问性**: 增强可访问性支持
5. **性能优化**: 优化DOM操作性能

该周列工具为Odoo Web客户端提供了简洁高效的周数显示功能，通过轻量级实现、FullCalendar集成和灵活配置确保了日历视图中周数显示的准确性和美观性。

# CalendarCommonRenderer - 日历通用渲染器

## 概述

`calendar_common_renderer.js` 是 Odoo Web 客户端日历视图的通用渲染器组件，提供了日、周、月视图的渲染功能。该模块包含403行代码，是一个OWL组件，专门用于集成FullCalendar库并提供日历事件的渲染、交互和管理功能，具备FullCalendar集成、事件渲染、弹出框管理、拖拽支持等特性，是日历视图系统中渲染层的核心实现。

## 文件信息
- **路径**: `/web/static/src/views/calendar/calendar_common/calendar_common_renderer.js`
- **行数**: 403
- **模块**: `@web/views/calendar/calendar_common/calendar_common_renderer`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/dates'                                              // 日期本地化
'@web/core/l10n/localization'                                       // 本地化服务
'@web/core/utils/render'                                            // 渲染工具
'@web/views/calendar/colors'                                        // 颜色管理
'@web/views/calendar/hooks'                                         // 日历钩子
'@web/views/calendar/calendar_common/calendar_common_popover'       // 通用弹出框
'@web/views/calendar/calendar_common/calendar_common_week_column'   // 周列组件
'@odoo/owl'                                                         // OWL框架
'@web/core/utils/hooks'                                             // 工具钩子
```

## 核心常量

### 1. 刻度到FullCalendar视图映射

```javascript
const SCALE_TO_FC_VIEW = {
    day: "timeGridDay",
    week: "timeGridWeek",
    month: "dayGridMonth",
};
```

### 2. 刻度到头部格式映射

```javascript
const SCALE_TO_HEADER_FORMAT = {
    day: "DDD",
    week: "EEE d",
    month: "EEEE",
};

const SHORT_SCALE_TO_HEADER_FORMAT = {
    ...SCALE_TO_HEADER_FORMAT,
    day: "D",
    month: "EEE",
};
```

### 3. 小时格式配置

```javascript
const HOUR_FORMATS = {
    12: {
        hour: "numeric",
        minute: "2-digit",
        omitZeroMinute: true,
        meridiem: "short",
    },
    24: {
        hour: "numeric",
        minute: "2-digit",
        hour12: false,
    },
};
```

## 主要组件定义

### 1. CalendarCommonRenderer - 日历通用渲染器

```javascript
class CalendarCommonRenderer extends Component {
    static template = "web.CalendarCommonRenderer";
    static components = {
        CalendarCommonPopover,
    };
    static props = {
        model: Object,
        isWeekendVisible: Boolean,
        createRecord: Function,
        editRecord: Function,
        deleteRecord: Function,
        setDate: Function,
    };

    setup() {
        this.popover = useCalendarPopover(CalendarCommonPopover);
        this.clickHandler = useClickHandler();
        this.fullCalendar = useFullCalendar();

        useBus(this.props.model, "update", this.render);

        this.state = useState({
            highlightedDate: null,
        });
    }
}
```

**组件特性**:
- **模板定义**: 使用CalendarCommonRenderer模板
- **子组件**: 集成弹出框组件
- **钩子集成**: 集成多个专门的钩子
- **状态管理**: 管理高亮日期状态

## 核心功能

### 1. FullCalendar配置

```javascript
get fullCalendarOptions() {
    const { model } = this.props;
    const { scale, date } = model;

    return {
        initialView: SCALE_TO_FC_VIEW[scale],
        initialDate: date.toJSDate(),
        headerToolbar: false,
        height: "auto",
        dayMaxEvents: model.meta.eventLimit,
        weekNumbers: getLocalWeekNumber() !== null,
        weekNumberFormat: { week: "numeric" },
        firstDay: localization.weekStart,
        locale: localization.code,
        timeZone: "local",
        slotMinTime: "00:00:00",
        slotMaxTime: "24:00:00",
        allDaySlot: true,
        eventDisplay: "block",
        dayHeaderFormat: this.getDayHeaderFormat(),
        slotLabelFormat: this.getSlotLabelFormat(),
        events: this.getEvents(),
        eventClick: this.onEventClick.bind(this),
        eventDrop: this.onEventDrop.bind(this),
        eventResize: this.onEventResize.bind(this),
        select: this.onSelect.bind(this),
        dateClick: this.onDateClick.bind(this),
        datesSet: this.onDatesSet.bind(this),
        eventDidMount: this.onEventDidMount.bind(this),
        dayCellDidMount: this.onDayCellDidMount.bind(this),
        selectable: model.meta.canCreate,
        editable: model.meta.canEdit,
        eventStartEditable: model.meta.canEdit,
        eventDurationEditable: model.meta.canEdit,
    };
}
```

**配置功能**:
- **视图映射**: 将Odoo刻度映射到FullCalendar视图
- **本地化**: 应用本地化设置
- **事件处理**: 配置各种事件处理器
- **权限控制**: 根据权限配置可编辑性

### 2. 事件数据处理

```javascript
getEvents() {
    const { model } = this.props;
    const events = [];

    for (const record of Object.values(model.data.records)) {
        const event = this.recordToEvent(record);
        if (event) {
            events.push(event);
        }
    }

    return events;
}

recordToEvent(record) {
    const { model } = this.props;
    const { fieldMapping } = model.meta;

    const startField = fieldMapping.date_start;
    const endField = fieldMapping.date_stop || fieldMapping.date_start;
    const allDayField = fieldMapping.all_day;
    const colorField = fieldMapping.color;

    const start = record[startField];
    const end = record[endField];

    if (!start) {
        return null;
    }

    const isAllDay = allDayField ? record[allDayField] : false;
    const colorValue = colorField ? record[colorField] : null;

    return {
        id: record.id,
        title: record.display_name || record.name,
        start: start.toJSDate(),
        end: end ? end.toJSDate() : start.toJSDate(),
        allDay: isAllDay,
        backgroundColor: getColor(colorValue),
        borderColor: getColor(colorValue),
        extendedProps: {
            record: record,
        },
    };
}
```

**事件处理功能**:
- **数据转换**: 将Odoo记录转换为FullCalendar事件
- **字段映射**: 使用模型的字段映射
- **颜色处理**: 应用事件颜色
- **扩展属性**: 保存原始记录数据

### 3. 事件交互处理

```javascript
onEventClick(info) {
    const { record } = info.event.extendedProps;

    this.popover.open(info.el, {
        record: record,
        model: this.props.model,
        createRecord: this.props.createRecord,
        editRecord: this.props.editRecord,
        deleteRecord: this.props.deleteRecord,
    });
}

onEventDrop(info) {
    const { record } = info.event.extendedProps;
    const { model } = this.props;
    const { fieldMapping } = model.meta;

    const startField = fieldMapping.date_start;
    const endField = fieldMapping.date_stop || fieldMapping.date_start;

    const newStart = DateTime.fromJSDate(info.event.start);
    const newEnd = info.event.end ? DateTime.fromJSDate(info.event.end) : newStart;

    const updateData = {
        [startField]: newStart,
        [endField]: newEnd,
    };

    this.props.editRecord(record, updateData);
}

onEventResize(info) {
    const { record } = info.event.extendedProps;
    const { model } = this.props;
    const { fieldMapping } = model.meta;

    const endField = fieldMapping.date_stop || fieldMapping.date_start;
    const newEnd = DateTime.fromJSDate(info.event.end);

    const updateData = {
        [endField]: newEnd,
    };

    this.props.editRecord(record, updateData);
}
```

**交互处理功能**:
- **点击处理**: 处理事件点击显示弹出框
- **拖拽处理**: 处理事件拖拽移动
- **调整处理**: 处理事件大小调整
- **数据更新**: 自动更新记录数据
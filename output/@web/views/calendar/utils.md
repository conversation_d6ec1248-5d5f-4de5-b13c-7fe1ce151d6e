# Utils - 日历工具函数

## 概述

`utils.js` 是 Odoo Web 客户端日历视图的工具函数模块，提供了日历相关的实用工具函数。该模块包含18行代码，是一个轻量级的工具模块，专门用于处理日历中的常见操作，具备日期格式化、时间范围处理、本地化支持等特性，是日历视图系统中工具函数的核心集合。

## 文件信息
- **路径**: `/web/static/src/views/calendar/utils.js`
- **行数**: 18
- **模块**: `@web/views/calendar/utils`

## 依赖关系

```javascript
// 无外部依赖
// 纯JavaScript实现的工具函数
```

## 主要函数

### 1. getFormattedDateSpan - 格式化日期范围

```javascript
function getFormattedDateSpan(start, end) {
    const isSameDay = start.hasSame(end, "days");

    if (!isSameDay && start.hasSame(end, "month")) {
        // Simplify date-range if an event occurs into the same month (eg. "August 4-5, 2019")
        return start.toFormat("LLLL d") + "-" + end.toFormat("d, y");
    } else {
        return isSameDay
            ? start.toFormat("DDD")
            : start.toFormat("DDD") + " - " + end.toFormat("DDD");
    }
}
```

**函数功能**:
- **日期范围格式化**: 智能格式化开始和结束日期
- **同日处理**: 特殊处理同一天的事件
- **同月优化**: 优化同月不同日的显示格式
- **本地化支持**: 使用本地化的日期格式

## 核心功能

### 1. 同日检测

```javascript
const isSameDay = start.hasSame(end, "days");
```

**检测功能**:
- **日期比较**: 比较开始和结束日期是否为同一天
- **精确匹配**: 使用"days"单位进行精确匹配
- **布尔结果**: 返回布尔值表示是否同日
- **后续处理**: 为后续格式化逻辑提供依据

### 2. 同月不同日处理

```javascript
if (!isSameDay && start.hasSame(end, "month")) {
    // Simplify date-range if an event occurs into the same month (eg. "August 4-5, 2019")
    return start.toFormat("LLLL d") + "-" + end.toFormat("d, y");
}
```

**同月处理功能**:
- **月份检测**: 检测是否为同一月份
- **简化格式**: 简化同月事件的日期显示
- **格式优化**: 避免重复显示月份和年份
- **示例格式**: "August 4-5, 2019"

### 3. 通用格式化

```javascript
return isSameDay
    ? start.toFormat("DDD")
    : start.toFormat("DDD") + " - " + end.toFormat("DDD");
```

**通用格式化功能**:
- **同日格式**: 同一天只显示一个日期
- **范围格式**: 不同日期显示完整范围
- **标准格式**: 使用DDD格式显示完整日期
- **分隔符**: 使用" - "分隔开始和结束日期

## 使用场景

### 1. 日历工具管理器

```javascript
// 日历工具管理器
class CalendarUtilsManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置工具配置
        this.utilsConfig = {
            enableLocalization: true,
            enableCustomFormats: true,
            enableCaching: true,
            enableValidation: true,
            defaultLocale: 'en-US',
            defaultTimezone: 'UTC'
        };
        
        // 设置格式化器
        this.formatters = new Map();
        
        // 设置缓存
        this.formatCache = new Map();
        
        // 设置本地化
        this.locales = new Map();
        
        // 设置统计
        this.utilsStatistics = {
            totalFormats: 0,
            cacheHits: 0,
            cacheMisses: 0,
            errors: 0
        };
        
        this.initializeUtils();
    }
    
    // 初始化工具
    initializeUtils() {
        // 扩展原始函数
        this.enhanceOriginalFunctions();
        
        // 设置格式化器
        this.setupFormatters();
        
        // 设置本地化
        this.setupLocalization();
        
        // 设置验证器
        this.setupValidators();
    }
    
    // 增强原始函数
    enhanceOriginalFunctions() {
        const originalGetFormattedDateSpan = getFormattedDateSpan;
        
        // 创建增强版本
        this.getFormattedDateSpan = (start, end, options = {}) => {
            const config = {
                enableCaching: options.enableCaching !== false,
                enableValidation: options.enableValidation !== false,
                locale: options.locale || this.utilsConfig.defaultLocale,
                timezone: options.timezone || this.utilsConfig.defaultTimezone,
                format: options.format || 'auto',
                ...options
            };
            
            // 统计
            this.utilsStatistics.totalFormats++;
            
            // 验证输入
            if (config.enableValidation) {
                const validation = this.validateDateInputs(start, end);
                if (!validation.isValid) {
                    this.utilsStatistics.errors++;
                    throw new Error(`Invalid date inputs: ${validation.error}`);
                }
            }
            
            // 检查缓存
            const cacheKey = this.generateCacheKey(start, end, config);
            if (config.enableCaching && this.formatCache.has(cacheKey)) {
                this.utilsStatistics.cacheHits++;
                return this.formatCache.get(cacheKey);
            }
            
            this.utilsStatistics.cacheMisses++;
            
            // 应用本地化
            const localizedStart = this.applyLocalization(start, config);
            const localizedEnd = this.applyLocalization(end, config);
            
            // 格式化
            let result;
            if (config.format === 'auto') {
                result = originalGetFormattedDateSpan(localizedStart, localizedEnd);
            } else {
                result = this.customFormat(localizedStart, localizedEnd, config);
            }
            
            // 缓存结果
            if (config.enableCaching) {
                this.formatCache.set(cacheKey, result);
            }
            
            return result;
        };
    }
    
    // 生成缓存键
    generateCacheKey(start, end, config) {
        return `${start.toISO()}_${end.toISO()}_${config.locale}_${config.format}`;
    }
    
    // 验证日期输入
    validateDateInputs(start, end) {
        if (!start || !end) {
            return { isValid: false, error: 'Start and end dates are required' };
        }
        
        if (!start.isValid || !end.isValid) {
            return { isValid: false, error: 'Invalid date objects' };
        }
        
        if (start > end) {
            return { isValid: false, error: 'Start date must be before or equal to end date' };
        }
        
        return { isValid: true };
    }
    
    // 应用本地化
    applyLocalization(date, config) {
        if (config.locale !== this.utilsConfig.defaultLocale) {
            return date.setLocale(config.locale);
        }
        
        if (config.timezone !== this.utilsConfig.defaultTimezone) {
            return date.setZone(config.timezone);
        }
        
        return date;
    }
    
    // 自定义格式化
    customFormat(start, end, config) {
        const formatter = this.formatters.get(config.format);
        
        if (!formatter) {
            throw new Error(`Unknown format: ${config.format}`);
        }
        
        return formatter(start, end, config);
    }
    
    // 设置格式化器
    setupFormatters() {
        // 短格式
        this.formatters.set('short', (start, end) => {
            const isSameDay = start.hasSame(end, "days");
            return isSameDay
                ? start.toFormat("M/d")
                : `${start.toFormat("M/d")} - ${end.toFormat("M/d")}`;
        });
        
        // 长格式
        this.formatters.set('long', (start, end) => {
            const isSameDay = start.hasSame(end, "days");
            return isSameDay
                ? start.toFormat("EEEE, LLLL d, y")
                : `${start.toFormat("EEEE, LLLL d, y")} - ${end.toFormat("EEEE, LLLL d, y")}`;
        });
        
        // 时间格式
        this.formatters.set('time', (start, end) => {
            const isSameDay = start.hasSame(end, "days");
            if (isSameDay) {
                return `${start.toFormat("DDD")} ${start.toFormat("t")} - ${end.toFormat("t")}`;
            } else {
                return `${start.toFormat("DDD t")} - ${end.toFormat("DDD t")}`;
            }
        });
        
        // ISO格式
        this.formatters.set('iso', (start, end) => {
            const isSameDay = start.hasSame(end, "days");
            return isSameDay
                ? start.toISODate()
                : `${start.toISODate()} - ${end.toISODate()}`;
        });
        
        // 相对格式
        this.formatters.set('relative', (start, end) => {
            const now = DateTime.now();
            const isSameDay = start.hasSame(end, "days");
            
            if (isSameDay) {
                if (start.hasSame(now, "days")) {
                    return "Today";
                } else if (start.hasSame(now.plus({ days: 1 }), "days")) {
                    return "Tomorrow";
                } else if (start.hasSame(now.minus({ days: 1 }), "days")) {
                    return "Yesterday";
                } else {
                    return start.toRelative();
                }
            } else {
                return `${start.toRelative()} - ${end.toRelative()}`;
            }
        });
    }
    
    // 设置本地化
    setupLocalization() {
        // 添加常用本地化
        this.locales.set('en-US', {
            name: 'English (US)',
            dateFormat: 'M/d/yyyy',
            timeFormat: 'h:mm a',
            firstDayOfWeek: 0
        });
        
        this.locales.set('zh-CN', {
            name: '中文 (简体)',
            dateFormat: 'yyyy/M/d',
            timeFormat: 'HH:mm',
            firstDayOfWeek: 1
        });
        
        this.locales.set('es-ES', {
            name: 'Español',
            dateFormat: 'd/M/yyyy',
            timeFormat: 'HH:mm',
            firstDayOfWeek: 1
        });
        
        this.locales.set('fr-FR', {
            name: 'Français',
            dateFormat: 'd/M/yyyy',
            timeFormat: 'HH:mm',
            firstDayOfWeek: 1
        });
    }
    
    // 设置验证器
    setupValidators() {
        this.validators = {
            dateRange: (start, end) => {
                return start <= end;
            },
            
            dateValid: (date) => {
                return date && date.isValid;
            },
            
            localeSupported: (locale) => {
                return this.locales.has(locale);
            },
            
            formatSupported: (format) => {
                return format === 'auto' || this.formatters.has(format);
            }
        };
    }
    
    // 添加工具函数
    addUtilityFunctions() {
        // 计算日期差
        this.getDateDifference = (start, end, unit = 'days') => {
            return end.diff(start, unit).get(unit);
        };
        
        // 获取日期范围内的所有日期
        this.getDateRange = (start, end) => {
            const dates = [];
            let current = start.startOf('day');
            const endDate = end.startOf('day');
            
            while (current <= endDate) {
                dates.push(current);
                current = current.plus({ days: 1 });
            }
            
            return dates;
        };
        
        // 检查日期是否在范围内
        this.isDateInRange = (date, start, end) => {
            return date >= start && date <= end;
        };
        
        // 获取周的开始和结束
        this.getWeekBounds = (date, firstDayOfWeek = 0) => {
            const start = date.startOf('week').plus({ days: firstDayOfWeek });
            const end = start.plus({ days: 6 }).endOf('day');
            return { start, end };
        };
        
        // 获取月的开始和结束
        this.getMonthBounds = (date) => {
            const start = date.startOf('month');
            const end = date.endOf('month');
            return { start, end };
        };
        
        // 格式化持续时间
        this.formatDuration = (start, end, options = {}) => {
            const duration = end.diff(start);
            const config = {
                units: options.units || ['days', 'hours', 'minutes'],
                format: options.format || 'long',
                ...options
            };
            
            if (config.format === 'short') {
                return duration.toFormat("d'd' h'h' m'm'");
            } else {
                return duration.toHuman({ listStyle: 'long' });
            }
        };
        
        // 获取时区信息
        this.getTimezoneInfo = (timezone) => {
            try {
                const now = DateTime.now().setZone(timezone);
                return {
                    name: timezone,
                    offset: now.offset,
                    offsetName: now.offsetNameShort,
                    isValid: true
                };
            } catch (error) {
                return {
                    name: timezone,
                    isValid: false,
                    error: error.message
                };
            }
        };
        
        // 转换时区
        this.convertTimezone = (date, fromZone, toZone) => {
            return date.setZone(fromZone).setZone(toZone);
        };
    }
    
    // 批量格式化
    batchFormat(dateRanges, options = {}) {
        const results = [];
        const errors = [];
        
        for (const [index, range] of dateRanges.entries()) {
            try {
                const result = this.getFormattedDateSpan(range.start, range.end, options);
                results.push({ index, range, result, success: true });
            } catch (error) {
                errors.push({ index, range, error, success: false });
            }
        }
        
        return {
            results: results,
            errors: errors,
            total: dateRanges.length,
            successful: results.length,
            failed: errors.length
        };
    }
    
    // 获取格式化预览
    getFormatPreview(start, end) {
        const formats = Array.from(this.formatters.keys()).concat(['auto']);
        const preview = {};
        
        for (const format of formats) {
            try {
                preview[format] = this.getFormattedDateSpan(start, end, { format });
            } catch (error) {
                preview[format] = `Error: ${error.message}`;
            }
        }
        
        return preview;
    }
    
    // 获取工具统计
    getUtilsStatistics() {
        return {
            ...this.utilsStatistics,
            cacheSize: this.formatCache.size,
            formattersCount: this.formatters.size,
            localesCount: this.locales.size,
            cacheHitRate: this.utilsStatistics.totalFormats > 0 
                ? (this.utilsStatistics.cacheHits / this.utilsStatistics.totalFormats * 100).toFixed(2) + '%'
                : '0%'
        };
    }
    
    // 清理缓存
    clearCache() {
        this.formatCache.clear();
    }
    
    // 添加自定义格式化器
    addFormatter(name, formatter) {
        this.formatters.set(name, formatter);
    }
    
    // 移除格式化器
    removeFormatter(name) {
        this.formatters.delete(name);
    }
    
    // 添加本地化
    addLocale(code, config) {
        this.locales.set(code, config);
    }
    
    // 销毁管理器
    destroy() {
        // 清理缓存
        this.clearCache();
        
        // 清理格式化器
        this.formatters.clear();
        
        // 清理本地化
        this.locales.clear();
        
        // 重置统计
        this.utilsStatistics = {
            totalFormats: 0,
            cacheHits: 0,
            cacheMisses: 0,
            errors: 0
        };
    }
}

// 使用示例
const utilsManager = new CalendarUtilsManager();

// 格式化日期范围
const formatted1 = utilsManager.getFormattedDateSpan(start, end, { format: 'short' });
const formatted2 = utilsManager.getFormattedDateSpan(start, end, { locale: 'zh-CN' });

// 批量格式化
const batchResult = utilsManager.batchFormat([
    { start: date1, end: date2 },
    { start: date3, end: date4 }
], { format: 'long' });

// 获取格式预览
const preview = utilsManager.getFormatPreview(start, end);

// 获取统计信息
const stats = utilsManager.getUtilsStatistics();
console.log('Utils statistics:', stats);
```

## 技术特点

### 1. 简洁高效
- **轻量级**: 仅18行代码实现核心功能
- **无依赖**: 不依赖任何外部库
- **高效算法**: 使用高效的日期处理算法
- **性能优化**: 最小的性能开销

### 2. 智能格式化
- **同日检测**: 智能检测同一天的事件
- **同月优化**: 优化同月不同日的显示
- **格式简化**: 避免重复信息的显示
- **本地化**: 支持本地化的日期格式

### 3. 灵活扩展
- **格式支持**: 支持多种日期格式
- **配置选项**: 提供灵活的配置选项
- **自定义**: 支持自定义格式化器
- **本地化**: 支持多语言本地化

### 4. 错误处理
- **输入验证**: 验证日期输入的有效性
- **错误恢复**: 提供错误恢复机制
- **异常处理**: 完善的异常处理
- **调试支持**: 提供调试信息

## 设计模式

### 1. 工具模式 (Utility Pattern)
- **纯函数**: 提供纯函数工具
- **无状态**: 无状态的工具函数
- **可复用**: 高度可复用的工具

### 2. 策略模式 (Strategy Pattern)
- **格式策略**: 不同的日期格式化策略
- **本地化策略**: 不同的本地化策略
- **显示策略**: 不同的显示策略

### 3. 工厂模式 (Factory Pattern)
- **格式化器工厂**: 创建不同的格式化器
- **本地化工厂**: 创建本地化配置
- **验证器工厂**: 创建验证器

### 4. 缓存模式 (Cache Pattern)
- **结果缓存**: 缓存格式化结果
- **性能优化**: 避免重复计算
- **内存管理**: 高效的缓存管理

## 注意事项

1. **日期有效性**: 确保输入日期的有效性
2. **时区处理**: 注意时区的正确处理
3. **本地化**: 考虑不同地区的日期格式
4. **性能考虑**: 避免频繁的日期格式化

## 扩展建议

1. **更多格式**: 支持更多的日期格式选项
2. **时区支持**: 增强时区处理功能
3. **本地化**: 添加更多语言的本地化支持
4. **性能优化**: 优化日期处理性能
5. **工具函数**: 添加更多实用的工具函数

该工具模块为Odoo Web客户端提供了简洁高效的日历工具功能，通过智能格式化、本地化支持和性能优化确保了日历日期显示的准确性和美观性。

# Hooks - 日历钩子函数

## 概述

`hooks.js` 是 Odoo Web 客户端日历视图的钩子函数模块，提供了日历组件的自定义钩子和工具函数。该模块包含128行代码，是一个专门的钩子模块，用于封装日历视图中常用的功能钩子，具备弹出框管理、FullCalendar集成、事件处理、生命周期管理等特性，是日历视图系统中组件逻辑复用的核心工具。

## 文件信息
- **路径**: `/web/static/src/views/calendar/hooks.js`
- **行数**: 128
- **模块**: `@web/views/calendar/hooks`

## 依赖关系

```javascript
// 核心依赖
'@web/core/assets'                  // 资源加载
'@web/core/browser/browser'         // 浏览器工具
'@web/core/popover/popover_hook'    // 弹出框钩子
'@web/core/utils/hooks'             // 工具钩子
'@odoo/owl'                         // OWL框架
```

## 主要钩子函数

### 1. useCalendarPopover - 日历弹出框钩子

```javascript
function useCalendarPopover(component) {
    const owner = useComponent();
    let popoverClass = "";
    const popoverOptions = { position: "right", onClose: cleanup };
    Object.defineProperty(popoverOptions, "popoverClass", { get: () => popoverClass });
    const popover = usePopover(component, popoverOptions);
    const dialog = useService("dialog");
    let removeDialog = null;
    let fcPopover;
    
    useExternalListener(
        window,
        "mousedown",
        (ev) => {
            if (fcPopover) {
                // do not let fullcalendar popover close when our own popover is open
                ev.stopPropagation();
            }
        },
        { capture: true }
    );
    
    function cleanup() {
        fcPopover = null;
        removeDialog = null;
    }
    
    function close() {
        removeDialog?.();
        popover.close();
        cleanup();
    }
    
    return {
        open: (target, props, options = {}) => {
            // 弹出框打开逻辑
        },
        close,
        isOpen: () => popover.isOpen,
    };
}
```

**钩子功能**:
- **弹出框管理**: 管理日历事件的弹出框
- **FullCalendar集成**: 与FullCalendar的弹出框协调
- **事件处理**: 处理鼠标事件和弹出框交互
- **生命周期**: 管理弹出框的生命周期

## 核心功能

### 1. 弹出框配置

```javascript
let popoverClass = "";
const popoverOptions = { position: "right", onClose: cleanup };
Object.defineProperty(popoverOptions, "popoverClass", { get: () => popoverClass });
const popover = usePopover(component, popoverOptions);
```

**配置功能**:
- **位置设置**: 设置弹出框默认位置为右侧
- **动态类名**: 支持动态设置弹出框CSS类名
- **关闭回调**: 设置弹出框关闭时的清理回调
- **组件集成**: 集成到指定的组件中

### 2. 事件监听

```javascript
useExternalListener(
    window,
    "mousedown",
    (ev) => {
        if (fcPopover) {
            // do not let fullcalendar popover close when our own popover is open
            ev.stopPropagation();
        }
    },
    { capture: true }
);
```

**事件监听功能**:
- **全局监听**: 监听全局鼠标按下事件
- **事件阻止**: 阻止FullCalendar弹出框的关闭
- **捕获阶段**: 在捕获阶段处理事件
- **条件处理**: 只在特定条件下阻止事件

### 3. 清理函数

```javascript
function cleanup() {
    fcPopover = null;
    removeDialog = null;
}
```

**清理功能**:
- **状态重置**: 重置弹出框相关状态
- **内存清理**: 清理引用避免内存泄漏
- **资源释放**: 释放相关资源
- **状态同步**: 同步清理状态

### 4. 关闭函数

```javascript
function close() {
    removeDialog?.();
    popover.close();
    cleanup();
}
```

**关闭功能**:
- **对话框关闭**: 关闭相关的对话框
- **弹出框关闭**: 关闭弹出框
- **清理执行**: 执行清理操作
- **状态更新**: 更新相关状态

### 5. 返回接口

```javascript
return {
    open: (target, props, options = {}) => {
        // 弹出框打开逻辑
    },
    close,
    isOpen: () => popover.isOpen,
};
```

**接口功能**:
- **打开方法**: 提供弹出框打开方法
- **关闭方法**: 提供弹出框关闭方法
- **状态查询**: 提供弹出框状态查询
- **参数支持**: 支持自定义参数和选项

## 使用场景

### 1. 日历钩子管理器

```javascript
// 日历钩子管理器
class CalendarHooksManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置钩子配置
        this.hooksConfig = {
            enablePopoverHooks: true,
            enableEventHooks: true,
            enableLifecycleHooks: true,
            enablePerformanceHooks: true,
            popoverPosition: 'right',
            popoverDelay: 200
        };
        
        // 设置钩子注册表
        this.hooksRegistry = new Map();
        
        // 设置钩子实例
        this.hookInstances = new Map();
        
        // 设置钩子统计
        this.hooksStatistics = {
            totalHooks: 0,
            activeHooks: 0,
            createdHooks: 0,
            destroyedHooks: 0
        };
        
        this.initializeHooks();
    }
    
    // 初始化钩子
    initializeHooks() {
        // 注册内置钩子
        this.registerBuiltinHooks();
        
        // 设置钩子工厂
        this.setupHookFactory();
        
        // 设置钩子生命周期
        this.setupHookLifecycle();
    }
    
    // 注册内置钩子
    registerBuiltinHooks() {
        // 注册弹出框钩子
        this.registerHook('popover', this.createPopoverHook.bind(this));
        
        // 注册事件钩子
        this.registerHook('event', this.createEventHook.bind(this));
        
        // 注册生命周期钩子
        this.registerHook('lifecycle', this.createLifecycleHook.bind(this));
        
        // 注册性能钩子
        this.registerHook('performance', this.createPerformanceHook.bind(this));
    }
    
    // 注册钩子
    registerHook(name, factory) {
        this.hooksRegistry.set(name, factory);
    }
    
    // 创建弹出框钩子
    createPopoverHook(component, options = {}) {
        const config = {
            position: options.position || this.hooksConfig.popoverPosition,
            delay: options.delay || this.hooksConfig.popoverDelay,
            enableFullCalendarIntegration: options.enableFullCalendarIntegration !== false,
            enableAutoClose: options.enableAutoClose !== false,
            ...options
        };
        
        return this.createEnhancedPopoverHook(component, config);
    }
    
    // 创建增强的弹出框钩子
    createEnhancedPopoverHook(component, config) {
        const originalHook = useCalendarPopover(component);
        
        // 扩展原始钩子
        const enhancedHook = {
            ...originalHook,
            
            // 增强的打开方法
            open: (target, props, options = {}) => {
                const openConfig = { ...config, ...options };
                
                // 添加延迟
                if (openConfig.delay > 0) {
                    setTimeout(() => {
                        this.executeOpen(originalHook, target, props, openConfig);
                    }, openConfig.delay);
                } else {
                    this.executeOpen(originalHook, target, props, openConfig);
                }
            },
            
            // 增强的关闭方法
            close: () => {
                this.executeClose(originalHook);
            },
            
            // 添加配置方法
            configure: (newConfig) => {
                Object.assign(config, newConfig);
            },
            
            // 添加状态方法
            getState: () => ({
                isOpen: originalHook.isOpen(),
                config: config,
                target: this.currentTarget
            })
        };
        
        // 记录钩子实例
        const hookId = this.generateHookId();
        this.hookInstances.set(hookId, {
            id: hookId,
            type: 'popover',
            instance: enhancedHook,
            component: component,
            config: config,
            createdAt: Date.now()
        });
        
        // 更新统计
        this.hooksStatistics.totalHooks++;
        this.hooksStatistics.activeHooks++;
        this.hooksStatistics.createdHooks++;
        
        return enhancedHook;
    }
    
    // 执行打开操作
    executeOpen(hook, target, props, config) {
        try {
            // 记录当前目标
            this.currentTarget = target;
            
            // 添加自定义属性
            const enhancedProps = {
                ...props,
                hookConfig: config,
                timestamp: Date.now()
            };
            
            // 执行原始打开
            hook.open(target, enhancedProps, config);
            
            // 触发打开事件
            this.triggerHookEvent('popover:opened', {
                target: target,
                props: enhancedProps,
                config: config
            });
            
        } catch (error) {
            this.handleHookError('popover:open', error);
        }
    }
    
    // 执行关闭操作
    executeClose(hook) {
        try {
            // 执行原始关闭
            hook.close();
            
            // 清理当前目标
            this.currentTarget = null;
            
            // 触发关闭事件
            this.triggerHookEvent('popover:closed', {
                timestamp: Date.now()
            });
            
        } catch (error) {
            this.handleHookError('popover:close', error);
        }
    }
    
    // 创建事件钩子
    createEventHook(component, options = {}) {
        const config = {
            enableEventDelegation: options.enableEventDelegation !== false,
            enableEventThrottling: options.enableEventThrottling !== false,
            throttleDelay: options.throttleDelay || 100,
            ...options
        };
        
        const eventListeners = new Map();
        const throttledHandlers = new Map();
        
        const eventHook = {
            // 添加事件监听器
            addEventListener: (element, event, handler, options = {}) => {
                const listenerConfig = { ...config, ...options };
                
                let finalHandler = handler;
                
                // 添加节流
                if (listenerConfig.enableEventThrottling) {
                    finalHandler = this.throttle(handler, listenerConfig.throttleDelay);
                    throttledHandlers.set(handler, finalHandler);
                }
                
                // 添加监听器
                element.addEventListener(event, finalHandler, listenerConfig);
                
                // 记录监听器
                const listenerId = this.generateListenerId();
                eventListeners.set(listenerId, {
                    element: element,
                    event: event,
                    handler: finalHandler,
                    originalHandler: handler,
                    config: listenerConfig
                });
                
                return listenerId;
            },
            
            // 移除事件监听器
            removeEventListener: (listenerId) => {
                const listener = eventListeners.get(listenerId);
                if (listener) {
                    listener.element.removeEventListener(
                        listener.event,
                        listener.handler,
                        listener.config
                    );
                    eventListeners.delete(listenerId);
                    
                    // 清理节流处理器
                    if (throttledHandlers.has(listener.originalHandler)) {
                        throttledHandlers.delete(listener.originalHandler);
                    }
                }
            },
            
            // 清理所有监听器
            cleanup: () => {
                for (const [listenerId] of eventListeners) {
                    eventHook.removeEventListener(listenerId);
                }
            },
            
            // 获取监听器统计
            getStatistics: () => ({
                totalListeners: eventListeners.size,
                throttledHandlers: throttledHandlers.size
            })
        };
        
        return eventHook;
    }
    
    // 创建生命周期钩子
    createLifecycleHook(component, options = {}) {
        const config = {
            enableAutoCleanup: options.enableAutoCleanup !== false,
            enableStateTracking: options.enableStateTracking !== false,
            ...options
        };
        
        const lifecycleHooks = {
            mounted: [],
            patched: [],
            willStart: [],
            willUnmount: []
        };
        
        const lifecycleHook = {
            // 添加生命周期钩子
            onMounted: (callback) => {
                lifecycleHooks.mounted.push(callback);
                onMounted(() => {
                    this.executeLifecycleCallback('mounted', callback);
                });
            },
            
            onPatched: (callback) => {
                lifecycleHooks.patched.push(callback);
                onPatched(() => {
                    this.executeLifecycleCallback('patched', callback);
                });
            },
            
            onWillStart: (callback) => {
                lifecycleHooks.willStart.push(callback);
                onWillStart(() => {
                    this.executeLifecycleCallback('willStart', callback);
                });
            },
            
            onWillUnmount: (callback) => {
                lifecycleHooks.willUnmount.push(callback);
                onWillUnmount(() => {
                    this.executeLifecycleCallback('willUnmount', callback);
                    
                    // 自动清理
                    if (config.enableAutoCleanup) {
                        this.cleanupLifecycleHook(lifecycleHook);
                    }
                });
            },
            
            // 获取钩子统计
            getHookCounts: () => ({
                mounted: lifecycleHooks.mounted.length,
                patched: lifecycleHooks.patched.length,
                willStart: lifecycleHooks.willStart.length,
                willUnmount: lifecycleHooks.willUnmount.length
            })
        };
        
        return lifecycleHook;
    }
    
    // 创建性能钩子
    createPerformanceHook(component, options = {}) {
        const config = {
            enablePerformanceTracking: options.enablePerformanceTracking !== false,
            enableMemoryTracking: options.enableMemoryTracking !== false,
            trackingInterval: options.trackingInterval || 1000,
            ...options
        };
        
        const performanceMetrics = {
            renderTimes: [],
            memoryUsage: [],
            eventCounts: {}
        };
        
        const performanceHook = {
            // 开始性能跟踪
            startTracking: () => {
                if (config.enablePerformanceTracking) {
                    this.startPerformanceTracking(performanceMetrics, config);
                }
            },
            
            // 停止性能跟踪
            stopTracking: () => {
                this.stopPerformanceTracking();
            },
            
            // 记录渲染时间
            recordRenderTime: (duration) => {
                performanceMetrics.renderTimes.push({
                    duration: duration,
                    timestamp: Date.now()
                });
                
                // 限制记录数量
                if (performanceMetrics.renderTimes.length > 100) {
                    performanceMetrics.renderTimes.shift();
                }
            },
            
            // 获取性能指标
            getMetrics: () => ({
                ...performanceMetrics,
                averageRenderTime: this.calculateAverageRenderTime(performanceMetrics.renderTimes),
                currentMemoryUsage: this.getCurrentMemoryUsage()
            })
        };
        
        return performanceHook;
    }
    
    // 设置钩子工厂
    setupHookFactory() {
        this.hookFactory = {
            create: (type, component, options = {}) => {
                const factory = this.hooksRegistry.get(type);
                if (!factory) {
                    throw new Error(`Hook type ${type} not found`);
                }
                
                return factory(component, options);
            },
            
            createMultiple: (types, component, options = {}) => {
                const hooks = {};
                
                for (const type of types) {
                    hooks[type] = this.hookFactory.create(type, component, options[type] || {});
                }
                
                return hooks;
            }
        };
    }
    
    // 设置钩子生命周期
    setupHookLifecycle() {
        this.lifecycle = {
            beforeCreate: [],
            afterCreate: [],
            beforeDestroy: [],
            afterDestroy: []
        };
    }
    
    // 执行生命周期回调
    executeLifecycleCallback(phase, callback) {
        try {
            callback();
            
            // 触发生命周期事件
            this.triggerHookEvent(`lifecycle:${phase}`, {
                phase: phase,
                timestamp: Date.now()
            });
            
        } catch (error) {
            this.handleHookError(`lifecycle:${phase}`, error);
        }
    }
    
    // 清理生命周期钩子
    cleanupLifecycleHook(lifecycleHook) {
        // 清理钩子数据
        if (lifecycleHook.cleanup) {
            lifecycleHook.cleanup();
        }
    }
    
    // 开始性能跟踪
    startPerformanceTracking(metrics, config) {
        if (this.performanceTrackingInterval) {
            clearInterval(this.performanceTrackingInterval);
        }
        
        this.performanceTrackingInterval = setInterval(() => {
            if (config.enableMemoryTracking && window.performance && window.performance.memory) {
                metrics.memoryUsage.push({
                    used: window.performance.memory.usedJSHeapSize,
                    total: window.performance.memory.totalJSHeapSize,
                    timestamp: Date.now()
                });
                
                // 限制记录数量
                if (metrics.memoryUsage.length > 100) {
                    metrics.memoryUsage.shift();
                }
            }
        }, config.trackingInterval);
    }
    
    // 停止性能跟踪
    stopPerformanceTracking() {
        if (this.performanceTrackingInterval) {
            clearInterval(this.performanceTrackingInterval);
            this.performanceTrackingInterval = null;
        }
    }
    
    // 计算平均渲染时间
    calculateAverageRenderTime(renderTimes) {
        if (renderTimes.length === 0) return 0;
        
        const total = renderTimes.reduce((sum, item) => sum + item.duration, 0);
        return total / renderTimes.length;
    }
    
    // 获取当前内存使用
    getCurrentMemoryUsage() {
        if (window.performance && window.performance.memory) {
            return {
                used: window.performance.memory.usedJSHeapSize,
                total: window.performance.memory.totalJSHeapSize,
                percentage: (window.performance.memory.usedJSHeapSize / window.performance.memory.totalJSHeapSize * 100).toFixed(2)
            };
        }
        return null;
    }
    
    // 节流函数
    throttle(func, delay) {
        let timeoutId;
        let lastExecTime = 0;
        
        return function (...args) {
            const currentTime = Date.now();
            
            if (currentTime - lastExecTime > delay) {
                func.apply(this, args);
                lastExecTime = currentTime;
            } else {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => {
                    func.apply(this, args);
                    lastExecTime = Date.now();
                }, delay - (currentTime - lastExecTime));
            }
        };
    }
    
    // 触发钩子事件
    triggerHookEvent(eventName, data) {
        // 可以在这里实现事件触发逻辑
        console.log(`Hook event: ${eventName}`, data);
    }
    
    // 处理钩子错误
    handleHookError(context, error) {
        console.error(`Hook error in ${context}:`, error);
        
        // 可以在这里实现错误报告逻辑
        this.reportHookError(context, error);
    }
    
    // 报告钩子错误
    reportHookError(context, error) {
        // 实现错误报告逻辑
        console.warn('Hook error reported:', { context, error });
    }
    
    // 生成钩子ID
    generateHookId() {
        return `hook_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    // 生成监听器ID
    generateListenerId() {
        return `listener_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    // 获取钩子统计
    getHooksStatistics() {
        return {
            ...this.hooksStatistics,
            registeredHooks: this.hooksRegistry.size,
            activeInstances: this.hookInstances.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 停止性能跟踪
        this.stopPerformanceTracking();
        
        // 清理钩子实例
        for (const [hookId, hookData] of this.hookInstances) {
            if (hookData.instance.cleanup) {
                hookData.instance.cleanup();
            }
        }
        this.hookInstances.clear();
        
        // 清理注册表
        this.hooksRegistry.clear();
        
        // 重置统计
        this.hooksStatistics = {
            totalHooks: 0,
            activeHooks: 0,
            createdHooks: 0,
            destroyedHooks: 0
        };
    }
}

// 使用示例
const hooksManager = new CalendarHooksManager();

// 创建弹出框钩子
const popoverHook = hooksManager.hookFactory.create('popover', component, {
    position: 'top',
    delay: 300
});

// 创建多个钩子
const hooks = hooksManager.hookFactory.createMultiple(
    ['popover', 'event', 'lifecycle'],
    component,
    {
        popover: { position: 'right' },
        event: { enableEventThrottling: true },
        lifecycle: { enableAutoCleanup: true }
    }
);

// 获取统计信息
const stats = hooksManager.getHooksStatistics();
console.log('Hooks statistics:', stats);
```

## 技术特点

### 1. 钩子封装
- **逻辑复用**: 封装常用的组件逻辑
- **状态管理**: 管理钩子相关状态
- **生命周期**: 完整的钩子生命周期管理
- **错误处理**: 完善的错误处理机制

### 2. FullCalendar集成
- **事件协调**: 与FullCalendar事件协调
- **弹出框管理**: 管理多个弹出框的交互
- **事件阻止**: 智能的事件传播控制
- **状态同步**: 同步不同组件的状态

### 3. 内存管理
- **资源清理**: 及时清理相关资源
- **引用管理**: 管理对象引用避免内存泄漏
- **状态重置**: 重置相关状态
- **生命周期**: 与组件生命周期绑定

### 4. 事件处理
- **全局监听**: 监听全局事件
- **事件捕获**: 在捕获阶段处理事件
- **条件处理**: 条件性的事件处理
- **性能优化**: 优化事件处理性能

## 设计模式

### 1. 钩子模式 (Hook Pattern)
- **逻辑封装**: 封装可复用的逻辑
- **状态管理**: 管理组件状态
- **副作用**: 处理组件副作用

### 2. 观察者模式 (Observer Pattern)
- **事件监听**: 监听各种事件
- **状态通知**: 通知状态变化
- **自动响应**: 自动响应事件

### 3. 策略模式 (Strategy Pattern)
- **处理策略**: 不同的事件处理策略
- **清理策略**: 不同的资源清理策略
- **集成策略**: 不同的组件集成策略

### 4. 工厂模式 (Factory Pattern)
- **钩子工厂**: 创建不同类型的钩子
- **配置工厂**: 创建钩子配置
- **处理器工厂**: 创建事件处理器

## 注意事项

1. **内存泄漏**: 注意清理事件监听器和引用
2. **事件冲突**: 避免与其他组件的事件冲突
3. **性能影响**: 避免过多的事件监听器
4. **生命周期**: 正确管理钩子的生命周期

## 扩展建议

1. **更多钩子**: 添加更多类型的钩子函数
2. **性能优化**: 优化钩子的性能和内存使用
3. **错误恢复**: 增强错误处理和恢复机制
4. **调试支持**: 添加钩子的调试和监控功能
5. **文档完善**: 完善钩子的使用文档

该钩子模块为Odoo Web客户端提供了强大的日历组件钩子功能，通过逻辑封装、状态管理和事件处理确保了日历组件的高效开发和良好的用户体验。

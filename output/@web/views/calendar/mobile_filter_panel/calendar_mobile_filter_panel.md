# CalendarMobileFilterPanel - 日历移动端过滤面板

## 概述

`calendar_mobile_filter_panel.js` 是 Odoo Web 客户端日历视图的移动端过滤面板组件，提供了适配移动设备的过滤器界面。该模块包含46行代码，是一个OWL组件，专门为移动端设计的简化过滤面板，具备侧边栏控制、过滤器排序、颜色管理、响应式设计等特性，是日历视图系统中移动端过滤的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/calendar/mobile_filter_panel/calendar_mobile_filter_panel.js`
- **行数**: 46
- **模块**: `@web/views/calendar/mobile_filter_panel/calendar_mobile_filter_panel`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                      // OWL框架
'@web/views/calendar/colors'     // 颜色管理
```

## 主要组件定义

### 1. CalendarMobileFilterPanel - 日历移动端过滤面板

```javascript
class CalendarMobileFilterPanel extends Component {
    static components = {};
    static template = "web.CalendarMobileFilterPanel";
    static props = {
        model: Object,
        sideBarShown: Boolean,
        toggleSideBar: Function,
    };
}
```

**组件特性**:
- **无子组件**: 简化的移动端设计
- **专用模板**: 使用移动端专用模板
- **侧边栏控制**: 支持侧边栏的显示控制
- **模型集成**: 集成日历模型数据

## 核心功能

### 1. 侧边栏方向控制

```javascript
get caretDirection() {
    return this.props.sideBarShown ? "down" : "left";
}
```

**方向控制功能**:
- **状态判断**: 根据侧边栏显示状态判断方向
- **图标控制**: 控制展开/收起图标的方向
- **视觉反馈**: 提供清晰的视觉状态反馈
- **用户体验**: 增强移动端用户体验

### 2. 过滤器颜色

```javascript
getFilterColor(filter) {
    return `o_color_${getColor(filter.colorIndex)}`;
}
```

**颜色功能**:
- **颜色索引**: 使用过滤器的颜色索引
- **CSS类生成**: 生成对应的CSS颜色类
- **颜色管理**: 集成统一的颜色管理系统
- **视觉区分**: 提供过滤器的视觉区分

### 3. 过滤器类型优先级

```javascript
getFilterTypePriority(type) {
    return ["user", "record", "dynamic", "all"].indexOf(type);
}
```

**优先级功能**:
- **类型排序**: 定义过滤器类型的排序优先级
- **用户优先**: 用户过滤器优先级最高
- **记录次之**: 记录过滤器次优先级
- **动态和全部**: 动态和全部过滤器优先级较低

### 4. 过滤器排序

```javascript
getSortedFilters(section) {
    return section.filters.slice().sort((a, b) => {
        if (a.type === b.type) {
            const va = a.value ? -1 : 0;
            const vb = b.value ? -1 : 0;
            //Condition to put unvaluable item (eg: Open Shifts) at the end of the sorted list.
            if (a.type === "dynamic" && va !== vb) {
                return va - vb;
            }
            return a.label.localeCompare(b.label, undefined, {
                numeric: true,
                sensitivity: "base",
                ignorePunctuation: true,
            });
        } else {
            return this.getFilterTypePriority(a.type) - this.getFilterTypePriority(b.type);
        }
    });
}
```

**排序功能**:
- **类型排序**: 首先按过滤器类型排序
- **标签排序**: 同类型内按标签字母顺序排序
- **值排序**: 动态类型特殊处理有值和无值项
- **本地化排序**: 使用本地化的字符串比较

## 使用场景

### 1. 移动端过滤面板管理器

```javascript
// 移动端过滤面板管理器
class MobileFilterPanelManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置移动端配置
        this.mobileConfig = {
            enableTouchGestures: true,
            enableSwipeToToggle: true,
            enableHapticFeedback: true,
            enableAutoHide: true,
            autoHideDelay: 3000,
            maxVisibleFilters: 5
        };
        
        // 设置手势处理
        this.gestureHandlers = new Map();
        
        // 设置过滤器状态
        this.filterState = {
            isVisible: false,
            isAnimating: false,
            selectedFilters: new Set(),
            lastInteraction: Date.now()
        };
        
        // 设置移动端统计
        this.mobileStatistics = {
            totalInteractions: 0,
            swipeGestures: 0,
            tapGestures: 0,
            filterToggles: 0
        };
        
        this.initializeMobileSystem();
    }
    
    // 初始化移动端系统
    initializeMobileSystem() {
        // 创建增强的移动端面板
        this.createEnhancedMobilePanel();
        
        // 设置手势识别
        this.setupGestureRecognition();
        
        // 设置响应式适配
        this.setupResponsiveAdaptation();
        
        // 设置性能优化
        this.setupPerformanceOptimization();
    }
    
    // 创建增强的移动端面板
    createEnhancedMobilePanel() {
        const originalPanel = CalendarMobileFilterPanel;
        
        this.EnhancedMobilePanel = class extends originalPanel {
            setup() {
                super.setup();
                
                // 添加移动端增强功能
                this.addMobileEnhancements();
                
                // 添加手势支持
                this.addGestureSupport();
                
                // 添加触觉反馈
                this.addHapticFeedback();
                
                // 添加自动隐藏
                this.addAutoHide();
            }
            
            addMobileEnhancements() {
                // 扩展状态
                this.mobileState = useState({
                    isExpanded: false,
                    isDragging: false,
                    dragOffset: 0,
                    touchStartY: 0,
                    velocity: 0,
                    lastTouchTime: 0
                });
                
                // 添加移动端特定方法
                this.addMobileMethods();
            }
            
            addMobileMethods() {
                // 切换展开状态
                this.toggleExpanded = () => {
                    this.mobileState.isExpanded = !this.mobileState.isExpanded;
                    
                    if (this.mobileConfig.enableHapticFeedback) {
                        this.triggerHapticFeedback('light');
                    }
                    
                    // 更新统计
                    this.mobileStatistics.filterToggles++;
                };
                
                // 快速切换过滤器
                this.quickToggleFilter = (section, filter) => {
                    // 添加快速切换动画
                    this.addQuickToggleAnimation(filter);
                    
                    // 执行切换
                    this.toggleFilter(section, filter);
                    
                    // 触觉反馈
                    if (this.mobileConfig.enableHapticFeedback) {
                        this.triggerHapticFeedback(filter.active ? 'medium' : 'light');
                    }
                };
                
                // 批量切换
                this.batchToggleFilters = (filters, active) => {
                    // 添加批量操作动画
                    this.addBatchAnimation();
                    
                    for (const filterData of filters) {
                        const { section, filter } = filterData;
                        if (filter.active !== active) {
                            this.toggleFilter(section, filter);
                        }
                    }
                    
                    // 强触觉反馈
                    if (this.mobileConfig.enableHapticFeedback) {
                        this.triggerHapticFeedback('heavy');
                    }
                };
                
                // 重置所有过滤器
                this.resetAllFilters = () => {
                    const sections = this.props.model.data.filterSections;
                    const activeFilters = [];
                    
                    for (const section of Object.values(sections)) {
                        for (const filter of section.filters) {
                            if (filter.active) {
                                activeFilters.push({ section, filter });
                            }
                        }
                    }
                    
                    this.batchToggleFilters(activeFilters, false);
                };
            }
            
            addGestureSupport() {
                // 添加触摸事件监听
                this.addTouchEventListeners();
                
                // 添加手势识别
                this.addGestureRecognition();
            }
            
            addTouchEventListeners() {
                // 触摸开始
                this.onTouchStart = (event) => {
                    const touch = event.touches[0];
                    this.mobileState.touchStartY = touch.clientY;
                    this.mobileState.lastTouchTime = Date.now();
                    this.mobileState.isDragging = false;
                    
                    // 停止自动隐藏
                    this.stopAutoHide();
                };
                
                // 触摸移动
                this.onTouchMove = (event) => {
                    if (!this.mobileState.touchStartY) return;
                    
                    const touch = event.touches[0];
                    const deltaY = touch.clientY - this.mobileState.touchStartY;
                    const currentTime = Date.now();
                    const deltaTime = currentTime - this.mobileState.lastTouchTime;
                    
                    // 计算速度
                    this.mobileState.velocity = deltaY / deltaTime;
                    this.mobileState.dragOffset = deltaY;
                    this.mobileState.isDragging = Math.abs(deltaY) > 10;
                    
                    // 实时更新位置
                    if (this.mobileState.isDragging) {
                        this.updatePanelPosition(deltaY);
                    }
                };
                
                // 触摸结束
                this.onTouchEnd = (event) => {
                    if (!this.mobileState.isDragging) {
                        // 简单点击
                        this.handleTap(event);
                    } else {
                        // 拖拽结束
                        this.handleDragEnd();
                    }
                    
                    // 重置状态
                    this.resetTouchState();
                    
                    // 重新启动自动隐藏
                    this.startAutoHide();
                };
            }
            
            addGestureRecognition() {
                // 识别滑动手势
                this.recognizeSwipeGesture = () => {
                    const { dragOffset, velocity } = this.mobileState;
                    
                    // 向上滑动展开
                    if (dragOffset < -50 || velocity < -0.5) {
                        if (!this.mobileState.isExpanded) {
                            this.toggleExpanded();
                            this.mobileStatistics.swipeGestures++;
                        }
                        return 'swipe_up';
                    }
                    
                    // 向下滑动收起
                    if (dragOffset > 50 || velocity > 0.5) {
                        if (this.mobileState.isExpanded) {
                            this.toggleExpanded();
                            this.mobileStatistics.swipeGestures++;
                        }
                        return 'swipe_down';
                    }
                    
                    return null;
                };
                
                // 识别长按手势
                this.recognizeLongPressGesture = (element) => {
                    let longPressTimer;
                    
                    const startLongPress = () => {
                        longPressTimer = setTimeout(() => {
                            this.handleLongPress(element);
                        }, 500);
                    };
                    
                    const cancelLongPress = () => {
                        if (longPressTimer) {
                            clearTimeout(longPressTimer);
                            longPressTimer = null;
                        }
                    };
                    
                    element.addEventListener('touchstart', startLongPress);
                    element.addEventListener('touchend', cancelLongPress);
                    element.addEventListener('touchmove', cancelLongPress);
                };
            }
            
            addHapticFeedback() {
                // 触发触觉反馈
                this.triggerHapticFeedback = (intensity = 'light') => {
                    if (!this.mobileConfig.enableHapticFeedback) return;
                    
                    if ('vibrate' in navigator) {
                        const patterns = {
                            light: [10],
                            medium: [20],
                            heavy: [30],
                            double: [10, 50, 10],
                            success: [10, 30, 10, 30, 10]
                        };
                        
                        navigator.vibrate(patterns[intensity] || patterns.light);
                    }
                };
            }
            
            addAutoHide() {
                let autoHideTimer;
                
                // 开始自动隐藏
                this.startAutoHide = () => {
                    if (!this.mobileConfig.enableAutoHide) return;
                    
                    this.stopAutoHide();
                    
                    autoHideTimer = setTimeout(() => {
                        if (this.mobileState.isExpanded) {
                            this.toggleExpanded();
                        }
                    }, this.mobileConfig.autoHideDelay);
                };
                
                // 停止自动隐藏
                this.stopAutoHide = () => {
                    if (autoHideTimer) {
                        clearTimeout(autoHideTimer);
                        autoHideTimer = null;
                    }
                };
                
                // 重置自动隐藏
                this.resetAutoHide = () => {
                    this.stopAutoHide();
                    this.startAutoHide();
                };
            }
            
            // 处理点击
            handleTap(event) {
                const target = event.target.closest('[data-filter]');
                
                if (target) {
                    const filterId = target.dataset.filter;
                    const filterData = this.findFilterById(filterId);
                    
                    if (filterData) {
                        this.quickToggleFilter(filterData.section, filterData.filter);
                    }
                }
                
                this.mobileStatistics.tapGestures++;
            }
            
            // 处理拖拽结束
            handleDragEnd() {
                const gesture = this.recognizeSwipeGesture();
                
                if (!gesture) {
                    // 回弹到原位置
                    this.animateToPosition(0);
                }
            }
            
            // 处理长按
            handleLongPress(element) {
                const filterId = element.dataset.filter;
                const filterData = this.findFilterById(filterId);
                
                if (filterData) {
                    // 显示过滤器选项菜单
                    this.showFilterOptionsMenu(filterData);
                    
                    // 强触觉反馈
                    this.triggerHapticFeedback('heavy');
                }
            }
            
            // 更新面板位置
            updatePanelPosition(offset) {
                const panel = this.el.querySelector('.o_calendar_mobile_filter_panel');
                if (panel) {
                    panel.style.transform = `translateY(${offset}px)`;
                }
            }
            
            // 动画到指定位置
            animateToPosition(targetY) {
                const panel = this.el.querySelector('.o_calendar_mobile_filter_panel');
                if (panel) {
                    panel.style.transition = 'transform 0.3s ease-out';
                    panel.style.transform = `translateY(${targetY}px)`;
                    
                    setTimeout(() => {
                        panel.style.transition = '';
                    }, 300);
                }
            }
            
            // 添加快速切换动画
            addQuickToggleAnimation(filter) {
                const filterElement = this.el.querySelector(`[data-filter="${filter.id}"]`);
                if (filterElement) {
                    filterElement.classList.add('o_filter_toggle_animation');
                    
                    setTimeout(() => {
                        filterElement.classList.remove('o_filter_toggle_animation');
                    }, 200);
                }
            }
            
            // 添加批量操作动画
            addBatchAnimation() {
                const panel = this.el.querySelector('.o_calendar_mobile_filter_panel');
                if (panel) {
                    panel.classList.add('o_batch_operation_animation');
                    
                    setTimeout(() => {
                        panel.classList.remove('o_batch_operation_animation');
                    }, 500);
                }
            }
            
            // 查找过滤器
            findFilterById(filterId) {
                const sections = this.props.model.data.filterSections;
                
                for (const section of Object.values(sections)) {
                    for (const filter of section.filters) {
                        if (filter.id === filterId) {
                            return { section, filter };
                        }
                    }
                }
                
                return null;
            }
            
            // 显示过滤器选项菜单
            showFilterOptionsMenu(filterData) {
                const options = [
                    {
                        label: filterData.filter.active ? 'Deactivate' : 'Activate',
                        action: () => this.quickToggleFilter(filterData.section, filterData.filter)
                    },
                    {
                        label: 'Edit',
                        action: () => this.editFilter(filterData)
                    },
                    {
                        label: 'Delete',
                        action: () => this.deleteFilter(filterData),
                        danger: true
                    }
                ];
                
                // 显示选项菜单（需要实现菜单组件）
                this.showOptionsMenu(options);
            }
            
            // 重置触摸状态
            resetTouchState() {
                this.mobileState.touchStartY = 0;
                this.mobileState.isDragging = false;
                this.mobileState.dragOffset = 0;
                this.mobileState.velocity = 0;
            }
            
            // 重写排序方法以适配移动端
            getSortedFilters(section) {
                const sorted = super.getSortedFilters(section);
                
                // 移动端限制显示数量
                if (this.mobileConfig.maxVisibleFilters > 0) {
                    return sorted.slice(0, this.mobileConfig.maxVisibleFilters);
                }
                
                return sorted;
            }
            
            // 获取移动端统计
            getMobileStatistics() {
                return {
                    ...this.mobileStatistics,
                    isExpanded: this.mobileState.isExpanded,
                    isDragging: this.mobileState.isDragging,
                    lastInteraction: this.filterState.lastInteraction
                };
            }
        };
    }
    
    // 设置手势识别
    setupGestureRecognition() {
        this.gestureThresholds = {
            swipeMinDistance: 50,
            swipeMinVelocity: 0.5,
            longPressDelay: 500,
            tapMaxDistance: 10,
            tapMaxDuration: 200
        };
    }
    
    // 设置响应式适配
    setupResponsiveAdaptation() {
        this.breakpoints = {
            mobile: 480,
            tablet: 768
        };
        
        this.currentDevice = this.detectDevice();
        
        // 监听设备方向变化
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.handleOrientationChange();
            }, 100);
        });
    }
    
    // 检测设备类型
    detectDevice() {
        const width = window.innerWidth;
        
        if (width < this.breakpoints.mobile) {
            return 'mobile';
        } else if (width < this.breakpoints.tablet) {
            return 'tablet';
        } else {
            return 'desktop';
        }
    }
    
    // 处理方向变化
    handleOrientationChange() {
        const newDevice = this.detectDevice();
        
        if (newDevice !== this.currentDevice) {
            this.currentDevice = newDevice;
            this.onDeviceChange(newDevice);
        }
    }
    
    // 设备变化处理
    onDeviceChange(device) {
        // 根据设备类型调整配置
        switch (device) {
            case 'mobile':
                this.mobileConfig.maxVisibleFilters = 3;
                this.mobileConfig.autoHideDelay = 2000;
                break;
            case 'tablet':
                this.mobileConfig.maxVisibleFilters = 5;
                this.mobileConfig.autoHideDelay = 3000;
                break;
            default:
                this.mobileConfig.maxVisibleFilters = 0; // 无限制
                this.mobileConfig.autoHideDelay = 5000;
        }
    }
    
    // 设置性能优化
    setupPerformanceOptimization() {
        // 节流函数
        this.throttle = (func, delay) => {
            let timeoutId;
            let lastExecTime = 0;
            
            return function (...args) {
                const currentTime = Date.now();
                
                if (currentTime - lastExecTime > delay) {
                    func.apply(this, args);
                    lastExecTime = currentTime;
                } else {
                    clearTimeout(timeoutId);
                    timeoutId = setTimeout(() => {
                        func.apply(this, args);
                        lastExecTime = Date.now();
                    }, delay - (currentTime - lastExecTime));
                }
            };
        };
        
        // 防抖函数
        this.debounce = (func, delay) => {
            let timeoutId;
            
            return function (...args) {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => func.apply(this, args), delay);
            };
        };
    }
    
    // 获取移动端统计
    getMobileStatistics() {
        return {
            ...this.mobileStatistics,
            currentDevice: this.currentDevice,
            gestureThresholds: this.gestureThresholds,
            configuredMaxFilters: this.mobileConfig.maxVisibleFilters
        };
    }
    
    // 销毁管理器
    destroy() {
        // 移除事件监听器
        window.removeEventListener('orientationchange', this.handleOrientationChange);
        
        // 清理手势处理器
        this.gestureHandlers.clear();
        
        // 重置统计
        this.mobileStatistics = {
            totalInteractions: 0,
            swipeGestures: 0,
            tapGestures: 0,
            filterToggles: 0
        };
    }
}

// 使用示例
const mobileFilterManager = new MobileFilterPanelManager();

// 创建增强的移动端面板
const EnhancedMobilePanel = mobileFilterManager.EnhancedMobilePanel;

// 获取统计信息
const stats = mobileFilterManager.getMobileStatistics();
console.log('Mobile filter panel statistics:', stats);
```

## 技术特点

### 1. 移动端优化
- **触摸友好**: 专为触摸设备优化的界面
- **手势支持**: 支持滑动、点击、长按等手势
- **响应式**: 适配不同屏幕尺寸
- **性能优化**: 针对移动设备的性能优化

### 2. 简化设计
- **轻量级**: 简化的组件设计
- **核心功能**: 专注于核心过滤功能
- **快速操作**: 支持快速的过滤器操作
- **直观界面**: 直观易用的用户界面

### 3. 智能排序
- **类型优先**: 按过滤器类型优先级排序
- **标签排序**: 同类型内按标签排序
- **本地化**: 支持本地化的排序规则
- **特殊处理**: 对特殊类型的特殊处理

### 4. 颜色系统
- **颜色编码**: 过滤器的颜色编码
- **视觉区分**: 清晰的视觉区分
- **一致性**: 与桌面版保持一致
- **可访问性**: 考虑颜色的可访问性

## 设计模式

### 1. 适配器模式 (Adapter Pattern)
- **移动适配**: 适配移动端的特殊需求
- **接口转换**: 转换桌面端接口到移动端
- **功能简化**: 简化复杂功能

### 2. 策略模式 (Strategy Pattern)
- **排序策略**: 不同的过滤器排序策略
- **显示策略**: 不同的显示策略
- **交互策略**: 不同的用户交互策略

### 3. 观察者模式 (Observer Pattern)
- **状态监听**: 监听过滤器状态变化
- **自动更新**: 自动更新界面显示
- **事件响应**: 响应用户交互事件

### 4. 工厂模式 (Factory Pattern)
- **组件创建**: 创建移动端专用组件
- **手势创建**: 创建手势处理器
- **动画创建**: 创建动画效果

## 注意事项

1. **触摸体验**: 确保良好的触摸体验
2. **性能考虑**: 避免过多的DOM操作
3. **电池优化**: 考虑移动设备的电池消耗
4. **网络优化**: 优化网络请求和数据传输

## 扩展建议

1. **手势增强**: 添加更多手势支持
2. **动画效果**: 增强动画和过渡效果
3. **离线支持**: 添加离线功能支持
4. **推送通知**: 集成推送通知功能
5. **语音控制**: 添加语音控制功能

该移动端过滤面板组件为Odoo Web客户端提供了优秀的移动端过滤体验，通过触摸优化、手势支持和响应式设计确保了移动设备上的良好用户体验。

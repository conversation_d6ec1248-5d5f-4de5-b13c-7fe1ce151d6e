# CalendarArchParser - 日历架构解析器

## 概述

`calendar_arch_parser.js` 是 Odoo Web 客户端日历视图的架构解析器，提供了日历视图XML架构的解析和配置功能。该模块包含214行代码，是一个专门的解析器类，用于解析日历视图的XML定义并生成相应的配置对象，具备字段映射、属性解析、过滤器配置、弹出框设置等特性，是日历视图系统中架构解析的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/calendar/calendar_arch_parser.js`
- **行数**: 214
- **模块**: `@web/views/calendar/calendar_arch_parser`

## 依赖关系

```javascript
// 核心依赖
'@web/core/browser/browser'         // 浏览器工具
'@web/core/py_js/py'                // Python表达式解析
'@web/core/utils/strings'           // 字符串工具
'@web/core/utils/xml'               // XML工具
'@web/views/fields/field'           // 字段组件
```

## 核心常量

### 1. 字段属性名称

```javascript
const FIELD_ATTRIBUTE_NAMES = [
    "date_start",           // 开始日期
    "date_delay",           // 延迟时间
    "date_stop",            // 结束日期
    "all_day",              // 全天事件
    "recurrence_update",    // 重复更新
    "create_name_field",    // 创建名称字段
    "color",                // 颜色
];
```

### 2. 时间刻度

```javascript
const SCALES = ["day", "week", "month", "year"];
```

## 主要类定义

### 1. CalendarParseArchError - 解析错误类

```javascript
class CalendarParseArchError extends Error {}
```

**错误类功能**:
- **专用错误**: 日历架构解析专用的错误类
- **错误标识**: 标识解析过程中的错误
- **错误处理**: 提供专门的错误处理机制

### 2. CalendarArchParser - 日历架构解析器

```javascript
class CalendarArchParser {
    parse(arch, models, modelName) {
        const fields = models[modelName].fields;
        const fieldNames = new Set(fields.display_name ? ["display_name"] : []);
        const fieldMapping = { date_start: "date_start" };
        
        // 默认配置
        let jsClass = null;
        let eventLimit = 5;
        let scales = [...SCALES];
        const sessionScale = browser.sessionStorage.getItem("calendar-scale");
        let scale = sessionScale || "week";
        let canCreate = true;
        let canDelete = true;
        let canEdit = true;
        let quickCreate = true;
        let quickCreateViewId = null;
        let hasEditDialog = false;
        let showUnusualDays = false;
        let isDateHidden = false;
        let isTimeHidden = false;
        let formViewId = false;
        const popoverFieldNodes = {};
        const filtersInfo = {};

        // XML解析逻辑
        visitXML(arch, (node) => {
            // 解析各种节点
        });

        return {
            fieldNames: [...fieldNames],
            fieldMapping,
            jsClass,
            eventLimit,
            scales,
            scale,
            canCreate,
            canDelete,
            canEdit,
            quickCreate,
            quickCreateViewId,
            hasEditDialog,
            showUnusualDays,
            isDateHidden,
            isTimeHidden,
            formViewId,
            popoverFieldNodes,
            filtersInfo,
        };
    }
}
```

## 核心功能

### 1. 架构解析

```javascript
parse(arch, models, modelName) {
    const fields = models[modelName].fields;
    const fieldNames = new Set(fields.display_name ? ["display_name"] : []);
    const fieldMapping = { date_start: "date_start" };
    
    // 解析XML架构
    visitXML(arch, (node) => {
        if (node.tagName === "calendar") {
            // 解析日历根节点
            this.parseCalendarNode(node);
        } else if (node.tagName === "field") {
            // 解析字段节点
            this.parseFieldNode(node);
        } else if (node.tagName === "filter") {
            // 解析过滤器节点
            this.parseFilterNode(node);
        }
    });
}
```

**解析功能**:
- **XML遍历**: 遍历XML架构的所有节点
- **节点识别**: 识别不同类型的节点
- **配置提取**: 从节点中提取配置信息
- **字段映射**: 建立字段名称映射关系

### 2. 日历节点解析

```javascript
parseCalendarNode(node) {
    // 解析基本属性
    if (node.hasAttribute("js_class")) {
        jsClass = node.getAttribute("js_class");
    }
    
    if (node.hasAttribute("event_limit")) {
        eventLimit = parseInt(node.getAttribute("event_limit"));
    }
    
    if (node.hasAttribute("scales")) {
        scales = node.getAttribute("scales").split(",");
    }
    
    if (node.hasAttribute("scale")) {
        scale = node.getAttribute("scale");
    }
    
    // 解析权限属性
    if (node.hasAttribute("create")) {
        canCreate = exprToBoolean(node.getAttribute("create"));
    }
    
    if (node.hasAttribute("delete")) {
        canDelete = exprToBoolean(node.getAttribute("delete"));
    }
    
    if (node.hasAttribute("edit")) {
        canEdit = exprToBoolean(node.getAttribute("edit"));
    }
    
    // 解析快速创建属性
    if (node.hasAttribute("quick_create")) {
        quickCreate = exprToBoolean(node.getAttribute("quick_create"));
    }
    
    if (node.hasAttribute("quick_create_view_id")) {
        quickCreateViewId = parseInt(node.getAttribute("quick_create_view_id"));
    }
}
```

**日历节点解析功能**:
- **基本配置**: 解析JS类、事件限制、时间刻度等
- **权限配置**: 解析创建、删除、编辑权限
- **快速创建**: 解析快速创建相关配置
- **视图配置**: 解析视图ID和显示选项

### 3. 字段节点解析

```javascript
parseFieldNode(node) {
    const fieldName = node.getAttribute("name");
    
    // 添加字段名到集合
    fieldNames.add(fieldName);
    
    // 检查是否为特殊字段属性
    for (const attrName of FIELD_ATTRIBUTE_NAMES) {
        if (node.hasAttribute(attrName)) {
            fieldMapping[attrName] = fieldName;
        }
    }
    
    // 解析字段特定属性
    if (node.hasAttribute("invisible")) {
        const invisible = evaluateExpr(node.getAttribute("invisible"));
        if (invisible) {
            // 处理不可见字段
        }
    }
    
    // 解析弹出框字段
    if (node.parentNode && node.parentNode.tagName === "templates") {
        const template = node.parentNode.getAttribute("template");
        if (template === "popover") {
            popoverFieldNodes[fieldName] = node;
        }
    }
}
```

**字段节点解析功能**:
- **字段收集**: 收集所有字段名称
- **属性映射**: 建立字段属性映射
- **可见性**: 处理字段可见性配置
- **弹出框**: 配置弹出框显示字段

### 4. 过滤器节点解析

```javascript
parseFilterNode(node) {
    const filterName = node.getAttribute("name");
    const filterField = node.getAttribute("field");
    
    if (!filterName || !filterField) {
        throw new CalendarParseArchError("Filter must have name and field attributes");
    }
    
    const filterInfo = {
        name: filterName,
        field: filterField,
        avatar_field: node.getAttribute("avatar_field"),
        color: node.getAttribute("color"),
        write_model: node.getAttribute("write_model"),
        write_field: node.getAttribute("write_field"),
    };
    
    // 解析过滤器选项
    const options = [];
    for (const child of node.children) {
        if (child.tagName === "filter") {
            options.push({
                value: child.getAttribute("value"),
                color: child.getAttribute("color"),
                label: child.textContent,
            });
        }
    }
    
    filterInfo.options = options;
    filtersInfo[filterName] = filterInfo;
}
```

**过滤器节点解析功能**:
- **过滤器配置**: 解析过滤器基本配置
- **字段映射**: 建立过滤器字段映射
- **选项解析**: 解析过滤器选项
- **颜色配置**: 配置过滤器颜色

### 5. 配置验证

```javascript
validateConfiguration() {
    // 验证必需字段
    if (!fieldMapping.date_start) {
        throw new CalendarParseArchError("date_start field is required");
    }
    
    // 验证时间刻度
    if (!SCALES.includes(scale)) {
        throw new CalendarParseArchError(`Invalid scale: ${scale}`);
    }
    
    // 验证事件限制
    if (eventLimit < 1) {
        throw new CalendarParseArchError("event_limit must be positive");
    }
    
    // 验证字段存在性
    for (const [attr, fieldName] of Object.entries(fieldMapping)) {
        if (fieldName && !fields[fieldName]) {
            throw new CalendarParseArchError(`Field ${fieldName} not found for attribute ${attr}`);
        }
    }
}
```

**验证功能**:
- **必需字段**: 验证必需字段的存在
- **配置有效性**: 验证配置值的有效性
- **字段存在**: 验证引用字段的存在性
- **错误报告**: 提供详细的错误信息

## 使用场景

### 1. 日历架构解析管理器

```javascript
// 日历架构解析管理器
class CalendarArchParsingManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置解析配置
        this.parsingConfig = {
            enableValidation: true,
            enableCaching: true,
            enableOptimization: true,
            strictMode: false,
            defaultScale: 'week',
            defaultEventLimit: 5
        };
        
        // 设置解析缓存
        this.parseCache = new Map();
        
        // 设置字段映射缓存
        this.fieldMappingCache = new Map();
        
        // 设置解析统计
        this.parseStatistics = {
            totalParses: 0,
            cacheHits: 0,
            cacheMisses: 0,
            errors: 0
        };
        
        this.initializeParser();
    }
    
    // 初始化解析器
    initializeParser() {
        this.parser = new CalendarArchParser();
        
        // 扩展解析器功能
        this.enhanceParser();
    }
    
    // 增强解析器
    enhanceParser() {
        const originalParse = this.parser.parse.bind(this.parser);
        
        this.parser.parse = (arch, models, modelName) => {
            // 生成缓存键
            const cacheKey = this.generateCacheKey(arch, modelName);
            
            // 检查缓存
            if (this.parsingConfig.enableCaching && this.parseCache.has(cacheKey)) {
                this.parseStatistics.cacheHits++;
                return this.parseCache.get(cacheKey);
            }
            
            this.parseStatistics.cacheMisses++;
            this.parseStatistics.totalParses++;
            
            try {
                // 执行解析
                const result = originalParse(arch, models, modelName);
                
                // 验证结果
                if (this.parsingConfig.enableValidation) {
                    this.validateParseResult(result, models, modelName);
                }
                
                // 优化结果
                if (this.parsingConfig.enableOptimization) {
                    this.optimizeParseResult(result);
                }
                
                // 缓存结果
                if (this.parsingConfig.enableCaching) {
                    this.parseCache.set(cacheKey, result);
                }
                
                return result;
                
            } catch (error) {
                this.parseStatistics.errors++;
                this.handleParseError(error, arch, modelName);
                throw error;
            }
        };
    }
    
    // 生成缓存键
    generateCacheKey(arch, modelName) {
        const archString = new XMLSerializer().serializeToString(arch);
        const hash = this.simpleHash(archString + modelName);
        return `calendar_arch_${modelName}_${hash}`;
    }
    
    // 简单哈希函数
    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash).toString(36);
    }
    
    // 验证解析结果
    validateParseResult(result, models, modelName) {
        const fields = models[modelName].fields;
        
        // 验证必需字段
        if (!result.fieldMapping.date_start) {
            throw new CalendarParseArchError("date_start field mapping is required");
        }
        
        // 验证字段存在性
        for (const [attr, fieldName] of Object.entries(result.fieldMapping)) {
            if (fieldName && !fields[fieldName]) {
                if (this.parsingConfig.strictMode) {
                    throw new CalendarParseArchError(`Field ${fieldName} not found for attribute ${attr}`);
                } else {
                    console.warn(`Field ${fieldName} not found for attribute ${attr}`);
                }
            }
        }
        
        // 验证时间刻度
        if (!SCALES.includes(result.scale)) {
            throw new CalendarParseArchError(`Invalid scale: ${result.scale}`);
        }
        
        // 验证过滤器配置
        for (const [filterName, filterInfo] of Object.entries(result.filtersInfo)) {
            if (!filterInfo.field || !fields[filterInfo.field]) {
                throw new CalendarParseArchError(`Filter ${filterName} references invalid field: ${filterInfo.field}`);
            }
        }
    }
    
    // 优化解析结果
    optimizeParseResult(result) {
        // 优化字段名称集合
        result.fieldNames = [...new Set(result.fieldNames)];
        
        // 优化时间刻度
        result.scales = result.scales.filter(scale => SCALES.includes(scale));
        
        // 优化弹出框字段
        const optimizedPopoverFields = {};
        for (const [fieldName, node] of Object.entries(result.popoverFieldNodes)) {
            if (result.fieldNames.includes(fieldName)) {
                optimizedPopoverFields[fieldName] = node;
            }
        }
        result.popoverFieldNodes = optimizedPopoverFields;
        
        // 设置默认值
        if (!result.scale || !SCALES.includes(result.scale)) {
            result.scale = this.parsingConfig.defaultScale;
        }
        
        if (!result.eventLimit || result.eventLimit < 1) {
            result.eventLimit = this.parsingConfig.defaultEventLimit;
        }
    }
    
    // 解析日历架构
    parseCalendarArch(arch, models, modelName, options = {}) {
        const config = {
            enableValidation: options.enableValidation !== false,
            enableCaching: options.enableCaching !== false,
            enableOptimization: options.enableOptimization !== false,
            ...options
        };
        
        // 临时设置配置
        const originalConfig = { ...this.parsingConfig };
        Object.assign(this.parsingConfig, config);
        
        try {
            const result = this.parser.parse(arch, models, modelName);
            
            // 添加解析元数据
            result._metadata = {
                modelName: modelName,
                parseTime: Date.now(),
                cacheKey: this.generateCacheKey(arch, modelName),
                config: config
            };
            
            return result;
            
        } finally {
            // 恢复原始配置
            this.parsingConfig = originalConfig;
        }
    }
    
    // 批量解析
    async parseBatchArchs(archConfigs) {
        const results = [];
        const errors = [];
        
        for (const config of archConfigs) {
            try {
                const result = this.parseCalendarArch(
                    config.arch,
                    config.models,
                    config.modelName,
                    config.options
                );
                results.push({ config, result, success: true });
            } catch (error) {
                errors.push({ config, error, success: false });
            }
        }
        
        return {
            results: results,
            errors: errors,
            total: archConfigs.length,
            successful: results.length,
            failed: errors.length
        };
    }
    
    // 处理解析错误
    handleParseError(error, arch, modelName) {
        console.error(`Calendar arch parsing error for model ${modelName}:`, error);
        
        // 记录错误详情
        const errorDetail = {
            error: error.message,
            modelName: modelName,
            archString: new XMLSerializer().serializeToString(arch),
            timestamp: Date.now()
        };
        
        // 可以在这里添加错误报告逻辑
        this.reportParseError(errorDetail);
    }
    
    // 报告解析错误
    reportParseError(errorDetail) {
        // 这里可以实现错误报告逻辑
        // 例如发送到错误跟踪服务
        console.warn('Calendar arch parse error reported:', errorDetail);
    }
    
    // 获取解析统计
    getParseStatistics() {
        return {
            ...this.parseStatistics,
            cacheSize: this.parseCache.size,
            cacheHitRate: this.parseStatistics.totalParses > 0 
                ? (this.parseStatistics.cacheHits / this.parseStatistics.totalParses * 100).toFixed(2) + '%'
                : '0%'
        };
    }
    
    // 清理缓存
    clearCache() {
        this.parseCache.clear();
        this.fieldMappingCache.clear();
    }
    
    // 预热缓存
    async preloadCommonArchs(commonArchConfigs) {
        for (const config of commonArchConfigs) {
            try {
                await this.parseCalendarArch(config.arch, config.models, config.modelName);
            } catch (error) {
                console.warn(`Failed to preload arch for ${config.modelName}:`, error);
            }
        }
    }
    
    // 导出解析配置
    exportParsingConfiguration() {
        const config = {
            exportDate: new Date().toISOString(),
            parsingConfig: this.parsingConfig,
            statistics: this.getParseStatistics(),
            cacheKeys: Array.from(this.parseCache.keys())
        };
        
        const blob = new Blob([JSON.stringify(config, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `calendar_arch_parsing_config_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
    }
    
    // 销毁管理器
    destroy() {
        // 清理缓存
        this.clearCache();
        
        // 重置统计
        this.parseStatistics = {
            totalParses: 0,
            cacheHits: 0,
            cacheMisses: 0,
            errors: 0
        };
    }
}

// 使用示例
const archParsingManager = new CalendarArchParsingManager();

// 解析单个架构
const result = archParsingManager.parseCalendarArch(arch, models, 'calendar.event');

// 批量解析
const batchResult = await archParsingManager.parseBatchArchs([
    { arch: arch1, models: models1, modelName: 'calendar.event' },
    { arch: arch2, models: models2, modelName: 'project.task' }
]);

// 获取统计信息
const stats = archParsingManager.getParseStatistics();
console.log('Parse statistics:', stats);
```

## 技术特点

### 1. XML架构解析
- **节点遍历**: 完整的XML节点遍历和解析
- **属性提取**: 智能的属性提取和类型转换
- **配置映射**: 灵活的配置映射机制
- **错误处理**: 完善的解析错误处理

### 2. 字段映射管理
- **字段收集**: 自动收集所有相关字段
- **属性映射**: 建立字段属性映射关系
- **类型验证**: 验证字段类型和存在性
- **默认处理**: 智能的默认值处理

### 3. 配置验证
- **必需验证**: 验证必需配置的存在
- **类型检查**: 检查配置值的类型和范围
- **依赖验证**: 验证配置间的依赖关系
- **错误报告**: 详细的错误信息报告

### 4. 性能优化
- **解析缓存**: 缓存解析结果避免重复解析
- **字段优化**: 优化字段集合和映射
- **配置压缩**: 压缩和优化配置对象
- **批量处理**: 支持批量架构解析

## 设计模式

### 1. 解析器模式 (Parser Pattern)
- **架构解析**: 专门的XML架构解析器
- **节点处理**: 不同节点的专门处理逻辑
- **结果构建**: 结构化的解析结果构建

### 2. 建造者模式 (Builder Pattern)
- **配置构建**: 逐步构建日历配置对象
- **字段映射**: 逐步建立字段映射关系
- **过滤器配置**: 逐步配置过滤器信息

### 3. 策略模式 (Strategy Pattern)
- **解析策略**: 不同节点的解析策略
- **验证策略**: 不同的配置验证策略
- **优化策略**: 不同的性能优化策略

### 4. 工厂模式 (Factory Pattern)
- **配置工厂**: 根据架构创建配置对象
- **映射工厂**: 创建字段映射关系
- **过滤器工厂**: 创建过滤器配置

## 注意事项

1. **XML格式**: 确保XML架构格式的正确性
2. **字段存在**: 验证引用字段在模型中的存在性
3. **类型转换**: 注意属性值的类型转换和验证
4. **性能影响**: 避免频繁的架构解析操作

## 扩展建议

1. **解析缓存**: 增强架构解析结果的缓存机制
2. **验证增强**: 添加更多的配置验证规则
3. **错误恢复**: 增强解析错误的恢复机制
4. **性能监控**: 添加解析性能的监控和优化
5. **扩展支持**: 支持更多的日历配置选项

该日历架构解析器为Odoo Web客户端提供了强大的日历视图配置解析功能，通过XML解析、字段映射和配置验证确保了日历视图的正确配置和高效运行。

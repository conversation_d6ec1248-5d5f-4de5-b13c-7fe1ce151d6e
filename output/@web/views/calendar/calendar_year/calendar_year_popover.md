# CalendarYearPopover - 日历年视图弹出框

## 概述

`calendar_year_popover.js` 是 Odoo Web 客户端日历年视图的弹出框组件，提供了年视图中日期的事件详情显示功能。该模块包含109行代码，是一个OWL组件，专门用于在年视图中点击日期时显示该日期的所有事件，具备事件分组、记录排序、对话框显示、操作按钮等特性，是日历年视图系统中事件交互的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/calendar/calendar_year/calendar_year_popover.js`
- **行数**: 109
- **模块**: `@web/views/calendar/calendar_year/calendar_year_popover`

## 依赖关系

```javascript
// 核心依赖
'@web/core/dialog/dialog'        // 对话框组件
'@web/core/l10n/dates'           // 日期本地化
'@web/views/calendar/colors'     // 颜色管理
'@web/views/calendar/utils'      // 日历工具函数
'@odoo/owl'                      // OWL框架
```

## 主要组件定义

### 1. CalendarYearPopover - 日历年视图弹出框

```javascript
class CalendarYearPopover extends Component {
    static components = { Dialog };
    static template = "web.CalendarYearPopover";
    static subTemplates = {
        popover: "web.CalendarYearPopover.popover",
        body: "web.CalendarYearPopover.body",
        footer: "web.CalendarYearPopover.footer",
        record: "web.CalendarYearPopover.record",
    };
    static props = {
        close: Function,
        date: true,
        model: Object,
        records: Array,
        createRecord: Function,
        deleteRecord: Function,
        editRecord: Function,
    };
}
```

**组件特性**:
- **对话框集成**: 集成Dialog组件
- **模板系统**: 使用主模板和子模板结构
- **属性接口**: 定义清晰的属性接口
- **操作支持**: 支持创建、编辑、删除操作

## 核心功能

### 1. 模板结构

```javascript
static subTemplates = {
    popover: "web.CalendarYearPopover.popover",    // 弹出框容器
    body: "web.CalendarYearPopover.body",          // 主体内容
    footer: "web.CalendarYearPopover.footer",      // 底部操作
    record: "web.CalendarYearPopover.record",      // 记录项
};
```

**模板结构功能**:
- **弹出框模板**: 定义弹出框的整体结构
- **主体模板**: 定义事件列表显示区域
- **底部模板**: 定义操作按钮区域
- **记录模板**: 定义单个事件的显示模板

### 2. 属性接口

```javascript
static props = {
    close: Function,        // 关闭回调函数
    date: true,             // 选中的日期
    model: Object,          // 日历模型
    records: Array,         // 事件记录数组
    createRecord: Function, // 创建记录回调
    deleteRecord: Function, // 删除记录回调
    editRecord: Function,   // 编辑记录回调
};
```

**属性接口功能**:
- **关闭控制**: 提供弹出框关闭功能
- **日期传递**: 传递选中的日期
- **数据传递**: 传递模型和记录数据
- **操作回调**: 提供CRUD操作回调

### 3. 记录分组

```javascript
get recordGroups() {
    return this.computeRecordGroups();
}

computeRecordGroups() {
    const recordGroups = this.groupRecords();
    return this.getSortedRecordGroups(recordGroups);
}

groupRecords() {
    const recordGroups = {};
    for (const record of this.props.records) {
        const start = record.start;
        const end = record.end;
        
        // 根据时间范围分组
        const groupKey = this.getGroupKey(start, end);
        
        if (!recordGroups[groupKey]) {
            recordGroups[groupKey] = {
                key: groupKey,
                title: this.getGroupTitle(start, end),
                records: []
            };
        }
        
        recordGroups[groupKey].records.push({
            ...record,
            color: getColor(record.colorValue),
            timeSpan: getFormattedDateSpan(start, end)
        });
    }
    
    return recordGroups;
}
```

**分组功能**:
- **时间分组**: 根据事件时间范围进行分组
- **分组键**: 生成唯一的分组键
- **分组标题**: 生成分组显示标题
- **记录增强**: 增强记录数据添加颜色和时间信息

### 4. 对话框标题

```javascript
get dialogTitle() {
    return formatDate(this.props.date, { format: "DDD" });
}
```

**标题功能**:
- **日期格式化**: 使用本地化格式化日期
- **标题显示**: 显示选中日期作为对话框标题
- **格式控制**: 使用DDD格式显示完整日期
- **本地化**: 支持本地化的日期显示

### 5. 记录排序

```javascript
getSortedRecordGroups(recordGroups) {
    const sortedGroups = Object.values(recordGroups);
    
    // 按时间排序分组
    sortedGroups.sort((a, b) => {
        const aTime = a.records[0].start;
        const bTime = b.records[0].start;
        return aTime.valueOf() - bTime.valueOf();
    });
    
    // 排序每个分组内的记录
    for (const group of sortedGroups) {
        group.records.sort((a, b) => {
            // 首先按开始时间排序
            const timeDiff = a.start.valueOf() - b.start.valueOf();
            if (timeDiff !== 0) {
                return timeDiff;
            }
            
            // 然后按标题排序
            return (a.title || '').localeCompare(b.title || '');
        });
    }
    
    return sortedGroups;
}
```

**排序功能**:
- **分组排序**: 按时间顺序排序分组
- **记录排序**: 排序每个分组内的记录
- **多级排序**: 先按时间后按标题排序
- **本地化排序**: 使用本地化的字符串比较

## 使用场景

### 1. 年视图弹出框管理器

```javascript
// 年视图弹出框管理器
class YearPopoverManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置弹出框配置
        this.popoverConfig = {
            enableGrouping: true,
            enableSorting: true,
            enableFiltering: true,
            enablePagination: true,
            maxRecordsPerPage: 10,
            groupByTime: true,
            sortByTime: true
        };
        
        // 设置弹出框实例
        this.popoverInstances = new Map();
        
        // 设置分组策略
        this.groupingStrategies = new Map();
        
        // 设置排序策略
        this.sortingStrategies = new Map();
        
        // 设置弹出框统计
        this.popoverStatistics = {
            totalPopovers: 0,
            activePopovers: 0,
            totalRecords: 0,
            totalGroups: 0
        };
        
        this.initializePopoverSystem();
    }
    
    // 初始化弹出框系统
    initializePopoverSystem() {
        // 注册分组策略
        this.registerGroupingStrategies();
        
        // 注册排序策略
        this.registerSortingStrategies();
        
        // 创建增强的弹出框组件
        this.createEnhancedPopover();
    }
    
    // 注册分组策略
    registerGroupingStrategies() {
        // 按时间分组
        this.groupingStrategies.set('time', (records) => {
            const groups = {};
            
            for (const record of records) {
                const timeKey = this.getTimeGroupKey(record);
                
                if (!groups[timeKey]) {
                    groups[timeKey] = {
                        key: timeKey,
                        title: this.getTimeGroupTitle(record),
                        records: []
                    };
                }
                
                groups[timeKey].records.push(record);
            }
            
            return groups;
        });
        
        // 按类型分组
        this.groupingStrategies.set('type', (records) => {
            const groups = {};
            
            for (const record of records) {
                const typeKey = record.model || 'default';
                
                if (!groups[typeKey]) {
                    groups[typeKey] = {
                        key: typeKey,
                        title: this.getTypeGroupTitle(typeKey),
                        records: []
                    };
                }
                
                groups[typeKey].records.push(record);
            }
            
            return groups;
        });
        
        // 按优先级分组
        this.groupingStrategies.set('priority', (records) => {
            const groups = {};
            
            for (const record of records) {
                const priorityKey = record.priority || 'normal';
                
                if (!groups[priorityKey]) {
                    groups[priorityKey] = {
                        key: priorityKey,
                        title: this.getPriorityGroupTitle(priorityKey),
                        records: []
                    };
                }
                
                groups[priorityKey].records.push(record);
            }
            
            return groups;
        });
    }
    
    // 注册排序策略
    registerSortingStrategies() {
        // 按时间排序
        this.sortingStrategies.set('time', (records) => {
            return records.sort((a, b) => {
                const timeDiff = a.start.valueOf() - b.start.valueOf();
                if (timeDiff !== 0) return timeDiff;
                
                return (a.title || '').localeCompare(b.title || '');
            });
        });
        
        // 按标题排序
        this.sortingStrategies.set('title', (records) => {
            return records.sort((a, b) => {
                return (a.title || '').localeCompare(b.title || '');
            });
        });
        
        // 按优先级排序
        this.sortingStrategies.set('priority', (records) => {
            const priorityOrder = { 'high': 0, 'normal': 1, 'low': 2 };
            
            return records.sort((a, b) => {
                const aPriority = priorityOrder[a.priority] || 1;
                const bPriority = priorityOrder[b.priority] || 1;
                
                if (aPriority !== bPriority) {
                    return aPriority - bPriority;
                }
                
                return a.start.valueOf() - b.start.valueOf();
            });
        });
    }
    
    // 创建增强的弹出框组件
    createEnhancedPopover() {
        const originalPopover = CalendarYearPopover;
        
        this.EnhancedYearPopover = class extends originalPopover {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加过滤功能
                this.addFilteringSupport();
                
                // 添加分页功能
                this.addPaginationSupport();
                
                // 添加搜索功能
                this.addSearchSupport();
            }
            
            addEnhancedFeatures() {
                // 添加状态管理
                this.state = useState({
                    currentPage: 1,
                    searchQuery: '',
                    selectedGroup: 'all',
                    sortBy: 'time',
                    groupBy: 'time'
                });
                
                // 添加计算属性
                this.addComputedProperties();
            }
            
            addComputedProperties() {
                // 过滤后的记录
                Object.defineProperty(this, 'filteredRecords', {
                    get: function() {
                        let records = this.props.records;
                        
                        // 应用搜索过滤
                        if (this.state.searchQuery) {
                            records = this.applySearchFilter(records);
                        }
                        
                        // 应用分组过滤
                        if (this.state.selectedGroup !== 'all') {
                            records = this.applyGroupFilter(records);
                        }
                        
                        return records;
                    }
                });
                
                // 分页记录
                Object.defineProperty(this, 'paginatedRecords', {
                    get: function() {
                        const records = this.filteredRecords;
                        const pageSize = this.popoverConfig.maxRecordsPerPage;
                        const startIndex = (this.state.currentPage - 1) * pageSize;
                        const endIndex = startIndex + pageSize;
                        
                        return records.slice(startIndex, endIndex);
                    }
                });
                
                // 总页数
                Object.defineProperty(this, 'totalPages', {
                    get: function() {
                        const totalRecords = this.filteredRecords.length;
                        const pageSize = this.popoverConfig.maxRecordsPerPage;
                        return Math.ceil(totalRecords / pageSize);
                    }
                });
            }
            
            addFilteringSupport() {
                // 应用搜索过滤
                this.applySearchFilter = (records) => {
                    const query = this.state.searchQuery.toLowerCase();
                    
                    return records.filter(record => {
                        const title = (record.title || '').toLowerCase();
                        const description = (record.description || '').toLowerCase();
                        
                        return title.includes(query) || description.includes(query);
                    });
                };
                
                // 应用分组过滤
                this.applyGroupFilter = (records) => {
                    const groupKey = this.state.selectedGroup;
                    
                    return records.filter(record => {
                        return this.getRecordGroupKey(record) === groupKey;
                    });
                };
                
                // 获取记录分组键
                this.getRecordGroupKey = (record) => {
                    switch (this.state.groupBy) {
                        case 'time':
                            return this.getTimeGroupKey(record);
                        case 'type':
                            return record.model || 'default';
                        case 'priority':
                            return record.priority || 'normal';
                        default:
                            return 'all';
                    }
                };
            }
            
            addPaginationSupport() {
                // 下一页
                this.nextPage = () => {
                    if (this.state.currentPage < this.totalPages) {
                        this.state.currentPage++;
                    }
                };
                
                // 上一页
                this.previousPage = () => {
                    if (this.state.currentPage > 1) {
                        this.state.currentPage--;
                    }
                };
                
                // 跳转到指定页
                this.goToPage = (page) => {
                    if (page >= 1 && page <= this.totalPages) {
                        this.state.currentPage = page;
                    }
                };
                
                // 重置分页
                this.resetPagination = () => {
                    this.state.currentPage = 1;
                };
            }
            
            addSearchSupport() {
                // 搜索记录
                this.searchRecords = (query) => {
                    this.state.searchQuery = query;
                    this.resetPagination();
                };
                
                // 清除搜索
                this.clearSearch = () => {
                    this.state.searchQuery = '';
                    this.resetPagination();
                };
                
                // 高亮搜索结果
                this.highlightSearchResults = (text) => {
                    if (!this.state.searchQuery) {
                        return text;
                    }
                    
                    const query = this.state.searchQuery;
                    const regex = new RegExp(`(${query})`, 'gi');
                    
                    return text.replace(regex, '<mark>$1</mark>');
                };
            }
            
            // 重写记录分组方法
            computeRecordGroups() {
                const groupingStrategy = this.groupingStrategies.get(this.state.groupBy);
                const sortingStrategy = this.sortingStrategies.get(this.state.sortBy);
                
                if (!groupingStrategy || !sortingStrategy) {
                    return super.computeRecordGroups();
                }
                
                // 使用过滤后的记录
                const records = this.paginatedRecords;
                
                // 应用分组策略
                const recordGroups = groupingStrategy(records);
                
                // 应用排序策略
                for (const group of Object.values(recordGroups)) {
                    group.records = sortingStrategy(group.records);
                }
                
                return this.getSortedRecordGroups(recordGroups);
            }
            
            // 获取时间分组键
            getTimeGroupKey(record) {
                const start = record.start;
                
                if (record.allDay) {
                    return 'all_day';
                } else if (start.hour < 12) {
                    return 'morning';
                } else if (start.hour < 18) {
                    return 'afternoon';
                } else {
                    return 'evening';
                }
            }
            
            // 获取时间分组标题
            getTimeGroupTitle(record) {
                const groupKey = this.getTimeGroupKey(record);
                
                const titles = {
                    'all_day': 'All Day Events',
                    'morning': 'Morning (6:00 - 12:00)',
                    'afternoon': 'Afternoon (12:00 - 18:00)',
                    'evening': 'Evening (18:00 - 24:00)'
                };
                
                return titles[groupKey] || 'Other';
            }
            
            // 获取类型分组标题
            getTypeGroupTitle(typeKey) {
                const titles = {
                    'calendar.event': 'Calendar Events',
                    'project.task': 'Tasks',
                    'crm.lead': 'Leads',
                    'default': 'Other Events'
                };
                
                return titles[typeKey] || typeKey;
            }
            
            // 获取优先级分组标题
            getPriorityGroupTitle(priorityKey) {
                const titles = {
                    'high': 'High Priority',
                    'normal': 'Normal Priority',
                    'low': 'Low Priority'
                };
                
                return titles[priorityKey] || 'Unknown Priority';
            }
            
            // 更改分组方式
            changeGroupBy(groupBy) {
                this.state.groupBy = groupBy;
                this.resetPagination();
            }
            
            // 更改排序方式
            changeSortBy(sortBy) {
                this.state.sortBy = sortBy;
                this.resetPagination();
            }
            
            // 获取统计信息
            getStatistics() {
                return {
                    totalRecords: this.props.records.length,
                    filteredRecords: this.filteredRecords.length,
                    currentPage: this.state.currentPage,
                    totalPages: this.totalPages,
                    groupBy: this.state.groupBy,
                    sortBy: this.state.sortBy
                };
            }
        };
    }
    
    // 获取时间分组键
    getTimeGroupKey(record) {
        const start = record.start;
        
        if (record.allDay) {
            return 'all_day';
        } else if (start.hour < 12) {
            return 'morning';
        } else if (start.hour < 18) {
            return 'afternoon';
        } else {
            return 'evening';
        }
    }
    
    // 创建弹出框实例
    createPopover(props) {
        const popoverId = this.generatePopoverId();
        
        const popoverInstance = new this.EnhancedYearPopover();
        popoverInstance.props = props;
        
        this.popoverInstances.set(popoverId, {
            id: popoverId,
            instance: popoverInstance,
            props: props,
            createdAt: Date.now(),
            status: 'active'
        });
        
        // 更新统计
        this.popoverStatistics.totalPopovers++;
        this.popoverStatistics.activePopovers++;
        this.popoverStatistics.totalRecords += props.records.length;
        
        return popoverId;
    }
    
    // 关闭弹出框
    closePopover(popoverId) {
        const popoverData = this.popoverInstances.get(popoverId);
        
        if (popoverData) {
            popoverData.status = 'closed';
            this.popoverInstances.delete(popoverId);
            
            // 更新统计
            this.popoverStatistics.activePopovers--;
        }
    }
    
    // 生成弹出框ID
    generatePopoverId() {
        return `year_popover_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    // 获取弹出框统计
    getPopoverStatistics() {
        return {
            ...this.popoverStatistics,
            instanceCount: this.popoverInstances.size,
            groupingStrategies: this.groupingStrategies.size,
            sortingStrategies: this.sortingStrategies.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 关闭所有弹出框
        for (const [popoverId] of this.popoverInstances) {
            this.closePopover(popoverId);
        }
        
        // 清理策略
        this.groupingStrategies.clear();
        this.sortingStrategies.clear();
        
        // 重置统计
        this.popoverStatistics = {
            totalPopovers: 0,
            activePopovers: 0,
            totalRecords: 0,
            totalGroups: 0
        };
    }
}

// 使用示例
const yearPopoverManager = new YearPopoverManager();

// 创建弹出框
const popoverId = yearPopoverManager.createPopover({
    date: selectedDate,
    records: dayRecords,
    model: calendarModel,
    createRecord: createRecordHandler,
    editRecord: editRecordHandler,
    deleteRecord: deleteRecordHandler,
    close: closeHandler
});

// 获取统计信息
const stats = yearPopoverManager.getPopoverStatistics();
console.log('Year popover statistics:', stats);
```

## 技术特点

### 1. 专门设计
- **年视图专用**: 专门为年视图设计的弹出框
- **多记录显示**: 支持显示多个事件记录
- **分组展示**: 智能的事件分组展示
- **操作集成**: 集成完整的CRUD操作

### 2. 数据处理
- **记录分组**: 智能的记录分组算法
- **排序功能**: 多级排序支持
- **格式化**: 本地化的日期时间格式化
- **颜色管理**: 自动的事件颜色管理

### 3. 用户体验
- **对话框**: 使用对话框提供更好的用户体验
- **模板系统**: 灵活的模板系统
- **操作便捷**: 便捷的事件操作
- **信息丰富**: 丰富的事件信息展示

### 4. 扩展性
- **模板可定制**: 支持模板定制
- **分组策略**: 可扩展的分组策略
- **排序策略**: 可扩展的排序策略
- **过滤功能**: 支持事件过滤

## 设计模式

### 1. 组件模式 (Component Pattern)
- **封装性**: 封装年视图弹出框功能
- **可复用**: 高度可复用的组件
- **接口标准**: 标准化的组件接口

### 2. 模板模式 (Template Pattern)
- **结构定义**: 定义弹出框结构
- **内容填充**: 动态填充事件内容
- **样式控制**: 控制显示样式

### 3. 策略模式 (Strategy Pattern)
- **分组策略**: 不同的事件分组策略
- **排序策略**: 不同的事件排序策略
- **显示策略**: 不同的内容显示策略

### 4. 工厂模式 (Factory Pattern)
- **组件创建**: 动态创建弹出框组件
- **分组创建**: 创建事件分组
- **记录创建**: 创建增强的记录对象

## 注意事项

1. **性能优化**: 避免大量事件时的性能问题
2. **内存管理**: 及时清理弹出框实例
3. **用户体验**: 确保良好的交互体验
4. **数据一致性**: 保持数据的一致性

## 扩展建议

1. **更多分组**: 支持更多的分组方式
2. **高级过滤**: 添加高级过滤功能
3. **批量操作**: 支持批量事件操作
4. **导出功能**: 支持事件数据导出
5. **打印功能**: 支持事件列表打印

该年视图弹出框组件为Odoo Web客户端提供了强大的年视图事件展示功能，通过智能分组、多级排序和丰富的操作支持确保了用户友好的事件管理界面。

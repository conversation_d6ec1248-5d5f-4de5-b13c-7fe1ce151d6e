# CalendarYearRenderer - 日历年视图渲染器

## 概述

`calendar_year_renderer.js` 是 Odoo Web 客户端日历年视图的渲染器组件，提供了年视图的完整渲染功能。该模块包含236行代码，是一个OWL组件，专门用于渲染12个月的日历网格，具备FullCalendar集成、月份渲染、事件显示、弹出框管理等特性，是日历视图系统中年视图渲染的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/calendar/calendar_year/calendar_year_renderer.js`
- **行数**: 236
- **模块**: `@web/views/calendar/calendar_year/calendar_year_renderer`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/localization'                                       // 本地化服务
'@web/core/utils/timing'                                            // 时间工具
'@web/views/calendar/colors'                                        // 颜色管理
'@web/views/calendar/hooks'                                         // 日历钩子
'@web/views/calendar/calendar_year/calendar_year_popover'           // 年视图弹出框
'@web/views/calendar/calendar_common/calendar_common_week_column'   // 周列组件
'@web/core/l10n/dates'                                              // 日期本地化
'@odoo/owl'                                                         // OWL框架
```

## 主要组件定义

### 1. CalendarYearRenderer - 日历年视图渲染器

```javascript
class CalendarYearRenderer extends Component {
    static components = {
        Popover: CalendarYearPopover,
    };
    static template = "web.CalendarYearRenderer";
    static props = {
        model: Object,
        displayName: { type: String, optional: true },
        isWeekendVisible: { type: Boolean, optional: true },
        createRecord: Function,
        editRecord: Function,
        deleteRecord: Function,
        setDate: { type: Function, optional: true },
    };

    setup() {
        this.months = luxon.Info.months();
        this.fcs = {};
        for (const month of this.months) {
            this.fcs[month] = useFullCalendar(
                `fullCalendar-${month}`,
                this.getOptionsForMonth(month)
            );
        }
        this.popover = useCalendarPopover(this.constructor.components.Popover);
        this.rootRef = useRef("root");
        this.onWindowResizeDebounced = useDebounced(this.onWindowResize, 200);

        useEffect(() => {
            this.updateSize();
        });
    }
}
```

**组件特性**:
- **弹出框集成**: 集成年视图弹出框组件
- **模板定义**: 使用CalendarYearRenderer模板
- **属性接口**: 定义清晰的属性接口
- **多月渲染**: 同时渲染12个月的日历

## 核心功能

### 1. 月份初始化

```javascript
setup() {
    this.months = luxon.Info.months();
    this.fcs = {};
    for (const month of this.months) {
        this.fcs[month] = useFullCalendar(
            `fullCalendar-${month}`,
            this.getOptionsForMonth(month)
        );
    }
}
```

**初始化功能**:
- **月份获取**: 获取本地化的月份信息
- **FullCalendar实例**: 为每个月创建独立的FullCalendar实例
- **配置生成**: 为每个月生成专门的配置
- **实例管理**: 管理12个FullCalendar实例

### 2. 月份配置生成

```javascript
getOptionsForMonth(month) {
    const monthIndex = this.months.indexOf(month);
    const { model } = this.props;
    const { date } = model;

    const monthStart = date.set({ month: monthIndex + 1, day: 1 });
    const monthEnd = monthStart.endOf('month');

    return {
        initialView: "dayGridMonth",
        initialDate: monthStart.toJSDate(),
        headerToolbar: false,
        height: "auto",
        dayMaxEvents: false,
        showNonCurrentDates: false,
        fixedWeekCount: false,
        weekNumbers: getLocalWeekNumber() !== null,
        firstDay: localization.weekStart,
        locale: localization.code,
        events: this.getEventsForMonth(monthStart, monthEnd),
        eventClick: this.onEventClick.bind(this),
        dateClick: this.onDateClick.bind(this),
        eventDidMount: this.onEventDidMount.bind(this),
        dayCellDidMount: this.onDayCellDidMount.bind(this),
        datesSet: (info) => this.onDatesSet(info, month),
    };
}
```

**配置功能**:
- **月份计算**: 计算每个月的开始和结束日期
- **视图配置**: 配置为dayGridMonth视图
- **事件过滤**: 为每个月过滤相应的事件
- **本地化**: 应用本地化设置

### 3. 事件数据处理

```javascript
getEventsForMonth(monthStart, monthEnd) {
    const { model } = this.props;
    const events = [];

    for (const record of Object.values(model.data.records)) {
        const event = this.recordToEvent(record);
        if (event && this.isEventInMonth(event, monthStart, monthEnd)) {
            events.push(event);
        }
    }

    return events;
}

isEventInMonth(event, monthStart, monthEnd) {
    const eventStart = DateTime.fromJSDate(event.start);
    const eventEnd = event.end ? DateTime.fromJSDate(event.end) : eventStart;

    // 检查事件是否与月份有重叠
    return eventStart <= monthEnd && eventEnd >= monthStart;
}

recordToEvent(record) {
    const { model } = this.props;
    const { fieldMapping } = model.meta;

    const startField = fieldMapping.date_start;
    const endField = fieldMapping.date_stop || fieldMapping.date_start;
    const colorField = fieldMapping.color;

    const start = record[startField];
    const end = record[endField];

    if (!start) {
        return null;
    }

    const colorValue = colorField ? record[colorField] : null;

    return {
        id: record.id,
        title: record.display_name || record.name,
        start: start.toJSDate(),
        end: end ? end.toJSDate() : start.toJSDate(),
        backgroundColor: getColor(colorValue),
        borderColor: getColor(colorValue),
        extendedProps: {
            record: record,
        },
    };
}
```

**事件处理功能**:
- **月份过滤**: 为每个月过滤相应的事件
- **重叠检测**: 检测事件是否与月份有重叠
- **数据转换**: 将Odoo记录转换为FullCalendar事件
- **颜色应用**: 应用事件颜色

### 4. 事件交互处理

```javascript
onEventClick(info) {
    // 年视图中事件点击不显示弹出框
    // 而是导航到该事件的详细视图
    const { record } = info.event.extendedProps;
    this.props.editRecord(record);
}

onDateClick(info) {
    const clickedDate = DateTime.fromJSDate(info.date);
    const { model } = this.props;

    // 获取该日期的所有事件
    const dayEvents = this.getEventsForDate(clickedDate);

    if (dayEvents.length > 0) {
        // 显示该日期的事件列表弹出框
        this.popover.open(info.dayEl, {
            date: clickedDate,
            records: dayEvents.map(event => event.extendedProps.record),
            model: model,
            createRecord: this.props.createRecord,
            editRecord: this.props.editRecord,
            deleteRecord: this.props.deleteRecord,
        });
    } else {
        // 创建新事件
        const context = {
            default_start: clickedDate.toISO(),
            default_stop: clickedDate.toISO(),
        };
        this.props.createRecord(context);
    }
}
```

**交互处理功能**:
- **事件点击**: 直接编辑事件而不显示弹出框
- **日期点击**: 显示该日期的事件列表或创建新事件
- **弹出框管理**: 管理年视图弹出框的显示
- **上下文传递**: 传递适当的上下文信息
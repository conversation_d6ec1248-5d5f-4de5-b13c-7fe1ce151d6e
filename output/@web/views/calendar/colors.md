# Colors - 颜色管理

## 概述

`colors.js` 是 Odoo Web 客户端日历视图的颜色管理模块，提供了日历事件颜色的分配和管理功能。该模块包含28行代码，是一个轻量级的工具模块，专门用于处理日历事件的颜色映射、CSS颜色验证、自动颜色分配等功能，具备颜色缓存、格式验证、自动分配、数值映射等特性，是日历视图系统中颜色处理的核心工具。

## 文件信息
- **路径**: `/web/static/src/views/calendar/colors.js`
- **行数**: 28
- **模块**: `@web/views/calendar/colors`

## 依赖关系

```javascript
// 无外部依赖
// 纯JavaScript实现的颜色管理工具
```

## 核心常量

### 1. CSS颜色正则表达式

```javascript
const CSS_COLOR_REGEX = /^((#[A-F0-9]{3})|(#[A-F0-9]{6})|((hsl|rgb)a?\(\s*(?:(\s*\d{1,3}%?\s*),?){3}(\s*,[0-9.]{1,4})?\))|)$/i;
```

**正则表达式功能**:
- **十六进制颜色**: 支持#RGB和#RRGGBB格式
- **RGB颜色**: 支持rgb()和rgba()格式
- **HSL颜色**: 支持hsl()和hsla()格式
- **大小写不敏感**: 使用i标志支持大小写不敏感匹配

### 2. 颜色映射缓存

```javascript
const colorMap = new Map();
```

**缓存功能**:
- **键值映射**: 建立键到颜色的映射关系
- **性能优化**: 避免重复计算颜色值
- **内存管理**: 使用Map提供高效的键值存储
- **持久化**: 在模块生命周期内持久化颜色映射

## 主要函数

### 1. getColor - 获取颜色

```javascript
function getColor(key) {
    if (!key) {
        return false;
    }
    if (colorMap.has(key)) {
        return colorMap.get(key);
    }

    // check if the key is a css color
    if (typeof key === "string" && key.match(CSS_COLOR_REGEX)) {
        colorMap.set(key, key);
    } else if (typeof key === "number") {
        colorMap.set(key, ((key - 1) % 55) + 1);
    } else {
        colorMap.set(key, (((colorMap.size + 1) * 5) % 24) + 1);
    }

    return colorMap.get(key);
}
```

**函数功能**:
- **输入验证**: 验证输入键的有效性
- **缓存检查**: 检查颜色是否已缓存
- **CSS颜色**: 直接使用有效的CSS颜色
- **数值映射**: 将数值映射到颜色索引
- **自动分配**: 为其他类型自动分配颜色

## 核心功能

### 1. 颜色验证

```javascript
if (typeof key === "string" && key.match(CSS_COLOR_REGEX)) {
    colorMap.set(key, key);
}
```

**验证功能**:
- **类型检查**: 检查输入是否为字符串
- **格式验证**: 使用正则表达式验证CSS颜色格式
- **直接使用**: 有效的CSS颜色直接使用
- **缓存存储**: 将验证结果存储到缓存

### 2. 数值颜色映射

```javascript
else if (typeof key === "number") {
    colorMap.set(key, ((key - 1) % 55) + 1);
}
```

**数值映射功能**:
- **数值检查**: 检查输入是否为数值
- **循环映射**: 使用模运算实现循环映射
- **范围控制**: 将数值映射到1-55的范围
- **一致性**: 相同数值总是映射到相同颜色

### 3. 自动颜色分配

```javascript
else {
    colorMap.set(key, (((colorMap.size + 1) * 5) % 24) + 1);
}
```

**自动分配功能**:
- **动态分配**: 根据缓存大小动态分配颜色
- **分散算法**: 使用乘法和模运算分散颜色
- **避免冲突**: 尽量避免相邻颜色的冲突
- **循环使用**: 在24种颜色中循环使用

### 4. 缓存管理

```javascript
if (colorMap.has(key)) {
    return colorMap.get(key);
}
```

**缓存管理功能**:
- **快速查找**: 使用Map提供O(1)查找性能
- **避免重计算**: 避免重复计算相同键的颜色
- **内存效率**: 高效的内存使用
- **生命周期**: 在模块生命周期内保持缓存

## 使用场景

### 1. 颜色管理器

```javascript
// 颜色管理器
class ColorManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置颜色配置
        this.colorConfig = {
            enableCustomColors: true,
            enableColorValidation: true,
            enableColorCaching: true,
            enableColorPalette: true,
            maxCacheSize: 1000,
            defaultColorScheme: 'material'
        };
        
        // 设置颜色调色板
        this.colorPalettes = new Map([
            ['material', this.getMaterialColors()],
            ['bootstrap', this.getBootstrapColors()],
            ['custom', this.getCustomColors()]
        ]);
        
        // 设置颜色缓存
        this.colorCache = new Map();
        
        // 设置颜色统计
        this.colorStatistics = {
            totalColors: 0,
            cacheHits: 0,
            cacheMisses: 0,
            validColors: 0,
            invalidColors: 0
        };
        
        this.initializeColorSystem();
    }
    
    // 初始化颜色系统
    initializeColorSystem() {
        // 扩展原始getColor函数
        this.enhanceGetColor();
        
        // 设置颜色验证器
        this.setupColorValidators();
        
        // 设置颜色转换器
        this.setupColorConverters();
    }
    
    // 增强getColor函数
    enhanceGetColor() {
        const originalGetColor = getColor;
        
        // 创建增强版本
        this.getColor = (key, options = {}) => {
            const config = {
                enableValidation: options.enableValidation !== false,
                enableCaching: options.enableCaching !== false,
                palette: options.palette || this.colorConfig.defaultColorScheme,
                format: options.format || 'auto',
                ...options
            };
            
            // 统计
            this.colorStatistics.totalColors++;
            
            // 检查缓存
            const cacheKey = this.generateCacheKey(key, config);
            if (config.enableCaching && this.colorCache.has(cacheKey)) {
                this.colorStatistics.cacheHits++;
                return this.colorCache.get(cacheKey);
            }
            
            this.colorStatistics.cacheMisses++;
            
            // 获取颜色
            let color = originalGetColor(key);
            
            // 应用配置
            if (config.palette !== 'default') {
                color = this.applyColorPalette(color, config.palette);
            }
            
            // 格式转换
            if (config.format !== 'auto') {
                color = this.convertColorFormat(color, config.format);
            }
            
            // 验证颜色
            if (config.enableValidation) {
                const isValid = this.validateColor(color);
                if (isValid) {
                    this.colorStatistics.validColors++;
                } else {
                    this.colorStatistics.invalidColors++;
                    color = this.getFallbackColor();
                }
            }
            
            // 缓存结果
            if (config.enableCaching) {
                this.colorCache.set(cacheKey, color);
            }
            
            return color;
        };
    }
    
    // 生成缓存键
    generateCacheKey(key, config) {
        return `${key}_${config.palette}_${config.format}`;
    }
    
    // 应用颜色调色板
    applyColorPalette(color, paletteName) {
        const palette = this.colorPalettes.get(paletteName);
        
        if (!palette) {
            console.warn(`Color palette ${paletteName} not found`);
            return color;
        }
        
        // 如果颜色是数值索引，从调色板中获取
        if (typeof color === 'number') {
            const paletteIndex = (color - 1) % palette.length;
            return palette[paletteIndex];
        }
        
        return color;
    }
    
    // 转换颜色格式
    convertColorFormat(color, format) {
        switch (format) {
            case 'hex':
                return this.toHex(color);
            case 'rgb':
                return this.toRgb(color);
            case 'hsl':
                return this.toHsl(color);
            case 'rgba':
                return this.toRgba(color);
            default:
                return color;
        }
    }
    
    // 设置颜色验证器
    setupColorValidators() {
        this.validators = {
            css: (color) => {
                return typeof color === 'string' && CSS_COLOR_REGEX.test(color);
            },
            hex: (color) => {
                return typeof color === 'string' && /^#[0-9A-F]{6}$/i.test(color);
            },
            rgb: (color) => {
                return typeof color === 'string' && /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/i.test(color);
            },
            hsl: (color) => {
                return typeof color === 'string' && /^hsl\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*\)$/i.test(color);
            }
        };
    }
    
    // 设置颜色转换器
    setupColorConverters() {
        this.converters = {
            hexToRgb: (hex) => {
                const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
                return result ? {
                    r: parseInt(result[1], 16),
                    g: parseInt(result[2], 16),
                    b: parseInt(result[3], 16)
                } : null;
            },
            rgbToHex: (r, g, b) => {
                return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
            },
            hslToRgb: (h, s, l) => {
                h /= 360;
                s /= 100;
                l /= 100;
                
                const hue2rgb = (p, q, t) => {
                    if (t < 0) t += 1;
                    if (t > 1) t -= 1;
                    if (t < 1/6) return p + (q - p) * 6 * t;
                    if (t < 1/2) return q;
                    if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
                    return p;
                };
                
                let r, g, b;
                
                if (s === 0) {
                    r = g = b = l;
                } else {
                    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
                    const p = 2 * l - q;
                    r = hue2rgb(p, q, h + 1/3);
                    g = hue2rgb(p, q, h);
                    b = hue2rgb(p, q, h - 1/3);
                }
                
                return {
                    r: Math.round(r * 255),
                    g: Math.round(g * 255),
                    b: Math.round(b * 255)
                };
            }
        };
    }
    
    // 获取Material Design颜色
    getMaterialColors() {
        return [
            '#F44336', '#E91E63', '#9C27B0', '#673AB7', '#3F51B5',
            '#2196F3', '#03A9F4', '#00BCD4', '#009688', '#4CAF50',
            '#8BC34A', '#CDDC39', '#FFEB3B', '#FFC107', '#FF9800',
            '#FF5722', '#795548', '#9E9E9E', '#607D8B', '#000000'
        ];
    }
    
    // 获取Bootstrap颜色
    getBootstrapColors() {
        return [
            '#007bff', '#6c757d', '#28a745', '#dc3545', '#ffc107',
            '#17a2b8', '#6f42c1', '#e83e8c', '#fd7e14', '#20c997'
        ];
    }
    
    // 获取自定义颜色
    getCustomColors() {
        return [
            '#1abc9c', '#2ecc71', '#3498db', '#9b59b6', '#34495e',
            '#16a085', '#27ae60', '#2980b9', '#8e44ad', '#2c3e50',
            '#f1c40f', '#e67e22', '#e74c3c', '#ecf0f1', '#95a5a6',
            '#f39c12', '#d35400', '#c0392b', '#bdc3c7', '#7f8c8d'
        ];
    }
    
    // 验证颜色
    validateColor(color) {
        // 使用所有验证器验证
        for (const [name, validator] of Object.entries(this.validators)) {
            if (validator(color)) {
                return true;
            }
        }
        
        // 检查是否为数值索引
        if (typeof color === 'number' && color >= 1 && color <= 55) {
            return true;
        }
        
        return false;
    }
    
    // 获取回退颜色
    getFallbackColor() {
        return '#6c757d'; // Bootstrap secondary color
    }
    
    // 转换为十六进制
    toHex(color) {
        if (typeof color === 'string' && color.startsWith('#')) {
            return color;
        }
        
        if (typeof color === 'number') {
            const palette = this.colorPalettes.get(this.colorConfig.defaultColorScheme);
            const paletteIndex = (color - 1) % palette.length;
            return palette[paletteIndex];
        }
        
        // 尝试从RGB转换
        const rgbMatch = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
        if (rgbMatch) {
            const r = parseInt(rgbMatch[1]);
            const g = parseInt(rgbMatch[2]);
            const b = parseInt(rgbMatch[3]);
            return this.converters.rgbToHex(r, g, b);
        }
        
        return color;
    }
    
    // 转换为RGB
    toRgb(color) {
        if (typeof color === 'string' && color.startsWith('rgb')) {
            return color;
        }
        
        if (typeof color === 'string' && color.startsWith('#')) {
            const rgb = this.converters.hexToRgb(color);
            return rgb ? `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})` : color;
        }
        
        if (typeof color === 'number') {
            const hex = this.toHex(color);
            return this.toRgb(hex);
        }
        
        return color;
    }
    
    // 转换为HSL
    toHsl(color) {
        // 简化实现，实际应用中需要完整的HSL转换
        return color;
    }
    
    // 转换为RGBA
    toRgba(color, alpha = 1) {
        const rgb = this.toRgb(color);
        if (rgb.startsWith('rgb(')) {
            return rgb.replace('rgb(', 'rgba(').replace(')', `, ${alpha})`);
        }
        return color;
    }
    
    // 生成颜色调色板
    generateColorPalette(baseColor, count = 10) {
        const colors = [];
        const baseRgb = this.converters.hexToRgb(baseColor);
        
        if (!baseRgb) {
            return colors;
        }
        
        for (let i = 0; i < count; i++) {
            const factor = (i + 1) / count;
            const r = Math.round(baseRgb.r * factor);
            const g = Math.round(baseRgb.g * factor);
            const b = Math.round(baseRgb.b * factor);
            
            colors.push(this.converters.rgbToHex(r, g, b));
        }
        
        return colors;
    }
    
    // 获取对比色
    getContrastColor(backgroundColor) {
        const rgb = this.converters.hexToRgb(backgroundColor);
        
        if (!rgb) {
            return '#000000';
        }
        
        // 计算亮度
        const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
        
        // 根据亮度返回黑色或白色
        return brightness > 128 ? '#000000' : '#ffffff';
    }
    
    // 获取颜色统计
    getColorStatistics() {
        return {
            ...this.colorStatistics,
            cacheSize: this.colorCache.size,
            paletteCount: this.colorPalettes.size,
            cacheHitRate: this.colorStatistics.totalColors > 0 
                ? (this.colorStatistics.cacheHits / this.colorStatistics.totalColors * 100).toFixed(2) + '%'
                : '0%'
        };
    }
    
    // 清理缓存
    clearCache() {
        this.colorCache.clear();
    }
    
    // 添加自定义调色板
    addColorPalette(name, colors) {
        this.colorPalettes.set(name, colors);
    }
    
    // 移除调色板
    removeColorPalette(name) {
        this.colorPalettes.delete(name);
    }
    
    // 销毁管理器
    destroy() {
        // 清理缓存
        this.clearCache();
        
        // 清理调色板
        this.colorPalettes.clear();
        
        // 重置统计
        this.colorStatistics = {
            totalColors: 0,
            cacheHits: 0,
            cacheMisses: 0,
            validColors: 0,
            invalidColors: 0
        };
    }
}

// 使用示例
const colorManager = new ColorManager();

// 获取颜色
const color1 = colorManager.getColor('user_1', { palette: 'material', format: 'hex' });
const color2 = colorManager.getColor('#ff5722', { enableValidation: true });
const color3 = colorManager.getColor(5, { palette: 'bootstrap' });

// 生成调色板
const palette = colorManager.generateColorPalette('#3498db', 8);

// 获取对比色
const contrastColor = colorManager.getContrastColor('#3498db');

// 获取统计信息
const stats = colorManager.getColorStatistics();
console.log('Color statistics:', stats);
```

## 技术特点

### 1. 轻量级设计
- **简洁实现**: 仅28行代码实现核心功能
- **无依赖**: 不依赖任何外部库
- **高效算法**: 使用高效的颜色分配算法
- **内存友好**: 最小的内存占用

### 2. 智能颜色分配
- **CSS颜色**: 直接支持标准CSS颜色格式
- **数值映射**: 智能的数值到颜色映射
- **自动分配**: 为未知键自动分配颜色
- **分散算法**: 使用数学算法分散颜色分布

### 3. 缓存机制
- **Map存储**: 使用Map提供高效存储
- **避免重计算**: 缓存计算结果避免重复计算
- **内存管理**: 合理的内存使用
- **性能优化**: O(1)的查找性能

### 4. 格式支持
- **十六进制**: 支持#RGB和#RRGGBB格式
- **RGB**: 支持rgb()和rgba()格式
- **HSL**: 支持hsl()和hsla()格式
- **正则验证**: 使用正则表达式验证格式

## 设计模式

### 1. 单例模式 (Singleton Pattern)
- **全局缓存**: 全局唯一的颜色缓存
- **状态共享**: 共享颜色映射状态
- **资源复用**: 复用颜色计算结果

### 2. 策略模式 (Strategy Pattern)
- **分配策略**: 不同类型键的颜色分配策略
- **验证策略**: 不同格式的颜色验证策略
- **映射策略**: 不同的颜色映射策略

### 3. 工厂模式 (Factory Pattern)
- **颜色工厂**: 根据键生成颜色
- **格式工厂**: 生成不同格式的颜色
- **调色板工厂**: 生成颜色调色板

### 4. 缓存模式 (Cache Pattern)
- **结果缓存**: 缓存颜色计算结果
- **性能优化**: 避免重复计算
- **内存管理**: 高效的缓存管理

## 注意事项

1. **内存使用**: 注意颜色缓存的内存使用
2. **颜色冲突**: 避免相似颜色的冲突
3. **格式验证**: 确保颜色格式的正确性
4. **性能考虑**: 避免频繁的颜色计算

## 扩展建议

1. **更多格式**: 支持更多颜色格式
2. **调色板**: 添加预定义的颜色调色板
3. **对比度**: 添加颜色对比度计算
4. **可访问性**: 考虑颜色的可访问性
5. **主题支持**: 支持不同的颜色主题

该颜色管理模块为Odoo Web客户端提供了简洁高效的颜色处理功能，通过智能分配、缓存机制和格式支持确保了日历事件颜色的一致性和美观性。

# CalendarFilterPanel - 日历过滤面板

## 概述

`calendar_filter_panel.js` 是 Odoo Web 客户端日历视图的过滤面板组件，提供了日历事件的过滤和筛选功能。该模块包含210行代码，是一个OWL组件，专门用于管理日历视图的过滤器，具备过滤器显示、自动完成、颜色管理、折叠展开等特性，是日历视图系统中事件过滤的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/calendar/filter_panel/calendar_filter_panel.js`
- **行数**: 210
- **模块**: `@web/views/calendar/filter_panel/calendar_filter_panel`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'                    // 翻译服务
'@web/core/autocomplete/autocomplete'           // 自动完成组件
'@web/core/transition'                          // 过渡动画
'@web/core/utils/hooks'                         // 工具钩子
'@web/views/view_dialogs/select_create_dialog'  // 选择创建对话框
'@web/views/calendar/colors'                    // 颜色管理
'@odoo/owl'                                     // OWL框架
```

## 主要组件定义

### 1. CalendarFilterPanel - 日历过滤面板

```javascript
class CalendarFilterPanel extends Component {
    static components = {
        AutoComplete,
        Transition,
    };
    static template = "web.CalendarFilterPanel";
    static subTemplates = {
        filter: "web.CalendarFilterPanel.filter",
    };
    static props = {
        model: Object,
    };

    setup() {
        this.state = useState({
            collapsed: {},
            fieldRev: 1,
        });
        this.addDialog = useOwnedDialogs();
        this.orm = useService("orm");
    }
}
```

**组件特性**:
- **自动完成**: 集成AutoComplete组件
- **过渡动画**: 集成Transition组件
- **模板系统**: 使用主模板和子模板
- **状态管理**: 管理折叠状态和字段版本

## 核心功能

### 1. 过滤器颜色

```javascript
getFilterColor(filter) {
    return filter.colorIndex !== null ? "o_cw_filter_color_" + getColor(filter.colorIndex) : "";
}
```

**颜色功能**:
- **颜色索引**: 使用过滤器的颜色索引
- **CSS类生成**: 生成对应的CSS类名
- **颜色管理**: 集成颜色管理系统
- **空值处理**: 处理无颜色的情况

### 2. 自动完成配置

```javascript
getAutoCompleteProps(section) {
    return {
        autoSelect: true,
        resetOnSelect: true,
        placeholder: _t("+ Add %s", section.label),
        sources: [
            {
                placeholder: _t("Loading..."),
                options: (request) => this.loadFilterOptions(section, request),
            },
        ],
        onSelect: (option) => this.onFilterSelect(section, option),
    };
}
```

**自动完成功能**:
- **自动选择**: 启用自动选择功能
- **选择重置**: 选择后重置输入
- **占位符**: 本地化的占位符文本
- **选项加载**: 动态加载过滤器选项

### 3. 过滤器选项加载

```javascript
async loadFilterOptions(section, request) {
    const { field, write_model } = section;
    const domain = [];
    
    if (request.trim()) {
        domain.push(['name', 'ilike', request]);
    }
    
    const records = await this.orm.searchRead(
        write_model || section.model,
        domain,
        ['id', 'name', section.avatar_field].filter(Boolean),
        { limit: 8 }
    );
    
    return records.map(record => ({
        value: record.id,
        label: record.name,
        avatar: section.avatar_field ? record[section.avatar_field] : null,
    }));
}
```

**选项加载功能**:
- **搜索域**: 构建搜索域条件
- **模糊搜索**: 支持模糊搜索功能
- **字段读取**: 读取必要的字段信息
- **结果限制**: 限制返回结果数量

### 4. 过滤器选择处理

```javascript
onFilterSelect(section, option) {
    const { field, write_model, write_field } = section;
    
    if (write_model && write_field) {
        // 创建关联记录
        this.createFilterRecord(section, option);
    } else {
        // 直接添加过滤器
        this.addFilter(section, option);
    }
}

async createFilterRecord(section, option) {
    const { write_model, write_field } = section;
    
    const recordData = {
        [write_field]: option.value,
        user_id: this.env.services.user.userId,
    };
    
    try {
        await this.orm.create(write_model, [recordData]);
        
        // 刷新过滤器数据
        this.refreshFilters();
        
    } catch (error) {
        this.env.services.notification.add(
            _t("Failed to add filter"),
            { type: "danger" }
        );
    }
}
```

**选择处理功能**:
- **类型判断**: 判断过滤器类型
- **记录创建**: 创建关联的过滤器记录
- **直接添加**: 直接添加过滤器选项
- **错误处理**: 处理创建失败的情况

### 5. 过滤器切换

```javascript
toggleFilter(section, filter) {
    const { model } = this.props;
    
    // 切换过滤器状态
    filter.active = !filter.active;
    
    // 更新模型过滤器
    model.updateFilter(section.field, filter.value, filter.active);
    
    // 重新加载数据
    model.load();
}

toggleSection(section) {
    const sectionKey = section.field;
    this.state.collapsed[sectionKey] = !this.state.collapsed[sectionKey];
}
```

**切换功能**:
- **状态切换**: 切换过滤器的激活状态
- **模型更新**: 更新模型的过滤器状态
- **数据重载**: 重新加载日历数据
- **折叠控制**: 控制过滤器分组的折叠状态

## 使用场景

### 1. 过滤面板管理器

```javascript
// 过滤面板管理器
class FilterPanelManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置过滤面板配置
        this.filterConfig = {
            enableAutoComplete: true,
            enableColorCoding: true,
            enableGroupCollapse: true,
            enableBulkOperations: true,
            maxFiltersPerGroup: 20,
            searchDelay: 300
        };
        
        // 设置过滤器缓存
        this.filterCache = new Map();
        
        // 设置过滤器分组
        this.filterGroups = new Map();
        
        // 设置过滤器统计
        this.filterStatistics = {
            totalFilters: 0,
            activeFilters: 0,
            totalGroups: 0,
            searchQueries: 0
        };
        
        this.initializeFilterSystem();
    }
    
    // 初始化过滤器系统
    initializeFilterSystem() {
        // 创建增强的过滤面板
        this.createEnhancedFilterPanel();
        
        // 设置过滤器预设
        this.setupFilterPresets();
        
        // 设置批量操作
        this.setupBulkOperations();
        
        // 设置搜索优化
        this.setupSearchOptimization();
    }
    
    // 创建增强的过滤面板
    createEnhancedFilterPanel() {
        const originalPanel = CalendarFilterPanel;
        
        this.EnhancedFilterPanel = class extends originalPanel {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加搜索功能
                this.addSearchFeatures();
                
                // 添加批量操作
                this.addBulkOperations();
                
                // 添加预设管理
                this.addPresetManagement();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                Object.assign(this.state, {
                    searchQuery: '',
                    selectedFilters: new Set(),
                    bulkMode: false,
                    currentPreset: null,
                    filterStats: {}
                });
                
                // 添加计算属性
                this.addComputedProperties();
            }
            
            addComputedProperties() {
                // 过滤后的分组
                Object.defineProperty(this, 'filteredSections', {
                    get: function() {
                        const sections = this.props.model.data.filterSections;
                        
                        if (!this.state.searchQuery) {
                            return sections;
                        }
                        
                        return this.applySearchFilter(sections);
                    }
                });
                
                // 活动过滤器数量
                Object.defineProperty(this, 'activeFilterCount', {
                    get: function() {
                        let count = 0;
                        const sections = this.props.model.data.filterSections;
                        
                        for (const section of Object.values(sections)) {
                            for (const filter of section.filters) {
                                if (filter.active) count++;
                            }
                        }
                        
                        return count;
                    }
                });
                
                // 总过滤器数量
                Object.defineProperty(this, 'totalFilterCount', {
                    get: function() {
                        let count = 0;
                        const sections = this.props.model.data.filterSections;
                        
                        for (const section of Object.values(sections)) {
                            count += section.filters.length;
                        }
                        
                        return count;
                    }
                });
            }
            
            addSearchFeatures() {
                // 搜索过滤器
                this.searchFilters = (query) => {
                    this.state.searchQuery = query.toLowerCase();
                };
                
                // 应用搜索过滤
                this.applySearchFilter = (sections) => {
                    const query = this.state.searchQuery;
                    const filteredSections = {};
                    
                    for (const [key, section] of Object.entries(sections)) {
                        const filteredFilters = section.filters.filter(filter => {
                            const label = (filter.label || '').toLowerCase();
                            const value = (filter.value || '').toString().toLowerCase();
                            
                            return label.includes(query) || value.includes(query);
                        });
                        
                        if (filteredFilters.length > 0) {
                            filteredSections[key] = {
                                ...section,
                                filters: filteredFilters
                            };
                        }
                    }
                    
                    return filteredSections;
                };
                
                // 清除搜索
                this.clearSearch = () => {
                    this.state.searchQuery = '';
                };
                
                // 高亮搜索结果
                this.highlightSearchResults = (text) => {
                    if (!this.state.searchQuery) {
                        return text;
                    }
                    
                    const query = this.state.searchQuery;
                    const regex = new RegExp(`(${query})`, 'gi');
                    
                    return text.replace(regex, '<mark>$1</mark>');
                };
            }
            
            addBulkOperations() {
                // 切换批量模式
                this.toggleBulkMode = () => {
                    this.state.bulkMode = !this.state.bulkMode;
                    if (!this.state.bulkMode) {
                        this.state.selectedFilters.clear();
                    }
                };
                
                // 选择过滤器
                this.selectFilter = (section, filter) => {
                    const filterKey = `${section.field}_${filter.value}`;
                    
                    if (this.state.selectedFilters.has(filterKey)) {
                        this.state.selectedFilters.delete(filterKey);
                    } else {
                        this.state.selectedFilters.add(filterKey);
                    }
                };
                
                // 全选过滤器
                this.selectAllFilters = () => {
                    const sections = this.filteredSections;
                    
                    for (const section of Object.values(sections)) {
                        for (const filter of section.filters) {
                            const filterKey = `${section.field}_${filter.value}`;
                            this.state.selectedFilters.add(filterKey);
                        }
                    }
                };
                
                // 清除选择
                this.clearSelection = () => {
                    this.state.selectedFilters.clear();
                };
                
                // 批量激活
                this.bulkActivate = () => {
                    this.bulkToggleFilters(true);
                };
                
                // 批量停用
                this.bulkDeactivate = () => {
                    this.bulkToggleFilters(false);
                };
                
                // 批量切换过滤器
                this.bulkToggleFilters = (active) => {
                    const sections = this.props.model.data.filterSections;
                    
                    for (const filterKey of this.state.selectedFilters) {
                        const [sectionField, filterValue] = filterKey.split('_');
                        const section = Object.values(sections).find(s => s.field === sectionField);
                        
                        if (section) {
                            const filter = section.filters.find(f => f.value.toString() === filterValue);
                            if (filter && filter.active !== active) {
                                this.toggleFilter(section, filter);
                            }
                        }
                    }
                    
                    this.clearSelection();
                };
                
                // 批量删除
                this.bulkDelete = async () => {
                    const sections = this.props.model.data.filterSections;
                    const deletePromises = [];
                    
                    for (const filterKey of this.state.selectedFilters) {
                        const [sectionField, filterValue] = filterKey.split('_');
                        const section = Object.values(sections).find(s => s.field === sectionField);
                        
                        if (section && section.write_model) {
                            deletePromises.push(
                                this.deleteFilter(section, filterValue)
                            );
                        }
                    }
                    
                    try {
                        await Promise.all(deletePromises);
                        this.clearSelection();
                        this.refreshFilters();
                        
                        this.env.services.notification.add(
                            _t("Filters deleted successfully"),
                            { type: "success" }
                        );
                    } catch (error) {
                        this.env.services.notification.add(
                            _t("Failed to delete some filters"),
                            { type: "danger" }
                        );
                    }
                };
            }
            
            addPresetManagement() {
                // 保存预设
                this.savePreset = async (name) => {
                    const activeFilters = this.getActiveFilters();
                    
                    const presetData = {
                        name: name,
                        filters: JSON.stringify(activeFilters),
                        user_id: this.env.services.user.userId,
                        model: this.props.model.meta.resModel
                    };
                    
                    try {
                        await this.orm.create('calendar.filter.preset', [presetData]);
                        
                        this.env.services.notification.add(
                            _t("Filter preset saved"),
                            { type: "success" }
                        );
                        
                        this.loadPresets();
                    } catch (error) {
                        this.env.services.notification.add(
                            _t("Failed to save preset"),
                            { type: "danger" }
                        );
                    }
                };
                
                // 加载预设
                this.loadPresets = async () => {
                    try {
                        const presets = await this.orm.searchRead(
                            'calendar.filter.preset',
                            [
                                ['user_id', '=', this.env.services.user.userId],
                                ['model', '=', this.props.model.meta.resModel]
                            ],
                            ['id', 'name', 'filters']
                        );
                        
                        this.state.presets = presets;
                    } catch (error) {
                        console.warn('Failed to load filter presets:', error);
                    }
                };
                
                // 应用预设
                this.applyPreset = async (preset) => {
                    try {
                        const filters = JSON.parse(preset.filters);
                        
                        // 先清除所有过滤器
                        this.clearAllFilters();
                        
                        // 应用预设过滤器
                        for (const filterData of filters) {
                            await this.applyFilterData(filterData);
                        }
                        
                        this.state.currentPreset = preset.id;
                        
                        this.env.services.notification.add(
                            _t("Filter preset applied"),
                            { type: "success" }
                        );
                    } catch (error) {
                        this.env.services.notification.add(
                            _t("Failed to apply preset"),
                            { type: "danger" }
                        );
                    }
                };
                
                // 删除预设
                this.deletePreset = async (presetId) => {
                    try {
                        await this.orm.unlink('calendar.filter.preset', [presetId]);
                        
                        if (this.state.currentPreset === presetId) {
                            this.state.currentPreset = null;
                        }
                        
                        this.loadPresets();
                        
                        this.env.services.notification.add(
                            _t("Filter preset deleted"),
                            { type: "success" }
                        );
                    } catch (error) {
                        this.env.services.notification.add(
                            _t("Failed to delete preset"),
                            { type: "danger" }
                        );
                    }
                };
            }
            
            // 获取活动过滤器
            getActiveFilters() {
                const activeFilters = [];
                const sections = this.props.model.data.filterSections;
                
                for (const section of Object.values(sections)) {
                    for (const filter of section.filters) {
                        if (filter.active) {
                            activeFilters.push({
                                section: section.field,
                                value: filter.value,
                                label: filter.label
                            });
                        }
                    }
                }
                
                return activeFilters;
            }
            
            // 清除所有过滤器
            clearAllFilters() {
                const sections = this.props.model.data.filterSections;
                
                for (const section of Object.values(sections)) {
                    for (const filter of section.filters) {
                        if (filter.active) {
                            this.toggleFilter(section, filter);
                        }
                    }
                }
            }
            
            // 应用过滤器数据
            async applyFilterData(filterData) {
                const sections = this.props.model.data.filterSections;
                const section = Object.values(sections).find(s => s.field === filterData.section);
                
                if (section) {
                    const filter = section.filters.find(f => f.value === filterData.value);
                    if (filter && !filter.active) {
                        this.toggleFilter(section, filter);
                    }
                }
            }
            
            // 删除过滤器
            async deleteFilter(section, filterValue) {
                if (section.write_model) {
                    const records = await this.orm.search(
                        section.write_model,
                        [
                            [section.write_field, '=', filterValue],
                            ['user_id', '=', this.env.services.user.userId]
                        ]
                    );
                    
                    if (records.length > 0) {
                        await this.orm.unlink(section.write_model, records);
                    }
                }
            }
            
            // 获取过滤器统计
            getFilterStatistics() {
                return {
                    totalFilters: this.totalFilterCount,
                    activeFilters: this.activeFilterCount,
                    selectedFilters: this.state.selectedFilters.size,
                    searchQuery: this.state.searchQuery,
                    bulkMode: this.state.bulkMode,
                    currentPreset: this.state.currentPreset
                };
            }
        };
    }
    
    // 设置过滤器预设
    setupFilterPresets() {
        this.defaultPresets = [
            {
                name: 'My Events',
                filters: [
                    { section: 'user_id', value: 'current_user' }
                ]
            },
            {
                name: 'This Week',
                filters: [
                    { section: 'date_range', value: 'this_week' }
                ]
            },
            {
                name: 'Important Events',
                filters: [
                    { section: 'priority', value: 'high' }
                ]
            }
        ];
    }
    
    // 设置批量操作
    setupBulkOperations() {
        this.bulkOperations = [
            {
                name: 'activate',
                label: 'Activate Selected',
                icon: 'fa-check',
                action: 'bulkActivate'
            },
            {
                name: 'deactivate',
                label: 'Deactivate Selected',
                icon: 'fa-times',
                action: 'bulkDeactivate'
            },
            {
                name: 'delete',
                label: 'Delete Selected',
                icon: 'fa-trash',
                action: 'bulkDelete',
                confirm: true
            }
        ];
    }
    
    // 设置搜索优化
    setupSearchOptimization() {
        this.searchOptimization = {
            enableDebounce: true,
            debounceDelay: this.filterConfig.searchDelay,
            enableCache: true,
            cacheTimeout: 60000, // 1分钟
            enableHighlight: true
        };
    }
    
    // 获取过滤面板统计
    getFilterPanelStatistics() {
        return {
            ...this.filterStatistics,
            cacheSize: this.filterCache.size,
            groupCount: this.filterGroups.size,
            presetCount: this.defaultPresets.length
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理缓存
        this.filterCache.clear();
        
        // 清理分组
        this.filterGroups.clear();
        
        // 重置统计
        this.filterStatistics = {
            totalFilters: 0,
            activeFilters: 0,
            totalGroups: 0,
            searchQueries: 0
        };
    }
}

// 使用示例
const filterPanelManager = new FilterPanelManager();

// 创建增强的过滤面板
const EnhancedFilterPanel = filterPanelManager.EnhancedFilterPanel;

// 获取统计信息
const stats = filterPanelManager.getFilterPanelStatistics();
console.log('Filter panel statistics:', stats);
```

## 技术特点

### 1. 组件化设计
- **模块化**: 高度模块化的组件设计
- **可复用**: 可在不同视图中复用
- **可扩展**: 支持功能扩展和定制
- **标准化**: 符合OWL组件标准

### 2. 自动完成集成
- **动态搜索**: 动态搜索过滤器选项
- **异步加载**: 异步加载搜索结果
- **用户体验**: 良好的用户交互体验
- **性能优化**: 优化的搜索性能

### 3. 状态管理
- **响应式**: 响应式的状态管理
- **持久化**: 状态的持久化存储
- **同步**: 与模型状态的同步
- **一致性**: 保持状态的一致性

### 4. 颜色系统
- **颜色编码**: 过滤器的颜色编码
- **视觉区分**: 视觉上的过滤器区分
- **主题支持**: 支持不同的颜色主题
- **可定制**: 支持自定义颜色

## 设计模式

### 1. 组件模式 (Component Pattern)
- **封装性**: 封装过滤面板功能
- **可复用**: 高度可复用的组件
- **接口标准**: 标准化的组件接口

### 2. 观察者模式 (Observer Pattern)
- **状态监听**: 监听模型状态变化
- **自动更新**: 自动更新过滤器显示
- **事件响应**: 响应用户交互事件

### 3. 策略模式 (Strategy Pattern)
- **过滤策略**: 不同的过滤策略
- **搜索策略**: 不同的搜索策略
- **显示策略**: 不同的显示策略

### 4. 工厂模式 (Factory Pattern)
- **过滤器工厂**: 创建不同类型的过滤器
- **选项工厂**: 创建过滤器选项
- **组件工厂**: 创建子组件

## 注意事项

1. **性能优化**: 避免频繁的搜索请求
2. **内存管理**: 及时清理过滤器缓存
3. **用户体验**: 确保良好的交互体验
4. **数据一致性**: 保持过滤器状态的一致性

## 扩展建议

1. **高级过滤**: 支持更复杂的过滤条件
2. **预设管理**: 添加过滤器预设功能
3. **批量操作**: 支持批量过滤器操作
4. **导入导出**: 支持过滤器配置的导入导出
5. **权限控制**: 添加过滤器的权限控制

该过滤面板组件为Odoo Web客户端提供了强大的日历事件过滤功能，通过自动完成、颜色编码和状态管理确保了用户友好的过滤器管理界面。

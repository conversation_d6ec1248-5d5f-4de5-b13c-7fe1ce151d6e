# CalendarController - 日历控制器

## 概述

`calendar_controller.js` 是 Odoo Web 客户端日历视图的控制器组件，提供了日历视图的交互控制和业务逻辑处理功能。该模块包含410行代码，是一个OWL组件，专门用于管理日历视图的用户交互、数据操作、对话框处理等功能，具备事件创建、编辑删除、快速创建、过滤面板、时间刻度切换等特性，是日历视图系统中用户交互的核心控制器。

## 文件信息
- **路径**: `/web/static/src/views/calendar/calendar_controller.js`
- **行数**: 410
- **模块**: `@web/views/calendar/calendar_controller`

## 依赖关系

```javascript
// 核心依赖
'@web/core/confirmation_dialog/confirmation_dialog'     // 确认对话框
'@web/core/l10n/translation'                           // 翻译服务
'@web/core/utils/hooks'                                // 工具钩子
'@web/search/layout'                                   // 搜索布局
'@web/model/model'                                     // 模型管理
'@web/views/view_dialogs/form_view_dialog'             // 表单视图对话框
'@web/search/action_hook'                              // 动作钩子
'@web/core/datetime/datetime_picker'                   // 日期时间选择器
'@web/views/calendar/filter_panel/calendar_filter_panel'        // 过滤面板
'@web/views/calendar/mobile_filter_panel/calendar_mobile_filter_panel'  // 移动过滤面板
'@web/views/calendar/quick_create/calendar_quick_create'         // 快速创建
'@web/search/search_bar/search_bar'                    // 搜索栏
'@web/search/search_bar/search_bar_toggler'            // 搜索栏切换器
'@web/views/view_components/view_scale_selector'       // 视图刻度选择器
'@web/search/cog_menu/cog_menu'                        // 齿轮菜单
'@web/core/browser/browser'                            // 浏览器工具
'@web/views/standard_view_props'                       // 标准视图属性
'@web/core/l10n/dates'                                 // 日期本地化
'@odoo/owl'                                            // OWL框架
```

## 核心常量

### 1. 时间刻度标签

```javascript
const SCALE_LABELS = {
    day: _t("Day"),
    week: _t("Week"),
    month: _t("Month"),
    year: _t("Year"),
};
```

## 工具函数

### 1. useUniqueDialog - 唯一对话框钩子

```javascript
function useUniqueDialog() {
    const displayDialog = useOwnedDialogs();
    let close = null;
    return (...args) => {
        if (close) {
            close();
        }
        close = displayDialog(...args);
    };
}
```

**钩子功能**:
- **唯一性**: 确保同时只有一个对话框打开
- **自动关闭**: 自动关闭之前的对话框
- **状态管理**: 管理对话框的打开和关闭状态

## 主要组件定义

### 1. CalendarController - 日历控制器

```javascript
class CalendarController extends Component {
    static template = "web.CalendarView";
    static components = {
        Layout,
        CalendarFilterPanel,
        CalendarMobileFilterPanel,
        CalendarQuickCreate,
        SearchBar,
        ViewScaleSelector,
        CogMenu,
        DateTimePicker,
    };
    static props = {
        ...standardViewProps,
        Model: Function,
        Renderer: Function,
        archInfo: Object,
        buttonTemplate: String,
    };

    setup() {
        this.actionService = useService("action");
        this.dialogService = useService("dialog");
        this.notificationService = useService("notification");
        this.orm = useService("orm");
        this.user = useService("user");

        this.displayDialog = useUniqueDialog();
        this.model = useModelWithSampleData(this.props.Model, this.modelParams);

        useSetupAction({
            getLocalState: () => {
                return {
                    scale: this.model.scale,
                    date: this.model.date.toISODate(),
                };
            },
            setLocalState: (localState) => {
                if (localState.scale) {
                    this.model.setScale(localState.scale);
                }
                if (localState.date) {
                    this.model.setDate(DateTime.fromISO(localState.date));
                }
            },
        });

        this.state = useState({
            showSearchBar: false,
            showMobileFilterPanel: false,
        });

        this.searchBarToggler = useSearchBarToggler();
    }
}
```

**组件特性**:
- **模板集成**: 使用CalendarView模板
- **子组件**: 集成多个专门的子组件
- **服务依赖**: 依赖多个核心服务
- **状态管理**: 管理搜索栏和过滤面板状态

## 核心功能

### 1. 模型参数配置

```javascript
get modelParams() {
    const { archInfo, fields, resModel, context, domain } = this.props;
    return {
        resModel,
        fields,
        archInfo,
        context,
        domain,
        // 其他配置参数
    };
}
```

**参数配置功能**:
- **模型信息**: 配置资源模型和字段信息
- **架构信息**: 传递解析后的架构信息
- **上下文**: 配置执行上下文
- **域过滤**: 配置域过滤条件

### 2. 事件创建处理

```javascript
async onCreateRecord(context = {}) {
    const { archInfo } = this.props;

    if (archInfo.quickCreate && !context.default_name) {
        // 显示快速创建对话框
        this.displayQuickCreate(context);
    } else {
        // 显示完整表单对话框
        this.displayFormDialog(context);
    }
}

displayQuickCreate(context) {
    this.displayDialog(CalendarQuickCreate, {
        context: context,
        model: this.model,
        onSave: (record) => {
            this.model.createRecord(record);
            this.notificationService.add(_t("Event created"), { type: "success" });
        },
        onEdit: (record) => {
            this.displayFormDialog({ ...context, ...record });
        },
    });
}

displayFormDialog(context) {
    this.displayDialog(FormViewDialog, {
        resModel: this.props.resModel,
        context: context,
        viewId: this.props.archInfo.formViewId,
        onRecordSaved: (record) => {
            this.model.load();
            this.notificationService.add(_t("Event saved"), { type: "success" });
        },
    });
}
```

**创建处理功能**:
- **快速创建**: 支持快速创建模式
- **完整表单**: 支持完整表单创建
- **上下文传递**: 传递创建上下文
- **保存回调**: 处理保存后的操作

### 3. 事件编辑处理

```javascript
async onEditRecord(record, context = {}) {
    const editContext = {
        ...context,
        default_id: record.id,
    };

    this.displayDialog(FormViewDialog, {
        resModel: this.props.resModel,
        resId: record.id,
        context: editContext,
        viewId: this.props.archInfo.formViewId,
        onRecordSaved: (savedRecord) => {
            this.model.updateRecord(savedRecord);
            this.notificationService.add(_t("Event updated"), { type: "success" });
        },
        onRecordDeleted: () => {
            this.model.deleteRecord(record.id);
            this.notificationService.add(_t("Event deleted"), { type: "success" });
        },
    });
}
```

**编辑处理功能**:
- **记录编辑**: 编辑指定记录
- **上下文配置**: 配置编辑上下文
- **保存更新**: 处理保存后的更新
- **删除处理**: 处理记录删除

### 4. 事件删除处理

```javascript
async onDeleteRecord(record) {
    const message = deleteConfirmationMessage.toString();

    this.displayDialog(ConfirmationDialog, {
        body: message,
        confirmLabel: _t("Delete"),
        confirm: async () => {
            try {
                await this.orm.unlink(this.props.resModel, [record.id]);
                this.model.deleteRecord(record.id);
                this.notificationService.add(_t("Event deleted"), { type: "success" });
            } catch (error) {
                this.notificationService.add(_t("Failed to delete event"), { type: "danger" });
            }
        },
        cancel: () => {},
    });
}
```

**删除处理功能**:
- **确认对话框**: 显示删除确认对话框
- **ORM删除**: 通过ORM执行删除操作
- **模型更新**: 更新本地模型状态
- **错误处理**: 处理删除失败的情况

### 5. 时间刻度切换

```javascript
onScaleChange(scale) {
    this.model.setScale(scale);
    browser.sessionStorage.setItem("calendar-scale", scale);
}

get scaleOptions() {
    return this.props.archInfo.scales.map(scale => ({
        key: scale,
        description: SCALE_LABELS[scale],
    }));
}
```

**刻度切换功能**:
- **刻度设置**: 设置当前时间刻度
- **持久化**: 保存刻度选择到会话存储
- **选项生成**: 生成可用的刻度选项
- **标签映射**: 映射刻度到显示标签
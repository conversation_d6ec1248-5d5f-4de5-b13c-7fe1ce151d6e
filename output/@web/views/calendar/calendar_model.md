# CalendarModel - 日历模型

## 概述

`calendar_model.js` 是 Odoo Web 客户端日历视图的数据模型，提供了日历数据的管理、加载和操作功能。该模块包含869行代码，是一个继承自Model的专门模型类，用于处理日历事件数据、过滤器管理、时间范围计算等功能，具备数据加载、记录管理、过滤处理、时间计算等特性，是日历视图系统中数据层的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/calendar/calendar_model.js`
- **行数**: 869
- **模块**: `@web/views/calendar/calendar_model`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/dates'                      // 日期本地化
'@web/core/l10n/localization'               // 本地化服务
'@web/core/l10n/translation'                // 翻译服务
'@web/core/registry'                        // 注册表系统
'@web/core/user'                            // 用户服务
'@web/core/utils/concurrency'               // 并发工具
'@web/model/model'                          // 基础模型
'@web/model/relational_model/utils'         // 关系模型工具
'@web/core/browser/browser'                 // 浏览器工具
```

## 主要类定义

### 1. CalendarModel - 日历模型

```javascript
class CalendarModel extends Model {
    setup(params, services) {
        this.keepLast = new KeepLast();

        const formViewFromConfig = (this.env.config.views || []).find((view) => view[1] === "form");
        const formViewIdFromConfig = formViewFromConfig ? formViewFromConfig[0] : false;
        const fieldNodes = params.popoverFieldNodes;
        const { activeFields, fields } = extractFieldsFromArchInfo({ fieldNodes }, params.fields);
        
        this.meta = {
            ...params,
            activeFields,
            fields,
            firstDayOfWeek: (localization.weekStart || 0) % 7,
            formViewId: params.formViewId || formViewIdFromConfig,
        };
        
        this.meta.scale = this.getLocalStorageScale();
        
        this.data = {
            filters: {},
            filterSections: {},
            hasCreateRight: null,
            range: null,
            records: {},
            unusualDays: [],
        };
    }

    async load(params = {}) {
        Object.assign(this.meta, params);
        
        // 加载数据逻辑
        await this.loadData();
        await this.loadFilters();
        await this.loadUnusualDays();
    }
}
```

**模型特性**:
- **继承Model**: 继承自基础Model类
- **并发控制**: 使用KeepLast控制并发请求
- **元数据管理**: 管理视图元数据和配置
- **数据结构**: 维护复杂的数据结构

## 核心功能

### 1. 模型初始化

```javascript
setup(params, services) {
    // 并发控制
    this.keepLast = new KeepLast();

    // 表单视图配置
    const formViewFromConfig = (this.env.config.views || []).find((view) => view[1] === "form");
    const formViewIdFromConfig = formViewFromConfig ? formViewFromConfig[0] : false;
    
    // 字段提取
    const fieldNodes = params.popoverFieldNodes;
    const { activeFields, fields } = extractFieldsFromArchInfo({ fieldNodes }, params.fields);
    
    // 元数据配置
    this.meta = {
        ...params,
        activeFields,
        fields,
        firstDayOfWeek: (localization.weekStart || 0) % 7,
        formViewId: params.formViewId || formViewIdFromConfig,
    };
    
    // 数据初始化
    this.data = {
        filters: {},           // 过滤器数据
        filterSections: {},    // 过滤器分组
        hasCreateRight: null,  // 创建权限
        range: null,           // 时间范围
        records: {},           // 记录数据
        unusualDays: [],       // 特殊日期
    };
}
```

**初始化功能**:
- **并发管理**: 设置并发请求控制
- **视图配置**: 配置表单视图ID
- **字段处理**: 提取和处理活动字段
- **本地化**: 设置本地化配置如周开始日

### 2. 数据加载

```javascript
async load(params = {}) {
    Object.assign(this.meta, params);
    
    // 设置时间范围
    this.setRange();
    
    // 并发加载数据
    const promises = [
        this.loadRecords(),
        this.loadFilters(),
        this.loadUnusualDays(),
        this.checkCreateRight(),
    ];
    
    await Promise.all(promises);
    
    // 处理加载后的数据
    this.processLoadedData();
}

async loadRecords() {
    const domain = this.buildDomain();
    const fields = this.getFieldsToRead();
    
    const records = await this.keepLast.add(
        this.orm.searchRead(this.meta.resModel, domain, fields)
    );
    
    this.data.records = this.processRecords(records);
}

buildDomain() {
    const { range, fieldMapping } = this.meta;
    const dateField = fieldMapping.date_start;
    
    let domain = [
        [dateField, ">=", serializeDateTime(range.start)],
        [dateField, "<=", serializeDateTime(range.end)],
    ];
    
    // 添加过滤器域
    const filterDomain = this.buildFilterDomain();
    if (filterDomain.length) {
        domain = domain.concat(filterDomain);
    }
    
    return domain;
}
```

**加载功能**:
- **参数合并**: 合并加载参数到元数据
- **范围设置**: 设置当前时间范围
- **并发加载**: 并发加载多种数据
- **域构建**: 构建查询域条件

### 3. 记录处理

```javascript
processRecords(rawRecords) {
    const processedRecords = {};
    
    for (const record of rawRecords) {
        const processedRecord = this.processRecord(record);
        processedRecords[record.id] = processedRecord;
    }
    
    return processedRecords;
}

processRecord(record) {
    const { fieldMapping } = this.meta;
    
    // 处理日期字段
    const startDate = this.processDateField(record, fieldMapping.date_start);
    const endDate = this.processDateField(record, fieldMapping.date_stop || fieldMapping.date_start);
    
    // 处理全天事件
    const isAllDay = fieldMapping.all_day ? record[fieldMapping.all_day] : false;
    
    // 处理颜色
    const colorValue = fieldMapping.color ? record[fieldMapping.color] : null;
    const color = this.getRecordColor(colorValue);
    
    return {
        ...record,
        start: startDate,
        end: endDate,
        isAllDay: isAllDay,
        color: color,
        title: this.getRecordTitle(record),
    };
}

processDateField(record, fieldName) {
    if (!fieldName || !record[fieldName]) {
        return null;
    }
    
    const fieldValue = record[fieldName];
    const field = this.meta.fields[fieldName];
    
    if (field.type === "datetime") {
        return deserializeDateTime(fieldValue);
    } else if (field.type === "date") {
        return deserializeDate(fieldValue);
    }
    
    return fieldValue;
}
```

**记录处理功能**:
- **批量处理**: 批量处理原始记录数据
- **日期转换**: 转换日期和时间字段
- **全天处理**: 处理全天事件标识
- **颜色映射**: 映射记录颜色

### 4. 过滤器管理

```javascript
async loadFilters() {
    const { filtersInfo } = this.meta;
    
    for (const [filterName, filterInfo] of Object.entries(filtersInfo)) {
        await this.loadFilter(filterName, filterInfo);
    }
}

async loadFilter(filterName, filterInfo) {
    const { field, avatar_field, write_model, write_field } = filterInfo;
    
    // 获取过滤器选项
    const options = await this.getFilterOptions(filterInfo);
    
    // 处理过滤器数据
    const filterData = {
        name: filterName,
        field: field,
        options: options,
        active: this.getActiveFilterOptions(filterName),
        avatar_field: avatar_field,
        write_model: write_model,
        write_field: write_field,
    };
    
    this.data.filters[filterName] = filterData;
}

async getFilterOptions(filterInfo) {
    const { field } = filterInfo;
    const fieldDef = this.meta.fields[field];
    
    if (fieldDef.type === "many2one") {
        return await this.getMany2OneOptions(filterInfo);
    } else if (fieldDef.type === "selection") {
        return this.getSelectionOptions(filterInfo);
    }
    
    return [];
}

async getMany2OneOptions(filterInfo) {
    const { field } = filterInfo;
    const fieldDef = this.meta.fields[field];
    const comodel = fieldDef.relation;
    
    const records = await this.orm.searchRead(
        comodel,
        [],
        ["id", "display_name", filterInfo.avatar_field].filter(Boolean)
    );
    
    return records.map(record => ({
        id: record.id,
        label: record.display_name,
        avatar: filterInfo.avatar_field ? record[filterInfo.avatar_field] : null,
        active: true,
    }));
}
```

**过滤器功能**:
- **动态加载**: 动态加载过滤器选项
- **类型支持**: 支持many2one和selection字段
- **状态管理**: 管理过滤器激活状态
- **头像支持**: 支持过滤器选项头像

### 5. 时间范围计算

```javascript
setRange() {
    const { scale, date } = this.meta;
    
    switch (scale) {
        case "day":
            this.setDayRange(date);
            break;
        case "week":
            this.setWeekRange(date);
            break;
        case "month":
            this.setMonthRange(date);
            break;
        case "year":
            this.setYearRange(date);
            break;
    }
}

setDayRange(date) {
    const start = date.startOf("day");
    const end = date.endOf("day");
    
    this.data.range = { start, end };
}

setWeekRange(date) {
    const { firstDayOfWeek } = this.meta;
    const start = date.startOf("week").plus({ days: firstDayOfWeek });
    const end = start.plus({ days: 6 }).endOf("day");
    
    this.data.range = { start, end };
}

setMonthRange(date) {
    const start = date.startOf("month");
    const end = date.endOf("month");
    
    this.data.range = { start, end };
}

setYearRange(date) {
    const start = date.startOf("year");
    const end = date.endOf("year");
    
    this.data.range = { start, end };
}
```

**时间范围功能**:
- **多刻度支持**: 支持日、周、月、年刻度
- **本地化**: 考虑本地化的周开始日
- **精确计算**: 精确计算时间范围边界
- **状态更新**: 更新模型的时间范围状态

### 6. 记录操作

```javascript
async createRecord(recordData) {
    const processedData = this.prepareRecordData(recordData);
    
    const newRecord = await this.orm.create(this.meta.resModel, [processedData]);
    
    // 重新加载数据
    await this.load();
    
    return newRecord[0];
}

async updateRecord(recordId, recordData) {
    const processedData = this.prepareRecordData(recordData);
    
    await this.orm.write(this.meta.resModel, [recordId], processedData);
    
    // 更新本地记录
    const updatedRecord = await this.orm.read(this.meta.resModel, [recordId], this.getFieldsToRead());
    this.data.records[recordId] = this.processRecord(updatedRecord[0]);
    
    return this.data.records[recordId];
}

async deleteRecord(recordId) {
    await this.orm.unlink(this.meta.resModel, [recordId]);
    
    // 从本地数据中移除
    delete this.data.records[recordId];
}

prepareRecordData(recordData) {
    const { fieldMapping } = this.meta;
    const preparedData = { ...recordData };
    
    // 处理日期字段
    if (preparedData.start && fieldMapping.date_start) {
        preparedData[fieldMapping.date_start] = this.serializeDateField(
            preparedData.start,
            fieldMapping.date_start
        );
        delete preparedData.start;
    }
    
    if (preparedData.end && fieldMapping.date_stop) {
        preparedData[fieldMapping.date_stop] = this.serializeDateField(
            preparedData.end,
            fieldMapping.date_stop
        );
        delete preparedData.end;
    }
    
    return preparedData;
}
```

**记录操作功能**:
- **创建记录**: 创建新的日历记录
- **更新记录**: 更新现有记录
- **删除记录**: 删除指定记录
- **数据准备**: 准备和转换记录数据

## 使用场景

### 1. 日历数据管理器

```javascript
// 日历数据管理器
class CalendarDataManager {
    constructor(model) {
        this.model = model;
        this.setupManager();
    }
    
    setupManager() {
        // 设置数据配置
        this.dataConfig = {
            enableCaching: true,
            enableOptimization: true,
            enableValidation: true,
            batchSize: 100,
            cacheTimeout: 300000 // 5分钟
        };
        
        // 设置数据缓存
        this.dataCache = new Map();
        
        // 设置操作队列
        this.operationQueue = [];
        
        // 设置统计信息
        this.statistics = {
            totalLoads: 0,
            cacheHits: 0,
            cacheMisses: 0,
            operations: 0
        };
        
        this.initializeDataHandlers();
    }
    
    // 初始化数据处理器
    initializeDataHandlers() {
        // 扩展模型加载方法
        const originalLoad = this.model.load.bind(this.model);
        
        this.model.load = async (params = {}) => {
            const cacheKey = this.generateCacheKey(params);
            
            // 检查缓存
            if (this.dataConfig.enableCaching && this.dataCache.has(cacheKey)) {
                const cached = this.dataCache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.dataConfig.cacheTimeout) {
                    this.statistics.cacheHits++;
                    return cached.data;
                }
            }
            
            this.statistics.cacheMisses++;
            this.statistics.totalLoads++;
            
            // 执行原始加载
            const result = await originalLoad(params);
            
            // 缓存结果
            if (this.dataConfig.enableCaching) {
                this.dataCache.set(cacheKey, {
                    data: result,
                    timestamp: Date.now()
                });
            }
            
            return result;
        };
    }
    
    // 生成缓存键
    generateCacheKey(params) {
        const keyData = {
            scale: params.scale || this.model.meta.scale,
            date: params.date || this.model.meta.date,
            domain: params.domain || this.model.meta.domain,
            filters: this.getActiveFilters()
        };
        
        return btoa(JSON.stringify(keyData));
    }
    
    // 获取活动过滤器
    getActiveFilters() {
        const activeFilters = {};
        
        for (const [filterName, filterData] of Object.entries(this.model.data.filters)) {
            activeFilters[filterName] = filterData.active;
        }
        
        return activeFilters;
    }
    
    // 批量操作记录
    async batchOperateRecords(operations) {
        const results = [];
        const errors = [];
        
        // 按操作类型分组
        const groupedOps = this.groupOperationsByType(operations);
        
        // 批量执行操作
        for (const [opType, ops] of Object.entries(groupedOps)) {
            try {
                const batchResult = await this.executeBatchOperation(opType, ops);
                results.push(...batchResult);
            } catch (error) {
                errors.push({ type: opType, operations: ops, error });
            }
        }
        
        // 重新加载数据
        if (results.length > 0) {
            await this.model.load();
        }
        
        return {
            results: results,
            errors: errors,
            total: operations.length,
            successful: results.length,
            failed: errors.length
        };
    }
    
    // 按类型分组操作
    groupOperationsByType(operations) {
        const grouped = {
            create: [],
            update: [],
            delete: []
        };
        
        for (const op of operations) {
            if (grouped[op.type]) {
                grouped[op.type].push(op);
            }
        }
        
        return grouped;
    }
    
    // 执行批量操作
    async executeBatchOperation(opType, operations) {
        switch (opType) {
            case 'create':
                return await this.batchCreateRecords(operations);
            case 'update':
                return await this.batchUpdateRecords(operations);
            case 'delete':
                return await this.batchDeleteRecords(operations);
            default:
                throw new Error(`Unknown operation type: ${opType}`);
        }
    }
    
    // 批量创建记录
    async batchCreateRecords(createOps) {
        const recordsData = createOps.map(op => this.model.prepareRecordData(op.data));
        
        const newRecords = await this.model.orm.create(this.model.meta.resModel, recordsData);
        
        this.statistics.operations += createOps.length;
        
        return newRecords.map((record, index) => ({
            operation: createOps[index],
            result: record,
            success: true
        }));
    }
    
    // 批量更新记录
    async batchUpdateRecords(updateOps) {
        const results = [];
        
        // 按批次处理更新
        for (let i = 0; i < updateOps.length; i += this.dataConfig.batchSize) {
            const batch = updateOps.slice(i, i + this.dataConfig.batchSize);
            
            for (const op of batch) {
                try {
                    const preparedData = this.model.prepareRecordData(op.data);
                    await this.model.orm.write(this.model.meta.resModel, [op.recordId], preparedData);
                    
                    results.push({
                        operation: op,
                        result: { id: op.recordId },
                        success: true
                    });
                } catch (error) {
                    results.push({
                        operation: op,
                        error: error,
                        success: false
                    });
                }
            }
        }
        
        this.statistics.operations += updateOps.length;
        
        return results.filter(r => r.success);
    }
    
    // 批量删除记录
    async batchDeleteRecords(deleteOps) {
        const recordIds = deleteOps.map(op => op.recordId);
        
        await this.model.orm.unlink(this.model.meta.resModel, recordIds);
        
        // 从本地数据中移除
        for (const recordId of recordIds) {
            delete this.model.data.records[recordId];
        }
        
        this.statistics.operations += deleteOps.length;
        
        return deleteOps.map(op => ({
            operation: op,
            result: { id: op.recordId },
            success: true
        }));
    }
    
    // 优化数据加载
    async optimizeDataLoading(params = {}) {
        const optimizedParams = { ...params };
        
        // 优化时间范围
        if (this.dataConfig.enableOptimization) {
            optimizedParams.range = this.optimizeTimeRange(params.range);
        }
        
        // 优化字段列表
        if (this.dataConfig.enableOptimization) {
            optimizedParams.fields = this.optimizeFieldList(params.fields);
        }
        
        return await this.model.load(optimizedParams);
    }
    
    // 优化时间范围
    optimizeTimeRange(range) {
        if (!range) return range;
        
        // 扩展范围以包含可能的重复事件
        const extendedStart = range.start.minus({ days: 1 });
        const extendedEnd = range.end.plus({ days: 1 });
        
        return { start: extendedStart, end: extendedEnd };
    }
    
    // 优化字段列表
    optimizeFieldList(fields) {
        if (!fields) return fields;
        
        // 移除不必要的字段
        const essentialFields = ['id', 'display_name'];
        const fieldMapping = this.model.meta.fieldMapping;
        
        const optimizedFields = [
            ...essentialFields,
            ...Object.values(fieldMapping).filter(Boolean),
            ...fields.filter(field => !essentialFields.includes(field))
        ];
        
        return [...new Set(optimizedFields)];
    }
    
    // 获取数据统计
    getDataStatistics() {
        return {
            ...this.statistics,
            cacheSize: this.dataCache.size,
            recordCount: Object.keys(this.model.data.records).length,
            filterCount: Object.keys(this.model.data.filters).length,
            cacheHitRate: this.statistics.totalLoads > 0 
                ? (this.statistics.cacheHits / this.statistics.totalLoads * 100).toFixed(2) + '%'
                : '0%'
        };
    }
    
    // 清理缓存
    clearCache() {
        this.dataCache.clear();
    }
    
    // 预加载数据
    async preloadData(ranges) {
        for (const range of ranges) {
            try {
                await this.model.load({ range });
            } catch (error) {
                console.warn('Failed to preload data for range:', range, error);
            }
        }
    }
    
    // 销毁管理器
    destroy() {
        // 清理缓存
        this.clearCache();
        
        // 清理队列
        this.operationQueue = [];
        
        // 重置统计
        this.statistics = {
            totalLoads: 0,
            cacheHits: 0,
            cacheMisses: 0,
            operations: 0
        };
    }
}

// 使用示例
const dataManager = new CalendarDataManager(calendarModel);

// 批量操作
const operations = [
    { type: 'create', data: { name: 'New Event', start: new Date() } },
    { type: 'update', recordId: 1, data: { name: 'Updated Event' } },
    { type: 'delete', recordId: 2 }
];

const batchResult = await dataManager.batchOperateRecords(operations);

// 获取统计信息
const stats = dataManager.getDataStatistics();
console.log('Data statistics:', stats);
```

## 技术特点

### 1. 数据模型架构
- **继承设计**: 继承自基础Model类
- **元数据管理**: 完整的视图元数据管理
- **数据结构**: 复杂的数据结构设计
- **状态管理**: 全面的状态管理机制

### 2. 并发控制
- **KeepLast**: 使用KeepLast控制并发请求
- **异步处理**: 完整的异步数据处理
- **请求管理**: 智能的请求管理机制
- **性能优化**: 避免重复和无效请求

### 3. 时间处理
- **多刻度支持**: 支持日、周、月、年刻度
- **本地化**: 完整的时间本地化支持
- **范围计算**: 精确的时间范围计算
- **日期转换**: 智能的日期格式转换

### 4. 过滤器系统
- **动态加载**: 动态加载过滤器选项
- **多类型支持**: 支持多种字段类型
- **状态管理**: 完整的过滤器状态管理
- **性能优化**: 优化的过滤器处理

## 设计模式

### 1. 模型模式 (Model Pattern)
- **数据封装**: 封装日历数据和操作
- **业务逻辑**: 包含业务逻辑处理
- **状态管理**: 管理模型状态

### 2. 观察者模式 (Observer Pattern)
- **数据变化**: 监听数据变化
- **状态通知**: 通知状态更新
- **事件处理**: 处理模型事件

### 3. 策略模式 (Strategy Pattern)
- **加载策略**: 不同的数据加载策略
- **处理策略**: 不同的数据处理策略
- **优化策略**: 不同的性能优化策略

### 4. 工厂模式 (Factory Pattern)
- **记录创建**: 创建和处理记录对象
- **过滤器创建**: 创建过滤器对象
- **范围创建**: 创建时间范围对象

## 注意事项

1. **内存管理**: 注意大量数据的内存使用
2. **并发控制**: 正确使用并发控制机制
3. **时间处理**: 注意时区和本地化问题
4. **性能优化**: 避免频繁的数据重载

## 扩展建议

1. **缓存机制**: 增强数据缓存机制
2. **批量操作**: 支持更多的批量操作
3. **实时更新**: 添加实时数据更新功能
4. **性能监控**: 添加性能监控和优化
5. **错误恢复**: 增强错误处理和恢复机制

该日历模型为Odoo Web客户端提供了强大的日历数据管理功能，通过完整的数据处理、并发控制和性能优化确保了日历视图的高效运行和良好的用户体验。

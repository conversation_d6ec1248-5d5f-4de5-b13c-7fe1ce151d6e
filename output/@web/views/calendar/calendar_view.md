# CalendarView - 日历视图

## 概述

`calendar_view.js` 是 Odoo Web 客户端日历视图的主视图定义，提供了日历视图的完整配置和注册功能。该模块包含39行代码，是一个视图配置对象，专门用于定义日历视图的组件结构、属性处理和注册信息，具备MVC架构集成、属性处理、搜索菜单配置、按钮模板等特性，是日历视图系统中视图层的核心定义。

## 文件信息
- **路径**: `/web/static/src/views/calendar/calendar_view.js`
- **行数**: 39
- **模块**: `@web/views/calendar/calendar_view`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                                    // 注册表系统
'@web/views/calendar/calendar_renderer'                 // 日历渲染器
'@web/views/calendar/calendar_arch_parser'              // 日历架构解析器
'@web/views/calendar/calendar_model'                    // 日历模型
'@web/views/calendar/calendar_controller'               // 日历控制器
```

## 主要视图定义

### 1. calendarView - 日历视图配置

```javascript
const calendarView = {
    type: "calendar",

    searchMenuTypes: ["filter", "favorite"],

    ArchParser: CalendarArchParser,
    Controller: CalendarController,
    Model: CalendarModel,
    Renderer: CalendarRenderer,

    buttonTemplate: "web.CalendarController.controlButtons",

    props: (props, view) => {
        const { ArchParser } = view;
        const { arch, relatedModels, resModel } = props;
        const archInfo = new ArchParser().parse(arch, relatedModels, resModel);
        return {
            ...props,
            Model: view.Model,
            Renderer: view.Renderer,
            buttonTemplate: view.buttonTemplate,
            archInfo,
        };
    },
};
```

**视图配置特性**:
- **类型定义**: 定义视图类型为"calendar"
- **MVC架构**: 完整的MVC组件配置
- **搜索集成**: 配置搜索菜单类型
- **属性处理**: 动态处理视图属性

## 核心功能

### 1. 视图类型定义

```javascript
type: "calendar",
```

**类型定义功能**:
- **视图标识**: 唯一标识日历视图类型
- **注册键**: 作为视图注册的键值
- **路由识别**: 用于路由和URL识别
- **组件选择**: 用于选择对应的视图组件

### 2. 搜索菜单配置

```javascript
searchMenuTypes: ["filter", "favorite"],
```

**搜索菜单功能**:
- **过滤器**: 支持过滤器搜索菜单
- **收藏夹**: 支持收藏夹搜索菜单
- **菜单类型**: 定义可用的搜索菜单类型
- **功能限制**: 限制可用的搜索功能

### 3. MVC组件配置

```javascript
ArchParser: CalendarArchParser,
Controller: CalendarController,
Model: CalendarModel,
Renderer: CalendarRenderer,
```

**MVC配置功能**:
- **架构解析器**: 配置XML架构解析器
- **控制器**: 配置用户交互控制器
- **模型**: 配置数据模型
- **渲染器**: 配置视图渲染器

### 4. 按钮模板配置

```javascript
buttonTemplate: "web.CalendarController.controlButtons",
```

**按钮模板功能**:
- **模板引用**: 引用控制按钮模板
- **UI集成**: 集成控制按钮到界面
- **功能访问**: 提供功能访问入口
- **样式统一**: 统一按钮样式和布局

### 5. 属性处理函数

```javascript
props: (props, view) => {
    const { ArchParser } = view;
    const { arch, relatedModels, resModel } = props;
    const archInfo = new ArchParser().parse(arch, relatedModels, resModel);
    return {
        ...props,
        Model: view.Model,
        Renderer: view.Renderer,
        buttonTemplate: view.buttonTemplate,
        archInfo,
    };
},
```

**属性处理功能**:
- **架构解析**: 解析XML架构生成配置信息
- **属性合并**: 合并原始属性和视图配置
- **组件传递**: 传递MVC组件到属性
- **信息增强**: 增强属性信息

### 6. 视图注册

```javascript
registry.category("views").add("calendar", calendarView);
```

**注册功能**:
- **视图注册**: 注册到视图类别
- **全局可用**: 在整个应用中可用
- **类型映射**: 建立类型到视图的映射
- **系统集成**: 集成到视图系统

## 使用场景

### 1. 日历视图管理器

```javascript
// 日历视图管理器
class CalendarViewManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置视图配置
        this.viewConfig = {
            enableCustomization: true,
            enableExtensions: true,
            enableValidation: true,
            enableCaching: true,
            defaultScale: 'week',
            defaultEventLimit: 5
        };
        
        // 设置视图扩展
        this.viewExtensions = new Map();
        
        // 设置视图实例
        this.viewInstances = new Map();
        
        // 设置视图统计
        this.viewStatistics = {
            totalViews: 0,
            activeViews: 0,
            createdViews: 0,
            destroyedViews: 0
        };
        
        this.initializeViewSystem();
    }
    
    // 初始化视图系统
    initializeViewSystem() {
        // 扩展原始视图配置
        this.extendCalendarView();
        
        // 设置视图工厂
        this.setupViewFactory();
        
        // 设置视图生命周期
        this.setupViewLifecycle();
    }
    
    // 扩展日历视图
    extendCalendarView() {
        const originalView = calendarView;
        
        const extendedView = {
            ...originalView,
            
            // 扩展属性处理
            props: (props, view) => {
                const baseProps = originalView.props(props, view);
                
                // 添加自定义属性
                const customProps = this.getCustomProps(props, view);
                
                // 添加扩展配置
                const extensionProps = this.getExtensionProps(props, view);
                
                return {
                    ...baseProps,
                    ...customProps,
                    ...extensionProps,
                    viewManager: this,
                };
            },
            
            // 扩展搜索菜单
            searchMenuTypes: [
                ...originalView.searchMenuTypes,
                "groupby",
                "comparison"
            ],
            
            // 添加自定义配置
            customConfig: this.viewConfig,
            
            // 添加扩展点
            extensions: this.viewExtensions,
        };
        
        // 重新注册扩展视图
        registry.category("views").add("calendar", extendedView);
    }
    
    // 获取自定义属性
    getCustomProps(props, view) {
        return {
            enableAdvancedFeatures: this.viewConfig.enableCustomization,
            enablePerformanceOptimization: true,
            enableAccessibilitySupport: true,
            enableMobileOptimization: true,
            customEventHandlers: this.getCustomEventHandlers(),
            customValidators: this.getCustomValidators(),
        };
    }
    
    // 获取扩展属性
    getExtensionProps(props, view) {
        const extensionProps = {};
        
        // 应用所有扩展
        for (const [name, extension] of this.viewExtensions) {
            if (extension.getProps) {
                const extProps = extension.getProps(props, view);
                Object.assign(extensionProps, extProps);
            }
        }
        
        return extensionProps;
    }
    
    // 获取自定义事件处理器
    getCustomEventHandlers() {
        return {
            onViewCreated: (view) => {
                this.handleViewCreated(view);
            },
            onViewDestroyed: (view) => {
                this.handleViewDestroyed(view);
            },
            onViewUpdated: (view) => {
                this.handleViewUpdated(view);
            },
            onViewError: (view, error) => {
                this.handleViewError(view, error);
            }
        };
    }
    
    // 获取自定义验证器
    getCustomValidators() {
        return {
            validateArchInfo: (archInfo) => {
                return this.validateArchInfo(archInfo);
            },
            validateProps: (props) => {
                return this.validateProps(props);
            },
            validateModel: (model) => {
                return this.validateModel(model);
            }
        };
    }
    
    // 设置视图工厂
    setupViewFactory() {
        this.viewFactory = {
            createView: (type, props) => {
                return this.createView(type, props);
            },
            destroyView: (viewId) => {
                return this.destroyView(viewId);
            },
            getView: (viewId) => {
                return this.getView(viewId);
            },
            listViews: () => {
                return this.listViews();
            }
        };
    }
    
    // 设置视图生命周期
    setupViewLifecycle() {
        this.lifecycle = {
            beforeCreate: [],
            afterCreate: [],
            beforeDestroy: [],
            afterDestroy: [],
            beforeUpdate: [],
            afterUpdate: []
        };
    }
    
    // 创建视图
    createView(type, props) {
        const viewId = this.generateViewId();
        
        try {
            // 执行创建前钩子
            this.executeLifecycleHooks('beforeCreate', { type, props });
            
            // 创建视图实例
            const viewInstance = this.instantiateView(type, props);
            
            // 注册视图实例
            this.viewInstances.set(viewId, {
                id: viewId,
                type: type,
                instance: viewInstance,
                props: props,
                createdAt: Date.now(),
                status: 'active'
            });
            
            // 更新统计
            this.viewStatistics.totalViews++;
            this.viewStatistics.activeViews++;
            this.viewStatistics.createdViews++;
            
            // 执行创建后钩子
            this.executeLifecycleHooks('afterCreate', { viewId, viewInstance });
            
            return viewId;
            
        } catch (error) {
            this.handleViewError(null, error);
            throw error;
        }
    }
    
    // 实例化视图
    instantiateView(type, props) {
        const viewConfig = registry.category("views").get(type);
        
        if (!viewConfig) {
            throw new Error(`View type ${type} not found`);
        }
        
        // 处理属性
        const processedProps = viewConfig.props ? viewConfig.props(props, viewConfig) : props;
        
        // 创建视图组件
        const viewComponent = {
            type: type,
            config: viewConfig,
            props: processedProps,
            created: Date.now()
        };
        
        return viewComponent;
    }
    
    // 销毁视图
    destroyView(viewId) {
        const viewData = this.viewInstances.get(viewId);
        
        if (!viewData) {
            throw new Error(`View ${viewId} not found`);
        }
        
        try {
            // 执行销毁前钩子
            this.executeLifecycleHooks('beforeDestroy', { viewId, viewData });
            
            // 销毁视图实例
            if (viewData.instance.destroy) {
                viewData.instance.destroy();
            }
            
            // 移除视图实例
            this.viewInstances.delete(viewId);
            
            // 更新统计
            this.viewStatistics.activeViews--;
            this.viewStatistics.destroyedViews++;
            
            // 执行销毁后钩子
            this.executeLifecycleHooks('afterDestroy', { viewId });
            
            return true;
            
        } catch (error) {
            this.handleViewError(viewData.instance, error);
            throw error;
        }
    }
    
    // 执行生命周期钩子
    executeLifecycleHooks(phase, data) {
        const hooks = this.lifecycle[phase] || [];
        
        for (const hook of hooks) {
            try {
                hook(data);
            } catch (error) {
                console.warn(`Lifecycle hook error in ${phase}:`, error);
            }
        }
    }
    
    // 添加视图扩展
    addExtension(name, extension) {
        this.viewExtensions.set(name, extension);
        
        // 重新注册视图以应用扩展
        this.extendCalendarView();
    }
    
    // 移除视图扩展
    removeExtension(name) {
        this.viewExtensions.delete(name);
        
        // 重新注册视图
        this.extendCalendarView();
    }
    
    // 验证架构信息
    validateArchInfo(archInfo) {
        const errors = [];
        
        // 验证必需字段
        if (!archInfo.fieldMapping.date_start) {
            errors.push('date_start field mapping is required');
        }
        
        // 验证时间刻度
        if (!archInfo.scales || archInfo.scales.length === 0) {
            errors.push('At least one time scale is required');
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
    
    // 验证属性
    validateProps(props) {
        const errors = [];
        
        // 验证必需属性
        if (!props.resModel) {
            errors.push('resModel is required');
        }
        
        if (!props.arch) {
            errors.push('arch is required');
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
    
    // 验证模型
    validateModel(model) {
        const errors = [];
        
        // 验证模型方法
        const requiredMethods = ['load', 'createRecord', 'updateRecord', 'deleteRecord'];
        
        for (const method of requiredMethods) {
            if (typeof model[method] !== 'function') {
                errors.push(`Model method ${method} is required`);
            }
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
    
    // 处理视图创建
    handleViewCreated(view) {
        console.log('View created:', view);
    }
    
    // 处理视图销毁
    handleViewDestroyed(view) {
        console.log('View destroyed:', view);
    }
    
    // 处理视图更新
    handleViewUpdated(view) {
        console.log('View updated:', view);
    }
    
    // 处理视图错误
    handleViewError(view, error) {
        console.error('View error:', error);
        
        // 可以在这里实现错误报告逻辑
        this.reportViewError(view, error);
    }
    
    // 报告视图错误
    reportViewError(view, error) {
        // 实现错误报告逻辑
        console.warn('View error reported:', { view, error });
    }
    
    // 生成视图ID
    generateViewId() {
        return `calendar_view_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    // 获取视图
    getView(viewId) {
        return this.viewInstances.get(viewId);
    }
    
    // 列出所有视图
    listViews() {
        return Array.from(this.viewInstances.values());
    }
    
    // 获取视图统计
    getViewStatistics() {
        return {
            ...this.viewStatistics,
            extensionCount: this.viewExtensions.size,
            instanceCount: this.viewInstances.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 销毁所有视图实例
        for (const [viewId] of this.viewInstances) {
            this.destroyView(viewId);
        }
        
        // 清理扩展
        this.viewExtensions.clear();
        
        // 重置统计
        this.viewStatistics = {
            totalViews: 0,
            activeViews: 0,
            createdViews: 0,
            destroyedViews: 0
        };
    }
}

// 使用示例
const viewManager = new CalendarViewManager();

// 添加扩展
viewManager.addExtension('customFeatures', {
    getProps: (props, view) => ({
        enableCustomFeature: true
    })
});

// 创建视图
const viewId = viewManager.createView('calendar', {
    resModel: 'calendar.event',
    arch: '<calendar date_start="start_date"/>',
    relatedModels: {}
});

// 获取统计信息
const stats = viewManager.getViewStatistics();
console.log('View statistics:', stats);
```

## 技术特点

### 1. 简洁配置
- **最小配置**: 用最少的代码定义完整视图
- **清晰结构**: 清晰的配置结构和组织
- **标准接口**: 符合Odoo视图标准接口
- **易于扩展**: 容易扩展和定制

### 2. MVC架构
- **完整分离**: 完整的MVC组件分离
- **组件复用**: 组件的独立性和复用性
- **职责清晰**: 每个组件职责明确
- **松耦合**: 组件间松耦合设计

### 3. 动态属性处理
- **架构解析**: 动态解析XML架构
- **属性增强**: 增强和处理视图属性
- **配置传递**: 传递配置到组件
- **灵活扩展**: 支持属性的灵活扩展

### 4. 系统集成
- **注册机制**: 标准的视图注册机制
- **搜索集成**: 集成搜索菜单功能
- **按钮集成**: 集成控制按钮模板
- **全局可用**: 在整个系统中可用

## 设计模式

### 1. 配置模式 (Configuration Pattern)
- **视图配置**: 集中的视图配置定义
- **组件配置**: 配置MVC组件
- **功能配置**: 配置视图功能

### 2. 工厂模式 (Factory Pattern)
- **视图工厂**: 创建视图实例
- **组件工厂**: 创建MVC组件
- **属性工厂**: 处理视图属性

### 3. 注册模式 (Registry Pattern)
- **视图注册**: 注册视图到系统
- **类型映射**: 建立类型到视图的映射
- **全局访问**: 提供全局访问机制

### 4. 模板方法模式 (Template Method Pattern)
- **属性处理**: 标准的属性处理流程
- **视图创建**: 标准的视图创建流程
- **组件初始化**: 标准的组件初始化流程

## 注意事项

1. **组件依赖**: 确保所有MVC组件正确定义
2. **属性处理**: 正确处理和传递视图属性
3. **架构解析**: 确保架构解析的正确性
4. **注册顺序**: 注意组件的注册顺序

## 扩展建议

1. **配置增强**: 增强视图配置选项
2. **组件扩展**: 支持更多的组件类型
3. **属性验证**: 添加属性验证机制
4. **错误处理**: 增强错误处理和恢复
5. **性能优化**: 优化视图创建和渲染性能

该日历视图定义为Odoo Web客户端提供了完整的日历视图配置，通过简洁的配置、MVC架构和系统集成确保了日历视图的正确注册和高效运行。

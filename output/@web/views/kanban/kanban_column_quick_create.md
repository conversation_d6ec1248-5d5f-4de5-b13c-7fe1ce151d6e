# KanbanColumnQuickCreate - 看板列快速创建

## 概述

`kanban_column_quick_create.js` 是 Odoo Web 客户端看板视图的列快速创建组件，负责提供快速创建看板列的功能。该模块包含107行代码，是一个OWL组件，专门用于在看板视图中快速添加新的列，具备自动聚焦、外部点击关闭、热键支持、示例对话框、折叠展开等特性，是看板视图系统中列管理的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/kanban/kanban_column_quick_create.js`
- **行数**: 107
- **模块**: `@web/views/kanban/kanban_column_quick_create`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'                           // 翻译服务
'@web/core/hotkeys/hotkey_hook'                        // 热键钩子
'@web/core/utils/hooks'                                // 工具钩子
'@web/views/kanban/kanban_column_examples_dialog'      // 看板列示例对话框
'@odoo/owl'                                            // OWL框架
```

## 主要组件定义

### 1. KanbanColumnQuickCreate - 看板列快速创建组件

```javascript
class KanbanColumnQuickCreate extends Component {
    static template = "web.KanbanColumnQuickCreate";
    static props = {
        exampleData: [Object, { value: null }],
        onFoldChange: Function,
        onValidate: Function,
        folded: Boolean,
        groupByField: Object,
    };

    setup() {
        this.dialog = useService("dialog");
        this.root = useRef("root");
        this.state = useState({
            hasInputFocused: false,
        });

        useAutofocus();
        this.inputRef = useRef("autofocus");

        // Close on outside click
        useExternalListener(window, "mousedown", (ev) => {
            this.mousedownTarget = ev.target;
        });
        useExternalListener(window, "click", (ev) => {
            const target = this.mousedownTarget || ev.target;
            const gotClickedInside = this.root.el.contains(target);
            if (!gotClickedInside) {
                this.fold();
            }
            this.mousedownTarget = null;
        });

        // Hotkeys
        useHotkey("escape", () => this.fold());
        useHotkey("enter", () => this.validate());
    }
}
```

**组件特性**:
- **专用模板**: 使用KanbanColumnQuickCreate专用模板
- **属性配置**: 丰富的属性配置选项
- **服务集成**: 集成对话框服务
- **状态管理**: 管理组件的内部状态

## 核心功能

### 1. 外部点击处理

```javascript
// Close on outside click
useExternalListener(window, "mousedown", (ev) => {
    this.mousedownTarget = ev.target;
});
useExternalListener(window, "click", (ev) => {
    const target = this.mousedownTarget || ev.target;
    const gotClickedInside = this.root.el.contains(target);
    if (!gotClickedInside) {
        this.fold();
    }
    this.mousedownTarget = null;
});
```

**外部点击功能**:
- **点击检测**: 检测鼠标按下和点击事件
- **区域判断**: 判断点击是否在组件内部
- **自动关闭**: 外部点击时自动折叠组件
- **目标跟踪**: 跟踪鼠标按下的目标元素

### 2. 热键支持

```javascript
// Hotkeys
useHotkey("escape", () => this.fold());
useHotkey("enter", () => this.validate());
```

**热键功能**:
- **Escape键**: 按Escape键折叠组件
- **Enter键**: 按Enter键验证并创建列
- **快捷操作**: 提供快捷的键盘操作
- **用户体验**: 提升用户操作体验

### 3. 折叠展开控制

```javascript
fold() {
    this.props.onFoldChange(true);
}

unfold() {
    this.props.onFoldChange(false);
}

onInputFocus() {
    this.state.hasInputFocused = true;
}

onInputBlur() {
    this.state.hasInputFocused = false;
}
```

**折叠控制功能**:
- **折叠操作**: 折叠快速创建组件
- **展开操作**: 展开快速创建组件
- **焦点管理**: 管理输入框的焦点状态
- **状态同步**: 与父组件同步折叠状态

### 4. 验证和创建

```javascript
validate() {
    const input = this.inputRef.el;
    const value = input.value.trim();
    if (value) {
        this.props.onValidate(value);
        input.value = "";
    }
}

onKeydown(ev) {
    if (ev.key === "Enter") {
        ev.preventDefault();
        this.validate();
    } else if (ev.key === "Escape") {
        ev.preventDefault();
        this.fold();
    }
}
```

**验证创建功能**:
- **输入验证**: 验证输入的列名称
- **创建回调**: 调用父组件的创建回调
- **输入清理**: 创建后清理输入框
- **键盘事件**: 处理键盘事件

### 5. 示例对话框

```javascript
openExamples() {
    if (this.props.exampleData) {
        this.dialog.add(KanbanColumnExamplesDialog, {
            examples: this.props.exampleData.examples,
        });
    }
}
```

**示例对话框功能**:
- **示例显示**: 显示列创建的示例
- **对话框集成**: 集成示例对话框组件
- **数据传递**: 传递示例数据
- **用户指导**: 为用户提供创建指导

## 使用场景

### 1. 看板列快速创建管理器

```javascript
// 看板列快速创建管理器
class KanbanColumnQuickCreateManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置快速创建配置
        this.quickCreateConfig = {
            enableAutoFocus: true,
            enableHotkeys: true,
            enableExamples: true,
            enableValidation: true,
            enableAutoComplete: true,
            maxColumnNameLength: 50,
            minColumnNameLength: 1
        };
        
        // 设置验证规则
        this.validationRules = new Map();
        
        // 设置自动完成数据
        this.autoCompleteData = new Set();
        
        // 设置创建统计
        this.createStatistics = {
            totalCreated: 0,
            successfulCreated: 0,
            failedCreated: 0,
            averageNameLength: 0
        };
        
        this.initializeQuickCreateSystem();
    }
    
    // 初始化快速创建系统
    initializeQuickCreateSystem() {
        // 创建增强的快速创建组件
        this.createEnhancedQuickCreate();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置自动完成系统
        this.setupAutoCompleteSystem();
        
        // 设置示例系统
        this.setupExampleSystem();
    }
    
    // 创建增强的快速创建组件
    createEnhancedQuickCreate() {
        const originalQuickCreate = KanbanColumnQuickCreate;
        
        this.EnhancedKanbanColumnQuickCreate = class extends originalQuickCreate {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
                
                // 添加自动完成功能
                this.addAutoCompleteFeatures();
                
                // 添加统计功能
                this.addStatisticsFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = useState({
                    isValidating: false,
                    validationError: null,
                    suggestions: [],
                    showSuggestions: false,
                    selectedSuggestion: -1
                });
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的验证方法
                this.enhancedValidate = async () => {
                    const input = this.inputRef.el;
                    const value = input.value.trim();
                    
                    if (!value) {
                        this.showValidationError(_t("Column name cannot be empty"));
                        return;
                    }
                    
                    this.enhancedState.isValidating = true;
                    this.enhancedState.validationError = null;
                    
                    try {
                        // 执行验证
                        const isValid = await this.validateColumnName(value);
                        if (isValid) {
                            await this.createColumn(value);
                            this.recordSuccessfulCreation(value);
                            input.value = "";
                            this.hideSuggestions();
                        }
                    } catch (error) {
                        this.showValidationError(error.message);
                        this.recordFailedCreation(value, error);
                    } finally {
                        this.enhancedState.isValidating = false;
                    }
                };
                
                // 验证列名称
                this.validateColumnName = async (name) => {
                    // 长度验证
                    if (name.length < this.quickCreateConfig.minColumnNameLength) {
                        throw new Error(_t("Column name is too short"));
                    }
                    
                    if (name.length > this.quickCreateConfig.maxColumnNameLength) {
                        throw new Error(_t("Column name is too long"));
                    }
                    
                    // 字符验证
                    if (!/^[a-zA-Z0-9\s\-_]+$/.test(name)) {
                        throw new Error(_t("Column name contains invalid characters"));
                    }
                    
                    // 重复验证
                    const isDuplicate = await this.checkDuplicateName(name);
                    if (isDuplicate) {
                        throw new Error(_t("Column name already exists"));
                    }
                    
                    // 自定义验证规则
                    for (const [ruleName, rule] of this.validationRules) {
                        const result = await rule.validate(name);
                        if (!result.valid) {
                            throw new Error(result.message);
                        }
                    }
                    
                    return true;
                };
                
                // 检查重复名称
                this.checkDuplicateName = async (name) => {
                    // 实现重复名称检查逻辑
                    return false; // 简化实现
                };
                
                // 创建列
                this.createColumn = async (name) => {
                    // 调用原始验证方法
                    this.props.onValidate(name);
                    
                    // 添加到自动完成数据
                    this.autoCompleteData.add(name);
                };
                
                // 显示验证错误
                this.showValidationError = (message) => {
                    this.enhancedState.validationError = message;
                    
                    // 自动清除错误
                    setTimeout(() => {
                        this.enhancedState.validationError = null;
                    }, 3000);
                };
                
                // 处理输入变化
                this.onInputChange = (event) => {
                    const value = event.target.value;
                    
                    // 清除验证错误
                    this.enhancedState.validationError = null;
                    
                    // 更新建议
                    this.updateSuggestions(value);
                };
                
                // 更新建议
                this.updateSuggestions = (value) => {
                    if (!value || value.length < 2) {
                        this.hideSuggestions();
                        return;
                    }
                    
                    const suggestions = this.getSuggestions(value);
                    this.enhancedState.suggestions = suggestions;
                    this.enhancedState.showSuggestions = suggestions.length > 0;
                    this.enhancedState.selectedSuggestion = -1;
                };
                
                // 获取建议
                this.getSuggestions = (value) => {
                    const lowerValue = value.toLowerCase();
                    const suggestions = [];
                    
                    // 从自动完成数据中获取建议
                    for (const item of this.autoCompleteData) {
                        if (item.toLowerCase().includes(lowerValue)) {
                            suggestions.push(item);
                        }
                    }
                    
                    // 从预定义模板中获取建议
                    const templates = this.getColumnTemplates();
                    for (const template of templates) {
                        if (template.toLowerCase().includes(lowerValue)) {
                            suggestions.push(template);
                        }
                    }
                    
                    // 去重并限制数量
                    return [...new Set(suggestions)].slice(0, 5);
                };
                
                // 获取列模板
                this.getColumnTemplates = () => {
                    return [
                        'To Do', 'In Progress', 'Done', 'Review', 'Testing',
                        'Backlog', 'Sprint', 'Blocked', 'Cancelled', 'Archived',
                        'New', 'Open', 'Closed', 'Pending', 'Approved'
                    ];
                };
                
                // 隐藏建议
                this.hideSuggestions = () => {
                    this.enhancedState.showSuggestions = false;
                    this.enhancedState.suggestions = [];
                    this.enhancedState.selectedSuggestion = -1;
                };
                
                // 选择建议
                this.selectSuggestion = (index) => {
                    if (index >= 0 && index < this.enhancedState.suggestions.length) {
                        const suggestion = this.enhancedState.suggestions[index];
                        this.inputRef.el.value = suggestion;
                        this.hideSuggestions();
                        this.inputRef.el.focus();
                    }
                };
                
                // 处理键盘导航
                this.onKeyDown = (event) => {
                    if (this.enhancedState.showSuggestions) {
                        switch (event.key) {
                            case 'ArrowDown':
                                event.preventDefault();
                                this.navigateSuggestions(1);
                                break;
                            case 'ArrowUp':
                                event.preventDefault();
                                this.navigateSuggestions(-1);
                                break;
                            case 'Tab':
                            case 'Enter':
                                if (this.enhancedState.selectedSuggestion >= 0) {
                                    event.preventDefault();
                                    this.selectSuggestion(this.enhancedState.selectedSuggestion);
                                    return;
                                }
                                break;
                            case 'Escape':
                                event.preventDefault();
                                this.hideSuggestions();
                                return;
                        }
                    }
                    
                    // 调用原始键盘处理
                    this.onKeydown(event);
                };
                
                // 导航建议
                this.navigateSuggestions = (direction) => {
                    const maxIndex = this.enhancedState.suggestions.length - 1;
                    let newIndex = this.enhancedState.selectedSuggestion + direction;
                    
                    if (newIndex < 0) {
                        newIndex = maxIndex;
                    } else if (newIndex > maxIndex) {
                        newIndex = 0;
                    }
                    
                    this.enhancedState.selectedSuggestion = newIndex;
                };
                
                // 记录成功创建
                this.recordSuccessfulCreation = (name) => {
                    this.createStatistics.totalCreated++;
                    this.createStatistics.successfulCreated++;
                    this.updateAverageNameLength(name.length);
                };
                
                // 记录失败创建
                this.recordFailedCreation = (name, error) => {
                    this.createStatistics.totalCreated++;
                    this.createStatistics.failedCreated++;
                    console.error('Column creation failed:', error);
                };
                
                // 更新平均名称长度
                this.updateAverageNameLength = (length) => {
                    const total = this.createStatistics.successfulCreated;
                    this.createStatistics.averageNameLength = 
                        (this.createStatistics.averageNameLength * (total - 1) + length) / total;
                };
            }
            
            addValidationFeatures() {
                // 添加实时验证
                this.addRealTimeValidation = () => {
                    // 实现实时验证功能
                };
            }
            
            addAutoCompleteFeatures() {
                // 添加智能自动完成
                this.addSmartAutoComplete = () => {
                    // 实现智能自动完成功能
                };
            }
            
            addStatisticsFeatures() {
                // 添加使用统计
                this.addUsageStatistics = () => {
                    // 实现使用统计功能
                };
            }
            
            // 重写验证方法
            validate() {
                this.enhancedValidate();
            }
            
            // 重写键盘处理
            onKeydown(event) {
                this.onKeyDown(event);
            }
            
            // 获取创建统计
            getCreateStatistics() {
                return {
                    ...this.createStatistics,
                    successRate: this.createStatistics.totalCreated > 0 
                        ? (this.createStatistics.successfulCreated / this.createStatistics.totalCreated * 100).toFixed(2) + '%'
                        : '0%'
                };
            }
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        // 添加默认验证规则
        this.validationRules.set('length', {
            validate: (name) => ({
                valid: name.length >= this.quickCreateConfig.minColumnNameLength && 
                       name.length <= this.quickCreateConfig.maxColumnNameLength,
                message: _t("Column name length must be between %s and %s characters", 
                           this.quickCreateConfig.minColumnNameLength, 
                           this.quickCreateConfig.maxColumnNameLength)
            })
        });
        
        this.validationRules.set('characters', {
            validate: (name) => ({
                valid: /^[a-zA-Z0-9\s\-_]+$/.test(name),
                message: _t("Column name can only contain letters, numbers, spaces, hyphens and underscores")
            })
        });
    }
    
    // 设置自动完成系统
    setupAutoCompleteSystem() {
        // 预填充常用列名
        const commonColumns = [
            'To Do', 'In Progress', 'Done', 'Review', 'Testing',
            'Backlog', 'Sprint', 'Blocked', 'Cancelled', 'Archived'
        ];
        
        for (const column of commonColumns) {
            this.autoCompleteData.add(column);
        }
    }
    
    // 设置示例系统
    setupExampleSystem() {
        this.exampleConfig = {
            enableExamples: this.quickCreateConfig.enableExamples,
            exampleCategories: ['project', 'sales', 'support', 'hr']
        };
    }
    
    // 添加验证规则
    addValidationRule(name, rule) {
        this.validationRules.set(name, rule);
    }
    
    // 移除验证规则
    removeValidationRule(name) {
        this.validationRules.delete(name);
    }
    
    // 添加自动完成数据
    addAutoCompleteData(data) {
        if (Array.isArray(data)) {
            for (const item of data) {
                this.autoCompleteData.add(item);
            }
        } else {
            this.autoCompleteData.add(data);
        }
    }
    
    // 创建快速创建组件
    createQuickCreate(props) {
        return new this.EnhancedKanbanColumnQuickCreate(props);
    }
    
    // 获取创建统计
    getCreateStatistics() {
        return {
            ...this.createStatistics,
            validationRuleCount: this.validationRules.size,
            autoCompleteDataCount: this.autoCompleteData.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理验证规则
        this.validationRules.clear();
        
        // 清理自动完成数据
        this.autoCompleteData.clear();
        
        // 重置统计
        this.createStatistics = {
            totalCreated: 0,
            successfulCreated: 0,
            failedCreated: 0,
            averageNameLength: 0
        };
    }
}

// 使用示例
const quickCreateManager = new KanbanColumnQuickCreateManager();

// 创建快速创建组件
const quickCreate = quickCreateManager.createQuickCreate({
    exampleData: { examples: [] },
    onFoldChange: (folded) => console.log('Fold changed:', folded),
    onValidate: (name) => console.log('Column created:', name),
    folded: false,
    groupByField: { name: 'stage_id', type: 'many2one' }
});

// 获取统计信息
const stats = quickCreateManager.getCreateStatistics();
console.log('Quick create statistics:', stats);
```

## 技术特点

### 1. 用户体验
- **自动聚焦**: 自动聚焦到输入框
- **外部点击**: 外部点击自动关闭
- **热键支持**: 支持键盘快捷键
- **即时反馈**: 提供即时的用户反馈

### 2. 交互设计
- **折叠展开**: 支持组件的折叠和展开
- **输入验证**: 实时验证用户输入
- **示例指导**: 提供创建示例指导
- **错误处理**: 优雅的错误处理

### 3. 事件处理
- **鼠标事件**: 处理鼠标点击事件
- **键盘事件**: 处理键盘输入事件
- **焦点事件**: 处理输入框焦点事件
- **外部事件**: 处理外部点击事件

### 4. 状态管理
- **组件状态**: 管理组件内部状态
- **焦点状态**: 跟踪输入框焦点状态
- **折叠状态**: 管理组件折叠状态
- **验证状态**: 管理验证状态

## 设计模式

### 1. 组件模式 (Component Pattern)
- **封装性**: 封装快速创建功能
- **可复用**: 高度可复用的组件
- **接口标准**: 标准化的组件接口

### 2. 观察者模式 (Observer Pattern)
- **事件监听**: 监听用户交互事件
- **状态变化**: 响应状态变化
- **回调通知**: 通过回调通知父组件

### 3. 状态模式 (State Pattern)
- **状态管理**: 管理不同的组件状态
- **状态切换**: 在不同状态间切换
- **行为变化**: 根据状态改变行为

### 4. 策略模式 (Strategy Pattern)
- **验证策略**: 不同的验证策略
- **交互策略**: 不同的用户交互策略
- **显示策略**: 不同的显示策略

## 注意事项

1. **用户体验**: 确保快速创建的流畅性
2. **输入验证**: 完善的输入验证机制
3. **错误处理**: 优雅的错误处理和提示
4. **性能考虑**: 避免频繁的DOM操作

## 扩展建议

1. **自动完成**: 添加智能自动完成功能
2. **模板支持**: 支持列名模板
3. **批量创建**: 支持批量创建多个列
4. **拖拽支持**: 支持拖拽创建列
5. **历史记录**: 添加创建历史记录功能

该看板列快速创建组件为Odoo Web客户端提供了便捷的列创建功能，通过直观的界面和丰富的交互特性大大提升了用户的操作效率和体验。

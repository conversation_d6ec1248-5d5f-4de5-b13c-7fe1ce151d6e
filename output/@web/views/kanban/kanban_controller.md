# KanbanController - 看板视图控制器

## 概述

`kanban_controller.js` 是 Odoo Web 客户端看板视图的控制器组件，负责处理看板视图的业务逻辑和用户交互。该模块包含353行代码，是一个OWL组件，专门用于管理看板视图的状态、数据操作、用户交互等功能，具备记录管理、分组操作、快速创建、拖拽支持、进度条等特性，是看板视图系统中业务逻辑处理的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/kanban/kanban_controller.js`
- **行数**: 353
- **模块**: `@web/views/kanban/kanban_controller`

## 依赖关系

```javascript
// 核心依赖
'@web/core/confirmation_dialog/confirmation_dialog'     // 确认对话框
'@web/core/l10n/translation'                           // 翻译服务
'@web/core/utils/hooks'                                // 工具钩子
'@web/search/cog_menu/cog_menu'                        // 齿轮菜单
'@web/core/py_js/py'                                   // Python表达式
'@web/search/action_hook'                              // 动作钩子
'@web/search/layout'                                   // 搜索布局
'@web/search/pager_hook'                               // 分页钩子
'@web/search/search_bar/search_bar'                    // 搜索栏
'@web/search/search_bar/search_bar_toggler'            // 搜索栏切换器
'@web/session'                                         // 会话服务
'@web/model/model'                                     // 模型基类
'@web/views/standard_view_props'                       // 标准视图属性
'@web/views/view_button/multi_record_view_button'      // 多记录视图按钮
'@web/views/view_button/view_button_hook'              // 视图按钮钩子
'@web/model/relational_model/utils'                    // 关系模型工具
'@web/views/kanban/kanban_renderer'                    // 看板渲染器
'@web/views/kanban/progress_bar_hook'                  // 进度条钩子
'@odoo/owl'                                            // OWL框架
```

## 核心常量

### 1. 快速创建字段类型

```javascript
const QUICK_CREATE_FIELD_TYPES = ["char", "boolean", "many2one", "selection", "many2many"];
```

**常量说明**:
- **支持类型**: 定义支持快速创建的字段类型
- **用户体验**: 限制快速创建表单的复杂度
- **扩展性**: 可以扩展支持更多字段类型
- **性能考虑**: 避免复杂字段影响快速创建性能

## 主要组件定义

### 1. KanbanController - 看板视图控制器

```javascript
class KanbanController extends Component {
    static template = "web.KanbanView";
    static components = {
        Layout,
        KanbanRenderer,
        MultiRecordViewButton,
        SearchBar,
        CogMenu
    };
    static props = {
        ...standardViewProps,
        defaultGroupBy: {
            validate: (dgb) => !dgb || typeof dgb === "string",
            optional: true,
        },
        editable: { type: Boolean, optional: true },
        forceGlobalClick: { type: Boolean, optional: true },
        onSelectionChanged: { type: Function, optional: true },
        showButtons: { type: Boolean, optional: true },
        Compiler: { type: Function, optional: true },
        Model: Function,
        Renderer: Function,
        archInfo: Object,
        buttonTemplate: String,
    };

    setup() {
        this.actionService = useService("action");
        this.dialogService = useService("dialog");
        this.notification = useService("notification");
        this.orm = useService("orm");
        this.router = useService("router");
        this.uiService = useService("ui");
        this.user = useService("user");

        this.model = useModelWithSampleData(this.props.Model, this.modelParams);
        this.pager = usePager();
        this.searchBarToggler = useSearchBarToggler();
        this.viewButtons = useViewButtons();
        this.progressBar = useProgressBar();

        useSetupAction({
            getLocalState: () => this.getLocalState(),
            getContext: () => this.getActionContext(),
        });

        this.state = useState({
            showSearchBar: false,
        });

        this.rootRef = useRef("root");

        onMounted(this.onMounted);
        onWillUnmount(this.onWillUnmount);
    }
}
```

**组件特性**:
- **模板定义**: 使用KanbanView模板
- **子组件**: 集成布局、渲染器、按钮等组件
- **服务集成**: 集成多种核心服务
- **钩子使用**: 使用分页、搜索、按钮等钩子

## 核心功能

### 1. 模型参数配置

```javascript
get modelParams() {
    const { archInfo, fields, resModel, context, domain, orderBy, countLimit } = this.props;

    return {
        config: {
            resModel,
            fields,
            activeFields: archInfo.activeFields,
            handleField: archInfo.handleField,
            openGroupsByDefault: archInfo.openGroupsByDefault,
            limit: archInfo.limit,
            countLimit,
        },
        state: {
            context,
            domain,
            groupBy: this.getGroupBy(),
            orderBy,
            offset: 0,
        },
    };
}

getGroupBy() {
    const { defaultGroupBy, searchViewArch } = this.props;

    // 从搜索视图获取分组
    if (searchViewArch) {
        const groupByFromSearch = this.extractGroupByFromSearch(searchViewArch);
        if (groupByFromSearch.length > 0) {
            return groupByFromSearch;
        }
    }

    // 使用默认分组
    if (defaultGroupBy) {
        return [defaultGroupBy];
    }

    return [];
}
```

**配置功能**:
- **模型配置**: 配置关系模型参数
- **字段配置**: 设置活动字段和句柄字段
- **分组配置**: 配置分组相关参数
- **状态配置**: 配置上下文、域和排序

### 2. 记录操作

```javascript
// 创建记录
async createRecord(groupId = null) {
    const context = this.getCreateContext(groupId);

    if (this.canQuickCreate(groupId)) {
        return this.quickCreateRecord(groupId, context);
    } else {
        return this.openCreateForm(context);
    }
}

// 快速创建记录
async quickCreateRecord(groupId, context) {
    const group = this.model.root.groups.find(g => g.id === groupId);
    if (!group) {
        throw new Error(`Group ${groupId} not found`);
    }

    const record = await group.createRecord(context);
    return record;
}

// 打开创建表单
async openCreateForm(context) {
    const action = {
        type: "ir.actions.act_window",
        res_model: this.props.resModel,
        view_mode: "form",
        views: [[false, "form"]],
        target: "new",
        context: context,
    };

    return this.actionService.doAction(action);
}

// 编辑记录
async editRecord(record) {
    const action = {
        type: "ir.actions.act_window",
        res_model: this.props.resModel,
        res_id: record.resId,
        view_mode: "form",
        views: [[false, "form"]],
        target: "current",
    };

    return this.actionService.doAction(action);
}

// 删除记录
async deleteRecord(record) {
    const dialogProps = {
        body: deleteConfirmationMessage(1),
        confirm: async () => {
            await record.delete();
            this.notification.add(_t("Record deleted"), { type: "success" });
        },
        cancel: () => {},
    };

    this.dialogService.add(ConfirmationDialog, dialogProps);
}
```

**记录操作功能**:
- **创建记录**: 支持快速创建和表单创建
- **编辑记录**: 打开表单编辑记录
- **删除记录**: 删除记录并确认
- **上下文处理**: 处理创建和编辑的上下文

### 3. 分组操作

```javascript
// 创建分组
async createGroup(name, parentGroupId = null) {
    const groupByField = this.model.root.groupByField;
    if (!groupByField) {
        throw new Error("Cannot create group without groupBy field");
    }

    const context = {
        ...this.props.context,
        [groupByField]: name,
    };

    if (parentGroupId) {
        const parentGroup = this.model.root.groups.find(g => g.id === parentGroupId);
        if (parentGroup) {
            context.parent_id = parentGroup.value;
        }
    }

    const group = await this.model.root.createGroup(context);
    return group;
}

// 归档分组
async archiveGroup(group) {
    const dialogProps = {
        body: _t("Are you sure you want to archive this group and all its records?"),
        confirm: async () => {
            await group.archive();
            this.notification.add(_t("Group archived"), { type: "success" });
        },
        cancel: () => {},
    };

    this.dialogService.add(ConfirmationDialog, dialogProps);
}

// 删除分组
async deleteGroup(group) {
    const dialogProps = {
        body: _t("Are you sure you want to delete this group and all its records?"),
        confirm: async () => {
            await group.delete();
            this.notification.add(_t("Group deleted"), { type: "success" });
        },
        cancel: () => {},
    };

    this.dialogService.add(ConfirmationDialog, dialogProps);
}

// 折叠/展开分组
async toggleGroup(group) {
    if (group.isOpen) {
        await group.fold();
    } else {
        await group.unfold();
    }
}
```

**分组操作功能**:
- **创建分组**: 创建新的看板分组
- **归档分组**: 归档分组及其记录
- **删除分组**: 删除分组及其记录
- **折叠控制**: 控制分组的折叠和展开

### 4. 拖拽支持

```javascript
// 处理记录拖拽
async onRecordDrop(recordId, targetGroupId, targetIndex) {
    const record = this.model.root.records.find(r => r.resId === recordId);
    const targetGroup = this.model.root.groups.find(g => g.id === targetGroupId);

    if (!record || !targetGroup) {
        return;
    }

    // 更新记录的分组字段
    const groupByField = this.model.root.groupByField;
    if (groupByField) {
        await record.update({
            [groupByField]: targetGroup.value,
        });
    }

    // 更新记录的序列
    if (this.model.root.handleField) {
        await this.updateRecordSequence(record, targetGroup, targetIndex);
    }

    this.notification.add(_t("Record moved"), { type: "success" });
}

// 更新记录序列
async updateRecordSequence(record, targetGroup, targetIndex) {
    const handleField = this.model.root.handleField;
    const groupRecords = targetGroup.records;

    let newSequence;
    if (targetIndex === 0) {
        // 移动到开头
        newSequence = groupRecords.length > 0 ? groupRecords[0].data[handleField] - 1 : 1;
    } else if (targetIndex >= groupRecords.length) {
        // 移动到末尾
        newSequence = groupRecords.length > 0 ? groupRecords[groupRecords.length - 1].data[handleField] + 1 : 1;
    } else {
        // 移动到中间
        const prevRecord = groupRecords[targetIndex - 1];
        const nextRecord = groupRecords[targetIndex];
        newSequence = (prevRecord.data[handleField] + nextRecord.data[handleField]) / 2;
    }

    await record.update({
        [handleField]: newSequence,
    });
}

// 处理分组拖拽
async onGroupDrop(groupId, targetIndex) {
    const group = this.model.root.groups.find(g => g.id === groupId);
    if (!group) {
        return;
    }

    // 重新排序分组
    await this.model.root.reorderGroups(groupId, targetIndex);

    this.notification.add(_t("Group moved"), { type: "success" });
}
```

**拖拽支持功能**:
- **记录拖拽**: 支持记录在分组间拖拽
- **序列更新**: 自动更新记录的序列号
- **分组拖拽**: 支持分组的拖拽重排序
- **用户反馈**: 提供拖拽操作的用户反馈

### 5. 快速创建

```javascript
// 判断是否可以快速创建
canQuickCreate(groupId) {
    const { archInfo } = this.props;

    // 检查架构配置
    if (!archInfo.quickCreate) {
        return false;
    }

    // 检查分组
    if (groupId) {
        const group = this.model.root.groups.find(g => g.id === groupId);
        if (!group || !group.canCreateRecord) {
            return false;
        }
    }

    // 检查字段类型
    const requiredFields = this.getRequiredFields();
    for (const field of requiredFields) {
        if (!QUICK_CREATE_FIELD_TYPES.includes(field.type)) {
            return false;
        }
    }

    return true;
}

// 获取必需字段
getRequiredFields() {
    const { fields } = this.props;
    const requiredFields = [];

    for (const [fieldName, field] of Object.entries(fields)) {
        if (field.required && !field.readonly) {
            requiredFields.push({
                name: fieldName,
                type: field.type,
                ...field,
            });
        }
    }

    return requiredFields;
}

// 获取创建上下文
getCreateContext(groupId) {
    const context = { ...this.props.context };

    if (groupId) {
        const group = this.model.root.groups.find(g => g.id === groupId);
        if (group) {
            const groupByField = this.model.root.groupByField;
            if (groupByField) {
                context[groupByField] = group.value;
            }
        }
    }

    return context;
}
```

**快速创建功能**:
- **可行性检查**: 检查是否可以进行快速创建
- **字段类型**: 限制支持的字段类型
- **上下文处理**: 处理创建记录的上下文
- **分组关联**: 自动关联到对应分组
# KanbanHeader - 看板头部组件

## 概述

`kanban_header.js` 是 Odoo Web 客户端看板视图的头部组件，负责渲染和管理看板列的头部区域。该模块包含309行代码，是一个OWL组件，专门用于显示列标题、进度条、下拉菜单、工具提示等功能，具备分组管理、快速创建、进度显示、交互控制等特性，是看板视图系统中列头部管理的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/kanban/kanban_header.js`
- **行数**: 309
- **模块**: `@web/views/kanban/kanban_header`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'                       // 翻译服务
'@odoo/owl'                                        // OWL框架
'@web/core/confirmation_dialog/confirmation_dialog' // 确认对话框
'@web/core/dropdown/dropdown'                      // 下拉菜单
'@web/core/dropdown/dropdown_item'                 // 下拉菜单项
'@web/core/popover/popover_hook'                   // 弹出框钩子
'@web/core/utils/functions'                        // 工具函数
'@web/core/utils/hooks'                            // 工具钩子
'@web/core/utils/timing'                           // 时间工具
'@web/model/relational_model/utils'                // 关系模型工具
'@web/views/utils'                                 // 视图工具
'@web/views/view_components/column_progress'       // 列进度组件
'@web/views/view_dialogs/form_view_dialog'         // 表单视图对话框
'@web/core/registry'                               // 注册表
'@web/core/ui/ui_service'                          // UI服务
```

## 主要组件定义

### 1. KanbanHeaderTooltip - 看板头部工具提示

```javascript
class KanbanHeaderTooltip extends Component {
    static template = "web.KanbanGroupTooltip";
    static props = {
        tooltip: Array,
        close: Function,
    };
}
```

**工具提示特性**:
- **专用模板**: 使用KanbanGroupTooltip模板
- **提示数据**: 接收工具提示数据数组
- **关闭回调**: 提供关闭回调函数
- **简洁设计**: 简洁的工具提示组件

### 2. KanbanHeader - 看板头部组件

```javascript
class KanbanHeader extends Component {
    static template = "web.KanbanHeader";
    static components = { ColumnProgress, Dropdown, DropdownItem };
    static props = {
        activeActions: { type: Object },
        canQuickCreate: { type: Boolean },
        deleteGroup: { type: Function },
        dialogClose: { type: Array },
        group: { type: Object },
        list: { type: Object },
        quickCreateState: { type: Object },
        scrollTop: { type: Function },
        tooltipInfo: { type: Object },
        progressBarState: { type: true, optional: true },
    };

    setup() {
        this.dialog = useService("dialog");
        this.orm = useService("orm");
        this.popover = usePopover(KanbanHeaderTooltip);
        this.rootRef = useRef("root");

        this.debouncedUpdateTooltip = useDebounced(this.updateTooltip, 400);
        this.memoizedGetTooltipInfo = memoize(this.getTooltipInfo.bind(this));
    }
}
```

**头部组件特性**:
- **专用模板**: 使用KanbanHeader专用模板
- **子组件**: 集成进度条、下拉菜单等组件
- **丰富属性**: 支持多种配置属性
- **服务集成**: 集成对话框、ORM等服务

## 核心功能

### 1. 初始化设置

```javascript
setup() {
    this.dialog = useService("dialog");
    this.orm = useService("orm");
    this.popover = usePopover(KanbanHeaderTooltip);
    this.rootRef = useRef("root");

    this.debouncedUpdateTooltip = useDebounced(this.updateTooltip, 400);
    this.memoizedGetTooltipInfo = memoize(this.getTooltipInfo.bind(this));
}
```

**初始化功能**:
- **服务注入**: 注入对话框和ORM服务
- **弹出框**: 设置工具提示弹出框
- **引用管理**: 管理根元素引用
- **防抖处理**: 防抖的工具提示更新
- **记忆化**: 记忆化的工具提示信息获取

### 2. 分组信息显示

```javascript
get groupDisplayName() {
    const { group } = this.props;
    if (group.displayName) {
        return group.displayName;
    }
    return group.value || _t("Undefined");
}

get groupCount() {
    const { group } = this.props;
    return group.count || 0;
}

get hasProgressBar() {
    const { progressBarState } = this.props;
    return progressBarState && progressBarState.colors;
}
```

**信息显示功能**:
- **显示名称**: 获取分组的显示名称
- **记录计数**: 获取分组中的记录数量
- **进度条**: 检查是否显示进度条
- **默认处理**: 提供合理的默认值

### 3. 工具提示管理

```javascript
getTooltipInfo() {
    const { tooltipInfo, group } = this.props;
    if (!tooltipInfo || !group) {
        return null;
    }

    const tooltip = [];
    for (const [fieldName, fieldInfo] of Object.entries(tooltipInfo)) {
        const value = group.aggregates[fieldName];
        if (value !== undefined && value !== null) {
            tooltip.push({
                label: fieldInfo.string,
                value: this.formatTooltipValue(value, fieldInfo),
            });
        }
    }

    return tooltip.length > 0 ? tooltip : null;
}

formatTooltipValue(value, fieldInfo) {
    switch (fieldInfo.type) {
        case 'monetary':
            return this.formatMonetary(value, fieldInfo);
        case 'float':
            return this.formatFloat(value, fieldInfo);
        case 'integer':
            return this.formatInteger(value);
        default:
            return value.toString();
    }
}

updateTooltip() {
    const tooltipInfo = this.memoizedGetTooltipInfo();
    if (tooltipInfo) {
        this.popover.open(this.rootRef.el, {
            tooltip: tooltipInfo,
            close: () => this.popover.close(),
        });
    }
}
```

**工具提示功能**:
- **信息收集**: 收集工具提示信息
- **值格式化**: 格式化不同类型的值
- **动态更新**: 动态更新工具提示内容
- **记忆化**: 使用记忆化优化性能

### 4. 分组操作

```javascript
async onEditGroup() {
    const { group } = this.props;
    if (!group.resId) {
        return;
    }

    this.dialog.add(FormViewDialog, {
        resModel: group.resModel,
        resId: group.resId,
        title: _t("Edit %s", group.displayName),
        mode: "edit",
        onRecordSaved: () => {
            this.props.list.load();
        },
    });
}

async onDeleteGroup() {
    const { group, deleteGroup } = this.props;

    this.dialog.add(ConfirmationDialog, {
        body: _t("Are you sure you want to delete this group?"),
        confirm: async () => {
            await deleteGroup(group);
        },
        cancel: () => {},
    });
}

async onArchiveGroup() {
    const { group } = this.props;
    if (!group.resId) {
        return;
    }

    try {
        await this.orm.write(group.resModel, [group.resId], {
            active: false,
        });
        this.props.list.load();
    } catch (error) {
        console.error("Failed to archive group:", error);
    }
}

onFoldGroup() {
    const { group } = this.props;
    if (group.isFolded) {
        group.unfold();
    } else {
        group.fold();
    }
}
```

**分组操作功能**:
- **编辑分组**: 打开表单对话框编辑分组
- **删除分组**: 确认后删除分组
- **归档分组**: 归档分组记录
- **折叠分组**: 折叠或展开分组

### 5. 快速创建控制

```javascript
get canQuickCreate() {
    const { canQuickCreate, group } = this.props;
    return canQuickCreate && group && !group.isFolded;
}

onQuickCreate() {
    const { quickCreateState, group } = this.props;
    if (this.canQuickCreate) {
        quickCreateState.open(group.id);
    }
}

onAddRecord() {
    const { group } = this.props;
    if (group) {
        this.openCreateDialog(group);
    }
}

openCreateDialog(group) {
    const context = {
        ...this.props.list.context,
        default_stage_id: group.resId,
    };

    this.dialog.add(FormViewDialog, {
        resModel: this.props.list.resModel,
        context: context,
        title: _t("Create Record"),
        mode: "edit",
        onRecordSaved: () => {
            this.props.list.load();
        },
    });
}
```

**快速创建功能**:
- **创建条件**: 检查是否可以快速创建
- **快速创建**: 触发快速创建功能
- **添加记录**: 打开表单对话框添加记录
- **上下文处理**: 处理创建记录的上下文

### 6. 进度条管理

```javascript
get progressBarData() {
    const { progressBarState, group } = this.props;
    if (!progressBarState || !group) {
        return null;
    }

    const { colors, field } = progressBarState;
    const aggregates = group.aggregates;

    const data = [];
    for (const [value, color] of Object.entries(colors)) {
        const count = aggregates[`${field}:${value}`] || 0;
        if (count > 0) {
            data.push({
                value: value,
                count: count,
                color: color,
                percentage: (count / group.count) * 100,
            });
        }
    }

    return data.length > 0 ? data : null;
}

onProgressBarClick(value) {
    const { group, list } = this.props;
    const domain = [
        ...list.domain,
        [list.groupByField, '=', group.value],
        [progressBarState.field, '=', value],
    ];

    // 触发搜索或过滤
    this.triggerFilter(domain);
}

triggerFilter(domain) {
    // 实现过滤逻辑
    console.log('Filter triggered with domain:', domain);
}
```

**进度条功能**:
- **数据计算**: 计算进度条的数据
- **百分比**: 计算各状态的百分比
- **点击处理**: 处理进度条的点击事件
- **过滤触发**: 触发相应的过滤操作
# KanbanRecord - 看板记录组件

## 概述

`kanban_record.js` 是 Odoo Web 客户端看板视图的记录组件，负责渲染和管理单个看板记录卡片。该模块包含414行代码，是一个OWL组件，专门用于显示看板记录的详细信息、处理用户交互、管理字段显示等，具备模板编译、字段渲染、按钮处理、颜色管理、拖拽支持等特性，是看板视图系统中记录展示的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/kanban/kanban_record.js`
- **行数**: 414
- **模块**: `@web/views/kanban/kanban_record`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'                       // 翻译服务
'@web/core/colorlist/colorlist'                    // 颜色列表
'@web/core/py_js/py'                               // Python表达式
'@web/core/dropdown/dropdown'                      // 下拉菜单
'@web/core/dropdown/dropdown_item'                 // 下拉菜单项
'@web/core/registry'                               // 注册表
'@web/core/user'                                   // 用户服务
'@web/core/utils/hooks'                            // 工具钩子
'@web/core/utils/urls'                             // URL工具
'@web/model/relational_model/utils'                // 关系模型工具
'@web/views/fields/field'                          // 字段组件
'@web/views/fields/image/image_field'              // 图片字段
'@web/views/view_button/view_button'               // 视图按钮
'@web/views/view_compiler'                         // 视图编译器
'@web/views/widgets/widget'                        // 小部件
'@web/views/utils'                                 // 视图工具
'@web/views/kanban/kanban_arch_parser'             // 看板架构解析器
'@web/views/kanban/kanban_compiler'                // 看板编译器
'@web/views/kanban/kanban_cover_image_dialog'      // 看板封面图片对话框
'@web/views/kanban/kanban_dropdown_menu_wrapper'   // 看板下拉菜单包装器
'@odoo/owl'                                        // OWL框架
```

## 核心常量和工具函数

### 1. 全局点击取消选择器

```javascript
const CANCEL_GLOBAL_CLICK = ["a", ".dropdown", ".oe_kanban_action", "[data-bs-toggle]"].join(",");
```

**选择器功能**:
- **点击过滤**: 定义哪些元素点击时不触发全局点击
- **交互控制**: 控制记录的点击行为
- **事件阻止**: 阻止特定元素的事件冒泡
- **用户体验**: 提升用户交互体验

### 2. 颜色索引计算函数

```javascript
function getColorIndex(value) {
    if (typeof value === "number") {
        return Math.round(value) % COLORS.length;
    }
    if (typeof value === "string") {
        let hash = 0;
        for (let i = 0; i < value.length; i++) {
            hash = ((hash << 5) - hash + value.charCodeAt(i)) & 0xffffffff;
        }
        return Math.abs(hash) % COLORS.length;
    }
    return 0;
}
```

**颜色计算功能**:
- **数值处理**: 处理数值类型的颜色索引
- **字符串哈希**: 为字符串生成哈希值
- **索引映射**: 映射到颜色数组索引
- **默认处理**: 提供默认颜色索引

## 主要组件定义

### 1. KanbanRecord - 看板记录组件

```javascript
class KanbanRecord extends Component {
    static template = "web.KanbanRecord";
    static components = {
        Dropdown,
        DropdownItem,
        Field,
        ViewButton,
        Widget,
        KanbanDropdownMenuWrapper,
    };
    static props = {
        archInfo: Object,
        canResequence: { type: Boolean, optional: true },
        forceGlobalClick: { type: Boolean, optional: true },
        list: Object,
        openRecord: { type: Function, optional: true },
        readonly: { type: Boolean, optional: true },
        record: Object,
        templates: Object,
    };

    setup() {
        this.dialog = useService("dialog");
        this.notification = useService("notification");
        this.orm = useService("orm");
        this.uiService = useService("ui");

        this.rootRef = useRef("root");
        this.state = useState({
            showDropdown: false,
        });

        this.compileTemplate();

        useRecordObserver((record) => {
            if (record === this.props.record) {
                this.render();
            }
        });

        onWillUpdateProps((nextProps) => {
            if (nextProps.templates !== this.props.templates) {
                this.compileTemplate();
            }
        });
    }
}
```

**记录组件特性**:
- **专用模板**: 使用KanbanRecord专用模板
- **子组件**: 集成字段、按钮、小部件等组件
- **丰富属性**: 支持多种配置属性
- **服务集成**: 集成对话框、通知、ORM等服务

## 核心功能

### 1. 模板编译

```javascript
compileTemplate() {
    const { templates, archInfo } = this.props;

    // 获取卡片模板
    const cardTemplate = templates[KANBAN_CARD_ATTRIBUTE] ||
                        templates[LEGACY_KANBAN_BOX_ATTRIBUTE];

    // 获取菜单模板
    const menuTemplate = templates[KANBAN_MENU_ATTRIBUTE] ||
                        templates[LEGACY_KANBAN_MENU_ATTRIBUTE];

    if (cardTemplate) {
        this.compiledCardTemplate = this.compileCardTemplate(cardTemplate);
    }

    if (menuTemplate) {
        this.compiledMenuTemplate = this.compileMenuTemplate(menuTemplate);
    }
}

compileCardTemplate(template) {
    const compiler = new KanbanCompiler();
    return compiler.compile(template, {
        record: this.props.record,
        readonly: this.props.readonly,
        archInfo: this.props.archInfo,
    });
}

compileMenuTemplate(template) {
    const compiler = new KanbanCompiler();
    return compiler.compile(template, {
        record: this.props.record,
        readonly: this.props.readonly,
        archInfo: this.props.archInfo,
    });
}
```

**模板编译功能**:
- **模板获取**: 获取卡片和菜单模板
- **编译器使用**: 使用看板编译器编译模板
- **参数传递**: 传递记录和配置参数
- **缓存编译**: 缓存编译结果

### 2. 记录点击处理

```javascript
onGlobalClick(ev) {
    if (this.props.forceGlobalClick || !this.shouldCancelGlobalClick(ev)) {
        this.openRecord();
    }
}

shouldCancelGlobalClick(ev) {
    const target = ev.target;

    // 检查是否点击了取消全局点击的元素
    if (target.closest(CANCEL_GLOBAL_CLICK)) {
        return true;
    }

    // 检查是否点击了可编辑字段
    if (target.closest('.o_field_widget[contenteditable="true"]')) {
        return true;
    }

    // 检查是否点击了输入元素
    if (target.matches('input, textarea, select')) {
        return true;
    }

    return false;
}

openRecord() {
    if (this.props.openRecord) {
        this.props.openRecord(this.props.record);
    }
}
```

**点击处理功能**:
- **全局点击**: 处理记录的全局点击事件
- **点击过滤**: 过滤不应触发打开的点击
- **记录打开**: 打开记录详情
- **事件控制**: 控制事件的传播

### 3. 字段渲染

```javascript
renderField(fieldName, fieldInfo) {
    const { record } = this.props;
    const value = record.data[fieldName];

    if (value === undefined || value === null) {
        return '';
    }

    // 根据字段类型渲染
    switch (fieldInfo.type) {
        case 'char':
        case 'text':
            return this.renderTextField(value, fieldInfo);
        case 'integer':
        case 'float':
            return this.renderNumberField(value, fieldInfo);
        case 'boolean':
            return this.renderBooleanField(value, fieldInfo);
        case 'date':
        case 'datetime':
            return this.renderDateField(value, fieldInfo);
        case 'many2one':
            return this.renderMany2oneField(value, fieldInfo);
        case 'selection':
            return this.renderSelectionField(value, fieldInfo);
        default:
            return this.renderDefaultField(value, fieldInfo);
    }
}

renderTextField(value, fieldInfo) {
    if (fieldInfo.widget === 'email') {
        return `<a href="mailto:${value}">${value}</a>`;
    }
    if (fieldInfo.widget === 'url') {
        return `<a href="${value}" target="_blank">${value}</a>`;
    }
    return value.toString();
}

renderNumberField(value, fieldInfo) {
    const formatter = formatters.get(fieldInfo.type);
    return formatter ? formatter(value, fieldInfo) : value.toString();
}

renderBooleanField(value, fieldInfo) {
    return value ? '✓' : '✗';
}

renderDateField(value, fieldInfo) {
    const formatter = formatters.get(fieldInfo.type);
    return formatter ? formatter(value, fieldInfo) : value.toString();
}

renderMany2oneField(value, fieldInfo) {
    if (Array.isArray(value) && value.length >= 2) {
        return value[1]; // 显示名称
    }
    return value ? value.toString() : '';
}

renderSelectionField(value, fieldInfo) {
    const selection = fieldInfo.selection;
    if (selection) {
        const option = selection.find(opt => opt[0] === value);
        return option ? option[1] : value.toString();
    }
    return value.toString();
}

renderDefaultField(value, fieldInfo) {
    return getFormattedValue(value, fieldInfo);
}
```

**字段渲染功能**:
- **类型识别**: 识别不同的字段类型
- **格式化**: 根据类型格式化字段值
- **小部件**: 支持不同的字段小部件
- **默认处理**: 提供默认的渲染逻辑

### 4. 颜色管理

```javascript
getRecordColor() {
    const { archInfo, record } = this.props;
    const colorField = archInfo.colorField;

    if (colorField && record.data[colorField] !== undefined) {
        return getColorIndex(record.data[colorField]);
    }

    return 0;
}

getCardColorClass() {
    const colorIndex = this.getRecordColor();
    return `oe_kanban_color_${colorIndex}`;
}

setRecordColor(colorIndex) {
    const { archInfo, record } = this.props;
    const colorField = archInfo.colorField;

    if (colorField) {
        record.update({
            [colorField]: colorIndex,
        });
    }
}
```

**颜色管理功能**:
- **颜色获取**: 获取记录的颜色索引
- **样式类**: 生成颜色样式类
- **颜色设置**: 设置记录的颜色
- **字段映射**: 映射颜色字段

### 5. 封面图片管理

```javascript
get hasCoverImage() {
    const { archInfo, record } = this.props;
    const coverField = archInfo.coverField;
    return coverField && record.data[coverField];
}

getCoverImageUrl() {
    const { archInfo, record } = this.props;
    const coverField = archInfo.coverField;

    if (coverField && record.data[coverField]) {
        const attachmentId = record.data[coverField][0];
        return imageUrl('ir.attachment', attachmentId, 'datas');
    }

    return null;
}

onSetCoverImage() {
    const { archInfo, record } = this.props;
    const coverField = archInfo.coverField;

    if (coverField) {
        this.dialog.add(KanbanCoverImageDialog, {
            record: record,
            fieldName: coverField,
            autoOpen: true,
        });
    }
}

onRemoveCoverImage() {
    const { archInfo, record } = this.props;
    const coverField = archInfo.coverField;

    if (coverField) {
        record.update({
            [coverField]: false,
        });
    }
}
```

**封面图片功能**:
- **图片检查**: 检查是否有封面图片
- **URL生成**: 生成封面图片URL
- **图片设置**: 打开封面图片设置对话框
- **图片移除**: 移除封面图片

### 6. 按钮处理

```javascript
onButtonClick(buttonName, buttonType) {
    const { record } = this.props;

    switch (buttonType) {
        case 'object':
            this.executeObjectButton(buttonName, record);
            break;
        case 'action':
            this.executeActionButton(buttonName, record);
            break;
        case 'edit':
            this.editRecord(record);
            break;
        case 'delete':
            this.deleteRecord(record);
            break;
        case 'archive':
            this.archiveRecord(record);
            break;
        case 'set_cover':
            this.onSetCoverImage();
            break;
        default:
            console.warn(`Unknown button type: ${buttonType}`);
    }
}

async executeObjectButton(methodName, record) {
    try {
        await this.orm.call(record.resModel, methodName, [record.resId]);
        this.notification.add(_t("Action executed successfully"), { type: "success" });
        record.load();
    } catch (error) {
        this.notification.add(_t("Failed to execute action"), { type: "danger" });
        console.error("Button execution failed:", error);
    }
}

async executeActionButton(actionName, record) {
    try {
        const action = await this.orm.call(record.resModel, actionName, [record.resId]);
        if (action) {
            this.actionService.doAction(action);
        }
    } catch (error) {
        this.notification.add(_t("Failed to execute action"), { type: "danger" });
        console.error("Action execution failed:", error);
    }
}

editRecord(record) {
    if (this.props.openRecord) {
        this.props.openRecord(record, { mode: 'edit' });
    }
}

async deleteRecord(record) {
    try {
        await record.delete();
        this.notification.add(_t("Record deleted"), { type: "success" });
    } catch (error) {
        this.notification.add(_t("Failed to delete record"), { type: "danger" });
        console.error("Delete failed:", error);
    }
}

async archiveRecord(record) {
    try {
        await record.update({ active: false });
        this.notification.add(_t("Record archived"), { type: "success" });
    } catch (error) {
        this.notification.add(_t("Failed to archive record"), { type: "danger" });
        console.error("Archive failed:", error);
    }
}
```

**按钮处理功能**:
- **按钮识别**: 识别不同类型的按钮
- **方法执行**: 执行对象方法
- **动作执行**: 执行动作按钮
- **记录操作**: 处理编辑、删除、归档等操作
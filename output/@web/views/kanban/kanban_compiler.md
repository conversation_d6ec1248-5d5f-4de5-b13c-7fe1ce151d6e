# KanbanCompiler - 看板编译器

## 概述

`kanban_compiler.js` 是 Odoo Web 客户端看板视图的编译器组件，负责将看板视图的XML架构编译为可执行的模板代码。该模块包含218行代码，是一个专门的编译器类，继承自ViewCompiler，用于处理看板特有的编译逻辑，具备模板编译、图片处理、按钮编译、下拉菜单等特性，是看板视图系统中模板编译的核心工具。

## 文件信息
- **路径**: `/web/static/src/views/kanban/kanban_compiler.js`
- **行数**: 218
- **模块**: `@web/views/kanban/kanban_compiler`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/xml'           // XML工具
'@web/views/utils'              // 视图工具
'@web/views/view_compiler'      // 视图编译器基类
```

## 核心常量

### 1. 动作类型常量

```javascript
const ACTION_TYPES = ["action", "object"];
const SPECIAL_TYPES = [
    ...ACTION_TYPES,
    "edit",
    "open",
    "delete",
    "url",
    "set_cover",
    "archive",
    "unarchive",
];
```

**常量说明**:
- **动作类型**: 定义支持的动作类型
- **特殊类型**: 包含所有特殊的按钮类型
- **扩展性**: 支持扩展新的动作类型
- **类型检查**: 用于验证按钮类型的有效性

### 2. 下拉菜单定义

```javascript
/**
 * @typedef {Object} DropdownDef
 * @property {Element} el
 * @property {boolean} inserted
 * @property {boolean} shouldInsert
 * @property {("dropdown" | "toggler" | "menu")[]} parts
 */
```

**下拉菜单结构**:
- **元素引用**: 下拉菜单的DOM元素
- **插入状态**: 是否已插入到DOM中
- **插入标志**: 是否应该插入
- **组成部分**: 下拉菜单的组成部分

## 主要编译器定义

### 1. KanbanCompiler - 看板编译器

```javascript
class KanbanCompiler extends ViewCompiler {
    setup() {
        this.ctx.readonly = "read_only_mode";
        this.compilers.push(
            { selector: "t[t-call]", fn: this.compileTCall },
            { selector: "img", fn: this.compileImage }
        );
    }
}
```

**编译器特性**:
- **继承设计**: 继承ViewCompiler基类
- **上下文设置**: 设置只读模式上下文
- **编译器注册**: 注册特定的编译器函数
- **选择器匹配**: 使用CSS选择器匹配元素

## 核心编译功能

### 1. 模板调用编译

```javascript
compileTCall(el, params) {
    const tCall = el.getAttribute("t-call");

    // 处理看板特定的模板调用
    if (tCall === "kanban-box" || tCall === "card") {
        return this.compileKanbanCard(el, params);
    } else if (tCall === "kanban-menu" || tCall === "menu") {
        return this.compileKanbanMenu(el, params);
    }

    // 默认处理
    return super.compileTCall(el, params);
}

compileKanbanCard(el, params) {
    // 编译看板卡片模板
    const cardEl = createElement("div");
    cardEl.setAttribute("class", "o_kanban_record");

    // 添加记录属性
    cardEl.setAttribute("t-att-data-id", "record.id");
    cardEl.setAttribute("t-att-tabindex", "record.id ? 0 : -1");

    // 编译子元素
    for (const child of el.children) {
        const compiledChild = this.compileNode(child, params);
        if (compiledChild) {
            append(cardEl, compiledChild);
        }
    }

    return cardEl;
}

compileKanbanMenu(el, params) {
    // 编译看板菜单模板
    const menuEl = createElement("div");
    menuEl.setAttribute("class", "o_kanban_menu");

    // 编译菜单项
    for (const child of el.children) {
        if (child.tagName === "a" || child.tagName === "button") {
            const compiledItem = this.compileMenuItem(child, params);
            if (compiledItem) {
                append(menuEl, compiledItem);
            }
        }
    }

    return menuEl;
}
```

**模板编译功能**:
- **模板识别**: 识别不同类型的模板调用
- **卡片编译**: 编译看板卡片模板
- **菜单编译**: 编译看板菜单模板
- **子元素处理**: 递归编译子元素

### 2. 图片编译

```javascript
compileImage(el, params) {
    const src = el.getAttribute("src");
    const tAttSrc = el.getAttribute("t-att-src");

    if (src && src.startsWith("/web/image/")) {
        // 处理Odoo图片URL
        return this.compileOdooImage(el, params);
    } else if (tAttSrc) {
        // 处理动态图片源
        return this.compileDynamicImage(el, params);
    }

    // 默认图片处理
    return super.compileImage(el, params);
}

compileOdooImage(el, params) {
    const imgEl = createElement("img");

    // 复制属性
    const attributes = extractAttributes(el);
    combineAttributes(imgEl, attributes);

    // 添加看板特定的图片属性
    imgEl.setAttribute("t-att-alt", "record.display_name");
    imgEl.setAttribute("loading", "lazy");

    // 处理图片错误
    imgEl.setAttribute("t-on-error", "this.onImageError");

    return imgEl;
}

compileDynamicImage(el, params) {
    const imgEl = createElement("img");

    // 处理动态源
    const tAttSrc = el.getAttribute("t-att-src");
    imgEl.setAttribute("t-att-src", tAttSrc);

    // 添加默认属性
    imgEl.setAttribute("t-att-alt", "record.display_name || ''");
    imgEl.setAttribute("loading", "lazy");

    return imgEl;
}
```

**图片编译功能**:
- **URL识别**: 识别Odoo图片URL
- **动态源**: 处理动态图片源
- **属性复制**: 复制原始图片属性
- **错误处理**: 添加图片加载错误处理
- **性能优化**: 添加懒加载属性

### 3. 按钮编译

```javascript
compileButton(el, params) {
    const type = el.getAttribute("type");
    const name = el.getAttribute("name");

    if (SPECIAL_TYPES.includes(type)) {
        return this.compileSpecialButton(el, params);
    }

    // 默认按钮编译
    return super.compileButton(el, params);
}

compileSpecialButton(el, params) {
    const type = el.getAttribute("type");
    const buttonEl = createElement("button");

    // 设置基础属性
    buttonEl.setAttribute("type", "button");
    buttonEl.setAttribute("class", `btn btn-sm o_kanban_button_${type}`);

    // 设置点击处理
    buttonEl.setAttribute("t-on-click", `() => this.onButtonClick('${type}', record)`);

    // 处理不同类型的按钮
    switch (type) {
        case "edit":
            this.compileEditButton(buttonEl, el, params);
            break;
        case "delete":
            this.compileDeleteButton(buttonEl, el, params);
            break;
        case "archive":
            this.compileArchiveButton(buttonEl, el, params);
            break;
        case "set_cover":
            this.compileCoverButton(buttonEl, el, params);
            break;
        default:
            this.compileActionButton(buttonEl, el, params);
    }

    return buttonEl;
}

compileEditButton(buttonEl, el, params) {
    buttonEl.setAttribute("title", "Edit");
    buttonEl.innerHTML = '<i class="fa fa-pencil"></i>';
}

compileDeleteButton(buttonEl, el, params) {
    buttonEl.setAttribute("title", "Delete");
    buttonEl.setAttribute("class", buttonEl.getAttribute("class") + " text-danger");
    buttonEl.innerHTML = '<i class="fa fa-trash"></i>';
}

compileArchiveButton(buttonEl, el, params) {
    buttonEl.setAttribute("title", "Archive");
    buttonEl.innerHTML = '<i class="fa fa-archive"></i>';
}

compileCoverButton(buttonEl, el, params) {
    buttonEl.setAttribute("title", "Set Cover Image");
    buttonEl.innerHTML = '<i class="fa fa-picture-o"></i>';
}

compileActionButton(buttonEl, el, params) {
    const string = el.getAttribute("string") || el.textContent;
    const icon = el.getAttribute("icon");

    if (icon) {
        buttonEl.innerHTML = `<i class="${icon}"></i> ${string}`;
    } else {
        buttonEl.textContent = string;
    }
}
```

**按钮编译功能**:
- **类型识别**: 识别不同类型的按钮
- **特殊按钮**: 处理看板特有的按钮类型
- **样式设置**: 设置按钮的样式和图标
- **事件绑定**: 绑定按钮点击事件
- **条件渲染**: 根据类型渲染不同的按钮

### 4. 下拉菜单编译

```javascript
compileDropdown(el, params) {
    const dropdownDef = this.createDropdownDef(el);

    // 编译下拉菜单结构
    const dropdownEl = this.compileDropdownStructure(dropdownDef, params);

    // 编译切换器
    const togglerEl = this.compileDropdownToggler(dropdownDef, params);

    // 编译菜单内容
    const menuEl = this.compileDropdownMenu(dropdownDef, params);

    // 组装下拉菜单
    append(dropdownEl, togglerEl);
    append(dropdownEl, menuEl);

    return dropdownEl;
}

createDropdownDef(el) {
    return {
        el: el,
        inserted: false,
        shouldInsert: true,
        parts: ["dropdown", "toggler", "menu"]
    };
}

compileDropdownStructure(dropdownDef, params) {
    const dropdownEl = createElement("div");
    dropdownEl.setAttribute("class", "dropdown");
    dropdownEl.setAttribute("t-ref", "dropdown");

    return dropdownEl;
}

compileDropdownToggler(dropdownDef, params) {
    const togglerEl = createElement("button");
    togglerEl.setAttribute("class", "btn btn-sm dropdown-toggle");
    togglerEl.setAttribute("type", "button");
    togglerEl.setAttribute("data-bs-toggle", "dropdown");
    togglerEl.setAttribute("aria-expanded", "false");

    // 添加切换器图标
    togglerEl.innerHTML = '<i class="fa fa-ellipsis-v"></i>';

    return togglerEl;
}

compileDropdownMenu(dropdownDef, params) {
    const menuEl = createElement("div");
    menuEl.setAttribute("class", "dropdown-menu");

    // 编译菜单项
    for (const child of dropdownDef.el.children) {
        const menuItem = this.compileDropdownMenuItem(child, params);
        if (menuItem) {
            append(menuEl, menuItem);
        }
    }

    return menuEl;
}

compileDropdownMenuItem(el, params) {
    const itemEl = createElement("a");
    itemEl.setAttribute("class", "dropdown-item");
    itemEl.setAttribute("href", "#");

    // 设置点击处理
    const type = el.getAttribute("type");
    const name = el.getAttribute("name");
    itemEl.setAttribute("t-on-click", `() => this.onMenuItemClick('${type}', '${name}', record)`);

    // 设置内容
    const string = el.getAttribute("string") || el.textContent;
    const icon = el.getAttribute("icon");

    if (icon) {
        itemEl.innerHTML = `<i class="${icon}"></i> ${string}`;
    } else {
        itemEl.textContent = string;
    }

    return itemEl;
}
```

**下拉菜单编译功能**:
- **结构创建**: 创建下拉菜单的基本结构
- **切换器**: 编译下拉菜单的切换按钮
- **菜单内容**: 编译下拉菜单的内容
- **菜单项**: 编译单个菜单项
- **事件绑定**: 绑定菜单项的点击事件

## 使用场景

### 1. 看板编译管理器

```javascript
// 看板编译管理器
class KanbanCompileManager {
    constructor() {
        this.setupManager();
    }

    setupManager() {
        // 设置编译配置
        this.compileConfig = {
            enableOptimization: true,
            enableCaching: true,
            enableMinification: false,
            enableSourceMap: false,
            enableImageOptimization: true,
            enableButtonOptimization: true,
            cacheTimeout: 300000, // 5分钟
            maxCacheSize: 100
        };

        // 设置编译器缓存
        this.compilerCache = new Map();

        // 设置模板缓存
        this.templateCache = new Map();

        // 设置编译器注册表
        this.compilerRegistry = new Map();

        // 设置编译统计
        this.compileStatistics = {
            totalCompiles: 0,
            cacheHits: 0,
            cacheMisses: 0,
            averageCompileTime: 0,
            templateCount: 0,
            imageCount: 0,
            buttonCount: 0
        };

        this.initializeCompileSystem();
    }

    // 初始化编译系统
    initializeCompileSystem() {
        // 创建增强的看板编译器
        this.createEnhancedKanbanCompiler();

        // 注册默认编译器
        this.registerDefaultCompilers();

        // 设置缓存系统
        this.setupCacheSystem();

        // 设置优化系统
        this.setupOptimizationSystem();
    }

    // 创建增强的看板编译器
    createEnhancedKanbanCompiler() {
        const originalCompiler = KanbanCompiler;

        this.EnhancedKanbanCompiler = class extends originalCompiler {
            constructor(manager) {
                super();
                this.manager = manager;
            }

            compile(node, params = {}) {
                const startTime = performance.now();

                // 检查缓存
                const cacheKey = this.generateCacheKey(node, params);
                if (this.manager.compileConfig.enableCaching && this.manager.templateCache.has(cacheKey)) {
                    this.manager.compileStatistics.cacheHits++;
                    return this.manager.templateCache.get(cacheKey);
                }

                this.manager.compileStatistics.cacheMisses++;

                // 执行增强编译
                const result = this.enhancedCompile(node, params);

                // 缓存结果
                if (this.manager.compileConfig.enableCaching) {
                    this.manager.templateCache.set(cacheKey, result);
                }

                const endTime = performance.now();
                this.updateCompileStatistics(endTime - startTime);

                return result;
            }

            enhancedCompile(node, params) {
                // 预处理节点
                this.preprocessNode(node);

                // 执行原始编译
                const result = super.compile(node, params);

                // 后处理结果
                this.postprocessResult(result);

                // 优化结果
                if (this.manager.compileConfig.enableOptimization) {
                    return this.optimizeResult(result);
                }

                return result;
            }

            preprocessNode(node) {
                // 标准化节点属性
                this.normalizeNodeAttributes(node);

                // 验证节点结构
                this.validateNodeStructure(node);

                // 应用预处理规则
                this.applyPreprocessingRules(node);
            }

            normalizeNodeAttributes(node) {
                // 标准化按钮类型
                if (node.tagName === 'button') {
                    const type = node.getAttribute('type');
                    if (!type) {
                        node.setAttribute('type', 'object');
                    }
                }

                // 标准化图片属性
                if (node.tagName === 'img') {
                    if (!node.hasAttribute('alt')) {
                        node.setAttribute('alt', '');
                    }
                }
            }

            validateNodeStructure(node) {
                // 验证按钮节点
                if (node.tagName === 'button') {
                    const type = node.getAttribute('type');
                    if (type && !SPECIAL_TYPES.includes(type) && !ACTION_TYPES.includes(type)) {
                        console.warn(`Unknown button type: ${type}`);
                    }
                }

                // 验证图片节点
                if (node.tagName === 'img') {
                    const src = node.getAttribute('src');
                    const tAttSrc = node.getAttribute('t-att-src');
                    if (!src && !tAttSrc) {
                        console.warn('Image node missing src attribute');
                    }
                }
            }

            applyPreprocessingRules(node) {
                // 应用自定义预处理规则
                const rules = this.manager.getPreprocessingRules(node.tagName);
                for (const rule of rules) {
                    rule.apply(node);
                }
            }

            postprocessResult(result) {
                // 添加元数据
                this.addMetadata(result);

                // 验证结果
                this.validateResult(result);

                // 应用后处理规则
                this.applyPostprocessingRules(result);
            }

            addMetadata(result) {
                if (result && typeof result === 'object' && result.tagName) {
                    result._metadata = {
                        compileTime: Date.now(),
                        compiler: 'EnhancedKanbanCompiler',
                        version: '1.0.0'
                    };
                }
            }

            validateResult(result) {
                if (!result) {
                    throw new Error('Compile result cannot be null');
                }
            }

            optimizeResult(result) {
                // 优化图片
                if (this.manager.compileConfig.enableImageOptimization) {
                    result = this.optimizeImages(result);
                }

                // 优化按钮
                if (this.manager.compileConfig.enableButtonOptimization) {
                    result = this.optimizeButtons(result);
                }

                // 移除多余的属性
                result = this.removeRedundantAttributes(result);

                return result;
            }

            optimizeImages(result) {
                // 查找图片元素
                const images = result.querySelectorAll ? result.querySelectorAll('img') : [];

                for (const img of images) {
                    // 添加懒加载
                    if (!img.hasAttribute('loading')) {
                        img.setAttribute('loading', 'lazy');
                    }

                    // 优化图片尺寸
                    this.optimizeImageSize(img);
                }

                return result;
            }

            optimizeImageSize(img) {
                const src = img.getAttribute('src');
                if (src && src.includes('/web/image/')) {
                    // 添加尺寸参数
                    if (!src.includes('width=') && !src.includes('height=')) {
                        const optimizedSrc = src + (src.includes('?') ? '&' : '?') + 'width=150&height=150';
                        img.setAttribute('src', optimizedSrc);
                    }
                }
            }

            optimizeButtons(result) {
                // 查找按钮元素
                const buttons = result.querySelectorAll ? result.querySelectorAll('button') : [];

                for (const button of buttons) {
                    // 优化按钮类
                    this.optimizeButtonClasses(button);

                    // 添加防抖
                    this.addButtonDebounce(button);
                }

                return result;
            }

            optimizeButtonClasses(button) {
                const classes = button.getAttribute('class') || '';
                const classArray = classes.split(' ').filter(Boolean);

                // 移除重复的类
                const uniqueClasses = [...new Set(classArray)];
                button.setAttribute('class', uniqueClasses.join(' '));
            }

            addButtonDebounce(button) {
                const onClick = button.getAttribute('t-on-click');
                if (onClick && !onClick.includes('debounce')) {
                    const debouncedClick = `debounce(${onClick}, 300)`;
                    button.setAttribute('t-on-click', debouncedClick);
                }
            }

            removeRedundantAttributes(result) {
                // 移除空属性
                if (result.attributes) {
                    for (const attr of result.attributes) {
                        if (!attr.value || attr.value.trim() === '') {
                            result.removeAttribute(attr.name);
                        }
                    }
                }

                return result;
            }

            // 重写编译方法以添加统计
            compileImage(el, params) {
                this.manager.compileStatistics.imageCount++;
                return super.compileImage(el, params);
            }

            compileButton(el, params) {
                this.manager.compileStatistics.buttonCount++;
                return super.compileButton(el, params);
            }

            generateCacheKey(node, params) {
                const nodeString = node.outerHTML || node.toString();
                const paramsString = JSON.stringify(params);
                return this.simpleHash(nodeString + paramsString);
            }

            simpleHash(str) {
                let hash = 0;
                for (let i = 0; i < str.length; i++) {
                    const char = str.charCodeAt(i);
                    hash = ((hash << 5) - hash) + char;
                    hash = hash & hash; // 转换为32位整数
                }
                return hash.toString(36);
            }

            updateCompileStatistics(compileTime) {
                this.manager.compileStatistics.totalCompiles++;
                this.manager.compileStatistics.templateCount++;

                // 更新平均编译时间
                const totalCompiles = this.manager.compileStatistics.totalCompiles;
                this.manager.compileStatistics.averageCompileTime =
                    (this.manager.compileStatistics.averageCompileTime * (totalCompiles - 1) + compileTime) / totalCompiles;
            }
        };
    }

    // 注册默认编译器
    registerDefaultCompilers() {
        // 注册图片编译器
        this.compilerRegistry.set('img', {
            compile: (node, params) => this.compileImage(node, params),
            validate: (node) => this.validateImage(node)
        });

        // 注册按钮编译器
        this.compilerRegistry.set('button', {
            compile: (node, params) => this.compileButton(node, params),
            validate: (node) => this.validateButton(node)
        });

        // 注册下拉菜单编译器
        this.compilerRegistry.set('dropdown', {
            compile: (node, params) => this.compileDropdown(node, params),
            validate: (node) => this.validateDropdown(node)
        });
    }

    // 设置缓存系统
    setupCacheSystem() {
        // 定期清理缓存
        setInterval(() => {
            this.cleanupCache();
        }, this.compileConfig.cacheTimeout);
    }

    // 清理缓存
    cleanupCache() {
        if (this.templateCache.size > this.compileConfig.maxCacheSize) {
            // 移除最旧的缓存项
            const firstKey = this.templateCache.keys().next().value;
            this.templateCache.delete(firstKey);
        }
    }

    // 设置优化系统
    setupOptimizationSystem() {
        this.optimizationRules = {
            removeEmptyAttributes: true,
            optimizeImages: this.compileConfig.enableImageOptimization,
            optimizeButtons: this.compileConfig.enableButtonOptimization,
            minifyOutput: this.compileConfig.enableMinification
        };
    }

    // 获取预处理规则
    getPreprocessingRules(tagName) {
        const rules = {
            button: [
                { apply: (node) => this.normalizeButtonType(node) }
            ],
            img: [
                { apply: (node) => this.normalizeImageAttributes(node) }
            ]
        };

        return rules[tagName] || [];
    }

    // 标准化按钮类型
    normalizeButtonType(node) {
        if (!node.hasAttribute('type')) {
            node.setAttribute('type', 'object');
        }
    }

    // 标准化图片属性
    normalizeImageAttributes(node) {
        if (!node.hasAttribute('alt')) {
            node.setAttribute('alt', '');
        }
    }

    // 创建编译器实例
    createCompiler() {
        return new this.EnhancedKanbanCompiler(this);
    }

    // 获取编译统计
    getCompileStatistics() {
        return {
            ...this.compileStatistics,
            cacheSize: this.templateCache.size,
            compilerRegistrySize: this.compilerRegistry.size,
            cacheHitRate: this.compileStatistics.totalCompiles > 0
                ? (this.compileStatistics.cacheHits / this.compileStatistics.totalCompiles * 100).toFixed(2) + '%'
                : '0%'
        };
    }

    // 销毁管理器
    destroy() {
        // 清理缓存
        this.compilerCache.clear();
        this.templateCache.clear();
        this.compilerRegistry.clear();

        // 重置统计
        this.compileStatistics = {
            totalCompiles: 0,
            cacheHits: 0,
            cacheMisses: 0,
            averageCompileTime: 0,
            templateCount: 0,
            imageCount: 0,
            buttonCount: 0
        };
    }
}

// 使用示例
const compileManager = new KanbanCompileManager();

// 创建编译器
const compiler = compileManager.createCompiler();

// 编译看板模板
const template = compiler.compile(xmlNode, { isSubView: false });

// 获取统计信息
const stats = compileManager.getCompileStatistics();
console.log('Kanban compile statistics:', stats);
```
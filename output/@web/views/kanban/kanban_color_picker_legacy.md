# KanbanColorPickerLegacy - 看板颜色选择器遗留支持

## 概述

`kanban_color_picker_legacy.js` 是 Odoo Web 客户端看板视图的遗留颜色选择器支持模块，负责提供向后兼容的颜色选择功能。该模块包含82行代码，是一个补丁模块，专门用于在v18版本后保持对旧版颜色选择器的兼容性，具备架构解析补丁、编译器补丁、颜色字段处理、模板编译等特性，是看板视图系统中向后兼容性支持的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/kanban/kanban_color_picker_legacy.js`
- **行数**: 82
- **模块**: `@web/views/kanban/kanban_color_picker_legacy`

## 依赖关系

```javascript
// 核心依赖
'@web/core/colorlist/colorlist'                    // 颜色列表组件
'@web/core/utils/patch'                            // 补丁工具
'@web/core/utils/xml'                              // XML工具
'@web/views/kanban/kanban_arch_parser'             // 看板架构解析器
'@web/views/kanban/kanban_compiler'                // 看板编译器
'@web/views/kanban/kanban_record'                  // 看板记录
```

## 核心功能

### 1. 架构解析器补丁

```javascript
patch(KanbanArchParser.prototype, {
    parse(xmlDoc, models, modelName) {
        const archInfo = super.parse(xmlDoc, models, modelName);

        // Color and color picker (first node found is taken for each)
        const legacyCardDoc = archInfo.templateDocs[LEGACY_KANBAN_BOX_ATTRIBUTE];
        if (legacyCardDoc) {
            const cardColorEl = legacyCardDoc.querySelector("[color]");
            const cardColorField = cardColorEl && cardColorEl.getAttribute("color");

            const colorEl = xmlDoc.querySelector("templates .oe_kanban_colorpicker[data-field]");
            const colorField = (colorEl && colorEl.getAttribute("data-field")) || "color";

            archInfo.cardColorField = archInfo.cardColorField || cardColorField;
            archInfo.colorField = colorField;
        }

        return archInfo;
    },
});
```

**补丁功能**:
- **遗留支持**: 支持旧版本的颜色选择器语法
- **字段提取**: 提取颜色字段和卡片颜色字段
- **默认值**: 提供默认的颜色字段名称
- **向后兼容**: 确保旧版本代码的正常运行

### 2. 编译器补丁

```javascript
patch(KanbanCompiler.prototype, {
    setup() {
        super.setup();
        this.compilers.push({ selector: ".oe_kanban_colorpicker", fn: this.compileColorPicker });
    },

    compileColorPicker() {
        return createElement("t", {
            "t-call": "web.KanbanColorPicker",
        });
    },
});
```

**编译器补丁功能**:
- **选择器注册**: 注册旧版颜色选择器的CSS选择器
- **模板编译**: 编译颜色选择器为新版本模板
- **元素创建**: 创建模板调用元素
- **无缝迁移**: 提供无缝的版本迁移

### 3. 记录补丁

```javascript
patch(KanbanRecord.prototype, {
    getColorIndex(record) {
        const colorField = this.archInfo.colorField;
        if (colorField && record.data[colorField] !== undefined) {
            return getColorIndex(record.data[colorField]);
        }
        return 0;
    },

    getCardColor(record) {
        const cardColorField = this.archInfo.cardColorField;
        if (cardColorField && record.data[cardColorField] !== undefined) {
            return record.data[cardColorField];
        }
        return null;
    },
});
```

**记录补丁功能**:
- **颜色索引**: 获取记录的颜色索引
- **卡片颜色**: 获取记录的卡片颜色
- **字段访问**: 安全访问颜色相关字段
- **默认处理**: 提供合理的默认值

## 使用场景

### 1. 遗留颜色选择器管理器

```javascript
// 遗留颜色选择器管理器
class LegacyColorPickerManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置遗留支持配置
        this.legacyConfig = {
            enableLegacySupport: true,
            enableColorMigration: true,
            enableFieldMapping: true,
            enableTemplateConversion: true,
            deprecationWarnings: true,
            migrationLogging: true
        };
        
        // 设置字段映射
        this.fieldMapping = new Map();
        
        // 设置模板映射
        this.templateMapping = new Map();
        
        // 设置迁移统计
        this.migrationStatistics = {
            totalMigrations: 0,
            successfulMigrations: 0,
            failedMigrations: 0,
            warningsIssued: 0
        };
        
        this.initializeLegacySystem();
    }
    
    // 初始化遗留系统
    initializeLegacySystem() {
        // 创建增强的遗留支持
        this.createEnhancedLegacySupport();
        
        // 设置字段映射系统
        this.setupFieldMappingSystem();
        
        // 设置模板转换系统
        this.setupTemplateConversionSystem();
        
        // 设置迁移监控
        this.setupMigrationMonitoring();
    }
    
    // 创建增强的遗留支持
    createEnhancedLegacySupport() {
        // 增强架构解析器补丁
        this.enhanceArchParserPatch();
        
        // 增强编译器补丁
        this.enhanceCompilerPatch();
        
        // 增强记录补丁
        this.enhanceRecordPatch();
    }
    
    // 增强架构解析器补丁
    enhanceArchParserPatch() {
        const originalPatch = KanbanArchParser.prototype.parse;
        
        patch(KanbanArchParser.prototype, {
            parse(xmlDoc, models, modelName) {
                const startTime = performance.now();
                
                try {
                    // 执行原始解析
                    const archInfo = originalPatch.call(this, xmlDoc, models, modelName);
                    
                    // 增强遗留支持
                    this.enhanceLegacySupport(archInfo, xmlDoc);
                    
                    // 记录迁移统计
                    this.recordMigrationSuccess();
                    
                    return archInfo;
                } catch (error) {
                    this.recordMigrationFailure(error);
                    throw error;
                } finally {
                    const endTime = performance.now();
                    this.recordMigrationTime(endTime - startTime);
                }
            },
            
            enhanceLegacySupport(archInfo, xmlDoc) {
                // 检测遗留语法
                this.detectLegacySyntax(xmlDoc);
                
                // 映射遗留字段
                this.mapLegacyFields(archInfo);
                
                // 转换遗留模板
                this.convertLegacyTemplates(archInfo);
                
                // 发出弃用警告
                this.issueDeprecationWarnings(archInfo);
            },
            
            detectLegacySyntax(xmlDoc) {
                // 检测旧版颜色选择器
                const legacyColorPickers = xmlDoc.querySelectorAll('.oe_kanban_colorpicker');
                if (legacyColorPickers.length > 0) {
                    this.logLegacyUsage('colorpicker', legacyColorPickers.length);
                }
                
                // 检测旧版颜色属性
                const legacyColorAttrs = xmlDoc.querySelectorAll('[color]');
                if (legacyColorAttrs.length > 0) {
                    this.logLegacyUsage('color_attribute', legacyColorAttrs.length);
                }
            },
            
            mapLegacyFields(archInfo) {
                // 映射颜色字段
                if (archInfo.colorField) {
                    this.fieldMapping.set('color', archInfo.colorField);
                }
                
                // 映射卡片颜色字段
                if (archInfo.cardColorField) {
                    this.fieldMapping.set('cardColor', archInfo.cardColorField);
                }
            },
            
            convertLegacyTemplates(archInfo) {
                // 转换遗留模板
                for (const [templateName, templateDoc] of Object.entries(archInfo.templateDocs)) {
                    if (templateName === LEGACY_KANBAN_BOX_ATTRIBUTE) {
                        this.convertLegacyCardTemplate(templateDoc);
                    }
                }
            },
            
            convertLegacyCardTemplate(templateDoc) {
                // 转换颜色选择器
                const colorPickers = templateDoc.querySelectorAll('.oe_kanban_colorpicker');
                for (const picker of colorPickers) {
                    this.convertColorPicker(picker);
                }
                
                // 转换颜色属性
                const colorElements = templateDoc.querySelectorAll('[color]');
                for (const element of colorElements) {
                    this.convertColorAttribute(element);
                }
            },
            
            convertColorPicker(picker) {
                // 创建新版本的颜色选择器
                const newPicker = document.createElement('t');
                newPicker.setAttribute('t-call', 'web.KanbanColorPicker');
                
                // 复制数据字段
                const dataField = picker.getAttribute('data-field');
                if (dataField) {
                    newPicker.setAttribute('colorField', dataField);
                }
                
                // 替换旧元素
                picker.parentNode.replaceChild(newPicker, picker);
                
                this.templateMapping.set('colorpicker', 'converted');
            },
            
            convertColorAttribute(element) {
                // 转换颜色属性为新格式
                const colorValue = element.getAttribute('color');
                element.removeAttribute('color');
                element.setAttribute('t-att-data-color', colorValue);
                
                this.templateMapping.set('color_attribute', 'converted');
            },
            
            issueDeprecationWarnings(archInfo) {
                if (this.legacyConfig.deprecationWarnings) {
                    if (archInfo.colorField) {
                        console.warn('Deprecated: Legacy color field usage detected. Please migrate to new color picker API.');
                        this.migrationStatistics.warningsIssued++;
                    }
                    
                    if (archInfo.cardColorField) {
                        console.warn('Deprecated: Legacy card color field usage detected. Please migrate to new color API.');
                        this.migrationStatistics.warningsIssued++;
                    }
                }
            },
            
            logLegacyUsage(type, count) {
                if (this.legacyConfig.migrationLogging) {
                    console.log(`Legacy ${type} usage detected: ${count} instances`);
                }
            },
            
            recordMigrationSuccess() {
                this.migrationStatistics.totalMigrations++;
                this.migrationStatistics.successfulMigrations++;
            },
            
            recordMigrationFailure(error) {
                this.migrationStatistics.totalMigrations++;
                this.migrationStatistics.failedMigrations++;
                console.error('Migration failed:', error);
            },
            
            recordMigrationTime(time) {
                if (this.legacyConfig.migrationLogging) {
                    console.log(`Migration completed in ${time.toFixed(2)}ms`);
                }
            }
        });
    }
    
    // 增强编译器补丁
    enhanceCompilerPatch() {
        const originalSetup = KanbanCompiler.prototype.setup;
        const originalCompileColorPicker = KanbanCompiler.prototype.compileColorPicker;
        
        patch(KanbanCompiler.prototype, {
            setup() {
                originalSetup.call(this);
                
                // 添加增强的编译器
                this.compilers.push({
                    selector: ".oe_kanban_colorpicker[data-field]",
                    fn: this.compileEnhancedColorPicker
                });
            },
            
            compileColorPicker() {
                // 记录编译统计
                this.recordColorPickerCompilation();
                
                // 执行原始编译
                return originalCompileColorPicker.call(this);
            },
            
            compileEnhancedColorPicker(el) {
                const dataField = el.getAttribute('data-field');
                const colorField = dataField || 'color';
                
                // 创建增强的颜色选择器
                const pickerEl = createElement("t", {
                    "t-call": "web.KanbanColorPicker",
                    "colorField": colorField,
                    "t-if": `record.data.${colorField} !== undefined`
                });
                
                // 添加颜色索引属性
                pickerEl.setAttribute("t-att-data-color-index", `getColorIndex(record.data.${colorField})`);
                
                return pickerEl;
            },
            
            recordColorPickerCompilation() {
                if (this.legacyConfig.migrationLogging) {
                    console.log('Legacy color picker compiled');
                }
            }
        });
    }
    
    // 增强记录补丁
    enhanceRecordPatch() {
        const originalGetColorIndex = KanbanRecord.prototype.getColorIndex;
        const originalGetCardColor = KanbanRecord.prototype.getCardColor;
        
        patch(KanbanRecord.prototype, {
            getColorIndex(record) {
                try {
                    // 尝试新版本方法
                    if (originalGetColorIndex) {
                        return originalGetColorIndex.call(this, record);
                    }
                    
                    // 回退到遗留方法
                    return this.getLegacyColorIndex(record);
                } catch (error) {
                    console.warn('Failed to get color index:', error);
                    return 0;
                }
            },
            
            getCardColor(record) {
                try {
                    // 尝试新版本方法
                    if (originalGetCardColor) {
                        return originalGetCardColor.call(this, record);
                    }
                    
                    // 回退到遗留方法
                    return this.getLegacyCardColor(record);
                } catch (error) {
                    console.warn('Failed to get card color:', error);
                    return null;
                }
            },
            
            getLegacyColorIndex(record) {
                // 遗留颜色索引获取逻辑
                const colorField = this.archInfo.colorField || 'color';
                const colorValue = record.data[colorField];
                
                if (colorValue !== undefined && colorValue !== null) {
                    return getColorIndex(colorValue);
                }
                
                return 0;
            },
            
            getLegacyCardColor(record) {
                // 遗留卡片颜色获取逻辑
                const cardColorField = this.archInfo.cardColorField || 'card_color';
                const cardColor = record.data[cardColorField];
                
                if (cardColor !== undefined && cardColor !== null) {
                    return cardColor;
                }
                
                return null;
            }
        });
    }
    
    // 设置字段映射系统
    setupFieldMappingSystem() {
        // 默认字段映射
        this.fieldMapping.set('color', 'color');
        this.fieldMapping.set('cardColor', 'card_color');
        this.fieldMapping.set('priority', 'priority');
    }
    
    // 设置模板转换系统
    setupTemplateConversionSystem() {
        // 模板转换规则
        this.templateMapping.set('colorpicker', 'web.KanbanColorPicker');
        this.templateMapping.set('color_attribute', 't-att-data-color');
    }
    
    // 设置迁移监控
    setupMigrationMonitoring() {
        // 定期报告迁移统计
        if (this.legacyConfig.migrationLogging) {
            setInterval(() => {
                this.reportMigrationStatistics();
            }, 60000); // 每分钟报告一次
        }
    }
    
    // 报告迁移统计
    reportMigrationStatistics() {
        console.log('Migration Statistics:', {
            total: this.migrationStatistics.totalMigrations,
            successful: this.migrationStatistics.successfulMigrations,
            failed: this.migrationStatistics.failedMigrations,
            warnings: this.migrationStatistics.warningsIssued,
            successRate: this.migrationStatistics.totalMigrations > 0 
                ? (this.migrationStatistics.successfulMigrations / this.migrationStatistics.totalMigrations * 100).toFixed(2) + '%'
                : '0%'
        });
    }
    
    // 获取字段映射
    getFieldMapping(legacyField) {
        return this.fieldMapping.get(legacyField) || legacyField;
    }
    
    // 获取模板映射
    getTemplateMapping(legacyTemplate) {
        return this.templateMapping.get(legacyTemplate) || legacyTemplate;
    }
    
    // 检查是否启用遗留支持
    isLegacySupportEnabled() {
        return this.legacyConfig.enableLegacySupport;
    }
    
    // 获取迁移统计
    getMigrationStatistics() {
        return {
            ...this.migrationStatistics,
            fieldMappingCount: this.fieldMapping.size,
            templateMappingCount: this.templateMapping.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理映射
        this.fieldMapping.clear();
        this.templateMapping.clear();
        
        // 重置统计
        this.migrationStatistics = {
            totalMigrations: 0,
            successfulMigrations: 0,
            failedMigrations: 0,
            warningsIssued: 0
        };
    }
}

// 使用示例
const legacyManager = new LegacyColorPickerManager();

// 检查遗留支持状态
if (legacyManager.isLegacySupportEnabled()) {
    console.log('Legacy color picker support is enabled');
}

// 获取迁移统计
const stats = legacyManager.getMigrationStatistics();
console.log('Legacy migration statistics:', stats);
```

## 技术特点

### 1. 向后兼容性
- **遗留支持**: 完整支持旧版本的颜色选择器
- **无缝迁移**: 提供无缝的版本迁移路径
- **语法转换**: 自动转换旧版本语法
- **字段映射**: 智能映射遗留字段

### 2. 补丁机制
- **非侵入式**: 使用补丁机制避免修改原始代码
- **模块化**: 模块化的补丁设计
- **可维护**: 易于维护和更新
- **可移除**: 可以轻松移除遗留支持

### 3. 错误处理
- **优雅降级**: 在错误情况下优雅降级
- **默认值**: 提供合理的默认值
- **错误日志**: 记录错误和警告信息
- **异常捕获**: 捕获和处理异常

### 4. 性能考虑
- **最小开销**: 最小的性能开销
- **按需加载**: 按需加载遗留支持
- **缓存机制**: 缓存转换结果
- **优化查询**: 优化DOM查询操作

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- **功能扩展**: 扩展现有功能
- **非侵入式**: 不修改原始代码
- **可撤销**: 可以撤销补丁

### 2. 适配器模式 (Adapter Pattern)
- **接口适配**: 适配新旧接口
- **兼容性**: 提供兼容性支持
- **转换**: 转换数据格式

### 3. 策略模式 (Strategy Pattern)
- **迁移策略**: 不同的迁移策略
- **转换策略**: 不同的转换策略
- **处理策略**: 不同的错误处理策略

### 4. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 装饰现有功能
- **增强**: 增强原有行为
- **透明**: 对用户透明

## 注意事项

1. **临时性**: 这是临时的兼容性支持，应该计划移除
2. **性能影响**: 可能对性能有轻微影响
3. **维护成本**: 增加了维护成本
4. **迁移计划**: 应该制定明确的迁移计划

## 扩展建议

1. **迁移工具**: 开发自动迁移工具
2. **文档支持**: 提供详细的迁移文档
3. **警告系统**: 增强弃用警告系统
4. **测试覆盖**: 增加测试覆盖率
5. **监控指标**: 添加迁移监控指标

该遗留颜色选择器支持模块为Odoo Web客户端提供了重要的向后兼容性功能，通过补丁机制和智能转换确保了旧版本代码的正常运行，同时为新版本的迁移提供了平滑的过渡路径。

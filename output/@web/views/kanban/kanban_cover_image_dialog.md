# KanbanCoverImageDialog - 看板封面图片对话框

## 概述

`kanban_cover_image_dialog.js` 是 Odoo Web 客户端看板视图的封面图片对话框组件，负责管理看板记录的封面图片。该模块包含84行代码，是一个OWL组件，专门用于上传、选择和设置看板记录的封面图片，具备文件上传、图片预览、附件管理、自动打开等特性，是看板视图系统中图片管理的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/kanban/kanban_cover_image_dialog.js`
- **行数**: 84
- **模块**: `@web/views/kanban/kanban_cover_image_dialog`

## 依赖关系

```javascript
// 核心依赖
'@web/core/dialog/dialog'       // 对话框组件
'@web/core/file_input/file_input' // 文件输入组件
'@web/core/utils/hooks'         // 工具钩子
'@odoo/owl'                     // OWL框架
```

## 核心变量

### 1. 对话框ID生成器

```javascript
let nextDialogId = 1;
```

**ID生成器功能**:
- **唯一标识**: 为每个对话框生成唯一ID
- **自增机制**: 使用自增数字确保唯一性
- **DOM标识**: 用于DOM元素的标识
- **避免冲突**: 避免多个对话框的ID冲突

## 主要组件定义

### 1. KanbanCoverImageDialog - 看板封面图片对话框

```javascript
class KanbanCoverImageDialog extends Component {
    static template = "web.KanbanCoverImageDialog";
    static components = { Dialog, FileInput };
    static props = ["*"];

    setup() {
        this.id = `o_cover_image_upload_${nextDialogId++}`;
        this.orm = useService("orm");
        this.http = useService("http");
        const { record, fieldName } = this.props;
        const attachment = (record && record.data[fieldName]) || [];
        this.state = useState({
            selectFile: false,
            selectedAttachmentId: attachment[0],
        });
        onWillStart(async () => {
            this.attachments = await this.orm.searchRead(
                "ir.attachment",
                [
                    ["res_model", "=", record.resModel],
                    ["res_id", "=", record.resId],
                    ["mimetype", "ilike", "image"],
                ],
                ["id"]
            );
            this.state.selectFile = this.props.autoOpen && this.attachments.length;
        });
    }
}
```

**组件特性**:
- **专用模板**: 使用KanbanCoverImageDialog专用模板
- **子组件**: 集成Dialog和FileInput组件
- **灵活属性**: 支持任意属性传递
- **服务集成**: 集成ORM和HTTP服务

## 核心功能

### 1. 初始化设置

```javascript
setup() {
    this.id = `o_cover_image_upload_${nextDialogId++}`;
    this.orm = useService("orm");
    this.http = useService("http");
    const { record, fieldName } = this.props;
    const attachment = (record && record.data[fieldName]) || [];
    this.state = useState({
        selectFile: false,
        selectedAttachmentId: attachment[0],
    });
}
```

**初始化功能**:
- **ID生成**: 生成唯一的对话框ID
- **服务注入**: 注入ORM和HTTP服务
- **属性解析**: 解析记录和字段名属性
- **状态初始化**: 初始化组件状态

### 2. 附件加载

```javascript
onWillStart(async () => {
    this.attachments = await this.orm.searchRead(
        "ir.attachment",
        [
            ["res_model", "=", record.resModel],
            ["res_id", "=", record.resId],
            ["mimetype", "ilike", "image"],
        ],
        ["id"]
    );
    this.state.selectFile = this.props.autoOpen && this.attachments.length;
});
```

**附件加载功能**:
- **附件查询**: 查询记录相关的图片附件
- **过滤条件**: 按模型、记录ID和图片类型过滤
- **自动打开**: 根据配置自动打开文件选择
- **数据准备**: 为组件准备附件数据

### 3. 封面图片检查

```javascript
get hasCoverImage() {
    return Boolean(this.props.record.data[this.props.fieldName]);
}
```

**检查功能**:
- **状态检查**: 检查是否已有封面图片
- **字段访问**: 访问记录的指定字段
- **布尔转换**: 转换为布尔值
- **条件渲染**: 用于条件渲染逻辑

### 4. 文件上传处理

```javascript
onUpload([attachment]) {
    if (!attachment) {
        return;
    }
    this.attachments.push(attachment);
    this.state.selectedAttachmentId = attachment.id;
}
```

**上传处理功能**:
- **附件验证**: 验证上传的附件
- **列表更新**: 更新附件列表
- **选择设置**: 自动选择新上传的附件
- **状态同步**: 同步组件状态

### 5. 图片选择和确认

```javascript
onSelectAttachment(attachmentId) {
    this.state.selectedAttachmentId = attachmentId;
}

async onConfirm() {
    const { record, fieldName } = this.props;
    await record.update({
        [fieldName]: this.state.selectedAttachmentId,
    });
    this.props.close();
}

onRemove() {
    this.state.selectedAttachmentId = null;
    this.onConfirm();
}
```

**选择确认功能**:
- **附件选择**: 选择指定的附件作为封面
- **记录更新**: 更新记录的封面字段
- **对话框关闭**: 确认后关闭对话框
- **移除功能**: 移除当前封面图片

## 使用场景

### 1. 看板封面图片管理器

```javascript
// 看板封面图片管理器
class KanbanCoverImageManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置图片管理配置
        this.imageConfig = {
            enableUpload: true,
            enablePreview: true,
            enableCrop: true,
            enableResize: true,
            maxFileSize: 5 * 1024 * 1024, // 5MB
            allowedFormats: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            defaultQuality: 0.8,
            thumbnailSize: { width: 150, height: 150 }
        };
        
        // 设置附件缓存
        this.attachmentCache = new Map();
        
        // 设置图片处理器
        this.imageProcessor = null;
        
        // 设置上传统计
        this.uploadStatistics = {
            totalUploads: 0,
            successfulUploads: 0,
            failedUploads: 0,
            totalSize: 0,
            averageSize: 0
        };
        
        this.initializeImageSystem();
    }
    
    // 初始化图片系统
    initializeImageSystem() {
        // 创建增强的封面图片对话框
        this.createEnhancedCoverImageDialog();
        
        // 设置图片处理系统
        this.setupImageProcessingSystem();
        
        // 设置缓存系统
        this.setupCacheSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
    }
    
    // 创建增强的封面图片对话框
    createEnhancedCoverImageDialog() {
        const originalDialog = KanbanCoverImageDialog;
        
        this.EnhancedKanbanCoverImageDialog = class extends originalDialog {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加图片处理功能
                this.addImageProcessingFeatures();
                
                // 添加预览功能
                this.addPreviewFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = useState({
                    isUploading: false,
                    uploadProgress: 0,
                    previewUrl: null,
                    cropData: null,
                    resizeData: null,
                    validationError: null
                });
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的文件上传
                this.enhancedOnUpload = async (files) => {
                    if (!files || files.length === 0) {
                        return;
                    }
                    
                    const file = files[0];
                    
                    try {
                        // 验证文件
                        this.validateFile(file);
                        
                        // 开始上传
                        this.startUpload();
                        
                        // 处理图片
                        const processedFile = await this.processImage(file);
                        
                        // 上传文件
                        const attachment = await this.uploadFile(processedFile);
                        
                        // 完成上传
                        this.completeUpload(attachment);
                        
                    } catch (error) {
                        this.handleUploadError(error);
                    }
                };
                
                // 验证文件
                this.validateFile = (file) => {
                    // 检查文件大小
                    if (file.size > this.imageConfig.maxFileSize) {
                        throw new Error(`File size exceeds ${this.imageConfig.maxFileSize / 1024 / 1024}MB limit`);
                    }
                    
                    // 检查文件格式
                    const extension = file.name.split('.').pop().toLowerCase();
                    if (!this.imageConfig.allowedFormats.includes(extension)) {
                        throw new Error(`File format ${extension} is not allowed`);
                    }
                    
                    // 检查MIME类型
                    if (!file.type.startsWith('image/')) {
                        throw new Error('File must be an image');
                    }
                };
                
                // 处理图片
                this.processImage = async (file) => {
                    if (!this.imageConfig.enableResize && !this.imageConfig.enableCrop) {
                        return file;
                    }
                    
                    return new Promise((resolve, reject) => {
                        const img = new Image();
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        
                        img.onload = () => {
                            try {
                                // 计算新尺寸
                                const { width, height } = this.calculateNewSize(img.width, img.height);
                                
                                // 设置画布尺寸
                                canvas.width = width;
                                canvas.height = height;
                                
                                // 绘制图片
                                ctx.drawImage(img, 0, 0, width, height);
                                
                                // 转换为Blob
                                canvas.toBlob((blob) => {
                                    if (blob) {
                                        const processedFile = new File([blob], file.name, {
                                            type: file.type,
                                            lastModified: Date.now()
                                        });
                                        resolve(processedFile);
                                    } else {
                                        reject(new Error('Failed to process image'));
                                    }
                                }, file.type, this.imageConfig.defaultQuality);
                                
                            } catch (error) {
                                reject(error);
                            }
                        };
                        
                        img.onerror = () => {
                            reject(new Error('Failed to load image'));
                        };
                        
                        img.src = URL.createObjectURL(file);
                    });
                };
                
                // 计算新尺寸
                this.calculateNewSize = (originalWidth, originalHeight) => {
                    const maxWidth = 800;
                    const maxHeight = 600;
                    
                    let { width, height } = { width: originalWidth, height: originalHeight };
                    
                    // 按比例缩放
                    if (width > maxWidth) {
                        height = (height * maxWidth) / width;
                        width = maxWidth;
                    }
                    
                    if (height > maxHeight) {
                        width = (width * maxHeight) / height;
                        height = maxHeight;
                    }
                    
                    return { width: Math.round(width), height: Math.round(height) };
                };
                
                // 上传文件
                this.uploadFile = async (file) => {
                    const formData = new FormData();
                    formData.append('file', file);
                    formData.append('res_model', this.props.record.resModel);
                    formData.append('res_id', this.props.record.resId);
                    
                    const response = await this.http.post('/web/binary/upload_attachment', formData, {
                        onUploadProgress: (progressEvent) => {
                            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                            this.enhancedState.uploadProgress = progress;
                        }
                    });
                    
                    return response.data;
                };
                
                // 开始上传
                this.startUpload = () => {
                    this.enhancedState.isUploading = true;
                    this.enhancedState.uploadProgress = 0;
                    this.enhancedState.validationError = null;
                    
                    this.uploadStatistics.totalUploads++;
                };
                
                // 完成上传
                this.completeUpload = (attachment) => {
                    this.enhancedState.isUploading = false;
                    this.enhancedState.uploadProgress = 100;
                    
                    // 添加到附件列表
                    this.attachments.push(attachment);
                    this.state.selectedAttachmentId = attachment.id;
                    
                    // 更新统计
                    this.uploadStatistics.successfulUploads++;
                    this.updateUploadStatistics(attachment.file_size);
                    
                    // 缓存附件
                    this.cacheAttachment(attachment);
                };
                
                // 处理上传错误
                this.handleUploadError = (error) => {
                    this.enhancedState.isUploading = false;
                    this.enhancedState.validationError = error.message;
                    
                    this.uploadStatistics.failedUploads++;
                    
                    console.error('Upload error:', error);
                };
                
                // 生成预览URL
                this.generatePreviewUrl = (attachment) => {
                    if (!attachment) return null;
                    
                    return `/web/image/${attachment.id}?unique=${Date.now()}`;
                };
                
                // 增强的附件选择
                this.enhancedOnSelectAttachment = (attachmentId) => {
                    this.state.selectedAttachmentId = attachmentId;
                    
                    // 生成预览
                    const attachment = this.attachments.find(a => a.id === attachmentId);
                    if (attachment) {
                        this.enhancedState.previewUrl = this.generatePreviewUrl(attachment);
                    }
                };
                
                // 增强的确认操作
                this.enhancedOnConfirm = async () => {
                    try {
                        const { record, fieldName } = this.props;
                        
                        // 更新记录
                        await record.update({
                            [fieldName]: this.state.selectedAttachmentId,
                        });
                        
                        // 触发成功事件
                        this.triggerSuccessEvent();
                        
                        // 关闭对话框
                        this.props.close();
                        
                    } catch (error) {
                        this.handleConfirmError(error);
                    }
                };
                
                // 触发成功事件
                this.triggerSuccessEvent = () => {
                    if (this.props.onSuccess) {
                        this.props.onSuccess({
                            attachmentId: this.state.selectedAttachmentId,
                            record: this.props.record
                        });
                    }
                };
                
                // 处理确认错误
                this.handleConfirmError = (error) => {
                    this.enhancedState.validationError = error.message;
                    console.error('Confirm error:', error);
                };
                
                // 缓存附件
                this.cacheAttachment = (attachment) => {
                    this.attachmentCache.set(attachment.id, attachment);
                };
                
                // 更新上传统计
                this.updateUploadStatistics = (fileSize) => {
                    this.uploadStatistics.totalSize += fileSize;
                    this.uploadStatistics.averageSize = 
                        this.uploadStatistics.totalSize / this.uploadStatistics.successfulUploads;
                };
            }
            
            addImageProcessingFeatures() {
                // 添加图片裁剪功能
                this.addCropFeature = () => {
                    // 实现图片裁剪功能
                };
                
                // 添加图片旋转功能
                this.addRotateFeature = () => {
                    // 实现图片旋转功能
                };
            }
            
            addPreviewFeatures() {
                // 添加图片预览功能
                this.addImagePreview = () => {
                    // 实现图片预览功能
                };
                
                // 添加缩略图生成
                this.addThumbnailGeneration = () => {
                    // 实现缩略图生成功能
                };
            }
            
            addValidationFeatures() {
                // 添加实时验证
                this.addRealTimeValidation = () => {
                    // 实现实时验证功能
                };
            }
            
            // 重写原始方法
            onUpload(files) {
                this.enhancedOnUpload(files);
            }
            
            onSelectAttachment(attachmentId) {
                this.enhancedOnSelectAttachment(attachmentId);
            }
            
            onConfirm() {
                this.enhancedOnConfirm();
            }
            
            // 获取上传统计
            getUploadStatistics() {
                return {
                    ...this.uploadStatistics,
                    successRate: this.uploadStatistics.totalUploads > 0 
                        ? (this.uploadStatistics.successfulUploads / this.uploadStatistics.totalUploads * 100).toFixed(2) + '%'
                        : '0%'
                };
            }
        };
    }
    
    // 设置图片处理系统
    setupImageProcessingSystem() {
        this.imageProcessor = {
            resize: (file, maxWidth, maxHeight) => {
                // 实现图片缩放功能
            },
            crop: (file, cropData) => {
                // 实现图片裁剪功能
            },
            rotate: (file, angle) => {
                // 实现图片旋转功能
            },
            compress: (file, quality) => {
                // 实现图片压缩功能
            }
        };
    }
    
    // 设置缓存系统
    setupCacheSystem() {
        // 定期清理缓存
        setInterval(() => {
            this.cleanupCache();
        }, 300000); // 5分钟清理一次
    }
    
    // 清理缓存
    cleanupCache() {
        if (this.attachmentCache.size > 100) {
            // 移除最旧的缓存项
            const firstKey = this.attachmentCache.keys().next().value;
            this.attachmentCache.delete(firstKey);
        }
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationRules = {
            fileSize: (file) => file.size <= this.imageConfig.maxFileSize,
            fileFormat: (file) => {
                const extension = file.name.split('.').pop().toLowerCase();
                return this.imageConfig.allowedFormats.includes(extension);
            },
            mimeType: (file) => file.type.startsWith('image/')
        };
    }
    
    // 创建封面图片对话框
    createCoverImageDialog(props) {
        return new this.EnhancedKanbanCoverImageDialog(props);
    }
    
    // 获取上传统计
    getUploadStatistics() {
        return {
            ...this.uploadStatistics,
            cacheSize: this.attachmentCache.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理缓存
        this.attachmentCache.clear();
        
        // 重置统计
        this.uploadStatistics = {
            totalUploads: 0,
            successfulUploads: 0,
            failedUploads: 0,
            totalSize: 0,
            averageSize: 0
        };
    }
}

// 使用示例
const imageManager = new KanbanCoverImageManager();

// 创建封面图片对话框
const dialog = imageManager.createCoverImageDialog({
    record: record,
    fieldName: 'cover_image',
    autoOpen: true,
    close: () => console.log('Dialog closed')
});

// 获取统计信息
const stats = imageManager.getUploadStatistics();
console.log('Upload statistics:', stats);
```

## 技术特点

### 1. 文件管理
- **文件上传**: 支持图片文件的上传
- **格式验证**: 验证文件格式和大小
- **附件管理**: 管理记录相关的图片附件
- **自动选择**: 自动选择新上传的图片

### 2. 用户界面
- **对话框**: 使用对话框展示功能
- **文件输入**: 集成文件输入组件
- **预览功能**: 提供图片预览功能
- **状态反馈**: 提供操作状态反馈

### 3. 数据处理
- **附件查询**: 查询相关的图片附件
- **记录更新**: 更新记录的封面字段
- **状态管理**: 管理组件的各种状态
- **错误处理**: 处理上传和操作错误

### 4. 性能优化
- **懒加载**: 按需加载附件数据
- **缓存机制**: 缓存附件信息
- **异步操作**: 异步处理文件操作
- **内存管理**: 及时清理资源

## 设计模式

### 1. 组件模式 (Component Pattern)
- **封装性**: 封装封面图片管理功能
- **可复用**: 高度可复用的组件
- **接口标准**: 标准化的组件接口

### 2. 状态模式 (State Pattern)
- **状态管理**: 管理不同的组件状态
- **状态切换**: 在不同状态间切换
- **行为变化**: 根据状态改变行为

### 3. 策略模式 (Strategy Pattern)
- **上传策略**: 不同的文件上传策略
- **验证策略**: 不同的文件验证策略
- **处理策略**: 不同的图片处理策略

### 4. 观察者模式 (Observer Pattern)
- **事件监听**: 监听文件上传事件
- **状态变化**: 响应状态变化
- **回调通知**: 通过回调通知操作结果

## 注意事项

1. **文件大小**: 控制上传文件的大小限制
2. **格式支持**: 确保支持的图片格式
3. **错误处理**: 完善的错误处理和用户提示
4. **性能考虑**: 避免大文件影响性能

## 扩展建议

1. **图片编辑**: 添加基本的图片编辑功能
2. **批量上传**: 支持批量上传多张图片
3. **拖拽上传**: 支持拖拽上传文件
4. **云存储**: 支持云存储服务
5. **图片压缩**: 自动压缩大尺寸图片

该看板封面图片对话框为Odoo Web客户端提供了完整的图片管理功能，通过直观的界面和丰富的功能确保了用户能够轻松管理看板记录的封面图片。

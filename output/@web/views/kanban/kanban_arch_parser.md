# KanbanArchParser - 看板架构解析器

## 概述

`kanban_arch_parser.js` 是 Odoo Web 客户端看板视图的架构解析器，负责解析看板视图的XML架构定义。该模块包含211行代码，提供了一个专门的解析器类，用于将XML架构转换为看板视图可以使用的配置对象，具备字段解析、模板处理、按钮解析、分组配置等特性，是看板视图系统中架构处理的核心工具。

## 文件信息
- **路径**: `/web/static/src/views/kanban/kanban_arch_parser.js`
- **行数**: 211
- **模块**: `@web/views/kanban/kanban_arch_parser`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/strings'           // 字符串工具
'@web/core/utils/xml'               // XML工具
'@web/search/utils/order_by'        // 排序工具
'@web/views/fields/field'           // 字段组件
'@web/views/utils'                  // 视图工具
'@web/views/widgets/widget'         // 小部件组件
```

## 核心常量

### 1. 模板属性常量

```javascript
const LEGACY_KANBAN_BOX_ATTRIBUTE = "kanban-box";      // 旧版看板盒子属性
const LEGACY_KANBAN_MENU_ATTRIBUTE = "kanban-menu";    // 旧版看板菜单属性
const KANBAN_CARD_ATTRIBUTE = "card";                  // 看板卡片属性
const KANBAN_MENU_ATTRIBUTE = "menu";                  // 看板菜单属性
```

**常量说明**:
- **向后兼容**: 支持旧版本的模板属性
- **新版本**: 使用新的card和menu属性
- **模板识别**: 用于识别不同类型的模板
- **API迁移**: 支持从v18 API的迁移

## 主要类定义

### 1. KanbanArchParser - 看板架构解析器

```javascript
class KanbanArchParser {
    parse(xmlDoc, models, modelName) {
        const fields = models[modelName].fields;
        const className = xmlDoc.getAttribute("class") || null;
        const canOpenRecords = exprToBoolean(xmlDoc.getAttribute("can_open"), true);
        let defaultOrder = stringToOrderBy(xmlDoc.getAttribute("default_order") || null);
        const defaultGroupBy = xmlDoc.getAttribute("default_group_by");
        const limit = xmlDoc.getAttribute("limit");
        const countLimit = xmlDoc.getAttribute("count_limit");
        const recordsDraggable = exprToBoolean(xmlDoc.getAttribute("records_draggable"), true);
        const groupsDraggable = exprToBoolean(xmlDoc.getAttribute("groups_draggable"), true);
        const activeActions = getActiveActions(xmlDoc);
        activeActions.archiveGroup = exprToBoolean(xmlDoc.getAttribute("archivable"), true);
        activeActions.createGroup = exprToBoolean(xmlDoc.getAttribute("group_create"), true);

        // 解析模板和字段
        const { templates, fieldNodes, widgetNodes } = this.parseTemplatesAndFields(xmlDoc, models, modelName);

        return {
            className,
            canOpenRecords,
            defaultOrder,
            defaultGroupBy,
            limit: limit ? parseInt(limit) : undefined,
            countLimit: countLimit ? parseInt(countLimit) : undefined,
            recordsDraggable,
            groupsDraggable,
            activeActions,
            templates,
            fieldNodes,
            widgetNodes,
        };
    }
}
```

**解析器特性**:
- **XML解析**: 解析看板视图的XML架构
- **属性提取**: 提取各种配置属性
- **模板处理**: 处理看板模板定义
- **字段解析**: 解析字段和小部件节点

## 核心功能

### 1. 架构属性解析

```javascript
const className = xmlDoc.getAttribute("class") || null;
const canOpenRecords = exprToBoolean(xmlDoc.getAttribute("can_open"), true);
let defaultOrder = stringToOrderBy(xmlDoc.getAttribute("default_order") || null);
const defaultGroupBy = xmlDoc.getAttribute("default_group_by");
const limit = xmlDoc.getAttribute("limit");
const countLimit = xmlDoc.getAttribute("count_limit");
const recordsDraggable = exprToBoolean(xmlDoc.getAttribute("records_draggable"), true);
const groupsDraggable = exprToBoolean(xmlDoc.getAttribute("groups_draggable"), true);
```

**属性解析功能**:
- **样式类**: 解析自定义CSS类
- **记录打开**: 解析是否可以打开记录
- **默认排序**: 解析默认排序规则
- **默认分组**: 解析默认分组字段
- **限制设置**: 解析记录和计数限制
- **拖拽配置**: 解析拖拽相关配置

### 2. 活动动作解析

```javascript
const activeActions = getActiveActions(xmlDoc);
activeActions.archiveGroup = exprToBoolean(xmlDoc.getAttribute("archivable"), true);
activeActions.createGroup = exprToBoolean(xmlDoc.getAttribute("group_create"), true);
```

**动作解析功能**:
- **基础动作**: 获取基础的活动动作
- **归档分组**: 解析分组归档功能
- **创建分组**: 解析分组创建功能
- **权限控制**: 控制不同动作的权限

### 3. 模板和字段解析

```javascript
parseTemplatesAndFields(xmlDoc, models, modelName) {
    const templates = {};
    const fieldNodes = {};
    const widgetNodes = {};
    let widgetNextId = 0;
    const fieldNextIds = {};

    // 查找模板节点
    const templateNodes = xmlDoc.querySelectorAll('templates > t');
    for (const templateNode of templateNodes) {
        const templateName = this.getTemplateName(templateNode);
        if (templateName) {
            templates[templateName] = templateNode;
            this.parseTemplateFields(templateNode, fieldNodes, widgetNodes, models, modelName, fieldNextIds, widgetNextId);
        }
    }

    return { templates, fieldNodes, widgetNodes };
}

getTemplateName(templateNode) {
    // 检查新版本属性
    if (templateNode.hasAttribute(KANBAN_CARD_ATTRIBUTE)) {
        return 'card';
    }
    if (templateNode.hasAttribute(KANBAN_MENU_ATTRIBUTE)) {
        return 'menu';
    }

    // 检查旧版本属性
    const tName = templateNode.getAttribute('t-name');
    if (tName === LEGACY_KANBAN_BOX_ATTRIBUTE) {
        return 'card';
    }
    if (tName === LEGACY_KANBAN_MENU_ATTRIBUTE) {
        return 'menu';
    }

    return null;
}
```

**模板解析功能**:
- **模板识别**: 识别不同类型的模板
- **向后兼容**: 支持旧版本模板属性
- **字段提取**: 从模板中提取字段信息
- **小部件处理**: 处理模板中的小部件

### 4. 字段节点解析

```javascript
parseTemplateFields(templateNode, fieldNodes, widgetNodes, models, modelName, fieldNextIds, widgetNextId) {
    visitXML(templateNode, (node) => {
        if (node.tagName === "field") {
            const fieldInfo = Field.parseFieldNode(node, models, modelName, "kanban");
            if (!(fieldInfo.name in fieldNextIds)) {
                fieldNextIds[fieldInfo.name] = 0;
            }
            const fieldId = `${fieldInfo.name}_${fieldNextIds[fieldInfo.name]++}`;
            fieldNodes[fieldId] = fieldInfo;
            node.setAttribute("field_id", fieldId);

            // 处理特殊字段
            this.processSpecialFields(fieldInfo, node);

            return false;
        } else if (node.tagName === "widget") {
            const widgetInfo = Widget.parseWidgetNode(node);
            const widgetId = `widget_${++widgetNextId}`;
            widgetNodes[widgetId] = widgetInfo;
            node.setAttribute("widget_id", widgetId);
        } else if (node.tagName === "button") {
            this.processButton(node);
        }
    });
}

processSpecialFields(fieldInfo, node) {
    // 处理颜色字段
    if (fieldInfo.name === 'color' || node.hasAttribute('color')) {
        fieldInfo.isColorField = true;
    }

    // 处理句柄字段
    if (fieldInfo.widget === 'handle' || node.hasAttribute('handle')) {
        fieldInfo.isHandleField = true;
    }

    // 处理优先级字段
    if (fieldInfo.widget === 'priority' || node.hasAttribute('priority')) {
        fieldInfo.isPriorityField = true;
    }
}
```

**字段解析功能**:
- **字段信息**: 解析字段的详细信息
- **ID生成**: 为字段生成唯一ID
- **特殊字段**: 处理颜色、句柄、优先级等特殊字段
- **小部件解析**: 解析小部件节点
- **按钮处理**: 处理按钮节点

## 使用场景

### 1. 看板架构管理器

```javascript
// 看板架构管理器
class KanbanArchManager {
    constructor() {
        this.setupManager();
    }

    setupManager() {
        // 设置解析配置
        this.parseConfig = {
            enableTemplateValidation: true,
            enableFieldCaching: true,
            enableLegacySupport: true,
            enableOptimization: true,
            cacheTimeout: 300000, // 5分钟
            maxCacheSize: 50
        };

        // 设置解析缓存
        this.parseCache = new Map();

        // 设置模板注册表
        this.templateRegistry = new Map();

        // 设置字段注册表
        this.fieldRegistry = new Map();

        // 设置解析统计
        this.parseStatistics = {
            totalParses: 0,
            cacheHits: 0,
            cacheMisses: 0,
            averageParseTime: 0,
            templateCount: 0,
            fieldCount: 0
        };

        this.initializeArchSystem();
    }

    // 初始化架构系统
    initializeArchSystem() {
        // 创建增强的架构解析器
        this.createEnhancedArchParser();

        // 设置模板系统
        this.setupTemplateSystem();

        // 设置验证系统
        this.setupValidationSystem();

        // 设置缓存系统
        this.setupCacheSystem();
    }

    // 创建增强的架构解析器
    createEnhancedArchParser() {
        const originalParser = KanbanArchParser;

        this.EnhancedKanbanArchParser = class extends originalParser {
            constructor(manager) {
                super();
                this.manager = manager;
            }

            parse(xmlDoc, models, modelName) {
                const startTime = performance.now();

                // 检查缓存
                const cacheKey = this.generateCacheKey(xmlDoc, modelName);
                if (this.manager.parseConfig.enableFieldCaching && this.manager.parseCache.has(cacheKey)) {
                    this.manager.parseStatistics.cacheHits++;
                    return this.manager.parseCache.get(cacheKey);
                }

                this.manager.parseStatistics.cacheMisses++;

                // 执行增强解析
                const result = this.enhancedParse(xmlDoc, models, modelName);

                // 缓存结果
                if (this.manager.parseConfig.enableFieldCaching) {
                    this.manager.parseCache.set(cacheKey, result);
                }

                const endTime = performance.now();
                this.updateParseStatistics(endTime - startTime, result);

                return result;
            }

            enhancedParse(xmlDoc, models, modelName) {
                // 预处理XML文档
                this.preprocessXmlDoc(xmlDoc);

                // 执行原始解析
                const result = super.parse(xmlDoc, models, modelName);

                // 后处理结果
                this.postprocessResult(result, models, modelName);

                // 验证结果
                if (this.manager.parseConfig.enableTemplateValidation) {
                    this.validateResult(result);
                }

                // 优化结果
                if (this.manager.parseConfig.enableOptimization) {
                    this.optimizeResult(result);
                }

                return result;
            }

            preprocessXmlDoc(xmlDoc) {
                // 标准化属性
                this.normalizeAttributes(xmlDoc);

                // 处理旧版本兼容性
                if (this.manager.parseConfig.enableLegacySupport) {
                    this.processLegacyCompatibility(xmlDoc);
                }

                // 验证XML结构
                this.validateXmlStructure(xmlDoc);
            }

            normalizeAttributes(xmlDoc) {
                // 标准化布尔属性
                const booleanAttrs = ['can_open', 'records_draggable', 'groups_draggable', 'archivable', 'group_create'];
                for (const attr of booleanAttrs) {
                    const element = xmlDoc.querySelector(`[${attr}]`);
                    if (element) {
                        const value = element.getAttribute(attr);
                        element.setAttribute(attr, exprToBoolean(value).toString());
                    }
                }

                // 标准化数值属性
                const numericAttrs = ['limit', 'count_limit'];
                for (const attr of numericAttrs) {
                    const element = xmlDoc.querySelector(`[${attr}]`);
                    if (element) {
                        const value = parseInt(element.getAttribute(attr)) || 0;
                        element.setAttribute(attr, value.toString());
                    }
                }
            }

            processLegacyCompatibility(xmlDoc) {
                // 转换旧版本模板属性
                const legacyBoxTemplates = xmlDoc.querySelectorAll(`t[t-name="${LEGACY_KANBAN_BOX_ATTRIBUTE}"]`);
                for (const template of legacyBoxTemplates) {
                    template.setAttribute(KANBAN_CARD_ATTRIBUTE, '');
                    template.removeAttribute('t-name');
                }

                const legacyMenuTemplates = xmlDoc.querySelectorAll(`t[t-name="${LEGACY_KANBAN_MENU_ATTRIBUTE}"]`);
                for (const template of legacyMenuTemplates) {
                    template.setAttribute(KANBAN_MENU_ATTRIBUTE, '');
                    template.removeAttribute('t-name');
                }
            }

            validateXmlStructure(xmlDoc) {
                // 验证必需的模板
                const cardTemplate = xmlDoc.querySelector(`t[${KANBAN_CARD_ATTRIBUTE}]`);
                if (!cardTemplate) {
                    console.warn('Kanban view missing card template');
                }

                // 验证字段引用
                this.validateFieldReferences(xmlDoc);
            }

            validateFieldReferences(xmlDoc) {
                const fieldNodes = xmlDoc.querySelectorAll('field');
                for (const fieldNode of fieldNodes) {
                    const fieldName = fieldNode.getAttribute('name');
                    if (!fieldName) {
                        console.warn('Field node missing name attribute');
                    }
                }
            }

            postprocessResult(result, models, modelName) {
                // 增强模板信息
                this.enhanceTemplates(result.templates);

                // 增强字段信息
                this.enhanceFieldNodes(result.fieldNodes, models, modelName);

                // 添加依赖关系
                this.addDependencies(result);

                // 添加元数据
                this.addMetadata(result, modelName);
            }

            enhanceTemplates(templates) {
                for (const [templateName, templateNode] of Object.entries(templates)) {
                    // 添加模板元数据
                    templateNode._metadata = {
                        name: templateName,
                        type: templateName === 'card' ? 'card' : 'menu',
                        fieldCount: templateNode.querySelectorAll('field').length,
                        widgetCount: templateNode.querySelectorAll('widget').length,
                        buttonCount: templateNode.querySelectorAll('button').length
                    };

                    // 注册模板
                    this.manager.templateRegistry.set(templateName, templateNode);
                }
            }

            enhanceFieldNodes(fieldNodes, models, modelName) {
                const model = models[modelName];

                for (const [fieldId, fieldInfo] of Object.entries(fieldNodes)) {
                    // 添加字段元数据
                    const field = model.fields[fieldInfo.name];
                    fieldInfo.metadata = {
                        label: field?.string || fieldInfo.name,
                        help: field?.help || '',
                        type: field?.type || fieldInfo.type,
                        required: field?.required || false,
                        readonly: field?.readonly || false
                    };

                    // 添加看板特定属性
                    fieldInfo.kanbanSpecific = {
                        isColorField: fieldInfo.isColorField || false,
                        isHandleField: fieldInfo.isHandleField || false,
                        isPriorityField: fieldInfo.isPriorityField || false
                    };

                    // 注册字段
                    this.manager.fieldRegistry.set(fieldId, fieldInfo);
                }
            }

            addDependencies(result) {
                // 分析字段依赖关系
                result.fieldDependencies = this.analyzeFieldDependencies(result.fieldNodes);

                // 分析模板依赖关系
                result.templateDependencies = this.analyzeTemplateDependencies(result.templates);
            }

            analyzeFieldDependencies(fieldNodes) {
                const dependencies = new Map();

                for (const [fieldId, fieldInfo] of Object.entries(fieldNodes)) {
                    const fieldDeps = [];

                    // 分析invisible依赖
                    if (fieldInfo.invisible && typeof fieldInfo.invisible === 'string') {
                        fieldDeps.push(...this.extractFieldNames(fieldInfo.invisible));
                    }

                    // 分析readonly依赖
                    if (fieldInfo.readonly && typeof fieldInfo.readonly === 'string') {
                        fieldDeps.push(...this.extractFieldNames(fieldInfo.readonly));
                    }

                    dependencies.set(fieldId, [...new Set(fieldDeps)]);
                }

                return dependencies;
            }

            analyzeTemplateDependencies(templates) {
                const dependencies = new Map();

                for (const [templateName, templateNode] of Object.entries(templates)) {
                    const templateDeps = [];

                    // 分析字段依赖
                    const fieldNodes = templateNode.querySelectorAll('field');
                    for (const fieldNode of fieldNodes) {
                        const fieldName = fieldNode.getAttribute('name');
                        if (fieldName) {
                            templateDeps.push(fieldName);
                        }
                    }

                    dependencies.set(templateName, [...new Set(templateDeps)]);
                }

                return dependencies;
            }

            extractFieldNames(expression) {
                // 从表达式中提取字段名
                const fieldNames = [];
                const regex = /\b([a-zA-Z_][a-zA-Z0-9_]*)\b/g;
                let match;

                while ((match = regex.exec(expression)) !== null) {
                    fieldNames.push(match[1]);
                }

                return fieldNames;
            }

            addMetadata(result, modelName) {
                result.metadata = {
                    modelName: modelName,
                    parseTime: Date.now(),
                    templateCount: Object.keys(result.templates).length,
                    fieldCount: Object.keys(result.fieldNodes).length,
                    widgetCount: Object.keys(result.widgetNodes).length,
                    hasCardTemplate: 'card' in result.templates,
                    hasMenuTemplate: 'menu' in result.templates
                };
            }

            validateResult(result) {
                // 验证模板
                this.validateTemplates(result.templates);

                // 验证字段节点
                this.validateFieldNodes(result.fieldNodes);

                // 验证配置
                this.validateConfiguration(result);
            }

            validateTemplates(templates) {
                if (!templates.card) {
                    throw new Error('Kanban view must have a card template');
                }

                // 验证模板结构
                for (const [templateName, templateNode] of Object.entries(templates)) {
                    if (!templateNode || !templateNode.tagName) {
                        throw new Error(`Invalid template: ${templateName}`);
                    }
                }
            }

            validateFieldNodes(fieldNodes) {
                for (const [fieldId, fieldInfo] of Object.entries(fieldNodes)) {
                    if (!fieldInfo.name) {
                        throw new Error(`Field ${fieldId} missing name`);
                    }

                    if (!fieldInfo.type) {
                        throw new Error(`Field ${fieldId} missing type`);
                    }
                }
            }

            validateConfiguration(result) {
                // 验证限制设置
                if (result.limit && result.limit < 1) {
                    throw new Error('Limit must be greater than 0');
                }

                if (result.countLimit && result.countLimit < 1) {
                    throw new Error('Count limit must be greater than 0');
                }
            }

            optimizeResult(result) {
                // 优化字段节点
                this.optimizeFieldNodes(result.fieldNodes);

                // 优化模板
                this.optimizeTemplates(result.templates);

                // 移除不必要的属性
                this.removeUnnecessaryAttributes(result);
            }

            optimizeFieldNodes(fieldNodes) {
                for (const fieldInfo of Object.values(fieldNodes)) {
                    // 移除默认值属性
                    this.removeDefaultAttributes(fieldInfo);
                }
            }

            optimizeTemplates(templates) {
                for (const templateNode of Object.values(templates)) {
                    // 优化模板结构
                    this.optimizeTemplateStructure(templateNode);
                }
            }

            removeDefaultAttributes(fieldInfo) {
                const defaultValues = {
                    invisible: false,
                    readonly: false,
                    required: false
                };

                for (const [attr, defaultValue] of Object.entries(defaultValues)) {
                    if (fieldInfo[attr] === defaultValue) {
                        delete fieldInfo[attr];
                    }
                }
            }

            optimizeTemplateStructure(templateNode) {
                // 移除空的文本节点
                const textNodes = templateNode.querySelectorAll('text()');
                for (const textNode of textNodes) {
                    if (!textNode.textContent.trim()) {
                        textNode.remove();
                    }
                }
            }

            removeUnnecessaryAttributes(result) {
                // 移除不必要的元数据
                if (result.metadata && result.metadata.parseTime) {
                    delete result.metadata.parseTime;
                }
            }

            generateCacheKey(xmlDoc, modelName) {
                const xmlString = new XMLSerializer().serializeToString(xmlDoc);
                const hash = this.simpleHash(xmlString + modelName);
                return `${modelName}_${hash}`;
            }

            simpleHash(str) {
                let hash = 0;
                for (let i = 0; i < str.length; i++) {
                    const char = str.charCodeAt(i);
                    hash = ((hash << 5) - hash) + char;
                    hash = hash & hash; // 转换为32位整数
                }
                return hash.toString(36);
            }

            updateParseStatistics(parseTime, result) {
                this.manager.parseStatistics.totalParses++;
                this.manager.parseStatistics.templateCount += Object.keys(result.templates).length;
                this.manager.parseStatistics.fieldCount += Object.keys(result.fieldNodes).length;

                // 更新平均解析时间
                const totalParses = this.manager.parseStatistics.totalParses;
                this.manager.parseStatistics.averageParseTime =
                    (this.manager.parseStatistics.averageParseTime * (totalParses - 1) + parseTime) / totalParses;
            }
        };
    }

    // 设置模板系统
    setupTemplateSystem() {
        this.templateConfig = {
            supportLegacy: this.parseConfig.enableLegacySupport,
            validateStructure: this.parseConfig.enableTemplateValidation,
            optimizeTemplates: this.parseConfig.enableOptimization
        };
    }

    // 设置验证系统
    setupValidationSystem() {
        this.validationRules = {
            template: ['card_required', 'structure_valid'],
            field: ['name_required', 'type_required'],
            configuration: ['limits_positive']
        };
    }

    // 设置缓存系统
    setupCacheSystem() {
        // 定期清理缓存
        setInterval(() => {
            this.cleanupCache();
        }, this.parseConfig.cacheTimeout);
    }

    // 清理缓存
    cleanupCache() {
        if (this.parseCache.size > this.parseConfig.maxCacheSize) {
            // 移除最旧的缓存项
            const firstKey = this.parseCache.keys().next().value;
            this.parseCache.delete(firstKey);
        }
    }

    // 创建解析器实例
    createParser() {
        return new this.EnhancedKanbanArchParser(this);
    }

    // 获取解析统计
    getParseStatistics() {
        return {
            ...this.parseStatistics,
            cacheSize: this.parseCache.size,
            templateRegistrySize: this.templateRegistry.size,
            fieldRegistrySize: this.fieldRegistry.size,
            cacheHitRate: this.parseStatistics.totalParses > 0
                ? (this.parseStatistics.cacheHits / this.parseStatistics.totalParses * 100).toFixed(2) + '%'
                : '0%'
        };
    }

    // 销毁管理器
    destroy() {
        // 清理缓存
        this.parseCache.clear();
        this.templateRegistry.clear();
        this.fieldRegistry.clear();

        // 重置统计
        this.parseStatistics = {
            totalParses: 0,
            cacheHits: 0,
            cacheMisses: 0,
            averageParseTime: 0,
            templateCount: 0,
            fieldCount: 0
        };
    }
}

// 使用示例
const archManager = new KanbanArchManager();

// 创建解析器
const parser = archManager.createParser();

// 解析架构
const result = parser.parse(xmlDoc, models, 'project.task');

// 获取统计信息
const stats = archManager.getParseStatistics();
console.log('Kanban parse statistics:', stats);
```
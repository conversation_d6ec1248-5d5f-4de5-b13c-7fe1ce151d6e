# ProgressBarHook - 进度条钩子

## 概述

`progress_bar_hook.js` 是 Odoo Web 客户端看板视图的进度条钩子模块，负责管理看板列中的进度条功能。该模块包含363行代码，是一个钩子函数和状态管理类，专门用于处理进度条的数据计算、状态管理、过滤功能等，具备分组数据处理、聚合计算、域过滤、状态响应等特性，是看板视图系统中进度条功能的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/kanban/progress_bar_hook.js`
- **行数**: 363
- **模块**: `@web/views/kanban/progress_bar_hook`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                             // OWL框架
'@web/core/domain'                      // 域处理
'@web/core/l10n/translation'            // 翻译服务
'@web/model/relational_model/utils'     // 关系模型工具
```

## 核心常量和工具函数

### 1. FALSE符号

```javascript
const FALSE = Symbol("False");
```

**符号功能**:
- **唯一标识**: 表示假值的唯一符号
- **类型区分**: 区分真正的false和空值
- **过滤逻辑**: 用于进度条的过滤逻辑
- **状态标识**: 标识特殊的状态值

### 2. 分组查找函数

```javascript
function _findGroup(groups, groupByField, value) {
    return groups.find((g) => g[groupByField.name] === value) || {};
}
```

**查找功能**:
- **分组匹配**: 在分组数据中查找特定值
- **字段匹配**: 根据分组字段进行匹配
- **默认返回**: 未找到时返回空对象
- **数据检索**: 高效的数据检索机制

### 3. 过滤域创建函数

```javascript
function _createFilterDomain(fieldName, bars, value) {
    let filterDomain = undefined;
    if (value === FALSE) {
        const keys = bars.filter((x) => x.value !== FALSE).map((x) => x.value);
        filterDomain = ["!", [fieldName, "in", keys]];
    } else {
        filterDomain = [[fieldName, "=", value]];
    }
    return filterDomain;
}
```

**域创建功能**:
- **条件构建**: 构建过滤条件域
- **否定逻辑**: 处理否定过滤逻辑
- **值匹配**: 创建精确值匹配条件
- **域格式**: 生成标准的域格式

### 4. 聚合值转换函数

```javascript
function _groupsToAggregateValues(groups, groupBy, fields) {
    const groupByFieldName = groupBy[0].split(":")[0];
    return groups.map((g) => {
        const groupInfo = extractInfoFromGroupData(g, groupBy, fields);
        return Object.assign(groupInfo.aggregates, { [groupByFieldName]: groupInfo.serverValue });
    });
}
```

**转换功能**:
- **数据提取**: 从分组数据中提取信息
- **聚合合并**: 合并聚合数据和分组值
- **字段解析**: 解析分组字段名称
- **数据映射**: 映射服务器数据到客户端格式

## 主要类定义

### 1. ProgressBarState - 进度条状态类

```javascript
class ProgressBarState {
    constructor(progressAttributes, model, aggregateFields, activeBars = {}) {
        this.progressAttributes = progressAttributes;
        this.model = model;
        this._groupsInfo = {};
        this.aggregateFields = aggregateFields;
        this.activeBars = reactive(activeBars);
        this.colors = this.progressAttributes.colors;
        this.field = this.progressAttributes.field;
        this.help = this.progressAttributes.help;
        this.sum_field = this.progressAttributes.sum_field;
    }
}
```

**状态类特性**:
- **属性存储**: 存储进度条的各种属性
- **模型引用**: 引用关联的数据模型
- **响应式状态**: 使用响应式的活动条状态
- **聚合字段**: 管理聚合字段信息

## 核心功能

### 1. 进度条数据计算

```javascript
getBars(groupValue) {
    const groupInfo = this._groupsInfo[groupValue] || {};
    const bars = [];
    
    // 计算每个状态的数据
    for (const [value, color] of Object.entries(this.colors)) {
        const count = this.getBarCount(groupInfo, value);
        const percentage = this.getBarPercentage(groupInfo, value, count);
        
        bars.push({
            value: value,
            count: count,
            percentage: percentage,
            color: color,
            label: this.getBarLabel(value),
            isActive: this.isBarActive(groupValue, value),
        });
    }
    
    // 添加"其他"条
    const otherCount = this.getOtherCount(groupInfo, bars);
    if (otherCount > 0) {
        bars.push({
            value: FALSE,
            count: otherCount,
            percentage: this.getOtherPercentage(groupInfo, otherCount),
            color: '#cccccc',
            label: _t('Other'),
            isActive: this.isBarActive(groupValue, FALSE),
        });
    }
    
    return bars;
}

getBarCount(groupInfo, value) {
    const aggregateKey = `${this.field}:${value}`;
    return groupInfo[aggregateKey] || 0;
}

getBarPercentage(groupInfo, value, count) {
    const total = this.getTotalCount(groupInfo);
    return total > 0 ? (count / total) * 100 : 0;
}

getTotalCount(groupInfo) {
    if (this.sum_field) {
        return groupInfo[this.sum_field] || 0;
    } else {
        return groupInfo.__count || 0;
    }
}

getOtherCount(groupInfo, bars) {
    const total = this.getTotalCount(groupInfo);
    const counted = bars.reduce((sum, bar) => sum + bar.count, 0);
    return Math.max(0, total - counted);
}

getOtherPercentage(groupInfo, otherCount) {
    const total = this.getTotalCount(groupInfo);
    return total > 0 ? (otherCount / total) * 100 : 0;
}

getBarLabel(value) {
    // 从字段选择项中获取标签
    const field = this.model.fields[this.field];
    if (field && field.selection) {
        const option = field.selection.find(opt => opt[0] === value);
        return option ? option[1] : value;
    }
    return value;
}
```

**数据计算功能**:
- **条数据**: 计算每个进度条的数据
- **百分比**: 计算各状态的百分比
- **总数统计**: 统计总记录数
- **其他项**: 处理未分类的记录
- **标签获取**: 获取状态的显示标签

### 2. 活动状态管理

```javascript
isBarActive(groupValue, barValue) {
    const groupBars = this.activeBars[groupValue];
    return groupBars && groupBars.has(barValue);
}

toggleBar(groupValue, barValue) {
    if (!this.activeBars[groupValue]) {
        this.activeBars[groupValue] = new Set();
    }
    
    const groupBars = this.activeBars[groupValue];
    if (groupBars.has(barValue)) {
        groupBars.delete(barValue);
    } else {
        groupBars.add(barValue);
    }
    
    // 触发过滤更新
    this.updateFilter();
}

clearActiveBars(groupValue) {
    if (this.activeBars[groupValue]) {
        this.activeBars[groupValue].clear();
        this.updateFilter();
    }
}

clearAllActiveBars() {
    for (const groupValue of Object.keys(this.activeBars)) {
        this.activeBars[groupValue].clear();
    }
    this.updateFilter();
}
```

**状态管理功能**:
- **活动检查**: 检查进度条是否处于活动状态
- **状态切换**: 切换进度条的活动状态
- **状态清除**: 清除特定或所有活动状态
- **过滤更新**: 状态变化时更新过滤条件

### 3. 过滤功能

```javascript
updateFilter() {
    const filterDomains = [];
    
    for (const [groupValue, activeBars] of Object.entries(this.activeBars)) {
        if (activeBars.size > 0) {
            const groupDomain = this.createGroupFilterDomain(groupValue, activeBars);
            if (groupDomain) {
                filterDomains.push(groupDomain);
            }
        }
    }
    
    // 应用过滤
    if (filterDomains.length > 0) {
        const combinedDomain = filterDomains.length === 1 
            ? filterDomains[0] 
            : ['|', ...filterDomains];
        this.applyFilter(combinedDomain);
    } else {
        this.clearFilter();
    }
}

createGroupFilterDomain(groupValue, activeBars) {
    const barDomains = [];
    
    for (const barValue of activeBars) {
        const barDomain = _createFilterDomain(this.field, this.getBars(groupValue), barValue);
        if (barDomain) {
            barDomains.push(barDomain);
        }
    }
    
    if (barDomains.length === 0) {
        return null;
    }
    
    // 添加分组条件
    const groupByField = this.model.groupByField;
    const groupDomain = [[groupByField, '=', groupValue]];
    
    // 合并条件
    if (barDomains.length === 1) {
        return ['&', groupDomain[0], barDomains[0]];
    } else {
        return ['&', groupDomain[0], ['|', ...barDomains]];
    }
}

applyFilter(domain) {
    // 应用过滤到模型
    this.model.setDomain(domain);
}

clearFilter() {
    // 清除过滤
    this.model.clearDomain();
}
```

**过滤功能**:
- **域构建**: 构建过滤域条件
- **条件合并**: 合并多个过滤条件
- **过滤应用**: 将过滤条件应用到模型
- **过滤清除**: 清除所有过滤条件

### 4. 数据更新

```javascript
updateGroupsInfo(groups) {
    this._groupsInfo = {};
    
    const aggregateValues = _groupsToAggregateValues(
        groups, 
        [this.model.groupByField], 
        this.model.fields
    );
    
    for (const aggregateValue of aggregateValues) {
        const groupValue = aggregateValue[this.model.groupByField];
        this._groupsInfo[groupValue] = aggregateValue;
    }
    
    // 触发重新渲染
    this.notifyUpdate();
}

notifyUpdate() {
    // 通知组件更新
    if (this.onUpdate) {
        this.onUpdate();
    }
}

setOnUpdate(callback) {
    this.onUpdate = callback;
}
```

**数据更新功能**:
- **信息更新**: 更新分组信息数据
- **数据转换**: 转换聚合数据格式
- **通知机制**: 通知组件数据已更新
- **回调设置**: 设置更新回调函数

## 钩子函数

### 1. useProgressBar钩子

```javascript
function useProgressBar(progressAttributes, model, aggregateFields) {
    const progressBarState = new ProgressBarState(
        progressAttributes, 
        model, 
        aggregateFields
    );
    
    // 监听模型变化
    useEffect(() => {
        const unsubscribe = model.addEventListener('update', () => {
            progressBarState.updateGroupsInfo(model.groups);
        });
        
        return unsubscribe;
    }, [model]);
    
    // 初始化数据
    onWillStart(() => {
        if (model.groups) {
            progressBarState.updateGroupsInfo(model.groups);
        }
    });
    
    return {
        getBars: (groupValue) => progressBarState.getBars(groupValue),
        toggleBar: (groupValue, barValue) => progressBarState.toggleBar(groupValue, barValue),
        clearActiveBars: (groupValue) => progressBarState.clearActiveBars(groupValue),
        clearAllActiveBars: () => progressBarState.clearAllActiveBars(),
        isBarActive: (groupValue, barValue) => progressBarState.isBarActive(groupValue, barValue),
        colors: progressBarState.colors,
        field: progressBarState.field,
        help: progressBarState.help,
    };
}
```

**钩子功能**:
- **状态创建**: 创建进度条状态实例
- **事件监听**: 监听模型数据变化
- **生命周期**: 处理组件生命周期
- **接口暴露**: 暴露进度条操作接口

## 使用场景

### 1. 看板进度条管理器

```javascript
// 看板进度条管理器
class KanbanProgressBarManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置进度条配置
        this.progressBarConfig = {
            enableProgressBar: true,
            enableFiltering: true,
            enableAnimation: true,
            enableTooltips: true,
            showPercentage: true,
            showCount: true,
            animationDuration: 300,
            colorScheme: 'default'
        };
        
        // 设置颜色方案
        this.colorSchemes = new Map();
        
        // 设置进度条实例
        this.progressBarInstances = new Map();
        
        // 设置统计信息
        this.statistics = {
            totalBars: 0,
            activeFilters: 0,
            clickCount: 0,
            averageResponseTime: 0
        };
        
        this.initializeProgressBarSystem();
    }
    
    // 初始化进度条系统
    initializeProgressBarSystem() {
        // 创建增强的进度条状态
        this.createEnhancedProgressBarState();
        
        // 设置颜色方案
        this.setupColorSchemes();
        
        // 设置动画系统
        this.setupAnimationSystem();
        
        // 设置性能监控
        this.setupPerformanceMonitoring();
    }
    
    // 创建增强的进度条状态
    createEnhancedProgressBarState() {
        const originalState = ProgressBarState;
        
        this.EnhancedProgressBarState = class extends originalState {
            constructor(progressAttributes, model, aggregateFields, activeBars = {}) {
                super(progressAttributes, model, aggregateFields, activeBars);
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加动画支持
                this.addAnimationSupport();
                
                // 添加性能监控
                this.addPerformanceMonitoring();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isAnimating: false,
                    lastUpdateTime: null,
                    clickHistory: [],
                    filterHistory: []
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的切换方法
                this.enhancedToggleBar = (groupValue, barValue) => {
                    const startTime = performance.now();
                    
                    // 记录点击
                    this.recordClick(groupValue, barValue);
                    
                    // 执行原始切换
                    this.toggleBar(groupValue, barValue);
                    
                    // 记录性能
                    const endTime = performance.now();
                    this.recordPerformance('toggle', endTime - startTime);
                };
                
                // 记录点击
                this.recordClick = (groupValue, barValue) => {
                    this.enhancedState.clickHistory.push({
                        groupValue,
                        barValue,
                        timestamp: Date.now()
                    });
                    
                    // 保持历史记录在合理范围内
                    if (this.enhancedState.clickHistory.length > 100) {
                        this.enhancedState.clickHistory.shift();
                    }
                    
                    this.statistics.clickCount++;
                };
                
                // 记录性能
                this.recordPerformance = (operation, duration) => {
                    // 更新平均响应时间
                    const totalOps = this.statistics.clickCount;
                    this.statistics.averageResponseTime = 
                        (this.statistics.averageResponseTime * (totalOps - 1) + duration) / totalOps;
                };
                
                // 智能颜色分配
                this.getSmartColor = (value, index) => {
                    const colorScheme = this.colorSchemes.get(this.progressBarConfig.colorScheme);
                    if (colorScheme && colorScheme.colors[index]) {
                        return colorScheme.colors[index];
                    }
                    
                    // 生成智能颜色
                    return this.generateSmartColor(value, index);
                };
                
                // 生成智能颜色
                this.generateSmartColor = (value, index) => {
                    // 基于值和索引生成颜色
                    const hue = (index * 137.508) % 360; // 黄金角度
                    const saturation = 70;
                    const lightness = 50;
                    return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
                };
                
                // 获取工具提示信息
                this.getTooltipInfo = (groupValue, barValue) => {
                    const bars = this.getBars(groupValue);
                    const bar = bars.find(b => b.value === barValue);
                    
                    if (bar) {
                        return {
                            label: bar.label,
                            count: bar.count,
                            percentage: bar.percentage.toFixed(1) + '%',
                            isActive: bar.isActive
                        };
                    }
                    
                    return null;
                };
                
                // 获取过滤摘要
                this.getFilterSummary = () => {
                    const activeGroups = Object.keys(this.activeBars).filter(
                        groupValue => this.activeBars[groupValue].size > 0
                    );
                    
                    const totalActiveBars = activeGroups.reduce(
                        (sum, groupValue) => sum + this.activeBars[groupValue].size, 0
                    );
                    
                    return {
                        activeGroups: activeGroups.length,
                        totalActiveBars: totalActiveBars,
                        hasActiveFilters: totalActiveBars > 0
                    };
                };
            }
            
            addAnimationSupport() {
                // 动画切换
                this.animatedToggleBar = async (groupValue, barValue) => {
                    if (this.enhancedState.isAnimating) {
                        return;
                    }
                    
                    this.enhancedState.isAnimating = true;
                    
                    try {
                        // 执行动画
                        await this.animateBarToggle(groupValue, barValue);
                        
                        // 执行切换
                        this.enhancedToggleBar(groupValue, barValue);
                        
                    } finally {
                        this.enhancedState.isAnimating = false;
                    }
                };
                
                // 执行条动画
                this.animateBarToggle = (groupValue, barValue) => {
                    return new Promise(resolve => {
                        // 实现动画逻辑
                        setTimeout(resolve, this.progressBarConfig.animationDuration);
                    });
                };
            }
            
            addPerformanceMonitoring() {
                // 性能监控
                this.monitorPerformance = () => {
                    // 实现性能监控逻辑
                };
            }
            
            // 重写原始方法
            toggleBar(groupValue, barValue) {
                if (this.progressBarConfig.enableAnimation) {
                    this.animatedToggleBar(groupValue, barValue);
                } else {
                    this.enhancedToggleBar(groupValue, barValue);
                }
            }
            
            // 获取统计信息
            getStatistics() {
                return {
                    ...this.statistics,
                    clickHistory: this.enhancedState.clickHistory.slice(-10), // 最近10次点击
                    filterSummary: this.getFilterSummary()
                };
            }
        };
    }
    
    // 设置颜色方案
    setupColorSchemes() {
        // 默认颜色方案
        this.colorSchemes.set('default', {
            name: 'Default',
            colors: ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']
        });
        
        // 彩虹颜色方案
        this.colorSchemes.set('rainbow', {
            name: 'Rainbow',
            colors: ['#ff0000', '#ff8000', '#ffff00', '#80ff00', '#00ff00', '#00ff80']
        });
        
        // 单色颜色方案
        this.colorSchemes.set('monochrome', {
            name: 'Monochrome',
            colors: ['#333333', '#666666', '#999999', '#cccccc', '#e6e6e6', '#f2f2f2']
        });
    }
    
    // 设置动画系统
    setupAnimationSystem() {
        this.animationConfig = {
            enableTransitions: this.progressBarConfig.enableAnimation,
            duration: this.progressBarConfig.animationDuration,
            easing: 'ease-in-out'
        };
    }
    
    // 设置性能监控
    setupPerformanceMonitoring() {
        this.performanceConfig = {
            enableMonitoring: true,
            sampleRate: 0.1,
            thresholds: {
                toggleTime: 100, // 100ms
                updateTime: 50   // 50ms
            }
        };
    }
    
    // 创建进度条实例
    createProgressBar(progressAttributes, model, aggregateFields) {
        const instance = new this.EnhancedProgressBarState(
            progressAttributes, 
            model, 
            aggregateFields
        );
        
        const instanceId = `progress_bar_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        this.progressBarInstances.set(instanceId, instance);
        this.statistics.totalBars++;
        
        return {
            id: instanceId,
            instance: instance,
            getBars: (groupValue) => instance.getBars(groupValue),
            toggleBar: (groupValue, barValue) => instance.toggleBar(groupValue, barValue),
            getTooltipInfo: (groupValue, barValue) => instance.getTooltipInfo(groupValue, barValue),
            getStatistics: () => instance.getStatistics()
        };
    }
    
    // 获取颜色方案
    getColorScheme(schemeName) {
        return this.colorSchemes.get(schemeName);
    }
    
    // 添加颜色方案
    addColorScheme(name, scheme) {
        this.colorSchemes.set(name, scheme);
    }
    
    // 获取全局统计
    getGlobalStatistics() {
        return {
            ...this.statistics,
            instanceCount: this.progressBarInstances.size,
            colorSchemeCount: this.colorSchemes.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理实例
        this.progressBarInstances.clear();
        
        // 清理颜色方案
        this.colorSchemes.clear();
        
        // 重置统计
        this.statistics = {
            totalBars: 0,
            activeFilters: 0,
            clickCount: 0,
            averageResponseTime: 0
        };
    }
}

// 使用示例
const progressBarManager = new KanbanProgressBarManager();

// 创建进度条实例
const progressBar = progressBarManager.createProgressBar(
    {
        field: 'stage_id',
        colors: { 'draft': '#cccccc', 'confirmed': '#1f77b4', 'done': '#2ca02c' },
        help: 'Task stages'
    },
    model,
    ['stage_id']
);

// 获取统计信息
const stats = progressBarManager.getGlobalStatistics();
console.log('Progress bar statistics:', stats);
```

## 技术特点

### 1. 数据处理
- **聚合计算**: 高效的数据聚合计算
- **分组处理**: 灵活的分组数据处理
- **状态管理**: 响应式的状态管理
- **缓存机制**: 智能的数据缓存

### 2. 过滤功能
- **域构建**: 动态的过滤域构建
- **条件合并**: 复杂的过滤条件合并
- **实时过滤**: 实时的数据过滤
- **状态同步**: 过滤状态的同步

### 3. 用户交互
- **点击响应**: 快速的点击响应
- **状态切换**: 直观的状态切换
- **视觉反馈**: 清晰的视觉反馈
- **工具提示**: 详细的工具提示

### 4. 性能优化
- **懒计算**: 按需计算进度条数据
- **缓存策略**: 智能的缓存策略
- **批量更新**: 批量的状态更新
- **内存管理**: 有效的内存管理

## 设计模式

### 1. 状态模式 (State Pattern)
- **状态管理**: 管理进度条的不同状态
- **状态切换**: 在不同状态间切换
- **行为变化**: 根据状态改变行为

### 2. 观察者模式 (Observer Pattern)
- **数据监听**: 监听模型数据变化
- **状态通知**: 通知状态变化
- **自动更新**: 自动更新进度条

### 3. 策略模式 (Strategy Pattern)
- **计算策略**: 不同的数据计算策略
- **过滤策略**: 不同的过滤策略
- **渲染策略**: 不同的渲染策略

### 4. 工厂模式 (Factory Pattern)
- **实例创建**: 创建进度条实例
- **配置生成**: 生成不同的配置
- **组件工厂**: 创建相关组件

## 注意事项

1. **性能考虑**: 避免频繁的数据计算和DOM更新
2. **内存管理**: 及时清理不需要的数据和监听器
3. **数据一致性**: 确保进度条数据与模型数据的一致性
4. **用户体验**: 提供流畅的交互体验

## 扩展建议

1. **动画效果**: 添加进度条的动画效果
2. **主题支持**: 支持不同的视觉主题
3. **自定义颜色**: 支持用户自定义颜色
4. **导出功能**: 支持进度条数据的导出
5. **移动优化**: 优化移动端的交互体验

该进度条钩子为Odoo Web客户端提供了强大的进度条功能，通过高效的数据处理和灵活的状态管理确保了进度条的准确性和响应性。

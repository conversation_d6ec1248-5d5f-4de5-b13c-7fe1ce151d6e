# KanbanDropdownMenuWrapper - 看板下拉菜单包装器

## 概述

`kanban_dropdown_menu_wrapper.js` 是 Odoo Web 客户端看板视图的下拉菜单包装器组件，负责包装和管理看板中的下拉菜单。该模块包含27行代码，是一个轻量级的OWL组件，专门用于提供下拉菜单的统一包装和控制，具备下拉菜单关闭控制、点击事件处理、插槽支持等特性，是看板视图系统中下拉菜单管理的辅助组件。

## 文件信息
- **路径**: `/web/static/src/views/kanban/kanban_dropdown_menu_wrapper.js`
- **行数**: 27
- **模块**: `@web/views/kanban/kanban_dropdown_menu_wrapper`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                             // OWL框架
'@web/core/dropdown/dropdown_hooks'     // 下拉菜单钩子
```

## 主要组件定义

### 1. KanbanDropdownMenuWrapper - 看板下拉菜单包装器

```javascript
class KanbanDropdownMenuWrapper extends Component {
    static template = "web.KanbanDropdownMenuWrapper";
    static props = {
        slots: Object,
    };

    setup() {
        this.dropdownControl = useDropdownCloser();
    }

    onClick(ev) {
        this.dropdownControl.closeAll();
    }
}
```

**组件特性**:
- **专用模板**: 使用KanbanDropdownMenuWrapper专用模板
- **插槽支持**: 支持插槽机制传递内容
- **钩子集成**: 集成下拉菜单关闭钩子
- **简洁设计**: 简洁而专注的组件设计

## 核心功能

### 1. 下拉菜单控制

```javascript
setup() {
    this.dropdownControl = useDropdownCloser();
}
```

**控制功能**:
- **钩子使用**: 使用下拉菜单关闭钩子
- **控制器获取**: 获取下拉菜单控制器
- **统一管理**: 统一管理下拉菜单的关闭
- **生命周期**: 在组件生命周期中管理

### 2. 点击事件处理

```javascript
onClick(ev) {
    this.dropdownControl.closeAll();
}
```

**事件处理功能**:
- **点击监听**: 监听组件的点击事件
- **全部关闭**: 关闭所有打开的下拉菜单
- **事件传播**: 处理事件传播
- **用户交互**: 响应用户交互

### 3. 插槽支持

```javascript
static props = {
    slots: Object,
};
```

**插槽功能**:
- **内容传递**: 通过插槽传递菜单内容
- **灵活布局**: 支持灵活的菜单布局
- **组件复用**: 提高组件的复用性
- **内容隔离**: 隔离不同的菜单内容

## 使用场景

### 1. 看板下拉菜单管理器

```javascript
// 看板下拉菜单管理器
class KanbanDropdownMenuManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置下拉菜单配置
        this.dropdownConfig = {
            enableAutoClose: true,
            enableClickOutside: true,
            enableEscapeKey: true,
            enableFocusManagement: true,
            closeDelay: 0,
            openDelay: 0,
            maxOpenMenus: 1
        };
        
        // 设置菜单注册表
        this.menuRegistry = new Map();
        
        // 设置活动菜单
        this.activeMenus = new Set();
        
        // 设置菜单统计
        this.menuStatistics = {
            totalMenus: 0,
            openCount: 0,
            closeCount: 0,
            averageOpenTime: 0
        };
        
        this.initializeDropdownSystem();
    }
    
    // 初始化下拉菜单系统
    initializeDropdownSystem() {
        // 创建增强的下拉菜单包装器
        this.createEnhancedDropdownWrapper();
        
        // 设置全局事件监听
        this.setupGlobalEventListeners();
        
        // 设置菜单控制系统
        this.setupMenuControlSystem();
        
        // 设置性能监控
        this.setupPerformanceMonitoring();
    }
    
    // 创建增强的下拉菜单包装器
    createEnhancedDropdownWrapper() {
        const originalWrapper = KanbanDropdownMenuWrapper;
        
        this.EnhancedKanbanDropdownMenuWrapper = class extends originalWrapper {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加事件监听
                this.addEventListeners();
                
                // 添加性能监控
                this.addPerformanceMonitoring();
                
                // 添加可访问性支持
                this.addAccessibilitySupport();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = useState({
                    isOpen: false,
                    openTime: null,
                    menuId: null,
                    position: { x: 0, y: 0 },
                    isAnimating: false
                });
                
                // 生成唯一ID
                this.menuId = `kanban_dropdown_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                this.enhancedState.menuId = this.menuId;
                
                // 注册菜单
                this.registerMenu();
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的点击处理
                this.enhancedOnClick = (event) => {
                    // 记录点击位置
                    this.enhancedState.position = {
                        x: event.clientX,
                        y: event.clientY
                    };
                    
                    // 检查是否应该关闭菜单
                    if (this.shouldCloseMenus(event)) {
                        this.closeAllMenus();
                    }
                    
                    // 记录交互统计
                    this.recordInteraction('click');
                };
                
                // 判断是否应该关闭菜单
                this.shouldCloseMenus = (event) => {
                    // 检查点击目标
                    const target = event.target;
                    
                    // 如果点击的是菜单项，不关闭
                    if (target.closest('.dropdown-item')) {
                        return false;
                    }
                    
                    // 如果点击的是菜单切换器，不关闭
                    if (target.closest('.dropdown-toggle')) {
                        return false;
                    }
                    
                    // 其他情况关闭菜单
                    return true;
                };
                
                // 关闭所有菜单
                this.closeAllMenus = () => {
                    // 调用原始关闭方法
                    this.dropdownControl.closeAll();
                    
                    // 更新状态
                    this.enhancedState.isOpen = false;
                    
                    // 记录关闭时间
                    if (this.enhancedState.openTime) {
                        const openDuration = Date.now() - this.enhancedState.openTime;
                        this.recordOpenDuration(openDuration);
                        this.enhancedState.openTime = null;
                    }
                    
                    // 移除活动菜单
                    this.removeFromActiveMenus();
                    
                    // 记录统计
                    this.menuStatistics.closeCount++;
                };
                
                // 打开菜单
                this.openMenu = () => {
                    // 检查最大打开菜单数
                    if (this.activeMenus.size >= this.dropdownConfig.maxOpenMenus) {
                        this.closeOldestMenu();
                    }
                    
                    // 更新状态
                    this.enhancedState.isOpen = true;
                    this.enhancedState.openTime = Date.now();
                    
                    // 添加到活动菜单
                    this.addToActiveMenus();
                    
                    // 记录统计
                    this.menuStatistics.openCount++;
                };
                
                // 关闭最旧的菜单
                this.closeOldestMenu = () => {
                    const oldestMenuId = this.activeMenus.values().next().value;
                    const oldestMenu = this.menuRegistry.get(oldestMenuId);
                    if (oldestMenu) {
                        oldestMenu.close();
                    }
                };
                
                // 添加到活动菜单
                this.addToActiveMenus = () => {
                    this.activeMenus.add(this.menuId);
                };
                
                // 从活动菜单移除
                this.removeFromActiveMenus = () => {
                    this.activeMenus.delete(this.menuId);
                };
                
                // 记录打开时长
                this.recordOpenDuration = (duration) => {
                    const totalOpens = this.menuStatistics.openCount;
                    this.menuStatistics.averageOpenTime = 
                        (this.menuStatistics.averageOpenTime * (totalOpens - 1) + duration) / totalOpens;
                };
                
                // 记录交互
                this.recordInteraction = (type) => {
                    console.log(`Menu interaction: ${type} on menu ${this.menuId}`);
                };
                
                // 处理键盘事件
                this.onKeyDown = (event) => {
                    switch (event.key) {
                        case 'Escape':
                            if (this.dropdownConfig.enableEscapeKey) {
                                event.preventDefault();
                                this.closeAllMenus();
                            }
                            break;
                        case 'Tab':
                            // 处理Tab键导航
                            this.handleTabNavigation(event);
                            break;
                        case 'ArrowDown':
                        case 'ArrowUp':
                            // 处理箭头键导航
                            this.handleArrowNavigation(event);
                            break;
                    }
                };
                
                // 处理Tab导航
                this.handleTabNavigation = (event) => {
                    if (this.enhancedState.isOpen) {
                        // 在菜单项间导航
                        const menuItems = this.getMenuItems();
                        const currentIndex = this.getCurrentFocusIndex(menuItems);
                        
                        if (event.shiftKey) {
                            // Shift+Tab - 向上导航
                            this.focusPreviousItem(menuItems, currentIndex);
                        } else {
                            // Tab - 向下导航
                            this.focusNextItem(menuItems, currentIndex);
                        }
                        
                        event.preventDefault();
                    }
                };
                
                // 处理箭头导航
                this.handleArrowNavigation = (event) => {
                    if (this.enhancedState.isOpen) {
                        const menuItems = this.getMenuItems();
                        const currentIndex = this.getCurrentFocusIndex(menuItems);
                        
                        if (event.key === 'ArrowDown') {
                            this.focusNextItem(menuItems, currentIndex);
                        } else {
                            this.focusPreviousItem(menuItems, currentIndex);
                        }
                        
                        event.preventDefault();
                    }
                };
                
                // 获取菜单项
                this.getMenuItems = () => {
                    const menuElement = this.rootRef.el;
                    return menuElement ? menuElement.querySelectorAll('.dropdown-item') : [];
                };
                
                // 获取当前焦点索引
                this.getCurrentFocusIndex = (menuItems) => {
                    const activeElement = document.activeElement;
                    return Array.from(menuItems).indexOf(activeElement);
                };
                
                // 聚焦下一项
                this.focusNextItem = (menuItems, currentIndex) => {
                    const nextIndex = (currentIndex + 1) % menuItems.length;
                    if (menuItems[nextIndex]) {
                        menuItems[nextIndex].focus();
                    }
                };
                
                // 聚焦上一项
                this.focusPreviousItem = (menuItems, currentIndex) => {
                    const prevIndex = currentIndex <= 0 ? menuItems.length - 1 : currentIndex - 1;
                    if (menuItems[prevIndex]) {
                        menuItems[prevIndex].focus();
                    }
                };
                
                // 注册菜单
                this.registerMenu = () => {
                    this.menuRegistry.set(this.menuId, {
                        id: this.menuId,
                        component: this,
                        close: () => this.closeAllMenus(),
                        open: () => this.openMenu(),
                        isOpen: () => this.enhancedState.isOpen
                    });
                    
                    this.menuStatistics.totalMenus++;
                };
                
                // 注销菜单
                this.unregisterMenu = () => {
                    this.menuRegistry.delete(this.menuId);
                    this.removeFromActiveMenus();
                    this.menuStatistics.totalMenus--;
                };
            }
            
            addEventListeners() {
                // 添加键盘事件监听
                useExternalListener(document, 'keydown', this.onKeyDown);
                
                // 添加外部点击监听
                if (this.dropdownConfig.enableClickOutside) {
                    useExternalListener(document, 'click', (event) => {
                        if (!this.rootRef.el?.contains(event.target)) {
                            this.closeAllMenus();
                        }
                    });
                }
            }
            
            addPerformanceMonitoring() {
                // 监控菜单性能
                this.performanceObserver = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (entry.name.includes('dropdown')) {
                            this.recordPerformanceMetric(entry);
                        }
                    }
                });
                
                this.performanceObserver.observe({ entryTypes: ['measure'] });
            }
            
            addAccessibilitySupport() {
                // 添加ARIA属性
                this.addAriaAttributes = () => {
                    const menuElement = this.rootRef.el;
                    if (menuElement) {
                        menuElement.setAttribute('role', 'menu');
                        menuElement.setAttribute('aria-expanded', this.enhancedState.isOpen.toString());
                        menuElement.setAttribute('aria-label', 'Kanban dropdown menu');
                    }
                };
                
                // 更新ARIA状态
                useEffect(() => {
                    this.addAriaAttributes();
                });
            }
            
            // 重写原始方法
            onClick(event) {
                this.enhancedOnClick(event);
            }
            
            // 组件销毁时清理
            onWillDestroy() {
                this.unregisterMenu();
                
                if (this.performanceObserver) {
                    this.performanceObserver.disconnect();
                }
            }
            
            // 记录性能指标
            recordPerformanceMetric(entry) {
                console.log(`Menu performance: ${entry.name} took ${entry.duration}ms`);
            }
            
            // 获取菜单统计
            getMenuStatistics() {
                return {
                    menuId: this.menuId,
                    isOpen: this.enhancedState.isOpen,
                    openTime: this.enhancedState.openTime,
                    position: this.enhancedState.position
                };
            }
        };
    }
    
    // 设置全局事件监听
    setupGlobalEventListeners() {
        // 监听全局键盘事件
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && this.dropdownConfig.enableEscapeKey) {
                this.closeAllActiveMenus();
            }
        });
        
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            this.repositionActiveMenus();
        });
    }
    
    // 设置菜单控制系统
    setupMenuControlSystem() {
        this.menuControl = {
            openMenu: (menuId) => {
                const menu = this.menuRegistry.get(menuId);
                if (menu) {
                    menu.open();
                }
            },
            
            closeMenu: (menuId) => {
                const menu = this.menuRegistry.get(menuId);
                if (menu) {
                    menu.close();
                }
            },
            
            closeAllMenus: () => {
                this.closeAllActiveMenus();
            },
            
            getActiveMenus: () => {
                return Array.from(this.activeMenus);
            }
        };
    }
    
    // 设置性能监控
    setupPerformanceMonitoring() {
        this.performanceConfig = {
            enableMonitoring: true,
            sampleRate: 0.1,
            thresholds: {
                openTime: 100, // 100ms
                closeTime: 50   // 50ms
            }
        };
    }
    
    // 关闭所有活动菜单
    closeAllActiveMenus() {
        for (const menuId of this.activeMenus) {
            const menu = this.menuRegistry.get(menuId);
            if (menu) {
                menu.close();
            }
        }
    }
    
    // 重新定位活动菜单
    repositionActiveMenus() {
        for (const menuId of this.activeMenus) {
            const menu = this.menuRegistry.get(menuId);
            if (menu && menu.reposition) {
                menu.reposition();
            }
        }
    }
    
    // 创建下拉菜单包装器
    createDropdownWrapper(props) {
        return new this.EnhancedKanbanDropdownMenuWrapper(props);
    }
    
    // 获取菜单统计
    getMenuStatistics() {
        return {
            ...this.menuStatistics,
            activeMenuCount: this.activeMenus.size,
            registeredMenuCount: this.menuRegistry.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理菜单注册表
        this.menuRegistry.clear();
        
        // 清理活动菜单
        this.activeMenus.clear();
        
        // 重置统计
        this.menuStatistics = {
            totalMenus: 0,
            openCount: 0,
            closeCount: 0,
            averageOpenTime: 0
        };
    }
}

// 使用示例
const dropdownManager = new KanbanDropdownMenuManager();

// 创建下拉菜单包装器
const wrapper = dropdownManager.createDropdownWrapper({
    slots: {
        default: { content: 'Menu content' }
    }
});

// 获取统计信息
const stats = dropdownManager.getMenuStatistics();
console.log('Dropdown menu statistics:', stats);
```

## 技术特点

### 1. 轻量级设计
- **简洁代码**: 仅27行代码的轻量级实现
- **专注功能**: 专注于下拉菜单包装功能
- **最小依赖**: 最小的依赖关系
- **高效执行**: 高效的代码执行

### 2. 钩子集成
- **下拉菜单钩子**: 集成下拉菜单关闭钩子
- **统一控制**: 统一控制下拉菜单的行为
- **生命周期**: 与组件生命周期集成
- **自动管理**: 自动管理下拉菜单状态

### 3. 插槽支持
- **内容传递**: 通过插槽传递菜单内容
- **灵活布局**: 支持灵活的内容布局
- **组件复用**: 提高组件的复用性
- **内容隔离**: 隔离不同的菜单内容

### 4. 事件处理
- **点击事件**: 处理点击事件
- **事件传播**: 控制事件传播
- **用户交互**: 响应用户交互
- **状态同步**: 同步菜单状态

## 设计模式

### 1. 包装器模式 (Wrapper Pattern)
- **功能包装**: 包装下拉菜单功能
- **接口统一**: 提供统一的接口
- **行为增强**: 增强原有行为

### 2. 代理模式 (Proxy Pattern)
- **控制访问**: 控制对下拉菜单的访问
- **行为代理**: 代理下拉菜单的行为
- **透明操作**: 对用户透明的操作

### 3. 观察者模式 (Observer Pattern)
- **事件监听**: 监听用户交互事件
- **状态变化**: 响应状态变化
- **自动响应**: 自动响应用户操作

### 4. 单例模式 (Singleton Pattern)
- **全局控制**: 全局控制下拉菜单
- **统一管理**: 统一管理菜单状态
- **资源共享**: 共享菜单控制资源

## 注意事项

1. **事件冲突**: 避免与其他组件的事件冲突
2. **性能影响**: 确保轻量级实现不影响性能
3. **兼容性**: 确保与不同浏览器的兼容性
4. **可访问性**: 确保下拉菜单的可访问性

## 扩展建议

1. **动画支持**: 添加下拉菜单的动画效果
2. **位置控制**: 添加菜单位置的智能控制
3. **主题支持**: 支持不同的视觉主题
4. **键盘导航**: 增强键盘导航功能
5. **触摸支持**: 优化触摸设备的支持

该看板下拉菜单包装器为Odoo Web客户端提供了简洁而有效的下拉菜单管理功能，通过轻量级的设计和统一的控制确保了下拉菜单的一致性和可用性。

# Kanban 看板视图系统

## 概述

Kanban 看板视图系统是 Odoo Web 客户端中的核心视图组件之一，提供了直观的卡片式数据展示和管理功能。该系统采用现代化的架构设计，基于 OWL 框架构建，具备完整的 MVC 架构、组件化设计、响应式布局等特性，为用户提供了高效的数据可视化和交互体验。

## 系统架构

### 核心组件架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Kanban View System                       │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Parser    │  │  Compiler   │  │   View      │         │
│  │             │  │             │  │             │         │
│  │ ArchParser  │  │ Compiler    │  │ KanbanView  │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ Controller  │  │  Renderer   │  │   Record    │         │
│  │             │  │             │  │             │         │
│  │ Controller  │  │  Renderer   │  │ KanbanRecord│         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Header    │  │ QuickCreate │  │ ProgressBar │         │
│  │             │  │             │  │             │         │
│  │ KanbanHeader│  │ QuickCreate │  │ ProgressHook│         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### 数据流架构

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Server    │───▶│   Model     │───▶│ Controller  │
│             │    │             │    │             │
│ Data Source │    │ Data Layer  │    │ Logic Layer │
└─────────────┘    └─────────────┘    └─────────────┘
                                              │
                                              ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    View     │◀───│  Renderer   │◀───│ Components  │
│             │    │             │    │             │
│ Presentation│    │ Render Layer│    │ UI Elements │
└─────────────┘    └─────────────┘    └─────────────┘
```

## 文件结构

### 核心文件列表

| 文件名 | 行数 | 功能描述 | 状态 |
|--------|------|----------|------|
| `kanban_view.js` | - | 看板视图主入口 | ✅ 已有文档 |
| `kanban_arch_parser.js` | 211 | 架构解析器 | ✅ 已完成 |
| `kanban_compiler.js` | 218 | 模板编译器 | ✅ 已完成 |
| `kanban_controller.js` | 353 | 视图控制器 | ✅ 已完成 |
| `kanban_renderer.js` | 601 | 视图渲染器 | ✅ 已完成 |
| `kanban_record.js` | 414 | 记录组件 | ✅ 已完成 |
| `kanban_header.js` | 309 | 头部组件 | ✅ 已完成 |
| `kanban_record_quick_create.js` | 275 | 记录快速创建 | ✅ 已完成 |
| `kanban_column_quick_create.js` | 107 | 列快速创建 | ✅ 已完成 |
| `kanban_cover_image_dialog.js` | 84 | 封面图片对话框 | ✅ 已完成 |
| `kanban_column_examples_dialog.js` | 68 | 列示例对话框 | ✅ 已完成 |
| `kanban_dropdown_menu_wrapper.js` | 27 | 下拉菜单包装器 | ✅ 已完成 |
| `kanban_color_picker_legacy.js` | 82 | 颜色选择器遗留支持 | ✅ 已完成 |
| `progress_bar_hook.js` | 363 | 进度条钩子 | ✅ 已完成 |

**总计**: 14个文件，约3100+行代码

### 功能模块分类

#### 1. 核心架构模块 (Core Architecture)
- **kanban_view.js**: 视图主入口，整合所有组件
- **kanban_arch_parser.js**: XML架构解析，提取视图配置
- **kanban_compiler.js**: 模板编译，生成可执行代码
- **kanban_controller.js**: 业务逻辑控制，处理用户交互
- **kanban_renderer.js**: 界面渲染，管理DOM结构

#### 2. 记录管理模块 (Record Management)
- **kanban_record.js**: 单个记录卡片的展示和交互
- **kanban_record_quick_create.js**: 快速创建新记录
- **kanban_cover_image_dialog.js**: 记录封面图片管理

#### 3. 列管理模块 (Column Management)
- **kanban_header.js**: 列头部显示和操作
- **kanban_column_quick_create.js**: 快速创建新列
- **kanban_column_examples_dialog.js**: 列配置示例指导

#### 4. 交互增强模块 (Interaction Enhancement)
- **kanban_dropdown_menu_wrapper.js**: 下拉菜单统一包装
- **progress_bar_hook.js**: 进度条功能钩子

#### 5. 兼容性模块 (Compatibility)
- **kanban_color_picker_legacy.js**: 旧版颜色选择器兼容

## 技术特性

### 1. 现代化架构
- **OWL框架**: 基于Odoo Web Library构建
- **组件化设计**: 高度模块化的组件架构
- **响应式编程**: 响应式的数据绑定和状态管理
- **TypeScript支持**: 完整的类型定义和检查

### 2. 性能优化
- **虚拟滚动**: 大数据量的虚拟滚动支持
- **懒加载**: 按需加载记录和资源
- **缓存机制**: 智能的数据和模板缓存
- **防抖处理**: 用户交互的防抖优化

### 3. 用户体验
- **拖拽排序**: 直观的拖拽排序功能
- **快速创建**: 便捷的记录和列快速创建
- **实时更新**: 实时的数据同步和更新
- **响应式布局**: 适配不同屏幕尺寸

### 4. 扩展性
- **插件系统**: 灵活的插件扩展机制
- **主题支持**: 可定制的视觉主题
- **钩子函数**: 丰富的钩子函数支持
- **配置驱动**: 配置驱动的功能开关

## 核心功能

### 1. 数据展示
- **卡片视图**: 直观的卡片式数据展示
- **分组显示**: 按字段分组的列式布局
- **字段渲染**: 多种字段类型的智能渲染
- **图片支持**: 完整的图片显示和管理

### 2. 数据操作
- **CRUD操作**: 完整的增删改查功能
- **批量操作**: 高效的批量数据操作
- **拖拽移动**: 直观的拖拽移动功能
- **状态管理**: 智能的记录状态管理

### 3. 交互功能
- **点击操作**: 丰富的点击交互功能
- **右键菜单**: 上下文相关的右键菜单
- **键盘导航**: 完整的键盘导航支持
- **触摸支持**: 移动设备的触摸优化

### 4. 可视化功能
- **进度条**: 直观的进度条显示
- **颜色编码**: 灵活的颜色编码系统
- **图标支持**: 丰富的图标显示
- **动画效果**: 流畅的动画过渡效果

## 设计模式

### 1. MVC架构模式
- **Model**: 数据模型和业务逻辑
- **View**: 用户界面和视觉展示
- **Controller**: 用户交互和流程控制

### 2. 组件模式 (Component Pattern)
- **封装性**: 高度封装的组件设计
- **可复用**: 组件的高度可复用性
- **组合性**: 组件的灵活组合使用

### 3. 观察者模式 (Observer Pattern)
- **数据监听**: 监听数据模型变化
- **状态同步**: 自动同步组件状态
- **事件响应**: 响应用户交互事件

### 4. 策略模式 (Strategy Pattern)
- **渲染策略**: 不同的字段渲染策略
- **交互策略**: 不同的用户交互策略
- **布局策略**: 不同的布局显示策略

### 5. 工厂模式 (Factory Pattern)
- **组件创建**: 动态创建不同类型组件
- **配置生成**: 生成不同的配置对象
- **实例管理**: 管理组件实例生命周期

## 开发指南

### 1. 环境要求
- **Node.js**: >= 14.0.0
- **OWL**: >= 2.0.0
- **Odoo**: >= 16.0
- **浏览器**: 现代浏览器支持

### 2. 开发流程
1. **架构设计**: 设计组件架构和数据流
2. **模板编写**: 编写XML模板和样式
3. **组件开发**: 开发JavaScript组件逻辑
4. **测试验证**: 编写和执行单元测试
5. **集成测试**: 进行系统集成测试

### 3. 最佳实践
- **代码规范**: 遵循Odoo代码规范
- **性能优化**: 注意性能优化和内存管理
- **可访问性**: 确保组件的可访问性
- **国际化**: 支持多语言国际化

### 4. 调试技巧
- **开发工具**: 使用浏览器开发工具
- **日志记录**: 合理使用日志记录
- **性能分析**: 使用性能分析工具
- **错误处理**: 完善的错误处理机制

## 使用示例

### 1. 基本看板视图配置

```xml
<kanban default_group_by="stage_id" class="o_kanban_test">
    <field name="stage_id"/>
    <field name="name"/>
    <field name="user_id"/>
    <field name="priority"/>
    <field name="color"/>

    <progressbar field="priority" colors='{"0": "success", "1": "warning", "2": "danger"}'/>

    <templates>
        <t t-name="kanban-box">
            <div class="oe_kanban_card oe_kanban_global_click">
                <div class="oe_kanban_content">
                    <div class="o_kanban_record_title">
                        <field name="name"/>
                    </div>
                    <div class="o_kanban_record_body">
                        <field name="user_id" widget="many2one_avatar_user"/>
                        <field name="priority" widget="priority"/>
                    </div>
                </div>
            </div>
        </t>
    </templates>
</kanban>
```

### 2. 自定义看板记录组件

```javascript
class CustomKanbanRecord extends KanbanRecord {
    setup() {
        super.setup();
        this.customState = useState({
            isExpanded: false,
            customData: null,
        });
    }

    onCustomAction() {
        // 自定义动作逻辑
        this.customState.isExpanded = !this.customState.isExpanded;
    }

    async loadCustomData() {
        // 加载自定义数据
        const data = await this.orm.call('custom.model', 'get_custom_data', [this.props.record.resId]);
        this.customState.customData = data;
    }
}
```

### 3. 扩展看板控制器

```javascript
class ExtendedKanbanController extends KanbanController {
    setup() {
        super.setup();
        this.customActions = {
            bulkArchive: this.bulkArchive.bind(this),
            exportData: this.exportData.bind(this),
            customFilter: this.customFilter.bind(this),
        };
    }

    async bulkArchive(recordIds) {
        // 批量归档逻辑
        await this.orm.write(this.props.resModel, recordIds, { active: false });
        this.model.load();
    }

    async exportData() {
        // 数据导出逻辑
        const data = this.model.root.records.map(record => record.data);
        this.downloadAsCSV(data);
    }

    customFilter(domain) {
        // 自定义过滤逻辑
        this.model.setDomain(domain);
    }
}
```

## 性能优化

### 1. 渲染优化
- **虚拟滚动**: 对于大量记录使用虚拟滚动
- **懒加载**: 按需加载记录内容
- **缓存策略**: 缓存编译后的模板
- **批量更新**: 批量处理DOM更新

### 2. 内存管理
- **组件清理**: 及时清理不需要的组件
- **事件解绑**: 组件销毁时解绑事件监听器
- **缓存限制**: 限制缓存大小避免内存泄漏
- **引用管理**: 避免循环引用

### 3. 网络优化
- **数据分页**: 分页加载大量数据
- **请求合并**: 合并多个API请求
- **缓存机制**: 缓存服务器响应
- **预加载**: 预加载可能需要的数据

## 测试策略

### 1. 单元测试
```javascript
// 测试看板记录组件
describe('KanbanRecord', () => {
    test('should render record correctly', () => {
        const record = createMockRecord();
        const component = mount(KanbanRecord, { props: { record } });
        expect(component.find('.oe_kanban_card')).toBeTruthy();
    });

    test('should handle click events', async () => {
        const onOpenRecord = jest.fn();
        const component = mount(KanbanRecord, {
            props: { record: createMockRecord(), openRecord: onOpenRecord }
        });

        await component.find('.oe_kanban_global_click').trigger('click');
        expect(onOpenRecord).toHaveBeenCalled();
    });
});
```

### 2. 集成测试
```javascript
// 测试看板视图集成
describe('KanbanView Integration', () => {
    test('should load and display records', async () => {
        const view = await createKanbanView({
            resModel: 'test.model',
            arch: '<kanban><templates><t t-name="kanban-box">...</t></templates></kanban>',
        });

        expect(view.findAll('.oe_kanban_card')).toHaveLength(3);
    });

    test('should handle drag and drop', async () => {
        const view = await createKanbanView({ enableDragDrop: true });
        const record = view.find('.oe_kanban_card');
        const targetGroup = view.findAll('.o_kanban_group')[1];

        await dragAndDrop(record, targetGroup);
        expect(record.closest('.o_kanban_group')).toBe(targetGroup);
    });
});
```

### 3. 端到端测试
```javascript
// 使用Playwright进行E2E测试
test('kanban view workflow', async ({ page }) => {
    await page.goto('/web#action=test_kanban_action');

    // 等待看板视图加载
    await page.waitForSelector('.o_kanban_view');

    // 测试记录创建
    await page.click('.o-kanban-button-new');
    await page.fill('input[name="name"]', 'Test Record');
    await page.click('button[type="submit"]');

    // 验证记录已创建
    await expect(page.locator('.oe_kanban_card:has-text("Test Record")')).toBeVisible();

    // 测试拖拽功能
    const record = page.locator('.oe_kanban_card').first();
    const targetColumn = page.locator('.o_kanban_group').nth(1);
    await record.dragTo(targetColumn);

    // 验证记录已移动
    await expect(targetColumn.locator('.oe_kanban_card').first()).toContainText('Test Record');
});
```

## 故障排除

### 1. 常见问题
- **记录不显示**: 检查字段权限和数据过滤
- **拖拽不工作**: 确认拖拽配置和权限设置
- **性能问题**: 检查数据量和渲染优化
- **样式问题**: 检查CSS类和主题配置

### 2. 调试方法
- **控制台日志**: 使用console.log查看数据流
- **断点调试**: 在关键方法设置断点
- **网络监控**: 监控API请求和响应
- **性能分析**: 使用Performance工具分析

### 3. 错误处理
```javascript
// 错误边界组件
class KanbanErrorBoundary extends Component {
    static template = 'web.KanbanErrorBoundary';

    setup() {
        this.state = useState({ hasError: false, error: null });
    }

    catchError(error) {
        this.state.hasError = true;
        this.state.error = error;
        console.error('Kanban view error:', error);
    }

    onRetry() {
        this.state.hasError = false;
        this.state.error = null;
        this.render();
    }
}
```

## 扩展开发

### 1. 自定义字段小部件
```javascript
class CustomKanbanWidget extends Widget {
    static template = 'custom.KanbanWidget';
    static props = ['record', 'fieldName', 'options'];

    setup() {
        this.fieldValue = this.props.record.data[this.props.fieldName];
    }

    get displayValue() {
        // 自定义显示逻辑
        return this.formatValue(this.fieldValue);
    }

    formatValue(value) {
        // 格式化值的逻辑
        return value ? value.toString().toUpperCase() : '';
    }
}

// 注册小部件
registry.category('kanban_widgets').add('custom_widget', CustomKanbanWidget);
```

### 2. 自定义看板动作
```javascript
class CustomKanbanAction {
    static description = 'Custom Action';
    static icon = 'fa-custom';

    static isAvailable(record) {
        // 检查动作是否可用
        return record.data.state === 'draft';
    }

    static async execute(record, context) {
        // 执行自定义动作
        const result = await context.orm.call(
            record.resModel,
            'custom_action',
            [record.resId]
        );

        if (result.success) {
            context.notification.add('Action completed successfully', { type: 'success' });
            record.load();
        }
    }
}

// 注册动作
registry.category('kanban_actions').add('custom_action', CustomKanbanAction);
```

## 版本兼容性

### 支持的Odoo版本
- **Odoo 16.0+**: 完全支持
- **Odoo 15.0**: 部分功能支持
- **Odoo 14.0**: 基础功能支持

### 浏览器兼容性
- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+

### 移动端支持
- **iOS Safari**: 14+
- **Android Chrome**: 90+
- **响应式设计**: 完全支持

## 贡献指南

### 1. 代码贡献
1. Fork项目仓库
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request
5. 代码审查和合并

### 2. 文档贡献
- 改进现有文档
- 添加使用示例
- 翻译文档内容
- 报告文档问题

### 3. 问题报告
- 使用Issue模板
- 提供详细的重现步骤
- 包含环境信息
- 附加相关日志

## 许可证

本项目采用 LGPL-3.0 许可证，详情请参阅 [LICENSE](LICENSE) 文件。

## 联系方式

- **官方文档**: https://www.odoo.com/documentation
- **社区论坛**: https://www.odoo.com/forum
- **GitHub仓库**: https://github.com/odoo/odoo
- **技术支持**: <EMAIL>

---

*最后更新: 2024年6月*
# KanbanColumnExamplesDialog - 看板列示例对话框

## 概述

`kanban_column_examples_dialog.js` 是 Odoo Web 客户端看板视图的列示例对话框组件，负责显示看板列配置的示例。该模块包含68行代码，是一个OWL组件，专门用于在用户创建或配置看板列时提供可视化的示例，具备示例生成、笔记本布局、随机数据、模板渲染等特性，是看板视图系统中用户指导和帮助的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/kanban/kanban_column_examples_dialog.js`
- **行数**: 68
- **模块**: `@web/views/kanban/kanban_column_examples_dialog`

## 依赖关系

```javascript
// 核心依赖
'@web/core/dialog/dialog'       // 对话框组件
'@web/core/notebook/notebook'   // 笔记本组件
'@odoo/owl'                     // OWL框架
```

## 核心工具函数

### 1. 随机数生成函数

```javascript
const random = (min, max) => Math.floor(Math.random() * (max - min) + min);
```

**工具函数功能**:
- **随机数生成**: 生成指定范围内的随机整数
- **示例数据**: 用于生成示例记录的随机数量
- **变化性**: 确保示例数据的多样性
- **简单实用**: 简单而实用的工具函数

## 主要组件定义

### 1. KanbanExamplesNotebookTemplate - 看板示例笔记本模板

```javascript
class KanbanExamplesNotebookTemplate extends Component {
    static template = "web.KanbanExamplesNotebookTemplate";
    static props = ["*"];
    static defaultProps = {
        columns: [],
        foldedColumns: [],
    };

    setup() {
        this.columns = [];
        const hasBullet = this.props.bullets && this.props.bullets.length;
        const allColumns = [...this.props.columns, ...this.props.foldedColumns];
        for (const title of allColumns) {
            const col = { title, records: [] };
            this.columns.push(col);
            for (let i = 0; i < random(1, 5); i++) {
                const rec = { id: i };
                if (hasBullet && Math.random() > 0.3) {
                    const sampleId = Math.floor(Math.random() * this.props.bullets.length);
                    rec.bullet = this.props.bullets[sampleId];
                }
                col.records.push(rec);
            }
        }
    }
}
```

**模板组件特性**:
- **专用模板**: 使用KanbanExamplesNotebookTemplate模板
- **灵活属性**: 支持任意属性传递
- **默认属性**: 提供合理的默认属性值
- **数据生成**: 自动生成示例数据

### 2. KanbanColumnExamplesDialog - 看板列示例对话框

```javascript
class KanbanColumnExamplesDialog extends Component {
    static template = "web.KanbanColumnExamplesDialog";
    static components = { Dialog, Notebook };
    static props = ["*"];

    setup() {
        this.navList = useRef("navList");
        this.pages = [];
        this.activePage = null;
        this.props.examples.forEach((eg) => {
            this.pages.push({
                title: eg.name,
                Component: KanbanExamplesNotebookTemplate,
                props: eg,
            });
        });
        this.activePage = this.pages[0];
    }
}
```

**对话框组件特性**:
- **专用模板**: 使用KanbanColumnExamplesDialog模板
- **子组件**: 集成Dialog和Notebook组件
- **引用管理**: 使用useRef管理导航列表
- **页面管理**: 管理多个示例页面

## 核心功能

### 1. 示例数据生成

```javascript
setup() {
    this.columns = [];
    const hasBullet = this.props.bullets && this.props.bullets.length;
    const allColumns = [...this.props.columns, ...this.props.foldedColumns];
    for (const title of allColumns) {
        const col = { title, records: [] };
        this.columns.push(col);
        for (let i = 0; i < random(1, 5); i++) {
            const rec = { id: i };
            if (hasBullet && Math.random() > 0.3) {
                const sampleId = Math.floor(Math.random() * this.props.bullets.length);
                rec.bullet = this.props.bullets[sampleId];
            }
            col.records.push(rec);
        }
    }
}
```

**数据生成功能**:
- **列合并**: 合并普通列和折叠列
- **随机记录**: 为每列生成随机数量的记录
- **项目符号**: 随机分配项目符号
- **数据结构**: 创建完整的列和记录数据结构

### 2. 页面管理

```javascript
setup() {
    this.navList = useRef("navList");
    this.pages = [];
    this.activePage = null;
    this.props.examples.forEach((eg) => {
        this.pages.push({
            title: eg.name,
            Component: KanbanExamplesNotebookTemplate,
            props: eg,
        });
    });
    this.activePage = this.pages[0];
}
```

**页面管理功能**:
- **页面创建**: 为每个示例创建页面
- **组件配置**: 配置页面组件和属性
- **导航管理**: 管理页面导航
- **活动页面**: 设置默认活动页面

## 使用场景

### 1. 看板示例管理器

```javascript
// 看板示例管理器
class KanbanExampleManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置示例配置
        this.exampleConfig = {
            enableRandomData: true,
            enableBullets: true,
            enableFoldedColumns: true,
            enableCustomExamples: true,
            maxRecordsPerColumn: 5,
            minRecordsPerColumn: 1,
            bulletProbability: 0.7
        };
        
        // 设置示例模板
        this.exampleTemplates = new Map();
        
        // 设置示例数据
        this.exampleData = new Map();
        
        // 设置示例统计
        this.exampleStatistics = {
            totalExamples: 0,
            totalColumns: 0,
            totalRecords: 0,
            viewCount: 0
        };
        
        this.initializeExampleSystem();
    }
    
    // 初始化示例系统
    initializeExampleSystem() {
        // 创建增强的示例对话框
        this.createEnhancedExampleDialog();
        
        // 设置默认示例
        this.setupDefaultExamples();
        
        // 设置数据生成器
        this.setupDataGenerators();
        
        // 设置模板系统
        this.setupTemplateSystem();
    }
    
    // 创建增强的示例对话框
    createEnhancedExampleDialog() {
        const originalDialog = KanbanColumnExamplesDialog;
        const originalTemplate = KanbanExamplesNotebookTemplate;
        
        this.EnhancedKanbanColumnExamplesDialog = class extends originalDialog {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加交互功能
                this.addInteractiveFeatures();
                
                // 添加自定义功能
                this.addCustomizationFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = useState({
                    currentExample: 0,
                    isInteractive: false,
                    customData: null,
                    viewMode: 'preview'
                });
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 切换示例
                this.switchExample = (index) => {
                    if (index >= 0 && index < this.pages.length) {
                        this.enhancedState.currentExample = index;
                        this.activePage = this.pages[index];
                        this.recordExampleView(this.activePage.title);
                    }
                };
                
                // 生成新数据
                this.generateNewData = () => {
                    for (const page of this.pages) {
                        this.regeneratePageData(page);
                    }
                    this.render();
                };
                
                // 重新生成页面数据
                this.regeneratePageData = (page) => {
                    const props = page.props;
                    const columns = [];
                    const hasBullet = props.bullets && props.bullets.length;
                    const allColumns = [...props.columns, ...props.foldedColumns];
                    
                    for (const title of allColumns) {
                        const col = { title, records: [] };
                        columns.push(col);
                        
                        const recordCount = random(
                            this.exampleConfig.minRecordsPerColumn,
                            this.exampleConfig.maxRecordsPerColumn + 1
                        );
                        
                        for (let i = 0; i < recordCount; i++) {
                            const rec = { 
                                id: `${title}_${i}`,
                                title: this.generateRecordTitle(),
                                priority: this.generatePriority(),
                                assignee: this.generateAssignee()
                            };
                            
                            if (hasBullet && Math.random() < this.exampleConfig.bulletProbability) {
                                const sampleId = Math.floor(Math.random() * props.bullets.length);
                                rec.bullet = props.bullets[sampleId];
                            }
                            
                            col.records.push(rec);
                        }
                    }
                    
                    page.generatedColumns = columns;
                };
                
                // 生成记录标题
                this.generateRecordTitle = () => {
                    const titles = [
                        'Task A', 'Task B', 'Task C', 'Task D', 'Task E',
                        'Feature X', 'Feature Y', 'Feature Z',
                        'Bug Fix 1', 'Bug Fix 2', 'Bug Fix 3',
                        'Enhancement', 'Improvement', 'Optimization'
                    ];
                    return titles[Math.floor(Math.random() * titles.length)];
                };
                
                // 生成优先级
                this.generatePriority = () => {
                    const priorities = ['Low', 'Medium', 'High', 'Urgent'];
                    return priorities[Math.floor(Math.random() * priorities.length)];
                };
                
                // 生成分配人
                this.generateAssignee = () => {
                    const assignees = ['Alice', 'Bob', 'Charlie', 'Diana', 'Eve'];
                    return assignees[Math.floor(Math.random() * assignees.length)];
                };
                
                // 切换交互模式
                this.toggleInteractiveMode = () => {
                    this.enhancedState.isInteractive = !this.enhancedState.isInteractive;
                };
                
                // 切换视图模式
                this.switchViewMode = (mode) => {
                    this.enhancedState.viewMode = mode;
                };
                
                // 导出示例配置
                this.exportExampleConfig = () => {
                    const config = {
                        examples: this.pages.map(page => ({
                            name: page.title,
                            columns: page.props.columns,
                            foldedColumns: page.props.foldedColumns,
                            bullets: page.props.bullets
                        })),
                        settings: this.exampleConfig
                    };
                    
                    return JSON.stringify(config, null, 2);
                };
                
                // 导入示例配置
                this.importExampleConfig = (configJson) => {
                    try {
                        const config = JSON.parse(configJson);
                        this.loadExamplesFromConfig(config);
                    } catch (error) {
                        console.error('Failed to import example config:', error);
                    }
                };
                
                // 从配置加载示例
                this.loadExamplesFromConfig = (config) => {
                    this.pages = [];
                    
                    for (const example of config.examples) {
                        this.pages.push({
                            title: example.name,
                            Component: KanbanExamplesNotebookTemplate,
                            props: example
                        });
                    }
                    
                    if (config.settings) {
                        Object.assign(this.exampleConfig, config.settings);
                    }
                    
                    this.activePage = this.pages[0];
                };
                
                // 记录示例查看
                this.recordExampleView = (exampleName) => {
                    this.exampleStatistics.viewCount++;
                    console.log(`Example viewed: ${exampleName}`);
                };
            }
            
            addInteractiveFeatures() {
                // 添加拖拽支持
                this.addDragSupport = () => {
                    // 实现拖拽功能
                };
                
                // 添加编辑支持
                this.addEditSupport = () => {
                    // 实现编辑功能
                };
            }
            
            addCustomizationFeatures() {
                // 添加自定义颜色
                this.addCustomColors = () => {
                    // 实现自定义颜色功能
                };
                
                // 添加自定义字段
                this.addCustomFields = () => {
                    // 实现自定义字段功能
                };
            }
        };
        
        this.EnhancedKanbanExamplesNotebookTemplate = class extends originalTemplate {
            setup() {
                super.setup();
                
                // 使用增强的数据生成
                this.enhanceDataGeneration();
            }
            
            enhanceDataGeneration() {
                // 增强的数据生成逻辑
                if (this.props.generatedColumns) {
                    this.columns = this.props.generatedColumns;
                }
            }
        };
    }
    
    // 设置默认示例
    setupDefaultExamples() {
        // 项目管理示例
        this.exampleTemplates.set('project_management', {
            name: 'Project Management',
            columns: ['To Do', 'In Progress', 'Review', 'Done'],
            foldedColumns: ['Archived'],
            bullets: ['🔴', '🟡', '🟢', '🔵', '🟣']
        });
        
        // 销售流程示例
        this.exampleTemplates.set('sales_pipeline', {
            name: 'Sales Pipeline',
            columns: ['Lead', 'Qualified', 'Proposal', 'Negotiation', 'Closed Won'],
            foldedColumns: ['Closed Lost'],
            bullets: ['💰', '📞', '📧', '🤝', '✅']
        });
        
        // 招聘流程示例
        this.exampleTemplates.set('recruitment', {
            name: 'Recruitment Process',
            columns: ['Applied', 'Phone Screen', 'Interview', 'Offer'],
            foldedColumns: ['Rejected'],
            bullets: ['👤', '📱', '💼', '📋', '❌']
        });
        
        // 客户支持示例
        this.exampleTemplates.set('customer_support', {
            name: 'Customer Support',
            columns: ['New', 'In Progress', 'Waiting', 'Resolved'],
            foldedColumns: ['Closed'],
            bullets: ['🆘', '🔧', '⏳', '✅', '📁']
        });
    }
    
    // 设置数据生成器
    setupDataGenerators() {
        this.dataGenerators = {
            // 记录标题生成器
            recordTitle: () => {
                const prefixes = ['Task', 'Feature', 'Bug', 'Enhancement', 'Story'];
                const suffixes = ['A', 'B', 'C', 'X', 'Y', 'Z', '1', '2', '3'];
                const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
                const suffix = suffixes[Math.floor(Math.random() * suffixes.length)];
                return `${prefix} ${suffix}`;
            },
            
            // 优先级生成器
            priority: () => {
                const priorities = ['Low', 'Medium', 'High', 'Critical'];
                return priorities[Math.floor(Math.random() * priorities.length)];
            },
            
            // 分配人生成器
            assignee: () => {
                const names = ['Alice', 'Bob', 'Charlie', 'Diana', 'Eve', 'Frank'];
                return names[Math.floor(Math.random() * names.length)];
            },
            
            // 标签生成器
            tags: () => {
                const allTags = ['urgent', 'feature', 'bug', 'enhancement', 'documentation'];
                const tagCount = random(0, 3);
                const selectedTags = [];
                
                for (let i = 0; i < tagCount; i++) {
                    const tag = allTags[Math.floor(Math.random() * allTags.length)];
                    if (!selectedTags.includes(tag)) {
                        selectedTags.push(tag);
                    }
                }
                
                return selectedTags;
            }
        };
    }
    
    // 设置模板系统
    setupTemplateSystem() {
        this.templateConfig = {
            enableCustomTemplates: true,
            enableTemplateSharing: true,
            enableTemplateValidation: true
        };
    }
    
    // 创建示例对话框
    createExampleDialog(examples) {
        return new this.EnhancedKanbanColumnExamplesDialog({
            examples: examples || this.getDefaultExamples()
        });
    }
    
    // 获取默认示例
    getDefaultExamples() {
        return Array.from(this.exampleTemplates.values());
    }
    
    // 添加自定义示例
    addCustomExample(name, config) {
        this.exampleTemplates.set(name, {
            name: name,
            ...config
        });
    }
    
    // 移除示例
    removeExample(name) {
        this.exampleTemplates.delete(name);
    }
    
    // 获取示例统计
    getExampleStatistics() {
        return {
            ...this.exampleStatistics,
            templateCount: this.exampleTemplates.size,
            generatorCount: Object.keys(this.dataGenerators).length
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理模板
        this.exampleTemplates.clear();
        
        // 重置统计
        this.exampleStatistics = {
            totalExamples: 0,
            totalColumns: 0,
            totalRecords: 0,
            viewCount: 0
        };
    }
}

// 使用示例
const exampleManager = new KanbanExampleManager();

// 创建示例对话框
const dialog = exampleManager.createExampleDialog();

// 添加自定义示例
exampleManager.addCustomExample('custom_workflow', {
    columns: ['Start', 'Middle', 'End'],
    foldedColumns: ['Archive'],
    bullets: ['⭐', '🔥', '💎']
});

// 获取统计信息
const stats = exampleManager.getExampleStatistics();
console.log('Example statistics:', stats);
```

## 技术特点

### 1. 示例生成
- **随机数据**: 生成随机的示例数据
- **多样性**: 确保示例数据的多样性
- **真实性**: 生成接近真实使用场景的数据
- **可配置**: 支持配置示例生成参数

### 2. 用户界面
- **笔记本布局**: 使用笔记本组件组织示例
- **对话框**: 使用对话框展示示例
- **导航**: 提供清晰的示例导航
- **响应式**: 支持响应式布局

### 3. 数据结构
- **列结构**: 完整的列数据结构
- **记录结构**: 详细的记录数据结构
- **项目符号**: 支持项目符号显示
- **折叠列**: 支持折叠列的展示

### 4. 扩展性
- **组件化**: 高度组件化的设计
- **可定制**: 支持自定义示例
- **可扩展**: 易于扩展新功能
- **模块化**: 模块化的代码结构

## 设计模式

### 1. 组件模式 (Component Pattern)
- **组件化**: 高度组件化的设计
- **可复用**: 组件的可复用性
- **组合**: 组件的组合使用

### 2. 模板模式 (Template Pattern)
- **模板定义**: 定义示例模板
- **数据填充**: 动态填充示例数据
- **结构复用**: 复用模板结构

### 3. 工厂模式 (Factory Pattern)
- **数据生成**: 生成示例数据
- **对象创建**: 创建示例对象
- **配置驱动**: 配置驱动的创建

### 4. 策略模式 (Strategy Pattern)
- **生成策略**: 不同的数据生成策略
- **显示策略**: 不同的显示策略
- **交互策略**: 不同的用户交互策略

## 注意事项

1. **性能考虑**: 避免生成过多的示例数据
2. **用户体验**: 确保示例的直观性和有用性
3. **数据质量**: 确保示例数据的质量和真实性
4. **内存管理**: 及时清理示例数据

## 扩展建议

1. **交互功能**: 添加示例的交互功能
2. **自定义模板**: 支持用户自定义示例模板
3. **导入导出**: 支持示例配置的导入导出
4. **预览功能**: 添加实时预览功能
5. **帮助系统**: 集成到帮助系统中

该看板列示例对话框为Odoo Web客户端提供了直观的看板配置指导功能，通过可视化的示例帮助用户理解和配置看板视图，提升了用户体验和系统的易用性。

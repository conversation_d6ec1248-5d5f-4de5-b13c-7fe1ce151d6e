# Odoo 看板视图 (Kanban View) 学习资料

## 文件概述

**文件路径**: `output/@web/views/kanban/kanban_view.js`  
**原始路径**: `/web/static/src/views/kanban/kanban_view.js`  
**模块类型**: 核心视图模块 - 看板视图定义  
**代码行数**: 48 行  
**依赖关系**: 
- `@web/core/registry` - 注册表系统
- `@web/model/relational_model/relational_model` - 关系模型
- `@web/views/kanban/kanban_arch_parser` - 看板架构解析器
- `@web/views/kanban/kanban_compiler` - 看板编译器
- `@web/views/kanban/kanban_controller` - 看板控制器
- `@web/views/kanban/kanban_renderer` - 看板渲染器

## 模块功能

看板视图模块是 Odoo Web 客户端的核心视图类型之一。该模块提供了：
- 看板视图的完整定义和配置
- 看板卡片的拖拽排序功能
- 分组和列管理
- 快速创建和编辑功能
- 进度条和状态指示器
- 封面图片和颜色管理

看板视图是一种直观的项目管理和数据可视化方式，广泛应用于任务管理、销售管道、项目跟踪等场景。

## 看板视图架构

### 核心组件结构
```
Kanban View
├── KanbanArchParser - 架构解析器
│   ├── 解析看板XML架构
│   ├── 提取字段信息
│   └── 处理模板定义
├── KanbanController - 控制器
│   ├── 用户交互处理
│   ├── 拖拽逻辑管理
│   └── 快速创建功能
├── KanbanRenderer - 渲染器
│   ├── 看板列渲染
│   ├── 卡片渲染
│   └── 布局管理
├── KanbanCompiler - 编译器
│   ├── 模板编译
│   ├── 字段处理
│   └── 动态内容生成
└── RelationalModel - 数据模型
    ├── 数据加载和缓存
    ├── 分组数据管理
    └── 关系字段处理
```

### 视图定义结构
```javascript
const kanbanView = {
    type: "kanban",
    
    // 核心组件
    ArchParser: KanbanArchParser,
    Controller: KanbanController,
    Model: RelationalModel,
    Renderer: KanbanRenderer,
    Compiler: KanbanCompiler,
    
    // 按钮模板
    buttonTemplate: "web.KanbanView.Buttons",
    
    // 属性处理函数
    props: (genericProps, view) => {
        // 处理和扩展属性
        return processedProps;
    }
};
```

## 核心组件详解

### 1. 看板视图定义
```javascript
const kanbanView = {
    type: "kanban",
    
    ArchParser: KanbanArchParser,
    Controller: KanbanController,
    Model: RelationalModel,
    Renderer: KanbanRenderer,
    Compiler: KanbanCompiler,
    
    buttonTemplate: "web.KanbanView.Buttons",
    
    props: (genericProps, view) => {
        const { arch, relatedModels, resModel } = genericProps;
        const { ArchParser } = view;
        const archInfo = new ArchParser().parse(arch, relatedModels, resModel);
        const defaultGroupBy =
            genericProps.searchMenuTypes.includes("groupBy") && archInfo.defaultGroupBy;

        return {
            ...genericProps,
            Model: view.Model,
            Renderer: view.Renderer,
            buttonTemplate: view.buttonTemplate,
            archInfo,
            defaultGroupBy,
        };
    },
};
```

**功能特性**:
- **类型标识**: 明确标识为"kanban"视图类型
- **组件集成**: 集成所有必要的看板组件
- **属性处理**: 智能处理和扩展视图属性
- **架构解析**: 自动解析看板XML架构
- **默认分组**: 处理默认分组逻辑

### 2. 属性处理函数 (props)
```javascript
props: (genericProps, view) => {
    const { arch, relatedModels, resModel } = genericProps;
    const { ArchParser } = view;
    const archInfo = new ArchParser().parse(arch, relatedModels, resModel);
    const defaultGroupBy =
        genericProps.searchMenuTypes.includes("groupBy") && archInfo.defaultGroupBy;

    return {
        ...genericProps,
        Model: view.Model,
        Renderer: view.Renderer,
        buttonTemplate: view.buttonTemplate,
        archInfo,
        defaultGroupBy,
    };
}
```

**处理流程**:
1. **架构解析**: 使用ArchParser解析XML架构
2. **信息提取**: 提取架构信息和字段定义
3. **分组处理**: 确定默认分组字段
4. **属性合并**: 合并通用属性和看板特定属性
5. **组件注入**: 注入必要的组件引用

## 使用示例

### 1. 基本看板视图
```javascript
// 看板视图XML架构示例
const kanbanArch = `
    <kanban default_group_by="stage_id" 
            class="o_kanban_small_column" 
            quick_create="false">
        
        <field name="stage_id"/>
        <field name="name"/>
        <field name="user_id"/>
        <field name="priority"/>
        <field name="activity_ids"/>
        <field name="color"/>
        
        <progressbar field="activity_state" 
                     colors='{"planned": "success", "today": "warning", "overdue": "danger"}'/>
        
        <templates>
            <t t-name="kanban-box">
                <div t-attf-class="{{!selection_mode ? 'oe_kanban_color_' + kanban_getcolor(record.color.raw_value) : ''}} oe_kanban_card oe_kanban_global_click">
                    
                    <div class="o_dropdown_kanban dropdown">
                        <a class="dropdown-toggle o-no-caret btn" data-toggle="dropdown" href="#" role="button" aria-label="Dropdown menu" title="Dropdown menu">
                            <span class="fa fa-ellipsis-v"/>
                        </a>
                        <div class="dropdown-menu" role="menu">
                            <a t-if="widget.editable" type="edit" class="dropdown-item">编辑</a>
                            <a t-if="widget.deletable" type="delete" class="dropdown-item">删除</a>
                            <div class="dropdown-divider"/>
                            <div class="dropdown-item-text text-muted">颜色</div>
                            <ul class="oe_kanban_colorpicker"/>
                        </div>
                    </div>
                    
                    <div class="oe_kanban_content">
                        <div class="o_kanban_record_top">
                            <div class="o_kanban_record_headings">
                                <strong class="o_kanban_record_title">
                                    <field name="name"/>
                                </strong>
                            </div>
                            <div class="o_kanban_record_top_right">
                                <div class="o_dropdown_kanban dropdown">
                                    <a class="dropdown-toggle o-no-caret btn" data-toggle="dropdown" href="#" role="button">
                                        <span class="fa fa-ellipsis-v"/>
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="o_kanban_record_body">
                            <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>
                            
                            <div class="o_kanban_record_bottom">
                                <div class="oe_kanban_bottom_left">
                                    <field name="priority" widget="priority"/>
                                    <field name="activity_ids" widget="kanban_activity"/>
                                </div>
                                <div class="oe_kanban_bottom_right">
                                    <img t-att-src="kanban_image('res.users', 'avatar_128', record.user_id.raw_value)" 
                                         t-att-title="record.user_id.value" 
                                         t-att-alt="record.user_id.value" 
                                         class="oe_kanban_avatar"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </templates>
    </kanban>
`;

// 使用看板视图
class ProjectKanbanView extends Component {
    static template = xml`
        <div class="project-kanban">
            <KanbanView resModel="'project.task'"
                        arch="kanbanArch"
                        context="context"
                        domain="domain"
                        fields="fields"
                        searchMenuTypes="['filter', 'groupBy', 'favorite']"
                        defaultGroupBy="'stage_id'" />
        </div>
    `;
    
    setup() {
        this.kanbanArch = kanbanArch;
        this.context = {
            default_project_id: this.props.projectId,
            search_default_my_tasks: 1
        };
        this.domain = [
            ['project_id', '=', this.props.projectId]
        ];
        this.fields = {
            name: { type: 'char', string: '任务名称' },
            stage_id: { type: 'many2one', relation: 'project.task.type', string: '阶段' },
            user_id: { type: 'many2one', relation: 'res.users', string: '负责人' },
            priority: { type: 'selection', selection: [['0', '低'], ['1', '高']], string: '优先级' },
            tag_ids: { type: 'many2many', relation: 'project.tags', string: '标签' },
            activity_ids: { type: 'one2many', relation: 'mail.activity', string: '活动' },
            color: { type: 'integer', string: '颜色' }
        };
    }
}
```

### 2. 高级看板视图配置
```javascript
class AdvancedKanbanView extends Component {
    static template = xml`
        <div class="advanced-kanban">
            <div class="kanban-controls">
                <div class="kanban-filters">
                    <select t-model="selectedFilter" t-on-change="applyFilter">
                        <option value="">所有任务</option>
                        <option value="my">我的任务</option>
                        <option value="urgent">紧急任务</option>
                        <option value="overdue">逾期任务</option>
                    </select>
                </div>
                
                <div class="kanban-actions">
                    <button t-on-click="createTask" class="btn btn-primary">
                        <i class="fa fa-plus" /> 新建任务
                    </button>
                    
                    <button t-on-click="toggleCompactMode" class="btn btn-secondary">
                        <i t-att-class="compactMode ? 'fa fa-expand' : 'fa fa-compress'" />
                        <span t-esc="compactMode ? '展开' : '紧凑'" />
                    </button>
                </div>
            </div>
            
            <KanbanView resModel="'project.task'"
                        arch="currentArch"
                        context="currentContext"
                        domain="currentDomain"
                        fields="fields"
                        className="kanbanClassName"
                        onRecordClick="onTaskClick"
                        onRecordDrop="onTaskDrop"
                        onColumnCreate="onStageCreate" />
        </div>
    `;
    
    setup() {
        this.selectedFilter = useState('');
        this.compactMode = useState(false);
        
        this.baseArch = this.generateKanbanArch();
        this.fields = this.getTaskFields();
        
        onWillStart(() => {
            this.updateView();
        });
    }
    
    get currentArch() {
        return this.compactMode ? this.getCompactArch() : this.baseArch;
    }
    
    get currentContext() {
        const context = {
            default_project_id: this.props.projectId
        };
        
        if (this.selectedFilter === 'my') {
            context.search_default_my_tasks = 1;
        } else if (this.selectedFilter === 'urgent') {
            context.search_default_urgent = 1;
        }
        
        return context;
    }
    
    get currentDomain() {
        let domain = [['project_id', '=', this.props.projectId]];
        
        if (this.selectedFilter === 'overdue') {
            domain.push(['date_deadline', '<', new Date().toISOString().split('T')[0]]);
        }
        
        return domain;
    }
    
    get kanbanClassName() {
        const classes = ['project-kanban'];
        
        if (this.compactMode) {
            classes.push('compact-mode');
        }
        
        return classes.join(' ');
    }
    
    generateKanbanArch() {
        return `
            <kanban default_group_by="stage_id" 
                    class="o_kanban_small_column" 
                    quick_create="true"
                    quick_create_view="project.quick_create_task_form">
                
                <field name="stage_id"/>
                <field name="name"/>
                <field name="user_id"/>
                <field name="priority"/>
                <field name="date_deadline"/>
                <field name="tag_ids"/>
                <field name="color"/>
                <field name="activity_state"/>
                
                <progressbar field="activity_state" 
                             colors='{"planned": "success", "today": "warning", "overdue": "danger"}'
                             sum_field="planned_hours"/>
                
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_color_{{kanban_getcolor(record.color.raw_value)}} oe_kanban_card oe_kanban_global_click">
                            ${this.generateCardTemplate()}
                        </div>
                    </t>
                </templates>
            </kanban>
        `;
    }
    
    generateCardTemplate() {
        return `
            <div class="o_dropdown_kanban dropdown">
                <a class="dropdown-toggle o-no-caret btn" data-toggle="dropdown" href="#" role="button">
                    <span class="fa fa-ellipsis-v"/>
                </a>
                <div class="dropdown-menu" role="menu">
                    <a type="edit" class="dropdown-item">编辑</a>
                    <a type="delete" class="dropdown-item">删除</a>
                    <div class="dropdown-divider"/>
                    <a name="action_set_high_priority" type="object" class="dropdown-item">设为高优先级</a>
                    <a name="action_assign_to_me" type="object" class="dropdown-item">分配给我</a>
                </div>
            </div>
            
            <div class="oe_kanban_content">
                <div class="o_kanban_record_top">
                    <div class="o_kanban_record_headings">
                        <strong class="o_kanban_record_title">
                            <field name="name"/>
                        </strong>
                        <div class="o_kanban_record_subtitle text-muted">
                            <field name="project_id"/>
                        </div>
                    </div>
                </div>
                
                <div class="o_kanban_record_body">
                    <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>
                    
                    <div class="o_kanban_record_bottom">
                        <div class="oe_kanban_bottom_left">
                            <field name="priority" widget="priority"/>
                            <field name="activity_ids" widget="kanban_activity"/>
                            <field name="date_deadline" widget="date" options="{'warn_future': true}"/>
                        </div>
                        <div class="oe_kanban_bottom_right">
                            <field name="user_id" widget="many2one_avatar_user"/>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    getCompactArch() {
        // 紧凑模式的架构
        return this.baseArch.replace(
            'class="o_kanban_small_column"',
            'class="o_kanban_small_column o_kanban_compact"'
        );
    }
    
    getTaskFields() {
        return {
            name: { type: 'char', string: '任务名称', required: true },
            stage_id: { type: 'many2one', relation: 'project.task.type', string: '阶段' },
            project_id: { type: 'many2one', relation: 'project.project', string: '项目' },
            user_id: { type: 'many2one', relation: 'res.users', string: '负责人' },
            priority: { 
                type: 'selection', 
                selection: [['0', '正常'], ['1', '高']], 
                string: '优先级' 
            },
            date_deadline: { type: 'date', string: '截止日期' },
            tag_ids: { type: 'many2many', relation: 'project.tags', string: '标签' },
            activity_ids: { type: 'one2many', relation: 'mail.activity', string: '活动' },
            activity_state: { 
                type: 'selection',
                selection: [['overdue', '逾期'], ['today', '今天'], ['planned', '计划']],
                string: '活动状态'
            },
            color: { type: 'integer', string: '颜色' },
            planned_hours: { type: 'float', string: '计划工时' }
        };
    }
    
    applyFilter() {
        this.updateView();
    }
    
    toggleCompactMode() {
        this.compactMode = !this.compactMode;
    }
    
    updateView() {
        // 触发视图更新
        this.render();
    }
    
    createTask() {
        // 创建新任务
        this.env.services.action.doAction({
            type: 'ir.actions.act_window',
            res_model: 'project.task',
            view_mode: 'form',
            views: [[false, 'form']],
            target: 'new',
            context: {
                default_project_id: this.props.projectId,
                default_stage_id: this.getFirstStageId()
            }
        });
    }
    
    onTaskClick(record) {
        // 处理任务点击
        this.env.services.action.doAction({
            type: 'ir.actions.act_window',
            res_model: 'project.task',
            res_id: record.id,
            view_mode: 'form',
            views: [[false, 'form']],
            target: 'current'
        });
    }
    
    onTaskDrop(record, targetStage) {
        // 处理任务拖拽
        this.env.services.orm.write('project.task', [record.id], {
            stage_id: targetStage.id
        });
    }
    
    onStageCreate(stageName) {
        // 处理新阶段创建
        this.env.services.orm.create('project.task.type', {
            name: stageName,
            project_ids: [[4, this.props.projectId]]
        });
    }
    
    getFirstStageId() {
        // 获取第一个阶段ID
        return 1; // 简化实现
    }
}
```

## 最佳实践

### 1. 看板配置
```javascript
// ✅ 推荐：合理的看板配置
<kanban default_group_by="stage_id"
        class="o_kanban_small_column"
        quick_create="true"
        records_draggable="true"
        group_create="true"
        group_delete="true"
        group_edit="true">
```

### 2. 性能优化
```javascript
// ✅ 推荐：限制记录数量
<kanban limit="20" count_limit="100">
    <!-- 看板内容 -->
</kanban>
```

### 3. 用户体验
```javascript
// ✅ 推荐：提供丰富的视觉反馈
<templates>
    <t t-name="kanban-box">
        <div t-attf-class="oe_kanban_card {{record.priority.raw_value == '1' ? 'border-warning' : ''}}">
            <!-- 卡片内容 -->
        </div>
    </t>
</templates>
```

## 总结

Odoo 看板视图模块提供了强大的可视化管理功能：

**核心优势**:
- **直观可视**: 直观的卡片式数据展示
- **拖拽操作**: 流畅的拖拽排序功能
- **快速操作**: 便捷的快速创建和编辑
- **状态管理**: 清晰的状态流转管理
- **数据分析**: 内置的数据分析和指标

**适用场景**:
- 项目管理和任务跟踪
- 销售管道管理
- 客户关系管理
- 库存状态监控
- 工作流程管理

**设计优势**:
- 组件化架构
- 模板驱动
- 高度可定制
- 响应式设计

这个看板视图为 Odoo Web 客户端提供了强大的可视化管理能力，是现代项目管理和业务流程的重要工具。

# KanbanRenderer - 看板视图渲染器

## 概述

`kanban_renderer.js` 是 Odoo Web 客户端看板视图的渲染器组件，负责渲染看板视图的用户界面。该模块包含601行代码，是一个OWL组件，专门用于渲染看板布局、分组、记录卡片等，具备拖拽排序、快速创建、进度条、分组管理、响应式布局等特性，是看板视图系统中界面渲染的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/kanban/kanban_renderer.js`
- **行数**: 601
- **模块**: `@web/views/kanban/kanban_renderer`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'                           // 翻译服务
'@web/core/dropdown/dropdown'                          // 下拉菜单组件
'@web/core/dropdown/dropdown_item'                     // 下拉菜单项组件
'@web/core/hotkeys/hotkey_hook'                        // 热键钩子
'@web/core/registry'                                   // 注册表服务
'@web/core/utils/hooks'                                // 工具钩子
'@web/core/utils/sortable_owl'                         // 排序工具
'@web/views/utils'                                     // 视图工具
'@web/views/view_components/column_progress'           // 列进度组件
'@web/views/view_hook'                                 // 视图钩子
'@web/views/kanban/kanban_column_quick_create'         // 看板列快速创建
'@web/views/kanban/kanban_header'                      // 看板头部
'@web/views/kanban/kanban_record'                      // 看板记录
'@web/views/kanban/kanban_record_quick_create'         // 看板记录快速创建
'@web/core/confirmation_dialog/confirmation_dialog'    // 确认对话框
'@odoo/owl'                                            // OWL框架
'@web/core/py_js/py'                                   // Python表达式
```

## 核心常量

### 1. 拖拽类型常量

```javascript
const DRAGGABLE_GROUP_TYPES = ["many2one"];
const MOVABLE_RECORD_TYPES = ["char", "boolean", "integer", "selection", "many2one"];
```

**常量说明**:
- **可拖拽分组**: 定义支持拖拽的分组字段类型
- **可移动记录**: 定义支持移动的记录字段类型
- **类型限制**: 限制拖拽功能的字段类型
- **用户体验**: 确保拖拽功能的合理性

### 2. 验证函数

```javascript
function validateColumnQuickCreateExamples(data) {
    const { allowedGroupBys = [], examples = [], foldField = "" } = data;
    if (!allowedGroupBys.length) {
        throw new Error("The example data must contain an array of allowed groupbys");
    }
    if (!examples.length) {
        throw new Error("The example data must contain an array of examples");
    }
    const someHasFoldedColumns = examples.some(({ foldedColumns = [] }) => foldedColumns.length);
    if (!foldField && someHasFoldedColumns) {
        throw new Error("The example data must contain a fold field if there are folded columns");
    }
}
```

**验证功能**:
- **数据验证**: 验证列快速创建的示例数据
- **必需字段**: 检查必需的字段和数组
- **折叠字段**: 验证折叠列的字段配置
- **错误处理**: 提供详细的错误信息

## 主要组件定义

### 1. KanbanRenderer - 看板视图渲染器

```javascript
class KanbanRenderer extends Component {
    static template = "web.KanbanRenderer";
    static components = {
        Dropdown,
        DropdownItem,
        ColumnProgress,
        KanbanColumnQuickCreate,
        KanbanHeader,
        KanbanRecord,
        KanbanRecordQuickCreate,
    };
    static props = {
        list: Object,
        openRecord: { type: Function, optional: true },
        archInfo: Object,
        readonly: { type: Boolean, optional: true },
        forceGlobalClick: { type: Boolean, optional: true },
        onSelectionChanged: { type: Function, optional: true },
        showButtons: { type: Boolean, optional: true },
        class: { type: String, optional: true },
    };

    setup() {
        this.dialog = useService("dialog");
        this.notification = useService("notification");
        this.orm = useService("orm");
        this.uiService = useService("ui");
        
        this.bounceButton = useBounceButton();
        
        this.state = useState({
            showColumnQuickCreate: false,
            quickCreateGroupId: null,
            selectedRecords: new Set(),
        });
        
        this.rootRef = useRef("root");
        this.columnRefs = useRef("columns");
        
        this.setupSortable();
        this.setupHotkeys();
        
        useBus(this.props.list, "update", this.render);
        
        onWillPatch(this.onWillPatch);
        onPatched(this.onPatched);
        onWillDestroy(this.onWillDestroy);
    }
}
```

**组件特性**:
- **专用模板**: 使用KanbanRenderer专用模板
- **子组件**: 集成多种看板相关组件
- **服务集成**: 集成对话框、通知、ORM等服务
- **状态管理**: 管理渲染器的各种状态

## 核心功能

### 1. 排序功能设置

```javascript
setupSortable() {
    // 设置记录排序
    this.recordSortable = useSortable({
        ref: this.rootRef,
        elements: ".o_kanban_record",
        groups: ".o_kanban_group",
        connectGroups: true,
        cursor: "grabbing",
        delay: 100,
        tolerance: "pointer",
        onDragStart: this.onRecordDragStart.bind(this),
        onDragEnd: this.onRecordDragEnd.bind(this),
        onDrop: this.onRecordDrop.bind(this),
    });
    
    // 设置分组排序
    if (this.canDragGroups) {
        this.groupSortable = useSortable({
            ref: this.rootRef,
            elements: ".o_kanban_group",
            cursor: "grabbing",
            delay: 100,
            tolerance: "pointer",
            onDragStart: this.onGroupDragStart.bind(this),
            onDragEnd: this.onGroupDragEnd.bind(this),
            onDrop: this.onGroupDrop.bind(this),
        });
    }
}

get canDragGroups() {
    const { archInfo } = this.props;
    return archInfo.groupsDraggable && this.isGroupByDraggable();
}

isGroupByDraggable() {
    const groupByField = this.props.list.groupByField;
    if (!groupByField) return false;
    
    const field = this.props.list.fields[groupByField];
    return field && DRAGGABLE_GROUP_TYPES.includes(field.type);
}

get canDragRecords() {
    const { archInfo } = this.props;
    return archInfo.recordsDraggable && this.isRecordMovable();
}

isRecordMovable() {
    const handleField = this.props.list.handleField;
    if (!handleField) return false;
    
    const field = this.props.list.fields[handleField];
    return field && MOVABLE_RECORD_TYPES.includes(field.type);
}
```

**排序功能**:
- **记录排序**: 支持记录在分组间的拖拽排序
- **分组排序**: 支持分组的拖拽重排序
- **类型检查**: 检查字段类型是否支持拖拽
- **配置控制**: 通过架构配置控制拖拽功能

### 2. 热键设置

```javascript
setupHotkeys() {
    // 创建记录热键
    useHotkey("c", () => {
        if (this.canCreateRecord()) {
            this.onCreateRecord();
        }
    }, { allowRepeat: false });
    
    // 删除记录热键
    useHotkey("Delete", () => {
        if (this.state.selectedRecords.size > 0) {
            this.onDeleteSelectedRecords();
        }
    }, { allowRepeat: false });
    
    // 全选热键
    useHotkey("ctrl+a", (ev) => {
        ev.preventDefault();
        this.onSelectAll();
    }, { allowRepeat: false });
    
    // 取消选择热键
    useHotkey("Escape", () => {
        if (this.state.selectedRecords.size > 0) {
            this.onClearSelection();
        }
    }, { allowRepeat: false });
}

canCreateRecord() {
    return !this.props.readonly && this.props.archInfo.canCreate;
}

onCreateRecord() {
    if (this.props.list.isGrouped) {
        // 在分组视图中显示快速创建
        this.showQuickCreate();
    } else {
        // 在非分组视图中打开表单
        this.openCreateForm();
    }
}

showQuickCreate(groupId = null) {
    this.state.showColumnQuickCreate = true;
    this.state.quickCreateGroupId = groupId;
}

openCreateForm() {
    this.trigger("create-record");
}
```

**热键功能**:
- **创建记录**: 使用C键快速创建记录
- **删除记录**: 使用Delete键删除选中记录
- **全选功能**: 使用Ctrl+A全选记录
- **取消选择**: 使用Escape键取消选择

### 3. 记录选择

```javascript
onRecordSelect(record, selected) {
    if (selected) {
        this.state.selectedRecords.add(record.id);
    } else {
        this.state.selectedRecords.delete(record.id);
    }
    
    // 通知选择变化
    if (this.props.onSelectionChanged) {
        this.props.onSelectionChanged(Array.from(this.state.selectedRecords));
    }
}

onSelectAll() {
    const allRecords = this.getAllVisibleRecords();
    for (const record of allRecords) {
        this.state.selectedRecords.add(record.id);
    }
    
    if (this.props.onSelectionChanged) {
        this.props.onSelectionChanged(Array.from(this.state.selectedRecords));
    }
}

onClearSelection() {
    this.state.selectedRecords.clear();
    
    if (this.props.onSelectionChanged) {
        this.props.onSelectionChanged([]);
    }
}

getAllVisibleRecords() {
    const records = [];
    
    if (this.props.list.isGrouped) {
        for (const group of this.props.list.groups) {
            if (!group.isFolded) {
                records.push(...group.records);
            }
        }
    } else {
        records.push(...this.props.list.records);
    }
    
    return records;
}

onDeleteSelectedRecords() {
    const selectedIds = Array.from(this.state.selectedRecords);
    if (selectedIds.length === 0) return;
    
    const message = selectedIds.length === 1 
        ? _t("Are you sure you want to delete this record?")
        : _t("Are you sure you want to delete these %s records?", selectedIds.length);
    
    this.dialog.add(ConfirmationDialog, {
        body: message,
        confirm: async () => {
            await this.deleteRecords(selectedIds);
            this.onClearSelection();
        },
        cancel: () => {},
    });
}

async deleteRecords(recordIds) {
    try {
        await this.orm.unlink(this.props.list.resModel, recordIds);
        this.notification.add(_t("Records deleted"), { type: "success" });
    } catch (error) {
        this.notification.add(_t("Failed to delete records"), { type: "danger" });
        throw error;
    }
}
```

**选择功能**:
- **单选记录**: 支持单个记录的选择
- **多选记录**: 支持多个记录的选择
- **全选功能**: 支持全选所有可见记录
- **批量删除**: 支持批量删除选中记录

### 4. 拖拽处理

```javascript
onRecordDragStart(params) {
    const { element, group } = params;
    const recordId = element.dataset.id;
    
    // 添加拖拽样式
    element.classList.add("o_kanban_record_dragging");
    
    // 存储拖拽信息
    this.dragInfo = {
        type: "record",
        recordId: recordId,
        sourceGroupId: group?.dataset.groupId,
    };
}

onRecordDragEnd(params) {
    const { element } = params;
    
    // 移除拖拽样式
    element.classList.remove("o_kanban_record_dragging");
    
    // 清理拖拽信息
    this.dragInfo = null;
}

async onRecordDrop(params) {
    const { element, group, index } = params;
    
    if (!this.dragInfo || this.dragInfo.type !== "record") {
        return;
    }
    
    const recordId = this.dragInfo.recordId;
    const sourceGroupId = this.dragInfo.sourceGroupId;
    const targetGroupId = group?.dataset.groupId;
    
    // 检查是否真的移动了
    if (sourceGroupId === targetGroupId) {
        // 同组内重排序
        await this.reorderRecord(recordId, targetGroupId, index);
    } else {
        // 跨组移动
        await this.moveRecord(recordId, sourceGroupId, targetGroupId, index);
    }
}

async moveRecord(recordId, sourceGroupId, targetGroupId, index) {
    const record = this.findRecord(recordId);
    const targetGroup = this.findGroup(targetGroupId);
    
    if (!record || !targetGroup) {
        return;
    }
    
    // 更新分组字段
    const groupByField = this.props.list.groupByField;
    const updates = {
        [groupByField]: targetGroup.value,
    };
    
    // 更新序列字段
    const handleField = this.props.list.handleField;
    if (handleField) {
        const newSequence = this.calculateSequence(targetGroup, index);
        updates[handleField] = newSequence;
    }
    
    try {
        await record.update(updates);
        this.notification.add(_t("Record moved"), { type: "success" });
    } catch (error) {
        this.notification.add(_t("Failed to move record"), { type: "danger" });
        throw error;
    }
}

async reorderRecord(recordId, groupId, index) {
    const record = this.findRecord(recordId);
    const group = this.findGroup(groupId);
    const handleField = this.props.list.handleField;
    
    if (!record || !group || !handleField) {
        return;
    }
    
    const newSequence = this.calculateSequence(group, index);
    
    try {
        await record.update({ [handleField]: newSequence });
        this.notification.add(_t("Record reordered"), { type: "success" });
    } catch (error) {
        this.notification.add(_t("Failed to reorder record"), { type: "danger" });
        throw error;
    }
}

calculateSequence(group, index) {
    const records = group.records;
    
    if (index === 0) {
        // 移动到开头
        return records.length > 0 ? records[0].data.sequence - 1 : 1;
    } else if (index >= records.length) {
        // 移动到末尾
        return records.length > 0 ? records[records.length - 1].data.sequence + 1 : 1;
    } else {
        // 移动到中间
        const prevRecord = records[index - 1];
        const nextRecord = records[index];
        return (prevRecord.data.sequence + nextRecord.data.sequence) / 2;
    }
}
```

**拖拽处理功能**:
- **拖拽开始**: 处理拖拽开始事件
- **拖拽结束**: 处理拖拽结束事件
- **记录移动**: 处理记录的跨组移动
- **记录重排**: 处理同组内的记录重排序
- **序列计算**: 智能计算新的序列号

### 5. 分组管理

```javascript
onGroupDragStart(params) {
    const { element } = params;
    const groupId = element.dataset.groupId;
    
    // 添加拖拽样式
    element.classList.add("o_kanban_group_dragging");
    
    // 存储拖拽信息
    this.dragInfo = {
        type: "group",
        groupId: groupId,
    };
}

onGroupDragEnd(params) {
    const { element } = params;
    
    // 移除拖拽样式
    element.classList.remove("o_kanban_group_dragging");
    
    // 清理拖拽信息
    this.dragInfo = null;
}

async onGroupDrop(params) {
    const { element, index } = params;
    
    if (!this.dragInfo || this.dragInfo.type !== "group") {
        return;
    }
    
    const groupId = this.dragInfo.groupId;
    
    try {
        await this.reorderGroup(groupId, index);
        this.notification.add(_t("Group moved"), { type: "success" });
    } catch (error) {
        this.notification.add(_t("Failed to move group"), { type: "danger" });
        throw error;
    }
}

async reorderGroup(groupId, newIndex) {
    const groups = this.props.list.groups;
    const currentIndex = groups.findIndex(g => g.id === groupId);
    
    if (currentIndex === -1 || currentIndex === newIndex) {
        return;
    }
    
    // 重新排序分组
    const group = groups[currentIndex];
    groups.splice(currentIndex, 1);
    groups.splice(newIndex, 0, group);
    
    // 更新分组序列
    await this.updateGroupSequences(groups);
}

async updateGroupSequences(groups) {
    const updates = [];
    
    for (let i = 0; i < groups.length; i++) {
        const group = groups[i];
        updates.push({
            id: group.value,
            sequence: i + 1,
        });
    }
    
    if (updates.length > 0) {
        const groupByField = this.props.list.groupByField;
        const model = this.props.list.fields[groupByField].relation;
        
        await this.orm.write(model, updates.map(u => u.id), { sequence: updates.map(u => u.sequence) });
    }
}

onToggleGroup(group) {
    if (group.isFolded) {
        group.unfold();
    } else {
        group.fold();
    }
}

onArchiveGroup(group) {
    this.dialog.add(ConfirmationDialog, {
        body: _t("Are you sure you want to archive this group and all its records?"),
        confirm: async () => {
            await group.archive();
            this.notification.add(_t("Group archived"), { type: "success" });
        },
        cancel: () => {},
    });
}

onDeleteGroup(group) {
    this.dialog.add(ConfirmationDialog, {
        body: _t("Are you sure you want to delete this group and all its records?"),
        confirm: async () => {
            await group.delete();
            this.notification.add(_t("Group deleted"), { type: "success" });
        },
        cancel: () => {},
    });
}
```

**分组管理功能**:
- **分组拖拽**: 支持分组的拖拽重排序
- **分组折叠**: 支持分组的折叠和展开
- **分组归档**: 支持分组的归档操作
- **分组删除**: 支持分组的删除操作
- **序列更新**: 自动更新分组的序列号

## 技术特点

### 1. 响应式设计
- **自适应布局**: 根据屏幕尺寸调整布局
- **移动端优化**: 优化移动端的交互体验
- **触摸支持**: 支持触摸设备的拖拽操作
- **断点管理**: 管理不同屏幕尺寸的断点

### 2. 拖拽系统
- **多类型拖拽**: 支持记录和分组的拖拽
- **跨组移动**: 支持记录在分组间移动
- **序列管理**: 智能管理拖拽后的序列
- **视觉反馈**: 提供拖拽过程的视觉反馈

### 3. 性能优化
- **虚拟滚动**: 支持大量记录的虚拟滚动
- **懒加载**: 按需加载记录和分组
- **缓存机制**: 缓存渲染结果
- **防抖处理**: 防抖的事件处理

### 4. 用户体验
- **快速创建**: 支持记录的快速创建
- **批量操作**: 支持记录的批量操作
- **热键支持**: 支持键盘快捷键
- **状态保持**: 保持用户的操作状态

## 设计模式

### 1. 组件模式 (Component Pattern)
- **组件化**: 高度组件化的设计
- **可复用**: 组件的可复用性
- **组合**: 组件的组合使用

### 2. 观察者模式 (Observer Pattern)
- **状态监听**: 监听数据状态变化
- **自动更新**: 自动更新界面
- **事件响应**: 响应用户交互事件

### 3. 策略模式 (Strategy Pattern)
- **渲染策略**: 不同的渲染策略
- **拖拽策略**: 不同的拖拽策略
- **布局策略**: 不同的布局策略

### 4. 命令模式 (Command Pattern)
- **操作封装**: 封装用户操作
- **撤销重做**: 支持操作的撤销重做
- **批量执行**: 支持批量操作执行

## 注意事项

1. **性能考虑**: 避免频繁的重渲染
2. **内存管理**: 及时清理事件监听器和引用
3. **拖拽兼容**: 确保拖拽功能的跨浏览器兼容性
4. **可访问性**: 确保组件的可访问性

## 扩展建议

1. **虚拟化**: 添加虚拟滚动支持
2. **动画增强**: 增强拖拽和状态变化的动画
3. **自定义布局**: 支持更灵活的自定义布局
4. **性能监控**: 添加详细的性能监控
5. **移动优化**: 进一步优化移动端体验

该看板渲染器为Odoo Web客户端提供了强大的看板界面渲染功能，通过拖拽排序、快速创建、响应式设计和性能优化确保了优秀的用户体验和系统性能。

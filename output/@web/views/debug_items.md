# Odoo 视图调试工具 (View Debug Items) 学习资料

## 文件概述

**文件路径**: `output/@web/views/debug_items.js`  
**原始路径**: `/web/static/src/views/debug_items.js`  
**模块类型**: 核心视图模块 - 调试工具集  
**代码行数**: 400 行  
**依赖关系**: 
- `@web/core/l10n/translation` - 国际化翻译 (_t)
- `@web/core/dialog/dialog` - 对话框组件 (Dialog)
- `@web/core/py_js/py` - Python表达式评估 (evaluateBooleanExpr)
- `@web/core/debug/debug_utils` - 调试工具 (editModelDebug)
- `@web/core/l10n/dates` - 日期处理
- `@web/core/registry` - 注册表系统 (registry)
- `@web/views/fields/formatters` - 字段格式化器 (formatMany2one)
- `@web/views/view_dialogs/form_view_dialog` - 表单视图对话框 (FormViewDialog)
- `@odoo/owl` - OWL框架

## 模块功能

视图调试工具模块是 Odoo Web 客户端的开发者工具集。该模块提供了：
- 视图架构查看和编辑
- 记录元数据管理
- 原始数据查看
- 默认值设置
- 附件管理
- 调试信息展示

这个模块为开发者提供了强大的调试和开发工具，帮助理解和调试视图行为、数据结构和系统配置。

## 调试工具架构

### 调试工具分类
```
Debug Items
├── 视图相关工具
│   ├── getView - 查看计算后的架构
│   ├── editView - 编辑视图
│   └── editSearchView - 编辑搜索视图
├── 记录相关工具
│   ├── viewMetadata - 查看元数据
│   ├── viewRawRecord - 查看原始数据
│   ├── setDefaults - 设置默认值
│   └── manageAttachments - 管理附件
└── 调试注册系统
    ├── debugRegistry - 调试注册表
    ├── 权限检查
    └── 上下文管理
```

### 调试工具注册机制
```javascript
const debugRegistry = registry.category("debug");

// 视图类别注册
debugRegistry.category("view").add("getView", getView);
debugRegistry.category("view").add("editView", editView);

// 表单类别注册
debugRegistry.category("form").add("viewMetadata", viewMetadata);
debugRegistry.category("form").add("setDefaults", setDefaults);
```

## 核心调试工具详解

### 1. getView() - 查看计算后的架构
```javascript
function getView({ component, env }) {
    return {
        type: "item",
        description: _t("Computed Arch"),
        callback: () => {
            env.services.dialog.add(GetViewDialog, { 
                arch: component.env.config.rawArch 
            });
        },
        sequence: 270,
        section: "ui",
    };
}
```

**功能特性**:
- **架构展示**: 显示经过计算和处理的视图架构
- **对话框界面**: 使用专门的对话框展示架构内容
- **开发调试**: 帮助开发者理解视图的最终结构
- **XML格式**: 以XML格式展示架构信息
- **实时查看**: 反映当前视图的实际架构状态

**使用示例**:
```javascript
// 自定义架构查看器
class AdvancedArchViewer extends Component {
    static template = xml`
        <div class="arch-viewer">
            <div class="arch-toolbar">
                <button t-on-click="viewComputedArch" class="btn btn-primary">
                    <i class="fa fa-code" /> 查看计算架构
                </button>
                
                <button t-on-click="viewRawArch" class="btn btn-secondary">
                    <i class="fa fa-file-code-o" /> 查看原始架构
                </button>
                
                <button t-on-click="compareArchs" class="btn btn-info">
                    <i class="fa fa-exchange" /> 对比架构
                </button>
                
                <button t-on-click="validateArch" class="btn btn-warning">
                    <i class="fa fa-check" /> 验证架构
                </button>
            </div>
            
            <div class="arch-content">
                <div t-if="showComputedArch" class="computed-arch">
                    <h4>计算后的架构</h4>
                    <pre t-esc="computedArch" />
                </div>
                
                <div t-if="showRawArch" class="raw-arch">
                    <h4>原始架构</h4>
                    <pre t-esc="rawArch" />
                </div>
                
                <div t-if="showComparison" class="arch-comparison">
                    <h4>架构对比</h4>
                    <div class="comparison-result" t-raw="comparisonResult" />
                </div>
                
                <div t-if="showValidation" class="arch-validation">
                    <h4>架构验证</h4>
                    <div t-foreach="validationResults" t-as="result" t-key="result_index"
                         t-att-class="'validation-item ' + result.type">
                        <i t-att-class="getValidationIcon(result.type)" />
                        <span t-esc="result.message" />
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setup() {
        this.showComputedArch = useState(false);
        this.showRawArch = useState(false);
        this.showComparison = useState(false);
        this.showValidation = useState(false);
        
        this.computedArch = '';
        this.rawArch = '';
        this.comparisonResult = '';
        this.validationResults = useState([]);
    }
    
    viewComputedArch() {
        this.computedArch = this.formatXML(this.env.config.rawArch);
        this.showComputedArch = true;
        this.hideOtherViews('computed');
    }
    
    viewRawArch() {
        this.rawArch = this.formatXML(this.props.originalArch);
        this.showRawArch = true;
        this.hideOtherViews('raw');
    }
    
    compareArchs() {
        const computed = this.env.config.rawArch;
        const raw = this.props.originalArch;
        
        this.comparisonResult = this.generateComparison(raw, computed);
        this.showComparison = true;
        this.hideOtherViews('comparison');
    }
    
    validateArch() {
        const arch = this.env.config.rawArch;
        this.validationResults = this.performValidation(arch);
        this.showValidation = true;
        this.hideOtherViews('validation');
    }
    
    hideOtherViews(except) {
        this.showComputedArch = except === 'computed';
        this.showRawArch = except === 'raw';
        this.showComparison = except === 'comparison';
        this.showValidation = except === 'validation';
    }
    
    formatXML(xmlString) {
        try {
            const parser = new DOMParser();
            const doc = parser.parseFromString(xmlString, 'text/xml');
            const serializer = new XMLSerializer();
            const formatted = serializer.serializeToString(doc);
            
            // 简单的格式化
            return formatted
                .replace(/></g, '>\n<')
                .replace(/^\s*\n/gm, '')
                .split('\n')
                .map((line, index) => {
                    const indent = '  '.repeat(this.getIndentLevel(line));
                    return indent + line.trim();
                })
                .join('\n');
        } catch (error) {
            return xmlString;
        }
    }
    
    getIndentLevel(line) {
        const trimmed = line.trim();
        if (trimmed.startsWith('</')) return 0;
        if (trimmed.includes('</')) return 1;
        return 1;
    }
    
    generateComparison(raw, computed) {
        const differences = this.findDifferences(raw, computed);
        
        if (differences.length === 0) {
            return '<div class="alert alert-success">架构完全一致</div>';
        }
        
        let html = '<div class="differences">';
        differences.forEach(diff => {
            html += `
                <div class="difference-item ${diff.type}">
                    <strong>${diff.type}:</strong> ${diff.description}
                    <div class="diff-details">
                        <div class="before">原始: ${diff.before}</div>
                        <div class="after">计算后: ${diff.after}</div>
                    </div>
                </div>
            `;
        });
        html += '</div>';
        
        return html;
    }
    
    findDifferences(raw, computed) {
        // 简化的差异检测
        const differences = [];
        
        if (raw.length !== computed.length) {
            differences.push({
                type: 'length',
                description: '架构长度不同',
                before: raw.length,
                after: computed.length
            });
        }
        
        // 可以添加更复杂的差异检测逻辑
        
        return differences;
    }
    
    performValidation(arch) {
        const results = [];
        
        try {
            const parser = new DOMParser();
            const doc = parser.parseFromString(arch, 'text/xml');
            
            // 检查XML格式
            const parseError = doc.querySelector('parsererror');
            if (parseError) {
                results.push({
                    type: 'error',
                    message: 'XML格式错误: ' + parseError.textContent
                });
            } else {
                results.push({
                    type: 'success',
                    message: 'XML格式正确'
                });
            }
            
            // 检查必需的属性
            const root = doc.documentElement;
            if (!root.hasAttribute('string')) {
                results.push({
                    type: 'warning',
                    message: '缺少string属性'
                });
            }
            
            // 检查字段定义
            const fields = doc.querySelectorAll('field');
            fields.forEach((field, index) => {
                if (!field.hasAttribute('name')) {
                    results.push({
                        type: 'error',
                        message: `第${index + 1}个字段缺少name属性`
                    });
                }
            });
            
            // 检查按钮定义
            const buttons = doc.querySelectorAll('button');
            buttons.forEach((button, index) => {
                if (!button.hasAttribute('name') && !button.hasAttribute('special')) {
                    results.push({
                        type: 'warning',
                        message: `第${index + 1}个按钮缺少name或special属性`
                    });
                }
            });
            
        } catch (error) {
            results.push({
                type: 'error',
                message: '验证过程中发生错误: ' + error.message
            });
        }
        
        return results;
    }
    
    getValidationIcon(type) {
        const icons = {
            success: 'fa fa-check text-success',
            warning: 'fa fa-exclamation-triangle text-warning',
            error: 'fa fa-times text-danger',
            info: 'fa fa-info-circle text-info'
        };
        return icons[type] || 'fa fa-question';
    }
}
```

### 2. editView() - 编辑视图
```javascript
function editView({ accessRights, component, env }) {
    if (!accessRights.canEditView) {
        return null;
    }
    const { viewId, viewType: type } = component.env.config;
    const displayName = type[0].toUpperCase() + type.slice(1);
    const description = _t("View: %(displayName)s", { displayName });
    
    return {
        type: "item",
        description,
        callback: () => {
            editModelDebug(env, description, "ir.ui.view", viewId);
        },
        sequence: 240,
        section: "ui",
    };
}
```

**功能特性**:
- **权限检查**: 检查用户是否有编辑视图的权限
- **视图编辑**: 直接编辑视图定义
- **类型识别**: 自动识别视图类型
- **调试集成**: 集成到调试工具中
- **安全控制**: 确保只有授权用户可以编辑

### 3. viewMetadata() - 查看元数据
```javascript
function viewMetadata({ component, env }) {
    const resId = component.model.root.resId;
    if (!resId) {
        return null; // No record
    }
    return {
        type: "item",
        description: _t("Metadata"),
        callback: () => {
            env.services.dialog.add(GetMetadataDialog, {
                resModel: component.props.resModel,
                resId,
            });
        },
        sequence: 110,
        section: "record",
    };
}
```

**功能特性**:
- **元数据展示**: 显示记录的完整元数据信息
- **创建信息**: 显示创建者和创建时间
- **修改信息**: 显示最后修改者和修改时间
- **XML ID管理**: 管理记录的XML ID
- **更新控制**: 控制记录的更新行为

**使用示例**:
```javascript
// 高级元数据管理器
class AdvancedMetadataManager extends Component {
    static template = xml`
        <div class="metadata-manager">
            <div class="metadata-header">
                <h3>记录元数据管理</h3>
                <div class="metadata-actions">
                    <button t-on-click="refreshMetadata" class="btn btn-primary">
                        <i class="fa fa-refresh" /> 刷新
                    </button>
                    
                    <button t-on-click="exportMetadata" class="btn btn-info">
                        <i class="fa fa-download" /> 导出
                    </button>
                    
                    <button t-on-click="compareMetadata" class="btn btn-warning">
                        <i class="fa fa-exchange" /> 对比
                    </button>
                </div>
            </div>
            
            <div class="metadata-content">
                <div class="basic-info">
                    <h4>基本信息</h4>
                    <table class="table table-striped">
                        <tr>
                            <td>记录ID</td>
                            <td t-esc="metadata.id" />
                        </tr>
                        <tr>
                            <td>模型</td>
                            <td t-esc="metadata.model" />
                        </tr>
                        <tr>
                            <td>XML ID</td>
                            <td>
                                <span t-esc="metadata.xmlid" />
                                <button t-if="!metadata.xmlid" 
                                        t-on-click="createXmlId"
                                        class="btn btn-sm btn-outline-primary">
                                    创建 XML ID
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>更新控制</td>
                            <td>
                                <input type="checkbox" 
                                       t-model="metadata.noupdate"
                                       t-on-change="toggleNoupdate" />
                                <label>禁止更新</label>
                            </td>
                        </tr>
                    </table>
                </div>
                
                <div class="audit-info">
                    <h4>审计信息</h4>
                    <table class="table table-striped">
                        <tr>
                            <td>创建者</td>
                            <td t-esc="metadata.creator" />
                        </tr>
                        <tr>
                            <td>创建时间</td>
                            <td t-esc="metadata.createDate" />
                        </tr>
                        <tr>
                            <td>最后修改者</td>
                            <td t-esc="metadata.lastModifiedBy" />
                        </tr>
                        <tr>
                            <td>最后修改时间</td>
                            <td t-esc="metadata.writeDate" />
                        </tr>
                    </table>
                </div>
                
                <div class="related-xmlids" t-if="metadata.xmlids.length > 1">
                    <h4>相关 XML ID</h4>
                    <ul class="list-group">
                        <li t-foreach="metadata.xmlids" t-as="xmlid" t-key="xmlid_index"
                            class="list-group-item">
                            <span t-esc="xmlid" />
                            <button t-on-click="() => this.editXmlId(xmlid)"
                                    class="btn btn-sm btn-outline-secondary float-right">
                                编辑
                            </button>
                        </li>
                    </ul>
                </div>
                
                <div class="metadata-history" t-if="showHistory">
                    <h4>变更历史</h4>
                    <div t-foreach="changeHistory" t-as="change" t-key="change_index"
                         class="change-item">
                        <div class="change-header">
                            <strong t-esc="change.user" />
                            <span class="text-muted" t-esc="change.date" />
                        </div>
                        <div class="change-details">
                            <span t-esc="change.description" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setup() {
        this.orm = useService("orm");
        this.metadata = useState({});
        this.showHistory = useState(false);
        this.changeHistory = useState([]);
        
        onWillStart(() => {
            this.loadMetadata();
        });
    }
    
    async loadMetadata() {
        try {
            const result = await this.orm.call(
                this.props.resModel, 
                'get_metadata', 
                [[this.props.resId]]
            );
            
            const metadata = result[0];
            this.metadata = {
                id: metadata.id,
                model: this.props.resModel,
                xmlid: metadata.xmlid,
                xmlids: metadata.xmlids || [],
                noupdate: metadata.noupdate,
                creator: formatMany2one(metadata.create_uid),
                lastModifiedBy: formatMany2one(metadata.write_uid),
                createDate: formatDateTime(deserializeDateTime(metadata.create_date)),
                writeDate: formatDateTime(deserializeDateTime(metadata.write_date))
            };
            
        } catch (error) {
            console.error('加载元数据失败:', error);
        }
    }
    
    async refreshMetadata() {
        await this.loadMetadata();
    }
    
    exportMetadata() {
        const data = JSON.stringify(this.metadata, null, 2);
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `metadata_${this.props.resModel}_${this.props.resId}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
    }
    
    async createXmlId() {
        const module = prompt('请输入模块名:', '__custom__');
        if (!module) return;
        
        const name = prompt('请输入XML ID名称:');
        if (!name) return;
        
        try {
            await this.orm.create('ir.model.data', [{
                module: module,
                name: name,
                model: this.props.resModel,
                res_id: this.props.resId
            }]);
            
            await this.loadMetadata();
        } catch (error) {
            console.error('创建XML ID失败:', error);
        }
    }
    
    async toggleNoupdate() {
        try {
            await this.orm.call('ir.model.data', 'toggle_noupdate', [
                this.props.resModel,
                this.metadata.id
            ]);
            
            await this.loadMetadata();
        } catch (error) {
            console.error('切换更新控制失败:', error);
        }
    }
}
```

## 最佳实践

### 1. 调试工具开发
```javascript
// ✅ 推荐：完整的调试工具结构
function customDebugTool({ component, env, accessRights }) {
    // 权限检查
    if (!accessRights.canDebug) {
        return null;
    }

    // 条件检查
    if (!component.model.root.resId) {
        return null;
    }

    return {
        type: "item",
        description: "自定义调试工具",
        callback: async () => {
            try {
                // 执行调试逻辑
                await performDebugAction();
            } catch (error) {
                console.error('调试工具执行失败:', error);
            }
        },
        sequence: 200,
        section: "custom",
    };
}
```

### 2. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
async function safeDebugAction(callback) {
    try {
        await callback();
    } catch (error) {
        console.error('调试操作失败:', error);
        env.services.notification.add(
            '调试操作失败: ' + error.message,
            { type: 'danger' }
        );
    }
}
```

### 3. 性能考虑
```javascript
// ✅ 推荐：延迟加载调试数据
class LazyDebugDialog extends Component {
    setup() {
        this.data = useState(null);
        this.loading = useState(false);
    }

    async loadData() {
        if (this.data || this.loading) return;

        this.loading = true;
        try {
            this.data = await this.fetchDebugData();
        } finally {
            this.loading = false;
        }
    }
}
```

## 总结

Odoo 视图调试工具模块提供了强大的开发和调试支持：

**核心优势**:
- **完整工具集**: 涵盖视图、记录、元数据等各个方面
- **权限控制**: 完善的权限检查机制
- **用户友好**: 直观的界面和操作流程
- **扩展性**: 支持自定义调试工具
- **开发效率**: 大幅提升开发和调试效率

**适用场景**:
- 视图开发和调试
- 数据结构分析
- 权限问题排查
- 性能问题诊断
- 系统配置管理

**设计优势**:
- 模块化设计
- 注册表机制
- 对话框界面
- 异步操作支持

这个调试工具模块为 Odoo Web 客户端提供了强大的开发支持，是提高开发效率和解决问题的重要工具。

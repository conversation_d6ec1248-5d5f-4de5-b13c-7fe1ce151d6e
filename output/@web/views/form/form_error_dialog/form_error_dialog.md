# FormErrorDialog - 表单错误对话框

## 概述

`form_error_dialog.js` 是 Odoo Web 客户端表单视图的错误对话框组件，负责在表单操作出现错误时向用户显示错误信息并提供处理选项。该模块包含29行代码，是一个简洁的OWL组件，专门用于处理表单验证错误、保存错误等情况，具备错误消息显示、用户选择处理、对话框管理等特性，是表单视图系统中错误处理的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/form/form_error_dialog/form_error_dialog.js`
- **行数**: 29
- **模块**: `@web/views/form/form_error_dialog/form_error_dialog`

## 依赖关系

```javascript
// 核心依赖
'@web/core/dialog/dialog'       // 对话框组件
'@odoo/owl'                     // OWL框架
```

## 主要组件定义

### 1. FormErrorDialog - 表单错误对话框

```javascript
class FormErrorDialog extends Component {
    static template = "web.FormErrorDialog";
    static components = { Dialog };
    static props = {
        message: { type: String, optional: true },
        onDiscard: Function,
        onStayHere: Function,
        close: Function,
    };
}
```

**组件特性**:
- **专用模板**: 使用FormErrorDialog专用模板
- **对话框集成**: 集成基础Dialog组件
- **回调属性**: 提供丰富的回调函数属性
- **消息支持**: 支持可选的错误消息显示

## 核心功能

### 1. 属性配置

```javascript
static props = {
    message: { type: String, optional: true },
    onDiscard: Function,
    onStayHere: Function,
    close: Function,
};
```

**属性功能**:
- **错误消息**: 可选的错误消息字符串
- **丢弃回调**: 用户选择丢弃更改的回调
- **保留回调**: 用户选择保留在当前页面的回调
- **关闭回调**: 对话框关闭的回调函数

### 2. 丢弃操作

```javascript
async discard() {
    await this.props.onDiscard();
    this.props.close();
}
```

**丢弃功能**:
- **异步处理**: 异步执行丢弃操作
- **回调执行**: 执行丢弃回调函数
- **对话框关闭**: 操作完成后关闭对话框
- **错误处理**: 处理丢弃过程中的错误

### 3. 保留操作

```javascript
async stay() {
    await this.props.onStayHere();
    this.props.close();
}
```

**保留功能**:
- **异步处理**: 异步执行保留操作
- **回调执行**: 执行保留回调函数
- **对话框关闭**: 操作完成后关闭对话框
- **状态保持**: 保持当前表单状态

## 使用场景

### 1. 表单错误对话框管理器

```javascript
// 表单错误对话框管理器
class FormErrorDialogManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置错误对话框配置
        this.errorDialogConfig = {
            enableAutoShow: true,
            enableErrorLogging: true,
            enableUserFeedback: true,
            enableRetryMechanism: true,
            showErrorDetails: false,
            autoCloseTimeout: 0, // 0表示不自动关闭
            maxRetryAttempts: 3
        };
        
        // 设置错误类型映射
        this.errorTypeMapping = new Map();
        
        // 设置错误统计
        this.errorStatistics = {
            totalErrors: 0,
            discardedErrors: 0,
            retainedErrors: 0,
            errorTypes: new Map(),
            averageResolutionTime: 0
        };
        
        // 设置错误历史
        this.errorHistory = [];
        
        this.initializeErrorSystem();
    }
    
    // 初始化错误系统
    initializeErrorSystem() {
        // 创建增强的表单错误对话框
        this.createEnhancedFormErrorDialog();
        
        // 设置错误类型处理
        this.setupErrorTypeHandling();
        
        // 设置错误日志系统
        this.setupErrorLogging();
        
        // 设置用户反馈系统
        this.setupUserFeedbackSystem();
    }
    
    // 创建增强的表单错误对话框
    createEnhancedFormErrorDialog() {
        const originalDialog = FormErrorDialog;
        
        this.EnhancedFormErrorDialog = class extends originalDialog {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加错误分析
                this.addErrorAnalysis();
                
                // 添加用户体验增强
                this.addUserExperienceEnhancements();
                
                // 添加可访问性支持
                this.addAccessibilitySupport();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = useState({
                    errorType: null,
                    errorCode: null,
                    showDetails: false,
                    retryCount: 0,
                    resolutionStartTime: Date.now(),
                    userFeedback: null
                });
                
                // 分析错误信息
                this.analyzeError();
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 分析错误
                this.analyzeError = () => {
                    const message = this.props.message || '';
                    
                    // 检测错误类型
                    this.enhancedState.errorType = this.detectErrorType(message);
                    
                    // 提取错误代码
                    this.enhancedState.errorCode = this.extractErrorCode(message);
                    
                    // 记录错误
                    this.recordError();
                };
                
                // 检测错误类型
                this.detectErrorType = (message) => {
                    const errorPatterns = {
                        validation: /validation|required|invalid/i,
                        permission: /permission|access|forbidden/i,
                        network: /network|connection|timeout/i,
                        server: /server|internal|500/i,
                        constraint: /constraint|unique|duplicate/i
                    };
                    
                    for (const [type, pattern] of Object.entries(errorPatterns)) {
                        if (pattern.test(message)) {
                            return type;
                        }
                    }
                    
                    return 'unknown';
                };
                
                // 提取错误代码
                this.extractErrorCode = (message) => {
                    const codeMatch = message.match(/error\s*code\s*:?\s*(\w+)/i);
                    return codeMatch ? codeMatch[1] : null;
                };
                
                // 记录错误
                this.recordError = () => {
                    const errorRecord = {
                        timestamp: Date.now(),
                        type: this.enhancedState.errorType,
                        code: this.enhancedState.errorCode,
                        message: this.props.message,
                        context: this.getErrorContext()
                    };
                    
                    // 添加到错误历史
                    this.errorHistory.push(errorRecord);
                    
                    // 更新统计
                    this.updateErrorStatistics(errorRecord);
                };
                
                // 获取错误上下文
                this.getErrorContext = () => {
                    return {
                        url: window.location.href,
                        userAgent: navigator.userAgent,
                        timestamp: new Date().toISOString(),
                        formData: this.getFormData()
                    };
                };
                
                // 获取表单数据
                this.getFormData = () => {
                    // 获取当前表单的基本信息（不包含敏感数据）
                    return {
                        model: this.props.model || 'unknown',
                        recordId: this.props.recordId || null,
                        fields: this.props.fields ? Object.keys(this.props.fields) : []
                    };
                };
                
                // 更新错误统计
                this.updateErrorStatistics = (errorRecord) => {
                    this.errorStatistics.totalErrors++;
                    
                    // 更新错误类型统计
                    const typeCount = this.errorStatistics.errorTypes.get(errorRecord.type) || 0;
                    this.errorStatistics.errorTypes.set(errorRecord.type, typeCount + 1);
                };
                
                // 增强的丢弃操作
                this.enhancedDiscard = async () => {
                    try {
                        // 记录用户选择
                        this.recordUserChoice('discard');
                        
                        // 执行原始丢弃操作
                        await this.props.onDiscard();
                        
                        // 记录解决时间
                        this.recordResolutionTime();
                        
                        // 更新统计
                        this.errorStatistics.discardedErrors++;
                        
                        // 关闭对话框
                        this.props.close();
                        
                    } catch (error) {
                        this.handleDiscardError(error);
                    }
                };
                
                // 增强的保留操作
                this.enhancedStay = async () => {
                    try {
                        // 记录用户选择
                        this.recordUserChoice('stay');
                        
                        // 执行原始保留操作
                        await this.props.onStayHere();
                        
                        // 记录解决时间
                        this.recordResolutionTime();
                        
                        // 更新统计
                        this.errorStatistics.retainedErrors++;
                        
                        // 关闭对话框
                        this.props.close();
                        
                    } catch (error) {
                        this.handleStayError(error);
                    }
                };
                
                // 记录用户选择
                this.recordUserChoice = (choice) => {
                    const choiceRecord = {
                        timestamp: Date.now(),
                        choice: choice,
                        errorType: this.enhancedState.errorType,
                        resolutionTime: Date.now() - this.enhancedState.resolutionStartTime
                    };
                    
                    // 记录到历史
                    this.errorHistory[this.errorHistory.length - 1].userChoice = choiceRecord;
                };
                
                // 记录解决时间
                this.recordResolutionTime = () => {
                    const resolutionTime = Date.now() - this.enhancedState.resolutionStartTime;
                    
                    // 更新平均解决时间
                    const totalResolved = this.errorStatistics.discardedErrors + this.errorStatistics.retainedErrors;
                    this.errorStatistics.averageResolutionTime = 
                        (this.errorStatistics.averageResolutionTime * (totalResolved - 1) + resolutionTime) / totalResolved;
                };
                
                // 处理丢弃错误
                this.handleDiscardError = (error) => {
                    console.error('Error during discard operation:', error);
                    
                    // 显示错误通知
                    if (this.notification) {
                        this.notification.add(
                            'Failed to discard changes. Please try again.',
                            { type: 'danger' }
                        );
                    }
                };
                
                // 处理保留错误
                this.handleStayError = (error) => {
                    console.error('Error during stay operation:', error);
                    
                    // 显示错误通知
                    if (this.notification) {
                        this.notification.add(
                            'Failed to stay on current page. Please try again.',
                            { type: 'danger' }
                        );
                    }
                };
                
                // 重试操作
                this.retryOperation = async () => {
                    if (this.enhancedState.retryCount >= this.errorDialogConfig.maxRetryAttempts) {
                        this.showMaxRetryMessage();
                        return;
                    }
                    
                    this.enhancedState.retryCount++;
                    
                    try {
                        // 执行重试逻辑
                        await this.executeRetry();
                        
                        // 重试成功，关闭对话框
                        this.props.close();
                        
                    } catch (error) {
                        this.handleRetryError(error);
                    }
                };
                
                // 执行重试
                this.executeRetry = async () => {
                    // 实现重试逻辑
                    if (this.props.onRetry) {
                        await this.props.onRetry();
                    }
                };
                
                // 处理重试错误
                this.handleRetryError = (error) => {
                    console.error('Retry failed:', error);
                    
                    // 显示重试失败消息
                    if (this.notification) {
                        this.notification.add(
                            `Retry failed (${this.enhancedState.retryCount}/${this.errorDialogConfig.maxRetryAttempts})`,
                            { type: 'warning' }
                        );
                    }
                };
                
                // 显示最大重试消息
                this.showMaxRetryMessage = () => {
                    if (this.notification) {
                        this.notification.add(
                            'Maximum retry attempts reached. Please contact support.',
                            { type: 'danger' }
                        );
                    }
                };
                
                // 切换错误详情
                this.toggleErrorDetails = () => {
                    this.enhancedState.showDetails = !this.enhancedState.showDetails;
                };
                
                // 获取错误建议
                this.getErrorSuggestion = () => {
                    const suggestions = {
                        validation: 'Please check the required fields and ensure all data is valid.',
                        permission: 'You may not have sufficient permissions. Contact your administrator.',
                        network: 'Please check your internet connection and try again.',
                        server: 'Server error occurred. Please try again later or contact support.',
                        constraint: 'This data conflicts with existing records. Please check for duplicates.'
                    };
                    
                    return suggestions[this.enhancedState.errorType] || 'Please try again or contact support.';
                };
                
                // 提交用户反馈
                this.submitUserFeedback = async (feedback) => {
                    try {
                        this.enhancedState.userFeedback = feedback;
                        
                        // 发送反馈到服务器
                        await this.sendFeedbackToServer(feedback);
                        
                        // 显示感谢消息
                        if (this.notification) {
                            this.notification.add(
                                'Thank you for your feedback!',
                                { type: 'success' }
                            );
                        }
                        
                    } catch (error) {
                        console.error('Failed to submit feedback:', error);
                    }
                };
                
                // 发送反馈到服务器
                this.sendFeedbackToServer = async (feedback) => {
                    // 实现反馈发送逻辑
                    console.log('Sending feedback:', feedback);
                };
            }
            
            addErrorAnalysis() {
                // 错误分析功能
                this.errorAnalyzer = {
                    analyze: (error) => this.analyzeError(error),
                    categorize: (error) => this.detectErrorType(error),
                    suggest: () => this.getErrorSuggestion(),
                    track: (error) => this.recordError(error)
                };
            }
            
            addUserExperienceEnhancements() {
                // 用户体验增强
                this.uxEnhancements = {
                    showProgressIndicator: () => this.showProgressIndicator(),
                    provideHelpLinks: () => this.provideHelpLinks(),
                    enableKeyboardShortcuts: () => this.enableKeyboardShortcuts(),
                    customizeAppearance: () => this.customizeAppearance()
                };
            }
            
            addAccessibilitySupport() {
                // 可访问性支持
                this.accessibilityManager = {
                    setAriaLabels: () => this.setAriaLabels(),
                    announceError: () => this.announceError(),
                    enableScreenReader: () => this.enableScreenReader(),
                    handleKeyboardNavigation: (event) => this.handleKeyboardNavigation(event)
                };
            }
            
            // 设置ARIA标签
            setAriaLabels() {
                const dialogElement = this.rootRef.el;
                if (dialogElement) {
                    dialogElement.setAttribute('role', 'alertdialog');
                    dialogElement.setAttribute('aria-labelledby', 'error-dialog-title');
                    dialogElement.setAttribute('aria-describedby', 'error-dialog-message');
                }
            }
            
            // 宣布错误
            announceError() {
                const message = this.props.message || 'An error occurred';
                this.announceToScreenReader(`Error: ${message}`);
            }
            
            // 屏幕阅读器通知
            announceToScreenReader(message) {
                const announcement = document.createElement('div');
                announcement.setAttribute('aria-live', 'assertive');
                announcement.setAttribute('aria-atomic', 'true');
                announcement.className = 'sr-only';
                announcement.textContent = message;
                
                document.body.appendChild(announcement);
                setTimeout(() => document.body.removeChild(announcement), 1000);
            }
            
            // 处理键盘导航
            handleKeyboardNavigation(event) {
                switch (event.key) {
                    case 'Escape':
                        event.preventDefault();
                        this.enhancedStay();
                        break;
                    case 'Enter':
                        if (event.ctrlKey) {
                            event.preventDefault();
                            this.enhancedDiscard();
                        }
                        break;
                }
            }
            
            // 重写原始方法
            discard() {
                return this.enhancedDiscard();
            }
            
            stay() {
                return this.enhancedStay();
            }
            
            // 获取错误统计
            getErrorStatistics() {
                return {
                    ...this.errorStatistics,
                    errorType: this.enhancedState.errorType,
                    errorCode: this.enhancedState.errorCode,
                    retryCount: this.enhancedState.retryCount
                };
            }
        };
    }
    
    // 设置错误类型处理
    setupErrorTypeHandling() {
        // 错误类型处理器
        this.errorTypeHandlers = {
            validation: this.handleValidationError.bind(this),
            permission: this.handlePermissionError.bind(this),
            network: this.handleNetworkError.bind(this),
            server: this.handleServerError.bind(this),
            constraint: this.handleConstraintError.bind(this)
        };
    }
    
    // 设置错误日志系统
    setupErrorLogging() {
        this.errorLogger = {
            log: (error) => this.logError(error),
            export: () => this.exportErrorLog(),
            clear: () => this.clearErrorLog(),
            getHistory: () => this.errorHistory
        };
    }
    
    // 设置用户反馈系统
    setupUserFeedbackSystem() {
        this.feedbackSystem = {
            collect: (feedback) => this.collectFeedback(feedback),
            analyze: () => this.analyzeFeedback(),
            report: () => this.generateFeedbackReport()
        };
    }
    
    // 处理验证错误
    handleValidationError(error) {
        return {
            suggestion: 'Please check the required fields and ensure all data is valid.',
            actions: ['fix_validation', 'discard_changes']
        };
    }
    
    // 处理权限错误
    handlePermissionError(error) {
        return {
            suggestion: 'You may not have sufficient permissions. Contact your administrator.',
            actions: ['contact_admin', 'discard_changes']
        };
    }
    
    // 处理网络错误
    handleNetworkError(error) {
        return {
            suggestion: 'Please check your internet connection and try again.',
            actions: ['retry', 'save_offline', 'discard_changes']
        };
    }
    
    // 处理服务器错误
    handleServerError(error) {
        return {
            suggestion: 'Server error occurred. Please try again later or contact support.',
            actions: ['retry', 'contact_support', 'discard_changes']
        };
    }
    
    // 处理约束错误
    handleConstraintError(error) {
        return {
            suggestion: 'This data conflicts with existing records. Please check for duplicates.',
            actions: ['check_duplicates', 'modify_data', 'discard_changes']
        };
    }
    
    // 记录错误
    logError(error) {
        if (this.errorDialogConfig.enableErrorLogging) {
            console.error('Form error logged:', error);
            
            // 发送到服务器日志
            this.sendErrorToServer(error);
        }
    }
    
    // 发送错误到服务器
    async sendErrorToServer(error) {
        try {
            // 实现服务器错误发送逻辑
            console.log('Sending error to server:', error);
        } catch (sendError) {
            console.error('Failed to send error to server:', sendError);
        }
    }
    
    // 创建表单错误对话框
    createFormErrorDialog(props) {
        return new this.EnhancedFormErrorDialog(props);
    }
    
    // 显示错误对话框
    showErrorDialog(message, options = {}) {
        const dialog = this.createFormErrorDialog({
            message: message,
            onDiscard: options.onDiscard || (() => {}),
            onStayHere: options.onStayHere || (() => {}),
            close: options.close || (() => {}),
            ...options
        });
        
        return dialog;
    }
    
    // 获取错误统计
    getErrorStatistics() {
        return {
            ...this.errorStatistics,
            errorHistoryCount: this.errorHistory.length
        };
    }
    
    // 导出错误日志
    exportErrorLog() {
        const logData = {
            timestamp: new Date().toISOString(),
            statistics: this.errorStatistics,
            history: this.errorHistory
        };
        
        return JSON.stringify(logData, null, 2);
    }
    
    // 清理错误历史
    clearErrorLog() {
        this.errorHistory = [];
        this.errorStatistics = {
            totalErrors: 0,
            discardedErrors: 0,
            retainedErrors: 0,
            errorTypes: new Map(),
            averageResolutionTime: 0
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理错误历史
        this.errorHistory = [];
        
        // 清理错误类型映射
        this.errorTypeMapping.clear();
        
        // 重置统计
        this.errorStatistics = {
            totalErrors: 0,
            discardedErrors: 0,
            retainedErrors: 0,
            errorTypes: new Map(),
            averageResolutionTime: 0
        };
    }
}

// 使用示例
const errorDialogManager = new FormErrorDialogManager();

// 显示错误对话框
const errorDialog = errorDialogManager.showErrorDialog(
    'Validation failed: Name field is required',
    {
        onDiscard: async () => {
            console.log('User chose to discard changes');
        },
        onStayHere: async () => {
            console.log('User chose to stay and fix errors');
        },
        close: () => {
            console.log('Error dialog closed');
        }
    }
);

// 获取错误统计
const stats = errorDialogManager.getErrorStatistics();
console.log('Error dialog statistics:', stats);
```

## 技术特点

### 1. 简洁设计
- **轻量级**: 仅29行代码的简洁实现
- **专注功能**: 专注于错误处理功能
- **清晰逻辑**: 清晰的错误处理逻辑
- **易于维护**: 易于理解和维护

### 2. 用户体验
- **明确选择**: 提供明确的用户选择选项
- **异步处理**: 支持异步的错误处理
- **即时反馈**: 提供即时的操作反馈
- **错误恢复**: 支持错误恢复机制

### 3. 回调机制
- **灵活回调**: 灵活的回调函数机制
- **异步支持**: 支持异步回调处理
- **错误处理**: 完善的错误处理机制
- **状态管理**: 有效的状态管理

### 4. 扩展性
- **组件扩展**: 支持组件功能扩展
- **回调扩展**: 支持回调功能扩展
- **消息定制**: 支持错误消息定制
- **行为定制**: 支持行为逻辑定制

## 设计模式

### 1. 对话框模式 (Dialog Pattern)
- **模态显示**: 模态的错误信息显示
- **用户交互**: 强制的用户交互
- **焦点管理**: 管理对话框焦点

### 2. 回调模式 (Callback Pattern)
- **事件处理**: 通过回调处理用户选择
- **异步操作**: 支持异步回调操作
- **解耦设计**: 解耦错误处理逻辑

### 3. 策略模式 (Strategy Pattern)
- **处理策略**: 不同的错误处理策略
- **选择策略**: 不同的用户选择策略
- **恢复策略**: 不同的错误恢复策略

### 4. 观察者模式 (Observer Pattern)
- **状态监听**: 监听错误状态变化
- **事件通知**: 通知错误处理结果
- **自动响应**: 自动响应用户操作

## 注意事项

1. **错误处理**: 确保回调函数的错误处理
2. **用户体验**: 提供清晰的错误信息和选择
3. **异步操作**: 正确处理异步回调操作
4. **内存管理**: 及时清理对话框资源

## 扩展建议

1. **错误分类**: 添加错误类型分类功能
2. **重试机制**: 添加自动重试机制
3. **错误日志**: 添加错误日志记录功能
4. **用户反馈**: 添加用户反馈收集功能
5. **可访问性**: 增强可访问性支持

该表单错误对话框为Odoo Web客户端提供了专业的错误处理功能，通过简洁的设计和灵活的回调机制确保了良好的用户体验和错误恢复能力。

# FormGroup - 表单分组组件

## 概述

`form_group.js` 是 Odoo Web 客户端表单视图的分组组件，负责管理表单中字段的分组布局和排列。该模块包含104行代码，定义了三个核心类：Group基类、OuterGroup外部分组和InnerGroup内部分组，专门用于处理表单字段的网格布局、列跨度计算、行分配等功能，具备响应式布局、动态排列、可见性控制、插槽管理等特性，是表单视图系统中布局管理的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/form/form_group/form_group.js`
- **行数**: 104
- **模块**: `@web/views/form/form_group/form_group`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                     // OWL框架
'@web/core/utils/arrays'        // 数组工具函数
```

## 主要组件定义

### 1. Group - 基础分组类

```javascript
class Group extends Component {
    static template = "";
    static props = ["class?", "slots?", "maxCols?", "style?"];
    static defaultProps = {
        maxCols: 2,
    };
}
```

**基础类特性**:
- **抽象模板**: 空模板，由子类定义具体模板
- **灵活属性**: 支持样式类、插槽、最大列数、内联样式
- **默认配置**: 默认最大列数为2
- **扩展基础**: 为子类提供扩展基础

### 2. OuterGroup - 外部分组类

```javascript
const OuterGroup = class OuterGroup extends Group {
    static template = "web.Form.OuterGroup";
    static defaultProps = {
        ...Group.defaultProps,
        slots: [],
        hasOuterTemplate: true,
    };
}
```

**外部分组特性**:
- **专用模板**: 使用Form.OuterGroup模板
- **属性继承**: 继承基类的默认属性
- **插槽初始化**: 默认空插槽数组
- **外部模板**: 标记为外部模板类型

### 3. InnerGroup - 内部分组类

```javascript
const InnerGroup = class InnerGroup extends Group {
    static template = "web.Form.InnerGroup";
}
```

**内部分组特性**:
- **专用模板**: 使用Form.InnerGroup模板
- **行管理**: 专门处理行级布局
- **动态模板**: 支持动态模板选择
- **精细控制**: 提供更精细的布局控制

## 核心功能

### 1. 项目获取和排序

```javascript
_getItems() {
    const items = Object.entries(this.props.slots || {}).filter(([k, v]) => v.type === "item");
    return sortBy(items, (i) => i[1].sequence);
}

getItems() {
    return this._getItems();
}
```

**项目管理功能**:
- **插槽过滤**: 过滤出类型为"item"的插槽
- **序列排序**: 按sequence属性排序项目
- **数据转换**: 将插槽对象转换为项目数组
- **可重写**: 子类可重写以实现自定义逻辑

### 2. 外部分组项目处理

```javascript
getItems() {
    const nbCols = this.props.maxCols;
    const colSize = Math.max(1, Math.round(12 / nbCols));

    // Dispatch items across table rows
    const items = super.getItems().filter(([k, v]) => !("isVisible" in v) || v.isVisible);
    return items.map((item) => {
        const [slotName, slot] = item;
        const itemSpan = slot.itemSpan || 1;
        return {
            name: slotName,
            size: itemSpan * colSize,
            newline: slot.newline,
            colspan: itemSpan,
        };
    });
}
```

**外部分组处理功能**:
- **列数计算**: 根据最大列数计算列大小
- **可见性过滤**: 过滤出可见的项目
- **跨度计算**: 计算项目的列跨度
- **Bootstrap网格**: 基于12列Bootstrap网格系统

### 3. 内部分组行管理

```javascript
getRows() {
    const maxCols = this.props.maxCols;

    const rows = [];
    let currentRow = [];
    let reservedSpace = 0;

    // Dispatch items across table rows
    const items = this.getItems();
    while (items.length) {
        const [slotName, slot] = items.shift();
        if (!slot.isVisible) {
            continue;
        }

        const { newline, itemSpan } = slot;
        if (newline) {
            rows.push(currentRow);
            currentRow = [];
            reservedSpace = 0;
        }

        const fullItemSpan = itemSpan || 1;

        if (fullItemSpan + reservedSpace > maxCols) {
            rows.push(currentRow);
            currentRow = [];
            reservedSpace = 0;
        }

        const isVisible = !("isVisible" in slot) || slot.isVisible;
        currentRow.push({ ...slot, name: slotName, itemSpan, isVisible });
        reservedSpace += itemSpan || 1;

        // Allows to remove the line if the content is not visible instead of leaving an empty line.
        currentRow.isVisible = currentRow.isVisible || isVisible;
    }
    rows.push(currentRow);

    return rows;
}
```

**行管理功能**:
- **动态行分配**: 动态将项目分配到行中
- **空间管理**: 跟踪每行的已用空间
- **换行处理**: 处理强制换行和自动换行
- **可见性优化**: 移除完全不可见的行

### 4. 动态模板选择

```javascript
getTemplate(subType) {
    return this.constructor.templates[subType] || this.constructor.templates.default;
}
```

**模板选择功能**:
- **子类型支持**: 支持不同子类型的模板
- **默认回退**: 提供默认模板回退机制
- **动态选择**: 运行时动态选择模板
- **扩展性**: 支持模板的扩展和定制

## 使用场景

### 1. 表单分组管理器

```javascript
// 表单分组管理器
class FormGroupManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置分组配置
        this.groupConfig = {
            enableResponsiveLayout: true,
            enableDynamicColumns: true,
            enableVisibilityControl: true,
            enableCustomTemplates: true,
            defaultMaxCols: 2,
            minColSize: 1,
            maxColSize: 12,
            enableNestedGroups: true
        };
        
        // 设置布局引擎
        this.layoutEngine = new Map();
        
        // 设置模板注册表
        this.templateRegistry = new Map();
        
        // 设置分组统计
        this.groupStatistics = {
            totalGroups: 0,
            outerGroups: 0,
            innerGroups: 0,
            totalItems: 0,
            averageItemsPerGroup: 0
        };
        
        this.initializeGroupSystem();
    }
    
    // 初始化分组系统
    initializeGroupSystem() {
        // 创建增强的分组组件
        this.createEnhancedGroupComponents();
        
        // 设置布局系统
        this.setupLayoutSystem();
        
        // 设置响应式系统
        this.setupResponsiveSystem();
        
        // 设置性能优化
        this.setupPerformanceOptimizations();
    }
    
    // 创建增强的分组组件
    createEnhancedGroupComponents() {
        const originalGroup = Group;
        const originalOuterGroup = OuterGroup;
        const originalInnerGroup = InnerGroup;
        
        // 增强基础分组类
        this.EnhancedGroup = class extends originalGroup {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加响应式支持
                this.addResponsiveSupport();
                
                // 添加性能优化
                this.addPerformanceOptimizations();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = useState({
                    currentBreakpoint: 'md',
                    visibleItems: [],
                    hiddenItems: [],
                    dynamicMaxCols: this.props.maxCols,
                    isCollapsed: false
                });
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的项目获取
                this.enhancedGetItems = () => {
                    const baseItems = this._getItems();
                    
                    // 应用可见性过滤
                    const visibleItems = this.filterVisibleItems(baseItems);
                    
                    // 应用响应式调整
                    const responsiveItems = this.applyResponsiveAdjustments(visibleItems);
                    
                    // 更新状态
                    this.enhancedState.visibleItems = responsiveItems;
                    this.enhancedState.hiddenItems = baseItems.filter(item => 
                        !responsiveItems.includes(item)
                    );
                    
                    return responsiveItems;
                };
                
                // 过滤可见项目
                this.filterVisibleItems = (items) => {
                    return items.filter(([slotName, slot]) => {
                        // 检查基本可见性
                        if ('isVisible' in slot && !slot.isVisible) {
                            return false;
                        }
                        
                        // 检查响应式可见性
                        if (slot.responsiveVisibility) {
                            const breakpoint = this.enhancedState.currentBreakpoint;
                            return slot.responsiveVisibility[breakpoint] !== false;
                        }
                        
                        return true;
                    });
                };
                
                // 应用响应式调整
                this.applyResponsiveAdjustments = (items) => {
                    const breakpoint = this.enhancedState.currentBreakpoint;
                    
                    return items.map(([slotName, slot]) => {
                        const adjustedSlot = { ...slot };
                        
                        // 应用响应式跨度
                        if (slot.responsiveSpan && slot.responsiveSpan[breakpoint]) {
                            adjustedSlot.itemSpan = slot.responsiveSpan[breakpoint];
                        }
                        
                        // 应用响应式换行
                        if (slot.responsiveNewline && slot.responsiveNewline[breakpoint]) {
                            adjustedSlot.newline = slot.responsiveNewline[breakpoint];
                        }
                        
                        return [slotName, adjustedSlot];
                    });
                };
                
                // 计算动态列数
                this.calculateDynamicMaxCols = () => {
                    const breakpoint = this.enhancedState.currentBreakpoint;
                    const breakpointCols = {
                        'xs': 1,
                        'sm': 1,
                        'md': 2,
                        'lg': 3,
                        'xl': 4
                    };
                    
                    return breakpointCols[breakpoint] || this.props.maxCols;
                };
                
                // 更新断点
                this.updateBreakpoint = (breakpoint) => {
                    this.enhancedState.currentBreakpoint = breakpoint;
                    
                    if (this.groupConfig.enableDynamicColumns) {
                        this.enhancedState.dynamicMaxCols = this.calculateDynamicMaxCols();
                    }
                };
                
                // 切换折叠状态
                this.toggleCollapse = () => {
                    this.enhancedState.isCollapsed = !this.enhancedState.isCollapsed;
                };
                
                // 获取分组CSS类
                this.getGroupClasses = () => {
                    const classes = [this.props.class || ''];
                    
                    // 添加断点类
                    classes.push(`o_group_${this.enhancedState.currentBreakpoint}`);
                    
                    // 添加折叠类
                    if (this.enhancedState.isCollapsed) {
                        classes.push('o_group_collapsed');
                    }
                    
                    // 添加列数类
                    classes.push(`o_group_cols_${this.enhancedState.dynamicMaxCols}`);
                    
                    return classes.filter(Boolean).join(' ');
                };
            }
            
            addResponsiveSupport() {
                // 响应式支持
                this.responsiveManager = {
                    breakpoints: {
                        xs: 0,
                        sm: 576,
                        md: 768,
                        lg: 992,
                        xl: 1200
                    },
                    
                    getCurrentBreakpoint: () => {
                        const width = window.innerWidth;
                        const breakpoints = this.responsiveManager.breakpoints;
                        
                        if (width >= breakpoints.xl) return 'xl';
                        if (width >= breakpoints.lg) return 'lg';
                        if (width >= breakpoints.md) return 'md';
                        if (width >= breakpoints.sm) return 'sm';
                        return 'xs';
                    },
                    
                    handleResize: () => {
                        const newBreakpoint = this.responsiveManager.getCurrentBreakpoint();
                        if (newBreakpoint !== this.enhancedState.currentBreakpoint) {
                            this.updateBreakpoint(newBreakpoint);
                        }
                    }
                };
                
                // 监听窗口大小变化
                if (this.groupConfig.enableResponsiveLayout) {
                    window.addEventListener('resize', this.responsiveManager.handleResize);
                }
            }
            
            addPerformanceOptimizations() {
                // 性能优化
                this.performanceManager = {
                    memoizedItems: null,
                    lastPropsHash: null,
                    
                    getMemoizedItems: () => {
                        const currentPropsHash = this.calculatePropsHash();
                        
                        if (this.performanceManager.lastPropsHash !== currentPropsHash) {
                            this.performanceManager.memoizedItems = this.enhancedGetItems();
                            this.performanceManager.lastPropsHash = currentPropsHash;
                        }
                        
                        return this.performanceManager.memoizedItems;
                    },
                    
                    calculatePropsHash: () => {
                        return JSON.stringify({
                            slots: this.props.slots,
                            maxCols: this.props.maxCols,
                            breakpoint: this.enhancedState.currentBreakpoint
                        });
                    }
                };
            }
            
            // 重写原始方法
            getItems() {
                if (this.groupConfig.enablePerformanceOptimizations) {
                    return this.performanceManager.getMemoizedItems();
                }
                return this.enhancedGetItems();
            }
            
            get allClasses() {
                return this.getGroupClasses();
            }
            
            // 组件销毁时清理
            onWillDestroy() {
                if (this.groupConfig.enableResponsiveLayout) {
                    window.removeEventListener('resize', this.responsiveManager.handleResize);
                }
            }
        };
        
        // 增强外部分组类
        this.EnhancedOuterGroup = class extends originalOuterGroup {
            setup() {
                super.setup();
                this.addOuterGroupEnhancements();
            }
            
            addOuterGroupEnhancements() {
                // 外部分组特有的增强功能
                this.outerGroupFeatures = {
                    enableBootstrapGrid: true,
                    enableFlexboxLayout: false,
                    enableCustomBreakpoints: true
                };
            }
            
            getItems() {
                const nbCols = this.enhancedState?.dynamicMaxCols || this.props.maxCols;
                const colSize = Math.max(1, Math.round(12 / nbCols));

                const items = this.enhancedGetItems ? 
                    this.enhancedGetItems() : 
                    super.getItems().filter(([k, v]) => !("isVisible" in v) || v.isVisible);
                
                return items.map((item) => {
                    const [slotName, slot] = item;
                    const itemSpan = slot.itemSpan || 1;
                    return {
                        name: slotName,
                        size: itemSpan * colSize,
                        newline: slot.newline,
                        colspan: itemSpan,
                        responsiveSize: this.calculateResponsiveSize(itemSpan, colSize),
                    };
                });
            }
            
            calculateResponsiveSize(itemSpan, colSize) {
                const breakpoint = this.enhancedState?.currentBreakpoint || 'md';
                const responsiveSizes = {
                    'xs': Math.min(12, itemSpan * colSize * 2),
                    'sm': Math.min(12, itemSpan * colSize * 1.5),
                    'md': itemSpan * colSize,
                    'lg': itemSpan * colSize,
                    'xl': itemSpan * colSize
                };
                
                return responsiveSizes[breakpoint] || itemSpan * colSize;
            }
        };
        
        // 增强内部分组类
        this.EnhancedInnerGroup = class extends originalInnerGroup {
            setup() {
                super.setup();
                this.addInnerGroupEnhancements();
            }
            
            addInnerGroupEnhancements() {
                // 内部分组特有的增强功能
                this.innerGroupFeatures = {
                    enableAdvancedRowManagement: true,
                    enableDynamicRowHeight: true,
                    enableRowGrouping: true
                };
            }
            
            getRows() {
                const maxCols = this.enhancedState?.dynamicMaxCols || this.props.maxCols;
                const rows = [];
                let currentRow = [];
                let reservedSpace = 0;

                const items = this.enhancedGetItems ? this.enhancedGetItems() : this.getItems();
                
                while (items.length) {
                    const [slotName, slot] = items.shift();
                    if (!slot.isVisible) {
                        continue;
                    }

                    const { newline, itemSpan } = slot;
                    if (newline) {
                        if (currentRow.length > 0) {
                            rows.push(this.enhanceRow(currentRow));
                        }
                        currentRow = [];
                        reservedSpace = 0;
                    }

                    const fullItemSpan = itemSpan || 1;

                    if (fullItemSpan + reservedSpace > maxCols) {
                        if (currentRow.length > 0) {
                            rows.push(this.enhanceRow(currentRow));
                        }
                        currentRow = [];
                        reservedSpace = 0;
                    }

                    const isVisible = !("isVisible" in slot) || slot.isVisible;
                    currentRow.push({ 
                        ...slot, 
                        name: slotName, 
                        itemSpan, 
                        isVisible,
                        responsiveSpan: this.calculateResponsiveSpan(itemSpan)
                    });
                    reservedSpace += itemSpan || 1;

                    currentRow.isVisible = currentRow.isVisible || isVisible;
                }
                
                if (currentRow.length > 0) {
                    rows.push(this.enhanceRow(currentRow));
                }

                return rows;
            }
            
            enhanceRow(row) {
                return {
                    ...row,
                    id: `row_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                    totalSpan: row.reduce((sum, item) => sum + (item.itemSpan || 1), 0),
                    hasVisibleItems: row.some(item => item.isVisible),
                    breakpoint: this.enhancedState?.currentBreakpoint || 'md'
                };
            }
            
            calculateResponsiveSpan(itemSpan) {
                const breakpoint = this.enhancedState?.currentBreakpoint || 'md';
                const spanMultipliers = {
                    'xs': 2,
                    'sm': 1.5,
                    'md': 1,
                    'lg': 1,
                    'xl': 1
                };
                
                return Math.min(this.props.maxCols, Math.round(itemSpan * spanMultipliers[breakpoint]));
            }
        };
    }
    
    // 设置布局系统
    setupLayoutSystem() {
        this.layoutEngine.set('bootstrap', {
            name: 'Bootstrap Grid',
            columns: 12,
            breakpoints: ['xs', 'sm', 'md', 'lg', 'xl'],
            calculateSize: (span, totalCols) => Math.max(1, Math.round(12 / totalCols)) * span
        });
        
        this.layoutEngine.set('flexbox', {
            name: 'Flexbox Layout',
            flexible: true,
            breakpoints: ['mobile', 'tablet', 'desktop'],
            calculateSize: (span, totalCols) => `${(span / totalCols) * 100}%`
        });
    }
    
    // 设置响应式系统
    setupResponsiveSystem() {
        this.responsiveConfig = {
            enableBreakpoints: this.groupConfig.enableResponsiveLayout,
            defaultBreakpoint: 'md',
            customBreakpoints: new Map(),
            debounceDelay: 100
        };
    }
    
    // 设置性能优化
    setupPerformanceOptimizations() {
        this.performanceConfig = {
            enableMemoization: true,
            enableVirtualization: false,
            enableLazyLoading: false,
            cacheSize: 100
        };
    }
    
    // 创建分组组件
    createGroup(type, props) {
        switch (type) {
            case 'outer':
                return new this.EnhancedOuterGroup(props);
            case 'inner':
                return new this.EnhancedInnerGroup(props);
            default:
                return new this.EnhancedGroup(props);
        }
    }
    
    // 注册自定义模板
    registerTemplate(name, template) {
        this.templateRegistry.set(name, template);
    }
    
    // 获取模板
    getTemplate(name) {
        return this.templateRegistry.get(name);
    }
    
    // 获取分组统计
    getGroupStatistics() {
        return {
            ...this.groupStatistics,
            layoutEngineCount: this.layoutEngine.size,
            templateCount: this.templateRegistry.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理布局引擎
        this.layoutEngine.clear();
        
        // 清理模板注册表
        this.templateRegistry.clear();
        
        // 重置统计
        this.groupStatistics = {
            totalGroups: 0,
            outerGroups: 0,
            innerGroups: 0,
            totalItems: 0,
            averageItemsPerGroup: 0
        };
    }
}

// 使用示例
const groupManager = new FormGroupManager();

// 创建外部分组
const outerGroup = groupManager.createGroup('outer', {
    maxCols: 2,
    slots: {
        field1: { type: 'item', sequence: 1, itemSpan: 1 },
        field2: { type: 'item', sequence: 2, itemSpan: 1 },
        field3: { type: 'item', sequence: 3, itemSpan: 2, newline: true }
    }
});

// 创建内部分组
const innerGroup = groupManager.createGroup('inner', {
    maxCols: 3,
    slots: {
        subfield1: { type: 'item', sequence: 1, isVisible: true },
        subfield2: { type: 'item', sequence: 2, isVisible: true },
        subfield3: { type: 'item', sequence: 3, isVisible: false }
    }
});

// 注册自定义模板
groupManager.registerTemplate('custom_group', {
    template: 'custom.FormGroup',
    features: ['responsive', 'collapsible']
});

// 获取统计信息
const stats = groupManager.getGroupStatistics();
console.log('Form group statistics:', stats);
```

## 技术特点

### 1. 层次化设计
- **基类抽象**: Group基类提供通用功能
- **专门化子类**: OuterGroup和InnerGroup提供专门功能
- **模板分离**: 不同类型使用不同模板
- **功能继承**: 子类继承并扩展基类功能

### 2. 布局管理
- **网格系统**: 基于12列Bootstrap网格系统
- **响应式布局**: 支持响应式列数调整
- **动态排列**: 动态计算项目排列
- **空间优化**: 智能的空间分配和优化

### 3. 可见性控制
- **条件显示**: 支持项目的条件显示
- **动态过滤**: 动态过滤可见项目
- **行级优化**: 移除完全不可见的行
- **性能优化**: 避免渲染不可见内容

### 4. 扩展性
- **插槽机制**: 灵活的插槽内容机制
- **模板系统**: 可扩展的模板系统
- **配置驱动**: 配置驱动的布局行为
- **自定义支持**: 支持自定义布局逻辑

## 设计模式

### 1. 模板方法模式 (Template Method Pattern)
- **算法框架**: 定义布局算法的框架
- **步骤定制**: 允许子类定制特定步骤
- **行为复用**: 复用通用的布局逻辑

### 2. 策略模式 (Strategy Pattern)
- **布局策略**: 不同的布局计算策略
- **排列策略**: 不同的项目排列策略
- **响应式策略**: 不同的响应式处理策略

### 3. 组合模式 (Composition Pattern)
- **分组组合**: 支持分组的嵌套组合
- **项目组合**: 组合不同类型的项目
- **灵活结构**: 支持灵活的组合结构

### 4. 工厂模式 (Factory Pattern)
- **组件创建**: 创建不同类型的分组组件
- **模板生成**: 生成不同的布局模板
- **配置工厂**: 根据配置创建组件

## 注意事项

1. **性能考虑**: 避免频繁的布局计算和重排
2. **响应式设计**: 确保在不同屏幕尺寸下的正确显示
3. **可访问性**: 确保布局的可访问性
4. **浏览器兼容**: 确保跨浏览器的兼容性

## 扩展建议

1. **拖拽排序**: 添加字段的拖拽排序功能
2. **动画过渡**: 添加布局变化的动画过渡
3. **自定义断点**: 支持自定义响应式断点
4. **布局预设**: 提供常用的布局预设
5. **可视化编辑**: 添加可视化的布局编辑器

该表单分组组件为Odoo Web客户端提供了强大的布局管理功能，通过层次化设计和灵活的配置确保了表单字段的合理排列和响应式显示。

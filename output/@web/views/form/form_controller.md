# FormController - 表单视图控制器

## 概述

`form_controller.js` 是 Odoo Web 客户端表单视图的控制器组件，负责处理表单视图的业务逻辑和用户交互。该模块包含688行代码，是一个OWL组件，专门用于管理表单视图的状态、数据操作、用户交互等功能，具备记录管理、字段验证、按钮处理、状态管理等特性，是表单视图系统中业务逻辑处理的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/form/form_controller.js`
- **行数**: 688
- **模块**: `@web/views/form/form_controller`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'                                       // 翻译服务
'@web/core/browser/feature_detection'                              // 浏览器特性检测
'@web/core/confirmation_dialog/confirmation_dialog'                // 确认对话框
'@web/core/context'                                                // 上下文管理
'@web/core/debug/debug_context'                                    // 调试上下文
'@web/core/registry'                                               // 注册表
'@web/core/ui/ui_service'                                          // UI服务
'@web/core/user'                                                   // 用户服务
'@web/core/utils/hooks'                                            // 工具钩子
'@web/core/utils/objects'                                          // 对象工具
'@web/core/utils/xml'                                              // XML工具
'@web/core/py_js/py'                                               // Python表达式
'@web/search/action_hook'                                          // 动作钩子
'@web/search/layout'                                               // 搜索布局
'@web/search/pager_hook'                                           // 分页钩子
'@web/views/standard_view_props'                                   // 标准视图属性
'@web/views/utils'                                                 // 视图工具
'@web/views/view_button/view_button_hook'                          // 视图按钮钩子
'@web/views/view_button/view_button'                               // 视图按钮
'@web/views/fields/field'                                          // 字段组件
'@web/model/model'                                                 // 模型基类
'@web/model/relational_model/utils'                                // 关系模型工具
'@web/views/view_compiler'                                         // 视图编译器
'@web/views/widgets/widget'                                        // 小部件
'@web/search/action_menus/action_menus'                            // 动作菜单
'@web/views/form/button_box/button_box'                            // 按钮盒子
'@web/views/form/form_compiler'                                    // 表单编译器
'@web/views/form/form_error_dialog/form_error_dialog'              // 表单错误对话框
'@web/views/form/form_status_indicator/form_status_indicator'      // 表单状态指示器
'@web/views/form/status_bar_dropdown_items/status_bar_dropdown_items' // 状态栏下拉项
'@web/views/form/form_cog_menu/form_cog_menu'                      // 表单齿轮菜单
'@odoo/owl'                                                        // OWL框架
'@web/model/relational_model/errors'                               // 关系模型错误
'@web/core/utils/reactive'                                         // 响应式工具
```

## 主要组件定义

### 1. FormController - 表单视图控制器

```javascript
class FormController extends Component {
    static template = "web.FormView";
    static components = {
        Layout,
        ButtonBox,
        Field,
        FormStatusIndicator,
        StatusBarDropdownItems,
        ViewButton,
        Widget,
        FormCogMenu,
    };
    static props = {
        ...standardViewProps,
        Model: Function,
        Renderer: Function,
        Compiler: Function,
        archInfo: Object,
        buttonTemplate: String,
        preventCreate: { type: Boolean, optional: true },
        preventEdit: { type: Boolean, optional: true },
        saveRecord: { type: Function, optional: true },
        removeRecord: { type: Function, optional: true },
        mode: { type: String, optional: true },
    };

    setup() {
        this.actionService = useService("action");
        this.dialogService = useService("dialog");
        this.notification = useService("notification");
        this.orm = useService("orm");
        this.router = useService("router");
        this.uiService = useService("ui");
        this.user = useService("user");

        this.model = useModel(this.props.Model, this.modelParams);
        this.pager = usePager();
        this.viewButtons = useViewButtons();

        this.compiler = useViewCompiler(
            this.props.Compiler || FormCompiler,
            this.processedArchInfo.arch
        );

        useSetupAction({
            getLocalState: () => this.getLocalState(),
            getContext: () => this.getActionContext(),
        });

        this.state = useState({
            showInvisible: false,
        });

        useBus(this.model, "update", this.render);
        useBus(this.model, "field-is-dirty", this.onFieldIsDirty);

        onMounted(this.onMounted);
        onWillUnmount(this.onWillUnmount);
        onError(this.onError);
    }
}
```

**组件特性**:
- **模板定义**: 使用FormView模板
- **子组件**: 集成布局、字段、按钮等组件
- **服务集成**: 集成多种核心服务
- **状态管理**: 管理控制器状态

## 核心功能

### 1. 模型参数配置

```javascript
get modelParams() {
    const { archInfo, fields, resModel, context, resId, resIds, mode } = this.props;

    return {
        config: {
            resModel,
            fields,
            activeFields: archInfo.activeFields,
            isMonoRecord: true,
            mode: mode || "readonly",
        },
        state: {
            context,
            resId,
            resIds,
            mode: mode || "readonly",
        },
    };
}
```

**配置功能**:
- **模型配置**: 配置关系模型参数
- **字段配置**: 设置活动字段
- **模式配置**: 设置表单模式（只读/编辑）
- **状态配置**: 配置上下文和记录ID

### 2. 记录操作

```javascript
// 保存记录
async saveRecord() {
    if (!this.model.root.isDirty) {
        return true;
    }

    try {
        await this.model.root.save();
        this.notification.add(_t("Record saved"), { type: "success" });
        return true;
    } catch (error) {
        this.handleSaveError(error);
        return false;
    }
}

// 删除记录
async deleteRecord() {
    const dialogProps = {
        body: deleteConfirmationMessage(1),
        confirm: async () => {
            await this.model.root.delete();
            this.notification.add(_t("Record deleted"), { type: "success" });
            this.actionService.doAction({ type: "ir.actions.act_window_close" });
        },
        cancel: () => {},
    };

    this.dialogService.add(ConfirmationDialog, dialogProps);
}

// 创建记录
async createRecord() {
    const { context } = this.props;

    const record = await this.model.root.switchMode("edit");
    return record;
}

// 编辑记录
async editRecord() {
    if (this.model.root.mode === "readonly") {
        await this.model.root.switchMode("edit");
    }
}
```

**记录操作功能**:
- **保存记录**: 保存表单数据到服务器
- **删除记录**: 删除当前记录
- **创建记录**: 创建新记录
- **编辑记录**: 切换到编辑模式

### 3. 字段验证

```javascript
// 验证字段
validateFields() {
    const invalidFields = [];

    for (const [fieldName, field] of Object.entries(this.model.root.activeFields)) {
        if (field.required && !this.model.root.data[fieldName]) {
            invalidFields.push(fieldName);
        }

        if (field.type === "email" && this.model.root.data[fieldName]) {
            if (!this.validateEmail(this.model.root.data[fieldName])) {
                invalidFields.push(fieldName);
            }
        }
    }

    return invalidFields;
}

// 验证邮箱
validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// 字段变化处理
onFieldIsDirty(ev) {
    const { fieldName, isDirty } = ev.detail;

    if (isDirty) {
        this.markFieldAsDirty(fieldName);
    } else {
        this.markFieldAsClean(fieldName);
    }
}
```

**验证功能**:
- **必填验证**: 验证必填字段
- **格式验证**: 验证字段格式
- **自定义验证**: 支持自定义验证规则
- **实时验证**: 实时验证字段变化

### 4. 按钮处理

```javascript
// 执行按钮操作
async executeButtonCallback(clickParams) {
    const { name, type, context, close } = clickParams;

    try {
        if (type === "object") {
            await this.executeObjectButton(name, context);
        } else if (type === "action") {
            await this.executeActionButton(name, context);
        }

        if (close) {
            this.actionService.doAction({ type: "ir.actions.act_window_close" });
        }
    } catch (error) {
        this.handleButtonError(error);
    }
}

// 执行对象按钮
async executeObjectButton(methodName, context) {
    const recordId = this.model.root.resId;
    const resModel = this.props.resModel;

    const result = await this.orm.call(resModel, methodName, [recordId], {
        context: makeContext([context, this.model.root.context]),
    });

    if (result && typeof result === "object") {
        this.actionService.doAction(result);
    }
}

// 执行动作按钮
async executeActionButton(actionId, context) {
    const action = await this.orm.call("ir.actions.actions", "read", [actionId]);

    if (action && action.length > 0) {
        this.actionService.doAction(action[0], {
            additionalContext: context,
        });
    }
}
```

**按钮处理功能**:
- **按钮执行**: 执行不同类型的按钮操作
- **对象方法**: 调用模型的对象方法
- **动作执行**: 执行预定义的动作
- **错误处理**: 处理按钮执行错误

### 5. 状态管理

```javascript
// 获取本地状态
getLocalState() {
    return {
        resId: this.model.root.resId,
        mode: this.model.root.mode,
        isDirty: this.model.root.isDirty,
        showInvisible: this.state.showInvisible,
    };
}

// 获取动作上下文
getActionContext() {
    return {
        ...this.model.root.context,
        active_id: this.model.root.resId,
        active_ids: [this.model.root.resId],
        active_model: this.props.resModel,
    };
}

// 切换显示不可见字段
toggleShowInvisible() {
    this.state.showInvisible = !this.state.showInvisible;
}

// 检查是否可以保存
canSave() {
    return this.model.root.isDirty && this.model.root.isValid;
}

// 检查是否可以编辑
canEdit() {
    return this.model.root.mode === "readonly" && !this.props.preventEdit;
}
```

**状态管理功能**:
- **本地状态**: 管理组件的本地状态
- **动作上下文**: 提供动作执行的上下文
- **可见性控制**: 控制字段的可见性
- **权限检查**: 检查操作权限
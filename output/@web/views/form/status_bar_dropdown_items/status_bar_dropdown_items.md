# StatusBarDropdownItems - 状态栏下拉项

## 概述

`status_bar_dropdown_items.js` 是 Odoo Web 客户端表单视图的状态栏下拉项组件，负责在状态栏中显示下拉菜单项。该模块包含16行代码，是一个简洁的OWL组件，专门用于继承状态栏按钮功能并以下拉菜单项的形式展示，具备组件继承、模板定制、下拉项集成等特性，是表单视图系统中状态栏下拉菜单的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/form/status_bar_dropdown_items/status_bar_dropdown_items.js`
- **行数**: 16
- **模块**: `@web/views/form/status_bar_dropdown_items/status_bar_dropdown_items`

## 依赖关系

```javascript
// 核心依赖
'@web/core/dropdown/dropdown_item'                     // 下拉菜单项组件
'@web/views/form/status_bar_buttons/status_bar_buttons' // 状态栏按钮组件
```

## 主要组件定义

### 1. StatusBarDropdownItems - 状态栏下拉项组件

```javascript
class StatusBarDropdownItems extends StatusBarButtons {
    static template = "web.StatusBarDropdownItems";
    static components = {
        DropdownItem,
    };
}
```

**组件特性**:
- **继承设计**: 继承自StatusBarButtons组件
- **专用模板**: 使用StatusBarDropdownItems专用模板
- **下拉项集成**: 集成DropdownItem组件
- **简洁实现**: 极简的代码实现

## 核心功能

### 1. 组件继承

```javascript
class StatusBarDropdownItems extends StatusBarButtons
```

**继承功能**:
- **功能复用**: 复用状态栏按钮的所有功能
- **行为继承**: 继承按钮的交互行为
- **数据处理**: 继承数据处理逻辑
- **事件处理**: 继承事件处理机制

### 2. 模板定制

```javascript
static template = "web.StatusBarDropdownItems";
```

**模板功能**:
- **专用模板**: 使用专门的下拉项模板
- **布局适配**: 适配下拉菜单的布局
- **样式定制**: 定制下拉项的样式
- **结构优化**: 优化下拉菜单结构

### 3. 组件集成

```javascript
static components = {
    DropdownItem,
};
```

**集成功能**:
- **下拉项**: 集成基础下拉菜单项组件
- **组件复用**: 复用现有的下拉组件
- **一致性**: 保持UI组件的一致性
- **标准化**: 使用标准化的下拉组件

## 使用场景

### 1. 状态栏下拉项管理器

```javascript
// 状态栏下拉项管理器
class StatusBarDropdownItemsManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置下拉项配置
        this.dropdownConfig = {
            enableGrouping: true,
            enableSorting: true,
            enableFiltering: true,
            enableCustomItems: true,
            maxVisibleItems: 10,
            showIcons: true,
            showDescriptions: false,
            enableKeyboardNavigation: true
        };
        
        // 设置项目注册表
        this.itemRegistry = new Map();
        
        // 设置分组配置
        this.groupConfig = new Map();
        
        // 设置下拉项统计
        this.dropdownStatistics = {
            totalItems: 0,
            visibleItems: 0,
            hiddenItems: 0,
            clickCount: 0,
            averageResponseTime: 0
        };
        
        this.initializeDropdownSystem();
    }
    
    // 初始化下拉项系统
    initializeDropdownSystem() {
        // 创建增强的状态栏下拉项
        this.createEnhancedStatusBarDropdownItems();
        
        // 设置项目管理系统
        this.setupItemManagementSystem();
        
        // 设置分组系统
        this.setupGroupingSystem();
        
        // 设置性能监控
        this.setupPerformanceMonitoring();
    }
    
    // 创建增强的状态栏下拉项
    createEnhancedStatusBarDropdownItems() {
        const originalDropdownItems = StatusBarDropdownItems;
        
        this.EnhancedStatusBarDropdownItems = class extends originalDropdownItems {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加交互功能
                this.addInteractionFeatures();
                
                // 添加可访问性支持
                this.addAccessibilitySupport();
                
                // 添加性能优化
                this.addPerformanceOptimizations();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = useState({
                    filteredItems: [],
                    groupedItems: new Map(),
                    selectedItem: null,
                    searchQuery: '',
                    isLoading: false,
                    hasError: false
                });
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 获取增强的下拉项
                this.getEnhancedDropdownItems = () => {
                    let items = this.getBaseDropdownItems();
                    
                    // 应用过滤
                    if (this.enhancedState.searchQuery) {
                        items = this.filterItems(items, this.enhancedState.searchQuery);
                    }
                    
                    // 应用排序
                    items = this.sortItems(items);
                    
                    // 应用分组
                    if (this.dropdownConfig.enableGrouping) {
                        items = this.groupItems(items);
                    }
                    
                    // 限制可见项数量
                    items = this.limitVisibleItems(items);
                    
                    return items;
                };
                
                // 获取基础下拉项
                this.getBaseDropdownItems = () => {
                    // 从父类获取基础项目
                    return this.getInheritedItems();
                };
                
                // 过滤项目
                this.filterItems = (items, query) => {
                    const lowerQuery = query.toLowerCase();
                    return items.filter(item => {
                        return item.label.toLowerCase().includes(lowerQuery) ||
                               (item.description && item.description.toLowerCase().includes(lowerQuery));
                    });
                };
                
                // 排序项目
                this.sortItems = (items) => {
                    if (!this.dropdownConfig.enableSorting) {
                        return items;
                    }
                    
                    return items.sort((a, b) => {
                        // 按优先级排序
                        if (a.priority !== b.priority) {
                            return (b.priority || 0) - (a.priority || 0);
                        }
                        
                        // 按标签排序
                        return a.label.localeCompare(b.label);
                    });
                };
                
                // 分组项目
                this.groupItems = (items) => {
                    const grouped = new Map();
                    
                    for (const item of items) {
                        const groupKey = item.group || 'default';
                        if (!grouped.has(groupKey)) {
                            grouped.set(groupKey, []);
                        }
                        grouped.get(groupKey).push(item);
                    }
                    
                    this.enhancedState.groupedItems = grouped;
                    return items;
                };
                
                // 限制可见项目
                this.limitVisibleItems = (items) => {
                    const maxVisible = this.dropdownConfig.maxVisibleItems;
                    if (items.length <= maxVisible) {
                        return items;
                    }
                    
                    const visibleItems = items.slice(0, maxVisible);
                    const hiddenCount = items.length - maxVisible;
                    
                    // 添加"更多"项目
                    visibleItems.push({
                        key: 'show_more',
                        label: `Show ${hiddenCount} more...`,
                        action: () => this.showAllItems(),
                        isSpecial: true
                    });
                    
                    return visibleItems;
                };
                
                // 显示所有项目
                this.showAllItems = () => {
                    this.dropdownConfig.maxVisibleItems = Infinity;
                    this.render();
                };
                
                // 增强的项目点击处理
                this.enhancedOnItemClick = async (item, event) => {
                    const startTime = performance.now();
                    
                    try {
                        // 设置选中项
                        this.enhancedState.selectedItem = item.key;
                        
                        // 设置加载状态
                        this.enhancedState.isLoading = true;
                        
                        // 执行原始点击处理
                        await this.onItemClick(item, event);
                        
                        // 记录点击统计
                        this.recordItemClick(item);
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordPerformance('item_click', endTime - startTime);
                        
                    } catch (error) {
                        this.handleItemClickError(item, error);
                    } finally {
                        this.enhancedState.isLoading = false;
                        this.enhancedState.selectedItem = null;
                    }
                };
                
                // 记录项目点击
                this.recordItemClick = (item) => {
                    this.dropdownStatistics.clickCount++;
                    
                    // 更新项目统计
                    const itemStats = this.itemRegistry.get(item.key) || { clickCount: 0 };
                    itemStats.clickCount++;
                    itemStats.lastClicked = Date.now();
                    this.itemRegistry.set(item.key, itemStats);
                };
                
                // 处理项目点击错误
                this.handleItemClickError = (item, error) => {
                    this.enhancedState.hasError = true;
                    console.error(`Error clicking item ${item.key}:`, error);
                    
                    // 显示错误通知
                    if (this.notification) {
                        this.notification.add(
                            `Failed to execute ${item.label}`,
                            { type: 'danger' }
                        );
                    }
                    
                    // 自动清除错误状态
                    setTimeout(() => {
                        this.enhancedState.hasError = false;
                    }, 3000);
                };
                
                // 搜索项目
                this.searchItems = (query) => {
                    this.enhancedState.searchQuery = query;
                    this.updateFilteredItems();
                };
                
                // 更新过滤项目
                this.updateFilteredItems = () => {
                    const items = this.getEnhancedDropdownItems();
                    this.enhancedState.filteredItems = items;
                };
                
                // 添加自定义项目
                this.addCustomItem = (item) => {
                    // 验证项目
                    if (!this.validateItem(item)) {
                        throw new Error('Invalid item configuration');
                    }
                    
                    // 添加到注册表
                    this.itemRegistry.set(item.key, {
                        ...item,
                        isCustom: true,
                        addedAt: Date.now()
                    });
                    
                    // 更新统计
                    this.dropdownStatistics.totalItems++;
                    
                    // 刷新显示
                    this.updateFilteredItems();
                };
                
                // 验证项目
                this.validateItem = (item) => {
                    return item &&
                           typeof item.key === 'string' &&
                           typeof item.label === 'string' &&
                           (typeof item.action === 'function' || item.action === undefined);
                };
                
                // 移除自定义项目
                this.removeCustomItem = (key) => {
                    const item = this.itemRegistry.get(key);
                    if (item && item.isCustom) {
                        this.itemRegistry.delete(key);
                        this.dropdownStatistics.totalItems--;
                        this.updateFilteredItems();
                    }
                };
                
                // 获取项目统计
                this.getItemStatistics = (key) => {
                    return this.itemRegistry.get(key) || null;
                };
                
                // 获取热门项目
                this.getPopularItems = (limit = 5) => {
                    const items = Array.from(this.itemRegistry.entries())
                        .map(([key, stats]) => ({ key, ...stats }))
                        .sort((a, b) => (b.clickCount || 0) - (a.clickCount || 0))
                        .slice(0, limit);
                    
                    return items;
                };
                
                // 记录性能指标
                this.recordPerformance = (operation, duration) => {
                    // 更新平均响应时间
                    const totalClicks = this.dropdownStatistics.clickCount;
                    this.dropdownStatistics.averageResponseTime = 
                        (this.dropdownStatistics.averageResponseTime * (totalClicks - 1) + duration) / totalClicks;
                };
            }
            
            addInteractionFeatures() {
                // 交互功能
                this.interactionManager = {
                    enableSearch: () => this.enableSearchFeature(),
                    enableKeyboardNavigation: () => this.enableKeyboardNavigation(),
                    enableHover: () => this.enableHoverEffects(),
                    enableTouch: () => this.enableTouchSupport()
                };
            }
            
            addAccessibilitySupport() {
                // 可访问性支持
                this.accessibilityManager = {
                    setAriaLabels: () => this.setAriaLabels(),
                    announceChanges: (message) => this.announceChanges(message),
                    handleKeyboardNavigation: (event) => this.handleKeyboardNavigation(event)
                };
            }
            
            addPerformanceOptimizations() {
                // 性能优化
                this.performanceManager = {
                    memoizeItems: () => this.memoizeItemCalculations(),
                    virtualizeList: () => this.enableVirtualization(),
                    debounceSearch: () => this.debounceSearchInput()
                };
            }
            
            // 启用搜索功能
            enableSearchFeature() {
                // 实现搜索功能
                this.searchEnabled = true;
            }
            
            // 启用键盘导航
            enableKeyboardNavigation() {
                // 实现键盘导航
                this.keyboardNavigationEnabled = true;
            }
            
            // 设置ARIA标签
            setAriaLabels() {
                const dropdownElement = this.rootRef.el;
                if (dropdownElement) {
                    dropdownElement.setAttribute('role', 'menu');
                    dropdownElement.setAttribute('aria-label', 'Status bar actions');
                }
            }
            
            // 宣布变化
            announceChanges(message) {
                const announcement = document.createElement('div');
                announcement.setAttribute('aria-live', 'polite');
                announcement.setAttribute('aria-atomic', 'true');
                announcement.className = 'sr-only';
                announcement.textContent = message;
                
                document.body.appendChild(announcement);
                setTimeout(() => document.body.removeChild(announcement), 1000);
            }
            
            // 处理键盘导航
            handleKeyboardNavigation(event) {
                switch (event.key) {
                    case 'ArrowDown':
                        event.preventDefault();
                        this.navigateItems(1);
                        break;
                    case 'ArrowUp':
                        event.preventDefault();
                        this.navigateItems(-1);
                        break;
                    case 'Enter':
                    case ' ':
                        event.preventDefault();
                        this.activateCurrentItem();
                        break;
                    case 'Escape':
                        event.preventDefault();
                        this.closeDropdown();
                        break;
                }
            }
            
            // 导航项目
            navigateItems(direction) {
                const items = this.enhancedState.filteredItems;
                const currentIndex = items.findIndex(item => item.key === this.enhancedState.selectedItem);
                const newIndex = Math.max(0, Math.min(items.length - 1, currentIndex + direction));
                
                if (items[newIndex]) {
                    this.enhancedState.selectedItem = items[newIndex].key;
                }
            }
            
            // 激活当前项目
            activateCurrentItem() {
                const selectedItem = this.enhancedState.filteredItems.find(
                    item => item.key === this.enhancedState.selectedItem
                );
                
                if (selectedItem) {
                    this.enhancedOnItemClick(selectedItem, new Event('click'));
                }
            }
            
            // 关闭下拉菜单
            closeDropdown() {
                // 实现关闭逻辑
                this.enhancedState.selectedItem = null;
            }
            
            // 重写原始方法
            onItemClick(item, event) {
                return this.enhancedOnItemClick(item, event);
            }
            
            // 获取下拉项统计
            getDropdownStatistics() {
                return {
                    ...this.dropdownStatistics,
                    registeredItemCount: this.itemRegistry.size,
                    filteredItemCount: this.enhancedState.filteredItems.length,
                    groupCount: this.enhancedState.groupedItems.size
                };
            }
            
            // 组件销毁时清理
            onWillDestroy() {
                // 清理定时器和监听器
                if (this.searchDebounceTimer) {
                    clearTimeout(this.searchDebounceTimer);
                }
            }
        };
    }
    
    // 设置项目管理系统
    setupItemManagementSystem() {
        // 默认项目配置
        this.defaultItems = [
            {
                key: 'edit',
                label: 'Edit',
                icon: 'fa-edit',
                priority: 100,
                group: 'actions'
            },
            {
                key: 'duplicate',
                label: 'Duplicate',
                icon: 'fa-copy',
                priority: 90,
                group: 'actions'
            },
            {
                key: 'archive',
                label: 'Archive',
                icon: 'fa-archive',
                priority: 80,
                group: 'actions'
            },
            {
                key: 'delete',
                label: 'Delete',
                icon: 'fa-trash',
                priority: 70,
                group: 'actions'
            }
        ];
        
        // 注册默认项目
        for (const item of this.defaultItems) {
            this.itemRegistry.set(item.key, item);
        }
    }
    
    // 设置分组系统
    setupGroupingSystem() {
        // 分组配置
        this.groupConfig.set('actions', {
            label: 'Actions',
            icon: 'fa-cog',
            priority: 100
        });
        
        this.groupConfig.set('navigation', {
            label: 'Navigation',
            icon: 'fa-compass',
            priority: 90
        });
        
        this.groupConfig.set('tools', {
            label: 'Tools',
            icon: 'fa-wrench',
            priority: 80
        });
    }
    
    // 设置性能监控
    setupPerformanceMonitoring() {
        this.performanceConfig = {
            enableMonitoring: true,
            sampleRate: 0.1,
            thresholds: {
                itemClickTime: 100, // 100ms
                searchTime: 50      // 50ms
            }
        };
    }
    
    // 创建状态栏下拉项
    createStatusBarDropdownItems(props) {
        return new this.EnhancedStatusBarDropdownItems(props);
    }
    
    // 获取项目注册表
    getItemRegistry() {
        return this.itemRegistry;
    }
    
    // 获取分组配置
    getGroupConfig() {
        return this.groupConfig;
    }
    
    // 获取下拉项统计
    getDropdownStatistics() {
        return {
            ...this.dropdownStatistics,
            registeredItemCount: this.itemRegistry.size,
            groupCount: this.groupConfig.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理注册表
        this.itemRegistry.clear();
        
        // 清理分组配置
        this.groupConfig.clear();
        
        // 重置统计
        this.dropdownStatistics = {
            totalItems: 0,
            visibleItems: 0,
            hiddenItems: 0,
            clickCount: 0,
            averageResponseTime: 0
        };
    }
}

// 使用示例
const dropdownItemsManager = new StatusBarDropdownItemsManager();

// 创建状态栏下拉项
const statusBarDropdownItems = dropdownItemsManager.createStatusBarDropdownItems({
    record: record,
    buttons: buttons
});

// 添加自定义项目
statusBarDropdownItems.addCustomItem({
    key: 'custom_action',
    label: 'Custom Action',
    icon: 'fa-star',
    action: () => console.log('Custom action executed'),
    group: 'tools',
    priority: 95
});

// 获取统计信息
const stats = dropdownItemsManager.getDropdownStatistics();
console.log('Status bar dropdown items statistics:', stats);
```

## 技术特点

### 1. 继承设计
- **功能复用**: 完全继承状态栏按钮功能
- **行为一致**: 保持与按钮一致的行为
- **代码简洁**: 极简的代码实现
- **维护性**: 易于维护和扩展

### 2. 组件集成
- **下拉项**: 集成标准下拉菜单项组件
- **模板定制**: 定制下拉菜单的模板
- **样式适配**: 适配下拉菜单的样式
- **交互优化**: 优化下拉菜单的交互

### 3. 轻量级实现
- **最小代码**: 仅16行代码的实现
- **高效执行**: 高效的代码执行
- **快速加载**: 快速的组件加载
- **内存友好**: 友好的内存使用

### 4. 扩展性
- **组件扩展**: 支持组件功能扩展
- **模板扩展**: 支持模板的扩展
- **行为扩展**: 支持行为的扩展
- **样式扩展**: 支持样式的扩展

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **类继承**: 继承状态栏按钮类
- **功能继承**: 继承所有基础功能
- **行为继承**: 继承交互行为

### 2. 适配器模式 (Adapter Pattern)
- **接口适配**: 适配下拉菜单接口
- **组件适配**: 适配不同的组件
- **样式适配**: 适配不同的样式

### 3. 模板方法模式 (Template Method Pattern)
- **模板定制**: 定制专用模板
- **结构复用**: 复用基础结构
- **行为定制**: 定制特定行为

### 4. 组合模式 (Composition Pattern)
- **组件组合**: 组合下拉菜单组件
- **功能组合**: 组合不同功能
- **灵活配置**: 灵活的组合配置

## 注意事项

1. **继承关系**: 确保正确继承基础组件
2. **模板一致性**: 保持模板的一致性
3. **样式兼容**: 确保样式的兼容性
4. **行为一致**: 保持行为的一致性

## 扩展建议

1. **搜索功能**: 添加下拉项搜索功能
2. **分组显示**: 支持下拉项的分组显示
3. **图标支持**: 添加下拉项的图标支持
4. **快捷键**: 支持下拉项的快捷键
5. **动态加载**: 支持下拉项的动态加载

该状态栏下拉项组件为Odoo Web客户端提供了简洁而有效的下拉菜单功能，通过继承设计和组件集成确保了功能的一致性和可维护性。

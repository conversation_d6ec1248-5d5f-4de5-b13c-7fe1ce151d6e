# StatusBarButtons - 状态栏按钮组件

## 概述

`status_bar_buttons.js` 是 Odoo Web 客户端表单视图的状态栏按钮组件，负责在表单状态栏中显示和管理各种操作按钮。该模块包含30行代码，是一个OWL组件，专门用于渲染状态栏中的按钮集合，具备插槽管理、可见性控制、下拉菜单集成等特性，是表单视图系统中状态栏操作的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/form/status_bar_buttons/status_bar_buttons.js`
- **行数**: 30
- **模块**: `@web/views/form/status_bar_buttons/status_bar_buttons`

## 依赖关系

```javascript
// 核心依赖
'@web/core/dropdown/dropdown'       // 下拉菜单组件
'@web/core/dropdown/dropdown_item'  // 下拉菜单项组件
'@odoo/owl'                         // OWL框架
```

## 主要组件定义

### 1. StatusBarButtons - 状态栏按钮组件

```javascript
class StatusBarButtons extends Component {
    static template = "web.StatusBarButtons";
    static components = {
        Dropdown,
        DropdownItem,
    };
    static props = {
        slots: { type: Object, optional: true },
    };
}
```

**组件特性**:
- **专用模板**: 使用StatusBarButtons专用模板
- **下拉集成**: 集成下拉菜单和下拉项组件
- **插槽支持**: 支持可选的插槽对象
- **简洁设计**: 简洁而专注的组件设计

## 核心功能

### 1. 插槽属性配置

```javascript
static props = {
    slots: { type: Object, optional: true },
};
```

**属性功能**:
- **插槽对象**: 接收插槽对象配置
- **可选配置**: 插槽为可选配置项
- **类型检查**: 确保插槽为对象类型
- **灵活扩展**: 支持灵活的内容扩展

### 2. 可见插槽名称计算

```javascript
get visibleSlotNames() {
    if (!this.props.slots) {
        return [];
    }
    return Object.entries(this.props.slots)
        .filter((entry) => entry[1].isVisible)
        .map((entry) => entry[0]);
}
```

**可见性功能**:
- **空值检查**: 检查插槽对象是否存在
- **可见性过滤**: 过滤出可见的插槽
- **名称提取**: 提取可见插槽的名称
- **数组返回**: 返回可见插槽名称数组

### 3. 组件集成

```javascript
static components = {
    Dropdown,
    DropdownItem,
};
```

**集成功能**:
- **下拉菜单**: 集成下拉菜单组件
- **下拉项**: 集成下拉菜单项组件
- **组件复用**: 复用现有的UI组件
- **一致性**: 保持UI组件的一致性

## 使用场景

### 1. 状态栏按钮管理器

```javascript
// 状态栏按钮管理器
class StatusBarButtonsManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置按钮配置
        this.buttonConfig = {
            enableGrouping: true,
            enableSorting: true,
            enableVisibilityControl: true,
            enableCustomButtons: true,
            maxVisibleButtons: 5,
            showIcons: true,
            showLabels: true,
            enableTooltips: true
        };
        
        // 设置按钮注册表
        this.buttonRegistry = new Map();
        
        // 设置插槽管理器
        this.slotManager = new Map();
        
        // 设置按钮统计
        this.buttonStatistics = {
            totalButtons: 0,
            visibleButtons: 0,
            hiddenButtons: 0,
            clickCount: 0,
            averageResponseTime: 0
        };
        
        this.initializeButtonSystem();
    }
    
    // 初始化按钮系统
    initializeButtonSystem() {
        // 创建增强的状态栏按钮
        this.createEnhancedStatusBarButtons();
        
        // 设置按钮管理系统
        this.setupButtonManagementSystem();
        
        // 设置插槽系统
        this.setupSlotSystem();
        
        // 设置性能监控
        this.setupPerformanceMonitoring();
    }
    
    // 创建增强的状态栏按钮
    createEnhancedStatusBarButtons() {
        const originalButtons = StatusBarButtons;
        
        this.EnhancedStatusBarButtons = class extends originalButtons {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加交互功能
                this.addInteractionFeatures();
                
                // 添加可访问性支持
                this.addAccessibilitySupport();
                
                // 添加性能优化
                this.addPerformanceOptimizations();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = useState({
                    activeButton: null,
                    loadingButtons: new Set(),
                    disabledButtons: new Set(),
                    groupedButtons: new Map(),
                    sortedButtons: [],
                    buttonStates: new Map()
                });
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的可见插槽名称计算
                this.enhancedGetVisibleSlotNames = () => {
                    const baseSlotNames = this.visibleSlotNames;
                    
                    // 应用排序
                    const sortedSlotNames = this.sortSlotNames(baseSlotNames);
                    
                    // 应用分组
                    if (this.buttonConfig.enableGrouping) {
                        return this.groupSlotNames(sortedSlotNames);
                    }
                    
                    return sortedSlotNames;
                };
                
                // 排序插槽名称
                this.sortSlotNames = (slotNames) => {
                    if (!this.buttonConfig.enableSorting) {
                        return slotNames;
                    }
                    
                    return slotNames.sort((a, b) => {
                        const buttonA = this.getButtonInfo(a);
                        const buttonB = this.getButtonInfo(b);
                        
                        // 按优先级排序
                        const priorityA = buttonA?.priority || 0;
                        const priorityB = buttonB?.priority || 0;
                        
                        if (priorityA !== priorityB) {
                            return priorityB - priorityA;
                        }
                        
                        // 按名称排序
                        return a.localeCompare(b);
                    });
                };
                
                // 分组插槽名称
                this.groupSlotNames = (slotNames) => {
                    const grouped = new Map();
                    
                    for (const slotName of slotNames) {
                        const buttonInfo = this.getButtonInfo(slotName);
                        const groupKey = buttonInfo?.group || 'default';
                        
                        if (!grouped.has(groupKey)) {
                            grouped.set(groupKey, []);
                        }
                        grouped.get(groupKey).push(slotName);
                    }
                    
                    this.enhancedState.groupedButtons = grouped;
                    return slotNames;
                };
                
                // 获取按钮信息
                this.getButtonInfo = (slotName) => {
                    return this.buttonRegistry.get(slotName);
                };
                
                // 处理按钮点击
                this.onButtonClick = async (slotName, event) => {
                    const startTime = performance.now();
                    
                    try {
                        // 设置活动按钮
                        this.enhancedState.activeButton = slotName;
                        
                        // 设置加载状态
                        this.enhancedState.loadingButtons.add(slotName);
                        
                        // 执行按钮动作
                        await this.executeButtonAction(slotName, event);
                        
                        // 记录点击统计
                        this.recordButtonClick(slotName);
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordPerformance('button_click', endTime - startTime);
                        
                    } catch (error) {
                        this.handleButtonClickError(slotName, error);
                    } finally {
                        this.enhancedState.loadingButtons.delete(slotName);
                        this.enhancedState.activeButton = null;
                    }
                };
                
                // 执行按钮动作
                this.executeButtonAction = async (slotName, event) => {
                    const buttonInfo = this.getButtonInfo(slotName);
                    
                    if (buttonInfo && buttonInfo.action) {
                        await buttonInfo.action(event, this.props);
                    }
                };
                
                // 记录按钮点击
                this.recordButtonClick = (slotName) => {
                    this.buttonStatistics.clickCount++;
                    
                    // 更新按钮统计
                    const buttonInfo = this.getButtonInfo(slotName);
                    if (buttonInfo) {
                        buttonInfo.clickCount = (buttonInfo.clickCount || 0) + 1;
                        buttonInfo.lastClicked = Date.now();
                    }
                };
                
                // 处理按钮点击错误
                this.handleButtonClickError = (slotName, error) => {
                    console.error(`Error clicking button ${slotName}:`, error);
                    
                    // 显示错误通知
                    if (this.notification) {
                        this.notification.add(
                            `Failed to execute ${slotName}`,
                            { type: 'danger' }
                        );
                    }
                };
                
                // 设置按钮状态
                this.setButtonState = (slotName, state) => {
                    this.enhancedState.buttonStates.set(slotName, state);
                };
                
                // 获取按钮状态
                this.getButtonState = (slotName) => {
                    return this.enhancedState.buttonStates.get(slotName) || 'normal';
                };
                
                // 启用按钮
                this.enableButton = (slotName) => {
                    this.enhancedState.disabledButtons.delete(slotName);
                    this.setButtonState(slotName, 'normal');
                };
                
                // 禁用按钮
                this.disableButton = (slotName) => {
                    this.enhancedState.disabledButtons.add(slotName);
                    this.setButtonState(slotName, 'disabled');
                };
                
                // 检查按钮是否可见
                this.isButtonVisible = (slotName) => {
                    const slot = this.props.slots?.[slotName];
                    return slot && slot.isVisible;
                };
                
                // 检查按钮是否启用
                this.isButtonEnabled = (slotName) => {
                    return !this.enhancedState.disabledButtons.has(slotName);
                };
                
                // 检查按钮是否加载中
                this.isButtonLoading = (slotName) => {
                    return this.enhancedState.loadingButtons.has(slotName);
                };
                
                // 获取按钮CSS类
                this.getButtonClass = (slotName) => {
                    const classes = ['o_status_bar_button'];
                    
                    const state = this.getButtonState(slotName);
                    classes.push(`o_button_${state}`);
                    
                    if (this.enhancedState.activeButton === slotName) {
                        classes.push('o_button_active');
                    }
                    
                    if (this.isButtonLoading(slotName)) {
                        classes.push('o_button_loading');
                    }
                    
                    if (!this.isButtonEnabled(slotName)) {
                        classes.push('o_button_disabled');
                    }
                    
                    return classes.join(' ');
                };
                
                // 添加自定义按钮
                this.addCustomButton = (slotName, buttonConfig) => {
                    // 验证按钮配置
                    if (!this.validateButtonConfig(buttonConfig)) {
                        throw new Error('Invalid button configuration');
                    }
                    
                    // 注册按钮
                    this.buttonRegistry.set(slotName, {
                        ...buttonConfig,
                        isCustom: true,
                        addedAt: Date.now()
                    });
                    
                    // 更新统计
                    this.buttonStatistics.totalButtons++;
                    
                    // 刷新显示
                    this.render();
                };
                
                // 验证按钮配置
                this.validateButtonConfig = (config) => {
                    return config &&
                           typeof config.label === 'string' &&
                           (typeof config.action === 'function' || config.action === undefined);
                };
                
                // 移除自定义按钮
                this.removeCustomButton = (slotName) => {
                    const buttonInfo = this.getButtonInfo(slotName);
                    if (buttonInfo && buttonInfo.isCustom) {
                        this.buttonRegistry.delete(slotName);
                        this.buttonStatistics.totalButtons--;
                        this.render();
                    }
                };
                
                // 获取按钮工具提示
                this.getButtonTooltip = (slotName) => {
                    const buttonInfo = this.getButtonInfo(slotName);
                    return buttonInfo?.tooltip || buttonInfo?.label || slotName;
                };
                
                // 获取按钮图标
                this.getButtonIcon = (slotName) => {
                    const buttonInfo = this.getButtonInfo(slotName);
                    return buttonInfo?.icon || null;
                };
                
                // 记录性能指标
                this.recordPerformance = (operation, duration) => {
                    // 更新平均响应时间
                    const totalClicks = this.buttonStatistics.clickCount;
                    this.buttonStatistics.averageResponseTime = 
                        (this.buttonStatistics.averageResponseTime * (totalClicks - 1) + duration) / totalClicks;
                };
            }
            
            addInteractionFeatures() {
                // 交互功能
                this.interactionManager = {
                    enableHover: () => this.enableHoverEffects(),
                    enableKeyboard: () => this.enableKeyboardNavigation(),
                    enableTouch: () => this.enableTouchSupport(),
                    enableDragDrop: () => this.enableDragDropReordering()
                };
            }
            
            addAccessibilitySupport() {
                // 可访问性支持
                this.accessibilityManager = {
                    setAriaLabels: () => this.setAriaLabels(),
                    announceChanges: (message) => this.announceChanges(message),
                    handleKeyboardNavigation: (event) => this.handleKeyboardNavigation(event)
                };
            }
            
            addPerformanceOptimizations() {
                // 性能优化
                this.performanceManager = {
                    memoizeSlots: () => this.memoizeSlotCalculations(),
                    debounceUpdates: () => this.debounceStateUpdates(),
                    virtualizeButtons: () => this.enableButtonVirtualization()
                };
            }
            
            // 启用悬停效果
            enableHoverEffects() {
                // 实现悬停效果
                this.hoverEffectsEnabled = true;
            }
            
            // 启用键盘导航
            enableKeyboardNavigation() {
                // 实现键盘导航
                this.keyboardNavigationEnabled = true;
            }
            
            // 设置ARIA标签
            setAriaLabels() {
                const buttonsElement = this.rootRef.el;
                if (buttonsElement) {
                    buttonsElement.setAttribute('role', 'toolbar');
                    buttonsElement.setAttribute('aria-label', 'Status bar actions');
                }
            }
            
            // 宣布变化
            announceChanges(message) {
                const announcement = document.createElement('div');
                announcement.setAttribute('aria-live', 'polite');
                announcement.setAttribute('aria-atomic', 'true');
                announcement.className = 'sr-only';
                announcement.textContent = message;
                
                document.body.appendChild(announcement);
                setTimeout(() => document.body.removeChild(announcement), 1000);
            }
            
            // 处理键盘导航
            handleKeyboardNavigation(event) {
                const visibleSlots = this.enhancedGetVisibleSlotNames();
                const currentIndex = visibleSlots.indexOf(this.enhancedState.activeButton);
                
                switch (event.key) {
                    case 'ArrowLeft':
                        event.preventDefault();
                        this.navigateButtons(visibleSlots, currentIndex, -1);
                        break;
                    case 'ArrowRight':
                        event.preventDefault();
                        this.navigateButtons(visibleSlots, currentIndex, 1);
                        break;
                    case 'Enter':
                    case ' ':
                        event.preventDefault();
                        if (this.enhancedState.activeButton) {
                            this.onButtonClick(this.enhancedState.activeButton, event);
                        }
                        break;
                    case 'Home':
                        event.preventDefault();
                        this.focusButton(visibleSlots[0]);
                        break;
                    case 'End':
                        event.preventDefault();
                        this.focusButton(visibleSlots[visibleSlots.length - 1]);
                        break;
                }
            }
            
            // 导航按钮
            navigateButtons(visibleSlots, currentIndex, direction) {
                const newIndex = Math.max(0, Math.min(visibleSlots.length - 1, currentIndex + direction));
                this.focusButton(visibleSlots[newIndex]);
            }
            
            // 聚焦按钮
            focusButton(slotName) {
                this.enhancedState.activeButton = slotName;
                
                // 聚焦到对应的DOM元素
                const buttonElement = this.rootRef.el?.querySelector(`[data-slot="${slotName}"]`);
                if (buttonElement && buttonElement.focus) {
                    buttonElement.focus();
                }
            }
            
            // 重写getter方法
            get visibleSlotNames() {
                return this.enhancedGetVisibleSlotNames();
            }
            
            // 获取按钮统计
            getButtonStatistics() {
                return {
                    ...this.buttonStatistics,
                    registeredButtonCount: this.buttonRegistry.size,
                    visibleButtonCount: this.visibleSlotNames.length,
                    groupCount: this.enhancedState.groupedButtons.size
                };
            }
            
            // 组件挂载后
            onMounted() {
                // 设置ARIA标签
                this.setAriaLabels();
                
                // 更新统计
                this.updateButtonStatistics();
            }
            
            // 更新按钮统计
            updateButtonStatistics() {
                const visibleSlots = this.visibleSlotNames;
                this.buttonStatistics.visibleButtons = visibleSlots.length;
                this.buttonStatistics.hiddenButtons = this.buttonStatistics.totalButtons - visibleSlots.length;
            }
            
            // 组件销毁前
            onWillDestroy() {
                // 清理定时器和监听器
                if (this.debounceTimer) {
                    clearTimeout(this.debounceTimer);
                }
            }
        };
    }
    
    // 设置按钮管理系统
    setupButtonManagementSystem() {
        // 默认按钮配置
        this.defaultButtons = [
            {
                key: 'save',
                label: 'Save',
                icon: 'fa-save',
                priority: 100,
                group: 'primary',
                tooltip: 'Save the current record'
            },
            {
                key: 'discard',
                label: 'Discard',
                icon: 'fa-undo',
                priority: 90,
                group: 'primary',
                tooltip: 'Discard changes'
            },
            {
                key: 'edit',
                label: 'Edit',
                icon: 'fa-edit',
                priority: 80,
                group: 'secondary',
                tooltip: 'Edit the record'
            },
            {
                key: 'duplicate',
                label: 'Duplicate',
                icon: 'fa-copy',
                priority: 70,
                group: 'secondary',
                tooltip: 'Duplicate the record'
            }
        ];
        
        // 注册默认按钮
        for (const button of this.defaultButtons) {
            this.buttonRegistry.set(button.key, button);
        }
    }
    
    // 设置插槽系统
    setupSlotSystem() {
        this.slotConfig = {
            enableDynamicSlots: true,
            enableSlotValidation: true,
            enableSlotCaching: true,
            maxSlots: 20
        };
    }
    
    // 设置性能监控
    setupPerformanceMonitoring() {
        this.performanceConfig = {
            enableMonitoring: true,
            sampleRate: 0.1,
            thresholds: {
                buttonClickTime: 100, // 100ms
                renderTime: 50        // 50ms
            }
        };
    }
    
    // 创建状态栏按钮
    createStatusBarButtons(props) {
        return new this.EnhancedStatusBarButtons(props);
    }
    
    // 注册按钮
    registerButton(key, config) {
        this.buttonRegistry.set(key, config);
        this.buttonStatistics.totalButtons++;
    }
    
    // 注销按钮
    unregisterButton(key) {
        if (this.buttonRegistry.delete(key)) {
            this.buttonStatistics.totalButtons--;
        }
    }
    
    // 获取按钮注册表
    getButtonRegistry() {
        return this.buttonRegistry;
    }
    
    // 获取插槽管理器
    getSlotManager() {
        return this.slotManager;
    }
    
    // 获取按钮统计
    getButtonStatistics() {
        return {
            ...this.buttonStatistics,
            registeredButtonCount: this.buttonRegistry.size,
            slotCount: this.slotManager.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理注册表
        this.buttonRegistry.clear();
        
        // 清理插槽管理器
        this.slotManager.clear();
        
        // 重置统计
        this.buttonStatistics = {
            totalButtons: 0,
            visibleButtons: 0,
            hiddenButtons: 0,
            clickCount: 0,
            averageResponseTime: 0
        };
    }
}

// 使用示例
const buttonsManager = new StatusBarButtonsManager();

// 创建状态栏按钮
const statusBarButtons = buttonsManager.createStatusBarButtons({
    slots: {
        save: { isVisible: true },
        discard: { isVisible: true },
        edit: { isVisible: false }
    }
});

// 注册自定义按钮
buttonsManager.registerButton('custom_action', {
    label: 'Custom Action',
    icon: 'fa-star',
    priority: 95,
    group: 'custom',
    tooltip: 'Execute custom action',
    action: () => console.log('Custom action executed')
});

// 获取统计信息
const stats = buttonsManager.getButtonStatistics();
console.log('Status bar buttons statistics:', stats);
```

## 技术特点

### 1. 插槽管理
- **插槽支持**: 支持灵活的插槽机制
- **可见性控制**: 动态控制按钮可见性
- **名称提取**: 提取可见插槽名称
- **状态管理**: 管理插槽的各种状态

### 2. 组件集成
- **下拉集成**: 集成下拉菜单组件
- **组件复用**: 复用现有UI组件
- **一致性**: 保持组件的一致性
- **标准化**: 使用标准化的组件

### 3. 简洁设计
- **轻量级**: 仅30行代码的轻量实现
- **专注功能**: 专注于按钮管理功能
- **清晰逻辑**: 清晰的业务逻辑
- **易于维护**: 易于理解和维护

### 4. 扩展性
- **属性扩展**: 支持属性的灵活扩展
- **功能扩展**: 支持功能的模块化扩展
- **组件扩展**: 支持组件的动态扩展
- **配置驱动**: 配置驱动的功能开关

## 设计模式

### 1. 组件模式 (Component Pattern)
- **封装性**: 封装按钮管理逻辑
- **可复用**: 高度可复用的组件
- **接口标准**: 标准化的组件接口

### 2. 插槽模式 (Slot Pattern)
- **内容分发**: 通过插槽分发内容
- **灵活布局**: 支持灵活的布局
- **动态内容**: 支持动态内容插入

### 3. 策略模式 (Strategy Pattern)
- **可见性策略**: 不同的可见性控制策略
- **渲染策略**: 不同的按钮渲染策略
- **交互策略**: 不同的用户交互策略

### 4. 观察者模式 (Observer Pattern)
- **状态监听**: 监听插槽状态变化
- **自动更新**: 自动更新按钮显示
- **事件响应**: 响应用户交互事件

## 注意事项

1. **插槽验证**: 确保插槽对象的有效性
2. **可见性控制**: 正确处理按钮的可见性
3. **性能考虑**: 避免频繁的插槽计算
4. **内存管理**: 及时清理不需要的插槽

## 扩展建议

1. **按钮分组**: 添加按钮分组功能
2. **权限控制**: 添加按钮权限控制
3. **动画效果**: 添加按钮状态变化动画
4. **快捷键**: 支持按钮的快捷键
5. **主题支持**: 支持不同的视觉主题

该状态栏按钮组件为Odoo Web客户端提供了灵活的按钮管理功能，通过插槽机制和组件集成确保了按钮显示的灵活性和一致性。

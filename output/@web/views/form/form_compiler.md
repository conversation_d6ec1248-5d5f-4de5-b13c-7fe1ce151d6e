# FormCompiler - 表单编译器

## 概述

`form_compiler.js` 是 Odoo Web 客户端表单视图的编译器组件，负责将表单视图的XML架构编译为可执行的模板代码。该模块包含715行代码，是一个专门的编译器类，继承自ViewCompiler，用于处理表单特有的编译逻辑，具备XML节点编译、模板生成、属性处理、组件注册等特性，是表单视图系统中模板编译的核心工具。

## 文件信息
- **路径**: `/web/static/src/views/form/form_compiler.js`
- **行数**: 715
- **模块**: `@web/views/form/form_compiler`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                // 注册表服务
'@web/core/ui/ui_service'           // UI服务
'@web/core/utils/xml'               // XML工具
'@web/views/utils'                  // 视图工具
'@web/views/view_compiler'          // 视图编译器基类
'@web/core/utils/strings'           // 字符串工具
```

## 核心工具函数

### 1. 属性处理函数

```javascript
// 追加属性格式化字符串
function appendAttf(el, attr, string) {
    const attrKey = `t-attf-${attr}`;
    const attrVal = el.getAttribute(attrKey);
    el.setAttribute(attrKey, appendToExpr(attrVal, string));
}

// 追加到表达式
function appendToExpr(expr, string) {
    const re = /{{.*}}/;
    const oldString = re.exec(expr);
    return oldString ? `${oldString} {{${string} }}` : `{{${string} }}`;
}

// 对象转字符串
function objectToString(obj) {
    return `{${Object.entries(obj)
        .map((t) => t.join(":"))
        .join(",")}}`;
}
```

**工具函数功能**:
- **属性格式化**: 处理模板属性的格式化
- **表达式追加**: 向现有表达式追加新内容
- **对象序列化**: 将对象转换为字符串表示

## 主要编译器定义

### 1. FormCompiler - 表单编译器

```javascript
class FormCompiler extends ViewCompiler {
    setup() {
        super.setup();
        this.compilers = compilersRegistry.content;
    }

    compile(node, params = {}) {
        const { isSubView = false } = params;

        if (!isSubView) {
            this.addLine(`<div class="o_form_view">`);
        }

        const compiled = this.compileNode(node, params);

        if (!isSubView) {
            this.addLine(`</div>`);
        }

        return compiled;
    }

    compileNode(node, params = {}) {
        const tagName = node.tagName;
        const compiler = this.compilers[tagName];

        if (compiler) {
            return compiler.call(this, node, params);
        } else {
            return super.compileNode(node, params);
        }
    }
}
```

**编译器特性**:
- **继承设计**: 继承ViewCompiler基类
- **编译器注册**: 使用注册表管理编译器
- **节点编译**: 编译不同类型的XML节点
- **参数传递**: 支持编译参数传递

## 核心编译功能

### 1. 表单节点编译

```javascript
// 编译form节点
compileForm(node, params) {
    const { class: className } = params;

    this.addLine(`<form class="o_form_view ${className || ''}">`);

    // 编译子节点
    for (const child of node.children) {
        this.compileNode(child, params);
    }

    this.addLine(`</form>`);
}

// 编译sheet节点
compileSheet(node, params) {
    this.addLine(`<div class="o_form_sheet_bg">`);
    this.addLine(`<div class="o_form_sheet">`);

    // 编译子节点
    for (const child of node.children) {
        this.compileNode(child, params);
    }

    this.addLine(`</div>`);
    this.addLine(`</div>`);
}
```

**表单编译功能**:
- **表单容器**: 编译表单容器元素
- **样式类**: 添加适当的CSS类
- **子节点处理**: 递归编译子节点
- **结构生成**: 生成正确的HTML结构

### 2. 字段编译

```javascript
// 编译field节点
compileField(node, params) {
    const fieldId = node.getAttribute('field_id');
    const fieldName = node.getAttribute('name');
    const widget = node.getAttribute('widget');

    // 生成字段组件
    this.addLine(`<Field`);
    this.addLine(`  name="${fieldName}"`);
    this.addLine(`  record="props.record"`);

    if (fieldId) {
        this.addLine(`  fieldId="${fieldId}"`);
    }

    if (widget) {
        this.addLine(`  widget="${widget}"`);
    }

    // 添加修饰符
    this.compileFieldModifiers(node);

    this.addLine(`/>`);
}

// 编译字段修饰符
compileFieldModifiers(node) {
    const modifiers = ['invisible', 'readonly', 'required'];

    for (const modifier of modifiers) {
        const value = getModifier(node, modifier);
        if (value !== undefined) {
            this.addLine(`  ${modifier}="${value}"`);
        }
    }
}
```

**字段编译功能**:
- **字段组件**: 生成Field组件代码
- **属性传递**: 传递字段属性
- **修饰符处理**: 处理字段修饰符
- **小部件支持**: 支持自定义小部件

### 3. 分组编译

```javascript
// 编译group节点
compileGroup(node, params) {
    const colspan = node.getAttribute('colspan') || '2';
    const string = node.getAttribute('string');

    this.addLine(`<OuterGroup`);
    this.addLine(`  colspan="${colspan}"`);

    if (string) {
        this.addLine(`  string="${string}"`);
    }

    this.addLine(`>`);

    // 编译子节点
    this.compileGroupChildren(node, params);

    this.addLine(`</OuterGroup>`);
}

// 编译分组子节点
compileGroupChildren(node, params) {
    let currentInnerGroup = null;

    for (const child of node.children) {
        if (child.tagName === 'field') {
            if (!currentInnerGroup) {
                this.addLine(`<InnerGroup>`);
                currentInnerGroup = true;
            }
            this.compileNode(child, params);
        } else {
            if (currentInnerGroup) {
                this.addLine(`</InnerGroup>`);
                currentInnerGroup = null;
            }
            this.compileNode(child, params);
        }
    }

    if (currentInnerGroup) {
        this.addLine(`</InnerGroup>`);
    }
}
```

**分组编译功能**:
- **外部分组**: 编译OuterGroup组件
- **内部分组**: 自动生成InnerGroup
- **列跨度**: 处理列跨度属性
- **分组标题**: 处理分组标题

### 4. 按钮编译

```javascript
// 编译button节点
compileButton(node, params) {
    const name = node.getAttribute('name');
    const type = node.getAttribute('type');
    const string = node.getAttribute('string');
    const icon = node.getAttribute('icon');

    this.addLine(`<ViewButton`);
    this.addLine(`  name="${name}"`);
    this.addLine(`  type="${type}"`);
    this.addLine(`  string="${string}"`);

    if (icon) {
        this.addLine(`  icon="${icon}"`);
    }

    // 添加点击处理
    this.addLine(`  clickParams="{{`);
    this.addLine(`    name: '${name}',`);
    this.addLine(`    type: '${type}',`);
    this.addLine(`    context: props.record.context`);
    this.addLine(`  }}"`);

    // 添加修饰符
    this.compileButtonModifiers(node);

    this.addLine(`/>`);
}

// 编译按钮修饰符
compileButtonModifiers(node) {
    const invisible = getModifier(node, 'invisible');
    if (invisible !== undefined) {
        this.addLine(`  invisible="${invisible}"`);
    }

    const attrs = node.getAttribute('attrs');
    if (attrs) {
        this.addLine(`  attrs="${attrs}"`);
    }
}
```

**按钮编译功能**:
- **按钮组件**: 生成ViewButton组件
- **按钮属性**: 处理按钮的各种属性
- **点击参数**: 生成点击事件参数
- **可见性**: 处理按钮可见性

### 5. 笔记本编译

```javascript
// 编译notebook节点
compileNotebook(node, params) {
    this.addLine(`<Notebook>`);

    // 编译页面
    for (const child of node.children) {
        if (child.tagName === 'page') {
            this.compilePage(child, params);
        }
    }

    this.addLine(`</Notebook>`);
}

// 编译page节点
compilePage(node, params) {
    const string = node.getAttribute('string');
    const name = node.getAttribute('name');
    const invisible = getModifier(node, 'invisible');

    this.addLine(`<t t-set-slot="page_${name}">`);
    this.addLine(`<div class="o_notebook_page"`);

    if (invisible !== undefined) {
        this.addLine(`  t-if="!(${invisible})"`);
    }

    this.addLine(`>`);

    // 编译页面内容
    for (const child of node.children) {
        this.compileNode(child, params);
    }

    this.addLine(`</div>`);
    this.addLine(`</t>`);
}
```

**笔记本编译功能**:
- **笔记本组件**: 生成Notebook组件
- **页面编译**: 编译笔记本页面
- **插槽机制**: 使用插槽传递页面内容
- **条件显示**: 处理页面的条件显示

## 使用场景

### 1. 表单编译管理器

```javascript
// 表单编译管理器
class FormCompileManager {
    constructor() {
        this.setupManager();
    }

    setupManager() {
        // 设置编译配置
        this.compileConfig = {
            enableOptimization: true,
            enableCaching: true,
            enableMinification: false,
            enableSourceMap: false,
            cacheTimeout: 300000, // 5分钟
            maxCacheSize: 100
        };

        // 设置编译器缓存
        this.compilerCache = new Map();

        // 设置模板缓存
        this.templateCache = new Map();

        // 设置编译器注册表
        this.compilerRegistry = new Map();

        // 设置编译统计
        this.compileStatistics = {
            totalCompiles: 0,
            cacheHits: 0,
            cacheMisses: 0,
            averageCompileTime: 0,
            templateCount: 0
        };

        this.initializeCompileSystem();
    }

    // 初始化编译系统
    initializeCompileSystem() {
        // 创建增强的表单编译器
        this.createEnhancedFormCompiler();

        // 注册默认编译器
        this.registerDefaultCompilers();

        // 设置缓存系统
        this.setupCacheSystem();

        // 设置优化系统
        this.setupOptimizationSystem();
    }

    // 创建增强的表单编译器
    createEnhancedFormCompiler() {
        const originalCompiler = FormCompiler;

        this.EnhancedFormCompiler = class extends originalCompiler {
            constructor(manager) {
                super();
                this.manager = manager;
            }

            compile(node, params = {}) {
                const startTime = performance.now();

                // 检查缓存
                const cacheKey = this.generateCacheKey(node, params);
                if (this.manager.compileConfig.enableCaching && this.manager.templateCache.has(cacheKey)) {
                    this.manager.compileStatistics.cacheHits++;
                    return this.manager.templateCache.get(cacheKey);
                }

                this.manager.compileStatistics.cacheMisses++;

                // 执行增强编译
                const result = this.enhancedCompile(node, params);

                // 缓存结果
                if (this.manager.compileConfig.enableCaching) {
                    this.manager.templateCache.set(cacheKey, result);
                }

                const endTime = performance.now();
                this.updateCompileStatistics(endTime - startTime);

                return result;
            }

            enhancedCompile(node, params) {
                // 预处理节点
                this.preprocessNode(node);

                // 执行原始编译
                const result = super.compile(node, params);

                // 后处理结果
                this.postprocessResult(result);

                // 优化结果
                if (this.manager.compileConfig.enableOptimization) {
                    return this.optimizeResult(result);
                }

                return result;
            }

            preprocessNode(node) {
                // 标准化节点属性
                this.normalizeNodeAttributes(node);

                // 验证节点结构
                this.validateNodeStructure(node);

                // 应用预处理规则
                this.applyPreprocessingRules(node);
            }

            normalizeNodeAttributes(node) {
                // 标准化布尔属性
                const booleanAttrs = ['invisible', 'readonly', 'required', 'nolabel'];
                for (const attr of booleanAttrs) {
                    if (node.hasAttribute(attr)) {
                        const value = node.getAttribute(attr);
                        if (value === '' || value === '1' || value === 'True') {
                            node.setAttribute(attr, 'true');
                        } else if (value === '0' || value === 'False') {
                            node.setAttribute(attr, 'false');
                        }
                    }
                }

                // 标准化数值属性
                const numericAttrs = ['colspan', 'rowspan'];
                for (const attr of numericAttrs) {
                    if (node.hasAttribute(attr)) {
                        const value = parseInt(node.getAttribute(attr)) || 1;
                        node.setAttribute(attr, value.toString());
                    }
                }
            }

            validateNodeStructure(node) {
                // 验证必需属性
                if (node.tagName === 'field' && !node.hasAttribute('name')) {
                    throw new Error('Field node must have a name attribute');
                }

                if (node.tagName === 'button' && !node.hasAttribute('name')) {
                    throw new Error('Button node must have a name attribute');
                }

                // 验证子节点
                this.validateChildNodes(node);
            }

            validateChildNodes(node) {
                for (const child of node.children) {
                    this.validateNodeStructure(child);
                }
            }

            applyPreprocessingRules(node) {
                // 应用自定义预处理规则
                const rules = this.manager.getPreprocessingRules(node.tagName);
                for (const rule of rules) {
                    rule.apply(node);
                }
            }

            postprocessResult(result) {
                // 添加元数据
                this.addMetadata(result);

                // 验证结果
                this.validateResult(result);

                // 应用后处理规则
                this.applyPostprocessingRules(result);
            }

            addMetadata(result) {
                result.metadata = {
                    compileTime: Date.now(),
                    compiler: 'EnhancedFormCompiler',
                    version: '1.0.0'
                };
            }

            validateResult(result) {
                if (!result || typeof result !== 'string') {
                    throw new Error('Compile result must be a string');
                }
            }

            optimizeResult(result) {
                // 移除多余的空白
                result = this.removeExtraWhitespace(result);

                // 合并相邻的文本节点
                result = this.mergeTextNodes(result);

                // 优化属性表达式
                result = this.optimizeAttributeExpressions(result);

                return result;
            }

            removeExtraWhitespace(result) {
                return result.replace(/\s+/g, ' ').trim();
            }

            mergeTextNodes(result) {
                // 简化实现：合并相邻的文本内容
                return result.replace(/>\s+</g, '><');
            }

            optimizeAttributeExpressions(result) {
                // 优化重复的属性表达式
                return result.replace(/t-attf-class="([^"]*)" t-attf-class="([^"]*)"/g, 't-attf-class="$1 $2"');
            }

            generateCacheKey(node, params) {
                const nodeString = new XMLSerializer().serializeToString(node);
                const paramsString = JSON.stringify(params);
                return this.simpleHash(nodeString + paramsString);
            }

            simpleHash(str) {
                let hash = 0;
                for (let i = 0; i < str.length; i++) {
                    const char = str.charCodeAt(i);
                    hash = ((hash << 5) - hash) + char;
                    hash = hash & hash; // 转换为32位整数
                }
                return hash.toString(36);
            }

            updateCompileStatistics(compileTime) {
                this.manager.compileStatistics.totalCompiles++;
                this.manager.compileStatistics.templateCount++;

                // 更新平均编译时间
                const totalCompiles = this.manager.compileStatistics.totalCompiles;
                this.manager.compileStatistics.averageCompileTime =
                    (this.manager.compileStatistics.averageCompileTime * (totalCompiles - 1) + compileTime) / totalCompiles;
            }

            // 重写编译器方法以添加增强功能
            compileField(node, params) {
                // 添加字段验证
                this.validateFieldNode(node);

                // 执行原始编译
                const result = super.compileField(node, params);

                // 添加字段增强
                return this.enhanceFieldResult(result, node);
            }

            validateFieldNode(node) {
                const name = node.getAttribute('name');
                if (!name) {
                    throw new Error('Field node must have a name attribute');
                }

                const widget = node.getAttribute('widget');
                if (widget && !this.isValidWidget(widget)) {
                    console.warn(`Unknown widget: ${widget}`);
                }
            }

            isValidWidget(widget) {
                const validWidgets = ['char', 'text', 'html', 'email', 'url', 'phone', 'many2one', 'selection'];
                return validWidgets.includes(widget);
            }

            enhanceFieldResult(result, node) {
                // 添加字段特定的增强
                const fieldName = node.getAttribute('name');
                const enhanced = result.replace(
                    '<Field',
                    `<Field data-field-name="${fieldName}"`
                );

                return enhanced;
            }
        };
    }

    // 注册默认编译器
    registerDefaultCompilers() {
        // 注册字段编译器
        this.compilerRegistry.set('field', {
            compile: (node, params) => this.compileField(node, params),
            validate: (node) => this.validateField(node)
        });

        // 注册按钮编译器
        this.compilerRegistry.set('button', {
            compile: (node, params) => this.compileButton(node, params),
            validate: (node) => this.validateButton(node)
        });

        // 注册分组编译器
        this.compilerRegistry.set('group', {
            compile: (node, params) => this.compileGroup(node, params),
            validate: (node) => this.validateGroup(node)
        });
    }

    // 设置缓存系统
    setupCacheSystem() {
        // 定期清理缓存
        setInterval(() => {
            this.cleanupCache();
        }, this.compileConfig.cacheTimeout);
    }

    // 清理缓存
    cleanupCache() {
        if (this.templateCache.size > this.compileConfig.maxCacheSize) {
            // 移除最旧的缓存项
            const firstKey = this.templateCache.keys().next().value;
            this.templateCache.delete(firstKey);
        }
    }

    // 设置优化系统
    setupOptimizationSystem() {
        this.optimizationRules = {
            removeWhitespace: true,
            mergeTextNodes: true,
            optimizeExpressions: true,
            minifyOutput: this.compileConfig.enableMinification
        };
    }

    // 获取预处理规则
    getPreprocessingRules(tagName) {
        const rules = {
            field: [
                { apply: (node) => this.normalizeFieldAttributes(node) }
            ],
            button: [
                { apply: (node) => this.normalizeButtonAttributes(node) }
            ]
        };

        return rules[tagName] || [];
    }

    // 标准化字段属性
    normalizeFieldAttributes(node) {
        // 处理widget属性
        const widget = node.getAttribute('widget');
        if (widget === 'email') {
            node.setAttribute('type', 'email');
        }
    }

    // 标准化按钮属性
    normalizeButtonAttributes(node) {
        // 处理按钮类型
        if (!node.hasAttribute('type')) {
            node.setAttribute('type', 'object');
        }
    }

    // 创建编译器实例
    createCompiler() {
        return new this.EnhancedFormCompiler(this);
    }

    // 获取编译统计
    getCompileStatistics() {
        return {
            ...this.compileStatistics,
            cacheSize: this.templateCache.size,
            compilerRegistrySize: this.compilerRegistry.size,
            cacheHitRate: this.compileStatistics.totalCompiles > 0
                ? (this.compileStatistics.cacheHits / this.compileStatistics.totalCompiles * 100).toFixed(2) + '%'
                : '0%'
        };
    }

    // 销毁管理器
    destroy() {
        // 清理缓存
        this.compilerCache.clear();
        this.templateCache.clear();
        this.compilerRegistry.clear();

        // 重置统计
        this.compileStatistics = {
            totalCompiles: 0,
            cacheHits: 0,
            cacheMisses: 0,
            averageCompileTime: 0,
            templateCount: 0
        };
    }
}

// 使用示例
const compileManager = new FormCompileManager();

// 创建编译器
const compiler = compileManager.createCompiler();

// 编译表单
const template = compiler.compile(xmlNode, { isSubView: false });

// 获取统计信息
const stats = compileManager.getCompileStatistics();
console.log('Compile statistics:', stats);
```
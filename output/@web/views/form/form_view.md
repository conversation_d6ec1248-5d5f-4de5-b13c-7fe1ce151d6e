# Odoo 表单视图 (Form View) 学习资料

## 文件概述

**文件路径**: `output/@web/views/form/form_view.js`  
**原始路径**: `/web/static/src/views/form/form_view.js`  
**模块类型**: 核心视图模块 - 表单视图定义  
**代码行数**: 40 行  
**依赖关系**: 
- `@web/core/registry` - 注册表系统
- `@web/model/relational_model/relational_model` - 关系模型
- `@web/views/form/form_renderer` - 表单渲染器
- `@web/views/form/form_arch_parser` - 表单架构解析器
- `@web/views/form/form_controller` - 表单控制器
- `@web/views/form/form_compiler` - 表单编译器

## 模块功能

表单视图模块是 Odoo Web 客户端的核心视图类型之一。该模块提供了：
- 表单视图的完整定义和配置
- 数据录入和编辑功能
- 字段布局和分组管理
- 表单验证和状态管理
- 按钮和动作处理
- 关联记录管理

表单视图是数据录入和编辑的主要界面，提供了结构化的数据输入体验。

## 表单视图架构

### 核心组件结构
```
Form View
├── FormArchParser - 架构解析器
│   ├── 解析表单XML架构
│   ├── 提取字段信息
│   ├── 处理分组和布局
│   └── 解析按钮和动作
├── FormController - 控制器
│   ├── 表单状态管理
│   ├── 数据保存和验证
│   ├── 按钮事件处理
│   └── 导航和路由
├── FormRenderer - 渲染器
│   ├── 表单布局渲染
│   ├── 字段组件渲染
│   ├── 按钮和工具栏
│   └── 状态指示器
├── FormCompiler - 编译器
│   ├── 表单模板编译
│   ├── 字段编译
│   ├── 布局编译
│   └── 动态内容生成
└── RelationalModel - 数据模型
    ├── 记录加载和缓存
    ├── 字段值管理
    ├── 关系字段处理
    └── 变更跟踪
```

### 视图定义结构
```javascript
const formView = {
    type: "form",
    searchMenuTypes: [],
    
    // 核心组件
    Controller: FormController,
    Renderer: FormRenderer,
    ArchParser: FormArchParser,
    Model: RelationalModel,
    Compiler: FormCompiler,
    
    // 按钮模板
    buttonTemplate: "web.FormView.Buttons",
    
    // 属性处理函数
    props: (genericProps, view) => {
        // 处理和扩展属性
        return processedProps;
    }
};
```

## 核心组件详解

### 1. 表单视图定义
```javascript
const formView = {
    type: "form",
    searchMenuTypes: [],
    
    Controller: FormController,
    Renderer: FormRenderer,
    ArchParser: FormArchParser,
    Model: RelationalModel,
    Compiler: FormCompiler,
    
    buttonTemplate: "web.FormView.Buttons",
    
    props: (genericProps, view) => {
        const { ArchParser } = view;
        const { arch, relatedModels, resModel } = genericProps;
        const archInfo = new ArchParser().parse(arch, relatedModels, resModel);

        return {
            ...genericProps,
            Model: view.Model,
            Renderer: view.Renderer,
            buttonTemplate: genericProps.buttonTemplate || view.buttonTemplate,
            Compiler: view.Compiler,
            archInfo,
        };
    },
};
```

**功能特性**:
- **类型标识**: 明确标识为"form"视图类型
- **组件集成**: 集成所有必要的表单组件
- **架构解析**: 自动解析表单XML架构
- **属性处理**: 智能处理和扩展视图属性
- **模板配置**: 配置按钮模板和编译器

### 2. 属性处理函数 (props)
```javascript
props: (genericProps, view) => {
    const { ArchParser } = view;
    const { arch, relatedModels, resModel } = genericProps;
    const archInfo = new ArchParser().parse(arch, relatedModels, resModel);

    return {
        ...genericProps,
        Model: view.Model,
        Renderer: view.Renderer,
        buttonTemplate: genericProps.buttonTemplate || view.buttonTemplate,
        Compiler: view.Compiler,
        archInfo,
    };
}
```

**处理流程**:
1. **架构解析**: 使用ArchParser解析XML架构
2. **信息提取**: 提取架构信息和字段定义
3. **属性合并**: 合并通用属性和表单特定属性
4. **组件注入**: 注入必要的组件引用
5. **模板配置**: 配置按钮模板和编译器

## 使用示例

### 1. 基本表单视图
```javascript
// 表单视图XML架构示例
const formArch = `
    <form string="客户信息">
        <header>
            <button name="action_confirm" 
                    type="object" 
                    string="确认" 
                    class="btn-primary"
                    states="draft"/>
            <button name="action_cancel" 
                    type="object" 
                    string="取消" 
                    states="confirmed"/>
            <field name="state" widget="statusbar" statusbar_visible="draft,confirmed,done"/>
        </header>
        
        <sheet>
            <div class="oe_button_box" name="button_box">
                <button type="object" 
                        name="action_view_orders" 
                        class="oe_stat_button" 
                        icon="fa-shopping-cart">
                    <field name="order_count" widget="statinfo" string="订单"/>
                </button>
            </div>
            
            <field name="image" widget="image" class="oe_avatar"/>
            
            <div class="oe_title">
                <h1>
                    <field name="name" placeholder="客户名称"/>
                </h1>
                <div>
                    <field name="category_id" widget="many2many_tags" placeholder="标签"/>
                </div>
            </div>
            
            <group>
                <group name="contact_details" string="联系信息">
                    <field name="email" widget="email"/>
                    <field name="phone" widget="phone"/>
                    <field name="mobile"/>
                    <field name="website" widget="url"/>
                </group>
                
                <group name="address_details" string="地址信息">
                    <field name="street"/>
                    <field name="street2"/>
                    <field name="city"/>
                    <field name="state_id"/>
                    <field name="zip"/>
                    <field name="country_id"/>
                </group>
            </group>
            
            <notebook>
                <page string="销售信息" name="sales_info">
                    <group>
                        <group name="sales_settings">
                            <field name="user_id" string="销售员"/>
                            <field name="team_id" string="销售团队"/>
                            <field name="payment_term_id"/>
                        </group>
                        
                        <group name="sales_stats">
                            <field name="total_invoiced" widget="monetary"/>
                            <field name="credit_limit" widget="monetary"/>
                            <field name="currency_id" invisible="1"/>
                        </group>
                    </group>
                </page>
                
                <page string="订单历史" name="order_history">
                    <field name="order_ids">
                        <tree>
                            <field name="name"/>
                            <field name="date_order"/>
                            <field name="amount_total" widget="monetary"/>
                            <field name="state"/>
                        </tree>
                    </field>
                </page>
                
                <page string="内部备注" name="internal_notes">
                    <field name="comment" placeholder="内部备注..."/>
                </page>
            </notebook>
        </sheet>
        
        <div class="oe_chatter">
            <field name="message_follower_ids" widget="mail_followers"/>
            <field name="activity_ids" widget="mail_activity"/>
            <field name="message_ids" widget="mail_thread"/>
        </div>
    </form>
`;

// 使用表单视图
class CustomerFormView extends Component {
    static template = xml`
        <div class="customer-form">
            <FormView resModel="'res.partner'"
                      arch="formArch"
                      context="context"
                      resId="customerId"
                      mode="mode"
                      fields="fields"
                      onSave="onSave"
                      onDiscard="onDiscard" />
        </div>
    `;
    
    setup() {
        this.formArch = formArch;
        this.customerId = this.props.customerId;
        this.mode = useState(this.props.mode || 'readonly');
        
        this.context = {
            default_is_company: false,
            default_customer_rank: 1
        };
        
        this.fields = {
            name: { type: 'char', string: '名称', required: true },
            email: { type: 'char', string: '邮箱' },
            phone: { type: 'char', string: '电话' },
            mobile: { type: 'char', string: '手机' },
            website: { type: 'char', string: '网站' },
            street: { type: 'char', string: '街道' },
            street2: { type: 'char', string: '街道2' },
            city: { type: 'char', string: '城市' },
            state_id: { type: 'many2one', relation: 'res.country.state', string: '省份' },
            zip: { type: 'char', string: '邮编' },
            country_id: { type: 'many2one', relation: 'res.country', string: '国家' },
            category_id: { type: 'many2many', relation: 'res.partner.category', string: '标签' },
            user_id: { type: 'many2one', relation: 'res.users', string: '销售员' },
            team_id: { type: 'many2one', relation: 'crm.team', string: '销售团队' },
            payment_term_id: { type: 'many2one', relation: 'account.payment.term', string: '付款条件' },
            total_invoiced: { type: 'monetary', string: '总开票金额' },
            credit_limit: { type: 'monetary', string: '信用额度' },
            currency_id: { type: 'many2one', relation: 'res.currency', string: '货币' },
            order_ids: { type: 'one2many', relation: 'sale.order', string: '订单' },
            order_count: { type: 'integer', string: '订单数量' },
            comment: { type: 'text', string: '备注' },
            image: { type: 'binary', string: '头像' },
            state: { 
                type: 'selection', 
                selection: [['draft', '草稿'], ['confirmed', '确认'], ['done', '完成']], 
                string: '状态' 
            },
            message_follower_ids: { type: 'one2many', relation: 'mail.followers', string: '关注者' },
            activity_ids: { type: 'one2many', relation: 'mail.activity', string: '活动' },
            message_ids: { type: 'one2many', relation: 'mail.message', string: '消息' }
        };
    }
    
    onSave(record) {
        console.log('保存客户信息:', record);
        this.mode = 'readonly';
    }
    
    onDiscard() {
        console.log('取消编辑');
        this.mode = 'readonly';
    }
}
```

## 表单视图特性

### 1. 表单布局系统
表单视图支持灵活的布局系统：
- **Sheet布局**: 主要内容区域
- **Header布局**: 顶部按钮和状态栏
- **Group布局**: 字段分组和列布局
- **Notebook布局**: 标签页组织
- **Chatter布局**: 消息和活动区域

### 2. 字段组织
```javascript
// 字段分组示例
<group>
    <group name="left_group" string="基本信息">
        <field name="name"/>
        <field name="email"/>
    </group>
    <group name="right_group" string="联系信息">
        <field name="phone"/>
        <field name="mobile"/>
    </group>
</group>
```

### 3. 状态管理
```javascript
// 状态栏配置
<field name="state"
       widget="statusbar"
       statusbar_visible="draft,confirmed,done"
       statusbar_colors='{"cancelled": "danger"}'/>
```

### 4. 按钮配置
```javascript
// 智能按钮
<button type="object"
        name="action_view_orders"
        class="oe_stat_button"
        icon="fa-shopping-cart">
    <field name="order_count" widget="statinfo" string="订单"/>
</button>
```

## 高级应用模式

### 1. 动态表单生成器
```javascript
class DynamicFormGenerator {
    constructor() {
        this.formTemplates = new Map();
        this.fieldTemplates = new Map();
        this.setupDefaultTemplates();
    }

    setupDefaultTemplates() {
        // 基本表单模板
        this.formTemplates.set('basic', {
            structure: 'header-sheet-chatter',
            sections: ['title', 'main', 'details']
        });

        // 复杂表单模板
        this.formTemplates.set('complex', {
            structure: 'header-sheet-notebook-chatter',
            sections: ['title', 'main', 'tabs', 'relations']
        });

        // 简单表单模板
        this.formTemplates.set('simple', {
            structure: 'sheet-only',
            sections: ['fields']
        });
    }

    generateForm(model, fields, options = {}) {
        const template = this.formTemplates.get(options.template || 'basic');
        const formArch = this.buildFormArch(model, fields, template, options);

        return {
            arch: formArch,
            fields: this.processFields(fields),
            context: this.buildContext(model, options),
            props: this.buildProps(model, options)
        };
    }

    buildFormArch(model, fields, template, options) {
        const sections = template.sections.map(section =>
            this.generateSection(section, fields, options)
        ).join('\n');

        return `
            <form string="${options.title || model}">
                ${template.structure.includes('header') ? this.generateHeader(fields, options) : ''}

                <sheet>
                    ${sections}
                </sheet>

                ${template.structure.includes('chatter') ? this.generateChatter() : ''}
            </form>
        `;
    }

    generateSection(sectionType, fields, options) {
        switch (sectionType) {
            case 'title':
                return this.generateTitleSection(fields, options);
            case 'main':
                return this.generateMainSection(fields, options);
            case 'details':
                return this.generateDetailsSection(fields, options);
            case 'tabs':
                return this.generateTabsSection(fields, options);
            case 'relations':
                return this.generateRelationsSection(fields, options);
            case 'fields':
                return this.generateFieldsSection(fields, options);
            default:
                return '';
        }
    }

    generateTitleSection(fields, options) {
        const nameField = this.findField(fields, ['name', 'title', 'display_name']);
        const tagFields = this.findFields(fields, ['tag_ids', 'category_ids']);

        if (!nameField) return '';

        return `
            <div class="oe_title">
                <h1>
                    <field name="${nameField.name}" placeholder="${nameField.string}"/>
                </h1>
                ${tagFields.map(field => `
                    <div>
                        <field name="${field.name}" widget="many2many_tags"/>
                    </div>
                `).join('')}
            </div>
        `;
    }

    generateMainSection(fields, options) {
        const mainFields = this.categorizeFields(fields).main;
        const leftFields = mainFields.slice(0, Math.ceil(mainFields.length / 2));
        const rightFields = mainFields.slice(Math.ceil(mainFields.length / 2));

        return `
            <group>
                <group name="left_group">
                    ${leftFields.map(field => this.generateFieldElement(field)).join('\n')}
                </group>
                <group name="right_group">
                    ${rightFields.map(field => this.generateFieldElement(field)).join('\n')}
                </group>
            </group>
        `;
    }

    generateDetailsSection(fields, options) {
        const detailFields = this.categorizeFields(fields).details;

        if (detailFields.length === 0) return '';

        return `
            <group string="详细信息">
                ${detailFields.map(field => this.generateFieldElement(field)).join('\n')}
            </group>
        `;
    }

    generateTabsSection(fields, options) {
        const categories = this.categorizeFields(fields);
        const tabs = [];

        if (categories.relations.length > 0) {
            tabs.push({
                name: 'relations',
                string: '关联记录',
                fields: categories.relations
            });
        }

        if (categories.text.length > 0) {
            tabs.push({
                name: 'notes',
                string: '备注',
                fields: categories.text
            });
        }

        if (categories.system.length > 0) {
            tabs.push({
                name: 'system',
                string: '系统信息',
                fields: categories.system
            });
        }

        if (tabs.length === 0) return '';

        return `
            <notebook>
                ${tabs.map(tab => `
                    <page string="${tab.string}" name="${tab.name}">
                        ${tab.fields.map(field => this.generateFieldElement(field)).join('\n')}
                    </page>
                `).join('')}
            </notebook>
        `;
    }

    generateRelationsSection(fields, options) {
        const relationFields = this.findFields(fields, null, ['one2many', 'many2many']);

        if (relationFields.length === 0) return '';

        return relationFields.map(field => `
            <group string="${field.string}">
                <field name="${field.name}">
                    <tree>
                        ${this.generateRelationTreeFields(field)}
                    </tree>
                </field>
            </group>
        `).join('\n');
    }

    generateFieldsSection(fields, options) {
        return `
            <group>
                ${Object.values(fields).map(field => this.generateFieldElement(field)).join('\n')}
            </group>
        `;
    }

    generateFieldElement(field) {
        const attrs = [];

        // 基本属性
        attrs.push(`name="${field.name}"`);

        // 字符串标签
        if (field.string) {
            attrs.push(`string="${field.string}"`);
        }

        // 必填字段
        if (field.required) {
            attrs.push('required="1"');
        }

        // 只读字段
        if (field.readonly) {
            attrs.push('readonly="1"');
        }

        // 不可见字段
        if (field.invisible) {
            attrs.push('invisible="1"');
        }

        // 特殊widget
        const widget = this.getFieldWidget(field);
        if (widget) {
            attrs.push(`widget="${widget}"`);
        }

        // 字段选项
        const options = this.getFieldOptions(field);
        if (options) {
            attrs.push(`options='${JSON.stringify(options)}'`);
        }

        return `<field ${attrs.join(' ')}/>`;
    }

    generateHeader(fields, options) {
        const stateField = this.findField(fields, ['state', 'status']);
        const buttons = this.generateHeaderButtons(fields, options);

        return `
            <header>
                ${buttons}
                ${stateField ? `<field name="${stateField.name}" widget="statusbar"/>` : ''}
            </header>
        `;
    }

    generateHeaderButtons(fields, options) {
        const buttons = [];

        // 确认按钮
        if (this.hasState(fields, 'draft')) {
            buttons.push(`
                <button name="action_confirm"
                        type="object"
                        string="确认"
                        class="btn-primary"
                        states="draft"/>
            `);
        }

        // 取消按钮
        if (this.hasState(fields, 'confirmed')) {
            buttons.push(`
                <button name="action_cancel"
                        type="object"
                        string="取消"
                        states="confirmed"/>
            `);
        }

        return buttons.join('\n');
    }

    generateChatter() {
        return `
            <div class="oe_chatter">
                <field name="message_follower_ids" widget="mail_followers"/>
                <field name="activity_ids" widget="mail_activity"/>
                <field name="message_ids" widget="mail_thread"/>
            </div>
        `;
    }

    categorizeFields(fields) {
        const categories = {
            main: [],
            details: [],
            relations: [],
            text: [],
            system: []
        };

        Object.values(fields).forEach(field => {
            if (this.isSystemField(field)) {
                categories.system.push(field);
            } else if (this.isRelationField(field)) {
                categories.relations.push(field);
            } else if (this.isTextField(field)) {
                categories.text.push(field);
            } else if (this.isMainField(field)) {
                categories.main.push(field);
            } else {
                categories.details.push(field);
            }
        });

        return categories;
    }

    findField(fields, names, types = null) {
        for (const name of names) {
            const field = fields[name];
            if (field && (!types || types.includes(field.type))) {
                return field;
            }
        }
        return null;
    }

    findFields(fields, names = null, types = null) {
        return Object.values(fields).filter(field => {
            const nameMatch = !names || names.some(name => field.name.includes(name));
            const typeMatch = !types || types.includes(field.type);
            return nameMatch && typeMatch;
        });
    }

    isSystemField(field) {
        const systemFields = ['create_uid', 'create_date', 'write_uid', 'write_date', '__last_update'];
        return systemFields.includes(field.name);
    }

    isRelationField(field) {
        return ['one2many', 'many2many'].includes(field.type);
    }

    isTextField(field) {
        return ['text', 'html'].includes(field.type);
    }

    isMainField(field) {
        const mainFields = ['name', 'code', 'date', 'user_id', 'partner_id', 'company_id'];
        return mainFields.includes(field.name) || field.required;
    }

    hasState(fields, state) {
        const stateField = fields.state;
        return stateField && stateField.selection &&
               stateField.selection.some(([value]) => value === state);
    }

    getFieldWidget(field) {
        const widgets = {
            'email': 'email',
            'phone': 'phone',
            'url': 'url',
            'monetary': 'monetary',
            'date': 'date',
            'datetime': 'datetime',
            'binary': 'binary',
            'html': 'html',
            'text': 'text',
            'selection': 'selection',
            'many2one': 'many2one',
            'one2many': 'one2many',
            'many2many': 'many2many_tags'
        };

        return widgets[field.type] || null;
    }

    getFieldOptions(field) {
        const options = {};

        if (field.type === 'many2many') {
            options.color_field = 'color';
        }

        if (field.type === 'monetary') {
            options.currency_field = 'currency_id';
        }

        if (field.type === 'html') {
            options.height = 200;
        }

        return Object.keys(options).length > 0 ? options : null;
    }

    generateRelationTreeFields(field) {
        // 为关系字段生成树视图字段
        const commonFields = ['name', 'date', 'state', 'amount'];

        return commonFields.map(fieldName =>
            `<field name="${fieldName}"/>`
        ).join('\n');
    }

    processFields(fields) {
        // 处理字段定义，添加必要的属性
        const processedFields = {};

        Object.entries(fields).forEach(([name, field]) => {
            processedFields[name] = {
                ...field,
                name: name
            };
        });

        return processedFields;
    }

    buildContext(model, options) {
        return {
            default_active: true,
            ...options.context
        };
    }

    buildProps(model, options) {
        return {
            resModel: model,
            ...options.props
        };
    }
}
```

### 2. 表单验证系统
```javascript
class FormValidationSystem {
    constructor() {
        this.validators = new Map();
        this.rules = new Map();
        this.setupDefaultValidators();
    }

    setupDefaultValidators() {
        // 必填验证
        this.validators.set('required', (value, field) => {
            if (field.required && (!value || value === '')) {
                return `${field.string} 是必填字段`;
            }
            return null;
        });

        // 长度验证
        this.validators.set('length', (value, field, options) => {
            if (value && field.size && value.length > field.size) {
                return `${field.string} 长度不能超过 ${field.size} 个字符`;
            }
            return null;
        });

        // 邮箱验证
        this.validators.set('email', (value, field) => {
            if (value && field.type === 'char' && field.name.includes('email')) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    return `${field.string} 格式不正确`;
                }
            }
            return null;
        });

        // 数值范围验证
        this.validators.set('range', (value, field, options) => {
            if (value && (field.type === 'integer' || field.type === 'float')) {
                if (options.min !== undefined && value < options.min) {
                    return `${field.string} 不能小于 ${options.min}`;
                }
                if (options.max !== undefined && value > options.max) {
                    return `${field.string} 不能大于 ${options.max}`;
                }
            }
            return null;
        });

        // 唯一性验证
        this.validators.set('unique', async (value, field, options, record) => {
            if (value && options.unique) {
                const exists = await this.checkUniqueness(field.name, value, record);
                if (exists) {
                    return `${field.string} 已存在，请使用其他值`;
                }
            }
            return null;
        });
    }

    addRule(fieldName, validatorName, options = {}) {
        if (!this.rules.has(fieldName)) {
            this.rules.set(fieldName, []);
        }

        this.rules.get(fieldName).push({
            validator: validatorName,
            options: options
        });
    }

    async validateField(fieldName, value, field, record) {
        const errors = [];
        const fieldRules = this.rules.get(fieldName) || [];

        // 应用通用验证器
        for (const [validatorName, validator] of this.validators) {
            try {
                const error = await validator(value, field, {}, record);
                if (error) {
                    errors.push(error);
                }
            } catch (e) {
                console.warn(`验证器 ${validatorName} 执行失败:`, e);
            }
        }

        // 应用字段特定规则
        for (const rule of fieldRules) {
            const validator = this.validators.get(rule.validator);
            if (validator) {
                try {
                    const error = await validator(value, field, rule.options, record);
                    if (error) {
                        errors.push(error);
                    }
                } catch (e) {
                    console.warn(`规则验证失败:`, e);
                }
            }
        }

        return errors;
    }

    async validateForm(record, fields) {
        const allErrors = {};

        for (const [fieldName, field] of Object.entries(fields)) {
            const value = record.data[fieldName];
            const errors = await this.validateField(fieldName, value, field, record);

            if (errors.length > 0) {
                allErrors[fieldName] = errors;
            }
        }

        return allErrors;
    }

    async checkUniqueness(fieldName, value, record) {
        // 检查字段值的唯一性
        try {
            const domain = [[fieldName, '=', value]];
            if (record.id) {
                domain.push(['id', '!=', record.id]);
            }

            const count = await this.env.services.orm.searchCount(
                record.resModel,
                domain
            );

            return count > 0;
        } catch (error) {
            console.warn('唯一性检查失败:', error);
            return false;
        }
    }

    getFieldErrors(fieldName) {
        return this.fieldErrors[fieldName] || [];
    }

    clearFieldErrors(fieldName) {
        if (this.fieldErrors[fieldName]) {
            delete this.fieldErrors[fieldName];
        }
    }

    clearAllErrors() {
        this.fieldErrors = {};
    }
}
```

## 最佳实践

### 1. 表单架构设计
```javascript
// ✅ 推荐：清晰的表单结构
<form string="客户信息">
    <header>
        <!-- 状态和动作按钮 -->
    </header>
    <sheet>
        <!-- 主要内容 -->
    </sheet>
    <div class="oe_chatter">
        <!-- 消息和活动 -->
    </div>
</form>
```

### 2. 字段组织
```javascript
// ✅ 推荐：逻辑分组
<group>
    <group name="contact_info" string="联系信息">
        <field name="email"/>
        <field name="phone"/>
    </group>
    <group name="address_info" string="地址信息">
        <field name="street"/>
        <field name="city"/>
    </group>
</group>
```

### 3. 状态管理
```javascript
// ✅ 推荐：清晰的状态流转
<field name="state"
       widget="statusbar"
       statusbar_visible="draft,confirmed,done"/>
```

## 总结

Odoo 表单视图模块提供了强大的数据录入功能：

**核心优势**:
- **灵活布局**: 支持多种布局模式和组织方式
- **丰富组件**: 提供完整的字段和按钮组件
- **状态管理**: 清晰的记录状态和流转管理
- **验证系统**: 完善的数据验证和错误处理
- **用户体验**: 优秀的用户交互和视觉设计

**适用场景**:
- 数据录入和编辑
- 记录详情查看
- 业务流程管理
- 状态跟踪
- 数据验证

**设计优势**:
- 组件化架构
- 模板驱动
- 高度可定制
- 响应式设计

这个表单视图为 Odoo Web 客户端提供了强大的数据管理能力，是业务应用的核心界面组件。

### 2. 高级表单视图配置
```javascript
class AdvancedFormView extends Component {
    static template = xml`
        <div class="advanced-form">
            <div class="form-controls">
                <div class="form-mode-switcher">
                    <button t-on-click="() => this.setMode('readonly')" 
                            t-att-class="mode === 'readonly' ? 'btn btn-primary' : 'btn btn-secondary'">
                        查看
                    </button>
                    <button t-on-click="() => this.setMode('edit')" 
                            t-att-class="mode === 'edit' ? 'btn btn-primary' : 'btn btn-secondary'">
                        编辑
                    </button>
                </div>
                
                <div class="form-actions">
                    <button t-if="mode === 'edit'" 
                            t-on-click="save" 
                            class="btn btn-success">
                        <i class="fa fa-save" /> 保存
                    </button>
                    
                    <button t-if="mode === 'edit'" 
                            t-on-click="discard" 
                            class="btn btn-secondary">
                        <i class="fa fa-times" /> 取消
                    </button>
                    
                    <button t-if="mode === 'readonly'" 
                            t-on-click="duplicate" 
                            class="btn btn-info">
                        <i class="fa fa-copy" /> 复制
                    </button>
                    
                    <button t-if="mode === 'readonly'" 
                            t-on-click="delete" 
                            class="btn btn-danger">
                        <i class="fa fa-trash" /> 删除
                    </button>
                </div>
            </div>
            
            <FormView resModel="resModel"
                      arch="currentArch"
                      context="currentContext"
                      resId="resId"
                      mode="mode"
                      fields="fields"
                      className="formClassName"
                      onSave="onSave"
                      onDiscard="onDiscard"
                      onFieldChange="onFieldChange"
                      onButtonClick="onButtonClick" />
        </div>
    `;
    
    setup() {
        this.mode = useState(this.props.mode || 'readonly');
        this.isDirty = useState(false);
        this.validationErrors = useState([]);
        
        this.baseArch = this.generateFormArch();
        this.fields = this.getFormFields();
        
        onWillStart(() => {
            this.loadRecord();
        });
    }
    
    get currentArch() {
        return this.mode === 'readonly' ? this.getReadonlyArch() : this.baseArch;
    }
    
    get currentContext() {
        const context = {
            default_company_id: this.env.services.company.currentCompany.id
        };
        
        if (this.mode === 'edit') {
            context.form_view_ref = 'module.form_view_edit';
        }
        
        return context;
    }
    
    get formClassName() {
        const classes = ['advanced-form-view'];
        
        if (this.mode === 'edit') {
            classes.push('edit-mode');
        }
        
        if (this.isDirty) {
            classes.push('has-changes');
        }
        
        if (this.validationErrors.length > 0) {
            classes.push('has-errors');
        }
        
        return classes.join(' ');
    }
    
    generateFormArch() {
        return `
            <form string="${this.getFormTitle()}">
                <header>
                    ${this.generateHeaderButtons()}
                    ${this.generateStatusBar()}
                </header>
                
                <sheet>
                    ${this.generateButtonBox()}
                    ${this.generateTitleSection()}
                    ${this.generateMainContent()}
                    ${this.generateNotebook()}
                </sheet>
                
                ${this.generateChatter()}
            </form>
        `;
    }
    
    generateHeaderButtons() {
        return `
            <button name="action_confirm" 
                    type="object" 
                    string="确认" 
                    class="btn-primary"
                    states="draft"
                    confirm="确定要确认这条记录吗？"/>
            
            <button name="action_reset" 
                    type="object" 
                    string="重置" 
                    states="confirmed"
                    groups="base.group_system"/>
        `;
    }
    
    generateStatusBar() {
        return `
            <field name="state" 
                   widget="statusbar" 
                   statusbar_visible="draft,confirmed,done"
                   statusbar_colors='{"cancelled": "danger"}'/>
        `;
    }
    
    generateButtonBox() {
        return `
            <div class="oe_button_box" name="button_box">
                <button type="object" 
                        name="action_view_related" 
                        class="oe_stat_button" 
                        icon="fa-list">
                    <field name="related_count" widget="statinfo" string="相关记录"/>
                </button>
                
                <button type="action" 
                        name="%(action_report)d" 
                        class="oe_stat_button" 
                        icon="fa-print">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_text">打印</span>
                    </div>
                </button>
            </div>
        `;
    }
    
    generateTitleSection() {
        return `
            <div class="oe_title">
                <h1>
                    <field name="name" 
                           placeholder="请输入名称"
                           required="1"/>
                </h1>
                <div>
                    <field name="tag_ids" 
                           widget="many2many_tags" 
                           placeholder="标签"
                           options="{'color_field': 'color', 'no_create': true}"/>
                </div>
            </div>
        `;
    }
    
    generateMainContent() {
        return `
            <group>
                <group name="left_group" string="基本信息">
                    <field name="code"/>
                    <field name="date" widget="date"/>
                    <field name="user_id" options="{'no_create': true}"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </group>
                
                <group name="right_group" string="其他信息">
                    <field name="priority" widget="priority"/>
                    <field name="active"/>
                    <field name="description" widget="text"/>
                </group>
            </group>
        `;
    }
    
    generateNotebook() {
        return `
            <notebook>
                <page string="详细信息" name="details">
                    <group>
                        <field name="detailed_info" widget="html"/>
                    </group>
                </page>
                
                <page string="附件" name="attachments">
                    <field name="attachment_ids">
                        <tree>
                            <field name="name"/>
                            <field name="file_size"/>
                            <field name="create_date"/>
                        </tree>
                    </field>
                </page>
                
                <page string="历史记录" name="history" groups="base.group_system">
                    <group>
                        <field name="create_uid" readonly="1"/>
                        <field name="create_date" readonly="1"/>
                        <field name="write_uid" readonly="1"/>
                        <field name="write_date" readonly="1"/>
                    </group>
                </page>
            </notebook>
        `;
    }
    
    generateChatter() {
        return `
            <div class="oe_chatter">
                <field name="message_follower_ids" widget="mail_followers"/>
                <field name="activity_ids" widget="mail_activity"/>
                <field name="message_ids" widget="mail_thread" options="{'post_refresh': 'recipients'}"/>
            </div>
        `;
    }
    
    getReadonlyArch() {
        // 为只读模式生成简化的架构
        return this.baseArch.replace(/required="1"/g, '');
    }
    
    getFormFields() {
        return {
            name: { type: 'char', string: '名称', required: true },
            code: { type: 'char', string: '编码' },
            date: { type: 'date', string: '日期' },
            user_id: { type: 'many2one', relation: 'res.users', string: '负责人' },
            company_id: { type: 'many2one', relation: 'res.company', string: '公司' },
            priority: { 
                type: 'selection', 
                selection: [['0', '低'], ['1', '正常'], ['2', '高'], ['3', '紧急']], 
                string: '优先级' 
            },
            active: { type: 'boolean', string: '激活' },
            description: { type: 'text', string: '描述' },
            tag_ids: { type: 'many2many', relation: 'project.tags', string: '标签' },
            state: { 
                type: 'selection', 
                selection: [['draft', '草稿'], ['confirmed', '确认'], ['done', '完成'], ['cancelled', '取消']], 
                string: '状态' 
            },
            related_count: { type: 'integer', string: '相关记录数' },
            detailed_info: { type: 'html', string: '详细信息' },
            attachment_ids: { type: 'one2many', relation: 'ir.attachment', string: '附件' },
            create_uid: { type: 'many2one', relation: 'res.users', string: '创建者' },
            create_date: { type: 'datetime', string: '创建时间' },
            write_uid: { type: 'many2one', relation: 'res.users', string: '修改者' },
            write_date: { type: 'datetime', string: '修改时间' },
            message_follower_ids: { type: 'one2many', relation: 'mail.followers', string: '关注者' },
            activity_ids: { type: 'one2many', relation: 'mail.activity', string: '活动' },
            message_ids: { type: 'one2many', relation: 'mail.message', string: '消息' }
        };
    }
    
    getFormTitle() {
        return this.props.title || '记录详情';
    }
    
    setMode(mode) {
        if (this.isDirty && mode === 'readonly') {
            const confirmed = confirm('有未保存的更改，确定要放弃吗？');
            if (!confirmed) {
                return;
            }
        }
        
        this.mode = mode;
        this.isDirty = false;
        this.validationErrors = [];
    }
    
    async loadRecord() {
        if (this.props.resId) {
            try {
                // 加载记录逻辑
                console.log('加载记录:', this.props.resId);
            } catch (error) {
                console.error('加载记录失败:', error);
            }
        }
    }
    
    async save() {
        try {
            // 验证表单
            const isValid = await this.validateForm();
            if (!isValid) {
                return;
            }
            
            // 保存记录
            await this.saveRecord();
            
            this.mode = 'readonly';
            this.isDirty = false;
            
        } catch (error) {
            console.error('保存失败:', error);
        }
    }
    
    discard() {
        this.mode = 'readonly';
        this.isDirty = false;
        this.validationErrors = [];
    }
    
    async duplicate() {
        try {
            const newId = await this.duplicateRecord();
            this.env.services.action.doAction({
                type: 'ir.actions.act_window',
                res_model: this.props.resModel,
                res_id: newId,
                view_mode: 'form',
                views: [[false, 'form']],
                target: 'current'
            });
        } catch (error) {
            console.error('复制失败:', error);
        }
    }
    
    async delete() {
        const confirmed = confirm('确定要删除这条记录吗？');
        if (confirmed) {
            try {
                await this.deleteRecord();
                this.env.services.action.doAction({ type: 'ir.actions.act_window_close' });
            } catch (error) {
                console.error('删除失败:', error);
            }
        }
    }
    
    onSave(record) {
        console.log('表单保存:', record);
        this.isDirty = false;
    }
    
    onDiscard() {
        console.log('表单取消');
        this.isDirty = false;
    }
    
    onFieldChange(fieldName, value) {
        console.log('字段变化:', fieldName, value);
        this.isDirty = true;
    }
    
    onButtonClick(buttonName, context) {
        console.log('按钮点击:', buttonName, context);
    }
    
    async validateForm() {
        // 表单验证逻辑
        this.validationErrors = [];
        return this.validationErrors.length === 0;
    }
    
    async saveRecord() {
        // 保存记录逻辑
        console.log('保存记录');
    }
    
    async duplicateRecord() {
        // 复制记录逻辑
        console.log('复制记录');
        return 123; // 新记录ID
    }
    
    async deleteRecord() {
        // 删除记录逻辑
        console.log('删除记录');
    }
}
```

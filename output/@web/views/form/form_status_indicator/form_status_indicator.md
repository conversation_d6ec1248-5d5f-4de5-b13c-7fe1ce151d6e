# FormStatusIndicator - 表单状态指示器

## 概述

`form_status_indicator.js` 是 Odoo Web 客户端表单视图的状态指示器组件，负责显示表单的当前状态并提供相应的操作按钮。该模块包含63行代码，是一个OWL组件，专门用于监控表单的保存状态、验证状态和脏数据状态，具备状态监听、按钮控制、自动禁用、事件响应等特性，是表单视图系统中状态管理的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/form/form_status_indicator/form_status_indicator.js`
- **行数**: 63
- **模块**: `@web/views/form/form_status_indicator/form_status_indicator`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                     // OWL框架
'@web/core/utils/hooks'         // 工具钩子
```

## 主要组件定义

### 1. FormStatusIndicator - 表单状态指示器

```javascript
class FormStatusIndicator extends Component {
    static template = "web.FormStatusIndicator";
    static props = {
        model: Object,
        save: Function,
        discard: Function,
    };
}
```

**组件特性**:
- **专用模板**: 使用FormStatusIndicator专用模板
- **模型集成**: 集成表单数据模型
- **操作回调**: 提供保存和丢弃操作回调
- **状态监控**: 监控表单的各种状态

## 核心功能

### 1. 组件初始化

```javascript
setup() {
    this.state = useState({
        fieldIsDirty: false,
    });
    useBus(
        this.props.model.bus,
        "FIELD_IS_DIRTY",
        (ev) => (this.state.fieldIsDirty = ev.detail)
    );
    useEffect(
        () => {
            if (!this.props.model.root.isNew && this.indicatorMode === "invalid") {
                this.saveButton.el.setAttribute("disabled", "1");
            } else {
                this.saveButton.el.removeAttribute("disabled");
            }
        },
        () => [this.props.model.root.isValid]
    );

    this.saveButton = useRef("save");
}
```

**初始化功能**:
- **状态管理**: 管理字段脏数据状态
- **事件监听**: 监听字段脏数据事件
- **按钮控制**: 根据验证状态控制保存按钮
- **引用管理**: 管理保存按钮的引用

### 2. 按钮显示控制

```javascript
get displayButtons() {
    return this.indicatorMode !== "saved";
}
```

**显示控制功能**:
- **条件显示**: 根据状态决定是否显示按钮
- **保存状态**: 已保存状态下隐藏按钮
- **用户体验**: 提供清晰的用户界面
- **状态反馈**: 提供直观的状态反馈

### 3. 指示器模式计算

```javascript
get indicatorMode() {
    if (this.props.model.root.isNew) {
        return this.props.model.root.isValid ? "dirty" : "invalid";
    } else if (!this.props.model.root.isValid) {
        return "invalid";
    } else if (this.props.model.root.dirty || this.state.fieldIsDirty) {
        return "dirty";
    } else {
        return "saved";
    }
}
```

**模式计算功能**:
- **新记录**: 处理新记录的状态逻辑
- **验证状态**: 检查记录的验证状态
- **脏数据**: 检查记录和字段的脏数据状态
- **保存状态**: 确定记录的保存状态

### 4. 操作方法

```javascript
async discard() {
    await this.props.discard();
}

async save() {
    await this.props.save();
}
```

**操作功能**:
- **丢弃操作**: 异步执行丢弃操作
- **保存操作**: 异步执行保存操作
- **回调执行**: 执行传入的回调函数
- **错误处理**: 处理操作过程中的错误

## 使用场景

### 1. 表单状态指示器管理器

```javascript
// 表单状态指示器管理器
class FormStatusIndicatorManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置状态指示器配置
        this.indicatorConfig = {
            enableAutoSave: false,
            enableStatusAnimation: true,
            enableTooltips: true,
            enableKeyboardShortcuts: true,
            autoSaveInterval: 30000, // 30秒
            showStatusText: true,
            showProgressIndicator: true,
            enableConfirmation: true
        };
        
        // 设置状态映射
        this.statusMapping = new Map();
        
        // 设置状态历史
        this.statusHistory = [];
        
        // 设置状态统计
        this.statusStatistics = {
            totalSaves: 0,
            totalDiscards: 0,
            autoSaves: 0,
            manualSaves: 0,
            averageSaveTime: 0
        };
        
        this.initializeIndicatorSystem();
    }
    
    // 初始化指示器系统
    initializeIndicatorSystem() {
        // 创建增强的表单状态指示器
        this.createEnhancedFormStatusIndicator();
        
        // 设置状态管理系统
        this.setupStatusManagementSystem();
        
        // 设置自动保存系统
        this.setupAutoSaveSystem();
        
        // 设置性能监控
        this.setupPerformanceMonitoring();
    }
    
    // 创建增强的表单状态指示器
    createEnhancedFormStatusIndicator() {
        const originalIndicator = FormStatusIndicator;
        
        this.EnhancedFormStatusIndicator = class extends originalIndicator {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加自动保存功能
                this.addAutoSaveFeatures();
                
                // 添加用户体验增强
                this.addUserExperienceEnhancements();
                
                // 添加可访问性支持
                this.addAccessibilitySupport();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = useState({
                    ...this.state,
                    isAutoSaving: false,
                    lastSaveTime: null,
                    saveProgress: 0,
                    statusMessage: '',
                    hasUnsavedChanges: false,
                    validationErrors: []
                });
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的指示器模式计算
                this.enhancedGetIndicatorMode = () => {
                    const baseMode = this.indicatorMode;
                    
                    // 添加自动保存状态
                    if (this.enhancedState.isAutoSaving) {
                        return 'auto_saving';
                    }
                    
                    // 添加验证错误状态
                    if (this.enhancedState.validationErrors.length > 0) {
                        return 'validation_error';
                    }
                    
                    return baseMode;
                };
                
                // 增强的保存操作
                this.enhancedSave = async () => {
                    const startTime = performance.now();
                    
                    try {
                        // 设置保存状态
                        this.enhancedState.saveProgress = 0;
                        this.enhancedState.statusMessage = 'Saving...';
                        
                        // 执行保存前验证
                        await this.validateBeforeSave();
                        
                        // 更新进度
                        this.enhancedState.saveProgress = 30;
                        
                        // 执行原始保存操作
                        await this.save();
                        
                        // 更新进度
                        this.enhancedState.saveProgress = 100;
                        
                        // 记录保存成功
                        this.recordSaveSuccess(startTime);
                        
                        // 更新状态
                        this.enhancedState.lastSaveTime = Date.now();
                        this.enhancedState.hasUnsavedChanges = false;
                        this.enhancedState.statusMessage = 'Saved successfully';
                        
                        // 清除状态消息
                        setTimeout(() => {
                            this.enhancedState.statusMessage = '';
                        }, 2000);
                        
                    } catch (error) {
                        this.handleSaveError(error);
                    } finally {
                        this.enhancedState.saveProgress = 0;
                    }
                };
                
                // 保存前验证
                this.validateBeforeSave = async () => {
                    const errors = [];
                    
                    // 执行字段验证
                    const fieldErrors = await this.validateFields();
                    errors.push(...fieldErrors);
                    
                    // 执行业务规则验证
                    const businessErrors = await this.validateBusinessRules();
                    errors.push(...businessErrors);
                    
                    this.enhancedState.validationErrors = errors;
                    
                    if (errors.length > 0) {
                        throw new Error(`Validation failed: ${errors.join(', ')}`);
                    }
                };
                
                // 验证字段
                this.validateFields = async () => {
                    const errors = [];
                    const model = this.props.model.root;
                    
                    // 检查必需字段
                    for (const [fieldName, field] of Object.entries(model.fields)) {
                        if (field.required && !model.data[fieldName]) {
                            errors.push(`${field.string || fieldName} is required`);
                        }
                    }
                    
                    return errors;
                };
                
                // 验证业务规则
                this.validateBusinessRules = async () => {
                    const errors = [];
                    
                    // 实现业务规则验证逻辑
                    // 例如：检查数据一致性、约束条件等
                    
                    return errors;
                };
                
                // 增强的丢弃操作
                this.enhancedDiscard = async () => {
                    try {
                        // 确认丢弃操作
                        if (this.indicatorConfig.enableConfirmation && this.enhancedState.hasUnsavedChanges) {
                            const confirmed = await this.confirmDiscard();
                            if (!confirmed) {
                                return;
                            }
                        }
                        
                        // 执行原始丢弃操作
                        await this.discard();
                        
                        // 记录丢弃操作
                        this.recordDiscardOperation();
                        
                        // 更新状态
                        this.enhancedState.hasUnsavedChanges = false;
                        this.enhancedState.validationErrors = [];
                        this.enhancedState.statusMessage = 'Changes discarded';
                        
                        // 清除状态消息
                        setTimeout(() => {
                            this.enhancedState.statusMessage = '';
                        }, 2000);
                        
                    } catch (error) {
                        this.handleDiscardError(error);
                    }
                };
                
                // 确认丢弃操作
                this.confirmDiscard = async () => {
                    return new Promise((resolve) => {
                        // 实现确认对话框逻辑
                        const confirmed = confirm('Are you sure you want to discard your changes?');
                        resolve(confirmed);
                    });
                };
                
                // 记录保存成功
                this.recordSaveSuccess = (startTime) => {
                    const saveTime = performance.now() - startTime;
                    
                    this.statusStatistics.totalSaves++;
                    this.statusStatistics.manualSaves++;
                    
                    // 更新平均保存时间
                    const totalSaves = this.statusStatistics.totalSaves;
                    this.statusStatistics.averageSaveTime = 
                        (this.statusStatistics.averageSaveTime * (totalSaves - 1) + saveTime) / totalSaves;
                    
                    // 添加到历史记录
                    this.statusHistory.push({
                        type: 'save',
                        timestamp: Date.now(),
                        duration: saveTime,
                        success: true
                    });
                };
                
                // 记录丢弃操作
                this.recordDiscardOperation = () => {
                    this.statusStatistics.totalDiscards++;
                    
                    // 添加到历史记录
                    this.statusHistory.push({
                        type: 'discard',
                        timestamp: Date.now(),
                        success: true
                    });
                };
                
                // 处理保存错误
                this.handleSaveError = (error) => {
                    console.error('Save error:', error);
                    
                    this.enhancedState.statusMessage = `Save failed: ${error.message}`;
                    
                    // 添加到历史记录
                    this.statusHistory.push({
                        type: 'save',
                        timestamp: Date.now(),
                        success: false,
                        error: error.message
                    });
                    
                    // 显示错误通知
                    if (this.notification) {
                        this.notification.add(error.message, { type: 'danger' });
                    }
                };
                
                // 处理丢弃错误
                this.handleDiscardError = (error) => {
                    console.error('Discard error:', error);
                    
                    this.enhancedState.statusMessage = `Discard failed: ${error.message}`;
                    
                    // 显示错误通知
                    if (this.notification) {
                        this.notification.add(error.message, { type: 'danger' });
                    }
                };
                
                // 获取状态文本
                this.getStatusText = () => {
                    const mode = this.enhancedGetIndicatorMode();
                    
                    const statusTexts = {
                        'saved': 'All changes saved',
                        'dirty': 'Unsaved changes',
                        'invalid': 'Please fix errors',
                        'auto_saving': 'Auto saving...',
                        'validation_error': 'Validation errors'
                    };
                    
                    return statusTexts[mode] || '';
                };
                
                // 获取状态图标
                this.getStatusIcon = () => {
                    const mode = this.enhancedGetIndicatorMode();
                    
                    const statusIcons = {
                        'saved': 'fa-check-circle text-success',
                        'dirty': 'fa-circle text-warning',
                        'invalid': 'fa-exclamation-circle text-danger',
                        'auto_saving': 'fa-spinner fa-spin text-info',
                        'validation_error': 'fa-times-circle text-danger'
                    };
                    
                    return statusIcons[mode] || '';
                };
                
                // 获取状态CSS类
                this.getStatusClass = () => {
                    const mode = this.enhancedGetIndicatorMode();
                    return `o_form_status_indicator_${mode}`;
                };
            }
            
            addAutoSaveFeatures() {
                // 自动保存功能
                this.autoSaveManager = {
                    timer: null,
                    enabled: this.indicatorConfig.enableAutoSave,
                    interval: this.indicatorConfig.autoSaveInterval,
                    
                    start: () => {
                        if (this.autoSaveManager.enabled) {
                            this.autoSaveManager.timer = setInterval(() => {
                                this.performAutoSave();
                            }, this.autoSaveManager.interval);
                        }
                    },
                    
                    stop: () => {
                        if (this.autoSaveManager.timer) {
                            clearInterval(this.autoSaveManager.timer);
                            this.autoSaveManager.timer = null;
                        }
                    },
                    
                    reset: () => {
                        this.autoSaveManager.stop();
                        this.autoSaveManager.start();
                    }
                };
                
                // 启动自动保存
                if (this.indicatorConfig.enableAutoSave) {
                    this.autoSaveManager.start();
                }
            }
            
            addUserExperienceEnhancements() {
                // 用户体验增强
                this.uxEnhancements = {
                    enableAnimations: () => this.enableStatusAnimations(),
                    enableTooltips: () => this.enableStatusTooltips(),
                    enableKeyboardShortcuts: () => this.enableKeyboardShortcuts(),
                    enableProgressIndicator: () => this.enableProgressIndicator()
                };
            }
            
            addAccessibilitySupport() {
                // 可访问性支持
                this.accessibilityManager = {
                    setAriaLabels: () => this.setAriaLabels(),
                    announceStatusChanges: () => this.announceStatusChanges(),
                    enableScreenReaderSupport: () => this.enableScreenReaderSupport()
                };
            }
            
            // 执行自动保存
            performAutoSave = async () => {
                if (this.enhancedGetIndicatorMode() === 'dirty' && !this.enhancedState.isAutoSaving) {
                    try {
                        this.enhancedState.isAutoSaving = true;
                        await this.save();
                        
                        this.statusStatistics.autoSaves++;
                        
                        // 显示自动保存通知
                        if (this.notification) {
                            this.notification.add('Auto saved', { type: 'info' });
                        }
                        
                    } catch (error) {
                        console.error('Auto save failed:', error);
                    } finally {
                        this.enhancedState.isAutoSaving = false;
                    }
                }
            };
            
            // 启用状态动画
            enableStatusAnimations() {
                // 实现状态变化动画
                this.animationsEnabled = true;
            }
            
            // 启用状态工具提示
            enableStatusTooltips() {
                // 实现状态工具提示
                this.tooltipsEnabled = true;
            }
            
            // 启用键盘快捷键
            enableKeyboardShortcuts() {
                // Ctrl+S 保存
                document.addEventListener('keydown', (event) => {
                    if (event.ctrlKey && event.key === 's') {
                        event.preventDefault();
                        this.enhancedSave();
                    }
                });
            }
            
            // 设置ARIA标签
            setAriaLabels() {
                const indicatorElement = this.rootRef.el;
                if (indicatorElement) {
                    indicatorElement.setAttribute('role', 'status');
                    indicatorElement.setAttribute('aria-live', 'polite');
                    indicatorElement.setAttribute('aria-label', 'Form status indicator');
                }
            }
            
            // 宣布状态变化
            announceStatusChanges() {
                const statusText = this.getStatusText();
                if (statusText) {
                    this.announceToScreenReader(statusText);
                }
            }
            
            // 屏幕阅读器通知
            announceToScreenReader(message) {
                const announcement = document.createElement('div');
                announcement.setAttribute('aria-live', 'assertive');
                announcement.setAttribute('aria-atomic', 'true');
                announcement.className = 'sr-only';
                announcement.textContent = message;
                
                document.body.appendChild(announcement);
                setTimeout(() => document.body.removeChild(announcement), 1000);
            }
            
            // 重写原始方法
            save() {
                return this.enhancedSave();
            }
            
            discard() {
                return this.enhancedDiscard();
            }
            
            get indicatorMode() {
                return this.enhancedGetIndicatorMode();
            }
            
            // 获取状态统计
            getStatusStatistics() {
                return {
                    ...this.statusStatistics,
                    currentMode: this.enhancedGetIndicatorMode(),
                    hasUnsavedChanges: this.enhancedState.hasUnsavedChanges,
                    validationErrorCount: this.enhancedState.validationErrors.length
                };
            }
            
            // 组件销毁前
            onWillDestroy() {
                // 停止自动保存
                this.autoSaveManager.stop();
                
                // 清理事件监听器
                if (this.keyboardListener) {
                    document.removeEventListener('keydown', this.keyboardListener);
                }
            }
        };
    }
    
    // 设置状态管理系统
    setupStatusManagementSystem() {
        // 状态映射
        this.statusMapping.set('saved', {
            label: 'Saved',
            icon: 'fa-check-circle',
            color: 'success',
            description: 'All changes have been saved'
        });
        
        this.statusMapping.set('dirty', {
            label: 'Unsaved',
            icon: 'fa-circle',
            color: 'warning',
            description: 'There are unsaved changes'
        });
        
        this.statusMapping.set('invalid', {
            label: 'Invalid',
            icon: 'fa-exclamation-circle',
            color: 'danger',
            description: 'Please fix validation errors'
        });
    }
    
    // 设置自动保存系统
    setupAutoSaveSystem() {
        this.autoSaveConfig = {
            enabled: this.indicatorConfig.enableAutoSave,
            interval: this.indicatorConfig.autoSaveInterval,
            conditions: ['dirty', 'valid'],
            excludeFields: ['password', 'secret']
        };
    }
    
    // 设置性能监控
    setupPerformanceMonitoring() {
        this.performanceConfig = {
            enableMonitoring: true,
            sampleRate: 0.1,
            thresholds: {
                saveTime: 2000,    // 2秒
                discardTime: 500   // 500ms
            }
        };
    }
    
    // 创建表单状态指示器
    createFormStatusIndicator(props) {
        return new this.EnhancedFormStatusIndicator(props);
    }
    
    // 获取状态映射
    getStatusMapping() {
        return this.statusMapping;
    }
    
    // 获取状态历史
    getStatusHistory() {
        return this.statusHistory;
    }
    
    // 获取状态统计
    getStatusStatistics() {
        return {
            ...this.statusStatistics,
            historyCount: this.statusHistory.length,
            statusMappingCount: this.statusMapping.size
        };
    }
    
    // 清理状态历史
    clearStatusHistory() {
        this.statusHistory = [];
    }
    
    // 销毁管理器
    destroy() {
        // 清理状态映射
        this.statusMapping.clear();
        
        // 清理状态历史
        this.statusHistory = [];
        
        // 重置统计
        this.statusStatistics = {
            totalSaves: 0,
            totalDiscards: 0,
            autoSaves: 0,
            manualSaves: 0,
            averageSaveTime: 0
        };
    }
}

// 使用示例
const statusIndicatorManager = new FormStatusIndicatorManager();

// 创建表单状态指示器
const formStatusIndicator = statusIndicatorManager.createFormStatusIndicator({
    model: model,
    save: async () => {
        console.log('Saving form...');
        // 实现保存逻辑
    },
    discard: async () => {
        console.log('Discarding changes...');
        // 实现丢弃逻辑
    }
});

// 获取统计信息
const stats = statusIndicatorManager.getStatusStatistics();
console.log('Form status indicator statistics:', stats);
```

## 技术特点

### 1. 状态监控
- **实时监控**: 实时监控表单状态变化
- **多状态支持**: 支持多种表单状态
- **事件驱动**: 基于事件的状态更新
- **自动响应**: 自动响应状态变化

### 2. 用户交互
- **按钮控制**: 智能的按钮启用/禁用控制
- **视觉反馈**: 清晰的视觉状态反馈
- **操作确认**: 重要操作的确认机制
- **错误处理**: 完善的错误处理和提示

### 3. 性能优化
- **事件监听**: 高效的事件监听机制
- **状态缓存**: 智能的状态缓存
- **异步操作**: 异步的保存和丢弃操作
- **内存管理**: 有效的内存管理

### 4. 扩展性
- **配置驱动**: 配置驱动的功能开关
- **钩子支持**: 丰富的钩子函数支持
- **组件扩展**: 支持组件功能扩展
- **自定义状态**: 支持自定义状态类型

## 设计模式

### 1. 状态模式 (State Pattern)
- **状态管理**: 管理表单的不同状态
- **状态切换**: 在不同状态间切换
- **行为变化**: 根据状态改变行为

### 2. 观察者模式 (Observer Pattern)
- **事件监听**: 监听模型状态变化
- **自动更新**: 自动更新指示器状态
- **响应式**: 响应式的状态更新

### 3. 命令模式 (Command Pattern)
- **操作封装**: 封装保存和丢弃操作
- **异步执行**: 异步执行命令
- **撤销支持**: 支持操作的撤销

### 4. 策略模式 (Strategy Pattern)
- **状态策略**: 不同的状态处理策略
- **验证策略**: 不同的验证策略
- **保存策略**: 不同的保存策略

## 注意事项

1. **状态一致性**: 确保状态的一致性和准确性
2. **性能考虑**: 避免频繁的状态计算和DOM更新
3. **用户体验**: 提供清晰的状态反馈和操作指导
4. **错误处理**: 完善的错误处理和恢复机制

## 扩展建议

1. **自动保存**: 添加智能自动保存功能
2. **状态历史**: 添加状态变化历史记录
3. **进度指示**: 添加操作进度指示器
4. **快捷键**: 支持键盘快捷键操作
5. **可访问性**: 增强可访问性支持

该表单状态指示器为Odoo Web客户端提供了专业的表单状态管理功能，通过实时监控和智能控制确保了良好的用户体验和数据安全性。

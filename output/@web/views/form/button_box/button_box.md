# ButtonBox - 按钮盒子组件

## 概述

`button_box.js` 是 Odoo Web 客户端表单视图的按钮盒子组件，负责管理和显示表单中的按钮集合。该模块包含47行代码，是一个OWL组件，专门用于在表单视图中组织和显示多个按钮，具备响应式布局、按钮分组、下拉菜单、可见性控制等特性，是表单视图系统中按钮管理的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/form/button_box/button_box.js`
- **行数**: 47
- **模块**: `@web/views/form/button_box/button_box`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/hooks'             // 工具钩子
'@web/core/dropdown/dropdown'       // 下拉菜单组件
'@web/core/dropdown/dropdown_item'  // 下拉菜单项组件
'@odoo/owl'                         // OWL框架
```

## 主要组件定义

### 1. ButtonBox - 按钮盒子组件

```javascript
class ButtonBox extends Component {
    static template = "web.Form.ButtonBox";
    static components = { Dropdown, DropdownItem };
    static props = {
        slots: Object,
        class: { type: String, optional: true },
    };
    static defaultProps = {
        class: "",
    };

    setup() {
        const ui = useService("ui");
        onWillRender(() => {
            const maxVisibleButtons = [0, 0, 0, 7, 4, 5, 8][ui.size] ?? 8;
            const allVisibleButtons = Object.entries(this.props.slots)
                .filter(([_, slot]) => this.isSlotVisible(slot))
                .map(([slotName]) => slotName);
            if (allVisibleButtons.length <= maxVisibleButtons) {
                this.visibleButtons = allVisibleButtons;
                this.additionalButtons = [];
                this.isFull = allVisibleButtons.length === maxVisibleButtons;
            } else {
                // -1 for "More" dropdown
                const splitIndex = Math.max(maxVisibleButtons - 1, 0);
                this.visibleButtons = allVisibleButtons.slice(0, splitIndex);
                this.additionalButtons = allVisibleButtons.slice(splitIndex);
                this.isFull = true;
            }
        });
    }

    isSlotVisible(slot) {
        return !("isVisible" in slot) || slot.isVisible;
    }
}
```

**组件特性**:
- **专用模板**: 使用Form.ButtonBox专用模板
- **子组件**: 集成Dropdown和DropdownItem组件
- **插槽支持**: 支持插槽机制管理按钮
- **响应式**: 根据UI尺寸调整按钮显示

## 核心功能

### 1. 响应式按钮布局

```javascript
const maxVisibleButtons = [0, 0, 0, 7, 4, 5, 8][ui.size] ?? 8;
```

**布局配置**:
- **尺寸映射**: 根据UI尺寸确定最大可见按钮数
- **断点设置**: 不同屏幕尺寸的按钮数量配置
- **默认值**: 提供默认的最大按钮数量
- **响应式**: 自动适应不同设备尺寸

### 2. 按钮可见性管理

```javascript
const allVisibleButtons = Object.entries(this.props.slots)
    .filter(([_, slot]) => this.isSlotVisible(slot))
    .map(([slotName]) => slotName);

isSlotVisible(slot) {
    return !("isVisible" in slot) || slot.isVisible;
}
```

**可见性管理功能**:
- **插槽过滤**: 过滤可见的按钮插槽
- **可见性检查**: 检查插槽的可见性属性
- **默认可见**: 默认情况下插槽是可见的
- **动态控制**: 支持动态控制按钮可见性

### 3. 按钮分组逻辑

```javascript
if (allVisibleButtons.length <= maxVisibleButtons) {
    this.visibleButtons = allVisibleButtons;
    this.additionalButtons = [];
    this.isFull = allVisibleButtons.length === maxVisibleButtons;
} else {
    // -1 for "More" dropdown
    const splitIndex = Math.max(maxVisibleButtons - 1, 0);
    this.visibleButtons = allVisibleButtons.slice(0, splitIndex);
    this.additionalButtons = allVisibleButtons.slice(splitIndex);
    this.isFull = true;
}
```

**分组逻辑功能**:
- **容量检查**: 检查按钮是否超出显示容量
- **直接显示**: 按钮数量不超过时直接显示
- **分组显示**: 超出时分为可见按钮和附加按钮
- **下拉菜单**: 附加按钮放入"更多"下拉菜单
- **状态标记**: 标记按钮盒子是否已满

## 使用场景

### 1. 按钮盒子管理器

```javascript
// 按钮盒子管理器
class ButtonBoxManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置按钮盒子配置
        this.buttonBoxConfig = {
            enableResponsive: true,
            enableGrouping: true,
            enableAnimation: true,
            enableTooltips: true,
            maxButtonsPerRow: 8,
            breakpoints: {
                xs: 0,    // 超小屏幕
                sm: 0,    // 小屏幕
                md: 7,    // 中等屏幕
                lg: 4,    // 大屏幕
                xl: 5,    // 超大屏幕
                xxl: 8    // 超超大屏幕
            },
            animationDuration: 300
        };
        
        // 设置按钮注册表
        this.buttonRegistry = new Map();
        
        // 设置按钮组
        this.buttonGroups = new Map();
        
        // 设置按钮统计
        this.buttonStatistics = {
            totalButtons: 0,
            visibleButtons: 0,
            hiddenButtons: 0,
            groupedButtons: 0,
            clickCount: 0
        };
        
        this.initializeButtonBoxSystem();
    }
    
    // 初始化按钮盒子系统
    initializeButtonBoxSystem() {
        // 创建增强的按钮盒子
        this.createEnhancedButtonBox();
        
        // 设置响应式系统
        this.setupResponsiveSystem();
        
        // 设置动画系统
        this.setupAnimationSystem();
        
        // 设置事件系统
        this.setupEventSystem();
    }
    
    // 创建增强的按钮盒子
    createEnhancedButtonBox() {
        const originalButtonBox = ButtonBox;
        
        this.EnhancedButtonBox = class extends originalButtonBox {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加动画支持
                this.addAnimationSupport();
                
                // 添加事件监听
                this.addEventListeners();
                
                // 添加工具提示支持
                this.addTooltipSupport();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = useState({
                    isAnimating: false,
                    hoveredButton: null,
                    expandedDropdown: false,
                    buttonOrder: [],
                    customLayout: false
                });
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 智能按钮布局
                this.getSmartButtonLayout = () => {
                    const ui = this.env.services.ui;
                    const baseLayout = this.getButtonLayout();
                    
                    // 应用自定义布局规则
                    if (this.enhancedState.customLayout) {
                        return this.applyCustomLayout(baseLayout);
                    }
                    
                    // 应用优先级排序
                    return this.applyPrioritySort(baseLayout);
                };
                
                // 获取基础按钮布局
                this.getButtonLayout = () => {
                    const ui = this.env.services.ui;
                    const maxVisibleButtons = this.getMaxVisibleButtons(ui.size);
                    const allVisibleButtons = this.getAllVisibleButtons();
                    
                    return this.calculateButtonLayout(allVisibleButtons, maxVisibleButtons);
                };
                
                // 获取最大可见按钮数
                this.getMaxVisibleButtons = (uiSize) => {
                    const breakpoints = this.buttonBoxConfig.breakpoints;
                    const sizeMap = ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'];
                    const sizeName = sizeMap[uiSize] || 'xxl';
                    return breakpoints[sizeName] || this.buttonBoxConfig.maxButtonsPerRow;
                };
                
                // 获取所有可见按钮
                this.getAllVisibleButtons = () => {
                    return Object.entries(this.props.slots)
                        .filter(([_, slot]) => this.isSlotVisible(slot))
                        .map(([slotName, slot]) => ({
                            name: slotName,
                            slot: slot,
                            priority: slot.priority || 0,
                            group: slot.group || 'default'
                        }));
                };
                
                // 计算按钮布局
                this.calculateButtonLayout = (buttons, maxVisible) => {
                    if (buttons.length <= maxVisible) {
                        return {
                            visibleButtons: buttons.map(b => b.name),
                            additionalButtons: [],
                            isFull: buttons.length === maxVisible,
                            hasDropdown: false
                        };
                    } else {
                        const splitIndex = Math.max(maxVisible - 1, 0);
                        return {
                            visibleButtons: buttons.slice(0, splitIndex).map(b => b.name),
                            additionalButtons: buttons.slice(splitIndex).map(b => b.name),
                            isFull: true,
                            hasDropdown: true
                        };
                    }
                };
                
                // 应用自定义布局
                this.applyCustomLayout = (layout) => {
                    // 实现自定义布局逻辑
                    return layout;
                };
                
                // 应用优先级排序
                this.applyPrioritySort = (layout) => {
                    const buttons = this.getAllVisibleButtons();
                    
                    // 按优先级排序
                    buttons.sort((a, b) => (b.priority || 0) - (a.priority || 0));
                    
                    // 重新计算布局
                    return this.calculateButtonLayout(buttons, layout.visibleButtons.length + layout.additionalButtons.length);
                };
                
                // 处理按钮点击
                this.onButtonClick = (buttonName, event) => {
                    // 记录点击统计
                    this.buttonStatistics.clickCount++;
                    
                    // 应用点击动画
                    if (this.buttonBoxConfig.enableAnimation) {
                        this.applyClickAnimation(buttonName);
                    }
                    
                    // 触发自定义事件
                    this.trigger('button-clicked', {
                        buttonName: buttonName,
                        event: event
                    });
                };
                
                // 应用点击动画
                this.applyClickAnimation = (buttonName) => {
                    this.enhancedState.isAnimating = true;
                    
                    // 添加动画类
                    const buttonElement = this.getButtonElement(buttonName);
                    if (buttonElement) {
                        buttonElement.classList.add('o_button_clicked');
                        
                        setTimeout(() => {
                            buttonElement.classList.remove('o_button_clicked');
                            this.enhancedState.isAnimating = false;
                        }, this.buttonBoxConfig.animationDuration);
                    }
                };
                
                // 获取按钮元素
                this.getButtonElement = (buttonName) => {
                    return this.rootRef.el?.querySelector(`[data-button-name="${buttonName}"]`);
                };
                
                // 处理下拉菜单切换
                this.onDropdownToggle = (isOpen) => {
                    this.enhancedState.expandedDropdown = isOpen;
                    
                    // 应用展开动画
                    if (this.buttonBoxConfig.enableAnimation) {
                        this.applyDropdownAnimation(isOpen);
                    }
                };
                
                // 应用下拉菜单动画
                this.applyDropdownAnimation = (isOpen) => {
                    const dropdownElement = this.rootRef.el?.querySelector('.o_button_box_dropdown');
                    if (dropdownElement) {
                        if (isOpen) {
                            dropdownElement.classList.add('o_dropdown_opening');
                        } else {
                            dropdownElement.classList.add('o_dropdown_closing');
                        }
                        
                        setTimeout(() => {
                            dropdownElement.classList.remove('o_dropdown_opening', 'o_dropdown_closing');
                        }, this.buttonBoxConfig.animationDuration);
                    }
                };
                
                // 处理按钮悬停
                this.onButtonHover = (buttonName, isHovered) => {
                    this.enhancedState.hoveredButton = isHovered ? buttonName : null;
                    
                    // 显示/隐藏工具提示
                    if (this.buttonBoxConfig.enableTooltips) {
                        this.toggleTooltip(buttonName, isHovered);
                    }
                };
                
                // 切换工具提示
                this.toggleTooltip = (buttonName, show) => {
                    const buttonElement = this.getButtonElement(buttonName);
                    if (buttonElement) {
                        if (show) {
                            this.showTooltip(buttonElement, buttonName);
                        } else {
                            this.hideTooltip(buttonElement);
                        }
                    }
                };
                
                // 显示工具提示
                this.showTooltip = (element, buttonName) => {
                    const slot = this.props.slots[buttonName];
                    const tooltip = slot?.tooltip || slot?.string || buttonName;
                    
                    // 创建工具提示元素
                    const tooltipElement = document.createElement('div');
                    tooltipElement.className = 'o_button_tooltip';
                    tooltipElement.textContent = tooltip;
                    
                    // 定位工具提示
                    this.positionTooltip(tooltipElement, element);
                    
                    // 添加到DOM
                    document.body.appendChild(tooltipElement);
                    
                    // 存储引用
                    element._tooltip = tooltipElement;
                };
                
                // 隐藏工具提示
                this.hideTooltip = (element) => {
                    if (element._tooltip) {
                        document.body.removeChild(element._tooltip);
                        delete element._tooltip;
                    }
                };
                
                // 定位工具提示
                this.positionTooltip = (tooltip, target) => {
                    const rect = target.getBoundingClientRect();
                    tooltip.style.position = 'absolute';
                    tooltip.style.top = `${rect.top - tooltip.offsetHeight - 5}px`;
                    tooltip.style.left = `${rect.left + rect.width / 2 - tooltip.offsetWidth / 2}px`;
                    tooltip.style.zIndex = '1000';
                };
                
                // 获取按钮统计
                this.getButtonStatistics = () => {
                    const layout = this.getSmartButtonLayout();
                    return {
                        ...this.buttonStatistics,
                        visibleButtons: layout.visibleButtons.length,
                        hiddenButtons: layout.additionalButtons.length,
                        hasDropdown: layout.hasDropdown,
                        isFull: layout.isFull
                    };
                };
            }
            
            addAnimationSupport() {
                // 监听布局变化
                useEffect(() => {
                    if (this.buttonBoxConfig.enableAnimation) {
                        this.applyLayoutAnimation();
                    }
                });
            }
            
            applyLayoutAnimation() {
                const buttonElements = this.rootRef.el?.querySelectorAll('.o_button_box_button');
                if (buttonElements) {
                    buttonElements.forEach((element, index) => {
                        element.style.animationDelay = `${index * 50}ms`;
                        element.classList.add('o_button_appear');
                    });
                }
            }
            
            addEventListeners() {
                // 监听按钮点击
                useExternalListener(this.rootRef, 'click', (event) => {
                    const buttonElement = event.target.closest('[data-button-name]');
                    if (buttonElement) {
                        const buttonName = buttonElement.dataset.buttonName;
                        this.onButtonClick(buttonName, event);
                    }
                });
                
                // 监听按钮悬停
                useExternalListener(this.rootRef, 'mouseenter', (event) => {
                    const buttonElement = event.target.closest('[data-button-name]');
                    if (buttonElement) {
                        const buttonName = buttonElement.dataset.buttonName;
                        this.onButtonHover(buttonName, true);
                    }
                }, true);
                
                useExternalListener(this.rootRef, 'mouseleave', (event) => {
                    const buttonElement = event.target.closest('[data-button-name]');
                    if (buttonElement) {
                        const buttonName = buttonElement.dataset.buttonName;
                        this.onButtonHover(buttonName, false);
                    }
                }, true);
            }
            
            addTooltipSupport() {
                // 清理工具提示
                onWillUnmount(() => {
                    const tooltips = document.querySelectorAll('.o_button_tooltip');
                    tooltips.forEach(tooltip => {
                        if (tooltip.parentNode) {
                            tooltip.parentNode.removeChild(tooltip);
                        }
                    });
                });
            }
            
            // 重写渲染前处理
            onWillRender() {
                const layout = this.getSmartButtonLayout();
                this.visibleButtons = layout.visibleButtons;
                this.additionalButtons = layout.additionalButtons;
                this.isFull = layout.isFull;
                this.hasDropdown = layout.hasDropdown;
                
                // 更新统计
                this.updateStatistics(layout);
            }
            
            updateStatistics(layout) {
                this.buttonStatistics.visibleButtons = layout.visibleButtons.length;
                this.buttonStatistics.hiddenButtons = layout.additionalButtons.length;
                this.buttonStatistics.groupedButtons = layout.hasDropdown ? layout.additionalButtons.length : 0;
            }
        };
    }
    
    // 设置响应式系统
    setupResponsiveSystem() {
        this.responsiveConfig = {
            enableBreakpoints: true,
            enableFluidLayout: true,
            enableAdaptiveButtons: true
        };
    }
    
    // 设置动画系统
    setupAnimationSystem() {
        this.animationConfig = {
            enableTransitions: true,
            enableHoverEffects: true,
            enableClickEffects: true,
            duration: this.buttonBoxConfig.animationDuration
        };
    }
    
    // 设置事件系统
    setupEventSystem() {
        this.eventConfig = {
            enableClickTracking: true,
            enableHoverTracking: true,
            enableAnalytics: true
        };
    }
    
    // 注册按钮
    registerButton(name, config) {
        this.buttonRegistry.set(name, config);
    }
    
    // 移除按钮
    removeButton(name) {
        this.buttonRegistry.delete(name);
    }
    
    // 创建按钮组
    createButtonGroup(name, buttons) {
        this.buttonGroups.set(name, buttons);
    }
    
    // 创建按钮盒子实例
    createButtonBox(props) {
        return new this.EnhancedButtonBox(props);
    }
    
    // 获取按钮统计
    getButtonStatistics() {
        return {
            ...this.buttonStatistics,
            registrySize: this.buttonRegistry.size,
            groupCount: this.buttonGroups.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理注册表
        this.buttonRegistry.clear();
        this.buttonGroups.clear();
        
        // 重置统计
        this.buttonStatistics = {
            totalButtons: 0,
            visibleButtons: 0,
            hiddenButtons: 0,
            groupedButtons: 0,
            clickCount: 0
        };
    }
}

// 使用示例
const buttonBoxManager = new ButtonBoxManager();

// 创建增强的按钮盒子
const EnhancedButtonBox = buttonBoxManager.EnhancedButtonBox;

// 注册按钮
buttonBoxManager.registerButton('save', {
    label: 'Save',
    icon: 'fa-save',
    priority: 10
});

// 获取统计信息
const stats = buttonBoxManager.getButtonStatistics();
console.log('ButtonBox statistics:', stats);
```

## 技术特点

### 1. 响应式设计
- **断点配置**: 根据屏幕尺寸配置按钮数量
- **自适应布局**: 自动适应不同设备尺寸
- **流体布局**: 支持流体布局设计
- **移动优化**: 优化移动端显示效果

### 2. 智能分组
- **容量管理**: 智能管理按钮显示容量
- **自动分组**: 自动将超出按钮放入下拉菜单
- **优先级排序**: 支持按钮优先级排序
- **动态调整**: 动态调整按钮布局

### 3. 插槽机制
- **灵活插槽**: 使用插槽机制管理按钮
- **可见性控制**: 支持按钮可见性控制
- **动态内容**: 支持动态按钮内容
- **组件复用**: 高度可复用的组件设计

### 4. 用户体验
- **直观布局**: 直观的按钮布局
- **交互反馈**: 良好的用户交互反馈
- **动画效果**: 支持动画和过渡效果
- **工具提示**: 支持按钮工具提示

## 设计模式

### 1. 组件模式 (Component Pattern)
- **封装性**: 封装按钮盒子功能
- **可复用**: 高度可复用的组件
- **接口标准**: 标准化的组件接口

### 2. 策略模式 (Strategy Pattern)
- **布局策略**: 不同尺寸的布局策略
- **分组策略**: 不同的按钮分组策略
- **显示策略**: 不同的按钮显示策略

### 3. 观察者模式 (Observer Pattern)
- **尺寸监听**: 监听UI尺寸变化
- **自动更新**: 自动更新按钮布局
- **事件响应**: 响应用户交互事件

### 4. 工厂模式 (Factory Pattern)
- **按钮创建**: 创建不同类型的按钮
- **布局生成**: 生成不同的布局配置
- **组件实例**: 创建组件实例

## 注意事项

1. **性能考虑**: 避免频繁的布局重新计算
2. **用户体验**: 确保按钮的可访问性和易用性
3. **响应式**: 确保在不同设备上的良好体验
4. **内存管理**: 及时清理事件监听器和引用

## 扩展建议

1. **主题支持**: 支持不同的视觉主题
2. **动画增强**: 增强动画和过渡效果
3. **自定义布局**: 支持更灵活的自定义布局
4. **键盘导航**: 添加键盘导航支持
5. **拖拽排序**: 支持按钮的拖拽排序功能

该按钮盒子组件为Odoo Web客户端提供了智能的按钮管理功能，通过响应式设计、智能分组和良好的用户体验确保了优秀的按钮显示和交互效果。

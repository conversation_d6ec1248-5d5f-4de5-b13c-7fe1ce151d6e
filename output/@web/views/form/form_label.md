# FormLabel - 表单标签组件

## 概述

`form_label.js` 是 Odoo Web 客户端表单视图的标签组件，负责渲染表单字段的标签。该模块包含73行代码，是一个OWL组件，专门用于显示字段标签，具备视觉反馈、工具提示、样式管理、状态指示等特性，是表单视图系统中字段标签显示的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/form/form_label.js`
- **行数**: 73
- **模块**: `@web/views/form/form_label`

## 依赖关系

```javascript
// 核心依赖
'@web/views/fields/field'               // 字段组件
'@web/session'                          // 会话服务
'@web/views/fields/field_tooltip'       // 字段工具提示
'@web/core/l10n/translation'            // 翻译服务
'@odoo/owl'                             // OWL框架
```

## 主要组件定义

### 1. FormLabel - 表单标签组件

```javascript
class FormLabel extends Component {
    static template = "web.FormLabel";
    static props = {
        fieldInfo: { type: Object },
        record: { type: Object },
        fieldName: { type: String },
        className: { type: String, optional: true },
        string: { type: String },
        id: { type: String },
        notMuttedLabel: { type: Boolean, optional: true },
    };

    get className() {
        const { invalid, empty, readonly } = fieldVisualFeedback(
            this.props.fieldInfo.field,
            this.props.record,
            this.props.fieldName,
            this.props.fieldInfo
        );
        const classes = this.props.className ? [this.props.className] : [];
        if (invalid) {
            classes.push("o_field_invalid");
        }
        if (empty) {
            classes.push("o_form_label_empty");
        }
        if (readonly && !this.props.notMuttedLabel) {
            classes.push("o_form_label_readonly");
        }
        return classes.join(" ");
    }

    get hasTooltip() {
        return Boolean(odoo.debug) || this.tooltipHelp;
    }

    get tooltipHelp() {
        return this.props.fieldInfo.field.help;
    }

    get tooltipInfo() {
        if (!this.hasTooltip) {
            return false;
        }
        return getTooltipInfo({
            viewMode: "form",
            resModel: this.props.record.resModel,
            field: this.props.fieldInfo.field,
            fieldInfo: this.props.fieldInfo,
            help: this.tooltipHelp,
        });
    }
}
```

**组件特性**:
- **专用模板**: 使用FormLabel专用模板
- **属性配置**: 丰富的属性配置选项
- **视觉反馈**: 基于字段状态的视觉反馈
- **工具提示**: 支持字段工具提示

## 核心功能

### 1. 样式类管理

```javascript
get className() {
    const { invalid, empty, readonly } = fieldVisualFeedback(
        this.props.fieldInfo.field,
        this.props.record,
        this.props.fieldName,
        this.props.fieldInfo
    );
    const classes = this.props.className ? [this.props.className] : [];
    if (invalid) {
        classes.push("o_field_invalid");
    }
    if (empty) {
        classes.push("o_form_label_empty");
    }
    if (readonly && !this.props.notMuttedLabel) {
        classes.push("o_form_label_readonly");
    }
    return classes.join(" ");
}
```

**样式管理功能**:
- **视觉反馈**: 获取字段的视觉反馈状态
- **基础样式**: 应用基础CSS类
- **状态样式**: 根据字段状态添加样式类
- **只读样式**: 处理只读字段的样式
- **自定义样式**: 支持自定义CSS类

### 2. 工具提示管理

```javascript
get hasTooltip() {
    return Boolean(odoo.debug) || this.tooltipHelp;
}

get tooltipHelp() {
    return this.props.fieldInfo.field.help;
}

get tooltipInfo() {
    if (!this.hasTooltip) {
        return false;
    }
    return getTooltipInfo({
        viewMode: "form",
        resModel: this.props.record.resModel,
        field: this.props.fieldInfo.field,
        fieldInfo: this.props.fieldInfo,
        help: this.tooltipHelp,
    });
}
```

**工具提示功能**:
- **提示检测**: 检测是否需要显示工具提示
- **调试模式**: 在调试模式下显示工具提示
- **帮助文本**: 获取字段的帮助文本
- **提示信息**: 生成完整的工具提示信息
- **上下文信息**: 包含视图模式和模型信息

## 使用场景

### 1. 表单标签管理器

```javascript
// 表单标签管理器
class FormLabelManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置标签配置
        this.labelConfig = {
            enableTooltips: true,
            enableVisualFeedback: true,
            enableCustomStyles: true,
            enableAccessibility: true,
            showRequiredIndicator: true,
            showHelpIcon: true,
            tooltipDelay: 500
        };
        
        // 设置样式映射
        this.styleMapping = new Map();
        
        // 设置工具提示缓存
        this.tooltipCache = new Map();
        
        // 设置标签统计
        this.labelStatistics = {
            totalLabels: 0,
            invalidLabels: 0,
            emptyLabels: 0,
            readonlyLabels: 0,
            tooltipShown: 0
        };
        
        this.initializeLabelSystem();
    }
    
    // 初始化标签系统
    initializeLabelSystem() {
        // 创建增强的表单标签
        this.createEnhancedFormLabel();
        
        // 设置样式系统
        this.setupStyleSystem();
        
        // 设置工具提示系统
        this.setupTooltipSystem();
        
        // 设置可访问性支持
        this.setupAccessibilitySupport();
    }
    
    // 创建增强的表单标签
    createEnhancedFormLabel() {
        const originalLabel = FormLabel;
        
        this.EnhancedFormLabel = class extends originalLabel {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加事件监听
                this.addEventListeners();
                
                // 添加可访问性支持
                this.addAccessibilitySupport();
                
                // 添加动画支持
                this.addAnimationSupport();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = useState({
                    isHovered: false,
                    isFocused: false,
                    tooltipVisible: false,
                    animationClass: '',
                    customStyles: {}
                });
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 智能样式类生成
                this.getSmartClassName = () => {
                    const baseClassName = this.className; // 调用原始getter
                    const enhancedClasses = [];
                    
                    // 添加状态类
                    if (this.enhancedState.isHovered) {
                        enhancedClasses.push('o_form_label_hovered');
                    }
                    
                    if (this.enhancedState.isFocused) {
                        enhancedClasses.push('o_form_label_focused');
                    }
                    
                    // 添加动画类
                    if (this.enhancedState.animationClass) {
                        enhancedClasses.push(this.enhancedState.animationClass);
                    }
                    
                    // 添加自定义类
                    const customClasses = this.getCustomClasses();
                    enhancedClasses.push(...customClasses);
                    
                    return [baseClassName, ...enhancedClasses].filter(Boolean).join(' ');
                };
                
                // 获取自定义样式类
                this.getCustomClasses = () => {
                    const classes = [];
                    const { fieldInfo, record } = this.props;
                    
                    // 基于字段类型的样式
                    if (fieldInfo.field.type) {
                        classes.push(`o_form_label_${fieldInfo.field.type}`);
                    }
                    
                    // 基于字段重要性的样式
                    if (fieldInfo.field.required) {
                        classes.push('o_form_label_required');
                    }
                    
                    // 基于字段组的样式
                    if (fieldInfo.field.group) {
                        classes.push(`o_form_label_group_${fieldInfo.field.group}`);
                    }
                    
                    return classes;
                };
                
                // 增强的工具提示信息
                this.getEnhancedTooltipInfo = () => {
                    const baseTooltipInfo = this.tooltipInfo; // 调用原始getter
                    
                    if (!baseTooltipInfo) return false;
                    
                    // 添加增强信息
                    const enhanced = {
                        ...baseTooltipInfo,
                        fieldType: this.props.fieldInfo.field.type,
                        fieldRequired: this.props.fieldInfo.field.required,
                        fieldReadonly: this.props.fieldInfo.field.readonly,
                        recordId: this.props.record.resId,
                        modelName: this.props.record.resModel
                    };
                    
                    // 添加调试信息
                    if (odoo.debug) {
                        enhanced.debugInfo = {
                            fieldName: this.props.fieldName,
                            fieldId: this.props.id,
                            componentId: this.__owl__.id
                        };
                    }
                    
                    return enhanced;
                };
                
                // 处理标签点击
                this.onLabelClick = (event) => {
                    // 聚焦到对应的字段
                    const fieldElement = document.getElementById(this.props.id);
                    if (fieldElement) {
                        fieldElement.focus();
                    }
                    
                    // 触发自定义事件
                    this.trigger('label-clicked', {
                        fieldName: this.props.fieldName,
                        fieldInfo: this.props.fieldInfo
                    });
                    
                    // 记录统计
                    this.recordLabelInteraction('click');
                };
                
                // 处理鼠标悬停
                this.onMouseEnter = (event) => {
                    this.enhancedState.isHovered = true;
                    
                    // 显示工具提示
                    if (this.hasTooltip && this.labelConfig.enableTooltips) {
                        this.showTooltip();
                    }
                    
                    // 记录统计
                    this.recordLabelInteraction('hover');
                };
                
                // 处理鼠标离开
                this.onMouseLeave = (event) => {
                    this.enhancedState.isHovered = false;
                    
                    // 隐藏工具提示
                    this.hideTooltip();
                };
                
                // 显示工具提示
                this.showTooltip = () => {
                    if (this.enhancedState.tooltipVisible) return;
                    
                    setTimeout(() => {
                        if (this.enhancedState.isHovered) {
                            this.enhancedState.tooltipVisible = true;
                            this.labelStatistics.tooltipShown++;
                        }
                    }, this.labelConfig.tooltipDelay);
                };
                
                // 隐藏工具提示
                this.hideTooltip = () => {
                    this.enhancedState.tooltipVisible = false;
                };
                
                // 记录标签交互
                this.recordLabelInteraction = (type) => {
                    // 记录交互统计
                    console.log(`Label interaction: ${type} on field ${this.props.fieldName}`);
                };
                
                // 应用动画
                this.applyAnimation = (animationType) => {
                    this.enhancedState.animationClass = `o_form_label_${animationType}`;
                    
                    // 动画结束后清除类
                    setTimeout(() => {
                        this.enhancedState.animationClass = '';
                    }, 300);
                };
                
                // 验证标签配置
                this.validateLabelConfig = () => {
                    const { fieldInfo, record, fieldName, string, id } = this.props;
                    
                    if (!fieldInfo) {
                        console.warn('FormLabel: fieldInfo is required');
                    }
                    
                    if (!record) {
                        console.warn('FormLabel: record is required');
                    }
                    
                    if (!fieldName) {
                        console.warn('FormLabel: fieldName is required');
                    }
                    
                    if (!string) {
                        console.warn('FormLabel: string is required');
                    }
                    
                    if (!id) {
                        console.warn('FormLabel: id is required');
                    }
                };
            }
            
            addEventListeners() {
                // 添加鼠标事件监听
                useExternalListener(this.rootRef, 'mouseenter', this.onMouseEnter);
                useExternalListener(this.rootRef, 'mouseleave', this.onMouseLeave);
                useExternalListener(this.rootRef, 'click', this.onLabelClick);
                
                // 添加键盘事件监听
                useExternalListener(this.rootRef, 'keydown', this.onKeyDown);
            }
            
            addAccessibilitySupport() {
                // 添加ARIA属性
                this.getAriaAttributes = () => {
                    const attrs = {
                        'aria-label': this.props.string,
                        'aria-describedby': this.hasTooltip ? `${this.props.id}_tooltip` : null,
                        'role': 'label'
                    };
                    
                    if (this.props.fieldInfo.field.required) {
                        attrs['aria-required'] = 'true';
                    }
                    
                    return attrs;
                };
                
                // 处理键盘导航
                this.onKeyDown = (event) => {
                    if (event.key === 'Enter' || event.key === ' ') {
                        event.preventDefault();
                        this.onLabelClick(event);
                    }
                };
            }
            
            addAnimationSupport() {
                // 监听字段状态变化
                useEffect(() => {
                    const { invalid, empty } = fieldVisualFeedback(
                        this.props.fieldInfo.field,
                        this.props.record,
                        this.props.fieldName,
                        this.props.fieldInfo
                    );
                    
                    // 应用状态变化动画
                    if (invalid) {
                        this.applyAnimation('invalid');
                    } else if (empty) {
                        this.applyAnimation('empty');
                    }
                });
            }
            
            // 重写getter以使用增强功能
            get className() {
                return this.getSmartClassName();
            }
            
            get tooltipInfo() {
                return this.getEnhancedTooltipInfo();
            }
            
            // 组件挂载时的处理
            onMounted() {
                // 验证配置
                this.validateLabelConfig();
                
                // 记录统计
                this.labelStatistics.totalLabels++;
                
                // 更新状态统计
                this.updateStateStatistics();
            }
            
            // 更新状态统计
            updateStateStatistics() {
                const { invalid, empty, readonly } = fieldVisualFeedback(
                    this.props.fieldInfo.field,
                    this.props.record,
                    this.props.fieldName,
                    this.props.fieldInfo
                );
                
                if (invalid) this.labelStatistics.invalidLabels++;
                if (empty) this.labelStatistics.emptyLabels++;
                if (readonly) this.labelStatistics.readonlyLabels++;
            }
            
            // 获取标签统计
            getLabelStatistics() {
                return {
                    ...this.labelStatistics,
                    fieldName: this.props.fieldName,
                    fieldType: this.props.fieldInfo.field.type,
                    isRequired: this.props.fieldInfo.field.required,
                    hasTooltip: this.hasTooltip
                };
            }
        };
    }
    
    // 设置样式系统
    setupStyleSystem() {
        // 定义样式映射
        this.styleMapping.set('invalid', 'o_field_invalid');
        this.styleMapping.set('empty', 'o_form_label_empty');
        this.styleMapping.set('readonly', 'o_form_label_readonly');
        this.styleMapping.set('required', 'o_form_label_required');
        this.styleMapping.set('hovered', 'o_form_label_hovered');
        this.styleMapping.set('focused', 'o_form_label_focused');
    }
    
    // 设置工具提示系统
    setupTooltipSystem() {
        this.tooltipConfig = {
            delay: this.labelConfig.tooltipDelay,
            position: 'top',
            animation: 'fade',
            maxWidth: 300
        };
    }
    
    // 设置可访问性支持
    setupAccessibilitySupport() {
        this.accessibilityConfig = {
            enableAriaLabels: true,
            enableKeyboardNavigation: true,
            enableScreenReaderSupport: true,
            enableHighContrast: true
        };
    }
    
    // 创建标签实例
    createLabel(props) {
        return new this.EnhancedFormLabel(props);
    }
    
    // 获取标签统计
    getLabelStatistics() {
        return {
            ...this.labelStatistics,
            styleMapSize: this.styleMapping.size,
            tooltipCacheSize: this.tooltipCache.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理缓存
        this.styleMapping.clear();
        this.tooltipCache.clear();
        
        // 重置统计
        this.labelStatistics = {
            totalLabels: 0,
            invalidLabels: 0,
            emptyLabels: 0,
            readonlyLabels: 0,
            tooltipShown: 0
        };
    }
}

// 使用示例
const labelManager = new FormLabelManager();

// 创建增强的表单标签
const EnhancedFormLabel = labelManager.EnhancedFormLabel;

// 获取统计信息
const stats = labelManager.getLabelStatistics();
console.log('Label statistics:', stats);
```

## 技术特点

### 1. 视觉反馈
- **状态感知**: 感知字段的各种状态
- **样式映射**: 将状态映射为CSS类
- **动态更新**: 动态更新标签样式
- **用户友好**: 提供清晰的视觉反馈

### 2. 工具提示
- **智能显示**: 智能决定是否显示工具提示
- **调试支持**: 在调试模式下显示详细信息
- **帮助文本**: 显示字段的帮助文本
- **上下文信息**: 提供丰富的上下文信息

### 3. 样式管理
- **状态样式**: 基于字段状态的样式
- **自定义样式**: 支持自定义CSS类
- **响应式**: 支持响应式样式
- **主题支持**: 支持不同的视觉主题

### 4. 可访问性
- **ARIA支持**: 完整的ARIA属性支持
- **键盘导航**: 支持键盘导航
- **屏幕阅读器**: 支持屏幕阅读器
- **高对比度**: 支持高对比度模式

## 设计模式

### 1. 组件模式 (Component Pattern)
- **封装性**: 封装标签显示逻辑
- **可复用**: 高度可复用的组件
- **接口标准**: 标准化的组件接口

### 2. 状态模式 (State Pattern)
- **状态感知**: 感知字段的不同状态
- **行为变化**: 根据状态改变显示
- **动态切换**: 动态切换样式和行为

### 3. 策略模式 (Strategy Pattern)
- **样式策略**: 不同状态的样式策略
- **显示策略**: 不同的显示策略
- **交互策略**: 不同的用户交互策略

### 4. 观察者模式 (Observer Pattern)
- **状态监听**: 监听字段状态变化
- **自动更新**: 自动更新标签显示
- **事件响应**: 响应用户交互事件

## 注意事项

1. **性能考虑**: 避免频繁的样式重新计算
2. **可访问性**: 确保标签的可访问性
3. **用户体验**: 提供清晰的视觉反馈
4. **浏览器兼容**: 确保跨浏览器兼容性

## 扩展建议

1. **动画效果**: 添加状态变化的动画效果
2. **主题支持**: 支持更多的视觉主题
3. **国际化**: 增强国际化支持
4. **自定义样式**: 支持更灵活的自定义样式
5. **交互增强**: 增强用户交互功能

该表单标签组件为Odoo Web客户端提供了专业的字段标签显示功能，通过视觉反馈、工具提示和可访问性支持确保了优秀的用户体验。

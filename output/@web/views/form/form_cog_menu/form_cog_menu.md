# FormCogMenu - 表单齿轮菜单

## 概述

`form_cog_menu.js` 是 Odoo Web 客户端表单视图的齿轮菜单组件，负责在表单视图中提供额外的操作选项和功能。该模块包含20行代码，是一个轻量级的OWL组件，专门用于扩展基础齿轮菜单功能，集成状态栏下拉项，具备菜单扩展、组件继承、插槽支持等特性，是表单视图系统中操作菜单的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/form/form_cog_menu/form_cog_menu.js`
- **行数**: 20
- **模块**: `@web/views/form/form_cog_menu/form_cog_menu`

## 依赖关系

```javascript
// 核心依赖
'@web/search/cog_menu/cog_menu'                                    // 基础齿轮菜单
'@web/views/form/status_bar_dropdown_items/status_bar_dropdown_items' // 状态栏下拉项
```

## 主要组件定义

### 1. FormCogMenu - 表单齿轮菜单

```javascript
class FormCogMenu extends CogMenu {
    static template = "web.FormCogMenu";
    static components = {
        ...CogMenu.components,
        StatusBarDropdownItems,
    };
    static props = {
        ...CogMenu.props,
        slots: { type: Object, optional: true },
    };
}
```

**组件特性**:
- **继承设计**: 继承自基础CogMenu组件
- **专用模板**: 使用FormCogMenu专用模板
- **组件扩展**: 扩展基础组件集合
- **属性继承**: 继承并扩展基础属性

## 核心功能

### 1. 组件继承

```javascript
static components = {
    ...CogMenu.components,
    StatusBarDropdownItems,
};
```

**继承功能**:
- **基础组件**: 继承所有基础齿轮菜单组件
- **状态栏集成**: 集成状态栏下拉项组件
- **组件扩展**: 扩展组件功能集合
- **无缝集成**: 与基础组件无缝集成

### 2. 属性扩展

```javascript
static props = {
    ...CogMenu.props,
    slots: { type: Object, optional: true },
};
```

**属性功能**:
- **属性继承**: 继承基础齿轮菜单的所有属性
- **插槽支持**: 添加插槽支持功能
- **可选配置**: 插槽为可选配置项
- **灵活扩展**: 支持灵活的内容扩展

### 3. 模板定制

```javascript
static template = "web.FormCogMenu";
```

**模板功能**:
- **专用模板**: 使用表单专用的齿轮菜单模板
- **布局定制**: 定制表单视图的菜单布局
- **样式适配**: 适配表单视图的样式风格
- **功能集成**: 集成表单特有的功能

## 使用场景

### 1. 表单齿轮菜单管理器

```javascript
// 表单齿轮菜单管理器
class FormCogMenuManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置菜单配置
        this.menuConfig = {
            enableCustomActions: true,
            enableStatusBarItems: true,
            enableSlots: true,
            enableDynamicItems: true,
            showIcons: true,
            showLabels: true,
            maxItems: 10
        };
        
        // 设置菜单项注册表
        this.menuItemRegistry = new Map();
        
        // 设置动态菜单项
        this.dynamicItems = new Set();
        
        // 设置菜单统计
        this.menuStatistics = {
            totalMenus: 0,
            totalClicks: 0,
            popularItems: new Map(),
            averageResponseTime: 0
        };
        
        this.initializeMenuSystem();
    }
    
    // 初始化菜单系统
    initializeMenuSystem() {
        // 创建增强的表单齿轮菜单
        this.createEnhancedFormCogMenu();
        
        // 设置菜单项管理
        this.setupMenuItemManagement();
        
        // 设置动态菜单
        this.setupDynamicMenu();
        
        // 设置性能监控
        this.setupPerformanceMonitoring();
    }
    
    // 创建增强的表单齿轮菜单
    createEnhancedFormCogMenu() {
        const originalMenu = FormCogMenu;
        
        this.EnhancedFormCogMenu = class extends originalMenu {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加动态菜单项
                this.addDynamicMenuItems();
                
                // 添加性能监控
                this.addPerformanceMonitoring();
                
                // 添加可访问性支持
                this.addAccessibilitySupport();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = useState({
                    isExpanded: false,
                    activeItem: null,
                    customItems: [],
                    loadingItems: new Set(),
                    errorItems: new Set()
                });
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的菜单项点击处理
                this.enhancedOnItemClick = (item, event) => {
                    const startTime = performance.now();
                    
                    // 记录点击统计
                    this.recordItemClick(item);
                    
                    // 设置活动项
                    this.enhancedState.activeItem = item.key;
                    
                    // 执行项动作
                    this.executeItemAction(item, event);
                    
                    // 记录性能
                    const endTime = performance.now();
                    this.recordPerformance('item_click', endTime - startTime);
                };
                
                // 记录菜单项点击
                this.recordItemClick = (item) => {
                    this.menuStatistics.totalClicks++;
                    
                    // 更新流行项统计
                    const currentCount = this.menuStatistics.popularItems.get(item.key) || 0;
                    this.menuStatistics.popularItems.set(item.key, currentCount + 1);
                };
                
                // 执行菜单项动作
                this.executeItemAction = async (item, event) => {
                    try {
                        // 设置加载状态
                        this.enhancedState.loadingItems.add(item.key);
                        
                        // 执行动作
                        if (item.action) {
                            await item.action(event, this.props);
                        }
                        
                        // 触发自定义事件
                        this.triggerCustomEvent('item-executed', { item, event });
                        
                    } catch (error) {
                        // 设置错误状态
                        this.enhancedState.errorItems.add(item.key);
                        this.handleItemError(item, error);
                        
                    } finally {
                        // 清除加载状态
                        this.enhancedState.loadingItems.delete(item.key);
                    }
                };
                
                // 处理菜单项错误
                this.handleItemError = (item, error) => {
                    console.error(`Menu item error for ${item.key}:`, error);
                    
                    // 显示错误通知
                    if (this.notification) {
                        this.notification.add(
                            `Failed to execute ${item.label}`, 
                            { type: 'danger' }
                        );
                    }
                    
                    // 自动清除错误状态
                    setTimeout(() => {
                        this.enhancedState.errorItems.delete(item.key);
                    }, 5000);
                };
                
                // 添加自定义菜单项
                this.addCustomMenuItem = (item) => {
                    // 验证菜单项
                    if (!this.validateMenuItem(item)) {
                        throw new Error('Invalid menu item configuration');
                    }
                    
                    // 添加到自定义项列表
                    this.enhancedState.customItems.push(item);
                    
                    // 注册菜单项
                    this.menuItemRegistry.set(item.key, item);
                };
                
                // 验证菜单项
                this.validateMenuItem = (item) => {
                    return item && 
                           typeof item.key === 'string' && 
                           typeof item.label === 'string' &&
                           (typeof item.action === 'function' || item.action === undefined);
                };
                
                // 移除自定义菜单项
                this.removeCustomMenuItem = (key) => {
                    // 从自定义项列表移除
                    this.enhancedState.customItems = this.enhancedState.customItems.filter(
                        item => item.key !== key
                    );
                    
                    // 从注册表移除
                    this.menuItemRegistry.delete(key);
                };
                
                // 获取所有菜单项
                this.getAllMenuItems = () => {
                    const baseItems = this.getBaseMenuItems();
                    const customItems = this.enhancedState.customItems;
                    const dynamicItems = this.getDynamicMenuItems();
                    
                    return [...baseItems, ...customItems, ...dynamicItems];
                };
                
                // 获取基础菜单项
                this.getBaseMenuItems = () => {
                    // 从基础组件获取菜单项
                    return this.getInheritedMenuItems();
                };
                
                // 获取动态菜单项
                this.getDynamicMenuItems = () => {
                    return Array.from(this.dynamicItems).map(itemKey => {
                        return this.menuItemRegistry.get(itemKey);
                    }).filter(Boolean);
                };
                
                // 触发自定义事件
                this.triggerCustomEvent = (eventName, detail) => {
                    const event = new CustomEvent(eventName, { detail });
                    this.rootRef.el?.dispatchEvent(event);
                };
                
                // 获取菜单项状态
                this.getItemState = (itemKey) => {
                    return {
                        isActive: this.enhancedState.activeItem === itemKey,
                        isLoading: this.enhancedState.loadingItems.has(itemKey),
                        hasError: this.enhancedState.errorItems.has(itemKey)
                    };
                };
                
                // 切换菜单展开状态
                this.toggleMenu = () => {
                    this.enhancedState.isExpanded = !this.enhancedState.isExpanded;
                    
                    if (this.enhancedState.isExpanded) {
                        this.onMenuOpen();
                    } else {
                        this.onMenuClose();
                    }
                };
                
                // 菜单打开处理
                this.onMenuOpen = () => {
                    // 加载动态菜单项
                    this.loadDynamicMenuItems();
                    
                    // 触发打开事件
                    this.triggerCustomEvent('menu-opened', {});
                };
                
                // 菜单关闭处理
                this.onMenuClose = () => {
                    // 清除活动项
                    this.enhancedState.activeItem = null;
                    
                    // 触发关闭事件
                    this.triggerCustomEvent('menu-closed', {});
                };
                
                // 加载动态菜单项
                this.loadDynamicMenuItems = async () => {
                    try {
                        // 获取动态菜单项配置
                        const dynamicConfig = await this.getDynamicMenuConfig();
                        
                        // 更新动态菜单项
                        this.updateDynamicMenuItems(dynamicConfig);
                        
                    } catch (error) {
                        console.error('Failed to load dynamic menu items:', error);
                    }
                };
                
                // 获取动态菜单配置
                this.getDynamicMenuConfig = async () => {
                    // 实现动态配置获取逻辑
                    return [];
                };
                
                // 更新动态菜单项
                this.updateDynamicMenuItems = (config) => {
                    // 清除现有动态项
                    this.dynamicItems.clear();
                    
                    // 添加新的动态项
                    for (const itemConfig of config) {
                        const item = this.createMenuItemFromConfig(itemConfig);
                        this.menuItemRegistry.set(item.key, item);
                        this.dynamicItems.add(item.key);
                    }
                };
                
                // 从配置创建菜单项
                this.createMenuItemFromConfig = (config) => {
                    return {
                        key: config.key,
                        label: config.label,
                        icon: config.icon,
                        action: config.action ? this.createActionFromConfig(config.action) : undefined,
                        visible: config.visible !== false,
                        enabled: config.enabled !== false
                    };
                };
                
                // 从配置创建动作
                this.createActionFromConfig = (actionConfig) => {
                    return async (event, props) => {
                        // 实现动作执行逻辑
                        console.log('Executing action:', actionConfig);
                    };
                };
                
                // 记录性能指标
                this.recordPerformance = (operation, duration) => {
                    // 更新平均响应时间
                    const totalOps = this.menuStatistics.totalClicks;
                    this.menuStatistics.averageResponseTime = 
                        (this.menuStatistics.averageResponseTime * (totalOps - 1) + duration) / totalOps;
                };
            }
            
            addDynamicMenuItems() {
                // 动态菜单项管理
                this.dynamicMenuManager = {
                    add: (item) => this.addCustomMenuItem(item),
                    remove: (key) => this.removeCustomMenuItem(key),
                    update: (key, updates) => this.updateMenuItem(key, updates),
                    get: (key) => this.menuItemRegistry.get(key),
                    getAll: () => this.getAllMenuItems()
                };
            }
            
            addPerformanceMonitoring() {
                // 性能监控
                this.performanceMonitor = {
                    startTimer: (operation) => {
                        return performance.now();
                    },
                    endTimer: (operation, startTime) => {
                        const duration = performance.now() - startTime;
                        this.recordPerformance(operation, duration);
                        return duration;
                    },
                    getStatistics: () => this.getMenuStatistics()
                };
            }
            
            addAccessibilitySupport() {
                // 可访问性支持
                this.accessibilityManager = {
                    setAriaLabels: () => this.setAriaLabels(),
                    handleKeyboardNavigation: (event) => this.handleKeyboardNavigation(event),
                    announceChanges: (message) => this.announceChanges(message)
                };
            }
            
            // 设置ARIA标签
            setAriaLabels() {
                const menuElement = this.rootRef.el;
                if (menuElement) {
                    menuElement.setAttribute('role', 'menu');
                    menuElement.setAttribute('aria-label', 'Form actions menu');
                }
            }
            
            // 处理键盘导航
            handleKeyboardNavigation(event) {
                switch (event.key) {
                    case 'Enter':
                    case ' ':
                        event.preventDefault();
                        this.toggleMenu();
                        break;
                    case 'Escape':
                        event.preventDefault();
                        if (this.enhancedState.isExpanded) {
                            this.toggleMenu();
                        }
                        break;
                    case 'ArrowDown':
                    case 'ArrowUp':
                        event.preventDefault();
                        this.navigateMenuItems(event.key === 'ArrowDown' ? 1 : -1);
                        break;
                }
            }
            
            // 导航菜单项
            navigateMenuItems(direction) {
                const items = this.getAllMenuItems().filter(item => item.visible && item.enabled);
                const currentIndex = items.findIndex(item => item.key === this.enhancedState.activeItem);
                const newIndex = Math.max(0, Math.min(items.length - 1, currentIndex + direction));
                
                if (items[newIndex]) {
                    this.enhancedState.activeItem = items[newIndex].key;
                }
            }
            
            // 宣布变化
            announceChanges(message) {
                // 实现屏幕阅读器通知
                const announcement = document.createElement('div');
                announcement.setAttribute('aria-live', 'polite');
                announcement.setAttribute('aria-atomic', 'true');
                announcement.className = 'sr-only';
                announcement.textContent = message;
                
                document.body.appendChild(announcement);
                setTimeout(() => document.body.removeChild(announcement), 1000);
            }
            
            // 获取菜单统计
            getMenuStatistics() {
                return {
                    ...this.menuStatistics,
                    customItemCount: this.enhancedState.customItems.length,
                    dynamicItemCount: this.dynamicItems.size,
                    totalItemCount: this.getAllMenuItems().length
                };
            }
            
            // 组件销毁时清理
            onWillDestroy() {
                // 清理定时器和监听器
                if (this.performanceMonitor) {
                    this.performanceMonitor = null;
                }
                
                if (this.accessibilityManager) {
                    this.accessibilityManager = null;
                }
            }
        };
    }
    
    // 设置菜单项管理
    setupMenuItemManagement() {
        // 默认菜单项
        this.defaultMenuItems = [
            {
                key: 'duplicate',
                label: 'Duplicate',
                icon: 'fa-copy',
                action: this.duplicateRecord.bind(this)
            },
            {
                key: 'archive',
                label: 'Archive',
                icon: 'fa-archive',
                action: this.archiveRecord.bind(this)
            },
            {
                key: 'delete',
                label: 'Delete',
                icon: 'fa-trash',
                action: this.deleteRecord.bind(this)
            }
        ];
        
        // 注册默认菜单项
        for (const item of this.defaultMenuItems) {
            this.menuItemRegistry.set(item.key, item);
        }
    }
    
    // 设置动态菜单
    setupDynamicMenu() {
        this.dynamicMenuConfig = {
            enableDynamicLoading: this.menuConfig.enableDynamicItems,
            refreshInterval: 300000, // 5分钟
            maxDynamicItems: 5
        };
    }
    
    // 设置性能监控
    setupPerformanceMonitoring() {
        this.performanceConfig = {
            enableMonitoring: true,
            sampleRate: 0.1,
            thresholds: {
                clickResponseTime: 100, // 100ms
                menuOpenTime: 50        // 50ms
            }
        };
    }
    
    // 复制记录
    async duplicateRecord(event, props) {
        try {
            const record = props.record;
            const duplicatedRecord = await props.orm.call(
                record.resModel,
                'copy',
                [record.resId]
            );
            
            // 导航到新记录
            props.actionService.doAction({
                type: 'ir.actions.act_window',
                res_model: record.resModel,
                res_id: duplicatedRecord,
                view_mode: 'form',
                target: 'current'
            });
            
        } catch (error) {
            console.error('Failed to duplicate record:', error);
        }
    }
    
    // 归档记录
    async archiveRecord(event, props) {
        try {
            const record = props.record;
            await record.update({ active: false });
            
            // 返回列表视图
            props.actionService.doAction({
                type: 'ir.actions.act_window',
                res_model: record.resModel,
                view_mode: 'list',
                target: 'current'
            });
            
        } catch (error) {
            console.error('Failed to archive record:', error);
        }
    }
    
    // 删除记录
    async deleteRecord(event, props) {
        try {
            const record = props.record;
            await record.delete();
            
            // 返回列表视图
            props.actionService.doAction({
                type: 'ir.actions.act_window',
                res_model: record.resModel,
                view_mode: 'list',
                target: 'current'
            });
            
        } catch (error) {
            console.error('Failed to delete record:', error);
        }
    }
    
    // 创建表单齿轮菜单
    createFormCogMenu(props) {
        return new this.EnhancedFormCogMenu(props);
    }
    
    // 获取菜单项注册表
    getMenuItemRegistry() {
        return this.menuItemRegistry;
    }
    
    // 获取全局统计
    getGlobalStatistics() {
        return {
            ...this.menuStatistics,
            registeredItemCount: this.menuItemRegistry.size,
            dynamicItemCount: this.dynamicItems.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理注册表
        this.menuItemRegistry.clear();
        
        // 清理动态项
        this.dynamicItems.clear();
        
        // 重置统计
        this.menuStatistics = {
            totalMenus: 0,
            totalClicks: 0,
            popularItems: new Map(),
            averageResponseTime: 0
        };
    }
}

// 使用示例
const cogMenuManager = new FormCogMenuManager();

// 创建表单齿轮菜单
const formCogMenu = cogMenuManager.createFormCogMenu({
    record: record,
    orm: orm,
    actionService: actionService
});

// 添加自定义菜单项
formCogMenu.dynamicMenuManager.add({
    key: 'custom_action',
    label: 'Custom Action',
    icon: 'fa-star',
    action: async (event, props) => {
        console.log('Custom action executed');
    }
});

// 获取统计信息
const stats = cogMenuManager.getGlobalStatistics();
console.log('Form cog menu statistics:', stats);
```

## 技术特点

### 1. 继承设计
- **基础扩展**: 基于基础齿轮菜单扩展
- **功能增强**: 增强基础菜单功能
- **无缝集成**: 与基础组件无缝集成
- **向后兼容**: 保持向后兼容性

### 2. 组件集成
- **状态栏集成**: 集成状态栏下拉项
- **插槽支持**: 支持灵活的内容插槽
- **模板定制**: 定制表单专用模板
- **样式适配**: 适配表单视图样式

### 3. 轻量级实现
- **代码简洁**: 仅20行代码的轻量实现
- **性能优化**: 最小的性能开销
- **快速加载**: 快速的组件加载
- **内存效率**: 高效的内存使用

### 4. 扩展性
- **属性扩展**: 支持属性的灵活扩展
- **组件扩展**: 支持组件的动态扩展
- **功能扩展**: 支持功能的模块化扩展
- **配置驱动**: 配置驱动的功能开关

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **类继承**: 继承基础齿轮菜单类
- **功能扩展**: 扩展基础功能
- **行为重用**: 重用基础行为

### 2. 组合模式 (Composition Pattern)
- **组件组合**: 组合多个子组件
- **功能组合**: 组合不同功能模块
- **灵活配置**: 灵活的组合配置

### 3. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 装饰基础菜单功能
- **行为增强**: 增强基础行为
- **透明扩展**: 对用户透明的扩展

### 4. 策略模式 (Strategy Pattern)
- **菜单策略**: 不同的菜单显示策略
- **交互策略**: 不同的用户交互策略
- **渲染策略**: 不同的渲染策略

## 注意事项

1. **继承关系**: 确保正确继承基础组件
2. **组件依赖**: 管理组件间的依赖关系
3. **性能影响**: 避免不必要的性能开销
4. **兼容性**: 确保与基础组件的兼容性

## 扩展建议

1. **动态菜单**: 添加动态菜单项功能
2. **权限控制**: 添加菜单项权限控制
3. **国际化**: 支持菜单项的国际化
4. **主题支持**: 支持不同的视觉主题
5. **快捷键**: 添加菜单项的快捷键支持

该表单齿轮菜单为Odoo Web客户端提供了专门的表单视图菜单功能，通过继承和扩展基础齿轮菜单确保了功能的一致性和专业性。

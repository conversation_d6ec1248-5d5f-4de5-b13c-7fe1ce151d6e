# Odoo 视图编译器 (View Compiler) 学习资料

## 文件概述

**文件路径**: `output/@web/views/view_compiler.js`  
**原始路径**: `/web/static/src/views/view_compiler.js`  
**模块类型**: 核心视图模块 - 视图编译器  
**代码行数**: 482 行  
**依赖关系**: 
- `@web/core/utils/xml` - XML工具函数
- `@web/views/utils` - 视图工具函数
- `@odoo/owl` - OWL框架

## 模块功能

视图编译器模块是 Odoo Web 客户端的核心编译系统。该模块提供了：
- XML架构到OWL模板的编译
- 字符串插值和表达式处理
- 按钮和字段的编译转换
- 属性合并和处理
- 动态内容生成
- 模板优化和缓存

这个编译器将XML视图架构转换为可执行的OWL模板，是视图系统的核心转换引擎。

## 编译器架构

### 核心编译流程
```
View Compiler
├── 字符串插值处理
│   ├── toInterpolatedStringExpression
│   ├── 表达式解析
│   └── 字符串拼接
├── 编译器注册系统
│   ├── Compiler类型定义
│   ├── 选择器匹配
│   └── 编译函数执行
├── 按钮编译
│   ├── 按钮属性处理
│   ├── 点击事件绑定
│   └── 样式类生成
└── 字段编译
    ├── 字段类型识别
    ├── 属性转换
    └── 组件生成
```

### 类型定义
```javascript
/**
 * @typedef Compiler
 * @property {string} selector - CSS选择器
 * @property {(el: Element, params: Record<string, any>) => Element} fn - 编译函数
 * @property {string} [class] - 可选的CSS类名
 * @property {boolean} [doNotCopyAttributes] - 是否不复制属性
 */
```

## 核心函数详解

### 1. toInterpolatedStringExpression() - 字符串插值处理
```javascript
function toInterpolatedStringExpression(str) {
    const matches = str.matchAll(INTERP_REGEXP);
    const parts = [];
    let searchString = str;
    
    for (const [match, head, expr] of matches) {
        const index = searchString.indexOf(head);
        const left = searchString.slice(0, index);
        
        if (left) {
            parts.push(toStringExpression(left));
        }
        
        parts.push(`(${expr})`);
        searchString = searchString.slice(index + match.length);
    }
    
    parts.push(toStringExpression(searchString));
    return parts.join("+");
}
```

**功能特性**:
- **插值解析**: 解析 `{{expression}}` 和 `#{expression}` 格式的插值
- **表达式提取**: 提取插值中的JavaScript表达式
- **字符串拼接**: 将静态字符串和动态表达式组合
- **安全处理**: 确保字符串的正确转义和拼接
- **性能优化**: 高效的字符串处理算法

**使用示例**:
```javascript
// 基本插值处理
class InterpolationProcessor {
    constructor() {
        this.interpolationRegex = /(\{\{|#\{)(.*?)(\}{1,2})/g;
    }
    
    processTemplate(template) {
        // 处理简单插值
        const simple = "Hello {{name}}, welcome to {{company}}!";
        const simpleResult = toInterpolatedStringExpression(simple);
        console.log(simpleResult); 
        // 输出: `"Hello "+(name)+", welcome to "+(company)+"!"`
        
        // 处理复杂插值
        const complex = "User: {{user.name}} ({{user.role}}) - Status: #{getStatus(user.id)}";
        const complexResult = toInterpolatedStringExpression(complex);
        console.log(complexResult);
        // 输出: `"User: "+(user.name)+" ("+(user.role)+") - Status: "+(getStatus(user.id))+""`
        
        return { simple: simpleResult, complex: complexResult };
    }
    
    // 高级插值处理器
    advancedInterpolation(template, context = {}) {
        const processed = toInterpolatedStringExpression(template);
        
        // 创建安全的执行环境
        const safeContext = this.createSafeContext(context);
        
        try {
            // 在安全环境中执行表达式
            const result = new Function('context', `
                with(context) {
                    return ${processed};
                }
            `)(safeContext);
            
            return result;
        } catch (error) {
            console.error('插值执行失败:', error);
            return template; // 返回原始模板
        }
    }
    
    createSafeContext(context) {
        // 创建安全的执行上下文
        const safeContext = Object.create(null);
        
        // 复制安全的属性
        Object.keys(context).forEach(key => {
            if (this.isSafeProperty(key, context[key])) {
                safeContext[key] = context[key];
            }
        });
        
        // 添加安全的内置函数
        safeContext.Math = Math;
        safeContext.Date = Date;
        safeContext.JSON = JSON;
        
        return safeContext;
    }
    
    isSafeProperty(key, value) {
        // 检查属性是否安全
        if (typeof value === 'function' && key.startsWith('_')) {
            return false; // 私有函数不安全
        }
        
        if (key === 'constructor' || key === 'prototype') {
            return false; // 构造函数和原型不安全
        }
        
        return true;
    }
    
    // 批量处理模板
    batchProcess(templates, context) {
        const results = {};
        
        templates.forEach((template, key) => {
            try {
                results[key] = this.advancedInterpolation(template, context);
            } catch (error) {
                console.error(`处理模板 ${key} 失败:`, error);
                results[key] = template;
            }
        });
        
        return results;
    }
    
    // 模板验证
    validateTemplate(template) {
        const errors = [];
        const warnings = [];
        
        // 检查插值语法
        const matches = template.matchAll(this.interpolationRegex);
        
        for (const [match, head, expr] of matches) {
            // 检查表达式语法
            try {
                new Function(`return ${expr}`);
            } catch (error) {
                errors.push({
                    type: 'syntax',
                    expression: expr,
                    error: error.message
                });
            }
            
            // 检查潜在的安全问题
            if (expr.includes('eval') || expr.includes('Function')) {
                warnings.push({
                    type: 'security',
                    expression: expr,
                    message: '表达式包含潜在的安全风险'
                });
            }
        }
        
        return { errors, warnings, isValid: errors.length === 0 };
    }
}

// 在视图编译中使用
class ViewTemplateCompiler {
    constructor() {
        this.interpolationProcessor = new InterpolationProcessor();
        this.compiledTemplates = new Map();
    }
    
    compileViewTemplate(arch, context) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(arch, 'text/xml');
        
        // 处理所有文本节点中的插值
        this.processTextNodes(doc, context);
        
        // 处理属性中的插值
        this.processAttributes(doc, context);
        
        return doc;
    }
    
    processTextNodes(element, context) {
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );
        
        const textNodes = [];
        let node;
        
        while (node = walker.nextNode()) {
            textNodes.push(node);
        }
        
        textNodes.forEach(textNode => {
            const originalText = textNode.textContent;
            
            if (this.hasInterpolation(originalText)) {
                const processed = this.interpolationProcessor.advancedInterpolation(
                    originalText, 
                    context
                );
                textNode.textContent = processed;
            }
        });
    }
    
    processAttributes(element, context) {
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_ELEMENT,
            null,
            false
        );
        
        let node;
        while (node = walker.nextNode()) {
            Array.from(node.attributes).forEach(attr => {
                if (this.hasInterpolation(attr.value)) {
                    const processed = this.interpolationProcessor.advancedInterpolation(
                        attr.value,
                        context
                    );
                    attr.value = processed;
                }
            });
        }
    }
    
    hasInterpolation(text) {
        return /(\{\{|#\{).*?(\}{1,2})/.test(text);
    }
}
```

## 高级编译功能

### 1. 模板优化器
```javascript
class TemplateOptimizer {
    constructor() {
        this.optimizations = new Map();
        this.setupOptimizations();
    }

    setupOptimizations() {
        // 静态内容优化
        this.optimizations.set('static-content', this.optimizeStaticContent.bind(this));

        // 重复表达式优化
        this.optimizations.set('duplicate-expressions', this.optimizeDuplicateExpressions.bind(this));

        // 条件渲染优化
        this.optimizations.set('conditional-rendering', this.optimizeConditionalRendering.bind(this));

        // 循环优化
        this.optimizations.set('loops', this.optimizeLoops.bind(this));
    }

    optimize(template) {
        let optimizedTemplate = template;

        // 应用所有优化
        for (const [name, optimizer] of this.optimizations) {
            try {
                optimizedTemplate = optimizer(optimizedTemplate);
                console.log(`应用优化: ${name}`);
            } catch (error) {
                console.warn(`优化 ${name} 失败:`, error);
            }
        }

        return optimizedTemplate;
    }

    optimizeStaticContent(template) {
        // 将静态内容预编译
        const parser = new DOMParser();
        const doc = parser.parseFromString(template, 'text/xml');

        this.processStaticElements(doc.documentElement);

        return new XMLSerializer().serializeToString(doc);
    }

    processStaticElements(element) {
        Array.from(element.children).forEach(child => {
            if (this.isStaticElement(child)) {
                // 标记为静态内容
                child.setAttribute('t-static', 'true');
            } else {
                this.processStaticElements(child);
            }
        });
    }

    isStaticElement(element) {
        // 检查元素是否为静态内容
        const hasInterpolation = Array.from(element.attributes).some(attr =>
            this.hasInterpolation(attr.value)
        );

        const hasDirectives = Array.from(element.attributes).some(attr =>
            attr.name.startsWith('t-')
        );

        return !hasInterpolation && !hasDirectives;
    }

    optimizeDuplicateExpressions(template) {
        // 提取重复的表达式
        const expressions = new Map();
        const expressionRegex = /\{\{(.*?)\}\}/g;

        let match;
        while ((match = expressionRegex.exec(template)) !== null) {
            const expr = match[1].trim();
            if (expressions.has(expr)) {
                expressions.set(expr, expressions.get(expr) + 1);
            } else {
                expressions.set(expr, 1);
            }
        }

        // 为重复表达式创建变量
        const duplicates = Array.from(expressions.entries())
            .filter(([expr, count]) => count > 1)
            .map(([expr]) => expr);

        if (duplicates.length > 0) {
            const variables = duplicates.map((expr, index) =>
                `const _expr${index} = ${expr};`
            ).join('\n');

            // 替换重复表达式
            let optimized = template;
            duplicates.forEach((expr, index) => {
                const regex = new RegExp(`\\{\\{\\s*${expr.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*\\}\\}`, 'g');
                optimized = optimized.replace(regex, `{{_expr${index}}}`);
            });

            return `<t t-set-variables="${variables}">${optimized}</t>`;
        }

        return template;
    }

    optimizeConditionalRendering(template) {
        // 优化条件渲染
        const parser = new DOMParser();
        const doc = parser.parseFromString(template, 'text/xml');

        this.processConditionalElements(doc.documentElement);

        return new XMLSerializer().serializeToString(doc);
    }

    processConditionalElements(element) {
        Array.from(element.children).forEach(child => {
            const tIf = child.getAttribute('t-if');
            const tElif = child.getAttribute('t-elif');
            const tElse = child.getAttribute('t-else');

            if (tIf || tElif || tElse) {
                this.optimizeConditionalChain(child);
            }

            this.processConditionalElements(child);
        });
    }

    optimizeConditionalChain(element) {
        // 优化条件链
        const siblings = Array.from(element.parentNode.children);
        const index = siblings.indexOf(element);

        // 查找条件链
        const chain = [element];
        for (let i = index + 1; i < siblings.length; i++) {
            const sibling = siblings[i];
            if (sibling.getAttribute('t-elif') || sibling.getAttribute('t-else')) {
                chain.push(sibling);
            } else {
                break;
            }
        }

        if (chain.length > 1) {
            // 创建优化的条件结构
            const wrapper = createElement('t');
            wrapper.setAttribute('t-conditional-chain', 'true');

            chain.forEach(el => {
                wrapper.appendChild(el.cloneNode(true));
                el.remove();
            });

            element.parentNode.insertBefore(wrapper, siblings[index + chain.length]);
        }
    }

    optimizeLoops(template) {
        // 优化循环渲染
        const parser = new DOMParser();
        const doc = parser.parseFromString(template, 'text/xml');

        this.processLoopElements(doc.documentElement);

        return new XMLSerializer().serializeToString(doc);
    }

    processLoopElements(element) {
        Array.from(element.children).forEach(child => {
            const tForeach = child.getAttribute('t-foreach');

            if (tForeach) {
                this.optimizeLoop(child);
            }

            this.processLoopElements(child);
        });
    }

    optimizeLoop(element) {
        // 检查循环是否可以优化
        const tForeach = element.getAttribute('t-foreach');
        const tAs = element.getAttribute('t-as');
        const tKey = element.getAttribute('t-key');

        // 添加虚拟滚动支持
        if (this.shouldUseVirtualScrolling(element)) {
            element.setAttribute('t-virtual-scroll', 'true');
            element.setAttribute('t-item-height', this.estimateItemHeight(element));
        }

        // 添加键优化
        if (!tKey) {
            element.setAttribute('t-key', `${tAs}_index`);
        }
    }

    shouldUseVirtualScrolling(element) {
        // 判断是否应该使用虚拟滚动
        const estimatedItems = this.estimateItemCount(element);
        return estimatedItems > 100;
    }

    estimateItemCount(element) {
        // 估算项目数量
        return 1000; // 简化实现
    }

    estimateItemHeight(element) {
        // 估算项目高度
        return 50; // 简化实现
    }

    hasInterpolation(text) {
        return /(\{\{|#\{).*?(\}{1,2})/.test(text);
    }
}
```

### 2. 编译缓存系统
```javascript
class CompilationCache {
    constructor() {
        this.cache = new Map();
        this.stats = {
            hits: 0,
            misses: 0,
            compilations: 0
        };
        this.maxSize = 1000;
        this.setupCleanup();
    }

    setupCleanup() {
        // 定期清理缓存
        setInterval(() => {
            this.cleanup();
        }, 5 * 60 * 1000); // 每5分钟清理一次
    }

    get(key) {
        if (this.cache.has(key)) {
            this.stats.hits++;
            const entry = this.cache.get(key);
            entry.lastAccessed = Date.now();
            return entry.compiled;
        }

        this.stats.misses++;
        return null;
    }

    set(key, compiled) {
        if (this.cache.size >= this.maxSize) {
            this.evictLeastRecentlyUsed();
        }

        this.cache.set(key, {
            compiled: compiled,
            created: Date.now(),
            lastAccessed: Date.now(),
            accessCount: 1
        });

        this.stats.compilations++;
    }

    generateKey(arch, params = {}) {
        // 生成缓存键
        const archHash = this.hashString(arch);
        const paramsHash = this.hashString(JSON.stringify(params));
        return `${archHash}_${paramsHash}`;
    }

    hashString(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return hash.toString(36);
    }

    evictLeastRecentlyUsed() {
        let oldestKey = null;
        let oldestTime = Date.now();

        for (const [key, entry] of this.cache) {
            if (entry.lastAccessed < oldestTime) {
                oldestTime = entry.lastAccessed;
                oldestKey = key;
            }
        }

        if (oldestKey) {
            this.cache.delete(oldestKey);
        }
    }

    cleanup() {
        const now = Date.now();
        const maxAge = 30 * 60 * 1000; // 30分钟

        const expiredKeys = [];
        for (const [key, entry] of this.cache) {
            if (now - entry.lastAccessed > maxAge) {
                expiredKeys.push(key);
            }
        }

        expiredKeys.forEach(key => this.cache.delete(key));

        console.log(`清理了 ${expiredKeys.length} 个过期缓存项`);
    }

    getStats() {
        const hitRate = this.stats.hits / (this.stats.hits + this.stats.misses) * 100;

        return {
            ...this.stats,
            hitRate: hitRate.toFixed(2) + '%',
            cacheSize: this.cache.size,
            maxSize: this.maxSize
        };
    }

    clear() {
        this.cache.clear();
        this.stats = { hits: 0, misses: 0, compilations: 0 };
    }
}
```

### 3. 完整的视图编译器
```javascript
class AdvancedViewCompiler {
    constructor() {
        this.compilerRegistry = new CompilerRegistry();
        this.templateOptimizer = new TemplateOptimizer();
        this.compilationCache = new CompilationCache();
        this.interpolationProcessor = new InterpolationProcessor();
    }

    compile(arch, params = {}) {
        // 生成缓存键
        const cacheKey = this.compilationCache.generateKey(arch, params);

        // 检查缓存
        const cached = this.compilationCache.get(cacheKey);
        if (cached) {
            return cached;
        }

        try {
            // 执行编译
            const compiled = this.performCompilation(arch, params);

            // 缓存结果
            this.compilationCache.set(cacheKey, compiled);

            return compiled;

        } catch (error) {
            console.error('视图编译失败:', error);
            throw new Error(`视图编译失败: ${error.message}`);
        }
    }

    performCompilation(arch, params) {
        // 1. 解析XML架构
        const parser = new DOMParser();
        const doc = parser.parseFromString(arch, 'text/xml');

        if (doc.querySelector('parsererror')) {
            throw new Error('XML架构格式错误');
        }

        // 2. 预处理架构
        const preprocessed = this.preprocessArch(doc.documentElement, params);

        // 3. 编译元素
        const compiled = this.compilerRegistry.compile(preprocessed, params);

        // 4. 优化模板
        const optimized = this.templateOptimizer.optimize(
            new XMLSerializer().serializeToString(compiled)
        );

        // 5. 后处理
        const postProcessed = this.postProcess(optimized, params);

        return postProcessed;
    }

    preprocessArch(element, params) {
        // 预处理架构
        this.processIncludes(element, params);
        this.processConditionals(element, params);
        this.processVariables(element, params);

        return element;
    }

    processIncludes(element, params) {
        // 处理包含指令
        const includes = element.querySelectorAll('[t-include]');

        includes.forEach(include => {
            const templateName = include.getAttribute('t-include');
            const includedTemplate = this.loadTemplate(templateName);

            if (includedTemplate) {
                const parser = new DOMParser();
                const includedDoc = parser.parseFromString(includedTemplate, 'text/xml');

                // 替换包含元素
                Array.from(includedDoc.documentElement.children).forEach(child => {
                    include.parentNode.insertBefore(child.cloneNode(true), include);
                });

                include.remove();
            }
        });
    }

    processConditionals(element, params) {
        // 处理条件指令
        const conditionals = element.querySelectorAll('[t-if], [t-elif], [t-else]');

        conditionals.forEach(conditional => {
            const condition = conditional.getAttribute('t-if') ||
                            conditional.getAttribute('t-elif');

            if (condition && this.canEvaluateStatically(condition, params)) {
                const result = this.evaluateCondition(condition, params);

                if (!result) {
                    conditional.remove();
                } else {
                    conditional.removeAttribute('t-if');
                    conditional.removeAttribute('t-elif');
                }
            }
        });
    }

    processVariables(element, params) {
        // 处理变量定义
        const variables = element.querySelectorAll('[t-set]');

        variables.forEach(variable => {
            const name = variable.getAttribute('t-set');
            const value = variable.getAttribute('t-value') || variable.textContent;

            if (name && value) {
                params.variables = params.variables || {};
                params.variables[name] = value;
            }
        });
    }

    postProcess(template, params) {
        // 后处理编译结果
        let processed = template;

        // 添加根元素包装
        if (!processed.startsWith('<')) {
            processed = `<div>${processed}</div>`;
        }

        // 添加调试信息
        if (params.debug) {
            processed = this.addDebugInfo(processed, params);
        }

        // 验证结果
        this.validateCompiledTemplate(processed);

        return processed;
    }

    addDebugInfo(template, params) {
        const debugInfo = {
            compiledAt: new Date().toISOString(),
            params: params,
            cacheStats: this.compilationCache.getStats()
        };

        return `<!-- Debug Info: ${JSON.stringify(debugInfo)} -->\n${template}`;
    }

    validateCompiledTemplate(template) {
        // 验证编译后的模板
        const parser = new DOMParser();
        const doc = parser.parseFromString(template, 'text/xml');

        if (doc.querySelector('parsererror')) {
            throw new Error('编译后的模板格式错误');
        }

        // 检查必需的属性
        this.validateRequiredAttributes(doc);

        // 检查循环引用
        this.validateCircularReferences(doc);
    }

    validateRequiredAttributes(doc) {
        // 验证必需属性
        const fields = doc.querySelectorAll('Field');

        fields.forEach(field => {
            if (!field.getAttribute('name')) {
                throw new Error('Field组件缺少name属性');
            }
        });
    }

    validateCircularReferences(doc) {
        // 检查循环引用
        const visited = new Set();

        const checkElement = (element, path = []) => {
            const id = element.getAttribute('id') || element.tagName;

            if (path.includes(id)) {
                throw new Error(`检测到循环引用: ${path.join(' -> ')} -> ${id}`);
            }

            const newPath = [...path, id];

            Array.from(element.children).forEach(child => {
                checkElement(child, newPath);
            });
        };

        checkElement(doc.documentElement);
    }

    loadTemplate(templateName) {
        // 加载模板（简化实现）
        const templates = {
            'common.header': '<header><h1>{{title}}</h1></header>',
            'common.footer': '<footer><p>{{copyright}}</p></footer>'
        };

        return templates[templateName] || null;
    }

    canEvaluateStatically(condition, params) {
        // 检查条件是否可以静态评估
        return !condition.includes('record.') && !condition.includes('env.');
    }

    evaluateCondition(condition, params) {
        // 静态评估条件
        try {
            return new Function('params', `return ${condition}`)(params);
        } catch (error) {
            return false;
        }
    }

    getCompilationStats() {
        return {
            cache: this.compilationCache.getStats(),
            registry: {
                compilerCount: this.compilerRegistry.compilers.size
            }
        };
    }
}
```

## 最佳实践

### 1. 编译器使用
```javascript
// ✅ 推荐：正确使用编译器
const compiler = new AdvancedViewCompiler();

const compiled = compiler.compile(archString, {
    debug: false,
    optimize: true,
    variables: { theme: 'dark' }
});
```

### 2. 性能优化
```javascript
// ✅ 推荐：启用缓存
const compiler = new AdvancedViewCompiler();
compiler.compilationCache.maxSize = 2000; // 增加缓存大小
```

### 3. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
try {
    const compiled = compiler.compile(arch, params);
} catch (error) {
    console.error('编译失败:', error);
    // 提供降级方案
}
```

## 总结

Odoo 视图编译器模块提供了强大的模板编译功能：

**核心优势**:
- **高效编译**: 快速的XML到OWL模板转换
- **智能缓存**: 高效的编译结果缓存机制
- **模板优化**: 自动的模板优化和性能提升
- **扩展性**: 灵活的编译器注册和扩展机制
- **错误处理**: 完善的错误检测和处理

**适用场景**:
- 视图模板编译
- 动态内容生成
- 模板优化
- 性能提升
- 开发工具

**设计优势**:
- 插件化架构
- 缓存机制
- 优化引擎
- 类型安全

这个编译器为 Odoo Web 客户端提供了强大的模板处理能力，是视图系统的核心转换引擎。

### 2. 编译器注册系统
```javascript
// 编译器注册和管理
class CompilerRegistry {
    constructor() {
        this.compilers = new Map();
        this.setupDefaultCompilers();
    }
    
    setupDefaultCompilers() {
        // 按钮编译器
        this.register({
            selector: 'button',
            fn: this.compileButton.bind(this),
            class: 'btn'
        });
        
        // 字段编译器
        this.register({
            selector: 'field',
            fn: this.compileField.bind(this),
            doNotCopyAttributes: true
        });
        
        // 标签编译器
        this.register({
            selector: 'label',
            fn: this.compileLabel.bind(this),
            class: 'form-label'
        });
        
        // 分组编译器
        this.register({
            selector: 'group',
            fn: this.compileGroup.bind(this),
            class: 'form-group'
        });
    }
    
    register(compiler) {
        if (!compiler.selector || !compiler.fn) {
            throw new Error('编译器必须包含selector和fn属性');
        }
        
        this.compilers.set(compiler.selector, compiler);
    }
    
    compile(element, params = {}) {
        const tagName = element.tagName.toLowerCase();
        const compiler = this.compilers.get(tagName);
        
        if (compiler) {
            return compiler.fn(element, params);
        }
        
        // 默认编译：直接复制元素
        return this.defaultCompile(element, params);
    }
    
    compileButton(element, params) {
        const button = createElement('button');
        
        // 处理按钮属性
        this.processButtonAttributes(button, element, params);
        
        // 处理按钮内容
        this.processButtonContent(button, element, params);
        
        // 添加事件处理
        this.addButtonEvents(button, element, params);
        
        return button;
    }
    
    processButtonAttributes(button, element, params) {
        // 复制基本属性
        BUTTON_STRING_PROPS.forEach(prop => {
            const value = element.getAttribute(prop);
            if (value) {
                if (this.hasInterpolation(value)) {
                    const processed = toInterpolatedStringExpression(value);
                    button.setAttribute(`t-att-${prop}`, processed);
                } else {
                    button.setAttribute(prop, value);
                }
            }
        });
        
        // 处理CSS类
        const className = element.getAttribute('class');
        const compilerClass = params.class || 'btn';
        const finalClass = className ? `${compilerClass} ${className}` : compilerClass;
        button.setAttribute('class', finalClass);
        
        // 处理点击事件
        const clickParams = this.extractButtonClickParams(element);
        if (Object.keys(clickParams).length > 0) {
            button.setAttribute('t-on-click', 'onButtonClick');
            button.setAttribute('data-click-params', JSON.stringify(clickParams));
        }
    }
    
    processButtonContent(button, element, params) {
        // 处理按钮文本
        const string = element.getAttribute('string');
        if (string) {
            if (this.hasInterpolation(string)) {
                button.setAttribute('t-esc', toInterpolatedStringExpression(string));
            } else {
                button.textContent = string;
            }
        }
        
        // 处理图标
        const icon = element.getAttribute('icon');
        if (icon) {
            const iconElement = createElement('i');
            iconElement.setAttribute('class', `fa ${icon}`);
            button.appendChild(iconElement);
            
            if (string) {
                button.appendChild(createTextNode(' '));
            }
        }
        
        // 复制子元素
        Array.from(element.childNodes).forEach(child => {
            const compiledChild = this.compileNode(child, params);
            if (compiledChild) {
                button.appendChild(compiledChild);
            }
        });
    }
    
    addButtonEvents(button, element, params) {
        // 添加通用按钮事件
        const type = element.getAttribute('type');
        
        if (type === 'object') {
            button.setAttribute('t-on-click', 'onObjectButtonClick');
        } else if (type === 'action') {
            button.setAttribute('t-on-click', 'onActionButtonClick');
        }
        
        // 添加确认对话框
        const confirm = element.getAttribute('confirm');
        if (confirm) {
            button.setAttribute('data-confirm', confirm);
        }
    }
    
    extractButtonClickParams(element) {
        const params = {};
        
        BUTTON_CLICK_PARAMS.forEach(param => {
            const value = element.getAttribute(param);
            if (value) {
                const key = param.replace(/-/g, '_');
                params[key] = value;
            }
        });
        
        return params;
    }
    
    compileField(element, params) {
        const field = createElement('Field');
        
        // 字段名称
        const name = element.getAttribute('name');
        if (name) {
            field.setAttribute('name', `"${name}"`);
        }
        
        // 字段记录
        field.setAttribute('record', 'record');
        
        // 处理字段属性
        this.processFieldAttributes(field, element, params);
        
        return field;
    }
    
    processFieldAttributes(field, element, params) {
        const fieldInfo = {};
        
        // 收集字段信息
        ['string', 'widget', 'options', 'readonly', 'required', 'invisible'].forEach(attr => {
            const value = element.getAttribute(attr);
            if (value) {
                fieldInfo[attr] = value;
            }
        });
        
        // 设置字段信息
        if (Object.keys(fieldInfo).length > 0) {
            field.setAttribute('fieldInfo', JSON.stringify(fieldInfo));
        }
        
        // 处理特殊属性
        const widget = element.getAttribute('widget');
        if (widget) {
            field.setAttribute('widget', `"${widget}"`);
        }
        
        const options = element.getAttribute('options');
        if (options) {
            field.setAttribute('options', options);
        }
    }
    
    compileLabel(element, params) {
        const label = createElement('label');
        
        // 复制属性
        this.copyAttributes(label, element, params);
        
        // 处理for属性
        const forAttr = element.getAttribute('for');
        if (forAttr) {
            label.setAttribute('for', forAttr);
        }
        
        // 处理标签文本
        const string = element.getAttribute('string');
        if (string) {
            label.textContent = string;
        }
        
        return label;
    }
    
    compileGroup(element, params) {
        const group = createElement('div');
        
        // 添加分组类
        group.setAttribute('class', 'form-group');
        
        // 处理分组标题
        const string = element.getAttribute('string');
        if (string) {
            const title = createElement('h4');
            title.textContent = string;
            title.setAttribute('class', 'form-group-title');
            group.appendChild(title);
        }
        
        // 编译子元素
        Array.from(element.children).forEach(child => {
            const compiled = this.compile(child, params);
            if (compiled) {
                group.appendChild(compiled);
            }
        });
        
        return group;
    }
    
    defaultCompile(element, params) {
        const compiled = createElement(element.tagName);
        
        // 复制属性
        this.copyAttributes(compiled, element, params);
        
        // 编译子元素
        Array.from(element.childNodes).forEach(child => {
            const compiledChild = this.compileNode(child, params);
            if (compiledChild) {
                compiled.appendChild(compiledChild);
            }
        });
        
        return compiled;
    }
    
    compileNode(node, params) {
        if (node.nodeType === Node.ELEMENT_NODE) {
            return this.compile(node, params);
        } else if (node.nodeType === Node.TEXT_NODE) {
            const text = node.textContent.trim();
            if (text) {
                if (this.hasInterpolation(text)) {
                    const span = createElement('span');
                    span.setAttribute('t-esc', toInterpolatedStringExpression(text));
                    return span;
                } else {
                    return createTextNode(text);
                }
            }
        }
        
        return null;
    }
    
    copyAttributes(target, source, params) {
        if (params.doNotCopyAttributes) {
            return;
        }
        
        Array.from(source.attributes).forEach(attr => {
            if (this.hasInterpolation(attr.value)) {
                target.setAttribute(`t-att-${attr.name}`, toInterpolatedStringExpression(attr.value));
            } else {
                target.setAttribute(attr.name, attr.value);
            }
        });
    }
    
    hasInterpolation(text) {
        return /(\{\{|#\{).*?(\}{1,2})/.test(text);
    }
}
```

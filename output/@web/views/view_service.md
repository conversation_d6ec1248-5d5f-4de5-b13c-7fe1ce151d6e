# Odoo 视图服务 (View Service) 学习资料

## 文件概述

**文件路径**: `output/@web/views/view_service.js`  
**原始路径**: `/web/static/src/views/view_service.js`  
**模块类型**: 核心视图模块 - 视图服务  
**代码行数**: 149 行  
**依赖关系**: 
- `@web/core/network/rpc` - RPC网络通信 (rpcBus)
- `@web/core/registry` - 注册表系统 (registry)
- `@web/core/orm_service` - ORM服务 (UPDATE_METHODS)

## 模块功能

视图服务模块是 Odoo Web 客户端的核心服务之一。该模块提供了：
- 视图定义加载和缓存
- 视图描述数据管理
- 动作菜单加载
- 过滤器管理
- 视图相关的网络请求处理
- 视图数据的统一接口

这个服务为整个视图系统提供了数据层支持，是连接前端视图组件和后端数据的重要桥梁。

## 类型定义

### IrFilter - 过滤器类型
```javascript
/**
 * @typedef {Object} IrFilter
 * @property {[number, string] | false} user_id - 用户ID和名称
 * @property {string} sort - 排序规则
 * @property {string} context - 上下文
 * @property {string} name - 过滤器名称
 * @property {string} domain - 域条件
 * @property {number} id - 过滤器ID
 * @property {boolean} is_default - 是否为默认过滤器
 * @property {string} model_id - 模型ID
 * @property {[number, string] | false} action_id - 动作ID和名称
 * @property {number | false} embedded_action_id - 嵌入动作ID
 * @property {number | false} embedded_parent_res_id - 嵌入父记录ID
 */
```

### ViewDescription - 视图描述类型
```javascript
/**
 * @typedef {Object} ViewDescription
 * @property {string} arch - 视图架构XML
 * @property {number|false} id - 视图ID
 * @property {number|null} [custom_view_id] - 自定义视图ID
 * @property {Object} [actionMenus] - 动作菜单 (非搜索视图)
 * @property {IrFilter[]} [irFilters] - 过滤器列表 (搜索视图)
 */
```

### LoadViewsParams - 加载视图参数
```javascript
/**
 * @typedef {Object} LoadViewsParams
 * @property {string} resModel - 资源模型名称
 * @property {[number, string][]} views - 视图列表 [[viewId, viewType], ...]
 * @property {Object} context - 上下文对象
 */
```

### LoadViewsOptions - 加载视图选项
```javascript
/**
 * @typedef {Object} LoadViewsOptions
 * @property {number|false} actionId - 动作ID
 * @property {boolean} loadActionMenus - 是否加载动作菜单
 * @property {boolean} loadIrFilters - 是否加载过滤器
 */
```

## 核心服务详解

### viewService - 视图服务对象
```javascript
const viewService = {
    dependencies: ["rpc", "orm"],
    
    start(env, { rpc, orm }) {
        // 监听ORM更新事件
        rpcBus.addEventListener("RPC:RESPONSE", onRPCResponse);
        
        /**
         * 加载视图定义
         * @param {LoadViewsParams} params
         * @param {LoadViewsOptions} options
         * @returns {Promise<ViewDescriptions>}
         */
        async function loadViews(params, options = {}) {
            const { context, resModel, views } = params;
            const loadViewsOptions = {
                action_id: options.actionId || false,
                embedded_action_id: options.embeddedActionId || false,
                embedded_parent_res_id: options.embeddedParentResId || false,
                load_filters: options.loadIrFilters || false,
                toolbar: options.loadActionMenus || false,
            };
            
            return rpc("/web/dataset/call_kw", {
                model: resModel,
                method: "get_views",
                args: [views.map(v => v[0]), views.map(v => v[1])],
                kwargs: {
                    context,
                    options: loadViewsOptions,
                },
            });
        }
        
        return { loadViews };
    },
};
```

**功能特性**:
- **依赖注入**: 依赖RPC和ORM服务
- **事件监听**: 监听RPC响应事件
- **视图加载**: 统一的视图加载接口
- **选项配置**: 灵活的加载选项配置
- **错误处理**: 内置的错误处理机制

**使用示例**:
```javascript
// 基本视图加载
class ViewLoader {
    constructor() {
        this.viewService = useService("view");
        this.cache = new Map();
    }
    
    async loadSingleView(resModel, viewType, viewId = false, context = {}) {
        const cacheKey = `${resModel}_${viewType}_${viewId}`;
        
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }
        
        try {
            const result = await this.viewService.loadViews({
                resModel: resModel,
                views: [[viewId, viewType]],
                context: context
            }, {
                actionId: false,
                loadActionMenus: true,
                loadIrFilters: viewType === 'search'
            });
            
            const viewDescription = result.views[viewType];
            this.cache.set(cacheKey, viewDescription);
            
            return viewDescription;
            
        } catch (error) {
            console.error('加载视图失败:', error);
            throw error;
        }
    }
    
    async loadMultipleViews(resModel, viewTypes, context = {}) {
        const views = viewTypes.map(type => [false, type]);
        
        try {
            const result = await this.viewService.loadViews({
                resModel: resModel,
                views: views,
                context: context
            }, {
                actionId: false,
                loadActionMenus: true,
                loadIrFilters: viewTypes.includes('search')
            });
            
            return result.views;
            
        } catch (error) {
            console.error('加载多个视图失败:', error);
            throw error;
        }
    }
    
    async loadViewsForAction(actionId, resModel, context = {}) {
        try {
            // 首先获取动作定义
            const action = await this.getActionDefinition(actionId);
            const views = action.views || [];
            
            const result = await this.viewService.loadViews({
                resModel: resModel,
                views: views,
                context: { ...context, ...action.context }
            }, {
                actionId: actionId,
                loadActionMenus: true,
                loadIrFilters: true
            });
            
            return {
                views: result.views,
                action: action,
                filters: result.filters
            };
            
        } catch (error) {
            console.error('为动作加载视图失败:', error);
            throw error;
        }
    }
    
    async getActionDefinition(actionId) {
        // 模拟获取动作定义
        return {
            id: actionId,
            name: 'Sample Action',
            res_model: 'res.partner',
            views: [
                [false, 'list'],
                [false, 'form']
            ],
            context: {},
            domain: []
        };
    }
    
    clearCache(resModel = null) {
        if (resModel) {
            // 清除特定模型的缓存
            for (const key of this.cache.keys()) {
                if (key.startsWith(resModel + '_')) {
                    this.cache.delete(key);
                }
            }
        } else {
            // 清除所有缓存
            this.cache.clear();
        }
    }
    
    getCacheStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys())
        };
    }
}

// 在组件中使用视图加载器
class ViewConsumerComponent extends Component {
    setup() {
        this.viewLoader = new ViewLoader();
        this.views = useState({});
        this.loading = useState(false);
        this.error = useState(null);
        
        onWillStart(() => {
            this.loadRequiredViews();
        });
    }
    
    async loadRequiredViews() {
        this.loading = true;
        this.error = null;
        
        try {
            // 加载列表视图
            const listView = await this.viewLoader.loadSingleView(
                this.props.resModel,
                'list',
                false,
                this.props.context
            );
            
            // 加载表单视图
            const formView = await this.viewLoader.loadSingleView(
                this.props.resModel,
                'form',
                false,
                this.props.context
            );
            
            // 加载搜索视图
            const searchView = await this.viewLoader.loadSingleView(
                this.props.resModel,
                'search',
                false,
                this.props.context
            );
            
            this.views = {
                list: listView,
                form: formView,
                search: searchView
            };
            
        } catch (error) {
            this.error = error.message;
        } finally {
            this.loading = false;
        }
    }
    
    async refreshViews() {
        // 清除缓存并重新加载
        this.viewLoader.clearCache(this.props.resModel);
        await this.loadRequiredViews();
    }
}
```

### 3. 视图依赖分析器
```javascript
class ViewDependencyAnalyzer {
    constructor(viewService) {
        this.viewService = viewService;
        this.dependencyGraph = new Map();
        this.fieldDependencies = new Map();
    }

    async analyzeViewDependencies(resModel, viewType) {
        try {
            const viewDescription = await this.viewService.loadViews({
                resModel: resModel,
                views: [[false, viewType]],
                context: {}
            });

            const arch = viewDescription.views[viewType].arch;
            const dependencies = this.extractDependencies(arch);

            this.dependencyGraph.set(`${resModel}_${viewType}`, dependencies);

            return dependencies;

        } catch (error) {
            console.error('分析视图依赖失败:', error);
            return null;
        }
    }

    extractDependencies(arch) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(arch, 'text/xml');

        const dependencies = {
            fields: [],
            models: [],
            views: [],
            actions: [],
            widgets: []
        };

        // 提取字段依赖
        const fields = doc.querySelectorAll('field');
        fields.forEach(field => {
            const fieldName = field.getAttribute('name');
            const widget = field.getAttribute('widget');
            const domain = field.getAttribute('domain');
            const context = field.getAttribute('context');

            if (fieldName) {
                dependencies.fields.push({
                    name: fieldName,
                    widget: widget,
                    domain: domain,
                    context: context
                });
            }

            if (widget) {
                dependencies.widgets.push(widget);
            }
        });

        // 提取按钮动作依赖
        const buttons = doc.querySelectorAll('button');
        buttons.forEach(button => {
            const name = button.getAttribute('name');
            const type = button.getAttribute('type');

            if (name && type === 'action') {
                dependencies.actions.push(name);
            }
        });

        // 提取嵌入视图依赖
        const embeddedViews = doc.querySelectorAll('[view_mode], [view_type]');
        embeddedViews.forEach(view => {
            const viewMode = view.getAttribute('view_mode');
            const viewType = view.getAttribute('view_type');

            if (viewMode) {
                dependencies.views.push(viewMode);
            }
            if (viewType) {
                dependencies.views.push(viewType);
            }
        });

        return dependencies;
    }

    async analyzeFieldDependencies(resModel) {
        try {
            // 获取模型字段信息
            const fieldInfo = await this.getModelFields(resModel);
            const dependencies = {};

            Object.entries(fieldInfo).forEach(([fieldName, field]) => {
                dependencies[fieldName] = {
                    type: field.type,
                    relation: field.relation,
                    relatedFields: [],
                    computeDependencies: field.depends || [],
                    domainDependencies: this.extractDomainDependencies(field.domain)
                };

                // 分析关系字段
                if (field.relation) {
                    dependencies[fieldName].relatedFields = this.getRelatedFields(field.relation);
                }
            });

            this.fieldDependencies.set(resModel, dependencies);
            return dependencies;

        } catch (error) {
            console.error('分析字段依赖失败:', error);
            return null;
        }
    }

    async getModelFields(resModel) {
        // 模拟获取模型字段信息
        return {
            name: { type: 'char', string: 'Name' },
            email: { type: 'char', string: 'Email' },
            partner_id: {
                type: 'many2one',
                relation: 'res.partner',
                string: 'Partner'
            },
            line_ids: {
                type: 'one2many',
                relation: 'sale.order.line',
                string: 'Order Lines'
            }
        };
    }

    extractDomainDependencies(domain) {
        if (!domain) return [];

        const dependencies = [];
        const domainStr = typeof domain === 'string' ? domain : JSON.stringify(domain);

        // 简单的字段名提取
        const fieldMatches = domainStr.match(/['"]([a-zA-Z_][a-zA-Z0-9_]*)['"]/g);
        if (fieldMatches) {
            fieldMatches.forEach(match => {
                const fieldName = match.replace(/['"]/g, '');
                if (!dependencies.includes(fieldName)) {
                    dependencies.push(fieldName);
                }
            });
        }

        return dependencies;
    }

    getRelatedFields(relationModel) {
        // 模拟获取关联模型的字段
        const commonFields = ['name', 'display_name', 'id'];
        return commonFields;
    }

    generateDependencyReport(resModel) {
        const viewDeps = Array.from(this.dependencyGraph.entries())
            .filter(([key]) => key.startsWith(resModel + '_'));

        const fieldDeps = this.fieldDependencies.get(resModel);

        return {
            model: resModel,
            viewDependencies: Object.fromEntries(viewDeps),
            fieldDependencies: fieldDeps,
            summary: this.generateSummary(viewDeps, fieldDeps)
        };
    }

    generateSummary(viewDeps, fieldDeps) {
        const summary = {
            totalViews: viewDeps.length,
            totalFields: fieldDeps ? Object.keys(fieldDeps).length : 0,
            uniqueWidgets: new Set(),
            relatedModels: new Set(),
            actions: new Set()
        };

        viewDeps.forEach(([key, deps]) => {
            deps.widgets.forEach(widget => summary.uniqueWidgets.add(widget));
            deps.actions.forEach(action => summary.actions.add(action));
        });

        if (fieldDeps) {
            Object.values(fieldDeps).forEach(field => {
                if (field.relation) {
                    summary.relatedModels.add(field.relation);
                }
            });
        }

        return {
            totalViews: summary.totalViews,
            totalFields: summary.totalFields,
            uniqueWidgets: Array.from(summary.uniqueWidgets),
            relatedModels: Array.from(summary.relatedModels),
            actions: Array.from(summary.actions)
        };
    }

    visualizeDependencies(resModel) {
        const report = this.generateDependencyReport(resModel);

        // 生成Mermaid图表代码
        let mermaidCode = 'graph TD\n';
        mermaidCode += `    ${resModel}[${resModel}]\n`;

        // 添加关联模型
        report.summary.relatedModels.forEach(model => {
            mermaidCode += `    ${model}[${model}]\n`;
            mermaidCode += `    ${resModel} --> ${model}\n`;
        });

        // 添加视图
        Object.keys(report.viewDependencies).forEach(viewKey => {
            const [model, viewType] = viewKey.split('_');
            mermaidCode += `    ${viewKey}[${viewType} View]\n`;
            mermaidCode += `    ${resModel} --> ${viewKey}\n`;
        });

        return mermaidCode;
    }
}
```

## RPC事件处理

### onRPCResponse - RPC响应处理
```javascript
function onRPCResponse({ detail: { data, settings } }) {
    const { model, method } = settings;

    // 检查是否是更新操作
    if (UPDATE_METHODS.includes(method)) {
        // 清除相关的视图缓存
        clearViewCache(model);

        // 触发视图刷新事件
        rpcBus.trigger("VIEW_CACHE_INVALIDATED", { model, method });
    }
}
```

**功能特性**:
- **自动缓存失效**: 监听数据更新操作
- **缓存清理**: 自动清理相关的视图缓存
- **事件通知**: 通知其他组件缓存已失效
- **性能优化**: 避免使用过期的缓存数据

## 最佳实践

### 1. 服务使用
```javascript
// ✅ 推荐：正确使用视图服务
class ViewAwareComponent extends Component {
    setup() {
        this.viewService = useService("view");
        this.views = useState({});

        onWillStart(async () => {
            await this.loadViews();
        });
    }

    async loadViews() {
        try {
            const result = await this.viewService.loadViews({
                resModel: this.props.resModel,
                views: [[false, 'list'], [false, 'form']],
                context: this.props.context || {}
            }, {
                actionId: this.props.actionId,
                loadActionMenus: true,
                loadIrFilters: false
            });

            this.views = result.views;
        } catch (error) {
            console.error('加载视图失败:', error);
        }
    }
}
```

### 2. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
async function safeLoadViews(viewService, params, options) {
    try {
        return await viewService.loadViews(params, options);
    } catch (error) {
        console.error('视图加载失败:', error);

        // 提供降级方案
        return {
            views: {},
            filters: [],
            actionMenus: {}
        };
    }
}
```

### 3. 性能优化
```javascript
// ✅ 推荐：缓存和批量加载
class OptimizedViewLoader {
    constructor(viewService) {
        this.viewService = viewService;
        this.cache = new Map();
        this.pendingRequests = new Map();
    }

    async loadViews(params, options) {
        const key = this.generateKey(params, options);

        // 检查缓存
        if (this.cache.has(key)) {
            return this.cache.get(key);
        }

        // 检查是否有相同的请求正在进行
        if (this.pendingRequests.has(key)) {
            return this.pendingRequests.get(key);
        }

        // 发起新请求
        const promise = this.viewService.loadViews(params, options);
        this.pendingRequests.set(key, promise);

        try {
            const result = await promise;
            this.cache.set(key, result);
            return result;
        } finally {
            this.pendingRequests.delete(key);
        }
    }

    generateKey(params, options) {
        return JSON.stringify({ params, options });
    }
}
```

## 总结

Odoo 视图服务模块提供了强大的视图数据管理功能：

**核心优势**:
- **统一接口**: 为视图加载提供统一的服务接口
- **缓存机制**: 内置的缓存和性能优化
- **事件驱动**: 基于事件的缓存失效机制
- **类型安全**: 完整的TypeScript类型定义
- **扩展性**: 支持自定义加载选项和处理逻辑

**适用场景**:
- 视图数据加载
- 缓存管理
- 性能优化
- 依赖分析
- 预加载策略

**设计优势**:
- 服务化架构
- 依赖注入
- 事件驱动
- 类型安全

这个视图服务为 Odoo Web 客户端提供了可靠的视图数据支持，是构建高性能视图系统的重要基础。

## 高级应用模式

### 1. 智能视图缓存管理器
```javascript
class SmartViewCacheManager {
    constructor() {
        this.cache = new Map();
        this.accessTimes = new Map();
        this.maxCacheSize = 100;
        this.maxAge = 5 * 60 * 1000; // 5分钟
        this.setupCleanupTimer();
    }
    
    setupCleanupTimer() {
        // 每分钟清理一次过期缓存
        setInterval(() => {
            this.cleanupExpiredCache();
        }, 60 * 1000);
    }
    
    generateCacheKey(resModel, viewType, viewId, context) {
        const contextKey = JSON.stringify(context || {});
        return `${resModel}_${viewType}_${viewId}_${this.hashString(contextKey)}`;
    }
    
    hashString(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return hash.toString(36);
    }
    
    get(key) {
        const item = this.cache.get(key);
        if (item) {
            // 更新访问时间
            this.accessTimes.set(key, Date.now());
            
            // 检查是否过期
            if (Date.now() - item.timestamp > this.maxAge) {
                this.cache.delete(key);
                this.accessTimes.delete(key);
                return null;
            }
            
            return item.data;
        }
        return null;
    }
    
    set(key, data) {
        // 检查缓存大小限制
        if (this.cache.size >= this.maxCacheSize) {
            this.evictLeastRecentlyUsed();
        }
        
        this.cache.set(key, {
            data: data,
            timestamp: Date.now()
        });
        this.accessTimes.set(key, Date.now());
    }
    
    evictLeastRecentlyUsed() {
        let oldestKey = null;
        let oldestTime = Date.now();
        
        for (const [key, time] of this.accessTimes) {
            if (time < oldestTime) {
                oldestTime = time;
                oldestKey = key;
            }
        }
        
        if (oldestKey) {
            this.cache.delete(oldestKey);
            this.accessTimes.delete(oldestKey);
        }
    }
    
    cleanupExpiredCache() {
        const now = Date.now();
        const expiredKeys = [];
        
        for (const [key, item] of this.cache) {
            if (now - item.timestamp > this.maxAge) {
                expiredKeys.push(key);
            }
        }
        
        expiredKeys.forEach(key => {
            this.cache.delete(key);
            this.accessTimes.delete(key);
        });
        
        console.log(`清理了 ${expiredKeys.length} 个过期缓存项`);
    }
    
    clear(pattern = null) {
        if (pattern) {
            const regex = new RegExp(pattern);
            const keysToDelete = [];
            
            for (const key of this.cache.keys()) {
                if (regex.test(key)) {
                    keysToDelete.push(key);
                }
            }
            
            keysToDelete.forEach(key => {
                this.cache.delete(key);
                this.accessTimes.delete(key);
            });
        } else {
            this.cache.clear();
            this.accessTimes.clear();
        }
    }
    
    getStats() {
        return {
            size: this.cache.size,
            maxSize: this.maxCacheSize,
            maxAge: this.maxAge,
            oldestItem: this.getOldestItemAge(),
            newestItem: this.getNewestItemAge()
        };
    }
    
    getOldestItemAge() {
        let oldest = Date.now();
        for (const item of this.cache.values()) {
            if (item.timestamp < oldest) {
                oldest = item.timestamp;
            }
        }
        return Date.now() - oldest;
    }
    
    getNewestItemAge() {
        let newest = 0;
        for (const item of this.cache.values()) {
            if (item.timestamp > newest) {
                newest = item.timestamp;
            }
        }
        return Date.now() - newest;
    }
}
```

### 2. 视图预加载器
```javascript
class ViewPreloader {
    constructor(viewService) {
        this.viewService = viewService;
        this.preloadQueue = [];
        this.isPreloading = false;
        this.preloadedViews = new Set();
    }
    
    addToPreloadQueue(resModel, viewTypes, priority = 'normal') {
        const item = {
            resModel,
            viewTypes,
            priority,
            timestamp: Date.now()
        };
        
        if (priority === 'high') {
            this.preloadQueue.unshift(item);
        } else {
            this.preloadQueue.push(item);
        }
        
        this.processPreloadQueue();
    }
    
    async processPreloadQueue() {
        if (this.isPreloading || this.preloadQueue.length === 0) {
            return;
        }
        
        this.isPreloading = true;
        
        while (this.preloadQueue.length > 0) {
            const item = this.preloadQueue.shift();
            await this.preloadViews(item.resModel, item.viewTypes);
            
            // 添加小延迟避免阻塞主线程
            await new Promise(resolve => setTimeout(resolve, 10));
        }
        
        this.isPreloading = false;
    }
    
    async preloadViews(resModel, viewTypes) {
        const key = `${resModel}_${viewTypes.join(',')}`;
        
        if (this.preloadedViews.has(key)) {
            return; // 已经预加载过
        }
        
        try {
            const views = viewTypes.map(type => [false, type]);
            
            await this.viewService.loadViews({
                resModel: resModel,
                views: views,
                context: {}
            }, {
                actionId: false,
                loadActionMenus: false,
                loadIrFilters: false
            });
            
            this.preloadedViews.add(key);
            console.log(`预加载完成: ${key}`);
            
        } catch (error) {
            console.warn(`预加载失败: ${key}`, error);
        }
    }
    
    preloadCommonViews() {
        // 预加载常用模型的视图
        const commonModels = [
            'res.partner',
            'res.users',
            'ir.attachment',
            'mail.message'
        ];
        
        commonModels.forEach(model => {
            this.addToPreloadQueue(model, ['list', 'form'], 'high');
        });
    }
    
    preloadRelatedViews(currentModel) {
        // 根据当前模型预加载相关视图
        const relatedModels = this.getRelatedModels(currentModel);
        
        relatedModels.forEach(model => {
            this.addToPreloadQueue(model, ['list', 'form'], 'normal');
        });
    }
    
    getRelatedModels(model) {
        // 简化的相关模型映射
        const relations = {
            'res.partner': ['res.users', 'sale.order', 'account.invoice'],
            'sale.order': ['sale.order.line', 'res.partner', 'product.product'],
            'product.product': ['product.category', 'product.template']
        };
        
        return relations[model] || [];
    }
    
    clearPreloadedViews() {
        this.preloadedViews.clear();
        this.preloadQueue = [];
    }
    
    getPreloadStats() {
        return {
            preloadedCount: this.preloadedViews.size,
            queueLength: this.preloadQueue.length,
            isPreloading: this.isPreloading,
            preloadedViews: Array.from(this.preloadedViews)
        };
    }
}
```

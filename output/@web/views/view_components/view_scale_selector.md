# ViewScaleSelector - 视图比例选择器

## 概述

`view_scale_selector.js` 是 Odoo Web 客户端的视图比例选择器组件，负责在时间相关的视图中提供时间比例选择功能。该模块包含32行代码，是一个功能专门的比例选择组件，专门用于日历、甘特图等时间视图中的时间比例切换，具备比例选择、周末显示控制、下拉选择等特性，是时间视图导航的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/view_components/view_scale_selector.js`
- **行数**: 32
- **模块**: `@web/views/view_components/view_scale_selector`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                            // OWL框架
'@web/core/dropdown/dropdown'          // 下拉菜单组件
'@web/core/dropdown/dropdown_item'     // 下拉菜单项组件
```

## 核心功能

### 1. 组件定义

```javascript
const ViewScaleSelector = class ViewScaleSelector extends Component {
    static components = {
        Dropdown,
        DropdownItem,
    };
    static template = "web.ViewScaleSelector";
    static props = {
        scales: { type: Object },
        currentScale: { type: String },
        isWeekendVisible: { type: Boolean, optional: true },
        setScale: { type: Function },
        toggleWeekendVisibility: { type: Function, optional: true },
        dropdownClass: { type: String, optional: true },
    };
}
```

**组件特性**:
- **下拉组件**: 集成Dropdown和DropdownItem组件
- **比例配置**: 接收比例配置对象
- **当前比例**: 标记当前选中的比例
- **周末控制**: 可选的周末显示控制
- **比例设置**: 比例切换回调函数
- **样式类**: 可选的下拉样式类

### 2. 比例数据处理

```javascript
get scales() {
    return Object.entries(this.props.scales).map(([key, value]) => ({ key, ...value }));
}
```

**数据处理功能**:
- **对象转换**: 将比例对象转换为数组
- **键值映射**: 映射键值对到对象
- **数据扁平化**: 扁平化比例数据结构
- **模板友好**: 提供模板友好的数据格式

### 3. 属性配置

```javascript
static props = {
    scales: { type: Object },                                    // 比例配置对象
    currentScale: { type: String },                             // 当前比例
    isWeekendVisible: { type: Boolean, optional: true },        // 周末可见性
    setScale: { type: Function },                               // 比例设置函数
    toggleWeekendVisibility: { type: Function, optional: true }, // 周末切换函数
    dropdownClass: { type: String, optional: true },           // 下拉样式类
};
```

**属性功能**:
- **比例管理**: 管理可用的时间比例
- **状态跟踪**: 跟踪当前选中的比例
- **功能控制**: 控制周末显示功能
- **回调处理**: 处理比例切换和周末切换
- **样式定制**: 支持自定义下拉样式

## 使用场景

### 1. 视图比例选择器管理器

```javascript
// 视图比例选择器管理器
class ViewScaleSelectorManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置比例选择器配置
        this.scaleSelectorConfig = {
            enableWeekendToggle: true,
            enableCustomScales: false,
            enableScalePresets: true,
            enableKeyboardNavigation: true,
            enableTooltips: true,
            defaultScale: 'month',
            autoSelectBestScale: false
        };
        
        // 设置标准时间比例
        this.standardScales = new Map([
            ['day', {
                name: 'Day',
                description: 'Daily view',
                interval: 'day',
                format: 'YYYY-MM-DD',
                granularity: 24 * 60 * 60 * 1000, // 1 day in ms
                icon: 'fa-calendar-day'
            }],
            ['week', {
                name: 'Week',
                description: 'Weekly view',
                interval: 'week',
                format: 'YYYY-[W]WW',
                granularity: 7 * 24 * 60 * 60 * 1000, // 1 week in ms
                icon: 'fa-calendar-week'
            }],
            ['month', {
                name: 'Month',
                description: 'Monthly view',
                interval: 'month',
                format: 'YYYY-MM',
                granularity: 30 * 24 * 60 * 60 * 1000, // ~1 month in ms
                icon: 'fa-calendar-alt'
            }],
            ['quarter', {
                name: 'Quarter',
                description: 'Quarterly view',
                interval: 'quarter',
                format: 'YYYY-[Q]Q',
                granularity: 90 * 24 * 60 * 60 * 1000, // ~1 quarter in ms
                icon: 'fa-calendar'
            }],
            ['year', {
                name: 'Year',
                description: 'Yearly view',
                interval: 'year',
                format: 'YYYY',
                granularity: 365 * 24 * 60 * 60 * 1000, // ~1 year in ms
                icon: 'fa-calendar-check'
            }]
        ]);
        
        // 设置比例预设
        this.scalePresets = new Map([
            ['calendar', ['day', 'week', 'month']],
            ['gantt', ['day', 'week', 'month', 'quarter']],
            ['timeline', ['day', 'week', 'month', 'year']],
            ['planning', ['week', 'month', 'quarter']],
            ['reporting', ['month', 'quarter', 'year']]
        ]);
        
        // 设置比例统计
        this.scaleStatistics = {
            totalSelectors: 0,
            scaleChanges: 0,
            weekendToggles: 0,
            scaleUsage: new Map(),
            averageScaleChangesPerSession: 0,
            mostUsedScale: null,
            sessionScaleChanges: 0
        };
        
        this.initializeScaleSelectorSystem();
    }
    
    // 初始化比例选择器系统
    initializeScaleSelectorSystem() {
        // 创建增强的比例选择器
        this.createEnhancedScaleSelector();
        
        // 设置比例系统
        this.setupScaleSystem();
        
        // 设置预设系统
        this.setupPresetSystem();
        
        // 设置导航系统
        this.setupNavigationSystem();
    }
    
    // 创建增强的比例选择器
    createEnhancedScaleSelector() {
        const originalComponent = ViewScaleSelector;
        
        this.EnhancedViewScaleSelector = class extends originalComponent {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加导航功能
                this.addNavigationFeatures();
                
                // 添加预设功能
                this.addPresetFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isOpen: false,
                    selectedScale: this.props.currentScale,
                    weekendVisible: this.props.isWeekendVisible || false,
                    lastChangeTime: null,
                    changeCount: 0,
                    favoriteScales: new Set()
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的比例获取
                this.enhancedScales = () => {
                    const scales = Object.entries(this.props.scales).map(([key, value]) => ({
                        key,
                        ...value,
                        isActive: key === this.enhancedState.selectedScale,
                        isFavorite: this.enhancedState.favoriteScales.has(key),
                        usage: this.scaleStatistics.scaleUsage.get(key) || 0
                    }));
                    
                    // 排序：活动的在前，收藏的在前，使用频率高的在前
                    return scales.sort((a, b) => {
                        if (a.isActive !== b.isActive) return b.isActive - a.isActive;
                        if (a.isFavorite !== b.isFavorite) return b.isFavorite - a.isFavorite;
                        return b.usage - a.usage;
                    });
                };
                
                // 增强的比例设置
                this.enhancedSetScale = (scale) => {
                    const previousScale = this.enhancedState.selectedScale;
                    
                    // 更新状态
                    this.enhancedState.selectedScale = scale;
                    this.enhancedState.lastChangeTime = new Date();
                    this.enhancedState.changeCount++;
                    
                    // 记录使用统计
                    this.recordScaleUsage(scale);
                    
                    // 执行原始设置
                    if (this.props.setScale) {
                        this.props.setScale(scale);
                    }
                    
                    // 记录比例变更
                    this.recordScaleChange(previousScale, scale);
                };
                
                // 增强的周末切换
                this.enhancedToggleWeekend = () => {
                    this.enhancedState.weekendVisible = !this.enhancedState.weekendVisible;
                    
                    // 执行原始切换
                    if (this.props.toggleWeekendVisibility) {
                        this.props.toggleWeekendVisibility();
                    }
                    
                    // 记录周末切换
                    this.recordWeekendToggle();
                };
                
                // 切换收藏比例
                this.toggleFavoriteScale = (scale) => {
                    const { favoriteScales } = this.enhancedState;
                    
                    if (favoriteScales.has(scale)) {
                        favoriteScales.delete(scale);
                    } else {
                        favoriteScales.add(scale);
                    }
                };
                
                // 获取比例信息
                this.getScaleInfo = (scaleKey) => {
                    const scale = this.standardScales.get(scaleKey);
                    if (!scale) {
                        return null;
                    }
                    
                    return {
                        ...scale,
                        key: scaleKey,
                        isActive: scaleKey === this.enhancedState.selectedScale,
                        isFavorite: this.enhancedState.favoriteScales.has(scaleKey),
                        usage: this.scaleStatistics.scaleUsage.get(scaleKey) || 0
                    };
                };
                
                // 获取最佳比例建议
                this.getBestScaleRecommendation = (dataRange) => {
                    if (!dataRange || !dataRange.start || !dataRange.end) {
                        return this.scaleSelectorConfig.defaultScale;
                    }
                    
                    const duration = dataRange.end - dataRange.start;
                    
                    // 根据数据范围推荐最佳比例
                    if (duration <= 7 * 24 * 60 * 60 * 1000) { // 1 week
                        return 'day';
                    } else if (duration <= 3 * 30 * 24 * 60 * 60 * 1000) { // 3 months
                        return 'week';
                    } else if (duration <= 2 * 365 * 24 * 60 * 60 * 1000) { // 2 years
                        return 'month';
                    } else if (duration <= 10 * 365 * 24 * 60 * 60 * 1000) { // 10 years
                        return 'quarter';
                    } else {
                        return 'year';
                    }
                };
                
                // 应用比例预设
                this.applyScalePreset = (presetName) => {
                    const preset = this.scalePresets.get(presetName);
                    if (!preset || preset.length === 0) {
                        return;
                    }
                    
                    // 选择预设中的第一个比例
                    this.enhancedSetScale(preset[0]);
                };
                
                // 获取选择器状态
                this.getSelectorState = () => {
                    return {
                        selectedScale: this.enhancedState.selectedScale,
                        weekendVisible: this.enhancedState.weekendVisible,
                        isOpen: this.enhancedState.isOpen,
                        changeCount: this.enhancedState.changeCount,
                        lastChangeTime: this.enhancedState.lastChangeTime,
                        favoriteScales: Array.from(this.enhancedState.favoriteScales),
                        availableScales: this.enhancedScales()
                    };
                };
                
                // 记录比例使用
                this.recordScaleUsage = (scale) => {
                    const count = this.scaleStatistics.scaleUsage.get(scale) || 0;
                    this.scaleStatistics.scaleUsage.set(scale, count + 1);
                    
                    this.updateMostUsedScale();
                };
                
                // 记录比例变更
                this.recordScaleChange = (from, to) => {
                    this.scaleStatistics.scaleChanges++;
                    this.scaleStatistics.sessionScaleChanges++;
                    
                    this.updateAverageScaleChanges();
                };
                
                // 记录周末切换
                this.recordWeekendToggle = () => {
                    this.scaleStatistics.weekendToggles++;
                };
                
                // 更新最常用比例
                this.updateMostUsedScale = () => {
                    let maxUsage = 0;
                    let mostUsed = null;
                    
                    for (const [scale, usage] of this.scaleStatistics.scaleUsage.entries()) {
                        if (usage > maxUsage) {
                            maxUsage = usage;
                            mostUsed = scale;
                        }
                    }
                    
                    this.scaleStatistics.mostUsedScale = mostUsed;
                };
                
                // 更新平均比例变更
                this.updateAverageScaleChanges = () => {
                    if (this.scaleStatistics.totalSelectors > 0) {
                        this.scaleStatistics.averageScaleChangesPerSession = 
                            this.scaleStatistics.scaleChanges / this.scaleStatistics.totalSelectors;
                    }
                };
            }
            
            addNavigationFeatures() {
                // 导航功能
                this.navigationManager = {
                    enabled: this.scaleSelectorConfig.enableKeyboardNavigation,
                    setScale: (scale) => this.enhancedSetScale(scale),
                    toggleWeekend: () => this.enhancedToggleWeekend(),
                    getBestScale: (dataRange) => this.getBestScaleRecommendation(dataRange)
                };
            }
            
            addPresetFeatures() {
                // 预设功能
                this.presetManager = {
                    enabled: this.scaleSelectorConfig.enableScalePresets,
                    presets: this.scalePresets,
                    apply: (presetName) => this.applyScalePreset(presetName)
                };
            }
            
            // 重写原始方法
            get scales() {
                return this.enhancedScales();
            }
        };
    }
    
    // 设置比例系统
    setupScaleSystem() {
        this.scaleSystemConfig = {
            enabled: true,
            scales: this.standardScales,
            defaultScale: this.scaleSelectorConfig.defaultScale
        };
    }
    
    // 设置预设系统
    setupPresetSystem() {
        this.presetSystemConfig = {
            enabled: this.scaleSelectorConfig.enableScalePresets,
            presets: this.scalePresets
        };
    }
    
    // 设置导航系统
    setupNavigationSystem() {
        this.navigationSystemConfig = {
            enabled: this.scaleSelectorConfig.enableKeyboardNavigation,
            autoSelect: this.scaleSelectorConfig.autoSelectBestScale
        };
    }
    
    // 创建比例选择器
    createScaleSelector(props) {
        const selector = new this.EnhancedViewScaleSelector(props);
        this.scaleStatistics.totalSelectors++;
        return selector;
    }
    
    // 注册自定义比例
    registerCustomScale(key, config) {
        this.standardScales.set(key, config);
    }
    
    // 注册比例预设
    registerScalePreset(name, scales) {
        this.scalePresets.set(name, scales);
    }
    
    // 获取比例统计
    getScaleStatistics() {
        return {
            ...this.scaleStatistics,
            scaleVariety: this.scaleStatistics.scaleUsage.size,
            changeRate: this.scaleStatistics.scaleChanges / 
                       Math.max(this.scaleStatistics.totalSelectors, 1),
            weekendToggleRate: this.scaleStatistics.weekendToggles / 
                              Math.max(this.scaleStatistics.totalSelectors, 1)
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理比例
        this.standardScales.clear();
        
        // 清理预设
        this.scalePresets.clear();
        
        // 清理统计
        this.scaleStatistics.scaleUsage.clear();
        
        // 重置统计
        this.scaleStatistics = {
            totalSelectors: 0,
            scaleChanges: 0,
            weekendToggles: 0,
            scaleUsage: new Map(),
            averageScaleChangesPerSession: 0,
            mostUsedScale: null,
            sessionScaleChanges: 0
        };
    }
}

// 使用示例
const scaleSelectorManager = new ViewScaleSelectorManager();

// 创建比例选择器
const scaleSelector = scaleSelectorManager.createScaleSelector({
    scales: {
        day: { name: 'Day', description: 'Daily view' },
        week: { name: 'Week', description: 'Weekly view' },
        month: { name: 'Month', description: 'Monthly view' }
    },
    currentScale: 'month',
    isWeekendVisible: true,
    setScale: (scale) => {
        console.log('Scale changed to:', scale);
    },
    toggleWeekendVisibility: () => {
        console.log('Weekend visibility toggled');
    }
});

// 注册自定义比例
scaleSelectorManager.registerCustomScale('hour', {
    name: 'Hour',
    description: 'Hourly view',
    interval: 'hour',
    format: 'YYYY-MM-DD HH:00',
    granularity: 60 * 60 * 1000,
    icon: 'fa-clock'
});

// 获取统计信息
const stats = scaleSelectorManager.getScaleStatistics();
console.log('Scale selector statistics:', stats);
```

## 技术特点

### 1. 简洁设计
- **最小代码**: 极简的代码实现
- **专注功能**: 专注于比例选择
- **模板驱动**: 使用模板驱动的渲染
- **组件集成**: 集成下拉菜单组件

### 2. 比例管理
- **比例配置**: 处理比例配置数据
- **当前状态**: 跟踪当前选中的比例
- **数据转换**: 转换比例数据格式
- **回调处理**: 处理比例切换事件

### 3. 功能扩展
- **周末控制**: 可选的周末显示控制
- **样式定制**: 支持自定义下拉样式
- **键盘导航**: 支持键盘操作
- **工具提示**: 支持工具提示显示

### 4. 时间视图支持
- **多比例**: 支持多种时间比例
- **灵活配置**: 灵活的比例配置
- **视图适配**: 适配不同的时间视图
- **用户体验**: 提供良好的用户体验

## 设计模式

### 1. 组合模式 (Composition Pattern)
- **组件组合**: 组合下拉菜单组件
- **功能组合**: 组合比例选择功能
- **模板组合**: 组合模板元素

### 2. 策略模式 (Strategy Pattern)
- **比例策略**: 不同的时间比例策略
- **显示策略**: 不同的比例显示策略
- **选择策略**: 不同的比例选择策略

### 3. 观察者模式 (Observer Pattern)
- **比例观察**: 观察比例变化
- **状态观察**: 观察组件状态变化
- **用户观察**: 观察用户交互

### 4. 工厂模式 (Factory Pattern)
- **组件工厂**: 创建比例选择器组件
- **比例工厂**: 创建比例配置对象
- **预设工厂**: 创建比例预设

## 注意事项

1. **数据验证**: 验证比例配置的有效性
2. **性能考虑**: 避免频繁的比例切换
3. **用户体验**: 提供清晰的比例选择界面
4. **兼容性**: 保持向后兼容性

## 扩展建议

1. **自定义比例**: 支持用户自定义比例
2. **比例预设**: 添加比例预设功能
3. **智能推荐**: 基于数据范围的智能比例推荐
4. **快捷键**: 支持快捷键切换比例
5. **历史记录**: 记录比例切换历史

该视图比例选择器为Odoo Web客户端提供了简洁高效的时间比例选择功能，通过下拉菜单界面和灵活的配置选项确保了时间视图导航的便利性和准确性。

# AnimatedNumber - 动画数字组件

## 概述

`animated_number.js` 是 Odoo Web 客户端的动画数字组件，负责显示带有平滑动画效果的数字变化。该模块包含61行代码，是一个功能专门的动画组件，专门用于数字的动画过渡显示，具备数值动画、格式化显示、动画控制、性能优化等特性，是数据可视化和用户体验增强的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/view_components/animated_number.js`
- **行数**: 61
- **模块**: `@web/views/view_components/animated_number`

## 依赖关系

```javascript
// 核心依赖
'@web/core/browser/browser'             // 浏览器服务
'@web/views/fields/formatters'          // 字段格式化器
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const AnimatedNumber = class AnimatedNumber extends Component {
    static template = "web.AnimatedNumber";
    static props = {
        value: Number,
        duration: Number,
        animationClass: { type: String, optional: true },
        currency: { type: [Object, Boolean], optional: true },
        title: { type: String, optional: true },
        slots: {
            type: Object,
            shape: {
                prefix: { type: Object, optional: true },
            },
            optional: true,
        },
    };
    static enableAnimations = true;
}
```

**组件特性**:
- **数值属性**: 必需的数值和动画持续时间
- **动画类**: 可选的动画CSS类
- **货币支持**: 支持货币格式显示
- **标题**: 支持标题属性
- **插槽**: 支持前缀插槽
- **动画控制**: 全局动画开关

### 2. 组件初始化

```javascript
setup() {
    this.formatInteger = formatInteger;
    this.state = useState({ value: this.props.value });
    this.handle = null;
    
    onWillUpdateProps((nextProps) => {
        const { value: from } = this.props;
        const { value: to, duration } = nextProps;
        
        if (!this.constructor.enableAnimations || !duration || to <= from) {
            browser.cancelAnimationFrame(this.handle);
            this.state.value = to;
            return;
        }
        
        // 启动动画
        this.startAnimation(from, to, duration);
    });
    
    onWillUnmount(() => browser.cancelAnimationFrame(this.handle));
}
```

**初始化功能**:
- **格式化器**: 绑定整数格式化器
- **状态管理**: 管理当前显示的数值
- **动画句柄**: 管理动画帧句柄
- **属性监听**: 监听属性变化触发动画
- **清理机制**: 组件卸载时清理动画

### 3. 动画逻辑

```javascript
const animate = () => {
    const progress = (Date.now() - startTime) / duration;
    if (progress >= 1) {
        this.state.value = to;
    } else {
        this.state.value = from + (to - from) * progress;
        this.handle = browser.requestAnimationFrame(animate);
    }
};
```

**动画功能**:
- **进度计算**: 基于时间计算动画进度
- **线性插值**: 在起始值和目标值之间线性插值
- **帧动画**: 使用requestAnimationFrame实现平滑动画
- **完成检测**: 检测动画完成并设置最终值

### 4. 数值格式化

```javascript
format(value) {
    return this.formatInteger(value, { 
        humanReadable: true, 
        decimals: 0, 
        minDigits: 3 
    });
}
```

**格式化功能**:
- **人类可读**: 使用人类可读的数字格式
- **无小数**: 显示整数格式
- **最小位数**: 确保最少3位数字显示
- **国际化**: 支持本地化数字格式

## 使用场景

### 1. 动画数字管理器

```javascript
// 动画数字管理器
class AnimatedNumberManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置动画数字配置
        this.animatedNumberConfig = {
            enableAnimations: true,
            enablePerformanceMode: false,
            enableCustomEasing: false,
            enableSoundEffects: false,
            defaultDuration: 1000,
            maxDuration: 5000,
            minDuration: 100,
            frameRate: 60
        };
        
        // 设置缓动函数
        this.easingFunctions = new Map([
            ['linear', (t) => t],
            ['easeInQuad', (t) => t * t],
            ['easeOutQuad', (t) => t * (2 - t)],
            ['easeInOutQuad', (t) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t],
            ['easeInCubic', (t) => t * t * t],
            ['easeOutCubic', (t) => (--t) * t * t + 1],
            ['easeInOutCubic', (t) => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1]
        ]);
        
        // 设置格式化选项
        this.formatOptions = new Map([
            ['integer', { humanReadable: true, decimals: 0, minDigits: 3 }],
            ['float', { humanReadable: true, decimals: 2, minDigits: 1 }],
            ['currency', { humanReadable: true, decimals: 2, minDigits: 1, currency: true }],
            ['percentage', { humanReadable: false, decimals: 1, suffix: '%' }],
            ['compact', { humanReadable: true, decimals: 0, minDigits: 1, compact: true }]
        ]);
        
        // 设置动画统计
        this.animationStatistics = {
            totalAnimations: 0,
            activeAnimations: 0,
            completedAnimations: 0,
            cancelledAnimations: 0,
            averageDuration: 0,
            totalDuration: 0,
            performanceMetrics: {
                frameDrops: 0,
                averageFPS: 60,
                minFPS: 60,
                maxFPS: 60
            }
        };
        
        this.initializeAnimatedNumberSystem();
    }
    
    // 初始化动画数字系统
    initializeAnimatedNumberSystem() {
        // 创建增强的动画数字组件
        this.createEnhancedAnimatedNumber();
        
        // 设置动画系统
        this.setupAnimationSystem();
        
        // 设置格式化系统
        this.setupFormattingSystem();
        
        // 设置性能监控
        this.setupPerformanceMonitoring();
    }
    
    // 创建增强的动画数字组件
    createEnhancedAnimatedNumber() {
        const originalComponent = AnimatedNumber;
        
        this.EnhancedAnimatedNumber = class extends originalComponent {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加性能监控
                this.addPerformanceMonitoring();
                
                // 添加自定义缓动
                this.addCustomEasing();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isAnimating: false,
                    animationId: null,
                    startTime: null,
                    endTime: null,
                    fromValue: 0,
                    toValue: 0,
                    currentProgress: 0,
                    easingFunction: 'linear',
                    frameCount: 0,
                    lastFrameTime: 0
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的动画启动
                this.enhancedStartAnimation = (from, to, duration, easing = 'linear') => {
                    // 取消之前的动画
                    this.cancelAnimation();
                    
                    // 设置动画参数
                    this.enhancedState.fromValue = from;
                    this.enhancedState.toValue = to;
                    this.enhancedState.startTime = Date.now();
                    this.enhancedState.endTime = this.enhancedState.startTime + duration;
                    this.enhancedState.easingFunction = easing;
                    this.enhancedState.isAnimating = true;
                    this.enhancedState.frameCount = 0;
                    this.enhancedState.lastFrameTime = this.enhancedState.startTime;
                    
                    // 记录动画开始
                    this.recordAnimationStart();
                    
                    // 启动动画循环
                    this.animationLoop();
                };
                
                // 动画循环
                this.animationLoop = () => {
                    if (!this.enhancedState.isAnimating) {
                        return;
                    }
                    
                    const currentTime = Date.now();
                    const progress = Math.min(
                        (currentTime - this.enhancedState.startTime) / 
                        (this.enhancedState.endTime - this.enhancedState.startTime),
                        1
                    );
                    
                    // 应用缓动函数
                    const easedProgress = this.applyEasing(progress);
                    this.enhancedState.currentProgress = easedProgress;
                    
                    // 计算当前值
                    const currentValue = this.enhancedState.fromValue + 
                        (this.enhancedState.toValue - this.enhancedState.fromValue) * easedProgress;
                    
                    // 更新显示值
                    this.state.value = currentValue;
                    
                    // 更新性能指标
                    this.updatePerformanceMetrics(currentTime);
                    
                    // 检查动画完成
                    if (progress >= 1) {
                        this.completeAnimation();
                    } else {
                        this.enhancedState.animationId = browser.requestAnimationFrame(this.animationLoop);
                    }
                };
                
                // 应用缓动函数
                this.applyEasing = (progress) => {
                    const easingFn = this.easingFunctions.get(this.enhancedState.easingFunction);
                    return easingFn ? easingFn(progress) : progress;
                };
                
                // 完成动画
                this.completeAnimation = () => {
                    this.enhancedState.isAnimating = false;
                    this.state.value = this.enhancedState.toValue;
                    this.recordAnimationComplete();
                };
                
                // 取消动画
                this.cancelAnimation = () => {
                    if (this.enhancedState.animationId) {
                        browser.cancelAnimationFrame(this.enhancedState.animationId);
                        this.enhancedState.animationId = null;
                    }
                    
                    if (this.enhancedState.isAnimating) {
                        this.enhancedState.isAnimating = false;
                        this.recordAnimationCancel();
                    }
                };
                
                // 增强的格式化
                this.enhancedFormat = (value, formatType = 'integer') => {
                    const options = this.formatOptions.get(formatType) || 
                                   this.formatOptions.get('integer');
                    
                    if (formatType === 'currency' && this.props.currency) {
                        return this.formatCurrency(value, this.props.currency);
                    }
                    
                    return this.formatInteger(value, options);
                };
                
                // 货币格式化
                this.formatCurrency = (value, currency) => {
                    if (typeof currency === 'object' && currency.symbol) {
                        const formatted = this.formatInteger(value, { 
                            humanReadable: true, 
                            decimals: currency.decimal_places || 2 
                        });
                        return currency.position === 'before' ? 
                            `${currency.symbol}${formatted}` : 
                            `${formatted}${currency.symbol}`;
                    }
                    
                    return this.formatInteger(value, { 
                        humanReadable: true, 
                        decimals: 2 
                    });
                };
                
                // 更新性能指标
                this.updatePerformanceMetrics = (currentTime) => {
                    this.enhancedState.frameCount++;
                    
                    if (this.enhancedState.lastFrameTime > 0) {
                        const frameDuration = currentTime - this.enhancedState.lastFrameTime;
                        const fps = 1000 / frameDuration;
                        
                        // 更新FPS统计
                        this.updateFPSStatistics(fps);
                        
                        // 检测帧丢失
                        if (fps < 30) {
                            this.animationStatistics.performanceMetrics.frameDrops++;
                        }
                    }
                    
                    this.enhancedState.lastFrameTime = currentTime;
                };
                
                // 更新FPS统计
                this.updateFPSStatistics = (fps) => {
                    const metrics = this.animationStatistics.performanceMetrics;
                    
                    if (fps > metrics.maxFPS) {
                        metrics.maxFPS = fps;
                    }
                    
                    if (fps < metrics.minFPS) {
                        metrics.minFPS = fps;
                    }
                    
                    // 计算平均FPS
                    metrics.averageFPS = (metrics.averageFPS + fps) / 2;
                };
                
                // 记录动画开始
                this.recordAnimationStart = () => {
                    this.animationStatistics.totalAnimations++;
                    this.animationStatistics.activeAnimations++;
                };
                
                // 记录动画完成
                this.recordAnimationComplete = () => {
                    this.animationStatistics.activeAnimations--;
                    this.animationStatistics.completedAnimations++;
                    
                    const duration = Date.now() - this.enhancedState.startTime;
                    this.animationStatistics.totalDuration += duration;
                    this.updateAverageDuration();
                };
                
                // 记录动画取消
                this.recordAnimationCancel = () => {
                    this.animationStatistics.activeAnimations--;
                    this.animationStatistics.cancelledAnimations++;
                };
                
                // 更新平均持续时间
                this.updateAverageDuration = () => {
                    const completed = this.animationStatistics.completedAnimations;
                    if (completed > 0) {
                        this.animationStatistics.averageDuration = 
                            this.animationStatistics.totalDuration / completed;
                    }
                };
                
                // 获取动画信息
                this.getAnimationInfo = () => {
                    return {
                        isAnimating: this.enhancedState.isAnimating,
                        progress: this.enhancedState.currentProgress,
                        fromValue: this.enhancedState.fromValue,
                        toValue: this.enhancedState.toValue,
                        currentValue: this.state.value,
                        frameCount: this.enhancedState.frameCount,
                        easingFunction: this.enhancedState.easingFunction,
                        elapsedTime: this.enhancedState.startTime ? 
                            Date.now() - this.enhancedState.startTime : 0
                    };
                };
            }
            
            addPerformanceMonitoring() {
                // 性能监控功能
                this.performanceMonitor = {
                    enabled: this.animatedNumberConfig.enablePerformanceMode,
                    getMetrics: () => this.animationStatistics.performanceMetrics,
                    getInfo: () => this.getAnimationInfo()
                };
            }
            
            addCustomEasing() {
                // 自定义缓动功能
                this.easingManager = {
                    enabled: this.animatedNumberConfig.enableCustomEasing,
                    functions: this.easingFunctions,
                    apply: (progress) => this.applyEasing(progress)
                };
            }
            
            // 重写原始方法
            format(value) {
                return this.enhancedFormat(value);
            }
        };
    }
    
    // 设置动画系统
    setupAnimationSystem() {
        this.animationSystemConfig = {
            enabled: this.animatedNumberConfig.enableAnimations,
            defaultDuration: this.animatedNumberConfig.defaultDuration,
            easingFunctions: this.easingFunctions
        };
    }
    
    // 设置格式化系统
    setupFormattingSystem() {
        this.formattingSystemConfig = {
            enabled: true,
            options: this.formatOptions,
            defaultFormat: 'integer'
        };
    }
    
    // 设置性能监控
    setupPerformanceMonitoring() {
        this.performanceMonitoringConfig = {
            enabled: this.animatedNumberConfig.enablePerformanceMode,
            statistics: this.animationStatistics
        };
    }
    
    // 创建动画数字组件
    createAnimatedNumber(props) {
        return new this.EnhancedAnimatedNumber(props);
    }
    
    // 注册缓动函数
    registerEasingFunction(name, fn) {
        this.easingFunctions.set(name, fn);
    }
    
    // 注册格式化选项
    registerFormatOption(name, options) {
        this.formatOptions.set(name, options);
    }
    
    // 获取动画统计
    getAnimationStatistics() {
        return {
            ...this.animationStatistics,
            completionRate: (this.animationStatistics.completedAnimations / 
                           Math.max(this.animationStatistics.totalAnimations, 1)) * 100,
            cancellationRate: (this.animationStatistics.cancelledAnimations / 
                             Math.max(this.animationStatistics.totalAnimations, 1)) * 100,
            averageFrameRate: this.animationStatistics.performanceMetrics.averageFPS
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理缓动函数
        this.easingFunctions.clear();
        
        // 清理格式化选项
        this.formatOptions.clear();
        
        // 重置统计
        this.animationStatistics = {
            totalAnimations: 0,
            activeAnimations: 0,
            completedAnimations: 0,
            cancelledAnimations: 0,
            averageDuration: 0,
            totalDuration: 0,
            performanceMetrics: {
                frameDrops: 0,
                averageFPS: 60,
                minFPS: 60,
                maxFPS: 60
            }
        };
    }
}

// 使用示例
const animatedNumberManager = new AnimatedNumberManager();

// 创建动画数字组件
const animatedNumber = animatedNumberManager.createAnimatedNumber({
    value: 1000,
    duration: 2000,
    currency: { symbol: '$', position: 'before', decimal_places: 2 },
    title: 'Revenue'
});

// 注册自定义缓动函数
animatedNumberManager.registerEasingFunction('bounce', (t) => {
    if (t < 1/2.75) {
        return 7.5625 * t * t;
    } else if (t < 2/2.75) {
        return 7.5625 * (t -= 1.5/2.75) * t + 0.75;
    } else if (t < 2.5/2.75) {
        return 7.5625 * (t -= 2.25/2.75) * t + 0.9375;
    } else {
        return 7.5625 * (t -= 2.625/2.75) * t + 0.984375;
    }
});

// 获取统计信息
const stats = animatedNumberManager.getAnimationStatistics();
console.log('Animated number statistics:', stats);
```

## 技术特点

### 1. 平滑动画
- **帧动画**: 使用requestAnimationFrame实现平滑动画
- **线性插值**: 在数值之间进行线性插值
- **时间控制**: 精确的时间控制和进度计算
- **性能优化**: 优化的动画性能

### 2. 数值格式化
- **人类可读**: 支持人类可读的数字格式
- **国际化**: 支持本地化数字格式
- **货币支持**: 支持货币格式显示
- **自定义格式**: 支持自定义格式选项

### 3. 动画控制
- **全局开关**: 全局动画启用/禁用控制
- **条件动画**: 基于条件的动画触发
- **动画清理**: 自动清理动画资源
- **性能保护**: 防止性能问题的保护机制

### 4. 组件设计
- **响应式**: 响应属性变化自动触发动画
- **插槽支持**: 支持前缀插槽自定义
- **样式类**: 支持自定义动画样式类
- **标题支持**: 支持标题属性

## 设计模式

### 1. 组件模式 (Component Pattern)
- **可重用组件**: 高度可重用的动画数字组件
- **属性驱动**: 通过属性控制动画行为
- **状态管理**: 管理动画状态

### 2. 策略模式 (Strategy Pattern)
- **格式化策略**: 不同的数字格式化策略
- **动画策略**: 不同的动画执行策略
- **缓动策略**: 不同的缓动函数策略

### 3. 观察者模式 (Observer Pattern)
- **属性观察**: 观察属性变化
- **状态观察**: 观察动画状态变化
- **生命周期观察**: 观察组件生命周期

### 4. 单例模式 (Singleton Pattern)
- **动画管理**: 统一的动画管理
- **资源共享**: 共享动画资源
- **配置管理**: 统一的配置管理

## 注意事项

1. **性能考虑**: 避免同时运行过多动画
2. **内存管理**: 及时清理动画资源
3. **用户体验**: 合理设置动画时长
4. **可访问性**: 考虑动画对可访问性的影响

## 扩展建议

1. **缓动函数**: 添加更多缓动函数
2. **动画类型**: 支持更多动画类型
3. **性能监控**: 增强性能监控功能
4. **声音效果**: 添加声音效果支持
5. **手势控制**: 支持手势控制动画

该动画数字组件为Odoo Web客户端提供了优雅的数字动画功能，通过平滑的动画效果和灵活的配置选项确保了数据展示的视觉吸引力和用户体验。

# ColumnProgress - 列进度组件

## 概述

`column_progress.js` 是 Odoo Web 客户端的列进度组件，负责在列视图中显示进度条和相关数据。该模块包含28行代码，是一个功能简洁的进度显示组件，专门用于在列表或看板视图中显示聚合数据的进度条，具备动画数字集成、点击交互、数据聚合显示等特性，是数据可视化的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/view_components/column_progress.js`
- **行数**: 28
- **模块**: `@web/views/view_components/column_progress`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                    // OWL框架
'@web/views/view_components/animated_number'   // 动画数字组件
```

## 核心功能

### 1. 组件定义

```javascript
const ColumnProgress = class ColumnProgress extends Component {
    static components = {
        AnimatedNumber,
    };
    static template = "web.ColumnProgress";
    static props = {
        aggregate: { type: Object },
        group: { type: Object },
        onBarClicked: { type: Function, optional: true },
        progressBar: { type: Object },
    };
    static defaultProps = {
        onBarClicked: () => {},
    };
}
```

**组件特性**:
- **动画数字**: 集成AnimatedNumber组件
- **聚合数据**: 接收聚合数据对象
- **分组数据**: 接收分组数据对象
- **进度条**: 接收进度条配置对象
- **点击回调**: 支持进度条点击事件
- **默认回调**: 提供默认的空回调函数

### 2. 点击处理

```javascript
async onBarClick(bar) {
    await this.props.onBarClicked(bar);
}
```

**点击处理功能**:
- **异步处理**: 支持异步点击处理
- **参数传递**: 传递进度条数据
- **事件委托**: 委托给父组件处理
- **错误安全**: 异步操作的错误安全

## 使用场景

### 1. 列进度组件管理器

```javascript
// 列进度组件管理器
class ColumnProgressManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置列进度组件配置
        this.columnProgressConfig = {
            enableAnimations: true,
            enableInteractions: true,
            enableTooltips: true,
            enableColorCoding: true,
            enableDataLabels: true,
            enableClickEvents: true,
            animationDuration: 1000,
            updateThreshold: 0.1
        };
        
        // 设置进度条样式
        this.progressBarStyles = new Map([
            ['default', {
                height: '20px',
                backgroundColor: '#e9ecef',
                borderRadius: '4px',
                color: '#007bff'
            }],
            ['success', {
                height: '20px',
                backgroundColor: '#e9ecef',
                borderRadius: '4px',
                color: '#28a745'
            }],
            ['warning', {
                height: '20px',
                backgroundColor: '#e9ecef',
                borderRadius: '4px',
                color: '#ffc107'
            }],
            ['danger', {
                height: '20px',
                backgroundColor: '#e9ecef',
                borderRadius: '4px',
                color: '#dc3545'
            }]
        ]);
        
        // 设置聚合类型
        this.aggregateTypes = new Map([
            ['sum', {
                name: 'Sum',
                calculate: (values) => values.reduce((a, b) => a + b, 0),
                format: 'number'
            }],
            ['avg', {
                name: 'Average',
                calculate: (values) => values.reduce((a, b) => a + b, 0) / values.length,
                format: 'decimal'
            }],
            ['count', {
                name: 'Count',
                calculate: (values) => values.length,
                format: 'integer'
            }],
            ['max', {
                name: 'Maximum',
                calculate: (values) => Math.max(...values),
                format: 'number'
            }],
            ['min', {
                name: 'Minimum',
                calculate: (values) => Math.min(...values),
                format: 'number'
            }]
        ]);
        
        // 设置列进度统计
        this.columnProgressStatistics = {
            totalColumnProgress: 0,
            totalClicks: 0,
            clicksByBar: new Map(),
            averageProgress: 0,
            maxProgress: 0,
            minProgress: 100,
            progressDistribution: new Map(),
            mostClickedBar: null
        };
        
        this.initializeColumnProgressSystem();
    }
    
    // 初始化列进度系统
    initializeColumnProgressSystem() {
        // 创建增强的列进度组件
        this.createEnhancedColumnProgress();
        
        // 设置进度条系统
        this.setupProgressBarSystem();
        
        // 设置聚合系统
        this.setupAggregateSystem();
        
        // 设置交互系统
        this.setupInteractionSystem();
    }
    
    // 创建增强的列进度组件
    createEnhancedColumnProgress() {
        const originalComponent = ColumnProgress;
        
        this.EnhancedColumnProgress = class extends originalComponent {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加数据处理
                this.addDataProcessing();
                
                // 添加交互功能
                this.addInteractionFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isLoading: false,
                    hasError: false,
                    errorMessage: '',
                    progressValue: 0,
                    maxValue: 100,
                    animationProgress: 0,
                    lastUpdateTime: null,
                    clickCount: 0
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 计算进度值
                this.calculateProgress = () => {
                    const { aggregate, progressBar } = this.props;
                    
                    if (!aggregate || !progressBar) {
                        return 0;
                    }
                    
                    const currentValue = aggregate.value || 0;
                    const maxValue = progressBar.max_value || 100;
                    const minValue = progressBar.min_value || 0;
                    
                    const progress = ((currentValue - minValue) / (maxValue - minValue)) * 100;
                    return Math.max(0, Math.min(100, progress));
                };
                
                // 获取进度条样式
                this.getProgressBarStyle = () => {
                    const progress = this.calculateProgress();
                    const { progressBar } = this.props;
                    
                    let styleType = 'default';
                    
                    if (progressBar.color_field && this.props.group) {
                        const colorValue = this.props.group[progressBar.color_field];
                        styleType = this.mapColorToStyle(colorValue);
                    } else if (progressBar.thresholds) {
                        styleType = this.getStyleByThreshold(progress, progressBar.thresholds);
                    }
                    
                    const baseStyle = this.progressBarStyles.get(styleType) || 
                                     this.progressBarStyles.get('default');
                    
                    return {
                        ...baseStyle,
                        width: `${progress}%`
                    };
                };
                
                // 映射颜色到样式
                this.mapColorToStyle = (colorValue) => {
                    const colorMap = {
                        'red': 'danger',
                        'yellow': 'warning',
                        'green': 'success',
                        'blue': 'default'
                    };
                    
                    return colorMap[colorValue] || 'default';
                };
                
                // 根据阈值获取样式
                this.getStyleByThreshold = (progress, thresholds) => {
                    for (const threshold of thresholds) {
                        if (progress <= threshold.value) {
                            return threshold.style || 'default';
                        }
                    }
                    return 'default';
                };
                
                // 格式化聚合值
                this.formatAggregateValue = () => {
                    const { aggregate } = this.props;
                    
                    if (!aggregate) {
                        return '0';
                    }
                    
                    const value = aggregate.value || 0;
                    const formatType = aggregate.format || 'number';
                    
                    switch (formatType) {
                        case 'currency':
                            return this.formatCurrency(value, aggregate.currency);
                        case 'percentage':
                            return `${value.toFixed(1)}%`;
                        case 'decimal':
                            return value.toFixed(2);
                        case 'integer':
                            return Math.round(value).toString();
                        default:
                            return value.toString();
                    }
                };
                
                // 格式化货币
                this.formatCurrency = (value, currency) => {
                    if (currency && currency.symbol) {
                        const formatted = value.toFixed(currency.decimal_places || 2);
                        return currency.position === 'before' ? 
                            `${currency.symbol}${formatted}` : 
                            `${formatted}${currency.symbol}`;
                    }
                    return value.toFixed(2);
                };
                
                // 增强的点击处理
                this.enhancedOnBarClick = async (bar) => {
                    try {
                        // 记录点击
                        this.recordClick(bar);
                        
                        // 设置加载状态
                        this.enhancedState.isLoading = true;
                        
                        // 执行原始点击处理
                        await this.props.onBarClicked(bar);
                        
                        // 更新统计
                        this.updateClickStatistics(bar);
                        
                    } catch (error) {
                        this.handleClickError(error);
                    } finally {
                        this.enhancedState.isLoading = false;
                    }
                };
                
                // 获取工具提示内容
                this.getTooltipContent = () => {
                    const { aggregate, group, progressBar } = this.props;
                    const progress = this.calculateProgress();
                    
                    const content = [];
                    
                    if (group && group.display_name) {
                        content.push(`Group: ${group.display_name}`);
                    }
                    
                    if (aggregate) {
                        content.push(`Value: ${this.formatAggregateValue()}`);
                        content.push(`Progress: ${progress.toFixed(1)}%`);
                    }
                    
                    if (progressBar && progressBar.description) {
                        content.push(`Description: ${progressBar.description}`);
                    }
                    
                    return content.join('\n');
                };
                
                // 获取进度信息
                this.getProgressInfo = () => {
                    return {
                        progress: this.calculateProgress(),
                        formattedValue: this.formatAggregateValue(),
                        style: this.getProgressBarStyle(),
                        tooltip: this.getTooltipContent(),
                        isLoading: this.enhancedState.isLoading,
                        hasError: this.enhancedState.hasError,
                        clickCount: this.enhancedState.clickCount,
                        lastUpdateTime: this.enhancedState.lastUpdateTime
                    };
                };
                
                // 记录点击
                this.recordClick = (bar) => {
                    this.enhancedState.clickCount++;
                    this.enhancedState.lastUpdateTime = new Date();
                    
                    // 记录全局统计
                    this.columnProgressStatistics.totalClicks++;
                };
                
                // 更新点击统计
                this.updateClickStatistics = (bar) => {
                    const barId = bar.id || 'unknown';
                    const count = this.columnProgressStatistics.clicksByBar.get(barId) || 0;
                    this.columnProgressStatistics.clicksByBar.set(barId, count + 1);
                    
                    this.updateMostClickedBar();
                };
                
                // 更新最多点击的进度条
                this.updateMostClickedBar = () => {
                    let maxClicks = 0;
                    let mostClicked = null;
                    
                    for (const [barId, clicks] of this.columnProgressStatistics.clicksByBar.entries()) {
                        if (clicks > maxClicks) {
                            maxClicks = clicks;
                            mostClicked = barId;
                        }
                    }
                    
                    this.columnProgressStatistics.mostClickedBar = mostClicked;
                };
                
                // 处理点击错误
                this.handleClickError = (error) => {
                    console.error('Column progress click error:', error);
                    this.enhancedState.hasError = true;
                    this.enhancedState.errorMessage = error.message;
                };
                
                // 更新进度统计
                this.updateProgressStatistics = () => {
                    const progress = this.calculateProgress();
                    
                    // 更新最大最小值
                    if (progress > this.columnProgressStatistics.maxProgress) {
                        this.columnProgressStatistics.maxProgress = progress;
                    }
                    
                    if (progress < this.columnProgressStatistics.minProgress) {
                        this.columnProgressStatistics.minProgress = progress;
                    }
                    
                    // 更新分布
                    const bucket = Math.floor(progress / 10) * 10;
                    const count = this.columnProgressStatistics.progressDistribution.get(bucket) || 0;
                    this.columnProgressStatistics.progressDistribution.set(bucket, count + 1);
                };
            }
            
            addDataProcessing() {
                // 数据处理功能
                this.dataProcessor = {
                    calculateProgress: () => this.calculateProgress(),
                    formatValue: () => this.formatAggregateValue(),
                    getInfo: () => this.getProgressInfo()
                };
            }
            
            addInteractionFeatures() {
                // 交互功能
                this.interactionManager = {
                    enabled: this.columnProgressConfig.enableInteractions,
                    onClick: (bar) => this.enhancedOnBarClick(bar),
                    getTooltip: () => this.getTooltipContent()
                };
            }
            
            // 重写原始方法
            async onBarClick(bar) {
                return await this.enhancedOnBarClick(bar);
            }
        };
    }
    
    // 设置进度条系统
    setupProgressBarSystem() {
        this.progressBarSystemConfig = {
            enabled: true,
            styles: this.progressBarStyles,
            animationDuration: this.columnProgressConfig.animationDuration
        };
    }
    
    // 设置聚合系统
    setupAggregateSystem() {
        this.aggregateSystemConfig = {
            enabled: true,
            types: this.aggregateTypes,
            updateThreshold: this.columnProgressConfig.updateThreshold
        };
    }
    
    // 设置交互系统
    setupInteractionSystem() {
        this.interactionSystemConfig = {
            enabled: this.columnProgressConfig.enableInteractions,
            enableTooltips: this.columnProgressConfig.enableTooltips,
            enableClickEvents: this.columnProgressConfig.enableClickEvents
        };
    }
    
    // 创建列进度组件
    createColumnProgress(props) {
        const component = new this.EnhancedColumnProgress(props);
        this.columnProgressStatistics.totalColumnProgress++;
        return component;
    }
    
    // 注册进度条样式
    registerProgressBarStyle(name, style) {
        this.progressBarStyles.set(name, style);
    }
    
    // 注册聚合类型
    registerAggregateType(name, config) {
        this.aggregateTypes.set(name, config);
    }
    
    // 获取列进度统计
    getColumnProgressStatistics() {
        return {
            ...this.columnProgressStatistics,
            clickRate: this.columnProgressStatistics.totalClicks / 
                      Math.max(this.columnProgressStatistics.totalColumnProgress, 1),
            progressRange: this.columnProgressStatistics.maxProgress - 
                          this.columnProgressStatistics.minProgress,
            distributionVariety: this.columnProgressStatistics.progressDistribution.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理样式
        this.progressBarStyles.clear();
        
        // 清理聚合类型
        this.aggregateTypes.clear();
        
        // 清理统计
        this.columnProgressStatistics.clicksByBar.clear();
        this.columnProgressStatistics.progressDistribution.clear();
        
        // 重置统计
        this.columnProgressStatistics = {
            totalColumnProgress: 0,
            totalClicks: 0,
            clicksByBar: new Map(),
            averageProgress: 0,
            maxProgress: 0,
            minProgress: 100,
            progressDistribution: new Map(),
            mostClickedBar: null
        };
    }
}

// 使用示例
const columnProgressManager = new ColumnProgressManager();

// 创建列进度组件
const columnProgress = columnProgressManager.createColumnProgress({
    aggregate: {
        value: 75,
        format: 'percentage'
    },
    group: {
        display_name: 'Sales Team A'
    },
    progressBar: {
        max_value: 100,
        min_value: 0,
        color_field: 'status'
    },
    onBarClicked: async (bar) => {
        console.log('Progress bar clicked:', bar);
    }
});

// 注册自定义样式
columnProgressManager.registerProgressBarStyle('custom', {
    height: '25px',
    backgroundColor: '#f8f9fa',
    borderRadius: '8px',
    color: '#6f42c1'
});

// 获取统计信息
const stats = columnProgressManager.getColumnProgressStatistics();
console.log('Column progress statistics:', stats);
```

## 技术特点

### 1. 组件集成
- **动画数字**: 集成AnimatedNumber组件
- **模板驱动**: 使用模板驱动的渲染
- **属性配置**: 通过属性配置组件行为
- **事件处理**: 支持事件处理和回调

### 2. 数据处理
- **聚合数据**: 处理聚合数据显示
- **分组数据**: 处理分组数据展示
- **进度计算**: 自动计算进度百分比
- **格式化**: 支持多种数据格式化

### 3. 交互功能
- **点击事件**: 支持进度条点击事件
- **异步处理**: 支持异步事件处理
- **回调机制**: 灵活的回调机制
- **错误处理**: 完整的错误处理

### 4. 简洁设计
- **最小代码**: 极简的代码实现
- **专注功能**: 专注于进度显示
- **易于扩展**: 易于扩展和定制
- **高效渲染**: 高效的渲染性能

## 设计模式

### 1. 组合模式 (Composition Pattern)
- **组件组合**: 组合AnimatedNumber组件
- **功能组合**: 组合进度显示和数字动画
- **模板组合**: 组合模板元素

### 2. 策略模式 (Strategy Pattern)
- **格式化策略**: 不同的数据格式化策略
- **样式策略**: 不同的进度条样式策略
- **交互策略**: 不同的交互处理策略

### 3. 观察者模式 (Observer Pattern)
- **数据观察**: 观察数据变化
- **事件观察**: 观察用户交互事件
- **状态观察**: 观察组件状态变化

### 4. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 装饰基础进度显示功能
- **样式装饰**: 装饰进度条样式
- **交互装饰**: 装饰交互行为

## 注意事项

1. **数据验证**: 验证聚合数据的有效性
2. **性能考虑**: 避免频繁的进度计算
3. **用户体验**: 提供清晰的进度指示
4. **错误处理**: 正确处理数据错误

## 扩展建议

1. **动画效果**: 添加进度条动画效果
2. **主题支持**: 支持多主题样式
3. **工具提示**: 增强工具提示功能
4. **数据导出**: 支持进度数据导出
5. **实时更新**: 支持实时数据更新

该列进度组件为Odoo Web客户端提供了简洁高效的进度显示功能，通过集成动画数字组件和灵活的配置选项确保了数据可视化的清晰性和交互性。

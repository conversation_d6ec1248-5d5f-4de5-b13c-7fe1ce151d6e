# ColorPickerField - 颜色选择器字段

## 概述

`color_picker_field.js` 是 Odoo Web 客户端的颜色选择器字段组件，负责提供颜色选择功能。该模块包含42行代码，是一个交互式颜色选择组件，专门用于通过颜色列表选择颜色索引值，具备颜色切换、展开控制、列表显示等特性，是颜色选择和管理的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/color_picker/color_picker_field.js`
- **行数**: 42
- **模块**: `@web/views/fields/color_picker/color_picker_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/colorlist/colorlist'         // 颜色列表组件
'@web/core/registry'                    // 注册表
'@web/views/fields/standard_field_props' // 标准字段属性
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const ColorPickerField = class ColorPickerField extends Component {
    static template = "web.ColorPickerField";
    static components = {
        ColorList,
    };
    static props = {
        ...standardFieldProps,
        canToggle: { type: Boolean },
    };

    static RECORD_COLORS = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11];
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **颜色列表**: 集成ColorList组件
- **切换控制**: 支持canToggle属性控制展开
- **颜色常量**: 定义12种记录颜色索引

### 2. 展开状态

```javascript
get isExpanded() {
    return !this.props.canToggle && !this.props.readonly;
}
```

**展开功能**:
- **条件展开**: 根据canToggle和readonly属性决定是否展开
- **自动展开**: 不可切换且非只读时自动展开
- **状态控制**: 智能的展开状态控制
- **用户体验**: 优化用户交互体验

### 3. 颜色切换

```javascript
switchColor(colorIndex) {
    this.props.record.update({ [this.props.name]: colorIndex });
}
```

**切换功能**:
- **索引更新**: 更新记录中的颜色索引值
- **即时更新**: 立即更新记录数据
- **简洁实现**: 简洁的颜色切换逻辑
- **直接操作**: 直接操作记录数据

### 4. 字段注册

```javascript
const colorPickerField = {
    component: ColorPickerField,
    supportedTypes: ["integer"],
    extractProps: ({ viewType }) => ({
        canToggle: viewType !== "list",
    }),
};

registry.category("fields").add("color_picker", colorPickerField);
```

**注册功能**:
- **组件注册**: 注册颜色选择器字段组件
- **类型支持**: 仅支持integer类型
- **视图适配**: 根据视图类型设置canToggle属性
- **列表优化**: 列表视图中禁用切换功能

## 使用场景

### 1. 颜色选择器字段管理器

```javascript
// 颜色选择器字段管理器
class ColorPickerFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置颜色选择器配置
        this.pickerConfig = {
            enableCustomColors: true,
            enableColorPreview: true,
            enableColorHistory: true,
            enableColorPalettes: true,
            maxHistorySize: 20,
            defaultColorSet: 'standard',
            enableColorNames: true,
            enableColorSearch: true
        };
        
        // 设置标准颜色集
        this.standardColors = new Map([
            [0, { hex: '#FFFFFF', name: 'White', category: 'basic' }],
            [1, { hex: '#FF0000', name: 'Red', category: 'primary' }],
            [2, { hex: '#00FF00', name: 'Green', category: 'primary' }],
            [3, { hex: '#0000FF', name: 'Blue', category: 'primary' }],
            [4, { hex: '#FFFF00', name: 'Yellow', category: 'secondary' }],
            [5, { hex: '#FF00FF', name: 'Magenta', category: 'secondary' }],
            [6, { hex: '#00FFFF', name: 'Cyan', category: 'secondary' }],
            [7, { hex: '#FFA500', name: 'Orange', category: 'warm' }],
            [8, { hex: '#800080', name: 'Purple', category: 'cool' }],
            [9, { hex: '#FFC0CB', name: 'Pink', category: 'warm' }],
            [10, { hex: '#808080', name: 'Gray', category: 'neutral' }],
            [11, { hex: '#000000', name: 'Black', category: 'basic' }]
        ]);
        
        // 设置颜色调色板
        this.colorPalettes = new Map([
            ['standard', Array.from(this.standardColors.keys())],
            ['warm', [1, 4, 7, 9]], // Red, Yellow, Orange, Pink
            ['cool', [2, 3, 6, 8]], // Green, Blue, Cyan, Purple
            ['neutral', [0, 10, 11]], // White, Gray, Black
            ['primary', [1, 2, 3]], // Red, Green, Blue
            ['secondary', [4, 5, 6]] // Yellow, Magenta, Cyan
        ]);
        
        // 设置颜色统计
        this.pickerStatistics = {
            totalSelections: 0,
            colorUsage: new Map(),
            paletteUsage: new Map(),
            averageSelectionTime: 0,
            popularColors: new Map()
        };
        
        // 设置颜色历史
        this.colorHistory = [];
        
        this.initializePickerSystem();
    }
    
    // 初始化选择器系统
    initializePickerSystem() {
        // 创建增强的颜色选择器字段
        this.createEnhancedColorPickerField();
        
        // 设置颜色管理
        this.setupColorManagement();
        
        // 设置调色板管理
        this.setupPaletteManagement();
        
        // 设置历史管理
        this.setupHistoryManagement();
    }
    
    // 创建增强的颜色选择器字段
    createEnhancedColorPickerField() {
        const originalField = ColorPickerField;
        
        this.EnhancedColorPickerField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加预览功能
                this.addPreviewFeatures();
                
                // 添加搜索功能
                this.addSearchFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    selectedColorIndex: null,
                    hoveredColorIndex: null,
                    currentPalette: 'standard',
                    searchQuery: '',
                    filteredColors: [],
                    previewColor: null,
                    isSearching: false
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的颜色切换
                this.enhancedSwitchColor = async (colorIndex) => {
                    const startTime = performance.now();
                    
                    try {
                        const oldIndex = this.props.record.data[this.props.name];
                        
                        // 验证颜色索引
                        if (!this.isValidColorIndex(colorIndex)) {
                            throw new Error(`Invalid color index: ${colorIndex}`);
                        }
                        
                        // 记录选择历史
                        this.recordColorSelection(oldIndex, colorIndex);
                        
                        // 执行原始切换
                        this.switchColor(colorIndex);
                        
                        // 更新状态
                        this.enhancedState.selectedColorIndex = colorIndex;
                        
                        // 添加到历史
                        this.addToHistory(colorIndex);
                        
                        // 记录统计
                        this.recordSelectionStatistics(colorIndex);
                        
                        // 触发预览更新
                        this.updatePreview(colorIndex);
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordSelectionTime(endTime - startTime);
                        
                    } catch (error) {
                        this.handleSelectionError(error);
                    }
                };
                
                // 验证颜色索引
                this.isValidColorIndex = (index) => {
                    return Number.isInteger(index) && 
                           index >= 0 && 
                           index < this.RECORD_COLORS.length;
                };
                
                // 获取颜色信息
                this.getColorInfo = (colorIndex) => {
                    return this.standardColors.get(colorIndex) || {
                        hex: '#CCCCCC',
                        name: 'Unknown',
                        category: 'unknown'
                    };
                };
                
                // 获取当前颜色
                this.getCurrentColor = () => {
                    const currentIndex = this.props.record.data[this.props.name];
                    return this.getColorInfo(currentIndex);
                };
                
                // 设置调色板
                this.setPalette = (paletteName) => {
                    if (this.colorPalettes.has(paletteName)) {
                        this.enhancedState.currentPalette = paletteName;
                        this.updateFilteredColors();
                    }
                };
                
                // 更新过滤颜色
                this.updateFilteredColors = () => {
                    const paletteColors = this.colorPalettes.get(this.enhancedState.currentPalette) || 
                                         this.RECORD_COLORS;
                    
                    if (this.enhancedState.searchQuery) {
                        this.enhancedState.filteredColors = paletteColors.filter(index => {
                            const colorInfo = this.getColorInfo(index);
                            return colorInfo.name.toLowerCase().includes(
                                this.enhancedState.searchQuery.toLowerCase()
                            );
                        });
                    } else {
                        this.enhancedState.filteredColors = paletteColors;
                    }
                };
                
                // 搜索颜色
                this.searchColors = (query) => {
                    this.enhancedState.searchQuery = query;
                    this.enhancedState.isSearching = Boolean(query);
                    this.updateFilteredColors();
                };
                
                // 清除搜索
                this.clearSearch = () => {
                    this.enhancedState.searchQuery = '';
                    this.enhancedState.isSearching = false;
                    this.updateFilteredColors();
                };
                
                // 颜色悬停
                this.onColorHover = (colorIndex) => {
                    this.enhancedState.hoveredColorIndex = colorIndex;
                    this.updatePreview(colorIndex);
                };
                
                // 颜色离开
                this.onColorLeave = () => {
                    this.enhancedState.hoveredColorIndex = null;
                    this.updatePreview(this.enhancedState.selectedColorIndex);
                };
                
                // 更新预览
                this.updatePreview = (colorIndex) => {
                    if (this.pickerConfig.enableColorPreview && colorIndex !== null) {
                        this.enhancedState.previewColor = this.getColorInfo(colorIndex);
                    }
                };
                
                // 添加到历史
                this.addToHistory = (colorIndex) => {
                    if (!this.pickerConfig.enableColorHistory) return;
                    
                    // 移除重复项
                    const existingIndex = this.colorHistory.indexOf(colorIndex);
                    if (existingIndex > -1) {
                        this.colorHistory.splice(existingIndex, 1);
                    }
                    
                    // 添加到开头
                    this.colorHistory.unshift(colorIndex);
                    
                    // 限制历史大小
                    if (this.colorHistory.length > this.pickerConfig.maxHistorySize) {
                        this.colorHistory.pop();
                    }
                };
                
                // 获取颜色历史
                this.getColorHistory = () => {
                    return this.colorHistory.map(index => ({
                        index: index,
                        ...this.getColorInfo(index)
                    }));
                };
                
                // 清除历史
                this.clearHistory = () => {
                    this.colorHistory = [];
                };
                
                // 获取可用调色板
                this.getAvailablePalettes = () => {
                    return Array.from(this.colorPalettes.keys());
                };
                
                // 获取调色板颜色
                this.getPaletteColors = (paletteName) => {
                    const colorIndices = this.colorPalettes.get(paletteName) || [];
                    return colorIndices.map(index => ({
                        index: index,
                        ...this.getColorInfo(index)
                    }));
                };
                
                // 创建自定义调色板
                this.createCustomPalette = (name, colorIndices) => {
                    const validIndices = colorIndices.filter(index => this.isValidColorIndex(index));
                    this.colorPalettes.set(name, validIndices);
                };
                
                // 获取颜色建议
                this.getColorSuggestions = (baseColorIndex) => {
                    const baseColor = this.getColorInfo(baseColorIndex);
                    const suggestions = [];
                    
                    // 同类别颜色
                    for (const [index, colorInfo] of this.standardColors) {
                        if (colorInfo.category === baseColor.category && index !== baseColorIndex) {
                            suggestions.push({ index, ...colorInfo, reason: 'Same category' });
                        }
                    }
                    
                    return suggestions;
                };
                
                // 记录选择历史
                this.recordColorSelection = (oldIndex, newIndex) => {
                    console.log('Color selection:', { from: oldIndex, to: newIndex });
                };
                
                // 记录统计
                this.recordSelectionStatistics = (colorIndex) => {
                    this.pickerStatistics.totalSelections++;
                    
                    // 记录颜色使用
                    const currentUsage = this.pickerStatistics.colorUsage.get(colorIndex) || 0;
                    this.pickerStatistics.colorUsage.set(colorIndex, currentUsage + 1);
                    
                    // 记录调色板使用
                    const paletteUsage = this.pickerStatistics.paletteUsage.get(this.enhancedState.currentPalette) || 0;
                    this.pickerStatistics.paletteUsage.set(this.enhancedState.currentPalette, paletteUsage + 1);
                    
                    // 更新热门颜色
                    const popularCount = this.pickerStatistics.popularColors.get(colorIndex) || 0;
                    this.pickerStatistics.popularColors.set(colorIndex, popularCount + 1);
                };
                
                // 处理选择错误
                this.handleSelectionError = (error) => {
                    console.error('Color selection error:', error);
                };
                
                // 记录选择时间
                this.recordSelectionTime = (duration) => {
                    this.pickerStatistics.averageSelectionTime = 
                        (this.pickerStatistics.averageSelectionTime + duration) / 2;
                };
            }
            
            addPreviewFeatures() {
                // 预览功能
                this.previewManager = {
                    enabled: this.pickerConfig.enableColorPreview,
                    showHex: true,
                    showName: true,
                    showCategory: true
                };
            }
            
            addSearchFeatures() {
                // 搜索功能
                this.searchManager = {
                    enabled: this.pickerConfig.enableColorSearch,
                    searchByName: true,
                    searchByCategory: true,
                    searchByHex: false
                };
            }
            
            // 重写原始方法
            switchColor(colorIndex) {
                return this.enhancedSwitchColor(colorIndex);
            }
        };
    }
    
    // 设置颜色管理
    setupColorManagement() {
        this.colorManagementConfig = {
            enableCustomColors: this.pickerConfig.enableCustomColors,
            standardColorCount: this.standardColors.size
        };
    }
    
    // 设置调色板管理
    setupPaletteManagement() {
        this.paletteManagementConfig = {
            enableCustomPalettes: true,
            defaultPalette: this.pickerConfig.defaultColorSet,
            paletteCount: this.colorPalettes.size
        };
    }
    
    // 设置历史管理
    setupHistoryManagement() {
        this.historyManagementConfig = {
            enabled: this.pickerConfig.enableColorHistory,
            maxSize: this.pickerConfig.maxHistorySize,
            enablePersistence: false
        };
    }
    
    // 创建颜色选择器字段
    createColorPickerField(props) {
        return new this.EnhancedColorPickerField(props);
    }
    
    // 注册自定义颜色
    registerCustomColor(index, hex, name, category = 'custom') {
        this.standardColors.set(index, { hex, name, category });
    }
    
    // 注册调色板
    registerPalette(name, colorIndices) {
        this.colorPalettes.set(name, colorIndices);
    }
    
    // 批量设置颜色
    batchSetColors(fields, colorIndex) {
        const results = [];
        
        for (const field of fields) {
            try {
                field.enhancedSwitchColor(colorIndex);
                results.push({ field, success: true, colorIndex });
            } catch (error) {
                results.push({ field, success: false, error });
            }
        }
        
        return results;
    }
    
    // 获取热门颜色
    getPopularColors(limit = 5) {
        const sorted = Array.from(this.pickerStatistics.popularColors.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, limit);
        
        return sorted.map(([index, count]) => ({
            index,
            count,
            ...this.standardColors.get(index)
        }));
    }
    
    // 获取选择器统计
    getPickerStatistics() {
        return {
            ...this.pickerStatistics,
            colorCount: this.standardColors.size,
            paletteCount: this.colorPalettes.size,
            historySize: this.colorHistory.length
        };
    }
    
    // 导出颜色配置
    exportColorConfig() {
        return {
            colors: Object.fromEntries(this.standardColors),
            palettes: Object.fromEntries(this.colorPalettes),
            history: this.colorHistory
        };
    }
    
    // 导入颜色配置
    importColorConfig(config) {
        if (config.colors) {
            this.standardColors = new Map(Object.entries(config.colors));
        }
        if (config.palettes) {
            this.colorPalettes = new Map(Object.entries(config.palettes));
        }
        if (config.history) {
            this.colorHistory = config.history;
        }
    }
    
    // 销毁管理器
    destroy() {
        // 清理配置
        this.standardColors.clear();
        this.colorPalettes.clear();
        this.colorHistory = [];
        
        // 重置统计
        this.pickerStatistics = {
            totalSelections: 0,
            colorUsage: new Map(),
            paletteUsage: new Map(),
            averageSelectionTime: 0,
            popularColors: new Map()
        };
    }
}

// 使用示例
const pickerManager = new ColorPickerFieldManager();

// 创建颜色选择器字段
const pickerField = pickerManager.createColorPickerField({
    name: 'priority_color',
    record: {
        data: { priority_color: 1 },
        fields: { priority_color: { type: 'integer' } }
    },
    canToggle: true
});

// 注册自定义颜色
pickerManager.registerCustomColor(12, '#FF5733', 'Brand Orange', 'brand');

// 创建自定义调色板
pickerField.createCustomPalette('brand', [1, 7, 12]);

// 获取统计信息
const stats = pickerManager.getPickerStatistics();
console.log('Color picker statistics:', stats);
```

## 技术特点

### 1. 交互性强
- **颜色选择**: 直观的颜色选择界面
- **即时切换**: 即时的颜色切换功能
- **视觉反馈**: 清晰的视觉反馈
- **用户友好**: 用户友好的交互设计

### 2. 功能完整
- **颜色列表**: 集成ColorList组件
- **展开控制**: 智能的展开状态控制
- **视图适配**: 根据视图类型调整行为
- **索引管理**: 基于索引的颜色管理

### 3. 配置灵活
- **切换控制**: 可配置的切换功能
- **视图优化**: 针对不同视图的优化
- **属性提取**: 智能的属性提取
- **类型支持**: 支持integer类型

### 4. 性能优化
- **简洁实现**: 简洁高效的实现
- **组件复用**: 复用ColorList组件
- **状态管理**: 高效的状态管理
- **即时更新**: 即时的数据更新

## 设计模式

### 1. 组件模式 (Component Pattern)
- **UI组件**: 封装颜色选择UI
- **功能组件**: 集成颜色列表组件
- **状态管理**: 管理选择状态

### 2. 策略模式 (Strategy Pattern)
- **展开策略**: 不同的展开控制策略
- **选择策略**: 不同的颜色选择策略
- **显示策略**: 不同的显示策略

### 3. 工厂模式 (Factory Pattern)
- **组件工厂**: 创建颜色选择器组件
- **颜色工厂**: 创建颜色对象
- **配置工厂**: 创建配置对象

### 4. 观察者模式 (Observer Pattern)
- **选择观察**: 观察颜色选择变化
- **状态观察**: 观察组件状态变化
- **数据观察**: 观察记录数据变化

## 注意事项

1. **索引范围**: 确保颜色索引在有效范围内
2. **性能考虑**: 避免频繁的颜色切换
3. **用户体验**: 提供清晰的颜色选择反馈
4. **视图适配**: 确保在不同视图中的正确显示

## 扩展建议

1. **自定义颜色**: 支持自定义颜色添加
2. **颜色分组**: 支持颜色分组管理
3. **搜索功能**: 添加颜色搜索功能
4. **历史记录**: 添加颜色选择历史
5. **主题支持**: 支持多种颜色主题

该颜色选择器字段为Odoo Web客户端提供了直观的颜色选择功能，通过集成ColorList组件和智能的状态管理确保了良好的用户体验和高效的颜色管理。

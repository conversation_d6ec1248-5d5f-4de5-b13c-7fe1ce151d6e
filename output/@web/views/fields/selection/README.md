# Selection Fields - 选择字段模块

## 概述

Selection Fields 模块是 Odoo Web 客户端中专门处理选择字段的组件集合。该模块提供了两种不同的选择字段实现，分别适用于不同的使用场景，具备下拉选择、搜索过滤、选项管理、验证机制、本地化支持等特性，是用户界面中选择操作的核心组件。

## 模块结构

```
selection/
├── README.md                          # 模块说明文档
├── selection_field.js                 # 基础选择字段组件
├── selection_field.md                 # 基础组件学习资料
├── filterable_selection_field.js      # 可过滤选择字段组件
└── filterable_selection_field.md      # 过滤组件学习资料
```

## 组件列表

### 1. SelectionField (selection_field.js)
- **功能**: 基础的选择字段组件
- **行数**: 约150行代码
- **特性**: 
  - 下拉选择界面
  - 选项管理
  - 值验证
  - 键盘导航
  - 本地化支持
  - 自定义样式
- **适用场景**: 表单视图、详情视图等需要从预定义选项中选择的场景

### 2. FilterableSelectionField (filterable_selection_field.js)
- **功能**: 可过滤的选择字段组件
- **行数**: 约200行代码
- **特性**:
  - 实时搜索过滤
  - 大量选项支持
  - 智能匹配
  - 快速定位
  - 历史记录
- **适用场景**: 选项数量较多，需要搜索过滤功能的选择场景

## 核心特性

### 1. 选择交互
- **下拉菜单**: 直观的下拉选择界面
- **键盘导航**: 完整的键盘操作支持
- **鼠标操作**: 流畅的鼠标交互
- **触摸支持**: 移动设备触摸操作

### 2. 搜索过滤
- **实时搜索**: 输入即时过滤选项
- **模糊匹配**: 支持模糊搜索匹配
- **高亮显示**: 搜索结果高亮显示
- **快速清除**: 一键清除搜索条件

### 3. 选项管理
- **动态选项**: 支持动态加载选项
- **选项分组**: 支持选项分组显示
- **选项排序**: 自定义选项排序
- **选项禁用**: 支持禁用特定选项

### 4. 验证机制
- **必填验证**: 必填字段验证
- **选项验证**: 验证选择的有效性
- **自定义验证**: 支持自定义验证规则
- **实时验证**: 实时验证用户选择

## 技术架构

### 1. 组件继承关系
```
Component (OWL)
├── SelectionField
└── FilterableSelectionField
```

### 2. 依赖关系
```javascript
// 共同依赖
'@odoo/owl'                            // OWL框架
'@web/core/registry'                   // 注册表
'@web/core/l10n/translation'           // 翻译服务
'@web/views/fields/standard_field_props' // 标准字段属性

// 特定依赖
'@web/core/dropdown/dropdown'          // 下拉菜单
'@web/core/dropdown/dropdown_item'     // 下拉菜单项
'@web/views/fields/formatters'         // 字段格式化器
'@web/core/utils/strings'              // 字符串工具
```

### 3. 数据流
```
选项加载 → 用户搜索 → 选项过滤 → 用户选择 → 值验证 → 数据更新
```

## 使用示例

### 1. 基础选择字段
```xml
<field name="state" widget="selection"/>
```

### 2. 可过滤选择字段
```xml
<field name="country_id" widget="filterable_selection"/>
```

### 3. 自定义配置
```xml
<field name="priority" widget="selection" 
       options="{
           'placeholder': 'Select Priority',
           'no_open': true,
           'no_create': true
       }"/>
```

## 配置选项

### 1. 显示选项
- **placeholder**: 占位符文本
- **no_open**: 禁止打开记录详情
- **no_create**: 禁止创建新记录
- **no_edit**: 禁止编辑现有记录

### 2. 搜索选项
- **searchable**: 是否启用搜索
- **search_placeholder**: 搜索占位符
- **min_search_length**: 最小搜索长度
- **search_delay**: 搜索延迟时间

### 3. 行为选项
- **required**: 是否必填
- **readonly**: 是否只读
- **multiple**: 是否多选
- **clearable**: 是否可清除

## 选项数据格式

### 1. 静态选项
```python
SELECTION_OPTIONS = [
    ('draft', 'Draft'),
    ('confirmed', 'Confirmed'),
    ('done', 'Done'),
    ('cancelled', 'Cancelled'),
]
```

### 2. 动态选项
```python
def _get_selection_options(self):
    return [
        (record.id, record.name)
        for record in self.env['model.name'].search([])
    ]
```

### 3. 分组选项
```python
GROUPED_OPTIONS = [
    ('Group 1', [
        ('option1', 'Option 1'),
        ('option2', 'Option 2'),
    ]),
    ('Group 2', [
        ('option3', 'Option 3'),
        ('option4', 'Option 4'),
    ]),
]
```

## 搜索算法

### 1. 精确匹配
```javascript
function exactMatch(query, option) {
    return option.label.toLowerCase() === query.toLowerCase();
}
```

### 2. 前缀匹配
```javascript
function prefixMatch(query, option) {
    return option.label.toLowerCase().startsWith(query.toLowerCase());
}
```

### 3. 模糊匹配
```javascript
function fuzzyMatch(query, option) {
    return option.label.toLowerCase().includes(query.toLowerCase());
}
```

### 4. 智能匹配
```javascript
function smartMatch(query, option) {
    const words = query.toLowerCase().split(' ');
    const label = option.label.toLowerCase();
    return words.every(word => label.includes(word));
}
```

## 最佳实践

### 1. 性能优化
- 合理设置搜索延迟
- 使用虚拟滚动处理大量选项
- 缓存搜索结果
- 延迟加载选项数据

### 2. 用户体验
- 提供清晰的选项描述
- 使用有意义的分组
- 提供搜索提示
- 显示选择状态

### 3. 可访问性
- 提供键盘导航支持
- 添加ARIA标签
- 确保颜色对比度
- 支持屏幕阅读器

## 扩展开发

### 1. 自定义选择字段
```javascript
class CustomSelectionField extends SelectionField {
    // 自定义实现
}
```

### 2. 添加新功能
- 多级选择支持
- 选项图标显示
- 选项描述工具提示
- 选择历史记录

### 3. 集成其他组件
- 与搜索组件集成
- 与过滤器集成
- 与表单验证集成
- 与数据源集成

## 国际化支持

### 1. 选项本地化
```python
SELECTION_OPTIONS = [
    ('draft', _('Draft')),
    ('confirmed', _('Confirmed')),
    ('done', _('Done')),
]
```

### 2. 搜索本地化
- 支持多语言搜索
- 本地化搜索提示
- 字符编码处理
- 排序规则适配

### 3. 界面本地化
- 按钮文本本地化
- 错误消息本地化
- 帮助文本本地化
- 日期格式本地化

## 故障排除

### 1. 常见问题
- **选项不显示**: 检查数据源和权限
- **搜索无效**: 验证搜索配置
- **选择无响应**: 检查事件绑定

### 2. 调试技巧
- 检查选项数据结构
- 验证搜索算法
- 查看网络请求
- 使用开发者工具

### 3. 性能问题
- 监控选项加载时间
- 检查搜索性能
- 优化渲染频率
- 减少DOM操作

## 版本兼容性

- **Odoo 18.0+**: 完全支持
- **现代浏览器**: Chrome 90+, Firefox 88+, Safari 14+
- **移动端**: 支持触摸操作和原生选择器
- **键盘导航**: 完整的键盘操作支持

## 相关模块

- **Many2One Field**: 多对一字段
- **Radio Field**: 单选按钮字段
- **Boolean Field**: 布尔字段
- **State Selection**: 状态选择字段

## 安全考虑

1. **输入验证**: 验证选择值的有效性
2. **权限检查**: 检查选项访问权限
3. **数据过滤**: 过滤敏感选项数据
4. **注入防护**: 防止选项注入攻击

## 贡献指南

1. 遵循现有代码风格
2. 添加适当的测试用例
3. 更新相关文档
4. 考虑性能影响
5. 测试不同数据场景

该模块为 Odoo Web 客户端提供了完整的选择字段解决方案，通过直观的选择界面和强大的搜索过滤功能确保了用户选择操作的便利性和准确性。

# SelectionField - 选择字段

## 概述

`selection_field.js` 是 Odoo Web 客户端的选择字段组件，负责处理下拉选择类型字段的显示和编辑。该模块包含124行代码，是一个功能完整的选择组件，专门用于处理selection和many2one类型的字段，具备动态选项加载、域过滤、占位符、自动保存等特性，是表单选择输入的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/selection/selection_field.js`
- **行数**: 124
- **模块**: `@web/views/fields/selection/selection_field`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                            // OWL框架
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/model/relational_model/utils'     // 关系模型工具
'@web/views/fields/relational_utils'    // 关系字段工具
'@web/views/fields/standard_field_props' // 标准字段属性
```

## 核心功能

### 1. 组件定义

```javascript
const SelectionField = class SelectionField extends Component {
    static template = "web.SelectionField";
    static props = {
        ...standardFieldProps,
        placeholder: { type: String, optional: true },
        required: { type: Boolean, optional: true },
        domain: { type: [Array, Function], optional: true },
        autosave: { type: Boolean, optional: true },
    };
    static defaultProps = {
        autosave: false,
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **占位符**: 支持placeholder配置占位符文本
- **必填**: 支持required配置必填验证
- **域过滤**: 支持domain配置域过滤条件
- **自动保存**: 支持autosave配置自动保存
- **默认配置**: 默认关闭自动保存

### 2. 组件初始化

```javascript
setup() {
    this.type = this.props.record.fields[this.props.name].type;
    if (this.type === "many2one") {
        this.specialData = useSpecialData((orm, props) => {
            const { relation } = props.record.fields[props.name];
            const domain = getFieldDomain(props.record, props.name, props.domain);
            return orm.call(relation, "name_search", ["", domain]);
        });
    }
}
```

**初始化功能**:
- **类型检测**: 检测字段类型
- **动态数据**: many2one类型使用动态数据加载
- **关系模型**: 获取关系模型名称
- **域过滤**: 应用域过滤条件
- **名称搜索**: 使用name_search方法获取选项

### 3. 选项获取

```javascript
get options() {
    switch (this.type) {
        case "many2one":
            return [...this.specialData.data];
        case "selection":
            return this.props.record.fields[this.props.name].selection.filter(
                (option) => option[0] !== false && option[1] !== ""
            );
        default:
            return [];
    }
}
```

**选项功能**:
- **Many2one类型**: 从动态数据获取选项
- **Selection类型**: 从字段定义获取选择选项
- **选项过滤**: 过滤无效选项（false值和空字符串）
- **类型适配**: 根据字段类型适配数据源

### 4. 值处理

```javascript
get value() {
    const fieldValue = this.props.record.data[this.props.name];
    if (this.type === "many2one") {
        return Array.isArray(fieldValue) ? fieldValue[0] : fieldValue;
    }
    return fieldValue;
}

get displayValue() {
    const value = this.value;
    if (!value && value !== 0) {
        return "";
    }
    
    const option = this.options.find(opt => opt[0] === value);
    return option ? option[1] : value;
}
```

**值处理功能**:
- **值获取**: 根据类型获取当前值
- **数组处理**: 处理many2one的数组格式
- **显示值**: 获取选项的显示文本
- **选项匹配**: 匹配值对应的选项

### 5. 事件处理

```javascript
onChange(ev) {
    const value = ev.target.value;
    let newValue;
    
    if (this.type === "many2one") {
        newValue = value ? parseInt(value) : false;
    } else {
        newValue = value || false;
    }
    
    this.props.record.update({ [this.props.name]: newValue });
    
    if (this.props.autosave) {
        this.props.record.save();
    }
}
```

**事件处理功能**:
- **值变更**: 处理选择值的变更
- **类型转换**: 根据字段类型转换值格式
- **记录更新**: 更新记录的字段值
- **自动保存**: 根据配置自动保存记录

### 6. 字段注册

```javascript
const selectionField = {
    component: SelectionField,
    displayName: _t("Selection"),
    supportedTypes: ["many2one", "selection"],
    isEmpty: (record, fieldName) => {
        const value = record.data[fieldName];
        return !value && value !== 0;
    },
    extractProps: ({ attrs, options }, dynamicInfo) => ({
        placeholder: attrs.placeholder,
        required: attrs.required,
        domain: dynamicInfo.domain,
        autosave: options.autosave,
    }),
};

registry.category("fields").add("selection", selectionField);
```

**注册功能**:
- **组件注册**: 注册选择字段组件
- **显示名称**: 设置为"Selection"
- **类型支持**: 支持many2one和selection类型
- **空值判断**: 定义空值判断逻辑
- **属性提取**: 提取占位符、必填、域、自动保存等属性

## 使用场景

### 1. 选择字段管理器

```javascript
// 选择字段管理器
class SelectionFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置选择字段配置
        this.selectionConfig = {
            enableDynamicLoading: true,
            enableDomainFiltering: true,
            enableOptionCaching: true,
            enableValidation: true,
            enableSearch: false,
            enableGrouping: false,
            enableSorting: true,
            enableCustomOptions: false
        };
        
        // 设置选项配置
        this.optionConfig = {
            enableEmptyOption: true,
            emptyOptionText: '',
            enableOptionIcons: false,
            enableOptionColors: false,
            enableOptionTooltips: false,
            maxOptionsDisplay: 100,
            enableLazyLoading: false
        };
        
        // 设置验证规则
        this.validationRules = {
            enableRequiredValidation: true,
            enableCustomValidation: false,
            customValidators: [],
            showValidationMessages: true,
            validationTrigger: 'change'
        };
        
        // 设置缓存系统
        this.cacheSystem = {
            optionCache: new Map(),
            domainCache: new Map(),
            cacheTimeout: 300000, // 5分钟
            maxCacheSize: 50,
            cacheHits: 0,
            cacheMisses: 0
        };
        
        // 设置选择统计
        this.selectionStatistics = {
            totalSelectionFields: 0,
            totalSelections: 0,
            selectionsByType: new Map(),
            optionDistribution: new Map(),
            averageOptionsPerField: 0,
            mostSelectedOption: null,
            cacheHitRate: 0
        };
        
        this.initializeSelectionSystem();
    }
    
    // 初始化选择系统
    initializeSelectionSystem() {
        // 创建增强的选择字段
        this.createEnhancedSelectionField();
        
        // 设置缓存系统
        this.setupCacheSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置选项管理系统
        this.setupOptionManagementSystem();
    }
    
    // 创建增强的选择字段
    createEnhancedSelectionField() {
        const originalField = SelectionField;
        
        this.EnhancedSelectionField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加缓存功能
                this.addCacheFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isLoading: false,
                    validationErrors: [],
                    cachedOptions: null,
                    lastLoadTime: null,
                    searchQuery: '',
                    filteredOptions: [],
                    selectedOption: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的选项获取
                this.enhancedGetOptions = async () => {
                    const cacheKey = this.getCacheKey();
                    
                    // 检查缓存
                    if (this.selectionConfig.enableOptionCaching) {
                        const cached = this.getCachedOptions(cacheKey);
                        if (cached) {
                            this.cacheSystem.cacheHits++;
                            return cached;
                        }
                    }
                    
                    this.cacheSystem.cacheMisses++;
                    this.enhancedState.isLoading = true;
                    
                    try {
                        let options;
                        
                        switch (this.type) {
                            case "many2one":
                                options = await this.loadMany2OneOptions();
                                break;
                            case "selection":
                                options = this.loadSelectionOptions();
                                break;
                            default:
                                options = [];
                        }
                        
                        // 处理选项
                        options = this.processOptions(options);
                        
                        // 缓存选项
                        if (this.selectionConfig.enableOptionCaching) {
                            this.setCachedOptions(cacheKey, options);
                        }
                        
                        this.enhancedState.lastLoadTime = new Date();
                        
                        return options;
                        
                    } catch (error) {
                        console.error('Failed to load options:', error);
                        return [];
                    } finally {
                        this.enhancedState.isLoading = false;
                    }
                };
                
                // 加载Many2One选项
                this.loadMany2OneOptions = async () => {
                    if (this.specialData && this.specialData.data) {
                        return [...this.specialData.data];
                    }
                    return [];
                };
                
                // 加载Selection选项
                this.loadSelectionOptions = () => {
                    const selection = this.props.record.fields[this.props.name].selection || [];
                    return selection.filter(
                        (option) => option[0] !== false && option[1] !== ""
                    );
                };
                
                // 处理选项
                this.processOptions = (options) => {
                    let processedOptions = [...options];
                    
                    // 排序
                    if (this.selectionConfig.enableSorting) {
                        processedOptions.sort((a, b) => {
                            const nameA = String(a[1] || '').toLowerCase();
                            const nameB = String(b[1] || '').toLowerCase();
                            return nameA.localeCompare(nameB);
                        });
                    }
                    
                    // 限制显示数量
                    if (this.optionConfig.maxOptionsDisplay > 0) {
                        processedOptions = processedOptions.slice(0, this.optionConfig.maxOptionsDisplay);
                    }
                    
                    // 添加空选项
                    if (this.optionConfig.enableEmptyOption && !this.props.required) {
                        processedOptions.unshift([false, this.optionConfig.emptyOptionText]);
                    }
                    
                    return processedOptions;
                };
                
                // 增强的值变更
                this.enhancedOnChange = async (ev) => {
                    const value = ev.target.value;
                    
                    try {
                        // 验证值
                        await this.validateSelection(value);
                        
                        // 转换值
                        let newValue;
                        if (this.type === "many2one") {
                            newValue = value ? parseInt(value) : false;
                        } else {
                            newValue = value || false;
                        }
                        
                        // 更新记录
                        this.props.record.update({ [this.props.name]: newValue });
                        
                        // 记录选择
                        this.recordSelection(newValue);
                        
                        // 自动保存
                        if (this.props.autosave) {
                            await this.props.record.save();
                        }
                        
                        // 更新状态
                        this.enhancedState.selectedOption = newValue;
                        
                    } catch (error) {
                        this.handleSelectionError(error);
                    }
                };
                
                // 验证选择
                this.validateSelection = async (value) => {
                    const errors = [];
                    
                    // 必填验证
                    if (this.validationRules.enableRequiredValidation && this.props.required) {
                        if (!value && value !== 0) {
                            errors.push('This field is required');
                        }
                    }
                    
                    // 选项验证
                    if (value && value !== 0) {
                        const options = await this.enhancedGetOptions();
                        const validOption = options.find(opt => opt[0] == value);
                        if (!validOption) {
                            errors.push('Invalid option selected');
                        }
                    }
                    
                    // 自定义验证
                    if (this.validationRules.enableCustomValidation) {
                        for (const validator of this.validationRules.customValidators) {
                            try {
                                const result = await validator(value, this.props);
                                if (result !== true) {
                                    errors.push(result || 'Invalid selection');
                                }
                            } catch (error) {
                                errors.push('Validation error');
                            }
                        }
                    }
                    
                    this.enhancedState.validationErrors = errors;
                    
                    if (errors.length > 0) {
                        throw new Error(errors.join(', '));
                    }
                };
                
                // 获取缓存键
                this.getCacheKey = () => {
                    const domain = JSON.stringify(this.props.domain || []);
                    return `${this.type}:${this.props.name}:${domain}`;
                };
                
                // 获取缓存选项
                this.getCachedOptions = (cacheKey) => {
                    const cached = this.cacheSystem.optionCache.get(cacheKey);
                    if (cached && Date.now() - cached.timestamp < this.cacheSystem.cacheTimeout) {
                        return cached.options;
                    }
                    return null;
                };
                
                // 设置缓存选项
                this.setCachedOptions = (cacheKey, options) => {
                    // 检查缓存大小
                    if (this.cacheSystem.optionCache.size >= this.cacheSystem.maxCacheSize) {
                        // 删除最旧的缓存项
                        const firstKey = this.cacheSystem.optionCache.keys().next().value;
                        this.cacheSystem.optionCache.delete(firstKey);
                    }
                    
                    this.cacheSystem.optionCache.set(cacheKey, {
                        options: options,
                        timestamp: Date.now()
                    });
                };
                
                // 搜索选项
                this.searchOptions = (query) => {
                    this.enhancedState.searchQuery = query;
                    
                    if (!query.trim()) {
                        this.enhancedState.filteredOptions = this.options;
                        return;
                    }
                    
                    const filtered = this.options.filter(option =>
                        String(option[1] || '').toLowerCase().includes(query.toLowerCase())
                    );
                    
                    this.enhancedState.filteredOptions = filtered;
                };
                
                // 获取选择信息
                this.getSelectionInfo = () => {
                    return {
                        type: this.type,
                        value: this.value,
                        displayValue: this.displayValue,
                        optionCount: this.options?.length || 0,
                        isLoading: this.enhancedState.isLoading,
                        validationErrors: this.enhancedState.validationErrors,
                        lastLoadTime: this.enhancedState.lastLoadTime,
                        selectedOption: this.enhancedState.selectedOption,
                        hasCache: this.getCachedOptions(this.getCacheKey()) !== null
                    };
                };
                
                // 记录选择
                this.recordSelection = (value) => {
                    this.selectionStatistics.totalSelections++;
                    
                    // 记录按类型分布
                    const count = this.selectionStatistics.selectionsByType.get(this.type) || 0;
                    this.selectionStatistics.selectionsByType.set(this.type, count + 1);
                    
                    // 记录选项分布
                    if (value !== false && value !== null && value !== undefined) {
                        const optionCount = this.selectionStatistics.optionDistribution.get(value) || 0;
                        this.selectionStatistics.optionDistribution.set(value, optionCount + 1);
                    }
                    
                    // 更新平均选项数
                    this.updateAverageOptions();
                };
                
                // 更新平均选项数
                this.updateAverageOptions = () => {
                    if (this.selectionStatistics.totalSelectionFields > 0) {
                        const totalOptions = Array.from(this.selectionStatistics.optionDistribution.values())
                            .reduce((sum, count) => sum + count, 0);
                        this.selectionStatistics.averageOptionsPerField = totalOptions / this.selectionStatistics.totalSelectionFields;
                    }
                };
                
                // 处理选择错误
                this.handleSelectionError = (error) => {
                    console.error('Selection field error:', error);
                    
                    if (this.validationRules.showValidationMessages) {
                        // 显示验证错误消息
                        this.showValidationMessage(error.message);
                    }
                };
                
                // 显示验证消息
                this.showValidationMessage = (message) => {
                    // 实现验证消息显示逻辑
                    console.warn('Validation message:', message);
                };
                
                // 清理缓存
                this.clearCache = () => {
                    this.cacheSystem.optionCache.clear();
                    this.cacheSystem.domainCache.clear();
                };
            }
            
            addCacheFeatures() {
                // 缓存功能
                this.cacheManager = {
                    enabled: this.selectionConfig.enableOptionCaching,
                    get: (key) => this.getCachedOptions(key),
                    set: (key, options) => this.setCachedOptions(key, options),
                    clear: () => this.clearCache(),
                    getStats: () => ({
                        hits: this.cacheSystem.cacheHits,
                        misses: this.cacheSystem.cacheMisses,
                        hitRate: this.cacheSystem.cacheHits / Math.max(this.cacheSystem.cacheHits + this.cacheSystem.cacheMisses, 1) * 100,
                        size: this.cacheSystem.optionCache.size
                    })
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.validationRules.enableRequiredValidation,
                    validate: (value) => this.validateSelection(value),
                    getErrors: () => this.enhancedState.validationErrors,
                    addValidator: (validator) => this.validationRules.customValidators.push(validator)
                };
            }
            
            // 重写原始方法
            get options() {
                return this.enhancedGetOptions();
            }
            
            onChange(ev) {
                return this.enhancedOnChange(ev);
            }
        };
    }
    
    // 设置缓存系统
    setupCacheSystem() {
        this.cacheSystemConfig = {
            enabled: this.selectionConfig.enableOptionCaching,
            system: this.cacheSystem
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.selectionConfig.enableValidation,
            rules: this.validationRules
        };
    }
    
    // 设置选项管理系统
    setupOptionManagementSystem() {
        this.optionManagementConfig = {
            enabled: true,
            config: this.optionConfig
        };
    }
    
    // 创建选择字段
    createSelectionField(props) {
        const field = new this.EnhancedSelectionField(props);
        this.selectionStatistics.totalSelectionFields++;
        return field;
    }
    
    // 添加自定义验证器
    addCustomValidator(validator) {
        this.validationRules.customValidators.push(validator);
    }
    
    // 获取最受欢迎的选项
    getMostSelectedOption() {
        let maxCount = 0;
        let mostSelected = null;
        
        for (const [option, count] of this.selectionStatistics.optionDistribution.entries()) {
            if (count > maxCount) {
                maxCount = count;
                mostSelected = option;
            }
        }
        
        this.selectionStatistics.mostSelectedOption = mostSelected;
        return mostSelected;
    }
    
    // 获取选择统计
    getSelectionStatistics() {
        const cacheStats = this.cacheSystem.cacheHits + this.cacheSystem.cacheMisses;
        
        return {
            ...this.selectionStatistics,
            mostSelectedOption: this.getMostSelectedOption(),
            typeVariety: this.selectionStatistics.selectionsByType.size,
            optionVariety: this.selectionStatistics.optionDistribution.size,
            selectionRate: this.selectionStatistics.totalSelections / Math.max(this.selectionStatistics.totalSelectionFields, 1),
            cacheHitRate: cacheStats > 0 ? (this.cacheSystem.cacheHits / cacheStats * 100) : 0,
            cacheSize: this.cacheSystem.optionCache.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理缓存
        this.cacheSystem.optionCache.clear();
        this.cacheSystem.domainCache.clear();
        
        // 清理统计
        this.selectionStatistics.selectionsByType.clear();
        this.selectionStatistics.optionDistribution.clear();
        
        // 重置统计
        this.selectionStatistics = {
            totalSelectionFields: 0,
            totalSelections: 0,
            selectionsByType: new Map(),
            optionDistribution: new Map(),
            averageOptionsPerField: 0,
            mostSelectedOption: null,
            cacheHitRate: 0
        };
    }
}

// 使用示例
const selectionManager = new SelectionFieldManager();

// 创建选择字段
const selectionField = selectionManager.createSelectionField({
    name: 'state',
    record: {
        data: { state: 'draft' },
        fields: { 
            state: { 
                type: 'selection',
                selection: [
                    ['draft', 'Draft'],
                    ['confirmed', 'Confirmed'],
                    ['done', 'Done'],
                    ['cancelled', 'Cancelled']
                ]
            }
        }
    },
    placeholder: 'Select a state...',
    required: true,
    autosave: false
});

// 添加自定义验证器
selectionManager.addCustomValidator((value, props) => {
    if (props.name === 'state' && value === 'cancelled') {
        return 'Cancelled state requires approval';
    }
    return true;
});

// 获取统计信息
const stats = selectionManager.getSelectionStatistics();
console.log('Selection field statistics:', stats);
```

## 技术特点

### 1. 多类型支持
- **Selection类型**: 支持selection字段类型
- **Many2one类型**: 支持many2one字段类型
- **动态加载**: many2one类型动态加载选项
- **类型适配**: 根据字段类型适配处理逻辑

### 2. 选项管理
- **选项过滤**: 过滤无效选项
- **动态获取**: 动态获取关系模型选项
- **域过滤**: 支持域过滤条件
- **缓存机制**: 缓存选项提高性能

### 3. 用户交互
- **下拉选择**: 标准的下拉选择交互
- **占位符**: 支持占位符文本
- **自动保存**: 支持选择后自动保存
- **验证**: 支持必填和自定义验证

### 4. 性能优化
- **懒加载**: 支持选项的懒加载
- **缓存**: 缓存选项数据
- **域优化**: 优化域过滤查询
- **批量处理**: 支持批量选项处理

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- **类型策略**: 不同字段类型的处理策略
- **加载策略**: 不同的选项加载策略
- **验证策略**: 不同的验证策略

### 2. 适配器模式 (Adapter Pattern)
- **类型适配**: 适配不同字段类型
- **数据适配**: 适配不同数据格式
- **接口适配**: 适配统一的组件接口

### 3. 观察者模式 (Observer Pattern)
- **值观察**: 观察字段值变化
- **选项观察**: 观察选项数据变化
- **状态观察**: 观察组件状态变化

### 4. 缓存模式 (Cache Pattern)
- **选项缓存**: 缓存选项数据
- **域缓存**: 缓存域查询结果
- **智能失效**: 智能的缓存失效机制

## 注意事项

1. **性能优化**: 合理使用缓存避免重复查询
2. **数据一致性**: 确保选项数据的一致性
3. **用户体验**: 提供清晰的选择指示
4. **验证**: 确保选择值的有效性

## 扩展建议

1. **搜索功能**: 添加选项搜索功能
2. **分组显示**: 支持选项分组显示
3. **多选支持**: 支持多选模式
4. **自定义渲染**: 支持自定义选项渲染
5. **异步加载**: 增强异步选项加载

该选择字段为Odoo Web客户端提供了完整的下拉选择功能，通过多类型支持和智能缓存确保了在不同场景下的高效性和良好的用户体验。

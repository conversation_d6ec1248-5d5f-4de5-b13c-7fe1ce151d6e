# FilterableSelectionField - 可过滤选择字段

## 概述

`filterable_selection_field.js` 是 Odoo Web 客户端的可过滤选择字段组件，是选择字段的增强版本，负责根据白名单或黑名单过滤选择选项。该模块包含83行代码，是一个功能专门的过滤选择组件，专门用于在不同视图中显示同一选择字段的不同选项子集，具备白名单过滤、黑名单过滤、动态字段过滤等特性，是多视图选择字段管理的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/selection/filterable_selection_field.js`
- **行数**: 83
- **模块**: `@web/views/fields/selection/filterable_selection_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                    // 注册表
'@web/views/fields/selection/selection_field' // 基础选择字段
```

## 核心功能

### 1. 组件定义

```javascript
const FilterableSelectionField = class FilterableSelectionField extends SelectionField {
    static props = {
        ...SelectionField.props,
        whitelist_fname: { type: String, optional: true },
        whitelisted_values: { type: Array, optional: true },
        blacklisted_values: { type: Array, optional: true },
    };
}
```

**组件特性**:
- **继承基类**: 继承SelectionField的所有功能
- **白名单字段**: 支持whitelist_fname配置白名单字段名
- **白名单值**: 支持whitelisted_values配置白名单值数组
- **黑名单值**: 支持blacklisted_values配置黑名单值数组
- **可选配置**: 所有过滤配置都是可选的

### 2. 选项过滤

```javascript
get options() {
    let options = super.options;
    if (this.props.whitelist_fname) {
        options = options.filter((option) => {
            return (
                option[0] === this.props.record.data[this.props.name] ||
                this.props.record.data[this.props.whitelist_fname].includes(option[0])
            );
        });
    } else if (this.props.whitelisted_values) {
        options = options.filter((option) => {
            return (
                option[0] === this.props.record.data[this.props.name] ||
                this.props.whitelisted_values.includes(option[0])
            );
        });
    } else if (this.props.blacklisted_values) {
        options = options.filter((option) => {
            return (
                option[0] === this.props.record.data[this.props.name] ||
                !this.props.blacklisted_values.includes(option[0])
            );
        });
    }
    return options;
}
```

**过滤功能**:
- **基础选项**: 从父类获取基础选项列表
- **白名单字段**: 根据记录中的白名单字段过滤
- **白名单值**: 根据配置的白名单值过滤
- **黑名单值**: 根据配置的黑名单值过滤
- **当前值保留**: 始终保留当前选中的值

### 3. 过滤逻辑

```javascript
// 白名单字段过滤
if (this.props.whitelist_fname) {
    const whitelistField = this.props.record.data[this.props.whitelist_fname];
    options = options.filter(option => 
        option[0] === currentValue || whitelistField.includes(option[0])
    );
}

// 白名单值过滤
else if (this.props.whitelisted_values) {
    options = options.filter(option => 
        option[0] === currentValue || this.props.whitelisted_values.includes(option[0])
    );
}

// 黑名单值过滤
else if (this.props.blacklisted_values) {
    options = options.filter(option => 
        option[0] === currentValue || !this.props.blacklisted_values.includes(option[0])
    );
}
```

**过滤逻辑特点**:
- **优先级**: 白名单字段 > 白名单值 > 黑名单值
- **当前值**: 始终显示当前选中的值
- **动态过滤**: 支持基于记录字段的动态过滤
- **静态过滤**: 支持基于配置的静态过滤

### 4. 字段注册

```javascript
const filterableSelectionField = {
    ...selectionField,
    component: FilterableSelectionField,
    extractProps: ({ attrs, options }, dynamicInfo) => ({
        ...selectionField.extractProps({ attrs, options }, dynamicInfo),
        whitelist_fname: attrs.whitelist_fname,
        whitelisted_values: attrs.whitelisted_values,
        blacklisted_values: attrs.blacklisted_values,
    }),
};

registry.category("fields").add("filterable_selection", filterableSelectionField);
```

**注册功能**:
- **配置继承**: 继承基础选择字段的所有配置
- **组件替换**: 使用可过滤选择字段组件
- **属性提取**: 提取过滤相关的属性
- **字段注册**: 注册为"filterable_selection"字段类型

## 使用场景

### 1. 可过滤选择字段管理器

```javascript
// 可过滤选择字段管理器
class FilterableSelectionFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置可过滤选择字段配置
        this.filterableSelectionConfig = {
            enableDynamicFiltering: true,
            enableStaticFiltering: true,
            enableCascadingFilters: false,
            enableFilterCaching: true,
            enableFilterValidation: true,
            enableFilterHistory: false,
            enableFilterPresets: false,
            enableFilterExport: false
        };
        
        // 设置过滤器类型
        this.filterTypes = new Map([
            ['whitelist_field', {
                name: 'Whitelist Field',
                description: 'Filter based on a field in the record',
                dynamic: true,
                priority: 1
            }],
            ['whitelist_values', {
                name: 'Whitelist Values',
                description: 'Filter based on predefined allowed values',
                dynamic: false,
                priority: 2
            }],
            ['blacklist_values', {
                name: 'Blacklist Values',
                description: 'Filter based on predefined blocked values',
                dynamic: false,
                priority: 3
            }]
        ]);
        
        // 设置过滤器预设
        this.filterPresets = new Map([
            ['status_active', {
                name: 'Active Status Only',
                type: 'whitelist_values',
                values: ['draft', 'confirmed', 'done']
            }],
            ['status_inactive', {
                name: 'Exclude Cancelled',
                type: 'blacklist_values',
                values: ['cancelled', 'rejected']
            }],
            ['priority_high', {
                name: 'High Priority Only',
                type: 'whitelist_values',
                values: ['high', 'urgent']
            }]
        ]);
        
        // 设置验证规则
        this.validationRules = {
            enableWhitelistValidation: true,
            enableBlacklistValidation: true,
            enableConflictDetection: true,
            enableEmptyFilterWarning: true,
            maxWhitelistSize: 50,
            maxBlacklistSize: 50
        };
        
        // 设置过滤统计
        this.filterStatistics = {
            totalFilterableFields: 0,
            totalFilters: 0,
            filtersByType: new Map(),
            filterEffectiveness: new Map(),
            averageFilteredOptions: 0,
            mostUsedFilterType: null
        };
        
        this.initializeFilterableSelectionSystem();
    }
    
    // 初始化可过滤选择系统
    initializeFilterableSelectionSystem() {
        // 创建增强的可过滤选择字段
        this.createEnhancedFilterableSelectionField();
        
        // 设置过滤器系统
        this.setupFilterSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置预设系统
        this.setupPresetSystem();
    }
    
    // 创建增强的可过滤选择字段
    createEnhancedFilterableSelectionField() {
        const originalField = FilterableSelectionField;
        
        this.EnhancedFilterableSelectionField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加过滤功能
                this.addFilterFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    activeFilters: [],
                    originalOptions: null,
                    filteredOptions: null,
                    filterEffectiveness: 0,
                    lastFilterTime: null,
                    filterHistory: [],
                    validationErrors: []
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的选项获取
                this.enhancedGetOptions = () => {
                    // 获取原始选项
                    if (!this.enhancedState.originalOptions) {
                        this.enhancedState.originalOptions = super.options;
                    }
                    
                    let options = [...this.enhancedState.originalOptions];
                    const currentValue = this.props.record.data[this.props.name];
                    
                    // 应用过滤器
                    const appliedFilters = [];
                    
                    // 白名单字段过滤
                    if (this.props.whitelist_fname) {
                        const filter = this.applyWhitelistFieldFilter(options, currentValue);
                        options = filter.options;
                        appliedFilters.push(filter.info);
                    }
                    // 白名单值过滤
                    else if (this.props.whitelisted_values) {
                        const filter = this.applyWhitelistValuesFilter(options, currentValue);
                        options = filter.options;
                        appliedFilters.push(filter.info);
                    }
                    // 黑名单值过滤
                    else if (this.props.blacklisted_values) {
                        const filter = this.applyBlacklistValuesFilter(options, currentValue);
                        options = filter.options;
                        appliedFilters.push(filter.info);
                    }
                    
                    // 更新状态
                    this.enhancedState.filteredOptions = options;
                    this.enhancedState.activeFilters = appliedFilters;
                    this.enhancedState.filterEffectiveness = this.calculateFilterEffectiveness();
                    this.enhancedState.lastFilterTime = new Date();
                    
                    // 记录过滤历史
                    this.recordFilterHistory(appliedFilters);
                    
                    // 记录统计
                    this.recordFilterStatistics(appliedFilters);
                    
                    return options;
                };
                
                // 应用白名单字段过滤
                this.applyWhitelistFieldFilter = (options, currentValue) => {
                    const whitelistField = this.props.record.data[this.props.whitelist_fname];
                    const originalCount = options.length;
                    
                    const filteredOptions = options.filter((option) => {
                        return option[0] === currentValue || 
                               (whitelistField && whitelistField.includes(option[0]));
                    });
                    
                    return {
                        options: filteredOptions,
                        info: {
                            type: 'whitelist_field',
                            field: this.props.whitelist_fname,
                            originalCount: originalCount,
                            filteredCount: filteredOptions.length,
                            effectiveness: (originalCount - filteredOptions.length) / originalCount * 100
                        }
                    };
                };
                
                // 应用白名单值过滤
                this.applyWhitelistValuesFilter = (options, currentValue) => {
                    const originalCount = options.length;
                    
                    const filteredOptions = options.filter((option) => {
                        return option[0] === currentValue || 
                               this.props.whitelisted_values.includes(option[0]);
                    });
                    
                    return {
                        options: filteredOptions,
                        info: {
                            type: 'whitelist_values',
                            values: this.props.whitelisted_values,
                            originalCount: originalCount,
                            filteredCount: filteredOptions.length,
                            effectiveness: (originalCount - filteredOptions.length) / originalCount * 100
                        }
                    };
                };
                
                // 应用黑名单值过滤
                this.applyBlacklistValuesFilter = (options, currentValue) => {
                    const originalCount = options.length;
                    
                    const filteredOptions = options.filter((option) => {
                        return option[0] === currentValue || 
                               !this.props.blacklisted_values.includes(option[0]);
                    });
                    
                    return {
                        options: filteredOptions,
                        info: {
                            type: 'blacklist_values',
                            values: this.props.blacklisted_values,
                            originalCount: originalCount,
                            filteredCount: filteredOptions.length,
                            effectiveness: (originalCount - filteredOptions.length) / originalCount * 100
                        }
                    };
                };
                
                // 计算过滤器效果
                this.calculateFilterEffectiveness = () => {
                    if (!this.enhancedState.originalOptions || !this.enhancedState.filteredOptions) {
                        return 0;
                    }
                    
                    const originalCount = this.enhancedState.originalOptions.length;
                    const filteredCount = this.enhancedState.filteredOptions.length;
                    
                    return originalCount > 0 ? (originalCount - filteredCount) / originalCount * 100 : 0;
                };
                
                // 验证过滤器配置
                this.validateFilterConfiguration = () => {
                    const errors = [];
                    
                    // 检查冲突配置
                    if (this.validationRules.enableConflictDetection) {
                        const configCount = [
                            this.props.whitelist_fname,
                            this.props.whitelisted_values,
                            this.props.blacklisted_values
                        ].filter(Boolean).length;
                        
                        if (configCount > 1) {
                            errors.push('Multiple filter configurations detected. Only one should be used.');
                        }
                    }
                    
                    // 检查白名单大小
                    if (this.validationRules.enableWhitelistValidation && this.props.whitelisted_values) {
                        if (this.props.whitelisted_values.length > this.validationRules.maxWhitelistSize) {
                            errors.push(`Whitelist size exceeds maximum of ${this.validationRules.maxWhitelistSize}`);
                        }
                        
                        if (this.props.whitelisted_values.length === 0) {
                            errors.push('Empty whitelist will hide all options');
                        }
                    }
                    
                    // 检查黑名单大小
                    if (this.validationRules.enableBlacklistValidation && this.props.blacklisted_values) {
                        if (this.props.blacklisted_values.length > this.validationRules.maxBlacklistSize) {
                            errors.push(`Blacklist size exceeds maximum of ${this.validationRules.maxBlacklistSize}`);
                        }
                    }
                    
                    // 检查空过滤器
                    if (this.validationRules.enableEmptyFilterWarning) {
                        const hasFilter = this.props.whitelist_fname || 
                                         this.props.whitelisted_values || 
                                         this.props.blacklisted_values;
                        if (!hasFilter) {
                            errors.push('No filter configuration found. Consider using regular selection field.');
                        }
                    }
                    
                    this.enhancedState.validationErrors = errors;
                    return errors;
                };
                
                // 获取过滤器信息
                this.getFilterInfo = () => {
                    return {
                        hasFilters: this.enhancedState.activeFilters.length > 0,
                        activeFilters: this.enhancedState.activeFilters,
                        originalOptionCount: this.enhancedState.originalOptions?.length || 0,
                        filteredOptionCount: this.enhancedState.filteredOptions?.length || 0,
                        filterEffectiveness: this.enhancedState.filterEffectiveness,
                        lastFilterTime: this.enhancedState.lastFilterTime,
                        validationErrors: this.enhancedState.validationErrors,
                        filterHistory: this.enhancedState.filterHistory.slice(-10) // 最近10次
                    };
                };
                
                // 应用预设过滤器
                this.applyFilterPreset = (presetName) => {
                    const preset = this.filterPresets.get(presetName);
                    if (!preset) {
                        return false;
                    }
                    
                    // 根据预设类型应用过滤器
                    switch (preset.type) {
                        case 'whitelist_values':
                            this.props.whitelisted_values = preset.values;
                            this.props.blacklisted_values = null;
                            this.props.whitelist_fname = null;
                            break;
                        case 'blacklist_values':
                            this.props.blacklisted_values = preset.values;
                            this.props.whitelisted_values = null;
                            this.props.whitelist_fname = null;
                            break;
                    }
                    
                    // 重新计算选项
                    this.enhancedState.originalOptions = null;
                    
                    return true;
                };
                
                // 记录过滤历史
                this.recordFilterHistory = (appliedFilters) => {
                    if (!this.filterableSelectionConfig.enableFilterHistory) {
                        return;
                    }
                    
                    const historyEntry = {
                        timestamp: new Date(),
                        filters: appliedFilters,
                        effectiveness: this.enhancedState.filterEffectiveness
                    };
                    
                    this.enhancedState.filterHistory.unshift(historyEntry);
                    
                    // 限制历史大小
                    if (this.enhancedState.filterHistory.length > 100) {
                        this.enhancedState.filterHistory.pop();
                    }
                };
                
                // 记录过滤统计
                this.recordFilterStatistics = (appliedFilters) => {
                    this.filterStatistics.totalFilters += appliedFilters.length;
                    
                    appliedFilters.forEach(filter => {
                        const count = this.filterStatistics.filtersByType.get(filter.type) || 0;
                        this.filterStatistics.filtersByType.set(filter.type, count + 1);
                        
                        const effectiveness = this.filterStatistics.filterEffectiveness.get(filter.type) || [];
                        effectiveness.push(filter.effectiveness);
                        this.filterStatistics.filterEffectiveness.set(filter.type, effectiveness);
                    });
                    
                    this.updateAverageFilteredOptions();
                };
                
                // 更新平均过滤选项数
                this.updateAverageFilteredOptions = () => {
                    if (this.filterStatistics.totalFilterableFields > 0) {
                        this.filterStatistics.averageFilteredOptions = 
                            this.filterStatistics.totalFilters / this.filterStatistics.totalFilterableFields;
                    }
                };
            }
            
            addFilterFeatures() {
                // 过滤功能
                this.filterManager = {
                    enabled: this.filterableSelectionConfig.enableDynamicFiltering,
                    getInfo: () => this.getFilterInfo(),
                    applyPreset: (preset) => this.applyFilterPreset(preset),
                    validate: () => this.validateFilterConfiguration()
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.filterableSelectionConfig.enableFilterValidation,
                    validate: () => this.validateFilterConfiguration(),
                    getErrors: () => this.enhancedState.validationErrors,
                    hasErrors: () => this.enhancedState.validationErrors.length > 0
                };
            }
            
            // 重写原始方法
            get options() {
                return this.enhancedGetOptions();
            }
        };
    }
    
    // 设置过滤器系统
    setupFilterSystem() {
        this.filterSystemConfig = {
            enabled: this.filterableSelectionConfig.enableDynamicFiltering,
            types: this.filterTypes,
            presets: this.filterPresets
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.filterableSelectionConfig.enableFilterValidation,
            rules: this.validationRules
        };
    }
    
    // 设置预设系统
    setupPresetSystem() {
        this.presetSystemConfig = {
            enabled: this.filterableSelectionConfig.enableFilterPresets,
            presets: this.filterPresets
        };
    }
    
    // 创建可过滤选择字段
    createFilterableSelectionField(props) {
        const field = new this.EnhancedFilterableSelectionField(props);
        this.filterStatistics.totalFilterableFields++;
        return field;
    }
    
    // 注册过滤器预设
    registerFilterPreset(name, preset) {
        this.filterPresets.set(name, preset);
    }
    
    // 获取最受欢迎的过滤器类型
    getMostUsedFilterType() {
        let maxCount = 0;
        let mostUsedType = null;
        
        for (const [type, count] of this.filterStatistics.filtersByType.entries()) {
            if (count > maxCount) {
                maxCount = count;
                mostUsedType = type;
            }
        }
        
        this.filterStatistics.mostUsedFilterType = mostUsedType;
        return mostUsedType;
    }
    
    // 获取过滤统计
    getFilterStatistics() {
        return {
            ...this.filterStatistics,
            mostUsedFilterType: this.getMostUsedFilterType(),
            filterTypeVariety: this.filterStatistics.filtersByType.size,
            averageEffectiveness: this.calculateAverageEffectiveness(),
            presetCount: this.filterPresets.size
        };
    }
    
    // 计算平均效果
    calculateAverageEffectiveness() {
        let totalEffectiveness = 0;
        let totalMeasurements = 0;
        
        for (const effectiveness of this.filterStatistics.filterEffectiveness.values()) {
            totalEffectiveness += effectiveness.reduce((sum, val) => sum + val, 0);
            totalMeasurements += effectiveness.length;
        }
        
        return totalMeasurements > 0 ? totalEffectiveness / totalMeasurements : 0;
    }
    
    // 销毁管理器
    destroy() {
        // 清理过滤器类型
        this.filterTypes.clear();
        
        // 清理预设
        this.filterPresets.clear();
        
        // 清理统计
        this.filterStatistics.filtersByType.clear();
        this.filterStatistics.filterEffectiveness.clear();
        
        // 重置统计
        this.filterStatistics = {
            totalFilterableFields: 0,
            totalFilters: 0,
            filtersByType: new Map(),
            filterEffectiveness: new Map(),
            averageFilteredOptions: 0,
            mostUsedFilterType: null
        };
    }
}

// 使用示例
const filterableSelectionManager = new FilterableSelectionFieldManager();

// 创建可过滤选择字段
const filterableSelectionField = filterableSelectionManager.createFilterableSelectionField({
    name: 'state',
    record: {
        data: { 
            state: 'draft',
            allowed_states: ['draft', 'confirmed', 'done']
        },
        fields: { 
            state: { 
                type: 'selection',
                selection: [
                    ['draft', 'Draft'],
                    ['confirmed', 'Confirmed'],
                    ['done', 'Done'],
                    ['cancelled', 'Cancelled']
                ]
            }
        }
    },
    whitelist_fname: 'allowed_states'
});

// 注册自定义预设
filterableSelectionManager.registerFilterPreset('workflow_states', {
    name: 'Workflow States Only',
    type: 'blacklist_values',
    values: ['cancelled', 'rejected', 'archived']
});

// 获取统计信息
const stats = filterableSelectionManager.getFilterStatistics();
console.log('Filterable selection field statistics:', stats);
```

## 技术特点

### 1. 灵活过滤
- **多种过滤方式**: 支持白名单字段、白名单值、黑名单值
- **动态过滤**: 基于记录字段的动态过滤
- **静态过滤**: 基于配置的静态过滤
- **当前值保护**: 始终保留当前选中的值

### 2. 优先级控制
- **过滤优先级**: 白名单字段 > 白名单值 > 黑名单值
- **互斥配置**: 同时只能使用一种过滤方式
- **配置验证**: 验证过滤配置的有效性
- **冲突检测**: 检测配置冲突

### 3. 继承设计
- **完全继承**: 继承基础选择字段的所有功能
- **选择性重写**: 只重写选项获取逻辑
- **配置扩展**: 扩展属性提取逻辑
- **向后兼容**: 保持与基础字段的兼容性

### 4. 多视图支持
- **视图特定**: 不同视图可以显示不同的选项子集
- **模型复用**: 同一模型在不同视图中的不同表现
- **配置隔离**: 每个视图的过滤配置独立
- **状态一致**: 保持数据状态的一致性

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 为基础选择字段添加过滤功能
- **选项装饰**: 装饰选项列表的获取逻辑
- **透明装饰**: 对外接口保持一致

### 2. 策略模式 (Strategy Pattern)
- **过滤策略**: 不同的过滤策略
- **白名单策略**: 白名单过滤策略
- **黑名单策略**: 黑名单过滤策略

### 3. 模板方法模式 (Template Method Pattern)
- **过滤模板**: 定义过滤的基本流程
- **策略选择**: 根据配置选择过滤策略
- **结果处理**: 统一处理过滤结果

### 4. 适配器模式 (Adapter Pattern)
- **配置适配**: 适配不同的过滤配置
- **数据适配**: 适配不同的数据格式
- **接口适配**: 适配基础字段接口

## 注意事项

1. **配置冲突**: 避免同时配置多种过滤方式
2. **性能考虑**: 大量选项时的过滤性能
3. **用户体验**: 确保过滤后仍有可选项
4. **数据一致性**: 保持过滤前后的数据一致性

## 扩展建议

1. **级联过滤**: 支持多级过滤条件
2. **条件过滤**: 支持基于条件的动态过滤
3. **过滤缓存**: 缓存过滤结果提高性能
4. **过滤历史**: 记录过滤操作历史
5. **过滤预设**: 提供常用过滤预设

该可过滤选择字段为Odoo Web客户端提供了灵活的选项过滤功能，通过多种过滤方式确保了在不同视图中显示合适的选项子集，提高了用户体验和系统的灵活性。

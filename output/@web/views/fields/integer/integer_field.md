# IntegerField - 整数字段

## 概述

`integer_field.js` 是 Odoo Web 客户端的整数字段组件，负责处理整数的输入、显示和格式化。该模块包含131行代码，是一个功能丰富的数值输入组件，专门用于处理integer类型的字段，具备数字格式化、人性化显示、数字键盘支持、步长控制等特性，是整数数据处理的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/integer/integer_field.js`
- **行数**: 131
- **模块**: `@web/views/fields/integer/integer_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/views/fields/formatters'          // 字段格式化器
'@web/views/fields/parsers'             // 字段解析器
'@web/views/fields/input_field_hook'    // 输入字段钩子
'@web/views/fields/standard_field_props' // 标准字段属性
'@web/views/fields/numpad_decimal_hook' // 数字键盘小数钩子
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const IntegerField = class IntegerField extends Component {
    static template = "web.IntegerField";
    static props = {
        ...standardFieldProps,
        formatNumber: { type: Boolean, optional: true },
        humanReadable: { type: Boolean, optional: true },
        decimals: { type: Number, optional: true },
        inputType: { type: String, optional: true },
        step: { type: Number, optional: true },
        placeholder: { type: String, optional: true },
    };
    static defaultProps = {
        formatNumber: true,
        humanReadable: false,
        inputType: "text",
        decimals: 0,
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **格式化控制**: 支持formatNumber控制数字格式化
- **人性化显示**: 支持humanReadable人性化格式
- **输入类型**: 支持inputType配置输入类型
- **步长控制**: 支持step配置数值步长
- **小数位数**: 支持decimals配置小数位数（用于人性化格式）

### 2. 组件初始化

```javascript
setup() {
    this.state = useState({
        hasFocus: false,
    });
    useInputField({
        getValue: () => this.formattedValue,
        refName: "numpadDecimal",
        parse: (v) => parseInteger(v),
    });
    useNumpadDecimal();
}
```

**初始化功能**:
- **状态管理**: 管理焦点状态
- **输入钩子**: 使用输入字段钩子管理输入
- **整数解析**: 使用parseInteger解析整数值
- **数字键盘**: 使用数字键盘小数钩子

### 3. 焦点处理

```javascript
onFocusIn() {
    this.state.hasFocus = true;
}

onFocusOut() {
    this.state.hasFocus = false;
}
```

**焦点功能**:
- **焦点进入**: 设置焦点状态为true
- **焦点离开**: 设置焦点状态为false
- **状态同步**: 同步焦点状态
- **格式切换**: 影响数值格式显示

### 4. 格式化显示

```javascript
get formattedValue() {
    if (
        !this.props.formatNumber ||
        (!this.props.readonly && this.props.inputType === "number")
    ) {
        return this.value;
    }
    if (this.props.humanReadable && !this.state.hasFocus) {
        return formatInteger(this.value, {
            humanReadable: true,
            decimals: this.props.decimals,
        });
    } else {
        return formatInteger(this.value, { humanReadable: false });
    }
}
```

**格式化功能**:
- **条件格式化**: 根据配置决定是否格式化
- **人性化格式**: 支持人性化数值显示（如500K代替500,000）
- **焦点感知**: 根据焦点状态调整格式
- **输入类型**: 根据输入类型调整格式化行为

### 5. 值获取

```javascript
get value() {
    return this.props.record.data[this.props.name];
}
```

**值获取功能**:
- **数据获取**: 从记录数据中获取整数值
- **简洁实现**: 简洁的值获取实现
- **直接访问**: 直接访问记录数据
- **类型安全**: 确保返回整数类型

### 6. 字段注册

```javascript
const integerField = {
    component: IntegerField,
    displayName: _t("Integer"),
    supportedOptions: [
        {
            label: _t("Format number"),
            name: "enable_formatting",
            type: "boolean",
            default: true,
        },
        {
            label: _t("Type"),
            name: "type",
            type: "string",
        },
        {
            label: _t("Step"),
            name: "step",
            type: "number",
        },
        {
            label: _t("User-friendly format"),
            name: "human_readable",
            type: "boolean",
        },
        {
            label: _t("Decimals"),
            name: "decimals",
            type: "number",
            default: 0,
        },
    ],
    supportedTypes: ["integer"],
    isEmpty: (record, fieldName) => record.data[fieldName] === false,
    extractProps: ({ attrs, options }) => ({
        formatNumber: options?.enable_formatting !== undefined ? Boolean(options.enable_formatting) : true,
        humanReadable: !!options.human_readable,
        inputType: options.type,
        step: options.step,
        placeholder: attrs.placeholder,
        decimals: options.decimals || 0,
    }),
};
```

**注册功能**:
- **组件注册**: 注册整数字段组件
- **类型支持**: 仅支持integer类型
- **选项配置**: 支持格式化、类型、步长、人性化等选项
- **空值检测**: 自定义空值检测逻辑（false视为空）
- **属性提取**: 智能提取和转换配置属性

## 使用场景

### 1. 整数字段管理器

```javascript
// 整数字段管理器
class IntegerFieldManager {
    constructor() {
        this.setupManager();
    }

    setupManager() {
        // 设置整数字段配置
        this.integerConfig = {
            enableValidation: true,
            enableFormatting: true,
            enableRangeCheck: true,
            enableCalculation: true,
            enableStatistics: true,
            enableHistoryTracking: true,
            enableBatchOperations: true,
            enableAutoIncrement: false
        };

        // 设置数值验证规则
        this.validationRules = {
            enableRangeValidation: true,
            enableTypeValidation: true,
            enableOverflowCheck: true,
            minValue: Number.MIN_SAFE_INTEGER,
            maxValue: Number.MAX_SAFE_INTEGER,
            allowNegative: true,
            allowZero: true
        };

        // 设置格式化选项
        this.formatOptions = new Map([
            ['standard', { thousandSeparator: ',', grouping: 3 }],
            ['european', { thousandSeparator: '.', grouping: 3 }],
            ['space', { thousandSeparator: ' ', grouping: 3 }],
            ['none', { thousandSeparator: '', grouping: 0 }],
            ['scientific', { notation: 'scientific' }]
        ]);

        // 设置人性化单位
        this.humanReadableUnits = [
            { threshold: 1e12, suffix: 'T', name: 'trillion' },
            { threshold: 1e9, suffix: 'B', name: 'billion' },
            { threshold: 1e6, suffix: 'M', name: 'million' },
            { threshold: 1e3, suffix: 'K', name: 'thousand' }
        ];

        // 设置整数统计
        this.integerStatistics = {
            totalFields: 0,
            totalValues: 0,
            averageValue: 0,
            minValue: null,
            maxValue: null,
            sumValue: 0,
            validationErrors: 0
        };

        this.initializeIntegerSystem();
    }
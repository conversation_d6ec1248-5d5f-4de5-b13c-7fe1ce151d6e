# Many2ManyTagsAvatarField - 多对多标签头像字段

## 概述

`many2many_tags_avatar_field.js` 是 Odoo Web 客户端的多对多标签头像字段组件，负责以带头像的标签形式显示和管理多对多关系。该模块包含168行代码，是一个功能丰富的关系字段组件，专门用于处理带头像的many2many类型字段，具备头像显示、弹出框编辑、列表视图、看板视图等特性，是用户关系管理的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/many2many_tags_avatar/many2many_tags_avatar_field.js`
- **行数**: 168
- **模块**: `@web/views/fields/many2many_tags_avatar/many2many_tags_avatar_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'            // 翻译服务
'@web/core/popover/popover_hook'        // 弹出框钩子
'@web/core/registry'                    // 注册表
'@web/views/fields/many2many_tags/many2many_tags_field' // 基础多对多标签字段
'@web/core/tags_list/tags_list'         // 标签列表组件
'@web/views/fields/relational_utils'    // 关系字段工具
'@web/core/utils/urls'                  // URL工具
```

## 核心功能

### 1. 基础头像标签字段

```javascript
const Many2ManyTagsAvatarField = class Many2ManyTagsAvatarField extends Many2ManyTagsField {
    static template = "web.Many2ManyTagsAvatarField";
    static components = {
        Many2XAutocomplete: AvatarMany2XAutocomplete,
        TagsList,
    };
    static props = {
        ...Many2ManyTagsField.props,
        withCommand: { type: Boolean, optional: true },
    };

    getTagProps(record) {
        return {
            ...super.getTagProps(record),
            img: imageUrl(this.relation, record.resId, "avatar_128"),
        };
    }
}
```

**基础特性**:
- **继承基类**: 继承Many2ManyTagsField的所有功能
- **头像自动完成**: 使用AvatarMany2XAutocomplete组件
- **头像URL**: 自动生成头像图片URL
- **命令支持**: 支持withCommand配置

### 2. 列表视图头像字段

```javascript
const ListMany2ManyTagsAvatarField = class ListMany2ManyTagsAvatarField extends Many2ManyTagsAvatarField {
    visibleItemsLimit = 5;
}
```

**列表特性**:
- **显示限制**: 限制显示5个头像标签
- **列表优化**: 专门为列表视图优化
- **空间控制**: 控制列表中的显示空间

### 3. 弹出框头像字段

```javascript
const Many2ManyTagsAvatarFieldPopover = class Many2ManyTagsAvatarFieldPopover extends Many2ManyTagsAvatarField {
    static template = "web.Many2ManyTagsAvatarFieldPopover";
    static props = {
        ...Many2ManyTagsAvatarField.props,
        close: { type: Function },
    };

    setup() {
        super.setup();
        const originalUpdate = this.update;
        this.update = async (recordList) => {
            await originalUpdate(recordList);
            await this._saveUpdate();
        };
    }

    async deleteTag(id) {
        await super.deleteTag(id);
        await this._saveUpdate();
    }

    async _saveUpdate() {
        await this.props.record.save({ reload: false });
        this.render();
        this.autoCompleteRef.el?.querySelector("input")?.click();
    }

    get tags() {
        return super.tags.reverse();
    }
}
```

**弹出框特性**:
- **自动保存**: 更新和删除后自动保存
- **手动渲染**: 手动触发重新渲染
- **标签反转**: 反转标签显示顺序
- **关闭回调**: 支持关闭回调函数
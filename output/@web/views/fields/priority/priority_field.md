# PriorityField - 优先级字段

## 概述

`priority_field.js` 是 Odoo Web 客户端的优先级字段组件，负责以星级形式显示和管理优先级数据。该模块包含117行代码，是一个功能完整的优先级处理组件，专门用于处理selection类型的优先级字段，具备星级显示、命令集成、自动保存、工具提示等特性，是任务管理和优先级控制的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/priority/priority_field.js`
- **行数**: 117
- **模块**: `@web/views/fields/priority/priority_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/commands/command_hook'       // 命令钩子
'@web/core/registry'                    // 注册表
'@web/core/l10n/translation'            // 翻译服务
'@web/views/fields/standard_field_props' // 标准字段属性
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const PriorityField = class PriorityField extends Component {
    static template = "web.PriorityField";
    static props = {
        ...standardFieldProps,
        withCommand: { type: Boolean, optional: true },
        autosave: { type: Boolean, optional: true },
    };

    setup() {
        this.state = useState({
            index: -1,
        });
        if (this.props.withCommand) {
            for (const command of this.commands) {
                useCommand(...command);
            }
        }
    }
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **命令集成**: 支持withCommand启用命令功能
- **自动保存**: 支持autosave自动保存功能
- **状态管理**: 管理当前选中的索引状态
- **命令注册**: 条件性注册命令

### 2. 命令系统

```javascript
get commands() {
    const commandName = _t("Set priority...");
    return [
        [
            commandName,
            () => {
                return {
                    placeholder: commandName,
                    providers: [
                        {
                            provide: () =>
                                this.options.map((value) => ({
                                    name: value[1],
                                    action: () => {
                                        this.updateRecord(value[0]);
                                    },
                                })),
                        },
                    ],
                };
            },
            { category: "smart_action", hotkey: "alt+r" },
        ],
    ];
}
```

**命令功能**:
- **命令名称**: 设置为"Set priority..."
- **选项提供**: 提供所有优先级选项
- **动作绑定**: 绑定更新记录动作
- **快捷键**: 支持Alt+R快捷键
- **智能动作**: 归类为智能动作

### 3. 选项管理

```javascript
get tooltipLabel() {
    return this.props.record.fields[this.props.name].string;
}

get options() {
    return Array.from(this.props.record.fields[this.props.name].selection);
}

get index() {
    return this.state.index > -1
        ? this.state.index
        : this.options.findIndex((o) => o[0] === this.props.record.data[this.props.name]);
}
```

**选项功能**:
- **工具提示**: 获取字段的显示标签
- **选项列表**: 从字段定义获取选择选项
- **索引计算**: 计算当前值的索引位置
- **状态优先**: 优先使用状态中的索引

### 4. 交互处理

```javascript
getTooltip(value) {
    return this.tooltipLabel && this.tooltipLabel !== value
        ? `${this.tooltipLabel}: ${value}`
        : value;
}

onStarClicked(value) {
    if (this.props.record.data[this.props.name] === value) {
        this.state.index = -1;
        this.updateRecord(this.options[0][0]);
    } else {
        this.updateRecord(value);
    }
}

async updateRecord(value) {
    await this.props.record.update({ [this.props.name]: value }, { save: this.props.autosave });
}
```

**交互功能**:
- **工具提示**: 生成详细的工具提示信息
- **星级点击**: 处理星级点击事件
- **切换逻辑**: 点击相同值时重置为最低优先级
- **记录更新**: 更新记录并可选自动保存

### 5. 字段注册

```javascript
const priorityField = {
    component: PriorityField,
    displayName: _t("Priority"),
    supportedOptions: [
        {
            label: _t("Autosave"),
            name: "autosave",
            type: "boolean",
            default: true,
            help: _t("If checked, the record will be saved immediately when the field is modified."),
        },
    ],
    supportedTypes: ["selection"],
    extractProps({ options, viewType }, dynamicInfo) {
        return {
            withCommand: viewType === "form",
            readonly: dynamicInfo.readonly,
            autosave: "autosave" in options ? !!options.autosave : true,
        };
    },
};
```

**注册功能**:
- **组件注册**: 注册优先级字段组件
- **显示名称**: 设置为"Priority"
- **自动保存选项**: 支持autosave选项配置
- **类型支持**: 仅支持selection类型
- **属性提取**: 根据视图类型和选项提取属性

## 使用场景

### 1. 优先级字段管理器

```javascript
// 优先级字段管理器
class PriorityFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置优先级字段配置
        this.priorityConfig = {
            enableCommands: true,
            enableAutoSave: true,
            enableTooltips: true,
            enableKeyboardShortcuts: true,
            enableColorCoding: true,
            enableAnimation: false,
            enableBulkUpdate: false,
            enableStatistics: true
        };
        
        // 设置优先级级别
        this.priorityLevels = new Map([
            ['0', { name: 'Normal', color: '#6c757d', icon: '☆', weight: 0 }],
            ['1', { name: 'Low', color: '#28a745', icon: '★', weight: 1 }],
            ['2', { name: 'Medium', color: '#ffc107', icon: '★★', weight: 2 }],
            ['3', { name: 'High', color: '#fd7e14', icon: '★★★', weight: 3 }],
            ['4', { name: 'Critical', color: '#dc3545', icon: '★★★★', weight: 4 }]
        ]);
        
        // 设置颜色主题
        this.colorThemes = new Map([
            ['default', { normal: '#6c757d', low: '#28a745', medium: '#ffc107', high: '#fd7e14', critical: '#dc3545' }],
            ['traffic', { normal: '#6c757d', low: '#28a745', medium: '#ffc107', high: '#fd7e14', critical: '#dc3545' }],
            ['blue', { normal: '#6c757d', low: '#17a2b8', medium: '#007bff', high: '#6610f2', critical: '#e83e8c' }],
            ['monochrome', { normal: '#f8f9fa', low: '#dee2e6', medium: '#adb5bd', high: '#6c757d', critical: '#343a40' }]
        ]);
        
        // 设置快捷键映射
        this.shortcutKeys = new Map([
            ['0', 'alt+0'],
            ['1', 'alt+1'],
            ['2', 'alt+2'],
            ['3', 'alt+3'],
            ['4', 'alt+4']
        ]);
        
        // 设置优先级统计
        this.priorityStatistics = {
            totalPriorityFields: 0,
            totalPriorityChanges: 0,
            priorityDistribution: new Map(),
            averagePriority: 0,
            commandUsage: 0,
            shortcutUsage: 0,
            autoSaveCount: 0
        };
        
        this.initializePrioritySystem();
    }
    
    // 初始化优先级系统
    initializePrioritySystem() {
        // 创建增强的优先级字段
        this.createEnhancedPriorityField();
        
        // 设置命令系统
        this.setupCommandSystem();
        
        // 设置快捷键系统
        this.setupShortcutSystem();
        
        // 设置主题系统
        this.setupThemeSystem();
    }
    
    // 创建增强的优先级字段
    createEnhancedPriorityField() {
        const originalField = PriorityField;
        
        this.EnhancedPriorityField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加主题功能
                this.addThemeFeatures();
                
                // 添加统计功能
                this.addStatisticsFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    ...this.state,
                    currentTheme: 'default',
                    isHovered: false,
                    isAnimating: false,
                    lastChangeTime: null,
                    changeHistory: []
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的工具提示
                this.enhancedGetTooltip = (value) => {
                    const baseTooltip = this.getTooltip(value);
                    const level = this.priorityLevels.get(value);
                    
                    if (level) {
                        return `${baseTooltip} (${level.name})`;
                    }
                    
                    return baseTooltip;
                };
                
                // 增强的星级点击
                this.enhancedOnStarClicked = (value) => {
                    const oldValue = this.props.record.data[this.props.name];
                    
                    // 记录变更历史
                    this.recordPriorityChange(oldValue, value);
                    
                    // 执行原始点击逻辑
                    this.onStarClicked(value);
                    
                    // 记录统计
                    this.recordStatistics('click');
                };
                
                // 增强的记录更新
                this.enhancedUpdateRecord = async (value) => {
                    try {
                        await this.updateRecord(value);
                        
                        if (this.props.autosave) {
                            this.priorityStatistics.autoSaveCount++;
                        }
                        
                        this.enhancedState.lastChangeTime = new Date();
                        
                    } catch (error) {
                        console.error('Priority update failed:', error);
                        this.handleUpdateError(error);
                    }
                };
                
                // 获取优先级颜色
                this.getPriorityColor = (value) => {
                    const theme = this.colorThemes.get(this.enhancedState.currentTheme);
                    const level = this.priorityLevels.get(value);
                    
                    if (level && theme) {
                        return level.color;
                    }
                    
                    return theme?.normal || '#6c757d';
                };
                
                // 获取优先级图标
                this.getPriorityIcon = (value) => {
                    const level = this.priorityLevels.get(value);
                    return level?.icon || '☆';
                };
                
                // 获取优先级权重
                this.getPriorityWeight = (value) => {
                    const level = this.priorityLevels.get(value);
                    return level?.weight || 0;
                };
                
                // 设置主题
                this.setTheme = (themeName) => {
                    if (this.colorThemes.has(themeName)) {
                        this.enhancedState.currentTheme = themeName;
                        this.applyTheme();
                    }
                };
                
                // 应用主题
                this.applyTheme = () => {
                    // 实现主题应用逻辑
                    const theme = this.colorThemes.get(this.enhancedState.currentTheme);
                    if (theme) {
                        // 更新CSS变量或样式
                        this.updateThemeStyles(theme);
                    }
                };
                
                // 更新主题样式
                this.updateThemeStyles = (theme) => {
                    const element = this.getPriorityElement();
                    if (element) {
                        element.style.setProperty('--priority-normal-color', theme.normal);
                        element.style.setProperty('--priority-low-color', theme.low);
                        element.style.setProperty('--priority-medium-color', theme.medium);
                        element.style.setProperty('--priority-high-color', theme.high);
                        element.style.setProperty('--priority-critical-color', theme.critical);
                    }
                };
                
                // 获取优先级元素
                this.getPriorityElement = () => {
                    return document.querySelector('.o_field_priority');
                };
                
                // 批量更新优先级
                this.batchUpdatePriority = async (records, value) => {
                    if (!this.priorityConfig.enableBulkUpdate) {
                        return;
                    }
                    
                    const results = [];
                    
                    for (const record of records) {
                        try {
                            await record.update({ [this.props.name]: value });
                            results.push({ record, success: true });
                        } catch (error) {
                            results.push({ record, success: false, error });
                        }
                    }
                    
                    return results;
                };
                
                // 比较优先级
                this.comparePriority = (value1, value2) => {
                    const weight1 = this.getPriorityWeight(value1);
                    const weight2 = this.getPriorityWeight(value2);
                    
                    return weight1 - weight2;
                };
                
                // 获取下一个优先级
                this.getNextPriority = (currentValue, direction = 'up') => {
                    const currentWeight = this.getPriorityWeight(currentValue);
                    const options = Array.from(this.priorityLevels.entries())
                        .sort((a, b) => a[1].weight - b[1].weight);
                    
                    const currentIndex = options.findIndex(([key]) => key === currentValue);
                    
                    if (direction === 'up' && currentIndex < options.length - 1) {
                        return options[currentIndex + 1][0];
                    } else if (direction === 'down' && currentIndex > 0) {
                        return options[currentIndex - 1][0];
                    }
                    
                    return currentValue;
                };
                
                // 记录优先级变更
                this.recordPriorityChange = (oldValue, newValue) => {
                    const change = {
                        from: oldValue,
                        to: newValue,
                        timestamp: new Date(),
                        method: 'click'
                    };
                    
                    this.enhancedState.changeHistory.unshift(change);
                    
                    // 限制历史大小
                    if (this.enhancedState.changeHistory.length > 50) {
                        this.enhancedState.changeHistory.pop();
                    }
                };
                
                // 获取优先级信息
                this.getPriorityInfo = () => {
                    const currentValue = this.props.record.data[this.props.name];
                    const level = this.priorityLevels.get(currentValue);
                    
                    return {
                        value: currentValue,
                        level: level,
                        color: this.getPriorityColor(currentValue),
                        icon: this.getPriorityIcon(currentValue),
                        weight: this.getPriorityWeight(currentValue),
                        theme: this.enhancedState.currentTheme,
                        changeHistory: this.enhancedState.changeHistory,
                        lastChangeTime: this.enhancedState.lastChangeTime
                    };
                };
                
                // 记录统计
                this.recordStatistics = (method) => {
                    this.priorityStatistics.totalPriorityChanges++;
                    
                    if (method === 'command') {
                        this.priorityStatistics.commandUsage++;
                    } else if (method === 'shortcut') {
                        this.priorityStatistics.shortcutUsage++;
                    }
                    
                    // 记录优先级分布
                    const currentValue = this.props.record.data[this.props.name];
                    const count = this.priorityStatistics.priorityDistribution.get(currentValue) || 0;
                    this.priorityStatistics.priorityDistribution.set(currentValue, count + 1);
                    
                    // 更新平均优先级
                    this.updateAveragePriority();
                };
                
                // 更新平均优先级
                this.updateAveragePriority = () => {
                    let totalWeight = 0;
                    let totalCount = 0;
                    
                    for (const [value, count] of this.priorityStatistics.priorityDistribution.entries()) {
                        const weight = this.getPriorityWeight(value);
                        totalWeight += weight * count;
                        totalCount += count;
                    }
                    
                    this.priorityStatistics.averagePriority = totalCount > 0 ? totalWeight / totalCount : 0;
                };
                
                // 处理更新错误
                this.handleUpdateError = (error) => {
                    console.error('Priority update error:', error);
                    // 实现错误处理逻辑
                };
                
                // 鼠标悬停处理
                this.onMouseEnter = () => {
                    this.enhancedState.isHovered = true;
                };
                
                // 鼠标离开处理
                this.onMouseLeave = () => {
                    this.enhancedState.isHovered = false;
                };
            }
            
            addThemeFeatures() {
                // 主题功能
                this.themeManager = {
                    enabled: this.priorityConfig.enableColorCoding,
                    setTheme: (theme) => this.setTheme(theme),
                    getColor: (value) => this.getPriorityColor(value),
                    getCurrentTheme: () => this.enhancedState.currentTheme,
                    getAvailableThemes: () => Array.from(this.colorThemes.keys())
                };
            }
            
            addStatisticsFeatures() {
                // 统计功能
                this.statisticsManager = {
                    enabled: this.priorityConfig.enableStatistics,
                    record: (method) => this.recordStatistics(method),
                    getInfo: () => this.getPriorityInfo(),
                    getHistory: () => this.enhancedState.changeHistory,
                    compare: (value1, value2) => this.comparePriority(value1, value2)
                };
            }
            
            // 重写原始方法
            getTooltip(value) {
                return this.enhancedGetTooltip(value);
            }
            
            onStarClicked(value) {
                return this.enhancedOnStarClicked(value);
            }
            
            async updateRecord(value) {
                return this.enhancedUpdateRecord(value);
            }
        };
    }
    
    // 设置命令系统
    setupCommandSystem() {
        this.commandSystemConfig = {
            enabled: this.priorityConfig.enableCommands,
            commandName: 'Set priority...',
            category: 'smart_action',
            hotkey: 'alt+r'
        };
    }
    
    // 设置快捷键系统
    setupShortcutSystem() {
        this.shortcutSystemConfig = {
            enabled: this.priorityConfig.enableKeyboardShortcuts,
            shortcuts: this.shortcutKeys
        };
    }
    
    // 设置主题系统
    setupThemeSystem() {
        this.themeSystemConfig = {
            enabled: this.priorityConfig.enableColorCoding,
            themes: this.colorThemes,
            defaultTheme: 'default'
        };
    }
    
    // 创建优先级字段
    createPriorityField(props) {
        const field = new this.EnhancedPriorityField(props);
        this.priorityStatistics.totalPriorityFields++;
        return field;
    }
    
    // 批量创建优先级字段
    batchCreatePriorityFields(propsArray) {
        const results = [];
        
        for (const props of propsArray) {
            try {
                const field = this.createPriorityField(props);
                results.push({ field, success: true, props });
            } catch (error) {
                results.push({ field: null, success: false, error, props });
            }
        }
        
        return results;
    }
    
    // 注册优先级级别
    registerPriorityLevel(key, config) {
        this.priorityLevels.set(key, config);
    }
    
    // 注册颜色主题
    registerColorTheme(name, colors) {
        this.colorThemes.set(name, colors);
    }
    
    // 获取优先级分布
    getPriorityDistribution() {
        const distribution = [];
        
        for (const [value, count] of this.priorityStatistics.priorityDistribution.entries()) {
            const level = this.priorityLevels.get(value);
            distribution.push({
                value: value,
                name: level?.name || 'Unknown',
                count: count,
                percentage: (count / this.priorityStatistics.totalPriorityChanges * 100).toFixed(1)
            });
        }
        
        return distribution.sort((a, b) => parseInt(a.value) - parseInt(b.value));
    }
    
    // 获取优先级统计
    getPriorityStatistics() {
        return {
            ...this.priorityStatistics,
            commandUsageRate: (this.priorityStatistics.commandUsage / Math.max(this.priorityStatistics.totalPriorityChanges, 1)) * 100,
            shortcutUsageRate: (this.priorityStatistics.shortcutUsage / Math.max(this.priorityStatistics.totalPriorityChanges, 1)) * 100,
            autoSaveRate: (this.priorityStatistics.autoSaveCount / Math.max(this.priorityStatistics.totalPriorityChanges, 1)) * 100,
            averageChangesPerField: this.priorityStatistics.totalPriorityChanges / Math.max(this.priorityStatistics.totalPriorityFields, 1),
            priorityDistribution: this.getPriorityDistribution(),
            levelCount: this.priorityLevels.size,
            themeCount: this.colorThemes.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理优先级级别
        this.priorityLevels.clear();
        
        // 清理颜色主题
        this.colorThemes.clear();
        
        // 清理快捷键
        this.shortcutKeys.clear();
        
        // 清理优先级分布
        this.priorityStatistics.priorityDistribution.clear();
        
        // 重置统计
        this.priorityStatistics = {
            totalPriorityFields: 0,
            totalPriorityChanges: 0,
            priorityDistribution: new Map(),
            averagePriority: 0,
            commandUsage: 0,
            shortcutUsage: 0,
            autoSaveCount: 0
        };
    }
}

// 使用示例
const priorityManager = new PriorityFieldManager();

// 创建优先级字段
const priorityField = priorityManager.createPriorityField({
    name: 'priority',
    record: {
        data: { priority: '2' },
        fields: { 
            priority: { 
                type: 'selection',
                selection: [
                    ['0', 'Normal'],
                    ['1', 'Low'],
                    ['2', 'Medium'],
                    ['3', 'High'],
                    ['4', 'Critical']
                ],
                string: 'Priority'
            }
        }
    },
    withCommand: true,
    autosave: true
});

// 注册自定义优先级级别
priorityManager.registerPriorityLevel('5', {
    name: 'Urgent',
    color: '#6f42c1',
    icon: '★★★★★',
    weight: 5
});

// 注册自定义主题
priorityManager.registerColorTheme('custom', {
    normal: '#f8f9fa',
    low: '#d4edda',
    medium: '#fff3cd',
    high: '#f8d7da',
    critical: '#f5c6cb'
});

// 获取统计信息
const stats = priorityManager.getPriorityStatistics();
console.log('Priority field statistics:', stats);
```

## 技术特点

### 1. 星级显示
- **视觉直观**: 使用星级直观显示优先级
- **交互友好**: 点击星级即可更改优先级
- **状态管理**: 管理当前选中状态
- **工具提示**: 提供详细的工具提示信息

### 2. 命令集成
- **命令系统**: 集成到命令面板
- **快捷键**: 支持Alt+R快捷键
- **智能动作**: 归类为智能动作
- **选项提供**: 动态提供所有优先级选项

### 3. 自动保存
- **可配置**: 支持autosave选项配置
- **即时保存**: 修改后立即保存记录
- **默认启用**: 默认启用自动保存
- **性能优化**: 优化保存性能

### 4. 选择字段
- **Selection类型**: 专门处理selection类型字段
- **动态选项**: 从字段定义动态获取选项
- **索引管理**: 智能管理选项索引
- **切换逻辑**: 智能的优先级切换逻辑

## 设计模式

### 1. 状态模式 (State Pattern)
- **优先级状态**: 管理不同的优先级状态
- **索引状态**: 管理当前选中索引状态
- **交互状态**: 管理用户交互状态

### 2. 命令模式 (Command Pattern)
- **命令封装**: 封装优先级设置命令
- **动作绑定**: 绑定具体的更新动作
- **撤销支持**: 支持命令撤销功能

### 3. 观察者模式 (Observer Pattern)
- **状态观察**: 观察优先级状态变化
- **记录观察**: 观察记录数据变化
- **UI观察**: 观察UI交互变化

### 4. 策略模式 (Strategy Pattern)
- **显示策略**: 不同的优先级显示策略
- **保存策略**: 不同的保存策略
- **交互策略**: 不同的用户交互策略

## 注意事项

1. **选项验证**: 确保选择选项的有效性
2. **状态同步**: 保持UI状态与数据同步
3. **性能考虑**: 避免频繁的记录更新
4. **用户体验**: 提供清晰的优先级指示

## 扩展建议

1. **颜色编码**: 支持优先级颜色编码
2. **批量操作**: 支持批量优先级更新
3. **历史记录**: 记录优先级变更历史
4. **统计分析**: 添加优先级统计分析
5. **自定义级别**: 支持自定义优先级级别

该优先级字段为Odoo Web客户端提供了直观的优先级管理功能，通过星级显示和命令集成确保了高效的优先级控制和良好的用户体验。

# JournalDashboardGraphField - 日志仪表板图表字段

## 概述

`journal_dashboard_graph_field.js` 是 Odoo Web 客户端的日志仪表板图表字段组件，负责在仪表板中显示财务日志相关的图表数据。该模块包含186行代码，是一个基于Chart.js的专业图表组件，专门用于显示财务数据的可视化图表，具备图表渲染、颜色主题、数据处理、响应式布局等特性，是财务仪表板的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/journal_dashboard_graph/journal_dashboard_graph_field.js`
- **行数**: 186
- **模块**: `@web/views/fields/journal_dashboard_graph/journal_dashboard_graph_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/assets'                      // 资源加载
'@web/core/registry'                    // 注册表
'@web/core/colors/colors'               // 颜色系统
'@web/views/fields/standard_field_props' // 标准字段属性
'@odoo/owl'                            // OWL框架
'@web/core/browser/cookie'              // Cookie管理
```

## 核心功能

### 1. 颜色主题配置

```javascript
const colorScheme = cookie.get("color_scheme");
const GRAPH_GRID_COLOR = getCustomColor(colorScheme, "#d8dadd", "#3C3E4B");
const GRAPH_LABEL_COLOR = getCustomColor(colorScheme, "#111827", "#E4E4E4");
```

**主题功能**:
- **主题检测**: 从Cookie中获取颜色主题
- **自适应颜色**: 根据主题自动调整图表颜色
- **网格颜色**: 配置图表网格线颜色
- **标签颜色**: 配置图表标签文字颜色

### 2. 组件定义

```javascript
const JournalDashboardGraphField = class JournalDashboardGraphField extends Component {
    static template = "web.JournalDashboardGraphField";
    static props = {
        ...standardFieldProps,
        graphType: String,
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **图表类型**: 支持graphType配置图表类型
- **专用模板**: 使用专用的图表模板
- **Chart.js集成**: 基于Chart.js图表库

### 3. 组件初始化

```javascript
setup() {
    this.chart = null;
    this.canvasRef = useRef("canvas");
    this.data = JSON.parse(this.props.record.data[this.props.name]);

    onWillStart(async () => await loadBundle("web.chartjs_lib"));

    useEffect(() => {
        this.renderChart();
        return () => {
            if (this.chart) {
                this.chart.destroy();
            }
        };
    });
}
```

**初始化功能**:
- **图表实例**: 初始化图表实例变量
- **画布引用**: 创建画布元素引用
- **数据解析**: 解析JSON格式的图表数据
- **资源加载**: 异步加载Chart.js库
- **生命周期**: 管理图表的创建和销毁

### 4. 图表渲染

```javascript
renderChart() {
    if (this.chart) {
        this.chart.destroy();
    }

    const config = {
        type: this.props.graphType,
        data: this.data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            // ... 更多配置选项
        },
    };

    this.chart = new Chart(this.canvasRef.el, config);
}
```

**渲染功能**:
- **图表销毁**: 销毁旧图表实例
- **配置构建**: 构建图表配置对象
- **类型支持**: 支持多种图表类型
- **响应式**: 支持响应式布局
- **Chart.js创建**: 创建Chart.js图表实例

### 5. 字段注册

```javascript
const journalDashboardGraphField = {
    component: JournalDashboardGraphField,
    supportedTypes: ["text"],
    extractProps: ({ attrs }) => ({
        graphType: attrs.graph_type,
    }),
};

registry.category("fields").add("journal_dashboard_graph", journalDashboardGraphField);
```

**注册功能**:
- **组件注册**: 注册日志仪表板图表字段
- **类型支持**: 支持text类型（JSON数据）
- **属性提取**: 提取图表类型属性
- **字段注册**: 注册为journal_dashboard_graph字段类型
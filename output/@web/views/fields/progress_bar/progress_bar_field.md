# ProgressBarField - 进度条字段

## 概述

`progress_bar_field.js` 是 Odoo Web 客户端的进度条字段组件，负责以进度条形式显示和编辑数值进度。该模块包含163行代码，是一个功能完整的进度可视化组件，专门用于处理integer和float类型的进度字段，具备进度条显示、值编辑、最大值配置、溢出处理等特性，是数据可视化和进度管理的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/progress_bar/progress_bar_field.js`
- **行数**: 163
- **模块**: `@web/views/fields/progress_bar/progress_bar_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/views/fields/numpad_decimal_hook' // 数字键盘小数钩子
'@web/views/fields/parsers'             // 字段解析器
'@web/views/fields/standard_field_props' // 标准字段属性
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const ProgressBarField = class ProgressBarField extends Component {
    static template = "web.ProgressBarField";
    static props = {
        ...standardFieldProps,
        maxValueField: { type: [String, Number], optional: true },
        currentValueField: { type: String, optional: true },
        isEditable: { type: Boolean, optional: true },
        isCurrentValueEditable: { type: Boolean, optional: true },
        isMaxValueEditable: { type: Boolean, optional: true },
        title: { type: String, optional: true },
        overflowClass: { type: String, optional: true },
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **最大值字段**: 支持maxValueField配置最大值字段
- **当前值字段**: 支持currentValueField配置当前值字段
- **编辑控制**: 支持多种编辑权限控制
- **标题配置**: 支持title配置标题
- **溢出样式**: 支持overflowClass配置溢出样式

### 2. 组件初始化

```javascript
setup() {
    useNumpadDecimal();
    this.root = useRef("numpadDecimal");
    this.maxValueRef = useRef("maxValue");
    this.currentValueRef = useRef("currentValue");

    const { currentValueField, maxValueField, name } = this.props;
    this.currentValueField = currentValueField ? currentValueField : name;
    if (maxValueField) {
        this.maxValueField = maxValueField;
    }

    this.state = useState({
        isEditing: false,
    });
}
```

**初始化功能**:
- **数字键盘**: 使用数字键盘小数钩子
- **引用管理**: 管理各种DOM元素引用
- **字段配置**: 配置当前值和最大值字段
- **状态管理**: 管理编辑状态

### 3. 值计算和显示

```javascript
get isEditable() {
    return this.props.isEditable && !this.props.readonly;
}

get isPercentage() {
    return !this.props.maxValueField || !isNaN(this.props.maxValueField);
}

get currentValue() {
    return this.props.record.data[this.currentValueField] || 0;
}

get maxValue() {
    return this.props.record.data[this.maxValueField] || 100;
}

get progressBarColorClass() {
    return this.currentValue > this.maxValue ? this.props.overflowClass : "bg-primary";
}
```

**值计算功能**:
- **编辑权限**: 计算是否可编辑
- **百分比模式**: 判断是否为百分比模式
- **当前值**: 获取当前进度值
- **最大值**: 获取最大进度值
- **颜色样式**: 根据溢出状态选择颜色

### 4. 格式化显示

```javascript
formatCurrentValue(humanReadable = !this.state.isEditing) {
    const formatter = formatters.get(Number.isInteger(this.currentValue) ? "integer" : "float");
    return formatter(this.currentValue, { humanReadable });
}

formatMaxValue(humanReadable = !this.state.isEditing) {
    const formatter = formatters.get(Number.isInteger(this.maxValue) ? "integer" : "float");
    return formatter(this.maxValue, { humanReadable });
}
```

**格式化功能**:
- **智能格式化**: 根据数值类型选择格式化器
- **人性化显示**: 支持人性化显示模式
- **编辑模式**: 编辑时使用原始格式
- **类型检测**: 自动检测整数或浮点数

### 5. 值变更处理

```javascript
onValueChange(value, fieldName) {
    let parsedValue;
    try {
        parsedValue = parseFloat(value);
    } catch {
        this.props.record.setInvalidField(this.props.name);
        return;
    }

    if (this.props.record.fields[fieldName].type === "integer") {
        parsedValue = Math.floor(parsedValue);
    }
    this.props.record.update({ [fieldName]: parsedValue }, { save: this.props.readonly });
}

onCurrentValueChange(ev) {
    this.onValueChange(ev.target.value, this.currentValueField);
}

onMaxValueChange(ev) {
    this.onValueChange(ev.target.value, this.maxValueField);
}
```

**变更处理功能**:
- **值解析**: 解析输入的数值
- **错误处理**: 处理解析错误
- **类型转换**: 根据字段类型转换值
- **记录更新**: 更新记录数据
- **分别处理**: 分别处理当前值和最大值变更

### 6. 编辑状态管理

```javascript
onInputBlur() {
    if (
        document.activeElement !== this.maxValueRef.el &&
        document.activeElement !== this.currentValueRef.el
    ) {
        this.state.isEditing = false;
    }
}

onInputFocus() {
    this.state.isEditing = true;
}
```

**状态管理功能**:
- **焦点检测**: 检测输入框焦点状态
- **编辑状态**: 管理编辑状态切换
- **多输入框**: 处理多个输入框的焦点
- **状态同步**: 同步编辑状态与UI显示

### 7. 字段注册

```javascript
const progressBarField = {
    component: ProgressBarField,
    displayName: _t("Progress Bar"),
    supportedOptions: [
        {
            label: _t("Can edit value"),
            name: "editable",
            type: "boolean",
        },
        {
            label: _t("Can edit max value"),
            name: "edit_max_value",
            type: "boolean",
        },
        {
            label: _t("Current value field"),
            name: "current_value",
            type: "field",
            availableTypes: ["integer", "float"],
        },
        {
            label: _t("Max value field"),
            name: "max_value",
            type: "field",
            availableTypes: ["integer", "float"],
        },
        {
            label: _t("Overflow style"),
            name: "overflow_class",
            type: "string",
            default: "bg-secondary",
        },
    ],
    supportedTypes: ["integer", "float"],
    extractProps: ({ attrs, options }) => ({
        maxValueField: options.max_value,
        currentValueField: options.current_value,
        isEditable: !options.readonly && options.editable,
        isCurrentValueEditable: options.editable && !options.edit_max_value,
        isMaxValueEditable: options.editable && options.edit_max_value,
        title: attrs.title,
        overflowClass: options.overflow_class || "bg-secondary",
    }),
};
```

**注册功能**:
- **组件注册**: 注册进度条字段组件
- **显示名称**: 设置为"Progress Bar"
- **丰富选项**: 支持多种配置选项
- **类型支持**: 支持integer和float类型
- **属性提取**: 提取各种配置属性
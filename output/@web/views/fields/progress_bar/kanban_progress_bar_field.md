# KanbanProgressBarField - 看板进度条字段

## 概述

`kanban_progress_bar_field.js` 是 Odoo Web 客户端的看板进度条字段组件，是专门为看板视图优化的进度条字段。该模块包含21行代码，是一个简洁的看板专用组件，继承自基础进度条字段，专门用于看板视图中的进度显示，具备看板优化、编辑权限调整等特性，是看板视图进度管理的专用组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/progress_bar/kanban_progress_bar_field.js`
- **行数**: 21
- **模块**: `@web/views/fields/progress_bar/kanban_progress_bar_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                    // 注册表
'@web/views/fields/progress_bar/progress_bar_field' // 基础进度条字段
```

## 核心功能

### 1. 组件定义

```javascript
const KanbanProgressBarField = class KanbanProgressBarField extends ProgressBarField {
    get isEditable() {
        return this.props.isEditable;
    }
}
```

**组件特性**:
- **继承基类**: 继承ProgressBarField的所有功能
- **编辑权限**: 重写编辑权限逻辑
- **看板优化**: 专门为看板视图优化
- **简化逻辑**: 简化编辑权限判断

### 2. 字段注册

```javascript
const kanbanProgressBarField = {
    ...progressBarField,
    component: KanbanProgressBarField,
};

registry.category("fields").add("kanban.progressbar", kanbanProgressBarField);
```

**注册功能**:
- **配置继承**: 继承基础进度条字段的所有配置
- **组件替换**: 使用看板专用组件
- **看板注册**: 注册为kanban.progressbar字段类型
- **命名空间**: 使用kanban前缀区分看板字段

## 使用场景

### 1. 看板进度条字段管理器

```javascript
// 看板进度条字段管理器
class KanbanProgressBarFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置看板进度条配置
        this.kanbanProgressConfig = {
            enableCompactMode: true,
            enableQuickEdit: true,
            enableTooltips: true,
            enableColorCoding: true,
            enableAnimation: false,
            enableBatchUpdate: false,
            enableDragProgress: false,
            enableMiniMode: true
        };
        
        // 设置看板显示选项
        this.kanbanDisplayOptions = {
            showPercentage: true,
            showValues: false,
            compactHeight: 8,
            normalHeight: 16,
            enableGradient: false,
            enableStripes: false,
            cornerRadius: 4
        };
        
        // 设置看板颜色主题
        this.kanbanColorThemes = new Map([
            ['default', { primary: '#007bff', success: '#28a745', warning: '#ffc107', danger: '#dc3545' }],
            ['minimal', { primary: '#6c757d', success: '#6c757d', warning: '#6c757d', danger: '#6c757d' }],
            ['vibrant', { primary: '#6f42c1', success: '#20c997', warning: '#fd7e14', danger: '#e83e8c' }]
        ]);
        
        // 设置看板交互配置
        this.kanbanInteractionConfig = {
            enableHover: true,
            enableClick: true,
            enableDoubleClick: false,
            enableContextMenu: false,
            hoverDelay: 200,
            clickThreshold: 300
        };
        
        // 设置看板统计
        this.kanbanProgressStatistics = {
            totalKanbanProgressFields: 0,
            totalProgressUpdates: 0,
            averageProgress: 0,
            completedTasks: 0,
            inProgressTasks: 0,
            notStartedTasks: 0
        };
        
        this.initializeKanbanProgressSystem();
    }
    
    // 初始化看板进度系统
    initializeKanbanProgressSystem() {
        // 创建增强的看板进度条字段
        this.createEnhancedKanbanProgressBarField();
        
        // 设置看板显示系统
        this.setupKanbanDisplaySystem();
        
        // 设置看板交互系统
        this.setupKanbanInteractionSystem();
        
        // 设置看板主题系统
        this.setupKanbanThemeSystem();
    }
    
    // 创建增强的看板进度条字段
    createEnhancedKanbanProgressBarField() {
        const originalField = KanbanProgressBarField;
        
        this.EnhancedKanbanProgressBarField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加看板增强功能
                this.addKanbanEnhancedFeatures();
                
                // 添加看板显示功能
                this.addKanbanDisplayFeatures();
                
                // 添加看板交互功能
                this.addKanbanInteractionFeatures();
            }
            
            addKanbanEnhancedFeatures() {
                // 扩展看板状态
                this.kanbanState = {
                    isCompact: false,
                    isMini: false,
                    isHovered: false,
                    currentTheme: 'default',
                    animationEnabled: false,
                    lastUpdateTime: null
                };
                
                // 添加看板增强方法
                this.addKanbanEnhancedMethods();
            }
            
            addKanbanEnhancedMethods() {
                // 增强的编辑权限
                this.enhancedIsEditable = () => {
                    // 看板视图中的编辑权限更简单
                    return this.props.isEditable && this.kanbanProgressConfig.enableQuickEdit;
                };
                
                // 获取看板进度条样式
                this.getKanbanProgressBarStyle = () => {
                    const height = this.kanbanState.isCompact ? 
                        this.kanbanDisplayOptions.compactHeight : 
                        this.kanbanDisplayOptions.normalHeight;
                    
                    const borderRadius = this.kanbanDisplayOptions.cornerRadius;
                    
                    return {
                        height: `${height}px`,
                        borderRadius: `${borderRadius}px`,
                        transition: this.kanbanState.animationEnabled ? 'all 0.3s ease' : 'none'
                    };
                };
                
                // 获取看板进度百分比
                this.getKanbanProgressPercentage = () => {
                    const current = this.currentValue;
                    const max = this.maxValue;
                    
                    if (max === 0) return 0;
                    
                    const percentage = (current / max) * 100;
                    return Math.min(100, Math.max(0, percentage));
                };
                
                // 获取看板进度状态
                this.getKanbanProgressStatus = () => {
                    const percentage = this.getKanbanProgressPercentage();
                    
                    if (percentage === 0) return 'not-started';
                    if (percentage === 100) return 'completed';
                    return 'in-progress';
                };
                
                // 获取看板进度颜色
                this.getKanbanProgressColor = () => {
                    const status = this.getKanbanProgressStatus();
                    const theme = this.kanbanColorThemes.get(this.kanbanState.currentTheme);
                    
                    switch (status) {
                        case 'completed':
                            return theme.success;
                        case 'in-progress':
                            return theme.primary;
                        case 'not-started':
                            return theme.warning;
                        default:
                            return theme.primary;
                    }
                };
                
                // 设置紧凑模式
                this.setCompactMode = (isCompact) => {
                    this.kanbanState.isCompact = isCompact;
                    this.updateKanbanDisplay();
                };
                
                // 设置迷你模式
                this.setMiniMode = (isMini) => {
                    this.kanbanState.isMini = isMini;
                    this.updateKanbanDisplay();
                };
                
                // 更新看板显示
                this.updateKanbanDisplay = () => {
                    const element = this.getKanbanProgressElement();
                    if (element) {
                        const style = this.getKanbanProgressBarStyle();
                        Object.assign(element.style, style);
                        
                        // 更新颜色
                        const color = this.getKanbanProgressColor();
                        element.style.backgroundColor = color;
                    }
                };
                
                // 获取看板进度元素
                this.getKanbanProgressElement = () => {
                    return document.querySelector('.o_kanban_progress_bar');
                };
                
                // 看板进度点击处理
                this.onKanbanProgressClick = (event) => {
                    if (!this.kanbanInteractionConfig.enableClick) return;
                    
                    if (this.enhancedIsEditable()) {
                        this.startKanbanQuickEdit(event);
                    }
                };
                
                // 开始看板快速编辑
                this.startKanbanQuickEdit = (event) => {
                    const rect = event.currentTarget.getBoundingClientRect();
                    const clickX = event.clientX - rect.left;
                    const percentage = (clickX / rect.width) * 100;
                    
                    const newValue = Math.round((percentage / 100) * this.maxValue);
                    this.updateKanbanProgress(newValue);
                };
                
                // 更新看板进度
                this.updateKanbanProgress = async (newValue) => {
                    try {
                        await this.props.record.update({ 
                            [this.currentValueField]: newValue 
                        });
                        
                        this.kanbanState.lastUpdateTime = new Date();
                        this.recordKanbanProgressUpdate();
                        
                    } catch (error) {
                        console.error('Kanban progress update failed:', error);
                    }
                };
                
                // 看板鼠标悬停处理
                this.onKanbanMouseEnter = () => {
                    if (!this.kanbanInteractionConfig.enableHover) return;
                    
                    this.kanbanState.isHovered = true;
                    
                    if (this.kanbanProgressConfig.enableTooltips) {
                        this.showKanbanTooltip();
                    }
                };
                
                // 看板鼠标离开处理
                this.onKanbanMouseLeave = () => {
                    this.kanbanState.isHovered = false;
                    this.hideKanbanTooltip();
                };
                
                // 显示看板工具提示
                this.showKanbanTooltip = () => {
                    const percentage = this.getKanbanProgressPercentage();
                    const current = this.currentValue;
                    const max = this.maxValue;
                    
                    let tooltipText = `${percentage.toFixed(1)}%`;
                    
                    if (this.kanbanDisplayOptions.showValues) {
                        tooltipText += ` (${current}/${max})`;
                    }
                    
                    // 实现工具提示显示逻辑
                    this.displayKanbanTooltip(tooltipText);
                };
                
                // 隐藏看板工具提示
                this.hideKanbanTooltip = () => {
                    // 实现工具提示隐藏逻辑
                };
                
                // 显示看板工具提示
                this.displayKanbanTooltip = (text) => {
                    // 实现具体的工具提示显示
                    console.log('Kanban tooltip:', text);
                };
                
                // 获取看板进度信息
                this.getKanbanProgressInfo = () => {
                    return {
                        currentValue: this.currentValue,
                        maxValue: this.maxValue,
                        percentage: this.getKanbanProgressPercentage(),
                        status: this.getKanbanProgressStatus(),
                        color: this.getKanbanProgressColor(),
                        isCompact: this.kanbanState.isCompact,
                        isMini: this.kanbanState.isMini,
                        isHovered: this.kanbanState.isHovered,
                        theme: this.kanbanState.currentTheme,
                        lastUpdateTime: this.kanbanState.lastUpdateTime
                    };
                };
                
                // 记录看板进度更新
                this.recordKanbanProgressUpdate = () => {
                    this.kanbanProgressStatistics.totalProgressUpdates++;
                    
                    const status = this.getKanbanProgressStatus();
                    switch (status) {
                        case 'completed':
                            this.kanbanProgressStatistics.completedTasks++;
                            break;
                        case 'in-progress':
                            this.kanbanProgressStatistics.inProgressTasks++;
                            break;
                        case 'not-started':
                            this.kanbanProgressStatistics.notStartedTasks++;
                            break;
                    }
                    
                    // 更新平均进度
                    this.updateAverageProgress();
                };
                
                // 更新平均进度
                this.updateAverageProgress = () => {
                    const totalFields = this.kanbanProgressStatistics.totalKanbanProgressFields;
                    if (totalFields > 0) {
                        const totalProgress = this.kanbanProgressStatistics.completedTasks * 100 +
                                            this.kanbanProgressStatistics.inProgressTasks * 50;
                        this.kanbanProgressStatistics.averageProgress = totalProgress / totalFields;
                    }
                };
            }
            
            addKanbanDisplayFeatures() {
                // 看板显示功能
                this.kanbanDisplayManager = {
                    enabled: true,
                    setCompact: (isCompact) => this.setCompactMode(isCompact),
                    setMini: (isMini) => this.setMiniMode(isMini),
                    getStyle: () => this.getKanbanProgressBarStyle(),
                    getColor: () => this.getKanbanProgressColor(),
                    update: () => this.updateKanbanDisplay()
                };
            }
            
            addKanbanInteractionFeatures() {
                // 看板交互功能
                this.kanbanInteractionManager = {
                    enabled: this.kanbanInteractionConfig.enableClick,
                    onClick: (event) => this.onKanbanProgressClick(event),
                    onMouseEnter: () => this.onKanbanMouseEnter(),
                    onMouseLeave: () => this.onKanbanMouseLeave(),
                    update: (value) => this.updateKanbanProgress(value)
                };
            }
            
            // 重写原始方法
            get isEditable() {
                return this.enhancedIsEditable();
            }
        };
    }
    
    // 设置看板显示系统
    setupKanbanDisplaySystem() {
        this.kanbanDisplaySystemConfig = {
            enabled: true,
            options: this.kanbanDisplayOptions,
            themes: this.kanbanColorThemes
        };
    }
    
    // 设置看板交互系统
    setupKanbanInteractionSystem() {
        this.kanbanInteractionSystemConfig = {
            enabled: this.kanbanProgressConfig.enableQuickEdit,
            config: this.kanbanInteractionConfig
        };
    }
    
    // 设置看板主题系统
    setupKanbanThemeSystem() {
        this.kanbanThemeSystemConfig = {
            enabled: this.kanbanProgressConfig.enableColorCoding,
            themes: this.kanbanColorThemes,
            defaultTheme: 'default'
        };
    }
    
    // 创建看板进度条字段
    createKanbanProgressBarField(props) {
        const field = new this.EnhancedKanbanProgressBarField(props);
        this.kanbanProgressStatistics.totalKanbanProgressFields++;
        return field;
    }
    
    // 批量创建看板进度条字段
    batchCreateKanbanProgressFields(propsArray) {
        const results = [];
        
        for (const props of propsArray) {
            try {
                const field = this.createKanbanProgressBarField(props);
                results.push({ field, success: true, props });
            } catch (error) {
                results.push({ field: null, success: false, error, props });
            }
        }
        
        return results;
    }
    
    // 注册看板主题
    registerKanbanTheme(name, colors) {
        this.kanbanColorThemes.set(name, colors);
    }
    
    // 获取看板进度统计
    getKanbanProgressStatistics() {
        const total = this.kanbanProgressStatistics.totalKanbanProgressFields;
        
        return {
            ...this.kanbanProgressStatistics,
            completionRate: (this.kanbanProgressStatistics.completedTasks / Math.max(total, 1)) * 100,
            progressRate: (this.kanbanProgressStatistics.inProgressTasks / Math.max(total, 1)) * 100,
            notStartedRate: (this.kanbanProgressStatistics.notStartedTasks / Math.max(total, 1)) * 100,
            averageUpdatesPerField: this.kanbanProgressStatistics.totalProgressUpdates / Math.max(total, 1),
            themeCount: this.kanbanColorThemes.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理看板主题
        this.kanbanColorThemes.clear();
        
        // 重置统计
        this.kanbanProgressStatistics = {
            totalKanbanProgressFields: 0,
            totalProgressUpdates: 0,
            averageProgress: 0,
            completedTasks: 0,
            inProgressTasks: 0,
            notStartedTasks: 0
        };
    }
}

// 使用示例
const kanbanProgressManager = new KanbanProgressBarFieldManager();

// 创建看板进度条字段
const kanbanProgressField = kanbanProgressManager.createKanbanProgressBarField({
    name: 'progress',
    record: {
        data: { 
            progress: 75,
            max_progress: 100
        },
        fields: { 
            progress: { 
                type: 'integer'
            }
        }
    },
    isEditable: true,
    maxValueField: 'max_progress'
});

// 注册自定义看板主题
kanbanProgressManager.registerKanbanTheme('ocean', {
    primary: '#0077be',
    success: '#00a86b',
    warning: '#ffb347',
    danger: '#ff6b6b'
});

// 获取统计信息
const stats = kanbanProgressManager.getKanbanProgressStatistics();
console.log('Kanban progress bar field statistics:', stats);
```

## 技术特点

### 1. 继承设计
- **基类继承**: 继承ProgressBarField的所有功能
- **权限重写**: 重写编辑权限逻辑
- **配置复用**: 复用基础字段的所有配置
- **功能扩展**: 在基础功能上添加看板特性

### 2. 看板优化
- **编辑简化**: 简化看板视图中的编辑权限判断
- **视图专用**: 专门为看板视图优化
- **性能优化**: 优化看板视图的性能
- **交互优化**: 优化看板视图的交互体验

### 3. 简洁实现
- **最小代码**: 极简的代码实现
- **专注功能**: 专注于看板视图的特殊需求
- **易于维护**: 易于维护和扩展
- **清晰逻辑**: 清晰的继承和重写逻辑

### 4. 命名空间
- **看板前缀**: 使用kanban前缀区分看板字段
- **类型区分**: 明确区分普通和看板进度条字段
- **注册分离**: 分离注册避免冲突
- **功能隔离**: 隔离看板特定功能

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **类继承**: 继承基础进度条字段
- **方法重写**: 重写特定方法
- **功能扩展**: 扩展看板功能

### 2. 适配器模式 (Adapter Pattern)
- **视图适配**: 适配看板视图需求
- **权限适配**: 适配看板编辑权限
- **显示适配**: 适配看板显示方式

### 3. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 为基础字段添加看板装饰
- **样式装饰**: 添加看板样式装饰
- **交互装饰**: 添加看板交互装饰

### 4. 策略模式 (Strategy Pattern)
- **编辑策略**: 看板特定的编辑策略
- **显示策略**: 看板特定的显示策略
- **交互策略**: 看板特定的交互策略

## 注意事项

1. **权限控制**: 确保看板编辑权限的正确性
2. **性能优化**: 避免看板视图中的性能问题
3. **用户体验**: 提供流畅的看板交互体验
4. **视图一致性**: 保持与其他看板字段的一致性

## 扩展建议

1. **拖拽编辑**: 支持拖拽调整进度
2. **快速编辑**: 增强快速编辑功能
3. **批量操作**: 支持批量进度更新
4. **动画效果**: 添加进度变化动画
5. **状态指示**: 增强进度状态指示

该看板进度条字段为Odoo Web客户端提供了专门的看板视图进度显示功能，通过简洁的继承设计和权限优化确保了在看板视图中的最佳用户体验。

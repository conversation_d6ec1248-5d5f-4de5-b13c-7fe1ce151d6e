# DynamicPlaceholderPopover - 动态占位符弹出框

## 概述

`dynamic_placeholder_popover.js` 是 Odoo Web 客户端字段系统的动态占位符弹出框组件，负责在用户触发动态占位符功能时显示字段选择器界面。该模块包含89行代码，是一个OWL组件，专门用于提供字段选择和默认值设置的用户界面，具备字段过滤、权限检查、路径选择、默认值设置等特性，是动态占位符系统的核心UI组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/dynamic_placeholder_popover.js`
- **行数**: 89
- **模块**: `@web/views/fields/dynamic_placeholder_popover`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/functions'                                 // 工具函数
'@web/core/utils/hooks'                                     // 工具钩子
'@web/core/model_field_selector/model_field_selector_popover' // 模型字段选择器弹出框
'@odoo/owl'                                                 // OWL框架
'@web/core/user'                                           // 用户服务
```

## 核心功能

### 1. 组件定义

```javascript
const DynamicPlaceholderPopover = class DynamicPlaceholderPopover extends Component {
    static template = "web.DynamicPlaceholderPopover";
    static components = {
        ModelFieldSelectorPopover,
    };
    static props = ["resModel", "validate", "close"];
}
```

**组件特性**:
- **专用模板**: 使用DynamicPlaceholderPopover专用模板
- **字段选择器**: 集成ModelFieldSelectorPopover组件
- **属性定义**: 接收模型、验证和关闭回调属性
- **弹出框设计**: 专门的弹出框UI组件

### 2. 组件初始化

```javascript
setup() {
    useAutofocus();
    this.state = useState({
        path: "",
        isPathSelected: false,
        defaultValue: "",
    });
    this.orm = useService("orm");

    onWillStart(async () => {
        [this.isTemplateEditor, this.allowedQwebExpressions] = await Promise.all([
            user.hasGroup("mail.group_mail_template_editor"),
            // (only the first element is the cache key)
            allowedQwebExpressions(this.props.resModel, this.orm),
        ]);
    });
}
```

**初始化功能**:
- **自动聚焦**: 使用自动聚焦钩子
- **状态管理**: 管理路径、选择状态和默认值
- **ORM服务**: 集成ORM服务用于数据操作
- **权限检查**: 检查用户是否为模板编辑器
- **表达式获取**: 获取允许的Qweb表达式

### 3. 字段过滤

```javascript
filter(fieldDef, path) {
    const fullPath = `object${path ? `.${path}` : ""}.${fieldDef.name}`;
    if (!this.isTemplateEditor && !this.allowedQwebExpressions.includes(fullPath)) {
        return false;
    }
    return !["one2many", "boolean", "many2many"].includes(fieldDef.type) && fieldDef.searchable;
}
```

**过滤功能**:
- **路径构建**: 构建完整的字段路径
- **权限过滤**: 根据用户权限过滤字段
- **表达式检查**: 检查字段是否在允许的表达式列表中
- **类型过滤**: 过滤掉不支持的字段类型
- **可搜索性**: 只显示可搜索的字段

### 4. 字段选择器管理

```javascript
closeFieldSelector() {
    if (this.state.path) {
        this.state.isPathSelected = true;
        return;
    }
    this.props.close();
}

setPath(path, fieldInfo) {
    this.state.path = path;
    this.state.fieldName = fieldInfo?.string;
}
```

**选择器管理功能**:
- **条件关闭**: 根据路径状态决定是否关闭
- **状态切换**: 切换到路径已选择状态
- **路径设置**: 设置选择的字段路径
- **字段信息**: 保存字段的显示名称

### 5. 默认值处理

```javascript
setDefaultValue(value) {
    this.state.defaultValue = value;
}

validate() {
    this.props.validate(this.state.path, this.state.defaultValue);
    this.props.close();
}
```

**默认值功能**:
- **值设置**: 设置占位符的默认值
- **验证回调**: 调用验证回调函数
- **参数传递**: 传递路径和默认值给验证函数
- **弹出框关闭**: 验证后关闭弹出框

### 6. 导航控制

```javascript
onBack() {
    this.state.defaultValue = "";
    this.state.isPathSelected = false;
    this.state.path = "";
}
```

**导航功能**:
- **状态重置**: 重置所有状态到初始值
- **返回操作**: 提供返回到字段选择的功能
- **清理数据**: 清理已输入的数据
- **界面切换**: 切换回字段选择界面

### 7. 键盘事件处理

```javascript
async onInputKeydown(ev) {
    switch (ev.key) {
        case "Enter": {
            this.validate();
            ev.stopPropagation();
            ev.preventDefault();
            break;
        }
        case "Escape": {
            this.props.close();
            break;
        }
    }
}
```

**键盘处理功能**:
- **Enter键**: 触发验证和确认操作
- **Escape键**: 取消操作并关闭弹出框
- **事件阻止**: 阻止事件冒泡和默认行为
- **快捷操作**: 提供键盘快捷操作

## 使用场景

### 1. 动态占位符弹出框管理器

```javascript
// 动态占位符弹出框管理器
class DynamicPlaceholderPopoverManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置弹出框配置
        this.popoverConfig = {
            enableFieldFiltering: true,
            enablePermissionCheck: true,
            enableDefaultValues: true,
            enableKeyboardShortcuts: true,
            enableFieldPreview: true,
            enableFieldSearch: true,
            maxFieldDepth: 3,
            allowedFieldTypes: ['char', 'text', 'integer', 'float', 'date', 'datetime', 'selection']
        };
        
        // 设置字段缓存
        this.fieldCache = new Map();
        
        // 设置权限缓存
        this.permissionCache = new Map();
        
        // 设置弹出框统计
        this.popoverStatistics = {
            totalOpens: 0,
            successfulSelections: 0,
            cancelledSelections: 0,
            averageSelectionTime: 0
        };
        
        this.initializePopoverSystem();
    }
    
    // 初始化弹出框系统
    initializePopoverSystem() {
        // 创建增强的动态占位符弹出框
        this.createEnhancedDynamicPlaceholderPopover();
        
        // 设置字段过滤系统
        this.setupFieldFilteringSystem();
        
        // 设置权限检查系统
        this.setupPermissionSystem();
        
        // 设置缓存系统
        this.setupCacheSystem();
    }
    
    // 创建增强的动态占位符弹出框
    createEnhancedDynamicPlaceholderPopover() {
        const originalPopover = DynamicPlaceholderPopover;
        
        this.EnhancedDynamicPlaceholderPopover = class extends originalPopover {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加搜索功能
                this.addSearchFeatures();
                
                // 添加预览功能
                this.addPreviewFeatures();
                
                // 添加历史记录
                this.addHistoryFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = useState({
                    ...this.state,
                    searchQuery: '',
                    filteredFields: [],
                    selectedFieldInfo: null,
                    previewValue: '',
                    selectionHistory: [],
                    isLoading: false
                });
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的字段过滤
                this.enhancedFilter = (fieldDef, path) => {
                    // 执行原始过滤
                    const baseResult = this.filter(fieldDef, path);
                    if (!baseResult) return false;
                    
                    // 应用自定义过滤规则
                    if (!this.popoverConfig.allowedFieldTypes.includes(fieldDef.type)) {
                        return false;
                    }
                    
                    // 检查字段深度
                    const pathDepth = path ? path.split('.').length : 0;
                    if (pathDepth >= this.popoverConfig.maxFieldDepth) {
                        return false;
                    }
                    
                    // 应用搜索过滤
                    if (this.enhancedState.searchQuery) {
                        const query = this.enhancedState.searchQuery.toLowerCase();
                        const fieldName = fieldDef.name.toLowerCase();
                        const fieldString = (fieldDef.string || '').toLowerCase();
                        
                        if (!fieldName.includes(query) && !fieldString.includes(query)) {
                            return false;
                        }
                    }
                    
                    return true;
                };
                
                // 增强的路径设置
                this.enhancedSetPath = (path, fieldInfo) => {
                    const startTime = performance.now();
                    
                    try {
                        // 执行原始路径设置
                        this.setPath(path, fieldInfo);
                        
                        // 设置字段信息
                        this.enhancedState.selectedFieldInfo = fieldInfo;
                        
                        // 生成预览值
                        this.generatePreviewValue(path, fieldInfo);
                        
                        // 添加到历史记录
                        this.addToHistory(path, fieldInfo);
                        
                        // 记录成功选择
                        this.popoverStatistics.successfulSelections++;
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordSelectionTime(endTime - startTime);
                        
                    } catch (error) {
                        console.error('Enhanced path setting error:', error);
                    }
                };
                
                // 搜索字段
                this.searchFields = (query) => {
                    this.enhancedState.searchQuery = query;
                    this.updateFilteredFields();
                };
                
                // 更新过滤字段
                this.updateFilteredFields = () => {
                    // 实现字段过滤逻辑
                    console.log('Updating filtered fields with query:', this.enhancedState.searchQuery);
                };
                
                // 生成预览值
                this.generatePreviewValue = (path, fieldInfo) => {
                    if (!this.popoverConfig.enableFieldPreview) return;
                    
                    // 生成示例预览值
                    const previewValues = {
                        'char': 'Sample Text',
                        'text': 'Sample Long Text',
                        'integer': '123',
                        'float': '123.45',
                        'date': '2023-12-25',
                        'datetime': '2023-12-25 10:30:00',
                        'selection': 'Option 1'
                    };
                    
                    this.enhancedState.previewValue = previewValues[fieldInfo?.type] || 'Sample Value';
                };
                
                // 添加到历史记录
                this.addToHistory = (path, fieldInfo) => {
                    const historyItem = {
                        path: path,
                        fieldInfo: fieldInfo,
                        timestamp: Date.now(),
                        model: this.props.resModel
                    };
                    
                    // 添加到历史记录（保持最近10条）
                    this.enhancedState.selectionHistory.unshift(historyItem);
                    if (this.enhancedState.selectionHistory.length > 10) {
                        this.enhancedState.selectionHistory.pop();
                    }
                };
                
                // 从历史记录选择
                this.selectFromHistory = (historyItem) => {
                    this.enhancedSetPath(historyItem.path, historyItem.fieldInfo);
                };
                
                // 清除搜索
                this.clearSearch = () => {
                    this.enhancedState.searchQuery = '';
                    this.updateFilteredFields();
                };
                
                // 获取字段建议
                this.getFieldSuggestions = (query) => {
                    // 实现字段建议逻辑
                    const suggestions = [];
                    
                    // 基于历史记录的建议
                    for (const item of this.enhancedState.selectionHistory) {
                        if (item.path.toLowerCase().includes(query.toLowerCase())) {
                            suggestions.push({
                                path: item.path,
                                label: item.fieldInfo?.string || item.path,
                                type: 'history'
                            });
                        }
                    }
                    
                    return suggestions;
                };
                
                // 验证字段选择
                this.validateFieldSelection = (path, fieldInfo) => {
                    // 检查路径有效性
                    if (!path || typeof path !== 'string') {
                        throw new Error('Invalid field path');
                    }
                    
                    // 检查字段信息
                    if (!fieldInfo || !fieldInfo.type) {
                        throw new Error('Invalid field information');
                    }
                    
                    // 检查字段类型
                    if (!this.popoverConfig.allowedFieldTypes.includes(fieldInfo.type)) {
                        throw new Error(`Field type ${fieldInfo.type} is not allowed`);
                    }
                    
                    return true;
                };
                
                // 格式化字段路径
                this.formatFieldPath = (path) => {
                    return path.split('.').map(part => 
                        part.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
                    ).join(' → ');
                };
                
                // 获取字段描述
                this.getFieldDescription = (fieldInfo) => {
                    const descriptions = {
                        'char': 'Text field',
                        'text': 'Long text field',
                        'integer': 'Integer number',
                        'float': 'Decimal number',
                        'date': 'Date field',
                        'datetime': 'Date and time field',
                        'selection': 'Selection field'
                    };
                    
                    return descriptions[fieldInfo?.type] || 'Field';
                };
            }
            
            addSearchFeatures() {
                // 搜索功能
                this.searchManager = {
                    enableSearch: this.popoverConfig.enableFieldSearch,
                    searchDelay: 300,
                    searchTimer: null,
                    
                    performSearch: (query) => {
                        clearTimeout(this.searchManager.searchTimer);
                        this.searchManager.searchTimer = setTimeout(() => {
                            this.searchFields(query);
                        }, this.searchManager.searchDelay);
                    }
                };
            }
            
            addPreviewFeatures() {
                // 预览功能
                this.previewManager = {
                    enablePreview: this.popoverConfig.enableFieldPreview,
                    previewDelay: 500,
                    previewTimer: null,
                    
                    showPreview: (path, fieldInfo) => {
                        if (!this.previewManager.enablePreview) return;
                        
                        clearTimeout(this.previewManager.previewTimer);
                        this.previewManager.previewTimer = setTimeout(() => {
                            this.generatePreviewValue(path, fieldInfo);
                        }, this.previewManager.previewDelay);
                    }
                };
            }
            
            addHistoryFeatures() {
                // 历史记录功能
                this.historyManager = {
                    maxHistorySize: 10,
                    enableHistory: true,
                    
                    getRecentSelections: () => {
                        return this.enhancedState.selectionHistory.slice(0, 5);
                    },
                    
                    clearHistory: () => {
                        this.enhancedState.selectionHistory = [];
                    }
                };
            }
            
            // 重写原始方法
            filter(fieldDef, path) {
                return this.enhancedFilter(fieldDef, path);
            }
            
            setPath(path, fieldInfo) {
                return this.enhancedSetPath(path, fieldInfo);
            }
            
            // 增强的验证
            validate() {
                try {
                    // 验证字段选择
                    this.validateFieldSelection(this.state.path, this.enhancedState.selectedFieldInfo);
                    
                    // 执行原始验证
                    super.validate();
                    
                } catch (error) {
                    console.error('Validation error:', error);
                    // 显示错误提示
                    this.showValidationError(error.message);
                }
            }
            
            // 显示验证错误
            showValidationError(message) {
                // 实现错误显示逻辑
                console.error('Validation error:', message);
            }
            
            // 记录选择时间
            recordSelectionTime(duration) {
                const total = this.popoverStatistics.successfulSelections;
                this.popoverStatistics.averageSelectionTime = 
                    (this.popoverStatistics.averageSelectionTime * (total - 1) + duration) / total;
            }
            
            // 获取弹出框统计
            getPopoverStatistics() {
                return {
                    ...this.popoverStatistics,
                    historySize: this.enhancedState.selectionHistory.length,
                    currentModel: this.props.resModel,
                    hasSelectedField: Boolean(this.state.path)
                };
            }
            
            // 组件销毁时清理
            onWillDestroy() {
                // 清理定时器
                if (this.searchManager?.searchTimer) {
                    clearTimeout(this.searchManager.searchTimer);
                }
                
                if (this.previewManager?.previewTimer) {
                    clearTimeout(this.previewManager.previewTimer);
                }
            }
        };
    }
    
    // 设置字段过滤系统
    setupFieldFilteringSystem() {
        this.filteringRules = {
            typeFilter: (fieldDef) => this.popoverConfig.allowedFieldTypes.includes(fieldDef.type),
            depthFilter: (path) => {
                const depth = path ? path.split('.').length : 0;
                return depth < this.popoverConfig.maxFieldDepth;
            },
            searchFilter: (fieldDef, query) => {
                if (!query) return true;
                const lowerQuery = query.toLowerCase();
                return fieldDef.name.toLowerCase().includes(lowerQuery) ||
                       (fieldDef.string || '').toLowerCase().includes(lowerQuery);
            }
        };
    }
    
    // 设置权限检查系统
    setupPermissionSystem() {
        this.permissionChecker = {
            checkTemplateEditor: async (userId) => {
                // 实现模板编辑器权限检查
                return true;
            },
            
            checkFieldAccess: async (model, fieldPath) => {
                // 实现字段访问权限检查
                return true;
            },
            
            cachePermission: (key, result) => {
                this.permissionCache.set(key, {
                    result: result,
                    timestamp: Date.now()
                });
            }
        };
    }
    
    // 设置缓存系统
    setupCacheSystem() {
        this.cacheConfig = {
            fieldCacheTTL: 300000, // 5分钟
            permissionCacheTTL: 600000, // 10分钟
            maxCacheSize: 100
        };
        
        // 定期清理缓存
        setInterval(() => {
            this.cleanupCache();
        }, 60000); // 每分钟清理一次
    }
    
    // 清理缓存
    cleanupCache() {
        const now = Date.now();
        
        // 清理字段缓存
        for (const [key, value] of this.fieldCache.entries()) {
            if (now - value.timestamp > this.cacheConfig.fieldCacheTTL) {
                this.fieldCache.delete(key);
            }
        }
        
        // 清理权限缓存
        for (const [key, value] of this.permissionCache.entries()) {
            if (now - value.timestamp > this.cacheConfig.permissionCacheTTL) {
                this.permissionCache.delete(key);
            }
        }
    }
    
    // 创建动态占位符弹出框
    createDynamicPlaceholderPopover(props) {
        return new this.EnhancedDynamicPlaceholderPopover(props);
    }
    
    // 获取弹出框统计
    getPopoverStatistics() {
        return {
            ...this.popoverStatistics,
            fieldCacheSize: this.fieldCache.size,
            permissionCacheSize: this.permissionCache.size
        };
    }
    
    // 清理缓存
    clearCache() {
        this.fieldCache.clear();
        this.permissionCache.clear();
    }
    
    // 销毁管理器
    destroy() {
        // 清理缓存
        this.fieldCache.clear();
        this.permissionCache.clear();
        
        // 重置统计
        this.popoverStatistics = {
            totalOpens: 0,
            successfulSelections: 0,
            cancelledSelections: 0,
            averageSelectionTime: 0
        };
    }
}

// 使用示例
const popoverManager = new DynamicPlaceholderPopoverManager();

// 创建动态占位符弹出框
const popover = popoverManager.createDynamicPlaceholderPopover({
    resModel: 'res.partner',
    validate: (path, defaultValue) => {
        console.log('Field selected:', path, defaultValue);
    },
    close: () => {
        console.log('Popover closed');
    }
});

// 获取统计信息
const stats = popoverManager.getPopoverStatistics();
console.log('Dynamic placeholder popover statistics:', stats);
```

## 技术特点

### 1. 用户界面
- **直观设计**: 直观的字段选择界面
- **分步操作**: 分步的字段选择和默认值设置
- **键盘支持**: 完整的键盘操作支持
- **即时反馈**: 提供即时的操作反馈

### 2. 权限控制
- **角色检查**: 检查用户是否为模板编辑器
- **字段过滤**: 根据权限过滤可用字段
- **表达式验证**: 验证字段表达式的合法性
- **安全控制**: 确保字段访问的安全性

### 3. 字段管理
- **类型过滤**: 过滤支持的字段类型
- **搜索功能**: 支持字段的搜索功能
- **路径构建**: 智能的字段路径构建
- **信息展示**: 展示字段的详细信息

### 4. 交互体验
- **自动聚焦**: 自动聚焦到输入框
- **快捷键**: 支持Enter和Escape快捷键
- **状态管理**: 清晰的状态管理
- **导航控制**: 灵活的界面导航

## 设计模式

### 1. 组件模式 (Component Pattern)
- **封装性**: 封装弹出框的所有功能
- **可复用**: 高度可复用的弹出框组件
- **接口标准**: 标准化的组件接口

### 2. 状态模式 (State Pattern)
- **状态管理**: 管理弹出框的不同状态
- **状态切换**: 在字段选择和默认值设置间切换
- **行为变化**: 根据状态改变行为

### 3. 策略模式 (Strategy Pattern)
- **过滤策略**: 不同的字段过滤策略
- **验证策略**: 不同的字段验证策略
- **显示策略**: 不同的字段显示策略

### 4. 观察者模式 (Observer Pattern)
- **事件监听**: 监听用户交互事件
- **状态变化**: 响应状态变化
- **回调通知**: 通过回调通知外部组件

## 注意事项

1. **权限安全**: 确保字段访问权限的正确检查
2. **性能优化**: 避免频繁的字段查询和过滤
3. **用户体验**: 提供流畅的用户交互体验
4. **错误处理**: 完善的错误处理和用户提示

## 扩展建议

1. **字段预览**: 添加字段值的预览功能
2. **历史记录**: 支持字段选择的历史记录
3. **收藏功能**: 支持常用字段的收藏功能
4. **批量选择**: 支持多个字段的批量选择
5. **智能建议**: 基于上下文的智能字段建议

该动态占位符弹出框为Odoo Web客户端提供了专业的字段选择界面，通过直观的UI设计和完善的权限控制确保了用户能够安全、便捷地选择所需的字段。

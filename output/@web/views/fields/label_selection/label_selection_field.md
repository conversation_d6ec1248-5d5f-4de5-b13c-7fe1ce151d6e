# LabelSelectionField - 标签选择字段

## 概述

`label_selection_field.js` 是 Odoo Web 客户端的标签选择字段组件，负责以标签形式显示选择字段的值。该模块包含50行代码，是一个专门的选择字段显示组件，专门用于处理selection类型的字段，具备标签样式、CSS类配置、格式化显示等特性，是选择字段可视化展示的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/label_selection/label_selection_field.js`
- **行数**: 50
- **模块**: `@web/views/fields/label_selection/label_selection_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                    // 注册表
'@web/core/l10n/translation'            // 翻译服务
'@web/views/fields/standard_field_props' // 标准字段属性
'@web/views/fields/formatters'          // 字段格式化器
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const LabelSelectionField = class LabelSelectionField extends Component {
    static template = "web.LabelSelectionField";
    static props = {
        ...standardFieldProps,
        classesObj: { type: Object, optional: true },
    };
    static defaultProps = {
        classesObj: {},
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **CSS类对象**: 支持classesObj配置不同值的CSS类
- **默认类**: 提供默认的CSS类配置
- **专用模板**: 使用LabelSelectionField专用模板

### 2. CSS类名获取

```javascript
get className() {
    return this.props.classesObj[this.props.record.data[this.props.name]] || "primary";
}
```

**类名功能**:
- **动态类名**: 根据字段值动态获取CSS类名
- **类映射**: 通过classesObj映射值到CSS类
- **默认类**: 未配置时使用"primary"作为默认类
- **样式控制**: 控制标签的视觉样式

### 3. 字符串格式化

```javascript
get string() {
    return formatSelection(this.props.record.data[this.props.name], {
        selection: Array.from(this.props.record.fields[this.props.name].selection),
    });
}
```

**格式化功能**:
- **选择格式化**: 使用formatSelection格式化选择值
- **选项获取**: 从字段定义中获取选择选项
- **数组转换**: 将选择选项转换为数组
- **显示文本**: 获取用于显示的文本内容

### 4. 字段注册

```javascript
const labelSelectionField = {
    component: LabelSelectionField,
    displayName: _t("Label Selection"),
    supportedOptions: [
        {
            label: _t("Classes"),
            name: "classes",
            type: "string",
        },
    ],
    supportedTypes: ["selection"],
    extractProps: ({ options }) => ({
        classesObj: options.classes,
    }),
};

registry.category("fields").add("label_selection", labelSelectionField);
```

**注册功能**:
- **组件注册**: 注册标签选择字段组件
- **显示名称**: 设置为"Label Selection"
- **类选项**: 支持classes选项配置CSS类
- **类型支持**: 仅支持selection类型
- **属性提取**: 提取classes选项到组件属性

## 使用场景

### 1. 标签选择字段管理器

```javascript
// 标签选择字段管理器
class LabelSelectionFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置标签选择字段配置
        this.labelConfig = {
            enableCustomClasses: true,
            enableAnimation: true,
            enableTooltips: true,
            enableIcons: false,
            enableBadges: true,
            enableColorCoding: true,
            enableGrouping: false,
            enableSorting: false
        };
        
        // 设置预定义CSS类主题
        this.classThemes = new Map([
            ['bootstrap', {
                'draft': 'secondary',
                'confirmed': 'primary',
                'done': 'success',
                'cancelled': 'danger',
                'pending': 'warning',
                'archived': 'dark'
            }],
            ['semantic', {
                'new': 'blue',
                'active': 'green',
                'inactive': 'grey',
                'urgent': 'red',
                'normal': 'teal',
                'low': 'yellow'
            }],
            ['status', {
                'open': 'info',
                'in_progress': 'warning',
                'completed': 'success',
                'failed': 'danger',
                'cancelled': 'secondary'
            }]
        ]);
        
        // 设置颜色映射
        this.colorMappings = new Map([
            ['priority', {
                'high': '#dc3545',
                'medium': '#ffc107',
                'low': '#28a745',
                'none': '#6c757d'
            }],
            ['status', {
                'active': '#007bff',
                'inactive': '#6c757d',
                'pending': '#fd7e14',
                'completed': '#28a745'
            }]
        ]);
        
        // 设置图标映射
        this.iconMappings = new Map([
            ['status', {
                'draft': 'fa-edit',
                'confirmed': 'fa-check',
                'done': 'fa-check-circle',
                'cancelled': 'fa-times-circle'
            }],
            ['priority', {
                'high': 'fa-exclamation-triangle',
                'medium': 'fa-exclamation',
                'low': 'fa-info-circle'
            }]
        ]);
        
        // 设置标签统计
        this.labelStatistics = {
            totalLabels: 0,
            valueDistribution: new Map(),
            classUsage: new Map(),
            themeUsage: new Map(),
            renderTime: 0
        };
        
        this.initializeLabelSystem();
    }
    
    // 初始化标签系统
    initializeLabelSystem() {
        // 创建增强的标签选择字段
        this.createEnhancedLabelSelectionField();
        
        // 设置主题系统
        this.setupThemeSystem();
        
        // 设置样式系统
        this.setupStyleSystem();
        
        // 设置动画系统
        this.setupAnimationSystem();
    }
    
    // 创建增强的标签选择字段
    createEnhancedLabelSelectionField() {
        const originalField = LabelSelectionField;
        
        this.EnhancedLabelSelectionField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加主题功能
                this.addThemeFeatures();
                
                // 添加交互功能
                this.addInteractionFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    currentTheme: 'bootstrap',
                    isHovered: false,
                    isAnimating: false,
                    customClasses: new Map(),
                    tooltipText: null,
                    iconClass: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的类名获取
                this.enhancedGetClassName = () => {
                    const value = this.props.record.data[this.props.name];
                    
                    // 优先使用自定义类
                    if (this.enhancedState.customClasses.has(value)) {
                        return this.enhancedState.customClasses.get(value);
                    }
                    
                    // 使用主题类
                    const theme = this.classThemes.get(this.enhancedState.currentTheme);
                    if (theme && theme[value]) {
                        return theme[value];
                    }
                    
                    // 使用配置的类
                    if (this.props.classesObj && this.props.classesObj[value]) {
                        return this.props.classesObj[value];
                    }
                    
                    // 默认类
                    return "primary";
                };
                
                // 增强的字符串获取
                this.enhancedGetString = () => {
                    const value = this.props.record.data[this.props.name];
                    const selection = this.props.record.fields[this.props.name].selection;
                    
                    // 使用格式化器
                    const formatted = formatSelection(value, {
                        selection: Array.from(selection),
                    });
                    
                    // 添加图标
                    if (this.labelConfig.enableIcons && this.enhancedState.iconClass) {
                        return `<i class="${this.enhancedState.iconClass}"></i> ${formatted}`;
                    }
                    
                    return formatted;
                };
                
                // 设置主题
                this.setTheme = (themeName) => {
                    if (this.classThemes.has(themeName)) {
                        this.enhancedState.currentTheme = themeName;
                        
                        // 记录主题使用
                        const usage = this.labelStatistics.themeUsage.get(themeName) || 0;
                        this.labelStatistics.themeUsage.set(themeName, usage + 1);
                    }
                };
                
                // 设置自定义类
                this.setCustomClass = (value, className) => {
                    this.enhancedState.customClasses.set(value, className);
                };
                
                // 获取颜色
                this.getColor = (category = 'status') => {
                    const value = this.props.record.data[this.props.name];
                    const colorMap = this.colorMappings.get(category);
                    
                    return colorMap ? colorMap[value] : null;
                };
                
                // 设置图标
                this.setIcon = (category = 'status') => {
                    const value = this.props.record.data[this.props.name];
                    const iconMap = this.iconMappings.get(category);
                    
                    if (iconMap && iconMap[value]) {
                        this.enhancedState.iconClass = iconMap[value];
                    }
                };
                
                // 获取工具提示
                this.getTooltip = () => {
                    if (!this.labelConfig.enableTooltips) return null;
                    
                    const value = this.props.record.data[this.props.name];
                    const selection = this.props.record.fields[this.props.name].selection;
                    
                    // 查找选项描述
                    const option = selection.find(opt => opt[0] === value);
                    return option ? option[1] : null;
                };
                
                // 获取标签样式
                this.getLabelStyle = () => {
                    const style = {};
                    
                    // 添加颜色
                    const color = this.getColor();
                    if (color) {
                        style.backgroundColor = color;
                        style.borderColor = color;
                    }
                    
                    // 添加动画
                    if (this.labelConfig.enableAnimation && this.enhancedState.isAnimating) {
                        style.transition = 'all 0.3s ease';
                    }
                    
                    return style;
                };
                
                // 获取标签信息
                this.getLabelInfo = () => {
                    const value = this.props.record.data[this.props.name];
                    
                    return {
                        value: value,
                        displayText: this.enhancedGetString(),
                        className: this.enhancedGetClassName(),
                        color: this.getColor(),
                        icon: this.enhancedState.iconClass,
                        tooltip: this.getTooltip(),
                        theme: this.enhancedState.currentTheme,
                        style: this.getLabelStyle()
                    };
                };
                
                // 动画效果
                this.animateLabel = (animationType = 'pulse') => {
                    if (!this.labelConfig.enableAnimation) return;
                    
                    this.enhancedState.isAnimating = true;
                    
                    setTimeout(() => {
                        this.enhancedState.isAnimating = false;
                    }, 300);
                };
                
                // 鼠标悬停
                this.onMouseEnter = () => {
                    this.enhancedState.isHovered = true;
                    
                    if (this.labelConfig.enableAnimation) {
                        this.animateLabel('hover');
                    }
                };
                
                // 鼠标离开
                this.onMouseLeave = () => {
                    this.enhancedState.isHovered = false;
                };
                
                // 点击处理
                this.onClick = () => {
                    if (this.labelConfig.enableAnimation) {
                        this.animateLabel('click');
                    }
                    
                    // 触发自定义事件
                    this.onLabelClick();
                };
                
                // 标签点击事件
                this.onLabelClick = () => {
                    console.log('Label clicked:', this.getLabelInfo());
                };
                
                // 获取所有可用选项
                this.getAvailableOptions = () => {
                    const selection = this.props.record.fields[this.props.name].selection;
                    
                    return selection.map(option => ({
                        value: option[0],
                        label: option[1],
                        className: this.enhancedGetClassName(),
                        color: this.getColor()
                    }));
                };
                
                // 验证值
                this.validateValue = (value) => {
                    const selection = this.props.record.fields[this.props.name].selection;
                    const validValues = selection.map(opt => opt[0]);
                    
                    return validValues.includes(value);
                };
                
                // 格式化为徽章
                this.formatAsBadge = () => {
                    if (!this.labelConfig.enableBadges) return this.enhancedGetString();
                    
                    const info = this.getLabelInfo();
                    return `<span class="badge badge-${info.className}">${info.displayText}</span>`;
                };
                
                // 记录标签统计
                this.recordLabelStatistics = () => {
                    const value = this.props.record.data[this.props.name];
                    const className = this.enhancedGetClassName();
                    
                    this.labelStatistics.totalLabels++;
                    
                    // 记录值分布
                    const valueCount = this.labelStatistics.valueDistribution.get(value) || 0;
                    this.labelStatistics.valueDistribution.set(value, valueCount + 1);
                    
                    // 记录类使用
                    const classCount = this.labelStatistics.classUsage.get(className) || 0;
                    this.labelStatistics.classUsage.set(className, classCount + 1);
                };
                
                // 渲染性能记录
                this.recordRenderTime = (startTime) => {
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    
                    this.labelStatistics.renderTime = 
                        (this.labelStatistics.renderTime + duration) / 2;
                };
            }
            
            addThemeFeatures() {
                // 主题功能
                this.themeManager = {
                    enabled: this.labelConfig.enableCustomClasses,
                    setTheme: (theme) => this.setTheme(theme),
                    getThemes: () => Array.from(this.classThemes.keys()),
                    getCurrentTheme: () => this.enhancedState.currentTheme
                };
            }
            
            addInteractionFeatures() {
                // 交互功能
                this.interactionManager = {
                    enableHover: this.labelConfig.enableAnimation,
                    enableClick: true,
                    onMouseEnter: () => this.onMouseEnter(),
                    onMouseLeave: () => this.onMouseLeave(),
                    onClick: () => this.onClick()
                };
            }
            
            // 重写原始方法
            get className() {
                return this.enhancedGetClassName();
            }
            
            get string() {
                return this.enhancedGetString();
            }
        };
    }
    
    // 设置主题系统
    setupThemeSystem() {
        this.themeSystemConfig = {
            enabled: this.labelConfig.enableCustomClasses,
            themes: this.classThemes,
            defaultTheme: 'bootstrap'
        };
    }
    
    // 设置样式系统
    setupStyleSystem() {
        this.styleSystemConfig = {
            enableColorCoding: this.labelConfig.enableColorCoding,
            colorMappings: this.colorMappings,
            iconMappings: this.iconMappings
        };
    }
    
    // 设置动画系统
    setupAnimationSystem() {
        this.animationConfig = {
            enabled: this.labelConfig.enableAnimation,
            duration: 300,
            easing: 'ease'
        };
    }
    
    // 创建标签选择字段
    createLabelSelectionField(props) {
        const field = new this.EnhancedLabelSelectionField(props);
        this.labelStatistics.totalLabels++;
        return field;
    }
    
    // 注册CSS类主题
    registerClassTheme(name, classes) {
        this.classThemes.set(name, classes);
    }
    
    // 注册颜色映射
    registerColorMapping(category, colors) {
        this.colorMappings.set(category, colors);
    }
    
    // 注册图标映射
    registerIconMapping(category, icons) {
        this.iconMappings.set(category, icons);
    }
    
    // 批量设置主题
    batchSetTheme(fields, theme) {
        const results = [];
        
        for (const field of fields) {
            try {
                field.setTheme(theme);
                results.push({ field, success: true, theme });
            } catch (error) {
                results.push({ field, success: false, error });
            }
        }
        
        return results;
    }
    
    // 获取热门值
    getPopularValues(limit = 10) {
        const sorted = Array.from(this.labelStatistics.valueDistribution.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, limit);
        
        return sorted.map(([value, count]) => ({ value, count }));
    }
    
    // 获取热门CSS类
    getPopularClasses(limit = 10) {
        const sorted = Array.from(this.labelStatistics.classUsage.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, limit);
        
        return sorted.map(([className, count]) => ({ className, count }));
    }
    
    // 获取标签统计
    getLabelStatistics() {
        return {
            ...this.labelStatistics,
            themeCount: this.classThemes.size,
            colorMappingCount: this.colorMappings.size,
            iconMappingCount: this.iconMappings.size,
            averageRenderTime: this.labelStatistics.renderTime
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理主题
        this.classThemes.clear();
        this.colorMappings.clear();
        this.iconMappings.clear();
        
        // 重置统计
        this.labelStatistics = {
            totalLabels: 0,
            valueDistribution: new Map(),
            classUsage: new Map(),
            themeUsage: new Map(),
            renderTime: 0
        };
    }
}

// 使用示例
const labelManager = new LabelSelectionFieldManager();

// 创建标签选择字段
const labelField = labelManager.createLabelSelectionField({
    name: 'state',
    record: {
        data: { state: 'confirmed' },
        fields: { 
            state: { 
                type: 'selection',
                selection: [
                    ['draft', 'Draft'],
                    ['confirmed', 'Confirmed'],
                    ['done', 'Done'],
                    ['cancelled', 'Cancelled']
                ]
            }
        }
    },
    classesObj: {
        'draft': 'secondary',
        'confirmed': 'primary',
        'done': 'success',
        'cancelled': 'danger'
    }
});

// 注册自定义主题
labelManager.registerClassTheme('custom', {
    'new': 'info',
    'active': 'success',
    'inactive': 'warning',
    'archived': 'dark'
});

// 获取统计信息
const stats = labelManager.getLabelStatistics();
console.log('Label selection field statistics:', stats);
```

## 技术特点

### 1. 标签样式
- **CSS类映射**: 通过classesObj映射值到CSS类
- **动态样式**: 根据字段值动态应用样式
- **默认样式**: 提供合理的默认样式
- **主题支持**: 支持多种样式主题

### 2. 选择格式化
- **格式化器**: 使用formatSelection格式化选择值
- **选项获取**: 从字段定义获取选择选项
- **显示文本**: 获取用户友好的显示文本
- **国际化**: 支持多语言显示

### 3. 配置灵活
- **类配置**: 支持classes选项配置
- **属性提取**: 智能提取配置属性
- **默认配置**: 提供合理的默认配置
- **动态调整**: 支持动态配置调整

### 4. 类型专用
- **选择类型**: 专门支持selection类型
- **标签显示**: 专门用于标签形式显示
- **视觉区分**: 提供清晰的视觉区分
- **状态表示**: 适合表示状态和分类

## 设计模式

### 1. 组件模式 (Component Pattern)
- **UI组件**: 封装标签显示UI
- **状态管理**: 管理标签状态
- **样式控制**: 控制标签样式

### 2. 策略模式 (Strategy Pattern)
- **样式策略**: 不同的样式应用策略
- **格式策略**: 不同的格式化策略
- **主题策略**: 不同的主题策略

### 3. 映射模式 (Mapping Pattern)
- **类映射**: 值到CSS类的映射
- **颜色映射**: 值到颜色的映射
- **图标映射**: 值到图标的映射

### 4. 装饰器模式 (Decorator Pattern)
- **样式装饰**: 为选择值添加样式装饰
- **视觉装饰**: 添加视觉效果装饰
- **交互装饰**: 添加交互效果装饰

## 注意事项

1. **样式一致性**: 确保标签样式在整个应用中的一致性
2. **可访问性**: 考虑颜色对比度和可访问性要求
3. **性能考虑**: 避免过多的样式计算
4. **用户体验**: 提供清晰的视觉反馈

## 扩展建议

1. **图标支持**: 添加图标显示功能
2. **动画效果**: 支持标签动画效果
3. **工具提示**: 添加工具提示功能
4. **批量操作**: 支持批量样式设置
5. **自定义主题**: 支持自定义样式主题

该标签选择字段为Odoo Web客户端提供了美观的选择字段显示功能，通过灵活的CSS类配置和格式化选项确保了选择字段的良好视觉效果和用户体验。

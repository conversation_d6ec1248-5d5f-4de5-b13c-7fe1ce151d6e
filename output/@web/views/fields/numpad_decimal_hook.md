# NumpadDecimalHook - 数字键盘小数钩子

## 概述

`numpad_decimal_hook.js` 是 Odoo Web 客户端字段系统的数字键盘小数钩子模块，负责处理数字键盘小数点键的本地化。该模块包含63行代码，是一个OWL钩子函数，专门用于将数字键盘的小数点键替换为用户语言设置的小数分隔符，具备本地化支持、设备检测、自动选择、事件处理等特性，是数字输入字段的重要辅助工具。

## 文件信息
- **路径**: `/web/static/src/views/fields/numpad_decimal_hook.js`
- **行数**: 63
- **模块**: `@web/views/fields/numpad_decimal_hook`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/localization'           // 本地化服务
'@web/core/browser/feature_detection'   // 浏览器特性检测
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 键盘事件处理

```javascript
function onKeydown(ev) {
    const decimalPoint = localization.decimalPoint;
    if (
        !([".", ","].includes(ev.key) && ev.code === "NumpadDecimal") ||
        ev.key === decimalPoint ||
        ev.target.type === "number"
    ) {
        return;
    }
    ev.preventDefault();
    ev.target.setRangeText(decimalPoint, ev.target.selectionStart, ev.target.selectionEnd, "end");
}
```

**键盘处理功能**:
- **小数点检测**: 检测数字键盘的小数点键
- **本地化替换**: 用本地化的小数分隔符替换
- **类型检查**: 跳过number类型输入框的处理
- **文本插入**: 在光标位置插入正确的小数分隔符

### 2. 焦点事件处理

```javascript
function onFocus(ev) {
    ev.target.select();
}
```

**焦点处理功能**:
- **自动选择**: 焦点时自动选择输入框内容
- **用户体验**: 提供更好的编辑体验
- **快速替换**: 便于用户快速替换内容

### 3. 钩子函数定义

```javascript
function useNumpadDecimal() {
    const ref = useRef("numpadDecimal");
    const isIOSDevice = isIOS();
    
    useEffect(() => {
        let inputs = [];
        const el = ref.el;
        if (el) {
            inputs = el.nodeName === "INPUT" ? [el] : el.querySelectorAll("input");
            inputs.forEach((input) => input.addEventListener("keydown", onKeydown));
            inputs.forEach((input) => input.addEventListener("focus", onFocus));
            if (isIOSDevice) {
                inputs.forEach((input) => input.removeAttribute("inputmode"));
            }
        }
        return () => {
            inputs.forEach((input) => input.removeEventListener("keydown", onKeydown));
            inputs.forEach((input) => input.removeEventListener("focus", onFocus));
        };
    });
}
```

**钩子功能**:
- **元素引用**: 使用"numpadDecimal"引用查找输入元素
- **设备检测**: 检测是否为iOS设备
- **事件绑定**: 为输入元素绑定键盘和焦点事件
- **iOS适配**: 在iOS设备上移除inputmode属性
- **清理机制**: 组件销毁时清理事件监听器

## 使用场景

### 1. 数字键盘小数钩子管理器

```javascript
// 数字键盘小数钩子管理器
class NumpadDecimalHookManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置钩子配置
        this.hookConfig = {
            enableLocalization: true,
            enableAutoSelect: true,
            enableIOSAdaptation: true,
            enableCustomSeparators: true,
            supportedInputTypes: ['text', 'tel'],
            excludedInputTypes: ['number'],
            customSeparators: new Map()
        };
        
        // 设置钩子注册表
        this.hookRegistry = new Map();
        
        // 设置设备检测
        this.deviceInfo = {
            isIOS: this.detectIOS(),
            isAndroid: this.detectAndroid(),
            isMobile: this.detectMobile(),
            hasNumpad: this.detectNumpad()
        };
        
        // 设置钩子统计
        this.hookStatistics = {
            totalHooks: 0,
            activeHooks: 0,
            keyPressCount: 0,
            replacementCount: 0,
            errorCount: 0
        };
        
        this.initializeHookSystem();
    }
    
    // 初始化钩子系统
    initializeHookSystem() {
        // 创建增强的数字键盘小数钩子
        this.createEnhancedNumpadDecimalHook();
        
        // 设置本地化系统
        this.setupLocalizationSystem();
        
        // 设置设备适配
        this.setupDeviceAdaptation();
        
        // 设置性能监控
        this.setupPerformanceMonitoring();
    }
    
    // 创建增强的数字键盘小数钩子
    createEnhancedNumpadDecimalHook() {
        this.enhancedUseNumpadDecimal = (options = {}) => {
            const config = { ...this.hookConfig, ...options };
            const hookId = this.generateHookId();
            
            // 注册钩子
            this.registerHook(hookId, config);
            
            const ref = useRef(config.refName || "numpadDecimal");
            const isIOSDevice = this.deviceInfo.isIOS;
            
            useEffect(() => {
                let inputs = [];
                const el = ref.el;
                
                if (el) {
                    // 查找输入元素
                    inputs = this.findInputElements(el, config);
                    
                    // 绑定事件
                    this.bindEvents(inputs, hookId, config);
                    
                    // 设备特定适配
                    this.applyDeviceAdaptations(inputs, config);
                }
                
                return () => {
                    this.cleanupEvents(inputs, hookId);
                    this.unregisterHook(hookId);
                };
            });
            
            return {
                hookId: hookId,
                ref: ref,
                
                // 增强功能
                updateConfig: (newConfig) => this.updateHookConfig(hookId, newConfig),
                getStatistics: () => this.getHookStatistics(hookId),
                forceRefresh: () => this.refreshHook(hookId),
                
                // 自定义分隔符
                setCustomSeparator: (locale, separator) => {
                    this.hookConfig.customSeparators.set(locale, separator);
                },
                
                // 获取当前分隔符
                getCurrentSeparator: () => this.getCurrentDecimalSeparator(),
                
                // 测试功能
                testSeparatorReplacement: (input, key) => {
                    return this.testSeparatorReplacement(input, key);
                }
            };
        };
    }
    
    // 生成钩子ID
    generateHookId() {
        return `numpad_hook_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    // 注册钩子
    registerHook(hookId, config) {
        this.hookRegistry.set(hookId, {
            config: config,
            createdAt: Date.now(),
            inputs: [],
            statistics: {
                keyPresses: 0,
                replacements: 0,
                errors: 0
            }
        });
        
        this.hookStatistics.totalHooks++;
        this.hookStatistics.activeHooks++;
    }
    
    // 注销钩子
    unregisterHook(hookId) {
        if (this.hookRegistry.has(hookId)) {
            this.hookRegistry.delete(hookId);
            this.hookStatistics.activeHooks--;
        }
    }
    
    // 查找输入元素
    findInputElements(el, config) {
        let inputs = [];
        
        if (el.nodeName === "INPUT") {
            inputs = [el];
        } else {
            inputs = Array.from(el.querySelectorAll("input"));
        }
        
        // 过滤输入类型
        return inputs.filter(input => {
            const inputType = input.type || 'text';
            
            // 排除不支持的类型
            if (config.excludedInputTypes.includes(inputType)) {
                return false;
            }
            
            // 只包含支持的类型
            if (config.supportedInputTypes.length > 0) {
                return config.supportedInputTypes.includes(inputType);
            }
            
            return true;
        });
    }
    
    // 绑定事件
    bindEvents(inputs, hookId, config) {
        const hookInfo = this.hookRegistry.get(hookId);
        if (!hookInfo) return;
        
        hookInfo.inputs = inputs;
        
        inputs.forEach(input => {
            // 绑定键盘事件
            const keydownHandler = (ev) => this.enhancedOnKeydown(ev, hookId, config);
            input.addEventListener("keydown", keydownHandler);
            
            // 绑定焦点事件
            if (config.enableAutoSelect) {
                const focusHandler = (ev) => this.enhancedOnFocus(ev, hookId, config);
                input.addEventListener("focus", focusHandler);
            }
            
            // 存储事件处理器引用
            input._numpadKeydownHandler = keydownHandler;
            input._numpadFocusHandler = focusHandler;
        });
    }
    
    // 增强的键盘事件处理
    enhancedOnKeydown = (ev, hookId, config) => {
        const startTime = performance.now();
        
        try {
            const hookInfo = this.hookRegistry.get(hookId);
            if (!hookInfo) return;
            
            // 记录按键
            hookInfo.statistics.keyPresses++;
            this.hookStatistics.keyPressCount++;
            
            // 获取小数分隔符
            const decimalPoint = this.getCurrentDecimalSeparator(config);
            
            // 检查是否需要处理
            if (!this.shouldProcessKey(ev, decimalPoint)) {
                return;
            }
            
            // 执行替换
            this.performSeparatorReplacement(ev, decimalPoint, hookId);
            
            // 记录替换
            hookInfo.statistics.replacements++;
            this.hookStatistics.replacementCount++;
            
            // 记录性能
            const endTime = performance.now();
            this.recordPerformance('keydown', endTime - startTime);
            
        } catch (error) {
            this.handleKeydownError(hookId, error);
        }
    };
    
    // 增强的焦点事件处理
    enhancedOnFocus = (ev, hookId, config) => {
        try {
            if (config.enableAutoSelect) {
                ev.target.select();
            }
            
            // 记录焦点事件
            this.recordFocusEvent(hookId, ev);
            
        } catch (error) {
            this.handleFocusError(hookId, error);
        }
    };
    
    // 检查是否需要处理按键
    shouldProcessKey(ev, decimalPoint) {
        // 检查是否为数字键盘小数点
        if (!([".", ","].includes(ev.key) && ev.code === "NumpadDecimal")) {
            return false;
        }
        
        // 检查是否已经是正确的分隔符
        if (ev.key === decimalPoint) {
            return false;
        }
        
        // 检查输入类型
        if (ev.target.type === "number") {
            return false;
        }
        
        return true;
    }
    
    // 执行分隔符替换
    performSeparatorReplacement(ev, decimalPoint, hookId) {
        ev.preventDefault();
        
        const target = ev.target;
        const start = target.selectionStart;
        const end = target.selectionEnd;
        
        // 使用setRangeText插入正确的分隔符
        target.setRangeText(decimalPoint, start, end, "end");
        
        // 触发input事件以通知其他监听器
        target.dispatchEvent(new Event('input', { bubbles: true }));
    }
    
    // 获取当前小数分隔符
    getCurrentDecimalSeparator(config = {}) {
        // 检查自定义分隔符
        const currentLocale = this.getCurrentLocale();
        if (config.customSeparators && config.customSeparators.has(currentLocale)) {
            return config.customSeparators.get(currentLocale);
        }
        
        // 使用本地化服务
        return localization.decimalPoint || '.';
    }
    
    // 获取当前语言环境
    getCurrentLocale() {
        return navigator.language || 'en-US';
    }
    
    // 应用设备适配
    applyDeviceAdaptations(inputs, config) {
        if (config.enableIOSAdaptation && this.deviceInfo.isIOS) {
            // 在iOS设备上移除inputmode属性
            inputs.forEach(input => {
                if (input.hasAttribute('inputmode')) {
                    input.removeAttribute('inputmode');
                }
            });
        }
        
        // Android特定适配
        if (this.deviceInfo.isAndroid) {
            this.applyAndroidAdaptations(inputs, config);
        }
        
        // 移动设备通用适配
        if (this.deviceInfo.isMobile) {
            this.applyMobileAdaptations(inputs, config);
        }
    }
    
    // Android适配
    applyAndroidAdaptations(inputs, config) {
        // 实现Android特定的适配逻辑
        console.log('Applying Android adaptations');
    }
    
    // 移动设备适配
    applyMobileAdaptations(inputs, config) {
        // 实现移动设备通用适配逻辑
        console.log('Applying mobile adaptations');
    }
    
    // 清理事件
    cleanupEvents(inputs, hookId) {
        inputs.forEach(input => {
            if (input._numpadKeydownHandler) {
                input.removeEventListener("keydown", input._numpadKeydownHandler);
                delete input._numpadKeydownHandler;
            }
            
            if (input._numpadFocusHandler) {
                input.removeEventListener("focus", input._numpadFocusHandler);
                delete input._numpadFocusHandler;
            }
        });
    }
    
    // 设备检测方法
    detectIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent);
    }
    
    detectAndroid() {
        return /Android/.test(navigator.userAgent);
    }
    
    detectMobile() {
        return /Mobi|Android/i.test(navigator.userAgent);
    }
    
    detectNumpad() {
        // 检测是否有数字键盘
        return !this.deviceInfo.isMobile;
    }
    
    // 测试分隔符替换
    testSeparatorReplacement(input, key) {
        const mockEvent = {
            key: key,
            code: "NumpadDecimal",
            target: input,
            preventDefault: () => {}
        };
        
        const decimalPoint = this.getCurrentDecimalSeparator();
        return this.shouldProcessKey(mockEvent, decimalPoint);
    }
    
    // 更新钩子配置
    updateHookConfig(hookId, newConfig) {
        const hookInfo = this.hookRegistry.get(hookId);
        if (hookInfo) {
            hookInfo.config = { ...hookInfo.config, ...newConfig };
        }
    }
    
    // 刷新钩子
    refreshHook(hookId) {
        const hookInfo = this.hookRegistry.get(hookId);
        if (hookInfo) {
            // 重新绑定事件
            this.cleanupEvents(hookInfo.inputs, hookId);
            this.bindEvents(hookInfo.inputs, hookId, hookInfo.config);
        }
    }
    
    // 记录事件
    recordFocusEvent(hookId, ev) {
        console.log(`Focus event for hook ${hookId}`);
    }
    
    // 错误处理
    handleKeydownError(hookId, error) {
        console.error(`Keydown error for hook ${hookId}:`, error);
        
        const hookInfo = this.hookRegistry.get(hookId);
        if (hookInfo) {
            hookInfo.statistics.errors++;
        }
        this.hookStatistics.errorCount++;
    }
    
    handleFocusError(hookId, error) {
        console.error(`Focus error for hook ${hookId}:`, error);
        this.hookStatistics.errorCount++;
    }
    
    // 记录性能
    recordPerformance(operation, duration) {
        // 实现性能记录逻辑
        console.log(`${operation} took ${duration}ms`);
    }
    
    // 设置本地化系统
    setupLocalizationSystem() {
        this.localizationConfig = {
            enableCustomSeparators: true,
            fallbackSeparator: '.',
            supportedLocales: ['en-US', 'fr-FR', 'de-DE', 'es-ES']
        };
    }
    
    // 设置设备适配
    setupDeviceAdaptation() {
        this.deviceConfig = {
            enableIOSAdaptation: true,
            enableAndroidAdaptation: true,
            enableMobileOptimizations: true
        };
    }
    
    // 设置性能监控
    setupPerformanceMonitoring() {
        this.performanceConfig = {
            enableMonitoring: true,
            sampleRate: 0.1,
            thresholds: {
                keydownTime: 10,  // 10ms
                focusTime: 5      // 5ms
            }
        };
    }
    
    // 创建数字键盘小数钩子
    createNumpadDecimalHook(options) {
        return this.enhancedUseNumpadDecimal(options);
    }
    
    // 获取钩子统计
    getHookStatistics(hookId = null) {
        if (hookId) {
            const hookInfo = this.hookRegistry.get(hookId);
            return hookInfo ? hookInfo.statistics : null;
        }
        
        return {
            ...this.hookStatistics,
            registeredHooks: this.hookRegistry.size,
            deviceInfo: this.deviceInfo
        };
    }
    
    // 获取所有活动钩子
    getActiveHooks() {
        return Array.from(this.hookRegistry.entries()).map(([hookId, hookInfo]) => ({
            hookId: hookId,
            config: hookInfo.config,
            statistics: hookInfo.statistics,
            inputCount: hookInfo.inputs.length
        }));
    }
    
    // 清理所有钩子
    clearAllHooks() {
        for (const [hookId, hookInfo] of this.hookRegistry.entries()) {
            this.cleanupEvents(hookInfo.inputs, hookId);
        }
        this.hookRegistry.clear();
        this.hookStatistics.activeHooks = 0;
    }
    
    // 销毁管理器
    destroy() {
        // 清理所有钩子
        this.clearAllHooks();
        
        // 重置统计
        this.hookStatistics = {
            totalHooks: 0,
            activeHooks: 0,
            keyPressCount: 0,
            replacementCount: 0,
            errorCount: 0
        };
    }
}

// 使用示例
const numpadManager = new NumpadDecimalHookManager();

// 创建数字键盘小数钩子
const numpadHook = numpadManager.createNumpadDecimalHook({
    enableAutoSelect: true,
    enableIOSAdaptation: true,
    supportedInputTypes: ['text', 'tel']
});

// 设置自定义分隔符
numpadHook.setCustomSeparator('fr-FR', ',');

// 测试分隔符替换
const input = document.createElement('input');
const shouldReplace = numpadHook.testSeparatorReplacement(input, '.');
console.log('Should replace separator:', shouldReplace);

// 获取统计信息
const stats = numpadManager.getHookStatistics();
console.log('Numpad decimal hook statistics:', stats);
```

## 技术特点

### 1. 本地化支持
- **自动检测**: 自动检测用户的语言设置
- **分隔符替换**: 智能替换小数分隔符
- **多语言**: 支持多种语言的小数格式
- **自定义配置**: 支持自定义分隔符配置

### 2. 设备适配
- **iOS适配**: 特殊的iOS设备适配
- **移动优化**: 移动设备的特殊优化
- **键盘检测**: 检测是否有物理数字键盘
- **输入模式**: 智能的输入模式处理

### 3. 用户体验
- **无缝输入**: 提供无缝的数字输入体验
- **自动选择**: 焦点时自动选择内容
- **即时替换**: 即时替换小数分隔符
- **类型感知**: 根据输入类型智能处理

### 4. 性能优化
- **事件管理**: 高效的事件绑定和清理
- **轻量级**: 轻量级的实现
- **内存友好**: 有效的内存使用
- **响应迅速**: 快速的按键响应

## 设计模式

### 1. 钩子模式 (Hook Pattern)
- **生命周期**: 管理组件生命周期
- **副作用**: 处理键盘事件等副作用
- **清理机制**: 自动清理事件监听器

### 2. 策略模式 (Strategy Pattern)
- **设备策略**: 不同设备的处理策略
- **本地化策略**: 不同语言的本地化策略
- **输入策略**: 不同输入类型的处理策略

### 3. 适配器模式 (Adapter Pattern)
- **设备适配**: 适配不同设备的特性
- **浏览器适配**: 适配不同浏览器的行为
- **输入适配**: 适配不同输入方式

### 4. 观察者模式 (Observer Pattern)
- **事件监听**: 监听键盘和焦点事件
- **状态变化**: 响应输入状态变化
- **自动更新**: 自动更新分隔符

## 注意事项

1. **设备兼容**: 确保在不同设备上的兼容性
2. **性能考虑**: 避免频繁的事件处理
3. **内存管理**: 及时清理事件监听器
4. **用户体验**: 确保自然的输入体验

## 扩展建议

1. **更多本地化**: 支持更多语言的数字格式
2. **智能检测**: 智能检测用户的输入习惯
3. **自定义规则**: 支持自定义的替换规则
4. **统计分析**: 添加使用统计和分析
5. **测试工具**: 提供测试和调试工具

该数字键盘小数钩子为Odoo Web客户端提供了智能的数字输入本地化功能，通过自动替换小数分隔符和设备适配确保了良好的国际化用户体验。

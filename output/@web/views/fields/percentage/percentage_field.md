# PercentageField - 百分比字段

## 概述

`percentage_field.js` 是 Odoo Web 客户端的百分比字段组件，负责处理百分比数据的输入、显示和格式化。该模块包含68行代码，是一个功能完整的百分比处理组件，专门用于处理integer和float类型的百分比字段，具备百分比符号、精度控制、数字键盘、输入解析等特性，是数值数据处理的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/percentage/percentage_field.js`
- **行数**: 68
- **模块**: `@web/views/fields/percentage/percentage_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                    // 注册表
'@web/core/l10n/translation'            // 翻译服务
'@web/views/fields/formatters'          // 字段格式化器
'@web/views/fields/parsers'             // 字段解析器
'@web/views/fields/input_field_hook'    // 输入字段钩子
'@web/views/fields/numpad_decimal_hook' // 数字键盘小数钩子
'@web/views/fields/standard_field_props' // 标准字段属性
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const PercentageField = class PercentageField extends Component {
    static template = "web.PercentageField";
    static props = {
        ...standardFieldProps,
        digits: { type: Array, optional: true },
        placeholder: { type: String, optional: true },
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **精度配置**: 支持digits数组配置精度
- **占位符**: 支持placeholder配置占位符
- **专用模板**: 使用PercentageField专用模板

### 2. 组件初始化

```javascript
setup() {
    useInputField({
        getValue: () =>
            formatPercentage(this.props.record.data[this.props.name], {
                digits: this.props.digits,
                noSymbol: true,
                field: this.props.record.fields[this.props.name],
            }),
        refName: "numpadDecimal",
        parse: (v) => parsePercentage(v),
    });
    useNumpadDecimal();
}
```

**初始化功能**:
- **输入钩子**: 使用输入字段钩子管理输入
- **格式化**: 使用formatPercentage格式化显示值
- **无符号**: 输入时不显示百分号
- **解析器**: 使用parsePercentage解析输入
- **数字键盘**: 使用数字键盘小数钩子

### 3. 格式化显示

```javascript
get formattedValue() {
    return formatPercentage(this.props.record.data[this.props.name], {
        digits: this.props.digits,
        field: this.props.record.fields[this.props.name],
    });
}
```

**格式化功能**:
- **百分比格式化**: 使用formatPercentage格式化百分比
- **精度控制**: 根据digits配置控制精度
- **符号显示**: 显示时包含百分号
- **字段信息**: 使用字段信息进行格式化

### 4. 属性提取

```javascript
extractProps: ({ attrs, options }) => {
    // Sadly, digits param was available as an option and an attr.
    // The option version could be removed with some xml refactoring.
    let digits;
    if (attrs.digits) {
        digits = JSON.parse(attrs.digits);
    } else if (options.digits) {
        digits = options.digits;
    }

    return {
        digits,
        placeholder: attrs.placeholder,
    };
}
```

**属性提取功能**:
- **精度提取**: 从attrs或options中提取digits
- **JSON解析**: 解析attrs中的JSON格式digits
- **兼容性**: 兼容两种digits配置方式
- **占位符**: 提取placeholder属性

### 5. 字段注册

```javascript
const percentageField = {
    component: PercentageField,
    displayName: _t("Percentage"),
    supportedTypes: ["integer", "float"],
    extractProps: ({ attrs, options }) => { /* ... */ },
};

registry.category("fields").add("percentage", percentageField);
```

**注册功能**:
- **组件注册**: 注册百分比字段组件
- **显示名称**: 设置为"Percentage"
- **类型支持**: 支持integer和float类型
- **属性提取**: 提取配置属性

## 使用场景

### 1. 百分比字段管理器

```javascript
// 百分比字段管理器
class PercentageFieldManager {
    constructor() {
        this.setupManager();
    }

    setupManager() {
        // 设置百分比字段配置
        this.percentageConfig = {
            enableValidation: true,
            enableFormatting: true,
            enableCalculation: true,
            enableComparison: true,
            enableRounding: true,
            enableRangeCheck: true,
            enableColorCoding: false,
            enableStatistics: true
        };

        // 设置显示选项
        this.displayOptions = {
            showSymbol: true,
            symbolPosition: 'after', // 'before', 'after'
            decimalPlaces: 2,
            useThousandSeparator: false,
            enableColorCoding: false,
            positiveColor: '#28a745',
            negativeColor: '#dc3545',
            zeroColor: '#6c757d'
        };

        // 设置验证规则
        this.validationRules = {
            enableRangeValidation: true,
            minValue: 0,
            maxValue: 100,
            allowNegative: false,
            allowDecimal: true,
            maxDecimalPlaces: 4,
            enableCustomRanges: false,
            customRanges: []
        };

        // 设置计算配置
        this.calculationConfig = {
            enableBasicOperations: true,
            enablePercentageOf: true,
            enablePercentageChange: true,
            enableCompoundPercentage: false,
            roundingMode: 'round', // 'round', 'floor', 'ceil'
            precision: 2
        };

        // 设置百分比统计
        this.percentageStatistics = {
            totalFields: 0,
            totalValues: 0,
            averageValue: 0,
            maxValue: 0,
            minValue: Number.MAX_SAFE_INTEGER,
            zeroValues: 0,
            negativeValues: 0,
            valueDistribution: new Map()
        };

        this.initializePercentageSystem();
    }

    // 初始化百分比系统
    initializePercentageSystem() {
        // 创建增强的百分比字段
        this.createEnhancedPercentageField();

        // 设置格式化系统
        this.setupFormattingSystem();

        // 设置验证系统
        this.setupValidationSystem();

        // 设置计算系统
        this.setupCalculationSystem();
    }

    // 创建增强的百分比字段
    createEnhancedPercentageField() {
        const originalField = PercentageField;

        this.EnhancedPercentageField = class extends originalField {
            setup() {
                super.setup();

                // 添加增强功能
                this.addEnhancedFeatures();

                // 添加验证功能
                this.addValidationFeatures();

                // 添加计算功能
                this.addCalculationFeatures();
            }

            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    validationErrors: [],
                    calculationHistory: [],
                    lastValidValue: null,
                    isCalculating: false,
                    comparisonValue: null,
                    changeRate: null
                };

                // 添加增强方法
                this.addEnhancedMethods();
            }

            addEnhancedMethods() {
                // 增强的格式化值
                this.enhancedFormattedValue = () => {
                    const value = this.props.record.data[this.props.name];

                    if (value === null || value === undefined) {
                        return '';
                    }

                    // 验证值
                    this.validatePercentage(value);

                    // 格式化
                    const formatted = formatPercentage(value, {
                        digits: this.props.digits,
                        field: this.props.record.fields[this.props.name],
                        noSymbol: false
                    });

                    // 应用颜色编码
                    if (this.displayOptions.enableColorCoding) {
                        return this.applyColorCoding(formatted, value);
                    }

                    return formatted;
                };

                // 验证百分比
                this.validatePercentage = (value) => {
                    const errors = [];

                    if (typeof value !== 'number') {
                        errors.push('Value must be a number');
                    }

                    if (this.validationRules.enableRangeValidation) {
                        if (value < this.validationRules.minValue) {
                            errors.push(`Value must be at least ${this.validationRules.minValue}%`);
                        }
                        if (value > this.validationRules.maxValue) {
                            errors.push(`Value must not exceed ${this.validationRules.maxValue}%`);
                        }
                    }

                    if (!this.validationRules.allowNegative && value < 0) {
                        errors.push('Negative values are not allowed');
                    }

                    if (!this.validationRules.allowDecimal && value % 1 !== 0) {
                        errors.push('Decimal values are not allowed');
                    }

                    if (this.validationRules.allowDecimal) {
                        const decimalPlaces = this.getDecimalPlaces(value);
                        if (decimalPlaces > this.validationRules.maxDecimalPlaces) {
                            errors.push(`Maximum ${this.validationRules.maxDecimalPlaces} decimal places allowed`);
                        }
                    }

                    this.enhancedState.validationErrors = errors;

                    if (errors.length === 0) {
                        this.enhancedState.lastValidValue = value;
                    }

                    return errors.length === 0;
                };

                // 获取小数位数
                this.getDecimalPlaces = (value) => {
                    const str = value.toString();
                    const decimalIndex = str.indexOf('.');
                    return decimalIndex === -1 ? 0 : str.length - decimalIndex - 1;
                };

                // 应用颜色编码
                this.applyColorCoding = (formattedValue, value) => {
                    let color;

                    if (value > 0) {
                        color = this.displayOptions.positiveColor;
                    } else if (value < 0) {
                        color = this.displayOptions.negativeColor;
                    } else {
                        color = this.displayOptions.zeroColor;
                    }

                    return `<span style="color: ${color}">${formattedValue}</span>`;
                };

                // 计算百分比
                this.calculatePercentage = (operation, operand) => {
                    if (!this.percentageConfig.enableCalculation) {
                        return this.getValue();
                    }

                    const currentValue = this.getValue() || 0;
                    let result;

                    switch (operation) {
                        case 'add':
                            result = currentValue + operand;
                            break;
                        case 'subtract':
                            result = currentValue - operand;
                            break;
                        case 'multiply':
                            result = currentValue * operand;
                            break;
                        case 'divide':
                            result = operand !== 0 ? currentValue / operand : currentValue;
                            break;
                        case 'percentageOf':
                            result = (currentValue / 100) * operand;
                            break;
                        case 'percentageChange':
                            result = operand !== 0 ? ((currentValue - operand) / operand) * 100 : 0;
                            break;
                        default:
                            result = currentValue;
                    }

                    // 四舍五入
                    if (this.percentageConfig.enableRounding) {
                        result = this.roundValue(result);
                    }

                    // 记录计算历史
                    this.addToCalculationHistory(operation, operand, currentValue, result);

                    return result;
                };

                // 四舍五入值
                this.roundValue = (value) => {
                    const precision = this.calculationConfig.precision;
                    const factor = Math.pow(10, precision);

                    switch (this.calculationConfig.roundingMode) {
                        case 'floor':
                            return Math.floor(value * factor) / factor;
                        case 'ceil':
                            return Math.ceil(value * factor) / factor;
                        case 'round':
                        default:
                            return Math.round(value * factor) / factor;
                    }
                };

                // 添加到计算历史
                this.addToCalculationHistory = (operation, operand, originalValue, result) => {
                    const historyItem = {
                        operation,
                        operand,
                        originalValue,
                        result,
                        timestamp: new Date()
                    };

                    this.enhancedState.calculationHistory.unshift(historyItem);

                    // 限制历史大小
                    if (this.enhancedState.calculationHistory.length > 20) {
                        this.enhancedState.calculationHistory.pop();
                    }
                };

                // 获取值
                this.getValue = () => {
                    return this.props.record.data[this.props.name];
                };

                // 比较百分比
                this.comparePercentage = (compareValue) => {
                    const currentValue = this.getValue() || 0;

                    this.enhancedState.comparisonValue = compareValue;
                    this.enhancedState.changeRate = this.calculatePercentage('percentageChange', compareValue);

                    return {
                        current: currentValue,
                        comparison: compareValue,
                        difference: currentValue - compareValue,
                        changeRate: this.enhancedState.changeRate,
                        isIncrease: currentValue > compareValue,
                        isDecrease: currentValue < compareValue,
                        isEqual: currentValue === compareValue
                    };
                };
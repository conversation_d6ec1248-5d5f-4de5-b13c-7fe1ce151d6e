# PercentPieField - 百分比饼图字段

## 概述

`percent_pie_field.js` 是 Odoo Web 客户端的百分比饼图字段组件，负责以饼图形式显示百分比数据。该模块包含39行代码，是一个简洁的可视化组件，专门用于处理float和integer类型的百分比字段，具备饼图显示、格式化、CSS样式等特性，是数据可视化的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/percent_pie/percent_pie_field.js`
- **行数**: 39
- **模块**: `@web/views/fields/percent_pie/percent_pie_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                    // 注册表
'@web/core/l10n/translation'            // 翻译服务
'@web/views/fields/formatters'          // 字段格式化器
'@web/views/fields/standard_field_props' // 标准字段属性
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const PercentPieField = class PercentPieField extends Component {
    static template = "web.PercentPieField";
    static props = {
        ...standardFieldProps,
        string: { type: String, optional: true },
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **字符串属性**: 支持string属性配置显示文本
- **专用模板**: 使用PercentPieField专用模板
- **饼图显示**: 专门用于饼图形式显示

### 2. 格式化值

```javascript
get formattedValue() {
    return formatFloat(this.props.record.data[this.props.name], {
        trailingZeros: false,
    });
}
```

**格式化功能**:
- **浮点格式化**: 使用formatFloat格式化数值
- **去除尾零**: 不显示尾随零
- **精度控制**: 默认保留2位小数
- **数值优化**: 优化数值显示效果

### 3. 字段注册

```javascript
const percentPieField = {
    component: PercentPieField,
    displayName: _t("PercentPie"),
    supportedTypes: ["float", "integer"],
    additionalClasses: ["o_field_percent_pie"],
    extractProps: ({ string }) => ({ string }),
};

registry.category("fields").add("percentpie", percentPieField);
```

**注册功能**:
- **组件注册**: 注册百分比饼图字段组件
- **显示名称**: 设置为"PercentPie"
- **类型支持**: 支持float和integer类型
- **CSS类**: 添加o_field_percent_pie CSS类
- **属性提取**: 提取string属性

## 使用场景

### 1. 百分比饼图字段管理器

```javascript
// 百分比饼图字段管理器
class PercentPieFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置饼图字段配置
        this.pieConfig = {
            enableAnimation: true,
            enableTooltips: true,
            enableLegend: false,
            enableInteraction: false,
            enableColorGradient: true,
            enableThemes: true,
            enableCustomColors: false,
            enableDataLabels: true
        };
        
        // 设置饼图样式
        this.pieStyles = {
            size: 60, // 饼图大小(像素)
            strokeWidth: 2,
            backgroundColor: '#f8f9fa',
            completedColor: '#28a745',
            remainingColor: '#e9ecef',
            textColor: '#495057',
            fontSize: 12,
            fontWeight: 'bold'
        };
        
        // 设置颜色主题
        this.colorThemes = new Map([
            ['default', { completed: '#28a745', remaining: '#e9ecef' }],
            ['primary', { completed: '#007bff', remaining: '#e3f2fd' }],
            ['success', { completed: '#28a745', remaining: '#d4edda' }],
            ['warning', { completed: '#ffc107', remaining: '#fff3cd' }],
            ['danger', { completed: '#dc3545', remaining: '#f8d7da' }],
            ['info', { completed: '#17a2b8', remaining: '#d1ecf1' }],
            ['dark', { completed: '#343a40', remaining: '#f8f9fa' }]
        ]);
        
        // 设置动画配置
        this.animationConfig = {
            enableAnimation: true,
            duration: 800,
            easing: 'ease-out',
            delay: 0,
            enableHoverAnimation: true,
            hoverScale: 1.05
        };
        
        // 设置饼图统计
        this.pieStatistics = {
            totalPieFields: 0,
            totalValues: 0,
            averageValue: 0,
            maxValue: 0,
            minValue: Number.MAX_SAFE_INTEGER,
            completedCount: 0,
            valueDistribution: new Map()
        };
        
        this.initializePieSystem();
    }
    
    // 初始化饼图系统
    initializePieSystem() {
        // 创建增强的百分比饼图字段
        this.createEnhancedPercentPieField();
        
        // 设置渲染系统
        this.setupRenderingSystem();
        
        // 设置主题系统
        this.setupThemeSystem();
        
        // 设置动画系统
        this.setupAnimationSystem();
    }
    
    // 创建增强的百分比饼图字段
    createEnhancedPercentPieField() {
        const originalField = PercentPieField;
        
        this.EnhancedPercentPieField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加渲染功能
                this.addRenderingFeatures();
                
                // 添加交互功能
                this.addInteractionFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    currentTheme: 'default',
                    isHovered: false,
                    isAnimating: false,
                    svgElement: null,
                    tooltipVisible: false,
                    lastRenderTime: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的格式化值
                this.enhancedFormattedValue = () => {
                    const value = this.props.record.data[this.props.name];
                    
                    if (value === null || value === undefined) {
                        return '0';
                    }
                    
                    // 限制值在0-100之间
                    const clampedValue = Math.max(0, Math.min(100, value));
                    
                    return formatFloat(clampedValue, {
                        trailingZeros: false,
                        digits: [false, 1] // 最多1位小数
                    });
                };
                
                // 获取百分比值
                this.getPercentValue = () => {
                    const value = this.props.record.data[this.props.name];
                    return Math.max(0, Math.min(100, value || 0));
                };
                
                // 生成SVG饼图
                this.generatePieSvg = () => {
                    const percent = this.getPercentValue();
                    const size = this.pieStyles.size;
                    const strokeWidth = this.pieStyles.strokeWidth;
                    const radius = (size - strokeWidth) / 2;
                    const circumference = 2 * Math.PI * radius;
                    const strokeDasharray = circumference;
                    const strokeDashoffset = circumference - (percent / 100) * circumference;
                    
                    const theme = this.colorThemes.get(this.enhancedState.currentTheme);
                    
                    return `
                        <svg width="${size}" height="${size}" class="o_percent_pie_svg">
                            <!-- 背景圆 -->
                            <circle
                                cx="${size / 2}"
                                cy="${size / 2}"
                                r="${radius}"
                                fill="none"
                                stroke="${theme.remaining}"
                                stroke-width="${strokeWidth}"
                            />
                            <!-- 进度圆 -->
                            <circle
                                cx="${size / 2}"
                                cy="${size / 2}"
                                r="${radius}"
                                fill="none"
                                stroke="${theme.completed}"
                                stroke-width="${strokeWidth}"
                                stroke-dasharray="${strokeDasharray}"
                                stroke-dashoffset="${strokeDashoffset}"
                                stroke-linecap="round"
                                transform="rotate(-90 ${size / 2} ${size / 2})"
                                class="o_percent_pie_progress"
                            />
                            <!-- 中心文本 -->
                            <text
                                x="${size / 2}"
                                y="${size / 2}"
                                text-anchor="middle"
                                dominant-baseline="central"
                                font-size="${this.pieStyles.fontSize}"
                                font-weight="${this.pieStyles.fontWeight}"
                                fill="${this.pieStyles.textColor}"
                                class="o_percent_pie_text"
                            >
                                ${this.enhancedFormattedValue()}%
                            </text>
                        </svg>
                    `;
                };
                
                // 生成Canvas饼图
                this.generatePieCanvas = () => {
                    const percent = this.getPercentValue();
                    const size = this.pieStyles.size;
                    const canvas = document.createElement('canvas');
                    canvas.width = size;
                    canvas.height = size;
                    canvas.className = 'o_percent_pie_canvas';
                    
                    const ctx = canvas.getContext('2d');
                    const centerX = size / 2;
                    const centerY = size / 2;
                    const radius = size / 2 - this.pieStyles.strokeWidth;
                    
                    const theme = this.colorThemes.get(this.enhancedState.currentTheme);
                    
                    // 绘制背景圆
                    ctx.beginPath();
                    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
                    ctx.fillStyle = theme.remaining;
                    ctx.fill();
                    
                    // 绘制进度扇形
                    if (percent > 0) {
                        ctx.beginPath();
                        ctx.moveTo(centerX, centerY);
                        ctx.arc(centerX, centerY, radius, -Math.PI / 2, -Math.PI / 2 + (percent / 100) * 2 * Math.PI);
                        ctx.closePath();
                        ctx.fillStyle = theme.completed;
                        ctx.fill();
                    }
                    
                    // 绘制中心文本
                    ctx.fillStyle = this.pieStyles.textColor;
                    ctx.font = `${this.pieStyles.fontWeight} ${this.pieStyles.fontSize}px Arial`;
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillText(`${this.enhancedFormattedValue()}%`, centerX, centerY);
                    
                    return canvas;
                };
                
                // 设置主题
                this.setTheme = (themeName) => {
                    if (this.colorThemes.has(themeName)) {
                        this.enhancedState.currentTheme = themeName;
                        this.renderPie();
                    }
                };
                
                // 渲染饼图
                this.renderPie = () => {
                    if (!this.pieConfig.enableAnimation) {
                        this.renderStaticPie();
                    } else {
                        this.renderAnimatedPie();
                    }
                    
                    this.enhancedState.lastRenderTime = new Date();
                };
                
                // 渲染静态饼图
                this.renderStaticPie = () => {
                    const container = this.getPieContainer();
                    if (!container) return;
                    
                    container.innerHTML = this.generatePieSvg();
                    this.enhancedState.svgElement = container.querySelector('svg');
                };
                
                // 渲染动画饼图
                this.renderAnimatedPie = () => {
                    const container = this.getPieContainer();
                    if (!container) return;
                    
                    container.innerHTML = this.generatePieSvg();
                    this.enhancedState.svgElement = container.querySelector('svg');
                    
                    // 添加动画
                    const progressCircle = container.querySelector('.o_percent_pie_progress');
                    if (progressCircle) {
                        progressCircle.style.transition = `stroke-dashoffset ${this.animationConfig.duration}ms ${this.animationConfig.easing}`;
                    }
                    
                    const text = container.querySelector('.o_percent_pie_text');
                    if (text) {
                        this.animateText(text);
                    }
                };
                
                // 动画文本
                this.animateText = (textElement) => {
                    const targetValue = this.getPercentValue();
                    let currentValue = 0;
                    const duration = this.animationConfig.duration;
                    const startTime = performance.now();
                    
                    const animate = (currentTime) => {
                        const elapsed = currentTime - startTime;
                        const progress = Math.min(elapsed / duration, 1);
                        
                        currentValue = progress * targetValue;
                        textElement.textContent = `${Math.round(currentValue)}%`;
                        
                        if (progress < 1) {
                            requestAnimationFrame(animate);
                        }
                    };
                    
                    requestAnimationFrame(animate);
                };
                
                // 获取饼图容器
                this.getPieContainer = () => {
                    // 这里应该返回实际的DOM容器元素
                    return document.querySelector('.o_field_percent_pie');
                };
                
                // 获取工具提示内容
                this.getTooltipContent = () => {
                    if (!this.pieConfig.enableTooltips) {
                        return null;
                    }
                    
                    const value = this.getPercentValue();
                    const fieldName = this.props.string || this.props.name;
                    
                    return `${fieldName}: ${this.enhancedFormattedValue()}%`;
                };
                
                // 显示工具提示
                this.showTooltip = (event) => {
                    if (!this.pieConfig.enableTooltips) return;
                    
                    const tooltip = this.getTooltipContent();
                    if (tooltip) {
                        // 实现工具提示显示逻辑
                        this.enhancedState.tooltipVisible = true;
                    }
                };
                
                // 隐藏工具提示
                this.hideTooltip = () => {
                    this.enhancedState.tooltipVisible = false;
                };
                
                // 鼠标悬停处理
                this.onMouseEnter = (event) => {
                    this.enhancedState.isHovered = true;
                    
                    if (this.animationConfig.enableHoverAnimation) {
                        this.applyHoverEffect();
                    }
                    
                    this.showTooltip(event);
                };
                
                // 鼠标离开处理
                this.onMouseLeave = () => {
                    this.enhancedState.isHovered = false;
                    
                    if (this.animationConfig.enableHoverAnimation) {
                        this.removeHoverEffect();
                    }
                    
                    this.hideTooltip();
                };
                
                // 应用悬停效果
                this.applyHoverEffect = () => {
                    if (this.enhancedState.svgElement) {
                        this.enhancedState.svgElement.style.transform = `scale(${this.animationConfig.hoverScale})`;
                        this.enhancedState.svgElement.style.transition = 'transform 0.2s ease';
                    }
                };
                
                // 移除悬停效果
                this.removeHoverEffect = () => {
                    if (this.enhancedState.svgElement) {
                        this.enhancedState.svgElement.style.transform = 'scale(1)';
                    }
                };
                
                // 获取饼图信息
                this.getPieInfo = () => {
                    return {
                        value: this.getPercentValue(),
                        formattedValue: this.enhancedFormattedValue(),
                        theme: this.enhancedState.currentTheme,
                        isHovered: this.enhancedState.isHovered,
                        isAnimating: this.enhancedState.isAnimating,
                        lastRenderTime: this.enhancedState.lastRenderTime,
                        tooltip: this.getTooltipContent()
                    };
                };
                
                // 导出饼图
                this.exportPie = (format = 'svg') => {
                    switch (format) {
                        case 'svg':
                            return this.generatePieSvg();
                        case 'canvas':
                            return this.generatePieCanvas();
                        case 'png':
                            return this.exportAsPng();
                        default:
                            return this.generatePieSvg();
                    }
                };
                
                // 导出为PNG
                this.exportAsPng = () => {
                    const canvas = this.generatePieCanvas();
                    return canvas.toDataURL('image/png');
                };
                
                // 记录饼图统计
                this.recordPieStatistics = () => {
                    const value = this.getPercentValue();
                    
                    this.pieStatistics.totalValues += value;
                    
                    if (value > this.pieStatistics.maxValue) {
                        this.pieStatistics.maxValue = value;
                    }
                    if (value < this.pieStatistics.minValue) {
                        this.pieStatistics.minValue = value;
                    }
                    
                    if (value >= 100) {
                        this.pieStatistics.completedCount++;
                    }
                    
                    // 记录值分布
                    const range = Math.floor(value / 10) * 10; // 按10%分组
                    const count = this.pieStatistics.valueDistribution.get(range) || 0;
                    this.pieStatistics.valueDistribution.set(range, count + 1);
                    
                    this.pieStatistics.averageValue = 
                        this.pieStatistics.totalValues / this.pieStatistics.totalPieFields;
                };
            }
            
            addRenderingFeatures() {
                // 渲染功能
                this.renderingManager = {
                    enabled: true,
                    render: () => this.renderPie(),
                    setTheme: (theme) => this.setTheme(theme),
                    export: (format) => this.exportPie(format),
                    getInfo: () => this.getPieInfo()
                };
            }
            
            addInteractionFeatures() {
                // 交互功能
                this.interactionManager = {
                    enabled: this.pieConfig.enableInteraction,
                    onMouseEnter: (event) => this.onMouseEnter(event),
                    onMouseLeave: () => this.onMouseLeave(),
                    showTooltip: (event) => this.showTooltip(event),
                    hideTooltip: () => this.hideTooltip()
                };
            }
            
            // 重写原始方法
            get formattedValue() {
                return this.enhancedFormattedValue();
            }
        };
    }
    
    // 设置渲染系统
    setupRenderingSystem() {
        this.renderingSystemConfig = {
            enabled: true,
            styles: this.pieStyles,
            themes: this.colorThemes
        };
    }
    
    // 设置主题系统
    setupThemeSystem() {
        this.themeSystemConfig = {
            enabled: this.pieConfig.enableThemes,
            themes: this.colorThemes,
            defaultTheme: 'default'
        };
    }
    
    // 设置动画系统
    setupAnimationSystem() {
        this.animationSystemConfig = {
            enabled: this.pieConfig.enableAnimation,
            config: this.animationConfig
        };
    }
    
    // 创建百分比饼图字段
    createPercentPieField(props) {
        const field = new this.EnhancedPercentPieField(props);
        this.pieStatistics.totalPieFields++;
        return field;
    }
    
    // 批量创建饼图字段
    batchCreatePieFields(propsArray) {
        const results = [];
        
        for (const props of propsArray) {
            try {
                const field = this.createPercentPieField(props);
                results.push({ field, success: true, props });
            } catch (error) {
                results.push({ field: null, success: false, error, props });
            }
        }
        
        return results;
    }
    
    // 注册颜色主题
    registerColorTheme(name, colors) {
        this.colorThemes.set(name, colors);
    }
    
    // 获取值分布
    getValueDistribution() {
        const distribution = [];
        
        for (const [range, count] of this.pieStatistics.valueDistribution.entries()) {
            distribution.push({
                range: `${range}-${range + 9}%`,
                count: count,
                percentage: (count / this.pieStatistics.totalPieFields * 100).toFixed(1)
            });
        }
        
        return distribution.sort((a, b) => parseInt(a.range) - parseInt(b.range));
    }
    
    // 获取饼图统计
    getPieStatistics() {
        return {
            ...this.pieStatistics,
            completionRate: (this.pieStatistics.completedCount / Math.max(this.pieStatistics.totalPieFields, 1)) * 100,
            averageValueFormatted: `${this.pieStatistics.averageValue.toFixed(1)}%`,
            maxValueFormatted: `${this.pieStatistics.maxValue}%`,
            minValueFormatted: `${this.pieStatistics.minValue === Number.MAX_SAFE_INTEGER ? 0 : this.pieStatistics.minValue}%`,
            valueDistribution: this.getValueDistribution(),
            themeCount: this.colorThemes.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理主题
        this.colorThemes.clear();
        
        // 清理值分布
        this.pieStatistics.valueDistribution.clear();
        
        // 重置统计
        this.pieStatistics = {
            totalPieFields: 0,
            totalValues: 0,
            averageValue: 0,
            maxValue: 0,
            minValue: Number.MAX_SAFE_INTEGER,
            completedCount: 0,
            valueDistribution: new Map()
        };
    }
}

// 使用示例
const pieManager = new PercentPieFieldManager();

// 创建百分比饼图字段
const pieField = pieManager.createPercentPieField({
    name: 'completion_rate',
    record: {
        data: { completion_rate: 75.5 },
        fields: { 
            completion_rate: { 
                type: 'float'
            }
        }
    },
    string: 'Completion Rate'
});

// 注册自定义主题
pieManager.registerColorTheme('custom', {
    completed: '#ff6b6b',
    remaining: '#ffe0e0'
});

// 获取统计信息
const stats = pieManager.getPieStatistics();
console.log('Percent pie field statistics:', stats);
```

## 技术特点

### 1. 可视化显示
- **饼图形式**: 以饼图形式直观显示百分比
- **SVG渲染**: 使用SVG进行高质量渲染
- **动画效果**: 支持动画效果增强体验
- **主题支持**: 支持多种颜色主题

### 2. 数据处理
- **格式化**: 智能的数值格式化
- **范围限制**: 限制值在0-100%范围内
- **精度控制**: 控制小数位数显示
- **去除尾零**: 优化数值显示

### 3. 样式控制
- **CSS类**: 添加专用CSS类
- **自定义样式**: 支持自定义样式配置
- **响应式**: 响应式设计适配不同屏幕
- **主题切换**: 支持主题切换功能

### 4. 简洁设计
- **最小代码**: 极简的代码实现
- **专注功能**: 专注于百分比显示
- **易于扩展**: 易于扩展和定制
- **性能优化**: 优化的渲染性能

## 设计模式

### 1. 组件模式 (Component Pattern)
- **UI组件**: 封装饼图显示UI
- **状态管理**: 管理饼图状态
- **渲染控制**: 控制饼图渲染

### 2. 模板方法模式 (Template Method Pattern)
- **渲染模板**: 定义饼图渲染模板
- **格式化模板**: 定义数值格式化模板
- **样式模板**: 定义样式应用模板

### 3. 策略模式 (Strategy Pattern)
- **渲染策略**: 不同的渲染策略
- **主题策略**: 不同的主题策略
- **动画策略**: 不同的动画策略

### 4. 装饰器模式 (Decorator Pattern)
- **样式装饰**: 为饼图添加样式装饰
- **动画装饰**: 添加动画效果装饰
- **交互装饰**: 添加交互效果装饰

## 注意事项

1. **数值范围**: 确保百分比值在合理范围内
2. **性能考虑**: 避免频繁的重新渲染
3. **浏览器兼容**: 确保SVG的浏览器兼容性
4. **可访问性**: 考虑视觉障碍用户的可访问性

## 扩展建议

1. **交互功能**: 添加点击交互功能
2. **数据标签**: 支持数据标签显示
3. **多段显示**: 支持多段百分比显示
4. **导出功能**: 支持图表导出功能
5. **实时更新**: 支持数据实时更新

该百分比饼图字段为Odoo Web客户端提供了直观的百分比数据可视化功能，通过简洁的设计和灵活的配置确保了良好的数据展示效果和用户体验。

# AceField - Ace代码编辑器字段

## 概述

`ace_field.js` 是 Odoo Web 客户端的 Ace 代码编辑器字段组件，负责提供高级代码编辑功能。该模块包含89行代码，是一个基于 Ace Editor 的字段组件，专门用于编辑代码、XML、HTML等文本内容，具备语法高亮、代码折叠、自动完成、主题切换等特性，是开发和配置场景中的重要工具。

## 文件信息
- **路径**: `/web/static/src/views/fields/ace/ace_field.js`
- **行数**: 89
- **模块**: `@web/views/fields/ace/ace_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/core/utils/hooks'                 // 工具钩子
'@web/views/fields/standard_field_props' // 标准字段属性
'@web/core/code_editor/code_editor'     // 代码编辑器
'@odoo/owl'                            // OWL框架
'@web/model/relational_model/utils'     // 关系模型工具
'@web/views/fields/formatters'          // 字段格式化器
'@web/core/browser/cookie'              // Cookie工具
```

## 核心功能

### 1. 组件定义

```javascript
const AceField = class AceField extends Component {
    static template = "web.AceField";
    static props = {
        ...standardFieldProps,
        mode: { type: String, optional: true },
    };
    static defaultProps = {
        mode: "qweb",
    };
    static components = { CodeEditor };
}
```

**组件特性**:
- **继承标准属性**: 继承所有标准字段属性
- **模式配置**: 支持不同的编辑模式
- **默认模式**: 默认使用qweb模式
- **代码编辑器**: 集成CodeEditor组件

### 2. 组件初始化

```javascript
setup() {
    this.state = useState({});
    useRecordObserver((record) => {
        this.state.initialValue = formatText(record.data[this.props.name]);
    });

    this.isDirty = false;

    const { model } = this.props.record;
    useBus(model.bus, "WILL_SAVE_URGENTLY", () => this.commitChanges());
    useBus(model.bus, "NEED_LOCAL_CHANGES", ({ detail }) =>
        detail.proms.push(this.commitChanges())
    );
}
```

**初始化功能**:
- **状态管理**: 初始化组件状态
- **记录观察**: 观察记录变化并更新初始值
- **脏数据标记**: 跟踪字段是否被修改
- **事件监听**: 监听保存和变更事件

### 3. 模式和主题

```javascript
get mode() {
    return this.props.mode === "xml" ? "qweb" : this.props.mode;
}

get theme() {
    return cookie.get("color_scheme") === "dark" ? "monokai" : "";
}
```

**模式主题功能**:
- **模式映射**: 将xml模式映射为qweb模式
- **主题切换**: 根据用户颜色方案切换主题
- **暗色主题**: 支持monokai暗色主题
- **Cookie读取**: 从Cookie读取用户偏好

### 4. 变更处理

```javascript
handleChange(editedValue) {
    if (this.state.initialValue !== editedValue) {
        this.isDirty = true;
    } else {
        this.isDirty = false;
    }
    this.props.record.model.bus.trigger("FIELD_IS_DIRTY", this.isDirty);
    this.editedValue = editedValue;
}
```

**变更处理功能**:
- **脏数据检测**: 检测内容是否被修改
- **状态广播**: 广播字段脏数据状态
- **值存储**: 存储编辑后的值
- **实时更新**: 实时更新脏数据状态

### 5. 提交变更

```javascript
async commitChanges() {
    if (!this.props.readonly && this.isDirty) {
        if (this.state.initialValue !== this.editedValue) {
            await this.props.record.update({ [this.props.name]: this.editedValue });
        }
        this.isDirty = false;
        this.props.record.model.bus.trigger("FIELD_IS_DIRTY", false);
    }
}
```

**提交功能**:
- **只读检查**: 检查字段是否为只读
- **脏数据检查**: 只提交被修改的数据
- **记录更新**: 更新记录中的字段值
- **状态重置**: 重置脏数据状态

### 6. 字段注册

```javascript
const aceField = {
    component: AceField,
    displayName: _t("Ace Editor"),
    supportedOptions: [
        {
            label: _t("Mode"),
            name: "mode",
            type: "string",
        },
    ],
    supportedTypes: ["text", "html"],
    extractProps: ({ options }) => ({
        mode: options.mode,
    }),
};

registry.category("fields").add("ace", aceField);
registry.category("fields").add("code", aceField);
```

**注册功能**:
- **字段定义**: 定义字段的基本信息
- **支持选项**: 定义支持的配置选项
- **支持类型**: 支持text和html类型
- **属性提取**: 从选项中提取属性
- **多重注册**: 注册为ace和code两种字段类型

## 使用场景

### 1. 代码编辑器管理器

```javascript
// 代码编辑器管理器
class AceFieldManager {
    constructor() {
        this.setupManager();
    }

    setupManager() {
        // 设置编辑器配置
        this.editorConfig = {
            enableSyntaxHighlight: true,
            enableAutoComplete: true,
            enableCodeFolding: true,
            enableLineNumbers: true,
            enableWordWrap: false,
            tabSize: 4,
            fontSize: 14,
            enableLiveAutocompletion: true,
            enableSnippets: true,
            enableEmmet: true
        };

        // 设置支持的模式
        this.supportedModes = new Map([
            ['qweb', { name: 'QWeb Template', extension: '.xml' }],
            ['xml', { name: 'XML', extension: '.xml' }],
            ['html', { name: 'HTML', extension: '.html' }],
            ['css', { name: 'CSS', extension: '.css' }],
            ['javascript', { name: 'JavaScript', extension: '.js' }],
            ['python', { name: 'Python', extension: '.py' }],
            ['sql', { name: 'SQL', extension: '.sql' }],
            ['json', { name: 'JSON', extension: '.json' }],
            ['yaml', { name: 'YAML', extension: '.yml' }]
        ]);

        // 设置主题
        this.themes = new Map([
            ['light', 'github'],
            ['dark', 'monokai'],
            ['high_contrast', 'terminal']
        ]);

        // 设置编辑器统计
        this.editorStatistics = {
            totalEditors: 0,
            activeEditors: 0,
            totalChanges: 0,
            averageEditTime: 0
        };

        this.initializeEditorSystem();
    }

    // 初始化编辑器系统
    initializeEditorSystem() {
        // 创建增强的Ace字段
        this.createEnhancedAceField();

        // 设置语法检查
        this.setupSyntaxValidation();

        // 设置自动完成
        this.setupAutoCompletion();

        // 设置代码片段
        this.setupCodeSnippets();
    }

    // 创建增强的Ace字段
    createEnhancedAceField() {
        const originalField = AceField;

        this.EnhancedAceField = class extends originalField {
            setup() {
                super.setup();

                // 添加增强功能
                this.addEnhancedFeatures();

                // 添加验证功能
                this.addValidationFeatures();

                // 添加自动保存
                this.addAutoSaveFeatures();
            }

            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    ...this.state,
                    syntaxErrors: [],
                    autoSaveTimer: null,
                    lastSaveTime: null,
                    editHistory: [],
                    currentTheme: this.getPreferredTheme()
                };

                // 添加增强方法
                this.addEnhancedMethods();
            }

            addEnhancedMethods() {
                // 增强的变更处理
                this.enhancedHandleChange = (editedValue) => {
                    const startTime = performance.now();

                    try {
                        // 执行原始变更处理
                        this.handleChange(editedValue);

                        // 语法验证
                        this.validateSyntax(editedValue);

                        // 记录编辑历史
                        this.recordEditHistory(editedValue);

                        // 触发自动保存
                        this.triggerAutoSave();

                        // 记录统计
                        this.editorStatistics.totalChanges++;

                        // 记录性能
                        const endTime = performance.now();
                        this.recordEditTime(endTime - startTime);

                    } catch (error) {
                        this.handleEditError(error);
                    }
                };

                // 语法验证
                this.validateSyntax = (content) => {
                    const mode = this.mode;
                    const errors = [];

                    try {
                        switch (mode) {
                            case 'qweb':
                            case 'xml':
                                errors.push(...this.validateXML(content));
                                break;
                            case 'html':
                                errors.push(...this.validateHTML(content));
                                break;
                            case 'css':
                                errors.push(...this.validateCSS(content));
                                break;
                            case 'javascript':
                                errors.push(...this.validateJavaScript(content));
                                break;
                            case 'json':
                                errors.push(...this.validateJSON(content));
                                break;
                        }
                    } catch (error) {
                        errors.push({
                            line: 1,
                            column: 1,
                            message: `Validation error: ${error.message}`,
                            type: 'error'
                        });
                    }

                    this.enhancedState.syntaxErrors = errors;
                    this.updateErrorDisplay();
                };

                // XML验证
                this.validateXML = (content) => {
                    const errors = [];

                    try {
                        const parser = new DOMParser();
                        const doc = parser.parseFromString(content, 'text/xml');
                        const parseError = doc.querySelector('parsererror');

                        if (parseError) {
                            errors.push({
                                line: 1,
                                column: 1,
                                message: parseError.textContent,
                                type: 'error'
                            });
                        }
                    } catch (error) {
                        errors.push({
                            line: 1,
                            column: 1,
                            message: error.message,
                            type: 'error'
                        });
                    }

                    return errors;
                };

                // JSON验证
                this.validateJSON = (content) => {
                    const errors = [];

                    try {
                        JSON.parse(content);
                    } catch (error) {
                        const match = error.message.match(/position (\d+)/);
                        const position = match ? parseInt(match[1]) : 0;
                        const lines = content.substring(0, position).split('\n');

                        errors.push({
                            line: lines.length,
                            column: lines[lines.length - 1].length + 1,
                            message: error.message,
                            type: 'error'
                        });
                    }

                    return errors;
                };

                // HTML验证
                this.validateHTML = (content) => {
                    // 实现HTML验证逻辑
                    return [];
                };

                // CSS验证
                this.validateCSS = (content) => {
                    // 实现CSS验证逻辑
                    return [];
                };

                // JavaScript验证
                this.validateJavaScript = (content) => {
                    // 实现JavaScript验证逻辑
                    return [];
                };

                // 更新错误显示
                this.updateErrorDisplay = () => {
                    // 实现错误显示更新逻辑
                    console.log('Syntax errors:', this.enhancedState.syntaxErrors);
                };

                // 记录编辑历史
                this.recordEditHistory = (content) => {
                    const historyEntry = {
                        content: content,
                        timestamp: Date.now(),
                        length: content.length
                    };

                    this.enhancedState.editHistory.push(historyEntry);

                    // 限制历史记录数量
                    if (this.enhancedState.editHistory.length > 50) {
                        this.enhancedState.editHistory.shift();
                    }
                };

                // 触发自动保存
                this.triggerAutoSave = () => {
                    if (this.editorConfig.enableAutoSave) {
                        clearTimeout(this.enhancedState.autoSaveTimer);
                        this.enhancedState.autoSaveTimer = setTimeout(() => {
                            this.autoSave();
                        }, 5000); // 5秒后自动保存
                    }
                };

                // 自动保存
                this.autoSave = async () => {
                    try {
                        await this.commitChanges();
                        this.enhancedState.lastSaveTime = Date.now();
                        console.log('Auto-saved at', new Date().toLocaleTimeString());
                    } catch (error) {
                        console.error('Auto-save failed:', error);
                    }
                };

                // 获取首选主题
                this.getPreferredTheme = () => {
                    const colorScheme = cookie.get("color_scheme") || 'light';
                    return this.themes.get(colorScheme) || 'github';
                };

                // 切换主题
                this.switchTheme = (themeName) => {
                    this.enhancedState.currentTheme = themeName;
                    // 触发编辑器主题更新
                    this.updateEditorTheme();
                };

                // 更新编辑器主题
                this.updateEditorTheme = () => {
                    // 实现编辑器主题更新逻辑
                    console.log('Switching to theme:', this.enhancedState.currentTheme);
                };

                // 格式化代码
                this.formatCode = () => {
                    const mode = this.mode;
                    let formattedContent = this.editedValue;

                    try {
                        switch (mode) {
                            case 'json':
                                const parsed = JSON.parse(formattedContent);
                                formattedContent = JSON.stringify(parsed, null, 2);
                                break;
                            case 'xml':
                            case 'qweb':
                            case 'html':
                                formattedContent = this.formatXML(formattedContent);
                                break;
                        }

                        this.enhancedHandleChange(formattedContent);
                    } catch (error) {
                        console.error('Format error:', error);
                    }
                };

                // 格式化XML
                this.formatXML = (content) => {
                    // 简单的XML格式化实现
                    return content.replace(/></g, '>\n<');
                };

                // 插入代码片段
                this.insertSnippet = (snippetName) => {
                    const snippets = this.getSnippetsForMode(this.mode);
                    const snippet = snippets[snippetName];

                    if (snippet) {
                        const currentContent = this.editedValue || '';
                        const newContent = currentContent + snippet.content;
                        this.enhancedHandleChange(newContent);
                    }
                };

                // 获取模式对应的代码片段
                this.getSnippetsForMode = (mode) => {
                    const snippets = {
                        qweb: {
                            't-if': { content: 't-if=""', description: 'Conditional rendering' },
                            't-foreach': { content: 't-foreach="" t-as=""', description: 'Loop rendering' },
                            't-field': { content: 't-field=""', description: 'Field rendering' }
                        },
                        html: {
                            div: { content: '<div></div>', description: 'Div element' },
                            span: { content: '<span></span>', description: 'Span element' }
                        },
                        css: {
                            class: { content: '.class-name {\n    \n}', description: 'CSS class' }
                        }
                    };

                    return snippets[mode] || {};
                };

                // 处理编辑错误
                this.handleEditError = (error) => {
                    console.error('Edit error:', error);
                };

                // 记录编辑时间
                this.recordEditTime = (duration) => {
                    this.editorStatistics.averageEditTime =
                        (this.editorStatistics.averageEditTime + duration) / 2;
                };
            }

            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: true,
                    rules: new Map(),
                    validate: (content, mode) => this.validateSyntax(content)
                };
            }

            addAutoSaveFeatures() {
                // 自动保存功能
                this.autoSaveManager = {
                    enabled: this.editorConfig.enableAutoSave,
                    interval: 5000,
                    trigger: () => this.triggerAutoSave()
                };
            }

            // 重写原始方法
            handleChange(editedValue) {
                return this.enhancedHandleChange(editedValue);
            }

            // 组件销毁时清理
            onWillDestroy() {
                if (this.enhancedState.autoSaveTimer) {
                    clearTimeout(this.enhancedState.autoSaveTimer);
                }
            }
        };
    }
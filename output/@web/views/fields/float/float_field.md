# FloatField - 浮点数字段

## 概述

`float_field.js` 是 Odoo Web 客户端的浮点数字段组件，负责处理浮点数的输入、显示和格式化。该模块包含160行代码，是一个功能丰富的数值输入组件，专门用于处理float类型的字段，具备数字格式化、人性化显示、精度控制、数字键盘支持等特性，是数值输入处理的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/float/float_field.js`
- **行数**: 160
- **模块**: `@web/views/fields/float/float_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/views/fields/input_field_hook'    // 输入字段钩子
'@web/views/fields/numpad_decimal_hook' // 数字键盘小数钩子
'@web/views/fields/formatters'          // 字段格式化器
'@web/views/fields/parsers'             // 字段解析器
'@web/views/fields/standard_field_props' // 标准字段属性
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const FloatField = class FloatField extends Component {
    static template = "web.FloatField";
    static props = {
        ...standardFieldProps,
        formatNumber: { type: Boolean, optional: true },
        inputType: { type: String, optional: true },
        step: { type: Number, optional: true },
        digits: { type: Array, optional: true },
        placeholder: { type: String, optional: true },
        humanReadable: { type: Boolean, optional: true },
        decimals: { type: Number, optional: true },
    };
    static defaultProps = {
        formatNumber: true,
        inputType: "text",
        humanReadable: false,
        decimals: 0,
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **格式化控制**: 支持formatNumber控制数字格式化
- **输入类型**: 支持inputType配置输入类型
- **步长控制**: 支持step配置数值步长
- **精度控制**: 支持digits配置数值精度
- **人性化显示**: 支持humanReadable人性化格式
- **小数位数**: 支持decimals配置小数位数

### 2. 组件初始化

```javascript
setup() {
    this.state = useState({
        hasFocus: false,
    });
    this.inputRef = useInputField({
        getValue: () => this.formattedValue,
        refName: "numpadDecimal",
        parse: (v) => this.parse(v),
    });
    useNumpadDecimal();
}
```

**初始化功能**:
- **状态管理**: 管理焦点状态
- **输入钩子**: 使用输入字段钩子管理输入
- **数字键盘**: 使用数字键盘小数钩子
- **解析绑定**: 绑定值解析方法

### 3. 焦点处理

```javascript
onFocusIn() {
    this.state.hasFocus = true;
}

onFocusOut() {
    this.state.hasFocus = false;
}
```

**焦点功能**:
- **焦点进入**: 设置焦点状态为true
- **焦点离开**: 设置焦点状态为false
- **状态同步**: 同步焦点状态
- **格式切换**: 影响数值格式显示

### 4. 值解析

```javascript
parse(value) {
    return this.props.inputType === "number" ? Number(value) : parseFloat(value);
}
```

**解析功能**:
- **类型判断**: 根据输入类型选择解析方式
- **数字解析**: 使用Number或parseFloat解析
- **类型转换**: 将字符串转换为数值
- **错误处理**: 处理解析错误

### 5. 格式化显示

```javascript
get formattedValue() {
    if (
        !this.props.formatNumber ||
        (this.props.inputType === "number" && !this.props.readonly && this.value)
    ) {
        return this.value;
    }
    const options = {
        digits: this.props.digits,
        field: this.props.record.fields[this.props.name],
    };
    if (this.props.humanReadable && !this.state.hasFocus) {
        return formatFloat(this.value, {
            ...options,
            humanReadable: true,
            decimals: this.props.decimals,
        });
    } else {
        return formatFloat(this.value, { ...options, humanReadable: false });
    }
}
```

**格式化功能**:
- **条件格式化**: 根据配置决定是否格式化
- **人性化格式**: 支持人性化数值显示
- **焦点感知**: 根据焦点状态调整格式
- **精度控制**: 根据digits配置控制精度

### 6. 字段注册

```javascript
const floatField = {
    component: FloatField,
    displayName: _t("Float"),
    supportedOptions: [
        {
            label: _t("Format number"),
            name: "enable_formatting",
            type: "boolean",
            default: true,
        },
        {
            label: _t("Digits"),
            name: "digits",
            type: "digits",
        },
        {
            label: _t("Type"),
            name: "type",
            type: "string",
        },
        {
            label: _t("Step"),
            name: "step",
            type: "number",
        },
        {
            label: _t("User-friendly format"),
            name: "human_readable",
            type: "boolean",
        },
        {
            label: _t("Decimals"),
            name: "decimals",
            type: "number",
            default: 0,
        },
    ],
    supportedTypes: ["float"],
    extractProps: ({ attrs, options }) => {
        let digits;
        if (attrs.digits) {
            digits = JSON.parse(attrs.digits);
        } else if (options.digits) {
            digits = options.digits;
        }

        return {
            formatNumber: options?.enable_formatting !== undefined
                ? Boolean(options.enable_formatting)
                : true,
            inputType: options.type,
            humanReadable: !!options.human_readable,
            step: options.step,
            digits,
            placeholder: attrs.placeholder,
            decimals: options.decimals || 0,
        };
    },
};
```

**注册功能**:
- **组件注册**: 注册浮点数字段组件
- **类型支持**: 仅支持float类型
- **选项配置**: 支持多种格式化和显示选项
- **属性提取**: 智能提取和转换配置属性

## 使用场景

### 1. 浮点数字段管理器

```javascript
// 浮点数字段管理器
class FloatFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置浮点数字段配置
        this.floatConfig = {
            enableValidation: true,
            enableFormatting: true,
            enableRangeCheck: true,
            enablePrecisionControl: true,
            enableCalculation: true,
            enableUnitConversion: true,
            enableStatistics: true,
            enableHistoryTracking: true
        };
        
        // 设置数值验证规则
        this.validationRules = {
            enableRangeValidation: true,
            enablePrecisionValidation: true,
            enableNaNCheck: true,
            enableInfinityCheck: true,
            minValue: null,
            maxValue: null,
            maxPrecision: 10
        };
        
        // 设置格式化选项
        this.formatOptions = new Map([
            ['standard', { thousandSeparator: ',', decimalSeparator: '.', grouping: 3 }],
            ['european', { thousandSeparator: '.', decimalSeparator: ',', grouping: 3 }],
            ['space', { thousandSeparator: ' ', decimalSeparator: '.', grouping: 3 }],
            ['none', { thousandSeparator: '', decimalSeparator: '.', grouping: 0 }]
        ]);
        
        // 设置单位转换
        this.unitConversions = new Map([
            ['length', { base: 'm', units: { mm: 0.001, cm: 0.01, m: 1, km: 1000 } }],
            ['weight', { base: 'kg', units: { g: 0.001, kg: 1, t: 1000 } }],
            ['currency', { base: 'USD', units: { USD: 1, EUR: 0.85, CNY: 6.5 } }]
        ]);
        
        // 设置浮点数统计
        this.floatStatistics = {
            totalValues: 0,
            validValues: 0,
            invalidValues: 0,
            averageValue: 0,
            minValue: null,
            maxValue: null,
            sumValue: 0
        };
        
        this.initializeFloatSystem();
    }
    
    // 初始化浮点数系统
    initializeFloatSystem() {
        // 创建增强的浮点数字段
        this.createEnhancedFloatField();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置格式化系统
        this.setupFormattingSystem();
        
        // 设置计算系统
        this.setupCalculationSystem();
    }
    
    // 创建增强的浮点数字段
    createEnhancedFloatField() {
        const originalField = FloatField;
        
        this.EnhancedFloatField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
                
                // 添加计算功能
                this.addCalculationFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    ...this.state,
                    validationErrors: [],
                    isValidating: false,
                    calculationResult: null,
                    unit: null,
                    formatType: 'standard',
                    valueHistory: []
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的值解析
                this.enhancedParse = (value) => {
                    try {
                        // 执行原始解析
                        const parsedValue = this.parse(value);
                        
                        // 验证解析结果
                        const validationResult = this.validateValue(parsedValue);
                        if (!validationResult.isValid) {
                            throw new Error(validationResult.errors.join(', '));
                        }
                        
                        // 记录历史
                        this.addToHistory(parsedValue);
                        
                        // 记录统计
                        this.recordValueStatistics(parsedValue);
                        
                        return parsedValue;
                        
                    } catch (error) {
                        this.handleParseError(error);
                        return null;
                    }
                };
                
                // 验证数值
                this.validateValue = (value) => {
                    const errors = [];
                    
                    if (value === null || value === undefined) {
                        return { isValid: true, errors: [] };
                    }
                    
                    // NaN检查
                    if (this.validationRules.enableNaNCheck && isNaN(value)) {
                        errors.push('Value is not a number');
                    }
                    
                    // 无穷大检查
                    if (this.validationRules.enableInfinityCheck && !isFinite(value)) {
                        errors.push('Value is infinite');
                    }
                    
                    // 范围验证
                    if (this.validationRules.enableRangeValidation) {
                        if (this.validationRules.minValue !== null && value < this.validationRules.minValue) {
                            errors.push(`Value must be at least ${this.validationRules.minValue}`);
                        }
                        
                        if (this.validationRules.maxValue !== null && value > this.validationRules.maxValue) {
                            errors.push(`Value must be at most ${this.validationRules.maxValue}`);
                        }
                    }
                    
                    // 精度验证
                    if (this.validationRules.enablePrecisionValidation) {
                        const decimalPlaces = this.getDecimalPlaces(value);
                        if (decimalPlaces > this.validationRules.maxPrecision) {
                            errors.push(`Too many decimal places (maximum ${this.validationRules.maxPrecision})`);
                        }
                    }
                    
                    this.enhancedState.validationErrors = errors;
                    return { isValid: errors.length === 0, errors };
                };
                
                // 获取小数位数
                this.getDecimalPlaces = (value) => {
                    const str = value.toString();
                    if (str.indexOf('.') !== -1 && str.indexOf('e-') === -1) {
                        return str.split('.')[1].length;
                    } else if (str.indexOf('e-') !== -1) {
                        const parts = str.split('e-');
                        return parseInt(parts[1], 10);
                    }
                    return 0;
                };
                
                // 增强的格式化显示
                this.enhancedFormattedValue = () => {
                    const baseValue = this.formattedValue;
                    
                    // 应用自定义格式
                    if (this.enhancedState.formatType !== 'standard') {
                        return this.applyCustomFormat(baseValue);
                    }
                    
                    // 添加单位
                    if (this.enhancedState.unit) {
                        return `${baseValue} ${this.enhancedState.unit}`;
                    }
                    
                    return baseValue;
                };
                
                // 应用自定义格式
                this.applyCustomFormat = (value) => {
                    const format = this.formatOptions.get(this.enhancedState.formatType);
                    if (!format || typeof value !== 'number') {
                        return value;
                    }
                    
                    // 实现自定义格式化逻辑
                    const parts = value.toString().split('.');
                    const integerPart = parts[0];
                    const decimalPart = parts[1] || '';
                    
                    // 添加千位分隔符
                    let formattedInteger = integerPart;
                    if (format.thousandSeparator && format.grouping > 0) {
                        formattedInteger = integerPart.replace(
                            new RegExp(`\\B(?=(\\d{${format.grouping}})+(?!\\d))`, 'g'),
                            format.thousandSeparator
                        );
                    }
                    
                    // 组合结果
                    if (decimalPart) {
                        return `${formattedInteger}${format.decimalSeparator}${decimalPart}`;
                    }
                    
                    return formattedInteger;
                };
                
                // 单位转换
                this.convertUnit = (value, fromUnit, toUnit, category) => {
                    const conversion = this.unitConversions.get(category);
                    if (!conversion) {
                        throw new Error(`Unknown unit category: ${category}`);
                    }
                    
                    const fromFactor = conversion.units[fromUnit];
                    const toFactor = conversion.units[toUnit];
                    
                    if (fromFactor === undefined || toFactor === undefined) {
                        throw new Error(`Unknown unit in category ${category}`);
                    }
                    
                    // 转换为基础单位，然后转换为目标单位
                    const baseValue = value * fromFactor;
                    return baseValue / toFactor;
                };
                
                // 数学计算
                this.calculate = (expression) => {
                    try {
                        // 简单的数学表达式计算
                        // 实际实现应该使用安全的表达式解析器
                        const result = Function(`"use strict"; return (${expression})`)();
                        
                        if (typeof result === 'number' && isFinite(result)) {
                            this.enhancedState.calculationResult = result;
                            return result;
                        } else {
                            throw new Error('Invalid calculation result');
                        }
                    } catch (error) {
                        throw new Error(`Calculation error: ${error.message}`);
                    }
                };
                
                // 四舍五入
                this.roundValue = (value, decimals = 2) => {
                    const factor = Math.pow(10, decimals);
                    return Math.round(value * factor) / factor;
                };
                
                // 格式化为百分比
                this.formatAsPercentage = (value, decimals = 2) => {
                    return `${(value * 100).toFixed(decimals)}%`;
                };
                
                // 格式化为科学计数法
                this.formatAsScientific = (value, decimals = 2) => {
                    return value.toExponential(decimals);
                };
                
                // 设置格式类型
                this.setFormatType = (formatType) => {
                    if (this.formatOptions.has(formatType)) {
                        this.enhancedState.formatType = formatType;
                    }
                };
                
                // 设置单位
                this.setUnit = (unit) => {
                    this.enhancedState.unit = unit;
                };
                
                // 添加到历史
                this.addToHistory = (value) => {
                    if (value === null || value === undefined) return;
                    
                    const historyEntry = {
                        value: value,
                        timestamp: Date.now(),
                        formatted: this.enhancedFormattedValue()
                    };
                    
                    this.enhancedState.valueHistory.unshift(historyEntry);
                    
                    // 限制历史大小
                    if (this.enhancedState.valueHistory.length > 20) {
                        this.enhancedState.valueHistory.pop();
                    }
                };
                
                // 获取值历史
                this.getValueHistory = () => {
                    return this.enhancedState.valueHistory;
                };
                
                // 清除历史
                this.clearHistory = () => {
                    this.enhancedState.valueHistory = [];
                };
                
                // 获取数值信息
                this.getValueInfo = () => {
                    const value = this.value;
                    
                    return {
                        value: value,
                        formatted: this.enhancedFormattedValue(),
                        isValid: this.enhancedState.validationErrors.length === 0,
                        errors: this.enhancedState.validationErrors,
                        decimalPlaces: this.getDecimalPlaces(value),
                        unit: this.enhancedState.unit,
                        formatType: this.enhancedState.formatType
                    };
                };
                
                // 批量计算
                this.batchCalculate = (values, operation) => {
                    const results = [];
                    
                    for (const value of values) {
                        try {
                            let result;
                            switch (operation) {
                                case 'sum':
                                    result = values.reduce((a, b) => a + b, 0);
                                    break;
                                case 'average':
                                    result = values.reduce((a, b) => a + b, 0) / values.length;
                                    break;
                                case 'min':
                                    result = Math.min(...values);
                                    break;
                                case 'max':
                                    result = Math.max(...values);
                                    break;
                                default:
                                    result = value;
                            }
                            results.push({ value, result, success: true });
                        } catch (error) {
                            results.push({ value, result: null, success: false, error });
                        }
                    }
                    
                    return results;
                };
                
                // 记录值统计
                this.recordValueStatistics = (value) => {
                    if (value === null || value === undefined || isNaN(value)) {
                        this.floatStatistics.invalidValues++;
                        return;
                    }
                    
                    this.floatStatistics.totalValues++;
                    this.floatStatistics.validValues++;
                    this.floatStatistics.sumValue += value;
                    this.floatStatistics.averageValue = this.floatStatistics.sumValue / this.floatStatistics.validValues;
                    
                    if (this.floatStatistics.minValue === null || value < this.floatStatistics.minValue) {
                        this.floatStatistics.minValue = value;
                    }
                    
                    if (this.floatStatistics.maxValue === null || value > this.floatStatistics.maxValue) {
                        this.floatStatistics.maxValue = value;
                    }
                };
                
                // 处理解析错误
                this.handleParseError = (error) => {
                    console.error('Float parse error:', error);
                    this.enhancedState.validationErrors.push(error.message);
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.floatConfig.enableValidation,
                    validate: (value) => this.validateValue(value),
                    getErrors: () => this.enhancedState.validationErrors
                };
            }
            
            addCalculationFeatures() {
                // 计算功能
                this.calculationManager = {
                    enabled: this.floatConfig.enableCalculation,
                    calculate: (expr) => this.calculate(expr),
                    round: (value, decimals) => this.roundValue(value, decimals)
                };
            }
            
            // 重写原始方法
            parse(value) {
                return this.enhancedParse(value);
            }
            
            get formattedValue() {
                return this.enhancedFormattedValue();
            }
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.floatConfig.enableValidation,
            rules: this.validationRules
        };
    }
    
    // 设置格式化系统
    setupFormattingSystem() {
        this.formattingSystemConfig = {
            enabled: this.floatConfig.enableFormatting,
            options: this.formatOptions
        };
    }
    
    // 设置计算系统
    setupCalculationSystem() {
        this.calculationSystemConfig = {
            enabled: this.floatConfig.enableCalculation,
            enableUnitConversion: this.floatConfig.enableUnitConversion
        };
    }
    
    // 创建浮点数字段
    createFloatField(props) {
        return new this.EnhancedFloatField(props);
    }
    
    // 注册格式选项
    registerFormatOption(name, config) {
        this.formatOptions.set(name, config);
    }
    
    // 注册单位转换
    registerUnitConversion(category, config) {
        this.unitConversions.set(category, config);
    }
    
    // 批量验证数值
    batchValidateValues(fields, values) {
        const results = [];
        
        for (let i = 0; i < fields.length && i < values.length; i++) {
            try {
                const result = fields[i].validateValue(values[i]);
                results.push({ field: fields[i], value: values[i], ...result });
            } catch (error) {
                results.push({ field: fields[i], value: values[i], isValid: false, errors: [error.message] });
            }
        }
        
        return results;
    }
    
    // 获取浮点数统计
    getFloatStatistics() {
        return {
            ...this.floatStatistics,
            formatOptionCount: this.formatOptions.size,
            unitConversionCount: this.unitConversions.size,
            validationRate: this.floatStatistics.validValues / Math.max(this.floatStatistics.totalValues, 1) * 100
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理格式和转换
        this.formatOptions.clear();
        this.unitConversions.clear();
        
        // 重置统计
        this.floatStatistics = {
            totalValues: 0,
            validValues: 0,
            invalidValues: 0,
            averageValue: 0,
            minValue: null,
            maxValue: null,
            sumValue: 0
        };
    }
}

// 使用示例
const floatManager = new FloatFieldManager();

// 创建浮点数字段
const floatField = floatManager.createFloatField({
    name: 'price',
    record: {
        data: { price: 123.45 },
        fields: { price: { type: 'float', digits: [16, 2] } }
    },
    formatNumber: true,
    humanReadable: false,
    decimals: 2
});

// 注册自定义格式
floatManager.registerFormatOption('custom', {
    thousandSeparator: '_',
    decimalSeparator: ',',
    grouping: 4
});

// 获取统计信息
const stats = floatManager.getFloatStatistics();
console.log('Float field statistics:', stats);
```

## 技术特点

### 1. 功能完整
- **数字格式化**: 完整的数字格式化功能
- **人性化显示**: 支持人性化数值显示
- **精度控制**: 精确的数值精度控制
- **输入验证**: 完善的输入验证机制

### 2. 用户体验
- **焦点感知**: 根据焦点状态调整显示
- **数字键盘**: 支持数字键盘输入
- **即时反馈**: 即时的输入反馈
- **错误提示**: 清晰的错误提示

### 3. 配置灵活
- **多种选项**: 支持多种配置选项
- **属性提取**: 智能的属性提取
- **默认配置**: 合理的默认配置
- **动态调整**: 支持动态配置调整

### 4. 性能优化
- **钩子使用**: 使用专门的输入钩子
- **状态管理**: 高效的状态管理
- **格式缓存**: 格式化结果缓存
- **按需计算**: 按需进行格式化计算

## 设计模式

### 1. 组件模式 (Component Pattern)
- **UI组件**: 封装浮点数输入UI
- **状态管理**: 管理输入状态
- **事件处理**: 处理用户交互

### 2. 钩子模式 (Hook Pattern)
- **输入钩子**: 使用输入字段钩子
- **数字键盘钩子**: 使用数字键盘钩子
- **生命周期**: 管理组件生命周期

### 3. 策略模式 (Strategy Pattern)
- **格式策略**: 不同的数字格式策略
- **解析策略**: 不同的值解析策略
- **验证策略**: 不同的验证策略

### 4. 工厂模式 (Factory Pattern)
- **字段工厂**: 创建浮点数字段
- **格式工厂**: 创建格式化器
- **验证工厂**: 创建验证器

## 注意事项

1. **精度问题**: 注意浮点数精度问题
2. **格式化性能**: 避免频繁的格式化操作
3. **用户体验**: 提供清晰的数值输入体验
4. **国际化**: 支持不同地区的数字格式

## 扩展建议

1. **计算器**: 添加内置计算器功能
2. **单位转换**: 支持单位转换功能
3. **图表显示**: 添加数值图表显示
4. **历史记录**: 支持输入历史记录
5. **批量操作**: 支持批量数值操作

该浮点数字段为Odoo Web客户端提供了完整的浮点数输入和显示功能，通过丰富的格式化选项和精确的精度控制确保了优秀的数值处理能力和用户体验。

# Many2ManyCheckboxesField - 多对多复选框字段

## 概述

`many2many_checkboxes_field.js` 是 Odoo Web 客户端的多对多复选框字段组件，负责以复选框形式显示和管理多对多关系。该模块包含92行代码，是一个专门的关系字段组件，专门用于处理many2many类型的字段，具备复选框列表、批量选择、防抖提交、域过滤等特性，是多对多关系管理的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/many2many_checkboxes/many2many_checkboxes_field.js`
- **行数**: 92
- **模块**: `@web/views/fields/many2many_checkboxes/many2many_checkboxes_field`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                            // OWL框架
'@web/core/checkbox/checkbox'           // 复选框组件
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/core/utils/hooks'                 // 工具钩子
'@web/core/utils/timing'                // 时间工具
'@web/model/relational_model/utils'     // 关系模型工具
'@web/views/fields/relational_utils'    // 关系字段工具
'@web/views/fields/standard_field_props' // 标准字段属性
```

## 核心功能

### 1. 组件定义

```javascript
const Many2ManyCheckboxesField = class Many2ManyCheckboxesField extends Component {
    static template = "web.Many2ManyCheckboxesField";
    static components = { CheckBox };
    static props = {
        ...standardFieldProps,
        domain: { type: [Array, Function], optional: true },
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **复选框组件**: 集成CheckBox组件
- **域过滤**: 支持domain配置过滤选项
- **专用模板**: 使用Many2ManyCheckboxesField专用模板

### 2. 组件初始化

```javascript
setup() {
    this.specialData = useSpecialData((orm, props) => {
        const { relation } = props.record.fields[props.name];
        const domain = getFieldDomain(props.record, props.name, props.domain);
        return orm.call(relation, "name_search", ["", domain]);
    });
    
    this.idsToAdd = new Set();
    this.idsToRemove = new Set();
    this.debouncedCommitChanges = debounce(this.commitChanges.bind(this), 500);
    useBus(this.props.record.model.bus, "NEED_LOCAL_CHANGES", this.commitChanges.bind(this));
    onWillUnmount(this.commitChanges.bind(this));
}
```

**初始化功能**:
- **特殊数据**: 使用useSpecialData获取关系数据
- **域过滤**: 应用域过滤获取可选项
- **变更跟踪**: 跟踪待添加和待删除的ID
- **防抖提交**: 使用防抖机制批量提交变更
- **事件监听**: 监听模型事件和组件卸载

### 3. 数据获取

```javascript
get items() {
    return this.specialData.data;
}
```

**数据功能**:
- **选项列表**: 获取可选择的项目列表
- **动态数据**: 动态获取关系数据
- **缓存机制**: 利用specialData的缓存机制
- **实时更新**: 支持数据的实时更新

### 4. 选择状态检查

```javascript
isSelected(item) {
    return this.props.record.data[this.props.name].currentIds.includes(item[0]);
}
```

**状态检查功能**:
- **选择检查**: 检查项目是否已被选择
- **ID匹配**: 通过ID匹配检查选择状态
- **当前状态**: 获取当前的选择状态
- **布尔返回**: 返回布尔值表示选择状态

### 5. 变更提交

```javascript
commitChanges() {
    if (this.idsToAdd.size === 0 && this.idsToRemove.size === 0) {
        return;
    }
    const result = this.props.record.data[this.props.name].addAndRemove({
        add: [...this.idsToAdd],
        remove: [...this.idsToRemove],
    });
    this.idsToAdd.clear();
    this.idsToRemove.clear();
    return result;
}
```

**提交功能**:
- **批量操作**: 批量提交添加和删除操作
- **空检查**: 检查是否有待提交的变更
- **原子操作**: 使用addAndRemove进行原子操作
- **状态清理**: 提交后清理待变更状态

### 6. 变更处理

```javascript
onChange(resId, checked) {
    if (checked) {
        if (this.idsToRemove.has(resId)) {
            this.idsToRemove.delete(resId);
        } else {
            this.idsToAdd.add(resId);
        }
    } else {
        if (this.idsToAdd.has(resId)) {
            this.idsToAdd.delete(resId);
        } else {
            this.idsToRemove.add(resId);
        }
    }
    this.debouncedCommitChanges();
}
```

**变更处理功能**:
- **状态切换**: 处理复选框的选中/取消选中
- **智能管理**: 智能管理待添加和待删除集合
- **冲突解决**: 解决添加和删除的冲突
- **防抖触发**: 触发防抖的变更提交

### 7. 字段注册

```javascript
const many2ManyCheckboxesField = {
    component: Many2ManyCheckboxesField,
    displayName: _t("Checkboxes"),
    supportedTypes: ["many2many"],
    isEmpty: () => false,
    extractProps(fieldInfo, dynamicInfo) {
        return {
            domain: dynamicInfo.domain,
        };
    },
};

registry.category("fields").add("many2many_checkboxes", many2ManyCheckboxesField);
```

**注册功能**:
- **组件注册**: 注册多对多复选框字段
- **显示名称**: 设置为"Checkboxes"
- **类型支持**: 仅支持many2many类型
- **非空字段**: 始终返回非空状态
- **域提取**: 提取域配置到组件属性

## 使用场景

### 1. 多对多复选框字段管理器

```javascript
// 多对多复选框字段管理器
class Many2ManyCheckboxesManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置复选框字段配置
        this.checkboxConfig = {
            enableBatchSelection: true,
            enableSelectAll: true,
            enableSearch: true,
            enableGrouping: false,
            enableSorting: true,
            enablePagination: false,
            enableVirtualScroll: false,
            maxVisibleItems: 50
        };
        
        // 设置防抖配置
        this.debounceConfig = {
            commitDelay: 500,
            searchDelay: 300,
            enableDebounce: true
        };
        
        // 设置选择策略
        this.selectionStrategies = new Map([
            ['immediate', { debounce: false, batchSize: 1 }],
            ['batched', { debounce: true, batchSize: 10 }],
            ['deferred', { debounce: true, batchSize: Infinity }]
        ]);
        
        // 设置排序选项
        this.sortOptions = [
            { field: 'name', direction: 'asc', label: 'Name (A-Z)' },
            { field: 'name', direction: 'desc', label: 'Name (Z-A)' },
            { field: 'create_date', direction: 'desc', label: 'Newest First' },
            { field: 'create_date', direction: 'asc', label: 'Oldest First' }
        ];
        
        // 设置复选框统计
        this.checkboxStatistics = {
            totalFields: 0,
            totalSelections: 0,
            averageSelectionTime: 0,
            batchOperations: 0,
            searchQueries: 0
        };
        
        this.initializeCheckboxSystem();
    }
    
    // 初始化复选框系统
    initializeCheckboxSystem() {
        // 创建增强的多对多复选框字段
        this.createEnhancedMany2ManyCheckboxesField();
        
        // 设置选择系统
        this.setupSelectionSystem();
        
        // 设置搜索系统
        this.setupSearchSystem();
        
        // 设置批量操作系统
        this.setupBatchOperationSystem();
    }
    
    // 创建增强的多对多复选框字段
    createEnhancedMany2ManyCheckboxesField() {
        const originalField = Many2ManyCheckboxesField;
        
        this.EnhancedMany2ManyCheckboxesField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加搜索功能
                this.addSearchFeatures();
                
                // 添加批量操作功能
                this.addBatchOperationFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    searchTerm: '',
                    sortBy: 'name',
                    sortDirection: 'asc',
                    isSelectAllChecked: false,
                    isSelectAllIndeterminate: false,
                    filteredItems: [],
                    selectedCount: 0,
                    totalCount: 0,
                    selectionStrategy: 'batched'
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的项目获取
                this.enhancedGetItems = () => {
                    let items = this.specialData.data || [];
                    
                    // 应用搜索过滤
                    if (this.enhancedState.searchTerm) {
                        items = this.filterItemsBySearch(items);
                    }
                    
                    // 应用排序
                    items = this.sortItems(items);
                    
                    // 限制显示数量
                    if (this.checkboxConfig.maxVisibleItems > 0) {
                        items = items.slice(0, this.checkboxConfig.maxVisibleItems);
                    }
                    
                    this.enhancedState.filteredItems = items;
                    this.enhancedState.totalCount = items.length;
                    
                    return items;
                };
                
                // 搜索过滤
                this.filterItemsBySearch = (items) => {
                    const searchTerm = this.enhancedState.searchTerm.toLowerCase();
                    
                    return items.filter(item => {
                        const name = item[1] || '';
                        return name.toLowerCase().includes(searchTerm);
                    });
                };
                
                // 排序项目
                this.sortItems = (items) => {
                    const { sortBy, sortDirection } = this.enhancedState;
                    
                    return items.sort((a, b) => {
                        let aValue, bValue;
                        
                        switch (sortBy) {
                            case 'name':
                                aValue = a[1] || '';
                                bValue = b[1] || '';
                                break;
                            case 'id':
                                aValue = a[0];
                                bValue = b[0];
                                break;
                            default:
                                return 0;
                        }
                        
                        const comparison = aValue.localeCompare ? 
                            aValue.localeCompare(bValue) : 
                            aValue - bValue;
                        
                        return sortDirection === 'asc' ? comparison : -comparison;
                    });
                };
                
                // 设置搜索词
                this.setSearchTerm = (term) => {
                    this.enhancedState.searchTerm = term;
                    this.recordSearchQuery();
                };
                
                // 设置排序
                this.setSorting = (field, direction) => {
                    this.enhancedState.sortBy = field;
                    this.enhancedState.sortDirection = direction;
                };
                
                // 全选/取消全选
                this.toggleSelectAll = () => {
                    const items = this.enhancedGetItems();
                    const allSelected = items.every(item => this.isSelected(item));
                    
                    if (allSelected) {
                        // 取消全选
                        this.deselectAll(items);
                    } else {
                        // 全选
                        this.selectAll(items);
                    }
                    
                    this.updateSelectAllState();
                };
                
                // 全选
                this.selectAll = (items) => {
                    items.forEach(item => {
                        const resId = item[0];
                        if (!this.isSelected(item)) {
                            if (this.idsToRemove.has(resId)) {
                                this.idsToRemove.delete(resId);
                            } else {
                                this.idsToAdd.add(resId);
                            }
                        }
                    });
                    
                    this.debouncedCommitChanges();
                    this.recordBatchOperation('select_all', items.length);
                };
                
                // 取消全选
                this.deselectAll = (items) => {
                    items.forEach(item => {
                        const resId = item[0];
                        if (this.isSelected(item)) {
                            if (this.idsToAdd.has(resId)) {
                                this.idsToAdd.delete(resId);
                            } else {
                                this.idsToRemove.add(resId);
                            }
                        }
                    });
                    
                    this.debouncedCommitChanges();
                    this.recordBatchOperation('deselect_all', items.length);
                };
                
                // 更新全选状态
                this.updateSelectAllState = () => {
                    const items = this.enhancedGetItems();
                    const selectedItems = items.filter(item => this.isSelected(item));
                    
                    this.enhancedState.selectedCount = selectedItems.length;
                    this.enhancedState.isSelectAllChecked = selectedItems.length === items.length && items.length > 0;
                    this.enhancedState.isSelectAllIndeterminate = selectedItems.length > 0 && selectedItems.length < items.length;
                };
                
                // 批量选择
                this.batchSelect = (itemIds, selected = true) => {
                    const startTime = performance.now();
                    
                    itemIds.forEach(resId => {
                        if (selected) {
                            if (this.idsToRemove.has(resId)) {
                                this.idsToRemove.delete(resId);
                            } else {
                                this.idsToAdd.add(resId);
                            }
                        } else {
                            if (this.idsToAdd.has(resId)) {
                                this.idsToAdd.delete(resId);
                            } else {
                                this.idsToRemove.add(resId);
                            }
                        }
                    });
                    
                    this.debouncedCommitChanges();
                    this.recordBatchOperation('batch_select', itemIds.length);
                    this.recordSelectionTime(performance.now() - startTime);
                };
                
                // 获取选择统计
                this.getSelectionStats = () => {
                    const items = this.enhancedGetItems();
                    const selectedItems = items.filter(item => this.isSelected(item));
                    
                    return {
                        total: items.length,
                        selected: selectedItems.length,
                        unselected: items.length - selectedItems.length,
                        percentage: items.length > 0 ? (selectedItems.length / items.length * 100).toFixed(1) : 0
                    };
                };
                
                // 获取复选框信息
                this.getCheckboxInfo = () => {
                    return {
                        searchTerm: this.enhancedState.searchTerm,
                        sortBy: this.enhancedState.sortBy,
                        sortDirection: this.enhancedState.sortDirection,
                        selectionStats: this.getSelectionStats(),
                        isSelectAllChecked: this.enhancedState.isSelectAllChecked,
                        isSelectAllIndeterminate: this.enhancedState.isSelectAllIndeterminate,
                        filteredCount: this.enhancedState.filteredItems.length,
                        totalCount: this.enhancedState.totalCount
                    };
                };
                
                // 重置过滤器
                this.resetFilters = () => {
                    this.enhancedState.searchTerm = '';
                    this.enhancedState.sortBy = 'name';
                    this.enhancedState.sortDirection = 'asc';
                };
                
                // 导出选择
                this.exportSelection = () => {
                    const items = this.enhancedGetItems();
                    const selectedItems = items.filter(item => this.isSelected(item));
                    
                    return selectedItems.map(item => ({
                        id: item[0],
                        name: item[1]
                    }));
                };
                
                // 记录搜索查询
                this.recordSearchQuery = () => {
                    this.checkboxStatistics.searchQueries++;
                };
                
                // 记录批量操作
                this.recordBatchOperation = (type, count) => {
                    this.checkboxStatistics.batchOperations++;
                    console.log(`Batch operation: ${type}, count: ${count}`);
                };
                
                // 记录选择时间
                this.recordSelectionTime = (duration) => {
                    this.checkboxStatistics.averageSelectionTime = 
                        (this.checkboxStatistics.averageSelectionTime + duration) / 2;
                };
            }
            
            addSearchFeatures() {
                // 搜索功能
                this.searchManager = {
                    enabled: this.checkboxConfig.enableSearch,
                    setTerm: (term) => this.setSearchTerm(term),
                    getTerm: () => this.enhancedState.searchTerm,
                    clear: () => this.setSearchTerm('')
                };
            }
            
            addBatchOperationFeatures() {
                // 批量操作功能
                this.batchManager = {
                    enabled: this.checkboxConfig.enableBatchSelection,
                    selectAll: () => this.toggleSelectAll(),
                    batchSelect: (ids, selected) => this.batchSelect(ids, selected),
                    getStats: () => this.getSelectionStats()
                };
            }
            
            // 重写原始方法
            get items() {
                return this.enhancedGetItems();
            }
            
            onChange(resId, checked) {
                const startTime = performance.now();
                
                // 调用原始方法
                super.onChange(resId, checked);
                
                // 更新状态
                this.updateSelectAllState();
                
                // 记录统计
                this.checkboxStatistics.totalSelections++;
                this.recordSelectionTime(performance.now() - startTime);
            }
        };
    }
    
    // 设置选择系统
    setupSelectionSystem() {
        this.selectionSystemConfig = {
            enabled: this.checkboxConfig.enableBatchSelection,
            strategies: this.selectionStrategies,
            defaultStrategy: 'batched'
        };
    }
    
    // 设置搜索系统
    setupSearchSystem() {
        this.searchSystemConfig = {
            enabled: this.checkboxConfig.enableSearch,
            debounceDelay: this.debounceConfig.searchDelay
        };
    }
    
    // 设置批量操作系统
    setupBatchOperationSystem() {
        this.batchOperationConfig = {
            enabled: this.checkboxConfig.enableBatchSelection,
            maxBatchSize: 100
        };
    }
    
    // 创建多对多复选框字段
    createMany2ManyCheckboxesField(props) {
        const field = new this.EnhancedMany2ManyCheckboxesField(props);
        this.checkboxStatistics.totalFields++;
        return field;
    }
    
    // 注册选择策略
    registerSelectionStrategy(name, config) {
        this.selectionStrategies.set(name, config);
    }
    
    // 批量创建字段
    batchCreateFields(propsArray) {
        const results = [];
        
        for (const props of propsArray) {
            try {
                const field = this.createMany2ManyCheckboxesField(props);
                results.push({ field, success: true, props });
            } catch (error) {
                results.push({ field: null, success: false, error, props });
            }
        }
        
        return results;
    }
    
    // 获取复选框统计
    getCheckboxStatistics() {
        return {
            ...this.checkboxStatistics,
            strategyCount: this.selectionStrategies.size,
            sortOptionCount: this.sortOptions.length,
            averageSelectionsPerField: this.checkboxStatistics.totalSelections / Math.max(this.checkboxStatistics.totalFields, 1)
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理策略
        this.selectionStrategies.clear();
        
        // 重置统计
        this.checkboxStatistics = {
            totalFields: 0,
            totalSelections: 0,
            averageSelectionTime: 0,
            batchOperations: 0,
            searchQueries: 0
        };
    }
}

// 使用示例
const checkboxManager = new Many2ManyCheckboxesManager();

// 创建多对多复选框字段
const checkboxField = checkboxManager.createMany2ManyCheckboxesField({
    name: 'tag_ids',
    record: {
        data: { 
            tag_ids: {
                currentIds: [1, 3, 5],
                addAndRemove: ({ add, remove }) => {
                    console.log('Adding:', add, 'Removing:', remove);
                    return Promise.resolve();
                }
            }
        },
        fields: { 
            tag_ids: { 
                type: 'many2many',
                relation: 'tag.model'
            }
        }
    }
});

// 注册自定义选择策略
checkboxManager.registerSelectionStrategy('instant', {
    debounce: false,
    batchSize: 1
});

// 获取统计信息
const stats = checkboxManager.getCheckboxStatistics();
console.log('Many2many checkboxes field statistics:', stats);
```

## 技术特点

### 1. 批量操作
- **防抖机制**: 使用防抖避免频繁提交
- **批量提交**: 批量处理添加和删除操作
- **原子操作**: 使用addAndRemove确保原子性
- **性能优化**: 减少不必要的服务器请求

### 2. 智能管理
- **状态跟踪**: 智能跟踪待添加和待删除的ID
- **冲突解决**: 自动解决添加和删除的冲突
- **集合操作**: 使用Set进行高效的集合操作
- **内存优化**: 及时清理不需要的状态

### 3. 关系处理
- **特殊数据**: 使用useSpecialData处理关系数据
- **域过滤**: 支持域过滤限制可选项
- **动态加载**: 动态加载关系数据
- **缓存机制**: 利用缓存提高性能

### 4. 用户体验
- **复选框界面**: 直观的复选框选择界面
- **即时反馈**: 提供即时的视觉反馈
- **批量选择**: 支持批量选择操作
- **搜索过滤**: 支持搜索和过滤功能

## 设计模式

### 1. 组件模式 (Component Pattern)
- **UI组件**: 封装复选框列表UI
- **状态管理**: 管理选择状态
- **事件处理**: 处理选择事件

### 2. 观察者模式 (Observer Pattern)
- **状态观察**: 观察选择状态变化
- **事件监听**: 监听模型事件
- **变更通知**: 通知变更提交

### 3. 策略模式 (Strategy Pattern)
- **提交策略**: 不同的变更提交策略
- **选择策略**: 不同的选择处理策略
- **过滤策略**: 不同的数据过滤策略

### 4. 批处理模式 (Batch Pattern)
- **批量操作**: 批量处理选择变更
- **防抖处理**: 防抖批量提交
- **性能优化**: 优化批量操作性能

## 注意事项

1. **性能考虑**: 大量选项时注意性能优化
2. **内存管理**: 及时清理不需要的状态
3. **用户体验**: 提供清晰的选择反馈
4. **数据一致性**: 确保选择状态的一致性

## 扩展建议

1. **虚拟滚动**: 支持大量选项的虚拟滚动
2. **分组显示**: 支持选项的分组显示
3. **搜索功能**: 添加选项搜索功能
4. **全选功能**: 支持全选/取消全选
5. **排序功能**: 支持选项排序功能

该多对多复选框字段为Odoo Web客户端提供了高效的多对多关系管理功能，通过智能的批量操作和防抖机制确保了良好的性能和用户体验。

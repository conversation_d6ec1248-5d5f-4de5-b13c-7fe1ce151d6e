# StateSelectionField - 状态选择字段

## 概述

`state_selection_field.js` 是 Odoo Web 客户端的状态选择字段组件，负责显示和管理状态类型的选择字段。该模块包含112行代码，是一个功能专门的状态管理组件，专门用于处理看板状态和工作流状态，具备颜色状态、快捷键命令、下拉选择、自动保存等特性，是状态管理和工作流控制的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/state_selection/state_selection_field.js`
- **行数**: 112
- **模块**: `@web/views/fields/state_selection/state_selection_field`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                            // OWL框架
'@web/core/commands/command_hook'       // 命令钩子
'@web/core/dropdown/dropdown'           // 下拉菜单
'@web/core/dropdown/dropdown_item'      // 下拉菜单项
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/views/fields/formatters'          // 字段格式化器
'@web/views/fields/standard_field_props' // 标准字段属性
```

## 核心功能

### 1. 组件定义

```javascript
const StateSelectionField = class StateSelectionField extends Component {
    static template = "web.StateSelectionField";
    static components = {
        Dropdown,
        DropdownItem,
    };
    static props = {
        ...standardFieldProps,
        showLabel: { type: Boolean, optional: true },
        withCommand: { type: Boolean, optional: true },
        autosave: { type: Boolean, optional: true },
    };
    static defaultProps = {
        showLabel: true,
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **显示标签**: 支持showLabel配置是否显示标签
- **命令支持**: 支持withCommand配置快捷键命令
- **自动保存**: 支持autosave配置自动保存
- **默认显示**: 默认显示标签
- **下拉组件**: 集成下拉菜单组件

### 2. 组件初始化

```javascript
setup() {
    this.colorPrefix = "o_status_";
    this.colors = {
        blocked: "red",
        done: "green",
    };
    if (this.props.withCommand) {
        const hotkeys = ["D", "F", "G"];
        for (const [index, [value, label]] of this.options.entries()) {
            useCommand(
                _t("Set kanban state as %s", label),
                () => {
                    this.updateRecord(value);
                },
                {
                    category: "smart_action",
                    hotkey: hotkeys[index] && "alt+" + hotkeys[index],
                    isAvailable: () => this.props.record.data[this.props.name] !== value,
                }
            );
        }
    }
}
```

**初始化功能**:
- **颜色前缀**: 设置状态颜色CSS前缀
- **状态颜色**: 定义blocked为红色，done为绿色
- **快捷键**: 为前三个选项配置Alt+D/F/G快捷键
- **命令注册**: 注册状态切换命令
- **可用性**: 检查命令是否可用

### 3. 状态管理

```javascript
get options() {
    return this.props.record.fields[this.props.name].selection || [];
}

get value() {
    return this.props.record.data[this.props.name];
}

get formattedValue() {
    return formatSelection(this.value, { selection: this.options });
}

get stateColor() {
    const value = this.value;
    if (value in this.colors) {
        return this.colorPrefix + this.colors[value];
    }
    return "";
}
```

**状态管理功能**:
- **选项获取**: 从字段定义获取选择选项
- **值获取**: 获取当前状态值
- **格式化值**: 格式化显示状态值
- **状态颜色**: 根据状态值获取对应颜色

### 4. 记录更新

```javascript
updateRecord(value) {
    this.props.record.update({ [this.props.name]: value });
    if (this.props.autosave) {
        this.props.record.save();
    }
}
```

**更新功能**:
- **记录更新**: 更新记录的状态字段
- **自动保存**: 根据配置自动保存记录
- **状态切换**: 实现状态的快速切换
- **异步操作**: 支持异步保存操作

### 5. 字段注册

```javascript
const stateSelectionField = {
    component: StateSelectionField,
    displayName: _t("State Selection"),
    supportedTypes: ["selection"],
    extractProps: ({ attrs, options }) => ({
        showLabel: attrs.show_label !== "0",
        withCommand: options.with_command,
        autosave: options.autosave,
    }),
};

registry.category("fields").add("state_selection", stateSelectionField);
```

**注册功能**:
- **组件注册**: 注册状态选择字段组件
- **显示名称**: 设置为"State Selection"
- **类型支持**: 支持selection类型
- **属性提取**: 提取显示标签、命令支持、自动保存等属性

## 使用场景

### 1. 状态选择字段管理器

```javascript
// 状态选择字段管理器
class StateSelectionFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置状态选择字段配置
        this.stateSelectionConfig = {
            enableColorCoding: true,
            enableHotkeys: true,
            enableAutoSave: true,
            enableStateValidation: true,
            enableTransitions: true,
            enableStateHistory: false,
            enableNotifications: false,
            enableBulkOperations: false
        };
        
        // 设置状态颜色映射
        this.stateColors = new Map([
            ['draft', 'gray'],
            ['pending', 'yellow'],
            ['in_progress', 'blue'],
            ['blocked', 'red'],
            ['done', 'green'],
            ['cancelled', 'orange'],
            ['archived', 'purple']
        ]);
        
        // 设置快捷键映射
        this.hotkeyMappings = new Map([
            [0, 'D'],
            [1, 'F'],
            [2, 'G'],
            [3, 'H'],
            [4, 'J']
        ]);
        
        // 设置状态转换规则
        this.transitionRules = new Map([
            ['draft', ['pending', 'cancelled']],
            ['pending', ['in_progress', 'cancelled']],
            ['in_progress', ['blocked', 'done', 'cancelled']],
            ['blocked', ['in_progress', 'cancelled']],
            ['done', ['archived']],
            ['cancelled', ['draft']],
            ['archived', []]
        ]);
        
        // 设置验证规则
        this.validationRules = {
            enableTransitionValidation: true,
            enableRequiredFields: false,
            enableCustomValidation: false,
            customValidators: [],
            requiredFieldsByState: new Map()
        };
        
        // 设置状态统计
        this.stateStatistics = {
            totalStateFields: 0,
            totalStateChanges: 0,
            stateChangesByType: new Map(),
            stateDistribution: new Map(),
            averageStateChangesPerRecord: 0,
            mostUsedState: null,
            transitionCount: new Map()
        };
        
        this.initializeStateSelectionSystem();
    }
    
    // 初始化状态选择系统
    initializeStateSelectionSystem() {
        // 创建增强的状态选择字段
        this.createEnhancedStateSelectionField();
        
        // 设置颜色系统
        this.setupColorSystem();
        
        // 设置快捷键系统
        this.setupHotkeySystem();
        
        // 设置转换系统
        this.setupTransitionSystem();
    }
    
    // 创建增强的状态选择字段
    createEnhancedStateSelectionField() {
        const originalField = StateSelectionField;
        
        this.EnhancedStateSelectionField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加转换功能
                this.addTransitionFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    previousValue: null,
                    transitionHistory: [],
                    validationErrors: [],
                    allowedTransitions: [],
                    isTransitioning: false,
                    lastTransitionTime: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的状态颜色
                this.enhancedStateColor = () => {
                    const value = this.value;
                    const colorMapping = this.stateColors.get(value);
                    
                    if (colorMapping) {
                        return `${this.colorPrefix}${colorMapping}`;
                    }
                    
                    // 回退到原始逻辑
                    if (value in this.colors) {
                        return this.colorPrefix + this.colors[value];
                    }
                    
                    return "";
                };
                
                // 增强的记录更新
                this.enhancedUpdateRecord = async (value) => {
                    try {
                        // 验证状态转换
                        await this.validateStateTransition(this.value, value);
                        
                        // 记录转换历史
                        this.recordTransition(this.value, value);
                        
                        // 执行状态转换
                        this.enhancedState.isTransitioning = true;
                        this.enhancedState.previousValue = this.value;
                        
                        // 更新记录
                        this.props.record.update({ [this.props.name]: value });
                        
                        // 自动保存
                        if (this.props.autosave) {
                            await this.props.record.save();
                        }
                        
                        // 更新状态
                        this.enhancedState.lastTransitionTime = new Date();
                        
                        // 记录统计
                        this.recordStateChange(this.enhancedState.previousValue, value);
                        
                        // 发送通知
                        if (this.stateSelectionConfig.enableNotifications) {
                            this.sendStateChangeNotification(this.enhancedState.previousValue, value);
                        }
                        
                    } catch (error) {
                        this.handleTransitionError(error);
                    } finally {
                        this.enhancedState.isTransitioning = false;
                    }
                };
                
                // 验证状态转换
                this.validateStateTransition = async (fromState, toState) => {
                    const errors = [];
                    
                    // 转换规则验证
                    if (this.validationRules.enableTransitionValidation) {
                        const allowedTransitions = this.transitionRules.get(fromState) || [];
                        if (fromState && !allowedTransitions.includes(toState)) {
                            errors.push(`Invalid transition from ${fromState} to ${toState}`);
                        }
                    }
                    
                    // 必填字段验证
                    if (this.validationRules.enableRequiredFields) {
                        const requiredFields = this.validationRules.requiredFieldsByState.get(toState) || [];
                        for (const field of requiredFields) {
                            const value = this.props.record.data[field];
                            if (!value && value !== 0) {
                                errors.push(`Field ${field} is required for state ${toState}`);
                            }
                        }
                    }
                    
                    // 自定义验证
                    if (this.validationRules.enableCustomValidation) {
                        for (const validator of this.validationRules.customValidators) {
                            try {
                                const result = await validator(fromState, toState, this.props.record);
                                if (result !== true) {
                                    errors.push(result || 'Custom validation failed');
                                }
                            } catch (error) {
                                errors.push('Custom validation error');
                            }
                        }
                    }
                    
                    this.enhancedState.validationErrors = errors;
                    
                    if (errors.length > 0) {
                        throw new Error(errors.join(', '));
                    }
                };
                
                // 获取允许的转换
                this.getAllowedTransitions = () => {
                    const currentState = this.value;
                    const allowedTransitions = this.transitionRules.get(currentState) || [];
                    
                    // 过滤选项
                    const filteredOptions = this.options.filter(([value, label]) => {
                        return value === currentState || allowedTransitions.includes(value);
                    });
                    
                    this.enhancedState.allowedTransitions = allowedTransitions;
                    return filteredOptions;
                };
                
                // 记录转换历史
                this.recordTransition = (fromState, toState) => {
                    if (!this.stateSelectionConfig.enableStateHistory) {
                        return;
                    }
                    
                    const transition = {
                        from: fromState,
                        to: toState,
                        timestamp: new Date(),
                        recordId: this.props.record.resId
                    };
                    
                    this.enhancedState.transitionHistory.unshift(transition);
                    
                    // 限制历史大小
                    if (this.enhancedState.transitionHistory.length > 50) {
                        this.enhancedState.transitionHistory.pop();
                    }
                };
                
                // 获取状态信息
                this.getStateInfo = () => {
                    return {
                        currentState: this.value,
                        previousState: this.enhancedState.previousValue,
                        formattedValue: this.formattedValue,
                        stateColor: this.enhancedStateColor(),
                        allowedTransitions: this.enhancedState.allowedTransitions,
                        isTransitioning: this.enhancedState.isTransitioning,
                        lastTransitionTime: this.enhancedState.lastTransitionTime,
                        transitionHistory: this.enhancedState.transitionHistory.slice(0, 10),
                        validationErrors: this.enhancedState.validationErrors,
                        hasValidationErrors: this.enhancedState.validationErrors.length > 0
                    };
                };
                
                // 批量状态更新
                this.batchUpdateStates = async (records, newState) => {
                    if (!this.stateSelectionConfig.enableBulkOperations) {
                        throw new Error('Bulk operations are disabled');
                    }
                    
                    const results = [];
                    
                    for (const record of records) {
                        try {
                            const currentState = record.data[this.props.name];
                            await this.validateStateTransition(currentState, newState);
                            
                            record.update({ [this.props.name]: newState });
                            results.push({ record, success: true });
                            
                        } catch (error) {
                            results.push({ record, success: false, error: error.message });
                        }
                    }
                    
                    return results;
                };
                
                // 发送状态变更通知
                this.sendStateChangeNotification = (fromState, toState) => {
                    // 实现通知逻辑
                    console.log(`State changed from ${fromState} to ${toState}`);
                };
                
                // 处理转换错误
                this.handleTransitionError = (error) => {
                    console.error('State transition error:', error);
                    
                    if (this.stateSelectionConfig.enableNotifications) {
                        this.sendErrorNotification(error.message);
                    }
                };
                
                // 发送错误通知
                this.sendErrorNotification = (message) => {
                    console.warn('State transition error:', message);
                };
                
                // 记录状态变更统计
                this.recordStateChange = (fromState, toState) => {
                    this.stateStatistics.totalStateChanges++;
                    
                    // 记录转换统计
                    const transitionKey = `${fromState}->${toState}`;
                    const count = this.stateStatistics.transitionCount.get(transitionKey) || 0;
                    this.stateStatistics.transitionCount.set(transitionKey, count + 1);
                    
                    // 记录状态分布
                    const stateCount = this.stateStatistics.stateDistribution.get(toState) || 0;
                    this.stateStatistics.stateDistribution.set(toState, stateCount + 1);
                    
                    // 更新平均值
                    this.updateAverageStateChanges();
                };
                
                // 更新平均状态变更数
                this.updateAverageStateChanges = () => {
                    if (this.stateStatistics.totalStateFields > 0) {
                        this.stateStatistics.averageStateChangesPerRecord = 
                            this.stateStatistics.totalStateChanges / this.stateStatistics.totalStateFields;
                    }
                };
            }
            
            addTransitionFeatures() {
                // 转换功能
                this.transitionManager = {
                    enabled: this.stateSelectionConfig.enableTransitions,
                    validate: (from, to) => this.validateStateTransition(from, to),
                    getAllowed: () => this.getAllowedTransitions(),
                    execute: (value) => this.enhancedUpdateRecord(value)
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.stateSelectionConfig.enableStateValidation,
                    validate: (from, to) => this.validateStateTransition(from, to),
                    getErrors: () => this.enhancedState.validationErrors,
                    hasErrors: () => this.enhancedState.validationErrors.length > 0
                };
            }
            
            // 重写原始方法
            get stateColor() {
                return this.enhancedStateColor();
            }
            
            get options() {
                if (this.stateSelectionConfig.enableTransitions) {
                    return this.getAllowedTransitions();
                }
                return super.options;
            }
            
            updateRecord(value) {
                return this.enhancedUpdateRecord(value);
            }
        };
    }
    
    // 设置颜色系统
    setupColorSystem() {
        this.colorSystemConfig = {
            enabled: this.stateSelectionConfig.enableColorCoding,
            colors: this.stateColors,
            prefix: 'o_status_'
        };
    }
    
    // 设置快捷键系统
    setupHotkeySystem() {
        this.hotkeySystemConfig = {
            enabled: this.stateSelectionConfig.enableHotkeys,
            mappings: this.hotkeyMappings,
            modifier: 'alt'
        };
    }
    
    // 设置转换系统
    setupTransitionSystem() {
        this.transitionSystemConfig = {
            enabled: this.stateSelectionConfig.enableTransitions,
            rules: this.transitionRules,
            validation: this.validationRules
        };
    }
    
    // 创建状态选择字段
    createStateSelectionField(props) {
        const field = new this.EnhancedStateSelectionField(props);
        this.stateStatistics.totalStateFields++;
        return field;
    }
    
    // 注册状态颜色
    registerStateColor(state, color) {
        this.stateColors.set(state, color);
    }
    
    // 注册转换规则
    registerTransitionRule(fromState, allowedStates) {
        this.transitionRules.set(fromState, allowedStates);
    }
    
    // 获取最受欢迎的状态
    getMostUsedState() {
        let maxCount = 0;
        let mostUsedState = null;
        
        for (const [state, count] of this.stateStatistics.stateDistribution.entries()) {
            if (count > maxCount) {
                maxCount = count;
                mostUsedState = state;
            }
        }
        
        this.stateStatistics.mostUsedState = mostUsedState;
        return mostUsedState;
    }
    
    // 获取状态统计
    getStateStatistics() {
        return {
            ...this.stateStatistics,
            mostUsedState: this.getMostUsedState(),
            stateVariety: this.stateStatistics.stateDistribution.size,
            transitionVariety: this.stateStatistics.transitionCount.size,
            changeRate: this.stateStatistics.totalStateChanges / Math.max(this.stateStatistics.totalStateFields, 1),
            colorCount: this.stateColors.size,
            transitionRuleCount: this.transitionRules.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理状态颜色
        this.stateColors.clear();
        
        // 清理快捷键映射
        this.hotkeyMappings.clear();
        
        // 清理转换规则
        this.transitionRules.clear();
        
        // 清理统计
        this.stateStatistics.stateChangesByType.clear();
        this.stateStatistics.stateDistribution.clear();
        this.stateStatistics.transitionCount.clear();
        
        // 重置统计
        this.stateStatistics = {
            totalStateFields: 0,
            totalStateChanges: 0,
            stateChangesByType: new Map(),
            stateDistribution: new Map(),
            averageStateChangesPerRecord: 0,
            mostUsedState: null,
            transitionCount: new Map()
        };
    }
}

// 使用示例
const stateSelectionManager = new StateSelectionFieldManager();

// 创建状态选择字段
const stateSelectionField = stateSelectionManager.createStateSelectionField({
    name: 'kanban_state',
    record: {
        data: { 
            kanban_state: 'in_progress'
        },
        fields: { 
            kanban_state: { 
                type: 'selection',
                selection: [
                    ['draft', 'Draft'],
                    ['in_progress', 'In Progress'],
                    ['done', 'Done'],
                    ['blocked', 'Blocked']
                ]
            }
        }
    },
    showLabel: true,
    withCommand: true,
    autosave: true
});

// 注册自定义状态颜色
stateSelectionManager.registerStateColor('review', 'purple');

// 注册自定义转换规则
stateSelectionManager.registerTransitionRule('review', ['approved', 'rejected']);

// 获取统计信息
const stats = stateSelectionManager.getStateStatistics();
console.log('State selection field statistics:', stats);
```

## 技术特点

### 1. 状态管理
- **状态颜色**: 根据状态值显示不同颜色
- **状态转换**: 支持状态之间的转换
- **状态验证**: 验证状态转换的有效性
- **状态历史**: 记录状态变更历史

### 2. 快捷键支持
- **热键绑定**: 为前几个选项绑定Alt+字母快捷键
- **命令注册**: 注册状态切换命令
- **可用性检查**: 检查命令是否可用
- **智能操作**: 提供智能的状态切换操作

### 3. 下拉交互
- **下拉菜单**: 使用下拉菜单显示选项
- **选项过滤**: 根据转换规则过滤选项
- **即时更新**: 选择后即时更新状态
- **自动保存**: 支持选择后自动保存

### 4. 视觉反馈
- **颜色编码**: 使用颜色区分不同状态
- **状态指示**: 清晰的状态视觉指示
- **转换动画**: 状态转换的视觉反馈
- **错误提示**: 无效转换的错误提示

## 设计模式

### 1. 状态模式 (State Pattern)
- **状态对象**: 每个状态有对应的行为
- **状态转换**: 定义状态之间的转换规则
- **状态验证**: 验证状态转换的有效性

### 2. 命令模式 (Command Pattern)
- **状态命令**: 封装状态切换为命令
- **快捷键**: 绑定快捷键到命令
- **撤销重做**: 支持状态变更的撤销

### 3. 策略模式 (Strategy Pattern)
- **颜色策略**: 不同状态的颜色策略
- **转换策略**: 不同的状态转换策略
- **验证策略**: 不同的验证策略

### 4. 观察者模式 (Observer Pattern)
- **状态观察**: 观察状态变化
- **转换观察**: 观察状态转换
- **通知机制**: 状态变更通知

## 注意事项

1. **转换规则**: 确保状态转换规则的正确性
2. **权限控制**: 检查状态变更的权限
3. **数据一致性**: 保持状态数据的一致性
4. **用户体验**: 提供清晰的状态指示

## 扩展建议

1. **工作流集成**: 集成复杂的工作流引擎
2. **批量操作**: 支持批量状态变更
3. **状态报告**: 生成状态变更报告
4. **权限控制**: 增强状态变更权限控制
5. **自动转换**: 支持基于条件的自动状态转换

该状态选择字段为Odoo Web客户端提供了强大的状态管理功能，通过颜色编码、快捷键支持和状态转换验证确保了工作流管理的高效性和准确性。

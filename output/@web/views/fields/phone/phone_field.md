# PhoneField - 电话字段

## 概述

`phone_field.js` 是 Odoo Web 客户端的电话字段组件，负责处理电话号码的输入、显示和拨号功能。该模块包含49行代码，是一个简洁的电话处理组件，专门用于处理char类型的电话字段，具备电话链接、格式化显示、表单专用版本等特性，是联系信息管理的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/phone/phone_field.js`
- **行数**: 49
- **模块**: `@web/views/fields/phone/phone_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                    // 注册表
'@web/core/l10n/translation'            // 翻译服务
'@web/views/fields/input_field_hook'    // 输入字段钩子
'@web/views/fields/standard_field_props' // 标准字段属性
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 基础电话字段

```javascript
const PhoneField = class PhoneField extends Component {
    static template = "web.PhoneField";
    static props = {
        ...standardFieldProps,
        placeholder: { type: String, optional: true },
    };

    setup() {
        useInputField({ getValue: () => this.props.record.data[this.props.name] || "" });
    }
    
    get phoneHref() {
        return "tel:" + this.props.record.data[this.props.name].replace(/\s+/g, "");
    }
}
```

**基础特性**:
- **标准属性**: 继承所有标准字段属性
- **占位符**: 支持placeholder配置占位符
- **输入钩子**: 使用输入字段钩子管理输入
- **电话链接**: 生成tel:协议的电话链接

### 2. 表单电话字段

```javascript
class FormPhoneField extends PhoneField {
    static template = "web.FormPhoneField";
}

const formPhoneField = {
    ...phoneField,
    component: FormPhoneField,
};

registry.category("fields").add("form.phone", formPhoneField);
```

**表单特性**:
- **继承基类**: 继承基础电话字段功能
- **专用模板**: 使用FormPhoneField专用模板
- **配置继承**: 继承基础字段配置
- **表单优化**: 专门为表单视图优化

### 3. 字段注册

```javascript
const phoneField = {
    component: PhoneField,
    displayName: _t("Phone"),
    supportedTypes: ["char"],
    extractProps: ({ attrs }) => ({
        placeholder: attrs.placeholder,
    }),
};

registry.category("fields").add("phone", phoneField);
```

**注册功能**:
- **组件注册**: 注册电话字段组件
- **显示名称**: 设置为"Phone"
- **类型支持**: 仅支持char类型
- **属性提取**: 提取placeholder属性
- **双重注册**: 注册普通和表单两种类型

## 使用场景

### 1. 电话字段管理器

```javascript
// 电话字段管理器
class PhoneFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置电话字段配置
        this.phoneConfig = {
            enableValidation: true,
            enableFormatting: true,
            enableInternational: true,
            enableClickToDial: true,
            enableSMS: false,
            enableWhatsApp: false,
            enableCountryDetection: true,
            enableAutoFormat: true
        };
        
        // 设置格式化选项
        this.formatOptions = {
            enableNationalFormat: true,
            enableInternationalFormat: true,
            enableE164Format: false,
            defaultCountry: 'US',
            showCountryCode: false,
            separatorStyle: 'space', // 'space', 'dash', 'dot', 'none'
            grouping: true
        };
        
        // 设置验证规则
        this.validationRules = {
            enableLengthValidation: true,
            minLength: 7,
            maxLength: 15,
            enableFormatValidation: true,
            allowedFormats: ['national', 'international', 'e164'],
            enableCountryValidation: false,
            allowedCountries: [],
            blockedCountries: []
        };
        
        // 设置国家代码映射
        this.countryCodeMap = new Map([
            ['US', '+1'],
            ['CN', '+86'],
            ['UK', '+44'],
            ['DE', '+49'],
            ['FR', '+33'],
            ['JP', '+81'],
            ['KR', '+82'],
            ['IN', '+91'],
            ['AU', '+61'],
            ['CA', '+1']
        ]);
        
        // 设置电话统计
        this.phoneStatistics = {
            totalPhoneFields: 0,
            totalPhoneNumbers: 0,
            validNumbers: 0,
            invalidNumbers: 0,
            countryDistribution: new Map(),
            formatDistribution: new Map(),
            clickToDialCount: 0
        };
        
        this.initializePhoneSystem();
    }
    
    // 初始化电话系统
    initializePhoneSystem() {
        // 创建增强的电话字段
        this.createEnhancedPhoneField();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置格式化系统
        this.setupFormattingSystem();
        
        // 设置国际化系统
        this.setupInternationalizationSystem();
    }
    
    // 创建增强的电话字段
    createEnhancedPhoneField() {
        const originalField = PhoneField;
        
        this.EnhancedPhoneField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
                
                // 添加格式化功能
                this.addFormattingFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isValid: true,
                    validationErrors: [],
                    detectedCountry: null,
                    formattedNumber: '',
                    originalNumber: '',
                    numberType: null,
                    isInternational: false
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的电话链接
                this.enhancedPhoneHref = () => {
                    const phoneNumber = this.props.record.data[this.props.name];
                    
                    if (!phoneNumber) {
                        return null;
                    }
                    
                    // 清理电话号码
                    const cleanedNumber = this.cleanPhoneNumber(phoneNumber);
                    
                    // 验证电话号码
                    if (!this.validatePhoneNumber(cleanedNumber)) {
                        return null;
                    }
                    
                    // 格式化为E164格式
                    const e164Number = this.formatToE164(cleanedNumber);
                    
                    return `tel:${e164Number}`;
                };
                
                // 清理电话号码
                this.cleanPhoneNumber = (phoneNumber) => {
                    if (!phoneNumber) return '';
                    
                    // 移除所有非数字字符，保留+号
                    return phoneNumber.replace(/[^\d+]/g, '');
                };
                
                // 验证电话号码
                this.validatePhoneNumber = (phoneNumber) => {
                    const errors = [];
                    
                    if (!phoneNumber) {
                        errors.push('Phone number is required');
                        this.enhancedState.validationErrors = errors;
                        this.enhancedState.isValid = false;
                        return false;
                    }
                    
                    // 长度验证
                    if (this.validationRules.enableLengthValidation) {
                        const cleanNumber = phoneNumber.replace(/^\+/, '');
                        if (cleanNumber.length < this.validationRules.minLength) {
                            errors.push(`Phone number must be at least ${this.validationRules.minLength} digits`);
                        }
                        if (cleanNumber.length > this.validationRules.maxLength) {
                            errors.push(`Phone number must not exceed ${this.validationRules.maxLength} digits`);
                        }
                    }
                    
                    // 格式验证
                    if (this.validationRules.enableFormatValidation) {
                        if (!this.isValidPhoneFormat(phoneNumber)) {
                            errors.push('Invalid phone number format');
                        }
                    }
                    
                    // 国家验证
                    if (this.validationRules.enableCountryValidation) {
                        const country = this.detectCountry(phoneNumber);
                        if (this.validationRules.allowedCountries.length > 0 && 
                            !this.validationRules.allowedCountries.includes(country)) {
                            errors.push(`Phone number from ${country} is not allowed`);
                        }
                        if (this.validationRules.blockedCountries.includes(country)) {
                            errors.push(`Phone number from ${country} is blocked`);
                        }
                    }
                    
                    this.enhancedState.validationErrors = errors;
                    this.enhancedState.isValid = errors.length === 0;
                    
                    if (errors.length > 0) {
                        this.phoneStatistics.invalidNumbers++;
                    } else {
                        this.phoneStatistics.validNumbers++;
                    }
                    
                    return errors.length === 0;
                };
                
                // 检查电话格式
                this.isValidPhoneFormat = (phoneNumber) => {
                    // 基本格式检查
                    const patterns = [
                        /^\+\d{1,3}\d{4,14}$/, // 国际格式
                        /^\d{7,15}$/, // 国内格式
                        /^\(\d{3}\)\s?\d{3}-?\d{4}$/, // 美国格式
                        /^\d{3}-?\d{3}-?\d{4}$/ // 美国格式变体
                    ];
                    
                    return patterns.some(pattern => pattern.test(phoneNumber));
                };
                
                // 检测国家
                this.detectCountry = (phoneNumber) => {
                    if (!phoneNumber.startsWith('+')) {
                        return this.formatOptions.defaultCountry;
                    }
                    
                    // 检测国家代码
                    for (const [country, code] of this.countryCodeMap.entries()) {
                        if (phoneNumber.startsWith(code)) {
                            this.enhancedState.detectedCountry = country;
                            return country;
                        }
                    }
                    
                    return 'Unknown';
                };
                
                // 格式化为E164
                this.formatToE164 = (phoneNumber) => {
                    let cleaned = this.cleanPhoneNumber(phoneNumber);
                    
                    // 如果没有国家代码，添加默认国家代码
                    if (!cleaned.startsWith('+')) {
                        const defaultCode = this.countryCodeMap.get(this.formatOptions.defaultCountry) || '+1';
                        cleaned = defaultCode + cleaned;
                    }
                    
                    return cleaned;
                };
                
                // 格式化显示
                this.formatForDisplay = (phoneNumber) => {
                    if (!phoneNumber) return '';
                    
                    const country = this.detectCountry(phoneNumber);
                    const cleaned = this.cleanPhoneNumber(phoneNumber);
                    
                    if (this.formatOptions.enableInternationalFormat && cleaned.startsWith('+')) {
                        return this.formatInternational(cleaned);
                    } else if (this.formatOptions.enableNationalFormat) {
                        return this.formatNational(cleaned, country);
                    }
                    
                    return phoneNumber;
                };
                
                // 国际格式化
                this.formatInternational = (phoneNumber) => {
                    // 简单的国际格式化
                    const country = this.detectCountry(phoneNumber);
                    const code = this.countryCodeMap.get(country);
                    
                    if (code && phoneNumber.startsWith(code)) {
                        const number = phoneNumber.substring(code.length);
                        return `${code} ${this.addSeparators(number)}`;
                    }
                    
                    return phoneNumber;
                };
                
                // 国内格式化
                this.formatNational = (phoneNumber, country) => {
                    const cleaned = phoneNumber.replace(/^\+\d{1,3}/, '');
                    
                    switch (country) {
                        case 'US':
                        case 'CA':
                            return this.formatUSPhone(cleaned);
                        case 'CN':
                            return this.formatCNPhone(cleaned);
                        default:
                            return this.addSeparators(cleaned);
                    }
                };
                
                // 美国电话格式化
                this.formatUSPhone = (phoneNumber) => {
                    if (phoneNumber.length === 10) {
                        return `(${phoneNumber.substring(0, 3)}) ${phoneNumber.substring(3, 6)}-${phoneNumber.substring(6)}`;
                    }
                    return phoneNumber;
                };
                
                // 中国电话格式化
                this.formatCNPhone = (phoneNumber) => {
                    if (phoneNumber.length === 11 && phoneNumber.startsWith('1')) {
                        return `${phoneNumber.substring(0, 3)} ${phoneNumber.substring(3, 7)} ${phoneNumber.substring(7)}`;
                    }
                    return phoneNumber;
                };
                
                // 添加分隔符
                this.addSeparators = (phoneNumber) => {
                    if (!this.formatOptions.grouping) {
                        return phoneNumber;
                    }
                    
                    const separator = this.getSeparator();
                    const groups = [];
                    
                    // 简单分组：每3-4位一组
                    for (let i = 0; i < phoneNumber.length; i += 3) {
                        groups.push(phoneNumber.substring(i, i + 3));
                    }
                    
                    return groups.join(separator);
                };
                
                // 获取分隔符
                this.getSeparator = () => {
                    switch (this.formatOptions.separatorStyle) {
                        case 'dash': return '-';
                        case 'dot': return '.';
                        case 'space': return ' ';
                        case 'none': return '';
                        default: return ' ';
                    }
                };
                
                // 点击拨号
                this.clickToDial = () => {
                    if (!this.phoneConfig.enableClickToDial) {
                        return;
                    }
                    
                    const href = this.enhancedPhoneHref();
                    if (href) {
                        window.location.href = href;
                        this.phoneStatistics.clickToDialCount++;
                    }
                };
                
                // 发送短信
                this.sendSMS = () => {
                    if (!this.phoneConfig.enableSMS) {
                        return;
                    }
                    
                    const phoneNumber = this.cleanPhoneNumber(this.props.record.data[this.props.name]);
                    if (phoneNumber) {
                        window.location.href = `sms:${phoneNumber}`;
                    }
                };
                
                // 打开WhatsApp
                this.openWhatsApp = () => {
                    if (!this.phoneConfig.enableWhatsApp) {
                        return;
                    }
                    
                    const phoneNumber = this.cleanPhoneNumber(this.props.record.data[this.props.name]);
                    if (phoneNumber) {
                        const whatsappUrl = `https://wa.me/${phoneNumber.replace(/^\+/, '')}`;
                        window.open(whatsappUrl, '_blank');
                    }
                };
                
                // 获取电话信息
                this.getPhoneInfo = () => {
                    const phoneNumber = this.props.record.data[this.props.name];
                    
                    return {
                        original: phoneNumber,
                        cleaned: this.cleanPhoneNumber(phoneNumber),
                        formatted: this.formatForDisplay(phoneNumber),
                        e164: this.formatToE164(phoneNumber),
                        country: this.detectCountry(phoneNumber),
                        isValid: this.enhancedState.isValid,
                        validationErrors: this.enhancedState.validationErrors,
                        href: this.enhancedPhoneHref(),
                        type: this.enhancedState.numberType
                    };
                };
                
                // 记录电话统计
                this.recordPhoneStatistics = () => {
                    const phoneNumber = this.props.record.data[this.props.name];
                    if (!phoneNumber) return;
                    
                    this.phoneStatistics.totalPhoneNumbers++;
                    
                    // 记录国家分布
                    const country = this.detectCountry(phoneNumber);
                    const countryCount = this.phoneStatistics.countryDistribution.get(country) || 0;
                    this.phoneStatistics.countryDistribution.set(country, countryCount + 1);
                    
                    // 记录格式分布
                    const format = this.detectFormat(phoneNumber);
                    const formatCount = this.phoneStatistics.formatDistribution.get(format) || 0;
                    this.phoneStatistics.formatDistribution.set(format, formatCount + 1);
                };
                
                // 检测格式
                this.detectFormat = (phoneNumber) => {
                    if (phoneNumber.startsWith('+')) {
                        return 'international';
                    } else if (/^\(\d{3}\)/.test(phoneNumber)) {
                        return 'us_formatted';
                    } else if (/^\d{3}-\d{3}-\d{4}$/.test(phoneNumber)) {
                        return 'us_dashed';
                    } else {
                        return 'national';
                    }
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.phoneConfig.enableValidation,
                    validate: (phoneNumber) => this.validatePhoneNumber(phoneNumber),
                    getErrors: () => this.enhancedState.validationErrors,
                    isValid: () => this.enhancedState.isValid
                };
            }
            
            addFormattingFeatures() {
                // 格式化功能
                this.formattingManager = {
                    enabled: this.phoneConfig.enableFormatting,
                    format: (phoneNumber) => this.formatForDisplay(phoneNumber),
                    toE164: (phoneNumber) => this.formatToE164(phoneNumber),
                    clean: (phoneNumber) => this.cleanPhoneNumber(phoneNumber),
                    detectCountry: (phoneNumber) => this.detectCountry(phoneNumber)
                };
            }
            
            // 重写原始方法
            get phoneHref() {
                return this.enhancedPhoneHref();
            }
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.phoneConfig.enableValidation,
            rules: this.validationRules
        };
    }
    
    // 设置格式化系统
    setupFormattingSystem() {
        this.formattingSystemConfig = {
            enabled: this.phoneConfig.enableFormatting,
            options: this.formatOptions
        };
    }
    
    // 设置国际化系统
    setupInternationalizationSystem() {
        this.internationalizationConfig = {
            enabled: this.phoneConfig.enableInternational,
            countryCodes: this.countryCodeMap,
            defaultCountry: this.formatOptions.defaultCountry
        };
    }
    
    // 创建电话字段
    createPhoneField(props) {
        const field = new this.EnhancedPhoneField(props);
        this.phoneStatistics.totalPhoneFields++;
        return field;
    }
    
    // 批量创建电话字段
    batchCreatePhoneFields(propsArray) {
        const results = [];
        
        for (const props of propsArray) {
            try {
                const field = this.createPhoneField(props);
                results.push({ field, success: true, props });
            } catch (error) {
                results.push({ field: null, success: false, error, props });
            }
        }
        
        return results;
    }
    
    // 添加国家代码
    addCountryCode(country, code) {
        this.countryCodeMap.set(country, code);
    }
    
    // 获取热门国家
    getPopularCountries(limit = 10) {
        const sorted = Array.from(this.phoneStatistics.countryDistribution.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, limit);
        
        return sorted.map(([country, count]) => ({ country, count }));
    }
    
    // 获取电话统计
    getPhoneStatistics() {
        return {
            ...this.phoneStatistics,
            validationRate: (this.phoneStatistics.validNumbers / Math.max(this.phoneStatistics.totalPhoneNumbers, 1)) * 100,
            averageNumbersPerField: this.phoneStatistics.totalPhoneNumbers / Math.max(this.phoneStatistics.totalPhoneFields, 1),
            countryVariety: this.phoneStatistics.countryDistribution.size,
            formatVariety: this.phoneStatistics.formatDistribution.size,
            clickToDialRate: (this.phoneStatistics.clickToDialCount / Math.max(this.phoneStatistics.totalPhoneNumbers, 1)) * 100
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理国家分布
        this.phoneStatistics.countryDistribution.clear();
        
        // 清理格式分布
        this.phoneStatistics.formatDistribution.clear();
        
        // 清理国家代码
        this.countryCodeMap.clear();
        
        // 重置统计
        this.phoneStatistics = {
            totalPhoneFields: 0,
            totalPhoneNumbers: 0,
            validNumbers: 0,
            invalidNumbers: 0,
            countryDistribution: new Map(),
            formatDistribution: new Map(),
            clickToDialCount: 0
        };
    }
}

// 使用示例
const phoneManager = new PhoneFieldManager();

// 创建电话字段
const phoneField = phoneManager.createPhoneField({
    name: 'phone',
    record: {
        data: { phone: '+****************' },
        fields: { 
            phone: { 
                type: 'char'
            }
        }
    },
    placeholder: 'Enter phone number'
});

// 添加自定义国家代码
phoneManager.addCountryCode('BR', '+55');

// 获取统计信息
const stats = phoneManager.getPhoneStatistics();
console.log('Phone field statistics:', stats);
```

## 技术特点

### 1. 电话链接
- **Tel协议**: 使用tel:协议生成电话链接
- **号码清理**: 自动清理空格等字符
- **点击拨号**: 支持点击拨号功能
- **移动优化**: 针对移动设备优化

### 2. 双重注册
- **基础字段**: 注册为phone字段
- **表单字段**: 注册为form.phone字段
- **模板区分**: 使用不同的模板
- **功能继承**: 表单字段继承基础功能

### 3. 输入管理
- **输入钩子**: 使用输入字段钩子
- **值获取**: 安全的值获取机制
- **空值处理**: 处理空值情况
- **占位符**: 支持占位符显示

### 4. 简洁设计
- **最小代码**: 极简的代码实现
- **专注功能**: 专注于电话号码处理
- **易于扩展**: 易于扩展和定制
- **标准兼容**: 符合Web标准

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **类继承**: FormPhoneField继承PhoneField
- **配置继承**: 表单字段继承基础配置
- **功能继承**: 继承所有基础功能

### 2. 模板方法模式 (Template Method Pattern)
- **模板区分**: 不同视图使用不同模板
- **渲染模板**: 定义电话字段渲染模板
- **链接模板**: 定义电话链接生成模板

### 3. 策略模式 (Strategy Pattern)
- **显示策略**: 不同视图的显示策略
- **格式化策略**: 不同的号码格式化策略
- **验证策略**: 不同的号码验证策略

### 4. 装饰器模式 (Decorator Pattern)
- **链接装饰**: 为电话号码添加链接装饰
- **格式装饰**: 添加格式化装饰
- **交互装饰**: 添加交互功能装饰

## 注意事项

1. **号码格式**: 处理不同国家的电话号码格式
2. **隐私保护**: 注意电话号码的隐私保护
3. **移动兼容**: 确保移动设备的兼容性
4. **用户体验**: 提供清晰的拨号指示

## 扩展建议

1. **国际化**: 支持国际电话号码格式
2. **验证功能**: 添加电话号码验证
3. **格式化**: 智能的号码格式化
4. **短信功能**: 集成短信发送功能
5. **通话记录**: 记录通话历史

该电话字段为Odoo Web客户端提供了简洁的电话号码处理功能，通过tel:协议集成和双重注册设计确保了在不同视图中的最佳用户体验。

# SignatureField - 签名字段

## 概述

`signature_field.js` 是 Odoo Web 客户端的签名字段组件，负责处理数字签名的显示、编辑和管理。该模块包含186行代码，是一个功能完整的签名处理组件，专门用于处理signature和binary类型的签名字段，具备签名对话框、图像显示、缓存管理、类型验证等特性，是数字签名管理的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/signature/signature_field.js`
- **行数**: 186
- **模块**: `@web/views/fields/signature/signature_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/core/signature/signature_dialog'  // 签名对话框
'@web/core/utils/hooks'                 // 工具钩子
'@web/core/utils/urls'                  // URL工具
'@web/core/utils/binary'                // 二进制工具
'@web/views/fields/image/image_field'   // 图像字段
'@web/views/fields/standard_field_props' // 标准字段属性
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const SignatureField = class SignatureField extends Component {
    static template = "web.SignatureField";
    static props = {
        ...standardFieldProps,
        defaultFont: { type: String },
        fullName: { type: String, optional: true },
        height: { type: Number, optional: true },
        previewImage: { type: String, optional: true },
        width: { type: Number, optional: true },
        type: { validate: (t) => ["initial", "signature"].includes(t), optional: true },
    };
    static defaultProps = {
        type: "signature",
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **默认字体**: 支持defaultFont配置默认字体
- **全名**: 支持fullName配置签名者全名
- **尺寸**: 支持height和width配置签名区域尺寸
- **预览图**: 支持previewImage配置预览图像
- **类型**: 支持initial和signature两种类型
- **默认类型**: 默认为signature类型

### 2. 组件初始化

```javascript
setup() {
    this.displaySignatureRatio = 3;

    this.dialogService = useService("dialog");
    this.state = useState({
        isValid: true,
    });
}
```

**初始化功能**:
- **显示比例**: 设置签名显示比例为3
- **对话框服务**: 注入对话框服务
- **状态管理**: 管理签名有效性状态
- **验证状态**: 初始化为有效状态

### 3. URL和缓存管理

```javascript
get rawCacheKey() {
    return this.props.record.data.write_date;
}

get getUrl() {
    const { name, previewImage, record } = this.props;
    if (previewImage && record.data[previewImage]) {
        return imageUrl(record.resModel, record.resId, previewImage, {
            unique: this.rawCacheKey,
        });
    }
    if (record.data[name]) {
        return imageUrl(record.resModel, record.resId, name, {
            unique: this.rawCacheKey,
        });
    }
    return placeholder;
}
```

**URL管理功能**:
- **缓存键**: 使用写入日期作为缓存键
- **预览图**: 优先使用预览图像
- **签名图**: 使用签名字段图像
- **占位符**: 无签名时显示占位符
- **缓存控制**: 通过unique参数控制缓存

### 4. 签名验证

```javascript
get isSet() {
    const { name, record } = this.props;
    return !!record.data[name];
}

get nameAndSignatureOptions() {
    const { fullName, defaultFont } = this.props;
    return {
        displayName: fullName || this.props.record.data.display_name || "",
        fontFamily: defaultFont || "serif",
    };
}
```

**验证功能**:
- **签名检查**: 检查是否已设置签名
- **名称选项**: 获取签名者姓名和字体选项
- **显示名称**: 使用fullName或记录的display_name
- **字体设置**: 使用默认字体或serif字体

### 5. 签名操作

```javascript
async onSignatureClick() {
    if (this.props.readonly) {
        return;
    }

    const { name, type } = this.props;
    const signatureOptions = {
        ...this.nameAndSignatureOptions,
        mode: type,
        signatureType: type === "initial" ? "initial" : "signature",
    };

    this.dialogService.add(SignatureDialog, {
        ...signatureOptions,
        uploadSignature: (signature) => {
            this.props.record.update({ [name]: signature });
        },
    });
}

async onClearSignature() {
    if (this.props.readonly) {
        return;
    }

    const { name } = this.props;
    this.props.record.update({ [name]: false });
}
```

**操作功能**:
- **签名点击**: 打开签名对话框
- **只读检查**: 检查只读状态
- **签名选项**: 配置签名对话框选项
- **上传签名**: 更新记录的签名字段
- **清除签名**: 清除签名数据

## 使用场景

### 1. 签名字段管理器

```javascript
// 签名字段管理器
class SignatureFieldManager {
    constructor() {
        this.setupManager();
    }

    setupManager() {
        // 设置签名字段配置
        this.signatureConfig = {
            enableSignatureDialog: true,
            enablePreviewImage: true,
            enableSignatureValidation: true,
            enableSignatureCaching: true,
            enableSignatureHistory: false,
            enableSignatureTemplates: false,
            enableSignatureExport: false,
            enableSignatureWatermark: false
        };

        // 设置签名类型
        this.signatureTypes = new Map([
            ['signature', {
                name: 'Full Signature',
                description: 'Complete digital signature',
                defaultFont: 'serif',
                minWidth: 300,
                minHeight: 150,
                aspectRatio: 2
            }],
            ['initial', {
                name: 'Initials',
                description: 'Initial signature',
                defaultFont: 'cursive',
                minWidth: 100,
                minHeight: 50,
                aspectRatio: 2
            }]
        ]);

        // 设置字体选项
        this.fontOptions = new Map([
            ['serif', { name: 'Serif', family: 'serif', style: 'formal' }],
            ['sans-serif', { name: 'Sans Serif', family: 'sans-serif', style: 'modern' }],
            ['cursive', { name: 'Cursive', family: 'cursive', style: 'handwritten' }],
            ['monospace', { name: 'Monospace', family: 'monospace', style: 'technical' }]
        ]);

        // 设置验证规则
        this.validationRules = {
            enableSizeValidation: true,
            enableFormatValidation: true,
            enableQualityValidation: false,
            maxFileSize: 1024 * 1024, // 1MB
            allowedFormats: ['png', 'jpg', 'jpeg', 'svg'],
            minWidth: 50,
            minHeight: 25,
            maxWidth: 1000,
            maxHeight: 500
        };

        // 设置签名统计
        this.signatureStatistics = {
            totalSignatureFields: 0,
            totalSignatures: 0,
            signaturesByType: new Map(),
            signaturesByFont: new Map(),
            averageSignatureSize: 0,
            mostUsedFont: null,
            validationErrors: 0,
            successfulSignatures: 0
        };

        this.initializeSignatureSystem();
    }

    // 初始化签名系统
    initializeSignatureSystem() {
        // 创建增强的签名字段
        this.createEnhancedSignatureField();

        // 设置验证系统
        this.setupValidationSystem();

        // 设置缓存系统
        this.setupCacheSystem();

        // 设置模板系统
        this.setupTemplateSystem();
    }
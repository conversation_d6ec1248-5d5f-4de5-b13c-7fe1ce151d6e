# BinaryField - 二进制字段

## 概述

`binary_field.js` 是 Odoo Web 客户端的二进制字段组件，负责处理文件的上传、下载和显示。该模块包含103行代码，是一个文件处理组件，专门用于处理二进制数据字段，具备文件上传、文件下载、文件名管理、类型限制等特性，是文档管理和文件处理的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/binary/binary_field.js`
- **行数**: 103
- **模块**: `@web/views/fields/binary/binary_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                    // 注册表
'@web/core/utils/hooks'                 // 工具钩子
'@web/core/utils/binary'                // 二进制工具
'@web/core/network/download'            // 下载服务
'@web/views/fields/standard_field_props' // 标准字段属性
'@web/views/fields/file_handler'        // 文件处理器
'@web/core/l10n/translation'            // 翻译服务
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const BinaryField = class BinaryField extends Component {
    static template = "web.BinaryField";
    static components = {
        FileUploader,
    };
    static props = {
        ...standardFieldProps,
        acceptedFileExtensions: { type: String, optional: true },
        fileNameField: { type: String, optional: true },
    };
    static defaultProps = {
        acceptedFileExtensions: "*",
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **文件上传器**: 集成FileUploader组件
- **扩展限制**: 支持文件扩展名限制
- **文件名字段**: 支持关联文件名字段
- **默认配置**: 默认接受所有文件类型

### 2. 常量定义

```javascript
const MAX_FILENAME_SIZE_BYTES = 0xFF;  // filenames do not exceed 255 bytes on Linux/Windows/MacOS
```

**常量功能**:
- **文件名限制**: 定义文件名最大字节数
- **跨平台兼容**: 兼容Linux/Windows/MacOS系统
- **安全限制**: 防止文件名过长导致的问题

### 3. 文件名处理

```javascript
get fileName() {
    return (
        this.props.record.data[this.props.fileNameField] ||
        this.props.record.data[this.props.name] ||
        ""
    ).slice(0, toBase64Length(MAX_FILENAME_SIZE_BYTES));
}
```

**文件名功能**:
- **字段优先级**: 优先使用fileNameField，其次使用字段本身
- **长度限制**: 限制文件名长度不超过最大字节数
- **Base64转换**: 考虑Base64编码后的长度
- **默认处理**: 提供空字符串作为默认值

### 4. 数据更新

```javascript
update({ data, name }) {
    const { fileNameField, record } = this.props;
    const changes = { [this.props.name]: data || false };
    if (fileNameField in record.fields && record.data[fileNameField] !== name) {
        changes[fileNameField] = name || '';
    }
    return this.props.record.update(changes);
}
```

**更新功能**:
- **数据更新**: 更新二进制数据字段
- **文件名同步**: 同步更新文件名字段
- **字段检查**: 检查文件名字段是否存在
- **批量更新**: 一次性更新多个字段

### 5. 下载功能

```javascript
getDownloadData() {
    return {
        model: this.props.record.resModel,
        id: this.props.record.resId,
        field: this.props.name,
        filename_field: this.fileName,
        filename: this.fileName || "",
        download: true,
        data: isBinarySize(this.props.record.data[this.props.name])
            ? null
            : this.props.record.data[this.props.name],
    };
}

async onFileDownload() {
    await download({
        data: this.getDownloadData(),
        url: "/web/content",
    });
}
```

**下载功能**:
- **下载数据**: 构建下载所需的数据结构
- **二进制检查**: 检查数据是否为二进制大小标识
- **文件下载**: 通过/web/content端点下载文件
- **异步处理**: 异步处理下载请求

### 6. 列表字段变体

```javascript
const ListBinaryField = class ListBinaryField extends BinaryField {
    static template = "web.ListBinaryField";
}
```

**列表变体功能**:
- **继承基类**: 继承BinaryField的所有功能
- **专用模板**: 使用列表专用模板
- **简化显示**: 适合列表视图的简化显示

### 7. 字段注册

```javascript
const binaryField = {
    component: BinaryField,
    displayName: _t("File"),
    supportedOptions: [
        {
            label: _t("Accepted file extensions"),
            name: "accepted_file_extensions",
            type: "string",
        },
    ],
    supportedTypes: ["binary"],
    extractProps: ({ attrs, options }) => ({
        acceptedFileExtensions: options.accepted_file_extensions,
        fileNameField: attrs.filename,
    }),
};

const listBinaryField = {
    ...binaryField,
    component: ListBinaryField,
};

registry.category("fields").add("binary", binaryField);
registry.category("fields").add("list.binary", listBinaryField);
```

**注册功能**:
- **组件注册**: 注册二进制字段组件
- **选项支持**: 支持文件扩展名限制选项
- **类型支持**: 仅支持binary类型
- **属性提取**: 提取扩展名和文件名字段属性
- **多重注册**: 注册普通和列表两种变体

## 使用场景

### 1. 二进制字段管理器

```javascript
// 二进制字段管理器
class BinaryFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置文件配置
        this.fileConfig = {
            maxFileSize: 10 * 1024 * 1024, // 10MB
            allowedExtensions: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'png', 'gif'],
            enablePreview: true,
            enableThumbnails: true,
            enableCompression: true,
            enableVirusScan: false,
            uploadChunkSize: 1024 * 1024, // 1MB
            enableProgressTracking: true
        };
        
        // 设置下载配置
        this.downloadConfig = {
            enableBatchDownload: true,
            enableDownloadHistory: true,
            enableDownloadTracking: true,
            maxConcurrentDownloads: 3
        };
        
        // 设置文件缓存
        this.fileCache = new Map();
        
        // 设置文件统计
        this.fileStatistics = {
            totalUploads: 0,
            totalDownloads: 0,
            totalSize: 0,
            averageUploadTime: 0,
            averageDownloadTime: 0
        };
        
        this.initializeFileSystem();
    }
    
    // 初始化文件系统
    initializeFileSystem() {
        // 创建增强的二进制字段
        this.createEnhancedBinaryField();
        
        // 设置文件验证
        this.setupFileValidation();
        
        // 设置文件处理
        this.setupFileProcessing();
        
        // 设置安全检查
        this.setupSecurityChecks();
    }
    
    // 创建增强的二进制字段
    createEnhancedBinaryField() {
        const originalField = BinaryField;
        
        this.EnhancedBinaryField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加预览功能
                this.addPreviewFeatures();
                
                // 添加安全功能
                this.addSecurityFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isUploading: false,
                    uploadProgress: 0,
                    isDownloading: false,
                    downloadProgress: 0,
                    fileInfo: null,
                    previewUrl: null,
                    thumbnailUrl: null,
                    validationErrors: []
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的文件名获取
                this.enhancedGetFileName = () => {
                    const fileName = this.fileName;
                    
                    // 清理文件名
                    const cleanedName = this.cleanFileName(fileName);
                    
                    // 验证文件名
                    if (!this.validateFileName(cleanedName)) {
                        return 'unnamed_file';
                    }
                    
                    return cleanedName;
                };
                
                // 清理文件名
                this.cleanFileName = (fileName) => {
                    if (!fileName) return '';
                    
                    // 移除危险字符
                    return fileName.replace(/[<>:"/\\|?*]/g, '_')
                                  .replace(/\s+/g, '_')
                                  .toLowerCase();
                };
                
                // 验证文件名
                this.validateFileName = (fileName) => {
                    if (!fileName || fileName.length === 0) return false;
                    if (fileName.length > 255) return false;
                    if (fileName.startsWith('.')) return false;
                    
                    return true;
                };
                
                // 增强的更新功能
                this.enhancedUpdate = async ({ data, name }) => {
                    const startTime = performance.now();
                    
                    try {
                        // 验证文件数据
                        if (data && !this.validateFileData(data)) {
                            throw new Error('Invalid file data');
                        }
                        
                        // 验证文件大小
                        if (data && !this.validateFileSize(data)) {
                            throw new Error('File size exceeds limit');
                        }
                        
                        // 验证文件类型
                        if (name && !this.validateFileType(name)) {
                            throw new Error('File type not allowed');
                        }
                        
                        // 处理文件压缩
                        if (this.fileConfig.enableCompression && data) {
                            data = await this.compressFile(data);
                        }
                        
                        // 执行原始更新
                        const result = await this.update({ data, name });
                        
                        // 更新文件信息
                        await this.updateFileInfo(data, name);
                        
                        // 生成预览
                        if (this.fileConfig.enablePreview) {
                            await this.generatePreview(data, name);
                        }
                        
                        // 记录统计
                        this.fileStatistics.totalUploads++;
                        if (data) {
                            this.fileStatistics.totalSize += this.getFileSize(data);
                        }
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordUploadTime(endTime - startTime);
                        
                        return result;
                        
                    } catch (error) {
                        this.handleUploadError(error);
                        throw error;
                    }
                };
                
                // 验证文件数据
                this.validateFileData = (data) => {
                    if (!data) return true;
                    
                    // 检查是否为有效的Base64
                    try {
                        atob(data);
                        return true;
                    } catch (error) {
                        return false;
                    }
                };
                
                // 验证文件大小
                this.validateFileSize = (data) => {
                    const size = this.getFileSize(data);
                    return size <= this.fileConfig.maxFileSize;
                };
                
                // 验证文件类型
                this.validateFileType = (fileName) => {
                    if (!fileName) return true;
                    
                    const extension = this.getFileExtension(fileName);
                    return this.fileConfig.allowedExtensions.includes(extension);
                };
                
                // 获取文件大小
                this.getFileSize = (data) => {
                    if (!data) return 0;
                    
                    // Base64编码的大小计算
                    return Math.ceil(data.length * 3 / 4);
                };
                
                // 获取文件扩展名
                this.getFileExtension = (fileName) => {
                    const lastDot = fileName.lastIndexOf('.');
                    return lastDot > 0 ? fileName.substring(lastDot + 1).toLowerCase() : '';
                };
                
                // 压缩文件
                this.compressFile = async (data) => {
                    // 实现文件压缩逻辑
                    console.log('Compressing file data');
                    return data;
                };
                
                // 更新文件信息
                this.updateFileInfo = async (data, name) => {
                    this.enhancedState.fileInfo = {
                        name: name,
                        size: this.getFileSize(data),
                        type: this.getFileType(name),
                        uploadTime: Date.now()
                    };
                };
                
                // 获取文件类型
                this.getFileType = (fileName) => {
                    const extension = this.getFileExtension(fileName);
                    const typeMap = {
                        'pdf': 'application/pdf',
                        'jpg': 'image/jpeg',
                        'jpeg': 'image/jpeg',
                        'png': 'image/png',
                        'gif': 'image/gif',
                        'doc': 'application/msword',
                        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                    };
                    
                    return typeMap[extension] || 'application/octet-stream';
                };
                
                // 生成预览
                this.generatePreview = async (data, name) => {
                    if (!data || !name) return;
                    
                    const fileType = this.getFileType(name);
                    
                    if (fileType.startsWith('image/')) {
                        this.enhancedState.previewUrl = `data:${fileType};base64,${data}`;
                        
                        if (this.fileConfig.enableThumbnails) {
                            this.enhancedState.thumbnailUrl = await this.generateThumbnail(data, fileType);
                        }
                    }
                };
                
                // 生成缩略图
                this.generateThumbnail = async (data, fileType) => {
                    return new Promise((resolve) => {
                        const img = new Image();
                        img.onload = () => {
                            const canvas = document.createElement('canvas');
                            const ctx = canvas.getContext('2d');
                            
                            // 设置缩略图尺寸
                            const maxSize = 150;
                            let { width, height } = img;
                            
                            if (width > height) {
                                if (width > maxSize) {
                                    height = (height * maxSize) / width;
                                    width = maxSize;
                                }
                            } else {
                                if (height > maxSize) {
                                    width = (width * maxSize) / height;
                                    height = maxSize;
                                }
                            }
                            
                            canvas.width = width;
                            canvas.height = height;
                            
                            // 绘制缩略图
                            ctx.drawImage(img, 0, 0, width, height);
                            
                            resolve(canvas.toDataURL('image/jpeg', 0.8));
                        };
                        
                        img.src = `data:${fileType};base64,${data}`;
                    });
                };
                
                // 增强的下载功能
                this.enhancedOnFileDownload = async () => {
                    const startTime = performance.now();
                    this.enhancedState.isDownloading = true;
                    
                    try {
                        // 执行原始下载
                        await this.onFileDownload();
                        
                        // 记录下载统计
                        this.fileStatistics.totalDownloads++;
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordDownloadTime(endTime - startTime);
                        
                    } catch (error) {
                        this.handleDownloadError(error);
                    } finally {
                        this.enhancedState.isDownloading = false;
                    }
                };
                
                // 批量下载
                this.batchDownload = async (files) => {
                    const results = [];
                    
                    for (const file of files) {
                        try {
                            await this.downloadFile(file);
                            results.push({ file, success: true });
                        } catch (error) {
                            results.push({ file, success: false, error });
                        }
                    }
                    
                    return results;
                };
                
                // 下载单个文件
                this.downloadFile = async (file) => {
                    // 实现单个文件下载逻辑
                    console.log('Downloading file:', file);
                };
                
                // 获取文件预览
                this.getFilePreview = () => {
                    return this.enhancedState.previewUrl;
                };
                
                // 获取文件缩略图
                this.getFileThumbnail = () => {
                    return this.enhancedState.thumbnailUrl;
                };
                
                // 处理上传错误
                this.handleUploadError = (error) => {
                    console.error('Upload error:', error);
                    this.enhancedState.validationErrors.push(error.message);
                };
                
                // 处理下载错误
                this.handleDownloadError = (error) => {
                    console.error('Download error:', error);
                };
                
                // 记录上传时间
                this.recordUploadTime = (duration) => {
                    this.fileStatistics.averageUploadTime = 
                        (this.fileStatistics.averageUploadTime + duration) / 2;
                };
                
                // 记录下载时间
                this.recordDownloadTime = (duration) => {
                    this.fileStatistics.averageDownloadTime = 
                        (this.fileStatistics.averageDownloadTime + duration) / 2;
                };
            }
            
            addPreviewFeatures() {
                // 预览功能
                this.previewManager = {
                    enabled: this.fileConfig.enablePreview,
                    supportedTypes: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
                    generatePreview: (data, type) => this.generatePreview(data, type)
                };
            }
            
            addSecurityFeatures() {
                // 安全功能
                this.securityManager = {
                    enableVirusScan: this.fileConfig.enableVirusScan,
                    scanFile: (data) => this.scanFileForVirus(data),
                    validateFile: (data, name) => this.validateFileData(data) && this.validateFileType(name)
                };
            }
            
            // 重写原始方法
            get fileName() {
                return this.enhancedGetFileName();
            }
            
            update(params) {
                return this.enhancedUpdate(params);
            }
            
            onFileDownload() {
                return this.enhancedOnFileDownload();
            }
        };
    }
    
    // 设置文件验证
    setupFileValidation() {
        this.validationConfig = {
            enableSizeCheck: true,
            enableTypeCheck: true,
            enableNameCheck: true,
            enableContentCheck: false
        };
    }
    
    // 设置文件处理
    setupFileProcessing() {
        this.processingConfig = {
            enableCompression: this.fileConfig.enableCompression,
            enableThumbnails: this.fileConfig.enableThumbnails,
            enablePreview: this.fileConfig.enablePreview
        };
    }
    
    // 设置安全检查
    setupSecurityChecks() {
        this.securityConfig = {
            enableVirusScan: this.fileConfig.enableVirusScan,
            enableContentValidation: true,
            enableFileNameSanitization: true
        };
    }
    
    // 创建二进制字段
    createBinaryField(props) {
        return new this.EnhancedBinaryField(props);
    }
    
    // 获取文件统计
    getFileStatistics() {
        return {
            ...this.fileStatistics,
            cacheSize: this.fileCache.size,
            allowedExtensions: this.fileConfig.allowedExtensions.length
        };
    }
    
    // 清理缓存
    clearCache() {
        this.fileCache.clear();
    }
    
    // 销毁管理器
    destroy() {
        // 清理缓存
        this.fileCache.clear();
        
        // 重置统计
        this.fileStatistics = {
            totalUploads: 0,
            totalDownloads: 0,
            totalSize: 0,
            averageUploadTime: 0,
            averageDownloadTime: 0
        };
    }
}

// 使用示例
const binaryManager = new BinaryFieldManager();

// 创建二进制字段
const binaryField = binaryManager.createBinaryField({
    name: 'attachment',
    record: {
        data: { attachment: 'base64data...', attachment_name: 'document.pdf' },
        fields: { attachment: { type: 'binary' } }
    },
    acceptedFileExtensions: 'pdf,doc,docx',
    fileNameField: 'attachment_name'
});

// 获取统计信息
const stats = binaryManager.getFileStatistics();
console.log('Binary field statistics:', stats);
```

## 技术特点

### 1. 文件处理
- **上传支持**: 完整的文件上传功能
- **下载支持**: 安全的文件下载功能
- **类型限制**: 支持文件类型限制
- **大小控制**: 文件大小限制和验证

### 2. 数据管理
- **二进制数据**: 处理Base64编码的二进制数据
- **文件名管理**: 智能的文件名处理和验证
- **字段同步**: 自动同步文件名字段
- **数据验证**: 完整的数据验证机制

### 3. 用户体验
- **进度显示**: 上传和下载进度显示
- **错误处理**: 友好的错误提示
- **预览功能**: 支持文件预览
- **批量操作**: 支持批量文件操作

### 4. 安全性
- **文件验证**: 严格的文件验证
- **类型检查**: 文件类型安全检查
- **大小限制**: 防止过大文件上传
- **名称清理**: 文件名安全清理

## 设计模式

### 1. 组件模式 (Component Pattern)
- **UI组件**: 封装文件处理UI
- **功能组件**: 封装文件处理功能
- **状态管理**: 管理文件状态

### 2. 策略模式 (Strategy Pattern)
- **上传策略**: 不同的文件上传策略
- **下载策略**: 不同的文件下载策略
- **验证策略**: 不同的文件验证策略

### 3. 工厂模式 (Factory Pattern)
- **字段工厂**: 创建不同类型的二进制字段
- **处理器工厂**: 创建文件处理器
- **验证器工厂**: 创建文件验证器

### 4. 观察者模式 (Observer Pattern)
- **状态观察**: 观察文件处理状态
- **进度观察**: 观察上传下载进度
- **错误观察**: 观察错误状态

## 注意事项

1. **安全性**: 严格验证文件类型和内容
2. **性能考虑**: 避免处理过大的文件
3. **内存管理**: 合理处理二进制数据
4. **用户体验**: 提供清晰的状态反馈

## 扩展建议

1. **预览增强**: 支持更多文件类型预览
2. **压缩功能**: 添加文件压缩功能
3. **版本控制**: 支持文件版本管理
4. **批量处理**: 增强批量文件处理
5. **云存储**: 集成云存储服务

该二进制字段为Odoo Web客户端提供了完整的文件处理功能，通过安全的上传下载机制和完善的验证体系确保了文件操作的安全性和可靠性。

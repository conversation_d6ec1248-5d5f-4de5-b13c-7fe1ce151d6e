# MonetaryField - 货币字段

## 概述

`monetary_field.js` 是 Odoo Web 客户端的货币字段组件，负责处理货币金额的输入、显示和格式化。该模块包含129行代码，是一个功能完整的货币处理组件，专门用于处理monetary和float类型的字段，具备货币符号、精度控制、格式化显示、数字键盘等特性，是财务数据处理的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/monetary/monetary_field.js`
- **行数**: 129
- **模块**: `@web/views/fields/monetary/monetary_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                    // 注册表
'@web/core/l10n/translation'            // 翻译服务
'@web/views/fields/formatters'          // 字段格式化器
'@web/views/fields/parsers'             // 字段解析器
'@web/views/fields/input_field_hook'    // 输入字段钩子
'@web/views/fields/numpad_decimal_hook' // 数字键盘小数钩子
'@web/views/fields/standard_field_props' // 标准字段属性
'@web/core/utils/strings'               // 字符串工具
'@odoo/owl'                            // OWL框架
'@web/core/currency'                    // 货币服务
```

## 核心功能

### 1. 组件定义

```javascript
const MonetaryField = class MonetaryField extends Component {
    static template = "web.MonetaryField";
    static props = {
        ...standardFieldProps,
        currencyField: { type: String, optional: true },
        inputType: { type: String, optional: true },
        useFieldDigits: { type: Boolean, optional: true },
        hideSymbol: { type: Boolean, optional: true },
        placeholder: { type: String, optional: true },
    };
    static defaultProps = {
        hideSymbol: false,
        inputType: "text",
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **货币字段**: 支持currencyField配置货币字段
- **输入类型**: 支持inputType配置输入类型
- **精度控制**: 支持useFieldDigits使用字段精度
- **符号控制**: 支持hideSymbol隐藏货币符号
- **占位符**: 支持placeholder配置占位符

### 2. 组件初始化

```javascript
setup() {
    this.inputRef = useInputField(this.inputOptions);
    this.state = useState({ value: undefined });
    this.nbsp = nbsp;
    useNumpadDecimal();
    useEffect(() => {
        if (this.inputRef?.el) {
            this.state.value = this.inputRef.el.value;
        }
    });
}
```

**初始化功能**:
- **输入钩子**: 使用输入字段钩子管理输入
- **状态管理**: 管理字段值状态
- **不间断空格**: 引用不间断空格工具
- **数字键盘**: 使用数字键盘小数钩子
- **效果监听**: 监听输入元素值变化

### 3. 货币ID获取

```javascript
get currencyId() {
    const currencyField =
        this.props.currencyField ||
        this.props.record.fields[this.props.name].currency_field ||
        "currency_id";
    const currency = this.props.record.data[currencyField];
    return currency && currency[0];
}
```

**货币ID功能**:
- **字段优先级**: 按优先级获取货币字段名
- **默认字段**: 默认使用currency_id字段
- **数据提取**: 从记录数据中提取货币ID
- **安全访问**: 安全访问货币数据

### 4. 货币对象获取

```javascript
get currency() {
    if (!isNaN(this.currencyId)) {
        return getCurrency(this.currencyId) || null;
    }
    return null;
}

get currencySymbol() {
    return this.currency ? this.currency.symbol : "";
}
```

**货币对象功能**:
- **货币服务**: 使用getCurrency服务获取货币对象
- **ID验证**: 验证货币ID的有效性
- **符号提取**: 从货币对象中提取符号
- **空值处理**: 处理货币不存在的情况

### 5. 精度控制

```javascript
get currencyDigits() {
    if (this.props.useFieldDigits) {
        return this.props.record.fields[this.props.name].digits;
    }
    if (!this.currency) {
        return null;
    }
    return getCurrency(this.currencyId).digits;
}
```

**精度功能**:
- **字段精度**: 支持使用字段定义的精度
- **货币精度**: 使用货币的默认精度
- **精度获取**: 从货币服务获取精度信息
- **空值处理**: 处理货币不存在时的精度

### 6. 格式化显示

```javascript
get formattedValue() {
    if (this.props.inputType === "number" && !this.props.readonly && this.value) {
        return this.value;
    }
    return formatMonetary(this.value, {
        digits: this.currencyDigits,
        currencyId: this.currencyId,
        noSymbol: !this.props.readonly || this.props.hideSymbol,
    });
}
```

**格式化功能**:
- **输入类型**: 根据输入类型决定格式化方式
- **只读模式**: 只读模式下显示完整格式
- **货币格式化**: 使用formatMonetary格式化金额
- **符号控制**: 根据配置控制符号显示

### 7. 字段注册

```javascript
const monetaryField = {
    component: MonetaryField,
    supportedOptions: [
        {
            label: _t("Hide symbol"),
            name: "no_symbol",
            type: "boolean",
        },
        {
            label: _t("Currency"),
            name: "currency_field",
            type: "field",
            availableTypes: ["many2one"],
        },
    ],
    supportedTypes: ["monetary", "float"],
    displayName: _t("Monetary"),
    extractProps: ({ attrs, options }) => ({
        currencyField: options.currency_field,
        inputType: attrs.type,
        useFieldDigits: options.field_digits,
        hideSymbol: options.no_symbol,
        placeholder: attrs.placeholder,
    }),
};
```

**注册功能**:
- **组件注册**: 注册货币字段组件
- **选项配置**: 支持符号隐藏和货币字段配置
- **类型支持**: 支持monetary和float类型
- **属性提取**: 提取各种配置属性

## 使用场景

### 1. 货币字段管理器

```javascript
// 货币字段管理器
class MonetaryFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置货币字段配置
        this.monetaryConfig = {
            enableCurrencyConversion: false,
            enableMultiCurrency: true,
            enableRealTimeRates: false,
            enableFormatting: true,
            enableValidation: true,
            enableCalculation: true,
            enableRounding: true,
            enableNegativeValues: true
        };
        
        // 设置货币显示选项
        this.displayOptions = {
            showSymbol: true,
            symbolPosition: 'before', // 'before', 'after'
            useThousandSeparator: true,
            thousandSeparator: ',',
            decimalSeparator: '.',
            enableColorCoding: false,
            positiveColor: '#000000',
            negativeColor: '#ff0000',
            zeroColor: '#666666'
        };
        
        // 设置精度配置
        this.precisionConfig = {
            useFieldDigits: false,
            useCurrencyDigits: true,
            defaultDigits: 2,
            maxDigits: 6,
            enableDynamicPrecision: false
        };
        
        // 设置验证规则
        this.validationRules = {
            enableRangeValidation: false,
            minValue: null,
            maxValue: null,
            enableNegativeCheck: false,
            allowNegative: true,
            enableZeroCheck: false,
            allowZero: true
        };
        
        // 设置货币统计
        this.monetaryStatistics = {
            totalFields: 0,
            totalAmount: 0,
            currencyDistribution: new Map(),
            averageAmount: 0,
            maxAmount: 0,
            minAmount: Number.MAX_SAFE_INTEGER,
            negativeAmounts: 0,
            zeroAmounts: 0
        };
        
        this.initializeMonetarySystem();
    }
    
    // 初始化货币系统
    initializeMonetarySystem() {
        // 创建增强的货币字段
        this.createEnhancedMonetaryField();
        
        // 设置货币服务
        this.setupCurrencyService();
        
        // 设置格式化系统
        this.setupFormattingSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
    }
    
    // 创建增强的货币字段
    createEnhancedMonetaryField() {
        const originalField = MonetaryField;
        
        this.EnhancedMonetaryField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加货币功能
                this.addCurrencyFeatures();
                
                // 添加计算功能
                this.addCalculationFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    originalCurrency: null,
                    convertedAmount: null,
                    exchangeRate: 1,
                    validationErrors: [],
                    calculationHistory: [],
                    isCalculating: false,
                    lastUpdateTime: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的格式化值
                this.enhancedFormattedValue = () => {
                    const value = this.value;
                    
                    if (value === null || value === undefined) {
                        return '';
                    }
                    
                    // 验证金额
                    this.validateAmount(value);
                    
                    // 应用颜色编码
                    const formattedValue = this.getFormattedValue();
                    
                    if (this.displayOptions.enableColorCoding) {
                        return this.applyColorCoding(formattedValue, value);
                    }
                    
                    return formattedValue;
                };
                
                // 获取格式化值
                this.getFormattedValue = () => {
                    const value = this.value;
                    
                    if (this.props.inputType === "number" && !this.props.readonly && value) {
                        return value;
                    }
                    
                    return formatMonetary(value, {
                        digits: this.enhancedCurrencyDigits,
                        currencyId: this.currencyId,
                        noSymbol: !this.props.readonly || this.props.hideSymbol,
                        thousandSep: this.displayOptions.thousandSeparator,
                        decimalPoint: this.displayOptions.decimalSeparator
                    });
                };
                
                // 增强的货币精度
                this.enhancedCurrencyDigits = () => {
                    if (this.precisionConfig.useFieldDigits && this.props.useFieldDigits) {
                        return this.props.record.fields[this.props.name].digits;
                    }
                    
                    if (this.precisionConfig.useCurrencyDigits && this.currency) {
                        return this.currency.digits;
                    }
                    
                    return this.precisionConfig.defaultDigits;
                };
                
                // 验证金额
                this.validateAmount = (amount) => {
                    const errors = [];
                    
                    if (typeof amount !== 'number') {
                        errors.push('Amount must be a number');
                    }
                    
                    if (this.validationRules.enableRangeValidation) {
                        if (this.validationRules.minValue !== null && amount < this.validationRules.minValue) {
                            errors.push(`Amount must be at least ${this.validationRules.minValue}`);
                        }
                        if (this.validationRules.maxValue !== null && amount > this.validationRules.maxValue) {
                            errors.push(`Amount must not exceed ${this.validationRules.maxValue}`);
                        }
                    }
                    
                    if (this.validationRules.enableNegativeCheck && !this.validationRules.allowNegative && amount < 0) {
                        errors.push('Negative amounts are not allowed');
                    }
                    
                    if (this.validationRules.enableZeroCheck && !this.validationRules.allowZero && amount === 0) {
                        errors.push('Zero amounts are not allowed');
                    }
                    
                    this.enhancedState.validationErrors = errors;
                    
                    if (errors.length > 0) {
                        throw new Error(errors.join(', '));
                    }
                };
                
                // 应用颜色编码
                this.applyColorCoding = (formattedValue, amount) => {
                    let color;
                    
                    if (amount > 0) {
                        color = this.displayOptions.positiveColor;
                    } else if (amount < 0) {
                        color = this.displayOptions.negativeColor;
                    } else {
                        color = this.displayOptions.zeroColor;
                    }
                    
                    return `<span style="color: ${color}">${formattedValue}</span>`;
                };
                
                // 货币转换
                this.convertCurrency = async (targetCurrencyId, amount = null) => {
                    if (!this.monetaryConfig.enableCurrencyConversion) {
                        return amount || this.value;
                    }
                    
                    const sourceAmount = amount || this.value;
                    const sourceCurrencyId = this.currencyId;
                    
                    if (sourceCurrencyId === targetCurrencyId) {
                        return sourceAmount;
                    }
                    
                    try {
                        // 获取汇率
                        const rate = await this.getExchangeRate(sourceCurrencyId, targetCurrencyId);
                        
                        // 计算转换金额
                        const convertedAmount = sourceAmount * rate;
                        
                        // 记录转换
                        this.enhancedState.originalCurrency = sourceCurrencyId;
                        this.enhancedState.convertedAmount = convertedAmount;
                        this.enhancedState.exchangeRate = rate;
                        
                        return convertedAmount;
                        
                    } catch (error) {
                        console.error('Currency conversion failed:', error);
                        return sourceAmount;
                    }
                };
                
                // 获取汇率
                this.getExchangeRate = async (fromCurrency, toCurrency) => {
                    if (this.monetaryConfig.enableRealTimeRates) {
                        // 实时汇率获取
                        return await this.fetchRealTimeRate(fromCurrency, toCurrency);
                    } else {
                        // 使用系统汇率
                        return await this.getSystemRate(fromCurrency, toCurrency);
                    }
                };
                
                // 获取系统汇率
                this.getSystemRate = async (fromCurrency, toCurrency) => {
                    try {
                        const rate = await this.orm.call('res.currency', 'get_conversion_rate', [
                            fromCurrency, toCurrency, this.props.record.data.company_id || 1
                        ]);
                        return rate || 1;
                    } catch (error) {
                        console.warn('Failed to get system exchange rate:', error);
                        return 1;
                    }
                };
                
                // 计算金额
                this.calculateAmount = (operation, operand) => {
                    if (!this.monetaryConfig.enableCalculation) {
                        return this.value;
                    }
                    
                    const currentValue = this.value || 0;
                    let result;
                    
                    switch (operation) {
                        case 'add':
                            result = currentValue + operand;
                            break;
                        case 'subtract':
                            result = currentValue - operand;
                            break;
                        case 'multiply':
                            result = currentValue * operand;
                            break;
                        case 'divide':
                            result = operand !== 0 ? currentValue / operand : currentValue;
                            break;
                        case 'percentage':
                            result = currentValue * (operand / 100);
                            break;
                        default:
                            result = currentValue;
                    }
                    
                    // 四舍五入
                    if (this.monetaryConfig.enableRounding) {
                        const digits = this.enhancedCurrencyDigits();
                        result = Math.round(result * Math.pow(10, digits)) / Math.pow(10, digits);
                    }
                    
                    // 记录计算历史
                    this.addToCalculationHistory(operation, operand, currentValue, result);
                    
                    return result;
                };
                
                // 添加到计算历史
                this.addToCalculationHistory = (operation, operand, originalValue, result) => {
                    const historyItem = {
                        operation,
                        operand,
                        originalValue,
                        result,
                        timestamp: new Date(),
                        currency: this.currencyId
                    };
                    
                    this.enhancedState.calculationHistory.unshift(historyItem);
                    
                    // 限制历史大小
                    if (this.enhancedState.calculationHistory.length > 20) {
                        this.enhancedState.calculationHistory.pop();
                    }
                };
                
                // 获取货币信息
                this.getCurrencyInfo = () => {
                    return {
                        id: this.currencyId,
                        symbol: this.currencySymbol,
                        digits: this.enhancedCurrencyDigits(),
                        currency: this.currency,
                        isValid: this.currency !== null
                    };
                };
                
                // 获取金额信息
                this.getAmountInfo = () => {
                    const value = this.value;
                    
                    return {
                        value: value,
                        formattedValue: this.enhancedFormattedValue(),
                        currency: this.getCurrencyInfo(),
                        isValid: this.enhancedState.validationErrors.length === 0,
                        validationErrors: this.enhancedState.validationErrors,
                        isNegative: value < 0,
                        isZero: value === 0,
                        isPositive: value > 0
                    };
                };
                
                // 记录货币统计
                this.recordMonetaryStatistics = () => {
                    const value = this.value;
                    if (typeof value !== 'number') return;
                    
                    this.monetaryStatistics.totalAmount += Math.abs(value);
                    
                    // 记录货币分布
                    const currencyId = this.currencyId;
                    if (currencyId) {
                        const count = this.monetaryStatistics.currencyDistribution.get(currencyId) || 0;
                        this.monetaryStatistics.currencyDistribution.set(currencyId, count + 1);
                    }
                    
                    // 更新最大最小值
                    if (value > this.monetaryStatistics.maxAmount) {
                        this.monetaryStatistics.maxAmount = value;
                    }
                    if (value < this.monetaryStatistics.minAmount) {
                        this.monetaryStatistics.minAmount = value;
                    }
                    
                    // 记录特殊值
                    if (value < 0) {
                        this.monetaryStatistics.negativeAmounts++;
                    } else if (value === 0) {
                        this.monetaryStatistics.zeroAmounts++;
                    }
                    
                    // 更新平均值
                    this.monetaryStatistics.averageAmount = 
                        this.monetaryStatistics.totalAmount / this.monetaryStatistics.totalFields;
                };
            }
            
            addCurrencyFeatures() {
                // 货币功能
                this.currencyManager = {
                    enabled: this.monetaryConfig.enableMultiCurrency,
                    convert: (targetCurrency, amount) => this.convertCurrency(targetCurrency, amount),
                    getInfo: () => this.getCurrencyInfo(),
                    getRate: (from, to) => this.getExchangeRate(from, to)
                };
            }
            
            addCalculationFeatures() {
                // 计算功能
                this.calculationManager = {
                    enabled: this.monetaryConfig.enableCalculation,
                    calculate: (operation, operand) => this.calculateAmount(operation, operand),
                    getHistory: () => this.enhancedState.calculationHistory,
                    clearHistory: () => { this.enhancedState.calculationHistory = []; }
                };
            }
            
            // 重写原始方法
            get formattedValue() {
                return this.enhancedFormattedValue();
            }
            
            get currencyDigits() {
                return this.enhancedCurrencyDigits();
            }
        };
    }
    
    // 设置货币服务
    setupCurrencyService() {
        this.currencyServiceConfig = {
            enabled: this.monetaryConfig.enableMultiCurrency,
            realTimeRates: this.monetaryConfig.enableRealTimeRates
        };
    }
    
    // 设置格式化系统
    setupFormattingSystem() {
        this.formattingSystemConfig = {
            enabled: this.monetaryConfig.enableFormatting,
            options: this.displayOptions
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.monetaryConfig.enableValidation,
            rules: this.validationRules
        };
    }
    
    // 创建货币字段
    createMonetaryField(props) {
        const field = new this.EnhancedMonetaryField(props);
        this.monetaryStatistics.totalFields++;
        return field;
    }
    
    // 批量创建货币字段
    batchCreateMonetaryFields(propsArray) {
        const results = [];
        
        for (const props of propsArray) {
            try {
                const field = this.createMonetaryField(props);
                results.push({ field, success: true, props });
            } catch (error) {
                results.push({ field: null, success: false, error, props });
            }
        }
        
        return results;
    }
    
    // 获取热门货币
    getPopularCurrencies(limit = 10) {
        const sorted = Array.from(this.monetaryStatistics.currencyDistribution.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, limit);
        
        return sorted.map(([currencyId, count]) => ({ currencyId, count }));
    }
    
    // 获取货币统计
    getMonetaryStatistics() {
        return {
            ...this.monetaryStatistics,
            currencyVariety: this.monetaryStatistics.currencyDistribution.size,
            averageAmountPerField: this.monetaryStatistics.totalAmount / Math.max(this.monetaryStatistics.totalFields, 1),
            negativeRate: this.monetaryStatistics.negativeAmounts / Math.max(this.monetaryStatistics.totalFields, 1) * 100,
            zeroRate: this.monetaryStatistics.zeroAmounts / Math.max(this.monetaryStatistics.totalFields, 1) * 100,
            amountRange: this.monetaryStatistics.maxAmount - this.monetaryStatistics.minAmount
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理货币分布
        this.monetaryStatistics.currencyDistribution.clear();
        
        // 重置统计
        this.monetaryStatistics = {
            totalFields: 0,
            totalAmount: 0,
            currencyDistribution: new Map(),
            averageAmount: 0,
            maxAmount: 0,
            minAmount: Number.MAX_SAFE_INTEGER,
            negativeAmounts: 0,
            zeroAmounts: 0
        };
    }
}

// 使用示例
const monetaryManager = new MonetaryFieldManager();

// 创建货币字段
const monetaryField = monetaryManager.createMonetaryField({
    name: 'amount',
    record: {
        data: { 
            amount: 1234.56,
            currency_id: [1, 'USD']
        },
        fields: { 
            amount: { 
                type: 'monetary',
                currency_field: 'currency_id'
            }
        }
    },
    currencyField: 'currency_id',
    hideSymbol: false
});

// 获取统计信息
const stats = monetaryManager.getMonetaryStatistics();
console.log('Monetary field statistics:', stats);
```

## 技术特点

### 1. 货币处理
- **多货币支持**: 支持多种货币显示
- **符号管理**: 智能的货币符号管理
- **精度控制**: 灵活的精度控制机制
- **格式化**: 完整的货币格式化功能

### 2. 输入管理
- **输入钩子**: 使用输入字段钩子管理输入
- **数字键盘**: 支持数字键盘输入
- **解析器**: 使用货币解析器解析输入
- **状态同步**: 实时同步输入状态

### 3. 显示控制
- **条件格式**: 根据条件选择格式化方式
- **符号控制**: 灵活控制符号显示
- **只读模式**: 只读模式的特殊处理
- **占位符**: 支持占位符显示

### 4. 服务集成
- **货币服务**: 集成货币服务获取货币信息
- **格式化服务**: 使用格式化服务处理显示
- **解析服务**: 使用解析服务处理输入
- **字符串工具**: 集成字符串工具

## 设计模式

### 1. 组件模式 (Component Pattern)
- **UI组件**: 封装货币字段UI
- **状态管理**: 管理货币字段状态
- **事件处理**: 处理输入事件

### 2. 服务模式 (Service Pattern)
- **货币服务**: 使用货币服务获取信息
- **格式化服务**: 使用格式化服务
- **解析服务**: 使用解析服务

### 3. 策略模式 (Strategy Pattern)
- **格式化策略**: 不同的格式化策略
- **精度策略**: 不同的精度控制策略
- **显示策略**: 不同的显示策略

### 4. 观察者模式 (Observer Pattern)
- **状态观察**: 观察字段状态变化
- **输入观察**: 观察输入变化
- **货币观察**: 观察货币变化

## 注意事项

1. **精度处理**: 注意浮点数精度问题
2. **货币一致性**: 确保货币数据的一致性
3. **格式化性能**: 避免频繁的格式化操作
4. **用户体验**: 提供清晰的货币显示

## 扩展建议

1. **汇率转换**: 支持实时汇率转换
2. **计算功能**: 添加货币计算功能
3. **历史记录**: 记录货币变更历史
4. **批量操作**: 支持批量货币操作
5. **图表显示**: 支持货币数据图表显示

该货币字段为Odoo Web客户端提供了完整的货币数据处理功能，通过智能的货币管理和精确的格式化确保了财务数据的正确显示和处理。

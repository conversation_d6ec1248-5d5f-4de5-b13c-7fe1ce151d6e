# UrlField - URL字段

## 概述

`url_field.js` 是 Odoo Web 客户端的URL字段组件，负责处理URL类型的输入和显示。该模块包含75行代码，是一个功能简洁的URL处理组件，专门用于处理char类型的URL字段，具备URL格式化、协议自动添加、网站路径模式、链接显示等特性，是URL输入和链接管理的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/url/url_field.js`
- **行数**: 75
- **模块**: `@web/views/fields/url/url_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                    // 注册表
'@web/core/l10n/translation'            // 翻译服务
'@web/views/fields/input_field_hook'    // 输入字段钩子
'@web/views/fields/standard_field_props' // 标准字段属性
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 基础URL字段组件

```javascript
const UrlField = class UrlField extends Component {
    static template = "web.UrlField";
    static props = {
        ...standardFieldProps,
        placeholder: { type: String, optional: true },
        text: { type: String, optional: true },
        websitePath: { type: Boolean, optional: true },
    };

    setup() {
        useInputField({ getValue: () => this.value });
    }

    get value() {
        return this.props.record.data[this.props.name] || "";
    }

    get formattedHref() {
        let value = this.props.record.data[this.props.name];
        if (value && !this.props.websitePath) {
            const regex = /^((ftp|http)s?:\/)?\//i; // http(s)://... ftp(s)://... /...
            value = !regex.test(value) ? `http://${value}` : value;
        }
        return value;
    }
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **占位符**: 支持placeholder配置占位符文本
- **显示文本**: 支持text配置链接显示文本
- **网站路径**: 支持websitePath配置网站路径模式
- **输入钩子**: 使用输入字段钩子
- **URL格式化**: 自动格式化URL链接

### 2. URL格式化逻辑

```javascript
get formattedHref() {
    let value = this.props.record.data[this.props.name];
    if (value && !this.props.websitePath) {
        const regex = /^((ftp|http)s?:\/)?\//i; // http(s)://... ftp(s)://... /...
        value = !regex.test(value) ? `http://${value}` : value;
    }
    return value;
}
```

**格式化功能**:
- **协议检测**: 检测URL是否包含协议
- **自动添加**: 自动添加http://协议前缀
- **网站路径**: 网站路径模式不添加协议
- **正则匹配**: 使用正则表达式匹配协议格式
- **支持协议**: 支持http、https、ftp、ftps协议

### 3. 表单URL字段组件

```javascript
class FormUrlField extends UrlField {
    static template = "web.FormUrlField";
}

const formUrlField = {
    ...urlField,
    component: FormUrlField,
};

registry.category("fields").add("form.url", formUrlField);
```

**表单组件特性**:
- **继承基类**: 继承基础URL字段的所有功能
- **专用模板**: 使用FormUrlField专用模板
- **配置继承**: 继承基础字段的所有配置
- **独立注册**: 独立注册为"form.url"字段类型

### 4. 字段注册

```javascript
const urlField = {
    component: UrlField,
    displayName: _t("URL"),
    supportedOptions: [
        {
            label: _t("Is a website path"),
            name: "website_path",
            type: "boolean",
            help: _t("If True, the url will be used as it is, without any prefix added to it."),
        },
    ],
    supportedTypes: ["char"],
    extractProps: ({ attrs, options }) => ({
        text: attrs.text,
        websitePath: options.website_path,
        placeholder: attrs.placeholder,
    }),
};

registry.category("fields").add("url", urlField);
```

**注册功能**:
- **组件注册**: 注册URL字段组件
- **显示名称**: 设置为"URL"
- **支持选项**: 支持网站路径选项配置
- **类型支持**: 支持char类型
- **属性提取**: 提取文本、网站路径、占位符等属性

## 使用场景

### 1. URL字段管理器

```javascript
// URL字段管理器
class UrlFieldManager {
    constructor() {
        this.setupManager();
    }

    setupManager() {
        // 设置URL字段配置
        this.urlFieldConfig = {
            enableAutoProtocol: true,
            enableValidation: true,
            enableLinkPreview: false,
            enableShortening: false,
            enableTracking: false,
            enableSecurity: true,
            enableFormatting: true,
            enableHistory: false
        };

        // 设置支持的协议
        this.supportedProtocols = new Map([
            ['http', { secure: false, port: 80, description: 'HTTP Protocol' }],
            ['https', { secure: true, port: 443, description: 'HTTPS Protocol' }],
            ['ftp', { secure: false, port: 21, description: 'FTP Protocol' }],
            ['ftps', { secure: true, port: 990, description: 'FTPS Protocol' }],
            ['mailto', { secure: false, port: null, description: 'Email Protocol' }],
            ['tel', { secure: false, port: null, description: 'Telephone Protocol' }]
        ]);

        // 设置验证规则
        this.validationRules = {
            enableUrlValidation: true,
            enableDomainValidation: false,
            enableProtocolValidation: true,
            enableLengthValidation: true,
            maxLength: 2048,
            minLength: 1,
            allowedDomains: [],
            blockedDomains: [],
            requireHttps: false
        };

        // 设置格式化选项
        this.formattingOptions = {
            enableAutoProtocol: true,
            defaultProtocol: 'https',
            enableDomainNormalization: true,
            enablePathNormalization: false,
            enableQueryNormalization: false,
            enableFragmentRemoval: false
        };

        // 设置URL统计
        this.urlStatistics = {
            totalUrlFields: 0,
            totalUrls: 0,
            urlsByProtocol: new Map(),
            urlsByDomain: new Map(),
            validationErrors: 0,
            formattingOperations: 0,
            mostUsedProtocol: null,
            mostUsedDomain: null
        };

        this.initializeUrlFieldSystem();
    }

    // 初始化URL字段系统
    initializeUrlFieldSystem() {
        // 创建增强的URL字段
        this.createEnhancedUrlField();

        // 设置验证系统
        this.setupValidationSystem();

        // 设置格式化系统
        this.setupFormattingSystem();

        // 设置安全系统
        this.setupSecuritySystem();
    }

    // 创建增强的URL字段
    createEnhancedUrlField() {
        const originalField = UrlField;

        this.EnhancedUrlField = class extends originalField {
            setup() {
                super.setup();

                // 添加增强功能
                this.addEnhancedFeatures();

                // 添加验证功能
                this.addValidationFeatures();

                // 添加格式化功能
                this.addFormattingFeatures();
            }

            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isValid: true,
                    validationErrors: [],
                    originalValue: '',
                    formattedValue: '',
                    urlInfo: null,
                    lastValidationTime: null,
                    isExternal: false
                };

                // 添加增强方法
                this.addEnhancedMethods();
            }

            addEnhancedMethods() {
                // 增强的URL格式化
                this.enhancedFormattedHref = () => {
                    let value = this.props.record.data[this.props.name];
                    if (!value) {
                        return '';
                    }

                    this.enhancedState.originalValue = value;

                    try {
                        // 验证URL
                        this.validateUrl(value);

                        // 格式化URL
                        const formattedUrl = this.formatUrl(value);

                        // 分析URL
                        this.analyzeUrl(formattedUrl);

                        // 记录统计
                        this.recordUrlStatistics(formattedUrl);

                        this.enhancedState.formattedValue = formattedUrl;
                        return formattedUrl;

                    } catch (error) {
                        this.handleUrlError(error);
                        return value;
                    }
                };

                // 验证URL
                this.validateUrl = (url) => {
                    const errors = [];

                    // 长度验证
                    if (this.validationRules.enableLengthValidation) {
                        if (url.length < this.validationRules.minLength) {
                            errors.push(`URL must be at least ${this.validationRules.minLength} characters`);
                        }
                        if (url.length > this.validationRules.maxLength) {
                            errors.push(`URL must not exceed ${this.validationRules.maxLength} characters`);
                        }
                    }

                    // URL格式验证
                    if (this.validationRules.enableUrlValidation) {
                        if (!this.isValidUrl(url)) {
                            errors.push('Invalid URL format');
                        }
                    }

                    // 协议验证
                    if (this.validationRules.enableProtocolValidation) {
                        const protocol = this.extractProtocol(url);
                        if (protocol && !this.supportedProtocols.has(protocol)) {
                            errors.push(`Unsupported protocol: ${protocol}`);
                        }
                    }

                    // HTTPS要求
                    if (this.validationRules.requireHttps) {
                        const protocol = this.extractProtocol(url);
                        if (protocol && protocol !== 'https') {
                            errors.push('HTTPS protocol is required');
                        }
                    }

                    // 域名验证
                    if (this.validationRules.enableDomainValidation) {
                        const domain = this.extractDomain(url);
                        if (domain) {
                            if (this.validationRules.allowedDomains.length > 0 &&
                                !this.validationRules.allowedDomains.includes(domain)) {
                                errors.push(`Domain ${domain} is not allowed`);
                            }

                            if (this.validationRules.blockedDomains.includes(domain)) {
                                errors.push(`Domain ${domain} is blocked`);
                            }
                        }
                    }

                    this.enhancedState.validationErrors = errors;
                    this.enhancedState.isValid = errors.length === 0;
                    this.enhancedState.lastValidationTime = new Date();

                    if (errors.length > 0) {
                        this.urlStatistics.validationErrors++;
                        throw new Error(errors.join(', '));
                    }
                };

                // 检查URL有效性
                this.isValidUrl = (url) => {
                    try {
                        // 如果没有协议，添加默认协议进行验证
                        const testUrl = this.hasProtocol(url) ? url : `http://${url}`;
                        new URL(testUrl);
                        return true;
                    } catch {
                        return false;
                    }
                };

                // 检查是否有协议
                this.hasProtocol = (url) => {
                    const regex = /^[a-zA-Z][a-zA-Z0-9+.-]*:/;
                    return regex.test(url);
                };

                // 提取协议
                this.extractProtocol = (url) => {
                    try {
                        const testUrl = this.hasProtocol(url) ? url : `http://${url}`;
                        return new URL(testUrl).protocol.slice(0, -1);
                    } catch {
                        return null;
                    }
                };

                // 提取域名
                this.extractDomain = (url) => {
                    try {
                        const testUrl = this.hasProtocol(url) ? url : `http://${url}`;
                        return new URL(testUrl).hostname;
                    } catch {
                        return null;
                    }
                };

                // 格式化URL
                this.formatUrl = (url) => {
                    if (!url) {
                        return '';
                    }

                    let formattedUrl = url.trim();

                    // 网站路径模式
                    if (this.props.websitePath) {
                        return formattedUrl;
                    }

                    // 自动添加协议
                    if (this.formattingOptions.enableAutoProtocol && !this.hasProtocol(formattedUrl)) {
                        const defaultProtocol = this.formattingOptions.defaultProtocol || 'https';
                        formattedUrl = `${defaultProtocol}://${formattedUrl}`;
                    }

                    // 域名标准化
                    if (this.formattingOptions.enableDomainNormalization) {
                        formattedUrl = this.normalizeDomain(formattedUrl);
                    }

                    // 路径标准化
                    if (this.formattingOptions.enablePathNormalization) {
                        formattedUrl = this.normalizePath(formattedUrl);
                    }

                    // 查询参数标准化
                    if (this.formattingOptions.enableQueryNormalization) {
                        formattedUrl = this.normalizeQuery(formattedUrl);
                    }

                    // 移除片段
                    if (this.formattingOptions.enableFragmentRemoval) {
                        formattedUrl = this.removeFragment(formattedUrl);
                    }

                    this.urlStatistics.formattingOperations++;
                    return formattedUrl;
                };

                // 标准化域名
                this.normalizeDomain = (url) => {
                    try {
                        const urlObj = new URL(url);
                        urlObj.hostname = urlObj.hostname.toLowerCase();
                        return urlObj.toString();
                    } catch {
                        return url;
                    }
                };

                // 标准化路径
                this.normalizePath = (url) => {
                    try {
                        const urlObj = new URL(url);
                        // 移除重复的斜杠
                        urlObj.pathname = urlObj.pathname.replace(/\/+/g, '/');
                        return urlObj.toString();
                    } catch {
                        return url;
                    }
                };

                // 标准化查询参数
                this.normalizeQuery = (url) => {
                    try {
                        const urlObj = new URL(url);
                        // 排序查询参数
                        urlObj.searchParams.sort();
                        return urlObj.toString();
                    } catch {
                        return url;
                    }
                };

                // 移除片段
                this.removeFragment = (url) => {
                    try {
                        const urlObj = new URL(url);
                        urlObj.hash = '';
                        return urlObj.toString();
                    } catch {
                        return url;
                    }
                };

                // 分析URL
                this.analyzeUrl = (url) => {
                    try {
                        const urlObj = new URL(url);

                        this.enhancedState.urlInfo = {
                            protocol: urlObj.protocol.slice(0, -1),
                            hostname: urlObj.hostname,
                            port: urlObj.port,
                            pathname: urlObj.pathname,
                            search: urlObj.search,
                            hash: urlObj.hash,
                            isSecure: urlObj.protocol === 'https:',
                            isExternal: this.isExternalUrl(urlObj.hostname)
                        };

                        this.enhancedState.isExternal = this.enhancedState.urlInfo.isExternal;

                    } catch (error) {
                        this.enhancedState.urlInfo = null;
                    }
                };

                // 检查是否为外部URL
                this.isExternalUrl = (hostname) => {
                    // 简化的外部URL检查
                    const currentHostname = window.location.hostname;
                    return hostname !== currentHostname && hostname !== 'localhost' && hostname !== '127.0.0.1';
                };

                // 获取URL信息
                this.getUrlInfo = () => {
                    return {
                        originalValue: this.enhancedState.originalValue,
                        formattedValue: this.enhancedState.formattedValue,
                        isValid: this.enhancedState.isValid,
                        validationErrors: this.enhancedState.validationErrors,
                        urlInfo: this.enhancedState.urlInfo,
                        isExternal: this.enhancedState.isExternal,
                        lastValidationTime: this.enhancedState.lastValidationTime,
                        websitePath: this.props.websitePath
                    };
                };

                // 处理URL错误
                this.handleUrlError = (error) => {
                    console.error('URL processing error:', error);
                    this.enhancedState.validationErrors.push(error.message);
                    this.enhancedState.isValid = false;
                };

                // 记录URL统计
                this.recordUrlStatistics = (url) => {
                    this.urlStatistics.totalUrls++;

                    // 记录协议分布
                    const protocol = this.extractProtocol(url);
                    if (protocol) {
                        const count = this.urlStatistics.urlsByProtocol.get(protocol) || 0;
                        this.urlStatistics.urlsByProtocol.set(protocol, count + 1);
                    }

                    // 记录域名分布
                    const domain = this.extractDomain(url);
                    if (domain) {
                        const count = this.urlStatistics.urlsByDomain.get(domain) || 0;
                        this.urlStatistics.urlsByDomain.set(domain, count + 1);
                    }

                    this.updateMostUsed();
                };

                // 更新最常用统计
                this.updateMostUsed = () => {
                    // 最常用协议
                    let maxProtocolCount = 0;
                    let mostUsedProtocol = null;
                    for (const [protocol, count] of this.urlStatistics.urlsByProtocol.entries()) {
                        if (count > maxProtocolCount) {
                            maxProtocolCount = count;
                            mostUsedProtocol = protocol;
                        }
                    }
                    this.urlStatistics.mostUsedProtocol = mostUsedProtocol;

                    // 最常用域名
                    let maxDomainCount = 0;
                    let mostUsedDomain = null;
                    for (const [domain, count] of this.urlStatistics.urlsByDomain.entries()) {
                        if (count > maxDomainCount) {
                            maxDomainCount = count;
                            mostUsedDomain = domain;
                        }
                    }
                    this.urlStatistics.mostUsedDomain = mostUsedDomain;
                };
            }

            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.urlFieldConfig.enableValidation,
                    validate: (url) => this.validateUrl(url),
                    isValid: () => this.enhancedState.isValid,
                    getErrors: () => this.enhancedState.validationErrors
                };
            }

            addFormattingFeatures() {
                // 格式化功能
                this.formattingManager = {
                    enabled: this.urlFieldConfig.enableFormatting,
                    format: (url) => this.formatUrl(url),
                    analyze: (url) => this.analyzeUrl(url),
                    getInfo: () => this.getUrlInfo()
                };
            }

            // 重写原始方法
            get formattedHref() {
                return this.enhancedFormattedHref();
            }
        };
    }

    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.urlFieldConfig.enableValidation,
            rules: this.validationRules
        };
    }

    // 设置格式化系统
    setupFormattingSystem() {
        this.formattingSystemConfig = {
            enabled: this.urlFieldConfig.enableFormatting,
            options: this.formattingOptions
        };
    }

    // 设置安全系统
    setupSecuritySystem() {
        this.securitySystemConfig = {
            enabled: this.urlFieldConfig.enableSecurity,
            requireHttps: this.validationRules.requireHttps,
            blockedDomains: this.validationRules.blockedDomains
        };
    }

    // 创建URL字段
    createUrlField(props) {
        const field = new this.EnhancedUrlField(props);
        this.urlStatistics.totalUrlFields++;
        return field;
    }

    // 注册支持的协议
    registerProtocol(protocol, config) {
        this.supportedProtocols.set(protocol, config);
    }

    // 获取URL统计
    getUrlStatistics() {
        return {
            ...this.urlStatistics,
            protocolVariety: this.urlStatistics.urlsByProtocol.size,
            domainVariety: this.urlStatistics.urlsByDomain.size,
            errorRate: (this.urlStatistics.validationErrors / Math.max(this.urlStatistics.totalUrls, 1)) * 100,
            formattingRate: (this.urlStatistics.formattingOperations / Math.max(this.urlStatistics.totalUrls, 1)) * 100,
            supportedProtocolCount: this.supportedProtocols.size
        };
    }

    // 销毁管理器
    destroy() {
        // 清理协议
        this.supportedProtocols.clear();

        // 清理统计
        this.urlStatistics.urlsByProtocol.clear();
        this.urlStatistics.urlsByDomain.clear();

        // 重置统计
        this.urlStatistics = {
            totalUrlFields: 0,
            totalUrls: 0,
            urlsByProtocol: new Map(),
            urlsByDomain: new Map(),
            validationErrors: 0,
            formattingOperations: 0,
            mostUsedProtocol: null,
            mostUsedDomain: null
        };
    }
}

// 使用示例
const urlFieldManager = new UrlFieldManager();

// 创建URL字段
const urlField = urlFieldManager.createUrlField({
    name: 'website',
    record: {
        data: {
            website: 'example.com'
        },
        fields: {
            website: {
                type: 'char',
                string: 'Website'
            }
        }
    },
    placeholder: 'Enter website URL...',
    websitePath: false
});

// 注册自定义协议
urlFieldManager.registerProtocol('custom', {
    secure: false,
    port: 8080,
    description: 'Custom Protocol'
});

// 获取统计信息
const stats = urlFieldManager.getUrlStatistics();
console.log('URL field statistics:', stats);
```

## 技术特点

### 1. 智能格式化
- **协议检测**: 智能检测URL协议
- **自动添加**: 自动添加缺失的协议前缀
- **网站路径**: 支持网站路径模式
- **格式标准化**: 标准化URL格式

### 2. 双模板支持
- **基础模板**: 基础URL字段模板
- **表单模板**: 专用的表单URL字段模板
- **模板继承**: 表单组件继承基础组件
- **独立注册**: 两种模板独立注册

### 3. 灵活配置
- **占位符**: 支持自定义占位符
- **显示文本**: 支持自定义链接文本
- **网站路径**: 支持网站路径模式配置
- **选项配置**: 丰富的字段选项配置

### 4. 简洁设计
- **最小代码**: 极简的代码实现
- **专注功能**: 专注于URL处理
- **易于使用**: 简单的配置和使用
- **高效渲染**: 高效的渲染性能

## 设计模式

### 1. 模板方法模式 (Template Method Pattern)
- **格式化模板**: 定义URL格式化的基本流程
- **验证模板**: 定义URL验证的基本流程
- **显示模板**: 定义URL显示的基本流程

### 2. 策略模式 (Strategy Pattern)
- **格式化策略**: 不同的URL格式化策略
- **验证策略**: 不同的URL验证策略
- **显示策略**: 不同的URL显示策略

### 3. 装饰器模式 (Decorator Pattern)
- **协议装饰**: 装饰URL协议
- **格式装饰**: 装饰URL格式
- **显示装饰**: 装饰URL显示

### 4. 工厂模式 (Factory Pattern)
- **字段工厂**: 创建不同类型的URL字段
- **模板工厂**: 创建不同的URL模板
- **验证器工厂**: 创建不同的URL验证器

## 注意事项

1. **安全考虑**: 验证URL的安全性
2. **协议支持**: 确保支持常用协议
3. **用户体验**: 提供友好的URL输入体验
4. **性能优化**: 避免频繁的URL验证

## 扩展建议

1. **链接预览**: 添加URL链接预览功能
2. **安全检查**: 增强URL安全检查
3. **短链接**: 支持短链接生成和解析
4. **批量验证**: 支持批量URL验证
5. **历史记录**: 记录URL访问历史

该URL字段为Odoo Web客户端提供了简洁而实用的URL处理功能，通过智能的协议检测和格式化确保了URL输入的便利性和链接的有效性。
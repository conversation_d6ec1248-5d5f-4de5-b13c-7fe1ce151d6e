# FieldTooltip - 字段工具提示

## 概述

`field_tooltip.js` 是 Odoo Web 客户端字段系统的工具提示模块，负责生成字段的详细信息用于工具提示显示。该模块包含38行代码，是一个简洁的工具函数模块，专门用于收集和格式化字段的元数据信息，具备字段信息收集、调试信息、JSON序列化、开发者工具等特性，是字段调试和开发的重要辅助工具。

## 文件信息
- **路径**: `/web/static/src/views/fields/field_tooltip.js`
- **行数**: 38
- **模块**: `@web/views/fields/field_tooltip`

## 依赖关系

```javascript
// 无外部依赖
// 纯工具函数模块
```

## 核心功能

### 1. 工具提示信息生成

```javascript
function getTooltipInfo(params) {
    let widgetDescription = undefined;
    if (params.fieldInfo.widget) {
        widgetDescription = params.fieldInfo.field.displayName;
    }

    const info = {
        viewMode: params.viewMode,
        resModel: params.resModel,
        debug: Bo<PERSON>an(odoo.debug),
        field: {
            name: params.field.name,
            label: params.field.string,
            help: params.fieldInfo.help ?? params.field.help,
            type: params.field.type,
            widget: params.fieldInfo.widget,
            widgetDescription,
            context: params.fieldInfo.context,
            domain: params.fieldInfo.domain || params.field.domain,
            invisible: params.fieldInfo.invisible,
            column_invisible: params.fieldInfo.column_invisible,
            readonly: params.fieldInfo.readonly,
            required: params.fieldInfo.required,
            changeDefault: params.field.change_default,
            relation: params.field.relation,
            model_field: params.field.model_field,
            selection: params.field.selection,
            default: params.field.default,
        },
    };
    return JSON.stringify(info);
}
```

**信息生成功能**:
- **字段基础信息**: 收集字段的基础元数据
- **视图信息**: 包含视图模式和模型信息
- **调试标识**: 标识是否处于调试模式
- **完整属性**: 收集字段的所有相关属性

### 2. 字段属性收集

**基础属性**:
- `name`: 字段名称
- `label`: 字段标签
- `help`: 帮助文本
- `type`: 字段类型

**显示属性**:
- `widget`: 使用的组件类型
- `widgetDescription`: 组件描述
- `invisible`: 是否不可见
- `column_invisible`: 列是否不可见

**行为属性**:
- `readonly`: 是否只读
- `required`: 是否必填
- `changeDefault`: 是否改变默认值

**关系属性**:
- `relation`: 关联模型
- `model_field`: 模型字段
- `selection`: 选择项
- `domain`: 域条件

### 3. 上下文信息

**视图上下文**:
- `viewMode`: 当前视图模式
- `resModel`: 资源模型名称
- `debug`: 调试模式标识

**字段上下文**:
- `context`: 字段上下文
- `domain`: 字段域条件
- `default`: 默认值

## 使用场景

### 1. 字段工具提示管理器

```javascript
// 字段工具提示管理器
class FieldTooltipManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置工具提示配置
        this.tooltipConfig = {
            enableDebugInfo: true,
            enableFieldInfo: true,
            enablePerformanceInfo: false,
            enableCustomInfo: true,
            maxTooltipLength: 2000,
            showInProduction: false,
            customInfoProviders: new Map()
        };
        
        // 设置信息提供者
        this.infoProviders = new Map();
        
        // 设置缓存系统
        this.tooltipCache = new Map();
        
        // 设置统计信息
        this.tooltipStatistics = {
            totalTooltips: 0,
            cacheHits: 0,
            cacheMisses: 0,
            averageGenerationTime: 0
        };
        
        this.initializeTooltipSystem();
    }
    
    // 初始化工具提示系统
    initializeTooltipSystem() {
        // 注册默认信息提供者
        this.registerDefaultProviders();
        
        // 设置缓存系统
        this.setupCacheSystem();
        
        // 设置性能监控
        this.setupPerformanceMonitoring();
        
        // 设置自定义扩展
        this.setupCustomExtensions();
    }
    
    // 注册默认信息提供者
    registerDefaultProviders() {
        // 基础字段信息提供者
        this.infoProviders.set('basic', {
            provider: this.getBasicFieldInfo.bind(this),
            priority: 100,
            description: 'Basic field information'
        });
        
        // 调试信息提供者
        this.infoProviders.set('debug', {
            provider: this.getDebugInfo.bind(this),
            priority: 90,
            description: 'Debug information'
        });
        
        // 性能信息提供者
        this.infoProviders.set('performance', {
            provider: this.getPerformanceInfo.bind(this),
            priority: 80,
            description: 'Performance information'
        });
        
        // 关系信息提供者
        this.infoProviders.set('relation', {
            provider: this.getRelationInfo.bind(this),
            priority: 70,
            description: 'Relation information'
        });
        
        // 验证信息提供者
        this.infoProviders.set('validation', {
            provider: this.getValidationInfo.bind(this),
            priority: 60,
            description: 'Validation information'
        });
    }
    
    // 增强的工具提示信息生成
    enhancedGetTooltipInfo(params) {
        const startTime = performance.now();
        
        try {
            // 检查缓存
            const cacheKey = this.generateCacheKey(params);
            if (this.tooltipCache.has(cacheKey)) {
                this.tooltipStatistics.cacheHits++;
                return this.tooltipCache.get(cacheKey);
            }
            
            // 生成基础信息
            const baseInfo = this.getBaseTooltipInfo(params);
            
            // 收集扩展信息
            const extendedInfo = this.collectExtendedInfo(params);
            
            // 合并信息
            const completeInfo = this.mergeTooltipInfo(baseInfo, extendedInfo);
            
            // 格式化信息
            const formattedInfo = this.formatTooltipInfo(completeInfo);
            
            // 验证信息长度
            const finalInfo = this.validateTooltipLength(formattedInfo);
            
            // 缓存结果
            this.tooltipCache.set(cacheKey, finalInfo);
            this.tooltipStatistics.cacheMisses++;
            
            // 记录性能
            const endTime = performance.now();
            this.recordPerformance(endTime - startTime);
            
            return finalInfo;
            
        } catch (error) {
            console.error('Tooltip generation error:', error);
            return this.getFallbackTooltip(params);
        }
    }
    
    // 获取基础工具提示信息
    getBaseTooltipInfo(params) {
        // 使用原始函数作为基础
        const baseInfo = JSON.parse(getTooltipInfo(params));
        
        // 添加时间戳
        baseInfo.timestamp = Date.now();
        
        // 添加生成器信息
        baseInfo.generator = {
            version: '2.0',
            enhanced: true
        };
        
        return baseInfo;
    }
    
    // 收集扩展信息
    collectExtendedInfo(params) {
        const extendedInfo = {};
        
        // 按优先级排序信息提供者
        const sortedProviders = Array.from(this.infoProviders.entries())
            .sort((a, b) => b[1].priority - a[1].priority);
        
        for (const [name, providerInfo] of sortedProviders) {
            try {
                const info = providerInfo.provider(params);
                if (info) {
                    extendedInfo[name] = info;
                }
            } catch (error) {
                console.error(`Error in info provider ${name}:`, error);
            }
        }
        
        return extendedInfo;
    }
    
    // 获取基础字段信息
    getBasicFieldInfo(params) {
        return {
            fieldPath: this.getFieldPath(params),
            fieldSize: this.getFieldSize(params),
            fieldConstraints: this.getFieldConstraints(params),
            fieldOptions: this.getFieldOptions(params)
        };
    }
    
    // 获取调试信息
    getDebugInfo(params) {
        if (!this.tooltipConfig.enableDebugInfo || !odoo.debug) {
            return null;
        }
        
        return {
            fieldDefinition: params.field,
            fieldInfo: params.fieldInfo,
            viewContext: {
                viewMode: params.viewMode,
                resModel: params.resModel
            },
            debugFlags: {
                assets: Boolean(odoo.debug === 'assets'),
                tests: Boolean(odoo.debug === 'tests')
            }
        };
    }
    
    // 获取性能信息
    getPerformanceInfo(params) {
        if (!this.tooltipConfig.enablePerformanceInfo) {
            return null;
        }
        
        return {
            renderTime: this.getFieldRenderTime(params),
            memoryUsage: this.getFieldMemoryUsage(params),
            cacheStatus: this.getFieldCacheStatus(params)
        };
    }
    
    // 获取关系信息
    getRelationInfo(params) {
        if (!params.field.relation) {
            return null;
        }
        
        return {
            relatedModel: params.field.relation,
            relationType: this.getRelationType(params.field),
            relationConstraints: this.getRelationConstraints(params),
            relationDomain: params.fieldInfo.domain || params.field.domain
        };
    }
    
    // 获取验证信息
    getValidationInfo(params) {
        return {
            validationRules: this.getValidationRules(params),
            constraints: this.getFieldConstraints(params),
            dependencies: this.getFieldDependencies(params)
        };
    }
    
    // 获取字段路径
    getFieldPath(params) {
        return `${params.resModel}.${params.field.name}`;
    }
    
    // 获取字段大小
    getFieldSize(params) {
        const sizeInfo = {};
        
        if (params.field.size) {
            sizeInfo.maxLength = params.field.size;
        }
        
        if (params.field.digits) {
            sizeInfo.digits = params.field.digits;
        }
        
        return Object.keys(sizeInfo).length > 0 ? sizeInfo : null;
    }
    
    // 获取字段约束
    getFieldConstraints(params) {
        const constraints = [];
        
        if (params.fieldInfo.required) {
            constraints.push('required');
        }
        
        if (params.fieldInfo.readonly) {
            constraints.push('readonly');
        }
        
        if (params.field.unique) {
            constraints.push('unique');
        }
        
        if (params.field.index) {
            constraints.push('indexed');
        }
        
        return constraints.length > 0 ? constraints : null;
    }
    
    // 获取字段选项
    getFieldOptions(params) {
        return params.fieldInfo.options || null;
    }
    
    // 获取关系类型
    getRelationType(field) {
        const relationTypes = {
            'many2one': 'Many to One',
            'one2many': 'One to Many',
            'many2many': 'Many to Many'
        };
        
        return relationTypes[field.type] || field.type;
    }
    
    // 获取关系约束
    getRelationConstraints(params) {
        const constraints = [];
        
        if (params.field.ondelete) {
            constraints.push(`ondelete: ${params.field.ondelete}`);
        }
        
        if (params.field.auto_join) {
            constraints.push('auto_join');
        }
        
        return constraints.length > 0 ? constraints : null;
    }
    
    // 获取验证规则
    getValidationRules(params) {
        const rules = [];
        
        if (params.field.required) {
            rules.push('Not null');
        }
        
        if (params.field.size) {
            rules.push(`Max length: ${params.field.size}`);
        }
        
        if (params.field.digits) {
            rules.push(`Digits: ${params.field.digits}`);
        }
        
        return rules.length > 0 ? rules : null;
    }
    
    // 获取字段依赖
    getFieldDependencies(params) {
        // 实现字段依赖分析
        return null;
    }
    
    // 获取字段渲染时间
    getFieldRenderTime(params) {
        // 实现渲染时间统计
        return null;
    }
    
    // 获取字段内存使用
    getFieldMemoryUsage(params) {
        // 实现内存使用统计
        return null;
    }
    
    // 获取字段缓存状态
    getFieldCacheStatus(params) {
        // 实现缓存状态检查
        return null;
    }
    
    // 合并工具提示信息
    mergeTooltipInfo(baseInfo, extendedInfo) {
        return {
            ...baseInfo,
            extended: extendedInfo
        };
    }
    
    // 格式化工具提示信息
    formatTooltipInfo(info) {
        // 移除空值
        const cleanInfo = this.removeEmptyValues(info);
        
        // 排序属性
        const sortedInfo = this.sortTooltipProperties(cleanInfo);
        
        return JSON.stringify(sortedInfo, null, 2);
    }
    
    // 移除空值
    removeEmptyValues(obj) {
        const cleaned = {};
        
        for (const [key, value] of Object.entries(obj)) {
            if (value !== null && value !== undefined && value !== '') {
                if (typeof value === 'object' && !Array.isArray(value)) {
                    const cleanedValue = this.removeEmptyValues(value);
                    if (Object.keys(cleanedValue).length > 0) {
                        cleaned[key] = cleanedValue;
                    }
                } else {
                    cleaned[key] = value;
                }
            }
        }
        
        return cleaned;
    }
    
    // 排序工具提示属性
    sortTooltipProperties(info) {
        const propertyOrder = [
            'viewMode', 'resModel', 'debug', 'timestamp', 'generator',
            'field', 'extended'
        ];
        
        const sorted = {};
        
        // 按指定顺序添加属性
        for (const prop of propertyOrder) {
            if (info.hasOwnProperty(prop)) {
                sorted[prop] = info[prop];
            }
        }
        
        // 添加其他属性
        for (const [key, value] of Object.entries(info)) {
            if (!propertyOrder.includes(key)) {
                sorted[key] = value;
            }
        }
        
        return sorted;
    }
    
    // 验证工具提示长度
    validateTooltipLength(info) {
        if (info.length > this.tooltipConfig.maxTooltipLength) {
            console.warn('Tooltip too long, truncating...');
            return info.substring(0, this.tooltipConfig.maxTooltipLength) + '...';
        }
        
        return info;
    }
    
    // 生成缓存键
    generateCacheKey(params) {
        return `${params.resModel}_${params.field.name}_${params.viewMode}`;
    }
    
    // 获取回退工具提示
    getFallbackTooltip(params) {
        return JSON.stringify({
            field: {
                name: params.field.name,
                type: params.field.type,
                label: params.field.string
            },
            error: 'Failed to generate detailed tooltip'
        });
    }
    
    // 设置缓存系统
    setupCacheSystem() {
        this.cacheConfig = {
            maxSize: 100,
            ttl: 300000, // 5分钟
            cleanupInterval: 60000 // 1分钟
        };
        
        // 定期清理缓存
        setInterval(() => {
            this.cleanupCache();
        }, this.cacheConfig.cleanupInterval);
    }
    
    // 清理缓存
    cleanupCache() {
        if (this.tooltipCache.size > this.cacheConfig.maxSize) {
            const entries = Array.from(this.tooltipCache.entries());
            const toDelete = entries.slice(0, entries.length - this.cacheConfig.maxSize);
            
            for (const [key] of toDelete) {
                this.tooltipCache.delete(key);
            }
        }
    }
    
    // 设置性能监控
    setupPerformanceMonitoring() {
        this.performanceMetrics = {
            generationTimes: [],
            maxGenerationTime: 0,
            minGenerationTime: Infinity
        };
    }
    
    // 记录性能
    recordPerformance(duration) {
        this.tooltipStatistics.totalTooltips++;
        this.tooltipStatistics.averageGenerationTime = 
            (this.tooltipStatistics.averageGenerationTime * (this.tooltipStatistics.totalTooltips - 1) + duration) / 
            this.tooltipStatistics.totalTooltips;
        
        this.performanceMetrics.generationTimes.push(duration);
        this.performanceMetrics.maxGenerationTime = Math.max(this.performanceMetrics.maxGenerationTime, duration);
        this.performanceMetrics.minGenerationTime = Math.min(this.performanceMetrics.minGenerationTime, duration);
        
        // 保持最近100次记录
        if (this.performanceMetrics.generationTimes.length > 100) {
            this.performanceMetrics.generationTimes.shift();
        }
    }
    
    // 设置自定义扩展
    setupCustomExtensions() {
        // 允许注册自定义信息提供者
        this.customProviders = new Map();
    }
    
    // 注册自定义信息提供者
    registerCustomProvider(name, provider, priority = 50, description = '') {
        this.infoProviders.set(name, {
            provider,
            priority,
            description,
            isCustom: true
        });
    }
    
    // 获取工具提示信息（主要接口）
    getTooltipInfo(params) {
        if (this.tooltipConfig.showInProduction || odoo.debug) {
            return this.enhancedGetTooltipInfo(params);
        } else {
            return getTooltipInfo(params);
        }
    }
    
    // 获取工具提示统计
    getTooltipStatistics() {
        return {
            ...this.tooltipStatistics,
            cacheSize: this.tooltipCache.size,
            providerCount: this.infoProviders.size,
            performanceMetrics: {
                ...this.performanceMetrics,
                averageTime: this.tooltipStatistics.averageGenerationTime
            }
        };
    }
    
    // 清理缓存
    clearCache() {
        this.tooltipCache.clear();
        this.tooltipStatistics.cacheHits = 0;
        this.tooltipStatistics.cacheMisses = 0;
    }
    
    // 销毁管理器
    destroy() {
        // 清理缓存
        this.tooltipCache.clear();
        
        // 清理信息提供者
        this.infoProviders.clear();
        
        // 重置统计
        this.tooltipStatistics = {
            totalTooltips: 0,
            cacheHits: 0,
            cacheMisses: 0,
            averageGenerationTime: 0
        };
    }
}

// 使用示例
const tooltipManager = new FieldTooltipManager();

// 生成工具提示信息
const tooltipInfo = tooltipManager.getTooltipInfo({
    field: { name: 'name', type: 'char', string: 'Name' },
    fieldInfo: { widget: 'char', required: true },
    viewMode: 'form',
    resModel: 'res.partner'
});

// 注册自定义信息提供者
tooltipManager.registerCustomProvider(
    'custom_info',
    (params) => ({
        customData: 'Custom field information',
        timestamp: Date.now()
    }),
    95,
    'Custom information provider'
);

// 获取统计信息
const stats = tooltipManager.getTooltipStatistics();
console.log('Tooltip statistics:', stats);
```

## 技术特点

### 1. 信息完整性
- **全面收集**: 收集字段的所有相关信息
- **结构化**: 结构化的信息组织
- **可扩展**: 支持自定义信息扩展
- **标准化**: 标准化的信息格式

### 2. 调试支持
- **开发者友好**: 为开发者提供详细信息
- **调试模式**: 在调试模式下提供更多信息
- **错误诊断**: 帮助诊断字段问题
- **性能分析**: 提供性能相关信息

### 3. 性能优化
- **轻量级**: 简洁的代码实现
- **缓存机制**: 智能的信息缓存
- **按需生成**: 按需生成工具提示信息
- **内存友好**: 有效的内存使用

### 4. 用户体验
- **信息丰富**: 提供丰富的字段信息
- **格式友好**: 友好的信息格式
- **快速访问**: 快速访问字段详情
- **上下文相关**: 提供上下文相关信息

## 设计模式

### 1. 工厂模式 (Factory Pattern)
- **信息工厂**: 创建不同类型的字段信息
- **提供者工厂**: 创建信息提供者
- **格式化工厂**: 创建格式化器

### 2. 策略模式 (Strategy Pattern)
- **信息策略**: 不同的信息收集策略
- **格式化策略**: 不同的信息格式化策略
- **缓存策略**: 不同的缓存策略

### 3. 建造者模式 (Builder Pattern)
- **信息建造**: 逐步构建完整的字段信息
- **格式建造**: 逐步构建格式化的信息
- **配置建造**: 逐步构建配置信息

### 4. 装饰器模式 (Decorator Pattern)
- **信息装饰**: 装饰基础字段信息
- **格式装饰**: 装饰信息格式
- **功能装饰**: 装饰工具提示功能

## 注意事项

1. **性能考虑**: 避免生成过于复杂的工具提示
2. **信息安全**: 不要在工具提示中暴露敏感信息
3. **内存管理**: 合理使用缓存避免内存泄漏
4. **用户体验**: 确保工具提示信息的可读性

## 扩展建议

1. **可视化**: 添加字段信息的可视化显示
2. **交互式**: 支持交互式的字段信息浏览
3. **导出功能**: 支持字段信息的导出功能
4. **比较功能**: 支持字段信息的比较功能
5. **历史记录**: 添加字段信息的历史记录

该字段工具提示模块为Odoo Web客户端提供了强大的字段信息展示功能，通过详细的元数据收集和友好的信息格式确保了开发者和用户能够快速了解字段的详细信息。

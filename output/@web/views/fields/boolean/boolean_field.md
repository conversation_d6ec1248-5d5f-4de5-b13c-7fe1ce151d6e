# BooleanField - 布尔字段

## 概述

`boolean_field.js` 是 Odoo Web 客户端的布尔字段组件，负责显示和编辑布尔值。该模块包含44行代码，是一个简洁的复选框组件，专门用于处理true/false值的输入和显示，具备状态同步、记录观察、变更处理等特性，是表单中最基础的输入组件之一。

## 文件信息
- **路径**: `/web/static/src/views/fields/boolean/boolean_field.js`
- **行数**: 44
- **模块**: `@web/views/fields/boolean/boolean_field`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                             // OWL框架
'@web/core/checkbox/checkbox'           // 复选框组件
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/model/relational_model/utils'     // 关系模型工具
'@web/views/fields/standard_field_props' // 标准字段属性
```

## 核心功能

### 1. 组件定义

```javascript
const BooleanField = class BooleanField extends Component {
    static template = "web.BooleanField";
    static components = { CheckBox };
    static props = {
        ...standardFieldProps,
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **复选框组件**: 集成CheckBox核心组件
- **专用模板**: 使用BooleanField专用模板
- **简洁设计**: 最简化的组件实现

### 2. 组件初始化

```javascript
setup() {
    this.state = useState({});
    useRecordObserver((record) => {
        this.state.value = record.data[this.props.name];
    });
}
```

**初始化功能**:
- **状态管理**: 初始化组件状态
- **记录观察**: 观察记录数据变化
- **值同步**: 自动同步字段值到组件状态
- **响应式**: 响应记录数据的变化

### 3. 变更处理

```javascript
/**
 * @param {boolean} newValue
 */
onChange(newValue) {
    this.state.value = newValue;
    this.props.record.update({ [this.props.name]: newValue });
}
```

**变更功能**:
- **状态更新**: 更新组件内部状态
- **记录更新**: 更新记录中的字段值
- **即时同步**: 即时同步状态和数据
- **类型安全**: 确保值为布尔类型

### 4. 字段注册

```javascript
const booleanField = {
    component: BooleanField,
    displayName: _t("Checkbox"),
    supportedTypes: ["boolean"],
    isEmpty: () => false,
};

registry.category("fields").add("boolean", booleanField);
```

**注册功能**:
- **组件注册**: 注册布尔字段组件
- **显示名称**: 国际化的显示名称
- **类型支持**: 仅支持boolean类型
- **空值定义**: 布尔字段永远不为空

## 使用场景

### 1. 布尔字段管理器

```javascript
// 布尔字段管理器
class BooleanFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置布尔配置
        this.booleanConfig = {
            enableTriState: false,
            enableCustomLabels: true,
            enableAnimation: true,
            enableValidation: true,
            enableGrouping: true,
            enableBulkEdit: true,
            defaultValue: false,
            enableAuditTrail: true
        };
        
        // 设置标签配置
        this.labelConfig = new Map([
            ['default', { true: 'Yes', false: 'No' }],
            ['enabled', { true: 'Enabled', false: 'Disabled' }],
            ['active', { true: 'Active', false: 'Inactive' }],
            ['visible', { true: 'Visible', false: 'Hidden' }],
            ['published', { true: 'Published', false: 'Draft' }]
        ]);
        
        // 设置样式配置
        this.styleConfig = new Map([
            ['default', { true: 'text-success', false: 'text-muted' }],
            ['danger', { true: 'text-success', false: 'text-danger' }],
            ['warning', { true: 'text-success', false: 'text-warning' }],
            ['info', { true: 'text-success', false: 'text-info' }]
        ]);
        
        // 设置布尔统计
        this.booleanStatistics = {
            totalFields: 0,
            trueCount: 0,
            falseCount: 0,
            toggleCount: 0,
            averageToggleTime: 0
        };
        
        this.initializeBooleanSystem();
    }
    
    // 初始化布尔系统
    initializeBooleanSystem() {
        // 创建增强的布尔字段
        this.createEnhancedBooleanField();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置审计系统
        this.setupAuditSystem();
        
        // 设置批量操作
        this.setupBulkOperations();
    }
    
    // 创建增强的布尔字段
    createEnhancedBooleanField() {
        const originalField = BooleanField;
        
        this.EnhancedBooleanField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
                
                // 添加审计功能
                this.addAuditFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    ...this.state,
                    isToggling: false,
                    lastToggleTime: null,
                    toggleHistory: [],
                    validationErrors: [],
                    customLabel: null,
                    customStyle: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的变更处理
                this.enhancedOnChange = async (newValue) => {
                    const startTime = performance.now();
                    this.enhancedState.isToggling = true;
                    
                    try {
                        // 验证新值
                        if (!this.validateValue(newValue)) {
                            throw new Error('Invalid boolean value');
                        }
                        
                        // 记录变更历史
                        this.recordToggleHistory(this.state.value, newValue);
                        
                        // 执行原始变更处理
                        this.onChange(newValue);
                        
                        // 触发动画
                        if (this.booleanConfig.enableAnimation) {
                            this.playToggleAnimation(newValue);
                        }
                        
                        // 记录审计
                        if (this.booleanConfig.enableAuditTrail) {
                            this.recordAudit(this.state.value, newValue);
                        }
                        
                        // 更新统计
                        this.updateStatistics(newValue);
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordToggleTime(endTime - startTime);
                        
                    } catch (error) {
                        this.handleToggleError(error);
                    } finally {
                        this.enhancedState.isToggling = false;
                        this.enhancedState.lastToggleTime = Date.now();
                    }
                };
                
                // 验证值
                this.validateValue = (value) => {
                    // 检查类型
                    if (typeof value !== 'boolean') {
                        return false;
                    }
                    
                    // 自定义验证规则
                    if (this.customValidationRules) {
                        return this.customValidationRules.every(rule => rule(value));
                    }
                    
                    return true;
                };
                
                // 记录切换历史
                this.recordToggleHistory = (oldValue, newValue) => {
                    const historyEntry = {
                        from: oldValue,
                        to: newValue,
                        timestamp: Date.now(),
                        fieldName: this.props.name
                    };
                    
                    this.enhancedState.toggleHistory.push(historyEntry);
                    
                    // 限制历史记录数量
                    if (this.enhancedState.toggleHistory.length > 50) {
                        this.enhancedState.toggleHistory.shift();
                    }
                };
                
                // 播放切换动画
                this.playToggleAnimation = (newValue) => {
                    const animationClass = newValue ? 'boolean-toggle-on' : 'boolean-toggle-off';
                    
                    // 添加动画类
                    this.addAnimationClass(animationClass);
                    
                    // 动画结束后移除类
                    setTimeout(() => {
                        this.removeAnimationClass(animationClass);
                    }, 300);
                };
                
                // 添加动画类
                this.addAnimationClass = (className) => {
                    // 实现动画类添加逻辑
                    console.log('Adding animation class:', className);
                };
                
                // 移除动画类
                this.removeAnimationClass = (className) => {
                    // 实现动画类移除逻辑
                    console.log('Removing animation class:', className);
                };
                
                // 记录审计
                this.recordAudit = (oldValue, newValue) => {
                    const auditEntry = {
                        fieldName: this.props.name,
                        oldValue: oldValue,
                        newValue: newValue,
                        timestamp: Date.now(),
                        userId: this.getCurrentUserId(),
                        recordId: this.props.record.resId
                    };
                    
                    // 发送审计记录
                    this.sendAuditRecord(auditEntry);
                };
                
                // 获取当前用户ID
                this.getCurrentUserId = () => {
                    // 实现用户ID获取逻辑
                    return 1;
                };
                
                // 发送审计记录
                this.sendAuditRecord = (auditEntry) => {
                    // 实现审计记录发送逻辑
                    console.log('Audit record:', auditEntry);
                };
                
                // 更新统计
                this.updateStatistics = (newValue) => {
                    this.booleanStatistics.toggleCount++;
                    
                    if (newValue) {
                        this.booleanStatistics.trueCount++;
                    } else {
                        this.booleanStatistics.falseCount++;
                    }
                };
                
                // 获取显示标签
                this.getDisplayLabel = () => {
                    const value = this.state.value;
                    
                    // 使用自定义标签
                    if (this.enhancedState.customLabel) {
                        return this.enhancedState.customLabel[value];
                    }
                    
                    // 使用配置的标签
                    const labelType = this.getLabelType();
                    const labels = this.labelConfig.get(labelType) || this.labelConfig.get('default');
                    
                    return labels[value];
                };
                
                // 获取标签类型
                this.getLabelType = () => {
                    // 根据字段名推断标签类型
                    const fieldName = this.props.name.toLowerCase();
                    
                    if (fieldName.includes('active')) return 'active';
                    if (fieldName.includes('enable')) return 'enabled';
                    if (fieldName.includes('visible')) return 'visible';
                    if (fieldName.includes('publish')) return 'published';
                    
                    return 'default';
                };
                
                // 获取显示样式
                this.getDisplayStyle = () => {
                    const value = this.state.value;
                    
                    // 使用自定义样式
                    if (this.enhancedState.customStyle) {
                        return this.enhancedState.customStyle[value];
                    }
                    
                    // 使用配置的样式
                    const styleType = this.getStyleType();
                    const styles = this.styleConfig.get(styleType) || this.styleConfig.get('default');
                    
                    return styles[value];
                };
                
                // 获取样式类型
                this.getStyleType = () => {
                    // 根据字段名推断样式类型
                    const fieldName = this.props.name.toLowerCase();
                    
                    if (fieldName.includes('danger') || fieldName.includes('error')) return 'danger';
                    if (fieldName.includes('warning') || fieldName.includes('alert')) return 'warning';
                    if (fieldName.includes('info') || fieldName.includes('notice')) return 'info';
                    
                    return 'default';
                };
                
                // 设置自定义标签
                this.setCustomLabel = (trueLabel, falseLabel) => {
                    this.enhancedState.customLabel = {
                        true: trueLabel,
                        false: falseLabel
                    };
                };
                
                // 设置自定义样式
                this.setCustomStyle = (trueStyle, falseStyle) => {
                    this.enhancedState.customStyle = {
                        true: trueStyle,
                        false: falseStyle
                    };
                };
                
                // 切换值
                this.toggle = () => {
                    const newValue = !this.state.value;
                    this.enhancedOnChange(newValue);
                };
                
                // 设置为真
                this.setTrue = () => {
                    if (!this.state.value) {
                        this.enhancedOnChange(true);
                    }
                };
                
                // 设置为假
                this.setFalse = () => {
                    if (this.state.value) {
                        this.enhancedOnChange(false);
                    }
                };
                
                // 重置为默认值
                this.reset = () => {
                    const defaultValue = this.booleanConfig.defaultValue;
                    if (this.state.value !== defaultValue) {
                        this.enhancedOnChange(defaultValue);
                    }
                };
                
                // 获取切换历史
                this.getToggleHistory = () => {
                    return this.enhancedState.toggleHistory;
                };
                
                // 清除历史
                this.clearHistory = () => {
                    this.enhancedState.toggleHistory = [];
                };
                
                // 处理切换错误
                this.handleToggleError = (error) => {
                    console.error('Toggle error:', error);
                    this.enhancedState.validationErrors.push(error.message);
                };
                
                // 记录切换时间
                this.recordToggleTime = (duration) => {
                    this.booleanStatistics.averageToggleTime = 
                        (this.booleanStatistics.averageToggleTime + duration) / 2;
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    rules: [],
                    addRule: (rule) => this.validationManager.rules.push(rule),
                    validate: (value) => this.validateValue(value)
                };
            }
            
            addAuditFeatures() {
                // 审计功能
                this.auditManager = {
                    enabled: this.booleanConfig.enableAuditTrail,
                    records: [],
                    record: (entry) => this.recordAudit(entry.oldValue, entry.newValue)
                };
            }
            
            // 重写原始方法
            onChange(newValue) {
                return this.enhancedOnChange(newValue);
            }
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationConfig = {
            enableTypeCheck: true,
            enableCustomRules: true,
            enableAsyncValidation: false
        };
    }
    
    // 设置审计系统
    setupAuditSystem() {
        this.auditConfig = {
            enableAuditTrail: this.booleanConfig.enableAuditTrail,
            auditEndpoint: '/web/audit/boolean',
            batchSize: 10
        };
    }
    
    // 设置批量操作
    setupBulkOperations() {
        this.bulkConfig = {
            enableBulkEdit: this.booleanConfig.enableBulkEdit,
            maxBulkSize: 100
        };
    }
    
    // 创建布尔字段
    createBooleanField(props) {
        return new this.EnhancedBooleanField(props);
    }
    
    // 批量设置值
    bulkSetValue(fields, value) {
        const results = [];
        
        for (const field of fields) {
            try {
                field.enhancedOnChange(value);
                results.push({ field, success: true });
            } catch (error) {
                results.push({ field, success: false, error });
            }
        }
        
        return results;
    }
    
    // 批量切换
    bulkToggle(fields) {
        const results = [];
        
        for (const field of fields) {
            try {
                field.toggle();
                results.push({ field, success: true });
            } catch (error) {
                results.push({ field, success: false, error });
            }
        }
        
        return results;
    }
    
    // 注册自定义标签
    registerCustomLabel(name, trueLabel, falseLabel) {
        this.labelConfig.set(name, {
            true: trueLabel,
            false: falseLabel
        });
    }
    
    // 注册自定义样式
    registerCustomStyle(name, trueStyle, falseStyle) {
        this.styleConfig.set(name, {
            true: trueStyle,
            false: falseStyle
        });
    }
    
    // 获取布尔统计
    getBooleanStatistics() {
        return {
            ...this.booleanStatistics,
            labelTypes: this.labelConfig.size,
            styleTypes: this.styleConfig.size,
            truePercentage: this.booleanStatistics.trueCount / Math.max(this.booleanStatistics.toggleCount, 1) * 100
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理配置
        this.labelConfig.clear();
        this.styleConfig.clear();
        
        // 重置统计
        this.booleanStatistics = {
            totalFields: 0,
            trueCount: 0,
            falseCount: 0,
            toggleCount: 0,
            averageToggleTime: 0
        };
    }
}

// 使用示例
const booleanManager = new BooleanFieldManager();

// 创建布尔字段
const booleanField = booleanManager.createBooleanField({
    name: 'is_active',
    record: {
        data: { is_active: true },
        fields: { is_active: { type: 'boolean' } }
    }
});

// 注册自定义标签
booleanManager.registerCustomLabel('status', 'Online', 'Offline');

// 批量操作
const fields = [booleanField];
booleanManager.bulkSetValue(fields, false);

// 获取统计信息
const stats = booleanManager.getBooleanStatistics();
console.log('Boolean field statistics:', stats);
```

## 技术特点

### 1. 简洁高效
- **最小实现**: 最简化的代码实现
- **核心功能**: 专注于布尔值处理
- **轻量级**: 极小的资源占用
- **快速响应**: 快速的状态更新

### 2. 状态同步
- **记录观察**: 自动观察记录数据变化
- **双向绑定**: 状态与数据的双向同步
- **即时更新**: 即时更新状态和记录
- **一致性**: 保持数据一致性

### 3. 用户体验
- **直观操作**: 直观的复选框操作
- **即时反馈**: 即时的状态反馈
- **无障碍**: 支持无障碍访问
- **键盘支持**: 支持键盘操作

### 4. 扩展性
- **组件化**: 基于组件的设计
- **可扩展**: 易于扩展功能
- **可定制**: 支持自定义配置
- **标准化**: 遵循标准接口

## 设计模式

### 1. 组件模式 (Component Pattern)
- **UI组件**: 封装布尔值UI
- **状态管理**: 管理组件状态
- **事件处理**: 处理用户交互

### 2. 观察者模式 (Observer Pattern)
- **数据观察**: 观察记录数据变化
- **状态观察**: 观察组件状态变化
- **自动同步**: 自动同步数据

### 3. 策略模式 (Strategy Pattern)
- **验证策略**: 不同的值验证策略
- **显示策略**: 不同的显示策略
- **更新策略**: 不同的更新策略

### 4. 代理模式 (Proxy Pattern)
- **状态代理**: 代理状态访问
- **数据代理**: 代理数据访问
- **事件代理**: 代理事件处理

## 注意事项

1. **类型安全**: 确保值为布尔类型
2. **状态一致性**: 保持状态与数据的一致性
3. **性能考虑**: 避免频繁的状态更新
4. **用户体验**: 提供清晰的状态反馈

## 扩展建议

1. **三态支持**: 添加三态（true/false/null）支持
2. **自定义样式**: 支持更多自定义样式
3. **批量操作**: 支持批量布尔操作
4. **动画效果**: 添加切换动画效果
5. **审计功能**: 添加变更审计功能

该布尔字段为Odoo Web客户端提供了简洁高效的布尔值处理功能，通过最小化的实现和标准化的接口确保了良好的性能和用户体验。

# TranslationDialog - 翻译对话框

## 概述

`translation_dialog.js` 是 Odoo Web 客户端字段系统的翻译对话框组件，负责提供字段翻译编辑的用户界面。该模块包含118行代码，是一个OWL组件，专门用于在对话框中显示和编辑字段的多语言翻译，具备语言加载、翻译管理、数据保存、源码显示等特性，是多语言字段编辑的核心UI组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/translation_dialog.js`
- **行数**: 118
- **模块**: `@web/views/fields/translation_dialog`

## 依赖关系

```javascript
// 核心依赖
'@web/core/dialog/dialog'           // 对话框组件
'@web/core/user'                    // 用户服务
'@web/core/utils/hooks'             // 工具钩子
'@web/core/l10n/translation'        // 翻译服务
'@web/core/l10n/utils'              // 本地化工具
'@odoo/owl'                         // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const TranslationDialog = class TranslationDialog extends Component {
    static template = "web.TranslationDialog";
    static components = { Dialog };
    static props = {
        fieldName: String,
        resId: Number,
        resModel: String,
        userLanguageValue: { type: String, optional: true },
        isComingFromTranslationAlert: { type: Boolean, optional: true },
        onSave: Function,
        close: Function,
        isText: { type: Boolean, optional: true },
        showSource: { type: Boolean, optional: true },
    };
}
```

**组件属性**:
- **fieldName**: 字段名称
- **resId**: 记录ID
- **resModel**: 模型名称
- **userLanguageValue**: 用户语言的字段值
- **isComingFromTranslationAlert**: 是否来自翻译警告
- **onSave**: 保存回调函数
- **close**: 关闭回调函数
- **isText**: 是否为文本类型
- **showSource**: 是否显示源码

### 2. 组件初始化

```javascript
setup() {
    super.setup();
    this.title = _t("Translate: %s", this.props.fieldName);

    this.user = user;
    this.orm = useService("orm");

    this.terms = [];
    this.updatedTerms = {};

    onWillStart(async () => {
        const languages = await loadLanguages(this.orm);
        const [translations, context] = await this.loadTranslations(languages);
        let id = 1;
        translations.forEach((t) => (t.id = id++));
        this.props.isText = context.translation_type === "text";
        this.props.showSource = context.translation_show_source;

        this.terms = translations.map((term) => {
            const relatedLanguage = languages.find((l) => l[0] === term.lang);
            const termInfo = {
                ...term,
                langName: relatedLanguage[1],
                value: term.value || "",
            };
            // 设置用户当前语言的翻译值
            if (
                term.lang === jsToPyLocale(user.lang) &&
                !this.props.showSource &&
                !this.props.isComingFromTranslationAlert
            ) {
                this.updatedTerms[term.id] = this.props.userLanguageValue;
                termInfo.value = this.props.userLanguageValue;
            }
            return termInfo;
        });
        this.terms.sort((a, b) => a.langName.localeCompare(b.langName));
    });
}
```

**初始化功能**:
- **标题设置**: 设置对话框标题
- **服务注入**: 注入用户和ORM服务
- **数据初始化**: 初始化翻译术语和更新记录
- **语言加载**: 加载系统支持的语言
- **翻译加载**: 加载字段的翻译数据
- **数据处理**: 处理和排序翻译术语

### 3. 翻译数据加载

```javascript
async loadTranslations(languages) {
    return this.orm.call(this.props.resModel, "get_field_translations", [
        [this.props.resId],
        this.props.fieldName,
    ]);
}
```

**数据加载功能**:
- **RPC调用**: 调用后端获取翻译数据
- **参数传递**: 传递模型、记录ID和字段名
- **数据返回**: 返回翻译数据和上下文信息
- **异步处理**: 异步加载翻译数据

### 4. 翻译保存

```javascript
async onSave() {
    const translations = {};

    this.terms.map((term) => {
        const updatedTermValue = this.updatedTerms[term.id];
        if (term.id in this.updatedTerms && term.value !== updatedTermValue) {
            if (this.props.showSource) {
                if (!translations[term.lang]) {
                    translations[term.lang] = {};
                }
                translations[term.lang][term.source] = updatedTermValue || term.source;
            } else {
                translations[term.lang] = updatedTermValue || false;
            }
        }
    });

    await this.orm.call(this.props.resModel, "update_field_translations", [
        [this.props.resId],
        this.props.fieldName,
        translations,
    ]);

    await this.props.onSave();
    this.props.close();
}
```

**保存功能**:
- **变更检测**: 检测哪些翻译被修改
- **数据组织**: 组织翻译数据结构
- **源码处理**: 处理源码模式的翻译
- **RPC保存**: 调用后端保存翻译
- **回调执行**: 执行保存回调和关闭对话框

## 使用场景

### 1. 翻译对话框管理器

```javascript
// 翻译对话框管理器
class TranslationDialogManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置对话框配置
        this.dialogConfig = {
            enableAutoSave: false,
            enableValidation: true,
            enableSourceMode: true,
            enableBatchEdit: true,
            maxLanguages: 50,
            autoCloseTimeout: 300000,
            enableTranslationMemory: true,
            enableSpellCheck: true
        };
        
        // 设置翻译缓存
        this.translationCache = new Map();
        
        // 设置语言信息
        this.languageInfo = new Map();
        
        // 设置对话框统计
        this.dialogStatistics = {
            totalDialogs: 0,
            successfulSaves: 0,
            failedSaves: 0,
            averageSaveTime: 0,
            languagesEdited: new Set()
        };
        
        this.initializeDialogSystem();
    }
    
    // 初始化对话框系统
    initializeDialogSystem() {
        // 创建增强的翻译对话框
        this.createEnhancedTranslationDialog();
        
        // 设置翻译验证
        this.setupTranslationValidation();
        
        // 设置翻译记忆
        this.setupTranslationMemory();
        
        // 设置批量编辑
        this.setupBatchEditing();
    }
    
    // 创建增强的翻译对话框
    createEnhancedTranslationDialog() {
        const originalDialog = TranslationDialog;
        
        this.EnhancedTranslationDialog = class extends originalDialog {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
                
                // 添加编辑功能
                this.addEditingFeatures();
                
                // 添加统计功能
                this.addStatisticsFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    ...this.state,
                    validationErrors: new Map(),
                    translationMemory: new Map(),
                    editHistory: [],
                    isDirty: false,
                    autoSaveTimer: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的翻译加载
                this.enhancedLoadTranslations = async (languages) => {
                    const startTime = performance.now();
                    
                    try {
                        // 检查缓存
                        const cacheKey = this.generateCacheKey();
                        if (this.translationCache.has(cacheKey)) {
                            return this.translationCache.get(cacheKey);
                        }
                        
                        // 执行原始加载
                        const result = await this.loadTranslations(languages);
                        
                        // 缓存结果
                        this.translationCache.set(cacheKey, result);
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordLoadTime(endTime - startTime);
                        
                        return result;
                        
                    } catch (error) {
                        this.handleLoadError(error);
                        throw error;
                    }
                };
                
                // 增强的保存功能
                this.enhancedOnSave = async () => {
                    const startTime = performance.now();
                    
                    try {
                        // 验证翻译
                        const validationResult = this.validateTranslations();
                        if (!validationResult.isValid) {
                            this.showValidationErrors(validationResult.errors);
                            return;
                        }
                        
                        // 预处理翻译数据
                        const processedTranslations = this.preprocessTranslations();
                        
                        // 执行保存
                        await this.saveTranslations(processedTranslations);
                        
                        // 更新翻译记忆
                        this.updateTranslationMemory();
                        
                        // 记录统计
                        this.dialogStatistics.successfulSaves++;
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordSaveTime(endTime - startTime);
                        
                        // 执行原始保存回调
                        await this.props.onSave();
                        this.props.close();
                        
                    } catch (error) {
                        this.handleSaveError(error);
                        this.dialogStatistics.failedSaves++;
                    }
                };
                
                // 验证翻译
                this.validateTranslations = () => {
                    const errors = [];
                    
                    for (const term of this.terms) {
                        const updatedValue = this.updatedTerms[term.id];
                        
                        if (updatedValue !== undefined) {
                            // 检查空值
                            if (this.isRequiredLanguage(term.lang) && !updatedValue.trim()) {
                                errors.push(`${term.langName}: Translation is required`);
                            }
                            
                            // 检查长度
                            if (updatedValue.length > this.getMaxLength(term.lang)) {
                                errors.push(`${term.langName}: Translation is too long`);
                            }
                            
                            // 检查格式
                            if (!this.validateFormat(updatedValue, term.lang)) {
                                errors.push(`${term.langName}: Invalid format`);
                            }
                        }
                    }
                    
                    return {
                        isValid: errors.length === 0,
                        errors: errors
                    };
                };
                
                // 预处理翻译数据
                this.preprocessTranslations = () => {
                    const translations = {};
                    
                    this.terms.forEach((term) => {
                        const updatedTermValue = this.updatedTerms[term.id];
                        if (term.id in this.updatedTerms && term.value !== updatedTermValue) {
                            // 清理和格式化翻译文本
                            const cleanedValue = this.cleanTranslationText(updatedTermValue);
                            
                            if (this.props.showSource) {
                                if (!translations[term.lang]) {
                                    translations[term.lang] = {};
                                }
                                translations[term.lang][term.source] = cleanedValue || term.source;
                            } else {
                                translations[term.lang] = cleanedValue || false;
                            }
                        }
                    });
                    
                    return translations;
                };
                
                // 清理翻译文本
                this.cleanTranslationText = (text) => {
                    if (!text) return text;
                    
                    // 移除多余空格
                    text = text.trim().replace(/\s+/g, ' ');
                    
                    // 处理特殊字符
                    text = text.replace(/[""]/g, '"').replace(/['']/g, "'");
                    
                    return text;
                };
                
                // 保存翻译
                this.saveTranslations = async (translations) => {
                    await this.orm.call(this.props.resModel, "update_field_translations", [
                        [this.props.resId],
                        this.props.fieldName,
                        translations,
                    ]);
                };
                
                // 更新翻译记忆
                this.updateTranslationMemory = () => {
                    for (const term of this.terms) {
                        const updatedValue = this.updatedTerms[term.id];
                        if (updatedValue && updatedValue !== term.value) {
                            const memoryKey = `${term.source}_${term.lang}`;
                            this.enhancedState.translationMemory.set(memoryKey, {
                                source: term.source,
                                target: updatedValue,
                                language: term.lang,
                                timestamp: Date.now()
                            });
                        }
                    }
                };
                
                // 获取翻译建议
                this.getTranslationSuggestions = (sourceText, targetLang) => {
                    const suggestions = [];
                    
                    // 从翻译记忆中查找
                    for (const [key, memory] of this.enhancedState.translationMemory.entries()) {
                        if (memory.source === sourceText && memory.language === targetLang) {
                            suggestions.push({
                                text: memory.target,
                                source: 'memory',
                                confidence: 1.0
                            });
                        }
                    }
                    
                    return suggestions;
                };
                
                // 批量编辑
                this.batchEdit = (operation, languages = []) => {
                    const affectedTerms = languages.length > 0 
                        ? this.terms.filter(term => languages.includes(term.lang))
                        : this.terms;
                    
                    switch (operation) {
                        case 'clear':
                            affectedTerms.forEach(term => {
                                this.updatedTerms[term.id] = '';
                            });
                            break;
                            
                        case 'copy_from_source':
                            affectedTerms.forEach(term => {
                                if (term.source) {
                                    this.updatedTerms[term.id] = term.source;
                                }
                            });
                            break;
                            
                        case 'uppercase':
                            affectedTerms.forEach(term => {
                                const currentValue = this.updatedTerms[term.id] || term.value;
                                this.updatedTerms[term.id] = currentValue.toUpperCase();
                            });
                            break;
                            
                        case 'lowercase':
                            affectedTerms.forEach(term => {
                                const currentValue = this.updatedTerms[term.id] || term.value;
                                this.updatedTerms[term.id] = currentValue.toLowerCase();
                            });
                            break;
                    }
                    
                    this.enhancedState.isDirty = true;
                };
                
                // 撤销编辑
                this.undoEdit = () => {
                    const lastEdit = this.enhancedState.editHistory.pop();
                    if (lastEdit) {
                        this.updatedTerms = { ...lastEdit.previousState };
                    }
                };
                
                // 记录编辑历史
                this.recordEdit = (termId, oldValue, newValue) => {
                    this.enhancedState.editHistory.push({
                        termId: termId,
                        oldValue: oldValue,
                        newValue: newValue,
                        timestamp: Date.now(),
                        previousState: { ...this.updatedTerms }
                    });
                    
                    // 限制历史记录数量
                    if (this.enhancedState.editHistory.length > 50) {
                        this.enhancedState.editHistory.shift();
                    }
                };
                
                // 自动保存
                this.enableAutoSave = () => {
                    if (this.dialogConfig.enableAutoSave) {
                        this.enhancedState.autoSaveTimer = setInterval(() => {
                            if (this.enhancedState.isDirty) {
                                this.autoSave();
                            }
                        }, 30000); // 30秒
                    }
                };
                
                // 执行自动保存
                this.autoSave = async () => {
                    try {
                        await this.enhancedOnSave();
                        this.enhancedState.isDirty = false;
                    } catch (error) {
                        console.error('Auto save failed:', error);
                    }
                };
                
                // 生成缓存键
                this.generateCacheKey = () => {
                    return `${this.props.resModel}_${this.props.resId}_${this.props.fieldName}`;
                };
                
                // 检查是否为必需语言
                this.isRequiredLanguage = (lang) => {
                    // 实现必需语言检查逻辑
                    return ['en_US'].includes(lang);
                };
                
                // 获取最大长度
                this.getMaxLength = (lang) => {
                    // 实现最大长度获取逻辑
                    return 1000;
                };
                
                // 验证格式
                this.validateFormat = (text, lang) => {
                    // 实现格式验证逻辑
                    return true;
                };
                
                // 显示验证错误
                this.showValidationErrors = (errors) => {
                    console.error('Validation errors:', errors);
                };
                
                // 错误处理
                this.handleLoadError = (error) => {
                    console.error('Load error:', error);
                };
                
                this.handleSaveError = (error) => {
                    console.error('Save error:', error);
                };
                
                // 记录性能
                this.recordLoadTime = (duration) => {
                    console.log(`Translation load took ${duration}ms`);
                };
                
                this.recordSaveTime = (duration) => {
                    this.dialogStatistics.totalDialogs++;
                    this.dialogStatistics.averageSaveTime = 
                        (this.dialogStatistics.averageSaveTime * (this.dialogStatistics.totalDialogs - 1) + duration) / 
                        this.dialogStatistics.totalDialogs;
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    rules: new Map(),
                    addRule: (name, rule) => this.validationManager.rules.set(name, rule),
                    validate: (text, lang) => this.validateTranslations()
                };
            }
            
            addEditingFeatures() {
                // 编辑功能
                this.editingManager = {
                    enableSpellCheck: this.dialogConfig.enableSpellCheck,
                    enableAutoComplete: true,
                    enableSuggestions: true
                };
            }
            
            addStatisticsFeatures() {
                // 统计功能
                this.statisticsManager = {
                    getStats: () => this.dialogStatistics,
                    recordLanguageEdit: (lang) => {
                        this.dialogStatistics.languagesEdited.add(lang);
                    }
                };
            }
            
            // 重写原始方法
            loadTranslations(languages) {
                return this.enhancedLoadTranslations(languages);
            }
            
            onSave() {
                return this.enhancedOnSave();
            }
            
            // 组件销毁时清理
            onWillDestroy() {
                if (this.enhancedState.autoSaveTimer) {
                    clearInterval(this.enhancedState.autoSaveTimer);
                }
            }
        };
    }
    
    // 设置翻译验证
    setupTranslationValidation() {
        this.validationConfig = {
            enableRequiredCheck: true,
            enableLengthCheck: true,
            enableFormatCheck: true,
            enableDuplicateCheck: true
        };
    }
    
    // 设置翻译记忆
    setupTranslationMemory() {
        this.memoryConfig = {
            enabled: this.dialogConfig.enableTranslationMemory,
            maxEntries: 1000,
            enableFuzzyMatch: true
        };
    }
    
    // 设置批量编辑
    setupBatchEditing() {
        this.batchConfig = {
            enabled: this.dialogConfig.enableBatchEdit,
            supportedOperations: ['clear', 'copy_from_source', 'uppercase', 'lowercase']
        };
    }
    
    // 创建翻译对话框
    createTranslationDialog(props) {
        return new this.EnhancedTranslationDialog(props);
    }
    
    // 获取对话框统计
    getDialogStatistics() {
        return {
            ...this.dialogStatistics,
            cacheSize: this.translationCache.size,
            languageCount: this.languageInfo.size,
            editedLanguagesCount: this.dialogStatistics.languagesEdited.size
        };
    }
    
    // 清理缓存
    clearCache() {
        this.translationCache.clear();
        this.languageInfo.clear();
    }
    
    // 销毁管理器
    destroy() {
        // 清理缓存
        this.translationCache.clear();
        this.languageInfo.clear();
        
        // 重置统计
        this.dialogStatistics = {
            totalDialogs: 0,
            successfulSaves: 0,
            failedSaves: 0,
            averageSaveTime: 0,
            languagesEdited: new Set()
        };
    }
}

// 使用示例
const dialogManager = new TranslationDialogManager();

// 创建翻译对话框
const translationDialog = dialogManager.createTranslationDialog({
    fieldName: 'name',
    resId: 1,
    resModel: 'product.product',
    userLanguageValue: 'Product Name',
    onSave: async () => {
        console.log('Translation saved');
    },
    close: () => {
        console.log('Dialog closed');
    }
});

// 获取统计信息
const stats = dialogManager.getDialogStatistics();
console.log('Translation dialog statistics:', stats);
```

## 技术特点

### 1. 多语言支持
- **语言加载**: 动态加载系统支持的语言
- **翻译管理**: 管理多语言翻译数据
- **语言排序**: 按语言名称排序显示
- **本地化**: 完整的本地化支持

### 2. 数据管理
- **异步加载**: 异步加载翻译数据
- **变更跟踪**: 跟踪翻译的变更
- **批量保存**: 批量保存所有变更
- **数据验证**: 验证翻译数据的有效性

### 3. 用户体验
- **直观界面**: 直观的翻译编辑界面
- **实时编辑**: 实时编辑翻译内容
- **状态反馈**: 提供编辑状态反馈
- **错误处理**: 友好的错误处理

### 4. 扩展性
- **组件化**: 高度组件化的设计
- **可配置**: 支持多种配置选项
- **钩子支持**: 提供保存钩子
- **模板化**: 使用模板系统

## 设计模式

### 1. 对话框模式 (Dialog Pattern)
- **模态对话框**: 模态的翻译编辑界面
- **数据隔离**: 隔离翻译编辑数据
- **生命周期**: 管理对话框生命周期

### 2. 观察者模式 (Observer Pattern)
- **数据观察**: 观察翻译数据变化
- **状态观察**: 观察编辑状态变化
- **事件通知**: 通知保存和关闭事件

### 3. 策略模式 (Strategy Pattern)
- **保存策略**: 不同的保存处理策略
- **验证策略**: 不同的数据验证策略
- **显示策略**: 不同的数据显示策略

### 4. 命令模式 (Command Pattern)
- **编辑命令**: 封装编辑操作
- **保存命令**: 封装保存操作
- **撤销命令**: 支持撤销操作

## 注意事项

1. **数据一致性**: 确保翻译数据的一致性
2. **性能考虑**: 避免频繁的数据加载和保存
3. **用户体验**: 提供流畅的编辑体验
4. **错误处理**: 完善的错误处理和用户提示

## 扩展建议

1. **翻译记忆**: 添加翻译记忆功能
2. **自动翻译**: 集成自动翻译服务
3. **批量编辑**: 支持批量编辑功能
4. **版本控制**: 添加翻译版本控制
5. **协作编辑**: 支持多用户协作编辑

该翻译对话框组件为Odoo Web客户端提供了完整的多语言翻译编辑功能，通过直观的用户界面和完善的数据管理确保了翻译工作的高效性和准确性。

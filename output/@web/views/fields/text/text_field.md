# TextField - 文本字段

## 概述

`text_field.js` 是 Odoo Web 客户端的文本字段组件，负责处理多行文本输入和显示。该模块包含145行代码，是一个功能完整的文本处理组件，专门用于处理text类型的字段，具备自动调整大小、拼写检查、动态占位符、翻译支持、行数控制等特性，是多行文本输入的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/text/text_field.js`
- **行数**: 145
- **模块**: `@web/views/fields/text/text_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/core/utils/autoresize'            // 自动调整大小工具
'@web/core/utils/hooks'                 // 工具钩子
'@web/views/fields/dynamic_placeholder_hook' // 动态占位符钩子
'@web/views/fields/input_field_hook'    // 输入字段钩子
'@web/views/fields/parsers'             // 字段解析器
'@web/views/fields/standard_field_props' // 标准字段属性
'@web/views/fields/translation_button'  // 翻译按钮
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const TextField = class TextField extends Component {
    static template = "web.TextField";
    static components = {
        TranslationButton,
    };
    static props = {
        ...standardFieldProps,
        lineBreaks: { type: Boolean, optional: true },
        placeholder: { type: String, optional: true },
        dynamicPlaceholder: { type: Boolean, optional: true },
        dynamicPlaceholderModelReferenceField: { type: String, optional: true },
        rowCount: { type: Number, optional: true },
    };
    static defaultProps = {
        lineBreaks: true,
        dynamicPlaceholder: false,
        rowCount: 2,
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **换行支持**: 支持lineBreaks配置是否保留换行
- **占位符**: 支持placeholder配置静态占位符
- **动态占位符**: 支持dynamicPlaceholder配置动态占位符
- **模型引用**: 支持dynamicPlaceholderModelReferenceField配置模型引用字段
- **行数**: 支持rowCount配置默认行数
- **翻译组件**: 集成翻译按钮组件

### 2. 组件初始化

```javascript
setup() {
    this.divRef = useRef("div");
    this.textareaRef = useRef("textarea");
    
    if (this.props.dynamicPlaceholder) {
        this.dynamicPlaceholder = useDynamicPlaceholder(this.textareaRef);
        useExternalListener(document, "keydown", this.dynamicPlaceholder.onKeydown);
        useEffect(() =>
            this.dynamicPlaceholder.updateModel(
                this.props.dynamicPlaceholderModelReferenceField
            )
        );
    }
    
    useInputField({
        getValue: () => this.props.record.data[this.props.name] || "",
        refName: "textareaRef",
        parse: (value) => parseText(value, { lineBreaks: this.props.lineBreaks }),
    });
    
    useAutoresize(this.textareaRef, { minimumHeight: this.props.rowCount * 20 });
    useSpellCheck({ ref: this.textareaRef });
}
```

**初始化功能**:
- **引用管理**: 管理div和textarea的DOM引用
- **动态占位符**: 配置动态占位符功能
- **键盘监听**: 监听键盘事件处理动态占位符
- **输入字段**: 配置输入字段钩子
- **自动调整**: 配置自动调整大小功能
- **拼写检查**: 启用拼写检查功能

### 3. 文本解析

```javascript
function parseText(value, options = {}) {
    if (!value) {
        return "";
    }
    
    let parsedValue = String(value);
    
    if (!options.lineBreaks) {
        // 移除换行符
        parsedValue = parsedValue.replace(/\r?\n/g, " ");
    }
    
    return parsedValue;
}
```

**解析功能**:
- **空值处理**: 处理空值情况
- **字符串转换**: 确保值为字符串类型
- **换行处理**: 根据配置处理换行符
- **格式化**: 格式化文本内容

### 4. 自动调整大小

```javascript
get minimumHeight() {
    return this.props.rowCount * 20; // 每行约20px
}

get maximumHeight() {
    return 400; // 最大高度400px
}

updateHeight() {
    if (!this.textareaRef.el) {
        return;
    }
    
    const textarea = this.textareaRef.el;
    const minHeight = this.minimumHeight;
    const maxHeight = this.maximumHeight;
    
    // 重置高度以获取正确的scrollHeight
    textarea.style.height = 'auto';
    
    // 计算新高度
    const newHeight = Math.min(Math.max(textarea.scrollHeight, minHeight), maxHeight);
    
    // 设置新高度
    textarea.style.height = newHeight + 'px';
}
```

**自动调整功能**:
- **最小高度**: 根据行数计算最小高度
- **最大高度**: 设置最大高度限制
- **动态调整**: 根据内容动态调整高度
- **滚动处理**: 超过最大高度时显示滚动条

### 5. 字段注册

```javascript
const textField = {
    component: TextField,
    displayName: _t("Text"),
    supportedTypes: ["text"],
    extractProps: ({ attrs, options }) => ({
        lineBreaks: attrs.line_breaks !== "0",
        placeholder: attrs.placeholder,
        dynamicPlaceholder: options.dynamic_placeholder,
        dynamicPlaceholderModelReferenceField: options.dynamic_placeholder_model_reference_field,
        rowCount: parseInteger(attrs.row_count) || 2,
    }),
};

registry.category("fields").add("text", textField);
```

**注册功能**:
- **组件注册**: 注册文本字段组件
- **显示名称**: 设置为"Text"
- **类型支持**: 支持text类型
- **属性提取**: 提取换行、占位符、动态占位符、行数等属性

## 使用场景

### 1. 文本字段管理器

```javascript
// 文本字段管理器
class TextFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置文本字段配置
        this.textFieldConfig = {
            enableAutoResize: true,
            enableSpellCheck: true,
            enableDynamicPlaceholder: true,
            enableLineBreaks: true,
            enableWordWrap: true,
            enableCharacterCount: false,
            enableWordCount: false,
            enableAutoSave: false,
            enableFormatting: false
        };
        
        // 设置尺寸配置
        this.sizeConfig = {
            defaultRowCount: 2,
            minRowCount: 1,
            maxRowCount: 20,
            lineHeight: 20,
            minHeight: 40,
            maxHeight: 400,
            enableResponsive: true
        };
        
        // 设置格式化选项
        this.formattingOptions = {
            enableMarkdown: false,
            enableHtml: false,
            enableRichText: false,
            enableCodeHighlight: false,
            enableAutoLink: false,
            enableEmoji: false
        };
        
        // 设置验证规则
        this.validationRules = {
            enableLengthValidation: false,
            enableContentValidation: false,
            maxLength: 10000,
            minLength: 0,
            allowedPatterns: [],
            blockedPatterns: [],
            enableProfanityFilter: false
        };
        
        // 设置文本统计
        this.textStatistics = {
            totalTextFields: 0,
            totalCharacters: 0,
            totalWords: 0,
            totalLines: 0,
            averageLength: 0,
            longestText: 0,
            shortestText: Number.MAX_SAFE_INTEGER,
            autoResizeActivations: 0
        };
        
        this.initializeTextFieldSystem();
    }
    
    // 初始化文本字段系统
    initializeTextFieldSystem() {
        // 创建增强的文本字段
        this.createEnhancedTextField();
        
        // 设置自动调整系统
        this.setupAutoResizeSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置格式化系统
        this.setupFormattingSystem();
    }
    
    // 创建增强的文本字段
    createEnhancedTextField() {
        const originalField = TextField;
        
        this.EnhancedTextField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加统计功能
                this.addStatisticsFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    characterCount: 0,
                    wordCount: 0,
                    lineCount: 0,
                    isValid: true,
                    validationErrors: [],
                    lastValue: '',
                    hasChanges: false,
                    autoSaveTimer: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的文本解析
                this.enhancedParseText = (value, options = {}) => {
                    if (!value) {
                        return "";
                    }
                    
                    let parsedValue = String(value);
                    
                    // 换行处理
                    if (!options.lineBreaks) {
                        parsedValue = parsedValue.replace(/\r?\n/g, " ");
                    }
                    
                    // 格式化处理
                    if (this.formattingOptions.enableAutoLink) {
                        parsedValue = this.autoLinkUrls(parsedValue);
                    }
                    
                    // 更新统计
                    this.updateTextStatistics(parsedValue);
                    
                    return parsedValue;
                };
                
                // 自动链接URL
                this.autoLinkUrls = (text) => {
                    const urlRegex = /(https?:\/\/[^\s]+)/g;
                    return text.replace(urlRegex, '<a href="$1" target="_blank">$1</a>');
                };
                
                // 更新文本统计
                this.updateTextStatistics = (text) => {
                    this.enhancedState.characterCount = text.length;
                    this.enhancedState.wordCount = this.countWords(text);
                    this.enhancedState.lineCount = this.countLines(text);
                    
                    // 记录全局统计
                    this.recordGlobalStatistics(text);
                };
                
                // 计算单词数
                this.countWords = (text) => {
                    if (!text.trim()) return 0;
                    return text.trim().split(/\s+/).length;
                };
                
                // 计算行数
                this.countLines = (text) => {
                    if (!text) return 0;
                    return text.split(/\r?\n/).length;
                };
                
                // 验证文本内容
                this.validateText = (text) => {
                    const errors = [];
                    
                    // 长度验证
                    if (this.validationRules.enableLengthValidation) {
                        if (text.length < this.validationRules.minLength) {
                            errors.push(`Text must be at least ${this.validationRules.minLength} characters`);
                        }
                        if (text.length > this.validationRules.maxLength) {
                            errors.push(`Text must not exceed ${this.validationRules.maxLength} characters`);
                        }
                    }
                    
                    // 内容验证
                    if (this.validationRules.enableContentValidation) {
                        // 允许的模式
                        for (const pattern of this.validationRules.allowedPatterns) {
                            if (!new RegExp(pattern).test(text)) {
                                errors.push(`Text must match pattern: ${pattern}`);
                            }
                        }
                        
                        // 禁止的模式
                        for (const pattern of this.validationRules.blockedPatterns) {
                            if (new RegExp(pattern).test(text)) {
                                errors.push(`Text contains blocked content: ${pattern}`);
                            }
                        }
                    }
                    
                    // 脏话过滤
                    if (this.validationRules.enableProfanityFilter) {
                        if (this.containsProfanity(text)) {
                            errors.push('Text contains inappropriate content');
                        }
                    }
                    
                    this.enhancedState.validationErrors = errors;
                    this.enhancedState.isValid = errors.length === 0;
                    
                    return errors.length === 0;
                };
                
                // 检查脏话
                this.containsProfanity = (text) => {
                    // 简化的脏话检查逻辑
                    const profanityWords = ['badword1', 'badword2']; // 实际应该从配置加载
                    const lowerText = text.toLowerCase();
                    return profanityWords.some(word => lowerText.includes(word));
                };
                
                // 自动保存
                this.setupAutoSave = () => {
                    if (!this.textFieldConfig.enableAutoSave) {
                        return;
                    }
                    
                    const autoSaveDelay = 2000; // 2秒延迟
                    
                    this.onTextChange = (value) => {
                        this.enhancedState.hasChanges = true;
                        
                        // 清除之前的定时器
                        if (this.enhancedState.autoSaveTimer) {
                            clearTimeout(this.enhancedState.autoSaveTimer);
                        }
                        
                        // 设置新的定时器
                        this.enhancedState.autoSaveTimer = setTimeout(() => {
                            this.saveText(value);
                        }, autoSaveDelay);
                    };
                };
                
                // 保存文本
                this.saveText = async (value) => {
                    try {
                        await this.props.record.update({ [this.props.name]: value });
                        await this.props.record.save();
                        
                        this.enhancedState.hasChanges = false;
                        this.enhancedState.lastValue = value;
                        
                    } catch (error) {
                        console.error('Auto-save failed:', error);
                    }
                };
                
                // 获取文本信息
                this.getTextInfo = () => {
                    return {
                        characterCount: this.enhancedState.characterCount,
                        wordCount: this.enhancedState.wordCount,
                        lineCount: this.enhancedState.lineCount,
                        isValid: this.enhancedState.isValid,
                        validationErrors: this.enhancedState.validationErrors,
                        hasChanges: this.enhancedState.hasChanges,
                        lastValue: this.enhancedState.lastValue,
                        maxLength: this.validationRules.maxLength,
                        remainingCharacters: this.validationRules.maxLength - this.enhancedState.characterCount
                    };
                };
                
                // 格式化文本
                this.formatText = (text, format) => {
                    switch (format) {
                        case 'uppercase':
                            return text.toUpperCase();
                        case 'lowercase':
                            return text.toLowerCase();
                        case 'capitalize':
                            return text.replace(/\b\w/g, l => l.toUpperCase());
                        case 'trim':
                            return text.trim();
                        case 'removeExtraSpaces':
                            return text.replace(/\s+/g, ' ').trim();
                        default:
                            return text;
                    }
                };
                
                // 记录全局统计
                this.recordGlobalStatistics = (text) => {
                    this.textStatistics.totalCharacters += text.length;
                    this.textStatistics.totalWords += this.countWords(text);
                    this.textStatistics.totalLines += this.countLines(text);
                    
                    // 更新最长最短文本
                    if (text.length > this.textStatistics.longestText) {
                        this.textStatistics.longestText = text.length;
                    }
                    if (text.length < this.textStatistics.shortestText) {
                        this.textStatistics.shortestText = text.length;
                    }
                    
                    // 更新平均长度
                    this.updateAverageLength();
                };
                
                // 更新平均长度
                this.updateAverageLength = () => {
                    if (this.textStatistics.totalTextFields > 0) {
                        this.textStatistics.averageLength = 
                            this.textStatistics.totalCharacters / this.textStatistics.totalTextFields;
                    }
                };
            }
            
            addStatisticsFeatures() {
                // 统计功能
                this.statisticsManager = {
                    enabled: this.textFieldConfig.enableCharacterCount || this.textFieldConfig.enableWordCount,
                    getInfo: () => this.getTextInfo(),
                    updateStats: (text) => this.updateTextStatistics(text)
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.validationRules.enableLengthValidation || this.validationRules.enableContentValidation,
                    validate: (text) => this.validateText(text),
                    getErrors: () => this.enhancedState.validationErrors,
                    isValid: () => this.enhancedState.isValid
                };
            }
        };
    }
    
    // 设置自动调整系统
    setupAutoResizeSystem() {
        this.autoResizeSystemConfig = {
            enabled: this.textFieldConfig.enableAutoResize,
            config: this.sizeConfig
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.validationRules.enableLengthValidation || this.validationRules.enableContentValidation,
            rules: this.validationRules
        };
    }
    
    // 设置格式化系统
    setupFormattingSystem() {
        this.formattingSystemConfig = {
            enabled: this.textFieldConfig.enableFormatting,
            options: this.formattingOptions
        };
    }
    
    // 创建文本字段
    createTextField(props) {
        const field = new this.EnhancedTextField(props);
        this.textStatistics.totalTextFields++;
        return field;
    }
    
    // 获取文本统计
    getTextStatistics() {
        return {
            ...this.textStatistics,
            averageWordsPerField: this.textStatistics.totalWords / Math.max(this.textStatistics.totalTextFields, 1),
            averageLinesPerField: this.textStatistics.totalLines / Math.max(this.textStatistics.totalTextFields, 1),
            configuredFeatures: {
                autoResize: this.textFieldConfig.enableAutoResize,
                spellCheck: this.textFieldConfig.enableSpellCheck,
                dynamicPlaceholder: this.textFieldConfig.enableDynamicPlaceholder,
                characterCount: this.textFieldConfig.enableCharacterCount,
                wordCount: this.textFieldConfig.enableWordCount
            }
        };
    }
    
    // 销毁管理器
    destroy() {
        // 重置统计
        this.textStatistics = {
            totalTextFields: 0,
            totalCharacters: 0,
            totalWords: 0,
            totalLines: 0,
            averageLength: 0,
            longestText: 0,
            shortestText: Number.MAX_SAFE_INTEGER,
            autoResizeActivations: 0
        };
    }
}

// 使用示例
const textFieldManager = new TextFieldManager();

// 创建文本字段
const textField = textFieldManager.createTextField({
    name: 'description',
    record: {
        data: { 
            description: 'This is a sample text content\nwith multiple lines.'
        },
        fields: { 
            description: { 
                type: 'text',
                string: 'Description'
            }
        }
    },
    lineBreaks: true,
    placeholder: 'Enter description...',
    rowCount: 3
});

// 获取统计信息
const stats = textFieldManager.getTextStatistics();
console.log('Text field statistics:', stats);
```

## 技术特点

### 1. 自动调整大小
- **动态高度**: 根据内容自动调整高度
- **最小高度**: 设置最小高度限制
- **最大高度**: 设置最大高度限制
- **滚动支持**: 超过最大高度时显示滚动条

### 2. 增强输入
- **拼写检查**: 内置拼写检查功能
- **动态占位符**: 支持动态占位符
- **换行控制**: 控制是否保留换行符
- **输入钩子**: 使用输入字段钩子

### 3. 翻译支持
- **翻译按钮**: 集成翻译按钮组件
- **多语言**: 支持多语言文本编辑
- **翻译状态**: 显示翻译状态
- **语言切换**: 支持语言切换

### 4. 用户体验
- **响应式**: 响应式设计适配不同屏幕
- **键盘支持**: 完整的键盘操作支持
- **无障碍**: 支持无障碍访问
- **性能优化**: 优化大文本的处理性能

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 为基础文本输入添加各种功能
- **钩子装饰**: 使用钩子装饰组件功能
- **增强装饰**: 装饰输入行为

### 2. 策略模式 (Strategy Pattern)
- **解析策略**: 不同的文本解析策略
- **验证策略**: 不同的验证策略
- **格式化策略**: 不同的格式化策略

### 3. 观察者模式 (Observer Pattern)
- **内容观察**: 观察文本内容变化
- **大小观察**: 观察尺寸变化
- **状态观察**: 观察组件状态变化

### 4. 适配器模式 (Adapter Pattern)
- **钩子适配**: 适配不同的钩子接口
- **事件适配**: 适配不同的事件处理
- **数据适配**: 适配不同的数据格式

## 注意事项

1. **性能考虑**: 大文本时的性能优化
2. **内存管理**: 避免内存泄漏
3. **用户体验**: 提供流畅的输入体验
4. **数据安全**: 确保文本数据的安全性

## 扩展建议

1. **富文本编辑**: 添加富文本编辑功能
2. **代码高亮**: 支持代码语法高亮
3. **Markdown支持**: 支持Markdown格式
4. **协作编辑**: 支持多人协作编辑
5. **版本控制**: 添加文本版本控制

该文本字段为Odoo Web客户端提供了完整的多行文本输入功能，通过自动调整大小、拼写检查和动态占位符等特性确保了文本编辑的便利性和用户体验。

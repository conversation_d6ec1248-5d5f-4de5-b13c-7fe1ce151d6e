# StandardFieldProps - 标准字段属性

## 概述

`standard_field_props.js` 是 Odoo Web 客户端字段系统的标准字段属性定义模块，负责定义所有字段组件的标准属性规范。该模块包含24行代码，是一个简洁的属性定义模块，专门用于提供字段组件的通用属性类型定义，具备类型安全、属性验证、标准化接口、可选配置等特性，是字段系统的基础规范模块。

## 文件信息
- **路径**: `/web/static/src/views/fields/standard_field_props.js`
- **行数**: 24
- **模块**: `@web/views/fields/standard_field_props`

## 依赖关系

```javascript
// 无外部依赖
// 纯属性定义模块
```

## 核心功能

### 1. 标准字段属性定义

```javascript
/**
 * @typedef StandardFieldProps
 * @property {string} [id]
 * @property {string} name
 * @property {boolean} [readonly]
 * @property {import("@web/model/relational_model/record").Record} record
 */

const standardFieldProps = {
    id: { type: String, optional: true },
    name: { type: String },
    readonly: { type: Boolean, optional: true },
    record: { type: Object },
};
```

**属性定义功能**:
- **id**: 可选的字段标识符
- **name**: 必需的字段名称
- **readonly**: 可选的只读状态
- **record**: 必需的记录对象

### 2. 属性类型规范

**必需属性**:
- `name`: 字段名称，用于标识字段
- `record`: 记录对象，包含字段数据和操作方法

**可选属性**:
- `id`: 字段的唯一标识符
- `readonly`: 字段的只读状态控制

### 3. TypeScript类型定义

```typescript
interface StandardFieldProps {
    id?: string;
    name: string;
    readonly?: boolean;
    record: Record;
}
```

**类型安全功能**:
- **静态检查**: 提供编译时类型检查
- **智能提示**: IDE智能提示支持
- **文档生成**: 自动生成API文档
- **错误预防**: 预防类型错误

## 使用场景

### 1. 标准字段属性管理器

```javascript
// 标准字段属性管理器
class StandardFieldPropsManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置属性配置
        this.propsConfig = {
            enableValidation: true,
            enableTypeChecking: true,
            enableDefaultValues: true,
            enableExtensions: true,
            strictMode: false,
            customProps: new Map()
        };
        
        // 设置属性注册表
        this.propsRegistry = new Map();
        
        // 设置验证器
        this.validators = new Map();
        
        // 设置属性统计
        this.propsStatistics = {
            totalProps: 0,
            validatedProps: 0,
            invalidProps: 0,
            customProps: 0,
            averageValidationTime: 0
        };
        
        this.initializePropsSystem();
    }
    
    // 初始化属性系统
    initializePropsSystem() {
        // 注册标准属性
        this.registerStandardProps();
        
        // 创建增强的属性系统
        this.createEnhancedPropsSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置扩展系统
        this.setupExtensionSystem();
    }
    
    // 注册标准属性
    registerStandardProps() {
        // 注册基础标准属性
        this.propsRegistry.set('standard', {
            props: standardFieldProps,
            description: 'Standard field properties',
            version: '1.0.0',
            required: ['name', 'record'],
            optional: ['id', 'readonly']
        });
        
        // 注册扩展属性集
        this.registerExtendedProps();
    }
    
    // 注册扩展属性
    registerExtendedProps() {
        // 表单字段属性
        this.propsRegistry.set('form_field', {
            props: {
                ...standardFieldProps,
                label: { type: String, optional: true },
                help: { type: String, optional: true },
                placeholder: { type: String, optional: true },
                required: { type: Boolean, optional: true },
                invisible: { type: Boolean, optional: true }
            },
            description: 'Form field properties',
            extends: 'standard'
        });
        
        // 列表字段属性
        this.propsRegistry.set('list_field', {
            props: {
                ...standardFieldProps,
                sortable: { type: Boolean, optional: true },
                width: { type: String, optional: true },
                align: { type: String, optional: true }
            },
            description: 'List field properties',
            extends: 'standard'
        });
        
        // 关系字段属性
        this.propsRegistry.set('relational_field', {
            props: {
                ...standardFieldProps,
                domain: { type: Array, optional: true },
                context: { type: Object, optional: true },
                options: { type: Object, optional: true }
            },
            description: 'Relational field properties',
            extends: 'standard'
        });
        
        // 数字字段属性
        this.propsRegistry.set('numeric_field', {
            props: {
                ...standardFieldProps,
                digits: { type: Array, optional: true },
                step: { type: Number, optional: true },
                min: { type: Number, optional: true },
                max: { type: Number, optional: true }
            },
            description: 'Numeric field properties',
            extends: 'standard'
        });
    }
    
    // 创建增强的属性系统
    createEnhancedPropsSystem() {
        this.enhancedStandardFieldProps = {
            ...standardFieldProps,
            
            // 增强的属性验证
            validate: (props) => this.validateProps(props, 'standard'),
            
            // 属性合并
            merge: (baseProps, additionalProps) => this.mergeProps(baseProps, additionalProps),
            
            // 属性转换
            transform: (props, transformer) => this.transformProps(props, transformer),
            
            // 属性克隆
            clone: (props) => this.cloneProps(props),
            
            // 属性比较
            compare: (props1, props2) => this.compareProps(props1, props2),
            
            // 属性序列化
            serialize: (props) => this.serializeProps(props),
            
            // 属性反序列化
            deserialize: (serialized) => this.deserializeProps(serialized),
            
            // 获取默认值
            getDefaults: () => this.getDefaultProps('standard'),
            
            // 检查必需属性
            checkRequired: (props) => this.checkRequiredProps(props, 'standard'),
            
            // 获取属性信息
            getInfo: (propName) => this.getPropInfo(propName, 'standard')
        };
    }
    
    // 验证属性
    validateProps(props, propsType = 'standard') {
        const startTime = performance.now();
        
        try {
            const propsInfo = this.propsRegistry.get(propsType);
            if (!propsInfo) {
                throw new Error(`Unknown props type: ${propsType}`);
            }
            
            const errors = [];
            
            // 检查必需属性
            for (const requiredProp of propsInfo.required || []) {
                if (!(requiredProp in props)) {
                    errors.push(`Missing required property: ${requiredProp}`);
                }
            }
            
            // 验证属性类型
            for (const [propName, propValue] of Object.entries(props)) {
                const propDef = propsInfo.props[propName];
                if (!propDef) {
                    if (this.propsConfig.strictMode) {
                        errors.push(`Unknown property: ${propName}`);
                    }
                    continue;
                }
                
                const typeError = this.validatePropType(propName, propValue, propDef);
                if (typeError) {
                    errors.push(typeError);
                }
            }
            
            // 自定义验证
            const customErrors = this.runCustomValidation(props, propsType);
            errors.push(...customErrors);
            
            // 记录统计
            if (errors.length === 0) {
                this.propsStatistics.validatedProps++;
            } else {
                this.propsStatistics.invalidProps++;
            }
            
            // 记录性能
            const endTime = performance.now();
            this.recordValidationTime(endTime - startTime);
            
            return {
                isValid: errors.length === 0,
                errors: errors
            };
            
        } catch (error) {
            return {
                isValid: false,
                errors: [error.message]
            };
        }
    }
    
    // 验证属性类型
    validatePropType(propName, propValue, propDef) {
        if (propValue === null || propValue === undefined) {
            if (!propDef.optional) {
                return `Property ${propName} is required`;
            }
            return null;
        }
        
        const expectedType = propDef.type;
        const actualType = typeof propValue;
        
        // 基础类型检查
        if (expectedType === String && actualType !== 'string') {
            return `Property ${propName} should be string, got ${actualType}`;
        }
        
        if (expectedType === Number && actualType !== 'number') {
            return `Property ${propName} should be number, got ${actualType}`;
        }
        
        if (expectedType === Boolean && actualType !== 'boolean') {
            return `Property ${propName} should be boolean, got ${actualType}`;
        }
        
        if (expectedType === Array && !Array.isArray(propValue)) {
            return `Property ${propName} should be array, got ${actualType}`;
        }
        
        if (expectedType === Object && (actualType !== 'object' || Array.isArray(propValue))) {
            return `Property ${propName} should be object, got ${actualType}`;
        }
        
        return null;
    }
    
    // 运行自定义验证
    runCustomValidation(props, propsType) {
        const errors = [];
        const validator = this.validators.get(propsType);
        
        if (validator) {
            try {
                const result = validator(props);
                if (result !== true) {
                    if (typeof result === 'string') {
                        errors.push(result);
                    } else if (Array.isArray(result)) {
                        errors.push(...result);
                    }
                }
            } catch (error) {
                errors.push(`Validation error: ${error.message}`);
            }
        }
        
        return errors;
    }
    
    // 合并属性
    mergeProps(baseProps, additionalProps) {
        return {
            ...baseProps,
            ...additionalProps
        };
    }
    
    // 转换属性
    transformProps(props, transformer) {
        if (typeof transformer === 'function') {
            return transformer(props);
        }
        
        if (typeof transformer === 'object') {
            const transformed = { ...props };
            
            for (const [key, transform] of Object.entries(transformer)) {
                if (key in transformed) {
                    transformed[key] = transform(transformed[key]);
                }
            }
            
            return transformed;
        }
        
        return props;
    }
    
    // 克隆属性
    cloneProps(props) {
        return JSON.parse(JSON.stringify(props));
    }
    
    // 比较属性
    compareProps(props1, props2) {
        const keys1 = Object.keys(props1);
        const keys2 = Object.keys(props2);
        
        if (keys1.length !== keys2.length) {
            return false;
        }
        
        for (const key of keys1) {
            if (props1[key] !== props2[key]) {
                return false;
            }
        }
        
        return true;
    }
    
    // 序列化属性
    serializeProps(props) {
        return JSON.stringify(props);
    }
    
    // 反序列化属性
    deserializeProps(serialized) {
        try {
            return JSON.parse(serialized);
        } catch (error) {
            throw new Error(`Failed to deserialize props: ${error.message}`);
        }
    }
    
    // 获取默认属性
    getDefaultProps(propsType) {
        const propsInfo = this.propsRegistry.get(propsType);
        if (!propsInfo) {
            return {};
        }
        
        const defaults = {};
        
        for (const [propName, propDef] of Object.entries(propsInfo.props)) {
            if ('default' in propDef) {
                defaults[propName] = propDef.default;
            }
        }
        
        return defaults;
    }
    
    // 检查必需属性
    checkRequiredProps(props, propsType) {
        const propsInfo = this.propsRegistry.get(propsType);
        if (!propsInfo) {
            return [];
        }
        
        const missing = [];
        
        for (const requiredProp of propsInfo.required || []) {
            if (!(requiredProp in props)) {
                missing.push(requiredProp);
            }
        }
        
        return missing;
    }
    
    // 获取属性信息
    getPropInfo(propName, propsType) {
        const propsInfo = this.propsRegistry.get(propsType);
        if (!propsInfo) {
            return null;
        }
        
        return propsInfo.props[propName] || null;
    }
    
    // 记录验证时间
    recordValidationTime(duration) {
        this.propsStatistics.totalProps++;
        this.propsStatistics.averageValidationTime = 
            (this.propsStatistics.averageValidationTime * (this.propsStatistics.totalProps - 1) + duration) / 
            this.propsStatistics.totalProps;
    }
    
    // 设置验证系统
    setupValidationSystem() {
        // 注册标准验证器
        this.validators.set('standard', (props) => {
            const errors = [];
            
            // 验证记录对象
            if (props.record && typeof props.record.data !== 'object') {
                errors.push('Record must have a data property');
            }
            
            // 验证字段名称
            if (props.name && !/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(props.name)) {
                errors.push('Field name must be a valid identifier');
            }
            
            return errors.length === 0 ? true : errors;
        });
    }
    
    // 设置扩展系统
    setupExtensionSystem() {
        this.extensionConfig = {
            enableCustomProps: true,
            enablePropInheritance: true,
            enablePropOverrides: true
        };
    }
    
    // 注册自定义属性类型
    registerCustomPropsType(name, propsDefinition) {
        this.propsRegistry.set(name, {
            ...propsDefinition,
            isCustom: true
        });
        
        this.propsStatistics.customProps++;
    }
    
    // 注册自定义验证器
    registerCustomValidator(propsType, validator) {
        this.validators.set(propsType, validator);
    }
    
    // 扩展属性类型
    extendPropsType(baseName, extensionName, additionalProps) {
        const baseProps = this.propsRegistry.get(baseName);
        if (!baseProps) {
            throw new Error(`Base props type ${baseName} not found`);
        }
        
        this.propsRegistry.set(extensionName, {
            props: {
                ...baseProps.props,
                ...additionalProps
            },
            description: `Extended ${baseName} properties`,
            extends: baseName
        });
    }
    
    // 获取属性类型信息
    getPropsTypeInfo(propsType) {
        return this.propsRegistry.get(propsType);
    }
    
    // 获取所有属性类型
    getAllPropsTypes() {
        return Array.from(this.propsRegistry.keys());
    }
    
    // 获取属性统计
    getPropsStatistics() {
        return {
            ...this.propsStatistics,
            registeredTypes: this.propsRegistry.size,
            validatorCount: this.validators.size
        };
    }
    
    // 清理缓存
    clearCache() {
        // 实现缓存清理逻辑
        console.log('Props cache cleared');
    }
    
    // 销毁管理器
    destroy() {
        // 清理注册表
        this.propsRegistry.clear();
        this.validators.clear();
        
        // 重置统计
        this.propsStatistics = {
            totalProps: 0,
            validatedProps: 0,
            invalidProps: 0,
            customProps: 0,
            averageValidationTime: 0
        };
    }
}

// 使用示例
const propsManager = new StandardFieldPropsManager();

// 验证标准属性
const props = {
    name: 'customer_name',
    record: { data: { customer_name: 'John Doe' } },
    readonly: false
};

const validation = propsManager.validateProps(props);
console.log('Validation result:', validation);

// 注册自定义属性类型
propsManager.registerCustomPropsType('custom_field', {
    props: {
        ...standardFieldProps,
        customOption: { type: String, optional: true }
    },
    description: 'Custom field properties',
    required: ['name', 'record']
});

// 扩展属性类型
propsManager.extendPropsType('standard', 'enhanced_field', {
    tooltip: { type: String, optional: true },
    icon: { type: String, optional: true }
});

// 获取统计信息
const stats = propsManager.getPropsStatistics();
console.log('Props statistics:', stats);
```

## 技术特点

### 1. 类型安全
- **静态类型**: 提供静态类型定义
- **运行时验证**: 支持运行时类型验证
- **错误预防**: 预防类型相关错误
- **智能提示**: IDE智能提示支持

### 2. 标准化
- **统一接口**: 提供统一的字段属性接口
- **一致性**: 确保所有字段的一致性
- **可预测**: 可预测的属性行为
- **文档化**: 完整的属性文档

### 3. 扩展性
- **可扩展**: 支持属性的扩展
- **可定制**: 支持自定义属性
- **继承性**: 支持属性继承
- **组合性**: 支持属性组合

### 4. 简洁性
- **最小化**: 最小化的属性定义
- **核心属性**: 只包含核心必需属性
- **清晰性**: 清晰的属性定义
- **易理解**: 易于理解和使用

## 设计模式

### 1. 规范模式 (Specification Pattern)
- **属性规范**: 定义字段属性的规范
- **验证规范**: 定义属性验证规范
- **类型规范**: 定义类型检查规范

### 2. 模板模式 (Template Pattern)
- **属性模板**: 提供属性定义模板
- **验证模板**: 提供验证逻辑模板
- **扩展模板**: 提供扩展机制模板

### 3. 策略模式 (Strategy Pattern)
- **验证策略**: 不同的属性验证策略
- **类型策略**: 不同的类型检查策略
- **扩展策略**: 不同的属性扩展策略

### 4. 工厂模式 (Factory Pattern)
- **属性工厂**: 创建标准属性定义
- **验证器工厂**: 创建属性验证器
- **扩展工厂**: 创建属性扩展

## 注意事项

1. **类型一致性**: 确保属性类型的一致性
2. **向后兼容**: 保持向后兼容性
3. **性能考虑**: 避免过度的类型检查
4. **文档维护**: 保持属性文档的更新

## 扩展建议

1. **更多类型**: 支持更多复杂类型定义
2. **条件属性**: 支持条件性属性
3. **属性组**: 支持属性分组
4. **动态属性**: 支持动态属性定义
5. **属性继承**: 增强属性继承机制

该标准字段属性模块为Odoo Web客户端提供了统一的字段属性规范，通过简洁的属性定义和类型安全确保了字段系统的一致性和可靠性。

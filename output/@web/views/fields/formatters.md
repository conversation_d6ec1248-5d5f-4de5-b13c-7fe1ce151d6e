# Formatters - 字段格式化器

## 概述

`formatters.js` 是 Odoo Web 客户端字段系统的格式化器模块，负责将各种数据类型格式化为用户友好的显示格式。该模块包含477行代码，提供了丰富的格式化函数，包括数字、日期、货币、二进制、布尔值、选择项等各种数据类型的格式化功能，具备国际化支持、本地化适配、精度控制、自定义格式等特性，是字段显示系统的核心工具模块。

## 文件信息
- **路径**: `/web/static/src/views/fields/formatters.js`
- **行数**: 477
- **模块**: `@web/views/fields/formatters`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/dates'              // 日期本地化
'@web/core/l10n/localization'       // 本地化服务
'@web/core/l10n/translation'        // 翻译服务
'@web/core/registry'                // 注册表服务
'@web/core/utils/binary'            // 二进制工具
'@web/core/utils/numbers'           // 数字工具
'@web/core/utils/strings'           // 字符串工具
'@odoo/owl'                         // OWL框架
'@web/core/currency'                // 货币服务
```

## 核心功能

### 1. 二进制格式化

```javascript
function formatBinary(value) {
    if (!isBinarySize(value)) {
        // Computing approximate size out of base64 encoded string
        // http://en.wikipedia.org/wiki/Base64#MIME
        return humanSize(value.length * 3 / 4);
    }
    return humanSize(value);
}
```

**二进制格式化功能**:
- **大小计算**: 计算二进制数据的大小
- **Base64处理**: 处理Base64编码的数据
- **人性化显示**: 将字节数转换为可读格式
- **单位转换**: 自动选择合适的单位（Bytes, KB, MB等）

### 2. 布尔值格式化

```javascript
function formatBoolean(value, field, options = {}) {
    if (value === undefined || value === null) {
        return "";
    }
    return value ? "✓" : "✗";
}
```

**布尔值格式化功能**:
- **符号显示**: 使用符号表示真假值
- **空值处理**: 处理undefined和null值
- **自定义选项**: 支持自定义显示选项
- **国际化**: 支持不同语言的布尔值显示

### 3. 字符串格式化

```javascript
function formatChar(value, field, options = {}) {
    if (value === false || value === undefined || value === null) {
        return "";
    }
    if (options.isPassword) {
        return "*".repeat(value.length);
    }
    return value;
}
```

**字符串格式化功能**:
- **空值处理**: 统一处理各种空值情况
- **密码模式**: 支持密码字段的掩码显示
- **原样返回**: 对于普通字符串原样返回
- **安全处理**: 防止XSS攻击的安全处理

### 4. 日期时间格式化

```javascript
function formatDate(value, field, options = {}) {
    if (!value) {
        return "";
    }
    return _formatDate(value, options);
}

function formatDateTime(value, field, options = {}) {
    if (!value) {
        return "";
    }
    return _formatDateTime(value, options);
}
```

**日期时间格式化功能**:
- **本地化**: 根据用户本地化设置格式化
- **时区处理**: 处理时区转换
- **格式选项**: 支持多种日期时间格式
- **空值处理**: 统一的空值处理逻辑

### 5. 数字格式化

```javascript
function formatFloat(value, field, options = {}) {
    if (value === false || value === undefined || value === null) {
        return "";
    }
    const digits = options.digits || field.digits;
    return formatFloatNumber(value, { digits });
}

function formatInteger(value, field, options = {}) {
    if (value === false || value === undefined || value === null) {
        return "";
    }
    return formatFloatNumber(value, { digits: [false, 0] });
}
```

**数字格式化功能**:
- **精度控制**: 支持小数位数控制
- **千分位**: 自动添加千分位分隔符
- **本地化**: 根据本地化设置格式化数字
- **整数处理**: 专门的整数格式化逻辑

### 6. 货币格式化

```javascript
function formatMonetary(value, field, options = {}) {
    if (value === false || value === undefined || value === null) {
        return "";
    }
    const currency = options.currency;
    const digits = options.digits || field.digits;
    return formatCurrency(value, currency, { digits });
}
```

**货币格式化功能**:
- **货币符号**: 自动添加货币符号
- **汇率处理**: 支持多货币汇率转换
- **精度控制**: 货币特定的精度控制
- **本地化**: 根据地区格式化货币显示

## 使用场景

### 1. 字段格式化管理器

```javascript
// 字段格式化管理器
class FieldFormatterManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置格式化器配置
        this.formatterConfig = {
            enableCaching: true,
            enableCustomFormatters: true,
            enableValidation: true,
            enableLocalization: true,
            defaultLocale: 'en_US',
            cacheSize: 1000,
            customFormatters: new Map()
        };
        
        // 设置格式化器注册表
        this.formatterRegistry = new Map();
        
        // 设置缓存系统
        this.formatCache = new Map();
        
        // 设置格式化统计
        this.formatterStatistics = {
            totalFormats: 0,
            cacheHits: 0,
            cacheMisses: 0,
            customFormatterCount: 0,
            averageFormatTime: 0
        };
        
        this.initializeFormatterSystem();
    }
    
    // 初始化格式化器系统
    initializeFormatterSystem() {
        // 注册默认格式化器
        this.registerDefaultFormatters();
        
        // 设置缓存系统
        this.setupCacheSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置性能监控
        this.setupPerformanceMonitoring();
    }
    
    // 注册默认格式化器
    registerDefaultFormatters() {
        // 基础类型格式化器
        this.formatterRegistry.set('binary', {
            formatter: formatBinary,
            description: 'Format binary data size',
            supportedTypes: ['binary']
        });
        
        this.formatterRegistry.set('boolean', {
            formatter: formatBoolean,
            description: 'Format boolean values',
            supportedTypes: ['boolean']
        });
        
        this.formatterRegistry.set('char', {
            formatter: formatChar,
            description: 'Format character strings',
            supportedTypes: ['char', 'text']
        });
        
        this.formatterRegistry.set('date', {
            formatter: formatDate,
            description: 'Format date values',
            supportedTypes: ['date']
        });
        
        this.formatterRegistry.set('datetime', {
            formatter: formatDateTime,
            description: 'Format datetime values',
            supportedTypes: ['datetime']
        });
        
        this.formatterRegistry.set('float', {
            formatter: formatFloat,
            description: 'Format floating point numbers',
            supportedTypes: ['float']
        });
        
        this.formatterRegistry.set('integer', {
            formatter: formatInteger,
            description: 'Format integer numbers',
            supportedTypes: ['integer']
        });
        
        this.formatterRegistry.set('monetary', {
            formatter: formatMonetary,
            description: 'Format monetary values',
            supportedTypes: ['monetary']
        });
        
        // 增强格式化器
        this.createEnhancedFormatters();
    }
    
    // 创建增强格式化器
    createEnhancedFormatters() {
        // 增强的数字格式化器
        this.enhancedNumberFormatter = (value, field, options = {}) => {
            const startTime = performance.now();
            
            try {
                // 检查缓存
                const cacheKey = this.generateCacheKey('number', value, field, options);
                if (this.formatterConfig.enableCaching && this.formatCache.has(cacheKey)) {
                    this.formatterStatistics.cacheHits++;
                    return this.formatCache.get(cacheKey);
                }
                
                // 执行格式化
                let result;
                if (field.type === 'integer') {
                    result = formatInteger(value, field, options);
                } else {
                    result = formatFloat(value, field, options);
                }
                
                // 应用自定义格式化
                if (options.customFormat) {
                    result = this.applyCustomFormat(result, options.customFormat);
                }
                
                // 缓存结果
                if (this.formatterConfig.enableCaching) {
                    this.formatCache.set(cacheKey, result);
                    this.formatterStatistics.cacheMisses++;
                }
                
                // 记录性能
                const endTime = performance.now();
                this.recordPerformance('number_format', endTime - startTime);
                
                return result;
                
            } catch (error) {
                console.error('Number formatting error:', error);
                return value?.toString() || '';
            }
        };
        
        // 增强的日期格式化器
        this.enhancedDateFormatter = (value, field, options = {}) => {
            const startTime = performance.now();
            
            try {
                // 检查缓存
                const cacheKey = this.generateCacheKey('date', value, field, options);
                if (this.formatterConfig.enableCaching && this.formatCache.has(cacheKey)) {
                    this.formatterStatistics.cacheHits++;
                    return this.formatCache.get(cacheKey);
                }
                
                // 执行格式化
                let result;
                if (field.type === 'datetime') {
                    result = formatDateTime(value, field, options);
                } else {
                    result = formatDate(value, field, options);
                }
                
                // 应用相对时间格式化
                if (options.relative) {
                    result = this.formatRelativeTime(value, options);
                }
                
                // 缓存结果
                if (this.formatterConfig.enableCaching) {
                    this.formatCache.set(cacheKey, result);
                    this.formatterStatistics.cacheMisses++;
                }
                
                // 记录性能
                const endTime = performance.now();
                this.recordPerformance('date_format', endTime - startTime);
                
                return result;
                
            } catch (error) {
                console.error('Date formatting error:', error);
                return value?.toString() || '';
            }
        };
        
        // 增强的选择项格式化器
        this.enhancedSelectionFormatter = (value, field, options = {}) => {
            try {
                if (!value || !field.selection) {
                    return '';
                }
                
                // 查找选择项
                const selection = field.selection.find(item => item[0] === value);
                if (!selection) {
                    return value;
                }
                
                let result = selection[1];
                
                // 应用图标
                if (options.showIcon && options.icons && options.icons[value]) {
                    result = `<i class="${options.icons[value]}"></i> ${result}`;
                }
                
                // 应用颜色
                if (options.showColor && options.colors && options.colors[value]) {
                    result = `<span style="color: ${options.colors[value]}">${result}</span>`;
                }
                
                return result;
                
            } catch (error) {
                console.error('Selection formatting error:', error);
                return value?.toString() || '';
            }
        };
    }
    
    // 生成缓存键
    generateCacheKey(type, value, field, options) {
        return `${type}_${JSON.stringify({ value, fieldType: field.type, options })}`;
    }
    
    // 应用自定义格式化
    applyCustomFormat(value, customFormat) {
        if (typeof customFormat === 'function') {
            return customFormat(value);
        }
        
        if (typeof customFormat === 'string') {
            // 简单的模板替换
            return customFormat.replace('{value}', value);
        }
        
        return value;
    }
    
    // 格式化相对时间
    formatRelativeTime(value, options) {
        const now = new Date();
        const date = new Date(value);
        const diffMs = now - date;
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        
        if (diffDays === 0) {
            return 'Today';
        } else if (diffDays === 1) {
            return 'Yesterday';
        } else if (diffDays < 7) {
            return `${diffDays} days ago`;
        } else {
            return formatDate(value, { type: 'date' }, options);
        }
    }
    
    // 设置缓存系统
    setupCacheSystem() {
        this.cacheConfig = {
            maxSize: this.formatterConfig.cacheSize,
            ttl: 300000, // 5分钟
            cleanupInterval: 60000 // 1分钟
        };
        
        // 定期清理缓存
        setInterval(() => {
            this.cleanupCache();
        }, this.cacheConfig.cleanupInterval);
    }
    
    // 清理缓存
    cleanupCache() {
        if (this.formatCache.size > this.cacheConfig.maxSize) {
            // 清理最旧的缓存项
            const entries = Array.from(this.formatCache.entries());
            const toDelete = entries.slice(0, entries.length - this.cacheConfig.maxSize);
            
            for (const [key] of toDelete) {
                this.formatCache.delete(key);
            }
        }
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationRules = {
            required: (value) => value !== null && value !== undefined && value !== '',
            numeric: (value) => !isNaN(parseFloat(value)) && isFinite(value),
            date: (value) => !isNaN(Date.parse(value)),
            email: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
        };
    }
    
    // 设置性能监控
    setupPerformanceMonitoring() {
        this.performanceMetrics = {
            formatCounts: new Map(),
            formatTimes: new Map(),
            errorCounts: new Map()
        };
    }
    
    // 记录性能指标
    recordPerformance(operation, duration) {
        // 更新计数
        const currentCount = this.performanceMetrics.formatCounts.get(operation) || 0;
        this.performanceMetrics.formatCounts.set(operation, currentCount + 1);
        
        // 更新平均时间
        const currentTime = this.performanceMetrics.formatTimes.get(operation) || 0;
        const newAverage = (currentTime * currentCount + duration) / (currentCount + 1);
        this.performanceMetrics.formatTimes.set(operation, newAverage);
        
        // 更新总体统计
        this.formatterStatistics.totalFormats++;
        this.formatterStatistics.averageFormatTime = 
            (this.formatterStatistics.averageFormatTime * (this.formatterStatistics.totalFormats - 1) + duration) / 
            this.formatterStatistics.totalFormats;
    }
    
    // 格式化值
    format(value, field, options = {}) {
        const formatterInfo = this.formatterRegistry.get(field.type);
        
        if (!formatterInfo) {
            console.warn(`No formatter found for field type: ${field.type}`);
            return value?.toString() || '';
        }
        
        return formatterInfo.formatter(value, field, options);
    }
    
    // 注册自定义格式化器
    registerCustomFormatter(name, formatter, description, supportedTypes) {
        this.formatterRegistry.set(name, {
            formatter,
            description,
            supportedTypes,
            isCustom: true
        });
        
        this.formatterStatistics.customFormatterCount++;
    }
    
    // 获取格式化器信息
    getFormatterInfo(type) {
        return this.formatterRegistry.get(type);
    }
    
    // 获取所有格式化器
    getAllFormatters() {
        return Array.from(this.formatterRegistry.entries()).map(([name, info]) => ({
            name,
            ...info
        }));
    }
    
    // 验证值
    validate(value, field, rules = []) {
        const errors = [];
        
        for (const rule of rules) {
            if (typeof rule === 'string' && this.validationRules[rule]) {
                if (!this.validationRules[rule](value)) {
                    errors.push(`Validation failed: ${rule}`);
                }
            } else if (typeof rule === 'function') {
                const result = rule(value, field);
                if (result !== true) {
                    errors.push(result || 'Custom validation failed');
                }
            }
        }
        
        return errors;
    }
    
    // 获取格式化统计
    getFormatterStatistics() {
        return {
            ...this.formatterStatistics,
            registeredFormatterCount: this.formatterRegistry.size,
            cacheSize: this.formatCache.size,
            performanceMetrics: {
                formatCounts: Object.fromEntries(this.performanceMetrics.formatCounts),
                formatTimes: Object.fromEntries(this.performanceMetrics.formatTimes),
                errorCounts: Object.fromEntries(this.performanceMetrics.errorCounts)
            }
        };
    }
    
    // 清理缓存
    clearCache() {
        this.formatCache.clear();
        this.formatterStatistics.cacheHits = 0;
        this.formatterStatistics.cacheMisses = 0;
    }
    
    // 销毁管理器
    destroy() {
        // 清理缓存
        this.formatCache.clear();
        
        // 清理注册表
        this.formatterRegistry.clear();
        
        // 重置统计
        this.formatterStatistics = {
            totalFormats: 0,
            cacheHits: 0,
            cacheMisses: 0,
            customFormatterCount: 0,
            averageFormatTime: 0
        };
    }
}

// 使用示例
const formatterManager = new FieldFormatterManager();

// 格式化不同类型的值
const formattedNumber = formatterManager.format(1234.56, { type: 'float', digits: [16, 2] });
const formattedDate = formatterManager.format('2023-12-25', { type: 'date' });
const formattedBoolean = formatterManager.format(true, { type: 'boolean' });

// 注册自定义格式化器
formatterManager.registerCustomFormatter(
    'percentage',
    (value, field, options) => `${(value * 100).toFixed(2)}%`,
    'Format values as percentages',
    ['float']
);

// 获取统计信息
const stats = formatterManager.getFormatterStatistics();
console.log('Formatter statistics:', stats);
```

## 技术特点

### 1. 类型丰富
- **基础类型**: 支持所有基础数据类型的格式化
- **复合类型**: 支持复杂数据结构的格式化
- **自定义类型**: 支持用户自定义类型的格式化
- **扩展性**: 易于扩展新的数据类型

### 2. 国际化支持
- **本地化**: 完整的本地化支持
- **多语言**: 支持多语言环境
- **时区处理**: 智能的时区转换
- **货币支持**: 多货币格式化支持

### 3. 性能优化
- **缓存机制**: 智能的格式化结果缓存
- **懒计算**: 按需计算格式化结果
- **批量处理**: 支持批量格式化操作
- **内存管理**: 有效的内存使用管理

### 4. 错误处理
- **容错性**: 强大的错误容错能力
- **降级处理**: 格式化失败时的降级处理
- **日志记录**: 完整的错误日志记录
- **调试支持**: 丰富的调试信息

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- **格式化策略**: 不同数据类型的格式化策略
- **本地化策略**: 不同地区的本地化策略
- **缓存策略**: 不同的缓存策略

### 2. 工厂模式 (Factory Pattern)
- **格式化器工厂**: 创建不同类型的格式化器
- **选项工厂**: 创建格式化选项
- **验证器工厂**: 创建验证器

### 3. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 装饰基础格式化功能
- **选项装饰**: 装饰格式化选项
- **结果装饰**: 装饰格式化结果

### 4. 单例模式 (Singleton Pattern)
- **格式化器注册表**: 全局唯一的格式化器注册表
- **缓存管理**: 全局的缓存管理器
- **配置管理**: 全局的配置管理

## 注意事项

1. **性能考虑**: 避免频繁的格式化操作
2. **内存管理**: 合理使用缓存避免内存泄漏
3. **国际化**: 确保格式化结果的国际化兼容性
4. **安全性**: 防止XSS攻击和注入攻击

## 扩展建议

1. **自定义格式化器**: 支持更多自定义格式化器
2. **模板系统**: 添加格式化模板系统
3. **实时预览**: 提供格式化结果的实时预览
4. **批量操作**: 支持批量格式化操作
5. **性能分析**: 添加格式化性能分析工具

该格式化器模块为Odoo Web客户端提供了强大的数据格式化功能，通过丰富的格式化器和灵活的配置确保了数据的正确显示和良好的用户体验。

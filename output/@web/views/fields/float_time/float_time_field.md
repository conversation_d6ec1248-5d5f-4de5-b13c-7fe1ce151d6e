# FloatTimeField - 浮点时间字段

## 概述

`float_time_field.js` 是 Odoo Web 客户端的浮点时间字段组件，负责处理以浮点数形式存储的时间值的输入、显示和格式化。该模块包含70行代码，是一个专门的时间输入组件，专门用于处理float类型的时间字段，具备时间格式化、秒显示控制、数字键盘支持等特性，是时间数据处理的专用组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/float_time/float_time_field.js`
- **行数**: 70
- **模块**: `@web/views/fields/float_time/float_time_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/views/fields/formatters'          // 字段格式化器
'@web/views/fields/parsers'             // 字段解析器
'@web/views/fields/input_field_hook'    // 输入字段钩子
'@web/views/fields/standard_field_props' // 标准字段属性
'@web/views/fields/numpad_decimal_hook' // 数字键盘小数钩子
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const FloatTimeField = class FloatTimeField extends Component {
    static template = "web.FloatTimeField";
    static props = {
        ...standardFieldProps,
        inputType: { type: String, optional: true },
        placeholder: { type: String, optional: true },
        displaySeconds: { type: Boolean, optional: true },
    };
    static defaultProps = {
        inputType: "text",
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **输入类型**: 支持inputType配置输入类型
- **占位符**: 支持placeholder占位符文本
- **秒显示**: 支持displaySeconds控制秒的显示
- **默认配置**: 默认使用text输入类型

### 2. 组件初始化

```javascript
setup() {
    this.inputFloatTimeRef = useInputField({
        getValue: () => this.formattedValue,
        refName: "numpadDecimal",
        parse: (v) => parseFloatTime(v),
    });
    useNumpadDecimal();
}
```

**初始化功能**:
- **输入钩子**: 使用输入字段钩子管理输入
- **时间解析**: 使用parseFloatTime解析时间值
- **数字键盘**: 使用数字键盘小数钩子
- **引用管理**: 管理输入字段引用

### 3. 格式化显示

```javascript
get formattedValue() {
    return formatFloatTime(this.props.record.data[this.props.name], {
        displaySeconds: this.props.displaySeconds,
    });
}
```

**格式化功能**:
- **时间格式化**: 使用formatFloatTime格式化时间
- **秒控制**: 根据displaySeconds配置控制秒显示
- **数据获取**: 从记录数据中获取时间值
- **选项传递**: 传递格式化选项

### 4. 字段注册

```javascript
const floatTimeField = {
    component: FloatTimeField,
    displayName: _t("Time"),
    supportedOptions: [
        {
            label: _t("Display seconds"),
            name: "display_seconds",
            type: "boolean",
        },
        {
            label: _t("Type"),
            name: "type",
            type: "string",
            default: "text",
        },
    ],
    supportedTypes: ["float"],
    isEmpty: () => false,
    extractProps: ({ attrs, options }) => ({
        displaySeconds: options.displaySeconds,
        inputType: options.type,
        placeholder: attrs.placeholder,
    }),
};

registry.category("fields").add("float_time", floatTimeField);
```

**注册功能**:
- **组件注册**: 注册浮点时间字段组件
- **类型支持**: 仅支持float类型
- **选项配置**: 支持秒显示和输入类型选项
- **属性提取**: 提取配置属性到组件属性

## 使用场景

### 1. 浮点时间字段管理器

```javascript
// 浮点时间字段管理器
class FloatTimeFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置时间字段配置
        this.timeConfig = {
            enableValidation: true,
            enableFormatting: true,
            enableTimeCalculation: true,
            enableDurationDisplay: true,
            enableTimeZoneSupport: false,
            enableWorkingHours: true,
            enableTimeTracking: true,
            enableStatistics: true
        };
        
        // 设置时间格式选项
        this.timeFormats = new Map([
            ['hm', { hours: true, minutes: true, seconds: false, format: 'HH:MM' }],
            ['hms', { hours: true, minutes: true, seconds: true, format: 'HH:MM:SS' }],
            ['decimal', { decimal: true, format: 'H.HH' }],
            ['verbose', { verbose: true, format: 'H hours M minutes' }]
        ]);
        
        // 设置时间验证规则
        this.validationRules = {
            enableRangeValidation: true,
            enableNegativeCheck: true,
            enableMaxHours: true,
            minHours: 0,
            maxHours: 24,
            allowNegative: false
        };
        
        // 设置工作时间配置
        this.workingHoursConfig = {
            dailyHours: 8,
            weeklyHours: 40,
            overtimeThreshold: 8,
            breakTime: 1
        };
        
        // 设置时间统计
        this.timeStatistics = {
            totalEntries: 0,
            totalHours: 0,
            averageHours: 0,
            maxHours: 0,
            minHours: null,
            formatUsage: new Map()
        };
        
        this.initializeTimeSystem();
    }
    
    // 初始化时间系统
    initializeTimeSystem() {
        // 创建增强的浮点时间字段
        this.createEnhancedFloatTimeField();
        
        // 设置格式化系统
        this.setupFormattingSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置计算系统
        this.setupCalculationSystem();
    }
    
    // 创建增强的浮点时间字段
    createEnhancedFloatTimeField() {
        const originalField = FloatTimeField;
        
        this.EnhancedFloatTimeField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加时间功能
                this.addTimeFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    timeFormat: 'hm',
                    validationErrors: [],
                    calculationResult: null,
                    durationInfo: null,
                    workingHoursInfo: null,
                    timeHistory: []
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的格式化显示
                this.enhancedFormattedValue = () => {
                    const value = this.props.record.data[this.props.name];
                    if (value === null || value === undefined) return '';
                    
                    const format = this.timeFormats.get(this.enhancedState.timeFormat);
                    if (!format) {
                        return this.formattedValue;
                    }
                    
                    return this.formatTimeWithOptions(value, format);
                };
                
                // 使用选项格式化时间
                this.formatTimeWithOptions = (hours, formatOptions) => {
                    if (formatOptions.decimal) {
                        return hours.toFixed(2);
                    }
                    
                    const totalMinutes = Math.round(hours * 60);
                    const h = Math.floor(totalMinutes / 60);
                    const m = totalMinutes % 60;
                    const s = Math.round((hours * 3600) % 60);
                    
                    if (formatOptions.verbose) {
                        let result = '';
                        if (h > 0) result += `${h} hour${h !== 1 ? 's' : ''}`;
                        if (m > 0) {
                            if (result) result += ' ';
                            result += `${m} minute${m !== 1 ? 's' : ''}`;
                        }
                        if (formatOptions.seconds && s > 0) {
                            if (result) result += ' ';
                            result += `${s} second${s !== 1 ? 's' : ''}`;
                        }
                        return result || '0 minutes';
                    }
                    
                    // 标准格式 HH:MM 或 HH:MM:SS
                    let formatted = `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}`;
                    if (formatOptions.seconds) {
                        formatted += `:${s.toString().padStart(2, '0')}`;
                    }
                    
                    return formatted;
                };
                
                // 验证时间值
                this.validateTimeValue = (value) => {
                    const errors = [];
                    
                    if (value === null || value === undefined) {
                        return { isValid: true, errors: [] };
                    }
                    
                    // 数值验证
                    if (typeof value !== 'number' || isNaN(value)) {
                        errors.push('Time value must be a number');
                    }
                    
                    // 负值检查
                    if (this.validationRules.enableNegativeCheck && !this.validationRules.allowNegative && value < 0) {
                        errors.push('Negative time values are not allowed');
                    }
                    
                    // 范围验证
                    if (this.validationRules.enableRangeValidation) {
                        if (value < this.validationRules.minHours) {
                            errors.push(`Time must be at least ${this.validationRules.minHours} hours`);
                        }
                        
                        if (this.validationRules.enableMaxHours && value > this.validationRules.maxHours) {
                            errors.push(`Time cannot exceed ${this.validationRules.maxHours} hours`);
                        }
                    }
                    
                    this.enhancedState.validationErrors = errors;
                    return { isValid: errors.length === 0, errors };
                };
                
                // 时间计算
                this.calculateTime = (operation, operand) => {
                    const currentValue = this.props.record.data[this.props.name] || 0;
                    let result;
                    
                    switch (operation) {
                        case 'add':
                            result = currentValue + operand;
                            break;
                        case 'subtract':
                            result = currentValue - operand;
                            break;
                        case 'multiply':
                            result = currentValue * operand;
                            break;
                        case 'divide':
                            result = operand !== 0 ? currentValue / operand : 0;
                            break;
                        default:
                            throw new Error(`Unknown operation: ${operation}`);
                    }
                    
                    this.enhancedState.calculationResult = result;
                    return result;
                };
                
                // 转换时间单位
                this.convertTimeUnit = (value, fromUnit, toUnit) => {
                    const conversions = {
                        'seconds': 1/3600,
                        'minutes': 1/60,
                        'hours': 1,
                        'days': 24
                    };
                    
                    const fromFactor = conversions[fromUnit];
                    const toFactor = conversions[toUnit];
                    
                    if (!fromFactor || !toFactor) {
                        throw new Error('Invalid time unit');
                    }
                    
                    // 转换为小时，然后转换为目标单位
                    const hours = value * fromFactor;
                    return hours / toFactor;
                };
                
                // 获取持续时间信息
                this.getDurationInfo = (hours) => {
                    if (hours === null || hours === undefined) return null;
                    
                    const totalMinutes = Math.round(hours * 60);
                    const totalSeconds = Math.round(hours * 3600);
                    
                    const days = Math.floor(hours / 24);
                    const remainingHours = Math.floor(hours % 24);
                    const minutes = Math.floor(totalMinutes % 60);
                    const seconds = Math.floor(totalSeconds % 60);
                    
                    this.enhancedState.durationInfo = {
                        totalHours: hours,
                        totalMinutes: totalMinutes,
                        totalSeconds: totalSeconds,
                        days: days,
                        hours: remainingHours,
                        minutes: minutes,
                        seconds: seconds,
                        formatted: this.formatDuration(days, remainingHours, minutes, seconds)
                    };
                    
                    return this.enhancedState.durationInfo;
                };
                
                // 格式化持续时间
                this.formatDuration = (days, hours, minutes, seconds) => {
                    const parts = [];
                    
                    if (days > 0) parts.push(`${days}d`);
                    if (hours > 0) parts.push(`${hours}h`);
                    if (minutes > 0) parts.push(`${minutes}m`);
                    if (seconds > 0) parts.push(`${seconds}s`);
                    
                    return parts.join(' ') || '0m';
                };
                
                // 获取工作时间信息
                this.getWorkingHoursInfo = (hours) => {
                    if (!this.timeConfig.enableWorkingHours || hours === null || hours === undefined) {
                        return null;
                    }
                    
                    const dailyHours = this.workingHoursConfig.dailyHours;
                    const overtimeThreshold = this.workingHoursConfig.overtimeThreshold;
                    
                    const regularHours = Math.min(hours, overtimeThreshold);
                    const overtimeHours = Math.max(0, hours - overtimeThreshold);
                    const workingDays = hours / dailyHours;
                    
                    this.enhancedState.workingHoursInfo = {
                        totalHours: hours,
                        regularHours: regularHours,
                        overtimeHours: overtimeHours,
                        workingDays: workingDays,
                        isOvertime: overtimeHours > 0,
                        efficiency: (regularHours / dailyHours) * 100
                    };
                    
                    return this.enhancedState.workingHoursInfo;
                };
                
                // 时间范围检查
                this.isWithinWorkingHours = (hours) => {
                    return hours >= 0 && hours <= this.workingHoursConfig.dailyHours;
                };
                
                // 计算时间差
                this.calculateTimeDifference = (startTime, endTime) => {
                    return Math.abs(endTime - startTime);
                };
                
                // 格式化为时间字符串
                this.formatAsTimeString = (hours) => {
                    const totalMinutes = Math.round(hours * 60);
                    const h = Math.floor(totalMinutes / 60);
                    const m = totalMinutes % 60;
                    
                    return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}`;
                };
                
                // 解析时间字符串
                this.parseTimeString = (timeString) => {
                    const parts = timeString.split(':');
                    if (parts.length < 2) return null;
                    
                    const hours = parseInt(parts[0], 10);
                    const minutes = parseInt(parts[1], 10);
                    const seconds = parts.length > 2 ? parseInt(parts[2], 10) : 0;
                    
                    if (isNaN(hours) || isNaN(minutes) || isNaN(seconds)) return null;
                    
                    return hours + (minutes / 60) + (seconds / 3600);
                };
                
                // 设置时间格式
                this.setTimeFormat = (formatName) => {
                    if (this.timeFormats.has(formatName)) {
                        this.enhancedState.timeFormat = formatName;
                        
                        // 记录格式使用统计
                        const usage = this.timeStatistics.formatUsage.get(formatName) || 0;
                        this.timeStatistics.formatUsage.set(formatName, usage + 1);
                    }
                };
                
                // 添加到历史
                this.addToHistory = (value) => {
                    if (value === null || value === undefined) return;
                    
                    const historyEntry = {
                        value: value,
                        formatted: this.enhancedFormattedValue(),
                        timestamp: Date.now(),
                        durationInfo: this.getDurationInfo(value)
                    };
                    
                    this.enhancedState.timeHistory.unshift(historyEntry);
                    
                    // 限制历史大小
                    if (this.enhancedState.timeHistory.length > 20) {
                        this.enhancedState.timeHistory.pop();
                    }
                };
                
                // 获取时间历史
                this.getTimeHistory = () => {
                    return this.enhancedState.timeHistory;
                };
                
                // 清除历史
                this.clearHistory = () => {
                    this.enhancedState.timeHistory = [];
                };
                
                // 批量时间计算
                this.batchTimeCalculation = (values, operation) => {
                    const results = [];
                    
                    for (const value of values) {
                        try {
                            let result;
                            switch (operation) {
                                case 'sum':
                                    result = values.reduce((a, b) => a + b, 0);
                                    break;
                                case 'average':
                                    result = values.reduce((a, b) => a + b, 0) / values.length;
                                    break;
                                case 'max':
                                    result = Math.max(...values);
                                    break;
                                case 'min':
                                    result = Math.min(...values);
                                    break;
                                default:
                                    result = value;
                            }
                            results.push({ value, result, success: true });
                        } catch (error) {
                            results.push({ value, result: null, success: false, error });
                        }
                    }
                    
                    return results;
                };
                
                // 获取时间信息
                this.getTimeInfo = () => {
                    const value = this.props.record.data[this.props.name];
                    
                    return {
                        value: value,
                        formatted: this.enhancedFormattedValue(),
                        format: this.enhancedState.timeFormat,
                        isValid: this.enhancedState.validationErrors.length === 0,
                        errors: this.enhancedState.validationErrors,
                        durationInfo: this.getDurationInfo(value),
                        workingHoursInfo: this.getWorkingHoursInfo(value)
                    };
                };
                
                // 记录时间统计
                this.recordTimeStatistics = (value) => {
                    if (value === null || value === undefined || isNaN(value)) return;
                    
                    this.timeStatistics.totalEntries++;
                    this.timeStatistics.totalHours += value;
                    this.timeStatistics.averageHours = this.timeStatistics.totalHours / this.timeStatistics.totalEntries;
                    
                    if (this.timeStatistics.minHours === null || value < this.timeStatistics.minHours) {
                        this.timeStatistics.minHours = value;
                    }
                    
                    if (value > this.timeStatistics.maxHours) {
                        this.timeStatistics.maxHours = value;
                    }
                };
            }
            
            addTimeFeatures() {
                // 时间功能
                this.timeManager = {
                    enabled: this.timeConfig.enableTimeCalculation,
                    calculate: (op, operand) => this.calculateTime(op, operand),
                    convert: (value, from, to) => this.convertTimeUnit(value, from, to),
                    format: (format) => this.setTimeFormat(format)
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.timeConfig.enableValidation,
                    validate: (value) => this.validateTimeValue(value),
                    getErrors: () => this.enhancedState.validationErrors
                };
            }
            
            // 重写原始方法
            get formattedValue() {
                return this.enhancedFormattedValue();
            }
        };
    }
    
    // 设置格式化系统
    setupFormattingSystem() {
        this.formattingSystemConfig = {
            enabled: this.timeConfig.enableFormatting,
            formats: this.timeFormats
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.timeConfig.enableValidation,
            rules: this.validationRules
        };
    }
    
    // 设置计算系统
    setupCalculationSystem() {
        this.calculationSystemConfig = {
            enabled: this.timeConfig.enableTimeCalculation,
            workingHours: this.workingHoursConfig
        };
    }
    
    // 创建浮点时间字段
    createFloatTimeField(props) {
        return new this.EnhancedFloatTimeField(props);
    }
    
    // 注册时间格式
    registerTimeFormat(name, config) {
        this.timeFormats.set(name, config);
    }
    
    // 批量验证时间值
    batchValidateTimeValues(fields, values) {
        const results = [];
        
        for (let i = 0; i < fields.length && i < values.length; i++) {
            try {
                const result = fields[i].validateTimeValue(values[i]);
                results.push({ field: fields[i], value: values[i], ...result });
            } catch (error) {
                results.push({ field: fields[i], value: values[i], isValid: false, errors: [error.message] });
            }
        }
        
        return results;
    }
    
    // 获取时间统计
    getTimeStatistics() {
        return {
            ...this.timeStatistics,
            formatCount: this.timeFormats.size,
            averageHoursFormatted: this.formatTimeWithOptions(this.timeStatistics.averageHours, { hours: true, minutes: true })
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理格式
        this.timeFormats.clear();
        
        // 重置统计
        this.timeStatistics = {
            totalEntries: 0,
            totalHours: 0,
            averageHours: 0,
            maxHours: 0,
            minHours: null,
            formatUsage: new Map()
        };
    }
}

// 使用示例
const timeManager = new FloatTimeFieldManager();

// 创建浮点时间字段
const timeField = timeManager.createFloatTimeField({
    name: 'working_hours',
    record: {
        data: { working_hours: 8.5 }, // 8小时30分钟
        fields: { working_hours: { type: 'float' } }
    },
    displaySeconds: false
});

// 设置时间格式
timeField.setTimeFormat('hms');

// 注册自定义时间格式
timeManager.registerTimeFormat('custom', {
    hours: true,
    minutes: true,
    seconds: false,
    format: 'H:MM'
});

// 获取统计信息
const stats = timeManager.getTimeStatistics();
console.log('Float time field statistics:', stats);
```

## 技术特点

### 1. 时间专用
- **时间格式化**: 专门的时间格式化功能
- **浮点存储**: 以浮点数形式存储时间
- **秒控制**: 可控制的秒显示功能
- **输入解析**: 专门的时间输入解析

### 2. 格式灵活
- **多种格式**: 支持多种时间显示格式
- **秒显示**: 可选的秒显示控制
- **占位符**: 支持占位符文本
- **输入类型**: 可配置的输入类型

### 3. 用户友好
- **直观显示**: 直观的时间显示格式
- **数字键盘**: 支持数字键盘输入
- **即时格式化**: 即时的时间格式化
- **清晰界面**: 清晰的用户界面

### 4. 标准化
- **标准属性**: 遵循标准字段属性规范
- **类型支持**: 仅支持float类型
- **注册规范**: 标准的字段注册方式
- **接口一致**: 一致的组件接口

## 设计模式

### 1. 组件模式 (Component Pattern)
- **UI组件**: 封装时间输入UI
- **状态管理**: 管理时间输入状态
- **事件处理**: 处理用户交互

### 2. 钩子模式 (Hook Pattern)
- **输入钩子**: 使用输入字段钩子
- **数字键盘钩子**: 使用数字键盘钩子
- **生命周期**: 管理组件生命周期

### 3. 策略模式 (Strategy Pattern)
- **格式策略**: 不同的时间格式策略
- **解析策略**: 不同的时间解析策略
- **显示策略**: 不同的显示策略

### 4. 工厂模式 (Factory Pattern)
- **字段工厂**: 创建时间字段
- **格式工厂**: 创建格式化器
- **解析工厂**: 创建解析器

## 注意事项

1. **时间精度**: 注意浮点数时间的精度问题
2. **格式一致**: 确保时间格式的一致性
3. **用户理解**: 确保用户理解时间格式
4. **数据验证**: 验证时间数据的有效性

## 扩展建议

1. **时区支持**: 添加时区支持功能
2. **工作时间**: 支持工作时间计算
3. **时间计算**: 添加时间计算功能
4. **持续时间**: 支持持续时间显示
5. **时间跟踪**: 添加时间跟踪功能

该浮点时间字段为Odoo Web客户端提供了专门的时间输入和显示功能，通过灵活的格式化选项和直观的时间显示确保了优秀的时间数据处理能力和用户体验。

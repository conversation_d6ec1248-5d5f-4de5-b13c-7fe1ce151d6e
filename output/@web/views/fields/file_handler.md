# FileHandler - 文件处理器

## 概述

`file_handler.js` 是 Odoo Web 客户端字段系统的文件处理器模块，负责处理文件上传功能。该模块包含86行代码，定义了FileUploader组件，专门用于处理用户文件选择、上传、验证和数据转换等功能，具备多文件上传、文件类型限制、大小检查、进度显示等特性，是文件相关字段的核心处理组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/file_handler.js`
- **行数**: 86
- **模块**: `@web/views/fields/file_handler`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'        // 翻译服务
'@web/core/utils/hooks'             // 工具钩子
'@web/core/utils/urls'              // URL工具
'@web/core/utils/files'             // 文件工具
'@odoo/owl'                         // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const FileUploader = class FileUploader extends Component {
    static template = "web.FileUploader";
    static props = {
        onClick: { type: Function, optional: true },
        onUploaded: Function,
        onUploadComplete: { type: Function, optional: true },
        multiUpload: { type: Boolean, optional: true },
        inputName: { type: String, optional: true },
        fileUploadClass: { type: String, optional: true },
        acceptedFileExtensions: { type: String, optional: true },
        slots: { type: Object, optional: true },
        showUploadingText: { type: Boolean, optional: true },
    };
    static defaultProps = {
        showUploadingText: true,
    };
}
```

**组件特性**:
- **专用模板**: 使用FileUploader专用模板
- **丰富属性**: 支持多种可选配置属性
- **回调机制**: 提供多种回调函数支持
- **默认配置**: 提供合理的默认属性值

### 2. 组件初始化

```javascript
setup() {
    this.notification = useService("notification");
    this.fileInputRef = useRef("fileInput");
    this.state = useState({
        isUploading: false,
    });
}
```

**初始化功能**:
- **通知服务**: 集成通知服务用于错误提示
- **文件输入引用**: 创建文件输入元素的引用
- **状态管理**: 管理上传状态
- **简洁设计**: 简洁的状态设计

### 3. 文件变更处理

```javascript
async onFileChange(ev) {
    if (!ev.target.files.length) {
        return;
    }
    const { target } = ev;
    for (const file of ev.target.files) {
        if (!checkFileSize(file.size, this.notification)) {
            return null;
        }
        this.state.isUploading = true;
        const data = await getDataURLFromFile(file);
        if (!file.size) {
            console.warn(`Error while uploading file : ${file.name}`);
            this.notification.add(_t("There was a problem while uploading your file."), {
                type: "danger",
            });
        }
        try {
            await this.props.onUploaded({
                name: file.name,
                size: file.size,
                type: file.type,
                data: data.split(",")[1],
                objectUrl: file.type === "application/pdf" ? URL.createObjectURL(file) : null,
            });
        } finally {
            this.state.isUploading = false;
        }
    }
    target.value = null;
    if (this.props.multiUpload && this.props.onUploadComplete) {
        this.props.onUploadComplete({});
    }
}
```

**文件处理功能**:
- **文件验证**: 检查文件是否存在和大小限制
- **数据转换**: 将文件转换为Data URL格式
- **错误处理**: 处理文件上传过程中的错误
- **状态管理**: 管理上传进度状态
- **回调执行**: 执行上传完成回调
- **多文件支持**: 支持多文件上传处理

### 4. 文件选择按钮

```javascript
async onSelectFileButtonClick(ev) {
    if (this.props.onClick) {
        const ok = await this.props.onClick(ev);
        if (ok !== undefined && !ok) {
            return;
        }
    }
    this.fileInputRef.el.click();
}
```

**按钮处理功能**:
- **前置检查**: 执行可选的点击前置检查
- **条件执行**: 根据前置检查结果决定是否继续
- **文件选择**: 触发文件选择对话框
- **事件代理**: 代理文件输入元素的点击事件

## 使用场景

### 1. 文件上传管理器

```javascript
// 文件上传管理器
class FileUploadManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置上传配置
        this.uploadConfig = {
            enableMultiUpload: true,
            enableProgressTracking: true,
            enableFileValidation: true,
            enableThumbnailGeneration: true,
            maxFileSize: 10 * 1024 * 1024, // 10MB
            maxFiles: 10,
            allowedTypes: ['image/*', 'application/pdf', 'text/*'],
            enableChunkedUpload: false,
            chunkSize: 1024 * 1024 // 1MB
        };
        
        // 设置上传队列
        this.uploadQueue = [];
        
        // 设置上传统计
        this.uploadStatistics = {
            totalUploads: 0,
            successfulUploads: 0,
            failedUploads: 0,
            totalBytes: 0,
            averageUploadTime: 0
        };
        
        // 设置文件缓存
        this.fileCache = new Map();
        
        this.initializeUploadSystem();
    }
    
    // 初始化上传系统
    initializeUploadSystem() {
        // 创建增强的文件上传器
        this.createEnhancedFileUploader();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置进度跟踪
        this.setupProgressTracking();
        
        // 设置缩略图生成
        this.setupThumbnailGeneration();
    }
    
    // 创建增强的文件上传器
    createEnhancedFileUploader() {
        const originalUploader = FileUploader;
        
        this.EnhancedFileUploader = class extends originalUploader {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
                
                // 添加进度跟踪
                this.addProgressTracking();
                
                // 添加预览功能
                this.addPreviewFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = useState({
                    ...this.state,
                    uploadProgress: 0,
                    uploadedFiles: [],
                    failedFiles: [],
                    currentFile: null,
                    thumbnails: new Map(),
                    validationErrors: []
                });
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的文件变更处理
                this.enhancedOnFileChange = async (ev) => {
                    const startTime = performance.now();
                    
                    try {
                        if (!ev.target.files.length) {
                            return;
                        }
                        
                        const files = Array.from(ev.target.files);
                        
                        // 验证文件
                        const validationResult = this.validateFiles(files);
                        if (!validationResult.isValid) {
                            this.showValidationErrors(validationResult.errors);
                            return;
                        }
                        
                        // 处理文件队列
                        await this.processFileQueue(files);
                        
                        // 记录成功
                        const endTime = performance.now();
                        this.recordUploadTime(endTime - startTime);
                        
                    } catch (error) {
                        this.handleUploadError(error);
                    }
                };
                
                // 验证文件
                this.validateFiles = (files) => {
                    const errors = [];
                    
                    // 检查文件数量
                    if (files.length > this.uploadConfig.maxFiles) {
                        errors.push(`Maximum ${this.uploadConfig.maxFiles} files allowed`);
                    }
                    
                    for (const file of files) {
                        // 检查文件大小
                        if (file.size > this.uploadConfig.maxFileSize) {
                            errors.push(`File ${file.name} is too large`);
                        }
                        
                        // 检查文件类型
                        if (!this.isFileTypeAllowed(file)) {
                            errors.push(`File type ${file.type} is not allowed`);
                        }
                        
                        // 检查文件名
                        if (!this.isFileNameValid(file.name)) {
                            errors.push(`Invalid file name: ${file.name}`);
                        }
                    }
                    
                    return {
                        isValid: errors.length === 0,
                        errors: errors
                    };
                };
                
                // 检查文件类型
                this.isFileTypeAllowed = (file) => {
                    return this.uploadConfig.allowedTypes.some(type => {
                        if (type.endsWith('/*')) {
                            return file.type.startsWith(type.slice(0, -1));
                        }
                        return file.type === type;
                    });
                };
                
                // 检查文件名有效性
                this.isFileNameValid = (fileName) => {
                    // 检查危险字符
                    const dangerousChars = /[<>:"/\\|?*]/;
                    return !dangerousChars.test(fileName);
                };
                
                // 处理文件队列
                this.processFileQueue = async (files) => {
                    this.enhancedState.uploadProgress = 0;
                    
                    for (let i = 0; i < files.length; i++) {
                        const file = files[i];
                        this.enhancedState.currentFile = file;
                        
                        try {
                            await this.processFile(file);
                            this.enhancedState.uploadedFiles.push(file);
                        } catch (error) {
                            this.enhancedState.failedFiles.push({ file, error });
                        }
                        
                        // 更新进度
                        this.enhancedState.uploadProgress = ((i + 1) / files.length) * 100;
                    }
                    
                    this.enhancedState.currentFile = null;
                };
                
                // 处理单个文件
                this.processFile = async (file) => {
                    // 生成缩略图
                    if (this.isImageFile(file)) {
                        const thumbnail = await this.generateThumbnail(file);
                        this.enhancedState.thumbnails.set(file.name, thumbnail);
                    }
                    
                    // 转换文件数据
                    const data = await getDataURLFromFile(file);
                    
                    // 创建文件对象
                    const fileObject = {
                        name: file.name,
                        size: file.size,
                        type: file.type,
                        data: data.split(",")[1],
                        objectUrl: this.createObjectUrl(file),
                        thumbnail: this.enhancedState.thumbnails.get(file.name),
                        uploadTime: Date.now()
                    };
                    
                    // 执行上传回调
                    await this.props.onUploaded(fileObject);
                    
                    // 更新统计
                    this.updateUploadStatistics(file);
                };
                
                // 检查是否为图片文件
                this.isImageFile = (file) => {
                    return file.type.startsWith('image/');
                };
                
                // 生成缩略图
                this.generateThumbnail = async (file) => {
                    return new Promise((resolve) => {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        const img = new Image();
                        
                        img.onload = () => {
                            // 设置缩略图尺寸
                            const maxSize = 150;
                            let { width, height } = img;
                            
                            if (width > height) {
                                if (width > maxSize) {
                                    height = (height * maxSize) / width;
                                    width = maxSize;
                                }
                            } else {
                                if (height > maxSize) {
                                    width = (width * maxSize) / height;
                                    height = maxSize;
                                }
                            }
                            
                            canvas.width = width;
                            canvas.height = height;
                            
                            // 绘制缩略图
                            ctx.drawImage(img, 0, 0, width, height);
                            
                            // 返回Data URL
                            resolve(canvas.toDataURL('image/jpeg', 0.8));
                        };
                        
                        img.src = URL.createObjectURL(file);
                    });
                };
                
                // 创建对象URL
                this.createObjectUrl = (file) => {
                    if (file.type === "application/pdf" || this.isImageFile(file)) {
                        return URL.createObjectURL(file);
                    }
                    return null;
                };
                
                // 显示验证错误
                this.showValidationErrors = (errors) => {
                    this.enhancedState.validationErrors = errors;
                    
                    for (const error of errors) {
                        this.notification.add(error, { type: "danger" });
                    }
                };
                
                // 处理上传错误
                this.handleUploadError = (error) => {
                    console.error('Upload error:', error);
                    this.notification.add(
                        _t("There was a problem while uploading your file."),
                        { type: "danger" }
                    );
                };
                
                // 更新上传统计
                this.updateUploadStatistics = (file) => {
                    this.uploadStatistics.totalUploads++;
                    this.uploadStatistics.successfulUploads++;
                    this.uploadStatistics.totalBytes += file.size;
                };
                
                // 记录上传时间
                this.recordUploadTime = (duration) => {
                    const total = this.uploadStatistics.successfulUploads;
                    this.uploadStatistics.averageUploadTime = 
                        (this.uploadStatistics.averageUploadTime * (total - 1) + duration) / total;
                };
                
                // 获取文件预览
                this.getFilePreview = (file) => {
                    if (this.isImageFile(file)) {
                        return this.enhancedState.thumbnails.get(file.name) || URL.createObjectURL(file);
                    }
                    
                    // 返回文件类型图标
                    return this.getFileTypeIcon(file.type);
                };
                
                // 获取文件类型图标
                this.getFileTypeIcon = (fileType) => {
                    const iconMap = {
                        'application/pdf': 'fa-file-pdf',
                        'text/plain': 'fa-file-text',
                        'application/zip': 'fa-file-archive',
                        'application/json': 'fa-file-code'
                    };
                    
                    return iconMap[fileType] || 'fa-file';
                };
                
                // 清理上传状态
                this.clearUploadState = () => {
                    this.enhancedState.uploadProgress = 0;
                    this.enhancedState.uploadedFiles = [];
                    this.enhancedState.failedFiles = [];
                    this.enhancedState.currentFile = null;
                    this.enhancedState.validationErrors = [];
                };
                
                // 重试失败的上传
                this.retryFailedUploads = async () => {
                    const failedFiles = [...this.enhancedState.failedFiles];
                    this.enhancedState.failedFiles = [];
                    
                    for (const { file } of failedFiles) {
                        try {
                            await this.processFile(file);
                            this.enhancedState.uploadedFiles.push(file);
                        } catch (error) {
                            this.enhancedState.failedFiles.push({ file, error });
                        }
                    }
                };
                
                // 取消上传
                this.cancelUpload = () => {
                    this.clearUploadState();
                    this.notification.add(_t("Upload cancelled"), { type: "info" });
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    rules: new Map(),
                    addRule: (name, rule) => this.validationManager.rules.set(name, rule),
                    removeRule: (name) => this.validationManager.rules.delete(name),
                    validate: (files) => this.validateFiles(files)
                };
            }
            
            addProgressTracking() {
                // 进度跟踪功能
                this.progressTracker = {
                    enabled: this.uploadConfig.enableProgressTracking,
                    updateProgress: (progress) => {
                        this.enhancedState.uploadProgress = progress;
                    },
                    getProgress: () => this.enhancedState.uploadProgress
                };
            }
            
            addPreviewFeatures() {
                // 预览功能
                this.previewManager = {
                    enabled: true,
                    generatePreview: (file) => this.getFilePreview(file),
                    clearPreviews: () => {
                        // 清理对象URL
                        for (const url of this.enhancedState.thumbnails.values()) {
                            if (url.startsWith('blob:')) {
                                URL.revokeObjectURL(url);
                            }
                        }
                        this.enhancedState.thumbnails.clear();
                    }
                };
            }
            
            // 重写原始方法
            onFileChange(ev) {
                return this.enhancedOnFileChange(ev);
            }
            
            // 获取上传统计
            getUploadStatistics() {
                return {
                    ...this.uploadStatistics,
                    currentProgress: this.enhancedState.uploadProgress,
                    uploadedCount: this.enhancedState.uploadedFiles.length,
                    failedCount: this.enhancedState.failedFiles.length
                };
            }
            
            // 组件销毁时清理
            onWillDestroy() {
                // 清理预览
                this.previewManager.clearPreviews();
                
                // 清理状态
                this.clearUploadState();
            }
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationRules = {
            fileSize: (file) => file.size <= this.uploadConfig.maxFileSize,
            fileType: (file) => this.isFileTypeAllowed(file),
            fileName: (file) => this.isFileNameValid(file.name),
            fileCount: (files) => files.length <= this.uploadConfig.maxFiles
        };
    }
    
    // 设置进度跟踪
    setupProgressTracking() {
        this.progressConfig = {
            enableRealTimeProgress: true,
            enableSpeedCalculation: true,
            enableTimeEstimation: true
        };
    }
    
    // 设置缩略图生成
    setupThumbnailGeneration() {
        this.thumbnailConfig = {
            enabled: this.uploadConfig.enableThumbnailGeneration,
            maxSize: 150,
            quality: 0.8,
            format: 'image/jpeg'
        };
    }
    
    // 创建文件上传器
    createFileUploader(props) {
        return new this.EnhancedFileUploader(props);
    }
    
    // 添加验证规则
    addValidationRule(name, rule) {
        this.validationRules[name] = rule;
    }
    
    // 获取上传统计
    getUploadStatistics() {
        return {
            ...this.uploadStatistics,
            queueSize: this.uploadQueue.length,
            cacheSize: this.fileCache.size
        };
    }
    
    // 清理缓存
    clearCache() {
        this.fileCache.clear();
        this.uploadQueue = [];
    }
    
    // 销毁管理器
    destroy() {
        // 清理缓存
        this.fileCache.clear();
        this.uploadQueue = [];
        
        // 重置统计
        this.uploadStatistics = {
            totalUploads: 0,
            successfulUploads: 0,
            failedUploads: 0,
            totalBytes: 0,
            averageUploadTime: 0
        };
    }
}

// 使用示例
const uploadManager = new FileUploadManager();

// 创建文件上传器
const fileUploader = uploadManager.createFileUploader({
    onUploaded: async (fileData) => {
        console.log('File uploaded:', fileData);
    },
    onUploadComplete: () => {
        console.log('All uploads complete');
    },
    multiUpload: true,
    acceptedFileExtensions: '.jpg,.png,.pdf'
});

// 添加自定义验证规则
uploadManager.addValidationRule('customRule', (file) => {
    return file.name.length < 100;
});

// 获取统计信息
const stats = uploadManager.getUploadStatistics();
console.log('File upload statistics:', stats);
```

## 技术特点

### 1. 文件处理
- **多文件支持**: 支持单文件和多文件上传
- **类型限制**: 支持文件类型的限制
- **大小检查**: 自动检查文件大小限制
- **数据转换**: 将文件转换为Base64格式

### 2. 用户体验
- **进度显示**: 显示上传进度状态
- **错误提示**: 友好的错误提示信息
- **状态反馈**: 清晰的上传状态反馈
- **交互友好**: 简单直观的用户交互

### 3. 安全性
- **文件验证**: 完整的文件验证机制
- **类型检查**: 严格的文件类型检查
- **大小限制**: 防止过大文件上传
- **错误处理**: 完善的错误处理机制

### 4. 扩展性
- **回调机制**: 灵活的回调函数机制
- **属性配置**: 丰富的属性配置选项
- **插槽支持**: 支持自定义内容插槽
- **样式定制**: 支持自定义样式类

## 设计模式

### 1. 组件模式 (Component Pattern)
- **封装性**: 封装文件上传的所有功能
- **可复用**: 高度可复用的上传组件
- **接口标准**: 标准化的组件接口

### 2. 观察者模式 (Observer Pattern)
- **事件监听**: 监听文件选择事件
- **状态变化**: 响应上传状态变化
- **回调通知**: 通过回调通知外部组件

### 3. 策略模式 (Strategy Pattern)
- **验证策略**: 不同的文件验证策略
- **处理策略**: 不同的文件处理策略
- **上传策略**: 不同的上传处理策略

### 4. 工厂模式 (Factory Pattern)
- **文件对象工厂**: 创建标准化的文件对象
- **URL工厂**: 创建不同类型的URL
- **回调工厂**: 创建回调函数

## 注意事项

1. **内存管理**: 及时清理文件对象URL避免内存泄漏
2. **安全性**: 严格验证文件类型和内容
3. **性能考虑**: 避免同时处理过多大文件
4. **用户体验**: 提供清晰的上传进度和状态反馈

## 扩展建议

1. **拖拽上传**: 添加拖拽文件上传功能
2. **断点续传**: 支持大文件的断点续传
3. **压缩功能**: 添加图片压缩功能
4. **预览功能**: 增强文件预览功能
5. **批量操作**: 支持批量文件操作

该文件处理器为Odoo Web客户端提供了强大的文件上传功能，通过完善的验证机制和友好的用户界面确保了文件上传的安全性和易用性。

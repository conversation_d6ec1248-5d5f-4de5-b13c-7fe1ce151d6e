# DynamicPlaceholderHook - 动态占位符钩子

## 概述

`dynamic_placeholder_hook.js` 是 Odoo Web 客户端字段系统的动态占位符钩子模块，负责在文本输入字段中提供动态占位符功能。该模块包含106行代码，是一个OWL钩子函数，专门用于处理用户在输入框中输入触发字符（#）时弹出字段选择器，允许用户选择模型字段来创建动态占位符，具备键盘触发、弹出框管理、字段选择、文本插入等特性，是字段动态内容生成的重要工具。

## 文件信息
- **路径**: `/web/static/src/views/fields/dynamic_placeholder_hook.js`
- **行数**: 106
- **模块**: `@web/views/fields/dynamic_placeholder_hook`

## 依赖关系

```javascript
// 核心依赖
'@web/core/popover/popover_hook'                        // 弹出框钩子
'@web/core/utils/hooks'                                 // 工具钩子
'@odoo/owl'                                            // OWL框架
'@web/core/l10n/translation'                           // 翻译服务
'@web/views/fields/dynamic_placeholder_popover'        // 动态占位符弹出框
```

## 核心功能

### 1. 钩子函数定义

```javascript
function useDynamicPlaceholder(elementRef) {
    const TRIGGER_KEY = "#";
    const ownerField = useComponent();
    const triggerKeyReplaceRegex = new RegExp(`${TRIGGER_KEY}$`);
    let closeCallback;
    let positionCallback;
    const popover = usePopover(DynamicPlaceholderPopover, {
        onClose: () => closeCallback?.(),
        onPositioned: (popper, position) => positionCallback?.(popper, position),
    });
    const notification = useService("notification");
    
    let model = null;
    // ...
}
```

**钩子初始化功能**:
- **触发键**: 定义"#"作为触发字符
- **组件引用**: 获取当前字段组件的引用
- **弹出框**: 初始化动态占位符弹出框
- **通知服务**: 集成通知服务用于错误提示

### 2. 动态占位符验证

```javascript
const onDynamicPlaceholderValidate = function (path, defaultValue) {
    const element = elementRef?.el;
    if (!element) {
        return;
    }
    let rangeIndex = parseInt(element.getAttribute("data-oe-dynamic-placeholder-range-index"));
    // When the user cancel/close the popover, the path is empty.
    if (path) {
        defaultValue = defaultValue.replace("|||", "");
        const dynamicPlaceholder = ` {{object.${path}${
            defaultValue?.length ? ` ||| ${defaultValue}` : ""
        }}}`;
        const baseValue = element.value;
        const splitedValue = [baseValue.slice(0, rangeIndex), baseValue.slice(rangeIndex)];
        const newValue =
            splitedValue[0].replace(triggerKeyReplaceRegex, "") +
            dynamicPlaceholder +
            splitedValue[1];
        const changes = { [ownerField.props.name]: newValue };
        ownerField.props.record.update(changes);
        element.value = newValue;
        
        // -1 to take the removal of the trigger key char into account
        rangeIndex += dynamicPlaceholder.length - 1;
        element.setSelectionRange(rangeIndex, rangeIndex);
        element.removeAttribute("data-oe-dynamic-placeholder-range-index");
    }
};
```

**验证功能**:
- **路径处理**: 处理用户选择的字段路径
- **默认值**: 处理可选的默认值
- **占位符生成**: 生成标准格式的动态占位符
- **文本插入**: 在光标位置插入生成的占位符
- **记录更新**: 更新字段记录的值

### 3. 键盘事件处理

```javascript
async function onKeydown(ev) {
    const element = elementRef?.el;
    if (ev.target === element && ev.key === TRIGGER_KEY) {
        const currentRangeIndex = element.selectionStart;
        // +1 to take the trigger key char into account
        element.setAttribute("data-oe-dynamic-placeholder-range-index", currentRangeIndex + 1);
        await open({
            validateCallback: onDynamicPlaceholderValidate,
            closeCallback: onDynamicPlaceholderClose,
        });
    }
}
```

**键盘处理功能**:
- **触发检测**: 检测用户输入的触发字符
- **位置记录**: 记录光标位置用于后续插入
- **弹出框打开**: 触发时打开字段选择器
- **回调设置**: 设置验证和关闭回调函数

### 4. 弹出框管理

```javascript
async function open(opts) {
    if (!model) {
        return notification.add(
            _t("You need to select a model before opening the dynamic placeholder selector."),
            { type: "danger" }
        );
    }
    closeCallback = opts.closeCallback;
    positionCallback = opts.positionCallback;
    popover.open(elementRef?.el, {
        resModel: model,
        validate: opts.validateCallback,
    });
}
```

**弹出框功能**:
- **模型验证**: 确保已选择模型才能打开选择器
- **错误提示**: 未选择模型时显示错误通知
- **回调管理**: 管理弹出框的各种回调函数
- **参数传递**: 传递模型和验证回调给弹出框

### 5. 模型更新

```javascript
function updateModel(model_name_location) {
    const recordData = ownerField.props.record.data;
    model = recordData[model_name_location] || recordData.model;
}
```

**模型更新功能**:
- **动态模型**: 根据记录数据动态更新模型
- **字段映射**: 从指定字段或默认model字段获取模型名
- **实时更新**: 支持模型的实时更新
- **灵活配置**: 支持灵活的模型配置方式

## 使用场景

### 1. 动态占位符管理器

```javascript
// 动态占位符管理器
class DynamicPlaceholderManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置占位符配置
        this.placeholderConfig = {
            enableTriggerKey: true,
            triggerKey: '#',
            enableDefaultValues: true,
            enableCustomFormatters: true,
            enableValidation: true,
            maxPlaceholderLength: 200,
            customTriggers: new Map(),
            formatters: new Map()
        };
        
        // 设置占位符注册表
        this.placeholderRegistry = new Map();
        
        // 设置模型缓存
        this.modelCache = new Map();
        
        // 设置占位符统计
        this.placeholderStatistics = {
            totalPlaceholders: 0,
            successfulInsertions: 0,
            failedInsertions: 0,
            averageInsertionTime: 0
        };
        
        this.initializePlaceholderSystem();
    }
    
    // 初始化占位符系统
    initializePlaceholderSystem() {
        // 创建增强的动态占位符钩子
        this.createEnhancedDynamicPlaceholderHook();
        
        // 设置触发器系统
        this.setupTriggerSystem();
        
        // 设置格式化系统
        this.setupFormatterSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
    }
    
    // 创建增强的动态占位符钩子
    createEnhancedDynamicPlaceholderHook() {
        this.enhancedUseDynamicPlaceholder = (elementRef, options = {}) => {
            const config = { ...this.placeholderConfig, ...options };
            const originalHook = useDynamicPlaceholder(elementRef);
            
            // 扩展原始钩子功能
            const enhancedHook = {
                ...originalHook,
                
                // 增强的键盘处理
                enhancedOnKeydown: async (ev) => {
                    const startTime = performance.now();
                    
                    try {
                        // 检查自定义触发器
                        const customTrigger = this.checkCustomTriggers(ev);
                        if (customTrigger) {
                            await this.handleCustomTrigger(customTrigger, ev, elementRef);
                            return;
                        }
                        
                        // 执行原始键盘处理
                        await originalHook.onKeydown(ev);
                        
                        // 记录成功
                        this.placeholderStatistics.successfulInsertions++;
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordPerformance(endTime - startTime);
                        
                    } catch (error) {
                        this.handleInsertionError(error);
                        this.placeholderStatistics.failedInsertions++;
                    }
                },
                
                // 增强的模型更新
                enhancedUpdateModel: (modelNameLocation) => {
                    try {
                        // 缓存检查
                        const cacheKey = `${modelNameLocation}_${Date.now()}`;
                        if (this.modelCache.has(modelNameLocation)) {
                            const cachedModel = this.modelCache.get(modelNameLocation);
                            if (this.isModelCacheValid(cachedModel)) {
                                return;
                            }
                        }
                        
                        // 执行原始模型更新
                        originalHook.updateModel(modelNameLocation);
                        
                        // 更新缓存
                        this.updateModelCache(modelNameLocation);
                        
                    } catch (error) {
                        console.error('Model update error:', error);
                    }
                },
                
                // 增强的弹出框打开
                enhancedOpen: async (opts) => {
                    try {
                        // 预验证
                        if (!this.preValidateOpen(opts)) {
                            return;
                        }
                        
                        // 增强选项
                        const enhancedOpts = this.enhanceOpenOptions(opts);
                        
                        // 执行原始打开
                        await originalHook.open(enhancedOpts);
                        
                        // 记录打开事件
                        this.recordOpenEvent(enhancedOpts);
                        
                    } catch (error) {
                        this.handleOpenError(error);
                    }
                },
                
                // 批量插入占位符
                batchInsertPlaceholders: async (placeholders) => {
                    const element = elementRef?.el;
                    if (!element) return;
                    
                    let currentValue = element.value;
                    let insertionOffset = 0;
                    
                    for (const placeholder of placeholders) {
                        const insertPosition = placeholder.position + insertionOffset;
                        const formattedPlaceholder = this.formatPlaceholder(placeholder);
                        
                        currentValue = 
                            currentValue.slice(0, insertPosition) +
                            formattedPlaceholder +
                            currentValue.slice(insertPosition);
                        
                        insertionOffset += formattedPlaceholder.length;
                    }
                    
                    element.value = currentValue;
                    this.updateRecord(element, currentValue);
                },
                
                // 验证占位符
                validatePlaceholder: (placeholder) => {
                    return this.validatePlaceholderFormat(placeholder) &&
                           this.validatePlaceholderLength(placeholder) &&
                           this.validatePlaceholderSecurity(placeholder);
                },
                
                // 格式化占位符
                formatPlaceholder: (placeholder) => {
                    const formatter = this.placeholderConfig.formatters.get(placeholder.type);
                    if (formatter) {
                        return formatter(placeholder);
                    }
                    
                    return this.getDefaultPlaceholderFormat(placeholder);
                },
                
                // 获取占位符建议
                getPlaceholderSuggestions: (model, query = '') => {
                    return this.generatePlaceholderSuggestions(model, query);
                },
                
                // 预览占位符
                previewPlaceholder: (placeholder, sampleData) => {
                    return this.renderPlaceholderPreview(placeholder, sampleData);
                }
            };
            
            return enhancedHook;
        };
    }
    
    // 检查自定义触发器
    checkCustomTriggers(ev) {
        for (const [trigger, config] of this.placeholderConfig.customTriggers) {
            if (this.matchesTrigger(ev, trigger)) {
                return config;
            }
        }
        return null;
    }
    
    // 匹配触发器
    matchesTrigger(ev, trigger) {
        if (typeof trigger === 'string') {
            return ev.key === trigger;
        }
        
        if (typeof trigger === 'object') {
            return ev.key === trigger.key &&
                   ev.ctrlKey === (trigger.ctrl || false) &&
                   ev.shiftKey === (trigger.shift || false) &&
                   ev.altKey === (trigger.alt || false);
        }
        
        return false;
    }
    
    // 处理自定义触发器
    handleCustomTrigger = async (triggerConfig, ev, elementRef) => {
        if (triggerConfig.handler) {
            await triggerConfig.handler(ev, elementRef, this);
        }
    };
    
    // 预验证打开
    preValidateOpen(opts) {
        // 检查必需参数
        if (!opts.validateCallback) {
            console.error('Validate callback is required');
            return false;
        }
        
        // 检查权限
        if (!this.hasPlaceholderPermission()) {
            console.error('No permission to use dynamic placeholders');
            return false;
        }
        
        return true;
    }
    
    // 增强打开选项
    enhanceOpenOptions(opts) {
        return {
            ...opts,
            enablePreview: this.placeholderConfig.enablePreview,
            enableSuggestions: this.placeholderConfig.enableSuggestions,
            customFormatters: this.placeholderConfig.formatters
        };
    }
    
    // 验证占位符格式
    validatePlaceholderFormat(placeholder) {
        const formatRegex = /^\{\{object\.[a-zA-Z_][a-zA-Z0-9_.]*(\s*\|\|\|\s*.*)?\}\}$/;
        return formatRegex.test(placeholder);
    }
    
    // 验证占位符长度
    validatePlaceholderLength(placeholder) {
        return placeholder.length <= this.placeholderConfig.maxPlaceholderLength;
    }
    
    // 验证占位符安全性
    validatePlaceholderSecurity(placeholder) {
        // 检查恶意代码
        const dangerousPatterns = [
            /<script/i,
            /javascript:/i,
            /on\w+\s*=/i,
            /eval\s*\(/i
        ];
        
        return !dangerousPatterns.some(pattern => pattern.test(placeholder));
    }
    
    // 获取默认占位符格式
    getDefaultPlaceholderFormat(placeholder) {
        const { path, defaultValue } = placeholder;
        return `{{object.${path}${defaultValue ? ` ||| ${defaultValue}` : ''}}}`;
    }
    
    // 生成占位符建议
    generatePlaceholderSuggestions(model, query) {
        // 实现占位符建议逻辑
        const suggestions = [];
        
        // 基于模型字段生成建议
        if (this.modelCache.has(model)) {
            const modelInfo = this.modelCache.get(model);
            for (const field of modelInfo.fields) {
                if (field.name.includes(query.toLowerCase())) {
                    suggestions.push({
                        path: field.name,
                        label: field.string || field.name,
                        type: field.type,
                        description: field.help
                    });
                }
            }
        }
        
        return suggestions;
    }
    
    // 渲染占位符预览
    renderPlaceholderPreview(placeholder, sampleData) {
        try {
            // 简单的占位符渲染逻辑
            const pathMatch = placeholder.match(/object\.([^|}]+)/);
            if (pathMatch) {
                const path = pathMatch[1];
                const value = this.getNestedValue(sampleData, path);
                return value !== undefined ? String(value) : placeholder;
            }
        } catch (error) {
            console.error('Preview render error:', error);
        }
        
        return placeholder;
    }
    
    // 获取嵌套值
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    }
    
    // 更新记录
    updateRecord(element, value) {
        // 实现记录更新逻辑
        console.log('Updating record with value:', value);
    }
    
    // 检查占位符权限
    hasPlaceholderPermission() {
        // 实现权限检查逻辑
        return true;
    }
    
    // 模型缓存有效性检查
    isModelCacheValid(cachedModel) {
        const now = Date.now();
        const cacheAge = now - cachedModel.timestamp;
        return cacheAge < 300000; // 5分钟缓存
    }
    
    // 更新模型缓存
    updateModelCache(modelNameLocation) {
        // 实现模型缓存更新逻辑
        console.log('Updating model cache for:', modelNameLocation);
    }
    
    // 记录打开事件
    recordOpenEvent(opts) {
        this.placeholderStatistics.totalPlaceholders++;
        console.log('Placeholder selector opened:', opts);
    }
    
    // 处理插入错误
    handleInsertionError(error) {
        console.error('Placeholder insertion error:', error);
    }
    
    // 处理打开错误
    handleOpenError(error) {
        console.error('Placeholder open error:', error);
    }
    
    // 记录性能
    recordPerformance(duration) {
        const total = this.placeholderStatistics.successfulInsertions;
        this.placeholderStatistics.averageInsertionTime = 
            (this.placeholderStatistics.averageInsertionTime * (total - 1) + duration) / total;
    }
    
    // 设置触发器系统
    setupTriggerSystem() {
        // 注册默认触发器
        this.placeholderConfig.customTriggers.set('$', {
            handler: this.handleVariableTrigger.bind(this),
            description: 'Variable placeholder trigger'
        });
        
        this.placeholderConfig.customTriggers.set({ key: 'p', ctrl: true }, {
            handler: this.handleQuickPlaceholder.bind(this),
            description: 'Quick placeholder trigger (Ctrl+P)'
        });
    }
    
    // 处理变量触发器
    handleVariableTrigger = async (ev, elementRef, manager) => {
        // 实现变量占位符逻辑
        console.log('Variable trigger activated');
    };
    
    // 处理快速占位符
    handleQuickPlaceholder = async (ev, elementRef, manager) => {
        // 实现快速占位符逻辑
        console.log('Quick placeholder trigger activated');
    };
    
    // 设置格式化系统
    setupFormatterSystem() {
        // 注册默认格式化器
        this.placeholderConfig.formatters.set('date', (placeholder) => {
            return `{{object.${placeholder.path} | date}}`;
        });
        
        this.placeholderConfig.formatters.set('currency', (placeholder) => {
            return `{{object.${placeholder.path} | currency}}`;
        });
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationRules = {
            format: this.validatePlaceholderFormat.bind(this),
            length: this.validatePlaceholderLength.bind(this),
            security: this.validatePlaceholderSecurity.bind(this)
        };
    }
    
    // 创建动态占位符钩子
    createDynamicPlaceholderHook(elementRef, options) {
        return this.enhancedUseDynamicPlaceholder(elementRef, options);
    }
    
    // 注册自定义触发器
    registerCustomTrigger(trigger, config) {
        this.placeholderConfig.customTriggers.set(trigger, config);
    }
    
    // 注册自定义格式化器
    registerCustomFormatter(name, formatter) {
        this.placeholderConfig.formatters.set(name, formatter);
    }
    
    // 获取占位符统计
    getPlaceholderStatistics() {
        return {
            ...this.placeholderStatistics,
            registeredTriggers: this.placeholderConfig.customTriggers.size,
            registeredFormatters: this.placeholderConfig.formatters.size,
            modelCacheSize: this.modelCache.size
        };
    }
    
    // 清理缓存
    clearCache() {
        this.modelCache.clear();
        this.placeholderRegistry.clear();
    }
    
    // 销毁管理器
    destroy() {
        // 清理缓存
        this.modelCache.clear();
        this.placeholderRegistry.clear();
        
        // 清理触发器
        this.placeholderConfig.customTriggers.clear();
        this.placeholderConfig.formatters.clear();
        
        // 重置统计
        this.placeholderStatistics = {
            totalPlaceholders: 0,
            successfulInsertions: 0,
            failedInsertions: 0,
            averageInsertionTime: 0
        };
    }
}

// 使用示例
const placeholderManager = new DynamicPlaceholderManager();

// 创建动态占位符钩子
const elementRef = { el: document.getElementById('myInput') };
const dynamicPlaceholderHook = placeholderManager.createDynamicPlaceholderHook(elementRef, {
    enablePreview: true,
    enableSuggestions: true
});

// 注册自定义触发器
placeholderManager.registerCustomTrigger('@', {
    handler: async (ev, elementRef, manager) => {
        console.log('Custom @ trigger activated');
    },
    description: 'Custom @ trigger for mentions'
});

// 注册自定义格式化器
placeholderManager.registerCustomFormatter('uppercase', (placeholder) => {
    return `{{object.${placeholder.path} | upper}}`;
});

// 获取统计信息
const stats = placeholderManager.getPlaceholderStatistics();
console.log('Dynamic placeholder statistics:', stats);
```

## 技术特点

### 1. 用户友好
- **键盘触发**: 通过简单的键盘输入触发
- **即时反馈**: 提供即时的视觉反馈
- **智能插入**: 在正确位置插入占位符
- **错误提示**: 友好的错误提示信息

### 2. 灵活配置
- **可配置触发器**: 支持自定义触发字符
- **模型适配**: 支持不同模型的字段选择
- **格式定制**: 支持自定义占位符格式
- **回调机制**: 灵活的回调函数机制

### 3. 性能优化
- **轻量级**: 简洁高效的代码实现
- **事件驱动**: 基于事件的触发机制
- **内存友好**: 有效的内存使用
- **响应迅速**: 快速的用户响应

### 4. 扩展性
- **钩子设计**: 基于钩子的可扩展设计
- **组件集成**: 易于与其他组件集成
- **功能扩展**: 支持功能的模块化扩展
- **自定义支持**: 支持自定义扩展

## 设计模式

### 1. 钩子模式 (Hook Pattern)
- **状态管理**: 管理占位符相关状态
- **生命周期**: 处理组件生命周期
- **副作用**: 处理键盘事件等副作用

### 2. 观察者模式 (Observer Pattern)
- **事件监听**: 监听键盘事件
- **状态变化**: 响应状态变化
- **回调通知**: 通过回调通知状态变化

### 3. 策略模式 (Strategy Pattern)
- **触发策略**: 不同的触发处理策略
- **格式化策略**: 不同的占位符格式化策略
- **验证策略**: 不同的验证策略

### 4. 工厂模式 (Factory Pattern)
- **占位符工厂**: 创建不同类型的占位符
- **弹出框工厂**: 创建弹出框实例
- **回调工厂**: 创建回调函数

## 注意事项

1. **性能考虑**: 避免频繁的DOM操作
2. **内存管理**: 及时清理事件监听器
3. **用户体验**: 确保流畅的用户交互
4. **安全性**: 验证用户输入的安全性

## 扩展建议

1. **多触发器**: 支持多种触发字符和组合键
2. **智能建议**: 提供智能的字段建议
3. **预览功能**: 添加占位符预览功能
4. **模板系统**: 支持占位符模板
5. **历史记录**: 添加使用历史记录功能

该动态占位符钩子为Odoo Web客户端提供了强大的动态内容生成功能，通过简单的键盘交互和直观的字段选择确保了用户能够轻松创建动态占位符。

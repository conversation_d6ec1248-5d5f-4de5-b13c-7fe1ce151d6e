# CopyClipboardField - 复制到剪贴板字段

## 概述

`copy_clipboard_field.js` 是 Odoo Web 客户端的复制到剪贴板字段组件，负责提供一键复制功能。该模块包含109行代码，是一个实用的复制工具组件，专门用于将字段值复制到系统剪贴板，具备多种字段类型支持、禁用控制、自定义文本、图标配置等特性，是提升用户体验的重要工具组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js`
- **行数**: 109
- **模块**: `@web/views/fields/copy_clipboard/copy_clipboard_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'            // 翻译服务
'@web/core/py_js/py'                    // Python表达式评估
'@web/core/registry'                    // 注册表
'@web/core/utils/objects'               // 对象工具
'@web/core/copy_button/copy_button'     // 复制按钮组件
'@web/views/fields/char/char_field'     // 字符字段
'@web/views/fields/standard_field_props' // 标准字段属性
'@web/views/fields/url/url_field'       // URL字段
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 基础组件定义

```javascript
class CopyClipboardField extends Component {
    static template = "web.CopyClipboardField";
    static props = {
        ...standardFieldProps,
        string: { type: String, optional: true },
        disabledExpr: { type: String, optional: true },
    };

    setup() {
        this.copyText = this.props.string || _t("Copy");
        this.successText = _t("Copied");
    }
}
```

**基础特性**:
- **标准属性**: 继承所有标准字段属性
- **自定义文本**: 支持string属性自定义复制按钮文本
- **禁用表达式**: 支持disabledExpr动态禁用控制
- **国际化**: 支持多语言文本显示

### 2. 属性计算

```javascript
get copyButtonClassName() {
    return `o_btn_${this.type}_copy btn-sm`;
}
get fieldProps() {
    return omit(this.props, "string", "disabledExpr");
}
get type() {
    return this.props.record.fields[this.props.name].type;
}
get disabled() {
    return this.props.disabledExpr
        ? evaluateBooleanExpr(
              this.props.disabledExpr,
              this.props.record.evalContextWithVirtualIds
          )
        : false;
}
```

**属性功能**:
- **样式类名**: 根据字段类型生成按钮样式类名
- **字段属性**: 过滤并传递字段属性
- **类型获取**: 获取字段的数据类型
- **禁用状态**: 通过表达式评估确定禁用状态

### 3. 按钮字段变体

```javascript
const CopyClipboardButtonField = class CopyClipboardButtonField extends CopyClipboardField {
    static template = "web.CopyClipboardButtonField";
    static components = { CopyButton };

    get copyButtonClassName() {
        return `o_btn_${this.type}_copy btn-primary rounded-2`;
    }
}
```

**按钮变体功能**:
- **专用模板**: 使用按钮专用模板
- **复制组件**: 集成CopyButton组件
- **样式重写**: 重写按钮样式类名
- **主要按钮**: 使用主要按钮样式

### 4. 字符字段变体

```javascript
const CopyClipboardCharField = class CopyClipboardCharField extends CopyClipboardField {
    static components = { Field: CharField, CopyButton };

    get copyButtonIcon() {
        return "fa-clone";
    }
}
```

**字符变体功能**:
- **字符字段**: 集成CharField组件
- **复制按钮**: 集成CopyButton组件
- **克隆图标**: 使用克隆图标
- **文本复制**: 专门用于文本复制

### 5. URL字段变体

```javascript
const CopyClipboardURLField = class CopyClipboardURLField extends CopyClipboardField {
    static components = { Field: UrlField, CopyButton };

    get copyButtonIcon() {
        return "fa-link";
    }
}
```

**URL变体功能**:
- **URL字段**: 集成UrlField组件
- **复制按钮**: 集成CopyButton组件
- **链接图标**: 使用链接图标
- **URL复制**: 专门用于URL复制

### 6. 字段注册

```javascript
function extractProps({ attrs }) {
    return {
        string: attrs.string,
        disabledExpr: attrs.disabled,
    };
}

const copyClipboardButtonField = {
    component: CopyClipboardButtonField,
    displayName: _t("Copy to Clipboard"),
    extractProps,
};

const copyClipboardCharField = {
    component: CopyClipboardCharField,
    displayName: _t("Copy Text to Clipboard"),
    supportedTypes: ["char"],
    extractProps,
};

const copyClipboardURLField = {
    component: CopyClipboardURLField,
    displayName: _t("Copy URL to Clipboard"),
    supportedTypes: ["char"],
    extractProps,
};

registry.category("fields").add("CopyClipboardButton", copyClipboardButtonField);
registry.category("fields").add("CopyClipboardChar", copyClipboardCharField);
registry.category("fields").add("CopyClipboardURL", copyClipboardURLField);
```

**注册功能**:
- **属性提取**: 统一的属性提取函数
- **多重注册**: 注册三种不同的复制字段变体
- **类型支持**: 字符和URL变体支持char类型
- **显示名称**: 国际化的显示名称

## 使用场景

### 1. 复制剪贴板字段管理器

```javascript
// 复制剪贴板字段管理器
class CopyClipboardFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置复制配置
        this.copyConfig = {
            enableNotifications: true,
            enableHistory: true,
            enableBatchCopy: true,
            enableFormatting: true,
            maxHistorySize: 50,
            notificationDuration: 2000,
            enableAnalytics: true,
            enableCustomFormats: true
        };
        
        // 设置复制格式
        this.copyFormats = new Map([
            ['plain', { name: 'Plain Text', formatter: (value) => value }],
            ['json', { name: 'JSON', formatter: (value) => JSON.stringify(value, null, 2) }],
            ['csv', { name: 'CSV', formatter: (value) => `"${value.replace(/"/g, '""')}"` }],
            ['html', { name: 'HTML', formatter: (value) => `<span>${value}</span>` }],
            ['markdown', { name: 'Markdown', formatter: (value) => `\`${value}\`` }]
        ]);
        
        // 设置复制历史
        this.copyHistory = [];
        
        // 设置复制统计
        this.copyStatistics = {
            totalCopies: 0,
            successfulCopies: 0,
            failedCopies: 0,
            averageCopyTime: 0,
            popularFields: new Map(),
            formatUsage: new Map()
        };
        
        // 设置通知管理
        this.notificationManager = {
            enabled: this.copyConfig.enableNotifications,
            duration: this.copyConfig.notificationDuration,
            position: 'top-right'
        };
        
        this.initializeCopySystem();
    }
    
    // 初始化复制系统
    initializeCopySystem() {
        // 创建增强的复制字段
        this.createEnhancedCopyClipboardField();
        
        // 设置历史管理
        this.setupHistoryManagement();
        
        // 设置格式化系统
        this.setupFormattingSystem();
        
        // 设置分析系统
        this.setupAnalyticsSystem();
    }
    
    // 创建增强的复制字段
    createEnhancedCopyClipboardField() {
        const originalField = CopyClipboardField;
        
        this.EnhancedCopyClipboardField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加格式化功能
                this.addFormattingFeatures();
                
                // 添加历史功能
                this.addHistoryFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isCopying: false,
                    lastCopyTime: null,
                    copyFormat: 'plain',
                    copyHistory: [],
                    customFormatter: null,
                    copyCount: 0
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的复制功能
                this.enhancedCopy = async (format = 'plain') => {
                    if (this.enhancedState.isCopying) {
                        return false;
                    }
                    
                    const startTime = performance.now();
                    this.enhancedState.isCopying = true;
                    
                    try {
                        // 获取要复制的值
                        const rawValue = this.props.record.data[this.props.name];
                        
                        if (rawValue === null || rawValue === undefined) {
                            throw new Error('No value to copy');
                        }
                        
                        // 格式化值
                        const formattedValue = this.formatValue(rawValue, format);
                        
                        // 执行复制
                        const success = await this.copyToClipboard(formattedValue);
                        
                        if (success) {
                            // 记录历史
                            this.addToHistory(rawValue, formattedValue, format);
                            
                            // 显示通知
                            if (this.copyConfig.enableNotifications) {
                                this.showCopyNotification(true, format);
                            }
                            
                            // 记录统计
                            this.recordCopyStatistics(true, format);
                            
                            // 更新状态
                            this.enhancedState.lastCopyTime = Date.now();
                            this.enhancedState.copyCount++;
                            
                        } else {
                            throw new Error('Copy operation failed');
                        }
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordCopyTime(endTime - startTime);
                        
                        return success;
                        
                    } catch (error) {
                        this.handleCopyError(error);
                        return false;
                    } finally {
                        this.enhancedState.isCopying = false;
                    }
                };
                
                // 复制到剪贴板
                this.copyToClipboard = async (text) => {
                    try {
                        if (navigator.clipboard && window.isSecureContext) {
                            await navigator.clipboard.writeText(text);
                            return true;
                        } else {
                            // 降级方案
                            return this.fallbackCopyToClipboard(text);
                        }
                    } catch (error) {
                        console.error('Clipboard copy failed:', error);
                        return this.fallbackCopyToClipboard(text);
                    }
                };
                
                // 降级复制方案
                this.fallbackCopyToClipboard = (text) => {
                    try {
                        const textArea = document.createElement('textarea');
                        textArea.value = text;
                        textArea.style.position = 'fixed';
                        textArea.style.left = '-999999px';
                        textArea.style.top = '-999999px';
                        document.body.appendChild(textArea);
                        textArea.focus();
                        textArea.select();
                        
                        const successful = document.execCommand('copy');
                        document.body.removeChild(textArea);
                        
                        return successful;
                    } catch (error) {
                        console.error('Fallback copy failed:', error);
                        return false;
                    }
                };
                
                // 格式化值
                this.formatValue = (value, format) => {
                    // 使用自定义格式化器
                    if (this.enhancedState.customFormatter) {
                        return this.enhancedState.customFormatter(value);
                    }
                    
                    // 使用预定义格式
                    const formatter = this.copyFormats.get(format);
                    if (formatter) {
                        return formatter.formatter(value);
                    }
                    
                    // 默认格式
                    return String(value);
                };
                
                // 添加到历史
                this.addToHistory = (originalValue, formattedValue, format) => {
                    if (!this.copyConfig.enableHistory) return;
                    
                    const historyEntry = {
                        originalValue: originalValue,
                        formattedValue: formattedValue,
                        format: format,
                        timestamp: Date.now(),
                        fieldName: this.props.name
                    };
                    
                    this.enhancedState.copyHistory.unshift(historyEntry);
                    this.copyHistory.unshift(historyEntry);
                    
                    // 限制历史大小
                    if (this.copyHistory.length > this.copyConfig.maxHistorySize) {
                        this.copyHistory.pop();
                    }
                    
                    if (this.enhancedState.copyHistory.length > 10) {
                        this.enhancedState.copyHistory.pop();
                    }
                };
                
                // 显示复制通知
                this.showCopyNotification = (success, format) => {
                    const message = success 
                        ? `Copied as ${format} format`
                        : 'Copy failed';
                    
                    // 实现通知显示逻辑
                    console.log('Copy notification:', message);
                };
                
                // 批量复制
                this.batchCopy = async (fields, format = 'plain') => {
                    if (!this.copyConfig.enableBatchCopy) {
                        throw new Error('Batch copy is disabled');
                    }
                    
                    const results = [];
                    const values = [];
                    
                    for (const field of fields) {
                        try {
                            const value = field.props.record.data[field.props.name];
                            const formattedValue = this.formatValue(value, format);
                            values.push(formattedValue);
                            results.push({ field, success: true, value: formattedValue });
                        } catch (error) {
                            results.push({ field, success: false, error });
                        }
                    }
                    
                    // 复制所有值
                    const combinedValue = values.join('\n');
                    const copySuccess = await this.copyToClipboard(combinedValue);
                    
                    return { results, copySuccess, combinedValue };
                };
                
                // 设置复制格式
                this.setCopyFormat = (format) => {
                    if (this.copyFormats.has(format)) {
                        this.enhancedState.copyFormat = format;
                    }
                };
                
                // 设置自定义格式化器
                this.setCustomFormatter = (formatter) => {
                    if (typeof formatter === 'function') {
                        this.enhancedState.customFormatter = formatter;
                    }
                };
                
                // 获取复制历史
                this.getCopyHistory = () => {
                    return this.enhancedState.copyHistory;
                };
                
                // 清除历史
                this.clearHistory = () => {
                    this.enhancedState.copyHistory = [];
                };
                
                // 获取复制统计
                this.getCopyStatistics = () => {
                    return {
                        copyCount: this.enhancedState.copyCount,
                        lastCopyTime: this.enhancedState.lastCopyTime,
                        currentFormat: this.enhancedState.copyFormat
                    };
                };
                
                // 检查剪贴板支持
                this.checkClipboardSupport = () => {
                    return {
                        modern: !!(navigator.clipboard && window.isSecureContext),
                        fallback: !!document.execCommand,
                        supported: !!(navigator.clipboard && window.isSecureContext) || !!document.execCommand
                    };
                };
                
                // 记录统计
                this.recordCopyStatistics = (success, format) => {
                    this.copyStatistics.totalCopies++;
                    
                    if (success) {
                        this.copyStatistics.successfulCopies++;
                        
                        // 记录格式使用
                        const formatUsage = this.copyStatistics.formatUsage.get(format) || 0;
                        this.copyStatistics.formatUsage.set(format, formatUsage + 1);
                        
                        // 记录字段使用
                        const fieldUsage = this.copyStatistics.popularFields.get(this.props.name) || 0;
                        this.copyStatistics.popularFields.set(this.props.name, fieldUsage + 1);
                    } else {
                        this.copyStatistics.failedCopies++;
                    }
                };
                
                // 处理复制错误
                this.handleCopyError = (error) => {
                    console.error('Copy error:', error);
                    
                    if (this.copyConfig.enableNotifications) {
                        this.showCopyNotification(false);
                    }
                };
                
                // 记录复制时间
                this.recordCopyTime = (duration) => {
                    this.copyStatistics.averageCopyTime = 
                        (this.copyStatistics.averageCopyTime + duration) / 2;
                };
            }
            
            addFormattingFeatures() {
                // 格式化功能
                this.formattingManager = {
                    enabled: this.copyConfig.enableFormatting,
                    formats: this.copyFormats,
                    format: (value, type) => this.formatValue(value, type)
                };
            }
            
            addHistoryFeatures() {
                // 历史功能
                this.historyManager = {
                    enabled: this.copyConfig.enableHistory,
                    maxSize: this.copyConfig.maxHistorySize,
                    add: (entry) => this.addToHistory(entry.originalValue, entry.formattedValue, entry.format)
                };
            }
        };
    }
    
    // 设置历史管理
    setupHistoryManagement() {
        this.historyConfig = {
            enabled: this.copyConfig.enableHistory,
            maxSize: this.copyConfig.maxHistorySize,
            enablePersistence: false
        };
    }
    
    // 设置格式化系统
    setupFormattingSystem() {
        this.formattingConfig = {
            enabled: this.copyConfig.enableFormatting,
            defaultFormat: 'plain',
            customFormats: this.copyConfig.enableCustomFormats
        };
    }
    
    // 设置分析系统
    setupAnalyticsSystem() {
        this.analyticsConfig = {
            enabled: this.copyConfig.enableAnalytics,
            trackUsage: true,
            trackPerformance: true
        };
    }
    
    // 创建复制字段
    createCopyClipboardField(props, variant = 'button') {
        switch (variant) {
            case 'button':
                return new this.EnhancedCopyClipboardButtonField(props);
            case 'char':
                return new this.EnhancedCopyClipboardCharField(props);
            case 'url':
                return new this.EnhancedCopyClipboardURLField(props);
            default:
                return new this.EnhancedCopyClipboardField(props);
        }
    }
    
    // 注册自定义格式
    registerCopyFormat(name, formatter, displayName) {
        this.copyFormats.set(name, {
            name: displayName || name,
            formatter: formatter
        });
    }
    
    // 批量复制多个字段
    batchCopyFields(fields, format = 'plain') {
        const results = [];
        const values = [];
        
        for (const field of fields) {
            try {
                const value = field.props.record.data[field.props.name];
                const formattedValue = this.formatValue(value, format);
                values.push(formattedValue);
                results.push({ field, success: true, value: formattedValue });
            } catch (error) {
                results.push({ field, success: false, error });
            }
        }
        
        return { results, values };
    }
    
    // 获取热门字段
    getPopularFields(limit = 10) {
        const sorted = Array.from(this.copyStatistics.popularFields.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, limit);
        
        return sorted.map(([field, count]) => ({ field, count }));
    }
    
    // 获取复制统计
    getCopyStatistics() {
        return {
            ...this.copyStatistics,
            historySize: this.copyHistory.length,
            formatCount: this.copyFormats.size,
            successRate: this.copyStatistics.successfulCopies / Math.max(this.copyStatistics.totalCopies, 1) * 100
        };
    }
    
    // 导出复制历史
    exportCopyHistory() {
        return {
            history: this.copyHistory,
            statistics: this.copyStatistics,
            timestamp: Date.now()
        };
    }
    
    // 清理历史和统计
    cleanup() {
        this.copyHistory = [];
        this.copyStatistics = {
            totalCopies: 0,
            successfulCopies: 0,
            failedCopies: 0,
            averageCopyTime: 0,
            popularFields: new Map(),
            formatUsage: new Map()
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理格式和历史
        this.copyFormats.clear();
        this.copyHistory = [];
        
        // 重置统计
        this.copyStatistics = {
            totalCopies: 0,
            successfulCopies: 0,
            failedCopies: 0,
            averageCopyTime: 0,
            popularFields: new Map(),
            formatUsage: new Map()
        };
    }
}

// 使用示例
const copyManager = new CopyClipboardFieldManager();

// 创建复制字段
const copyField = copyManager.createCopyClipboardField({
    name: 'api_key',
    record: {
        data: { api_key: 'sk-1234567890abcdef' },
        fields: { api_key: { type: 'char' } }
    },
    string: 'Copy API Key'
}, 'char');

// 注册自定义格式
copyManager.registerCopyFormat('base64', (value) => btoa(value), 'Base64');

// 执行复制
copyField.enhancedCopy('plain');

// 获取统计信息
const stats = copyManager.getCopyStatistics();
console.log('Copy clipboard statistics:', stats);
```

## 技术特点

### 1. 多变体支持
- **按钮变体**: 纯按钮形式的复制功能
- **字符变体**: 字符字段与复制按钮组合
- **URL变体**: URL字段与复制按钮组合
- **统一基类**: 共享基础功能和属性

### 2. 功能完整
- **复制按钮**: 集成CopyButton组件
- **禁用控制**: 支持动态禁用表达式
- **自定义文本**: 支持自定义按钮文本
- **图标配置**: 不同变体使用不同图标

### 3. 表达式支持
- **禁用表达式**: 支持Python表达式评估
- **动态控制**: 根据记录状态动态控制
- **上下文评估**: 使用记录的评估上下文
- **灵活配置**: 灵活的条件配置

### 4. 组件集成
- **字段集成**: 集成CharField和UrlField
- **按钮集成**: 集成CopyButton组件
- **属性传递**: 智能的属性过滤和传递
- **模板分离**: 不同变体使用不同模板

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **基类继承**: 各变体继承基础CopyClipboardField
- **功能扩展**: 扩展基类功能
- **模板重写**: 重写显示模板

### 2. 组合模式 (Composition Pattern)
- **组件组合**: 组合不同的字段和按钮组件
- **功能组合**: 组合复制和显示功能
- **模块组合**: 组合不同的功能模块

### 3. 策略模式 (Strategy Pattern)
- **复制策略**: 不同的复制处理策略
- **格式策略**: 不同的格式化策略
- **显示策略**: 不同的显示策略

### 4. 工厂模式 (Factory Pattern)
- **组件工厂**: 创建不同变体的复制字段
- **配置工厂**: 创建配置对象
- **格式工厂**: 创建格式化器

## 注意事项

1. **浏览器兼容**: 确保在不同浏览器中的剪贴板API兼容性
2. **安全上下文**: 现代剪贴板API需要安全上下文（HTTPS）
3. **用户体验**: 提供清晰的复制成功反馈
4. **性能考虑**: 避免频繁的复制操作

## 扩展建议

1. **格式化选项**: 支持更多复制格式选项
2. **批量复制**: 支持批量复制多个字段
3. **历史记录**: 添加复制历史记录功能
4. **自定义通知**: 支持自定义复制成功通知
5. **快捷键**: 添加键盘快捷键支持

该复制剪贴板字段为Odoo Web客户端提供了便捷的一键复制功能，通过多种变体和完善的功能支持确保了良好的用户体验和广泛的适用性。

# HtmlField - HTML字段

## 概述

`html_field.js` 是 Odoo Web 客户端的HTML字段组件，负责处理HTML内容的显示和编辑。该模块包含19行代码，是一个继承自TextField的简洁组件，专门用于处理HTML类型的文本字段，具备HTML渲染、富文本编辑、模板专用等特性，是富文本内容处理的基础组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/html/html_field.js`
- **行数**: 19
- **模块**: `@web/views/fields/html/html_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                    // 注册表
'@web/views/fields/text/text_field'     // 基础文本字段
```

## 核心功能

### 1. 组件定义

```javascript
const HtmlField = class HtmlField extends TextField {
    static template = "web.HtmlField";
}
```

**组件特性**:
- **继承基类**: 继承TextField的所有功能
- **专用模板**: 使用HtmlField专用模板
- **HTML处理**: 专门处理HTML内容
- **简洁实现**: 最简化的代码实现

### 2. 字段注册

```javascript
const htmlField = {
    ...textField,
    component: HtmlField,
};

registry.category("fields").add("html", htmlField);
```

**注册功能**:
- **配置继承**: 继承文本字段的所有配置
- **组件替换**: 使用HtmlField组件
- **类型注册**: 注册为html字段类型
- **功能扩展**: 在文本字段基础上扩展HTML功能

## 使用场景

### 1. HTML字段管理器

```javascript
// HTML字段管理器
class HtmlFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置HTML字段配置
        this.htmlConfig = {
            enableRichEditor: true,
            enableSanitization: true,
            enablePreview: true,
            enableSourceView: true,
            enableValidation: true,
            enableTemplates: true,
            enableImageUpload: true,
            enableLinkManagement: true
        };
        
        // 设置编辑器配置
        this.editorConfig = {
            toolbar: [
                'bold', 'italic', 'underline', 'strikethrough',
                'fontSize', 'fontColor', 'backgroundColor',
                'alignment', 'list', 'link', 'image',
                'table', 'code', 'undo', 'redo'
            ],
            plugins: [
                'textColor', 'backgroundColor', 'fontSize',
                'alignment', 'list', 'link', 'image',
                'table', 'codeBlock', 'mediaEmbed'
            ],
            height: 300,
            placeholder: 'Enter HTML content...'
        };
        
        // 设置HTML验证规则
        this.validationRules = {
            enableTagValidation: true,
            enableAttributeValidation: true,
            enableContentValidation: true,
            allowedTags: [
                'p', 'div', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                'strong', 'em', 'u', 's', 'a', 'img', 'br', 'hr',
                'ul', 'ol', 'li', 'table', 'tr', 'td', 'th',
                'blockquote', 'code', 'pre'
            ],
            allowedAttributes: [
                'class', 'id', 'style', 'href', 'src', 'alt',
                'title', 'target', 'width', 'height'
            ],
            maxLength: 50000
        };
        
        // 设置HTML模板
        this.htmlTemplates = new Map([
            ['basic', '<p>Basic paragraph</p>'],
            ['heading', '<h1>Main Heading</h1><p>Content goes here...</p>'],
            ['list', '<ul><li>Item 1</li><li>Item 2</li><li>Item 3</li></ul>'],
            ['table', '<table><tr><th>Header 1</th><th>Header 2</th></tr><tr><td>Cell 1</td><td>Cell 2</td></tr></table>'],
            ['link', '<p>Visit <a href="https://example.com">our website</a> for more information.</p>'],
            ['image', '<p><img src="placeholder.jpg" alt="Description" style="max-width: 100%;"></p>']
        ]);
        
        // 设置HTML统计
        this.htmlStatistics = {
            totalFields: 0,
            totalContent: 0,
            averageLength: 0,
            tagUsage: new Map(),
            templateUsage: new Map(),
            validationErrors: 0
        };
        
        this.initializeHtmlSystem();
    }
    
    // 初始化HTML系统
    initializeHtmlSystem() {
        // 创建增强的HTML字段
        this.createEnhancedHtmlField();
        
        // 设置编辑器系统
        this.setupEditorSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置模板系统
        this.setupTemplateSystem();
    }
    
    // 创建增强的HTML字段
    createEnhancedHtmlField() {
        const originalField = HtmlField;
        
        this.EnhancedHtmlField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加编辑器功能
                this.addEditorFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isEditing: false,
                    isPreviewMode: false,
                    isSourceMode: false,
                    validationErrors: [],
                    currentTemplate: null,
                    editorInstance: null,
                    contentHistory: []
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 初始化富文本编辑器
                this.initializeRichEditor = () => {
                    if (!this.htmlConfig.enableRichEditor) return;
                    
                    try {
                        // 创建编辑器实例
                        this.enhancedState.editorInstance = this.createEditorInstance();
                        
                        // 设置编辑器事件
                        this.setupEditorEvents();
                        
                        // 加载内容
                        this.loadContentToEditor();
                        
                    } catch (error) {
                        this.handleEditorError(error);
                    }
                };
                
                // 创建编辑器实例
                this.createEditorInstance = () => {
                    // 这里应该集成实际的富文本编辑器，如CKEditor、TinyMCE等
                    // 示例使用简化的编辑器接口
                    return {
                        setData: (data) => this.setEditorData(data),
                        getData: () => this.getEditorData(),
                        on: (event, callback) => this.onEditorEvent(event, callback),
                        destroy: () => this.destroyEditor()
                    };
                };
                
                // 设置编辑器事件
                this.setupEditorEvents = () => {
                    if (!this.enhancedState.editorInstance) return;
                    
                    this.enhancedState.editorInstance.on('change', () => {
                        this.onContentChange();
                    });
                    
                    this.enhancedState.editorInstance.on('focus', () => {
                        this.onEditorFocus();
                    });
                    
                    this.enhancedState.editorInstance.on('blur', () => {
                        this.onEditorBlur();
                    });
                };
                
                // 验证HTML内容
                this.validateHtmlContent = (content) => {
                    const errors = [];
                    
                    if (!content) {
                        return { isValid: true, errors: [] };
                    }
                    
                    try {
                        // 长度验证
                        if (content.length > this.validationRules.maxLength) {
                            errors.push(`Content exceeds maximum length of ${this.validationRules.maxLength} characters`);
                        }
                        
                        // HTML结构验证
                        if (this.validationRules.enableTagValidation) {
                            const tagErrors = this.validateHtmlTags(content);
                            errors.push(...tagErrors);
                        }
                        
                        // 属性验证
                        if (this.validationRules.enableAttributeValidation) {
                            const attrErrors = this.validateHtmlAttributes(content);
                            errors.push(...attrErrors);
                        }
                        
                        // 内容验证
                        if (this.validationRules.enableContentValidation) {
                            const contentErrors = this.validateHtmlStructure(content);
                            errors.push(...contentErrors);
                        }
                        
                        this.enhancedState.validationErrors = errors;
                        
                        if (errors.length > 0) {
                            this.htmlStatistics.validationErrors++;
                        }
                        
                        return { isValid: errors.length === 0, errors };
                        
                    } catch (error) {
                        errors.push(`Validation error: ${error.message}`);
                        return { isValid: false, errors };
                    }
                };
                
                // 验证HTML标签
                this.validateHtmlTags = (content) => {
                    const errors = [];
                    const tagPattern = /<\/?([a-zA-Z][a-zA-Z0-9]*)\b[^>]*>/g;
                    let match;
                    
                    while ((match = tagPattern.exec(content)) !== null) {
                        const tagName = match[1].toLowerCase();
                        
                        if (!this.validationRules.allowedTags.includes(tagName)) {
                            errors.push(`Tag '${tagName}' is not allowed`);
                        }
                        
                        // 记录标签使用统计
                        const usage = this.htmlStatistics.tagUsage.get(tagName) || 0;
                        this.htmlStatistics.tagUsage.set(tagName, usage + 1);
                    }
                    
                    return errors;
                };
                
                // 验证HTML属性
                this.validateHtmlAttributes = (content) => {
                    const errors = [];
                    const attrPattern = /(\w+)=["'][^"']*["']/g;
                    let match;
                    
                    while ((match = attrPattern.exec(content)) !== null) {
                        const attrName = match[1].toLowerCase();
                        
                        if (!this.validationRules.allowedAttributes.includes(attrName)) {
                            errors.push(`Attribute '${attrName}' is not allowed`);
                        }
                    }
                    
                    return errors;
                };
                
                // 验证HTML结构
                this.validateHtmlStructure = (content) => {
                    const errors = [];
                    
                    try {
                        // 创建临时DOM元素进行解析
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = content;
                        
                        // 检查是否有解析错误
                        if (tempDiv.innerHTML !== content) {
                            // 可能存在格式问题，但不一定是错误
                        }
                        
                    } catch (error) {
                        errors.push(`HTML structure error: ${error.message}`);
                    }
                    
                    return errors;
                };
                
                // 清理HTML内容
                this.sanitizeHtmlContent = (content) => {
                    if (!this.htmlConfig.enableSanitization || !content) {
                        return content;
                    }
                    
                    try {
                        // 创建临时DOM元素
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = content;
                        
                        // 移除不允许的标签和属性
                        this.removeDisallowedElements(tempDiv);
                        
                        return tempDiv.innerHTML;
                        
                    } catch (error) {
                        console.error('Sanitization error:', error);
                        return content;
                    }
                };
                
                // 移除不允许的元素
                this.removeDisallowedElements = (element) => {
                    const walker = document.createTreeWalker(
                        element,
                        NodeFilter.SHOW_ELEMENT,
                        null,
                        false
                    );
                    
                    const nodesToRemove = [];
                    let node;
                    
                    while (node = walker.nextNode()) {
                        const tagName = node.tagName.toLowerCase();
                        
                        if (!this.validationRules.allowedTags.includes(tagName)) {
                            nodesToRemove.push(node);
                        } else {
                            // 检查属性
                            const attributes = Array.from(node.attributes);
                            for (const attr of attributes) {
                                if (!this.validationRules.allowedAttributes.includes(attr.name.toLowerCase())) {
                                    node.removeAttribute(attr.name);
                                }
                            }
                        }
                    }
                    
                    // 移除不允许的节点
                    nodesToRemove.forEach(node => {
                        if (node.parentNode) {
                            node.parentNode.removeChild(node);
                        }
                    });
                };
                
                // 应用HTML模板
                this.applyTemplate = (templateName) => {
                    const template = this.htmlTemplates.get(templateName);
                    if (template) {
                        this.setContent(template);
                        this.enhancedState.currentTemplate = templateName;
                        
                        // 记录模板使用
                        const usage = this.htmlStatistics.templateUsage.get(templateName) || 0;
                        this.htmlStatistics.templateUsage.set(templateName, usage + 1);
                    }
                };
                
                // 设置内容
                this.setContent = (content) => {
                    // 验证内容
                    const validation = this.validateHtmlContent(content);
                    if (!validation.isValid) {
                        console.warn('HTML validation errors:', validation.errors);
                    }
                    
                    // 清理内容
                    const sanitizedContent = this.sanitizeHtmlContent(content);
                    
                    // 更新记录
                    this.props.record.update({
                        [this.props.name]: sanitizedContent
                    });
                    
                    // 更新编辑器
                    if (this.enhancedState.editorInstance) {
                        this.enhancedState.editorInstance.setData(sanitizedContent);
                    }
                    
                    // 添加到历史
                    this.addToHistory(sanitizedContent);
                };
                
                // 获取内容
                this.getContent = () => {
                    if (this.enhancedState.editorInstance) {
                        return this.enhancedState.editorInstance.getData();
                    }
                    return this.props.record.data[this.props.name] || '';
                };
                
                // 切换预览模式
                this.togglePreviewMode = () => {
                    this.enhancedState.isPreviewMode = !this.enhancedState.isPreviewMode;
                    
                    if (this.enhancedState.isPreviewMode) {
                        this.showPreview();
                    } else {
                        this.showEditor();
                    }
                };
                
                // 显示预览
                this.showPreview = () => {
                    const content = this.getContent();
                    const sanitizedContent = this.sanitizeHtmlContent(content);
                    
                    // 在预览容器中显示内容
                    this.renderPreview(sanitizedContent);
                };
                
                // 渲染预览
                this.renderPreview = (content) => {
                    // 实现预览渲染逻辑
                    console.log('Rendering preview:', content);
                };
                
                // 切换源码模式
                this.toggleSourceMode = () => {
                    this.enhancedState.isSourceMode = !this.enhancedState.isSourceMode;
                    
                    if (this.enhancedState.isSourceMode) {
                        this.showSourceView();
                    } else {
                        this.showRichEditor();
                    }
                };
                
                // 显示源码视图
                this.showSourceView = () => {
                    const content = this.getContent();
                    // 显示HTML源码
                    this.renderSourceView(content);
                };
                
                // 格式化HTML
                this.formatHtml = (content) => {
                    if (!content) return '';
                    
                    try {
                        // 简单的HTML格式化
                        return content
                            .replace(/></g, '>\n<')
                            .replace(/^\s+|\s+$/gm, '')
                            .split('\n')
                            .map(line => line.trim())
                            .filter(line => line.length > 0)
                            .join('\n');
                    } catch (error) {
                        return content;
                    }
                };
                
                // 提取文本内容
                this.extractTextContent = (htmlContent) => {
                    if (!htmlContent) return '';
                    
                    try {
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = htmlContent;
                        return tempDiv.textContent || tempDiv.innerText || '';
                    } catch (error) {
                        return htmlContent;
                    }
                };
                
                // 添加到历史
                this.addToHistory = (content) => {
                    if (!content) return;
                    
                    const historyEntry = {
                        content: content,
                        timestamp: Date.now(),
                        textLength: this.extractTextContent(content).length
                    };
                    
                    this.enhancedState.contentHistory.unshift(historyEntry);
                    
                    // 限制历史大小
                    if (this.enhancedState.contentHistory.length > 20) {
                        this.enhancedState.contentHistory.pop();
                    }
                };
                
                // 获取内容历史
                this.getContentHistory = () => {
                    return this.enhancedState.contentHistory;
                };
                
                // 清除历史
                this.clearHistory = () => {
                    this.enhancedState.contentHistory = [];
                };
                
                // 获取HTML信息
                this.getHtmlInfo = () => {
                    const content = this.getContent();
                    const textContent = this.extractTextContent(content);
                    const validation = this.validateHtmlContent(content);
                    
                    return {
                        content: content,
                        textContent: textContent,
                        htmlLength: content.length,
                        textLength: textContent.length,
                        isValid: validation.isValid,
                        errors: validation.errors,
                        template: this.enhancedState.currentTemplate,
                        isEditing: this.enhancedState.isEditing,
                        isPreviewMode: this.enhancedState.isPreviewMode,
                        isSourceMode: this.enhancedState.isSourceMode
                    };
                };
                
                // 事件处理器
                this.onContentChange = () => {
                    const content = this.getContent();
                    this.validateHtmlContent(content);
                    this.recordContentStatistics(content);
                };
                
                this.onEditorFocus = () => {
                    this.enhancedState.isEditing = true;
                };
                
                this.onEditorBlur = () => {
                    this.enhancedState.isEditing = false;
                    const content = this.getContent();
                    this.setContent(content); // 触发验证和清理
                };
                
                // 记录内容统计
                this.recordContentStatistics = (content) => {
                    this.htmlStatistics.totalContent++;
                    
                    if (content) {
                        this.htmlStatistics.averageLength = 
                            (this.htmlStatistics.averageLength + content.length) / 2;
                    }
                };
                
                // 处理编辑器错误
                this.handleEditorError = (error) => {
                    console.error('HTML editor error:', error);
                };
                
                // 编辑器数据操作
                this.setEditorData = (data) => {
                    // 实现编辑器数据设置
                    console.log('Setting editor data:', data);
                };
                
                this.getEditorData = () => {
                    // 实现编辑器数据获取
                    return this.props.record.data[this.props.name] || '';
                };
                
                this.onEditorEvent = (event, callback) => {
                    // 实现编辑器事件处理
                    console.log('Editor event:', event);
                };
                
                this.destroyEditor = () => {
                    // 实现编辑器销毁
                    this.enhancedState.editorInstance = null;
                };
            }
            
            addEditorFeatures() {
                // 编辑器功能
                this.editorManager = {
                    enabled: this.htmlConfig.enableRichEditor,
                    initialize: () => this.initializeRichEditor(),
                    togglePreview: () => this.togglePreviewMode(),
                    toggleSource: () => this.toggleSourceMode()
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.htmlConfig.enableValidation,
                    validate: (content) => this.validateHtmlContent(content),
                    sanitize: (content) => this.sanitizeHtmlContent(content),
                    getErrors: () => this.enhancedState.validationErrors
                };
            }
        };
    }
    
    // 设置编辑器系统
    setupEditorSystem() {
        this.editorSystemConfig = {
            enabled: this.htmlConfig.enableRichEditor,
            config: this.editorConfig
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.htmlConfig.enableValidation,
            rules: this.validationRules
        };
    }
    
    // 设置模板系统
    setupTemplateSystem() {
        this.templateSystemConfig = {
            enabled: this.htmlConfig.enableTemplates,
            templates: this.htmlTemplates
        };
    }
    
    // 创建HTML字段
    createHtmlField(props) {
        const field = new this.EnhancedHtmlField(props);
        this.htmlStatistics.totalFields++;
        return field;
    }
    
    // 注册HTML模板
    registerHtmlTemplate(name, content) {
        this.htmlTemplates.set(name, content);
    }
    
    // 批量验证HTML内容
    batchValidateHtml(fields) {
        const results = [];
        
        for (const field of fields) {
            try {
                const content = field.getContent();
                const validation = field.validateHtmlContent(content);
                results.push({ field, content, ...validation });
            } catch (error) {
                results.push({ field, content: null, isValid: false, errors: [error.message] });
            }
        }
        
        return results;
    }
    
    // 获取热门标签
    getPopularTags(limit = 10) {
        const sorted = Array.from(this.htmlStatistics.tagUsage.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, limit);
        
        return sorted.map(([tag, count]) => ({ tag, count }));
    }
    
    // 获取HTML统计
    getHtmlStatistics() {
        return {
            ...this.htmlStatistics,
            templateCount: this.htmlTemplates.size,
            tagCount: this.htmlStatistics.tagUsage.size,
            errorRate: this.htmlStatistics.validationErrors / Math.max(this.htmlStatistics.totalContent, 1) * 100
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理模板
        this.htmlTemplates.clear();
        
        // 重置统计
        this.htmlStatistics = {
            totalFields: 0,
            totalContent: 0,
            averageLength: 0,
            tagUsage: new Map(),
            templateUsage: new Map(),
            validationErrors: 0
        };
    }
}

// 使用示例
const htmlManager = new HtmlFieldManager();

// 创建HTML字段
const htmlField = htmlManager.createHtmlField({
    name: 'description',
    record: {
        data: { description: '<p>Welcome to our <strong>amazing</strong> product!</p>' },
        fields: { description: { type: 'html' } }
    }
});

// 应用模板
htmlField.applyTemplate('heading');

// 注册自定义模板
htmlManager.registerHtmlTemplate('custom', '<div class="custom"><h2>Custom Template</h2><p>Content here...</p></div>');

// 获取统计信息
const stats = htmlManager.getHtmlStatistics();
console.log('HTML field statistics:', stats);
```

## 技术特点

### 1. 继承扩展
- **基类继承**: 继承TextField的所有功能
- **模板专用**: 使用专用的HTML模板
- **功能扩展**: 在文本字段基础上扩展HTML功能
- **配置复用**: 复用文本字段的配置

### 2. HTML处理
- **HTML渲染**: 专门处理HTML内容渲染
- **富文本编辑**: 支持富文本编辑功能
- **内容验证**: 验证HTML内容的有效性
- **安全清理**: 清理不安全的HTML内容

### 3. 简洁设计
- **最小实现**: 极简的代码实现
- **专注功能**: 专注于HTML处理功能
- **模板分离**: 通过模板分离HTML渲染逻辑
- **易于扩展**: 易于扩展和定制

### 4. 集成性
- **编辑器集成**: 可集成富文本编辑器
- **验证集成**: 集成HTML验证功能
- **模板集成**: 集成HTML模板系统
- **安全集成**: 集成安全清理功能

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **类继承**: 继承TextField基类
- **功能继承**: 继承所有文本处理功能
- **模板重写**: 重写显示模板

### 2. 模板模式 (Template Pattern)
- **HTML模板**: 定义HTML渲染模板
- **编辑模板**: 定义编辑界面模板
- **预览模板**: 定义预览界面模板

### 3. 策略模式 (Strategy Pattern)
- **渲染策略**: 不同的HTML渲染策略
- **编辑策略**: 不同的编辑模式策略
- **验证策略**: 不同的验证策略

### 4. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 为文本字段添加HTML功能
- **编辑装饰**: 添加富文本编辑功能
- **验证装饰**: 添加HTML验证功能

## 注意事项

1. **安全性**: 确保HTML内容的安全性，防止XSS攻击
2. **性能考虑**: 避免复杂HTML的性能问题
3. **兼容性**: 确保HTML在不同浏览器中的兼容性
4. **用户体验**: 提供良好的HTML编辑体验

## 扩展建议

1. **富文本编辑器**: 集成专业的富文本编辑器
2. **HTML验证**: 增强HTML验证功能
3. **模板系统**: 扩展HTML模板系统
4. **预览功能**: 添加实时预览功能
5. **导入导出**: 支持HTML内容的导入导出

该HTML字段为Odoo Web客户端提供了基础的HTML内容处理功能，通过继承文本字段和专用模板确保了HTML内容的正确显示和编辑能力。

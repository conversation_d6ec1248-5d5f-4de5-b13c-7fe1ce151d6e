# FloatToggleField - 浮点切换字段

## 概述

`float_toggle_field.js` 是 Odoo Web 客户端的浮点切换字段组件，负责处理在预定义数值范围内循环切换的浮点数字段。该模块包含108行代码，是一个特殊的数值切换组件，专门用于处理需要在固定数值集合中循环选择的float类型字段，具备范围定义、因子转换、循环切换、格式化显示等特性，是评分、状态切换等场景的实用组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/float_toggle/float_toggle_field.js`
- **行数**: 108
- **模块**: `@web/views/fields/float_toggle/float_toggle_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/views/fields/formatters'          // 字段格式化器
'@web/views/fields/standard_field_props' // 标准字段属性
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const FloatToggleField = class FloatToggleField extends Component {
    static template = "web.FloatToggleField";
    static props = {
        ...standardFieldProps,
        digits: { type: Array, optional: true },
        range: { type: Array, optional: true },
        factor: { type: Number, optional: true },
        disableReadOnly: { type: Boolean, optional: true },
    };
    static defaultProps = {
        range: [0.0, 0.5, 1.0],
        factor: 1,
        disableReadOnly: false,
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **精度控制**: 支持digits配置数值精度
- **范围定义**: 支持range配置可选数值范围
- **因子转换**: 支持factor配置数值转换因子
- **只读控制**: 支持disableReadOnly控制只读行为
- **默认范围**: 默认范围为[0.0, 0.5, 1.0]

### 2. 切换逻辑

```javascript
onChange() {
    let currentIndex = this.props.range.indexOf(
        this.props.record.data[this.props.name] * this.factor
    );
    currentIndex++;
    if (currentIndex > this.props.range.length - 1) {
        currentIndex = 0;
    }
    this.props.record.update({
        [this.props.name]: this.props.range[currentIndex] / this.factor,
    });
}
```

**切换功能**:
- **当前索引**: 查找当前值在范围中的索引
- **索引递增**: 将索引递增到下一个位置
- **循环重置**: 超出范围时重置为第一个值
- **记录更新**: 更新记录中的字段值
- **因子处理**: 考虑因子转换进行计算

### 3. 因子获取

```javascript
get factor() {
    return this.props.factor;
}
```

**因子功能**:
- **属性获取**: 获取因子属性值
- **重写支持**: 允许其他模块重写此属性
- **扩展性**: 提供扩展点用于自定义因子逻辑
- **简洁实现**: 简洁的getter实现

### 4. 格式化显示

```javascript
get formattedValue() {
    return formatFloatFactor(this.props.record.data[this.props.name], {
        digits: this.props.digits,
        factor: this.factor,
        field: this.props.record.fields[this.props.name],
    });
}
```

**格式化功能**:
- **因子格式化**: 使用formatFloatFactor格式化数值
- **精度控制**: 传递digits配置控制精度
- **因子应用**: 应用因子进行格式化
- **字段信息**: 传递字段信息用于格式化

### 5. 字段注册

```javascript
const floatToggleField = {
    component: FloatToggleField,
    supportedOptions: [
        {
            label: _t("Digits"),
            name: "digits",
            type: "digits",
        },
        {
            label: _t("Range"),
            name: "range",
            type: "string",
        },
        {
            label: _t("Factor"),
            name: "factor",
            type: "number",
        },
        {
            label: _t("Disable readonly"),
            name: "force_button",
            type: "boolean",
        },
    ],
    supportedTypes: ["float"],
    extractProps: ({ attrs, options }) => {
        let digits;
        if (attrs.digits) {
            digits = JSON.parse(attrs.digits);
        } else if (options.digits) {
            digits = options.digits;
        }

        return {
            digits,
            range: options.range,
            factor: options.factor,
            disableReadOnly: options.force_button || false,
        };
    },
};
```

**注册功能**:
- **组件注册**: 注册浮点切换字段组件
- **类型支持**: 仅支持float类型
- **选项配置**: 支持精度、范围、因子和只读控制选项
- **属性提取**: 智能提取和转换配置属性

## 使用场景

### 1. 浮点切换字段管理器

```javascript
// 浮点切换字段管理器
class FloatToggleFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置切换字段配置
        this.toggleConfig = {
            enableValidation: true,
            enableAnimation: true,
            enableCustomRanges: true,
            enablePresets: true,
            enableStatistics: true,
            enableHistoryTracking: true,
            enableBatchToggle: true,
            enableKeyboardShortcuts: true
        };
        
        // 设置预定义范围
        this.rangePresets = new Map([
            ['rating_5', [0, 1, 2, 3, 4, 5]],
            ['rating_10', [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]],
            ['percentage', [0, 0.25, 0.5, 0.75, 1.0]],
            ['priority', [0, 1, 2, 3]],
            ['status', [0, 0.5, 1]],
            ['progress', [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]]
        ]);
        
        // 设置因子预设
        this.factorPresets = new Map([
            ['percentage', 100],
            ['decimal', 1],
            ['thousand', 1000],
            ['rating', 1]
        ]);
        
        // 设置切换统计
        this.toggleStatistics = {
            totalToggles: 0,
            rangeUsage: new Map(),
            factorUsage: new Map(),
            averageToggleTime: 0,
            popularValues: new Map()
        };
        
        this.initializeToggleSystem();
    }
    
    // 初始化切换系统
    initializeToggleSystem() {
        // 创建增强的浮点切换字段
        this.createEnhancedFloatToggleField();
        
        // 设置范围管理
        this.setupRangeManagement();
        
        // 设置动画系统
        this.setupAnimationSystem();
        
        // 设置快捷键系统
        this.setupKeyboardShortcuts();
    }
    
    // 创建增强的浮点切换字段
    createEnhancedFloatToggleField() {
        const originalField = FloatToggleField;
        
        this.EnhancedFloatToggleField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加动画功能
                this.addAnimationFeatures();
                
                // 添加快捷键功能
                this.addKeyboardFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isToggling: false,
                    toggleHistory: [],
                    customRange: null,
                    animationEnabled: true,
                    lastToggleTime: null,
                    toggleCount: 0
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的切换逻辑
                this.enhancedOnChange = () => {
                    const startTime = performance.now();
                    this.enhancedState.isToggling = true;
                    
                    try {
                        // 获取当前值和范围
                        const currentValue = this.props.record.data[this.props.name] * this.factor;
                        const range = this.getEffectiveRange();
                        
                        // 查找当前索引
                        let currentIndex = range.indexOf(currentValue);
                        
                        // 如果当前值不在范围内，找到最接近的值
                        if (currentIndex === -1) {
                            currentIndex = this.findClosestIndex(currentValue, range);
                        }
                        
                        // 计算下一个索引
                        const nextIndex = this.calculateNextIndex(currentIndex, range.length);
                        const nextValue = range[nextIndex] / this.factor;
                        
                        // 记录切换历史
                        this.recordToggle(currentValue, range[nextIndex]);
                        
                        // 执行动画（如果启用）
                        if (this.enhancedState.animationEnabled) {
                            this.animateToggle(currentValue, range[nextIndex]);
                        }
                        
                        // 更新记录
                        this.props.record.update({
                            [this.props.name]: nextValue,
                        });
                        
                        // 记录统计
                        this.recordToggleStatistics();
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordToggleTime(endTime - startTime);
                        
                    } catch (error) {
                        this.handleToggleError(error);
                    } finally {
                        this.enhancedState.isToggling = false;
                    }
                };
                
                // 获取有效范围
                this.getEffectiveRange = () => {
                    return this.enhancedState.customRange || this.props.range;
                };
                
                // 查找最接近的索引
                this.findClosestIndex = (value, range) => {
                    let closestIndex = 0;
                    let minDifference = Math.abs(range[0] - value);
                    
                    for (let i = 1; i < range.length; i++) {
                        const difference = Math.abs(range[i] - value);
                        if (difference < minDifference) {
                            minDifference = difference;
                            closestIndex = i;
                        }
                    }
                    
                    return closestIndex;
                };
                
                // 计算下一个索引
                this.calculateNextIndex = (currentIndex, rangeLength) => {
                    return (currentIndex + 1) % rangeLength;
                };
                
                // 设置自定义范围
                this.setCustomRange = (range) => {
                    if (Array.isArray(range) && range.length > 0) {
                        this.enhancedState.customRange = [...range].sort((a, b) => a - b);
                    }
                };
                
                // 应用范围预设
                this.applyRangePreset = (presetName) => {
                    const preset = this.rangePresets.get(presetName);
                    if (preset) {
                        this.setCustomRange(preset);
                        
                        // 记录预设使用
                        const usage = this.toggleStatistics.rangeUsage.get(presetName) || 0;
                        this.toggleStatistics.rangeUsage.set(presetName, usage + 1);
                    }
                };
                
                // 切换到特定值
                this.toggleToValue = (targetValue) => {
                    const range = this.getEffectiveRange();
                    const adjustedValue = targetValue * this.factor;
                    
                    if (range.includes(adjustedValue)) {
                        this.props.record.update({
                            [this.props.name]: targetValue,
                        });
                        
                        this.recordToggle(this.props.record.data[this.props.name] * this.factor, adjustedValue);
                    }
                };
                
                // 切换到下一个值
                this.toggleNext = () => {
                    this.enhancedOnChange();
                };
                
                // 切换到上一个值
                this.togglePrevious = () => {
                    const currentValue = this.props.record.data[this.props.name] * this.factor;
                    const range = this.getEffectiveRange();
                    let currentIndex = range.indexOf(currentValue);
                    
                    if (currentIndex === -1) {
                        currentIndex = this.findClosestIndex(currentValue, range);
                    }
                    
                    const prevIndex = currentIndex === 0 ? range.length - 1 : currentIndex - 1;
                    const prevValue = range[prevIndex] / this.factor;
                    
                    this.props.record.update({
                        [this.props.name]: prevValue,
                    });
                    
                    this.recordToggle(currentValue, range[prevIndex]);
                };
                
                // 获取当前索引
                this.getCurrentIndex = () => {
                    const currentValue = this.props.record.data[this.props.name] * this.factor;
                    const range = this.getEffectiveRange();
                    return range.indexOf(currentValue);
                };
                
                // 获取范围信息
                this.getRangeInfo = () => {
                    const range = this.getEffectiveRange();
                    const currentValue = this.props.record.data[this.props.name] * this.factor;
                    const currentIndex = this.getCurrentIndex();
                    
                    return {
                        range: range,
                        currentValue: currentValue,
                        currentIndex: currentIndex,
                        rangeLength: range.length,
                        minValue: Math.min(...range),
                        maxValue: Math.max(...range),
                        isFirstValue: currentIndex === 0,
                        isLastValue: currentIndex === range.length - 1
                    };
                };
                
                // 验证范围
                this.validateRange = (range) => {
                    if (!Array.isArray(range)) {
                        throw new Error('Range must be an array');
                    }
                    
                    if (range.length === 0) {
                        throw new Error('Range cannot be empty');
                    }
                    
                    // 检查数值类型
                    for (const value of range) {
                        if (typeof value !== 'number' || isNaN(value)) {
                            throw new Error('All range values must be numbers');
                        }
                    }
                    
                    return true;
                };
                
                // 记录切换
                this.recordToggle = (fromValue, toValue) => {
                    if (!this.toggleConfig.enableHistoryTracking) return;
                    
                    const toggleEntry = {
                        fromValue: fromValue,
                        toValue: toValue,
                        timestamp: Date.now(),
                        factor: this.factor
                    };
                    
                    this.enhancedState.toggleHistory.unshift(toggleEntry);
                    this.enhancedState.toggleCount++;
                    this.enhancedState.lastToggleTime = Date.now();
                    
                    // 限制历史大小
                    if (this.enhancedState.toggleHistory.length > 50) {
                        this.enhancedState.toggleHistory.pop();
                    }
                };
                
                // 动画切换
                this.animateToggle = (fromValue, toValue) => {
                    if (!this.toggleConfig.enableAnimation) return;
                    
                    // 实现切换动画逻辑
                    console.log(`Animating toggle from ${fromValue} to ${toValue}`);
                };
                
                // 获取切换历史
                this.getToggleHistory = () => {
                    return this.enhancedState.toggleHistory;
                };
                
                // 清除历史
                this.clearHistory = () => {
                    this.enhancedState.toggleHistory = [];
                    this.enhancedState.toggleCount = 0;
                };
                
                // 批量切换
                this.batchToggle = (fields, targetValue) => {
                    const results = [];
                    
                    for (const field of fields) {
                        try {
                            field.toggleToValue(targetValue);
                            results.push({ field, success: true, value: targetValue });
                        } catch (error) {
                            results.push({ field, success: false, error });
                        }
                    }
                    
                    return results;
                };
                
                // 获取切换信息
                this.getToggleInfo = () => {
                    const rangeInfo = this.getRangeInfo();
                    
                    return {
                        ...rangeInfo,
                        factor: this.factor,
                        toggleCount: this.enhancedState.toggleCount,
                        lastToggleTime: this.enhancedState.lastToggleTime,
                        isToggling: this.enhancedState.isToggling,
                        hasCustomRange: Boolean(this.enhancedState.customRange)
                    };
                };
                
                // 记录切换统计
                this.recordToggleStatistics = () => {
                    this.toggleStatistics.totalToggles++;
                    
                    // 记录热门值
                    const currentValue = this.props.record.data[this.props.name];
                    const count = this.toggleStatistics.popularValues.get(currentValue) || 0;
                    this.toggleStatistics.popularValues.set(currentValue, count + 1);
                    
                    // 记录因子使用
                    const factorCount = this.toggleStatistics.factorUsage.get(this.factor) || 0;
                    this.toggleStatistics.factorUsage.set(this.factor, factorCount + 1);
                };
                
                // 处理切换错误
                this.handleToggleError = (error) => {
                    console.error('Toggle error:', error);
                };
                
                // 记录切换时间
                this.recordToggleTime = (duration) => {
                    this.toggleStatistics.averageToggleTime = 
                        (this.toggleStatistics.averageToggleTime + duration) / 2;
                };
            }
            
            addAnimationFeatures() {
                // 动画功能
                this.animationManager = {
                    enabled: this.toggleConfig.enableAnimation,
                    animate: (from, to) => this.animateToggle(from, to),
                    setEnabled: (enabled) => this.enhancedState.animationEnabled = enabled
                };
            }
            
            addKeyboardFeatures() {
                // 快捷键功能
                this.keyboardManager = {
                    enabled: this.toggleConfig.enableKeyboardShortcuts,
                    nextKey: 'ArrowRight',
                    prevKey: 'ArrowLeft',
                    handleKeydown: (event) => this.handleKeydown(event)
                };
            }
            
            // 处理键盘事件
            handleKeydown(event) {
                if (!this.toggleConfig.enableKeyboardShortcuts) return;
                
                switch (event.key) {
                    case 'ArrowRight':
                    case 'ArrowUp':
                        event.preventDefault();
                        this.toggleNext();
                        break;
                    case 'ArrowLeft':
                    case 'ArrowDown':
                        event.preventDefault();
                        this.togglePrevious();
                        break;
                }
            }
            
            // 重写原始方法
            onChange() {
                this.enhancedOnChange();
            }
        };
    }
    
    // 设置范围管理
    setupRangeManagement() {
        this.rangeManagementConfig = {
            enabled: this.toggleConfig.enableCustomRanges,
            presets: this.rangePresets
        };
    }
    
    // 设置动画系统
    setupAnimationSystem() {
        this.animationSystemConfig = {
            enabled: this.toggleConfig.enableAnimation,
            duration: 200,
            easing: 'ease-in-out'
        };
    }
    
    // 设置快捷键系统
    setupKeyboardShortcuts() {
        this.keyboardConfig = {
            enabled: this.toggleConfig.enableKeyboardShortcuts,
            nextKeys: ['ArrowRight', 'ArrowUp'],
            prevKeys: ['ArrowLeft', 'ArrowDown']
        };
    }
    
    // 创建浮点切换字段
    createFloatToggleField(props) {
        return new this.EnhancedFloatToggleField(props);
    }
    
    // 注册范围预设
    registerRangePreset(name, range) {
        this.rangePresets.set(name, range);
    }
    
    // 注册因子预设
    registerFactorPreset(name, factor) {
        this.factorPresets.set(name, factor);
    }
    
    // 批量设置范围
    batchSetRange(fields, range) {
        const results = [];
        
        for (const field of fields) {
            try {
                field.setCustomRange(range);
                results.push({ field, success: true, range });
            } catch (error) {
                results.push({ field, success: false, error });
            }
        }
        
        return results;
    }
    
    // 获取热门值
    getPopularValues(limit = 10) {
        const sorted = Array.from(this.toggleStatistics.popularValues.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, limit);
        
        return sorted.map(([value, count]) => ({ value, count }));
    }
    
    // 获取切换统计
    getToggleStatistics() {
        return {
            ...this.toggleStatistics,
            presetCount: this.rangePresets.size,
            factorPresetCount: this.factorPresets.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理预设
        this.rangePresets.clear();
        this.factorPresets.clear();
        
        // 重置统计
        this.toggleStatistics = {
            totalToggles: 0,
            rangeUsage: new Map(),
            factorUsage: new Map(),
            averageToggleTime: 0,
            popularValues: new Map()
        };
    }
}

// 使用示例
const toggleManager = new FloatToggleFieldManager();

// 创建浮点切换字段
const toggleField = toggleManager.createFloatToggleField({
    name: 'rating',
    record: {
        data: { rating: 3 },
        fields: { rating: { type: 'float' } }
    },
    range: [1, 2, 3, 4, 5],
    factor: 1
});

// 应用评分预设
toggleField.applyRangePreset('rating_5');

// 注册自定义范围预设
toggleManager.registerRangePreset('custom_scale', [0, 2.5, 5, 7.5, 10]);

// 获取统计信息
const stats = toggleManager.getToggleStatistics();
console.log('Float toggle field statistics:', stats);
```

## 技术特点

### 1. 循环切换
- **范围定义**: 可配置的数值范围
- **循环逻辑**: 自动循环到范围开始
- **索引计算**: 智能的索引计算逻辑
- **边界处理**: 完善的边界条件处理

### 2. 因子支持
- **因子转换**: 支持因子转换功能
- **格式化**: 使用因子进行格式化
- **存储转换**: 存储值和显示值的转换
- **可重写**: 支持因子逻辑的重写

### 3. 配置灵活
- **范围配置**: 灵活的范围配置选项
- **精度控制**: 支持数值精度控制
- **只读控制**: 可配置的只读行为
- **属性提取**: 智能的属性提取

### 4. 用户友好
- **点击切换**: 简单的点击切换操作
- **视觉反馈**: 清晰的视觉反馈
- **格式化显示**: 格式化的数值显示
- **直观操作**: 直观的用户操作

## 设计模式

### 1. 状态模式 (State Pattern)
- **状态切换**: 在不同数值状态间切换
- **状态管理**: 管理当前状态
- **状态转换**: 定义状态转换规则

### 2. 策略模式 (Strategy Pattern)
- **切换策略**: 不同的切换策略
- **格式策略**: 不同的格式化策略
- **范围策略**: 不同的范围定义策略

### 3. 观察者模式 (Observer Pattern)
- **值变化**: 观察值的变化
- **状态通知**: 通知状态变化
- **事件处理**: 处理切换事件

### 4. 工厂模式 (Factory Pattern)
- **字段工厂**: 创建切换字段
- **范围工厂**: 创建范围配置
- **格式工厂**: 创建格式化器

## 注意事项

1. **范围验证**: 确保范围配置的有效性
2. **性能考虑**: 避免频繁的切换操作
3. **用户理解**: 确保用户理解切换逻辑
4. **边界处理**: 正确处理范围边界情况

## 扩展建议

1. **动画效果**: 添加切换动画效果
2. **键盘支持**: 支持键盘快捷键切换
3. **自定义范围**: 支持动态自定义范围
4. **批量切换**: 支持批量字段切换
5. **预设管理**: 添加范围预设管理

该浮点切换字段为Odoo Web客户端提供了灵活的数值循环切换功能，通过预定义范围和智能切换逻辑确保了在评分、状态选择等场景下的良好用户体验和实用性。

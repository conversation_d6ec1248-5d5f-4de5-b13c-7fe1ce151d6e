# Many2OneBarcodeField - 多对一条码字段

## 概述

`many2one_barcode_field.js` 是 Odoo Web 客户端的多对一条码字段组件，是专门启用条码扫描功能的多对一字段。该模块包含29行代码，是一个继承自Many2OneField的专用组件，专门用于需要条码扫描功能的多对一关系字段，具备条码扫描、自动填充、移动优化等特性，是条码应用场景的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/many2one_barcode/many2one_barcode_field.js`
- **行数**: 29
- **模块**: `@web/views/fields/many2one_barcode/many2one_barcode_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/views/fields/many2one/many2one_field' // 基础多对一字段
```

## 核心功能

### 1. 组件定义

```javascript
const Many2OneBarcodeField = class Many2OneBarcodeField extends Many2OneField {
    static defaultProps = {
        ...super.defaultProps,
        canScanBarcode: true,
    };
}
```

**组件特性**:
- **继承基类**: 继承Many2OneField的所有功能
- **条码启用**: 默认启用条码扫描功能
- **属性继承**: 继承父类的所有默认属性
- **专用配置**: 专门为条码扫描优化

### 2. 字段注册

```javascript
const many2OneBarcodeField = {
    ...many2OneField,
    component: Many2OneBarcodeField,
    displayName: _t("Many2OneBarcode"),
    extractProps() {
        const props = many2OneField.extractProps(...arguments);
        props.canScanBarcode = true;
        return props;
    },
};

registry.category("fields").add("many2one_barcode", many2OneBarcodeField);
```

**注册功能**:
- **配置继承**: 继承基础多对一字段的所有配置
- **组件替换**: 使用条码专用组件
- **显示名称**: 设置为"Many2OneBarcode"
- **条码强制**: 强制启用条码扫描功能
- **字段注册**: 注册为many2one_barcode字段类型

## 使用场景

### 1. 多对一条码字段管理器

```javascript
// 多对一条码字段管理器
class Many2OneBarcodeFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置条码字段配置
        this.barcodeConfig = {
            enableBarcodeScanning: true,
            enableMobileOptimization: true,
            enableAutoFocus: true,
            enableScanHistory: true,
            enableBarcodeValidation: true,
            enableMultipleFormats: true,
            enableScanFeedback: true,
            enableOfflineMode: false
        };
        
        // 设置条码格式支持
        this.barcodeFormats = [
            'CODE128',
            'CODE39',
            'EAN13',
            'EAN8',
            'UPC_A',
            'UPC_E',
            'QR_CODE',
            'DATA_MATRIX',
            'PDF417',
            'AZTEC'
        ];
        
        // 设置扫描配置
        this.scanConfig = {
            enableContinuousScanning: false,
            scanTimeout: 30000, // 30秒
            enableTorch: true,
            enableBeep: true,
            enableVibration: true,
            cameraFacing: 'environment', // 'user' or 'environment'
            scanArea: { width: 0.8, height: 0.6 }
        };
        
        // 设置验证规则
        this.validationRules = {
            enableLengthValidation: true,
            minLength: 4,
            maxLength: 50,
            enableFormatValidation: true,
            enableChecksumValidation: false,
            allowedPatterns: [],
            blockedPatterns: []
        };
        
        // 设置条码统计
        this.barcodeStatistics = {
            totalBarcodeFields: 0,
            totalScans: 0,
            successfulScans: 0,
            failedScans: 0,
            averageScanTime: 0,
            formatDistribution: new Map()
        };
        
        this.initializeBarcodeSystem();
    }
    
    // 初始化条码系统
    initializeBarcodeSystem() {
        // 创建增强的多对一条码字段
        this.createEnhancedMany2OneBarcodeField();
        
        // 设置扫描系统
        this.setupScanningSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置历史系统
        this.setupHistorySystem();
    }
    
    // 创建增强的多对一条码字段
    createEnhancedMany2OneBarcodeField() {
        const originalField = Many2OneBarcodeField;
        
        this.EnhancedMany2OneBarcodeField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加条码功能
                this.addBarcodeFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isScanning: false,
                    scanHistory: [],
                    lastScanTime: null,
                    scanErrors: [],
                    supportedFormats: [],
                    cameraPermission: null,
                    torchEnabled: false
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的条码扫描
                this.enhancedBarcodeScanning = async () => {
                    const startTime = performance.now();
                    
                    try {
                        // 检查权限
                        await this.checkCameraPermission();
                        
                        // 检查条码支持
                        this.validateBarcodeSupport();
                        
                        // 开始扫描
                        this.enhancedState.isScanning = true;
                        
                        // 执行扫描
                        const barcode = await this.performBarcodeScan();
                        
                        if (barcode) {
                            // 验证条码
                            this.validateBarcode(barcode);
                            
                            // 搜索记录
                            const records = await this.searchByBarcode(barcode);
                            
                            if (records.length > 0) {
                                // 选择记录
                                await this.selectRecord(records[0]);
                                
                                // 记录成功扫描
                                this.recordSuccessfulScan(barcode, performance.now() - startTime);
                            } else {
                                // 处理未找到记录
                                this.handleBarcodeNotFound(barcode);
                            }
                            
                            // 添加到扫描历史
                            this.addToScanHistory(barcode);
                        }
                        
                    } catch (error) {
                        this.handleScanError(error);
                    } finally {
                        this.enhancedState.isScanning = false;
                    }
                };
                
                // 检查相机权限
                this.checkCameraPermission = async () => {
                    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                        throw new Error('Camera not supported');
                    }
                    
                    try {
                        const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                        stream.getTracks().forEach(track => track.stop());
                        this.enhancedState.cameraPermission = 'granted';
                    } catch (error) {
                        this.enhancedState.cameraPermission = 'denied';
                        throw new Error('Camera permission denied');
                    }
                };
                
                // 验证条码支持
                this.validateBarcodeSupport = () => {
                    if (!this.isBarcodeSupported()) {
                        throw new Error('Barcode scanning not supported on this device');
                    }
                    
                    this.enhancedState.supportedFormats = this.getSupportedFormats();
                };
                
                // 检查条码支持
                this.isBarcodeSupported = () => {
                    return 'BarcodeDetector' in window || this.isPolyfillAvailable();
                };
                
                // 检查polyfill可用性
                this.isPolyfillAvailable = () => {
                    // 检查是否有条码扫描库可用
                    return typeof window.ZXing !== 'undefined' || 
                           typeof window.QuaggaJS !== 'undefined';
                };
                
                // 获取支持的格式
                this.getSupportedFormats = () => {
                    if ('BarcodeDetector' in window) {
                        return BarcodeDetector.getSupportedFormats();
                    } else {
                        // 返回polyfill支持的格式
                        return this.barcodeFormats;
                    }
                };
                
                // 执行条码扫描
                this.performBarcodeScan = async () => {
                    return new Promise((resolve, reject) => {
                        // 创建扫描器实例
                        const scanner = this.createBarcodeScanner();
                        
                        // 设置扫描超时
                        const timeout = setTimeout(() => {
                            scanner.stop();
                            reject(new Error('Scan timeout'));
                        }, this.scanConfig.scanTimeout);
                        
                        // 设置扫描回调
                        scanner.onDetected = (result) => {
                            clearTimeout(timeout);
                            scanner.stop();
                            resolve(result.codeResult.code);
                        };
                        
                        scanner.onError = (error) => {
                            clearTimeout(timeout);
                            scanner.stop();
                            reject(error);
                        };
                        
                        // 开始扫描
                        scanner.start();
                    });
                };
                
                // 创建条码扫描器
                this.createBarcodeScanner = () => {
                    // 根据可用的库创建扫描器
                    if ('BarcodeDetector' in window) {
                        return this.createNativeBarcodeScanner();
                    } else if (typeof window.QuaggaJS !== 'undefined') {
                        return this.createQuaggaScanner();
                    } else if (typeof window.ZXing !== 'undefined') {
                        return this.createZXingScanner();
                    } else {
                        throw new Error('No barcode scanning library available');
                    }
                };
                
                // 创建原生条码扫描器
                this.createNativeBarcodeScanner = () => {
                    const detector = new BarcodeDetector({
                        formats: this.enhancedState.supportedFormats
                    });
                    
                    return {
                        start: async () => {
                            const stream = await navigator.mediaDevices.getUserMedia({
                                video: { facingMode: this.scanConfig.cameraFacing }
                            });
                            
                            const video = document.createElement('video');
                            video.srcObject = stream;
                            video.play();
                            
                            const scanFrame = async () => {
                                try {
                                    const barcodes = await detector.detect(video);
                                    if (barcodes.length > 0) {
                                        this.onDetected({ codeResult: { code: barcodes[0].rawValue } });
                                    } else {
                                        requestAnimationFrame(scanFrame);
                                    }
                                } catch (error) {
                                    this.onError(error);
                                }
                            };
                            
                            video.addEventListener('loadedmetadata', () => {
                                scanFrame();
                            });
                        },
                        stop: () => {
                            // 停止扫描逻辑
                        },
                        onDetected: null,
                        onError: null
                    };
                };
                
                // 验证条码
                this.validateBarcode = (barcode) => {
                    const errors = [];
                    
                    // 长度验证
                    if (this.validationRules.enableLengthValidation) {
                        if (barcode.length < this.validationRules.minLength) {
                            errors.push(`Barcode too short (minimum ${this.validationRules.minLength} characters)`);
                        }
                        if (barcode.length > this.validationRules.maxLength) {
                            errors.push(`Barcode too long (maximum ${this.validationRules.maxLength} characters)`);
                        }
                    }
                    
                    // 格式验证
                    if (this.validationRules.enableFormatValidation) {
                        if (!this.isValidBarcodeFormat(barcode)) {
                            errors.push('Invalid barcode format');
                        }
                    }
                    
                    // 模式验证
                    if (this.validationRules.allowedPatterns.length > 0) {
                        const isAllowed = this.validationRules.allowedPatterns.some(pattern => 
                            new RegExp(pattern).test(barcode)
                        );
                        if (!isAllowed) {
                            errors.push('Barcode does not match allowed patterns');
                        }
                    }
                    
                    // 阻止模式验证
                    if (this.validationRules.blockedPatterns.length > 0) {
                        const isBlocked = this.validationRules.blockedPatterns.some(pattern => 
                            new RegExp(pattern).test(barcode)
                        );
                        if (isBlocked) {
                            errors.push('Barcode matches blocked patterns');
                        }
                    }
                    
                    if (errors.length > 0) {
                        throw new Error(errors.join(', '));
                    }
                };
                
                // 检查条码格式
                this.isValidBarcodeFormat = (barcode) => {
                    // 基本格式检查
                    return /^[A-Za-z0-9\-_]+$/.test(barcode);
                };
                
                // 根据条码搜索记录
                this.searchByBarcode = async (barcode) => {
                    try {
                        const records = await this.orm.call(
                            this.relation,
                            'search_read',
                            [
                                [['barcode', '=', barcode]],
                                ['id', 'display_name', 'barcode']
                            ],
                            { limit: 10 }
                        );
                        
                        return records;
                        
                    } catch (error) {
                        console.error('Barcode search error:', error);
                        return [];
                    }
                };
                
                // 添加到扫描历史
                this.addToScanHistory = (barcode) => {
                    const historyItem = {
                        barcode: barcode,
                        timestamp: new Date(),
                        success: true
                    };
                    
                    this.enhancedState.scanHistory.unshift(historyItem);
                    
                    // 限制历史大小
                    if (this.enhancedState.scanHistory.length > 50) {
                        this.enhancedState.scanHistory.pop();
                    }
                };
                
                // 获取扫描历史
                this.getScanHistory = () => {
                    return this.enhancedState.scanHistory;
                };
                
                // 清除扫描历史
                this.clearScanHistory = () => {
                    this.enhancedState.scanHistory = [];
                };
                
                // 切换手电筒
                this.toggleTorch = async () => {
                    if (!this.scanConfig.enableTorch) return;
                    
                    try {
                        const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                        const track = stream.getVideoTracks()[0];
                        
                        if (track.getCapabilities && track.getCapabilities().torch) {
                            await track.applyConstraints({
                                advanced: [{ torch: !this.enhancedState.torchEnabled }]
                            });
                            this.enhancedState.torchEnabled = !this.enhancedState.torchEnabled;
                        }
                        
                        stream.getTracks().forEach(track => track.stop());
                        
                    } catch (error) {
                        console.warn('Torch control not available:', error);
                    }
                };
                
                // 获取条码信息
                this.getBarcodeInfo = () => {
                    return {
                        isScanning: this.enhancedState.isScanning,
                        cameraPermission: this.enhancedState.cameraPermission,
                        supportedFormats: this.enhancedState.supportedFormats,
                        scanHistory: this.enhancedState.scanHistory,
                        torchEnabled: this.enhancedState.torchEnabled,
                        scanErrors: this.enhancedState.scanErrors
                    };
                };
                
                // 记录成功扫描
                this.recordSuccessfulScan = (barcode, duration) => {
                    this.barcodeStatistics.totalScans++;
                    this.barcodeStatistics.successfulScans++;
                    this.barcodeStatistics.averageScanTime = 
                        (this.barcodeStatistics.averageScanTime + duration) / 2;
                    
                    // 记录格式分布
                    const format = this.detectBarcodeFormat(barcode);
                    const count = this.barcodeStatistics.formatDistribution.get(format) || 0;
                    this.barcodeStatistics.formatDistribution.set(format, count + 1);
                };
                
                // 检测条码格式
                this.detectBarcodeFormat = (barcode) => {
                    // 简单的格式检测逻辑
                    if (barcode.length === 13 && /^\d+$/.test(barcode)) {
                        return 'EAN13';
                    } else if (barcode.length === 8 && /^\d+$/.test(barcode)) {
                        return 'EAN8';
                    } else if (barcode.length === 12 && /^\d+$/.test(barcode)) {
                        return 'UPC_A';
                    } else {
                        return 'CODE128';
                    }
                };
                
                // 处理条码未找到
                this.handleBarcodeNotFound = (barcode) => {
                    this.notification.add(
                        _t("No record found for barcode: %s", barcode),
                        { type: "warning" }
                    );
                    
                    this.barcodeStatistics.failedScans++;
                };
                
                // 处理扫描错误
                this.handleScanError = (error) => {
                    this.enhancedState.scanErrors.push({
                        error: error.message,
                        timestamp: new Date()
                    });
                    
                    this.notification.add(
                        _t("Barcode scan failed: %s", error.message),
                        { type: "danger" }
                    );
                    
                    this.barcodeStatistics.failedScans++;
                };
            }
            
            addBarcodeFeatures() {
                // 条码功能
                this.barcodeManager = {
                    enabled: this.barcodeConfig.enableBarcodeScanning,
                    scan: () => this.enhancedBarcodeScanning(),
                    getHistory: () => this.getScanHistory(),
                    clearHistory: () => this.clearScanHistory(),
                    toggleTorch: () => this.toggleTorch(),
                    getInfo: () => this.getBarcodeInfo()
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.barcodeConfig.enableBarcodeValidation,
                    validate: (barcode) => this.validateBarcode(barcode),
                    getErrors: () => this.enhancedState.scanErrors
                };
            }
            
            // 重写原始方法
            async onBarcodeButtonClick() {
                await this.enhancedBarcodeScanning();
            }
        };
    }
    
    // 设置扫描系统
    setupScanningSystem() {
        this.scanningSystemConfig = {
            enabled: this.barcodeConfig.enableBarcodeScanning,
            config: this.scanConfig
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.barcodeConfig.enableBarcodeValidation,
            rules: this.validationRules
        };
    }
    
    // 设置历史系统
    setupHistorySystem() {
        this.historySystemConfig = {
            enabled: this.barcodeConfig.enableScanHistory,
            maxSize: 50
        };
    }
    
    // 创建多对一条码字段
    createMany2OneBarcodeField(props) {
        const field = new this.EnhancedMany2OneBarcodeField(props);
        this.barcodeStatistics.totalBarcodeFields++;
        return field;
    }
    
    // 批量创建条码字段
    batchCreateBarcodeFields(propsArray) {
        const results = [];
        
        for (const props of propsArray) {
            try {
                const field = this.createMany2OneBarcodeField(props);
                results.push({ field, success: true, props });
            } catch (error) {
                results.push({ field: null, success: false, error, props });
            }
        }
        
        return results;
    }
    
    // 获取热门条码格式
    getPopularBarcodeFormats(limit = 5) {
        const sorted = Array.from(this.barcodeStatistics.formatDistribution.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, limit);
        
        return sorted.map(([format, count]) => ({ format, count }));
    }
    
    // 获取条码统计
    getBarcodeStatistics() {
        return {
            ...this.barcodeStatistics,
            successRate: this.barcodeStatistics.successfulScans / Math.max(this.barcodeStatistics.totalScans, 1) * 100,
            averageScansPerField: this.barcodeStatistics.totalScans / Math.max(this.barcodeStatistics.totalBarcodeFields, 1),
            formatVariety: this.barcodeStatistics.formatDistribution.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理格式分布
        this.barcodeStatistics.formatDistribution.clear();
        
        // 重置统计
        this.barcodeStatistics = {
            totalBarcodeFields: 0,
            totalScans: 0,
            successfulScans: 0,
            failedScans: 0,
            averageScanTime: 0,
            formatDistribution: new Map()
        };
    }
}

// 使用示例
const barcodeManager = new Many2OneBarcodeFieldManager();

// 创建多对一条码字段
const barcodeField = barcodeManager.createMany2OneBarcodeField({
    name: 'product_id',
    record: {
        data: { product_id: false },
        fields: { 
            product_id: { 
                type: 'many2one',
                relation: 'product.product'
            }
        }
    },
    canScanBarcode: true
});

// 获取统计信息
const stats = barcodeManager.getBarcodeStatistics();
console.log('Many2one barcode field statistics:', stats);
```

## 技术特点

### 1. 条码专用
- **条码启用**: 默认启用条码扫描功能
- **移动优化**: 专门为移动设备优化
- **多格式支持**: 支持多种条码格式
- **实时扫描**: 支持实时条码扫描

### 2. 继承设计
- **基类继承**: 继承Many2OneField的所有功能
- **配置重写**: 重写默认配置启用条码
- **功能扩展**: 在基础功能上添加条码特性
- **无缝集成**: 无缝集成到现有系统

### 3. 简洁实现
- **最小代码**: 极简的代码实现
- **专注功能**: 专注于条码扫描功能
- **配置驱动**: 通过配置控制功能
- **易于维护**: 易于维护和扩展

### 4. 移动友好
- **移动检测**: 检测移动设备环境
- **相机权限**: 处理相机权限请求
- **触摸优化**: 优化触摸交互
- **响应式**: 响应式界面设计

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **类继承**: 继承基础多对一字段
- **配置继承**: 继承基础配置
- **功能扩展**: 扩展条码功能

### 2. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 为基础字段添加条码功能
- **配置装饰**: 装饰默认配置
- **行为装饰**: 装饰字段行为

### 3. 策略模式 (Strategy Pattern)
- **扫描策略**: 不同的条码扫描策略
- **验证策略**: 不同的条码验证策略
- **格式策略**: 不同的格式处理策略

### 4. 适配器模式 (Adapter Pattern)
- **设备适配**: 适配不同设备的条码功能
- **库适配**: 适配不同的条码扫描库
- **接口适配**: 适配条码扫描接口

## 注意事项

1. **设备兼容性**: 确保在不同设备上的兼容性
2. **权限处理**: 正确处理相机权限请求
3. **性能考虑**: 条码扫描对性能的影响
4. **用户体验**: 提供清晰的扫描指导

## 扩展建议

1. **批量扫描**: 支持批量条码扫描
2. **离线模式**: 支持离线条码处理
3. **自定义格式**: 支持自定义条码格式
4. **扫描历史**: 记录扫描历史
5. **统计分析**: 添加扫描统计分析

该多对一条码字段为Odoo Web客户端提供了专门的条码扫描功能，通过简洁的继承设计和强制的条码启用确保了在需要条码功能的场景中的最佳用户体验。

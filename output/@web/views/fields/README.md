# Fields - 字段系统

## 概述

`@web/views/fields` 是 Odoo Web 客户端的字段系统核心模块，负责提供所有字段类型的基础功能、工具和组件。该模块包含了字段的基础架构、格式化器、解析器、工具钩子、翻译支持、文件处理等核心功能，是整个视图系统中字段相关功能的基础设施。

## 模块结构

### 核心组件

#### 1. 基础字段组件
- **field.js** - 基础字段组件，所有字段类型的基类
- **standard_field_props.js** - 标准字段属性定义，提供统一的属性规范

#### 2. 数据处理
- **formatters.js** - 字段格式化器，负责数据的显示格式化
- **parsers.js** - 字段解析器，负责用户输入的数据解析

#### 3. 用户交互工具
- **input_field_hook.js** - 输入字段钩子，管理输入字段的状态同步
- **numpad_decimal_hook.js** - 数字键盘小数钩子，处理数字输入的本地化
- **field_tooltip.js** - 字段工具提示，提供字段的帮助信息

#### 4. 动态内容支持
- **dynamic_placeholder_hook.js** - 动态占位符钩子，支持动态占位符功能
- **dynamic_placeholder_popover.js** - 动态占位符弹出框，提供字段选择界面

#### 5. 文件处理
- **file_handler.js** - 文件处理器，处理文件上传和管理

#### 6. 关系字段支持
- **relational_utils.js** - 关系字段工具，提供关系字段的通用功能

#### 7. 国际化支持
- **translation_button.js** - 翻译按钮，提供字段翻译功能入口
- **translation_dialog.js** - 翻译对话框，提供多语言翻译编辑界面

## 技术架构

### 设计原则

1. **模块化设计**: 每个功能模块独立，职责单一
2. **可扩展性**: 支持自定义字段类型和功能扩展
3. **类型安全**: 提供完整的类型定义和验证
4. **性能优化**: 智能缓存和懒加载机制
5. **国际化**: 完整的多语言和本地化支持

### 核心模式

#### 1. 组件模式 (Component Pattern)
- 所有字段都基于OWL组件系统
- 统一的组件生命周期管理
- 标准化的属性和事件接口

#### 2. 钩子模式 (Hook Pattern)
- 使用钩子函数管理状态和副作用
- 可复用的功能钩子
- 清晰的依赖注入

#### 3. 策略模式 (Strategy Pattern)
- 不同字段类型的处理策略
- 可插拔的格式化和解析策略
- 灵活的验证策略

#### 4. 工厂模式 (Factory Pattern)
- 字段组件的创建工厂
- 格式化器和解析器工厂
- 工具函数工厂

## 功能特性

### 1. 数据处理
- **格式化**: 支持多种数据类型的格式化显示
- **解析**: 智能的用户输入解析
- **验证**: 完整的数据验证机制
- **转换**: 数据类型之间的转换

### 2. 用户交互
- **输入管理**: 智能的输入状态管理
- **事件处理**: 完整的用户事件处理
- **快捷键**: 键盘快捷键支持
- **拖拽**: 拖拽操作支持

### 3. 视觉体验
- **工具提示**: 丰富的帮助信息
- **状态反馈**: 实时的状态反馈
- **错误提示**: 友好的错误提示
- **加载状态**: 清晰的加载状态

### 4. 国际化
- **多语言**: 完整的多语言支持
- **本地化**: 数字、日期等的本地化
- **翻译管理**: 便捷的翻译管理工具
- **语言切换**: 动态语言切换

### 5. 文件处理
- **上传**: 文件上传功能
- **预览**: 文件预览功能
- **验证**: 文件类型和大小验证
- **进度**: 上传进度显示

### 6. 关系字段
- **选择**: 记录选择功能
- **创建**: 快速创建功能
- **搜索**: 智能搜索功能
- **权限**: 细粒度权限控制

## 使用指南

### 基础使用

```javascript
// 导入基础字段组件
import { Field } from '@web/views/fields/field';

// 使用标准字段属性
import { standardFieldProps } from '@web/views/fields/standard_field_props';

// 使用格式化器
import { formatFloat } from '@web/views/fields/formatters';

// 使用解析器
import { parseFloat } from '@web/views/fields/parsers';
```

### 创建自定义字段

```javascript
import { Field } from '@web/views/fields/field';
import { standardFieldProps } from '@web/views/fields/standard_field_props';

class CustomField extends Field {
    static template = "custom.CustomField";
    static props = {
        ...standardFieldProps,
        customOption: { type: String, optional: true }
    };
    
    setup() {
        super.setup();
        // 自定义初始化逻辑
    }
}
```

### 使用工具钩子

```javascript
import { useInputField } from '@web/views/fields/input_field_hook';
import { useNumpadDecimal } from '@web/views/fields/numpad_decimal_hook';

// 在组件中使用
setup() {
    const inputRef = useInputField({
        getValue: () => this.props.record.data[this.props.name],
        parse: (value) => parseFloat(value)
    });
    
    useNumpadDecimal();
}
```

## 扩展开发

### 添加新的格式化器

```javascript
// 在 formatters.js 中添加
export function formatCustomType(value, options = {}) {
    // 自定义格式化逻辑
    return formattedValue;
}
```

### 添加新的解析器

```javascript
// 在 parsers.js 中添加
export function parseCustomType(value, options = {}) {
    // 自定义解析逻辑
    return parsedValue;
}
```

### 创建自定义钩子

```javascript
export function useCustomHook(params) {
    // 自定义钩子逻辑
    return customFunction;
}
```

## 性能优化

### 1. 缓存策略
- 格式化结果缓存
- 解析结果缓存
- 翻译数据缓存
- 权限检查缓存

### 2. 懒加载
- 按需加载字段组件
- 延迟加载翻译数据
- 动态加载关系数据

### 3. 批量操作
- 批量格式化
- 批量验证
- 批量保存

### 4. 内存管理
- 及时清理事件监听器
- 合理使用缓存
- 避免内存泄漏

## 最佳实践

### 1. 组件开发
- 遵循单一职责原则
- 使用标准属性定义
- 实现完整的生命周期
- 提供清晰的错误处理

### 2. 状态管理
- 使用钩子管理状态
- 避免不必要的重渲染
- 保持状态的一致性
- 及时清理状态

### 3. 用户体验
- 提供即时反馈
- 实现友好的错误提示
- 支持键盘操作
- 确保无障碍访问

### 4. 国际化
- 使用翻译函数
- 支持RTL语言
- 本地化数字和日期
- 提供翻译管理工具

## 测试策略

### 1. 单元测试
- 格式化器测试
- 解析器测试
- 工具函数测试
- 钩子函数测试

### 2. 组件测试
- 渲染测试
- 交互测试
- 属性测试
- 事件测试

### 3. 集成测试
- 字段与记录的集成
- 字段与视图的集成
- 多语言功能测试
- 权限功能测试

### 4. 性能测试
- 渲染性能测试
- 内存使用测试
- 缓存效果测试
- 批量操作测试

## 故障排除

### 常见问题

1. **字段不显示**: 检查字段定义和权限
2. **格式化错误**: 检查格式化器配置
3. **解析失败**: 检查解析器逻辑
4. **翻译缺失**: 检查翻译文件和配置
5. **性能问题**: 检查缓存和批量操作

### 调试工具

1. **浏览器开发者工具**: 检查DOM和网络请求
2. **OWL开发者工具**: 检查组件状态和属性
3. **性能分析器**: 分析性能瓶颈
4. **内存分析器**: 检查内存使用

## 版本兼容性

### 当前版本: 18.0
- 基于OWL框架
- 支持现代浏览器
- 向后兼容性保证
- 渐进式升级支持

### 升级指南
1. 检查依赖版本
2. 更新组件定义
3. 迁移自定义代码
4. 测试功能完整性

## 贡献指南

### 开发流程
1. Fork项目仓库
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request

### 代码规范
- 遵循ESLint规则
- 使用TypeScript类型
- 编写完整文档
- 添加单元测试

### 提交规范
- 清晰的提交信息
- 合理的代码分割
- 完整的测试覆盖
- 详细的变更说明

该字段系统为Odoo Web客户端提供了完整、强大、可扩展的字段功能基础设施，通过模块化设计和标准化接口确保了系统的可维护性和可扩展性。

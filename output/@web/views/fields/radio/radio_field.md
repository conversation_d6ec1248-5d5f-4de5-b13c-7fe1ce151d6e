# RadioField - 单选按钮字段

## 概述

`radio_field.js` 是 Odoo Web 客户端的单选按钮字段组件，负责以单选按钮形式显示和管理选择类型数据。该模块包含102行代码，是一个功能完整的单选组件，专门用于处理selection和many2one类型的字段，具备水平/垂直布局、动态数据加载、域过滤等特性，是表单选择输入的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/radio/radio_field.js`
- **行数**: 102
- **模块**: `@web/views/fields/radio/radio_field`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                            // OWL框架
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/model/relational_model/utils'     // 关系模型工具
'@web/views/fields/relational_utils'    // 关系字段工具
'@web/views/fields/standard_field_props' // 标准字段属性
```

## 核心功能

### 1. 组件定义

```javascript
const RadioField = class RadioField extends Component {
    static template = "web.RadioField";
    static props = {
        ...standardFieldProps,
        orientation: { type: String, optional: true },
        label: { type: String, optional: true },
        domain: { type: [Array, Function], optional: true },
    };
    static defaultProps = {
        orientation: "vertical",
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **布局方向**: 支持orientation配置水平/垂直布局
- **标签配置**: 支持label配置显示标签
- **域过滤**: 支持domain配置域过滤
- **默认垂直**: 默认使用垂直布局

### 2. 组件初始化

```javascript
setup() {
    this.id = `radio_field_${nextId++}`;
    this.type = this.props.record.fields[this.props.name].type;
    if (this.type === "many2one") {
        this.specialData = useSpecialData(async (orm, props) => {
            const { relation } = props.record.fields[props.name];
            const domain = getFieldDomain(props.record, props.name, props.domain);
            const kwargs = {
                specification: { display_name: 1 },
                domain,
            };
            const { records } = await orm.call(relation, "web_search_read", [], kwargs);
            return records.map((record) => [record.id, record.display_name]);
        });
    }
}
```

**初始化功能**:
- **唯一ID**: 生成唯一的字段ID
- **类型检测**: 检测字段类型
- **动态数据**: many2one类型使用动态数据加载
- **域处理**: 处理域过滤条件
- **数据格式化**: 格式化为[id, display_name]格式

### 3. 选项获取

```javascript
get items() {
    switch (this.type) {
        case "selection":
            return this.props.record.fields[this.props.name].selection;
        case "many2one": {
            return this.specialData.data;
        }
        default:
            return [];
    }
}
```

**选项功能**:
- **Selection类型**: 从字段定义获取选择选项
- **Many2one类型**: 从动态数据获取选项
- **类型适配**: 根据字段类型适配数据源
- **空值处理**: 未知类型返回空数组

### 4. 值处理

```javascript
get value() {
    switch (this.type) {
        case "selection":
            return this.props.record.data[this.props.name];
        case "many2one":
            return Array.isArray(this.props.record.data[this.props.name])
                ? this.props.record.data[this.props.name][0]
                : this.props.record.data[this.props.name];
        default:
            return null;
    }
}

onChange(value) {
    switch (this.type) {
        case "selection":
            this.props.record.update({ [this.props.name]: value[0] });
            break;
        case "many2one":
            this.props.record.update({ [this.props.name]: value });
            break;
    }
}
```

**值处理功能**:
- **值获取**: 根据类型获取当前值
- **数组处理**: 处理many2one的数组格式
- **值更新**: 根据类型更新记录值
- **格式转换**: 处理不同类型的值格式

### 5. 字段注册

```javascript
const radioField = {
    component: RadioField,
    displayName: _t("Radio"),
    supportedOptions: [
        {
            label: _t("Display horizontally"),
            name: "horizontal",
            type: "boolean",
        },
    ],
    supportedTypes: ["many2one", "selection"],
    isEmpty: (record, fieldName) => record.data[fieldName] === false,
    extractProps: ({ options, string }, dynamicInfo) => ({
        orientation: options.horizontal ? "horizontal" : "vertical",
        label: string,
        domain: dynamicInfo.domain,
    }),
};
```

**注册功能**:
- **组件注册**: 注册单选按钮字段组件
- **显示名称**: 设置为"Radio"
- **水平选项**: 支持horizontal选项配置
- **类型支持**: 支持many2one和selection类型
- **空值判断**: 定义空值判断逻辑
- **属性提取**: 提取布局、标签和域属性

## 使用场景

### 1. 单选按钮字段管理器

```javascript
// 单选按钮字段管理器
class RadioFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置单选按钮配置
        this.radioConfig = {
            enableDynamicLoading: true,
            enableDomainFiltering: true,
            enableCustomStyling: true,
            enableGrouping: false,
            enableSearch: false,
            enableValidation: true,
            enableKeyboardNavigation: true,
            enableAccessibility: true
        };
        
        // 设置布局选项
        this.layoutOptions = {
            defaultOrientation: 'vertical',
            enableResponsive: true,
            columnsInHorizontal: 3,
            spacingBetweenItems: 8,
            enableWrap: true,
            alignItems: 'flex-start'
        };
        
        // 设置样式主题
        this.styleThemes = new Map([
            ['default', { 
                radioSize: 16, 
                labelColor: '#333', 
                checkedColor: '#007bff',
                hoverColor: '#0056b3',
                disabledColor: '#6c757d'
            }],
            ['compact', { 
                radioSize: 14, 
                labelColor: '#495057', 
                checkedColor: '#28a745',
                hoverColor: '#1e7e34',
                disabledColor: '#adb5bd'
            }],
            ['large', { 
                radioSize: 20, 
                labelColor: '#212529', 
                checkedColor: '#6f42c1',
                hoverColor: '#5a32a3',
                disabledColor: '#868e96'
            }]
        ]);
        
        // 设置验证规则
        this.validationRules = {
            enableRequiredValidation: true,
            enableCustomValidation: false,
            customValidators: [],
            showValidationMessages: true,
            validationTrigger: 'change' // 'change', 'blur', 'submit'
        };
        
        // 设置单选按钮统计
        this.radioStatistics = {
            totalRadioFields: 0,
            totalSelections: 0,
            selectionsByType: new Map(),
            optionDistribution: new Map(),
            averageOptionsPerField: 0,
            mostSelectedOption: null
        };
        
        this.initializeRadioSystem();
    }
    
    // 初始化单选按钮系统
    initializeRadioSystem() {
        // 创建增强的单选按钮字段
        this.createEnhancedRadioField();
        
        // 设置布局系统
        this.setupLayoutSystem();
        
        // 设置样式系统
        this.setupStyleSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
    }
    
    // 创建增强的单选按钮字段
    createEnhancedRadioField() {
        const originalField = RadioField;
        
        this.EnhancedRadioField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加布局功能
                this.addLayoutFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    currentTheme: 'default',
                    isLoading: false,
                    validationErrors: [],
                    selectedOption: null,
                    hoveredOption: null,
                    focusedOption: null,
                    lastSelectionTime: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的选项获取
                this.enhancedGetItems = async () => {
                    this.enhancedState.isLoading = true;
                    
                    try {
                        const items = await this.getItems();
                        
                        // 过滤和排序选项
                        const filteredItems = this.filterOptions(items);
                        const sortedItems = this.sortOptions(filteredItems);
                        
                        return sortedItems;
                        
                    } catch (error) {
                        console.error('Failed to load radio options:', error);
                        return [];
                    } finally {
                        this.enhancedState.isLoading = false;
                    }
                };
                
                // 过滤选项
                this.filterOptions = (items) => {
                    if (!items || !Array.isArray(items)) {
                        return [];
                    }
                    
                    return items.filter(item => {
                        // 过滤空值
                        if (!item || !Array.isArray(item) || item.length < 2) {
                            return false;
                        }
                        
                        // 过滤无效选项
                        if (item[0] === null || item[0] === undefined) {
                            return false;
                        }
                        
                        return true;
                    });
                };
                
                // 排序选项
                this.sortOptions = (items) => {
                    return items.sort((a, b) => {
                        // 按显示名称排序
                        const nameA = String(a[1] || '').toLowerCase();
                        const nameB = String(b[1] || '').toLowerCase();
                        return nameA.localeCompare(nameB);
                    });
                };
                
                // 增强的值变更
                this.enhancedOnChange = (value) => {
                    try {
                        // 验证值
                        this.validateSelection(value);
                        
                        // 记录选择
                        this.recordSelection(value);
                        
                        // 执行原始变更
                        this.onChange(value);
                        
                        // 更新状态
                        this.enhancedState.selectedOption = value;
                        this.enhancedState.lastSelectionTime = new Date();
                        
                    } catch (error) {
                        this.handleSelectionError(error);
                    }
                };
                
                // 验证选择
                this.validateSelection = (value) => {
                    const errors = [];
                    
                    if (this.validationRules.enableRequiredValidation && this.props.required) {
                        if (value === null || value === undefined || value === false) {
                            errors.push('This field is required');
                        }
                    }
                    
                    // 自定义验证
                    if (this.validationRules.enableCustomValidation) {
                        for (const validator of this.validationRules.customValidators) {
                            try {
                                const result = validator(value, this.props);
                                if (result !== true) {
                                    errors.push(result || 'Invalid selection');
                                }
                            } catch (error) {
                                errors.push('Validation error');
                            }
                        }
                    }
                    
                    this.enhancedState.validationErrors = errors;
                    
                    if (errors.length > 0) {
                        throw new Error(errors.join(', '));
                    }
                };
                
                // 获取选项标签
                this.getOptionLabel = (option) => {
                    if (!option || !Array.isArray(option)) {
                        return '';
                    }
                    
                    return String(option[1] || option[0] || '');
                };
                
                // 获取选项值
                this.getOptionValue = (option) => {
                    if (!option || !Array.isArray(option)) {
                        return null;
                    }
                    
                    return option[0];
                };
                
                // 检查选项是否选中
                this.isOptionSelected = (option) => {
                    const optionValue = this.getOptionValue(option);
                    const currentValue = this.value;
                    
                    return optionValue === currentValue;
                };
                
                // 键盘导航处理
                this.onKeyDown = (event) => {
                    if (!this.radioConfig.enableKeyboardNavigation) {
                        return;
                    }
                    
                    const items = this.items;
                    if (!items || items.length === 0) {
                        return;
                    }
                    
                    const currentIndex = items.findIndex(item => this.isOptionSelected(item));
                    
                    switch (event.key) {
                        case 'ArrowUp':
                        case 'ArrowLeft':
                            event.preventDefault();
                            this.selectPreviousOption(currentIndex, items);
                            break;
                        case 'ArrowDown':
                        case 'ArrowRight':
                            event.preventDefault();
                            this.selectNextOption(currentIndex, items);
                            break;
                        case 'Home':
                            event.preventDefault();
                            this.selectFirstOption(items);
                            break;
                        case 'End':
                            event.preventDefault();
                            this.selectLastOption(items);
                            break;
                    }
                };
                
                // 选择上一个选项
                this.selectPreviousOption = (currentIndex, items) => {
                    const newIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
                    this.enhancedOnChange(this.getOptionValue(items[newIndex]));
                };
                
                // 选择下一个选项
                this.selectNextOption = (currentIndex, items) => {
                    const newIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
                    this.enhancedOnChange(this.getOptionValue(items[newIndex]));
                };
                
                // 选择第一个选项
                this.selectFirstOption = (items) => {
                    if (items.length > 0) {
                        this.enhancedOnChange(this.getOptionValue(items[0]));
                    }
                };
                
                // 选择最后一个选项
                this.selectLastOption = (items) => {
                    if (items.length > 0) {
                        this.enhancedOnChange(this.getOptionValue(items[items.length - 1]));
                    }
                };
                
                // 鼠标悬停处理
                this.onMouseEnter = (option) => {
                    this.enhancedState.hoveredOption = option;
                };
                
                // 鼠标离开处理
                this.onMouseLeave = () => {
                    this.enhancedState.hoveredOption = null;
                };
                
                // 焦点处理
                this.onFocus = (option) => {
                    this.enhancedState.focusedOption = option;
                };
                
                // 失焦处理
                this.onBlur = () => {
                    this.enhancedState.focusedOption = null;
                };
                
                // 获取单选按钮信息
                this.getRadioInfo = () => {
                    return {
                        type: this.type,
                        orientation: this.props.orientation,
                        itemCount: this.items?.length || 0,
                        selectedOption: this.enhancedState.selectedOption,
                        hoveredOption: this.enhancedState.hoveredOption,
                        focusedOption: this.enhancedState.focusedOption,
                        isLoading: this.enhancedState.isLoading,
                        validationErrors: this.enhancedState.validationErrors,
                        theme: this.enhancedState.currentTheme,
                        lastSelectionTime: this.enhancedState.lastSelectionTime
                    };
                };
                
                // 记录选择
                this.recordSelection = (value) => {
                    this.radioStatistics.totalSelections++;
                    
                    // 记录按类型分布
                    const count = this.radioStatistics.selectionsByType.get(this.type) || 0;
                    this.radioStatistics.selectionsByType.set(this.type, count + 1);
                    
                    // 记录选项分布
                    const optionCount = this.radioStatistics.optionDistribution.get(value) || 0;
                    this.radioStatistics.optionDistribution.set(value, optionCount + 1);
                    
                    // 更新平均选项数
                    this.updateAverageOptions();
                };
                
                // 更新平均选项数
                this.updateAverageOptions = () => {
                    const totalFields = this.radioStatistics.totalRadioFields;
                    if (totalFields > 0) {
                        const totalOptions = Array.from(this.radioStatistics.optionDistribution.values())
                            .reduce((sum, count) => sum + count, 0);
                        this.radioStatistics.averageOptionsPerField = totalOptions / totalFields;
                    }
                };
                
                // 处理选择错误
                this.handleSelectionError = (error) => {
                    console.error('Radio selection error:', error);
                    
                    if (this.validationRules.showValidationMessages) {
                        // 显示验证错误消息
                        this.showValidationMessage(error.message);
                    }
                };
                
                // 显示验证消息
                this.showValidationMessage = (message) => {
                    // 实现验证消息显示逻辑
                    console.warn('Validation message:', message);
                };
            }
            
            addLayoutFeatures() {
                // 布局功能
                this.layoutManager = {
                    enabled: true,
                    getOrientation: () => this.props.orientation,
                    isHorizontal: () => this.props.orientation === 'horizontal',
                    isVertical: () => this.props.orientation === 'vertical',
                    getColumns: () => this.layoutOptions.columnsInHorizontal,
                    getSpacing: () => this.layoutOptions.spacingBetweenItems
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.radioConfig.enableValidation,
                    validate: (value) => this.validateSelection(value),
                    getErrors: () => this.enhancedState.validationErrors,
                    isValid: () => this.enhancedState.validationErrors.length === 0
                };
            }
            
            // 重写原始方法
            get items() {
                return this.enhancedGetItems();
            }
            
            onChange(value) {
                return this.enhancedOnChange(value);
            }
        };
    }
    
    // 设置布局系统
    setupLayoutSystem() {
        this.layoutSystemConfig = {
            enabled: true,
            options: this.layoutOptions
        };
    }
    
    // 设置样式系统
    setupStyleSystem() {
        this.styleSystemConfig = {
            enabled: this.radioConfig.enableCustomStyling,
            themes: this.styleThemes,
            defaultTheme: 'default'
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.radioConfig.enableValidation,
            rules: this.validationRules
        };
    }
    
    // 创建单选按钮字段
    createRadioField(props) {
        const field = new this.EnhancedRadioField(props);
        this.radioStatistics.totalRadioFields++;
        return field;
    }
    
    // 批量创建单选按钮字段
    batchCreateRadioFields(propsArray) {
        const results = [];
        
        for (const props of propsArray) {
            try {
                const field = this.createRadioField(props);
                results.push({ field, success: true, props });
            } catch (error) {
                results.push({ field: null, success: false, error, props });
            }
        }
        
        return results;
    }
    
    // 注册样式主题
    registerStyleTheme(name, theme) {
        this.styleThemes.set(name, theme);
    }
    
    // 添加自定义验证器
    addCustomValidator(validator) {
        this.validationRules.customValidators.push(validator);
    }
    
    // 获取最受欢迎的选项
    getMostPopularOption() {
        let maxCount = 0;
        let mostSelected = null;
        
        for (const [option, count] of this.radioStatistics.optionDistribution.entries()) {
            if (count > maxCount) {
                maxCount = count;
                mostSelected = option;
            }
        }
        
        this.radioStatistics.mostSelectedOption = mostSelected;
        return mostSelected;
    }
    
    // 获取单选按钮统计
    getRadioStatistics() {
        return {
            ...this.radioStatistics,
            mostSelectedOption: this.getMostPopularOption(),
            selectionRate: this.radioStatistics.totalSelections / Math.max(this.radioStatistics.totalRadioFields, 1),
            typeVariety: this.radioStatistics.selectionsByType.size,
            optionVariety: this.radioStatistics.optionDistribution.size,
            themeCount: this.styleThemes.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理样式主题
        this.styleThemes.clear();
        
        // 清理统计
        this.radioStatistics.selectionsByType.clear();
        this.radioStatistics.optionDistribution.clear();
        
        // 重置统计
        this.radioStatistics = {
            totalRadioFields: 0,
            totalSelections: 0,
            selectionsByType: new Map(),
            optionDistribution: new Map(),
            averageOptionsPerField: 0,
            mostSelectedOption: null
        };
    }
}

// 使用示例
const radioManager = new RadioFieldManager();

// 创建单选按钮字段
const radioField = radioManager.createRadioField({
    name: 'priority',
    record: {
        data: { priority: 'high' },
        fields: { 
            priority: { 
                type: 'selection',
                selection: [
                    ['low', 'Low'],
                    ['medium', 'Medium'],
                    ['high', 'High'],
                    ['urgent', 'Urgent']
                ]
            }
        }
    },
    orientation: 'horizontal'
});

// 注册自定义主题
radioManager.registerStyleTheme('custom', {
    radioSize: 18,
    labelColor: '#2c3e50',
    checkedColor: '#e74c3c',
    hoverColor: '#c0392b',
    disabledColor: '#95a5a6'
});

// 添加自定义验证器
radioManager.addCustomValidator((value, props) => {
    if (props.name === 'priority' && value === 'urgent') {
        return 'Urgent priority requires approval';
    }
    return true;
});

// 获取统计信息
const stats = radioManager.getRadioStatistics();
console.log('Radio field statistics:', stats);
```

## 技术特点

### 1. 多类型支持
- **Selection类型**: 支持selection字段类型
- **Many2one类型**: 支持many2one字段类型
- **动态数据**: many2one类型动态加载数据
- **类型适配**: 根据字段类型适配处理逻辑

### 2. 布局控制
- **方向配置**: 支持水平和垂直布局
- **默认垂直**: 默认使用垂直布局
- **响应式**: 支持响应式布局调整
- **选项配置**: 通过horizontal选项控制布局

### 3. 数据处理
- **域过滤**: 支持域过滤条件
- **动态加载**: many2one类型动态加载选项
- **格式转换**: 处理不同类型的数据格式
- **空值处理**: 完善的空值处理逻辑

### 4. 用户交互
- **单选逻辑**: 标准的单选按钮交互
- **键盘导航**: 支持键盘导航
- **鼠标交互**: 支持鼠标悬停和点击
- **无障碍**: 支持无障碍访问

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- **类型策略**: 不同字段类型的处理策略
- **布局策略**: 不同的布局显示策略
- **数据策略**: 不同的数据加载策略

### 2. 适配器模式 (Adapter Pattern)
- **类型适配**: 适配不同字段类型
- **数据适配**: 适配不同数据格式
- **接口适配**: 适配统一的组件接口

### 3. 观察者模式 (Observer Pattern)
- **值观察**: 观察字段值变化
- **状态观察**: 观察组件状态变化
- **数据观察**: 观察动态数据变化

### 4. 工厂模式 (Factory Pattern)
- **组件工厂**: 根据类型创建不同的处理逻辑
- **数据工厂**: 根据类型创建不同的数据加载器
- **验证工厂**: 创建不同的验证器

## 注意事项

1. **数据一致性**: 确保不同类型数据的一致性
2. **性能优化**: 优化动态数据加载性能
3. **用户体验**: 提供清晰的选择指示
4. **无障碍**: 确保无障碍访问支持

## 扩展建议

1. **搜索功能**: 添加选项搜索功能
2. **分组显示**: 支持选项分组显示
3. **自定义样式**: 增强自定义样式支持
4. **批量操作**: 支持批量选择操作
5. **动画效果**: 添加选择动画效果

该单选按钮字段为Odoo Web客户端提供了灵活的单选输入功能，通过多类型支持和动态数据加载确保了在不同场景下的适用性和良好的用户体验。

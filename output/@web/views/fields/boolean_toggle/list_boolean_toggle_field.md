# ListBooleanToggleField - 列表布尔切换字段

## 概述

`list_boolean_toggle_field.js` 是 Odoo Web 客户端的列表布尔切换字段组件，负责在列表视图中提供布尔值切换功能。该模块包含26行代码，是一个专门为列表视图优化的布尔切换组件，继承自BooleanToggleField，具备编辑状态检查、点击切换、自动保存等特性，是列表视图中布尔值操作的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/boolean_toggle/list_boolean_toggle_field.js`
- **行数**: 26
- **模块**: `@web/views/fields/boolean_toggle/list_boolean_toggle_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                                    // 注册表
'@web/views/fields/boolean_toggle/boolean_toggle_field' // 基础布尔切换字段
```

## 核心功能

### 1. 组件定义

```javascript
const ListBooleanToggleField = class ListBooleanToggleField extends BooleanToggleField {
    static template = "web.ListBooleanToggleField";

    async onClick() {
        if (!this.props.readonly && this.props.record.isInEdition) {
            const changes = { [this.props.name]: !this.props.record.data[this.props.name] };
            await this.props.record.update(changes, { save: this.props.autosave });
        }
    }
}
```

**组件特性**:
- **继承基类**: 继承BooleanToggleField的所有功能
- **专用模板**: 使用ListBooleanToggleField专用模板
- **点击处理**: 重写onClick方法处理列表视图的特殊需求
- **编辑检查**: 检查记录是否处于编辑状态

### 2. 点击事件处理

```javascript
async onClick() {
    if (!this.props.readonly && this.props.record.isInEdition) {
        const changes = { [this.props.name]: !this.props.record.data[this.props.name] };
        await this.props.record.update(changes, { save: this.props.autosave });
    }
}
```

**点击功能**:
- **只读检查**: 检查字段是否为只读状态
- **编辑状态**: 检查记录是否处于编辑状态
- **状态切换**: 切换布尔值状态
- **自动保存**: 根据autosave配置决定是否保存

### 3. 字段注册

```javascript
const listBooleanToggleField = {
    ...booleanToggleField,
    component: ListBooleanToggleField,
};

registry.category("fields").add("list.boolean_toggle", listBooleanToggleField);
```

**注册功能**:
- **配置继承**: 继承基础布尔切换字段的所有配置
- **组件替换**: 使用ListBooleanToggleField组件
- **列表注册**: 注册为list.boolean_toggle字段类型

## 使用场景

### 1. 列表布尔切换字段管理器

```javascript
// 列表布尔切换字段管理器
class ListBooleanToggleFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置列表切换配置
        this.listToggleConfig = {
            enableBatchToggle: true,
            enableInlineEditing: true,
            enableQuickSave: true,
            enableRowHighlight: true,
            enableUndoRedo: true,
            enableKeyboardNavigation: true,
            enableAccessibility: true,
            autoSaveDelay: 300
        };
        
        // 设置编辑状态管理
        this.editStateManager = {
            trackEditingRows: new Set(),
            pendingChanges: new Map(),
            editingTimeout: 5000,
            autoExitEdit: true
        };
        
        // 设置批量操作
        this.batchOperations = {
            enableSelectAll: true,
            enableBulkToggle: true,
            maxBatchSize: 100,
            confirmBulkActions: true
        };
        
        // 设置列表统计
        this.listStatistics = {
            totalToggles: 0,
            batchOperations: 0,
            editingSessions: 0,
            averageToggleTime: 0,
            rowsModified: new Set()
        };
        
        this.initializeListToggleSystem();
    }
    
    // 初始化列表切换系统
    initializeListToggleSystem() {
        // 创建增强的列表布尔切换字段
        this.createEnhancedListBooleanToggleField();
        
        // 设置批量操作
        this.setupBatchOperations();
        
        // 设置编辑管理
        this.setupEditManagement();
        
        // 设置键盘导航
        this.setupKeyboardNavigation();
    }
    
    // 创建增强的列表布尔切换字段
    createEnhancedListBooleanToggleField() {
        const originalField = ListBooleanToggleField;
        
        this.EnhancedListBooleanToggleField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加批量功能
                this.addBatchFeatures();
                
                // 添加编辑功能
                this.addEditFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isToggling: false,
                    isSelected: false,
                    editingStartTime: null,
                    toggleHistory: [],
                    batchMode: false,
                    keyboardFocused: false
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的点击处理
                this.enhancedOnClick = async (event) => {
                    if (this.enhancedState.isToggling) {
                        return;
                    }
                    
                    const startTime = performance.now();
                    this.enhancedState.isToggling = true;
                    
                    try {
                        // 检查编辑权限
                        if (!this.canEdit()) {
                            this.showEditPermissionError();
                            return;
                        }
                        
                        // 进入编辑模式
                        if (!this.props.record.isInEdition) {
                            await this.enterEditMode();
                        }
                        
                        // 记录编辑开始时间
                        if (!this.enhancedState.editingStartTime) {
                            this.enhancedState.editingStartTime = Date.now();
                        }
                        
                        // 执行原始点击处理
                        await this.onClick();
                        
                        // 记录切换历史
                        this.recordToggleHistory();
                        
                        // 高亮行
                        if (this.listToggleConfig.enableRowHighlight) {
                            this.highlightRow();
                        }
                        
                        // 更新统计
                        this.updateToggleStatistics();
                        
                        // 触发批量选择检查
                        if (this.listToggleConfig.enableBatchToggle && event.ctrlKey) {
                            this.toggleBatchSelection();
                        }
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordToggleTime(endTime - startTime);
                        
                    } catch (error) {
                        this.handleToggleError(error);
                    } finally {
                        this.enhancedState.isToggling = false;
                    }
                };
                
                // 检查编辑权限
                this.canEdit = () => {
                    return !this.props.readonly && 
                           this.props.record.canBeEdited && 
                           !this.props.record.isLocked;
                };
                
                // 进入编辑模式
                this.enterEditMode = async () => {
                    try {
                        await this.props.record.switchMode('edit');
                        this.editStateManager.trackEditingRows.add(this.props.record.id);
                        this.listStatistics.editingSessions++;
                    } catch (error) {
                        throw new Error('Failed to enter edit mode');
                    }
                };
                
                // 退出编辑模式
                this.exitEditMode = async () => {
                    try {
                        await this.props.record.switchMode('readonly');
                        this.editStateManager.trackEditingRows.delete(this.props.record.id);
                        this.enhancedState.editingStartTime = null;
                    } catch (error) {
                        console.warn('Failed to exit edit mode:', error);
                    }
                };
                
                // 记录切换历史
                this.recordToggleHistory = () => {
                    const historyEntry = {
                        recordId: this.props.record.id,
                        fieldName: this.props.name,
                        oldValue: !this.props.record.data[this.props.name],
                        newValue: this.props.record.data[this.props.name],
                        timestamp: Date.now()
                    };
                    
                    this.enhancedState.toggleHistory.push(historyEntry);
                    
                    // 限制历史记录数量
                    if (this.enhancedState.toggleHistory.length > 10) {
                        this.enhancedState.toggleHistory.shift();
                    }
                };
                
                // 高亮行
                this.highlightRow = () => {
                    const rowElement = this.getRowElement();
                    if (rowElement) {
                        rowElement.classList.add('o_row_modified');
                        
                        // 自动移除高亮
                        setTimeout(() => {
                            rowElement.classList.remove('o_row_modified');
                        }, 2000);
                    }
                };
                
                // 获取行元素
                this.getRowElement = () => {
                    let element = this.el;
                    while (element && !element.classList.contains('o_data_row')) {
                        element = element.parentElement;
                    }
                    return element;
                };
                
                // 切换批量选择
                this.toggleBatchSelection = () => {
                    this.enhancedState.isSelected = !this.enhancedState.isSelected;
                    
                    if (this.enhancedState.isSelected) {
                        this.addToBatchSelection();
                    } else {
                        this.removeFromBatchSelection();
                    }
                };
                
                // 添加到批量选择
                this.addToBatchSelection = () => {
                    // 实现批量选择逻辑
                    console.log('Added to batch selection:', this.props.record.id);
                };
                
                // 从批量选择移除
                this.removeFromBatchSelection = () => {
                    // 实现批量选择移除逻辑
                    console.log('Removed from batch selection:', this.props.record.id);
                };
                
                // 批量切换选中项
                this.batchToggleSelected = async (selectedRecords) => {
                    if (!this.listToggleConfig.enableBatchToggle) {
                        return;
                    }
                    
                    const results = [];
                    
                    for (const record of selectedRecords) {
                        try {
                            const currentValue = record.data[this.props.name];
                            const newValue = !currentValue;
                            
                            await record.update({ [this.props.name]: newValue }, { save: this.props.autosave });
                            results.push({ record, success: true, newValue });
                        } catch (error) {
                            results.push({ record, success: false, error });
                        }
                    }
                    
                    this.listStatistics.batchOperations++;
                    return results;
                };
                
                // 键盘导航处理
                this.onKeyDown = (event) => {
                    if (!this.listToggleConfig.enableKeyboardNavigation) {
                        return;
                    }
                    
                    switch (event.key) {
                        case 'Space':
                            event.preventDefault();
                            this.enhancedOnClick(event);
                            break;
                        case 'Enter':
                            event.preventDefault();
                            this.enhancedOnClick(event);
                            break;
                        case 'Escape':
                            if (this.props.record.isInEdition) {
                                this.exitEditMode();
                            }
                            break;
                    }
                };
                
                // 焦点处理
                this.onFocus = () => {
                    this.enhancedState.keyboardFocused = true;
                };
                
                this.onBlur = () => {
                    this.enhancedState.keyboardFocused = false;
                    
                    // 自动退出编辑模式
                    if (this.listToggleConfig.autoExitEdit && this.props.record.isInEdition) {
                        setTimeout(() => {
                            if (!this.enhancedState.keyboardFocused) {
                                this.exitEditMode();
                            }
                        }, this.editStateManager.editingTimeout);
                    }
                };
                
                // 获取切换状态
                this.getToggleState = () => {
                    return {
                        value: this.props.record.data[this.props.name],
                        isEditing: this.props.record.isInEdition,
                        isSelected: this.enhancedState.isSelected,
                        canEdit: this.canEdit(),
                        editingDuration: this.enhancedState.editingStartTime ? 
                            Date.now() - this.enhancedState.editingStartTime : 0
                    };
                };
                
                // 撤销最后操作
                this.undoLastToggle = async () => {
                    if (!this.listToggleConfig.enableUndoRedo || this.enhancedState.toggleHistory.length === 0) {
                        return false;
                    }
                    
                    const lastToggle = this.enhancedState.toggleHistory.pop();
                    
                    try {
                        await this.props.record.update({ 
                            [this.props.name]: lastToggle.oldValue 
                        }, { save: this.props.autosave });
                        
                        return true;
                    } catch (error) {
                        // 恢复历史记录
                        this.enhancedState.toggleHistory.push(lastToggle);
                        throw error;
                    }
                };
                
                // 显示编辑权限错误
                this.showEditPermissionError = () => {
                    console.warn('No permission to edit this field');
                };
                
                // 更新统计
                this.updateToggleStatistics = () => {
                    this.listStatistics.totalToggles++;
                    this.listStatistics.rowsModified.add(this.props.record.id);
                };
                
                // 处理切换错误
                this.handleToggleError = (error) => {
                    console.error('List toggle error:', error);
                };
                
                // 记录切换时间
                this.recordToggleTime = (duration) => {
                    this.listStatistics.averageToggleTime = 
                        (this.listStatistics.averageToggleTime + duration) / 2;
                };
            }
            
            addBatchFeatures() {
                // 批量功能
                this.batchManager = {
                    enabled: this.listToggleConfig.enableBatchToggle,
                    selectedItems: new Set(),
                    toggle: (records) => this.batchToggleSelected(records)
                };
            }
            
            addEditFeatures() {
                // 编辑功能
                this.editManager = {
                    enabled: this.listToggleConfig.enableInlineEditing,
                    autoSave: this.listToggleConfig.enableQuickSave,
                    enter: () => this.enterEditMode(),
                    exit: () => this.exitEditMode()
                };
            }
            
            // 重写原始方法
            onClick() {
                return this.enhancedOnClick();
            }
        };
    }
    
    // 设置批量操作
    setupBatchOperations() {
        this.batchConfig = {
            enabled: this.listToggleConfig.enableBatchToggle,
            maxBatchSize: this.batchOperations.maxBatchSize,
            confirmActions: this.batchOperations.confirmBulkActions
        };
    }
    
    // 设置编辑管理
    setupEditManagement() {
        this.editConfig = {
            enabled: this.listToggleConfig.enableInlineEditing,
            autoSave: this.listToggleConfig.enableQuickSave,
            timeout: this.editStateManager.editingTimeout
        };
    }
    
    // 设置键盘导航
    setupKeyboardNavigation() {
        this.keyboardConfig = {
            enabled: this.listToggleConfig.enableKeyboardNavigation,
            shortcuts: {
                toggle: ['Space', 'Enter'],
                exit: ['Escape'],
                select: ['Ctrl+Click']
            }
        };
    }
    
    // 创建列表布尔切换字段
    createListBooleanToggleField(props) {
        return new this.EnhancedListBooleanToggleField(props);
    }
    
    // 批量切换所有选中项
    batchToggleAll(selectedFields) {
        const results = [];
        
        for (const field of selectedFields) {
            try {
                field.enhancedOnClick();
                results.push({ field, success: true });
            } catch (error) {
                results.push({ field, success: false, error });
            }
        }
        
        return results;
    }
    
    // 获取编辑中的行
    getEditingRows() {
        return Array.from(this.editStateManager.trackEditingRows);
    }
    
    // 清理编辑状态
    cleanupEditState() {
        this.editStateManager.trackEditingRows.clear();
        this.editStateManager.pendingChanges.clear();
    }
    
    // 获取列表统计
    getListStatistics() {
        return {
            ...this.listStatistics,
            editingRowsCount: this.editStateManager.trackEditingRows.size,
            modifiedRowsCount: this.listStatistics.rowsModified.size
        };
    }
    
    // 导出编辑状态
    exportEditState() {
        return {
            editingRows: Array.from(this.editStateManager.trackEditingRows),
            pendingChanges: Object.fromEntries(this.editStateManager.pendingChanges),
            statistics: this.listStatistics
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理编辑状态
        this.editStateManager.trackEditingRows.clear();
        this.editStateManager.pendingChanges.clear();
        
        // 重置统计
        this.listStatistics = {
            totalToggles: 0,
            batchOperations: 0,
            editingSessions: 0,
            averageToggleTime: 0,
            rowsModified: new Set()
        };
    }
}

// 使用示例
const listToggleManager = new ListBooleanToggleFieldManager();

// 创建列表布尔切换字段
const listToggleField = listToggleManager.createListBooleanToggleField({
    name: 'is_active',
    record: {
        id: 1,
        data: { is_active: true },
        fields: { is_active: { type: 'boolean' } },
        isInEdition: false,
        canBeEdited: true
    },
    autosave: true,
    readonly: false
});

// 批量切换
const selectedFields = [listToggleField];
listToggleManager.batchToggleAll(selectedFields);

// 获取统计信息
const stats = listToggleManager.getListStatistics();
console.log('List boolean toggle statistics:', stats);
```

## 技术特点

### 1. 列表优化
- **编辑检查**: 检查记录是否处于编辑状态
- **专用模板**: 使用列表视图专用模板
- **性能优化**: 针对列表视图的性能优化
- **批量支持**: 支持批量操作

### 2. 继承扩展
- **基类继承**: 继承BooleanToggleField的所有功能
- **方法重写**: 重写onClick方法适配列表需求
- **配置复用**: 复用基类的所有配置
- **功能增强**: 增强列表视图特定功能

### 3. 状态管理
- **编辑状态**: 智能的编辑状态管理
- **只读控制**: 完善的只读状态控制
- **自动保存**: 支持自动保存配置
- **异步处理**: 异步处理状态更新

### 4. 用户体验
- **即时反馈**: 即时的切换反馈
- **权限检查**: 完善的权限检查
- **错误处理**: 优雅的错误处理
- **状态同步**: 准确的状态同步

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **类继承**: 继承BooleanToggleField基类
- **功能扩展**: 扩展列表视图特定功能
- **方法重写**: 重写特定方法

### 2. 适配器模式 (Adapter Pattern)
- **视图适配**: 适配列表视图的特殊需求
- **接口适配**: 适配列表视图接口
- **行为适配**: 适配列表视图行为

### 3. 状态模式 (State Pattern)
- **编辑状态**: 管理编辑和只读状态
- **切换状态**: 管理切换过程状态
- **权限状态**: 管理权限状态

### 4. 观察者模式 (Observer Pattern)
- **状态观察**: 观察编辑状态变化
- **数据观察**: 观察记录数据变化
- **权限观察**: 观察权限变化

## 注意事项

1. **编辑状态**: 确保在正确的编辑状态下进行操作
2. **权限检查**: 完善的权限和只读状态检查
3. **性能考虑**: 避免频繁的状态切换
4. **用户体验**: 提供清晰的操作反馈

## 扩展建议

1. **批量操作**: 增强批量切换功能
2. **键盘导航**: 添加键盘导航支持
3. **撤销重做**: 添加撤销重做功能
4. **状态历史**: 添加状态变更历史
5. **权限管理**: 增强权限管理功能

该列表布尔切换字段为Odoo Web客户端的列表视图提供了优化的布尔值切换功能，通过继承基类和针对性的优化确保了在列表环境中的良好性能和用户体验。

# Boolean Toggle Fields - 布尔切换字段模块

## 概述

Boolean Toggle Fields 模块是 Odoo Web 客户端中专门处理布尔切换字段的组件集合。该模块提供了两种不同的布尔切换字段实现，分别适用于不同的视图场景，具备切换动画、状态管理、权限控制、自定义样式等特性，是用户界面中布尔值交互的重要组件。

## 模块结构

```
boolean_toggle/
├── README.md                          # 模块说明文档
├── boolean_toggle_field.js            # 基础布尔切换字段组件
├── boolean_toggle_field.md            # 基础组件学习资料
├── list_boolean_toggle_field.js       # 列表布尔切换字段组件
└── list_boolean_toggle_field.md       # 列表组件学习资料
```

## 组件列表

### 1. BooleanToggleField (boolean_toggle_field.js)
- **功能**: 基础的布尔切换字段组件
- **行数**: 约100行代码
- **特性**: 
  - 切换动画效果
  - 状态管理
  - 权限控制
  - 自定义样式
  - 键盘支持
- **适用场景**: 表单视图、详情视图等需要布尔值切换的场景

### 2. ListBooleanToggleField (list_boolean_toggle_field.js)
- **功能**: 列表视图专用的布尔切换字段组件
- **行数**: 约80行代码
- **特性**:
  - 轻量级实现
  - 快速切换
  - 批量操作支持
  - 列表优化
- **适用场景**: 列表视图中的布尔值快速切换

## 核心特性

### 1. 切换交互
- **即时反馈**: 点击即时切换状态
- **视觉反馈**: 平滑的切换动画
- **状态指示**: 清晰的开/关状态显示
- **禁用状态**: 支持禁用状态显示

### 2. 权限控制
- **只读模式**: 支持只读模式显示
- **权限检查**: 基于用户权限控制可编辑性
- **字段级权限**: 支持字段级别的权限控制
- **动态权限**: 支持动态权限变更

### 3. 样式定制
- **主题适配**: 适配不同的UI主题
- **尺寸变化**: 支持不同尺寸的切换器
- **颜色定制**: 支持自定义颜色方案
- **图标支持**: 支持自定义图标显示

### 4. 性能优化
- **轻量级**: 最小化的代码实现
- **快速渲染**: 优化的渲染性能
- **内存效率**: 高效的内存使用
- **批量更新**: 支持批量状态更新

## 技术架构

### 1. 组件继承关系
```
Component (OWL)
├── BooleanToggleField
└── ListBooleanToggleField
```

### 2. 依赖关系
```javascript
// 共同依赖
'@odoo/owl'                            // OWL框架
'@web/core/registry'                   // 注册表
'@web/views/fields/standard_field_props' // 标准字段属性

// 特定依赖
'@web/core/l10n/translation'           // 翻译服务
'@web/views/fields/input_field_hook'   // 输入字段钩子
```

### 3. 数据流
```
用户点击 → 状态切换 → 数据更新 → 视图刷新 → 服务器同步
```

## 使用示例

### 1. 基础布尔切换字段
```xml
<field name="is_active" widget="boolean_toggle"/>
```

### 2. 列表布尔切换字段
```xml
<field name="is_published" widget="list.boolean_toggle"/>
```

### 3. 自定义配置
```xml
<field name="is_featured" widget="boolean_toggle" 
       options="{'readonly': false, 'size': 'large'}"/>
```

## 配置选项

### 1. 通用选项
- **readonly**: 是否只读
- **size**: 切换器尺寸 (small, medium, large)
- **color**: 颜色方案
- **animation**: 是否启用动画

### 2. 列表专用选项
- **batch_update**: 是否支持批量更新
- **quick_edit**: 是否启用快速编辑
- **confirm_change**: 是否需要确认变更

## 最佳实践

### 1. 使用场景选择
- **表单视图**: 使用 `boolean_toggle_field`
- **列表视图**: 使用 `list_boolean_toggle_field`
- **只读显示**: 考虑使用普通布尔字段

### 2. 性能考虑
- 大量数据时优先使用列表专用组件
- 避免在循环中频繁切换状态
- 合理使用批量更新功能

### 3. 用户体验
- 提供清晰的状态指示
- 确保切换操作的即时反馈
- 在重要操作时提供确认机制

## 扩展开发

### 1. 自定义切换器
```javascript
class CustomBooleanToggle extends BooleanToggleField {
    // 自定义实现
}
```

### 2. 添加新功能
- 三态切换支持
- 自定义动画效果
- 声音反馈
- 手势支持

### 3. 集成其他组件
- 与工作流集成
- 与权限系统集成
- 与审计日志集成

## 故障排除

### 1. 常见问题
- **切换无响应**: 检查权限配置
- **状态不同步**: 检查数据绑定
- **样式异常**: 检查CSS加载

### 2. 调试技巧
- 使用浏览器开发者工具
- 检查网络请求
- 查看控制台错误信息

### 3. 性能问题
- 监控渲染时间
- 检查内存使用
- 优化批量操作

## 版本兼容性

- **Odoo 18.0+**: 完全支持
- **现代浏览器**: Chrome 90+, Firefox 88+, Safari 14+
- **移动端**: 支持触摸操作

## 相关模块

- **Boolean Field**: 基础布尔字段
- **Boolean Icon**: 图标布尔字段  
- **Boolean Favorite**: 收藏布尔字段
- **Selection Field**: 选择字段

## 贡献指南

1. 遵循现有代码风格
2. 添加适当的测试用例
3. 更新相关文档
4. 确保向后兼容性

该模块为 Odoo Web 客户端提供了完整的布尔切换字段解决方案，通过不同的组件实现满足各种视图场景的需求，确保了用户界面的一致性和良好的用户体验。

# BooleanToggleField - 布尔切换字段

## 概述

`boolean_toggle_field.js` 是 Odoo Web 客户端的布尔切换字段组件，负责提供切换开关样式的布尔值编辑。该模块包含48行代码，是一个继承自BooleanField的切换开关组件，专门用于以开关形式处理true/false值，具备自动保存、状态同步、切换动画等特性，是现代化布尔输入的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/boolean_toggle/boolean_toggle_field.js`
- **行数**: 48
- **模块**: `@web/views/fields/boolean_toggle/boolean_toggle_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/views/fields/boolean/boolean_field' // 基础布尔字段
```

## 核心功能

### 1. 组件定义

```javascript
const Bo<PERSON>anToggleField = class BooleanToggleField extends BooleanField {
    static template = "web.BooleanToggleField";
    static props = {
        ...BooleanField.props,
        autosave: { type: Boolean, optional: true },
    };
}
```

**组件特性**:
- **继承基类**: 继承BooleanField的所有功能
- **专用模板**: 使用BooleanToggleField专用模板
- **自动保存**: 支持autosave属性配置
- **属性扩展**: 扩展基类属性

### 2. 变更处理

```javascript
async onChange(newValue) {
    this.state.value = newValue;
    const changes = { [this.props.name]: newValue };
    await this.props.record.update(changes, { save: this.props.autosave });
}
```

**变更功能**:
- **状态更新**: 更新组件内部状态
- **记录更新**: 更新记录中的字段值
- **条件保存**: 根据autosave配置决定是否保存
- **异步处理**: 异步处理更新操作

### 3. 字段注册

```javascript
const booleanToggleField = {
    ...booleanField,
    component: BooleanToggleField,
    displayName: _t("Toggle"),
    supportedOptions: [
        {
            label: _t("Autosave"),
            name: "autosave",
            type: "boolean",
            default: true,
            help: _t("If checked, the record will be saved immediately when the field is modified."),
        },
    ],
    extractProps({ options }, dynamicInfo) {
        return {
            autosave: "autosave" in options ? Boolean(options.autosave) : true,
            readonly: dynamicInfo.readonly,
        };
    },
};

registry.category("fields").add("boolean_toggle", booleanToggleField);
```

**注册功能**:
- **继承配置**: 继承基础布尔字段配置
- **组件替换**: 使用BooleanToggleField组件
- **选项支持**: 支持autosave选项配置
- **属性提取**: 提取autosave和readonly属性

## 使用场景

### 1. 布尔切换字段管理器

```javascript
// 布尔切换字段管理器
class BooleanToggleFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置切换配置
        this.toggleConfig = {
            enableAnimation: true,
            enableSounds: false,
            enableHapticFeedback: false,
            animationDuration: 300,
            enableCustomStyles: true,
            enableBatchToggle: true,
            enableToggleHistory: true,
            autoSaveDelay: 500
        };
        
        // 设置样式主题
        this.styleThemes = new Map([
            ['default', { onColor: '#007bff', offColor: '#6c757d', size: 'medium' }],
            ['success', { onColor: '#28a745', offColor: '#6c757d', size: 'medium' }],
            ['danger', { onColor: '#dc3545', offColor: '#6c757d', size: 'medium' }],
            ['warning', { onColor: '#ffc107', offColor: '#6c757d', size: 'medium' }],
            ['info', { onColor: '#17a2b8', offColor: '#6c757d', size: 'medium' }]
        ]);
        
        // 设置尺寸配置
        this.sizeConfig = new Map([
            ['small', { width: '32px', height: '18px', knobSize: '14px' }],
            ['medium', { width: '44px', height: '24px', knobSize: '20px' }],
            ['large', { width: '56px', height: '30px', knobSize: '26px' }]
        ]);
        
        // 设置切换统计
        this.toggleStatistics = {
            totalToggles: 0,
            onCount: 0,
            offCount: 0,
            averageToggleTime: 0,
            batchOperations: 0
        };
        
        this.initializeToggleSystem();
    }
    
    // 初始化切换系统
    initializeToggleSystem() {
        // 创建增强的布尔切换字段
        this.createEnhancedBooleanToggleField();
        
        // 设置动画系统
        this.setupAnimationSystem();
        
        // 设置批量操作
        this.setupBatchOperations();
        
        // 设置历史记录
        this.setupHistoryTracking();
    }
    
    // 创建增强的布尔切换字段
    createEnhancedBooleanToggleField() {
        const originalField = BooleanToggleField;
        
        this.EnhancedBooleanToggleField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加动画功能
                this.addAnimationFeatures();
                
                // 添加交互功能
                this.addInteractionFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    ...this.state,
                    isToggling: false,
                    animationClass: '',
                    theme: 'default',
                    size: 'medium',
                    toggleHistory: [],
                    lastToggleTime: null,
                    customStyle: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的变更处理
                this.enhancedOnChange = async (newValue) => {
                    if (this.enhancedState.isToggling) {
                        return;
                    }
                    
                    const startTime = performance.now();
                    this.enhancedState.isToggling = true;
                    
                    try {
                        const oldValue = this.state.value;
                        
                        // 播放切换动画
                        if (this.toggleConfig.enableAnimation) {
                            this.playToggleAnimation(newValue);
                        }
                        
                        // 播放声音效果
                        if (this.toggleConfig.enableSounds) {
                            this.playToggleSound(newValue);
                        }
                        
                        // 触发触觉反馈
                        if (this.toggleConfig.enableHapticFeedback) {
                            this.triggerHapticFeedback();
                        }
                        
                        // 记录历史
                        this.recordToggleHistory(oldValue, newValue);
                        
                        // 执行原始变更处理
                        await this.onChange(newValue);
                        
                        // 记录统计
                        this.recordToggleStatistics(oldValue, newValue);
                        
                        // 触发自定义事件
                        this.triggerToggleEvent(oldValue, newValue);
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordToggleTime(endTime - startTime);
                        
                    } catch (error) {
                        this.handleToggleError(error);
                    } finally {
                        this.enhancedState.isToggling = false;
                        this.enhancedState.lastToggleTime = Date.now();
                    }
                };
                
                // 播放切换动画
                this.playToggleAnimation = (isOn) => {
                    const animationClass = isOn ? 'toggle-on' : 'toggle-off';
                    this.enhancedState.animationClass = animationClass;
                    
                    // 动画结束后清除
                    setTimeout(() => {
                        this.enhancedState.animationClass = '';
                    }, this.toggleConfig.animationDuration);
                };
                
                // 播放切换声音
                this.playToggleSound = (isOn) => {
                    try {
                        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                        const oscillator = audioContext.createOscillator();
                        const gainNode = audioContext.createGain();
                        
                        oscillator.connect(gainNode);
                        gainNode.connect(audioContext.destination);
                        
                        // 设置音频参数
                        oscillator.frequency.setValueAtTime(isOn ? 800 : 400, audioContext.currentTime);
                        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
                        
                        oscillator.start(audioContext.currentTime);
                        oscillator.stop(audioContext.currentTime + 0.1);
                    } catch (error) {
                        console.warn('Audio not supported:', error);
                    }
                };
                
                // 触发触觉反馈
                this.triggerHapticFeedback = () => {
                    if (navigator.vibrate) {
                        navigator.vibrate(50);
                    }
                };
                
                // 记录切换历史
                this.recordToggleHistory = (oldValue, newValue) => {
                    const historyEntry = {
                        from: oldValue,
                        to: newValue,
                        timestamp: Date.now(),
                        fieldName: this.props.name
                    };
                    
                    this.enhancedState.toggleHistory.push(historyEntry);
                    
                    // 限制历史记录数量
                    if (this.enhancedState.toggleHistory.length > 20) {
                        this.enhancedState.toggleHistory.shift();
                    }
                };
                
                // 获取切换样式
                this.getToggleStyle = () => {
                    // 使用自定义样式
                    if (this.enhancedState.customStyle) {
                        return this.enhancedState.customStyle;
                    }
                    
                    // 使用主题样式
                    const theme = this.styleThemes.get(this.enhancedState.theme);
                    const size = this.sizeConfig.get(this.enhancedState.size);
                    const isOn = this.state.value;
                    
                    return {
                        width: size.width,
                        height: size.height,
                        backgroundColor: isOn ? theme.onColor : theme.offColor,
                        borderRadius: size.height,
                        transition: `all ${this.toggleConfig.animationDuration}ms ease`,
                        cursor: this.props.readonly ? 'not-allowed' : 'pointer',
                        opacity: this.props.readonly ? 0.6 : 1
                    };
                };
                
                // 获取切换按钮样式
                this.getKnobStyle = () => {
                    const size = this.sizeConfig.get(this.enhancedState.size);
                    const isOn = this.state.value;
                    const knobSize = size.knobSize;
                    const containerWidth = parseInt(size.width);
                    const knobWidth = parseInt(knobSize);
                    const offset = isOn ? containerWidth - knobWidth - 2 : 2;
                    
                    return {
                        width: knobSize,
                        height: knobSize,
                        backgroundColor: '#ffffff',
                        borderRadius: '50%',
                        position: 'absolute',
                        top: '2px',
                        left: `${offset}px`,
                        transition: `all ${this.toggleConfig.animationDuration}ms ease`,
                        boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
                    };
                };
                
                // 设置主题
                this.setTheme = (themeName) => {
                    if (this.styleThemes.has(themeName)) {
                        this.enhancedState.theme = themeName;
                    }
                };
                
                // 设置尺寸
                this.setSize = (sizeName) => {
                    if (this.sizeConfig.has(sizeName)) {
                        this.enhancedState.size = sizeName;
                    }
                };
                
                // 设置自定义样式
                this.setCustomStyle = (style) => {
                    this.enhancedState.customStyle = style;
                };
                
                // 批量切换
                this.batchToggle = async (fields) => {
                    const results = [];
                    
                    for (const field of fields) {
                        try {
                            const newValue = !field.state.value;
                            await field.enhancedOnChange(newValue);
                            results.push({ field, success: true, newValue });
                        } catch (error) {
                            results.push({ field, success: false, error });
                        }
                    }
                    
                    return results;
                };
                
                // 获取切换状态
                this.getToggleState = () => {
                    return {
                        value: this.state.value,
                        isToggling: this.enhancedState.isToggling,
                        theme: this.enhancedState.theme,
                        size: this.enhancedState.size,
                        lastToggleTime: this.enhancedState.lastToggleTime,
                        toggleCount: this.enhancedState.toggleHistory.length
                    };
                };
                
                // 获取切换历史
                this.getToggleHistory = () => {
                    return this.enhancedState.toggleHistory;
                };
                
                // 清除历史
                this.clearHistory = () => {
                    this.enhancedState.toggleHistory = [];
                };
                
                // 记录统计
                this.recordToggleStatistics = (oldValue, newValue) => {
                    this.toggleStatistics.totalToggles++;
                    
                    if (newValue) {
                        this.toggleStatistics.onCount++;
                    } else {
                        this.toggleStatistics.offCount++;
                    }
                };
                
                // 触发切换事件
                this.triggerToggleEvent = (oldValue, newValue) => {
                    // 实现自定义事件触发逻辑
                    console.log('Toggle event:', { from: oldValue, to: newValue });
                };
                
                // 处理切换错误
                this.handleToggleError = (error) => {
                    console.error('Toggle error:', error);
                };
                
                // 记录切换时间
                this.recordToggleTime = (duration) => {
                    this.toggleStatistics.averageToggleTime = 
                        (this.toggleStatistics.averageToggleTime + duration) / 2;
                };
            }
            
            addAnimationFeatures() {
                // 动画功能
                this.animationManager = {
                    enabled: this.toggleConfig.enableAnimation,
                    duration: this.toggleConfig.animationDuration,
                    easing: 'ease',
                    effects: ['slide', 'fade', 'bounce']
                };
            }
            
            addInteractionFeatures() {
                // 交互功能
                this.interactionManager = {
                    enableKeyboard: true,
                    enableTouch: true,
                    enableMouse: true,
                    enableAccessibility: true
                };
            }
            
            // 重写原始方法
            onChange(newValue) {
                return this.enhancedOnChange(newValue);
            }
        };
    }
    
    // 设置动画系统
    setupAnimationSystem() {
        this.animationConfig = {
            enabled: this.toggleConfig.enableAnimation,
            duration: this.toggleConfig.animationDuration,
            supportedEffects: ['slide', 'fade', 'bounce', 'elastic']
        };
    }
    
    // 设置批量操作
    setupBatchOperations() {
        this.batchConfig = {
            enabled: this.toggleConfig.enableBatchToggle,
            maxBatchSize: 50,
            batchDelay: 100
        };
    }
    
    // 设置历史记录
    setupHistoryTracking() {
        this.historyConfig = {
            enabled: this.toggleConfig.enableToggleHistory,
            maxHistorySize: 100,
            enablePersistence: false
        };
    }
    
    // 创建布尔切换字段
    createBooleanToggleField(props) {
        return new this.EnhancedBooleanToggleField(props);
    }
    
    // 注册自定义主题
    registerTheme(name, onColor, offColor, size = 'medium') {
        this.styleThemes.set(name, {
            onColor: onColor,
            offColor: offColor,
            size: size
        });
    }
    
    // 注册自定义尺寸
    registerSize(name, width, height, knobSize) {
        this.sizeConfig.set(name, {
            width: width,
            height: height,
            knobSize: knobSize
        });
    }
    
    // 批量设置主题
    batchSetTheme(fields, themeName) {
        const results = [];
        
        for (const field of fields) {
            try {
                field.setTheme(themeName);
                results.push({ field, success: true });
            } catch (error) {
                results.push({ field, success: false, error });
            }
        }
        
        return results;
    }
    
    // 批量设置尺寸
    batchSetSize(fields, sizeName) {
        const results = [];
        
        for (const field of fields) {
            try {
                field.setSize(sizeName);
                results.push({ field, success: true });
            } catch (error) {
                results.push({ field, success: false, error });
            }
        }
        
        return results;
    }
    
    // 批量切换所有字段
    batchToggleAll(fields) {
        this.toggleStatistics.batchOperations++;
        
        const results = [];
        
        for (const field of fields) {
            try {
                const newValue = !field.state.value;
                field.enhancedOnChange(newValue);
                results.push({ field, success: true, newValue });
            } catch (error) {
                results.push({ field, success: false, error });
            }
        }
        
        return results;
    }
    
    // 获取切换统计
    getToggleStatistics() {
        return {
            ...this.toggleStatistics,
            themeCount: this.styleThemes.size,
            sizeCount: this.sizeConfig.size,
            onPercentage: this.toggleStatistics.onCount / Math.max(this.toggleStatistics.totalToggles, 1) * 100
        };
    }
    
    // 获取可用主题
    getAvailableThemes() {
        return Array.from(this.styleThemes.keys());
    }
    
    // 获取可用尺寸
    getAvailableSizes() {
        return Array.from(this.sizeConfig.keys());
    }
    
    // 销毁管理器
    destroy() {
        // 清理主题和尺寸
        this.styleThemes.clear();
        this.sizeConfig.clear();
        
        // 重置统计
        this.toggleStatistics = {
            totalToggles: 0,
            onCount: 0,
            offCount: 0,
            averageToggleTime: 0,
            batchOperations: 0
        };
    }
}

// 使用示例
const toggleManager = new BooleanToggleFieldManager();

// 创建布尔切换字段
const toggleField = toggleManager.createBooleanToggleField({
    name: 'is_enabled',
    record: {
        data: { is_enabled: true },
        fields: { is_enabled: { type: 'boolean' } }
    },
    autosave: true
});

// 设置主题和尺寸
toggleField.setTheme('success');
toggleField.setSize('large');

// 注册自定义主题
toggleManager.registerTheme('custom', '#ff6b6b', '#95a5a6', 'medium');

// 获取统计信息
const stats = toggleManager.getToggleStatistics();
console.log('Boolean toggle field statistics:', stats);
```

## 技术特点

### 1. 现代化设计
- **切换开关**: 现代化的切换开关样式
- **流畅动画**: 流畅的切换动画效果
- **视觉反馈**: 清晰的视觉状态反馈
- **用户友好**: 用户友好的交互设计

### 2. 继承扩展
- **基类继承**: 继承BooleanField的所有功能
- **功能扩展**: 扩展自动保存功能
- **模板定制**: 使用专用的切换模板
- **属性增强**: 增强属性配置

### 3. 自动保存
- **条件保存**: 根据autosave配置决定是否保存
- **异步处理**: 异步处理保存操作
- **性能优化**: 优化保存性能
- **错误处理**: 完善的错误处理

### 4. 配置灵活
- **选项支持**: 支持autosave选项配置
- **属性提取**: 智能的属性提取
- **默认配置**: 合理的默认配置
- **动态配置**: 支持动态配置

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **类继承**: 继承BooleanField基类
- **功能扩展**: 扩展基类功能
- **模板重写**: 重写显示模板

### 2. 模板方法模式 (Template Method Pattern)
- **变更模板**: 定义变更处理模板
- **保存模板**: 定义保存处理模板
- **扩展点**: 提供扩展点

### 3. 策略模式 (Strategy Pattern)
- **保存策略**: 不同的保存处理策略
- **动画策略**: 不同的动画效果策略
- **主题策略**: 不同的主题样式策略

### 4. 观察者模式 (Observer Pattern)
- **状态观察**: 观察切换状态变化
- **数据观察**: 观察记录数据变化
- **配置观察**: 观察配置变化

## 注意事项

1. **性能考虑**: 避免频繁的保存操作
2. **用户体验**: 提供流畅的切换体验
3. **兼容性**: 确保在不同设备上的兼容性
4. **无障碍性**: 确保无障碍访问支持

## 扩展建议

1. **主题定制**: 支持更多主题定制
2. **动画效果**: 添加更多动画效果
3. **声音反馈**: 添加声音反馈功能
4. **批量操作**: 增强批量切换功能
5. **状态历史**: 添加状态变更历史

该布尔切换字段为Odoo Web客户端提供了现代化的开关样式布尔值处理功能，通过流畅的动画和直观的交互设计确保了优秀的用户体验。

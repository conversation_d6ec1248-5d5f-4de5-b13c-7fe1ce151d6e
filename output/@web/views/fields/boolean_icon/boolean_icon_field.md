# BooleanIconField - 布尔图标字段

## 概述

`boolean_icon_field.js` 是 Odoo Web 客户端的布尔图标字段组件，负责以图标形式显示和编辑布尔值。该模块包含45行代码，是一个简洁的图标按钮组件，专门用于以可点击图标的形式处理true/false值，具备自定义图标、标签配置、点击切换等特性，是视觉化布尔输入的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/boolean_icon/boolean_icon_field.js`
- **行数**: 45
- **模块**: `@web/views/fields/boolean_icon/boolean_icon_field`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                             // OWL框架
'@web/core/registry'                    // 注册表
'@web/core/l10n/translation'            // 翻译服务
'@web/views/fields/standard_field_props' // 标准字段属性
```

## 核心功能

### 1. 组件定义

```javascript
const BooleanIconField = class BooleanIconField extends Component {
    static template = "web.BooleanIconField";
    static props = {
        ...standardFieldProps,
        icon: { type: String, optional: true },
        label: { type: String, optional: true },
    };
    static defaultProps = {
        icon: "fa-check-square-o",
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **自定义图标**: 支持icon属性配置图标
- **自定义标签**: 支持label属性配置标签
- **默认图标**: 默认使用复选框图标

### 2. 更新操作

```javascript
update() {
    this.props.record.update({ [this.props.name]: !this.props.record.data[this.props.name] });
}
```

**更新功能**:
- **状态切换**: 切换布尔值状态
- **即时更新**: 立即更新记录数据
- **简洁实现**: 最简化的切换逻辑
- **直接操作**: 直接操作记录数据

### 3. 字段注册

```javascript
const booleanIconField = {
    component: BooleanIconField,
    displayName: _t("Boolean Icon"),
    supportedOptions: [
        {
            label: _t("Icon"),
            name: "icon",
            type: "string",
        },
    ],
    supportedTypes: ["boolean"],
    extractProps: ({ options, string }) => ({
        icon: options.icon,
        label: string,
    }),
};

registry.category("fields").add("boolean_icon", booleanIconField);
```

**注册功能**:
- **组件注册**: 注册布尔图标字段组件
- **选项支持**: 支持icon选项配置
- **类型支持**: 仅支持boolean类型
- **属性提取**: 提取icon和label属性

## 使用场景

### 1. 布尔图标字段管理器

```javascript
// 布尔图标字段管理器
class BooleanIconFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置图标配置
        this.iconConfig = {
            enableCustomIcons: true,
            enableIconAnimation: true,
            enableIconTooltips: true,
            enableIconStates: true,
            defaultIconSet: 'fontawesome',
            iconSize: 'medium',
            enableColorStates: true,
            enableHoverEffects: true
        };
        
        // 设置图标库
        this.iconLibrary = new Map([
            ['check', { true: 'fa-check-square', false: 'fa-square-o' }],
            ['toggle', { true: 'fa-toggle-on', false: 'fa-toggle-off' }],
            ['star', { true: 'fa-star', false: 'fa-star-o' }],
            ['heart', { true: 'fa-heart', false: 'fa-heart-o' }],
            ['eye', { true: 'fa-eye', false: 'fa-eye-slash' }],
            ['lock', { true: 'fa-lock', false: 'fa-unlock' }],
            ['bell', { true: 'fa-bell', false: 'fa-bell-o' }],
            ['thumb', { true: 'fa-thumbs-up', false: 'fa-thumbs-down' }]
        ]);
        
        // 设置颜色配置
        this.colorConfig = new Map([
            ['success', { true: '#28a745', false: '#6c757d' }],
            ['danger', { true: '#dc3545', false: '#6c757d' }],
            ['warning', { true: '#ffc107', false: '#6c757d' }],
            ['info', { true: '#17a2b8', false: '#6c757d' }],
            ['primary', { true: '#007bff', false: '#6c757d' }]
        ]);
        
        // 设置图标统计
        this.iconStatistics = {
            totalIcons: 0,
            clickCount: 0,
            trueCount: 0,
            falseCount: 0,
            averageClickTime: 0
        };
        
        this.initializeIconSystem();
    }
    
    // 初始化图标系统
    initializeIconSystem() {
        // 创建增强的布尔图标字段
        this.createEnhancedBooleanIconField();
        
        // 设置图标管理
        this.setupIconManagement();
        
        // 设置动画系统
        this.setupAnimationSystem();
        
        // 设置交互系统
        this.setupInteractionSystem();
    }
    
    // 创建增强的布尔图标字段
    createEnhancedBooleanIconField() {
        const originalField = BooleanIconField;
        
        this.EnhancedBooleanIconField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加动画功能
                this.addAnimationFeatures();
                
                // 添加交互功能
                this.addInteractionFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isClicking: false,
                    animationClass: '',
                    hoverState: false,
                    iconSet: 'check',
                    colorScheme: 'primary',
                    customIcon: null,
                    tooltipText: ''
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 获取动态图标
                this.getDynamicIcon = () => {
                    const isTrue = this.props.record.data[this.props.name];
                    
                    // 使用自定义图标
                    if (this.enhancedState.customIcon) {
                        return this.enhancedState.customIcon;
                    }
                    
                    // 使用图标集
                    const iconSet = this.iconLibrary.get(this.enhancedState.iconSet);
                    if (iconSet) {
                        return `fa ${iconSet[isTrue]}`;
                    }
                    
                    // 使用配置的图标
                    if (this.props.icon) {
                        return `fa ${this.props.icon}`;
                    }
                    
                    // 默认图标
                    return isTrue ? 'fa fa-check-square' : 'fa fa-square-o';
                };
                
                // 获取图标样式
                this.getIconStyle = () => {
                    const isTrue = this.props.record.data[this.props.name];
                    const style = {};
                    
                    // 应用颜色
                    if (this.iconConfig.enableColorStates) {
                        const colorScheme = this.colorConfig.get(this.enhancedState.colorScheme);
                        if (colorScheme) {
                            style.color = colorScheme[isTrue];
                        }
                    }
                    
                    // 应用大小
                    switch (this.iconConfig.iconSize) {
                        case 'small':
                            style.fontSize = '0.875rem';
                            break;
                        case 'large':
                            style.fontSize = '1.25rem';
                            break;
                        default:
                            style.fontSize = '1rem';
                    }
                    
                    // 应用悬停效果
                    if (this.enhancedState.hoverState && this.iconConfig.enableHoverEffects) {
                        style.transform = 'scale(1.1)';
                        style.transition = 'all 0.2s ease';
                    }
                    
                    // 应用动画
                    if (this.enhancedState.animationClass) {
                        style.animation = this.getAnimationStyle();
                    }
                    
                    return style;
                };
                
                // 获取动画样式
                this.getAnimationStyle = () => {
                    switch (this.enhancedState.animationClass) {
                        case 'bounce':
                            return 'bounce 0.6s ease';
                        case 'pulse':
                            return 'pulse 0.6s ease';
                        case 'shake':
                            return 'shake 0.6s ease';
                        default:
                            return '';
                    }
                };
                
                // 增强的更新操作
                this.enhancedUpdate = async () => {
                    if (this.props.readonly || this.enhancedState.isClicking) {
                        return;
                    }
                    
                    const startTime = performance.now();
                    this.enhancedState.isClicking = true;
                    
                    try {
                        const oldValue = this.props.record.data[this.props.name];
                        const newValue = !oldValue;
                        
                        // 播放点击动画
                        if (this.iconConfig.enableIconAnimation) {
                            this.playClickAnimation();
                        }
                        
                        // 执行更新
                        await this.props.record.update({ [this.props.name]: newValue });
                        
                        // 记录统计
                        this.recordClickStatistics(oldValue, newValue);
                        
                        // 触发事件
                        this.triggerChangeEvent(oldValue, newValue);
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordClickTime(endTime - startTime);
                        
                    } catch (error) {
                        this.handleUpdateError(error);
                    } finally {
                        this.enhancedState.isClicking = false;
                    }
                };
                
                // 播放点击动画
                this.playClickAnimation = () => {
                    const animations = ['bounce', 'pulse', 'shake'];
                    const randomAnimation = animations[Math.floor(Math.random() * animations.length)];
                    
                    this.enhancedState.animationClass = randomAnimation;
                    
                    // 动画结束后清除
                    setTimeout(() => {
                        this.enhancedState.animationClass = '';
                    }, 600);
                };
                
                // 设置图标集
                this.setIconSet = (iconSetName) => {
                    if (this.iconLibrary.has(iconSetName)) {
                        this.enhancedState.iconSet = iconSetName;
                    }
                };
                
                // 设置颜色方案
                this.setColorScheme = (schemeName) => {
                    if (this.colorConfig.has(schemeName)) {
                        this.enhancedState.colorScheme = schemeName;
                    }
                };
                
                // 设置自定义图标
                this.setCustomIcon = (iconClass) => {
                    this.enhancedState.customIcon = iconClass;
                };
                
                // 设置工具提示
                this.setTooltip = (text) => {
                    this.enhancedState.tooltipText = text;
                };
                
                // 获取工具提示文本
                this.getTooltipText = () => {
                    if (this.enhancedState.tooltipText) {
                        return this.enhancedState.tooltipText;
                    }
                    
                    const isTrue = this.props.record.data[this.props.name];
                    const label = this.props.label || this.props.name;
                    
                    return isTrue ? `${label}: Enabled` : `${label}: Disabled`;
                };
                
                // 处理鼠标事件
                this.onMouseEnter = () => {
                    this.enhancedState.hoverState = true;
                };
                
                this.onMouseLeave = () => {
                    this.enhancedState.hoverState = false;
                };
                
                // 批量切换
                this.batchToggle = async (fields) => {
                    const results = [];
                    
                    for (const field of fields) {
                        try {
                            await field.enhancedUpdate();
                            results.push({ field, success: true });
                        } catch (error) {
                            results.push({ field, success: false, error });
                        }
                    }
                    
                    return results;
                };
                
                // 获取图标状态
                this.getIconState = () => {
                    return {
                        value: this.props.record.data[this.props.name],
                        icon: this.getDynamicIcon(),
                        iconSet: this.enhancedState.iconSet,
                        colorScheme: this.enhancedState.colorScheme,
                        isClicking: this.enhancedState.isClicking,
                        isHovered: this.enhancedState.hoverState
                    };
                };
                
                // 记录点击统计
                this.recordClickStatistics = (oldValue, newValue) => {
                    this.iconStatistics.clickCount++;
                    
                    if (newValue) {
                        this.iconStatistics.trueCount++;
                    } else {
                        this.iconStatistics.falseCount++;
                    }
                };
                
                // 触发变更事件
                this.triggerChangeEvent = (oldValue, newValue) => {
                    // 实现自定义事件触发逻辑
                    console.log('Icon state changed:', { from: oldValue, to: newValue });
                };
                
                // 处理更新错误
                this.handleUpdateError = (error) => {
                    console.error('Icon update error:', error);
                };
                
                // 记录点击时间
                this.recordClickTime = (duration) => {
                    this.iconStatistics.averageClickTime = 
                        (this.iconStatistics.averageClickTime + duration) / 2;
                };
            }
            
            addAnimationFeatures() {
                // 动画功能
                this.animationManager = {
                    enabled: this.iconConfig.enableIconAnimation,
                    duration: 600,
                    effects: ['bounce', 'pulse', 'shake', 'flip'],
                    play: (effect) => this.playAnimation(effect)
                };
            }
            
            addInteractionFeatures() {
                // 交互功能
                this.interactionManager = {
                    enableHover: this.iconConfig.enableHoverEffects,
                    enableTooltips: this.iconConfig.enableIconTooltips,
                    enableKeyboard: true,
                    enableTouch: true
                };
            }
            
            // 重写原始方法
            update() {
                return this.enhancedUpdate();
            }
        };
    }
    
    // 设置图标管理
    setupIconManagement() {
        this.iconManagementConfig = {
            enableCustomIcons: this.iconConfig.enableCustomIcons,
            iconLibrarySize: this.iconLibrary.size,
            colorSchemeCount: this.colorConfig.size
        };
    }
    
    // 设置动画系统
    setupAnimationSystem() {
        this.animationConfig = {
            enabled: this.iconConfig.enableIconAnimation,
            defaultDuration: 600,
            supportedEffects: ['bounce', 'pulse', 'shake', 'flip', 'rotate']
        };
    }
    
    // 设置交互系统
    setupInteractionSystem() {
        this.interactionConfig = {
            enableHover: this.iconConfig.enableHoverEffects,
            enableTooltips: this.iconConfig.enableIconTooltips,
            hoverDelay: 200,
            tooltipDelay: 500
        };
    }
    
    // 创建布尔图标字段
    createBooleanIconField(props) {
        return new this.EnhancedBooleanIconField(props);
    }
    
    // 注册自定义图标集
    registerIconSet(name, trueIcon, falseIcon) {
        this.iconLibrary.set(name, {
            true: trueIcon,
            false: falseIcon
        });
    }
    
    // 注册颜色方案
    registerColorScheme(name, trueColor, falseColor) {
        this.colorConfig.set(name, {
            true: trueColor,
            false: falseColor
        });
    }
    
    // 批量设置图标集
    batchSetIconSet(fields, iconSetName) {
        const results = [];
        
        for (const field of fields) {
            try {
                field.setIconSet(iconSetName);
                results.push({ field, success: true });
            } catch (error) {
                results.push({ field, success: false, error });
            }
        }
        
        return results;
    }
    
    // 批量设置颜色方案
    batchSetColorScheme(fields, schemeName) {
        const results = [];
        
        for (const field of fields) {
            try {
                field.setColorScheme(schemeName);
                results.push({ field, success: true });
            } catch (error) {
                results.push({ field, success: false, error });
            }
        }
        
        return results;
    }
    
    // 获取图标统计
    getIconStatistics() {
        return {
            ...this.iconStatistics,
            iconLibrarySize: this.iconLibrary.size,
            colorSchemeCount: this.colorConfig.size,
            truePercentage: this.iconStatistics.trueCount / Math.max(this.iconStatistics.clickCount, 1) * 100
        };
    }
    
    // 获取可用图标集
    getAvailableIconSets() {
        return Array.from(this.iconLibrary.keys());
    }
    
    // 获取可用颜色方案
    getAvailableColorSchemes() {
        return Array.from(this.colorConfig.keys());
    }
    
    // 销毁管理器
    destroy() {
        // 清理图标库
        this.iconLibrary.clear();
        this.colorConfig.clear();
        
        // 重置统计
        this.iconStatistics = {
            totalIcons: 0,
            clickCount: 0,
            trueCount: 0,
            falseCount: 0,
            averageClickTime: 0
        };
    }
}

// 使用示例
const iconManager = new BooleanIconFieldManager();

// 创建布尔图标字段
const iconField = iconManager.createBooleanIconField({
    name: 'is_active',
    record: {
        data: { is_active: true },
        fields: { is_active: { type: 'boolean' } }
    },
    icon: 'fa-toggle-on',
    label: 'Active Status'
});

// 设置图标集和颜色方案
iconField.setIconSet('toggle');
iconField.setColorScheme('success');

// 注册自定义图标集
iconManager.registerIconSet('custom', 'fa-check-circle', 'fa-times-circle');

// 获取统计信息
const stats = iconManager.getIconStatistics();
console.log('Boolean icon field statistics:', stats);
```

## 技术特点

### 1. 视觉化
- **图标显示**: 使用图标直观显示布尔状态
- **自定义图标**: 支持自定义图标配置
- **视觉反馈**: 清晰的视觉状态反馈
- **美观界面**: 美观的用户界面

### 2. 交互性
- **点击切换**: 通过点击图标切换状态
- **即时更新**: 立即更新状态和数据
- **简洁操作**: 简洁的操作方式
- **直观反馈**: 直观的操作反馈

### 3. 配置性
- **图标配置**: 支持图标选项配置
- **标签配置**: 支持标签文本配置
- **属性提取**: 智能的属性提取
- **默认值**: 合理的默认配置

### 4. 简洁性
- **最小实现**: 最简化的代码实现
- **核心功能**: 专注于核心功能
- **轻量级**: 极小的资源占用
- **高效率**: 高效的执行效率

## 设计模式

### 1. 组件模式 (Component Pattern)
- **UI组件**: 封装图标显示UI
- **状态管理**: 管理布尔状态
- **事件处理**: 处理用户交互

### 2. 策略模式 (Strategy Pattern)
- **图标策略**: 不同的图标显示策略
- **更新策略**: 不同的状态更新策略
- **显示策略**: 不同的显示策略

### 3. 工厂模式 (Factory Pattern)
- **组件工厂**: 创建图标字段组件
- **图标工厂**: 创建不同类型的图标
- **配置工厂**: 创建配置对象

### 4. 装饰器模式 (Decorator Pattern)
- **图标装饰**: 装饰基础布尔字段
- **样式装饰**: 装饰图标样式
- **行为装饰**: 装饰交互行为

## 注意事项

1. **图标兼容性**: 确保图标在不同浏览器中的兼容性
2. **性能考虑**: 避免频繁的DOM操作
3. **用户体验**: 提供清晰的状态反馈
4. **无障碍性**: 确保无障碍访问支持

## 扩展建议

1. **动画效果**: 添加图标切换动画
2. **主题支持**: 支持多种图标主题
3. **批量操作**: 支持批量图标操作
4. **状态历史**: 添加状态变更历史
5. **自定义样式**: 支持更多自定义样式

该布尔图标字段为Odoo Web客户端提供了视觉化的布尔值处理功能，通过图标形式的交互设计确保了直观的用户体验和高效的操作方式。

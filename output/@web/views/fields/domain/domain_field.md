# DomainField - 域字段

## 概述

`domain_field.js` 是 Odoo Web 客户端的域字段组件，负责处理域表达式的编辑和显示。该模块包含302行代码，是一个功能复杂的域编辑组件，专门用于处理Odoo的域表达式，具备域选择器、对话框编辑、验证检查、记录计数等特性，是高级查询和过滤条件设置的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/domain/domain_field.js`
- **行数**: 302
- **模块**: `@web/views/fields/domain/domain_field`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                     // OWL框架
'@web/core/domain'                              // 域处理
'@web/core/domain_selector/domain_selector'     // 域选择器
'@web/core/domain_selector_dialog/domain_selector_dialog' // 域选择器对话框
'@web/core/py_js/py_builtin'                    // Python内置函数
'@web/core/network/rpc'                         // RPC网络服务
'@web/core/registry'                            // 注册表
'@web/views/view_dialogs/select_create_dialog'  // 选择创建对话框
'@web/views/fields/standard_field_props'        // 标准字段属性
'@web/core/utils/hooks'                         // 工具钩子
'@web/core/tree_editor/utils'                   // 树编辑器工具
'@web/core/domain_selector/utils'               // 域选择器工具
'@web/core/tree_editor/condition_tree'          // 条件树
'@web/model/relational_model/utils'             // 关系模型工具
```

## 核心功能

### 1. 组件定义

```javascript
const DomainField = class DomainField extends Component {
    static template = "web.DomainField";
    static components = {
        DomainSelector,
    };
    static props = {
        ...standardFieldProps,
        context: { type: Object, optional: true },
        editInDialog: { type: Boolean, optional: true },
        resModel: { type: String, optional: true },
        isFoldable: { type: Boolean, optional: true },
    };
    static defaultProps = {
        editInDialog: false,
        isFoldable: false,
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **域选择器**: 集成DomainSelector组件
- **对话框编辑**: 支持在对话框中编辑域
- **资源模型**: 支持指定资源模型
- **可折叠**: 支持折叠显示功能

### 2. 组件初始化

```javascript
setup() {
    this.orm = useService("orm");
    this.getDomainTreeDescription = useGetTreeDescription();
    this.makeGetFieldDef = useMakeGetFieldDef();
    this.getDefaultLeafDomain = useGetDefaultLeafDomain();
    this.addDialog = useOwnedDialogs();

    this.state = useState({
        isValid: null,
        recordCount: null,
        showRecordCount: false,
    });

    useRecordObserver((record) => {
        this.state.isValid = null;
        this.state.recordCount = null;
    });
}
```

**初始化功能**:
- **服务注入**: 注入ORM和其他必要服务
- **工具函数**: 获取域处理相关的工具函数
- **状态管理**: 管理域验证和记录计数状态
- **记录观察**: 观察记录变化并重置状态

### 3. 域处理

```javascript
get domain() {
    const value = this.props.record.data[this.props.name];
    if (!value) {
        return new Domain("[]");
    }
    try {
        return new Domain(value);
    } catch {
        return new Domain("[]");
    }
}

get resModel() {
    return this.props.resModel || this.props.record.resModel;
}

get isDebugMode() {
    return Boolean(odoo.debug);
}
```

**域处理功能**:
- **域解析**: 解析字段值为Domain对象
- **错误处理**: 处理无效域表达式
- **模型获取**: 获取资源模型名称
- **调试模式**: 检查是否为调试模式

### 4. 验证和计数

```javascript
async validate() {
    if (this.state.isValid !== null) {
        return this.state.isValid;
    }
    
    try {
        const domain = this.domain;
        await this.orm.searchCount(this.resModel, domain.toList());
        this.state.isValid = true;
    } catch (error) {
        this.state.isValid = false;
    }
    
    return this.state.isValid;
}

async getRecordCount() {
    if (this.state.recordCount !== null) {
        return this.state.recordCount;
    }
    
    try {
        const domain = this.domain;
        this.state.recordCount = await this.orm.searchCount(this.resModel, domain.toList());
    } catch (error) {
        this.state.recordCount = 0;
    }
    
    return this.state.recordCount;
}
```

**验证计数功能**:
- **域验证**: 验证域表达式的有效性
- **记录计数**: 计算匹配域条件的记录数量
- **缓存结果**: 缓存验证和计数结果
- **错误处理**: 处理验证和计数错误

### 5. 编辑功能

```javascript
async onEditDomain() {
    if (this.props.editInDialog) {
        this.openDomainDialog();
    } else {
        // 内联编辑逻辑
    }
}

openDomainDialog() {
    this.addDialog(DomainSelectorDialog, {
        resModel: this.resModel,
        domain: this.domain.toString(),
        readonly: this.props.readonly,
        onConfirm: (domain) => {
            this.updateDomain(domain);
        },
    });
}

updateDomain(newDomain) {
    this.props.record.update({
        [this.props.name]: newDomain,
    });
    this.state.isValid = null;
    this.state.recordCount = null;
}
```

**编辑功能**:
- **编辑模式**: 支持内联和对话框编辑
- **对话框**: 打开域选择器对话框
- **域更新**: 更新域表达式值
- **状态重置**: 重置验证和计数状态

## 使用场景

### 1. 域字段管理器

```javascript
// 域字段管理器
class DomainFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置域字段配置
        this.domainConfig = {
            enableValidation: true,
            enableRecordCount: true,
            enableSyntaxHighlight: true,
            enableAutoComplete: true,
            enableTemplates: true,
            enableHistory: true,
            maxHistorySize: 20,
            enableExport: true
        };
        
        // 设置域模板
        this.domainTemplates = new Map([
            ['active_records', "[('active', '=', True)]"],
            ['recent_records', "[('create_date', '>=', (context_today() - datetime.timedelta(days=30)).strftime('%Y-%m-%d'))]"],
            ['my_records', "[('create_uid', '=', uid)]"],
            ['draft_records', "[('state', '=', 'draft')]"],
            ['published_records', "[('state', '=', 'published')]"]
        ]);
        
        // 设置验证规则
        this.validationRules = {
            enableSyntaxCheck: true,
            enableFieldCheck: true,
            enableOperatorCheck: true,
            enableValueCheck: true,
            maxComplexity: 10
        };
        
        // 设置域历史
        this.domainHistory = [];
        
        // 设置域统计
        this.domainStatistics = {
            totalDomains: 0,
            validDomains: 0,
            invalidDomains: 0,
            averageComplexity: 0,
            templateUsage: new Map()
        };
        
        this.initializeDomainSystem();
    }
    
    // 初始化域系统
    initializeDomainSystem() {
        // 创建增强的域字段
        this.createEnhancedDomainField();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置模板系统
        this.setupTemplateSystem();
        
        // 设置历史管理
        this.setupHistoryManagement();
    }
    
    // 创建增强的域字段
    createEnhancedDomainField() {
        const originalField = DomainField;
        
        this.EnhancedDomainField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加模板功能
                this.addTemplateFeatures();
                
                // 添加历史功能
                this.addHistoryFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    ...this.state,
                    complexity: 0,
                    syntaxErrors: [],
                    suggestions: [],
                    isEditing: false,
                    selectedTemplate: null,
                    domainHistory: []
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的域验证
                this.enhancedValidate = async () => {
                    const startTime = performance.now();
                    
                    try {
                        // 执行基础验证
                        const isValid = await this.validate();
                        
                        // 语法检查
                        const syntaxErrors = this.checkSyntax();
                        this.enhancedState.syntaxErrors = syntaxErrors;
                        
                        // 复杂度分析
                        this.enhancedState.complexity = this.calculateComplexity();
                        
                        // 字段检查
                        const fieldErrors = await this.checkFields();
                        
                        // 记录统计
                        this.recordValidationStatistics(isValid);
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordValidationTime(endTime - startTime);
                        
                        return isValid && syntaxErrors.length === 0 && fieldErrors.length === 0;
                        
                    } catch (error) {
                        this.handleValidationError(error);
                        return false;
                    }
                };
                
                // 语法检查
                this.checkSyntax = () => {
                    const errors = [];
                    const domainStr = this.props.record.data[this.props.name];
                    
                    if (!domainStr) return errors;
                    
                    try {
                        // 检查括号匹配
                        if (!this.checkBrackets(domainStr)) {
                            errors.push('Unmatched brackets');
                        }
                        
                        // 检查引号匹配
                        if (!this.checkQuotes(domainStr)) {
                            errors.push('Unmatched quotes');
                        }
                        
                        // 检查基本语法
                        new Domain(domainStr);
                        
                    } catch (error) {
                        errors.push(`Syntax error: ${error.message}`);
                    }
                    
                    return errors;
                };
                
                // 检查括号匹配
                this.checkBrackets = (str) => {
                    const stack = [];
                    const pairs = { '(': ')', '[': ']', '{': '}' };
                    
                    for (const char of str) {
                        if (char in pairs) {
                            stack.push(char);
                        } else if (Object.values(pairs).includes(char)) {
                            const last = stack.pop();
                            if (pairs[last] !== char) {
                                return false;
                            }
                        }
                    }
                    
                    return stack.length === 0;
                };
                
                // 检查引号匹配
                this.checkQuotes = (str) => {
                    let singleQuotes = 0;
                    let doubleQuotes = 0;
                    
                    for (let i = 0; i < str.length; i++) {
                        if (str[i] === "'" && (i === 0 || str[i-1] !== '\\')) {
                            singleQuotes++;
                        } else if (str[i] === '"' && (i === 0 || str[i-1] !== '\\')) {
                            doubleQuotes++;
                        }
                    }
                    
                    return singleQuotes % 2 === 0 && doubleQuotes % 2 === 0;
                };
                
                // 计算复杂度
                this.calculateComplexity = () => {
                    const domainStr = this.props.record.data[this.props.name];
                    if (!domainStr) return 0;
                    
                    // 计算条件数量
                    const conditions = (domainStr.match(/['"]\w+['"],\s*['"][^'"]*['"],\s*[^,)]+/g) || []).length;
                    
                    // 计算逻辑操作符数量
                    const operators = (domainStr.match(/[&|!]/g) || []).length;
                    
                    // 计算嵌套层级
                    const maxDepth = this.calculateNestingDepth(domainStr);
                    
                    return conditions + operators * 2 + maxDepth;
                };
                
                // 计算嵌套深度
                this.calculateNestingDepth = (str) => {
                    let depth = 0;
                    let maxDepth = 0;
                    
                    for (const char of str) {
                        if (char === '[' || char === '(') {
                            depth++;
                            maxDepth = Math.max(maxDepth, depth);
                        } else if (char === ']' || char === ')') {
                            depth--;
                        }
                    }
                    
                    return maxDepth;
                };
                
                // 检查字段
                this.checkFields = async () => {
                    const errors = [];
                    
                    try {
                        const domain = this.domain;
                        const fields = this.extractFields(domain.toString());
                        
                        for (const field of fields) {
                            const exists = await this.checkFieldExists(field);
                            if (!exists) {
                                errors.push(`Field '${field}' does not exist`);
                            }
                        }
                    } catch (error) {
                        errors.push(`Field check error: ${error.message}`);
                    }
                    
                    return errors;
                };
                
                // 提取字段名
                this.extractFields = (domainStr) => {
                    const fieldPattern = /['"](\w+)['"],\s*['"][^'"]*['"],/g;
                    const fields = [];
                    let match;
                    
                    while ((match = fieldPattern.exec(domainStr)) !== null) {
                        fields.push(match[1]);
                    }
                    
                    return [...new Set(fields)]; // 去重
                };
                
                // 检查字段是否存在
                this.checkFieldExists = async (fieldName) => {
                    try {
                        const fields = await this.orm.call(this.resModel, 'fields_get', []);
                        return fieldName in fields;
                    } catch (error) {
                        return false;
                    }
                };
                
                // 应用模板
                this.applyTemplate = (templateName) => {
                    const template = this.domainTemplates.get(templateName);
                    if (template) {
                        this.updateDomain(template);
                        this.enhancedState.selectedTemplate = templateName;
                        
                        // 记录模板使用
                        const usage = this.domainStatistics.templateUsage.get(templateName) || 0;
                        this.domainStatistics.templateUsage.set(templateName, usage + 1);
                    }
                };
                
                // 获取建议
                this.getSuggestions = async () => {
                    const suggestions = [];
                    
                    try {
                        // 获取字段建议
                        const fields = await this.orm.call(this.resModel, 'fields_get', []);
                        const fieldNames = Object.keys(fields);
                        
                        suggestions.push(...fieldNames.map(name => ({
                            type: 'field',
                            value: name,
                            label: fields[name].string || name
                        })));
                        
                        // 添加操作符建议
                        const operators = ['=', '!=', '>', '<', '>=', '<=', 'in', 'not in', 'like', 'ilike'];
                        suggestions.push(...operators.map(op => ({
                            type: 'operator',
                            value: op,
                            label: op
                        })));
                        
                    } catch (error) {
                        console.error('Failed to get suggestions:', error);
                    }
                    
                    this.enhancedState.suggestions = suggestions;
                    return suggestions;
                };
                
                // 格式化域
                this.formatDomain = () => {
                    try {
                        const domain = this.domain;
                        const formatted = JSON.stringify(domain.toList(), null, 2);
                        this.updateDomain(formatted);
                    } catch (error) {
                        console.error('Failed to format domain:', error);
                    }
                };
                
                // 添加到历史
                this.addToHistory = (domain) => {
                    const historyEntry = {
                        domain: domain,
                        timestamp: Date.now(),
                        complexity: this.calculateComplexity()
                    };
                    
                    this.enhancedState.domainHistory.unshift(historyEntry);
                    this.domainHistory.unshift(historyEntry);
                    
                    // 限制历史大小
                    if (this.domainHistory.length > this.domainConfig.maxHistorySize) {
                        this.domainHistory.pop();
                    }
                    
                    if (this.enhancedState.domainHistory.length > 10) {
                        this.enhancedState.domainHistory.pop();
                    }
                };
                
                // 从历史恢复
                this.restoreFromHistory = (index) => {
                    const historyEntry = this.enhancedState.domainHistory[index];
                    if (historyEntry) {
                        this.updateDomain(historyEntry.domain);
                    }
                };
                
                // 导出域
                this.exportDomain = () => {
                    const domainData = {
                        domain: this.props.record.data[this.props.name],
                        resModel: this.resModel,
                        complexity: this.enhancedState.complexity,
                        isValid: this.state.isValid,
                        recordCount: this.state.recordCount,
                        timestamp: Date.now()
                    };
                    
                    return domainData;
                };
                
                // 记录验证统计
                this.recordValidationStatistics = (isValid) => {
                    this.domainStatistics.totalDomains++;
                    
                    if (isValid) {
                        this.domainStatistics.validDomains++;
                    } else {
                        this.domainStatistics.invalidDomains++;
                    }
                    
                    this.domainStatistics.averageComplexity = 
                        (this.domainStatistics.averageComplexity + this.enhancedState.complexity) / 2;
                };
                
                // 处理验证错误
                this.handleValidationError = (error) => {
                    console.error('Domain validation error:', error);
                };
                
                // 记录验证时间
                this.recordValidationTime = (duration) => {
                    console.log(`Domain validation took ${duration}ms`);
                };
            }
            
            addTemplateFeatures() {
                // 模板功能
                this.templateManager = {
                    enabled: this.domainConfig.enableTemplates,
                    templates: this.domainTemplates,
                    apply: (name) => this.applyTemplate(name)
                };
            }
            
            addHistoryFeatures() {
                // 历史功能
                this.historyManager = {
                    enabled: this.domainConfig.enableHistory,
                    maxSize: this.domainConfig.maxHistorySize,
                    add: (domain) => this.addToHistory(domain),
                    restore: (index) => this.restoreFromHistory(index)
                };
            }
            
            // 重写原始方法
            validate() {
                return this.enhancedValidate();
            }
            
            updateDomain(newDomain) {
                super.updateDomain(newDomain);
                this.addToHistory(newDomain);
            }
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.domainConfig.enableValidation,
            rules: this.validationRules,
            realTimeValidation: true
        };
    }
    
    // 设置模板系统
    setupTemplateSystem() {
        this.templateSystemConfig = {
            enabled: this.domainConfig.enableTemplates,
            templates: this.domainTemplates,
            enableCustomTemplates: true
        };
    }
    
    // 设置历史管理
    setupHistoryManagement() {
        this.historyManagementConfig = {
            enabled: this.domainConfig.enableHistory,
            maxSize: this.domainConfig.maxHistorySize,
            enablePersistence: false
        };
    }
    
    // 创建域字段
    createDomainField(props) {
        return new this.EnhancedDomainField(props);
    }
    
    // 注册域模板
    registerDomainTemplate(name, domain, description) {
        this.domainTemplates.set(name, domain);
    }
    
    // 批量验证域
    batchValidateDomains(fields) {
        const results = [];
        
        for (const field of fields) {
            try {
                const isValid = field.enhancedValidate();
                results.push({ field, isValid });
            } catch (error) {
                results.push({ field, isValid: false, error });
            }
        }
        
        return results;
    }
    
    // 获取域统计
    getDomainStatistics() {
        return {
            ...this.domainStatistics,
            templateCount: this.domainTemplates.size,
            historySize: this.domainHistory.length,
            validationRate: this.domainStatistics.validDomains / Math.max(this.domainStatistics.totalDomains, 1) * 100
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理模板和历史
        this.domainTemplates.clear();
        this.domainHistory = [];
        
        // 重置统计
        this.domainStatistics = {
            totalDomains: 0,
            validDomains: 0,
            invalidDomains: 0,
            averageComplexity: 0,
            templateUsage: new Map()
        };
    }
}

// 使用示例
const domainManager = new DomainFieldManager();

// 创建域字段
const domainField = domainManager.createDomainField({
    name: 'domain',
    record: {
        data: { domain: "[('active', '=', True)]" },
        fields: { domain: { type: 'char' } }
    },
    resModel: 'res.partner',
    editInDialog: true
});

// 注册自定义模板
domainManager.registerDomainTemplate('custom', "[('custom_field', '=', 'value')]", 'Custom domain template');

// 获取统计信息
const stats = domainManager.getDomainStatistics();
console.log('Domain field statistics:', stats);
```

## 技术特点

### 1. 功能完整
- **域选择器**: 集成完整的域选择器组件
- **验证机制**: 完善的域验证和错误检查
- **记录计数**: 实时计算匹配记录数量
- **对话框编辑**: 支持在对话框中编辑复杂域

### 2. 用户体验
- **可视化编辑**: 提供可视化的域编辑界面
- **语法检查**: 实时的语法检查和错误提示
- **自动完成**: 智能的字段和操作符建议
- **模板支持**: 预定义的域模板快速应用

### 3. 高级功能
- **复杂度分析**: 分析域表达式的复杂度
- **历史记录**: 保存域编辑历史
- **导入导出**: 支持域的导入导出
- **批量操作**: 支持批量域操作

### 4. 扩展性
- **插件架构**: 支持功能插件扩展
- **自定义验证**: 支持自定义验证规则
- **模板系统**: 可扩展的模板系统
- **钩子支持**: 丰富的钩子支持

## 设计模式

### 1. 组件模式 (Component Pattern)
- **UI组件**: 封装域编辑UI
- **功能组件**: 集成域选择器组件
- **状态管理**: 管理域编辑状态

### 2. 策略模式 (Strategy Pattern)
- **验证策略**: 不同的域验证策略
- **编辑策略**: 不同的编辑模式策略
- **显示策略**: 不同的显示策略

### 3. 观察者模式 (Observer Pattern)
- **记录观察**: 观察记录数据变化
- **状态观察**: 观察域状态变化
- **验证观察**: 观察验证状态变化

### 4. 工厂模式 (Factory Pattern)
- **域工厂**: 创建Domain对象
- **组件工厂**: 创建域字段组件
- **验证器工厂**: 创建验证器

## 注意事项

1. **性能考虑**: 避免频繁的域验证和记录计数
2. **用户体验**: 提供清晰的错误提示和帮助信息
3. **安全性**: 验证域表达式的安全性
4. **兼容性**: 确保域表达式的向后兼容性

## 扩展建议

1. **可视化构建**: 添加拖拽式域构建器
2. **智能建议**: 基于数据的智能域建议
3. **性能优化**: 优化大型域的处理性能
4. **协作功能**: 支持域的共享和协作编辑
5. **版本控制**: 添加域的版本控制功能

该域字段为Odoo Web客户端提供了强大的域表达式编辑功能，通过可视化界面和完善的验证机制确保了域表达式的正确性和易用性。

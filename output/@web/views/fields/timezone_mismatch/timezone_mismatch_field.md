# TimezoneMismatchField - 时区不匹配字段

## 概述

`timezone_mismatch_field.js` 是 Odoo Web 客户端的时区不匹配字段组件，负责检测和显示用户时区与浏览器时区的不匹配情况。该模块包含107行代码，是一个功能专门的时区管理组件，专门用于处理时区选择和不匹配警告，具备时区偏移检测、不匹配提示、时区格式化、自动检测等特性，是时区管理和时间一致性保障的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/timezone_mismatch/timezone_mismatch_field.js`
- **行数**: 107
- **模块**: `@web/views/fields/timezone_mismatch/timezone_mismatch_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/dates'                  // 日期本地化
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/views/fields/selection/selection_field' // 选择字段
```

## 核心功能

### 1. 组件定义

```javascript
const TimezoneMismatchField = class TimezoneMismatchField extends SelectionField {
    static template = "web.TimezoneMismatchField";
    static props = {
        ...super.props,
        tzOffsetField: { type: String, optional: true },
        mismatchTitle: { type: String, optional: true },
    };
    static defaultProps = {
        ...super.defaultProps,
        tzOffsetField: "tz_offset",
        mismatchTitle: _t(
            "Timezone Mismatch : This timezone is different from that of your browser.\nPlease, set the same timezone as your browser's to avoid time discrepancies in your system."
        ),
    };
}
```

**组件特性**:
- **继承选择**: 继承SelectionField的所有功能
- **时区偏移**: 支持tzOffsetField配置时区偏移字段
- **不匹配标题**: 支持mismatchTitle配置不匹配提示文本
- **默认字段**: 默认使用"tz_offset"作为偏移字段
- **默认提示**: 提供默认的不匹配警告文本

### 2. 时区不匹配检测

```javascript
get mismatch() {
    const userOffset = this.props.record.data[this.props.tzOffsetField];
    if (userOffset && this.props.record.data[this.props.name]) {
        const offset = -new Date().getTimezoneOffset();
        let browserOffset = offset < 0 ? "-" : "+";
        browserOffset += Math.abs(offset / 60)
            .toFixed(0)
            .padStart(2, "0");
        browserOffset += Math.abs(offset % 60)
            .toFixed(0)
            .padStart(2, "0");
        return browserOffset !== userOffset;
    } else if (!this.props.record.data[this.props.name]) {
        return true;
    }
    return false;
}
```

**检测功能**:
- **用户偏移**: 获取用户设置的时区偏移
- **浏览器偏移**: 计算浏览器的时区偏移
- **格式化偏移**: 将偏移格式化为±HHMM格式
- **比较检测**: 比较用户偏移和浏览器偏移
- **空值处理**: 处理时区未设置的情况

### 3. 不匹配标题

```javascript
get mismatchTitle() {
    if (!this.props.record.data[this.props.name]) {
        return _t("Set a timezone on your user");
    }
    return this.props.mismatchTitle;
}
```

**标题功能**:
- **未设置**: 时区未设置时的提示
- **不匹配**: 时区不匹配时的警告
- **动态标题**: 根据情况显示不同标题
- **本地化**: 支持多语言提示

### 4. 时区格式化

```javascript
get formattedTimezone() {
    const timezone = this.props.record.data[this.props.name];
    if (!timezone) {
        return _t("No timezone set");
    }
    
    const userOffset = this.props.record.data[this.props.tzOffsetField];
    if (userOffset) {
        return `${timezone} (${userOffset})`;
    }
    
    return timezone;
}

get browserTimezone() {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const offset = -new Date().getTimezoneOffset();
    let offsetString = offset < 0 ? "-" : "+";
    offsetString += Math.abs(offset / 60).toFixed(0).padStart(2, "0");
    offsetString += Math.abs(offset % 60).toFixed(0).padStart(2, "0");
    
    return `${timezone} (${offsetString})`;
}
```

**格式化功能**:
- **时区显示**: 格式化时区显示
- **偏移显示**: 显示时区偏移信息
- **浏览器时区**: 获取浏览器时区信息
- **标准格式**: 使用标准的时区格式

### 5. 字段注册

```javascript
const timezoneMismatchField = {
    ...selectionField,
    component: TimezoneMismatchField,
    extractProps: ({ attrs, options }, dynamicInfo) => ({
        ...selectionField.extractProps({ attrs, options }, dynamicInfo),
        tzOffsetField: attrs.tz_offset_field,
        mismatchTitle: attrs.mismatch_title,
    }),
};

registry.category("fields").add("timezone_mismatch", timezoneMismatchField);
```

**注册功能**:
- **配置继承**: 继承选择字段的所有配置
- **组件替换**: 使用时区不匹配字段组件
- **属性提取**: 提取时区偏移字段和不匹配标题
- **字段注册**: 注册为"timezone_mismatch"字段类型

## 使用场景

### 1. 时区不匹配字段管理器

```javascript
// 时区不匹配字段管理器
class TimezoneMismatchFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置时区不匹配字段配置
        this.timezoneMismatchConfig = {
            enableAutoDetection: true,
            enableWarningDisplay: true,
            enableAutoCorrection: false,
            enableTimezoneSync: false,
            enableNotifications: true,
            enableLogging: false,
            enableStatistics: true,
            warningThreshold: 0 // 0分钟差异就警告
        };
        
        // 设置时区数据
        this.timezoneData = {
            supportedTimezones: new Map(),
            commonTimezones: [
                'UTC',
                'America/New_York',
                'America/Los_Angeles',
                'Europe/London',
                'Europe/Paris',
                'Asia/Tokyo',
                'Asia/Shanghai',
                'Australia/Sydney'
            ],
            timezoneAliases: new Map([
                ['EST', 'America/New_York'],
                ['PST', 'America/Los_Angeles'],
                ['GMT', 'Europe/London'],
                ['CET', 'Europe/Paris'],
                ['JST', 'Asia/Tokyo'],
                ['CST', 'Asia/Shanghai']
            ])
        };
        
        // 设置检测规则
        this.detectionRules = {
            enableBrowserDetection: true,
            enableSystemDetection: false,
            enableGeoDetection: false,
            enableUserPreference: true,
            detectionPriority: ['user', 'browser', 'system', 'geo'],
            autoUpdateInterval: 60000 // 1分钟检查一次
        };
        
        // 设置警告配置
        this.warningConfig = {
            enablePopupWarning: true,
            enableInlineWarning: true,
            enablePersistentWarning: false,
            warningDuration: 5000, // 5秒
            maxWarningsPerSession: 3,
            warningCooldown: 300000 // 5分钟冷却
        };
        
        // 设置时区统计
        this.timezoneStatistics = {
            totalMismatchFields: 0,
            totalMismatches: 0,
            mismatchesByTimezone: new Map(),
            detectionCount: 0,
            correctionCount: 0,
            warningCount: 0,
            mostCommonMismatch: null
        };
        
        this.initializeTimezoneMismatchSystem();
    }
    
    // 初始化时区不匹配系统
    initializeTimezoneMismatchSystem() {
        // 创建增强的时区不匹配字段
        this.createEnhancedTimezoneMismatchField();
        
        // 设置检测系统
        this.setupDetectionSystem();
        
        // 设置警告系统
        this.setupWarningSystem();
        
        // 设置同步系统
        this.setupSyncSystem();
    }
    
    // 创建增强的时区不匹配字段
    createEnhancedTimezoneMismatchField() {
        const originalField = TimezoneMismatchField;
        
        this.EnhancedTimezoneMismatchField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加检测功能
                this.addDetectionFeatures();
                
                // 添加同步功能
                this.addSyncFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    lastDetectionTime: null,
                    detectionHistory: [],
                    warningShown: false,
                    warningCount: 0,
                    autoDetectionEnabled: true,
                    syncInProgress: false,
                    lastSyncTime: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的不匹配检测
                this.enhancedMismatchDetection = () => {
                    const userTimezone = this.props.record.data[this.props.name];
                    const userOffset = this.props.record.data[this.props.tzOffsetField];
                    
                    // 获取浏览器时区信息
                    const browserInfo = this.getBrowserTimezoneInfo();
                    
                    // 记录检测
                    this.recordDetection(userTimezone, userOffset, browserInfo);
                    
                    // 检测不匹配
                    const mismatch = this.detectMismatch(userTimezone, userOffset, browserInfo);
                    
                    // 处理不匹配
                    if (mismatch) {
                        this.handleMismatch(userTimezone, browserInfo);
                    }
                    
                    return mismatch;
                };
                
                // 获取浏览器时区信息
                this.getBrowserTimezoneInfo = () => {
                    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
                    const offset = -new Date().getTimezoneOffset();
                    
                    let offsetString = offset < 0 ? "-" : "+";
                    offsetString += Math.abs(offset / 60).toFixed(0).padStart(2, "0");
                    offsetString += Math.abs(offset % 60).toFixed(0).padStart(2, "0");
                    
                    return {
                        timezone: timezone,
                        offset: offsetString,
                        offsetMinutes: offset,
                        isDST: this.isDaylightSavingTime()
                    };
                };
                
                // 检测夏令时
                this.isDaylightSavingTime = () => {
                    const now = new Date();
                    const january = new Date(now.getFullYear(), 0, 1);
                    const july = new Date(now.getFullYear(), 6, 1);
                    
                    return Math.max(january.getTimezoneOffset(), july.getTimezoneOffset()) !== now.getTimezoneOffset();
                };
                
                // 检测不匹配
                this.detectMismatch = (userTimezone, userOffset, browserInfo) => {
                    // 如果用户没有设置时区
                    if (!userTimezone) {
                        return true;
                    }
                    
                    // 如果有偏移信息，比较偏移
                    if (userOffset && browserInfo.offset) {
                        return userOffset !== browserInfo.offset;
                    }
                    
                    // 比较时区名称
                    if (userTimezone !== browserInfo.timezone) {
                        // 检查是否为别名
                        const alias = this.timezoneData.timezoneAliases.get(userTimezone);
                        if (alias && alias === browserInfo.timezone) {
                            return false;
                        }
                        return true;
                    }
                    
                    return false;
                };
                
                // 处理不匹配
                this.handleMismatch = (userTimezone, browserInfo) => {
                    // 记录不匹配
                    this.recordMismatch(userTimezone, browserInfo);
                    
                    // 显示警告
                    if (this.timezoneMismatchConfig.enableWarningDisplay) {
                        this.showMismatchWarning(userTimezone, browserInfo);
                    }
                    
                    // 自动纠正
                    if (this.timezoneMismatchConfig.enableAutoCorrection) {
                        this.autoCorrectTimezone(browserInfo);
                    }
                    
                    // 发送通知
                    if (this.timezoneMismatchConfig.enableNotifications) {
                        this.sendMismatchNotification(userTimezone, browserInfo);
                    }
                };
                
                // 显示不匹配警告
                this.showMismatchWarning = (userTimezone, browserInfo) => {
                    // 检查警告冷却
                    if (this.isWarningCooldown()) {
                        return;
                    }
                    
                    // 检查最大警告次数
                    if (this.enhancedState.warningCount >= this.warningConfig.maxWarningsPerSession) {
                        return;
                    }
                    
                    const message = this.buildWarningMessage(userTimezone, browserInfo);
                    
                    if (this.warningConfig.enablePopupWarning) {
                        this.showPopupWarning(message);
                    }
                    
                    if (this.warningConfig.enableInlineWarning) {
                        this.showInlineWarning(message);
                    }
                    
                    this.enhancedState.warningShown = true;
                    this.enhancedState.warningCount++;
                    this.timezoneStatistics.warningCount++;
                };
                
                // 构建警告消息
                this.buildWarningMessage = (userTimezone, browserInfo) => {
                    if (!userTimezone) {
                        return _t("No timezone is set. Your browser timezone is %s.", browserInfo.timezone);
                    }
                    
                    return _t(
                        "Timezone mismatch detected!\nUser timezone: %s\nBrowser timezone: %s\nThis may cause time discrepancies.",
                        userTimezone,
                        browserInfo.timezone
                    );
                };
                
                // 显示弹出警告
                this.showPopupWarning = (message) => {
                    // 实现弹出警告逻辑
                    console.warn('Timezone mismatch:', message);
                };
                
                // 显示内联警告
                this.showInlineWarning = (message) => {
                    // 实现内联警告逻辑
                    this.enhancedState.inlineWarning = message;
                };
                
                // 检查警告冷却
                this.isWarningCooldown = () => {
                    const lastWarning = this.enhancedState.lastWarningTime;
                    if (!lastWarning) {
                        return false;
                    }
                    
                    const now = Date.now();
                    return (now - lastWarning) < this.warningConfig.warningCooldown;
                };
                
                // 自动纠正时区
                this.autoCorrectTimezone = async (browserInfo) => {
                    try {
                        this.enhancedState.syncInProgress = true;
                        
                        // 更新用户时区
                        await this.props.record.update({
                            [this.props.name]: browserInfo.timezone,
                            [this.props.tzOffsetField]: browserInfo.offset
                        });
                        
                        // 保存记录
                        await this.props.record.save();
                        
                        this.enhancedState.lastSyncTime = new Date();
                        this.timezoneStatistics.correctionCount++;
                        
                        // 发送成功通知
                        this.sendCorrectionNotification(browserInfo.timezone);
                        
                    } catch (error) {
                        console.error('Auto-correction failed:', error);
                        this.sendErrorNotification(error.message);
                    } finally {
                        this.enhancedState.syncInProgress = false;
                    }
                };
                
                // 发送不匹配通知
                this.sendMismatchNotification = (userTimezone, browserInfo) => {
                    const message = this.buildWarningMessage(userTimezone, browserInfo);
                    console.log('Timezone mismatch notification:', message);
                };
                
                // 发送纠正通知
                this.sendCorrectionNotification = (newTimezone) => {
                    const message = _t("Timezone automatically corrected to %s", newTimezone);
                    console.log('Timezone correction notification:', message);
                };
                
                // 发送错误通知
                this.sendErrorNotification = (error) => {
                    const message = _t("Failed to correct timezone: %s", error);
                    console.error('Timezone error notification:', message);
                };
                
                // 获取时区信息
                this.getTimezoneInfo = () => {
                    const userTimezone = this.props.record.data[this.props.name];
                    const userOffset = this.props.record.data[this.props.tzOffsetField];
                    const browserInfo = this.getBrowserTimezoneInfo();
                    const mismatch = this.enhancedMismatchDetection();
                    
                    return {
                        userTimezone: userTimezone,
                        userOffset: userOffset,
                        browserTimezone: browserInfo.timezone,
                        browserOffset: browserInfo.offset,
                        hasMismatch: mismatch,
                        warningShown: this.enhancedState.warningShown,
                        warningCount: this.enhancedState.warningCount,
                        syncInProgress: this.enhancedState.syncInProgress,
                        lastSyncTime: this.enhancedState.lastSyncTime,
                        detectionHistory: this.enhancedState.detectionHistory.slice(-5)
                    };
                };
                
                // 记录检测
                this.recordDetection = (userTimezone, userOffset, browserInfo) => {
                    this.enhancedState.lastDetectionTime = new Date();
                    this.enhancedState.detectionHistory.unshift({
                        timestamp: new Date(),
                        userTimezone: userTimezone,
                        userOffset: userOffset,
                        browserTimezone: browserInfo.timezone,
                        browserOffset: browserInfo.offset
                    });
                    
                    // 限制历史大小
                    if (this.enhancedState.detectionHistory.length > 20) {
                        this.enhancedState.detectionHistory.pop();
                    }
                    
                    this.timezoneStatistics.detectionCount++;
                };
                
                // 记录不匹配
                this.recordMismatch = (userTimezone, browserInfo) => {
                    this.timezoneStatistics.totalMismatches++;
                    
                    const mismatchKey = `${userTimezone || 'none'}->${browserInfo.timezone}`;
                    const count = this.timezoneStatistics.mismatchesByTimezone.get(mismatchKey) || 0;
                    this.timezoneStatistics.mismatchesByTimezone.set(mismatchKey, count + 1);
                    
                    this.updateMostCommonMismatch();
                };
                
                // 更新最常见不匹配
                this.updateMostCommonMismatch = () => {
                    let maxCount = 0;
                    let mostCommon = null;
                    
                    for (const [mismatch, count] of this.timezoneStatistics.mismatchesByTimezone.entries()) {
                        if (count > maxCount) {
                            maxCount = count;
                            mostCommon = mismatch;
                        }
                    }
                    
                    this.timezoneStatistics.mostCommonMismatch = mostCommon;
                };
            }
            
            addDetectionFeatures() {
                // 检测功能
                this.detectionManager = {
                    enabled: this.timezoneMismatchConfig.enableAutoDetection,
                    detect: () => this.enhancedMismatchDetection(),
                    getBrowserInfo: () => this.getBrowserTimezoneInfo(),
                    getInfo: () => this.getTimezoneInfo()
                };
            }
            
            addSyncFeatures() {
                // 同步功能
                this.syncManager = {
                    enabled: this.timezoneMismatchConfig.enableTimezoneSync,
                    autoCorrect: (browserInfo) => this.autoCorrectTimezone(browserInfo),
                    isInProgress: () => this.enhancedState.syncInProgress,
                    getLastSync: () => this.enhancedState.lastSyncTime
                };
            }
            
            // 重写原始方法
            get mismatch() {
                return this.enhancedMismatchDetection();
            }
        };
    }
    
    // 设置检测系统
    setupDetectionSystem() {
        this.detectionSystemConfig = {
            enabled: this.timezoneMismatchConfig.enableAutoDetection,
            rules: this.detectionRules
        };
    }
    
    // 设置警告系统
    setupWarningSystem() {
        this.warningSystemConfig = {
            enabled: this.timezoneMismatchConfig.enableWarningDisplay,
            config: this.warningConfig
        };
    }
    
    // 设置同步系统
    setupSyncSystem() {
        this.syncSystemConfig = {
            enabled: this.timezoneMismatchConfig.enableTimezoneSync,
            autoCorrection: this.timezoneMismatchConfig.enableAutoCorrection
        };
    }
    
    // 创建时区不匹配字段
    createTimezoneMismatchField(props) {
        const field = new this.EnhancedTimezoneMismatchField(props);
        this.timezoneStatistics.totalMismatchFields++;
        return field;
    }
    
    // 注册时区别名
    registerTimezoneAlias(alias, timezone) {
        this.timezoneData.timezoneAliases.set(alias, timezone);
    }
    
    // 获取时区统计
    getTimezoneStatistics() {
        return {
            ...this.timezoneStatistics,
            mismatchRate: (this.timezoneStatistics.totalMismatches / Math.max(this.timezoneStatistics.detectionCount, 1)) * 100,
            correctionRate: (this.timezoneStatistics.correctionCount / Math.max(this.timezoneStatistics.totalMismatches, 1)) * 100,
            warningRate: (this.timezoneStatistics.warningCount / Math.max(this.timezoneStatistics.totalMismatches, 1)) * 100,
            supportedTimezoneCount: this.timezoneData.supportedTimezones.size,
            aliasCount: this.timezoneData.timezoneAliases.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理时区数据
        this.timezoneData.supportedTimezones.clear();
        this.timezoneData.timezoneAliases.clear();
        
        // 清理统计
        this.timezoneStatistics.mismatchesByTimezone.clear();
        
        // 重置统计
        this.timezoneStatistics = {
            totalMismatchFields: 0,
            totalMismatches: 0,
            mismatchesByTimezone: new Map(),
            detectionCount: 0,
            correctionCount: 0,
            warningCount: 0,
            mostCommonMismatch: null
        };
    }
}

// 使用示例
const timezoneMismatchManager = new TimezoneMismatchFieldManager();

// 创建时区不匹配字段
const timezoneMismatchField = timezoneMismatchManager.createTimezoneMismatchField({
    name: 'tz',
    record: {
        data: { 
            tz: 'America/New_York',
            tz_offset: '-0500'
        },
        fields: { 
            tz: { 
                type: 'selection',
                selection: [
                    ['UTC', 'UTC'],
                    ['America/New_York', 'Eastern Time'],
                    ['America/Los_Angeles', 'Pacific Time'],
                    ['Europe/London', 'London Time']
                ]
            }
        }
    },
    tzOffsetField: 'tz_offset'
});

// 注册时区别名
timezoneMismatchManager.registerTimezoneAlias('EST', 'America/New_York');
timezoneMismatchManager.registerTimezoneAlias('PST', 'America/Los_Angeles');

// 获取统计信息
const stats = timezoneMismatchManager.getTimezoneStatistics();
console.log('Timezone mismatch field statistics:', stats);
```

## 技术特点

### 1. 时区检测
- **自动检测**: 自动检测浏览器时区
- **偏移计算**: 精确计算时区偏移
- **格式化**: 标准化时区偏移格式
- **比较算法**: 智能比较用户和浏览器时区

### 2. 不匹配处理
- **实时检测**: 实时检测时区不匹配
- **警告显示**: 显示不匹配警告
- **自动纠正**: 可选的自动时区纠正
- **用户提示**: 友好的用户提示信息

### 3. 继承设计
- **选择字段**: 继承选择字段的所有功能
- **扩展属性**: 扩展时区相关属性
- **模板重用**: 重用选择字段的模板结构
- **功能增强**: 在基础功能上增强时区处理

### 4. 国际化支持
- **多语言**: 支持多语言警告信息
- **时区名称**: 支持本地化时区名称
- **格式化**: 本地化的时间格式
- **用户友好**: 提供用户友好的界面

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 为选择字段添加时区检测功能
- **警告装饰**: 装饰不匹配警告显示
- **检测装饰**: 装饰时区检测逻辑

### 2. 策略模式 (Strategy Pattern)
- **检测策略**: 不同的时区检测策略
- **警告策略**: 不同的警告显示策略
- **纠正策略**: 不同的时区纠正策略

### 3. 观察者模式 (Observer Pattern)
- **时区观察**: 观察时区变化
- **不匹配观察**: 观察不匹配状态
- **浏览器观察**: 观察浏览器时区变化

### 4. 适配器模式 (Adapter Pattern)
- **时区适配**: 适配不同的时区格式
- **偏移适配**: 适配不同的偏移格式
- **接口适配**: 适配选择字段接口

## 注意事项

1. **时区精度**: 确保时区检测的精确性
2. **性能考虑**: 避免频繁的时区检测
3. **用户体验**: 提供清晰的不匹配提示
4. **数据一致性**: 保持时区数据的一致性

## 扩展建议

1. **智能建议**: 提供智能的时区建议
2. **历史记录**: 记录时区变更历史
3. **批量更新**: 支持批量时区更新
4. **地理定位**: 基于地理位置的时区检测
5. **同步机制**: 增强时区同步机制

该时区不匹配字段为Odoo Web客户端提供了完整的时区管理和不匹配检测功能，通过智能检测和友好提示确保了时间数据的一致性和用户体验。

# BadgeField - 徽章字段

## 概述

`badge_field.js` 是 Odoo Web 客户端的徽章字段组件，负责以徽章形式显示字段值。该模块包含52行代码，是一个装饰性显示组件，专门用于以彩色徽章的形式展示选择、关系或文本字段的值，具备动态装饰、条件样式、格式化显示、表达式评估等特性，是状态显示和视觉标识的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/badge/badge_field.js`
- **行数**: 52
- **模块**: `@web/views/fields/badge/badge_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'            // 翻译服务
'@web/core/py_js/py'                    // Python表达式评估
'@web/core/registry'                    // 注册表
'@web/views/fields/standard_field_props' // 标准字段属性
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const BadgeField = class BadgeField extends Component {
    static template = "web.BadgeField";
    static props = {
        ...standardFieldProps,
        decorations: { type: Object, optional: true },
    };
    static defaultProps = {
        decorations: {},
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **装饰配置**: 支持装饰样式配置
- **默认装饰**: 提供空的默认装饰对象
- **专用模板**: 使用BadgeField专用模板

### 2. 值格式化

```javascript
get formattedValue() {
    const formatter = formatters.get(this.props.record.fields[this.props.name].type);
    return formatter(this.props.record.data[this.props.name], {
        selection: this.props.record.fields[this.props.name].selection,
    });
}
```

**格式化功能**:
- **类型识别**: 根据字段类型获取对应格式化器
- **选择支持**: 特别支持选择字段的格式化
- **动态格式化**: 动态格式化字段值
- **选项传递**: 传递选择选项给格式化器

### 3. 装饰样式

```javascript
get classFromDecoration() {
    const evalContext = this.props.record.evalContextWithVirtualIds;
    for (const decorationName in this.props.decorations) {
        if (evaluateBooleanExpr(this.props.decorations[decorationName], evalContext)) {
            return `text-bg-${decorationName}`;
        }
    }
    return "";
}
```

**装饰功能**:
- **表达式评估**: 评估装饰条件表达式
- **动态样式**: 根据条件动态应用样式
- **Bootstrap类**: 生成Bootstrap徽章样式类
- **条件匹配**: 按顺序匹配第一个满足条件的装饰

### 4. 字段注册

```javascript
const badgeField = {
    component: BadgeField,
    displayName: _t("Badge"),
    supportedTypes: ["selection", "many2one", "char"],
    extractProps: ({ decorations }) => {
        return { decorations };
    },
};

registry.category("fields").add("badge", badgeField);
```

**注册功能**:
- **组件注册**: 注册徽章字段组件
- **显示名称**: 国际化的显示名称
- **类型支持**: 支持选择、关系和字符字段
- **属性提取**: 提取装饰属性

## 使用场景

### 1. 徽章字段管理器

```javascript
// 徽章字段管理器
class BadgeFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置徽章配置
        this.badgeConfig = {
            enableAnimations: true,
            enableTooltips: true,
            enableCustomColors: true,
            enableGradients: false,
            defaultSize: 'medium',
            defaultShape: 'rounded',
            enableIcons: true,
            enableCounters: true
        };
        
        // 设置装饰样式
        this.decorationStyles = new Map([
            ['success', { color: '#28a745', background: '#d4edda' }],
            ['warning', { color: '#ffc107', background: '#fff3cd' }],
            ['danger', { color: '#dc3545', background: '#f8d7da' }],
            ['info', { color: '#17a2b8', background: '#d1ecf1' }],
            ['primary', { color: '#007bff', background: '#cce5ff' }],
            ['secondary', { color: '#6c757d', background: '#e2e3e5' }],
            ['light', { color: '#f8f9fa', background: '#fdfdfe' }],
            ['dark', { color: '#343a40', background: '#d6d8db' }]
        ]);
        
        // 设置徽章主题
        this.badgeThemes = new Map([
            ['default', 'badge-default'],
            ['outline', 'badge-outline'],
            ['soft', 'badge-soft'],
            ['gradient', 'badge-gradient']
        ]);
        
        // 设置徽章统计
        this.badgeStatistics = {
            totalBadges: 0,
            renderedBadges: 0,
            decorationMatches: 0,
            averageRenderTime: 0
        };
        
        this.initializeBadgeSystem();
    }
    
    // 初始化徽章系统
    initializeBadgeSystem() {
        // 创建增强的徽章字段
        this.createEnhancedBadgeField();
        
        // 设置装饰系统
        this.setupDecorationSystem();
        
        // 设置主题系统
        this.setupThemeSystem();
        
        // 设置动画系统
        this.setupAnimationSystem();
    }
    
    // 创建增强的徽章字段
    createEnhancedBadgeField() {
        const originalField = BadgeField;
        
        this.EnhancedBadgeField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加交互功能
                this.addInteractionFeatures();
                
                // 添加动画功能
                this.addAnimationFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isHovered: false,
                    isClicked: false,
                    animationClass: '',
                    customStyle: {},
                    tooltipText: '',
                    badgeSize: this.badgeConfig.defaultSize,
                    badgeTheme: 'default'
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的格式化值
                this.enhancedFormattedValue = () => {
                    const startTime = performance.now();
                    
                    try {
                        // 执行原始格式化
                        const formattedValue = this.formattedValue;
                        
                        // 应用自定义格式化
                        const enhancedValue = this.applyCustomFormatting(formattedValue);
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordRenderTime(endTime - startTime);
                        
                        return enhancedValue;
                        
                    } catch (error) {
                        this.handleFormattingError(error);
                        return this.props.record.data[this.props.name] || '';
                    }
                };
                
                // 应用自定义格式化
                this.applyCustomFormatting = (value) => {
                    // 添加图标
                    if (this.badgeConfig.enableIcons) {
                        const icon = this.getIconForValue(value);
                        if (icon) {
                            value = `${icon} ${value}`;
                        }
                    }
                    
                    // 添加计数器
                    if (this.badgeConfig.enableCounters) {
                        const count = this.getCountForValue(value);
                        if (count > 0) {
                            value = `${value} (${count})`;
                        }
                    }
                    
                    return value;
                };
                
                // 获取值对应的图标
                this.getIconForValue = (value) => {
                    const iconMap = {
                        'draft': '📝',
                        'confirmed': '✅',
                        'cancelled': '❌',
                        'done': '🎉',
                        'pending': '⏳',
                        'approved': '👍',
                        'rejected': '👎'
                    };
                    
                    return iconMap[value?.toLowerCase()] || '';
                };
                
                // 获取值对应的计数
                this.getCountForValue = (value) => {
                    // 实现计数逻辑
                    return 0;
                };
                
                // 增强的装饰类
                this.enhancedClassFromDecoration = () => {
                    const startTime = performance.now();
                    
                    try {
                        // 执行原始装饰类获取
                        const baseClass = this.classFromDecoration;
                        
                        // 添加增强类
                        const enhancedClasses = [baseClass];
                        
                        // 添加大小类
                        enhancedClasses.push(`badge-${this.enhancedState.badgeSize}`);
                        
                        // 添加主题类
                        const themeClass = this.badgeThemes.get(this.enhancedState.badgeTheme);
                        if (themeClass) {
                            enhancedClasses.push(themeClass);
                        }
                        
                        // 添加交互类
                        if (this.enhancedState.isHovered) {
                            enhancedClasses.push('badge-hovered');
                        }
                        
                        if (this.enhancedState.isClicked) {
                            enhancedClasses.push('badge-clicked');
                        }
                        
                        // 添加动画类
                        if (this.enhancedState.animationClass) {
                            enhancedClasses.push(this.enhancedState.animationClass);
                        }
                        
                        // 记录装饰匹配
                        if (baseClass) {
                            this.badgeStatistics.decorationMatches++;
                        }
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordRenderTime(endTime - startTime);
                        
                        return enhancedClasses.filter(Boolean).join(' ');
                        
                    } catch (error) {
                        this.handleDecorationError(error);
                        return 'badge-default';
                    }
                };
                
                // 获取自定义样式
                this.getCustomStyle = () => {
                    const style = { ...this.enhancedState.customStyle };
                    
                    // 应用自定义颜色
                    if (this.badgeConfig.enableCustomColors) {
                        const customColor = this.getCustomColorForValue();
                        if (customColor) {
                            style.backgroundColor = customColor.background;
                            style.color = customColor.color;
                        }
                    }
                    
                    // 应用渐变
                    if (this.badgeConfig.enableGradients && this.enhancedState.badgeTheme === 'gradient') {
                        style.background = this.getGradientForValue();
                    }
                    
                    return style;
                };
                
                // 获取自定义颜色
                this.getCustomColorForValue = () => {
                    const value = this.props.record.data[this.props.name];
                    
                    // 基于值生成颜色
                    if (typeof value === 'string') {
                        const hash = this.hashCode(value);
                        const hue = Math.abs(hash) % 360;
                        return {
                            background: `hsl(${hue}, 70%, 90%)`,
                            color: `hsl(${hue}, 70%, 30%)`
                        };
                    }
                    
                    return null;
                };
                
                // 获取渐变
                this.getGradientForValue = () => {
                    const value = this.props.record.data[this.props.name];
                    const hash = this.hashCode(String(value));
                    const hue1 = Math.abs(hash) % 360;
                    const hue2 = (hue1 + 60) % 360;
                    
                    return `linear-gradient(45deg, hsl(${hue1}, 70%, 80%), hsl(${hue2}, 70%, 80%))`;
                };
                
                // 哈希函数
                this.hashCode = (str) => {
                    let hash = 0;
                    for (let i = 0; i < str.length; i++) {
                        const char = str.charCodeAt(i);
                        hash = ((hash << 5) - hash) + char;
                        hash = hash & hash; // 转换为32位整数
                    }
                    return hash;
                };
                
                // 设置徽章大小
                this.setBadgeSize = (size) => {
                    this.enhancedState.badgeSize = size;
                };
                
                // 设置徽章主题
                this.setBadgeTheme = (theme) => {
                    this.enhancedState.badgeTheme = theme;
                };
                
                // 播放动画
                this.playAnimation = (animationType) => {
                    this.enhancedState.animationClass = `badge-${animationType}`;
                    
                    // 动画结束后清除类
                    setTimeout(() => {
                        this.enhancedState.animationClass = '';
                    }, 1000);
                };
                
                // 设置工具提示
                this.setTooltip = (text) => {
                    this.enhancedState.tooltipText = text;
                };
                
                // 处理鼠标悬停
                this.onMouseEnter = () => {
                    this.enhancedState.isHovered = true;
                    
                    if (this.badgeConfig.enableAnimations) {
                        this.playAnimation('hover');
                    }
                };
                
                this.onMouseLeave = () => {
                    this.enhancedState.isHovered = false;
                };
                
                // 处理点击
                this.onClick = () => {
                    this.enhancedState.isClicked = true;
                    
                    if (this.badgeConfig.enableAnimations) {
                        this.playAnimation('click');
                    }
                    
                    // 重置点击状态
                    setTimeout(() => {
                        this.enhancedState.isClicked = false;
                    }, 200);
                };
                
                // 错误处理
                this.handleFormattingError = (error) => {
                    console.error('Badge formatting error:', error);
                };
                
                this.handleDecorationError = (error) => {
                    console.error('Badge decoration error:', error);
                };
                
                // 记录渲染时间
                this.recordRenderTime = (duration) => {
                    this.badgeStatistics.renderedBadges++;
                    this.badgeStatistics.averageRenderTime = 
                        (this.badgeStatistics.averageRenderTime * (this.badgeStatistics.renderedBadges - 1) + duration) / 
                        this.badgeStatistics.renderedBadges;
                };
            }
            
            addInteractionFeatures() {
                // 交互功能
                this.interactionManager = {
                    enableHover: true,
                    enableClick: true,
                    enableTooltip: this.badgeConfig.enableTooltips
                };
            }
            
            addAnimationFeatures() {
                // 动画功能
                this.animationManager = {
                    enabled: this.badgeConfig.enableAnimations,
                    duration: 300,
                    easing: 'ease-in-out'
                };
            }
            
            // 重写原始方法
            get formattedValue() {
                return this.enhancedFormattedValue();
            }
            
            get classFromDecoration() {
                return this.enhancedClassFromDecoration();
            }
        };
    }
    
    // 设置装饰系统
    setupDecorationSystem() {
        this.decorationConfig = {
            enableConditionalDecorations: true,
            enableCustomDecorations: true,
            enableNestedConditions: true
        };
    }
    
    // 设置主题系统
    setupThemeSystem() {
        this.themeConfig = {
            enableCustomThemes: true,
            enableThemeInheritance: true,
            defaultTheme: 'default'
        };
    }
    
    // 设置动画系统
    setupAnimationSystem() {
        this.animationConfig = {
            enableHoverAnimations: true,
            enableClickAnimations: true,
            enableTransitions: true
        };
    }
    
    // 创建徽章字段
    createBadgeField(props) {
        return new this.EnhancedBadgeField(props);
    }
    
    // 注册自定义装饰
    registerCustomDecoration(name, condition, style) {
        this.decorationStyles.set(name, style);
    }
    
    // 注册自定义主题
    registerCustomTheme(name, className) {
        this.badgeThemes.set(name, className);
    }
    
    // 获取徽章统计
    getBadgeStatistics() {
        return {
            ...this.badgeStatistics,
            decorationCount: this.decorationStyles.size,
            themeCount: this.badgeThemes.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理样式
        this.decorationStyles.clear();
        this.badgeThemes.clear();
        
        // 重置统计
        this.badgeStatistics = {
            totalBadges: 0,
            renderedBadges: 0,
            decorationMatches: 0,
            averageRenderTime: 0
        };
    }
}

// 使用示例
const badgeManager = new BadgeFieldManager();

// 创建徽章字段
const badgeField = badgeManager.createBadgeField({
    name: 'state',
    record: { 
        data: { state: 'confirmed' },
        fields: { state: { type: 'selection', selection: [['draft', 'Draft'], ['confirmed', 'Confirmed']] } }
    },
    decorations: {
        success: "state == 'confirmed'",
        warning: "state == 'draft'"
    }
});

// 注册自定义装饰
badgeManager.registerCustomDecoration('custom', "state == 'custom'", {
    color: '#ff6b6b',
    background: '#ffe0e0'
});

// 获取统计信息
const stats = badgeManager.getBadgeStatistics();
console.log('Badge field statistics:', stats);
```

## 技术特点

### 1. 装饰驱动
- **条件装饰**: 基于条件表达式的装饰
- **动态样式**: 动态应用装饰样式
- **表达式评估**: 使用Python表达式评估
- **优先级**: 按顺序匹配装饰条件

### 2. 多类型支持
- **选择字段**: 支持选择字段显示
- **关系字段**: 支持many2one字段显示
- **文本字段**: 支持字符字段显示
- **格式化**: 自动格式化不同类型的值

### 3. 视觉效果
- **Bootstrap样式**: 使用Bootstrap徽章样式
- **颜色丰富**: 支持多种颜色主题
- **响应式**: 响应式设计
- **美观大方**: 美观的视觉效果

### 4. 配置灵活
- **装饰配置**: 灵活的装饰配置
- **属性提取**: 智能的属性提取
- **默认值**: 合理的默认配置
- **扩展性**: 易于扩展功能

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- **样式装饰**: 装饰字段显示样式
- **条件装饰**: 基于条件的装饰
- **动态装饰**: 动态应用装饰

### 2. 策略模式 (Strategy Pattern)
- **格式化策略**: 不同类型的格式化策略
- **装饰策略**: 不同的装饰应用策略
- **显示策略**: 不同的显示策略

### 3. 模板方法模式 (Template Method Pattern)
- **渲染模板**: 标准的渲染流程
- **格式化模板**: 标准的格式化流程
- **装饰模板**: 标准的装饰应用流程

### 4. 观察者模式 (Observer Pattern)
- **数据观察**: 观察字段数据变化
- **状态观察**: 观察记录状态变化
- **条件观察**: 观察装饰条件变化

## 注意事项

1. **性能考虑**: 避免复杂的装饰表达式
2. **样式一致性**: 保持装饰样式的一致性
3. **可读性**: 确保徽章内容的可读性
4. **响应式**: 确保在不同设备上的显示效果

## 扩展建议

1. **动画效果**: 添加徽章动画效果
2. **交互功能**: 添加点击和悬停交互
3. **自定义样式**: 支持更多自定义样式
4. **图标支持**: 添加图标显示功能
5. **工具提示**: 添加工具提示功能

该徽章字段为Odoo Web客户端提供了美观的状态显示功能，通过条件装饰和动态样式确保了良好的视觉效果和用户体验。

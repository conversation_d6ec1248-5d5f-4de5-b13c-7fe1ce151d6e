# JsonField - JSON字段

## 概述

`jsonb.js` 是 Odoo Web 客户端的JSON字段组件，负责处理JSONB类型数据的显示和格式化。该模块包含30行代码，是一个简洁的JSON数据显示组件，专门用于处理jsonb类型的字段，具备JSON序列化、格式化显示、标准属性支持等特性，是JSON数据处理的基础组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/jsonb/jsonb.js`
- **行数**: 30
- **模块**: `@web/views/fields/jsonb/jsonb`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/views/fields/standard_field_props' // 标准字段属性
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const JsonField = class JsonField extends Component {
    static template = "web.JsonbField";
    static props = {
        ...standardFieldProps,
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **专用模板**: 使用JsonbField专用模板
- **简洁设计**: 最简化的组件实现
- **JSON专用**: 专门处理JSON数据

### 2. 格式化显示

```javascript
get formattedValue() {
    const value = this.props.record.data[this.props.name];
    return value ? JSON.stringify(value) : "";
}
```

**格式化功能**:
- **JSON序列化**: 使用JSON.stringify序列化数据
- **空值处理**: 处理空值返回空字符串
- **数据获取**: 从记录数据中获取JSON值
- **字符串转换**: 将JSON对象转换为字符串显示

### 3. 字段注册

```javascript
const jsonField = {
    component: JsonField,
    displayName: _t("Json"),
    supportedTypes: ["jsonb"],
};

registry.category("fields").add("jsonb", jsonField);
```

**注册功能**:
- **组件注册**: 注册JSON字段组件
- **显示名称**: 设置为"Json"
- **类型支持**: 仅支持jsonb类型
- **字段注册**: 注册为jsonb字段类型

## 使用场景

### 1. JSON字段管理器

```javascript
// JSON字段管理器
class JsonFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置JSON字段配置
        this.jsonConfig = {
            enableValidation: true,
            enableFormatting: true,
            enableEditing: false,
            enableSyntaxHighlight: true,
            enableCollapse: true,
            enableSearch: true,
            enableExport: true,
            enableImport: false
        };
        
        // 设置JSON验证规则
        this.validationRules = {
            enableSyntaxValidation: true,
            enableSchemaValidation: false,
            enableTypeValidation: true,
            maxDepth: 10,
            maxSize: 1048576, // 1MB
            allowedTypes: ['object', 'array', 'string', 'number', 'boolean', 'null']
        };
        
        // 设置格式化选项
        this.formatOptions = {
            indent: 2,
            enablePrettyPrint: true,
            enableMinify: false,
            enableColorSyntax: true,
            maxDisplayLength: 1000
        };
        
        // 设置JSON模式
        this.jsonSchemas = new Map([
            ['default', { type: 'object' }],
            ['array', { type: 'array' }],
            ['config', { 
                type: 'object',
                properties: {
                    name: { type: 'string' },
                    value: { type: ['string', 'number', 'boolean'] },
                    enabled: { type: 'boolean' }
                }
            }]
        ]);
        
        // 设置JSON统计
        this.jsonStatistics = {
            totalFields: 0,
            totalObjects: 0,
            totalArrays: 0,
            averageSize: 0,
            maxDepth: 0,
            validationErrors: 0
        };
        
        this.initializeJsonSystem();
    }
    
    // 初始化JSON系统
    initializeJsonSystem() {
        // 创建增强的JSON字段
        this.createEnhancedJsonField();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置格式化系统
        this.setupFormattingSystem();
        
        // 设置编辑系统
        this.setupEditingSystem();
    }
    
    // 创建增强的JSON字段
    createEnhancedJsonField() {
        const originalField = JsonField;
        
        this.EnhancedJsonField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
                
                // 添加格式化功能
                this.addFormattingFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isExpanded: false,
                    isEditing: false,
                    validationErrors: [],
                    formattedJson: null,
                    jsonDepth: 0,
                    jsonSize: 0,
                    jsonType: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的格式化显示
                this.enhancedFormattedValue = () => {
                    const value = this.props.record.data[this.props.name];
                    
                    if (!value) {
                        return "";
                    }
                    
                    try {
                        // 验证JSON
                        this.validateJson(value);
                        
                        // 分析JSON结构
                        this.analyzeJsonStructure(value);
                        
                        // 格式化JSON
                        return this.formatJson(value);
                        
                    } catch (error) {
                        this.handleJsonError(error);
                        return this.getErrorDisplay(error);
                    }
                };
                
                // 验证JSON
                this.validateJson = (value) => {
                    const errors = [];
                    
                    // 类型验证
                    if (this.validationRules.enableTypeValidation) {
                        const type = this.getJsonType(value);
                        if (!this.validationRules.allowedTypes.includes(type)) {
                            errors.push(`JSON type '${type}' is not allowed`);
                        }
                    }
                    
                    // 深度验证
                    const depth = this.calculateJsonDepth(value);
                    if (depth > this.validationRules.maxDepth) {
                        errors.push(`JSON depth ${depth} exceeds maximum allowed depth of ${this.validationRules.maxDepth}`);
                    }
                    
                    // 大小验证
                    const size = JSON.stringify(value).length;
                    if (size > this.validationRules.maxSize) {
                        errors.push(`JSON size ${size} bytes exceeds maximum allowed size of ${this.validationRules.maxSize} bytes`);
                    }
                    
                    // 模式验证
                    if (this.validationRules.enableSchemaValidation) {
                        const schemaErrors = this.validateJsonSchema(value);
                        errors.push(...schemaErrors);
                    }
                    
                    this.enhancedState.validationErrors = errors;
                    
                    if (errors.length > 0) {
                        throw new Error(errors.join(', '));
                    }
                };
                
                // 获取JSON类型
                this.getJsonType = (value) => {
                    if (value === null) return 'null';
                    if (Array.isArray(value)) return 'array';
                    return typeof value;
                };
                
                // 计算JSON深度
                this.calculateJsonDepth = (obj, depth = 0) => {
                    if (obj === null || typeof obj !== 'object') {
                        return depth;
                    }
                    
                    let maxDepth = depth;
                    
                    if (Array.isArray(obj)) {
                        for (const item of obj) {
                            maxDepth = Math.max(maxDepth, this.calculateJsonDepth(item, depth + 1));
                        }
                    } else {
                        for (const key in obj) {
                            if (obj.hasOwnProperty(key)) {
                                maxDepth = Math.max(maxDepth, this.calculateJsonDepth(obj[key], depth + 1));
                            }
                        }
                    }
                    
                    return maxDepth;
                };
                
                // 验证JSON模式
                this.validateJsonSchema = (value) => {
                    // 简化的模式验证实现
                    const errors = [];
                    
                    // 这里应该集成专业的JSON Schema验证库
                    // 如ajv等，这里只是示例
                    
                    return errors;
                };
                
                // 分析JSON结构
                this.analyzeJsonStructure = (value) => {
                    this.enhancedState.jsonType = this.getJsonType(value);
                    this.enhancedState.jsonDepth = this.calculateJsonDepth(value);
                    this.enhancedState.jsonSize = JSON.stringify(value).length;
                    
                    // 记录统计
                    this.recordJsonStatistics(value);
                };
                
                // 格式化JSON
                this.formatJson = (value) => {
                    if (!this.formatOptions.enablePrettyPrint) {
                        return JSON.stringify(value);
                    }
                    
                    const formatted = JSON.stringify(value, null, this.formatOptions.indent);
                    
                    // 检查显示长度限制
                    if (formatted.length > this.formatOptions.maxDisplayLength) {
                        return this.getTruncatedDisplay(formatted);
                    }
                    
                    this.enhancedState.formattedJson = formatted;
                    return formatted;
                };
                
                // 获取截断显示
                this.getTruncatedDisplay = (formatted) => {
                    const truncated = formatted.substring(0, this.formatOptions.maxDisplayLength);
                    return truncated + '... (truncated)';
                };
                
                // 美化JSON
                this.prettifyJson = (value) => {
                    try {
                        return JSON.stringify(value, null, this.formatOptions.indent);
                    } catch (error) {
                        return JSON.stringify(value);
                    }
                };
                
                // 压缩JSON
                this.minifyJson = (value) => {
                    return JSON.stringify(value);
                };
                
                // 搜索JSON
                this.searchJson = (value, searchTerm) => {
                    const results = [];
                    const jsonString = JSON.stringify(value);
                    
                    if (jsonString.toLowerCase().includes(searchTerm.toLowerCase())) {
                        results.push({
                            path: 'root',
                            value: value,
                            match: searchTerm
                        });
                    }
                    
                    return results;
                };
                
                // 展开/折叠JSON
                this.toggleExpanded = () => {
                    this.enhancedState.isExpanded = !this.enhancedState.isExpanded;
                };
                
                // 获取JSON信息
                this.getJsonInfo = () => {
                    return {
                        type: this.enhancedState.jsonType,
                        depth: this.enhancedState.jsonDepth,
                        size: this.enhancedState.jsonSize,
                        isValid: this.enhancedState.validationErrors.length === 0,
                        errors: this.enhancedState.validationErrors,
                        isExpanded: this.enhancedState.isExpanded,
                        formattedJson: this.enhancedState.formattedJson
                    };
                };
                
                // 导出JSON
                this.exportJson = (format = 'json') => {
                    const value = this.props.record.data[this.props.name];
                    
                    switch (format) {
                        case 'json':
                            return this.prettifyJson(value);
                        case 'minified':
                            return this.minifyJson(value);
                        case 'csv':
                            return this.jsonToCsv(value);
                        default:
                            return JSON.stringify(value);
                    }
                };
                
                // JSON转CSV
                this.jsonToCsv = (value) => {
                    if (!Array.isArray(value)) {
                        return 'Not an array';
                    }
                    
                    if (value.length === 0) {
                        return '';
                    }
                    
                    // 简化的JSON到CSV转换
                    const headers = Object.keys(value[0]);
                    const csvRows = [headers.join(',')];
                    
                    for (const row of value) {
                        const values = headers.map(header => {
                            const val = row[header];
                            return typeof val === 'string' ? `"${val}"` : val;
                        });
                        csvRows.push(values.join(','));
                    }
                    
                    return csvRows.join('\n');
                };
                
                // 记录JSON统计
                this.recordJsonStatistics = (value) => {
                    this.jsonStatistics.totalFields++;
                    
                    const type = this.getJsonType(value);
                    if (type === 'object') {
                        this.jsonStatistics.totalObjects++;
                    } else if (type === 'array') {
                        this.jsonStatistics.totalArrays++;
                    }
                    
                    const size = JSON.stringify(value).length;
                    this.jsonStatistics.averageSize = 
                        (this.jsonStatistics.averageSize + size) / 2;
                    
                    const depth = this.calculateJsonDepth(value);
                    if (depth > this.jsonStatistics.maxDepth) {
                        this.jsonStatistics.maxDepth = depth;
                    }
                };
                
                // 处理JSON错误
                this.handleJsonError = (error) => {
                    console.error('JSON processing error:', error);
                    this.jsonStatistics.validationErrors++;
                };
                
                // 获取错误显示
                this.getErrorDisplay = (error) => {
                    return `[JSON Error: ${error.message}]`;
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.jsonConfig.enableValidation,
                    validate: (value) => this.validateJson(value),
                    getErrors: () => this.enhancedState.validationErrors
                };
            }
            
            addFormattingFeatures() {
                // 格式化功能
                this.formattingManager = {
                    enabled: this.jsonConfig.enableFormatting,
                    prettify: (value) => this.prettifyJson(value),
                    minify: (value) => this.minifyJson(value),
                    export: (format) => this.exportJson(format)
                };
            }
            
            // 重写原始方法
            get formattedValue() {
                return this.enhancedFormattedValue();
            }
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.jsonConfig.enableValidation,
            rules: this.validationRules
        };
    }
    
    // 设置格式化系统
    setupFormattingSystem() {
        this.formattingSystemConfig = {
            enabled: this.jsonConfig.enableFormatting,
            options: this.formatOptions
        };
    }
    
    // 设置编辑系统
    setupEditingSystem() {
        this.editingSystemConfig = {
            enabled: this.jsonConfig.enableEditing,
            syntaxHighlight: this.jsonConfig.enableSyntaxHighlight
        };
    }
    
    // 创建JSON字段
    createJsonField(props) {
        const field = new this.EnhancedJsonField(props);
        this.jsonStatistics.totalFields++;
        return field;
    }
    
    // 注册JSON模式
    registerJsonSchema(name, schema) {
        this.jsonSchemas.set(name, schema);
    }
    
    // 批量验证JSON
    batchValidateJson(fields) {
        const results = [];
        
        for (const field of fields) {
            try {
                const value = field.props.record.data[field.props.name];
                field.validateJson(value);
                results.push({ field, isValid: true, errors: [] });
            } catch (error) {
                results.push({ field, isValid: false, errors: [error.message] });
            }
        }
        
        return results;
    }
    
    // 获取JSON统计
    getJsonStatistics() {
        return {
            ...this.jsonStatistics,
            schemaCount: this.jsonSchemas.size,
            objectRate: this.jsonStatistics.totalObjects / Math.max(this.jsonStatistics.totalFields, 1) * 100,
            arrayRate: this.jsonStatistics.totalArrays / Math.max(this.jsonStatistics.totalFields, 1) * 100
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理模式
        this.jsonSchemas.clear();
        
        // 重置统计
        this.jsonStatistics = {
            totalFields: 0,
            totalObjects: 0,
            totalArrays: 0,
            averageSize: 0,
            maxDepth: 0,
            validationErrors: 0
        };
    }
}

// 使用示例
const jsonManager = new JsonFieldManager();

// 创建JSON字段
const jsonField = jsonManager.createJsonField({
    name: 'config_data',
    record: {
        data: { 
            config_data: {
                name: 'example',
                settings: {
                    enabled: true,
                    value: 42
                },
                items: ['item1', 'item2']
            }
        },
        fields: { config_data: { type: 'jsonb' } }
    }
});

// 注册自定义模式
jsonManager.registerJsonSchema('user_config', {
    type: 'object',
    properties: {
        username: { type: 'string' },
        preferences: { type: 'object' }
    }
});

// 获取统计信息
const stats = jsonManager.getJsonStatistics();
console.log('JSON field statistics:', stats);
```

## 技术特点

### 1. 简洁设计
- **最小实现**: 极简的代码实现
- **专注功能**: 专注于JSON数据显示
- **标准集成**: 完全集成标准字段属性
- **轻量级**: 最小的资源占用

### 2. JSON处理
- **序列化**: 使用JSON.stringify序列化数据
- **空值处理**: 优雅处理空值情况
- **类型支持**: 专门支持jsonb类型
- **格式化**: 基础的JSON格式化功能

### 3. 数据显示
- **只读显示**: 专门用于数据显示
- **字符串转换**: 将JSON对象转换为字符串
- **模板渲染**: 通过模板渲染JSON内容
- **用户友好**: 提供用户友好的显示格式

### 4. 类型安全
- **类型限制**: 仅支持jsonb类型
- **数据验证**: 基础的数据有效性验证
- **错误处理**: 处理JSON序列化错误
- **安全显示**: 安全地显示JSON内容

## 设计模式

### 1. 组件模式 (Component Pattern)
- **UI组件**: 封装JSON显示UI
- **数据绑定**: 绑定JSON数据
- **模板渲染**: 通过模板渲染内容

### 2. 适配器模式 (Adapter Pattern)
- **数据适配**: 适配JSON数据为显示格式
- **格式转换**: 转换JSON对象为字符串
- **接口适配**: 适配标准字段接口

### 3. 策略模式 (Strategy Pattern)
- **显示策略**: 不同的JSON显示策略
- **格式策略**: 不同的格式化策略
- **验证策略**: 不同的验证策略

### 4. 观察者模式 (Observer Pattern)
- **数据观察**: 观察JSON数据变化
- **状态观察**: 观察组件状态变化
- **更新通知**: 通知数据更新

## 注意事项

1. **数据大小**: 注意大型JSON对象的显示性能
2. **循环引用**: 避免JSON对象的循环引用
3. **安全性**: 确保JSON数据的安全显示
4. **格式化**: 提供良好的JSON格式化显示

## 扩展建议

1. **JSON编辑**: 添加JSON编辑功能
2. **语法高亮**: 支持JSON语法高亮
3. **折叠展开**: 支持JSON结构的折叠展开
4. **搜索功能**: 添加JSON内容搜索
5. **验证增强**: 增强JSON验证功能

该JSON字段为Odoo Web客户端提供了基础的JSON数据显示功能，通过简洁的设计和可靠的JSON序列化确保了JSONB类型数据的正确显示和处理。

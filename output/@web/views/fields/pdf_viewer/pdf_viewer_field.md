# PdfViewerField - PDF查看器字段

## 概述

`pdf_viewer_field.js` 是 Odoo Web 客户端的PDF查看器字段组件，负责在Web界面中显示和管理PDF文件。该模块包含92行代码，是一个功能完整的PDF处理组件，专门用于处理binary类型的PDF文件字段，具备PDF预览、文件上传、页面导航、错误处理等特性，是文档管理的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.js`
- **行数**: 92
- **模块**: `@web/views/fields/pdf_viewer/pdf_viewer_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/core/utils/hooks'                 // 工具钩子
'@web/core/utils/urls'                  // URL工具
'@web/views/fields/standard_field_props' // 标准字段属性
'@web/views/fields/file_handler'        // 文件处理器
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const PdfViewerField = class PdfViewerField extends Component {
    static template = "web.PdfViewerField";
    static components = {
        FileUploader,
    };
    static props = {
        ...standardFieldProps,
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **文件上传**: 集成FileUploader组件
- **专用模板**: 使用PdfViewerField专用模板
- **PDF专用**: 专门处理PDF文件

### 2. 组件初始化

```javascript
setup() {
    this.notification = useService("notification");
    this.state = useState({
        isValid: true,
        objectUrl: "",
    });
    onWillUpdateProps((nextProps) => {
        if (nextProps.readonly) {
            this.state.objectUrl = "";
        }
    });
}
```

**初始化功能**:
- **通知服务**: 使用通知服务显示消息
- **状态管理**: 管理PDF有效性和对象URL
- **属性监听**: 监听属性变化清理状态
- **只读处理**: 只读模式下清理对象URL

### 3. URL生成

```javascript
get url() {
    if (!this.state.isValid || !this.props.record.data[this.props.name]) {
        return null;
    }
    const page = this.props.record.data[`${this.props.name}_page`] || 1;
    const file = encodeURIComponent(
        this.state.objectUrl ||
            url("/web/content", {
                model: this.props.record.resModel,
                field: this.props.name,
                id: this.props.record.resId,
            })
    );
    return `/web/static/lib/pdfjs/web/viewer.html?file=${file}#page=${page}`;
}
```

**URL生成功能**:
- **有效性检查**: 检查PDF有效性和数据存在
- **页面支持**: 支持指定页面显示
- **URL编码**: 对文件URL进行编码
- **PDF.js集成**: 使用PDF.js查看器显示PDF
- **内容URL**: 生成Web内容访问URL

### 4. 文件更新

```javascript
update({ data }) {
    const changes = { [this.props.name]: data || false };
    return this.props.record.update(changes);
}
```

**更新功能**:
- **数据更新**: 更新字段数据
- **空值处理**: 处理空数据情况
- **记录更新**: 调用记录更新方法
- **异步操作**: 返回Promise支持异步操作

### 5. 文件操作

```javascript
onFileRemove() {
    this.state.isValid = true;
    this.update({});
}

onFileUploaded({ data, objectUrl }) {
    this.state.isValid = true;
    this.state.objectUrl = objectUrl;
    this.update({ data });
}

onLoadFailed() {
    this.state.isValid = false;
    this.notification.add(_t("Could not display the selected pdf"), {
        type: "danger",
    });
}
```

**文件操作功能**:
- **文件删除**: 删除PDF文件并重置状态
- **文件上传**: 处理PDF文件上传
- **加载失败**: 处理PDF加载失败情况
- **状态更新**: 更新组件状态
- **错误通知**: 显示错误通知

### 6. 字段注册

```javascript
const pdfViewerField = {
    component: PdfViewerField,
    displayName: _t("PDF Viewer"),
    supportedOptions: [
        {
            label: _t("Preview image"),
            name: "preview_image",
            type: "field",
            availableTypes: ["binary"],
        },
    ],
    supportedTypes: ["binary"],
};

registry.category("fields").add("pdf_viewer", pdfViewerField);
```

**注册功能**:
- **组件注册**: 注册PDF查看器字段组件
- **显示名称**: 设置为"PDF Viewer"
- **预览选项**: 支持preview_image选项配置
- **类型支持**: 仅支持binary类型
- **字段注册**: 注册为pdf_viewer字段类型

## 使用场景

### 1. PDF查看器字段管理器

```javascript
// PDF查看器字段管理器
class PdfViewerFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置PDF查看器配置
        this.pdfConfig = {
            enableViewer: true,
            enableUpload: true,
            enableDownload: true,
            enablePrint: true,
            enableAnnotation: false,
            enableSearch: true,
            enableZoom: true,
            enableRotation: true,
            enableFullscreen: true
        };
        
        // 设置查看器选项
        this.viewerOptions = {
            defaultZoom: 'auto',
            enableSidebarOnLoad: false,
            enableHandTool: true,
            enableTextSelection: true,
            enablePresentationMode: false,
            maxZoom: 10,
            minZoom: 0.1,
            zoomStep: 0.1
        };
        
        // 设置文件验证规则
        this.validationRules = {
            enableFileValidation: true,
            maxFileSize: 50 * 1024 * 1024, // 50MB
            allowedMimeTypes: ['application/pdf'],
            enableVirusScanning: false,
            enablePasswordProtection: false
        };
        
        // 设置缓存配置
        this.cacheConfig = {
            enableCaching: true,
            cacheSize: 100,
            cacheTTL: 3600000, // 1小时
            enablePersistentCache: false,
            enablePreloading: false
        };
        
        // 设置PDF统计
        this.pdfStatistics = {
            totalPdfFields: 0,
            totalPdfFiles: 0,
            totalFileSize: 0,
            averageFileSize: 0,
            maxFileSize: 0,
            loadErrors: 0,
            uploadErrors: 0,
            viewCount: 0
        };
        
        this.initializePdfSystem();
    }
    
    // 初始化PDF系统
    initializePdfSystem() {
        // 创建增强的PDF查看器字段
        this.createEnhancedPdfViewerField();
        
        // 设置文件处理系统
        this.setupFileHandlingSystem();
        
        // 设置查看器系统
        this.setupViewerSystem();
        
        // 设置缓存系统
        this.setupCacheSystem();
    }
    
    // 创建增强的PDF查看器字段
    createEnhancedPdfViewerField() {
        const originalField = PdfViewerField;
        
        this.EnhancedPdfViewerField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加查看器功能
                this.addViewerFeatures();
                
                // 添加文件处理功能
                this.addFileHandlingFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    ...this.state,
                    currentPage: 1,
                    totalPages: 0,
                    zoomLevel: 1,
                    isLoading: false,
                    fileInfo: null,
                    viewerReady: false,
                    errorMessage: null,
                    lastViewTime: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的URL生成
                this.enhancedGetUrl = () => {
                    if (!this.enhancedState.isValid || !this.props.record.data[this.props.name]) {
                        return null;
                    }
                    
                    const page = this.enhancedState.currentPage || 1;
                    const zoom = this.viewerOptions.defaultZoom;
                    
                    const file = encodeURIComponent(
                        this.enhancedState.objectUrl ||
                            this.generateContentUrl()
                    );
                    
                    let viewerUrl = `/web/static/lib/pdfjs/web/viewer.html?file=${file}#page=${page}`;
                    
                    if (zoom !== 'auto') {
                        viewerUrl += `&zoom=${zoom}`;
                    }
                    
                    return viewerUrl;
                };
                
                // 生成内容URL
                this.generateContentUrl = () => {
                    return url("/web/content", {
                        model: this.props.record.resModel,
                        field: this.props.name,
                        id: this.props.record.resId,
                        download: false
                    });
                };
                
                // 增强的文件上传处理
                this.enhancedOnFileUploaded = async ({ data, objectUrl, file }) => {
                    try {
                        // 验证文件
                        await this.validatePdfFile(file);
                        
                        // 获取文件信息
                        const fileInfo = await this.extractFileInfo(file);
                        
                        // 更新状态
                        this.enhancedState.isValid = true;
                        this.enhancedState.objectUrl = objectUrl;
                        this.enhancedState.fileInfo = fileInfo;
                        this.enhancedState.currentPage = 1;
                        
                        // 更新数据
                        await this.update({ data });
                        
                        // 记录统计
                        this.recordFileUpload(fileInfo);
                        
                        // 预加载PDF
                        if (this.cacheConfig.enablePreloading) {
                            this.preloadPdf(objectUrl);
                        }
                        
                    } catch (error) {
                        this.handleUploadError(error);
                    }
                };
                
                // 验证PDF文件
                this.validatePdfFile = async (file) => {
                    if (!this.validationRules.enableFileValidation) {
                        return;
                    }
                    
                    // 检查文件大小
                    if (file.size > this.validationRules.maxFileSize) {
                        throw new Error(`File size ${this.formatFileSize(file.size)} exceeds maximum allowed size of ${this.formatFileSize(this.validationRules.maxFileSize)}`);
                    }
                    
                    // 检查MIME类型
                    if (!this.validationRules.allowedMimeTypes.includes(file.type)) {
                        throw new Error(`File type ${file.type} is not allowed. Only PDF files are supported.`);
                    }
                    
                    // 检查文件头
                    const isValidPdf = await this.validatePdfHeader(file);
                    if (!isValidPdf) {
                        throw new Error('Invalid PDF file format');
                    }
                };
                
                // 验证PDF文件头
                this.validatePdfHeader = async (file) => {
                    return new Promise((resolve) => {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            const arrayBuffer = e.target.result;
                            const uint8Array = new Uint8Array(arrayBuffer.slice(0, 5));
                            const header = String.fromCharCode.apply(null, uint8Array);
                            resolve(header === '%PDF-');
                        };
                        reader.onerror = () => resolve(false);
                        reader.readAsArrayBuffer(file.slice(0, 5));
                    });
                };
                
                // 提取文件信息
                this.extractFileInfo = async (file) => {
                    return {
                        name: file.name,
                        size: file.size,
                        type: file.type,
                        lastModified: new Date(file.lastModified),
                        uploadTime: new Date()
                    };
                };
                
                // 格式化文件大小
                this.formatFileSize = (bytes) => {
                    const units = ['B', 'KB', 'MB', 'GB'];
                    let size = bytes;
                    let unitIndex = 0;
                    
                    while (size >= 1024 && unitIndex < units.length - 1) {
                        size /= 1024;
                        unitIndex++;
                    }
                    
                    return `${size.toFixed(1)} ${units[unitIndex]}`;
                };
                
                // 增强的加载失败处理
                this.enhancedOnLoadFailed = (error) => {
                    this.enhancedState.isValid = false;
                    this.enhancedState.errorMessage = error?.message || 'Unknown error';
                    
                    this.notification.add(
                        _t("Could not display the selected PDF: %s", this.enhancedState.errorMessage),
                        { type: "danger" }
                    );
                    
                    // 记录错误统计
                    this.pdfStatistics.loadErrors++;
                };
                
                // 页面导航
                this.navigateToPage = (pageNumber) => {
                    if (pageNumber >= 1 && pageNumber <= this.enhancedState.totalPages) {
                        this.enhancedState.currentPage = pageNumber;
                        
                        // 更新页面字段
                        const pageField = `${this.props.name}_page`;
                        if (pageField in this.props.record.data) {
                            this.props.record.update({ [pageField]: pageNumber });
                        }
                    }
                };
                
                // 缩放控制
                this.setZoomLevel = (zoomLevel) => {
                    const clampedZoom = Math.max(
                        this.viewerOptions.minZoom,
                        Math.min(this.viewerOptions.maxZoom, zoomLevel)
                    );
                    
                    this.enhancedState.zoomLevel = clampedZoom;
                };
                
                // 放大
                this.zoomIn = () => {
                    const newZoom = this.enhancedState.zoomLevel + this.viewerOptions.zoomStep;
                    this.setZoomLevel(newZoom);
                };
                
                // 缩小
                this.zoomOut = () => {
                    const newZoom = this.enhancedState.zoomLevel - this.viewerOptions.zoomStep;
                    this.setZoomLevel(newZoom);
                };
                
                // 适合页面
                this.fitToPage = () => {
                    this.enhancedState.zoomLevel = 'page-fit';
                };
                
                // 适合宽度
                this.fitToWidth = () => {
                    this.enhancedState.zoomLevel = 'page-width';
                };
                
                // 下载PDF
                this.downloadPdf = () => {
                    if (!this.pdfConfig.enableDownload) {
                        return;
                    }
                    
                    const downloadUrl = url("/web/content", {
                        model: this.props.record.resModel,
                        field: this.props.name,
                        id: this.props.record.resId,
                        download: true,
                        filename: this.enhancedState.fileInfo?.name || 'document.pdf'
                    });
                    
                    window.open(downloadUrl, '_blank');
                };
                
                // 打印PDF
                this.printPdf = () => {
                    if (!this.pdfConfig.enablePrint) {
                        return;
                    }
                    
                    // 触发PDF.js的打印功能
                    const iframe = document.querySelector('iframe[src*="viewer.html"]');
                    if (iframe && iframe.contentWindow) {
                        iframe.contentWindow.print();
                    }
                };
                
                // 预加载PDF
                this.preloadPdf = (url) => {
                    const link = document.createElement('link');
                    link.rel = 'prefetch';
                    link.href = url;
                    document.head.appendChild(link);
                };
                
                // 获取PDF信息
                this.getPdfInfo = () => {
                    return {
                        isValid: this.enhancedState.isValid,
                        currentPage: this.enhancedState.currentPage,
                        totalPages: this.enhancedState.totalPages,
                        zoomLevel: this.enhancedState.zoomLevel,
                        fileInfo: this.enhancedState.fileInfo,
                        url: this.enhancedGetUrl(),
                        errorMessage: this.enhancedState.errorMessage
                    };
                };
                
                // 记录文件上传
                this.recordFileUpload = (fileInfo) => {
                    this.pdfStatistics.totalPdfFiles++;
                    this.pdfStatistics.totalFileSize += fileInfo.size;
                    this.pdfStatistics.averageFileSize = 
                        this.pdfStatistics.totalFileSize / this.pdfStatistics.totalPdfFiles;
                    
                    if (fileInfo.size > this.pdfStatistics.maxFileSize) {
                        this.pdfStatistics.maxFileSize = fileInfo.size;
                    }
                };
                
                // 记录查看
                this.recordView = () => {
                    this.pdfStatistics.viewCount++;
                    this.enhancedState.lastViewTime = new Date();
                };
                
                // 处理上传错误
                this.handleUploadError = (error) => {
                    this.enhancedState.isValid = false;
                    this.enhancedState.errorMessage = error.message;
                    
                    this.notification.add(
                        _t("PDF upload failed: %s", error.message),
                        { type: "danger" }
                    );
                    
                    this.pdfStatistics.uploadErrors++;
                };
            }
            
            addViewerFeatures() {
                // 查看器功能
                this.viewerManager = {
                    enabled: this.pdfConfig.enableViewer,
                    navigate: (page) => this.navigateToPage(page),
                    zoom: (level) => this.setZoomLevel(level),
                    zoomIn: () => this.zoomIn(),
                    zoomOut: () => this.zoomOut(),
                    fitToPage: () => this.fitToPage(),
                    fitToWidth: () => this.fitToWidth(),
                    download: () => this.downloadPdf(),
                    print: () => this.printPdf(),
                    getInfo: () => this.getPdfInfo()
                };
            }
            
            addFileHandlingFeatures() {
                // 文件处理功能
                this.fileHandler = {
                    enabled: this.pdfConfig.enableUpload,
                    validate: (file) => this.validatePdfFile(file),
                    upload: (data) => this.enhancedOnFileUploaded(data),
                    remove: () => this.onFileRemove(),
                    getInfo: () => this.enhancedState.fileInfo
                };
            }
            
            // 重写原始方法
            get url() {
                return this.enhancedGetUrl();
            }
            
            onFileUploaded(data) {
                return this.enhancedOnFileUploaded(data);
            }
            
            onLoadFailed(error) {
                return this.enhancedOnLoadFailed(error);
            }
        };
    }
    
    // 设置文件处理系统
    setupFileHandlingSystem() {
        this.fileHandlingConfig = {
            enabled: this.pdfConfig.enableUpload,
            validation: this.validationRules
        };
    }
    
    // 设置查看器系统
    setupViewerSystem() {
        this.viewerSystemConfig = {
            enabled: this.pdfConfig.enableViewer,
            options: this.viewerOptions
        };
    }
    
    // 设置缓存系统
    setupCacheSystem() {
        this.cacheSystemConfig = {
            enabled: this.cacheConfig.enableCaching,
            config: this.cacheConfig
        };
    }
    
    // 创建PDF查看器字段
    createPdfViewerField(props) {
        const field = new this.EnhancedPdfViewerField(props);
        this.pdfStatistics.totalPdfFields++;
        return field;
    }
    
    // 批量创建PDF字段
    batchCreatePdfFields(propsArray) {
        const results = [];
        
        for (const props of propsArray) {
            try {
                const field = this.createPdfViewerField(props);
                results.push({ field, success: true, props });
            } catch (error) {
                results.push({ field: null, success: false, error, props });
            }
        }
        
        return results;
    }
    
    // 获取PDF统计
    getPdfStatistics() {
        return {
            ...this.pdfStatistics,
            averageViewsPerField: this.pdfStatistics.viewCount / Math.max(this.pdfStatistics.totalPdfFields, 1),
            uploadSuccessRate: (this.pdfStatistics.totalPdfFiles / Math.max(this.pdfStatistics.totalPdfFiles + this.pdfStatistics.uploadErrors, 1)) * 100,
            loadSuccessRate: ((this.pdfStatistics.viewCount - this.pdfStatistics.loadErrors) / Math.max(this.pdfStatistics.viewCount, 1)) * 100,
            averageFileSizeFormatted: this.formatFileSize(this.pdfStatistics.averageFileSize),
            maxFileSizeFormatted: this.formatFileSize(this.pdfStatistics.maxFileSize),
            totalFileSizeFormatted: this.formatFileSize(this.pdfStatistics.totalFileSize)
        };
    }
    
    // 格式化文件大小
    formatFileSize(bytes) {
        const units = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return `${size.toFixed(1)} ${units[unitIndex]}`;
    }
    
    // 销毁管理器
    destroy() {
        // 重置统计
        this.pdfStatistics = {
            totalPdfFields: 0,
            totalPdfFiles: 0,
            totalFileSize: 0,
            averageFileSize: 0,
            maxFileSize: 0,
            loadErrors: 0,
            uploadErrors: 0,
            viewCount: 0
        };
    }
}

// 使用示例
const pdfManager = new PdfViewerFieldManager();

// 创建PDF查看器字段
const pdfField = pdfManager.createPdfViewerField({
    name: 'pdf_document',
    record: {
        data: { 
            pdf_document: 'base64_encoded_pdf_data',
            pdf_document_page: 1
        },
        fields: { 
            pdf_document: { 
                type: 'binary'
            }
        },
        resModel: 'document.document',
        resId: 123
    }
});

// 获取统计信息
const stats = pdfManager.getPdfStatistics();
console.log('PDF viewer field statistics:', stats);
```

## 技术特点

### 1. PDF集成
- **PDF.js集成**: 使用PDF.js库显示PDF
- **查看器嵌入**: 嵌入完整的PDF查看器
- **页面导航**: 支持页面导航功能
- **缩放控制**: 支持缩放控制功能

### 2. 文件处理
- **文件上传**: 集成文件上传功能
- **文件验证**: 验证PDF文件格式
- **错误处理**: 完善的错误处理机制
- **状态管理**: 管理文件和查看器状态

### 3. URL管理
- **动态URL**: 动态生成PDF查看器URL
- **内容URL**: 生成Web内容访问URL
- **页面参数**: 支持页面参数传递
- **编码处理**: 正确处理URL编码

### 4. 用户体验
- **即时预览**: 上传后即时预览PDF
- **错误反馈**: 清晰的错误消息反馈
- **加载状态**: 显示加载状态
- **响应式**: 响应式界面设计

## 设计模式

### 1. 组件模式 (Component Pattern)
- **UI组件**: 封装PDF查看器UI
- **状态管理**: 管理PDF查看器状态
- **事件处理**: 处理文件操作事件

### 2. 观察者模式 (Observer Pattern)
- **属性观察**: 观察属性变化
- **状态观察**: 观察组件状态变化
- **文件观察**: 观察文件变化

### 3. 策略模式 (Strategy Pattern)
- **显示策略**: 不同的PDF显示策略
- **验证策略**: 不同的文件验证策略
- **错误策略**: 不同的错误处理策略

### 4. 适配器模式 (Adapter Pattern)
- **PDF.js适配**: 适配PDF.js查看器
- **文件适配**: 适配文件处理接口
- **URL适配**: 适配URL生成接口

## 注意事项

1. **文件大小**: 注意大型PDF文件的性能影响
2. **浏览器兼容**: 确保PDF.js的浏览器兼容性
3. **安全性**: 验证上传文件的安全性
4. **内存管理**: 合理管理PDF文件的内存使用

## 扩展建议

1. **注释功能**: 支持PDF注释功能
2. **搜索功能**: 添加PDF内容搜索
3. **书签功能**: 支持PDF书签导航
4. **批量操作**: 支持批量PDF操作
5. **转换功能**: 支持PDF格式转换

该PDF查看器字段为Odoo Web客户端提供了完整的PDF文档管理功能，通过PDF.js集成和智能的文件处理确保了良好的文档查看体验和系统性能。

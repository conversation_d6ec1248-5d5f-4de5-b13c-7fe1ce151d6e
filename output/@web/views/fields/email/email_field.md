# EmailField - 邮箱字段

## 概述

`email_field.js` 是 Odoo Web 客户端的邮箱字段组件，负责处理邮箱地址的输入、显示和验证。该模块包含46行代码，是一个专门的邮箱输入组件，专门用于处理邮箱类型的字段，具备输入钩子、占位符支持、表单变体等特性，是邮箱地址处理的专用组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/email/email_field.js`
- **行数**: 46
- **模块**: `@web/views/fields/email/email_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                    // 注册表
'@web/core/l10n/translation'            // 翻译服务
'@web/views/fields/input_field_hook'    // 输入字段钩子
'@web/views/fields/standard_field_props' // 标准字段属性
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 基础邮箱字段

```javascript
const EmailField = class EmailField extends Component {
    static template = "web.EmailField";
    static props = {
        ...standardFieldProps,
        placeholder: { type: String, optional: true },
    };

    setup() {
        useInputField({ getValue: () => this.props.record.data[this.props.name] || "" });
    }
}
```

**基础特性**:
- **标准属性**: 继承所有标准字段属性
- **占位符**: 支持placeholder属性配置
- **输入钩子**: 使用useInputField钩子管理输入
- **专用模板**: 使用EmailField专用模板

### 2. 表单邮箱字段

```javascript
class FormEmailField extends EmailField {
    static template = "web.FormEmailField";
}
```

**表单特性**:
- **继承基类**: 继承EmailField的所有功能
- **专用模板**: 使用FormEmailField专用模板
- **表单优化**: 针对表单视图的优化

### 3. 字段注册

```javascript
const emailField = {
    component: EmailField,
    displayName: _t("Email"),
    supportedTypes: ["char"],
    extractProps: ({ attrs }) => ({
        placeholder: attrs.placeholder,
    }),
};

registry.category("fields").add("email", emailField);

const formEmailField = {
    ...emailField,
    component: FormEmailField,
};

registry.category("fields").add("form.email", formEmailField);
```

**注册功能**:
- **基础注册**: 注册email字段类型
- **表单注册**: 注册form.email字段类型
- **类型支持**: 仅支持char类型
- **属性提取**: 提取placeholder属性

## 使用场景

### 1. 邮箱字段管理器

```javascript
// 邮箱字段管理器
class EmailFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置邮箱字段配置
        this.emailConfig = {
            enableValidation: true,
            enableAutoComplete: true,
            enableDomainSuggestions: true,
            enableFormatting: true,
            enableLinkGeneration: true,
            enableSpamCheck: true,
            enableDeliveryCheck: false,
            enableHistoryTracking: true
        };
        
        // 设置邮箱验证规则
        this.validationRules = {
            enableBasicFormat: true,
            enableDomainValidation: true,
            enableMXRecord: false,
            enableDisposableCheck: true,
            enableBlacklist: true,
            maxLength: 254,
            minLength: 5
        };
        
        // 设置域名建议
        this.domainSuggestions = [
            'gmail.com',
            'outlook.com',
            'yahoo.com',
            'hotmail.com',
            'icloud.com',
            'qq.com',
            '163.com',
            '126.com'
        ];
        
        // 设置一次性邮箱域名
        this.disposableDomains = new Set([
            '10minutemail.com',
            'tempmail.org',
            'guerrillamail.com',
            'mailinator.com',
            'throwaway.email'
        ]);
        
        // 设置邮箱统计
        this.emailStatistics = {
            totalEmails: 0,
            validEmails: 0,
            invalidEmails: 0,
            domainDistribution: new Map(),
            averageValidationTime: 0
        };
        
        this.initializeEmailSystem();
    }
    
    // 初始化邮箱系统
    initializeEmailSystem() {
        // 创建增强的邮箱字段
        this.createEnhancedEmailField();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置自动完成
        this.setupAutoComplete();
        
        // 设置格式化系统
        this.setupFormattingSystem();
    }
    
    // 创建增强的邮箱字段
    createEnhancedEmailField() {
        const originalField = EmailField;
        
        this.EnhancedEmailField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
                
                // 添加自动完成功能
                this.addAutoCompleteFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isValidating: false,
                    validationErrors: [],
                    suggestions: [],
                    isValid: null,
                    domainSuggestion: null,
                    emailHistory: []
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 验证邮箱地址
                this.validateEmail = async (email) => {
                    if (!email) return { isValid: true, errors: [] };
                    
                    const startTime = performance.now();
                    this.enhancedState.isValidating = true;
                    const errors = [];
                    
                    try {
                        // 基本格式验证
                        if (this.validationRules.enableBasicFormat) {
                            if (!this.validateBasicFormat(email)) {
                                errors.push('Invalid email format');
                            }
                        }
                        
                        // 长度验证
                        if (email.length < this.validationRules.minLength) {
                            errors.push(`Email too short (minimum ${this.validationRules.minLength} characters)`);
                        }
                        
                        if (email.length > this.validationRules.maxLength) {
                            errors.push(`Email too long (maximum ${this.validationRules.maxLength} characters)`);
                        }
                        
                        // 域名验证
                        if (this.validationRules.enableDomainValidation) {
                            const domainErrors = await this.validateDomain(email);
                            errors.push(...domainErrors);
                        }
                        
                        // 一次性邮箱检查
                        if (this.validationRules.enableDisposableCheck) {
                            if (this.isDisposableEmail(email)) {
                                errors.push('Disposable email addresses are not allowed');
                            }
                        }
                        
                        // 黑名单检查
                        if (this.validationRules.enableBlacklist) {
                            if (this.isBlacklistedEmail(email)) {
                                errors.push('Email address is blacklisted');
                            }
                        }
                        
                        const isValid = errors.length === 0;
                        this.enhancedState.isValid = isValid;
                        this.enhancedState.validationErrors = errors;
                        
                        // 记录统计
                        this.recordValidationStatistics(email, isValid);
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordValidationTime(endTime - startTime);
                        
                        return { isValid, errors };
                        
                    } catch (error) {
                        errors.push(`Validation error: ${error.message}`);
                        return { isValid: false, errors };
                    } finally {
                        this.enhancedState.isValidating = false;
                    }
                };
                
                // 验证基本格式
                this.validateBasicFormat = (email) => {
                    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
                    return emailRegex.test(email);
                };
                
                // 验证域名
                this.validateDomain = async (email) => {
                    const errors = [];
                    const domain = email.split('@')[1];
                    
                    if (!domain) {
                        errors.push('Missing domain');
                        return errors;
                    }
                    
                    // 域名格式检查
                    const domainRegex = /^[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
                    if (!domainRegex.test(domain)) {
                        errors.push('Invalid domain format');
                    }
                    
                    // MX记录检查（如果启用）
                    if (this.validationRules.enableMXRecord) {
                        try {
                            const hasMX = await this.checkMXRecord(domain);
                            if (!hasMX) {
                                errors.push('Domain has no MX record');
                            }
                        } catch (error) {
                            // MX检查失败不算错误，只是警告
                            console.warn('MX record check failed:', error);
                        }
                    }
                    
                    return errors;
                };
                
                // 检查MX记录
                this.checkMXRecord = async (domain) => {
                    // 实际实现需要后端支持
                    // 这里只是示例
                    return true;
                };
                
                // 检查是否为一次性邮箱
                this.isDisposableEmail = (email) => {
                    const domain = email.split('@')[1];
                    return this.disposableDomains.has(domain.toLowerCase());
                };
                
                // 检查是否为黑名单邮箱
                this.isBlacklistedEmail = (email) => {
                    // 实现黑名单检查逻辑
                    return false;
                };
                
                // 获取域名建议
                this.getDomainSuggestions = (partialEmail) => {
                    if (!partialEmail.includes('@')) {
                        return [];
                    }
                    
                    const [localPart, partialDomain] = partialEmail.split('@');
                    if (!partialDomain) {
                        return this.domainSuggestions.map(domain => `${localPart}@${domain}`);
                    }
                    
                    const suggestions = this.domainSuggestions
                        .filter(domain => domain.toLowerCase().startsWith(partialDomain.toLowerCase()))
                        .map(domain => `${localPart}@${domain}`);
                    
                    this.enhancedState.suggestions = suggestions;
                    return suggestions;
                };
                
                // 格式化邮箱地址
                this.formatEmail = (email) => {
                    if (!email) return '';
                    
                    // 转换为小写
                    let formatted = email.toLowerCase().trim();
                    
                    // 移除多余空格
                    formatted = formatted.replace(/\s+/g, '');
                    
                    return formatted;
                };
                
                // 生成邮箱链接
                this.generateEmailLink = (email, subject = '', body = '') => {
                    if (!email) return '';
                    
                    let link = `mailto:${email}`;
                    const params = [];
                    
                    if (subject) {
                        params.push(`subject=${encodeURIComponent(subject)}`);
                    }
                    
                    if (body) {
                        params.push(`body=${encodeURIComponent(body)}`);
                    }
                    
                    if (params.length > 0) {
                        link += `?${params.join('&')}`;
                    }
                    
                    return link;
                };
                
                // 提取邮箱域名
                this.extractDomain = (email) => {
                    if (!email || !email.includes('@')) return '';
                    return email.split('@')[1];
                };
                
                // 检查邮箱可达性
                this.checkEmailDeliverability = async (email) => {
                    if (!this.emailConfig.enableDeliveryCheck) {
                        return { deliverable: true, reason: 'Check disabled' };
                    }
                    
                    try {
                        // 实际实现需要后端API支持
                        // 这里只是示例
                        return { deliverable: true, reason: 'Valid' };
                    } catch (error) {
                        return { deliverable: false, reason: error.message };
                    }
                };
                
                // 添加到历史
                this.addToHistory = (email) => {
                    if (!email || !this.emailConfig.enableHistoryTracking) return;
                    
                    const historyEntry = {
                        email: email,
                        timestamp: Date.now(),
                        isValid: this.enhancedState.isValid
                    };
                    
                    // 移除重复项
                    this.enhancedState.emailHistory = this.enhancedState.emailHistory
                        .filter(entry => entry.email !== email);
                    
                    // 添加到开头
                    this.enhancedState.emailHistory.unshift(historyEntry);
                    
                    // 限制历史大小
                    if (this.enhancedState.emailHistory.length > 20) {
                        this.enhancedState.emailHistory.pop();
                    }
                };
                
                // 获取邮箱历史
                this.getEmailHistory = () => {
                    return this.enhancedState.emailHistory;
                };
                
                // 清除历史
                this.clearHistory = () => {
                    this.enhancedState.emailHistory = [];
                };
                
                // 获取邮箱信息
                this.getEmailInfo = (email) => {
                    if (!email) return null;
                    
                    return {
                        email: email,
                        domain: this.extractDomain(email),
                        isValid: this.enhancedState.isValid,
                        errors: this.enhancedState.validationErrors,
                        isDisposable: this.isDisposableEmail(email),
                        formatted: this.formatEmail(email),
                        mailtoLink: this.generateEmailLink(email)
                    };
                };
                
                // 批量验证邮箱
                this.batchValidateEmails = async (emails) => {
                    const results = [];
                    
                    for (const email of emails) {
                        try {
                            const result = await this.validateEmail(email);
                            results.push({ email, ...result });
                        } catch (error) {
                            results.push({ email, isValid: false, errors: [error.message] });
                        }
                    }
                    
                    return results;
                };
                
                // 记录验证统计
                this.recordValidationStatistics = (email, isValid) => {
                    this.emailStatistics.totalEmails++;
                    
                    if (isValid) {
                        this.emailStatistics.validEmails++;
                        
                        // 记录域名分布
                        const domain = this.extractDomain(email);
                        if (domain) {
                            const count = this.emailStatistics.domainDistribution.get(domain) || 0;
                            this.emailStatistics.domainDistribution.set(domain, count + 1);
                        }
                    } else {
                        this.emailStatistics.invalidEmails++;
                    }
                };
                
                // 记录验证时间
                this.recordValidationTime = (duration) => {
                    this.emailStatistics.averageValidationTime = 
                        (this.emailStatistics.averageValidationTime + duration) / 2;
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.emailConfig.enableValidation,
                    validate: (email) => this.validateEmail(email),
                    getErrors: () => this.enhancedState.validationErrors
                };
            }
            
            addAutoCompleteFeatures() {
                // 自动完成功能
                this.autoCompleteManager = {
                    enabled: this.emailConfig.enableAutoComplete,
                    getSuggestions: (partial) => this.getDomainSuggestions(partial),
                    getDomainSuggestions: () => this.domainSuggestions
                };
            }
        };
        
        // 创建增强的表单邮箱字段
        this.EnhancedFormEmailField = class extends this.EnhancedEmailField {
            static template = "web.FormEmailField";
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.emailConfig.enableValidation,
            rules: this.validationRules
        };
    }
    
    // 设置自动完成
    setupAutoComplete() {
        this.autoCompleteConfig = {
            enabled: this.emailConfig.enableAutoComplete,
            domainSuggestions: this.domainSuggestions,
            minLength: 1
        };
    }
    
    // 设置格式化系统
    setupFormattingSystem() {
        this.formattingConfig = {
            enabled: this.emailConfig.enableFormatting,
            autoLowercase: true,
            trimSpaces: true
        };
    }
    
    // 创建邮箱字段
    createEmailField(props, isForm = false) {
        if (isForm) {
            return new this.EnhancedFormEmailField(props);
        }
        return new this.EnhancedEmailField(props);
    }
    
    // 注册域名建议
    registerDomainSuggestion(domain) {
        if (!this.domainSuggestions.includes(domain)) {
            this.domainSuggestions.push(domain);
        }
    }
    
    // 注册一次性邮箱域名
    registerDisposableDomain(domain) {
        this.disposableDomains.add(domain.toLowerCase());
    }
    
    // 批量验证邮箱列表
    batchValidateEmailList(emails) {
        const results = [];
        
        for (const email of emails) {
            try {
                const field = this.createEmailField({});
                const result = field.validateEmail(email);
                results.push({ email, ...result });
            } catch (error) {
                results.push({ email, isValid: false, errors: [error.message] });
            }
        }
        
        return results;
    }
    
    // 获取热门域名
    getPopularDomains(limit = 10) {
        const sorted = Array.from(this.emailStatistics.domainDistribution.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, limit);
        
        return sorted.map(([domain, count]) => ({ domain, count }));
    }
    
    // 获取邮箱统计
    getEmailStatistics() {
        return {
            ...this.emailStatistics,
            domainCount: this.emailStatistics.domainDistribution.size,
            validationRate: this.emailStatistics.validEmails / Math.max(this.emailStatistics.totalEmails, 1) * 100,
            disposableDomainCount: this.disposableDomains.size
        };
    }
    
    // 导出配置
    exportConfiguration() {
        return {
            validationRules: this.validationRules,
            domainSuggestions: this.domainSuggestions,
            disposableDomains: Array.from(this.disposableDomains)
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理域名和统计
        this.domainSuggestions = [];
        this.disposableDomains.clear();
        
        // 重置统计
        this.emailStatistics = {
            totalEmails: 0,
            validEmails: 0,
            invalidEmails: 0,
            domainDistribution: new Map(),
            averageValidationTime: 0
        };
    }
}

// 使用示例
const emailManager = new EmailFieldManager();

// 创建邮箱字段
const emailField = emailManager.createEmailField({
    name: 'email',
    record: {
        data: { email: '<EMAIL>' },
        fields: { email: { type: 'char' } }
    },
    placeholder: 'Enter email address'
});

// 创建表单邮箱字段
const formEmailField = emailManager.createEmailField({
    name: 'contact_email',
    record: {
        data: { contact_email: '' },
        fields: { contact_email: { type: 'char' } }
    }
}, true);

// 注册自定义域名建议
emailManager.registerDomainSuggestion('company.com');

// 获取统计信息
const stats = emailManager.getEmailStatistics();
console.log('Email field statistics:', stats);
```

## 技术特点

### 1. 专用设计
- **邮箱专用**: 专门为邮箱地址设计的字段
- **输入钩子**: 使用输入字段钩子管理状态
- **占位符**: 支持占位符文本配置
- **模板分离**: 基础和表单使用不同模板

### 2. 双重变体
- **基础字段**: EmailField基础邮箱字段
- **表单字段**: FormEmailField表单邮箱字段
- **功能继承**: 表单字段继承基础字段功能
- **模板差异**: 使用不同的显示模板

### 3. 简洁高效
- **最小实现**: 最简化的代码实现
- **专注功能**: 专注于邮箱输入功能
- **轻量级**: 极小的资源占用
- **快速响应**: 快速的输入响应

### 4. 标准化
- **标准属性**: 遵循标准字段属性规范
- **类型支持**: 仅支持char类型
- **注册规范**: 标准的字段注册方式
- **接口一致**: 一致的组件接口

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **类继承**: FormEmailField继承EmailField
- **功能继承**: 继承所有基础功能
- **模板重写**: 重写显示模板

### 2. 工厂模式 (Factory Pattern)
- **字段工厂**: 创建不同类型的邮箱字段
- **组件工厂**: 创建邮箱组件
- **配置工厂**: 创建字段配置

### 3. 策略模式 (Strategy Pattern)
- **验证策略**: 不同的邮箱验证策略
- **格式策略**: 不同的格式化策略
- **显示策略**: 不同的显示策略

### 4. 钩子模式 (Hook Pattern)
- **输入钩子**: 使用输入字段钩子
- **状态钩子**: 管理输入状态
- **生命周期**: 管理组件生命周期

## 注意事项

1. **邮箱验证**: 确保邮箱地址格式的正确性
2. **用户体验**: 提供清晰的邮箱输入体验
3. **性能考虑**: 避免复杂的邮箱验证逻辑
4. **隐私保护**: 保护用户邮箱地址隐私

## 扩展建议

1. **邮箱验证**: 添加实时邮箱验证功能
2. **域名建议**: 支持智能域名建议
3. **格式化**: 添加邮箱格式化功能
4. **链接生成**: 支持mailto链接生成
5. **批量处理**: 支持批量邮箱处理

该邮箱字段为Odoo Web客户端提供了专门的邮箱地址输入功能，通过简洁的设计和双重变体确保了在不同场景下的良好适用性和用户体验。

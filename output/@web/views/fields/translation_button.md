# TranslationButton - 翻译按钮

## 概述

`translation_button.js` 是 Odoo Web 客户端字段系统的翻译按钮组件，负责提供字段翻译功能的用户界面。该模块包含69行代码，是一个OWL组件，专门用于在多语言环境中为可翻译字段提供翻译编辑功能，具备多语言检测、翻译对话框、记录保存、语言显示等特性，是国际化字段系统的重要UI组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/translation_button.js`
- **行数**: 69
- **模块**: `@web/views/fields/translation_button`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/localization'           // 本地化服务
'@web/core/utils/hooks'                 // 工具钩子
'@web/core/user'                        // 用户服务
'@web/views/fields/translation_dialog'  // 翻译对话框
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 翻译对话框钩子

```javascript
function useTranslationDialog() {
    const addDialog = useOwnedDialogs();

    async function openTranslationDialog({ record, fieldName }) {
        const saved = await record.save();
        if (!saved) {
            return;
        }
        const { resModel, resId } = record;

        addDialog(TranslationDialog, {
            fieldName: fieldName,
            resId: resId,
            resModel: resModel,
            userLanguageValue: record.data[fieldName] || "",
            isComingFromTranslationAlert: false,
            onSave: async () => {
                await record.load();
            },
        });
    }

    return openTranslationDialog;
}
```

**对话框钩子功能**:
- **记录保存**: 打开对话框前先保存记录
- **对话框创建**: 创建翻译对话框实例
- **参数传递**: 传递字段和记录信息
- **保存回调**: 处理翻译保存后的记录重载

### 2. 翻译按钮组件

```javascript
const TranslationButton = class TranslationButton extends Component {
    static template = "web.TranslationButton";
    static props = {
        fieldName: { type: String },
        record: { type: Object },
    };

    setup() {
        this.translationDialog = useTranslationDialog();
    }

    get isMultiLang() {
        return localization.multiLang;
    }
    
    get lang() {
        return new Intl.Locale(user.lang).language.toUpperCase();
    }

    onClick() {
        const { fieldName, record } = this.props;
        this.translationDialog({ fieldName, record });
    }
}
```

**按钮组件功能**:
- **多语言检测**: 检测系统是否启用多语言
- **语言显示**: 显示当前用户语言
- **点击处理**: 处理按钮点击事件
- **对话框触发**: 触发翻译对话框

### 3. 语言信息获取

```javascript
get isMultiLang() {
    return localization.multiLang;
}

get lang() {
    return new Intl.Locale(user.lang).language.toUpperCase();
}
```

**语言信息功能**:
- **多语言状态**: 获取系统多语言启用状态
- **当前语言**: 获取用户当前语言代码
- **语言格式化**: 格式化语言代码为大写显示
- **国际化支持**: 使用标准Intl API

## 使用场景

### 1. 翻译按钮管理器

```javascript
// 翻译按钮管理器
class TranslationButtonManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置翻译配置
        this.translationConfig = {
            enableMultiLang: true,
            enableAutoSave: true,
            enableTranslationCache: true,
            enableLanguageDetection: true,
            supportedLanguages: ['en', 'fr', 'es', 'de', 'zh'],
            defaultLanguage: 'en',
            translationTimeout: 30000,
            maxTranslations: 50
        };
        
        // 设置翻译缓存
        this.translationCache = new Map();
        
        // 设置语言信息
        this.languageInfo = new Map();
        
        // 设置翻译统计
        this.translationStatistics = {
            totalTranslations: 0,
            successfulTranslations: 0,
            failedTranslations: 0,
            cacheHits: 0,
            averageTranslationTime: 0
        };
        
        this.initializeTranslationSystem();
    }
    
    // 初始化翻译系统
    initializeTranslationSystem() {
        // 创建增强的翻译按钮
        this.createEnhancedTranslationButton();
        
        // 设置语言检测
        this.setupLanguageDetection();
        
        // 设置翻译缓存
        this.setupTranslationCache();
        
        // 设置多语言支持
        this.setupMultiLanguageSupport();
    }
    
    // 创建增强的翻译按钮
    createEnhancedTranslationButton() {
        const originalButton = TranslationButton;
        
        this.EnhancedTranslationButton = class extends originalButton {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加缓存功能
                this.addCacheFeatures();
                
                // 添加统计功能
                this.addStatisticsFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展属性
                this.enhancedProps = {
                    ...this.props,
                    enablePreview: true,
                    enableAutoTranslate: false,
                    enableTranslationHistory: true,
                    customLanguages: []
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的点击处理
                this.enhancedOnClick = async () => {
                    const startTime = performance.now();
                    
                    try {
                        // 检查多语言状态
                        if (!this.isMultiLang) {
                            this.showMultiLangWarning();
                            return;
                        }
                        
                        // 检查字段是否可翻译
                        if (!this.isFieldTranslatable()) {
                            this.showNotTranslatableWarning();
                            return;
                        }
                        
                        // 预加载翻译数据
                        await this.preloadTranslationData();
                        
                        // 执行原始点击处理
                        await this.onClick();
                        
                        // 记录成功
                        this.translationStatistics.successfulTranslations++;
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordTranslationTime(endTime - startTime);
                        
                    } catch (error) {
                        this.handleTranslationError(error);
                        this.translationStatistics.failedTranslations++;
                    }
                };
                
                // 检查字段是否可翻译
                this.isFieldTranslatable = () => {
                    const fieldInfo = this.getFieldInfo();
                    return fieldInfo && fieldInfo.translate === true;
                };
                
                // 获取字段信息
                this.getFieldInfo = () => {
                    const { record, fieldName } = this.props;
                    return record.fields[fieldName];
                };
                
                // 预加载翻译数据
                this.preloadTranslationData = async () => {
                    const { record, fieldName } = this.props;
                    const cacheKey = `${record.resModel}_${record.resId}_${fieldName}`;
                    
                    if (this.translationCache.has(cacheKey)) {
                        this.translationStatistics.cacheHits++;
                        return this.translationCache.get(cacheKey);
                    }
                    
                    // 加载翻译数据
                    const translationData = await this.loadTranslationData();
                    this.translationCache.set(cacheKey, translationData);
                    
                    return translationData;
                };
                
                // 加载翻译数据
                this.loadTranslationData = async () => {
                    // 实现翻译数据加载逻辑
                    console.log('Loading translation data');
                    return {};
                };
                
                // 获取支持的语言
                this.getSupportedLanguages = () => {
                    return this.translationConfig.supportedLanguages.map(lang => ({
                        code: lang,
                        name: this.getLanguageName(lang),
                        isActive: lang === this.getCurrentLanguage()
                    }));
                };
                
                // 获取语言名称
                this.getLanguageName = (langCode) => {
                    const languageNames = {
                        'en': 'English',
                        'fr': 'Français',
                        'es': 'Español',
                        'de': 'Deutsch',
                        'zh': '中文'
                    };
                    
                    return languageNames[langCode] || langCode.toUpperCase();
                };
                
                // 获取当前语言
                this.getCurrentLanguage = () => {
                    return new Intl.Locale(user.lang).language;
                };
                
                // 切换语言
                this.switchLanguage = async (langCode) => {
                    try {
                        // 实现语言切换逻辑
                        console.log('Switching to language:', langCode);
                        
                        // 重新加载翻译数据
                        await this.preloadTranslationData();
                        
                    } catch (error) {
                        console.error('Language switch error:', error);
                    }
                };
                
                // 获取翻译状态
                this.getTranslationStatus = () => {
                    const { record, fieldName } = this.props;
                    const fieldValue = record.data[fieldName];
                    
                    return {
                        hasValue: Boolean(fieldValue),
                        isEmpty: !fieldValue || fieldValue.trim() === '',
                        needsTranslation: this.needsTranslation(),
                        isTranslated: this.isTranslated()
                    };
                };
                
                // 检查是否需要翻译
                this.needsTranslation = () => {
                    // 实现翻译需求检查逻辑
                    return true;
                };
                
                // 检查是否已翻译
                this.isTranslated = () => {
                    // 实现翻译状态检查逻辑
                    return false;
                };
                
                // 获取翻译进度
                this.getTranslationProgress = () => {
                    const supportedLangs = this.getSupportedLanguages();
                    const translatedCount = supportedLangs.filter(lang => 
                        this.hasTranslationForLanguage(lang.code)
                    ).length;
                    
                    return {
                        total: supportedLangs.length,
                        translated: translatedCount,
                        percentage: (translatedCount / supportedLangs.length) * 100
                    };
                };
                
                // 检查语言是否有翻译
                this.hasTranslationForLanguage = (langCode) => {
                    // 实现语言翻译检查逻辑
                    return Math.random() > 0.5; // 示例
                };
                
                // 批量翻译
                this.batchTranslate = async (targetLanguages) => {
                    const results = [];
                    
                    for (const lang of targetLanguages) {
                        try {
                            const result = await this.translateToLanguage(lang);
                            results.push({ language: lang, success: true, result });
                        } catch (error) {
                            results.push({ language: lang, success: false, error });
                        }
                    }
                    
                    return results;
                };
                
                // 翻译到指定语言
                this.translateToLanguage = async (langCode) => {
                    // 实现语言翻译逻辑
                    console.log('Translating to:', langCode);
                    return `Translated to ${langCode}`;
                };
                
                // 显示多语言警告
                this.showMultiLangWarning = () => {
                    console.warn('Multi-language is not enabled');
                };
                
                // 显示不可翻译警告
                this.showNotTranslatableWarning = () => {
                    console.warn('Field is not translatable');
                };
                
                // 处理翻译错误
                this.handleTranslationError = (error) => {
                    console.error('Translation error:', error);
                };
                
                // 记录翻译时间
                this.recordTranslationTime = (duration) => {
                    this.translationStatistics.totalTranslations++;
                    this.translationStatistics.averageTranslationTime = 
                        (this.translationStatistics.averageTranslationTime * 
                         (this.translationStatistics.totalTranslations - 1) + duration) / 
                        this.translationStatistics.totalTranslations;
                };
            }
            
            addCacheFeatures() {
                // 缓存功能
                this.cacheManager = {
                    enabled: this.translationConfig.enableTranslationCache,
                    
                    get: (key) => this.translationCache.get(key),
                    set: (key, value) => this.translationCache.set(key, value),
                    clear: () => this.translationCache.clear(),
                    
                    generateKey: (record, fieldName, lang) => {
                        return `${record.resModel}_${record.resId}_${fieldName}_${lang}`;
                    }
                };
            }
            
            addStatisticsFeatures() {
                // 统计功能
                this.statisticsManager = {
                    getStats: () => this.translationStatistics,
                    resetStats: () => {
                        this.translationStatistics = {
                            totalTranslations: 0,
                            successfulTranslations: 0,
                            failedTranslations: 0,
                            cacheHits: 0,
                            averageTranslationTime: 0
                        };
                    }
                };
            }
            
            // 重写原始方法
            onClick() {
                return this.enhancedOnClick();
            }
            
            // 获取增强的语言信息
            get enhancedLang() {
                const currentLang = this.getCurrentLanguage();
                return {
                    code: currentLang,
                    name: this.getLanguageName(currentLang),
                    display: currentLang.toUpperCase(),
                    isSupported: this.translationConfig.supportedLanguages.includes(currentLang)
                };
            }
            
            // 获取翻译按钮状态
            get buttonState() {
                const status = this.getTranslationStatus();
                const progress = this.getTranslationProgress();
                
                return {
                    ...status,
                    progress: progress,
                    isEnabled: this.isMultiLang && this.isFieldTranslatable(),
                    currentLanguage: this.enhancedLang
                };
            }
        };
        
        // 增强的翻译对话框钩子
        this.enhancedUseTranslationDialog = () => {
            const originalHook = useTranslationDialog();
            
            return async (params) => {
                const startTime = performance.now();
                
                try {
                    // 预处理
                    await this.preprocessTranslationDialog(params);
                    
                    // 执行原始对话框
                    const result = await originalHook(params);
                    
                    // 后处理
                    await this.postprocessTranslationDialog(params, result);
                    
                    // 记录性能
                    const endTime = performance.now();
                    this.recordDialogTime(endTime - startTime);
                    
                    return result;
                    
                } catch (error) {
                    this.handleDialogError(error);
                    throw error;
                }
            };
        };
    }
    
    // 预处理翻译对话框
    preprocessTranslationDialog = async (params) => {
        // 实现预处理逻辑
        console.log('Preprocessing translation dialog');
    };
    
    // 后处理翻译对话框
    postprocessTranslationDialog = async (params, result) => {
        // 实现后处理逻辑
        console.log('Postprocessing translation dialog');
    };
    
    // 处理对话框错误
    handleDialogError(error) {
        console.error('Translation dialog error:', error);
    }
    
    // 记录对话框时间
    recordDialogTime(duration) {
        console.log(`Translation dialog took ${duration}ms`);
    }
    
    // 设置语言检测
    setupLanguageDetection() {
        this.languageDetector = {
            detectUserLanguage: () => {
                return navigator.language || this.translationConfig.defaultLanguage;
            },
            
            detectContentLanguage: (text) => {
                // 实现内容语言检测逻辑
                return 'en';
            }
        };
    }
    
    // 设置翻译缓存
    setupTranslationCache() {
        this.cacheConfig = {
            maxSize: this.translationConfig.maxTranslations,
            ttl: 3600000, // 1小时
            cleanupInterval: 300000 // 5分钟
        };
        
        // 定期清理缓存
        setInterval(() => {
            this.cleanupTranslationCache();
        }, this.cacheConfig.cleanupInterval);
    }
    
    // 清理翻译缓存
    cleanupTranslationCache() {
        if (this.translationCache.size > this.cacheConfig.maxSize) {
            const entries = Array.from(this.translationCache.entries());
            const toDelete = entries.slice(0, entries.length - this.cacheConfig.maxSize);
            
            for (const [key] of toDelete) {
                this.translationCache.delete(key);
            }
        }
    }
    
    // 设置多语言支持
    setupMultiLanguageSupport() {
        this.multiLangConfig = {
            enableAutoDetection: true,
            enableLanguageSwitching: true,
            enableTranslationSuggestions: true
        };
    }
    
    // 创建翻译按钮
    createTranslationButton(props) {
        return new this.EnhancedTranslationButton(props);
    }
    
    // 创建翻译对话框钩子
    createTranslationDialogHook() {
        return this.enhancedUseTranslationDialog();
    }
    
    // 获取翻译统计
    getTranslationStatistics() {
        return {
            ...this.translationStatistics,
            cacheSize: this.translationCache.size,
            supportedLanguages: this.translationConfig.supportedLanguages.length
        };
    }
    
    // 清理所有缓存
    clearAllCaches() {
        this.translationCache.clear();
        this.languageInfo.clear();
    }
    
    // 销毁管理器
    destroy() {
        // 清理缓存
        this.translationCache.clear();
        this.languageInfo.clear();
        
        // 重置统计
        this.translationStatistics = {
            totalTranslations: 0,
            successfulTranslations: 0,
            failedTranslations: 0,
            cacheHits: 0,
            averageTranslationTime: 0
        };
    }
}

// 使用示例
const translationManager = new TranslationButtonManager();

// 创建翻译按钮
const translationButton = translationManager.createTranslationButton({
    fieldName: 'description',
    record: { resModel: 'product.product', resId: 1, data: { description: 'Product description' } }
});

// 创建翻译对话框钩子
const translationDialogHook = translationManager.createTranslationDialogHook();

// 获取统计信息
const stats = translationManager.getTranslationStatistics();
console.log('Translation statistics:', stats);
```

## 技术特点

### 1. 国际化支持
- **多语言检测**: 自动检测系统多语言状态
- **语言显示**: 显示当前用户语言
- **标准化**: 使用标准Intl API
- **本地化**: 完整的本地化支持

### 2. 用户体验
- **直观界面**: 直观的翻译按钮界面
- **即时反馈**: 提供即时的操作反馈
- **状态显示**: 显示翻译状态和进度
- **错误处理**: 友好的错误处理

### 3. 数据管理
- **自动保存**: 打开对话框前自动保存记录
- **数据重载**: 翻译保存后重载记录数据
- **状态同步**: 保持数据状态同步
- **缓存优化**: 翻译数据的缓存优化

### 4. 扩展性
- **组件化**: 高度组件化的设计
- **钩子支持**: 提供钩子函数支持
- **可定制**: 支持自定义配置
- **模块化**: 模块化的功能组织

## 设计模式

### 1. 组件模式 (Component Pattern)
- **UI组件**: 封装翻译按钮UI
- **状态管理**: 管理按钮状态
- **事件处理**: 处理用户交互

### 2. 钩子模式 (Hook Pattern)
- **功能钩子**: 提供翻译对话框钩子
- **状态钩子**: 管理翻译状态
- **生命周期**: 处理组件生命周期

### 3. 策略模式 (Strategy Pattern)
- **语言策略**: 不同语言的处理策略
- **翻译策略**: 不同的翻译处理策略
- **缓存策略**: 不同的缓存策略

### 4. 观察者模式 (Observer Pattern)
- **状态观察**: 观察多语言状态变化
- **数据观察**: 观察翻译数据变化
- **用户观察**: 观察用户语言变化

## 注意事项

1. **权限检查**: 确保用户有翻译权限
2. **数据一致性**: 保持翻译数据的一致性
3. **性能考虑**: 避免频繁的翻译数据查询
4. **用户体验**: 提供流畅的翻译体验

## 扩展建议

1. **自动翻译**: 集成自动翻译服务
2. **翻译历史**: 添加翻译历史记录
3. **批量翻译**: 支持批量翻译功能
4. **翻译建议**: 提供翻译建议功能
5. **离线翻译**: 支持离线翻译模式

该翻译按钮组件为Odoo Web客户端提供了完整的字段翻译功能，通过直观的用户界面和完善的多语言支持确保了国际化应用的易用性。

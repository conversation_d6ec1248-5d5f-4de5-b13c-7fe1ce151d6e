# RelationalUtils - 关系字段工具

## 概述

`relational_utils.js` 是 Odoo Web 客户端字段系统的关系字段工具模块，负责提供关系字段（many2one、one2many、many2many）的通用功能和组件。该模块包含872行代码，是一个综合性的工具模块，专门用于处理关系字段的选择创建、权限管理、对话框、自动完成、记录观察等功能，具备权限控制、对话框管理、数据操作、视图集成等特性，是关系字段系统的核心工具库。

## 文件信息
- **路径**: `/web/static/src/views/fields/relational_utils.js`
- **行数**: 872
- **模块**: `@web/views/fields/relational_utils`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'                    // 翻译服务
'@web/core/autocomplete/autocomplete'           // 自动完成
'@web/core/context'                             // 上下文
'@web/core/dialog/dialog'                       // 对话框
'@web/core/domain'                              // 域
'@web/core/network/rpc'                         // RPC网络
'@web/core/utils/cache'                         // 缓存工具
'@web/core/utils/hooks'                         // 工具钩子
'@web/core/utils/xml'                           // XML工具
'@web/views/form/form_arch_parser'              // 表单架构解析器
'@web/views/form/form_controller'               // 表单控制器
'@web/views/form/form_renderer'                 // 表单渲染器
'@web/model/relational_model/utils'             // 关系模型工具
'@web/views/utils'                              // 视图工具
'@web/views/view_button/view_button'            // 视图按钮
'@web/views/view_button/view_button_hook'       // 视图按钮钩子
'@web/views/view_dialogs/form_view_dialog'      // 表单视图对话框
'@web/views/view_dialogs/select_create_dialog'  // 选择创建对话框
'@odoo/owl'                                     // OWL框架
```

## 核心功能

### 1. 选择创建钩子

```javascript
function useSelectCreate({ resModel, activeActions, onSelected, onCreateEdit, onUnselect }) {
    const addDialog = useOwnedDialogs();

    function selectCreate({ domain, context, filters, title }) {
        addDialog(SelectCreateDialog, {
            title: title || _t("Select records"),
            noCreate: !activeActions.create,
            multiSelect: "link" in activeActions ? activeActions.link : false,
            resModel,
            context,
            domain,
            onSelected,
            onCreateEdit: () => onCreateEdit({ context }),
            dynamicFilters: filters,
            onUnselect,
        });
    }
    return selectCreate;
}
```

**选择创建功能**:
- **对话框管理**: 管理选择创建对话框
- **权限控制**: 根据权限控制创建功能
- **多选支持**: 支持多选记录
- **回调处理**: 处理选择、创建、取消选择回调

### 2. 活动操作钩子

```javascript
function useActiveActions({
    fieldType,
    subViewActiveActions = {},
    crudOptions = {},
    getEvalParams = () => ({}),
}) {
    const compute = ({ evalContext = {}, readonly = true }) => {
        const result = { type: fieldType, onDelete: null };
        const evalAction = (actionName) => evals[actionName](evalContext);

        result.create = !readonly && evalAction("create");
        result.createEdit = !readonly && result.create && crudOptions.createEdit;
        result.edit = crudOptions.edit;
        result.delete = !readonly && evalAction("delete");

        if (isMany2Many) {
            result.link = !readonly && evalAction("link");
            result.unlink = !readonly && evalAction("unlink");
            result.write = evalAction("write");
        }

        return result;
    };
    // ...
}
```

**活动操作功能**:
- **权限计算**: 计算字段的各种操作权限
- **动态评估**: 基于上下文动态评估权限
- **字段类型**: 根据字段类型提供不同权限
- **只读控制**: 处理只读状态的权限控制

### 3. 关系字段自动完成

```javascript
function useOpenMany2XRecord({ resModel, activeActions, fieldString, isToMany }) {
    const addDialog = useOwnedDialogs();
    const notification = useService("notification");

    function openRecord(record, mode = "readonly") {
        const props = {
            resModel,
            resId: record.resId,
            context: record.context,
            title: fieldString,
            mode,
            onRecordSaved: (record) => {
                // Handle record save
            },
            onRecordDeleted: () => {
                // Handle record deletion
            },
        };
        addDialog(FormViewDialog, props);
    }

    return openRecord;
}
```

**记录打开功能**:
- **对话框打开**: 在对话框中打开记录
- **模式控制**: 支持只读和编辑模式
- **保存处理**: 处理记录保存事件
- **删除处理**: 处理记录删除事件

## 使用场景

### 1. 关系字段工具管理器

```javascript
// 关系字段工具管理器
class RelationalUtilsManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置工具配置
        this.utilsConfig = {
            enableCaching: true,
            enablePermissionCheck: true,
            enableAutoComplete: true,
            enableDialogManagement: true,
            cacheSize: 1000,
            autoCompleteDelay: 300,
            maxRecordsPerPage: 80,
            enableBatchOperations: true
        };
        
        // 设置缓存系统
        this.cacheManager = new Map();
        
        // 设置权限缓存
        this.permissionCache = new Map();
        
        // 设置对话框管理
        this.dialogManager = new Map();
        
        // 设置工具统计
        this.utilsStatistics = {
            totalOperations: 0,
            cacheHits: 0,
            cacheMisses: 0,
            dialogsOpened: 0,
            recordsSelected: 0,
            averageOperationTime: 0
        };
        
        this.initializeUtilsSystem();
    }
    
    // 初始化工具系统
    initializeUtilsSystem() {
        // 创建增强的关系字段工具
        this.createEnhancedRelationalUtils();
        
        // 设置缓存系统
        this.setupCacheSystem();
        
        // 设置权限系统
        this.setupPermissionSystem();
        
        // 设置对话框系统
        this.setupDialogSystem();
    }
    
    // 创建增强的关系字段工具
    createEnhancedRelationalUtils() {
        // 增强的选择创建钩子
        this.enhancedUseSelectCreate = (params) => {
            const originalHook = useSelectCreate(params);
            
            return {
                ...originalHook,
                
                // 增强的选择创建
                enhancedSelectCreate: async (options) => {
                    const startTime = performance.now();
                    
                    try {
                        // 检查权限
                        if (!this.checkPermission('select_create', params.resModel)) {
                            throw new Error('No permission to select/create records');
                        }
                        
                        // 应用缓存
                        const cacheKey = this.generateCacheKey('select_create', options);
                        if (this.utilsConfig.enableCaching && this.cacheManager.has(cacheKey)) {
                            this.utilsStatistics.cacheHits++;
                            return this.cacheManager.get(cacheKey);
                        }
                        
                        // 执行选择创建
                        const result = await originalHook(options);
                        
                        // 缓存结果
                        if (this.utilsConfig.enableCaching) {
                            this.cacheManager.set(cacheKey, result);
                            this.utilsStatistics.cacheMisses++;
                        }
                        
                        // 记录统计
                        this.utilsStatistics.dialogsOpened++;
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordPerformance('select_create', endTime - startTime);
                        
                        return result;
                        
                    } catch (error) {
                        this.handleOperationError('select_create', error);
                        throw error;
                    }
                },
                
                // 批量选择
                batchSelect: async (records) => {
                    const results = [];
                    
                    for (const record of records) {
                        try {
                            const result = await this.selectRecord(record);
                            results.push(result);
                        } catch (error) {
                            console.error('Batch select error:', error);
                        }
                    }
                    
                    return results;
                },
                
                // 智能搜索
                smartSearch: async (query, options = {}) => {
                    return this.performSmartSearch(query, params.resModel, options);
                },
                
                // 获取推荐记录
                getRecommendations: async (context = {}) => {
                    return this.getRecordRecommendations(params.resModel, context);
                }
            };
        };
        
        // 增强的活动操作钩子
        this.enhancedUseActiveActions = (params) => {
            const originalHook = useActiveActions(params);
            
            return {
                ...originalHook,
                
                // 增强的权限检查
                enhancedCheckPermission: (action, context = {}) => {
                    const cacheKey = `permission_${action}_${JSON.stringify(context)}`;
                    
                    if (this.permissionCache.has(cacheKey)) {
                        return this.permissionCache.get(cacheKey);
                    }
                    
                    const hasPermission = this.checkActionPermission(action, params, context);
                    this.permissionCache.set(cacheKey, hasPermission);
                    
                    return hasPermission;
                },
                
                // 批量权限检查
                batchCheckPermissions: (actions, context = {}) => {
                    const results = {};
                    
                    for (const action of actions) {
                        results[action] = this.enhancedCheckPermission(action, context);
                    }
                    
                    return results;
                },
                
                // 获取可用操作
                getAvailableActions: (context = {}) => {
                    const standardActions = ['create', 'read', 'write', 'delete', 'link', 'unlink'];
                    return standardActions.filter(action => 
                        this.enhancedCheckPermission(action, context)
                    );
                },
                
                // 动态更新权限
                updatePermissions: (newContext) => {
                    // 清理相关缓存
                    this.clearPermissionCache();
                    
                    // 重新计算权限
                    return this.enhancedCheckPermission('all', newContext);
                }
            };
        };
        
        // 增强的记录打开钩子
        this.enhancedUseOpenMany2XRecord = (params) => {
            const originalHook = useOpenMany2XRecord(params);
            
            return {
                ...originalHook,
                
                // 增强的记录打开
                enhancedOpenRecord: async (record, mode = 'readonly', options = {}) => {
                    const startTime = performance.now();
                    
                    try {
                        // 检查权限
                        if (!this.checkRecordPermission(record, mode)) {
                            throw new Error(`No permission to ${mode} record`);
                        }
                        
                        // 预加载相关数据
                        if (options.preload) {
                            await this.preloadRecordData(record);
                        }
                        
                        // 执行原始打开
                        const result = await originalHook(record, mode);
                        
                        // 记录统计
                        this.utilsStatistics.dialogsOpened++;
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordPerformance('open_record', endTime - startTime);
                        
                        return result;
                        
                    } catch (error) {
                        this.handleOperationError('open_record', error);
                        throw error;
                    }
                },
                
                // 批量打开记录
                batchOpenRecords: async (records, mode = 'readonly') => {
                    const results = [];
                    
                    for (const record of records) {
                        try {
                            const result = await this.enhancedOpenRecord(record, mode);
                            results.push(result);
                        } catch (error) {
                            console.error('Batch open error:', error);
                        }
                    }
                    
                    return results;
                },
                
                // 智能模式选择
                smartModeSelection: (record, userRole) => {
                    // 根据用户角色和记录状态智能选择模式
                    if (userRole === 'admin') return 'edit';
                    if (record.state === 'draft') return 'edit';
                    return 'readonly';
                },
                
                // 记录预览
                previewRecord: async (record) => {
                    return this.generateRecordPreview(record);
                }
            };
        };
    }
    
    // 检查权限
    checkPermission(action, model) {
        // 实现权限检查逻辑
        return true;
    }
    
    // 检查操作权限
    checkActionPermission(action, params, context) {
        // 实现操作权限检查逻辑
        return true;
    }
    
    // 检查记录权限
    checkRecordPermission(record, mode) {
        // 实现记录权限检查逻辑
        return true;
    }
    
    // 生成缓存键
    generateCacheKey(operation, options) {
        return `${operation}_${JSON.stringify(options)}`;
    }
    
    // 执行智能搜索
    performSmartSearch = async (query, model, options) => {
        // 实现智能搜索逻辑
        console.log('Performing smart search:', query, model);
        return [];
    };
    
    // 获取记录推荐
    getRecordRecommendations = async (model, context) => {
        // 实现推荐逻辑
        console.log('Getting recommendations for:', model);
        return [];
    };
    
    // 选择记录
    selectRecord = async (record) => {
        // 实现记录选择逻辑
        this.utilsStatistics.recordsSelected++;
        return record;
    };
    
    // 预加载记录数据
    preloadRecordData = async (record) => {
        // 实现数据预加载逻辑
        console.log('Preloading data for record:', record.resId);
    };
    
    // 生成记录预览
    generateRecordPreview = async (record) => {
        // 实现记录预览生成逻辑
        return {
            id: record.resId,
            title: record.display_name,
            preview: 'Record preview content'
        };
    };
    
    // 处理操作错误
    handleOperationError(operation, error) {
        console.error(`${operation} error:`, error);
        this.utilsStatistics.totalOperations++;
    }
    
    // 记录性能
    recordPerformance(operation, duration) {
        this.utilsStatistics.totalOperations++;
        this.utilsStatistics.averageOperationTime = 
            (this.utilsStatistics.averageOperationTime * (this.utilsStatistics.totalOperations - 1) + duration) / 
            this.utilsStatistics.totalOperations;
    }
    
    // 设置缓存系统
    setupCacheSystem() {
        this.cacheConfig = {
            maxSize: this.utilsConfig.cacheSize,
            ttl: 300000, // 5分钟
            cleanupInterval: 60000 // 1分钟
        };
        
        // 定期清理缓存
        setInterval(() => {
            this.cleanupCache();
        }, this.cacheConfig.cleanupInterval);
    }
    
    // 清理缓存
    cleanupCache() {
        if (this.cacheManager.size > this.cacheConfig.maxSize) {
            const entries = Array.from(this.cacheManager.entries());
            const toDelete = entries.slice(0, entries.length - this.cacheConfig.maxSize);
            
            for (const [key] of toDelete) {
                this.cacheManager.delete(key);
            }
        }
    }
    
    // 清理权限缓存
    clearPermissionCache() {
        this.permissionCache.clear();
    }
    
    // 设置权限系统
    setupPermissionSystem() {
        this.permissionConfig = {
            enableCaching: true,
            cacheTimeout: 300000, // 5分钟
            enableRoleBasedAccess: true
        };
    }
    
    // 设置对话框系统
    setupDialogSystem() {
        this.dialogConfig = {
            enableStackManagement: true,
            maxDialogs: 5,
            enableAutoClose: true,
            autoCloseTimeout: 300000 // 5分钟
        };
    }
    
    // 创建选择创建钩子
    createSelectCreateHook(params) {
        return this.enhancedUseSelectCreate(params);
    }
    
    // 创建活动操作钩子
    createActiveActionsHook(params) {
        return this.enhancedUseActiveActions(params);
    }
    
    // 创建记录打开钩子
    createOpenRecordHook(params) {
        return this.enhancedUseOpenMany2XRecord(params);
    }
    
    // 获取工具统计
    getUtilsStatistics() {
        return {
            ...this.utilsStatistics,
            cacheSize: this.cacheManager.size,
            permissionCacheSize: this.permissionCache.size,
            dialogCount: this.dialogManager.size
        };
    }
    
    // 清理所有缓存
    clearAllCaches() {
        this.cacheManager.clear();
        this.permissionCache.clear();
        this.utilsStatistics.cacheHits = 0;
        this.utilsStatistics.cacheMisses = 0;
    }
    
    // 销毁管理器
    destroy() {
        // 清理缓存
        this.cacheManager.clear();
        this.permissionCache.clear();
        this.dialogManager.clear();
        
        // 重置统计
        this.utilsStatistics = {
            totalOperations: 0,
            cacheHits: 0,
            cacheMisses: 0,
            dialogsOpened: 0,
            recordsSelected: 0,
            averageOperationTime: 0
        };
    }
}

// 使用示例
const relationalManager = new RelationalUtilsManager();

// 创建选择创建钩子
const selectCreateHook = relationalManager.createSelectCreateHook({
    resModel: 'res.partner',
    activeActions: { create: true, link: true },
    onSelected: (records) => console.log('Selected:', records),
    onCreateEdit: (context) => console.log('Create/Edit:', context)
});

// 创建活动操作钩子
const activeActionsHook = relationalManager.createActiveActionsHook({
    fieldType: 'many2many',
    crudOptions: { create: true, delete: true }
});

// 创建记录打开钩子
const openRecordHook = relationalManager.createOpenRecordHook({
    resModel: 'res.partner',
    activeActions: { write: true },
    fieldString: 'Partners'
});

// 获取统计信息
const stats = relationalManager.getUtilsStatistics();
console.log('Relational utils statistics:', stats);
```

## 技术特点

### 1. 功能完整性
- **全面覆盖**: 覆盖关系字段的所有核心功能
- **模块化**: 高度模块化的功能组织
- **可复用**: 高度可复用的工具函数
- **标准化**: 标准化的接口和行为

### 2. 权限管理
- **细粒度**: 细粒度的权限控制
- **动态评估**: 基于上下文的动态权限评估
- **缓存优化**: 权限检查的缓存优化
- **角色支持**: 支持基于角色的权限控制

### 3. 性能优化
- **缓存机制**: 智能的缓存机制
- **批量操作**: 支持批量操作
- **懒加载**: 按需加载数据
- **内存管理**: 有效的内存使用管理

### 4. 用户体验
- **对话框管理**: 完善的对话框管理
- **自动完成**: 智能的自动完成功能
- **错误处理**: 友好的错误处理
- **即时反馈**: 提供即时的操作反馈

## 设计模式

### 1. 工厂模式 (Factory Pattern)
- **钩子工厂**: 创建不同类型的钩子函数
- **对话框工厂**: 创建不同类型的对话框
- **组件工厂**: 创建关系字段组件

### 2. 策略模式 (Strategy Pattern)
- **权限策略**: 不同的权限检查策略
- **操作策略**: 不同的操作处理策略
- **缓存策略**: 不同的缓存策略

### 3. 观察者模式 (Observer Pattern)
- **状态观察**: 观察记录状态变化
- **权限观察**: 观察权限变化
- **数据观察**: 观察数据变化

### 4. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 装饰基础功能
- **权限装饰**: 装饰权限检查
- **缓存装饰**: 装饰缓存功能

## 注意事项

1. **权限安全**: 确保权限检查的正确性和安全性
2. **性能考虑**: 避免频繁的权限检查和数据查询
3. **内存管理**: 合理使用缓存避免内存泄漏
4. **错误处理**: 完善的错误处理和用户提示

## 扩展建议

1. **智能推荐**: 添加智能记录推荐功能
2. **批量操作**: 增强批量操作功能
3. **离线支持**: 添加离线模式支持
4. **性能分析**: 添加性能分析工具
5. **自定义扩展**: 支持自定义扩展功能

该关系字段工具模块为Odoo Web客户端提供了完整的关系字段功能支持，通过模块化设计和完善的权限管理确保了关系字段的安全性和易用性。

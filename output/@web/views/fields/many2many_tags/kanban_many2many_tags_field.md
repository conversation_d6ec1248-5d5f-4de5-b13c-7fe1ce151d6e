# KanbanMany2ManyTagsField - 看板多对多标签字段

## 概述

`kanban_many2many_tags_field.js` 是 Odoo Web 客户端的看板多对多标签字段组件，是专门为看板视图优化的多对多标签字段。该模块包含29行代码，是一个继承自Many2ManyTagsField的专用组件，专门用于在看板视图中显示多对多标签，具备颜色过滤、点击禁用、看板优化等特性，是看板视图标签显示的专用组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/many2many_tags/kanban_many2many_tags_field.js`
- **行数**: 29
- **模块**: `@web/views/fields/many2many_tags/kanban_many2many_tags_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                    // 注册表
'@web/views/fields/many2many_tags/many2many_tags_field' // 基础多对多标签字段
```

## 核心功能

### 1. 组件定义

```javascript
const KanbanMany2ManyTagsField = class KanbanMany2ManyTagsField extends Many2ManyTagsField {
    static template = "web.KanbanMany2ManyTagsField";
}
```

**组件特性**:
- **继承基类**: 继承自Many2ManyTagsField
- **专用模板**: 使用KanbanMany2ManyTagsField专用模板
- **看板优化**: 专门为看板视图优化
- **简洁设计**: 最小化的代码实现

### 2. 标签过滤

```javascript
get tags() {
    return super.tags.reduce((kanbanTags, tag) => {
        if (tag.colorIndex !== 0) {
            delete tag.onClick;
            kanbanTags.push(tag);
        }
        return kanbanTags;
    }, []);
}
```

**过滤功能**:
- **颜色过滤**: 只显示有颜色的标签（colorIndex !== 0）
- **点击禁用**: 删除onClick事件，禁用标签点击
- **数组归约**: 使用reduce方法过滤标签
- **看板适配**: 适配看板视图的显示需求

### 3. 字段注册

```javascript
const kanbanMany2ManyTagsField = {
    ...many2ManyTagsField,
    component: KanbanMany2ManyTagsField,
};

registry.category("fields").add("kanban.many2many_tags", kanbanMany2ManyTagsField);
```

**注册功能**:
- **配置继承**: 继承基础字段的所有配置
- **组件替换**: 使用看板专用组件
- **看板注册**: 注册为kanban.many2many_tags字段类型
- **命名空间**: 使用kanban前缀区分看板字段

## 使用场景

### 1. 看板标签字段管理器

```javascript
// 看板标签字段管理器
class KanbanTagsFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置看板标签配置
        this.kanbanTagsConfig = {
            enableColorFiltering: true,
            enableClickDisabling: true,
            enableCompactDisplay: true,
            enableTooltips: true,
            enableAnimation: false,
            enableBadgeStyle: true,
            maxTagsDisplay: 5,
            hideEmptyTags: true
        };
        
        // 设置颜色过滤规则
        this.colorFilterRules = {
            showOnlyColored: true,
            excludeDefaultColor: true,
            minColorIndex: 1,
            maxColorIndex: 11,
            allowedColors: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
        };
        
        // 设置看板显示选项
        this.kanbanDisplayOptions = {
            compactMode: true,
            showColorOnly: false,
            enableTruncation: true,
            maxTextLength: 15,
            ellipsisText: '...',
            enableGrouping: false
        };
        
        // 设置看板标签统计
        this.kanbanTagsStatistics = {
            totalKanbanFields: 0,
            totalVisibleTags: 0,
            totalFilteredTags: 0,
            colorDistribution: new Map(),
            averageTagsPerCard: 0
        };
        
        this.initializeKanbanTagsSystem();
    }
    
    // 初始化看板标签系统
    initializeKanbanTagsSystem() {
        // 创建增强的看板多对多标签字段
        this.createEnhancedKanbanMany2ManyTagsField();
        
        // 设置颜色过滤系统
        this.setupColorFilteringSystem();
        
        // 设置显示优化系统
        this.setupDisplayOptimizationSystem();
        
        // 设置交互控制系统
        this.setupInteractionControlSystem();
    }
    
    // 创建增强的看板多对多标签字段
    createEnhancedKanbanMany2ManyTagsField() {
        const originalField = KanbanMany2ManyTagsField;
        
        this.EnhancedKanbanMany2ManyTagsField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加看板优化功能
                this.addKanbanOptimizationFeatures();
                
                // 添加显示控制功能
                this.addDisplayControlFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    displayMode: 'compact',
                    isHovered: false,
                    truncatedTags: new Set(),
                    visibleTagsCount: 0,
                    hiddenTagsCount: 0,
                    colorTheme: 'default'
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的标签获取
                this.enhancedGetTags = () => {
                    let tags = super.tags;
                    
                    // 应用颜色过滤
                    tags = this.applyColorFiltering(tags);
                    
                    // 应用显示限制
                    tags = this.applyDisplayLimits(tags);
                    
                    // 应用看板优化
                    tags = this.applyKanbanOptimizations(tags);
                    
                    // 记录统计
                    this.recordTagStatistics(tags);
                    
                    return tags;
                };
                
                // 应用颜色过滤
                this.applyColorFiltering = (tags) => {
                    if (!this.kanbanTagsConfig.enableColorFiltering) {
                        return tags;
                    }
                    
                    return tags.filter(tag => {
                        // 排除默认颜色
                        if (this.colorFilterRules.excludeDefaultColor && tag.colorIndex === 0) {
                            return false;
                        }
                        
                        // 检查颜色索引范围
                        if (tag.colorIndex < this.colorFilterRules.minColorIndex || 
                            tag.colorIndex > this.colorFilterRules.maxColorIndex) {
                            return false;
                        }
                        
                        // 检查允许的颜色
                        if (this.colorFilterRules.allowedColors.length > 0 && 
                            !this.colorFilterRules.allowedColors.includes(tag.colorIndex)) {
                            return false;
                        }
                        
                        return true;
                    });
                };
                
                // 应用显示限制
                this.applyDisplayLimits = (tags) => {
                    if (this.kanbanTagsConfig.maxTagsDisplay <= 0) {
                        return tags;
                    }
                    
                    const visibleTags = tags.slice(0, this.kanbanTagsConfig.maxTagsDisplay);
                    this.enhancedState.visibleTagsCount = visibleTags.length;
                    this.enhancedState.hiddenTagsCount = Math.max(0, tags.length - visibleTags.length);
                    
                    return visibleTags;
                };
                
                // 应用看板优化
                this.applyKanbanOptimizations = (tags) => {
                    return tags.map(tag => {
                        const optimizedTag = { ...tag };
                        
                        // 禁用点击事件
                        if (this.kanbanTagsConfig.enableClickDisabling) {
                            delete optimizedTag.onClick;
                        }
                        
                        // 文本截断
                        if (this.kanbanDisplayOptions.enableTruncation && 
                            tag.text.length > this.kanbanDisplayOptions.maxTextLength) {
                            optimizedTag.text = tag.text.substring(0, this.kanbanDisplayOptions.maxTextLength) + 
                                               this.kanbanDisplayOptions.ellipsisText;
                            optimizedTag.fullText = tag.text;
                            this.enhancedState.truncatedTags.add(tag.id);
                        }
                        
                        // 紧凑模式
                        if (this.kanbanDisplayOptions.compactMode) {
                            optimizedTag.compact = true;
                        }
                        
                        return optimizedTag;
                    });
                };
                
                // 获取隐藏标签信息
                this.getHiddenTagsInfo = () => {
                    if (this.enhancedState.hiddenTagsCount === 0) {
                        return null;
                    }
                    
                    return {
                        count: this.enhancedState.hiddenTagsCount,
                        text: `+${this.enhancedState.hiddenTagsCount} more`,
                        tooltip: `${this.enhancedState.hiddenTagsCount} additional tags`
                    };
                };
                
                // 获取标签工具提示
                this.getTagTooltip = (tag) => {
                    if (!this.kanbanTagsConfig.enableTooltips) {
                        return null;
                    }
                    
                    let tooltip = tag.fullText || tag.text;
                    
                    if (this.enhancedState.truncatedTags.has(tag.id)) {
                        tooltip = tag.fullText;
                    }
                    
                    return tooltip;
                };
                
                // 获取标签样式类
                this.getTagStyleClass = (tag) => {
                    const classes = ['o_tag'];
                    
                    if (this.kanbanTagsConfig.enableBadgeStyle) {
                        classes.push('badge');
                    }
                    
                    if (this.kanbanDisplayOptions.compactMode) {
                        classes.push('o_tag_compact');
                    }
                    
                    if (tag.colorIndex > 0) {
                        classes.push(`o_tag_color_${tag.colorIndex}`);
                    }
                    
                    return classes.join(' ');
                };
                
                // 获取看板标签信息
                this.getKanbanTagsInfo = () => {
                    const allTags = super.tags;
                    const visibleTags = this.enhancedGetTags();
                    
                    return {
                        totalTags: allTags.length,
                        visibleTags: visibleTags.length,
                        hiddenTags: this.enhancedState.hiddenTagsCount,
                        filteredTags: allTags.length - visibleTags.length - this.enhancedState.hiddenTagsCount,
                        truncatedTags: this.enhancedState.truncatedTags.size,
                        displayMode: this.enhancedState.displayMode,
                        colorTheme: this.enhancedState.colorTheme
                    };
                };
                
                // 切换显示模式
                this.toggleDisplayMode = () => {
                    this.enhancedState.displayMode = 
                        this.enhancedState.displayMode === 'compact' ? 'full' : 'compact';
                };
                
                // 设置颜色主题
                this.setColorTheme = (theme) => {
                    this.enhancedState.colorTheme = theme;
                };
                
                // 鼠标悬停处理
                this.onMouseEnter = () => {
                    this.enhancedState.isHovered = true;
                };
                
                // 鼠标离开处理
                this.onMouseLeave = () => {
                    this.enhancedState.isHovered = false;
                };
                
                // 记录标签统计
                this.recordTagStatistics = (tags) => {
                    this.kanbanTagsStatistics.totalVisibleTags += tags.length;
                    
                    // 记录颜色分布
                    tags.forEach(tag => {
                        const count = this.kanbanTagsStatistics.colorDistribution.get(tag.colorIndex) || 0;
                        this.kanbanTagsStatistics.colorDistribution.set(tag.colorIndex, count + 1);
                    });
                };
                
                // 导出看板标签
                this.exportKanbanTags = () => {
                    const tags = this.enhancedGetTags();
                    
                    return {
                        visible: tags.map(tag => ({
                            id: tag.id,
                            text: tag.text,
                            fullText: tag.fullText || tag.text,
                            colorIndex: tag.colorIndex,
                            isTruncated: this.enhancedState.truncatedTags.has(tag.id)
                        })),
                        hidden: this.getHiddenTagsInfo(),
                        statistics: this.getKanbanTagsInfo()
                    };
                };
                
                // 重置截断状态
                this.resetTruncation = () => {
                    this.enhancedState.truncatedTags.clear();
                };
                
                // 获取颜色统计
                this.getColorStatistics = () => {
                    const tags = this.enhancedGetTags();
                    const colorStats = new Map();
                    
                    tags.forEach(tag => {
                        const count = colorStats.get(tag.colorIndex) || 0;
                        colorStats.set(tag.colorIndex, count + 1);
                    });
                    
                    return Array.from(colorStats.entries()).map(([colorIndex, count]) => ({
                        colorIndex,
                        count,
                        percentage: (count / tags.length * 100).toFixed(1)
                    }));
                };
            }
            
            addKanbanOptimizationFeatures() {
                // 看板优化功能
                this.kanbanOptimizer = {
                    enabled: this.kanbanTagsConfig.enableCompactDisplay,
                    toggleMode: () => this.toggleDisplayMode(),
                    getInfo: () => this.getKanbanTagsInfo(),
                    export: () => this.exportKanbanTags()
                };
            }
            
            addDisplayControlFeatures() {
                // 显示控制功能
                this.displayController = {
                    enableTruncation: this.kanbanDisplayOptions.enableTruncation,
                    maxLength: this.kanbanDisplayOptions.maxTextLength,
                    getTooltip: (tag) => this.getTagTooltip(tag),
                    getStyleClass: (tag) => this.getTagStyleClass(tag)
                };
            }
            
            // 重写原始方法
            get tags() {
                return this.enhancedGetTags();
            }
        };
    }
    
    // 设置颜色过滤系统
    setupColorFilteringSystem() {
        this.colorFilteringConfig = {
            enabled: this.kanbanTagsConfig.enableColorFiltering,
            rules: this.colorFilterRules
        };
    }
    
    // 设置显示优化系统
    setupDisplayOptimizationSystem() {
        this.displayOptimizationConfig = {
            enabled: this.kanbanTagsConfig.enableCompactDisplay,
            options: this.kanbanDisplayOptions
        };
    }
    
    // 设置交互控制系统
    setupInteractionControlSystem() {
        this.interactionControlConfig = {
            disableClick: this.kanbanTagsConfig.enableClickDisabling,
            enableHover: this.kanbanTagsConfig.enableTooltips
        };
    }
    
    // 创建看板多对多标签字段
    createKanbanMany2ManyTagsField(props) {
        const field = new this.EnhancedKanbanMany2ManyTagsField(props);
        this.kanbanTagsStatistics.totalKanbanFields++;
        return field;
    }
    
    // 注册颜色过滤规则
    registerColorFilterRule(name, rule) {
        this.colorFilterRules[name] = rule;
    }
    
    // 批量创建看板字段
    batchCreateKanbanFields(propsArray) {
        const results = [];
        
        for (const props of propsArray) {
            try {
                const field = this.createKanbanMany2ManyTagsField(props);
                results.push({ field, success: true, props });
            } catch (error) {
                results.push({ field: null, success: false, error, props });
            }
        }
        
        return results;
    }
    
    // 获取热门颜色
    getPopularColors(limit = 5) {
        const sorted = Array.from(this.kanbanTagsStatistics.colorDistribution.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, limit);
        
        return sorted.map(([colorIndex, count]) => ({ colorIndex, count }));
    }
    
    // 获取看板标签统计
    getKanbanTagsStatistics() {
        return {
            ...this.kanbanTagsStatistics,
            averageVisibleTagsPerField: this.kanbanTagsStatistics.totalVisibleTags / Math.max(this.kanbanTagsStatistics.totalKanbanFields, 1),
            colorVariety: this.kanbanTagsStatistics.colorDistribution.size,
            filteringEnabled: this.kanbanTagsConfig.enableColorFiltering
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理颜色分布
        this.kanbanTagsStatistics.colorDistribution.clear();
        
        // 重置统计
        this.kanbanTagsStatistics = {
            totalKanbanFields: 0,
            totalVisibleTags: 0,
            totalFilteredTags: 0,
            colorDistribution: new Map(),
            averageTagsPerCard: 0
        };
    }
}

// 使用示例
const kanbanTagsManager = new KanbanTagsFieldManager();

// 创建看板多对多标签字段
const kanbanTagsField = kanbanTagsManager.createKanbanMany2ManyTagsField({
    name: 'tag_ids',
    record: {
        data: { 
            tag_ids: {
                records: [
                    { resId: 1, data: { display_name: 'Important', color: 1 } },
                    { resId: 2, data: { display_name: 'Urgent', color: 2 } },
                    { resId: 3, data: { display_name: 'No Color', color: 0 } }, // 会被过滤掉
                    { resId: 4, data: { display_name: 'Review', color: 3 } }
                ]
            }
        },
        fields: { 
            tag_ids: { 
                type: 'many2many',
                relation: 'tag.model'
            }
        }
    },
    colorField: 'color'
});

// 注册自定义过滤规则
kanbanTagsManager.registerColorFilterRule('customRule', {
    minColorIndex: 2,
    allowedColors: [2, 3, 4, 5]
});

// 获取统计信息
const stats = kanbanTagsManager.getKanbanTagsStatistics();
console.log('Kanban many2many tags field statistics:', stats);
```

## 技术特点

### 1. 看板优化
- **颜色过滤**: 只显示有颜色的标签
- **点击禁用**: 禁用标签点击事件
- **紧凑显示**: 适配看板卡片的紧凑空间
- **性能优化**: 减少不必要的交互和渲染

### 2. 继承设计
- **基类继承**: 继承Many2ManyTagsField的所有功能
- **模板重写**: 使用专门的看板模板
- **配置复用**: 复用基类的所有配置选项
- **功能扩展**: 在基础功能上添加看板特定优化

### 3. 过滤机制
- **条件过滤**: 基于颜色索引过滤标签
- **数组归约**: 使用reduce方法高效过滤
- **状态保持**: 保持原始标签数据不变
- **灵活配置**: 支持自定义过滤规则

### 4. 视图适配
- **看板专用**: 专门为看板视图设计
- **空间优化**: 优化标签在卡片中的显示
- **交互简化**: 简化交互以适应看板场景
- **视觉一致**: 保持看板视图的视觉一致性

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **类继承**: 继承基础标签字段类
- **功能扩展**: 扩展基础功能
- **模板重写**: 重写显示模板

### 2. 过滤器模式 (Filter Pattern)
- **条件过滤**: 基于条件过滤标签
- **链式过滤**: 支持多重过滤条件
- **数据转换**: 转换数据格式

### 3. 适配器模式 (Adapter Pattern)
- **视图适配**: 适配看板视图需求
- **接口适配**: 适配看板接口
- **数据适配**: 适配看板数据格式

### 4. 模板方法模式 (Template Method Pattern)
- **方法重写**: 重写基类方法
- **流程控制**: 控制标签处理流程
- **扩展点**: 提供扩展点

## 注意事项

1. **性能考虑**: 看板视图中可能有大量卡片，注意性能优化
2. **视觉一致性**: 确保标签样式与看板视图一致
3. **空间限制**: 考虑看板卡片的空间限制
4. **用户体验**: 提供清晰的视觉反馈

## 扩展建议

1. **动态过滤**: 支持动态过滤规则
2. **批量操作**: 支持批量标签操作
3. **拖拽支持**: 支持标签拖拽功能
4. **自定义样式**: 支持自定义标签样式
5. **统计分析**: 添加标签使用统计

该看板多对多标签字段为Odoo Web客户端提供了专门优化的看板标签显示功能，通过颜色过滤和交互简化确保了在看板视图中的最佳显示效果和用户体验。

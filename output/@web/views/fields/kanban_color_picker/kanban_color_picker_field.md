# KanbanColorPickerField - 看板颜色选择器字段

## 概述

`kanban_color_picker_field.js` 是 Odoo Web 客户端的看板颜色选择器字段组件，负责在看板视图中提供颜色选择功能。该模块包含37行代码，是一个专门的颜色选择组件，专门用于看板卡片的颜色标记，具备颜色列表、颜色选择、即时保存等特性，是看板视图颜色管理的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/kanban_color_picker/kanban_color_picker_field.js`
- **行数**: 37
- **模块**: `@web/views/fields/kanban_color_picker/kanban_color_picker_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'            // 翻译服务
'@web/core/colorlist/colorlist'         // 颜色列表
'@web/core/registry'                    // 注册表
'@web/views/fields/standard_field_props' // 标准字段属性
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
class KanbanColorPickerField extends Component {
    static template = "web.KanbanColorPickerField";
    static props = standardFieldProps;
}
```

**组件特性**:
- **标准属性**: 使用标准字段属性
- **专用模板**: 使用KanbanColorPickerField专用模板
- **简洁设计**: 简洁的组件实现
- **看板专用**: 专门为看板视图设计

### 2. 颜色获取

```javascript
get colors() {
    return ColorList.COLORS;
}
```

**颜色功能**:
- **颜色列表**: 获取系统预定义的颜色列表
- **统一管理**: 使用统一的颜色管理系统
- **标准化**: 标准化的颜色选择
- **一致性**: 确保颜色选择的一致性

### 3. 颜色选择

```javascript
selectColor(colorIndex) {
    return this.props.record.update({ [this.props.name]: colorIndex }, { save: true });
}
```

**选择功能**:
- **颜色索引**: 使用颜色索引进行选择
- **记录更新**: 更新记录中的颜色字段
- **即时保存**: 选择后立即保存到服务器
- **异步操作**: 返回Promise支持异步操作

### 4. 字段注册

```javascript
const kanbanColorPickerField = {
    component: KanbanColorPickerField,
    displayName: _t("Color Picker"),
    extractProps(fieldInfo, dynamicInfo) {
        return {
            readonly: dynamicInfo.readonly,
        };
    },
};

registry.category("fields").add("kanban_color_picker", kanbanColorPickerField);
```

**注册功能**:
- **组件注册**: 注册看板颜色选择器字段
- **显示名称**: 设置为"Color Picker"
- **属性提取**: 提取只读属性
- **字段注册**: 注册为kanban_color_picker字段类型

## 使用场景

### 1. 看板颜色选择器管理器

```javascript
// 看板颜色选择器管理器
class KanbanColorPickerManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置颜色选择器配置
        this.colorPickerConfig = {
            enableCustomColors: false,
            enableColorHistory: true,
            enableColorPreview: true,
            enableBatchColorChange: false,
            enableColorSearch: false,
            enableAccessibility: true,
            enableAnimation: true,
            enableTooltips: true
        };
        
        // 设置颜色配置
        this.colorConfig = {
            defaultColorIndex: 0,
            maxColors: 12,
            enableGradients: false,
            enableTransparency: false,
            colorFormat: 'index' // 'index', 'hex', 'rgb'
        };
        
        // 设置颜色主题
        this.colorThemes = new Map([
            ['default', ColorList.COLORS],
            ['pastel', [
                '#FFE5E5', '#E5F3FF', '#E5FFE5', '#FFF5E5',
                '#F0E5FF', '#FFE5F0', '#E5FFFF', '#FFFFE5'
            ]],
            ['vibrant', [
                '#FF4444', '#4444FF', '#44FF44', '#FFAA44',
                '#AA44FF', '#FF44AA', '#44FFFF', '#AAFF44'
            ]],
            ['monochrome', [
                '#000000', '#333333', '#666666', '#999999',
                '#CCCCCC', '#FFFFFF', '#F5F5F5', '#E0E0E0'
            ]]
        ]);
        
        // 设置颜色历史
        this.colorHistory = [];
        this.maxHistorySize = 10;
        
        // 设置颜色统计
        this.colorStatistics = {
            totalSelections: 0,
            colorUsage: new Map(),
            popularColors: new Map(),
            averageSelectionTime: 0
        };
        
        this.initializeColorPickerSystem();
    }
    
    // 初始化颜色选择器系统
    initializeColorPickerSystem() {
        // 创建增强的看板颜色选择器字段
        this.createEnhancedKanbanColorPickerField();
        
        // 设置颜色管理系统
        this.setupColorManagementSystem();
        
        // 设置主题系统
        this.setupThemeSystem();
        
        // 设置历史系统
        this.setupHistorySystem();
    }
    
    // 创建增强的看板颜色选择器字段
    createEnhancedKanbanColorPickerField() {
        const originalField = KanbanColorPickerField;
        
        this.EnhancedKanbanColorPickerField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加主题功能
                this.addThemeFeatures();
                
                // 添加历史功能
                this.addHistoryFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    currentTheme: 'default',
                    isPickerOpen: false,
                    hoveredColor: null,
                    selectionStartTime: null,
                    lastSelectedColor: null,
                    colorPreview: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的颜色获取
                this.enhancedGetColors = () => {
                    const theme = this.colorThemes.get(this.enhancedState.currentTheme);
                    return theme || ColorList.COLORS;
                };
                
                // 增强的颜色选择
                this.enhancedSelectColor = async (colorIndex) => {
                    const startTime = this.enhancedState.selectionStartTime;
                    const selectionTime = startTime ? Date.now() - startTime : 0;
                    
                    try {
                        // 记录选择开始
                        this.recordColorSelection(colorIndex, selectionTime);
                        
                        // 添加到历史
                        this.addToColorHistory(colorIndex);
                        
                        // 执行原始选择
                        const result = await this.selectColor(colorIndex);
                        
                        // 记录成功选择
                        this.recordSuccessfulSelection(colorIndex);
                        
                        // 更新状态
                        this.enhancedState.lastSelectedColor = colorIndex;
                        this.enhancedState.isPickerOpen = false;
                        
                        return result;
                        
                    } catch (error) {
                        this.handleSelectionError(error);
                        throw error;
                    }
                };
                
                // 获取颜色信息
                this.getColorInfo = (colorIndex) => {
                    const colors = this.enhancedGetColors();
                    const color = colors[colorIndex];
                    
                    if (!color) {
                        return null;
                    }
                    
                    return {
                        index: colorIndex,
                        hex: color,
                        rgb: this.hexToRgb(color),
                        hsl: this.hexToHsl(color),
                        name: this.getColorName(colorIndex),
                        isLight: this.isLightColor(color)
                    };
                };
                
                // 十六进制转RGB
                this.hexToRgb = (hex) => {
                    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
                    return result ? {
                        r: parseInt(result[1], 16),
                        g: parseInt(result[2], 16),
                        b: parseInt(result[3], 16)
                    } : null;
                };
                
                // 十六进制转HSL
                this.hexToHsl = (hex) => {
                    const rgb = this.hexToRgb(hex);
                    if (!rgb) return null;
                    
                    const r = rgb.r / 255;
                    const g = rgb.g / 255;
                    const b = rgb.b / 255;
                    
                    const max = Math.max(r, g, b);
                    const min = Math.min(r, g, b);
                    let h, s, l = (max + min) / 2;
                    
                    if (max === min) {
                        h = s = 0; // achromatic
                    } else {
                        const d = max - min;
                        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
                        switch (max) {
                            case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                            case g: h = (b - r) / d + 2; break;
                            case b: h = (r - g) / d + 4; break;
                        }
                        h /= 6;
                    }
                    
                    return {
                        h: Math.round(h * 360),
                        s: Math.round(s * 100),
                        l: Math.round(l * 100)
                    };
                };
                
                // 获取颜色名称
                this.getColorName = (colorIndex) => {
                    const colorNames = [
                        'Red', 'Orange', 'Yellow', 'Green',
                        'Blue', 'Purple', 'Pink', 'Brown',
                        'Gray', 'Black', 'White', 'Cyan'
                    ];
                    return colorNames[colorIndex] || `Color ${colorIndex}`;
                };
                
                // 判断是否为浅色
                this.isLightColor = (hex) => {
                    const rgb = this.hexToRgb(hex);
                    if (!rgb) return false;
                    
                    // 使用相对亮度公式
                    const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
                    return brightness > 128;
                };
                
                // 设置颜色主题
                this.setColorTheme = (themeName) => {
                    if (this.colorThemes.has(themeName)) {
                        this.enhancedState.currentTheme = themeName;
                    }
                };
                
                // 打开颜色选择器
                this.openColorPicker = () => {
                    this.enhancedState.isPickerOpen = true;
                    this.enhancedState.selectionStartTime = Date.now();
                };
                
                // 关闭颜色选择器
                this.closeColorPicker = () => {
                    this.enhancedState.isPickerOpen = false;
                    this.enhancedState.hoveredColor = null;
                    this.enhancedState.selectionStartTime = null;
                };
                
                // 颜色悬停
                this.onColorHover = (colorIndex) => {
                    this.enhancedState.hoveredColor = colorIndex;
                    
                    if (this.colorPickerConfig.enableColorPreview) {
                        this.enhancedState.colorPreview = this.getColorInfo(colorIndex);
                    }
                };
                
                // 颜色离开
                this.onColorLeave = () => {
                    this.enhancedState.hoveredColor = null;
                    this.enhancedState.colorPreview = null;
                };
                
                // 添加到颜色历史
                this.addToColorHistory = (colorIndex) => {
                    if (!this.colorPickerConfig.enableColorHistory) return;
                    
                    // 移除重复项
                    this.colorHistory = this.colorHistory.filter(index => index !== colorIndex);
                    
                    // 添加到开头
                    this.colorHistory.unshift(colorIndex);
                    
                    // 限制历史大小
                    if (this.colorHistory.length > this.maxHistorySize) {
                        this.colorHistory.pop();
                    }
                };
                
                // 获取颜色历史
                this.getColorHistory = () => {
                    return this.colorHistory.map(index => this.getColorInfo(index));
                };
                
                // 清除颜色历史
                this.clearColorHistory = () => {
                    this.colorHistory = [];
                };
                
                // 获取当前颜色
                this.getCurrentColor = () => {
                    const currentIndex = this.props.record.data[this.props.name];
                    return this.getColorInfo(currentIndex);
                };
                
                // 获取推荐颜色
                this.getRecommendedColors = () => {
                    // 基于使用频率推荐颜色
                    const sorted = Array.from(this.colorStatistics.popularColors.entries())
                        .sort((a, b) => b[1] - a[1])
                        .slice(0, 6);
                    
                    return sorted.map(([index, count]) => ({
                        ...this.getColorInfo(index),
                        usageCount: count
                    }));
                };
                
                // 批量设置颜色
                this.batchSetColor = async (records, colorIndex) => {
                    const results = [];
                    
                    for (const record of records) {
                        try {
                            await record.update({ [this.props.name]: colorIndex }, { save: true });
                            results.push({ record, success: true, colorIndex });
                        } catch (error) {
                            results.push({ record, success: false, error });
                        }
                    }
                    
                    return results;
                };
                
                // 获取颜色选择器信息
                this.getColorPickerInfo = () => {
                    return {
                        currentTheme: this.enhancedState.currentTheme,
                        isOpen: this.enhancedState.isPickerOpen,
                        hoveredColor: this.enhancedState.hoveredColor,
                        lastSelectedColor: this.enhancedState.lastSelectedColor,
                        colorPreview: this.enhancedState.colorPreview,
                        availableColors: this.enhancedGetColors().length,
                        colorHistory: this.getColorHistory()
                    };
                };
                
                // 记录颜色选择
                this.recordColorSelection = (colorIndex, selectionTime) => {
                    this.colorStatistics.totalSelections++;
                    
                    // 记录颜色使用
                    const usage = this.colorStatistics.colorUsage.get(colorIndex) || 0;
                    this.colorStatistics.colorUsage.set(colorIndex, usage + 1);
                    
                    // 记录热门颜色
                    const popularity = this.colorStatistics.popularColors.get(colorIndex) || 0;
                    this.colorStatistics.popularColors.set(colorIndex, popularity + 1);
                    
                    // 记录选择时间
                    if (selectionTime > 0) {
                        this.colorStatistics.averageSelectionTime = 
                            (this.colorStatistics.averageSelectionTime + selectionTime) / 2;
                    }
                };
                
                // 记录成功选择
                this.recordSuccessfulSelection = (colorIndex) => {
                    console.log(`Color ${colorIndex} selected successfully`);
                };
                
                // 处理选择错误
                this.handleSelectionError = (error) => {
                    console.error('Color selection error:', error);
                };
            }
            
            addThemeFeatures() {
                // 主题功能
                this.themeManager = {
                    enabled: true,
                    setTheme: (theme) => this.setColorTheme(theme),
                    getThemes: () => Array.from(this.colorThemes.keys()),
                    getCurrentTheme: () => this.enhancedState.currentTheme
                };
            }
            
            addHistoryFeatures() {
                // 历史功能
                this.historyManager = {
                    enabled: this.colorPickerConfig.enableColorHistory,
                    getHistory: () => this.getColorHistory(),
                    clearHistory: () => this.clearColorHistory(),
                    addToHistory: (colorIndex) => this.addToColorHistory(colorIndex)
                };
            }
            
            // 重写原始方法
            get colors() {
                return this.enhancedGetColors();
            }
            
            selectColor(colorIndex) {
                return this.enhancedSelectColor(colorIndex);
            }
        };
    }
    
    // 设置颜色管理系统
    setupColorManagementSystem() {
        this.colorManagementConfig = {
            defaultTheme: 'default',
            enableCustomThemes: this.colorPickerConfig.enableCustomColors
        };
    }
    
    // 设置主题系统
    setupThemeSystem() {
        this.themeSystemConfig = {
            themes: this.colorThemes,
            defaultTheme: 'default'
        };
    }
    
    // 设置历史系统
    setupHistorySystem() {
        this.historySystemConfig = {
            enabled: this.colorPickerConfig.enableColorHistory,
            maxSize: this.maxHistorySize
        };
    }
    
    // 创建看板颜色选择器字段
    createKanbanColorPickerField(props) {
        return new this.EnhancedKanbanColorPickerField(props);
    }
    
    // 注册颜色主题
    registerColorTheme(name, colors) {
        this.colorThemes.set(name, colors);
    }
    
    // 获取热门颜色
    getPopularColors(limit = 5) {
        const sorted = Array.from(this.colorStatistics.popularColors.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, limit);
        
        return sorted.map(([colorIndex, count]) => ({ colorIndex, count }));
    }
    
    // 获取颜色统计
    getColorStatistics() {
        return {
            ...this.colorStatistics,
            themeCount: this.colorThemes.size,
            historySize: this.colorHistory.length,
            uniqueColorsUsed: this.colorStatistics.colorUsage.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理主题
        this.colorThemes.clear();
        
        // 清理历史
        this.colorHistory = [];
        
        // 重置统计
        this.colorStatistics = {
            totalSelections: 0,
            colorUsage: new Map(),
            popularColors: new Map(),
            averageSelectionTime: 0
        };
    }
}

// 使用示例
const colorPickerManager = new KanbanColorPickerManager();

// 创建看板颜色选择器字段
const colorPickerField = colorPickerManager.createKanbanColorPickerField({
    name: 'color',
    record: {
        data: { color: 2 },
        fields: { color: { type: 'integer' } },
        update: (data, options) => Promise.resolve()
    }
});

// 注册自定义主题
colorPickerManager.registerColorTheme('custom', [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
    '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'
]);

// 获取统计信息
const stats = colorPickerManager.getColorStatistics();
console.log('Color picker statistics:', stats);
```

## 技术特点

### 1. 简洁高效
- **最小实现**: 极简的代码实现
- **专注功能**: 专注于颜色选择功能
- **即时保存**: 选择后立即保存
- **用户友好**: 直观的颜色选择界面

### 2. 颜色管理
- **统一颜色**: 使用系统统一的颜色列表
- **颜色索引**: 通过索引管理颜色
- **标准化**: 标准化的颜色选择
- **一致性**: 确保整个系统的颜色一致性

### 3. 看板专用
- **看板优化**: 专门为看板视图优化
- **卡片标记**: 用于看板卡片的颜色标记
- **视觉区分**: 提供视觉上的分类区分
- **快速识别**: 帮助用户快速识别不同类型

### 4. 交互简单
- **点击选择**: 简单的点击选择操作
- **即时反馈**: 立即的视觉反馈
- **无需确认**: 选择即生效，无需额外确认
- **响应迅速**: 快速的响应速度

## 设计模式

### 1. 组件模式 (Component Pattern)
- **UI组件**: 封装颜色选择UI
- **状态管理**: 管理颜色选择状态
- **事件处理**: 处理颜色选择事件

### 2. 策略模式 (Strategy Pattern)
- **颜色策略**: 不同的颜色选择策略
- **主题策略**: 不同的颜色主题策略
- **保存策略**: 不同的保存策略

### 3. 观察者模式 (Observer Pattern)
- **颜色变化**: 观察颜色选择变化
- **状态观察**: 观察组件状态变化
- **记录更新**: 观察记录更新

### 4. 命令模式 (Command Pattern)
- **选择命令**: 封装颜色选择操作
- **更新命令**: 封装记录更新操作
- **撤销支持**: 支持操作撤销

## 注意事项

1. **颜色一致性**: 确保整个系统的颜色使用一致性
2. **可访问性**: 考虑色盲用户的可访问性需求
3. **性能考虑**: 避免频繁的颜色更新操作
4. **用户体验**: 提供清晰的颜色选择反馈

## 扩展建议

1. **自定义颜色**: 支持自定义颜色功能
2. **颜色主题**: 添加多种颜色主题选择
3. **颜色历史**: 记录用户的颜色选择历史
4. **批量操作**: 支持批量颜色设置
5. **颜色搜索**: 添加颜色搜索功能

该看板颜色选择器字段为Odoo Web客户端提供了简洁高效的颜色选择功能，通过统一的颜色管理和即时保存机制确保了看板视图中良好的颜色标记体验和视觉效果。

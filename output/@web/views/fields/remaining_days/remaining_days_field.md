# RemainingDaysField - 剩余天数字段

## 概述

`remaining_days_field.js` 是 Odoo Web 客户端的剩余天数字段组件，负责计算和显示距离指定日期的剩余天数。该模块包含111行代码，是一个功能完整的日期计算组件，专门用于处理date和datetime类型的字段，具备天数计算、状态显示、装饰器支持、本地化等特性，是时间管理和截止日期提醒的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/remaining_days/remaining_days_field.js`
- **行数**: 111
- **模块**: `@web/views/fields/remaining_days/remaining_days_field`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                            // OWL框架
'@web/core/py_js/py'                   // Python表达式评估
'@web/core/l10n/dates'                 // 日期本地化
'@web/views/utils'                     // 视图工具
'@web/core/l10n/localization'          // 本地化服务
'@web/core/l10n/translation'           // 翻译服务
'@web/core/registry'                   // 注册表
'@web/views/fields/datetime/datetime_field' // 日期时间字段
'@web/views/fields/standard_field_props' // 标准字段属性
```

## 核心功能

### 1. 组件定义

```javascript
const RemainingDaysField = class RemainingDaysField extends Component {
    static components = { DateTimeField };

    static props = {
        ...standardFieldProps,
        classes: { type: Object, optional: true },
    };

    static defaultProps = {
        classes: {
            'bf': 'days <= 0',
            'danger': 'days < 0',
            'warning': 'days == 0',
        },
    };

    static template = "web.RemainingDaysField";
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **装饰器类**: 支持classes配置装饰器类
- **默认样式**: 提供默认的状态样式配置
- **日期时间**: 集成DateTimeField组件
- **专用模板**: 使用RemainingDaysField专用模板

### 2. 天数计算

```javascript
get diffDays() {
    const { record, name } = this.props;
    const value = record.data[name];
    if (!value) {
        return null;
    }
    const today = DateTime.local().startOf("day");
    const diff = value.startOf("day").diff(today, "days");
    return Math.floor(diff.days);
}
```

**计算功能**:
- **空值处理**: 处理空日期值
- **当前日期**: 获取当前日期的开始时间
- **日期差值**: 计算目标日期与当前日期的差值
- **向下取整**: 使用Math.floor确保整数天数

### 3. 显示文本

```javascript
get diffString() {
    const diffDays = this.diffDays;
    if (diffDays === null) {
        return "";
    }
    if (diffDays === 0) {
        return _t("Today");
    } else if (diffDays === 1) {
        return _t("Tomorrow");
    } else if (diffDays === -1) {
        return _t("Yesterday");
    } else if (diffDays > 0) {
        return _t("In %s days", diffDays);
    } else {
        return _t("%s days ago", Math.abs(diffDays));
    }
}
```

**显示功能**:
- **特殊日期**: 特殊处理今天、明天、昨天
- **未来日期**: 显示"In X days"格式
- **过去日期**: 显示"X days ago"格式
- **本地化**: 使用翻译服务支持多语言
- **绝对值**: 过去日期使用绝对值显示

### 4. 装饰器类

```javascript
get decorationClasses() {
    const { classes } = this.props;
    const diffDays = this.diffDays;
    if (diffDays === null || !classes) {
        return "";
    }
    
    const classNames = [];
    for (const [className, condition] of Object.entries(classes)) {
        const context = { days: diffDays };
        if (evaluateExpr(condition, context)) {
            classNames.push(className);
        }
    }
    
    return getClassNameFromDecoration(classNames);
}
```

**装饰器功能**:
- **条件评估**: 使用Python表达式评估条件
- **上下文**: 提供days变量给条件表达式
- **类名生成**: 根据条件生成CSS类名
- **多条件**: 支持多个装饰器条件

### 5. 字段注册

```javascript
const remainingDaysField = {
    component: RemainingDaysField,
    displayName: _t("Remaining Days"),
    supportedTypes: ["date", "datetime"],
    extractProps: ({ attrs, options }) => ({
        classes: options.classes || attrs.classes,
    }),
};

registry.category("fields").add("remaining_days", remainingDaysField);
```

**注册功能**:
- **组件注册**: 注册剩余天数字段组件
- **显示名称**: 设置为"Remaining Days"
- **类型支持**: 支持date和datetime类型
- **属性提取**: 提取装饰器类配置

## 使用场景

### 1. 剩余天数字段管理器

```javascript
// 剩余天数字段管理器
class RemainingDaysFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置剩余天数配置
        this.remainingDaysConfig = {
            enableDecorations: true,
            enableNotifications: false,
            enableCustomFormats: true,
            enableTimeZoneSupport: true,
            enableBusinessDays: false,
            enableHolidays: false,
            enableReminders: false,
            enableStatistics: true
        };
        
        // 设置装饰器主题
        this.decorationThemes = new Map([
            ['default', {
                'bf': 'days <= 0',
                'danger': 'days < 0',
                'warning': 'days == 0',
                'info': 'days == 1',
                'success': 'days > 7'
            }],
            ['urgent', {
                'danger': 'days < 0',
                'warning': 'days <= 3',
                'info': 'days <= 7',
                'success': 'days > 7'
            }],
            ['project', {
                'danger': 'days < 0',
                'warning': 'days <= 1',
                'primary': 'days <= 5',
                'info': 'days <= 14',
                'success': 'days > 14'
            }]
        ]);
        
        // 设置显示格式
        this.displayFormats = new Map([
            ['simple', (days) => {
                if (days === 0) return 'Today';
                if (days === 1) return 'Tomorrow';
                if (days === -1) return 'Yesterday';
                if (days > 0) return `${days} days`;
                return `${Math.abs(days)} days ago`;
            }],
            ['detailed', (days) => {
                if (days === 0) return 'Due Today';
                if (days === 1) return 'Due Tomorrow';
                if (days === -1) return 'Overdue by 1 day';
                if (days > 0) return `Due in ${days} days`;
                return `Overdue by ${Math.abs(days)} days`;
            }],
            ['business', (days) => {
                const businessDays = this.calculateBusinessDays(days);
                if (businessDays === 0) return 'Due Today';
                if (businessDays > 0) return `${businessDays} business days`;
                return `${Math.abs(businessDays)} business days overdue`;
            }]
        ]);
        
        // 设置时区配置
        this.timeZoneConfig = {
            enableTimeZoneSupport: true,
            defaultTimeZone: 'local',
            userTimeZone: null,
            enableTimeZoneConversion: false
        };
        
        // 设置剩余天数统计
        this.remainingDaysStatistics = {
            totalFields: 0,
            totalCalculations: 0,
            averageDays: 0,
            maxDays: Number.MIN_SAFE_INTEGER,
            minDays: Number.MAX_SAFE_INTEGER,
            overdueCount: 0,
            todayCount: 0,
            futureCount: 0,
            dayDistribution: new Map()
        };
        
        this.initializeRemainingDaysSystem();
    }
    
    // 初始化剩余天数系统
    initializeRemainingDaysSystem() {
        // 创建增强的剩余天数字段
        this.createEnhancedRemainingDaysField();
        
        // 设置装饰器系统
        this.setupDecorationSystem();
        
        // 设置格式化系统
        this.setupFormattingSystem();
        
        // 设置时区系统
        this.setupTimeZoneSystem();
    }
    
    // 创建增强的剩余天数字段
    createEnhancedRemainingDaysField() {
        const originalField = RemainingDaysField;
        
        this.EnhancedRemainingDaysField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加格式化功能
                this.addFormattingFeatures();
                
                // 添加通知功能
                this.addNotificationFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    currentTheme: 'default',
                    currentFormat: 'simple',
                    timeZone: this.timeZoneConfig.defaultTimeZone,
                    lastCalculationTime: null,
                    businessDaysOnly: false,
                    includeHolidays: false
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的天数计算
                this.enhancedDiffDays = () => {
                    const { record, name } = this.props;
                    const value = record.data[name];
                    if (!value) {
                        return null;
                    }
                    
                    // 获取当前日期
                    let today;
                    if (this.timeZoneConfig.enableTimeZoneSupport && this.enhancedState.timeZone !== 'local') {
                        today = DateTime.now().setZone(this.enhancedState.timeZone).startOf("day");
                    } else {
                        today = DateTime.local().startOf("day");
                    }
                    
                    // 计算差值
                    const targetDate = value.startOf("day");
                    let diff = targetDate.diff(today, "days");
                    let days = Math.floor(diff.days);
                    
                    // 业务日计算
                    if (this.enhancedState.businessDaysOnly) {
                        days = this.calculateBusinessDays(today, targetDate);
                    }
                    
                    // 记录统计
                    this.recordDaysCalculation(days);
                    
                    this.enhancedState.lastCalculationTime = new Date();
                    
                    return days;
                };
                
                // 计算业务日
                this.calculateBusinessDays = (startDate, endDate) => {
                    if (!this.remainingDaysConfig.enableBusinessDays) {
                        return Math.floor(endDate.diff(startDate, "days").days);
                    }
                    
                    let businessDays = 0;
                    let current = startDate;
                    
                    while (current < endDate) {
                        // 跳过周末
                        if (current.weekday <= 5) { // 1-7, 周一到周日
                            // 检查是否为假期
                            if (!this.isHoliday(current)) {
                                businessDays++;
                            }
                        }
                        current = current.plus({ days: 1 });
                    }
                    
                    return businessDays;
                };
                
                // 检查是否为假期
                this.isHoliday = (date) => {
                    if (!this.remainingDaysConfig.enableHolidays) {
                        return false;
                    }
                    
                    // 这里可以集成假期API或配置
                    // 简单示例：新年
                    return date.month === 1 && date.day === 1;
                };
                
                // 增强的显示字符串
                this.enhancedDiffString = () => {
                    const diffDays = this.enhancedDiffDays();
                    if (diffDays === null) {
                        return "";
                    }
                    
                    const formatter = this.displayFormats.get(this.enhancedState.currentFormat);
                    if (formatter) {
                        return formatter(diffDays);
                    }
                    
                    // 回退到默认格式
                    return this.getDefaultDiffString(diffDays);
                };
                
                // 获取默认显示字符串
                this.getDefaultDiffString = (diffDays) => {
                    if (diffDays === 0) {
                        return _t("Today");
                    } else if (diffDays === 1) {
                        return _t("Tomorrow");
                    } else if (diffDays === -1) {
                        return _t("Yesterday");
                    } else if (diffDays > 0) {
                        return _t("In %s days", diffDays);
                    } else {
                        return _t("%s days ago", Math.abs(diffDays));
                    }
                };
                
                // 增强的装饰器类
                this.enhancedDecorationClasses = () => {
                    const theme = this.decorationThemes.get(this.enhancedState.currentTheme);
                    const classes = theme || this.props.classes;
                    
                    const diffDays = this.enhancedDiffDays();
                    if (diffDays === null || !classes) {
                        return "";
                    }
                    
                    const classNames = [];
                    for (const [className, condition] of Object.entries(classes)) {
                        const context = { 
                            days: diffDays,
                            isOverdue: diffDays < 0,
                            isToday: diffDays === 0,
                            isFuture: diffDays > 0
                        };
                        
                        if (evaluateExpr(condition, context)) {
                            classNames.push(className);
                        }
                    }
                    
                    return getClassNameFromDecoration(classNames);
                };
                
                // 设置主题
                this.setTheme = (themeName) => {
                    if (this.decorationThemes.has(themeName)) {
                        this.enhancedState.currentTheme = themeName;
                    }
                };
                
                // 设置格式
                this.setFormat = (formatName) => {
                    if (this.displayFormats.has(formatName)) {
                        this.enhancedState.currentFormat = formatName;
                    }
                };
                
                // 设置时区
                this.setTimeZone = (timeZone) => {
                    this.enhancedState.timeZone = timeZone;
                };
                
                // 获取状态信息
                this.getStatusInfo = () => {
                    const days = this.enhancedDiffDays();
                    
                    return {
                        days: days,
                        isOverdue: days < 0,
                        isToday: days === 0,
                        isTomorrow: days === 1,
                        isYesterday: days === -1,
                        isFuture: days > 0,
                        isPast: days < 0,
                        urgencyLevel: this.getUrgencyLevel(days),
                        displayString: this.enhancedDiffString(),
                        decorationClasses: this.enhancedDecorationClasses()
                    };
                };
                
                // 获取紧急程度
                this.getUrgencyLevel = (days) => {
                    if (days < 0) return 'overdue';
                    if (days === 0) return 'today';
                    if (days <= 1) return 'urgent';
                    if (days <= 3) return 'high';
                    if (days <= 7) return 'medium';
                    return 'low';
                };
                
                // 记录天数计算
                this.recordDaysCalculation = (days) => {
                    this.remainingDaysStatistics.totalCalculations++;
                    
                    if (days < 0) {
                        this.remainingDaysStatistics.overdueCount++;
                    } else if (days === 0) {
                        this.remainingDaysStatistics.todayCount++;
                    } else {
                        this.remainingDaysStatistics.futureCount++;
                    }
                    
                    // 更新最大最小值
                    if (days > this.remainingDaysStatistics.maxDays) {
                        this.remainingDaysStatistics.maxDays = days;
                    }
                    if (days < this.remainingDaysStatistics.minDays) {
                        this.remainingDaysStatistics.minDays = days;
                    }
                    
                    // 记录分布
                    const range = this.getDayRange(days);
                    const count = this.remainingDaysStatistics.dayDistribution.get(range) || 0;
                    this.remainingDaysStatistics.dayDistribution.set(range, count + 1);
                    
                    // 更新平均值
                    this.updateAverageDays();
                };
                
                // 获取天数范围
                this.getDayRange = (days) => {
                    if (days < -30) return 'overdue_30+';
                    if (days < -7) return 'overdue_7-30';
                    if (days < 0) return 'overdue_1-7';
                    if (days === 0) return 'today';
                    if (days <= 7) return 'future_1-7';
                    if (days <= 30) return 'future_7-30';
                    return 'future_30+';
                };
                
                // 更新平均天数
                this.updateAverageDays = () => {
                    if (this.remainingDaysStatistics.totalCalculations > 0) {
                        // 这里需要累计所有天数值来计算平均值
                        // 简化处理，实际应该累计所有计算的天数
                    }
                };
            }
            
            addFormattingFeatures() {
                // 格式化功能
                this.formattingManager = {
                    enabled: this.remainingDaysConfig.enableCustomFormats,
                    setFormat: (format) => this.setFormat(format),
                    getFormats: () => Array.from(this.displayFormats.keys()),
                    addFormat: (name, formatter) => this.displayFormats.set(name, formatter)
                };
            }
            
            addNotificationFeatures() {
                // 通知功能
                this.notificationManager = {
                    enabled: this.remainingDaysConfig.enableNotifications,
                    checkReminders: () => this.checkReminders(),
                    addReminder: (days, message) => this.addReminder(days, message)
                };
            }
            
            // 重写原始方法
            get diffDays() {
                return this.enhancedDiffDays();
            }
            
            get diffString() {
                return this.enhancedDiffString();
            }
            
            get decorationClasses() {
                return this.enhancedDecorationClasses();
            }
        };
    }
    
    // 设置装饰器系统
    setupDecorationSystem() {
        this.decorationSystemConfig = {
            enabled: this.remainingDaysConfig.enableDecorations,
            themes: this.decorationThemes,
            defaultTheme: 'default'
        };
    }
    
    // 设置格式化系统
    setupFormattingSystem() {
        this.formattingSystemConfig = {
            enabled: this.remainingDaysConfig.enableCustomFormats,
            formats: this.displayFormats,
            defaultFormat: 'simple'
        };
    }
    
    // 设置时区系统
    setupTimeZoneSystem() {
        this.timeZoneSystemConfig = {
            enabled: this.remainingDaysConfig.enableTimeZoneSupport,
            config: this.timeZoneConfig
        };
    }
    
    // 创建剩余天数字段
    createRemainingDaysField(props) {
        const field = new this.EnhancedRemainingDaysField(props);
        this.remainingDaysStatistics.totalFields++;
        return field;
    }
    
    // 注册装饰器主题
    registerDecorationTheme(name, theme) {
        this.decorationThemes.set(name, theme);
    }
    
    // 注册显示格式
    registerDisplayFormat(name, formatter) {
        this.displayFormats.set(name, formatter);
    }
    
    // 获取剩余天数统计
    getRemainingDaysStatistics() {
        return {
            ...this.remainingDaysStatistics,
            overdueRate: (this.remainingDaysStatistics.overdueCount / Math.max(this.remainingDaysStatistics.totalCalculations, 1)) * 100,
            todayRate: (this.remainingDaysStatistics.todayCount / Math.max(this.remainingDaysStatistics.totalCalculations, 1)) * 100,
            futureRate: (this.remainingDaysStatistics.futureCount / Math.max(this.remainingDaysStatistics.totalCalculations, 1)) * 100,
            themeCount: this.decorationThemes.size,
            formatCount: this.displayFormats.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理主题
        this.decorationThemes.clear();
        
        // 清理格式
        this.displayFormats.clear();
        
        // 清理分布
        this.remainingDaysStatistics.dayDistribution.clear();
        
        // 重置统计
        this.remainingDaysStatistics = {
            totalFields: 0,
            totalCalculations: 0,
            averageDays: 0,
            maxDays: Number.MIN_SAFE_INTEGER,
            minDays: Number.MAX_SAFE_INTEGER,
            overdueCount: 0,
            todayCount: 0,
            futureCount: 0,
            dayDistribution: new Map()
        };
    }
}

// 使用示例
const remainingDaysManager = new RemainingDaysFieldManager();

// 创建剩余天数字段
const remainingDaysField = remainingDaysManager.createRemainingDaysField({
    name: 'deadline',
    record: {
        data: { 
            deadline: DateTime.local().plus({ days: 5 })
        },
        fields: { 
            deadline: { 
                type: 'date'
            }
        }
    },
    classes: {
        'danger': 'days < 0',
        'warning': 'days <= 3',
        'success': 'days > 7'
    }
});

// 注册自定义主题
remainingDaysManager.registerDecorationTheme('custom', {
    'danger': 'days < -7',
    'warning': 'days >= -7 && days < 0',
    'info': 'days >= 0 && days <= 3',
    'success': 'days > 3'
});

// 注册自定义格式
remainingDaysManager.registerDisplayFormat('verbose', (days) => {
    if (days === 0) return 'Due today - immediate attention required';
    if (days === 1) return 'Due tomorrow - prepare now';
    if (days > 0) return `${days} days remaining until deadline`;
    return `Overdue by ${Math.abs(days)} days - urgent action needed`;
});

// 获取统计信息
const stats = remainingDaysManager.getRemainingDaysStatistics();
console.log('Remaining days field statistics:', stats);
```

## 技术特点

### 1. 日期计算
- **精确计算**: 使用Luxon库进行精确的日期计算
- **时区支持**: 支持不同时区的日期计算
- **业务日**: 支持业务日计算（排除周末和假期）
- **向下取整**: 确保返回整数天数

### 2. 装饰器系统
- **条件装饰**: 基于Python表达式的条件装饰
- **多样式**: 支持多种CSS类的条件应用
- **动态评估**: 动态评估装饰条件
- **上下文变量**: 提供days等上下文变量

### 3. 本地化支持
- **多语言**: 支持多语言显示
- **特殊日期**: 本地化今天、明天、昨天等特殊日期
- **格式化**: 本地化的日期格式化
- **翻译**: 使用翻译服务

### 4. 状态显示
- **过期状态**: 清晰显示过期状态
- **当前状态**: 突出显示今天的任务
- **未来状态**: 显示未来的截止日期
- **紧急程度**: 根据剩余天数显示紧急程度

## 设计模式

### 1. 计算模式 (Calculation Pattern)
- **日期计算**: 封装日期差值计算
- **业务日计算**: 封装业务日计算逻辑
- **缓存计算**: 缓存计算结果

### 2. 装饰器模式 (Decorator Pattern)
- **样式装饰**: 根据条件装饰样式
- **状态装饰**: 装饰不同的状态
- **条件装饰**: 基于条件的装饰

### 3. 策略模式 (Strategy Pattern)
- **显示策略**: 不同的显示格式策略
- **计算策略**: 不同的日期计算策略
- **装饰策略**: 不同的装饰策略

### 4. 模板方法模式 (Template Method Pattern)
- **计算模板**: 定义日期计算模板
- **显示模板**: 定义显示格式模板
- **装饰模板**: 定义装饰应用模板

## 注意事项

1. **时区处理**: 正确处理不同时区的日期
2. **性能优化**: 避免频繁的日期计算
3. **用户体验**: 提供清晰的状态指示
4. **本地化**: 确保多语言支持的正确性

## 扩展建议

1. **提醒功能**: 添加截止日期提醒
2. **业务日**: 增强业务日计算功能
3. **假期支持**: 集成假期日历
4. **图表显示**: 添加剩余天数图表
5. **批量操作**: 支持批量日期操作

该剩余天数字段为Odoo Web客户端提供了完整的日期计算和状态显示功能，通过智能的装饰器系统和本地化支持确保了时间管理的直观性和实用性。

# Parsers - 字段解析器

## 概述

`parsers.js` 是 Odoo Web 客户端字段系统的解析器模块，负责将用户输入的字符串解析为相应的数据类型。该模块包含203行代码，提供了数字、日期、布尔值等各种数据类型的解析功能，具备数学表达式计算、本地化支持、格式验证、错误处理等特性，是字段输入处理系统的核心工具模块。

## 文件信息
- **路径**: `/web/static/src/views/fields/parsers.js`
- **行数**: 203
- **模块**: `@web/views/fields/parsers`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/dates'              // 日期本地化
'@web/core/l10n/localization'       // 本地化服务
'@web/core/py_js/py'                // Python表达式解析
'@web/core/registry'                // 注册表服务
'@web/core/utils/strings'           // 字符串工具
```

## 核心功能

### 1. 数学表达式计算

```javascript
function evaluateMathematicalExpression(expr, context = {}) {
    // remove extra space
    var val = expr.replace(new RegExp(/( )/g), "");
    var safeEvalString = "";
    for (let v of val.split(new RegExp(/([-+*/()^])/g))) {
        if (!["+", "-", "*", "/", "(", ")", "^"].includes(v) && v.length) {
            // check if this is a float and take into account user delimiter preference
            v = parseFloat(v);
        }
        if (v === "^") {
            v = "**";
        }
        safeEvalString += v;
    }
    return evaluateExpr(safeEvalString, context);
}
```

**数学表达式功能**:
- **表达式解析**: 解析数学表达式字符串
- **运算符支持**: 支持基本数学运算符
- **安全计算**: 安全的表达式计算
- **上下文支持**: 支持上下文变量

### 2. 数字解析

```javascript
function parseNumber(value, options = {}) {
    if (value.startsWith("=")) {
        value = evaluateMathematicalExpression(value.substring(1));
        if (options.truncate) {
            value = Math.trunc(value);
        }
    } else {
        // 处理本地化的数字格式
        const thousandsSep = options.thousandsSep || localization.thousandsSep;
        const decimalPoint = options.decimalPoint || localization.decimalPoint;
        
        // 移除千分位分隔符
        value = value.replace(new RegExp(escapeRegExp(thousandsSep), 'g'), '');
        
        // 替换小数点
        if (decimalPoint !== '.') {
            value = value.replace(new RegExp(escapeRegExp(decimalPoint), 'g'), '.');
        }
        
        value = parseFloat(value);
    }
    
    return isNaN(value) ? 0 : value;
}
```

**数字解析功能**:
- **表达式支持**: 支持以"="开头的数学表达式
- **本地化**: 支持本地化的数字格式
- **千分位**: 处理千分位分隔符
- **小数点**: 处理不同的小数点符号

### 3. 整数解析

```javascript
function parseInteger(value, options = {}) {
    return parseNumber(value, { ...options, truncate: true });
}
```

**整数解析功能**:
- **截断处理**: 自动截断小数部分
- **表达式支持**: 继承数字解析的表达式功能
- **本地化**: 支持本地化的整数格式
- **错误处理**: 统一的错误处理逻辑

### 4. 浮点数解析

```javascript
function parseFloat(value, options = {}) {
    return parseNumber(value, options);
}
```

**浮点数解析功能**:
- **精度保持**: 保持浮点数精度
- **表达式支持**: 支持数学表达式计算
- **本地化**: 支持本地化的浮点数格式
- **范围验证**: 可选的数值范围验证

### 5. 日期解析

```javascript
function parseDate(value, options = {}) {
    if (!value) {
        return false;
    }
    return parseDate(value, options);
}

function parseDateTime(value, options = {}) {
    if (!value) {
        return false;
    }
    return parseDateTime(value, options);
}
```

**日期解析功能**:
- **格式识别**: 自动识别日期格式
- **本地化**: 支持本地化的日期格式
- **时区处理**: 处理时区转换
- **验证**: 日期有效性验证

### 6. 布尔值解析

```javascript
function parseBoolean(value, options = {}) {
    if (typeof value === 'boolean') {
        return value;
    }
    
    if (typeof value === 'string') {
        const lowerValue = value.toLowerCase().trim();
        const trueValues = ['true', '1', 'yes', 'on', 'checked'];
        const falseValues = ['false', '0', 'no', 'off', 'unchecked'];
        
        if (trueValues.includes(lowerValue)) {
            return true;
        }
        if (falseValues.includes(lowerValue)) {
            return false;
        }
    }
    
    return Boolean(value);
}
```

**布尔值解析功能**:
- **多格式支持**: 支持多种布尔值表示格式
- **大小写不敏感**: 不区分大小写
- **本地化**: 支持本地化的布尔值表示
- **默认处理**: 提供合理的默认值

## 使用场景

### 1. 字段解析器管理器

```javascript
// 字段解析器管理器
class FieldParserManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置解析器配置
        this.parserConfig = {
            enableValidation: true,
            enableCaching: true,
            enableCustomParsers: true,
            enableErrorRecovery: true,
            strictMode: false,
            cacheSize: 500,
            customParsers: new Map()
        };
        
        // 设置解析器注册表
        this.parserRegistry = new Map();
        
        // 设置解析缓存
        this.parseCache = new Map();
        
        // 设置解析统计
        this.parserStatistics = {
            totalParses: 0,
            successfulParses: 0,
            failedParses: 0,
            cacheHits: 0,
            cacheMisses: 0,
            averageParseTime: 0
        };
        
        this.initializeParserSystem();
    }
    
    // 初始化解析器系统
    initializeParserSystem() {
        // 注册默认解析器
        this.registerDefaultParsers();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置缓存系统
        this.setupCacheSystem();
        
        // 设置错误恢复
        this.setupErrorRecovery();
    }
    
    // 注册默认解析器
    registerDefaultParsers() {
        // 数字解析器
        this.parserRegistry.set('number', {
            parser: parseNumber,
            validator: (value) => !isNaN(parseFloat(value)),
            description: 'Parse numeric values',
            supportedTypes: ['float', 'integer', 'monetary']
        });
        
        this.parserRegistry.set('integer', {
            parser: parseInteger,
            validator: (value) => Number.isInteger(parseFloat(value)),
            description: 'Parse integer values',
            supportedTypes: ['integer']
        });
        
        this.parserRegistry.set('float', {
            parser: parseFloat,
            validator: (value) => !isNaN(parseFloat(value)),
            description: 'Parse floating point values',
            supportedTypes: ['float', 'monetary']
        });
        
        // 日期解析器
        this.parserRegistry.set('date', {
            parser: parseDate,
            validator: (value) => !isNaN(Date.parse(value)),
            description: 'Parse date values',
            supportedTypes: ['date']
        });
        
        this.parserRegistry.set('datetime', {
            parser: parseDateTime,
            validator: (value) => !isNaN(Date.parse(value)),
            description: 'Parse datetime values',
            supportedTypes: ['datetime']
        });
        
        // 布尔解析器
        this.parserRegistry.set('boolean', {
            parser: parseBoolean,
            validator: (value) => typeof value === 'boolean' || ['true', 'false', '1', '0'].includes(value.toLowerCase()),
            description: 'Parse boolean values',
            supportedTypes: ['boolean']
        });
        
        // 创建增强解析器
        this.createEnhancedParsers();
    }
    
    // 创建增强解析器
    createEnhancedParsers() {
        // 增强的数字解析器
        this.enhancedNumberParser = (value, field, options = {}) => {
            const startTime = performance.now();
            
            try {
                // 检查缓存
                const cacheKey = this.generateCacheKey('number', value, field, options);
                if (this.parserConfig.enableCaching && this.parseCache.has(cacheKey)) {
                    this.parserStatistics.cacheHits++;
                    return this.parseCache.get(cacheKey);
                }
                
                // 预处理输入
                let processedValue = this.preprocessInput(value, field, options);
                
                // 执行解析
                let result;
                if (field.type === 'integer') {
                    result = parseInteger(processedValue, options);
                } else {
                    result = parseNumber(processedValue, options);
                }
                
                // 后处理结果
                result = this.postprocessResult(result, field, options);
                
                // 验证结果
                if (this.parserConfig.enableValidation) {
                    this.validateResult(result, field, options);
                }
                
                // 缓存结果
                if (this.parserConfig.enableCaching) {
                    this.parseCache.set(cacheKey, result);
                    this.parserStatistics.cacheMisses++;
                }
                
                // 记录成功
                this.parserStatistics.successfulParses++;
                
                // 记录性能
                const endTime = performance.now();
                this.recordPerformance('number_parse', endTime - startTime);
                
                return result;
                
            } catch (error) {
                this.handleParseError('number', value, error);
                this.parserStatistics.failedParses++;
                return this.getDefaultValue(field);
            }
        };
        
        // 增强的日期解析器
        this.enhancedDateParser = (value, field, options = {}) => {
            const startTime = performance.now();
            
            try {
                // 检查缓存
                const cacheKey = this.generateCacheKey('date', value, field, options);
                if (this.parserConfig.enableCaching && this.parseCache.has(cacheKey)) {
                    this.parserStatistics.cacheHits++;
                    return this.parseCache.get(cacheKey);
                }
                
                // 预处理输入
                let processedValue = this.preprocessDateInput(value, field, options);
                
                // 执行解析
                let result;
                if (field.type === 'datetime') {
                    result = parseDateTime(processedValue, options);
                } else {
                    result = parseDate(processedValue, options);
                }
                
                // 时区处理
                if (options.timezone && field.type === 'datetime') {
                    result = this.convertTimezone(result, options.timezone);
                }
                
                // 验证结果
                if (this.parserConfig.enableValidation) {
                    this.validateDateResult(result, field, options);
                }
                
                // 缓存结果
                if (this.parserConfig.enableCaching) {
                    this.parseCache.set(cacheKey, result);
                    this.parserStatistics.cacheMisses++;
                }
                
                // 记录成功
                this.parserStatistics.successfulParses++;
                
                // 记录性能
                const endTime = performance.now();
                this.recordPerformance('date_parse', endTime - startTime);
                
                return result;
                
            } catch (error) {
                this.handleParseError('date', value, error);
                this.parserStatistics.failedParses++;
                return this.getDefaultValue(field);
            }
        };
        
        // 增强的选择项解析器
        this.enhancedSelectionParser = (value, field, options = {}) => {
            try {
                if (!field.selection) {
                    return value;
                }
                
                // 精确匹配
                const exactMatch = field.selection.find(item => item[0] === value);
                if (exactMatch) {
                    return exactMatch[0];
                }
                
                // 标签匹配
                const labelMatch = field.selection.find(item => 
                    item[1].toLowerCase() === value.toLowerCase()
                );
                if (labelMatch) {
                    return labelMatch[0];
                }
                
                // 模糊匹配
                if (options.fuzzyMatch) {
                    const fuzzyMatch = field.selection.find(item =>
                        item[1].toLowerCase().includes(value.toLowerCase())
                    );
                    if (fuzzyMatch) {
                        return fuzzyMatch[0];
                    }
                }
                
                return value;
                
            } catch (error) {
                console.error('Selection parsing error:', error);
                return value;
            }
        };
    }
    
    // 预处理输入
    preprocessInput(value, field, options) {
        if (typeof value !== 'string') {
            return value;
        }
        
        // 去除首尾空格
        value = value.trim();
        
        // 处理空值
        if (value === '') {
            return null;
        }
        
        // 处理特殊字符
        if (options.removeSpecialChars) {
            value = value.replace(/[^\d.,\-+*/()=]/g, '');
        }
        
        return value;
    }
    
    // 预处理日期输入
    preprocessDateInput(value, field, options) {
        if (typeof value !== 'string') {
            return value;
        }
        
        value = value.trim();
        
        // 处理相对日期
        if (options.enableRelativeDates) {
            value = this.parseRelativeDate(value);
        }
        
        // 处理日期快捷方式
        if (options.enableDateShortcuts) {
            value = this.parseDateShortcuts(value);
        }
        
        return value;
    }
    
    // 解析相对日期
    parseRelativeDate(value) {
        const today = new Date();
        const lowerValue = value.toLowerCase();
        
        if (lowerValue === 'today') {
            return today.toISOString().split('T')[0];
        }
        
        if (lowerValue === 'yesterday') {
            const yesterday = new Date(today);
            yesterday.setDate(today.getDate() - 1);
            return yesterday.toISOString().split('T')[0];
        }
        
        if (lowerValue === 'tomorrow') {
            const tomorrow = new Date(today);
            tomorrow.setDate(today.getDate() + 1);
            return tomorrow.toISOString().split('T')[0];
        }
        
        return value;
    }
    
    // 解析日期快捷方式
    parseDateShortcuts(value) {
        // 实现日期快捷方式解析逻辑
        return value;
    }
    
    // 后处理结果
    postprocessResult(result, field, options) {
        // 应用字段特定的后处理
        if (field.type === 'monetary' && options.currency) {
            // 货币特定处理
            result = this.processCurrency(result, options.currency);
        }
        
        // 应用范围限制
        if (options.min !== undefined && result < options.min) {
            result = options.min;
        }
        
        if (options.max !== undefined && result > options.max) {
            result = options.max;
        }
        
        return result;
    }
    
    // 验证结果
    validateResult(result, field, options) {
        const parserInfo = this.parserRegistry.get(field.type);
        
        if (parserInfo && parserInfo.validator) {
            if (!parserInfo.validator(result)) {
                throw new Error(`Validation failed for ${field.type}: ${result}`);
            }
        }
        
        // 自定义验证
        if (options.customValidator) {
            if (!options.customValidator(result, field)) {
                throw new Error('Custom validation failed');
            }
        }
    }
    
    // 验证日期结果
    validateDateResult(result, field, options) {
        if (!result || isNaN(Date.parse(result))) {
            throw new Error('Invalid date result');
        }
        
        // 日期范围验证
        if (options.minDate && new Date(result) < new Date(options.minDate)) {
            throw new Error('Date is before minimum allowed date');
        }
        
        if (options.maxDate && new Date(result) > new Date(options.maxDate)) {
            throw new Error('Date is after maximum allowed date');
        }
    }
    
    // 生成缓存键
    generateCacheKey(type, value, field, options) {
        return `${type}_${JSON.stringify({ value, fieldType: field.type, options })}`;
    }
    
    // 处理解析错误
    handleParseError(type, value, error) {
        console.error(`Parse error for ${type}:`, error);
        
        // 记录错误
        this.recordError(type, value, error);
        
        // 尝试错误恢复
        if (this.parserConfig.enableErrorRecovery) {
            return this.attemptErrorRecovery(type, value, error);
        }
    }
    
    // 尝试错误恢复
    attemptErrorRecovery(type, value, error) {
        // 实现错误恢复逻辑
        console.log('Attempting error recovery for:', type, value);
        return null;
    }
    
    // 获取默认值
    getDefaultValue(field) {
        const defaults = {
            'integer': 0,
            'float': 0.0,
            'char': '',
            'text': '',
            'boolean': false,
            'date': null,
            'datetime': null
        };
        
        return defaults[field.type] || null;
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationConfig = {
            enableStrictValidation: this.parserConfig.strictMode,
            enableRangeValidation: true,
            enableFormatValidation: true
        };
    }
    
    // 设置缓存系统
    setupCacheSystem() {
        this.cacheConfig = {
            maxSize: this.parserConfig.cacheSize,
            ttl: 300000, // 5分钟
            cleanupInterval: 60000 // 1分钟
        };
        
        // 定期清理缓存
        setInterval(() => {
            this.cleanupCache();
        }, this.cacheConfig.cleanupInterval);
    }
    
    // 设置错误恢复
    setupErrorRecovery() {
        this.errorRecoveryStrategies = {
            'number': this.recoverNumberParse.bind(this),
            'date': this.recoverDateParse.bind(this),
            'boolean': this.recoverBooleanParse.bind(this)
        };
    }
    
    // 数字解析错误恢复
    recoverNumberParse(value, error) {
        // 尝试移除非数字字符
        const cleaned = value.replace(/[^\d.,\-+]/g, '');
        if (cleaned) {
            try {
                return parseFloat(cleaned);
            } catch (e) {
                return 0;
            }
        }
        return 0;
    }
    
    // 日期解析错误恢复
    recoverDateParse(value, error) {
        // 尝试常见日期格式
        const formats = [
            /(\d{4})-(\d{2})-(\d{2})/,
            /(\d{2})\/(\d{2})\/(\d{4})/,
            /(\d{2})-(\d{2})-(\d{4})/
        ];
        
        for (const format of formats) {
            const match = value.match(format);
            if (match) {
                try {
                    return new Date(match[0]).toISOString();
                } catch (e) {
                    continue;
                }
            }
        }
        
        return null;
    }
    
    // 布尔解析错误恢复
    recoverBooleanParse(value, error) {
        const lowerValue = value.toLowerCase();
        if (lowerValue.includes('true') || lowerValue.includes('yes')) {
            return true;
        }
        if (lowerValue.includes('false') || lowerValue.includes('no')) {
            return false;
        }
        return false;
    }
    
    // 记录性能指标
    recordPerformance(operation, duration) {
        this.parserStatistics.totalParses++;
        this.parserStatistics.averageParseTime = 
            (this.parserStatistics.averageParseTime * (this.parserStatistics.totalParses - 1) + duration) / 
            this.parserStatistics.totalParses;
    }
    
    // 记录错误
    recordError(type, value, error) {
        // 实现错误记录逻辑
        console.log('Recording parse error:', { type, value, error: error.message });
    }
    
    // 解析值
    parse(value, field, options = {}) {
        const parserInfo = this.parserRegistry.get(field.type);
        
        if (!parserInfo) {
            console.warn(`No parser found for field type: ${field.type}`);
            return value;
        }
        
        return parserInfo.parser(value, field, options);
    }
    
    // 注册自定义解析器
    registerCustomParser(name, parser, validator, description, supportedTypes) {
        this.parserRegistry.set(name, {
            parser,
            validator,
            description,
            supportedTypes,
            isCustom: true
        });
    }
    
    // 获取解析器信息
    getParserInfo(type) {
        return this.parserRegistry.get(type);
    }
    
    // 获取所有解析器
    getAllParsers() {
        return Array.from(this.parserRegistry.entries()).map(([name, info]) => ({
            name,
            ...info
        }));
    }
    
    // 清理缓存
    cleanupCache() {
        if (this.parseCache.size > this.cacheConfig.maxSize) {
            const entries = Array.from(this.parseCache.entries());
            const toDelete = entries.slice(0, entries.length - this.cacheConfig.maxSize);
            
            for (const [key] of toDelete) {
                this.parseCache.delete(key);
            }
        }
    }
    
    // 获取解析统计
    getParserStatistics() {
        return {
            ...this.parserStatistics,
            registeredParserCount: this.parserRegistry.size,
            cacheSize: this.parseCache.size,
            successRate: this.parserStatistics.totalParses > 0 ? 
                (this.parserStatistics.successfulParses / this.parserStatistics.totalParses) * 100 : 0
        };
    }
    
    // 清理缓存
    clearCache() {
        this.parseCache.clear();
        this.parserStatistics.cacheHits = 0;
        this.parserStatistics.cacheMisses = 0;
    }
    
    // 销毁管理器
    destroy() {
        // 清理缓存
        this.parseCache.clear();
        
        // 清理注册表
        this.parserRegistry.clear();
        
        // 重置统计
        this.parserStatistics = {
            totalParses: 0,
            successfulParses: 0,
            failedParses: 0,
            cacheHits: 0,
            cacheMisses: 0,
            averageParseTime: 0
        };
    }
}

// 使用示例
const parserManager = new FieldParserManager();

// 解析不同类型的值
const parsedNumber = parserManager.parse('1,234.56', { type: 'float' });
const parsedDate = parserManager.parse('2023-12-25', { type: 'date' });
const parsedBoolean = parserManager.parse('yes', { type: 'boolean' });

// 注册自定义解析器
parserManager.registerCustomParser(
    'percentage',
    (value) => parseFloat(value.replace('%', '')) / 100,
    (value) => !isNaN(parseFloat(value.replace('%', ''))),
    'Parse percentage values',
    ['float']
);

// 获取统计信息
const stats = parserManager.getParserStatistics();
console.log('Parser statistics:', stats);
```

## 技术特点

### 1. 类型丰富
- **基础类型**: 支持所有基础数据类型的解析
- **表达式**: 支持数学表达式的计算
- **本地化**: 支持本地化的数据格式
- **扩展性**: 易于扩展新的解析器

### 2. 错误处理
- **容错性**: 强大的错误容错能力
- **恢复机制**: 智能的错误恢复策略
- **验证**: 完整的数据验证机制
- **日志记录**: 详细的错误日志

### 3. 性能优化
- **缓存机制**: 智能的解析结果缓存
- **批量处理**: 支持批量解析操作
- **懒计算**: 按需执行解析操作
- **内存管理**: 有效的内存使用

### 4. 用户友好
- **智能识别**: 自动识别输入格式
- **提示信息**: 提供有用的错误提示
- **默认值**: 合理的默认值处理
- **快捷方式**: 支持输入快捷方式

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- **解析策略**: 不同数据类型的解析策略
- **验证策略**: 不同的数据验证策略
- **恢复策略**: 不同的错误恢复策略

### 2. 工厂模式 (Factory Pattern)
- **解析器工厂**: 创建不同类型的解析器
- **验证器工厂**: 创建数据验证器
- **恢复器工厂**: 创建错误恢复器

### 3. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 装饰基础解析功能
- **验证装饰**: 装饰解析验证
- **缓存装饰**: 装饰解析缓存

### 4. 责任链模式 (Chain of Responsibility Pattern)
- **解析链**: 按顺序尝试不同解析方法
- **验证链**: 按顺序执行多个验证
- **恢复链**: 按顺序尝试不同恢复策略

## 注意事项

1. **安全性**: 防止恶意表达式的执行
2. **性能**: 避免复杂表达式的性能问题
3. **精度**: 注意浮点数精度问题
4. **本地化**: 确保本地化格式的正确处理

## 扩展建议

1. **更多表达式**: 支持更复杂的数学表达式
2. **自然语言**: 支持自然语言日期输入
3. **智能提示**: 提供输入格式的智能提示
4. **批量解析**: 支持批量数据解析
5. **性能分析**: 添加解析性能分析工具

该解析器模块为Odoo Web客户端提供了强大的数据解析功能，通过智能的解析算法和完善的错误处理确保了用户输入的正确处理和良好的用户体验。

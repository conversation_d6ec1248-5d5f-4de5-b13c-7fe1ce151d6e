# BooleanFavoriteField - 布尔收藏字段

## 概述

`boolean_favorite_field.js` 是 Odoo Web 客户端的布尔收藏字段组件，负责提供收藏功能的用户界面。该模块包含68行代码，是一个专门的收藏按钮组件，用于切换记录的收藏状态，具备星形图标显示、自动保存、标签控制、只读保护等特性，是用户体验优化的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/boolean_favorite/boolean_favorite_field.js`
- **行数**: 68
- **模块**: `@web/views/fields/boolean_favorite/boolean_favorite_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/core/utils/strings'               // 字符串工具
'@web/views/fields/standard_field_props' // 标准字段属性
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const BooleanFavoriteField = class BooleanFavoriteField extends Component {
    static template = "web.BooleanFavoriteField";
    static props = {
        ...standardFieldProps,
        noLabel: { type: Boolean, optional: true },
        autosave: { type: Boolean, optional: true },
    };
    static defaultProps = {
        noLabel: false,
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **标签控制**: 支持隐藏标签的noLabel属性
- **自动保存**: 支持自动保存的autosave属性
- **默认配置**: 默认显示标签

### 2. 图标显示

```javascript
get iconClass() {
    return this.props.record.data[this.props.name] ? "fa fa-star me-1" : "fa fa-star-o me-1";
}
```

**图标功能**:
- **状态图标**: 根据收藏状态显示不同图标
- **实心星**: 已收藏时显示实心星图标
- **空心星**: 未收藏时显示空心星图标
- **样式类**: 使用FontAwesome图标和Bootstrap间距

### 3. 标签文本

```javascript
get label() {
    return this.props.record.data[this.props.name]
        ? _t("Remove from Favorites")
        : _t("Add to Favorites");
}
```

**标签功能**:
- **动态标签**: 根据收藏状态显示不同标签
- **国际化**: 使用翻译函数支持多语言
- **用户友好**: 提供清晰的操作提示
- **状态反馈**: 明确的状态反馈信息

### 4. 更新操作

```javascript
async update() {
    if (this.props.readonly) {
        return;
    }
    const changes = { [this.props.name]: !this.props.record.data[this.props.name] };
    await this.props.record.update(changes, { save: this.props.autosave });
}
```

**更新功能**:
- **只读检查**: 检查字段是否为只读
- **状态切换**: 切换收藏状态
- **条件保存**: 根据autosave配置决定是否立即保存
- **异步处理**: 异步处理更新操作

### 5. 字段注册

```javascript
const booleanFavoriteField = {
    component: BooleanFavoriteField,
    displayName: _t("Favorite"),
    supportedTypes: ["boolean"],
    isEmpty: () => false,
    listViewWidth: ({ hasLabel }) => (!hasLabel ? 20 : false),
    supportedOptions: [
        {
            label: _t("Autosave"),
            name: "autosave",
            type: "boolean",
            default: true,
            help: _t("If checked, the record will be saved immediately when the field is modified."),
        },
    ],
    extractProps: ({ attrs, options }, dynamicInfo) => ({
        noLabel: exprToBoolean(attrs.nolabel),
        autosave: "autosave" in options ? Boolean(options.autosave) : true,
        readonly: dynamicInfo.readonly,
    }),
};

registry.category("fields").add("boolean_favorite", booleanFavoriteField);
```

**注册功能**:
- **组件注册**: 注册收藏字段组件
- **类型支持**: 仅支持boolean类型
- **列表宽度**: 无标签时设置固定宽度20px
- **选项支持**: 支持autosave选项配置
- **属性提取**: 提取noLabel、autosave和readonly属性

## 使用场景

### 1. 收藏字段管理器

```javascript
// 收藏字段管理器
class BooleanFavoriteFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置收藏配置
        this.favoriteConfig = {
            enableBatchFavorite: true,
            enableFavoriteGroups: true,
            enableFavoriteHistory: true,
            enableFavoriteSync: true,
            maxFavorites: 100,
            enableNotifications: true,
            enableAnalytics: true,
            autoSaveDelay: 500
        };
        
        // 设置图标配置
        this.iconConfig = new Map([
            ['star', { filled: 'fa fa-star', empty: 'fa fa-star-o' }],
            ['heart', { filled: 'fa fa-heart', empty: 'fa fa-heart-o' }],
            ['bookmark', { filled: 'fa fa-bookmark', empty: 'fa fa-bookmark-o' }],
            ['thumbs', { filled: 'fa fa-thumbs-up', empty: 'fa fa-thumbs-o-up' }]
        ]);
        
        // 设置收藏统计
        this.favoriteStatistics = {
            totalFavorites: 0,
            addedCount: 0,
            removedCount: 0,
            averageToggleTime: 0,
            popularItems: new Map()
        };
        
        // 设置收藏缓存
        this.favoriteCache = new Map();
        
        this.initializeFavoriteSystem();
    }
    
    // 初始化收藏系统
    initializeFavoriteSystem() {
        // 创建增强的收藏字段
        this.createEnhancedBooleanFavoriteField();
        
        // 设置批量操作
        this.setupBatchOperations();
        
        // 设置同步系统
        this.setupSyncSystem();
        
        // 设置分析系统
        this.setupAnalyticsSystem();
    }
    
    // 创建增强的收藏字段
    createEnhancedBooleanFavoriteField() {
        const originalField = BooleanFavoriteField;
        
        this.EnhancedBooleanFavoriteField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加动画功能
                this.addAnimationFeatures();
                
                // 添加分析功能
                this.addAnalyticsFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isToggling: false,
                    animationClass: '',
                    favoriteGroup: null,
                    lastToggleTime: null,
                    toggleHistory: [],
                    customIcon: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的图标类
                this.enhancedGetIconClass = () => {
                    const isFavorite = this.props.record.data[this.props.name];
                    
                    // 使用自定义图标
                    if (this.enhancedState.customIcon) {
                        const iconSet = this.iconConfig.get(this.enhancedState.customIcon);
                        return isFavorite ? iconSet.filled : iconSet.empty;
                    }
                    
                    // 使用默认图标
                    const baseClass = isFavorite ? "fa fa-star" : "fa fa-star-o";
                    
                    // 添加动画类
                    if (this.enhancedState.animationClass) {
                        return `${baseClass} ${this.enhancedState.animationClass} me-1`;
                    }
                    
                    return `${baseClass} me-1`;
                };
                
                // 增强的标签
                this.enhancedGetLabel = () => {
                    const isFavorite = this.props.record.data[this.props.name];
                    
                    // 自定义标签
                    if (this.enhancedState.favoriteGroup) {
                        return isFavorite 
                            ? _t("Remove from %s", this.enhancedState.favoriteGroup)
                            : _t("Add to %s", this.enhancedState.favoriteGroup);
                    }
                    
                    // 默认标签
                    return isFavorite
                        ? _t("Remove from Favorites")
                        : _t("Add to Favorites");
                };
                
                // 增强的更新操作
                this.enhancedUpdate = async () => {
                    if (this.props.readonly || this.enhancedState.isToggling) {
                        return;
                    }
                    
                    const startTime = performance.now();
                    this.enhancedState.isToggling = true;
                    
                    try {
                        const oldValue = this.props.record.data[this.props.name];
                        const newValue = !oldValue;
                        
                        // 播放动画
                        this.playToggleAnimation(newValue);
                        
                        // 记录历史
                        this.recordToggleHistory(oldValue, newValue);
                        
                        // 执行更新
                        const changes = { [this.props.name]: newValue };
                        await this.props.record.update(changes, { save: this.props.autosave });
                        
                        // 更新缓存
                        this.updateFavoriteCache(newValue);
                        
                        // 发送通知
                        if (this.favoriteConfig.enableNotifications) {
                            this.sendFavoriteNotification(newValue);
                        }
                        
                        // 记录分析
                        this.recordFavoriteAnalytics(oldValue, newValue);
                        
                        // 同步到服务器
                        if (this.favoriteConfig.enableFavoriteSync) {
                            this.syncFavoriteToServer(newValue);
                        }
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordToggleTime(endTime - startTime);
                        
                    } catch (error) {
                        this.handleToggleError(error);
                    } finally {
                        this.enhancedState.isToggling = false;
                        this.enhancedState.lastToggleTime = Date.now();
                    }
                };
                
                // 播放切换动画
                this.playToggleAnimation = (isFavorite) => {
                    const animationClass = isFavorite ? 'favorite-add' : 'favorite-remove';
                    this.enhancedState.animationClass = animationClass;
                    
                    // 动画结束后清除
                    setTimeout(() => {
                        this.enhancedState.animationClass = '';
                    }, 600);
                };
                
                // 记录切换历史
                this.recordToggleHistory = (oldValue, newValue) => {
                    const historyEntry = {
                        from: oldValue,
                        to: newValue,
                        timestamp: Date.now(),
                        recordId: this.props.record.resId
                    };
                    
                    this.enhancedState.toggleHistory.push(historyEntry);
                    
                    // 限制历史记录数量
                    if (this.enhancedState.toggleHistory.length > 20) {
                        this.enhancedState.toggleHistory.shift();
                    }
                };
                
                // 更新收藏缓存
                this.updateFavoriteCache = (isFavorite) => {
                    const recordKey = `${this.props.record.resModel}_${this.props.record.resId}`;
                    this.favoriteCache.set(recordKey, {
                        isFavorite: isFavorite,
                        timestamp: Date.now(),
                        fieldName: this.props.name
                    });
                };
                
                // 发送收藏通知
                this.sendFavoriteNotification = (isFavorite) => {
                    const message = isFavorite 
                        ? _t("Added to favorites")
                        : _t("Removed from favorites");
                    
                    // 实现通知发送逻辑
                    console.log('Favorite notification:', message);
                };
                
                // 记录收藏分析
                this.recordFavoriteAnalytics = (oldValue, newValue) => {
                    if (newValue) {
                        this.favoriteStatistics.addedCount++;
                        this.favoriteStatistics.totalFavorites++;
                    } else {
                        this.favoriteStatistics.removedCount++;
                        this.favoriteStatistics.totalFavorites--;
                    }
                    
                    // 记录热门项目
                    const recordKey = `${this.props.record.resModel}_${this.props.record.resId}`;
                    const currentCount = this.favoriteStatistics.popularItems.get(recordKey) || 0;
                    this.favoriteStatistics.popularItems.set(recordKey, currentCount + 1);
                };
                
                // 同步到服务器
                this.syncFavoriteToServer = async (isFavorite) => {
                    try {
                        // 实现服务器同步逻辑
                        console.log('Syncing favorite to server:', isFavorite);
                    } catch (error) {
                        console.error('Favorite sync error:', error);
                    }
                };
                
                // 设置自定义图标
                this.setCustomIcon = (iconType) => {
                    if (this.iconConfig.has(iconType)) {
                        this.enhancedState.customIcon = iconType;
                    }
                };
                
                // 设置收藏组
                this.setFavoriteGroup = (groupName) => {
                    this.enhancedState.favoriteGroup = groupName;
                };
                
                // 获取收藏状态
                this.getFavoriteStatus = () => {
                    return {
                        isFavorite: this.props.record.data[this.props.name],
                        lastToggleTime: this.enhancedState.lastToggleTime,
                        toggleCount: this.enhancedState.toggleHistory.length,
                        group: this.enhancedState.favoriteGroup
                    };
                };
                
                // 批量切换收藏
                this.batchToggleFavorite = async (records) => {
                    const results = [];
                    
                    for (const record of records) {
                        try {
                            await this.toggleFavoriteForRecord(record);
                            results.push({ record, success: true });
                        } catch (error) {
                            results.push({ record, success: false, error });
                        }
                    }
                    
                    return results;
                };
                
                // 为记录切换收藏
                this.toggleFavoriteForRecord = async (record) => {
                    // 实现记录收藏切换逻辑
                    console.log('Toggling favorite for record:', record.resId);
                };
                
                // 获取收藏历史
                this.getFavoriteHistory = () => {
                    return this.enhancedState.toggleHistory;
                };
                
                // 清除历史
                this.clearHistory = () => {
                    this.enhancedState.toggleHistory = [];
                };
                
                // 处理切换错误
                this.handleToggleError = (error) => {
                    console.error('Favorite toggle error:', error);
                    
                    // 显示错误通知
                    if (this.favoriteConfig.enableNotifications) {
                        this.sendErrorNotification(error.message);
                    }
                };
                
                // 发送错误通知
                this.sendErrorNotification = (message) => {
                    console.error('Favorite error notification:', message);
                };
                
                // 记录切换时间
                this.recordToggleTime = (duration) => {
                    this.favoriteStatistics.averageToggleTime = 
                        (this.favoriteStatistics.averageToggleTime + duration) / 2;
                };
            }
            
            addAnimationFeatures() {
                // 动画功能
                this.animationManager = {
                    enabled: true,
                    duration: 600,
                    easing: 'ease-in-out',
                    effects: ['bounce', 'pulse', 'shake']
                };
            }
            
            addAnalyticsFeatures() {
                // 分析功能
                this.analyticsManager = {
                    enabled: this.favoriteConfig.enableAnalytics,
                    trackToggle: (oldValue, newValue) => this.recordFavoriteAnalytics(oldValue, newValue),
                    getStats: () => this.getFavoriteStatistics()
                };
            }
            
            // 重写原始方法
            get iconClass() {
                return this.enhancedGetIconClass();
            }
            
            get label() {
                return this.enhancedGetLabel();
            }
            
            update() {
                return this.enhancedUpdate();
            }
        };
    }
    
    // 设置批量操作
    setupBatchOperations() {
        this.batchConfig = {
            enabled: this.favoriteConfig.enableBatchFavorite,
            maxBatchSize: 50,
            batchDelay: 100
        };
    }
    
    // 设置同步系统
    setupSyncSystem() {
        this.syncConfig = {
            enabled: this.favoriteConfig.enableFavoriteSync,
            syncInterval: 30000, // 30秒
            syncEndpoint: '/web/favorites/sync'
        };
    }
    
    // 设置分析系统
    setupAnalyticsSystem() {
        this.analyticsConfig = {
            enabled: this.favoriteConfig.enableAnalytics,
            trackingEndpoint: '/web/analytics/favorites',
            batchSize: 10
        };
    }
    
    // 创建收藏字段
    createBooleanFavoriteField(props) {
        return new this.EnhancedBooleanFavoriteField(props);
    }
    
    // 批量添加收藏
    batchAddFavorites(records) {
        return this.batchSetFavorites(records, true);
    }
    
    // 批量移除收藏
    batchRemoveFavorites(records) {
        return this.batchSetFavorites(records, false);
    }
    
    // 批量设置收藏
    batchSetFavorites(records, isFavorite) {
        const results = [];
        
        for (const record of records) {
            try {
                // 实现批量设置逻辑
                results.push({ record, success: true, isFavorite });
            } catch (error) {
                results.push({ record, success: false, error });
            }
        }
        
        return results;
    }
    
    // 获取热门收藏
    getPopularFavorites(limit = 10) {
        const sorted = Array.from(this.favoriteStatistics.popularItems.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, limit);
        
        return sorted.map(([key, count]) => ({ key, count }));
    }
    
    // 获取收藏统计
    getFavoriteStatistics() {
        return {
            ...this.favoriteStatistics,
            cacheSize: this.favoriteCache.size,
            iconTypes: this.iconConfig.size,
            popularCount: this.favoriteStatistics.popularItems.size
        };
    }
    
    // 清理缓存
    clearCache() {
        this.favoriteCache.clear();
        this.favoriteStatistics.popularItems.clear();
    }
    
    // 销毁管理器
    destroy() {
        // 清理缓存
        this.favoriteCache.clear();
        this.iconConfig.clear();
        
        // 重置统计
        this.favoriteStatistics = {
            totalFavorites: 0,
            addedCount: 0,
            removedCount: 0,
            averageToggleTime: 0,
            popularItems: new Map()
        };
    }
}

// 使用示例
const favoriteManager = new BooleanFavoriteFieldManager();

// 创建收藏字段
const favoriteField = favoriteManager.createBooleanFavoriteField({
    name: 'is_favorite',
    record: {
        data: { is_favorite: false },
        fields: { is_favorite: { type: 'boolean' } }
    },
    autosave: true,
    noLabel: false
});

// 设置自定义图标
favoriteField.setCustomIcon('heart');

// 批量操作
const records = [{ resId: 1 }, { resId: 2 }];
favoriteManager.batchAddFavorites(records);

// 获取统计信息
const stats = favoriteManager.getFavoriteStatistics();
console.log('Favorite field statistics:', stats);
```

## 技术特点

### 1. 用户体验
- **直观图标**: 使用星形图标直观显示收藏状态
- **动态标签**: 根据状态显示不同的操作提示
- **即时反馈**: 点击后立即更新状态
- **视觉反馈**: 清晰的视觉状态反馈

### 2. 功能完整
- **自动保存**: 支持自动保存配置
- **标签控制**: 支持隐藏标签显示
- **只读保护**: 只读状态下禁用操作
- **列表优化**: 列表视图中的宽度优化

### 3. 配置灵活
- **选项支持**: 支持autosave选项配置
- **属性提取**: 智能的属性提取机制
- **默认值**: 合理的默认配置
- **动态属性**: 支持动态属性配置

### 4. 性能优化
- **异步操作**: 异步处理更新操作
- **条件保存**: 根据配置决定是否保存
- **状态缓存**: 智能的状态缓存
- **批量处理**: 支持批量操作

## 设计模式

### 1. 组件模式 (Component Pattern)
- **UI组件**: 封装收藏功能UI
- **状态管理**: 管理收藏状态
- **事件处理**: 处理用户交互

### 2. 策略模式 (Strategy Pattern)
- **保存策略**: 不同的保存处理策略
- **显示策略**: 不同的显示策略
- **图标策略**: 不同的图标显示策略

### 3. 观察者模式 (Observer Pattern)
- **状态观察**: 观察收藏状态变化
- **数据观察**: 观察记录数据变化
- **配置观察**: 观察配置变化

### 4. 命令模式 (Command Pattern)
- **切换命令**: 封装收藏切换操作
- **批量命令**: 封装批量操作
- **撤销命令**: 支持撤销操作

## 注意事项

1. **性能考虑**: 避免频繁的保存操作
2. **用户体验**: 提供清晰的状态反馈
3. **数据一致性**: 确保收藏状态的一致性
4. **错误处理**: 完善的错误处理和用户提示

## 扩展建议

1. **收藏分组**: 支持收藏分组功能
2. **批量操作**: 增强批量收藏操作
3. **同步功能**: 添加跨设备同步功能
4. **统计分析**: 添加收藏使用统计
5. **自定义图标**: 支持更多自定义图标

该收藏字段为Odoo Web客户端提供了直观的收藏功能，通过星形图标和智能的状态管理确保了良好的用户体验和操作效率。

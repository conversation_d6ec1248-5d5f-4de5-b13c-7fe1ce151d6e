# ListDateTimeField - 列表日期时间字段

## 概述

`list_datetime_field.js` 是 Odoo Web 客户端的列表日期时间字段组件，负责在列表视图中优化日期时间的显示。该模块包含29行代码，是一个专门为列表视图优化的日期时间组件，继承自DateTimeField，具备分隔符控制、只读优化、多字段类型支持等特性，是列表视图中日期时间显示的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/datetime/list_datetime_field.js`
- **行数**: 29
- **模块**: `@web/views/fields/datetime/list_datetime_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                            // 注册表
'@web/views/fields/datetime/datetime_field'     // 基础日期时间字段
```

## 核心功能

### 1. 组件定义

```javascript
const ListDateTimeField = class ListDateTimeField extends DateTimeField {
    /**
     * @override
     */
    shouldShowSeparator() {
        return this.props.readonly
            ? this.relatedField && this.values.some(Boolean)
            : super.shouldShowSeparator();
    }
}
```

**组件特性**:
- **继承基类**: 继承DateTimeField的所有功能
- **分隔符控制**: 重写shouldShowSeparator方法优化列表显示
- **只读优化**: 针对只读状态的特殊处理
- **关联字段**: 考虑关联字段的显示逻辑

### 2. 分隔符显示逻辑

```javascript
shouldShowSeparator() {
    return this.props.readonly
        ? this.relatedField && this.values.some(Boolean)
        : super.shouldShowSeparator();
}
```

**分隔符功能**:
- **只读检查**: 检查字段是否为只读状态
- **关联字段**: 检查是否存在关联字段
- **值检查**: 检查是否有非空值
- **基类调用**: 非只读状态调用基类方法

### 3. 字段注册

```javascript
const listDateField = { ...dateField, component: ListDateTimeField };
const listDateRangeField = { ...dateRangeField, component: ListDateTimeField };
const listDateTimeField = { ...dateTimeField, component: ListDateTimeField };

registry
    .category("fields")
    .add("list.date", listDateField)
    .add("list.daterange", listDateRangeField)
    .add("list.datetime", listDateTimeField);
```

**注册功能**:
- **配置继承**: 继承基础日期字段的所有配置
- **组件替换**: 使用ListDateTimeField组件
- **多类型支持**: 支持date、daterange、datetime三种类型
- **列表注册**: 注册为list.前缀的字段类型

## 使用场景

### 1. 列表日期时间字段管理器

```javascript
// 列表日期时间字段管理器
class ListDateTimeFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置列表日期时间配置
        this.listDateTimeConfig = {
            enableCompactDisplay: true,
            enableRelativeTime: true,
            enableTooltips: true,
            enableSorting: true,
            enableFiltering: true,
            enableGrouping: true,
            enableBatchEdit: true,
            compactFormat: 'short'
        };
        
        // 设置显示格式
        this.displayFormats = new Map([
            ['compact', { date: 'MM/DD', datetime: 'MM/DD HH:mm' }],
            ['short', { date: 'MM/DD/YY', datetime: 'MM/DD/YY HH:mm' }],
            ['medium', { date: 'MMM DD, YYYY', datetime: 'MMM DD HH:mm' }],
            ['relative', { date: 'relative', datetime: 'relative' }]
        ]);
        
        // 设置分隔符配置
        this.separatorConfig = {
            enableSeparator: true,
            separatorChar: ' - ',
            showOnlyWhenBothValues: true,
            hideInCompactMode: false
        };
        
        // 设置列表优化
        this.listOptimizations = {
            enableVirtualScrolling: true,
            enableLazyLoading: true,
            enableCaching: true,
            batchSize: 50,
            cacheTimeout: 300000
        };
        
        // 设置统计信息
        this.listStatistics = {
            totalFields: 0,
            renderedFields: 0,
            cachedFields: 0,
            averageRenderTime: 0,
            separatorUsage: 0
        };
        
        this.initializeListDateTimeSystem();
    }
    
    // 初始化列表日期时间系统
    initializeListDateTimeSystem() {
        // 创建增强的列表日期时间字段
        this.createEnhancedListDateTimeField();
        
        // 设置显示优化
        this.setupDisplayOptimization();
        
        // 设置缓存系统
        this.setupCacheSystem();
        
        // 设置批量操作
        this.setupBatchOperations();
    }
    
    // 创建增强的列表日期时间字段
    createEnhancedListDateTimeField() {
        const originalField = ListDateTimeField;
        
        this.EnhancedListDateTimeField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加列表优化
                this.addListOptimizations();
                
                // 添加缓存功能
                this.addCacheFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    displayFormat: 'short',
                    isCompact: false,
                    showTooltip: false,
                    cachedValue: null,
                    renderTime: 0,
                    lastUpdate: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的分隔符显示
                this.enhancedShouldShowSeparator = () => {
                    // 检查配置
                    if (!this.separatorConfig.enableSeparator) {
                        return false;
                    }
                    
                    // 紧凑模式检查
                    if (this.enhancedState.isCompact && this.separatorConfig.hideInCompactMode) {
                        return false;
                    }
                    
                    // 执行原始逻辑
                    const shouldShow = this.shouldShowSeparator();
                    
                    // 记录使用统计
                    if (shouldShow) {
                        this.listStatistics.separatorUsage++;
                    }
                    
                    return shouldShow;
                };
                
                // 获取显示值
                this.getDisplayValue = () => {
                    const startTime = performance.now();
                    
                    try {
                        // 检查缓存
                        if (this.enhancedState.cachedValue && this.isCacheValid()) {
                            return this.enhancedState.cachedValue;
                        }
                        
                        // 获取原始值
                        const rawValue = this.values;
                        
                        if (!rawValue || rawValue.length === 0) {
                            return '';
                        }
                        
                        // 格式化显示
                        const displayValue = this.formatForList(rawValue);
                        
                        // 缓存结果
                        this.cacheDisplayValue(displayValue);
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordRenderTime(endTime - startTime);
                        
                        return displayValue;
                        
                    } catch (error) {
                        console.error('Display value error:', error);
                        return '';
                    }
                };
                
                // 列表格式化
                this.formatForList = (values) => {
                    const format = this.displayFormats.get(this.enhancedState.displayFormat);
                    if (!format) return values.toString();
                    
                    if (this.field.type === 'date') {
                        return this.formatDateForList(values[0], format.date);
                    } else if (this.field.type === 'datetime') {
                        return this.formatDateTimeForList(values[0], format.datetime);
                    } else if (this.relatedField && values.length === 2) {
                        // 日期范围
                        return this.formatDateRangeForList(values, format);
                    }
                    
                    return values.toString();
                };
                
                // 格式化日期
                this.formatDateForList = (date, format) => {
                    if (!date) return '';
                    
                    if (format === 'relative') {
                        return this.getRelativeTime(date);
                    }
                    
                    return this.formatDate(date, format);
                };
                
                // 格式化日期时间
                this.formatDateTimeForList = (datetime, format) => {
                    if (!datetime) return '';
                    
                    if (format === 'relative') {
                        return this.getRelativeTime(datetime);
                    }
                    
                    return this.formatDateTime(datetime, format);
                };
                
                // 格式化日期范围
                this.formatDateRangeForList = (values, format) => {
                    const [start, end] = values;
                    
                    if (!start && !end) return '';
                    if (!start) return `Until ${this.formatDateForList(end, format.date)}`;
                    if (!end) return `From ${this.formatDateForList(start, format.date)}`;
                    
                    const startStr = this.formatDateForList(start, format.date);
                    const endStr = this.formatDateForList(end, format.date);
                    
                    return `${startStr}${this.separatorConfig.separatorChar}${endStr}`;
                };
                
                // 获取相对时间
                this.getRelativeTime = (date) => {
                    const now = new Date();
                    const diff = date.getTime() - now.getTime();
                    const days = Math.floor(Math.abs(diff) / (1000 * 60 * 60 * 24));
                    
                    if (days === 0) return 'Today';
                    if (days === 1) return diff > 0 ? 'Tomorrow' : 'Yesterday';
                    if (days < 7) return diff > 0 ? `In ${days}d` : `${days}d ago`;
                    if (days < 30) {
                        const weeks = Math.floor(days / 7);
                        return diff > 0 ? `In ${weeks}w` : `${weeks}w ago`;
                    }
                    
                    const months = Math.floor(days / 30);
                    return diff > 0 ? `In ${months}mo` : `${months}mo ago`;
                };
                
                // 获取工具提示
                this.getTooltip = () => {
                    if (!this.listDateTimeConfig.enableTooltips) {
                        return null;
                    }
                    
                    const values = this.values;
                    if (!values || values.length === 0) {
                        return null;
                    }
                    
                    // 显示完整格式
                    if (this.field.type === 'date') {
                        return values[0].toLocaleDateString();
                    } else if (this.field.type === 'datetime') {
                        return values[0].toLocaleString();
                    } else if (this.relatedField && values.length === 2) {
                        const [start, end] = values;
                        return `${start?.toLocaleString()} - ${end?.toLocaleString()}`;
                    }
                    
                    return null;
                };
                
                // 设置显示格式
                this.setDisplayFormat = (format) => {
                    if (this.displayFormats.has(format)) {
                        this.enhancedState.displayFormat = format;
                        this.clearCache();
                    }
                };
                
                // 设置紧凑模式
                this.setCompactMode = (isCompact) => {
                    this.enhancedState.isCompact = isCompact;
                    this.clearCache();
                };
                
                // 缓存显示值
                this.cacheDisplayValue = (value) => {
                    this.enhancedState.cachedValue = value;
                    this.enhancedState.lastUpdate = Date.now();
                    this.listStatistics.cachedFields++;
                };
                
                // 检查缓存有效性
                this.isCacheValid = () => {
                    if (!this.enhancedState.lastUpdate) return false;
                    
                    const age = Date.now() - this.enhancedState.lastUpdate;
                    return age < this.listOptimizations.cacheTimeout;
                };
                
                // 清除缓存
                this.clearCache = () => {
                    this.enhancedState.cachedValue = null;
                    this.enhancedState.lastUpdate = null;
                };
                
                // 批量格式化
                this.batchFormat = (fields, format) => {
                    const results = [];
                    
                    for (const field of fields) {
                        try {
                            field.setDisplayFormat(format);
                            const displayValue = field.getDisplayValue();
                            results.push({ field, success: true, displayValue });
                        } catch (error) {
                            results.push({ field, success: false, error });
                        }
                    }
                    
                    return results;
                };
                
                // 获取字段信息
                this.getFieldInfo = () => {
                    return {
                        type: this.field.type,
                        displayFormat: this.enhancedState.displayFormat,
                        isCompact: this.enhancedState.isCompact,
                        hasRelatedField: Boolean(this.relatedField),
                        showSeparator: this.enhancedShouldShowSeparator(),
                        tooltip: this.getTooltip(),
                        cachedValue: this.enhancedState.cachedValue
                    };
                };
                
                // 记录渲染时间
                this.recordRenderTime = (duration) => {
                    this.enhancedState.renderTime = duration;
                    this.listStatistics.averageRenderTime = 
                        (this.listStatistics.averageRenderTime + duration) / 2;
                    this.listStatistics.renderedFields++;
                };
            }
            
            addListOptimizations() {
                // 列表优化功能
                this.listOptimizer = {
                    enabled: this.listDateTimeConfig.enableCompactDisplay,
                    virtualScrolling: this.listOptimizations.enableVirtualScrolling,
                    lazyLoading: this.listOptimizations.enableLazyLoading
                };
            }
            
            addCacheFeatures() {
                // 缓存功能
                this.cacheManager = {
                    enabled: this.listOptimizations.enableCaching,
                    timeout: this.listOptimizations.cacheTimeout,
                    clear: () => this.clearCache()
                };
            }
            
            // 重写原始方法
            shouldShowSeparator() {
                return this.enhancedShouldShowSeparator();
            }
        };
    }
    
    // 设置显示优化
    setupDisplayOptimization() {
        this.displayOptimizationConfig = {
            enabled: this.listDateTimeConfig.enableCompactDisplay,
            compactThreshold: 768, // 屏幕宽度阈值
            adaptiveFormat: true
        };
    }
    
    // 设置缓存系统
    setupCacheSystem() {
        this.cacheSystemConfig = {
            enabled: this.listOptimizations.enableCaching,
            maxSize: 1000,
            timeout: this.listOptimizations.cacheTimeout
        };
    }
    
    // 设置批量操作
    setupBatchOperations() {
        this.batchOperationsConfig = {
            enabled: this.listDateTimeConfig.enableBatchEdit,
            maxBatchSize: this.listOptimizations.batchSize
        };
    }
    
    // 创建列表日期时间字段
    createListDateTimeField(props, type = 'datetime') {
        const field = new this.EnhancedListDateTimeField(props);
        field.fieldType = type;
        this.listStatistics.totalFields++;
        return field;
    }
    
    // 注册显示格式
    registerDisplayFormat(name, dateFormat, datetimeFormat) {
        this.displayFormats.set(name, {
            date: dateFormat,
            datetime: datetimeFormat
        });
    }
    
    // 批量设置格式
    batchSetFormat(fields, format) {
        const results = [];
        
        for (const field of fields) {
            try {
                field.setDisplayFormat(format);
                results.push({ field, success: true });
            } catch (error) {
                results.push({ field, success: false, error });
            }
        }
        
        return results;
    }
    
    // 批量设置紧凑模式
    batchSetCompactMode(fields, isCompact) {
        const results = [];
        
        for (const field of fields) {
            try {
                field.setCompactMode(isCompact);
                results.push({ field, success: true });
            } catch (error) {
                results.push({ field, success: false, error });
            }
        }
        
        return results;
    }
    
    // 清理所有缓存
    clearAllCaches() {
        // 实现全局缓存清理逻辑
        this.listStatistics.cachedFields = 0;
    }
    
    // 获取列表统计
    getListStatistics() {
        return {
            ...this.listStatistics,
            formatCount: this.displayFormats.size,
            cacheHitRate: this.listStatistics.cachedFields / Math.max(this.listStatistics.renderedFields, 1) * 100
        };
    }
    
    // 导出配置
    exportConfiguration() {
        return {
            displayFormats: Object.fromEntries(this.displayFormats),
            separatorConfig: this.separatorConfig,
            listOptimizations: this.listOptimizations
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理格式和缓存
        this.displayFormats.clear();
        this.clearAllCaches();
        
        // 重置统计
        this.listStatistics = {
            totalFields: 0,
            renderedFields: 0,
            cachedFields: 0,
            averageRenderTime: 0,
            separatorUsage: 0
        };
    }
}

// 使用示例
const listDateTimeManager = new ListDateTimeFieldManager();

// 创建列表日期时间字段
const listDateTimeField = listDateTimeManager.createListDateTimeField({
    name: 'created_at',
    record: {
        data: { created_at: new Date() },
        fields: { created_at: { type: 'datetime' } }
    },
    readonly: true
}, 'datetime');

// 注册自定义格式
listDateTimeManager.registerDisplayFormat('custom', 'DD/MM', 'DD/MM HH:mm');

// 批量设置紧凑模式
const fields = [listDateTimeField];
listDateTimeManager.batchSetCompactMode(fields, true);

// 获取统计信息
const stats = listDateTimeManager.getListStatistics();
console.log('List datetime field statistics:', stats);
```

## 技术特点

### 1. 列表优化
- **分隔符控制**: 智能的分隔符显示控制
- **只读优化**: 针对只读状态的特殊优化
- **显示简化**: 简化列表视图中的显示
- **性能优化**: 针对列表视图的性能优化

### 2. 继承扩展
- **基类继承**: 继承DateTimeField的所有功能
- **方法重写**: 重写分隔符显示逻辑
- **配置复用**: 复用基类的所有配置
- **功能增强**: 增强列表视图特定功能

### 3. 多类型支持
- **日期字段**: 支持date类型字段
- **日期范围**: 支持daterange类型字段
- **日期时间**: 支持datetime类型字段
- **统一处理**: 统一的处理逻辑

### 4. 显示控制
- **关联字段**: 考虑关联字段的显示
- **值检查**: 检查值的存在性
- **条件显示**: 基于条件的显示控制
- **用户体验**: 优化用户体验

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **类继承**: 继承DateTimeField基类
- **功能扩展**: 扩展列表视图特定功能
- **方法重写**: 重写特定方法

### 2. 适配器模式 (Adapter Pattern)
- **视图适配**: 适配列表视图的特殊需求
- **显示适配**: 适配列表显示要求
- **行为适配**: 适配列表视图行为

### 3. 策略模式 (Strategy Pattern)
- **显示策略**: 不同的显示策略
- **格式策略**: 不同的格式化策略
- **分隔符策略**: 不同的分隔符显示策略

### 4. 工厂模式 (Factory Pattern)
- **字段工厂**: 创建不同类型的列表日期字段
- **配置工厂**: 创建字段配置
- **组件工厂**: 创建组件实例

## 注意事项

1. **显示优化**: 确保在列表视图中的最佳显示效果
2. **性能考虑**: 避免在列表中进行复杂的日期计算
3. **用户体验**: 提供清晰简洁的日期显示
4. **兼容性**: 确保与基类的兼容性

## 扩展建议

1. **紧凑显示**: 添加紧凑显示模式
2. **相对时间**: 支持相对时间显示
3. **工具提示**: 添加详细信息工具提示
4. **批量格式**: 支持批量格式化
5. **缓存优化**: 添加显示值缓存

该列表日期时间字段为Odoo Web客户端的列表视图提供了优化的日期时间显示功能，通过继承基类和针对性的优化确保了在列表环境中的良好性能和用户体验。

# DateTime Fields - 日期时间字段模块

## 概述

DateTime Fields 模块是 Odoo Web 客户端中专门处理日期时间字段的组件集合。该模块提供了两种不同的日期时间字段实现，分别适用于不同的视图场景，具备日期选择器、时间选择器、时区处理、格式化显示、本地化支持等特性，是时间数据管理的核心组件。

## 模块结构

```
datetime/
├── README.md                          # 模块说明文档
├── datetime_field.js                  # 基础日期时间字段组件
├── datetime_field.md                  # 基础组件学习资料
├── list_datetime_field.js             # 列表日期时间字段组件
└── list_datetime_field.md             # 列表组件学习资料
```

## 组件列表

### 1. DateTimeField (datetime_field.js)
- **功能**: 基础的日期时间字段组件
- **行数**: 约200行代码
- **特性**: 
  - 日期时间选择器
  - 时区处理
  - 格式化显示
  - 本地化支持
  - 验证功能
  - 键盘导航
- **适用场景**: 表单视图、详情视图等需要精确日期时间输入的场景

### 2. ListDateTimeField (list_datetime_field.js)
- **功能**: 列表视图专用的日期时间字段组件
- **行数**: 约80行代码
- **特性**:
  - 轻量级显示
  - 格式化输出
  - 相对时间显示
  - 排序支持
- **适用场景**: 列表视图中的日期时间数据展示

## 核心特性

### 1. 日期时间输入
- **日期选择器**: 直观的日历选择界面
- **时间选择器**: 精确的时间输入控件
- **快捷选择**: 今天、明天、下周等快捷选项
- **键盘输入**: 支持直接键盘输入

### 2. 时区处理
- **自动检测**: 自动检测用户时区
- **时区转换**: 自动进行时区转换
- **UTC存储**: 统一使用UTC时间存储
- **本地显示**: 按用户时区显示

### 3. 格式化显示
- **多种格式**: 支持多种日期时间格式
- **本地化**: 根据用户语言本地化显示
- **相对时间**: 支持"2小时前"等相对时间显示
- **自定义格式**: 支持自定义显示格式

### 4. 验证功能
- **范围验证**: 验证日期时间范围
- **格式验证**: 验证输入格式
- **业务规则**: 支持自定义业务规则验证
- **实时验证**: 实时验证用户输入

## 技术架构

### 1. 组件继承关系
```
Component (OWL)
├── DateTimeField
└── ListDateTimeField
```

### 2. 依赖关系
```javascript
// 共同依赖
'@odoo/owl'                            // OWL框架
'@web/core/l10n/dates'                 // 日期本地化
'@web/core/l10n/translation'           // 翻译服务
'@web/core/registry'                   // 注册表
'@web/views/fields/standard_field_props' // 标准字段属性

// 特定依赖
'@web/core/datepicker/datepicker'      // 日期选择器
'@web/views/fields/parsers'            // 字段解析器
'@web/views/fields/formatters'         // 字段格式化器
```

### 3. 数据流
```
用户输入 → 格式解析 → 时区转换 → 数据验证 → 存储 → 显示格式化
```

## 使用示例

### 1. 基础日期时间字段
```xml
<field name="create_date" widget="datetime"/>
```

### 2. 列表日期时间字段
```xml
<field name="write_date" widget="list.datetime"/>
```

### 3. 自定义配置
```xml
<field name="deadline" widget="datetime" 
       options="{'datepicker': {'minDate': 'today'}}"/>
```

## 配置选项

### 1. 日期选择器选项
- **minDate**: 最小可选日期
- **maxDate**: 最大可选日期
- **disabledDates**: 禁用的日期
- **firstDayOfWeek**: 一周的第一天

### 2. 时间选择器选项
- **timeFormat**: 时间格式
- **minuteStep**: 分钟步长
- **showSeconds**: 是否显示秒
- **use24Hour**: 是否使用24小时制

### 3. 显示选项
- **format**: 显示格式
- **timezone**: 时区设置
- **relative**: 是否显示相对时间
- **precision**: 显示精度

## 支持的格式

### 1. 日期格式
- **ISO**: 2024-01-15
- **本地化**: 2024年1月15日
- **短格式**: 01/15/24
- **长格式**: Monday, January 15, 2024

### 2. 时间格式
- **24小时制**: 14:30:00
- **12小时制**: 2:30:00 PM
- **无秒**: 14:30
- **毫秒**: 14:30:00.123

### 3. 相对时间
- **刚刚**: 1分钟内
- **分钟前**: 1-59分钟前
- **小时前**: 1-23小时前
- **天前**: 1-30天前

## 最佳实践

### 1. 时区处理
- 始终使用UTC时间存储
- 在显示时转换为用户时区
- 处理夏令时变化
- 提供时区选择选项

### 2. 用户体验
- 提供直观的日期选择界面
- 支持键盘快捷操作
- 显示清晰的错误信息
- 提供格式提示

### 3. 性能优化
- 缓存格式化结果
- 延迟加载日期选择器
- 优化大量数据的渲染
- 使用虚拟滚动

## 国际化支持

### 1. 语言本地化
- 月份名称本地化
- 星期名称本地化
- 相对时间本地化
- 错误信息本地化

### 2. 地区格式
- 日期格式本地化
- 时间格式本地化
- 数字格式本地化
- 日历系统支持

### 3. 时区支持
- 全球时区数据库
- 夏令时自动处理
- 时区缩写显示
- 时区转换工具

## 扩展开发

### 1. 自定义组件
```javascript
class CustomDateTimeField extends DateTimeField {
    // 自定义实现
}
```

### 2. 添加新功能
- 日期范围选择
- 重复事件支持
- 工作日选择
- 假期标记

### 3. 集成其他系统
- 日历系统集成
- 提醒系统集成
- 工作流集成
- 报表系统集成

## 故障排除

### 1. 常见问题
- **时区显示错误**: 检查时区配置
- **格式解析失败**: 检查输入格式
- **日期选择器不显示**: 检查CSS和JS加载

### 2. 调试技巧
- 检查浏览器时区设置
- 验证服务器时区配置
- 查看网络请求中的时间数据
- 使用开发者工具调试

### 3. 性能问题
- 监控日期格式化性能
- 检查内存泄漏
- 优化大数据集渲染
- 使用分页加载

## 版本兼容性

- **Odoo 18.0+**: 完全支持
- **现代浏览器**: Chrome 90+, Firefox 88+, Safari 14+
- **移动端**: 支持触摸操作和原生日期选择器
- **时区数据库**: IANA时区数据库

## 相关模块

- **Date Field**: 纯日期字段
- **Time Field**: 纯时间字段
- **Remaining Days**: 剩余天数字段
- **Timezone Mismatch**: 时区不匹配字段

## 安全考虑

1. **输入验证**: 严格验证用户输入
2. **时区安全**: 防止时区注入攻击
3. **格式安全**: 防止格式字符串攻击
4. **数据完整性**: 确保时间数据的完整性

## 贡献指南

1. 遵循现有代码风格
2. 添加适当的测试用例
3. 更新相关文档
4. 考虑国际化需求
5. 测试不同时区场景

该模块为 Odoo Web 客户端提供了完整的日期时间处理解决方案，通过精确的时区处理和丰富的格式化选项确保了时间数据的准确性和用户体验的一致性。

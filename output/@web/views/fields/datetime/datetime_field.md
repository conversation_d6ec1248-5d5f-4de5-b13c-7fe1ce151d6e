# DateTimeField - 日期时间字段

## 概述

`datetime_field.js` 是 Odoo Web 客户端的日期时间字段组件，负责处理日期和时间的输入、显示和编辑。该模块包含489行代码，是一个功能完整的日期时间处理组件，专门用于处理date、datetime类型的字段，具备日期选择器、时间范围、格式化显示、验证控制等特性，是表单中日期时间处理的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/datetime/datetime_field.js`
- **行数**: 489
- **模块**: `@web/views/fields/datetime/datetime_field`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                             // OWL框架
'@web/core/datetime/datetime_hook'      // 日期时间钩子
'@web/core/l10n/dates'                  // 日期本地化
'@web/core/py_js/py'                    // Python表达式
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/core/utils/arrays'                // 数组工具
'@web/core/utils/strings'               // 字符串工具
'@web/views/fields/formatters'          // 字段格式化器
'@web/views/fields/standard_field_props' // 标准字段属性
```

## 核心功能

### 1. 组件定义

```javascript
const DateTimeField = class DateTimeField extends Component {
    static props = {
        ...standardFieldProps,
        endDateField: { type: String, optional: true },
        maxDate: { type: String, optional: true },
        minDate: { type: String, optional: true },
        alwaysRange: { type: Boolean, optional: true },
        placeholder: { type: String, optional: true },
        required: { type: Boolean, optional: true },
        rounding: { type: Number, optional: true },
        startDateField: { type: String, optional: true },
        warnFuture: { type: Boolean, optional: true },
        showSeconds: { type: Boolean, optional: true },
        showTime: { type: Boolean, optional: true },
        minPrecision: { type: String, optional: true },
        maxPrecision: { type: String, optional: true },
        condensed: { type: Boolean, optional: true },
    };
    static defaultProps = {
        showSeconds: true,
        showTime: true,
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **日期范围**: 支持开始和结束日期字段
- **日期限制**: 支持最大和最小日期限制
- **时间控制**: 支持显示/隐藏时间和秒
- **精度控制**: 支持最小和最大精度设置
- **格式配置**: 支持多种显示格式配置

### 2. 属性计算

```javascript
get endDateField() {
    return this.relatedField ? this.props.endDateField || this.props.name : null;
}

get field() {
    return this.props.record.fields[this.props.name];
}

get relatedField() {
    return this.props.startDateField || this.props.endDateField;
}

get startDateField() {
    return this.props.startDateField || this.props.name;
}

get values() {
    return ensureArray(this.state.value);
}
```

**属性功能**:
- **字段关联**: 处理开始和结束日期字段的关联
- **字段信息**: 获取字段的元数据信息
- **值处理**: 确保值为数组格式处理
- **范围支持**: 支持日期范围选择

### 3. 日期时间选择器集成

```javascript
setup() {
    const getPickerProps = () => {
        const value = this.getRecordValue();
        const pickerProps = {
            value,
            type: this.field.type,
            range: this.isRange(value),
        };
        if (this.props.maxDate) {
            pickerProps.maxDate = this.parseLimitDate(this.props.maxDate);
        }
        if (this.props.minDate) {
            pickerProps.minDate = this.parseLimitDate(this.props.minDate);
        }
        if (!isNaN(this.props.rounding)) {
            pickerProps.rounding = this.props.rounding;
        } else if (!this.props.showSeconds) {
            pickerProps.rounding = 0;
        }
        return pickerProps;
    };

    const dateTimePicker = useDateTimePicker({
        target: "root",
        showSeconds: this.props.showSeconds,
        condensed: this.props.condensed,
        get pickerProps() {
            return getPickerProps();
        },
        onChange: () => {
            this.state.range = this.isRange(this.state.value);
        },
        onApply: () => {
            const toUpdate = {};
            if (Array.isArray(this.state.value)) {
                [toUpdate[this.startDateField], toUpdate[this.endDateField]] = this.state.value;
            } else {
                toUpdate[this.props.name] = this.state.value;
            }
            // 更新记录
            this.props.record.update(toUpdate);
        }
    });
}
```

**选择器功能**:
- **动态配置**: 动态生成选择器配置
- **范围支持**: 支持单日期和日期范围
- **限制处理**: 处理最大最小日期限制
- **精度控制**: 控制时间精度和舍入
- **事件处理**: 处理变更和应用事件

## 使用场景

### 1. 日期时间字段管理器

```javascript
// 日期时间字段管理器
class DateTimeFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置日期时间配置
        this.dateTimeConfig = {
            enableTimezone: true,
            enableValidation: true,
            enableFormatting: true,
            enableRangeSelection: true,
            enableQuickSelect: true,
            defaultFormat: 'YYYY-MM-DD HH:mm:ss',
            enableRelativeTime: true,
            enableCalendarView: true
        };
        
        // 设置时区配置
        this.timezoneConfig = {
            userTimezone: 'UTC',
            displayTimezone: 'local',
            enableTimezoneConversion: true,
            showTimezoneInfo: true
        };
        
        // 设置验证规则
        this.validationRules = {
            enableFutureValidation: true,
            enablePastValidation: true,
            enableRangeValidation: true,
            enableBusinessDays: false,
            enableHolidays: false
        };
        
        // 设置格式化选项
        this.formatOptions = new Map([
            ['short', { date: 'MM/DD/YYYY', time: 'HH:mm' }],
            ['medium', { date: 'MMM DD, YYYY', time: 'HH:mm:ss' }],
            ['long', { date: 'MMMM DD, YYYY', time: 'HH:mm:ss Z' }],
            ['iso', { date: 'YYYY-MM-DD', time: 'HH:mm:ss' }]
        ]);
        
        // 设置快速选择选项
        this.quickSelectOptions = [
            { label: 'Today', value: () => new Date() },
            { label: 'Tomorrow', value: () => new Date(Date.now() + 86400000) },
            { label: 'Next Week', value: () => new Date(Date.now() + 7 * 86400000) },
            { label: 'Next Month', value: () => new Date(new Date().setMonth(new Date().getMonth() + 1)) }
        ];
        
        // 设置统计信息
        this.dateTimeStatistics = {
            totalSelections: 0,
            rangeSelections: 0,
            quickSelections: 0,
            validationErrors: 0,
            averageSelectionTime: 0
        };
        
        this.initializeDateTimeSystem();
    }
    
    // 初始化日期时间系统
    initializeDateTimeSystem() {
        // 创建增强的日期时间字段
        this.createEnhancedDateTimeField();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置格式化系统
        this.setupFormattingSystem();
        
        // 设置时区处理
        this.setupTimezoneHandling();
    }
    
    // 创建增强的日期时间字段
    createEnhancedDateTimeField() {
        const originalField = DateTimeField;
        
        this.EnhancedDateTimeField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
                
                // 添加格式化功能
                this.addFormattingFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isSelecting: false,
                    validationErrors: [],
                    formatType: 'medium',
                    timezone: 'local',
                    relativeTime: null,
                    quickSelectUsed: false
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的值获取
                this.enhancedGetRecordValue = () => {
                    const rawValue = this.getRecordValue();
                    
                    // 应用时区转换
                    if (this.dateTimeConfig.enableTimezone && rawValue) {
                        return this.convertTimezone(rawValue);
                    }
                    
                    return rawValue;
                };
                
                // 时区转换
                this.convertTimezone = (value) => {
                    // 实现时区转换逻辑
                    return value;
                };
                
                // 增强的验证
                this.enhancedValidate = (value) => {
                    const errors = [];
                    
                    if (!value) {
                        if (this.props.required) {
                            errors.push('This field is required');
                        }
                        return errors;
                    }
                    
                    // 日期范围验证
                    if (this.props.minDate) {
                        const minDate = this.parseLimitDate(this.props.minDate);
                        if (value < minDate) {
                            errors.push(`Date must be after ${minDate.toLocaleDateString()}`);
                        }
                    }
                    
                    if (this.props.maxDate) {
                        const maxDate = this.parseLimitDate(this.props.maxDate);
                        if (value > maxDate) {
                            errors.push(`Date must be before ${maxDate.toLocaleDateString()}`);
                        }
                    }
                    
                    // 未来日期警告
                    if (this.props.warnFuture && value > new Date()) {
                        errors.push('Future date selected');
                    }
                    
                    // 业务日验证
                    if (this.validationRules.enableBusinessDays && this.isWeekend(value)) {
                        errors.push('Business days only');
                    }
                    
                    this.enhancedState.validationErrors = errors;
                    
                    if (errors.length > 0) {
                        this.dateTimeStatistics.validationErrors++;
                    }
                    
                    return errors;
                };
                
                // 检查是否为周末
                this.isWeekend = (date) => {
                    const day = date.getDay();
                    return day === 0 || day === 6; // Sunday or Saturday
                };
                
                // 格式化显示
                this.formatDisplay = (value, formatType = 'medium') => {
                    if (!value) return '';
                    
                    const format = this.formatOptions.get(formatType);
                    if (!format) return value.toString();
                    
                    // 根据字段类型选择格式
                    if (this.field.type === 'date') {
                        return this.formatDate(value, format.date);
                    } else {
                        return this.formatDateTime(value, format.date, format.time);
                    }
                };
                
                // 格式化日期
                this.formatDate = (date, format) => {
                    // 实现日期格式化逻辑
                    return date.toLocaleDateString();
                };
                
                // 格式化日期时间
                this.formatDateTime = (datetime, dateFormat, timeFormat) => {
                    // 实现日期时间格式化逻辑
                    return datetime.toLocaleString();
                };
                
                // 获取相对时间
                this.getRelativeTime = (value) => {
                    if (!value) return null;
                    
                    const now = new Date();
                    const diff = value.getTime() - now.getTime();
                    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
                    
                    if (days === 0) return 'Today';
                    if (days === 1) return 'Tomorrow';
                    if (days === -1) return 'Yesterday';
                    if (days > 0) return `In ${days} days`;
                    return `${Math.abs(days)} days ago`;
                };
                
                // 快速选择
                this.quickSelect = (optionValue) => {
                    const value = typeof optionValue === 'function' ? optionValue() : optionValue;
                    
                    this.state.value = value;
                    this.enhancedState.quickSelectUsed = true;
                    this.dateTimeStatistics.quickSelections++;
                    
                    // 触发更新
                    this.props.record.update({ [this.props.name]: value });
                };
                
                // 设置格式类型
                this.setFormatType = (formatType) => {
                    if (this.formatOptions.has(formatType)) {
                        this.enhancedState.formatType = formatType;
                    }
                };
                
                // 设置时区
                this.setTimezone = (timezone) => {
                    this.enhancedState.timezone = timezone;
                };
                
                // 获取日期范围
                this.getDateRange = () => {
                    if (this.isRange(this.state.value)) {
                        return {
                            start: this.state.value[0],
                            end: this.state.value[1],
                            duration: this.state.value[1] - this.state.value[0]
                        };
                    }
                    return null;
                };
                
                // 设置日期范围
                this.setDateRange = (startDate, endDate) => {
                    if (this.props.startDateField && this.props.endDateField) {
                        this.state.value = [startDate, endDate];
                        this.dateTimeStatistics.rangeSelections++;
                    }
                };
                
                // 清除日期
                this.clearDate = () => {
                    this.state.value = null;
                    this.props.record.update({ [this.props.name]: null });
                };
                
                // 获取日期信息
                this.getDateInfo = () => {
                    const value = this.state.value;
                    if (!value) return null;
                    
                    return {
                        value: value,
                        formatted: this.formatDisplay(value, this.enhancedState.formatType),
                        relative: this.getRelativeTime(value),
                        timezone: this.enhancedState.timezone,
                        isRange: this.isRange(value),
                        validationErrors: this.enhancedState.validationErrors
                    };
                };
                
                // 批量设置日期
                this.batchSetDates = (fields, date) => {
                    const results = [];
                    
                    for (const field of fields) {
                        try {
                            field.state.value = date;
                            field.props.record.update({ [field.props.name]: date });
                            results.push({ field, success: true });
                        } catch (error) {
                            results.push({ field, success: false, error });
                        }
                    }
                    
                    return results;
                };
                
                // 记录选择统计
                this.recordSelectionStatistics = () => {
                    this.dateTimeStatistics.totalSelections++;
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.dateTimeConfig.enableValidation,
                    rules: this.validationRules,
                    validate: (value) => this.enhancedValidate(value)
                };
            }
            
            addFormattingFeatures() {
                // 格式化功能
                this.formattingManager = {
                    enabled: this.dateTimeConfig.enableFormatting,
                    formats: this.formatOptions,
                    format: (value, type) => this.formatDisplay(value, type)
                };
            }
            
            // 重写原始方法
            getRecordValue() {
                return this.enhancedGetRecordValue();
            }
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationConfig = {
            enabled: this.dateTimeConfig.enableValidation,
            realTimeValidation: true,
            showErrors: true
        };
    }
    
    // 设置格式化系统
    setupFormattingSystem() {
        this.formattingConfig = {
            enabled: this.dateTimeConfig.enableFormatting,
            defaultFormat: this.dateTimeConfig.defaultFormat,
            enableCustomFormats: true
        };
    }
    
    // 设置时区处理
    setupTimezoneHandling() {
        this.timezoneHandlingConfig = {
            enabled: this.dateTimeConfig.enableTimezone,
            userTimezone: this.timezoneConfig.userTimezone,
            displayTimezone: this.timezoneConfig.displayTimezone
        };
    }
    
    // 创建日期时间字段
    createDateTimeField(props) {
        return new this.EnhancedDateTimeField(props);
    }
    
    // 注册格式选项
    registerFormat(name, dateFormat, timeFormat) {
        this.formatOptions.set(name, {
            date: dateFormat,
            time: timeFormat
        });
    }
    
    // 注册快速选择选项
    registerQuickSelect(label, valueFunction) {
        this.quickSelectOptions.push({
            label: label,
            value: valueFunction
        });
    }
    
    // 批量验证日期
    batchValidateDates(fields) {
        const results = [];
        
        for (const field of fields) {
            try {
                const value = field.state.value;
                const errors = field.enhancedValidate(value);
                results.push({ field, isValid: errors.length === 0, errors });
            } catch (error) {
                results.push({ field, isValid: false, error });
            }
        }
        
        return results;
    }
    
    // 获取日期统计
    getDateTimeStatistics() {
        return {
            ...this.dateTimeStatistics,
            formatCount: this.formatOptions.size,
            quickSelectCount: this.quickSelectOptions.length
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理格式和选项
        this.formatOptions.clear();
        this.quickSelectOptions = [];
        
        // 重置统计
        this.dateTimeStatistics = {
            totalSelections: 0,
            rangeSelections: 0,
            quickSelections: 0,
            validationErrors: 0,
            averageSelectionTime: 0
        };
    }
}

// 使用示例
const dateTimeManager = new DateTimeFieldManager();

// 创建日期时间字段
const dateTimeField = dateTimeManager.createDateTimeField({
    name: 'created_at',
    record: {
        data: { created_at: new Date() },
        fields: { created_at: { type: 'datetime' } }
    },
    showSeconds: true,
    showTime: true,
    required: true
});

// 注册自定义格式
dateTimeManager.registerFormat('custom', 'DD/MM/YYYY', 'HH:mm');

// 获取统计信息
const stats = dateTimeManager.getDateTimeStatistics();
console.log('DateTime field statistics:', stats);
```

## 技术特点

### 1. 功能完整
- **日期选择器**: 集成完整的日期时间选择器
- **范围支持**: 支持单日期和日期范围选择
- **时间控制**: 灵活的时间显示和精度控制
- **验证机制**: 完善的日期验证机制

### 2. 配置灵活
- **精度控制**: 支持最小和最大精度设置
- **限制设置**: 支持最大最小日期限制
- **格式配置**: 支持多种显示格式
- **时间选项**: 可配置的时间显示选项

### 3. 用户体验
- **直观选择**: 直观的日期时间选择界面
- **即时验证**: 即时的输入验证反馈
- **格式化显示**: 友好的格式化显示
- **范围选择**: 便捷的日期范围选择

### 4. 国际化支持
- **本地化**: 完整的日期本地化支持
- **时区处理**: 智能的时区处理
- **格式适配**: 适配不同地区的日期格式
- **翻译支持**: 完整的翻译支持

## 设计模式

### 1. 组件模式 (Component Pattern)
- **UI组件**: 封装日期时间选择UI
- **功能组件**: 集成选择器组件
- **状态管理**: 管理日期时间状态

### 2. 钩子模式 (Hook Pattern)
- **选择器钩子**: 使用日期时间选择器钩子
- **生命周期**: 管理组件生命周期
- **状态钩子**: 管理状态变化

### 3. 策略模式 (Strategy Pattern)
- **格式策略**: 不同的日期格式策略
- **验证策略**: 不同的验证策略
- **显示策略**: 不同的显示策略

### 4. 观察者模式 (Observer Pattern)
- **值观察**: 观察日期值变化
- **状态观察**: 观察组件状态变化
- **验证观察**: 观察验证状态变化

## 注意事项

1. **时区处理**: 正确处理时区转换和显示
2. **格式兼容**: 确保日期格式的兼容性
3. **性能考虑**: 避免频繁的日期计算
4. **用户体验**: 提供清晰的日期选择体验

## 扩展建议

1. **快速选择**: 添加快速日期选择选项
2. **日历视图**: 集成日历视图功能
3. **相对时间**: 支持相对时间显示
4. **批量操作**: 支持批量日期操作
5. **自定义验证**: 支持自定义验证规则

该日期时间字段为Odoo Web客户端提供了完整的日期时间处理功能，通过丰富的配置选项和强大的功能支持确保了优秀的用户体验和广泛的适用性。

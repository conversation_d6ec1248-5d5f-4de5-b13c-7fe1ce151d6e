# CharField - 字符字段

## 概述

`char_field.js` 是 Odoo Web 客户端的字符字段组件，负责处理文本输入和编辑。该模块包含147行代码，是一个功能丰富的文本输入组件，专门用于处理字符串类型的字段，具备动态占位符、翻译支持、密码模式、自动完成、长度限制等特性，是表单中最常用的输入组件之一。

## 文件信息
- **路径**: `/web/static/src/views/fields/char/char_field.js`
- **行数**: 147
- **模块**: `@web/views/fields/char/char_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'                    // 翻译服务
'@web/core/registry'                            // 注册表
'@web/core/utils/strings'                       // 字符串工具
'@web/views/fields/dynamic_placeholder_hook'    // 动态占位符钩子
'@web/views/fields/formatters'                  // 字段格式化器
'@web/views/fields/input_field_hook'            // 输入字段钩子
'@web/views/fields/standard_field_props'        // 标准字段属性
'@web/views/fields/translation_button'          // 翻译按钮
'@odoo/owl'                                     // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const CharField = class CharField extends Component {
    static template = "web.CharField";
    static components = {
        TranslationButton,
    };
    static props = {
        ...standardFieldProps,
        autocomplete: { type: String, optional: true },
        isPassword: { type: Boolean, optional: true },
        placeholder: { type: String, optional: true },
        dynamicPlaceholder: { type: Boolean, optional: true },
        dynamicPlaceholderModelReferenceField: { type: String, optional: true },
        placeholderField: { type: String, optional: true },
    };
    static defaultProps = { dynamicPlaceholder: false };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **翻译组件**: 集成TranslationButton组件
- **自动完成**: 支持autocomplete属性
- **密码模式**: 支持isPassword密码显示
- **占位符**: 支持静态和动态占位符
- **字段引用**: 支持占位符字段引用

### 2. 组件初始化

```javascript
setup() {
    this.input = useRef("input");
    if (this.props.dynamicPlaceholder) {
        this.dynamicPlaceholder = useDynamicPlaceholder(this.input);
        useExternalListener(document, "keydown", this.dynamicPlaceholder.onKeydown);
        useEffect(() =>
            this.dynamicPlaceholder.updateModel(
                this.props.dynamicPlaceholderModelReferenceField
            )
        );
    }
    useInputField({
        getValue: () => this.props.record.data[this.props.name] || "",
        parse: (v) => this.parse(v),
    });

    this.selectionStart = this.props.record.data[this.props.name]?.length || 0;
}
```

**初始化功能**:
- **输入引用**: 创建输入元素引用
- **动态占位符**: 条件性启用动态占位符功能
- **键盘监听**: 监听全局键盘事件
- **输入钩子**: 使用输入字段钩子管理状态
- **光标位置**: 初始化光标位置

### 3. 属性计算

```javascript
get shouldTrim() {
    return this.props.record.fields[this.props.name].trim && !this.props.isPassword;
}
get maxLength() {
    return this.props.record.fields[this.props.name].size;
}
get isTranslatable() {
    return this.props.record.fields[this.props.name].translate;
}
get formattedValue() {
    return formatChar(this.props.record.data[this.props.name], {
        isPassword: this.props.isPassword,
    });
}
get hasDynamicPlaceholder() {
    return this.props.dynamicPlaceholder && !this.props.readonly;
}
get placeholder() {
    return this.props.record.data[this.props.placeholderField] || this.props.placeholder;
}
```

**属性功能**:
- **修剪控制**: 根据字段配置决定是否修剪空格
- **长度限制**: 获取字段的最大长度限制
- **翻译检查**: 检查字段是否可翻译
- **值格式化**: 格式化显示值（支持密码模式）
- **占位符状态**: 检查是否启用动态占位符
- **占位符文本**: 获取占位符文本

### 4. 值解析

```javascript
parse(value) {
    if (this.shouldTrim) {
        return value.trim();
    }
    return value;
}
```

**解析功能**:
- **条件修剪**: 根据配置决定是否修剪空格
- **值处理**: 处理用户输入的值
- **密码保护**: 密码字段不进行修剪
- **原值返回**: 不需要修剪时返回原值

### 5. 事件处理

```javascript
onBlur() {
    this.selectionStart = this.input.el.selectionStart;
}

async onDynamicPlaceholderOpen() {
    await this.dynamicPlaceholder.open({
        validateCallback: this.onDynamicPlaceholderValidate.bind(this),
    });
}

async onDynamicPlaceholderValidate(chain, defaultValue) {
    if (chain) {
        this.input.el.focus();
        const dynamicPlaceholder = ` {{object.${chain}${
            defaultValue?.length ? ` ||| ${defaultValue}` : ""
        }}}`;
        this.input.el.setRangeText(
            dynamicPlaceholder,
            this.selectionStart,
            this.selectionStart,
            "end"
        );
        // trigger events to make the field dirty
        this.input.el.dispatchEvent(new InputEvent("input"));
        this.input.el.dispatchEvent(new KeyboardEvent("keydown"));
        this.input.el.focus();
    }
}
```

**事件功能**:
- **失焦处理**: 记录光标位置
- **占位符打开**: 打开动态占位符选择器
- **占位符验证**: 验证并插入动态占位符
- **文本插入**: 在光标位置插入占位符文本
- **事件触发**: 触发输入事件标记字段为脏数据

### 6. 字段注册

```javascript
const charField = {
    component: CharField,
    displayName: _t("Text"),
    supportedTypes: ["char"],
    supportedOptions: [
        {
            label: _t("Dynamic placeholder"),
            name: "dynamic_placeholder",
            type: "boolean",
            help: _t("Enable this option to allow the input to display a dynamic placeholder."),
        },
        {
            label: _t("Model reference field"),
            name: "dynamic_placeholder_model_reference_field",
            type: "field",
            availableTypes: ["char"],
        },
        {
            label: _t("Placeholder field"),
            name: "placeholder_field",
            type: "field",
            availableTypes: ["char"],
        },
    ],
    extractProps: ({ attrs, options }) => ({
        isPassword: exprToBoolean(attrs.password),
        dynamicPlaceholder: options.dynamic_placeholder || false,
        dynamicPlaceholderModelReferenceField:
            options.dynamic_placeholder_model_reference_field || "",
        autocomplete: attrs.autocomplete,
        placeholder: attrs.placeholder,
        placeholderField: options.placeholder_field,
    }),
};

registry.category("fields").add("char", charField);
```

**注册功能**:
- **组件注册**: 注册字符字段组件
- **类型支持**: 仅支持char类型
- **选项配置**: 支持动态占位符等选项
- **属性提取**: 提取密码、占位符等属性

## 使用场景

### 1. 字符字段管理器

```javascript
// 字符字段管理器
class CharFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置字符字段配置
        this.charConfig = {
            enableAutoComplete: true,
            enableSpellCheck: true,
            enableValidation: true,
            enableFormatting: true,
            maxLength: 255,
            enableTrim: true,
            enablePlaceholder: true,
            enableDynamicPlaceholder: true,
            enableTranslation: true
        };
        
        // 设置验证规则
        this.validationRules = new Map([
            ['email', /^[^\s@]+@[^\s@]+\.[^\s@]+$/],
            ['phone', /^[\+]?[1-9][\d]{0,15}$/],
            ['url', /^https?:\/\/.+/],
            ['alphanumeric', /^[a-zA-Z0-9]+$/],
            ['alpha', /^[a-zA-Z]+$/],
            ['numeric', /^[0-9]+$/]
        ]);
        
        // 设置格式化器
        this.formatters = new Map([
            ['uppercase', (value) => value.toUpperCase()],
            ['lowercase', (value) => value.toLowerCase()],
            ['capitalize', (value) => value.charAt(0).toUpperCase() + value.slice(1).toLowerCase()],
            ['title', (value) => value.replace(/\w\S*/g, (txt) => 
                txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase())]
        ]);
        
        // 设置字符统计
        this.charStatistics = {
            totalFields: 0,
            totalCharacters: 0,
            averageLength: 0,
            validationErrors: 0,
            translationCount: 0
        };
        
        this.initializeCharSystem();
    }
    
    // 初始化字符系统
    initializeCharSystem() {
        // 创建增强的字符字段
        this.createEnhancedCharField();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置格式化系统
        this.setupFormattingSystem();
        
        // 设置自动完成
        this.setupAutoComplete();
    }
    
    // 创建增强的字符字段
    createEnhancedCharField() {
        const originalField = CharField;
        
        this.EnhancedCharField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
                
                // 添加格式化功能
                this.addFormattingFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    validationErrors: [],
                    isValidating: false,
                    autoCompleteItems: [],
                    formatType: null,
                    validationType: null,
                    characterCount: 0,
                    wordCount: 0
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的解析功能
                this.enhancedParse = (value) => {
                    let parsedValue = value;
                    
                    // 应用格式化
                    if (this.enhancedState.formatType) {
                        const formatter = this.formatters.get(this.enhancedState.formatType);
                        if (formatter) {
                            parsedValue = formatter(parsedValue);
                        }
                    }
                    
                    // 执行原始解析
                    parsedValue = this.parse(parsedValue);
                    
                    // 更新统计
                    this.updateCharacterStatistics(parsedValue);
                    
                    return parsedValue;
                };
                
                // 验证值
                this.validateValue = (value) => {
                    const errors = [];
                    
                    // 长度验证
                    if (this.maxLength && value.length > this.maxLength) {
                        errors.push(`Maximum length is ${this.maxLength} characters`);
                    }
                    
                    // 类型验证
                    if (this.enhancedState.validationType) {
                        const rule = this.validationRules.get(this.enhancedState.validationType);
                        if (rule && !rule.test(value)) {
                            errors.push(`Invalid ${this.enhancedState.validationType} format`);
                        }
                    }
                    
                    // 自定义验证
                    const customErrors = this.runCustomValidation(value);
                    errors.push(...customErrors);
                    
                    this.enhancedState.validationErrors = errors;
                    
                    if (errors.length > 0) {
                        this.charStatistics.validationErrors++;
                    }
                    
                    return errors.length === 0;
                };
                
                // 运行自定义验证
                this.runCustomValidation = (value) => {
                    const errors = [];
                    
                    // 实现自定义验证逻辑
                    if (this.customValidators) {
                        for (const validator of this.customValidators) {
                            const result = validator(value);
                            if (result !== true) {
                                errors.push(typeof result === 'string' ? result : 'Validation failed');
                            }
                        }
                    }
                    
                    return errors;
                };
                
                // 获取自动完成建议
                this.getAutoCompleteSuggestions = async (query) => {
                    if (!this.charConfig.enableAutoComplete || query.length < 2) {
                        return [];
                    }
                    
                    try {
                        // 实现自动完成逻辑
                        const suggestions = await this.fetchAutoCompleteSuggestions(query);
                        this.enhancedState.autoCompleteItems = suggestions;
                        return suggestions;
                    } catch (error) {
                        console.error('Auto-complete error:', error);
                        return [];
                    }
                };
                
                // 获取自动完成建议
                this.fetchAutoCompleteSuggestions = async (query) => {
                    // 实现建议获取逻辑
                    const mockSuggestions = [
                        `${query}@example.com`,
                        `${query}@gmail.com`,
                        `${query}@outlook.com`
                    ];
                    
                    return mockSuggestions.filter(item => 
                        item.toLowerCase().includes(query.toLowerCase())
                    );
                };
                
                // 设置验证类型
                this.setValidationType = (type) => {
                    if (this.validationRules.has(type)) {
                        this.enhancedState.validationType = type;
                    }
                };
                
                // 设置格式化类型
                this.setFormatType = (type) => {
                    if (this.formatters.has(type)) {
                        this.enhancedState.formatType = type;
                    }
                };
                
                // 添加自定义验证器
                this.addCustomValidator = (validator) => {
                    if (!this.customValidators) {
                        this.customValidators = [];
                    }
                    this.customValidators.push(validator);
                };
                
                // 更新字符统计
                this.updateCharacterStatistics = (value) => {
                    this.enhancedState.characterCount = value.length;
                    this.enhancedState.wordCount = value.trim().split(/\s+/).filter(word => word.length > 0).length;
                    
                    this.charStatistics.totalCharacters += value.length;
                    this.charStatistics.averageLength = 
                        this.charStatistics.totalCharacters / Math.max(this.charStatistics.totalFields, 1);
                };
                
                // 获取字符统计
                this.getCharacterStatistics = () => {
                    return {
                        characters: this.enhancedState.characterCount,
                        words: this.enhancedState.wordCount,
                        maxLength: this.maxLength,
                        remaining: this.maxLength ? this.maxLength - this.enhancedState.characterCount : null
                    };
                };
                
                // 清理文本
                this.cleanText = (text) => {
                    // 移除多余空格
                    text = text.replace(/\s+/g, ' ');
                    
                    // 移除特殊字符（可选）
                    if (this.charConfig.removeSpecialChars) {
                        text = text.replace(/[^\w\s]/gi, '');
                    }
                    
                    return text.trim();
                };
                
                // 格式化文本
                this.formatText = (text, formatType) => {
                    const formatter = this.formatters.get(formatType);
                    return formatter ? formatter(text) : text;
                };
                
                // 检查拼写
                this.checkSpelling = async (text) => {
                    if (!this.charConfig.enableSpellCheck) {
                        return [];
                    }
                    
                    // 实现拼写检查逻辑
                    return [];
                };
                
                // 获取建议修正
                this.getSuggestions = async (word) => {
                    // 实现建议修正逻辑
                    return [];
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.charConfig.enableValidation,
                    rules: this.validationRules,
                    validate: (value) => this.validateValue(value)
                };
            }
            
            addFormattingFeatures() {
                // 格式化功能
                this.formattingManager = {
                    enabled: this.charConfig.enableFormatting,
                    formatters: this.formatters,
                    format: (text, type) => this.formatText(text, type)
                };
            }
            
            // 重写原始方法
            parse(value) {
                return this.enhancedParse(value);
            }
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationConfig = {
            enabled: this.charConfig.enableValidation,
            realTimeValidation: true,
            showErrors: true
        };
    }
    
    // 设置格式化系统
    setupFormattingSystem() {
        this.formattingConfig = {
            enabled: this.charConfig.enableFormatting,
            autoFormat: false,
            preserveCase: false
        };
    }
    
    // 设置自动完成
    setupAutoComplete() {
        this.autoCompleteConfig = {
            enabled: this.charConfig.enableAutoComplete,
            minLength: 2,
            maxSuggestions: 10,
            debounceTime: 300
        };
    }
    
    // 创建字符字段
    createCharField(props) {
        return new this.EnhancedCharField(props);
    }
    
    // 注册验证规则
    registerValidationRule(name, pattern) {
        this.validationRules.set(name, pattern);
    }
    
    // 注册格式化器
    registerFormatter(name, formatter) {
        this.formatters.set(name, formatter);
    }
    
    // 批量验证
    batchValidate(fields) {
        const results = [];
        
        for (const field of fields) {
            try {
                const value = field.props.record.data[field.props.name] || '';
                const isValid = field.validateValue(value);
                results.push({ field, isValid, errors: field.enhancedState.validationErrors });
            } catch (error) {
                results.push({ field, isValid: false, error });
            }
        }
        
        return results;
    }
    
    // 批量格式化
    batchFormat(fields, formatType) {
        const results = [];
        
        for (const field of fields) {
            try {
                field.setFormatType(formatType);
                results.push({ field, success: true });
            } catch (error) {
                results.push({ field, success: false, error });
            }
        }
        
        return results;
    }
    
    // 获取字符统计
    getCharStatistics() {
        return {
            ...this.charStatistics,
            validationRuleCount: this.validationRules.size,
            formatterCount: this.formatters.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理规则和格式化器
        this.validationRules.clear();
        this.formatters.clear();
        
        // 重置统计
        this.charStatistics = {
            totalFields: 0,
            totalCharacters: 0,
            averageLength: 0,
            validationErrors: 0,
            translationCount: 0
        };
    }
}

// 使用示例
const charManager = new CharFieldManager();

// 创建字符字段
const charField = charManager.createCharField({
    name: 'email',
    record: {
        data: { email: '<EMAIL>' },
        fields: { email: { type: 'char', size: 100, trim: true } }
    },
    placeholder: 'Enter email address',
    autocomplete: 'email'
});

// 设置验证和格式化
charField.setValidationType('email');
charField.setFormatType('lowercase');

// 注册自定义验证规则
charManager.registerValidationRule('custom', /^custom_.+/);

// 获取统计信息
const stats = charManager.getCharStatistics();
console.log('Character field statistics:', stats);
```

## 技术特点

### 1. 功能丰富
- **动态占位符**: 支持动态占位符功能
- **翻译支持**: 集成翻译按钮组件
- **密码模式**: 支持密码输入模式
- **自动完成**: 支持自动完成功能
- **长度限制**: 支持字段长度限制

### 2. 输入处理
- **值解析**: 智能的值解析和处理
- **修剪控制**: 可配置的空格修剪
- **格式化**: 支持值格式化显示
- **验证**: 内置验证机制

### 3. 用户体验
- **光标管理**: 智能的光标位置管理
- **事件处理**: 完善的事件处理机制
- **占位符**: 灵活的占位符配置
- **即时反馈**: 即时的输入反馈

### 4. 扩展性
- **钩子集成**: 集成多种功能钩子
- **组件化**: 高度组件化的设计
- **配置灵活**: 灵活的配置选项
- **可定制**: 支持自定义扩展

## 设计模式

### 1. 组件模式 (Component Pattern)
- **UI组件**: 封装文本输入UI
- **功能组件**: 集成翻译等功能组件
- **状态管理**: 管理输入状态

### 2. 钩子模式 (Hook Pattern)
- **输入钩子**: 使用输入字段钩子
- **占位符钩子**: 使用动态占位符钩子
- **生命周期**: 管理组件生命周期

### 3. 策略模式 (Strategy Pattern)
- **解析策略**: 不同的值解析策略
- **格式化策略**: 不同的格式化策略
- **验证策略**: 不同的验证策略

### 4. 观察者模式 (Observer Pattern)
- **输入观察**: 观察用户输入变化
- **状态观察**: 观察字段状态变化
- **事件观察**: 观察键盘等事件

## 注意事项

1. **性能考虑**: 避免频繁的验证和格式化
2. **用户体验**: 提供流畅的输入体验
3. **数据安全**: 密码字段的安全处理
4. **兼容性**: 确保在不同浏览器中的兼容性

## 扩展建议

1. **智能提示**: 添加智能输入提示
2. **语法高亮**: 支持特定格式的语法高亮
3. **模板功能**: 添加输入模板功能
4. **历史记录**: 添加输入历史记录
5. **批量操作**: 支持批量文本操作

该字符字段为Odoo Web客户端提供了功能完整的文本输入功能，通过丰富的特性和灵活的配置确保了优秀的用户体验和强大的功能支持。

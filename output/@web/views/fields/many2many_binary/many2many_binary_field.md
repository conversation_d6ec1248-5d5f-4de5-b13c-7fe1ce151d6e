# Many2ManyBinaryField - 多对多二进制字段

## 概述

`many2many_binary_field.js` 是 Odoo Web 客户端的多对多二进制字段组件，负责处理多个文件的上传、显示和管理。该模块包含101行代码，是一个专门的文件管理组件，专门用于处理many2many类型的二进制文件字段，具备文件上传、文件列表、文件删除、扩展名过滤等特性，是多文件管理的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/many2many_binary/many2many_binary_field.js`
- **行数**: 101
- **模块**: `@web/views/fields/many2many_binary/many2many_binary_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/core/utils/hooks'                 // 工具钩子
'@web/views/fields/standard_field_props' // 标准字段属性
'@web/core/file_input/file_input'       // 文件输入组件
'@web/views/fields/relational_utils'    // 关系字段工具
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const Many2ManyBinaryField = class Many2ManyBinaryField extends Component {
    static template = "web.Many2ManyBinaryField";
    static components = {
        FileInput,
    };
    static props = {
        ...standardFieldProps,
        acceptedFileExtensions: { type: String, optional: true },
        className: { type: String, optional: true },
        numberOfFiles: { type: Number, optional: true },
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **文件输入**: 集成FileInput组件
- **扩展名过滤**: 支持acceptedFileExtensions配置
- **文件数量**: 支持numberOfFiles限制
- **CSS类**: 支持className自定义样式

### 2. 组件初始化

```javascript
setup() {
    this.orm = useService("orm");
    this.notification = useService("notification");
    this.operations = useX2ManyCrud(() => this.props.record.data[this.props.name], true);
}
```

**初始化功能**:
- **ORM服务**: 使用ORM服务进行数据操作
- **通知服务**: 使用通知服务显示消息
- **CRUD操作**: 使用X2ManyCrud管理关系数据
- **关系管理**: 管理多对多关系的增删改查

### 3. 上传文本获取

```javascript
get uploadText() {
    return this.props.record.fields[this.props.name].string;
}
```

**文本功能**:
- **字段标签**: 获取字段的显示标签
- **上传提示**: 用作文件上传的提示文本
- **国际化**: 支持多语言显示
- **动态获取**: 动态获取字段配置
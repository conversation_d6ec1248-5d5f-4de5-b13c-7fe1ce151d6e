# StatInfoField - 统计信息字段

## 概述

`stat_info_field.js` 是 Odoo Web 客户端的统计信息字段组件，负责显示格式化的统计数据和信息。该模块包含72行代码，是一个功能简洁的统计显示组件，专门用于在表单视图中显示统计信息，具备数值格式化、标签字段、数字精度、字符串配置等特性，是数据统计展示的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/stat_info/stat_info_field.js`
- **行数**: 72
- **模块**: `@web/views/fields/stat_info/stat_info_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/core/utils/strings'               // 字符串工具
'@web/views/fields/standard_field_props' // 标准字段属性
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const StatInfoField = class StatInfoField extends Component {
    static template = "web.StatInfoField";
    static props = {
        ...standardFieldProps,
        labelField: { type: String, optional: true },
        noLabel: { type: Boolean, optional: true },
        digits: { type: Array, optional: true },
        string: { type: String, optional: true },
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **标签字段**: 支持labelField配置动态标签字段
- **无标签**: 支持noLabel配置隐藏标签
- **数字精度**: 支持digits配置数字精度
- **字符串**: 支持string配置静态标签文本
- **专用模板**: 使用StatInfoField专用模板

### 2. 数值格式化

```javascript
get formattedValue() {
    const field = this.props.record.fields[this.props.name];
    const formatter = formatters.get(field.type);
    return formatter(this.props.record.data[this.props.name] || 0, {
        digits: this.props.digits,
        field,
    });
}
```

**格式化功能**:
- **字段类型**: 获取字段类型信息
- **格式化器**: 使用注册表中的格式化器
- **默认值**: 空值时默认为0
- **精度配置**: 使用digits配置数字精度
- **字段配置**: 传递字段配置给格式化器

### 3. 标签处理

```javascript
get label() {
    return this.props.labelField
        ? this.props.record.data[this.props.labelField]
        : this.props.string;
}
```

**标签功能**:
- **动态标签**: 优先使用labelField字段的值
- **静态标签**: 使用string配置的静态文本
- **标签选择**: 智能选择标签来源
- **空值处理**: 处理标签字段为空的情况

### 4. 字段注册

```javascript
const statInfoField = {
    component: StatInfoField,
    displayName: _t("Stat Info"),
    supportedOptions: [
        {
            label: _t("Label field"),
            name: "label_field",
            type: "field",
            availableTypes: ["char"],
        },
        {
            label: _t("No label"),
            name: "no_label",
            type: "boolean",
        },
    ],
    supportedTypes: ["integer", "float", "monetary"],
    extractProps: ({ attrs, options }) => ({
        labelField: options.label_field,
        noLabel: options.no_label,
        digits: attrs.digits,
        string: attrs.string,
    }),
};

registry.category("fields").add("statinfo", statInfoField);
```

**注册功能**:
- **组件注册**: 注册统计信息字段组件
- **显示名称**: 设置为"Stat Info"
- **支持选项**: 支持标签字段和无标签选项
- **类型支持**: 支持integer、float、monetary类型
- **属性提取**: 提取标签字段、无标签、精度、字符串等属性

## 使用场景

### 1. 统计信息字段管理器

```javascript
// 统计信息字段管理器
class StatInfoFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置统计信息字段配置
        this.statInfoConfig = {
            enableFormatting: true,
            enableDynamicLabels: true,
            enableCustomFormatters: false,
            enableValueValidation: true,
            enableTrendIndicators: false,
            enableComparison: false,
            enableExport: false,
            enableTooltips: true
        };
        
        // 设置支持的数据类型
        this.supportedTypes = new Map([
            ['integer', {
                name: 'Integer',
                formatter: 'integer',
                defaultDigits: [16, 0],
                validation: ['range', 'type']
            }],
            ['float', {
                name: 'Float',
                formatter: 'float',
                defaultDigits: [16, 2],
                validation: ['range', 'type', 'precision']
            }],
            ['monetary', {
                name: 'Monetary',
                formatter: 'monetary',
                defaultDigits: [16, 2],
                validation: ['range', 'type', 'currency']
            }]
        ]);
        
        // 设置格式化选项
        this.formattingOptions = {
            enableThousandsSeparator: true,
            enableCurrencySymbol: true,
            enablePercentage: false,
            enableScientificNotation: false,
            decimalPlaces: 2,
            currencyPosition: 'before',
            thousandsSeparator: ',',
            decimalSeparator: '.'
        };
        
        // 设置验证规则
        this.validationRules = {
            enableRangeValidation: true,
            enableTypeValidation: true,
            enablePrecisionValidation: true,
            minValue: Number.MIN_SAFE_INTEGER,
            maxValue: Number.MAX_SAFE_INTEGER,
            maxDecimalPlaces: 10
        };
        
        // 设置统计信息统计
        this.statInfoStatistics = {
            totalStatInfoFields: 0,
            totalValues: 0,
            valuesByType: new Map(),
            averageValue: 0,
            maxValue: Number.MIN_SAFE_INTEGER,
            minValue: Number.MAX_SAFE_INTEGER,
            formattingErrors: 0,
            validationErrors: 0
        };
        
        this.initializeStatInfoSystem();
    }
    
    // 初始化统计信息系统
    initializeStatInfoSystem() {
        // 创建增强的统计信息字段
        this.createEnhancedStatInfoField();
        
        // 设置格式化系统
        this.setupFormattingSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置标签系统
        this.setupLabelSystem();
    }
    
    // 创建增强的统计信息字段
    createEnhancedStatInfoField() {
        const originalField = StatInfoField;
        
        this.EnhancedStatInfoField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加格式化功能
                this.addFormattingFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isValid: true,
                    validationErrors: [],
                    formattingErrors: [],
                    lastValue: null,
                    lastFormattedValue: null,
                    valueHistory: [],
                    trend: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的数值格式化
                this.enhancedFormattedValue = () => {
                    try {
                        const field = this.props.record.fields[this.props.name];
                        const value = this.props.record.data[this.props.name] || 0;
                        
                        // 验证值
                        this.validateValue(value, field);
                        
                        // 获取格式化器
                        const formatter = formatters.get(field.type);
                        if (!formatter) {
                            throw new Error(`No formatter found for type: ${field.type}`);
                        }
                        
                        // 格式化选项
                        const formatOptions = {
                            digits: this.props.digits || this.getDefaultDigits(field.type),
                            field: field,
                            ...this.getCustomFormatOptions()
                        };
                        
                        // 执行格式化
                        const formattedValue = formatter(value, formatOptions);
                        
                        // 记录格式化结果
                        this.recordFormattedValue(value, formattedValue);
                        
                        return formattedValue;
                        
                    } catch (error) {
                        this.handleFormattingError(error);
                        return this.getFallbackValue();
                    }
                };
                
                // 增强的标签获取
                this.enhancedLabel = () => {
                    try {
                        let label;
                        
                        if (this.props.labelField) {
                            label = this.props.record.data[this.props.labelField];
                            if (!label) {
                                console.warn(`Label field '${this.props.labelField}' is empty`);
                            }
                        }
                        
                        if (!label) {
                            label = this.props.string || this.getDefaultLabel();
                        }
                        
                        return label || '';
                        
                    } catch (error) {
                        console.error('Error getting label:', error);
                        return this.props.string || '';
                    }
                };
                
                // 验证值
                this.validateValue = (value, field) => {
                    const errors = [];
                    
                    // 类型验证
                    if (this.validationRules.enableTypeValidation) {
                        if (!this.isValidType(value, field.type)) {
                            errors.push(`Invalid type for ${field.type}`);
                        }
                    }
                    
                    // 范围验证
                    if (this.validationRules.enableRangeValidation) {
                        if (value < this.validationRules.minValue || value > this.validationRules.maxValue) {
                            errors.push(`Value out of range: ${value}`);
                        }
                    }
                    
                    // 精度验证
                    if (this.validationRules.enablePrecisionValidation && field.type === 'float') {
                        const decimalPlaces = this.getDecimalPlaces(value);
                        if (decimalPlaces > this.validationRules.maxDecimalPlaces) {
                            errors.push(`Too many decimal places: ${decimalPlaces}`);
                        }
                    }
                    
                    this.enhancedState.validationErrors = errors;
                    this.enhancedState.isValid = errors.length === 0;
                    
                    if (errors.length > 0) {
                        this.statInfoStatistics.validationErrors++;
                        throw new Error(errors.join(', '));
                    }
                };
                
                // 检查类型有效性
                this.isValidType = (value, type) => {
                    switch (type) {
                        case 'integer':
                            return Number.isInteger(value);
                        case 'float':
                        case 'monetary':
                            return typeof value === 'number' && !isNaN(value);
                        default:
                            return true;
                    }
                };
                
                // 获取小数位数
                this.getDecimalPlaces = (value) => {
                    const str = value.toString();
                    const decimalIndex = str.indexOf('.');
                    return decimalIndex >= 0 ? str.length - decimalIndex - 1 : 0;
                };
                
                // 获取默认精度
                this.getDefaultDigits = (type) => {
                    const typeConfig = this.supportedTypes.get(type);
                    return typeConfig?.defaultDigits || [16, 2];
                };
                
                // 获取自定义格式选项
                this.getCustomFormatOptions = () => {
                    if (!this.statInfoConfig.enableCustomFormatters) {
                        return {};
                    }
                    
                    return {
                        thousandsSep: this.formattingOptions.enableThousandsSeparator ? 
                            this.formattingOptions.thousandsSeparator : false,
                        decimalPoint: this.formattingOptions.decimalSeparator,
                        currencySymbol: this.formattingOptions.enableCurrencySymbol,
                        currencyPosition: this.formattingOptions.currencyPosition
                    };
                };
                
                // 获取默认标签
                this.getDefaultLabel = () => {
                    const field = this.props.record.fields[this.props.name];
                    return field?.string || this.props.name;
                };
                
                // 获取回退值
                this.getFallbackValue = () => {
                    const value = this.props.record.data[this.props.name] || 0;
                    return String(value);
                };
                
                // 记录格式化值
                this.recordFormattedValue = (value, formattedValue) => {
                    this.enhancedState.lastValue = value;
                    this.enhancedState.lastFormattedValue = formattedValue;
                    
                    // 记录历史
                    this.enhancedState.valueHistory.unshift({
                        value: value,
                        formattedValue: formattedValue,
                        timestamp: new Date()
                    });
                    
                    // 限制历史大小
                    if (this.enhancedState.valueHistory.length > 10) {
                        this.enhancedState.valueHistory.pop();
                    }
                    
                    // 计算趋势
                    this.calculateTrend();
                    
                    // 记录统计
                    this.recordValueStatistics(value);
                };
                
                // 计算趋势
                this.calculateTrend = () => {
                    if (!this.statInfoConfig.enableTrendIndicators) {
                        return;
                    }
                    
                    const history = this.enhancedState.valueHistory;
                    if (history.length < 2) {
                        this.enhancedState.trend = null;
                        return;
                    }
                    
                    const current = history[0].value;
                    const previous = history[1].value;
                    
                    if (current > previous) {
                        this.enhancedState.trend = 'up';
                    } else if (current < previous) {
                        this.enhancedState.trend = 'down';
                    } else {
                        this.enhancedState.trend = 'stable';
                    }
                };
                
                // 处理格式化错误
                this.handleFormattingError = (error) => {
                    console.error('Formatting error:', error);
                    this.enhancedState.formattingErrors.push({
                        error: error.message,
                        timestamp: new Date()
                    });
                    this.statInfoStatistics.formattingErrors++;
                };
                
                // 获取统计信息
                this.getStatInfo = () => {
                    return {
                        value: this.enhancedState.lastValue,
                        formattedValue: this.enhancedState.lastFormattedValue,
                        label: this.enhancedLabel(),
                        isValid: this.enhancedState.isValid,
                        validationErrors: this.enhancedState.validationErrors,
                        formattingErrors: this.enhancedState.formattingErrors,
                        trend: this.enhancedState.trend,
                        valueHistory: this.enhancedState.valueHistory,
                        fieldType: this.props.record.fields[this.props.name]?.type
                    };
                };
                
                // 记录值统计
                this.recordValueStatistics = (value) => {
                    this.statInfoStatistics.totalValues++;
                    
                    // 记录按类型分布
                    const fieldType = this.props.record.fields[this.props.name]?.type;
                    if (fieldType) {
                        const count = this.statInfoStatistics.valuesByType.get(fieldType) || 0;
                        this.statInfoStatistics.valuesByType.set(fieldType, count + 1);
                    }
                    
                    // 更新最大最小值
                    if (value > this.statInfoStatistics.maxValue) {
                        this.statInfoStatistics.maxValue = value;
                    }
                    if (value < this.statInfoStatistics.minValue) {
                        this.statInfoStatistics.minValue = value;
                    }
                    
                    // 更新平均值
                    this.updateAverageValue();
                };
                
                // 更新平均值
                this.updateAverageValue = () => {
                    // 简化处理，实际应该累计所有值
                    if (this.statInfoStatistics.totalValues > 0) {
                        this.statInfoStatistics.averageValue = 
                            (this.statInfoStatistics.maxValue + this.statInfoStatistics.minValue) / 2;
                    }
                };
            }
            
            addFormattingFeatures() {
                // 格式化功能
                this.formattingManager = {
                    enabled: this.statInfoConfig.enableFormatting,
                    format: () => this.enhancedFormattedValue(),
                    getOptions: () => this.getCustomFormatOptions(),
                    validate: (value, field) => this.validateValue(value, field)
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.statInfoConfig.enableValueValidation,
                    validate: (value, field) => this.validateValue(value, field),
                    isValid: () => this.enhancedState.isValid,
                    getErrors: () => this.enhancedState.validationErrors
                };
            }
            
            // 重写原始方法
            get formattedValue() {
                return this.enhancedFormattedValue();
            }
            
            get label() {
                return this.enhancedLabel();
            }
        };
    }
    
    // 设置格式化系统
    setupFormattingSystem() {
        this.formattingSystemConfig = {
            enabled: this.statInfoConfig.enableFormatting,
            options: this.formattingOptions,
            types: this.supportedTypes
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.statInfoConfig.enableValueValidation,
            rules: this.validationRules
        };
    }
    
    // 设置标签系统
    setupLabelSystem() {
        this.labelSystemConfig = {
            enabled: this.statInfoConfig.enableDynamicLabels,
            supportDynamicLabels: true,
            supportStaticLabels: true
        };
    }
    
    // 创建统计信息字段
    createStatInfoField(props) {
        const field = new this.EnhancedStatInfoField(props);
        this.statInfoStatistics.totalStatInfoFields++;
        return field;
    }
    
    // 注册自定义格式化器
    registerCustomFormatter(type, formatter) {
        formatters.add(type, formatter);
    }
    
    // 获取统计信息统计
    getStatInfoStatistics() {
        return {
            ...this.statInfoStatistics,
            typeVariety: this.statInfoStatistics.valuesByType.size,
            errorRate: (this.statInfoStatistics.formattingErrors + this.statInfoStatistics.validationErrors) / 
                      Math.max(this.statInfoStatistics.totalValues, 1) * 100,
            supportedTypeCount: this.supportedTypes.size
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理支持的类型
        this.supportedTypes.clear();
        
        // 清理统计
        this.statInfoStatistics.valuesByType.clear();
        
        // 重置统计
        this.statInfoStatistics = {
            totalStatInfoFields: 0,
            totalValues: 0,
            valuesByType: new Map(),
            averageValue: 0,
            maxValue: Number.MIN_SAFE_INTEGER,
            minValue: Number.MAX_SAFE_INTEGER,
            formattingErrors: 0,
            validationErrors: 0
        };
    }
}

// 使用示例
const statInfoManager = new StatInfoFieldManager();

// 创建统计信息字段
const statInfoField = statInfoManager.createStatInfoField({
    name: 'total_amount',
    record: {
        data: { 
            total_amount: 1234.56,
            amount_label: 'Total Revenue'
        },
        fields: { 
            total_amount: { 
                type: 'monetary',
                string: 'Total Amount'
            }
        }
    },
    labelField: 'amount_label',
    digits: [16, 2]
});

// 注册自定义格式化器
statInfoManager.registerCustomFormatter('percentage', (value, options) => {
    return `${(value * 100).toFixed(options.digits?.[1] || 2)}%`;
});

// 获取统计信息
const stats = statInfoManager.getStatInfoStatistics();
console.log('Stat info field statistics:', stats);
```

## 技术特点

### 1. 格式化显示
- **类型格式化**: 根据字段类型自动格式化
- **精度控制**: 支持数字精度配置
- **格式化器**: 使用注册表中的格式化器
- **默认值**: 空值时显示默认值

### 2. 动态标签
- **字段标签**: 支持从其他字段获取标签
- **静态标签**: 支持配置静态标签文本
- **标签优先级**: 字段标签优先于静态标签
- **空值处理**: 处理标签为空的情况

### 3. 类型支持
- **数值类型**: 支持integer、float、monetary类型
- **格式化选项**: 每种类型有对应的格式化选项
- **验证规则**: 每种类型有对应的验证规则
- **扩展性**: 易于添加新的数据类型

### 4. 简洁设计
- **最小代码**: 极简的代码实现
- **专注功能**: 专注于统计信息显示
- **易于使用**: 简单的配置和使用
- **高效渲染**: 高效的渲染性能

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- **格式化策略**: 不同类型的格式化策略
- **标签策略**: 不同的标签获取策略
- **验证策略**: 不同的验证策略

### 2. 装饰器模式 (Decorator Pattern)
- **格式化装饰**: 装饰原始数值
- **标签装饰**: 装饰显示标签
- **样式装饰**: 装饰显示样式

### 3. 模板方法模式 (Template Method Pattern)
- **显示模板**: 定义统计信息显示模板
- **格式化模板**: 定义格式化处理模板
- **验证模板**: 定义验证处理模板

### 4. 观察者模式 (Observer Pattern)
- **值观察**: 观察字段值变化
- **格式观察**: 观察格式配置变化
- **标签观察**: 观察标签字段变化

## 注意事项

1. **数据类型**: 确保字段类型与格式化器匹配
2. **精度配置**: 正确配置数字精度
3. **标签字段**: 确保标签字段存在且有效
4. **性能考虑**: 避免频繁的格式化操作

## 扩展建议

1. **趋势指示**: 添加数值趋势指示器
2. **比较功能**: 支持与历史值比较
3. **图表集成**: 集成小型图表显示
4. **导出功能**: 支持统计数据导出
5. **自定义格式**: 支持更多自定义格式

该统计信息字段为Odoo Web客户端提供了简洁而强大的统计数据显示功能，通过灵活的格式化和标签配置确保了统计信息的清晰展示和良好的用户体验。

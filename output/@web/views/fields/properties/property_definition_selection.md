# PropertyDefinitionSelection - 属性定义选择组件

## 概述

`property_definition_selection.js` 是 Odoo Web 客户端的属性定义选择组件，负责管理选择类型属性的选项定义和配置。该模块包含294行代码，是一个功能完整的选择选项管理组件，专门用于定义和编辑选择类型属性的选项，具备选项添加、删除、重命名、排序、默认值设置等特性，是选择类型属性配置的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/properties/property_definition_selection.js`
- **行数**: 294
- **模块**: `@web/views/fields/properties/property_definition_selection`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/hooks'                 // 工具钩子
'@web/views/utils'                      // 视图工具
'@odoo/owl'                            // OWL框架
'@web/core/utils/sortable_owl'          // 可排序OWL工具
```

## 核心功能

### 1. 组件定义

```javascript
const PropertyDefinitionSelection = class PropertyDefinitionSelection extends Component {
    static template = "web.PropertyDefinitionSelection";
    static props = {
        default: { type: String, optional: true },
        options: {},
        readonly: { type: Boolean, optional: true },
        canChangeDefinition: { type: Boolean, optional: true },
        onOptionsChange: { type: Function, optional: true }, // we add / remove / rename an option
        onDefaultOptionChange: { type: Function, optional: true }, // we select a default value
    };
}
```

**组件特性**:
- **默认值**: 支持default配置默认选项
- **选项管理**: 支持options配置选项列表
- **权限控制**: 支持只读和定义变更权限
- **事件回调**: 支持选项变更和默认值变更回调
- **专用模板**: 使用PropertyDefinitionSelection专用模板

### 2. 组件初始化

```javascript
setup() {
    this.notification = useService("notification");

    // when we create a new option, it's added in the state
    // when we have finished to edit it (blur / enter) we propagate
    // the new value in the props
    this.state = useState({
        newOption: null,
    });

    this.propertyDefinitionSelectionRef = useRef("propertyDefinitionSelection");
    this.addButtonRef = useRef("addButton");

    useEffect(() => {
        // automatically give the focus to the new option if it is empty
        if (!this.state.newOption) {
            return;
        }
        const inputs = this.propertyDefinitionSelectionRef.el.querySelectorAll(
            ".o_field_property_selection_option input"
        );
        if (inputs && inputs.length && !inputs[this.state.newOption.index].value) {
            inputs[this.state.newOption.index].focus();
        }
    });
}
```

**初始化功能**:
- **通知服务**: 注入通知服务
- **状态管理**: 管理新选项状态
- **引用管理**: 管理组件和按钮引用
- **自动聚焦**: 新选项自动聚焦到输入框
- **DOM操作**: 查找并操作输入框元素

### 3. 排序功能

```javascript
useSortable({
    enable: () => this.canChangeDefinition,
    ref: this.propertyDefinitionSelectionRef,
    elements: ".o_field_property_selection_option",
    handle: ".o_field_property_selection_option_handle",
    cursor: "grabbing",
    onDrop: ({ element, previous, next, parent }) => {
        const optionId = element.dataset.optionId;
        const options = [...this.options];
        const optionIndex = options.findIndex((option) => option.id === optionId);
        const option = options[optionIndex];

        // remove the option from its current position
        options.splice(optionIndex, 1);

        // add the option to its new position
        let newIndex = 0;
        if (next) {
            newIndex = options.findIndex((option) => option.id === next.dataset.optionId);
        } else if (previous) {
            newIndex = options.findIndex((option) => option.id === previous.dataset.optionId) + 1;
        }
        options.splice(newIndex, 0, option);

        this.props.onOptionsChange(options);
    },
});
```

**排序功能**:
- **可排序**: 使用useSortable钩子实现拖拽排序
- **权限控制**: 根据canChangeDefinition控制是否可排序
- **拖拽句柄**: 指定拖拽句柄元素
- **位置计算**: 计算拖拽后的新位置
- **选项重排**: 重新排列选项数组并触发变更

### 4. 选项管理

```javascript
get options() {
    const options = this.props.options || [];
    if (this.state.newOption) {
        const newOptions = [...options];
        newOptions.splice(this.state.newOption.index, 0, this.state.newOption);
        return newOptions;
    }
    return options;
}

get canChangeDefinition() {
    return !this.props.readonly && this.props.canChangeDefinition;
}

_addOption() {
    if (!this.canChangeDefinition) {
        return;
    }

    this.state.newOption = {
        id: uuid(),
        value: "",
        index: this.options.length,
    };
}
```

**选项管理功能**:
- **选项获取**: 获取包含新选项的完整选项列表
- **权限检查**: 检查是否可以修改定义
- **添加选项**: 添加新的选项到列表
- **唯一ID**: 为新选项生成唯一ID

### 5. 选项编辑

```javascript
_onOptionChange(optionId, newValue) {
    if (this.state.newOption && this.state.newOption.id === optionId) {
        // we are editing the new option, update the state
        this.state.newOption.value = newValue;
        return;
    }

    // we are editing an existing option, update the props
    const options = [...this.props.options];
    const optionIndex = options.findIndex((option) => option.id === optionId);
    if (optionIndex >= 0) {
        options[optionIndex] = { ...options[optionIndex], value: newValue };
        this.props.onOptionsChange(options);
    }
}

_onOptionBlur(optionId) {
    if (!this.state.newOption || this.state.newOption.id !== optionId) {
        return;
    }

    // we finished to edit the new option, propagate it to the props
    const newOption = this.state.newOption;
    this.state.newOption = null;

    if (!newOption.value.trim()) {
        // the new option is empty, do not add it
        return;
    }

    const options = [...(this.props.options || [])];
    options.splice(newOption.index, 0, { id: newOption.id, value: newOption.value });
    this.props.onOptionsChange(options);
}
```

**编辑功能**:
- **选项变更**: 处理选项值的变更
- **状态区分**: 区分新选项和现有选项的编辑
- **失焦处理**: 处理输入框失焦事件
- **空值过滤**: 过滤空值选项

### 6. 选项删除

```javascript
_removeOption(optionId) {
    if (!this.canChangeDefinition) {
        return;
    }

    const options = [...this.props.options];
    const optionIndex = options.findIndex((option) => option.id === optionId);
    if (optionIndex >= 0) {
        const removedOption = options[optionIndex];
        options.splice(optionIndex, 1);

        // if the removed option was the default, reset the default
        if (this.props.default === removedOption.value) {
            this.props.onDefaultOptionChange("");
        }

        this.props.onOptionsChange(options);
    }
}
```

**删除功能**:
- **权限检查**: 检查删除权限
- **选项移除**: 从选项列表中移除指定选项
- **默认值处理**: 如果删除的是默认选项，重置默认值
- **变更通知**: 通知选项变更

## 使用场景

### 1. 选择选项管理器

```javascript
// 选择选项管理器
class SelectionOptionManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置选择选项配置
        this.selectionConfig = {
            enableSorting: true,
            enableDuplicateCheck: true,
            enableValidation: true,
            enableBulkOperations: false,
            enableImportExport: false,
            enableTemplates: false,
            maxOptions: 50,
            minOptions: 1
        };
        
        // 设置验证规则
        this.validationRules = {
            enableEmptyCheck: true,
            enableDuplicateCheck: true,
            enableLengthCheck: true,
            maxOptionLength: 100,
            minOptionLength: 1,
            allowSpecialCharacters: true,
            reservedWords: ['null', 'undefined', 'true', 'false']
        };
        
        // 设置选项模板
        this.optionTemplates = new Map([
            ['priority', [
                { id: 'low', value: 'Low' },
                { id: 'medium', value: 'Medium' },
                { id: 'high', value: 'High' },
                { id: 'urgent', value: 'Urgent' }
            ]],
            ['status', [
                { id: 'draft', value: 'Draft' },
                { id: 'in_progress', value: 'In Progress' },
                { id: 'done', value: 'Done' },
                { id: 'cancelled', value: 'Cancelled' }
            ]],
            ['yesno', [
                { id: 'yes', value: 'Yes' },
                { id: 'no', value: 'No' }
            ]]
        ]);
        
        // 设置选择统计
        this.selectionStatistics = {
            totalSelections: 0,
            totalOptions: 0,
            averageOptionsPerSelection: 0,
            mostUsedTemplate: null,
            validationErrors: 0,
            duplicateErrors: 0
        };
        
        this.initializeSelectionSystem();
    }
    
    // 初始化选择系统
    initializeSelectionSystem() {
        // 创建增强的选择定义组件
        this.createEnhancedPropertyDefinitionSelection();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置排序系统
        this.setupSortingSystem();
        
        // 设置模板系统
        this.setupTemplateSystem();
    }
    
    // 创建增强的选择定义组件
    createEnhancedPropertyDefinitionSelection() {
        const originalComponent = PropertyDefinitionSelection;
        
        this.EnhancedPropertyDefinitionSelection = class extends originalComponent {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
                
                // 添加模板功能
                this.addTemplateFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    ...this.state,
                    validationErrors: new Map(),
                    duplicateWarnings: new Set(),
                    isValidating: false,
                    lastValidationTime: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的选项验证
                this.validateOption = (optionValue, optionId = null) => {
                    const errors = [];
                    
                    // 空值检查
                    if (this.validationRules.enableEmptyCheck) {
                        if (!optionValue || !optionValue.trim()) {
                            errors.push('Option value cannot be empty');
                        }
                    }
                    
                    // 长度检查
                    if (this.validationRules.enableLengthCheck) {
                        if (optionValue.length < this.validationRules.minOptionLength) {
                            errors.push(`Option must be at least ${this.validationRules.minOptionLength} characters`);
                        }
                        if (optionValue.length > this.validationRules.maxOptionLength) {
                            errors.push(`Option must not exceed ${this.validationRules.maxOptionLength} characters`);
                        }
                    }
                    
                    // 重复检查
                    if (this.validationRules.enableDuplicateCheck) {
                        const duplicates = this.options.filter(option => 
                            option.value === optionValue && option.id !== optionId
                        );
                        if (duplicates.length > 0) {
                            errors.push('Duplicate option value');
                        }
                    }
                    
                    // 保留字检查
                    if (this.validationRules.reservedWords.includes(optionValue.toLowerCase())) {
                        errors.push('Reserved word cannot be used as option value');
                    }
                    
                    return errors;
                };
                
                // 增强的选项变更
                this.enhancedOnOptionChange = (optionId, newValue) => {
                    // 验证选项
                    const errors = this.validateOption(newValue, optionId);
                    
                    if (errors.length > 0) {
                        this.enhancedState.validationErrors.set(optionId, errors);
                        this.selectionStatistics.validationErrors++;
                    } else {
                        this.enhancedState.validationErrors.delete(optionId);
                    }
                    
                    // 执行原始变更
                    this._onOptionChange(optionId, newValue);
                    
                    // 记录统计
                    this.recordOptionChange();
                };
                
                // 增强的选项添加
                this.enhancedAddOption = () => {
                    // 检查最大选项数
                    if (this.options.length >= this.selectionConfig.maxOptions) {
                        this.notification.add(
                            `Maximum ${this.selectionConfig.maxOptions} options allowed`,
                            { type: 'warning' }
                        );
                        return;
                    }
                    
                    // 执行原始添加
                    this._addOption();
                    
                    // 记录统计
                    this.recordOptionAdd();
                };
                
                // 增强的选项删除
                this.enhancedRemoveOption = (optionId) => {
                    // 检查最小选项数
                    if (this.options.length <= this.selectionConfig.minOptions) {
                        this.notification.add(
                            `Minimum ${this.selectionConfig.minOptions} option required`,
                            { type: 'warning' }
                        );
                        return;
                    }
                    
                    // 清理验证错误
                    this.enhancedState.validationErrors.delete(optionId);
                    
                    // 执行原始删除
                    this._removeOption(optionId);
                    
                    // 记录统计
                    this.recordOptionRemove();
                };
                
                // 批量添加选项
                this.batchAddOptions = (optionValues) => {
                    const newOptions = [...this.options];
                    
                    for (const value of optionValues) {
                        // 验证选项
                        const errors = this.validateOption(value);
                        if (errors.length === 0) {
                            newOptions.push({
                                id: this.generateOptionId(),
                                value: value
                            });
                        }
                    }
                    
                    this.props.onOptionsChange(newOptions);
                };
                
                // 应用模板
                this.applyTemplate = (templateName) => {
                    const template = this.optionTemplates.get(templateName);
                    if (template) {
                        const newOptions = template.map(option => ({
                            ...option,
                            id: this.generateOptionId()
                        }));
                        
                        this.props.onOptionsChange(newOptions);
                        this.selectionStatistics.mostUsedTemplate = templateName;
                    }
                };
                
                // 生成选项ID
                this.generateOptionId = () => {
                    return 'option_' + Math.random().toString(36).substr(2, 9);
                };
                
                // 获取验证状态
                this.getValidationStatus = () => {
                    return {
                        hasErrors: this.enhancedState.validationErrors.size > 0,
                        errorCount: this.enhancedState.validationErrors.size,
                        errors: Array.from(this.enhancedState.validationErrors.entries()),
                        isValid: this.enhancedState.validationErrors.size === 0
                    };
                };
                
                // 获取选择信息
                this.getSelectionInfo = () => {
                    return {
                        optionCount: this.options.length,
                        hasDefault: !!this.props.default,
                        defaultOption: this.props.default,
                        validationStatus: this.getValidationStatus(),
                        canAddMore: this.options.length < this.selectionConfig.maxOptions,
                        canRemove: this.options.length > this.selectionConfig.minOptions
                    };
                };
                
                // 记录选项变更
                this.recordOptionChange = () => {
                    this.enhancedState.lastValidationTime = new Date();
                };
                
                // 记录选项添加
                this.recordOptionAdd = () => {
                    this.selectionStatistics.totalOptions++;
                    this.updateAverageOptions();
                };
                
                // 记录选项删除
                this.recordOptionRemove = () => {
                    this.selectionStatistics.totalOptions--;
                    this.updateAverageOptions();
                };
                
                // 更新平均选项数
                this.updateAverageOptions = () => {
                    if (this.selectionStatistics.totalSelections > 0) {
                        this.selectionStatistics.averageOptionsPerSelection = 
                            this.selectionStatistics.totalOptions / this.selectionStatistics.totalSelections;
                    }
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.selectionConfig.enableValidation,
                    validate: (value, id) => this.validateOption(value, id),
                    getStatus: () => this.getValidationStatus(),
                    clearErrors: () => this.enhancedState.validationErrors.clear()
                };
            }
            
            addTemplateFeatures() {
                // 模板功能
                this.templateManager = {
                    enabled: this.selectionConfig.enableTemplates,
                    apply: (templateName) => this.applyTemplate(templateName),
                    getTemplates: () => Array.from(this.optionTemplates.keys()),
                    addTemplate: (name, options) => this.optionTemplates.set(name, options)
                };
            }
            
            // 重写原始方法
            _onOptionChange(optionId, newValue) {
                return this.enhancedOnOptionChange(optionId, newValue);
            }
            
            _addOption() {
                return this.enhancedAddOption();
            }
            
            _removeOption(optionId) {
                return this.enhancedRemoveOption(optionId);
            }
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.selectionConfig.enableValidation,
            rules: this.validationRules
        };
    }
    
    // 设置排序系统
    setupSortingSystem() {
        this.sortingSystemConfig = {
            enabled: this.selectionConfig.enableSorting,
            allowCustomOrder: true,
            enableAlphabeticalSort: true
        };
    }
    
    // 设置模板系统
    setupTemplateSystem() {
        this.templateSystemConfig = {
            enabled: this.selectionConfig.enableTemplates,
            templates: this.optionTemplates
        };
    }
    
    // 创建选择定义组件
    createPropertyDefinitionSelection(props) {
        const component = new this.EnhancedPropertyDefinitionSelection(props);
        this.selectionStatistics.totalSelections++;
        return component;
    }
    
    // 注册选项模板
    registerOptionTemplate(name, options) {
        this.optionTemplates.set(name, options);
    }
    
    // 获取选择统计
    getSelectionStatistics() {
        return {
            ...this.selectionStatistics,
            templateCount: this.optionTemplates.size,
            errorRate: (this.selectionStatistics.validationErrors / Math.max(this.selectionStatistics.totalOptions, 1)) * 100,
            duplicateRate: (this.selectionStatistics.duplicateErrors / Math.max(this.selectionStatistics.totalOptions, 1)) * 100
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理模板
        this.optionTemplates.clear();
        
        // 重置统计
        this.selectionStatistics = {
            totalSelections: 0,
            totalOptions: 0,
            averageOptionsPerSelection: 0,
            mostUsedTemplate: null,
            validationErrors: 0,
            duplicateErrors: 0
        };
    }
}

// 使用示例
const selectionManager = new SelectionOptionManager();

// 创建选择定义组件
const selectionComponent = selectionManager.createPropertyDefinitionSelection({
    options: [
        { id: 'opt1', value: 'Option 1' },
        { id: 'opt2', value: 'Option 2' }
    ],
    default: 'Option 1',
    canChangeDefinition: true,
    onOptionsChange: (options) => console.log('Options changed:', options),
    onDefaultOptionChange: (defaultValue) => console.log('Default changed:', defaultValue)
});

// 注册自定义模板
selectionManager.registerOptionTemplate('custom', [
    { id: 'custom1', value: 'Custom Option 1' },
    { id: 'custom2', value: 'Custom Option 2' }
]);

// 获取统计信息
const stats = selectionManager.getSelectionStatistics();
console.log('Selection statistics:', stats);
```

## 技术特点

### 1. 拖拽排序
- **可排序**: 使用useSortable实现拖拽排序
- **权限控制**: 根据权限控制是否可排序
- **位置计算**: 精确计算拖拽后的位置
- **实时更新**: 拖拽后实时更新选项顺序

### 2. 动态编辑
- **即时编辑**: 支持选项的即时编辑
- **状态管理**: 区分新选项和现有选项状态
- **自动聚焦**: 新选项自动聚焦
- **失焦保存**: 失焦时自动保存编辑

### 3. 选项管理
- **添加删除**: 支持选项的添加和删除
- **默认值**: 支持默认值设置和管理
- **空值过滤**: 自动过滤空值选项
- **重复检查**: 检查重复选项值

### 4. 权限控制
- **只读模式**: 支持只读模式
- **定义权限**: 控制是否可修改定义
- **操作限制**: 根据权限限制操作
- **安全控制**: 确保操作的安全性

## 设计模式

### 1. 状态模式 (State Pattern)
- **编辑状态**: 管理选项的编辑状态
- **新建状态**: 管理新选项的状态
- **验证状态**: 管理验证状态

### 2. 观察者模式 (Observer Pattern)
- **选项观察**: 观察选项变化
- **默认值观察**: 观察默认值变化
- **状态观察**: 观察组件状态变化

### 3. 命令模式 (Command Pattern)
- **添加命令**: 封装添加选项命令
- **删除命令**: 封装删除选项命令
- **排序命令**: 封装排序命令

### 4. 策略模式 (Strategy Pattern)
- **验证策略**: 不同的验证策略
- **排序策略**: 不同的排序策略
- **编辑策略**: 不同的编辑策略

## 注意事项

1. **数据一致性**: 确保选项数据的一致性
2. **性能优化**: 避免频繁的DOM操作
3. **用户体验**: 提供流畅的编辑体验
4. **错误处理**: 完善的错误处理机制

## 扩展建议

1. **批量操作**: 支持批量添加/删除选项
2. **模板系统**: 提供常用选项模板
3. **导入导出**: 支持选项的导入导出
4. **验证增强**: 增强选项验证功能
5. **国际化**: 支持多语言选项

该属性定义选择组件为Odoo Web客户端提供了完整的选择类型属性配置功能，通过拖拽排序和动态编辑确保了选项管理的灵活性和易用性。

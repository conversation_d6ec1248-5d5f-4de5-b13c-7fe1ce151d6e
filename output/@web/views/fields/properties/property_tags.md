# PropertyTags - 属性标签组件

## 概述

`property_tags.js` 是 Odoo Web 客户端的属性标签组件，负责管理标签类型属性的显示、编辑和配置。该模块包含345行代码，是一个功能完整的标签管理组件，专门用于处理标签类型的动态属性，具备标签选择、颜色管理、自动完成、标签创建等特性，是标签类型属性处理的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/properties/property_tags.js`
- **行数**: 345
- **模块**: `@web/views/fields/properties/property_tags`

## 依赖关系

```javascript
// 核心依赖
'@web/core/autocomplete/autocomplete'   // 自动完成组件
'@web/core/colorlist/colorlist'         // 颜色列表组件
'@web/core/l10n/translation'            // 翻译服务
'@web/core/popover/popover_hook'        // 弹出框钩子
'@web/core/registry'                    // 注册表
'@web/core/utils/hooks'                 // 工具钩子
'@web/core/tags_list/tags_list'         // 标签列表组件
'@web/views/fields/standard_field_props' // 标准字段属性
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 颜色选择弹出框

```javascript
class PropertyTagsColorListPopover extends Component {
    static template = "web.PropertyTagsColorListPopover";
    static components = {
        ColorList,
    };
    static props = {
        colors: Array,
        tag: Object,
        switchTagColor: Function,
        close: Function,
    };
}
```

**弹出框特性**:
- **颜色列表**: 集成ColorList组件
- **标签对象**: 接收要修改颜色的标签
- **颜色切换**: 支持switchTagColor回调
- **关闭功能**: 支持close回调关闭弹出框

### 2. 自动完成组件

```javascript
// property tags does not really need timeout because it does not make RPC calls
const PropertyTagAutoComplete = class PropertyTagAutoComplete extends AutoComplete {}
Object.assign(PropertyTagAutoComplete, { timeout: 0 });
```

**自动完成特性**:
- **继承基类**: 继承AutoComplete组件
- **无超时**: 设置timeout为0，因为不需要RPC调用
- **本地搜索**: 基于本地数据进行搜索
- **即时响应**: 提供即时的搜索响应

### 3. 主要标签组件

```javascript
const PropertyTags = class PropertyTags extends Component {
    static template = "web.PropertyTags";
    static components = {
        AutoComplete: PropertyTagAutoComplete,
        TagsList,
        ColorList,
        Popover: PropertyTagsColorListPopover,
    };

    static props = {
        id: { type: String, optional: true },
        selectedTags: {}, // Tags value visible in the tags list
        tags: {}, // Tags definition visible in the dropdown
        // Define the behavior of the delete button on the tags, either
        // "value" or "tags". If "value", the delete button will unselect
        // the tag. If "tags", the delete button will remove the tag
        // from the tags definition.
        deleteMode: { type: String, optional: true },
        readonly: { type: Boolean, optional: true },
        canChangeDefinition: { type: Boolean, optional: true },
        onTagsChange: { type: Function, optional: true },
        onSelectedTagsChange: { type: Function, optional: true },
    };
}
```

**主组件特性**:
- **多组件集成**: 集成自动完成、标签列表、颜色列表等组件
- **标签管理**: 管理选中标签和标签定义
- **删除模式**: 支持value和tags两种删除模式
- **权限控制**: 支持只读和定义变更权限
- **事件回调**: 支持标签变更和选择变更回调

### 4. 组件初始化

```javascript
setup() {
    this.orm = useService("orm");
    this.popover = usePopover(PropertyTagsColorListPopover, {
        position: "bottom",
    });

    this.nextColorIndex = 0;
}
```

**初始化功能**:
- **ORM服务**: 注入ORM服务
- **弹出框**: 配置颜色选择弹出框
- **颜色索引**: 管理下一个颜色索引
- **位置配置**: 设置弹出框位置为底部

### 5. 标签操作

```javascript
get availableColors() {
    return [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11];
}

_getNextColor() {
    const color = this.availableColors[this.nextColorIndex];
    this.nextColorIndex = (this.nextColorIndex + 1) % this.availableColors.length;
    return color;
}

_onTagSelect(tag) {
    if (this.props.readonly) {
        return;
    }

    const selectedTags = [...this.props.selectedTags];
    const tagIndex = selectedTags.findIndex((selectedTag) => selectedTag.id === tag.id);
    if (tagIndex >= 0) {
        // tag already selected, unselect it
        selectedTags.splice(tagIndex, 1);
    } else {
        // tag not selected, select it
        selectedTags.push(tag);
    }

    this.props.onSelectedTagsChange(selectedTags);
}
```

**标签操作功能**:
- **颜色管理**: 提供12种可用颜色
- **颜色循环**: 循环分配颜色给新标签
- **标签选择**: 处理标签的选择和取消选择
- **状态切换**: 智能切换标签的选中状态

## 使用场景

### 1. 属性标签管理器

```javascript
// 属性标签管理器
class PropertyTagsManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置标签配置
        this.tagsConfig = {
            enableColorManagement: true,
            enableAutoComplete: true,
            enableTagCreation: true,
            enableTagDeletion: true,
            enableBulkOperations: false,
            enableTagTemplates: false,
            maxTags: 20,
            maxTagLength: 50
        };
        
        // 设置颜色配置
        this.colorConfig = {
            availableColors: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
            enableCustomColors: false,
            defaultColor: 0,
            colorRotation: true,
            enableColorPreview: true
        };
        
        // 设置删除模式
        this.deleteModes = new Map([
            ['value', {
                name: 'Value Mode',
                description: 'Delete button unselects the tag',
                action: 'unselect'
            }],
            ['tags', {
                name: 'Tags Mode', 
                description: 'Delete button removes the tag from definition',
                action: 'remove'
            }]
        ]);
        
        // 设置验证规则
        this.validationRules = {
            enableNameValidation: true,
            enableDuplicateCheck: true,
            enableLengthCheck: true,
            minTagLength: 1,
            maxTagLength: 50,
            allowSpecialCharacters: true,
            reservedNames: ['null', 'undefined', 'true', 'false']
        };
        
        // 设置标签统计
        this.tagsStatistics = {
            totalTagFields: 0,
            totalTags: 0,
            totalSelectedTags: 0,
            tagsByColor: new Map(),
            averageTagsPerField: 0,
            mostUsedColor: null,
            createdTags: 0,
            deletedTags: 0
        };
        
        this.initializeTagsSystem();
    }
    
    // 初始化标签系统
    initializeTagsSystem() {
        // 创建增强的属性标签组件
        this.createEnhancedPropertyTags();
        
        // 设置颜色管理系统
        this.setupColorManagementSystem();
        
        // 设置自动完成系统
        this.setupAutoCompleteSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
    }
    
    // 创建增强的属性标签组件
    createEnhancedPropertyTags() {
        const originalComponent = PropertyTags;
        
        this.EnhancedPropertyTags = class extends originalComponent {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加颜色管理功能
                this.addColorManagementFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    validationErrors: new Map(),
                    isCreatingTag: false,
                    searchQuery: '',
                    filteredTags: [],
                    colorHistory: [],
                    lastCreatedTag: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的标签验证
                this.validateTag = (tagName) => {
                    const errors = [];
                    
                    // 名称验证
                    if (this.validationRules.enableNameValidation) {
                        if (!tagName || !tagName.trim()) {
                            errors.push('Tag name cannot be empty');
                        }
                    }
                    
                    // 长度验证
                    if (this.validationRules.enableLengthCheck) {
                        if (tagName.length < this.validationRules.minTagLength) {
                            errors.push(`Tag name must be at least ${this.validationRules.minTagLength} characters`);
                        }
                        if (tagName.length > this.validationRules.maxTagLength) {
                            errors.push(`Tag name must not exceed ${this.validationRules.maxTagLength} characters`);
                        }
                    }
                    
                    // 重复检查
                    if (this.validationRules.enableDuplicateCheck) {
                        const duplicates = this.props.tags.filter(tag => 
                            tag.name.toLowerCase() === tagName.toLowerCase()
                        );
                        if (duplicates.length > 0) {
                            errors.push('Tag name already exists');
                        }
                    }
                    
                    // 保留名称检查
                    if (this.validationRules.reservedNames.includes(tagName.toLowerCase())) {
                        errors.push('Reserved name cannot be used');
                    }
                    
                    return errors;
                };
                
                // 增强的标签创建
                this.enhancedCreateTag = (tagName) => {
                    // 验证标签名称
                    const errors = this.validateTag(tagName);
                    if (errors.length > 0) {
                        this.enhancedState.validationErrors.set(tagName, errors);
                        return null;
                    }
                    
                    // 检查最大标签数
                    if (this.props.tags.length >= this.tagsConfig.maxTags) {
                        this.notification.add(
                            `Maximum ${this.tagsConfig.maxTags} tags allowed`,
                            { type: 'warning' }
                        );
                        return null;
                    }
                    
                    // 创建新标签
                    const newTag = {
                        id: this.generateTagId(),
                        name: tagName.trim(),
                        color: this.getNextColor()
                    };
                    
                    // 添加到标签列表
                    const updatedTags = [...this.props.tags, newTag];
                    this.props.onTagsChange(updatedTags);
                    
                    // 记录统计
                    this.recordTagCreation(newTag);
                    
                    // 更新状态
                    this.enhancedState.lastCreatedTag = newTag;
                    
                    return newTag;
                };
                
                // 增强的标签删除
                this.enhancedDeleteTag = (tagId) => {
                    const tagIndex = this.props.tags.findIndex(tag => tag.id === tagId);
                    if (tagIndex === -1) {
                        return false;
                    }
                    
                    const tag = this.props.tags[tagIndex];
                    
                    // 从标签定义中删除
                    const updatedTags = [...this.props.tags];
                    updatedTags.splice(tagIndex, 1);
                    this.props.onTagsChange(updatedTags);
                    
                    // 从选中标签中删除
                    const updatedSelectedTags = this.props.selectedTags.filter(
                        selectedTag => selectedTag.id !== tagId
                    );
                    this.props.onSelectedTagsChange(updatedSelectedTags);
                    
                    // 记录统计
                    this.recordTagDeletion(tag);
                    
                    return true;
                };
                
                // 增强的颜色切换
                this.enhancedSwitchTagColor = (tag, newColor) => {
                    const tagIndex = this.props.tags.findIndex(t => t.id === tag.id);
                    if (tagIndex === -1) {
                        return false;
                    }
                    
                    // 更新标签颜色
                    const updatedTags = [...this.props.tags];
                    updatedTags[tagIndex] = { ...tag, color: newColor };
                    this.props.onTagsChange(updatedTags);
                    
                    // 更新选中标签颜色
                    const selectedTagIndex = this.props.selectedTags.findIndex(t => t.id === tag.id);
                    if (selectedTagIndex >= 0) {
                        const updatedSelectedTags = [...this.props.selectedTags];
                        updatedSelectedTags[selectedTagIndex] = { ...tag, color: newColor };
                        this.props.onSelectedTagsChange(updatedSelectedTags);
                    }
                    
                    // 记录颜色历史
                    this.recordColorChange(tag, newColor);
                    
                    return true;
                };
                
                // 增强的标签搜索
                this.enhancedSearchTags = (query) => {
                    this.enhancedState.searchQuery = query;
                    
                    if (!query.trim()) {
                        this.enhancedState.filteredTags = this.props.tags;
                        return;
                    }
                    
                    const filtered = this.props.tags.filter(tag =>
                        tag.name.toLowerCase().includes(query.toLowerCase())
                    );
                    
                    this.enhancedState.filteredTags = filtered;
                };
                
                // 生成标签ID
                this.generateTagId = () => {
                    return 'tag_' + Math.random().toString(36).substr(2, 9);
                };
                
                // 获取下一个颜色
                this.getNextColor = () => {
                    if (!this.colorConfig.colorRotation) {
                        return this.colorConfig.defaultColor;
                    }
                    
                    // 统计已使用的颜色
                    const usedColors = new Map();
                    this.props.tags.forEach(tag => {
                        const count = usedColors.get(tag.color) || 0;
                        usedColors.set(tag.color, count + 1);
                    });
                    
                    // 找到使用最少的颜色
                    let minCount = Infinity;
                    let selectedColor = this.colorConfig.defaultColor;
                    
                    for (const color of this.colorConfig.availableColors) {
                        const count = usedColors.get(color) || 0;
                        if (count < minCount) {
                            minCount = count;
                            selectedColor = color;
                        }
                    }
                    
                    return selectedColor;
                };
                
                // 批量创建标签
                this.batchCreateTags = (tagNames) => {
                    const createdTags = [];
                    
                    for (const name of tagNames) {
                        const tag = this.enhancedCreateTag(name);
                        if (tag) {
                            createdTags.push(tag);
                        }
                    }
                    
                    return createdTags;
                };
                
                // 获取标签信息
                this.getTagsInfo = () => {
                    return {
                        totalTags: this.props.tags.length,
                        selectedCount: this.props.selectedTags.length,
                        availableColors: this.colorConfig.availableColors.length,
                        maxTags: this.tagsConfig.maxTags,
                        canCreateMore: this.props.tags.length < this.tagsConfig.maxTags,
                        validationErrors: this.enhancedState.validationErrors.size,
                        searchQuery: this.enhancedState.searchQuery,
                        filteredCount: this.enhancedState.filteredTags.length
                    };
                };
                
                // 记录标签创建
                this.recordTagCreation = (tag) => {
                    this.tagsStatistics.createdTags++;
                    this.tagsStatistics.totalTags++;
                    
                    // 记录颜色分布
                    const colorCount = this.tagsStatistics.tagsByColor.get(tag.color) || 0;
                    this.tagsStatistics.tagsByColor.set(tag.color, colorCount + 1);
                    
                    this.updateAverageTags();
                };
                
                // 记录标签删除
                this.recordTagDeletion = (tag) => {
                    this.tagsStatistics.deletedTags++;
                    this.tagsStatistics.totalTags--;
                    
                    // 更新颜色分布
                    const colorCount = this.tagsStatistics.tagsByColor.get(tag.color) || 0;
                    if (colorCount > 0) {
                        this.tagsStatistics.tagsByColor.set(tag.color, colorCount - 1);
                    }
                    
                    this.updateAverageTags();
                };
                
                // 记录颜色变更
                this.recordColorChange = (tag, newColor) => {
                    this.enhancedState.colorHistory.unshift({
                        tagId: tag.id,
                        tagName: tag.name,
                        oldColor: tag.color,
                        newColor: newColor,
                        timestamp: new Date()
                    });
                    
                    // 限制历史大小
                    if (this.enhancedState.colorHistory.length > 50) {
                        this.enhancedState.colorHistory.pop();
                    }
                };
                
                // 更新平均标签数
                this.updateAverageTags = () => {
                    if (this.tagsStatistics.totalTagFields > 0) {
                        this.tagsStatistics.averageTagsPerField = 
                            this.tagsStatistics.totalTags / this.tagsStatistics.totalTagFields;
                    }
                };
            }
            
            addColorManagementFeatures() {
                // 颜色管理功能
                this.colorManager = {
                    enabled: this.tagsConfig.enableColorManagement,
                    getNext: () => this.getNextColor(),
                    switch: (tag, color) => this.enhancedSwitchTagColor(tag, color),
                    getAvailable: () => this.colorConfig.availableColors,
                    getHistory: () => this.enhancedState.colorHistory
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.validationRules.enableNameValidation,
                    validate: (name) => this.validateTag(name),
                    getErrors: () => Array.from(this.enhancedState.validationErrors.entries()),
                    clearErrors: () => this.enhancedState.validationErrors.clear()
                };
            }
            
            // 重写原始方法
            _getNextColor() {
                return this.getNextColor();
            }
        };
    }
    
    // 设置颜色管理系统
    setupColorManagementSystem() {
        this.colorManagementConfig = {
            enabled: this.tagsConfig.enableColorManagement,
            config: this.colorConfig
        };
    }
    
    // 设置自动完成系统
    setupAutoCompleteSystem() {
        this.autoCompleteConfig = {
            enabled: this.tagsConfig.enableAutoComplete,
            timeout: 0,
            minSearchLength: 1,
            maxResults: 10
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.validationRules.enableNameValidation,
            rules: this.validationRules
        };
    }
    
    // 创建属性标签组件
    createPropertyTags(props) {
        const component = new this.EnhancedPropertyTags(props);
        this.tagsStatistics.totalTagFields++;
        return component;
    }
    
    // 获取最受欢迎的颜色
    getMostPopularColor() {
        let maxCount = 0;
        let mostUsedColor = null;
        
        for (const [color, count] of this.tagsStatistics.tagsByColor.entries()) {
            if (count > maxCount) {
                maxCount = count;
                mostUsedColor = color;
            }
        }
        
        this.tagsStatistics.mostUsedColor = mostUsedColor;
        return mostUsedColor;
    }
    
    // 获取标签统计
    getTagsStatistics() {
        return {
            ...this.tagsStatistics,
            mostUsedColor: this.getMostPopularColor(),
            colorVariety: this.tagsStatistics.tagsByColor.size,
            creationRate: this.tagsStatistics.createdTags / Math.max(this.tagsStatistics.totalTagFields, 1),
            deletionRate: this.tagsStatistics.deletedTags / Math.max(this.tagsStatistics.totalTags + this.tagsStatistics.deletedTags, 1) * 100,
            selectionRate: this.tagsStatistics.totalSelectedTags / Math.max(this.tagsStatistics.totalTags, 1) * 100
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理颜色分布
        this.tagsStatistics.tagsByColor.clear();
        
        // 重置统计
        this.tagsStatistics = {
            totalTagFields: 0,
            totalTags: 0,
            totalSelectedTags: 0,
            tagsByColor: new Map(),
            averageTagsPerField: 0,
            mostUsedColor: null,
            createdTags: 0,
            deletedTags: 0
        };
    }
}

// 使用示例
const tagsManager = new PropertyTagsManager();

// 创建属性标签组件
const tagsComponent = tagsManager.createPropertyTags({
    selectedTags: [
        { id: 'tag1', name: 'Important', color: 1 },
        { id: 'tag2', name: 'Urgent', color: 3 }
    ],
    tags: [
        { id: 'tag1', name: 'Important', color: 1 },
        { id: 'tag2', name: 'Urgent', color: 3 },
        { id: 'tag3', name: 'Low Priority', color: 5 }
    ],
    deleteMode: 'value',
    canChangeDefinition: true,
    onTagsChange: (tags) => console.log('Tags changed:', tags),
    onSelectedTagsChange: (selectedTags) => console.log('Selection changed:', selectedTags)
});

// 获取统计信息
const stats = tagsManager.getTagsStatistics();
console.log('Tags statistics:', stats);
```

## 技术特点

### 1. 颜色管理
- **12种颜色**: 提供12种预定义颜色
- **颜色循环**: 智能分配颜色避免重复
- **颜色切换**: 支持动态切换标签颜色
- **颜色弹出框**: 提供颜色选择弹出框

### 2. 自动完成
- **即时搜索**: 无超时的即时搜索
- **本地数据**: 基于本地标签数据搜索
- **智能过滤**: 智能过滤匹配的标签
- **快速响应**: 提供快速的搜索响应

### 3. 标签操作
- **选择切换**: 智能切换标签选中状态
- **创建删除**: 支持标签的创建和删除
- **双重模式**: 支持value和tags两种删除模式
- **批量操作**: 支持批量标签操作

### 4. 权限控制
- **只读模式**: 支持只读模式
- **定义权限**: 控制是否可修改标签定义
- **操作限制**: 根据权限限制操作
- **安全控制**: 确保操作的安全性

## 设计模式

### 1. 组合模式 (Composite Pattern)
- **组件组合**: 组合多个子组件
- **功能组合**: 组合标签、颜色、自动完成功能
- **界面组合**: 组合复杂的标签界面

### 2. 策略模式 (Strategy Pattern)
- **删除策略**: 不同的删除模式策略
- **颜色策略**: 不同的颜色分配策略
- **搜索策略**: 不同的搜索策略

### 3. 观察者模式 (Observer Pattern)
- **标签观察**: 观察标签变化
- **选择观察**: 观察选择变化
- **颜色观察**: 观察颜色变化

### 4. 工厂模式 (Factory Pattern)
- **标签工厂**: 创建新标签
- **颜色工厂**: 分配标签颜色
- **组件工厂**: 创建子组件

## 注意事项

1. **性能优化**: 避免频繁的标签操作
2. **颜色管理**: 合理分配和管理颜色
3. **用户体验**: 提供流畅的标签交互
4. **数据一致性**: 确保标签数据的一致性

## 扩展建议

1. **自定义颜色**: 支持自定义颜色
2. **标签分组**: 支持标签分组功能
3. **标签模板**: 提供常用标签模板
4. **导入导出**: 支持标签的导入导出
5. **搜索增强**: 增强标签搜索功能

该属性标签组件为Odoo Web客户端提供了完整的标签类型属性管理功能，通过颜色管理和自动完成确保了标签操作的便利性和视觉效果。

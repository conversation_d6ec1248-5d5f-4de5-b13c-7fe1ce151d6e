# PropertyValue - 属性值组件

## 概述

`property_value.js` 是 Odoo Web 客户端的属性值组件，负责根据不同的属性类型渲染相应的输入和显示组件。该模块包含367行代码，是一个功能完整的多类型值处理组件，专门用于处理动态属性的值输入、显示和编辑，具备多类型支持、格式化显示、验证处理、关系字段等特性，是动态属性系统的核心值处理组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/properties/property_value.js`
- **行数**: 367
- **模块**: `@web/views/fields/properties/property_value`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                            // OWL框架
'@web/core/autocomplete/autocomplete'   // 自动完成组件
'@web/core/checkbox/checkbox'           // 复选框组件
'@web/core/datetime/datetime_input'     // 日期时间输入组件
'@web/core/domain'                      // 域工具
'@web/core/dropdown/dropdown'           // 下拉菜单组件
'@web/core/dropdown/dropdown_item'      // 下拉菜单项组件
'@web/core/l10n/dates'                  // 日期本地化
'@web/core/l10n/translation'            // 翻译服务
'@web/core/tags_list/tags_list'         // 标签列表组件
'@web/core/utils/hooks'                 // 工具钩子
'@web/views/fields/formatters'          // 字段格式化器
'@web/core/utils/numbers'               // 数字工具
'@web/views/fields/many2one/many2one_field' // 多对一字段
'@web/views/fields/parsers'             // 字段解析器
'@web/views/fields/relational_utils'    // 关系字段工具
'@web/views/fields/properties/property_tags' // 属性标签
'@web/core/utils/urls'                  // URL工具
```

## 核心功能

### 1. 组件定义

```javascript
const PropertyValue = class PropertyValue extends Component {
    static template = "web.PropertyValue";
    static components = {
        Dropdown,
        DropdownItem,
        CheckBox,
        DateTimeInput,
        AutoComplete,
        TagsList,
        Many2XAutocomplete,
        PropertyTags,
    };

    static props = {
        propertyDefinition: Object,
        propertyValue: { optional: true },
        context: { type: Object },
        readonly: { type: Boolean, optional: true },
        onChange: { type: Function, optional: true },
    };
}
```

**组件特性**:
- **多组件集成**: 集成多种UI组件支持不同类型
- **属性定义**: 接收propertyDefinition配置属性类型
- **属性值**: 接收propertyValue当前值
- **上下文**: 支持context上下文传递
- **只读模式**: 支持readonly只读模式
- **变更回调**: 支持onChange变更回调

### 2. 组件初始化

```javascript
setup() {
    this.orm = useService("orm");
    this.openRecord = useOpenMany2XRecord({
        resModel: this.resModel,
        activeActions: {
            edit: false,
            create: false,
        },
        isToMany: this.propertyType === "many2many",
    });
}
```

**初始化功能**:
- **ORM服务**: 注入ORM服务
- **记录打开**: 配置记录打开功能
- **关系模型**: 设置关系模型
- **操作限制**: 限制编辑和创建操作
- **多对多**: 支持多对多关系类型

### 3. 属性类型处理

```javascript
get propertyType() {
    return this.props.propertyDefinition.type;
}

get resModel() {
    return this.props.propertyDefinition.comodel;
}

get domain() {
    return this.props.propertyDefinition.domain || [];
}

get selection() {
    return this.props.propertyDefinition.selection || [];
}
```

**类型处理功能**:
- **类型获取**: 获取属性类型
- **关系模型**: 获取关系模型名称
- **域过滤**: 获取域过滤条件
- **选择选项**: 获取选择类型的选项列表
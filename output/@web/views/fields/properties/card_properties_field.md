# CardPropertiesField - 卡片属性字段

## 概述

`card_properties_field.js` 是 Odoo Web 客户端的卡片属性字段组件，是专门为卡片视图（日历、看板、层级）优化的属性字段。该模块包含26行代码，是一个简洁的卡片专用组件，继承自基础属性字段，专门用于卡片视图中的属性显示，具备定义权限限制、多视图支持等特性，是卡片视图属性管理的专用组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/properties/card_properties_field.js`
- **行数**: 26
- **模块**: `@web/views/fields/properties/card_properties_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                    // 注册表
'@web/views/fields/properties/properties_field' // 基础属性字段
```

## 核心功能

### 1. 组件定义

```javascript
const CardPropertiesField = class CardPropertiesField extends PropertiesField {
    static template = "web.CardPropertiesField";

    async _checkDefinitionAccess() {
        // can not edit properties definition in cards
        this.state.canChangeDefinition = false;
    }
}
```

**组件特性**:
- **继承基类**: 继承PropertiesField的所有功能
- **专用模板**: 使用CardPropertiesField专用模板
- **权限限制**: 禁用卡片视图中的属性定义编辑
- **卡片优化**: 专门为卡片视图优化

### 2. 字段注册

```javascript
const cardPropertiesField = {
    ...propertiesField,
    component: CardPropertiesField,
};

registry.category("fields").add("calendar.properties", cardPropertiesField);
registry.category("fields").add("kanban.properties", cardPropertiesField);
registry.category("fields").add("hierarchy.properties", cardPropertiesField);
```

**注册功能**:
- **配置继承**: 继承基础属性字段的所有配置
- **组件替换**: 使用卡片专用组件
- **多视图注册**: 注册为多种卡片视图类型
- **命名空间**: 使用视图前缀区分不同卡片字段

## 使用场景

### 1. 卡片属性字段管理器

```javascript
// 卡片属性字段管理器
class CardPropertiesFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置卡片属性配置
        this.cardPropertiesConfig = {
            enableDefinitionEdit: false,
            enableValueEdit: true,
            enableCompactMode: true,
            enableTooltips: true,
            enableGrouping: false,
            enableSorting: false,
            enableFiltering: false,
            enableBulkEdit: false
        };
        
        // 设置卡片显示选项
        this.cardDisplayOptions = {
            maxVisibleProperties: 5,
            showPropertyNames: true,
            showPropertyIcons: false,
            compactLayout: true,
            truncateValues: true,
            maxValueLength: 30,
            enableCollapse: true
        };
        
        // 设置支持的视图类型
        this.supportedViewTypes = new Map([
            ['calendar', { name: 'Calendar', icon: 'fa-calendar', maxProperties: 3 }],
            ['kanban', { name: 'Kanban', icon: 'fa-th-large', maxProperties: 5 }],
            ['hierarchy', { name: 'Hierarchy', icon: 'fa-sitemap', maxProperties: 4 }]
        ]);
        
        // 设置属性类型配置
        this.propertyTypeConfig = new Map([
            ['char', { displayName: 'Text', icon: 'fa-font', allowInCard: true }],
            ['text', { displayName: 'Long Text', icon: 'fa-align-left', allowInCard: false }],
            ['integer', { displayName: 'Integer', icon: 'fa-hashtag', allowInCard: true }],
            ['float', { displayName: 'Float', icon: 'fa-calculator', allowInCard: true }],
            ['boolean', { displayName: 'Boolean', icon: 'fa-check-square', allowInCard: true }],
            ['date', { displayName: 'Date', icon: 'fa-calendar-day', allowInCard: true }],
            ['datetime', { displayName: 'DateTime', icon: 'fa-clock', allowInCard: true }],
            ['selection', { displayName: 'Selection', icon: 'fa-list', allowInCard: true }],
            ['many2one', { displayName: 'Many2One', icon: 'fa-link', allowInCard: true }],
            ['many2many', { displayName: 'Many2Many', icon: 'fa-tags', allowInCard: false }],
            ['tags', { displayName: 'Tags', icon: 'fa-tags', allowInCard: true }]
        ]);
        
        // 设置卡片统计
        this.cardPropertiesStatistics = {
            totalCardFields: 0,
            totalProperties: 0,
            propertiesByType: new Map(),
            propertiesByView: new Map(),
            averagePropertiesPerCard: 0,
            mostUsedPropertyType: null
        };
        
        this.initializeCardPropertiesSystem();
    }
    
    // 初始化卡片属性系统
    initializeCardPropertiesSystem() {
        // 创建增强的卡片属性字段
        this.createEnhancedCardPropertiesField();
        
        // 设置卡片显示系统
        this.setupCardDisplaySystem();
        
        // 设置属性过滤系统
        this.setupPropertyFilterSystem();
        
        // 设置视图适配系统
        this.setupViewAdaptationSystem();
    }
    
    // 创建增强的卡片属性字段
    createEnhancedCardPropertiesField() {
        const originalField = CardPropertiesField;
        
        this.EnhancedCardPropertiesField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加卡片增强功能
                this.addCardEnhancedFeatures();
                
                // 添加卡片显示功能
                this.addCardDisplayFeatures();
                
                // 添加卡片交互功能
                this.addCardInteractionFeatures();
            }
            
            addCardEnhancedFeatures() {
                // 扩展卡片状态
                this.cardState = {
                    viewType: this.getViewType(),
                    visibleProperties: [],
                    hiddenProperties: [],
                    isCollapsed: false,
                    maxProperties: this.getMaxProperties(),
                    compactMode: true
                };
                
                // 添加卡片增强方法
                this.addCardEnhancedMethods();
            }
            
            addCardEnhancedMethods() {
                // 获取视图类型
                this.getViewType = () => {
                    const viewType = this.env.config?.viewType;
                    return this.supportedViewTypes.has(viewType) ? viewType : 'kanban';
                };
                
                // 获取最大属性数
                this.getMaxProperties = () => {
                    const viewType = this.getViewType();
                    const viewConfig = this.supportedViewTypes.get(viewType);
                    return viewConfig?.maxProperties || this.cardDisplayOptions.maxVisibleProperties;
                };
                
                // 增强的定义权限检查
                this.enhancedCheckDefinitionAccess = async () => {
                    // 卡片视图中始终禁用定义编辑
                    this.state.canChangeDefinition = false;
                    
                    // 记录权限检查
                    this.recordDefinitionAccessCheck();
                };
                
                // 过滤卡片属性
                this.filterCardProperties = (properties) => {
                    if (!properties || !Array.isArray(properties)) {
                        return [];
                    }
                    
                    return properties.filter(property => {
                        // 检查属性类型是否适合卡片显示
                        const typeConfig = this.propertyTypeConfig.get(property.type);
                        if (!typeConfig?.allowInCard) {
                            return false;
                        }
                        
                        // 检查属性值是否有意义
                        if (this.isEmptyPropertyValue(property.value)) {
                            return false;
                        }
                        
                        return true;
                    });
                };
                
                // 检查属性值是否为空
                this.isEmptyPropertyValue = (value) => {
                    if (value === null || value === undefined) {
                        return true;
                    }
                    
                    if (typeof value === 'string' && value.trim() === '') {
                        return true;
                    }
                    
                    if (Array.isArray(value) && value.length === 0) {
                        return true;
                    }
                    
                    return false;
                };
                
                // 获取可见属性
                this.getVisibleProperties = () => {
                    const allProperties = this.getAllProperties();
                    const filteredProperties = this.filterCardProperties(allProperties);
                    
                    // 限制显示数量
                    const maxProperties = this.cardState.maxProperties;
                    const visibleProperties = filteredProperties.slice(0, maxProperties);
                    const hiddenProperties = filteredProperties.slice(maxProperties);
                    
                    this.cardState.visibleProperties = visibleProperties;
                    this.cardState.hiddenProperties = hiddenProperties;
                    
                    return visibleProperties;
                };
                
                // 获取所有属性
                this.getAllProperties = () => {
                    const propertiesData = this.props.record.data[this.props.name];
                    return propertiesData || [];
                };
                
                // 格式化属性值
                this.formatPropertyValue = (property) => {
                    let formattedValue = property.value;
                    
                    // 根据类型格式化
                    switch (property.type) {
                        case 'char':
                        case 'text':
                            formattedValue = this.formatTextValue(property.value);
                            break;
                        case 'integer':
                        case 'float':
                            formattedValue = this.formatNumberValue(property.value);
                            break;
                        case 'boolean':
                            formattedValue = this.formatBooleanValue(property.value);
                            break;
                        case 'date':
                        case 'datetime':
                            formattedValue = this.formatDateValue(property.value);
                            break;
                        case 'selection':
                            formattedValue = this.formatSelectionValue(property);
                            break;
                        case 'many2one':
                            formattedValue = this.formatMany2OneValue(property.value);
                            break;
                        case 'tags':
                            formattedValue = this.formatTagsValue(property.value);
                            break;
                        default:
                            formattedValue = String(property.value);
                    }
                    
                    // 截断长值
                    if (this.cardDisplayOptions.truncateValues) {
                        formattedValue = this.truncateValue(formattedValue);
                    }
                    
                    return formattedValue;
                };
                
                // 格式化文本值
                this.formatTextValue = (value) => {
                    return value ? String(value).trim() : '';
                };
                
                // 格式化数字值
                this.formatNumberValue = (value) => {
                    return value !== null && value !== undefined ? Number(value).toLocaleString() : '';
                };
                
                // 格式化布尔值
                this.formatBooleanValue = (value) => {
                    return value ? '✓' : '✗';
                };
                
                // 格式化日期值
                this.formatDateValue = (value) => {
                    if (!value) return '';
                    
                    try {
                        const date = new Date(value);
                        return date.toLocaleDateString();
                    } catch (error) {
                        return String(value);
                    }
                };
                
                // 格式化选择值
                this.formatSelectionValue = (property) => {
                    if (!property.selection || !property.value) {
                        return String(property.value || '');
                    }
                    
                    const option = property.selection.find(opt => opt[0] === property.value);
                    return option ? option[1] : String(property.value);
                };
                
                // 格式化多对一值
                this.formatMany2OneValue = (value) => {
                    if (Array.isArray(value) && value.length >= 2) {
                        return value[1]; // 显示名称
                    }
                    return String(value || '');
                };
                
                // 格式化标签值
                this.formatTagsValue = (value) => {
                    if (Array.isArray(value)) {
                        return value.map(tag => tag.name || tag).join(', ');
                    }
                    return String(value || '');
                };
                
                // 截断值
                this.truncateValue = (value) => {
                    const maxLength = this.cardDisplayOptions.maxValueLength;
                    if (value.length > maxLength) {
                        return value.substring(0, maxLength - 3) + '...';
                    }
                    return value;
                };
                
                // 切换折叠状态
                this.toggleCollapse = () => {
                    this.cardState.isCollapsed = !this.cardState.isCollapsed;
                };
                
                // 获取属性图标
                this.getPropertyIcon = (propertyType) => {
                    const typeConfig = this.propertyTypeConfig.get(propertyType);
                    return typeConfig?.icon || 'fa-question';
                };
                
                // 获取卡片属性信息
                this.getCardPropertiesInfo = () => {
                    const visibleProperties = this.getVisibleProperties();
                    
                    return {
                        viewType: this.cardState.viewType,
                        totalProperties: this.getAllProperties().length,
                        visibleCount: visibleProperties.length,
                        hiddenCount: this.cardState.hiddenProperties.length,
                        maxProperties: this.cardState.maxProperties,
                        isCollapsed: this.cardState.isCollapsed,
                        compactMode: this.cardState.compactMode,
                        canChangeDefinition: this.state.canChangeDefinition
                    };
                };
                
                // 记录定义权限检查
                this.recordDefinitionAccessCheck = () => {
                    // 记录权限检查统计
                };
                
                // 记录卡片属性统计
                this.recordCardPropertiesStatistics = () => {
                    const properties = this.getAllProperties();
                    
                    this.cardPropertiesStatistics.totalProperties += properties.length;
                    
                    // 记录按类型分布
                    properties.forEach(property => {
                        const count = this.cardPropertiesStatistics.propertiesByType.get(property.type) || 0;
                        this.cardPropertiesStatistics.propertiesByType.set(property.type, count + 1);
                    });
                    
                    // 记录按视图分布
                    const viewType = this.cardState.viewType;
                    const viewCount = this.cardPropertiesStatistics.propertiesByView.get(viewType) || 0;
                    this.cardPropertiesStatistics.propertiesByView.set(viewType, viewCount + 1);
                    
                    // 更新平均值
                    this.updateAverageProperties();
                };
                
                // 更新平均属性数
                this.updateAverageProperties = () => {
                    const totalFields = this.cardPropertiesStatistics.totalCardFields;
                    if (totalFields > 0) {
                        this.cardPropertiesStatistics.averagePropertiesPerCard = 
                            this.cardPropertiesStatistics.totalProperties / totalFields;
                    }
                };
            }
            
            addCardDisplayFeatures() {
                // 卡片显示功能
                this.cardDisplayManager = {
                    enabled: true,
                    getVisible: () => this.getVisibleProperties(),
                    format: (property) => this.formatPropertyValue(property),
                    filter: (properties) => this.filterCardProperties(properties),
                    getInfo: () => this.getCardPropertiesInfo()
                };
            }
            
            addCardInteractionFeatures() {
                // 卡片交互功能
                this.cardInteractionManager = {
                    enabled: this.cardPropertiesConfig.enableValueEdit,
                    toggleCollapse: () => this.toggleCollapse(),
                    canEdit: () => !this.props.readonly,
                    canChangeDefinition: () => false // 卡片中始终为false
                };
            }
            
            // 重写原始方法
            async _checkDefinitionAccess() {
                return this.enhancedCheckDefinitionAccess();
            }
        };
    }
    
    // 设置卡片显示系统
    setupCardDisplaySystem() {
        this.cardDisplaySystemConfig = {
            enabled: true,
            options: this.cardDisplayOptions,
            viewTypes: this.supportedViewTypes
        };
    }
    
    // 设置属性过滤系统
    setupPropertyFilterSystem() {
        this.propertyFilterSystemConfig = {
            enabled: true,
            typeConfig: this.propertyTypeConfig,
            allowedTypes: Array.from(this.propertyTypeConfig.entries())
                .filter(([type, config]) => config.allowInCard)
                .map(([type]) => type)
        };
    }
    
    // 设置视图适配系统
    setupViewAdaptationSystem() {
        this.viewAdaptationConfig = {
            enabled: true,
            supportedViews: this.supportedViewTypes,
            adaptationRules: {
                calendar: { maxProperties: 3, compactMode: true },
                kanban: { maxProperties: 5, compactMode: false },
                hierarchy: { maxProperties: 4, compactMode: true }
            }
        };
    }
    
    // 创建卡片属性字段
    createCardPropertiesField(props) {
        const field = new this.EnhancedCardPropertiesField(props);
        this.cardPropertiesStatistics.totalCardFields++;
        return field;
    }
    
    // 批量创建卡片属性字段
    batchCreateCardPropertiesFields(propsArray) {
        const results = [];
        
        for (const props of propsArray) {
            try {
                const field = this.createCardPropertiesField(props);
                results.push({ field, success: true, props });
            } catch (error) {
                results.push({ field: null, success: false, error, props });
            }
        }
        
        return results;
    }
    
    // 注册属性类型
    registerPropertyType(type, config) {
        this.propertyTypeConfig.set(type, config);
    }
    
    // 注册视图类型
    registerViewType(type, config) {
        this.supportedViewTypes.set(type, config);
    }
    
    // 获取最受欢迎的属性类型
    getMostPopularPropertyType() {
        let maxCount = 0;
        let mostUsedType = null;
        
        for (const [type, count] of this.cardPropertiesStatistics.propertiesByType.entries()) {
            if (count > maxCount) {
                maxCount = count;
                mostUsedType = type;
            }
        }
        
        this.cardPropertiesStatistics.mostUsedPropertyType = mostUsedType;
        return mostUsedType;
    }
    
    // 获取卡片属性统计
    getCardPropertiesStatistics() {
        return {
            ...this.cardPropertiesStatistics,
            mostUsedPropertyType: this.getMostPopularPropertyType(),
            supportedViewTypes: this.supportedViewTypes.size,
            supportedPropertyTypes: this.propertyTypeConfig.size,
            allowedPropertyTypes: Array.from(this.propertyTypeConfig.entries())
                .filter(([type, config]) => config.allowInCard).length
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理视图类型
        this.supportedViewTypes.clear();
        
        // 清理属性类型
        this.propertyTypeConfig.clear();
        
        // 清理统计
        this.cardPropertiesStatistics.propertiesByType.clear();
        this.cardPropertiesStatistics.propertiesByView.clear();
        
        // 重置统计
        this.cardPropertiesStatistics = {
            totalCardFields: 0,
            totalProperties: 0,
            propertiesByType: new Map(),
            propertiesByView: new Map(),
            averagePropertiesPerCard: 0,
            mostUsedPropertyType: null
        };
    }
}

// 使用示例
const cardPropertiesManager = new CardPropertiesFieldManager();

// 创建卡片属性字段
const cardPropertiesField = cardPropertiesManager.createCardPropertiesField({
    name: 'properties',
    record: {
        data: { 
            properties: [
                { name: 'priority', type: 'selection', value: 'high' },
                { name: 'assignee', type: 'many2one', value: [1, 'John Doe'] },
                { name: 'progress', type: 'integer', value: 75 }
            ]
        },
        fields: { 
            properties: { 
                type: 'properties'
            }
        }
    }
});

// 注册自定义属性类型
cardPropertiesManager.registerPropertyType('custom_type', {
    displayName: 'Custom Type',
    icon: 'fa-star',
    allowInCard: true
});

// 获取统计信息
const stats = cardPropertiesManager.getCardPropertiesStatistics();
console.log('Card properties field statistics:', stats);
```

## 技术特点

### 1. 继承设计
- **基类继承**: 继承PropertiesField的所有功能
- **权限重写**: 重写定义权限检查逻辑
- **配置复用**: 复用基础字段的所有配置
- **功能限制**: 限制卡片视图中的定义编辑

### 2. 多视图支持
- **日历视图**: 支持calendar.properties注册
- **看板视图**: 支持kanban.properties注册
- **层级视图**: 支持hierarchy.properties注册
- **统一接口**: 提供统一的卡片属性接口

### 3. 权限控制
- **定义禁用**: 禁用属性定义编辑功能
- **值编辑**: 保留属性值编辑功能
- **视图限制**: 根据视图类型限制功能
- **安全控制**: 确保卡片视图的安全性

### 4. 简洁实现
- **最小代码**: 极简的代码实现
- **专注功能**: 专注于卡片视图的特殊需求
- **易于维护**: 易于维护和扩展
- **清晰逻辑**: 清晰的继承和重写逻辑

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **类继承**: 继承基础属性字段
- **方法重写**: 重写权限检查方法
- **功能扩展**: 扩展卡片功能

### 2. 适配器模式 (Adapter Pattern)
- **视图适配**: 适配不同卡片视图需求
- **权限适配**: 适配卡片视图权限
- **显示适配**: 适配卡片显示方式

### 3. 策略模式 (Strategy Pattern)
- **权限策略**: 卡片特定的权限策略
- **显示策略**: 卡片特定的显示策略
- **编辑策略**: 卡片特定的编辑策略

### 4. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 为基础字段添加卡片装饰
- **权限装饰**: 装饰权限控制
- **视图装饰**: 装饰视图特定功能

## 注意事项

1. **权限控制**: 确保卡片视图中的权限限制
2. **性能优化**: 避免卡片视图中的性能问题
3. **用户体验**: 提供一致的卡片交互体验
4. **视图兼容**: 保持与不同卡片视图的兼容性

## 扩展建议

1. **自定义布局**: 支持自定义卡片布局
2. **属性过滤**: 增强属性过滤功能
3. **批量编辑**: 支持批量属性编辑
4. **模板系统**: 添加属性模板系统
5. **导入导出**: 支持属性配置导入导出

该卡片属性字段为Odoo Web客户端提供了专门的卡片视图属性显示功能，通过权限限制和多视图支持确保了在不同卡片视图中的安全性和一致性。

# PropertiesField - 属性字段

## 概述

`properties_field.js` 是 Odoo Web 客户端的属性字段组件，是动态属性系统的核心组件，负责管理和显示动态属性的完整功能。该模块包含952行代码，是一个功能极其完整的属性管理组件，专门用于处理动态属性的显示、编辑、定义、排序、分组等功能，具备属性定义管理、值编辑、拖拽排序、权限控制、弹出框编辑、分组显示等特性，是Odoo动态属性系统的核心实现。

## 文件信息
- **路径**: `/web/static/src/views/fields/properties/properties_field.js`
- **行数**: 952
- **模块**: `@web/views/fields/properties/properties_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/confirmation_dialog/confirmation_dialog' // 确认对话框
'@web/core/dropdown/dropdown'           // 下拉菜单
'@web/core/dropdown/dropdown_item'      // 下拉菜单项
'@web/core/l10n/translation'            // 翻译服务
'@web/core/popover/popover_hook'        // 弹出框钩子
'@web/core/position/utils'              // 位置工具
'@web/core/registry'                    // 注册表
'@web/core/user'                        // 用户服务
'@web/core/utils/hooks'                 // 工具钩子
'@web/core/utils/objects'               // 对象工具
'@web/core/utils/sortable_owl'          // 可排序OWL工具
'@web/core/utils/strings'               // 字符串工具
'@web/model/relational_model/utils'     // 关系模型工具
'@web/views/utils'                      // 视图工具
'@web/views/fields/standard_field_props' // 标准字段属性
'@web/views/fields/properties/property_definition' // 属性定义组件
'@web/views/fields/properties/property_value' // 属性值组件
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const PropertiesField = class PropertiesField extends Component {
    static template = "web.PropertiesField";
    static components = {
        Dropdown,
        DropdownItem,
        PropertyDefinition,
        PropertyValue,
    };
    static props = {
        ...standardFieldProps,
        context: { type: Object, optional: true },
        columns: { type: Number, optional: true },
        showAddButton: { type: Boolean, optional: true },
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **上下文**: 支持context上下文传递
- **列数**: 支持columns配置显示列数
- **添加按钮**: 支持showAddButton控制添加按钮显示
- **子组件**: 集成属性定义和属性值组件

### 2. 组件初始化

```javascript
setup() {
    this.notification = useService("notification");
    this.orm = useService("orm");
    this.dialogService = useService("dialog");
    this.popover = usePopover(PropertyDefinition, {
        closeOnClickAway: this.checkPopoverClose,
        popoverClass: "o_property_field_popover",
        position: "top",
        onClose: () => this.onCloseCurrentPopover?.(),
        fixedPosition: true,
        arrow: false,
    });
    this.propertiesRef = useRef("properties");

    let currentResId;
    useRecordObserver((record) => {
        if (currentResId !== record.resId) {
            currentResId = record.resId;
            this._saveInitialPropertiesValues();
        }
    });

    const field = this.props.record.fields[this.props.name];
    this.definitionRecordField = field.definition_record;

    this.state = useState({
        canChangeDefinition: true,
        movedPropertyName: null,
        showAddButton: this.props.showAddButton,
        unfoldedSeparators: this._getUnfoldedSeparators(),
    });
}
```

**初始化功能**:
- **服务注入**: 注入通知、ORM、对话框服务
- **弹出框**: 配置属性定义弹出框
- **记录观察**: 观察记录变化保存初始值
- **定义字段**: 获取定义记录字段
- **状态管理**: 管理组件状态
- **权限控制**: 管理定义变更权限

### 3. 拖拽排序

```javascript
// sort properties
useSortable({
    enable: () => !this.props.readonly && this.state.canChangeDefinition,
    ref: this.propertiesRef,
    handle: ".o_field_property_label .oi-draggable",
    // on mono-column layout, allow to move before a separator to make the usage more fluid
    elements:
        this.renderedColumnsCount === 1
            ? "*:is(.o_property_field, .o_field_property_group_label)"
            : ".o_property_field",
    groups: ".o_property_group",
    connectGroups: true,
    cursor: "grabbing",
    onDragStart: ({ element, group }) => {
        this.propertiesRef.el.classList.add("o_property_dragging");
        element.classList.add("o_property_drag_item");
        group.classList.add("o_property_drag_group");
        // without this, if we edit a char property, move it,
        // the change will be reset when we drop the property
        document.activeElement.blur();
    },
    onDrop: async ({ parent, element, next, previous }) => {
        const from = element.getAttribute("property-name");
        let to = previous && previous.getAttribute("property-name");
        let moveBefore = false;
        if (!to && next) {
            // we move the element at the first position inside a group
            // or at the first position of a column
            if (next.classList.contains("o_field_property_group_label")) {
                // mono-column layout, move before the separator
                next = next.closest(".o_property_group");
            }
            to = next.getAttribute("property-name");
            moveBefore = !!to;
        }
        if (!to) {
            // we move in an empty group or outside of the DOM element
            // move the element at the end of the group
            const groupName = parent.getAttribute("property-name");
            const group = this.groupedPropertiesList.find(
                (group) => group.name === groupName
            );
            to = group.elements.length ? group.elements.at(-1).name : groupName;
        }
        await this.onPropertyMoveTo(from, to, moveBefore);
    },
    onDragEnd: ({ element }) => {
        this.propertiesRef.el.classList.remove("o_property_dragging");
        element.classList.remove("o_property_drag_item");
        element.closest(".o_property_group").classList.remove("o_property_drag_group");
    },
});
```

**拖拽功能**:
- **权限控制**: 根据只读和权限控制是否可拖拽
- **拖拽句柄**: 指定拖拽句柄元素
- **元素选择**: 根据列数选择可拖拽元素
- **分组支持**: 支持分组间的拖拽
- **位置计算**: 精确计算拖拽后的位置
- **样式管理**: 管理拖拽过程中的样式

### 4. 属性管理

```javascript
get propertiesList() {
    const properties = this.props.record.data[this.props.name] || [];
    return properties.filter(property => property && property.name);
}

get groupedPropertiesList() {
    const groups = [];
    let currentGroup = null;

    for (const property of this.propertiesList) {
        if (property.type === 'separator') {
            currentGroup = {
                name: property.name,
                string: property.string,
                type: 'separator',
                elements: []
            };
            groups.push(currentGroup);
        } else {
            if (!currentGroup) {
                currentGroup = {
                    name: '',
                    string: '',
                    type: 'default',
                    elements: []
                };
                groups.push(currentGroup);
            }
            currentGroup.elements.push(property);
        }
    }

    return groups;
}

get renderedColumnsCount() {
    return this.props.columns || 1;
}
```

**属性管理功能**:
- **属性列表**: 获取过滤后的属性列表
- **分组列表**: 将属性按分隔符分组
- **列数计算**: 计算渲染的列数
- **分隔符处理**: 处理分隔符类型的属性

### 5. 权限控制

```javascript
async _checkDefinitionAccess() {
    if (!this.definitionRecordField) {
        this.state.canChangeDefinition = false;
        return;
    }

    try {
        const definitionRecord = this.props.record.data[this.definitionRecordField];
        if (!definitionRecord) {
            this.state.canChangeDefinition = false;
            return;
        }

        // Check if user has write access to the definition record
        const hasAccess = await this.orm.call(
            definitionRecord.resModel,
            'check_access_rights',
            ['write'],
            { raise_exception: false }
        );

        this.state.canChangeDefinition = hasAccess;
    } catch (error) {
        console.error('Failed to check definition access:', error);
        this.state.canChangeDefinition = false;
    }
}

get canChangeDefinition() {
    return !this.props.readonly && this.state.canChangeDefinition;
}
```

**权限控制功能**:
- **定义权限**: 检查是否可以修改属性定义
- **写入权限**: 检查对定义记录的写入权限
- **只读模式**: 考虑字段的只读状态
- **错误处理**: 完善的权限检查错误处理

### 6. 属性操作

```javascript
async onPropertyCreate() {
    if (!this.canChangeDefinition) {
        return;
    }

    const newProperty = {
        name: this._generatePropertyName(),
        string: _t("New Property"),
        type: "char",
        default: false,
    };

    const properties = [...this.propertiesList, newProperty];
    await this._updateProperties(properties);

    // Open the property definition popover
    this.openPropertyDefinition = newProperty.name;
}

async onPropertyDelete(propertyName) {
    if (!this.canChangeDefinition) {
        return;
    }

    const property = this.propertiesList.find(p => p.name === propertyName);
    if (!property) {
        return;
    }

    const confirmed = await this._confirmPropertyDeletion(property);
    if (!confirmed) {
        return;
    }

    const properties = this.propertiesList.filter(p => p.name !== propertyName);
    await this._updateProperties(properties);

    this.notification.add(_t("Property deleted"), { type: "success" });
}

async onPropertyMoveTo(fromName, toName, moveBefore = false) {
    if (!this.canChangeDefinition) {
        return;
    }

    const properties = [...this.propertiesList];
    const fromIndex = properties.findIndex(p => p.name === fromName);
    const toIndex = properties.findIndex(p => p.name === toName);

    if (fromIndex === -1 || toIndex === -1) {
        return;
    }

    // Remove the property from its current position
    const [movedProperty] = properties.splice(fromIndex, 1);

    // Insert at the new position
    const insertIndex = moveBefore ? toIndex : toIndex + 1;
    properties.splice(insertIndex, 0, movedProperty);

    await this._updateProperties(properties);

    this.state.movedPropertyName = fromName;
    setTimeout(() => {
        this.state.movedPropertyName = null;
    }, 300);
}
```

**属性操作功能**:
- **创建属性**: 创建新的属性定义
- **删除属性**: 删除指定的属性
- **移动属性**: 移动属性到指定位置
- **确认对话框**: 删除前显示确认对话框
- **动画效果**: 移动后的视觉反馈

## 使用场景

### 1. 属性字段管理器

```javascript
// 属性字段管理器
class PropertiesFieldManager {
    constructor() {
        this.setupManager();
    }

    setupManager() {
        // 设置属性字段配置
        this.propertiesFieldConfig = {
            enableDefinitionManagement: true,
            enableValueEditing: true,
            enableDragAndDrop: true,
            enableGrouping: true,
            enableColumnLayout: true,
            enablePopoverEditing: true,
            enableBulkOperations: false,
            enableTemplates: false,
            enableVersioning: false,
            enableAuditLog: false
        };

        // 设置布局配置
        this.layoutConfig = {
            defaultColumns: 1,
            maxColumns: 4,
            enableResponsive: true,
            enableAutoLayout: false,
            columnBreakpoints: {
                sm: 1,
                md: 2,
                lg: 3,
                xl: 4
            },
            enableCompactMode: false
        };

        // 设置权限配置
        this.permissionConfig = {
            enableDefinitionAccess: true,
            enableValueAccess: true,
            enableFieldLevelSecurity: false,
            enableRecordLevelSecurity: false,
            defaultDefinitionAccess: false,
            defaultValueAccess: true
        };

        // 设置属性类型配置
        this.propertyTypeConfig = new Map([
            ['char', {
                name: 'Text',
                icon: 'fa-font',
                hasDefault: true,
                hasSelection: false,
                hasComodel: false,
                hasDomain: false,
                validation: ['required', 'maxLength']
            }],
            ['text', {
                name: 'Long Text',
                icon: 'fa-align-left',
                hasDefault: true,
                hasSelection: false,
                hasComodel: false,
                hasDomain: false,
                validation: ['required', 'maxLength']
            }],
            ['boolean', {
                name: 'Checkbox',
                icon: 'fa-check-square',
                hasDefault: true,
                hasSelection: false,
                hasComodel: false,
                hasDomain: false,
                validation: ['required']
            }],
            ['integer', {
                name: 'Integer',
                icon: 'fa-hashtag',
                hasDefault: true,
                hasSelection: false,
                hasComodel: false,
                hasDomain: false,
                validation: ['required', 'min', 'max']
            }],
            ['float', {
                name: 'Decimal',
                icon: 'fa-calculator',
                hasDefault: true,
                hasSelection: false,
                hasComodel: false,
                hasDomain: false,
                validation: ['required', 'min', 'max', 'precision']
            }],
            ['date', {
                name: 'Date',
                icon: 'fa-calendar-day',
                hasDefault: true,
                hasSelection: false,
                hasComodel: false,
                hasDomain: false,
                validation: ['required', 'dateRange']
            }],
            ['datetime', {
                name: 'Date & Time',
                icon: 'fa-clock',
                hasDefault: true,
                hasSelection: false,
                hasComodel: false,
                hasDomain: false,
                validation: ['required', 'dateRange']
            }],
            ['selection', {
                name: 'Selection',
                icon: 'fa-list',
                hasDefault: false,
                hasSelection: true,
                hasComodel: false,
                hasDomain: false,
                validation: ['required', 'selectionOptions']
            }],
            ['tags', {
                name: 'Tags',
                icon: 'fa-tags',
                hasDefault: false,
                hasSelection: true,
                hasComodel: false,
                hasDomain: false,
                validation: ['required']
            }],
            ['many2one', {
                name: 'Many2one',
                icon: 'fa-link',
                hasDefault: false,
                hasSelection: false,
                hasComodel: true,
                hasDomain: true,
                validation: ['required', 'comodel']
            }],
            ['many2many', {
                name: 'Many2many',
                icon: 'fa-sitemap',
                hasDefault: false,
                hasSelection: false,
                hasComodel: true,
                hasDomain: true,
                validation: ['required', 'comodel']
            }],
            ['separator', {
                name: 'Separator',
                icon: 'fa-minus',
                hasDefault: false,
                hasSelection: false,
                hasComodel: false,
                hasDomain: false,
                validation: []
            }]
        ]);

        // 设置属性统计
        this.propertiesStatistics = {
            totalPropertiesFields: 0,
            totalProperties: 0,
            propertiesByType: new Map(),
            propertiesByGroup: new Map(),
            averagePropertiesPerField: 0,
            mostUsedType: null,
            definitionChanges: 0,
            valueChanges: 0,
            dragOperations: 0
        };

        this.initializePropertiesSystem();
    }

    // 初始化属性系统
    initializePropertiesSystem() {
        // 创建增强的属性字段
        this.createEnhancedPropertiesField();

        // 设置布局系统
        this.setupLayoutSystem();

        // 设置权限系统
        this.setupPermissionSystem();

        // 设置类型系统
        this.setupTypeSystem();
    }
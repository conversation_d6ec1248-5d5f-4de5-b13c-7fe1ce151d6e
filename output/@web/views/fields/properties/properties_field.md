# PropertiesField - 属性字段

## 概述

`properties_field.js` 是 Odoo Web 客户端的属性字段组件，是动态属性系统的核心组件，负责管理和显示动态属性的完整功能。该模块包含952行代码，是一个功能极其完整的属性管理组件，专门用于处理动态属性的显示、编辑、定义、排序、分组等功能，具备属性定义管理、值编辑、拖拽排序、权限控制、弹出框编辑、分组显示等特性，是Odoo动态属性系统的核心实现。

## 文件信息
- **路径**: `/web/static/src/views/fields/properties/properties_field.js`
- **行数**: 952
- **模块**: `@web/views/fields/properties/properties_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/confirmation_dialog/confirmation_dialog' // 确认对话框
'@web/core/dropdown/dropdown'           // 下拉菜单
'@web/core/dropdown/dropdown_item'      // 下拉菜单项
'@web/core/l10n/translation'            // 翻译服务
'@web/core/popover/popover_hook'        // 弹出框钩子
'@web/core/position/utils'              // 位置工具
'@web/core/registry'                    // 注册表
'@web/core/user'                        // 用户服务
'@web/core/utils/hooks'                 // 工具钩子
'@web/core/utils/objects'               // 对象工具
'@web/core/utils/sortable_owl'          // 可排序OWL工具
'@web/core/utils/strings'               // 字符串工具
'@web/model/relational_model/utils'     // 关系模型工具
'@web/views/utils'                      // 视图工具
'@web/views/fields/standard_field_props' // 标准字段属性
'@web/views/fields/properties/property_definition' // 属性定义组件
'@web/views/fields/properties/property_value' // 属性值组件
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const PropertiesField = class PropertiesField extends Component {
    static template = "web.PropertiesField";
    static components = {
        Dropdown,
        DropdownItem,
        PropertyDefinition,
        PropertyValue,
    };
    static props = {
        ...standardFieldProps,
        context: { type: Object, optional: true },
        columns: { type: Number, optional: true },
        showAddButton: { type: Boolean, optional: true },
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **上下文**: 支持context上下文传递
- **列数**: 支持columns配置显示列数
- **添加按钮**: 支持showAddButton控制添加按钮显示
- **子组件**: 集成属性定义和属性值组件

### 2. 组件初始化

```javascript
setup() {
    this.notification = useService("notification");
    this.orm = useService("orm");
    this.dialogService = useService("dialog");
    this.popover = usePopover(PropertyDefinition, {
        closeOnClickAway: this.checkPopoverClose,
        popoverClass: "o_property_field_popover",
        position: "top",
        onClose: () => this.onCloseCurrentPopover?.(),
        fixedPosition: true,
        arrow: false,
    });
    this.propertiesRef = useRef("properties");

    let currentResId;
    useRecordObserver((record) => {
        if (currentResId !== record.resId) {
            currentResId = record.resId;
            this._saveInitialPropertiesValues();
        }
    });

    const field = this.props.record.fields[this.props.name];
    this.definitionRecordField = field.definition_record;

    this.state = useState({
        canChangeDefinition: true,
        movedPropertyName: null,
        showAddButton: this.props.showAddButton,
        unfoldedSeparators: this._getUnfoldedSeparators(),
    });
}
```

**初始化功能**:
- **服务注入**: 注入通知、ORM、对话框服务
- **弹出框**: 配置属性定义弹出框
- **记录观察**: 观察记录变化保存初始值
- **定义字段**: 获取定义记录字段
- **状态管理**: 管理组件状态
- **权限控制**: 管理定义变更权限

### 3. 拖拽排序

```javascript
// sort properties
useSortable({
    enable: () => !this.props.readonly && this.state.canChangeDefinition,
    ref: this.propertiesRef,
    handle: ".o_field_property_label .oi-draggable",
    // on mono-column layout, allow to move before a separator to make the usage more fluid
    elements:
        this.renderedColumnsCount === 1
            ? "*:is(.o_property_field, .o_field_property_group_label)"
            : ".o_property_field",
    groups: ".o_property_group",
    connectGroups: true,
    cursor: "grabbing",
    onDragStart: ({ element, group }) => {
        this.propertiesRef.el.classList.add("o_property_dragging");
        element.classList.add("o_property_drag_item");
        group.classList.add("o_property_drag_group");
        // without this, if we edit a char property, move it,
        // the change will be reset when we drop the property
        document.activeElement.blur();
    },
    onDrop: async ({ parent, element, next, previous }) => {
        const from = element.getAttribute("property-name");
        let to = previous && previous.getAttribute("property-name");
        let moveBefore = false;
        if (!to && next) {
            // we move the element at the first position inside a group
            // or at the first position of a column
            if (next.classList.contains("o_field_property_group_label")) {
                // mono-column layout, move before the separator
                next = next.closest(".o_property_group");
            }
            to = next.getAttribute("property-name");
            moveBefore = !!to;
        }
        if (!to) {
            // we move in an empty group or outside of the DOM element
            // move the element at the end of the group
            const groupName = parent.getAttribute("property-name");
            const group = this.groupedPropertiesList.find(
                (group) => group.name === groupName
            );
            to = group.elements.length ? group.elements.at(-1).name : groupName;
        }
        await this.onPropertyMoveTo(from, to, moveBefore);
    },
    onDragEnd: ({ element }) => {
        this.propertiesRef.el.classList.remove("o_property_dragging");
        element.classList.remove("o_property_drag_item");
        element.closest(".o_property_group").classList.remove("o_property_drag_group");
    },
});
```

**拖拽功能**:
- **权限控制**: 根据只读和权限控制是否可拖拽
- **拖拽句柄**: 指定拖拽句柄元素
- **元素选择**: 根据列数选择可拖拽元素
- **分组支持**: 支持分组间的拖拽
- **位置计算**: 精确计算拖拽后的位置
- **样式管理**: 管理拖拽过程中的样式

### 4. 属性管理

```javascript
get propertiesList() {
    const properties = this.props.record.data[this.props.name] || [];
    return properties.filter(property => property && property.name);
}

get groupedPropertiesList() {
    const groups = [];
    let currentGroup = null;

    for (const property of this.propertiesList) {
        if (property.type === 'separator') {
            currentGroup = {
                name: property.name,
                string: property.string,
                type: 'separator',
                elements: []
            };
            groups.push(currentGroup);
        } else {
            if (!currentGroup) {
                currentGroup = {
                    name: '',
                    string: '',
                    type: 'default',
                    elements: []
                };
                groups.push(currentGroup);
            }
            currentGroup.elements.push(property);
        }
    }

    return groups;
}

get renderedColumnsCount() {
    return this.props.columns || 1;
}
```

**属性管理功能**:
- **属性列表**: 获取过滤后的属性列表
- **分组列表**: 将属性按分隔符分组
- **列数计算**: 计算渲染的列数
- **分隔符处理**: 处理分隔符类型的属性

### 5. 权限控制

```javascript
async _checkDefinitionAccess() {
    if (!this.definitionRecordField) {
        this.state.canChangeDefinition = false;
        return;
    }

    try {
        const definitionRecord = this.props.record.data[this.definitionRecordField];
        if (!definitionRecord) {
            this.state.canChangeDefinition = false;
            return;
        }

        // Check if user has write access to the definition record
        const hasAccess = await this.orm.call(
            definitionRecord.resModel,
            'check_access_rights',
            ['write'],
            { raise_exception: false }
        );

        this.state.canChangeDefinition = hasAccess;
    } catch (error) {
        console.error('Failed to check definition access:', error);
        this.state.canChangeDefinition = false;
    }
}

get canChangeDefinition() {
    return !this.props.readonly && this.state.canChangeDefinition;
}
```

**权限控制功能**:
- **定义权限**: 检查是否可以修改属性定义
- **写入权限**: 检查对定义记录的写入权限
- **只读模式**: 考虑字段的只读状态
- **错误处理**: 完善的权限检查错误处理

### 6. 属性操作

```javascript
async onPropertyCreate() {
    if (!this.canChangeDefinition) {
        return;
    }

    const newProperty = {
        name: this._generatePropertyName(),
        string: _t("New Property"),
        type: "char",
        default: false,
    };

    const properties = [...this.propertiesList, newProperty];
    await this._updateProperties(properties);

    // Open the property definition popover
    this.openPropertyDefinition = newProperty.name;
}

async onPropertyDelete(propertyName) {
    if (!this.canChangeDefinition) {
        return;
    }

    const property = this.propertiesList.find(p => p.name === propertyName);
    if (!property) {
        return;
    }

    const confirmed = await this._confirmPropertyDeletion(property);
    if (!confirmed) {
        return;
    }

    const properties = this.propertiesList.filter(p => p.name !== propertyName);
    await this._updateProperties(properties);

    this.notification.add(_t("Property deleted"), { type: "success" });
}

async onPropertyMoveTo(fromName, toName, moveBefore = false) {
    if (!this.canChangeDefinition) {
        return;
    }

    const properties = [...this.propertiesList];
    const fromIndex = properties.findIndex(p => p.name === fromName);
    const toIndex = properties.findIndex(p => p.name === toName);

    if (fromIndex === -1 || toIndex === -1) {
        return;
    }

    // Remove the property from its current position
    const [movedProperty] = properties.splice(fromIndex, 1);

    // Insert at the new position
    const insertIndex = moveBefore ? toIndex : toIndex + 1;
    properties.splice(insertIndex, 0, movedProperty);

    await this._updateProperties(properties);

    this.state.movedPropertyName = fromName;
    setTimeout(() => {
        this.state.movedPropertyName = null;
    }, 300);
}
```

**属性操作功能**:
- **创建属性**: 创建新的属性定义
- **删除属性**: 删除指定的属性
- **移动属性**: 移动属性到指定位置
- **确认对话框**: 删除前显示确认对话框
- **动画效果**: 移动后的视觉反馈

## 使用场景

### 1. 属性字段管理器

```javascript
// 属性字段管理器
class PropertiesFieldManager {
    constructor() {
        this.setupManager();
    }

    setupManager() {
        // 设置属性字段配置
        this.propertiesFieldConfig = {
            enableDefinitionManagement: true,
            enableValueEditing: true,
            enableDragAndDrop: true,
            enableGrouping: true,
            enableColumnLayout: true,
            enablePopoverEditing: true,
            enableBulkOperations: false,
            enableTemplates: false,
            enableVersioning: false,
            enableAuditLog: false
        };

        // 设置布局配置
        this.layoutConfig = {
            defaultColumns: 1,
            maxColumns: 4,
            enableResponsive: true,
            enableAutoLayout: false,
            columnBreakpoints: {
                sm: 1,
                md: 2,
                lg: 3,
                xl: 4
            },
            enableCompactMode: false
        };

        // 设置权限配置
        this.permissionConfig = {
            enableDefinitionAccess: true,
            enableValueAccess: true,
            enableFieldLevelSecurity: false,
            enableRecordLevelSecurity: false,
            defaultDefinitionAccess: false,
            defaultValueAccess: true
        };

        // 设置属性类型配置
        this.propertyTypeConfig = new Map([
            ['char', {
                name: 'Text',
                icon: 'fa-font',
                hasDefault: true,
                hasSelection: false,
                hasComodel: false,
                hasDomain: false,
                validation: ['required', 'maxLength']
            }],
            ['text', {
                name: 'Long Text',
                icon: 'fa-align-left',
                hasDefault: true,
                hasSelection: false,
                hasComodel: false,
                hasDomain: false,
                validation: ['required', 'maxLength']
            }],
            ['boolean', {
                name: 'Checkbox',
                icon: 'fa-check-square',
                hasDefault: true,
                hasSelection: false,
                hasComodel: false,
                hasDomain: false,
                validation: ['required']
            }],
            ['integer', {
                name: 'Integer',
                icon: 'fa-hashtag',
                hasDefault: true,
                hasSelection: false,
                hasComodel: false,
                hasDomain: false,
                validation: ['required', 'min', 'max']
            }],
            ['float', {
                name: 'Decimal',
                icon: 'fa-calculator',
                hasDefault: true,
                hasSelection: false,
                hasComodel: false,
                hasDomain: false,
                validation: ['required', 'min', 'max', 'precision']
            }],
            ['date', {
                name: 'Date',
                icon: 'fa-calendar-day',
                hasDefault: true,
                hasSelection: false,
                hasComodel: false,
                hasDomain: false,
                validation: ['required', 'dateRange']
            }],
            ['datetime', {
                name: 'Date & Time',
                icon: 'fa-clock',
                hasDefault: true,
                hasSelection: false,
                hasComodel: false,
                hasDomain: false,
                validation: ['required', 'dateRange']
            }],
            ['selection', {
                name: 'Selection',
                icon: 'fa-list',
                hasDefault: false,
                hasSelection: true,
                hasComodel: false,
                hasDomain: false,
                validation: ['required', 'selectionOptions']
            }],
            ['tags', {
                name: 'Tags',
                icon: 'fa-tags',
                hasDefault: false,
                hasSelection: true,
                hasComodel: false,
                hasDomain: false,
                validation: ['required']
            }],
            ['many2one', {
                name: 'Many2one',
                icon: 'fa-link',
                hasDefault: false,
                hasSelection: false,
                hasComodel: true,
                hasDomain: true,
                validation: ['required', 'comodel']
            }],
            ['many2many', {
                name: 'Many2many',
                icon: 'fa-sitemap',
                hasDefault: false,
                hasSelection: false,
                hasComodel: true,
                hasDomain: true,
                validation: ['required', 'comodel']
            }],
            ['separator', {
                name: 'Separator',
                icon: 'fa-minus',
                hasDefault: false,
                hasSelection: false,
                hasComodel: false,
                hasDomain: false,
                validation: []
            }]
        ]);

        // 设置属性统计
        this.propertiesStatistics = {
            totalPropertiesFields: 0,
            totalProperties: 0,
            propertiesByType: new Map(),
            propertiesByGroup: new Map(),
            averagePropertiesPerField: 0,
            mostUsedType: null,
            definitionChanges: 0,
            valueChanges: 0,
            dragOperations: 0
        };

        this.initializePropertiesSystem();
    }

    // 初始化属性系统
    initializePropertiesSystem() {
        // 创建增强的属性字段
        this.createEnhancedPropertiesField();

        // 设置布局系统
        this.setupLayoutSystem();

        // 设置权限系统
        this.setupPermissionSystem();

        // 设置类型系统
        this.setupTypeSystem();
    }

    // 创建增强的属性字段
    createEnhancedPropertiesField() {
        const originalField = PropertiesField;

        this.EnhancedPropertiesField = class extends originalField {
            setup() {
                super.setup();

                // 添加增强功能
                this.addEnhancedFeatures();

                // 添加布局功能
                this.addLayoutFeatures();

                // 添加权限功能
                this.addPermissionFeatures();
            }

            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    ...this.state,
                    layoutMode: 'default',
                    compactMode: false,
                    selectedProperties: new Set(),
                    clipboardProperty: null,
                    undoStack: [],
                    redoStack: [],
                    searchQuery: '',
                    filteredProperties: [],
                    groupCollapsed: new Map(),
                    lastOperation: null
                };

                // 添加增强方法
                this.addEnhancedMethods();
            }

            addEnhancedMethods() {
                // 增强的属性创建
                this.enhancedPropertyCreate = async (propertyType = 'char', groupName = null) => {
                    if (!this.canChangeDefinition) {
                        this.notification.add(_t("No permission to create properties"), { type: "warning" });
                        return null;
                    }

                    const typeConfig = this.propertyTypeConfig.get(propertyType);
                    if (!typeConfig) {
                        this.notification.add(_t("Invalid property type"), { type: "error" });
                        return null;
                    }

                    const newProperty = {
                        name: this.generatePropertyName(),
                        string: _t("New %s", typeConfig.name),
                        type: propertyType,
                        default: typeConfig.hasDefault ? false : undefined,
                        group: groupName || '',
                        required: false,
                        readonly: false
                    };

                    // 添加类型特定属性
                    if (typeConfig.hasSelection) {
                        newProperty.selection = [];
                    }
                    if (typeConfig.hasComodel) {
                        newProperty.comodel = null;
                    }
                    if (typeConfig.hasDomain) {
                        newProperty.domain = [];
                    }

                    const properties = [...this.propertiesList, newProperty];
                    await this.updateProperties(properties);

                    // 记录操作
                    this.recordOperation('create', newProperty);

                    // 打开属性定义
                    this.openPropertyDefinition = newProperty.name;

                    return newProperty;
                };

                // 增强的属性删除
                this.enhancedPropertyDelete = async (propertyName) => {
                    if (!this.canChangeDefinition) {
                        this.notification.add(_t("No permission to delete properties"), { type: "warning" });
                        return false;
                    }

                    const property = this.propertiesList.find(p => p.name === propertyName);
                    if (!property) {
                        return false;
                    }

                    // 检查是否有值
                    const hasValues = await this.checkPropertyHasValues(propertyName);

                    let confirmed = true;
                    if (hasValues) {
                        confirmed = await this.confirmPropertyDeletion(property);
                    }

                    if (!confirmed) {
                        return false;
                    }

                    const properties = this.propertiesList.filter(p => p.name !== propertyName);
                    await this.updateProperties(properties);

                    // 记录操作
                    this.recordOperation('delete', property);

                    this.notification.add(_t("Property '%s' deleted", property.string), { type: "success" });
                    return true;
                };

                // 增强的属性复制
                this.enhancedPropertyCopy = async (propertyName) => {
                    if (!this.canChangeDefinition) {
                        this.notification.add(_t("No permission to copy properties"), { type: "warning" });
                        return null;
                    }

                    const property = this.propertiesList.find(p => p.name === propertyName);
                    if (!property) {
                        return null;
                    }

                    const copiedProperty = {
                        ...property,
                        name: this.generatePropertyName(),
                        string: _t("Copy of %s", property.string)
                    };

                    const properties = [...this.propertiesList, copiedProperty];
                    await this.updateProperties(properties);

                    // 记录操作
                    this.recordOperation('copy', copiedProperty);

                    this.notification.add(_t("Property copied"), { type: "success" });
                    return copiedProperty;
                };

                // 批量操作
                this.batchPropertyOperations = async (operations) => {
                    if (!this.canChangeDefinition) {
                        this.notification.add(_t("No permission for batch operations"), { type: "warning" });
                        return false;
                    }

                    let properties = [...this.propertiesList];
                    const results = [];

                    for (const operation of operations) {
                        try {
                            switch (operation.type) {
                                case 'create':
                                    const newProperty = await this.createPropertyFromTemplate(operation.template);
                                    properties.push(newProperty);
                                    results.push({ success: true, property: newProperty });
                                    break;

                                case 'delete':
                                    properties = properties.filter(p => p.name !== operation.propertyName);
                                    results.push({ success: true, propertyName: operation.propertyName });
                                    break;

                                case 'update':
                                    const index = properties.findIndex(p => p.name === operation.propertyName);
                                    if (index >= 0) {
                                        properties[index] = { ...properties[index], ...operation.changes };
                                        results.push({ success: true, property: properties[index] });
                                    }
                                    break;

                                default:
                                    results.push({ success: false, error: 'Unknown operation type' });
                            }
                        } catch (error) {
                            results.push({ success: false, error: error.message });
                        }
                    }

                    await this.updateProperties(properties);

                    // 记录批量操作
                    this.recordOperation('batch', { operations, results });

                    const successCount = results.filter(r => r.success).length;
                    this.notification.add(_t("%s operations completed", successCount), { type: "success" });

                    return results;
                };

                // 属性搜索和过滤
                this.searchProperties = (query) => {
                    this.enhancedState.searchQuery = query;

                    if (!query.trim()) {
                        this.enhancedState.filteredProperties = this.propertiesList;
                        return;
                    }

                    const filtered = this.propertiesList.filter(property => {
                        return property.string.toLowerCase().includes(query.toLowerCase()) ||
                               property.name.toLowerCase().includes(query.toLowerCase()) ||
                               property.type.toLowerCase().includes(query.toLowerCase());
                    });

                    this.enhancedState.filteredProperties = filtered;
                };

                // 分组管理
                this.createPropertyGroup = async (groupName, groupString) => {
                    if (!this.canChangeDefinition) {
                        return false;
                    }

                    const separator = {
                        name: groupName,
                        string: groupString,
                        type: 'separator'
                    };

                    const properties = [...this.propertiesList, separator];
                    await this.updateProperties(properties);

                    this.recordOperation('createGroup', separator);
                    return true;
                };

                // 切换分组折叠状态
                this.toggleGroupCollapse = (groupName) => {
                    const isCollapsed = this.enhancedState.groupCollapsed.get(groupName) || false;
                    this.enhancedState.groupCollapsed.set(groupName, !isCollapsed);
                };

                // 生成属性名称
                this.generatePropertyName = () => {
                    const existingNames = new Set(this.propertiesList.map(p => p.name));
                    let counter = 1;
                    let name;

                    do {
                        name = `x_property_${counter}`;
                        counter++;
                    } while (existingNames.has(name));

                    return name;
                };

                // 检查属性是否有值
                this.checkPropertyHasValues = async (propertyName) => {
                    try {
                        const model = this.props.record.resModel;
                        const domain = [[this.props.name, '!=', false]];

                        const records = await this.orm.searchRead(model, domain, [this.props.name], { limit: 1 });

                        for (const record of records) {
                            const properties = record[this.props.name] || [];
                            const property = properties.find(p => p.name === propertyName);
                            if (property && property.value !== false && property.value !== null && property.value !== undefined) {
                                return true;
                            }
                        }

                        return false;
                    } catch (error) {
                        console.warn('Failed to check property values:', error);
                        return false;
                    }
                };

                // 确认属性删除
                this.confirmPropertyDeletion = async (property) => {
                    return new Promise((resolve) => {
                        this.dialogService.add(ConfirmationDialog, {
                            title: _t("Delete Property"),
                            body: _t("Are you sure you want to delete the property '%s'? This action cannot be undone and will remove all values for this property.", property.string),
                            confirm: () => resolve(true),
                            cancel: () => resolve(false),
                        });
                    });
                };

                // 更新属性
                this.updateProperties = async (properties) => {
                    // 保存到撤销栈
                    this.enhancedState.undoStack.push([...this.propertiesList]);
                    if (this.enhancedState.undoStack.length > 50) {
                        this.enhancedState.undoStack.shift();
                    }
                    this.enhancedState.redoStack = [];

                    // 更新记录
                    await this.props.record.update({ [this.props.name]: properties });

                    // 记录统计
                    this.recordPropertiesUpdate(properties);
                };

                // 撤销操作
                this.undoOperation = async () => {
                    if (this.enhancedState.undoStack.length === 0) {
                        return false;
                    }

                    const previousState = this.enhancedState.undoStack.pop();
                    this.enhancedState.redoStack.push([...this.propertiesList]);

                    await this.props.record.update({ [this.props.name]: previousState });

                    this.notification.add(_t("Operation undone"), { type: "info" });
                    return true;
                };

                // 重做操作
                this.redoOperation = async () => {
                    if (this.enhancedState.redoStack.length === 0) {
                        return false;
                    }

                    const nextState = this.enhancedState.redoStack.pop();
                    this.enhancedState.undoStack.push([...this.propertiesList]);

                    await this.props.record.update({ [this.props.name]: nextState });

                    this.notification.add(_t("Operation redone"), { type: "info" });
                    return true;
                };

                // 记录操作
                this.recordOperation = (type, data) => {
                    this.enhancedState.lastOperation = {
                        type: type,
                        data: data,
                        timestamp: new Date()
                    };

                    // 更新统计
                    if (type === 'create' || type === 'delete' || type === 'copy') {
                        this.propertiesStatistics.definitionChanges++;
                    }
                    if (type === 'move') {
                        this.propertiesStatistics.dragOperations++;
                    }
                };

                // 记录属性更新统计
                this.recordPropertiesUpdate = (properties) => {
                    this.propertiesStatistics.totalProperties = properties.length;

                    // 记录按类型分布
                    this.propertiesStatistics.propertiesByType.clear();
                    properties.forEach(property => {
                        const count = this.propertiesStatistics.propertiesByType.get(property.type) || 0;
                        this.propertiesStatistics.propertiesByType.set(property.type, count + 1);
                    });

                    // 记录按分组分布
                    this.propertiesStatistics.propertiesByGroup.clear();
                    const groups = this.groupedPropertiesList;
                    groups.forEach(group => {
                        this.propertiesStatistics.propertiesByGroup.set(group.name || 'default', group.elements.length);
                    });

                    // 更新平均值
                    this.updateAverageProperties();
                };

                // 更新平均属性数
                this.updateAverageProperties = () => {
                    if (this.propertiesStatistics.totalPropertiesFields > 0) {
                        this.propertiesStatistics.averagePropertiesPerField =
                            this.propertiesStatistics.totalProperties / this.propertiesStatistics.totalPropertiesFields;
                    }
                };

                // 获取属性信息
                this.getPropertiesInfo = () => {
                    return {
                        totalProperties: this.propertiesList.length,
                        groupCount: this.groupedPropertiesList.length,
                        typeDistribution: Object.fromEntries(this.propertiesStatistics.propertiesByType),
                        canChangeDefinition: this.canChangeDefinition,
                        layoutMode: this.enhancedState.layoutMode,
                        compactMode: this.enhancedState.compactMode,
                        searchQuery: this.enhancedState.searchQuery,
                        filteredCount: this.enhancedState.filteredProperties.length,
                        undoAvailable: this.enhancedState.undoStack.length > 0,
                        redoAvailable: this.enhancedState.redoStack.length > 0,
                        lastOperation: this.enhancedState.lastOperation
                    };
                };
            }

            addLayoutFeatures() {
                // 布局功能
                this.layoutManager = {
                    enabled: this.propertiesFieldConfig.enableColumnLayout,
                    setMode: (mode) => this.enhancedState.layoutMode = mode,
                    setCompact: (compact) => this.enhancedState.compactMode = compact,
                    getColumns: () => this.renderedColumnsCount,
                    setColumns: (columns) => this.props.columns = columns
                };
            }

            addPermissionFeatures() {
                // 权限功能
                this.permissionManager = {
                    enabled: this.permissionConfig.enableDefinitionAccess,
                    canChangeDefinition: () => this.canChangeDefinition,
                    canEditValues: () => !this.props.readonly,
                    checkAccess: (operation) => this.checkOperationAccess(operation)
                };
            }

            // 重写原始方法
            async onPropertyCreate() {
                return this.enhancedPropertyCreate();
            }

            async onPropertyDelete(propertyName) {
                return this.enhancedPropertyDelete(propertyName);
            }
        };
    }

    // 设置布局系统
    setupLayoutSystem() {
        this.layoutSystemConfig = {
            enabled: this.propertiesFieldConfig.enableColumnLayout,
            config: this.layoutConfig
        };
    }

    // 设置权限系统
    setupPermissionSystem() {
        this.permissionSystemConfig = {
            enabled: this.permissionConfig.enableDefinitionAccess,
            config: this.permissionConfig
        };
    }

    // 设置类型系统
    setupTypeSystem() {
        this.typeSystemConfig = {
            enabled: true,
            types: this.propertyTypeConfig
        };
    }

    // 创建属性字段
    createPropertiesField(props) {
        const field = new this.EnhancedPropertiesField(props);
        this.propertiesStatistics.totalPropertiesFields++;
        return field;
    }

    // 注册属性类型
    registerPropertyType(type, config) {
        this.propertyTypeConfig.set(type, config);
    }

    // 获取最受欢迎的属性类型
    getMostUsedPropertyType() {
        let maxCount = 0;
        let mostUsedType = null;

        for (const [type, count] of this.propertiesStatistics.propertiesByType.entries()) {
            if (count > maxCount) {
                maxCount = count;
                mostUsedType = type;
            }
        }

        this.propertiesStatistics.mostUsedType = mostUsedType;
        return mostUsedType;
    }

    // 获取属性统计
    getPropertiesStatistics() {
        return {
            ...this.propertiesStatistics,
            mostUsedType: this.getMostUsedPropertyType(),
            typeVariety: this.propertiesStatistics.propertiesByType.size,
            groupVariety: this.propertiesStatistics.propertiesByGroup.size,
            definitionChangeRate: this.propertiesStatistics.definitionChanges / Math.max(this.propertiesStatistics.totalPropertiesFields, 1),
            dragOperationRate: this.propertiesStatistics.dragOperations / Math.max(this.propertiesStatistics.totalPropertiesFields, 1),
            supportedTypeCount: this.propertyTypeConfig.size
        };
    }

    // 销毁管理器
    destroy() {
        // 清理属性类型
        this.propertyTypeConfig.clear();

        // 清理统计
        this.propertiesStatistics.propertiesByType.clear();
        this.propertiesStatistics.propertiesByGroup.clear();

        // 重置统计
        this.propertiesStatistics = {
            totalPropertiesFields: 0,
            totalProperties: 0,
            propertiesByType: new Map(),
            propertiesByGroup: new Map(),
            averagePropertiesPerField: 0,
            mostUsedType: null,
            definitionChanges: 0,
            valueChanges: 0,
            dragOperations: 0
        };
    }
}

// 使用示例
const propertiesManager = new PropertiesFieldManager();

// 创建属性字段
const propertiesField = propertiesManager.createPropertiesField({
    name: 'properties',
    record: {
        data: {
            properties: [
                { name: 'priority', string: 'Priority', type: 'selection', selection: [['low', 'Low'], ['high', 'High']] },
                { name: 'description', string: 'Description', type: 'text' },
                { name: 'due_date', string: 'Due Date', type: 'date' }
            ]
        },
        fields: {
            properties: {
                type: 'properties',
                definition_record: 'definition_record_field'
            }
        }
    },
    columns: 2,
    showAddButton: true
});

// 注册自定义属性类型
propertiesManager.registerPropertyType('custom_type', {
    name: 'Custom Type',
    icon: 'fa-star',
    hasDefault: true,
    hasSelection: false,
    hasComodel: false,
    hasDomain: false,
    validation: ['required']
});

// 获取统计信息
const stats = propertiesManager.getPropertiesStatistics();
console.log('Properties field statistics:', stats);
```

### 7. 字段注册

```javascript
const propertiesField = {
    component: PropertiesField,
    displayName: _t("Properties"),
    supportedTypes: ["properties"],
    extractProps: ({ attrs, options }) => ({
        context: attrs.context,
        columns: parseInt(attrs.columns) || 1,
        showAddButton: options.showAddButton !== false,
    }),
};

registry.category("fields").add("properties", propertiesField);
```

**注册功能**:
- **组件注册**: 注册属性字段组件
- **显示名称**: 设置为"Properties"
- **类型支持**: 支持properties类型
- **属性提取**: 提取上下文、列数、添加按钮等属性

## 技术特点

### 1. 复杂状态管理
- **多层状态**: 管理组件、属性、分组等多层状态
- **状态同步**: 保持UI状态与数据状态同步
- **状态持久化**: 支持状态的持久化存储
- **状态回滚**: 支持撤销重做操作

### 2. 高级交互
- **拖拽排序**: 复杂的拖拽排序逻辑
- **弹出框编辑**: 弹出框中的属性定义编辑
- **分组管理**: 属性的分组显示和管理
- **批量操作**: 支持批量属性操作

### 3. 权限控制
- **细粒度权限**: 定义级和值级的权限控制
- **动态权限**: 动态检查和更新权限
- **权限缓存**: 缓存权限检查结果
- **权限反馈**: 清晰的权限反馈机制

### 4. 性能优化
- **懒加载**: 按需加载属性定义
- **虚拟滚动**: 大量属性时的虚拟滚动
- **缓存机制**: 缓存属性定义和值
- **批量更新**: 批量更新减少重渲染

## 设计模式

### 1. 组合模式 (Composite Pattern)
- **属性组合**: 组合不同类型的属性
- **分组组合**: 组合属性分组
- **组件组合**: 组合多个子组件

### 2. 命令模式 (Command Pattern)
- **操作命令**: 封装属性操作为命令
- **撤销重做**: 实现撤销重做功能
- **批量命令**: 支持批量命令执行

### 3. 观察者模式 (Observer Pattern)
- **状态观察**: 观察属性状态变化
- **记录观察**: 观察记录数据变化
- **权限观察**: 观察权限状态变化

### 4. 策略模式 (Strategy Pattern)
- **布局策略**: 不同的布局显示策略
- **编辑策略**: 不同的编辑模式策略
- **验证策略**: 不同的验证策略

### 5. 工厂模式 (Factory Pattern)
- **属性工厂**: 创建不同类型的属性
- **组件工厂**: 创建不同的子组件
- **验证器工厂**: 创建不同的验证器

## 注意事项

1. **性能考虑**: 大量属性时的性能优化
2. **内存管理**: 避免内存泄漏和过度使用
3. **用户体验**: 提供流畅的交互体验
4. **数据一致性**: 确保属性数据的一致性
5. **权限安全**: 确保权限控制的安全性

## 扩展建议

1. **模板系统**: 提供属性模板功能
2. **导入导出**: 支持属性配置的导入导出
3. **版本控制**: 添加属性定义的版本控制
4. **审计日志**: 记录属性变更的审计日志
5. **API集成**: 提供属性管理的API接口
6. **移动端优化**: 优化移动端的显示和交互
7. **国际化**: 增强多语言支持
8. **主题定制**: 支持主题和样式定制

该属性字段为Odoo Web客户端提供了完整的动态属性管理功能，是整个动态属性系统的核心组件。通过复杂的状态管理、高级交互功能和细粒度的权限控制，确保了动态属性系统的强大功能和良好的用户体验。
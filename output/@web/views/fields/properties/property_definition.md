# PropertyDefinition - 属性定义组件

## 概述

`property_definition.js` 是 Odoo Web 客户端的属性定义组件，负责管理动态属性的定义、配置和编辑。该模块包含419行代码，是一个功能完整的属性管理组件，专门用于定义和配置动态属性，具备属性类型选择、默认值设置、域配置、权限控制等特性，是动态属性系统的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/properties/property_definition.js`
- **行数**: 419
- **模块**: `@web/views/fields/properties/property_definition`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'            // 翻译服务
'@web/views/fields/properties/property_value' // 属性值组件
'@web/core/checkbox/checkbox'           // 复选框组件
'@web/core/domain_selector/domain_selector' // 域选择器
'@web/core/domain'                      // 域工具
'@web/core/dropdown/dropdown'           // 下拉菜单
'@web/core/dropdown/dropdown_item'      // 下拉菜单项
'@web/core/model_selector/model_selector' // 模型选择器
'@web/views/fields/relational_utils'    // 关系字段工具
'@web/core/utils/hooks'                 // 工具钩子
'@web/views/fields/properties/property_definition_selection' // 属性定义选择
'@web/views/fields/properties/property_tags' // 属性标签
'@web/views/view_dialogs/select_create_dialog' // 选择创建对话框
'@web/views/utils'                      // 视图工具
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const PropertyDefinition = class PropertyDefinition extends Component {
    static template = "web.PropertyDefinition";
    static components = {
        CheckBox,
        DomainSelector,
        Dropdown,
        DropdownItem,
        PropertyValue,
        Many2XAutocomplete,
        ModelSelector,
        PropertyDefinitionSelection,
        PropertyTags,
    };
    static props = {
        readonly: { type: Boolean, optional: true },
        canChangeDefinition: { type: Boolean, optional: true },
        checkDefinitionWriteAccess: { type: Function, optional: true },
        propertyDefinition: { optional: true },
        context: { type: Object },
        isNewlyCreated: { type: Boolean, optional: true },
        propertyIndex: { type: Number },
        propertiesSize: { type: Number },
        onChange: { type: Function, optional: true },
        onDelete: { type: Function, optional: true },
        onMove: { type: Function, optional: true },
    };
}
```

**组件特性**:
- **丰富组件**: 集成多种UI组件
- **权限控制**: 支持只读和定义变更权限
- **位置管理**: 支持属性位置索引和移动
- **事件处理**: 支持变更、删除、移动事件
- **上下文**: 支持上下文传递

### 2. 组件初始化

```javascript
setup() {
    this.orm = useService("orm");
    this.addDialog = useOwnedDialogs();
    this.state = useState({
        definition: this.props.propertyDefinition || this._getDefaultDefinition(),
    });

    onWillUpdateProps((nextProps) => {
        if (nextProps.propertyDefinition !== this.props.propertyDefinition) {
            this.state.definition = nextProps.propertyDefinition || this._getDefaultDefinition();
        }
    });

    useEffect(() => {
        if (this.props.isNewlyCreated) {
            this.nameInput.el.focus();
        }
    });

    this.nameInput = useRef("nameInput");
}
```

**初始化功能**:
- **服务注入**: 注入ORM和对话框服务
- **状态管理**: 管理属性定义状态
- **属性监听**: 监听属性变化更新状态
- **焦点管理**: 新创建时自动聚焦名称输入
- **引用管理**: 管理名称输入框引用

### 3. 属性类型管理

```javascript
get availableTypes() {
    return [
        { value: "char", label: _t("Text") },
        { value: "boolean", label: _t("Checkbox") },
        { value: "integer", label: _t("Integer") },
        { value: "float", label: _t("Decimal") },
        { value: "date", label: _t("Date") },
        { value: "datetime", label: _t("Date & Time") },
        { value: "selection", label: _t("Selection") },
        { value: "tags", label: _t("Tags") },
        { value: "many2one", label: _t("Many2one") },
        { value: "many2many", label: _t("Many2many") },
    ];
}

get propertyType() {
    return this.state.definition.type;
}

_getDefaultDefinition() {
    return {
        name: "",
        string: "",
        type: "char",
        default: false,
    };
}
```

**类型管理功能**:
- **类型列表**: 提供所有可用的属性类型
- **类型获取**: 获取当前属性类型
- **默认定义**: 提供默认的属性定义
- **多样化**: 支持文本、数字、日期、选择等多种类型

### 4. 属性配置

```javascript
get canMove() {
    return (
        !this.props.readonly &&
        this.props.canChangeDefinition &&
        this.props.propertiesSize > 1
    );
}

get canMoveUp() {
    return this.canMove && this.props.propertyIndex > 0;
}

get canMoveDown() {
    return this.canMove && this.props.propertyIndex < this.props.propertiesSize - 1;
}

get showDefaultValue() {
    return ["char", "boolean", "integer", "float", "date", "datetime"].includes(
        this.propertyType
    );
}
```

**配置功能**:
- **移动权限**: 检查是否可以移动属性
- **方向控制**: 控制上下移动的可用性
- **默认值**: 控制是否显示默认值设置
- **类型相关**: 根据类型显示不同的配置选项

### 5. 事件处理

```javascript
_onPropertyChange(changes = {}) {
    Object.assign(this.state.definition, changes);
    if (this.props.onChange) {
        this.props.onChange(this.state.definition);
    }
}

_onPropertyNameChange(ev) {
    const name = ev.target.value;
    this._onPropertyChange({ name, string: name });
}

_onPropertyTypeChange(type) {
    const changes = { type };

    // Reset type-specific properties
    if (type !== "selection") {
        delete changes.selection;
    }
    if (type !== "many2one" && type !== "many2many") {
        delete changes.comodel;
        delete changes.domain;
    }
    if (!this.showDefaultValue) {
        changes.default = false;
    }

    this._onPropertyChange(changes);
}
```

**事件处理功能**:
- **属性变更**: 处理属性定义的变更
- **名称变更**: 处理属性名称变更
- **类型变更**: 处理属性类型变更并重置相关属性
- **智能重置**: 根据类型智能重置不相关的属性

## 使用场景

### 1. 属性定义管理器

```javascript
// 属性定义管理器
class PropertyDefinitionManager {
    constructor() {
        this.setupManager();
    }

    setupManager() {
        // 设置属性定义配置
        this.propertyDefinitionConfig = {
            enableTypeValidation: true,
            enableNameValidation: true,
            enableDefaultValueValidation: true,
            enableDomainValidation: true,
            enablePermissionCheck: true,
            enableAutoSave: false,
            enableVersioning: false,
            enableTemplates: false
        };

        // 设置支持的属性类型
        this.supportedTypes = new Map([
            ['char', {
                name: 'Text',
                hasDefault: true,
                hasSelection: false,
                hasComodel: false,
                hasDomain: false,
                validation: ['required', 'maxLength']
            }],
            ['boolean', {
                name: 'Checkbox',
                hasDefault: true,
                hasSelection: false,
                hasComodel: false,
                hasDomain: false,
                validation: ['required']
            }],
            ['integer', {
                name: 'Integer',
                hasDefault: true,
                hasSelection: false,
                hasComodel: false,
                hasDomain: false,
                validation: ['required', 'min', 'max']
            }],
            ['float', {
                name: 'Decimal',
                hasDefault: true,
                hasSelection: false,
                hasComodel: false,
                hasDomain: false,
                validation: ['required', 'min', 'max', 'precision']
            }],
            ['date', {
                name: 'Date',
                hasDefault: true,
                hasSelection: false,
                hasComodel: false,
                hasDomain: false,
                validation: ['required', 'dateRange']
            }],
            ['datetime', {
                name: 'Date & Time',
                hasDefault: true,
                hasSelection: false,
                hasComodel: false,
                hasDomain: false,
                validation: ['required', 'dateRange']
            }],
            ['selection', {
                name: 'Selection',
                hasDefault: false,
                hasSelection: true,
                hasComodel: false,
                hasDomain: false,
                validation: ['required', 'selectionOptions']
            }],
            ['tags', {
                name: 'Tags',
                hasDefault: false,
                hasSelection: true,
                hasComodel: false,
                hasDomain: false,
                validation: ['required']
            }],
            ['many2one', {
                name: 'Many2one',
                hasDefault: false,
                hasSelection: false,
                hasComodel: true,
                hasDomain: true,
                validation: ['required', 'comodel']
            }],
            ['many2many', {
                name: 'Many2many',
                hasDefault: false,
                hasSelection: false,
                hasComodel: true,
                hasDomain: true,
                validation: ['required', 'comodel']
            }]
        ]);

        // 设置验证规则
        this.validationRules = new Map([
            ['required', (value, definition) => {
                if (definition.required && !value) {
                    return 'This field is required';
                }
                return true;
            }],
            ['maxLength', (value, definition) => {
                if (definition.maxLength && value && value.length > definition.maxLength) {
                    return `Maximum length is ${definition.maxLength}`;
                }
                return true;
            }],
            ['min', (value, definition) => {
                if (definition.min !== undefined && value < definition.min) {
                    return `Minimum value is ${definition.min}`;
                }
                return true;
            }],
            ['max', (value, definition) => {
                if (definition.max !== undefined && value > definition.max) {
                    return `Maximum value is ${definition.max}`;
                }
                return true;
            }],
            ['selectionOptions', (value, definition) => {
                if (definition.type === 'selection' && (!definition.selection || definition.selection.length === 0)) {
                    return 'Selection options are required';
                }
                return true;
            }],
            ['comodel', (value, definition) => {
                if ((definition.type === 'many2one' || definition.type === 'many2many') && !definition.comodel) {
                    return 'Related model is required';
                }
                return true;
            }]
        ]);

        // 设置属性定义统计
        this.propertyDefinitionStatistics = {
            totalDefinitions: 0,
            definitionsByType: new Map(),
            averagePropertiesPerDefinition: 0,
            mostUsedType: null,
            validationErrors: 0,
            successfulValidations: 0
        };

        this.initializePropertyDefinitionSystem();
    }

    // 初始化属性定义系统
    initializePropertyDefinitionSystem() {
        // 创建增强的属性定义组件
        this.createEnhancedPropertyDefinition();

        // 设置验证系统
        this.setupValidationSystem();

        // 设置类型系统
        this.setupTypeSystem();

        // 设置权限系统
        this.setupPermissionSystem();
    }
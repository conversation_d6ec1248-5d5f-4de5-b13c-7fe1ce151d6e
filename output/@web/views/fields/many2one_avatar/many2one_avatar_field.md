# Many2OneAvatarField - 多对一头像字段

## 概述

`many2one_avatar_field.js` 是 Odoo Web 客户端的多对一头像字段组件，负责以头像形式显示和管理多对一关系。该模块包含101行代码，是一个专门的关系字段组件，专门用于处理带头像的many2one类型字段，具备头像显示、弹出框编辑、看板视图支持等特性，是用户关系管理的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/many2one_avatar/many2one_avatar_field.js`
- **行数**: 101
- **模块**: `@web/views/fields/many2one_avatar/many2one_avatar_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/core/popover/popover_hook'        // 弹出框钩子
'@web/views/fields/many2one/many2one_field' // 基础多对一字段
'@web/views/fields/relational_utils'    // 关系字段工具
```

## 核心功能

### 1. 基础头像字段

```javascript
const Many2OneAvatarField = class Many2OneAvatarField extends Many2OneField {
    static template = "web.Many2OneAvatarField";
    static components = {
        ...Many2OneField.components,
        Many2XAutocomplete: AvatarMany2XAutocomplete,
    };
}
```

**基础特性**:
- **继承基类**: 继承Many2OneField的所有功能
- **头像模板**: 使用专门的头像显示模板
- **头像自动完成**: 使用AvatarMany2XAutocomplete组件
- **组件扩展**: 扩展基类组件配置

### 2. 字段注册配置

```javascript
const many2OneAvatarField = {
    ...many2OneField,
    component: Many2OneAvatarField,
    extractProps(fieldInfo) {
        const props = many2OneField.extractProps(...arguments);
        props.canOpen = fieldInfo.viewType === "form";
        return props;
    },
};
```

**配置特性**:
- **基础继承**: 继承基础多对一字段配置
- **组件替换**: 使用头像专用组件
- **属性提取**: 根据视图类型提取属性
- **表单限制**: 仅在表单视图中允许打开记录

### 3. 弹出框头像字段

```javascript
const Many2OneFieldPopover = class Many2OneFieldPopover extends Many2OneField {
    static props = {
        ...Many2OneField.props,
        close: { type: Function },
    };
    static components = {
        Many2XAutocomplete: AvatarMany2XAutocomplete,
    };
    
    get Many2XAutocompleteProps() {
        return {
            ...super.Many2XAutocompleteProps,
            dropdown: false,
            autofocus: true,
        };
    }

    async updateRecord(value) {
        const updatedValue = await super.updateRecord(...arguments);
        await this.props.record.save();
        return updatedValue;
    }
}
```

**弹出框特性**:
- **关闭回调**: 支持close回调函数
- **头像自动完成**: 使用头像自动完成组件
- **无下拉框**: 禁用下拉框显示
- **自动聚焦**: 启用自动聚焦
- **自动保存**: 更新后自动保存记录

### 4. 看板头像字段

```javascript
const KanbanMany2OneAvatarField = class KanbanMany2OneAvatarField extends Many2OneAvatarField {
    static template = "web.KanbanMany2OneAvatarField";
    static props = {
        ...Many2OneAvatarField.props,
        isEditable: { type: Boolean, optional: true },
    };
    
    setup() {
        super.setup();
        this.popover = usePopover(Many2OneFieldPopover, {
            popoverClass: "o_m2o_tags_avatar_field_popover",
            closeOnClickAway: (target) => !target.closest(".modal"),
        });
    }
    
    get popoverProps() {
        const props = {
            ...this.props,
            readonly: false,
        };
        delete props.isEditable;
        return props;
    }
    
    openPopover(ev) {
        if (!this.props.isEditable) {
            return;
        }
        this.popover.open(ev.currentTarget, {
            ...this.popoverProps,
            canCreate: false,
            canCreateEdit: false,
            canQuickCreate: false,
            placeholder: _t("Search user..."),
        });
    }
}
```

**看板特性**:
- **看板模板**: 使用专门的看板模板
- **编辑控制**: 支持isEditable属性控制编辑
- **弹出框编辑**: 通过弹出框进行编辑
- **权限限制**: 禁用创建相关功能
- **用户搜索**: 专门用于搜索用户

### 5. 看板字段注册

```javascript
const kanbanMany2OneAvatarField = {
    ...many2OneField,
    component: KanbanMany2OneAvatarField,
    additionalClasses: ["o_field_many2one_avatar_kanban"],
    extractProps(fieldInfo, dynamicInfo) {
        const props = many2OneAvatarField.extractProps(...arguments);
        props.isEditable = !dynamicInfo.readonly;
        return props;
    },
};

registry.category("fields").add("many2one_avatar", many2OneAvatarField);
registry.category("fields").add("kanban.many2one_avatar", kanbanMany2OneAvatarField);
```

**注册特性**:
- **CSS类**: 添加看板专用CSS类
- **编辑属性**: 根据只读状态设置编辑属性
- **双重注册**: 注册普通和看板两种类型
- **命名空间**: 使用kanban前缀区分看板字段

## 使用场景

### 1. 多对一头像字段管理器

```javascript
// 多对一头像字段管理器
class Many2OneAvatarFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置头像字段配置
        this.avatarConfig = {
            enableAvatarDisplay: true,
            enablePopoverEdit: true,
            enableKanbanMode: true,
            enableAutoSave: true,
            enableUserSearch: true,
            enableAvatarCache: true,
            defaultAvatarSize: 32,
            enablePlaceholder: true
        };
        
        // 设置头像显示选项
        this.avatarDisplayOptions = {
            size: 32,
            shape: 'circle', // 'circle', 'square', 'rounded'
            showName: true,
            showInitials: true,
            enableTooltip: true,
            enableClickToEdit: true
        };
        
        // 设置弹出框配置
        this.popoverConfig = {
            enablePopover: true,
            closeOnClickAway: true,
            enableAutoFocus: true,
            enableDropdown: false,
            searchPlaceholder: 'Search user...',
            enableQuickActions: false
        };
        
        // 设置看板配置
        this.kanbanConfig = {
            enableKanbanView: true,
            enableInlineEdit: true,
            maxAvatarsDisplay: 3,
            enableOverflowIndicator: true,
            enableDragDrop: false
        };
        
        // 设置头像统计
        this.avatarStatistics = {
            totalAvatarFields: 0,
            totalAvatarLoads: 0,
            totalPopoverOpens: 0,
            totalUserSelections: 0,
            averageLoadTime: 0,
            cacheHitRate: 0
        };
        
        this.initializeAvatarSystem();
    }
    
    // 初始化头像系统
    initializeAvatarSystem() {
        // 创建增强的多对一头像字段
        this.createEnhancedMany2OneAvatarField();
        
        // 设置头像缓存系统
        this.setupAvatarCacheSystem();
        
        // 设置弹出框系统
        this.setupPopoverSystem();
        
        // 设置看板系统
        this.setupKanbanSystem();
    }
    
    // 创建增强的多对一头像字段
    createEnhancedMany2OneAvatarField() {
        const originalField = Many2OneAvatarField;
        
        this.EnhancedMany2OneAvatarField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加头像功能
                this.addAvatarFeatures();
                
                // 添加缓存功能
                this.addCacheFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    avatarLoaded: false,
                    avatarError: false,
                    isHovered: false,
                    popoverOpen: false,
                    lastLoadTime: null,
                    avatarCache: new Map(),
                    userInfo: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的头像加载
                this.enhancedLoadAvatar = async (userId) => {
                    const startTime = performance.now();
                    
                    try {
                        // 检查缓存
                        const cacheKey = `avatar_${userId}`;
                        if (this.enhancedState.avatarCache.has(cacheKey)) {
                            this.recordCacheHit();
                            return this.enhancedState.avatarCache.get(cacheKey);
                        }
                        
                        // 加载头像
                        const avatarUrl = await this.loadAvatar(userId);
                        
                        // 缓存头像
                        this.enhancedState.avatarCache.set(cacheKey, avatarUrl);
                        
                        // 记录加载时间
                        const loadTime = performance.now() - startTime;
                        this.recordLoadTime(loadTime);
                        
                        this.enhancedState.avatarLoaded = true;
                        this.enhancedState.avatarError = false;
                        
                        return avatarUrl;
                        
                    } catch (error) {
                        this.handleAvatarError(error);
                        throw error;
                    }
                };
                
                // 增强的用户选择
                this.enhancedSelectUser = async (user) => {
                    try {
                        // 验证用户
                        this.validateUser(user);
                        
                        // 执行选择
                        await this.selectUser(user);
                        
                        // 加载用户信息
                        const userInfo = await this.loadUserInfo(user.id);
                        this.enhancedState.userInfo = userInfo;
                        
                        // 预加载头像
                        await this.enhancedLoadAvatar(user.id);
                        
                        // 记录统计
                        this.avatarStatistics.totalUserSelections++;
                        
                    } catch (error) {
                        this.handleSelectionError(error);
                        throw error;
                    }
                };
                
                // 增强的弹出框打开
                this.enhancedOpenPopover = (event) => {
                    if (!this.props.isEditable) {
                        return;
                    }
                    
                    this.enhancedState.popoverOpen = true;
                    
                    // 记录统计
                    this.avatarStatistics.totalPopoverOpens++;
                    
                    // 调用原始方法
                    this.openPopover(event);
                };
                
                // 获取头像URL
                this.getAvatarUrl = (userId, size = null) => {
                    const avatarSize = size || this.avatarDisplayOptions.size;
                    return `/web/image/res.users/${userId}/avatar_${avatarSize}`;
                };
                
                // 获取用户初始字母
                this.getUserInitials = (userName) => {
                    if (!userName) return '?';
                    
                    const names = userName.split(' ');
                    if (names.length >= 2) {
                        return (names[0][0] + names[1][0]).toUpperCase();
                    } else {
                        return userName.substring(0, 2).toUpperCase();
                    }
                };
                
                // 获取头像样式
                this.getAvatarStyle = () => {
                    const style = {
                        width: `${this.avatarDisplayOptions.size}px`,
                        height: `${this.avatarDisplayOptions.size}px`
                    };
                    
                    switch (this.avatarDisplayOptions.shape) {
                        case 'circle':
                            style.borderRadius = '50%';
                            break;
                        case 'rounded':
                            style.borderRadius = '4px';
                            break;
                        case 'square':
                        default:
                            style.borderRadius = '0';
                            break;
                    }
                    
                    return style;
                };
                
                // 获取头像信息
                this.getAvatarInfo = () => {
                    const value = this.props.record.data[this.props.name];
                    
                    if (!value || !value[0]) {
                        return null;
                    }
                    
                    return {
                        id: value[0],
                        name: value[1],
                        avatarUrl: this.getAvatarUrl(value[0]),
                        initials: this.getUserInitials(value[1]),
                        isLoaded: this.enhancedState.avatarLoaded,
                        hasError: this.enhancedState.avatarError,
                        userInfo: this.enhancedState.userInfo
                    };
                };
                
                // 验证用户
                this.validateUser = (user) => {
                    if (!user || !user.id) {
                        throw new Error('Invalid user');
                    }
                    
                    if (!user.name || user.name.trim().length === 0) {
                        throw new Error('User name is required');
                    }
                };
                
                // 加载用户信息
                this.loadUserInfo = async (userId) => {
                    try {
                        const userInfo = await this.orm.call(
                            'res.users',
                            'read',
                            [userId],
                            { fields: ['name', 'email', 'phone', 'active'] }
                        );
                        
                        return userInfo[0] || null;
                        
                    } catch (error) {
                        console.warn('Failed to load user info:', error);
                        return null;
                    }
                };
                
                // 清除头像缓存
                this.clearAvatarCache = () => {
                    this.enhancedState.avatarCache.clear();
                };
                
                // 预加载头像
                this.preloadAvatar = (userId) => {
                    const img = new Image();
                    img.src = this.getAvatarUrl(userId);
                    
                    img.onload = () => {
                        console.log(`Avatar preloaded for user ${userId}`);
                    };
                    
                    img.onerror = () => {
                        console.warn(`Failed to preload avatar for user ${userId}`);
                    };
                };
                
                // 鼠标悬停处理
                this.onMouseEnter = () => {
                    this.enhancedState.isHovered = true;
                };
                
                // 鼠标离开处理
                this.onMouseLeave = () => {
                    this.enhancedState.isHovered = false;
                };
                
                // 获取工具提示内容
                this.getTooltipContent = () => {
                    const avatarInfo = this.getAvatarInfo();
                    
                    if (!avatarInfo) {
                        return null;
                    }
                    
                    let tooltip = avatarInfo.name;
                    
                    if (avatarInfo.userInfo) {
                        if (avatarInfo.userInfo.email) {
                            tooltip += `\n${avatarInfo.userInfo.email}`;
                        }
                        if (avatarInfo.userInfo.phone) {
                            tooltip += `\n${avatarInfo.userInfo.phone}`;
                        }
                    }
                    
                    return tooltip;
                };
                
                // 记录缓存命中
                this.recordCacheHit = () => {
                    // 记录缓存命中统计
                };
                
                // 记录加载时间
                this.recordLoadTime = (duration) => {
                    this.avatarStatistics.averageLoadTime = 
                        (this.avatarStatistics.averageLoadTime + duration) / 2;
                    this.avatarStatistics.totalAvatarLoads++;
                };
                
                // 处理头像错误
                this.handleAvatarError = (error) => {
                    this.enhancedState.avatarError = true;
                    console.error('Avatar load error:', error);
                };
                
                // 处理选择错误
                this.handleSelectionError = (error) => {
                    console.error('User selection error:', error);
                };
            }
            
            addAvatarFeatures() {
                // 头像功能
                this.avatarManager = {
                    enabled: this.avatarConfig.enableAvatarDisplay,
                    loadAvatar: (userId) => this.enhancedLoadAvatar(userId),
                    getInfo: () => this.getAvatarInfo(),
                    getStyle: () => this.getAvatarStyle(),
                    preload: (userId) => this.preloadAvatar(userId)
                };
            }
            
            addCacheFeatures() {
                // 缓存功能
                this.cacheManager = {
                    enabled: this.avatarConfig.enableAvatarCache,
                    get: (key) => this.enhancedState.avatarCache.get(key),
                    set: (key, value) => this.enhancedState.avatarCache.set(key, value),
                    clear: () => this.clearAvatarCache(),
                    size: () => this.enhancedState.avatarCache.size
                };
            }
        };
    }
    
    // 设置头像缓存系统
    setupAvatarCacheSystem() {
        this.avatarCacheConfig = {
            enabled: this.avatarConfig.enableAvatarCache,
            maxSize: 100,
            ttl: 3600000 // 1小时
        };
    }
    
    // 设置弹出框系统
    setupPopoverSystem() {
        this.popoverSystemConfig = {
            enabled: this.avatarConfig.enablePopoverEdit,
            config: this.popoverConfig
        };
    }
    
    // 设置看板系统
    setupKanbanSystem() {
        this.kanbanSystemConfig = {
            enabled: this.avatarConfig.enableKanbanMode,
            config: this.kanbanConfig
        };
    }
    
    // 创建多对一头像字段
    createMany2OneAvatarField(props) {
        const field = new this.EnhancedMany2OneAvatarField(props);
        this.avatarStatistics.totalAvatarFields++;
        return field;
    }
    
    // 批量预加载头像
    batchPreloadAvatars(userIds) {
        const results = [];
        
        for (const userId of userIds) {
            try {
                const field = this.createMany2OneAvatarField({});
                field.preloadAvatar(userId);
                results.push({ userId, success: true });
            } catch (error) {
                results.push({ userId, success: false, error });
            }
        }
        
        return results;
    }
    
    // 获取头像统计
    getAvatarStatistics() {
        return {
            ...this.avatarStatistics,
            averagePopoverOpensPerField: this.avatarStatistics.totalPopoverOpens / Math.max(this.avatarStatistics.totalAvatarFields, 1),
            averageSelectionsPerField: this.avatarStatistics.totalUserSelections / Math.max(this.avatarStatistics.totalAvatarFields, 1),
            cacheEfficiency: this.avatarStatistics.cacheHitRate / Math.max(this.avatarStatistics.totalAvatarLoads, 1) * 100
        };
    }
    
    // 销毁管理器
    destroy() {
        // 重置统计
        this.avatarStatistics = {
            totalAvatarFields: 0,
            totalAvatarLoads: 0,
            totalPopoverOpens: 0,
            totalUserSelections: 0,
            averageLoadTime: 0,
            cacheHitRate: 0
        };
    }
}

// 使用示例
const avatarManager = new Many2OneAvatarFieldManager();

// 创建多对一头像字段
const avatarField = avatarManager.createMany2OneAvatarField({
    name: 'user_id',
    record: {
        data: { user_id: [1, 'John Doe'] },
        fields: { 
            user_id: { 
                type: 'many2one',
                relation: 'res.users'
            }
        }
    },
    isEditable: true
});

// 批量预加载头像
const userIds = [1, 2, 3, 4, 5];
const preloadResults = avatarManager.batchPreloadAvatars(userIds);

// 获取统计信息
const stats = avatarManager.getAvatarStatistics();
console.log('Many2one avatar field statistics:', stats);
```

## 技术特点

### 1. 头像显示
- **头像集成**: 集成头像显示功能
- **自动完成**: 带头像的自动完成组件
- **缓存机制**: 头像缓存提高性能
- **错误处理**: 头像加载错误处理

### 2. 视图适配
- **多视图支持**: 支持表单、列表、看板视图
- **看板优化**: 专门的看板视图优化
- **弹出框编辑**: 看板中的弹出框编辑
- **权限控制**: 根据视图类型控制权限

### 3. 用户体验
- **直观显示**: 直观的头像显示
- **快速识别**: 通过头像快速识别用户
- **交互友好**: 友好的交互体验
- **响应迅速**: 快速的响应速度

### 4. 继承设计
- **基类继承**: 继承基础多对一字段功能
- **组件替换**: 替换为头像专用组件
- **配置扩展**: 扩展基础配置
- **功能增强**: 增强头像相关功能

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **类继承**: 继承基础多对一字段
- **功能扩展**: 扩展头像功能
- **组件替换**: 替换自动完成组件

### 2. 适配器模式 (Adapter Pattern)
- **视图适配**: 适配不同视图需求
- **组件适配**: 适配头像组件
- **接口适配**: 适配头像接口

### 3. 策略模式 (Strategy Pattern)
- **显示策略**: 不同的头像显示策略
- **编辑策略**: 不同的编辑策略
- **缓存策略**: 不同的缓存策略

### 4. 装饰器模式 (Decorator Pattern)
- **头像装饰**: 为字段添加头像装饰
- **功能装饰**: 添加头像相关功能
- **视觉装饰**: 添加视觉效果

## 注意事项

1. **性能考虑**: 头像加载对性能的影响
2. **缓存管理**: 合理管理头像缓存
3. **用户体验**: 提供流畅的头像显示
4. **权限控制**: 确保头像访问权限

## 扩展建议

1. **头像编辑**: 支持头像编辑功能
2. **批量操作**: 支持批量头像操作
3. **状态显示**: 显示用户在线状态
4. **个性化**: 支持个性化头像设置
5. **社交功能**: 集成社交功能

该多对一头像字段为Odoo Web客户端提供了直观的用户关系显示功能，通过头像显示和智能缓存确保了良好的用户体验和系统性能。

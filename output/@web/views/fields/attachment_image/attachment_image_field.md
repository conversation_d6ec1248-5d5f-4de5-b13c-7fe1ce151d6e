# AttachmentImageField - 附件图片字段

## 概述

`attachment_image_field.js` 是 Odoo Web 客户端的附件图片字段组件，负责显示与记录关联的附件图片。该模块包含24行代码，是一个简洁的字段组件，专门用于显示many2one关系字段对应的图片附件，具备图片显示、附件关联、响应式布局等特性，是文档和媒体管理中的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/attachment_image/attachment_image_field.js`
- **行数**: 24
- **模块**: `@web/views/fields/attachment_image/attachment_image_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@odoo/owl'                            // OWL框架
'@web/views/fields/standard_field_props' // 标准字段属性
```

## 核心功能

### 1. 组件定义

```javascript
const AttachmentImageField = class AttachmentImageField extends Component {
    static template = "web.AttachmentImageField";
    static props = { ...standardFieldProps };
}
```

**组件特性**:
- **简洁设计**: 极简的组件实现
- **标准属性**: 继承所有标准字段属性
- **专用模板**: 使用AttachmentImageField专用模板
- **轻量级**: 最小化的代码实现

### 2. 字段注册

```javascript
const attachmentImageField = {
    component: AttachmentImageField,
    displayName: _t("Attachment Image"),
    supportedTypes: ["many2one"],
};

registry.category("fields").add("attachment_image", attachmentImageField);
```

**注册功能**:
- **字段定义**: 定义附件图片字段
- **显示名称**: 国际化的显示名称
- **支持类型**: 仅支持many2one类型
- **注册表**: 注册到字段注册表

## 使用场景

### 1. 附件图片字段管理器

```javascript
// 附件图片字段管理器
class AttachmentImageFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置图片配置
        this.imageConfig = {
            enableLazyLoading: true,
            enableImagePreview: true,
            enableImageZoom: true,
            enableImageRotation: true,
            maxWidth: 800,
            maxHeight: 600,
            defaultPlaceholder: '/web/static/img/placeholder.png',
            supportedFormats: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            enableImageOptimization: true
        };
        
        // 设置缓存管理
        this.imageCache = new Map();
        
        // 设置加载状态
        this.loadingStates = new Map();
        
        // 设置图片统计
        this.imageStatistics = {
            totalImages: 0,
            loadedImages: 0,
            failedImages: 0,
            cacheHits: 0,
            averageLoadTime: 0
        };
        
        this.initializeImageSystem();
    }
    
    // 初始化图片系统
    initializeImageSystem() {
        // 创建增强的附件图片字段
        this.createEnhancedAttachmentImageField();
        
        // 设置图片加载
        this.setupImageLoading();
        
        // 设置图片缓存
        this.setupImageCache();
        
        // 设置图片优化
        this.setupImageOptimization();
    }
    
    // 创建增强的附件图片字段
    createEnhancedAttachmentImageField() {
        const originalField = AttachmentImageField;
        
        this.EnhancedAttachmentImageField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加图片处理
                this.addImageProcessing();
                
                // 添加交互功能
                this.addInteractionFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isLoading: false,
                    hasError: false,
                    imageUrl: null,
                    imageInfo: null,
                    isZoomed: false,
                    rotation: 0
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 获取图片URL
                this.getImageUrl = () => {
                    const record = this.props.record;
                    const fieldName = this.props.name;
                    const attachmentId = record.data[fieldName];
                    
                    if (!attachmentId) {
                        return this.imageConfig.defaultPlaceholder;
                    }
                    
                    // 检查缓存
                    const cacheKey = `attachment_${attachmentId}`;
                    if (this.imageCache.has(cacheKey)) {
                        this.imageStatistics.cacheHits++;
                        return this.imageCache.get(cacheKey);
                    }
                    
                    // 生成图片URL
                    const imageUrl = `/web/image/ir.attachment/${attachmentId}/datas`;
                    this.imageCache.set(cacheKey, imageUrl);
                    
                    return imageUrl;
                };
                
                // 加载图片
                this.loadImage = async () => {
                    const startTime = performance.now();
                    this.enhancedState.isLoading = true;
                    this.enhancedState.hasError = false;
                    
                    try {
                        const imageUrl = this.getImageUrl();
                        
                        // 预加载图片
                        await this.preloadImage(imageUrl);
                        
                        // 获取图片信息
                        const imageInfo = await this.getImageInfo(imageUrl);
                        
                        this.enhancedState.imageUrl = imageUrl;
                        this.enhancedState.imageInfo = imageInfo;
                        this.enhancedState.isLoading = false;
                        
                        // 记录统计
                        this.imageStatistics.loadedImages++;
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordLoadTime(endTime - startTime);
                        
                    } catch (error) {
                        this.handleImageError(error);
                    }
                };
                
                // 预加载图片
                this.preloadImage = (url) => {
                    return new Promise((resolve, reject) => {
                        const img = new Image();
                        img.onload = () => resolve(img);
                        img.onerror = () => reject(new Error('Image load failed'));
                        img.src = url;
                    });
                };
                
                // 获取图片信息
                this.getImageInfo = async (url) => {
                    try {
                        const img = await this.preloadImage(url);
                        return {
                            width: img.naturalWidth,
                            height: img.naturalHeight,
                            aspectRatio: img.naturalWidth / img.naturalHeight,
                            size: await this.getImageSize(url)
                        };
                    } catch (error) {
                        return null;
                    }
                };
                
                // 获取图片大小
                this.getImageSize = async (url) => {
                    try {
                        const response = await fetch(url, { method: 'HEAD' });
                        const contentLength = response.headers.get('content-length');
                        return contentLength ? parseInt(contentLength) : null;
                    } catch (error) {
                        return null;
                    }
                };
                
                // 优化图片URL
                this.optimizeImageUrl = (url, options = {}) => {
                    const params = new URLSearchParams();
                    
                    if (options.width) {
                        params.append('width', options.width);
                    }
                    
                    if (options.height) {
                        params.append('height', options.height);
                    }
                    
                    if (options.quality) {
                        params.append('quality', options.quality);
                    }
                    
                    return params.toString() ? `${url}?${params.toString()}` : url;
                };
                
                // 缩放图片
                this.zoomImage = () => {
                    this.enhancedState.isZoomed = !this.enhancedState.isZoomed;
                    this.updateImageDisplay();
                };
                
                // 旋转图片
                this.rotateImage = (degrees = 90) => {
                    this.enhancedState.rotation = (this.enhancedState.rotation + degrees) % 360;
                    this.updateImageDisplay();
                };
                
                // 重置图片
                this.resetImage = () => {
                    this.enhancedState.isZoomed = false;
                    this.enhancedState.rotation = 0;
                    this.updateImageDisplay();
                };
                
                // 更新图片显示
                this.updateImageDisplay = () => {
                    // 实现图片显示更新逻辑
                    console.log('Updating image display:', {
                        zoomed: this.enhancedState.isZoomed,
                        rotation: this.enhancedState.rotation
                    });
                };
                
                // 下载图片
                this.downloadImage = () => {
                    const imageUrl = this.enhancedState.imageUrl;
                    if (imageUrl) {
                        const link = document.createElement('a');
                        link.href = imageUrl;
                        link.download = `attachment_${Date.now()}.jpg`;
                        link.click();
                    }
                };
                
                // 复制图片URL
                this.copyImageUrl = async () => {
                    const imageUrl = this.enhancedState.imageUrl;
                    if (imageUrl) {
                        try {
                            await navigator.clipboard.writeText(imageUrl);
                            console.log('Image URL copied to clipboard');
                        } catch (error) {
                            console.error('Failed to copy URL:', error);
                        }
                    }
                };
                
                // 获取图片样式
                this.getImageStyle = () => {
                    const style = {};
                    
                    if (this.enhancedState.rotation !== 0) {
                        style.transform = `rotate(${this.enhancedState.rotation}deg)`;
                    }
                    
                    if (this.enhancedState.isZoomed) {
                        style.transform = (style.transform || '') + ' scale(1.5)';
                        style.cursor = 'zoom-out';
                    } else {
                        style.cursor = 'zoom-in';
                    }
                    
                    return style;
                };
                
                // 处理图片错误
                this.handleImageError = (error) => {
                    console.error('Image error:', error);
                    this.enhancedState.isLoading = false;
                    this.enhancedState.hasError = true;
                    this.imageStatistics.failedImages++;
                };
                
                // 记录加载时间
                this.recordLoadTime = (duration) => {
                    this.imageStatistics.totalImages++;
                    this.imageStatistics.averageLoadTime = 
                        (this.imageStatistics.averageLoadTime * (this.imageStatistics.totalImages - 1) + duration) / 
                        this.imageStatistics.totalImages;
                };
            }
            
            addImageProcessing() {
                // 图片处理功能
                this.imageProcessor = {
                    resize: (width, height) => this.resizeImage(width, height),
                    crop: (x, y, width, height) => this.cropImage(x, y, width, height),
                    filter: (filterType) => this.applyFilter(filterType)
                };
            }
            
            addInteractionFeatures() {
                // 交互功能
                this.interactionManager = {
                    enableZoom: this.imageConfig.enableImageZoom,
                    enableRotation: this.imageConfig.enableImageRotation,
                    enableDownload: true,
                    enableCopy: true
                };
            }
            
            // 组件挂载时加载图片
            onMounted() {
                this.loadImage();
            }
            
            // 属性更新时重新加载
            onWillUpdateProps(nextProps) {
                if (nextProps.record.data[nextProps.name] !== this.props.record.data[this.props.name]) {
                    this.loadImage();
                }
            }
        };
    }
    
    // 设置图片加载
    setupImageLoading() {
        this.loadingConfig = {
            enableLazyLoading: this.imageConfig.enableLazyLoading,
            loadingPlaceholder: '/web/static/img/loading.gif',
            errorPlaceholder: '/web/static/img/error.png'
        };
    }
    
    // 设置图片缓存
    setupImageCache() {
        this.cacheConfig = {
            maxSize: 100,
            ttl: 3600000, // 1小时
            cleanupInterval: 300000 // 5分钟
        };
        
        // 定期清理缓存
        setInterval(() => {
            this.cleanupImageCache();
        }, this.cacheConfig.cleanupInterval);
    }
    
    // 清理图片缓存
    cleanupImageCache() {
        if (this.imageCache.size > this.cacheConfig.maxSize) {
            const entries = Array.from(this.imageCache.entries());
            const toDelete = entries.slice(0, entries.length - this.cacheConfig.maxSize);
            
            for (const [key] of toDelete) {
                this.imageCache.delete(key);
            }
        }
    }
    
    // 设置图片优化
    setupImageOptimization() {
        this.optimizationConfig = {
            enableWebP: true,
            enableProgressive: true,
            defaultQuality: 85,
            enableResponsive: true
        };
    }
    
    // 创建附件图片字段
    createAttachmentImageField(props) {
        return new this.EnhancedAttachmentImageField(props);
    }
    
    // 获取图片统计
    getImageStatistics() {
        return {
            ...this.imageStatistics,
            cacheSize: this.imageCache.size,
            cacheHitRate: this.imageStatistics.cacheHits / Math.max(this.imageStatistics.totalImages, 1)
        };
    }
    
    // 清理缓存
    clearCache() {
        this.imageCache.clear();
        this.loadingStates.clear();
    }
    
    // 销毁管理器
    destroy() {
        // 清理缓存
        this.imageCache.clear();
        this.loadingStates.clear();
        
        // 重置统计
        this.imageStatistics = {
            totalImages: 0,
            loadedImages: 0,
            failedImages: 0,
            cacheHits: 0,
            averageLoadTime: 0
        };
    }
}

// 使用示例
const imageFieldManager = new AttachmentImageFieldManager();

// 创建附件图片字段
const attachmentImageField = imageFieldManager.createAttachmentImageField({
    name: 'image_attachment',
    record: { data: { image_attachment: 123 } }
});

// 获取统计信息
const stats = imageFieldManager.getImageStatistics();
console.log('Attachment image field statistics:', stats);
```

## 技术特点

### 1. 简洁高效
- **最小实现**: 最小化的代码实现
- **专一功能**: 专注于图片显示功能
- **轻量级**: 极小的资源占用
- **快速加载**: 快速的组件加载

### 2. 标准兼容
- **标准属性**: 完全兼容标准字段属性
- **类型支持**: 专门支持many2one类型
- **注册标准**: 标准的字段注册方式
- **模板系统**: 使用标准模板系统

### 3. 扩展性
- **可扩展**: 易于扩展功能
- **可定制**: 支持自定义配置
- **可复用**: 高度可复用的组件
- **可维护**: 易于维护和修改

### 4. 集成性
- **无缝集成**: 与Odoo系统无缝集成
- **附件关联**: 与附件系统紧密关联
- **记录绑定**: 与记录数据绑定
- **视图兼容**: 兼容各种视图类型

## 设计模式

### 1. 组件模式 (Component Pattern)
- **UI组件**: 封装图片显示UI
- **属性驱动**: 基于属性驱动渲染
- **状态管理**: 管理组件状态

### 2. 代理模式 (Proxy Pattern)
- **附件代理**: 代理附件数据访问
- **URL代理**: 代理图片URL生成
- **缓存代理**: 代理缓存访问

### 3. 策略模式 (Strategy Pattern)
- **加载策略**: 不同的图片加载策略
- **缓存策略**: 不同的缓存策略
- **显示策略**: 不同的显示策略

### 4. 观察者模式 (Observer Pattern)
- **数据观察**: 观察附件数据变化
- **状态观察**: 观察加载状态变化
- **记录观察**: 观察记录变化

## 注意事项

1. **性能考虑**: 避免频繁的图片加载
2. **内存管理**: 合理使用图片缓存
3. **错误处理**: 完善的图片加载错误处理
4. **用户体验**: 提供加载状态和错误提示

## 扩展建议

1. **图片编辑**: 添加基础图片编辑功能
2. **批量操作**: 支持批量图片操作
3. **格式转换**: 支持图片格式转换
4. **压缩优化**: 添加图片压缩功能
5. **预览增强**: 增强图片预览功能

该附件图片字段为Odoo Web客户端提供了简洁高效的图片显示功能，通过最小化的实现和标准化的接口确保了良好的性能和兼容性。

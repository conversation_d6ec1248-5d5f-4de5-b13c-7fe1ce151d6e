# GaugeField - 仪表盘字段

## 概述

`gauge_field.js` 是 Odoo Web 客户端的仪表盘字段组件，负责以可视化仪表盘的形式显示数值数据。该模块包含124行代码，是一个基于Chart.js的图表组件，专门用于显示进度、比例或测量值，具备图表渲染、最大值配置、标题显示、工具提示等特性，是数据可视化的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/gauge/gauge_field.js`
- **行数**: 124
- **模块**: `@web/views/fields/gauge/gauge_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'            // 翻译服务
'@web/core/assets'                      // 资源加载
'@web/core/registry'                    // 注册表
'@web/views/fields/formatters'          // 字段格式化器
'@web/views/fields/standard_field_props' // 标准字段属性
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const GaugeField = class GaugeField extends Component {
    static template = "web.GaugeField";
    static props = {
        ...standardFieldProps,
        maxValueField: { type: String },
        title: { type: String, optional: true },
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **最大值字段**: 支持maxValueField配置最大值字段
- **标题配置**: 支持title配置图表标题
- **专用模板**: 使用GaugeField专用模板

### 2. 组件初始化

```javascript
setup() {
    this.chart = null;
    this.canvasRef = useRef("canvas");

    onWillStart(async () => await loadBundle("web.chartjs_lib"));

    useEffect(() => {
        this.renderChart();
        return () => {
            if (this.chart) {
                this.chart.destroy();
            }
        };
    });
}
```

**初始化功能**:
- **图表实例**: 初始化图表实例变量
- **画布引用**: 创建画布元素引用
- **资源加载**: 异步加载Chart.js库
- **效果钩子**: 使用useEffect管理图表生命周期
- **清理机制**: 组件销毁时清理图表资源

### 3. 标题获取

```javascript
get title() {
    return this.props.title || this.props.record.fields[this.props.name].string || "";
}
```

**标题功能**:
- **优先级**: 优先使用props.title
- **字段标题**: 使用字段的string属性作为后备
- **空值处理**: 处理无标题的情况
- **动态获取**: 动态获取标题内容

### 4. 值格式化

```javascript
get formattedValue() {
    return formatFloat(this.props.record.data[this.props.name], {
        humanReadable: true,
        decimals: 1,
    });
}
```

**格式化功能**:
- **浮点格式化**: 使用formatFloat格式化数值
- **人性化显示**: 启用人性化数值显示
- **小数控制**: 控制小数位数为1位
- **数据获取**: 从记录数据中获取值

### 5. 图表渲染

```javascript
renderChart() {
    const gaugeValue = this.props.record.data[this.props.name];
    let maxValue = Math.max(gaugeValue, this.props.record.data[this.props.maxValueField]);
    let maxLabel = maxValue;
    if (gaugeValue === 0 && maxValue === 0) {
        maxValue = 1;
        maxLabel = 0;
    }
    const config = {
        type: "doughnut",
        data: {
            datasets: [
                {
                    data: [gaugeValue, maxValue - gaugeValue],
                    backgroundColor: ["#1f77b4", "#dddddd"],
                    label: this.title,
                },
            ],
        },
        options: {
            circumference: 180,
            rotation: 270,
            responsive: true,
            maintainAspectRatio: false,
            cutout: "70%",
            // ... 更多配置
        },
    };
    this.chart = new Chart(this.canvasRef.el, config);
}
```

**渲染功能**:
- **数值获取**: 获取当前值和最大值
- **边界处理**: 处理零值的特殊情况
- **甜甜圈图**: 使用doughnut类型创建仪表盘
- **半圆显示**: 配置180度显示角度
- **颜色配置**: 设置进度和背景颜色
- **响应式**: 支持响应式布局

### 6. 字段注册

```javascript
const gaugeField = {
    component: GaugeField,
    supportedOptions: [
        {
            label: _t("Title"),
            name: "title",
            type: "string",
        },
        {
            label: _t("Max value field"),
            name: "max_value",
            type: "field",
            availableTypes: ["integer", "float"],
        },
    ],
    extractProps: ({ options }) => ({
        maxValueField: options.max_field,
        title: options.title,
    }),
};

registry.category("fields").add("gauge", gaugeField);
```

**注册功能**:
- **组件注册**: 注册仪表盘字段组件
- **选项配置**: 支持标题和最大值字段选项
- **字段类型**: 最大值字段支持integer和float类型
- **属性提取**: 提取配置选项到组件属性

## 使用场景

### 1. 仪表盘字段管理器

```javascript
// 仪表盘字段管理器
class GaugeFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置仪表盘配置
        this.gaugeConfig = {
            enableAnimation: true,
            enableInteraction: true,
            enableCustomColors: true,
            enableThresholds: true,
            enableLabels: true,
            enableTooltips: true,
            enableExport: true,
            enableRealTime: false
        };
        
        // 设置颜色主题
        this.colorThemes = new Map([
            ['default', { progress: '#1f77b4', background: '#dddddd' }],
            ['success', { progress: '#28a745', background: '#e9ecef' }],
            ['warning', { progress: '#ffc107', background: '#f8f9fa' }],
            ['danger', { progress: '#dc3545', background: '#f8f9fa' }],
            ['info', { progress: '#17a2b8', background: '#e9ecef' }],
            ['gradient', { progress: 'linear-gradient(45deg, #1f77b4, #17a2b8)', background: '#dddddd' }]
        ]);
        
        // 设置阈值配置
        this.thresholdConfig = {
            enableThresholds: true,
            thresholds: [
                { value: 25, color: '#dc3545', label: 'Low' },
                { value: 50, color: '#ffc107', label: 'Medium' },
                { value: 75, color: '#28a745', label: 'Good' },
                { value: 100, color: '#007bff', label: 'Excellent' }
            ]
        };
        
        // 设置动画配置
        this.animationConfig = {
            duration: 1000,
            easing: 'easeOutQuart',
            delay: 0,
            loop: false
        };
        
        // 设置仪表盘统计
        this.gaugeStatistics = {
            totalGauges: 0,
            averageValue: 0,
            maxValue: 0,
            minValue: null,
            themeUsage: new Map(),
            renderTime: 0
        };
        
        this.initializeGaugeSystem();
    }
    
    // 初始化仪表盘系统
    initializeGaugeSystem() {
        // 创建增强的仪表盘字段
        this.createEnhancedGaugeField();
        
        // 设置图表系统
        this.setupChartSystem();
        
        // 设置主题系统
        this.setupThemeSystem();
        
        // 设置交互系统
        this.setupInteractionSystem();
    }
    
    // 创建增强的仪表盘字段
    createEnhancedGaugeField() {
        const originalField = GaugeField;
        
        this.EnhancedGaugeField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加主题功能
                this.addThemeFeatures();
                
                // 添加交互功能
                this.addInteractionFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    currentTheme: 'default',
                    thresholds: [],
                    isAnimating: false,
                    lastValue: null,
                    renderStartTime: null,
                    customConfig: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的图表渲染
                this.enhancedRenderChart = () => {
                    const startTime = performance.now();
                    this.enhancedState.renderStartTime = startTime;
                    this.enhancedState.isAnimating = true;
                    
                    try {
                        // 获取数值
                        const gaugeValue = this.props.record.data[this.props.name];
                        const maxValue = this.calculateMaxValue(gaugeValue);
                        
                        // 应用主题
                        const colors = this.getThemeColors();
                        
                        // 应用阈值
                        const progressColor = this.getProgressColor(gaugeValue, maxValue);
                        
                        // 创建配置
                        const config = this.createChartConfig(gaugeValue, maxValue, progressColor, colors);
                        
                        // 销毁旧图表
                        if (this.chart) {
                            this.chart.destroy();
                        }
                        
                        // 创建新图表
                        this.chart = new Chart(this.canvasRef.el, config);
                        
                        // 记录统计
                        this.recordRenderStatistics(gaugeValue);
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordRenderTime(endTime - startTime);
                        
                    } catch (error) {
                        this.handleRenderError(error);
                    } finally {
                        this.enhancedState.isAnimating = false;
                    }
                };
                
                // 计算最大值
                this.calculateMaxValue = (gaugeValue) => {
                    let maxValue = Math.max(gaugeValue, this.props.record.data[this.props.maxValueField] || 0);
                    
                    // 处理零值情况
                    if (gaugeValue === 0 && maxValue === 0) {
                        maxValue = 1;
                    }
                    
                    return maxValue;
                };
                
                // 获取主题颜色
                this.getThemeColors = () => {
                    return this.colorThemes.get(this.enhancedState.currentTheme) || 
                           this.colorThemes.get('default');
                };
                
                // 获取进度颜色
                this.getProgressColor = (value, maxValue) => {
                    if (!this.gaugeConfig.enableThresholds || !this.enhancedState.thresholds.length) {
                        return this.getThemeColors().progress;
                    }
                    
                    const percentage = (value / maxValue) * 100;
                    
                    for (const threshold of this.enhancedState.thresholds) {
                        if (percentage <= threshold.value) {
                            return threshold.color;
                        }
                    }
                    
                    return this.getThemeColors().progress;
                };
                
                // 创建图表配置
                this.createChartConfig = (gaugeValue, maxValue, progressColor, colors) => {
                    const baseConfig = {
                        type: "doughnut",
                        data: {
                            datasets: [
                                {
                                    data: [gaugeValue, maxValue - gaugeValue],
                                    backgroundColor: [progressColor, colors.background],
                                    borderWidth: 0,
                                    label: this.title,
                                },
                            ],
                        },
                        options: {
                            circumference: 180,
                            rotation: 270,
                            responsive: true,
                            maintainAspectRatio: false,
                            cutout: "70%",
                            layout: {
                                padding: 5,
                            },
                            plugins: {
                                title: {
                                    display: Boolean(this.title),
                                    text: this.title,
                                    padding: 4,
                                    font: {
                                        size: 14,
                                        weight: 'bold'
                                    }
                                },
                                tooltip: {
                                    enabled: this.gaugeConfig.enableTooltips,
                                    displayColors: false,
                                    callbacks: {
                                        label: (tooltipItem) => {
                                            if (tooltipItem.dataIndex === 0) {
                                                return `Value: ${this.formatValue(gaugeValue)}`;
                                            }
                                            return `Max: ${this.formatValue(maxValue)}`;
                                        },
                                    },
                                },
                                legend: {
                                    display: false
                                }
                            },
                            aspectRatio: 2,
                            animation: this.gaugeConfig.enableAnimation ? this.animationConfig : false
                        },
                    };
                    
                    // 应用自定义配置
                    if (this.enhancedState.customConfig) {
                        return this.mergeConfigs(baseConfig, this.enhancedState.customConfig);
                    }
                    
                    return baseConfig;
                };
                
                // 格式化数值
                this.formatValue = (value) => {
                    return formatFloat(value, {
                        humanReadable: true,
                        decimals: 1,
                    });
                };
                
                // 设置主题
                this.setTheme = (themeName) => {
                    if (this.colorThemes.has(themeName)) {
                        this.enhancedState.currentTheme = themeName;
                        this.enhancedRenderChart();
                        
                        // 记录主题使用
                        const usage = this.gaugeStatistics.themeUsage.get(themeName) || 0;
                        this.gaugeStatistics.themeUsage.set(themeName, usage + 1);
                    }
                };
                
                // 设置阈值
                this.setThresholds = (thresholds) => {
                    if (Array.isArray(thresholds)) {
                        this.enhancedState.thresholds = thresholds.sort((a, b) => a.value - b.value);
                        this.enhancedRenderChart();
                    }
                };
                
                // 设置自定义配置
                this.setCustomConfig = (config) => {
                    this.enhancedState.customConfig = config;
                    this.enhancedRenderChart();
                };
                
                // 获取仪表盘信息
                this.getGaugeInfo = () => {
                    const gaugeValue = this.props.record.data[this.props.name];
                    const maxValue = this.calculateMaxValue(gaugeValue);
                    const percentage = (gaugeValue / maxValue) * 100;
                    
                    return {
                        value: gaugeValue,
                        maxValue: maxValue,
                        percentage: percentage,
                        formattedValue: this.formatValue(gaugeValue),
                        formattedMaxValue: this.formatValue(maxValue),
                        theme: this.enhancedState.currentTheme,
                        thresholds: this.enhancedState.thresholds,
                        isAnimating: this.enhancedState.isAnimating
                    };
                };
                
                // 导出图表
                this.exportChart = (format = 'png') => {
                    if (!this.chart) return null;
                    
                    try {
                        return this.chart.toBase64Image(format);
                    } catch (error) {
                        console.error('Export error:', error);
                        return null;
                    }
                };
                
                // 更新数据
                this.updateData = (newValue, newMaxValue = null) => {
                    if (this.chart) {
                        const maxValue = newMaxValue || this.calculateMaxValue(newValue);
                        
                        this.chart.data.datasets[0].data = [newValue, maxValue - newValue];
                        this.chart.update();
                        
                        this.enhancedState.lastValue = newValue;
                    }
                };
                
                // 合并配置
                this.mergeConfigs = (baseConfig, customConfig) => {
                    // 深度合并配置对象
                    const merged = JSON.parse(JSON.stringify(baseConfig));
                    
                    for (const key in customConfig) {
                        if (typeof customConfig[key] === 'object' && !Array.isArray(customConfig[key])) {
                            merged[key] = { ...merged[key], ...customConfig[key] };
                        } else {
                            merged[key] = customConfig[key];
                        }
                    }
                    
                    return merged;
                };
                
                // 记录渲染统计
                this.recordRenderStatistics = (value) => {
                    this.gaugeStatistics.totalGauges++;
                    
                    if (value !== null && value !== undefined && !isNaN(value)) {
                        this.gaugeStatistics.averageValue = 
                            (this.gaugeStatistics.averageValue + value) / 2;
                        
                        if (this.gaugeStatistics.minValue === null || value < this.gaugeStatistics.minValue) {
                            this.gaugeStatistics.minValue = value;
                        }
                        
                        if (value > this.gaugeStatistics.maxValue) {
                            this.gaugeStatistics.maxValue = value;
                        }
                    }
                };
                
                // 处理渲染错误
                this.handleRenderError = (error) => {
                    console.error('Gauge render error:', error);
                };
                
                // 记录渲染时间
                this.recordRenderTime = (duration) => {
                    this.gaugeStatistics.renderTime = 
                        (this.gaugeStatistics.renderTime + duration) / 2;
                };
            }
            
            addThemeFeatures() {
                // 主题功能
                this.themeManager = {
                    enabled: this.gaugeConfig.enableCustomColors,
                    setTheme: (theme) => this.setTheme(theme),
                    getThemes: () => Array.from(this.colorThemes.keys()),
                    getCurrentTheme: () => this.enhancedState.currentTheme
                };
            }
            
            addInteractionFeatures() {
                // 交互功能
                this.interactionManager = {
                    enabled: this.gaugeConfig.enableInteraction,
                    export: (format) => this.exportChart(format),
                    update: (value, max) => this.updateData(value, max)
                };
            }
            
            // 重写原始方法
            renderChart() {
                this.enhancedRenderChart();
            }
        };
    }
    
    // 设置图表系统
    setupChartSystem() {
        this.chartSystemConfig = {
            enabled: true,
            library: 'chartjs',
            version: '3.x'
        };
    }
    
    // 设置主题系统
    setupThemeSystem() {
        this.themeSystemConfig = {
            enabled: this.gaugeConfig.enableCustomColors,
            themes: this.colorThemes,
            defaultTheme: 'default'
        };
    }
    
    // 设置交互系统
    setupInteractionSystem() {
        this.interactionSystemConfig = {
            enabled: this.gaugeConfig.enableInteraction,
            enableExport: this.gaugeConfig.enableExport,
            enableRealTime: this.gaugeConfig.enableRealTime
        };
    }
    
    // 创建仪表盘字段
    createGaugeField(props) {
        return new this.EnhancedGaugeField(props);
    }
    
    // 注册颜色主题
    registerColorTheme(name, colors) {
        this.colorThemes.set(name, colors);
    }
    
    // 批量设置主题
    batchSetTheme(fields, theme) {
        const results = [];
        
        for (const field of fields) {
            try {
                field.setTheme(theme);
                results.push({ field, success: true, theme });
            } catch (error) {
                results.push({ field, success: false, error });
            }
        }
        
        return results;
    }
    
    // 获取热门主题
    getPopularThemes(limit = 5) {
        const sorted = Array.from(this.gaugeStatistics.themeUsage.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, limit);
        
        return sorted.map(([theme, count]) => ({ theme, count }));
    }
    
    // 获取仪表盘统计
    getGaugeStatistics() {
        return {
            ...this.gaugeStatistics,
            themeCount: this.colorThemes.size,
            averageRenderTime: this.gaugeStatistics.renderTime
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理主题
        this.colorThemes.clear();
        
        // 重置统计
        this.gaugeStatistics = {
            totalGauges: 0,
            averageValue: 0,
            maxValue: 0,
            minValue: null,
            themeUsage: new Map(),
            renderTime: 0
        };
    }
}

// 使用示例
const gaugeManager = new GaugeFieldManager();

// 创建仪表盘字段
const gaugeField = gaugeManager.createGaugeField({
    name: 'progress',
    record: {
        data: { progress: 75, max_progress: 100 },
        fields: { progress: { type: 'float', string: 'Progress' } }
    },
    maxValueField: 'max_progress',
    title: 'Project Progress'
});

// 设置成功主题
gaugeField.setTheme('success');

// 注册自定义主题
gaugeManager.registerColorTheme('custom', {
    progress: '#6f42c1',
    background: '#f8f9fa'
});

// 获取统计信息
const stats = gaugeManager.getGaugeStatistics();
console.log('Gauge field statistics:', stats);
```

## 技术特点

### 1. 可视化图表
- **Chart.js集成**: 基于Chart.js的专业图表库
- **甜甜圈图**: 使用doughnut类型实现仪表盘效果
- **半圆显示**: 配置180度显示角度
- **响应式**: 支持响应式布局和缩放

### 2. 配置灵活
- **最大值字段**: 支持动态最大值配置
- **标题配置**: 可配置的图表标题
- **颜色定制**: 可定制的颜色方案
- **工具提示**: 丰富的工具提示信息

### 3. 资源管理
- **异步加载**: 异步加载Chart.js库
- **生命周期**: 完善的组件生命周期管理
- **内存清理**: 自动清理图表资源
- **性能优化**: 优化的渲染性能

### 4. 用户体验
- **直观显示**: 直观的进度显示
- **格式化**: 人性化的数值格式化
- **交互性**: 支持鼠标悬停交互
- **美观界面**: 美观的视觉效果

## 设计模式

### 1. 组件模式 (Component Pattern)
- **UI组件**: 封装仪表盘UI
- **状态管理**: 管理图表状态
- **生命周期**: 管理组件生命周期

### 2. 策略模式 (Strategy Pattern)
- **渲染策略**: 不同的图表渲染策略
- **主题策略**: 不同的颜色主题策略
- **格式策略**: 不同的数值格式策略

### 3. 观察者模式 (Observer Pattern)
- **数据观察**: 观察数据变化
- **状态观察**: 观察组件状态变化
- **事件处理**: 处理图表事件

### 4. 工厂模式 (Factory Pattern)
- **图表工厂**: 创建图表实例
- **配置工厂**: 创建图表配置
- **主题工厂**: 创建主题配置

## 注意事项

1. **资源加载**: 确保Chart.js库正确加载
2. **内存管理**: 及时清理图表资源避免内存泄漏
3. **数据验证**: 验证数值数据的有效性
4. **性能考虑**: 避免频繁的图表重绘

## 扩展建议

1. **动画效果**: 添加更丰富的动画效果
2. **主题系统**: 扩展主题和颜色系统
3. **交互功能**: 增强用户交互功能
4. **导出功能**: 支持图表导出功能
5. **实时更新**: 支持实时数据更新

该仪表盘字段为Odoo Web客户端提供了专业的数据可视化功能，通过Chart.js的强大图表能力和灵活的配置选项确保了优秀的数据展示效果和用户体验。

# FloatFactorField - 浮点因子字段

## 概述

`float_factor_field.js` 是 Odoo Web 客户端的浮点因子字段组件，负责处理带有因子转换的浮点数输入和显示。该模块包含48行代码，是一个继承自FloatField的特殊数值组件，专门用于处理需要因子转换的浮点数字段，具备因子乘除、值转换、配置扩展等特性，是单位转换和比例计算的重要组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/float_factor/float_factor_field.js`
- **行数**: 48
- **模块**: `@web/views/fields/float_factor/float_factor_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                    // 注册表
'@web/views/fields/float/float_field'   // 基础浮点数字段
'@web/core/l10n/translation'            // 翻译服务
```

## 核心功能

### 1. 组件定义

```javascript
const FloatFactorField = class FloatFactorField extends FloatField {
    static props = {
        ...FloatField.props,
        factor: { type: Number, optional: true },
    };
    static defaultProps = {
        ...FloatField.defaultProps,
        factor: 1,
    };
}
```

**组件特性**:
- **继承基类**: 继承FloatField的所有功能
- **因子属性**: 扩展factor属性用于数值转换
- **默认因子**: 默认因子为1，不改变原值
- **可选配置**: factor属性为可选配置

### 2. 值解析

```javascript
parse(value) {
    return super.parse(value) / this.props.factor;
}
```

**解析功能**:
- **基类解析**: 调用基类的解析方法
- **因子除法**: 将解析后的值除以因子
- **存储转换**: 将显示值转换为存储值
- **简洁实现**: 简洁的转换逻辑

### 3. 值获取

```javascript
get value() {
    return this.props.record.data[this.props.name] * this.props.factor;
}
```

**值获取功能**:
- **存储值**: 获取记录中的存储值
- **因子乘法**: 将存储值乘以因子
- **显示转换**: 将存储值转换为显示值
- **实时计算**: 实时计算显示值

### 4. 字段注册

```javascript
const floatFactorField = {
    ...floatField,
    component: FloatFactorField,
    supportedOptions: [
        ...floatField.supportedOptions,
        {
            label: _t("Factor"),
            name: "factor",
            type: "number",
        },
    ],
    extractProps({ options }) {
        const props = floatField.extractProps(...arguments);
        props.factor = options.factor;
        return props;
    },
};

registry.category("fields").add("float_factor", floatFactorField);
```

**注册功能**:
- **配置继承**: 继承浮点数字段的所有配置
- **组件替换**: 使用FloatFactorField组件
- **选项扩展**: 扩展factor选项配置
- **属性提取**: 提取factor属性到组件属性

## 使用场景

### 1. 浮点因子字段管理器

```javascript
// 浮点因子字段管理器
class FloatFactorFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置因子字段配置
        this.factorConfig = {
            enableValidation: true,
            enableUnitDisplay: true,
            enableConversionHistory: true,
            enableFactorCalculation: true,
            enableRangeCheck: true,
            enablePrecisionControl: true,
            enableBatchConversion: true,
            enableFactorPresets: true
        };
        
        // 设置因子预设
        this.factorPresets = new Map([
            ['percentage', { factor: 100, unit: '%', description: 'Percentage conversion' }],
            ['thousand', { factor: 1000, unit: 'K', description: 'Thousands' }],
            ['million', { factor: 1000000, unit: 'M', description: 'Millions' }],
            ['meter_to_cm', { factor: 100, unit: 'cm', description: 'Meters to centimeters' }],
            ['kg_to_g', { factor: 1000, unit: 'g', description: 'Kilograms to grams' }],
            ['hour_to_min', { factor: 60, unit: 'min', description: 'Hours to minutes' }]
        ]);
        
        // 设置单位转换表
        this.unitConversions = new Map([
            ['length', {
                base: 'm',
                conversions: {
                    'mm': 1000,
                    'cm': 100,
                    'm': 1,
                    'km': 0.001
                }
            }],
            ['weight', {
                base: 'kg',
                conversions: {
                    'g': 1000,
                    'kg': 1,
                    't': 0.001
                }
            }],
            ['time', {
                base: 'h',
                conversions: {
                    's': 3600,
                    'min': 60,
                    'h': 1,
                    'd': 1/24
                }
            }]
        ]);
        
        // 设置因子统计
        this.factorStatistics = {
            totalConversions: 0,
            factorUsage: new Map(),
            averageConversionTime: 0,
            conversionErrors: 0,
            popularFactors: new Map()
        };
        
        this.initializeFactorSystem();
    }
    
    // 初始化因子系统
    initializeFactorSystem() {
        // 创建增强的浮点因子字段
        this.createEnhancedFloatFactorField();
        
        // 设置转换系统
        this.setupConversionSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置预设管理
        this.setupPresetManagement();
    }
    
    // 创建增强的浮点因子字段
    createEnhancedFloatFactorField() {
        const originalField = FloatFactorField;
        
        this.EnhancedFloatFactorField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加转换功能
                this.addConversionFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    conversionHistory: [],
                    currentUnit: null,
                    targetUnit: null,
                    validationErrors: [],
                    isConverting: false,
                    factorInfo: null
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的值解析
                this.enhancedParse = (value) => {
                    const startTime = performance.now();
                    
                    try {
                        // 验证输入值
                        this.validateInput(value);
                        
                        // 执行原始解析
                        const parsedValue = this.parse(value);
                        
                        // 记录转换历史
                        this.recordConversion(value, parsedValue, 'parse');
                        
                        // 记录统计
                        this.recordConversionStatistics();
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordConversionTime(endTime - startTime);
                        
                        return parsedValue;
                        
                    } catch (error) {
                        this.handleConversionError(error);
                        return null;
                    }
                };
                
                // 增强的值获取
                this.enhancedGetValue = () => {
                    try {
                        // 获取原始值
                        const rawValue = this.props.record.data[this.props.name];
                        
                        if (rawValue === null || rawValue === undefined) {
                            return rawValue;
                        }
                        
                        // 应用因子转换
                        const convertedValue = rawValue * this.props.factor;
                        
                        // 记录转换历史
                        this.recordConversion(rawValue, convertedValue, 'display');
                        
                        return convertedValue;
                        
                    } catch (error) {
                        this.handleConversionError(error);
                        return 0;
                    }
                };
                
                // 验证输入值
                this.validateInput = (value) => {
                    const errors = [];
                    
                    if (value === null || value === undefined || value === '') {
                        return; // 空值允许
                    }
                    
                    // 数值验证
                    if (isNaN(value)) {
                        errors.push('Value must be a number');
                    }
                    
                    // 无穷大验证
                    if (!isFinite(value)) {
                        errors.push('Value cannot be infinite');
                    }
                    
                    // 因子验证
                    if (this.props.factor === 0) {
                        errors.push('Factor cannot be zero');
                    }
                    
                    // 范围验证
                    if (this.factorConfig.enableRangeCheck) {
                        const convertedValue = value / this.props.factor;
                        if (Math.abs(convertedValue) > Number.MAX_SAFE_INTEGER) {
                            errors.push('Converted value exceeds safe integer range');
                        }
                    }
                    
                    this.enhancedState.validationErrors = errors;
                    
                    if (errors.length > 0) {
                        this.factorStatistics.conversionErrors++;
                        throw new Error(errors.join(', '));
                    }
                };
                
                // 记录转换
                this.recordConversion = (inputValue, outputValue, type) => {
                    if (!this.factorConfig.enableConversionHistory) return;
                    
                    const conversionEntry = {
                        inputValue: inputValue,
                        outputValue: outputValue,
                        factor: this.props.factor,
                        type: type, // 'parse' or 'display'
                        timestamp: Date.now()
                    };
                    
                    this.enhancedState.conversionHistory.unshift(conversionEntry);
                    
                    // 限制历史大小
                    if (this.enhancedState.conversionHistory.length > 50) {
                        this.enhancedState.conversionHistory.pop();
                    }
                };
                
                // 设置因子
                this.setFactor = (factor) => {
                    if (typeof factor !== 'number' || factor === 0) {
                        throw new Error('Factor must be a non-zero number');
                    }
                    
                    const oldFactor = this.props.factor;
                    this.props.factor = factor;
                    
                    // 记录因子变更
                    this.recordFactorChange(oldFactor, factor);
                };
                
                // 应用因子预设
                this.applyFactorPreset = (presetName) => {
                    const preset = this.factorPresets.get(presetName);
                    if (!preset) {
                        throw new Error(`Unknown factor preset: ${presetName}`);
                    }
                    
                    this.setFactor(preset.factor);
                    this.enhancedState.currentUnit = preset.unit;
                    this.enhancedState.factorInfo = preset;
                    
                    // 记录预设使用
                    const usage = this.factorStatistics.factorUsage.get(presetName) || 0;
                    this.factorStatistics.factorUsage.set(presetName, usage + 1);
                };
                
                // 单位转换
                this.convertUnit = (value, fromUnit, toUnit, category) => {
                    const conversion = this.unitConversions.get(category);
                    if (!conversion) {
                        throw new Error(`Unknown unit category: ${category}`);
                    }
                    
                    const fromFactor = conversion.conversions[fromUnit];
                    const toFactor = conversion.conversions[toUnit];
                    
                    if (fromFactor === undefined || toFactor === undefined) {
                        throw new Error(`Unknown unit in category ${category}`);
                    }
                    
                    // 转换为基础单位，然后转换为目标单位
                    const baseValue = value / fromFactor;
                    return baseValue * toFactor;
                };
                
                // 计算逆因子
                this.calculateInverseFactor = () => {
                    if (this.props.factor === 0) {
                        throw new Error('Cannot calculate inverse of zero factor');
                    }
                    return 1 / this.props.factor;
                };
                
                // 获取转换信息
                this.getConversionInfo = () => {
                    const rawValue = this.props.record.data[this.props.name];
                    const displayValue = this.enhancedGetValue();
                    
                    return {
                        rawValue: rawValue,
                        displayValue: displayValue,
                        factor: this.props.factor,
                        inverseFactor: this.calculateInverseFactor(),
                        unit: this.enhancedState.currentUnit,
                        factorInfo: this.enhancedState.factorInfo,
                        conversionRatio: `1:${this.props.factor}`
                    };
                };
                
                // 批量转换
                this.batchConvert = (values, factor = this.props.factor) => {
                    const results = [];
                    
                    for (const value of values) {
                        try {
                            const converted = value * factor;
                            results.push({ 
                                input: value, 
                                output: converted, 
                                factor: factor,
                                success: true 
                            });
                        } catch (error) {
                            results.push({ 
                                input: value, 
                                output: null, 
                                factor: factor,
                                success: false, 
                                error: error.message 
                            });
                        }
                    }
                    
                    return results;
                };
                
                // 格式化显示值
                this.formatWithUnit = (value) => {
                    let formatted = value;
                    
                    // 应用数字格式化
                    if (this.props.formatNumber) {
                        formatted = this.formattedValue;
                    }
                    
                    // 添加单位
                    if (this.enhancedState.currentUnit) {
                        formatted = `${formatted} ${this.enhancedState.currentUnit}`;
                    }
                    
                    return formatted;
                };
                
                // 获取转换历史
                this.getConversionHistory = () => {
                    return this.enhancedState.conversionHistory;
                };
                
                // 清除转换历史
                this.clearConversionHistory = () => {
                    this.enhancedState.conversionHistory = [];
                };
                
                // 获取因子建议
                this.getFactorSuggestions = (targetValue, currentValue) => {
                    if (currentValue === 0) return [];
                    
                    const suggestedFactor = targetValue / currentValue;
                    const suggestions = [];
                    
                    // 查找接近的预设因子
                    for (const [name, preset] of this.factorPresets) {
                        const difference = Math.abs(preset.factor - suggestedFactor);
                        const tolerance = suggestedFactor * 0.1; // 10% tolerance
                        
                        if (difference <= tolerance) {
                            suggestions.push({
                                name: name,
                                factor: preset.factor,
                                unit: preset.unit,
                                description: preset.description,
                                difference: difference
                            });
                        }
                    }
                    
                    // 按差异排序
                    suggestions.sort((a, b) => a.difference - b.difference);
                    
                    return suggestions;
                };
                
                // 记录因子变更
                this.recordFactorChange = (oldFactor, newFactor) => {
                    console.log(`Factor changed from ${oldFactor} to ${newFactor}`);
                };
                
                // 记录转换统计
                this.recordConversionStatistics = () => {
                    this.factorStatistics.totalConversions++;
                    
                    // 记录热门因子
                    const factor = this.props.factor;
                    const count = this.factorStatistics.popularFactors.get(factor) || 0;
                    this.factorStatistics.popularFactors.set(factor, count + 1);
                };
                
                // 处理转换错误
                this.handleConversionError = (error) => {
                    console.error('Factor conversion error:', error);
                    this.factorStatistics.conversionErrors++;
                };
                
                // 记录转换时间
                this.recordConversionTime = (duration) => {
                    this.factorStatistics.averageConversionTime = 
                        (this.factorStatistics.averageConversionTime + duration) / 2;
                };
            }
            
            addConversionFeatures() {
                // 转换功能
                this.conversionManager = {
                    enabled: this.factorConfig.enableFactorCalculation,
                    setFactor: (factor) => this.setFactor(factor),
                    applyPreset: (preset) => this.applyFactorPreset(preset),
                    convertUnit: (value, from, to, category) => this.convertUnit(value, from, to, category)
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.factorConfig.enableValidation,
                    validate: (value) => this.validateInput(value),
                    getErrors: () => this.enhancedState.validationErrors
                };
            }
            
            // 重写原始方法
            parse(value) {
                return this.enhancedParse(value);
            }
            
            get value() {
                return this.enhancedGetValue();
            }
        };
    }
    
    // 设置转换系统
    setupConversionSystem() {
        this.conversionSystemConfig = {
            enabled: this.factorConfig.enableFactorCalculation,
            presets: this.factorPresets,
            unitConversions: this.unitConversions
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationSystemConfig = {
            enabled: this.factorConfig.enableValidation,
            enableRangeCheck: this.factorConfig.enableRangeCheck
        };
    }
    
    // 设置预设管理
    setupPresetManagement() {
        this.presetManagementConfig = {
            enabled: this.factorConfig.enableFactorPresets,
            presets: this.factorPresets
        };
    }
    
    // 创建浮点因子字段
    createFloatFactorField(props) {
        return new this.EnhancedFloatFactorField(props);
    }
    
    // 注册因子预设
    registerFactorPreset(name, factor, unit, description) {
        this.factorPresets.set(name, {
            factor: factor,
            unit: unit,
            description: description
        });
    }
    
    // 注册单位转换
    registerUnitConversion(category, baseUnit, conversions) {
        this.unitConversions.set(category, {
            base: baseUnit,
            conversions: conversions
        });
    }
    
    // 批量设置因子
    batchSetFactor(fields, factor) {
        const results = [];
        
        for (const field of fields) {
            try {
                field.setFactor(factor);
                results.push({ field, success: true, factor });
            } catch (error) {
                results.push({ field, success: false, error });
            }
        }
        
        return results;
    }
    
    // 获取热门因子
    getPopularFactors(limit = 10) {
        const sorted = Array.from(this.factorStatistics.popularFactors.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, limit);
        
        return sorted.map(([factor, count]) => ({ factor, count }));
    }
    
    // 获取因子统计
    getFactorStatistics() {
        return {
            ...this.factorStatistics,
            presetCount: this.factorPresets.size,
            unitConversionCount: this.unitConversions.size,
            errorRate: this.factorStatistics.conversionErrors / Math.max(this.factorStatistics.totalConversions, 1) * 100
        };
    }
    
    // 导出配置
    exportConfiguration() {
        return {
            factorPresets: Object.fromEntries(this.factorPresets),
            unitConversions: Object.fromEntries(this.unitConversions),
            config: this.factorConfig
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理预设和转换
        this.factorPresets.clear();
        this.unitConversions.clear();
        
        // 重置统计
        this.factorStatistics = {
            totalConversions: 0,
            factorUsage: new Map(),
            averageConversionTime: 0,
            conversionErrors: 0,
            popularFactors: new Map()
        };
    }
}

// 使用示例
const factorManager = new FloatFactorFieldManager();

// 创建浮点因子字段
const factorField = factorManager.createFloatFactorField({
    name: 'percentage',
    record: {
        data: { percentage: 0.85 }, // 存储为小数
        fields: { percentage: { type: 'float' } }
    },
    factor: 100 // 显示为百分比
});

// 应用百分比预设
factorField.applyFactorPreset('percentage');

// 注册自定义因子预设
factorManager.registerFactorPreset('custom_ratio', 1.5, 'x', 'Custom ratio conversion');

// 获取统计信息
const stats = factorManager.getFactorStatistics();
console.log('Float factor field statistics:', stats);
```

## 技术特点

### 1. 因子转换
- **双向转换**: 支持显示值和存储值的双向转换
- **因子配置**: 可配置的转换因子
- **精确计算**: 精确的数值转换计算
- **默认处理**: 合理的默认因子设置

### 2. 继承扩展
- **基类继承**: 继承FloatField的所有功能
- **方法重写**: 重写parse和value方法
- **属性扩展**: 扩展factor相关属性
- **配置继承**: 继承基类的所有配置

### 3. 简洁高效
- **最小实现**: 最简化的代码实现
- **专注转换**: 专注于因子转换功能
- **性能优化**: 高效的转换计算
- **内存友好**: 最小的内存占用

### 4. 配置灵活
- **因子选项**: 支持factor选项配置
- **属性提取**: 智能的属性提取
- **默认值**: 合理的默认配置
- **动态调整**: 支持动态因子调整

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **类继承**: 继承FloatField基类
- **功能扩展**: 扩展因子转换功能
- **方法重写**: 重写关键方法

### 2. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 为基础浮点字段添加因子转换
- **透明扩展**: 透明地扩展基类功能
- **行为增强**: 增强数值处理行为

### 3. 策略模式 (Strategy Pattern)
- **转换策略**: 不同的因子转换策略
- **计算策略**: 不同的数值计算策略
- **显示策略**: 不同的值显示策略

### 4. 工厂模式 (Factory Pattern)
- **字段工厂**: 创建因子字段
- **转换器工厂**: 创建转换器
- **配置工厂**: 创建配置对象

## 注意事项

1. **因子验证**: 确保因子不为零避免除零错误
2. **精度问题**: 注意浮点数运算的精度问题
3. **数值范围**: 避免转换后的数值超出安全范围
4. **用户理解**: 确保用户理解显示值和存储值的关系

## 扩展建议

1. **单位显示**: 添加单位显示功能
2. **转换历史**: 支持转换历史记录
3. **预设因子**: 提供常用因子预设
4. **批量转换**: 支持批量因子转换
5. **智能建议**: 基于上下文的因子建议

该浮点因子字段为Odoo Web客户端提供了灵活的数值因子转换功能，通过简洁的实现和强大的转换能力确保了在需要单位转换和比例计算场景下的良好适用性。

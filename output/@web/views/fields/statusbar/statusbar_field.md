# StatusBarField - 状态栏字段

## 概述

`statusbar_field.js` 是 Odoo Web 客户端的状态栏字段组件，负责在表单视图中显示工作流状态栏。该模块包含364行代码，是一个功能复杂的状态栏组件，专门用于显示和管理工作流状态，具备状态折叠、可见性控制、快捷键操作、下拉选择、动态数据加载等特性，是工作流可视化和状态管理的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/statusbar/statusbar_field.js`
- **行数**: 364
- **模块**: `@web/views/fields/statusbar/statusbar_field`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                            // OWL框架
'@web/core/browser/browser'             // 浏览器服务
'@web/core/commands/command_hook'       // 命令钩子
'@web/core/domain'                      // 域工具
'@web/core/dropdown/dropdown'           // 下拉菜单
'@web/core/dropdown/dropdown_item'      // 下拉菜单项
'@web/core/l10n/translation'            // 翻译服务
'@web/core/registry'                    // 注册表
'@web/core/utils/arrays'                // 数组工具
'@web/core/utils/strings'               // 字符串工具
'@web/core/utils/timing'                // 时间工具
'@web/model/relational_model/utils'     // 关系模型工具
'@web/views/fields/relational_utils'    // 关系字段工具
'@web/views/fields/standard_field_props' // 标准字段属性
```

## 核心功能

### 1. 类型定义

```javascript
/**
 * @typedef StatusBarFieldProps
 * @property {[Array, Function]} domain - 域过滤条件
 * @property {string} foldField - 折叠字段名
 * @property {boolean} isDisabled - 是否禁用
 * @property {string[]} visibleSelection - 可见选择项
 * @property {boolean} withCommand - 是否启用命令
 */

/**
 * @typedef StatusBarItem
 * @property {number} value - 状态值
 * @property {string} label - 状态标签
 * @property {boolean} isFolded - 是否折叠
 * @property {boolean} isSelected - 是否选中
 */

/**
 * @typedef StatusBarList
 * @property {string} label - 列表标签
 * @property {StatusBarItem[]} items - 状态项列表
 */
```

**类型定义特点**:
- **属性扩展**: 扩展标准字段属性
- **状态项**: 定义状态项的结构
- **状态列表**: 定义状态列表的结构
- **类型安全**: 提供完整的类型定义

### 2. 组件定义

```javascript
const StatusBarField = class StatusBarField extends Component {
    static template = "web.StatusBarField";
    static components = {
        Dropdown,
        DropdownItem,
    };
    static props = {
        ...standardFieldProps,
        domain: { type: [Array, Function], optional: true },
        foldField: { type: String, optional: true },
        isDisabled: { type: Boolean, optional: true },
        visibleSelection: { type: Array, optional: true },
        withCommand: { type: Boolean, optional: true },
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **域过滤**: 支持domain配置域过滤条件
- **折叠字段**: 支持foldField配置折叠状态字段
- **禁用状态**: 支持isDisabled配置禁用状态
- **可见选择**: 支持visibleSelection配置可见选项
- **命令支持**: 支持withCommand配置快捷键命令

### 3. 组件初始化

```javascript
setup() {
    this.statusBarRef = useRef("statusBar");
    this.type = this.props.record.fields[this.props.name].type;

    if (this.type === "many2one") {
        this.specialData = useSpecialData(async (orm, props) => {
            const { relation } = props.record.fields[props.name];
            const domain = getFieldDomain(props.record, props.name, props.domain);
            const kwargs = {
                specification: { display_name: 1 },
                domain,
            };
            if (props.foldField) {
                kwargs.specification[props.foldField] = 1;
            }
            const { records } = await orm.call(relation, "web_search_read", [], kwargs);
            return records.map((record) => [record.id, record.display_name, record[props.foldField]]);
        });
    }

    // 设置快捷键命令
    if (this.props.withCommand) {
        this.setupCommands();
    }

    // 设置响应式布局
    this.setupResponsiveLayout();
}
```

**初始化功能**:
- **引用管理**: 管理状态栏DOM引用
- **类型检测**: 检测字段类型
- **动态数据**: many2one类型使用动态数据加载
- **折叠支持**: 支持折叠字段的加载
- **命令设置**: 设置快捷键命令
- **响应式**: 设置响应式布局

### 4. 状态项管理

```javascript
get items() {
    switch (this.type) {
        case "many2one":
            return this.specialData.data || [];
        case "selection":
            const selection = this.props.record.fields[this.props.name].selection || [];
            return selection.map(([value, label]) => [value, label, false]);
        default:
            return [];
    }
}

get statusBarItems() {
    const items = this.items;
    const currentValue = this.props.record.data[this.props.name];
    const visibleSelection = this.props.visibleSelection;

    return items
        .filter(([value, label]) => {
            if (visibleSelection && visibleSelection.length > 0) {
                return visibleSelection.includes(String(value)) || value === currentValue;
            }
            return true;
        })
        .map(([value, label, isFolded]) => ({
            value,
            label,
            isFolded: isFolded || false,
            isSelected: value === currentValue,
        }));
}
```

**状态项功能**:
- **类型适配**: 根据字段类型获取状态项
- **可见性过滤**: 根据visibleSelection过滤可见项
- **当前值**: 始终显示当前选中的值
- **折叠状态**: 处理状态项的折叠状态
- **选中标记**: 标记当前选中的状态

### 5. 响应式布局

```javascript
setupResponsiveLayout() {
    const throttledResize = throttleForAnimation(() => {
        this.updateLayout();
    });

    useExternalListener(browser.window, "resize", throttledResize);

    onWillRender(() => {
        this.updateLayout();
    });

    useEffect(() => {
        this.updateLayout();
    });
}

updateLayout() {
    if (!this.statusBarRef.el) {
        return;
    }

    const container = this.statusBarRef.el;
    const items = container.querySelectorAll('.o_statusbar_status');
    const containerWidth = container.offsetWidth;
    let totalWidth = 0;

    // 计算所有项目的总宽度
    items.forEach(item => {
        totalWidth += item.offsetWidth;
    });

    // 如果总宽度超过容器宽度，显示下拉菜单
    if (totalWidth > containerWidth) {
        this.showDropdown = true;
        this.hideOverflowItems();
    } else {
        this.showDropdown = false;
        this.showAllItems();
    }
}
```

**布局功能**:
- **响应式**: 根据容器宽度调整布局
- **溢出处理**: 处理状态项溢出的情况
- **下拉显示**: 溢出时显示下拉菜单
- **动态调整**: 动态调整状态项的显示

## 使用场景

### 1. 状态栏字段管理器

```javascript
// 状态栏字段管理器
class StatusBarFieldManager {
    constructor() {
        this.setupManager();
    }

    setupManager() {
        // 设置状态栏字段配置
        this.statusBarConfig = {
            enableResponsiveLayout: true,
            enableFolding: true,
            enableCommands: true,
            enableVisibilityControl: true,
            enableAnimations: true,
            enableTooltips: true,
            enableCustomStyling: false,
            enableBulkOperations: false
        };

        // 设置布局选项
        this.layoutOptions = {
            maxVisibleItems: 5,
            minItemWidth: 80,
            itemSpacing: 8,
            enableCompactMode: false,
            enableVerticalLayout: false,
            breakpoints: {
                sm: 576,
                md: 768,
                lg: 992,
                xl: 1200
            }
        };

        // 设置状态样式
        this.statusStyles = new Map([
            ['draft', { color: '#6c757d', background: '#f8f9fa' }],
            ['pending', { color: '#fd7e14', background: '#fff3cd' }],
            ['approved', { color: '#198754', background: '#d1e7dd' }],
            ['rejected', { color: '#dc3545', background: '#f8d7da' }],
            ['cancelled', { color: '#6f42c1', background: '#e2e3f3' }]
        ]);

        // 设置快捷键映射
        this.commandMappings = new Map([
            ['next', { key: 'ArrowRight', description: 'Move to next status' }],
            ['previous', { key: 'ArrowLeft', description: 'Move to previous status' }],
            ['first', { key: 'Home', description: 'Move to first status' }],
            ['last', { key: 'End', description: 'Move to last status' }]
        ]);

        // 设置状态栏统计
        this.statusBarStatistics = {
            totalStatusBarFields: 0,
            totalStatusChanges: 0,
            statusChangesByField: new Map(),
            averageStatusesPerBar: 0,
            mostUsedStatus: null,
            layoutChanges: 0,
            responsiveActivations: 0
        };

        this.initializeStatusBarSystem();
    }

    // 初始化状态栏系统
    initializeStatusBarSystem() {
        // 创建增强的状态栏字段
        this.createEnhancedStatusBarField();

        // 设置布局系统
        this.setupLayoutSystem();

        // 设置命令系统
        this.setupCommandSystem();

        // 设置样式系统
        this.setupStyleSystem();
    }
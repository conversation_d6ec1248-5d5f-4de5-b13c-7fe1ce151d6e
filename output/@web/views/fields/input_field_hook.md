# InputFieldHook - 输入字段钩子

## 概述

`input_field_hook.js` 是 Odoo Web 客户端字段系统的输入字段钩子模块，负责管理输入字段的状态同步和用户交互。该模块包含185行代码，是一个OWL钩子函数，专门用于防止用户正在编辑的字段值被模型更新（如onchange）意外清除，具备脏数据检测、值同步、事件处理、紧急保存等特性，是输入类字段组件的核心工具。

## 文件信息
- **路径**: `/web/static/src/views/fields/input_field_hook.js`
- **行数**: 185
- **模块**: `@web/views/fields/input_field_hook`

## 依赖关系

```javascript
// 核心依赖
'@web/core/hotkeys/hotkey_service'      // 热键服务
'@web/core/utils/hooks'                 // 工具钩子
'@odoo/owl'                            // OWL框架
```

## 核心功能

### 1. 钩子函数定义

```javascript
function useInputField(params) {
    const inputRef = params.ref || useRef(params.refName || "input");
    const component = useComponent();
    
    let isDirty = false;
    let lastSetValue = null;
    let pendingUpdate = false;
    
    // ... 实现逻辑
    
    return inputRef;
}
```

**钩子参数**:
- `getValue`: 返回字段值的函数
- `ref`: 可选的输入元素引用
- `refName`: 输入元素引用名称（默认"input"）
- `preventLineBreaks`: 是否阻止换行符
- `parse`: 可选的值解析函数

### 2. 状态管理

```javascript
let isDirty = false;        // 字段是否为脏数据
let lastSetValue = null;    // 最后提交到模型的值
let pendingUpdate = false;  // 是否有待处理的更新
```

**状态说明**:
- **isDirty**: 字段是否与模型不同步
- **lastSetValue**: 最后成功提交的值
- **pendingUpdate**: 跟踪未确认的更改

### 3. 输入事件处理

```javascript
function onInput(ev) {
    isDirty = ev.target.value !== lastSetValue;
    if (params.preventLineBreaks && ev.inputType === "insertFromPaste") {
        ev.target.value = ev.target.value.replace(/[\r\n]+/g, " ");
    }
    component.props.record.model.bus.trigger("FIELD_IS_DIRTY", isDirty);
    if (!component.props.record.isValid) {
        component.props.record.resetFieldValidity(component.props.name);
    }
}
```

**输入处理功能**:
- **脏数据检测**: 检测输入值是否与最后设置值不同
- **换行处理**: 可选的换行符替换功能
- **状态广播**: 广播字段脏数据状态
- **验证重置**: 重置字段验证状态

### 4. 变更事件处理

```javascript
async function onChange(ev) {
    if (isDirty) {
        isDirty = false;
        let isInvalid = false;
        let val = ev.target.value;
        
        if (params.parse) {
            try {
                val = params.parse(val);
            } catch {
                component.props.record.setInvalidField(component.props.name);
                isInvalid = true;
            }
        }

        if (!isInvalid) {
            if (val !== component.props.record.data[component.props.name]) {
                lastSetValue = inputRef.el.value;
                pendingUpdate = true;
                await component.props.record.update({ [component.props.name]: val });
                pendingUpdate = false;
                component.props.record.model.bus.trigger("FIELD_IS_DIRTY", isDirty);
            } else {
                inputRef.el.value = params.getValue();
            }
        }
    }
}
```

**变更处理功能**:
- **脏数据检查**: 只处理脏数据状态的字段
- **值解析**: 可选的值解析和验证
- **模型更新**: 更新模型中的字段值
- **状态同步**: 同步字段和模型状态

### 5. 键盘事件处理

```javascript
function onKeydown(ev) {
    const hotkey = getActiveHotkey(ev);
    if (["enter", "tab", "shift+tab"].includes(hotkey)) {
        commitChanges(false);
    }
    if (params.preventLineBreaks && ["enter", "shift+enter"].includes(hotkey)) {
        ev.preventDefault();
    }
}
```

**键盘处理功能**:
- **热键检测**: 检测特定的热键组合
- **提交触发**: 在特定键盘事件时提交更改
- **换行阻止**: 可选的换行符阻止功能
- **事件阻止**: 阻止默认的键盘行为

### 6. 紧急提交

```javascript
async function commitChanges(urgent) {
    if (!inputRef.el) {
        return;
    }

    isDirty = inputRef.el.value !== lastSetValue;
    if (isDirty || (urgent && pendingUpdate)) {
        let isInvalid = false;
        isDirty = false;
        let val = inputRef.el.value;
        
        if (params.parse) {
            try {
                val = params.parse(val);
            } catch {
                isInvalid = true;
                if (urgent) {
                    return;
                } else {
                    component.props.record.setInvalidField(component.props.name);
                }
            }
        }

        if (isInvalid) {
            return;
        }

        if ((val || false) !== (component.props.record.data[component.props.name] || false)) {
            lastSetValue = inputRef.el.value;
            await component.props.record.update({ [component.props.name]: val });
            component.props.record.model.bus.trigger("FIELD_IS_DIRTY", false);
        } else {
            inputRef.el.value = params.getValue();
        }
    }
}
```

**紧急提交功能**:
- **紧急模式**: 支持紧急保存模式
- **待处理检查**: 检查是否有待处理的更新
- **错误处理**: 紧急模式下的特殊错误处理
- **强制同步**: 强制同步字段和模型状态

## 使用场景

### 1. 输入字段钩子管理器

```javascript
// 输入字段钩子管理器
class InputFieldHookManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置钩子配置
        this.hookConfig = {
            enableDirtyTracking: true,
            enableAutoCommit: true,
            enableValidation: true,
            enableHotkeys: true,
            commitDelay: 300,
            maxRetries: 3,
            enableDebugMode: false,
            customParsers: new Map()
        };
        
        // 设置钩子注册表
        this.hookRegistry = new Map();
        
        // 设置状态跟踪
        this.stateTracker = new Map();
        
        // 设置钩子统计
        this.hookStatistics = {
            totalHooks: 0,
            activeHooks: 0,
            dirtyFields: 0,
            commitCount: 0,
            errorCount: 0,
            averageCommitTime: 0
        };
        
        this.initializeHookSystem();
    }
    
    // 初始化钩子系统
    initializeHookSystem() {
        // 创建增强的输入字段钩子
        this.createEnhancedInputFieldHook();
        
        // 设置状态管理系统
        this.setupStateManagementSystem();
        
        // 设置验证系统
        this.setupValidationSystem();
        
        // 设置性能监控
        this.setupPerformanceMonitoring();
    }
    
    // 创建增强的输入字段钩子
    createEnhancedInputFieldHook() {
        this.enhancedUseInputField = (params) => {
            const hookId = this.generateHookId();
            const originalHook = useInputField(params);
            
            // 注册钩子
            this.registerHook(hookId, params);
            
            // 扩展钩子功能
            const enhancedHook = {
                ...originalHook,
                hookId: hookId,
                
                // 增强的状态管理
                enhancedStateManager: {
                    isDirty: false,
                    lastValue: null,
                    pendingUpdate: false,
                    validationErrors: [],
                    commitHistory: [],
                    
                    // 设置脏数据状态
                    setDirty: (dirty) => {
                        this.enhancedStateManager.isDirty = dirty;
                        this.updateHookState(hookId, 'isDirty', dirty);
                        
                        if (dirty) {
                            this.hookStatistics.dirtyFields++;
                        } else {
                            this.hookStatistics.dirtyFields = Math.max(0, this.hookStatistics.dirtyFields - 1);
                        }
                    },
                    
                    // 获取状态
                    getState: () => ({
                        isDirty: this.enhancedStateManager.isDirty,
                        lastValue: this.enhancedStateManager.lastValue,
                        pendingUpdate: this.enhancedStateManager.pendingUpdate,
                        validationErrors: this.enhancedStateManager.validationErrors,
                        commitHistory: this.enhancedStateManager.commitHistory
                    }),
                    
                    // 重置状态
                    resetState: () => {
                        this.enhancedStateManager.isDirty = false;
                        this.enhancedStateManager.lastValue = null;
                        this.enhancedStateManager.pendingUpdate = false;
                        this.enhancedStateManager.validationErrors = [];
                    }
                },
                
                // 增强的输入处理
                enhancedOnInput: (ev) => {
                    const startTime = performance.now();
                    
                    try {
                        // 执行原始输入处理
                        this.handleInput(ev, hookId, params);
                        
                        // 记录输入事件
                        this.recordInputEvent(hookId, ev);
                        
                        // 自动验证
                        if (this.hookConfig.enableValidation) {
                            this.validateInput(hookId, ev.target.value);
                        }
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordPerformance('input', endTime - startTime);
                        
                    } catch (error) {
                        this.handleInputError(hookId, error);
                    }
                },
                
                // 增强的变更处理
                enhancedOnChange: async (ev) => {
                    const startTime = performance.now();
                    
                    try {
                        // 执行原始变更处理
                        await this.handleChange(ev, hookId, params);
                        
                        // 记录提交
                        this.recordCommit(hookId, ev.target.value);
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordPerformance('change', endTime - startTime);
                        
                    } catch (error) {
                        this.handleChangeError(hookId, error);
                    }
                },
                
                // 增强的键盘处理
                enhancedOnKeydown: (ev) => {
                    try {
                        // 执行原始键盘处理
                        this.handleKeydown(ev, hookId, params);
                        
                        // 记录键盘事件
                        this.recordKeyboardEvent(hookId, ev);
                        
                    } catch (error) {
                        this.handleKeyboardError(hookId, error);
                    }
                },
                
                // 批量提交
                batchCommit: async (fields) => {
                    const commitPromises = [];
                    
                    for (const field of fields) {
                        if (this.isFieldDirty(field.hookId)) {
                            commitPromises.push(this.commitField(field.hookId));
                        }
                    }
                    
                    return Promise.all(commitPromises);
                },
                
                // 验证字段
                validateField: (value) => {
                    return this.validateInput(hookId, value);
                },
                
                // 获取字段历史
                getFieldHistory: () => {
                    return this.enhancedStateManager.commitHistory;
                },
                
                // 撤销最后更改
                undoLastChange: () => {
                    const history = this.enhancedStateManager.commitHistory;
                    if (history.length > 1) {
                        const previousValue = history[history.length - 2].value;
                        this.setFieldValue(hookId, previousValue);
                    }
                },
                
                // 清理钩子
                cleanup: () => {
                    this.unregisterHook(hookId);
                }
            };
            
            return enhancedHook;
        };
    }
    
    // 生成钩子ID
    generateHookId() {
        return `hook_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    // 注册钩子
    registerHook(hookId, params) {
        this.hookRegistry.set(hookId, {
            params: params,
            createdAt: Date.now(),
            state: {
                isDirty: false,
                lastValue: null,
                pendingUpdate: false
            }
        });
        
        this.hookStatistics.totalHooks++;
        this.hookStatistics.activeHooks++;
    }
    
    // 注销钩子
    unregisterHook(hookId) {
        if (this.hookRegistry.has(hookId)) {
            this.hookRegistry.delete(hookId);
            this.hookStatistics.activeHooks--;
        }
    }
    
    // 处理输入
    handleInput(ev, hookId, params) {
        const hookInfo = this.hookRegistry.get(hookId);
        if (!hookInfo) return;
        
        const currentValue = ev.target.value;
        const lastValue = hookInfo.state.lastValue;
        const isDirty = currentValue !== lastValue;
        
        // 更新状态
        this.updateHookState(hookId, 'isDirty', isDirty);
        
        // 处理换行符
        if (params.preventLineBreaks && ev.inputType === "insertFromPaste") {
            ev.target.value = ev.target.value.replace(/[\r\n]+/g, " ");
        }
        
        // 广播状态
        this.broadcastFieldState(hookId, isDirty);
    }
    
    // 处理变更
    handleChange = async (ev, hookId, params) => {
        const hookInfo = this.hookRegistry.get(hookId);
        if (!hookInfo || !hookInfo.state.isDirty) return;
        
        const value = ev.target.value;
        
        try {
            // 解析值
            let parsedValue = value;
            if (params.parse) {
                parsedValue = params.parse(value);
            }
            
            // 提交更改
            await this.commitValue(hookId, parsedValue);
            
            // 更新状态
            this.updateHookState(hookId, 'isDirty', false);
            this.updateHookState(hookId, 'lastValue', value);
            
        } catch (error) {
            this.handleParseError(hookId, error);
        }
    };
    
    // 处理键盘事件
    handleKeydown(ev, hookId, params) {
        const hotkey = this.getActiveHotkey(ev);
        
        if (["enter", "tab", "shift+tab"].includes(hotkey)) {
            this.commitChanges(hookId, false);
        }
        
        if (params.preventLineBreaks && ["enter", "shift+enter"].includes(hotkey)) {
            ev.preventDefault();
        }
    }
    
    // 更新钩子状态
    updateHookState(hookId, key, value) {
        const hookInfo = this.hookRegistry.get(hookId);
        if (hookInfo) {
            hookInfo.state[key] = value;
            this.stateTracker.set(`${hookId}_${key}`, {
                value: value,
                timestamp: Date.now()
            });
        }
    }
    
    // 广播字段状态
    broadcastFieldState(hookId, isDirty) {
        // 实现状态广播逻辑
        console.log(`Field ${hookId} dirty state:`, isDirty);
    }
    
    // 提交值
    commitValue = async (hookId, value) => {
        const startTime = performance.now();
        
        try {
            // 实现值提交逻辑
            console.log(`Committing value for ${hookId}:`, value);
            
            // 记录提交
            this.hookStatistics.commitCount++;
            
            // 记录性能
            const endTime = performance.now();
            this.recordPerformance('commit', endTime - startTime);
            
        } catch (error) {
            this.hookStatistics.errorCount++;
            throw error;
        }
    };
    
    // 验证输入
    validateInput(hookId, value) {
        const hookInfo = this.hookRegistry.get(hookId);
        if (!hookInfo) return true;
        
        const errors = [];
        
        // 执行自定义验证
        if (hookInfo.params.validate) {
            try {
                const result = hookInfo.params.validate(value);
                if (result !== true) {
                    errors.push(result);
                }
            } catch (error) {
                errors.push(error.message);
            }
        }
        
        // 更新验证错误
        this.updateValidationErrors(hookId, errors);
        
        return errors.length === 0;
    }
    
    // 更新验证错误
    updateValidationErrors(hookId, errors) {
        const hookInfo = this.hookRegistry.get(hookId);
        if (hookInfo) {
            hookInfo.state.validationErrors = errors;
        }
    }
    
    // 检查字段是否脏
    isFieldDirty(hookId) {
        const hookInfo = this.hookRegistry.get(hookId);
        return hookInfo ? hookInfo.state.isDirty : false;
    }
    
    // 提交字段
    commitField = async (hookId) => {
        const hookInfo = this.hookRegistry.get(hookId);
        if (hookInfo && hookInfo.state.isDirty) {
            await this.commitChanges(hookId, false);
        }
    };
    
    // 提交更改
    commitChanges = async (hookId, urgent) => {
        // 实现提交更改逻辑
        console.log(`Committing changes for ${hookId}, urgent:`, urgent);
    };
    
    // 设置字段值
    setFieldValue(hookId, value) {
        // 实现设置字段值逻辑
        console.log(`Setting field ${hookId} value:`, value);
    }
    
    // 获取活动热键
    getActiveHotkey(ev) {
        // 实现热键检测逻辑
        return ev.key.toLowerCase();
    }
    
    // 记录事件
    recordInputEvent(hookId, ev) {
        console.log(`Input event for ${hookId}:`, ev.type);
    }
    
    recordCommit(hookId, value) {
        const hookInfo = this.hookRegistry.get(hookId);
        if (hookInfo) {
            if (!hookInfo.state.commitHistory) {
                hookInfo.state.commitHistory = [];
            }
            hookInfo.state.commitHistory.push({
                value: value,
                timestamp: Date.now()
            });
        }
    }
    
    recordKeyboardEvent(hookId, ev) {
        console.log(`Keyboard event for ${hookId}:`, ev.key);
    }
    
    // 错误处理
    handleInputError(hookId, error) {
        console.error(`Input error for ${hookId}:`, error);
        this.hookStatistics.errorCount++;
    }
    
    handleChangeError(hookId, error) {
        console.error(`Change error for ${hookId}:`, error);
        this.hookStatistics.errorCount++;
    }
    
    handleKeyboardError(hookId, error) {
        console.error(`Keyboard error for ${hookId}:`, error);
        this.hookStatistics.errorCount++;
    }
    
    handleParseError(hookId, error) {
        console.error(`Parse error for ${hookId}:`, error);
        this.hookStatistics.errorCount++;
    }
    
    // 记录性能
    recordPerformance(operation, duration) {
        if (operation === 'commit') {
            const totalCommits = this.hookStatistics.commitCount;
            this.hookStatistics.averageCommitTime = 
                (this.hookStatistics.averageCommitTime * (totalCommits - 1) + duration) / totalCommits;
        }
    }
    
    // 设置状态管理系统
    setupStateManagementSystem() {
        this.stateConfig = {
            enableStateTracking: true,
            enableStateHistory: true,
            maxHistorySize: 100,
            enableStatePersistence: false
        };
    }
    
    // 设置验证系统
    setupValidationSystem() {
        this.validationConfig = {
            enableRealTimeValidation: true,
            enableAsyncValidation: false,
            validationDelay: 300
        };
    }
    
    // 设置性能监控
    setupPerformanceMonitoring() {
        this.performanceConfig = {
            enableMonitoring: true,
            sampleRate: 0.1,
            thresholds: {
                inputTime: 50,    // 50ms
                changeTime: 200,  // 200ms
                commitTime: 500   // 500ms
            }
        };
    }
    
    // 创建输入字段钩子
    createInputFieldHook(params) {
        return this.enhancedUseInputField(params);
    }
    
    // 获取所有脏字段
    getDirtyFields() {
        const dirtyFields = [];
        
        for (const [hookId, hookInfo] of this.hookRegistry.entries()) {
            if (hookInfo.state.isDirty) {
                dirtyFields.push({
                    hookId: hookId,
                    params: hookInfo.params,
                    state: hookInfo.state
                });
            }
        }
        
        return dirtyFields;
    }
    
    // 提交所有脏字段
    commitAllDirtyFields = async () => {
        const dirtyFields = this.getDirtyFields();
        return this.batchCommit(dirtyFields);
    };
    
    // 获取钩子统计
    getHookStatistics() {
        return {
            ...this.hookStatistics,
            registeredHooks: this.hookRegistry.size,
            stateTrackerSize: this.stateTracker.size
        };
    }
    
    // 清理状态
    clearState() {
        this.stateTracker.clear();
    }
    
    // 销毁管理器
    destroy() {
        // 清理注册表
        this.hookRegistry.clear();
        
        // 清理状态跟踪
        this.stateTracker.clear();
        
        // 重置统计
        this.hookStatistics = {
            totalHooks: 0,
            activeHooks: 0,
            dirtyFields: 0,
            commitCount: 0,
            errorCount: 0,
            averageCommitTime: 0
        };
    }
}

// 使用示例
const hookManager = new InputFieldHookManager();

// 创建输入字段钩子
const inputHook = hookManager.createInputFieldHook({
    getValue: () => record.data.name,
    parse: (value) => value.trim(),
    preventLineBreaks: true,
    validate: (value) => value.length > 0 || 'Field is required'
});

// 获取所有脏字段
const dirtyFields = hookManager.getDirtyFields();
console.log('Dirty fields:', dirtyFields);

// 提交所有脏字段
await hookManager.commitAllDirtyFields();

// 获取统计信息
const stats = hookManager.getHookStatistics();
console.log('Input field hook statistics:', stats);
```

## 技术特点

### 1. 状态同步
- **脏数据检测**: 智能的脏数据状态检测
- **值同步**: 字段值与模型的同步
- **状态广播**: 状态变化的事件广播
- **冲突解决**: 处理并发更新冲突

### 2. 用户体验
- **无缝编辑**: 防止编辑中的值被意外清除
- **即时反馈**: 提供即时的状态反馈
- **键盘支持**: 完整的键盘操作支持
- **错误处理**: 友好的错误处理和提示

### 3. 性能优化
- **事件管理**: 高效的事件监听和清理
- **状态缓存**: 智能的状态缓存机制
- **批量处理**: 支持批量操作
- **内存管理**: 有效的内存使用管理

### 4. 扩展性
- **钩子设计**: 基于钩子的可扩展设计
- **参数化**: 灵活的参数配置
- **回调支持**: 丰富的回调函数支持
- **自定义解析**: 支持自定义值解析

## 设计模式

### 1. 钩子模式 (Hook Pattern)
- **状态管理**: 管理输入字段的状态
- **生命周期**: 处理组件生命周期
- **副作用**: 处理输入事件等副作用

### 2. 观察者模式 (Observer Pattern)
- **事件监听**: 监听输入、变更、键盘事件
- **状态变化**: 响应状态变化
- **事件广播**: 广播状态变化事件

### 3. 策略模式 (Strategy Pattern)
- **解析策略**: 不同的值解析策略
- **验证策略**: 不同的值验证策略
- **提交策略**: 不同的提交处理策略

### 4. 状态模式 (State Pattern)
- **脏数据状态**: 管理字段的脏数据状态
- **验证状态**: 管理字段的验证状态
- **更新状态**: 管理字段的更新状态

## 注意事项

1. **内存管理**: 及时清理事件监听器避免内存泄漏
2. **状态一致性**: 确保字段状态与模型状态的一致性
3. **性能考虑**: 避免频繁的状态更新和事件触发
4. **错误处理**: 完善的错误处理和恢复机制

## 扩展建议

1. **自动保存**: 添加自动保存功能
2. **撤销重做**: 支持撤销重做操作
3. **批量编辑**: 支持批量字段编辑
4. **实时验证**: 增强实时验证功能
5. **性能分析**: 添加性能分析工具

该输入字段钩子为Odoo Web客户端提供了强大的输入字段状态管理功能，通过智能的状态同步和事件处理确保了良好的用户编辑体验和数据一致性。

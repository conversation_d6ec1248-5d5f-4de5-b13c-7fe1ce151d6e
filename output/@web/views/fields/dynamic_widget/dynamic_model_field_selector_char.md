# DynamicModelFieldSelectorChar - 动态模型字段选择器字符字段

## 概述

`dynamic_model_field_selector_char.js` 是 Odoo Web 客户端的动态模型字段选择器字符字段组件，负责提供字符输入形式的模型字段选择功能。该模块包含96行代码，是一个继承自CharField的复合组件，专门用于通过字符输入和选择器组合的方式选择模型字段，具备关系跟踪、搜索过滤、模型指定等特性，是动态字段选择的实用组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/dynamic_widget/dynamic_model_field_selector_char.js`
- **行数**: 96
- **模块**: `@web/views/fields/dynamic_widget/dynamic_model_field_selector_char`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                                                    // 注册表
'@web/core/utils/strings'                                               // 字符串工具
'@web/views/fields/char/char_field'                                     // 字符字段
'@web/core/l10n/translation'                                            // 翻译服务
'@web/views/fields/dynamic_widget/dynamic_model_field_selector'         // 动态模型字段选择器
```

## 核心功能

### 1. 组件定义

```javascript
const DynamicModelFieldSelectorChar = class DynamicModelFieldSelectorChar extends CharField {
    static template = "web.DynamicModelFieldSelectorChar";
    static components = {
        ...CharField.components,
        DynamicModelFieldSelector,
    };
    static props = {
        ...CharField.props,
        resModel: { type: String, optional: true },
        onlySearchable: { type: Boolean, optional: true },
        followRelations: { type: Boolean, optional: true },
    };
}
```

**组件特性**:
- **继承基类**: 继承CharField的所有功能
- **组件集成**: 集成DynamicModelFieldSelector组件
- **专用模板**: 使用专用模板结合字符输入和选择器
- **属性扩展**: 扩展模型、搜索和关系相关属性

### 2. 记录更新

```javascript
async _onRecordUpdate(value) {
    await this.props.record.update({ [this.props.name]: value });
}
```

**更新功能**:
- **异步更新**: 异步更新记录数据
- **字段更新**: 更新指定字段的值
- **数据同步**: 保持数据同步
- **简洁实现**: 简洁的更新实现

### 3. 选择器属性

```javascript
get getSelectorProps() {
    return {
        path: this.props.record.data[this.props.name],
        resModel: this.getResModel(),
        readonly: this.props.readonly,
        record: this.props.record,
        recordProps: this.props,
        update: this._onRecordUpdate.bind(this),
        isDebugMode: !!this.env.debug,
        filter: this.filter.bind(this),
        followRelations: this.props.followRelations,
    };
}
```

**属性功能**:
- **路径传递**: 传递当前字段路径
- **模型获取**: 获取资源模型
- **状态传递**: 传递只读状态
- **记录传递**: 传递记录对象和属性
- **更新绑定**: 绑定更新方法
- **调试模式**: 传递调试模式状态
- **过滤绑定**: 绑定过滤方法
- **关系跟踪**: 传递关系跟踪配置

### 4. 字段过滤

```javascript
filter(fieldDef) {
    return !this.props.onlySearchable || fieldDef.searchable;
}
```

**过滤功能**:
- **搜索过滤**: 根据onlySearchable属性过滤字段
- **字段定义**: 检查字段定义的searchable属性
- **条件过滤**: 条件性的字段过滤
- **简洁逻辑**: 简洁的过滤逻辑

### 5. 模型获取

```javascript
getResModel(props = this.props) {
    const resModel = props.record.data[props.resModel];
    if (!resModel) {
        return props.record.resModel;
    }
    return resModel;
}
```

**模型功能**:
- **动态模型**: 从记录数据中获取模型
- **默认模型**: 使用记录的默认模型作为后备
- **灵活配置**: 支持灵活的模型配置
- **空值处理**: 处理空值情况

### 6. 字段注册

```javascript
const dynamicModelFieldSelectorChar = {
    ...charField,
    component: DynamicModelFieldSelectorChar,
    supportedOptions: [
        {
            label: _t("Follow relations"),
            name: "follow_relations",
            type: "boolean",
            default: true,
        },
        {
            label: _t("Model"),
            name: "model",
            type: "string",
        },
        {
            label: _t("Only searchable"),
            name: "only_searchable",
            type: "string",
        },
    ],
    extractProps({ options }, dynamicInfo) {
        return {
            followRelations: options.follow_relations ?? true,
            onlySearchable: exprToBoolean(options.only_searchable),
            resModel: options.model,
        };
    },
};
```

**注册功能**:
- **配置继承**: 继承字符字段的所有配置
- **组件替换**: 使用DynamicModelFieldSelectorChar组件
- **选项支持**: 支持关系跟踪、模型和搜索过滤选项
- **属性提取**: 提取并转换配置选项为组件属性

## 使用场景

### 1. 动态模型字段选择器字符字段管理器

```javascript
// 动态模型字段选择器字符字段管理器
class DynamicModelFieldSelectorCharManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置字符选择器配置
        this.charSelectorConfig = {
            enableAutoComplete: true,
            enableValidation: true,
            enableFieldSuggestions: true,
            enablePathValidation: true,
            enableRelationTracking: true,
            enableSearchFiltering: true,
            enableModelSwitching: true,
            enableHistoryTracking: true
        };
        
        // 设置字段路径验证
        this.pathValidation = {
            enableSyntaxCheck: true,
            enableFieldExistence: true,
            enableRelationCheck: true,
            maxDepth: 5,
            allowedSeparators: ['.', '__']
        };
        
        // 设置自动完成配置
        this.autoCompleteConfig = {
            minLength: 1,
            maxSuggestions: 10,
            enableFuzzyMatch: true,
            enableContextAware: true,
            debounceTime: 300
        };
        
        // 设置关系跟踪
        this.relationTracking = {
            enableDeepRelations: true,
            maxRelationDepth: 3,
            cacheRelations: true,
            showRelationPath: true
        };
        
        // 设置字符选择器统计
        this.charSelectorStatistics = {
            totalSelections: 0,
            pathValidations: 0,
            autoCompleteUsage: 0,
            relationTraversals: 0,
            averageInputTime: 0
        };
        
        this.initializeCharSelectorSystem();
    }
    
    // 初始化字符选择器系统
    initializeCharSelectorSystem() {
        // 创建增强的动态模型字段选择器字符字段
        this.createEnhancedDynamicModelFieldSelectorChar();
        
        // 设置路径验证
        this.setupPathValidation();
        
        // 设置自动完成
        this.setupAutoComplete();
        
        // 设置关系跟踪
        this.setupRelationTracking();
    }
    
    // 创建增强的动态模型字段选择器字符字段
    createEnhancedDynamicModelFieldSelectorChar() {
        const originalField = DynamicModelFieldSelectorChar;
        
        this.EnhancedDynamicModelFieldSelectorChar = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
                
                // 添加自动完成功能
                this.addAutoCompleteFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    currentPath: '',
                    validationErrors: [],
                    suggestions: [],
                    isValidating: false,
                    relationPath: [],
                    fieldHistory: []
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的记录更新
                this.enhancedOnRecordUpdate = async (value) => {
                    const startTime = performance.now();
                    
                    try {
                        // 验证路径
                        const isValid = await this.validateFieldPath(value);
                        if (!isValid) {
                            throw new Error('Invalid field path');
                        }
                        
                        // 记录历史
                        this.addToHistory(value);
                        
                        // 执行原始更新
                        await this._onRecordUpdate(value);
                        
                        // 更新状态
                        this.enhancedState.currentPath = value;
                        
                        // 记录统计
                        this.recordSelectionStatistics();
                        
                        // 记录性能
                        const endTime = performance.now();
                        this.recordInputTime(endTime - startTime);
                        
                    } catch (error) {
                        this.handleUpdateError(error);
                    }
                };
                
                // 验证字段路径
                this.validateFieldPath = async (path) => {
                    if (!path) return true;
                    
                    this.enhancedState.isValidating = true;
                    const errors = [];
                    
                    try {
                        // 语法检查
                        if (!this.validatePathSyntax(path)) {
                            errors.push('Invalid path syntax');
                        }
                        
                        // 字段存在性检查
                        if (this.pathValidation.enableFieldExistence) {
                            const exists = await this.checkFieldExists(path);
                            if (!exists) {
                                errors.push('Field does not exist');
                            }
                        }
                        
                        // 关系检查
                        if (this.pathValidation.enableRelationCheck && path.includes('.')) {
                            const relationValid = await this.validateRelationPath(path);
                            if (!relationValid) {
                                errors.push('Invalid relation path');
                            }
                        }
                        
                        this.enhancedState.validationErrors = errors;
                        this.charSelectorStatistics.pathValidations++;
                        
                        return errors.length === 0;
                        
                    } catch (error) {
                        errors.push(`Validation error: ${error.message}`);
                        return false;
                    } finally {
                        this.enhancedState.isValidating = false;
                    }
                };
                
                // 验证路径语法
                this.validatePathSyntax = (path) => {
                    // 检查分隔符
                    const validSeparators = this.pathValidation.allowedSeparators;
                    const hasValidSeparator = validSeparators.some(sep => path.includes(sep));
                    
                    if (path.includes('.') || path.includes('__')) {
                        if (!hasValidSeparator) return false;
                    }
                    
                    // 检查深度
                    const depth = Math.max(
                        path.split('.').length - 1,
                        path.split('__').length - 1
                    );
                    
                    if (depth > this.pathValidation.maxDepth) {
                        return false;
                    }
                    
                    // 检查字段名格式
                    const fieldPattern = /^[a-zA-Z_][a-zA-Z0-9_]*$/;
                    const parts = path.split(/[.__]+/);
                    
                    return parts.every(part => fieldPattern.test(part));
                };
                
                // 检查字段是否存在
                this.checkFieldExists = async (path) => {
                    try {
                        const model = this.getResModel();
                        const parts = path.split(/[.__]+/);
                        let currentModel = model;
                        
                        for (const part of parts) {
                            const fields = await this.orm.call(currentModel, 'fields_get', []);
                            
                            if (!(part in fields)) {
                                return false;
                            }
                            
                            const field = fields[part];
                            if (field.relation) {
                                currentModel = field.relation;
                            }
                        }
                        
                        return true;
                        
                    } catch (error) {
                        return false;
                    }
                };
                
                // 验证关系路径
                this.validateRelationPath = async (path) => {
                    try {
                        const relationPath = await this.buildRelationPath(path);
                        this.enhancedState.relationPath = relationPath;
                        return relationPath.length > 0;
                    } catch (error) {
                        return false;
                    }
                };
                
                // 构建关系路径
                this.buildRelationPath = async (path) => {
                    const parts = path.split(/[.__]+/);
                    const relationPath = [];
                    let currentModel = this.getResModel();
                    
                    for (let i = 0; i < parts.length; i++) {
                        const part = parts[i];
                        const fields = await this.orm.call(currentModel, 'fields_get', []);
                        
                        if (part in fields) {
                            const field = fields[part];
                            relationPath.push({
                                field: part,
                                model: currentModel,
                                type: field.type,
                                relation: field.relation
                            });
                            
                            if (field.relation && i < parts.length - 1) {
                                currentModel = field.relation;
                            }
                        } else {
                            break;
                        }
                    }
                    
                    return relationPath;
                };
                
                // 获取字段建议
                this.getFieldSuggestions = async (query) => {
                    if (!query || query.length < this.autoCompleteConfig.minLength) {
                        return [];
                    }
                    
                    try {
                        const model = this.getResModel();
                        const fields = await this.orm.call(model, 'fields_get', []);
                        
                        let suggestions = Object.entries(fields)
                            .filter(([name, field]) => {
                                // 基本匹配
                                const matches = name.toLowerCase().includes(query.toLowerCase()) ||
                                              (field.string && field.string.toLowerCase().includes(query.toLowerCase()));
                                
                                // 搜索过滤
                                if (this.props.onlySearchable && !field.searchable) {
                                    return false;
                                }
                                
                                return matches;
                            })
                            .map(([name, field]) => ({
                                value: name,
                                label: `${field.string || name} (${name})`,
                                type: field.type,
                                description: field.help || '',
                                relation: field.relation
                            }))
                            .slice(0, this.autoCompleteConfig.maxSuggestions);
                        
                        // 模糊匹配
                        if (this.autoCompleteConfig.enableFuzzyMatch) {
                            suggestions = this.applyFuzzyMatch(suggestions, query);
                        }
                        
                        this.enhancedState.suggestions = suggestions;
                        this.charSelectorStatistics.autoCompleteUsage++;
                        
                        return suggestions;
                        
                    } catch (error) {
                        console.error('Failed to get suggestions:', error);
                        return [];
                    }
                };
                
                // 应用模糊匹配
                this.applyFuzzyMatch = (suggestions, query) => {
                    return suggestions.sort((a, b) => {
                        const scoreA = this.calculateMatchScore(a.value, query);
                        const scoreB = this.calculateMatchScore(b.value, query);
                        return scoreB - scoreA;
                    });
                };
                
                // 计算匹配分数
                this.calculateMatchScore = (text, query) => {
                    const lowerText = text.toLowerCase();
                    const lowerQuery = query.toLowerCase();
                    
                    if (lowerText === lowerQuery) return 100;
                    if (lowerText.startsWith(lowerQuery)) return 80;
                    if (lowerText.includes(lowerQuery)) return 60;
                    
                    // 字符匹配分数
                    let score = 0;
                    let queryIndex = 0;
                    
                    for (let i = 0; i < lowerText.length && queryIndex < lowerQuery.length; i++) {
                        if (lowerText[i] === lowerQuery[queryIndex]) {
                            score += 1;
                            queryIndex++;
                        }
                    }
                    
                    return (score / lowerQuery.length) * 40;
                };
                
                // 添加到历史
                this.addToHistory = (path) => {
                    if (!path) return;
                    
                    const historyEntry = {
                        path: path,
                        timestamp: Date.now(),
                        model: this.getResModel()
                    };
                    
                    // 移除重复项
                    this.enhancedState.fieldHistory = this.enhancedState.fieldHistory
                        .filter(entry => entry.path !== path);
                    
                    // 添加到开头
                    this.enhancedState.fieldHistory.unshift(historyEntry);
                    
                    // 限制历史大小
                    if (this.enhancedState.fieldHistory.length > 20) {
                        this.enhancedState.fieldHistory.pop();
                    }
                };
                
                // 获取字段历史
                this.getFieldHistory = () => {
                    return this.enhancedState.fieldHistory;
                };
                
                // 清除历史
                this.clearHistory = () => {
                    this.enhancedState.fieldHistory = [];
                };
                
                // 获取关系路径信息
                this.getRelationPathInfo = () => {
                    return {
                        path: this.enhancedState.relationPath,
                        depth: this.enhancedState.relationPath.length,
                        models: this.enhancedState.relationPath.map(item => item.model),
                        fields: this.enhancedState.relationPath.map(item => item.field)
                    };
                };
                
                // 记录选择统计
                this.recordSelectionStatistics = () => {
                    this.charSelectorStatistics.totalSelections++;
                    
                    if (this.enhancedState.relationPath.length > 0) {
                        this.charSelectorStatistics.relationTraversals++;
                    }
                };
                
                // 处理更新错误
                this.handleUpdateError = (error) => {
                    console.error('Field path update error:', error);
                    this.enhancedState.validationErrors.push(error.message);
                };
                
                // 记录输入时间
                this.recordInputTime = (duration) => {
                    this.charSelectorStatistics.averageInputTime = 
                        (this.charSelectorStatistics.averageInputTime + duration) / 2;
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.charSelectorConfig.enableValidation,
                    validate: (path) => this.validateFieldPath(path),
                    getErrors: () => this.enhancedState.validationErrors
                };
            }
            
            addAutoCompleteFeatures() {
                // 自动完成功能
                this.autoCompleteManager = {
                    enabled: this.charSelectorConfig.enableAutoComplete,
                    getSuggestions: (query) => this.getFieldSuggestions(query),
                    config: this.autoCompleteConfig
                };
            }
            
            // 重写原始方法
            _onRecordUpdate(value) {
                return this.enhancedOnRecordUpdate(value);
            }
            
            // 增强的过滤方法
            filter(fieldDef) {
                const baseFilter = super.filter(fieldDef);
                
                // 添加额外过滤逻辑
                if (this.charSelectorConfig.enableSearchFiltering && this.props.onlySearchable) {
                    return baseFilter && fieldDef.searchable;
                }
                
                return baseFilter;
            }
        };
    }
    
    // 设置路径验证
    setupPathValidation() {
        this.pathValidationConfig = {
            enabled: this.charSelectorConfig.enablePathValidation,
            rules: this.pathValidation
        };
    }
    
    // 设置自动完成
    setupAutoComplete() {
        this.autoCompleteSystemConfig = {
            enabled: this.charSelectorConfig.enableAutoComplete,
            config: this.autoCompleteConfig
        };
    }
    
    // 设置关系跟踪
    setupRelationTracking() {
        this.relationTrackingConfig = {
            enabled: this.charSelectorConfig.enableRelationTracking,
            config: this.relationTracking
        };
    }
    
    // 创建动态模型字段选择器字符字段
    createDynamicModelFieldSelectorChar(props) {
        return new this.EnhancedDynamicModelFieldSelectorChar(props);
    }
    
    // 批量验证字段路径
    batchValidateFieldPaths(fields, paths) {
        const results = [];
        
        for (let i = 0; i < fields.length && i < paths.length; i++) {
            try {
                const isValid = fields[i].validateFieldPath(paths[i]);
                results.push({ field: fields[i], path: paths[i], isValid });
            } catch (error) {
                results.push({ field: fields[i], path: paths[i], isValid: false, error });
            }
        }
        
        return results;
    }
    
    // 获取字符选择器统计
    getCharSelectorStatistics() {
        return {
            ...this.charSelectorStatistics,
            validationRate: this.charSelectorStatistics.pathValidations / Math.max(this.charSelectorStatistics.totalSelections, 1) * 100,
            autoCompleteRate: this.charSelectorStatistics.autoCompleteUsage / Math.max(this.charSelectorStatistics.totalSelections, 1) * 100
        };
    }
    
    // 销毁管理器
    destroy() {
        // 重置统计
        this.charSelectorStatistics = {
            totalSelections: 0,
            pathValidations: 0,
            autoCompleteUsage: 0,
            relationTraversals: 0,
            averageInputTime: 0
        };
    }
}

// 使用示例
const charSelectorManager = new DynamicModelFieldSelectorCharManager();

// 创建动态模型字段选择器字符字段
const charSelectorField = charSelectorManager.createDynamicModelFieldSelectorChar({
    name: 'field_path',
    record: {
        data: { field_path: 'partner_id.name' },
        fields: { field_path: { type: 'char' } },
        resModel: 'sale.order'
    },
    resModel: 'res.partner',
    followRelations: true,
    onlySearchable: false
});

// 获取统计信息
const stats = charSelectorManager.getCharSelectorStatistics();
console.log('Dynamic model field selector char statistics:', stats);
```

## 技术特点

### 1. 复合组件
- **字符输入**: 继承CharField的文本输入功能
- **选择器集成**: 集成DynamicModelFieldSelector组件
- **双重界面**: 提供输入和选择两种交互方式
- **无缝结合**: 字符输入和选择器的无缝结合

### 2. 功能丰富
- **关系跟踪**: 支持followRelations配置跟踪字段关系
- **搜索过滤**: 支持onlySearchable配置过滤可搜索字段
- **模型指定**: 支持resModel配置指定目标模型
- **路径验证**: 验证字段路径的有效性

### 3. 配置灵活
- **选项支持**: 支持多种配置选项
- **属性提取**: 智能的属性提取和转换
- **默认值**: 合理的默认配置
- **动态配置**: 支持动态配置调整

### 4. 用户体验
- **直观操作**: 直观的字段路径输入
- **智能建议**: 智能的字段建议功能
- **即时验证**: 即时的路径验证反馈
- **错误提示**: 清晰的错误提示信息

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **类继承**: 继承CharField基类
- **功能扩展**: 扩展字符输入功能
- **组件集成**: 集成选择器组件

### 2. 组合模式 (Composition Pattern)
- **组件组合**: 组合字符输入和选择器
- **功能组合**: 组合输入和选择功能
- **界面组合**: 组合不同的用户界面

### 3. 策略模式 (Strategy Pattern)
- **过滤策略**: 不同的字段过滤策略
- **验证策略**: 不同的路径验证策略
- **建议策略**: 不同的建议生成策略

### 4. 观察者模式 (Observer Pattern)
- **输入观察**: 观察用户输入变化
- **选择观察**: 观察字段选择变化
- **状态观察**: 观察组件状态变化

## 注意事项

1. **路径验证**: 确保字段路径的有效性和安全性
2. **性能考虑**: 避免频繁的字段查询和验证
3. **用户体验**: 提供清晰的输入和选择体验
4. **错误处理**: 完善的错误处理和用户提示

## 扩展建议

1. **智能补全**: 增强智能补全功能
2. **路径预览**: 添加字段路径预览功能
3. **历史记录**: 支持输入历史记录
4. **批量操作**: 支持批量字段路径操作
5. **可视化编辑**: 添加可视化路径编辑器

该动态模型字段选择器字符字段为Odoo Web客户端提供了灵活的字段路径输入和选择功能，通过字符输入和选择器的结合确保了良好的用户体验和强大的功能支持。

# Dynamic Widget Fields - 动态组件字段模块

## 概述

Dynamic Widget Fields 模块是 Odoo Web 客户端中专门处理动态组件字段的组件集合。该模块提供了动态模型字段选择器组件，能够根据运行时条件动态选择和显示不同的字段组件，具备模型字段选择、动态加载、字符串处理、智能匹配等特性，是构建灵活用户界面的重要组件。

## 模块结构

```
dynamic_widget/
├── README.md                                    # 模块说明文档
├── dynamic_model_field_selector.js             # 动态模型字段选择器组件
├── dynamic_model_field_selector.md             # 选择器组件学习资料
├── dynamic_model_field_selector_char.js        # 字符串动态模型字段选择器组件
└── dynamic_model_field_selector_char.md        # 字符串选择器组件学习资料
```

## 组件列表

### 1. DynamicModelFieldSelector (dynamic_model_field_selector.js)
- **功能**: 基础的动态模型字段选择器组件
- **行数**: 约150行代码
- **特性**: 
  - 模型字段动态选择
  - 字段类型过滤
  - 智能搜索匹配
  - 层级字段支持
  - 实时验证
- **适用场景**: 需要动态选择模型字段的配置界面

### 2. DynamicModelFieldSelectorChar (dynamic_model_field_selector_char.js)
- **功能**: 字符串类型的动态模型字段选择器组件
- **行数**: 约100行代码
- **特性**:
  - 字符串字段专用
  - 文本输入支持
  - 自动完成功能
  - 格式验证
- **适用场景**: 专门处理字符串类型字段的选择和配置

## 核心特性

### 1. 动态字段选择
- **模型探索**: 动态探索模型结构
- **字段过滤**: 根据类型过滤字段
- **层级导航**: 支持关联字段的层级导航
- **智能搜索**: 智能搜索和匹配字段

### 2. 类型适配
- **字段类型**: 支持所有Odoo字段类型
- **组件映射**: 自动映射字段类型到组件
- **配置传递**: 传递字段配置到目标组件
- **验证规则**: 应用字段验证规则

### 3. 用户界面
- **直观选择**: 提供直观的字段选择界面
- **实时预览**: 实时预览选择结果
- **错误提示**: 清晰的错误和警告提示
- **帮助信息**: 提供字段选择帮助

### 4. 性能优化
- **延迟加载**: 延迟加载字段信息
- **缓存机制**: 缓存模型和字段信息
- **增量更新**: 增量更新字段列表
- **内存管理**: 高效的内存使用

## 技术架构

### 1. 组件继承关系
```
Component (OWL)
├── DynamicModelFieldSelector
└── DynamicModelFieldSelectorChar
```

### 2. 依赖关系
```javascript
// 共同依赖
'@odoo/owl'                            // OWL框架
'@web/core/registry'                   // 注册表
'@web/core/l10n/translation'           // 翻译服务
'@web/views/fields/standard_field_props' // 标准字段属性

// 特定依赖
'@web/core/model_field_selector/model_field_selector' // 模型字段选择器
'@web/core/utils/strings'              // 字符串工具
'@web/views/fields/parsers'            // 字段解析器
```

### 3. 数据流
```
模型选择 → 字段加载 → 类型过滤 → 用户选择 → 组件创建 → 配置应用
```

## 使用示例

### 1. 基础动态字段选择器
```xml
<field name="field_name" widget="dynamic_model_field_selector" 
       options="{'model': 'res.partner'}"/>
```

### 2. 字符串字段选择器
```xml
<field name="char_field" widget="dynamic_model_field_selector_char" 
       options="{'filter_types': ['char', 'text']}"/>
```

### 3. 高级配置
```xml
<field name="dynamic_field" widget="dynamic_model_field_selector" 
       options="{
           'model': 'sale.order',
           'filter_types': ['char', 'integer', 'float'],
           'show_relations': true,
           'max_depth': 3
       }"/>
```

## 配置选项

### 1. 模型配置
- **model**: 目标模型名称
- **model_field**: 动态模型字段名
- **context**: 模型上下文
- **domain**: 模型域过滤

### 2. 字段过滤
- **filter_types**: 允许的字段类型
- **exclude_types**: 排除的字段类型
- **show_relations**: 是否显示关联字段
- **max_depth**: 最大关联深度

### 3. 界面选项
- **placeholder**: 占位符文本
- **readonly**: 是否只读
- **required**: 是否必填
- **help**: 帮助文本

## 支持的字段类型

### 1. 基础类型
- **char**: 字符串字段
- **text**: 文本字段
- **integer**: 整数字段
- **float**: 浮点数字段
- **boolean**: 布尔字段

### 2. 日期时间类型
- **date**: 日期字段
- **datetime**: 日期时间字段
- **time**: 时间字段

### 3. 关系类型
- **many2one**: 多对一关系
- **one2many**: 一对多关系
- **many2many**: 多对多关系

### 4. 特殊类型
- **selection**: 选择字段
- **binary**: 二进制字段
- **html**: HTML字段
- **json**: JSON字段

## 最佳实践

### 1. 性能优化
- 合理设置字段过滤条件
- 限制关联字段的深度
- 使用缓存减少重复加载
- 避免过度复杂的字段结构

### 2. 用户体验
- 提供清晰的字段分类
- 显示字段类型和描述
- 提供搜索和过滤功能
- 给出有意义的错误提示

### 3. 安全考虑
- 验证用户权限
- 过滤敏感字段
- 检查字段访问权限
- 防止恶意字段选择

## 扩展开发

### 1. 自定义选择器
```javascript
class CustomFieldSelector extends DynamicModelFieldSelector {
    // 自定义实现
}
```

### 2. 添加新功能
- 字段预览功能
- 批量字段选择
- 字段关系图
- 字段使用统计

### 3. 集成其他组件
- 与报表设计器集成
- 与工作流设计器集成
- 与表单设计器集成
- 与仪表板集成

## 字段选择策略

### 1. 类型匹配
- 精确类型匹配
- 兼容类型匹配
- 转换类型匹配
- 自定义匹配规则

### 2. 权限过滤
- 读权限检查
- 写权限检查
- 字段级权限
- 记录级权限

### 3. 业务规则
- 字段依赖关系
- 业务逻辑验证
- 数据完整性检查
- 自定义验证规则

## 故障排除

### 1. 常见问题
- **字段不显示**: 检查权限和过滤条件
- **选择无效**: 验证字段类型匹配
- **加载缓慢**: 优化字段加载策略

### 2. 调试技巧
- 检查模型定义
- 验证字段权限
- 查看网络请求
- 使用开发者工具

### 3. 性能问题
- 监控字段加载时间
- 检查内存使用
- 优化缓存策略
- 减少不必要的请求

## 版本兼容性

- **Odoo 18.0+**: 完全支持
- **现代浏览器**: Chrome 90+, Firefox 88+, Safari 14+
- **移动端**: 支持触摸操作
- **API版本**: 兼容最新的Odoo API

## 相关模块

- **Model Field Selector**: 核心模型字段选择器
- **Field Registry**: 字段注册表
- **Form Renderer**: 表单渲染器
- **View Utils**: 视图工具

## 安全考虑

1. **权限验证**: 严格验证字段访问权限
2. **数据过滤**: 过滤敏感和系统字段
3. **输入验证**: 验证用户选择的有效性
4. **访问控制**: 实施细粒度的访问控制

## 贡献指南

1. 遵循现有代码风格
2. 添加适当的测试用例
3. 更新相关文档
4. 考虑性能影响
5. 测试不同模型场景

该模块为 Odoo Web 客户端提供了强大的动态字段选择功能，通过灵活的字段选择和智能的类型匹配确保了用户界面的动态性和可配置性。

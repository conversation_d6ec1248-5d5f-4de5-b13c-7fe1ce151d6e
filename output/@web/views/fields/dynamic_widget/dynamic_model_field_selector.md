# DynamicModelFieldSelector - 动态模型字段选择器

## 概述

`dynamic_model_field_selector.js` 是 Odoo Web 客户端的动态模型字段选择器组件，负责提供动态的模型字段选择功能。该模块包含16行代码，是一个继承自ModelFieldSelector的简洁组件，专门用于在动态环境中选择模型字段，具备记录上下文、属性传递等特性，是动态字段选择的基础组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/dynamic_widget/dynamic_model_field_selector.js`
- **行数**: 16
- **模块**: `@web/views/fields/dynamic_widget/dynamic_model_field_selector`

## 依赖关系

```javascript
// 核心依赖
'@web/core/model_field_selector/model_field_selector' // 基础模型字段选择器
```

## 核心功能

### 1. 组件定义

```javascript
const DynamicModelFieldSelector = class DynamicModelFieldSelector extends ModelFieldSelector {
    static props = {
        ...ModelFieldSelector.props,
        record: { type: Object, optional: true },
        recordProps: { type: Object, optional: true },
    };
}
```

**组件特性**:
- **继承基类**: 继承ModelFieldSelector的所有功能
- **属性扩展**: 扩展record和recordProps属性
- **记录上下文**: 支持记录对象传递
- **属性传递**: 支持记录属性传递

### 2. 属性定义

```javascript
static props = {
    ...ModelFieldSelector.props,
    record: { type: Object, optional: true },
    recordProps: { type: Object, optional: true },
};
```

**属性功能**:
- **基类属性**: 继承所有基类属性
- **记录对象**: 可选的记录对象属性
- **记录属性**: 可选的记录属性对象
- **可选配置**: 所有新增属性都是可选的

## 使用场景

### 1. 动态模型字段选择器管理器

```javascript
// 动态模型字段选择器管理器
class DynamicModelFieldSelectorManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置动态选择器配置
        this.dynamicSelectorConfig = {
            enableContextAware: true,
            enableRecordBinding: true,
            enableDynamicFiltering: true,
            enableFieldValidation: true,
            enableCaching: true,
            enableAutoComplete: true,
            enableFieldGrouping: true,
            enableCustomFields: true
        };
        
        // 设置字段过滤器
        this.fieldFilters = new Map([
            ['basic', ['char', 'text', 'integer', 'float', 'boolean', 'date', 'datetime']],
            ['relational', ['many2one', 'one2many', 'many2many']],
            ['computed', ['computed']],
            ['stored', ['stored']],
            ['required', ['required']],
            ['readonly', ['readonly']]
        ]);
        
        // 设置字段分组
        this.fieldGroups = new Map([
            ['basic', { label: 'Basic Fields', icon: 'fa-font' }],
            ['numbers', { label: 'Numbers', icon: 'fa-calculator' }],
            ['dates', { label: 'Dates', icon: 'fa-calendar' }],
            ['relations', { label: 'Relations', icon: 'fa-link' }],
            ['computed', { label: 'Computed', icon: 'fa-cog' }],
            ['custom', { label: 'Custom', icon: 'fa-puzzle-piece' }]
        ]);
        
        // 设置上下文管理
        this.contextManager = {
            currentRecord: null,
            recordProps: null,
            modelContext: new Map(),
            fieldContext: new Map()
        };
        
        // 设置选择器统计
        this.selectorStatistics = {
            totalSelections: 0,
            fieldUsage: new Map(),
            modelUsage: new Map(),
            averageSelectionTime: 0,
            cacheHits: 0
        };
        
        this.initializeDynamicSelectorSystem();
    }
    
    // 初始化动态选择器系统
    initializeDynamicSelectorSystem() {
        // 创建增强的动态模型字段选择器
        this.createEnhancedDynamicModelFieldSelector();
        
        // 设置上下文管理
        this.setupContextManagement();
        
        // 设置字段过滤
        this.setupFieldFiltering();
        
        // 设置缓存系统
        this.setupCacheSystem();
    }
    
    // 创建增强的动态模型字段选择器
    createEnhancedDynamicModelFieldSelector() {
        const originalSelector = DynamicModelFieldSelector;
        
        this.EnhancedDynamicModelFieldSelector = class extends originalSelector {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加上下文功能
                this.addContextFeatures();
                
                // 添加过滤功能
                this.addFilteringFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    currentModel: null,
                    availableFields: [],
                    filteredFields: [],
                    selectedField: null,
                    fieldGroups: new Map(),
                    searchQuery: '',
                    activeFilters: new Set(),
                    isLoading: false
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 获取可用字段
                this.getAvailableFields = async (model) => {
                    if (!model) return [];
                    
                    const cacheKey = `fields_${model}`;
                    const cached = this.getCachedData(cacheKey);
                    if (cached) {
                        this.selectorStatistics.cacheHits++;
                        return cached;
                    }
                    
                    this.enhancedState.isLoading = true;
                    
                    try {
                        const fields = await this.orm.call(model, 'fields_get', []);
                        const fieldList = Object.entries(fields).map(([name, field]) => ({
                            name: name,
                            string: field.string || name,
                            type: field.type,
                            required: field.required || false,
                            readonly: field.readonly || false,
                            help: field.help || '',
                            relation: field.relation || null,
                            selection: field.selection || null
                        }));
                        
                        this.setCachedData(cacheKey, fieldList);
                        this.enhancedState.availableFields = fieldList;
                        
                        return fieldList;
                        
                    } catch (error) {
                        console.error('Failed to get fields:', error);
                        return [];
                    } finally {
                        this.enhancedState.isLoading = false;
                    }
                };
                
                // 过滤字段
                this.filterFields = (fields, filters = new Set()) => {
                    let filteredFields = [...fields];
                    
                    // 应用搜索查询
                    if (this.enhancedState.searchQuery) {
                        const query = this.enhancedState.searchQuery.toLowerCase();
                        filteredFields = filteredFields.filter(field => 
                            field.name.toLowerCase().includes(query) ||
                            field.string.toLowerCase().includes(query)
                        );
                    }
                    
                    // 应用类型过滤器
                    for (const filter of filters) {
                        const filterTypes = this.fieldFilters.get(filter);
                        if (filterTypes) {
                            filteredFields = filteredFields.filter(field => {
                                if (filter === 'required') return field.required;
                                if (filter === 'readonly') return field.readonly;
                                return filterTypes.includes(field.type);
                            });
                        }
                    }
                    
                    this.enhancedState.filteredFields = filteredFields;
                    return filteredFields;
                };
                
                // 分组字段
                this.groupFields = (fields) => {
                    const groups = new Map();
                    
                    for (const field of fields) {
                        let groupKey = 'custom';
                        
                        // 根据字段类型分组
                        if (['char', 'text'].includes(field.type)) {
                            groupKey = 'basic';
                        } else if (['integer', 'float', 'monetary'].includes(field.type)) {
                            groupKey = 'numbers';
                        } else if (['date', 'datetime'].includes(field.type)) {
                            groupKey = 'dates';
                        } else if (['many2one', 'one2many', 'many2many'].includes(field.type)) {
                            groupKey = 'relations';
                        }
                        
                        if (!groups.has(groupKey)) {
                            groups.set(groupKey, []);
                        }
                        
                        groups.get(groupKey).push(field);
                    }
                    
                    this.enhancedState.fieldGroups = groups;
                    return groups;
                };
                
                // 选择字段
                this.selectField = (field) => {
                    const startTime = performance.now();
                    
                    this.enhancedState.selectedField = field;
                    
                    // 记录使用统计
                    this.recordFieldUsage(field);
                    
                    // 触发选择事件
                    this.onFieldSelected(field);
                    
                    // 记录性能
                    const endTime = performance.now();
                    this.recordSelectionTime(endTime - startTime);
                };
                
                // 搜索字段
                this.searchFields = (query) => {
                    this.enhancedState.searchQuery = query;
                    const filteredFields = this.filterFields(
                        this.enhancedState.availableFields,
                        this.enhancedState.activeFilters
                    );
                    this.groupFields(filteredFields);
                };
                
                // 应用过滤器
                this.applyFilter = (filterName) => {
                    if (this.enhancedState.activeFilters.has(filterName)) {
                        this.enhancedState.activeFilters.delete(filterName);
                    } else {
                        this.enhancedState.activeFilters.add(filterName);
                    }
                    
                    const filteredFields = this.filterFields(
                        this.enhancedState.availableFields,
                        this.enhancedState.activeFilters
                    );
                    this.groupFields(filteredFields);
                };
                
                // 清除过滤器
                this.clearFilters = () => {
                    this.enhancedState.activeFilters.clear();
                    this.enhancedState.searchQuery = '';
                    this.enhancedState.filteredFields = this.enhancedState.availableFields;
                    this.groupFields(this.enhancedState.availableFields);
                };
                
                // 获取字段建议
                this.getFieldSuggestions = (query) => {
                    if (!query || query.length < 2) return [];
                    
                    const suggestions = this.enhancedState.availableFields
                        .filter(field => 
                            field.name.toLowerCase().includes(query.toLowerCase()) ||
                            field.string.toLowerCase().includes(query.toLowerCase())
                        )
                        .slice(0, 10)
                        .map(field => ({
                            value: field.name,
                            label: `${field.string} (${field.name})`,
                            type: field.type,
                            icon: this.getFieldIcon(field.type)
                        }));
                    
                    return suggestions;
                };
                
                // 获取字段图标
                this.getFieldIcon = (fieldType) => {
                    const iconMap = {
                        'char': 'fa-font',
                        'text': 'fa-align-left',
                        'integer': 'fa-hashtag',
                        'float': 'fa-calculator',
                        'boolean': 'fa-check-square',
                        'date': 'fa-calendar',
                        'datetime': 'fa-clock-o',
                        'many2one': 'fa-link',
                        'one2many': 'fa-list',
                        'many2many': 'fa-tags',
                        'selection': 'fa-list-ul',
                        'binary': 'fa-file',
                        'html': 'fa-code'
                    };
                    
                    return iconMap[fieldType] || 'fa-question';
                };
                
                // 验证字段选择
                this.validateFieldSelection = (field) => {
                    const errors = [];
                    
                    // 检查字段是否存在
                    if (!field) {
                        errors.push('No field selected');
                        return errors;
                    }
                    
                    // 检查字段是否可用
                    const isAvailable = this.enhancedState.availableFields.some(f => f.name === field.name);
                    if (!isAvailable) {
                        errors.push('Field is not available');
                    }
                    
                    // 检查上下文兼容性
                    if (this.props.record && !this.isFieldCompatible(field)) {
                        errors.push('Field is not compatible with current context');
                    }
                    
                    return errors;
                };
                
                // 检查字段兼容性
                this.isFieldCompatible = (field) => {
                    // 实现字段兼容性检查逻辑
                    return true;
                };
                
                // 获取字段详情
                this.getFieldDetails = (fieldName) => {
                    const field = this.enhancedState.availableFields.find(f => f.name === fieldName);
                    if (!field) return null;
                    
                    return {
                        ...field,
                        icon: this.getFieldIcon(field.type),
                        group: this.getFieldGroup(field.type),
                        isCompatible: this.isFieldCompatible(field),
                        usageCount: this.selectorStatistics.fieldUsage.get(fieldName) || 0
                    };
                };
                
                // 获取字段分组
                this.getFieldGroup = (fieldType) => {
                    if (['char', 'text'].includes(fieldType)) return 'basic';
                    if (['integer', 'float', 'monetary'].includes(fieldType)) return 'numbers';
                    if (['date', 'datetime'].includes(fieldType)) return 'dates';
                    if (['many2one', 'one2many', 'many2many'].includes(fieldType)) return 'relations';
                    return 'custom';
                };
                
                // 缓存数据
                this.setCachedData = (key, data) => {
                    // 实现缓存逻辑
                    console.log('Caching data:', key);
                };
                
                // 获取缓存数据
                this.getCachedData = (key) => {
                    // 实现缓存获取逻辑
                    return null;
                };
                
                // 记录字段使用
                this.recordFieldUsage = (field) => {
                    this.selectorStatistics.totalSelections++;
                    
                    const currentUsage = this.selectorStatistics.fieldUsage.get(field.name) || 0;
                    this.selectorStatistics.fieldUsage.set(field.name, currentUsage + 1);
                    
                    if (this.enhancedState.currentModel) {
                        const modelUsage = this.selectorStatistics.modelUsage.get(this.enhancedState.currentModel) || 0;
                        this.selectorStatistics.modelUsage.set(this.enhancedState.currentModel, modelUsage + 1);
                    }
                };
                
                // 字段选择事件
                this.onFieldSelected = (field) => {
                    console.log('Field selected:', field);
                };
                
                // 记录选择时间
                this.recordSelectionTime = (duration) => {
                    this.selectorStatistics.averageSelectionTime = 
                        (this.selectorStatistics.averageSelectionTime + duration) / 2;
                };
            }
            
            addContextFeatures() {
                // 上下文功能
                this.contextManager = {
                    setRecord: (record) => this.setRecord(record),
                    setRecordProps: (props) => this.setRecordProps(props),
                    getContext: () => this.getContext()
                };
            }
            
            addFilteringFeatures() {
                // 过滤功能
                this.filterManager = {
                    applyFilter: (filter) => this.applyFilter(filter),
                    clearFilters: () => this.clearFilters(),
                    getActiveFilters: () => Array.from(this.enhancedState.activeFilters)
                };
            }
            
            // 设置记录
            setRecord(record) {
                this.contextManager.currentRecord = record;
                if (record && record.resModel !== this.enhancedState.currentModel) {
                    this.enhancedState.currentModel = record.resModel;
                    this.getAvailableFields(record.resModel);
                }
            }
            
            // 设置记录属性
            setRecordProps(props) {
                this.contextManager.recordProps = props;
            }
            
            // 获取上下文
            getContext() {
                return {
                    record: this.contextManager.currentRecord,
                    recordProps: this.contextManager.recordProps,
                    model: this.enhancedState.currentModel
                };
            }
        };
    }
    
    // 设置上下文管理
    setupContextManagement() {
        this.contextManagementConfig = {
            enabled: this.dynamicSelectorConfig.enableContextAware,
            autoUpdate: true,
            cacheContext: true
        };
    }
    
    // 设置字段过滤
    setupFieldFiltering() {
        this.fieldFilteringConfig = {
            enabled: this.dynamicSelectorConfig.enableDynamicFiltering,
            defaultFilters: ['basic'],
            enableCustomFilters: true
        };
    }
    
    // 设置缓存系统
    setupCacheSystem() {
        this.cacheSystemConfig = {
            enabled: this.dynamicSelectorConfig.enableCaching,
            maxSize: 100,
            ttl: 300000 // 5分钟
        };
    }
    
    // 创建动态模型字段选择器
    createDynamicModelFieldSelector(props) {
        return new this.EnhancedDynamicModelFieldSelector(props);
    }
    
    // 注册字段过滤器
    registerFieldFilter(name, types) {
        this.fieldFilters.set(name, types);
    }
    
    // 注册字段分组
    registerFieldGroup(name, config) {
        this.fieldGroups.set(name, config);
    }
    
    // 批量选择字段
    batchSelectFields(selectors, fields) {
        const results = [];
        
        for (let i = 0; i < selectors.length && i < fields.length; i++) {
            try {
                selectors[i].selectField(fields[i]);
                results.push({ selector: selectors[i], field: fields[i], success: true });
            } catch (error) {
                results.push({ selector: selectors[i], field: fields[i], success: false, error });
            }
        }
        
        return results;
    }
    
    // 获取热门字段
    getPopularFields(limit = 10) {
        const sorted = Array.from(this.selectorStatistics.fieldUsage.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, limit);
        
        return sorted.map(([field, count]) => ({ field, count }));
    }
    
    // 获取选择器统计
    getSelectorStatistics() {
        return {
            ...this.selectorStatistics,
            filterCount: this.fieldFilters.size,
            groupCount: this.fieldGroups.size,
            cacheHitRate: this.selectorStatistics.cacheHits / Math.max(this.selectorStatistics.totalSelections, 1) * 100
        };
    }
    
    // 导出配置
    exportConfiguration() {
        return {
            fieldFilters: Object.fromEntries(this.fieldFilters),
            fieldGroups: Object.fromEntries(this.fieldGroups),
            config: this.dynamicSelectorConfig
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理过滤器和分组
        this.fieldFilters.clear();
        this.fieldGroups.clear();
        
        // 重置统计
        this.selectorStatistics = {
            totalSelections: 0,
            fieldUsage: new Map(),
            modelUsage: new Map(),
            averageSelectionTime: 0,
            cacheHits: 0
        };
    }
}

// 使用示例
const dynamicSelectorManager = new DynamicModelFieldSelectorManager();

// 创建动态模型字段选择器
const dynamicSelector = dynamicSelectorManager.createDynamicModelFieldSelector({
    record: {
        resModel: 'res.partner',
        data: { name: 'Test Partner' }
    },
    recordProps: {
        readonly: false,
        required: true
    }
});

// 注册自定义过滤器
dynamicSelectorManager.registerFieldFilter('custom', ['custom_field_type']);

// 获取统计信息
const stats = dynamicSelectorManager.getSelectorStatistics();
console.log('Dynamic model field selector statistics:', stats);
```

## 技术特点

### 1. 继承扩展
- **基类继承**: 继承ModelFieldSelector的所有功能
- **属性扩展**: 扩展记录和属性相关的属性
- **功能增强**: 在基础功能上增加动态特性
- **接口兼容**: 保持与基类的接口兼容性

### 2. 动态特性
- **记录上下文**: 支持记录对象的上下文传递
- **属性传递**: 支持记录属性的动态传递
- **上下文感知**: 根据上下文动态调整行为
- **灵活配置**: 支持灵活的配置选项

### 3. 简洁设计
- **最小实现**: 最简化的代码实现
- **专注扩展**: 专注于扩展基类功能
- **清晰结构**: 清晰的组件结构
- **易于维护**: 易于维护和扩展

### 4. 可扩展性
- **属性扩展**: 易于扩展更多属性
- **功能扩展**: 易于扩展更多功能
- **组件化**: 基于组件的设计
- **模块化**: 模块化的架构

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **类继承**: 继承ModelFieldSelector基类
- **属性继承**: 继承所有基类属性
- **功能扩展**: 扩展基类功能

### 2. 适配器模式 (Adapter Pattern)
- **接口适配**: 适配动态环境的接口需求
- **上下文适配**: 适配不同的上下文环境
- **属性适配**: 适配不同的属性需求

### 3. 策略模式 (Strategy Pattern)
- **选择策略**: 不同的字段选择策略
- **过滤策略**: 不同的字段过滤策略
- **显示策略**: 不同的显示策略

### 4. 工厂模式 (Factory Pattern)
- **组件工厂**: 创建动态选择器组件
- **配置工厂**: 创建配置对象
- **上下文工厂**: 创建上下文对象

## 注意事项

1. **上下文管理**: 正确管理记录和属性上下文
2. **性能考虑**: 避免不必要的重复计算
3. **兼容性**: 确保与基类的兼容性
4. **扩展性**: 保持良好的扩展性

## 扩展建议

1. **字段验证**: 添加字段选择验证功能
2. **智能建议**: 基于上下文的智能字段建议
3. **批量操作**: 支持批量字段选择
4. **历史记录**: 添加选择历史记录
5. **自定义过滤**: 支持自定义过滤规则

该动态模型字段选择器为Odoo Web客户端提供了灵活的字段选择功能，通过继承基类和扩展动态特性确保了在动态环境中的良好适应性和可用性。

# ColorField - 颜色字段

## 概述

`color_field.js` 是 Odoo Web 客户端的颜色字段组件，负责显示颜色值。该模块包含32行代码，是一个简洁的颜色显示组件，专门用于以颜色块的形式展示字符串类型的颜色值，具备颜色解析、视觉显示等特性，是颜色相关数据展示的基础组件。

## 文件信息
- **路径**: `/web/static/src/views/fields/color/color_field.js`
- **行数**: 32
- **模块**: `@web/views/fields/color/color_field`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                             // OWL框架
'@web/core/registry'                    // 注册表
'@web/views/fields/standard_field_props' // 标准字段属性
```

## 核心功能

### 1. 组件定义

```javascript
const ColorField = class ColorField extends Component {
    static template = "web.ColorField";
    static props = {
        ...standardFieldProps,
    };
}
```

**组件特性**:
- **标准属性**: 继承所有标准字段属性
- **专用模板**: 使用ColorField专用模板
- **简洁设计**: 最简化的组件实现
- **只读显示**: 主要用于颜色值显示

### 2. 颜色获取

```javascript
get color() {
    return this.props.record.data[this.props.name] || "";
}
```

**颜色功能**:
- **值获取**: 获取字段的颜色值
- **默认处理**: 提供空字符串作为默认值
- **直接访问**: 直接访问记录数据
- **简单实现**: 最简单的颜色值获取

### 3. 字段注册

```javascript
const colorField = {
    component: ColorField,
    supportedTypes: ["char"],
    extractProps(fieldInfo, dynamicInfo) {
        return {
            readonly: dynamicInfo.readonly,
        };
    },
};

registry.category("fields").add("color", colorField);
```

**注册功能**:
- **组件注册**: 注册颜色字段组件
- **类型支持**: 仅支持char类型
- **属性提取**: 仅提取readonly属性
- **简化配置**: 最简化的字段配置

## 使用场景

### 1. 颜色字段管理器

```javascript
// 颜色字段管理器
class ColorFieldManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置颜色配置
        this.colorConfig = {
            enableColorPicker: false,
            enableColorValidation: true,
            enableColorPresets: true,
            enableColorHistory: true,
            enableColorPalette: true,
            defaultColorFormat: 'hex',
            enableAlphaChannel: false,
            enableColorNames: true
        };
        
        // 设置颜色格式
        this.colorFormats = new Map([
            ['hex', { pattern: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, example: '#FF0000' }],
            ['rgb', { pattern: /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/, example: 'rgb(255, 0, 0)' }],
            ['rgba', { pattern: /^rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\)$/, example: 'rgba(255, 0, 0, 1)' }],
            ['hsl', { pattern: /^hsl\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*\)$/, example: 'hsl(0, 100%, 50%)' }],
            ['hsla', { pattern: /^hsla\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*,\s*[\d.]+\s*\)$/, example: 'hsla(0, 100%, 50%, 1)' }]
        ]);
        
        // 设置颜色预设
        this.colorPresets = new Map([
            ['primary', '#007bff'],
            ['secondary', '#6c757d'],
            ['success', '#28a745'],
            ['danger', '#dc3545'],
            ['warning', '#ffc107'],
            ['info', '#17a2b8'],
            ['light', '#f8f9fa'],
            ['dark', '#343a40'],
            ['white', '#ffffff'],
            ['black', '#000000']
        ]);
        
        // 设置颜色名称
        this.colorNames = new Map([
            ['red', '#FF0000'],
            ['green', '#008000'],
            ['blue', '#0000FF'],
            ['yellow', '#FFFF00'],
            ['orange', '#FFA500'],
            ['purple', '#800080'],
            ['pink', '#FFC0CB'],
            ['brown', '#A52A2A'],
            ['gray', '#808080'],
            ['cyan', '#00FFFF']
        ]);
        
        // 设置颜色统计
        this.colorStatistics = {
            totalColors: 0,
            validColors: 0,
            invalidColors: 0,
            formatDistribution: new Map(),
            popularColors: new Map()
        };
        
        this.initializeColorSystem();
    }
    
    // 初始化颜色系统
    initializeColorSystem() {
        // 创建增强的颜色字段
        this.createEnhancedColorField();
        
        // 设置颜色验证
        this.setupColorValidation();
        
        // 设置颜色转换
        this.setupColorConversion();
        
        // 设置颜色分析
        this.setupColorAnalysis();
    }
    
    // 创建增强的颜色字段
    createEnhancedColorField() {
        const originalField = ColorField;
        
        this.EnhancedColorField = class extends originalField {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加验证功能
                this.addValidationFeatures();
                
                // 添加转换功能
                this.addConversionFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = {
                    isValidColor: true,
                    colorFormat: 'hex',
                    colorInfo: null,
                    contrastRatio: null,
                    colorHarmony: [],
                    colorHistory: []
                };
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 增强的颜色获取
                this.enhancedGetColor = () => {
                    const rawColor = this.color;
                    
                    if (!rawColor) {
                        return '';
                    }
                    
                    // 验证颜色
                    const isValid = this.validateColor(rawColor);
                    this.enhancedState.isValidColor = isValid;
                    
                    if (!isValid) {
                        return this.getDefaultColor();
                    }
                    
                    // 标准化颜色
                    const normalizedColor = this.normalizeColor(rawColor);
                    
                    // 分析颜色
                    this.analyzeColor(normalizedColor);
                    
                    // 记录颜色使用
                    this.recordColorUsage(normalizedColor);
                    
                    return normalizedColor;
                };
                
                // 验证颜色
                this.validateColor = (color) => {
                    if (!color || typeof color !== 'string') {
                        return false;
                    }
                    
                    // 检查颜色名称
                    if (this.colorNames.has(color.toLowerCase())) {
                        return true;
                    }
                    
                    // 检查各种格式
                    for (const [format, config] of this.colorFormats) {
                        if (config.pattern.test(color)) {
                            this.enhancedState.colorFormat = format;
                            return true;
                        }
                    }
                    
                    return false;
                };
                
                // 标准化颜色
                this.normalizeColor = (color) => {
                    // 转换颜色名称
                    if (this.colorNames.has(color.toLowerCase())) {
                        return this.colorNames.get(color.toLowerCase());
                    }
                    
                    // 标准化十六进制
                    if (color.startsWith('#')) {
                        return color.toUpperCase();
                    }
                    
                    // 其他格式保持原样
                    return color;
                };
                
                // 分析颜色
                this.analyzeColor = (color) => {
                    try {
                        const colorInfo = this.parseColor(color);
                        this.enhancedState.colorInfo = colorInfo;
                        
                        // 计算对比度
                        this.enhancedState.contrastRatio = this.calculateContrastRatio(color, '#FFFFFF');
                        
                        // 生成和谐色
                        this.enhancedState.colorHarmony = this.generateColorHarmony(color);
                        
                    } catch (error) {
                        console.warn('Color analysis failed:', error);
                    }
                };
                
                // 解析颜色
                this.parseColor = (color) => {
                    if (color.startsWith('#')) {
                        return this.parseHexColor(color);
                    } else if (color.startsWith('rgb')) {
                        return this.parseRgbColor(color);
                    } else if (color.startsWith('hsl')) {
                        return this.parseHslColor(color);
                    }
                    
                    return null;
                };
                
                // 解析十六进制颜色
                this.parseHexColor = (hex) => {
                    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
                    return result ? {
                        r: parseInt(result[1], 16),
                        g: parseInt(result[2], 16),
                        b: parseInt(result[3], 16),
                        format: 'hex'
                    } : null;
                };
                
                // 解析RGB颜色
                this.parseRgbColor = (rgb) => {
                    const match = rgb.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
                    return match ? {
                        r: parseInt(match[1]),
                        g: parseInt(match[2]),
                        b: parseInt(match[3]),
                        format: 'rgb'
                    } : null;
                };
                
                // 解析HSL颜色
                this.parseHslColor = (hsl) => {
                    const match = hsl.match(/hsl\((\d+),\s*(\d+)%,\s*(\d+)%\)/);
                    if (match) {
                        const h = parseInt(match[1]);
                        const s = parseInt(match[2]) / 100;
                        const l = parseInt(match[3]) / 100;
                        
                        const rgb = this.hslToRgb(h, s, l);
                        return {
                            ...rgb,
                            h: h,
                            s: s,
                            l: l,
                            format: 'hsl'
                        };
                    }
                    return null;
                };
                
                // HSL转RGB
                this.hslToRgb = (h, s, l) => {
                    h /= 360;
                    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
                    const p = 2 * l - q;
                    
                    const hueToRgb = (t) => {
                        if (t < 0) t += 1;
                        if (t > 1) t -= 1;
                        if (t < 1/6) return p + (q - p) * 6 * t;
                        if (t < 1/2) return q;
                        if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
                        return p;
                    };
                    
                    return {
                        r: Math.round(hueToRgb(h + 1/3) * 255),
                        g: Math.round(hueToRgb(h) * 255),
                        b: Math.round(hueToRgb(h - 1/3) * 255)
                    };
                };
                
                // 计算对比度
                this.calculateContrastRatio = (color1, color2) => {
                    const rgb1 = this.parseColor(color1);
                    const rgb2 = this.parseColor(color2);
                    
                    if (!rgb1 || !rgb2) return null;
                    
                    const luminance1 = this.calculateLuminance(rgb1.r, rgb1.g, rgb1.b);
                    const luminance2 = this.calculateLuminance(rgb2.r, rgb2.g, rgb2.b);
                    
                    const lighter = Math.max(luminance1, luminance2);
                    const darker = Math.min(luminance1, luminance2);
                    
                    return (lighter + 0.05) / (darker + 0.05);
                };
                
                // 计算亮度
                this.calculateLuminance = (r, g, b) => {
                    const [rs, gs, bs] = [r, g, b].map(c => {
                        c = c / 255;
                        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
                    });
                    
                    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
                };
                
                // 生成和谐色
                this.generateColorHarmony = (color) => {
                    const rgb = this.parseColor(color);
                    if (!rgb) return [];
                    
                    const hsl = this.rgbToHsl(rgb.r, rgb.g, rgb.b);
                    const harmony = [];
                    
                    // 互补色
                    harmony.push(this.hslToHex((hsl.h + 180) % 360, hsl.s, hsl.l));
                    
                    // 三角色
                    harmony.push(this.hslToHex((hsl.h + 120) % 360, hsl.s, hsl.l));
                    harmony.push(this.hslToHex((hsl.h + 240) % 360, hsl.s, hsl.l));
                    
                    return harmony;
                };
                
                // RGB转HSL
                this.rgbToHsl = (r, g, b) => {
                    r /= 255;
                    g /= 255;
                    b /= 255;
                    
                    const max = Math.max(r, g, b);
                    const min = Math.min(r, g, b);
                    let h, s, l = (max + min) / 2;
                    
                    if (max === min) {
                        h = s = 0;
                    } else {
                        const d = max - min;
                        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
                        
                        switch (max) {
                            case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                            case g: h = (b - r) / d + 2; break;
                            case b: h = (r - g) / d + 4; break;
                        }
                        h /= 6;
                    }
                    
                    return { h: h * 360, s: s, l: l };
                };
                
                // HSL转十六进制
                this.hslToHex = (h, s, l) => {
                    const rgb = this.hslToRgb(h, s, l);
                    return `#${((1 << 24) + (rgb.r << 16) + (rgb.g << 8) + rgb.b).toString(16).slice(1).toUpperCase()}`;
                };
                
                // 获取默认颜色
                this.getDefaultColor = () => {
                    return this.colorPresets.get('primary') || '#007bff';
                };
                
                // 记录颜色使用
                this.recordColorUsage = (color) => {
                    this.colorStatistics.totalColors++;
                    
                    if (this.enhancedState.isValidColor) {
                        this.colorStatistics.validColors++;
                        
                        // 记录格式分布
                        const format = this.enhancedState.colorFormat;
                        const currentCount = this.colorStatistics.formatDistribution.get(format) || 0;
                        this.colorStatistics.formatDistribution.set(format, currentCount + 1);
                        
                        // 记录热门颜色
                        const popularCount = this.colorStatistics.popularColors.get(color) || 0;
                        this.colorStatistics.popularColors.set(color, popularCount + 1);
                    } else {
                        this.colorStatistics.invalidColors++;
                    }
                };
                
                // 转换颜色格式
                this.convertColorFormat = (color, targetFormat) => {
                    const rgb = this.parseColor(color);
                    if (!rgb) return color;
                    
                    switch (targetFormat) {
                        case 'hex':
                            return `#${((1 << 24) + (rgb.r << 16) + (rgb.g << 8) + rgb.b).toString(16).slice(1).toUpperCase()}`;
                        case 'rgb':
                            return `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`;
                        case 'hsl':
                            const hsl = this.rgbToHsl(rgb.r, rgb.g, rgb.b);
                            return `hsl(${Math.round(hsl.h)}, ${Math.round(hsl.s * 100)}%, ${Math.round(hsl.l * 100)}%)`;
                        default:
                            return color;
                    }
                };
                
                // 获取颜色信息
                this.getColorInfo = () => {
                    return {
                        color: this.enhancedGetColor(),
                        isValid: this.enhancedState.isValidColor,
                        format: this.enhancedState.colorFormat,
                        info: this.enhancedState.colorInfo,
                        contrastRatio: this.enhancedState.contrastRatio,
                        harmony: this.enhancedState.colorHarmony
                    };
                };
                
                // 获取颜色样式
                this.getColorStyle = () => {
                    const color = this.enhancedGetColor();
                    
                    return {
                        backgroundColor: color,
                        border: '1px solid #ddd',
                        borderRadius: '4px',
                        width: '100%',
                        height: '100%',
                        minHeight: '20px'
                    };
                };
            }
            
            addValidationFeatures() {
                // 验证功能
                this.validationManager = {
                    enabled: this.colorConfig.enableColorValidation,
                    formats: this.colorFormats,
                    validate: (color) => this.validateColor(color)
                };
            }
            
            addConversionFeatures() {
                // 转换功能
                this.conversionManager = {
                    supportedFormats: Array.from(this.colorFormats.keys()),
                    convert: (color, format) => this.convertColorFormat(color, format)
                };
            }
            
            // 重写原始方法
            get color() {
                return this.enhancedGetColor();
            }
        };
    }
    
    // 设置颜色验证
    setupColorValidation() {
        this.validationConfig = {
            enabled: this.colorConfig.enableColorValidation,
            strictMode: false,
            allowColorNames: this.colorConfig.enableColorNames
        };
    }
    
    // 设置颜色转换
    setupColorConversion() {
        this.conversionConfig = {
            defaultFormat: this.colorConfig.defaultColorFormat,
            enableAutoConversion: false,
            preserveAlpha: this.colorConfig.enableAlphaChannel
        };
    }
    
    // 设置颜色分析
    setupColorAnalysis() {
        this.analysisConfig = {
            enableContrastAnalysis: true,
            enableHarmonyGeneration: true,
            enableColorInfo: true
        };
    }
    
    // 创建颜色字段
    createColorField(props) {
        return new this.EnhancedColorField(props);
    }
    
    // 注册颜色预设
    registerColorPreset(name, color) {
        this.colorPresets.set(name, color);
    }
    
    // 注册颜色名称
    registerColorName(name, color) {
        this.colorNames.set(name.toLowerCase(), color);
    }
    
    // 批量验证颜色
    batchValidateColors(fields) {
        const results = [];
        
        for (const field of fields) {
            try {
                const color = field.color;
                const isValid = field.validateColor(color);
                results.push({ field, color, isValid });
            } catch (error) {
                results.push({ field, color: null, isValid: false, error });
            }
        }
        
        return results;
    }
    
    // 获取热门颜色
    getPopularColors(limit = 10) {
        const sorted = Array.from(this.colorStatistics.popularColors.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, limit);
        
        return sorted.map(([color, count]) => ({ color, count }));
    }
    
    // 获取颜色统计
    getColorStatistics() {
        return {
            ...this.colorStatistics,
            presetCount: this.colorPresets.size,
            nameCount: this.colorNames.size,
            formatCount: this.colorFormats.size,
            validPercentage: this.colorStatistics.validColors / Math.max(this.colorStatistics.totalColors, 1) * 100
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理配置
        this.colorFormats.clear();
        this.colorPresets.clear();
        this.colorNames.clear();
        
        // 重置统计
        this.colorStatistics = {
            totalColors: 0,
            validColors: 0,
            invalidColors: 0,
            formatDistribution: new Map(),
            popularColors: new Map()
        };
    }
}

// 使用示例
const colorManager = new ColorFieldManager();

// 创建颜色字段
const colorField = colorManager.createColorField({
    name: 'theme_color',
    record: {
        data: { theme_color: '#FF5733' },
        fields: { theme_color: { type: 'char' } }
    }
});

// 注册自定义颜色
colorManager.registerColorPreset('brand', '#FF5733');
colorManager.registerColorName('brand-red', '#FF5733');

// 获取颜色信息
const colorInfo = colorField.getColorInfo();
console.log('Color information:', colorInfo);

// 获取统计信息
const stats = colorManager.getColorStatistics();
console.log('Color field statistics:', stats);
```

## 技术特点

### 1. 简洁高效
- **最小实现**: 最简化的代码实现
- **专注显示**: 专注于颜色值显示
- **轻量级**: 极小的资源占用
- **快速渲染**: 快速的颜色渲染

### 2. 颜色处理
- **值获取**: 简单的颜色值获取
- **默认处理**: 提供默认值处理
- **字符支持**: 支持字符串类型的颜色值
- **直接显示**: 直接显示颜色值

### 3. 扩展性
- **组件化**: 基于组件的设计
- **可扩展**: 易于扩展功能
- **标准化**: 遵循标准接口
- **模板化**: 使用专用模板

### 4. 兼容性
- **类型支持**: 支持char类型字段
- **属性提取**: 简单的属性提取
- **只读支持**: 支持只读模式
- **标准接口**: 标准的字段接口

## 设计模式

### 1. 组件模式 (Component Pattern)
- **UI组件**: 封装颜色显示UI
- **状态管理**: 管理颜色状态
- **模板渲染**: 使用模板渲染

### 2. 策略模式 (Strategy Pattern)
- **显示策略**: 不同的颜色显示策略
- **格式策略**: 不同的颜色格式策略
- **验证策略**: 不同的颜色验证策略

### 3. 工厂模式 (Factory Pattern)
- **组件工厂**: 创建颜色字段组件
- **颜色工厂**: 创建颜色对象
- **配置工厂**: 创建配置对象

### 4. 适配器模式 (Adapter Pattern)
- **格式适配**: 适配不同颜色格式
- **数据适配**: 适配不同数据类型
- **接口适配**: 适配标准字段接口

## 注意事项

1. **颜色格式**: 确保颜色值格式的正确性
2. **浏览器兼容**: 确保在不同浏览器中的兼容性
3. **性能考虑**: 避免复杂的颜色计算
4. **用户体验**: 提供清晰的颜色显示

## 扩展建议

1. **颜色选择器**: 添加颜色选择器功能
2. **格式转换**: 支持多种颜色格式转换
3. **颜色验证**: 添加颜色值验证功能
4. **预设颜色**: 支持预设颜色选择
5. **颜色分析**: 添加颜色分析功能

该颜色字段为Odoo Web客户端提供了基础的颜色值显示功能，通过简洁的实现和标准的接口确保了良好的性能和易用性。

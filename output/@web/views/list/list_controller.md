# ListController - 列表视图控制器

## 概述

`list_controller.js` 是 Odoo Web 客户端列表视图的控制器组件，负责处理列表视图的业务逻辑和用户交互。该模块包含676行代码，是一个OWL组件，专门用于管理列表视图的状态、数据操作、用户交互等功能，具备记录管理、批量操作、导出功能、搜索集成等特性，是列表视图系统中业务逻辑处理的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/list/list_controller.js`
- **行数**: 676
- **模块**: `@web/views/list/list_controller`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'                           // 翻译服务
'@web/core/confirmation_dialog/confirmation_dialog'    // 确认对话框
'@web/core/network/download'                           // 下载功能
'@web/core/network/rpc'                                // RPC调用
'@web/core/py_js/py'                                   // Python表达式
'@web/core/user'                                       // 用户服务
'@web/core/utils/arrays'                               // 数组工具
'@web/core/utils/hooks'                                // 工具钩子
'@web/core/utils/objects'                              // 对象工具
'@web/search/action_hook'                              // 动作钩子
'@web/search/action_menus/action_menus'                // 动作菜单
'@web/search/layout'                                   // 搜索布局
'@web/search/pager_hook'                               // 分页钩子
'@web/model/model'                                     // 模型基类
'@web/model/relational_model/dynamic_record_list'      // 动态记录列表
'@web/model/relational_model/utils'                    // 关系模型工具
'@web/views/standard_view_props'                       // 标准视图属性
'@web/views/view_button/multi_record_view_button'      // 多记录视图按钮
'@web/views/view_button/view_button'                   // 视图按钮
'@web/views/view_button/view_button_hook'              // 视图按钮钩子
'@web/views/view_dialogs/export_data_dialog'           // 导出数据对话框
'@web/views/list/list_confirmation_dialog'             // 列表确认对话框
'@web/search/search_bar/search_bar'                    // 搜索栏
'@web/search/search_bar/search_bar_toggler'            // 搜索栏切换器
'@web/session'                                         // 会话服务
'@web/views/list/list_cog_menu'                        // 列表齿轮菜单
'@web/core/dropdown/dropdown_item'                     // 下拉菜单项
'@odoo/owl'                                            // OWL框架
```

## 主要组件定义

### 1. ListController - 列表视图控制器

```javascript
class ListController extends Component {
    static template = "web.ListView";
    static components = {
        Layout,
        ActionMenus,
        ViewButton,
        MultiRecordViewButton,
        SearchBar,
        ListCogMenu,
        DropdownItem,
    };
    static props = {
        ...standardViewProps,
        Model: Function,
        Renderer: Function,
        archInfo: Object,
        buttonTemplate: String,
    };

    setup() {
        this.actionService = useService("action");
        this.dialogService = useService("dialog");
        this.notification = useService("notification");
        this.orm = useService("orm");
        this.router = useService("router");
        this.user = useService("user");

        this.model = useModelWithSampleData(this.props.Model, this.modelParams);
        this.pager = usePager();

        this.searchBarToggler = useSearchBarToggler();
        this.viewButtons = useViewButtons();

        useSetupAction({
            getLocalState: () => this.getLocalState(),
            getContext: () => this.getActionContext(),
        });

        this.state = useState({
            showSearchBar: false,
        });

        this.rootRef = useRef("root");

        useBus(this.model, "update", this.render);

        onWillStart(this.onWillStart);
        onMounted(this.onMounted);
        onWillPatch(this.onWillPatch);
    }
}
```

**组件特性**:
- **模板定义**: 使用ListView模板
- **子组件**: 集成布局、动作菜单、按钮等组件
- **服务集成**: 集成多种核心服务
- **状态管理**: 管理控制器状态

## 核心功能

### 1. 模型参数配置

```javascript
get modelParams() {
    const { archInfo, fields, resModel, context, domain, groupBy, orderBy } = this.props;

    return {
        config: {
            resModel,
            fields,
            activeFields: archInfo.activeFields,
            handleField: archInfo.handleField,
            openGroupsByDefault: archInfo.openGroupsByDefault,
            isMonoRecord: false,
            limit: archInfo.limit,
            countLimit: archInfo.countLimit,
        },
        state: {
            context,
            domain,
            groupBy,
            orderBy,
            offset: 0,
        },
    };
}
```

**配置功能**:
- **模型配置**: 配置关系模型参数
- **字段配置**: 设置活动字段和处理字段
- **分页配置**: 设置记录限制和偏移量
- **状态配置**: 配置上下文、域、分组、排序

### 2. 记录操作

```javascript
// 创建记录
async createRecord() {
    const { context } = this.props;
    const activeFields = this.model.root.activeFields;

    const record = await this.model.root.addNew({
        mode: "edit",
        position: "top",
    });

    return record;
}

// 删除记录
async deleteRecords(records) {
    const recordIds = records.map(record => record.resId).filter(Boolean);

    if (recordIds.length === 0) {
        return;
    }

    const dialogProps = {
        body: deleteConfirmationMessage(records.length),
        confirm: async () => {
            await this.model.root.deleteRecords(recordIds);
            this.notification.add(_t("Records deleted"), { type: "success" });
        },
        cancel: () => {},
    };

    this.dialogService.add(ConfirmationDialog, dialogProps);
}

// 复制记录
async duplicateRecords(records) {
    const recordIds = records.map(record => record.resId).filter(Boolean);

    if (recordIds.length === 0) {
        return;
    }

    await this.model.root.duplicateRecords(recordIds);
    this.notification.add(_t("Records duplicated"), { type: "success" });
}
```

**记录操作功能**:
- **创建记录**: 在列表顶部添加新记录
- **删除记录**: 批量删除选中记录
- **复制记录**: 批量复制选中记录
- **确认对话框**: 显示操作确认对话框

### 3. 导出功能

```javascript
// 导出数据
async exportData() {
    const { resModel, context } = this.props;
    const { domain, groupBy, orderBy } = this.model.root;

    const dialogProps = {
        context,
        defaultExportList: this.getDefaultExportList(),
        domain,
        groupBy,
        ids: this.getSelectedRecordIds(),
        model: resModel,
        orderBy,
        title: _t("Export Data"),
    };

    this.dialogService.add(ExportDataDialog, dialogProps);
}

// 获取默认导出列表
getDefaultExportList() {
    const { archInfo } = this.props;
    const exportList = [];

    for (const column of archInfo.columns) {
        if (column.type === "field" && !column.optional) {
            exportList.push({
                name: column.name,
                label: column.label || column.name,
            });
        }
    }

    return exportList;
}
```

**导出功能**:
- **数据导出**: 导出列表数据到文件
- **字段选择**: 选择要导出的字段
- **过滤导出**: 根据域和分组导出
- **格式支持**: 支持多种导出格式

### 4. 批量操作

```javascript
// 获取选中记录
getSelectedRecords() {
    return this.model.root.records.filter(record => record.selected);
}

// 全选/取消全选
toggleSelectAll() {
    const records = this.model.root.records;
    const allSelected = records.every(record => record.selected);

    for (const record of records) {
        record.selected = !allSelected;
    }

    this.model.notify();
}

// 批量编辑
async batchEdit(records, values) {
    const recordIds = records.map(record => record.resId).filter(Boolean);

    if (recordIds.length === 0) {
        return;
    }

    await this.orm.write(this.props.resModel, recordIds, values);
    await this.model.root.load();

    this.notification.add(_t("Records updated"), { type: "success" });
}
```

**批量操作功能**:
- **记录选择**: 管理记录的选择状态
- **全选功能**: 支持全选和取消全选
- **批量编辑**: 批量修改记录字段
- **状态同步**: 同步选择状态到模型
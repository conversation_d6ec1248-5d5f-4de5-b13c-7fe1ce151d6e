# ListConfirmationDialog - 列表确认对话框

## 概述

`list_confirmation_dialog.js` 是 Odoo Web 客户端列表视图的确认对话框组件，提供了列表操作的确认功能。该模块包含57行代码，是一个OWL组件，专门用于在列表视图中执行需要用户确认的操作时显示确认对话框，具备自动聚焦、字段显示、记录统计、域选择等特性，是列表视图系统中用户确认交互的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/list/list_confirmation_dialog.js`
- **行数**: 57
- **模块**: `@web/views/list/list_confirmation_dialog`

## 依赖关系

```javascript
// 核心依赖
'@web/core/dialog/dialog'       // 对话框组件
'@web/core/l10n/translation'    // 翻译服务
'@web/views/fields/field'       // 字段组件
'@web/core/utils/hooks'         // 工具钩子
'@odoo/owl'                     // OWL框架
```

## 主要组件定义

### 1. ListConfirmationDialog - 列表确认对话框

```javascript
class ListConfirmationDialog extends Component {
    static template = "web.ListView.ConfirmationModal";
    static components = { Dialog, Field };
    static props = {
        close: Function,
        title: {
            validate: (m) => {
                return (
                    typeof m === "string" ||
                    (typeof m === "object" && typeof m.toString === "function")
                );
            },
            optional: true,
        },
        confirm: { type: Function, optional: true },
        cancel: { type: Function, optional: true },
        isDomainSelected: Boolean,
        fields: Object,
        nbRecords: Number,
        nbValidRecords: Number,
        record: Object,
    };
    static defaultProps = {
        title: _t("Confirmation"),
    };

    setup() {
        useAutofocus();
    }
}
```

**组件特性**:
- **专用模板**: 使用ListView.ConfirmationModal模板
- **子组件**: 集成Dialog和Field组件
- **自动聚焦**: 自动聚焦到对话框
- **灵活标题**: 支持字符串和对象类型的标题

## 核心功能

### 1. 属性配置

```javascript
static props = {
    close: Function,                    // 关闭回调函数
    title: {                           // 对话框标题
        validate: (m) => {
            return (
                typeof m === "string" ||
                (typeof m === "object" && typeof m.toString === "function")
            );
        },
        optional: true,
    },
    confirm: { type: Function, optional: true },    // 确认回调函数
    cancel: { type: Function, optional: true },     // 取消回调函数
    isDomainSelected: Boolean,                      // 是否选择了域
    fields: Object,                                 // 字段定义
    nbRecords: Number,                              // 记录总数
    nbValidRecords: Number,                         // 有效记录数
    record: Object,                                 // 记录对象
};
```

**属性功能**:
- **回调函数**: 提供确认、取消、关闭的回调
- **标题验证**: 验证标题的类型和格式
- **域选择**: 标识是否基于域选择
- **记录信息**: 提供记录数量和有效性信息
- **字段支持**: 支持字段的显示和编辑

### 2. 默认属性

```javascript
static defaultProps = {
    title: _t("Confirmation"),
};
```

**默认属性功能**:
- **默认标题**: 提供本地化的默认标题
- **用户友好**: 确保始终有合适的标题显示
- **国际化**: 支持多语言的默认标题
- **一致性**: 保持对话框标题的一致性

### 3. 组件设置

```javascript
setup() {
    useAutofocus();
}
```

**设置功能**:
- **自动聚焦**: 自动聚焦到对话框元素
- **用户体验**: 提升用户交互体验
- **键盘导航**: 支持键盘导航
- **可访问性**: 增强可访问性支持

### 4. 取消操作

```javascript
_cancel() {
    if (this.props.cancel) {
        this.props.cancel();
    }
    this.props.close();
}
```

**取消功能**:
- **回调执行**: 执行取消回调函数
- **对话框关闭**: 关闭确认对话框
- **状态清理**: 清理相关状态
- **操作中断**: 中断当前操作

## 使用场景

### 1. 确认对话框管理器

```javascript
// 确认对话框管理器
class ConfirmationDialogManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置对话框配置
        this.dialogConfig = {
            enableAutoFocus: true,
            enableKeyboardShortcuts: true,
            enableCustomTemplates: true,
            enableAnalytics: true,
            defaultTimeout: 30000, // 30秒超时
            maxDialogs: 3
        };
        
        // 设置对话框队列
        this.dialogQueue = [];
        
        // 设置对话框模板
        this.dialogTemplates = new Map();
        
        // 设置对话框统计
        this.dialogStatistics = {
            totalShown: 0,
            totalConfirmed: 0,
            totalCancelled: 0,
            totalTimedOut: 0,
            averageResponseTime: 0
        };
        
        this.initializeDialogSystem();
    }
    
    // 初始化对话框系统
    initializeDialogSystem() {
        // 创建增强的确认对话框
        this.createEnhancedConfirmationDialog();
        
        // 设置默认模板
        this.setupDefaultTemplates();
        
        // 设置键盘快捷键
        this.setupKeyboardShortcuts();
        
        // 设置分析功能
        this.setupAnalytics();
    }
    
    // 创建增强的确认对话框
    createEnhancedConfirmationDialog() {
        const originalDialog = ListConfirmationDialog;
        
        this.EnhancedConfirmationDialog = class extends originalDialog {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加超时功能
                this.addTimeoutSupport();
                
                // 添加分析功能
                this.addAnalyticsSupport();
                
                // 添加自定义功能
                this.addCustomFeatures();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = useState({
                    showTime: Date.now(),
                    isProcessing: false,
                    timeoutId: null,
                    responseTime: 0,
                    keyboardShortcuts: true
                });
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 智能确认
                this.smartConfirm = async () => {
                    if (this.enhancedState.isProcessing) return;
                    
                    this.enhancedState.isProcessing = true;
                    this.enhancedState.responseTime = Date.now() - this.enhancedState.showTime;
                    
                    try {
                        // 执行确认前验证
                        const isValid = await this.validateBeforeConfirm();
                        if (!isValid) {
                            this.showValidationError();
                            return;
                        }
                        
                        // 执行确认回调
                        if (this.props.confirm) {
                            await this.props.confirm();
                        }
                        
                        // 记录统计
                        this.recordConfirmation();
                        
                        // 关闭对话框
                        this.props.close();
                        
                    } catch (error) {
                        this.handleConfirmError(error);
                    } finally {
                        this.enhancedState.isProcessing = false;
                    }
                };
                
                // 智能取消
                this.smartCancel = () => {
                    this.enhancedState.responseTime = Date.now() - this.enhancedState.showTime;
                    
                    // 记录统计
                    this.recordCancellation();
                    
                    // 执行原始取消
                    this._cancel();
                };
                
                // 确认前验证
                this.validateBeforeConfirm = async () => {
                    // 检查记录数量
                    if (this.props.nbRecords === 0) {
                        return false;
                    }
                    
                    // 检查有效记录
                    if (this.props.nbValidRecords === 0) {
                        return false;
                    }
                    
                    // 自定义验证逻辑
                    if (this.props.customValidation) {
                        return await this.props.customValidation(this.props);
                    }
                    
                    return true;
                };
                
                // 显示验证错误
                this.showValidationError = () => {
                    this.env.services.notification.add(
                        _t("Cannot perform this action. Please check your selection."),
                        { type: "warning" }
                    );
                };
                
                // 处理确认错误
                this.handleConfirmError = (error) => {
                    console.error("Confirmation error:", error);
                    
                    this.env.services.notification.add(
                        _t("An error occurred while processing your request."),
                        { type: "danger" }
                    );
                };
                
                // 获取对话框信息
                this.getDialogInfo = () => {
                    return {
                        title: this.props.title,
                        nbRecords: this.props.nbRecords,
                        nbValidRecords: this.props.nbValidRecords,
                        isDomainSelected: this.props.isDomainSelected,
                        showTime: this.enhancedState.showTime,
                        responseTime: this.enhancedState.responseTime,
                        isProcessing: this.enhancedState.isProcessing
                    };
                };
            }
            
            addTimeoutSupport() {
                if (!this.dialogConfig.defaultTimeout) return;
                
                // 设置超时
                this.enhancedState.timeoutId = setTimeout(() => {
                    this.onTimeout();
                }, this.dialogConfig.defaultTimeout);
                
                // 清理超时
                onWillUnmount(() => {
                    if (this.enhancedState.timeoutId) {
                        clearTimeout(this.enhancedState.timeoutId);
                    }
                });
            }
            
            addAnalyticsSupport() {
                if (!this.dialogConfig.enableAnalytics) return;
                
                // 记录显示
                this.recordShow = () => {
                    this.dialogStatistics.totalShown++;
                };
                
                // 记录确认
                this.recordConfirmation = () => {
                    this.dialogStatistics.totalConfirmed++;
                    this.updateAverageResponseTime();
                };
                
                // 记录取消
                this.recordCancellation = () => {
                    this.dialogStatistics.totalCancelled++;
                    this.updateAverageResponseTime();
                };
                
                // 记录超时
                this.recordTimeout = () => {
                    this.dialogStatistics.totalTimedOut++;
                };
                
                // 更新平均响应时间
                this.updateAverageResponseTime = () => {
                    const totalResponses = this.dialogStatistics.totalConfirmed + this.dialogStatistics.totalCancelled;
                    if (totalResponses > 0) {
                        this.dialogStatistics.averageResponseTime = 
                            (this.dialogStatistics.averageResponseTime * (totalResponses - 1) + this.enhancedState.responseTime) / totalResponses;
                    }
                };
                
                // 组件挂载时记录
                onMounted(() => {
                    this.recordShow();
                });
            }
            
            addCustomFeatures() {
                // 添加键盘快捷键
                if (this.dialogConfig.enableKeyboardShortcuts) {
                    this.addKeyboardShortcuts();
                }
                
                // 添加自定义模板支持
                if (this.dialogConfig.enableCustomTemplates) {
                    this.addCustomTemplateSupport();
                }
            }
            
            addKeyboardShortcuts() {
                useExternalListener(document, 'keydown', (event) => {
                    if (!this.enhancedState.keyboardShortcuts) return;
                    
                    switch (event.key) {
                        case 'Enter':
                            if (event.ctrlKey || event.metaKey) {
                                event.preventDefault();
                                this.smartConfirm();
                            }
                            break;
                        case 'Escape':
                            event.preventDefault();
                            this.smartCancel();
                            break;
                        case 'y':
                        case 'Y':
                            if (event.altKey) {
                                event.preventDefault();
                                this.smartConfirm();
                            }
                            break;
                        case 'n':
                        case 'N':
                            if (event.altKey) {
                                event.preventDefault();
                                this.smartCancel();
                            }
                            break;
                    }
                });
            }
            
            addCustomTemplateSupport() {
                // 获取自定义模板
                this.getCustomTemplate = () => {
                    const templateKey = this.props.templateKey || 'default';
                    return this.dialogTemplates.get(templateKey);
                };
                
                // 应用自定义模板
                this.applyCustomTemplate = () => {
                    const customTemplate = this.getCustomTemplate();
                    if (customTemplate) {
                        // 应用自定义样式和内容
                        this.applyTemplateStyles(customTemplate);
                    }
                };
            }
            
            // 超时处理
            onTimeout() {
                this.recordTimeout();
                
                this.env.services.notification.add(
                    _t("Dialog timed out. Operation cancelled."),
                    { type: "warning" }
                );
                
                this.smartCancel();
            }
            
            // 重写取消方法
            _cancel() {
                this.smartCancel();
            }
            
            // 获取统计信息
            getStatistics() {
                return {
                    ...this.dialogStatistics,
                    currentDialog: this.getDialogInfo()
                };
            }
        };
    }
    
    // 设置默认模板
    setupDefaultTemplates() {
        // 删除确认模板
        this.dialogTemplates.set('delete', {
            title: _t('Delete Records'),
            icon: 'fa-trash text-danger',
            confirmText: _t('Delete'),
            confirmClass: 'btn-danger',
            message: _t('Are you sure you want to delete the selected records?')
        });
        
        // 归档确认模板
        this.dialogTemplates.set('archive', {
            title: _t('Archive Records'),
            icon: 'fa-archive text-warning',
            confirmText: _t('Archive'),
            confirmClass: 'btn-warning',
            message: _t('Are you sure you want to archive the selected records?')
        });
        
        // 批量编辑模板
        this.dialogTemplates.set('bulk_edit', {
            title: _t('Bulk Edit'),
            icon: 'fa-edit text-primary',
            confirmText: _t('Apply'),
            confirmClass: 'btn-primary',
            message: _t('This will update all selected records. Continue?')
        });
    }
    
    // 设置键盘快捷键
    setupKeyboardShortcuts() {
        this.shortcuts = {
            'Ctrl+Enter': 'confirm',
            'Escape': 'cancel',
            'Alt+Y': 'confirm',
            'Alt+N': 'cancel'
        };
    }
    
    // 设置分析功能
    setupAnalytics() {
        this.analyticsConfig = {
            trackResponseTime: true,
            trackUserBehavior: true,
            trackErrorRate: true,
            reportInterval: 300000 // 5分钟
        };
    }
    
    // 显示确认对话框
    showConfirmationDialog(options) {
        const dialogProps = {
            ...options,
            close: () => this.closeDialog(options.id),
            confirm: () => this.confirmDialog(options.id, options.confirm),
            cancel: () => this.cancelDialog(options.id, options.cancel)
        };
        
        const dialogId = this.generateDialogId();
        this.dialogQueue.push({
            id: dialogId,
            props: dialogProps,
            component: this.EnhancedConfirmationDialog
        });
        
        this.processDialogQueue();
        
        return dialogId;
    }
    
    // 处理对话框队列
    processDialogQueue() {
        if (this.dialogQueue.length > this.dialogConfig.maxDialogs) {
            // 移除最旧的对话框
            this.dialogQueue.shift();
        }
        
        // 显示队列中的对话框
        for (const dialog of this.dialogQueue) {
            this.env.services.dialog.add(dialog.component, dialog.props);
        }
    }
    
    // 关闭对话框
    closeDialog(dialogId) {
        const index = this.dialogQueue.findIndex(d => d.id === dialogId);
        if (index > -1) {
            this.dialogQueue.splice(index, 1);
        }
    }
    
    // 确认对话框
    confirmDialog(dialogId, confirmCallback) {
        if (confirmCallback) {
            confirmCallback();
        }
        this.closeDialog(dialogId);
    }
    
    // 取消对话框
    cancelDialog(dialogId, cancelCallback) {
        if (cancelCallback) {
            cancelCallback();
        }
        this.closeDialog(dialogId);
    }
    
    // 生成对话框ID
    generateDialogId() {
        return `dialog_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    // 添加模板
    addTemplate(key, template) {
        this.dialogTemplates.set(key, template);
    }
    
    // 移除模板
    removeTemplate(key) {
        this.dialogTemplates.delete(key);
    }
    
    // 获取统计信息
    getStatistics() {
        return {
            ...this.dialogStatistics,
            queueLength: this.dialogQueue.length,
            templateCount: this.dialogTemplates.size,
            confirmationRate: this.dialogStatistics.totalShown > 0 
                ? (this.dialogStatistics.totalConfirmed / this.dialogStatistics.totalShown * 100).toFixed(2) + '%'
                : '0%'
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理队列
        this.dialogQueue = [];
        
        // 清理模板
        this.dialogTemplates.clear();
        
        // 重置统计
        this.dialogStatistics = {
            totalShown: 0,
            totalConfirmed: 0,
            totalCancelled: 0,
            totalTimedOut: 0,
            averageResponseTime: 0
        };
    }
}

// 使用示例
const dialogManager = new ConfirmationDialogManager();

// 显示删除确认对话框
const dialogId = dialogManager.showConfirmationDialog({
    templateKey: 'delete',
    nbRecords: 5,
    nbValidRecords: 5,
    isDomainSelected: false,
    confirm: () => {
        console.log('Records deleted');
    },
    cancel: () => {
        console.log('Delete cancelled');
    }
});

// 获取统计信息
const stats = dialogManager.getStatistics();
console.log('Dialog statistics:', stats);
```

## 技术特点

### 1. 简洁设计
- **轻量级**: 仅57行代码的轻量级实现
- **专注功能**: 专注于确认对话框的核心功能
- **易于维护**: 简洁的代码结构易于维护
- **高效执行**: 高效的执行性能

### 2. 灵活配置
- **属性验证**: 完善的属性类型验证
- **可选回调**: 灵活的回调函数配置
- **默认值**: 合理的默认属性值
- **扩展性**: 良好的扩展性设计

### 3. 用户体验
- **自动聚焦**: 自动聚焦提升用户体验
- **键盘支持**: 支持键盘操作
- **本地化**: 完整的本地化支持
- **可访问性**: 良好的可访问性

### 4. 数据支持
- **记录统计**: 支持记录数量统计
- **域选择**: 支持域选择状态
- **字段显示**: 支持字段的显示
- **数据验证**: 支持数据有效性验证

## 设计模式

### 1. 组件模式 (Component Pattern)
- **封装性**: 封装确认对话框功能
- **可复用**: 高度可复用的组件
- **接口标准**: 标准化的组件接口

### 2. 回调模式 (Callback Pattern)
- **事件处理**: 通过回调处理用户操作
- **异步支持**: 支持异步操作
- **灵活响应**: 灵活的响应机制

### 3. 模板模式 (Template Pattern)
- **UI模板**: 定义对话框的UI结构
- **内容填充**: 动态填充对话框内容
- **样式控制**: 控制对话框样式

### 4. 验证模式 (Validation Pattern)
- **属性验证**: 验证组件属性
- **数据验证**: 验证操作数据
- **状态验证**: 验证组件状态

## 注意事项

1. **用户体验**: 确保对话框的响应性和直观性
2. **数据一致性**: 确保显示数据的准确性
3. **错误处理**: 完善的错误处理机制
4. **性能考虑**: 避免不必要的重渲染

## 扩展建议

1. **更多模板**: 添加更多的确认对话框模板
2. **动画效果**: 添加对话框的动画效果
3. **自定义验证**: 支持自定义验证逻辑
4. **批量操作**: 支持批量确认操作
5. **使用统计**: 添加使用统计和分析功能

该列表确认对话框组件为Odoo Web客户端提供了简洁而有效的用户确认功能，通过灵活的配置和良好的用户体验确保了安全可靠的操作确认机制。

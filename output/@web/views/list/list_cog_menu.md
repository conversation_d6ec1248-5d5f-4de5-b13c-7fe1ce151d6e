# ListCogMenu - 列表齿轮菜单

## 概述

`list_cog_menu.js` 是 Odoo Web 客户端列表视图的齿轮菜单组件，提供了列表视图特定的菜单功能。该模块包含19行代码，是一个OWL组件，继承自通用的CogMenu组件，专门用于在列表视图中显示上下文相关的操作菜单，具备选择状态感知、动态菜单项、插槽支持等特性，是列表视图系统中用户操作菜单的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/list/list_cog_menu.js`
- **行数**: 19
- **模块**: `@web/views/list/list_cog_menu`

## 依赖关系

```javascript
// 核心依赖
'@web/search/cog_menu/cog_menu'    // 通用齿轮菜单
```

## 主要组件定义

### 1. ListCogMenu - 列表齿轮菜单

```javascript
class ListCogMenu extends CogMenu {
    static template = "web.ListCogMenu";
    static props = {
        ...CogMenu.props,
        hasSelectedRecords: { type: Number, optional: true },
        slots: { type: Object, optional: true },
    };

    _registryItems() {
        return this.props.hasSelectedRecords ? [] : super._registryItems();
    }
}
```

**组件特性**:
- **继承设计**: 继承自通用CogMenu组件
- **专用模板**: 使用ListCogMenu专用模板
- **选择感知**: 感知记录选择状态
- **插槽支持**: 支持自定义插槽内容

## 核心功能

### 1. 属性扩展

```javascript
static props = {
    ...CogMenu.props,                                    // 继承父类属性
    hasSelectedRecords: { type: Number, optional: true }, // 选中记录数量
    slots: { type: Object, optional: true },             // 自定义插槽
};
```

**属性功能**:
- **父类继承**: 继承CogMenu的所有属性
- **选择状态**: 跟踪选中记录的数量
- **插槽扩展**: 支持自定义内容插槽
- **可选配置**: 所有新增属性都是可选的

### 2. 注册表项过滤

```javascript
_registryItems() {
    return this.props.hasSelectedRecords ? [] : super._registryItems();
}
```

**过滤功能**:
- **条件过滤**: 根据选择状态过滤菜单项
- **选择时隐藏**: 有选中记录时隐藏默认菜单项
- **无选择时显示**: 无选中记录时显示所有菜单项
- **动态切换**: 动态切换菜单内容

## 使用场景

### 1. 列表菜单管理器

```javascript
// 列表菜单管理器
class ListMenuManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置菜单配置
        this.menuConfig = {
            enableDynamicItems: true,
            enableSelectionAware: true,
            enableCustomSlots: true,
            enableContextMenu: true,
            maxMenuItems: 10,
            groupSimilarItems: true
        };
        
        // 设置菜单项注册表
        this.menuRegistry = new Map();
        
        // 设置菜单状态
        this.menuState = {
            selectedRecords: 0,
            activeFilters: [],
            currentView: 'list',
            userPermissions: []
        };
        
        // 设置菜单统计
        this.menuStatistics = {
            totalClicks: 0,
            popularItems: new Map(),
            sessionClicks: 0,
            lastUsed: null
        };
        
        this.initializeMenuSystem();
    }
    
    // 初始化菜单系统
    initializeMenuSystem() {
        // 创建增强的列表菜单
        this.createEnhancedListMenu();
        
        // 注册默认菜单项
        this.registerDefaultMenuItems();
        
        // 设置上下文菜单
        this.setupContextMenu();
        
        // 设置菜单分析
        this.setupMenuAnalytics();
    }
    
    // 创建增强的列表菜单
    createEnhancedListMenu() {
        const originalMenu = ListCogMenu;
        
        this.EnhancedListCogMenu = class extends originalMenu {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加分析功能
                this.addAnalyticsSupport();
                
                // 添加自定义菜单项
                this.addCustomMenuItems();
                
                // 添加快捷键支持
                this.addKeyboardShortcuts();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = useState({
                    isOpen: false,
                    hoveredItem: null,
                    recentItems: [],
                    favoriteItems: [],
                    customItems: []
                });
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 智能菜单项过滤
                this.getSmartMenuItems = () => {
                    const baseItems = this._registryItems();
                    const customItems = this.getCustomMenuItems();
                    const contextItems = this.getContextMenuItems();
                    
                    let allItems = [...baseItems, ...customItems, ...contextItems];
                    
                    // 根据权限过滤
                    allItems = this.filterByPermissions(allItems);
                    
                    // 根据上下文过滤
                    allItems = this.filterByContext(allItems);
                    
                    // 排序和分组
                    allItems = this.sortAndGroupItems(allItems);
                    
                    return allItems;
                };
                
                // 获取自定义菜单项
                this.getCustomMenuItems = () => {
                    const customItems = [];
                    
                    // 添加选择相关的菜单项
                    if (this.props.hasSelectedRecords > 0) {
                        customItems.push({
                            key: 'bulk_edit',
                            description: _t('Bulk Edit'),
                            callback: () => this.onBulkEdit(),
                            icon: 'fa-edit',
                            sequence: 10
                        });
                        
                        customItems.push({
                            key: 'bulk_delete',
                            description: _t('Delete Selected'),
                            callback: () => this.onBulkDelete(),
                            icon: 'fa-trash',
                            sequence: 20,
                            className: 'text-danger'
                        });
                        
                        customItems.push({
                            key: 'bulk_export',
                            description: _t('Export Selected'),
                            callback: () => this.onBulkExport(),
                            icon: 'fa-download',
                            sequence: 30
                        });
                    }
                    
                    // 添加视图相关的菜单项
                    if (this.props.hasSelectedRecords === 0) {
                        customItems.push({
                            key: 'export_all',
                            description: _t('Export All'),
                            callback: () => this.onExportAll(),
                            icon: 'fa-download',
                            sequence: 40
                        });
                        
                        customItems.push({
                            key: 'import_data',
                            description: _t('Import'),
                            callback: () => this.onImport(),
                            icon: 'fa-upload',
                            sequence: 50
                        });
                        
                        customItems.push({
                            key: 'customize_view',
                            description: _t('Customize'),
                            callback: () => this.onCustomizeView(),
                            icon: 'fa-cog',
                            sequence: 60
                        });
                    }
                    
                    return customItems;
                };
                
                // 获取上下文菜单项
                this.getContextMenuItems = () => {
                    const contextItems = [];
                    
                    // 根据当前模型添加特定菜单项
                    const modelName = this.props.resModel;
                    const modelItems = this.menuRegistry.get(modelName) || [];
                    
                    for (const item of modelItems) {
                        if (this.shouldShowItem(item)) {
                            contextItems.push(item);
                        }
                    }
                    
                    return contextItems;
                };
                
                // 根据权限过滤
                this.filterByPermissions = (items) => {
                    return items.filter(item => {
                        if (!item.requiredPermission) return true;
                        return this.menuState.userPermissions.includes(item.requiredPermission);
                    });
                };
                
                // 根据上下文过滤
                this.filterByContext = (items) => {
                    return items.filter(item => {
                        if (!item.contextCondition) return true;
                        return item.contextCondition(this.props, this.menuState);
                    });
                };
                
                // 排序和分组
                this.sortAndGroupItems = (items) => {
                    // 按序列号排序
                    items.sort((a, b) => (a.sequence || 100) - (b.sequence || 100));
                    
                    // 分组处理
                    if (this.menuConfig.groupSimilarItems) {
                        return this.groupMenuItems(items);
                    }
                    
                    return items;
                };
                
                // 分组菜单项
                this.groupMenuItems = (items) => {
                    const groups = new Map();
                    
                    for (const item of items) {
                        const group = item.group || 'default';
                        if (!groups.has(group)) {
                            groups.set(group, []);
                        }
                        groups.get(group).push(item);
                    }
                    
                    const groupedItems = [];
                    for (const [groupName, groupItems] of groups) {
                        if (groupedItems.length > 0) {
                            groupedItems.push({ type: 'separator' });
                        }
                        groupedItems.push(...groupItems);
                    }
                    
                    return groupedItems;
                };
                
                // 判断是否显示菜单项
                this.shouldShowItem = (item) => {
                    // 检查选择状态
                    if (item.requiresSelection && this.props.hasSelectedRecords === 0) {
                        return false;
                    }
                    
                    if (item.requiresNoSelection && this.props.hasSelectedRecords > 0) {
                        return false;
                    }
                    
                    // 检查其他条件
                    if (item.condition && !item.condition(this.props, this.menuState)) {
                        return false;
                    }
                    
                    return true;
                };
            }
            
            addAnalyticsSupport() {
                // 跟踪菜单项点击
                this.trackMenuClick = (itemKey) => {
                    this.menuStatistics.totalClicks++;
                    this.menuStatistics.sessionClicks++;
                    this.menuStatistics.lastUsed = Date.now();
                    
                    // 更新流行度
                    const currentCount = this.menuStatistics.popularItems.get(itemKey) || 0;
                    this.menuStatistics.popularItems.set(itemKey, currentCount + 1);
                    
                    // 添加到最近使用
                    this.enhancedState.recentItems.unshift(itemKey);
                    if (this.enhancedState.recentItems.length > 5) {
                        this.enhancedState.recentItems.pop();
                    }
                };
                
                // 获取使用统计
                this.getUsageStatistics = () => {
                    const popularItems = Array.from(this.menuStatistics.popularItems.entries())
                        .sort((a, b) => b[1] - a[1])
                        .slice(0, 5);
                    
                    return {
                        totalClicks: this.menuStatistics.totalClicks,
                        sessionClicks: this.menuStatistics.sessionClicks,
                        popularItems: popularItems,
                        recentItems: this.enhancedState.recentItems,
                        lastUsed: this.menuStatistics.lastUsed
                    };
                };
            }
            
            addCustomMenuItems() {
                // 添加收藏菜单项
                this.addToFavorites = (itemKey) => {
                    if (!this.enhancedState.favoriteItems.includes(itemKey)) {
                        this.enhancedState.favoriteItems.push(itemKey);
                        this.saveFavorites();
                    }
                };
                
                // 从收藏中移除
                this.removeFromFavorites = (itemKey) => {
                    const index = this.enhancedState.favoriteItems.indexOf(itemKey);
                    if (index > -1) {
                        this.enhancedState.favoriteItems.splice(index, 1);
                        this.saveFavorites();
                    }
                };
                
                // 保存收藏
                this.saveFavorites = () => {
                    localStorage.setItem('list_menu_favorites', JSON.stringify(this.enhancedState.favoriteItems));
                };
                
                // 加载收藏
                this.loadFavorites = () => {
                    try {
                        const saved = localStorage.getItem('list_menu_favorites');
                        if (saved) {
                            this.enhancedState.favoriteItems = JSON.parse(saved);
                        }
                    } catch (error) {
                        console.warn('Failed to load menu favorites:', error);
                    }
                };
            }
            
            addKeyboardShortcuts() {
                // 设置快捷键
                this.shortcuts = {
                    'Ctrl+A': () => this.onSelectAll(),
                    'Ctrl+E': () => this.onBulkEdit(),
                    'Delete': () => this.onBulkDelete(),
                    'Ctrl+Shift+E': () => this.onExportAll(),
                    'Ctrl+I': () => this.onImport()
                };
                
                // 监听键盘事件
                useExternalListener(document, 'keydown', (event) => {
                    const key = this.getShortcutKey(event);
                    const handler = this.shortcuts[key];
                    
                    if (handler && this.enhancedState.isOpen) {
                        event.preventDefault();
                        handler();
                    }
                });
            }
            
            // 获取快捷键字符串
            getShortcutKey(event) {
                const parts = [];
                
                if (event.ctrlKey) parts.push('Ctrl');
                if (event.shiftKey) parts.push('Shift');
                if (event.altKey) parts.push('Alt');
                
                parts.push(event.key);
                
                return parts.join('+');
            }
            
            // 重写注册表项方法
            _registryItems() {
                if (this.menuConfig.enableDynamicItems) {
                    return this.getSmartMenuItems();
                } else {
                    return super._registryItems();
                }
            }
            
            // 菜单项点击处理
            onMenuItemClick(item) {
                this.trackMenuClick(item.key);
                
                if (item.callback) {
                    item.callback();
                }
                
                this.enhancedState.isOpen = false;
            }
            
            // 批量编辑
            onBulkEdit() {
                this.env.services.action.doAction({
                    type: 'ir.actions.act_window',
                    name: _t('Bulk Edit'),
                    res_model: this.props.resModel,
                    view_mode: 'form',
                    views: [[false, 'form']],
                    target: 'new',
                    context: {
                        active_ids: this.getSelectedRecordIds(),
                        active_model: this.props.resModel
                    }
                });
            }
            
            // 批量删除
            onBulkDelete() {
                this.env.services.dialog.add(ConfirmationDialog, {
                    body: _t('Are you sure you want to delete the selected records?'),
                    confirm: () => {
                        this.deleteSelectedRecords();
                    }
                });
            }
            
            // 批量导出
            onBulkExport() {
                this.env.services.action.doAction({
                    type: 'ir.actions.act_window',
                    name: _t('Export Data'),
                    res_model: 'base.export',
                    view_mode: 'form',
                    views: [[false, 'form']],
                    target: 'new',
                    context: {
                        active_ids: this.getSelectedRecordIds(),
                        active_model: this.props.resModel
                    }
                });
            }
            
            // 导出全部
            onExportAll() {
                this.env.services.action.doAction({
                    type: 'ir.actions.act_window',
                    name: _t('Export All Data'),
                    res_model: 'base.export',
                    view_mode: 'form',
                    views: [[false, 'form']],
                    target: 'new',
                    context: {
                        active_model: this.props.resModel
                    }
                });
            }
            
            // 导入数据
            onImport() {
                this.env.services.action.doAction({
                    type: 'ir.actions.act_window',
                    name: _t('Import Data'),
                    res_model: 'base.import',
                    view_mode: 'form',
                    views: [[false, 'form']],
                    target: 'new',
                    context: {
                        active_model: this.props.resModel
                    }
                });
            }
            
            // 自定义视图
            onCustomizeView() {
                this.env.services.action.doAction({
                    type: 'ir.actions.act_window',
                    name: _t('Customize View'),
                    res_model: 'ir.ui.view',
                    view_mode: 'form',
                    views: [[false, 'form']],
                    target: 'new'
                });
            }
            
            // 全选
            onSelectAll() {
                this.trigger('select-all');
            }
            
            // 获取选中记录ID
            getSelectedRecordIds() {
                // 这里需要从父组件获取选中的记录ID
                return this.props.selectedRecordIds || [];
            }
            
            // 删除选中记录
            deleteSelectedRecords() {
                const recordIds = this.getSelectedRecordIds();
                if (recordIds.length > 0) {
                    this.env.services.orm.unlink(this.props.resModel, recordIds);
                }
            }
            
            // 获取菜单统计
            getMenuStatistics() {
                return this.getUsageStatistics();
            }
        };
    }
    
    // 注册默认菜单项
    registerDefaultMenuItems() {
        // 通用菜单项
        this.registerMenuItem('default', {
            key: 'refresh',
            description: _t('Refresh'),
            callback: () => this.onRefresh(),
            icon: 'fa-refresh',
            sequence: 1000,
            group: 'view'
        });
        
        this.registerMenuItem('default', {
            key: 'archive',
            description: _t('Archive'),
            callback: () => this.onArchive(),
            icon: 'fa-archive',
            sequence: 2000,
            group: 'action',
            requiresSelection: true
        });
    }
    
    // 注册菜单项
    registerMenuItem(modelName, item) {
        if (!this.menuRegistry.has(modelName)) {
            this.menuRegistry.set(modelName, []);
        }
        this.menuRegistry.get(modelName).push(item);
    }
    
    // 移除菜单项
    removeMenuItem(modelName, itemKey) {
        const items = this.menuRegistry.get(modelName);
        if (items) {
            const index = items.findIndex(item => item.key === itemKey);
            if (index > -1) {
                items.splice(index, 1);
            }
        }
    }
    
    // 设置上下文菜单
    setupContextMenu() {
        this.contextMenuConfig = {
            enabled: this.menuConfig.enableContextMenu,
            trigger: 'right-click',
            items: []
        };
    }
    
    // 设置菜单分析
    setupMenuAnalytics() {
        this.analyticsConfig = {
            enabled: true,
            trackClicks: true,
            trackHovers: true,
            trackTiming: true
        };
    }
    
    // 获取菜单统计
    getMenuStatistics() {
        return {
            ...this.menuStatistics,
            registrySize: this.menuRegistry.size,
            totalMenuItems: Array.from(this.menuRegistry.values()).reduce((sum, items) => sum + items.length, 0)
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理注册表
        this.menuRegistry.clear();
        
        // 重置统计
        this.menuStatistics = {
            totalClicks: 0,
            popularItems: new Map(),
            sessionClicks: 0,
            lastUsed: null
        };
    }
}

// 使用示例
const menuManager = new ListMenuManager();

// 创建增强的列表菜单
const EnhancedListCogMenu = menuManager.EnhancedListCogMenu;

// 注册自定义菜单项
menuManager.registerMenuItem('sale.order', {
    key: 'confirm_orders',
    description: _t('Confirm Orders'),
    callback: () => console.log('Confirming orders...'),
    icon: 'fa-check',
    sequence: 100,
    requiresSelection: true
});

// 获取统计信息
const stats = menuManager.getMenuStatistics();
console.log('Menu statistics:', stats);
```

## 技术特点

### 1. 继承设计
- **代码复用**: 继承CogMenu的所有功能
- **专门定制**: 针对列表视图的特殊需求
- **最小实现**: 只实现必要的差异化功能
- **向后兼容**: 保持与父类的兼容性

### 2. 状态感知
- **选择感知**: 感知记录选择状态
- **动态菜单**: 根据状态动态显示菜单项
- **上下文相关**: 提供上下文相关的操作
- **智能过滤**: 智能过滤不相关的菜单项

### 3. 简洁实现
- **轻量级**: 仅19行代码的轻量级实现
- **专注功能**: 专注于核心功能实现
- **易于维护**: 简洁的代码易于维护
- **高效执行**: 高效的执行性能

### 4. 扩展性
- **插槽支持**: 支持自定义插槽内容
- **属性扩展**: 支持属性的扩展
- **功能增强**: 支持功能的增强和定制
- **模板定制**: 支持模板的定制

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **功能继承**: 继承父类的所有功能
- **行为重写**: 重写特定的行为方法
- **接口保持**: 保持一致的接口

### 2. 策略模式 (Strategy Pattern)
- **菜单策略**: 不同状态下的菜单显示策略
- **过滤策略**: 不同的菜单项过滤策略
- **显示策略**: 不同的菜单显示策略

### 3. 状态模式 (State Pattern)
- **状态感知**: 感知组件的不同状态
- **行为变化**: 根据状态改变行为
- **动态切换**: 动态切换菜单内容

### 4. 模板模式 (Template Pattern)
- **模板定制**: 定制专门的模板
- **结构保持**: 保持基本的结构
- **内容变化**: 改变具体的内容

## 注意事项

1. **状态同步**: 确保选择状态的正确同步
2. **性能考虑**: 避免频繁的菜单重新计算
3. **用户体验**: 确保菜单的响应性和直观性
4. **权限检查**: 确保菜单项的权限检查

## 扩展建议

1. **更多菜单项**: 添加更多的列表特定菜单项
2. **快捷键支持**: 添加键盘快捷键支持
3. **自定义菜单**: 支持用户自定义菜单项
4. **菜单分组**: 支持菜单项的分组显示
5. **使用统计**: 添加菜单使用统计功能

该列表齿轮菜单组件为Odoo Web客户端提供了简洁而强大的列表视图菜单功能，通过状态感知和动态菜单确保了用户友好的操作体验。

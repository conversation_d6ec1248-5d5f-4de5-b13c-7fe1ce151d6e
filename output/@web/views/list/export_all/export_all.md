# ExportAll - 导出全部功能

## 概述

`export_all.js` 是 Odoo Web 客户端列表视图的导出全部功能组件，提供了导出特定模型所有记录的功能。该模块包含47行代码，是一个OWL组件，专门用于在列表视图的齿轮菜单中添加"导出全部"选项，具备权限检查、条件显示、直接导出、注册表集成等特性，是列表视图系统中数据导出的核心组件。

## 文件信息
- **路径**: `/web/static/src/views/list/export_all/export_all.js`
- **行数**: 47
- **模块**: `@web/views/list/export_all/export_all`

## 依赖关系

```javascript
// 核心依赖
'@web/core/dropdown/dropdown_item'      // 下拉菜单项组件
'@web/core/registry'                    // 注册表服务
'@web/core/user'                        // 用户服务
'@web/core/utils/strings'               // 字符串工具
'@web/search/action_menus/action_menus' // 动作菜单
'@odoo/owl'                             // OWL框架
```

## 主要组件定义

### 1. ExportAll - 导出全部组件

```javascript
class ExportAll extends Component {
    static template = "web.ExportAll";
    static components = { DropdownItem };
    static props = {};

    async onDirectExportData() {
        this.env.searchModel.trigger("direct-export-data");
    }
}
```

**组件特性**:
- **专用模板**: 使用ExportAll专用模板
- **下拉菜单项**: 集成DropdownItem组件
- **无属性**: 不需要外部属性配置
- **事件触发**: 通过搜索模型触发导出事件

## 核心功能

### 1. 直接导出数据

```javascript
async onDirectExportData() {
    this.env.searchModel.trigger("direct-export-data");
}
```

**导出功能**:
- **事件触发**: 触发搜索模型的导出事件
- **异步处理**: 支持异步导出操作
- **直接导出**: 直接导出所有数据
- **模型集成**: 与搜索模型紧密集成

### 2. 菜单项配置

```javascript
const exportAllItem = {
    Component: ExportAll,
    groupNumber: STATIC_ACTIONS_GROUP_NUMBER,
    isDisplayed: async (env) =>
        env.config.viewType === "list" &&
        !env.model.root.selection.length &&
        (await user.hasGroup("base.group_allow_export")) &&
        exprToBoolean(env.config.viewArch.getAttribute("export_xlsx"), true),
};
```

**配置功能**:
- **组件指定**: 指定ExportAll组件
- **分组编号**: 使用静态动作分组编号
- **显示条件**: 复杂的显示条件判断
- **权限检查**: 检查用户导出权限

### 3. 显示条件检查

```javascript
isDisplayed: async (env) =>
    env.config.viewType === "list" &&           // 必须是列表视图
    !env.model.root.selection.length &&        // 没有选中记录
    (await user.hasGroup("base.group_allow_export")) && // 有导出权限
    exprToBoolean(env.config.viewArch.getAttribute("export_xlsx"), true), // 允许导出
```

**条件检查功能**:
- **视图类型**: 仅在列表视图中显示
- **选择状态**: 仅在无选中记录时显示
- **权限验证**: 验证用户是否有导出权限
- **配置检查**: 检查视图配置是否允许导出

### 4. 注册表集成

```javascript
cogMenuRegistry.add("export-all-menu", exportAllItem, { sequence: 10 });
```

**注册功能**:
- **菜单注册**: 注册到齿轮菜单注册表
- **唯一标识**: 使用"export-all-menu"作为标识
- **序列号**: 设置序列号控制显示顺序
- **自动集成**: 自动集成到齿轮菜单系统

## 使用场景

### 1. 导出全部管理器

```javascript
// 导出全部管理器
class ExportAllManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置导出配置
        this.exportConfig = {
            enableBatchExport: true,
            enableProgressTracking: true,
            enableFormatSelection: true,
            enableFieldSelection: true,
            maxRecordsPerBatch: 10000,
            supportedFormats: ['xlsx', 'csv', 'pdf'],
            defaultFormat: 'xlsx'
        };
        
        // 设置导出队列
        this.exportQueue = [];
        
        // 设置导出历史
        this.exportHistory = [];
        
        // 设置导出统计
        this.exportStatistics = {
            totalExports: 0,
            successfulExports: 0,
            failedExports: 0,
            totalRecordsExported: 0,
            averageExportTime: 0
        };
        
        this.initializeExportSystem();
    }
    
    // 初始化导出系统
    initializeExportSystem() {
        // 创建增强的导出组件
        this.createEnhancedExportAll();
        
        // 设置导出格式
        this.setupExportFormats();
        
        // 设置进度跟踪
        this.setupProgressTracking();
        
        // 设置错误处理
        this.setupErrorHandling();
    }
    
    // 创建增强的导出组件
    createEnhancedExportAll() {
        const originalExportAll = ExportAll;
        
        this.EnhancedExportAll = class extends originalExportAll {
            setup() {
                super.setup();
                
                // 添加增强功能
                this.addEnhancedFeatures();
                
                // 添加进度跟踪
                this.addProgressTracking();
                
                // 添加格式选择
                this.addFormatSelection();
                
                // 添加字段选择
                this.addFieldSelection();
            }
            
            addEnhancedFeatures() {
                // 扩展状态
                this.enhancedState = useState({
                    isExporting: false,
                    exportProgress: 0,
                    selectedFormat: this.exportConfig.defaultFormat,
                    selectedFields: [],
                    exportId: null,
                    startTime: null
                });
                
                // 添加增强方法
                this.addEnhancedMethods();
            }
            
            addEnhancedMethods() {
                // 智能导出
                this.smartExport = async () => {
                    if (this.enhancedState.isExporting) return;
                    
                    try {
                        // 开始导出
                        this.startExport();
                        
                        // 获取导出数据
                        const exportData = await this.prepareExportData();
                        
                        // 执行导出
                        const result = await this.performExport(exportData);
                        
                        // 完成导出
                        this.completeExport(result);
                        
                    } catch (error) {
                        this.handleExportError(error);
                    }
                };
                
                // 开始导出
                this.startExport = () => {
                    this.enhancedState.isExporting = true;
                    this.enhancedState.exportProgress = 0;
                    this.enhancedState.startTime = Date.now();
                    this.enhancedState.exportId = this.generateExportId();
                    
                    // 记录统计
                    this.exportStatistics.totalExports++;
                    
                    // 显示进度
                    this.showExportProgress();
                };
                
                // 准备导出数据
                this.prepareExportData = async () => {
                    const { searchModel } = this.env;
                    
                    // 获取搜索域
                    const domain = searchModel.domain;
                    
                    // 获取字段列表
                    const fields = this.getExportFields();
                    
                    // 获取记录数量
                    const recordCount = await this.getRecordCount(domain);
                    
                    return {
                        domain: domain,
                        fields: fields,
                        recordCount: recordCount,
                        format: this.enhancedState.selectedFormat,
                        model: this.env.config.resModel
                    };
                };
                
                // 执行导出
                this.performExport = async (exportData) => {
                    const { recordCount, format } = exportData;
                    
                    if (recordCount > this.exportConfig.maxRecordsPerBatch) {
                        // 批量导出
                        return await this.performBatchExport(exportData);
                    } else {
                        // 直接导出
                        return await this.performDirectExport(exportData);
                    }
                };
                
                // 批量导出
                this.performBatchExport = async (exportData) => {
                    const { recordCount } = exportData;
                    const batchSize = this.exportConfig.maxRecordsPerBatch;
                    const totalBatches = Math.ceil(recordCount / batchSize);
                    
                    const results = [];
                    
                    for (let i = 0; i < totalBatches; i++) {
                        const offset = i * batchSize;
                        const limit = Math.min(batchSize, recordCount - offset);
                        
                        // 导出批次
                        const batchResult = await this.exportBatch(exportData, offset, limit);
                        results.push(batchResult);
                        
                        // 更新进度
                        this.enhancedState.exportProgress = ((i + 1) / totalBatches) * 100;
                    }
                    
                    // 合并结果
                    return this.mergeExportResults(results);
                };
                
                // 直接导出
                this.performDirectExport = async (exportData) => {
                    // 触发原始导出事件
                    this.env.searchModel.trigger("direct-export-data", {
                        format: exportData.format,
                        fields: exportData.fields,
                        domain: exportData.domain
                    });
                    
                    return {
                        success: true,
                        recordCount: exportData.recordCount,
                        format: exportData.format
                    };
                };
                
                // 导出批次
                this.exportBatch = async (exportData, offset, limit) => {
                    const batchData = {
                        ...exportData,
                        offset: offset,
                        limit: limit
                    };
                    
                    return await this.callExportService(batchData);
                };
                
                // 调用导出服务
                this.callExportService = async (data) => {
                    const response = await this.env.services.rpc({
                        route: '/web/export/xlsx',
                        params: {
                            model: data.model,
                            fields: data.fields,
                            domain: data.domain,
                            offset: data.offset || 0,
                            limit: data.limit || null,
                            format: data.format
                        }
                    });
                    
                    return response;
                };
                
                // 完成导出
                this.completeExport = (result) => {
                    this.enhancedState.isExporting = false;
                    this.enhancedState.exportProgress = 100;
                    
                    const exportTime = Date.now() - this.enhancedState.startTime;
                    
                    // 记录统计
                    this.exportStatistics.successfulExports++;
                    this.exportStatistics.totalRecordsExported += result.recordCount;
                    this.updateAverageExportTime(exportTime);
                    
                    // 记录历史
                    this.recordExportHistory(result, exportTime);
                    
                    // 显示成功消息
                    this.showExportSuccess(result);
                    
                    // 隐藏进度
                    this.hideExportProgress();
                };
                
                // 处理导出错误
                this.handleExportError = (error) => {
                    this.enhancedState.isExporting = false;
                    
                    // 记录统计
                    this.exportStatistics.failedExports++;
                    
                    // 显示错误消息
                    this.showExportError(error);
                    
                    // 隐藏进度
                    this.hideExportProgress();
                    
                    console.error('Export error:', error);
                };
                
                // 获取导出字段
                this.getExportFields = () => {
                    if (this.enhancedState.selectedFields.length > 0) {
                        return this.enhancedState.selectedFields;
                    }
                    
                    // 使用默认字段
                    return this.getDefaultExportFields();
                };
                
                // 获取默认导出字段
                this.getDefaultExportFields = () => {
                    const { archInfo } = this.env.config;
                    const fields = [];
                    
                    for (const column of archInfo.columns) {
                        if (column.type === 'field' && !column.optional) {
                            fields.push({
                                name: column.name,
                                label: column.label || column.name
                            });
                        }
                    }
                    
                    return fields;
                };
                
                // 获取记录数量
                this.getRecordCount = async (domain) => {
                    const result = await this.env.services.orm.searchCount(
                        this.env.config.resModel,
                        domain
                    );
                    
                    return result;
                };
                
                // 显示导出进度
                this.showExportProgress = () => {
                    this.env.services.notification.add(
                        _t("Export started..."),
                        { type: "info" }
                    );
                };
                
                // 隐藏导出进度
                this.hideExportProgress = () => {
                    // 进度条隐藏逻辑
                };
                
                // 显示导出成功
                this.showExportSuccess = (result) => {
                    this.env.services.notification.add(
                        _t("Export completed successfully. %s records exported.", result.recordCount),
                        { type: "success" }
                    );
                };
                
                // 显示导出错误
                this.showExportError = (error) => {
                    this.env.services.notification.add(
                        _t("Export failed: %s", error.message),
                        { type: "danger" }
                    );
                };
                
                // 更新平均导出时间
                this.updateAverageExportTime = (exportTime) => {
                    const totalExports = this.exportStatistics.successfulExports;
                    this.exportStatistics.averageExportTime = 
                        (this.exportStatistics.averageExportTime * (totalExports - 1) + exportTime) / totalExports;
                };
                
                // 记录导出历史
                this.recordExportHistory = (result, exportTime) => {
                    this.exportHistory.push({
                        id: this.enhancedState.exportId,
                        timestamp: Date.now(),
                        recordCount: result.recordCount,
                        format: result.format,
                        exportTime: exportTime,
                        success: true
                    });
                    
                    // 限制历史记录数量
                    if (this.exportHistory.length > 100) {
                        this.exportHistory.shift();
                    }
                };
                
                // 生成导出ID
                this.generateExportId = () => {
                    return `export_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                };
            }
            
            addProgressTracking() {
                // 进度跟踪相关方法
            }
            
            addFormatSelection() {
                // 格式选择相关方法
            }
            
            addFieldSelection() {
                // 字段选择相关方法
            }
            
            // 重写原始导出方法
            async onDirectExportData() {
                await this.smartExport();
            }
            
            // 获取导出统计
            getExportStatistics() {
                return {
                    ...this.exportStatistics,
                    currentExport: {
                        isExporting: this.enhancedState.isExporting,
                        progress: this.enhancedState.exportProgress,
                        exportId: this.enhancedState.exportId
                    },
                    historyCount: this.exportHistory.length
                };
            }
        };
    }
    
    // 设置导出格式
    setupExportFormats() {
        this.exportFormats = new Map([
            ['xlsx', {
                name: 'Excel',
                extension: '.xlsx',
                mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                maxRecords: 1000000
            }],
            ['csv', {
                name: 'CSV',
                extension: '.csv',
                mimeType: 'text/csv',
                maxRecords: null
            }],
            ['pdf', {
                name: 'PDF',
                extension: '.pdf',
                mimeType: 'application/pdf',
                maxRecords: 10000
            }]
        ]);
    }
    
    // 设置进度跟踪
    setupProgressTracking() {
        this.progressConfig = {
            enableRealTimeProgress: true,
            updateInterval: 1000,
            showEstimatedTime: true,
            showRecordCount: true
        };
    }
    
    // 设置错误处理
    setupErrorHandling() {
        this.errorHandlers = new Map([
            ['network_error', (error) => this.handleNetworkError(error)],
            ['permission_error', (error) => this.handlePermissionError(error)],
            ['data_error', (error) => this.handleDataError(error)],
            ['timeout_error', (error) => this.handleTimeoutError(error)]
        ]);
    }
    
    // 处理网络错误
    handleNetworkError(error) {
        console.error('Network error during export:', error);
    }
    
    // 处理权限错误
    handlePermissionError(error) {
        console.error('Permission error during export:', error);
    }
    
    // 处理数据错误
    handleDataError(error) {
        console.error('Data error during export:', error);
    }
    
    // 处理超时错误
    handleTimeoutError(error) {
        console.error('Timeout error during export:', error);
    }
    
    // 获取导出统计
    getExportStatistics() {
        return {
            ...this.exportStatistics,
            queueLength: this.exportQueue.length,
            historyLength: this.exportHistory.length,
            supportedFormats: Array.from(this.exportFormats.keys()),
            successRate: this.exportStatistics.totalExports > 0 
                ? (this.exportStatistics.successfulExports / this.exportStatistics.totalExports * 100).toFixed(2) + '%'
                : '0%'
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理队列
        this.exportQueue = [];
        
        // 清理历史
        this.exportHistory = [];
        
        // 重置统计
        this.exportStatistics = {
            totalExports: 0,
            successfulExports: 0,
            failedExports: 0,
            totalRecordsExported: 0,
            averageExportTime: 0
        };
    }
}

// 使用示例
const exportManager = new ExportAllManager();

// 创建增强的导出组件
const EnhancedExportAll = exportManager.EnhancedExportAll;

// 获取统计信息
const stats = exportManager.getExportStatistics();
console.log('Export statistics:', stats);
```

## 技术特点

### 1. 简洁实现
- **轻量级**: 仅47行代码的轻量级实现
- **专注功能**: 专注于导出全部的核心功能
- **易于维护**: 简洁的代码结构
- **高效执行**: 高效的执行性能

### 2. 条件显示
- **智能判断**: 智能的显示条件判断
- **权限检查**: 完善的权限验证
- **状态感知**: 感知选择状态
- **配置检查**: 检查视图配置

### 3. 注册表集成
- **自动注册**: 自动注册到齿轮菜单
- **序列控制**: 控制菜单项显示顺序
- **标准集成**: 符合Odoo标准的集成方式
- **动态加载**: 支持动态加载和卸载

### 4. 事件驱动
- **事件触发**: 通过事件触发导出操作
- **模型集成**: 与搜索模型紧密集成
- **异步支持**: 支持异步操作
- **错误处理**: 完善的错误处理机制

## 设计模式

### 1. 组件模式 (Component Pattern)
- **封装性**: 封装导出全部功能
- **可复用**: 高度可复用的组件
- **接口标准**: 标准化的组件接口

### 2. 注册表模式 (Registry Pattern)
- **动态注册**: 动态注册菜单项
- **插件化**: 支持插件化扩展
- **配置驱动**: 配置驱动的注册

### 3. 事件模式 (Event Pattern)
- **事件触发**: 通过事件触发操作
- **解耦合**: 组件间的解耦合
- **异步处理**: 支持异步事件处理

### 4. 策略模式 (Strategy Pattern)
- **显示策略**: 不同条件下的显示策略
- **导出策略**: 不同的导出策略
- **权限策略**: 不同的权限检查策略

## 注意事项

1. **权限检查**: 确保用户有导出权限
2. **性能考虑**: 避免导出过大的数据集
3. **错误处理**: 完善的错误处理和用户提示
4. **用户体验**: 提供清晰的导出状态反馈

## 扩展建议

1. **格式支持**: 支持更多的导出格式
2. **进度显示**: 添加导出进度显示
3. **字段选择**: 支持用户选择导出字段
4. **批量导出**: 支持大数据量的批量导出
5. **导出历史**: 添加导出历史记录功能

该导出全部组件为Odoo Web客户端提供了简洁而强大的数据导出功能，通过智能的条件判断和权限检查确保了安全可靠的数据导出体验。

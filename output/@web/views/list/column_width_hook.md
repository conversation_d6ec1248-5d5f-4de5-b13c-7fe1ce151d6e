# ColumnWidthHook - 列宽钩子

## 概述

`column_width_hook.js` 是 Odoo Web 客户端列表视图的列宽管理钩子，负责优化列表视图中列的宽度分配和管理。该模块包含436行代码，提供了一个专门的钩子函数，用于计算和管理列表视图中各列的最优宽度，具备智能宽度计算、固定宽度支持、响应式调整、冻结机制等特性，是列表视图系统中列宽优化的核心工具。

## 文件信息
- **路径**: `/web/static/src/views/list/column_width_hook.js`
- **行数**: 436
- **模块**: `@web/views/list/column_width_hook`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/timing'    // 时间工具
'@odoo/owl'                 // OWL框架
```

## 核心概念

### 1. 宽度计算规范

**固定宽度字段**:
- 某些字段类型有硬编码的列宽（如日期字段）
- 架构中可以指定固定宽度（`width="60px"`）
- 列宽 = 指定宽度 + 单元格左右内边距

**数值字段范围**:
- 数值字段有宽度范围而非固定宽度
- 最小宽度：能显示"1 million"
- 最大宽度：能显示"1 billion"
- 根据字段类型（integer、float、monetary）确定

**其他列**:
- 有硬编码的最小宽度保证
- 没有最大宽度限制

### 2. 计算场景

**无数据场景**:
- 固定宽度列强制使用指定宽度
- 剩余空间在其他列中均匀分配

**有数据场景**:
- 让浏览器基于表格内容计算理想宽度
- 确保每列符合最小和最大宽度要求
- 调整过窄或过宽的列
- 确保列宽总和填满100%的表格宽度

### 3. 冻结逻辑

**冻结机制**:
- 计算出最优宽度后冻结表格
- 防止用户交互时列宽变化
- 存储计算的宽度并在每次渲染时重新应用

**例外情况**:
- 列配置发生变化时重新计算
- 表格容器大小变化时重新计算

## 主要钩子函数

### 1. useMagicColumnWidths - 魔法列宽钩子

```javascript
function useMagicColumnWidths(tableRef) {
    const component = useComponent();
    
    // 状态管理
    const state = {
        columnWidths: new Map(),
        isComputed: false,
        tableWidth: 0,
        lastComputeTime: 0,
    };
    
    // 防抖的重新计算函数
    const debouncedRecompute = useDebounced(recomputeWidths, 100);
    
    // 监听窗口大小变化
    useExternalListener(window, "resize", debouncedRecompute);
    
    // 组件挂载后计算宽度
    useEffect(() => {
        if (tableRef.el) {
            computeColumnWidths();
        }
    });
    
    return {
        columnWidths: state.columnWidths,
        recompute: recomputeWidths,
        isComputed: state.isComputed,
    };
}
```

**钩子功能**:
- **宽度计算**: 智能计算列的最优宽度
- **状态管理**: 管理列宽相关状态
- **响应式**: 响应窗口大小变化
- **防抖处理**: 防抖的重新计算机制

## 核心功能

### 1. 宽度计算算法

```javascript
function computeColumnWidths() {
    const table = tableRef.el;
    if (!table) return;
    
    const columns = getTableColumns(table);
    const hasData = table.querySelector('tbody tr:not(.o_data_row)') !== null;
    
    if (hasData) {
        computeWidthsWithData(table, columns);
    } else {
        computeWidthsWithoutData(table, columns);
    }
    
    applyComputedWidths(table, columns);
    state.isComputed = true;
}

function computeWidthsWithData(table, columns) {
    // 1. 让浏览器计算自然宽度
    resetTableWidths(table);
    const naturalWidths = measureColumnWidths(table);
    
    // 2. 应用最小/最大宽度约束
    const constrainedWidths = applyWidthConstraints(naturalWidths, columns);
    
    // 3. 调整总宽度以填满100%
    const finalWidths = adjustTotalWidth(constrainedWidths, table.clientWidth);
    
    // 4. 存储计算结果
    storeColumnWidths(finalWidths, columns);
}

function computeWidthsWithoutData(table, columns) {
    const tableWidth = table.clientWidth;
    let remainingWidth = tableWidth;
    const computedWidths = new Map();
    
    // 1. 分配固定宽度列
    for (const column of columns) {
        if (column.hasFixedWidth) {
            const width = getFixedWidth(column);
            computedWidths.set(column.name, width);
            remainingWidth -= width;
        }
    }
    
    // 2. 在剩余列中均匀分配剩余宽度
    const flexColumns = columns.filter(col => !col.hasFixedWidth);
    const flexWidth = Math.max(MIN_COLUMN_WIDTH, remainingWidth / flexColumns.length);
    
    for (const column of flexColumns) {
        computedWidths.set(column.name, flexWidth);
    }
    
    storeColumnWidths(computedWidths, columns);
}
```

**计算算法功能**:
- **自然宽度**: 让浏览器计算基于内容的自然宽度
- **约束应用**: 应用最小和最大宽度约束
- **总宽度调整**: 确保总宽度填满表格
- **结果存储**: 存储计算的宽度结果

### 2. 宽度约束处理

```javascript
function applyWidthConstraints(widths, columns) {
    const constrainedWidths = new Map();
    
    for (const column of columns) {
        const naturalWidth = widths.get(column.name);
        const minWidth = getMinWidth(column);
        const maxWidth = getMaxWidth(column);
        
        let constrainedWidth = naturalWidth;
        
        // 应用最小宽度约束
        if (constrainedWidth < minWidth) {
            constrainedWidth = minWidth;
        }
        
        // 应用最大宽度约束
        if (maxWidth && constrainedWidth > maxWidth) {
            constrainedWidth = maxWidth;
        }
        
        constrainedWidths.set(column.name, constrainedWidth);
    }
    
    return constrainedWidths;
}

function getMinWidth(column) {
    const { type, fieldType } = column;
    
    // 字段类型的最小宽度
    const MIN_WIDTHS = {
        boolean: 60,
        integer: 80,
        float: 100,
        monetary: 120,
        date: 100,
        datetime: 140,
        char: 100,
        text: 150,
        selection: 100,
        many2one: 150,
        many2many: 150,
        one2many: 150,
    };
    
    return MIN_WIDTHS[fieldType] || MIN_WIDTHS.char;
}

function getMaxWidth(column) {
    const { fieldType } = column;
    
    // 某些字段类型有最大宽度限制
    const MAX_WIDTHS = {
        integer: 150,
        float: 180,
        monetary: 200,
        boolean: 60,
        date: 100,
        datetime: 140,
    };
    
    return MAX_WIDTHS[fieldType] || null;
}
```

**约束处理功能**:
- **最小宽度**: 确保列有足够的最小宽度
- **最大宽度**: 限制某些列的最大宽度
- **类型特定**: 根据字段类型应用不同约束
- **灵活配置**: 支持自定义宽度约束

### 3. 总宽度调整

```javascript
function adjustTotalWidth(widths, targetWidth) {
    const currentTotal = Array.from(widths.values()).reduce((sum, width) => sum + width, 0);
    const difference = targetWidth - currentTotal;
    
    if (Math.abs(difference) < 1) {
        return widths; // 差异很小，不需要调整
    }
    
    const adjustedWidths = new Map(widths);
    
    if (difference > 0) {
        // 需要扩展列宽
        expandColumns(adjustedWidths, difference);
    } else {
        // 需要收缩列宽
        shrinkColumns(adjustedWidths, Math.abs(difference));
    }
    
    return adjustedWidths;
}

function expandColumns(widths, extraWidth) {
    // 找到可以扩展的列（没有最大宽度限制的列）
    const expandableColumns = [];
    for (const [columnName, width] of widths) {
        const column = getColumnByName(columnName);
        const maxWidth = getMaxWidth(column);
        
        if (!maxWidth || width < maxWidth) {
            expandableColumns.push({
                name: columnName,
                currentWidth: width,
                maxWidth: maxWidth,
                expandable: maxWidth ? maxWidth - width : Infinity,
            });
        }
    }
    
    if (expandableColumns.length === 0) return;
    
    // 按可扩展空间排序，优先扩展可扩展空间大的列
    expandableColumns.sort((a, b) => b.expandable - a.expandable);
    
    let remainingWidth = extraWidth;
    const widthPerColumn = remainingWidth / expandableColumns.length;
    
    for (const column of expandableColumns) {
        const expansion = Math.min(widthPerColumn, column.expandable, remainingWidth);
        widths.set(column.name, column.currentWidth + expansion);
        remainingWidth -= expansion;
        
        if (remainingWidth <= 0) break;
    }
}

function shrinkColumns(widths, excessWidth) {
    // 找到可以收缩的列
    const shrinkableColumns = [];
    for (const [columnName, width] of widths) {
        const column = getColumnByName(columnName);
        const minWidth = getMinWidth(column);
        
        if (width > minWidth) {
            shrinkableColumns.push({
                name: columnName,
                currentWidth: width,
                minWidth: minWidth,
                shrinkable: width - minWidth,
            });
        }
    }
    
    if (shrinkableColumns.length === 0) return;
    
    // 按可收缩空间排序，优先收缩可收缩空间大的列
    shrinkableColumns.sort((a, b) => b.shrinkable - a.shrinkable);
    
    let remainingWidth = excessWidth;
    const widthPerColumn = remainingWidth / shrinkableColumns.length;
    
    for (const column of shrinkableColumns) {
        const shrinkage = Math.min(widthPerColumn, column.shrinkable, remainingWidth);
        widths.set(column.name, column.currentWidth - shrinkage);
        remainingWidth -= shrinkage;
        
        if (remainingWidth <= 0) break;
    }
}
```

**总宽度调整功能**:
- **差异计算**: 计算当前总宽度与目标宽度的差异
- **扩展策略**: 智能扩展列宽以填满空间
- **收缩策略**: 智能收缩列宽以适应空间
- **优先级**: 根据可调整空间确定调整优先级

### 4. 宽度应用和冻结

```javascript
function applyComputedWidths(table, columns) {
    const thead = table.querySelector('thead');
    const tbody = table.querySelector('tbody');
    
    // 应用到表头
    if (thead) {
        applyWidthsToRow(thead.querySelector('tr'), columns);
    }
    
    // 应用到表体
    if (tbody) {
        const rows = tbody.querySelectorAll('tr');
        for (const row of rows) {
            applyWidthsToRow(row, columns);
        }
    }
    
    // 冻结表格布局
    table.style.tableLayout = 'fixed';
}

function applyWidthsToRow(row, columns) {
    const cells = row.querySelectorAll('th, td');
    
    for (let i = 0; i < cells.length && i < columns.length; i++) {
        const cell = cells[i];
        const column = columns[i];
        const width = state.columnWidths.get(column.name);
        
        if (width) {
            cell.style.width = `${width}px`;
            cell.style.minWidth = `${width}px`;
            cell.style.maxWidth = `${width}px`;
        }
    }
}

function freezeTable(table) {
    // 设置表格布局为固定
    table.style.tableLayout = 'fixed';
    
    // 防止列宽变化
    table.style.width = '100%';
    
    // 标记为已冻结
    table.dataset.frozen = 'true';
}
```

**应用和冻结功能**:
- **宽度应用**: 将计算的宽度应用到表格单元格
- **表格冻结**: 冻结表格布局防止宽度变化
- **样式设置**: 设置CSS样式确保宽度固定
- **状态标记**: 标记表格的冻结状态

## 使用场景

### 1. 列宽管理器

```javascript
// 列宽管理器
class ColumnWidthManager {
    constructor() {
        this.setupManager();
    }
    
    setupManager() {
        // 设置列宽配置
        this.widthConfig = {
            enableAutoResize: true,
            enableResponsive: true,
            enableCaching: true,
            enableAnimation: false,
            minColumnWidth: 60,
            maxTableWidth: null,
            resizeThreshold: 10
        };
        
        // 设置列宽缓存
        this.widthCache = new Map();
        
        // 设置列宽规则
        this.widthRules = new Map();
        
        // 设置列宽统计
        this.widthStatistics = {
            totalCalculations: 0,
            cacheHits: 0,
            cacheMisses: 0,
            averageCalculationTime: 0
        };
        
        this.initializeWidthSystem();
    }
    
    // 初始化宽度系统
    initializeWidthSystem() {
        // 创建增强的列宽钩子
        this.createEnhancedWidthHook();
        
        // 设置默认规则
        this.setupDefaultRules();
        
        // 设置响应式支持
        this.setupResponsiveSupport();
        
        // 设置性能监控
        this.setupPerformanceMonitoring();
    }
    
    // 创建增强的列宽钩子
    createEnhancedWidthHook() {
        const originalHook = useMagicColumnWidths;
        
        this.enhancedUseMagicColumnWidths = (tableRef, options = {}) => {
            const config = {
                ...this.widthConfig,
                ...options
            };
            
            // 调用原始钩子
            const originalResult = originalHook(tableRef);
            
            // 添加增强功能
            const enhancedResult = {
                ...originalResult,
                
                // 智能重新计算
                smartRecompute: () => {
                    const startTime = performance.now();
                    
                    // 检查是否需要重新计算
                    if (this.shouldRecompute(tableRef)) {
                        originalResult.recompute();
                        
                        const endTime = performance.now();
                        this.updateStatistics(endTime - startTime);
                    }
                },
                
                // 获取列宽信息
                getColumnInfo: (columnName) => {
                    return {
                        width: originalResult.columnWidths.get(columnName),
                        minWidth: this.getMinWidth(columnName),
                        maxWidth: this.getMaxWidth(columnName),
                        isFixed: this.isFixedWidth(columnName)
                    };
                },
                
                // 设置列宽
                setColumnWidth: (columnName, width) => {
                    if (this.validateWidth(columnName, width)) {
                        originalResult.columnWidths.set(columnName, width);
                        this.applyColumnWidth(tableRef, columnName, width);
                    }
                },
                
                // 重置列宽
                resetColumnWidths: () => {
                    originalResult.columnWidths.clear();
                    originalResult.recompute();
                },
                
                // 获取统计信息
                getStatistics: () => {
                    return {
                        ...this.widthStatistics,
                        totalColumns: originalResult.columnWidths.size,
                        isComputed: originalResult.isComputed
                    };
                }
            };
            
            return enhancedResult;
        };
    }
    
    // 设置默认规则
    setupDefaultRules() {
        // 字段类型规则
        this.widthRules.set('boolean', {
            minWidth: 60,
            maxWidth: 60,
            fixed: true
        });
        
        this.widthRules.set('integer', {
            minWidth: 80,
            maxWidth: 150,
            fixed: false
        });
        
        this.widthRules.set('float', {
            minWidth: 100,
            maxWidth: 180,
            fixed: false
        });
        
        this.widthRules.set('monetary', {
            minWidth: 120,
            maxWidth: 200,
            fixed: false
        });
        
        this.widthRules.set('date', {
            minWidth: 100,
            maxWidth: 100,
            fixed: true
        });
        
        this.widthRules.set('datetime', {
            minWidth: 140,
            maxWidth: 140,
            fixed: true
        });
        
        this.widthRules.set('char', {
            minWidth: 100,
            maxWidth: null,
            fixed: false
        });
        
        this.widthRules.set('text', {
            minWidth: 150,
            maxWidth: null,
            fixed: false
        });
        
        this.widthRules.set('selection', {
            minWidth: 100,
            maxWidth: null,
            fixed: false
        });
        
        this.widthRules.set('many2one', {
            minWidth: 150,
            maxWidth: null,
            fixed: false
        });
        
        this.widthRules.set('many2many', {
            minWidth: 150,
            maxWidth: null,
            fixed: false
        });
        
        this.widthRules.set('one2many', {
            minWidth: 150,
            maxWidth: null,
            fixed: false
        });
    }
    
    // 设置响应式支持
    setupResponsiveSupport() {
        this.breakpoints = {
            mobile: 768,
            tablet: 1024,
            desktop: 1200
        };
        
        this.responsiveRules = {
            mobile: {
                hideOptionalColumns: true,
                minColumnWidth: 80,
                maxVisibleColumns: 3
            },
            tablet: {
                hideOptionalColumns: false,
                minColumnWidth: 100,
                maxVisibleColumns: 6
            },
            desktop: {
                hideOptionalColumns: false,
                minColumnWidth: 120,
                maxVisibleColumns: null
            }
        };
    }
    
    // 设置性能监控
    setupPerformanceMonitoring() {
        this.performanceConfig = {
            enableMonitoring: true,
            sampleRate: 0.1,
            thresholds: {
                calculationTime: 50, // 50ms
                columnCount: 20
            }
        };
    }
    
    // 判断是否需要重新计算
    shouldRecompute(tableRef) {
        if (!tableRef.el) return false;
        
        const currentWidth = tableRef.el.clientWidth;
        const lastWidth = this.widthCache.get('tableWidth');
        
        if (!lastWidth) {
            this.widthCache.set('tableWidth', currentWidth);
            return true;
        }
        
        const widthDifference = Math.abs(currentWidth - lastWidth);
        if (widthDifference > this.widthConfig.resizeThreshold) {
            this.widthCache.set('tableWidth', currentWidth);
            return true;
        }
        
        return false;
    }
    
    // 获取最小宽度
    getMinWidth(columnName) {
        const rule = this.widthRules.get(columnName);
        return rule ? rule.minWidth : this.widthConfig.minColumnWidth;
    }
    
    // 获取最大宽度
    getMaxWidth(columnName) {
        const rule = this.widthRules.get(columnName);
        return rule ? rule.maxWidth : null;
    }
    
    // 判断是否固定宽度
    isFixedWidth(columnName) {
        const rule = this.widthRules.get(columnName);
        return rule ? rule.fixed : false;
    }
    
    // 验证宽度
    validateWidth(columnName, width) {
        const minWidth = this.getMinWidth(columnName);
        const maxWidth = this.getMaxWidth(columnName);
        
        if (width < minWidth) return false;
        if (maxWidth && width > maxWidth) return false;
        
        return true;
    }
    
    // 应用列宽
    applyColumnWidth(tableRef, columnName, width) {
        if (!tableRef.el) return;
        
        const table = tableRef.el;
        const cells = table.querySelectorAll(`[data-name="${columnName}"]`);
        
        for (const cell of cells) {
            cell.style.width = `${width}px`;
            cell.style.minWidth = `${width}px`;
            cell.style.maxWidth = `${width}px`;
        }
    }
    
    // 更新统计
    updateStatistics(calculationTime) {
        this.widthStatistics.totalCalculations++;
        this.widthStatistics.averageCalculationTime = 
            (this.widthStatistics.averageCalculationTime * (this.widthStatistics.totalCalculations - 1) + calculationTime) / 
            this.widthStatistics.totalCalculations;
    }
    
    // 添加宽度规则
    addWidthRule(fieldType, rule) {
        this.widthRules.set(fieldType, rule);
    }
    
    // 移除宽度规则
    removeWidthRule(fieldType) {
        this.widthRules.delete(fieldType);
    }
    
    // 获取宽度统计
    getWidthStatistics() {
        return {
            ...this.widthStatistics,
            cacheSize: this.widthCache.size,
            rulesCount: this.widthRules.size,
            cacheHitRate: this.widthStatistics.totalCalculations > 0 
                ? (this.widthStatistics.cacheHits / this.widthStatistics.totalCalculations * 100).toFixed(2) + '%'
                : '0%'
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理缓存
        this.widthCache.clear();
        
        // 清理规则
        this.widthRules.clear();
        
        // 重置统计
        this.widthStatistics = {
            totalCalculations: 0,
            cacheHits: 0,
            cacheMisses: 0,
            averageCalculationTime: 0
        };
    }
}

// 使用示例
const widthManager = new ColumnWidthManager();

// 创建增强的列宽钩子
const enhancedWidthHook = widthManager.enhancedUseMagicColumnWidths;

// 在组件中使用
function MyListComponent() {
    const tableRef = useRef('table');
    const columnWidths = enhancedWidthHook(tableRef, {
        enableAutoResize: true,
        minColumnWidth: 80
    });
    
    // 获取统计信息
    const stats = columnWidths.getStatistics();
    console.log('Column width statistics:', stats);
    
    return columnWidths;
}
```

## 技术特点

### 1. 智能计算
- **自适应算法**: 根据内容和约束智能计算宽度
- **多场景支持**: 支持有数据和无数据两种场景
- **约束处理**: 完善的最小/最大宽度约束处理
- **总宽度保证**: 确保列宽总和填满表格宽度

### 2. 性能优化
- **防抖处理**: 防抖的重新计算避免频繁计算
- **缓存机制**: 缓存计算结果避免重复计算
- **增量更新**: 只在必要时重新计算
- **高效算法**: 优化的宽度分配算法

### 3. 响应式支持
- **窗口监听**: 监听窗口大小变化
- **自动调整**: 自动调整列宽适应新尺寸
- **断点支持**: 支持不同设备断点
- **灵活配置**: 灵活的响应式配置

### 4. 冻结机制
- **布局固定**: 固定表格布局防止闪烁
- **宽度锁定**: 锁定计算的列宽
- **状态保持**: 保持冻结状态
- **异常处理**: 处理冻结异常情况

## 设计模式

### 1. 钩子模式 (Hook Pattern)
- **逻辑封装**: 封装列宽管理逻辑
- **状态管理**: 管理列宽相关状态
- **副作用处理**: 处理DOM操作副作用

### 2. 策略模式 (Strategy Pattern)
- **计算策略**: 不同场景的宽度计算策略
- **调整策略**: 不同的宽度调整策略
- **约束策略**: 不同字段类型的约束策略

### 3. 观察者模式 (Observer Pattern)
- **事件监听**: 监听窗口大小变化
- **自动响应**: 自动响应尺寸变化
- **状态通知**: 通知宽度变化

### 4. 缓存模式 (Cache Pattern)
- **结果缓存**: 缓存计算结果
- **性能优化**: 避免重复计算
- **智能失效**: 智能的缓存失效机制

## 注意事项

1. **性能考虑**: 避免频繁的宽度重新计算
2. **浏览器兼容**: 确保跨浏览器的表格布局兼容性
3. **内容变化**: 处理表格内容变化对宽度的影响
4. **边界情况**: 处理极端宽度情况

## 扩展建议

1. **用户自定义**: 支持用户手动调整列宽
2. **持久化**: 保存用户的列宽偏好
3. **动画支持**: 添加宽度变化的动画效果
4. **更多约束**: 支持更复杂的宽度约束规则
5. **调试工具**: 提供列宽调试和分析工具

该列宽钩子为Odoo Web客户端提供了智能的列表视图列宽管理功能，通过先进的计算算法、性能优化和响应式支持确保了最佳的表格显示效果和用户体验。

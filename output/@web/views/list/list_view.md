# Odoo 列表视图 (List View) 学习资料

## 文件概述

**文件路径**: `output/@web/views/list/list_view.js`  
**原始路径**: `/web/static/src/views/list/list_view.js`  
**模块类型**: 核心视图模块 - 列表视图定义  
**代码行数**: 39 行  
**依赖关系**: 
- `@web/core/registry` - 注册表系统
- `@web/model/relational_model/relational_model` - 关系模型
- `@web/views/list/list_arch_parser` - 列表架构解析器
- `@web/views/list/list_controller` - 列表控制器
- `@web/views/list/list_renderer` - 列表渲染器

## 模块功能

列表视图模块是 Odoo Web 客户端的核心视图类型之一。该模块提供了：
- 列表视图的完整定义和配置
- 数据表格展示功能
- 排序和筛选功能
- 批量操作和选择
- 分页和导航
- 内联编辑功能

列表视图是数据浏览和管理的主要界面，提供了高效的数据查看和操作体验。

## 列表视图架构

### 核心组件结构
```
List View
├── ListArchParser - 架构解析器
│   ├── 解析列表XML架构
│   ├── 提取列定义
│   ├── 处理排序配置
│   └── 解析操作按钮
├── ListController - 控制器
│   ├── 数据加载和刷新
│   ├── 排序和筛选
│   ├── 批量操作处理
│   └── 分页控制
├── ListRenderer - 渲染器
│   ├── 表格结构渲染
│   ├── 列头和数据行
│   ├── 选择框和操作
│   └── 分页组件
└── RelationalModel - 数据模型
    ├── 数据查询和缓存
    ├── 排序和筛选
    ├── 分页数据管理
    └── 批量操作支持
```

### 视图定义结构
```javascript
const listView = {
    type: "list",
    
    // 核心组件
    Controller: ListController,
    Renderer: ListRenderer,
    ArchParser: ListArchParser,
    Model: RelationalModel,
    
    // 配置选项
    buttonTemplate: "web.ListView.Buttons",
    canOrderByCount: true,
    limit: 80,
    
    // 属性处理函数
    props: (genericProps, view) => {
        // 处理和扩展属性
        return processedProps;
    }
};
```

## 核心组件详解

### 1. 列表视图定义
```javascript
const listView = {
    type: "list",
    
    Controller: ListController,
    Renderer: ListRenderer,
    ArchParser: ListArchParser,
    Model: RelationalModel,
    
    buttonTemplate: "web.ListView.Buttons",
    canOrderByCount: true,
    limit: 80,
    
    props: (genericProps, view) => {
        const { ArchParser } = view;
        const { arch, relatedModels, resModel } = genericProps;
        const archInfo = new ArchParser().parse(arch, relatedModels, resModel);

        return {
            ...genericProps,
            Model: view.Model,
            Renderer: view.Renderer,
            buttonTemplate: view.buttonTemplate,
            archInfo,
        };
    },
};
```

**功能特性**:
- **类型标识**: 明确标识为"list"视图类型
- **组件集成**: 集成所有必要的列表组件
- **架构解析**: 自动解析列表XML架构
- **分页配置**: 默认每页80条记录
- **排序支持**: 支持按计数字段排序

### 2. 属性处理函数 (props)
```javascript
props: (genericProps, view) => {
    const { ArchParser } = view;
    const { arch, relatedModels, resModel } = genericProps;
    const archInfo = new ArchParser().parse(arch, relatedModels, resModel);

    return {
        ...genericProps,
        Model: view.Model,
        Renderer: view.Renderer,
        buttonTemplate: view.buttonTemplate,
        archInfo,
    };
}
```

**处理流程**:
1. **架构解析**: 使用ArchParser解析XML架构
2. **信息提取**: 提取架构信息和列定义
3. **属性合并**: 合并通用属性和列表特定属性
4. **组件注入**: 注入必要的组件引用
5. **模板配置**: 配置按钮模板

## 使用示例

### 1. 基本列表视图
```javascript
// 列表视图XML架构示例
const listArch = `
    <tree string="客户列表" 
          editable="bottom" 
          create="true" 
          delete="true"
          export_xlsx="true">
        
        <field name="name" string="客户名称"/>
        <field name="email" string="邮箱" widget="email"/>
        <field name="phone" string="电话" widget="phone"/>
        <field name="city" string="城市"/>
        <field name="country_id" string="国家"/>
        <field name="customer_rank" string="客户等级" widget="badge"/>
        <field name="total_invoiced" string="总开票金额" widget="monetary" sum="总计"/>
        <field name="payment_term_id" string="付款条件"/>
        <field name="user_id" string="销售员" widget="many2one_avatar_user"/>
        <field name="create_date" string="创建日期" widget="date"/>
        <field name="active" string="激活" widget="boolean_toggle"/>
        
        <!-- 按钮操作 -->
        <button name="action_send_email" 
                type="object" 
                string="发送邮件" 
                icon="fa-envelope"
                groups="sales_team.group_sale_salesman"/>
        
        <button name="action_create_opportunity" 
                type="object" 
                string="创建商机" 
                icon="fa-plus"
                context="{'default_partner_id': active_id}"/>
    </tree>
`;

// 使用列表视图
class CustomerListView extends Component {
    static template = xml`
        <div class="customer-list">
            <div class="list-controls">
                <div class="list-filters">
                    <select t-model="selectedFilter" t-on-change="applyFilter">
                        <option value="">所有客户</option>
                        <option value="active">活跃客户</option>
                        <option value="inactive">非活跃客户</option>
                        <option value="vip">VIP客户</option>
                    </select>
                    
                    <input type="text" 
                           t-model="searchText" 
                           t-on-input="onSearch"
                           placeholder="搜索客户..."
                           class="form-control"/>
                </div>
                
                <div class="list-actions">
                    <button t-on-click="createCustomer" class="btn btn-primary">
                        <i class="fa fa-plus" /> 新建客户
                    </button>
                    
                    <button t-on-click="exportData" class="btn btn-secondary">
                        <i class="fa fa-download" /> 导出
                    </button>
                    
                    <button t-on-click="importData" class="btn btn-info">
                        <i class="fa fa-upload" /> 导入
                    </button>
                </div>
            </div>
            
            <ListView resModel="'res.partner'"
                      arch="listArch"
                      context="currentContext"
                      domain="currentDomain"
                      fields="fields"
                      limit="limit"
                      orderBy="orderBy"
                      className="listClassName"
                      onRecordClick="onCustomerClick"
                      onSelectionChange="onSelectionChange"
                      onSort="onSort" />
        </div>
    `;
    
    setup() {
        this.selectedFilter = useState('');
        this.searchText = useState('');
        this.selectedRecords = useState([]);
        this.sortField = useState('name');
        this.sortOrder = useState('asc');
        
        this.listArch = listArch;
        this.limit = 80;
        this.fields = this.getCustomerFields();
        
        onWillStart(() => {
            this.updateView();
        });
    }
    
    get currentContext() {
        const context = {
            default_is_company: false,
            default_customer_rank: 1
        };
        
        if (this.selectedFilter === 'vip') {
            context.search_default_vip = 1;
        }
        
        return context;
    }
    
    get currentDomain() {
        let domain = [['customer_rank', '>', 0]];
        
        if (this.selectedFilter === 'active') {
            domain.push(['active', '=', true]);
        } else if (this.selectedFilter === 'inactive') {
            domain.push(['active', '=', false]);
        } else if (this.selectedFilter === 'vip') {
            domain.push(['category_id', 'ilike', 'VIP']);
        }
        
        if (this.searchText) {
            domain.push(['name', 'ilike', this.searchText]);
        }
        
        return domain;
    }
    
    get orderBy() {
        return [{
            name: this.sortField,
            asc: this.sortOrder === 'asc'
        }];
    }
    
    get listClassName() {
        const classes = ['customer-list-view'];
        
        if (this.selectedRecords.length > 0) {
            classes.push('has-selection');
        }
        
        return classes.join(' ');
    }
    
    getCustomerFields() {
        return {
            name: { type: 'char', string: '客户名称', required: true },
            email: { type: 'char', string: '邮箱' },
            phone: { type: 'char', string: '电话' },
            city: { type: 'char', string: '城市' },
            country_id: { type: 'many2one', relation: 'res.country', string: '国家' },
            customer_rank: { 
                type: 'integer', 
                string: '客户等级',
                selection: [[1, '普通'], [2, '重要'], [3, 'VIP']]
            },
            total_invoiced: { type: 'monetary', string: '总开票金额' },
            payment_term_id: { type: 'many2one', relation: 'account.payment.term', string: '付款条件' },
            user_id: { type: 'many2one', relation: 'res.users', string: '销售员' },
            create_date: { type: 'datetime', string: '创建日期' },
            active: { type: 'boolean', string: '激活' }
        };
    }
    
    applyFilter() {
        this.updateView();
    }
    
    onSearch() {
        // 防抖搜索
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.updateView();
        }, 300);
    }
    
    updateView() {
        // 触发视图更新
        this.render();
    }
    
    createCustomer() {
        this.env.services.action.doAction({
            type: 'ir.actions.act_window',
            res_model: 'res.partner',
            view_mode: 'form',
            views: [[false, 'form']],
            target: 'new',
            context: this.currentContext
        });
    }
    
    exportData() {
        this.env.services.action.doAction({
            type: 'ir.actions.act_url',
            url: '/web/export/xlsx',
            target: 'self'
        });
    }
    
    importData() {
        this.env.services.action.doAction({
            type: 'ir.actions.client',
            tag: 'import',
            params: {
                model: 'res.partner',
                context: this.currentContext
            }
        });
    }
    
    onCustomerClick(record) {
        this.env.services.action.doAction({
            type: 'ir.actions.act_window',
            res_model: 'res.partner',
            res_id: record.id,
            view_mode: 'form',
            views: [[false, 'form']],
            target: 'current'
        });
    }
    
    onSelectionChange(selectedRecords) {
        this.selectedRecords = selectedRecords;
    }
    
    onSort(fieldName, order) {
        this.sortField = fieldName;
        this.sortOrder = order;
        this.updateView();
    }
}
```

### 2. 高级列表视图配置
```javascript
class AdvancedListView extends Component {
    static template = xml`
        <div class="advanced-list">
            <div class="list-toolbar">
                <div class="toolbar-left">
                    <div class="view-switcher">
                        <button t-on-click="() => this.setViewMode('list')" 
                                t-att-class="viewMode === 'list' ? 'btn btn-primary' : 'btn btn-secondary'">
                            <i class="fa fa-list" /> 列表
                        </button>
                        <button t-on-click="() => this.setViewMode('kanban')" 
                                t-att-class="viewMode === 'kanban' ? 'btn btn-primary' : 'btn btn-secondary'">
                            <i class="fa fa-th-large" /> 看板
                        </button>
                    </div>
                    
                    <div class="bulk-actions" t-if="selectedRecords.length > 0">
                        <span class="selection-info">
                            已选择 <strong t-esc="selectedRecords.length" /> 条记录
                        </span>
                        
                        <button t-on-click="bulkEdit" class="btn btn-sm btn-info">
                            <i class="fa fa-edit" /> 批量编辑
                        </button>
                        
                        <button t-on-click="bulkDelete" class="btn btn-sm btn-danger">
                            <i class="fa fa-trash" /> 批量删除
                        </button>
                        
                        <button t-on-click="bulkExport" class="btn btn-sm btn-secondary">
                            <i class="fa fa-download" /> 导出选中
                        </button>
                    </div>
                </div>
                
                <div class="toolbar-right">
                    <div class="list-settings">
                        <button t-on-click="toggleGroupBy" class="btn btn-sm btn-outline-secondary">
                            <i class="fa fa-group" /> 分组
                        </button>
                        
                        <button t-on-click="toggleFilters" class="btn btn-sm btn-outline-secondary">
                            <i class="fa fa-filter" /> 筛选
                        </button>
                        
                        <button t-on-click="customizeColumns" class="btn btn-sm btn-outline-secondary">
                            <i class="fa fa-columns" /> 列设置
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="list-filters" t-if="showFilters">
                <div class="filter-group">
                    <label>状态筛选:</label>
                    <select t-model="statusFilter" t-on-change="applyFilters">
                        <option value="">全部</option>
                        <option value="active">激活</option>
                        <option value="inactive">未激活</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label>日期范围:</label>
                    <input type="date" t-model="dateFrom" t-on-change="applyFilters"/>
                    <span>至</span>
                    <input type="date" t-model="dateTo" t-on-change="applyFilters"/>
                </div>
                
                <div class="filter-group">
                    <label>销售员:</label>
                    <select t-model="userFilter" t-on-change="applyFilters">
                        <option value="">全部</option>
                        <option t-foreach="salesUsers" 
                                t-as="user" 
                                t-key="user.id"
                                t-att-value="user.id"
                                t-esc="user.name"/>
                    </select>
                </div>
            </div>
            
            <ListView resModel="resModel"
                      arch="currentArch"
                      context="currentContext"
                      domain="currentDomain"
                      fields="fields"
                      limit="currentLimit"
                      orderBy="orderBy"
                      groupBy="groupBy"
                      className="listClassName"
                      editable="editable"
                      multiEdit="multiEdit"
                      onRecordClick="onRecordClick"
                      onRecordEdit="onRecordEdit"
                      onSelectionChange="onSelectionChange"
                      onSort="onSort"
                      onGroupToggle="onGroupToggle" />
            
            <div class="list-summary" t-if="showSummary">
                <div class="summary-stats">
                    <div class="stat-item">
                        <span class="stat-label">总记录数:</span>
                        <span class="stat-value" t-esc="totalCount"/>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">当前页:</span>
                        <span class="stat-value" t-esc="currentPage"/>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">总页数:</span>
                        <span class="stat-value" t-esc="totalPages"/>
                    </div>
                </div>
                
                <div class="summary-aggregates" t-if="aggregates">
                    <div t-foreach="aggregates" 
                         t-as="aggregate" 
                         t-key="aggregate.field"
                         class="aggregate-item">
                        <span class="aggregate-label" t-esc="aggregate.label"/>:
                        <span class="aggregate-value" t-esc="aggregate.value"/>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setup() {
        this.viewMode = useState('list');
        this.selectedRecords = useState([]);
        this.showFilters = useState(false);
        this.showSummary = useState(true);
        
        // 筛选状态
        this.statusFilter = useState('');
        this.dateFrom = useState('');
        this.dateTo = useState('');
        this.userFilter = useState('');
        
        // 分组和排序
        this.groupBy = useState([]);
        this.orderBy = useState([]);
        
        // 列表配置
        this.editable = useState(false);
        this.multiEdit = useState(false);
        this.currentLimit = useState(80);
        
        // 统计数据
        this.totalCount = useState(0);
        this.currentPage = useState(1);
        this.totalPages = useState(1);
        this.aggregates = useState([]);
        
        this.salesUsers = useState([]);
        
        onWillStart(() => {
            this.loadSalesUsers();
            this.loadStatistics();
        });
    }
    
    get currentArch() {
        return this.generateDynamicArch();
    }
    
    get currentContext() {
        return {
            ...this.props.context,
            list_view_mode: this.viewMode
        };
    }
    
    get currentDomain() {
        let domain = [...(this.props.domain || [])];
        
        if (this.statusFilter === 'active') {
            domain.push(['active', '=', true]);
        } else if (this.statusFilter === 'inactive') {
            domain.push(['active', '=', false]);
        }
        
        if (this.dateFrom) {
            domain.push(['create_date', '>=', this.dateFrom]);
        }
        
        if (this.dateTo) {
            domain.push(['create_date', '<=', this.dateTo]);
        }
        
        if (this.userFilter) {
            domain.push(['user_id', '=', parseInt(this.userFilter)]);
        }
        
        return domain;
    }
    
    get listClassName() {
        const classes = ['advanced-list-view'];
        
        if (this.selectedRecords.length > 0) {
            classes.push('has-selection');
        }
        
        if (this.editable) {
            classes.push('editable');
        }
        
        if (this.groupBy.length > 0) {
            classes.push('grouped');
        }
        
        return classes.join(' ');
    }
    
    generateDynamicArch() {
        const columns = this.getVisibleColumns();
        
        return `
            <tree string="${this.getListTitle()}" 
                  editable="${this.editable ? 'bottom' : 'false'}"
                  multi_edit="${this.multiEdit}"
                  create="true" 
                  delete="true"
                  export_xlsx="true">
                
                ${columns.map(col => this.generateColumnElement(col)).join('\n')}
                
                ${this.generateActionButtons()}
            </tree>
        `;
    }
    
    getVisibleColumns() {
        // 获取可见列配置
        return [
            { name: 'name', string: '名称', type: 'char', required: true },
            { name: 'email', string: '邮箱', type: 'char', widget: 'email' },
            { name: 'phone', string: '电话', type: 'char', widget: 'phone' },
            { name: 'user_id', string: '销售员', type: 'many2one', widget: 'many2one_avatar_user' },
            { name: 'create_date', string: '创建日期', type: 'datetime', widget: 'date' },
            { name: 'active', string: '激活', type: 'boolean', widget: 'boolean_toggle' }
        ];
    }
    
    generateColumnElement(column) {
        const attrs = [`name="${column.name}"`];
        
        if (column.string) {
            attrs.push(`string="${column.string}"`);
        }
        
        if (column.widget) {
            attrs.push(`widget="${column.widget}"`);
        }
        
        if (column.sum) {
            attrs.push(`sum="${column.sum}"`);
        }
        
        if (column.avg) {
            attrs.push(`avg="${column.avg}"`);
        }
        
        return `<field ${attrs.join(' ')}/>`;
    }
    
    generateActionButtons() {
        return `
            <button name="action_custom_action" 
                    type="object" 
                    string="自定义操作" 
                    icon="fa-cog"/>
        `;
    }
    
    getListTitle() {
        return this.props.title || '记录列表';
    }
    
    setViewMode(mode) {
        this.viewMode = mode;
    }
    
    toggleGroupBy() {
        // 切换分组显示
        if (this.groupBy.length > 0) {
            this.groupBy = [];
        } else {
            this.groupBy = ['user_id'];
        }
    }
    
    toggleFilters() {
        this.showFilters = !this.showFilters;
    }
    
    customizeColumns() {
        // 打开列自定义对话框
        this.env.services.dialog.add(ColumnCustomizationDialog, {
            columns: this.getVisibleColumns(),
            onSave: this.onColumnsCustomized.bind(this)
        });
    }
    
    applyFilters() {
        // 应用筛选条件
        this.currentPage = 1;
        this.loadData();
    }
    
    async loadSalesUsers() {
        try {
            const users = await this.env.services.orm.searchRead(
                'res.users',
                [['groups_id', 'in', [/* sales group id */]]],
                ['id', 'name']
            );
            this.salesUsers = users;
        } catch (error) {
            console.error('加载销售员失败:', error);
        }
    }
    
    async loadStatistics() {
        try {
            // 加载统计数据
            this.totalCount = 1000; // 示例数据
            this.totalPages = Math.ceil(this.totalCount / this.currentLimit);
            
            this.aggregates = [
                { field: 'total_invoiced', label: '总开票金额', value: '¥1,234,567' },
                { field: 'avg_invoiced', label: '平均开票金额', value: '¥12,345' }
            ];
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }
    
    async loadData() {
        // 加载列表数据
        console.log('加载数据');
    }
    
    bulkEdit() {
        const recordIds = this.selectedRecords.map(r => r.id);
        
        this.env.services.action.doAction({
            type: 'ir.actions.act_window',
            res_model: this.props.resModel,
            view_mode: 'form',
            views: [[false, 'form']],
            target: 'new',
            context: {
                active_ids: recordIds,
                active_model: this.props.resModel
            }
        });
    }
    
    async bulkDelete() {
        const confirmed = confirm(`确定要删除选中的 ${this.selectedRecords.length} 条记录吗？`);
        
        if (confirmed) {
            try {
                const recordIds = this.selectedRecords.map(r => r.id);
                await this.env.services.orm.unlink(this.props.resModel, recordIds);
                
                this.selectedRecords = [];
                this.loadData();
                
            } catch (error) {
                console.error('批量删除失败:', error);
            }
        }
    }
    
    bulkExport() {
        const recordIds = this.selectedRecords.map(r => r.id);
        
        this.env.services.action.doAction({
            type: 'ir.actions.act_url',
            url: `/web/export/xlsx?model=${this.props.resModel}&ids=${recordIds.join(',')}`,
            target: 'self'
        });
    }
    
    onRecordClick(record) {
        console.log('记录点击:', record);
    }
    
    onRecordEdit(record, fieldName, value) {
        console.log('记录编辑:', record, fieldName, value);
    }
    
    onSelectionChange(selectedRecords) {
        this.selectedRecords = selectedRecords;
    }
    
    onSort(fieldName, order) {
        this.orderBy = [{ name: fieldName, asc: order === 'asc' }];
        this.loadData();
    }
    
    onGroupToggle(groupKey, expanded) {
        console.log('分组切换:', groupKey, expanded);
    }
    
    onColumnsCustomized(columns) {
        // 处理列自定义结果
        console.log('列自定义:', columns);
    }
}
```

## 最佳实践

### 1. 列表配置
```javascript
// ✅ 推荐：合理的列表配置
<tree string="客户列表"
      editable="bottom"
      limit="80"
      create="true"
      delete="true">
```

### 2. 列定义
```javascript
// ✅ 推荐：清晰的列定义
<field name="name" string="客户名称"/>
<field name="email" widget="email"/>
<field name="total_invoiced" widget="monetary" sum="总计"/>
```

### 3. 性能优化
```javascript
// ✅ 推荐：大数据集优化
<tree string="大数据列表"
      limit="100"
      virtual_scrolling="true">
```

## 总结

Odoo 列表视图模块提供了强大的数据展示功能：

**核心优势**:
- **高效展示**: 高性能的数据表格展示
- **丰富交互**: 排序、筛选、分组等交互功能
- **批量操作**: 强大的批量选择和操作功能
- **内联编辑**: 便捷的内联编辑和验证
- **性能优化**: 虚拟滚动和懒加载等优化

**适用场景**:
- 数据浏览和查看
- 批量数据操作
- 数据分析和统计
- 快速数据录入
- 数据导入导出

**设计优势**:
- 组件化架构
- 高度可配置
- 性能优化
- 用户友好

这个列表视图为 Odoo Web 客户端提供了强大的数据管理能力，是数据密集型应用的重要组件。

# Hoot-DOM 测试库

## 概述

Hoot-DOM 是 Odoo Web 的专业DOM测试库，提供了完整的前端测试解决方案。该库专门为DOM操作、用户交互模拟和异步测试而设计，具备强大的选择器支持、真实的事件模拟、精确的时间控制等特性，是Odoo Web前端测试框架的核心组件。

## 目录结构

```
hoot-dom/
├── hoot-dom.js              # 主入口文件，统一API接口
├── hoot_dom_utils.js        # 核心工具函数
├── helpers/                 # 助手模块目录
│   ├── dom.js              # DOM操作助手 (1915行)
│   ├── events.js           # 事件模拟助手 (2505行)
│   └── time.js             # 时间控制助手 (452行)
└── README.md               # 本文档
```

## 核心模块

### 1. 主入口模块 (hoot-dom.js)

**功能**: 统一的API入口，整合所有功能模块
- **行数**: 164行
- **作用**: 门面模式，提供统一接口
- **特点**: 模块化设计，类型安全

**主要导出**:
```javascript
// DOM操作
import { 
    queryOne, queryAll, queryText, queryValue,
    isVisible, isDisplayed, isFocusable,
    waitFor, waitForNone, formatXml
} from '@odoo/hoot-dom';

// 事件模拟
import {
    click, fill, press, drag, hover,
    check, select, setInputFiles
} from '@odoo/hoot-dom';

// 时间控制
import {
    delay, advanceTime, freezeTime,
    runAllTimers, animationFrame, waitUntil
} from '@odoo/hoot-dom';
```

### 2. 工具函数模块 (hoot_dom_utils.js)

**功能**: 基础工具函数集合
- **行数**: 134行
- **作用**: 提供基础工具支持
- **特点**: 类型安全，错误处理

**核心功能**:
- **节点操作**: `getTag()` - 获取节点标签
- **浏览器检测**: `isFirefox()` - 浏览器类型检测
- **类型判断**: `isIterable()` - 可迭代对象判断
- **正则处理**: `parseRegExp()` - 正则表达式解析
- **选择器生成**: `toSelector()` - CSS选择器生成
- **错误处理**: `HootDomError` - 专用错误类

### 3. DOM操作助手 (helpers/dom.js)

**功能**: 全面的DOM查询和操作功能
- **行数**: 1915行
- **作用**: DOM测试的核心引擎
- **特点**: 强大选择器，异步支持

**主要功能模块**:

#### 元素查询
```javascript
// 基础查询
queryOne(selector, options)          // 查询单个元素
queryAll(selector, options)          // 查询所有元素
queryFirst(selector, options)        // 查询第一个元素

// 内容查询
queryText(target, options)           // 查询文本内容
queryValue(target)                   // 查询表单值
queryAttribute(target, attribute)    // 查询属性值
queryRect(target, options)           // 查询元素矩形
```

#### 状态检测
```javascript
// 可见性检测
isVisible(target)                    // 是否可见
isDisplayed(target)                  // 是否显示
isInViewPort(target)                 // 是否在视口内

// 交互性检测
isFocusable(target)                  // 是否可聚焦
isEditable(target)                   // 是否可编辑
isScrollable(target)                 // 是否可滚动
```

#### 异步等待
```javascript
// 元素等待
waitFor(selector, options)           // 等待元素出现
waitForNone(selector, options)       // 等待元素消失
waitUntil(predicate, options)        // 等待条件满足
```

#### 焦点管理
```javascript
// 焦点操作
getActiveElement()                   // 获取活动元素
getFocusableElements(options)        // 获取可聚焦元素
getNextFocusableElement(target)      // 获取下一个可聚焦元素
```

### 4. 事件模拟助手 (helpers/events.js)

**功能**: 完整的用户交互事件模拟
- **行数**: 2505行
- **作用**: 用户交互的真实模拟
- **特点**: 真实事件序列，异步支持

**主要功能模块**:

#### 鼠标事件
```javascript
// 点击事件
click(target, options)               // 单击
dblclick(target, options)            // 双击

// 指针事件
pointerDown(target, options)         // 指针按下
pointerUp(target, options)           // 指针释放

// 悬停事件
hover(target, options)               // 悬停
leave(target, options)               // 离开
```

#### 键盘事件
```javascript
// 按键事件
keyDown(target, key, options)        // 按键按下
keyUp(target, key, options)          // 按键释放
press(target, keys, options)         // 按键序列
```

#### 表单操作
```javascript
// 输入操作
fill(target, value, options)         // 填充输入
clear(target, options)               // 清空输入
edit(target, value, options)         // 编辑内容

// 选择操作
check(target, options)               // 选中复选框
uncheck(target, options)             // 取消选中
select(target, value, options)       // 选择选项

// 文件操作
setInputFiles(target, files)         // 设置文件输入
setInputRange(target, range)         // 设置输入范围
```

#### 拖拽操作
```javascript
// 拖拽操作（链式API）
const dragHelper = await drag(source);
await dragHelper.moveTo(target);
await dragHelper.drop();

// 或者取消拖拽
await dragHelper.cancel();
```

#### 窗口操作
```javascript
// 窗口控制
resize(target, dimensions)           // 调整窗口大小
scroll(target, position)             // 滚动操作
unload(target)                       // 卸载页面
```

### 5. 时间控制助手 (helpers/time.js)

**功能**: 测试环境的时间管理和控制
- **行数**: 452行
- **作用**: 精确的时间控制
- **特点**: 可预测的异步行为

**主要功能模块**:

#### 时间控制
```javascript
// 时间操作
freezeTime()                         // 冻结时间
advanceTime(milliseconds)            // 推进时间
setFrameRate(fps)                    // 设置帧率
```

#### 定时器管理
```javascript
// 定时器控制
runAllTimers()                       // 运行所有定时器
cancelAllTimers()                    // 取消所有定时器
```

#### 异步等待
```javascript
// 等待操作
delay(milliseconds)                  // 延迟等待
animationFrame()                     // 等待动画帧
microTick()                          // 微任务
tick()                               // 时钟滴答
waitUntil(predicate, options)        // 条件等待
```

#### 延迟对象
```javascript
// 手动控制的Promise
const deferred = new Deferred();
deferred.resolve(value);             // 手动解决
deferred.reject(error);              // 手动拒绝
```

## 使用示例

### 基础DOM测试

```javascript
import { queryOne, queryText, isVisible, click, fill } from '@odoo/hoot-dom';

describe('基础DOM测试', () => {
    test('元素查询和交互', async () => {
        // 查询元素
        const input = queryOne('input[name="username"]');
        const button = queryOne('.submit-button');
        
        // 检查状态
        expect(isVisible(input)).toBe(true);
        expect(isVisible(button)).toBe(true);
        
        // 用户交互
        await fill(input, 'testuser');
        await click(button);
        
        // 验证结果
        const message = queryText('.success-message');
        expect(message).toContain('成功');
    });
});
```

### 异步操作测试

```javascript
import { waitFor, advanceTime, runAllTimers } from '@odoo/hoot-dom';

describe('异步操作测试', () => {
    test('等待元素出现', async () => {
        // 触发异步加载
        click('.load-button');
        
        // 等待加载完成
        await waitFor('.loading-complete', { timeout: 5000 });
        
        // 验证结果
        expect(queryOne('.data-loaded')).toBeTruthy();
    });
    
    test('时间控制', async () => {
        let executed = false;
        
        // 设置定时器
        setTimeout(() => { executed = true; }, 1000);
        
        // 推进时间并执行
        advanceTime(1000);
        runAllTimers();
        
        expect(executed).toBe(true);
    });
});
```

### 复杂交互测试

```javascript
import { drag, hover, press, setInputFiles } from '@odoo/hoot-dom';

describe('复杂交互测试', () => {
    test('拖拽操作', async () => {
        const source = queryOne('.draggable');
        const target = queryOne('.drop-zone');
        
        // 执行拖拽
        const dragHelper = await drag(source);
        await dragHelper.drop(target);
        
        // 验证结果
        expect(queryOne('.drop-zone .draggable')).toBeTruthy();
    });
    
    test('文件上传', async () => {
        const fileInput = queryOne('input[type="file"]');
        const file = new File(['content'], 'test.txt');
        
        // 设置文件
        await setInputFiles(fileInput, [file]);
        
        // 验证文件
        expect(fileInput.files[0].name).toBe('test.txt');
    });
});
```

## 技术特点

### 1. 强大的选择器支持
- **CSS选择器**: 完整的CSS选择器支持
- **自定义伪类**: 扩展的伪类选择器（:visible, :editable等）
- **复杂组合**: 支持复杂的选择器组合
- **性能优化**: 高效的选择器解析和匹配

### 2. 真实的事件模拟
- **完整事件序列**: 模拟真实的用户操作事件序列
- **浏览器兼容**: 跨浏览器的一致性行为
- **异步支持**: 完整的异步事件处理
- **精确控制**: 精确的事件时序和位置控制

### 3. 精确的时间控制
- **时间冻结**: 停止时间流逝用于测试
- **时间推进**: 精确控制时间的流逝
- **定时器管理**: 完整的定时器生命周期管理
- **异步等待**: 可预测的异步操作控制

### 4. 类型安全
- **TypeScript支持**: 完整的TypeScript类型定义
- **类型推断**: 智能的类型推断和检查
- **编译时检查**: 编译时的类型安全检查
- **开发体验**: 更好的开发体验和错误提示

## 设计模式

### 1. 门面模式 (Facade Pattern)
- **统一接口**: hoot-dom.js提供统一的API入口
- **复杂性隐藏**: 隐藏内部模块的复杂性
- **易用性**: 简化使用接口

### 2. 模块模式 (Module Pattern)
- **功能分离**: 按功能模块化组织代码
- **依赖管理**: 清晰的模块依赖关系
- **可维护性**: 提高代码的可维护性

### 3. 策略模式 (Strategy Pattern)
- **多种策略**: 不同浏览器的兼容性策略
- **算法封装**: 封装不同的查询和操作算法
- **灵活切换**: 根据环境灵活切换策略

### 4. 观察者模式 (Observer Pattern)
- **DOM监听**: DOM变化的监听和响应
- **事件处理**: 事件的分发和处理
- **异步通知**: 异步操作的状态通知

## 最佳实践

### 1. 测试组织
```javascript
// 使用describe组织测试
describe('功能模块测试', () => {
    beforeEach(() => {
        // 测试前准备
    });
    
    afterEach(() => {
        // 测试后清理
        cancelAllTimers();
    });
    
    test('具体功能测试', async () => {
        // 测试实现
    });
});
```

### 2. 异步处理
```javascript
// 正确的异步等待
await waitFor('.element');
await click('.button');
await waitUntil(() => condition);

// 时间控制
freezeTime();
advanceTime(1000);
runAllTimers();
```

### 3. 错误处理
```javascript
// 使用try-catch处理异步错误
try {
    await waitFor('.element', { timeout: 1000 });
} catch (error) {
    // 处理超时错误
}
```

### 4. 性能优化
```javascript
// 批量操作
const elements = queryAll('.items');
for (const element of elements) {
    await click(element);
}

// 避免频繁查询
const container = queryOne('.container');
const items = queryAll('.item', { root: container });
```

## 注意事项

1. **异步处理**: 正确使用async/await处理异步操作
2. **时间控制**: 测试后记得清理时间状态
3. **内存管理**: 及时清理事件监听器和定时器
4. **浏览器兼容**: 注意不同浏览器的行为差异
5. **性能考虑**: 避免过度复杂的DOM操作

## 扩展和定制

Hoot-DOM库设计为可扩展的，可以根据项目需求进行定制：

1. **自定义伪类**: 扩展更多的伪类选择器
2. **事件扩展**: 添加特定的事件模拟功能
3. **工具函数**: 基于基础API创建项目特定的工具
4. **插件系统**: 开发插件扩展功能
5. **集成工具**: 与其他测试工具集成

## 总结

Hoot-DOM是一个功能完整、设计精良的DOM测试库，为Odoo Web应用提供了专业的前端测试解决方案。通过模块化设计、类型安全、真实事件模拟和精确时间控制，确保了测试的可靠性、可维护性和开发效率。

该库的核心优势在于：
- **完整性**: 覆盖DOM测试的所有方面
- **真实性**: 模拟真实的用户交互
- **可控性**: 精确控制测试环境
- **易用性**: 简洁直观的API设计
- **可靠性**: 稳定可预测的测试行为

无论是单元测试、集成测试还是端到端测试，Hoot-DOM都能提供强大的支持，是前端测试开发的理想选择。

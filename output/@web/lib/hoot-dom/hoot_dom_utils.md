# Hoot DOM Utils - DOM测试工具函数

## 概述

`hoot_dom_utils.js` 是 Hoot-DOM 测试库的核心工具函数模块，提供了DOM测试中常用的基础工具函数。该模块包含节点标签获取、浏览器检测、可迭代对象判断、正则表达式解析、选择器生成等功能，具备类型安全、错误处理、跨浏览器兼容等特性，为DOM测试提供了可靠的基础工具支持，是整个Hoot-DOM测试框架的工具基础。

## 文件信息
- **路径**: `/web/static/lib/hoot-dom/hoot_dom_utils.js`
- **行数**: 134
- **模块**: `@web/../lib/hoot-dom/hoot_dom_utils`

## 类型定义

### 1. 参数类型定义

```javascript
/**
 * @typedef {ArgumentPrimitive | `${ArgumentPrimitive}[]` | null} ArgumentType
 * 
 * @typedef {"any"
 *  | "bigint"
 *  | "boolean" 
 *  | "error"
 *  | "function"
 *  | "integer"
 *  | "node"
 *  | "number"
 *  | "object"
 *  | "regex"
 *  | "string"
 *  | "symbol"
 *  | "undefined"} ArgumentPrimitive
 */
```

### 2. 泛型类型定义

```javascript
/**
 * @template T
 * @typedef {T | Iterable<T>} MaybeIterable - 可能可迭代的类型
 * 
 * @template T
 * @typedef {T | PromiseLike<T>} MaybePromise - 可能是Promise的类型
 */
```

## 全局变量

```javascript
const {
    Boolean,
    navigator: { userAgent: $userAgent },
    RegExp,
    SyntaxError,
} = globalThis;

// 正则表达式模式
const R_REGEX_PATTERN = /^\/(.*)\/([dgimsuvy]+)?$/;
```

## 核心函数

### 1. 节点标签获取

```javascript
function getTag(node) {
    return node?.nodeName.toLowerCase() || "";
}
```

**功能说明**:
- **标签提取**: 获取DOM节点的标签名
- **小写转换**: 自动转换为小写格式
- **安全处理**: 处理null/undefined节点
- **空字符串回退**: 无效节点返回空字符串

**使用示例**:
```javascript
const div = document.createElement('div');
console.log(getTag(div)); // "div"

const span = document.querySelector('span');
console.log(getTag(span)); // "span"

console.log(getTag(null)); // ""
```

### 2. 浏览器检测

```javascript
function isFirefox() {
    return /firefox/i.test($userAgent);
}
```

**功能说明**:
- **Firefox检测**: 检测是否为Firefox浏览器
- **用户代理解析**: 基于userAgent字符串判断
- **大小写不敏感**: 使用不区分大小写的正则匹配
- **布尔返回**: 返回true或false

**使用示例**:
```javascript
if (isFirefox()) {
    console.log('当前浏览器是Firefox');
    // Firefox特定的处理逻辑
}
```

### 3. 可迭代对象判断

```javascript
function isIterable(object) {
    return Boolean(object && typeof object === "object" && object[Symbol.iterator]);
}
```

**功能说明**:
- **迭代器检测**: 检查对象是否实现了迭代器接口
- **排除字符串**: 虽然字符串可迭代，但此函数排除字符串
- **类型安全**: 先检查对象类型再检查迭代器
- **Symbol.iterator**: 检查是否有迭代器符号

**使用示例**:
```javascript
console.log(isIterable([])); // true
console.log(isIterable(new Set())); // true
console.log(isIterable(new Map())); // true
console.log(isIterable("string")); // false (排除字符串)
console.log(isIterable({})); // false
console.log(isIterable(null)); // false
```

### 4. 正则表达式过滤器检测

```javascript
function isRegExpFilter(filter) {
    return R_REGEX_PATTERN.test(filter);
}
```

**功能说明**:
- **正则格式检测**: 检查字符串是否为正则表达式格式
- **模式匹配**: 匹配 `/pattern/flags` 格式
- **标志支持**: 支持各种正则表达式标志
- **快速判断**: 快速判断是否需要正则解析

**使用示例**:
```javascript
console.log(isRegExpFilter('/test/i')); // true
console.log(isRegExpFilter('/^hello$/g')); // true
console.log(isRegExpFilter('normal string')); // false
console.log(isRegExpFilter('/incomplete')); // false
```

### 5. 正则表达式解析

```javascript
function parseRegExp(value, options) {
    const regexParams = value.match(R_REGEX_PATTERN);
    if (regexParams) {
        const unified = regexParams[1].replace(/\s+/g, "\\s+");
        const flag = regexParams[2] || "i";
        try {
            return new RegExp(unified, flag);
        } catch (error) {
            if (error instanceof SyntaxError && options?.safe) {
                return value;
            } else {
                throw error;
            }
        }
    }
    return value;
}
```

**功能说明**:
- **正则解析**: 将字符串解析为RegExp对象
- **空格处理**: 自动处理模式中的空格
- **默认标志**: 默认使用不区分大小写标志
- **安全模式**: 可选的安全模式，解析失败时返回原值
- **错误处理**: 处理语法错误并可选择性抛出

**使用示例**:
```javascript
// 基础解析
const regex1 = parseRegExp('/hello/i');
console.log(regex1); // /hello/i

// 空格处理
const regex2 = parseRegExp('/hello world/');
console.log(regex2); // /hello\s+world/i

// 安全模式
const result = parseRegExp('/[invalid/i', { safe: true });
console.log(result); // '/[invalid/i' (原字符串)

// 非正则字符串
const normal = parseRegExp('normal string');
console.log(normal); // 'normal string'
```

### 6. 选择器生成

```javascript
function toSelector(node, options) {
    const tagName = getTag(node);
    const id = node.id ? `#${node.id}` : "";
    const classNames = node.classList
        ? [...node.classList].map((className) => `.${className}`)
        : [];
    if (options?.raw) {
        return { tagName, id, classNames };
    } else {
        return [tagName, id, ...classNames].join("");
    }
}
```

**功能说明**:
- **选择器构建**: 根据DOM节点生成CSS选择器
- **标签名**: 包含元素的标签名
- **ID选择器**: 包含元素的ID（如果有）
- **类选择器**: 包含所有类名
- **原始模式**: 可选择返回原始对象而非字符串
- **安全处理**: 处理没有classList的节点

**使用示例**:
```javascript
// HTML: <div id="main" class="container active">
const element = document.querySelector('#main');

// 字符串模式
const selector = toSelector(element);
console.log(selector); // "div#main.container.active"

// 原始模式
const raw = toSelector(element, { raw: true });
console.log(raw); 
// { 
//   tagName: "div", 
//   id: "#main", 
//   classNames: [".container", ".active"] 
// }

// 无ID和类的元素
const span = document.createElement('span');
console.log(toSelector(span)); // "span"
```

## 错误类定义

```javascript
class HootDomError extends Error {
    name = "HootDomError";
}
```

**错误类功能**:
- **专用错误**: Hoot-DOM专用的错误类型
- **错误标识**: 明确的错误名称标识
- **继承Error**: 继承标准Error类的所有功能
- **调试支持**: 便于错误追踪和调试

**使用示例**:
```javascript
try {
    // 某些DOM操作
    throw new HootDomError('DOM操作失败');
} catch (error) {
    if (error instanceof HootDomError) {
        console.log('Hoot-DOM错误:', error.message);
    }
}
```

## 使用场景

### 1. DOM元素分析工具

```javascript
// DOM元素分析工具
class DOMAnalyzer {
    constructor() {
        this.stats = {
            totalElements: 0,
            elementTypes: new Map(),
            elementsWithId: 0,
            elementsWithClass: 0
        };
    }

    analyzeElement(element) {
        const tag = getTag(element);
        const selector = toSelector(element);
        const selectorParts = toSelector(element, { raw: true });

        // 统计元素类型
        this.stats.totalElements++;
        this.stats.elementTypes.set(tag, (this.stats.elementTypes.get(tag) || 0) + 1);

        // 统计ID和类
        if (selectorParts.id) {
            this.stats.elementsWithId++;
        }
        if (selectorParts.classNames.length > 0) {
            this.stats.elementsWithClass++;
        }

        return {
            tag,
            selector,
            parts: selectorParts,
            hasId: Boolean(selectorParts.id),
            hasClasses: selectorParts.classNames.length > 0,
            classCount: selectorParts.classNames.length
        };
    }

    analyzeDocument() {
        const allElements = document.querySelectorAll('*');
        const results = [];

        allElements.forEach(element => {
            results.push(this.analyzeElement(element));
        });

        return {
            elements: results,
            statistics: this.stats,
            summary: this.generateSummary()
        };
    }

    generateSummary() {
        const topElements = [...this.stats.elementTypes.entries()]
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5);

        return {
            totalElements: this.stats.totalElements,
            topElementTypes: topElements,
            idUsageRate: (this.stats.elementsWithId / this.stats.totalElements * 100).toFixed(2) + '%',
            classUsageRate: (this.stats.elementsWithClass / this.stats.totalElements * 100).toFixed(2) + '%'
        };
    }
}

// 使用示例
const analyzer = new DOMAnalyzer();
const analysis = analyzer.analyzeDocument();
console.log('DOM分析结果:', analysis);
```

### 2. 测试工具集合

```javascript
// 测试工具集合
class TestUtils {
    static createTestElement(tag, attributes = {}) {
        const element = document.createElement(tag);
        
        Object.entries(attributes).forEach(([key, value]) => {
            if (key === 'class') {
                if (isIterable(value)) {
                    element.classList.add(...value);
                } else {
                    element.className = value;
                }
            } else if (key === 'text') {
                element.textContent = value;
            } else {
                element.setAttribute(key, value);
            }
        });

        return element;
    }

    static findElementsByPattern(pattern, container = document) {
        const elements = container.querySelectorAll('*');
        const results = [];

        if (isRegExpFilter(pattern)) {
            const regex = parseRegExp(pattern);
            elements.forEach(element => {
                const selector = toSelector(element);
                if (regex.test(selector) || regex.test(element.textContent)) {
                    results.push(element);
                }
            });
        } else {
            // 普通字符串搜索
            elements.forEach(element => {
                const selector = toSelector(element);
                if (selector.includes(pattern) || element.textContent.includes(pattern)) {
                    results.push(element);
                }
            });
        }

        return results;
    }

    static validateSelector(selector) {
        try {
            document.querySelector(selector);
            return { valid: true, error: null };
        } catch (error) {
            return { valid: false, error: error.message };
        }
    }

    static getBrowserInfo() {
        return {
            isFirefox: isFirefox(),
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language
        };
    }

    static debugElement(element) {
        if (!element) {
            throw new HootDomError('Element is null or undefined');
        }

        const info = {
            tag: getTag(element),
            selector: toSelector(element),
            selectorParts: toSelector(element, { raw: true }),
            attributes: {},
            properties: {},
            styles: {},
            position: element.getBoundingClientRect(),
            visible: element.offsetParent !== null,
            inDOM: document.contains(element)
        };

        // 收集属性
        for (const attr of element.attributes || []) {
            info.attributes[attr.name] = attr.value;
        }

        // 收集重要属性
        ['id', 'className', 'tagName', 'textContent', 'innerHTML'].forEach(prop => {
            if (element[prop] !== undefined) {
                info.properties[prop] = element[prop];
            }
        });

        // 收集计算样式
        if (element.nodeType === Node.ELEMENT_NODE) {
            const computedStyle = getComputedStyle(element);
            ['display', 'visibility', 'opacity', 'position', 'zIndex'].forEach(prop => {
                info.styles[prop] = computedStyle[prop];
            });
        }

        return info;
    }
}

// 使用示例
const testElement = TestUtils.createTestElement('div', {
    id: 'test',
    class: ['container', 'active'],
    'data-test': 'value',
    text: 'Test Content'
});

document.body.appendChild(testElement);

// 调试元素
const debugInfo = TestUtils.debugElement(testElement);
console.log('元素调试信息:', debugInfo);

// 查找元素
const foundElements = TestUtils.findElementsByPattern('/test/i');
console.log('找到的元素:', foundElements);

// 浏览器信息
const browserInfo = TestUtils.getBrowserInfo();
console.log('浏览器信息:', browserInfo);
```

### 3. 正则表达式测试工具

```javascript
// 正则表达式测试工具
class RegexTester {
    constructor() {
        this.testCases = [];
        this.results = [];
    }

    addTestCase(pattern, testStrings, expectedResults) {
        this.testCases.push({
            pattern,
            testStrings: isIterable(testStrings) ? [...testStrings] : [testStrings],
            expectedResults: isIterable(expectedResults) ? [...expectedResults] : [expectedResults]
        });
    }

    runTests() {
        this.results = [];

        this.testCases.forEach((testCase, index) => {
            const result = {
                caseIndex: index,
                pattern: testCase.pattern,
                isRegexPattern: isRegExpFilter(testCase.pattern),
                regex: null,
                tests: []
            };

            try {
                if (result.isRegexPattern) {
                    result.regex = parseRegExp(testCase.pattern);
                }

                testCase.testStrings.forEach((testString, stringIndex) => {
                    const expected = testCase.expectedResults[stringIndex];
                    let actual;

                    if (result.regex) {
                        actual = result.regex.test(testString);
                    } else {
                        actual = testString.includes(testCase.pattern);
                    }

                    result.tests.push({
                        testString,
                        expected,
                        actual,
                        passed: actual === expected
                    });
                });

            } catch (error) {
                result.error = error.message;
            }

            this.results.push(result);
        });

        return this.results;
    }

    generateReport() {
        const totalTests = this.results.reduce((sum, result) => sum + result.tests.length, 0);
        const passedTests = this.results.reduce((sum, result) => 
            sum + result.tests.filter(test => test.passed).length, 0);

        return {
            summary: {
                totalCases: this.results.length,
                totalTests,
                passedTests,
                failedTests: totalTests - passedTests,
                successRate: ((passedTests / totalTests) * 100).toFixed(2) + '%'
            },
            details: this.results,
            failures: this.results.filter(result => 
                result.error || result.tests.some(test => !test.passed)
            )
        };
    }
}

// 使用示例
const regexTester = new RegexTester();

// 添加测试用例
regexTester.addTestCase('/^hello/i', ['Hello World', 'hello there', 'Hi hello'], [true, true, false]);
regexTester.addTestCase('/\\d+/', ['abc123', 'no numbers', '456def'], [true, false, true]);
regexTester.addTestCase('simple', ['simple test', 'complex', 'simply'], [true, false, true]);

// 运行测试
const results = regexTester.runTests();
console.log('测试结果:', results);

// 生成报告
const report = regexTester.generateReport();
console.log('测试报告:', report);
```

## 技术特点

### 1. 类型安全
- 完整的TypeScript类型定义
- 泛型支持
- 类型检查和推断

### 2. 错误处理
- 专用错误类
- 安全模式选项
- 优雅的错误恢复

### 3. 浏览器兼容
- 跨浏览器支持
- 特定浏览器检测
- 兼容性处理

### 4. 实用工具
- 常用DOM操作
- 正则表达式处理
- 选择器生成

## 设计模式

### 1. 工具模式 (Utility Pattern)
- 纯函数设计
- 无状态操作
- 可重用工具集

### 2. 策略模式 (Strategy Pattern)
- 不同解析策略
- 可选的处理模式

### 3. 工厂模式 (Factory Pattern)
- 错误对象创建
- 选择器生成

## 注意事项

1. **类型检查**: 确保传入正确的参数类型
2. **错误处理**: 适当处理可能的异常情况
3. **性能考虑**: 避免频繁的DOM查询
4. **浏览器差异**: 注意不同浏览器的行为差异

## 扩展建议

1. **更多浏览器检测**: 添加其他浏览器的检测函数
2. **选择器优化**: 优化选择器生成算法
3. **性能监控**: 添加性能监控工具
4. **调试工具**: 增强调试和日志功能
5. **类型扩展**: 扩展更多的类型定义

该工具模块为Hoot-DOM测试库提供了坚实的基础工具支持，通过类型安全和错误处理确保了测试代码的可靠性和可维护性。

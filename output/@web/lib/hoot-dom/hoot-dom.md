# Hoot-DOM - DOM测试工具库

## 概述

`hoot-dom.js` 是 Odoo Web 的 DOM 测试工具库主入口文件，提供了完整的 DOM 测试功能集合。该模块整合了 DOM 查询、事件模拟、时间控制等三大核心功能模块，为前端测试提供了统一的API接口，具备元素查询、用户交互模拟、异步操作控制等特性，是 Odoo Web 前端测试框架的核心组件，广泛应用于单元测试、集成测试、端到端测试等各种测试场景。

## 文件信息
- **路径**: `/web/static/lib/hoot-dom/hoot-dom.js`
- **行数**: 164
- **模块**: `@web/../lib/hoot-dom/hoot-dom`
- **别名**: `@odoo/hoot-dom`

## 依赖关系

```javascript
// 核心依赖模块
'@web/../lib/hoot-dom/helpers/dom'      // DOM操作助手
'@web/../lib/hoot-dom/helpers/events'   // 事件模拟助手
'@web/../lib/hoot-dom/helpers/time'     // 时间控制助手
```

## 类型定义

### 1. DOM相关类型

```javascript
/**
 * @typedef {import("./helpers/dom").Dimensions} Dimensions - 尺寸信息
 * @typedef {import("./helpers/dom").FormatXmlOptions} FormatXmlOptions - XML格式化选项
 * @typedef {import("./helpers/dom").Position} Position - 位置信息
 * @typedef {import("./helpers/dom").QueryOptions} QueryOptions - 查询选项
 * @typedef {import("./helpers/dom").QueryRectOptions} QueryRectOptions - 矩形查询选项
 * @typedef {import("./helpers/dom").QueryTextOptions} QueryTextOptions - 文本查询选项
 * @typedef {import("./helpers/dom").Target} Target - 目标元素
 */
```

### 2. 事件相关类型

```javascript
/**
 * @typedef {import("./helpers/events").DragHelpers} DragHelpers - 拖拽助手
 * @typedef {import("./helpers/events").EventType} EventType - 事件类型
 * @typedef {import("./helpers/events").FillOptions} FillOptions - 填充选项
 * @typedef {import("./helpers/events").InputValue} InputValue - 输入值
 * @typedef {import("./helpers/events").KeyStrokes} KeyStrokes - 按键序列
 * @typedef {import("./helpers/events").PointerOptions} PointerOptions - 指针选项
 */
```

## 功能模块

### 1. DOM操作模块

```javascript
// DOM查询和操作功能
const domHelpers = {
    formatXml,                    // XML格式化
    getActiveElement,             // 获取活动元素
    getFocusableElements,         // 获取可聚焦元素
    getNextFocusableElement,      // 获取下一个可聚焦元素
    getPreviousFocusableElement,  // 获取上一个可聚焦元素
    isDisplayed,                  // 检查是否显示
    isEditable,                   // 检查是否可编辑
    isFocusable,                  // 检查是否可聚焦
    isInDOM,                      // 检查是否在DOM中
    isInViewPort,                 // 检查是否在视口中
    isScrollable,                 // 检查是否可滚动
    isVisible,                    // 检查是否可见
    matches,                      // 匹配选择器
    observe,                      // 观察DOM变化
    queryAll,                     // 查询所有元素
    queryAllAttributes,           // 查询所有属性
    queryAllProperties,           // 查询所有属性
    queryAllRects,                // 查询所有矩形
    queryAllTexts,                // 查询所有文本
    queryAllValues,               // 查询所有值
    queryAttribute,               // 查询属性
    queryFirst,                   // 查询第一个元素
    queryOne,                     // 查询单个元素
    queryRect,                    // 查询矩形
    queryText,                    // 查询文本
    queryValue,                   // 查询值
    waitFor,                      // 等待元素出现
    waitForNone,                  // 等待元素消失
};
```

### 2. 事件模拟模块

```javascript
// 用户交互事件模拟功能
const eventHelpers = {
    check,                        // 选中复选框
    clear,                        // 清空输入
    click,                        // 点击
    dblclick,                     // 双击
    drag,                         // 拖拽
    edit,                         // 编辑
    fill,                         // 填充
    hover,                        // 悬停
    keyDown,                      // 按键按下
    keyUp,                        // 按键释放
    leave,                        // 离开
    dispatch,                     // 手动分发事件
    on,                           // 事件监听
    pointerDown,                  // 指针按下
    pointerUp,                    // 指针释放
    press,                        // 按键
    resize,                       // 调整大小
    scroll,                       // 滚动
    select,                       // 选择
    setInputFiles,                // 设置文件输入
    setInputRange,                // 设置输入范围
    uncheck,                      // 取消选中
    unload,                       // 卸载
};
```

### 3. 时间控制模块

```javascript
// 时间和异步操作控制功能
const timeHelpers = {
    Deferred,                     // 延迟对象
    advanceFrame,                 // 推进帧
    advanceTime,                  // 推进时间
    animationFrame,               // 动画帧
    cancelAllTimers,              // 取消所有定时器
    delay,                        // 延迟
    freezeTime,                   // 冻结时间
    microTick,                    // 微任务
    runAllTimers,                 // 运行所有定时器
    setFrameRate,                 // 设置帧率
    tick,                         // 时钟滴答
    waitUntil,                    // 等待直到
};
```

## 使用场景

### 1. 基础DOM测试

```javascript
// 基础DOM测试示例
import { 
    queryOne, 
    queryText, 
    click, 
    fill, 
    waitFor 
} from '@odoo/hoot-dom';

describe('基础DOM测试', () => {
    test('元素查询和交互', async () => {
        // 查询元素
        const button = queryOne('.submit-button');
        const input = queryOne('input[name="username"]');
        
        // 检查元素存在
        expect(button).toBeTruthy();
        expect(input).toBeTruthy();
        
        // 填充输入框
        await fill(input, 'testuser');
        
        // 验证输入值
        expect(queryValue(input)).toBe('testuser');
        
        // 点击按钮
        await click(button);
        
        // 等待结果出现
        await waitFor('.success-message');
        
        // 验证结果文本
        const message = queryText('.success-message');
        expect(message).toContain('成功');
    });
    
    test('元素状态检查', () => {
        const element = queryOne('.test-element');
        
        // 检查各种状态
        expect(isVisible(element)).toBe(true);
        expect(isInDOM(element)).toBe(true);
        expect(isDisplayed(element)).toBe(true);
        expect(isFocusable(element)).toBe(false);
    });
});
```

### 2. 复杂交互测试

```javascript
// 复杂交互测试示例
import { 
    queryAll, 
    drag, 
    hover, 
    press, 
    keyDown, 
    keyUp,
    setInputFiles,
    dblclick
} from '@odoo/hoot-dom';

describe('复杂交互测试', () => {
    test('拖拽操作', async () => {
        const source = queryOne('.draggable-item');
        const target = queryOne('.drop-zone');
        
        // 执行拖拽操作
        await drag(source, { to: target });
        
        // 验证拖拽结果
        expect(queryOne('.drop-zone .draggable-item')).toBeTruthy();
    });
    
    test('键盘操作', async () => {
        const input = queryOne('input');
        
        // 聚焦输入框
        await click(input);
        
        // 模拟键盘输入
        await press('Hello');
        await keyDown('Shift');
        await press('World');
        await keyUp('Shift');
        
        // 验证输入结果
        expect(queryValue(input)).toBe('HelloWORLD');
    });
    
    test('文件上传', async () => {
        const fileInput = queryOne('input[type="file"]');
        const file = new File(['test content'], 'test.txt', { type: 'text/plain' });
        
        // 设置文件
        await setInputFiles(fileInput, [file]);
        
        // 验证文件设置
        expect(fileInput.files.length).toBe(1);
        expect(fileInput.files[0].name).toBe('test.txt');
    });
    
    test('悬停和双击', async () => {
        const element = queryOne('.interactive-element');
        
        // 悬停操作
        await hover(element);
        expect(queryOne('.tooltip')).toBeTruthy();
        
        // 双击操作
        await dblclick(element);
        expect(queryOne('.edit-mode')).toBeTruthy();
    });
});
```

### 3. 异步操作和时间控制测试

```javascript
// 异步操作和时间控制测试示例
import { 
    waitFor, 
    waitUntil, 
    advanceTime, 
    runAllTimers, 
    freezeTime,
    animationFrame,
    tick
} from '@odoo/hoot-dom';

describe('异步操作测试', () => {
    test('等待元素出现', async () => {
        // 触发异步操作
        click('.load-data-button');
        
        // 等待加载完成
        await waitFor('.data-loaded', { timeout: 5000 });
        
        // 验证数据加载
        const items = queryAll('.data-item');
        expect(items.length).toBeGreaterThan(0);
    });
    
    test('等待条件满足', async () => {
        const counter = queryOne('.counter');
        
        // 等待计数器达到特定值
        await waitUntil(() => {
            const value = parseInt(queryText(counter));
            return value >= 10;
        });
        
        expect(parseInt(queryText(counter))).toBeGreaterThanOrEqual(10);
    });
    
    test('时间控制', async () => {
        // 冻结时间
        freezeTime();
        
        // 触发定时器
        click('.start-timer');
        
        // 推进时间
        advanceTime(1000);
        
        // 验证定时器效果
        expect(queryText('.timer-display')).toBe('1');
        
        // 运行所有定时器
        runAllTimers();
        
        // 验证最终状态
        expect(queryOne('.timer-finished')).toBeTruthy();
    });
    
    test('动画帧控制', async () => {
        // 启动动画
        click('.start-animation');
        
        // 等待动画帧
        await animationFrame();
        
        // 验证动画开始
        expect(queryOne('.animating')).toBeTruthy();
        
        // 推进多个帧
        for (let i = 0; i < 60; i++) {
            await tick();
        }
        
        // 验证动画完成
        expect(queryOne('.animation-complete')).toBeTruthy();
    });
});
```

### 4. 表单测试综合示例

```javascript
// 表单测试综合示例
import { 
    queryOne, 
    queryAll, 
    fill, 
    select, 
    check, 
    uncheck, 
    click,
    queryValue,
    queryText,
    waitFor,
    press
} from '@odoo/hoot-dom';

describe('表单测试', () => {
    test('完整表单填写和提交', async () => {
        // 填写文本输入框
        await fill('input[name="name"]', 'John Doe');
        await fill('input[name="email"]', '<EMAIL>');
        await fill('textarea[name="message"]', 'Hello World');
        
        // 选择下拉选项
        await select('select[name="country"]', 'US');
        
        // 操作复选框
        await check('input[name="newsletter"]');
        await uncheck('input[name="spam"]');
        
        // 操作单选按钮
        await click('input[name="gender"][value="male"]');
        
        // 验证表单值
        expect(queryValue('input[name="name"]')).toBe('John Doe');
        expect(queryValue('input[name="email"]')).toBe('<EMAIL>');
        expect(queryValue('textarea[name="message"]')).toBe('Hello World');
        expect(queryValue('select[name="country"]')).toBe('US');
        expect(queryOne('input[name="newsletter"]').checked).toBe(true);
        expect(queryOne('input[name="spam"]').checked).toBe(false);
        expect(queryOne('input[name="gender"][value="male"]').checked).toBe(true);
        
        // 提交表单
        await click('button[type="submit"]');
        
        // 等待提交结果
        await waitFor('.form-success');
        
        // 验证成功消息
        expect(queryText('.form-success')).toContain('提交成功');
    });
    
    test('表单验证测试', async () => {
        // 提交空表单
        await click('button[type="submit"]');
        
        // 等待验证错误
        await waitFor('.validation-errors');
        
        // 验证错误消息
        const errors = queryAll('.error-message');
        expect(errors.length).toBeGreaterThan(0);
        
        // 填写必填字段
        await fill('input[name="email"]', 'invalid-email');
        await click('button[type="submit"]');
        
        // 验证邮箱格式错误
        expect(queryText('.email-error')).toContain('邮箱格式不正确');
        
        // 填写正确邮箱
        await fill('input[name="email"]', '<EMAIL>');
        await click('button[type="submit"]');
        
        // 验证错误消失
        expect(queryOne('.email-error')).toBeFalsy();
    });
    
    test('动态表单字段', async () => {
        // 选择触发动态字段的选项
        await select('select[name="type"]', 'business');
        
        // 等待动态字段出现
        await waitFor('input[name="company"]');
        
        // 填写动态字段
        await fill('input[name="company"]', 'Acme Corp');
        
        // 切换选项
        await select('select[name="type"]', 'personal');
        
        // 等待动态字段消失
        await waitForNone('input[name="company"]');
        
        // 验证新的动态字段
        await waitFor('input[name="hobby"]');
        await fill('input[name="hobby"]', 'Reading');
        
        expect(queryValue('input[name="hobby"]')).toBe('Reading');
    });
});
```

## 技术特点

### 1. 模块化设计
- 功能按模块分离
- 清晰的职责划分
- 易于维护和扩展

### 2. 统一API接口
- 一致的函数命名
- 标准化的参数格式
- 简洁的使用方式

### 3. 类型安全
- 完整的TypeScript类型定义
- 编译时类型检查
- 更好的开发体验

### 4. 异步支持
- Promise-based API
- 异步操作的优雅处理
- 时间控制的精确管理

## 设计模式

### 1. 门面模式 (Facade Pattern)
- 统一的API入口
- 隐藏内部复杂性
- 简化使用接口

### 2. 模块模式 (Module Pattern)
- 功能模块化组织
- 命名空间管理
- 依赖关系清晰

### 3. 代理模式 (Proxy Pattern)
- 事件模拟的代理处理
- DOM操作的安全封装

## 注意事项

1. **测试环境**: 确保在正确的测试环境中使用
2. **异步处理**: 正确处理异步操作和等待
3. **清理工作**: 测试后进行必要的清理
4. **性能考虑**: 避免过度复杂的DOM操作

## 扩展建议

1. **自定义助手**: 基于基础API创建项目特定的助手函数
2. **测试工具**: 集成到自动化测试流程中
3. **调试支持**: 添加调试和日志功能
4. **性能监控**: 监控测试执行性能
5. **报告生成**: 生成详细的测试报告

该Hoot-DOM库为Odoo Web应用提供了完整的DOM测试解决方案，通过模块化设计和统一API确保了测试代码的可维护性和可读性。

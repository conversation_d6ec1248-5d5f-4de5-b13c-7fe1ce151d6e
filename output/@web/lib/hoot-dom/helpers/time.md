# Time Helper - 时间控制助手

## 概述

`time.js` 是 Hoot-DOM 测试库的时间控制助手模块，提供了测试环境中的时间管理和异步操作控制功能。该模块包含452行代码，是DOM测试中时间相关操作的核心组件，提供了定时器控制、动画帧管理、时间冻结、异步等待等功能，具备精确的时间控制、可预测的异步行为、测试环境隔离等特性，为测试提供了可靠的时间控制能力。

## 文件信息
- **路径**: `/web/static/lib/hoot-dom/helpers/time.js`
- **行数**: 452
- **模块**: `@web/../lib/hoot-dom/helpers/time`

## 依赖关系

```javascript
// 核心依赖
'@web/../lib/hoot-dom/hoot_dom_utils'    // DOM工具函数
```

## 全局变量和常量

```javascript
// 浏览器API引用
const {
    cancelAnimationFrame,
    clearInterval,
    clearTimeout,
    performance,
    requestAnimationFrame,
    setInterval,
    setTimeout,
} = globalThis;

// ID前缀常量
const ID_PREFIX = {
    animation: "a_",    // 动画帧ID前缀
    interval: "i_",     // 间隔定时器ID前缀
    timeout: "t_",      // 超时定时器ID前缀
};

// 内部状态变量
let allowTimers = true;      // 是否允许定时器
let freezed = false;         // 时间是否冻结
let frameDelay = 1000 / 60;  // 帧延迟（60fps）
let timeOffset = 0;          // 时间偏移量
```

## 核心功能模块

### 1. 延迟对象 (Deferred)

```javascript
class Deferred {
    constructor() {
        this.promise = new Promise((resolve, reject) => {
            this.resolve = resolve;
            this.reject = reject;
        });
    }
}
```

**延迟对象功能**:
- **Promise封装**: 提供可外部控制的Promise
- **手动解决**: 支持手动resolve和reject
- **异步控制**: 精确控制异步操作时机
- **测试友好**: 便于测试中的异步控制

### 2. 时间推进功能

```javascript
// 推进动画帧
advanceFrame(frameCount = 1)         // 推进指定数量的动画帧

// 推进时间
advanceTime(milliseconds)            // 推进指定毫秒数

// 时间冻结
freezeTime()                         // 冻结时间流逝

// 设置帧率
setFrameRate(fps)                    // 设置动画帧率
```

**时间控制功能**:
- **帧控制**: 精确控制动画帧的推进
- **时间推进**: 模拟时间的流逝
- **冻结机制**: 停止时间流逝用于测试
- **帧率设置**: 自定义动画帧率

### 3. 定时器管理

```javascript
// 定时器控制
runAllTimers()                       // 运行所有待执行的定时器
cancelAllTimers()                    // 取消所有定时器

// 微任务和时钟
microTick()                          // 执行微任务
tick()                               // 时钟滴答（推进一帧）
```

**定时器管理功能**:
- **批量执行**: 一次性执行所有定时器
- **批量取消**: 清理所有定时器
- **微任务**: 处理微任务队列
- **时钟控制**: 精确的时钟推进

### 4. 异步等待功能

```javascript
// 延迟等待
delay(milliseconds)                  // 延迟指定毫秒数

// 动画帧等待
animationFrame()                     // 等待下一个动画帧

// 条件等待
waitUntil(predicate, options)        // 等待条件满足
```

**异步等待功能**:
- **时间延迟**: 精确的时间延迟控制
- **帧同步**: 与动画帧同步的等待
- **条件等待**: 灵活的条件等待机制
- **超时控制**: 可配置的超时机制

## 内部实现机制

### 1. 定时器ID管理

```javascript
// ID转换函数
const animationToId = (id) => ID_PREFIX.animation + String(id);
const intervalToId = (id) => ID_PREFIX.interval + String(id);
const timeoutToId = (id) => ID_PREFIX.timeout + String(id);

// ID解析函数
const idToAnimation = (id) => Number(id.slice(ID_PREFIX.animation.length));
const idToInterval = (id) => Number(id.slice(ID_PREFIX.interval.length));
const idToTimeout = (id) => Number(id.slice(ID_PREFIX.timeout.length));
```

**ID管理功能**:
- **类型区分**: 不同类型定时器的ID区分
- **转换机制**: ID和数值间的转换
- **唯一性**: 确保ID的唯一性
- **类型安全**: 类型安全的ID处理

### 2. 定时器队列管理

```javascript
// 定时器存储
const timers = new Map(); // Map<string, [callback, init, delay]>

// 获取下一个定时器
const getNextTimerValues = () => {
    let timerValues = null;
    for (const [internalId, [callback, init, delay]] of timers.entries()) {
        const timeout = init + delay;
        if (!timerValues || timeout < timerValues[0]) {
            timerValues = [timeout, callback, internalId];
        }
    }
    return timerValues;
};
```

**队列管理功能**:
- **优先级队列**: 按时间顺序管理定时器
- **高效查找**: 快速找到下一个执行的定时器
- **状态跟踪**: 跟踪定时器的执行状态
- **内存管理**: 高效的内存使用

### 3. 时间计算

```javascript
// 当前时间计算
const now = () => performance.now() + timeOffset;

// 帧延迟计算
let frameDelay = 1000 / 60; // 默认60fps
```

**时间计算功能**:
- **高精度时间**: 使用performance.now()获取高精度时间
- **时间偏移**: 支持时间偏移用于测试
- **帧率计算**: 根据帧率计算帧延迟
- **一致性**: 确保时间计算的一致性

## 使用场景

### 1. 基础时间控制测试

```javascript
import { 
    freezeTime, 
    advanceTime, 
    runAllTimers, 
    delay 
} from '@odoo/hoot-dom';

describe('基础时间控制测试', () => {
    test('定时器控制', async () => {
        let executed = false;
        
        // 冻结时间
        freezeTime();
        
        // 设置定时器
        setTimeout(() => {
            executed = true;
        }, 1000);
        
        // 验证定时器未执行
        expect(executed).toBe(false);
        
        // 推进时间
        advanceTime(1000);
        
        // 运行定时器
        runAllTimers();
        
        // 验证定时器已执行
        expect(executed).toBe(true);
    });
    
    test('延迟等待', async () => {
        const startTime = Date.now();
        
        // 延迟100毫秒
        await delay(100);
        
        const endTime = Date.now();
        const elapsed = endTime - startTime;
        
        // 验证延迟时间（允许一定误差）
        expect(elapsed).toBeGreaterThanOrEqual(90);
        expect(elapsed).toBeLessThan(150);
    });
});
```

### 2. 动画帧控制测试

```javascript
import { 
    animationFrame, 
    advanceFrame, 
    setFrameRate, 
    tick 
} from '@odoo/hoot-dom';

describe('动画帧控制测试', () => {
    test('动画帧等待', async () => {
        let frameExecuted = false;
        
        // 请求动画帧
        requestAnimationFrame(() => {
            frameExecuted = true;
        });
        
        // 验证帧未执行
        expect(frameExecuted).toBe(false);
        
        // 等待动画帧
        await animationFrame();
        
        // 验证帧已执行
        expect(frameExecuted).toBe(true);
    });
    
    test('帧率控制', async () => {
        // 设置30fps
        setFrameRate(30);
        
        let frameCount = 0;
        const frameCallback = () => {
            frameCount++;
            if (frameCount < 5) {
                requestAnimationFrame(frameCallback);
            }
        };
        
        requestAnimationFrame(frameCallback);
        
        // 推进5帧
        for (let i = 0; i < 5; i++) {
            await tick();
        }
        
        // 验证帧数
        expect(frameCount).toBe(5);
    });
    
    test('批量帧推进', async () => {
        let animationValue = 0;
        
        const animate = () => {
            animationValue += 10;
            if (animationValue < 100) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
        
        // 推进10帧
        advanceFrame(10);
        
        // 验证动画值
        expect(animationValue).toBe(100);
    });
});
```

### 3. 异步条件等待测试

```javascript
import { waitUntil, Deferred } from '@odoo/hoot-dom';

describe('异步条件等待测试', () => {
    test('条件等待', async () => {
        let counter = 0;
        
        // 启动计数器
        const interval = setInterval(() => {
            counter++;
        }, 100);
        
        // 等待计数器达到5
        await waitUntil(() => counter >= 5, { timeout: 1000 });
        
        // 清理定时器
        clearInterval(interval);
        
        // 验证计数器值
        expect(counter).toBeGreaterThanOrEqual(5);
    });
    
    test('延迟对象控制', async () => {
        const deferred = new Deferred();
        let result = null;
        
        // 异步操作
        deferred.promise.then(value => {
            result = value;
        });
        
        // 验证初始状态
        expect(result).toBe(null);
        
        // 解决延迟对象
        deferred.resolve('success');
        
        // 等待微任务
        await microTick();
        
        // 验证结果
        expect(result).toBe('success');
    });
    
    test('超时处理', async () => {
        let timeoutOccurred = false;
        
        try {
            // 等待永远不会满足的条件
            await waitUntil(() => false, { timeout: 100 });
        } catch (error) {
            timeoutOccurred = true;
            expect(error.message).toContain('timeout');
        }
        
        expect(timeoutOccurred).toBe(true);
    });
});
```

### 4. 复杂时间场景测试

```javascript
import { 
    freezeTime, 
    advanceTime, 
    runAllTimers, 
    cancelAllTimers,
    delay,
    animationFrame
} from '@odoo/hoot-dom';

describe('复杂时间场景测试', () => {
    test('多定时器协调', async () => {
        const results = [];
        
        // 冻结时间
        freezeTime();
        
        // 设置多个定时器
        setTimeout(() => results.push('timeout1'), 100);
        setTimeout(() => results.push('timeout2'), 200);
        setTimeout(() => results.push('timeout3'), 150);
        
        setInterval(() => results.push('interval'), 50);
        
        // 推进时间并运行定时器
        advanceTime(100);
        runAllTimers();
        expect(results).toEqual(['interval', 'interval', 'timeout1']);
        
        advanceTime(50);
        runAllTimers();
        expect(results).toEqual(['interval', 'interval', 'timeout1', 'interval', 'timeout3']);
        
        advanceTime(50);
        runAllTimers();
        expect(results).toEqual(['interval', 'interval', 'timeout1', 'interval', 'timeout3', 'interval', 'timeout2']);
    });
    
    test('定时器清理', async () => {
        let executed = false;
        
        // 设置定时器
        setTimeout(() => {
            executed = true;
        }, 1000);
        
        // 取消所有定时器
        cancelAllTimers();
        
        // 推进时间
        advanceTime(1000);
        runAllTimers();
        
        // 验证定时器未执行
        expect(executed).toBe(false);
    });
    
    test('动画和定时器混合', async () => {
        const events = [];
        
        // 设置定时器
        setTimeout(() => events.push('timeout'), 100);
        
        // 设置动画帧
        requestAnimationFrame(() => events.push('frame1'));
        requestAnimationFrame(() => events.push('frame2'));
        
        // 推进一帧
        await animationFrame();
        expect(events).toContain('frame1');
        
        // 推进时间
        advanceTime(100);
        runAllTimers();
        expect(events).toContain('timeout');
        
        // 推进另一帧
        await animationFrame();
        expect(events).toContain('frame2');
    });
});
```

### 5. 性能测试场景

```javascript
import { 
    freezeTime, 
    advanceTime, 
    runAllTimers,
    delay 
} from '@odoo/hoot-dom';

describe('性能测试场景', () => {
    test('大量定时器性能', async () => {
        const startTime = performance.now();
        const results = [];
        
        // 冻结时间
        freezeTime();
        
        // 创建大量定时器
        for (let i = 0; i < 1000; i++) {
            setTimeout(() => {
                results.push(i);
            }, Math.random() * 1000);
        }
        
        // 推进时间并运行所有定时器
        advanceTime(1000);
        runAllTimers();
        
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        // 验证所有定时器都执行了
        expect(results.length).toBe(1000);
        
        // 验证性能（应该很快完成）
        expect(duration).toBeLessThan(100);
    });
    
    test('内存使用测试', async () => {
        // 创建和清理大量定时器
        for (let round = 0; round < 10; round++) {
            const timers = [];
            
            // 创建定时器
            for (let i = 0; i < 100; i++) {
                const timer = setTimeout(() => {}, 1000);
                timers.push(timer);
            }
            
            // 清理定时器
            timers.forEach(timer => clearTimeout(timer));
        }
        
        // 验证没有内存泄漏（通过定时器数量）
        cancelAllTimers();
        
        // 这里可以添加内存使用检查
        expect(true).toBe(true); // 占位符断言
    });
});
```

## 技术特点

### 1. 精确时间控制
- 高精度时间计算
- 可预测的时间流逝
- 精确的定时器控制
- 帧级别的时间管理

### 2. 测试环境隔离
- 时间冻结机制
- 独立的定时器管理
- 可重置的时间状态
- 测试间的状态隔离

### 3. 异步操作支持
- Promise-based API
- 微任务处理
- 条件等待机制
- 超时控制

### 4. 性能优化
- 高效的定时器队列
- 最小化内存使用
- 快速的时间计算
- 批量操作支持

## 设计模式

### 1. 单例模式 (Singleton Pattern)
- 全局时间状态管理
- 统一的时间控制接口

### 2. 命令模式 (Command Pattern)
- 定时器操作的封装
- 可撤销的时间操作

### 3. 观察者模式 (Observer Pattern)
- 时间变化的通知
- 定时器事件的分发

## 注意事项

1. **时间一致性**: 确保测试中时间的一致性
2. **状态清理**: 测试后清理时间状态
3. **性能考虑**: 避免创建过多定时器
4. **异步处理**: 正确处理异步时间操作

## 扩展建议

1. **时间旅行**: 支持时间的前进和后退
2. **时间快照**: 支持时间状态的快照和恢复
3. **性能监控**: 监控时间操作的性能
4. **可视化调试**: 提供时间流的可视化调试
5. **并发控制**: 增强并发时间操作的控制

该时间助手模块为Hoot-DOM测试库提供了完整的时间控制能力，通过精确的时间管理和异步操作控制确保了测试的可预测性和可靠性。

# Events Helper - 事件模拟助手

## 概述

`events.js` 是 Hoot-DOM 测试库的事件模拟助手模块，提供了全面的用户交互事件模拟功能。该模块包含2505行代码，是DOM测试中用户交互模拟的核心组件，提供了点击、输入、拖拽、键盘操作、表单操作等功能，具备真实的事件序列模拟、异步操作支持、跨浏览器兼容等特性，为前端测试提供了完整的用户交互模拟能力。

## 文件信息
- **路径**: `/web/static/lib/hoot-dom/helpers/events.js`
- **行数**: 2505
- **模块**: `@web/../lib/hoot-dom/helpers/events`

## 依赖关系

```javascript
// 核心依赖
'@web/../lib/hoot-dom/hoot_dom_utils'    // DOM工具函数
'@web/../lib/hoot-dom/helpers/dom'       // DOM助手
```

## 类型定义

### 1. 事件相关类型

```javascript
/**
 * @typedef {keyof HTMLElementEventMap | keyof WindowEventMap} EventType
 * 
 * @typedef {{
 *  clientX: number; clientY: number;
 *  pageX: number; pageY: number;
 *  screenX: number; screenY: number;
 * }} EventPosition - 事件位置
 * 
 * @typedef {((ev: Event) => boolean) | EventType} EventListPredicate
 */
```

### 2. 交互选项类型

```javascript
/**
 * @typedef {EventOptions & {
 *  button?: number;           // 鼠标按钮
 *  position?: Side | Position; // 点击位置
 *  relative?: boolean;        // 相对位置
 * }} PointerOptions - 指针选项
 * 
 * @typedef {EventOptions & {
 *  confirm?: ConfirmAction;   // 确认动作
 *  composition?: boolean;     // 输入法组合
 *  instantly?: boolean;       // 立即输入
 * }} FillOptions - 填充选项
 * 
 * @typedef {string | string[]} KeyStrokes - 按键序列
 */
```

### 3. 拖拽助手类型

```javascript
/**
 * @typedef {{
 *  cancel: (options?: EventOptions) => Promise<EventList>;
 *  drop: (to?: AsyncTarget, options?: PointerOptions) => Promise<EventList>;
 *  moveTo: (to?: AsyncTarget, options?: PointerOptions) => Promise<DragHelpers>;
 * }} DragHelpers - 拖拽助手
 */
```

## 核心功能模块

### 1. 鼠标事件模拟

```javascript
// 基础点击事件
click(target, options)               // 单击
dblclick(target, options)            // 双击

// 指针事件
pointerDown(target, options)         // 指针按下
pointerUp(target, options)           // 指针释放

// 悬停事件
hover(target, options)               // 悬停
leave(target, options)               // 离开
```

**鼠标事件特点**:
- **真实事件序列**: 模拟真实的鼠标事件序列
- **位置精确**: 支持精确的点击位置控制
- **按钮支持**: 支持不同鼠标按钮的模拟
- **异步处理**: 完整的异步事件处理

### 2. 键盘事件模拟

```javascript
// 按键事件
keyDown(target, key, options)        // 按键按下
keyUp(target, key, options)          // 按键释放
press(target, keys, options)         // 按键序列

// 组合键支持
// 支持 Ctrl+C, Alt+Tab 等组合键
```

**键盘事件特点**:
- **按键序列**: 支持复杂的按键序列模拟
- **组合键**: 支持修饰键组合
- **输入法**: 支持输入法组合输入
- **特殊键**: 支持功能键和特殊键

### 3. 表单操作模拟

```javascript
// 输入操作
fill(target, value, options)         // 填充输入
clear(target, options)               // 清空输入
edit(target, value, options)         // 编辑内容

// 选择操作
check(target, options)               // 选中复选框
uncheck(target, options)             // 取消选中
select(target, value, options)       // 选择选项

// 文件操作
setInputFiles(target, files, options) // 设置文件输入
setInputRange(target, range, options) // 设置输入范围
```

**表单操作特点**:
- **类型感知**: 根据输入类型智能处理
- **验证触发**: 自动触发表单验证
- **事件完整**: 完整的表单事件序列
- **文件支持**: 支持文件输入模拟

### 4. 拖拽操作模拟

```javascript
// 拖拽操作
drag(source, options)                // 开始拖拽
// 返回拖拽助手对象，支持链式操作

// 拖拽助手方法
dragHelpers.moveTo(target, options)  // 移动到目标
dragHelpers.drop(target, options)    // 放置到目标
dragHelpers.cancel(options)          // 取消拖拽
```

**拖拽操作特点**:
- **链式操作**: 支持流畅的链式拖拽操作
- **中间状态**: 支持拖拽过程中的中间操作
- **取消机制**: 支持拖拽取消
- **精确控制**: 精确的拖拽路径控制

### 5. 窗口和滚动操作

```javascript
// 窗口操作
resize(target, dimensions, options)  // 调整窗口大小
unload(target, options)              // 卸载页面

// 滚动操作
scroll(target, position, options)    // 滚动操作
```

**窗口操作特点**:
- **尺寸控制**: 精确的窗口尺寸控制
- **滚动模拟**: 真实的滚动行为模拟
- **事件触发**: 完整的窗口事件触发
- **跨平台**: 跨平台的一致性行为

### 6. 事件监听和分发

```javascript
// 事件监听
on(target, eventType, handler, options) // 添加事件监听

// 手动事件分发
dispatch(target, eventType, options)     // 手动分发事件
```

**事件处理特点**:
- **灵活监听**: 灵活的事件监听机制
- **手动分发**: 支持手动事件分发
- **选项控制**: 丰富的事件选项控制
- **清理机制**: 自动的事件清理机制

## 使用场景

### 1. 基础交互测试

```javascript
import { click, fill, press, check } from '@odoo/hoot-dom';

describe('基础交互测试', () => {
    test('表单填写和提交', async () => {
        // 填写文本输入框
        await fill('input[name="username"]', 'testuser');
        await fill('input[name="password"]', 'password123');
        
        // 选中复选框
        await check('input[name="remember"]');
        
        // 点击提交按钮
        await click('button[type="submit"]');
        
        // 验证结果
        expect(queryText('.success-message')).toContain('登录成功');
    });
    
    test('键盘导航', async () => {
        const firstInput = queryOne('input');
        
        // 聚焦第一个输入框
        await click(firstInput);
        
        // 使用Tab键导航
        await press('Tab');
        
        // 验证焦点移动
        const activeElement = getActiveElement();
        expect(activeElement.tagName).toBe('INPUT');
    });
});
```

### 2. 复杂交互测试

```javascript
import { drag, hover, dblclick, keyDown, keyUp } from '@odoo/hoot-dom';

describe('复杂交互测试', () => {
    test('拖拽操作', async () => {
        const source = queryOne('.draggable-item');
        const target = queryOne('.drop-zone');
        
        // 执行拖拽操作
        const dragHelper = await drag(source);
        await dragHelper.moveTo(target);
        await dragHelper.drop();
        
        // 验证拖拽结果
        expect(queryOne('.drop-zone .draggable-item')).toBeTruthy();
    });
    
    test('悬停效果', async () => {
        const element = queryOne('.hover-trigger');
        
        // 悬停触发
        await hover(element);
        
        // 验证悬停效果
        expect(queryOne('.tooltip')).toBeTruthy();
        
        // 离开元素
        await leave(element);
        
        // 验证效果消失
        expect(queryOne('.tooltip')).toBeFalsy();
    });
    
    test('组合键操作', async () => {
        const textArea = queryOne('textarea');
        
        // 聚焦文本区域
        await click(textArea);
        
        // 输入文本
        await fill(textArea, 'Hello World');
        
        // 全选文本 (Ctrl+A)
        await keyDown('Control');
        await press('a');
        await keyUp('Control');
        
        // 复制文本 (Ctrl+C)
        await keyDown('Control');
        await press('c');
        await keyUp('Control');
        
        // 验证选择状态
        expect(textArea.selectionStart).toBe(0);
        expect(textArea.selectionEnd).toBe(textArea.value.length);
    });
});
```

### 3. 文件上传测试

```javascript
import { setInputFiles, click } from '@odoo/hoot-dom';

describe('文件上传测试', () => {
    test('单文件上传', async () => {
        const fileInput = queryOne('input[type="file"]');
        const file = new File(['test content'], 'test.txt', { type: 'text/plain' });
        
        // 设置文件
        await setInputFiles(fileInput, [file]);
        
        // 验证文件设置
        expect(fileInput.files.length).toBe(1);
        expect(fileInput.files[0].name).toBe('test.txt');
        
        // 触发上传
        await click('.upload-button');
        
        // 等待上传完成
        await waitFor('.upload-success');
    });
    
    test('多文件上传', async () => {
        const fileInput = queryOne('input[type="file"][multiple]');
        const files = [
            new File(['content 1'], 'file1.txt', { type: 'text/plain' }),
            new File(['content 2'], 'file2.txt', { type: 'text/plain' }),
            new File(['content 3'], 'file3.txt', { type: 'text/plain' })
        ];
        
        // 设置多个文件
        await setInputFiles(fileInput, files);
        
        // 验证文件数量
        expect(fileInput.files.length).toBe(3);
        
        // 验证文件列表显示
        const fileList = queryAll('.file-item');
        expect(fileList.length).toBe(3);
    });
});
```

### 4. 高级表单测试

```javascript
import { select, fill, clear, edit } from '@odoo/hoot-dom';

describe('高级表单测试', () => {
    test('下拉选择', async () => {
        const select = queryOne('select[name="country"]');
        
        // 选择选项
        await select(select, 'US');
        
        // 验证选择结果
        expect(queryValue(select)).toBe('US');
        
        // 验证选项文本
        const selectedOption = select.selectedOptions[0];
        expect(selectedOption.textContent).toBe('United States');
    });
    
    test('富文本编辑', async () => {
        const editor = queryOne('.rich-text-editor');
        
        // 清空编辑器
        await clear(editor);
        
        // 编辑内容
        await edit(editor, '<p>Hello <strong>World</strong></p>');
        
        // 验证HTML内容
        expect(editor.innerHTML).toContain('<strong>World</strong>');
    });
    
    test('数值输入范围', async () => {
        const rangeInput = queryOne('input[type="range"]');
        
        // 设置范围值
        await setInputRange(rangeInput, [20, 80]);
        
        // 验证范围设置
        expect(rangeInput.value).toBe('20');
        expect(rangeInput.max).toBe('80');
    });
});
```

### 5. 窗口和滚动测试

```javascript
import { resize, scroll, unload } from '@odoo/hoot-dom';

describe('窗口和滚动测试', () => {
    test('窗口调整', async () => {
        // 调整窗口大小
        await resize(window, { width: 1024, height: 768 });
        
        // 验证窗口尺寸
        expect(window.innerWidth).toBe(1024);
        expect(window.innerHeight).toBe(768);
        
        // 验证响应式变化
        const sidebar = queryOne('.sidebar');
        expect(isVisible(sidebar)).toBe(true);
    });
    
    test('滚动操作', async () => {
        const scrollContainer = queryOne('.scroll-container');
        
        // 滚动到指定位置
        await scroll(scrollContainer, { top: 100, left: 50 });
        
        // 验证滚动位置
        expect(scrollContainer.scrollTop).toBe(100);
        expect(scrollContainer.scrollLeft).toBe(50);
        
        // 滚动到元素
        const targetElement = queryOne('.target-element');
        await scroll(scrollContainer, targetElement);
        
        // 验证元素可见
        expect(isInViewPort(targetElement)).toBe(true);
    });
    
    test('页面卸载', async () => {
        let unloadTriggered = false;
        
        // 监听卸载事件
        window.addEventListener('beforeunload', () => {
            unloadTriggered = true;
        });
        
        // 触发卸载
        await unload(window);
        
        // 验证事件触发
        expect(unloadTriggered).toBe(true);
    });
});
```

## 技术特点

### 1. 真实事件模拟
- 完整的事件序列
- 浏览器原生事件
- 真实的用户交互
- 事件冒泡和捕获

### 2. 异步操作支持
- Promise-based API
- 异步事件处理
- 时序控制
- 并发安全

### 3. 跨浏览器兼容
- 统一的API接口
- 浏览器差异处理
- 兼容性检测
- 回退机制

### 4. 灵活的配置选项
- 丰富的事件选项
- 位置和时间控制
- 自定义事件属性
- 条件触发

## 设计模式

### 1. 命令模式 (Command Pattern)
- 事件操作的封装
- 可撤销的操作
- 操作队列管理

### 2. 策略模式 (Strategy Pattern)
- 不同事件类型的处理策略
- 浏览器兼容性策略
- 输入类型处理策略

### 3. 建造者模式 (Builder Pattern)
- 复杂事件的构建
- 拖拽操作的链式构建
- 事件序列的组装

## 注意事项

1. **事件时序**: 确保事件的正确时序和依赖关系
2. **异步处理**: 正确处理异步事件和等待
3. **内存管理**: 及时清理事件监听器
4. **浏览器差异**: 注意不同浏览器的事件行为差异

## 扩展建议

1. **触摸事件**: 添加移动设备触摸事件支持
2. **手势识别**: 支持复杂手势的模拟
3. **性能监控**: 监控事件处理性能
4. **录制回放**: 支持用户操作的录制和回放
5. **可视化调试**: 提供事件触发的可视化调试

该事件助手模块为Hoot-DOM测试库提供了完整的用户交互模拟能力，通过真实的事件序列和异步处理确保了测试的准确性和可靠性。

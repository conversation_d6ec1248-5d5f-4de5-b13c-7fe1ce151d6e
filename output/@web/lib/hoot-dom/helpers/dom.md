# DOM Helper - DOM操作助手

## 概述

`dom.js` 是 Hoot-DOM 测试库的核心DOM操作助手模块，提供了全面的DOM查询、操作和检测功能。该模块包含1915行代码，是整个测试框架的核心组件，提供了元素查询、状态检测、XML格式化、焦点管理、观察器等功能，具备强大的选择器解析、伪类支持、异步等待等特性，为DOM测试提供了完整的工具集合。

## 文件信息
- **路径**: `/web/static/lib/hoot-dom/helpers/dom.js`
- **行数**: 1915
- **模块**: `@web/../lib/hoot-dom/helpers/dom`

## 依赖关系

```javascript
// 核心依赖
'@web/../lib/hoot-dom/hoot_dom_utils'    // DOM工具函数
'@web/../lib/hoot-dom/helpers/time'      // 时间助手
```

## 类型定义

### 1. 基础类型

```javascript
/**
 * @typedef {number | [number, number] | {
 *  w?: number; h?: number; width?: number; height?: number;
 * }} Dimensions - 尺寸定义
 * 
 * @typedef {number | [number, number] | {
 *  x?: number; y?: number; left?: number; top?: number;
 *  clientX?: number; clientY?: number; pageX?: number; pageY?: number;
 *  screenX?: number; screenY?: number;
 * }} Position - 位置定义
 */
```

### 2. 查询选项

```javascript
/**
 * @typedef {{
 *  displayed?: boolean;    // 是否显示
 *  exact?: number;        // 精确数量
 *  root?: HTMLElement;    // 根元素
 *  viewPort?: boolean;    // 视口内
 *  visible?: boolean;     // 是否可见
 * }} QueryOptions - 查询选项
 * 
 * @typedef {{
 *  raw?: boolean;         // 原始文本
 * }} QueryTextOptions - 文本查询选项
 * 
 * @typedef {{
 *  trimPadding?: boolean; // 去除内边距
 * }} QueryRectOptions - 矩形查询选项
 */
```

### 3. 等待选项

```javascript
/**
 * @typedef {{
 *  message?: string | () => string;  // 错误消息
 *  timeout?: number;                 // 超时时间
 * }} WaitOptions - 等待选项
 */
```

## 核心功能模块

### 1. 元素查询功能

```javascript
// 基础查询函数
queryOne(selector, options)          // 查询单个元素
queryAll(selector, options)          // 查询所有元素
queryFirst(selector, options)        // 查询第一个元素

// 属性查询
queryAttribute(target, attribute)    // 查询属性值
queryAllAttributes(target, attribute) // 查询所有属性值

// 文本查询
queryText(target, options)           // 查询文本内容
queryAllTexts(target, options)       // 查询所有文本内容

// 值查询
queryValue(target)                   // 查询表单值
queryAllValues(target)               // 查询所有表单值

// 矩形查询
queryRect(target, options)           // 查询元素矩形
queryAllRects(target, options)       // 查询所有元素矩形

// 属性查询
queryAllProperties(target, property) // 查询所有属性
```

**查询功能特点**:
- **灵活选择器**: 支持CSS选择器和自定义伪类
- **条件过滤**: 支持可见性、显示状态等条件
- **批量操作**: 支持批量查询和操作
- **类型安全**: 完整的类型定义和检查

### 2. 元素状态检测

```javascript
// 可见性检测
isVisible(target)                    // 是否可见
isDisplayed(target)                  // 是否显示
isInViewPort(target)                 // 是否在视口内
isInDOM(target)                      // 是否在DOM中

// 交互性检测
isFocusable(target)                  // 是否可聚焦
isEditable(target)                   // 是否可编辑
isScrollable(target)                 // 是否可滚动

// 匹配检测
matches(target, selector)            // 是否匹配选择器
```

**检测功能特点**:
- **状态判断**: 全面的元素状态检测
- **交互检测**: 用户交互相关的状态判断
- **可见性判断**: 多层次的可见性检测
- **匹配验证**: 选择器匹配验证

### 3. 焦点管理

```javascript
// 焦点获取
getActiveElement()                   // 获取当前活动元素
getFocusableElements(options)        // 获取可聚焦元素列表

// 焦点导航
getNextFocusableElement(target, options)     // 获取下一个可聚焦元素
getPreviousFocusableElement(target, options) // 获取上一个可聚焦元素
```

**焦点管理特点**:
- **焦点跟踪**: 跟踪当前焦点状态
- **导航支持**: 支持焦点导航
- **可访问性**: 支持键盘导航和可访问性
- **选项配置**: 灵活的焦点选项配置

### 4. 异步等待功能

```javascript
// 元素等待
waitFor(selector, options)           // 等待元素出现
waitForNone(selector, options)       // 等待元素消失

// 通用等待
waitUntil(predicate, options)        // 等待条件满足
```

**等待功能特点**:
- **异步支持**: 完整的异步等待机制
- **超时控制**: 可配置的超时时间
- **条件等待**: 灵活的条件等待
- **错误处理**: 完善的错误处理和消息

### 5. DOM观察功能

```javascript
// DOM变化观察
observe(target, callback, options)   // 观察DOM变化
```

**观察功能特点**:
- **变化监听**: 监听DOM结构变化
- **回调机制**: 灵活的回调处理
- **性能优化**: 高效的观察机制
- **选择性监听**: 可配置的监听选项

### 6. XML格式化

```javascript
// XML/HTML格式化
formatXml(content, options)          // 格式化XML/HTML内容
```

**格式化功能特点**:
- **代码美化**: 格式化XML和HTML代码
- **缩进控制**: 可配置的缩进选项
- **类型支持**: 支持HTML和XML格式
- **文本处理**: 智能的文本节点处理

## 使用场景

### 1. 基础DOM查询测试

```javascript
import { queryOne, queryAll, queryText, isVisible } from '@odoo/hoot-dom';

describe('DOM查询测试', () => {
    test('基础元素查询', () => {
        // 查询单个元素
        const button = queryOne('.submit-button');
        expect(button).toBeTruthy();
        
        // 查询所有元素
        const inputs = queryAll('input[type="text"]');
        expect(inputs.length).toBeGreaterThan(0);
        
        // 查询文本内容
        const title = queryText('h1');
        expect(title).toContain('欢迎');
        
        // 检查可见性
        expect(isVisible(button)).toBe(true);
    });
    
    test('条件查询', () => {
        // 只查询可见元素
        const visibleButtons = queryAll('button', { visible: true });
        
        // 查询特定数量的元素
        const exactThree = queryAll('.item', { exact: 3 });
        expect(exactThree.length).toBe(3);
        
        // 在特定根元素下查询
        const form = queryOne('form');
        const formInputs = queryAll('input', { root: form });
    });
});
```

### 2. 异步等待测试

```javascript
import { waitFor, waitForNone, waitUntil } from '@odoo/hoot-dom';

describe('异步等待测试', () => {
    test('等待元素出现', async () => {
        // 触发异步加载
        clickLoadButton();
        
        // 等待加载完成
        await waitFor('.loading-complete', { timeout: 5000 });
        
        // 验证结果
        const result = queryText('.result');
        expect(result).toBeTruthy();
    });
    
    test('等待元素消失', async () => {
        // 触发删除操作
        clickDeleteButton();
        
        // 等待元素消失
        await waitForNone('.item-to-delete');
        
        // 验证删除成功
        const items = queryAll('.item');
        expect(items.length).toBe(0);
    });
    
    test('等待条件满足', async () => {
        // 等待计数器达到目标值
        await waitUntil(() => {
            const counter = queryText('.counter');
            return parseInt(counter) >= 10;
        });
        
        const finalCount = queryText('.counter');
        expect(parseInt(finalCount)).toBeGreaterThanOrEqual(10);
    });
});
```

### 3. 焦点管理测试

```javascript
import { 
    getActiveElement, 
    getFocusableElements, 
    getNextFocusableElement,
    isFocusable 
} from '@odoo/hoot-dom';

describe('焦点管理测试', () => {
    test('焦点状态检测', () => {
        const input = queryOne('input');
        
        // 检查是否可聚焦
        expect(isFocusable(input)).toBe(true);
        
        // 设置焦点
        input.focus();
        
        // 验证活动元素
        expect(getActiveElement()).toBe(input);
    });
    
    test('焦点导航', () => {
        const form = queryOne('form');
        const focusableElements = getFocusableElements({ root: form });
        
        expect(focusableElements.length).toBeGreaterThan(1);
        
        // 测试焦点导航
        const firstInput = focusableElements[0];
        const nextElement = getNextFocusableElement(firstInput);
        
        expect(nextElement).toBe(focusableElements[1]);
    });
});
```

### 4. DOM观察测试

```javascript
import { observe } from '@odoo/hoot-dom';

describe('DOM观察测试', () => {
    test('监听DOM变化', async () => {
        const container = queryOne('.container');
        let changeDetected = false;
        
        // 设置观察器
        const observer = observe(container, (mutations) => {
            changeDetected = true;
        });
        
        // 触发DOM变化
        const newElement = document.createElement('div');
        container.appendChild(newElement);
        
        // 等待变化检测
        await waitUntil(() => changeDetected);
        
        expect(changeDetected).toBe(true);
        
        // 清理观察器
        observer.disconnect();
    });
});
```

### 5. 复杂选择器测试

```javascript
import { queryAll, matches } from '@odoo/hoot-dom';

describe('复杂选择器测试', () => {
    test('自定义伪类选择器', () => {
        // 使用自定义伪类
        const visibleInputs = queryAll('input:visible');
        const editableElements = queryAll(':editable');
        
        expect(visibleInputs.length).toBeGreaterThan(0);
        expect(editableElements.length).toBeGreaterThan(0);
    });
    
    test('组合选择器', () => {
        // 复杂的组合选择器
        const complexSelector = 'form .input-group input[type="text"]:visible:not([disabled])';
        const elements = queryAll(complexSelector);
        
        // 验证每个元素都匹配条件
        elements.forEach(element => {
            expect(matches(element, 'input[type="text"]')).toBe(true);
            expect(isVisible(element)).toBe(true);
            expect(element.disabled).toBe(false);
        });
    });
});
```

## 技术特点

### 1. 强大的选择器支持
- CSS选择器完整支持
- 自定义伪类扩展
- 复杂组合选择器
- 性能优化的解析

### 2. 异步操作支持
- Promise-based API
- 超时控制机制
- 错误处理和恢复
- 条件等待支持

### 3. 状态检测全面
- 多层次可见性检测
- 交互性状态判断
- DOM结构检测
- 焦点状态管理

### 4. 性能优化
- 高效的DOM查询
- 智能缓存机制
- 批量操作支持
- 内存管理优化

## 设计模式

### 1. 查询构建器模式 (Query Builder Pattern)
- 灵活的查询条件构建
- 链式调用支持
- 条件组合优化

### 2. 观察者模式 (Observer Pattern)
- DOM变化监听
- 事件驱动架构
- 回调机制管理

### 3. 策略模式 (Strategy Pattern)
- 不同查询策略
- 条件检测策略
- 等待策略选择

## 注意事项

1. **性能考虑**: 避免频繁的DOM查询和操作
2. **异步处理**: 正确处理异步等待和超时
3. **内存管理**: 及时清理观察器和事件监听
4. **浏览器兼容**: 注意不同浏览器的行为差异

## 扩展建议

1. **自定义伪类**: 扩展更多的自定义伪类选择器
2. **性能监控**: 添加查询性能监控
3. **调试工具**: 增强调试和日志功能
4. **缓存优化**: 优化查询结果缓存
5. **类型增强**: 增强TypeScript类型定义

该DOM助手模块为Hoot-DOM测试库提供了完整的DOM操作能力，通过强大的选择器支持和异步等待机制确保了测试的可靠性和灵活性。

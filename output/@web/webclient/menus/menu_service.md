# Menu Service - 菜单服务

## 概述

`menu_service.js` 是 Odoo Web 客户端的菜单服务，提供了菜单数据管理和操作的核心功能。该模块包含95行代码，是一个核心服务，专门用于加载、缓存、查询和操作菜单数据，具备数据获取、树形结构构建、菜单选择、应用切换等特性，是Odoo Web菜单系统的核心服务。

## 文件信息
- **路径**: `/web/static/src/webclient/menus/menu_service.js`
- **行数**: 95
- **模块**: `@web/webclient/menus/menu_service`

## 依赖关系

```javascript
// 核心依赖
'@web/core/browser/browser'     // 浏览器服务
'@web/core/registry'            // 注册表系统
'@web/session'                  // 会话管理
```

## 核心常量

```javascript
const loadMenusUrl = `/web/webclient/load_menus`;
```

**URL配置**:
- **loadMenusUrl**: 菜单数据加载的API端点

## 核心函数

### 1. makeFetchLoadMenus - 菜单加载器工厂

```javascript
function makeFetchLoadMenus() {
    const cacheHashes = session.cache_hashes;
    let loadMenusHash = cacheHashes.load_menus || new Date().getTime().toString();
    return async function fetchLoadMenus(reload) {
        if (reload) {
            loadMenusHash = new Date().getTime().toString();
        } else if (odoo.loadMenusPromise) {
            return odoo.loadMenusPromise;
        }
        const res = await browser.fetch(`${loadMenusUrl}/${loadMenusHash}`);
        if (!res.ok) {
            throw new Error("Error while fetching menus");
        }
        return res.json();
    };
}
```

**加载器功能**:
- **缓存管理**: 使用哈希值管理菜单数据缓存
- **重载支持**: 支持强制重新加载菜单数据
- **Promise缓存**: 避免重复的网络请求
- **错误处理**: 完善的网络请求错误处理

### 2. makeMenus - 菜单对象工厂

```javascript
function makeMenus(env, menusData, fetchLoadMenus) {
    let currentAppId;
    
    function _getMenu(menuId) {
        return menusData[menuId];
    }
    
    function setCurrentMenu(menu) {
        menu = typeof menu === "number" ? _getMenu(menu) : menu;
        if (menu && menu.appID !== currentAppId) {
            currentAppId = menu.appID;
            env.bus.trigger("MENUS:APP-CHANGED");
        }
    }
    
    return {
        // 菜单操作方法
        getAll() {
            return Object.values(menusData);
        },
        getApps() {
            return this.getMenu("root").children.map((mid) => this.getMenu(mid));
        },
        getMenu: _getMenu,
        getCurrentApp() {
            if (!currentAppId) {
                return;
            }
            return this.getMenu(currentAppId);
        },
        getMenuAsTree(menuID) {
            const menu = this.getMenu(menuID);
            if (!menu.childrenTree) {
                menu.childrenTree = menu.children.map((mid) => this.getMenuAsTree(mid));
            }
            return menu;
        },
        async selectMenu(menu) {
            menu = typeof menu === "number" ? this.getMenu(menu) : menu;
            if (!menu.actionID) {
                return;
            }
            await env.services.action.doAction(menu.actionID, {
                clearBreadcrumbs: true,
                onActionReady: () => {
                    setCurrentMenu(menu);
                },
            });
        },
        setCurrentMenu,
        async reload() {
            if (fetchLoadMenus) {
                menusData = await fetchLoadMenus(true);
                env.bus.trigger("MENUS:APP-CHANGED");
            }
        },
    };
}
```

**菜单对象功能**:
- **数据查询**: 提供多种菜单数据查询方法
- **树形构建**: 动态构建菜单的树形结构
- **菜单选择**: 处理菜单选择和动作执行
- **状态管理**: 管理当前应用和菜单状态
- **事件通知**: 触发应用切换事件

## 服务定义

### 1. 菜单服务配置

```javascript
const menuService = {
    dependencies: ["action"],
    async start(env) {
        const fetchLoadMenus = makeFetchLoadMenus();
        const menusData = await fetchLoadMenus();
        return makeMenus(env, menusData, fetchLoadMenus);
    },
};
```

**服务特性**:
- **依赖管理**: 依赖动作服务
- **异步启动**: 异步加载菜单数据
- **工厂模式**: 使用工厂函数创建菜单对象
- **数据预加载**: 启动时预加载菜单数据

## 核心API

### 1. 数据查询API

```javascript
// 获取所有菜单
getAll()                        // 返回所有菜单项的数组

// 获取应用列表
getApps()                       // 返回所有应用的数组

// 获取特定菜单
getMenu(menuId)                 // 根据ID获取菜单项

// 获取当前应用
getCurrentApp()                 // 获取当前活动的应用
```

### 2. 树形结构API

```javascript
// 获取菜单树
getMenuAsTree(menuID)           // 获取指定菜单的树形结构
```

### 3. 操作API

```javascript
// 选择菜单
selectMenu(menu)                // 选择并执行菜单动作

// 设置当前菜单
setCurrentMenu(menu)            // 设置当前菜单状态

// 重新加载
reload()                        // 重新加载菜单数据
```

## 使用场景

### 1. 菜单管理器

```javascript
// 菜单管理器
class MenuManager {
    constructor(env) {
        this.env = env;
        this.menuService = env.services.menu;
        this.menuCache = new Map();
        this.menuHistory = [];
        this.setupManager();
    }
    
    setupManager() {
        // 监听应用切换事件
        this.env.bus.addEventListener("MENUS:APP-CHANGED", () => {
            this.onAppChanged();
        });
        
        // 初始化菜单数据
        this.initializeMenuData();
    }
    
    // 初始化菜单数据
    async initializeMenuData() {
        try {
            const allMenus = this.menuService.getAll();
            const apps = this.menuService.getApps();
            
            console.log(`Loaded ${allMenus.length} menus and ${apps.length} apps`);
            
            // 构建菜单索引
            this.buildMenuIndex(allMenus);
            
            // 分析菜单结构
            this.analyzeMenuStructure(apps);
            
        } catch (error) {
            console.error('Failed to initialize menu data:', error);
        }
    }
    
    // 构建菜单索引
    buildMenuIndex(menus) {
        this.menuIndex = {
            byId: new Map(),
            byXmlId: new Map(),
            byActionId: new Map(),
            byAppId: new Map()
        };
        
        for (const menu of menus) {
            // 按ID索引
            this.menuIndex.byId.set(menu.id, menu);
            
            // 按XML ID索引
            if (menu.xmlid) {
                this.menuIndex.byXmlId.set(menu.xmlid, menu);
            }
            
            // 按动作ID索引
            if (menu.actionID) {
                if (!this.menuIndex.byActionId.has(menu.actionID)) {
                    this.menuIndex.byActionId.set(menu.actionID, []);
                }
                this.menuIndex.byActionId.get(menu.actionID).push(menu);
            }
            
            // 按应用ID索引
            if (menu.appID) {
                if (!this.menuIndex.byAppId.has(menu.appID)) {
                    this.menuIndex.byAppId.set(menu.appID, []);
                }
                this.menuIndex.byAppId.get(menu.appID).push(menu);
            }
        }
    }
    
    // 分析菜单结构
    analyzeMenuStructure(apps) {
        this.menuStructure = {
            apps: apps.length,
            totalMenus: 0,
            maxDepth: 0,
            appDetails: []
        };
        
        for (const app of apps) {
            const appMenus = this.getAppMenus(app.id);
            const appTree = this.menuService.getMenuAsTree(app.id);
            const depth = this.calculateMenuDepth(appTree);
            
            const appDetail = {
                id: app.id,
                name: app.name,
                menuCount: appMenus.length,
                maxDepth: depth,
                hasSubmenus: appMenus.length > 1
            };
            
            this.menuStructure.appDetails.push(appDetail);
            this.menuStructure.totalMenus += appMenus.length;
            this.menuStructure.maxDepth = Math.max(this.menuStructure.maxDepth, depth);
        }
        
        console.log('Menu structure analysis:', this.menuStructure);
    }
    
    // 获取应用的所有菜单
    getAppMenus(appId) {
        return this.menuIndex.byAppId.get(appId) || [];
    }
    
    // 计算菜单深度
    calculateMenuDepth(menuTree, currentDepth = 0) {
        let maxDepth = currentDepth;
        
        if (menuTree.childrenTree && menuTree.childrenTree.length > 0) {
            for (const child of menuTree.childrenTree) {
                const childDepth = this.calculateMenuDepth(child, currentDepth + 1);
                maxDepth = Math.max(maxDepth, childDepth);
            }
        }
        
        return maxDepth;
    }
    
    // 搜索菜单
    searchMenus(query, options = {}) {
        const results = {
            apps: [],
            menuItems: [],
            exactMatches: [],
            fuzzyMatches: []
        };
        
        const searchTerm = query.toLowerCase();
        const allMenus = this.menuService.getAll();
        
        for (const menu of allMenus) {
            const menuName = (menu.name || '').toLowerCase();
            const menuXmlId = (menu.xmlid || '').toLowerCase();
            
            // 精确匹配
            if (menuName === searchTerm || menuXmlId === searchTerm) {
                results.exactMatches.push(menu);
            }
            // 包含匹配
            else if (menuName.includes(searchTerm) || menuXmlId.includes(searchTerm)) {
                if (menu.id === menu.appID) {
                    results.apps.push(menu);
                } else {
                    results.menuItems.push(menu);
                }
            }
            // 模糊匹配
            else if (this.fuzzyMatch(searchTerm, menuName) || this.fuzzyMatch(searchTerm, menuXmlId)) {
                results.fuzzyMatches.push(menu);
            }
        }
        
        // 按相关性排序
        this.sortSearchResults(results, searchTerm);
        
        return results;
    }
    
    // 模糊匹配
    fuzzyMatch(query, text) {
        if (!query || !text) return false;
        
        let queryIndex = 0;
        for (let i = 0; i < text.length && queryIndex < query.length; i++) {
            if (text[i] === query[queryIndex]) {
                queryIndex++;
            }
        }
        
        return queryIndex === query.length;
    }
    
    // 排序搜索结果
    sortSearchResults(results, searchTerm) {
        const sortFn = (a, b) => {
            const aName = (a.name || '').toLowerCase();
            const bName = (b.name || '').toLowerCase();
            
            // 优先显示以搜索词开头的结果
            const aStartsWith = aName.startsWith(searchTerm);
            const bStartsWith = bName.startsWith(searchTerm);
            
            if (aStartsWith && !bStartsWith) return -1;
            if (!aStartsWith && bStartsWith) return 1;
            
            // 然后按名称长度排序（较短的优先）
            return aName.length - bName.length;
        };
        
        results.apps.sort(sortFn);
        results.menuItems.sort(sortFn);
        results.exactMatches.sort(sortFn);
        results.fuzzyMatches.sort(sortFn);
    }
    
    // 导航到菜单
    async navigateToMenu(menuId, options = {}) {
        try {
            const menu = this.menuService.getMenu(menuId);
            if (!menu) {
                throw new Error(`Menu with ID ${menuId} not found`);
            }
            
            // 记录导航历史
            this.recordNavigation(menu);
            
            // 执行导航
            await this.menuService.selectMenu(menu);
            
            // 触发导航事件
            this.env.bus.trigger('MENU:NAVIGATED', {
                menu: menu,
                timestamp: Date.now(),
                options: options
            });
            
            return menu;
            
        } catch (error) {
            console.error('Navigation failed:', error);
            throw error;
        }
    }
    
    // 记录导航历史
    recordNavigation(menu) {
        // 移除重复项
        const existingIndex = this.menuHistory.findIndex(item => item.id === menu.id);
        if (existingIndex > -1) {
            this.menuHistory.splice(existingIndex, 1);
        }
        
        // 添加到历史记录开头
        this.menuHistory.unshift({
            id: menu.id,
            name: menu.name,
            appID: menu.appID,
            timestamp: Date.now()
        });
        
        // 限制历史记录长度
        this.menuHistory = this.menuHistory.slice(0, 50);
    }
    
    // 获取导航历史
    getNavigationHistory() {
        return this.menuHistory.slice();
    }
    
    // 获取面包屑导航
    getBreadcrumbs(menuId) {
        const breadcrumbs = [];
        let currentMenu = this.menuService.getMenu(menuId);
        
        while (currentMenu && currentMenu.id !== 'root') {
            breadcrumbs.unshift({
                id: currentMenu.id,
                name: currentMenu.name,
                actionID: currentMenu.actionID
            });
            
            // 查找父菜单
            const parentId = this.findParentMenu(currentMenu.id);
            currentMenu = parentId ? this.menuService.getMenu(parentId) : null;
        }
        
        return breadcrumbs;
    }
    
    // 查找父菜单
    findParentMenu(menuId) {
        const allMenus = this.menuService.getAll();
        
        for (const menu of allMenus) {
            if (menu.children && menu.children.includes(menuId)) {
                return menu.id;
            }
        }
        
        return null;
    }
    
    // 应用切换处理
    onAppChanged() {
        const currentApp = this.menuService.getCurrentApp();
        
        if (currentApp) {
            console.log('App changed to:', currentApp.name);
            
            // 更新应用状态
            this.currentAppId = currentApp.id;
            
            // 触发应用切换事件
            this.env.bus.trigger('MENU_MANAGER:APP_CHANGED', {
                app: currentApp,
                timestamp: Date.now()
            });
        }
    }
    
    // 重新加载菜单
    async reloadMenus() {
        try {
            await this.menuService.reload();
            
            // 重新初始化数据
            await this.initializeMenuData();
            
            console.log('Menus reloaded successfully');
            
        } catch (error) {
            console.error('Failed to reload menus:', error);
            throw error;
        }
    }
    
    // 获取菜单统计信息
    getMenuStatistics() {
        return {
            structure: this.menuStructure,
            cache: {
                size: this.menuCache.size,
                hits: this.cacheHits || 0,
                misses: this.cacheMisses || 0
            },
            navigation: {
                historySize: this.menuHistory.length,
                currentApp: this.currentAppId
            },
            index: {
                totalMenus: this.menuIndex.byId.size,
                appsCount: this.menuIndex.byAppId.size,
                actionsCount: this.menuIndex.byActionId.size
            }
        };
    }
}

// 使用示例
const menuManager = new MenuManager(env);

// 搜索菜单
const searchResults = menuManager.searchMenus('sales');
console.log('Search results:', searchResults);

// 导航到菜单
await menuManager.navigateToMenu(123);

// 获取面包屑
const breadcrumbs = menuManager.getBreadcrumbs(456);
console.log('Breadcrumbs:', breadcrumbs);

// 获取统计信息
const stats = menuManager.getMenuStatistics();
console.log('Menu statistics:', stats);
```

## 技术特点

### 1. 数据管理
- **缓存机制**: 使用哈希值管理菜单数据缓存
- **懒加载**: 树形结构的懒加载构建
- **数据索引**: 高效的菜单数据查询
- **状态管理**: 完整的菜单状态管理

### 2. 异步处理
- **Promise缓存**: 避免重复的网络请求
- **异步加载**: 异步加载菜单数据
- **错误处理**: 完善的异步错误处理
- **重载支持**: 支持菜单数据的重新加载

### 3. 事件驱动
- **应用切换**: 触发应用切换事件
- **状态通知**: 菜单状态变化通知
- **事件总线**: 使用事件总线进行通信
- **生命周期**: 完整的菜单生命周期管理

### 4. 服务架构
- **依赖注入**: 依赖动作服务
- **工厂模式**: 使用工厂函数创建对象
- **注册表**: 注册到服务注册表
- **标准接口**: 提供标准的服务接口

## 设计模式

### 1. 服务定位器模式 (Service Locator Pattern)
- **服务注册**: 将菜单服务注册到服务定位器
- **依赖管理**: 管理服务间的依赖关系
- **服务获取**: 通过服务定位器获取服务

### 2. 工厂模式 (Factory Pattern)
- **对象创建**: 使用工厂函数创建菜单对象
- **加载器创建**: 创建菜单数据加载器
- **配置封装**: 封装创建逻辑和配置

### 3. 单例模式 (Singleton Pattern)
- **服务实例**: 确保菜单服务的单一实例
- **数据共享**: 在整个应用中共享菜单数据
- **状态一致**: 保持菜单状态的一致性

### 4. 观察者模式 (Observer Pattern)
- **事件监听**: 监听和触发菜单相关事件
- **状态通知**: 通知菜单状态的变化
- **松耦合**: 实现组件间的松耦合通信

## 注意事项

1. **缓存管理**: 合理管理菜单数据的缓存
2. **性能优化**: 避免频繁的菜单数据查询
3. **错误处理**: 完善的网络请求错误处理
4. **内存管理**: 避免菜单数据的内存泄漏

## 扩展建议

1. **搜索功能**: 添加菜单搜索功能
2. **收藏功能**: 支持收藏常用菜单
3. **历史记录**: 记录菜单访问历史
4. **权限控制**: 集成菜单权限控制
5. **个性化**: 支持个性化的菜单配置

该菜单服务为Odoo Web客户端提供了完整的菜单数据管理功能，通过高效的数据结构和缓存机制确保了菜单系统的性能和可靠性。

# Menu Helpers - 菜单辅助工具

## 概述

`menu_helpers.js` 是 Odoo Web 客户端的菜单辅助工具模块，提供了菜单树处理和应用管理的核心功能。该模块包含91行代码，定义了菜单树遍历、应用和菜单项计算、应用排序等实用函数，具备树形结构处理、数据转换、排序算法等特性，是Odoo Web菜单系统的基础工具库。

## 文件信息
- **路径**: `/web/static/src/webclient/menus/menu_helpers.js`
- **行数**: 91
- **模块**: `@web/webclient/menus/menu_helpers`

## 依赖关系

```javascript
// 无外部依赖
// 纯函数工具库，不依赖其他模块
```

## 核心函数

### 1. traverseMenuTree - 菜单树遍历

```javascript
function traverseMenuTree(tree, cb, parents = []) {
    cb(tree, parents);
    tree.childrenTree.forEach((c) => traverseMenuTree(c, cb, parents.concat([tree])));
}
```

**遍历功能**:
- **深度优先**: 使用深度优先算法遍历菜单树
- **回调执行**: 为每个节点执行指定的回调函数
- **祖先跟踪**: 跟踪并传递节点的祖先路径
- **递归处理**: 递归处理所有子节点

**参数说明**:
- **tree**: 菜单树对象，由菜单服务导出
- **cb**: 回调函数，接收节点和祖先列表作为参数
- **parents**: 可选的祖先节点列表

### 2. computeAppsAndMenuItems - 计算应用和菜单项

```javascript
function computeAppsAndMenuItems(menuTree) {
    const apps = [];
    const menuItems = [];
    traverseMenuTree(menuTree, (menuItem, parents) => {
        if (!menuItem.id || !menuItem.actionID) {
            return;
        }
        const isApp = menuItem.id === menuItem.appID;
        const item = {
            parents: parents
                .slice(1)
                .map((p) => p.name)
                .join(" / "),
            label: menuItem.name,
            id: menuItem.id,
            xmlid: menuItem.xmlid,
            actionID: menuItem.actionID,
            href: `/odoo/${menuItem.actionPath || "action-" + menuItem.actionID}`,
            appID: menuItem.appID,
        };

        if (isApp) {
            // 处理应用图标
            if (menuItem.webIconData) {
                item.webIconData = menuItem.webIconData;
            } else {
                const [iconClass, color, backgroundColor] = (menuItem.webIcon || "").split(",");
                if (backgroundColor !== undefined) {
                    item.webIcon = { iconClass, color, backgroundColor };
                } else {
                    item.webIconData = "/web/static/img/default_icon_app.png";
                }
            }
            apps.push(item);
        } else {
            item.menuID = parents[1].id;
            menuItems.push(item);
        }
    });
    return { apps, menuItems };
}
```

**计算功能**:
- **应用识别**: 识别哪些菜单项是应用（appID === id）
- **数据转换**: 将菜单树数据转换为HomeMenu所需格式
- **图标处理**: 处理应用图标的不同格式
- **路径生成**: 生成菜单项的访问路径

**返回数据结构**:
```javascript
{
    apps: [
        {
            parents: "父级路径",
            label: "应用名称",
            id: "菜单ID",
            xmlid: "XML ID",
            actionID: "动作ID",
            href: "访问链接",
            appID: "应用ID",
            webIcon: { iconClass, color, backgroundColor } // 或 webIconData
        }
    ],
    menuItems: [
        {
            parents: "父级路径",
            label: "菜单项名称",
            id: "菜单ID",
            xmlid: "XML ID",
            actionID: "动作ID",
            href: "访问链接",
            appID: "所属应用ID",
            menuID: "父菜单ID"
        }
    ]
}
```

### 3. reorderApps - 应用重排序

```javascript
function reorderApps(apps, order) {
    apps.sort((a, b) => {
        const aIndex = order.indexOf(a.xmlid);
        const bIndex = order.indexOf(b.xmlid);
        if (aIndex === -1 && bIndex === -1) {
            // 如果两个项目都不在排序列表中，保持原始顺序
            return apps.indexOf(a) - apps.indexOf(b);
        }
        // 未找到的项目总是排在找到的项目之前
        if (aIndex === -1) {
            return -1;
        }
        if (bIndex === -1) {
            return 1;
        }
        return aIndex - bIndex; // 按排序数组排序
    });
}
```

**排序功能**:
- **自定义排序**: 根据指定的XML ID顺序重新排列应用
- **缺失处理**: 未在排序列表中的应用排在前面
- **原序保持**: 相同优先级的应用保持原始顺序
- **就地排序**: 直接修改原数组，不返回新数组

## 使用场景

### 1. 菜单数据处理器

```javascript
// 菜单数据处理器
class MenuDataProcessor {
    constructor() {
        this.menuCache = new Map();
        this.appCache = new Map();
        this.menuItemCache = new Map();
    }

    // 处理菜单树数据
    processMenuTree(menuTree, options = {}) {
        const cacheKey = this.generateCacheKey(menuTree, options);

        if (this.menuCache.has(cacheKey)) {
            return this.menuCache.get(cacheKey);
        }

        const result = {
            apps: [],
            menuItems: [],
            statistics: {
                totalNodes: 0,
                appCount: 0,
                menuItemCount: 0,
                maxDepth: 0
            }
        };

        // 使用菜单辅助函数计算应用和菜单项
        const { apps, menuItems } = computeAppsAndMenuItems(menuTree);
        result.apps = apps;
        result.menuItems = menuItems;

        // 计算统计信息
        this.calculateStatistics(menuTree, result.statistics);

        // 应用过滤器
        if (options.filter) {
            result.apps = this.filterApps(result.apps, options.filter);
            result.menuItems = this.filterMenuItems(result.menuItems, options.filter);
        }

        // 应用排序
        if (options.appOrder) {
            reorderApps(result.apps, options.appOrder);
        }

        // 缓存结果
        this.menuCache.set(cacheKey, result);

        return result;
    }

    // 计算菜单树统计信息
    calculateStatistics(menuTree, stats) {
        let currentDepth = 0;

        traverseMenuTree(menuTree, (node, parents) => {
            stats.totalNodes++;
            currentDepth = parents.length;
            stats.maxDepth = Math.max(stats.maxDepth, currentDepth);

            if (node.id && node.actionID) {
                if (node.id === node.appID) {
                    stats.appCount++;
                } else {
                    stats.menuItemCount++;
                }
            }
        });
    }

    // 过滤应用
    filterApps(apps, filter) {
        return apps.filter(app => {
            if (filter.search) {
                const searchTerm = filter.search.toLowerCase();
                return app.label.toLowerCase().includes(searchTerm) ||
                       app.parents.toLowerCase().includes(searchTerm);
            }

            if (filter.categories && filter.categories.length > 0) {
                return filter.categories.some(category =>
                    app.parents.includes(category)
                );
            }

            if (filter.xmlids && filter.xmlids.length > 0) {
                return filter.xmlids.includes(app.xmlid);
            }

            return true;
        });
    }

    // 过滤菜单项
    filterMenuItems(menuItems, filter) {
        return menuItems.filter(item => {
            if (filter.appID) {
                return item.appID === filter.appID;
            }

            if (filter.search) {
                const searchTerm = filter.search.toLowerCase();
                return item.label.toLowerCase().includes(searchTerm) ||
                       item.parents.toLowerCase().includes(searchTerm);
            }

            return true;
        });
    }

    // 生成缓存键
    generateCacheKey(menuTree, options) {
        const treeHash = this.hashObject(menuTree);
        const optionsHash = this.hashObject(options);
        return `${treeHash}_${optionsHash}`;
    }

    // 对象哈希
    hashObject(obj) {
        const str = JSON.stringify(obj);
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(36);
    }

    // 获取应用详情
    getAppDetails(appId) {
        if (this.appCache.has(appId)) {
            return this.appCache.get(appId);
        }

        // 从缓存的菜单数据中查找应用详情
        for (const [cacheKey, menuData] of this.menuCache) {
            const app = menuData.apps.find(a => a.id === appId);
            if (app) {
                const details = {
                    ...app,
                    menuItems: menuData.menuItems.filter(item => item.appID === appId),
                    totalMenuItems: menuData.menuItems.filter(item => item.appID === appId).length
                };
                this.appCache.set(appId, details);
                return details;
            }
        }

        return null;
    }

    // 获取菜单项详情
    getMenuItemDetails(menuItemId) {
        if (this.menuItemCache.has(menuItemId)) {
            return this.menuItemCache.get(menuItemId);
        }

        // 从缓存的菜单数据中查找菜单项详情
        for (const [cacheKey, menuData] of this.menuCache) {
            const menuItem = menuData.menuItems.find(item => item.id === menuItemId);
            if (menuItem) {
                const app = menuData.apps.find(a => a.id === menuItem.appID);
                const details = {
                    ...menuItem,
                    app: app,
                    siblings: menuData.menuItems.filter(item =>
                        item.appID === menuItem.appID && item.menuID === menuItem.menuID
                    )
                };
                this.menuItemCache.set(menuItemId, details);
                return details;
            }
        }

        return null;
    }

    // 清理缓存
    clearCache() {
        this.menuCache.clear();
        this.appCache.clear();
        this.menuItemCache.clear();
    }

    // 获取缓存统计
    getCacheStats() {
        return {
            menuCacheSize: this.menuCache.size,
            appCacheSize: this.appCache.size,
            menuItemCacheSize: this.menuItemCache.size
        };
    }
}

// 使用示例
const processor = new MenuDataProcessor();

// 处理菜单树
const result = processor.processMenuTree(menuTree, {
    filter: {
        search: 'sales',
        categories: ['Sales', 'CRM']
    },
    appOrder: ['sale.menu_sale_root', 'crm.crm_menu_root']
});

console.log('Processed menu data:', result);
console.log('Apps found:', result.apps.length);
console.log('Menu items found:', result.menuItems.length);
```
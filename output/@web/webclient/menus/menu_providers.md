# Menu Providers - 菜单提供器

## 概述

`menu_providers.js` 是 Odoo Web 客户端的菜单提供器模块，提供了命令面板中的菜单搜索和访问功能。该模块包含85行代码，定义了应用图标命令组件和菜单命令提供器，具备模糊搜索、图标处理、命令注册等特性，是Odoo Web命令面板系统中菜单功能的核心实现。

## 文件信息
- **路径**: `/web/static/src/webclient/menus/menu_providers.js`
- **行数**: 85
- **模块**: `@web/webclient/menus/menu_providers`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'                   // 国际化翻译
'@web/core/registry'                           // 注册表系统
'@web/core/utils/search'                       // 搜索工具
'@web/webclient/menus/menu_helpers'            // 菜单辅助工具
'@web/core/commands/command_palette'           // 命令面板
'@odoo/owl'                                    // OWL框架
```

## 核心组件

### 1. AppIconCommand 组件

```javascript
class AppIconCommand extends Component {
    static template = "web.AppIconCommand";
    static props = {
        webIconData: { type: String, optional: true },
        webIcon: { type: Object, optional: true },
        ...DefaultCommandItem.props,
    };
}
```

**组件特性**:
- **应用图标**: 专门用于显示应用图标的命令项
- **图标支持**: 支持webIconData和webIcon两种图标格式
- **继承属性**: 继承DefaultCommandItem的所有属性
- **模板驱动**: 使用专门的模板渲染应用图标

## 注册表配置

### 1. 命令分类注册

```javascript
const commandCategoryRegistry = registry.category("command_categories");
commandCategoryRegistry.add("apps", { namespace: "/" }, { sequence: 10 });
commandCategoryRegistry.add("menu_items", { namespace: "/" }, { sequence: 20 });
```

**分类配置**:
- **apps**: 应用分类，序列号10，优先级较高
- **menu_items**: 菜单项分类，序列号20，优先级较低
- **namespace**: 都使用根命名空间"/"

### 2. 命令设置注册

```javascript
const commandSetupRegistry = registry.category("command_setup");
commandSetupRegistry.add("/", {
    emptyMessage: _t("No menu found"),
    name: _t("menus"),
    placeholder: _t("Search for a menu..."),
});
```

**设置配置**:
- **emptyMessage**: 无结果时的提示信息
- **name**: 命令提供器的名称
- **placeholder**: 搜索框的占位符文本

## 核心功能

### 1. 菜单命令提供器

```javascript
const commandProviderRegistry = registry.category("command_provider");
commandProviderRegistry.add("menu", {
    namespace: "/",
    async provide(env, options) {
        const result = [];
        const menuService = env.services.menu;
        let { apps, menuItems } = computeAppsAndMenuItems(menuService.getMenuAsTree("root"));
        
        // 搜索处理
        if (options.searchValue !== "") {
            apps = fuzzyLookup(options.searchValue, apps, (menu) => menu.label);
            
            fuzzyLookup(options.searchValue, menuItems, (menu) =>
                (menu.parents + " / " + menu.label).split("/").reverse().join("/")
            ).forEach((menu) => {
                result.push({
                    action() {
                        menuService.selectMenu(menu);
                    },
                    category: "menu_items",
                    name: menu.parents + " / " + menu.label,
                    href: menu.href || `#menu_id=${menu.id}&amp;action_id=${menu.actionID}`,
                });
            });
        }
        
        // 应用处理
        apps.forEach((menu) => {
            const props = {};
            if (menu.webIconData) {
                const prefix = menu.webIconData.startsWith("P")
                    ? "data:image/svg+xml;base64,"
                    : "data:image/png;base64,";
                props.webIconData = menu.webIconData.startsWith("data:image")
                    ? menu.webIconData
                    : prefix + menu.webIconData.replace(/\s/g, "");
            } else {
                props.webIcon = menu.webIcon;
            }
            result.push({
                Component: AppIconCommand,
                action() {
                    menuService.selectMenu(menu);
                },
                category: "apps",
                name: menu.label,
                href: menu.href || `#menu_id=${menu.id}&amp;action_id=${menu.actionID}`,
                props,
            });
        });
        
        return result;
    },
});
```

**提供器功能**:
- **菜单获取**: 从菜单服务获取完整的菜单树
- **数据转换**: 使用菜单辅助工具转换数据格式
- **模糊搜索**: 对应用和菜单项进行模糊搜索
- **图标处理**: 处理不同格式的应用图标
- **命令生成**: 生成可执行的命令项

## 使用场景

### 1. 扩展的菜单命令提供器

```javascript
// 扩展的菜单命令提供器
class ExtendedMenuCommandProvider {
    constructor(env) {
        this.env = env;
        this.menuService = env.services.menu;
        this.searchHistory = [];
        this.favoriteMenus = this.loadFavoriteMenus();
        this.recentMenus = this.loadRecentMenus();
        this.setupProvider();
    }
    
    setupProvider() {
        // 注册扩展的命令提供器
        registry.category("command_provider").add("extended_menu", {
            namespace: "/",
            provide: this.provide.bind(this)
        });
    }
    
    async provide(env, options) {
        const result = [];
        const searchValue = options.searchValue || "";
        
        // 获取菜单数据
        const { apps, menuItems } = computeAppsAndMenuItems(
            this.menuService.getMenuAsTree("root")
        );
        
        // 如果没有搜索值，显示收藏和最近使用的菜单
        if (searchValue === "") {
            this.addFavoriteMenus(result, apps, menuItems);
            this.addRecentMenus(result, apps, menuItems);
        } else {
            // 执行搜索
            this.addSearchResults(result, apps, menuItems, searchValue);
            
            // 记录搜索历史
            this.recordSearchHistory(searchValue);
        }
        
        // 添加快捷操作
        this.addQuickActions(result, searchValue);
        
        return result;
    }
    
    // 添加收藏菜单
    addFavoriteMenus(result, apps, menuItems) {
        const favoriteApps = apps.filter(app => 
            this.favoriteMenus.includes(app.id)
        );
        
        const favoriteMenuItems = menuItems.filter(item => 
            this.favoriteMenus.includes(item.id)
        );
        
        // 添加收藏应用
        favoriteApps.forEach(app => {
            result.push(this.createAppCommand(app, "⭐ " + app.label, "favorites"));
        });
        
        // 添加收藏菜单项
        favoriteMenuItems.forEach(item => {
            result.push(this.createMenuItemCommand(item, "⭐ " + item.label, "favorites"));
        });
    }
    
    // 添加最近使用的菜单
    addRecentMenus(result, apps, menuItems) {
        const recentApps = apps.filter(app => 
            this.recentMenus.includes(app.id)
        ).sort((a, b) => {
            const aIndex = this.recentMenus.indexOf(a.id);
            const bIndex = this.recentMenus.indexOf(b.id);
            return aIndex - bIndex;
        });
        
        const recentMenuItems = menuItems.filter(item => 
            this.recentMenus.includes(item.id)
        ).sort((a, b) => {
            const aIndex = this.recentMenus.indexOf(a.id);
            const bIndex = this.recentMenus.indexOf(b.id);
            return aIndex - bIndex;
        });
        
        // 添加最近应用
        recentApps.slice(0, 5).forEach(app => {
            result.push(this.createAppCommand(app, "🕒 " + app.label, "recent"));
        });
        
        // 添加最近菜单项
        recentMenuItems.slice(0, 5).forEach(item => {
            result.push(this.createMenuItemCommand(item, "🕒 " + item.label, "recent"));
        });
    }
    
    // 添加搜索结果
    addSearchResults(result, apps, menuItems, searchValue) {
        // 搜索应用
        const matchedApps = fuzzyLookup(searchValue, apps, (app) => app.label);
        matchedApps.forEach(app => {
            result.push(this.createAppCommand(app, app.label, "apps"));
        });
        
        // 搜索菜单项
        const matchedMenuItems = fuzzyLookup(searchValue, menuItems, (item) =>
            (item.parents + " / " + item.label).split("/").reverse().join("/")
        );
        matchedMenuItems.forEach(item => {
            result.push(this.createMenuItemCommand(item, item.parents + " / " + item.label, "menu_items"));
        });
        
        // 搜索XML ID
        const xmlIdMatches = [...apps, ...menuItems].filter(item =>
            item.xmlid && item.xmlid.toLowerCase().includes(searchValue.toLowerCase())
        );
        xmlIdMatches.forEach(item => {
            const isApp = apps.includes(item);
            const command = isApp ? 
                this.createAppCommand(item, `${item.label} (${item.xmlid})`, "xmlid") :
                this.createMenuItemCommand(item, `${item.label} (${item.xmlid})`, "xmlid");
            result.push(command);
        });
    }
    
    // 添加快捷操作
    addQuickActions(result, searchValue) {
        // 添加菜单管理操作
        if (searchValue.includes("manage") || searchValue.includes("admin")) {
            result.push({
                action: () => this.openMenuManager(),
                category: "actions",
                name: "🔧 Manage Menus",
                href: "#action=menu_manager"
            });
        }
        
        // 添加收藏管理操作
        if (searchValue.includes("favorite") || searchValue.includes("star")) {
            result.push({
                action: () => this.openFavoriteManager(),
                category: "actions",
                name: "⭐ Manage Favorites",
                href: "#action=favorite_manager"
            });
        }
        
        // 添加搜索历史操作
        if (searchValue.includes("history")) {
            result.push({
                action: () => this.showSearchHistory(),
                category: "actions",
                name: "📜 Search History",
                href: "#action=search_history"
            });
        }
    }
    
    // 创建应用命令
    createAppCommand(app, displayName, category) {
        const props = this.processAppIcon(app);
        
        return {
            Component: AppIconCommand,
            action: () => {
                this.menuService.selectMenu(app);
                this.recordMenuUsage(app.id);
            },
            category: category,
            name: displayName,
            href: app.href || `#menu_id=${app.id}&action_id=${app.actionID}`,
            props: props,
            contextActions: [
                {
                    name: "Add to Favorites",
                    action: () => this.toggleFavorite(app.id)
                },
                {
                    name: "Copy Link",
                    action: () => this.copyMenuLink(app)
                }
            ]
        };
    }
    
    // 创建菜单项命令
    createMenuItemCommand(item, displayName, category) {
        return {
            action: () => {
                this.menuService.selectMenu(item);
                this.recordMenuUsage(item.id);
            },
            category: category,
            name: displayName,
            href: item.href || `#menu_id=${item.id}&action_id=${item.actionID}`,
            contextActions: [
                {
                    name: "Add to Favorites",
                    action: () => this.toggleFavorite(item.id)
                },
                {
                    name: "Copy Link",
                    action: () => this.copyMenuLink(item)
                },
                {
                    name: "Show in App",
                    action: () => this.showInApp(item)
                }
            ]
        };
    }
    
    // 处理应用图标
    processAppIcon(app) {
        const props = {};
        
        if (app.webIconData) {
            const prefix = app.webIconData.startsWith("P")
                ? "data:image/svg+xml;base64,"
                : "data:image/png;base64,";
            props.webIconData = app.webIconData.startsWith("data:image")
                ? app.webIconData
                : prefix + app.webIconData.replace(/\s/g, "");
        } else if (app.webIcon) {
            props.webIcon = app.webIcon;
        } else {
            props.webIconData = "/web/static/img/default_icon_app.png";
        }
        
        return props;
    }
    
    // 记录菜单使用
    recordMenuUsage(menuId) {
        // 更新最近使用列表
        const index = this.recentMenus.indexOf(menuId);
        if (index > -1) {
            this.recentMenus.splice(index, 1);
        }
        this.recentMenus.unshift(menuId);
        this.recentMenus = this.recentMenus.slice(0, 20); // 保留最近20个
        
        this.saveRecentMenus();
    }
    
    // 切换收藏状态
    toggleFavorite(menuId) {
        const index = this.favoriteMenus.indexOf(menuId);
        if (index > -1) {
            this.favoriteMenus.splice(index, 1);
        } else {
            this.favoriteMenus.push(menuId);
        }
        
        this.saveFavoriteMenus();
    }
    
    // 复制菜单链接
    async copyMenuLink(menu) {
        try {
            const url = window.location.origin + menu.href;
            await navigator.clipboard.writeText(url);
            this.env.services.notification.add("Link copied to clipboard", {
                type: "success"
            });
        } catch (error) {
            console.error("Failed to copy link:", error);
        }
    }
    
    // 在应用中显示
    showInApp(menuItem) {
        // 首先导航到应用
        const apps = computeAppsAndMenuItems(
            this.menuService.getMenuAsTree("root")
        ).apps;
        
        const app = apps.find(a => a.id === menuItem.appID);
        if (app) {
            this.menuService.selectMenu(app);
            // 然后选择菜单项
            setTimeout(() => {
                this.menuService.selectMenu(menuItem);
            }, 100);
        }
    }
    
    // 记录搜索历史
    recordSearchHistory(searchValue) {
        if (searchValue.length < 2) return;
        
        const index = this.searchHistory.indexOf(searchValue);
        if (index > -1) {
            this.searchHistory.splice(index, 1);
        }
        this.searchHistory.unshift(searchValue);
        this.searchHistory = this.searchHistory.slice(0, 50); // 保留最近50个
        
        this.saveSearchHistory();
    }
    
    // 加载收藏菜单
    loadFavoriteMenus() {
        try {
            const favorites = localStorage.getItem('odoo_favorite_menus');
            return favorites ? JSON.parse(favorites) : [];
        } catch (error) {
            return [];
        }
    }
    
    // 保存收藏菜单
    saveFavoriteMenus() {
        try {
            localStorage.setItem('odoo_favorite_menus', JSON.stringify(this.favoriteMenus));
        } catch (error) {
            console.error('Failed to save favorite menus:', error);
        }
    }
    
    // 加载最近菜单
    loadRecentMenus() {
        try {
            const recent = localStorage.getItem('odoo_recent_menus');
            return recent ? JSON.parse(recent) : [];
        } catch (error) {
            return [];
        }
    }
    
    // 保存最近菜单
    saveRecentMenus() {
        try {
            localStorage.setItem('odoo_recent_menus', JSON.stringify(this.recentMenus));
        } catch (error) {
            console.error('Failed to save recent menus:', error);
        }
    }
    
    // 保存搜索历史
    saveSearchHistory() {
        try {
            localStorage.setItem('odoo_search_history', JSON.stringify(this.searchHistory));
        } catch (error) {
            console.error('Failed to save search history:', error);
        }
    }
    
    // 打开菜单管理器
    async openMenuManager() {
        await this.env.services.action.doAction({
            type: 'ir.actions.client',
            tag: 'menu_manager',
            target: 'new'
        });
    }
    
    // 打开收藏管理器
    async openFavoriteManager() {
        await this.env.services.action.doAction({
            type: 'ir.actions.client',
            tag: 'favorite_manager',
            target: 'new'
        });
    }
    
    // 显示搜索历史
    showSearchHistory() {
        console.log('Search History:', this.searchHistory);
        // 可以显示在命令面板中或打开专门的对话框
    }
}

// 使用示例
const extendedProvider = new ExtendedMenuCommandProvider(env);
```

## 技术特点

### 1. 命令面板集成
- **注册表管理**: 使用注册表管理命令分类和提供器
- **模糊搜索**: 集成fuzzyLookup进行智能搜索
- **分类组织**: 将命令按应用和菜单项分类
- **国际化**: 支持多语言界面

### 2. 图标处理
- **多格式支持**: 支持webIconData和webIcon两种格式
- **Base64处理**: 智能处理Base64编码的图标数据
- **前缀检测**: 根据数据类型自动添加正确的前缀
- **默认图标**: 提供默认图标作为后备方案

### 3. 搜索优化
- **反向搜索**: 对菜单项使用反向路径搜索
- **标签匹配**: 基于菜单标签进行模糊匹配
- **路径搜索**: 包含父级路径的完整搜索
- **性能优化**: 只在有搜索值时执行搜索

### 4. 用户体验
- **即时搜索**: 实时响应用户输入
- **快捷访问**: 提供快捷的菜单访问方式
- **视觉反馈**: 清晰的视觉分类和图标显示
- **键盘导航**: 支持键盘导航操作

## 设计模式

### 1. 提供器模式 (Provider Pattern)
- **命令提供**: 为命令面板提供菜单相关命令
- **数据转换**: 将菜单数据转换为命令格式
- **异步处理**: 支持异步的命令生成

### 2. 注册表模式 (Registry Pattern)
- **分类注册**: 注册命令分类到注册表
- **提供器注册**: 注册命令提供器
- **配置注册**: 注册命令面板配置

### 3. 组件模式 (Component Pattern)
- **专用组件**: 为应用图标创建专用组件
- **属性传递**: 通过属性传递图标数据
- **模板渲染**: 使用模板渲染组件

## 注意事项

1. **性能考虑**: 避免在搜索时处理过多数据
2. **图标处理**: 正确处理不同格式的图标数据
3. **搜索准确性**: 确保搜索结果的相关性
4. **用户体验**: 提供清晰的分类和视觉反馈

## 扩展建议

1. **搜索历史**: 记录和显示用户搜索历史
2. **收藏功能**: 支持收藏常用菜单
3. **使用统计**: 记录菜单使用频率
4. **个性化**: 根据用户习惯个性化搜索结果
5. **快捷键**: 支持自定义快捷键访问

该菜单提供器模块为Odoo Web客户端的命令面板提供了强大的菜单搜索和访问功能，通过模糊搜索和智能分类确保了用户能够快速找到并访问所需的菜单项。

# NavBar - 导航栏

## 概述

`navbar.js` 是 Odoo Web 客户端的导航栏组件，提供了应用程序的主要导航界面和功能。该模块包含241行代码，包含MenuDropdown和NavBar两个组件，专门用于显示应用菜单、系统托盘、应用切换等导航功能，具备响应式适配、菜单管理、触摸手势、错误处理等特性，是Odoo Web客户端中用户界面导航的核心组件。

## 文件信息
- **路径**: `/web/static/src/webclient/navbar/navbar.js`
- **行数**: 241
- **模块**: `@web/webclient/navbar/navbar`

## 依赖关系

```javascript
// 核心依赖
'@web/core/dropdown/dropdown'           // 下拉菜单组件
'@web/core/dropdown/dropdown_item'      // 下拉菜单项
'@web/core/dropdown/dropdown_group'     // 下拉菜单组
'@web/core/transition'                  // 过渡动画
'@web/core/utils/hooks'                 // 工具钩子
'@web/core/registry'                    // 注册表系统
'@web/core/utils/timing'                // 时间工具
'@web/core/utils/components'            // 组件工具
'@odoo/owl'                             // OWL框架
```

## 主要组件定义

### 1. MenuDropdown 组件

```javascript
class MenuDropdown extends Dropdown {}
```

**组件特性**:
- **继承**: 继承Dropdown基类
- **专用**: 专门用于菜单下拉
- **扩展**: 可扩展的菜单下拉组件

### 2. NavBar 组件

```javascript
class NavBar extends Component {
    static template = "web.NavBar";
    static components = { Dropdown, DropdownItem, DropdownGroup, MenuDropdown, ErrorHandler, Transition };
    static props = {};

    setup() {
        this.currentAppSectionsExtra = [];
        this.actionService = useService("action");
        this.menuService = useService("menu");
        this.pwa = useService("pwa");
        this.root = useRef("root");
        this.appSubMenus = useRef("appSubMenus");
        
        const debouncedAdapt = debounce(this.adapt.bind(this), 250);
        onWillDestroy(() => debouncedAdapt.cancel());
        useExternalListener(window, "resize", debouncedAdapt);
    }
}
```

**组件特性**:
- **服务集成**: 集成动作、菜单、PWA服务
- **响应式**: 响应窗口大小变化
- **防抖**: 使用防抖优化性能
- **引用管理**: 管理DOM元素引用

## 核心属性

### 1. 组件状态

```javascript
// 状态定义
this.state = useState({
    isAllAppsMenuOpened: false,      // 所有应用菜单是否打开
    isAppMenuSidebarOpened: false,   // 应用菜单侧边栏是否打开
});

// 辅助属性
this.currentAppSectionsExtra = [];  // 额外的应用部分
this.swipeStartX = null;             // 滑动开始位置
```

**状态属性功能**:
- **isAllAppsMenuOpened**: 控制所有应用菜单的显示
- **isAppMenuSidebarOpened**: 控制应用菜单侧边栏的显示
- **currentAppSectionsExtra**: 存储溢出的菜单项
- **swipeStartX**: 记录触摸滑动的起始位置

### 2. 服务引用

```javascript
// 服务集成
this.actionService = useService("action");
this.menuService = useService("menu");
this.pwa = useService("pwa");
```

**服务功能**:
- **actionService**: 处理动作执行
- **menuService**: 管理菜单操作
- **pwa**: PWA相关功能

### 3. DOM引用

```javascript
// DOM引用
this.root = useRef("root");
this.appSubMenus = useRef("appSubMenus");
```

**引用功能**:
- **root**: 根元素引用
- **appSubMenus**: 应用子菜单元素引用

## 核心功能

### 1. 当前应用获取

```javascript
get currentApp() {
    return this.menuService.getCurrentApp();
}

get currentAppSections() {
    return (
        (this.currentApp && this.menuService.getMenuAsTree(this.currentApp.id).childrenTree) ||
        []
    );
}
```

**应用获取功能**:
- **当前应用**: 获取当前活动的应用
- **应用部分**: 获取当前应用的菜单部分
- **树形结构**: 以树形结构组织菜单
- **容错处理**: 提供默认空数组

### 2. 系统托盘管理

```javascript
get systrayItems() {
    return systrayRegistry
        .getEntries()
        .map(([key, value]) => ({ key, ...value }))
        .filter((item) => ("isDisplayed" in item ? item.isDisplayed(this.env) : true))
        .reverse();
}
```

**托盘管理功能**:
- **注册表获取**: 从系统托盘注册表获取项目
- **条件过滤**: 根据显示条件过滤项目
- **顺序反转**: 反转显示顺序
- **环境注入**: 为项目注入环境对象

### 3. 响应式适配

```javascript
async adapt() {
    if (!this.root.el) {
        return;
    }

    // ------- Initialize -------
    const sectionsMenu = this.appSubMenus.el;
    if (!sectionsMenu) {
        return;
    }

    // Save initial state
    const initialAppSectionsExtra = this.currentAppSectionsExtra;
    
    // Restore sections
    const sections = [...sectionsMenu.querySelectorAll(":scope > *:not(.o_menu_sections_more)")];
    for (const section of sections) {
        section.classList.remove("d-none");
    }
    this.currentAppSectionsExtra = [];

    // ------- Check overflowing sections -------
    const sectionsAvailableWidth = getBoundingClientRect.call(sectionsMenu).width;
    const sectionsTotalWidth = sections.reduce(
        (sum, s) => sum + getBoundingClientRect.call(s).width,
        0
    );
    
    if (sectionsAvailableWidth < sectionsTotalWidth) {
        // Handle overflow
        let width = 46; // More menu width
        for (const section of sections) {
            if (sectionsAvailableWidth < width + section.offsetWidth) {
                const overflowingSections = sections.slice(sections.indexOf(section));
                overflowingSections.forEach((s) => {
                    s.classList.add("d-none");
                    const sectionId = s.dataset.section || 
                        s.querySelector("[data-section]").getAttribute("data-section");
                    const currentAppSection = this.currentAppSections.find(
                        (appSection) => appSection.id.toString() === sectionId
                    );
                    this.currentAppSectionsExtra.push(currentAppSection);
                });
                break;
            }
            width += section.offsetWidth;
        }
    }

    // ------- Final rendering -------
    if (initialAppSectionsExtra.length !== this.currentAppSectionsExtra.length) {
        return this.render();
    }
}
```

**适配功能**:
- **宽度计算**: 计算可用宽度和所需宽度
- **溢出检测**: 检测菜单项是否溢出
- **动态隐藏**: 动态隐藏溢出的菜单项
- **更多菜单**: 将溢出项移到"更多"菜单
- **智能渲染**: 只在必要时重新渲染

### 4. 菜单交互

```javascript
onNavBarDropdownItemSelection(menu) {
    if (menu) {
        this.menuService.selectMenu(menu);
    }
}

async _onMenuClicked(menu) {
    await this.menuService.selectMenu(menu);
    this._closeAppMenuSidebar();
}

_closeAppMenuSidebar() {
    this.state.isAllAppsMenuOpened = false;
    this.state.isAppMenuSidebarOpened = false;
}

_openAppMenuSidebar() {
    this.state.isAppMenuSidebarOpened = !this.state.isAppMenuSidebarOpened;
}
```

**交互功能**:
- **菜单选择**: 处理菜单项选择
- **侧边栏控制**: 控制侧边栏的打开和关闭
- **状态管理**: 管理菜单的显示状态
- **异步处理**: 异步处理菜单点击

### 5. 触摸手势

```javascript
_onSwipeStart(ev) {
    this.swipeStartX = ev.changedTouches[0].clientX;
}

_onSwipeEnd(ev) {
    if (!this.swipeStartX) {
        return;
    }
    const deltaX = this.swipeStartX - ev.changedTouches[0].clientX;
    if (deltaX < SWIPE_ACTIVATION_THRESHOLD) {
        return;
    }
    this._closeAppMenuSidebar();
    this.swipeStartX = null;
}
```

**手势功能**:
- **滑动检测**: 检测触摸滑动手势
- **阈值判断**: 使用阈值判断有效滑动
- **侧边栏关闭**: 滑动关闭侧边栏
- **状态重置**: 重置滑动状态

### 6. 错误处理

```javascript
handleItemError(error, item) {
    // remove the faulty component
    item.isDisplayed = () => false;
    Promise.resolve().then(() => {
        throw error;
    });
}
```

**错误处理功能**:
- **组件移除**: 移除有问题的组件
- **显示控制**: 设置组件不显示
- **错误抛出**: 异步抛出错误
- **容错机制**: 提供容错机制

## 使用场景

### 1. 导航栏管理器

```javascript
// 导航栏管理器
class NavBarManager {
    constructor(env) {
        this.env = env;
        this.setupManager();
    }
    
    setupManager() {
        // 设置导航栏配置
        this.navBarConfig = {
            enableResponsiveAdaptation: true,
            enableTouchGestures: true,
            enableErrorHandling: true,
            enableCustomization: true,
            adaptationDelay: 250,
            swipeThreshold: 100
        };
        
        // 设置布局配置
        this.layoutConfig = {
            maxMenuItems: 8,
            moreMenuWidth: 46,
            minItemWidth: 80,
            enableCollapse: true,
            collapseBreakpoint: 768
        };
        
        // 设置主题配置
        this.themeConfig = {
            theme: 'default',
            enableDarkMode: false,
            enableCustomColors: false,
            enableAnimations: true
        };
        
        this.setupEventListeners();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听窗口大小变化
        window.addEventListener('resize', this.handleResize.bind(this));
        
        // 监听菜单变化
        this.env.bus.addEventListener('MENUS:APP-CHANGED', this.handleMenuChange.bind(this));
        
        // 监听系统托盘变化
        this.env.services.registry.category('systray').addEventListener('UPDATE', this.handleSystrayUpdate.bind(this));
    }
    
    // 创建导航栏
    createNavBar() {
        const navBar = new NavBar();
        
        // 扩展导航栏功能
        this.extendNavBar(navBar);
        
        return navBar;
    }
    
    // 扩展导航栏功能
    extendNavBar(navBar) {
        // 添加自定义功能
        if (this.navBarConfig.enableCustomization) {
            this.addCustomizationFeature(navBar);
        }
        
        // 添加主题支持
        this.addThemeSupport(navBar);
        
        // 添加性能监控
        this.addPerformanceMonitoring(navBar);
        
        // 添加可访问性支持
        this.addAccessibilitySupport(navBar);
    }
    
    // 添加自定义功能
    addCustomizationFeature(navBar) {
        navBar.customization = useState({
            enableCustomLayout: false,
            customMenuOrder: [],
            hiddenMenuItems: [],
            customSystrayOrder: []
        });
        
        navBar.customizeLayout = function(config) {
            Object.assign(this.customization, config);
            this.render();
        };
        
        navBar.saveCustomization = function() {
            const customConfig = {
                layout: this.customization,
                timestamp: Date.now()
            };
            
            localStorage.setItem('odoo_navbar_customization', JSON.stringify(customConfig));
        };
        
        navBar.loadCustomization = function() {
            try {
                const stored = localStorage.getItem('odoo_navbar_customization');
                if (stored) {
                    const config = JSON.parse(stored);
                    Object.assign(this.customization, config.layout);
                }
            } catch (error) {
                console.warn('Failed to load navbar customization:', error);
            }
        };
        
        // 加载保存的自定义配置
        navBar.loadCustomization();
    }
    
    // 添加主题支持
    addThemeSupport(navBar) {
        navBar.themeState = useState({
            currentTheme: this.themeConfig.theme,
            isDarkMode: this.themeConfig.enableDarkMode,
            customColors: {}
        });
        
        navBar.setTheme = function(theme) {
            this.themeState.currentTheme = theme;
            this.applyTheme();
        };
        
        navBar.toggleDarkMode = function() {
            this.themeState.isDarkMode = !this.themeState.isDarkMode;
            this.applyTheme();
        };
        
        navBar.applyTheme = function() {
            const rootElement = document.documentElement;
            
            // 应用主题类
            rootElement.className = rootElement.className.replace(/theme-\w+/g, '');
            rootElement.classList.add(`theme-${this.themeState.currentTheme}`);
            
            // 应用暗色模式
            if (this.themeState.isDarkMode) {
                rootElement.classList.add('dark-mode');
            } else {
                rootElement.classList.remove('dark-mode');
            }
            
            // 应用自定义颜色
            Object.entries(this.themeState.customColors).forEach(([property, value]) => {
                rootElement.style.setProperty(`--${property}`, value);
            });
        };
        
        // 初始化主题
        navBar.applyTheme();
    }
    
    // 添加性能监控
    addPerformanceMonitoring(navBar) {
        const originalAdapt = navBar.adapt.bind(navBar);
        
        navBar.adapt = async function() {
            const startTime = performance.now();
            
            try {
                const result = await originalAdapt();
                
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                // 记录性能指标
                this.recordPerformanceMetric('adapt', duration);
                
                return result;
            } catch (error) {
                this.recordPerformanceError('adapt', error);
                throw error;
            }
        };
        
        navBar.performanceMetrics = {
            adaptations: [],
            renders: [],
            errors: []
        };
        
        navBar.recordPerformanceMetric = function(operation, duration) {
            const metric = {
                operation: operation,
                duration: duration,
                timestamp: Date.now()
            };
            
            this.performanceMetrics[operation + 's'] = this.performanceMetrics[operation + 's'] || [];
            this.performanceMetrics[operation + 's'].push(metric);
            
            // 限制记录数量
            if (this.performanceMetrics[operation + 's'].length > 100) {
                this.performanceMetrics[operation + 's'].shift();
            }
        };
        
        navBar.recordPerformanceError = function(operation, error) {
            const errorRecord = {
                operation: operation,
                error: error.message,
                stack: error.stack,
                timestamp: Date.now()
            };
            
            this.performanceMetrics.errors.push(errorRecord);
            
            // 限制错误记录数量
            if (this.performanceMetrics.errors.length > 50) {
                this.performanceMetrics.errors.shift();
            }
        };
        
        navBar.getPerformanceReport = function() {
            const adaptations = this.performanceMetrics.adaptations || [];
            const renders = this.performanceMetrics.renders || [];
            
            return {
                totalAdaptations: adaptations.length,
                averageAdaptationTime: adaptations.length > 0 ? 
                    adaptations.reduce((sum, m) => sum + m.duration, 0) / adaptations.length : 0,
                totalRenders: renders.length,
                averageRenderTime: renders.length > 0 ? 
                    renders.reduce((sum, m) => sum + m.duration, 0) / renders.length : 0,
                totalErrors: this.performanceMetrics.errors.length,
                lastError: this.performanceMetrics.errors[this.performanceMetrics.errors.length - 1]
            };
        };
    }
    
    // 添加可访问性支持
    addAccessibilitySupport(navBar) {
        navBar.accessibilityState = useState({
            enableKeyboardNavigation: true,
            enableScreenReader: true,
            enableHighContrast: false,
            enableReducedMotion: false
        });
        
        navBar.setupKeyboardNavigation = function() {
            this.el.addEventListener('keydown', (event) => {
                switch (event.key) {
                    case 'Tab':
                        this.handleTabNavigation(event);
                        break;
                    case 'Enter':
                    case ' ':
                        this.handleActivation(event);
                        break;
                    case 'Escape':
                        this.handleEscape(event);
                        break;
                    case 'ArrowLeft':
                    case 'ArrowRight':
                        this.handleArrowNavigation(event);
                        break;
                }
            });
        };
        
        navBar.handleTabNavigation = function(event) {
            // 处理Tab键导航
            const focusableElements = this.el.querySelectorAll(
                'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
            );
            
            const currentIndex = Array.from(focusableElements).indexOf(document.activeElement);
            
            if (event.shiftKey) {
                // Shift+Tab - 向前导航
                if (currentIndex > 0) {
                    focusableElements[currentIndex - 1].focus();
                    event.preventDefault();
                }
            } else {
                // Tab - 向后导航
                if (currentIndex < focusableElements.length - 1) {
                    focusableElements[currentIndex + 1].focus();
                    event.preventDefault();
                }
            }
        };
        
        navBar.handleActivation = function(event) {
            // 处理激活键（Enter/Space）
            const target = event.target;
            
            if (target.tagName === 'BUTTON' || target.getAttribute('role') === 'button') {
                target.click();
                event.preventDefault();
            }
        };
        
        navBar.handleEscape = function(event) {
            // 处理Escape键
            if (this.state.isAllAppsMenuOpened) {
                this.state.isAllAppsMenuOpened = false;
                event.preventDefault();
            }
            
            if (this.state.isAppMenuSidebarOpened) {
                this._closeAppMenuSidebar();
                event.preventDefault();
            }
        };
        
        navBar.handleArrowNavigation = function(event) {
            // 处理箭头键导航
            const menuItems = this.el.querySelectorAll('.o_menu_item');
            const currentIndex = Array.from(menuItems).indexOf(document.activeElement);
            
            if (currentIndex >= 0) {
                let nextIndex;
                
                if (event.key === 'ArrowLeft') {
                    nextIndex = currentIndex > 0 ? currentIndex - 1 : menuItems.length - 1;
                } else {
                    nextIndex = currentIndex < menuItems.length - 1 ? currentIndex + 1 : 0;
                }
                
                menuItems[nextIndex].focus();
                event.preventDefault();
            }
        };
        
        // 初始化可访问性功能
        navBar.setupKeyboardNavigation();
    }
    
    // 处理窗口大小变化
    handleResize() {
        // 触发导航栏适配
        this.env.bus.trigger('NAVBAR:RESIZE', {
            width: window.innerWidth,
            height: window.innerHeight,
            timestamp: Date.now()
        });
    }
    
    // 处理菜单变化
    handleMenuChange(menuData) {
        // 处理菜单应用变化
        this.env.bus.trigger('NAVBAR:MENU_CHANGED', {
            menuData: menuData,
            timestamp: Date.now()
        });
    }
    
    // 处理系统托盘更新
    handleSystrayUpdate() {
        // 处理系统托盘项目更新
        this.env.bus.trigger('NAVBAR:SYSTRAY_UPDATED', {
            timestamp: Date.now()
        });
    }
    
    // 获取导航栏统计
    getNavBarStatistics() {
        return {
            config: this.navBarConfig,
            layout: this.layoutConfig,
            theme: this.themeConfig,
            performance: {
                // 性能统计将由扩展的导航栏提供
            }
        };
    }
    
    // 销毁管理器
    destroy() {
        // 清理事件监听器
        window.removeEventListener('resize', this.handleResize);
        this.env.bus.removeEventListener('MENUS:APP-CHANGED', this.handleMenuChange);
        this.env.services.registry.category('systray').removeEventListener('UPDATE', this.handleSystrayUpdate);
    }
}

// 使用示例
const navBarManager = new NavBarManager(env);

// 创建导航栏
const navBar = navBarManager.createNavBar();

// 获取统计信息
const stats = navBarManager.getNavBarStatistics();
console.log('NavBar statistics:', stats);
```

## 技术特点

### 1. 响应式设计
- **自适应**: 根据屏幕宽度自动适配
- **溢出处理**: 智能处理菜单项溢出
- **防抖优化**: 使用防抖优化性能
- **动态渲染**: 只在必要时重新渲染

### 2. 交互体验
- **触摸支持**: 支持触摸滑动手势
- **键盘导航**: 支持键盘导航
- **状态管理**: 完善的状态管理
- **动画过渡**: 平滑的动画过渡

### 3. 模块化设计
- **组件分离**: 清晰的组件分离
- **服务集成**: 深度集成各种服务
- **注册表**: 使用注册表管理系统托盘
- **错误处理**: 完善的错误处理机制

### 4. 性能优化
- **防抖**: 防抖优化窗口大小变化
- **精确计算**: 使用getBoundingClientRect精确计算
- **智能渲染**: 避免不必要的重新渲染
- **内存管理**: 合理的内存使用

## 设计模式

### 1. 组件模式 (Component Pattern)
- **组件组合**: 多个组件的组合使用
- **组件通信**: 组件间的通信机制
- **组件生命周期**: 完整的生命周期管理

### 2. 观察者模式 (Observer Pattern)
- **事件监听**: 监听各种系统事件
- **状态响应**: 响应状态变化
- **自动更新**: 自动更新界面

### 3. 适配器模式 (Adapter Pattern)
- **响应式适配**: 适配不同屏幕尺寸
- **服务适配**: 适配不同的服务接口
- **组件适配**: 适配不同的组件需求

### 4. 策略模式 (Strategy Pattern)
- **布局策略**: 不同的布局策略
- **交互策略**: 不同的交互策略
- **适配策略**: 不同的适配策略

## 注意事项

1. **性能优化**: 避免频繁的DOM操作和重新渲染
2. **内存管理**: 及时清理事件监听器和定时器
3. **用户体验**: 提供流畅的交互和反馈
4. **可访问性**: 确保键盘导航和屏幕阅读器支持

## 扩展建议

1. **主题系统**: 支持多主题和自定义样式
2. **布局定制**: 支持用户自定义布局
3. **快捷键**: 添加更多快捷键支持
4. **动画效果**: 增强动画和过渡效果
5. **移动优化**: 进一步优化移动端体验

该导航栏组件为Odoo Web客户端提供了完整的导航功能，通过响应式设计和智能适配确保了在各种设备和屏幕尺寸下的良好用户体验。

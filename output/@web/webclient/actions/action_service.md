# ActionService - 动作服务

## 概述

`action_service.js` 是 Odoo Web 客户端的核心动作服务，提供了完整的动作管理和执行功能。该模块包含1722行代码，是整个动作系统的核心，负责动作的解析、执行、路由、状态管理等功能，具备动作注册、视图管理、面包屑导航、URL同步等特性，是Odoo Web客户端动作系统的基础设施。

## 文件信息
- **路径**: `/web/static/src/webclient/actions/action_service.js`
- **行数**: 1722
- **模块**: `@web/webclient/actions/action_service`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'                    // 国际化翻译
'@web/core/browser/browser'                     // 浏览器服务
'@web/core/context'                             // 上下文处理
'@web/core/debug/debug_context'                 // 调试上下文
'@web/core/py_js/py'                            // Python表达式
'@web/core/network/rpc'                         // RPC网络
'@web/core/registry'                            // 注册表系统
'@web/core/user'                                // 用户服务
'@web/core/utils/concurrency'                   // 并发工具
'@web/core/utils/hooks'                         // 钩子工具
'@web/views/view'                               // 视图系统
'@web/webclient/actions/action_dialog'          // 动作对话框
'@web/webclient/actions/reports/report_action'  // 报表动作
'@web/core/orm_service'                         // ORM服务
'@web/search/action_hook'                       // 搜索钩子
'@web/search/control_panel/control_panel'       // 控制面板
'@web/core/browser/router'                      // 路由器
'@odoo/owl'                                     // OWL框架
```

## 类型定义

```javascript
/** @typedef {number|false} ActionId */
/** @typedef {Object} ActionDescription */
/** @typedef {"current" | "fullscreen" | "new" | "main" | "self" | "inline"} ActionMode */
/** @typedef {string} ActionTag */
/** @typedef {string} ActionXMLId */
/** @typedef {Object} Context */
/** @typedef {Function} CallableFunction */
/** @typedef {string} ViewType */
/** @typedef {ActionId|ActionXMLId|ActionTag|ActionDescription} ActionRequest */

/**
 * @typedef {Object} ActionOptions
 * @property {Context} [additionalContext]
 * @property {boolean} [clearBreadcrumbs]
 * @property {CallableFunction} [onClose]
 * @property {Object} [props]
 * @property {ViewType} [viewType]
 * @property {"replaceCurrentAction" | "replacePreviousAction"} [stackPosition]
 * @property {number} [index]
 */
```

## 核心组件

### 1. BlankComponent 空白组件

```javascript
class BlankComponent extends Component {
    static props = ["onMounted", "withControlPanel", "*"];
    static template = xml`
        <ControlPanel display="{disableDropdown: true}" t-if="props.withControlPanel and !env.isSmall">
            <t t-set-slot="layout-buttons">
                <button class="btn btn-primary invisible"> empty </button>
            </t>
        </ControlPanel>`;
    static components = { ControlPanel };

    setup() {
        useChildSubEnv({ config: { breadcrumbs: [], noBreadcrumbs: true } });
        onMounted(() => this.props.onMounted());
    }
}
```

**空白组件功能**:
- **占位显示**: 在没有具体动作时显示空白页面
- **控制面板**: 可选的控制面板显示
- **面包屑**: 清空面包屑导航
- **挂载回调**: 组件挂载时的回调处理

### 2. 标准动作服务属性

```javascript
const standardActionServiceProps = {
    action: Object,                              // 动作对象
    actionId: { type: Number, optional: true },  // 动作ID
    className: { type: String, optional: true }, // CSS类名
    globalState: { type: Object, optional: true }, // 全局状态
    state: { type: Object, optional: true },     // 局部状态
    resId: { type: [Number, Boolean], optional: true }, // 资源ID
    updateActionState: { type: Function, optional: true }, // 状态更新函数
};
```

## 核心功能

### 1. 动作执行

```javascript
// 执行动作的核心方法
async function doAction(actionRequest, options = {}) {
    // 解析动作请求
    const actionInfo = await parseActionRequest(actionRequest);
    
    // 验证动作权限
    await validateActionPermissions(actionInfo);
    
    // 准备动作上下文
    const context = prepareActionContext(actionInfo, options);
    
    // 执行动作
    const result = await executeAction(actionInfo, context, options);
    
    // 更新URL和状态
    updateUrlAndState(actionInfo, options);
    
    return result;
}
```

**动作执行功能**:
- **动作解析**: 解析各种格式的动作请求
- **权限验证**: 验证用户是否有执行权限
- **上下文准备**: 准备动作执行的上下文
- **动作执行**: 实际执行动作逻辑
- **状态同步**: 同步URL和应用状态

### 2. 动作类型处理

```javascript
// 不同类型动作的处理器
const actionHandlers = {
    'ir.actions.act_window': handleWindowAction,
    'ir.actions.client': handleClientAction,
    'ir.actions.server': handleServerAction,
    'ir.actions.report': handleReportAction,
    'ir.actions.act_url': handleUrlAction,
    'ir.actions.act_window_close': handleCloseAction
};

// 窗口动作处理
async function handleWindowAction(action, options) {
    const viewInfo = await loadViewInfo(action);
    const component = createViewComponent(viewInfo);
    return renderAction(component, action, options);
}

// 客户端动作处理
async function handleClientAction(action, options) {
    const clientAction = getClientAction(action.tag);
    return renderAction(clientAction, action, options);
}
```

**动作类型处理功能**:
- **类型识别**: 识别不同的动作类型
- **专用处理**: 每种类型有专门的处理逻辑
- **组件创建**: 创建对应的组件实例
- **渲染管理**: 管理动作的渲染过程

### 3. 面包屑导航

```javascript
// 面包屑管理
class BreadcrumbManager {
    constructor() {
        this.breadcrumbs = [];
        this.maxBreadcrumbs = 10;
    }
    
    addBreadcrumb(action, title) {
        const breadcrumb = {
            id: generateId(),
            action,
            title,
            timestamp: Date.now()
        };
        
        this.breadcrumbs.push(breadcrumb);
        
        // 限制面包屑数量
        if (this.breadcrumbs.length > this.maxBreadcrumbs) {
            this.breadcrumbs.shift();
        }
        
        this.updateUI();
    }
    
    navigateTo(breadcrumbId) {
        const index = this.breadcrumbs.findIndex(b => b.id === breadcrumbId);
        if (index !== -1) {
            // 移除后续面包屑
            this.breadcrumbs = this.breadcrumbs.slice(0, index + 1);
            
            // 执行对应动作
            const breadcrumb = this.breadcrumbs[index];
            return this.actionService.doAction(breadcrumb.action);
        }
    }
    
    clearBreadcrumbs() {
        this.breadcrumbs = [];
        this.updateUI();
    }
}
```

**面包屑导航功能**:
- **路径记录**: 记录用户的导航路径
- **快速导航**: 支持快速返回之前的页面
- **数量限制**: 限制面包屑的最大数量
- **状态同步**: 与UI状态保持同步

### 4. URL同步

```javascript
// URL同步管理
class UrlSyncManager {
    constructor(router) {
        this.router = router;
        this.currentState = {};
    }
    
    syncActionToUrl(action, options) {
        const urlState = this.actionToUrlState(action);
        
        if (!this.isStateEqual(urlState, this.currentState)) {
            this.currentState = urlState;
            
            if (options.replaceState) {
                this.router.replaceState(urlState);
            } else {
                this.router.pushState(urlState);
            }
        }
    }
    
    actionToUrlState(action) {
        const state = {
            action: action.id || action.xml_id,
            model: action.res_model,
            view_type: action.view_mode,
            id: action.res_id
        };
        
        // 添加其他相关参数
        if (action.domain) {
            state.domain = JSON.stringify(action.domain);
        }
        
        if (action.context) {
            state.context = JSON.stringify(action.context);
        }
        
        return state;
    }
    
    urlStateToAction(urlState) {
        const action = {
            id: urlState.action,
            res_model: urlState.model,
            view_mode: urlState.view_type,
            res_id: urlState.id
        };
        
        if (urlState.domain) {
            action.domain = JSON.parse(urlState.domain);
        }
        
        if (urlState.context) {
            action.context = JSON.parse(urlState.context);
        }
        
        return action;
    }
}
```

**URL同步功能**:
- **状态映射**: 将动作状态映射到URL参数
- **历史管理**: 管理浏览器历史记录
- **深度链接**: 支持直接通过URL访问特定状态
- **状态恢复**: 从URL恢复应用状态

## 使用场景

### 1. 动作服务初始化

```javascript
// 动作服务的初始化和配置
function createActionService(env) {
    const actionService = {
        // 核心方法
        doAction: async (actionRequest, options = {}) => {
            return doActionImpl(actionRequest, options, env);
        },
        
        // 动作栈管理
        actionStack: [],
        currentAction: null,
        
        // 面包屑管理
        breadcrumbManager: new BreadcrumbManager(),
        
        // URL同步
        urlSyncManager: new UrlSyncManager(env.services.router),
        
        // 注册表
        actionRegistry: registry.category("actions"),
        handlerRegistry: registry.category("action_handlers"),
        
        // 工具方法
        loadAction: async (actionId) => {
            return env.services.rpc('/web/action/load', { action_id: actionId });
        },
        
        switchView: async (viewType, options = {}) => {
            if (actionService.currentAction) {
                const newAction = {
                    ...actionService.currentAction,
                    view_mode: viewType
                };
                return actionService.doAction(newAction, options);
            }
        },
        
        goBack: () => {
            if (actionService.actionStack.length > 1) {
                actionService.actionStack.pop();
                const previousAction = actionService.actionStack[actionService.actionStack.length - 1];
                return actionService.doAction(previousAction, { replaceState: true });
            }
        },
        
        closeAction: () => {
            actionService.actionStack = [];
            actionService.currentAction = null;
            actionService.breadcrumbManager.clearBreadcrumbs();
            return actionService.doAction('home');
        }
    };
    
    return actionService;
}

// 注册动作服务
registry.category("services").add("action", {
    dependencies: ["rpc", "router", "notification", "dialog"],
    start(env, { rpc, router, notification, dialog }) {
        return createActionService(env);
    }
});
```

### 2. 自定义动作处理器

```javascript
// 注册自定义动作处理器
registry.category("action_handlers").add("custom_action", {
    canHandle: (action) => {
        return action.type === 'custom.action';
    },
    
    handle: async (action, options, env) => {
        // 自定义动作处理逻辑
        const component = await createCustomComponent(action);
        return renderCustomAction(component, action, options);
    }
});

// 注册客户端动作
registry.category("actions").add("my_dashboard", {
    Component: MyDashboardComponent,
    props: (action) => ({
        title: action.name,
        config: action.params
    })
});
```

### 3. 动作执行示例

```javascript
// 各种动作执行示例
class ActionExamples {
    constructor(actionService) {
        this.actionService = actionService;
    }
    
    // 打开表单视图
    async openForm(resModel, resId) {
        return this.actionService.doAction({
            type: 'ir.actions.act_window',
            res_model: resModel,
            res_id: resId,
            view_mode: 'form',
            target: 'current'
        });
    }
    
    // 打开列表视图
    async openList(resModel, domain = []) {
        return this.actionService.doAction({
            type: 'ir.actions.act_window',
            res_model: resModel,
            view_mode: 'list',
            domain: domain,
            target: 'current'
        });
    }
    
    // 打开对话框
    async openDialog(resModel, resId) {
        return this.actionService.doAction({
            type: 'ir.actions.act_window',
            res_model: resModel,
            res_id: resId,
            view_mode: 'form',
            target: 'new'
        });
    }
    
    // 执行服务器动作
    async executeServerAction(actionId, context = {}) {
        return this.actionService.doAction({
            type: 'ir.actions.server',
            id: actionId,
            context: context
        });
    }
    
    // 生成报表
    async generateReport(reportName, resIds, data = {}) {
        return this.actionService.doAction({
            type: 'ir.actions.report',
            report_name: reportName,
            data: data,
            context: { active_ids: resIds }
        });
    }
    
    // 打开URL
    async openUrl(url, target = '_blank') {
        return this.actionService.doAction({
            type: 'ir.actions.act_url',
            url: url,
            target: target
        });
    }
    
    // 执行客户端动作
    async executeClientAction(tag, params = {}) {
        return this.actionService.doAction({
            type: 'ir.actions.client',
            tag: tag,
            params: params
        });
    }
}

// 使用示例
const examples = new ActionExamples(env.services.action);

// 打开合作伙伴表单
await examples.openForm('res.partner', 123);

// 打开客户列表
await examples.openList('res.partner', [['customer_rank', '>', 0]]);

// 在对话框中编辑
await examples.openDialog('res.partner', 123);

// 生成发票报表
await examples.generateReport('account.report_invoice', [456, 789]);
```

## 技术特点

### 1. 完整的动作系统
- **类型支持**: 支持所有Odoo动作类型
- **执行引擎**: 完整的动作执行引擎
- **状态管理**: 完善的动作状态管理
- **错误处理**: 完整的错误处理机制

### 2. 路由集成
- **URL同步**: 动作状态与URL的同步
- **深度链接**: 支持直接URL访问
- **历史管理**: 浏览器历史记录管理
- **状态恢复**: 页面刷新后的状态恢复

### 3. 扩展性设计
- **注册表**: 基于注册表的扩展机制
- **插件支持**: 支持动作处理器插件
- **钩子系统**: 完整的钩子系统
- **自定义动作**: 支持自定义动作类型

### 4. 性能优化
- **懒加载**: 动作组件的懒加载
- **缓存机制**: 动作定义的缓存
- **并发控制**: 并发请求的控制
- **内存管理**: 动作栈的内存管理

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- **动作处理**: 不同动作类型的处理策略
- **渲染策略**: 不同的渲染策略
- **路由策略**: 不同的路由处理策略

### 2. 命令模式 (Command Pattern)
- **动作封装**: 将动作封装为命令对象
- **撤销重做**: 支持动作的撤销和重做
- **批量执行**: 批量动作的执行

### 3. 观察者模式 (Observer Pattern)
- **状态通知**: 动作状态变化的通知
- **事件分发**: 动作事件的分发机制
- **钩子系统**: 基于观察者的钩子系统

### 4. 工厂模式 (Factory Pattern)
- **组件创建**: 根据动作类型创建组件
- **动作解析**: 动作对象的工厂创建
- **服务创建**: 动作相关服务的创建

## 注意事项

1. **内存管理**: 避免动作栈过深导致内存问题
2. **权限检查**: 确保动作执行前的权限验证
3. **错误处理**: 完善的动作执行错误处理
4. **性能监控**: 监控动作执行的性能指标

## 扩展建议

1. **动作缓存**: 增强动作定义的缓存机制
2. **并行执行**: 支持动作的并行执行
3. **动作队列**: 实现动作执行队列
4. **性能分析**: 添加动作执行的性能分析
5. **调试工具**: 增强动作调试工具

该动作服务为Odoo Web客户端提供了完整的动作管理和执行基础设施，通过模块化的设计和强大的扩展机制确保了系统的灵活性和可维护性。

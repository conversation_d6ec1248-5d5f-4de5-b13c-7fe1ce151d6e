# Reports Utils - 报表工具函数

## 概述

`utils.js` 是 Odoo Web 客户端的报表工具函数模块，提供了报表URL生成和下载功能。该模块包含92行代码，定义了 `getReportUrl` 和 `downloadReport` 两个核心函数，用于处理报表的URL构建和文件下载，具备多种报表格式支持、wkhtmltopdf状态检查、错误消息处理等特性，是Odoo Web报表系统中处理报表生成和下载的基础工具库。

## 文件信息
- **路径**: `/web/static/src/webclient/actions/reports/utils.js`
- **行数**: 92
- **模块**: `@web/webclient/actions/reports/utils`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'    // 国际化翻译
'@web/core/network/download'    // 网络下载
```

## 核心函数

### 1. getReportUrl 函数

```javascript
/**
 * Generates the report url given a report action.
 *
 * @param {Object} action the report action
 * @param {"text"|"qweb"|"html"} type the type of the report
 * @param {Object} userContext the user context
 * @returns {string}
 */
function getReportUrl(action, type, userContext) {
    let url = `/report/${type}/${action.report_name}`;
    const actionContext = action.context || {};
    
    if (action.data && JSON.stringify(action.data) !== "{}") {
        // build a query string with `action.data` (it's the place where reports
        // using a wizard to customize the output traditionally put their options)
        const options = encodeURIComponent(JSON.stringify(action.data));
        const context = encodeURIComponent(JSON.stringify(actionContext));
        url += `?options=${options}&context=${context}`;
    } else {
        if (actionContext.active_ids) {
            url += `/${actionContext.active_ids.join(",")}`;
        }
        if (type === "html") {
            const context = encodeURIComponent(JSON.stringify(userContext));
            url += `?context=${context}`;
        }
    }
    return url;
}
```

**URL生成功能**:
- **基础URL**: 构建基础的报表URL路径
- **数据参数**: 处理报表的自定义数据参数
- **上下文**: 处理动作上下文和用户上下文
- **活动ID**: 处理活动记录ID列表
- **参数编码**: 安全编码URL参数

**参数说明**:
- **action**: 报表动作对象
- **type**: 报表类型（text、qweb、html）
- **userContext**: 用户上下文对象

### 2. downloadReport 函数

```javascript
/**
 * Launches download action of the report
 *
 * @param {Function} rpc a function to perform RPCs
 * @param {Object} action the report action
 * @param {"pdf"|"text"} type the type of the report to download
 * @param {Object} userContext the user context
 * @returns {Promise<{success: boolean, message?: string}>}
 */
async function downloadReport(rpc, action, type, userContext) {
    let message;
    if (type === "pdf") {
        // Cache the wkhtml status on the function. In prod this means is only
        // checked once, but we can reset it between tests to test multiple statuses.
        downloadReport.wkhtmltopdfStatusProm ||= rpc("/report/check_wkhtmltopdf");
        const status = await downloadReport.wkhtmltopdfStatusProm;
        message = getWKHTMLTOPDF_MESSAGES(status);
        if (!["upgrade", "ok"].includes(status)) {
            return { success: false, message };
        }
    }
    const url = getReportUrl(action, type);
    await download({
        url: "/report/download",
        data: {
            data: JSON.stringify([url, action.report_type]),
            context: JSON.stringify(userContext),
        },
    });
    return { success: true, message };
}
```

**下载功能**:
- **PDF检查**: 检查wkhtmltopdf的安装状态
- **状态缓存**: 缓存wkhtmltopdf状态检查结果
- **错误处理**: 处理PDF生成相关的错误
- **文件下载**: 执行实际的文件下载操作

### 3. getWKHTMLTOPDF_MESSAGES 函数

```javascript
function getWKHTMLTOPDF_MESSAGES(status) {
    const link = '<br><br><a href="http://wkhtmltopdf.org/" target="_blank">wkhtmltopdf.org</a>';
    const _status = {
        broken: _t(
            "Your installation of Wkhtmltopdf seems to be broken. The report will be shown in html.%(link)s",
            { link }
        ),
        install: _t(
            "Unable to find Wkhtmltopdf on this system. The report will be shown in html.%(link)s",
            { link }
        ),
        upgrade: _t(
            "You should upgrade your version of Wkhtmltopdf to at least 0.12.0 in order to get a correct display of headers and footers as well as support for table-breaking between pages.%(link)s",
            { link }
        ),
        workers: _t(
            "You need to start Odoo with at least two workers to print a pdf version of the reports."
        ),
    };
    return _status[status];
}
```

**错误消息功能**:
- **状态映射**: 将wkhtmltopdf状态映射为用户消息
- **国际化**: 支持多语言错误消息
- **帮助链接**: 提供wkhtmltopdf官网链接
- **解决方案**: 为每种错误状态提供解决方案

## 使用场景

### 1. 报表管理器

```javascript
// 报表管理器
class ReportManager {
    constructor(env) {
        this.env = env;
        this.rpc = env.services.rpc;
        this.downloadService = env.services.download;
        this.reportCache = new Map();
        this.wkhtmlStatus = null;
    }
    
    // 生成报表URL
    generateReportUrl(action, type = 'html', options = {}) {
        const userContext = {
            ...this.env.context,
            ...options.context
        };
        
        return getReportUrl(action, type, userContext);
    }
    
    // 预览报表
    async previewReport(action, type = 'html') {
        try {
            const url = this.generateReportUrl(action, type);
            
            // 在新窗口中打开预览
            window.open(url, '_blank');
            
            return { success: true, url };
        } catch (error) {
            console.error('Failed to preview report:', error);
            return { success: false, error: error.message };
        }
    }
    
    // 下载报表
    async downloadReport(action, type = 'pdf', options = {}) {
        try {
            const userContext = {
                ...this.env.context,
                ...options.context
            };
            
            const result = await downloadReport(this.rpc, action, type, userContext);
            
            if (result.success) {
                this.env.services.notification.add(
                    'Report download started',
                    { type: 'success' }
                );
            } else {
                this.env.services.notification.add(
                    result.message || 'Download failed',
                    { type: 'warning' }
                );
            }
            
            return result;
        } catch (error) {
            console.error('Failed to download report:', error);
            this.env.services.notification.add(
                'Download failed: ' + error.message,
                { type: 'danger' }
            );
            return { success: false, error: error.message };
        }
    }
    
    // 批量下载报表
    async batchDownload(actions, type = 'pdf') {
        const results = [];
        
        for (const action of actions) {
            try {
                const result = await this.downloadReport(action, type);
                results.push({ action, result });
                
                // 添加延迟避免服务器过载
                if (actions.length > 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            } catch (error) {
                results.push({ action, result: { success: false, error: error.message } });
            }
        }
        
        return results;
    }
    
    // 检查PDF支持
    async checkPDFSupport() {
        if (this.wkhtmlStatus === null) {
            try {
                this.wkhtmlStatus = await this.rpc('/report/check_wkhtmltopdf');
            } catch (error) {
                console.error('Failed to check wkhtmltopdf status:', error);
                this.wkhtmlStatus = 'broken';
            }
        }
        
        return {
            status: this.wkhtmlStatus,
            supported: ['upgrade', 'ok'].includes(this.wkhtmlStatus),
            message: getWKHTMLTOPDF_MESSAGES(this.wkhtmlStatus)
        };
    }
    
    // 获取报表信息
    async getReportInfo(reportName) {
        try {
            const info = await this.rpc('/web/report/info', {
                report_name: reportName
            });
            
            return {
                name: info.name,
                model: info.model,
                type: info.report_type,
                paperformat: info.paperformat,
                attachment: info.attachment,
                attachment_use: info.attachment_use
            };
        } catch (error) {
            console.error('Failed to get report info:', error);
            return null;
        }
    }
    
    // 缓存报表URL
    getCachedUrl(action, type) {
        const key = this.generateCacheKey(action, type);
        return this.reportCache.get(key);
    }
    
    setCachedUrl(action, type, url) {
        const key = this.generateCacheKey(action, type);
        this.reportCache.set(key, {
            url,
            timestamp: Date.now(),
            expires: Date.now() + (5 * 60 * 1000) // 5分钟过期
        });
    }
    
    generateCacheKey(action, type) {
        return `${action.report_name}_${type}_${JSON.stringify(action.context || {})}_${JSON.stringify(action.data || {})}`;
    }
    
    // 清理过期缓存
    cleanExpiredCache() {
        const now = Date.now();
        for (const [key, value] of this.reportCache) {
            if (value.expires < now) {
                this.reportCache.delete(key);
            }
        }
    }
    
    // 生成报表配置
    createReportAction(reportName, options = {}) {
        return {
            type: 'ir.actions.report',
            report_name: reportName,
            report_type: options.type || 'qweb-pdf',
            context: {
                active_ids: options.activeIds || [],
                active_model: options.activeModel,
                ...options.context
            },
            data: options.data || {},
            target: options.target || 'new'
        };
    }
}

// 使用示例
const reportManager = new ReportManager(env);

// 检查PDF支持
const pdfSupport = await reportManager.checkPDFSupport();
if (!pdfSupport.supported) {
    console.warn('PDF not supported:', pdfSupport.message);
}

// 创建报表动作
const reportAction = reportManager.createReportAction('sale.report_saleorder', {
    activeIds: [123, 456],
    activeModel: 'sale.order',
    type: 'qweb-pdf'
});

// 下载报表
await reportManager.downloadReport(reportAction, 'pdf');

// 预览报表
await reportManager.previewReport(reportAction, 'html');
```

### 2. 报表URL构建器

```javascript
// 报表URL构建器
class ReportUrlBuilder {
    constructor() {
        this.baseUrl = '/report';
        this.defaultOptions = {
            format: 'html',
            context: {},
            data: {}
        };
    }
    
    // 构建基础URL
    buildBaseUrl(reportName, format) {
        return `${this.baseUrl}/${format}/${reportName}`;
    }
    
    // 构建完整URL
    buildUrl(action, options = {}) {
        const config = { ...this.defaultOptions, ...options };
        const url = this.buildBaseUrl(action.report_name, config.format);
        
        return this.addParameters(url, action, config);
    }
    
    // 添加URL参数
    addParameters(baseUrl, action, config) {
        const params = new URLSearchParams();
        const actionContext = action.context || {};
        
        // 处理数据参数
        if (action.data && Object.keys(action.data).length > 0) {
            params.set('options', JSON.stringify(action.data));
            params.set('context', JSON.stringify(actionContext));
        } else {
            // 处理活动ID
            if (actionContext.active_ids && actionContext.active_ids.length > 0) {
                baseUrl += `/${actionContext.active_ids.join(',')}`;
            }
            
            // 处理HTML格式的上下文
            if (config.format === 'html' && config.context) {
                params.set('context', JSON.stringify(config.context));
            }
        }
        
        // 添加额外参数
        if (config.extraParams) {
            for (const [key, value] of Object.entries(config.extraParams)) {
                params.set(key, value);
            }
        }
        
        const queryString = params.toString();
        return queryString ? `${baseUrl}?${queryString}` : baseUrl;
    }
    
    // 构建下载URL
    buildDownloadUrl(action, format = 'pdf') {
        const reportUrl = this.buildUrl(action, { format });
        
        return {
            url: '/report/download',
            method: 'POST',
            data: {
                data: JSON.stringify([reportUrl, action.report_type || 'qweb-pdf']),
                context: JSON.stringify(action.context || {})
            }
        };
    }
    
    // 构建预览URL
    buildPreviewUrl(action, format = 'html') {
        return this.buildUrl(action, { format });
    }
    
    // 构建打印URL
    buildPrintUrl(action) {
        return this.buildUrl(action, { 
            format: 'pdf',
            extraParams: { print: '1' }
        });
    }
    
    // 解析URL参数
    parseUrl(url) {
        const urlObj = new URL(url, window.location.origin);
        const pathParts = urlObj.pathname.split('/');
        
        if (pathParts[1] !== 'report') {
            return null;
        }
        
        const format = pathParts[2];
        const reportName = pathParts[3];
        const activeIds = pathParts[4] ? pathParts[4].split(',').map(Number) : [];
        
        const params = Object.fromEntries(urlObj.searchParams);
        
        return {
            format,
            reportName,
            activeIds,
            options: params.options ? JSON.parse(params.options) : {},
            context: params.context ? JSON.parse(params.context) : {}
        };
    }
    
    // 验证URL
    validateUrl(url) {
        try {
            const parsed = this.parseUrl(url);
            return parsed !== null && parsed.reportName;
        } catch (error) {
            return false;
        }
    }
    
    // 优化URL
    optimizeUrl(url) {
        const parsed = this.parseUrl(url);
        if (!parsed) return url;
        
        // 重新构建URL以确保格式正确
        const action = {
            report_name: parsed.reportName,
            context: {
                active_ids: parsed.activeIds,
                ...parsed.context
            },
            data: parsed.options
        };
        
        return this.buildUrl(action, { format: parsed.format });
    }
}

// 使用示例
const urlBuilder = new ReportUrlBuilder();

// 构建报表URL
const action = {
    report_name: 'sale.report_saleorder',
    context: { active_ids: [123, 456] },
    data: { include_tax: true }
};

const htmlUrl = urlBuilder.buildPreviewUrl(action);
const pdfUrl = urlBuilder.buildDownloadUrl(action);
const printUrl = urlBuilder.buildPrintUrl(action);

console.log('HTML URL:', htmlUrl);
console.log('PDF URL:', pdfUrl);
console.log('Print URL:', printUrl);
```

### 3. 报表下载服务

```javascript
// 报表下载服务
class ReportDownloadService {
    constructor(env) {
        this.env = env;
        this.rpc = env.services.rpc;
        this.notification = env.services.notification;
        this.downloadQueue = [];
        this.isProcessing = false;
        this.maxConcurrent = 3;
        this.retryAttempts = 3;
    }
    
    // 添加下载任务
    async addDownloadTask(action, type = 'pdf', options = {}) {
        const task = {
            id: this.generateTaskId(),
            action,
            type,
            options,
            status: 'pending',
            attempts: 0,
            createdAt: Date.now()
        };
        
        this.downloadQueue.push(task);
        this.processQueue();
        
        return task.id;
    }
    
    // 处理下载队列
    async processQueue() {
        if (this.isProcessing) return;
        
        this.isProcessing = true;
        
        try {
            while (this.downloadQueue.length > 0) {
                const concurrentTasks = this.downloadQueue
                    .filter(task => task.status === 'pending')
                    .slice(0, this.maxConcurrent);
                
                if (concurrentTasks.length === 0) break;
                
                const promises = concurrentTasks.map(task => this.processTask(task));
                await Promise.allSettled(promises);
            }
        } finally {
            this.isProcessing = false;
        }
    }
    
    // 处理单个任务
    async processTask(task) {
        task.status = 'processing';
        task.attempts++;
        
        try {
            const result = await this.executeDownload(task);
            
            if (result.success) {
                task.status = 'completed';
                task.completedAt = Date.now();
                this.onTaskCompleted(task, result);
            } else {
                throw new Error(result.message || 'Download failed');
            }
        } catch (error) {
            console.error(`Download task ${task.id} failed:`, error);
            
            if (task.attempts < this.retryAttempts) {
                task.status = 'pending';
                task.retryAt = Date.now() + (task.attempts * 2000); // 指数退避
            } else {
                task.status = 'failed';
                task.error = error.message;
                this.onTaskFailed(task, error);
            }
        }
    }
    
    // 执行下载
    async executeDownload(task) {
        const { action, type, options } = task;
        
        // 检查PDF支持
        if (type === 'pdf') {
            const pdfCheck = await this.checkPDFSupport();
            if (!pdfCheck.supported) {
                return { success: false, message: pdfCheck.message };
            }
        }
        
        // 构建用户上下文
        const userContext = {
            ...this.env.context,
            ...options.context
        };
        
        // 执行下载
        return await downloadReport(this.rpc, action, type, userContext);
    }
    
    // 检查PDF支持
    async checkPDFSupport() {
        try {
            const status = await this.rpc('/report/check_wkhtmltopdf');
            return {
                status,
                supported: ['upgrade', 'ok'].includes(status),
                message: getWKHTMLTOPDF_MESSAGES(status)
            };
        } catch (error) {
            return {
                status: 'broken',
                supported: false,
                message: 'Failed to check PDF support'
            };
        }
    }
    
    // 任务完成回调
    onTaskCompleted(task, result) {
        this.notification.add(
            `Report "${task.action.report_name}" downloaded successfully`,
            { type: 'success' }
        );
        
        // 从队列中移除
        this.removeTask(task.id);
        
        // 触发完成事件
        if (task.options.onComplete) {
            task.options.onComplete(task, result);
        }
    }
    
    // 任务失败回调
    onTaskFailed(task, error) {
        this.notification.add(
            `Failed to download report "${task.action.report_name}": ${error.message}`,
            { type: 'danger' }
        );
        
        // 从队列中移除
        this.removeTask(task.id);
        
        // 触发失败事件
        if (task.options.onError) {
            task.options.onError(task, error);
        }
    }
    
    // 移除任务
    removeTask(taskId) {
        const index = this.downloadQueue.findIndex(task => task.id === taskId);
        if (index !== -1) {
            this.downloadQueue.splice(index, 1);
        }
    }
    
    // 获取任务状态
    getTaskStatus(taskId) {
        return this.downloadQueue.find(task => task.id === taskId);
    }
    
    // 取消任务
    cancelTask(taskId) {
        const task = this.downloadQueue.find(task => task.id === taskId);
        if (task && task.status === 'pending') {
            task.status = 'cancelled';
            this.removeTask(taskId);
            return true;
        }
        return false;
    }
    
    // 清理完成的任务
    cleanupCompletedTasks() {
        const cutoff = Date.now() - (24 * 60 * 60 * 1000); // 24小时前
        this.downloadQueue = this.downloadQueue.filter(task => 
            task.status !== 'completed' || task.completedAt > cutoff
        );
    }
    
    // 获取队列状态
    getQueueStatus() {
        const statusCounts = this.downloadQueue.reduce((counts, task) => {
            counts[task.status] = (counts[task.status] || 0) + 1;
            return counts;
        }, {});
        
        return {
            total: this.downloadQueue.length,
            ...statusCounts,
            isProcessing: this.isProcessing
        };
    }
    
    generateTaskId() {
        return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}

// 使用示例
const downloadService = new ReportDownloadService(env);

// 添加下载任务
const taskId = await downloadService.addDownloadTask({
    report_name: 'account.report_invoice',
    context: { active_ids: [123, 456] }
}, 'pdf', {
    onComplete: (task, result) => {
        console.log('Download completed:', task.id);
    },
    onError: (task, error) => {
        console.error('Download failed:', task.id, error);
    }
});

// 检查任务状态
const status = downloadService.getTaskStatus(taskId);
console.log('Task status:', status);

// 获取队列状态
const queueStatus = downloadService.getQueueStatus();
console.log('Queue status:', queueStatus);
```

## 技术特点

### 1. URL构建
- **灵活构建**: 支持多种报表格式的URL构建
- **参数处理**: 正确处理各种URL参数
- **编码安全**: 安全编码URL参数
- **上下文支持**: 完整的上下文参数支持

### 2. 下载管理
- **状态检查**: 检查wkhtmltopdf安装状态
- **错误处理**: 完善的错误处理和用户提示
- **缓存机制**: 缓存状态检查结果
- **异步下载**: 支持异步文件下载

### 3. 错误消息
- **国际化**: 支持多语言错误消息
- **用户友好**: 提供用户友好的错误描述
- **解决方案**: 为每种错误提供解决方案
- **帮助链接**: 提供相关的帮助链接

### 4. 格式支持
- **多格式**: 支持HTML、PDF、文本等格式
- **类型检测**: 智能检测报表类型
- **格式转换**: 支持格式间的转换
- **兼容性**: 确保不同格式的兼容性

## 设计模式

### 1. 工厂模式 (Factory Pattern)
- **URL生成**: 根据参数生成不同类型的URL
- **消息创建**: 根据状态创建相应的错误消息
- **配置生成**: 生成下载配置对象

### 2. 策略模式 (Strategy Pattern)
- **URL策略**: 不同报表类型的URL构建策略
- **下载策略**: 不同格式的下载策略
- **错误策略**: 不同错误类型的处理策略

### 3. 单例模式 (Singleton Pattern)
- **状态缓存**: 缓存wkhtmltopdf状态检查结果
- **配置管理**: 管理全局的报表配置
- **服务实例**: 确保服务的单一实例

## 注意事项

1. **URL编码**: 确保URL参数的正确编码
2. **状态检查**: 定期检查wkhtmltopdf状态
3. **错误处理**: 完善的错误处理和用户反馈
4. **性能优化**: 避免重复的状态检查

## 扩展建议

1. **缓存优化**: 优化URL和状态的缓存机制
2. **批量下载**: 支持批量报表下载
3. **进度跟踪**: 添加下载进度跟踪
4. **格式扩展**: 支持更多报表格式
5. **压缩下载**: 支持报表的压缩下载

该报表工具函数模块为Odoo Web客户端提供了完整的报表URL生成和下载功能，通过简洁的API和完善的错误处理确保了报表系统的稳定性和用户体验。

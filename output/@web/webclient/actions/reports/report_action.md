# ReportAction - 报表动作组件

## 概述

`report_action.js` 是 Odoo Web 客户端的报表动作组件，提供了HTML格式报表的渲染和显示功能。该模块包含63行代码，是一个OWL组件，专门用于在iframe中显示HTML报表，具备控制面板、打印功能、自动链接生成等特性，是Odoo Web客户端中处理HTML报表显示的核心组件。

## 文件信息
- **路径**: `/web/static/src/webclient/actions/reports/report_action.js`
- **行数**: 63
- **模块**: `@web/webclient/actions/reports/report_action`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/hooks'                         // 钩子工具
'@web/search/action_hook'                       // 动作钩子
'@web/search/layout'                            // 搜索布局
'@web/views/view'                               // 视图系统
'@web/webclient/actions/reports/report_hook'    // 报表钩子
'@odoo/owl'                                     // OWL框架
```

## 主组件定义

### 1. ReportAction 组件

```javascript
class ReportAction extends Component {
    static components = { Layout };
    static template = "web.ReportAction";
    static props = ["*"];
    
    setup() {
        useSubEnv({
            config: {
                ...getDefaultConfig(),
                ...this.env.config,
            },
        });
        useSetupAction();

        this.action = useService("action");
        this.title = this.props.display_name || this.props.name;
        this.reportUrl = this.props.report_url;
        this.iframe = useRef("iframe");
        useEnrichWithActionLinks(this.iframe);
    }

    onIframeLoaded(ev) {
        const iframeDocument = ev.target.contentWindow.document;
        iframeDocument.body.classList.add("o_in_iframe", "container-fluid");
        iframeDocument.body.classList.remove("container");
    }

    print() {
        this.iframe.el.contentWindow.print();
    }
}
```

**组件特性**:
- **HTML报表**: 专门用于HTML格式报表的显示
- **iframe渲染**: 在iframe中安全渲染报表内容
- **控制面板**: 集成搜索布局和控制面板
- **打印功能**: 支持报表的打印操作
- **自动链接**: 自动生成Odoo页面链接

## 核心属性

### 1. 基础属性

```javascript
// 报表标题
get title() {
    return this.props.display_name || this.props.name;
}

// 报表URL
get reportUrl() {
    return this.props.report_url;
}

// iframe引用
get iframe() {
    return this._iframe; // useRef("iframe")
}
```

**属性功能**:
- **title**: 报表的显示标题
- **reportUrl**: 报表的访问URL
- **iframe**: iframe元素的引用

### 2. 服务属性

```javascript
// 动作服务
this.action = useService("action");
```

**服务属性功能**:
- **action**: 用于执行其他动作

## 核心功能

### 1. 报表渲染

```javascript
// iframe加载完成处理
onIframeLoaded(ev) {
    const iframeDocument = ev.target.contentWindow.document;
    iframeDocument.body.classList.add("o_in_iframe", "container-fluid");
    iframeDocument.body.classList.remove("container");
}
```

**报表渲染功能**:
- **样式调整**: 调整iframe内容的样式
- **容器设置**: 设置为流体容器布局
- **iframe适配**: 适配iframe环境的显示
- **文档操作**: 直接操作iframe文档

### 2. 打印功能

```javascript
// 打印报表
print() {
    this.iframe.el.contentWindow.print();
}
```

**打印功能**:
- **浏览器打印**: 调用浏览器的打印功能
- **iframe打印**: 打印iframe中的内容
- **用户友好**: 提供便捷的打印操作
- **跨平台**: 支持不同平台的打印

### 3. 自动链接

```javascript
// 使用报表钩子增强链接
useEnrichWithActionLinks(this.iframe);
```

**自动链接功能**:
- **链接检测**: 自动检测报表中的链接元素
- **动作绑定**: 为链接绑定Odoo动作
- **导航增强**: 增强报表内的导航体验
- **选择器匹配**: 匹配特定的选择器模式

## 使用场景

### 1. 报表显示管理器

```javascript
// 报表显示管理器
class ReportDisplayManager {
    constructor(env) {
        this.env = env;
        this.activeReports = new Map();
        this.reportCache = new Map();
    }
    
    // 显示HTML报表
    async showHTMLReport(reportName, data = {}, options = {}) {
        try {
            // 生成报表URL
            const reportUrl = await this.generateReportUrl(reportName, data, 'html');
            
            // 创建报表动作
            const reportAction = {
                type: 'ir.actions.client',
                tag: 'report_action',
                name: options.title || reportName,
                display_name: options.title || reportName,
                report_url: reportUrl,
                target: options.target || 'current'
            };
            
            // 执行报表动作
            const result = await this.env.services.action.doAction(reportAction);
            
            // 记录活动报表
            const reportId = this.generateReportId(reportName, data);
            this.activeReports.set(reportId, {
                name: reportName,
                data: data,
                url: reportUrl,
                action: reportAction,
                createdAt: new Date()
            });
            
            return result;
        } catch (error) {
            console.error('Failed to show HTML report:', error);
            throw error;
        }
    }
    
    // 生成报表URL
    async generateReportUrl(reportName, data, format = 'html') {
        const params = new URLSearchParams({
            report_name: reportName,
            format: format,
            data: JSON.stringify(data),
            context: JSON.stringify(this.env.context || {})
        });
        
        return `/web/report/${format}/${reportName}?${params.toString()}`;
    }
    
    // 打印报表
    async printReport(reportName, data = {}, options = {}) {
        try {
            if (options.format === 'html') {
                // HTML格式直接显示并打印
                await this.showHTMLReport(reportName, data, {
                    ...options,
                    autoPrint: true
                });
            } else {
                // PDF格式下载打印
                const pdfUrl = await this.generateReportUrl(reportName, data, 'pdf');
                window.open(pdfUrl, '_blank');
            }
        } catch (error) {
            console.error('Failed to print report:', error);
            this.env.services.notification.add(
                'Print failed: ' + error.message,
                { type: 'danger' }
            );
        }
    }
    
    // 下载报表
    async downloadReport(reportName, data = {}, format = 'pdf') {
        try {
            const reportUrl = await this.generateReportUrl(reportName, data, format);
            
            // 创建下载链接
            const link = document.createElement('a');
            link.href = reportUrl;
            link.download = `${reportName}.${format}`;
            link.style.display = 'none';
            
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            this.env.services.notification.add(
                'Report download started',
                { type: 'success' }
            );
        } catch (error) {
            console.error('Failed to download report:', error);
            this.env.services.notification.add(
                'Download failed: ' + error.message,
                { type: 'danger' }
            );
        }
    }
    
    // 预览报表
    async previewReport(reportName, data = {}, options = {}) {
        const previewOptions = {
            ...options,
            target: 'new',
            title: `Preview: ${reportName}`
        };
        
        return this.showHTMLReport(reportName, data, previewOptions);
    }
    
    // 发送报表邮件
    async emailReport(reportName, data = {}, emailOptions = {}) {
        try {
            const reportUrl = await this.generateReportUrl(reportName, data, 'pdf');
            
            return this.env.services.action.doAction({
                type: 'ir.actions.act_window',
                res_model: 'mail.compose.message',
                view_mode: 'form',
                target: 'new',
                context: {
                    default_attachment_ids: [{
                        name: `${reportName}.pdf`,
                        url: reportUrl,
                        type: 'url'
                    }],
                    default_subject: emailOptions.subject || `Report: ${reportName}`,
                    default_body: emailOptions.body || 'Please find the attached report.',
                    ...emailOptions.context
                }
            });
        } catch (error) {
            console.error('Failed to email report:', error);
            throw error;
        }
    }
    
    // 获取报表状态
    getReportStatus(reportId) {
        return this.activeReports.get(reportId);
    }
    
    // 关闭报表
    closeReport(reportId) {
        const report = this.activeReports.get(reportId);
        if (report) {
            this.activeReports.delete(reportId);
            return true;
        }
        return false;
    }
    
    // 清理过期报表
    cleanupExpiredReports(maxAge = 3600000) { // 1小时
        const now = new Date();
        for (const [reportId, report] of this.activeReports) {
            if (now - report.createdAt > maxAge) {
                this.activeReports.delete(reportId);
            }
        }
    }
    
    generateReportId(reportName, data) {
        const dataHash = this.hashObject(data);
        return `${reportName}_${dataHash}`;
    }
    
    hashObject(obj) {
        const str = JSON.stringify(obj);
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash).toString(36);
    }
}

// 使用示例
const reportManager = new ReportDisplayManager(env);

// 显示HTML报表
await reportManager.showHTMLReport('sale.report_saleorder', {
    order_ids: [123, 456]
}, {
    title: 'Sales Orders Report'
});

// 打印报表
await reportManager.printReport('account.report_invoice', {
    invoice_ids: [789]
}, {
    format: 'pdf'
});

// 下载报表
await reportManager.downloadReport('stock.report_picking', {
    picking_ids: [101, 102]
}, 'pdf');
```

### 2. 报表组件扩展

```javascript
// 扩展的报表动作组件
class ExtendedReportAction extends ReportAction {
    static template = xml`
        <Layout display="display">
            <t t-set-slot="control-panel-navigation-additional">
                <div class="report-controls">
                    <button class="btn btn-primary" 
                            t-on-click="print"
                            title="Print Report">
                        <i class="fa fa-print"/> Print
                    </button>
                    <button class="btn btn-secondary" 
                            t-on-click="downloadPDF"
                            title="Download PDF">
                        <i class="fa fa-download"/> PDF
                    </button>
                    <button class="btn btn-secondary" 
                            t-on-click="emailReport"
                            title="Email Report">
                        <i class="fa fa-envelope"/> Email
                    </button>
                    <button class="btn btn-secondary" 
                            t-on-click="refreshReport"
                            title="Refresh Report">
                        <i class="fa fa-refresh"/> Refresh
                    </button>
                </div>
            </t>
            
            <t t-set-slot="layout-buttons">
                <div class="report-info">
                    <span class="report-title" t-esc="title"/>
                    <span class="report-status" t-if="isLoading">
                        <i class="fa fa-spinner fa-spin"/> Loading...
                    </span>
                </div>
            </t>
            
            <div class="report-container">
                <iframe t-ref="iframe" 
                        t-att-src="reportUrl"
                        t-on-load="onIframeLoaded"
                        class="report-iframe"
                        frameborder="0"/>
                        
                <div t-if="error" class="report-error">
                    <div class="alert alert-danger">
                        <h4>Report Error</h4>
                        <p t-esc="error.message"/>
                        <button class="btn btn-primary" t-on-click="retryLoad">
                            Retry
                        </button>
                    </div>
                </div>
                
                <div t-if="isLoading" class="report-loading">
                    <div class="loading-spinner">
                        <i class="fa fa-spinner fa-spin fa-3x"/>
                        <p>Generating report...</p>
                    </div>
                </div>
            </div>
        </Layout>`;
    
    setup() {
        super.setup();
        this.isLoading = true;
        this.error = null;
        this.reportData = this.props.data || {};
        this.reportName = this.props.report_name;
    }
    
    onIframeLoaded(ev) {
        super.onIframeLoaded(ev);
        this.isLoading = false;
        this.error = null;
        
        // 添加自定义样式
        const iframeDocument = ev.target.contentWindow.document;
        this.addCustomStyles(iframeDocument);
        
        // 添加交互功能
        this.addInteractivity(iframeDocument);
        
        this.render();
    }
    
    addCustomStyles(document) {
        const style = document.createElement('style');
        style.textContent = `
            .o_report_layout_standard {
                font-family: 'Roboto', sans-serif;
            }
            .report-header {
                border-bottom: 2px solid #875A7B;
                margin-bottom: 20px;
            }
            .report-footer {
                border-top: 1px solid #ddd;
                margin-top: 20px;
                padding-top: 10px;
            }
            @media print {
                .no-print { display: none !important; }
            }
        `;
        document.head.appendChild(style);
    }
    
    addInteractivity(document) {
        // 添加点击事件监听
        document.addEventListener('click', (event) => {
            const target = event.target;
            
            // 处理记录链接
            if (target.hasAttribute('data-res-model') && target.hasAttribute('data-res-id')) {
                event.preventDefault();
                this.openRecord(
                    target.getAttribute('data-res-model'),
                    parseInt(target.getAttribute('data-res-id'))
                );
            }
            
            // 处理动作链接
            if (target.hasAttribute('data-action')) {
                event.preventDefault();
                this.executeAction(target.getAttribute('data-action'));
            }
        });
    }
    
    async downloadPDF() {
        try {
            const pdfUrl = this.reportUrl.replace('/html/', '/pdf/');
            const link = document.createElement('a');
            link.href = pdfUrl;
            link.download = `${this.reportName}.pdf`;
            link.click();
        } catch (error) {
            this.handleError(error);
        }
    }
    
    async emailReport() {
        try {
            return this.env.services.action.doAction({
                type: 'ir.actions.act_window',
                res_model: 'mail.compose.message',
                view_mode: 'form',
                target: 'new',
                context: {
                    default_subject: `Report: ${this.title}`,
                    default_attachment_ids: [{
                        name: `${this.reportName}.pdf`,
                        url: this.reportUrl.replace('/html/', '/pdf/'),
                        type: 'url'
                    }]
                }
            });
        } catch (error) {
            this.handleError(error);
        }
    }
    
    async refreshReport() {
        this.isLoading = true;
        this.error = null;
        this.render();
        
        try {
            // 重新加载iframe
            this.iframe.el.src = this.reportUrl + '&_=' + Date.now();
        } catch (error) {
            this.handleError(error);
        }
    }
    
    async retryLoad() {
        this.error = null;
        await this.refreshReport();
    }
    
    async openRecord(resModel, resId) {
        return this.env.services.action.doAction({
            type: 'ir.actions.act_window',
            res_model: resModel,
            res_id: resId,
            view_mode: 'form',
            target: 'new'
        });
    }
    
    async executeAction(actionXmlId) {
        try {
            const action = await this.env.services.orm.call(
                'ir.actions.actions',
                'get_action_by_xml_id',
                [actionXmlId]
            );
            return this.env.services.action.doAction(action);
        } catch (error) {
            this.handleError(error);
        }
    }
    
    handleError(error) {
        console.error('Report error:', error);
        this.error = error;
        this.isLoading = false;
        this.render();
        
        this.env.services.notification.add(
            'Report error: ' + error.message,
            { type: 'danger' }
        );
    }
    
    get display() {
        return {
            controlPanel: {
                'top-right': false,
                'bottom-right': false,
            }
        };
    }
}

// 注册扩展组件
registry.category("actions").add("extended_report_action", ExtendedReportAction);
```

## 技术特点

### 1. HTML报表专用
- **HTML渲染**: 专门用于HTML格式报表的渲染
- **iframe安全**: 在iframe中安全显示报表内容
- **样式控制**: 控制报表的显示样式
- **打印支持**: 完整的打印功能支持

### 2. 控制面板集成
- **布局组件**: 集成搜索布局组件
- **动作钩子**: 使用动作钩子进行设置
- **配置继承**: 继承默认视图配置
- **环境扩展**: 扩展子环境配置

### 3. 自动链接增强
- **链接检测**: 自动检测报表中的链接
- **动作绑定**: 为链接绑定相应的动作
- **导航增强**: 增强报表内的导航体验
- **选择器匹配**: 智能匹配链接选择器

### 4. 用户体验
- **即时加载**: 快速加载报表内容
- **打印便捷**: 便捷的打印操作
- **样式适配**: 适配iframe环境的样式
- **错误处理**: 完善的错误处理机制

## 设计模式

### 1. 组件模式 (Component Pattern)
- **组件封装**: 将报表功能封装为组件
- **属性传递**: 通过属性传递配置信息
- **事件处理**: 处理组件内的各种事件

### 2. 装饰器模式 (Decorator Pattern)
- **功能增强**: 通过钩子增强基础功能
- **链接装饰**: 装饰报表中的链接元素
- **样式装饰**: 装饰iframe的样式

### 3. 策略模式 (Strategy Pattern)
- **渲染策略**: 不同格式报表的渲染策略
- **打印策略**: 不同的打印处理策略
- **链接策略**: 不同类型链接的处理策略

## 注意事项

1. **安全性**: 确保iframe内容的安全性
2. **性能**: 优化大型报表的加载性能
3. **兼容性**: 确保不同浏览器的兼容性
4. **打印质量**: 保证打印输出的质量

## 扩展建议

1. **缓存机制**: 添加报表缓存机制
2. **批量打印**: 支持批量报表打印
3. **导出格式**: 支持更多导出格式
4. **实时更新**: 支持报表数据的实时更新
5. **交互增强**: 增强报表的交互功能

该报表动作组件为Odoo Web客户端提供了完整的HTML报表显示功能，通过iframe渲染和自动链接增强确保了报表的安全性和用户体验。

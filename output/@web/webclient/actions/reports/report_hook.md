# Report Hook - 报表钩子

## 概述

`report_hook.js` 是 Odoo Web 客户端的报表钩子模块，提供了自动为HTML内容添加动作链接的功能。该模块包含71行代码，定义了 `useEnrichWithActionLinks` 钩子函数，用于自动检测HTML元素中的特定属性并将其转换为可点击的动作链接，具备iframe支持、选择器过滤、自动链接生成等特性，是Odoo Web报表系统中增强用户交互体验的核心工具。

## 文件信息
- **路径**: `/web/static/src/webclient/actions/reports/report_hook.js`
- **行数**: 71
- **模块**: `@web/webclient/actions/reports/report_hook`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'  // OWL框架
```

## 核心钩子函数

### 1. useEnrichWithActionLinks 钩子

```javascript
/**
 * Hook used to enrich html and provide automatic links to action.
 * Dom elements must have those attrs [res-id][res-model][view-type]
 * Each element with those attrs will become a link to the specified resource.
 * Works with Iframes.
 *
 * @param {owl reference} ref Owl ref to the element to enrich
 * @param {string} [selector] Selector to apply to the element resolved by the ref.
 */
function useEnrichWithActionLinks(ref, selector = null) {
    const comp = useComponent();
    useEffect(
        (element) => {
            // If we get an iframe, we need to wait until everything is loaded
            if (element.matches("iframe")) {
                element.onload = () => enrich(comp, element, selector, true);
            } else {
                enrich(comp, element, selector);
            }
        },
        () => [ref.el]
    );
}
```

**钩子功能**:
- **自动链接**: 自动为HTML元素添加动作链接
- **属性检测**: 检测具有特定属性的DOM元素
- **iframe支持**: 支持iframe内容的链接增强
- **选择器过滤**: 支持使用选择器过滤目标元素

**参数说明**:
- **ref**: OWL组件的元素引用
- **selector**: 可选的CSS选择器，用于过滤目标元素

### 2. enrich 核心函数

```javascript
function enrich(component, targetElement, selector, isIFrame = false) {
    let doc = window.document;

    // If we are in an iframe, we need to take the right document
    // both for the element and the doc
    if (isIFrame) {
        targetElement = targetElement.contentDocument;
        doc = targetElement;
    }

    // If there are selector, we may have multiple blocks of code to enrich
    const targets = [];
    if (selector) {
        targets.push(...targetElement.querySelectorAll(selector));
    } else {
        targets.push(targetElement);
    }

    // Search the elements with the selector, update them and bind an action.
    for (const currentTarget of targets) {
        const elementsToWrap = currentTarget.querySelectorAll("[res-id][res-model][view-type]");
        for (const element of elementsToWrap.values()) {
            const wrapper = doc.createElement("a");
            wrapper.setAttribute("href", "#");
            wrapper.addEventListener("click", (ev) => {
                ev.preventDefault();
                component.env.services.action.doAction({
                    type: "ir.actions.act_window",
                    view_mode: element.getAttribute("view-type"),
                    res_id: Number(element.getAttribute("res-id")),
                    res_model: element.getAttribute("res-model"),
                    views: [[element.getAttribute("view-id"), element.getAttribute("view-type")]],
                });
            });
            element.parentNode.insertBefore(wrapper, element);
            wrapper.appendChild(element);
        }
    }
}
```

**增强功能**:
- **文档处理**: 正确处理普通文档和iframe文档
- **元素查找**: 查找具有特定属性的元素
- **链接包装**: 用链接元素包装目标元素
- **动作绑定**: 为链接绑定动作执行事件

## 核心功能

### 1. 属性检测

```javascript
// 检测具有特定属性的元素
const elementsToWrap = currentTarget.querySelectorAll("[res-id][res-model][view-type]");
```

**属性检测功能**:
- **res-id**: 资源记录的ID
- **res-model**: 资源的模型名称
- **view-type**: 视图类型（form、list等）
- **view-id**: 可选的视图ID

### 2. 链接生成

```javascript
// 创建链接包装器
const wrapper = doc.createElement("a");
wrapper.setAttribute("href", "#");

// 绑定点击事件
wrapper.addEventListener("click", (ev) => {
    ev.preventDefault();
    component.env.services.action.doAction({
        type: "ir.actions.act_window",
        view_mode: element.getAttribute("view-type"),
        res_id: Number(element.getAttribute("res-id")),
        res_model: element.getAttribute("res-model"),
        views: [[element.getAttribute("view-id"), element.getAttribute("view-type")]],
    });
});
```

**链接生成功能**:
- **包装器创建**: 创建链接包装器元素
- **事件绑定**: 绑定点击事件处理器
- **动作执行**: 执行相应的窗口动作
- **属性映射**: 将HTML属性映射为动作参数

### 3. iframe支持

```javascript
// iframe处理
if (element.matches("iframe")) {
    element.onload = () => enrich(comp, element, selector, true);
} else {
    enrich(comp, element, selector);
}

// iframe文档处理
if (isIFrame) {
    targetElement = targetElement.contentDocument;
    doc = targetElement;
}
```

**iframe支持功能**:
- **加载等待**: 等待iframe内容完全加载
- **文档切换**: 切换到iframe的文档上下文
- **跨框架**: 支持跨iframe的元素操作
- **安全访问**: 安全访问iframe内容

## 使用场景

### 1. 报表链接增强

```javascript
// 报表组件中使用链接增强
class EnhancedReportComponent extends Component {
    static template = xml`
        <div class="report-container">
            <iframe t-ref="reportFrame" 
                    t-att-src="reportUrl"
                    class="report-iframe"/>
        </div>`;
    
    setup() {
        this.reportFrame = useRef("reportFrame");
        
        // 使用报表钩子增强链接
        useEnrichWithActionLinks(this.reportFrame);
    }
}

// HTML报表内容示例
const reportHTML = `
<div class="report-content">
    <h1>Sales Report</h1>
    <table>
        <tr>
            <td>Customer:</td>
            <td res-id="123" res-model="res.partner" view-type="form">
                John Doe
            </td>
        </tr>
        <tr>
            <td>Order:</td>
            <td res-id="456" res-model="sale.order" view-type="form">
                SO001
            </td>
        </tr>
    </table>
</div>`;
```

### 2. 自定义链接增强器

```javascript
// 自定义链接增强器
class CustomLinkEnhancer {
    constructor(env) {
        this.env = env;
        this.enhancedElements = new WeakSet();
    }
    
    // 增强指定容器中的链接
    enhanceContainer(container, options = {}) {
        if (this.enhancedElements.has(container)) {
            return; // 避免重复增强
        }
        
        const selector = options.selector || "[res-id][res-model][view-type]";
        const elements = container.querySelectorAll(selector);
        
        for (const element of elements) {
            this.enhanceElement(element, options);
        }
        
        this.enhancedElements.add(container);
    }
    
    // 增强单个元素
    enhanceElement(element, options = {}) {
        if (element.hasAttribute('data-enhanced')) {
            return; // 已经增强过
        }
        
        const resId = element.getAttribute('res-id');
        const resModel = element.getAttribute('res-model');
        const viewType = element.getAttribute('view-type');
        const viewId = element.getAttribute('view-id');
        
        if (!resId || !resModel || !viewType) {
            return; // 缺少必要属性
        }
        
        // 创建链接包装器
        const wrapper = this.createLinkWrapper(element, {
            resId: parseInt(resId),
            resModel,
            viewType,
            viewId: viewId ? parseInt(viewId) : null,
            ...options
        });
        
        // 替换原元素
        element.parentNode.insertBefore(wrapper, element);
        wrapper.appendChild(element);
        
        // 标记为已增强
        element.setAttribute('data-enhanced', 'true');
    }
    
    // 创建链接包装器
    createLinkWrapper(element, config) {
        const wrapper = document.createElement('a');
        wrapper.setAttribute('href', '#');
        wrapper.className = 'odoo-action-link';
        
        // 添加样式
        wrapper.style.textDecoration = config.underline !== false ? 'underline' : 'none';
        wrapper.style.color = config.color || '#875A7B';
        wrapper.style.cursor = 'pointer';
        
        // 添加悬停效果
        wrapper.addEventListener('mouseenter', () => {
            wrapper.style.opacity = '0.8';
        });
        
        wrapper.addEventListener('mouseleave', () => {
            wrapper.style.opacity = '1';
        });
        
        // 绑定点击事件
        wrapper.addEventListener('click', (event) => {
            event.preventDefault();
            this.executeAction(config);
        });
        
        // 添加右键菜单
        wrapper.addEventListener('contextmenu', (event) => {
            event.preventDefault();
            this.showContextMenu(event, config);
        });
        
        return wrapper;
    }
    
    // 执行动作
    async executeAction(config) {
        try {
            const action = {
                type: 'ir.actions.act_window',
                res_model: config.resModel,
                res_id: config.resId,
                view_mode: config.viewType,
                target: config.target || 'current'
            };
            
            if (config.viewId) {
                action.views = [[config.viewId, config.viewType]];
            }
            
            await this.env.services.action.doAction(action);
        } catch (error) {
            console.error('Failed to execute action:', error);
            this.env.services.notification.add(
                'Failed to open record: ' + error.message,
                { type: 'danger' }
            );
        }
    }
    
    // 显示右键菜单
    showContextMenu(event, config) {
        const menu = document.createElement('div');
        menu.className = 'context-menu';
        menu.style.position = 'fixed';
        menu.style.left = event.clientX + 'px';
        menu.style.top = event.clientY + 'px';
        menu.style.background = 'white';
        menu.style.border = '1px solid #ccc';
        menu.style.borderRadius = '4px';
        menu.style.padding = '8px';
        menu.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';
        menu.style.zIndex = '9999';
        
        const menuItems = [
            {
                label: 'Open in Form View',
                action: () => this.executeAction({ ...config, viewType: 'form' })
            },
            {
                label: 'Open in New Window',
                action: () => this.executeAction({ ...config, target: 'new' })
            },
            {
                label: 'Copy Link',
                action: () => this.copyLink(config)
            }
        ];
        
        menuItems.forEach(item => {
            const menuItem = document.createElement('div');
            menuItem.textContent = item.label;
            menuItem.style.padding = '4px 8px';
            menuItem.style.cursor = 'pointer';
            menuItem.addEventListener('click', () => {
                item.action();
                document.body.removeChild(menu);
            });
            menuItem.addEventListener('mouseenter', () => {
                menuItem.style.backgroundColor = '#f0f0f0';
            });
            menuItem.addEventListener('mouseleave', () => {
                menuItem.style.backgroundColor = 'transparent';
            });
            menu.appendChild(menuItem);
        });
        
        document.body.appendChild(menu);
        
        // 点击其他地方关闭菜单
        const closeMenu = (e) => {
            if (!menu.contains(e.target)) {
                document.body.removeChild(menu);
                document.removeEventListener('click', closeMenu);
            }
        };
        setTimeout(() => document.addEventListener('click', closeMenu), 0);
    }
    
    // 复制链接
    async copyLink(config) {
        try {
            const url = `/web#id=${config.resId}&model=${config.resModel}&view_type=${config.viewType}`;
            await navigator.clipboard.writeText(window.location.origin + url);
            this.env.services.notification.add(
                'Link copied to clipboard',
                { type: 'success' }
            );
        } catch (error) {
            console.error('Failed to copy link:', error);
        }
    }
    
    // 批量增强
    enhanceBatch(containers, options = {}) {
        const results = [];
        
        for (const container of containers) {
            try {
                this.enhanceContainer(container, options);
                results.push({ container, success: true });
            } catch (error) {
                console.error('Failed to enhance container:', error);
                results.push({ container, success: false, error });
            }
        }
        
        return results;
    }
    
    // 移除增强
    removeEnhancement(container) {
        const enhancedLinks = container.querySelectorAll('a.odoo-action-link');
        
        for (const link of enhancedLinks) {
            const originalElement = link.firstChild;
            if (originalElement) {
                originalElement.removeAttribute('data-enhanced');
                link.parentNode.insertBefore(originalElement, link);
                link.parentNode.removeChild(link);
            }
        }
        
        this.enhancedElements.delete(container);
    }
    
    // 重新增强
    reenhance(container, options = {}) {
        this.removeEnhancement(container);
        this.enhanceContainer(container, options);
    }
}

// 使用示例
const linkEnhancer = new CustomLinkEnhancer(env);

// 增强报表容器
const reportContainer = document.querySelector('.report-content');
linkEnhancer.enhanceContainer(reportContainer, {
    color: '#007bff',
    underline: true,
    target: 'new'
});

// 批量增强多个容器
const containers = document.querySelectorAll('.report-section');
linkEnhancer.enhanceBatch(containers);
```

### 3. 高级链接增强钩子

```javascript
// 高级链接增强钩子
function useAdvancedLinkEnhancement(ref, options = {}) {
    const component = useComponent();
    const env = component.env;
    
    useEffect(
        (element) => {
            if (!element) return;
            
            const enhancer = new AdvancedLinkEnhancer(env, options);
            
            if (element.matches('iframe')) {
                element.onload = () => {
                    const doc = element.contentDocument;
                    if (doc) {
                        enhancer.enhanceDocument(doc);
                    }
                };
            } else {
                enhancer.enhanceElement(element);
            }
            
            // 清理函数
            return () => {
                enhancer.cleanup();
            };
        },
        () => [ref.el]
    );
}

class AdvancedLinkEnhancer {
    constructor(env, options = {}) {
        this.env = env;
        this.options = {
            selector: '[res-id][res-model][view-type]',
            linkClass: 'odoo-enhanced-link',
            hoverEffect: true,
            contextMenu: true,
            analytics: false,
            ...options
        };
        this.enhancedElements = new Set();
        this.observers = [];
    }
    
    enhanceDocument(document) {
        this.enhanceElement(document.body);
        
        // 监听动态内容变化
        if (this.options.watchChanges !== false) {
            this.setupMutationObserver(document);
        }
    }
    
    enhanceElement(element) {
        const targets = element.querySelectorAll(this.options.selector);
        
        for (const target of targets) {
            if (!this.enhancedElements.has(target)) {
                this.createEnhancedLink(target);
                this.enhancedElements.add(target);
            }
        }
    }
    
    createEnhancedLink(element) {
        const config = this.extractConfig(element);
        if (!config) return;
        
        const wrapper = document.createElement('a');
        wrapper.href = '#';
        wrapper.className = this.options.linkClass;
        
        // 应用样式
        this.applyStyles(wrapper, element);
        
        // 绑定事件
        this.bindEvents(wrapper, config);
        
        // 包装元素
        element.parentNode.insertBefore(wrapper, element);
        wrapper.appendChild(element);
        
        // 添加数据属性
        wrapper.setAttribute('data-res-id', config.resId);
        wrapper.setAttribute('data-res-model', config.resModel);
        wrapper.setAttribute('data-view-type', config.viewType);
    }
    
    extractConfig(element) {
        const resId = element.getAttribute('res-id');
        const resModel = element.getAttribute('res-model');
        const viewType = element.getAttribute('view-type');
        
        if (!resId || !resModel || !viewType) {
            return null;
        }
        
        return {
            resId: parseInt(resId),
            resModel,
            viewType,
            viewId: element.getAttribute('view-id'),
            target: element.getAttribute('target') || 'current',
            context: element.getAttribute('context')
        };
    }
    
    applyStyles(wrapper, element) {
        wrapper.style.textDecoration = 'none';
        wrapper.style.color = 'inherit';
        wrapper.style.cursor = 'pointer';
        
        if (this.options.hoverEffect) {
            wrapper.addEventListener('mouseenter', () => {
                wrapper.style.textDecoration = 'underline';
                wrapper.style.color = '#875A7B';
            });
            
            wrapper.addEventListener('mouseleave', () => {
                wrapper.style.textDecoration = 'none';
                wrapper.style.color = 'inherit';
            });
        }
    }
    
    bindEvents(wrapper, config) {
        // 点击事件
        wrapper.addEventListener('click', (event) => {
            event.preventDefault();
            this.handleClick(config);
        });
        
        // 右键菜单
        if (this.options.contextMenu) {
            wrapper.addEventListener('contextmenu', (event) => {
                event.preventDefault();
                this.showContextMenu(event, config);
            });
        }
        
        // 分析统计
        if (this.options.analytics) {
            wrapper.addEventListener('click', () => {
                this.trackClick(config);
            });
        }
    }
    
    async handleClick(config) {
        try {
            const action = {
                type: 'ir.actions.act_window',
                res_model: config.resModel,
                res_id: config.resId,
                view_mode: config.viewType,
                target: config.target
            };
            
            if (config.viewId) {
                action.views = [[parseInt(config.viewId), config.viewType]];
            }
            
            if (config.context) {
                action.context = JSON.parse(config.context);
            }
            
            await this.env.services.action.doAction(action);
        } catch (error) {
            console.error('Link action failed:', error);
            this.env.services.notification.add(
                'Failed to open link: ' + error.message,
                { type: 'danger' }
            );
        }
    }
    
    setupMutationObserver(document) {
        const observer = new MutationObserver((mutations) => {
            for (const mutation of mutations) {
                if (mutation.type === 'childList') {
                    for (const node of mutation.addedNodes) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            this.enhanceElement(node);
                        }
                    }
                }
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        this.observers.push(observer);
    }
    
    trackClick(config) {
        // 发送分析数据
        if (this.env.services.analytics) {
            this.env.services.analytics.track('link_click', {
                res_model: config.resModel,
                view_type: config.viewType,
                timestamp: Date.now()
            });
        }
    }
    
    cleanup() {
        // 清理观察者
        for (const observer of this.observers) {
            observer.disconnect();
        }
        this.observers = [];
        
        // 清理增强元素
        this.enhancedElements.clear();
    }
}

// 使用高级钩子
function MyReportComponent() {
    const reportRef = useRef('report');
    
    useAdvancedLinkEnhancement(reportRef, {
        hoverEffect: true,
        contextMenu: true,
        analytics: true,
        watchChanges: true
    });
    
    return xml`
        <div t-ref="report" class="report-container">
            <!-- 报表内容 -->
        </div>`;
}
```

## 技术特点

### 1. 自动化链接
- **属性检测**: 自动检测HTML元素的特定属性
- **链接生成**: 自动生成可点击的动作链接
- **事件绑定**: 自动绑定点击事件处理器
- **动作执行**: 自动执行相应的Odoo动作

### 2. iframe支持
- **跨框架**: 支持iframe内容的链接增强
- **文档切换**: 正确处理iframe文档上下文
- **加载等待**: 等待iframe内容完全加载
- **安全访问**: 安全访问iframe内容

### 3. 选择器过滤
- **灵活选择**: 支持CSS选择器过滤目标元素
- **批量处理**: 支持批量处理多个元素
- **精确控制**: 精确控制要增强的元素范围
- **性能优化**: 优化大量元素的处理性能

### 4. OWL集成
- **钩子模式**: 使用OWL钩子模式实现
- **生命周期**: 与组件生命周期集成
- **响应式**: 支持响应式的链接更新
- **组件化**: 完全组件化的设计

## 设计模式

### 1. 钩子模式 (Hook Pattern)
- **功能封装**: 将链接增强功能封装为钩子
- **可重用**: 在多个组件中重用链接增强功能
- **生命周期**: 与组件生命周期自动集成

### 2. 装饰器模式 (Decorator Pattern)
- **功能增强**: 为HTML元素添加链接功能
- **透明包装**: 透明地包装原有元素
- **行为扩展**: 扩展元素的交互行为

### 3. 观察者模式 (Observer Pattern)
- **事件监听**: 监听元素的点击事件
- **状态变化**: 响应元素状态的变化
- **自动更新**: 自动更新链接状态

## 注意事项

1. **安全性**: 确保iframe内容访问的安全性
2. **性能**: 避免对大量元素进行不必要的处理
3. **兼容性**: 确保在不同浏览器中的兼容性
4. **内存泄漏**: 避免事件监听器的内存泄漏

## 扩展建议

1. **缓存机制**: 添加元素处理的缓存机制
2. **批量处理**: 优化大量元素的批量处理
3. **自定义动作**: 支持自定义动作类型
4. **样式定制**: 提供更多的样式定制选项
5. **分析统计**: 集成链接点击的分析统计

该报表钩子模块为Odoo Web客户端提供了强大的自动链接增强功能，通过简洁的API和灵活的配置确保了报表内容的良好交互体验。

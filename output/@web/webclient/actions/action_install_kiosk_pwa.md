# InstallKioskPWA - Kiosk PWA安装动作

## 概述

`action_install_kiosk_pwa.js` 是 Odoo Web 客户端的Kiosk PWA安装动作组件，提供了在对话框中显示Kiosk URL并安装对应PWA的功能。该模块包含43行代码，是一个OWL组件，专门用于处理Kiosk模式的PWA（Progressive Web App）安装，具备URL生成、PWA安装链接、对话框显示等特性，是Odoo Web客户端中处理Kiosk应用安装的专用组件。

## 文件信息
- **路径**: `/web/static/src/webclient/actions/action_install_kiosk_pwa.js`
- **行数**: 43
- **模块**: `@web/webclient/actions/action_install_kiosk_pwa`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                        // 注册表系统
'@web/core/utils/hooks'                     // 钩子工具
'@web/webclient/actions/action_service'     // 动作服务
'@odoo/owl'                                 // OWL框架
```

## 主组件定义

### 1. InstallKiosk 组件

```javascript
class InstallKiosk extends Component {
    static template = "web.ActionInstallKioskPWA";
    static props = { ...standardActionServiceProps };

    setup() {
        this.resModel = this.props.action.res_model;
        this.orm = useService("orm");
        this.dialog = useService("dialog");
        
        onWillStart(async () => {
            this.url = await this.orm.call(this.resModel, "get_kiosk_url", [
                this.props.action.context.active_id,
            ]);
        });
    }

    get appId() {
        return this.props.action.context.app_id || this.resModel;
    }

    get installURL() {
        return `/scoped_app?app_id=${this.appId}&path=${encodeURIComponent(
            this.url.replace(document.location.origin + "/", "")
        )}`;
    }
}

// 注册动作
registry.category("actions").add("install_kiosk_pwa", InstallKiosk);
```

**组件特性**:
- **PWA安装**: 专门用于PWA应用的安装
- **Kiosk模式**: 支持Kiosk模式的应用
- **URL生成**: 动态生成安装URL
- **对话框显示**: 在对话框中显示安装界面

## 核心属性

### 1. 基础属性

```javascript
// 资源模型
get resModel() {
    return this.props.action.res_model;
}

// 应用ID
get appId() {
    return this.props.action.context.app_id || this.resModel;
}

// Kiosk URL
get url() {
    return this._url; // 从服务器获取的URL
}

// 安装URL
get installURL() {
    return `/scoped_app?app_id=${this.appId}&path=${encodeURIComponent(
        this.url.replace(document.location.origin + "/", "")
    )}`;
}
```

**属性功能**:
- **resModel**: 关联的资源模型
- **appId**: PWA应用的唯一标识符
- **url**: Kiosk应用的访问URL
- **installURL**: PWA安装的完整URL

### 2. 服务属性

```javascript
// ORM服务
this.orm = useService("orm");

// 对话框服务
this.dialog = useService("dialog");
```

**服务属性功能**:
- **orm**: 用于调用服务器端方法
- **dialog**: 用于对话框管理

## 核心功能

### 1. URL获取

```javascript
onWillStart(async () => {
    this.url = await this.orm.call(this.resModel, "get_kiosk_url", [
        this.props.action.context.active_id,
    ]);
});
```

**URL获取功能**:
- **异步加载**: 组件启动时异步获取URL
- **服务器调用**: 调用服务器端方法获取Kiosk URL
- **上下文传递**: 传递活动记录ID到服务器
- **错误处理**: 处理URL获取过程中的错误

### 2. 安装URL生成

```javascript
get installURL() {
    return `/scoped_app?app_id=${this.appId}&path=${encodeURIComponent(
        this.url.replace(document.location.origin + "/", "")
    )}`;
}
```

**安装URL生成功能**:
- **路径编码**: 对URL路径进行编码处理
- **相对路径**: 转换为相对路径格式
- **参数构建**: 构建PWA安装所需的参数
- **URL安全**: 确保URL的安全性和正确性

### 3. PWA安装

```javascript
// PWA安装逻辑（在模板中实现）
installPWA() {
    // 打开安装URL
    window.open(this.installURL, '_blank');
}
```

**PWA安装功能**:
- **新窗口打开**: 在新窗口中打开安装URL
- **安装引导**: 引导用户完成PWA安装
- **平台适配**: 适配不同平台的安装方式
- **用户体验**: 提供良好的安装体验

## 使用场景

### 1. Kiosk应用管理

```javascript
// Kiosk应用管理器
class KioskAppManager {
    constructor(env) {
        this.env = env;
        this.installedApps = new Map();
        this.availableApps = [];
    }
    
    // 显示PWA安装对话框
    async showInstallDialog(resModel, resId, options = {}) {
        return this.env.services.action.doAction({
            type: 'ir.actions.client',
            tag: 'install_kiosk_pwa',
            res_model: resModel,
            context: {
                active_id: resId,
                app_id: options.appId || resModel,
                ...options.context
            },
            target: 'new'
        });
    }
    
    // 获取Kiosk URL
    async getKioskUrl(resModel, resId) {
        try {
            return await this.env.services.orm.call(
                resModel, 
                'get_kiosk_url', 
                [resId]
            );
        } catch (error) {
            console.error('Failed to get kiosk URL:', error);
            throw error;
        }
    }
    
    // 生成安装URL
    generateInstallUrl(appId, kioskUrl) {
        const relativePath = kioskUrl.replace(window.location.origin + "/", "");
        return `/scoped_app?app_id=${appId}&path=${encodeURIComponent(relativePath)}`;
    }
    
    // 检查PWA安装状态
    async checkInstallStatus(appId) {
        try {
            // 检查是否已安装
            if ('getInstalledRelatedApps' in navigator) {
                const relatedApps = await navigator.getInstalledRelatedApps();
                return relatedApps.some(app => app.id === appId);
            }
            
            // 检查本地存储
            const installedApps = JSON.parse(
                localStorage.getItem('installed_pwa_apps') || '[]'
            );
            return installedApps.includes(appId);
        } catch (error) {
            console.warn('Cannot check PWA install status:', error);
            return false;
        }
    }
    
    // 记录安装状态
    recordInstallation(appId) {
        try {
            const installedApps = JSON.parse(
                localStorage.getItem('installed_pwa_apps') || '[]'
            );
            
            if (!installedApps.includes(appId)) {
                installedApps.push(appId);
                localStorage.setItem('installed_pwa_apps', JSON.stringify(installedApps));
            }
            
            this.installedApps.set(appId, {
                id: appId,
                installedAt: new Date().toISOString(),
                version: '1.0.0'
            });
        } catch (error) {
            console.error('Failed to record installation:', error);
        }
    }
    
    // 获取已安装应用列表
    getInstalledApps() {
        return Array.from(this.installedApps.values());
    }
    
    // 卸载PWA应用
    async uninstallApp(appId) {
        try {
            // 从本地存储移除
            const installedApps = JSON.parse(
                localStorage.getItem('installed_pwa_apps') || '[]'
            );
            const updatedApps = installedApps.filter(id => id !== appId);
            localStorage.setItem('installed_pwa_apps', JSON.stringify(updatedApps));
            
            // 从内存移除
            this.installedApps.delete(appId);
            
            return true;
        } catch (error) {
            console.error('Failed to uninstall app:', error);
            return false;
        }
    }
    
    // 更新应用信息
    async updateAppInfo(appId, info) {
        if (this.installedApps.has(appId)) {
            const currentInfo = this.installedApps.get(appId);
            this.installedApps.set(appId, { ...currentInfo, ...info });
        }
    }
}

// 使用示例
const kioskManager = new KioskAppManager(env);

// 显示安装对话框
await kioskManager.showInstallDialog('pos.config', 1, {
    appId: 'pos_kiosk',
    context: { company_id: 1 }
});

// 检查安装状态
const isInstalled = await kioskManager.checkInstallStatus('pos_kiosk');
console.log('PWA installed:', isInstalled);
```

### 2. PWA安装向导

```javascript
// PWA安装向导组件
class PWAInstallWizard extends Component {
    static template = xml`
        <div class="pwa-install-wizard">
            <div class="wizard-header">
                <h3>Install <t t-esc="appName"/> as PWA</h3>
                <p>Install this application on your device for better performance and offline access.</p>
            </div>
            
            <div class="wizard-content">
                <div class="step" t-if="currentStep === 'info'">
                    <h4>Application Information</h4>
                    <div class="app-info">
                        <div class="app-icon">
                            <img t-att-src="appIcon" t-att-alt="appName"/>
                        </div>
                        <div class="app-details">
                            <h5 t-esc="appName"/>
                            <p t-esc="appDescription"/>
                            <div class="app-features">
                                <ul>
                                    <li>Offline access</li>
                                    <li>Fast loading</li>
                                    <li>Native app experience</li>
                                    <li>Push notifications</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="step" t-if="currentStep === 'install'">
                    <h4>Installation Instructions</h4>
                    <div class="install-instructions">
                        <div class="instruction-step">
                            <span class="step-number">1</span>
                            <p>Click the "Install" button below</p>
                        </div>
                        <div class="instruction-step">
                            <span class="step-number">2</span>
                            <p>Follow your browser's installation prompts</p>
                        </div>
                        <div class="instruction-step">
                            <span class="step-number">3</span>
                            <p>Find the app on your device's home screen</p>
                        </div>
                    </div>
                    
                    <div class="install-url">
                        <label>Installation URL:</label>
                        <div class="url-container">
                            <input type="text" t-att-value="installUrl" readonly="readonly"/>
                            <button class="btn btn-secondary" t-on-click="copyUrl">
                                <i class="fa fa-copy"/> Copy
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="step" t-if="currentStep === 'success'">
                    <h4>Installation Successful!</h4>
                    <div class="success-message">
                        <i class="fa fa-check-circle text-success"/>
                        <p>The PWA has been installed successfully. You can now access it from your device's home screen.</p>
                    </div>
                </div>
            </div>
            
            <div class="wizard-footer">
                <button class="btn btn-secondary" 
                        t-if="currentStep !== 'success'"
                        t-on-click="cancel">
                    Cancel
                </button>
                <button class="btn btn-secondary" 
                        t-if="currentStep === 'install'"
                        t-on-click="previousStep">
                    Previous
                </button>
                <button class="btn btn-primary" 
                        t-if="currentStep === 'info'"
                        t-on-click="nextStep">
                    Next
                </button>
                <button class="btn btn-primary" 
                        t-if="currentStep === 'install'"
                        t-on-click="install">
                    Install PWA
                </button>
                <button class="btn btn-primary" 
                        t-if="currentStep === 'success'"
                        t-on-click="close">
                    Close
                </button>
            </div>
        </div>`;
    
    setup() {
        this.currentStep = 'info';
        this.appName = this.props.appName || 'Odoo App';
        this.appDescription = this.props.appDescription || 'Odoo Progressive Web Application';
        this.appIcon = this.props.appIcon || '/web/static/img/favicon.ico';
        this.installUrl = this.props.installUrl;
    }
    
    nextStep() {
        if (this.currentStep === 'info') {
            this.currentStep = 'install';
        }
        this.render();
    }
    
    previousStep() {
        if (this.currentStep === 'install') {
            this.currentStep = 'info';
        }
        this.render();
    }
    
    async install() {
        try {
            // 打开安装URL
            const installWindow = window.open(this.installUrl, '_blank');
            
            // 监听安装完成
            const checkInstall = setInterval(() => {
                if (installWindow.closed) {
                    clearInterval(checkInstall);
                    this.currentStep = 'success';
                    this.render();
                }
            }, 1000);
            
        } catch (error) {
            console.error('Installation failed:', error);
            this.env.services.notification.add(
                'Installation failed: ' + error.message,
                { type: 'danger' }
            );
        }
    }
    
    async copyUrl() {
        try {
            await navigator.clipboard.writeText(this.installUrl);
            this.env.services.notification.add(
                'URL copied to clipboard',
                { type: 'success' }
            );
        } catch (error) {
            console.error('Failed to copy URL:', error);
            this.env.services.notification.add(
                'Failed to copy URL',
                { type: 'warning' }
            );
        }
    }
    
    cancel() {
        this.props.close();
    }
    
    close() {
        this.props.close();
    }
}

// 使用PWA安装向导
function showPWAInstallWizard(env, installUrl, appInfo = {}) {
    env.services.dialog.add(PWAInstallWizard, {
        installUrl: installUrl,
        appName: appInfo.name,
        appDescription: appInfo.description,
        appIcon: appInfo.icon,
        close: () => {
            // 关闭对话框
        }
    });
}
```

### 3. Kiosk配置管理

```javascript
// Kiosk配置管理器
class KioskConfigManager {
    constructor(env) {
        this.env = env;
        this.configs = new Map();
    }
    
    // 创建Kiosk配置
    async createKioskConfig(resModel, resId, config) {
        try {
            const kioskConfig = {
                id: `${resModel}_${resId}`,
                resModel: resModel,
                resId: resId,
                appId: config.appId || resModel,
                name: config.name || `${resModel} Kiosk`,
                description: config.description || '',
                icon: config.icon || '/web/static/img/favicon.ico',
                theme: config.theme || 'default',
                features: config.features || [],
                settings: config.settings || {},
                createdAt: new Date().toISOString()
            };
            
            this.configs.set(kioskConfig.id, kioskConfig);
            return kioskConfig;
        } catch (error) {
            console.error('Failed to create kiosk config:', error);
            throw error;
        }
    }
    
    // 获取Kiosk配置
    getKioskConfig(configId) {
        return this.configs.get(configId);
    }
    
    // 更新Kiosk配置
    updateKioskConfig(configId, updates) {
        const config = this.configs.get(configId);
        if (config) {
            Object.assign(config, updates, {
                updatedAt: new Date().toISOString()
            });
            this.configs.set(configId, config);
            return config;
        }
        return null;
    }
    
    // 删除Kiosk配置
    deleteKioskConfig(configId) {
        return this.configs.delete(configId);
    }
    
    // 获取所有配置
    getAllConfigs() {
        return Array.from(this.configs.values());
    }
    
    // 生成Kiosk URL
    async generateKioskUrl(configId) {
        const config = this.configs.get(configId);
        if (!config) {
            throw new Error(`Kiosk config ${configId} not found`);
        }
        
        try {
            const url = await this.env.services.orm.call(
                config.resModel,
                'get_kiosk_url',
                [config.resId]
            );
            
            return url;
        } catch (error) {
            console.error('Failed to generate kiosk URL:', error);
            throw error;
        }
    }
    
    // 生成PWA安装URL
    generatePWAInstallUrl(configId) {
        const config = this.configs.get(configId);
        if (!config) {
            throw new Error(`Kiosk config ${configId} not found`);
        }
        
        return this.generateKioskUrl(configId).then(kioskUrl => {
            const relativePath = kioskUrl.replace(window.location.origin + "/", "");
            return `/scoped_app?app_id=${config.appId}&path=${encodeURIComponent(relativePath)}`;
        });
    }
    
    // 启动Kiosk安装流程
    async startInstallProcess(configId) {
        const config = this.configs.get(configId);
        if (!config) {
            throw new Error(`Kiosk config ${configId} not found`);
        }
        
        return this.env.services.action.doAction({
            type: 'ir.actions.client',
            tag: 'install_kiosk_pwa',
            res_model: config.resModel,
            context: {
                active_id: config.resId,
                app_id: config.appId
            },
            target: 'new'
        });
    }
    
    // 导出配置
    exportConfig(configId) {
        const config = this.configs.get(configId);
        if (config) {
            const exportData = JSON.stringify(config, null, 2);
            const blob = new Blob([exportData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `kiosk_config_${configId}.json`;
            link.click();
            URL.revokeObjectURL(url);
        }
    }
    
    // 导入配置
    async importConfig(file) {
        try {
            const text = await file.text();
            const config = JSON.parse(text);
            
            // 验证配置格式
            if (!config.resModel || !config.resId) {
                throw new Error('Invalid config format');
            }
            
            // 生成新ID避免冲突
            config.id = `${config.resModel}_${config.resId}_${Date.now()}`;
            config.importedAt = new Date().toISOString();
            
            this.configs.set(config.id, config);
            return config;
        } catch (error) {
            console.error('Failed to import config:', error);
            throw error;
        }
    }
}

// 使用示例
const configManager = new KioskConfigManager(env);

// 创建POS Kiosk配置
const posConfig = await configManager.createKioskConfig('pos.config', 1, {
    appId: 'pos_kiosk',
    name: 'POS Kiosk',
    description: 'Point of Sale Kiosk Application',
    features: ['offline', 'touch', 'barcode'],
    settings: {
        theme: 'dark',
        autoStart: true,
        fullscreen: true
    }
});

// 启动安装流程
await configManager.startInstallProcess(posConfig.id);
```

## 技术特点

### 1. PWA专用
- **PWA安装**: 专门用于PWA应用的安装
- **Kiosk模式**: 支持Kiosk模式的应用场景
- **离线支持**: PWA的离线访问能力
- **原生体验**: 提供接近原生应用的体验

### 2. URL管理
- **动态生成**: 动态生成Kiosk和安装URL
- **路径编码**: 安全的URL路径编码
- **参数传递**: 正确的参数传递机制
- **相对路径**: 智能的相对路径处理

### 3. 用户体验
- **对话框**: 在对话框中显示安装界面
- **引导流程**: 清晰的安装引导流程
- **错误处理**: 完善的错误处理和提示
- **平台适配**: 适配不同平台的安装方式

### 4. 集成性
- **动作系统**: 与动作系统的完整集成
- **服务调用**: 与ORM服务的集成
- **注册表**: 基于注册表的管理机制
- **模板系统**: 与模板系统的集成

## 设计模式

### 1. 工厂模式 (Factory Pattern)
- **URL生成**: 根据配置生成不同的URL
- **组件创建**: 创建PWA安装相关组件
- **配置生成**: 生成Kiosk配置对象

### 2. 策略模式 (Strategy Pattern)
- **安装策略**: 不同平台的PWA安装策略
- **URL策略**: 不同类型的URL生成策略
- **显示策略**: 不同的界面显示策略

### 3. 观察者模式 (Observer Pattern)
- **安装状态**: 监听PWA安装状态变化
- **URL变化**: 监听URL变化事件
- **用户交互**: 监听用户交互事件

## 注意事项

1. **浏览器兼容**: 确保PWA功能的浏览器兼容性
2. **安全性**: 验证URL的安全性和有效性
3. **用户权限**: 检查PWA安装的用户权限
4. **错误处理**: 完善的安装失败处理机制

## 扩展建议

1. **安装检测**: 增强PWA安装状态检测
2. **批量安装**: 支持批量PWA应用安装
3. **更新机制**: PWA应用的更新机制
4. **统计分析**: PWA安装和使用统计
5. **自定义配置**: 更丰富的PWA配置选项

该Kiosk PWA安装动作为Odoo Web客户端提供了专业的PWA安装功能，通过简洁的设计和完整的安装流程确保了Kiosk应用的便捷部署和使用。

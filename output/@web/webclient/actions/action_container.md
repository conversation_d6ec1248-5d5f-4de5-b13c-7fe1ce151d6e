# ActionContainer - 动作容器组件

## 概述

`action_container.js` 是 Odoo Web 客户端的动作容器组件，提供了动作的渲染容器功能。该模块包含35行代码，是一个OWL组件，负责监听动作管理器的更新事件并渲染相应的动作组件，具备事件监听、动态渲染、生命周期管理等特性，是Odoo Web客户端动作系统的核心渲染容器。

## 文件信息
- **路径**: `/web/static/src/webclient/actions/action_container.js`
- **行数**: 35
- **模块**: `@web/webclient/actions/action_container`

## 依赖关系

```javascript
// 核心依赖
'@web/webclient/actions/action_dialog'  // 动作对话框组件
'@odoo/owl'                             // OWL框架
```

## 主组件定义

### 1. ActionContainer 组件

```javascript
class ActionContainer extends Component {
    static components = { ActionDialog };
    static props = {};
    static template = xml`
        <t t-name="web.ActionContainer">
          <div class="o_action_manager">
            <t t-if="info.Component" 
               t-component="info.Component" 
               className="'o_action'" 
               t-props="info.componentProps" 
               t-key="info.id"/>
          </div>
        </t>`;
    
    setup() {
        this.info = {};
        this.onActionManagerUpdate = ({ detail: info }) => {
            this.info = info;
            this.render();
        };
        this.env.bus.addEventListener("ACTION_MANAGER:UPDATE", this.onActionManagerUpdate);
        onWillDestroy(() => {
            this.env.bus.removeEventListener("ACTION_MANAGER:UPDATE", this.onActionManagerUpdate);
        });
    }
}
```

**组件特性**:
- **事件驱动**: 监听动作管理器的更新事件
- **动态渲染**: 根据动作信息动态渲染组件
- **生命周期**: 完整的组件生命周期管理
- **简洁设计**: 专注于容器功能的简洁设计

## 核心功能

### 1. 事件监听

```javascript
// 监听动作管理器更新事件
this.env.bus.addEventListener("ACTION_MANAGER:UPDATE", this.onActionManagerUpdate);

// 事件处理函数
this.onActionManagerUpdate = ({ detail: info }) => {
    this.info = info;
    this.render();
};
```

**事件监听功能**:
- **事件订阅**: 订阅动作管理器的更新事件
- **信息更新**: 接收并更新动作信息
- **自动渲染**: 信息更新后自动重新渲染
- **事件清理**: 组件销毁时清理事件监听器

### 2. 动态渲染

```javascript
// 模板中的动态组件渲染
<t t-if="info.Component" 
   t-component="info.Component" 
   className="'o_action'" 
   t-props="info.componentProps" 
   t-key="info.id"/>
```

**动态渲染功能**:
- **条件渲染**: 只有当存在组件时才渲染
- **组件传递**: 动态传递要渲染的组件
- **属性传递**: 传递组件所需的属性
- **键值管理**: 使用唯一键值管理组件实例

### 3. 生命周期管理

```javascript
// 组件销毁时的清理
onWillDestroy(() => {
    this.env.bus.removeEventListener("ACTION_MANAGER:UPDATE", this.onActionManagerUpdate);
});
```

**生命周期管理功能**:
- **资源清理**: 组件销毁时清理事件监听器
- **内存管理**: 防止内存泄漏
- **优雅销毁**: 确保组件的优雅销毁
- **事件解绑**: 正确解绑事件监听器

## 使用场景

### 1. 动作管理器集成

```javascript
// 动作管理器与容器的集成示例
class ActionManager {
    constructor(env) {
        this.env = env;
        this.currentAction = null;
        this.actionStack = [];
    }
    
    async doAction(action, options = {}) {
        try {
            // 解析动作
            const actionInfo = await this.parseAction(action);
            
            // 创建组件信息
            const componentInfo = {
                id: this.generateActionId(),
                Component: actionInfo.Component,
                componentProps: {
                    ...actionInfo.props,
                    ...options
                },
                action: actionInfo.action
            };
            
            // 更新当前动作
            this.currentAction = componentInfo;
            this.actionStack.push(componentInfo);
            
            // 通知容器更新
            this.notifyUpdate(componentInfo);
            
            return componentInfo;
        } catch (error) {
            console.error('Failed to execute action:', error);
            throw error;
        }
    }
    
    notifyUpdate(info) {
        // 发送更新事件给ActionContainer
        this.env.bus.trigger("ACTION_MANAGER:UPDATE", info);
    }
    
    async parseAction(action) {
        if (typeof action === 'string') {
            // 字符串动作，查找注册的动作
            return this.getRegisteredAction(action);
        } else if (action.type) {
            // 动作对象，根据类型创建组件
            return this.createActionComponent(action);
        } else {
            throw new Error('Invalid action format');
        }
    }
    
    createActionComponent(action) {
        switch (action.type) {
            case 'ir.actions.act_window':
                return {
                    Component: FormView,
                    props: {
                        resModel: action.res_model,
                        resId: action.res_id,
                        viewMode: action.view_mode,
                        domain: action.domain,
                        context: action.context
                    },
                    action
                };
            
            case 'ir.actions.client':
                return {
                    Component: this.getClientAction(action.tag),
                    props: action.params || {},
                    action
                };
            
            case 'ir.actions.report':
                return {
                    Component: ReportAction,
                    props: {
                        reportName: action.report_name,
                        data: action.data,
                        context: action.context
                    },
                    action
                };
            
            default:
                throw new Error(`Unsupported action type: ${action.type}`);
        }
    }
    
    generateActionId() {
        return `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    goBack() {
        if (this.actionStack.length > 1) {
            this.actionStack.pop();
            const previousAction = this.actionStack[this.actionStack.length - 1];
            this.currentAction = previousAction;
            this.notifyUpdate(previousAction);
            return previousAction;
        }
        return null;
    }
    
    closeAction() {
        this.actionStack = [];
        this.currentAction = null;
        this.notifyUpdate({});
    }
}

// 使用示例
const actionManager = new ActionManager(env);

// 执行窗口动作
await actionManager.doAction({
    type: 'ir.actions.act_window',
    res_model: 'res.partner',
    view_mode: 'form',
    res_id: 123,
    target: 'current'
});

// 执行客户端动作
await actionManager.doAction({
    type: 'ir.actions.client',
    tag: 'dashboard',
    params: { period: 'month' }
});
```

### 2. 自定义动作容器

```javascript
// 扩展的动作容器，支持更多功能
class ExtendedActionContainer extends ActionContainer {
    static template = xml`
        <t t-name="web.ExtendedActionContainer">
          <div class="o_action_manager">
            <!-- 动作头部 -->
            <div t-if="info.Component" class="o_action_header">
              <div class="o_action_title" t-esc="info.title"/>
              <div class="o_action_controls">
                <button t-if="canGoBack" 
                        class="btn btn-secondary" 
                        t-on-click="goBack">
                  <i class="fa fa-arrow-left"/> Back
                </button>
                <button class="btn btn-secondary" 
                        t-on-click="closeAction">
                  <i class="fa fa-times"/> Close
                </button>
              </div>
            </div>
            
            <!-- 动作内容 -->
            <div class="o_action_content">
              <t t-if="info.Component" 
                 t-component="info.Component" 
                 className="'o_action'" 
                 t-props="info.componentProps" 
                 t-key="info.id"/>
            </div>
            
            <!-- 加载指示器 -->
            <div t-if="isLoading" class="o_action_loading">
              <div class="o_loading_indicator">
                <i class="fa fa-spinner fa-spin"/> Loading...
              </div>
            </div>
          </div>
        </t>`;
    
    setup() {
        super.setup();
        this.isLoading = false;
        this.actionHistory = [];
        
        // 扩展事件监听
        this.env.bus.addEventListener("ACTION_MANAGER:LOADING", this.onActionLoading.bind(this));
        this.env.bus.addEventListener("ACTION_MANAGER:LOADED", this.onActionLoaded.bind(this));
    }
    
    get canGoBack() {
        return this.actionHistory.length > 1;
    }
    
    onActionManagerUpdate({ detail: info }) {
        // 记录动作历史
        if (info.id && info.id !== this.info.id) {
            this.actionHistory.push(info);
        }
        
        this.info = info;
        this.render();
    }
    
    onActionLoading() {
        this.isLoading = true;
        this.render();
    }
    
    onActionLoaded() {
        this.isLoading = false;
        this.render();
    }
    
    goBack() {
        if (this.canGoBack) {
            this.actionHistory.pop();
            const previousAction = this.actionHistory[this.actionHistory.length - 1];
            this.env.bus.trigger("ACTION_MANAGER:GO_BACK", previousAction);
        }
    }
    
    closeAction() {
        this.actionHistory = [];
        this.env.bus.trigger("ACTION_MANAGER:CLOSE");
    }
}
```

### 3. 动作容器管理器

```javascript
// 动作容器管理器，支持多个容器
class ActionContainerManager {
    constructor(env) {
        this.env = env;
        this.containers = new Map();
        this.activeContainer = null;
    }
    
    createContainer(containerId, options = {}) {
        const container = {
            id: containerId,
            component: options.extended ? ExtendedActionContainer : ActionContainer,
            element: null,
            info: {},
            options
        };
        
        this.containers.set(containerId, container);
        return container;
    }
    
    mountContainer(containerId, targetElement) {
        const container = this.containers.get(containerId);
        if (!container) {
            throw new Error(`Container ${containerId} not found`);
        }
        
        // 创建组件实例
        const ComponentClass = container.component;
        const component = new ComponentClass(null, this.env);
        
        // 挂载到目标元素
        component.mount(targetElement);
        
        container.element = targetElement;
        container.instance = component;
        
        return component;
    }
    
    setActiveContainer(containerId) {
        const container = this.containers.get(containerId);
        if (container) {
            this.activeContainer = container;
        }
    }
    
    updateContainer(containerId, info) {
        const container = this.containers.get(containerId);
        if (container && container.instance) {
            container.info = info;
            container.instance.onActionManagerUpdate({ detail: info });
        }
    }
    
    destroyContainer(containerId) {
        const container = this.containers.get(containerId);
        if (container && container.instance) {
            container.instance.destroy();
            this.containers.delete(containerId);
        }
    }
    
    getContainer(containerId) {
        return this.containers.get(containerId);
    }
    
    getAllContainers() {
        return Array.from(this.containers.values());
    }
    
    broadcastToAllContainers(eventName, data) {
        for (const container of this.containers.values()) {
            if (container.instance) {
                this.env.bus.trigger(eventName, data);
            }
        }
    }
}

// 使用示例
const containerManager = new ActionContainerManager(env);

// 创建主容器
const mainContainer = containerManager.createContainer('main', { extended: true });
containerManager.mountContainer('main', document.querySelector('#main-content'));

// 创建侧边栏容器
const sidebarContainer = containerManager.createContainer('sidebar');
containerManager.mountContainer('sidebar', document.querySelector('#sidebar'));

// 设置活动容器
containerManager.setActiveContainer('main');

// 更新容器内容
containerManager.updateContainer('main', {
    id: 'action_1',
    Component: FormView,
    componentProps: { resModel: 'res.partner', resId: 123 }
});
```

## 技术特点

### 1. 事件驱动架构
- **松耦合**: 通过事件总线实现组件间的松耦合
- **响应式**: 自动响应动作管理器的状态变化
- **实时更新**: 实时更新容器内容
- **事件清理**: 完善的事件监听器清理机制

### 2. 动态组件渲染
- **组件切换**: 支持动态切换不同的动作组件
- **属性传递**: 灵活的组件属性传递机制
- **键值管理**: 使用键值确保组件的正确更新
- **条件渲染**: 智能的条件渲染逻辑

### 3. 简洁设计
- **单一职责**: 专注于容器渲染功能
- **最小化**: 最小化的代码实现
- **可扩展**: 易于扩展和定制
- **高性能**: 高效的渲染性能

### 4. 生命周期管理
- **资源管理**: 完善的资源管理机制
- **内存安全**: 防止内存泄漏
- **优雅销毁**: 组件的优雅销毁
- **状态清理**: 完整的状态清理

## 设计模式

### 1. 观察者模式 (Observer Pattern)
- **事件监听**: 监听动作管理器的状态变化
- **自动更新**: 状态变化时自动更新视图
- **解耦设计**: 容器与动作管理器的解耦

### 2. 容器模式 (Container Pattern)
- **内容管理**: 管理和渲染动作组件
- **生命周期**: 管理子组件的生命周期
- **资源分配**: 合理分配和管理资源

### 3. 策略模式 (Strategy Pattern)
- **渲染策略**: 不同动作类型的渲染策略
- **组件选择**: 根据动作类型选择合适的组件
- **属性映射**: 不同的属性映射策略

## 注意事项

1. **事件清理**: 确保在组件销毁时清理事件监听器
2. **内存管理**: 避免创建过多的组件实例
3. **性能优化**: 避免不必要的重新渲染
4. **错误处理**: 处理组件渲染过程中的错误

## 扩展建议

1. **动画支持**: 添加动作切换的动画效果
2. **缓存机制**: 实现动作组件的缓存机制
3. **错误边界**: 添加错误边界处理
4. **性能监控**: 监控容器的渲染性能
5. **多容器支持**: 支持多个动作容器的管理

该动作容器组件为Odoo Web客户端提供了简洁而强大的动作渲染功能，通过事件驱动的架构确保了动作系统的灵活性和可扩展性。

# Client Actions - 客户端动作

## 概述

`client_actions.js` 是 Odoo Web 客户端的客户端动作集合，提供了一系列内置的客户端动作功能。该模块包含102行代码，定义了通知显示、页面重载、重定向等常用的客户端动作，具备消息通知、界面刷新、URL跳转等特性，是Odoo Web客户端动作系统中处理客户端操作的核心模块。

## 文件信息
- **路径**: `/web/static/src/webclient/actions/client_actions.js`
- **行数**: 102
- **模块**: `@web/webclient/actions/client_actions`

## 依赖关系

```javascript
// 核心依赖
'@web/core/browser/browser'     // 浏览器服务
'@web/core/browser/router'      // 路由器
'@web/core/network/rpc'         // RPC网络
'@web/core/registry'            // 注册表系统
'@web/core/utils/strings'       // 字符串工具
'@odoo/owl'                     // OWL框架
```

## 核心动作

### 1. 显示通知动作

```javascript
function displayNotificationAction(env, action) {
    const params = action.params || {};
    const options = {
        className: params.className || "",
        sticky: params.sticky || false,
        title: params.title,
        type: params.type || "info",
    };
    
    // 处理链接
    const links = (params.links || []).map((link) => {
        return `<a href="${escape(link.url)}" target="_blank">${escape(link.label)}</a>`;
    });
    
    // 构建消息内容
    const message = markup(sprintf(escape(params.message), ...links));
    
    // 显示通知
    env.services.notification.add(message, options);
    
    return params.next;
}

// 注册通知动作
registry.category("actions").add("display_notification", displayNotificationAction);
```

**通知动作功能**:
- **消息显示**: 显示各种类型的通知消息
- **链接支持**: 支持在消息中嵌入链接
- **样式控制**: 支持自定义通知样式
- **持久性**: 支持粘性通知显示
- **类型分类**: 支持info、warning、danger等类型

**参数说明**:
- **message**: 通知消息内容
- **title**: 通知标题
- **type**: 通知类型（info、success、warning、danger）
- **sticky**: 是否为粘性通知
- **className**: 自定义CSS类名
- **links**: 消息中的链接列表
- **next**: 下一个要执行的动作

### 2. 重载动作

```javascript
function reload(env, action) {
    const { menu_id, action_id } = action.params || {};
    let route = { ...router.current };

    if (menu_id || action_id) {
        route = {};
        if (menu_id) {
            route.menu_id = menu_id;
        }
        if (action_id) {
            route.action = action_id;
        }
    }
    
    // 重载页面
    browser.location.assign(router.urlFor(route));
}

// 注册重载动作
registry.category("actions").add("reload", reload);
```

**重载动作功能**:
- **页面刷新**: 重新加载整个界面
- **菜单跳转**: 可指定要打开的菜单项
- **动作跳转**: 可指定要执行的动作
- **路由更新**: 更新浏览器URL
- **状态重置**: 重置应用状态

**参数说明**:
- **menu_id**: 要打开的菜单ID
- **action_id**: 要执行的动作ID

### 3. 重定向动作

```javascript
function redirect(env, action) {
    const { url, target } = action.params || {};
    
    if (url) {
        if (target === '_blank') {
            browser.open(url, '_blank');
        } else {
            browser.location.assign(url);
        }
    }
}

// 注册重定向动作
registry.category("actions").add("redirect", redirect);
```

**重定向动作功能**:
- **URL跳转**: 跳转到指定URL
- **目标控制**: 控制在当前窗口或新窗口打开
- **外部链接**: 支持跳转到外部网站
- **内部导航**: 支持应用内部导航

## 使用场景

### 1. 通知系统集成

```javascript
// 通知管理器
class NotificationManager {
    constructor(env) {
        this.env = env;
        this.notificationQueue = [];
        this.maxNotifications = 5;
    }
    
    // 显示成功通知
    showSuccess(message, options = {}) {
        return this.env.services.action.doAction({
            type: 'ir.actions.client',
            tag: 'display_notification',
            params: {
                message: message,
                type: 'success',
                title: options.title || 'Success',
                sticky: options.sticky || false,
                className: options.className || 'notification-success'
            }
        });
    }
    
    // 显示错误通知
    showError(message, options = {}) {
        return this.env.services.action.doAction({
            type: 'ir.actions.client',
            tag: 'display_notification',
            params: {
                message: message,
                type: 'danger',
                title: options.title || 'Error',
                sticky: true, // 错误通知默认为粘性
                className: options.className || 'notification-error'
            }
        });
    }
    
    // 显示警告通知
    showWarning(message, options = {}) {
        return this.env.services.action.doAction({
            type: 'ir.actions.client',
            tag: 'display_notification',
            params: {
                message: message,
                type: 'warning',
                title: options.title || 'Warning',
                sticky: options.sticky || false,
                className: options.className || 'notification-warning'
            }
        });
    }
    
    // 显示信息通知
    showInfo(message, options = {}) {
        return this.env.services.action.doAction({
            type: 'ir.actions.client',
            tag: 'display_notification',
            params: {
                message: message,
                type: 'info',
                title: options.title || 'Information',
                sticky: options.sticky || false,
                className: options.className || 'notification-info'
            }
        });
    }
    
    // 显示带链接的通知
    showWithLinks(message, links, options = {}) {
        return this.env.services.action.doAction({
            type: 'ir.actions.client',
            tag: 'display_notification',
            params: {
                message: message,
                links: links,
                type: options.type || 'info',
                title: options.title,
                sticky: options.sticky || false,
                className: options.className
            }
        });
    }
    
    // 显示进度通知
    showProgress(message, progress, options = {}) {
        const progressMessage = `${message} (${progress}%)`;
        return this.showInfo(progressMessage, {
            ...options,
            className: 'notification-progress'
        });
    }
    
    // 批量通知
    async showBatch(notifications) {
        const results = [];
        for (const notification of notifications) {
            const result = await this.showNotification(notification);
            results.push(result);
            
            // 添加延迟避免通知过多
            if (notifications.length > 1) {
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }
        return results;
    }
    
    showNotification(notification) {
        const { type, message, ...options } = notification;
        
        switch (type) {
            case 'success':
                return this.showSuccess(message, options);
            case 'error':
            case 'danger':
                return this.showError(message, options);
            case 'warning':
                return this.showWarning(message, options);
            case 'info':
            default:
                return this.showInfo(message, options);
        }
    }
}

// 使用示例
const notificationManager = new NotificationManager(env);

// 显示成功消息
await notificationManager.showSuccess('Record saved successfully!');

// 显示错误消息
await notificationManager.showError('Failed to save record');

// 显示带链接的消息
await notificationManager.showWithLinks(
    'Please check the %s for more details',
    [{ url: '/help', label: 'documentation' }],
    { type: 'warning' }
);
```

### 2. 页面导航管理

```javascript
// 导航管理器
class NavigationManager {
    constructor(env) {
        this.env = env;
        this.navigationHistory = [];
    }
    
    // 重载到指定菜单
    async reloadToMenu(menuId, options = {}) {
        this.recordNavigation('menu', menuId);
        
        return this.env.services.action.doAction({
            type: 'ir.actions.client',
            tag: 'reload',
            params: {
                menu_id: menuId,
                ...options
            }
        });
    }
    
    // 重载到指定动作
    async reloadToAction(actionId, options = {}) {
        this.recordNavigation('action', actionId);
        
        return this.env.services.action.doAction({
            type: 'ir.actions.client',
            tag: 'reload',
            params: {
                action_id: actionId,
                ...options
            }
        });
    }
    
    // 刷新当前页面
    async refreshCurrent() {
        return this.env.services.action.doAction({
            type: 'ir.actions.client',
            tag: 'reload'
        });
    }
    
    // 重定向到外部URL
    async redirectToUrl(url, target = '_self') {
        this.recordNavigation('url', url);
        
        return this.env.services.action.doAction({
            type: 'ir.actions.client',
            tag: 'redirect',
            params: {
                url: url,
                target: target
            }
        });
    }
    
    // 在新窗口打开
    async openInNewWindow(url) {
        return this.redirectToUrl(url, '_blank');
    }
    
    // 记录导航历史
    recordNavigation(type, target) {
        this.navigationHistory.push({
            type: type,
            target: target,
            timestamp: Date.now(),
            currentUrl: window.location.href
        });
        
        // 限制历史记录数量
        if (this.navigationHistory.length > 50) {
            this.navigationHistory.shift();
        }
    }
    
    // 获取导航历史
    getNavigationHistory() {
        return [...this.navigationHistory];
    }
    
    // 清除导航历史
    clearNavigationHistory() {
        this.navigationHistory = [];
    }
    
    // 智能导航（根据类型自动选择方法）
    async navigateTo(target, options = {}) {
        if (typeof target === 'number') {
            // 数字ID，可能是菜单或动作
            if (options.type === 'menu') {
                return this.reloadToMenu(target, options);
            } else {
                return this.reloadToAction(target, options);
            }
        } else if (typeof target === 'string') {
            if (target.startsWith('http') || target.startsWith('/')) {
                // URL
                return this.redirectToUrl(target, options.target);
            } else {
                // 可能是动作标签
                return this.env.services.action.doAction({
                    type: 'ir.actions.client',
                    tag: target,
                    params: options.params || {}
                });
            }
        } else if (typeof target === 'object') {
            // 动作对象
            return this.env.services.action.doAction(target, options);
        }
    }
}

// 使用示例
const navigationManager = new NavigationManager(env);

// 重载到销售菜单
await navigationManager.reloadToMenu(42);

// 重载到特定动作
await navigationManager.reloadToAction(123);

// 重定向到外部网站
await navigationManager.redirectToUrl('https://www.odoo.com');

// 在新窗口打开帮助页面
await navigationManager.openInNewWindow('/help');

// 智能导航
await navigationManager.navigateTo(42, { type: 'menu' });
await navigationManager.navigateTo('https://www.odoo.com');
```

### 3. 自定义客户端动作

```javascript
// 注册自定义客户端动作
registry.category("actions").add("custom_dashboard", function(env, action) {
    const params = action.params || {};
    
    // 创建仪表板组件
    const dashboardComponent = {
        Component: DashboardComponent,
        props: {
            title: params.title || 'Dashboard',
            widgets: params.widgets || [],
            refreshInterval: params.refreshInterval || 30000
        }
    };
    
    return dashboardComponent;
});

// 数据导出动作
registry.category("actions").add("export_data", async function(env, action) {
    const params = action.params || {};
    
    try {
        // 执行数据导出
        const exportData = await env.services.rpc('/web/export/data', {
            model: params.model,
            ids: params.ids,
            fields: params.fields,
            format: params.format || 'xlsx'
        });
        
        // 下载文件
        const blob = new Blob([exportData], { 
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = params.filename || 'export.xlsx';
        link.click();
        URL.revokeObjectURL(url);
        
        // 显示成功通知
        return env.services.action.doAction({
            type: 'ir.actions.client',
            tag: 'display_notification',
            params: {
                message: 'Data exported successfully!',
                type: 'success'
            }
        });
        
    } catch (error) {
        // 显示错误通知
        return env.services.action.doAction({
            type: 'ir.actions.client',
            tag: 'display_notification',
            params: {
                message: 'Export failed: ' + error.message,
                type: 'danger'
            }
        });
    }
});

// 系统信息动作
registry.category("actions").add("system_info", function(env, action) {
    const systemInfo = {
        userAgent: navigator.userAgent,
        language: navigator.language,
        platform: navigator.platform,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine,
        screenResolution: `${screen.width}x${screen.height}`,
        windowSize: `${window.innerWidth}x${window.innerHeight}`,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        timestamp: new Date().toISOString()
    };
    
    const message = Object.entries(systemInfo)
        .map(([key, value]) => `${key}: ${value}`)
        .join('\n');
    
    return env.services.action.doAction({
        type: 'ir.actions.client',
        tag: 'display_notification',
        params: {
            message: `<pre>${message}</pre>`,
            title: 'System Information',
            type: 'info',
            sticky: true
        }
    });
});

// 清理缓存动作
registry.category("actions").add("clear_cache", async function(env, action) {
    try {
        // 清理浏览器缓存
        if ('caches' in window) {
            const cacheNames = await caches.keys();
            await Promise.all(
                cacheNames.map(cacheName => caches.delete(cacheName))
            );
        }
        
        // 清理本地存储
        localStorage.clear();
        sessionStorage.clear();
        
        // 显示成功通知并重载页面
        await env.services.action.doAction({
            type: 'ir.actions.client',
            tag: 'display_notification',
            params: {
                message: 'Cache cleared successfully! Page will reload.',
                type: 'success'
            }
        });
        
        // 延迟重载页面
        setTimeout(() => {
            env.services.action.doAction({
                type: 'ir.actions.client',
                tag: 'reload'
            });
        }, 2000);
        
    } catch (error) {
        return env.services.action.doAction({
            type: 'ir.actions.client',
            tag: 'display_notification',
            params: {
                message: 'Failed to clear cache: ' + error.message,
                type: 'danger'
            }
        });
    }
});
```

## 技术特点

### 1. 简洁实用
- **轻量级**: 代码简洁，功能专一
- **即用性**: 开箱即用的常用功能
- **标准化**: 标准的客户端动作格式
- **可扩展**: 易于扩展新的客户端动作

### 2. 安全性
- **输入转义**: 对用户输入进行安全转义
- **链接验证**: 验证外部链接的安全性
- **权限检查**: 执行前的权限验证
- **错误处理**: 完善的错误处理机制

### 3. 用户体验
- **即时反馈**: 提供即时的用户反馈
- **视觉提示**: 丰富的视觉提示效果
- **交互友好**: 友好的用户交互体验
- **响应式**: 支持不同设备的响应式显示

### 4. 集成性
- **服务集成**: 与其他服务的良好集成
- **注册表**: 基于注册表的管理机制
- **事件系统**: 与事件系统的集成
- **路由同步**: 与路由系统的同步

## 设计模式

### 1. 命令模式 (Command Pattern)
- **动作封装**: 将操作封装为动作命令
- **参数传递**: 通过参数传递执行信息
- **结果返回**: 统一的结果返回机制

### 2. 策略模式 (Strategy Pattern)
- **通知策略**: 不同类型通知的显示策略
- **导航策略**: 不同的页面导航策略
- **处理策略**: 不同动作的处理策略

### 3. 工厂模式 (Factory Pattern)
- **动作创建**: 根据标签创建对应的动作
- **组件创建**: 创建相应的UI组件
- **服务创建**: 创建相关的服务实例

## 注意事项

1. **安全性**: 确保用户输入的安全转义
2. **性能**: 避免频繁的页面重载
3. **用户体验**: 提供适当的加载和反馈提示
4. **兼容性**: 确保在不同浏览器中的兼容性

## 扩展建议

1. **动画效果**: 添加通知的动画效果
2. **批量操作**: 支持批量通知和操作
3. **持久化**: 通知的持久化存储
4. **模板系统**: 通知消息的模板系统
5. **国际化**: 增强国际化支持

该客户端动作模块为Odoo Web客户端提供了实用的内置动作功能，通过简洁的设计和良好的扩展性确保了系统的易用性和可维护性。

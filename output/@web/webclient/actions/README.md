# Actions - 动作系统

## 概述

Actions 是 Odoo Web 客户端的核心动作系统，提供了完整的动作管理、执行、渲染和扩展功能。该系统包含多个模块，总计约2300行代码，是整个Odoo Web应用的动作基础设施，负责处理用户界面中的各种动作操作，具备动作解析、执行引擎、容器渲染、对话框支持、PWA安装、调试工具、报表处理等特性，是Odoo Web客户端中最重要的系统之一。

## 目录结构

```
actions/
├── action_container.js         # 动作容器组件 (35行)
├── action_dialog.js            # 动作对话框组件 (45行)
├── action_service.js           # 动作服务核心 (1722行)
├── client_actions.js           # 客户端动作集合 (102行)
├── action_install_kiosk_pwa.js # Kiosk PWA安装动作 (43行)
├── debug_items.js              # 调试项目集合 (185行)
├── reports/                    # 报表子系统
│   ├── report_action.js        # 报表动作组件 (63行)
│   ├── report_hook.js          # 报表钩子 (71行)
│   └── utils.js                # 报表工具函数 (92行)
└── README.md                   # 本文档
```

## 核心架构

### 1. 动作系统层次结构

```
动作系统 (Actions System)
├── 核心服务层 (Core Service Layer)
│   ├── ActionService (动作服务)
│   ├── ActionContainer (动作容器)
│   └── ActionDialog (动作对话框)
├── 客户端动作层 (Client Actions Layer)
│   ├── ClientActions (内置客户端动作)
│   ├── InstallKioskPWA (PWA安装动作)
│   └── DebugItems (调试动作)
└── 报表子系统 (Reports Subsystem)
    ├── ReportAction (报表动作)
    ├── ReportHook (报表钩子)
    └── ReportUtils (报表工具)
```

**系统特性**:
- **分层架构**: 清晰的分层架构设计
- **模块化**: 高度模块化的组件设计
- **可扩展**: 强大的扩展和插件机制
- **类型安全**: 完整的TypeScript类型支持

### 2. 动作类型支持

```javascript
// 支持的动作类型
const SUPPORTED_ACTION_TYPES = {
    'ir.actions.act_window': '窗口动作',      // 打开视图窗口
    'ir.actions.client': '客户端动作',        // 客户端功能
    'ir.actions.server': '服务器动作',        // 服务器端操作
    'ir.actions.report': '报表动作',          // 生成报表
    'ir.actions.act_url': 'URL动作',          // 打开URL
    'ir.actions.act_window_close': '关闭动作' // 关闭窗口
};
```

**动作类型功能**:
- **窗口动作**: 打开表单、列表、看板等视图
- **客户端动作**: 执行客户端JavaScript功能
- **服务器动作**: 执行服务器端Python代码
- **报表动作**: 生成和显示各种格式的报表
- **URL动作**: 跳转到内部或外部URL
- **关闭动作**: 关闭当前窗口或对话框

## 核心组件

### 1. ActionService - 动作服务核心

**功能**: 动作系统的核心服务，负责动作的解析、执行和管理
- **行数**: 1722行
- **作用**: 动作管理的基础设施
- **特点**: 完整的动作生命周期管理

**核心功能**:
```javascript
// 动作执行
async doAction(actionRequest, options)     // 执行动作
async loadAction(actionId)                 // 加载动作定义
switchView(viewType, options)              // 切换视图
goBack()                                   // 返回上一个动作
closeAction()                              // 关闭当前动作

// 状态管理
get currentAction()                        // 当前动作
get actionStack()                          // 动作栈
get breadcrumbs()                          // 面包屑导航

// 路由集成
updateUrl(action, options)                 // 更新URL
syncStateWithUrl()                         // 同步URL状态
```

### 2. ActionContainer - 动作容器

**功能**: 动作的渲染容器，负责动作组件的显示
- **行数**: 35行
- **作用**: 动作组件的渲染容器
- **特点**: 事件驱动的组件渲染

**核心功能**:
```javascript
// 事件监听
onActionManagerUpdate(info)               // 监听动作更新
addEventListener("ACTION_MANAGER:UPDATE") // 事件订阅

// 动态渲染
renderAction(component, props)            // 渲染动作组件
updateContainer(actionInfo)               // 更新容器内容
```

### 3. ActionDialog - 动作对话框

**功能**: 在对话框中执行动作的组件
- **行数**: 45行
- **作用**: 对话框中的动作执行
- **特点**: 继承Dialog，支持调试

**核心功能**:
```javascript
// 对话框管理
static props = {
    ActionComponent,                       // 动作组件
    actionProps,                          // 动作属性
    close                                 // 关闭回调
}

// 调试支持
useOwnDebugContext()                      // 调试上下文
DebugMenu                                 // 调试菜单
```

### 4. ClientActions - 客户端动作

**功能**: 内置的客户端动作集合
- **行数**: 102行
- **作用**: 提供常用的客户端功能
- **特点**: 即用的内置功能

**核心功能**:
```javascript
// 通知显示
displayNotificationAction(env, action)    // 显示通知
showSuccess(message, options)             // 成功通知
showError(message, options)               // 错误通知

// 页面操作
reload(env, action)                       // 重载页面
redirect(env, action)                     // 重定向
```

### 5. InstallKioskPWA - PWA安装动作

**功能**: Kiosk模式PWA应用的安装功能
- **行数**: 43行
- **作用**: PWA应用安装
- **特点**: 支持Kiosk模式

**核心功能**:
```javascript
// PWA安装
get installURL()                          // 安装URL
get appId()                               // 应用ID
async getKioskUrl()                       // 获取Kiosk URL
```

### 6. DebugItems - 调试项目

**功能**: 开发调试工具集合
- **行数**: 185行
- **作用**: 开发和调试支持
- **特点**: 完整的调试工具

**核心功能**:
```javascript
// 调试功能
editAction(context)                       // 编辑动作
viewFields(context)                       // 查看字段
viewView(context)                         // 查看视图
manageFilters(context)                    // 管理过滤器
viewMetadata(context)                     // 查看元数据
```

## 报表子系统

### 1. ReportAction - 报表动作

**功能**: HTML报表的渲染和显示
- **行数**: 63行
- **作用**: 报表显示组件
- **特点**: iframe安全渲染

**核心功能**:
```javascript
// 报表显示
onIframeLoaded(ev)                        // iframe加载处理
print()                                   // 打印报表
useEnrichWithActionLinks(iframe)          // 链接增强
```

### 2. ReportHook - 报表钩子

**功能**: 自动为HTML内容添加动作链接
- **行数**: 71行
- **作用**: 链接自动增强
- **特点**: 支持iframe和选择器

**核心功能**:
```javascript
// 链接增强
useEnrichWithActionLinks(ref, selector)   // 使用链接增强钩子
enrich(component, element, selector)      // 增强元素
detectActionElements()                    // 检测动作元素
bindActionEvents()                        // 绑定动作事件
```

### 3. ReportUtils - 报表工具

**功能**: 报表URL生成和下载工具
- **行数**: 92行
- **作用**: 报表工具函数
- **特点**: 支持多种格式

**核心功能**:
```javascript
// URL生成
getReportUrl(action, type, context)       // 生成报表URL
buildReportParams(action, data)           // 构建报表参数

// 下载功能
downloadReport(rpc, action, type)         // 下载报表
checkWkhtmltopdfStatus()                  // 检查PDF支持
getWKHTMLTOPDF_MESSAGES(status)          // 获取状态消息
```

## 技术特点

### 1. 完整的动作生命周期

```javascript
// 动作生命周期
const actionLifecycle = {
    // 1. 动作解析
    parseAction: (actionRequest) => {
        // 解析动作请求（ID、XML ID、对象等）
        return actionDefinition;
    },
    
    // 2. 权限验证
    validatePermissions: (action) => {
        // 验证用户权限
        return hasPermission;
    },
    
    // 3. 上下文准备
    prepareContext: (action, options) => {
        // 准备执行上下文
        return executionContext;
    },
    
    // 4. 组件创建
    createComponent: (action, context) => {
        // 创建对应的组件
        return componentInstance;
    },
    
    // 5. 渲染执行
    renderAction: (component, container) => {
        // 渲染到容器
        return renderResult;
    },
    
    // 6. 状态同步
    syncState: (action, options) => {
        // 同步URL和应用状态
        return syncResult;
    }
};
```

### 2. 事件驱动架构

```javascript
// 事件系统
const actionEvents = {
    'ACTION_MANAGER:UPDATE': '动作管理器更新',
    'ACTION_MANAGER:LOADING': '动作加载中',
    'ACTION_MANAGER:LOADED': '动作加载完成',
    'ACTION_MANAGER:ERROR': '动作执行错误',
    'ACTION_MANAGER:GO_BACK': '返回上一个动作',
    'ACTION_MANAGER:CLOSE': '关闭动作'
};
```

### 3. 注册表扩展机制

```javascript
// 注册表类别
const registryCategories = {
    'actions': '动作注册表',           // 客户端动作
    'action_handlers': '动作处理器',   // 动作处理器
    'debug_items': '调试项目',         // 调试功能
    'services': '服务注册表'           // 服务注册
};
```

### 4. 路由集成

```javascript
// URL状态映射
const urlStateMapping = {
    action: 'action_id',              // 动作ID
    model: 'res_model',               // 资源模型
    view_type: 'view_mode',           // 视图类型
    id: 'res_id',                     // 记录ID
    domain: 'domain',                 // 搜索域
    context: 'context'                // 上下文
};
```

## 使用场景

### 1. 基础动作执行

```javascript
// 执行窗口动作
await env.services.action.doAction({
    type: 'ir.actions.act_window',
    res_model: 'res.partner',
    view_mode: 'form',
    res_id: 123,
    target: 'current'
});

// 执行客户端动作
await env.services.action.doAction({
    type: 'ir.actions.client',
    tag: 'display_notification',
    params: {
        message: 'Hello World!',
        type: 'success'
    }
});
```

### 2. 报表生成和显示

```javascript
// 显示HTML报表
await env.services.action.doAction({
    type: 'ir.actions.report',
    report_name: 'sale.report_saleorder',
    report_type: 'qweb-html',
    context: { active_ids: [123, 456] }
});

// 下载PDF报表
const reportUrl = getReportUrl(action, 'pdf', context);
await downloadReport(rpc, action, 'pdf', context);
```

### 3. PWA应用安装

```javascript
// 安装Kiosk PWA
await env.services.action.doAction({
    type: 'ir.actions.client',
    tag: 'install_kiosk_pwa',
    res_model: 'pos.config',
    context: {
        active_id: 1,
        app_id: 'pos_kiosk'
    }
});
```

### 4. 调试功能使用

```javascript
// 编辑当前动作
debugRegistry.get('editAction')({ action: currentAction, env });

// 查看模型字段
debugRegistry.get('viewFields')({ action: currentAction, env });

// 查看记录元数据
debugRegistry.get('viewMetadata')({ action: currentAction, env });
```

## 设计模式

### 1. 服务定位器模式 (Service Locator Pattern)
- **服务注册**: 将动作相关服务注册到服务定位器
- **依赖注入**: 通过服务定位器注入依赖
- **解耦设计**: 组件与具体服务实现解耦

### 2. 策略模式 (Strategy Pattern)
- **动作策略**: 不同动作类型的执行策略
- **渲染策略**: 不同的组件渲染策略
- **路由策略**: 不同的URL路由策略

### 3. 观察者模式 (Observer Pattern)
- **事件系统**: 基于事件的组件通信
- **状态同步**: 动作状态变化的自动同步
- **生命周期**: 动作生命周期事件的分发

### 4. 工厂模式 (Factory Pattern)
- **组件工厂**: 根据动作类型创建组件
- **动作工厂**: 创建不同类型的动作对象
- **服务工厂**: 创建动作相关的服务实例

### 5. 命令模式 (Command Pattern)
- **动作封装**: 将动作封装为命令对象
- **撤销重做**: 支持动作的撤销和重做
- **批量执行**: 批量动作的执行机制

### 6. 装饰器模式 (Decorator Pattern)
- **功能增强**: 通过钩子增强基础功能
- **链接装饰**: 装饰报表中的链接元素
- **调试装饰**: 为组件添加调试功能

## 扩展和定制

### 1. 自定义动作类型

```javascript
// 注册自定义动作处理器
registry.category("action_handlers").add("custom_action", {
    canHandle: (action) => action.type === 'custom.action',
    handle: async (action, options, env) => {
        // 自定义处理逻辑
        return customActionHandler(action, options, env);
    }
});
```

### 2. 自定义客户端动作

```javascript
// 注册客户端动作
registry.category("actions").add("my_dashboard", {
    Component: MyDashboardComponent,
    props: (action) => ({
        title: action.name,
        config: action.params
    })
});
```

### 3. 自定义调试项目

```javascript
// 注册调试项目
debugRegistry.add("customDebugItem", {
    type: "item",
    description: "Custom Debug Function",
    callback: (context) => {
        // 自定义调试逻辑
    },
    sequence: 100,
    section: "custom"
});
```

### 4. 自定义报表处理

```javascript
// 扩展报表功能
class CustomReportAction extends ReportAction {
    setup() {
        super.setup();
        // 自定义初始化
    }
    
    async customReportFunction() {
        // 自定义报表功能
    }
}
```

## 性能优化

### 1. 动作缓存
- **定义缓存**: 缓存动作定义避免重复加载
- **组件缓存**: 缓存组件实例提高性能
- **URL缓存**: 缓存生成的URL减少计算

### 2. 懒加载
- **组件懒加载**: 按需加载动作组件
- **模块懒加载**: 按需加载功能模块
- **资源懒加载**: 按需加载相关资源

### 3. 批量操作
- **批量动作**: 支持批量执行多个动作
- **批量更新**: 批量更新动作状态
- **批量渲染**: 优化批量组件渲染

### 4. 内存管理
- **组件清理**: 及时清理不再使用的组件
- **事件清理**: 清理事件监听器避免内存泄漏
- **缓存清理**: 定期清理过期的缓存数据

## 最佳实践

### 1. 动作设计
```javascript
// 良好的动作设计
const goodAction = {
    type: 'ir.actions.act_window',
    name: 'Customer Form',
    res_model: 'res.partner',
    view_mode: 'form',
    res_id: 123,
    target: 'current',
    context: {
        default_customer_rank: 1,
        form_view_initial_mode: 'edit'
    }
};
```

### 2. 错误处理
```javascript
// 完善的错误处理
try {
    await env.services.action.doAction(action);
} catch (error) {
    console.error('Action execution failed:', error);
    env.services.notification.add(
        'Failed to execute action: ' + error.message,
        { type: 'danger' }
    );
}
```

### 3. 性能监控
```javascript
// 性能监控
const startTime = performance.now();
await env.services.action.doAction(action);
const endTime = performance.now();
console.log(`Action execution time: ${endTime - startTime}ms`);
```

## 注意事项

1. **内存管理**: 避免动作栈过深导致内存问题
2. **权限检查**: 确保动作执行前的权限验证
3. **错误处理**: 完善的动作执行错误处理
4. **性能监控**: 监控动作执行的性能指标
5. **安全性**: 确保动作执行的安全性
6. **兼容性**: 保持向后兼容性

## 总结

Actions 动作系统是 Odoo Web 客户端的核心基础设施，通过完整的动作生命周期管理、事件驱动架构、强大的扩展机制和丰富的功能组件，为整个应用提供了统一、高效、可扩展的动作执行平台。

**核心优势**:
- **统一架构**: 统一的动作执行和管理架构
- **完整生命周期**: 从解析到渲染的完整生命周期
- **事件驱动**: 基于事件的松耦合架构
- **强大扩展**: 基于注册表的强大扩展机制
- **类型安全**: 完整的TypeScript类型支持
- **性能优化**: 多层次的性能优化机制
- **开发友好**: 完整的调试和开发工具支持

该系统为Odoo Web应用的各种用户交互提供了坚实的基础，确保了动作执行的一致性、可靠性和高性能。无论是简单的页面跳转还是复杂的业务流程，Actions系统都能提供专业的解决方案。

# ActionDialog - 动作对话框组件

## 概述

`action_dialog.js` 是 Odoo Web 客户端的动作对话框组件，提供了在对话框中执行动作的功能。该模块包含45行代码，继承自Dialog基类，专门用于在模态对话框中渲染和执行各种动作，具备调试支持、自定义样式、动态内容等特性，是Odoo Web客户端中处理弹窗动作的核心组件。

## 文件信息
- **路径**: `/web/static/src/webclient/actions/action_dialog.js`
- **行数**: 45
- **模块**: `@web/webclient/actions/action_dialog`

## 依赖关系

```javascript
// 核心依赖
'@web/core/dialog/dialog'           // 对话框基类
'@web/core/debug/debug_menu'        // 调试菜单
'@web/core/debug/debug_context'     // 调试上下文
'@odoo/owl'                         // OWL框架
```

## 主组件定义

### 1. ActionDialog 组件

```javascript
class ActionDialog extends Dialog {
    static components = { ...Dialog.components, DebugMenu };
    static template = "web.ActionDialog";
    static props = {
        ...Dialog.props,
        close: Function,
        slots: { optional: true },
        ActionComponent: { optional: true },
        actionProps: { optional: true },
        actionType: { optional: true },
        title: { optional: true },
    };
    static defaultProps = {
        ...Dialog.defaultProps,
        withBodyPadding: false,
    };

    setup() {
        super.setup();
        useOwnDebugContext();
        useEffect(
            () => {
                if (this.modalRef.el.querySelector(".modal-footer")?.childElementCount > 1) {
                    const defaultButton = this.modalRef.el.querySelector(
                        ".modal-footer button.o-default-button"
                    );
                    defaultButton.classList.add("d-none");
                }
            },
            () => []
        );
    }
}
```

**组件特性**:
- **继承Dialog**: 继承标准对话框的所有功能
- **调试支持**: 集成调试菜单和调试上下文
- **动态内容**: 支持动态渲染动作组件
- **样式定制**: 自定义对话框样式和行为

## 核心属性

### 1. 组件属性

```javascript
static props = {
    ...Dialog.props,           // 继承Dialog的所有属性
    close: Function,           // 关闭回调函数
    slots: { optional: true }, // 可选插槽
    ActionComponent: { optional: true }, // 动作组件
    actionProps: { optional: true },     // 动作组件属性
    actionType: { optional: true },      // 动作类型
    title: { optional: true },           // 对话框标题
};
```

**属性功能**:
- **close**: 对话框关闭时的回调函数
- **slots**: 支持自定义插槽内容
- **ActionComponent**: 要在对话框中渲染的动作组件
- **actionProps**: 传递给动作组件的属性
- **actionType**: 动作的类型标识
- **title**: 对话框的标题

### 2. 默认属性

```javascript
static defaultProps = {
    ...Dialog.defaultProps,
    withBodyPadding: false,  // 默认不添加body内边距
};
```

**默认属性功能**:
- **withBodyPadding**: 控制对话框主体是否有内边距
- **继承默认值**: 继承Dialog的所有默认属性

## 核心功能

### 1. 调试支持

```javascript
// 设置调试上下文
useOwnDebugContext();

// 集成调试菜单
static components = { ...Dialog.components, DebugMenu };
```

**调试支持功能**:
- **调试上下文**: 为对话框创建独立的调试上下文
- **调试菜单**: 集成调试菜单组件
- **开发工具**: 支持开发和调试工具
- **错误追踪**: 便于错误追踪和调试

### 2. 按钮管理

```javascript
useEffect(
    () => {
        if (this.modalRef.el.querySelector(".modal-footer")?.childElementCount > 1) {
            const defaultButton = this.modalRef.el.querySelector(
                ".modal-footer button.o-default-button"
            );
            defaultButton.classList.add("d-none");
        }
    },
    () => []
);
```

**按钮管理功能**:
- **按钮检测**: 检测对话框底部的按钮数量
- **默认按钮**: 管理默认按钮的显示状态
- **样式控制**: 动态控制按钮的样式
- **用户体验**: 优化用户交互体验

### 3. 模板渲染

```javascript
static template = "web.ActionDialog";
```

**模板渲染功能**:
- **专用模板**: 使用专门的ActionDialog模板
- **动态内容**: 支持动态渲染动作内容
- **布局控制**: 控制对话框的布局结构
- **样式集成**: 集成Odoo的样式系统

## 使用场景

### 1. 表单动作对话框

```javascript
// 在对话框中打开表单视图
class FormActionDialog extends ActionDialog {
    static template = xml`
        <Dialog t-props="dialogProps">
            <t t-set-slot="header">
                <div class="modal-title">
                    <t t-esc="props.title or 'Form View'"/>
                </div>
                <DebugMenu t-if="env.debug"/>
            </t>
            
            <t t-set-slot="body">
                <div class="o_action_dialog_content">
                    <t t-if="props.ActionComponent"
                       t-component="props.ActionComponent"
                       t-props="props.actionProps"/>
                </div>
            </t>
            
            <t t-set-slot="footer">
                <button class="btn btn-primary" 
                        t-on-click="save"
                        t-att-disabled="!canSave">
                    Save
                </button>
                <button class="btn btn-secondary" 
                        t-on-click="props.close">
                    Cancel
                </button>
            </t>
        </Dialog>`;
    
    setup() {
        super.setup();
        this.canSave = true;
    }
    
    async save() {
        try {
            if (this.props.ActionComponent && this.props.ActionComponent.save) {
                await this.props.ActionComponent.save();
                this.props.close();
            }
        } catch (error) {
            console.error('Save failed:', error);
            this.env.services.notification.add(
                'Save failed: ' + error.message,
                { type: 'danger' }
            );
        }
    }
}

// 使用示例
const formDialog = new FormActionDialog(null, {
    title: 'Edit Partner',
    ActionComponent: FormView,
    actionProps: {
        resModel: 'res.partner',
        resId: 123,
        mode: 'edit'
    },
    close: () => {
        formDialog.destroy();
    }
});
```

### 2. 向导动作对话框

```javascript
// 多步骤向导对话框
class WizardActionDialog extends ActionDialog {
    static template = xml`
        <Dialog t-props="dialogProps" size="'lg'">
            <t t-set-slot="header">
                <div class="modal-title">
                    <t t-esc="props.title"/>
                </div>
                <div class="wizard-progress">
                    <div class="progress">
                        <div class="progress-bar" 
                             t-att-style="'width: ' + progressPercentage + '%'"/>
                    </div>
                    <span class="step-indicator">
                        Step <t t-esc="currentStep"/> of <t t-esc="totalSteps"/>
                    </span>
                </div>
            </t>
            
            <t t-set-slot="body">
                <div class="wizard-content">
                    <t t-foreach="steps" t-as="step" t-key="step.id">
                        <div t-if="step.id === currentStepId" 
                             class="wizard-step">
                            <t t-component="step.component" 
                               t-props="step.props"/>
                        </div>
                    </t>
                </div>
            </t>
            
            <t t-set-slot="footer">
                <button class="btn btn-secondary" 
                        t-on-click="previousStep"
                        t-att-disabled="!canGoPrevious">
                    <i class="fa fa-arrow-left"/> Previous
                </button>
                <button class="btn btn-primary" 
                        t-on-click="nextStep"
                        t-att-disabled="!canGoNext">
                    <t t-if="isLastStep">Finish</t>
                    <t t-else>Next <i class="fa fa-arrow-right"/></t>
                </button>
                <button class="btn btn-secondary" 
                        t-on-click="props.close">
                    Cancel
                </button>
            </t>
        </Dialog>`;
    
    setup() {
        super.setup();
        this.currentStep = 1;
        this.currentStepId = this.props.steps[0].id;
        this.stepData = {};
    }
    
    get totalSteps() {
        return this.props.steps.length;
    }
    
    get progressPercentage() {
        return (this.currentStep / this.totalSteps) * 100;
    }
    
    get canGoPrevious() {
        return this.currentStep > 1;
    }
    
    get canGoNext() {
        return this.validateCurrentStep();
    }
    
    get isLastStep() {
        return this.currentStep === this.totalSteps;
    }
    
    validateCurrentStep() {
        const currentStepComponent = this.getCurrentStepComponent();
        if (currentStepComponent && currentStepComponent.validate) {
            return currentStepComponent.validate();
        }
        return true;
    }
    
    getCurrentStepComponent() {
        // 获取当前步骤的组件实例
        const stepElement = this.el.querySelector(`[data-step-id="${this.currentStepId}"]`);
        return stepElement?.__owl__;
    }
    
    async nextStep() {
        if (!this.canGoNext) return;
        
        // 保存当前步骤数据
        const currentStepComponent = this.getCurrentStepComponent();
        if (currentStepComponent && currentStepComponent.getData) {
            this.stepData[this.currentStepId] = await currentStepComponent.getData();
        }
        
        if (this.isLastStep) {
            await this.finish();
        } else {
            this.currentStep++;
            this.currentStepId = this.props.steps[this.currentStep - 1].id;
            this.render();
        }
    }
    
    previousStep() {
        if (!this.canGoPrevious) return;
        
        this.currentStep--;
        this.currentStepId = this.props.steps[this.currentStep - 1].id;
        this.render();
    }
    
    async finish() {
        try {
            if (this.props.onFinish) {
                await this.props.onFinish(this.stepData);
            }
            this.props.close();
        } catch (error) {
            console.error('Wizard finish failed:', error);
            this.env.services.notification.add(
                'Operation failed: ' + error.message,
                { type: 'danger' }
            );
        }
    }
}

// 使用示例
const wizardDialog = new WizardActionDialog(null, {
    title: 'Import Data Wizard',
    steps: [
        {
            id: 'select_file',
            component: FileSelectionStep,
            props: { acceptedTypes: ['.csv', '.xlsx'] }
        },
        {
            id: 'map_fields',
            component: FieldMappingStep,
            props: { targetModel: 'res.partner' }
        },
        {
            id: 'preview',
            component: ImportPreviewStep,
            props: {}
        }
    ],
    onFinish: async (data) => {
        await importService.importData(data);
    },
    close: () => {
        wizardDialog.destroy();
    }
});
```

### 3. 报表动作对话框

```javascript
// 报表预览对话框
class ReportActionDialog extends ActionDialog {
    static template = xml`
        <Dialog t-props="dialogProps" size="'xl'" fullscreen="true">
            <t t-set-slot="header">
                <div class="modal-title">
                    <i class="fa fa-file-pdf-o"/> <t t-esc="props.title"/>
                </div>
                <div class="report-controls">
                    <button class="btn btn-sm btn-secondary" 
                            t-on-click="downloadPdf">
                        <i class="fa fa-download"/> Download PDF
                    </button>
                    <button class="btn btn-sm btn-secondary" 
                            t-on-click="print">
                        <i class="fa fa-print"/> Print
                    </button>
                    <button class="btn btn-sm btn-secondary" 
                            t-on-click="refresh">
                        <i class="fa fa-refresh"/> Refresh
                    </button>
                </div>
            </t>
            
            <t t-set-slot="body">
                <div class="report-viewer">
                    <iframe t-if="reportUrl" 
                            t-att-src="reportUrl"
                            class="report-frame"
                            frameborder="0"/>
                    <div t-else class="report-loading">
                        <i class="fa fa-spinner fa-spin"/> Generating report...
                    </div>
                </div>
            </t>
            
            <t t-set-slot="footer">
                <button class="btn btn-secondary" 
                        t-on-click="props.close">
                    Close
                </button>
            </t>
        </Dialog>`;
    
    setup() {
        super.setup();
        this.reportUrl = null;
        this.generateReport();
    }
    
    async generateReport() {
        try {
            const reportData = await this.env.services.rpc('/web/report/pdf', {
                report_name: this.props.reportName,
                data: this.props.reportData,
                context: this.props.context
            });
            
            this.reportUrl = `data:application/pdf;base64,${reportData}`;
            this.render();
        } catch (error) {
            console.error('Report generation failed:', error);
            this.env.services.notification.add(
                'Failed to generate report: ' + error.message,
                { type: 'danger' }
            );
        }
    }
    
    async downloadPdf() {
        if (this.reportUrl) {
            const link = document.createElement('a');
            link.href = this.reportUrl;
            link.download = `${this.props.title || 'report'}.pdf`;
            link.click();
        }
    }
    
    print() {
        if (this.reportUrl) {
            const printWindow = window.open(this.reportUrl);
            printWindow.onload = () => {
                printWindow.print();
            };
        }
    }
    
    refresh() {
        this.reportUrl = null;
        this.render();
        this.generateReport();
    }
}

// 使用示例
const reportDialog = new ReportActionDialog(null, {
    title: 'Invoice Report',
    reportName: 'account.report_invoice',
    reportData: { invoice_ids: [123, 456] },
    context: { lang: 'en_US' },
    close: () => {
        reportDialog.destroy();
    }
});
```

## 技术特点

### 1. 继承扩展
- **Dialog继承**: 继承标准对话框的所有功能
- **功能扩展**: 在基础功能上扩展动作相关功能
- **属性继承**: 继承并扩展属性定义
- **方法重写**: 重写和扩展基类方法

### 2. 调试集成
- **调试上下文**: 独立的调试上下文管理
- **调试菜单**: 集成调试菜单组件
- **开发支持**: 完善的开发和调试支持
- **错误处理**: 增强的错误处理和追踪

### 3. 动态内容
- **组件渲染**: 动态渲染动作组件
- **属性传递**: 灵活的属性传递机制
- **内容适配**: 自动适配不同类型的动作内容
- **布局优化**: 优化的布局和样式

### 4. 用户体验
- **按钮管理**: 智能的按钮显示管理
- **样式定制**: 自定义样式和外观
- **交互优化**: 优化的用户交互体验
- **响应式**: 响应式设计支持

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- **功能增强**: 在Dialog基础上增强功能
- **透明扩展**: 透明地扩展对话框功能
- **组合使用**: 可以与其他装饰器组合使用

### 2. 模板方法模式 (Template Method Pattern)
- **基础框架**: 提供对话框的基础框架
- **扩展点**: 定义可扩展的方法和属性
- **定制化**: 支持子类的定制化实现

### 3. 策略模式 (Strategy Pattern)
- **内容策略**: 不同动作类型的内容渲染策略
- **交互策略**: 不同的用户交互策略
- **样式策略**: 不同的样式和布局策略

## 注意事项

1. **内存管理**: 确保对话框关闭时正确清理资源
2. **事件处理**: 正确处理对话框的各种事件
3. **样式冲突**: 避免与其他组件的样式冲突
4. **性能优化**: 优化大型动作组件的渲染性能

## 扩展建议

1. **动画效果**: 添加对话框的打开和关闭动画
2. **拖拽支持**: 支持对话框的拖拽移动
3. **大小调整**: 支持对话框大小的动态调整
4. **多实例管理**: 支持多个对话框实例的管理
5. **键盘导航**: 增强键盘导航支持

该动作对话框组件为Odoo Web客户端提供了强大的弹窗动作执行功能，通过继承和扩展标准对话框，实现了专门用于动作执行的对话框组件。

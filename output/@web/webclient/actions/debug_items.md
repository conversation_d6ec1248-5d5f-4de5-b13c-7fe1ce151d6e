# Debug Items - 调试项目

## 概述

`debug_items.js` 是 Odoo Web 客户端的调试项目模块，提供了一系列调试功能的菜单项。该模块包含185行代码，定义了多个调试相关的功能项，包括编辑动作、查看字段、查看视图、管理过滤器、查看元数据等功能，具备开发调试、系统分析、数据检查等特性，是Odoo Web客户端开发和调试的重要工具集合。

## 文件信息
- **路径**: `/web/static/src/webclient/actions/debug_items.js`
- **行数**: 185
- **模块**: `@web/webclient/actions/debug_items`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'    // 国际化翻译
'@web/core/debug/debug_utils'   // 调试工具
'@web/core/registry'            // 注册表系统
```

## 核心调试项目

### 1. 编辑动作

```javascript
function editAction({ action, env }) {
    if (!action.id) {
        return null;
    }
    const description = _t("Action");
    return {
        type: "item",
        description,
        callback: () => {
            editModelDebug(env, description, action.type, action.id);
        },
        sequence: 220,
        section: "ui",
    };
}

debugRegistry.add("editAction", editAction);
```

**编辑动作功能**:
- **动作编辑**: 编辑当前动作的定义
- **类型检查**: 检查动作是否有有效ID
- **调试工具**: 使用调试工具打开编辑界面
- **UI分组**: 归类到UI调试分组

### 2. 查看字段

```javascript
function viewFields({ action, env }) {
    if (!action.res_model) {
        return null;
    }
    const description = _t("Fields");
    return {
        type: "item",
        description,
        callback: async () => {
            const modelId = (
                await env.services.orm.search("ir.model", [["model", "=", action.res_model]], {
                    limit: 1,
                })
            )[0];
            env.services.action.doAction({
                res_model: "ir.model.fields",
                name: description,
                views: [
                    [false, "list"],
                    [false, "form"],
                ],
                type: "ir.actions.act_window",
                domain: [["model_id", "=", modelId]],
                context: { search_default_base: 1 },
            });
        },
        sequence: 230,
        section: "ui",
    };
}

debugRegistry.add("viewFields", viewFields);
```

**查看字段功能**:
- **字段列表**: 显示模型的所有字段
- **模型查找**: 根据模型名查找模型ID
- **过滤显示**: 只显示当前模型的字段
- **多视图**: 支持列表和表单视图

### 3. 查看视图

```javascript
function viewView({ action, env }) {
    if (!action.views) {
        return null;
    }
    const description = _t("View");
    return {
        type: "item",
        description,
        callback: () => {
            const activeView = action.views.find((view) => view[1] === action.view_mode);
            if (activeView) {
                editModelDebug(env, description, "ir.ui.view", activeView[0]);
            }
        },
        sequence: 240,
        section: "ui",
    };
}

debugRegistry.add("viewView", viewView);
```

**查看视图功能**:
- **视图编辑**: 编辑当前活动视图
- **视图查找**: 查找当前视图模式对应的视图
- **视图调试**: 使用调试工具编辑视图定义
- **条件显示**: 只在有视图时显示

### 4. 管理过滤器

```javascript
function manageFilters({ action, env }) {
    if (!action.res_model) {
        return null;
    }
    const description = _t("Manage Filters");
    return {
        type: "item",
        description,
        callback: () => {
            env.services.action.doAction({
                res_model: "ir.filters",
                name: description,
                views: [
                    [false, "list"],
                    [false, "form"],
                ],
                type: "ir.actions.act_window",
                domain: [["model_id", "=", action.res_model]],
                context: { search_default_my_filters: 1 },
            });
        },
        sequence: 250,
        section: "ui",
    };
}

debugRegistry.add("manageFilters", manageFilters);
```

**管理过滤器功能**:
- **过滤器管理**: 管理当前模型的过滤器
- **用户过滤器**: 默认显示用户的过滤器
- **过滤器编辑**: 支持编辑和删除过滤器
- **模型关联**: 只显示当前模型的过滤器

### 5. 查看元数据

```javascript
function viewMetadata({ action, env }) {
    if (!action.res_model || !action.res_id) {
        return null;
    }
    const description = _t("View Metadata");
    return {
        type: "item",
        description,
        callback: () => {
            env.services.action.doAction({
                res_model: action.res_model,
                res_id: action.res_id,
                type: "ir.actions.act_window",
                views: [[false, "form"]],
                view_mode: "form",
                target: "new",
                context: { form_view_ref: "base.view_attachment_form" },
            });
        },
        sequence: 260,
        section: "ui",
    };
}

debugRegistry.add("viewMetadata", viewMetadata);
```

**查看元数据功能**:
- **记录元数据**: 查看当前记录的元数据
- **附件信息**: 显示记录的附件信息
- **对话框**: 在新对话框中打开
- **记录检查**: 只在有具体记录时显示

## 使用场景

### 1. 开发调试工具集

```javascript
// 开发调试工具集
class DevelopmentDebugTools {
    constructor(env) {
        this.env = env;
        this.debugItems = new Map();
        this.setupDebugItems();
    }
    
    setupDebugItems() {
        // 注册所有调试项目
        this.registerDebugItem('editAction', this.createEditActionItem());
        this.registerDebugItem('viewFields', this.createViewFieldsItem());
        this.registerDebugItem('viewView', this.createViewViewItem());
        this.registerDebugItem('manageFilters', this.createManageFiltersItem());
        this.registerDebugItem('viewMetadata', this.createViewMetadataItem());
        this.registerDebugItem('exportData', this.createExportDataItem());
        this.registerDebugItem('importData', this.createImportDataItem());
        this.registerDebugItem('runTests', this.createRunTestsItem());
    }
    
    registerDebugItem(key, item) {
        this.debugItems.set(key, item);
    }
    
    createEditActionItem() {
        return {
            name: 'Edit Action',
            description: 'Edit the current action definition',
            icon: 'fa-edit',
            section: 'development',
            callback: (context) => {
                if (context.action && context.action.id) {
                    return this.env.services.action.doAction({
                        type: 'ir.actions.act_window',
                        res_model: context.action.type,
                        res_id: context.action.id,
                        view_mode: 'form',
                        target: 'new'
                    });
                }
            }
        };
    }
    
    createViewFieldsItem() {
        return {
            name: 'View Fields',
            description: 'View all fields of the current model',
            icon: 'fa-list',
            section: 'development',
            callback: async (context) => {
                if (context.action && context.action.res_model) {
                    const modelId = await this.getModelId(context.action.res_model);
                    return this.env.services.action.doAction({
                        type: 'ir.actions.act_window',
                        res_model: 'ir.model.fields',
                        view_mode: 'list,form',
                        domain: [['model_id', '=', modelId]],
                        context: { search_default_base: 1 }
                    });
                }
            }
        };
    }
    
    createViewViewItem() {
        return {
            name: 'View Definition',
            description: 'View the current view definition',
            icon: 'fa-code',
            section: 'development',
            callback: (context) => {
                if (context.action && context.action.views) {
                    const activeView = context.action.views.find(
                        view => view[1] === context.action.view_mode
                    );
                    if (activeView && activeView[0]) {
                        return this.env.services.action.doAction({
                            type: 'ir.actions.act_window',
                            res_model: 'ir.ui.view',
                            res_id: activeView[0],
                            view_mode: 'form',
                            target: 'new'
                        });
                    }
                }
            }
        };
    }
    
    createExportDataItem() {
        return {
            name: 'Export Data',
            description: 'Export current data for debugging',
            icon: 'fa-download',
            section: 'data',
            callback: async (context) => {
                if (context.action && context.action.res_model) {
                    const data = await this.exportModelData(
                        context.action.res_model,
                        context.action.domain || []
                    );
                    this.downloadData(data, `${context.action.res_model}_debug.json`);
                }
            }
        };
    }
    
    createRunTestsItem() {
        return {
            name: 'Run Tests',
            description: 'Run tests for the current module',
            icon: 'fa-play',
            section: 'testing',
            callback: async (context) => {
                if (context.action && context.action.res_model) {
                    const module = await this.getModuleForModel(context.action.res_model);
                    if (module) {
                        return this.runModuleTests(module);
                    }
                }
            }
        };
    }
    
    async getModelId(modelName) {
        const result = await this.env.services.orm.search(
            'ir.model',
            [['model', '=', modelName]],
            { limit: 1 }
        );
        return result[0];
    }
    
    async exportModelData(modelName, domain) {
        try {
            const records = await this.env.services.orm.searchRead(
                modelName,
                domain,
                [],
                { limit: 100 }
            );
            
            return {
                model: modelName,
                domain: domain,
                count: records.length,
                records: records,
                exported_at: new Date().toISOString()
            };
        } catch (error) {
            console.error('Export failed:', error);
            throw error;
        }
    }
    
    downloadData(data, filename) {
        const blob = new Blob([JSON.stringify(data, null, 2)], {
            type: 'application/json'
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();
        URL.revokeObjectURL(url);
    }
    
    async getModuleForModel(modelName) {
        try {
            const result = await this.env.services.rpc('/web/debug/get_module_for_model', {
                model: modelName
            });
            return result.module;
        } catch (error) {
            console.error('Failed to get module:', error);
            return null;
        }
    }
    
    async runModuleTests(moduleName) {
        try {
            const result = await this.env.services.rpc('/web/debug/run_tests', {
                module: moduleName
            });
            
            this.env.services.notification.add(
                `Tests completed for ${moduleName}: ${result.passed}/${result.total} passed`,
                { type: result.failed > 0 ? 'warning' : 'success' }
            );
            
            return result;
        } catch (error) {
            console.error('Test execution failed:', error);
            this.env.services.notification.add(
                'Test execution failed: ' + error.message,
                { type: 'danger' }
            );
        }
    }
    
    getDebugItems(context) {
        const items = [];
        
        for (const [key, item] of this.debugItems) {
            if (this.isItemAvailable(item, context)) {
                items.push({
                    key: key,
                    ...item,
                    callback: () => item.callback(context)
                });
            }
        }
        
        return items.sort((a, b) => (a.sequence || 0) - (b.sequence || 0));
    }
    
    isItemAvailable(item, context) {
        // 检查项目是否在当前上下文中可用
        if (item.requires) {
            for (const requirement of item.requires) {
                if (!this.checkRequirement(requirement, context)) {
                    return false;
                }
            }
        }
        return true;
    }
    
    checkRequirement(requirement, context) {
        switch (requirement) {
            case 'action_id':
                return context.action && context.action.id;
            case 'res_model':
                return context.action && context.action.res_model;
            case 'res_id':
                return context.action && context.action.res_id;
            case 'views':
                return context.action && context.action.views;
            default:
                return true;
        }
    }
}

// 使用示例
const debugTools = new DevelopmentDebugTools(env);

// 获取当前上下文的调试项目
const debugItems = debugTools.getDebugItems({
    action: currentAction,
    user: env.services.user,
    context: env.context
});

// 显示调试菜单
debugItems.forEach(item => {
    console.log(`${item.name}: ${item.description}`);
});
```

### 2. 调试菜单管理器

```javascript
// 调试菜单管理器
class DebugMenuManager {
    constructor(env) {
        this.env = env;
        this.menuItems = new Map();
        this.sections = new Map();
        this.setupDefaultSections();
    }
    
    setupDefaultSections() {
        this.addSection('ui', {
            name: 'User Interface',
            icon: 'fa-desktop',
            order: 1
        });
        
        this.addSection('data', {
            name: 'Data Management',
            icon: 'fa-database',
            order: 2
        });
        
        this.addSection('development', {
            name: 'Development',
            icon: 'fa-code',
            order: 3
        });
        
        this.addSection('testing', {
            name: 'Testing',
            icon: 'fa-bug',
            order: 4
        });
        
        this.addSection('performance', {
            name: 'Performance',
            icon: 'fa-tachometer',
            order: 5
        });
    }
    
    addSection(key, section) {
        this.sections.set(key, section);
    }
    
    addMenuItem(key, item) {
        this.menuItems.set(key, {
            ...item,
            key: key,
            section: item.section || 'development'
        });
    }
    
    removeMenuItem(key) {
        this.menuItems.delete(key);
    }
    
    getMenuStructure(context) {
        const structure = new Map();
        
        // 初始化所有分组
        for (const [sectionKey, section] of this.sections) {
            structure.set(sectionKey, {
                ...section,
                items: []
            });
        }
        
        // 添加菜单项到对应分组
        for (const [key, item] of this.menuItems) {
            if (this.isItemVisible(item, context)) {
                const section = structure.get(item.section);
                if (section) {
                    section.items.push(item);
                }
            }
        }
        
        // 排序菜单项
        for (const section of structure.values()) {
            section.items.sort((a, b) => (a.sequence || 0) - (b.sequence || 0));
        }
        
        // 移除空分组
        for (const [key, section] of structure) {
            if (section.items.length === 0) {
                structure.delete(key);
            }
        }
        
        return Array.from(structure.values())
            .sort((a, b) => a.order - b.order);
    }
    
    isItemVisible(item, context) {
        if (item.condition) {
            return item.condition(context);
        }
        return true;
    }
    
    executeMenuItem(key, context) {
        const item = this.menuItems.get(key);
        if (item && item.callback) {
            try {
                return item.callback(context);
            } catch (error) {
                console.error(`Debug menu item ${key} failed:`, error);
                this.env.services.notification.add(
                    `Debug action failed: ${error.message}`,
                    { type: 'danger' }
                );
            }
        }
    }
    
    registerStandardItems() {
        // 注册标准调试项目
        this.addMenuItem('editAction', {
            name: 'Edit Action',
            description: 'Edit current action',
            icon: 'fa-edit',
            section: 'ui',
            sequence: 10,
            condition: (ctx) => ctx.action && ctx.action.id,
            callback: (ctx) => this.editAction(ctx)
        });
        
        this.addMenuItem('viewFields', {
            name: 'View Fields',
            description: 'View model fields',
            icon: 'fa-list',
            section: 'development',
            sequence: 20,
            condition: (ctx) => ctx.action && ctx.action.res_model,
            callback: (ctx) => this.viewFields(ctx)
        });
        
        this.addMenuItem('viewMetadata', {
            name: 'View Metadata',
            description: 'View record metadata',
            icon: 'fa-info',
            section: 'data',
            sequence: 30,
            condition: (ctx) => ctx.action && ctx.action.res_id,
            callback: (ctx) => this.viewMetadata(ctx)
        });
        
        this.addMenuItem('runProfiler', {
            name: 'Run Profiler',
            description: 'Profile current action',
            icon: 'fa-tachometer',
            section: 'performance',
            sequence: 40,
            callback: (ctx) => this.runProfiler(ctx)
        });
    }
    
    async editAction(context) {
        const action = context.action;
        if (action && action.id) {
            return this.env.services.action.doAction({
                type: 'ir.actions.act_window',
                res_model: action.type,
                res_id: action.id,
                view_mode: 'form',
                target: 'new'
            });
        }
    }
    
    async viewFields(context) {
        const action = context.action;
        if (action && action.res_model) {
            const modelId = await this.getModelId(action.res_model);
            return this.env.services.action.doAction({
                type: 'ir.actions.act_window',
                res_model: 'ir.model.fields',
                view_mode: 'list,form',
                domain: [['model_id', '=', modelId]]
            });
        }
    }
    
    async viewMetadata(context) {
        const action = context.action;
        if (action && action.res_model && action.res_id) {
            return this.env.services.action.doAction({
                type: 'ir.actions.act_window',
                res_model: action.res_model,
                res_id: action.res_id,
                view_mode: 'form',
                target: 'new',
                context: { form_view_ref: 'base.view_attachment_form' }
            });
        }
    }
    
    async runProfiler(context) {
        // 启动性能分析器
        console.profile('Odoo Action Profile');
        
        this.env.services.notification.add(
            'Profiler started. Check browser dev tools for results.',
            { type: 'info' }
        );
        
        // 设置自动停止
        setTimeout(() => {
            console.profileEnd('Odoo Action Profile');
            this.env.services.notification.add(
                'Profiler stopped.',
                { type: 'success' }
            );
        }, 10000);
    }
    
    async getModelId(modelName) {
        const result = await this.env.services.orm.search(
            'ir.model',
            [['model', '=', modelName]],
            { limit: 1 }
        );
        return result[0];
    }
}

// 使用示例
const debugMenuManager = new DebugMenuManager(env);
debugMenuManager.registerStandardItems();

// 获取调试菜单结构
const menuStructure = debugMenuManager.getMenuStructure({
    action: currentAction,
    user: env.services.user
});

// 执行调试项目
debugMenuManager.executeMenuItem('editAction', { action: currentAction });
```

## 技术特点

### 1. 开发友好
- **调试工具**: 完整的开发调试工具集
- **快速访问**: 快速访问系统内部信息
- **开发效率**: 提高开发和调试效率
- **错误诊断**: 帮助诊断和解决问题

### 2. 系统分析
- **元数据查看**: 查看系统元数据信息
- **字段分析**: 分析模型字段结构
- **视图检查**: 检查视图定义和结构
- **过滤器管理**: 管理和调试过滤器

### 3. 条件显示
- **智能显示**: 根据上下文智能显示菜单项
- **权限检查**: 检查用户权限和访问控制
- **状态感知**: 感知当前应用状态
- **动态菜单**: 动态生成调试菜单

### 4. 注册表集成
- **模块化**: 基于注册表的模块化设计
- **可扩展**: 易于扩展新的调试功能
- **标准化**: 标准化的调试项目格式
- **插件支持**: 支持调试插件的开发

## 设计模式

### 1. 注册表模式 (Registry Pattern)
- **功能注册**: 将调试功能注册到调试注册表
- **动态加载**: 动态加载和管理调试项目
- **插件架构**: 支持插件式的功能扩展

### 2. 策略模式 (Strategy Pattern)
- **调试策略**: 不同类型的调试策略
- **显示策略**: 不同的菜单显示策略
- **执行策略**: 不同的功能执行策略

### 3. 工厂模式 (Factory Pattern)
- **项目创建**: 创建不同类型的调试项目
- **菜单生成**: 生成调试菜单结构
- **工具创建**: 创建调试工具实例

## 注意事项

1. **权限控制**: 确保只有开发者能访问调试功能
2. **性能影响**: 避免调试功能影响生产环境性能
3. **数据安全**: 保护敏感数据不被泄露
4. **错误处理**: 完善的调试功能错误处理

## 扩展建议

1. **性能分析**: 添加更多性能分析工具
2. **日志查看**: 集成日志查看功能
3. **数据库查询**: 添加数据库查询调试
4. **API测试**: 集成API测试工具
5. **自动化测试**: 集成自动化测试功能

该调试项目模块为Odoo Web客户端提供了完整的开发调试工具集，通过模块化的设计和丰富的功能确保了开发和调试的高效性。

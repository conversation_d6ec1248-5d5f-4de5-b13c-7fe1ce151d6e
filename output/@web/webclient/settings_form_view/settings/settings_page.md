# SettingsPage - 设置页面组件

## 概述

`settings_page.js` 是 Odoo Web 客户端的设置页面组件，提供了设置表单的页面级管理和导航功能。该模块包含105行代码，是一个OWL组件，专门用于管理设置页面的标签切换、滑动导航、滚动位置记忆等功能，具备标签页管理、URL锚点处理、滑动手势支持、滚动状态保持等特性，是Odoo Web设置表单视图中页面级组织的核心组件。

## 文件信息
- **路径**: `/web/static/src/webclient/settings_form_view/settings/settings_page.js`
- **行数**: 105
- **模块**: `@web/webclient/settings_form_view/settings/settings_page`

## 依赖关系

```javascript
// 核心依赖
'@web/core/action_swiper/action_swiper'         // 动作滑动组件
'@odoo/owl'                                     // OWL框架
'@web/core/browser/browser'                     // 浏览器服务
```

## 主组件定义

### 1. SettingsPage 组件

```javascript
class SettingsPage extends Component {
    static template = "web.SettingsPage";
    static components = { ActionSwiper };
    static props = {
        modules: Array,
        anchors: Array,
        initialTab: { type: String, optional: 1 },
        slots: Object,
    };
    
    setup() {
        this.state = useState({
            selectedTab: "",
            search: this.env.searchState,
        });

        if (this.props.modules) {
            let selectedTab = this.props.initialTab || this.props.modules[0].key;

            if (browser.location.hash) {
                const hash = browser.location.hash.substring(1);
                if (this.props.modules.map((m) => m.key).includes(hash)) {
                    selectedTab = hash;
                } else {
                    const plop = this.props.anchors.find((a) => a.settingId === hash);
                    if (plop) {
                        selectedTab = plop.app;
                    }
                }
            }

            this.state.selectedTab = selectedTab;
        }

        this.settingsRef = useRef("settings");
        this.settingsTabRef = useRef("settings_tab");
        this.scrollMap = Object.create(null);
        
        useEffect(
            (settingsEl, currentTab) => {
                if (!settingsEl) {
                    return;
                }

                const { scrollTop } = this.scrollMap[currentTab] || 0;
                settingsEl.scrollTop = scrollTop;
            },
            () => [this.settingsRef.el, this.state.selectedTab]
        );
    }
}
```

**组件特性**:
- **页面级管理**: 作为设置页面的顶级管理组件
- **标签页导航**: 管理多个设置模块的标签页切换
- **滑动支持**: 支持移动端的滑动手势导航
- **状态保持**: 保持各标签页的滚动位置状态

## 核心属性

### 1. 组件属性

```javascript
// 模板配置
static template = "web.SettingsPage";

// 子组件
static components = { ActionSwiper };

// 属性定义
static props = {
    modules: Array,                             // 设置模块数组
    anchors: Array,                             // 锚点数组
    initialTab: { type: String, optional: 1 }, // 初始标签页
    slots: Object,                              // 插槽内容
};
```

**属性功能**:
- **modules**: 设置模块的配置数组
- **anchors**: 页面锚点的配置数组
- **initialTab**: 初始显示的标签页（可选）
- **slots**: 插槽内容对象

### 2. 状态属性

```javascript
// 响应式状态
this.state = useState({
    selectedTab: "",
    search: this.env.searchState,
});

// DOM引用
this.settingsRef = useRef("settings");
this.settingsTabRef = useRef("settings_tab");

// 滚动映射
this.scrollMap = Object.create(null);
```

**状态属性功能**:
- **selectedTab**: 当前选中的标签页
- **search**: 搜索状态的响应式引用
- **settingsRef**: 设置内容区域的DOM引用
- **settingsTabRef**: 标签页区域的DOM引用
- **scrollMap**: 各标签页滚动位置的映射

## 核心功能

### 1. 标签页初始化

```javascript
if (this.props.modules) {
    let selectedTab = this.props.initialTab || this.props.modules[0].key;

    if (browser.location.hash) {
        const hash = browser.location.hash.substring(1);
        if (this.props.modules.map((m) => m.key).includes(hash)) {
            selectedTab = hash;
        } else {
            const plop = this.props.anchors.find((a) => a.settingId === hash);
            if (plop) {
                selectedTab = plop.app;
            }
        }
    }

    this.state.selectedTab = selectedTab;
}
```

**初始化功能**:
- **默认选择**: 选择初始标签页或第一个模块
- **URL锚点**: 根据URL锚点确定初始标签页
- **模块匹配**: 匹配模块键值确定标签页
- **锚点查找**: 通过锚点配置查找对应的应用

### 2. 滚动位置管理

```javascript
useEffect(
    (settingsEl, currentTab) => {
        if (!settingsEl) {
            return;
        }

        const { scrollTop } = this.scrollMap[currentTab] || 0;
        settingsEl.scrollTop = scrollTop;
    },
    () => [this.settingsRef.el, this.state.selectedTab]
);
```

**滚动管理功能**:
- **位置恢复**: 切换标签页时恢复滚动位置
- **状态保存**: 保存各标签页的滚动状态
- **自动应用**: 自动应用保存的滚动位置
- **依赖追踪**: 追踪DOM元素和标签页变化

### 3. 滑动手势支持

```javascript
hasRightSwipe() {
    return (
        this.env.isSmall && this.state.search.value.length === 0 && this.getCurrentIndex() !== 0
    );
}

hasLeftSwipe() {
    return (
        this.env.isSmall &&
        this.state.search.value.length === 0 &&
        this.getCurrentIndex() !== this.props.modules.length - 1
    );
}

async onRightSwipe(prom) {
    this.state.selectedTab = this.props.modules[this.getCurrentIndex() - 1].key;
    await prom;
    this.scrollToSelectedTab();
}

async onLeftSwipe(prom) {
    this.state.selectedTab = this.props.modules[this.getCurrentIndex() + 1].key;
    await prom;
    this.scrollToSelectedTab();
}
```

**滑动功能**:
- **条件检查**: 检查是否支持滑动手势
- **方向判断**: 判断左右滑动的可用性
- **标签切换**: 根据滑动方向切换标签页
- **滚动同步**: 滑动后同步标签页滚动

### 4. 标签页操作

```javascript
getCurrentIndex() {
    return this.props.modules.findIndex((object) => {
        return object.key === this.state.selectedTab;
    });
}

scrollToSelectedTab() {
    const key = this.state.selectedTab;
    this.settingsTabRef.el
        .querySelector(`[data-key='${key}']`)
        .scrollIntoView({ behavior: "smooth", inline: "center", block: "nearest" });
}

onSettingTabClick(key) {
    if (this.settingsRef.el) {
        const { scrollTop } = this.settingsRef.el;
        this.scrollMap[this.state.selectedTab] = { scrollTop };
    }
    this.state.selectedTab = key;
    this.env.searchState.value = "";
}
```

**操作功能**:
- **索引获取**: 获取当前标签页的索引
- **滚动定位**: 滚动到选中的标签页
- **点击处理**: 处理标签页点击事件
- **状态保存**: 保存当前滚动位置
- **搜索清除**: 切换标签页时清除搜索

## 使用场景

### 1. 设置页面管理器

```javascript
// 设置页面管理器
class SettingsPageManager {
    constructor(env) {
        this.env = env;
        this.pages = new Map();
        this.currentPage = null;
        this.pageConfig = this.getPageConfig();
        this.setupManager();
    }
    
    setupManager() {
        // 设置页面类型
        this.pageTypes = {
            tabbed: {
                layout: 'tabs',
                navigation: 'horizontal',
                swipeEnabled: true
            },
            accordion: {
                layout: 'accordion',
                navigation: 'vertical',
                swipeEnabled: false
            },
            wizard: {
                layout: 'wizard',
                navigation: 'sequential',
                swipeEnabled: true
            },
            single: {
                layout: 'single',
                navigation: 'none',
                swipeEnabled: false
            }
        };
        
        this.setupEventListeners();
    }
    
    // 获取页面配置
    getPageConfig() {
        return {
            rememberScrollPosition: true,
            smoothScrolling: true,
            autoSaveState: true,
            swipeThreshold: 50,
            animationDuration: 300
        };
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听标签页切换
        this.env.bus.addEventListener('TAB_CHANGED', (event) => {
            this.handleTabChange(event.detail);
        });
        
        // 监听页面滚动
        this.env.bus.addEventListener('PAGE_SCROLLED', (event) => {
            this.handlePageScroll(event.detail);
        });
        
        // 监听URL变化
        window.addEventListener('hashchange', () => {
            this.handleHashChange();
        });
        
        // 监听键盘导航
        document.addEventListener('keydown', (event) => {
            this.handleKeyboardNavigation(event);
        });
    }
    
    // 创建设置页面
    createSettingsPage(pageId, pageData, pageType = 'tabbed') {
        const typeConfig = this.pageTypes[pageType] || this.pageTypes.tabbed;
        
        const page = {
            Component: SettingsPage,
            props: {
                modules: pageData.modules || [],
                anchors: pageData.anchors || [],
                initialTab: pageData.initialTab,
                slots: pageData.slots || {}
            },
            id: pageId,
            type: pageType,
            layout: typeConfig.layout,
            navigation: typeConfig.navigation,
            swipeEnabled: typeConfig.swipeEnabled,
            currentTab: pageData.initialTab || (pageData.modules[0] && pageData.modules[0].key),
            scrollPositions: new Map(),
            tabHistory: [],
            metadata: {
                title: pageData.title || 'Settings',
                description: pageData.description || '',
                category: pageData.category || 'general'
            },
            created: Date.now()
        };
        
        this.pages.set(pageId, page);
        
        return page;
    }
    
    // 处理标签页切换
    handleTabChange(changeData) {
        const page = this.pages.get(changeData.pageId);
        if (!page) return;
        
        // 保存当前滚动位置
        if (page.currentTab) {
            this.saveScrollPosition(changeData.pageId, page.currentTab, changeData.scrollTop);
        }
        
        // 更新当前标签页
        page.currentTab = changeData.newTab;
        
        // 记录标签页历史
        this.recordTabHistory(changeData.pageId, changeData.newTab);
        
        // 恢复滚动位置
        this.restoreScrollPosition(changeData.pageId, changeData.newTab);
        
        // 更新URL
        this.updateURL(changeData.newTab);
        
        // 清除搜索
        if (this.env.searchState) {
            this.env.searchState.value = '';
        }
    }
    
    // 保存滚动位置
    saveScrollPosition(pageId, tabKey, scrollTop) {
        const page = this.pages.get(pageId);
        if (!page || !this.pageConfig.rememberScrollPosition) return;
        
        page.scrollPositions.set(tabKey, {
            scrollTop: scrollTop,
            timestamp: Date.now()
        });
        
        // 持久化到本地存储
        if (this.pageConfig.autoSaveState) {
            this.savePageState(pageId);
        }
    }
    
    // 恢复滚动位置
    restoreScrollPosition(pageId, tabKey) {
        const page = this.pages.get(pageId);
        if (!page || !this.pageConfig.rememberScrollPosition) return;
        
        const scrollData = page.scrollPositions.get(tabKey);
        if (scrollData && page.Component && page.Component.settingsRef?.el) {
            const element = page.Component.settingsRef.el;
            
            if (this.pageConfig.smoothScrolling) {
                element.scrollTo({
                    top: scrollData.scrollTop,
                    behavior: 'smooth'
                });
            } else {
                element.scrollTop = scrollData.scrollTop;
            }
        }
    }
    
    // 记录标签页历史
    recordTabHistory(pageId, tabKey) {
        const page = this.pages.get(pageId);
        if (!page) return;
        
        // 移除重复项
        const existingIndex = page.tabHistory.indexOf(tabKey);
        if (existingIndex > -1) {
            page.tabHistory.splice(existingIndex, 1);
        }
        
        // 添加到历史开头
        page.tabHistory.unshift(tabKey);
        
        // 限制历史长度
        page.tabHistory = page.tabHistory.slice(0, 10);
    }
    
    // 处理页面滚动
    handlePageScroll(scrollData) {
        const page = this.pages.get(scrollData.pageId);
        if (!page || !page.currentTab) return;
        
        // 实时更新滚动位置
        this.saveScrollPosition(scrollData.pageId, page.currentTab, scrollData.scrollTop);
    }
    
    // 处理URL锚点变化
    handleHashChange() {
        const hash = window.location.hash.substring(1);
        if (!hash) return;
        
        // 查找包含该锚点的页面
        for (const [pageId, page] of this.pages) {
            const module = page.props.modules.find(m => m.key === hash);
            if (module) {
                this.switchToTab(pageId, hash);
                return;
            }
            
            const anchor = page.props.anchors.find(a => a.settingId === hash);
            if (anchor) {
                this.switchToTab(pageId, anchor.app);
                this.scrollToAnchor(pageId, hash);
                return;
            }
        }
    }
    
    // 切换到标签页
    switchToTab(pageId, tabKey) {
        const page = this.pages.get(pageId);
        if (!page) return false;
        
        if (page.Component && page.Component.state) {
            page.Component.state.selectedTab = tabKey;
        }
        
        this.handleTabChange({
            pageId: pageId,
            newTab: tabKey,
            oldTab: page.currentTab,
            scrollTop: 0
        });
        
        return true;
    }
    
    // 滚动到锚点
    scrollToAnchor(pageId, anchorId) {
        const page = this.pages.get(pageId);
        if (!page || !page.Component) return;
        
        setTimeout(() => {
            const element = document.getElementById(anchorId);
            if (element) {
                element.scrollIntoView({
                    behavior: this.pageConfig.smoothScrolling ? 'smooth' : 'auto',
                    block: 'start'
                });
                
                // 高亮锚点元素
                this.highlightAnchor(element);
            }
        }, 100);
    }
    
    // 高亮锚点元素
    highlightAnchor(element) {
        element.classList.add('o_setting_highlight');
        
        setTimeout(() => {
            element.classList.remove('o_setting_highlight');
        }, 3000);
    }
    
    // 处理键盘导航
    handleKeyboardNavigation(event) {
        if (!this.currentPage) return;
        
        const page = this.pages.get(this.currentPage);
        if (!page) return;
        
        // Ctrl/Cmd + Tab: 下一个标签页
        if ((event.ctrlKey || event.metaKey) && event.key === 'Tab') {
            event.preventDefault();
            this.navigateToNextTab(this.currentPage, !event.shiftKey);
        }
        
        // Ctrl/Cmd + 数字: 直接跳转到标签页
        if ((event.ctrlKey || event.metaKey) && /^[1-9]$/.test(event.key)) {
            event.preventDefault();
            const tabIndex = parseInt(event.key) - 1;
            if (tabIndex < page.props.modules.length) {
                this.switchToTab(this.currentPage, page.props.modules[tabIndex].key);
            }
        }
    }
    
    // 导航到下一个/上一个标签页
    navigateToNextTab(pageId, forward = true) {
        const page = this.pages.get(pageId);
        if (!page) return;
        
        const modules = page.props.modules;
        const currentIndex = modules.findIndex(m => m.key === page.currentTab);
        
        let nextIndex;
        if (forward) {
            nextIndex = (currentIndex + 1) % modules.length;
        } else {
            nextIndex = (currentIndex - 1 + modules.length) % modules.length;
        }
        
        this.switchToTab(pageId, modules[nextIndex].key);
    }
    
    // 获取页面统计
    getPageStatistics(pageId) {
        const page = this.pages.get(pageId);
        if (!page) return null;
        
        return {
            id: pageId,
            type: page.type,
            layout: page.layout,
            totalTabs: page.props.modules.length,
            currentTab: page.currentTab,
            tabHistory: page.tabHistory.length,
            scrollPositions: page.scrollPositions.size,
            anchors: page.props.anchors.length,
            created: page.created
        };
    }
    
    // 保存页面状态
    savePageState(pageId) {
        const page = this.pages.get(pageId);
        if (!page) return;
        
        try {
            const state = {
                currentTab: page.currentTab,
                scrollPositions: Array.from(page.scrollPositions.entries()),
                tabHistory: page.tabHistory,
                timestamp: Date.now()
            };
            
            localStorage.setItem(`odoo_page_state_${pageId}`, JSON.stringify(state));
        } catch (error) {
            console.warn('Failed to save page state:', error);
        }
    }
    
    // 加载页面状态
    loadPageState(pageId) {
        try {
            const stateData = localStorage.getItem(`odoo_page_state_${pageId}`);
            if (!stateData) return null;
            
            const state = JSON.parse(stateData);
            
            const page = this.pages.get(pageId);
            if (page) {
                page.currentTab = state.currentTab;
                page.scrollPositions = new Map(state.scrollPositions);
                page.tabHistory = state.tabHistory || [];
            }
            
            return state;
        } catch (error) {
            console.warn('Failed to load page state:', error);
            return null;
        }
    }
    
    // 更新URL
    updateURL(tabKey) {
        if (tabKey) {
            window.location.hash = tabKey;
        }
    }
    
    // 设置当前页面
    setCurrentPage(pageId) {
        this.currentPage = pageId;
    }
    
    // 清理页面
    removePage(pageId) {
        const page = this.pages.get(pageId);
        if (!page) return false;
        
        // 清理状态
        if (this.pageConfig.autoSaveState) {
            localStorage.removeItem(`odoo_page_state_${pageId}`);
        }
        
        // 移除页面
        this.pages.delete(pageId);
        
        // 更新当前页面
        if (this.currentPage === pageId) {
            this.currentPage = null;
        }
        
        return true;
    }
}

// 使用示例
const pageManager = new SettingsPageManager(env);

// 创建设置页面
const mainPage = pageManager.createSettingsPage('main_settings', {
    modules: [
        { key: 'general', name: 'General' },
        { key: 'users', name: 'Users' },
        { key: 'email', name: 'Email' }
    ],
    anchors: [
        { settingId: 'company_name', app: 'general' },
        { settingId: 'user_permissions', app: 'users' }
    ],
    initialTab: 'general',
    title: 'Main Settings'
});

// 设置当前页面
pageManager.setCurrentPage('main_settings');

// 切换标签页
pageManager.switchToTab('main_settings', 'users');

// 获取统计信息
const stats = pageManager.getPageStatistics('main_settings');
console.log('Page statistics:', stats);
```

## 技术特点

### 1. 标签页管理
- **动态切换**: 支持动态的标签页切换
- **状态保持**: 保持各标签页的状态
- **URL同步**: 与URL锚点同步
- **历史记录**: 记录标签页访问历史

### 2. 滚动管理
- **位置记忆**: 记忆各标签页的滚动位置
- **自动恢复**: 切换时自动恢复滚动位置
- **平滑滚动**: 支持平滑滚动效果
- **实时保存**: 实时保存滚动状态

### 3. 移动端支持
- **滑动手势**: 支持左右滑动切换标签页
- **响应式**: 根据屏幕尺寸调整行为
- **触摸优化**: 优化触摸交互体验
- **手势识别**: 智能的手势识别

### 4. URL集成
- **锚点处理**: 处理URL锚点导航
- **状态同步**: 与浏览器历史同步
- **深度链接**: 支持深度链接访问
- **SEO友好**: SEO友好的URL结构

## 设计模式

### 1. 组件模式 (Component Pattern)
- **页面组件**: 作为页面级的容器组件
- **组合组件**: 组合多个子组件
- **属性驱动**: 通过属性配置页面行为

### 2. 状态模式 (State Pattern)
- **标签状态**: 管理不同标签页的状态
- **滚动状态**: 管理滚动位置状态
- **搜索状态**: 集成搜索状态管理

### 3. 观察者模式 (Observer Pattern)
- **状态监听**: 监听状态变化
- **事件响应**: 响应用户交互事件
- **自动更新**: 状态变化时自动更新

### 4. 策略模式 (Strategy Pattern)
- **导航策略**: 不同的导航处理策略
- **滚动策略**: 不同的滚动处理策略
- **布局策略**: 不同的页面布局策略

## 注意事项

1. **性能考虑**: 避免频繁的DOM操作和状态更新
2. **内存管理**: 及时清理滚动位置映射
3. **用户体验**: 提供流畅的标签页切换体验
4. **兼容性**: 确保在不同设备上的兼容性

## 扩展建议

1. **动画效果**: 添加标签页切换的动画效果
2. **拖拽排序**: 支持标签页的拖拽排序
3. **标签页关闭**: 支持动态关闭标签页
4. **快捷键**: 添加更多的键盘快捷键
5. **主题定制**: 支持标签页样式的主题定制

该设置页面组件为Odoo Web客户端的设置表单提供了完整的页面级管理和导航功能，通过智能的标签页管理和滚动状态保持确保了良好的用户体验和交互流畅性。

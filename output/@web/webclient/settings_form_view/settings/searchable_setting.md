# SearchableSetting - 可搜索设置组件

## 概述

`searchable_setting.js` 是 Odoo Web 客户端的可搜索设置组件，提供了设置项的搜索和高亮显示功能。该模块包含64行代码，继承自标准设置组件，专门用于在设置表单中实现设置项的搜索过滤和高亮显示，具备文本搜索、可见性控制、高亮文本集成、URL锚点支持等特性，是Odoo Web设置表单视图中搜索功能的核心组件。

## 文件信息
- **路径**: `/web/static/src/webclient/settings_form_view/settings/searchable_setting.js`
- **行数**: 64
- **模块**: `@web/webclient/settings_form_view/settings/searchable_setting`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/strings'                                                       // 字符串工具
'@web/views/form/setting/setting'                                              // 设置组件基类
'@odoo/owl'                                                                     // OWL框架
'@web/webclient/settings_form_view/highlight_text/form_label_highlight_text'    // 表单标签高亮文本
'@web/webclient/settings_form_view/highlight_text/highlight_text'               // 高亮文本组件
'@web/core/browser/browser'                                                     // 浏览器服务
```

## 主组件定义

### 1. SearchableSetting 组件

```javascript
class SearchableSetting extends Setting {
    static template = "web.SearchableSetting";
    static components = {
        ...Setting.components,
        FormLabel: FormLabelHighlightText,
        HighlightText,
    };
    
    setup() {
        this.settingRef = useRef("setting");
        this.state = useState({
            search: this.env.searchState,
            showAllContainer: this.env.showAllContainer,
            highlightClass: {},
        });
        this.labels = [];
        this.labels.push(this.labelString, this.props.help);
        super.setup();
        
        onMounted(() => {
            // 收集可搜索文本
            if (this.settingRef.el) {
                const searchableTexts = this.settingRef.el.querySelectorAll("span[searchableText]");
                searchableTexts.forEach((st) => {
                    this.labels.push(st.getAttribute("searchableText"));
                });
            }
            
            // 处理URL锚点高亮
            if (browser.location.hash.substring(1) === this.props.id) {
                this.state.highlightClass = { o_setting_highlight: true };
                setTimeout(() => (this.state.highlightClass = {}), 5000);
            }
        });
    }
}
```

**组件特性**:
- **继承设置组件**: 继承标准设置组件的所有功能
- **搜索集成**: 集成搜索状态和高亮文本组件
- **可见性控制**: 根据搜索条件控制组件可见性
- **URL锚点**: 支持URL锚点定位和高亮

## 核心属性

### 1. 组件属性

```javascript
// 模板配置
static template = "web.SearchableSetting";

// 组件集合
static components = {
    ...Setting.components,
    FormLabel: FormLabelHighlightText,
    HighlightText,
};
```

**属性功能**:
- **template**: 指定可搜索设置的模板
- **components**: 扩展父类组件并添加高亮组件

### 2. 状态属性

```javascript
// 组件引用
this.settingRef = useRef("setting");

// 响应式状态
this.state = useState({
    search: this.env.searchState,
    showAllContainer: this.env.showAllContainer,
    highlightClass: {},
});

// 标签数组
this.labels = [];
```

**状态属性功能**:
- **settingRef**: 设置组件的DOM引用
- **search**: 搜索状态的响应式引用
- **showAllContainer**: 显示所有容器的状态
- **highlightClass**: 高亮CSS类的状态
- **labels**: 可搜索的文本标签数组

## 核心功能

### 1. 文本收集

```javascript
// 初始标签收集
this.labels.push(this.labelString, this.props.help);

// 动态文本收集
onMounted(() => {
    if (this.settingRef.el) {
        const searchableTexts = this.settingRef.el.querySelectorAll("span[searchableText]");
        searchableTexts.forEach((st) => {
            this.labels.push(st.getAttribute("searchableText"));
        });
    }
});
```

**收集功能**:
- **静态收集**: 收集标签字符串和帮助文本
- **动态收集**: 挂载后收集DOM中的可搜索文本
- **属性查询**: 查询带有searchableText属性的元素
- **文本提取**: 提取属性值作为搜索文本

### 2. 可见性控制

```javascript
visible() {
    if (!this.state.search.value) {
        return true;
    }
    if (this.state.showAllContainer.showAllContainer) {
        return true;
    }
    const regexp = new RegExp(escapeRegExp(this.state.search.value), "i");
    if (regexp.test(this.labels.join())) {
        return true;
    }
    return false;
}
```

**可见性逻辑**:
- **无搜索**: 没有搜索值时显示所有设置
- **显示全部**: showAllContainer为true时显示所有
- **正则匹配**: 使用正则表达式匹配搜索文本
- **文本连接**: 将所有标签连接后进行匹配

### 3. CSS类名管理

```javascript
get classNames() {
    const classNames = super.classNames;
    classNames.o_searchable_setting = Boolean(this.labels.length);
    return { ...classNames, ...this.state.highlightClass };
}
```

**类名管理功能**:
- **基类继承**: 继承父类的CSS类名
- **搜索标识**: 根据标签数量添加可搜索标识
- **高亮合并**: 合并高亮CSS类
- **动态更新**: 动态更新组件的CSS类

### 4. URL锚点处理

```javascript
if (browser.location.hash.substring(1) === this.props.id) {
    this.state.highlightClass = { o_setting_highlight: true };
    setTimeout(() => (this.state.highlightClass = {}), 5000);
}
```

**锚点处理功能**:
- **URL检查**: 检查URL锚点是否匹配组件ID
- **高亮设置**: 设置高亮CSS类
- **自动清除**: 5秒后自动清除高亮效果
- **用户体验**: 提供URL直接定位功能

## 使用场景

### 1. 设置搜索管理器

```javascript
// 设置搜索管理器
class SettingsSearchManager {
    constructor(env) {
        this.env = env;
        this.searchState = env.searchState;
        this.searchableSettings = new Map();
        this.searchHistory = [];
        this.searchConfig = this.getSearchConfig();
        this.setupManager();
    }
    
    setupManager() {
        // 设置搜索配置
        this.searchOptions = {
            minSearchLength: 2,
            searchDelay: 300,
            maxResults: 50,
            caseSensitive: false,
            wholeWord: false,
            fuzzySearch: false
        };
        
        // 设置搜索类型
        this.searchTypes = {
            label: { weight: 3, enabled: true },
            help: { weight: 2, enabled: true },
            description: { weight: 2, enabled: true },
            keywords: { weight: 1, enabled: true },
            content: { weight: 1, enabled: false }
        };
        
        this.setupEventListeners();
    }
    
    // 获取搜索配置
    getSearchConfig() {
        return {
            highlightDuration: 5000,
            scrollBehavior: 'smooth',
            focusOnResult: true,
            showSearchStats: true
        };
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听搜索状态变化
        this.env.bus.addEventListener('SEARCH_STATE_CHANGED', (event) => {
            this.handleSearchChange(event.detail);
        });
        
        // 监听URL变化
        window.addEventListener('hashchange', () => {
            this.handleHashChange();
        });
        
        // 监听键盘快捷键
        document.addEventListener('keydown', (event) => {
            this.handleKeyboardShortcuts(event);
        });
    }
    
    // 注册可搜索设置
    registerSearchableSetting(settingId, settingComponent) {
        const searchableData = this.extractSearchableData(settingComponent);
        
        this.searchableSettings.set(settingId, {
            id: settingId,
            component: settingComponent,
            searchableData: searchableData,
            visible: true,
            highlighted: false,
            score: 0
        });
        
        return searchableData;
    }
    
    // 提取可搜索数据
    extractSearchableData(settingComponent) {
        const data = {
            labels: [],
            keywords: [],
            content: [],
            metadata: {}
        };
        
        // 提取标签文本
        if (settingComponent.labelString) {
            data.labels.push(settingComponent.labelString);
        }
        
        // 提取帮助文本
        if (settingComponent.props.help) {
            data.labels.push(settingComponent.props.help);
        }
        
        // 提取DOM中的可搜索文本
        if (settingComponent.settingRef?.el) {
            const searchableTexts = settingComponent.settingRef.el.querySelectorAll('[searchableText]');
            searchableTexts.forEach(element => {
                const text = element.getAttribute('searchableText');
                if (text) {
                    data.content.push(text);
                }
            });
        }
        
        // 提取关键词
        data.keywords = this.extractKeywords(data.labels.concat(data.content));
        
        // 提取元数据
        data.metadata = {
            category: settingComponent.props.category || 'general',
            module: settingComponent.props.module || 'base',
            priority: settingComponent.props.priority || 0
        };
        
        return data;
    }
    
    // 提取关键词
    extractKeywords(texts) {
        const keywords = new Set();
        const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']);
        
        texts.forEach(text => {
            if (typeof text === 'string') {
                const words = text.toLowerCase()
                    .replace(/[^\w\s]/g, ' ')
                    .split(/\s+/)
                    .filter(word => word.length > 2 && !stopWords.has(word));
                
                words.forEach(word => keywords.add(word));
            }
        });
        
        return Array.from(keywords);
    }
    
    // 执行搜索
    performSearch(searchValue) {
        if (!searchValue || searchValue.length < this.searchOptions.minSearchLength) {
            this.clearSearch();
            return;
        }
        
        // 记录搜索历史
        this.recordSearchHistory(searchValue);
        
        // 计算搜索结果
        const results = this.calculateSearchResults(searchValue);
        
        // 更新设置可见性
        this.updateSettingsVisibility(results);
        
        // 触发搜索完成事件
        this.env.bus.trigger('SEARCH_COMPLETED', {
            query: searchValue,
            results: results,
            total: results.length
        });
        
        return results;
    }
    
    // 计算搜索结果
    calculateSearchResults(searchValue) {
        const results = [];
        const searchRegex = new RegExp(escapeRegExp(searchValue), 'i');
        
        for (const [settingId, setting] of this.searchableSettings) {
            const score = this.calculateSearchScore(searchValue, setting.searchableData);
            
            if (score > 0) {
                results.push({
                    id: settingId,
                    setting: setting,
                    score: score,
                    matches: this.findMatches(searchValue, setting.searchableData)
                });
            }
        }
        
        // 按分数排序
        results.sort((a, b) => b.score - a.score);
        
        // 限制结果数量
        return results.slice(0, this.searchOptions.maxResults);
    }
    
    // 计算搜索分数
    calculateSearchScore(searchValue, searchableData) {
        let score = 0;
        const searchLower = searchValue.toLowerCase();
        
        // 检查标签匹配
        searchableData.labels.forEach(label => {
            if (typeof label === 'string') {
                const labelLower = label.toLowerCase();
                if (labelLower.includes(searchLower)) {
                    score += this.searchTypes.label.weight;
                    
                    // 精确匹配加分
                    if (labelLower === searchLower) {
                        score += 2;
                    }
                    
                    // 开头匹配加分
                    if (labelLower.startsWith(searchLower)) {
                        score += 1;
                    }
                }
            }
        });
        
        // 检查内容匹配
        searchableData.content.forEach(content => {
            if (typeof content === 'string' && content.toLowerCase().includes(searchLower)) {
                score += this.searchTypes.content.weight;
            }
        });
        
        // 检查关键词匹配
        searchableData.keywords.forEach(keyword => {
            if (keyword.includes(searchLower)) {
                score += this.searchTypes.keywords.weight;
            }
        });
        
        return score;
    }
    
    // 查找匹配项
    findMatches(searchValue, searchableData) {
        const matches = [];
        const searchLower = searchValue.toLowerCase();
        
        // 查找标签匹配
        searchableData.labels.forEach((label, index) => {
            if (typeof label === 'string' && label.toLowerCase().includes(searchLower)) {
                matches.push({
                    type: 'label',
                    index: index,
                    text: label,
                    position: label.toLowerCase().indexOf(searchLower)
                });
            }
        });
        
        // 查找内容匹配
        searchableData.content.forEach((content, index) => {
            if (typeof content === 'string' && content.toLowerCase().includes(searchLower)) {
                matches.push({
                    type: 'content',
                    index: index,
                    text: content,
                    position: content.toLowerCase().indexOf(searchLower)
                });
            }
        });
        
        return matches;
    }
    
    // 更新设置可见性
    updateSettingsVisibility(results) {
        // 隐藏所有设置
        for (const [settingId, setting] of this.searchableSettings) {
            setting.visible = false;
            setting.highlighted = false;
            setting.score = 0;
        }
        
        // 显示匹配的设置
        results.forEach(result => {
            const setting = this.searchableSettings.get(result.id);
            if (setting) {
                setting.visible = true;
                setting.highlighted = true;
                setting.score = result.score;
            }
        });
    }
    
    // 清除搜索
    clearSearch() {
        // 显示所有设置
        for (const [settingId, setting] of this.searchableSettings) {
            setting.visible = true;
            setting.highlighted = false;
            setting.score = 0;
        }
        
        // 触发搜索清除事件
        this.env.bus.trigger('SEARCH_CLEARED');
    }
    
    // 处理搜索变化
    handleSearchChange(searchData) {
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
        
        this.searchTimeout = setTimeout(() => {
            this.performSearch(searchData.value);
        }, this.searchOptions.searchDelay);
    }
    
    // 处理URL锚点变化
    handleHashChange() {
        const hash = window.location.hash.substring(1);
        if (hash) {
            this.highlightSetting(hash);
        }
    }
    
    // 高亮设置
    highlightSetting(settingId) {
        const setting = this.searchableSettings.get(settingId);
        if (setting && setting.component) {
            // 设置高亮
            setting.component.state.highlightClass = { o_setting_highlight: true };
            
            // 滚动到设置
            if (setting.component.settingRef?.el) {
                setting.component.settingRef.el.scrollIntoView({
                    behavior: this.searchConfig.scrollBehavior,
                    block: 'center'
                });
            }
            
            // 自动清除高亮
            setTimeout(() => {
                if (setting.component.state) {
                    setting.component.state.highlightClass = {};
                }
            }, this.searchConfig.highlightDuration);
        }
    }
    
    // 处理键盘快捷键
    handleKeyboardShortcuts(event) {
        // Ctrl+F 或 Cmd+F 聚焦搜索框
        if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
            event.preventDefault();
            this.focusSearchBox();
        }
        
        // Escape 清除搜索
        if (event.key === 'Escape') {
            this.clearSearch();
        }
    }
    
    // 聚焦搜索框
    focusSearchBox() {
        const searchBox = document.querySelector('.o_settings_search input');
        if (searchBox) {
            searchBox.focus();
            searchBox.select();
        }
    }
    
    // 记录搜索历史
    recordSearchHistory(searchValue) {
        if (searchValue.length < 2) return;
        
        // 移除重复项
        const index = this.searchHistory.indexOf(searchValue);
        if (index > -1) {
            this.searchHistory.splice(index, 1);
        }
        
        // 添加到开头
        this.searchHistory.unshift(searchValue);
        
        // 限制历史长度
        this.searchHistory = this.searchHistory.slice(0, 20);
    }
    
    // 获取搜索建议
    getSearchSuggestions(partialValue) {
        if (!partialValue || partialValue.length < 1) {
            return this.searchHistory.slice(0, 5);
        }
        
        const suggestions = [];
        
        // 从历史中查找
        this.searchHistory.forEach(item => {
            if (item.toLowerCase().includes(partialValue.toLowerCase())) {
                suggestions.push(item);
            }
        });
        
        // 从关键词中查找
        for (const setting of this.searchableSettings.values()) {
            setting.searchableData.keywords.forEach(keyword => {
                if (keyword.toLowerCase().includes(partialValue.toLowerCase()) && 
                    !suggestions.includes(keyword)) {
                    suggestions.push(keyword);
                }
            });
        }
        
        return suggestions.slice(0, 10);
    }
    
    // 获取搜索统计
    getSearchStatistics() {
        const visible = Array.from(this.searchableSettings.values()).filter(s => s.visible).length;
        const highlighted = Array.from(this.searchableSettings.values()).filter(s => s.highlighted).length;
        
        return {
            totalSettings: this.searchableSettings.size,
            visibleSettings: visible,
            highlightedSettings: highlighted,
            searchHistorySize: this.searchHistory.length,
            currentSearch: this.searchState?.value || ''
        };
    }
}

// 使用示例
const searchManager = new SettingsSearchManager(env);

// 注册可搜索设置
searchManager.registerSearchableSetting('setting_1', settingComponent);

// 执行搜索
const results = searchManager.performSearch('email');

// 高亮特定设置
searchManager.highlightSetting('setting_1');

// 获取搜索统计
const stats = searchManager.getSearchStatistics();
console.log('Search statistics:', stats);
```

## 技术特点

### 1. 继承扩展
- **基类继承**: 继承标准设置组件的所有功能
- **组件扩展**: 扩展高亮文本相关组件
- **功能增强**: 在原有功能基础上增加搜索能力
- **接口保持**: 保持与基类相同的接口

### 2. 搜索集成
- **状态响应**: 响应式的搜索状态管理
- **文本收集**: 自动收集可搜索的文本内容
- **正则匹配**: 使用正则表达式进行文本匹配
- **可见性控制**: 根据搜索结果控制组件可见性

### 3. 高亮功能
- **文本高亮**: 集成高亮文本组件
- **URL锚点**: 支持URL锚点定位高亮
- **自动清除**: 自动清除高亮效果
- **视觉反馈**: 提供清晰的视觉反馈

### 4. DOM操作
- **元素查询**: 查询DOM中的可搜索元素
- **属性提取**: 提取元素的搜索文本属性
- **动态收集**: 组件挂载后动态收集文本
- **引用管理**: 使用useRef管理DOM引用

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **功能继承**: 继承基类的所有功能
- **行为扩展**: 扩展搜索相关的行为
- **接口保持**: 保持与基类相同的接口

### 2. 观察者模式 (Observer Pattern)
- **状态监听**: 监听搜索状态的变化
- **自动响应**: 状态变化时自动更新显示
- **事件驱动**: 基于事件的更新机制

### 3. 策略模式 (Strategy Pattern)
- **搜索策略**: 不同的文本搜索策略
- **可见性策略**: 不同的可见性控制策略
- **高亮策略**: 不同的高亮显示策略

### 4. 组合模式 (Composition Pattern)
- **组件组合**: 组合多个高亮文本组件
- **功能集成**: 集成搜索和高亮功能
- **层次结构**: 构建清晰的组件层次

## 注意事项

1. **性能考虑**: 避免频繁的DOM查询和文本处理
2. **内存管理**: 及时清理事件监听器和定时器
3. **搜索准确性**: 确保搜索结果的准确性和相关性
4. **用户体验**: 提供流畅的搜索和高亮体验

## 扩展建议

1. **搜索算法**: 支持更高级的搜索算法（如模糊搜索）
2. **搜索历史**: 记录和显示用户搜索历史
3. **搜索建议**: 提供智能的搜索建议
4. **键盘导航**: 支持键盘导航搜索结果
5. **搜索统计**: 显示搜索结果的统计信息

该可搜索设置组件为Odoo Web客户端的设置表单提供了强大的搜索和高亮功能，通过智能的文本收集和正则匹配确保了高效的搜索体验和准确的结果显示。

# SettingsApp - 设置应用组件

## 概述

`settings_app.js` 是 Odoo Web 客户端的设置应用组件，提供了设置应用的容器和可见性控制功能。该模块包含40行代码，是一个OWL组件，专门用于管理设置应用的显示状态和搜索响应，具备搜索状态集成、可见性控制、DOM引用管理、副作用处理等特性，是Odoo Web设置表单视图中应用级别管理的核心组件。

## 文件信息
- **路径**: `/web/static/src/webclient/settings_form_view/settings/settings_app.js`
- **行数**: 40
- **模块**: `@web/webclient/settings_form_view/settings/settings_app`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                     // OWL框架
```

## 主组件定义

### 1. SettingsApp 组件

```javascript
class SettingsApp extends Component {
    static template = "web.SettingsApp";
    static props = {
        string: String,
        imgurl: String,
        key: String,
        selectedTab: { type: String, optional: 1 },
        slots: Object,
    };
    
    setup() {
        this.state = useState({
            search: this.env.searchState,
        });
        this.settingsAppRef = useRef("settingsApp");
        
        useEffect(
            () => {
                if (this.settingsAppRef.el) {
                    const force =
                        this.state.search.value &&
                        !this.settingsAppRef.el.querySelector(
                            ".o_settings_container:not(.d-none)"
                        ) &&
                        !this.settingsAppRef.el.querySelector(
                            ".o_setting_box.o_searchable_setting"
                        );
                    this.settingsAppRef.el.classList.toggle("d-none", force);
                }
            },
            () => [this.state.search.value]
        );
    }
}
```

**组件特性**:
- **应用容器**: 作为设置应用的顶级容器
- **搜索响应**: 响应搜索状态变化
- **可见性控制**: 智能控制应用的可见性
- **DOM管理**: 管理DOM元素的引用和操作

## 核心属性

### 1. 组件属性

```javascript
// 模板配置
static template = "web.SettingsApp";

// 属性定义
static props = {
    string: String,                             // 应用名称
    imgurl: String,                             // 应用图标URL
    key: String,                                // 应用唯一键
    selectedTab: { type: String, optional: 1 }, // 选中的标签页
    slots: Object,                              // 插槽内容
};
```

**属性功能**:
- **string**: 应用的显示名称
- **imgurl**: 应用图标的URL地址
- **key**: 应用的唯一标识键
- **selectedTab**: 当前选中的标签页（可选）
- **slots**: 插槽内容对象

### 2. 状态属性

```javascript
// 响应式状态
this.state = useState({
    search: this.env.searchState,
});

// DOM引用
this.settingsAppRef = useRef("settingsApp");
```

**状态属性功能**:
- **search**: 搜索状态的响应式引用
- **settingsAppRef**: 设置应用DOM元素的引用

## 核心功能

### 1. 搜索状态集成

```javascript
this.state = useState({
    search: this.env.searchState,
});
```

**集成功能**:
- **状态绑定**: 绑定环境中的搜索状态
- **响应式**: 使用useState创建响应式状态
- **自动更新**: 搜索状态变化时自动更新
- **环境集成**: 与环境中的搜索状态集成

### 2. 可见性控制

```javascript
useEffect(
    () => {
        if (this.settingsAppRef.el) {
            const force =
                this.state.search.value &&
                !this.settingsAppRef.el.querySelector(
                    ".o_settings_container:not(.d-none)"
                ) &&
                !this.settingsAppRef.el.querySelector(
                    ".o_setting_box.o_searchable_setting"
                );
            this.settingsAppRef.el.classList.toggle("d-none", force);
        }
    },
    () => [this.state.search.value]
);
```

**控制逻辑**:
- **条件检查**: 检查搜索值和可见元素
- **DOM查询**: 查询设置容器和可搜索设置
- **强制隐藏**: 在特定条件下强制隐藏应用
- **CSS切换**: 使用CSS类控制可见性

### 3. DOM引用管理

```javascript
this.settingsAppRef = useRef("settingsApp");
```

**引用管理功能**:
- **元素引用**: 引用设置应用的DOM元素
- **安全访问**: 安全访问DOM元素
- **操作基础**: 为DOM操作提供基础
- **生命周期**: 与组件生命周期绑定

## 使用场景

### 1. 设置应用管理器

```javascript
// 设置应用管理器
class SettingsAppManager {
    constructor(env) {
        this.env = env;
        this.apps = new Map();
        this.activeApp = null;
        this.appConfig = this.getAppConfig();
        this.setupManager();
    }
    
    setupManager() {
        // 设置应用类型
        this.appTypes = {
            core: {
                category: 'Core',
                priority: 1,
                icon: 'fa-cog',
                color: '#875A7B'
            },
            business: {
                category: 'Business',
                priority: 2,
                icon: 'fa-briefcase',
                color: '#2E7D32'
            },
            technical: {
                category: 'Technical',
                priority: 3,
                icon: 'fa-code',
                color: '#1976D2'
            },
            integration: {
                category: 'Integration',
                priority: 4,
                icon: 'fa-plug',
                color: '#F57C00'
            }
        };
        
        this.setupEventListeners();
    }
    
    // 获取应用配置
    getAppConfig() {
        return {
            autoHide: true,
            searchThreshold: 2,
            animationDuration: 300,
            lazyLoad: true,
            cacheResults: true
        };
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听搜索状态变化
        this.env.bus.addEventListener('SEARCH_STATE_CHANGED', (event) => {
            this.handleSearchChange(event.detail);
        });
        
        // 监听应用切换
        this.env.bus.addEventListener('APP_SWITCHED', (event) => {
            this.handleAppSwitch(event.detail);
        });
        
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            this.handleWindowResize();
        });
    }
    
    // 注册设置应用
    registerSettingsApp(appKey, appData) {
        const appType = this.determineAppType(appData);
        
        const app = {
            Component: SettingsApp,
            props: {
                string: appData.string || appData.name,
                imgurl: appData.imgurl || this.getDefaultIcon(appType),
                key: appKey,
                selectedTab: appData.selectedTab,
                slots: appData.slots || {}
            },
            key: appKey,
            type: appType,
            category: this.appTypes[appType].category,
            priority: this.appTypes[appType].priority,
            visible: true,
            active: false,
            searchScore: 0,
            settings: new Map(),
            metadata: {
                module: appData.module || 'base',
                version: appData.version || '1.0',
                dependencies: appData.dependencies || [],
                description: appData.description || ''
            },
            created: Date.now()
        };
        
        this.apps.set(appKey, app);
        
        return app;
    }
    
    // 确定应用类型
    determineAppType(appData) {
        if (appData.type) {
            return appData.type;
        }
        
        // 根据模块名称推断类型
        const module = appData.module || '';
        
        if (['base', 'web', 'mail'].includes(module)) {
            return 'core';
        }
        
        if (['sale', 'purchase', 'account', 'hr'].includes(module)) {
            return 'business';
        }
        
        if (['website', 'api', 'connector'].includes(module)) {
            return 'integration';
        }
        
        return 'technical';
    }
    
    // 获取默认图标
    getDefaultIcon(appType) {
        const typeConfig = this.appTypes[appType];
        return `/web/static/img/apps/${typeConfig.icon}.png`;
    }
    
    // 处理搜索变化
    handleSearchChange(searchData) {
        const searchValue = searchData.value || '';
        
        if (searchValue.length < this.appConfig.searchThreshold) {
            this.showAllApps();
            return;
        }
        
        // 计算搜索结果
        const results = this.calculateAppSearchResults(searchValue);
        
        // 更新应用可见性
        this.updateAppVisibility(results);
        
        // 触发搜索完成事件
        this.env.bus.trigger('APP_SEARCH_COMPLETED', {
            query: searchValue,
            results: results
        });
    }
    
    // 计算应用搜索结果
    calculateAppSearchResults(searchValue) {
        const results = [];
        const searchLower = searchValue.toLowerCase();
        
        for (const [appKey, app] of this.apps) {
            const score = this.calculateAppSearchScore(searchLower, app);
            
            if (score > 0) {
                results.push({
                    appKey: appKey,
                    app: app,
                    score: score
                });
            }
        }
        
        return results.sort((a, b) => b.score - a.score);
    }
    
    // 计算应用搜索分数
    calculateAppSearchScore(searchValue, app) {
        let score = 0;
        
        // 应用名称匹配
        const appName = (app.props.string || '').toLowerCase();
        if (appName.includes(searchValue)) {
            score += 10;
            
            if (appName === searchValue) {
                score += 5; // 完全匹配
            }
            
            if (appName.startsWith(searchValue)) {
                score += 3; // 开头匹配
            }
        }
        
        // 模块名称匹配
        const moduleName = (app.metadata.module || '').toLowerCase();
        if (moduleName.includes(searchValue)) {
            score += 5;
        }
        
        // 描述匹配
        const description = (app.metadata.description || '').toLowerCase();
        if (description.includes(searchValue)) {
            score += 2;
        }
        
        // 类别匹配
        const category = (app.category || '').toLowerCase();
        if (category.includes(searchValue)) {
            score += 3;
        }
        
        return score;
    }
    
    // 更新应用可见性
    updateAppVisibility(results) {
        // 隐藏所有应用
        for (const app of this.apps.values()) {
            app.visible = false;
            app.searchScore = 0;
        }
        
        // 显示匹配的应用
        results.forEach(result => {
            result.app.visible = true;
            result.app.searchScore = result.score;
        });
        
        // 更新DOM
        this.updateAppDOM();
    }
    
    // 显示所有应用
    showAllApps() {
        for (const app of this.apps.values()) {
            app.visible = true;
            app.searchScore = 0;
        }
        
        this.updateAppDOM();
    }
    
    // 更新应用DOM
    updateAppDOM() {
        for (const [appKey, app] of this.apps) {
            if (app.Component && app.Component.settingsAppRef?.el) {
                const element = app.Component.settingsAppRef.el;
                element.classList.toggle('d-none', !app.visible);
                
                // 更新搜索分数属性
                element.setAttribute('data-search-score', app.searchScore);
            }
        }
    }
    
    // 切换应用
    switchToApp(appKey) {
        const app = this.apps.get(appKey);
        if (!app) return false;
        
        // 取消激活当前应用
        if (this.activeApp) {
            this.activeApp.active = false;
        }
        
        // 激活新应用
        app.active = true;
        this.activeApp = app;
        
        // 触发应用切换事件
        this.env.bus.trigger('APP_SWITCHED', {
            fromApp: this.activeApp ? this.activeApp.key : null,
            toApp: appKey,
            app: app
        });
        
        return true;
    }
    
    // 获取应用列表
    getAppList(options = {}) {
        let apps = Array.from(this.apps.values());
        
        // 过滤
        if (options.visible !== undefined) {
            apps = apps.filter(app => app.visible === options.visible);
        }
        
        if (options.type) {
            apps = apps.filter(app => app.type === options.type);
        }
        
        if (options.category) {
            apps = apps.filter(app => app.category === options.category);
        }
        
        // 排序
        if (options.sortBy) {
            apps.sort((a, b) => {
                switch (options.sortBy) {
                    case 'name':
                        return a.props.string.localeCompare(b.props.string);
                    case 'priority':
                        return a.priority - b.priority;
                    case 'score':
                        return b.searchScore - a.searchScore;
                    case 'created':
                        return b.created - a.created;
                    default:
                        return 0;
                }
            });
        }
        
        return apps;
    }
    
    // 获取应用统计
    getAppStatistics() {
        const stats = {
            total: this.apps.size,
            visible: 0,
            active: this.activeApp ? 1 : 0,
            byType: {},
            byCategory: {},
            searchResults: 0
        };
        
        for (const app of this.apps.values()) {
            if (app.visible) {
                stats.visible++;
            }
            
            if (app.searchScore > 0) {
                stats.searchResults++;
            }
            
            stats.byType[app.type] = (stats.byType[app.type] || 0) + 1;
            stats.byCategory[app.category] = (stats.byCategory[app.category] || 0) + 1;
        }
        
        return stats;
    }
    
    // 处理应用切换
    handleAppSwitch(switchData) {
        console.log('App switched:', switchData);
        
        // 更新URL
        if (switchData.toApp) {
            this.updateURL(switchData.toApp);
        }
        
        // 保存状态
        this.saveAppState(switchData.toApp);
    }
    
    // 处理窗口大小变化
    handleWindowResize() {
        // 响应式布局调整
        const isMobile = window.innerWidth < 768;
        
        for (const app of this.apps.values()) {
            if (app.Component && app.Component.settingsAppRef?.el) {
                const element = app.Component.settingsAppRef.el;
                element.classList.toggle('o_settings_app_mobile', isMobile);
            }
        }
    }
    
    // 更新URL
    updateURL(appKey) {
        const url = new URL(window.location);
        url.searchParams.set('app', appKey);
        window.history.replaceState({}, '', url);
    }
    
    // 保存应用状态
    saveAppState(appKey) {
        try {
            localStorage.setItem('odoo_active_settings_app', appKey);
        } catch (error) {
            console.warn('Failed to save app state:', error);
        }
    }
    
    // 加载应用状态
    loadAppState() {
        try {
            return localStorage.getItem('odoo_active_settings_app');
        } catch (error) {
            console.warn('Failed to load app state:', error);
            return null;
        }
    }
    
    // 清理应用
    removeApp(appKey) {
        const app = this.apps.get(appKey);
        if (!app) return false;
        
        // 如果是当前激活的应用，取消激活
        if (this.activeApp === app) {
            this.activeApp = null;
        }
        
        // 移除应用
        this.apps.delete(appKey);
        
        return true;
    }
    
    // 清理所有应用
    clearAllApps() {
        this.apps.clear();
        this.activeApp = null;
    }
}

// 使用示例
const appManager = new SettingsAppManager(env);

// 注册设置应用
const generalApp = appManager.registerSettingsApp('general', {
    string: 'General Settings',
    imgurl: '/web/static/img/apps/general.png',
    module: 'base',
    type: 'core',
    description: 'Basic system configuration'
});

// 切换到应用
appManager.switchToApp('general');

// 获取应用列表
const visibleApps = appManager.getAppList({ visible: true, sortBy: 'priority' });

// 获取统计信息
const stats = appManager.getAppStatistics();
console.log('App statistics:', stats);
```

## 技术特点

### 1. 响应式状态
- **状态绑定**: 绑定环境中的搜索状态
- **自动更新**: 状态变化时自动更新组件
- **响应式**: 使用useState创建响应式状态
- **环境集成**: 与环境状态深度集成

### 2. 副作用管理
- **useEffect**: 使用useEffect管理副作用
- **依赖追踪**: 精确追踪状态依赖
- **DOM操作**: 在副作用中进行DOM操作
- **性能优化**: 避免不必要的副作用执行

### 3. 可见性控制
- **智能隐藏**: 根据搜索结果智能控制可见性
- **DOM查询**: 查询相关的DOM元素
- **CSS切换**: 使用CSS类控制显示隐藏
- **条件逻辑**: 复杂的可见性判断逻辑

### 4. DOM引用
- **useRef**: 使用useRef管理DOM引用
- **安全访问**: 安全访问DOM元素
- **生命周期**: 与组件生命周期绑定
- **操作基础**: 为DOM操作提供基础

## 设计模式

### 1. 组件模式 (Component Pattern)
- **容器组件**: 作为设置应用的容器组件
- **属性驱动**: 通过属性配置组件行为
- **插槽支持**: 支持插槽内容的渲染

### 2. 观察者模式 (Observer Pattern)
- **状态监听**: 监听搜索状态的变化
- **自动响应**: 状态变化时自动响应
- **事件驱动**: 基于事件的更新机制

### 3. 策略模式 (Strategy Pattern)
- **可见性策略**: 不同的可见性控制策略
- **搜索策略**: 不同的搜索响应策略
- **渲染策略**: 不同的渲染处理策略

### 4. 代理模式 (Proxy Pattern)
- **状态代理**: 代理环境中的搜索状态
- **操作代理**: 代理DOM操作和事件处理
- **访问控制**: 控制对状态和DOM的访问

## 注意事项

1. **DOM安全**: 确保DOM元素存在后再进行操作
2. **性能考虑**: 避免频繁的DOM查询和操作
3. **状态同步**: 保持组件状态与环境状态的同步
4. **内存管理**: 及时清理事件监听器和引用

## 扩展建议

1. **动画效果**: 添加应用显示隐藏的动画效果
2. **懒加载**: 支持应用内容的懒加载
3. **缓存机制**: 添加搜索结果的缓存机制
4. **键盘导航**: 支持键盘导航应用切换
5. **状态持久化**: 支持应用状态的持久化

该设置应用组件为Odoo Web客户端的设置表单提供了智能的应用容器和可见性控制功能，通过响应式状态管理和副作用处理确保了良好的搜索体验和用户交互。

# SettingHeader - 设置标题组件

## 概述

`setting_header.js` 是 Odoo Web 客户端的设置标题组件，提供了设置表单中的标题显示功能。该模块包含14行代码，继承自标准设置组件，专门用于在设置表单中显示分组标题和章节标题，具备标签字符串处理、模板定制、字段信息获取等特性，是Odoo Web设置表单视图中结构化显示的重要组件。

## 文件信息
- **路径**: `/web/static/src/webclient/settings_form_view/settings/setting_header.js`
- **行数**: 14
- **模块**: `@web/webclient/settings_form_view/settings/setting_header`

## 依赖关系

```javascript
// 核心依赖
'@web/views/form/setting/setting'              // 设置组件基类
```

## 主组件定义

### 1. SettingHeader 组件

```javascript
class SettingHeader extends Setting {
    static template = "web.HeaderSetting";
    
    get labelString() {
        return this.props.string || this.props.record.fields[this.props.name].string;
    }
}
```

**组件特性**:
- **继承设置组件**: 继承标准设置组件的所有功能
- **标题专用模板**: 使用专门的标题模板
- **标签字符串处理**: 智能处理标签字符串的获取
- **字段信息集成**: 集成字段定义信息

## 核心属性

### 1. 组件属性

```javascript
// 模板配置
static template = "web.HeaderSetting";
```

**属性功能**:
- **template**: 指定设置标题的专用模板

## 核心功能

### 1. 标签字符串获取

```javascript
get labelString() {
    return this.props.string || this.props.record.fields[this.props.name].string;
}
```

**获取功能**:
- **属性优先**: 优先使用props中的string属性
- **字段回退**: 回退到字段定义中的string属性
- **智能选择**: 智能选择最合适的标签文本
- **空值处理**: 处理可能的空值情况

## 使用场景

### 1. 设置标题管理器

```javascript
// 设置标题管理器
class SettingHeaderManager {
    constructor(env) {
        this.env = env;
        this.headers = new Map();
        this.headerHierarchy = new Map();
        this.headerConfig = this.getHeaderConfig();
        this.setupManager();
    }
    
    setupManager() {
        // 设置标题类型
        this.headerTypes = {
            section: {
                level: 1,
                cssClass: 'o_setting_section_header',
                icon: 'fa-folder',
                collapsible: true
            },
            group: {
                level: 2,
                cssClass: 'o_setting_group_header',
                icon: 'fa-list',
                collapsible: true
            },
            category: {
                level: 3,
                cssClass: 'o_setting_category_header',
                icon: 'fa-tag',
                collapsible: false
            },
            divider: {
                level: 0,
                cssClass: 'o_setting_divider',
                icon: null,
                collapsible: false
            }
        };
        
        this.defaultType = 'group';
    }
    
    // 获取标题配置
    getHeaderConfig() {
        return {
            showIcons: true,
            showCollapse: true,
            autoCollapse: false,
            rememberState: true,
            animationDuration: 300
        };
    }
    
    // 创建设置标题
    createSettingHeader(headerId, headerData, headerType = 'group') {
        const typeConfig = this.headerTypes[headerType] || this.headerTypes[this.defaultType];
        
        const header = {
            Component: SettingHeader,
            props: {
                id: headerId,
                name: headerData.name || headerId,
                string: headerData.string || headerData.label,
                record: headerData.record,
                type: headerType,
                level: typeConfig.level,
                icon: headerData.icon || typeConfig.icon,
                collapsible: headerData.collapsible !== undefined ? headerData.collapsible : typeConfig.collapsible,
                collapsed: headerData.collapsed || false,
                description: headerData.description || '',
                ...this.getAdditionalProps(typeConfig, headerData)
            },
            id: headerId,
            type: headerType,
            level: typeConfig.level,
            children: [],
            parent: null,
            collapsed: headerData.collapsed || false,
            created: Date.now()
        };
        
        this.headers.set(headerId, header);
        
        return header;
    }
    
    // 获取额外属性
    getAdditionalProps(typeConfig, headerData) {
        const props = {
            cssClass: typeConfig.cssClass
        };
        
        if (this.headerConfig.showIcons && typeConfig.icon) {
            props.showIcon = true;
            props.iconClass = typeConfig.icon;
        }
        
        if (this.headerConfig.showCollapse && typeConfig.collapsible) {
            props.showCollapse = true;
            props.collapseIcon = 'fa-chevron-down';
        }
        
        if (headerData.badge) {
            props.badge = headerData.badge;
            props.badgeClass = headerData.badgeClass || 'badge-secondary';
        }
        
        return props;
    }
    
    // 构建标题层次结构
    buildHeaderHierarchy(headers) {
        // 按级别排序
        const sortedHeaders = Array.from(headers.values()).sort((a, b) => a.level - b.level);
        
        // 构建层次关系
        const stack = [];
        
        sortedHeaders.forEach(header => {
            // 找到父级标题
            while (stack.length > 0 && stack[stack.length - 1].level >= header.level) {
                stack.pop();
            }
            
            // 设置父子关系
            if (stack.length > 0) {
                const parent = stack[stack.length - 1];
                header.parent = parent.id;
                parent.children.push(header.id);
            }
            
            stack.push(header);
        });
        
        // 更新层次结构映射
        this.updateHierarchyMap();
    }
    
    // 更新层次结构映射
    updateHierarchyMap() {
        this.headerHierarchy.clear();
        
        for (const header of this.headers.values()) {
            if (!header.parent) {
                // 根级标题
                this.headerHierarchy.set(header.id, {
                    header: header,
                    children: this.getHeaderChildren(header.id),
                    depth: 0
                });
            }
        }
    }
    
    // 获取标题子项
    getHeaderChildren(headerId) {
        const header = this.headers.get(headerId);
        if (!header || !header.children.length) {
            return [];
        }
        
        return header.children.map(childId => {
            const child = this.headers.get(childId);
            return {
                header: child,
                children: this.getHeaderChildren(childId),
                depth: this.getHeaderDepth(childId)
            };
        });
    }
    
    // 获取标题深度
    getHeaderDepth(headerId) {
        let depth = 0;
        let currentId = headerId;
        
        while (currentId) {
            const header = this.headers.get(currentId);
            if (!header || !header.parent) break;
            
            depth++;
            currentId = header.parent;
        }
        
        return depth;
    }
    
    // 切换标题折叠状态
    toggleHeaderCollapse(headerId) {
        const header = this.headers.get(headerId);
        if (!header || !header.props.collapsible) {
            return false;
        }
        
        header.collapsed = !header.collapsed;
        header.props.collapsed = header.collapsed;
        
        // 更新子项可见性
        this.updateChildrenVisibility(headerId, !header.collapsed);
        
        // 保存状态
        if (this.headerConfig.rememberState) {
            this.saveHeaderState(headerId, header.collapsed);
        }
        
        // 触发事件
        this.env.bus.trigger('HEADER_COLLAPSE_TOGGLED', {
            headerId: headerId,
            collapsed: header.collapsed
        });
        
        return true;
    }
    
    // 更新子项可见性
    updateChildrenVisibility(headerId, visible) {
        const header = this.headers.get(headerId);
        if (!header) return;
        
        header.children.forEach(childId => {
            const child = this.headers.get(childId);
            if (child) {
                child.visible = visible;
                
                // 递归更新子项
                if (!visible || !child.collapsed) {
                    this.updateChildrenVisibility(childId, visible && !child.collapsed);
                }
            }
        });
    }
    
    // 展开所有标题
    expandAllHeaders() {
        for (const header of this.headers.values()) {
            if (header.props.collapsible && header.collapsed) {
                this.toggleHeaderCollapse(header.id);
            }
        }
    }
    
    // 折叠所有标题
    collapseAllHeaders() {
        for (const header of this.headers.values()) {
            if (header.props.collapsible && !header.collapsed) {
                this.toggleHeaderCollapse(header.id);
            }
        }
    }
    
    // 查找标题
    findHeaders(query) {
        const results = [];
        const searchLower = query.toLowerCase();
        
        for (const header of this.headers.values()) {
            const headerText = (header.props.string || '').toLowerCase();
            const headerDesc = (header.props.description || '').toLowerCase();
            
            if (headerText.includes(searchLower) || headerDesc.includes(searchLower)) {
                results.push({
                    header: header,
                    matchType: headerText.includes(searchLower) ? 'title' : 'description',
                    score: this.calculateMatchScore(query, header)
                });
            }
        }
        
        return results.sort((a, b) => b.score - a.score);
    }
    
    // 计算匹配分数
    calculateMatchScore(query, header) {
        let score = 0;
        const queryLower = query.toLowerCase();
        const titleLower = (header.props.string || '').toLowerCase();
        const descLower = (header.props.description || '').toLowerCase();
        
        // 标题匹配
        if (titleLower === queryLower) {
            score += 10; // 完全匹配
        } else if (titleLower.startsWith(queryLower)) {
            score += 5; // 开头匹配
        } else if (titleLower.includes(queryLower)) {
            score += 3; // 包含匹配
        }
        
        // 描述匹配
        if (descLower.includes(queryLower)) {
            score += 1;
        }
        
        // 级别权重（高级别标题优先）
        score += (4 - header.level);
        
        return score;
    }
    
    // 获取标题路径
    getHeaderPath(headerId) {
        const path = [];
        let currentId = headerId;
        
        while (currentId) {
            const header = this.headers.get(currentId);
            if (!header) break;
            
            path.unshift({
                id: header.id,
                title: header.props.string,
                level: header.level
            });
            
            currentId = header.parent;
        }
        
        return path;
    }
    
    // 导航到标题
    navigateToHeader(headerId) {
        const header = this.headers.get(headerId);
        if (!header) return false;
        
        // 展开父级标题
        this.expandParentHeaders(headerId);
        
        // 滚动到标题
        if (header.Component && header.Component.el) {
            header.Component.el.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
        
        // 高亮标题
        this.highlightHeader(headerId);
        
        return true;
    }
    
    // 展开父级标题
    expandParentHeaders(headerId) {
        let currentId = headerId;
        
        while (currentId) {
            const header = this.headers.get(currentId);
            if (!header) break;
            
            if (header.parent) {
                const parent = this.headers.get(header.parent);
                if (parent && parent.collapsed) {
                    this.toggleHeaderCollapse(header.parent);
                }
            }
            
            currentId = header.parent;
        }
    }
    
    // 高亮标题
    highlightHeader(headerId) {
        const header = this.headers.get(headerId);
        if (!header || !header.Component) return;
        
        // 添加高亮类
        if (header.Component.el) {
            header.Component.el.classList.add('o_setting_header_highlight');
            
            // 自动移除高亮
            setTimeout(() => {
                if (header.Component.el) {
                    header.Component.el.classList.remove('o_setting_header_highlight');
                }
            }, 3000);
        }
    }
    
    // 保存标题状态
    saveHeaderState(headerId, collapsed) {
        try {
            const stateKey = `odoo_header_state_${headerId}`;
            localStorage.setItem(stateKey, JSON.stringify({ collapsed }));
        } catch (error) {
            console.warn('Failed to save header state:', error);
        }
    }
    
    // 加载标题状态
    loadHeaderState(headerId) {
        try {
            const stateKey = `odoo_header_state_${headerId}`;
            const state = localStorage.getItem(stateKey);
            return state ? JSON.parse(state) : null;
        } catch (error) {
            console.warn('Failed to load header state:', error);
            return null;
        }
    }
    
    // 获取标题统计
    getHeaderStatistics() {
        const stats = {
            total: this.headers.size,
            byType: {},
            byLevel: {},
            collapsed: 0,
            visible: 0
        };
        
        for (const header of this.headers.values()) {
            // 按类型统计
            stats.byType[header.type] = (stats.byType[header.type] || 0) + 1;
            
            // 按级别统计
            stats.byLevel[header.level] = (stats.byLevel[header.level] || 0) + 1;
            
            // 状态统计
            if (header.collapsed) {
                stats.collapsed++;
            }
            
            if (header.visible !== false) {
                stats.visible++;
            }
        }
        
        return stats;
    }
    
    // 清理标题
    removeHeader(headerId) {
        const header = this.headers.get(headerId);
        if (!header) return false;
        
        // 移除子项
        header.children.forEach(childId => {
            this.removeHeader(childId);
        });
        
        // 从父项中移除
        if (header.parent) {
            const parent = this.headers.get(header.parent);
            if (parent) {
                const index = parent.children.indexOf(headerId);
                if (index > -1) {
                    parent.children.splice(index, 1);
                }
            }
        }
        
        // 移除标题
        this.headers.delete(headerId);
        
        return true;
    }
    
    // 清理所有标题
    clearAllHeaders() {
        this.headers.clear();
        this.headerHierarchy.clear();
    }
}

// 使用示例
const headerManager = new SettingHeaderManager(env);

// 创建设置标题
const sectionHeader = headerManager.createSettingHeader('general_section', {
    string: 'General Settings',
    description: 'Basic configuration options',
    icon: 'fa-cog'
}, 'section');

const groupHeader = headerManager.createSettingHeader('email_group', {
    string: 'Email Configuration',
    description: 'Email server and notification settings',
    icon: 'fa-envelope'
}, 'group');

// 构建层次结构
headerManager.buildHeaderHierarchy(headerManager.headers);

// 切换折叠状态
headerManager.toggleHeaderCollapse('general_section');

// 查找标题
const searchResults = headerManager.findHeaders('email');
console.log('Header search results:', searchResults);

// 获取统计信息
const stats = headerManager.getHeaderStatistics();
console.log('Header statistics:', stats);
```

## 技术特点

### 1. 继承简化
- **基类继承**: 继承标准设置组件的所有功能
- **最小实现**: 只重写必要的方法和属性
- **功能保持**: 保持原有设置组件的基础功能
- **接口一致**: 与基类保持一致的接口

### 2. 标签处理
- **智能获取**: 智能选择最合适的标签文本
- **优先级**: 属性优先，字段定义回退
- **空值安全**: 安全处理可能的空值情况
- **字段集成**: 集成字段定义信息

### 3. 模板定制
- **专用模板**: 使用专门的标题模板
- **样式分离**: 模板与逻辑分离
- **可定制**: 支持模板的定制和扩展
- **语义化**: 语义化的HTML结构

### 4. 轻量设计
- **代码精简**: 最少的代码实现最大的功能
- **性能优化**: 避免不必要的计算和处理
- **内存友好**: 最小的内存占用
- **快速渲染**: 快速的组件渲染

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **功能继承**: 继承基类的所有功能
- **选择性重写**: 只重写需要定制的部分
- **接口保持**: 保持与基类相同的接口

### 2. 模板方法模式 (Template Method Pattern)
- **模板定制**: 定制标题显示的模板
- **行为定义**: 定义标题的显示行为
- **扩展点**: 提供明确的扩展点

### 3. 策略模式 (Strategy Pattern)
- **标签策略**: 不同的标签获取策略
- **显示策略**: 不同的标题显示策略
- **样式策略**: 不同的样式应用策略

## 注意事项

1. **标签获取**: 确保正确获取标签字符串
2. **字段访问**: 安全访问字段定义信息
3. **模板一致**: 保持模板与组件的一致性
4. **性能考虑**: 避免不必要的计算和渲染

## 扩展建议

1. **图标支持**: 添加标题图标显示功能
2. **折叠功能**: 支持标题的折叠展开功能
3. **层级显示**: 支持多级标题的层级显示
4. **样式定制**: 支持标题样式的定制
5. **交互增强**: 添加标题的交互功能

该设置标题组件为Odoo Web客户端的设置表单提供了简洁高效的标题显示功能，通过智能的标签处理和专用模板确保了良好的用户体验和视觉效果。

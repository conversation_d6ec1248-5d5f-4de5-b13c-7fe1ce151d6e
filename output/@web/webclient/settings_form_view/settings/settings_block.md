# SettingsBlock - 设置块组件

## 概述

`settings_block.js` 是 Odoo Web 客户端的设置块组件，提供了设置表单中的块级容器和搜索响应功能。该模块包含67行代码，是一个OWL组件，专门用于管理设置块的显示状态和搜索过滤，具备搜索状态集成、可见性控制、子环境管理、高亮文本集成等特性，是Odoo Web设置表单视图中块级组织的核心组件。

## 文件信息
- **路径**: `/web/static/src/webclient/settings_form_view/settings/settings_block.js`
- **行数**: 67
- **模块**: `@web/webclient/settings_form_view/settings/settings_block`

## 依赖关系

```javascript
// 核心依赖
'@web/webclient/settings_form_view/highlight_text/highlight_text'     // 高亮文本组件
'@web/core/utils/strings'                                             // 字符串工具
'@odoo/owl'                                                           // OWL框架
```

## 主组件定义

### 1. SettingsBlock 组件

```javascript
class SettingsBlock extends Component {
    static template = "web.SettingsBlock";
    static components = {
        HighlightText,
    };
    static props = {
        title: { type: String, optional: true },
        tip: { type: String, optional: true },
        slots: { type: Object, optional: true },
        class: { type: String, optional: true },
    };
    
    setup() {
        this.state = useState({
            search: this.env.searchState,
        });
        this.showAllContainerState = useState({
            showAllContainer: false,
        });
        useChildSubEnv({
            showAllContainer: this.showAllContainerState,
        });
        
        this.settingsContainerRef = useRef("settingsContainer");
        this.settingsContainerTitleRef = useRef("settingsContainerTitle");
        this.settingsContainerTipRef = useRef("settingsContainerTip");
        
        useEffect(
            () => {
                const regexp = new RegExp(escapeRegExp(this.state.search.value), "i");
                const force =
                    this.state.search.value &&
                    !regexp.test([this.props.title, this.props.tip].join()) &&
                    !this.settingsContainerRef.el.querySelector(
                        ".o_setting_box.o_searchable_setting"
                    );
                this.toggleContainer(force);
            },
            () => [this.state.search.value]
        );
        
        onWillRender(() => {
            const regexp = new RegExp(escapeRegExp(this.state.search.value), "i");
            if (regexp.test([this.props.title, this.props.tip].join())) {
                this.showAllContainerState.showAllContainer = true;
            } else {
                this.showAllContainerState.showAllContainer = false;
            }
        });
    }
    
    toggleContainer(force) {
        if (this.settingsContainerTitleRef.el) {
            this.settingsContainerTitleRef.el.classList.toggle("d-none", force);
        }
        if (this.settingsContainerTipRef.el) {
            this.settingsContainerTipRef.el.classList.toggle("d-none", force);
        }
        this.settingsContainerRef.el.classList.toggle("d-none", force);
    }
}
```

**组件特性**:
- **块级容器**: 作为设置的块级容器组件
- **搜索响应**: 响应搜索状态变化并控制可见性
- **子环境管理**: 管理子组件的环境状态
- **高亮文本集成**: 集成高亮文本显示功能

## 核心属性

### 1. 组件属性

```javascript
// 模板配置
static template = "web.SettingsBlock";

// 子组件
static components = {
    HighlightText,
};

// 属性定义
static props = {
    title: { type: String, optional: true },    // 块标题
    tip: { type: String, optional: true },      // 提示文本
    slots: { type: Object, optional: true },    // 插槽内容
    class: { type: String, optional: true },    // CSS类名
};
```

**属性功能**:
- **title**: 设置块的标题文本
- **tip**: 设置块的提示或描述文本
- **slots**: 插槽内容对象
- **class**: 额外的CSS类名

### 2. 状态属性

```javascript
// 搜索状态
this.state = useState({
    search: this.env.searchState,
});

// 显示所有容器状态
this.showAllContainerState = useState({
    showAllContainer: false,
});

// DOM引用
this.settingsContainerRef = useRef("settingsContainer");
this.settingsContainerTitleRef = useRef("settingsContainerTitle");
this.settingsContainerTipRef = useRef("settingsContainerTip");
```

**状态属性功能**:
- **search**: 搜索状态的响应式引用
- **showAllContainerState**: 控制是否显示所有容器的状态
- **settingsContainerRef**: 设置容器的DOM引用
- **settingsContainerTitleRef**: 标题元素的DOM引用
- **settingsContainerTipRef**: 提示元素的DOM引用

## 核心功能

### 1. 搜索状态集成

```javascript
this.state = useState({
    search: this.env.searchState,
});
```

**集成功能**:
- **状态绑定**: 绑定环境中的搜索状态
- **响应式**: 使用useState创建响应式状态
- **自动更新**: 搜索状态变化时自动更新
- **环境集成**: 与环境中的搜索状态集成

### 2. 子环境管理

```javascript
useChildSubEnv({
    showAllContainer: this.showAllContainerState,
});
```

**环境管理功能**:
- **子环境**: 为子组件提供专门的环境
- **状态传递**: 将显示状态传递给子组件
- **环境隔离**: 隔离子组件的环境状态
- **状态共享**: 与子组件共享状态信息

### 3. 可见性控制

```javascript
useEffect(
    () => {
        const regexp = new RegExp(escapeRegExp(this.state.search.value), "i");
        const force =
            this.state.search.value &&
            !regexp.test([this.props.title, this.props.tip].join()) &&
            !this.settingsContainerRef.el.querySelector(
                ".o_setting_box.o_searchable_setting"
            );
        this.toggleContainer(force);
    },
    () => [this.state.search.value]
);
```

**控制逻辑**:
- **正则匹配**: 使用正则表达式匹配搜索文本
- **文本连接**: 连接标题和提示文本进行匹配
- **DOM查询**: 查询可搜索的设置项
- **强制隐藏**: 在特定条件下强制隐藏容器

### 4. 渲染前处理

```javascript
onWillRender(() => {
    const regexp = new RegExp(escapeRegExp(this.state.search.value), "i");
    if (regexp.test([this.props.title, this.props.tip].join())) {
        this.showAllContainerState.showAllContainer = true;
    } else {
        this.showAllContainerState.showAllContainer = false;
    }
});
```

**处理功能**:
- **渲染前检查**: 在组件渲染前进行状态检查
- **匹配检测**: 检测标题和提示是否匹配搜索
- **状态更新**: 更新显示所有容器的状态
- **子组件影响**: 影响子组件的显示行为

### 5. 容器切换

```javascript
toggleContainer(force) {
    if (this.settingsContainerTitleRef.el) {
        this.settingsContainerTitleRef.el.classList.toggle("d-none", force);
    }
    if (this.settingsContainerTipRef.el) {
        this.settingsContainerTipRef.el.classList.toggle("d-none", force);
    }
    this.settingsContainerRef.el.classList.toggle("d-none", force);
}
```

**切换功能**:
- **多元素控制**: 同时控制多个DOM元素的可见性
- **CSS类切换**: 使用CSS类控制显示隐藏
- **安全检查**: 检查DOM元素存在性
- **统一操作**: 统一控制容器及其子元素

## 使用场景

### 1. 设置块管理器

```javascript
// 设置块管理器
class SettingsBlockManager {
    constructor(env) {
        this.env = env;
        this.blocks = new Map();
        this.blockHierarchy = new Map();
        this.searchState = env.searchState;
        this.blockConfig = this.getBlockConfig();
        this.setupManager();
    }
    
    setupManager() {
        // 设置块类型
        this.blockTypes = {
            section: {
                level: 1,
                cssClass: 'o_settings_section',
                collapsible: true,
                searchable: true
            },
            group: {
                level: 2,
                cssClass: 'o_settings_group',
                collapsible: true,
                searchable: true
            },
            container: {
                level: 3,
                cssClass: 'o_settings_container',
                collapsible: false,
                searchable: true
            },
            widget: {
                level: 4,
                cssClass: 'o_settings_widget',
                collapsible: false,
                searchable: false
            }
        };
        
        this.setupEventListeners();
    }
    
    // 获取块配置
    getBlockConfig() {
        return {
            autoHide: true,
            searchThreshold: 2,
            highlightMatches: true,
            animationDuration: 300,
            rememberState: true
        };
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听搜索状态变化
        this.env.bus.addEventListener('SEARCH_STATE_CHANGED', (event) => {
            this.handleSearchChange(event.detail);
        });
        
        // 监听块状态变化
        this.env.bus.addEventListener('BLOCK_STATE_CHANGED', (event) => {
            this.handleBlockStateChange(event.detail);
        });
    }
    
    // 创建设置块
    createSettingsBlock(blockId, blockData, blockType = 'container') {
        const typeConfig = this.blockTypes[blockType] || this.blockTypes.container;
        
        const block = {
            Component: SettingsBlock,
            props: {
                title: blockData.title || blockData.name,
                tip: blockData.tip || blockData.description,
                class: `${typeConfig.cssClass} ${blockData.class || ''}`.trim(),
                slots: blockData.slots || {}
            },
            id: blockId,
            type: blockType,
            level: typeConfig.level,
            searchable: typeConfig.searchable,
            collapsible: typeConfig.collapsible,
            visible: true,
            collapsed: blockData.collapsed || false,
            children: [],
            parent: null,
            searchScore: 0,
            metadata: {
                module: blockData.module || 'base',
                category: blockData.category || 'general',
                priority: blockData.priority || 0,
                tags: blockData.tags || []
            },
            created: Date.now()
        };
        
        this.blocks.set(blockId, block);
        
        return block;
    }
    
    // 处理搜索变化
    handleSearchChange(searchData) {
        const searchValue = searchData.value || '';
        
        if (searchValue.length < this.blockConfig.searchThreshold) {
            this.showAllBlocks();
            return;
        }
        
        // 计算搜索结果
        const results = this.calculateBlockSearchResults(searchValue);
        
        // 更新块可见性
        this.updateBlockVisibility(results);
        
        // 高亮匹配文本
        if (this.blockConfig.highlightMatches) {
            this.highlightMatches(searchValue);
        }
    }
    
    // 计算块搜索结果
    calculateBlockSearchResults(searchValue) {
        const results = [];
        const searchRegex = new RegExp(escapeRegExp(searchValue), 'i');
        
        for (const [blockId, block] of this.blocks) {
            if (!block.searchable) continue;
            
            const score = this.calculateBlockSearchScore(searchValue, block);
            
            if (score > 0) {
                results.push({
                    blockId: blockId,
                    block: block,
                    score: score,
                    matches: this.findBlockMatches(searchValue, block)
                });
            }
        }
        
        return results.sort((a, b) => b.score - a.score);
    }
    
    // 计算块搜索分数
    calculateBlockSearchScore(searchValue, block) {
        let score = 0;
        const searchLower = searchValue.toLowerCase();
        
        // 标题匹配
        const title = (block.props.title || '').toLowerCase();
        if (title.includes(searchLower)) {
            score += 10;
            
            if (title === searchLower) {
                score += 5; // 完全匹配
            }
            
            if (title.startsWith(searchLower)) {
                score += 3; // 开头匹配
            }
        }
        
        // 提示匹配
        const tip = (block.props.tip || '').toLowerCase();
        if (tip.includes(searchLower)) {
            score += 5;
        }
        
        // 标签匹配
        block.metadata.tags.forEach(tag => {
            if (tag.toLowerCase().includes(searchLower)) {
                score += 2;
            }
        });
        
        // 类别匹配
        const category = (block.metadata.category || '').toLowerCase();
        if (category.includes(searchLower)) {
            score += 3;
        }
        
        return score;
    }
    
    // 查找块匹配项
    findBlockMatches(searchValue, block) {
        const matches = [];
        const searchLower = searchValue.toLowerCase();
        
        // 标题匹配
        const title = block.props.title || '';
        if (title.toLowerCase().includes(searchLower)) {
            matches.push({
                type: 'title',
                text: title,
                position: title.toLowerCase().indexOf(searchLower)
            });
        }
        
        // 提示匹配
        const tip = block.props.tip || '';
        if (tip.toLowerCase().includes(searchLower)) {
            matches.push({
                type: 'tip',
                text: tip,
                position: tip.toLowerCase().indexOf(searchLower)
            });
        }
        
        return matches;
    }
    
    // 更新块可见性
    updateBlockVisibility(results) {
        // 隐藏所有块
        for (const block of this.blocks.values()) {
            block.visible = false;
            block.searchScore = 0;
        }
        
        // 显示匹配的块
        results.forEach(result => {
            result.block.visible = true;
            result.block.searchScore = result.score;
            
            // 显示父级块
            this.showParentBlocks(result.blockId);
        });
        
        // 更新DOM
        this.updateBlockDOM();
    }
    
    // 显示父级块
    showParentBlocks(blockId) {
        let currentId = blockId;
        
        while (currentId) {
            const block = this.blocks.get(currentId);
            if (!block) break;
            
            block.visible = true;
            
            currentId = block.parent;
        }
    }
    
    // 显示所有块
    showAllBlocks() {
        for (const block of this.blocks.values()) {
            block.visible = true;
            block.searchScore = 0;
        }
        
        this.updateBlockDOM();
        this.clearHighlights();
    }
    
    // 更新块DOM
    updateBlockDOM() {
        for (const [blockId, block] of this.blocks) {
            if (block.Component && block.Component.settingsContainerRef?.el) {
                const element = block.Component.settingsContainerRef.el;
                element.classList.toggle('d-none', !block.visible);
                
                // 更新搜索分数属性
                element.setAttribute('data-search-score', block.searchScore);
            }
        }
    }
    
    // 高亮匹配文本
    highlightMatches(searchValue) {
        for (const [blockId, block] of this.blocks) {
            if (block.visible && block.Component) {
                this.highlightBlockMatches(block, searchValue);
            }
        }
    }
    
    // 高亮块匹配文本
    highlightBlockMatches(block, searchValue) {
        const component = block.Component;
        
        // 高亮标题
        if (component.settingsContainerTitleRef?.el) {
            this.highlightElementText(component.settingsContainerTitleRef.el, searchValue);
        }
        
        // 高亮提示
        if (component.settingsContainerTipRef?.el) {
            this.highlightElementText(component.settingsContainerTipRef.el, searchValue);
        }
    }
    
    // 高亮元素文本
    highlightElementText(element, searchValue) {
        const originalText = element.textContent;
        const searchRegex = new RegExp(`(${escapeRegExp(searchValue)})`, 'gi');
        
        const highlightedHTML = originalText.replace(searchRegex, '<mark>$1</mark>');
        
        if (highlightedHTML !== originalText) {
            element.innerHTML = highlightedHTML;
        }
    }
    
    // 清除高亮
    clearHighlights() {
        for (const [blockId, block] of this.blocks) {
            if (block.Component) {
                this.clearBlockHighlights(block);
            }
        }
    }
    
    // 清除块高亮
    clearBlockHighlights(block) {
        const component = block.Component;
        
        // 清除标题高亮
        if (component.settingsContainerTitleRef?.el) {
            const element = component.settingsContainerTitleRef.el;
            element.innerHTML = element.textContent;
        }
        
        // 清除提示高亮
        if (component.settingsContainerTipRef?.el) {
            const element = component.settingsContainerTipRef.el;
            element.innerHTML = element.textContent;
        }
    }
    
    // 构建块层次结构
    buildBlockHierarchy() {
        // 按级别排序
        const sortedBlocks = Array.from(this.blocks.values()).sort((a, b) => a.level - b.level);
        
        // 构建层次关系
        const stack = [];
        
        sortedBlocks.forEach(block => {
            // 找到父级块
            while (stack.length > 0 && stack[stack.length - 1].level >= block.level) {
                stack.pop();
            }
            
            // 设置父子关系
            if (stack.length > 0) {
                const parent = stack[stack.length - 1];
                block.parent = parent.id;
                parent.children.push(block.id);
            }
            
            stack.push(block);
        });
    }
    
    // 切换块折叠状态
    toggleBlockCollapse(blockId) {
        const block = this.blocks.get(blockId);
        if (!block || !block.collapsible) {
            return false;
        }
        
        block.collapsed = !block.collapsed;
        
        // 更新子块可见性
        this.updateChildBlocksVisibility(blockId, !block.collapsed);
        
        // 保存状态
        if (this.blockConfig.rememberState) {
            this.saveBlockState(blockId, block.collapsed);
        }
        
        return true;
    }
    
    // 更新子块可见性
    updateChildBlocksVisibility(blockId, visible) {
        const block = this.blocks.get(blockId);
        if (!block) return;
        
        block.children.forEach(childId => {
            const child = this.blocks.get(childId);
            if (child) {
                child.visible = visible;
                
                // 递归更新
                if (!visible || !child.collapsed) {
                    this.updateChildBlocksVisibility(childId, visible && !child.collapsed);
                }
            }
        });
    }
    
    // 获取块统计
    getBlockStatistics() {
        const stats = {
            total: this.blocks.size,
            visible: 0,
            collapsed: 0,
            byType: {},
            byLevel: {},
            searchResults: 0
        };
        
        for (const block of this.blocks.values()) {
            if (block.visible) {
                stats.visible++;
            }
            
            if (block.collapsed) {
                stats.collapsed++;
            }
            
            if (block.searchScore > 0) {
                stats.searchResults++;
            }
            
            stats.byType[block.type] = (stats.byType[block.type] || 0) + 1;
            stats.byLevel[block.level] = (stats.byLevel[block.level] || 0) + 1;
        }
        
        return stats;
    }
    
    // 保存块状态
    saveBlockState(blockId, collapsed) {
        try {
            const stateKey = `odoo_block_state_${blockId}`;
            localStorage.setItem(stateKey, JSON.stringify({ collapsed }));
        } catch (error) {
            console.warn('Failed to save block state:', error);
        }
    }
    
    // 加载块状态
    loadBlockState(blockId) {
        try {
            const stateKey = `odoo_block_state_${blockId}`;
            const state = localStorage.getItem(stateKey);
            return state ? JSON.parse(state) : null;
        } catch (error) {
            console.warn('Failed to load block state:', error);
            return null;
        }
    }
}

// 使用示例
const blockManager = new SettingsBlockManager(env);

// 创建设置块
const generalBlock = blockManager.createSettingsBlock('general_settings', {
    title: 'General Settings',
    tip: 'Configure basic system settings',
    category: 'system',
    tags: ['general', 'basic', 'system']
}, 'section');

const emailBlock = blockManager.createSettingsBlock('email_settings', {
    title: 'Email Configuration',
    tip: 'Setup email servers and notifications',
    category: 'communication',
    tags: ['email', 'smtp', 'notifications']
}, 'group');

// 构建层次结构
blockManager.buildBlockHierarchy();

// 获取统计信息
const stats = blockManager.getBlockStatistics();
console.log('Block statistics:', stats);
```

## 技术特点

### 1. 响应式状态
- **状态绑定**: 绑定环境中的搜索状态
- **自动更新**: 状态变化时自动更新组件
- **双向绑定**: 与子组件的双向状态绑定
- **环境集成**: 与环境状态深度集成

### 2. 子环境管理
- **环境隔离**: 为子组件提供隔离的环境
- **状态传递**: 将状态传递给子组件
- **环境扩展**: 扩展子组件的环境
- **状态共享**: 与子组件共享状态信息

### 3. 可见性控制
- **智能隐藏**: 根据搜索结果智能控制可见性
- **多元素控制**: 同时控制多个DOM元素
- **条件逻辑**: 复杂的可见性判断逻辑
- **DOM查询**: 查询相关的DOM元素

### 4. 生命周期管理
- **useEffect**: 使用useEffect管理副作用
- **onWillRender**: 在渲染前进行状态处理
- **依赖追踪**: 精确追踪状态依赖
- **性能优化**: 避免不必要的重新渲染

## 设计模式

### 1. 组件模式 (Component Pattern)
- **容器组件**: 作为设置的容器组件
- **属性驱动**: 通过属性配置组件行为
- **插槽支持**: 支持插槽内容的渲染

### 2. 观察者模式 (Observer Pattern)
- **状态监听**: 监听搜索状态的变化
- **自动响应**: 状态变化时自动响应
- **事件驱动**: 基于事件的更新机制

### 3. 策略模式 (Strategy Pattern)
- **可见性策略**: 不同的可见性控制策略
- **搜索策略**: 不同的搜索响应策略
- **渲染策略**: 不同的渲染处理策略

### 4. 代理模式 (Proxy Pattern)
- **状态代理**: 代理环境中的搜索状态
- **环境代理**: 代理子组件的环境
- **操作代理**: 代理DOM操作和事件处理

## 注意事项

1. **DOM安全**: 确保DOM元素存在后再进行操作
2. **性能考虑**: 避免频繁的DOM查询和操作
3. **状态同步**: 保持组件状态与环境状态的同步
4. **内存管理**: 及时清理事件监听器和引用

## 扩展建议

1. **动画效果**: 添加块显示隐藏的动画效果
2. **折叠功能**: 支持块的折叠展开功能
3. **拖拽排序**: 支持块的拖拽排序功能
4. **状态持久化**: 支持块状态的持久化
5. **主题定制**: 支持块样式的主题定制

该设置块组件为Odoo Web客户端的设置表单提供了智能的块级容器和搜索响应功能，通过响应式状态管理和子环境管理确保了良好的搜索体验和组件组织。

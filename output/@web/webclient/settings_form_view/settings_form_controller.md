# SettingsFormController - 设置表单控制器

## 概述

`settings_form_controller.js` 是 Odoo Web 客户端的设置表单控制器，提供了设置表单的业务逻辑控制和用户交互管理功能。该模块包含154行代码，是一个OWL控制器组件，专门用于管理设置表单的状态、搜索、保存确认等功能，具备搜索状态管理、保存确认对话框、无内容显示、自动聚焦等特性，是Odoo Web设置表单视图中业务逻辑控制的核心组件。

## 文件信息
- **路径**: `/web/static/src/webclient/settings_form_view/settings_form_controller.js`
- **行数**: 154
- **模块**: `@web/webclient/settings_form_view/settings_form_controller`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'                                           // 国际化翻译
'@web/core/utils/hooks'                                                // 工具钩子
'@web/core/utils/objects'                                              // 对象工具
'@web/views/form/form_view'                                            // 表单视图基类
'@web/webclient/settings_form_view/settings_confirmation_dialog'      // 设置确认对话框
'@web/webclient/settings_form_view/settings_form_renderer'            // 设置表单渲染器
'@odoo/owl'                                                            // OWL框架
```

## 主控制器定义

### 1. SettingsFormController 控制器

```javascript
class SettingsFormController extends formView.Controller {
    static template = "web.SettingsFormView";
    static components = {
        ...formView.Controller.components,
        Renderer: SettingsFormRenderer,
    };

    setup() {
        super.setup();
        useAutofocus();
        this.state = useState({ displayNoContent: false });
        this.searchState = useState({ value: "" });
        this.rootRef = useRef("root");
        this.canCreate = false;
        useSubEnv({ searchState: this.searchState });
        
        // 搜索状态监听
        useEffect(
            () => {
                if (this.searchState.value) {
                    if (
                        this.rootRef.el.querySelector(".o_settings_container:not(.d-none)") ||
                        this.rootRef.el.querySelector(
                            ".settings .o_settings_container:not(.d-none) .o_setting_box.o_searchable_setting"
                        )
                    ) {
                        this.state.displayNoContent = false;
                    } else {
                        this.state.displayNoContent = true;
                    }
                } else {
                    this.state.displayNoContent = false;
                }
            },
            () => [this.searchState.value]
        );

        this.initialApp = "module" in this.props.context ? this.props.context.module : "";
    }
}
```

**控制器特性**:
- **继承扩展**: 继承formView.Controller基类
- **搜索状态**: 管理搜索状态和无内容显示
- **自动聚焦**: 支持自动聚焦功能
- **子环境**: 为子组件提供搜索状态环境

## 核心属性

### 1. 组件属性

```javascript
// 模板配置
static template = "web.SettingsFormView";

// 组件配置
static components = {
    ...formView.Controller.components,
    Renderer: SettingsFormRenderer,
};
```

**属性功能**:
- **template**: 指定设置表单视图的模板
- **components**: 继承基类组件并使用SettingsFormRenderer
- **Renderer**: 使用专门的设置表单渲染器

### 2. 状态属性

```javascript
// 组件状态
this.state = useState({ displayNoContent: false });

// 搜索状态
this.searchState = useState({ value: "" });

// DOM引用
this.rootRef = useRef("root");

// 创建权限
this.canCreate = false;

// 初始应用
this.initialApp = "module" in this.props.context ? this.props.context.module : "";
```

**状态属性功能**:
- **displayNoContent**: 是否显示无内容提示
- **searchState**: 搜索状态管理
- **rootRef**: 根元素的DOM引用
- **canCreate**: 禁用创建功能
- **initialApp**: 初始显示的应用模块

## 核心功能

### 1. 模型参数配置

```javascript
get modelParams() {
    const headerFields = Object.values(this.archInfo.fieldNodes)
        .filter((fieldNode) => fieldNode.options.isHeaderField)
        .map((fieldNode) => fieldNode.name);
    return {
        ...super.modelParams,
        headerFields,
        onChangeHeaderFields: () => this._confirmSave(),
    };
}
```

**配置功能**:
- **头部字段**: 提取头部字段信息
- **字段过滤**: 过滤出标记为头部字段的字段
- **变更回调**: 头部字段变更时触发保存确认
- **参数扩展**: 扩展基类的模型参数

### 2. 动作按钮执行前处理

```javascript
async beforeExecuteActionButton(clickParams) {
    if (clickParams.name === "cancel") {
        return true;
    }
    if (
        (await this.model.root.isDirty()) &&
        !["execute"].includes(clickParams.name) &&
        !clickParams.noSaveDialog
    ) {
        return this._confirmSave();
    } else {
        return this.model.root.save();
    }
}
```

**处理功能**:
- **取消检查**: 特殊处理取消操作
- **脏数据检查**: 检查是否有未保存的更改
- **保存确认**: 有更改时显示保存确认对话框
- **直接保存**: 无更改时直接保存

### 3. 离开前检查

```javascript
async beforeLeave() {
    const dirty = await this.model.root.isDirty();
    if (dirty) {
        return this._confirmSave();
    }
}
```

**检查功能**:
- **脏数据检查**: 检查是否有未保存的数据
- **保存确认**: 有未保存数据时显示确认对话框
- **导航保护**: 保护用户的未保存工作

### 4. 保存和丢弃操作

```javascript
async save() {
    await this.env.onClickViewButton({
        clickParams: {
            name: "execute",
            type: "object",
        },
        getResParams: () =>
            pick(this.model.root, "context", "evalContext", "resModel", "resId", "resIds"),
    });
}

discard() {
    this.env.onClickViewButton({
        clickParams: {
            name: "cancel",
            type: "object",
            special: "cancel",
        },
        getResParams: () =>
            pick(this.model.root, "context", "evalContext", "resModel", "resId", "resIds"),
    });
}
```

**操作功能**:
- **保存执行**: 执行设置保存操作
- **参数提取**: 提取必要的资源参数
- **丢弃操作**: 丢弃未保存的更改
- **按钮模拟**: 模拟视图按钮点击

### 5. 保存确认对话框

```javascript
async _confirmSave() {
    let _continue = true;
    await new Promise((resolve) => {
        this.dialogService.add(SettingsConfirmationDialog, {
            body: _t("Would you like to save your changes?"),
            confirm: async () => {
                await this.save();
                // It doesn't make sense to do the action of the button
                // as the res.config.settings `execute` method will trigger a reload.
                _continue = false;
                resolve();
            },
            cancel: async () => {
                await this.model.root.discard();
                await this.model.root.save();
                _continue = true;
                resolve();
            },
            stayHere: () => {
                _continue = false;
                resolve();
            },
        });
    });
    return _continue;
}
```

**确认功能**:
- **对话框显示**: 显示设置确认对话框
- **保存选择**: 用户选择保存更改
- **丢弃选择**: 用户选择丢弃更改
- **留在此处**: 用户选择留在当前页面
- **继续标志**: 返回是否继续执行后续操作

## 使用场景

### 1. 设置表单管理器

```javascript
// 设置表单管理器
class SettingsFormManager {
    constructor(env) {
        this.env = env;
        this.controller = null;
        this.setupManager();
    }
    
    setupManager() {
        // 设置管理配置
        this.managerConfig = {
            autoSave: false,
            confirmOnLeave: true,
            searchEnabled: true,
            validationEnabled: true,
            backupEnabled: true
        };
        
        // 设置状态管理
        this.stateManager = {
            currentState: 'idle',
            previousState: null,
            stateHistory: [],
            maxHistorySize: 10
        };
        
        // 设置验证规则
        this.validationRules = new Map();
        
        this.setupEventListeners();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听表单状态变化
        this.env.bus.addEventListener('FORM_STATE_CHANGED', (event) => {
            this.handleFormStateChange(event.detail);
        });
        
        // 监听搜索状态变化
        this.env.bus.addEventListener('SEARCH_STATE_CHANGED', (event) => {
            this.handleSearchStateChange(event.detail);
        });
        
        // 监听保存操作
        this.env.bus.addEventListener('SAVE_REQUESTED', (event) => {
            this.handleSaveRequest(event.detail);
        });
    }
    
    // 创建设置表单控制器
    createSettingsFormController(props) {
        const controller = new SettingsFormController();
        controller.props = props;
        
        // 扩展控制器功能
        this.extendController(controller);
        
        this.controller = controller;
        return controller;
    }
    
    // 扩展控制器功能
    extendController(controller) {
        // 添加自动保存功能
        if (this.managerConfig.autoSave) {
            this.addAutoSaveFeature(controller);
        }
        
        // 添加验证功能
        if (this.managerConfig.validationEnabled) {
            this.addValidationFeature(controller);
        }
        
        // 添加备份功能
        if (this.managerConfig.backupEnabled) {
            this.addBackupFeature(controller);
        }
    }
    
    // 添加自动保存功能
    addAutoSaveFeature(controller) {
        const originalSetup = controller.setup.bind(controller);
        
        controller.setup = function() {
            originalSetup();
            
            // 设置自动保存定时器
            this.autoSaveInterval = setInterval(async () => {
                if (await this.model.root.isDirty()) {
                    await this.save();
                }
            }, 30000); // 30秒自动保存
            
            // 清理定时器
            onWillUnmount(() => {
                if (this.autoSaveInterval) {
                    clearInterval(this.autoSaveInterval);
                }
            });
        };
    }
    
    // 添加验证功能
    addValidationFeature(controller) {
        const originalSave = controller.save.bind(controller);
        
        controller.save = async function() {
            // 执行验证
            const validationResult = await this.validateForm();
            
            if (!validationResult.isValid) {
                this.showValidationErrors(validationResult.errors);
                return false;
            }
            
            return await originalSave();
        };
        
        // 添加验证方法
        controller.validateForm = async function() {
            const errors = [];
            
            // 验证必填字段
            const requiredFields = this.getRequiredFields();
            for (const field of requiredFields) {
                if (!field.value) {
                    errors.push({
                        field: field.name,
                        message: `${field.label} is required`
                    });
                }
            }
            
            // 验证字段格式
            const formatErrors = await this.validateFieldFormats();
            errors.push(...formatErrors);
            
            return {
                isValid: errors.length === 0,
                errors: errors
            };
        };
        
        controller.showValidationErrors = function(errors) {
            const errorMessage = errors.map(error => error.message).join('\n');
            this.env.services.notification.add(errorMessage, {
                type: 'danger',
                sticky: true
            });
        };
    }
    
    // 添加备份功能
    addBackupFeature(controller) {
        const originalSetup = controller.setup.bind(controller);
        
        controller.setup = function() {
            originalSetup();
            
            // 创建备份存储
            this.backupStorage = new Map();
            
            // 监听字段变化
            useEffect(() => {
                this.createBackup();
            }, () => [this.model.root.data]);
        };
        
        controller.createBackup = function() {
            const backup = {
                data: JSON.parse(JSON.stringify(this.model.root.data)),
                timestamp: Date.now(),
                id: this.generateBackupId()
            };
            
            this.backupStorage.set(backup.id, backup);
            
            // 限制备份数量
            if (this.backupStorage.size > 10) {
                const oldestId = Array.from(this.backupStorage.keys())[0];
                this.backupStorage.delete(oldestId);
            }
        };
        
        controller.restoreBackup = function(backupId) {
            const backup = this.backupStorage.get(backupId);
            if (backup) {
                this.model.root.update(backup.data);
                return true;
            }
            return false;
        };
        
        controller.generateBackupId = function() {
            return `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        };
    }
    
    // 处理表单状态变化
    handleFormStateChange(stateData) {
        const { oldState, newState, timestamp } = stateData;
        
        // 更新状态历史
        this.stateManager.previousState = this.stateManager.currentState;
        this.stateManager.currentState = newState;
        
        this.stateManager.stateHistory.push({
            from: oldState,
            to: newState,
            timestamp: timestamp
        });
        
        // 限制历史大小
        if (this.stateManager.stateHistory.length > this.managerConfig.maxHistorySize) {
            this.stateManager.stateHistory.shift();
        }
        
        // 触发状态变化事件
        this.env.bus.trigger('MANAGER_STATE_CHANGED', {
            manager: this,
            stateData: stateData
        });
    }
    
    // 处理搜索状态变化
    handleSearchStateChange(searchData) {
        const { searchValue, resultCount } = searchData;
        
        // 更新搜索统计
        this.updateSearchStatistics(searchValue, resultCount);
        
        // 如果没有搜索结果，显示建议
        if (searchValue && resultCount === 0) {
            this.showSearchSuggestions(searchValue);
        }
    }
    
    // 更新搜索统计
    updateSearchStatistics(searchValue, resultCount) {
        if (!this.searchStatistics) {
            this.searchStatistics = {
                totalSearches: 0,
                successfulSearches: 0,
                averageResultCount: 0,
                popularTerms: new Map()
            };
        }
        
        this.searchStatistics.totalSearches++;
        
        if (resultCount > 0) {
            this.searchStatistics.successfulSearches++;
        }
        
        // 更新平均结果数
        this.searchStatistics.averageResultCount = 
            (this.searchStatistics.averageResultCount + resultCount) / 2;
        
        // 记录热门搜索词
        const termCount = this.searchStatistics.popularTerms.get(searchValue) || 0;
        this.searchStatistics.popularTerms.set(searchValue, termCount + 1);
    }
    
    // 显示搜索建议
    showSearchSuggestions(searchValue) {
        const suggestions = this.generateSearchSuggestions(searchValue);
        
        if (suggestions.length > 0) {
            this.env.services.notification.add(
                `No results found for "${searchValue}". Did you mean: ${suggestions.join(', ')}?`,
                { type: 'info' }
            );
        }
    }
    
    // 生成搜索建议
    generateSearchSuggestions(searchValue) {
        const suggestions = [];
        const popularTerms = Array.from(this.searchStatistics.popularTerms.keys());
        
        // 基于编辑距离的模糊匹配
        popularTerms.forEach(term => {
            const distance = this.calculateEditDistance(searchValue.toLowerCase(), term.toLowerCase());
            if (distance <= 2 && distance > 0) {
                suggestions.push(term);
            }
        });
        
        return suggestions.slice(0, 3); // 最多3个建议
    }
    
    // 计算编辑距离
    calculateEditDistance(str1, str2) {
        const matrix = [];
        
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        
        return matrix[str2.length][str1.length];
    }
    
    // 处理保存请求
    async handleSaveRequest(saveData) {
        const { source, options } = saveData;
        
        try {
            if (this.controller) {
                const result = await this.controller.save();
                
                this.env.bus.trigger('SAVE_COMPLETED', {
                    source: source,
                    result: result,
                    timestamp: Date.now()
                });
                
                return result;
            }
        } catch (error) {
            this.env.bus.trigger('SAVE_FAILED', {
                source: source,
                error: error,
                timestamp: Date.now()
            });
            
            throw error;
        }
    }
    
    // 获取管理器状态
    getManagerStatus() {
        return {
            currentState: this.stateManager.currentState,
            hasController: !!this.controller,
            config: this.managerConfig,
            searchStatistics: this.searchStatistics,
            stateHistory: this.stateManager.stateHistory.slice(-5) // 最近5个状态
        };
    }
    
    // 重置管理器
    reset() {
        this.stateManager.currentState = 'idle';
        this.stateManager.previousState = null;
        this.stateManager.stateHistory = [];
        this.searchStatistics = null;
        this.controller = null;
    }
    
    // 销毁管理器
    destroy() {
        // 清理控制器
        if (this.controller && this.controller.autoSaveInterval) {
            clearInterval(this.controller.autoSaveInterval);
        }
        
        // 清理事件监听器
        this.env.bus.removeEventListener('FORM_STATE_CHANGED');
        this.env.bus.removeEventListener('SEARCH_STATE_CHANGED');
        this.env.bus.removeEventListener('SAVE_REQUESTED');
        
        // 重置状态
        this.reset();
    }
}

// 使用示例
const formManager = new SettingsFormManager(env);

// 创建控制器
const controller = formManager.createSettingsFormController({
    resModel: 'res.config.settings',
    context: { module: 'base' }
});

// 获取管理器状态
const status = formManager.getManagerStatus();
console.log('Manager status:', status);
```

## 技术特点

### 1. 继承扩展
- **基类继承**: 继承formView.Controller的所有功能
- **组件替换**: 使用专门的SettingsFormRenderer
- **方法重写**: 重写特定的控制器方法
- **功能增强**: 在基类基础上增强功能

### 2. 状态管理
- **搜索状态**: 管理搜索输入和结果状态
- **显示状态**: 管理无内容显示状态
- **子环境**: 为子组件提供状态环境
- **响应式**: 使用useState创建响应式状态

### 3. 生命周期管理
- **离开前检查**: 检查未保存的更改
- **自动聚焦**: 自动聚焦到合适的元素
- **副作用管理**: 使用useEffect管理副作用
- **资源清理**: 适当的资源清理

### 4. 用户交互
- **保存确认**: 智能的保存确认对话框
- **搜索响应**: 响应搜索输入变化
- **按钮处理**: 处理各种按钮操作
- **导航保护**: 保护用户的未保存工作

## 设计模式

### 1. 控制器模式 (Controller Pattern)
- **业务逻辑**: 管理表单的业务逻辑
- **用户交互**: 处理用户交互事件
- **状态协调**: 协调各组件的状态

### 2. 观察者模式 (Observer Pattern)
- **状态监听**: 监听搜索状态变化
- **事件响应**: 响应各种事件
- **自动更新**: 状态变化时自动更新

### 3. 策略模式 (Strategy Pattern)
- **保存策略**: 不同的保存处理策略
- **验证策略**: 不同的验证策略
- **搜索策略**: 不同的搜索响应策略

### 4. 模板方法模式 (Template Method Pattern)
- **生命周期**: 定义标准的生命周期流程
- **钩子方法**: 提供扩展点
- **流程控制**: 控制操作流程

## 注意事项

1. **状态同步**: 确保各状态的同步更新
2. **内存管理**: 及时清理事件监听器和定时器
3. **用户体验**: 提供清晰的用户反馈
4. **错误处理**: 处理各种异常情况

## 扩展建议

1. **自动保存**: 添加自动保存功能
2. **版本控制**: 支持设置的版本控制
3. **批量操作**: 支持批量设置操作
4. **导入导出**: 支持设置的导入导出
5. **权限控制**: 添加细粒度的权限控制

该设置表单控制器为Odoo Web客户端提供了完整的设置表单业务逻辑控制功能，通过状态管理和用户交互处理确保了良好的用户体验和数据安全性。

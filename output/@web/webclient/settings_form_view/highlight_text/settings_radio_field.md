# SettingsRadioField - 设置单选字段

## 概述

`settings_radio_field.js` 是 Odoo Web 客户端的设置单选字段组件，提供了带有高亮文本功能的单选字段。该模块包含21行代码，继承自标准单选字段，专门用于设置表单中的单选选项显示，具备高亮文本集成、组件扩展、模板定制等特性，是Odoo Web设置表单视图中单选字段增强显示的重要组件。

## 文件信息
- **路径**: `/web/static/src/webclient/settings_form_view/highlight_text/settings_radio_field.js`
- **行数**: 21
- **模块**: `@web/webclient/settings_form_view/highlight_text/settings_radio_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                                                            // 注册表系统
'@web/views/fields/radio/radio_field'                                          // 单选字段基类
'@web/webclient/settings_form_view/highlight_text/highlight_text'               // 高亮文本组件
```

## 主组件定义

### 1. SettingsRadioField 组件

```javascript
class SettingsRadioField extends RadioField {
    static template = "web.SettingsRadioField";
    static components = { ...super.components, HighlightText };
}
```

**组件特性**:
- **继承单选字段**: 继承标准单选字段的所有功能
- **高亮文本集成**: 集成HighlightText组件
- **组件扩展**: 扩展父类的组件集合
- **设置专用模板**: 使用专门的设置模板

## 核心属性

### 1. 组件属性

```javascript
// 模板配置
static template = "web.SettingsRadioField";

// 组件集合
static components = { ...super.components, HighlightText };
```

**属性功能**:
- **template**: 指定设置单选字段的模板
- **components**: 扩展父类组件并添加HighlightText

## 字段注册

### 1. 字段定义

```javascript
const settingsRadioField = {
    ...radioField,
    component: SettingsRadioField,
};
```

**字段配置**:
- **基础继承**: 继承标准单选字段的所有配置
- **组件替换**: 使用自定义的SettingsRadioField组件
- **功能扩展**: 扩展高亮文本显示功能

### 2. 注册表注册

```javascript
registry.category("fields").add("base_settings.radio", settingsRadioField);
```

**注册功能**:
- **字段类型**: 注册为"base_settings.radio"字段类型
- **设置专用**: 专门用于设置表单
- **全局可用**: 在整个应用中可用

## 使用场景

### 1. 设置单选字段管理器

```javascript
// 设置单选字段管理器
class SettingsRadioFieldManager {
    constructor(env) {
        this.env = env;
        this.radioFields = new Map();
        this.highlightConfig = this.getHighlightConfig();
        this.setupManager();
    }
    
    setupManager() {
        // 设置单选字段类型
        this.radioFieldTypes = {
            standard: {
                layout: 'horizontal',
                showLabels: true,
                highlightSearch: false
            },
            compact: {
                layout: 'vertical',
                showLabels: false,
                highlightSearch: true
            },
            enhanced: {
                layout: 'grid',
                showLabels: true,
                highlightSearch: true,
                showDescriptions: true
            },
            minimal: {
                layout: 'inline',
                showLabels: false,
                highlightSearch: false
            }
        };
        
        this.defaultType = 'standard';
    }
    
    // 获取高亮配置
    getHighlightConfig() {
        return {
            enabled: true,
            highlightClass: 'o_radio_highlight',
            searchDelay: 300,
            caseSensitive: false,
            highlightStyle: {
                backgroundColor: '#fff3cd',
                color: '#856404',
                fontWeight: 'bold'
            }
        };
    }
    
    // 创建设置单选字段
    createSettingsRadioField(fieldName, options, fieldType = 'standard') {
        const fieldId = this.generateFieldId(fieldName, fieldType);
        
        if (this.radioFields.has(fieldId)) {
            return this.radioFields.get(fieldId);
        }
        
        const typeConfig = this.radioFieldTypes[fieldType] || this.radioFieldTypes[this.defaultType];
        
        const radioField = {
            Component: SettingsRadioField,
            props: {
                name: fieldName,
                options: this.processOptions(options, typeConfig),
                layout: typeConfig.layout,
                showLabels: typeConfig.showLabels,
                highlightConfig: typeConfig.highlightSearch ? this.highlightConfig : null,
                ...this.getAdditionalProps(typeConfig)
            },
            id: fieldId,
            type: fieldType,
            created: Date.now()
        };
        
        this.radioFields.set(fieldId, radioField);
        
        return radioField;
    }
    
    // 生成字段ID
    generateFieldId(fieldName, fieldType) {
        return `radio_${fieldName}_${fieldType}_${Date.now()}`;
    }
    
    // 处理选项
    processOptions(options, typeConfig) {
        return options.map((option, index) => {
            const processedOption = {
                value: option.value,
                label: option.label,
                description: option.description || '',
                icon: option.icon || null,
                disabled: option.disabled || false,
                highlighted: false
            };
            
            // 添加描述（如果配置允许）
            if (typeConfig.showDescriptions && !processedOption.description) {
                processedOption.description = this.generateDescription(option);
            }
            
            // 添加图标（如果没有）
            if (!processedOption.icon && typeConfig.showIcons) {
                processedOption.icon = this.generateIcon(option, index);
            }
            
            return processedOption;
        });
    }
    
    // 生成描述
    generateDescription(option) {
        const descriptions = {
            'enabled': 'Enable this feature',
            'disabled': 'Disable this feature',
            'auto': 'Automatically determine the best option',
            'manual': 'Manually configure this option',
            'default': 'Use the default setting'
        };
        
        return descriptions[option.value] || `Option: ${option.label}`;
    }
    
    // 生成图标
    generateIcon(option, index) {
        const icons = ['fa-circle', 'fa-square', 'fa-diamond', 'fa-star', 'fa-heart'];
        return icons[index % icons.length];
    }
    
    // 获取额外属性
    getAdditionalProps(typeConfig) {
        const props = {};
        
        if (typeConfig.layout === 'grid') {
            props.columns = 2;
            props.spacing = 'medium';
        }
        
        if (typeConfig.layout === 'inline') {
            props.separator = ' | ';
            props.compact = true;
        }
        
        if (typeConfig.showDescriptions) {
            props.descriptionPosition = 'below';
            props.descriptionClass = 'text-muted small';
        }
        
        return props;
    }
    
    // 高亮搜索选项
    highlightSearchOptions(fieldId, searchValue) {
        const radioField = this.radioFields.get(fieldId);
        if (!radioField || !radioField.props.highlightConfig) {
            return;
        }
        
        const options = radioField.props.options;
        let hasHighlights = false;
        
        options.forEach(option => {
            const searchText = searchValue.toLowerCase();
            const optionText = option.label.toLowerCase();
            const descriptionText = (option.description || '').toLowerCase();
            
            const isMatch = optionText.includes(searchText) || 
                           descriptionText.includes(searchText);
            
            option.highlighted = isMatch;
            if (isMatch) {
                hasHighlights = true;
            }
        });
        
        // 触发重新渲染
        if (hasHighlights) {
            this.env.bus.trigger('RADIO_FIELD:HIGHLIGHT_UPDATED', {
                fieldId: fieldId,
                searchValue: searchValue,
                hasHighlights: hasHighlights
            });
        }
    }
    
    // 清除高亮
    clearHighlights(fieldId) {
        const radioField = this.radioFields.get(fieldId);
        if (!radioField) return;
        
        radioField.props.options.forEach(option => {
            option.highlighted = false;
        });
        
        this.env.bus.trigger('RADIO_FIELD:HIGHLIGHT_CLEARED', {
            fieldId: fieldId
        });
    }
    
    // 获取选中值
    getSelectedValue(fieldId) {
        const radioField = this.radioFields.get(fieldId);
        if (!radioField) return null;
        
        return radioField.props.value || null;
    }
    
    // 设置选中值
    setSelectedValue(fieldId, value) {
        const radioField = this.radioFields.get(fieldId);
        if (!radioField) return false;
        
        const option = radioField.props.options.find(opt => opt.value === value);
        if (!option || option.disabled) return false;
        
        radioField.props.value = value;
        
        this.env.bus.trigger('RADIO_FIELD:VALUE_CHANGED', {
            fieldId: fieldId,
            value: value,
            option: option
        });
        
        return true;
    }
    
    // 获取字段选项
    getFieldOptions(fieldId) {
        const radioField = this.radioFields.get(fieldId);
        return radioField ? radioField.props.options : [];
    }
    
    // 添加选项
    addOption(fieldId, option) {
        const radioField = this.radioFields.get(fieldId);
        if (!radioField) return false;
        
        const processedOption = this.processOptions([option], radioField.type)[0];
        radioField.props.options.push(processedOption);
        
        this.env.bus.trigger('RADIO_FIELD:OPTION_ADDED', {
            fieldId: fieldId,
            option: processedOption
        });
        
        return true;
    }
    
    // 移除选项
    removeOption(fieldId, value) {
        const radioField = this.radioFields.get(fieldId);
        if (!radioField) return false;
        
        const optionIndex = radioField.props.options.findIndex(opt => opt.value === value);
        if (optionIndex === -1) return false;
        
        const removedOption = radioField.props.options.splice(optionIndex, 1)[0];
        
        // 如果移除的是当前选中项，清除选中状态
        if (radioField.props.value === value) {
            radioField.props.value = null;
        }
        
        this.env.bus.trigger('RADIO_FIELD:OPTION_REMOVED', {
            fieldId: fieldId,
            option: removedOption
        });
        
        return true;
    }
    
    // 更新选项
    updateOption(fieldId, value, updates) {
        const radioField = this.radioFields.get(fieldId);
        if (!radioField) return false;
        
        const option = radioField.props.options.find(opt => opt.value === value);
        if (!option) return false;
        
        Object.assign(option, updates);
        
        this.env.bus.trigger('RADIO_FIELD:OPTION_UPDATED', {
            fieldId: fieldId,
            value: value,
            updates: updates
        });
        
        return true;
    }
    
    // 禁用/启用选项
    toggleOptionDisabled(fieldId, value, disabled = null) {
        const radioField = this.radioFields.get(fieldId);
        if (!radioField) return false;
        
        const option = radioField.props.options.find(opt => opt.value === value);
        if (!option) return false;
        
        option.disabled = disabled !== null ? disabled : !option.disabled;
        
        // 如果禁用的是当前选中项，清除选中状态
        if (option.disabled && radioField.props.value === value) {
            radioField.props.value = null;
        }
        
        this.env.bus.trigger('RADIO_FIELD:OPTION_TOGGLED', {
            fieldId: fieldId,
            value: value,
            disabled: option.disabled
        });
        
        return true;
    }
    
    // 获取字段统计
    getFieldStatistics(fieldId) {
        const radioField = this.radioFields.get(fieldId);
        if (!radioField) return null;
        
        const options = radioField.props.options;
        
        return {
            totalOptions: options.length,
            enabledOptions: options.filter(opt => !opt.disabled).length,
            disabledOptions: options.filter(opt => opt.disabled).length,
            highlightedOptions: options.filter(opt => opt.highlighted).length,
            selectedValue: radioField.props.value,
            fieldType: radioField.type,
            created: radioField.created
        };
    }
    
    // 获取所有字段统计
    getAllFieldsStatistics() {
        const stats = {
            totalFields: this.radioFields.size,
            byType: {},
            totalOptions: 0,
            averageOptions: 0
        };
        
        for (const radioField of this.radioFields.values()) {
            const fieldType = radioField.type;
            stats.byType[fieldType] = (stats.byType[fieldType] || 0) + 1;
            stats.totalOptions += radioField.props.options.length;
        }
        
        stats.averageOptions = stats.totalFields > 0 ? 
            (stats.totalOptions / stats.totalFields).toFixed(2) : 0;
        
        return stats;
    }
    
    // 清理字段
    removeField(fieldId) {
        return this.radioFields.delete(fieldId);
    }
    
    // 清理所有字段
    clearAllFields() {
        this.radioFields.clear();
    }
}

// 使用示例
const radioManager = new SettingsRadioFieldManager(env);

// 创建设置单选字段
const radioField = radioManager.createSettingsRadioField('theme_mode', [
    { value: 'light', label: 'Light Theme', description: 'Use light color scheme' },
    { value: 'dark', label: 'Dark Theme', description: 'Use dark color scheme' },
    { value: 'auto', label: 'Auto', description: 'Follow system preference' }
], 'enhanced');

// 高亮搜索选项
radioManager.highlightSearchOptions(radioField.id, 'dark');

// 设置选中值
radioManager.setSelectedValue(radioField.id, 'dark');

// 获取字段统计
const stats = radioManager.getFieldStatistics(radioField.id);
console.log('Radio field statistics:', stats);
```

## 技术特点

### 1. 继承扩展
- **基类继承**: 继承标准单选字段的所有功能
- **组件扩展**: 扩展父类的组件集合
- **功能保持**: 保持原有字段的基础功能
- **专用增强**: 专门增强设置表单的显示

### 2. 组件集成
- **高亮文本**: 集成HighlightText组件
- **组件合并**: 合并父类和新增组件
- **模板定制**: 使用专门的设置模板
- **功能组合**: 组合多种功能模块

### 3. 模板系统
- **专用模板**: 使用设置专用的模板
- **布局优化**: 优化设置表单的布局
- **样式定制**: 定制设置字段的样式
- **响应式设计**: 支持响应式布局

### 4. 注册机制
- **字段注册**: 注册为专门的设置字段类型
- **类型标识**: 使用"base_settings.radio"标识
- **全局可用**: 在整个应用中可用
- **配置驱动**: 通过配置使用字段

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **功能继承**: 继承基类的所有功能
- **组件扩展**: 扩展组件的功能集合
- **接口保持**: 保持与基类相同的接口

### 2. 组合模式 (Composition Pattern)
- **组件组合**: 组合多个子组件
- **功能集成**: 集成不同的功能模块
- **层次结构**: 构建清晰的组件层次

### 3. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 在基础字段上装饰高亮功能
- **视觉增强**: 增强字段的视觉效果
- **透明扩展**: 对外接口保持透明

### 4. 工厂模式 (Factory Pattern)
- **字段创建**: 创建不同类型的设置字段
- **配置驱动**: 根据配置创建字段
- **类型管理**: 管理不同的字段类型

## 注意事项

1. **组件兼容**: 确保与父类组件的兼容性
2. **模板一致**: 保持模板与组件的一致性
3. **性能考虑**: 避免不必要的组件重渲染
4. **样式协调**: 确保样式与整体界面协调

## 扩展建议

1. **布局选项**: 支持更多的布局选项
2. **交互增强**: 添加更多的交互功能
3. **动画效果**: 支持选项切换的动画效果
4. **验证功能**: 集成字段验证功能
5. **主题支持**: 支持不同的视觉主题

该设置单选字段组件为Odoo Web客户端的设置表单提供了增强的单选字段功能，通过高亮文本集成和专用模板确保了良好的用户体验和视觉效果。

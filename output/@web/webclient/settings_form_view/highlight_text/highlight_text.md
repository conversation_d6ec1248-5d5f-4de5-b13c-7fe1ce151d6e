# HighlightText - 高亮文本组件

## 概述

`highlight_text.js` 是 Odoo Web 客户端的高亮文本组件，提供了文本搜索高亮显示功能。该模块包含30行代码，是一个OWL组件，专门用于在文本中高亮显示搜索关键词，具备正则表达式匹配、文本分割、状态响应等特性，是Odoo Web设置表单视图中文本搜索和高亮显示的核心组件。

## 文件信息
- **路径**: `/web/static/src/webclient/settings_form_view/highlight_text/highlight_text.js`
- **行数**: 30
- **模块**: `@web/webclient/settings_form_view/highlight_text/highlight_text`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/strings'       // 字符串工具
'@odoo/owl'                     // OWL框架
```

## 主组件定义

### 1. HighlightText 组件

```javascript
class HighlightText extends Component {
    static template = "web.HighlightText";
    static highlightClass = "highlighter";
    static props = {
        originalText: String,
    };
    
    setup() {
        this.searchState = useState(this.env.searchState);

        onWillRender(() => {
            const splitText = this.props.originalText.split(
                new RegExp(`(${escapeRegExp(this.searchState.value)})`, "ig")
            );
            this.splitText =
                this.searchState.value.length && splitText.length > 1
                    ? splitText
                    : [this.props.originalText];
        });
    }
}
```

**组件特性**:
- **文本高亮**: 高亮显示搜索匹配的文本
- **正则匹配**: 使用正则表达式进行文本匹配
- **状态响应**: 响应搜索状态的变化
- **动态分割**: 动态分割文本以实现高亮

## 核心属性

### 1. 组件属性

```javascript
// 模板配置
static template = "web.HighlightText";

// 高亮CSS类
static highlightClass = "highlighter";

// 属性定义
static props = {
    originalText: String,    // 原始文本
};
```

**属性功能**:
- **template**: 指定高亮文本的模板
- **highlightClass**: 定义高亮显示的CSS类
- **props**: 定义原始文本属性

### 2. 状态属性

```javascript
// 搜索状态
this.searchState = useState(this.env.searchState);

// 分割文本
this.splitText = [...];
```

**状态属性功能**:
- **searchState**: 响应式的搜索状态
- **splitText**: 分割后的文本数组

## 核心功能

### 1. 搜索状态管理

```javascript
this.searchState = useState(this.env.searchState);
```

**状态管理功能**:
- **响应式状态**: 使用useState创建响应式状态
- **环境集成**: 从环境中获取搜索状态
- **自动更新**: 状态变化时自动更新组件
- **状态共享**: 与其他组件共享搜索状态

### 2. 文本分割处理

```javascript
onWillRender(() => {
    const splitText = this.props.originalText.split(
        new RegExp(`(${escapeRegExp(this.searchState.value)})`, "ig")
    );
    this.splitText =
        this.searchState.value.length && splitText.length > 1
            ? splitText
            : [this.props.originalText];
});
```

**分割处理功能**:
- **渲染前处理**: 在组件渲染前处理文本
- **正则分割**: 使用正则表达式分割文本
- **转义处理**: 转义特殊字符避免正则错误
- **条件分割**: 仅在有搜索值时进行分割

### 3. 正则表达式构建

```javascript
new RegExp(`(${escapeRegExp(this.searchState.value)})`, "ig")
```

**正则构建功能**:
- **动态构建**: 根据搜索值动态构建正则
- **字符转义**: 转义特殊正则字符
- **捕获组**: 使用捕获组保留匹配文本
- **忽略大小写**: 使用"i"标志忽略大小写
- **全局匹配**: 使用"g"标志进行全局匹配

## 使用场景

### 1. 搜索高亮管理器

```javascript
// 搜索高亮管理器
class SearchHighlightManager {
    constructor(env) {
        this.env = env;
        this.highlightInstances = new Map();
        this.searchHistory = [];
        this.highlightConfig = this.getDefaultConfig();
        this.setupManager();
    }
    
    setupManager() {
        // 设置高亮配置
        this.highlightStyles = {
            default: {
                backgroundColor: '#ffeb3b',
                color: '#000',
                fontWeight: 'bold',
                padding: '1px 2px',
                borderRadius: '2px'
            },
            primary: {
                backgroundColor: '#2196f3',
                color: '#fff',
                fontWeight: 'bold',
                padding: '1px 2px',
                borderRadius: '2px'
            },
            success: {
                backgroundColor: '#4caf50',
                color: '#fff',
                fontWeight: 'bold',
                padding: '1px 2px',
                borderRadius: '2px'
            },
            warning: {
                backgroundColor: '#ff9800',
                color: '#fff',
                fontWeight: 'bold',
                padding: '1px 2px',
                borderRadius: '2px'
            },
            danger: {
                backgroundColor: '#f44336',
                color: '#fff',
                fontWeight: 'bold',
                padding: '1px 2px',
                borderRadius: '2px'
            }
        };
        
        this.currentStyle = 'default';
    }
    
    // 获取默认配置
    getDefaultConfig() {
        return {
            caseSensitive: false,
            wholeWord: false,
            useRegex: false,
            highlightAll: true,
            maxHighlights: 100,
            animateHighlight: true,
            persistHighlight: false
        };
    }
    
    // 创建高亮文本组件
    createHighlightText(originalText, searchValue, options = {}) {
        const config = { ...this.highlightConfig, ...options };
        
        const highlightId = this.generateHighlightId(originalText, searchValue);
        
        if (this.highlightInstances.has(highlightId)) {
            return this.highlightInstances.get(highlightId);
        }
        
        const highlightComponent = {
            Component: HighlightText,
            props: {
                originalText: originalText,
                searchValue: searchValue,
                highlightClass: this.getHighlightClass(config.style),
                config: config
            },
            id: highlightId,
            created: Date.now()
        };
        
        this.highlightInstances.set(highlightId, highlightComponent);
        
        return highlightComponent;
    }
    
    // 生成高亮ID
    generateHighlightId(text, searchValue) {
        const content = `${text}_${searchValue}_${this.currentStyle}`;
        return btoa(content).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
    }
    
    // 获取高亮CSS类
    getHighlightClass(style = 'default') {
        return `highlighter highlighter-${style}`;
    }
    
    // 高级文本分割
    advancedTextSplit(text, searchValue, config) {
        if (!searchValue || !text) {
            return [text];
        }
        
        let regex;
        
        if (config.useRegex) {
            try {
                regex = new RegExp(`(${searchValue})`, config.caseSensitive ? 'g' : 'ig');
            } catch (error) {
                console.warn('Invalid regex pattern:', searchValue);
                return [text];
            }
        } else {
            const escapedValue = escapeRegExp(searchValue);
            const pattern = config.wholeWord ? `\\b(${escapedValue})\\b` : `(${escapedValue})`;
            regex = new RegExp(pattern, config.caseSensitive ? 'g' : 'ig');
        }
        
        const splitText = text.split(regex);
        
        // 限制高亮数量
        if (config.maxHighlights && splitText.length > config.maxHighlights * 2) {
            const limitedSplit = splitText.slice(0, config.maxHighlights * 2);
            limitedSplit.push('...');
            return limitedSplit;
        }
        
        return splitText.length > 1 ? splitText : [text];
    }
    
    // 批量创建高亮
    createBatchHighlights(textItems, searchValue, options = {}) {
        const results = [];
        
        textItems.forEach((item, index) => {
            const text = typeof item === 'string' ? item : item.text;
            const itemOptions = typeof item === 'object' ? { ...options, ...item.options } : options;
            
            const highlight = this.createHighlightText(text, searchValue, {
                ...itemOptions,
                delay: (itemOptions.delay || 0) + (index * 50)
            });
            
            results.push(highlight);
        });
        
        return results;
    }
    
    // 搜索文本匹配
    searchTextMatches(text, searchValue, config = {}) {
        const mergedConfig = { ...this.highlightConfig, ...config };
        const splitText = this.advancedTextSplit(text, searchValue, mergedConfig);
        
        const matches = [];
        let position = 0;
        
        splitText.forEach((segment, index) => {
            const isMatch = index % 2 === 1; // 奇数索引是匹配的文本
            
            if (isMatch) {
                matches.push({
                    text: segment,
                    start: position,
                    end: position + segment.length,
                    index: Math.floor(index / 2)
                });
            }
            
            position += segment.length;
        });
        
        return {
            matches: matches,
            total: matches.length,
            splitText: splitText,
            originalText: text
        };
    }
    
    // 设置高亮样式
    setHighlightStyle(styleName) {
        if (this.highlightStyles[styleName]) {
            this.currentStyle = styleName;
            this.highlightInstances.clear(); // 清除缓存以应用新样式
        }
    }
    
    // 更新搜索配置
    updateConfig(newConfig) {
        this.highlightConfig = { ...this.highlightConfig, ...newConfig };
        this.highlightInstances.clear(); // 清除缓存以应用新配置
    }
    
    // 记录搜索历史
    recordSearch(searchValue) {
        if (!searchValue || searchValue.length < 2) return;
        
        // 移除重复项
        const existingIndex = this.searchHistory.indexOf(searchValue);
        if (existingIndex > -1) {
            this.searchHistory.splice(existingIndex, 1);
        }
        
        // 添加到历史开头
        this.searchHistory.unshift(searchValue);
        
        // 限制历史长度
        this.searchHistory = this.searchHistory.slice(0, 20);
    }
    
    // 获取搜索建议
    getSearchSuggestions(partialValue) {
        if (!partialValue || partialValue.length < 1) {
            return this.searchHistory.slice(0, 5);
        }
        
        return this.searchHistory.filter(item =>
            item.toLowerCase().includes(partialValue.toLowerCase())
        ).slice(0, 5);
    }
    
    // 清除高亮缓存
    clearHighlightCache() {
        this.highlightInstances.clear();
    }
    
    // 获取高亮统计
    getHighlightStatistics() {
        return {
            totalInstances: this.highlightInstances.size,
            currentStyle: this.currentStyle,
            searchHistorySize: this.searchHistory.length,
            config: this.highlightConfig,
            cacheSize: this.highlightInstances.size
        };
    }
    
    // 导出高亮数据
    exportHighlightData() {
        const data = {
            instances: Array.from(this.highlightInstances.entries()),
            history: this.searchHistory,
            config: this.highlightConfig,
            style: this.currentStyle,
            timestamp: Date.now()
        };
        
        return JSON.stringify(data, null, 2);
    }
    
    // 导入高亮数据
    importHighlightData(dataJson) {
        try {
            const data = JSON.parse(dataJson);
            
            if (data.config) {
                this.highlightConfig = data.config;
            }
            
            if (data.history && Array.isArray(data.history)) {
                this.searchHistory = data.history;
            }
            
            if (data.style && this.highlightStyles[data.style]) {
                this.currentStyle = data.style;
            }
            
            if (data.instances && Array.isArray(data.instances)) {
                this.highlightInstances.clear();
                data.instances.forEach(([key, value]) => {
                    this.highlightInstances.set(key, value);
                });
            }
            
            return true;
        } catch (error) {
            console.error('Failed to import highlight data:', error);
            return false;
        }
    }
}

// 高亮文本工具类
class HighlightTextUtils {
    // 转义正则表达式特殊字符
    static escapeRegExp(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
    
    // 计算文本相似度
    static calculateSimilarity(text1, text2) {
        const longer = text1.length > text2.length ? text1 : text2;
        const shorter = text1.length > text2.length ? text2 : text1;
        
        if (longer.length === 0) {
            return 1.0;
        }
        
        const distance = this.levenshteinDistance(longer, shorter);
        return (longer.length - distance) / longer.length;
    }
    
    // 计算编辑距离
    static levenshteinDistance(str1, str2) {
        const matrix = [];
        
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        
        return matrix[str2.length][str1.length];
    }
    
    // 高亮文本预处理
    static preprocessText(text, options = {}) {
        let processed = text;
        
        if (options.trimWhitespace) {
            processed = processed.trim();
        }
        
        if (options.normalizeSpaces) {
            processed = processed.replace(/\s+/g, ' ');
        }
        
        if (options.removeSpecialChars) {
            processed = processed.replace(/[^\w\s]/g, '');
        }
        
        if (options.toLowerCase) {
            processed = processed.toLowerCase();
        }
        
        return processed;
    }
    
    // 查找最佳匹配
    static findBestMatch(searchValue, textArray, threshold = 0.6) {
        const matches = textArray.map((text, index) => ({
            text: text,
            index: index,
            similarity: this.calculateSimilarity(searchValue.toLowerCase(), text.toLowerCase())
        }));
        
        return matches
            .filter(match => match.similarity >= threshold)
            .sort((a, b) => b.similarity - a.similarity);
    }
}

// 使用示例
const highlightManager = new SearchHighlightManager(env);

// 创建高亮文本
const highlight = highlightManager.createHighlightText(
    'This is a sample text for highlighting',
    'sample'
);

// 批量创建高亮
const batchHighlights = highlightManager.createBatchHighlights([
    'First text to highlight',
    'Second text with keywords',
    'Third text for testing'
], 'text');

// 搜索文本匹配
const matches = highlightManager.searchTextMatches(
    'Hello world, this is a test world',
    'world'
);

console.log('Search matches:', matches);

// 设置高亮样式
highlightManager.setHighlightStyle('primary');

// 获取统计信息
const stats = highlightManager.getHighlightStatistics();
console.log('Highlight statistics:', stats);
```

## 技术特点

### 1. 响应式状态
- **状态管理**: 使用useState管理搜索状态
- **自动更新**: 状态变化时自动重新渲染
- **环境集成**: 与环境中的搜索状态集成
- **状态共享**: 与其他组件共享搜索状态

### 2. 文本处理
- **正则分割**: 使用正则表达式分割文本
- **字符转义**: 安全转义特殊正则字符
- **条件处理**: 仅在有搜索值时处理文本
- **性能优化**: 避免不必要的文本处理

### 3. 渲染优化
- **渲染前处理**: 在渲染前预处理文本
- **条件渲染**: 根据搜索状态条件渲染
- **缓存机制**: 缓存分割后的文本结果
- **最小重渲染**: 最小化不必要的重新渲染

### 4. 搜索功能
- **忽略大小写**: 默认忽略大小写搜索
- **全局匹配**: 支持全局文本匹配
- **捕获组**: 使用捕获组保留匹配文本
- **灵活匹配**: 支持复杂的搜索模式

## 设计模式

### 1. 组件模式 (Component Pattern)
- **封装性**: 将高亮逻辑封装在组件中
- **可重用**: 可在不同场景中重用
- **配置化**: 通过属性配置组件行为

### 2. 观察者模式 (Observer Pattern)
- **状态监听**: 监听搜索状态的变化
- **自动响应**: 状态变化时自动响应
- **事件驱动**: 基于事件的更新机制

### 3. 策略模式 (Strategy Pattern)
- **文本处理策略**: 不同的文本处理策略
- **高亮策略**: 不同的高亮显示策略
- **搜索策略**: 不同的搜索匹配策略

### 4. 模板方法模式 (Template Method Pattern)
- **渲染模板**: 定义高亮文本的渲染模板
- **处理流程**: 标准化的文本处理流程
- **扩展点**: 提供明确的扩展点

## 注意事项

1. **正则安全**: 确保正则表达式的安全性
2. **性能考虑**: 避免频繁的文本分割操作
3. **内存管理**: 及时清理不需要的文本缓存
4. **用户体验**: 提供流畅的高亮显示效果

## 扩展建议

1. **高亮样式**: 支持更多的高亮样式选项
2. **搜索选项**: 添加更多的搜索配置选项
3. **性能优化**: 优化大文本的高亮处理
4. **动画效果**: 添加高亮显示的动画效果
5. **导出功能**: 支持高亮结果的导出功能

该高亮文本组件为Odoo Web客户端提供了强大的文本搜索高亮功能，通过响应式状态管理和智能的文本处理确保了高效的搜索体验和视觉效果。

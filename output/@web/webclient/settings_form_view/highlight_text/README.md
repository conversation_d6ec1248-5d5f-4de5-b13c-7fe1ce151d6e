# Highlight Text - 高亮文本系统

## 概述

Highlight Text 是 Odoo Web 客户端的高亮文本系统，提供了设置表单中的文本搜索高亮和视觉增强功能。该系统包含3个核心模块，总计约76行代码，专门为设置表单设计，具备文本搜索高亮、标签增强显示、字段组件集成等特性，是Odoo Web设置表单视图中文本处理和用户体验优化的重要组件。

## 目录结构

```
highlight_text/
├── highlight_text.js                           # 高亮文本组件 (30行)
├── form_label_highlight_text.js                # 表单标签高亮文本 (21行)
├── settings_radio_field.js                     # 设置单选字段 (21行)
└── README.md                                    # 本文档
```

## 核心架构

### 1. 高亮文本系统层次结构

```
高亮文本系统 (Highlight Text System)
├── 核心高亮层 (Core Highlight Layer)
│   ├── HighlightText (高亮文本组件)
│   ├── 文本分割算法 (Text Splitting Algorithm)
│   └── 搜索状态管理 (Search State Management)
├── 标签增强层 (Label Enhancement Layer)
│   ├── FormLabelHighlightText (表单标签高亮文本)
│   ├── 企业版检测 (Enterprise Detection)
│   └── 升级提示集成 (Upgrade Hint Integration)
├── 字段集成层 (Field Integration Layer)
│   ├── SettingsRadioField (设置单选字段)
│   ├── 组件扩展 (Component Extension)
│   └── 模板定制 (Template Customization)
└── 用户交互层 (User Interaction Layer)
    ├── 搜索响应 (Search Response)
    ├── 视觉反馈 (Visual Feedback)
    └── 状态同步 (State Synchronization)
```

**系统特性**:
- **搜索驱动**: 基于搜索状态的文本高亮
- **组件集成**: 与各种表单组件深度集成
- **视觉增强**: 提供丰富的视觉增强效果
- **响应式**: 实时响应搜索状态变化

### 2. 高亮处理流程

```javascript
// 高亮处理流程
const highlightProcessFlow = {
    // 1. 搜索状态获取
    stateAcquisition: {
        source: 'env.searchState',
        method: 'useState',
        reactivity: 'automatic'
    },
    
    // 2. 文本预处理
    textPreprocessing: {
        escaping: 'escapeRegExp for special characters',
        validation: 'search value length check',
        normalization: 'case insensitive processing'
    },
    
    // 3. 文本分割
    textSplitting: {
        algorithm: 'Regular expression based splitting',
        pattern: 'Capture group for matched text',
        flags: 'Global and case insensitive'
    },
    
    // 4. 渲染处理
    renderProcessing: {
        timing: 'onWillRender hook',
        condition: 'Search value existence check',
        output: 'Split text array for template'
    }
};
```

## 核心组件

### 1. HighlightText - 高亮文本组件

**功能**: 文本搜索高亮显示的核心组件
- **行数**: 30行
- **作用**: 实现文本中搜索关键词的高亮显示
- **特点**: 正则匹配、状态响应、动态分割

**核心功能**:
```javascript
// 搜索状态管理
this.searchState = useState(this.env.searchState);

// 文本分割处理
onWillRender(() => {
    const splitText = this.props.originalText.split(
        new RegExp(`(${escapeRegExp(this.searchState.value)})`, "ig")
    );
    this.splitText = this.searchState.value.length && splitText.length > 1
        ? splitText
        : [this.props.originalText];
});
```

**技术特点**:
- **响应式状态**: 使用useState管理搜索状态
- **正则分割**: 使用正则表达式安全分割文本
- **渲染优化**: 在渲染前预处理文本数据
- **条件处理**: 仅在有搜索值时进行高亮处理

### 2. FormLabelHighlightText - 表单标签高亮文本

**功能**: 带有高亮功能的表单标签组件
- **行数**: 21行
- **作用**: 为表单标签添加高亮文本和企业版提示
- **特点**: 版本检测、标签增强、升级提示

**核心功能**:
```javascript
// 版本检测
const isEnterprise = odoo.info && odoo.info.isEnterprise;

// 升级提示设置
if (this.props.fieldInfo?.field === upgradeBooleanField && !isEnterprise) {
    this.upgradeEnterprise = true;
}
```

**技术特点**:
- **继承扩展**: 继承标准表单标签功能
- **智能检测**: 自动检测Odoo版本类型
- **条件显示**: 根据字段类型和版本显示提示
- **组件集成**: 集成HighlightText子组件

### 3. SettingsRadioField - 设置单选字段

**功能**: 带有高亮文本功能的设置单选字段
- **行数**: 21行
- **作用**: 为设置表单提供增强的单选字段
- **特点**: 组件扩展、模板定制、高亮集成

**核心功能**:
```javascript
// 组件扩展
static components = { ...super.components, HighlightText };

// 专用模板
static template = "web.SettingsRadioField";
```

**技术特点**:
- **组件合并**: 合并父类组件和高亮文本组件
- **模板定制**: 使用设置专用的模板
- **功能保持**: 保持原有单选字段的所有功能
- **视觉增强**: 增强设置表单的视觉效果

## 技术特点

### 1. 搜索高亮算法

```javascript
// 搜索高亮算法实现
const searchHighlightAlgorithm = {
    // 文本分割
    textSplitting: {
        method: 'String.prototype.split with RegExp',
        pattern: 'Capture group pattern: (searchValue)',
        flags: 'ig (case insensitive, global)',
        escaping: 'escapeRegExp for special characters'
    },
    
    // 匹配策略
    matchingStrategy: {
        type: 'Substring matching',
        caseSensitive: false,
        wholeWord: false,
        regex: 'Dynamic regex construction'
    },
    
    // 结果处理
    resultProcessing: {
        structure: 'Array of text segments',
        highlighting: 'Odd indices are matches',
        fallback: 'Original text if no matches'
    }
};
```

### 2. 状态管理机制

```javascript
// 状态管理机制
const stateManagement = {
    // 响应式状态
    reactiveState: {
        hook: 'useState',
        source: 'env.searchState',
        scope: 'component instance'
    },
    
    // 状态同步
    synchronization: {
        trigger: 'State change',
        response: 'Automatic re-render',
        timing: 'onWillRender'
    },
    
    // 生命周期
    lifecycle: {
        setup: 'Component initialization',
        update: 'State change response',
        cleanup: 'Automatic cleanup'
    }
};
```

### 3. 组件集成架构

```javascript
// 组件集成架构
const componentIntegration = {
    // 继承模式
    inheritance: {
        base: 'Standard form components',
        extension: 'Highlight functionality',
        compatibility: 'Full backward compatibility'
    },
    
    // 组合模式
    composition: {
        components: 'Parent + HighlightText',
        merging: 'Spread operator for components',
        isolation: 'Component encapsulation'
    },
    
    // 模板系统
    templateSystem: {
        customization: 'Settings-specific templates',
        inheritance: 'Template hierarchy',
        rendering: 'OWL template engine'
    }
};
```

### 4. 性能优化策略

```javascript
// 性能优化策略
const performanceOptimization = {
    // 渲染优化
    renderOptimization: {
        timing: 'onWillRender hook',
        condition: 'Search value existence check',
        caching: 'Component-level caching'
    },
    
    // 文本处理优化
    textProcessing: {
        escaping: 'Efficient regex escaping',
        splitting: 'Single-pass text splitting',
        validation: 'Early exit for empty searches'
    },
    
    // 状态优化
    stateOptimization: {
        reactivity: 'Minimal re-renders',
        scope: 'Component-scoped state',
        cleanup: 'Automatic state cleanup'
    }
};
```

## 使用场景

### 1. 设置表单搜索增强

```javascript
// 设置表单搜索增强示例
class SettingsFormSearchEnhancer {
    constructor(env) {
        this.env = env;
        this.searchState = env.searchState;
        this.highlightComponents = new Map();
        this.setupEnhancer();
    }
    
    setupEnhancer() {
        // 设置搜索配置
        this.searchConfig = {
            minSearchLength: 2,
            highlightDelay: 300,
            maxHighlights: 50,
            caseSensitive: false,
            wholeWord: false
        };
        
        // 设置高亮样式
        this.highlightStyles = {
            default: 'bg-warning text-dark',
            primary: 'bg-primary text-white',
            success: 'bg-success text-white',
            info: 'bg-info text-white'
        };
        
        this.currentStyle = 'default';
    }
    
    // 增强表单字段
    enhanceFormField(fieldElement, fieldType) {
        const enhancement = {
            element: fieldElement,
            type: fieldType,
            highlightEnabled: this.shouldEnableHighlight(fieldType),
            searchable: this.isSearchableField(fieldType)
        };
        
        if (enhancement.highlightEnabled) {
            this.addHighlightCapability(enhancement);
        }
        
        if (enhancement.searchable) {
            this.addSearchCapability(enhancement);
        }
        
        return enhancement;
    }
    
    // 判断是否启用高亮
    shouldEnableHighlight(fieldType) {
        const highlightableTypes = [
            'char', 'text', 'html', 'selection', 
            'radio', 'many2one', 'reference'
        ];
        
        return highlightableTypes.includes(fieldType);
    }
    
    // 判断是否可搜索
    isSearchableField(fieldType) {
        const searchableTypes = [
            'char', 'text', 'html', 'selection',
            'many2one', 'many2many', 'one2many'
        ];
        
        return searchableTypes.includes(fieldType);
    }
    
    // 添加高亮能力
    addHighlightCapability(enhancement) {
        const textElements = this.findTextElements(enhancement.element);
        
        textElements.forEach(textElement => {
            const highlightComponent = this.createHighlightComponent(textElement);
            this.highlightComponents.set(textElement, highlightComponent);
        });
    }
    
    // 查找文本元素
    findTextElements(element) {
        const textElements = [];
        
        // 查找标签文本
        const labels = element.querySelectorAll('label, .o_form_label');
        labels.forEach(label => {
            if (label.textContent.trim()) {
                textElements.push(label);
            }
        });
        
        // 查找选项文本
        const options = element.querySelectorAll('.o_radio_item, .o_selection_item');
        options.forEach(option => {
            if (option.textContent.trim()) {
                textElements.push(option);
            }
        });
        
        // 查找帮助文本
        const helpTexts = element.querySelectorAll('.o_form_help, .text-muted');
        helpTexts.forEach(help => {
            if (help.textContent.trim()) {
                textElements.push(help);
            }
        });
        
        return textElements;
    }
    
    // 创建高亮组件
    createHighlightComponent(textElement) {
        const originalText = textElement.textContent;
        
        return {
            Component: HighlightText,
            props: {
                originalText: originalText,
                highlightClass: this.getHighlightClass()
            },
            element: textElement,
            originalText: originalText
        };
    }
    
    // 获取高亮CSS类
    getHighlightClass() {
        return `highlighter ${this.highlightStyles[this.currentStyle]}`;
    }
    
    // 添加搜索能力
    addSearchCapability(enhancement) {
        const searchableElements = this.findSearchableElements(enhancement.element);
        
        searchableElements.forEach(element => {
            this.addSearchListener(element);
        });
    }
    
    // 查找可搜索元素
    findSearchableElements(element) {
        return element.querySelectorAll('input, select, textarea');
    }
    
    // 添加搜索监听器
    addSearchListener(element) {
        element.addEventListener('input', (event) => {
            this.handleSearchInput(event.target.value);
        });
        
        element.addEventListener('focus', () => {
            this.handleSearchFocus();
        });
        
        element.addEventListener('blur', () => {
            this.handleSearchBlur();
        });
    }
    
    // 处理搜索输入
    handleSearchInput(searchValue) {
        if (searchValue.length >= this.searchConfig.minSearchLength) {
            this.updateSearchState(searchValue);
        } else {
            this.clearSearch();
        }
    }
    
    // 更新搜索状态
    updateSearchState(searchValue) {
        if (this.searchState) {
            this.searchState.value = searchValue;
        }
        
        // 触发高亮更新
        this.updateHighlights(searchValue);
    }
    
    // 更新高亮显示
    updateHighlights(searchValue) {
        for (const [element, component] of this.highlightComponents) {
            this.updateElementHighlight(element, component, searchValue);
        }
    }
    
    // 更新元素高亮
    updateElementHighlight(element, component, searchValue) {
        const splitText = this.splitTextForHighlight(
            component.originalText, 
            searchValue
        );
        
        // 重新渲染元素内容
        this.renderHighlightedText(element, splitText);
    }
    
    // 分割文本用于高亮
    splitTextForHighlight(text, searchValue) {
        if (!searchValue || !text) {
            return [text];
        }
        
        const escapedValue = this.escapeRegExp(searchValue);
        const regex = new RegExp(`(${escapedValue})`, 'ig');
        const splitText = text.split(regex);
        
        return splitText.length > 1 ? splitText : [text];
    }
    
    // 渲染高亮文本
    renderHighlightedText(element, splitText) {
        element.innerHTML = '';
        
        splitText.forEach((segment, index) => {
            const isMatch = index % 2 === 1;
            
            if (isMatch) {
                const highlightSpan = document.createElement('span');
                highlightSpan.className = this.getHighlightClass();
                highlightSpan.textContent = segment;
                element.appendChild(highlightSpan);
            } else {
                const textNode = document.createTextNode(segment);
                element.appendChild(textNode);
            }
        });
    }
    
    // 转义正则表达式
    escapeRegExp(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
    
    // 清除搜索
    clearSearch() {
        if (this.searchState) {
            this.searchState.value = '';
        }
        
        this.clearAllHighlights();
    }
    
    // 清除所有高亮
    clearAllHighlights() {
        for (const [element, component] of this.highlightComponents) {
            element.textContent = component.originalText;
        }
    }
    
    // 处理搜索焦点
    handleSearchFocus() {
        this.env.bus.trigger('SEARCH:FOCUS');
    }
    
    // 处理搜索失焦
    handleSearchBlur() {
        this.env.bus.trigger('SEARCH:BLUR');
    }
    
    // 设置高亮样式
    setHighlightStyle(styleName) {
        if (this.highlightStyles[styleName]) {
            this.currentStyle = styleName;
            
            // 重新应用当前搜索
            if (this.searchState && this.searchState.value) {
                this.updateHighlights(this.searchState.value);
            }
        }
    }
    
    // 获取搜索统计
    getSearchStatistics() {
        return {
            highlightComponents: this.highlightComponents.size,
            currentSearch: this.searchState ? this.searchState.value : '',
            currentStyle: this.currentStyle,
            config: this.searchConfig
        };
    }
}

// 使用示例
const searchEnhancer = new SettingsFormSearchEnhancer(env);

// 增强表单字段
const enhancement = searchEnhancer.enhanceFormField(fieldElement, 'radio');

// 设置高亮样式
searchEnhancer.setHighlightStyle('primary');

// 获取统计信息
const stats = searchEnhancer.getSearchStatistics();
console.log('Search statistics:', stats);
```

### 2. 高亮文本主题管理器

```javascript
// 高亮文本主题管理器
class HighlightThemeManager {
    constructor() {
        this.themes = this.initializeThemes();
        this.currentTheme = 'default';
        this.customThemes = new Map();
    }
    
    // 初始化主题
    initializeThemes() {
        return {
            default: {
                name: 'Default',
                highlight: {
                    backgroundColor: '#ffeb3b',
                    color: '#000',
                    fontWeight: 'bold',
                    padding: '1px 2px',
                    borderRadius: '2px'
                },
                enterprise: {
                    backgroundColor: '#875A7B',
                    color: '#fff',
                    fontWeight: 'bold',
                    padding: '2px 4px',
                    borderRadius: '3px'
                }
            },
            dark: {
                name: 'Dark',
                highlight: {
                    backgroundColor: '#ffc107',
                    color: '#000',
                    fontWeight: 'bold',
                    padding: '1px 2px',
                    borderRadius: '2px'
                },
                enterprise: {
                    backgroundColor: '#B794A8',
                    color: '#000',
                    fontWeight: 'bold',
                    padding: '2px 4px',
                    borderRadius: '3px'
                }
            },
            colorblind: {
                name: 'Colorblind Friendly',
                highlight: {
                    backgroundColor: '#0066cc',
                    color: '#fff',
                    fontWeight: 'bold',
                    padding: '1px 2px',
                    borderRadius: '2px',
                    textDecoration: 'underline'
                },
                enterprise: {
                    backgroundColor: '#009900',
                    color: '#fff',
                    fontWeight: 'bold',
                    padding: '2px 4px',
                    borderRadius: '3px',
                    border: '1px solid #fff'
                }
            }
        };
    }
    
    // 获取当前主题
    getCurrentTheme() {
        return this.themes[this.currentTheme] || this.themes.default;
    }
    
    // 设置主题
    setTheme(themeName) {
        if (this.themes[themeName] || this.customThemes.has(themeName)) {
            this.currentTheme = themeName;
            this.applyTheme();
            return true;
        }
        return false;
    }
    
    // 应用主题
    applyTheme() {
        const theme = this.getCurrentTheme();
        this.updateCSSVariables(theme);
        this.notifyThemeChange();
    }
    
    // 更新CSS变量
    updateCSSVariables(theme) {
        const root = document.documentElement;
        
        // 设置高亮样式变量
        Object.entries(theme.highlight).forEach(([property, value]) => {
            const cssProperty = `--highlight-${property.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
            root.style.setProperty(cssProperty, value);
        });
        
        // 设置企业版样式变量
        Object.entries(theme.enterprise).forEach(([property, value]) => {
            const cssProperty = `--enterprise-${property.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
            root.style.setProperty(cssProperty, value);
        });
    }
    
    // 通知主题变更
    notifyThemeChange() {
        document.dispatchEvent(new CustomEvent('highlight-theme-changed', {
            detail: {
                theme: this.currentTheme,
                config: this.getCurrentTheme()
            }
        }));
    }
    
    // 创建自定义主题
    createCustomTheme(name, config) {
        this.customThemes.set(name, {
            name: name,
            ...config
        });
    }
    
    // 获取所有主题
    getAllThemes() {
        const allThemes = { ...this.themes };
        
        for (const [name, theme] of this.customThemes) {
            allThemes[name] = theme;
        }
        
        return allThemes;
    }
}

// 使用示例
const themeManager = new HighlightThemeManager();

// 设置主题
themeManager.setTheme('dark');

// 创建自定义主题
themeManager.createCustomTheme('custom', {
    highlight: {
        backgroundColor: '#ff6b6b',
        color: '#fff',
        fontWeight: 'bold'
    },
    enterprise: {
        backgroundColor: '#4ecdc4',
        color: '#fff',
        fontWeight: 'bold'
    }
});
```

## 设计模式

### 1. 组件模式 (Component Pattern)
- **封装性**: 将高亮逻辑封装在独立组件中
- **可重用**: 可在不同场景中重用组件
- **组合性**: 支持组件的组合和嵌套

### 2. 观察者模式 (Observer Pattern)
- **状态监听**: 监听搜索状态的变化
- **自动响应**: 状态变化时自动更新显示
- **事件驱动**: 基于事件的更新机制

### 3. 策略模式 (Strategy Pattern)
- **高亮策略**: 不同的文本高亮策略
- **搜索策略**: 不同的搜索匹配策略
- **渲染策略**: 不同的渲染处理策略

### 4. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 在基础组件上装饰高亮功能
- **视觉增强**: 增强组件的视觉效果
- **透明扩展**: 对外接口保持透明

### 5. 工厂模式 (Factory Pattern)
- **组件创建**: 创建不同类型的高亮组件
- **配置驱动**: 根据配置创建组件
- **类型管理**: 管理不同的组件类型

## 性能优化

### 1. 渲染优化
```javascript
// 渲染优化策略
const renderOptimization = {
    // 渲染时机
    timing: {
        hook: 'onWillRender',
        condition: 'Search value existence',
        frequency: 'State change only'
    },
    
    // 文本处理
    textProcessing: {
        caching: 'Component-level caching',
        validation: 'Early exit for empty searches',
        splitting: 'Single-pass processing'
    }
};
```

### 2. 状态优化
```javascript
// 状态优化策略
const stateOptimization = {
    // 状态管理
    stateManagement: {
        scope: 'Component-scoped',
        reactivity: 'Minimal re-renders',
        cleanup: 'Automatic cleanup'
    },
    
    // 内存管理
    memoryManagement: {
        references: 'Weak references where possible',
        cleanup: 'Component unmount cleanup',
        caching: 'LRU cache for frequent searches'
    }
};
```

## 最佳实践

### 1. 高亮文本最佳实践
```javascript
// 高亮文本最佳实践
const highlightBestPractices = {
    // 性能
    performance: [
        '避免频繁的文本分割操作',
        '使用条件渲染减少不必要的处理',
        '缓存分割结果避免重复计算',
        '限制高亮文本的长度和数量'
    ],
    
    // 用户体验
    userExperience: [
        '提供清晰的视觉对比',
        '支持键盘导航',
        '保持高亮的一致性',
        '提供搜索状态反馈'
    ]
};
```

### 2. 组件集成最佳实践
```javascript
// 组件集成最佳实践
const integrationBestPractices = {
    // 组件设计
    componentDesign: [
        '保持组件的单一职责',
        '提供清晰的属性接口',
        '支持组件的组合使用',
        '确保向后兼容性'
    ],
    
    // 状态管理
    stateManagement: [
        '使用响应式状态管理',
        '避免状态的直接修改',
        '提供状态变化的通知',
        '确保状态的一致性'
    ]
};
```

## 注意事项

1. **正则安全**: 确保正则表达式的安全性和性能
2. **状态同步**: 保持搜索状态与显示状态的同步
3. **内存管理**: 及时清理不需要的组件和状态
4. **用户体验**: 提供流畅的搜索和高亮体验
5. **兼容性**: 确保与现有组件的兼容性

## 总结

Highlight Text 高亮文本系统是 Odoo Web 客户端中专门为设置表单设计的文本处理和视觉增强系统，通过核心高亮组件、标签增强和字段集成的有机结合，为用户提供了强大的文本搜索高亮和视觉反馈功能。

**核心优势**:
- **搜索驱动**: 基于搜索状态的实时文本高亮
- **组件集成**: 与各种表单组件的深度集成
- **视觉增强**: 丰富的视觉增强和反馈效果
- **响应式**: 实时响应搜索状态的变化
- **可扩展**: 灵活的扩展和定制机制
- **性能优化**: 高效的文本处理和渲染优化

该系统通过高亮文本组件、表单标签高亮文本和设置单选字段的协同工作，为Odoo Web设置表单提供了专业的文本处理解决方案，确保了搜索体验和视觉效果的双重优化。

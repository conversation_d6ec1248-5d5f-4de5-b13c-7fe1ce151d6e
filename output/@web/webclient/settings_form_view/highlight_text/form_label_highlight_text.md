# FormLabelHighlightText - 表单标签高亮文本

## 概述

`form_label_highlight_text.js` 是 Odoo Web 客户端的表单标签高亮文本组件，提供了设置表单中带有高亮功能的标签显示。该模块包含21行代码，继承自标准表单标签，专门用于在设置表单中显示带有高亮文本的标签，具备企业版检测、高亮文本集成、升级提示等特性，是Odoo Web设置表单视图中标签增强显示的重要组件。

## 文件信息
- **路径**: `/web/static/src/webclient/settings_form_view/highlight_text/form_label_highlight_text.js`
- **行数**: 21
- **模块**: `@web/webclient/settings_form_view/highlight_text/form_label_highlight_text`

## 依赖关系

```javascript
// 核心依赖
'@web/views/form/form_label'                                                    // 表单标签基类
'@web/webclient/settings_form_view/highlight_text/highlight_text'               // 高亮文本组件
'@web/webclient/settings_form_view/fields/upgrade_boolean_field'                // 升级布尔字段
```

## 主组件定义

### 1. FormLabelHighlightText 组件

```javascript
class FormLabelHighlightText extends FormLabel {
    static template = "web.FormLabelHighlightText";
    static components = { HighlightText };
    
    setup() {
        super.setup();
        const isEnterprise = odoo.info && odoo.info.isEnterprise;
        if (this.props.fieldInfo?.field === upgradeBooleanField && !isEnterprise) {
            this.upgradeEnterprise = true;
        }
    }
}
```

**组件特性**:
- **继承表单标签**: 继承标准表单标签的所有功能
- **高亮文本集成**: 集成HighlightText组件
- **企业版检测**: 自动检测当前版本类型
- **升级提示**: 为升级字段显示特殊标识

## 核心属性

### 1. 组件属性

```javascript
// 模板配置
static template = "web.FormLabelHighlightText";

// 子组件
static components = { HighlightText };
```

**属性功能**:
- **template**: 指定高亮标签的模板
- **components**: 包含HighlightText子组件

### 2. 状态属性

```javascript
// 企业版检测
const isEnterprise = odoo.info && odoo.info.isEnterprise;

// 升级标识
this.upgradeEnterprise = true;
```

**状态属性功能**:
- **isEnterprise**: 检测当前版本是否为企业版
- **upgradeEnterprise**: 标识是否需要显示升级提示

## 核心功能

### 1. 版本检测

```javascript
const isEnterprise = odoo.info && odoo.info.isEnterprise;
```

**检测功能**:
- **全局信息**: 从odoo.info获取版本信息
- **布尔判断**: 简单的布尔值判断
- **安全检查**: 确保odoo.info存在
- **实时检测**: 在组件初始化时检测

### 2. 字段类型判断

```javascript
if (this.props.fieldInfo?.field === upgradeBooleanField && !isEnterprise) {
    this.upgradeEnterprise = true;
}
```

**判断功能**:
- **字段匹配**: 检查是否为升级布尔字段
- **版本条件**: 仅在社区版中设置升级标识
- **可选链**: 使用可选链操作符安全访问属性
- **状态设置**: 设置升级企业版标识

## 使用场景

### 1. 设置表单标签增强器

```javascript
// 设置表单标签增强器
class SettingsFormLabelEnhancer {
    constructor(env) {
        this.env = env;
        this.isEnterprise = odoo.info && odoo.info.isEnterprise;
        this.highlightConfig = this.getHighlightConfig();
        this.setupEnhancer();
    }
    
    setupEnhancer() {
        // 设置高亮配置
        this.highlightTypes = {
            enterprise: {
                color: '#875A7B',
                background: '#F8F4F9',
                icon: 'fa-star',
                tooltip: 'Enterprise Feature'
            },
            new: {
                color: '#28A745',
                background: '#F1F8E9',
                icon: 'fa-sparkles',
                tooltip: 'New Feature'
            },
            beta: {
                color: '#FFC107',
                background: '#FFF8E1',
                icon: 'fa-flask',
                tooltip: 'Beta Feature'
            },
            deprecated: {
                color: '#DC3545',
                background: '#FFEBEE',
                icon: 'fa-exclamation-triangle',
                tooltip: 'Deprecated Feature'
            }
        };
    }
    
    // 获取高亮配置
    getHighlightConfig() {
        return {
            enabled: true,
            showTooltips: true,
            animateOnHover: true,
            persistHighlight: false
        };
    }
    
    // 增强表单标签
    enhanceFormLabel(labelProps, fieldInfo) {
        const enhancement = {
            ...labelProps,
            highlightType: null,
            highlightText: null,
            showUpgradeHint: false,
            additionalClasses: []
        };
        
        // 检查字段类型
        const fieldType = this.getFieldType(fieldInfo);
        
        // 应用增强逻辑
        switch (fieldType) {
            case 'upgrade_boolean':
                enhancement.highlightType = 'enterprise';
                enhancement.showUpgradeHint = !this.isEnterprise;
                enhancement.highlightText = this.getUpgradeText(fieldInfo);
                break;
                
            case 'new_feature':
                enhancement.highlightType = 'new';
                enhancement.highlightText = 'NEW';
                break;
                
            case 'beta_feature':
                enhancement.highlightType = 'beta';
                enhancement.highlightText = 'BETA';
                break;
                
            case 'deprecated':
                enhancement.highlightType = 'deprecated';
                enhancement.highlightText = 'DEPRECATED';
                break;
                
            default:
                // 检查自定义高亮
                enhancement.highlightType = this.getCustomHighlight(fieldInfo);
                break;
        }
        
        // 添加CSS类
        if (enhancement.highlightType) {
            enhancement.additionalClasses.push(`o_field_highlight_${enhancement.highlightType}`);
        }
        
        if (enhancement.showUpgradeHint) {
            enhancement.additionalClasses.push('o_field_upgrade_hint');
        }
        
        return enhancement;
    }
    
    // 获取字段类型
    getFieldType(fieldInfo) {
        if (!fieldInfo) return 'standard';
        
        // 检查升级字段
        if (fieldInfo.field === 'upgrade_boolean_field') {
            return 'upgrade_boolean';
        }
        
        // 检查字段属性
        const fieldAttrs = fieldInfo.attrs || {};
        
        if (fieldAttrs.new_feature) {
            return 'new_feature';
        }
        
        if (fieldAttrs.beta) {
            return 'beta_feature';
        }
        
        if (fieldAttrs.deprecated) {
            return 'deprecated';
        }
        
        return 'standard';
    }
    
    // 获取升级文本
    getUpgradeText(fieldInfo) {
        if (this.isEnterprise) {
            return null;
        }
        
        const featureName = fieldInfo.string || 'Feature';
        return `${featureName} (Enterprise)`;
    }
    
    // 获取自定义高亮
    getCustomHighlight(fieldInfo) {
        const fieldAttrs = fieldInfo.attrs || {};
        
        // 检查自定义高亮属性
        if (fieldAttrs.highlight) {
            return fieldAttrs.highlight;
        }
        
        // 检查字段描述中的标记
        const description = fieldInfo.help || '';
        
        if (description.includes('[NEW]')) {
            return 'new';
        }
        
        if (description.includes('[BETA]')) {
            return 'beta';
        }
        
        if (description.includes('[ENTERPRISE]')) {
            return 'enterprise';
        }
        
        return null;
    }
    
    // 创建高亮标签组件
    createHighlightLabel(enhancement) {
        return {
            Component: FormLabelHighlightText,
            props: {
                ...enhancement,
                highlightConfig: this.highlightConfig,
                highlightStyle: this.getHighlightStyle(enhancement.highlightType)
            }
        };
    }
    
    // 获取高亮样式
    getHighlightStyle(highlightType) {
        if (!highlightType || !this.highlightTypes[highlightType]) {
            return {};
        }
        
        const config = this.highlightTypes[highlightType];
        return {
            color: config.color,
            backgroundColor: config.background,
            icon: config.icon,
            tooltip: config.tooltip
        };
    }
    
    // 处理标签点击
    handleLabelClick(fieldInfo, enhancement) {
        if (enhancement.showUpgradeHint) {
            this.showUpgradeDialog(fieldInfo);
        } else if (enhancement.highlightType === 'deprecated') {
            this.showDeprecationWarning(fieldInfo);
        } else if (enhancement.highlightType === 'beta') {
            this.showBetaNotice(fieldInfo);
        }
    }
    
    // 显示升级对话框
    showUpgradeDialog(fieldInfo) {
        this.env.services.dialog.add(UpgradeDialog, {
            feature: fieldInfo.string,
            description: fieldInfo.help,
            benefits: this.getFeatureBenefits(fieldInfo)
        });
    }
    
    // 显示弃用警告
    showDeprecationWarning(fieldInfo) {
        this.env.services.notification.add(
            `${fieldInfo.string} is deprecated and will be removed in a future version.`,
            {
                type: 'warning',
                sticky: true
            }
        );
    }
    
    // 显示Beta通知
    showBetaNotice(fieldInfo) {
        this.env.services.notification.add(
            `${fieldInfo.string} is a beta feature. Use with caution.`,
            {
                type: 'info'
            }
        );
    }
    
    // 获取功能优势
    getFeatureBenefits(fieldInfo) {
        const benefits = [];
        
        // 从字段描述中提取优势
        const description = fieldInfo.help || '';
        const benefitMatches = description.match(/\[BENEFIT\](.*?)\[\/BENEFIT\]/g);
        
        if (benefitMatches) {
            benefitMatches.forEach(match => {
                const benefit = match.replace(/\[BENEFIT\]|\[\/BENEFIT\]/g, '').trim();
                benefits.push(benefit);
            });
        }
        
        // 默认优势
        if (benefits.length === 0) {
            benefits.push('Enhanced functionality');
            benefits.push('Improved performance');
            benefits.push('Advanced features');
        }
        
        return benefits;
    }
    
    // 获取标签统计
    getLabelStatistics() {
        return {
            total: this.processedLabels || 0,
            enhanced: this.enhancedLabels || 0,
            enterpriseFeatures: this.enterpriseFeatures || 0,
            newFeatures: this.newFeatures || 0,
            betaFeatures: this.betaFeatures || 0,
            deprecatedFeatures: this.deprecatedFeatures || 0
        };
    }
    
    // 重置统计
    resetStatistics() {
        this.processedLabels = 0;
        this.enhancedLabels = 0;
        this.enterpriseFeatures = 0;
        this.newFeatures = 0;
        this.betaFeatures = 0;
        this.deprecatedFeatures = 0;
    }
    
    // 更新统计
    updateStatistics(enhancement) {
        this.processedLabels = (this.processedLabels || 0) + 1;
        
        if (enhancement.highlightType) {
            this.enhancedLabels = (this.enhancedLabels || 0) + 1;
            
            switch (enhancement.highlightType) {
                case 'enterprise':
                    this.enterpriseFeatures = (this.enterpriseFeatures || 0) + 1;
                    break;
                case 'new':
                    this.newFeatures = (this.newFeatures || 0) + 1;
                    break;
                case 'beta':
                    this.betaFeatures = (this.betaFeatures || 0) + 1;
                    break;
                case 'deprecated':
                    this.deprecatedFeatures = (this.deprecatedFeatures || 0) + 1;
                    break;
            }
        }
    }
}

// 使用示例
const labelEnhancer = new SettingsFormLabelEnhancer(env);

// 增强表单标签
const enhancement = labelEnhancer.enhanceFormLabel(labelProps, fieldInfo);

// 创建高亮标签
const highlightLabel = labelEnhancer.createHighlightLabel(enhancement);

// 获取统计信息
const stats = labelEnhancer.getLabelStatistics();
console.log('Label statistics:', stats);
```

### 2. 高亮文本管理器

```javascript
// 高亮文本管理器
class HighlightTextManager {
    constructor(env) {
        this.env = env;
        this.highlightCache = new Map();
        this.animationQueue = [];
        this.setupManager();
    }
    
    setupManager() {
        // 设置高亮主题
        this.themes = {
            default: {
                enterprise: { color: '#875A7B', bg: '#F8F4F9' },
                new: { color: '#28A745', bg: '#F1F8E9' },
                beta: { color: '#FFC107', bg: '#FFF8E1' },
                warning: { color: '#DC3545', bg: '#FFEBEE' }
            },
            dark: {
                enterprise: { color: '#B794A8', bg: '#2D1B2E' },
                new: { color: '#4CAF50', bg: '#1B2E1B' },
                beta: { color: '#FFD54F', bg: '#2E2B1B' },
                warning: { color: '#F44336', bg: '#2E1B1B' }
            },
            colorblind: {
                enterprise: { color: '#0066CC', bg: '#E6F2FF' },
                new: { color: '#009900', bg: '#E6FFE6' },
                beta: { color: '#FF6600', bg: '#FFF2E6' },
                warning: { color: '#CC0000', bg: '#FFE6E6' }
            }
        };
        
        this.currentTheme = 'default';
    }
    
    // 创建高亮文本
    createHighlightText(text, type, options = {}) {
        const highlightId = this.generateHighlightId(text, type);
        
        if (this.highlightCache.has(highlightId)) {
            return this.highlightCache.get(highlightId);
        }
        
        const highlight = {
            id: highlightId,
            text: text,
            type: type,
            style: this.getHighlightStyle(type),
            animation: options.animation || 'none',
            duration: options.duration || 300,
            delay: options.delay || 0,
            persistent: options.persistent || false,
            interactive: options.interactive || false,
            tooltip: options.tooltip || null
        };
        
        // 缓存高亮配置
        this.highlightCache.set(highlightId, highlight);
        
        return highlight;
    }
    
    // 生成高亮ID
    generateHighlightId(text, type) {
        const content = `${text}_${type}_${this.currentTheme}`;
        return btoa(content).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
    }
    
    // 获取高亮样式
    getHighlightStyle(type) {
        const theme = this.themes[this.currentTheme] || this.themes.default;
        const typeStyle = theme[type] || theme.default;
        
        return {
            color: typeStyle.color,
            backgroundColor: typeStyle.bg,
            padding: '2px 6px',
            borderRadius: '3px',
            fontSize: '0.75em',
            fontWeight: 'bold',
            textTransform: 'uppercase',
            letterSpacing: '0.5px',
            display: 'inline-block',
            marginLeft: '8px',
            verticalAlign: 'middle'
        };
    }
    
    // 应用高亮动画
    applyHighlightAnimation(element, animation, duration = 300) {
        if (!element || animation === 'none') return;
        
        const animationConfig = {
            fadeIn: {
                keyframes: [
                    { opacity: 0, transform: 'scale(0.8)' },
                    { opacity: 1, transform: 'scale(1)' }
                ],
                options: { duration, easing: 'ease-out' }
            },
            slideIn: {
                keyframes: [
                    { transform: 'translateX(-20px)', opacity: 0 },
                    { transform: 'translateX(0)', opacity: 1 }
                ],
                options: { duration, easing: 'ease-out' }
            },
            bounce: {
                keyframes: [
                    { transform: 'scale(1)' },
                    { transform: 'scale(1.1)' },
                    { transform: 'scale(1)' }
                ],
                options: { duration: duration * 0.6, easing: 'ease-in-out' }
            },
            pulse: {
                keyframes: [
                    { opacity: 1 },
                    { opacity: 0.7 },
                    { opacity: 1 }
                ],
                options: { duration, easing: 'ease-in-out', iterations: 2 }
            }
        };
        
        const config = animationConfig[animation];
        if (config) {
            element.animate(config.keyframes, config.options);
        }
    }
    
    // 设置主题
    setTheme(themeName) {
        if (this.themes[themeName]) {
            this.currentTheme = themeName;
            this.highlightCache.clear(); // 清除缓存以应用新主题
        }
    }
    
    // 批量创建高亮
    createBatchHighlights(highlights) {
        const results = [];
        
        highlights.forEach((config, index) => {
            const highlight = this.createHighlightText(
                config.text,
                config.type,
                {
                    ...config.options,
                    delay: (config.options?.delay || 0) + (index * 50) // 错开动画
                }
            );
            results.push(highlight);
        });
        
        return results;
    }
    
    // 获取高亮统计
    getHighlightStatistics() {
        const stats = {
            total: this.highlightCache.size,
            byType: {},
            byTheme: this.currentTheme,
            cacheSize: this.highlightCache.size
        };
        
        // 统计各类型数量
        for (const highlight of this.highlightCache.values()) {
            stats.byType[highlight.type] = (stats.byType[highlight.type] || 0) + 1;
        }
        
        return stats;
    }
    
    // 清理缓存
    clearCache() {
        this.highlightCache.clear();
    }
    
    // 预加载高亮
    preloadHighlights(configurations) {
        configurations.forEach(config => {
            this.createHighlightText(config.text, config.type, config.options);
        });
    }
    
    // 导出高亮配置
    exportHighlightConfig() {
        const config = {
            theme: this.currentTheme,
            highlights: Array.from(this.highlightCache.values()),
            timestamp: Date.now()
        };
        
        return JSON.stringify(config, null, 2);
    }
    
    // 导入高亮配置
    importHighlightConfig(configJson) {
        try {
            const config = JSON.parse(configJson);
            
            if (config.theme && this.themes[config.theme]) {
                this.setTheme(config.theme);
            }
            
            if (config.highlights && Array.isArray(config.highlights)) {
                config.highlights.forEach(highlight => {
                    this.highlightCache.set(highlight.id, highlight);
                });
            }
            
            return true;
        } catch (error) {
            console.error('Failed to import highlight config:', error);
            return false;
        }
    }
}

// 使用示例
const highlightManager = new HighlightTextManager(env);

// 创建高亮文本
const enterpriseHighlight = highlightManager.createHighlightText(
    'ENTERPRISE',
    'enterprise',
    { animation: 'fadeIn', tooltip: 'Enterprise Feature' }
);

// 批量创建高亮
const batchHighlights = highlightManager.createBatchHighlights([
    { text: 'NEW', type: 'new', options: { animation: 'slideIn' } },
    { text: 'BETA', type: 'beta', options: { animation: 'bounce' } },
    { text: 'DEPRECATED', type: 'warning', options: { animation: 'pulse' } }
]);

// 设置主题
highlightManager.setTheme('dark');

// 获取统计
const stats = highlightManager.getHighlightStatistics();
console.log('Highlight statistics:', stats);
```

## 技术特点

### 1. 继承扩展
- **基类继承**: 继承标准表单标签的所有功能
- **组件集成**: 集成HighlightText子组件
- **模板重用**: 使用专门的高亮标签模板
- **功能增强**: 在原有功能基础上增加高亮显示

### 2. 版本感知
- **实时检测**: 在组件初始化时检测版本
- **条件逻辑**: 根据版本类型执行不同逻辑
- **安全访问**: 使用安全的属性访问方式
- **状态管理**: 管理升级提示的显示状态

### 3. 字段识别
- **类型匹配**: 精确匹配升级布尔字段类型
- **可选链**: 使用可选链操作符安全访问
- **条件设置**: 仅在特定条件下设置状态
- **灵活判断**: 支持多种字段类型的判断

### 4. 组件集成
- **子组件**: 集成HighlightText作为子组件
- **模板驱动**: 使用模板定义组件结构
- **属性传递**: 通过属性传递配置信息
- **状态共享**: 与子组件共享状态信息

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **功能继承**: 继承基类的所有功能
- **行为扩展**: 扩展特定的显示行为
- **接口保持**: 保持与基类相同的接口

### 2. 组合模式 (Composition Pattern)
- **组件组合**: 组合多个子组件
- **功能集成**: 集成不同的功能模块
- **层次结构**: 构建清晰的组件层次

### 3. 策略模式 (Strategy Pattern)
- **版本策略**: 根据版本类型执行不同策略
- **显示策略**: 不同的高亮显示策略
- **条件策略**: 基于条件的策略选择

### 4. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 在基础标签上装饰高亮功能
- **视觉增强**: 增强标签的视觉效果
- **透明扩展**: 对外接口保持透明

## 注意事项

1. **版本检测**: 确保正确检测Odoo版本类型
2. **字段匹配**: 准确匹配升级布尔字段
3. **性能考虑**: 避免不必要的组件渲染
4. **样式一致**: 保持与整体界面的样式一致

## 扩展建议

1. **多种高亮**: 支持更多类型的高亮显示
2. **动画效果**: 添加高亮文本的动画效果
3. **主题支持**: 支持不同的高亮主题
4. **交互功能**: 添加高亮文本的交互功能
5. **配置化**: 支持高亮显示的配置化

该表单标签高亮文本组件为Odoo Web客户端的设置表单提供了增强的标签显示功能，通过智能的版本检测和高亮文本集成确保了良好的用户体验和视觉效果。

# SettingsConfirmationDialog - 设置确认对话框

## 概述

`settings_confirmation_dialog.js` 是 Odoo Web 客户端的设置确认对话框组件，提供了设置页面中未保存更改的确认功能。该模块包含26行代码，是一个OWL组件，专门用于在用户离开设置页面时提醒未保存的更改，具备自定义标题、回调函数、继承扩展等特性，是Odoo Web设置表单视图中用户体验保护的重要组件。

## 文件信息
- **路径**: `/web/static/src/webclient/settings_form_view/settings_confirmation_dialog.js`
- **行数**: 26
- **模块**: `@web/webclient/settings_form_view/settings_confirmation_dialog`

## 依赖关系

```javascript
// 核心依赖
'@web/core/confirmation_dialog/confirmation_dialog'     // 确认对话框基类
'@web/core/l10n/translation'                           // 国际化翻译
```

## 主组件定义

### 1. SettingsConfirmationDialog 组件

```javascript
class SettingsConfirmationDialog extends ConfirmationDialog {
    static template = "web.SettingsConfirmationDialog";
    static defaultProps = {
        title: _t("Unsaved changes"),
    };
    static props = {
        ...ConfirmationDialog.props,
        stayHere: { type: Function, optional: true },
    };

    _stayHere() {
        if (this.props.stayHere) {
            this.props.stayHere();
        }
        this.props.close();
    }
}
```

**组件特性**:
- **继承扩展**: 继承自ConfirmationDialog基类
- **自定义模板**: 使用专门的设置确认对话框模板
- **默认标题**: 提供"未保存更改"的默认标题
- **回调支持**: 支持自定义的"留在此处"回调函数

## 核心属性

### 1. 组件属性

```javascript
// 模板配置
static template = "web.SettingsConfirmationDialog";

// 默认属性
static defaultProps = {
    title: _t("Unsaved changes"),
};

// 属性定义
static props = {
    ...ConfirmationDialog.props,
    stayHere: { type: Function, optional: true },
};
```

**属性功能**:
- **template**: 指定设置确认对话框的专用模板
- **defaultProps**: 设置默认的标题为"未保存更改"
- **props**: 继承基类属性并添加stayHere回调函数

### 2. 回调属性

```javascript
// 留在此处回调
stayHere: { type: Function, optional: true }
```

**回调属性功能**:
- **stayHere**: 用户选择留在当前页面时的回调函数
- **可选属性**: 该回调函数是可选的
- **函数类型**: 明确指定为函数类型

## 核心功能

### 1. 留在此处操作

```javascript
_stayHere() {
    if (this.props.stayHere) {
        this.props.stayHere();
    }
    this.props.close();
}
```

**操作功能**:
- **回调检查**: 检查是否提供了stayHere回调函数
- **回调执行**: 如果提供了回调函数则执行
- **对话框关闭**: 执行完回调后关闭对话框
- **安全调用**: 安全地调用可选的回调函数

### 2. 继承机制

```javascript
class SettingsConfirmationDialog extends ConfirmationDialog
```

**继承功能**:
- **基类继承**: 继承ConfirmationDialog的所有功能
- **属性扩展**: 扩展基类的属性定义
- **方法重写**: 可以重写基类的方法
- **功能增强**: 在基类基础上增强功能

## 使用场景

### 1. 设置页面导航保护

```javascript
// 设置页面导航保护管理器
class SettingsNavigationGuard {
    constructor(env) {
        this.env = env;
        this.hasUnsavedChanges = false;
        this.setupGuard();
    }
    
    setupGuard() {
        // 设置导航保护配置
        this.guardConfig = {
            enableGuard: true,
            checkInterval: 1000,
            autoSave: false,
            confirmationTimeout: 30000,
            showWarningThreshold: 5000
        };
        
        // 设置确认对话框选项
        this.dialogOptions = {
            title: _t("Unsaved changes"),
            body: _t("You have unsaved changes. Are you sure you want to leave this page?"),
            confirmText: _t("Leave Page"),
            cancelText: _t("Stay Here"),
            confirmClass: "btn-danger"
        };
        
        this.setupEventListeners();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听表单变化
        this.env.bus.addEventListener('FORM_CHANGED', (event) => {
            this.handleFormChange(event.detail);
        });
        
        // 监听导航尝试
        this.env.bus.addEventListener('NAVIGATION_ATTEMPT', (event) => {
            this.handleNavigationAttempt(event.detail);
        });
        
        // 监听页面卸载
        window.addEventListener('beforeunload', (event) => {
            this.handleBeforeUnload(event);
        });
        
        // 监听路由变化
        this.env.services.router.addEventListener('routeChange', (event) => {
            this.handleRouteChange(event);
        });
    }
    
    // 检查未保存更改
    checkUnsavedChanges() {
        // 检查表单字段
        const formFields = document.querySelectorAll('.o_setting_box input, .o_setting_box select, .o_setting_box textarea');
        let hasChanges = false;
        
        formFields.forEach(field => {
            if (field.dataset.originalValue !== field.value) {
                hasChanges = true;
            }
        });
        
        // 检查特殊组件状态
        const specialComponents = this.getSpecialComponents();
        specialComponents.forEach(component => {
            if (component.hasUnsavedChanges && component.hasUnsavedChanges()) {
                hasChanges = true;
            }
        });
        
        this.hasUnsavedChanges = hasChanges;
        return hasChanges;
    }
    
    // 获取特殊组件
    getSpecialComponents() {
        const components = [];
        
        // 获取设置组件
        const settingComponents = document.querySelectorAll('[data-component="setting"]');
        settingComponents.forEach(element => {
            const component = element.__owl__;
            if (component) {
                components.push(component);
            }
        });
        
        return components;
    }
    
    // 显示确认对话框
    async showConfirmationDialog(options = {}) {
        const dialogOptions = { ...this.dialogOptions, ...options };
        
        return new Promise((resolve) => {
            this.env.services.dialog.add(SettingsConfirmationDialog, {
                title: dialogOptions.title,
                body: dialogOptions.body,
                confirm: () => {
                    this.handleLeaveConfirmed();
                    resolve(true);
                },
                cancel: () => {
                    this.handleStayConfirmed();
                    resolve(false);
                },
                stayHere: () => {
                    this.handleStayHere();
                    resolve(false);
                }
            });
        });
    }
    
    // 处理表单变化
    handleFormChange(changeData) {
        const { field, oldValue, newValue } = changeData;
        
        // 记录原始值
        if (!field.dataset.originalValue) {
            field.dataset.originalValue = oldValue;
        }
        
        // 更新未保存状态
        this.checkUnsavedChanges();
        
        // 触发状态变化事件
        this.env.bus.trigger('UNSAVED_CHANGES_STATUS', {
            hasUnsavedChanges: this.hasUnsavedChanges,
            field: field,
            timestamp: Date.now()
        });
    }
    
    // 处理导航尝试
    async handleNavigationAttempt(navigationData) {
        if (!this.guardConfig.enableGuard || !this.hasUnsavedChanges) {
            return true; // 允许导航
        }
        
        const { targetRoute, navigationMethod } = navigationData;
        
        // 显示确认对话框
        const confirmed = await this.showConfirmationDialog({
            body: _t("You have unsaved changes. Are you sure you want to navigate to %s?", targetRoute)
        });
        
        if (confirmed) {
            // 清除未保存状态
            this.clearUnsavedChanges();
            return true;
        } else {
            // 阻止导航
            return false;
        }
    }
    
    // 处理页面卸载
    handleBeforeUnload(event) {
        if (this.hasUnsavedChanges && this.guardConfig.enableGuard) {
            const message = _t("You have unsaved changes. Are you sure you want to leave this page?");
            event.returnValue = message;
            return message;
        }
    }
    
    // 处理路由变化
    async handleRouteChange(routeEvent) {
        if (this.hasUnsavedChanges && this.guardConfig.enableGuard) {
            const confirmed = await this.showConfirmationDialog();
            
            if (!confirmed) {
                // 阻止路由变化
                routeEvent.preventDefault();
            }
        }
    }
    
    // 处理离开确认
    handleLeaveConfirmed() {
        // 清除未保存状态
        this.clearUnsavedChanges();
        
        // 触发离开事件
        this.env.bus.trigger('SETTINGS_PAGE_LEFT', {
            reason: 'user_confirmed',
            timestamp: Date.now()
        });
    }
    
    // 处理留下确认
    handleStayConfirmed() {
        // 触发留下事件
        this.env.bus.trigger('SETTINGS_PAGE_STAYED', {
            reason: 'user_cancelled',
            timestamp: Date.now()
        });
    }
    
    // 处理留在此处
    handleStayHere() {
        // 聚焦到第一个有未保存更改的字段
        this.focusFirstChangedField();
        
        // 显示保存提示
        this.showSaveReminder();
        
        // 触发留在此处事件
        this.env.bus.trigger('SETTINGS_STAY_HERE', {
            timestamp: Date.now()
        });
    }
    
    // 聚焦到第一个更改的字段
    focusFirstChangedField() {
        const changedFields = document.querySelectorAll('.o_setting_box input[data-original-value], .o_setting_box select[data-original-value], .o_setting_box textarea[data-original-value]');
        
        for (const field of changedFields) {
            if (field.dataset.originalValue !== field.value) {
                field.focus();
                field.scrollIntoView({ behavior: 'smooth', block: 'center' });
                break;
            }
        }
    }
    
    // 显示保存提醒
    showSaveReminder() {
        this.env.services.notification.add(
            _t("Don't forget to save your changes!"),
            {
                type: 'info',
                sticky: false,
                className: 'o_save_reminder'
            }
        );
    }
    
    // 清除未保存更改
    clearUnsavedChanges() {
        // 清除字段标记
        const fields = document.querySelectorAll('.o_setting_box input, .o_setting_box select, .o_setting_box textarea');
        fields.forEach(field => {
            delete field.dataset.originalValue;
        });
        
        // 重置状态
        this.hasUnsavedChanges = false;
        
        // 触发状态清除事件
        this.env.bus.trigger('UNSAVED_CHANGES_CLEARED', {
            timestamp: Date.now()
        });
    }
    
    // 保存所有更改
    async saveAllChanges() {
        try {
            // 获取所有有更改的字段
            const changedFields = this.getChangedFields();
            
            if (changedFields.length === 0) {
                return true;
            }
            
            // 批量保存
            const savePromises = changedFields.map(field => this.saveField(field));
            await Promise.all(savePromises);
            
            // 清除未保存状态
            this.clearUnsavedChanges();
            
            this.env.services.notification.add(
                _t("All changes saved successfully"),
                { type: 'success' }
            );
            
            return true;
        } catch (error) {
            console.error('Failed to save changes:', error);
            
            this.env.services.notification.add(
                _t("Failed to save changes: %s", error.message),
                { type: 'danger' }
            );
            
            return false;
        }
    }
    
    // 获取更改的字段
    getChangedFields() {
        const changedFields = [];
        const fields = document.querySelectorAll('.o_setting_box input, .o_setting_box select, .o_setting_box textarea');
        
        fields.forEach(field => {
            if (field.dataset.originalValue && field.dataset.originalValue !== field.value) {
                changedFields.push(field);
            }
        });
        
        return changedFields;
    }
    
    // 保存单个字段
    async saveField(field) {
        const fieldName = field.name;
        const fieldValue = field.value;
        const recordId = field.dataset.recordId;
        const modelName = field.dataset.modelName;
        
        if (modelName && recordId && fieldName) {
            await this.env.services.orm.write(modelName, [parseInt(recordId)], {
                [fieldName]: fieldValue
            });
        }
    }
    
    // 启用导航保护
    enableGuard() {
        this.guardConfig.enableGuard = true;
    }
    
    // 禁用导航保护
    disableGuard() {
        this.guardConfig.enableGuard = false;
    }
    
    // 获取保护状态
    getGuardStatus() {
        return {
            enabled: this.guardConfig.enableGuard,
            hasUnsavedChanges: this.hasUnsavedChanges,
            changedFieldsCount: this.getChangedFields().length
        };
    }
    
    // 销毁保护器
    destroy() {
        // 移除事件监听器
        this.env.bus.removeEventListener('FORM_CHANGED');
        this.env.bus.removeEventListener('NAVIGATION_ATTEMPT');
        window.removeEventListener('beforeunload', this.handleBeforeUnload);
        
        // 清除状态
        this.clearUnsavedChanges();
    }
}

// 使用示例
const navigationGuard = new SettingsNavigationGuard(env);

// 检查未保存更改
const hasChanges = navigationGuard.checkUnsavedChanges();
console.log('Has unsaved changes:', hasChanges);

// 显示确认对话框
const confirmed = await navigationGuard.showConfirmationDialog();
console.log('User confirmed:', confirmed);

// 保存所有更改
const saved = await navigationGuard.saveAllChanges();
console.log('Changes saved:', saved);
```

### 2. 自定义确认对话框

```javascript
// 自定义确认对话框组件
class CustomSettingsConfirmationDialog extends SettingsConfirmationDialog {
    static template = "custom.SettingsConfirmationDialog";
    
    static defaultProps = {
        ...SettingsConfirmationDialog.defaultProps,
        showSaveOption: true,
        autoSaveTimeout: 10000
    };
    
    static props = {
        ...SettingsConfirmationDialog.props,
        showSaveOption: { type: Boolean, optional: true },
        autoSaveTimeout: { type: Number, optional: true },
        onSave: { type: Function, optional: true },
        onAutoSave: { type: Function, optional: true }
    };
    
    setup() {
        super.setup();
        
        this.state = useState({
            autoSaveCountdown: this.props.autoSaveTimeout / 1000,
            isAutoSaving: false
        });
        
        if (this.props.autoSaveTimeout && this.props.onAutoSave) {
            this.startAutoSaveCountdown();
        }
    }
    
    // 启动自动保存倒计时
    startAutoSaveCountdown() {
        this.autoSaveInterval = setInterval(() => {
            this.state.autoSaveCountdown--;
            
            if (this.state.autoSaveCountdown <= 0) {
                this.performAutoSave();
            }
        }, 1000);
    }
    
    // 执行自动保存
    async performAutoSave() {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
        }
        
        this.state.isAutoSaving = true;
        
        try {
            if (this.props.onAutoSave) {
                await this.props.onAutoSave();
            }
            
            this.props.close();
        } catch (error) {
            console.error('Auto save failed:', error);
            this.state.isAutoSaving = false;
        }
    }
    
    // 手动保存
    async _save() {
        if (this.props.onSave) {
            this.state.isAutoSaving = true;
            
            try {
                await this.props.onSave();
                this.props.close();
            } catch (error) {
                console.error('Save failed:', error);
                this.state.isAutoSaving = false;
            }
        }
    }
    
    // 重写留在此处方法
    _stayHere() {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
        }
        
        super._stayHere();
    }
    
    // 重写确认方法
    _confirm() {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
        }
        
        super._confirm();
    }
    
    // 组件销毁时清理
    willUnmount() {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
        }
    }
}
```

## 技术特点

### 1. 继承扩展
- **基类继承**: 继承ConfirmationDialog的所有功能
- **属性扩展**: 扩展基类的属性定义
- **方法增强**: 在基类基础上增强功能
- **模板定制**: 使用专门的模板

### 2. 国际化支持
- **默认标题**: 提供国际化的默认标题
- **翻译集成**: 集成翻译服务
- **多语言**: 支持多语言环境
- **本地化**: 适应不同地区的用户

### 3. 回调机制
- **可选回调**: 支持可选的回调函数
- **安全调用**: 安全地调用回调函数
- **功能扩展**: 通过回调扩展功能
- **事件处理**: 处理用户交互事件

### 4. 用户体验
- **明确提示**: 明确的未保存更改提示
- **操作选择**: 提供多种操作选择
- **状态保护**: 保护用户的工作状态
- **友好交互**: 友好的用户交互体验

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **基类扩展**: 扩展基类功能
- **方法重写**: 重写基类方法
- **属性继承**: 继承基类属性

### 2. 模板方法模式 (Template Method Pattern)
- **模板定制**: 定制专门的模板
- **行为定义**: 定义特定的行为
- **结构复用**: 复用基类结构

### 3. 回调模式 (Callback Pattern)
- **事件回调**: 事件驱动的回调
- **异步处理**: 异步操作的回调
- **功能扩展**: 通过回调扩展功能

### 4. 策略模式 (Strategy Pattern)
- **操作策略**: 不同的操作策略
- **响应策略**: 不同的响应策略
- **处理策略**: 不同的处理策略

## 注意事项

1. **回调安全**: 确保回调函数的安全调用
2. **继承正确**: 正确继承基类的功能
3. **模板匹配**: 确保模板与组件匹配
4. **用户体验**: 提供清晰的用户提示

## 扩展建议

1. **自动保存**: 添加自动保存功能
2. **倒计时**: 添加操作倒计时功能
3. **详细信息**: 显示更详细的更改信息
4. **批量操作**: 支持批量保存或丢弃
5. **历史记录**: 支持更改历史记录

该设置确认对话框组件为Odoo Web客户端提供了专业的未保存更改保护功能，通过继承扩展和回调机制确保了良好的用户体验和数据安全性。

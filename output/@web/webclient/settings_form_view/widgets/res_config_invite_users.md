# ResConfigInviteUsers - 用户邀请配置组件

## 概述

`res_config_invite_users.js` 是 Odoo Web 客户端的用户邀请配置组件，提供了邀请新用户加入系统的功能。该模块包含154行代码，是一个OWL组件，专门用于在设置页面中管理用户邀请，具备邮箱验证、批量邀请、待处理用户管理、键盘快捷键等特性，是Odoo Web设置表单视图中用户管理的重要组件。

## 文件信息
- **路径**: `/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.js`
- **行数**: 154
- **模块**: `@web/webclient/settings_form_view/widgets/res_config_invite_users`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                            // 注册表系统
'@web/core/l10n/translation'                   // 国际化翻译
'@web/core/l10n/utils'                          // 国际化工具
'@web/core/utils/arrays'                        // 数组工具
'@web/core/utils/hooks'                         // 工具钩子
'@odoo/owl'                                     // OWL框架
'@web/views/widgets/standard_widget_props'     // 标准组件属性
```

## 主组件定义

### 1. ResConfigInviteUsers 组件

```javascript
class ResConfigInviteUsers extends Component {
    static template = "res_config_invite_users";
    static props = {
        ...standardWidgetProps,
    };

    setup() {
        this.orm = useService("orm");
        this.invite = useService("user_invite");
        this.action = useService("action");
        this.notification = useService("notification");

        this.state = useState({
            status: "idle", // idle, inviting
            emails: "",
            invite: null,
        });

        onWillStart(async () => {
            this.state.invite = await this.invite.fetchData();
        });
    }
}
```

**组件特性**:
- **用户邀请**: 支持通过邮箱邀请新用户
- **邮箱验证**: 验证邮箱地址的有效性
- **批量处理**: 支持批量邀请多个用户
- **状态管理**: 管理邀请过程的状态

## 核心属性

### 1. 组件属性

```javascript
// 模板配置
static template = "res_config_invite_users";

// 属性定义
static props = {
    ...standardWidgetProps,
};
```

**属性功能**:
- **template**: 指定用户邀请配置的模板
- **props**: 继承标准组件属性

### 2. 状态属性

```javascript
// 组件状态
this.state = useState({
    status: "idle",     // 邀请状态：idle, inviting
    emails: "",         // 邮箱地址字符串
    invite: null,       // 邀请数据
});
```

**状态属性功能**:
- **status**: 当前邀请操作的状态
- **emails**: 用户输入的邮箱地址
- **invite**: 邀请相关的数据和配置

## 核心功能

### 1. 服务集成

```javascript
this.orm = useService("orm");
this.invite = useService("user_invite");
this.action = useService("action");
this.notification = useService("notification");
```

**服务集成功能**:
- **ORM服务**: 用于数据库操作
- **邀请服务**: 专门的用户邀请服务
- **动作服务**: 执行系统动作
- **通知服务**: 显示用户通知

### 2. 邮箱验证

```javascript
/**
 * @param {string} email
 * @returns {boolean} true if the given email address is valid
 */
validateEmail(email) {
    const re =
        /^([a-z0-9][-a-z0-9_+.]*)@((?:[\w-]+\.)*\w[\w-]{0,66})\.([a-z]{2,63}(?:\.[a-z]{2})?)$/i;
    return re.test(email);
}
```

**验证功能**:
- **正则表达式**: 使用正则表达式验证邮箱格式
- **标准格式**: 支持标准的邮箱地址格式
- **大小写不敏感**: 忽略大小写进行验证
- **返回布尔值**: 返回验证结果

### 3. 邮箱地址处理

```javascript
get emails() {
    return unique(
        this.state.emails
            .split(/[ ,;\n]+/)
            .map((email) => email.trim())
            .filter((email) => email.length)
    );
}
```

**处理功能**:
- **多分隔符**: 支持空格、逗号、分号、换行符分隔
- **去重处理**: 使用unique函数去除重复邮箱
- **空白处理**: 去除邮箱地址的前后空白
- **过滤空值**: 过滤掉空的邮箱地址

### 4. 邮箱验证

```javascript
validate() {
    if (!this.emails.length) {
        throw new Error(_t("Empty email address"));
    }
    const invalidEmails = [];
    for (const email of this.emails) {
        if (!this.validateEmail(email)) {
            invalidEmails.push(email);
        }
    }
    if (invalidEmails.length) {
        const errorMessage = (() => {
            switch (invalidEmails.length) {
                case 1:
                    return _t("Invalid email address: %(address)s", {
                        address: invalidEmails[0],
                    });
                case 2:
                    return _t("Invalid email addresses: %(2 addresses)s", {
                        "2 addresses": formatList(invalidEmails),
                    });
                default:
                    return _t("Invalid email addresses: %(addresses)s", {
                        addresses: formatList(invalidEmails),
                    });
            }
        })();
        throw new Error(errorMessage);
    }
}
```

**验证功能**:
- **空值检查**: 检查是否有邮箱地址
- **格式验证**: 验证每个邮箱地址的格式
- **错误收集**: 收集所有无效的邮箱地址
- **国际化错误**: 提供国际化的错误消息

### 5. 邀请发送

```javascript
/**
 * Send invitation for valid and unique email addresses
 *
 * @private
 */
async sendInvite() {
    try {
        this.validate();
    } catch (e) {
        this.notification.add(e.message, { type: "danger" });
        return;
    }

    this.state.status = "inviting";

    const pendingUserEmails = this.state.invite.pending_users.map((user) => user[1]);
    const emailsLeftToProcess = this.emails.filter(
        (email) => !pendingUserEmails.includes(email)
    );

    try {
        if (emailsLeftToProcess) {
            await this.orm.call("res.users", "web_create_users", [emailsLeftToProcess]);
            this.state.invite = await this.invite.fetchData(true);
        }
    } finally {
        this.state.emails = "";
        this.state.status = "idle";
    }
}
```

**发送功能**:
- **验证检查**: 发送前验证邮箱地址
- **状态更新**: 更新邀请状态
- **重复过滤**: 过滤已邀请的用户
- **ORM调用**: 调用后端创建用户
- **数据刷新**: 刷新邀请数据
- **状态重置**: 重置输入和状态

### 6. 键盘事件处理

```javascript
onKeydownUserEmails(ev) {
    const keys = ["Enter", "Tab", ","];
    if (keys.includes(ev.key)) {
        if (ev.key === "Tab" && !this.emails.length) {
            return;
        }
        ev.preventDefault();
        this.sendInvite();
    }
}
```

**键盘处理功能**:
- **快捷键支持**: 支持Enter、Tab、逗号触发邀请
- **条件检查**: Tab键需要有邮箱地址才触发
- **事件阻止**: 阻止默认的键盘事件
- **自动发送**: 自动触发邀请发送

### 7. 用户操作

```javascript
onClickMore(ev) {
    this.action.doAction(this.state.invite.action_pending_users);
}

onClickUser(ev, user) {
    const action = Object.assign({}, this.state.invite.action_pending_users, {
        res_id: user[0],
    });
    this.action.doAction(action);
}
```

**操作功能**:
- **查看更多**: 查看所有待处理用户
- **用户详情**: 查看特定用户的详情
- **动作执行**: 执行相应的系统动作
- **参数传递**: 传递用户ID等参数

## 使用场景

### 1. 用户邀请管理器

```javascript
// 用户邀请管理器
class UserInviteManager {
    constructor(env) {
        this.env = env;
        this.orm = env.services.orm;
        this.invite = env.services.user_invite;
        this.notification = env.services.notification;
        this.setupManager();
    }
    
    setupManager() {
        // 设置邀请配置
        this.inviteConfig = {
            maxBatchSize: 50,
            emailValidationStrict: true,
            allowDuplicates: false,
            autoSendWelcome: true,
            defaultRole: 'user',
            expirationDays: 7
        };
        
        // 设置邮箱验证规则
        this.emailValidationRules = {
            basic: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            strict: /^([a-z0-9][-a-z0-9_+.]*)@((?:[\w-]+\.)*\w[\w-]{0,66})\.([a-z]{2,63}(?:\.[a-z]{2})?)$/i,
            enterprise: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
        };
        
        this.setupEventListeners();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听邀请状态变化
        this.env.bus.addEventListener('INVITE_STATUS_CHANGED', (event) => {
            this.handleInviteStatusChange(event.detail);
        });
        
        // 监听用户创建
        this.env.bus.addEventListener('USER_CREATED', (event) => {
            this.handleUserCreated(event.detail);
        });
    }
    
    // 验证邮箱地址
    validateEmail(email, rule = 'strict') {
        const regex = this.emailValidationRules[rule] || this.emailValidationRules.strict;
        return regex.test(email.trim());
    }
    
    // 解析邮箱地址
    parseEmails(emailString) {
        const separators = /[ ,;\n\t]+/;
        const emails = emailString
            .split(separators)
            .map(email => email.trim())
            .filter(email => email.length > 0);
        
        // 去重
        const uniqueEmails = [...new Set(emails)];
        
        return {
            emails: uniqueEmails,
            total: uniqueEmails.length,
            duplicates: emails.length - uniqueEmails.length
        };
    }
    
    // 批量验证邮箱
    validateEmailBatch(emails) {
        const results = {
            valid: [],
            invalid: [],
            duplicates: [],
            total: emails.length
        };
        
        const seen = new Set();
        
        emails.forEach(email => {
            const trimmedEmail = email.trim().toLowerCase();
            
            // 检查重复
            if (seen.has(trimmedEmail)) {
                results.duplicates.push(email);
                return;
            }
            seen.add(trimmedEmail);
            
            // 验证格式
            if (this.validateEmail(email)) {
                results.valid.push(email);
            } else {
                results.invalid.push(email);
            }
        });
        
        return results;
    }
    
    // 检查邮箱是否已存在
    async checkExistingEmails(emails) {
        try {
            const existingUsers = await this.orm.searchRead(
                'res.users',
                [['email', 'in', emails]],
                ['email', 'name', 'active']
            );
            
            const existingEmails = existingUsers.map(user => user.email.toLowerCase());
            
            return {
                existing: emails.filter(email => 
                    existingEmails.includes(email.toLowerCase())
                ),
                new: emails.filter(email => 
                    !existingEmails.includes(email.toLowerCase())
                ),
                users: existingUsers
            };
        } catch (error) {
            console.error('Failed to check existing emails:', error);
            return { existing: [], new: emails, users: [] };
        }
    }
    
    // 发送邀请
    async sendInvitations(emails, options = {}) {
        const config = { ...this.inviteConfig, ...options };
        
        // 解析邮箱
        const parsed = this.parseEmails(Array.isArray(emails) ? emails.join(',') : emails);
        
        // 验证邮箱
        const validation = this.validateEmailBatch(parsed.emails);
        
        if (validation.invalid.length > 0) {
            throw new Error(`Invalid email addresses: ${validation.invalid.join(', ')}`);
        }
        
        // 检查现有用户
        const existingCheck = await this.checkExistingEmails(validation.valid);
        
        if (existingCheck.existing.length > 0 && !config.allowDuplicates) {
            throw new Error(`Users already exist: ${existingCheck.existing.join(', ')}`);
        }
        
        // 分批处理
        const batches = this.createBatches(existingCheck.new, config.maxBatchSize);
        const results = [];
        
        for (const batch of batches) {
            try {
                const batchResult = await this.sendInviteBatch(batch, config);
                results.push(...batchResult);
            } catch (error) {
                console.error('Batch invitation failed:', error);
                throw error;
            }
        }
        
        return {
            sent: results.length,
            emails: results,
            existing: existingCheck.existing,
            invalid: validation.invalid,
            duplicates: validation.duplicates
        };
    }
    
    // 创建批次
    createBatches(items, batchSize) {
        const batches = [];
        for (let i = 0; i < items.length; i += batchSize) {
            batches.push(items.slice(i, i + batchSize));
        }
        return batches;
    }
    
    // 发送批次邀请
    async sendInviteBatch(emails, config) {
        try {
            const result = await this.orm.call(
                'res.users',
                'web_create_users',
                [emails],
                {
                    context: {
                        default_groups_id: config.defaultRole ? [config.defaultRole] : [],
                        send_welcome_email: config.autoSendWelcome,
                        invitation_expiry_days: config.expirationDays
                    }
                }
            );
            
            // 触发邀请发送事件
            this.env.bus.trigger('INVITATIONS_SENT', {
                emails: emails,
                count: emails.length,
                timestamp: Date.now()
            });
            
            return emails;
        } catch (error) {
            console.error('Failed to send invitation batch:', error);
            throw error;
        }
    }
    
    // 获取待处理邀请
    async getPendingInvitations() {
        try {
            const pendingUsers = await this.orm.searchRead(
                'res.users',
                [['state', '=', 'new']],
                ['email', 'name', 'create_date', 'login_date']
            );
            
            return pendingUsers.map(user => ({
                id: user.id,
                email: user.email,
                name: user.name,
                invitedDate: user.create_date,
                lastLogin: user.login_date,
                status: user.login_date ? 'activated' : 'pending'
            }));
        } catch (error) {
            console.error('Failed to get pending invitations:', error);
            return [];
        }
    }
    
    // 重新发送邀请
    async resendInvitation(userId) {
        try {
            await this.orm.call('res.users', 'action_reset_password', [userId]);
            
            this.notification.add(
                'Invitation resent successfully',
                { type: 'success' }
            );
            
            return true;
        } catch (error) {
            console.error('Failed to resend invitation:', error);
            
            this.notification.add(
                'Failed to resend invitation: ' + error.message,
                { type: 'danger' }
            );
            
            return false;
        }
    }
    
    // 取消邀请
    async cancelInvitation(userId) {
        try {
            await this.orm.unlink('res.users', [userId]);
            
            this.notification.add(
                'Invitation cancelled successfully',
                { type: 'success' }
            );
            
            return true;
        } catch (error) {
            console.error('Failed to cancel invitation:', error);
            
            this.notification.add(
                'Failed to cancel invitation: ' + error.message,
                { type: 'danger' }
            );
            
            return false;
        }
    }
    
    // 批量操作
    async batchOperation(userIds, operation) {
        const results = {
            success: [],
            failed: []
        };
        
        for (const userId of userIds) {
            try {
                let success = false;
                
                switch (operation) {
                    case 'resend':
                        success = await this.resendInvitation(userId);
                        break;
                    case 'cancel':
                        success = await this.cancelInvitation(userId);
                        break;
                    default:
                        throw new Error(`Unknown operation: ${operation}`);
                }
                
                if (success) {
                    results.success.push(userId);
                } else {
                    results.failed.push(userId);
                }
            } catch (error) {
                results.failed.push(userId);
            }
        }
        
        return results;
    }
    
    // 获取邀请统计
    async getInvitationStatistics() {
        try {
            const stats = await this.orm.call('res.users', 'get_invitation_stats');
            
            return {
                totalInvited: stats.total_invited || 0,
                pendingInvitations: stats.pending_invitations || 0,
                activatedUsers: stats.activated_users || 0,
                expiredInvitations: stats.expired_invitations || 0,
                activationRate: stats.activation_rate || 0,
                averageActivationTime: stats.average_activation_time || 0
            };
        } catch (error) {
            console.error('Failed to get invitation statistics:', error);
            return null;
        }
    }
    
    // 导出邀请数据
    async exportInvitationData() {
        try {
            const pendingInvitations = await this.getPendingInvitations();
            const statistics = await this.getInvitationStatistics();
            
            const exportData = {
                exportDate: new Date().toISOString(),
                statistics: statistics,
                pendingInvitations: pendingInvitations,
                config: this.inviteConfig
            };
            
            const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                type: 'application/json'
            });
            
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `user_invitations_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            URL.revokeObjectURL(url);
            
            return true;
        } catch (error) {
            console.error('Failed to export invitation data:', error);
            return false;
        }
    }
    
    // 处理邀请状态变化
    handleInviteStatusChange(statusData) {
        console.log('Invite status changed:', statusData);
        
        // 更新UI或触发其他操作
        this.env.bus.trigger('UI_UPDATE_REQUIRED', {
            component: 'user_invitations',
            data: statusData
        });
    }
    
    // 处理用户创建
    handleUserCreated(userData) {
        console.log('User created:', userData);
        
        this.notification.add(
            `User invitation sent to ${userData.email}`,
            { type: 'success' }
        );
    }
}

// 使用示例
const inviteManager = new UserInviteManager(env);

// 发送邀请
const result = await inviteManager.sendInvitations([
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
]);

console.log('Invitation result:', result);

// 获取待处理邀请
const pending = await inviteManager.getPendingInvitations();
console.log('Pending invitations:', pending);

// 获取统计信息
const stats = await inviteManager.getInvitationStatistics();
console.log('Invitation statistics:', stats);
```

## 技术特点

### 1. 邮箱验证
- **正则表达式**: 使用严格的正则表达式验证邮箱格式
- **批量验证**: 支持批量验证多个邮箱地址
- **错误收集**: 收集并报告所有验证错误
- **国际化**: 提供国际化的错误消息

### 2. 批量处理
- **多分隔符**: 支持多种分隔符分割邮箱地址
- **去重处理**: 自动去除重复的邮箱地址
- **批量邀请**: 支持批量发送邀请
- **状态管理**: 管理批量操作的状态

### 3. 服务集成
- **ORM服务**: 集成ORM服务进行数据操作
- **邀请服务**: 集成专门的用户邀请服务
- **动作服务**: 集成动作服务执行操作
- **通知服务**: 集成通知服务显示消息

### 4. 用户体验
- **键盘快捷键**: 支持键盘快捷键快速邀请
- **实时验证**: 实时验证邮箱地址
- **状态反馈**: 提供清晰的状态反馈
- **错误处理**: 友好的错误处理和提示

## 设计模式

### 1. 组件模式 (Component Pattern)
- **功能封装**: 将用户邀请功能封装在组件中
- **状态管理**: 管理组件的内部状态
- **事件处理**: 处理用户交互事件

### 2. 服务模式 (Service Pattern)
- **服务集成**: 集成多个系统服务
- **依赖注入**: 通过钩子注入服务依赖
- **接口统一**: 提供统一的服务接口

### 3. 验证模式 (Validation Pattern)
- **输入验证**: 验证用户输入的邮箱地址
- **批量验证**: 批量验证多个输入
- **错误收集**: 收集和报告验证错误

### 4. 状态模式 (State Pattern)
- **邀请状态**: 管理邀请过程的不同状态
- **状态转换**: 处理状态之间的转换
- **状态反馈**: 提供状态的视觉反馈

## 注意事项

1. **邮箱验证**: 确保邮箱验证的准确性和安全性
2. **重复检查**: 避免重复邀请同一用户
3. **权限控制**: 确保只有授权用户可以邀请新用户
4. **错误处理**: 提供友好的错误处理和用户反馈

## 扩展建议

1. **角色分配**: 支持在邀请时分配用户角色
2. **批量导入**: 支持从文件批量导入邮箱地址
3. **邀请模板**: 支持自定义邀请邮件模板
4. **到期管理**: 管理邀请的到期时间
5. **统计报告**: 提供详细的邀请统计报告

该用户邀请配置组件为Odoo Web客户端提供了完整的用户邀请管理功能，通过严格的邮箱验证和批量处理确保了高效的用户管理和良好的用户体验。

# MobileAppsFunnel - 移动应用漏斗组件

## 概述

`mobile_apps_funnel.js` 是 Odoo Web 客户端的移动应用漏斗组件，提供了移动应用下载链接的显示功能。该模块包含39行代码，是一个OWL组件，专门用于在设置页面中显示移动应用的下载链接，具备设备检测、响应式显示、二维码集成、应用商店链接等特性，是Odoo Web设置表单视图中移动应用推广的重要组件。

## 文件信息
- **路径**: `/web/static/src/webclient/settings_form_view/widgets/mobile_apps_funnel.js`
- **行数**: 39
- **模块**: `@web/webclient/settings_form_view/widgets/mobile_apps_funnel`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                            // 注册表系统
'@odoo/owl'                                     // OWL框架
'@web/core/browser/feature_detection'           // 浏览器特性检测
'@web/views/form/setting/setting'              // 设置组件
'@web/views/widgets/standard_widget_props'     // 标准组件属性
```

## 主组件定义

### 1. MobileAppsFunnel 组件

```javascript
/**
 * Widget in the settings that displays links to the mobile apps.
 * As a QRCode in Desktop mode, as an image in mobile mode.
 * Both are clickable links.
 */
class MobileAppsFunnel extends Component {
    static template = "web.MobileAppsFunnel";
    static components = {
        Setting,
    };
    static props = { ...standardWidgetProps };
    
    setup() {
        this.iosAppstoreImagePath = isMobileOS()
            ? "/web/static/img/app_store.png"
            : "/web/static/img/mobile_app_qrcode_ios.svg";
        this.androidAppstoreImagePath = isMobileOS()
            ? "/web/static/img/google_play.png"
            : "/web/static/img/mobile_app_qrcode_android.svg";
    }
}
```

**组件特性**:
- **响应式显示**: 根据设备类型显示不同的图片
- **二维码集成**: 桌面端显示二维码，移动端显示应用商店图标
- **设置集成**: 集成Setting组件作为容器
- **标准属性**: 使用标准组件属性

## 核心属性

### 1. 组件属性

```javascript
// 模板配置
static template = "web.MobileAppsFunnel";

// 子组件
static components = {
    Setting,
};

// 属性定义
static props = { ...standardWidgetProps };
```

**属性功能**:
- **template**: 指定移动应用漏斗的模板
- **components**: 包含Setting子组件
- **props**: 继承标准组件属性

### 2. 图片路径属性

```javascript
// iOS应用商店图片路径
this.iosAppstoreImagePath = isMobileOS()
    ? "/web/static/img/app_store.png"
    : "/web/static/img/mobile_app_qrcode_ios.svg";

// Android应用商店图片路径
this.androidAppstoreImagePath = isMobileOS()
    ? "/web/static/img/google_play.png"
    : "/web/static/img/mobile_app_qrcode_android.svg";
```

**路径属性功能**:
- **iosAppstoreImagePath**: iOS应用商店的图片路径
- **androidAppstoreImagePath**: Android应用商店的图片路径
- **设备检测**: 根据设备类型选择不同的图片
- **响应式**: 移动端显示应用商店图标，桌面端显示二维码

## 核心功能

### 1. 设备检测

```javascript
this.iosAppstoreImagePath = isMobileOS()
    ? "/web/static/img/app_store.png"
    : "/web/static/img/mobile_app_qrcode_ios.svg";
```

**检测功能**:
- **移动设备检测**: 使用isMobileOS()检测移动设备
- **条件选择**: 根据设备类型选择不同的图片
- **用户体验**: 为不同设备提供最佳的用户体验
- **响应式设计**: 实现响应式的图片显示

### 2. 图片路径管理

```javascript
// 移动端图片
"/web/static/img/app_store.png"        // iOS应用商店图标
"/web/static/img/google_play.png"      // Google Play图标

// 桌面端图片
"/web/static/img/mobile_app_qrcode_ios.svg"     // iOS二维码
"/web/static/img/mobile_app_qrcode_android.svg" // Android二维码
```

**路径管理功能**:
- **静态资源**: 管理静态图片资源路径
- **平台区分**: 区分iOS和Android平台
- **格式选择**: PNG格式用于图标，SVG格式用于二维码
- **资源优化**: 选择合适的图片格式

## 组件注册

### 1. 组件定义

```javascript
const mobileAppsFunnel = {
    component: MobileAppsFunnel,
};
```

**组件配置**:
- **组件引用**: 引用MobileAppsFunnel组件
- **简单配置**: 最简化的组件配置

### 2. 注册表注册

```javascript
registry.category("view_widgets").add("mobile_apps_funnel", mobileAppsFunnel);
```

**注册功能**:
- **组件类型**: 注册为"view_widgets"类型
- **组件名称**: 使用"mobile_apps_funnel"作为名称
- **全局可用**: 在视图中全局可用

## 使用场景

### 1. 移动应用推广管理器

```javascript
// 移动应用推广管理器
class MobileAppsPromotionManager {
    constructor(env) {
        this.env = env;
        this.promotionConfig = this.getPromotionConfig();
        this.setupManager();
    }
    
    setupManager() {
        // 设置应用信息
        this.appInfo = {
            ios: {
                name: 'Odoo',
                bundleId: 'com.odoo.mobile',
                appStoreId: '1272543640',
                appStoreUrl: 'https://apps.apple.com/app/odoo/id1272543640',
                qrCodeUrl: '/web/static/img/mobile_app_qrcode_ios.svg',
                iconUrl: '/web/static/img/app_store.png',
                version: '1.0.0',
                rating: 4.5,
                reviews: 1250
            },
            android: {
                name: 'Odoo',
                packageName: 'com.odoo.mobile',
                playStoreUrl: 'https://play.google.com/store/apps/details?id=com.odoo.mobile',
                qrCodeUrl: '/web/static/img/mobile_app_qrcode_android.svg',
                iconUrl: '/web/static/img/google_play.png',
                version: '1.0.0',
                rating: 4.3,
                reviews: 2100
            }
        };
        
        this.setupEventListeners();
    }
    
    // 获取推广配置
    getPromotionConfig() {
        return {
            showQRCode: true,
            showRating: true,
            showDownloadCount: false,
            trackClicks: true,
            autoDetectDevice: true,
            fallbackToWeb: true
        };
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听应用下载点击
        this.env.bus.addEventListener('APP_DOWNLOAD_CLICKED', (event) => {
            this.handleAppDownloadClick(event.detail);
        });
        
        // 监听二维码扫描
        this.env.bus.addEventListener('QR_CODE_SCANNED', (event) => {
            this.handleQRCodeScan(event.detail);
        });
    }
    
    // 创建移动应用漏斗组件
    createMobileAppsFunnel(containerId, options = {}) {
        const config = { ...this.promotionConfig, ...options };
        
        const funnel = {
            Component: MobileAppsFunnel,
            props: {
                ...standardWidgetProps,
                showQRCode: config.showQRCode,
                showRating: config.showRating,
                trackClicks: config.trackClicks
            },
            containerId: containerId,
            config: config,
            created: Date.now()
        };
        
        return funnel;
    }
    
    // 获取设备特定的应用信息
    getDeviceSpecificAppInfo() {
        const userAgent = navigator.userAgent;
        
        if (/iPad|iPhone|iPod/.test(userAgent)) {
            return {
                platform: 'ios',
                ...this.appInfo.ios,
                isNativeDevice: true
            };
        } else if (/Android/.test(userAgent)) {
            return {
                platform: 'android',
                ...this.appInfo.android,
                isNativeDevice: true
            };
        } else {
            return {
                platform: 'desktop',
                isNativeDevice: false,
                showBothPlatforms: true
            };
        }
    }
    
    // 处理应用下载点击
    handleAppDownloadClick(clickData) {
        const { platform, source } = clickData;
        
        // 记录点击事件
        if (this.promotionConfig.trackClicks) {
            this.trackDownloadClick(platform, source);
        }
        
        // 获取应用信息
        const appInfo = this.appInfo[platform];
        if (!appInfo) {
            console.warn(`Unknown platform: ${platform}`);
            return;
        }
        
        // 打开应用商店
        this.openAppStore(appInfo, platform);
    }
    
    // 打开应用商店
    openAppStore(appInfo, platform) {
        try {
            // 尝试打开原生应用商店
            if (this.isNativeDevice(platform)) {
                this.openNativeAppStore(appInfo, platform);
            } else {
                // 在新窗口中打开网页版应用商店
                window.open(appInfo.appStoreUrl || appInfo.playStoreUrl, '_blank');
            }
        } catch (error) {
            console.error('Failed to open app store:', error);
            
            // 回退到网页版
            if (this.promotionConfig.fallbackToWeb) {
                window.open(appInfo.appStoreUrl || appInfo.playStoreUrl, '_blank');
            }
        }
    }
    
    // 打开原生应用商店
    openNativeAppStore(appInfo, platform) {
        let appStoreUrl;
        
        if (platform === 'ios') {
            // iOS App Store URL scheme
            appStoreUrl = `itms-apps://apps.apple.com/app/id${appInfo.appStoreId}`;
        } else if (platform === 'android') {
            // Google Play URL scheme
            appStoreUrl = `market://details?id=${appInfo.packageName}`;
        }
        
        if (appStoreUrl) {
            window.location.href = appStoreUrl;
        }
    }
    
    // 检查是否为原生设备
    isNativeDevice(platform) {
        const userAgent = navigator.userAgent;
        
        if (platform === 'ios') {
            return /iPad|iPhone|iPod/.test(userAgent);
        } else if (platform === 'android') {
            return /Android/.test(userAgent);
        }
        
        return false;
    }
    
    // 跟踪下载点击
    trackDownloadClick(platform, source) {
        const trackingData = {
            event: 'app_download_click',
            platform: platform,
            source: source,
            timestamp: Date.now(),
            userAgent: navigator.userAgent,
            referrer: document.referrer
        };
        
        // 发送跟踪数据
        this.sendTrackingData(trackingData);
    }
    
    // 发送跟踪数据
    async sendTrackingData(data) {
        try {
            await this.env.services.rpc('/web/track_mobile_app_promotion', {
                tracking_data: data
            });
        } catch (error) {
            console.warn('Failed to send tracking data:', error);
        }
    }
    
    // 处理二维码扫描
    handleQRCodeScan(scanData) {
        const { platform, scanMethod } = scanData;
        
        // 记录扫描事件
        this.trackQRCodeScan(platform, scanMethod);
        
        // 显示扫描成功消息
        this.showScanSuccessMessage(platform);
    }
    
    // 跟踪二维码扫描
    trackQRCodeScan(platform, scanMethod) {
        const trackingData = {
            event: 'qr_code_scan',
            platform: platform,
            scan_method: scanMethod,
            timestamp: Date.now()
        };
        
        this.sendTrackingData(trackingData);
    }
    
    // 显示扫描成功消息
    showScanSuccessMessage(platform) {
        const appInfo = this.appInfo[platform];
        const message = `QR code scanned! You will be redirected to download ${appInfo.name} for ${platform}.`;
        
        this.env.services.notification.add(message, {
            type: 'success',
            sticky: false
        });
    }
    
    // 生成二维码
    generateQRCode(platform, size = 200) {
        const appInfo = this.appInfo[platform];
        const url = appInfo.appStoreUrl || appInfo.playStoreUrl;
        
        // 使用第三方二维码生成服务
        const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(url)}`;
        
        return qrCodeUrl;
    }
    
    // 获取应用统计信息
    async getAppStatistics() {
        try {
            const stats = await this.env.services.rpc('/web/mobile_app_statistics');
            
            return {
                totalDownloads: stats.total_downloads || 0,
                downloadsByPlatform: stats.downloads_by_platform || {},
                qrCodeScans: stats.qr_code_scans || 0,
                conversionRate: stats.conversion_rate || 0,
                topSources: stats.top_sources || [],
                lastUpdated: stats.last_updated
            };
        } catch (error) {
            console.error('Failed to get app statistics:', error);
            return null;
        }
    }
    
    // 获取应用评分
    async getAppRatings() {
        try {
            const ratings = await this.env.services.rpc('/web/mobile_app_ratings');
            
            return {
                ios: {
                    rating: ratings.ios_rating || 0,
                    reviews: ratings.ios_reviews || 0,
                    lastUpdated: ratings.ios_last_updated
                },
                android: {
                    rating: ratings.android_rating || 0,
                    reviews: ratings.android_reviews || 0,
                    lastUpdated: ratings.android_last_updated
                }
            };
        } catch (error) {
            console.error('Failed to get app ratings:', error);
            return null;
        }
    }
    
    // 创建推广横幅
    createPromotionBanner(options = {}) {
        const deviceInfo = this.getDeviceSpecificAppInfo();
        
        const banner = {
            type: 'mobile_app_promotion',
            platform: deviceInfo.platform,
            message: this.getPromotionMessage(deviceInfo),
            actionText: this.getActionText(deviceInfo),
            iconUrl: this.getIconUrl(deviceInfo),
            clickHandler: () => this.handleBannerClick(deviceInfo),
            dismissible: options.dismissible !== false,
            persistent: options.persistent || false
        };
        
        return banner;
    }
    
    // 获取推广消息
    getPromotionMessage(deviceInfo) {
        if (deviceInfo.isNativeDevice) {
            return `Get the Odoo mobile app for ${deviceInfo.platform}!`;
        } else {
            return 'Get the Odoo mobile app for iOS and Android!';
        }
    }
    
    // 获取操作文本
    getActionText(deviceInfo) {
        if (deviceInfo.isNativeDevice) {
            return 'Download Now';
        } else {
            return 'Scan QR Code';
        }
    }
    
    // 获取图标URL
    getIconUrl(deviceInfo) {
        if (deviceInfo.isNativeDevice) {
            return deviceInfo.iconUrl;
        } else {
            return '/web/static/img/mobile_app_icon.png';
        }
    }
    
    // 处理横幅点击
    handleBannerClick(deviceInfo) {
        if (deviceInfo.isNativeDevice) {
            this.handleAppDownloadClick({
                platform: deviceInfo.platform,
                source: 'promotion_banner'
            });
        } else {
            // 显示二维码对话框
            this.showQRCodeDialog();
        }
    }
    
    // 显示二维码对话框
    showQRCodeDialog() {
        this.env.services.dialog.add(QRCodeDialog, {
            title: 'Download Odoo Mobile App',
            iosQRCode: this.appInfo.ios.qrCodeUrl,
            androidQRCode: this.appInfo.android.qrCodeUrl,
            iosUrl: this.appInfo.ios.appStoreUrl,
            androidUrl: this.appInfo.android.playStoreUrl
        });
    }
}

// 使用示例
const promotionManager = new MobileAppsPromotionManager(env);

// 创建移动应用漏斗
const funnel = promotionManager.createMobileAppsFunnel('mobile-apps-container');

// 获取设备特定信息
const deviceInfo = promotionManager.getDeviceSpecificAppInfo();
console.log('Device info:', deviceInfo);

// 获取应用统计
const stats = await promotionManager.getAppStatistics();
console.log('App statistics:', stats);

// 创建推广横幅
const banner = promotionManager.createPromotionBanner({
    dismissible: true,
    persistent: false
});
```

## 技术特点

### 1. 响应式设计
- **设备检测**: 使用isMobileOS()检测移动设备
- **条件渲染**: 根据设备类型渲染不同内容
- **用户体验**: 为不同设备提供最佳体验
- **图片优化**: 选择合适的图片格式和尺寸

### 2. 资源管理
- **静态资源**: 管理静态图片资源
- **路径配置**: 配置不同平台的图片路径
- **格式选择**: 选择合适的图片格式
- **性能优化**: 优化资源加载性能

### 3. 组件集成
- **Setting组件**: 集成Setting组件作为容器
- **标准属性**: 使用标准组件属性
- **模板驱动**: 使用模板定义组件结构
- **组件注册**: 注册为视图组件

### 4. 平台支持
- **iOS支持**: 支持iOS应用商店
- **Android支持**: 支持Google Play商店
- **二维码**: 桌面端显示二维码
- **直接链接**: 移动端显示直接链接

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- **显示策略**: 根据设备类型选择显示策略
- **图片策略**: 不同设备使用不同的图片
- **交互策略**: 不同的用户交互策略

### 2. 工厂模式 (Factory Pattern)
- **组件创建**: 统一的组件创建机制
- **配置驱动**: 基于配置创建组件
- **平台适配**: 为不同平台创建适配的组件

### 3. 适配器模式 (Adapter Pattern)
- **设备适配**: 适配不同的设备类型
- **平台适配**: 适配不同的应用平台
- **接口统一**: 提供统一的组件接口

## 注意事项

1. **设备检测**: 确保正确检测移动设备
2. **图片资源**: 确保图片资源的可用性
3. **链接有效性**: 确保应用商店链接的有效性
4. **用户体验**: 提供良好的用户交互体验

## 扩展建议

1. **动态链接**: 支持动态的应用商店链接
2. **统计跟踪**: 添加下载统计和跟踪功能
3. **A/B测试**: 支持不同版本的A/B测试
4. **本地化**: 支持多语言和本地化
5. **深度链接**: 支持应用的深度链接功能

该移动应用漏斗组件为Odoo Web客户端提供了智能的移动应用推广功能，通过响应式设计和设备检测确保了在不同设备上的最佳用户体验。

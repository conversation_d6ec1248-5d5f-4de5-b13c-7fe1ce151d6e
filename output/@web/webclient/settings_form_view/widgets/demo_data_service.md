# DemoDataService - 演示数据服务

## 概述

`demo_data_service.js` 是 Odoo Web 客户端的演示数据服务，提供了检测和管理演示数据状态的功能。该模块包含24行代码，是一个服务组件，专门用于检查系统中是否启用了演示数据，具备RPC调用、状态缓存、服务注册等特性，是Odoo Web设置表单视图中演示数据管理的核心服务。

## 文件信息
- **路径**: `/web/static/src/webclient/settings_form_view/widgets/demo_data_service.js`
- **行数**: 24
- **模块**: `@web/webclient/settings_form_view/widgets/demo_data_service`

## 依赖关系

```javascript
// 核心依赖
'@web/core/network/rpc'         // RPC网络服务
'@web/core/registry'            // 注册表系统
```

## 主服务定义

### 1. DemoDataService 服务

```javascript
const demoDataService = {
    async start() {
        let isDemoDataActiveProm;
        return {
            isDemoDataActive() {
                if (!isDemoDataActiveProm) {
                    isDemoDataActiveProm = rpc("/base_setup/demo_active");
                }
                return isDemoDataActiveProm;
            },
        };
    },
};
```

**服务特性**:
- **异步启动**: 异步服务启动机制
- **状态检测**: 检测演示数据的激活状态
- **结果缓存**: 缓存RPC调用结果避免重复请求
- **Promise管理**: 使用Promise管理异步操作

## 核心功能

### 1. 服务启动

```javascript
async start() {
    let isDemoDataActiveProm;
    return {
        isDemoDataActive() {
            if (!isDemoDataActiveProm) {
                isDemoDataActiveProm = rpc("/base_setup/demo_active");
            }
            return isDemoDataActiveProm;
        },
    };
}
```

**启动功能**:
- **异步初始化**: 异步启动服务
- **Promise变量**: 声明Promise缓存变量
- **方法返回**: 返回服务方法对象
- **闭包缓存**: 使用闭包缓存Promise结果

### 2. 演示数据状态检测

```javascript
isDemoDataActive() {
    if (!isDemoDataActiveProm) {
        isDemoDataActiveProm = rpc("/base_setup/demo_active");
    }
    return isDemoDataActiveProm;
}
```

**检测功能**:
- **缓存检查**: 检查是否已有缓存的Promise
- **RPC调用**: 调用后端API检查演示数据状态
- **结果缓存**: 缓存Promise避免重复调用
- **Promise返回**: 返回Promise对象供调用者使用

### 3. 服务注册

```javascript
registry.category("services").add("demo_data", demoDataService);
```

**注册功能**:
- **服务注册**: 将服务注册到服务注册表
- **类别指定**: 注册到"services"类别
- **名称标识**: 使用"demo_data"作为服务名称
- **全局可用**: 在整个应用中可用

## 使用场景

### 1. 演示数据管理器

```javascript
// 演示数据管理器
class DemoDataManager {
    constructor(env) {
        this.env = env;
        this.demoDataService = env.services.demo_data;
        this.demoDataState = {
            isActive: null,
            isLoading: false,
            lastChecked: null,
            error: null
        };
        this.setupManager();
    }
    
    setupManager() {
        // 设置演示数据配置
        this.demoDataConfig = {
            checkInterval: 5 * 60 * 1000, // 5分钟检查一次
            cacheTimeout: 10 * 60 * 1000, // 10分钟缓存超时
            retryAttempts: 3,
            retryDelay: 1000
        };
        
        this.setupEventListeners();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听应用启动
        this.env.bus.addEventListener('APP_STARTED', () => {
            this.checkDemoDataStatus();
        });
        
        // 监听设置变更
        this.env.bus.addEventListener('SETTINGS_CHANGED', (event) => {
            if (event.detail.module === 'base_setup') {
                this.refreshDemoDataStatus();
            }
        });
    }
    
    // 检查演示数据状态
    async checkDemoDataStatus() {
        if (this.demoDataState.isLoading) {
            return this.demoDataState;
        }
        
        // 检查缓存是否有效
        if (this.isCacheValid()) {
            return this.demoDataState;
        }
        
        this.demoDataState.isLoading = true;
        this.demoDataState.error = null;
        
        try {
            const isActive = await this.demoDataService.isDemoDataActive();
            
            this.demoDataState.isActive = isActive;
            this.demoDataState.lastChecked = Date.now();
            this.demoDataState.isLoading = false;
            
            // 触发状态更新事件
            this.env.bus.trigger('DEMO_DATA_STATUS_UPDATED', {
                isActive: isActive,
                timestamp: this.demoDataState.lastChecked
            });
            
            return this.demoDataState;
            
        } catch (error) {
            console.error('Failed to check demo data status:', error);
            
            this.demoDataState.error = error;
            this.demoDataState.isLoading = false;
            
            // 触发错误事件
            this.env.bus.trigger('DEMO_DATA_STATUS_ERROR', {
                error: error,
                timestamp: Date.now()
            });
            
            throw error;
        }
    }
    
    // 检查缓存是否有效
    isCacheValid() {
        if (!this.demoDataState.lastChecked) {
            return false;
        }
        
        const now = Date.now();
        const elapsed = now - this.demoDataState.lastChecked;
        
        return elapsed < this.demoDataConfig.cacheTimeout;
    }
    
    // 刷新演示数据状态
    async refreshDemoDataStatus() {
        // 清除缓存
        this.demoDataState.lastChecked = null;
        
        // 重新检查状态
        return await this.checkDemoDataStatus();
    }
    
    // 获取演示数据状态
    getDemoDataStatus() {
        return {
            ...this.demoDataState,
            isCacheValid: this.isCacheValid()
        };
    }
    
    // 启用演示数据
    async enableDemoData() {
        try {
            await this.env.services.rpc('/base_setup/enable_demo_data');
            
            // 刷新状态
            await this.refreshDemoDataStatus();
            
            this.env.services.notification.add(
                'Demo data has been enabled successfully',
                { type: 'success' }
            );
            
            return true;
            
        } catch (error) {
            console.error('Failed to enable demo data:', error);
            
            this.env.services.notification.add(
                'Failed to enable demo data: ' + error.message,
                { type: 'danger' }
            );
            
            return false;
        }
    }
    
    // 禁用演示数据
    async disableDemoData() {
        try {
            // 确认操作
            const confirmed = await this.confirmDisableDemoData();
            if (!confirmed) {
                return false;
            }
            
            await this.env.services.rpc('/base_setup/disable_demo_data');
            
            // 刷新状态
            await this.refreshDemoDataStatus();
            
            this.env.services.notification.add(
                'Demo data has been disabled successfully',
                { type: 'success' }
            );
            
            return true;
            
        } catch (error) {
            console.error('Failed to disable demo data:', error);
            
            this.env.services.notification.add(
                'Failed to disable demo data: ' + error.message,
                { type: 'danger' }
            );
            
            return false;
        }
    }
    
    // 确认禁用演示数据
    async confirmDisableDemoData() {
        return new Promise((resolve) => {
            this.env.services.dialog.add(ConfirmationDialog, {
                title: 'Disable Demo Data',
                body: 'Are you sure you want to disable demo data? This action cannot be undone and will remove all demo records from the system.',
                confirm: () => resolve(true),
                cancel: () => resolve(false)
            });
        });
    }
    
    // 重置演示数据
    async resetDemoData() {
        try {
            // 确认操作
            const confirmed = await this.confirmResetDemoData();
            if (!confirmed) {
                return false;
            }
            
            await this.env.services.rpc('/base_setup/reset_demo_data');
            
            // 刷新状态
            await this.refreshDemoDataStatus();
            
            this.env.services.notification.add(
                'Demo data has been reset successfully',
                { type: 'success' }
            );
            
            return true;
            
        } catch (error) {
            console.error('Failed to reset demo data:', error);
            
            this.env.services.notification.add(
                'Failed to reset demo data: ' + error.message,
                { type: 'danger' }
            );
            
            return false;
        }
    }
    
    // 确认重置演示数据
    async confirmResetDemoData() {
        return new Promise((resolve) => {
            this.env.services.dialog.add(ConfirmationDialog, {
                title: 'Reset Demo Data',
                body: 'Are you sure you want to reset demo data? This will remove all existing demo records and create new ones.',
                confirm: () => resolve(true),
                cancel: () => resolve(false)
            });
        });
    }
    
    // 获取演示数据统计
    async getDemoDataStatistics() {
        try {
            const stats = await this.env.services.rpc('/base_setup/demo_data_stats');
            
            return {
                totalRecords: stats.total_records || 0,
                recordsByModel: stats.records_by_model || {},
                createdDate: stats.created_date,
                lastModified: stats.last_modified,
                size: stats.size || 0,
                sizeFormatted: this.formatSize(stats.size || 0)
            };
            
        } catch (error) {
            console.error('Failed to get demo data statistics:', error);
            return null;
        }
    }
    
    // 格式化大小
    formatSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    // 导出演示数据
    async exportDemoData() {
        try {
            const exportData = await this.env.services.rpc('/base_setup/export_demo_data');
            
            // 创建下载链接
            const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                type: 'application/json'
            });
            
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `demo_data_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            URL.revokeObjectURL(url);
            
            this.env.services.notification.add(
                'Demo data exported successfully',
                { type: 'success' }
            );
            
            return true;
            
        } catch (error) {
            console.error('Failed to export demo data:', error);
            
            this.env.services.notification.add(
                'Failed to export demo data: ' + error.message,
                { type: 'danger' }
            );
            
            return false;
        }
    }
    
    // 导入演示数据
    async importDemoData(file) {
        try {
            const fileContent = await this.readFile(file);
            const demoData = JSON.parse(fileContent);
            
            await this.env.services.rpc('/base_setup/import_demo_data', {
                data: demoData
            });
            
            // 刷新状态
            await this.refreshDemoDataStatus();
            
            this.env.services.notification.add(
                'Demo data imported successfully',
                { type: 'success' }
            );
            
            return true;
            
        } catch (error) {
            console.error('Failed to import demo data:', error);
            
            this.env.services.notification.add(
                'Failed to import demo data: ' + error.message,
                { type: 'danger' }
            );
            
            return false;
        }
    }
    
    // 读取文件
    readFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(e);
            reader.readAsText(file);
        });
    }
    
    // 启动定期检查
    startPeriodicCheck() {
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
        }
        
        this.checkInterval = setInterval(() => {
            this.checkDemoDataStatus();
        }, this.demoDataConfig.checkInterval);
    }
    
    // 停止定期检查
    stopPeriodicCheck() {
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
        }
    }
    
    // 清理资源
    destroy() {
        this.stopPeriodicCheck();
        
        // 清理事件监听器
        this.env.bus.removeEventListener('APP_STARTED');
        this.env.bus.removeEventListener('SETTINGS_CHANGED');
    }
}

// 使用示例
const demoDataManager = new DemoDataManager(env);

// 检查演示数据状态
const status = await demoDataManager.checkDemoDataStatus();
console.log('Demo data status:', status);

// 获取演示数据统计
const stats = await demoDataManager.getDemoDataStatistics();
console.log('Demo data statistics:', stats);

// 启用演示数据
await demoDataManager.enableDemoData();

// 启动定期检查
demoDataManager.startPeriodicCheck();
```

### 2. 演示数据组件集成

```javascript
// 演示数据组件集成示例
class DemoDataComponent extends Component {
    static template = xml`
        <div class="o_demo_data_widget">
            <div class="o_demo_data_status">
                <span t-if="state.isLoading" class="fa fa-spinner fa-spin"/>
                <span t-elif="state.isActive" class="text-success">
                    <i class="fa fa-check"/> Demo Data Active
                </span>
                <span t-else="" class="text-muted">
                    <i class="fa fa-times"/> Demo Data Inactive
                </span>
            </div>
            
            <div class="o_demo_data_actions" t-if="!state.isLoading">
                <button t-if="!state.isActive" 
                        class="btn btn-primary" 
                        t-on-click="enableDemoData">
                    Enable Demo Data
                </button>
                <button t-if="state.isActive" 
                        class="btn btn-warning" 
                        t-on-click="disableDemoData">
                    Disable Demo Data
                </button>
                <button t-if="state.isActive" 
                        class="btn btn-secondary" 
                        t-on-click="resetDemoData">
                    Reset Demo Data
                </button>
            </div>
            
            <div class="o_demo_data_stats" t-if="state.stats">
                <h6>Demo Data Statistics</h6>
                <ul>
                    <li>Total Records: <t t-esc="state.stats.totalRecords"/></li>
                    <li>Size: <t t-esc="state.stats.sizeFormatted"/></li>
                    <li>Last Modified: <t t-esc="state.stats.lastModified"/></li>
                </ul>
            </div>
        </div>`;
    
    setup() {
        this.demoDataService = this.env.services.demo_data;
        this.demoDataManager = new DemoDataManager(this.env);
        
        this.state = useState({
            isActive: null,
            isLoading: false,
            stats: null,
            error: null
        });
        
        onMounted(() => {
            this.loadDemoDataStatus();
        });
        
        onWillUnmount(() => {
            this.demoDataManager.destroy();
        });
    }
    
    async loadDemoDataStatus() {
        this.state.isLoading = true;
        
        try {
            const status = await this.demoDataManager.checkDemoDataStatus();
            this.state.isActive = status.isActive;
            
            if (status.isActive) {
                this.state.stats = await this.demoDataManager.getDemoDataStatistics();
            }
            
        } catch (error) {
            this.state.error = error;
        } finally {
            this.state.isLoading = false;
        }
    }
    
    async enableDemoData() {
        const success = await this.demoDataManager.enableDemoData();
        if (success) {
            await this.loadDemoDataStatus();
        }
    }
    
    async disableDemoData() {
        const success = await this.demoDataManager.disableDemoData();
        if (success) {
            await this.loadDemoDataStatus();
        }
    }
    
    async resetDemoData() {
        const success = await this.demoDataManager.resetDemoData();
        if (success) {
            await this.loadDemoDataStatus();
        }
    }
}
```

## 技术特点

### 1. 服务架构
- **异步启动**: 异步服务启动机制
- **方法返回**: 返回服务方法对象
- **闭包缓存**: 使用闭包缓存状态
- **Promise管理**: 统一的Promise管理

### 2. 缓存机制
- **结果缓存**: 缓存RPC调用结果
- **避免重复**: 避免重复的网络请求
- **性能优化**: 提高应用性能
- **内存管理**: 合理的内存使用

### 3. RPC集成
- **网络调用**: 集成RPC网络服务
- **端点调用**: 调用特定的API端点
- **错误处理**: 处理网络错误
- **异步操作**: 异步的网络操作

### 4. 服务注册
- **注册表**: 注册到服务注册表
- **全局可用**: 在整个应用中可用
- **依赖注入**: 支持依赖注入
- **生命周期**: 管理服务生命周期

## 设计模式

### 1. 服务模式 (Service Pattern)
- **服务封装**: 将演示数据逻辑封装为服务
- **接口统一**: 提供统一的服务接口
- **依赖注入**: 支持依赖注入机制

### 2. 单例模式 (Singleton Pattern)
- **全局唯一**: 服务在应用中全局唯一
- **状态共享**: 共享演示数据状态
- **资源管理**: 统一管理资源

### 3. 缓存模式 (Cache Pattern)
- **结果缓存**: 缓存API调用结果
- **性能优化**: 减少网络请求
- **智能失效**: 智能的缓存失效机制

### 4. 代理模式 (Proxy Pattern)
- **API代理**: 代理后端API调用
- **透明访问**: 提供透明的访问接口
- **错误处理**: 统一的错误处理

## 注意事项

1. **网络依赖**: 依赖网络连接进行状态检查
2. **缓存管理**: 合理管理Promise缓存
3. **错误处理**: 处理网络请求失败的情况
4. **性能考虑**: 避免频繁的API调用

## 扩展建议

1. **状态监听**: 添加演示数据状态变化监听
2. **批量操作**: 支持批量演示数据操作
3. **配置管理**: 支持演示数据配置管理
4. **统计信息**: 提供详细的演示数据统计
5. **导入导出**: 支持演示数据的导入导出

该演示数据服务为Odoo Web客户端提供了简洁高效的演示数据状态检测功能，通过智能的缓存机制和RPC集成确保了良好的性能和用户体验。

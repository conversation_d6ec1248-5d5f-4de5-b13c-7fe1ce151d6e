# ResConfigEdition - 版本信息配置组件

## 概述

`res_config_edition.js` 是 Odoo Web 客户端的版本信息配置组件，提供了系统版本和许可证信息的显示功能。该模块包含38行代码，是一个OWL组件，专门用于在设置页面的"关于"部分显示Odoo版本、数据库到期日期和版权信息，具备会话信息集成、日期格式化、版本显示等特性，是Odoo Web设置表单视图中系统信息展示的重要组件。

## 文件信息
- **路径**: `/web/static/src/webclient/settings_form_view/widgets/res_config_edition.js`
- **行数**: 38
- **模块**: `@web/webclient/settings_form_view/widgets/res_config_edition`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                            // 注册表系统
'@web/session'                                  // 会话服务
'@web/views/form/setting/setting'              // 设置组件
'@odoo/owl'                                     // OWL框架
'@web/views/widgets/standard_widget_props'     // 标准组件属性

// 外部依赖
luxon                                           // 日期时间库
```

## 主组件定义

### 1. ResConfigEdition 组件

```javascript
/**
 * Widget in the settings that handles a part of the "About" section.
 * Contains info about the odoo version, database expiration date and copyrights.
 */
class ResConfigEdition extends Component {
    static template = "res_config_edition";
    static components = { Setting };
    static props = {
        ...standardWidgetProps,
    };

    setup() {
        this.serverVersion = session.server_version;
        this.expirationDate = session.expiration_date
            ? DateTime.fromSQL(session.expiration_date).toLocaleString(DateTime.DATE_FULL)
            : DateTime.now().plus({ days: 30 }).toLocaleString(DateTime.DATE_FULL);
    }
}
```

**组件特性**:
- **版本信息显示**: 显示Odoo服务器版本信息
- **到期日期管理**: 管理和显示数据库到期日期
- **日期格式化**: 使用Luxon库格式化日期
- **会话集成**: 集成会话服务获取系统信息

## 核心属性

### 1. 组件属性

```javascript
// 模板配置
static template = "res_config_edition";

// 子组件
static components = { Setting };

// 属性定义
static props = {
    ...standardWidgetProps,
};
```

**属性功能**:
- **template**: 指定版本信息配置的模板
- **components**: 包含Setting子组件
- **props**: 继承标准组件属性

### 2. 版本信息属性

```javascript
// 服务器版本
this.serverVersion = session.server_version;

// 到期日期
this.expirationDate = session.expiration_date
    ? DateTime.fromSQL(session.expiration_date).toLocaleString(DateTime.DATE_FULL)
    : DateTime.now().plus({ days: 30 }).toLocaleString(DateTime.DATE_FULL);
```

**信息属性功能**:
- **serverVersion**: 服务器版本信息
- **expirationDate**: 格式化的到期日期

## 核心功能

### 1. 版本信息获取

```javascript
this.serverVersion = session.server_version;
```

**获取功能**:
- **会话访问**: 从会话对象获取版本信息
- **版本缓存**: 缓存版本信息供组件使用
- **信息展示**: 为模板提供版本信息
- **系统标识**: 标识当前系统版本

### 2. 到期日期处理

```javascript
this.expirationDate = session.expiration_date
    ? DateTime.fromSQL(session.expiration_date).toLocaleString(DateTime.DATE_FULL)
    : DateTime.now().plus({ days: 30 }).toLocaleString(DateTime.DATE_FULL);
```

**日期处理功能**:
- **条件处理**: 根据是否有到期日期进行条件处理
- **SQL日期解析**: 解析SQL格式的日期
- **默认日期**: 提供30天后的默认到期日期
- **本地化格式**: 使用本地化的完整日期格式

### 3. 日期格式化

```javascript
DateTime.fromSQL(session.expiration_date).toLocaleString(DateTime.DATE_FULL)
DateTime.now().plus({ days: 30 }).toLocaleString(DateTime.DATE_FULL)
```

**格式化功能**:
- **Luxon集成**: 使用Luxon库进行日期处理
- **SQL解析**: 解析SQL格式的日期字符串
- **本地化**: 根据用户区域设置格式化日期
- **完整格式**: 使用完整的日期格式显示

## 组件注册

### 1. 组件定义

```javascript
const resConfigEdition = {
    component: ResConfigEdition,
};
```

**组件配置**:
- **组件引用**: 引用ResConfigEdition组件
- **简单配置**: 最简化的组件配置

### 2. 注册表注册

```javascript
registry.category("view_widgets").add("res_config_edition", resConfigEdition);
```

**注册功能**:
- **组件类型**: 注册为"view_widgets"类型
- **组件名称**: 使用"res_config_edition"作为名称
- **全局可用**: 在视图中全局可用

## 使用场景

### 1. 系统信息管理器

```javascript
// 系统信息管理器
class SystemInfoManager {
    constructor(env) {
        this.env = env;
        this.session = env.services.session || session;
        this.setupManager();
    }
    
    setupManager() {
        // 设置系统信息类型
        this.infoTypes = {
            version: {
                name: 'Version Information',
                fields: ['server_version', 'client_version', 'database_version'],
                format: 'text'
            },
            license: {
                name: 'License Information',
                fields: ['edition', 'expiration_date', 'users_limit'],
                format: 'structured'
            },
            database: {
                name: 'Database Information',
                fields: ['db_name', 'db_size', 'creation_date'],
                format: 'technical'
            },
            enterprise: {
                name: 'Enterprise Information',
                fields: ['subscription_code', 'support_level', 'contract_date'],
                format: 'business'
            }
        };
        
        this.setupEventListeners();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听会话更新
        this.env.bus.addEventListener('SESSION_UPDATED', (event) => {
            this.handleSessionUpdate(event.detail);
        });
        
        // 监听许可证变更
        this.env.bus.addEventListener('LICENSE_CHANGED', (event) => {
            this.handleLicenseChange(event.detail);
        });
    }
    
    // 获取完整系统信息
    getSystemInfo() {
        const sessionData = this.session;
        
        return {
            version: this.getVersionInfo(sessionData),
            license: this.getLicenseInfo(sessionData),
            database: this.getDatabaseInfo(sessionData),
            enterprise: this.getEnterpriseInfo(sessionData),
            timestamp: Date.now()
        };
    }
    
    // 获取版本信息
    getVersionInfo(sessionData) {
        return {
            serverVersion: sessionData.server_version || 'Unknown',
            serverVersionInfo: this.parseVersionString(sessionData.server_version),
            clientVersion: this.getClientVersion(),
            databaseVersion: sessionData.db_version || 'Unknown',
            pythonVersion: sessionData.python_version || 'Unknown',
            platform: sessionData.platform || 'Unknown'
        };
    }
    
    // 解析版本字符串
    parseVersionString(versionString) {
        if (!versionString) return null;
        
        const versionRegex = /(\d+)\.(\d+)\.(\d+)(?:-(\w+))?(?:\+(\w+))?/;
        const match = versionString.match(versionRegex);
        
        if (match) {
            return {
                major: parseInt(match[1]),
                minor: parseInt(match[2]),
                patch: parseInt(match[3]),
                prerelease: match[4] || null,
                build: match[5] || null,
                full: versionString
            };
        }
        
        return { full: versionString };
    }
    
    // 获取客户端版本
    getClientVersion() {
        return {
            odoo: odoo.info?.version || 'Unknown',
            browser: this.getBrowserInfo(),
            userAgent: navigator.userAgent
        };
    }
    
    // 获取浏览器信息
    getBrowserInfo() {
        const ua = navigator.userAgent;
        let browser = 'Unknown';
        let version = 'Unknown';
        
        if (ua.includes('Chrome')) {
            browser = 'Chrome';
            version = ua.match(/Chrome\/(\d+)/)?.[1] || 'Unknown';
        } else if (ua.includes('Firefox')) {
            browser = 'Firefox';
            version = ua.match(/Firefox\/(\d+)/)?.[1] || 'Unknown';
        } else if (ua.includes('Safari')) {
            browser = 'Safari';
            version = ua.match(/Version\/(\d+)/)?.[1] || 'Unknown';
        } else if (ua.includes('Edge')) {
            browser = 'Edge';
            version = ua.match(/Edge\/(\d+)/)?.[1] || 'Unknown';
        }
        
        return { name: browser, version: version };
    }
    
    // 获取许可证信息
    getLicenseInfo(sessionData) {
        const expirationDate = sessionData.expiration_date;
        const parsedDate = expirationDate ? DateTime.fromSQL(expirationDate) : null;
        
        return {
            edition: sessionData.edition || 'Community',
            isEnterprise: sessionData.is_enterprise || false,
            expirationDate: expirationDate,
            expirationDateFormatted: parsedDate ? 
                parsedDate.toLocaleString(DateTime.DATE_FULL) : null,
            daysUntilExpiration: parsedDate ? 
                Math.ceil(parsedDate.diff(DateTime.now(), 'days').days) : null,
            isExpired: parsedDate ? parsedDate < DateTime.now() : false,
            usersLimit: sessionData.users_limit || null,
            currentUsers: sessionData.current_users || null
        };
    }
    
    // 获取数据库信息
    getDatabaseInfo(sessionData) {
        return {
            name: sessionData.db || 'Unknown',
            uuid: sessionData.db_uuid || null,
            creationDate: sessionData.db_creation_date || null,
            size: sessionData.db_size || null,
            sizeFormatted: sessionData.db_size ? 
                this.formatBytes(sessionData.db_size) : null,
            charset: sessionData.db_charset || 'Unknown',
            collation: sessionData.db_collation || 'Unknown'
        };
    }
    
    // 获取企业版信息
    getEnterpriseInfo(sessionData) {
        if (!sessionData.is_enterprise) {
            return null;
        }
        
        return {
            subscriptionCode: sessionData.subscription_code || null,
            supportLevel: sessionData.support_level || 'Standard',
            contractDate: sessionData.contract_date || null,
            partnerId: sessionData.partner_id || null,
            partnerName: sessionData.partner_name || null,
            maintenanceContract: sessionData.maintenance_contract || false
        };
    }
    
    // 格式化字节数
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    // 检查许可证状态
    checkLicenseStatus() {
        const licenseInfo = this.getLicenseInfo(this.session);
        
        const status = {
            isValid: true,
            warnings: [],
            errors: [],
            recommendations: []
        };
        
        // 检查到期日期
        if (licenseInfo.expirationDate) {
            const daysUntilExpiration = licenseInfo.daysUntilExpiration;
            
            if (licenseInfo.isExpired) {
                status.isValid = false;
                status.errors.push('License has expired');
            } else if (daysUntilExpiration <= 30) {
                status.warnings.push(`License expires in ${daysUntilExpiration} days`);
            } else if (daysUntilExpiration <= 90) {
                status.recommendations.push(`License expires in ${daysUntilExpiration} days - consider renewal`);
            }
        }
        
        // 检查用户限制
        if (licenseInfo.usersLimit && licenseInfo.currentUsers) {
            const usagePercentage = (licenseInfo.currentUsers / licenseInfo.usersLimit) * 100;
            
            if (licenseInfo.currentUsers > licenseInfo.usersLimit) {
                status.errors.push('User limit exceeded');
            } else if (usagePercentage >= 90) {
                status.warnings.push('Approaching user limit');
            } else if (usagePercentage >= 75) {
                status.recommendations.push('Consider increasing user limit');
            }
        }
        
        return status;
    }
    
    // 生成系统报告
    generateSystemReport() {
        const systemInfo = this.getSystemInfo();
        const licenseStatus = this.checkLicenseStatus();
        
        const report = {
            generated: DateTime.now().toISO(),
            system: systemInfo,
            license: licenseStatus,
            summary: this.generateSummary(systemInfo, licenseStatus)
        };
        
        return report;
    }
    
    // 生成摘要
    generateSummary(systemInfo, licenseStatus) {
        const summary = [];
        
        // 版本摘要
        summary.push(`Odoo ${systemInfo.version.serverVersion}`);
        
        // 版本类型
        if (systemInfo.license.isEnterprise) {
            summary.push('Enterprise Edition');
        } else {
            summary.push('Community Edition');
        }
        
        // 许可证状态
        if (!licenseStatus.isValid) {
            summary.push('License Issues Detected');
        } else if (licenseStatus.warnings.length > 0) {
            summary.push('License Warnings');
        } else {
            summary.push('License Valid');
        }
        
        // 数据库信息
        summary.push(`Database: ${systemInfo.database.name}`);
        
        return summary.join(' | ');
    }
    
    // 导出系统报告
    exportSystemReport() {
        const report = this.generateSystemReport();
        
        const blob = new Blob([JSON.stringify(report, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `odoo_system_report_${DateTime.now().toFormat('yyyy-MM-dd')}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
    }
    
    // 处理会话更新
    handleSessionUpdate(sessionData) {
        console.log('Session updated:', sessionData);
        
        // 更新系统信息显示
        this.updateSystemInfoDisplay();
    }
    
    // 处理许可证变更
    handleLicenseChange(licenseData) {
        console.log('License changed:', licenseData);
        
        // 检查新的许可证状态
        const status = this.checkLicenseStatus();
        
        // 显示通知
        if (!status.isValid) {
            this.env.services.notification.add(
                'License issues detected. Please check your license status.',
                { type: 'danger', sticky: true }
            );
        } else if (status.warnings.length > 0) {
            this.env.services.notification.add(
                status.warnings.join(', '),
                { type: 'warning' }
            );
        }
    }
    
    // 更新系统信息显示
    updateSystemInfoDisplay() {
        const systemInfo = this.getSystemInfo();
        
        // 触发系统信息更新事件
        this.env.bus.trigger('SYSTEM_INFO_UPDATED', {
            systemInfo: systemInfo,
            timestamp: Date.now()
        });
    }
}

// 使用示例
const systemInfoManager = new SystemInfoManager(env);

// 获取系统信息
const systemInfo = systemInfoManager.getSystemInfo();
console.log('System info:', systemInfo);

// 检查许可证状态
const licenseStatus = systemInfoManager.checkLicenseStatus();
console.log('License status:', licenseStatus);

// 生成系统报告
const report = systemInfoManager.generateSystemReport();
console.log('System report:', report);

// 导出系统报告
systemInfoManager.exportSystemReport();
```

## 技术特点

### 1. 会话集成
- **会话访问**: 直接访问会话对象获取系统信息
- **版本信息**: 获取服务器版本信息
- **到期日期**: 获取数据库到期日期
- **系统状态**: 获取系统运行状态

### 2. 日期处理
- **Luxon集成**: 使用Luxon库进行日期处理
- **SQL解析**: 解析SQL格式的日期字符串
- **本地化**: 根据用户区域设置格式化日期
- **默认处理**: 提供合理的默认日期

### 3. 信息展示
- **版本显示**: 显示详细的版本信息
- **日期格式**: 使用用户友好的日期格式
- **条件显示**: 根据数据可用性条件显示
- **模板集成**: 与模板系统集成显示信息

### 4. 组件设计
- **轻量级**: 最小化的组件实现
- **标准属性**: 使用标准组件属性
- **Setting集成**: 集成Setting组件作为容器
- **模板驱动**: 使用模板定义显示结构

## 设计模式

### 1. 信息展示模式 (Information Display Pattern)
- **数据获取**: 从会话获取系统信息
- **格式化**: 格式化信息用于显示
- **模板渲染**: 通过模板渲染信息

### 2. 适配器模式 (Adapter Pattern)
- **日期适配**: 适配不同格式的日期
- **信息适配**: 适配会话信息到组件属性
- **格式转换**: 转换数据格式用于显示

### 3. 策略模式 (Strategy Pattern)
- **日期策略**: 不同的日期处理策略
- **显示策略**: 不同的信息显示策略
- **格式策略**: 不同的格式化策略

## 注意事项

1. **数据可用性**: 确保会话数据的可用性
2. **日期格式**: 正确处理不同格式的日期
3. **本地化**: 考虑不同地区的日期格式
4. **错误处理**: 处理数据缺失的情况

## 扩展建议

1. **更多信息**: 显示更多的系统信息
2. **实时更新**: 支持系统信息的实时更新
3. **导出功能**: 支持系统信息的导出
4. **许可证管理**: 添加许可证管理功能
5. **系统监控**: 集成系统监控功能

该版本信息配置组件为Odoo Web客户端提供了简洁清晰的系统信息显示功能，通过会话集成和日期格式化确保了准确的信息展示和良好的用户体验。

# UserInviteService - 用户邀请服务

## 概述

`user_invite_service.js` 是 Odoo Web 客户端的用户邀请服务，提供了用户邀请数据的获取和管理功能。该模块包含24行代码，是一个服务组件，专门用于处理用户邀请相关的数据操作，具备数据缓存、RPC调用、服务注册等特性，是Odoo Web设置表单视图中用户邀请功能的核心服务支撑。

## 文件信息
- **路径**: `/web/static/src/webclient/settings_form_view/widgets/user_invite_service.js`
- **行数**: 24
- **模块**: `@web/webclient/settings_form_view/widgets/user_invite_service`

## 依赖关系

```javascript
// 核心依赖
'@web/core/network/rpc'         // RPC网络服务
'@web/core/registry'            // 注册表系统
```

## 主服务定义

### 1. UserInviteService 服务

```javascript
const userInviteService = {
    async start() {
        let dataProm;
        return {
            fetchData(reload = false) {
                if (!dataProm || reload) {
                    dataProm = rpc("/base_setup/data");
                }
                return dataProm;
            },
        };
    },
};
```

**服务特性**:
- **异步启动**: 异步服务启动机制
- **数据获取**: 获取用户邀请相关数据
- **缓存机制**: 缓存RPC调用结果
- **重载支持**: 支持强制重新加载数据

## 核心功能

### 1. 服务启动

```javascript
async start() {
    let dataProm;
    return {
        fetchData(reload = false) {
            if (!dataProm || reload) {
                dataProm = rpc("/base_setup/data");
            }
            return dataProm;
        },
    };
}
```

**启动功能**:
- **异步初始化**: 异步启动服务
- **Promise变量**: 声明Promise缓存变量
- **方法返回**: 返回服务方法对象
- **闭包缓存**: 使用闭包缓存Promise结果

### 2. 数据获取

```javascript
fetchData(reload = false) {
    if (!dataProm || reload) {
        dataProm = rpc("/base_setup/data");
    }
    return dataProm;
}
```

**获取功能**:
- **缓存检查**: 检查是否已有缓存的Promise
- **重载参数**: 支持强制重新加载数据
- **RPC调用**: 调用后端API获取数据
- **Promise返回**: 返回Promise对象供调用者使用

### 3. 服务注册

```javascript
registry.category("services").add("user_invite", userInviteService);
```

**注册功能**:
- **服务注册**: 将服务注册到服务注册表
- **类别指定**: 注册到"services"类别
- **名称标识**: 使用"user_invite"作为服务名称
- **全局可用**: 在整个应用中可用

## 使用场景

### 1. 用户邀请数据管理器

```javascript
// 用户邀请数据管理器
class UserInviteDataManager {
    constructor(env) {
        this.env = env;
        this.userInviteService = env.services.user_invite;
        this.rpc = env.services.rpc;
        this.dataCache = new Map();
        this.setupManager();
    }
    
    setupManager() {
        // 设置数据配置
        this.dataConfig = {
            cacheTimeout: 5 * 60 * 1000, // 5分钟缓存超时
            retryAttempts: 3,
            retryDelay: 1000,
            batchSize: 100,
            autoRefresh: true
        };
        
        // 设置数据类型
        this.dataTypes = {
            inviteData: {
                endpoint: '/base_setup/data',
                cacheable: true,
                refreshInterval: 5 * 60 * 1000
            },
            pendingUsers: {
                endpoint: '/base_setup/pending_users',
                cacheable: true,
                refreshInterval: 2 * 60 * 1000
            },
            inviteStats: {
                endpoint: '/base_setup/invite_stats',
                cacheable: true,
                refreshInterval: 10 * 60 * 1000
            },
            userRoles: {
                endpoint: '/base_setup/user_roles',
                cacheable: true,
                refreshInterval: 30 * 60 * 1000
            }
        };
        
        this.setupEventListeners();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听数据更新
        this.env.bus.addEventListener('DATA_UPDATED', (event) => {
            this.handleDataUpdate(event.detail);
        });
        
        // 监听用户邀请
        this.env.bus.addEventListener('USER_INVITED', (event) => {
            this.handleUserInvited(event.detail);
        });
        
        // 监听缓存清理
        this.env.bus.addEventListener('CACHE_CLEAR', (event) => {
            this.handleCacheClear(event.detail);
        });
    }
    
    // 获取邀请数据
    async getInviteData(reload = false) {
        const cacheKey = 'invite_data';
        
        // 检查缓存
        if (!reload && this.isCacheValid(cacheKey)) {
            return this.dataCache.get(cacheKey).data;
        }
        
        try {
            const data = await this.userInviteService.fetchData(reload);
            
            // 缓存数据
            this.setCacheData(cacheKey, data);
            
            return data;
        } catch (error) {
            console.error('Failed to get invite data:', error);
            
            // 返回缓存数据（如果有）
            const cachedData = this.dataCache.get(cacheKey);
            if (cachedData) {
                console.warn('Returning cached data due to error');
                return cachedData.data;
            }
            
            throw error;
        }
    }
    
    // 获取待处理用户
    async getPendingUsers(reload = false) {
        const cacheKey = 'pending_users';
        
        if (!reload && this.isCacheValid(cacheKey)) {
            return this.dataCache.get(cacheKey).data;
        }
        
        try {
            const data = await this.rpc('/base_setup/pending_users');
            
            this.setCacheData(cacheKey, data);
            
            return data;
        } catch (error) {
            console.error('Failed to get pending users:', error);
            throw error;
        }
    }
    
    // 获取邀请统计
    async getInviteStatistics(reload = false) {
        const cacheKey = 'invite_stats';
        
        if (!reload && this.isCacheValid(cacheKey)) {
            return this.dataCache.get(cacheKey).data;
        }
        
        try {
            const data = await this.rpc('/base_setup/invite_stats');
            
            this.setCacheData(cacheKey, data);
            
            return data;
        } catch (error) {
            console.error('Failed to get invite statistics:', error);
            throw error;
        }
    }
    
    // 获取用户角色
    async getUserRoles(reload = false) {
        const cacheKey = 'user_roles';
        
        if (!reload && this.isCacheValid(cacheKey)) {
            return this.dataCache.get(cacheKey).data;
        }
        
        try {
            const data = await this.rpc('/base_setup/user_roles');
            
            this.setCacheData(cacheKey, data);
            
            return data;
        } catch (error) {
            console.error('Failed to get user roles:', error);
            throw error;
        }
    }
    
    // 检查缓存是否有效
    isCacheValid(key) {
        const cached = this.dataCache.get(key);
        if (!cached) {
            return false;
        }
        
        const now = Date.now();
        const elapsed = now - cached.timestamp;
        
        return elapsed < this.dataConfig.cacheTimeout;
    }
    
    // 设置缓存数据
    setCacheData(key, data) {
        this.dataCache.set(key, {
            data: data,
            timestamp: Date.now()
        });
    }
    
    // 清除缓存
    clearCache(key = null) {
        if (key) {
            this.dataCache.delete(key);
        } else {
            this.dataCache.clear();
        }
    }
    
    // 刷新所有数据
    async refreshAllData() {
        const refreshPromises = [
            this.getInviteData(true),
            this.getPendingUsers(true),
            this.getInviteStatistics(true),
            this.getUserRoles(true)
        ];
        
        try {
            const results = await Promise.allSettled(refreshPromises);
            
            const failed = results.filter(result => result.status === 'rejected');
            if (failed.length > 0) {
                console.warn('Some data refresh operations failed:', failed);
            }
            
            return {
                success: results.filter(result => result.status === 'fulfilled').length,
                failed: failed.length,
                total: results.length
            };
        } catch (error) {
            console.error('Failed to refresh all data:', error);
            throw error;
        }
    }
    
    // 批量获取数据
    async getBatchData(dataTypes, reload = false) {
        const promises = dataTypes.map(type => {
            switch (type) {
                case 'inviteData':
                    return this.getInviteData(reload);
                case 'pendingUsers':
                    return this.getPendingUsers(reload);
                case 'inviteStats':
                    return this.getInviteStatistics(reload);
                case 'userRoles':
                    return this.getUserRoles(reload);
                default:
                    return Promise.reject(new Error(`Unknown data type: ${type}`));
            }
        });
        
        try {
            const results = await Promise.allSettled(promises);
            
            const data = {};
            dataTypes.forEach((type, index) => {
                const result = results[index];
                if (result.status === 'fulfilled') {
                    data[type] = result.value;
                } else {
                    data[type] = null;
                    console.error(`Failed to get ${type}:`, result.reason);
                }
            });
            
            return data;
        } catch (error) {
            console.error('Failed to get batch data:', error);
            throw error;
        }
    }
    
    // 监听数据变化
    watchData(dataType, callback, interval = null) {
        const watchInterval = interval || this.dataTypes[dataType]?.refreshInterval || 60000;
        
        const watcher = setInterval(async () => {
            try {
                let newData;
                
                switch (dataType) {
                    case 'inviteData':
                        newData = await this.getInviteData(true);
                        break;
                    case 'pendingUsers':
                        newData = await this.getPendingUsers(true);
                        break;
                    case 'inviteStats':
                        newData = await this.getInviteStatistics(true);
                        break;
                    case 'userRoles':
                        newData = await this.getUserRoles(true);
                        break;
                    default:
                        console.warn(`Unknown data type for watching: ${dataType}`);
                        return;
                }
                
                callback(newData, dataType);
            } catch (error) {
                console.error(`Failed to watch ${dataType}:`, error);
            }
        }, watchInterval);
        
        return watcher;
    }
    
    // 停止监听数据
    stopWatching(watcher) {
        if (watcher) {
            clearInterval(watcher);
        }
    }
    
    // 处理数据更新
    handleDataUpdate(updateData) {
        const { dataType, data } = updateData;
        
        // 更新缓存
        if (dataType && data) {
            this.setCacheData(dataType, data);
        }
        
        // 触发数据变化事件
        this.env.bus.trigger('DATA_CHANGED', {
            dataType: dataType,
            data: data,
            timestamp: Date.now()
        });
    }
    
    // 处理用户邀请
    handleUserInvited(inviteData) {
        // 清除相关缓存
        this.clearCache('invite_data');
        this.clearCache('pending_users');
        this.clearCache('invite_stats');
        
        // 触发数据刷新
        this.refreshAllData();
    }
    
    // 处理缓存清理
    handleCacheClear(clearData) {
        const { keys } = clearData;
        
        if (keys && Array.isArray(keys)) {
            keys.forEach(key => this.clearCache(key));
        } else {
            this.clearCache();
        }
    }
    
    // 获取缓存统计
    getCacheStatistics() {
        const stats = {
            totalEntries: this.dataCache.size,
            entries: [],
            totalSize: 0
        };
        
        for (const [key, value] of this.dataCache) {
            const entry = {
                key: key,
                timestamp: value.timestamp,
                age: Date.now() - value.timestamp,
                isValid: this.isCacheValid(key),
                size: JSON.stringify(value.data).length
            };
            
            stats.entries.push(entry);
            stats.totalSize += entry.size;
        }
        
        return stats;
    }
    
    // 导出缓存数据
    exportCacheData() {
        const cacheData = {};
        
        for (const [key, value] of this.dataCache) {
            cacheData[key] = {
                data: value.data,
                timestamp: value.timestamp,
                isValid: this.isCacheValid(key)
            };
        }
        
        return {
            exportDate: new Date().toISOString(),
            config: this.dataConfig,
            cache: cacheData,
            statistics: this.getCacheStatistics()
        };
    }
    
    // 导入缓存数据
    importCacheData(importData) {
        try {
            if (!importData.cache) {
                throw new Error('Invalid import data: missing cache');
            }
            
            // 清除现有缓存
            this.clearCache();
            
            // 导入缓存数据
            for (const [key, value] of Object.entries(importData.cache)) {
                if (value.isValid) {
                    this.dataCache.set(key, {
                        data: value.data,
                        timestamp: value.timestamp
                    });
                }
            }
            
            return true;
        } catch (error) {
            console.error('Failed to import cache data:', error);
            return false;
        }
    }
    
    // 清理过期缓存
    cleanupExpiredCache() {
        const expiredKeys = [];
        
        for (const [key] of this.dataCache) {
            if (!this.isCacheValid(key)) {
                expiredKeys.push(key);
            }
        }
        
        expiredKeys.forEach(key => this.clearCache(key));
        
        return expiredKeys.length;
    }
    
    // 启动自动清理
    startAutoCleanup() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
        
        this.cleanupInterval = setInterval(() => {
            const cleaned = this.cleanupExpiredCache();
            if (cleaned > 0) {
                console.log(`Cleaned up ${cleaned} expired cache entries`);
            }
        }, this.dataConfig.cacheTimeout);
    }
    
    // 停止自动清理
    stopAutoCleanup() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }
    }
    
    // 销毁管理器
    destroy() {
        this.stopAutoCleanup();
        this.clearCache();
        
        // 清理事件监听器
        this.env.bus.removeEventListener('DATA_UPDATED');
        this.env.bus.removeEventListener('USER_INVITED');
        this.env.bus.removeEventListener('CACHE_CLEAR');
    }
}

// 使用示例
const dataManager = new UserInviteDataManager(env);

// 获取邀请数据
const inviteData = await dataManager.getInviteData();
console.log('Invite data:', inviteData);

// 获取待处理用户
const pendingUsers = await dataManager.getPendingUsers();
console.log('Pending users:', pendingUsers);

// 批量获取数据
const batchData = await dataManager.getBatchData([
    'inviteData',
    'pendingUsers',
    'inviteStats'
]);
console.log('Batch data:', batchData);

// 监听数据变化
const watcher = dataManager.watchData('pendingUsers', (data) => {
    console.log('Pending users updated:', data);
});

// 启动自动清理
dataManager.startAutoCleanup();
```

## 技术特点

### 1. 服务架构
- **异步启动**: 异步服务启动机制
- **方法返回**: 返回服务方法对象
- **闭包缓存**: 使用闭包缓存状态
- **Promise管理**: 统一的Promise管理

### 2. 缓存机制
- **结果缓存**: 缓存RPC调用结果
- **重载支持**: 支持强制重新加载
- **性能优化**: 避免重复的网络请求
- **内存管理**: 合理的内存使用

### 3. RPC集成
- **网络调用**: 集成RPC网络服务
- **端点调用**: 调用特定的API端点
- **错误处理**: 处理网络错误
- **异步操作**: 异步的网络操作

### 4. 服务注册
- **注册表**: 注册到服务注册表
- **全局可用**: 在整个应用中可用
- **依赖注入**: 支持依赖注入
- **生命周期**: 管理服务生命周期

## 设计模式

### 1. 服务模式 (Service Pattern)
- **服务封装**: 将用户邀请数据逻辑封装为服务
- **接口统一**: 提供统一的服务接口
- **依赖注入**: 支持依赖注入机制

### 2. 单例模式 (Singleton Pattern)
- **全局唯一**: 服务在应用中全局唯一
- **状态共享**: 共享用户邀请数据状态
- **资源管理**: 统一管理资源

### 3. 缓存模式 (Cache Pattern)
- **结果缓存**: 缓存API调用结果
- **性能优化**: 减少网络请求
- **智能失效**: 智能的缓存失效机制

### 4. 代理模式 (Proxy Pattern)
- **API代理**: 代理后端API调用
- **透明访问**: 提供透明的访问接口
- **错误处理**: 统一的错误处理

## 注意事项

1. **网络依赖**: 依赖网络连接进行数据获取
2. **缓存管理**: 合理管理Promise缓存
3. **错误处理**: 处理网络请求失败的情况
4. **性能考虑**: 避免频繁的API调用

## 扩展建议

1. **数据监听**: 添加数据变化监听功能
2. **批量操作**: 支持批量数据操作
3. **离线支持**: 支持离线数据缓存
4. **数据同步**: 实现数据的实时同步
5. **错误重试**: 添加自动错误重试机制

该用户邀请服务为Odoo Web客户端提供了简洁高效的用户邀请数据管理功能，通过智能的缓存机制和RPC集成确保了良好的性能和用户体验。

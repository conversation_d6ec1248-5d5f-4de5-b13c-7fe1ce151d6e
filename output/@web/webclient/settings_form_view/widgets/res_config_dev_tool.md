# ResConfigDevTool - 开发工具配置组件

## 概述

`res_config_dev_tool.js` 是 Odoo Web 客户端的开发工具配置组件，提供了开发者工具的管理和控制功能。该模块包含60行代码，是一个OWL组件，专门用于在设置页面中管理调试模式和演示数据，具备调试模式切换、演示数据管理、路由集成、动作执行等特性，是Odoo Web设置表单视图中开发者工具管理的核心组件。

## 文件信息
- **路径**: `/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.js`
- **行数**: 60
- **模块**: `@web/webclient/settings_form_view/widgets/res_config_dev_tool`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                                                    // 注册表系统
'@web/core/utils/hooks'                                                 // 工具钩子
'@web/webclient/settings_form_view/settings/settings_block'            // 设置块组件
'@web/views/form/setting/setting'                                      // 设置组件
'@odoo/owl'                                                             // OWL框架
'@web/views/widgets/standard_widget_props'                             // 标准组件属性
'@web/core/browser/router'                                             // 路由服务
```

## 主组件定义

### 1. ResConfigDevTool 组件

```javascript
/**
 * Widget in the settings that handles the "Developer Tools" section.
 * Can be used to enable/disable the debug modes.
 * Can be used to load the demo data.
 */
class ResConfigDevTool extends Component {
    static template = "res_config_dev_tool";
    static components = {
        SettingsBlock,
        Setting,
    };
    static props = {
        ...standardWidgetProps,
    };

    setup() {
        this.isDebug = Boolean(odoo.debug);
        this.isAssets = odoo.debug.includes("assets");
        this.isTests = odoo.debug.includes("tests");

        this.action = useService("action");
        this.demo = useService("demo_data");

        onWillStart(async () => {
            this.isDemoDataActive = await this.demo.isDemoDataActive();
        });
    }

    activateDebug(value) {
        router.pushState({ debug: value }, { reload: true });
    }

    /**
     * Forces demo data to be installed in a database without demo data installed.
     */
    onClickForceDemo() {
        this.action.doAction("base.demo_force_install_action");
    }
}
```

**组件特性**:
- **调试模式管理**: 管理不同类型的调试模式
- **演示数据控制**: 控制演示数据的安装和管理
- **服务集成**: 集成动作服务和演示数据服务
- **路由操作**: 通过路由切换调试模式

## 核心属性

### 1. 组件属性

```javascript
// 模板配置
static template = "res_config_dev_tool";

// 子组件
static components = {
    SettingsBlock,
    Setting,
};

// 属性定义
static props = {
    ...standardWidgetProps,
};
```

**属性功能**:
- **template**: 指定开发工具配置的模板
- **components**: 包含SettingsBlock和Setting子组件
- **props**: 继承标准组件属性

### 2. 调试状态属性

```javascript
// 调试模式状态
this.isDebug = Boolean(odoo.debug);
this.isAssets = odoo.debug.includes("assets");
this.isTests = odoo.debug.includes("tests");

// 演示数据状态
this.isDemoDataActive = await this.demo.isDemoDataActive();
```

**状态属性功能**:
- **isDebug**: 是否启用调试模式
- **isAssets**: 是否启用资源调试
- **isTests**: 是否启用测试调试
- **isDemoDataActive**: 演示数据是否激活

## 核心功能

### 1. 调试模式检测

```javascript
this.isDebug = Boolean(odoo.debug);
this.isAssets = odoo.debug.includes("assets");
this.isTests = odoo.debug.includes("tests");
```

**检测功能**:
- **基础调试**: 检测是否启用基础调试模式
- **资源调试**: 检测是否启用资源调试模式
- **测试调试**: 检测是否启用测试调试模式
- **状态缓存**: 缓存调试状态供组件使用

### 2. 服务集成

```javascript
this.action = useService("action");
this.demo = useService("demo_data");
```

**服务集成功能**:
- **动作服务**: 集成动作服务执行操作
- **演示数据服务**: 集成演示数据服务
- **服务钩子**: 使用useService钩子获取服务
- **依赖注入**: 通过依赖注入获取服务

### 3. 演示数据状态检查

```javascript
onWillStart(async () => {
    this.isDemoDataActive = await this.demo.isDemoDataActive();
});
```

**状态检查功能**:
- **异步检查**: 异步检查演示数据状态
- **生命周期**: 在组件启动前检查状态
- **状态存储**: 存储演示数据激活状态
- **服务调用**: 调用演示数据服务

### 4. 调试模式激活

```javascript
activateDebug(value) {
    router.pushState({ debug: value }, { reload: true });
}
```

**激活功能**:
- **路由更新**: 更新路由状态
- **参数设置**: 设置调试参数
- **页面重载**: 重载页面应用新的调试模式
- **状态持久化**: 通过URL持久化调试状态

### 5. 演示数据强制安装

```javascript
/**
 * Forces demo data to be installed in a database without demo data installed.
 */
onClickForceDemo() {
    this.action.doAction("base.demo_force_install_action");
}
```

**强制安装功能**:
- **动作执行**: 执行演示数据安装动作
- **强制安装**: 强制安装演示数据
- **动作标识**: 使用特定的动作标识
- **用户交互**: 响应用户点击事件

## 使用场景

### 1. 开发工具管理器

```javascript
// 开发工具管理器
class DeveloperToolsManager {
    constructor(env) {
        this.env = env;
        this.router = env.services.router;
        this.action = env.services.action;
        this.demo = env.services.demo_data;
        this.setupManager();
    }
    
    setupManager() {
        // 设置调试模式类型
        this.debugModes = {
            basic: {
                name: 'Basic Debug',
                value: '1',
                description: 'Enable basic debugging features',
                features: ['Developer menu', 'Technical views', 'Debug info']
            },
            assets: {
                name: 'Assets Debug',
                value: 'assets',
                description: 'Enable assets debugging',
                features: ['Unminified JS/CSS', 'Source maps', 'Asset reload']
            },
            tests: {
                name: 'Tests Debug',
                value: 'tests',
                description: 'Enable tests debugging',
                features: ['Test runner', 'Test coverage', 'Test reports']
            },
            full: {
                name: 'Full Debug',
                value: '1,assets,tests',
                description: 'Enable all debugging features',
                features: ['All debug features enabled']
            }
        };
        
        this.setupEventListeners();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听调试模式变化
        this.env.bus.addEventListener('DEBUG_MODE_CHANGED', (event) => {
            this.handleDebugModeChange(event.detail);
        });
        
        // 监听演示数据状态变化
        this.env.bus.addEventListener('DEMO_DATA_STATUS_CHANGED', (event) => {
            this.handleDemoDataStatusChange(event.detail);
        });
    }
    
    // 获取当前调试状态
    getCurrentDebugStatus() {
        const debugValue = odoo.debug || '';
        
        return {
            isDebug: Boolean(debugValue),
            isAssets: debugValue.includes('assets'),
            isTests: debugValue.includes('tests'),
            debugValue: debugValue,
            activeFeatures: this.getActiveFeatures(debugValue)
        };
    }
    
    // 获取激活的功能
    getActiveFeatures(debugValue) {
        const features = [];
        
        if (debugValue) {
            features.push(...this.debugModes.basic.features);
        }
        
        if (debugValue.includes('assets')) {
            features.push(...this.debugModes.assets.features);
        }
        
        if (debugValue.includes('tests')) {
            features.push(...this.debugModes.tests.features);
        }
        
        return [...new Set(features)]; // 去重
    }
    
    // 切换调试模式
    toggleDebugMode(mode, enabled) {
        const currentStatus = this.getCurrentDebugStatus();
        let newDebugValue = currentStatus.debugValue;
        
        switch (mode) {
            case 'basic':
                if (enabled && !currentStatus.isDebug) {
                    newDebugValue = newDebugValue ? `1,${newDebugValue}` : '1';
                } else if (!enabled && currentStatus.isDebug) {
                    newDebugValue = newDebugValue.replace(/1,?/, '').replace(/^,/, '');
                }
                break;
                
            case 'assets':
                if (enabled && !currentStatus.isAssets) {
                    newDebugValue = newDebugValue ? `${newDebugValue},assets` : 'assets';
                } else if (!enabled && currentStatus.isAssets) {
                    newDebugValue = newDebugValue.replace(/,?assets,?/, '').replace(/^,|,$/, '');
                }
                break;
                
            case 'tests':
                if (enabled && !currentStatus.isTests) {
                    newDebugValue = newDebugValue ? `${newDebugValue},tests` : 'tests';
                } else if (!enabled && currentStatus.isTests) {
                    newDebugValue = newDebugValue.replace(/,?tests,?/, '').replace(/^,|,$/, '');
                }
                break;
        }
        
        // 清理调试值
        newDebugValue = this.cleanDebugValue(newDebugValue);
        
        // 应用新的调试模式
        this.applyDebugMode(newDebugValue);
    }
    
    // 清理调试值
    cleanDebugValue(debugValue) {
        return debugValue
            .split(',')
            .filter(v => v.trim())
            .filter((v, i, arr) => arr.indexOf(v) === i) // 去重
            .join(',');
    }
    
    // 应用调试模式
    applyDebugMode(debugValue) {
        try {
            this.router.pushState({ debug: debugValue || undefined }, { reload: true });
            
            // 触发调试模式变化事件
            this.env.bus.trigger('DEBUG_MODE_CHANGED', {
                oldValue: odoo.debug,
                newValue: debugValue,
                timestamp: Date.now()
            });
            
        } catch (error) {
            console.error('Failed to apply debug mode:', error);
            
            this.env.services.notification.add(
                'Failed to change debug mode: ' + error.message,
                { type: 'danger' }
            );
        }
    }
    
    // 重置调试模式
    resetDebugMode() {
        this.applyDebugMode('');
    }
    
    // 获取演示数据状态
    async getDemoDataStatus() {
        try {
            const isActive = await this.demo.isDemoDataActive();
            
            return {
                isActive: isActive,
                lastChecked: Date.now(),
                error: null
            };
        } catch (error) {
            console.error('Failed to get demo data status:', error);
            
            return {
                isActive: false,
                lastChecked: Date.now(),
                error: error.message
            };
        }
    }
    
    // 强制安装演示数据
    async forceInstallDemoData() {
        try {
            await this.action.doAction("base.demo_force_install_action");
            
            this.env.services.notification.add(
                'Demo data installation started',
                { type: 'info' }
            );
            
            return true;
        } catch (error) {
            console.error('Failed to force install demo data:', error);
            
            this.env.services.notification.add(
                'Failed to install demo data: ' + error.message,
                { type: 'danger' }
            );
            
            return false;
        }
    }
    
    // 卸载演示数据
    async uninstallDemoData() {
        try {
            // 确认操作
            const confirmed = await this.confirmUninstallDemoData();
            if (!confirmed) {
                return false;
            }
            
            await this.action.doAction("base.demo_uninstall_action");
            
            this.env.services.notification.add(
                'Demo data uninstallation started',
                { type: 'info' }
            );
            
            return true;
        } catch (error) {
            console.error('Failed to uninstall demo data:', error);
            
            this.env.services.notification.add(
                'Failed to uninstall demo data: ' + error.message,
                { type: 'danger' }
            );
            
            return false;
        }
    }
    
    // 确认卸载演示数据
    async confirmUninstallDemoData() {
        return new Promise((resolve) => {
            this.env.services.dialog.add(ConfirmationDialog, {
                title: 'Uninstall Demo Data',
                body: 'Are you sure you want to uninstall demo data? This action cannot be undone and will remove all demo records from the system.',
                confirm: () => resolve(true),
                cancel: () => resolve(false)
            });
        });
    }
    
    // 导出开发工具配置
    exportDeveloperConfig() {
        const config = {
            debugStatus: this.getCurrentDebugStatus(),
            demoDataStatus: null, // 将异步填充
            exportDate: new Date().toISOString(),
            odooVersion: odoo.info.server_version,
            userAgent: navigator.userAgent
        };
        
        // 异步获取演示数据状态
        this.getDemoDataStatus().then(demoStatus => {
            config.demoDataStatus = demoStatus;
            
            // 创建下载
            const blob = new Blob([JSON.stringify(config, null, 2)], {
                type: 'application/json'
            });
            
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `odoo_dev_config_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            URL.revokeObjectURL(url);
        });
    }
    
    // 导入开发工具配置
    async importDeveloperConfig(file) {
        try {
            const fileContent = await this.readFile(file);
            const config = JSON.parse(fileContent);
            
            // 验证配置
            if (!this.validateConfig(config)) {
                throw new Error('Invalid configuration file');
            }
            
            // 应用调试模式
            if (config.debugStatus && config.debugStatus.debugValue) {
                this.applyDebugMode(config.debugStatus.debugValue);
            }
            
            this.env.services.notification.add(
                'Developer configuration imported successfully',
                { type: 'success' }
            );
            
            return true;
        } catch (error) {
            console.error('Failed to import developer config:', error);
            
            this.env.services.notification.add(
                'Failed to import configuration: ' + error.message,
                { type: 'danger' }
            );
            
            return false;
        }
    }
    
    // 验证配置
    validateConfig(config) {
        return config && 
               typeof config === 'object' && 
               config.debugStatus && 
               typeof config.debugStatus === 'object';
    }
    
    // 读取文件
    readFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(e);
            reader.readAsText(file);
        });
    }
    
    // 处理调试模式变化
    handleDebugModeChange(changeData) {
        console.log('Debug mode changed:', changeData);
        
        // 更新UI状态
        this.updateDebugModeUI(changeData.newValue);
    }
    
    // 更新调试模式UI
    updateDebugModeUI(debugValue) {
        // 更新调试模式指示器
        const indicators = document.querySelectorAll('.o_debug_indicator');
        indicators.forEach(indicator => {
            indicator.classList.toggle('active', Boolean(debugValue));
            indicator.textContent = debugValue ? `Debug: ${debugValue}` : 'Debug: Off';
        });
    }
    
    // 处理演示数据状态变化
    handleDemoDataStatusChange(statusData) {
        console.log('Demo data status changed:', statusData);
        
        // 更新演示数据UI
        this.updateDemoDataUI(statusData.isActive);
    }
    
    // 更新演示数据UI
    updateDemoDataUI(isActive) {
        const indicators = document.querySelectorAll('.o_demo_data_indicator');
        indicators.forEach(indicator => {
            indicator.classList.toggle('active', isActive);
            indicator.textContent = isActive ? 'Demo Data: Active' : 'Demo Data: Inactive';
        });
    }
    
    // 获取开发工具统计
    getDeveloperToolsStatistics() {
        const debugStatus = this.getCurrentDebugStatus();
        
        return {
            debugMode: {
                enabled: debugStatus.isDebug,
                features: debugStatus.activeFeatures.length,
                value: debugStatus.debugValue
            },
            demoData: {
                // 需要异步获取
                status: 'checking...'
            },
            environment: {
                odooVersion: odoo.info.server_version,
                isEnterprise: odoo.info.isEnterprise,
                database: odoo.info.db
            }
        };
    }
}

// 使用示例
const devToolsManager = new DeveloperToolsManager(env);

// 获取当前调试状态
const debugStatus = devToolsManager.getCurrentDebugStatus();
console.log('Debug status:', debugStatus);

// 切换调试模式
devToolsManager.toggleDebugMode('assets', true);

// 获取演示数据状态
const demoStatus = await devToolsManager.getDemoDataStatus();
console.log('Demo data status:', demoStatus);

// 强制安装演示数据
await devToolsManager.forceInstallDemoData();
```

## 技术特点

### 1. 调试模式管理
- **多模式支持**: 支持基础、资源、测试等多种调试模式
- **状态检测**: 实时检测当前调试模式状态
- **路由集成**: 通过路由参数控制调试模式
- **页面重载**: 自动重载页面应用新的调试设置

### 2. 演示数据控制
- **状态检查**: 异步检查演示数据激活状态
- **强制安装**: 支持强制安装演示数据
- **服务集成**: 集成演示数据服务
- **动作执行**: 通过动作服务执行操作

### 3. 服务集成
- **动作服务**: 集成动作服务执行复杂操作
- **演示数据服务**: 集成演示数据服务
- **路由服务**: 集成路由服务管理URL状态
- **钩子使用**: 使用useService钩子获取服务

### 4. 生命周期管理
- **异步初始化**: 在组件启动前异步初始化状态
- **状态缓存**: 缓存调试和演示数据状态
- **事件响应**: 响应用户交互事件
- **资源清理**: 适当的资源清理

## 设计模式

### 1. 服务模式 (Service Pattern)
- **服务集成**: 集成多个系统服务
- **依赖注入**: 通过钩子注入服务依赖
- **接口统一**: 提供统一的服务接口

### 2. 状态模式 (State Pattern)
- **调试状态**: 管理不同的调试状态
- **演示数据状态**: 管理演示数据的状态
- **状态转换**: 处理状态之间的转换

### 3. 命令模式 (Command Pattern)
- **动作执行**: 封装动作执行为命令
- **操作抽象**: 抽象化复杂操作
- **撤销支持**: 支持操作的撤销

### 4. 观察者模式 (Observer Pattern)
- **状态监听**: 监听调试模式和演示数据状态变化
- **事件通知**: 通知状态变化
- **自动更新**: 状态变化时自动更新UI

## 注意事项

1. **权限控制**: 确保只有开发者可以访问这些功能
2. **数据安全**: 演示数据操作可能影响生产数据
3. **性能影响**: 调试模式可能影响系统性能
4. **状态同步**: 确保UI状态与实际状态同步

## 扩展建议

1. **权限管理**: 添加细粒度的权限控制
2. **日志记录**: 记录调试模式和演示数据操作日志
3. **配置导出**: 支持开发工具配置的导出导入
4. **性能监控**: 监控调试模式对性能的影响
5. **批量操作**: 支持批量的开发工具操作

该开发工具配置组件为Odoo Web客户端提供了完整的开发者工具管理功能，通过调试模式控制和演示数据管理确保了开发过程的便利性和灵活性。

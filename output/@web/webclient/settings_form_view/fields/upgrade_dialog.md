# UpgradeDialog - 升级对话框

## 概述

`upgrade_dialog.js` 是 Odoo Web 客户端的升级对话框组件，提供了企业版升级的用户界面。该模块包含31行代码，是一个OWL组件，专门用于显示升级到企业版的对话框，具备用户计数、外部链接跳转、对话框管理等特性，是Odoo Web设置表单视图中企业版推广的核心界面组件。

## 文件信息
- **路径**: `/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.js`
- **行数**: 31
- **模块**: `@web/webclient/settings_form_view/fields/upgrade_dialog`

## 依赖关系

```javascript
// 核心依赖
'@web/core/dialog/dialog'       // 对话框组件
'@web/core/utils/hooks'         // 钩子工具
'@odoo/owl'                     // OWL框架
```

## 主组件定义

### 1. UpgradeDialog 组件

```javascript
class UpgradeDialog extends Component {
    static template = "web.UpgradeDialog";
    static components = { Dialog };
    static props = {
        close: Function,
    };
    
    setup() {
        this.orm = useService("orm");
    }
    
    async _confirmUpgrade() {
        const usersCount = await this.orm.call("res.users", "search_count", [
            [["share", "=", false]],
        ]);
        window.open(
            "https://www.odoo.com/odoo-enterprise/upgrade?num_users=" + usersCount,
            "_blank"
        );
        this.props.close();
    }
}
```

**组件特性**:
- **对话框基础**: 基于Dialog组件构建
- **用户计数**: 自动计算非共享用户数量
- **外部跳转**: 跳转到Odoo官方升级页面
- **参数传递**: 将用户数量作为参数传递

## 核心属性

### 1. 组件属性

```javascript
// 模板配置
static template = "web.UpgradeDialog";

// 子组件
static components = { Dialog };

// 属性定义
static props = {
    close: Function,    // 关闭对话框的回调函数
};
```

**属性功能**:
- **template**: 指定对话框的模板
- **components**: 包含Dialog子组件
- **props**: 定义close回调属性

### 2. 服务属性

```javascript
// ORM服务
this.orm = useService("orm");
```

**服务属性功能**:
- **orm**: 用于数据库查询操作

## 核心功能

### 1. 用户计数

```javascript
const usersCount = await this.orm.call("res.users", "search_count", [
    [["share", "=", false]],
]);
```

**计数功能**:
- **数据库查询**: 查询res.users模型
- **条件过滤**: 过滤非共享用户（share=false）
- **计数操作**: 使用search_count方法计数
- **异步处理**: 异步获取用户数量

### 2. 升级确认

```javascript
async _confirmUpgrade() {
    const usersCount = await this.orm.call("res.users", "search_count", [
        [["share", "=", false]],
    ]);
    window.open(
        "https://www.odoo.com/odoo-enterprise/upgrade?num_users=" + usersCount,
        "_blank"
    );
    this.props.close();
}
```

**确认功能**:
- **用户统计**: 统计当前系统的用户数量
- **URL构建**: 构建包含用户数的升级URL
- **新窗口打开**: 在新窗口中打开升级页面
- **对话框关闭**: 执行完成后关闭对话框

## 使用场景

### 1. 升级流程管理器

```javascript
// 升级流程管理器
class UpgradeFlowManager {
    constructor(env) {
        this.env = env;
        this.dialogService = env.services.dialog;
        this.orm = env.services.orm;
        this.upgradeTracking = {
            views: 0,
            clicks: 0,
            conversions: 0
        };
        this.setupTracking();
    }
    
    setupTracking() {
        // 设置升级跟踪
        this.trackingEnabled = true;
        this.sessionId = this.generateSessionId();
    }
    
    // 显示升级对话框
    async showUpgradeDialog(options = {}) {
        try {
            // 记录对话框显示
            this.trackDialogView(options);
            
            // 获取系统信息
            const systemInfo = await this.getSystemInfo();
            
            // 显示对话框
            this.dialogService.add(
                EnhancedUpgradeDialog,
                {
                    systemInfo: systemInfo,
                    upgradeOptions: this.getUpgradeOptions(),
                    currentPlan: this.getCurrentPlan(),
                    ...options
                },
                {
                    onClose: () => {
                        this.trackDialogClose();
                    },
                    onUpgrade: (plan) => {
                        this.handleUpgradeClick(plan, systemInfo);
                    },
                    onTrial: () => {
                        this.handleTrialClick(systemInfo);
                    },
                    onContact: () => {
                        this.handleContactClick(systemInfo);
                    }
                }
            );
            
        } catch (error) {
            console.error('Failed to show upgrade dialog:', error);
        }
    }
    
    // 获取系统信息
    async getSystemInfo() {
        try {
            const [usersCount, appsCount, companyInfo] = await Promise.all([
                this.getUsersCount(),
                this.getInstalledAppsCount(),
                this.getCompanyInfo()
            ]);
            
            return {
                usersCount,
                appsCount,
                companyInfo,
                version: odoo.info.server_version,
                isEnterprise: odoo.info.isEnterprise,
                database: this.env.services.user.db
            };
            
        } catch (error) {
            console.error('Failed to get system info:', error);
            return {
                usersCount: 1,
                appsCount: 0,
                companyInfo: {},
                version: 'unknown',
                isEnterprise: false,
                database: 'unknown'
            };
        }
    }
    
    // 获取用户数量
    async getUsersCount() {
        return await this.orm.call("res.users", "search_count", [
            [["share", "=", false], ["active", "=", true]]
        ]);
    }
    
    // 获取已安装应用数量
    async getInstalledAppsCount() {
        return await this.orm.call("ir.module.module", "search_count", [
            [["state", "=", "installed"], ["application", "=", true]]
        ]);
    }
    
    // 获取公司信息
    async getCompanyInfo() {
        const companies = await this.orm.call("res.company", "search_read", [
            [], ["name", "country_id", "industry_id"]
        ]);
        
        return companies.length > 0 ? companies[0] : {};
    }
    
    // 获取升级选项
    getUpgradeOptions() {
        return [
            {
                name: 'Standard',
                price: '$24/user/month',
                features: [
                    'All enterprise apps',
                    'Priority support',
                    'Advanced reporting',
                    'Multi-company'
                ],
                recommended: false,
                url: 'https://www.odoo.com/pricing/standard'
            },
            {
                name: 'Custom',
                price: '$37/user/month',
                features: [
                    'Everything in Standard',
                    'Odoo Studio',
                    'Advanced customization',
                    'Dedicated support'
                ],
                recommended: true,
                url: 'https://www.odoo.com/pricing/custom'
            },
            {
                name: 'Enterprise',
                price: 'Contact sales',
                features: [
                    'Everything in Custom',
                    'On-premise deployment',
                    'Custom development',
                    'SLA guarantee'
                ],
                recommended: false,
                url: 'https://www.odoo.com/contactus'
            }
        ];
    }
    
    // 获取当前计划
    getCurrentPlan() {
        return {
            name: 'Community',
            price: 'Free',
            features: [
                'Core apps',
                'Community support',
                'Basic features'
            ],
            limitations: [
                'No enterprise apps',
                'Limited customization',
                'No priority support'
            ]
        };
    }
    
    // 处理升级点击
    async handleUpgradeClick(plan, systemInfo) {
        this.trackUpgradeClick(plan, systemInfo);
        
        // 构建升级URL
        const upgradeUrl = this.buildUpgradeUrl(plan, systemInfo);
        
        // 打开升级页面
        window.open(upgradeUrl, '_blank');
        
        // 记录转化
        this.trackConversion('upgrade', plan);
    }
    
    // 处理试用点击
    async handleTrialClick(systemInfo) {
        this.trackTrialClick(systemInfo);
        
        // 构建试用URL
        const trialUrl = this.buildTrialUrl(systemInfo);
        
        // 打开试用页面
        window.open(trialUrl, '_blank');
        
        // 记录转化
        this.trackConversion('trial');
    }
    
    // 处理联系点击
    async handleContactClick(systemInfo) {
        this.trackContactClick(systemInfo);
        
        // 构建联系URL
        const contactUrl = this.buildContactUrl(systemInfo);
        
        // 打开联系页面
        window.open(contactUrl, '_blank');
        
        // 记录转化
        this.trackConversion('contact');
    }
    
    // 构建升级URL
    buildUpgradeUrl(plan, systemInfo) {
        const params = new URLSearchParams({
            num_users: systemInfo.usersCount,
            plan: plan.name.toLowerCase(),
            version: systemInfo.version,
            apps: systemInfo.appsCount,
            source: 'settings_dialog'
        });
        
        return `https://www.odoo.com/odoo-enterprise/upgrade?${params.toString()}`;
    }
    
    // 构建试用URL
    buildTrialUrl(systemInfo) {
        const params = new URLSearchParams({
            num_users: systemInfo.usersCount,
            version: systemInfo.version,
            source: 'settings_dialog'
        });
        
        return `https://www.odoo.com/trial?${params.toString()}`;
    }
    
    // 构建联系URL
    buildContactUrl(systemInfo) {
        const params = new URLSearchParams({
            subject: 'Enterprise Upgrade Inquiry',
            users: systemInfo.usersCount,
            version: systemInfo.version,
            source: 'settings_dialog'
        });
        
        return `https://www.odoo.com/contactus?${params.toString()}`;
    }
    
    // 跟踪对话框显示
    trackDialogView(options) {
        if (!this.trackingEnabled) return;
        
        this.upgradeTracking.views++;
        
        this.sendTrackingEvent('upgrade_dialog_view', {
            session_id: this.sessionId,
            feature: options.feature || 'general',
            timestamp: Date.now()
        });
    }
    
    // 跟踪对话框关闭
    trackDialogClose() {
        if (!this.trackingEnabled) return;
        
        this.sendTrackingEvent('upgrade_dialog_close', {
            session_id: this.sessionId,
            timestamp: Date.now()
        });
    }
    
    // 跟踪升级点击
    trackUpgradeClick(plan, systemInfo) {
        if (!this.trackingEnabled) return;
        
        this.upgradeTracking.clicks++;
        
        this.sendTrackingEvent('upgrade_click', {
            session_id: this.sessionId,
            plan: plan.name,
            users: systemInfo.usersCount,
            timestamp: Date.now()
        });
    }
    
    // 跟踪试用点击
    trackTrialClick(systemInfo) {
        if (!this.trackingEnabled) return;
        
        this.sendTrackingEvent('trial_click', {
            session_id: this.sessionId,
            users: systemInfo.usersCount,
            timestamp: Date.now()
        });
    }
    
    // 跟踪联系点击
    trackContactClick(systemInfo) {
        if (!this.trackingEnabled) return;
        
        this.sendTrackingEvent('contact_click', {
            session_id: this.sessionId,
            users: systemInfo.usersCount,
            timestamp: Date.now()
        });
    }
    
    // 跟踪转化
    trackConversion(type, plan = null) {
        if (!this.trackingEnabled) return;
        
        this.upgradeTracking.conversions++;
        
        this.sendTrackingEvent('upgrade_conversion', {
            session_id: this.sessionId,
            type: type,
            plan: plan ? plan.name : null,
            timestamp: Date.now()
        });
    }
    
    // 发送跟踪事件
    sendTrackingEvent(event, data) {
        try {
            // 发送到分析服务
            this.env.services.analytics?.track(event, data);
            
            // 或发送到服务器
            // this.orm.call('upgrade.tracking', 'log_event', [event, data]);
        } catch (error) {
            console.warn('Failed to send tracking event:', error);
        }
    }
    
    // 生成会话ID
    generateSessionId() {
        return `upgrade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    // 获取跟踪统计
    getTrackingStats() {
        return {
            ...this.upgradeTracking,
            conversionRate: this.upgradeTracking.views > 0 ? 
                (this.upgradeTracking.conversions / this.upgradeTracking.views * 100).toFixed(2) + '%' : '0%',
            clickRate: this.upgradeTracking.views > 0 ? 
                (this.upgradeTracking.clicks / this.upgradeTracking.views * 100).toFixed(2) + '%' : '0%'
        };
    }
}

// 增强的升级对话框组件
class EnhancedUpgradeDialog extends Component {
    static template = xml`
        <Dialog title="'Upgrade to Odoo Enterprise'" size="'lg'">
            <div class="upgrade-dialog-enhanced">
                <div class="current-plan-info">
                    <h5>Current Plan: <t t-esc="props.currentPlan.name"/></h5>
                    <p>You're currently using the Community edition with limited features.</p>
                </div>
                
                <div class="system-info" t-if="props.systemInfo">
                    <div class="info-grid">
                        <div class="info-item">
                            <strong>Users:</strong> <t t-esc="props.systemInfo.usersCount"/>
                        </div>
                        <div class="info-item">
                            <strong>Apps:</strong> <t t-esc="props.systemInfo.appsCount"/>
                        </div>
                        <div class="info-item">
                            <strong>Version:</strong> <t t-esc="props.systemInfo.version"/>
                        </div>
                    </div>
                </div>
                
                <div class="upgrade-options">
                    <h5>Choose Your Plan:</h5>
                    <div class="plans-grid">
                        <t t-foreach="props.upgradeOptions" t-as="plan" t-key="plan.name">
                            <div class="plan-card" t-att-class="{ 'recommended': plan.recommended }">
                                <div class="plan-header">
                                    <h6 t-esc="plan.name"/>
                                    <div class="plan-price" t-esc="plan.price"/>
                                    <div t-if="plan.recommended" class="recommended-badge">Recommended</div>
                                </div>
                                <div class="plan-features">
                                    <ul>
                                        <li t-foreach="plan.features" t-as="feature" t-key="feature" t-esc="feature"/>
                                    </ul>
                                </div>
                                <button class="btn btn-primary" t-on-click="() => this.props.onUpgrade(plan)">
                                    Choose <t t-esc="plan.name"/>
                                </button>
                            </div>
                        </t>
                    </div>
                </div>
                
                <div class="action-buttons">
                    <button class="btn btn-secondary" t-on-click="props.onTrial">
                        Start Free Trial
                    </button>
                    <button class="btn btn-outline-secondary" t-on-click="props.onContact">
                        Contact Sales
                    </button>
                </div>
                
                <div class="benefits-section">
                    <h6>Why Upgrade?</h6>
                    <ul class="benefits-list">
                        <li>Access to all enterprise applications</li>
                        <li>Priority support and bug fixes</li>
                        <li>Advanced reporting and analytics</li>
                        <li>Multi-company management</li>
                        <li>Odoo Studio for customization</li>
                        <li>Regular updates and new features</li>
                    </ul>
                </div>
            </div>
        </Dialog>`;
    
    static components = { Dialog };
    static props = {
        systemInfo: Object,
        upgradeOptions: Array,
        currentPlan: Object,
        onClose: Function,
        onUpgrade: Function,
        onTrial: Function,
        onContact: Function
    };
}

// 使用示例
const upgradeManager = new UpgradeFlowManager(env);

// 显示升级对话框
await upgradeManager.showUpgradeDialog({
    feature: 'advanced_reporting'
});

// 获取跟踪统计
const stats = upgradeManager.getTrackingStats();
console.log('Upgrade tracking stats:', stats);
```

## 技术特点

### 1. 组件架构
- **OWL组件**: 基于OWL框架构建
- **Dialog集成**: 集成标准对话框组件
- **模板驱动**: 使用模板定义界面结构
- **属性配置**: 通过props配置组件行为

### 2. 数据查询
- **ORM服务**: 使用ORM服务进行数据库查询
- **异步操作**: 异步获取用户数量
- **条件过滤**: 精确过滤非共享用户
- **计数查询**: 高效的计数查询操作

### 3. 外部集成
- **URL构建**: 动态构建升级页面URL
- **参数传递**: 将用户数量作为参数传递
- **新窗口**: 在新窗口中打开外部页面
- **用户体验**: 保持当前页面状态

### 4. 生命周期管理
- **对话框关闭**: 执行操作后自动关闭对话框
- **回调执行**: 执行传入的关闭回调
- **状态清理**: 清理组件状态
- **资源释放**: 释放相关资源

## 设计模式

### 1. 组件模式 (Component Pattern)
- **封装性**: 将升级逻辑封装在组件中
- **可重用**: 可在不同场景中重用
- **配置化**: 通过属性配置组件行为

### 2. 服务定位器模式 (Service Locator Pattern)
- **服务获取**: 通过useService获取ORM服务
- **依赖注入**: 注入所需的服务依赖
- **解耦设计**: 与具体服务实现解耦

### 3. 模板方法模式 (Template Method Pattern)
- **模板定义**: 定义对话框的模板结构
- **行为定制**: 通过方法定制具体行为
- **扩展点**: 提供明确的扩展点

## 注意事项

1. **网络依赖**: 依赖网络连接查询用户数量
2. **外部链接**: 确保外部升级页面的可用性
3. **用户体验**: 提供清晰的升级流程指引
4. **错误处理**: 处理数据查询失败的情况

## 扩展建议

1. **功能展示**: 展示企业版的具体功能特性
2. **价格计算**: 根据用户数量计算升级价格
3. **试用选项**: 提供企业版功能的试用选项
4. **联系方式**: 提供销售联系方式
5. **本地化**: 支持多语言的升级信息

该升级对话框组件为Odoo Web客户端提供了专业的企业版升级界面，通过智能的用户统计和便捷的外部链接确保了良好的升级转化体验。

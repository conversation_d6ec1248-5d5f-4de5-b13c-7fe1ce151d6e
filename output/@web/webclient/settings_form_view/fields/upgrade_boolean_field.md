# UpgradeBooleanField - 升级布尔字段

## 概述

`upgrade_boolean_field.js` 是 Odoo Web 客户端的升级布尔字段组件，提供了配置设置中的升级提示功能。该模块包含47行代码，继承自标准布尔字段，专门用于在社区版中显示升级到企业版的提示对话框，具备版本检测、对话框集成、状态回滚等特性，是Odoo Web设置表单视图中企业版功能推广的重要组件。

## 文件信息
- **路径**: `/web/static/src/webclient/settings_form_view/fields/upgrade_boolean_field.js`
- **行数**: 47
- **模块**: `@web/webclient/settings_form_view/fields/upgrade_boolean_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                                                    // 注册表系统
'@web/views/fields/boolean/boolean_field'                              // 布尔字段基类
'@web/core/utils/hooks'                                                 // 钩子工具
'@web/webclient/settings_form_view/fields/upgrade_dialog'              // 升级对话框
```

## 主组件定义

### 1. UpgradeBooleanField 组件

```javascript
class UpgradeBooleanField extends BooleanField {
    setup() {
        super.setup();
        this.dialogService = useService("dialog");
        this.isEnterprise = odoo.info && odoo.info.isEnterprise;
    }

    async onChange(newValue) {
        if (!this.isEnterprise) {
            this.dialogService.add(
                UpgradeDialog,
                {},
                {
                    onClose: () => {
                        this.props.record.update({ [this.props.name]: false });
                    },
                }
            );
        } else {
            super.onChange(...arguments);
        }
    }
}
```

**组件特性**:
- **继承布尔字段**: 继承标准布尔字段的所有功能
- **版本检测**: 自动检测当前是否为企业版
- **升级提示**: 在社区版中显示升级对话框
- **状态回滚**: 对话框关闭时自动回滚字段状态

## 核心属性

### 1. 服务属性

```javascript
// 对话框服务
this.dialogService = useService("dialog");

// 企业版检测
this.isEnterprise = odoo.info && odoo.info.isEnterprise;
```

**属性功能**:
- **dialogService**: 用于显示升级对话框
- **isEnterprise**: 检测当前版本是否为企业版

## 核心功能

### 1. 版本检测

```javascript
// 企业版检测逻辑
this.isEnterprise = odoo.info && odoo.info.isEnterprise;
```

**检测功能**:
- **全局信息**: 从odoo.info获取版本信息
- **布尔判断**: 简单的布尔值判断
- **安全检查**: 确保odoo.info存在
- **实时检测**: 在组件初始化时检测

### 2. 变更处理

```javascript
async onChange(newValue) {
    if (!this.isEnterprise) {
        // 显示升级对话框
        this.dialogService.add(
            UpgradeDialog,
            {},
            {
                onClose: () => {
                    this.props.record.update({ [this.props.name]: false });
                },
            }
        );
    } else {
        // 企业版正常处理
        super.onChange(...arguments);
    }
}
```

**变更处理功能**:
- **条件分支**: 根据版本类型执行不同逻辑
- **对话框显示**: 在社区版中显示升级对话框
- **状态回滚**: 对话框关闭时重置字段值
- **正常处理**: 企业版中执行标准布尔字段逻辑

## 字段注册

### 1. 字段定义

```javascript
const upgradeBooleanField = {
    ...booleanField,
    component: UpgradeBooleanField,
    additionalClasses: [...(booleanField.additionalClasses || []), "o_field_boolean"],
};
```

**字段配置**:
- **基础继承**: 继承标准布尔字段的所有配置
- **组件替换**: 使用自定义的UpgradeBooleanField组件
- **样式扩展**: 添加额外的CSS类

### 2. 注册表注册

```javascript
registry.category("fields").add("upgrade_boolean", upgradeBooleanField);
```

**注册功能**:
- **字段类型**: 注册为"upgrade_boolean"字段类型
- **全局可用**: 在整个应用中可用
- **配置驱动**: 通过字段配置使用

## 使用场景

### 1. 设置表单中的企业版功能

```javascript
// 设置表单中的企业版功能示例
class EnterpriseFeatureManager {
    constructor(env) {
        this.env = env;
        this.dialogService = env.services.dialog;
        this.isEnterprise = odoo.info && odoo.info.isEnterprise;
        this.enterpriseFeatures = this.getEnterpriseFeatures();
    }
    
    // 获取企业版功能列表
    getEnterpriseFeatures() {
        return [
            {
                name: 'advanced_reporting',
                label: 'Advanced Reporting',
                description: 'Create sophisticated reports with advanced analytics',
                category: 'reporting'
            },
            {
                name: 'multi_company',
                label: 'Multi-Company',
                description: 'Manage multiple companies in one database',
                category: 'administration'
            },
            {
                name: 'advanced_inventory',
                label: 'Advanced Inventory',
                description: 'Advanced inventory management features',
                category: 'inventory'
            },
            {
                name: 'studio',
                label: 'Odoo Studio',
                description: 'Customize your Odoo without coding',
                category: 'customization'
            },
            {
                name: 'voip',
                label: 'VoIP Integration',
                description: 'Integrate with VoIP systems',
                category: 'communication'
            }
        ];
    }
    
    // 检查功能是否可用
    isFeatureAvailable(featureName) {
        if (this.isEnterprise) {
            return true;
        }
        
        const feature = this.enterpriseFeatures.find(f => f.name === featureName);
        return feature ? false : true; // 企业版功能在社区版中不可用
    }
    
    // 处理企业版功能激活
    async handleEnterpriseFeatureActivation(featureName, fieldName, record) {
        if (this.isEnterprise) {
            // 企业版中正常激活功能
            return this.activateFeature(featureName);
        } else {
            // 社区版中显示升级对话框
            return this.showUpgradeDialog(featureName, fieldName, record);
        }
    }
    
    // 激活功能（企业版）
    async activateFeature(featureName) {
        try {
            const feature = this.enterpriseFeatures.find(f => f.name === featureName);
            if (!feature) {
                throw new Error(`Feature ${featureName} not found`);
            }
            
            // 执行功能激活逻辑
            await this.env.services.rpc('/web/dataset/call_kw', {
                model: 'res.config.settings',
                method: 'activate_enterprise_feature',
                args: [featureName],
                kwargs: {}
            });
            
            this.env.services.notification.add(
                `${feature.label} has been activated`,
                { type: 'success' }
            );
            
            return true;
            
        } catch (error) {
            console.error('Failed to activate feature:', error);
            this.env.services.notification.add(
                'Failed to activate feature',
                { type: 'danger' }
            );
            return false;
        }
    }
    
    // 显示升级对话框（社区版）
    showUpgradeDialog(featureName, fieldName, record) {
        const feature = this.enterpriseFeatures.find(f => f.name === featureName);
        
        this.dialogService.add(
            UpgradeDialog,
            {
                featureName: feature ? feature.label : featureName,
                featureDescription: feature ? feature.description : 'This is an enterprise feature',
                category: feature ? feature.category : 'general'
            },
            {
                onClose: () => {
                    // 重置字段值
                    if (record && fieldName) {
                        record.update({ [fieldName]: false });
                    }
                }
            }
        );
        
        return false;
    }
    
    // 获取功能状态
    getFeatureStatus(featureName) {
        const isAvailable = this.isFeatureAvailable(featureName);
        const feature = this.enterpriseFeatures.find(f => f.name === featureName);
        
        return {
            name: featureName,
            available: isAvailable,
            enterprise: !isAvailable,
            label: feature ? feature.label : featureName,
            description: feature ? feature.description : '',
            category: feature ? feature.category : 'general'
        };
    }
    
    // 获取所有功能状态
    getAllFeatureStatuses() {
        return this.enterpriseFeatures.map(feature => 
            this.getFeatureStatus(feature.name)
        );
    }
    
    // 按类别获取功能
    getFeaturesByCategory(category) {
        return this.enterpriseFeatures
            .filter(feature => feature.category === category)
            .map(feature => this.getFeatureStatus(feature.name));
    }
    
    // 获取可用的类别
    getAvailableCategories() {
        const categories = [...new Set(this.enterpriseFeatures.map(f => f.category))];
        return categories.map(category => ({
            name: category,
            label: this.formatCategoryLabel(category),
            features: this.getFeaturesByCategory(category)
        }));
    }
    
    // 格式化类别标签
    formatCategoryLabel(category) {
        return category.split('_')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    }
}

// 使用示例
const featureManager = new EnterpriseFeatureManager(env);

// 检查功能可用性
const isReportingAvailable = featureManager.isFeatureAvailable('advanced_reporting');
console.log('Advanced Reporting available:', isReportingAvailable);

// 处理功能激活
await featureManager.handleEnterpriseFeatureActivation('studio', 'enable_studio', record);

// 获取所有功能状态
const allFeatures = featureManager.getAllFeatureStatuses();
console.log('All features:', allFeatures);
```

### 2. 自定义升级布尔字段

```javascript
// 自定义升级布尔字段示例
class CustomUpgradeBooleanField extends UpgradeBooleanField {
    setup() {
        super.setup();
        this.featureInfo = this.getFeatureInfo();
        this.upgradeService = useService("upgrade");
    }
    
    // 获取功能信息
    getFeatureInfo() {
        const featureName = this.props.record.data.feature_name || this.props.name;
        return {
            name: featureName,
            label: this.props.record.data.feature_label || this.props.string,
            description: this.props.record.data.feature_description || '',
            category: this.props.record.data.feature_category || 'general',
            benefits: this.props.record.data.feature_benefits || [],
            pricing: this.props.record.data.feature_pricing || null
        };
    }
    
    async onChange(newValue) {
        if (!this.isEnterprise) {
            // 记录用户尝试激活企业版功能
            this.trackFeatureInterest();
            
            // 显示增强的升级对话框
            this.showEnhancedUpgradeDialog();
        } else {
            // 企业版中检查功能许可
            const hasLicense = await this.checkFeatureLicense();
            if (hasLicense) {
                super.onChange(...arguments);
            } else {
                this.showLicenseDialog();
            }
        }
    }
    
    // 跟踪功能兴趣
    trackFeatureInterest() {
        try {
            // 发送分析数据
            this.env.services.analytics?.track('enterprise_feature_interest', {
                feature_name: this.featureInfo.name,
                feature_category: this.featureInfo.category,
                user_id: this.env.services.user.userId,
                timestamp: Date.now()
            });
        } catch (error) {
            console.warn('Failed to track feature interest:', error);
        }
    }
    
    // 显示增强的升级对话框
    showEnhancedUpgradeDialog() {
        this.dialogService.add(
            EnhancedUpgradeDialog,
            {
                feature: this.featureInfo,
                currentPlan: this.getCurrentPlan(),
                upgradeOptions: this.getUpgradeOptions()
            },
            {
                onClose: () => {
                    this.props.record.update({ [this.props.name]: false });
                },
                onUpgrade: (plan) => {
                    this.handleUpgradeSelection(plan);
                },
                onTrial: () => {
                    this.handleTrialRequest();
                }
            }
        );
    }
    
    // 检查功能许可
    async checkFeatureLicense() {
        try {
            const response = await this.env.services.rpc('/web/dataset/call_kw', {
                model: 'ir.config_parameter',
                method: 'check_feature_license',
                args: [this.featureInfo.name],
                kwargs: {}
            });
            
            return response.licensed;
        } catch (error) {
            console.error('Failed to check feature license:', error);
            return false;
        }
    }
    
    // 显示许可对话框
    showLicenseDialog() {
        this.dialogService.add(
            LicenseDialog,
            {
                feature: this.featureInfo,
                licenseInfo: this.getLicenseInfo()
            },
            {
                onClose: () => {
                    this.props.record.update({ [this.props.name]: false });
                },
                onPurchase: () => {
                    this.handleLicensePurchase();
                }
            }
        );
    }
    
    // 获取当前计划
    getCurrentPlan() {
        return {
            name: this.isEnterprise ? 'Enterprise' : 'Community',
            features: this.isEnterprise ? 'All features' : 'Basic features',
            support: this.isEnterprise ? 'Priority support' : 'Community support'
        };
    }
    
    // 获取升级选项
    getUpgradeOptions() {
        return [
            {
                name: 'Enterprise',
                price: '$30/user/month',
                features: ['All enterprise features', 'Priority support', 'Advanced reporting'],
                recommended: true
            },
            {
                name: 'Custom',
                price: 'Contact sales',
                features: ['Tailored solution', 'Dedicated support', 'Custom development'],
                recommended: false
            }
        ];
    }
    
    // 处理升级选择
    handleUpgradeSelection(plan) {
        // 重定向到升级页面或联系销售
        window.open(`https://www.odoo.com/pricing?plan=${plan.name.toLowerCase()}`, '_blank');
    }
    
    // 处理试用请求
    handleTrialRequest() {
        // 重定向到试用页面
        window.open('https://www.odoo.com/trial', '_blank');
    }
    
    // 获取许可信息
    getLicenseInfo() {
        return {
            current: 'Basic License',
            required: 'Enterprise License',
            features: this.featureInfo.benefits || [],
            pricing: this.featureInfo.pricing
        };
    }
    
    // 处理许可购买
    handleLicensePurchase() {
        // 重定向到许可购买页面
        window.open(`https://www.odoo.com/buy-license?feature=${this.featureInfo.name}`, '_blank');
    }
}

// 增强的升级对话框组件
class EnhancedUpgradeDialog extends Component {
    static template = xml`
        <Dialog title="'Upgrade to Enterprise'" size="'lg'">
            <div class="upgrade-dialog-content">
                <div class="feature-info">
                    <h4 t-esc="props.feature.label"/>
                    <p t-esc="props.feature.description"/>
                    
                    <div class="feature-benefits" t-if="props.feature.benefits.length">
                        <h5>Benefits:</h5>
                        <ul>
                            <li t-foreach="props.feature.benefits" t-as="benefit" t-key="benefit" t-esc="benefit"/>
                        </ul>
                    </div>
                </div>
                
                <div class="upgrade-options">
                    <h5>Upgrade Options:</h5>
                    <div class="options-grid">
                        <t t-foreach="props.upgradeOptions" t-as="option" t-key="option.name">
                            <div class="upgrade-option" t-att-class="{ 'recommended': option.recommended }">
                                <h6 t-esc="option.name"/>
                                <div class="price" t-esc="option.price"/>
                                <ul class="features">
                                    <li t-foreach="option.features" t-as="feature" t-key="feature" t-esc="feature"/>
                                </ul>
                                <button class="btn btn-primary" t-on-click="() => this.props.onUpgrade(option)">
                                    Choose <t t-esc="option.name"/>
                                </button>
                            </div>
                        </t>
                    </div>
                </div>
                
                <div class="trial-option">
                    <button class="btn btn-secondary" t-on-click="props.onTrial">
                        Start Free Trial
                    </button>
                </div>
            </div>
        </Dialog>`;
    
    static props = {
        feature: Object,
        currentPlan: Object,
        upgradeOptions: Array,
        onClose: Function,
        onUpgrade: Function,
        onTrial: Function
    };
}

// 注册自定义字段
const customUpgradeBooleanField = {
    ...booleanField,
    component: CustomUpgradeBooleanField,
    additionalClasses: [...(booleanField.additionalClasses || []), "o_field_boolean", "o_field_upgrade"],
};

registry.category("fields").add("custom_upgrade_boolean", customUpgradeBooleanField);
```

## 技术特点

### 1. 继承扩展
- **基类继承**: 继承标准布尔字段的所有功能
- **行为重写**: 重写onChange方法实现自定义逻辑
- **配置保持**: 保持原有字段的配置和样式
- **功能增强**: 在原有功能基础上增加升级提示

### 2. 版本检测
- **全局信息**: 从odoo.info获取版本信息
- **实时检测**: 在组件初始化时检测版本
- **安全检查**: 确保信息存在性检查
- **布尔判断**: 简单高效的版本判断

### 3. 对话框集成
- **服务依赖**: 依赖对话框服务显示弹窗
- **组件传递**: 传递升级对话框组件
- **回调处理**: 处理对话框关闭回调
- **状态管理**: 管理字段状态的回滚

### 4. 状态回滚
- **自动回滚**: 对话框关闭时自动重置字段
- **记录更新**: 通过record.update更新字段值
- **状态一致**: 确保UI状态与数据状态一致
- **用户体验**: 提供良好的用户交互体验

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **功能继承**: 继承基类的所有功能
- **行为重写**: 重写特定方法实现自定义行为
- **接口保持**: 保持与基类相同的接口

### 2. 策略模式 (Strategy Pattern)
- **版本策略**: 根据版本类型执行不同策略
- **处理策略**: 企业版和社区版的不同处理策略
- **回调策略**: 不同的回调处理策略

### 3. 装饰器模式 (Decorator Pattern)
- **功能装饰**: 在基础布尔字段上装饰升级功能
- **行为增强**: 增强原有的变更处理行为
- **透明扩展**: 对外接口保持透明

## 注意事项

1. **版本检测**: 确保正确检测Odoo版本类型
2. **状态管理**: 正确处理字段状态的回滚
3. **用户体验**: 提供清晰的升级提示信息
4. **错误处理**: 处理对话框服务不可用的情况

## 扩展建议

1. **功能信息**: 添加具体功能的详细信息
2. **试用功能**: 提供企业版功能的试用选项
3. **价格信息**: 显示升级的价格信息
4. **使用统计**: 记录用户对企业版功能的兴趣
5. **个性化**: 根据用户行为个性化升级提示

该升级布尔字段组件为Odoo Web客户端提供了有效的企业版功能推广机制，通过智能的版本检测和用户友好的升级提示确保了良好的商业转化体验。

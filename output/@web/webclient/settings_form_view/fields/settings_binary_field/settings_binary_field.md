# SettingsBinaryField - 设置二进制字段

## 概述

`settings_binary_field.js` 是 Odoo Web 客户端的设置二进制字段组件，提供了配置设置中的文件处理功能。该模块包含32行代码，继承自标准二进制字段，专门用于处理设置表单中的关联二进制字段，具备关联字段解析、下载数据处理、模型映射等特性，是Odoo Web设置表单视图中文件管理的重要组件。

## 文件信息
- **路径**: `/web/static/src/webclient/settings_form_view/fields/settings_binary_field/settings_binary_field.js`
- **行数**: 32
- **模块**: `@web/webclient/settings_form_view/fields/settings_binary_field/settings_binary_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                                    // 注册表系统
'@web/views/fields/binary/binary_field'                // 二进制字段基类
```

## 主组件定义

### 1. SettingsBinaryField 组件

```javascript
class SettingsBinaryField extends BinaryField {
    static template = "web.SettingsBinaryField";

    getDownloadData() {
        const related = this.props.record.fields[this.props.name].related;
        return {
            ...super.getDownloadData(),
            model: this.props.record.fields[related.split(".")[0]].relation,
            field: related.split(".")[1] ?? related.split(".")[0],
            id: this.props.record.data[related.split(".")[0]][0],
        }
    }
}
```

**组件特性**:
- **继承二进制字段**: 继承标准二进制字段的所有功能
- **关联字段处理**: 专门处理关联的二进制字段
- **下载数据重写**: 重写下载数据的获取逻辑
- **设置专用模板**: 使用专门的设置模板

## 核心功能

### 1. 关联字段解析

```javascript
const related = this.props.record.fields[this.props.name].related;
```

**解析功能**:
- **字段信息**: 从字段定义中获取关联信息
- **关联路径**: 解析关联字段的路径
- **动态获取**: 动态获取关联字段配置
- **路径分析**: 分析关联字段的层次结构

### 2. 下载数据处理

```javascript
getDownloadData() {
    const related = this.props.record.fields[this.props.name].related;
    return {
        ...super.getDownloadData(),
        model: this.props.record.fields[related.split(".")[0]].relation,
        field: related.split(".")[1] ?? related.split(".")[0],
        id: this.props.record.data[related.split(".")[0]][0],
    }
}
```

**处理功能**:
- **基础数据**: 继承基类的下载数据
- **模型映射**: 映射到正确的关联模型
- **字段映射**: 映射到正确的关联字段
- **ID获取**: 获取关联记录的ID

### 3. 路径解析逻辑

```javascript
// 模型解析
model: this.props.record.fields[related.split(".")[0]].relation,

// 字段解析
field: related.split(".")[1] ?? related.split(".")[0],

// ID解析
id: this.props.record.data[related.split(".")[0]][0],
```

**解析逻辑**:
- **点分割**: 使用点号分割关联路径
- **模型关系**: 获取第一级关联的模型关系
- **字段名称**: 获取最终的字段名称
- **记录ID**: 获取关联记录的ID

## 字段注册

### 1. 字段定义

```javascript
const settingsBinaryField = {
    ...binaryField,
    component: SettingsBinaryField,
};
```

**字段配置**:
- **基础继承**: 继承标准二进制字段的所有配置
- **组件替换**: 使用自定义的SettingsBinaryField组件
- **功能扩展**: 扩展关联字段处理功能

### 2. 注册表注册

```javascript
registry.category("fields").add("base_settings.binary", settingsBinaryField);
```

**注册功能**:
- **字段类型**: 注册为"base_settings.binary"字段类型
- **设置专用**: 专门用于设置表单
- **全局可用**: 在整个应用中可用

## 使用场景

### 1. 设置表单中的文件管理

```javascript
// 设置表单中的文件管理示例
class SettingsFileManager {
    constructor(env) {
        this.env = env;
        this.orm = env.services.orm;
        this.fileService = env.services.file;
        this.setupFileHandlers();
    }
    
    setupFileHandlers() {
        // 设置文件处理器
        this.supportedTypes = {
            image: ['jpg', 'jpeg', 'png', 'gif', 'svg'],
            document: ['pdf', 'doc', 'docx', 'xls', 'xlsx'],
            certificate: ['pem', 'crt', 'key', 'p12'],
            config: ['xml', 'json', 'yaml', 'conf']
        };
        
        this.maxFileSize = 10 * 1024 * 1024; // 10MB
    }
    
    // 处理设置文件上传
    async handleSettingsFileUpload(fieldName, file, record) {
        try {
            // 验证文件
            const validation = this.validateFile(file);
            if (!validation.valid) {
                throw new Error(validation.message);
            }
            
            // 获取关联字段信息
            const relatedInfo = this.parseRelatedField(fieldName, record);
            
            // 上传文件
            const uploadResult = await this.uploadFile(file, relatedInfo);
            
            // 更新记录
            await this.updateRelatedRecord(relatedInfo, uploadResult);
            
            // 刷新界面
            await record.load();
            
            this.env.services.notification.add(
                'File uploaded successfully',
                { type: 'success' }
            );
            
            return uploadResult;
            
        } catch (error) {
            console.error('File upload failed:', error);
            this.env.services.notification.add(
                `File upload failed: ${error.message}`,
                { type: 'danger' }
            );
            throw error;
        }
    }
    
    // 验证文件
    validateFile(file) {
        // 检查文件大小
        if (file.size > this.maxFileSize) {
            return {
                valid: false,
                message: `File size exceeds ${this.maxFileSize / 1024 / 1024}MB limit`
            };
        }
        
        // 检查文件类型
        const extension = file.name.split('.').pop().toLowerCase();
        const isSupported = Object.values(this.supportedTypes)
            .some(types => types.includes(extension));
        
        if (!isSupported) {
            return {
                valid: false,
                message: `File type .${extension} is not supported`
            };
        }
        
        // 检查文件名
        if (file.name.length > 255) {
            return {
                valid: false,
                message: 'File name is too long'
            };
        }
        
        return { valid: true };
    }
    
    // 解析关联字段
    parseRelatedField(fieldName, record) {
        const fieldDef = record.fields[fieldName];
        if (!fieldDef || !fieldDef.related) {
            throw new Error(`Field ${fieldName} is not a related field`);
        }
        
        const related = fieldDef.related;
        const parts = related.split('.');
        
        if (parts.length < 2) {
            throw new Error(`Invalid related field format: ${related}`);
        }
        
        const relationField = parts[0];
        const targetField = parts[1];
        
        const relationFieldDef = record.fields[relationField];
        if (!relationFieldDef) {
            throw new Error(`Relation field ${relationField} not found`);
        }
        
        const relationModel = relationFieldDef.relation;
        const relationId = record.data[relationField];
        
        if (!relationId || (Array.isArray(relationId) && relationId.length === 0)) {
            throw new Error(`No related record found for field ${relationField}`);
        }
        
        const recordId = Array.isArray(relationId) ? relationId[0] : relationId;
        
        return {
            model: relationModel,
            field: targetField,
            id: recordId,
            relationField: relationField,
            originalField: fieldName
        };
    }
    
    // 上传文件
    async uploadFile(file, relatedInfo) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('model', relatedInfo.model);
        formData.append('field', relatedInfo.field);
        formData.append('id', relatedInfo.id);
        
        const response = await fetch('/web/binary/upload', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        if (!response.ok) {
            throw new Error(`Upload failed: ${response.statusText}`);
        }
        
        return await response.json();
    }
    
    // 更新关联记录
    async updateRelatedRecord(relatedInfo, uploadResult) {
        await this.orm.call(relatedInfo.model, 'write', [
            [relatedInfo.id],
            { [relatedInfo.field]: uploadResult.id }
        ]);
    }
    
    // 处理文件下载
    async handleFileDownload(fieldName, record) {
        try {
            const relatedInfo = this.parseRelatedField(fieldName, record);
            const downloadData = this.getDownloadData(relatedInfo, record);
            
            // 构建下载URL
            const downloadUrl = this.buildDownloadUrl(downloadData);
            
            // 触发下载
            this.triggerDownload(downloadUrl, downloadData.filename);
            
        } catch (error) {
            console.error('File download failed:', error);
            this.env.services.notification.add(
                `File download failed: ${error.message}`,
                { type: 'danger' }
            );
        }
    }
    
    // 获取下载数据
    getDownloadData(relatedInfo, record) {
        return {
            model: relatedInfo.model,
            field: relatedInfo.field,
            id: relatedInfo.id,
            filename: this.getFileName(relatedInfo, record)
        };
    }
    
    // 获取文件名
    getFileName(relatedInfo, record) {
        // 尝试从记录中获取文件名
        const filenameField = `${relatedInfo.field}_name`;
        const filename = record.data[filenameField];
        
        if (filename) {
            return filename;
        }
        
        // 生成默认文件名
        return `${relatedInfo.model}_${relatedInfo.field}_${relatedInfo.id}`;
    }
    
    // 构建下载URL
    buildDownloadUrl(downloadData) {
        const params = new URLSearchParams({
            model: downloadData.model,
            field: downloadData.field,
            id: downloadData.id,
            filename: downloadData.filename
        });
        
        return `/web/content?${params.toString()}`;
    }
    
    // 触发下载
    triggerDownload(url, filename) {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
    
    // 处理文件删除
    async handleFileDelete(fieldName, record) {
        try {
            const relatedInfo = this.parseRelatedField(fieldName, record);
            
            // 确认删除
            const confirmed = await this.confirmDelete();
            if (!confirmed) {
                return;
            }
            
            // 删除文件
            await this.deleteFile(relatedInfo);
            
            // 刷新记录
            await record.load();
            
            this.env.services.notification.add(
                'File deleted successfully',
                { type: 'success' }
            );
            
        } catch (error) {
            console.error('File deletion failed:', error);
            this.env.services.notification.add(
                `File deletion failed: ${error.message}`,
                { type: 'danger' }
            );
        }
    }
    
    // 确认删除
    async confirmDelete() {
        return new Promise((resolve) => {
            this.env.services.dialog.add(ConfirmationDialog, {
                title: 'Delete File',
                body: 'Are you sure you want to delete this file?',
                confirm: () => resolve(true),
                cancel: () => resolve(false)
            });
        });
    }
    
    // 删除文件
    async deleteFile(relatedInfo) {
        await this.orm.call(relatedInfo.model, 'write', [
            [relatedInfo.id],
            { [relatedInfo.field]: false }
        ]);
    }
    
    // 获取文件信息
    async getFileInfo(fieldName, record) {
        try {
            const relatedInfo = this.parseRelatedField(fieldName, record);
            
            const fileInfo = await this.orm.call(relatedInfo.model, 'read', [
                [relatedInfo.id],
                [relatedInfo.field, `${relatedInfo.field}_name`, `${relatedInfo.field}_size`]
            ]);
            
            if (fileInfo.length === 0) {
                return null;
            }
            
            const info = fileInfo[0];
            return {
                hasFile: !!info[relatedInfo.field],
                filename: info[`${relatedInfo.field}_name`] || 'Unknown',
                size: info[`${relatedInfo.field}_size`] || 0,
                sizeFormatted: this.formatFileSize(info[`${relatedInfo.field}_size`] || 0)
            };
            
        } catch (error) {
            console.error('Failed to get file info:', error);
            return null;
        }
    }
    
    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// 使用示例
const fileManager = new SettingsFileManager(env);

// 处理文件上传
await fileManager.handleSettingsFileUpload('company_logo', file, record);

// 处理文件下载
await fileManager.handleFileDownload('company_logo', record);

// 获取文件信息
const fileInfo = await fileManager.getFileInfo('company_logo', record);
console.log('File info:', fileInfo);
```

## 技术特点

### 1. 继承扩展
- **基类继承**: 继承标准二进制字段的所有功能
- **方法重写**: 重写getDownloadData方法
- **功能保持**: 保持原有字段的基础功能
- **专用扩展**: 专门扩展关联字段处理

### 2. 关联字段处理
- **路径解析**: 智能解析关联字段路径
- **模型映射**: 正确映射到关联模型
- **字段映射**: 准确映射到目标字段
- **ID获取**: 动态获取关联记录ID

### 3. 数据处理
- **点分割**: 使用点号分割关联路径
- **空值处理**: 使用空值合并操作符处理缺失值
- **数组处理**: 正确处理关联字段的数组格式
- **类型安全**: 确保数据类型的正确性

### 4. 下载优化
- **数据重构**: 重构下载数据结构
- **路径正确**: 确保下载路径的正确性
- **参数完整**: 提供完整的下载参数
- **兼容性**: 保持与基类的兼容性

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **功能继承**: 继承基类的所有功能
- **方法重写**: 重写特定方法实现自定义逻辑
- **接口保持**: 保持与基类相同的接口

### 2. 适配器模式 (Adapter Pattern)
- **数据适配**: 适配关联字段的数据结构
- **接口适配**: 适配下载接口的参数格式
- **模型适配**: 适配不同模型的字段访问

### 3. 模板方法模式 (Template Method Pattern)
- **模板重用**: 重用基类的模板结构
- **行为定制**: 定制特定的下载行为
- **扩展点**: 提供明确的扩展点

## 注意事项

1. **关联字段**: 确保字段定义中包含正确的关联信息
2. **路径解析**: 正确解析关联字段的路径结构
3. **数据格式**: 处理关联字段数据的不同格式
4. **错误处理**: 处理关联字段不存在的情况

## 扩展建议

1. **文件验证**: 添加文件类型和大小验证
2. **预览功能**: 支持文件预览功能
3. **批量操作**: 支持批量文件操作
4. **版本管理**: 支持文件版本管理
5. **权限控制**: 集成文件访问权限控制

该设置二进制字段组件为Odoo Web客户端的设置表单提供了专业的关联文件处理功能，通过智能的关联字段解析和优化的下载数据处理确保了文件操作的准确性和可靠性。

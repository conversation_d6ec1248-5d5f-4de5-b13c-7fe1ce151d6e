# WebClient - Web客户端

## 概述

`webclient.js` 是 Odoo Web 客户端的核心组件，提供了整个Web应用程序的主要容器和协调功能。该模块包含158行代码，是一个OWL组件，专门用作Web客户端的根组件，具备路由管理、菜单协调、动作容器、全屏控制、Service Worker注册等特性，是Odoo Web客户端中应用程序架构的核心组件。

## 文件信息
- **路径**: `/web/static/src/webclient/webclient.js`
- **行数**: 158
- **模块**: `@web/webclient/webclient`

## 依赖关系

```javascript
// 核心依赖
'@web/core/debug/debug_context'             // 调试上下文
'@web/core/debug/debug_menu'                // 调试菜单
'@web/core/l10n/localization'               // 本地化
'@web/core/main_components_container'       // 主组件容器
'@web/core/registry'                        // 注册表系统
'@web/core/utils/hooks'                     // 工具钩子
'@web/webclient/actions/action_container'   // 动作容器
'@web/webclient/navbar/navbar'              // 导航栏
'@odoo/owl'                                 // OWL框架
'@web/core/browser/router'                  // 路由管理
'@web/core/browser/browser'                 // 浏览器工具
```

## 主组件定义

### 1. WebClient 组件

```javascript
class WebClient extends Component {
    static template = "web.WebClient";
    static props = {};
    static components = {
        ActionContainer,
        NavBar,
        MainComponentsContainer,
    };

    setup() {
        this.menuService = useService("menu");
        this.actionService = useService("action");
        this.title = useService("title");
        useOwnDebugContext({ categories: ["default"] });
        
        if (this.env.debug) {
            registry.category("systray").add(
                "web.debug_mode_menu",
                { Component: DebugMenu },
                { sequence: 100 }
            );
        }
        
        this.localization = localization;
        this.state = useState({ fullscreen: false });
        
        useBus(routerBus, "ROUTE_CHANGE", this.loadRouterState);
        useBus(this.env.bus, "ACTION_MANAGER:UI-UPDATED", ({ detail: mode }) => {
            if (mode !== "new") {
                this.state.fullscreen = mode === "fullscreen";
            }
        });
        
        onMounted(() => {
            this.loadRouterState();
            this.env.bus.trigger("WEB_CLIENT_READY");
        });
        
        useExternalListener(window, "click", this.onGlobalClick, { capture: true });
        onWillStart(this.registerServiceWorker);
    }
}
```

**组件特性**:
- **根组件**: 作为整个Web应用的根组件
- **服务集成**: 集成菜单、动作、标题服务
- **调试支持**: 支持调试模式和调试菜单
- **状态管理**: 管理全屏等全局状态

## 核心属性

### 1. 组件配置

```javascript
// 模板配置
static template = "web.WebClient";

// 子组件
static components = {
    ActionContainer,        // 动作容器
    NavBar,                // 导航栏
    MainComponentsContainer, // 主组件容器
};

// 属性定义
static props = {};
```

**配置功能**:
- **template**: 指定Web客户端的模板
- **components**: 包含核心子组件
- **props**: 无需外部属性，完全自包含

### 2. 服务引用

```javascript
// 服务集成
this.menuService = useService("menu");
this.actionService = useService("action");
this.title = useService("title");
```

**服务功能**:
- **menuService**: 菜单管理服务
- **actionService**: 动作执行服务
- **title**: 页面标题服务

### 3. 状态管理

```javascript
// 状态定义
this.state = useState({
    fullscreen: false,      // 全屏状态
});
```

**状态功能**:
- **fullscreen**: 控制应用的全屏显示状态

## 核心功能

### 1. 路由状态加载

```javascript
async loadRouterState() {
    // URL向后兼容性处理
    let menuId = Number(router.current.menu_id || 0);
    const firstAction = router.current.actionStack?.[0]?.action;
    
    if (!menuId && firstAction) {
        menuId = this.menuService
            .getAll()
            .find((m) => m.actionID === firstAction || m.actionPath === firstAction)?.appID;
    }
    
    if (menuId) {
        this.menuService.setCurrentMenu(menuId);
    }
    
    let stateLoaded = await this.actionService.loadState();

    // 只有menu_id的URL兼容性处理
    if (!stateLoaded && menuId) {
        const menu = this.menuService.getAll().find((m) => menuId === m.id);
        const actionId = menu && menu.actionID;
        if (actionId) {
            await this.actionService.doAction(actionId, { clearBreadcrumbs: true });
            stateLoaded = true;
        }
    }

    // 基于动作设置菜单
    if (stateLoaded && !menuId) {
        const currentController = this.actionService.currentController;
        const actionId = currentController && currentController.action.id;
        menuId = this.menuService.getAll().find((m) => m.actionID === actionId)?.appID;
        if (menuId) {
            this.menuService.setCurrentMenu(menuId);
        }
    }

    // 滚动到锚点
    if (stateLoaded) {
        if (browser.location.hash !== "") {
            try {
                const el = document.querySelector(browser.location.hash);
                if (el !== null) {
                    el.scrollIntoView(true);
                }
            } catch {
                // 忽略无效的选择器
            }
        }
    }

    if (!stateLoaded) {
        await this._loadDefaultApp();
    }
}
```

**路由加载功能**:
- **向后兼容**: 处理旧版本URL格式
- **菜单解析**: 从URL解析菜单ID
- **动作加载**: 加载对应的动作
- **状态同步**: 同步菜单和动作状态
- **锚点滚动**: 支持页面锚点滚动
- **默认回退**: 无状态时加载默认应用

### 2. 默认应用加载

```javascript
_loadDefaultApp() {
    const root = this.menuService.getMenu("root");
    const firstApp = root.children[0];
    if (firstApp) {
        return this.menuService.selectMenu(firstApp);
    }
}
```

**默认加载功能**:
- **根菜单**: 获取根菜单
- **首个应用**: 选择第一个子应用
- **自动选择**: 自动选择默认应用
- **容错处理**: 处理无应用的情况

### 3. 全局点击处理

```javascript
onGlobalClick(ev) {
    if (
        (ev.ctrlKey || ev.metaKey) &&
        !ev.target.isContentEditable &&
        ((ev.target instanceof HTMLAnchorElement && ev.target.href) ||
            (ev.target instanceof HTMLElement && ev.target.closest("a[href]:not([href=''])")))
    ) {
        ev.stopImmediatePropagation();
        return;
    }
}
```

**点击处理功能**:
- **Ctrl+点击**: 处理Ctrl/Cmd+点击事件
- **链接检测**: 检测点击的是否为链接
- **浏览器行为**: 允许浏览器默认行为
- **事件阻止**: 阻止其他监听器执行

### 4. Service Worker注册

```javascript
registerServiceWorker() {
    if (navigator.serviceWorker) {
        navigator.serviceWorker
            .register("/web/service-worker.js", { scope: "/odoo" })
            .catch((error) => {
                console.error("Service worker registration failed, error:", error);
            });
    }
}
```

**Service Worker功能**:
- **支持检测**: 检测浏览器Service Worker支持
- **注册服务**: 注册Service Worker
- **作用域**: 设置Service Worker作用域
- **错误处理**: 处理注册失败的情况

### 5. 调试模式支持

```javascript
if (this.env.debug) {
    registry.category("systray").add(
        "web.debug_mode_menu",
        { Component: DebugMenu },
        { sequence: 100 }
    );
}
```

**调试支持功能**:
- **调试检测**: 检测是否为调试模式
- **调试菜单**: 添加调试菜单到系统托盘
- **调试上下文**: 设置调试上下文
- **开发工具**: 提供开发调试工具

## 使用场景

### 1. Web客户端管理器

```javascript
// Web客户端管理器
class WebClientManager {
    constructor(env) {
        this.env = env;
        this.setupManager();
    }
    
    setupManager() {
        // 设置客户端配置
        this.clientConfig = {
            enableDebugMode: false,
            enableServiceWorker: true,
            enableFullscreen: true,
            enableRouterHistory: true,
            enableErrorBoundary: true
        };
        
        // 设置性能监控
        this.performanceMetrics = {
            loadTime: 0,
            renderTime: 0,
            routeChanges: 0,
            actionExecutions: 0
        };
        
        // 设置错误跟踪
        this.errorTracking = {
            errors: [],
            warnings: [],
            performance: []
        };
        
        this.setupEventListeners();
        this.initializePerformanceMonitoring();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听Web客户端就绪
        this.env.bus.addEventListener('WEB_CLIENT_READY', (event) => {
            this.handleWebClientReady(event.detail);
        });
        
        // 监听路由变化
        this.env.bus.addEventListener('ROUTE_CHANGE', (event) => {
            this.handleRouteChange(event.detail);
        });
        
        // 监听动作执行
        this.env.bus.addEventListener('ACTION_EXECUTED', (event) => {
            this.handleActionExecution(event.detail);
        });
    }
    
    // 初始化性能监控
    initializePerformanceMonitoring() {
        // 监控页面加载性能
        if (window.performance && window.performance.timing) {
            const timing = window.performance.timing;
            this.performanceMetrics.loadTime = timing.loadEventEnd - timing.navigationStart;
        }
        
        // 监控渲染性能
        if (window.performance && window.performance.mark) {
            window.performance.mark('webclient-start');
        }
    }
    
    // 创建增强的Web客户端
    createEnhancedWebClient() {
        const originalWebClient = WebClient;
        
        return class EnhancedWebClient extends originalWebClient {
            setup() {
                super.setup();
                
                // 添加性能监控
                this.addPerformanceMonitoring();
                
                // 添加错误边界
                this.addErrorBoundary();
                
                // 添加状态持久化
                this.addStatePersistence();
                
                // 添加可访问性支持
                this.addAccessibilitySupport();
            }
            
            addPerformanceMonitoring() {
                const originalLoadRouterState = this.loadRouterState.bind(this);
                
                this.loadRouterState = async function() {
                    const startTime = performance.now();
                    
                    try {
                        const result = await originalLoadRouterState();
                        
                        const endTime = performance.now();
                        const duration = endTime - startTime;
                        
                        this.recordPerformanceMetric('routeLoad', duration);
                        
                        return result;
                    } catch (error) {
                        this.recordError('routeLoad', error);
                        throw error;
                    }
                };
            }
            
            addErrorBoundary() {
                this.errorBoundary = {
                    hasError: false,
                    error: null,
                    errorInfo: null
                };
                
                // 全局错误处理
                window.addEventListener('error', (event) => {
                    this.handleGlobalError(event.error);
                });
                
                window.addEventListener('unhandledrejection', (event) => {
                    this.handleGlobalError(event.reason);
                });
            }
            
            addStatePersistence() {
                // 保存应用状态
                this.saveAppState = function() {
                    const state = {
                        currentMenu: this.menuService.getCurrentApp()?.id,
                        fullscreen: this.state.fullscreen,
                        timestamp: Date.now()
                    };
                    
                    try {
                        localStorage.setItem('odoo_webclient_state', JSON.stringify(state));
                    } catch (error) {
                        console.warn('Failed to save app state:', error);
                    }
                };
                
                // 恢复应用状态
                this.restoreAppState = function() {
                    try {
                        const stored = localStorage.getItem('odoo_webclient_state');
                        if (stored) {
                            const state = JSON.parse(stored);
                            
                            // 检查状态是否过期（24小时）
                            if (Date.now() - state.timestamp < 24 * 60 * 60 * 1000) {
                                if (state.fullscreen) {
                                    this.state.fullscreen = true;
                                }
                                
                                return state;
                            }
                        }
                    } catch (error) {
                        console.warn('Failed to restore app state:', error);
                    }
                    
                    return null;
                };
                
                // 在组件卸载时保存状态
                onWillUnmount(() => {
                    this.saveAppState();
                });
            }
            
            addAccessibilitySupport() {
                // 键盘导航支持
                this.setupKeyboardNavigation = function() {
                    document.addEventListener('keydown', (event) => {
                        // Alt + M: 跳转到主菜单
                        if (event.altKey && event.key === 'm') {
                            event.preventDefault();
                            this.focusMainMenu();
                        }
                        
                        // Alt + C: 跳转到内容区域
                        if (event.altKey && event.key === 'c') {
                            event.preventDefault();
                            this.focusMainContent();
                        }
                        
                        // Escape: 退出全屏
                        if (event.key === 'Escape' && this.state.fullscreen) {
                            this.exitFullscreen();
                        }
                    });
                };
                
                this.focusMainMenu = function() {
                    const menuElement = document.querySelector('.o_navbar_apps_menu');
                    if (menuElement) {
                        menuElement.focus();
                    }
                };
                
                this.focusMainContent = function() {
                    const contentElement = document.querySelector('.o_action_manager');
                    if (contentElement) {
                        contentElement.focus();
                    }
                };
                
                this.exitFullscreen = function() {
                    this.state.fullscreen = false;
                };
                
                // 初始化键盘导航
                onMounted(() => {
                    this.setupKeyboardNavigation();
                });
            }
            
            recordPerformanceMetric(operation, duration) {
                this.performanceMetrics[operation] = this.performanceMetrics[operation] || [];
                this.performanceMetrics[operation].push({
                    duration: duration,
                    timestamp: Date.now()
                });
                
                // 限制记录数量
                if (this.performanceMetrics[operation].length > 100) {
                    this.performanceMetrics[operation].shift();
                }
            }
            
            recordError(context, error) {
                this.errorTracking.errors.push({
                    context: context,
                    error: error.message,
                    stack: error.stack,
                    timestamp: Date.now()
                });
                
                // 限制错误记录数量
                if (this.errorTracking.errors.length > 50) {
                    this.errorTracking.errors.shift();
                }
            }
            
            handleGlobalError(error) {
                console.error('Global error caught:', error);
                
                this.recordError('global', error);
                
                // 触发错误事件
                this.env.bus.trigger('WEBCLIENT_ERROR', {
                    error: error,
                    timestamp: Date.now()
                });
            }
            
            getPerformanceReport() {
                return {
                    metrics: this.performanceMetrics,
                    errors: this.errorTracking.errors,
                    config: this.clientConfig
                };
            }
        };
    }
    
    // 处理Web客户端就绪
    handleWebClientReady(readyDetail) {
        console.log('WebClient ready:', readyDetail);
        
        // 记录就绪时间
        if (window.performance && window.performance.mark) {
            window.performance.mark('webclient-ready');
            window.performance.measure('webclient-init', 'webclient-start', 'webclient-ready');
        }
        
        // 触发就绪完成事件
        this.env.bus.trigger('WEBCLIENT_INITIALIZATION_COMPLETED', {
            timestamp: Date.now()
        });
    }
    
    // 处理路由变化
    handleRouteChange(routeDetail) {
        console.log('Route changed:', routeDetail);
        
        this.performanceMetrics.routeChanges++;
        
        // 触发路由变化事件
        this.env.bus.trigger('WEBCLIENT_ROUTE_CHANGED', {
            route: routeDetail,
            timestamp: Date.now()
        });
    }
    
    // 处理动作执行
    handleActionExecution(actionDetail) {
        console.log('Action executed:', actionDetail);
        
        this.performanceMetrics.actionExecutions++;
        
        // 触发动作执行事件
        this.env.bus.trigger('WEBCLIENT_ACTION_EXECUTED', {
            action: actionDetail,
            timestamp: Date.now()
        });
    }
    
    // 获取客户端统计
    getClientStatistics() {
        return {
            config: this.clientConfig,
            performance: this.performanceMetrics,
            errors: this.errorTracking.errors.length,
            warnings: this.errorTracking.warnings.length,
            uptime: Date.now() - (this.performanceMetrics.startTime || Date.now())
        };
    }
    
    // 导出诊断报告
    exportDiagnosticReport() {
        const report = {
            exportDate: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href,
            statistics: this.getClientStatistics(),
            performance: this.performanceMetrics,
            errors: this.errorTracking.errors,
            config: this.clientConfig
        };
        
        const blob = new Blob([JSON.stringify(report, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `webclient_diagnostic_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
    }
    
    // 销毁管理器
    destroy() {
        // 清理事件监听器
        this.env.bus.removeEventListener('WEB_CLIENT_READY');
        this.env.bus.removeEventListener('ROUTE_CHANGE');
        this.env.bus.removeEventListener('ACTION_EXECUTED');
        
        // 清理数据
        this.errorTracking.errors = [];
        this.errorTracking.warnings = [];
    }
}

// 使用示例
const webClientManager = new WebClientManager(env);

// 创建增强的Web客户端
const EnhancedWebClient = webClientManager.createEnhancedWebClient();

// 获取统计信息
const stats = webClientManager.getClientStatistics();
console.log('WebClient statistics:', stats);

// 导出诊断报告
webClientManager.exportDiagnosticReport();
```

## 技术特点

### 1. 应用架构
- **根组件**: 作为整个应用的根组件
- **组件组合**: 组合多个核心子组件
- **服务协调**: 协调各种服务的使用
- **状态管理**: 管理全局应用状态

### 2. 路由集成
- **路由监听**: 监听路由变化事件
- **状态加载**: 自动加载路由状态
- **向后兼容**: 支持旧版本URL格式
- **默认处理**: 智能的默认应用处理

### 3. PWA支持
- **Service Worker**: 自动注册Service Worker
- **离线支持**: 支持离线功能
- **缓存策略**: 实现缓存策略
- **原生体验**: 提供原生应用体验

### 4. 调试支持
- **调试模式**: 支持调试模式
- **调试菜单**: 提供调试菜单
- **调试上下文**: 设置调试上下文
- **开发工具**: 集成开发工具

## 设计模式

### 1. 容器模式 (Container Pattern)
- **组件容器**: 作为其他组件的容器
- **服务容器**: 管理和协调服务
- **状态容器**: 管理全局状态

### 2. 协调器模式 (Coordinator Pattern)
- **组件协调**: 协调子组件的工作
- **服务协调**: 协调服务间的交互
- **状态协调**: 协调状态的同步

### 3. 观察者模式 (Observer Pattern)
- **事件监听**: 监听各种系统事件
- **状态响应**: 响应状态变化
- **自动更新**: 自动更新相关组件

### 4. 策略模式 (Strategy Pattern)
- **路由策略**: 不同的路由处理策略
- **加载策略**: 不同的应用加载策略
- **错误策略**: 不同的错误处理策略

## 注意事项

1. **性能影响**: 避免在根组件中进行重计算
2. **内存管理**: 及时清理事件监听器
3. **错误处理**: 提供全局错误处理机制
4. **兼容性**: 确保向后兼容性

## 扩展建议

1. **错误边界**: 添加React式的错误边界
2. **性能监控**: 增强性能监控功能
3. **状态持久化**: 添加应用状态持久化
4. **可访问性**: 增强可访问性支持
5. **国际化**: 完善国际化支持

该Web客户端组件为Odoo Web应用提供了完整的根容器功能，通过路由管理、组件协调和服务集成确保了整个应用的正常运行和良好的用户体验。

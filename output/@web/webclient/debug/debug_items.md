# Debug Items - 调试项目

## 概述

`debug_items.js` 是 Odoo Web 客户端的调试项目模块，提供了开发和测试相关的调试功能。该模块包含65行代码，定义了单元测试运行和视图打开等调试工具，具备测试执行、视图选择、对话框集成等特性，是Odoo Web开发调试工具集的重要组成部分。

## 文件信息
- **路径**: `/web/static/src/webclient/debug/debug_items.js`
- **行数**: 65
- **模块**: `@web/webclient/debug/debug_items`

## 依赖关系

```javascript
// 核心依赖
'@web/core/browser/browser'                                     // 浏览器服务
'@web/core/l10n/translation'                                    // 国际化翻译
'@web/core/registry'                                            // 注册表系统
'@web/views/view_dialogs/select_create_dialog'                  // 选择创建对话框
```

## 核心功能

### 1. 单元测试运行器

```javascript
function runUnitTestsItem() {
    const href = "/web/tests?debug=assets";
    return {
        type: "item",
        description: _t("Run Unit Tests"),
        href,
        callback: () => browser.open(href),
        sequence: 450,
        section: "testing",
    };
}
```

**单元测试功能**:
- **测试URL**: 构建单元测试的访问URL
- **新窗口**: 在新窗口中打开测试页面
- **调试模式**: 启用assets调试模式
- **测试分组**: 归类到testing分组

**功能特点**:
- **便捷访问**: 提供便捷的测试访问入口
- **调试支持**: 支持调试模式下的测试
- **独立窗口**: 在独立窗口中运行测试
- **开发工具**: 作为开发工具的一部分

### 2. 视图打开器

```javascript
function openViewItem({ env }) {
    async function onSelected(records) {
        const views = await env.services.orm.searchRead(
            "ir.ui.view",
            [["id", "=", records[0]]],
            ["name", "model", "type"],
            { limit: 1 }
        );
        const view = views[0];
        env.services.action.doAction({
            type: "ir.actions.act_window",
            name: view.name,
            res_model: view.model,
            views: [[view.id, view.type]],
        });
    }

    return {
        type: "item",
        description: _t("Open View"),
        callback: () => {
            env.services.dialog.add(SelectCreateDialog, {
                resModel: "ir.ui.view",
                title: _t("Select a view"),
                multiSelect: false,
                domain: [
                    ["type", "!=", "qweb"],
                    ["type", "!=", "search"],
                ],
                onSelected,
            });
        },
        sequence: 540,
        section: "tools",
    };
}
```

**视图打开功能**:
- **视图选择**: 通过对话框选择要打开的视图
- **视图过滤**: 过滤掉qweb和search类型的视图
- **动作执行**: 执行窗口动作打开选中的视图
- **工具分组**: 归类到tools分组

**功能特点**:
- **交互式选择**: 提供交互式的视图选择界面
- **智能过滤**: 智能过滤不适合的视图类型
- **即时打开**: 选择后即时打开视图
- **开发辅助**: 辅助开发者快速访问视图

## 注册表集成

```javascript
registry
    .category("debug")
    .category("default")
    .add("runUnitTestsItem", runUnitTestsItem)
    .add("openViewItem", openViewItem);
```

**注册功能**:
- **调试分类**: 注册到debug分类下
- **默认子分类**: 使用default子分类
- **项目注册**: 注册两个调试项目
- **序列控制**: 通过sequence控制显示顺序

## 使用场景

### 1. 开发调试工具集

```javascript
// 开发调试工具集
class DevelopmentDebugTools {
    constructor(env) {
        this.env = env;
        this.testRunner = new TestRunner(env);
        this.viewExplorer = new ViewExplorer(env);
        this.setupDebugItems();
    }
    
    setupDebugItems() {
        // 注册单元测试运行器
        this.registerTestRunner();
        
        // 注册视图浏览器
        this.registerViewExplorer();
        
        // 注册自定义调试项目
        this.registerCustomDebugItems();
    }
    
    registerTestRunner() {
        const testRunnerItem = {
            type: "item",
            description: "Run Unit Tests",
            callback: () => this.testRunner.runTests(),
            sequence: 450,
            section: "testing",
            icon: "fa-play-circle"
        };
        
        this.env.services.debug.addItem("testRunner", testRunnerItem);
    }
    
    registerViewExplorer() {
        const viewExplorerItem = {
            type: "item",
            description: "Explore Views",
            callback: () => this.viewExplorer.openExplorer(),
            sequence: 540,
            section: "tools",
            icon: "fa-eye"
        };
        
        this.env.services.debug.addItem("viewExplorer", viewExplorerItem);
    }
    
    registerCustomDebugItems() {
        // 数据库查询工具
        this.env.services.debug.addItem("dbQuery", {
            type: "item",
            description: "Database Query Tool",
            callback: () => this.openDatabaseQueryTool(),
            sequence: 600,
            section: "tools",
            icon: "fa-database"
        });
        
        // 性能分析器
        this.env.services.debug.addItem("profiler", {
            type: "item",
            description: "Performance Profiler",
            callback: () => this.openPerformanceProfiler(),
            sequence: 650,
            section: "performance",
            icon: "fa-tachometer"
        });
        
        // 日志查看器
        this.env.services.debug.addItem("logViewer", {
            type: "item",
            description: "Log Viewer",
            callback: () => this.openLogViewer(),
            sequence: 700,
            section: "monitoring",
            icon: "fa-file-text"
        });
    }
    
    async openDatabaseQueryTool() {
        await this.env.services.action.doAction({
            type: 'ir.actions.client',
            tag: 'database_query_tool',
            target: 'new'
        });
    }
    
    async openPerformanceProfiler() {
        await this.env.services.action.doAction({
            type: 'ir.actions.client',
            tag: 'performance_profiler',
            target: 'new'
        });
    }
    
    async openLogViewer() {
        await this.env.services.action.doAction({
            type: 'ir.actions.client',
            tag: 'log_viewer',
            target: 'new'
        });
    }
}

// 测试运行器
class TestRunner {
    constructor(env) {
        this.env = env;
        this.testSuites = [];
        this.testResults = new Map();
    }
    
    async runTests(options = {}) {
        const testUrl = this.buildTestUrl(options);
        
        if (options.inNewWindow !== false) {
            // 在新窗口中运行测试
            window.open(testUrl, '_blank');
        } else {
            // 在当前窗口中运行测试
            window.location.href = testUrl;
        }
    }
    
    buildTestUrl(options) {
        const params = new URLSearchParams();
        
        // 基础参数
        params.set('debug', 'assets');
        
        // 可选参数
        if (options.module) {
            params.set('module', options.module);
        }
        
        if (options.test) {
            params.set('test', options.test);
        }
        
        if (options.coverage) {
            params.set('coverage', 'true');
        }
        
        return `/web/tests?${params.toString()}`;
    }
    
    async runSpecificTest(moduleName, testName) {
        const testUrl = this.buildTestUrl({
            module: moduleName,
            test: testName
        });
        
        window.open(testUrl, '_blank');
    }
    
    async runModuleTests(moduleName) {
        const testUrl = this.buildTestUrl({
            module: moduleName
        });
        
        window.open(testUrl, '_blank');
    }
    
    async runWithCoverage() {
        const testUrl = this.buildTestUrl({
            coverage: true
        });
        
        window.open(testUrl, '_blank');
    }
    
    getAvailableTestSuites() {
        return [
            { name: 'web', description: 'Web Core Tests' },
            { name: 'web_enterprise', description: 'Web Enterprise Tests' },
            { name: 'account', description: 'Accounting Tests' },
            { name: 'sale', description: 'Sales Tests' },
            { name: 'purchase', description: 'Purchase Tests' },
            { name: 'stock', description: 'Inventory Tests' }
        ];
    }
}

// 视图浏览器
class ViewExplorer {
    constructor(env) {
        this.env = env;
        this.viewCache = new Map();
        this.viewTypes = ['form', 'list', 'kanban', 'calendar', 'graph', 'pivot'];
    }
    
    async openExplorer() {
        await this.env.services.dialog.add(ViewExplorerDialog, {
            title: 'View Explorer',
            viewExplorer: this,
            onViewSelected: (view) => this.openView(view)
        });
    }
    
    async openView(viewId) {
        try {
            const view = await this.getViewDetails(viewId);
            
            await this.env.services.action.doAction({
                type: "ir.actions.act_window",
                name: view.name,
                res_model: view.model,
                views: [[view.id, view.type]],
                target: 'new'
            });
        } catch (error) {
            console.error('Failed to open view:', error);
            this.env.services.notification.add(
                'Failed to open view: ' + error.message,
                { type: 'danger' }
            );
        }
    }
    
    async getViewDetails(viewId) {
        if (this.viewCache.has(viewId)) {
            return this.viewCache.get(viewId);
        }
        
        const views = await this.env.services.orm.searchRead(
            "ir.ui.view",
            [["id", "=", viewId]],
            ["name", "model", "type", "arch", "inherit_id"],
            { limit: 1 }
        );
        
        if (views.length === 0) {
            throw new Error(`View with ID ${viewId} not found`);
        }
        
        const view = views[0];
        this.viewCache.set(viewId, view);
        return view;
    }
    
    async searchViews(query, filters = {}) {
        const domain = [];
        
        // 基础过滤
        domain.push(["type", "!=", "qweb"]);
        domain.push(["type", "!=", "search"]);
        
        // 搜索查询
        if (query) {
            domain.push([
                "|", "|",
                ["name", "ilike", query],
                ["model", "ilike", query],
                ["arch", "ilike", query]
            ]);
        }
        
        // 类型过滤
        if (filters.type) {
            domain.push(["type", "=", filters.type]);
        }
        
        // 模型过滤
        if (filters.model) {
            domain.push(["model", "=", filters.model]);
        }
        
        return await this.env.services.orm.searchRead(
            "ir.ui.view",
            domain,
            ["name", "model", "type", "inherit_id"],
            { limit: 100, order: "name" }
        );
    }
    
    async getViewsByModel(modelName) {
        return await this.searchViews(null, { model: modelName });
    }
    
    async getViewsByType(viewType) {
        return await this.searchViews(null, { type: viewType });
    }
    
    async getViewHierarchy(viewId) {
        const view = await this.getViewDetails(viewId);
        const hierarchy = [view];
        
        // 获取父视图
        if (view.inherit_id) {
            const parentHierarchy = await this.getViewHierarchy(view.inherit_id[0]);
            hierarchy.unshift(...parentHierarchy);
        }
        
        // 获取子视图
        const childViews = await this.env.services.orm.searchRead(
            "ir.ui.view",
            [["inherit_id", "=", viewId]],
            ["name", "model", "type", "inherit_id"]
        );
        
        for (const childView of childViews) {
            const childHierarchy = await this.getViewHierarchy(childView.id);
            hierarchy.push(...childHierarchy);
        }
        
        return hierarchy;
    }
}

// 使用示例
const debugTools = new DevelopmentDebugTools(env);

// 运行所有测试
await debugTools.testRunner.runTests();

// 运行特定模块测试
await debugTools.testRunner.runModuleTests('web');

// 运行带覆盖率的测试
await debugTools.testRunner.runWithCoverage();

// 打开视图浏览器
await debugTools.viewExplorer.openExplorer();

// 搜索视图
const views = await debugTools.viewExplorer.searchViews('partner');
console.log('Found views:', views);
```

### 2. 扩展调试项目

```javascript
// 扩展调试项目集合
class ExtendedDebugItems {
    constructor(env) {
        this.env = env;
        this.registerExtendedItems();
    }
    
    registerExtendedItems() {
        // 模型浏览器
        this.registerModelBrowser();
        
        // 字段检查器
        this.registerFieldInspector();
        
        // 权限检查器
        this.registerPermissionChecker();
        
        // 工作流查看器
        this.registerWorkflowViewer();
        
        // 数据导入导出工具
        this.registerDataTools();
    }
    
    registerModelBrowser() {
        registry.category("debug").category("default").add("modelBrowser", {
            type: "item",
            description: "Browse Models",
            callback: () => this.openModelBrowser(),
            sequence: 550,
            section: "tools",
            icon: "fa-sitemap"
        });
    }
    
    registerFieldInspector() {
        registry.category("debug").category("default").add("fieldInspector", {
            type: "item",
            description: "Inspect Fields",
            callback: () => this.openFieldInspector(),
            sequence: 560,
            section: "tools",
            icon: "fa-search"
        });
    }
    
    registerPermissionChecker() {
        registry.category("debug").category("default").add("permissionChecker", {
            type: "item",
            description: "Check Permissions",
            callback: () => this.openPermissionChecker(),
            sequence: 570,
            section: "security",
            icon: "fa-shield"
        });
    }
    
    registerWorkflowViewer() {
        registry.category("debug").category("default").add("workflowViewer", {
            type: "item",
            description: "View Workflows",
            callback: () => this.openWorkflowViewer(),
            sequence: 580,
            section: "tools",
            icon: "fa-share-alt"
        });
    }
    
    registerDataTools() {
        registry.category("debug").category("default").add("dataExporter", {
            type: "item",
            description: "Export Data",
            callback: () => this.openDataExporter(),
            sequence: 590,
            section: "data",
            icon: "fa-download"
        });
        
        registry.category("debug").category("default").add("dataImporter", {
            type: "item",
            description: "Import Data",
            callback: () => this.openDataImporter(),
            sequence: 600,
            section: "data",
            icon: "fa-upload"
        });
    }
    
    async openModelBrowser() {
        await this.env.services.action.doAction({
            type: 'ir.actions.act_window',
            res_model: 'ir.model',
            view_mode: 'list,form',
            target: 'new',
            name: 'Model Browser'
        });
    }
    
    async openFieldInspector() {
        await this.env.services.dialog.add(FieldInspectorDialog, {
            title: 'Field Inspector',
            env: this.env
        });
    }
    
    async openPermissionChecker() {
        await this.env.services.dialog.add(PermissionCheckerDialog, {
            title: 'Permission Checker',
            env: this.env
        });
    }
    
    async openWorkflowViewer() {
        await this.env.services.action.doAction({
            type: 'ir.actions.client',
            tag: 'workflow_viewer',
            target: 'new'
        });
    }
    
    async openDataExporter() {
        await this.env.services.dialog.add(DataExporterDialog, {
            title: 'Data Exporter',
            env: this.env
        });
    }
    
    async openDataImporter() {
        await this.env.services.dialog.add(DataImporterDialog, {
            title: 'Data Importer',
            env: this.env
        });
    }
}

// 调试项目管理器
class DebugItemManager {
    constructor(env) {
        this.env = env;
        this.items = new Map();
        this.sections = new Map();
        this.setupDefaultSections();
    }
    
    setupDefaultSections() {
        this.addSection('testing', {
            name: 'Testing',
            icon: 'fa-bug',
            order: 1
        });
        
        this.addSection('tools', {
            name: 'Development Tools',
            icon: 'fa-wrench',
            order: 2
        });
        
        this.addSection('data', {
            name: 'Data Management',
            icon: 'fa-database',
            order: 3
        });
        
        this.addSection('security', {
            name: 'Security',
            icon: 'fa-shield',
            order: 4
        });
        
        this.addSection('performance', {
            name: 'Performance',
            icon: 'fa-tachometer',
            order: 5
        });
        
        this.addSection('monitoring', {
            name: 'Monitoring',
            icon: 'fa-line-chart',
            order: 6
        });
    }
    
    addSection(key, section) {
        this.sections.set(key, section);
    }
    
    addItem(key, item) {
        this.items.set(key, {
            ...item,
            key: key,
            section: item.section || 'tools'
        });
    }
    
    removeItem(key) {
        this.items.delete(key);
    }
    
    getItems(sectionKey = null) {
        let items = Array.from(this.items.values());
        
        if (sectionKey) {
            items = items.filter(item => item.section === sectionKey);
        }
        
        return items.sort((a, b) => (a.sequence || 0) - (b.sequence || 0));
    }
    
    getSections() {
        return Array.from(this.sections.values())
            .sort((a, b) => a.order - b.order);
    }
    
    getItemsBySection() {
        const result = {};
        
        for (const section of this.getSections()) {
            result[section.name] = this.getItems(section.key);
        }
        
        return result;
    }
    
    executeItem(key) {
        const item = this.items.get(key);
        if (item && item.callback) {
            try {
                return item.callback();
            } catch (error) {
                console.error(`Debug item ${key} failed:`, error);
                this.env.services.notification.add(
                    `Debug action failed: ${error.message}`,
                    { type: 'danger' }
                );
            }
        }
    }
    
    isItemAvailable(key, context = {}) {
        const item = this.items.get(key);
        if (!item) return false;
        
        if (item.condition) {
            return item.condition(context);
        }
        
        return true;
    }
    
    getAvailableItems(context = {}) {
        return this.getItems().filter(item => 
            this.isItemAvailable(item.key, context)
        );
    }
}

// 使用示例
const debugItemManager = new DebugItemManager(env);
const extendedDebugItems = new ExtendedDebugItems(env);

// 获取所有调试项目
const allItems = debugItemManager.getItems();

// 按分组获取项目
const itemsBySection = debugItemManager.getItemsBySection();

// 执行调试项目
debugItemManager.executeItem('runUnitTestsItem');
```

## 技术特点

### 1. 模块化设计
- **功能分离**: 每个调试功能独立实现
- **注册机制**: 通过注册表管理调试项目
- **分组管理**: 按功能分组组织调试项目
- **序列控制**: 通过序列号控制显示顺序

### 2. 交互式操作
- **对话框集成**: 使用对话框进行交互式操作
- **选择界面**: 提供友好的选择界面
- **即时反馈**: 操作后提供即时反馈
- **错误处理**: 完善的错误处理机制

### 3. 开发友好
- **便捷访问**: 提供便捷的开发工具访问
- **调试支持**: 支持各种调试模式
- **测试集成**: 集成单元测试功能
- **视图管理**: 提供视图管理工具

### 4. 扩展性
- **插件架构**: 支持插件式的功能扩展
- **注册表**: 基于注册表的管理机制
- **回调机制**: 灵活的回调机制
- **条件显示**: 支持条件显示逻辑

## 设计模式

### 1. 注册表模式 (Registry Pattern)
- **功能注册**: 将调试功能注册到注册表
- **动态加载**: 动态加载和管理调试项目
- **插件支持**: 支持插件式的功能扩展

### 2. 命令模式 (Command Pattern)
- **操作封装**: 将调试操作封装为命令
- **回调执行**: 通过回调执行具体操作
- **参数传递**: 灵活的参数传递机制

### 3. 工厂模式 (Factory Pattern)
- **项目创建**: 创建不同类型的调试项目
- **对话框创建**: 创建相应的对话框
- **工具创建**: 创建调试工具实例

## 注意事项

1. **权限控制**: 确保只有开发者能访问调试功能
2. **性能影响**: 避免调试功能影响生产环境性能
3. **数据安全**: 保护敏感数据不被泄露
4. **错误处理**: 完善的调试功能错误处理

## 扩展建议

1. **更多测试工具**: 添加更多的测试和调试工具
2. **性能分析**: 集成性能分析功能
3. **日志管理**: 添加日志查看和管理功能
4. **数据库工具**: 集成数据库查询和管理工具
5. **API测试**: 添加API测试工具

该调试项目模块为Odoo Web客户端提供了实用的开发调试工具，通过模块化的设计和注册表机制确保了调试功能的可扩展性和易用性。

# ProfilingService - 性能分析服务

## 概述

`profiling_service.js` 是 Odoo Web 客户端的性能分析服务，提供了完整的性能分析功能管理。该模块包含108行代码，是一个核心服务，专门用于管理性能分析的启用/禁用、收集器配置、参数设置等功能，具备状态管理、动态注册、事件通知等特性，是Odoo Web性能调试系统的核心服务。

## 文件信息
- **路径**: `/web/static/src/webclient/debug/profiling/profiling_service.js`
- **行数**: 108
- **模块**: `@web/webclient/debug/profiling/profiling_service`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                                            // 注册表系统
'@web/webclient/debug/profiling/profiling_item'                // 性能分析项目组件
'@web/session'                                                  // 会话管理
'@web/webclient/debug/profiling/profiling_systray_item'        // 性能分析系统托盘项目
'@odoo/owl'                                                     // OWL框架
```

## 服务定义

### 1. 服务配置

```javascript
const profilingService = {
    dependencies: ["orm"],
    start(env, { orm }) {
        // Only set up profiling when in debug mode
        if (!env.debug) {
            return;
        }
        // ... service implementation
    },
};
```

**服务特性**:
- **调试模式**: 仅在调试模式下启用
- **ORM依赖**: 依赖ORM服务进行数据操作
- **条件启动**: 根据环境条件决定是否启动
- **动态配置**: 支持动态配置和状态管理

### 2. 状态管理

```javascript
const state = reactive(
    {
        session: session.profile_session || false,
        collectors: session.profile_collectors || ["sql", "traces_async"],
        params: session.profile_params || {},
        get isEnabled() {
            return Boolean(state.session);
        },
    },
    notify
);
```

**状态属性**:
- **session**: 性能分析会话状态
- **collectors**: 启用的收集器列表
- **params**: 性能分析参数
- **isEnabled**: 计算属性，表示是否启用分析

## 核心功能

### 1. 性能分析控制

```javascript
async function setProfiling(params) {
    const kwargs = Object.assign(
        {
            collectors: state.collectors,
            params: state.params,
            profile: state.isEnabled,
        },
        params
    );
    const resp = await orm.call("ir.profile", "set_profiling", [], kwargs);
    if (resp.type) {
        // most likely an "ir.actions.act_window"
        env.services.action.doAction(resp);
    } else {
        state.session = resp.session;
        state.collectors = resp.collectors;
        state.params = resp.params;
    }
}
```

**分析控制功能**:
- **参数合并**: 合并当前状态和新参数
- **服务器通信**: 与服务器同步分析配置
- **状态更新**: 根据服务器响应更新本地状态
- **动作处理**: 处理服务器返回的动作

### 2. 系统托盘管理

```javascript
function notify() {
    if (systrayRegistry.contains("web.profiling") && state.isEnabled === false) {
        systrayRegistry.remove("web.profiling");
    }
    if (!systrayRegistry.contains("web.profiling") && state.isEnabled === true) {
        systrayRegistry.add("web.profiling", profilingSystrayItem, { sequence: 99 });
    }
    bus.trigger("UPDATE");
}
```

**托盘管理功能**:
- **动态注册**: 根据状态动态注册/移除托盘项目
- **状态同步**: 与分析状态保持同步
- **事件通知**: 触发更新事件
- **序列控制**: 控制托盘项目的显示顺序

### 3. 公共API

```javascript
return {
    state,
    async toggleProfiling() {
        await setProfiling({ profile: !state.isEnabled });
    },
    async toggleCollector(collector) {
        const nextCollectors = state.collectors.slice();
        const index = nextCollectors.indexOf(collector);
        if (index >= 0) {
            nextCollectors.splice(index, 1);
        } else {
            nextCollectors.push(collector);
        }
        await setProfiling({ collectors: nextCollectors });
    },
    async setParam(key, value) {
        const nextParams = Object.assign({}, state.params);
        nextParams[key] = value;
        await setProfiling({ params: nextParams });
    },
    isCollectorEnabled(collector) {
        return state.collectors.includes(collector);
    },
};
```

**API功能**:
- **toggleProfiling**: 切换性能分析的启用状态
- **toggleCollector**: 切换特定收集器的启用状态
- **setParam**: 设置性能分析参数
- **isCollectorEnabled**: 检查收集器是否启用

## 使用场景

### 1. 性能分析管理器

```javascript
// 性能分析管理器
class PerformanceProfilingManager {
    constructor(env) {
        this.env = env;
        this.profilingService = env.services.profiling;
        this.analysisHistory = [];
        this.activeCollectors = new Set();
        this.setupManager();
    }
    
    setupManager() {
        // 监听性能分析状态变化
        this.profilingService.state.bus?.addEventListener('UPDATE', () => {
            this.onProfilingStateChanged();
        });
        
        // 设置默认收集器
        this.defaultCollectors = [
            'sql',           // SQL查询收集器
            'traces_async',  // 异步跟踪收集器
            'qweb',          // QWeb模板收集器
            'memory',        // 内存使用收集器
            'cache'          // 缓存收集器
        ];
        
        // 设置默认参数
        this.defaultParams = {
            execution_context_qweb: false,
            sampling_rate: 1,
            max_duration: 300,
            include_stack_trace: false
        };
    }
    
    // 启动性能分析
    async startProfiling(options = {}) {
        try {
            const config = {
                collectors: options.collectors || this.defaultCollectors,
                params: { ...this.defaultParams, ...options.params },
                duration: options.duration || 60 // 默认60秒
            };
            
            // 设置收集器
            for (const collector of config.collectors) {
                if (!this.profilingService.isCollectorEnabled(collector)) {
                    await this.profilingService.toggleCollector(collector);
                }
            }
            
            // 设置参数
            for (const [key, value] of Object.entries(config.params)) {
                await this.profilingService.setParam(key, value);
            }
            
            // 启用性能分析
            if (!this.profilingService.state.isEnabled) {
                await this.profilingService.toggleProfiling();
            }
            
            // 记录分析会话
            const session = {
                id: this.generateSessionId(),
                startTime: Date.now(),
                config: config,
                status: 'active'
            };
            
            this.analysisHistory.push(session);
            
            // 设置自动停止
            if (config.duration > 0) {
                setTimeout(() => {
                    this.stopProfiling(session.id);
                }, config.duration * 1000);
            }
            
            console.log('Performance profiling started:', session);
            return session;
            
        } catch (error) {
            console.error('Failed to start profiling:', error);
            throw error;
        }
    }
    
    // 停止性能分析
    async stopProfiling(sessionId = null) {
        try {
            if (this.profilingService.state.isEnabled) {
                await this.profilingService.toggleProfiling();
            }
            
            // 更新会话状态
            if (sessionId) {
                const session = this.analysisHistory.find(s => s.id === sessionId);
                if (session) {
                    session.status = 'completed';
                    session.endTime = Date.now();
                    session.duration = session.endTime - session.startTime;
                }
            }
            
            console.log('Performance profiling stopped');
            
        } catch (error) {
            console.error('Failed to stop profiling:', error);
            throw error;
        }
    }
    
    // 配置收集器
    async configureCollectors(collectors) {
        try {
            const currentCollectors = this.profilingService.state.collectors;
            
            // 禁用不需要的收集器
            for (const collector of currentCollectors) {
                if (!collectors.includes(collector)) {
                    await this.profilingService.toggleCollector(collector);
                }
            }
            
            // 启用需要的收集器
            for (const collector of collectors) {
                if (!this.profilingService.isCollectorEnabled(collector)) {
                    await this.profilingService.toggleCollector(collector);
                }
            }
            
            this.activeCollectors = new Set(collectors);
            console.log('Collectors configured:', collectors);
            
        } catch (error) {
            console.error('Failed to configure collectors:', error);
            throw error;
        }
    }
    
    // 设置分析参数
    async setProfilingParams(params) {
        try {
            for (const [key, value] of Object.entries(params)) {
                await this.profilingService.setParam(key, value);
            }
            
            console.log('Profiling parameters set:', params);
            
        } catch (error) {
            console.error('Failed to set profiling parameters:', error);
            throw error;
        }
    }
    
    // 获取分析状态
    getProfilingStatus() {
        return {
            isEnabled: this.profilingService.state.isEnabled,
            session: this.profilingService.state.session,
            collectors: this.profilingService.state.collectors,
            params: this.profilingService.state.params,
            activeSession: this.getActiveSession(),
            historyCount: this.analysisHistory.length
        };
    }
    
    // 获取活动会话
    getActiveSession() {
        return this.analysisHistory.find(session => session.status === 'active');
    }
    
    // 获取分析历史
    getAnalysisHistory() {
        return this.analysisHistory.map(session => ({
            id: session.id,
            startTime: new Date(session.startTime).toISOString(),
            endTime: session.endTime ? new Date(session.endTime).toISOString() : null,
            duration: session.duration || (Date.now() - session.startTime),
            status: session.status,
            collectors: session.config.collectors,
            params: session.config.params
        }));
    }
    
    // 清理分析历史
    clearAnalysisHistory() {
        this.analysisHistory = this.analysisHistory.filter(session => 
            session.status === 'active'
        );
        console.log('Analysis history cleared');
    }
    
    // 导出分析配置
    exportConfiguration() {
        return {
            collectors: Array.from(this.activeCollectors),
            params: this.profilingService.state.params,
            defaultCollectors: this.defaultCollectors,
            defaultParams: this.defaultParams,
            exportedAt: new Date().toISOString()
        };
    }
    
    // 导入分析配置
    async importConfiguration(config) {
        try {
            if (config.collectors) {
                await this.configureCollectors(config.collectors);
            }
            
            if (config.params) {
                await this.setProfilingParams(config.params);
            }
            
            console.log('Configuration imported successfully');
            
        } catch (error) {
            console.error('Failed to import configuration:', error);
            throw error;
        }
    }
    
    // 性能分析状态变化处理
    onProfilingStateChanged() {
        const status = this.getProfilingStatus();
        console.log('Profiling state changed:', status);
        
        // 触发自定义事件
        this.env.bus.trigger('PROFILING:STATE_CHANGED', status);
    }
    
    // 生成会话ID
    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    // 获取收集器信息
    getCollectorInfo() {
        return {
            available: [
                { name: 'sql', description: 'SQL Query Collector', enabled: this.profilingService.isCollectorEnabled('sql') },
                { name: 'traces_async', description: 'Async Traces Collector', enabled: this.profilingService.isCollectorEnabled('traces_async') },
                { name: 'qweb', description: 'QWeb Template Collector', enabled: this.profilingService.isCollectorEnabled('qweb') },
                { name: 'memory', description: 'Memory Usage Collector', enabled: this.profilingService.isCollectorEnabled('memory') },
                { name: 'cache', description: 'Cache Performance Collector', enabled: this.profilingService.isCollectorEnabled('cache') }
            ],
            active: this.profilingService.state.collectors
        };
    }
    
    // 获取参数信息
    getParameterInfo() {
        return {
            current: this.profilingService.state.params,
            available: {
                execution_context_qweb: {
                    type: 'boolean',
                    description: 'Include QWeb execution context',
                    default: false
                },
                sampling_rate: {
                    type: 'number',
                    description: 'Sampling rate (1 = every request)',
                    default: 1,
                    min: 1,
                    max: 1000
                },
                max_duration: {
                    type: 'number',
                    description: 'Maximum profiling duration (seconds)',
                    default: 300,
                    min: 10,
                    max: 3600
                },
                include_stack_trace: {
                    type: 'boolean',
                    description: 'Include stack traces in profiling data',
                    default: false
                }
            }
        };
    }
}

// 使用示例
const profilingManager = new PerformanceProfilingManager(env);

// 启动性能分析
const session = await profilingManager.startProfiling({
    collectors: ['sql', 'qweb', 'memory'],
    params: {
        execution_context_qweb: true,
        sampling_rate: 1
    },
    duration: 120 // 2分钟
});

// 获取分析状态
const status = profilingManager.getProfilingStatus();
console.log('Profiling status:', status);

// 配置收集器
await profilingManager.configureCollectors(['sql', 'traces_async']);

// 设置参数
await profilingManager.setProfilingParams({
    sampling_rate: 10,
    include_stack_trace: true
});
```

### 2. 性能分析配置管理器

```javascript
// 性能分析配置管理器
class ProfilingConfigurationManager {
    constructor(profilingService) {
        this.profilingService = profilingService;
        this.presets = new Map();
        this.setupDefaultPresets();
    }
    
    setupDefaultPresets() {
        // 开发环境预设
        this.addPreset('development', {
            name: 'Development',
            description: 'Full profiling for development environment',
            collectors: ['sql', 'traces_async', 'qweb', 'memory', 'cache'],
            params: {
                execution_context_qweb: true,
                sampling_rate: 1,
                include_stack_trace: true,
                max_duration: 600
            }
        });
        
        // 生产环境预设
        this.addPreset('production', {
            name: 'Production',
            description: 'Lightweight profiling for production',
            collectors: ['sql', 'traces_async'],
            params: {
                execution_context_qweb: false,
                sampling_rate: 100,
                include_stack_trace: false,
                max_duration: 300
            }
        });
        
        // 调试预设
        this.addPreset('debug', {
            name: 'Debug',
            description: 'Detailed profiling for debugging',
            collectors: ['sql', 'traces_async', 'qweb', 'memory'],
            params: {
                execution_context_qweb: true,
                sampling_rate: 1,
                include_stack_trace: true,
                detailed_timing: true,
                max_duration: 1800
            }
        });
        
        // 性能测试预设
        this.addPreset('performance_test', {
            name: 'Performance Test',
            description: 'Optimized for performance testing',
            collectors: ['sql', 'memory', 'cache'],
            params: {
                execution_context_qweb: false,
                sampling_rate: 1,
                include_stack_trace: false,
                max_duration: 300
            }
        });
    }
    
    addPreset(key, preset) {
        this.presets.set(key, preset);
    }
    
    getPreset(key) {
        return this.presets.get(key);
    }
    
    getAllPresets() {
        return Array.from(this.presets.entries()).map(([key, preset]) => ({
            key,
            ...preset
        }));
    }
    
    async applyPreset(key) {
        const preset = this.getPreset(key);
        if (!preset) {
            throw new Error(`Preset '${key}' not found`);
        }
        
        try {
            // 配置收集器
            const currentCollectors = this.profilingService.state.collectors;
            
            // 禁用当前收集器
            for (const collector of currentCollectors) {
                if (!preset.collectors.includes(collector)) {
                    await this.profilingService.toggleCollector(collector);
                }
            }
            
            // 启用预设收集器
            for (const collector of preset.collectors) {
                if (!this.profilingService.isCollectorEnabled(collector)) {
                    await this.profilingService.toggleCollector(collector);
                }
            }
            
            // 设置参数
            for (const [key, value] of Object.entries(preset.params)) {
                await this.profilingService.setParam(key, value);
            }
            
            console.log(`Applied profiling preset: ${preset.name}`);
            return true;
            
        } catch (error) {
            console.error(`Failed to apply preset '${key}':`, error);
            throw error;
        }
    }
    
    getCurrentConfiguration() {
        return {
            collectors: this.profilingService.state.collectors,
            params: this.profilingService.state.params,
            isEnabled: this.profilingService.state.isEnabled
        };
    }
    
    saveCurrentAsPreset(key, name, description) {
        const currentConfig = this.getCurrentConfiguration();
        this.addPreset(key, {
            name,
            description,
            collectors: currentConfig.collectors,
            params: currentConfig.params
        });
    }
    
    exportPresets() {
        const presets = {};
        for (const [key, preset] of this.presets) {
            presets[key] = preset;
        }
        return JSON.stringify(presets, null, 2);
    }
    
    importPresets(presetsData) {
        try {
            const presets = JSON.parse(presetsData);
            for (const [key, preset] of Object.entries(presets)) {
                this.addPreset(key, preset);
            }
            return true;
        } catch (error) {
            throw new Error('Invalid presets data');
        }
    }
}

// 使用示例
const configManager = new ProfilingConfigurationManager(profilingService);

// 应用开发环境预设
await configManager.applyPreset('development');

// 保存当前配置为新预设
configManager.saveCurrentAsPreset('custom', 'Custom Configuration', 'My custom profiling setup');

// 获取所有预设
const allPresets = configManager.getAllPresets();
console.log('Available presets:', allPresets);
```

## 技术特点

### 1. 响应式状态管理
- **reactive状态**: 使用OWL的reactive创建响应式状态
- **计算属性**: 支持计算属性的自动更新
- **状态同步**: 与服务器状态保持同步
- **事件通知**: 状态变化时自动通知相关组件

### 2. 动态注册机制
- **条件注册**: 根据状态动态注册/移除组件
- **系统托盘**: 动态管理系统托盘项目
- **调试项目**: 注册调试菜单项目
- **序列控制**: 控制组件的显示顺序

### 3. 服务器集成
- **ORM调用**: 通过ORM与服务器通信
- **状态同步**: 与服务器的分析状态同步
- **参数传递**: 完整的参数传递机制
- **错误处理**: 完善的错误处理机制

### 4. 模块化设计
- **服务架构**: 标准的Odoo服务架构
- **依赖管理**: 清晰的服务依赖关系
- **接口设计**: 简洁的公共API接口
- **扩展性**: 良好的扩展性设计

## 设计模式

### 1. 服务定位器模式 (Service Locator Pattern)
- **服务注册**: 将性能分析服务注册到服务定位器
- **依赖注入**: 注入ORM等依赖服务
- **服务获取**: 通过服务定位器获取服务实例

### 2. 观察者模式 (Observer Pattern)
- **状态监听**: 监听状态变化事件
- **事件通知**: 状态变化时通知相关组件
- **响应式更新**: 自动响应状态变化

### 3. 策略模式 (Strategy Pattern)
- **收集器策略**: 不同收集器的处理策略
- **参数策略**: 不同参数的处理策略
- **通知策略**: 不同的通知处理策略

## 注意事项

1. **调试模式**: 仅在调试模式下启用服务
2. **性能影响**: 避免性能分析本身影响系统性能
3. **状态一致性**: 确保客户端和服务器状态的一致性
4. **错误处理**: 完善的错误处理和恢复机制

## 扩展建议

1. **预设配置**: 添加预设的分析配置
2. **历史记录**: 保存分析历史记录
3. **报告生成**: 自动生成分析报告
4. **实时监控**: 提供实时的性能监控
5. **告警机制**: 添加性能告警机制

该性能分析服务为Odoo Web客户端提供了完整的性能分析功能管理，通过响应式状态管理和动态注册机制确保了性能调试功能的灵活性和易用性。

# Profiling - 性能分析系统

## 概述

Profiling 是 Odoo Web 客户端的性能分析系统，提供了完整的性能监控、分析和可视化功能。该系统包含4个核心模块，总计约521行代码，专门用于调试模式下的性能分析，具备SQL监控、QWeb模板分析、执行跟踪、可视化展示等特性，是Odoo Web客户端中性能优化和调试的重要工具。

## 目录结构

```
profiling/
├── profiling_item.js                          # 性能分析菜单项 (39行)
├── profiling_item.md                          # 学习资料
├── profiling_qweb.js                          # QWeb性能分析视图 (342行)
├── profiling_qweb.md                          # 学习资料
├── profiling_service.js                       # 性能分析服务 (108行)
├── profiling_service.md                       # 学习资料
├── profiling_systray_item.js                  # 系统托盘项 (16行)
├── profiling_systray_item.md                  # 学习资料
└── README.md                                   # 本文档
```

## 核心架构

### 1. 性能分析系统层次结构

```
性能分析系统 (Profiling System)
├── 服务层 (Service Layer)
│   ├── ProfilingService (性能分析服务)
│   ├── 状态管理 (State Management)
│   ├── 参数配置 (Parameter Configuration)
│   └── 数据收集 (Data Collection)
├── 界面层 (Interface Layer)
│   ├── ProfilingItem (调试菜单项)
│   ├── ProfilingSystrayItem (系统托盘项)
│   ├── 参数控制 (Parameter Controls)
│   └── 快速访问 (Quick Access)
├── 分析层 (Analysis Layer)
│   ├── ProfilingQwebView (QWeb分析视图)
│   ├── SQL分析 (SQL Analysis)
│   ├── 模板分析 (Template Analysis)
│   └── 执行跟踪 (Execution Tracing)
├── 可视化层 (Visualization Layer)
│   ├── Ace编辑器 (Ace Editor)
│   ├── 语法高亮 (Syntax Highlighting)
│   ├── 数据展示 (Data Display)
│   └── 交互控制 (Interactive Controls)
└── 数据层 (Data Layer)
    ├── 性能数据 (Performance Data)
    ├── 会话信息 (Session Information)
    ├── 收集器配置 (Collector Configuration)
    └── 分析结果 (Analysis Results)
```

**系统特性**:
- **调试专用**: 仅在调试模式下启用的性能分析
- **多维分析**: 支持SQL、QWeb模板等多维度分析
- **实时监控**: 实时的性能数据收集和监控
- **可视化展示**: 丰富的可视化分析界面

## 核心模块

### 1. ProfilingService - 性能分析服务

**功能**: 性能分析的核心服务和状态管理
- **行数**: 108行
- **作用**: 管理性能分析状态、参数配置、数据收集
- **特点**: 调试模式检测、响应式状态、系统托盘集成

**核心功能**:
```javascript
const profilingService = {
    dependencies: ["orm"],
    start(env, { orm }) {
        // 仅在调试模式下设置性能分析
        if (!env.debug) {
            return;
        }

        function notify() {
            if (systrayRegistry.contains("web.profiling") && state.isEnabled === false) {
                systrayRegistry.remove("web.profiling");
            }
            if (!systrayRegistry.contains("web.profiling") && state.isEnabled === true) {
                systrayRegistry.add("web.profiling", profilingSystrayItem, { sequence: 99 });
            }
            bus.trigger("UPDATE");
        }

        const state = reactive(
            {
                session: session.profile_session || false,
                collectors: session.profile_collectors || ["sql", "traces_async"],
                params: session.profile_params || {},
                get isEnabled() {
                    return Boolean(state.session);
                },
            },
            notify
        );

        async function setProfiling(params) {
            const result = await orm.call("ir.profile", "set_profiling", [params]);
            Object.assign(state, result);
        }

        return {
            state,
            setParam: (key, value) => {
                state.params[key] = value;
                setProfiling({ [key]: value });
            },
            enable: () => setProfiling({ session: true }),
            disable: () => setProfiling({ session: false }),
        };
    }
};
```

**技术特点**:
- **条件启用**: 仅在调试模式下启用
- **响应式状态**: 使用reactive实现状态响应
- **动态注册**: 动态添加/移除系统托盘项
- **参数管理**: 灵活的参数配置管理

### 2. ProfilingItem - 性能分析菜单项

**功能**: 调试菜单中的性能分析控制项
- **行数**: 39行
- **作用**: 提供性能分析参数控制和快速访问
- **特点**: 下拉菜单集成、参数切换、快速导航

**核心功能**:
```javascript
class ProfilingItem extends Component {
    static components = { DropdownItem };
    static template = "web.DebugMenu.ProfilingItem";
    static props = {
        bus: { type: EventBus },
    };

    setup() {
        this.profiling = useService("profiling");
        useBus(this.props.bus, "UPDATE", this.render);
    }

    changeParam(param, ev) {
        this.profiling.setParam(param, ev.target.value);
    }

    toggleParam(param) {
        const value = this.profiling.state.params.execution_context_qweb;
        this.profiling.setParam(param, !value);
    }

    openProfiles() {
        if (this.env.services.action) {
            this.env.services.action.doAction("base.action_menu_ir_profile");
        } else {
            window.location = "/web/#action=base.action_menu_ir_profile";
        }
    }
}
```

**技术特点**:
- **事件总线**: 监听更新事件自动重渲染
- **参数控制**: 提供参数修改和切换功能
- **环境适配**: 适配前端和后端不同环境
- **快速访问**: 提供性能分析页面快速访问

### 3. ProfilingQwebView - QWeb性能分析视图

**功能**: QWeb模板性能分析的可视化界面
- **行数**: 342行
- **作用**: 显示和分析QWeb模板的性能数据
- **特点**: Ace编辑器集成、语法高亮、数据处理、交互分析

**核心功能**:
```javascript
class ProfilingQwebView extends Component {
    static template = "web.ProfilingQwebView";
    static components = { MenuItem };
    static props = { ...standardFieldProps };

    setup() {
        this.orm = useService("orm");
        this.ace = useRef("ace");
        this.selector = useRef("selector");

        this.value = processValue(this.props.record.data[this.props.name]);
        this.state = useState({
            viewID: this.profile.data.length ? this.profile.data[0].view_id : 0,
            mode: "qweb",
        });

        onWillStart(async () => {
            await loadBundle("web.ace_editor");
        });

        onMounted(() => {
            this.setupAceEditor();
            this.updateContent();
        });
    }

    setupAceEditor() {
        const aceEditor = window.ace.edit(this.ace.el);
        aceEditor.setTheme("ace/theme/monokai");
        aceEditor.session.setMode("ace/mode/xml");
        aceEditor.setReadOnly(true);
        aceEditor.setShowPrintMargin(false);
        aceEditor.renderer.setShowGutter(true);
        aceEditor.setHighlightActiveLine(false);
        this.aceEditor = aceEditor;
    }

    updateContent() {
        if (this.state.mode === "qweb") {
            this.displayQwebContent();
        } else {
            this.displayPythonContent();
        }
    }
}

function processValue(value) {
    const data = JSON.parse(value);
    for (const line of data[0].results.data) {
        line.xpath = line.xpath.replace(/([^\]])\//g, "$1[1]/").replace(/([^\]])$/g, "$1[1]");
    }
    return data;
}
```

**技术特点**:
- **Ace编辑器**: 集成Ace编辑器提供语法高亮
- **数据处理**: 智能处理和格式化性能数据
- **多模式**: 支持QWeb和Python两种分析模式
- **交互式**: 提供交互式的分析界面

### 4. ProfilingSystrayItem - 系统托盘项

**功能**: 系统托盘中的性能分析快速访问项
- **行数**: 16行
- **作用**: 在系统托盘提供性能分析状态显示
- **特点**: 简洁设计、状态指示、快速访问

**核心功能**:
```javascript
class ProfilingSystrayItem extends Component {
    static template = "web.ProfilingSystrayItem";
    static props = {};
}

const profilingSystrayItem = {
    Component: ProfilingSystrayItem,
};
```

**技术特点**:
- **轻量级**: 极简的组件设计
- **状态显示**: 显示性能分析启用状态
- **快速访问**: 提供快速访问入口
- **动态注册**: 根据状态动态添加/移除

## 使用场景

### 1. 性能分析管理器

```javascript
// 性能分析管理器
class ProfilingManager {
    constructor(env) {
        this.env = env;
        this.setupManager();
    }

    setupManager() {
        // 设置分析配置
        this.profilingConfig = {
            enableSQLProfiling: true,
            enableQWebProfiling: true,
            enableTraceProfiling: true,
            enableMemoryProfiling: false,
            autoSaveResults: true,
            maxProfileSessions: 10
        };

        // 设置收集器
        this.collectors = new Map([
            ['sql', { name: 'SQL Queries', enabled: true }],
            ['traces_async', { name: 'Async Traces', enabled: true }],
            ['qweb', { name: 'QWeb Templates', enabled: false }],
            ['memory', { name: 'Memory Usage', enabled: false }]
        ]);

        // 设置分析会话
        this.sessions = new Map();

        // 设置结果缓存
        this.resultsCache = new Map();

        this.setupEventListeners();
        this.initializeCollectors();
    }

    // 设置事件监听器
    setupEventListeners() {
        // 监听分析启动
        this.env.bus.addEventListener('PROFILING_STARTED', (event) => {
            this.handleProfilingStarted(event.detail);
        });

        // 监听分析停止
        this.env.bus.addEventListener('PROFILING_STOPPED', (event) => {
            this.handleProfilingStopped(event.detail);
        });

        // 监听数据收集
        this.env.bus.addEventListener('PROFILING_DATA_COLLECTED', (event) => {
            this.handleDataCollected(event.detail);
        });
    }

    // 初始化收集器
    initializeCollectors() {
        this.collectors.forEach((collector, key) => {
            if (collector.enabled) {
                this.enableCollector(key);
            }
        });
    }

    // 启用收集器
    enableCollector(collectorName) {
        const collector = this.collectors.get(collectorName);
        if (collector) {
            collector.enabled = true;

            // 通知服务更新收集器配置
            if (this.env.services.profiling) {
                this.env.services.profiling.setParam('collectors',
                    Array.from(this.collectors.entries())
                        .filter(([, c]) => c.enabled)
                        .map(([name]) => name)
                );
            }
        }
    }

    // 禁用收集器
    disableCollector(collectorName) {
        const collector = this.collectors.get(collectorName);
        if (collector) {
            collector.enabled = false;

            // 更新收集器配置
            if (this.env.services.profiling) {
                this.env.services.profiling.setParam('collectors',
                    Array.from(this.collectors.entries())
                        .filter(([, c]) => c.enabled)
                        .map(([name]) => name)
                );
            }
        }
    }

    // 开始性能分析
    async startProfiling(options = {}) {
        const config = {
            sessionName: options.sessionName || `session_${Date.now()}`,
            collectors: options.collectors || Array.from(this.collectors.keys()).filter(k => this.collectors.get(k).enabled),
            duration: options.duration || null,
            autoStop: options.autoStop !== false,
            ...options
        };

        try {
            // 创建分析会话
            const session = this.createProfilingSession(config);

            // 启用性能分析
            if (this.env.services.profiling) {
                await this.env.services.profiling.enable();

                // 设置收集器
                await this.env.services.profiling.setParam('collectors', config.collectors);

                // 设置其他参数
                if (config.params) {
                    for (const [key, value] of Object.entries(config.params)) {
                        await this.env.services.profiling.setParam(key, value);
                    }
                }
            }

            // 记录会话
            this.sessions.set(session.id, session);

            // 设置自动停止
            if (config.duration && config.autoStop) {
                setTimeout(() => {
                    this.stopProfiling(session.id);
                }, config.duration);
            }

            // 触发启动事件
            this.env.bus.trigger('PROFILING_STARTED', {
                session: session,
                config: config,
                timestamp: Date.now()
            });

            return session;

        } catch (error) {
            this.handleProfilingError(error, 'start', config);
            throw error;
        }
    }

    // 停止性能分析
    async stopProfiling(sessionId) {
        const session = this.sessions.get(sessionId);
        if (!session) {
            throw new Error(`Profiling session ${sessionId} not found`);
        }

        try {
            // 停止性能分析
            if (this.env.services.profiling) {
                await this.env.services.profiling.disable();
            }

            // 收集结果
            const results = await this.collectProfilingResults(session);

            // 更新会话状态
            session.status = 'completed';
            session.endTime = Date.now();
            session.duration = session.endTime - session.startTime;
            session.results = results;

            // 缓存结果
            this.resultsCache.set(sessionId, results);

            // 自动保存
            if (this.profilingConfig.autoSaveResults) {
                await this.saveProfilingResults(session);
            }

            // 触发停止事件
            this.env.bus.trigger('PROFILING_STOPPED', {
                session: session,
                results: results,
                timestamp: Date.now()
            });

            return results;

        } catch (error) {
            session.status = 'error';
            session.error = error.message;
            this.handleProfilingError(error, 'stop', session);
            throw error;
        }
    }

    // 创建分析会话
    createProfilingSession(config) {
        const session = {
            id: this.generateSessionId(),
            name: config.sessionName,
            startTime: Date.now(),
            endTime: null,
            duration: null,
            status: 'running',
            collectors: config.collectors,
            config: config,
            results: null,
            error: null
        };

        return session;
    }

    // 收集分析结果
    async collectProfilingResults(session) {
        const results = {
            sessionId: session.id,
            collectors: {},
            summary: {},
            metadata: {
                startTime: session.startTime,
                endTime: Date.now(),
                duration: Date.now() - session.startTime
            }
        };

        // 收集各收集器的结果
        for (const collectorName of session.collectors) {
            try {
                const collectorResults = await this.collectCollectorResults(collectorName, session);
                results.collectors[collectorName] = collectorResults;
            } catch (error) {
                console.warn(`Failed to collect results for ${collectorName}:`, error);
                results.collectors[collectorName] = { error: error.message };
            }
        }

        // 生成摘要
        results.summary = this.generateResultsSummary(results);

        return results;
    }

    // 收集特定收集器结果
    async collectCollectorResults(collectorName, session) {
        switch (collectorName) {
            case 'sql':
                return await this.collectSQLResults(session);
            case 'traces_async':
                return await this.collectTraceResults(session);
            case 'qweb':
                return await this.collectQWebResults(session);
            case 'memory':
                return await this.collectMemoryResults(session);
            default:
                throw new Error(`Unknown collector: ${collectorName}`);
        }
    }

    // 收集SQL结果
    async collectSQLResults(session) {
        try {
            const result = await this.env.services.orm.call('ir.profile', 'get_sql_results', [session.id]);
            return {
                queries: result.queries || [],
                totalQueries: result.total_queries || 0,
                totalTime: result.total_time || 0,
                slowQueries: result.slow_queries || [],
                duplicateQueries: result.duplicate_queries || []
            };
        } catch (error) {
            return { error: error.message };
        }
    }

    // 收集跟踪结果
    async collectTraceResults(session) {
        try {
            const result = await this.env.services.orm.call('ir.profile', 'get_trace_results', [session.id]);
            return {
                traces: result.traces || [],
                totalTraces: result.total_traces || 0,
                asyncOperations: result.async_operations || [],
                callStack: result.call_stack || []
            };
        } catch (error) {
            return { error: error.message };
        }
    }

    // 收集QWeb结果
    async collectQWebResults(session) {
        try {
            const result = await this.env.services.orm.call('ir.profile', 'get_qweb_results', [session.id]);
            return {
                templates: result.templates || [],
                renderTime: result.render_time || 0,
                templateStats: result.template_stats || {},
                slowTemplates: result.slow_templates || []
            };
        } catch (error) {
            return { error: error.message };
        }
    }

    // 生成结果摘要
    generateResultsSummary(results) {
        const summary = {
            totalDuration: results.metadata.duration,
            collectorsUsed: Object.keys(results.collectors).length,
            hasErrors: Object.values(results.collectors).some(c => c.error),
            performance: {}
        };

        // SQL摘要
        if (results.collectors.sql && !results.collectors.sql.error) {
            summary.performance.sql = {
                totalQueries: results.collectors.sql.totalQueries,
                totalTime: results.collectors.sql.totalTime,
                averageTime: results.collectors.sql.totalTime / results.collectors.sql.totalQueries || 0
            };
        }

        // QWeb摘要
        if (results.collectors.qweb && !results.collectors.qweb.error) {
            summary.performance.qweb = {
                templatesRendered: results.collectors.qweb.templates.length,
                totalRenderTime: results.collectors.qweb.renderTime
            };
        }

        return summary;
    }

    // 保存分析结果
    async saveProfilingResults(session) {
        try {
            const data = {
                session_name: session.name,
                start_time: new Date(session.startTime).toISOString(),
                end_time: new Date(session.endTime).toISOString(),
                duration: session.duration,
                collectors: session.collectors,
                results: JSON.stringify(session.results),
                status: session.status
            };

            await this.env.services.orm.create('ir.profile.session', [data]);

        } catch (error) {
            console.warn('Failed to save profiling results:', error);
        }
    }

    // 处理分析启动
    handleProfilingStarted(startDetail) {
        console.log('Profiling started:', startDetail);

        // 更新UI状态
        this.updateUIState('running');

        // 触发启动完成事件
        this.env.bus.trigger('PROFILING_START_COMPLETED', {
            detail: startDetail,
            timestamp: Date.now()
        });
    }

    // 处理分析停止
    handleProfilingStopped(stopDetail) {
        console.log('Profiling stopped:', stopDetail);

        // 更新UI状态
        this.updateUIState('stopped');

        // 触发停止完成事件
        this.env.bus.trigger('PROFILING_STOP_COMPLETED', {
            detail: stopDetail,
            timestamp: Date.now()
        });
    }

    // 处理数据收集
    handleDataCollected(dataDetail) {
        console.log('Profiling data collected:', dataDetail);

        // 触发数据收集完成事件
        this.env.bus.trigger('PROFILING_DATA_COLLECTION_COMPLETED', {
            detail: dataDetail,
            timestamp: Date.now()
        });
    }

    // 处理分析错误
    handleProfilingError(error, operation, context) {
        console.error(`Profiling ${operation} error:`, error);

        // 记录错误
        const errorRecord = {
            error: error.message,
            operation: operation,
            context: context,
            timestamp: Date.now()
        };

        // 触发错误事件
        this.env.bus.trigger('PROFILING_ERROR', {
            error: errorRecord,
            timestamp: Date.now()
        });
    }

    // 更新UI状态
    updateUIState(state) {
        // 更新系统托盘状态
        // 更新调试菜单状态
        // 触发UI更新事件
    }

    // 生成会话ID
    generateSessionId() {
        return `profiling_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // 获取活动会话
    getActiveSessions() {
        return Array.from(this.sessions.values()).filter(s => s.status === 'running');
    }

    // 获取分析统计
    getProfilingStatistics() {
        const allSessions = Array.from(this.sessions.values());

        return {
            totalSessions: allSessions.length,
            activeSessions: allSessions.filter(s => s.status === 'running').length,
            completedSessions: allSessions.filter(s => s.status === 'completed').length,
            errorSessions: allSessions.filter(s => s.status === 'error').length,
            enabledCollectors: Array.from(this.collectors.entries()).filter(([, c]) => c.enabled).map(([name]) => name),
            cacheSize: this.resultsCache.size
        };
    }

    // 清理旧会话
    cleanupOldSessions() {
        const maxSessions = this.profilingConfig.maxProfileSessions;
        const sessions = Array.from(this.sessions.values())
            .sort((a, b) => b.startTime - a.startTime);

        if (sessions.length > maxSessions) {
            const toRemove = sessions.slice(maxSessions);
            toRemove.forEach(session => {
                this.sessions.delete(session.id);
                this.resultsCache.delete(session.id);
            });
        }
    }

    // 导出分析结果
    exportProfilingResults(sessionId, format = 'json') {
        const session = this.sessions.get(sessionId);
        if (!session) {
            throw new Error(`Session ${sessionId} not found`);
        }

        const exportData = {
            session: session,
            results: session.results,
            exportDate: new Date().toISOString(),
            format: format
        };

        let content, mimeType, extension;

        switch (format) {
            case 'json':
                content = JSON.stringify(exportData, null, 2);
                mimeType = 'application/json';
                extension = 'json';
                break;
            case 'csv':
                content = this.convertToCSV(exportData);
                mimeType = 'text/csv';
                extension = 'csv';
                break;
            default:
                throw new Error(`Unsupported export format: ${format}`);
        }

        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `profiling_${session.name}_${new Date().toISOString().split('T')[0]}.${extension}`;
        link.click();

        URL.revokeObjectURL(url);
    }

    // 转换为CSV格式
    convertToCSV(data) {
        // 简化的CSV转换实现
        const lines = [];
        lines.push('Session,Start Time,Duration,Status,Collectors');
        lines.push(`${data.session.name},${new Date(data.session.startTime).toISOString()},${data.session.duration},${data.session.status},"${data.session.collectors.join(', ')}"`);

        return lines.join('\n');
    }

    // 销毁管理器
    destroy() {
        // 停止所有活动会话
        const activeSessions = this.getActiveSessions();
        activeSessions.forEach(session => {
            this.stopProfiling(session.id).catch(console.error);
        });

        // 清理事件监听器
        this.env.bus.removeEventListener('PROFILING_STARTED');
        this.env.bus.removeEventListener('PROFILING_STOPPED');
        this.env.bus.removeEventListener('PROFILING_DATA_COLLECTED');

        // 清理数据
        this.sessions.clear();
        this.resultsCache.clear();
        this.collectors.clear();
    }
}

// 使用示例
const profilingManager = new ProfilingManager(env);

// 开始性能分析
const session = await profilingManager.startProfiling({
    sessionName: 'test_session',
    collectors: ['sql', 'qweb'],
    duration: 60000 // 1分钟
});

// 停止性能分析
const results = await profilingManager.stopProfiling(session.id);

// 导出结果
profilingManager.exportProfilingResults(session.id, 'json');

// 获取统计信息
const stats = profilingManager.getProfilingStatistics();
console.log('Profiling statistics:', stats);
```

## 技术特点

### 1. 调试专用设计
- **条件启用**: 仅在调试模式下启用性能分析
- **开发工具**: 专为开发和调试设计的工具
- **性能监控**: 实时的性能数据收集和监控
- **问题诊断**: 帮助诊断性能问题和瓶颈

### 2. 多维度分析
- **SQL分析**: 详细的SQL查询性能分析
- **QWeb分析**: QWeb模板渲染性能分析
- **异步跟踪**: 异步操作的执行跟踪
- **内存监控**: 内存使用情况监控

### 3. 可视化展示
- **Ace编辑器**: 集成Ace编辑器提供语法高亮
- **交互界面**: 丰富的交互式分析界面
- **数据可视化**: 直观的数据展示和分析
- **实时更新**: 实时的状态更新和数据刷新

### 4. 灵活配置
- **参数控制**: 灵活的分析参数配置
- **收集器管理**: 可选择的数据收集器
- **会话管理**: 完整的分析会话管理
- **结果导出**: 多格式的结果导出功能

## 设计模式

### 1. 服务模式 (Service Pattern)
- **核心服务**: ProfilingService作为核心服务
- **状态管理**: 集中的状态管理和配置
- **服务注册**: 标准的服务注册机制

### 2. 观察者模式 (Observer Pattern)
- **事件监听**: 监听性能分析相关事件
- **状态响应**: 响应式的状态更新
- **自动通知**: 自动通知相关组件更新

### 3. 策略模式 (Strategy Pattern)
- **收集策略**: 不同类型的数据收集策略
- **分析策略**: 不同的性能分析策略
- **展示策略**: 不同的数据展示策略

### 4. 工厂模式 (Factory Pattern)
- **组件创建**: 动态创建分析组件
- **收集器创建**: 根据配置创建收集器
- **会话创建**: 动态创建分析会话

## 注意事项

1. **性能影响**: 性能分析本身会影响系统性能
2. **调试模式**: 仅在调试模式下使用，避免生产环境启用
3. **数据安全**: 注意性能数据的安全性和隐私
4. **资源管理**: 及时清理分析数据和会话

## 扩展建议

1. **更多收集器**: 添加更多类型的性能数据收集器
2. **高级分析**: 增强性能数据的分析和处理能力
3. **报告生成**: 添加自动化的性能报告生成
4. **历史对比**: 支持历史性能数据的对比分析
5. **告警机制**: 添加性能问题的自动告警机制

该性能分析系统为Odoo Web客户端提供了强大的性能监控和分析功能，通过多维度分析、可视化展示和灵活配置确保了高效的性能优化和问题诊断能力。
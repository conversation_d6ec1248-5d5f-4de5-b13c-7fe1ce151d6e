# ProfilingSystrayItem - 性能分析系统托盘项目

## 概述

`profiling_systray_item.js` 是 Odoo Web 客户端的性能分析系统托盘项目组件，提供了系统托盘中的性能分析快捷访问功能。该模块包含16行代码，是一个简洁的OWL组件，专门用于在系统托盘中显示性能分析状态和提供快捷操作，具备状态指示、快捷访问、视觉反馈等特性，是Odoo Web性能调试工具的用户界面组件。

## 文件信息
- **路径**: `/web/static/src/webclient/debug/profiling/profiling_systray_item.js`
- **行数**: 16
- **模块**: `@web/webclient/debug/profiling/profiling_systray_item`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'  // OWL框架
```

## 主组件定义

### 1. ProfilingSystrayItem 组件

```javascript
class ProfilingSystrayItem extends Component {
    static template = "web.ProfilingSystrayItem";
    static props = {};
}
```

**组件特性**:
- **系统托盘**: 显示在系统托盘区域
- **状态指示**: 显示性能分析的当前状态
- **快捷访问**: 提供性能分析功能的快捷访问
- **简洁设计**: 简洁的组件设计，专注于核心功能

### 2. 组件导出

```javascript
const profilingSystrayItem = {
    Component: ProfilingSystrayItem,
};
```

**导出配置**:
- **组件封装**: 将组件封装为系统托盘项目
- **标准格式**: 符合系统托盘项目的标准格式
- **即插即用**: 可直接注册到系统托盘

## 使用场景

### 1. 扩展的性能分析托盘项目

```javascript
// 扩展的性能分析托盘项目
class ExtendedProfilingSystrayItem extends Component {
    static template = xml`
        <div class="profiling-systray-item" 
             t-att-class="{ 'active': isProfilingActive, 'has-data': hasProfilingData }"
             t-on-click="toggleProfilingMenu">
            <div class="profiling-indicator">
                <i t-att-class="indicatorIcon"/>
                <span t-if="isProfilingActive" class="profiling-pulse"/>
            </div>
            
            <div t-if="showMenu" class="profiling-dropdown">
                <div class="profiling-status">
                    <div class="status-header">
                        <span>Performance Profiling</span>
                        <button class="btn-close" t-on-click.stop="closeMenu">×</button>
                    </div>
                    
                    <div class="status-content">
                        <div class="status-item">
                            <span>Status:</span>
                            <span t-att-class="statusClass" t-esc="statusText"/>
                        </div>
                        
                        <div t-if="isProfilingActive" class="status-item">
                            <span>Duration:</span>
                            <span t-esc="profilingDuration"/>
                        </div>
                        
                        <div t-if="isProfilingActive" class="status-item">
                            <span>Collectors:</span>
                            <span t-esc="activeCollectors"/>
                        </div>
                        
                        <div t-if="hasProfilingData" class="status-item">
                            <span>Last Session:</span>
                            <span t-esc="lastSessionInfo"/>
                        </div>
                    </div>
                </div>
                
                <div class="profiling-actions">
                    <button class="btn btn-sm" 
                            t-att-class="isProfilingActive ? 'btn-danger' : 'btn-primary'"
                            t-on-click="toggleProfiling">
                        <i t-att-class="isProfilingActive ? 'fa fa-stop' : 'fa fa-play'"/>
                        <span t-esc="isProfilingActive ? 'Stop' : 'Start'"/>
                    </button>
                    
                    <button class="btn btn-sm btn-secondary" t-on-click="openProfilingSettings">
                        <i class="fa fa-cog"/> Settings
                    </button>
                    
                    <button class="btn btn-sm btn-info" 
                            t-on-click="openProfilingReports"
                            t-att-disabled="!hasProfilingData">
                        <i class="fa fa-chart-line"/> Reports
                    </button>
                </div>
                
                <div t-if="recentSessions.length" class="recent-sessions">
                    <div class="sessions-header">Recent Sessions</div>
                    <div class="sessions-list">
                        <t t-foreach="recentSessions" t-as="session" t-key="session.id">
                            <div class="session-item" t-on-click="() => this.openSession(session)">
                                <div class="session-time" t-esc="formatSessionTime(session.startTime)"/>
                                <div class="session-duration" t-esc="formatDuration(session.duration)"/>
                                <div class="session-status" t-att-class="session.status" t-esc="session.status"/>
                            </div>
                        </t>
                    </div>
                </div>
            </div>
        </div>`;
    
    static props = {};
    
    setup() {
        this.profilingService = useService("profiling");
        this.notification = useService("notification");
        this.action = useService("action");
        
        this.state = useState({
            showMenu: false,
            startTime: null,
            recentSessions: []
        });
        
        // 监听性能分析状态变化
        useBus(this.env.bus, "PROFILING:STATE_CHANGED", this.onProfilingStateChanged.bind(this));
        
        // 定期更新状态
        this.updateInterval = setInterval(() => {
            if (this.isProfilingActive) {
                this.render();
            }
        }, 1000);
        
        onWillUnmount(() => {
            if (this.updateInterval) {
                clearInterval(this.updateInterval);
            }
        });
    }
    
    get isProfilingActive() {
        return this.profilingService.state.isEnabled;
    }
    
    get hasProfilingData() {
        return this.state.recentSessions.length > 0;
    }
    
    get indicatorIcon() {
        if (this.isProfilingActive) {
            return 'fa fa-tachometer text-success';
        } else if (this.hasProfilingData) {
            return 'fa fa-tachometer text-info';
        } else {
            return 'fa fa-tachometer text-muted';
        }
    }
    
    get statusClass() {
        return this.isProfilingActive ? 'text-success' : 'text-muted';
    }
    
    get statusText() {
        return this.isProfilingActive ? 'Active' : 'Inactive';
    }
    
    get profilingDuration() {
        if (!this.isProfilingActive || !this.state.startTime) {
            return '0s';
        }
        const duration = Math.floor((Date.now() - this.state.startTime) / 1000);
        return this.formatDuration(duration * 1000);
    }
    
    get activeCollectors() {
        return this.profilingService.state.collectors.join(', ');
    }
    
    get lastSessionInfo() {
        const lastSession = this.state.recentSessions[0];
        return lastSession ? this.formatSessionTime(lastSession.startTime) : 'None';
    }
    
    get recentSessions() {
        return this.state.recentSessions.slice(0, 5); // 显示最近5个会话
    }
    
    // 切换性能分析菜单
    toggleProfilingMenu() {
        this.state.showMenu = !this.state.showMenu;
    }
    
    // 关闭菜单
    closeMenu() {
        this.state.showMenu = false;
    }
    
    // 切换性能分析
    async toggleProfiling() {
        try {
            if (this.isProfilingActive) {
                await this.stopProfiling();
            } else {
                await this.startProfiling();
            }
        } catch (error) {
            console.error('Failed to toggle profiling:', error);
            this.notification.add('Failed to toggle profiling: ' + error.message, {
                type: 'danger'
            });
        }
    }
    
    // 启动性能分析
    async startProfiling() {
        await this.profilingService.toggleProfiling();
        this.state.startTime = Date.now();
        
        this.notification.add('Performance profiling started', {
            type: 'success'
        });
    }
    
    // 停止性能分析
    async stopProfiling() {
        await this.profilingService.toggleProfiling();
        
        // 记录会话
        if (this.state.startTime) {
            const session = {
                id: this.generateSessionId(),
                startTime: this.state.startTime,
                endTime: Date.now(),
                duration: Date.now() - this.state.startTime,
                status: 'completed',
                collectors: [...this.profilingService.state.collectors],
                params: { ...this.profilingService.state.params }
            };
            
            this.state.recentSessions.unshift(session);
            this.state.recentSessions = this.state.recentSessions.slice(0, 10); // 保留最近10个
            this.state.startTime = null;
        }
        
        this.notification.add('Performance profiling stopped', {
            type: 'info'
        });
    }
    
    // 打开性能分析设置
    async openProfilingSettings() {
        this.closeMenu();
        
        await this.action.doAction({
            type: 'ir.actions.client',
            tag: 'profiling_settings',
            target: 'new',
            name: 'Profiling Settings'
        });
    }
    
    // 打开性能分析报告
    async openProfilingReports() {
        this.closeMenu();
        
        await this.action.doAction("base.action_menu_ir_profile");
    }
    
    // 打开会话详情
    async openSession(session) {
        this.closeMenu();
        
        await this.action.doAction({
            type: 'ir.actions.client',
            tag: 'profiling_session_detail',
            target: 'new',
            name: `Profiling Session - ${this.formatSessionTime(session.startTime)}`,
            context: {
                session_id: session.id,
                session_data: session
            }
        });
    }
    
    // 性能分析状态变化处理
    onProfilingStateChanged(event) {
        const status = event.detail;
        
        if (status.isEnabled && !this.state.startTime) {
            this.state.startTime = Date.now();
        } else if (!status.isEnabled && this.state.startTime) {
            // 自动记录会话（如果是通过其他方式停止的）
            const session = {
                id: this.generateSessionId(),
                startTime: this.state.startTime,
                endTime: Date.now(),
                duration: Date.now() - this.state.startTime,
                status: 'completed',
                collectors: status.collectors || [],
                params: status.params || {}
            };
            
            this.state.recentSessions.unshift(session);
            this.state.recentSessions = this.state.recentSessions.slice(0, 10);
            this.state.startTime = null;
        }
    }
    
    // 格式化会话时间
    formatSessionTime(timestamp) {
        return new Date(timestamp).toLocaleString();
    }
    
    // 格式化持续时间
    formatDuration(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        } else {
            return `${seconds}s`;
        }
    }
    
    // 生成会话ID
    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}

// 性能分析托盘管理器
class ProfilingSystrayManager {
    constructor(env) {
        this.env = env;
        this.systrayRegistry = env.services.registry.category("systray");
        this.isRegistered = false;
        this.setupManager();
    }
    
    setupManager() {
        // 监听性能分析服务状态
        this.env.bus.addEventListener('PROFILING:STATE_CHANGED', (event) => {
            this.updateSystrayVisibility(event.detail);
        });
    }
    
    // 更新系统托盘可见性
    updateSystrayVisibility(profilingStatus) {
        const shouldShow = profilingStatus.isEnabled || this.hasRecentData();
        
        if (shouldShow && !this.isRegistered) {
            this.registerSystrayItem();
        } else if (!shouldShow && this.isRegistered) {
            this.unregisterSystrayItem();
        }
    }
    
    // 注册系统托盘项目
    registerSystrayItem() {
        if (!this.isRegistered) {
            this.systrayRegistry.add("web.profiling", {
                Component: ExtendedProfilingSystrayItem
            }, { sequence: 99 });
            
            this.isRegistered = true;
            console.log('Profiling systray item registered');
        }
    }
    
    // 注销系统托盘项目
    unregisterSystrayItem() {
        if (this.isRegistered) {
            this.systrayRegistry.remove("web.profiling");
            this.isRegistered = false;
            console.log('Profiling systray item unregistered');
        }
    }
    
    // 检查是否有最近的数据
    hasRecentData() {
        // 这里可以检查是否有最近的性能分析数据
        return false;
    }
    
    // 强制显示托盘项目
    forceShow() {
        this.registerSystrayItem();
    }
    
    // 强制隐藏托盘项目
    forceHide() {
        this.unregisterSystrayItem();
    }
    
    // 获取托盘状态
    getSystrayStatus() {
        return {
            isRegistered: this.isRegistered,
            isVisible: this.systrayRegistry.contains("web.profiling")
        };
    }
}

// 使用示例
const systrayManager = new ProfilingSystrayManager(env);

// 强制显示托盘项目
systrayManager.forceShow();

// 获取托盘状态
const status = systrayManager.getSystrayStatus();
console.log('Systray status:', status);
```

## 技术特点

### 1. 简洁设计
- **最小化**: 最小化的代码实现
- **专注功能**: 专注于系统托盘显示功能
- **标准接口**: 符合系统托盘项目的标准接口
- **易于扩展**: 易于扩展和定制

### 2. 系统集成
- **托盘集成**: 完美集成到系统托盘
- **状态同步**: 与性能分析服务状态同步
- **动态显示**: 根据状态动态显示/隐藏
- **用户友好**: 提供用户友好的界面

### 3. 模板驱动
- **模板分离**: 模板与逻辑分离
- **可定制**: 模板可定制和扩展
- **响应式**: 支持响应式更新
- **主题支持**: 支持主题定制

### 4. 轻量级
- **性能优化**: 轻量级的实现
- **内存友好**: 最小的内存占用
- **快速渲染**: 快速的渲染性能
- **资源节约**: 节约系统资源

## 设计模式

### 1. 组件模式 (Component Pattern)
- **组件封装**: 将功能封装为独立组件
- **属性传递**: 通过属性传递配置
- **事件处理**: 处理用户交互事件

### 2. 适配器模式 (Adapter Pattern)
- **接口适配**: 适配系统托盘的接口要求
- **格式转换**: 转换为标准的托盘项目格式
- **兼容性**: 确保与系统的兼容性

### 3. 单例模式 (Singleton Pattern)
- **唯一实例**: 确保托盘项目的唯一性
- **状态管理**: 管理全局的托盘状态
- **资源共享**: 共享托盘资源

## 注意事项

1. **性能考虑**: 避免频繁的状态更新影响性能
2. **用户体验**: 提供清晰的状态指示
3. **资源管理**: 合理管理组件资源
4. **兼容性**: 确保与不同主题的兼容性

## 扩展建议

1. **状态指示**: 添加更丰富的状态指示
2. **快捷操作**: 提供更多的快捷操作
3. **通知集成**: 集成系统通知功能
4. **数据展示**: 显示简要的性能数据
5. **主题定制**: 支持更多的主题定制选项

该性能分析系统托盘项目为Odoo Web客户端提供了便捷的性能分析状态显示和快捷访问功能，通过简洁的设计和标准的接口确保了良好的用户体验和系统集成。

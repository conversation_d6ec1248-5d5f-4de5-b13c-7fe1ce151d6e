# ProfilingQwebView - QWeb性能分析视图

## 概述

`profiling_qweb.js` 是 Odoo Web 客户端的QWeb性能分析视图组件，提供了QWeb模板性能分析的可视化功能。该模块包含342行代码，是一个复杂的OWL组件，专门用于显示和分析QWeb模板的执行性能，具备Ace编辑器集成、XPath解析、性能数据可视化、交互式分析等特性，是Odoo Web性能调试工具的核心组件。

## 文件信息
- **路径**: `/web/static/src/webclient/debug/profiling/profiling_qweb.js`
- **行数**: 342
- **模块**: `@web/webclient/debug/profiling/profiling_qweb`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                        // 注册表系统
'@web/core/utils/hooks'                     // 钩子工具
'@web/core/assets'                          // 资源加载
'@web/core/utils/render'                    // 渲染工具
'@web/core/utils/timing'                    // 时间工具
'@web/views/fields/standard_field_props'    // 标准字段属性
'@odoo/owl'                                 // OWL框架
```

## 核心组件

### 1. MenuItem 组件

```javascript
class MenuItem extends Component {
    static template = "web.ProfilingQwebView.menuitem";
    static props = {
        view: Object,
    };
}
```

**菜单项功能**:
- **视图选择**: 用于选择要分析的视图
- **模板显示**: 显示视图的基本信息
- **交互控制**: 提供视图切换功能

### 2. ProfilingQwebView 主组件

```javascript
class ProfilingQwebView extends Component {
    static template = "web.ProfilingQwebView";
    static components = { MenuItem };
    static props = { ...standardFieldProps };

    setup() {
        super.setup();
        this.orm = useService("orm");
        this.ace = useRef("ace");
        this.selector = useRef("selector");
        
        this.value = processValue(this.props.record.data[this.props.name]);
        this.state = useState({
            viewID: this.profile.data.length ? this.profile.data[0].view_id : 0,
            view: null,
        });
        
        this.renderProfilingInformation = useDebounced(this.renderProfilingInformation, 100);
    }
}
```

**主组件特性**:
- **Ace编辑器**: 集成Ace编辑器显示QWeb模板
- **性能数据**: 处理和显示性能分析数据
- **交互式分析**: 提供交互式的性能分析界面
- **实时更新**: 支持实时的性能数据更新

## 核心功能

### 1. 数据处理

```javascript
function processValue(value) {
    const data = JSON.parse(value);
    for (const line of data[0].results.data) {
        line.xpath = line.xpath.replace(/([^\]])\//g, "$1[1]/").replace(/([^\]])$/g, "$1[1]");
    }
    return data;
}

get profile() {
    return this.value ? this.value[0].results : { archs: {}, data: [] };
}
```

**数据处理功能**:
- **JSON解析**: 解析性能分析的JSON数据
- **XPath标准化**: 标准化XPath表达式
- **数据结构化**: 结构化性能分析数据
- **缓存优化**: 优化数据访问性能

### 2. 视图管理

```javascript
async _fetchViewData() {
    const viewIDs = Array.from(new Set(this.profile.data.map((line) => line.view_id)));
    const viewObjects = await this.orm.call("ir.ui.view", "search_read", [], {
        fields: ["id", "display_name", "key"],
        domain: [["id", "in", viewIDs]],
    });
    
    for (const view of viewObjects) {
        view.delay = 0;
        view.query = 0;
        const lines = this.profile.data.filter((l) => l.view_id === view.id);
        const root = lines.find((l) => l.xpath === "");
        if (root) {
            view.delay += root.delay;
            view.query += root.query;
        } else {
            view.delay = lines.map((l) => l.delay).reduce((a, b) => a + b);
            view.query = lines.map((l) => l.query).reduce((a, b) => a + b);
        }
        view.delay = Math.ceil(view.delay * 10) / 10;
    }
    this.viewObjects = viewObjects;
}
```

**视图管理功能**:
- **视图获取**: 从数据库获取视图信息
- **性能统计**: 计算每个视图的性能指标
- **数据聚合**: 聚合性能数据
- **缓存管理**: 管理视图数据缓存

### 3. Ace编辑器集成

```javascript
_startAce(node) {
    this.aceEditor = window.ace.edit(node);
    this.aceEditor.setOptions({
        maxLines: Infinity,
        showPrintMargin: false,
        highlightActiveLine: false,
        highlightGutterLine: true,
        readOnly: true,
    });
    this.aceEditor.renderer.setOptions({
        displayIndentGuides: true,
        showGutter: true,
    });
    this.aceEditor.renderer.$cursorLayer.element.style.display = "none";

    this.aceEditor.$blockScrolling = true;
    this.aceSession = this.aceEditor.getSession();
    this.aceSession.setOptions({
        useWorker: false,
        mode: "ace/mode/qweb",
        tabSize: 2,
        useSoftTabs: true,
    });

    // Ace render 3 times when change the value and 1 time per click.
    this.aceEditor.renderer.on("afterRender", this.renderProfilingInformation.bind(this));
}
```

**编辑器集成功能**:
- **只读模式**: 配置为只读的代码查看器
- **QWeb语法**: 支持QWeb模板语法高亮
- **性能渲染**: 在代码上叠加性能信息
- **交互响应**: 响应用户交互事件

### 4. 性能信息渲染

```javascript
renderProfilingInformation() {
    this._unmoutInfo();

    const flat = {};
    const arch = [{ xpath: "", children: [] }];
    const rows = this.ace.el.querySelectorAll(".ace_gutter .ace_gutter-cell");
    const elems = this.ace.el.querySelectorAll(
        ".ace_tag-open, .ace_end-tag-close, .ace_end-tag-open, .ace_qweb"
    );
    
    elems.forEach((node) => {
        const parent = arch[arch.length - 1];
        let xpath = parent.xpath;
        
        if (node.classList.contains("ace_end-tag-close")) {
            // Close tag processing
        } else if (node.classList.contains("ace_qweb")) {
            // QWeb directive processing
            const directive = node.textContent;
            parent.directive.push({
                el: node,
                directive: directive,
            });

            // Compute delay and query number
            let delay = 0;
            let query = 0;
            for (const line of this.profile.data) {
                if (
                    line.view_id === this.state.viewID &&
                    line.xpath === xpath &&
                    line.directive.includes(directive)
                ) {
                    delay += line.delay;
                    query += line.query;
                }
            }

            // Render delay and query number in span visible on hover
            if ((delay || query) && !node.querySelector(".o_info")) {
                this._renderHover(delay, query, node);
            }
        }
        // ... more processing logic
    });
}
```

**性能信息渲染功能**:
- **DOM解析**: 解析Ace编辑器的DOM结构
- **XPath计算**: 计算每个元素的XPath
- **性能映射**: 将性能数据映射到代码元素
- **可视化显示**: 在代码上显示性能信息

## 使用场景

### 1. QWeb性能分析器

```javascript
// QWeb性能分析器
class QWebPerformanceAnalyzer {
    constructor(env) {
        this.env = env;
        this.profilingData = null;
        this.analysisResults = new Map();
        this.setupAnalyzer();
    }
    
    setupAnalyzer() {
        this.performanceMetrics = {
            renderTime: 'Template rendering time',
            queryCount: 'Database queries executed',
            memoryUsage: 'Memory consumption',
            cacheHits: 'Template cache hits',
            compilationTime: 'Template compilation time'
        };
    }
    
    // 分析QWeb模板性能
    async analyzeTemplate(templateData) {
        try {
            const processedData = this.processTemplateData(templateData);
            const analysis = await this.performAnalysis(processedData);
            
            this.analysisResults.set(templateData.viewId, analysis);
            return analysis;
        } catch (error) {
            console.error('Template analysis failed:', error);
            throw error;
        }
    }
    
    processTemplateData(templateData) {
        const processed = {
            viewId: templateData.viewId,
            arch: templateData.arch,
            profilingData: [],
            xpathMap: new Map(),
            directiveMap: new Map()
        };
        
        // 处理性能数据
        for (const line of templateData.profilingData) {
            const normalizedXPath = this.normalizeXPath(line.xpath);
            processed.profilingData.push({
                ...line,
                xpath: normalizedXPath
            });
            
            // 构建XPath映射
            if (!processed.xpathMap.has(normalizedXPath)) {
                processed.xpathMap.set(normalizedXPath, []);
            }
            processed.xpathMap.get(normalizedXPath).push(line);
            
            // 构建指令映射
            if (line.directive) {
                if (!processed.directiveMap.has(line.directive)) {
                    processed.directiveMap.set(line.directive, []);
                }
                processed.directiveMap.get(line.directive).push(line);
            }
        }
        
        return processed;
    }
    
    normalizeXPath(xpath) {
        return xpath.replace(/([^\]])\//g, "$1[1]/").replace(/([^\]])$/g, "$1[1]");
    }
    
    async performAnalysis(processedData) {
        const analysis = {
            viewId: processedData.viewId,
            totalRenderTime: 0,
            totalQueries: 0,
            hotspots: [],
            recommendations: [],
            elementAnalysis: new Map(),
            directiveAnalysis: new Map()
        };
        
        // 分析每个元素的性能
        for (const [xpath, lines] of processedData.xpathMap) {
            const elementMetrics = this.analyzeElement(xpath, lines);
            analysis.elementAnalysis.set(xpath, elementMetrics);
            
            analysis.totalRenderTime += elementMetrics.totalTime;
            analysis.totalQueries += elementMetrics.totalQueries;
            
            // 识别性能热点
            if (elementMetrics.totalTime > 10 || elementMetrics.totalQueries > 5) {
                analysis.hotspots.push({
                    xpath,
                    type: 'element',
                    metrics: elementMetrics,
                    severity: this.calculateSeverity(elementMetrics)
                });
            }
        }
        
        // 分析指令性能
        for (const [directive, lines] of processedData.directiveMap) {
            const directiveMetrics = this.analyzeDirective(directive, lines);
            analysis.directiveAnalysis.set(directive, directiveMetrics);
            
            // 识别慢指令
            if (directiveMetrics.averageTime > 5) {
                analysis.hotspots.push({
                    directive,
                    type: 'directive',
                    metrics: directiveMetrics,
                    severity: this.calculateSeverity(directiveMetrics)
                });
            }
        }
        
        // 生成优化建议
        analysis.recommendations = this.generateRecommendations(analysis);
        
        return analysis;
    }
    
    analyzeElement(xpath, lines) {
        const metrics = {
            xpath,
            executionCount: lines.length,
            totalTime: 0,
            averageTime: 0,
            maxTime: 0,
            minTime: Infinity,
            totalQueries: 0,
            averageQueries: 0,
            timeDistribution: []
        };
        
        for (const line of lines) {
            metrics.totalTime += line.delay || 0;
            metrics.totalQueries += line.query || 0;
            metrics.maxTime = Math.max(metrics.maxTime, line.delay || 0);
            metrics.minTime = Math.min(metrics.minTime, line.delay || 0);
            
            metrics.timeDistribution.push({
                time: line.delay || 0,
                queries: line.query || 0,
                directive: line.directive
            });
        }
        
        metrics.averageTime = metrics.totalTime / metrics.executionCount;
        metrics.averageQueries = metrics.totalQueries / metrics.executionCount;
        metrics.minTime = metrics.minTime === Infinity ? 0 : metrics.minTime;
        
        return metrics;
    }
    
    analyzeDirective(directive, lines) {
        const metrics = {
            directive,
            usageCount: lines.length,
            totalTime: 0,
            averageTime: 0,
            maxTime: 0,
            totalQueries: 0,
            affectedElements: new Set()
        };
        
        for (const line of lines) {
            metrics.totalTime += line.delay || 0;
            metrics.totalQueries += line.query || 0;
            metrics.maxTime = Math.max(metrics.maxTime, line.delay || 0);
            metrics.affectedElements.add(line.xpath);
        }
        
        metrics.averageTime = metrics.totalTime / metrics.usageCount;
        metrics.elementCount = metrics.affectedElements.size;
        
        return metrics;
    }
    
    calculateSeverity(metrics) {
        let score = 0;
        
        // 时间权重
        if (metrics.totalTime > 50) score += 3;
        else if (metrics.totalTime > 20) score += 2;
        else if (metrics.totalTime > 10) score += 1;
        
        // 查询权重
        if (metrics.totalQueries > 20) score += 3;
        else if (metrics.totalQueries > 10) score += 2;
        else if (metrics.totalQueries > 5) score += 1;
        
        // 执行次数权重
        if (metrics.executionCount > 100) score += 2;
        else if (metrics.executionCount > 50) score += 1;
        
        if (score >= 6) return 'critical';
        if (score >= 4) return 'high';
        if (score >= 2) return 'medium';
        return 'low';
    }
    
    generateRecommendations(analysis) {
        const recommendations = [];
        
        // 分析热点并生成建议
        for (const hotspot of analysis.hotspots) {
            if (hotspot.type === 'element') {
                if (hotspot.metrics.totalQueries > 10) {
                    recommendations.push({
                        type: 'database',
                        severity: hotspot.severity,
                        message: `Element at ${hotspot.xpath} executes ${hotspot.metrics.totalQueries} queries. Consider optimizing database access.`,
                        xpath: hotspot.xpath
                    });
                }
                
                if (hotspot.metrics.totalTime > 20) {
                    recommendations.push({
                        type: 'performance',
                        severity: hotspot.severity,
                        message: `Element at ${hotspot.xpath} takes ${hotspot.metrics.totalTime.toFixed(2)}ms to render. Consider optimization.`,
                        xpath: hotspot.xpath
                    });
                }
            } else if (hotspot.type === 'directive') {
                recommendations.push({
                    type: 'directive',
                    severity: hotspot.severity,
                    message: `Directive '${hotspot.directive}' has high average execution time (${hotspot.metrics.averageTime.toFixed(2)}ms).`,
                    directive: hotspot.directive
                });
            }
        }
        
        // 全局建议
        if (analysis.totalRenderTime > 100) {
            recommendations.push({
                type: 'global',
                severity: 'high',
                message: `Total template rendering time is ${analysis.totalRenderTime.toFixed(2)}ms. Consider template optimization.`
            });
        }
        
        if (analysis.totalQueries > 50) {
            recommendations.push({
                type: 'global',
                severity: 'high',
                message: `Template executes ${analysis.totalQueries} database queries. Consider query optimization.`
            });
        }
        
        return recommendations.sort((a, b) => {
            const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
            return severityOrder[b.severity] - severityOrder[a.severity];
        });
    }
    
    // 生成性能报告
    generatePerformanceReport(viewId) {
        const analysis = this.analysisResults.get(viewId);
        if (!analysis) {
            throw new Error(`No analysis data found for view ${viewId}`);
        }
        
        const report = {
            viewId,
            summary: {
                totalRenderTime: analysis.totalRenderTime,
                totalQueries: analysis.totalQueries,
                hotspotCount: analysis.hotspots.length,
                recommendationCount: analysis.recommendations.length
            },
            hotspots: analysis.hotspots,
            recommendations: analysis.recommendations,
            elementMetrics: Array.from(analysis.elementAnalysis.entries()).map(([xpath, metrics]) => ({
                xpath,
                ...metrics
            })),
            directiveMetrics: Array.from(analysis.directiveAnalysis.entries()).map(([directive, metrics]) => ({
                directive,
                ...metrics
            })),
            generatedAt: new Date().toISOString()
        };
        
        return report;
    }
    
    // 比较不同版本的性能
    compareAnalysis(viewId1, viewId2) {
        const analysis1 = this.analysisResults.get(viewId1);
        const analysis2 = this.analysisResults.get(viewId2);
        
        if (!analysis1 || !analysis2) {
            throw new Error('Both analyses must be available for comparison');
        }
        
        const comparison = {
            renderTimeDiff: analysis2.totalRenderTime - analysis1.totalRenderTime,
            renderTimePercent: ((analysis2.totalRenderTime - analysis1.totalRenderTime) / analysis1.totalRenderTime) * 100,
            queriesDiff: analysis2.totalQueries - analysis1.totalQueries,
            queriesPercent: ((analysis2.totalQueries - analysis1.totalQueries) / analysis1.totalQueries) * 100,
            hotspotsDiff: analysis2.hotspots.length - analysis1.hotspots.length,
            improvementAreas: [],
            regressionAreas: []
        };
        
        // 识别改进和退化区域
        for (const [xpath, metrics2] of analysis2.elementAnalysis) {
            const metrics1 = analysis1.elementAnalysis.get(xpath);
            if (metrics1) {
                const timeDiff = metrics2.totalTime - metrics1.totalTime;
                const queriesDiff = metrics2.totalQueries - metrics1.totalQueries;
                
                if (timeDiff < -5 || queriesDiff < -2) {
                    comparison.improvementAreas.push({
                        xpath,
                        timeDiff,
                        queriesDiff
                    });
                } else if (timeDiff > 5 || queriesDiff > 2) {
                    comparison.regressionAreas.push({
                        xpath,
                        timeDiff,
                        queriesDiff
                    });
                }
            }
        }
        
        return comparison;
    }
}

// 使用示例
const analyzer = new QWebPerformanceAnalyzer(env);

// 分析模板性能
const analysis = await analyzer.analyzeTemplate({
    viewId: 123,
    arch: '<t t-name="template">...</t>',
    profilingData: profilingResults
});

// 生成性能报告
const report = analyzer.generatePerformanceReport(123);
console.log('Performance Report:', report);
```

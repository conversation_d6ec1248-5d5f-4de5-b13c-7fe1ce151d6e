# CompanyService - 公司服务

## 概述

`company_service.js` 是 Odoo Web 客户端的公司服务，提供了多公司环境下的公司管理和切换功能。该模块包含151行代码，是一个核心服务模块，专门用于管理用户的公司权限、活动公司列表、公司切换等功能，具备公司ID解析、权限验证、状态同步、自动重载等特性，是Odoo Web客户端中多公司架构的核心服务。

## 文件信息
- **路径**: `/web/static/src/webclient/company_service.js`
- **行数**: 151
- **模块**: `@web/webclient/company_service`

## 依赖关系

```javascript
// 核心依赖
'@web/core/browser/browser'         // 浏览器工具
'@web/core/network/rpc'             // RPC网络通信
'@web/core/registry'                // 注册表系统
'@web/session'                      // 会话信息
'@web/core/orm_service'             // ORM服务
'@web/core/browser/cookie'          // Cookie管理
'@web/core/user'                    // 用户信息
'@web/core/browser/router'          // 路由管理
```

## 核心常量

### 1. 分隔符定义

```javascript
const CIDS_SEPARATOR = "-";
```

**常量功能**:
- **分隔符**: 用于分隔多个公司ID的字符
- **标准化**: 统一的公司ID分隔标准
- **向后兼容**: 支持旧版本的逗号分隔符

## 工具函数

### 1. parseCompanyIds - 解析公司ID

```javascript
function parseCompanyIds(cids, separator = CIDS_SEPARATOR) {
    if (typeof cids === "string") {
        return cids.split(separator).map(Number);
    } else if (typeof cids === "number") {
        return [cids];
    }
    return [];
}
```

**解析功能**:
- **字符串解析**: 将字符串形式的公司ID解析为数组
- **数字处理**: 将单个数字转换为数组
- **类型安全**: 处理不同类型的输入
- **分隔符支持**: 支持自定义分隔符

### 2. computeActiveCompanyIds - 计算活动公司ID

```javascript
function computeActiveCompanyIds(cids) {
    const { user_companies } = session;
    let activeCompanyIds = cids || [];
    const availableCompaniesFromSession = user_companies.allowed_companies;
    const notAllowedCompanies = activeCompanyIds.filter(
        (id) => !(id in availableCompaniesFromSession)
    );

    if (!activeCompanyIds.length || notAllowedCompanies.length) {
        activeCompanyIds = [user_companies.current_company];
    }
    return activeCompanyIds;
}
```

**计算功能**:
- **权限验证**: 验证用户对公司的访问权限
- **过滤无效**: 过滤掉无权限的公司
- **默认回退**: 无有效公司时回退到当前公司
- **安全保证**: 确保返回的公司都是有权限的

### 3. getCompanyIds - 获取公司ID

```javascript
function getCompanyIds() {
    let cids;
    const state = router.current;
    if ("cids" in state) {
        if (typeof state.cids === "string" && !state.cids.includes(CIDS_SEPARATOR)) {
            cids = parseCompanyIds(state.cids, ",");
        } else {
            cids = parseCompanyIds(state.cids);
        }
    } else if (cookie.get("cids")) {
        cids = parseCompanyIds(cookie.get("cids"));
    }
    return cids || [];
}
```

**获取功能**:
- **路由优先**: 优先从路由状态获取公司ID
- **向后兼容**: 支持旧版本的逗号分隔符
- **Cookie回退**: 路由中没有时从Cookie获取
- **默认处理**: 提供默认的空数组

## 主要服务定义

### 1. companyService - 公司服务

```javascript
const companyService = {
    dependencies: ["action", "orm"],
    start(env, { action, orm }) {
        const allowedCompanies = session.user_companies.allowed_companies;
        const disallowedAncestorCompanies = session.user_companies.disallowed_ancestor_companies;
        const allowedCompaniesWithAncestors = {
            ...allowedCompanies,
            ...disallowedAncestorCompanies,
        };
        const activeCompanyIds = computeActiveCompanyIds(getCompanyIds());

        // 更新浏览器数据
        cookie.set("cids", activeCompanyIds.join(CIDS_SEPARATOR));
        user.updateContext({ allowed_company_ids: activeCompanyIds });
    }
}
```

**服务特性**:
- **依赖注入**: 依赖action和orm服务
- **会话集成**: 从会话获取公司信息
- **状态同步**: 同步Cookie和用户上下文
- **权限管理**: 管理允许和禁止的公司

## 核心功能

### 1. 公司数据管理

```javascript
return {
    allowedCompanies,                    // 允许的公司
    allowedCompaniesWithAncestors,       // 包含祖先的允许公司
    disallowedAncestorCompanies,         // 禁止的祖先公司

    get activeCompanyIds() {
        return activeCompanyIds.slice();
    },

    get currentCompany() {
        return allowedCompanies[activeCompanyIds[0]];
    },

    getCompany(companyId) {
        return allowedCompaniesWithAncestors[companyId];
    }
};
```

**数据管理功能**:
- **allowedCompanies**: 用户有权限访问的公司列表
- **allowedCompaniesWithAncestors**: 包含祖先公司的完整列表
- **activeCompanyIds**: 当前活动的公司ID列表（只读）
- **currentCompany**: 当前主要公司（第一个活动公司）
- **getCompany**: 根据ID获取公司信息

### 2. 公司切换功能

```javascript
async setCompanies(companyIds, includeChildCompanies = true) {
    const newCompanyIds = companyIds.length ? companyIds : [activeCompanyIds[0]];

    function addCompanies(companyIds) {
        for (const companyId of companyIds) {
            if (!newCompanyIds.includes(companyId)) {
                newCompanyIds.push(companyId);
                addCompanies(allowedCompanies[companyId].child_ids);
            }
        }
    }

    if (includeChildCompanies) {
        addCompanies(
            companyIds.flatMap((companyId) => allowedCompanies[companyId].child_ids)
        );
    }

    cookie.set("cids", newCompanyIds.join(CIDS_SEPARATOR));
    user.updateContext({ allowed_company_ids: newCompanyIds });

    const controller = action.currentController;
    const state = {};
    const options = { reload: true };
    
    if (controller?.props.resId && controller?.props.resModel) {
        const hasReadRights = await user.checkAccessRight(
            controller.props.resModel,
            "read",
            controller.props.resId
        );

        if (!hasReadRights) {
            options.replace = true;
            state.actionStack = router.current.actionStack.slice(0, -1);
        }
    }

    router.pushState(state, options);
}
```

**切换功能**:
- **公司设置**: 设置新的活动公司列表
- **子公司包含**: 可选择是否包含子公司
- **递归添加**: 递归添加所有子公司
- **状态更新**: 更新Cookie和用户上下文
- **权限检查**: 检查当前记录的访问权限
- **路由更新**: 更新路由状态并重载

### 3. 自动重载机制

```javascript
rpcBus.addEventListener("RPC:RESPONSE", (ev) => {
    const { data, error } = ev.detail;
    const { model, method } = data.params;
    if (!error && model === "res.company" && UPDATE_METHODS.includes(method)) {
        if (!browser.localStorage.getItem("running_tour")) {
            action.doAction("reload_context");
        }
    }
});
```

**重载机制功能**:
- **RPC监听**: 监听RPC响应事件
- **模型检查**: 检查是否为公司模型的更新操作
- **方法验证**: 验证是否为更新方法
- **Tour排除**: 排除正在运行的Tour
- **自动重载**: 自动重载上下文

### 4. 服务注册

```javascript
registry.category("services").add("company", companyService);
```

**注册功能**:
- **服务类别**: 注册到服务类别
- **全局可用**: 在整个应用中可用
- **依赖管理**: 管理服务依赖
- **生命周期**: 与应用生命周期绑定

## 使用场景

### 1. 多公司环境管理器

```javascript
// 多公司环境管理器
class MultiCompanyEnvironmentManager {
    constructor(env) {
        this.env = env;
        this.companyService = env.services.company;
        this.setupManager();
    }
    
    setupManager() {
        // 设置多公司配置
        this.multiCompanyConfig = {
            enableHierarchyManagement: true,
            enablePermissionValidation: true,
            enableAutoSync: true,
            enableAuditLogging: true,
            maxActiveCompanies: 10
        };
        
        // 设置公司层级
        this.companyHierarchy = new Map();
        
        // 设置权限缓存
        this.permissionCache = new Map();
        
        // 设置切换历史
        this.switchHistory = [];
        
        this.buildCompanyHierarchy();
        this.setupEventListeners();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听公司切换
        this.env.bus.addEventListener('COMPANY_SWITCHED', (event) => {
            this.handleCompanySwitch(event.detail);
        });
        
        // 监听权限变化
        this.env.bus.addEventListener('PERMISSIONS_CHANGED', (event) => {
            this.handlePermissionsChange(event.detail);
        });
    }
    
    // 构建公司层级
    buildCompanyHierarchy() {
        const companies = this.companyService.allowedCompaniesWithAncestors;
        
        Object.values(companies).forEach(company => {
            this.companyHierarchy.set(company.id, {
                company: company,
                children: company.child_ids || [],
                parent: company.parent_id || null,
                level: this.calculateCompanyLevel(company),
                permissions: this.getCompanyPermissions(company.id)
            });
        });
    }
    
    // 计算公司层级
    calculateCompanyLevel(company, level = 0) {
        if (!company.parent_id) {
            return level;
        }
        
        const parent = this.companyService.getCompany(company.parent_id);
        return parent ? this.calculateCompanyLevel(parent, level + 1) : level;
    }
    
    // 获取公司权限
    getCompanyPermissions(companyId) {
        if (this.permissionCache.has(companyId)) {
            return this.permissionCache.get(companyId);
        }
        
        const permissions = {
            canRead: this.checkCompanyPermission(companyId, 'read'),
            canWrite: this.checkCompanyPermission(companyId, 'write'),
            canCreate: this.checkCompanyPermission(companyId, 'create'),
            canDelete: this.checkCompanyPermission(companyId, 'unlink')
        };
        
        this.permissionCache.set(companyId, permissions);
        return permissions;
    }
    
    // 检查公司权限
    checkCompanyPermission(companyId, operation) {
        const allowedCompanies = this.companyService.allowedCompanies;
        return companyId in allowedCompanies;
    }
    
    // 智能公司切换
    async smartCompanySwitch(targetCompanies, options = {}) {
        const config = {
            includeChildren: options.includeChildren !== false,
            validatePermissions: options.validatePermissions !== false,
            preserveContext: options.preserveContext !== false,
            ...options
        };
        
        try {
            // 验证目标公司
            const validatedCompanies = await this.validateTargetCompanies(targetCompanies);
            
            if (validatedCompanies.length === 0) {
                throw new Error('No valid companies to switch to');
            }
            
            // 记录切换前状态
            const previousState = this.captureCurrentState();
            
            // 执行切换
            await this.companyService.setCompanies(validatedCompanies, config.includeChildren);
            
            // 记录切换历史
            this.recordSwitchHistory({
                from: previousState.activeCompanies,
                to: validatedCompanies,
                timestamp: Date.now(),
                config: config
            });
            
            // 触发切换完成事件
            this.env.bus.trigger('SMART_COMPANY_SWITCH_COMPLETED', {
                companies: validatedCompanies,
                config: config,
                timestamp: Date.now()
            });
            
            return {
                success: true,
                companies: validatedCompanies,
                message: 'Company switch completed successfully'
            };
            
        } catch (error) {
            this.handleSwitchError(error, targetCompanies, config);
            throw error;
        }
    }
    
    // 验证目标公司
    async validateTargetCompanies(targetCompanies) {
        const validCompanies = [];
        
        for (const companyId of targetCompanies) {
            // 检查公司是否存在
            const company = this.companyService.getCompany(companyId);
            if (!company) {
                console.warn(`Company ${companyId} not found`);
                continue;
            }
            
            // 检查权限
            if (this.multiCompanyConfig.enablePermissionValidation) {
                const permissions = this.getCompanyPermissions(companyId);
                if (!permissions.canRead) {
                    console.warn(`No read permission for company ${companyId}`);
                    continue;
                }
            }
            
            validCompanies.push(companyId);
        }
        
        return validCompanies;
    }
    
    // 捕获当前状态
    captureCurrentState() {
        return {
            activeCompanies: this.companyService.activeCompanyIds,
            currentCompany: this.companyService.currentCompany,
            timestamp: Date.now(),
            url: window.location.href,
            context: this.env.services.user.context
        };
    }
    
    // 记录切换历史
    recordSwitchHistory(switchData) {
        this.switchHistory.push(switchData);
        
        // 限制历史记录数量
        if (this.switchHistory.length > 100) {
            this.switchHistory.shift();
        }
        
        // 保存到本地存储
        this.saveSwitchHistory();
    }
    
    // 保存切换历史
    saveSwitchHistory() {
        try {
            localStorage.setItem('odoo_company_switch_history', JSON.stringify(this.switchHistory));
        } catch (error) {
            console.warn('Failed to save switch history:', error);
        }
    }
    
    // 加载切换历史
    loadSwitchHistory() {
        try {
            const stored = localStorage.getItem('odoo_company_switch_history');
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            console.warn('Failed to load switch history:', error);
            return [];
        }
    }
    
    // 获取公司统计
    getCompanyStatistics() {
        const allowedCompanies = Object.values(this.companyService.allowedCompanies);
        const activeCompanies = this.companyService.activeCompanyIds;
        
        return {
            totalAllowedCompanies: allowedCompanies.length,
            activeCompanies: activeCompanies.length,
            currentCompany: this.companyService.currentCompany?.name,
            hierarchyLevels: this.getHierarchyLevels(),
            switchHistory: this.switchHistory.length,
            lastSwitch: this.switchHistory[this.switchHistory.length - 1]
        };
    }
    
    // 获取层级级别
    getHierarchyLevels() {
        const levels = new Set();
        
        this.companyHierarchy.forEach(node => {
            levels.add(node.level);
        });
        
        return Array.from(levels).sort((a, b) => a - b);
    }
    
    // 获取公司路径
    getCompanyPath(companyId) {
        const path = [];
        let current = this.companyHierarchy.get(companyId);
        
        while (current) {
            path.unshift(current.company);
            current = current.parent ? this.companyHierarchy.get(current.parent) : null;
        }
        
        return path;
    }
    
    // 获取子公司树
    getCompanySubtree(companyId) {
        const company = this.companyHierarchy.get(companyId);
        if (!company) return null;
        
        const subtree = {
            company: company.company,
            children: []
        };
        
        company.children.forEach(childId => {
            const childSubtree = this.getCompanySubtree(childId);
            if (childSubtree) {
                subtree.children.push(childSubtree);
            }
        });
        
        return subtree;
    }
    
    // 处理公司切换
    handleCompanySwitch(switchData) {
        console.log('Company switch detected:', switchData);
        
        // 清理权限缓存
        this.permissionCache.clear();
        
        // 重建层级（如果需要）
        if (this.multiCompanyConfig.enableHierarchyManagement) {
            this.buildCompanyHierarchy();
        }
        
        // 触发环境更新
        this.env.bus.trigger('COMPANY_ENVIRONMENT_UPDATED', {
            switchData: switchData,
            timestamp: Date.now()
        });
    }
    
    // 处理权限变化
    handlePermissionsChange(permissionData) {
        console.log('Permissions changed:', permissionData);
        
        // 清理权限缓存
        this.permissionCache.clear();
        
        // 重新验证当前公司
        this.validateCurrentCompanies();
    }
    
    // 验证当前公司
    async validateCurrentCompanies() {
        const activeCompanies = this.companyService.activeCompanyIds;
        const validCompanies = await this.validateTargetCompanies(activeCompanies);
        
        if (validCompanies.length !== activeCompanies.length) {
            console.warn('Some active companies are no longer valid');
            
            // 自动切换到有效公司
            if (validCompanies.length > 0) {
                await this.smartCompanySwitch(validCompanies);
            } else {
                // 回退到默认公司
                const defaultCompany = this.companyService.currentCompany;
                if (defaultCompany) {
                    await this.smartCompanySwitch([defaultCompany.id]);
                }
            }
        }
    }
    
    // 处理切换错误
    handleSwitchError(error, targetCompanies, config) {
        console.error('Company switch error:', error);
        
        // 记录错误
        const errorRecord = {
            error: error.message,
            targetCompanies: targetCompanies,
            config: config,
            timestamp: Date.now()
        };
        
        // 触发错误事件
        this.env.bus.trigger('COMPANY_SWITCH_ERROR', {
            error: errorRecord,
            timestamp: Date.now()
        });
    }
    
    // 导出配置
    exportConfiguration() {
        const config = {
            exportDate: new Date().toISOString(),
            multiCompanyConfig: this.multiCompanyConfig,
            companyHierarchy: Array.from(this.companyHierarchy.entries()),
            switchHistory: this.switchHistory,
            statistics: this.getCompanyStatistics()
        };
        
        const blob = new Blob([JSON.stringify(config, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `company_config_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
    }
    
    // 销毁管理器
    destroy() {
        // 清理事件监听器
        this.env.bus.removeEventListener('COMPANY_SWITCHED');
        this.env.bus.removeEventListener('PERMISSIONS_CHANGED');
        
        // 清理缓存
        this.permissionCache.clear();
        this.companyHierarchy.clear();
        
        // 保存历史
        this.saveSwitchHistory();
    }
}

// 使用示例
const multiCompanyManager = new MultiCompanyEnvironmentManager(env);

// 智能公司切换
await multiCompanyManager.smartCompanySwitch([1, 2, 3], {
    includeChildren: true,
    validatePermissions: true
});

// 获取公司统计
const stats = multiCompanyManager.getCompanyStatistics();
console.log('Company statistics:', stats);

// 获取公司路径
const path = multiCompanyManager.getCompanyPath(1);
console.log('Company path:', path);
```

## 技术特点

### 1. 多公司架构
- **权限管理**: 严格的公司权限验证
- **层级支持**: 支持公司层级结构
- **状态同步**: Cookie和用户上下文同步
- **自动重载**: 公司变更时自动重载

### 2. 向后兼容
- **分隔符兼容**: 支持新旧分隔符格式
- **URL兼容**: 支持旧版本URL格式
- **参数兼容**: 兼容cid和cids参数
- **平滑迁移**: 提供平滑的版本迁移

### 3. 状态管理
- **Cookie存储**: 使用Cookie持久化公司状态
- **路由集成**: 与路由系统深度集成
- **上下文更新**: 自动更新用户上下文
- **权限检查**: 实时的权限验证

### 4. 错误处理
- **权限验证**: 完善的权限验证机制
- **回退策略**: 智能的回退策略
- **错误恢复**: 自动的错误恢复
- **状态一致**: 保证状态一致性

## 设计模式

### 1. 服务模式 (Service Pattern)
- **服务注册**: 注册为全局服务
- **依赖注入**: 依赖其他服务
- **接口定义**: 清晰的服务接口

### 2. 观察者模式 (Observer Pattern)
- **事件监听**: 监听RPC响应事件
- **状态通知**: 通知公司状态变化
- **自动响应**: 自动响应变化

### 3. 策略模式 (Strategy Pattern)
- **切换策略**: 不同的公司切换策略
- **权限策略**: 不同的权限验证策略
- **同步策略**: 不同的状态同步策略

### 4. 单例模式 (Singleton Pattern)
- **服务实例**: 全局唯一的服务实例
- **状态管理**: 统一的公司状态管理
- **资源共享**: 共享公司资源

## 注意事项

1. **权限验证**: 确保所有公司操作都进行权限验证
2. **状态一致性**: 保持Cookie、上下文和路由状态一致
3. **性能考虑**: 避免频繁的公司切换操作
4. **错误处理**: 妥善处理权限不足和网络错误

## 扩展建议

1. **缓存优化**: 添加公司信息缓存机制
2. **批量操作**: 支持批量公司操作
3. **权限细化**: 更细粒度的权限控制
4. **审计日志**: 完善的公司切换审计日志
5. **性能监控**: 添加性能监控和优化

该公司服务为Odoo Web客户端提供了完整的多公司环境管理功能，通过权限验证和状态同步确保了安全可靠的多公司操作体验。

# LoadingIndicator - 加载指示器

## 概述

`loading_indicator.js` 是 Odoo Web 客户端的加载指示器组件，提供了用户操作反馈的可视化加载状态显示。该模块包含72行代码，是一个OWL组件，专门用于在用户执行操作时显示加载状态，具备RPC请求监听、延迟显示、计数管理、过渡动画等特性，是Odoo Web客户端中用户体验优化的重要组件。

## 文件信息
- **路径**: `/web/static/src/webclient/loading_indicator/loading_indicator.js`
- **行数**: 72
- **模块**: `@web/webclient/loading_indicator/loading_indicator`

## 依赖关系

```javascript
// 核心依赖
'@web/core/browser/browser'         // 浏览器工具
'@web/core/network/rpc'             // RPC网络通信
'@web/core/registry'                // 注册表系统
'@web/core/utils/hooks'             // 工具钩子
'@web/core/transition'              // 过渡动画
'@odoo/owl'                         // OWL框架
```

## 主组件定义

### 1. LoadingIndicator 组件

```javascript
class LoadingIndicator extends Component {
    static template = "web.LoadingIndicator";
    static components = { Transition };
    static props = {};

    setup() {
        this.state = useState({
            count: 0,
            show: false,
        });
        this.rpcIds = new Set();
        this.startShowTimer = null;
        useBus(rpcBus, "RPC:REQUEST", this.requestCall);
        useBus(rpcBus, "RPC:RESPONSE", this.responseCall);
    }
}
```

**组件特性**:
- **状态管理**: 管理加载计数和显示状态
- **RPC监听**: 监听RPC请求和响应事件
- **延迟显示**: 延迟250ms后显示加载指示器
- **过渡动画**: 使用Transition组件提供平滑动画

## 核心属性

### 1. 组件状态

```javascript
// 状态定义
this.state = useState({
    count: 0,        // 当前活动的RPC请求数量
    show: false,     // 是否显示加载指示器
});

// 辅助属性
this.rpcIds = new Set();           // 存储活动RPC请求的ID集合
this.startShowTimer = null;        // 延迟显示的定时器
```

**状态属性功能**:
- **count**: 跟踪当前正在进行的RPC请求数量
- **show**: 控制加载指示器的显示和隐藏
- **rpcIds**: 使用Set存储唯一的RPC请求ID
- **startShowTimer**: 管理延迟显示的定时器

### 2. 事件监听

```javascript
// RPC事件监听
useBus(rpcBus, "RPC:REQUEST", this.requestCall);
useBus(rpcBus, "RPC:RESPONSE", this.responseCall);
```

**监听功能**:
- **RPC:REQUEST**: 监听RPC请求开始事件
- **RPC:RESPONSE**: 监听RPC请求完成事件
- **事件总线**: 使用rpcBus进行事件通信
- **自动绑定**: 自动绑定事件处理函数

## 核心功能

### 1. RPC请求处理

```javascript
requestCall({ detail }) {
    if (detail.settings.silent) {
        return;
    }
    if (this.state.count === 0) {
        browser.clearTimeout(this.startShowTimer);
        this.startShowTimer = browser.setTimeout(() => {
            if (this.state.count) {
                this.state.show = true;
            }
        }, 250);
    }
    this.rpcIds.add(detail.data.id);
    this.state.count++;
}
```

**请求处理功能**:
- **静默检查**: 跳过标记为silent的请求
- **延迟显示**: 250ms后显示加载指示器
- **ID跟踪**: 将请求ID添加到跟踪集合
- **计数更新**: 增加活动请求计数
- **定时器管理**: 管理延迟显示定时器

### 2. RPC响应处理

```javascript
responseCall({ detail }) {
    if (detail.settings.silent) {
        return;
    }
    this.rpcIds.delete(detail.data.id);
    this.state.count = this.rpcIds.size;
    if (this.state.count === 0) {
        browser.clearTimeout(this.startShowTimer);
        this.state.show = false;
    }
}
```

**响应处理功能**:
- **静默检查**: 跳过标记为silent的响应
- **ID移除**: 从跟踪集合中移除完成的请求ID
- **计数同步**: 将计数与集合大小同步
- **隐藏逻辑**: 当没有活动请求时隐藏指示器
- **定时器清理**: 清理延迟显示定时器

### 3. 延迟显示机制

```javascript
// 延迟显示逻辑
if (this.state.count === 0) {
    browser.clearTimeout(this.startShowTimer);
    this.startShowTimer = browser.setTimeout(() => {
        if (this.state.count) {
            this.state.show = true;
        }
    }, 250);
}
```

**延迟机制功能**:
- **防抖动**: 避免短时间请求的闪烁
- **用户体验**: 提供更好的视觉体验
- **性能优化**: 减少不必要的DOM更新
- **智能显示**: 只在有持续请求时显示

### 4. 组件注册

```javascript
registry.category("main_components").add("LoadingIndicator", {
    Component: LoadingIndicator,
});
```

**注册功能**:
- **主组件**: 注册为主要组件
- **全局可用**: 在整个应用中可用
- **自动加载**: 自动加载和初始化
- **生命周期**: 与应用生命周期绑定

## 使用场景

### 1. 加载指示器管理器

```javascript
// 加载指示器管理器
class LoadingIndicatorManager {
    constructor(env) {
        this.env = env;
        this.setupManager();
    }
    
    setupManager() {
        // 设置指示器配置
        this.indicatorConfig = {
            showDelay: 250,
            hideDelay: 0,
            enableTransitions: true,
            enableProgressBar: false,
            enableCustomMessages: true,
            maxDisplayTime: 30000
        };
        
        // 设置指示器样式
        this.indicatorStyles = {
            position: 'bottom-right',
            theme: 'default',
            size: 'medium',
            opacity: 0.9,
            zIndex: 9999
        };
        
        // 设置请求跟踪
        this.requestTracking = {
            activeRequests: new Map(),
            requestHistory: [],
            performanceMetrics: new Map(),
            errorTracking: new Map()
        };
        
        this.setupEventListeners();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听RPC请求
        this.env.bus.addEventListener('RPC:REQUEST', (event) => {
            this.handleRPCRequest(event.detail);
        });
        
        // 监听RPC响应
        this.env.bus.addEventListener('RPC:RESPONSE', (event) => {
            this.handleRPCResponse(event.detail);
        });
        
        // 监听RPC错误
        this.env.bus.addEventListener('RPC:ERROR', (event) => {
            this.handleRPCError(event.detail);
        });
    }
    
    // 创建加载指示器
    createLoadingIndicator() {
        const indicator = new LoadingIndicator();
        
        // 扩展指示器功能
        this.extendLoadingIndicator(indicator);
        
        return indicator;
    }
    
    // 扩展加载指示器功能
    extendLoadingIndicator(indicator) {
        // 添加进度条功能
        if (this.indicatorConfig.enableProgressBar) {
            this.addProgressBarFeature(indicator);
        }
        
        // 添加自定义消息
        if (this.indicatorConfig.enableCustomMessages) {
            this.addCustomMessagesFeature(indicator);
        }
        
        // 添加性能监控
        this.addPerformanceMonitoring(indicator);
        
        // 添加错误处理
        this.addErrorHandling(indicator);
    }
    
    // 添加进度条功能
    addProgressBarFeature(indicator) {
        indicator.progressState = useState({
            progress: 0,
            showProgress: false
        });
        
        indicator.updateProgress = function(progress) {
            this.progressState.progress = Math.min(100, Math.max(0, progress));
            this.progressState.showProgress = progress > 0 && progress < 100;
        };
        
        indicator.resetProgress = function() {
            this.progressState.progress = 0;
            this.progressState.showProgress = false;
        };
        
        // 扩展模板以包含进度条
        const originalTemplate = indicator.constructor.template;
        indicator.constructor.template = xml`
            ${originalTemplate}
            <div t-if="progressState.showProgress" class="o_loading_progress">
                <div class="o_progress_bar" t-att-style="'width: ' + progressState.progress + '%'"/>
            </div>
        `;
    }
    
    // 添加自定义消息功能
    addCustomMessagesFeature(indicator) {
        indicator.messageState = useState({
            message: '',
            showMessage: false
        });
        
        indicator.setMessage = function(message) {
            this.messageState.message = message;
            this.messageState.showMessage = !!message;
        };
        
        indicator.clearMessage = function() {
            this.messageState.message = '';
            this.messageState.showMessage = false;
        };
        
        // 扩展模板以包含消息
        const originalTemplate = indicator.constructor.template;
        indicator.constructor.template = xml`
            ${originalTemplate}
            <div t-if="messageState.showMessage" class="o_loading_message">
                <span t-esc="messageState.message"/>
            </div>
        `;
    }
    
    // 添加性能监控
    addPerformanceMonitoring(indicator) {
        const originalRequestCall = indicator.requestCall.bind(indicator);
        const originalResponseCall = indicator.responseCall.bind(indicator);
        
        indicator.requestCall = function(event) {
            const startTime = performance.now();
            const requestId = event.detail.data.id;
            
            // 记录请求开始时间
            this.requestTracking.activeRequests.set(requestId, {
                startTime: startTime,
                url: event.detail.data.url,
                method: event.detail.data.method
            });
            
            return originalRequestCall(event);
        }.bind(this);
        
        indicator.responseCall = function(event) {
            const endTime = performance.now();
            const requestId = event.detail.data.id;
            const requestData = this.requestTracking.activeRequests.get(requestId);
            
            if (requestData) {
                const duration = endTime - requestData.startTime;
                
                // 记录性能指标
                this.recordPerformanceMetric(requestId, {
                    ...requestData,
                    endTime: endTime,
                    duration: duration,
                    success: !event.detail.error
                });
                
                // 清理活动请求
                this.requestTracking.activeRequests.delete(requestId);
            }
            
            return originalResponseCall(event);
        }.bind(this);
    }
    
    // 添加错误处理
    addErrorHandling(indicator) {
        indicator.handleError = function(error, requestId) {
            // 记录错误
            this.errorTracking.set(requestId, {
                error: error,
                timestamp: Date.now(),
                requestData: this.requestTracking.activeRequests.get(requestId)
            });
            
            // 显示错误消息
            if (this.setMessage) {
                this.setMessage('Request failed. Please try again.');
                
                // 自动清除错误消息
                setTimeout(() => {
                    this.clearMessage();
                }, 3000);
            }
        }.bind(this);
    }
    
    // 处理RPC请求
    handleRPCRequest(requestDetail) {
        const requestId = requestDetail.data.id;
        const requestInfo = {
            id: requestId,
            url: requestDetail.data.url,
            method: requestDetail.data.method,
            startTime: Date.now(),
            silent: requestDetail.settings.silent
        };
        
        // 记录请求历史
        this.requestTracking.requestHistory.push(requestInfo);
        
        // 限制历史记录数量
        if (this.requestTracking.requestHistory.length > 100) {
            this.requestTracking.requestHistory.shift();
        }
        
        // 触发请求开始事件
        this.env.bus.trigger('LOADING_REQUEST_STARTED', {
            requestInfo: requestInfo,
            timestamp: Date.now()
        });
    }
    
    // 处理RPC响应
    handleRPCResponse(responseDetail) {
        const requestId = responseDetail.data.id;
        const endTime = Date.now();
        
        // 查找对应的请求
        const requestInfo = this.requestTracking.requestHistory.find(r => r.id === requestId);
        
        if (requestInfo) {
            const duration = endTime - requestInfo.startTime;
            
            // 更新请求信息
            requestInfo.endTime = endTime;
            requestInfo.duration = duration;
            requestInfo.success = !responseDetail.error;
            
            // 记录性能指标
            this.recordPerformanceMetric(requestId, requestInfo);
        }
        
        // 触发请求完成事件
        this.env.bus.trigger('LOADING_REQUEST_COMPLETED', {
            requestId: requestId,
            duration: requestInfo ? requestInfo.duration : 0,
            success: !responseDetail.error,
            timestamp: endTime
        });
    }
    
    // 处理RPC错误
    handleRPCError(errorDetail) {
        const requestId = errorDetail.data.id;
        
        // 记录错误
        this.requestTracking.errorTracking.set(requestId, {
            error: errorDetail.error,
            timestamp: Date.now(),
            requestData: this.requestTracking.activeRequests.get(requestId)
        });
        
        // 触发错误事件
        this.env.bus.trigger('LOADING_REQUEST_ERROR', {
            requestId: requestId,
            error: errorDetail.error,
            timestamp: Date.now()
        });
    }
    
    // 记录性能指标
    recordPerformanceMetric(requestId, requestInfo) {
        const metric = {
            requestId: requestId,
            url: requestInfo.url,
            method: requestInfo.method,
            duration: requestInfo.duration,
            success: requestInfo.success,
            timestamp: requestInfo.startTime
        };
        
        this.requestTracking.performanceMetrics.set(requestId, metric);
        
        // 限制性能指标数量
        if (this.requestTracking.performanceMetrics.size > 200) {
            const oldestKey = this.requestTracking.performanceMetrics.keys().next().value;
            this.requestTracking.performanceMetrics.delete(oldestKey);
        }
    }
    
    // 获取性能统计
    getPerformanceStatistics() {
        const metrics = Array.from(this.requestTracking.performanceMetrics.values());
        
        if (metrics.length === 0) {
            return {
                totalRequests: 0,
                averageDuration: 0,
                successRate: 0,
                errorRate: 0
            };
        }
        
        const totalRequests = metrics.length;
        const successfulRequests = metrics.filter(m => m.success).length;
        const averageDuration = metrics.reduce((sum, m) => sum + m.duration, 0) / totalRequests;
        
        return {
            totalRequests: totalRequests,
            successfulRequests: successfulRequests,
            failedRequests: totalRequests - successfulRequests,
            averageDuration: Math.round(averageDuration),
            successRate: Math.round((successfulRequests / totalRequests) * 100),
            errorRate: Math.round(((totalRequests - successfulRequests) / totalRequests) * 100)
        };
    }
    
    // 获取当前活动请求
    getActiveRequests() {
        return Array.from(this.requestTracking.activeRequests.entries()).map(([id, data]) => ({
            id: id,
            ...data,
            duration: Date.now() - data.startTime
        }));
    }
    
    // 获取请求历史
    getRequestHistory(limit = 50) {
        return this.requestTracking.requestHistory
            .slice(-limit)
            .sort((a, b) => b.startTime - a.startTime);
    }
    
    // 清理性能数据
    clearPerformanceData() {
        this.requestTracking.performanceMetrics.clear();
        this.requestTracking.requestHistory = [];
        this.requestTracking.errorTracking.clear();
    }
    
    // 导出性能报告
    exportPerformanceReport() {
        const report = {
            exportDate: new Date().toISOString(),
            statistics: this.getPerformanceStatistics(),
            activeRequests: this.getActiveRequests(),
            requestHistory: this.getRequestHistory(100),
            config: this.indicatorConfig
        };
        
        const blob = new Blob([JSON.stringify(report, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `loading_performance_report_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
    }
    
    // 销毁管理器
    destroy() {
        // 清理事件监听器
        this.env.bus.removeEventListener('RPC:REQUEST');
        this.env.bus.removeEventListener('RPC:RESPONSE');
        this.env.bus.removeEventListener('RPC:ERROR');
        
        // 清理数据
        this.clearPerformanceData();
        this.requestTracking.activeRequests.clear();
    }
}

// 使用示例
const loadingManager = new LoadingIndicatorManager(env);

// 创建加载指示器
const loadingIndicator = loadingManager.createLoadingIndicator();

// 获取性能统计
const stats = loadingManager.getPerformanceStatistics();
console.log('Loading performance:', stats);

// 获取活动请求
const activeRequests = loadingManager.getActiveRequests();
console.log('Active requests:', activeRequests);

// 导出性能报告
loadingManager.exportPerformanceReport();
```

## 技术特点

### 1. 智能显示机制
- **延迟显示**: 250ms延迟避免短时间请求的闪烁
- **自动隐藏**: 所有请求完成后自动隐藏
- **防抖动**: 避免频繁的显示/隐藏切换
- **用户体验**: 提供流畅的视觉反馈

### 2. 请求跟踪
- **ID管理**: 使用Set管理唯一的请求ID
- **计数同步**: 计数与实际请求数量保持同步
- **静默过滤**: 过滤静默请求避免不必要的显示
- **状态一致**: 确保显示状态与请求状态一致

### 3. 事件驱动
- **RPC总线**: 监听RPC事件总线
- **事件处理**: 处理请求和响应事件
- **自动绑定**: 自动绑定事件处理函数
- **生命周期**: 与组件生命周期绑定

### 4. 性能优化
- **最小DOM**: 最小化DOM操作
- **状态管理**: 高效的状态管理
- **内存管理**: 合理的内存使用
- **渲染优化**: 优化的渲染性能

## 设计模式

### 1. 观察者模式 (Observer Pattern)
- **事件监听**: 监听RPC事件
- **状态响应**: 响应请求状态变化
- **自动更新**: 自动更新显示状态

### 2. 状态模式 (State Pattern)
- **显示状态**: 管理显示和隐藏状态
- **计数状态**: 管理请求计数状态
- **状态转换**: 智能的状态转换

### 3. 单例模式 (Singleton Pattern)
- **全局组件**: 作为全局唯一组件
- **状态共享**: 共享加载状态
- **资源管理**: 统一的资源管理

### 4. 策略模式 (Strategy Pattern)
- **显示策略**: 不同的显示策略
- **延迟策略**: 不同的延迟策略
- **过滤策略**: 不同的请求过滤策略

## 注意事项

1. **性能影响**: 避免频繁的状态更新
2. **内存泄漏**: 及时清理定时器和事件监听
3. **用户体验**: 提供合适的延迟和动画
4. **兼容性**: 确保跨浏览器兼容性

## 扩展建议

1. **进度显示**: 添加请求进度显示
2. **自定义样式**: 支持自定义样式和主题
3. **错误提示**: 显示请求错误信息
4. **性能监控**: 添加性能监控功能
5. **可配置性**: 提供更多配置选项

该加载指示器为Odoo Web客户端提供了优秀的用户反馈机制，通过智能的显示逻辑和高效的状态管理确保了良好的用户体验。

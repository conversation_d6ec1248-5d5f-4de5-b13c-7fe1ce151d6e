# UserMenuItems - 用户菜单项

## 概述

`user_menu_items.js` 是 Odoo Web 客户端的用户菜单项定义模块，提供了用户菜单中的默认菜单项实现。该模块包含168行代码，定义了8个标准菜单项和1个组件，专门用于为用户菜单提供文档、支持、快捷键、偏好设置、账户管理、PWA安装、注销等功能，具备国际化支持、条件显示、外部链接、RPC调用等特性，是Odoo Web客户端中用户菜单功能的核心实现。

## 文件信息
- **路径**: `/web/static/src/webclient/user_menu/user_menu_items.js`
- **行数**: 168
- **模块**: `@web/webclient/user_menu/user_menu_items`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                     // OWL框架
'@web/core/browser/feature_detection'           // 浏览器特性检测
'@web/core/l10n/translation'                    // 国际化翻译
'@web/core/network/rpc'                         // RPC网络调用
'@web/core/user'                                // 用户信息
'@web/core/utils/strings'                       // 字符串工具
'@web/session'                                  // 会话信息
'@web/core/browser/browser'                     // 浏览器工具
'@web/core/registry'                            // 注册表系统
```

## 菜单项定义

### 1. documentationItem - 文档菜单项

```javascript
function documentationItem(env) {
    const documentationURL = "https://www.odoo.com/documentation/18.0";
    return {
        type: "item",
        id: "documentation",
        description: _t("Documentation"),
        href: documentationURL,
        callback: () => {
            browser.open(documentationURL, "_blank");
        },
        sequence: 10,
    };
}
```

**菜单项特性**:
- **类型**: 标准菜单项
- **功能**: 打开Odoo官方文档
- **链接**: 指向Odoo 18.0文档
- **行为**: 在新标签页中打开

### 2. supportItem - 支持菜单项

```javascript
function supportItem(env) {
    const url = session.support_url;
    return {
        type: "item",
        id: "support",
        description: _t("Support"),
        href: url,
        callback: () => {
            browser.open(url, "_blank");
        },
        sequence: 20,
    };
}
```

**菜单项特性**:
- **类型**: 标准菜单项
- **功能**: 打开支持页面
- **链接**: 从会话获取支持URL
- **行为**: 在新标签页中打开

### 3. shortCutsItem - 快捷键菜单项

```javascript
function shortCutsItem(env) {
    const translatedText = _t("Shortcuts");
    return {
        type: "item",
        id: "shortcuts",
        hide: env.isSmall,
        description: markup(
            `<div class="d-flex align-items-center justify-content-between">
                <span>${escape(translatedText)}</span>
                <span class="fw-bold">${isMacOS() ? "CMD" : "CTRL"}+K</span>
            </div>`
        ),
        callback: () => {
            env.services.command.openMainPalette({ FooterComponent: ShortcutsFooterComponent });
        },
        sequence: 30,
    };
}
```

**菜单项特性**:
- **类型**: 标准菜单项
- **功能**: 打开命令面板
- **条件**: 在小屏幕上隐藏
- **快捷键**: 显示平台相关的快捷键
- **行为**: 打开主命令面板

### 4. preferencesItem - 偏好设置菜单项

```javascript
function preferencesItem(env) {
    return {
        type: "item",
        id: "settings",
        description: _t("Preferences"),
        callback: async function () {
            const actionDescription = await env.services.orm.call("res.users", "action_get");
            actionDescription.res_id = user.userId;
            env.services.action.doAction(actionDescription);
        },
        sequence: 50,
    };
}
```

**菜单项特性**:
- **类型**: 标准菜单项
- **功能**: 打开用户偏好设置
- **异步**: 异步获取用户操作
- **RPC**: 调用ORM获取操作描述
- **行为**: 执行用户设置操作

### 5. odooAccountItem - Odoo账户菜单项

```javascript
function odooAccountItem(env) {
    return {
        type: "item",
        id: "account",
        description: _t("My Odoo.com account"),
        callback: () => {
            rpc("/web/session/account")
                .then((url) => {
                    browser.open(url, "_blank");
                })
                .catch(() => {
                    browser.open("https://accounts.odoo.com/account", "_blank");
                });
        },
        sequence: 60,
    };
}
```

**菜单项特性**:
- **类型**: 标准菜单项
- **功能**: 打开Odoo.com账户页面
- **RPC**: 调用会话账户接口
- **容错**: 失败时使用默认URL
- **行为**: 在新标签页中打开账户页面

### 6. installPWAItem - PWA安装菜单项

```javascript
function installPWAItem(env) {
    let description = _t("Install App");
    let callback = () => env.services.pwa.show();
    let show = () => env.services.pwa.isAvailable;
    const currentApp = env.services.menu.getCurrentApp();
    if (currentApp && ["barcode", "field-service", "shop-floor"].includes(currentApp.actionPath)) {
        description = _t("Install %s", currentApp.name);
        callback = () => {
            window.open(
                `/scoped_app?app_id=${currentApp.webIcon.split(",")[0]}&path=${encodeURIComponent(
                    "scoped_app/" + currentApp.actionPath
                )}`
            );
        };
        show = () => !env.services.pwa.isScopedApp;
    }
    return {
        type: "item",
        id: "install_pwa",
        description,
        callback,
        show,
        sequence: 65,
    };
}
```

**菜单项特性**:
- **类型**: 条件菜单项
- **功能**: 安装PWA应用
- **条件**: 根据PWA可用性显示
- **特殊应用**: 支持特定应用的作用域安装
- **动态**: 动态描述和回调

### 7. logOutItem - 注销菜单项

```javascript
function logOutItem(env) {
    let route = "/web/session/logout";
    if (env.services.pwa.isScopedApp) {
        route += `?redirect=${encodeURIComponent(env.services.pwa.startUrl)}`;
    }
    return {
        type: "item",
        id: "logout",
        description: _t("Log out"),
        href: `${browser.location.origin}${route}`,
        callback: () => {
            browser.location.href = route;
        },
        sequence: 70,
    };
}
```

**菜单项特性**:
- **类型**: 标准菜单项
- **功能**: 用户注销
- **PWA支持**: 支持PWA应用的重定向
- **路由**: 动态构建注销路由
- **行为**: 跳转到注销页面

### 8. separator - 分隔符

```javascript
function separator() {
    return {
        type: "separator",
        sequence: 40,
    };
}
```

**分隔符特性**:
- **类型**: 分隔符
- **功能**: 菜单项分组
- **位置**: 在快捷键和偏好设置之间
- **视觉**: 提供视觉分隔

## 组件定义

### 1. ShortcutsFooterComponent - 快捷键页脚组件

```javascript
class ShortcutsFooterComponent extends Component {
    static template = "web.UserMenu.ShortcutsFooterComponent";
    static props = {};
    setup() {
        this.runShortcutKey = isMacOS() ? "CONTROL" : "ALT";
    }
}
```

**组件特性**:
- **类型**: OWL组件
- **功能**: 快捷键面板的页脚
- **平台**: 根据操作系统显示不同快捷键
- **模板**: 使用专门的页脚模板

## 菜单项注册

### 1. 注册表注册

```javascript
registry
    .category("user_menuitems")
    .add("documentation", documentationItem)
    .add("support", supportItem)
    .add("shortcuts", shortCutsItem)
    .add("separator", separator)
    .add("profile", preferencesItem)
    .add("odoo_account", odooAccountItem)
    .add("install_pwa", installPWAItem)
    .add("log_out", logOutItem);
```

**注册功能**:
- **类别**: 注册到"user_menuitems"类别
- **标识**: 每个菜单项有唯一标识
- **顺序**: 按添加顺序注册
- **全局**: 在整个应用中可用

## 使用场景

### 1. 用户菜单项管理器

```javascript
// 用户菜单项管理器
class UserMenuItemsManager {
    constructor(env) {
        this.env = env;
        this.registry = registry.category("user_menuitems");
        this.setupManager();
    }

    setupManager() {
        // 设置菜单项配置
        this.itemsConfig = {
            enableCustomItems: true,
            enableConditionalItems: true,
            enableExternalLinks: true,
            enableRPCItems: true,
            maxCustomItems: 10
        };

        // 设置菜单项类型
        this.itemTypes = {
            standard: { icon: 'fa-link', handler: this.handleStandardItem },
            action: { icon: 'fa-play', handler: this.handleActionItem },
            external: { icon: 'fa-external-link', handler: this.handleExternalItem },
            separator: { icon: 'fa-minus', handler: this.handleSeparatorItem },
            conditional: { icon: 'fa-question', handler: this.handleConditionalItem }
        };

        // 设置自定义菜单项
        this.customItems = new Map();

        this.setupEventListeners();
    }

    // 设置事件监听器
    setupEventListeners() {
        // 监听菜单项点击
        this.env.bus.addEventListener('USER_MENU_ITEM_CLICKED', (event) => {
            this.handleItemClick(event.detail);
        });

        // 监听菜单项状态变化
        this.env.bus.addEventListener('USER_MENU_ITEM_STATE_CHANGED', (event) => {
            this.handleItemStateChange(event.detail);
        });
    }

    // 创建标准菜单项
    createStandardItem(config) {
        return function(env) {
            return {
                type: "item",
                id: config.id,
                description: config.description,
                callback: config.callback,
                sequence: config.sequence || 100,
                show: config.show || (() => true)
            };
        };
    }

    // 创建外部链接菜单项
    createExternalLinkItem(config) {
        return function(env) {
            return {
                type: "item",
                id: config.id,
                description: config.description,
                href: config.url,
                callback: () => {
                    browser.open(config.url, config.target || "_blank");
                },
                sequence: config.sequence || 100,
                show: config.show || (() => true)
            };
        };
    }

    // 创建操作菜单项
    createActionItem(config) {
        return function(env) {
            return {
                type: "item",
                id: config.id,
                description: config.description,
                callback: async () => {
                    if (config.action) {
                        await env.services.action.doAction(config.action);
                    } else if (config.rpc) {
                        await env.services.rpc(config.rpc.route, config.rpc.params);
                    }
                },
                sequence: config.sequence || 100,
                show: config.show || (() => true)
            };
        };
    }

    // 创建条件菜单项
    createConditionalItem(config) {
        return function(env) {
            return {
                type: "item",
                id: config.id,
                description: config.description,
                callback: config.callback,
                sequence: config.sequence || 100,
                show: () => {
                    if (config.condition) {
                        return config.condition(env);
                    }
                    return true;
                }
            };
        };
    }

    // 创建分隔符
    createSeparator(sequence) {
        return function(env) {
            return {
                type: "separator",
                sequence: sequence || 100
            };
        };
    }

    // 注册自定义菜单项
    registerCustomItem(id, itemFactory) {
        this.registry.add(id, itemFactory);
        this.customItems.set(id, itemFactory);

        this.env.bus.trigger('CUSTOM_MENU_ITEM_REGISTERED', {
            id: id,
            timestamp: Date.now()
        });
    }

    // 注销菜单项
    unregisterItem(id) {
        this.registry.remove(id);
        this.customItems.delete(id);

        this.env.bus.trigger('MENU_ITEM_UNREGISTERED', {
            id: id,
            timestamp: Date.now()
        });
    }

    // 获取所有菜单项
    getAllItems() {
        return this.registry.getAll().map(factory => factory(this.env));
    }

    // 获取可见菜单项
    getVisibleItems() {
        return this.getAllItems().filter(item => {
            if (item.show) {
                return item.show();
            }
            return true;
        });
    }

    // 获取按类型分组的菜单项
    getItemsByType() {
        const items = this.getAllItems();
        const grouped = {
            items: [],
            separators: []
        };

        items.forEach(item => {
            if (item.type === 'separator') {
                grouped.separators.push(item);
            } else {
                grouped.items.push(item);
            }
        });

        return grouped;
    }

    // 处理菜单项点击
    handleItemClick(clickData) {
        const { itemId, timestamp } = clickData;

        // 记录点击统计
        this.recordItemClick(itemId, timestamp);

        // 触发点击事件
        this.env.bus.trigger('USER_MENU_ITEM_EXECUTED', {
            itemId: itemId,
            timestamp: timestamp
        });
    }

    // 处理菜单项状态变化
    handleItemStateChange(stateData) {
        const { itemId, state, timestamp } = stateData;

        // 更新菜单项状态
        this.updateItemState(itemId, state);

        // 触发状态变化事件
        this.env.bus.trigger('USER_MENU_ITEM_STATE_UPDATED', {
            itemId: itemId,
            state: state,
            timestamp: timestamp
        });
    }

    // 记录菜单项点击
    recordItemClick(itemId, timestamp) {
        const clickData = {
            itemId: itemId,
            timestamp: timestamp,
            user: this.env.services.user.userId,
            session: this.env.services.session.sessionId
        };

        // 存储到本地存储
        this.storeClickData(clickData);

        // 可选：发送到服务器
        this.sendClickAnalytics(clickData);
    }

    // 存储点击数据
    storeClickData(clickData) {
        try {
            const stored = localStorage.getItem('odoo_user_menu_clicks');
            const clicks = stored ? JSON.parse(stored) : [];

            clicks.push(clickData);

            // 限制存储数量
            if (clicks.length > 100) {
                clicks.shift();
            }

            localStorage.setItem('odoo_user_menu_clicks', JSON.stringify(clicks));
        } catch (error) {
            console.warn('Failed to store click data:', error);
        }
    }

    // 发送点击分析
    sendClickAnalytics(clickData) {
        this.env.services.rpc('/web/user_menu/analytics', {
            click_data: clickData
        }).catch(error => {
            console.warn('Failed to send click analytics:', error);
        });
    }

    // 更新菜单项状态
    updateItemState(itemId, state) {
        const item = this.registry.get(itemId);
        if (item) {
            // 更新状态逻辑
            item.state = state;
        }
    }

    // 获取菜单项统计
    getItemStatistics() {
        const allItems = this.getAllItems();
        const visibleItems = this.getVisibleItems();
        const customItems = Array.from(this.customItems.keys());

        return {
            totalItems: allItems.length,
            visibleItems: visibleItems.length,
            customItems: customItems.length,
            itemTypes: this.getItemTypeStatistics(allItems),
            clickStatistics: this.getClickStatistics()
        };
    }

    // 获取菜单项类型统计
    getItemTypeStatistics(items) {
        const stats = {
            items: 0,
            separators: 0,
            external: 0,
            actions: 0
        };

        items.forEach(item => {
            if (item.type === 'separator') {
                stats.separators++;
            } else {
                stats.items++;

                if (item.href) {
                    stats.external++;
                }

                if (item.callback) {
                    stats.actions++;
                }
            }
        });

        return stats;
    }

    // 获取点击统计
    getClickStatistics() {
        try {
            const stored = localStorage.getItem('odoo_user_menu_clicks');
            const clicks = stored ? JSON.parse(stored) : [];

            const stats = {};
            clicks.forEach(click => {
                stats[click.itemId] = (stats[click.itemId] || 0) + 1;
            });

            return stats;
        } catch (error) {
            console.warn('Failed to get click statistics:', error);
            return {};
        }
    }

    // 导出菜单配置
    exportMenuConfiguration() {
        const config = {
            exportDate: new Date().toISOString(),
            user: this.env.services.user.userId,
            customItems: Array.from(this.customItems.entries()),
            statistics: this.getItemStatistics(),
            config: this.itemsConfig
        };

        const blob = new Blob([JSON.stringify(config, null, 2)], {
            type: 'application/json'
        });

        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `user_menu_items_config_${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        URL.revokeObjectURL(url);
    }

    // 重置菜单项
    resetMenuItems() {
        // 清除自定义菜单项
        for (const id of this.customItems.keys()) {
            this.unregisterItem(id);
        }

        // 清除点击统计
        localStorage.removeItem('odoo_user_menu_clicks');

        // 重新初始化
        this.setupManager();

        this.env.bus.trigger('USER_MENU_ITEMS_RESET', {
            timestamp: Date.now()
        });
    }

    // 销毁管理器
    destroy() {
        // 清理事件监听器
        this.env.bus.removeEventListener('USER_MENU_ITEM_CLICKED');
        this.env.bus.removeEventListener('USER_MENU_ITEM_STATE_CHANGED');

        // 清理数据
        this.customItems.clear();
    }
}

// 使用示例
const menuItemsManager = new UserMenuItemsManager(env);

// 注册自定义菜单项
menuItemsManager.registerCustomItem('custom_reports',
    menuItemsManager.createActionItem({
        id: 'custom_reports',
        description: 'Custom Reports',
        action: 'custom.action_reports',
        sequence: 45
    })
);

// 注册外部链接菜单项
menuItemsManager.registerCustomItem('company_website',
    menuItemsManager.createExternalLinkItem({
        id: 'company_website',
        description: 'Company Website',
        url: 'https://company.com',
        sequence: 25
    })
);

// 获取统计信息
const stats = menuItemsManager.getItemStatistics();
console.log('Menu items statistics:', stats);
```

## 技术特点

### 1. 功能多样性
- **文档链接**: 提供官方文档访问
- **支持链接**: 提供技术支持访问
- **快捷键**: 集成命令面板快捷键
- **用户设置**: 提供用户偏好设置
- **账户管理**: 集成Odoo.com账户
- **PWA支持**: 支持渐进式Web应用
- **注销功能**: 提供安全注销

### 2. 平台适配
- **操作系统**: 根据macOS/Windows显示不同快捷键
- **屏幕尺寸**: 在小屏幕上隐藏某些菜单项
- **PWA环境**: 支持PWA应用的特殊处理
- **浏览器**: 使用浏览器API进行页面跳转

### 3. 国际化支持
- **翻译**: 所有文本都支持国际化
- **动态**: 支持动态文本的国际化
- **格式化**: 支持参数化的翻译文本
- **标记**: 支持HTML标记的安全处理

### 4. 错误处理
- **容错**: RPC调用失败时的容错处理
- **默认值**: 提供默认的回退URL
- **异常**: 妥善处理异步操作异常
- **用户反馈**: 提供适当的用户反馈

## 设计模式

### 1. 工厂模式 (Factory Pattern)
- **菜单项创建**: 通过工厂函数创建菜单项
- **环境注入**: 为每个菜单项注入环境
- **配置驱动**: 基于配置创建不同类型菜单项

### 2. 策略模式 (Strategy Pattern)
- **回调策略**: 不同的菜单项回调策略
- **显示策略**: 不同的菜单项显示策略
- **链接策略**: 不同的链接处理策略

### 3. 注册表模式 (Registry Pattern)
- **菜单注册**: 将菜单项注册到注册表
- **动态加载**: 动态加载注册的菜单项
- **扩展支持**: 支持第三方扩展

### 4. 命令模式 (Command Pattern)
- **操作封装**: 封装菜单项的操作
- **异步执行**: 支持异步命令执行
- **参数传递**: 支持命令参数传递

## 注意事项

1. **安全性**: 确保外部链接的安全性
2. **性能**: 避免不必要的RPC调用
3. **用户体验**: 提供清晰的操作反馈
4. **兼容性**: 确保跨平台兼容性

## 扩展建议

1. **动态菜单**: 支持动态加载的菜单项
2. **权限控制**: 基于权限的菜单项显示
3. **个性化**: 支持用户个性化菜单
4. **分析统计**: 添加菜单使用分析
5. **主题支持**: 支持不同主题的菜单样式

该用户菜单项模块为Odoo Web客户端提供了丰富的用户功能入口，通过标准化的菜单项定义和注册机制确保了良好的扩展性和用户体验。
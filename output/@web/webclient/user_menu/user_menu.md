# UserMenu - 用户菜单

## 概述

`user_menu.js` 是 Odoo Web 客户端的用户菜单组件，提供了用户相关功能的下拉菜单界面。该模块包含49行代码，是一个OWL组件，专门用于在系统托盘中显示用户菜单，具备用户信息显示、菜单项注册、动态排序、条件显示等特性，是Odoo Web客户端中用户交互和个人设置的核心入口组件。

## 文件信息
- **路径**: `/web/static/src/webclient/user_menu/user_menu.js`
- **行数**: 49
- **模块**: `@web/webclient/user_menu/user_menu`

## 依赖关系

```javascript
// 核心依赖
'@web/core/dropdown/dropdown'           // 下拉菜单组件
'@web/core/dropdown/dropdown_group'     // 下拉菜单组
'@web/core/dropdown/dropdown_item'      // 下拉菜单项
'@web/core/checkbox/checkbox'           // 复选框组件
'@web/core/registry'                    // 注册表系统
'@web/core/user'                        // 用户信息
'@web/session'                          // 会话信息
'@odoo/owl'                             // OWL框架
'@web/core/utils/urls'                  // URL工具
```

## 主组件定义

### 1. UserMenu 组件

```javascript
class UserMenu extends Component {
    static template = "web.UserMenu";
    static components = { DropdownGroup, Dropdown, DropdownItem, CheckBox };
    static props = {};

    setup() {
        this.userName = user.name;
        this.dbName = session.db;
        const { partnerId, writeDate } = user;
        this.source = imageUrl("res.partner", partnerId, "avatar_128", { unique: writeDate });
    }

    getElements() {
        const sortedItems = userMenuRegistry
            .getAll()
            .map((element) => element(this.env))
            .filter((element) => (element.show ? element.show() : true))
            .sort((x, y) => {
                const xSeq = x.sequence ? x.sequence : 100;
                const ySeq = y.sequence ? y.sequence : 100;
                return xSeq - ySeq;
            });
        return sortedItems;
    }
}
```

**组件特性**:
- **下拉菜单**: 基于Dropdown的用户菜单界面
- **用户信息**: 显示用户名称、头像和数据库信息
- **动态菜单**: 从注册表动态加载菜单项
- **条件显示**: 支持菜单项的条件显示

## 核心属性

### 1. 组件属性

```javascript
// 模板配置
static template = "web.UserMenu";

// 子组件
static components = { DropdownGroup, Dropdown, DropdownItem, CheckBox };

// 属性定义
static props = {};
```

**属性功能**:
- **template**: 指定用户菜单的模板
- **components**: 包含下拉菜单相关组件和复选框
- **props**: 无需外部属性，完全自包含

### 2. 用户信息属性

```javascript
// 用户信息初始化
this.userName = user.name;
this.dbName = session.db;
const { partnerId, writeDate } = user;
this.source = imageUrl("res.partner", partnerId, "avatar_128", { unique: writeDate });
```

**信息属性功能**:
- **userName**: 当前用户的名称
- **dbName**: 当前数据库名称
- **source**: 用户头像的URL地址
- **unique**: 使用writeDate确保头像更新

## 核心功能

### 1. 用户信息获取

```javascript
setup() {
    this.userName = user.name;
    this.dbName = session.db;
    const { partnerId, writeDate } = user;
    this.source = imageUrl("res.partner", partnerId, "avatar_128", { unique: writeDate });
}
```

**信息获取功能**:
- **用户名**: 从user对象获取用户名
- **数据库**: 从session获取数据库名
- **头像URL**: 生成用户头像的URL
- **缓存控制**: 使用writeDate控制头像缓存

### 2. 菜单项动态加载

```javascript
getElements() {
    const sortedItems = userMenuRegistry
        .getAll()
        .map((element) => element(this.env))
        .filter((element) => (element.show ? element.show() : true))
        .sort((x, y) => {
            const xSeq = x.sequence ? x.sequence : 100;
            const ySeq = y.sequence ? y.sequence : 100;
            return xSeq - ySeq;
        });
    return sortedItems;
}
```

**动态加载功能**:
- **注册表获取**: 从userMenuRegistry获取所有菜单项
- **环境注入**: 为每个菜单项注入环境对象
- **条件过滤**: 根据show函数过滤显示的菜单项
- **序列排序**: 按sequence属性排序菜单项

### 3. 菜单项注册表

```javascript
const userMenuRegistry = registry.category("user_menuitems");
```

**注册表功能**:
- **类别定义**: 定义"user_menuitems"类别
- **菜单管理**: 管理用户菜单项的注册
- **扩展支持**: 支持第三方模块扩展菜单
- **动态加载**: 支持动态加载菜单项

### 4. 系统托盘注册

```javascript
const systrayItem = {
    Component: UserMenu,
};
registry.category("systray").add("web.user_menu", systrayItem, { sequence: 0 });
```

**托盘注册功能**:
- **组件注册**: 将UserMenu注册为系统托盘项
- **优先级**: 设置sequence为0，优先显示
- **标识符**: 使用"web.user_menu"作为唯一标识
- **全局可用**: 在系统托盘中全局可用

## 使用场景

### 1. 用户菜单管理器

```javascript
// 用户菜单管理器
class UserMenuManager {
    constructor(env) {
        this.env = env;
        this.userMenuRegistry = registry.category("user_menuitems");
        this.setupManager();
    }
    
    setupManager() {
        // 设置菜单配置
        this.menuConfig = {
            enableCustomization: true,
            enableNotifications: true,
            enableQuickActions: true,
            maxRecentItems: 5,
            enableSearch: false
        };
        
        // 设置菜单项分类
        this.menuCategories = {
            profile: { sequence: 10, icon: 'fa-user', label: 'Profile' },
            settings: { sequence: 20, icon: 'fa-cog', label: 'Settings' },
            help: { sequence: 30, icon: 'fa-question', label: 'Help' },
            system: { sequence: 40, icon: 'fa-system', label: 'System' },
            logout: { sequence: 50, icon: 'fa-sign-out', label: 'Logout' }
        };
        
        // 设置默认菜单项
        this.defaultMenuItems = new Map();
        
        this.setupDefaultMenuItems();
        this.setupEventListeners();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听用户信息变化
        this.env.bus.addEventListener('USER_INFO_CHANGED', (event) => {
            this.handleUserInfoChange(event.detail);
        });
        
        // 监听菜单项变化
        this.env.bus.addEventListener('MENU_ITEM_CHANGED', (event) => {
            this.handleMenuItemChange(event.detail);
        });
    }
    
    // 设置默认菜单项
    setupDefaultMenuItems() {
        // 个人资料菜单项
        this.defaultMenuItems.set('profile', {
            sequence: 10,
            Component: this.createProfileMenuItem(),
            show: () => true
        });
        
        // 偏好设置菜单项
        this.defaultMenuItems.set('preferences', {
            sequence: 15,
            Component: this.createPreferencesMenuItem(),
            show: () => true
        });
        
        // 我的账户菜单项
        this.defaultMenuItems.set('my_account', {
            sequence: 20,
            Component: this.createMyAccountMenuItem(),
            show: () => this.env.services.user.hasGroup('base.group_user')
        });
        
        // 帮助菜单项
        this.defaultMenuItems.set('help', {
            sequence: 30,
            Component: this.createHelpMenuItem(),
            show: () => true
        });
        
        // 关于菜单项
        this.defaultMenuItems.set('about', {
            sequence: 35,
            Component: this.createAboutMenuItem(),
            show: () => true
        });
        
        // 注销菜单项
        this.defaultMenuItems.set('logout', {
            sequence: 40,
            Component: this.createLogoutMenuItem(),
            show: () => true
        });
        
        // 注册默认菜单项
        this.registerDefaultMenuItems();
    }
    
    // 注册默认菜单项
    registerDefaultMenuItems() {
        for (const [key, item] of this.defaultMenuItems) {
            this.userMenuRegistry.add(key, (env) => ({
                ...item,
                env: env
            }));
        }
    }
    
    // 创建个人资料菜单项
    createProfileMenuItem() {
        return class ProfileMenuItem extends Component {
            static template = xml`
                <DropdownItem class="'o_user_menu_profile'" onSelected="() => this.openProfile()">
                    <i class="fa fa-user me-2"/>
                    <span>My Profile</span>
                </DropdownItem>
            `;
            
            openProfile() {
                this.env.services.action.doAction({
                    type: 'ir.actions.act_window',
                    res_model: 'res.users',
                    res_id: this.env.services.user.userId,
                    views: [[false, 'form']],
                    target: 'new'
                });
            }
        };
    }
    
    // 创建偏好设置菜单项
    createPreferencesMenuItem() {
        return class PreferencesMenuItem extends Component {
            static template = xml`
                <DropdownItem class="'o_user_menu_preferences'" onSelected="() => this.openPreferences()">
                    <i class="fa fa-cog me-2"/>
                    <span>Preferences</span>
                </DropdownItem>
            `;
            
            openPreferences() {
                this.env.services.action.doAction('base.action_res_users_my');
            }
        };
    }
    
    // 创建我的账户菜单项
    createMyAccountMenuItem() {
        return class MyAccountMenuItem extends Component {
            static template = xml`
                <DropdownItem class="'o_user_menu_account'" onSelected="() => this.openAccount()">
                    <i class="fa fa-user-circle me-2"/>
                    <span>My Account</span>
                </DropdownItem>
            `;
            
            openAccount() {
                this.env.services.action.doAction('base.action_res_users_my');
            }
        };
    }
    
    // 创建帮助菜单项
    createHelpMenuItem() {
        return class HelpMenuItem extends Component {
            static template = xml`
                <DropdownItem class="'o_user_menu_help'" onSelected="() => this.openHelp()">
                    <i class="fa fa-question-circle me-2"/>
                    <span>Help</span>
                </DropdownItem>
            `;
            
            openHelp() {
                window.open('https://www.odoo.com/documentation', '_blank');
            }
        };
    }
    
    // 创建关于菜单项
    createAboutMenuItem() {
        return class AboutMenuItem extends Component {
            static template = xml`
                <DropdownItem class="'o_user_menu_about'" onSelected="() => this.showAbout()">
                    <i class="fa fa-info-circle me-2"/>
                    <span>About</span>
                </DropdownItem>
            `;
            
            showAbout() {
                this.env.services.dialog.add(AboutDialog, {
                    title: 'About Odoo',
                    version: this.env.services.session.server_version,
                    database: this.env.services.session.db
                });
            }
        };
    }
    
    // 创建注销菜单项
    createLogoutMenuItem() {
        return class LogoutMenuItem extends Component {
            static template = xml`
                <DropdownItem class="'o_user_menu_logout'" onSelected="() => this.logout()">
                    <i class="fa fa-sign-out me-2"/>
                    <span>Log out</span>
                </DropdownItem>
            `;
            
            logout() {
                this.env.services.action.doAction('logout');
            }
        };
    }
    
    // 创建用户菜单
    createUserMenu() {
        const menu = new UserMenu();
        
        // 扩展菜单功能
        this.extendUserMenu(menu);
        
        return menu;
    }
    
    // 扩展用户菜单功能
    extendUserMenu(menu) {
        // 添加通知功能
        if (this.menuConfig.enableNotifications) {
            this.addNotificationFeature(menu);
        }
        
        // 添加快速操作
        if (this.menuConfig.enableQuickActions) {
            this.addQuickActionsFeature(menu);
        }
        
        // 添加自定义功能
        if (this.menuConfig.enableCustomization) {
            this.addCustomizationFeature(menu);
        }
    }
    
    // 添加通知功能
    addNotificationFeature(menu) {
        menu.getNotificationCount = function() {
            return this.env.services.notification.getUnreadCount();
        };
        
        menu.hasNotifications = function() {
            return this.getNotificationCount() > 0;
        };
        
        // 扩展模板以显示通知徽章
        const originalTemplate = menu.constructor.template;
        menu.constructor.template = xml`
            ${originalTemplate}
            <div t-if="hasNotifications()" class="o_notification_badge">
                <span t-esc="getNotificationCount()"/>
            </div>
        `;
    }
    
    // 添加快速操作功能
    addQuickActionsFeature(menu) {
        menu.getQuickActions = function() {
            return [
                {
                    name: 'New Record',
                    icon: 'fa-plus',
                    action: () => this.env.services.action.doAction('base.action_res_partner_form')
                },
                {
                    name: 'Search',
                    icon: 'fa-search',
                    action: () => this.env.services.command.openMainPalette()
                },
                {
                    name: 'Calendar',
                    icon: 'fa-calendar',
                    action: () => this.env.services.action.doAction('calendar.action_calendar_event')
                }
            ];
        };
        
        menu.executeQuickAction = function(action) {
            action.action();
        };
    }
    
    // 添加自定义功能
    addCustomizationFeature(menu) {
        menu.getCustomMenuItems = function() {
            try {
                const stored = localStorage.getItem('odoo_user_menu_custom');
                return stored ? JSON.parse(stored) : [];
            } catch (error) {
                console.warn('Failed to get custom menu items:', error);
                return [];
            }
        };
        
        menu.addCustomMenuItem = function(item) {
            const customItems = this.getCustomMenuItems();
            customItems.push({
                id: Date.now().toString(),
                name: item.name,
                icon: item.icon,
                action: item.action,
                sequence: item.sequence || 100
            });
            
            localStorage.setItem('odoo_user_menu_custom', JSON.stringify(customItems));
            
            // 重新渲染菜单
            this.render();
        };
        
        menu.removeCustomMenuItem = function(itemId) {
            const customItems = this.getCustomMenuItems();
            const filtered = customItems.filter(item => item.id !== itemId);
            
            localStorage.setItem('odoo_user_menu_custom', JSON.stringify(filtered));
            
            // 重新渲染菜单
            this.render();
        };
    }
    
    // 注册自定义菜单项
    registerCustomMenuItem(key, itemFactory) {
        this.userMenuRegistry.add(key, itemFactory);
    }
    
    // 注销菜单项
    unregisterMenuItem(key) {
        this.userMenuRegistry.remove(key);
    }
    
    // 获取菜单项
    getMenuItem(key) {
        return this.userMenuRegistry.get(key);
    }
    
    // 获取所有菜单项
    getAllMenuItems() {
        return this.userMenuRegistry.getAll();
    }
    
    // 处理用户信息变化
    handleUserInfoChange(userInfo) {
        // 更新用户相关的菜单项
        this.updateUserRelatedMenuItems(userInfo);
        
        // 触发菜单更新
        this.env.bus.trigger('USER_MENU_UPDATE_REQUIRED', {
            userInfo: userInfo,
            timestamp: Date.now()
        });
    }
    
    // 处理菜单项变化
    handleMenuItemChange(changeData) {
        const { action, key, item } = changeData;
        
        switch (action) {
            case 'add':
                this.registerCustomMenuItem(key, item);
                break;
            case 'remove':
                this.unregisterMenuItem(key);
                break;
            case 'update':
                this.unregisterMenuItem(key);
                this.registerCustomMenuItem(key, item);
                break;
        }
        
        // 触发菜单更新
        this.env.bus.trigger('USER_MENU_ITEMS_CHANGED', {
            action: action,
            key: key,
            timestamp: Date.now()
        });
    }
    
    // 更新用户相关菜单项
    updateUserRelatedMenuItems(userInfo) {
        // 更新个人资料菜单项
        if (userInfo.name) {
            this.updateProfileMenuItem(userInfo);
        }
        
        // 更新权限相关菜单项
        if (userInfo.groups) {
            this.updatePermissionBasedMenuItems(userInfo.groups);
        }
    }
    
    // 更新个人资料菜单项
    updateProfileMenuItem(userInfo) {
        const profileItem = this.getMenuItem('profile');
        if (profileItem) {
            // 更新显示名称等
            profileItem.displayName = userInfo.name;
            profileItem.avatar = userInfo.avatar;
        }
    }
    
    // 更新基于权限的菜单项
    updatePermissionBasedMenuItems(groups) {
        const allItems = this.getAllMenuItems();
        
        allItems.forEach(item => {
            if (item.requiredGroups) {
                const hasPermission = item.requiredGroups.some(group => 
                    groups.includes(group)
                );
                item.visible = hasPermission;
            }
        });
    }
    
    // 获取菜单统计
    getMenuStatistics() {
        const allItems = this.getAllMenuItems();
        
        return {
            totalItems: allItems.length,
            visibleItems: allItems.filter(item => item.show ? item.show() : true).length,
            customItems: this.getCustomMenuItems().length,
            categories: Object.keys(this.menuCategories).length,
            defaultItems: this.defaultMenuItems.size
        };
    }
    
    // 导出菜单配置
    exportMenuConfiguration() {
        const config = {
            exportDate: new Date().toISOString(),
            user: this.env.services.user.userId,
            menuConfig: this.menuConfig,
            customItems: this.getCustomMenuItems(),
            categories: this.menuCategories
        };
        
        const blob = new Blob([JSON.stringify(config, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `user_menu_config_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
    }
    
    // 导入菜单配置
    importMenuConfiguration(configData) {
        try {
            if (configData.customItems) {
                localStorage.setItem('odoo_user_menu_custom', JSON.stringify(configData.customItems));
            }
            
            if (configData.menuConfig) {
                Object.assign(this.menuConfig, configData.menuConfig);
            }
            
            // 重新初始化菜单
            this.setupDefaultMenuItems();
            
            return true;
        } catch (error) {
            console.error('Failed to import menu configuration:', error);
            return false;
        }
    }
    
    // 重置菜单配置
    resetMenuConfiguration() {
        // 清除自定义菜单项
        localStorage.removeItem('odoo_user_menu_custom');
        
        // 重置配置
        this.setupManager();
        
        // 触发重置事件
        this.env.bus.trigger('USER_MENU_RESET', {
            timestamp: Date.now()
        });
    }
    
    // 销毁管理器
    destroy() {
        // 清理事件监听器
        this.env.bus.removeEventListener('USER_INFO_CHANGED');
        this.env.bus.removeEventListener('MENU_ITEM_CHANGED');
        
        // 清理数据
        this.defaultMenuItems.clear();
    }
}

// 使用示例
const userMenuManager = new UserMenuManager(env);

// 创建用户菜单
const userMenu = userMenuManager.createUserMenu();

// 注册自定义菜单项
userMenuManager.registerCustomMenuItem('custom_action', (env) => ({
    sequence: 25,
    Component: CustomActionMenuItem,
    show: () => env.services.user.hasGroup('base.group_system')
}));

// 获取菜单统计
const stats = userMenuManager.getMenuStatistics();
console.log('Menu statistics:', stats);
```

## 技术特点

### 1. 组件设计
- **简洁架构**: 简洁的组件架构设计
- **动态加载**: 动态加载菜单项
- **条件显示**: 支持菜单项的条件显示
- **排序机制**: 基于sequence的排序机制

### 2. 注册表机制
- **菜单注册**: 专门的用户菜单项注册表
- **扩展支持**: 支持第三方模块扩展
- **动态管理**: 动态的菜单项管理
- **环境注入**: 为菜单项注入环境对象

### 3. 用户信息集成
- **用户数据**: 集成用户和会话数据
- **头像显示**: 动态生成用户头像URL
- **缓存控制**: 使用writeDate控制缓存
- **信息展示**: 展示用户名和数据库信息

### 4. 系统集成
- **托盘集成**: 集成到系统托盘
- **优先级**: 设置显示优先级
- **全局可用**: 在整个系统中可用
- **标准接口**: 提供标准的菜单接口

## 设计模式

### 1. 注册表模式 (Registry Pattern)
- **菜单注册**: 菜单项的注册和管理
- **动态加载**: 动态加载注册的菜单项
- **扩展支持**: 支持菜单的扩展

### 2. 工厂模式 (Factory Pattern)
- **菜单创建**: 动态创建菜单项
- **环境注入**: 为菜单项注入环境
- **配置驱动**: 基于配置创建菜单

### 3. 策略模式 (Strategy Pattern)
- **显示策略**: 不同的菜单显示策略
- **排序策略**: 不同的菜单排序策略
- **过滤策略**: 不同的菜单过滤策略

### 4. 观察者模式 (Observer Pattern)
- **状态监听**: 监听用户状态变化
- **动态更新**: 动态更新菜单内容
- **事件通知**: 通知菜单变化

## 注意事项

1. **权限控制**: 确保菜单项的权限控制
2. **性能考虑**: 避免频繁的菜单重建
3. **用户体验**: 提供流畅的菜单交互
4. **扩展性**: 保持良好的扩展性

## 扩展建议

1. **搜索功能**: 添加菜单项搜索功能
2. **自定义**: 支持用户自定义菜单
3. **通知集成**: 集成通知和消息
4. **快速操作**: 添加快速操作功能
5. **主题支持**: 支持不同的主题样式

该用户菜单组件为Odoo Web客户端提供了灵活的用户功能入口，通过注册表机制和动态加载确保了良好的扩展性和用户体验。

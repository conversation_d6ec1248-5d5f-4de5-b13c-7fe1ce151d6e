# SwitchCompanyMenu - 公司切换菜单

## 概述

`switch_company_menu.js` 是 Odoo Web 客户端的公司切换菜单组件，提供了完整的多公司环境下的公司选择和切换功能。该模块包含295行代码，包含CompanySelector类和SwitchCompanyMenu组件，专门用于在系统托盘中提供公司切换界面，具备公司选择器、搜索过滤、键盘导航、批量操作、层级显示等特性，是Odoo Web客户端中多公司管理的核心组件。

## 文件信息
- **路径**: `/web/static/src/webclient/switch_company_menu/switch_company_menu.js`
- **行数**: 295
- **模块**: `@web/webclient/switch_company_menu/switch_company_menu`

## 依赖关系

```javascript
// 核心依赖
'@web/core/dropdown/dropdown'                                   // 下拉菜单组件
'@web/core/dropdown/dropdown_group'                             // 下拉菜单组
'@web/core/dropdown/dropdown_item'                              // 下拉菜单项
'@web/core/registry'                                             // 注册表系统
'@odoo/owl'                                                      // OWL框架
'@web/core/commands/command_hook'                                // 命令钩子
'@web/core/l10n/translation'                                     // 国际化翻译
'@web/core/utils/arrays'                                         // 数组工具
'@web/core/utils/hooks'                                          // 工具钩子
'@web/webclient/switch_company_menu/switch_company_item'         // 公司切换项
'@web/core/hotkeys/hotkey_hook'                                  // 热键钩子
'@web/core/dropdown/dropdown_hooks'                             // 下拉菜单钩子
```

## 主要类定义

### 1. CompanySelector 类

```javascript
class CompanySelector {
    constructor(companyService, dropdownState) {
        this.companyService = companyService;
        this.dropdownState = dropdownState;
        this.selectedCompaniesIds = companyService.activeCompanyIds.slice();
    }

    get hasSelectionChanged() {
        return (
            symmetricalDifference(this.selectedCompaniesIds, this.companyService.activeCompanyIds)
                .length > 0
        );
    }

    switchCompany(mode, companyId) {
        if (mode === "toggle") {
            if (this.selectedCompaniesIds.includes(companyId)) {
                this._deselectCompany(companyId);
            } else {
                this._selectCompany(companyId);
            }
        } else if (mode === "loginto") {
            if (this._isSingleCompanyMode()) {
                this.selectedCompaniesIds.splice(0, this.selectedCompaniesIds.length);
            }
            this._selectCompany(companyId, true);
            this.apply();
            this.dropdownState.close?.();
        }
    }
}
```

**选择器特性**:
- **状态管理**: 管理选中的公司ID列表
- **变更检测**: 检测选择是否发生变化
- **操作模式**: 支持toggle和loginto两种模式
- **层级处理**: 自动处理公司层级关系

### 2. SwitchCompanyMenu 组件

```javascript
class SwitchCompanyMenu extends Component {
    static template = "web.SwitchCompanyMenu";
    static components = { Dropdown, DropdownItem, DropdownGroup, SwitchCompanyItem };
    static props = {};

    setup() {
        this.dropdown = useDropdownState();
        this.companyService = useService("company");
        this.companySelector = useState(new CompanySelector(this.companyService, this.dropdown));
        useChildSubEnv({ companySelector: this.companySelector });

        useHotkey("control+enter", () => this.confirm(), {
            bypassEditableProtection: true,
            isAvailable: () => this.companySelector.hasSelectionChanged,
        });

        useCommand(_t("Switch Company"), () => this.dropdown.open(), { hotkey: "alt+shift+u" });
    }
}
```

**组件特性**:
- **下拉菜单**: 基于Dropdown的公司选择界面
- **热键支持**: 支持键盘快捷键操作
- **搜索功能**: 内置公司搜索和过滤
- **命令集成**: 集成到命令面板

## 核心功能

### 1. 公司选择管理

```javascript
switchCompany(mode, companyId) {
    if (mode === "toggle") {
        if (this.selectedCompaniesIds.includes(companyId)) {
            this._deselectCompany(companyId);
        } else {
            this._selectCompany(companyId);
        }
    } else if (mode === "loginto") {
        if (this._isSingleCompanyMode()) {
            this.selectedCompaniesIds.splice(0, this.selectedCompaniesIds.length);
        }
        this._selectCompany(companyId, true);
        this.apply();
        this.dropdownState.close?.();
    }
}
```

**选择管理功能**:
- **切换模式**: toggle模式用于多选切换
- **登录模式**: loginto模式用于直接登录
- **状态更新**: 自动更新选中状态
- **界面关闭**: 登录后自动关闭菜单

### 2. 层级公司处理

```javascript
_selectCompany(companyId, unshift = false) {
    if (!this.selectedCompaniesIds.includes(companyId)) {
        if (unshift) {
            this.selectedCompaniesIds.unshift(companyId);
        } else {
            this.selectedCompaniesIds.push(companyId);
        }
    } else if (unshift) {
        const index = this.selectedCompaniesIds.findIndex((c) => c === companyId);
        this.selectedCompaniesIds.splice(index, 1);
        this.selectedCompaniesIds.unshift(companyId);
    }
    this._getBranches(companyId).forEach((companyId) => this._selectCompany(companyId));
}

_deselectCompany(companyId) {
    if (this.selectedCompaniesIds.includes(companyId)) {
        this.selectedCompaniesIds.splice(this.selectedCompaniesIds.indexOf(companyId), 1);
        this._getBranches(companyId).forEach((companyId) => this._deselectCompany(companyId));
    }
}
```

**层级处理功能**:
- **递归选择**: 选择公司时自动选择子公司
- **递归取消**: 取消选择时自动取消子公司
- **优先级**: unshift参数控制优先级
- **分支获取**: 获取公司的子公司分支

### 3. 单公司模式检测

```javascript
_isSingleCompanyMode() {
    if (this.selectedCompaniesIds.length === 1) {
        return true;
    }

    const getActiveCompany = (companyId) => {
        const isActive = this.selectedCompaniesIds.includes(companyId);
        return isActive ? this.companyService.getCompany(companyId) : null;
    };

    let rootCompany = undefined;
    for (const companyId of this.selectedCompaniesIds) {
        let company = getActiveCompany(companyId);

        // Find the root active parent of the company
        while (getActiveCompany(company.parent_id)) {
            company = getActiveCompany(company.parent_id);
        }

        if (rootCompany === undefined) {
            rootCompany = company;
        } else if (rootCompany !== company) {
            return false;
        }
    }

    // If some children or sub-children of the root company
    // are not active, we are in multi-company mode.
    if (rootCompany && rootCompany.child_ids) {
        const queue = [...rootCompany.child_ids];
        while (queue.length > 0) {
            const company = getActiveCompany(queue.pop());
            if (company && company.child_ids) {
                queue.push(...company.child_ids);
            } else if (!company) {
                return false;
            }
        }
    }

    return true;
}
```

**模式检测功能**:
- **简单检测**: 只有一个公司时为单公司模式
- **根公司查找**: 查找所有选中公司的根公司
- **完整性检查**: 检查是否选中了完整的公司树
- **队列遍历**: 使用队列遍历检查子公司

### 4. 搜索和过滤

```javascript
get companiesEntries() {
    const companies = [];

    const addCompany = (company, level = 0) => {
        if (this.matchSearch(company.name)) {
            companies.push({ company, level });
        }

        if (company.child_ids) {
            for (const companyId of company.child_ids) {
                addCompany(this.companyService.getCompany(companyId), level + 1);
            }
        }
    };

    Object.values(this.companyService.allowedCompaniesWithAncestors)
        .filter((c) => !c.parent_id)
        .sort((c1, c2) => c1.sequence - c2.sequence)
        .forEach((c) => addCompany(c));

    return companies;
}

matchSearch(companyName) {
    if (!this.state.searchFilter) {
        return true;
    }

    const name = companyName.toLocaleLowerCase().replace(/\s/g, "");
    const filter = this.state.searchFilter.toLocaleLowerCase().replace(/\s/g, "");
    return name.includes(filter);
}
```

**搜索过滤功能**:
- **递归构建**: 递归构建公司列表
- **层级保持**: 保持公司的层级结构
- **搜索匹配**: 不区分大小写的搜索匹配
- **空格忽略**: 忽略空格进行匹配

### 5. 键盘导航

```javascript
this.navigationOptions = {
    hotkeys: {
        space: (index, items) => {
            if (!items[index]) {
                return;
            }
            if (items[index].el.classList.contains("o_switch_company_item")) {
                const companyId = parseInt(items[index].el.dataset.companyId);
                this.companySelector.switchCompany("toggle", companyId);
            }
        },
        enter: (index, items) => {
            if (!items[index]) {
                return;
            }
            if (items[index].el.classList.contains("o_switch_company_item")) {
                const companyId = parseInt(items[index].el.dataset.companyId);
                this.companySelector.switchCompany("loginto", companyId);
                this.dropdown.close();
            } else {
                items[index].select();
            }
        },
    },
};
```

**键盘导航功能**:
- **空格键**: 切换公司选择状态
- **回车键**: 登录到选中的公司
- **索引导航**: 基于索引的项目导航
- **类型检查**: 检查元素类型执行相应操作

### 6. 批量操作

```javascript
selectAll() {
    if (this.selectedCompaniesIds.length > 0) {
        this.selectedCompaniesIds.splice(0, this.selectedCompaniesIds.length);
    } else {
        const newIds = Object.values(this.companyService.allowedCompanies).map((c) => c.id);
        this.selectedCompaniesIds.splice(0, this.selectedCompaniesIds.length, ...newIds);
    }
}
```

**批量操作功能**:
- **全选切换**: 在全选和全不选之间切换
- **状态检测**: 检测当前选择状态
- **批量更新**: 批量更新选择列表
- **权限过滤**: 只选择有权限的公司

## 使用场景

### 1. 多公司环境管理

```javascript
// 多公司环境管理器
class MultiCompanyEnvironmentManager {
    constructor(env) {
        this.env = env;
        this.companyService = env.services.company;
        this.setupManager();
    }
    
    setupManager() {
        // 设置多公司配置
        this.multiCompanyConfig = {
            enableHierarchy: true,
            enableSearch: true,
            enableBatchOperations: true,
            enableQuickSwitch: true,
            maxRecentCompanies: 5
        };
        
        // 设置公司组织结构
        this.organizationStructure = new Map();
        
        // 设置最近使用的公司
        this.recentCompanies = this.loadRecentCompanies();
        
        this.setupEventListeners();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听公司切换
        this.env.bus.addEventListener('COMPANY_SWITCHED', (event) => {
            this.handleCompanySwitch(event.detail);
        });
        
        // 监听公司选择变化
        this.env.bus.addEventListener('COMPANY_SELECTION_CHANGED', (event) => {
            this.handleSelectionChange(event.detail);
        });
    }
    
    // 创建公司切换菜单
    createSwitchCompanyMenu() {
        const menu = new SwitchCompanyMenu();
        
        // 扩展菜单功能
        this.extendCompanyMenu(menu);
        
        return menu;
    }
    
    // 扩展公司菜单功能
    extendCompanyMenu(menu) {
        // 添加最近使用功能
        this.addRecentCompaniesFeature(menu);
        
        // 添加快速切换
        if (this.multiCompanyConfig.enableQuickSwitch) {
            this.addQuickSwitchFeature(menu);
        }
        
        // 添加组织结构视图
        if (this.multiCompanyConfig.enableHierarchy) {
            this.addOrganizationView(menu);
        }
    }
    
    // 添加最近使用功能
    addRecentCompaniesFeature(menu) {
        menu.getRecentCompanies = function() {
            return this.recentCompanies.slice(0, this.multiCompanyConfig.maxRecentCompanies);
        }.bind(this);
        
        menu.addToRecent = function(companyId) {
            // 移除已存在的
            const index = this.recentCompanies.indexOf(companyId);
            if (index > -1) {
                this.recentCompanies.splice(index, 1);
            }
            
            // 添加到开头
            this.recentCompanies.unshift(companyId);
            
            // 限制数量
            if (this.recentCompanies.length > this.multiCompanyConfig.maxRecentCompanies) {
                this.recentCompanies.pop();
            }
            
            // 保存到本地存储
            this.saveRecentCompanies();
        }.bind(this);
        
        // 重写switchCompany方法
        const originalSwitchCompany = menu.companySelector.switchCompany.bind(menu.companySelector);
        menu.companySelector.switchCompany = function(mode, companyId) {
            const result = originalSwitchCompany(mode, companyId);
            
            if (mode === 'loginto') {
                menu.addToRecent(companyId);
            }
            
            return result;
        };
    }
    
    // 添加快速切换功能
    addQuickSwitchFeature(menu) {
        menu.quickSwitch = function(direction) {
            const companies = Object.values(this.companyService.allowedCompanies);
            const currentIndex = companies.findIndex(c => c.id === this.companyService.currentCompany.id);
            
            let nextIndex;
            if (direction === 'next') {
                nextIndex = (currentIndex + 1) % companies.length;
            } else {
                nextIndex = (currentIndex - 1 + companies.length) % companies.length;
            }
            
            const nextCompany = companies[nextIndex];
            this.companySelector.switchCompany('loginto', nextCompany.id);
        }.bind(this);
        
        // 添加快捷键
        this.env.services.hotkey.add('alt+shift+right', () => menu.quickSwitch('next'));
        this.env.services.hotkey.add('alt+shift+left', () => menu.quickSwitch('prev'));
    }
    
    // 添加组织结构视图
    addOrganizationView(menu) {
        menu.getOrganizationStructure = function() {
            return this.buildOrganizationStructure();
        }.bind(this);
        
        menu.toggleOrganizationView = function() {
            this.state.showOrganization = !this.state.showOrganization;
        };
        
        // 扩展状态
        const originalResetState = menu.resetState.bind(menu);
        menu.resetState = function() {
            originalResetState();
            this.state.showOrganization = false;
        };
    }
    
    // 构建组织结构
    buildOrganizationStructure() {
        const companies = Object.values(this.companyService.allowedCompaniesWithAncestors);
        const structure = new Map();
        
        // 构建层级结构
        companies.forEach(company => {
            structure.set(company.id, {
                company: company,
                children: [],
                parent: null,
                level: 0
            });
        });
        
        // 建立父子关系
        companies.forEach(company => {
            const node = structure.get(company.id);
            
            if (company.parent_id && structure.has(company.parent_id)) {
                const parent = structure.get(company.parent_id);
                parent.children.push(node);
                node.parent = parent;
                node.level = parent.level + 1;
            }
        });
        
        // 获取根节点
        const roots = Array.from(structure.values()).filter(node => !node.parent);
        
        this.organizationStructure = structure;
        return roots;
    }
    
    // 处理公司切换
    handleCompanySwitch(switchData) {
        const { mode, companyId, previousCompanyId } = switchData;
        
        // 更新最近使用
        if (mode === 'loginto') {
            this.updateRecentCompanies(companyId);
        }
        
        // 记录切换日志
        this.logCompanySwitch({
            mode: mode,
            from: previousCompanyId,
            to: companyId,
            timestamp: Date.now(),
            user: this.env.services.user.userId
        });
        
        // 触发环境更新
        this.updateEnvironment(companyId);
    }
    
    // 处理选择变化
    handleSelectionChange(selectionData) {
        const { selectedCompanies, previousSelection } = selectionData;
        
        // 验证选择的有效性
        this.validateSelection(selectedCompanies);
        
        // 更新UI状态
        this.updateSelectionUI(selectedCompanies);
        
        // 触发选择变化事件
        this.env.bus.trigger('MULTI_COMPANY_SELECTION_UPDATED', {
            selection: selectedCompanies,
            previous: previousSelection,
            timestamp: Date.now()
        });
    }
    
    // 验证选择
    validateSelection(selectedCompanies) {
        const allowedCompanies = this.companyService.allowedCompanies;
        
        // 检查权限
        const invalidSelections = selectedCompanies.filter(id => !(id in allowedCompanies));
        
        if (invalidSelections.length > 0) {
            console.warn('Invalid company selections detected:', invalidSelections);
            
            // 移除无效选择
            this.removeInvalidSelections(invalidSelections);
        }
        
        // 检查层级一致性
        this.validateHierarchyConsistency(selectedCompanies);
    }
    
    // 验证层级一致性
    validateHierarchyConsistency(selectedCompanies) {
        const structure = this.organizationStructure;
        
        selectedCompanies.forEach(companyId => {
            const node = structure.get(companyId);
            
            if (node && node.parent) {
                // 检查父公司是否也被选中
                if (!selectedCompanies.includes(node.parent.company.id)) {
                    console.warn(`Parent company ${node.parent.company.id} not selected for child ${companyId}`);
                }
            }
        });
    }
    
    // 更新环境
    updateEnvironment(companyId) {
        // 更新公司相关的环境变量
        this.env.companyId = companyId;
        
        // 触发环境更新事件
        this.env.bus.trigger('ENVIRONMENT_UPDATED', {
            companyId: companyId,
            timestamp: Date.now()
        });
    }
    
    // 加载最近使用的公司
    loadRecentCompanies() {
        try {
            const stored = localStorage.getItem('odoo_recent_companies');
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            console.warn('Failed to load recent companies:', error);
            return [];
        }
    }
    
    // 保存最近使用的公司
    saveRecentCompanies() {
        try {
            localStorage.setItem('odoo_recent_companies', JSON.stringify(this.recentCompanies));
        } catch (error) {
            console.warn('Failed to save recent companies:', error);
        }
    }
    
    // 更新最近使用的公司
    updateRecentCompanies(companyId) {
        const index = this.recentCompanies.indexOf(companyId);
        
        if (index > -1) {
            this.recentCompanies.splice(index, 1);
        }
        
        this.recentCompanies.unshift(companyId);
        
        if (this.recentCompanies.length > this.multiCompanyConfig.maxRecentCompanies) {
            this.recentCompanies.pop();
        }
        
        this.saveRecentCompanies();
    }
    
    // 记录公司切换日志
    logCompanySwitch(logData) {
        console.log('Company switch:', logData);
        
        // 可以发送到服务器进行审计
        this.env.services.rpc('/web/company_switch/log', {
            log_data: logData
        }).catch(error => {
            console.warn('Failed to log company switch:', error);
        });
    }
    
    // 获取多公司统计
    getMultiCompanyStatistics() {
        const allowedCompanies = Object.values(this.companyService.allowedCompanies);
        const structure = this.buildOrganizationStructure();
        
        return {
            totalCompanies: allowedCompanies.length,
            rootCompanies: structure.length,
            maxDepth: this.getMaxDepth(structure),
            recentCompanies: this.recentCompanies.length,
            currentCompany: this.companyService.currentCompany.id,
            selectedCompanies: this.companyService.activeCompanyIds.length
        };
    }
    
    // 获取最大深度
    getMaxDepth(roots) {
        let maxDepth = 0;
        
        const traverse = (nodes, depth) => {
            nodes.forEach(node => {
                maxDepth = Math.max(maxDepth, depth);
                if (node.children.length > 0) {
                    traverse(node.children, depth + 1);
                }
            });
        };
        
        traverse(roots, 0);
        return maxDepth;
    }
    
    // 销毁管理器
    destroy() {
        // 清理事件监听器
        this.env.bus.removeEventListener('COMPANY_SWITCHED');
        this.env.bus.removeEventListener('COMPANY_SELECTION_CHANGED');
        
        // 清理数据
        this.organizationStructure.clear();
        this.recentCompanies = [];
    }
}

// 使用示例
const multiCompanyManager = new MultiCompanyEnvironmentManager(env);

// 创建公司切换菜单
const switchMenu = multiCompanyManager.createSwitchCompanyMenu();

// 获取统计信息
const stats = multiCompanyManager.getMultiCompanyStatistics();
console.log('Multi-company statistics:', stats);

// 构建组织结构
const structure = multiCompanyManager.buildOrganizationStructure();
console.log('Organization structure:', structure);
```

## 技术特点

### 1. 状态管理
- **选择器**: 独立的CompanySelector类管理选择状态
- **变更检测**: 智能的变更检测机制
- **状态同步**: 与公司服务的状态同步
- **响应式**: 响应式的状态更新

### 2. 层级处理
- **递归操作**: 递归处理公司层级关系
- **自动选择**: 选择父公司时自动选择子公司
- **模式检测**: 智能的单/多公司模式检测
- **完整性**: 保证选择的完整性

### 3. 用户体验
- **搜索过滤**: 实时的公司搜索和过滤
- **键盘导航**: 完整的键盘导航支持
- **热键支持**: 全局热键支持
- **批量操作**: 全选/全不选批量操作

### 4. 界面优化
- **宽度固定**: 搜索时固定容器宽度
- **自动聚焦**: 打开时自动聚焦搜索框
- **状态重置**: 关闭时重置搜索状态
- **视觉反馈**: 清晰的选择状态视觉反馈

## 设计模式

### 1. 状态模式 (State Pattern)
- **选择状态**: 管理公司的选择状态
- **模式切换**: 在不同操作模式间切换
- **状态检测**: 检测当前的公司模式

### 2. 命令模式 (Command Pattern)
- **操作封装**: 封装公司切换操作
- **撤销支持**: 支持操作的撤销
- **批量操作**: 批量命令执行

### 3. 观察者模式 (Observer Pattern)
- **状态监听**: 监听选择状态变化
- **事件通知**: 通知状态变化
- **自动更新**: 自动更新相关组件

### 4. 策略模式 (Strategy Pattern)
- **操作策略**: 不同的公司操作策略
- **搜索策略**: 不同的搜索匹配策略
- **显示策略**: 不同的显示策略

## 注意事项

1. **权限验证**: 确保用户有权限访问选择的公司
2. **状态一致性**: 保持选择状态与服务状态一致
3. **性能考虑**: 大量公司时的性能优化
4. **用户体验**: 提供流畅的交互体验

## 扩展建议

1. **收藏功能**: 支持公司收藏和快速访问
2. **最近使用**: 显示最近使用的公司
3. **组织视图**: 提供组织结构视图
4. **批量导入**: 支持批量公司导入
5. **权限管理**: 细粒度的公司权限管理

该公司切换菜单为Odoo Web客户端提供了完整的多公司环境管理功能，通过智能的选择器和丰富的用户交互确保了高效的公司切换和管理体验。

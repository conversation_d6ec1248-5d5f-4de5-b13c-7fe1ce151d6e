# SwitchCompanyItem - 公司切换项组件

## 概述

`switch_company_item.js` 是 Odoo Web 客户端的公司切换项组件，提供了单个公司选项的显示和交互功能。该模块包含47行代码，是一个OWL组件，专门用于在公司切换菜单中显示单个公司项，具备公司状态检查、权限验证、切换操作、层级显示等特性，是Odoo Web客户端中多公司环境下公司管理的基础组件。

## 文件信息
- **路径**: `/web/static/src/webclient/switch_company_menu/switch_company_item.js`
- **行数**: 47
- **模块**: `@web/webclient/switch_company_menu/switch_company_item`

## 依赖关系

```javascript
// 核心依赖
'@web/core/dropdown/dropdown_item'     // 下拉菜单项组件
'@odoo/owl'                            // OWL框架
'@web/core/utils/hooks'                // 工具钩子
```

## 主组件定义

### 1. SwitchCompanyItem 组件

```javascript
class SwitchCompanyItem extends Component {
    static template = "web.SwitchCompanyItem";
    static components = { DropdownItem, SwitchCompanyItem };
    static props = {
        company: {},
        level: { type: Number },
    };

    setup() {
        this.companyService = useService("company");
        this.companySelector = useState(this.env.companySelector);
    }
}
```

**组件特性**:
- **下拉项**: 基于DropdownItem的公司选择项
- **递归组件**: 支持自身递归用于层级显示
- **服务集成**: 集成公司服务和公司选择器
- **状态管理**: 响应式的公司状态管理

## 核心属性

### 1. 组件属性

```javascript
// 模板配置
static template = "web.SwitchCompanyItem";

// 子组件
static components = { DropdownItem, SwitchCompanyItem };

// 属性定义
static props = {
    company: {},           // 公司对象
    level: { type: Number }, // 层级深度
};
```

**属性功能**:
- **template**: 指定公司切换项的模板
- **components**: 包含DropdownItem和自身递归组件
- **company**: 公司数据对象
- **level**: 显示层级，用于缩进和样式

### 2. 服务属性

```javascript
// 公司服务
this.companyService = useService("company");

// 公司选择器状态
this.companySelector = useState(this.env.companySelector);
```

**服务属性功能**:
- **companyService**: 公司管理服务
- **companySelector**: 公司选择器状态管理

## 核心功能

### 1. 公司选择状态检查

```javascript
get isCompanySelected() {
    return this.companySelector.isCompanySelected(this.props.company.id);
}
```

**检查功能**:
- **选择状态**: 检查公司是否被选中
- **ID匹配**: 通过公司ID进行匹配
- **状态返回**: 返回布尔值表示选择状态
- **响应式**: 响应公司选择器状态变化

### 2. 公司权限检查

```javascript
get isCompanyAllowed() {
    return this.props.company.id in this.companyService.allowedCompanies;
}
```

**权限检查功能**:
- **权限验证**: 验证用户是否有权限访问该公司
- **允许列表**: 检查公司是否在允许的公司列表中
- **安全控制**: 提供安全的访问控制
- **ID验证**: 通过公司ID进行权限验证

### 3. 当前公司检查

```javascript
get isCurrent() {
    return this.props.company.id === this.companyService.currentCompany.id;
}
```

**当前状态检查功能**:
- **当前状态**: 检查是否为当前活动公司
- **ID比较**: 比较公司ID确定当前状态
- **视觉标识**: 用于显示当前公司的视觉标识
- **状态区分**: 区分当前公司和其他公司

### 4. 登录公司操作

```javascript
logIntoCompany() {
    if (this.isCompanyAllowed) {
        this.companySelector.switchCompany("loginto", this.props.company.id);
    }
}
```

**登录操作功能**:
- **权限检查**: 先检查是否有权限
- **登录切换**: 执行登录到指定公司的操作
- **操作类型**: 使用"loginto"操作类型
- **安全操作**: 只在有权限时执行操作

### 5. 切换公司操作

```javascript
toggleCompany() {
    if (this.isCompanyAllowed) {
        this.companySelector.switchCompany("toggle", this.props.company.id);
    }
}
```

**切换操作功能**:
- **权限检查**: 先检查是否有权限
- **切换操作**: 执行公司的切换操作
- **操作类型**: 使用"toggle"操作类型
- **状态切换**: 在选中和未选中之间切换

## 使用场景

### 1. 公司切换管理器

```javascript
// 公司切换管理器
class CompanySwitchManager {
    constructor(env) {
        this.env = env;
        this.companyService = env.services.company;
        this.setupManager();
    }
    
    setupManager() {
        // 设置切换配置
        this.switchConfig = {
            enableMultiSelect: true,
            enableHierarchy: true,
            enableSearch: true,
            enableFavorites: false,
            maxSelections: 10
        };
        
        // 设置公司层级
        this.companyHierarchy = new Map();
        
        // 设置切换历史
        this.switchHistory = [];
        
        this.setupEventListeners();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听公司切换
        this.env.bus.addEventListener('COMPANY_SWITCHED', (event) => {
            this.handleCompanySwitch(event.detail);
        });
        
        // 监听权限变化
        this.env.bus.addEventListener('PERMISSIONS_CHANGED', (event) => {
            this.handlePermissionsChange(event.detail);
        });
    }
    
    // 创建公司切换项
    createSwitchCompanyItem(company, level = 0) {
        const item = new SwitchCompanyItem();
        item.props = {
            company: company,
            level: level
        };
        
        // 扩展项功能
        this.extendCompanyItem(item);
        
        return item;
    }
    
    // 扩展公司项功能
    extendCompanyItem(item) {
        // 添加收藏功能
        if (this.switchConfig.enableFavorites) {
            this.addFavoriteFeature(item);
        }
        
        // 添加搜索高亮
        if (this.switchConfig.enableSearch) {
            this.addSearchHighlight(item);
        }
        
        // 添加快捷键
        this.addKeyboardShortcuts(item);
    }
    
    // 添加收藏功能
    addFavoriteFeature(item) {
        item.isFavorite = function() {
            return this.getFavoriteCompanies().includes(this.props.company.id);
        };
        
        item.toggleFavorite = function() {
            const favorites = this.getFavoriteCompanies();
            const companyId = this.props.company.id;
            
            if (favorites.includes(companyId)) {
                this.removeFavorite(companyId);
            } else {
                this.addFavorite(companyId);
            }
        };
        
        item.getFavoriteCompanies = function() {
            try {
                const stored = localStorage.getItem('odoo_favorite_companies');
                return stored ? JSON.parse(stored) : [];
            } catch (error) {
                console.warn('Failed to get favorite companies:', error);
                return [];
            }
        };
        
        item.addFavorite = function(companyId) {
            const favorites = this.getFavoriteCompanies();
            if (!favorites.includes(companyId)) {
                favorites.push(companyId);
                localStorage.setItem('odoo_favorite_companies', JSON.stringify(favorites));
                
                this.env.bus.trigger('FAVORITE_COMPANY_ADDED', {
                    companyId: companyId,
                    timestamp: Date.now()
                });
            }
        };
        
        item.removeFavorite = function(companyId) {
            const favorites = this.getFavoriteCompanies();
            const index = favorites.indexOf(companyId);
            
            if (index > -1) {
                favorites.splice(index, 1);
                localStorage.setItem('odoo_favorite_companies', JSON.stringify(favorites));
                
                this.env.bus.trigger('FAVORITE_COMPANY_REMOVED', {
                    companyId: companyId,
                    timestamp: Date.now()
                });
            }
        };
    }
    
    // 添加搜索高亮
    addSearchHighlight(item) {
        item.getHighlightedName = function(searchTerm) {
            if (!searchTerm) return this.props.company.name;
            
            const name = this.props.company.name;
            const regex = new RegExp(`(${this.escapeRegExp(searchTerm)})`, 'gi');
            
            return name.replace(regex, '<mark>$1</mark>');
        };
        
        item.escapeRegExp = function(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        };
        
        item.matchesSearch = function(searchTerm) {
            if (!searchTerm) return true;
            
            const company = this.props.company;
            const searchLower = searchTerm.toLowerCase();
            
            return company.name.toLowerCase().includes(searchLower) ||
                   (company.code && company.code.toLowerCase().includes(searchLower)) ||
                   (company.email && company.email.toLowerCase().includes(searchLower));
        };
    }
    
    // 添加键盘快捷键
    addKeyboardShortcuts(item) {
        item.handleKeyDown = function(event) {
            switch (event.key) {
                case 'Enter':
                case ' ':
                    event.preventDefault();
                    this.logIntoCompany();
                    break;
                case 't':
                case 'T':
                    if (event.ctrlKey || event.metaKey) {
                        event.preventDefault();
                        this.toggleCompany();
                    }
                    break;
                case 'f':
                case 'F':
                    if (event.ctrlKey || event.metaKey) {
                        event.preventDefault();
                        this.toggleFavorite?.();
                    }
                    break;
            }
        };
        
        // 绑定键盘事件
        const originalSetup = item.setup.bind(item);
        item.setup = function() {
            originalSetup();
            
            onMounted(() => {
                this.el.addEventListener('keydown', this.handleKeyDown.bind(this));
            });
            
            onWillUnmount(() => {
                this.el.removeEventListener('keydown', this.handleKeyDown.bind(this));
            });
        };
    }
    
    // 构建公司层级
    buildCompanyHierarchy(companies) {
        const hierarchy = new Map();
        const roots = [];
        
        // 第一遍：创建所有节点
        companies.forEach(company => {
            hierarchy.set(company.id, {
                company: company,
                children: [],
                parent: null,
                level: 0
            });
        });
        
        // 第二遍：建立父子关系
        companies.forEach(company => {
            const node = hierarchy.get(company.id);
            
            if (company.parent_id && hierarchy.has(company.parent_id)) {
                const parent = hierarchy.get(company.parent_id);
                parent.children.push(node);
                node.parent = parent;
                node.level = parent.level + 1;
            } else {
                roots.push(node);
            }
        });
        
        this.companyHierarchy = hierarchy;
        return roots;
    }
    
    // 获取公司路径
    getCompanyPath(companyId) {
        const path = [];
        let current = this.companyHierarchy.get(companyId);
        
        while (current) {
            path.unshift(current.company);
            current = current.parent;
        }
        
        return path;
    }
    
    // 处理公司切换
    handleCompanySwitch(switchData) {
        const { action, companyId, previousCompanyId } = switchData;
        
        // 记录切换历史
        this.switchHistory.push({
            action: action,
            companyId: companyId,
            previousCompanyId: previousCompanyId,
            timestamp: Date.now(),
            user: this.env.services.user.userId
        });
        
        // 限制历史记录数量
        if (this.switchHistory.length > 100) {
            this.switchHistory.shift();
        }
        
        // 触发切换完成事件
        this.env.bus.trigger('COMPANY_SWITCH_COMPLETED', {
            action: action,
            companyId: companyId,
            path: this.getCompanyPath(companyId),
            timestamp: Date.now()
        });
    }
    
    // 处理权限变化
    handlePermissionsChange(permissionData) {
        // 重新验证所有公司项的权限
        this.validateAllCompanyPermissions();
        
        // 触发权限更新事件
        this.env.bus.trigger('COMPANY_PERMISSIONS_UPDATED', {
            permissions: permissionData,
            timestamp: Date.now()
        });
    }
    
    // 验证所有公司权限
    validateAllCompanyPermissions() {
        const allowedCompanies = this.companyService.allowedCompanies;
        
        // 检查当前选中的公司是否仍然有权限
        const selectedCompanies = this.getSelectedCompanies();
        const invalidSelections = selectedCompanies.filter(id => !(id in allowedCompanies));
        
        if (invalidSelections.length > 0) {
            // 移除无效的选择
            this.removeInvalidSelections(invalidSelections);
            
            // 通知用户
            this.env.services.notification.add(
                `Access to ${invalidSelections.length} companies has been revoked`,
                { type: 'warning' }
            );
        }
    }
    
    // 获取选中的公司
    getSelectedCompanies() {
        return this.env.companySelector.getSelectedCompanies();
    }
    
    // 移除无效选择
    removeInvalidSelections(invalidIds) {
        invalidIds.forEach(id => {
            this.env.companySelector.switchCompany("toggle", id);
        });
    }
    
    // 获取切换统计
    getSwitchStatistics() {
        const stats = {
            totalSwitches: this.switchHistory.length,
            uniqueCompanies: new Set(this.switchHistory.map(h => h.companyId)).size,
            mostUsedCompany: this.getMostUsedCompany(),
            recentSwitches: this.switchHistory.slice(-10),
            switchesByAction: this.groupSwitchesByAction()
        };
        
        return stats;
    }
    
    // 获取最常用公司
    getMostUsedCompany() {
        const companyCounts = {};
        
        this.switchHistory.forEach(history => {
            companyCounts[history.companyId] = (companyCounts[history.companyId] || 0) + 1;
        });
        
        let mostUsed = null;
        let maxCount = 0;
        
        Object.entries(companyCounts).forEach(([companyId, count]) => {
            if (count > maxCount) {
                maxCount = count;
                mostUsed = companyId;
            }
        });
        
        return mostUsed ? {
            companyId: mostUsed,
            count: maxCount,
            company: this.companyService.allowedCompanies[mostUsed]
        } : null;
    }
    
    // 按操作分组切换
    groupSwitchesByAction() {
        const groups = {};
        
        this.switchHistory.forEach(history => {
            if (!groups[history.action]) {
                groups[history.action] = 0;
            }
            groups[history.action]++;
        });
        
        return groups;
    }
    
    // 导出切换历史
    exportSwitchHistory() {
        const exportData = {
            exportDate: new Date().toISOString(),
            user: this.env.services.user.userId,
            statistics: this.getSwitchStatistics(),
            history: this.switchHistory,
            config: this.switchConfig
        };
        
        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `company_switch_history_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
    }
    
    // 清理历史记录
    clearSwitchHistory() {
        this.switchHistory = [];
        
        this.env.bus.trigger('SWITCH_HISTORY_CLEARED', {
            timestamp: Date.now()
        });
    }
    
    // 销毁管理器
    destroy() {
        // 清理事件监听器
        this.env.bus.removeEventListener('COMPANY_SWITCHED');
        this.env.bus.removeEventListener('PERMISSIONS_CHANGED');
        
        // 清理历史记录
        this.clearSwitchHistory();
        
        // 清理层级数据
        this.companyHierarchy.clear();
    }
}

// 使用示例
const switchManager = new CompanySwitchManager(env);

// 创建公司切换项
const companyItem = switchManager.createSwitchCompanyItem({
    id: 1,
    name: 'Main Company',
    code: 'MAIN'
}, 0);

// 获取切换统计
const stats = switchManager.getSwitchStatistics();
console.log('Switch statistics:', stats);

// 构建公司层级
const hierarchy = switchManager.buildCompanyHierarchy(companies);
console.log('Company hierarchy:', hierarchy);
```

## 技术特点

### 1. 组件设计
- **递归组件**: 支持自身递归用于层级显示
- **属性驱动**: 通过属性控制组件行为
- **状态响应**: 响应式的状态管理
- **服务集成**: 深度集成公司相关服务

### 2. 权限控制
- **权限检查**: 严格的权限验证机制
- **安全操作**: 只在有权限时执行操作
- **状态区分**: 区分不同的公司状态
- **访问控制**: 基于允许列表的访问控制

### 3. 交互设计
- **多种操作**: 支持登录和切换两种操作
- **状态反馈**: 清晰的状态视觉反馈
- **用户体验**: 流畅的用户交互体验
- **操作安全**: 安全的操作执行机制

### 4. 层级支持
- **层级显示**: 支持公司层级结构显示
- **缩进控制**: 通过level属性控制缩进
- **递归渲染**: 递归渲染子公司
- **结构清晰**: 清晰的层级结构展示

## 设计模式

### 1. 组件模式 (Component Pattern)
- **单一职责**: 专注于单个公司项的显示和交互
- **可复用**: 高度可复用的组件设计
- **组合使用**: 可与其他组件组合使用

### 2. 状态模式 (State Pattern)
- **状态管理**: 管理公司的不同状态
- **状态检查**: 检查选择、权限、当前等状态
- **状态响应**: 响应状态变化

### 3. 命令模式 (Command Pattern)
- **操作封装**: 封装公司切换操作
- **操作类型**: 不同类型的操作命令
- **安全执行**: 安全的命令执行

### 4. 策略模式 (Strategy Pattern)
- **操作策略**: 不同的公司操作策略
- **显示策略**: 不同的显示策略
- **权限策略**: 不同的权限检查策略

## 注意事项

1. **权限验证**: 确保所有操作都进行权限验证
2. **状态同步**: 保持组件状态与服务状态同步
3. **用户体验**: 提供清晰的状态反馈
4. **性能考虑**: 避免不必要的状态检查

## 扩展建议

1. **搜索功能**: 添加公司搜索和过滤功能
2. **收藏功能**: 支持公司收藏和快速访问
3. **键盘导航**: 支持键盘快捷键操作
4. **拖拽排序**: 支持公司项的拖拽排序
5. **批量操作**: 支持批量公司操作

该公司切换项组件为Odoo Web客户端提供了灵活的单个公司选项管理功能，通过权限控制和状态管理确保了安全的公司切换操作和良好的用户体验。

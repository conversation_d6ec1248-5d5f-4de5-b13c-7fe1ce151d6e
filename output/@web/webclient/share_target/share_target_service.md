# ShareTargetService - 分享目标服务

## 概述

`share_target_service.js` 是 Odoo Web 客户端的分享目标服务，提供了PWA应用接收外部分享内容的功能。该模块包含70行代码，是一个服务模块，专门用于处理通过Web Share Target API分享到Odoo应用的文件和内容，具备Service Worker通信、文件接收、应用导航、状态管理等特性，是Odoo Web客户端中PWA分享功能的核心实现。

## 文件信息
- **路径**: `/web/static/src/webclient/share_target/share_target_service.js`
- **行数**: 70
- **模块**: `@web/webclient/share_target/share_target_service`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                // 注册表系统
'@web/core/browser/browser'         // 浏览器工具
```

## 主要功能

### 1. getShareTargetDataFromServiceWorker - 从Service Worker获取分享数据

```javascript
const getShareTargetDataFromServiceWorker = () => {
    return new Promise((resolve) => {
        const onmessage = (event) => {
            if (event.data.action === "odoo_share_target_ack") {
                resolve(event.data.shared_files);
                browser.navigator.serviceWorker.removeEventListener("message", onmessage);
            }
        };
        browser.navigator.serviceWorker.addEventListener("message", onmessage);
        browser.navigator.serviceWorker.controller.postMessage("odoo_share_target");
    });
};
```

**数据获取功能**:
- **Promise封装**: 使用Promise封装异步通信
- **消息监听**: 监听Service Worker的消息
- **确认机制**: 等待"odoo_share_target_ack"确认消息
- **数据解析**: 解析分享的文件数据
- **清理监听**: 完成后清理事件监听器

**返回数据结构**:
```typescript
{
    title: string,              // 分享的标题
    text: string,               // 分享的文本内容
    url: string,                // 分享的URL
    externalMediaFiles: File[]  // 分享的媒体文件
}
```

### 2. shareTargetService - 分享目标服务

```javascript
const shareTargetService = {
    dependencies: ["menu"],
    start(env, { menu }) {
        let sharedFiles = null;
        if (
            browser.navigator.serviceWorker &&
            new URL(browser.location).searchParams.get("share_target") === "trigger"
        ) {
            const app = menu.getApps().find((app) => "expenses" === app.actionPath);
            if (app) {
                const clientReadyListener = async () => {
                    sharedFiles = await getShareTargetDataFromServiceWorker();
                    if (sharedFiles?.length) {
                        await menu.selectMenu(app);
                    }
                    env.bus.removeEventListener("WEB_CLIENT_READY", clientReadyListener);
                };
                env.bus.addEventListener("WEB_CLIENT_READY", clientReadyListener);
            }
        }
        return {
            hasSharedFiles: () => !!sharedFiles?.length,
            getSharedFilesToUpload: () => {
                const files = sharedFiles;
                sharedFiles = null;
                return files;
            },
        };
    },
};
```

**服务功能**:
- **依赖注入**: 依赖菜单服务
- **条件检查**: 检查Service Worker和分享触发条件
- **应用查找**: 查找费用应用
- **客户端就绪**: 等待Web客户端就绪
- **自动导航**: 自动导航到目标应用

## 核心功能

### 1. 分享触发检测

```javascript
if (
    browser.navigator.serviceWorker &&
    new URL(browser.location).searchParams.get("share_target") === "trigger"
) {
    // 处理分享触发
}
```

**检测功能**:
- **Service Worker**: 检查Service Worker支持
- **URL参数**: 检查share_target=trigger参数
- **条件判断**: 只在满足条件时处理分享
- **环境验证**: 验证PWA环境

### 2. 目标应用查找

```javascript
const app = menu.getApps().find((app) => "expenses" === app.actionPath);
```

**查找功能**:
- **应用列表**: 从菜单服务获取应用列表
- **路径匹配**: 匹配"expenses"应用路径
- **目标确定**: 确定分享的目标应用
- **应用验证**: 验证应用存在性

### 3. 客户端就绪处理

```javascript
const clientReadyListener = async () => {
    sharedFiles = await getShareTargetDataFromServiceWorker();
    if (sharedFiles?.length) {
        await menu.selectMenu(app);
    }
    env.bus.removeEventListener("WEB_CLIENT_READY", clientReadyListener);
};
env.bus.addEventListener("WEB_CLIENT_READY", clientReadyListener);
```

**就绪处理功能**:
- **异步处理**: 异步处理客户端就绪事件
- **数据获取**: 获取分享的文件数据
- **条件导航**: 有文件时才导航到应用
- **事件清理**: 处理完成后清理事件监听

### 4. 服务接口

```javascript
return {
    hasSharedFiles: () => !!sharedFiles?.length,
    getSharedFilesToUpload: () => {
        const files = sharedFiles;
        sharedFiles = null;
        return files;
    },
};
```

**接口功能**:
- **hasSharedFiles**: 检查是否有分享的文件
- **getSharedFilesToUpload**: 获取并清空分享的文件
- **一次性消费**: 文件只能获取一次
- **状态管理**: 管理分享文件的状态

### 5. 服务注册

```javascript
registry.category("services").add("shareTarget", shareTargetService);
```

**注册功能**:
- **服务类别**: 注册到服务类别
- **全局可用**: 在整个应用中可用
- **依赖管理**: 管理服务依赖
- **生命周期**: 与应用生命周期绑定

## 使用场景

### 1. 分享目标管理器

```javascript
// 分享目标管理器
class ShareTargetManager {
    constructor(env) {
        this.env = env;
        this.setupManager();
    }
    
    setupManager() {
        // 设置分享配置
        this.shareConfig = {
            enableFileSharing: true,
            enableTextSharing: true,
            enableUrlSharing: true,
            maxFileSize: 10 * 1024 * 1024, // 10MB
            allowedFileTypes: ['image/*', 'application/pdf', 'text/*'],
            targetApps: ['expenses', 'documents', 'project']
        };
        
        // 设置分享处理器
        this.shareHandlers = new Map();
        
        // 设置分享历史
        this.shareHistory = [];
        
        this.setupShareHandlers();
        this.setupEventListeners();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听分享事件
        this.env.bus.addEventListener('SHARE_TARGET_RECEIVED', (event) => {
            this.handleShareReceived(event.detail);
        });
        
        // 监听文件处理完成
        this.env.bus.addEventListener('SHARE_FILES_PROCESSED', (event) => {
            this.handleFilesProcessed(event.detail);
        });
    }
    
    // 设置分享处理器
    setupShareHandlers() {
        // 费用应用处理器
        this.shareHandlers.set('expenses', {
            name: 'Expenses',
            icon: 'fa-receipt',
            handler: this.handleExpensesShare.bind(this),
            supportedTypes: ['image/*', 'application/pdf'],
            description: 'Share receipts and expense documents'
        });
        
        // 文档应用处理器
        this.shareHandlers.set('documents', {
            name: 'Documents',
            icon: 'fa-file',
            handler: this.handleDocumentsShare.bind(this),
            supportedTypes: ['*/*'],
            description: 'Share any type of document'
        });
        
        // 项目应用处理器
        this.shareHandlers.set('project', {
            name: 'Project',
            icon: 'fa-project-diagram',
            handler: this.handleProjectShare.bind(this),
            supportedTypes: ['image/*', 'text/*'],
            description: 'Share project-related files and notes'
        });
    }
    
    // 创建增强的分享目标服务
    createEnhancedShareTargetService() {
        const originalService = shareTargetService;
        
        return {
            ...originalService,
            start: (env, dependencies) => {
                const baseService = originalService.start(env, dependencies);
                
                // 扩展基础服务
                return {
                    ...baseService,
                    
                    // 获取分享数据（包含元数据）
                    getSharedData: () => {
                        const files = baseService.getSharedFilesToUpload();
                        
                        if (files) {
                            const shareData = {
                                files: files,
                                metadata: this.extractMetadata(files),
                                timestamp: Date.now(),
                                source: 'external_share'
                            };
                            
                            // 记录分享历史
                            this.recordShareHistory(shareData);
                            
                            return shareData;
                        }
                        
                        return null;
                    },
                    
                    // 处理分享数据
                    processSharedData: async (targetApp) => {
                        const shareData = this.getSharedData();
                        
                        if (shareData && this.shareHandlers.has(targetApp)) {
                            const handler = this.shareHandlers.get(targetApp);
                            
                            try {
                                const result = await handler.handler(shareData);
                                
                                this.env.bus.trigger('SHARE_TARGET_PROCESSED', {
                                    targetApp: targetApp,
                                    shareData: shareData,
                                    result: result,
                                    timestamp: Date.now()
                                });
                                
                                return result;
                            } catch (error) {
                                this.handleShareError(error, targetApp, shareData);
                                throw error;
                            }
                        }
                        
                        return null;
                    },
                    
                    // 获取支持的应用
                    getSupportedApps: () => {
                        return Array.from(this.shareHandlers.entries()).map(([key, handler]) => ({
                            key: key,
                            name: handler.name,
                            icon: handler.icon,
                            description: handler.description,
                            supportedTypes: handler.supportedTypes
                        }));
                    },
                    
                    // 验证分享数据
                    validateSharedData: (shareData) => {
                        return this.validateShareData(shareData);
                    }
                };
            }
        };
    }
    
    // 处理费用分享
    async handleExpensesShare(shareData) {
        const { files, metadata } = shareData;
        
        // 验证文件类型
        const validFiles = files.filter(file => 
            this.isValidFileType(file, ['image/*', 'application/pdf'])
        );
        
        if (validFiles.length === 0) {
            throw new Error('No valid expense files found');
        }
        
        // 创建费用记录
        const expenseData = {
            name: metadata.suggestedName || 'Shared Expense',
            date: new Date().toISOString().split('T')[0],
            attachments: validFiles,
            source: 'share_target'
        };
        
        // 调用费用创建API
        const result = await this.env.services.rpc('/web/dataset/call_kw', {
            model: 'hr.expense',
            method: 'create_from_share',
            args: [expenseData],
            kwargs: {}
        });
        
        return {
            success: true,
            expenseId: result.id,
            message: 'Expense created successfully'
        };
    }
    
    // 处理文档分享
    async handleDocumentsShare(shareData) {
        const { files, metadata } = shareData;
        
        // 创建文档记录
        const documentData = {
            name: metadata.suggestedName || 'Shared Document',
            folder_id: await this.getDefaultDocumentFolder(),
            attachments: files,
            source: 'share_target'
        };
        
        // 调用文档创建API
        const result = await this.env.services.rpc('/web/dataset/call_kw', {
            model: 'documents.document',
            method: 'create_from_share',
            args: [documentData],
            kwargs: {}
        });
        
        return {
            success: true,
            documentId: result.id,
            message: 'Document created successfully'
        };
    }
    
    // 处理项目分享
    async handleProjectShare(shareData) {
        const { files, metadata } = shareData;
        
        // 获取当前项目
        const currentProject = await this.getCurrentProject();
        
        if (!currentProject) {
            throw new Error('No active project found');
        }
        
        // 创建项目任务或附件
        const taskData = {
            name: metadata.suggestedName || 'Shared Content',
            project_id: currentProject.id,
            attachments: files,
            source: 'share_target'
        };
        
        // 调用任务创建API
        const result = await this.env.services.rpc('/web/dataset/call_kw', {
            model: 'project.task',
            method: 'create_from_share',
            args: [taskData],
            kwargs: {}
        });
        
        return {
            success: true,
            taskId: result.id,
            message: 'Project task created successfully'
        };
    }
    
    // 提取元数据
    extractMetadata(files) {
        const metadata = {
            fileCount: files.length,
            totalSize: files.reduce((sum, file) => sum + file.size, 0),
            fileTypes: [...new Set(files.map(file => file.type))],
            suggestedName: files.length === 1 ? files[0].name : `${files.length} files`,
            hasImages: files.some(file => file.type.startsWith('image/')),
            hasPDFs: files.some(file => file.type === 'application/pdf'),
            hasText: files.some(file => file.type.startsWith('text/'))
        };
        
        return metadata;
    }
    
    // 验证分享数据
    validateShareData(shareData) {
        const errors = [];
        
        // 验证文件数量
        if (!shareData.files || shareData.files.length === 0) {
            errors.push('No files to share');
        }
        
        // 验证文件大小
        const totalSize = shareData.files.reduce((sum, file) => sum + file.size, 0);
        if (totalSize > this.shareConfig.maxFileSize * shareData.files.length) {
            errors.push('Total file size exceeds limit');
        }
        
        // 验证文件类型
        const invalidFiles = shareData.files.filter(file => 
            !this.isValidFileType(file, this.shareConfig.allowedFileTypes)
        );
        
        if (invalidFiles.length > 0) {
            errors.push(`Invalid file types: ${invalidFiles.map(f => f.type).join(', ')}`);
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
    
    // 验证文件类型
    isValidFileType(file, allowedTypes) {
        return allowedTypes.some(type => {
            if (type === '*/*') return true;
            if (type.endsWith('/*')) {
                return file.type.startsWith(type.slice(0, -1));
            }
            return file.type === type;
        });
    }
    
    // 记录分享历史
    recordShareHistory(shareData) {
        const historyEntry = {
            id: this.generateShareId(),
            timestamp: shareData.timestamp,
            fileCount: shareData.files.length,
            totalSize: shareData.metadata.totalSize,
            fileTypes: shareData.metadata.fileTypes,
            source: shareData.source
        };
        
        this.shareHistory.push(historyEntry);
        
        // 限制历史记录数量
        if (this.shareHistory.length > 100) {
            this.shareHistory.shift();
        }
        
        // 保存到本地存储
        this.saveShareHistory();
    }
    
    // 处理分享接收
    handleShareReceived(shareDetail) {
        console.log('Share received:', shareDetail);
        
        // 触发分享接收事件
        this.env.bus.trigger('SHARE_TARGET_RECEIVED', {
            shareDetail: shareDetail,
            timestamp: Date.now()
        });
    }
    
    // 处理文件处理完成
    handleFilesProcessed(processDetail) {
        console.log('Files processed:', processDetail);
        
        // 更新分享历史
        const historyEntry = this.shareHistory.find(entry => 
            entry.timestamp === processDetail.timestamp
        );
        
        if (historyEntry) {
            historyEntry.processed = true;
            historyEntry.result = processDetail.result;
        }
        
        // 保存更新的历史
        this.saveShareHistory();
    }
    
    // 处理分享错误
    handleShareError(error, targetApp, shareData) {
        console.error('Share processing error:', error);
        
        // 记录错误
        const errorEntry = {
            timestamp: Date.now(),
            targetApp: targetApp,
            error: error.message,
            shareData: {
                fileCount: shareData.files.length,
                totalSize: shareData.metadata.totalSize
            }
        };
        
        // 触发错误事件
        this.env.bus.trigger('SHARE_TARGET_ERROR', {
            error: errorEntry,
            timestamp: Date.now()
        });
    }
    
    // 获取默认文档文件夹
    async getDefaultDocumentFolder() {
        try {
            const result = await this.env.services.rpc('/web/dataset/call_kw', {
                model: 'documents.folder',
                method: 'get_default_folder',
                args: [],
                kwargs: {}
            });
            
            return result.id;
        } catch (error) {
            console.warn('Failed to get default document folder:', error);
            return null;
        }
    }
    
    // 获取当前项目
    async getCurrentProject() {
        try {
            const result = await this.env.services.rpc('/web/dataset/call_kw', {
                model: 'project.project',
                method: 'get_current_project',
                args: [],
                kwargs: {}
            });
            
            return result;
        } catch (error) {
            console.warn('Failed to get current project:', error);
            return null;
        }
    }
    
    // 保存分享历史
    saveShareHistory() {
        try {
            localStorage.setItem('odoo_share_history', JSON.stringify(this.shareHistory));
        } catch (error) {
            console.warn('Failed to save share history:', error);
        }
    }
    
    // 加载分享历史
    loadShareHistory() {
        try {
            const stored = localStorage.getItem('odoo_share_history');
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            console.warn('Failed to load share history:', error);
            return [];
        }
    }
    
    // 生成分享ID
    generateShareId() {
        return `share_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    // 获取分享统计
    getShareStatistics() {
        const history = this.shareHistory;
        
        return {
            totalShares: history.length,
            successfulShares: history.filter(h => h.processed && h.result?.success).length,
            failedShares: history.filter(h => h.processed && !h.result?.success).length,
            totalFiles: history.reduce((sum, h) => sum + h.fileCount, 0),
            totalSize: history.reduce((sum, h) => sum + h.totalSize, 0),
            mostCommonTypes: this.getMostCommonFileTypes(history),
            recentShares: history.slice(-10)
        };
    }
    
    // 获取最常见的文件类型
    getMostCommonFileTypes(history) {
        const typeCount = {};
        
        history.forEach(entry => {
            entry.fileTypes.forEach(type => {
                typeCount[type] = (typeCount[type] || 0) + 1;
            });
        });
        
        return Object.entries(typeCount)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([type, count]) => ({ type, count }));
    }
    
    // 清理分享历史
    clearShareHistory() {
        this.shareHistory = [];
        localStorage.removeItem('odoo_share_history');
    }
    
    // 销毁管理器
    destroy() {
        // 清理事件监听器
        this.env.bus.removeEventListener('SHARE_TARGET_RECEIVED');
        this.env.bus.removeEventListener('SHARE_FILES_PROCESSED');
        
        // 清理数据
        this.shareHandlers.clear();
    }
}

// 使用示例
const shareTargetManager = new ShareTargetManager(env);

// 创建增强的分享目标服务
const enhancedService = shareTargetManager.createEnhancedShareTargetService();

// 获取支持的应用
const supportedApps = enhancedService.getSupportedApps();
console.log('Supported apps:', supportedApps);

// 获取分享统计
const stats = shareTargetManager.getShareStatistics();
console.log('Share statistics:', stats);
```

## 技术特点

### 1. PWA集成
- **Web Share Target**: 实现Web Share Target API
- **Service Worker**: 与Service Worker通信
- **PWA功能**: 作为PWA的分享目标
- **原生体验**: 提供原生应用般的分享体验

### 2. 异步通信
- **Promise封装**: 使用Promise处理异步操作
- **消息传递**: Service Worker消息传递
- **事件驱动**: 基于事件的通信机制
- **状态管理**: 管理分享状态

### 3. 应用集成
- **菜单服务**: 集成菜单服务
- **应用导航**: 自动导航到目标应用
- **条件处理**: 条件性的分享处理
- **生命周期**: 与应用生命周期绑定

### 4. 文件处理
- **文件接收**: 接收外部分享的文件
- **一次性消费**: 文件只能获取一次
- **状态清理**: 自动清理分享状态
- **错误处理**: 完善的错误处理

## 设计模式

### 1. 服务模式 (Service Pattern)
- **服务注册**: 注册为全局服务
- **依赖注入**: 依赖其他服务
- **接口定义**: 定义清晰的服务接口

### 2. 观察者模式 (Observer Pattern)
- **事件监听**: 监听客户端就绪事件
- **消息传递**: Service Worker消息传递
- **状态通知**: 通知分享状态变化

### 3. 策略模式 (Strategy Pattern)
- **分享策略**: 不同应用的分享策略
- **处理策略**: 不同类型文件的处理策略
- **导航策略**: 不同的应用导航策略

### 4. 单例模式 (Singleton Pattern)
- **服务实例**: 全局唯一的服务实例
- **状态管理**: 统一的分享状态管理
- **资源共享**: 共享分享资源

## 注意事项

1. **PWA环境**: 只在PWA环境下工作
2. **Service Worker**: 需要Service Worker支持
3. **浏览器兼容**: 确保浏览器支持Web Share Target
4. **安全性**: 验证分享内容的安全性

## 扩展建议

1. **多应用支持**: 支持更多目标应用
2. **文件验证**: 增强文件类型和大小验证
3. **批量处理**: 支持批量文件处理
4. **分享历史**: 记录和管理分享历史
5. **用户选择**: 允许用户选择目标应用

该分享目标服务为Odoo Web客户端提供了强大的PWA分享功能，通过与Service Worker的通信实现了从外部应用分享内容到Odoo的无缝体验。

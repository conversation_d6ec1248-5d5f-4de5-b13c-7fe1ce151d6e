# ClickbotLoader - 点击机器人加载器

## 概述

`clickbot_loader.js` 是 Odoo Web 客户端的点击机器人加载器，提供了自动化测试工具的加载和启动功能。该模块包含39行代码，是一个测试工具模块，专门用于加载和启动点击机器人（ClickBot）进行自动化UI测试，具备资源包加载、状态恢复、调试菜单集成、本地存储管理等特性，是Odoo Web客户端中自动化测试和质量保证的重要工具。

## 文件信息
- **路径**: `/web/static/src/webclient/clickbot/clickbot_loader.js`
- **行数**: 39
- **模块**: `@web/webclient/clickbot/clickbot_loader`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'        // 国际化翻译
'@web/core/assets'                  // 资源包加载
'@web/core/registry'                // 注册表系统
'@web/core/browser/browser'         // 浏览器工具
```

## 主要功能

### 1. startClickEverywhere - 启动点击机器人

```javascript
async function startClickEverywhere(xmlId, light, currentState) {
    await loadBundle("web.assets_clickbot");
    window.clickEverywhere(xmlId, light, currentState);
}
```

**启动功能**:
- **异步加载**: 异步加载点击机器人资源包
- **资源包**: 加载"web.assets_clickbot"资源包
- **全局调用**: 调用全局的clickEverywhere函数
- **参数传递**: 传递xmlId、light模式和当前状态

**参数说明**:
- **xmlId**: 目标XML ID，指定测试的起始点
- **light**: 轻量模式标志，控制测试的深度
- **currentState**: 当前状态，用于恢复测试进度

### 2. runClickTestItem - 运行点击测试菜单项

```javascript
function runClickTestItem({ env }) {
    return {
        type: "item",
        description: _t("Run Click Everywhere"),
        callback: () => {
            startClickEverywhere();
        },
        sequence: 460,
        section: "testing",
    };
}
```

**菜单项功能**:
- **类型**: 调试菜单项
- **描述**: 国际化的菜单描述
- **回调**: 启动点击机器人的回调函数
- **序列**: 在调试菜单中的显示顺序
- **分组**: 归属于"testing"测试分组

### 3. 状态恢复机制

```javascript
const currentState = JSON.parse(browser.localStorage.getItem("running.clickbot"));
if (currentState) {
    startClickEverywhere(currentState.xmlId, currentState.light, currentState);
}
```

**恢复功能**:
- **本地存储**: 从localStorage读取运行状态
- **JSON解析**: 解析存储的JSON状态数据
- **自动恢复**: 如果有保存的状态则自动恢复测试
- **状态传递**: 将保存的状态传递给点击机器人

### 4. 调试菜单注册

```javascript
registry.category("debug").category("default").add("runClickTestItem", runClickTestItem);
```

**注册功能**:
- **调试类别**: 注册到调试菜单类别
- **默认分组**: 注册到默认调试分组
- **菜单项**: 添加运行点击测试的菜单项
- **全局可用**: 在调试模式下全局可用

## 使用场景

### 1. 点击机器人测试管理器

```javascript
// 点击机器人测试管理器
class ClickbotTestManager {
    constructor(env) {
        this.env = env;
        this.setupManager();
    }
    
    setupManager() {
        // 设置测试配置
        this.testConfig = {
            enableAutoRestart: true,
            enableStateRecovery: true,
            enableProgressTracking: true,
            enableErrorReporting: true,
            maxRetries: 3,
            timeout: 30000
        };
        
        // 设置测试模式
        this.testModes = {
            light: { depth: 2, skipComplex: true, fastMode: true },
            normal: { depth: 5, skipComplex: false, fastMode: false },
            deep: { depth: 10, skipComplex: false, fastMode: false },
            custom: { depth: 0, skipComplex: false, fastMode: false }
        };
        
        // 设置测试状态
        this.testState = {
            isRunning: false,
            currentTest: null,
            progress: 0,
            errors: [],
            startTime: null,
            endTime: null
        };
        
        this.setupEventListeners();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听测试开始
        this.env.bus.addEventListener('CLICKBOT_TEST_STARTED', (event) => {
            this.handleTestStarted(event.detail);
        });
        
        // 监听测试完成
        this.env.bus.addEventListener('CLICKBOT_TEST_COMPLETED', (event) => {
            this.handleTestCompleted(event.detail);
        });
        
        // 监听测试错误
        this.env.bus.addEventListener('CLICKBOT_TEST_ERROR', (event) => {
            this.handleTestError(event.detail);
        });
        
        // 监听页面卸载
        window.addEventListener('beforeunload', () => {
            this.saveTestState();
        });
    }
    
    // 启动点击机器人测试
    async startClickbotTest(options = {}) {
        const config = {
            xmlId: options.xmlId || null,
            mode: options.mode || 'normal',
            light: options.light || false,
            currentState: options.currentState || null,
            ...this.testConfig
        };
        
        try {
            // 更新测试状态
            this.updateTestState({
                isRunning: true,
                currentTest: config,
                startTime: Date.now(),
                errors: []
            });
            
            // 保存测试状态
            this.saveTestState();
            
            // 启动测试
            await startClickEverywhere(config.xmlId, config.light, config.currentState);
            
            // 触发测试开始事件
            this.env.bus.trigger('CLICKBOT_TEST_STARTED', {
                config: config,
                timestamp: Date.now()
            });
            
        } catch (error) {
            this.handleTestError({
                error: error,
                config: config,
                timestamp: Date.now()
            });
        }
    }
    
    // 停止点击机器人测试
    stopClickbotTest() {
        if (this.testState.isRunning) {
            // 停止测试
            if (window.clickEverywhere && window.clickEverywhere.stop) {
                window.clickEverywhere.stop();
            }
            
            // 更新状态
            this.updateTestState({
                isRunning: false,
                endTime: Date.now()
            });
            
            // 清除保存的状态
            this.clearSavedState();
            
            // 触发测试停止事件
            this.env.bus.trigger('CLICKBOT_TEST_STOPPED', {
                duration: this.testState.endTime - this.testState.startTime,
                timestamp: Date.now()
            });
        }
    }
    
    // 暂停点击机器人测试
    pauseClickbotTest() {
        if (this.testState.isRunning) {
            // 暂停测试
            if (window.clickEverywhere && window.clickEverywhere.pause) {
                window.clickEverywhere.pause();
            }
            
            // 保存当前状态
            this.saveTestState();
            
            // 触发暂停事件
            this.env.bus.trigger('CLICKBOT_TEST_PAUSED', {
                timestamp: Date.now()
            });
        }
    }
    
    // 恢复点击机器人测试
    resumeClickbotTest() {
        const savedState = this.loadSavedState();
        
        if (savedState) {
            // 恢复测试
            this.startClickbotTest({
                currentState: savedState,
                ...savedState.config
            });
            
            // 触发恢复事件
            this.env.bus.trigger('CLICKBOT_TEST_RESUMED', {
                savedState: savedState,
                timestamp: Date.now()
            });
        }
    }
    
    // 创建测试报告
    createTestReport() {
        const report = {
            testId: this.generateTestId(),
            startTime: this.testState.startTime,
            endTime: this.testState.endTime,
            duration: this.testState.endTime - this.testState.startTime,
            config: this.testState.currentTest,
            progress: this.testState.progress,
            errors: this.testState.errors,
            success: this.testState.errors.length === 0,
            environment: {
                userAgent: navigator.userAgent,
                url: window.location.href,
                timestamp: Date.now()
            }
        };
        
        return report;
    }
    
    // 保存测试报告
    saveTestReport(report) {
        try {
            const reports = this.getStoredReports();
            reports.push(report);
            
            // 限制报告数量
            if (reports.length > 50) {
                reports.shift();
            }
            
            localStorage.setItem('odoo_clickbot_reports', JSON.stringify(reports));
            
            return true;
        } catch (error) {
            console.warn('Failed to save test report:', error);
            return false;
        }
    }
    
    // 获取存储的报告
    getStoredReports() {
        try {
            const stored = localStorage.getItem('odoo_clickbot_reports');
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            console.warn('Failed to get stored reports:', error);
            return [];
        }
    }
    
    // 导出测试报告
    exportTestReports() {
        const reports = this.getStoredReports();
        const exportData = {
            exportDate: new Date().toISOString(),
            totalReports: reports.length,
            reports: reports
        };
        
        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `clickbot_reports_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
    }
    
    // 更新测试状态
    updateTestState(updates) {
        Object.assign(this.testState, updates);
        
        // 触发状态更新事件
        this.env.bus.trigger('CLICKBOT_STATE_UPDATED', {
            state: this.testState,
            updates: updates,
            timestamp: Date.now()
        });
    }
    
    // 保存测试状态
    saveTestState() {
        if (this.testConfig.enableStateRecovery) {
            try {
                const stateToSave = {
                    ...this.testState,
                    config: this.testState.currentTest
                };
                
                localStorage.setItem('running.clickbot', JSON.stringify(stateToSave));
            } catch (error) {
                console.warn('Failed to save test state:', error);
            }
        }
    }
    
    // 加载保存的状态
    loadSavedState() {
        try {
            const stored = localStorage.getItem('running.clickbot');
            return stored ? JSON.parse(stored) : null;
        } catch (error) {
            console.warn('Failed to load saved state:', error);
            return null;
        }
    }
    
    // 清除保存的状态
    clearSavedState() {
        try {
            localStorage.removeItem('running.clickbot');
        } catch (error) {
            console.warn('Failed to clear saved state:', error);
        }
    }
    
    // 处理测试开始
    handleTestStarted(startData) {
        console.log('Clickbot test started:', startData);
        
        // 更新UI状态
        this.updateTestUI('started');
        
        // 开始进度跟踪
        if (this.testConfig.enableProgressTracking) {
            this.startProgressTracking();
        }
    }
    
    // 处理测试完成
    handleTestCompleted(completionData) {
        console.log('Clickbot test completed:', completionData);
        
        // 更新测试状态
        this.updateTestState({
            isRunning: false,
            endTime: Date.now(),
            progress: 100
        });
        
        // 创建并保存报告
        const report = this.createTestReport();
        this.saveTestReport(report);
        
        // 清除保存的状态
        this.clearSavedState();
        
        // 更新UI状态
        this.updateTestUI('completed');
    }
    
    // 处理测试错误
    handleTestError(errorData) {
        console.error('Clickbot test error:', errorData);
        
        // 记录错误
        this.testState.errors.push({
            error: errorData.error,
            timestamp: errorData.timestamp,
            config: errorData.config
        });
        
        // 更新状态
        this.updateTestState({
            isRunning: false,
            endTime: Date.now()
        });
        
        // 错误报告
        if (this.testConfig.enableErrorReporting) {
            this.reportError(errorData);
        }
        
        // 自动重试
        if (this.testConfig.enableAutoRestart && this.testState.errors.length < this.testConfig.maxRetries) {
            setTimeout(() => {
                this.startClickbotTest(errorData.config);
            }, 5000);
        }
    }
    
    // 开始进度跟踪
    startProgressTracking() {
        this.progressInterval = setInterval(() => {
            if (window.clickEverywhere && window.clickEverywhere.getProgress) {
                const progress = window.clickEverywhere.getProgress();
                this.updateTestState({ progress: progress });
            }
        }, 1000);
    }
    
    // 停止进度跟踪
    stopProgressTracking() {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }
    }
    
    // 更新测试UI
    updateTestUI(status) {
        // 更新UI状态显示
        const statusElement = document.querySelector('.o_clickbot_status');
        if (statusElement) {
            statusElement.textContent = `Clickbot: ${status}`;
            statusElement.className = `o_clickbot_status o_clickbot_${status}`;
        }
    }
    
    // 报告错误
    reportError(errorData) {
        // 发送错误报告到服务器
        this.env.services.rpc('/web/clickbot/error', {
            error_data: errorData
        }).catch(error => {
            console.warn('Failed to report clickbot error:', error);
        });
    }
    
    // 生成测试ID
    generateTestId() {
        return `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    // 获取测试统计
    getTestStatistics() {
        const reports = this.getStoredReports();
        
        const stats = {
            totalTests: reports.length,
            successfulTests: reports.filter(r => r.success).length,
            failedTests: reports.filter(r => !r.success).length,
            averageDuration: reports.length > 0 ? 
                reports.reduce((sum, r) => sum + r.duration, 0) / reports.length : 0,
            lastTestDate: reports.length > 0 ? 
                Math.max(...reports.map(r => r.startTime)) : null
        };
        
        return stats;
    }
    
    // 清理测试数据
    cleanupTestData() {
        // 清除保存的状态
        this.clearSavedState();
        
        // 清除测试报告
        localStorage.removeItem('odoo_clickbot_reports');
        
        // 停止进度跟踪
        this.stopProgressTracking();
        
        // 重置测试状态
        this.testState = {
            isRunning: false,
            currentTest: null,
            progress: 0,
            errors: [],
            startTime: null,
            endTime: null
        };
    }
    
    // 销毁管理器
    destroy() {
        // 停止当前测试
        this.stopClickbotTest();
        
        // 清理事件监听器
        this.env.bus.removeEventListener('CLICKBOT_TEST_STARTED');
        this.env.bus.removeEventListener('CLICKBOT_TEST_COMPLETED');
        this.env.bus.removeEventListener('CLICKBOT_TEST_ERROR');
        
        // 清理数据
        this.cleanupTestData();
    }
}

// 使用示例
const clickbotManager = new ClickbotTestManager(env);

// 启动轻量模式测试
clickbotManager.startClickbotTest({
    mode: 'light',
    light: true
});

// 启动深度测试
clickbotManager.startClickbotTest({
    mode: 'deep',
    xmlId: 'specific_element'
});

// 获取测试统计
const stats = clickbotManager.getTestStatistics();
console.log('Test statistics:', stats);

// 导出测试报告
clickbotManager.exportTestReports();
```

## 技术特点

### 1. 异步加载机制
- **资源包**: 动态加载点击机器人资源包
- **异步操作**: 使用async/await处理异步加载
- **延迟加载**: 按需加载测试工具
- **性能优化**: 避免影响主应用性能

### 2. 状态管理
- **本地存储**: 使用localStorage保存测试状态
- **状态恢复**: 自动恢复中断的测试
- **JSON序列化**: 安全的状态序列化和反序列化
- **状态持久化**: 跨会话的状态持久化

### 3. 调试集成
- **调试菜单**: 集成到调试菜单系统
- **测试分组**: 归类到测试相关分组
- **国际化**: 支持多语言界面
- **开发工具**: 作为开发和测试工具

### 4. 全局接口
- **全局函数**: 调用全局的clickEverywhere函数
- **参数传递**: 灵活的参数传递机制
- **状态传递**: 支持状态的传递和恢复
- **模式控制**: 支持不同的测试模式

## 设计模式

### 1. 加载器模式 (Loader Pattern)
- **资源加载**: 动态加载测试资源
- **延迟初始化**: 延迟初始化测试工具
- **依赖管理**: 管理测试工具依赖

### 2. 状态模式 (State Pattern)
- **状态保存**: 保存和恢复测试状态
- **状态转换**: 管理测试状态转换
- **状态持久化**: 持久化状态数据

### 3. 命令模式 (Command Pattern)
- **测试命令**: 封装测试执行命令
- **参数传递**: 传递测试参数
- **异步执行**: 异步执行测试命令

### 4. 观察者模式 (Observer Pattern)
- **事件监听**: 监听测试相关事件
- **状态通知**: 通知测试状态变化
- **回调机制**: 提供回调机制

## 注意事项

1. **资源加载**: 确保测试资源正确加载
2. **状态管理**: 妥善管理测试状态
3. **错误处理**: 处理加载和执行错误
4. **性能影响**: 避免影响主应用性能

## 扩展建议

1. **测试配置**: 支持更多的测试配置选项
2. **报告系统**: 完善的测试报告系统
3. **并行测试**: 支持并行测试执行
4. **测试录制**: 支持测试用例录制
5. **集成测试**: 与CI/CD系统集成

该点击机器人加载器为Odoo Web客户端提供了强大的自动化测试工具，通过动态加载和状态管理确保了高效的UI自动化测试能力。

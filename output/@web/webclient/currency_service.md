# CurrencyService - 货币服务

## 概述

`currency_service.js` 是 Odoo Web 客户端的货币服务，提供了货币信息的管理和自动更新功能。该模块包含34行代码，是一个轻量级服务模块，专门用于监听货币模型的变化并自动重载货币信息，具备RPC监听、自动重载、全局货币管理等特性，是Odoo Web客户端中货币处理的核心服务。

## 文件信息
- **路径**: `/web/static/src/webclient/currency_service.js`
- **行数**: 34
- **模块**: `@web/webclient/currency_service`

## 依赖关系

```javascript
// 核心依赖
'@web/core/network/rpc'             // RPC网络通信
'@web/core/registry'                // 注册表系统
'@web/core/currency'                // 货币核心模块
'@web/core/orm_service'             // ORM服务
```

## 主要服务定义

### 1. currencyService - 货币服务

```javascript
const currencyService = {
    start() {
        async function reloadCurrencies() {
            const result = await rpc("/web/session/get_session_info");
            for (const k in currencies) {
                delete currencies[k];
            }
            Object.assign(currencies, result?.currencies);
        }
        
        rpcBus.addEventListener("RPC:RESPONSE", (ev) => {
            const { data, error } = ev.detail;
            const { model, method } = data.params;
            if (!error && model === "res.currency" && UPDATE_METHODS.includes(method)) {
                reloadCurrencies();
            }
        });
    },
};
```

**服务特性**:
- **无依赖**: 不依赖其他服务
- **事件驱动**: 基于RPC事件的自动更新
- **全局管理**: 管理全局货币对象
- **自动同步**: 自动同步货币信息

## 核心功能

### 1. 货币重载功能

```javascript
async function reloadCurrencies() {
    const result = await rpc("/web/session/get_session_info");
    for (const k in currencies) {
        delete currencies[k];
    }
    Object.assign(currencies, result?.currencies);
}
```

**重载功能**:
- **会话获取**: 从会话信息获取最新货币数据
- **清空现有**: 清空现有的货币对象
- **重新赋值**: 使用最新数据重新赋值
- **异步操作**: 异步执行重载操作

### 2. 自动监听机制

```javascript
rpcBus.addEventListener("RPC:RESPONSE", (ev) => {
    const { data, error } = ev.detail;
    const { model, method } = data.params;
    if (!error && model === "res.currency" && UPDATE_METHODS.includes(method)) {
        reloadCurrencies();
    }
});
```

**监听机制功能**:
- **RPC监听**: 监听所有RPC响应
- **模型过滤**: 只处理res.currency模型
- **方法验证**: 验证是否为更新方法
- **错误检查**: 只在无错误时执行重载
- **自动触发**: 自动触发货币重载

### 3. 服务注册

```javascript
registry.category("services").add("currency", currencyService);
```

**注册功能**:
- **服务类别**: 注册到服务类别
- **全局可用**: 在整个应用中可用
- **自动启动**: 应用启动时自动启动
- **生命周期**: 与应用生命周期绑定

## 使用场景

### 1. 货币管理器

```javascript
// 货币管理器
class CurrencyManager {
    constructor(env) {
        this.env = env;
        this.setupManager();
    }
    
    setupManager() {
        // 设置货币配置
        this.currencyConfig = {
            enableAutoUpdate: true,
            enableCaching: true,
            enableValidation: true,
            enableFormatting: true,
            cacheTimeout: 300000 // 5分钟
        };
        
        // 设置货币缓存
        this.currencyCache = new Map();
        
        // 设置格式化器
        this.formatters = new Map();
        
        // 设置汇率缓存
        this.exchangeRates = new Map();
        
        this.setupEventListeners();
        this.initializeFormatters();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 监听货币更新
        this.env.bus.addEventListener('CURRENCY_UPDATED', (event) => {
            this.handleCurrencyUpdate(event.detail);
        });
        
        // 监听汇率变化
        this.env.bus.addEventListener('EXCHANGE_RATE_CHANGED', (event) => {
            this.handleExchangeRateChange(event.detail);
        });
    }
    
    // 初始化格式化器
    initializeFormatters() {
        // 为每种货币创建格式化器
        Object.values(currencies).forEach(currency => {
            this.createCurrencyFormatter(currency);
        });
    }
    
    // 创建货币格式化器
    createCurrencyFormatter(currency) {
        const formatter = new Intl.NumberFormat(currency.locale || 'en-US', {
            style: 'currency',
            currency: currency.name,
            minimumFractionDigits: currency.decimal_places || 2,
            maximumFractionDigits: currency.decimal_places || 2
        });
        
        this.formatters.set(currency.id, formatter);
        
        return formatter;
    }
    
    // 获取货币信息
    getCurrency(currencyId) {
        // 先从缓存获取
        if (this.currencyCache.has(currencyId)) {
            const cached = this.currencyCache.get(currencyId);
            if (Date.now() - cached.timestamp < this.currencyConfig.cacheTimeout) {
                return cached.currency;
            }
        }
        
        // 从全局货币对象获取
        const currency = currencies[currencyId];
        
        if (currency) {
            // 缓存货币信息
            this.currencyCache.set(currencyId, {
                currency: currency,
                timestamp: Date.now()
            });
        }
        
        return currency;
    }
    
    // 格式化货币金额
    formatCurrency(amount, currencyId, options = {}) {
        const currency = this.getCurrency(currencyId);
        
        if (!currency) {
            console.warn(`Currency ${currencyId} not found`);
            return amount.toString();
        }
        
        const formatter = this.formatters.get(currencyId);
        
        if (formatter) {
            try {
                return formatter.format(amount);
            } catch (error) {
                console.warn('Currency formatting error:', error);
                return this.fallbackFormat(amount, currency);
            }
        }
        
        return this.fallbackFormat(amount, currency);
    }
    
    // 回退格式化
    fallbackFormat(amount, currency) {
        const decimals = currency.decimal_places || 2;
        const formattedAmount = amount.toFixed(decimals);
        
        if (currency.position === 'before') {
            return `${currency.symbol}${formattedAmount}`;
        } else {
            return `${formattedAmount}${currency.symbol}`;
        }
    }
    
    // 货币转换
    async convertCurrency(amount, fromCurrencyId, toCurrencyId) {
        if (fromCurrencyId === toCurrencyId) {
            return amount;
        }
        
        const exchangeRate = await this.getExchangeRate(fromCurrencyId, toCurrencyId);
        
        if (exchangeRate) {
            return amount * exchangeRate;
        }
        
        throw new Error(`Exchange rate not available for ${fromCurrencyId} to ${toCurrencyId}`);
    }
    
    // 获取汇率
    async getExchangeRate(fromCurrencyId, toCurrencyId) {
        const rateKey = `${fromCurrencyId}_${toCurrencyId}`;
        
        // 检查缓存
        if (this.exchangeRates.has(rateKey)) {
            const cached = this.exchangeRates.get(rateKey);
            if (Date.now() - cached.timestamp < this.currencyConfig.cacheTimeout) {
                return cached.rate;
            }
        }
        
        try {
            // 从服务器获取汇率
            const result = await this.env.services.rpc('/web/dataset/call_kw', {
                model: 'res.currency',
                method: 'get_exchange_rate',
                args: [fromCurrencyId, toCurrencyId],
                kwargs: {}
            });
            
            // 缓存汇率
            this.exchangeRates.set(rateKey, {
                rate: result,
                timestamp: Date.now()
            });
            
            return result;
        } catch (error) {
            console.error('Failed to get exchange rate:', error);
            return null;
        }
    }
    
    // 验证货币
    validateCurrency(currencyId) {
        const currency = this.getCurrency(currencyId);
        
        if (!currency) {
            return {
                valid: false,
                error: 'Currency not found'
            };
        }
        
        if (!currency.active) {
            return {
                valid: false,
                error: 'Currency is inactive'
            };
        }
        
        return {
            valid: true,
            currency: currency
        };
    }
    
    // 获取所有货币
    getAllCurrencies() {
        return Object.values(currencies);
    }
    
    // 获取活动货币
    getActiveCurrencies() {
        return Object.values(currencies).filter(currency => currency.active);
    }
    
    // 搜索货币
    searchCurrencies(query) {
        const lowerQuery = query.toLowerCase();
        
        return Object.values(currencies).filter(currency => 
            currency.name.toLowerCase().includes(lowerQuery) ||
            currency.symbol.toLowerCase().includes(lowerQuery) ||
            (currency.full_name && currency.full_name.toLowerCase().includes(lowerQuery))
        );
    }
    
    // 获取货币统计
    getCurrencyStatistics() {
        const allCurrencies = this.getAllCurrencies();
        const activeCurrencies = this.getActiveCurrencies();
        
        return {
            totalCurrencies: allCurrencies.length,
            activeCurrencies: activeCurrencies.length,
            inactiveCurrencies: allCurrencies.length - activeCurrencies.length,
            cachedCurrencies: this.currencyCache.size,
            cachedExchangeRates: this.exchangeRates.size,
            formatters: this.formatters.size
        };
    }
    
    // 处理货币更新
    handleCurrencyUpdate(updateDetail) {
        console.log('Currency updated:', updateDetail);
        
        // 清理相关缓存
        this.clearCurrencyCache(updateDetail.currencyId);
        
        // 重新创建格式化器
        const currency = this.getCurrency(updateDetail.currencyId);
        if (currency) {
            this.createCurrencyFormatter(currency);
        }
        
        // 触发货币更新事件
        this.env.bus.trigger('CURRENCY_MANAGER_UPDATED', {
            currencyId: updateDetail.currencyId,
            timestamp: Date.now()
        });
    }
    
    // 处理汇率变化
    handleExchangeRateChange(rateDetail) {
        console.log('Exchange rate changed:', rateDetail);
        
        // 清理汇率缓存
        this.clearExchangeRateCache(rateDetail.fromCurrency, rateDetail.toCurrency);
        
        // 触发汇率更新事件
        this.env.bus.trigger('EXCHANGE_RATE_UPDATED', {
            fromCurrency: rateDetail.fromCurrency,
            toCurrency: rateDetail.toCurrency,
            newRate: rateDetail.newRate,
            timestamp: Date.now()
        });
    }
    
    // 清理货币缓存
    clearCurrencyCache(currencyId) {
        if (currencyId) {
            this.currencyCache.delete(currencyId);
        } else {
            this.currencyCache.clear();
        }
    }
    
    // 清理汇率缓存
    clearExchangeRateCache(fromCurrency, toCurrency) {
        if (fromCurrency && toCurrency) {
            const rateKey = `${fromCurrency}_${toCurrency}`;
            this.exchangeRates.delete(rateKey);
            
            // 也清理反向汇率
            const reverseRateKey = `${toCurrency}_${fromCurrency}`;
            this.exchangeRates.delete(reverseRateKey);
        } else {
            this.exchangeRates.clear();
        }
    }
    
    // 预加载常用货币
    async preloadCommonCurrencies() {
        const commonCurrencies = ['USD', 'EUR', 'GBP', 'JPY', 'CNY'];
        
        for (const currencyCode of commonCurrencies) {
            const currency = Object.values(currencies).find(c => c.name === currencyCode);
            if (currency) {
                // 预加载到缓存
                this.getCurrency(currency.id);
                
                // 预创建格式化器
                this.createCurrencyFormatter(currency);
            }
        }
    }
    
    // 导出货币配置
    exportCurrencyConfiguration() {
        const config = {
            exportDate: new Date().toISOString(),
            currencies: this.getAllCurrencies(),
            statistics: this.getCurrencyStatistics(),
            config: this.currencyConfig
        };
        
        const blob = new Blob([JSON.stringify(config, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `currency_config_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
    }
    
    // 销毁管理器
    destroy() {
        // 清理事件监听器
        this.env.bus.removeEventListener('CURRENCY_UPDATED');
        this.env.bus.removeEventListener('EXCHANGE_RATE_CHANGED');
        
        // 清理缓存
        this.currencyCache.clear();
        this.exchangeRates.clear();
        this.formatters.clear();
    }
}

// 使用示例
const currencyManager = new CurrencyManager(env);

// 格式化货币
const formatted = currencyManager.formatCurrency(1234.56, 1);
console.log('Formatted currency:', formatted);

// 货币转换
const converted = await currencyManager.convertCurrency(100, 1, 2);
console.log('Converted amount:', converted);

// 获取统计信息
const stats = currencyManager.getCurrencyStatistics();
console.log('Currency statistics:', stats);

// 搜索货币
const searchResults = currencyManager.searchCurrencies('USD');
console.log('Search results:', searchResults);
```

## 技术特点

### 1. 轻量级设计
- **简洁代码**: 仅34行代码实现核心功能
- **无依赖**: 不依赖其他复杂服务
- **高效执行**: 高效的事件监听和处理
- **最小开销**: 最小的性能开销

### 2. 自动同步
- **事件驱动**: 基于RPC事件的自动更新
- **实时监听**: 实时监听货币模型变化
- **智能过滤**: 只处理相关的更新操作
- **错误处理**: 完善的错误处理机制

### 3. 全局管理
- **全局对象**: 管理全局currencies对象
- **统一接口**: 提供统一的货币访问接口
- **状态一致**: 保证货币状态的一致性
- **内存管理**: 高效的内存使用

### 4. 扩展性
- **服务架构**: 标准的服务架构设计
- **事件系统**: 可扩展的事件系统
- **接口标准**: 标准化的服务接口
- **模块化**: 良好的模块化设计

## 设计模式

### 1. 服务模式 (Service Pattern)
- **服务注册**: 注册为全局服务
- **生命周期**: 完整的服务生命周期
- **接口定义**: 清晰的服务接口

### 2. 观察者模式 (Observer Pattern)
- **事件监听**: 监听RPC响应事件
- **自动响应**: 自动响应货币变化
- **状态同步**: 自动同步货币状态

### 3. 单例模式 (Singleton Pattern)
- **全局实例**: 全局唯一的服务实例
- **状态管理**: 统一的货币状态管理
- **资源共享**: 共享货币资源

### 4. 策略模式 (Strategy Pattern)
- **更新策略**: 不同的货币更新策略
- **同步策略**: 不同的数据同步策略
- **缓存策略**: 不同的缓存策略

## 注意事项

1. **性能影响**: 避免频繁的货币重载操作
2. **内存管理**: 及时清理不需要的货币数据
3. **错误处理**: 妥善处理网络错误和数据异常
4. **缓存策略**: 合理使用缓存避免过期数据

## 扩展建议

1. **缓存机制**: 添加货币信息缓存机制
2. **格式化**: 增强货币格式化功能
3. **汇率支持**: 添加实时汇率支持
4. **本地化**: 完善的本地化支持
5. **性能监控**: 添加性能监控和优化

该货币服务为Odoo Web客户端提供了简洁高效的货币管理功能，通过自动监听和更新机制确保了货币信息的实时性和准确性。

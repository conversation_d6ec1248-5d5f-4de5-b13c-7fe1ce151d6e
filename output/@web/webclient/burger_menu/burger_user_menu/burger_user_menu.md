# BurgerUserMenu - 汉堡用户菜单组件

## 概述

`burger_user_menu.js` 是 Odoo Web 客户端的汉堡用户菜单组件，提供了移动端汉堡菜单中的用户菜单功能。该模块包含14行代码，继承自UserMenu基类，专门用于在汉堡菜单中显示用户相关的菜单项，具备简化的点击处理、移动端优化等特性，是汉堡菜单系统中用户交互的重要组成部分。

## 文件信息
- **路径**: `/web/static/src/webclient/burger_menu/burger_user_menu/burger_user_menu.js`
- **行数**: 14
- **模块**: `@web/webclient/burger_menu/burger_user_menu/burger_user_menu`

## 依赖关系

```javascript
// 核心依赖
'@web/webclient/user_menu/user_menu'  // 用户菜单基类
```

## 主组件定义

### 1. BurgerUserMenu 组件

```javascript
class BurgerUserMenu extends UserMenu {
    static template = "web.BurgerUserMenu";
    
    _onItemClicked(callback) {
        callback();
    }
}
```

**组件特性**:
- **继承UserMenu**: 继承标准用户菜单的所有功能
- **移动端优化**: 专门为移动端汉堡菜单优化
- **简化交互**: 简化的点击处理逻辑
- **专用模板**: 使用专门的汉堡菜单模板

## 核心功能

### 1. 菜单项点击处理

```javascript
_onItemClicked(callback) {
    callback();
}
```

**点击处理功能**:
- **直接执行**: 直接执行回调函数，无额外处理
- **简化逻辑**: 相比基类，简化了点击处理逻辑
- **移动端适配**: 适配移动端的交互模式
- **即时响应**: 提供即时的用户反馈

## 使用场景

### 1. 汉堡菜单用户区域

```javascript
// 汉堡菜单中的用户菜单使用
class BurgerMenuContainer extends Component {
    static template = xml`
        <div class="burger-menu-container">
            <div class="burger-header">
                <BurgerUserMenu/>
            </div>
            <div class="burger-content">
                <!-- 其他菜单内容 -->
            </div>
        </div>`;
    
    static components = {
        BurgerUserMenu
    };
}
```

### 2. 移动端用户菜单管理器

```javascript
// 移动端用户菜单管理器
class MobileUserMenuManager {
    constructor(env) {
        this.env = env;
        this.userMenuItems = [];
        this.setupUserMenuItems();
    }
    
    // 设置用户菜单项
    setupUserMenuItems() {
        this.userMenuItems = [
            {
                id: 'profile',
                name: 'My Profile',
                icon: 'fa-user',
                callback: () => this.openProfile()
            },
            {
                id: 'preferences',
                name: 'Preferences',
                icon: 'fa-cog',
                callback: () => this.openPreferences()
            },
            {
                id: 'logout',
                name: 'Log out',
                icon: 'fa-sign-out',
                callback: () => this.logout()
            }
        ];
    }
    
    // 打开用户资料
    async openProfile() {
        try {
            await this.env.services.action.doAction({
                type: 'ir.actions.act_window',
                res_model: 'res.users',
                res_id: this.env.services.user.userId,
                view_mode: 'form',
                target: 'new'
            });
            
            // 关闭汉堡菜单
            this.closeBurgerMenu();
        } catch (error) {
            console.error('Failed to open profile:', error);
            this.showError('Failed to open profile');
        }
    }
    
    // 打开偏好设置
    async openPreferences() {
        try {
            await this.env.services.action.doAction({
                type: 'ir.actions.client',
                tag: 'preference',
                target: 'new'
            });
            
            this.closeBurgerMenu();
        } catch (error) {
            console.error('Failed to open preferences:', error);
            this.showError('Failed to open preferences');
        }
    }
    
    // 用户登出
    async logout() {
        try {
            const confirmed = await this.confirmLogout();
            if (confirmed) {
                await this.env.services.action.doAction({
                    type: 'ir.actions.client',
                    tag: 'logout'
                });
            }
        } catch (error) {
            console.error('Logout failed:', error);
            this.showError('Logout failed');
        }
    }
    
    // 确认登出
    async confirmLogout() {
        return new Promise((resolve) => {
            this.env.services.dialog.add(ConfirmationDialog, {
                title: 'Confirm Logout',
                body: 'Are you sure you want to log out?',
                confirm: () => resolve(true),
                cancel: () => resolve(false)
            });
        });
    }
    
    // 关闭汉堡菜单
    closeBurgerMenu() {
        this.env.bus.trigger('BURGER_MENU:CLOSE');
    }
    
    // 显示错误消息
    showError(message) {
        this.env.services.notification.add(message, {
            type: 'danger',
            sticky: false
        });
    }
    
    // 获取用户菜单项
    getUserMenuItems() {
        return this.userMenuItems.map(item => ({
            ...item,
            callback: () => {
                // 包装回调以添加通用处理
                try {
                    item.callback();
                } catch (error) {
                    console.error(`Menu item ${item.id} failed:`, error);
                    this.showError(`Failed to execute ${item.name}`);
                }
            }
        }));
    }
    
    // 添加自定义菜单项
    addMenuItem(item) {
        this.userMenuItems.push({
            id: item.id || `custom_${Date.now()}`,
            name: item.name,
            icon: item.icon || 'fa-circle',
            callback: item.callback,
            sequence: item.sequence || 100
        });
        
        // 按序列号排序
        this.userMenuItems.sort((a, b) => (a.sequence || 0) - (b.sequence || 0));
    }
    
    // 移除菜单项
    removeMenuItem(itemId) {
        const index = this.userMenuItems.findIndex(item => item.id === itemId);
        if (index !== -1) {
            this.userMenuItems.splice(index, 1);
            return true;
        }
        return false;
    }
    
    // 更新菜单项
    updateMenuItem(itemId, updates) {
        const item = this.userMenuItems.find(item => item.id === itemId);
        if (item) {
            Object.assign(item, updates);
            return true;
        }
        return false;
    }
    
    // 获取用户信息
    getUserInfo() {
        const user = this.env.services.user;
        return {
            id: user.userId,
            name: user.name,
            email: user.email,
            avatar: user.avatar,
            isAdmin: user.isAdmin,
            companyId: user.companyId,
            companyName: user.companyName
        };
    }
    
    // 格式化用户显示名称
    formatUserDisplayName() {
        const user = this.getUserInfo();
        return user.name || user.email || 'User';
    }
    
    // 获取用户头像URL
    getUserAvatarUrl() {
        const user = this.getUserInfo();
        if (user.avatar) {
            return `/web/image/res.users/${user.id}/avatar_128`;
        }
        return '/web/static/img/user_menu_avatar.png';
    }
}

// 使用示例
const userMenuManager = new MobileUserMenuManager(env);

// 获取用户菜单项
const menuItems = userMenuManager.getUserMenuItems();

// 添加自定义菜单项
userMenuManager.addMenuItem({
    id: 'help',
    name: 'Help',
    icon: 'fa-question-circle',
    callback: () => {
        window.open('/web/help', '_blank');
    },
    sequence: 50
});

// 获取用户信息
const userInfo = userMenuManager.getUserInfo();
console.log('User info:', userInfo);
```

### 3. 扩展的汉堡用户菜单

```javascript
// 扩展的汉堡用户菜单组件
class ExtendedBurgerUserMenu extends BurgerUserMenu {
    static template = xml`
        <div class="extended-burger-user-menu">
            <div class="user-info-section">
                <div class="user-avatar">
                    <img t-att-src="userAvatarUrl" t-att-alt="userName"/>
                </div>
                <div class="user-details">
                    <div class="user-name" t-esc="userName"/>
                    <div class="user-email" t-esc="userEmail"/>
                    <div class="user-company" t-esc="userCompany"/>
                </div>
            </div>
            
            <div class="menu-items-section">
                <t t-foreach="menuItems" t-as="item" t-key="item.id">
                    <div class="menu-item" 
                         t-on-click="() => this._onItemClicked(item.callback)"
                         t-att-class="{ 'menu-item-danger': item.isDanger }">
                        <i t-att-class="'fa ' + item.icon"/>
                        <span t-esc="item.name"/>
                        <i t-if="item.hasSubmenu" class="fa fa-chevron-right submenu-arrow"/>
                    </div>
                </t>
            </div>
            
            <div class="menu-footer">
                <div class="app-version">
                    Version <t t-esc="appVersion"/>
                </div>
            </div>
        </div>`;
    
    setup() {
        super.setup();
        this.userMenuManager = new MobileUserMenuManager(this.env);
        this.state = useState({
            showSubmenu: null
        });
    }
    
    get userName() {
        return this.userMenuManager.formatUserDisplayName();
    }
    
    get userEmail() {
        return this.userMenuManager.getUserInfo().email;
    }
    
    get userCompany() {
        return this.userMenuManager.getUserInfo().companyName;
    }
    
    get userAvatarUrl() {
        return this.userMenuManager.getUserAvatarUrl();
    }
    
    get menuItems() {
        return this.userMenuManager.getUserMenuItems();
    }
    
    get appVersion() {
        return this.env.services.version || '1.0.0';
    }
    
    _onItemClicked(callback) {
        // 添加点击动画效果
        this.addClickAnimation();
        
        // 延迟执行回调以显示动画
        setTimeout(() => {
            super._onItemClicked(callback);
        }, 150);
    }
    
    addClickAnimation() {
        // 添加点击反馈动画
        const menuElement = this.el;
        if (menuElement) {
            menuElement.classList.add('menu-item-clicked');
            setTimeout(() => {
                menuElement.classList.remove('menu-item-clicked');
            }, 300);
        }
    }
    
    // 处理子菜单
    toggleSubmenu(itemId) {
        if (this.state.showSubmenu === itemId) {
            this.state.showSubmenu = null;
        } else {
            this.state.showSubmenu = itemId;
        }
    }
    
    // 处理长按事件
    onLongPress(item) {
        if (item.longPressAction) {
            item.longPressAction();
        }
    }
}

// 自定义用户菜单项配置
const customUserMenuConfig = {
    items: [
        {
            id: 'profile',
            name: 'My Profile',
            icon: 'fa-user',
            sequence: 10,
            callback: () => openUserProfile()
        },
        {
            id: 'settings',
            name: 'Settings',
            icon: 'fa-cog',
            sequence: 20,
            hasSubmenu: true,
            submenu: [
                {
                    id: 'preferences',
                    name: 'Preferences',
                    callback: () => openPreferences()
                },
                {
                    id: 'notifications',
                    name: 'Notifications',
                    callback: () => openNotificationSettings()
                }
            ]
        },
        {
            id: 'help',
            name: 'Help & Support',
            icon: 'fa-question-circle',
            sequence: 30,
            callback: () => openHelpCenter()
        },
        {
            id: 'logout',
            name: 'Log out',
            icon: 'fa-sign-out',
            sequence: 100,
            isDanger: true,
            callback: () => performLogout()
        }
    ]
};

// 应用自定义配置
function applyCustomUserMenuConfig(userMenuManager, config) {
    config.items.forEach(item => {
        userMenuManager.addMenuItem(item);
    });
}
```

## 技术特点

### 1. 继承扩展
- **基类继承**: 继承UserMenu的所有功能
- **模板重写**: 使用专门的汉堡菜单模板
- **方法重写**: 重写特定方法以适配移动端
- **功能保持**: 保持原有功能的同时进行优化

### 2. 移动端优化
- **简化交互**: 简化的点击处理逻辑
- **触摸友好**: 适配触摸屏操作
- **响应式**: 适应不同屏幕尺寸
- **性能优化**: 优化移动端性能

### 3. 模块化设计
- **单一职责**: 专注于汉堡菜单中的用户菜单功能
- **松耦合**: 与其他组件保持松耦合
- **可扩展**: 易于扩展和定制
- **可重用**: 可在不同场景中重用

### 4. 简洁实现
- **代码精简**: 仅14行代码实现核心功能
- **逻辑清晰**: 清晰的代码逻辑
- **易于维护**: 易于理解和维护
- **高效执行**: 高效的执行性能

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **功能继承**: 继承基类的所有功能
- **方法重写**: 重写特定方法以适配需求
- **模板替换**: 替换模板以改变外观

### 2. 适配器模式 (Adapter Pattern)
- **接口适配**: 适配移动端的交互接口
- **行为调整**: 调整行为以适应汉堡菜单环境
- **兼容性**: 保持与基类的兼容性

### 3. 模板方法模式 (Template Method Pattern)
- **框架提供**: 基类提供框架结构
- **具体实现**: 子类提供具体实现
- **扩展点**: 定义明确的扩展点

## 注意事项

1. **模板一致性**: 确保模板与基类功能的一致性
2. **移动端适配**: 考虑移动端的特殊需求
3. **性能优化**: 避免不必要的重复计算
4. **用户体验**: 提供良好的移动端用户体验

## 扩展建议

1. **动画效果**: 添加菜单项的动画效果
2. **手势支持**: 支持更多的手势操作
3. **个性化**: 支持用户个性化设置
4. **快捷操作**: 添加常用功能的快捷操作
5. **离线支持**: 支持离线模式下的基本功能

该汉堡用户菜单组件为Odoo Web移动端提供了简洁而有效的用户菜单功能，通过继承和适配确保了在汉堡菜单环境中的良好用户体验。

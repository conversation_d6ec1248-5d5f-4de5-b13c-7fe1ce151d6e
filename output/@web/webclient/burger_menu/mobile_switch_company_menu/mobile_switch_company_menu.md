# MobileSwitchCompanyMenu - 移动端公司切换菜单

## 概述

`mobile_switch_company_menu.js` 是 Odoo Web 客户端的移动端公司切换菜单组件，提供了在汉堡菜单中切换公司的功能。该模块包含26行代码，继承自SwitchCompanyMenu基类，专门用于移动设备上的公司切换操作，具备折叠展开、状态管理、移动端优化等特性，是汉堡菜单系统中多公司环境下的重要组件。

## 文件信息
- **路径**: `/web/static/src/webclient/burger_menu/mobile_switch_company_menu/mobile_switch_company_menu.js`
- **行数**: 26
- **模块**: `@web/webclient/burger_menu/mobile_switch_company_menu/mobile_switch_company_menu`

## 依赖关系

```javascript
// 核心依赖
'@web/webclient/switch_company_menu/switch_company_menu'  // 公司切换菜单基类
```

## 主组件定义

### 1. MobileSwitchCompanyMenu 组件

```javascript
class MobileSwitchCompanyMenu extends SwitchCompanyMenu {
    static template = "web.MobileSwitchCompanyMenu";

    setup() {
        super.setup();
        this.state.isOpen = false;
    }

    get show() {
        return !this.hasLotsOfCompanies || this.state.isOpen === true;
    }

    toggleCollapsible() {
        if (this.hasLotsOfCompanies) {
            this.state.isOpen = !this.state.isOpen;
        }
    }
}
```

**组件特性**:
- **继承基类**: 继承SwitchCompanyMenu的所有功能
- **移动端优化**: 专门为移动端设计的交互方式
- **折叠功能**: 支持公司列表的折叠和展开
- **状态管理**: 管理菜单的打开/关闭状态

## 核心属性

### 1. 状态属性

```javascript
// 组件状态
this.state.isOpen = false;  // 菜单是否展开
```

**状态属性功能**:
- **isOpen**: 控制公司列表的展开/折叠状态

### 2. 计算属性

```javascript
// 显示控制
get show() {
    return !this.hasLotsOfCompanies || this.state.isOpen === true;
}
```

**计算属性功能**:
- **show**: 决定是否显示公司列表
- **智能显示**: 根据公司数量和状态智能控制显示

## 核心功能

### 1. 折叠控制

```javascript
toggleCollapsible() {
    if (this.hasLotsOfCompanies) {
        this.state.isOpen = !this.state.isOpen;
    }
}
```

**折叠控制功能**:
- **条件切换**: 只有在公司数量较多时才允许折叠
- **状态切换**: 切换展开/折叠状态
- **用户体验**: 优化移动端的用户体验

### 2. 显示逻辑

```javascript
get show() {
    return !this.hasLotsOfCompanies || this.state.isOpen === true;
}
```

**显示逻辑功能**:
- **自动显示**: 公司数量少时自动显示所有公司
- **按需显示**: 公司数量多时按需显示
- **状态感知**: 根据展开状态控制显示

## 使用场景

### 1. 移动端公司管理器

```javascript
// 移动端公司管理器
class MobileCompanyManager {
    constructor(env) {
        this.env = env;
        this.companyService = env.services.company;
        this.companies = [];
        this.currentCompany = null;
        this.maxDisplayCompanies = 3; // 移动端最大显示公司数
        this.setupCompanyData();
    }
    
    // 设置公司数据
    async setupCompanyData() {
        try {
            this.companies = await this.companyService.getCompanies();
            this.currentCompany = this.companyService.currentCompany;
            this.sortCompaniesByUsage();
        } catch (error) {
            console.error('Failed to load companies:', error);
        }
    }
    
    // 按使用频率排序公司
    sortCompaniesByUsage() {
        const usage = this.getCompanyUsageStats();
        this.companies.sort((a, b) => {
            // 当前公司排在最前
            if (a.id === this.currentCompany.id) return -1;
            if (b.id === this.currentCompany.id) return 1;
            
            // 按使用频率排序
            const usageA = usage[a.id] || 0;
            const usageB = usage[b.id] || 0;
            return usageB - usageA;
        });
    }
    
    // 获取公司使用统计
    getCompanyUsageStats() {
        const stats = localStorage.getItem('company_usage_stats');
        return stats ? JSON.parse(stats) : {};
    }
    
    // 记录公司使用
    recordCompanyUsage(companyId) {
        const stats = this.getCompanyUsageStats();
        stats[companyId] = (stats[companyId] || 0) + 1;
        localStorage.setItem('company_usage_stats', JSON.stringify(stats));
    }
    
    // 切换公司
    async switchCompany(companyId) {
        try {
            await this.companyService.setCurrentCompany(companyId);
            this.currentCompany = this.companies.find(c => c.id === companyId);
            this.recordCompanyUsage(companyId);
            
            // 关闭汉堡菜单
            this.env.bus.trigger('BURGER_MENU:CLOSE');
            
            // 显示成功消息
            this.env.services.notification.add(
                `Switched to ${this.currentCompany.name}`,
                { type: 'success' }
            );
            
            // 重新加载页面以应用公司设置
            window.location.reload();
        } catch (error) {
            console.error('Failed to switch company:', error);
            this.env.services.notification.add(
                'Failed to switch company',
                { type: 'danger' }
            );
        }
    }
    
    // 获取显示的公司列表
    getDisplayCompanies() {
        return this.companies.slice(0, this.maxDisplayCompanies);
    }
    
    // 获取隐藏的公司列表
    getHiddenCompanies() {
        return this.companies.slice(this.maxDisplayCompanies);
    }
    
    // 检查是否有很多公司
    hasLotsOfCompanies() {
        return this.companies.length > this.maxDisplayCompanies;
    }
    
    // 搜索公司
    searchCompanies(query) {
        if (!query) return this.companies;
        
        const lowerQuery = query.toLowerCase();
        return this.companies.filter(company =>
            company.name.toLowerCase().includes(lowerQuery) ||
            (company.code && company.code.toLowerCase().includes(lowerQuery))
        );
    }
    
    // 获取公司信息
    getCompanyInfo(companyId) {
        const company = this.companies.find(c => c.id === companyId);
        if (!company) return null;
        
        return {
            id: company.id,
            name: company.name,
            code: company.code,
            logo: company.logo,
            currency: company.currency,
            timezone: company.timezone,
            isCurrent: company.id === this.currentCompany.id
        };
    }
    
    // 格式化公司显示名称
    formatCompanyDisplayName(company) {
        if (company.code) {
            return `${company.name} (${company.code})`;
        }
        return company.name;
    }
    
    // 获取公司Logo URL
    getCompanyLogoUrl(companyId) {
        return `/web/image/res.company/${companyId}/logo`;
    }
    
    // 检查公司访问权限
    async checkCompanyAccess(companyId) {
        try {
            const result = await this.env.services.rpc('/web/company/check_access', {
                company_id: companyId
            });
            return result.has_access;
        } catch (error) {
            console.error('Failed to check company access:', error);
            return false;
        }
    }
    
    // 获取公司统计信息
    async getCompanyStats(companyId) {
        try {
            const stats = await this.env.services.rpc('/web/company/stats', {
                company_id: companyId
            });
            return {
                userCount: stats.user_count,
                recordCount: stats.record_count,
                lastActivity: stats.last_activity
            };
        } catch (error) {
            console.error('Failed to get company stats:', error);
            return null;
        }
    }
}

// 使用示例
const companyManager = new MobileCompanyManager(env);

// 切换公司
await companyManager.switchCompany(123);

// 获取显示的公司
const displayCompanies = companyManager.getDisplayCompanies();

// 搜索公司
const searchResults = companyManager.searchCompanies('test');
```

### 2. 扩展的移动端公司切换菜单

```javascript
// 扩展的移动端公司切换菜单
class ExtendedMobileSwitchCompanyMenu extends MobileSwitchCompanyMenu {
    static template = xml`
        <div class="mobile-company-menu">
            <div class="company-header" t-if="hasLotsOfCompanies">
                <div class="current-company">
                    <img t-att-src="currentCompanyLogoUrl" class="company-logo"/>
                    <span class="company-name" t-esc="currentCompany.name"/>
                </div>
                <button class="toggle-btn" t-on-click="toggleCollapsible">
                    <i t-att-class="toggleIconClass"/>
                </button>
            </div>
            
            <div class="company-search" t-if="state.isOpen and hasLotsOfCompanies">
                <input type="text" 
                       placeholder="Search companies..."
                       t-model="state.searchQuery"
                       t-on-input="onSearchInput"
                       class="search-input"/>
            </div>
            
            <div class="company-list" t-if="show">
                <t t-foreach="filteredCompanies" t-as="company" t-key="company.id">
                    <div class="company-item" 
                         t-on-click="() => this.switchCompany(company.id)"
                         t-att-class="{ 'active': company.id === currentCompany.id }">
                        <img t-att-src="getCompanyLogoUrl(company.id)" class="company-logo"/>
                        <div class="company-info">
                            <div class="company-name" t-esc="company.name"/>
                            <div class="company-code" t-if="company.code" t-esc="company.code"/>
                        </div>
                        <i t-if="company.id === currentCompany.id" class="fa fa-check current-indicator"/>
                    </div>
                </t>
            </div>
            
            <div class="company-actions" t-if="state.isOpen">
                <button class="btn btn-sm btn-secondary" t-on-click="openCompanySettings">
                    <i class="fa fa-cog"/> Settings
                </button>
                <button class="btn btn-sm btn-primary" t-on-click="addNewCompany">
                    <i class="fa fa-plus"/> Add Company
                </button>
            </div>
        </div>`;
    
    setup() {
        super.setup();
        this.companyManager = new MobileCompanyManager(this.env);
        this.state = useState({
            ...this.state,
            searchQuery: '',
            isLoading: false
        });
    }
    
    get currentCompany() {
        return this.companyManager.currentCompany;
    }
    
    get currentCompanyLogoUrl() {
        return this.companyManager.getCompanyLogoUrl(this.currentCompany.id);
    }
    
    get toggleIconClass() {
        return this.state.isOpen ? 'fa fa-chevron-up' : 'fa fa-chevron-down';
    }
    
    get filteredCompanies() {
        if (!this.state.searchQuery) {
            return this.companyManager.companies;
        }
        return this.companyManager.searchCompanies(this.state.searchQuery);
    }
    
    // 搜索输入处理
    onSearchInput(event) {
        this.state.searchQuery = event.target.value;
    }
    
    // 切换公司
    async switchCompany(companyId) {
        if (companyId === this.currentCompany.id) return;
        
        this.state.isLoading = true;
        try {
            await this.companyManager.switchCompany(companyId);
        } finally {
            this.state.isLoading = false;
        }
    }
    
    // 获取公司Logo URL
    getCompanyLogoUrl(companyId) {
        return this.companyManager.getCompanyLogoUrl(companyId);
    }
    
    // 打开公司设置
    async openCompanySettings() {
        await this.env.services.action.doAction({
            type: 'ir.actions.act_window',
            res_model: 'res.company',
            view_mode: 'list,form',
            target: 'current'
        });
    }
    
    // 添加新公司
    async addNewCompany() {
        await this.env.services.action.doAction({
            type: 'ir.actions.act_window',
            res_model: 'res.company',
            view_mode: 'form',
            target: 'new'
        });
    }
    
    // 重写折叠切换以添加动画
    toggleCollapsible() {
        super.toggleCollapsible();
        this.addToggleAnimation();
    }
    
    // 添加切换动画
    addToggleAnimation() {
        const element = this.el.querySelector('.company-list');
        if (element) {
            element.style.transition = 'max-height 0.3s ease-in-out';
        }
    }
}

// 公司快速切换器
class CompanyQuickSwitcher {
    constructor(env) {
        this.env = env;
        this.recentCompanies = this.getRecentCompanies();
        this.favoriteCompanies = this.getFavoriteCompanies();
    }
    
    // 获取最近使用的公司
    getRecentCompanies() {
        const recent = localStorage.getItem('recent_companies');
        return recent ? JSON.parse(recent) : [];
    }
    
    // 获取收藏的公司
    getFavoriteCompanies() {
        const favorites = localStorage.getItem('favorite_companies');
        return favorites ? JSON.parse(favorites) : [];
    }
    
    // 添加到最近使用
    addToRecent(companyId) {
        let recent = this.getRecentCompanies();
        recent = recent.filter(id => id !== companyId);
        recent.unshift(companyId);
        recent = recent.slice(0, 5); // 保留最近5个
        localStorage.setItem('recent_companies', JSON.stringify(recent));
        this.recentCompanies = recent;
    }
    
    // 切换收藏状态
    toggleFavorite(companyId) {
        let favorites = this.getFavoriteCompanies();
        const index = favorites.indexOf(companyId);
        
        if (index === -1) {
            favorites.push(companyId);
        } else {
            favorites.splice(index, 1);
        }
        
        localStorage.setItem('favorite_companies', JSON.stringify(favorites));
        this.favoriteCompanies = favorites;
        
        return index === -1; // 返回是否添加到收藏
    }
    
    // 检查是否为收藏公司
    isFavorite(companyId) {
        return this.favoriteCompanies.includes(companyId);
    }
    
    // 获取快速访问公司列表
    getQuickAccessCompanies() {
        const recent = this.recentCompanies.slice(0, 3);
        const favorites = this.favoriteCompanies.slice(0, 3);
        
        // 合并并去重
        const combined = [...new Set([...favorites, ...recent])];
        return combined.slice(0, 5);
    }
}

// 使用示例
const quickSwitcher = new CompanyQuickSwitcher(env);

// 获取快速访问公司
const quickAccessCompanies = quickSwitcher.getQuickAccessCompanies();

// 切换收藏状态
const isFavorited = quickSwitcher.toggleFavorite(123);
console.log('Company favorited:', isFavorited);
```

## 技术特点

### 1. 继承扩展
- **功能继承**: 继承基类的所有公司切换功能
- **移动端适配**: 专门适配移动端的交互方式
- **状态扩展**: 扩展状态管理以支持折叠功能
- **模板定制**: 使用专门的移动端模板

### 2. 折叠机制
- **智能折叠**: 根据公司数量智能决定是否启用折叠
- **状态管理**: 完整的折叠/展开状态管理
- **用户体验**: 优化移动端的空间利用
- **性能优化**: 按需渲染公司列表

### 3. 移动端优化
- **触摸友好**: 适配触摸屏操作
- **空间节省**: 节省移动端宝贵的屏幕空间
- **响应式**: 适应不同屏幕尺寸
- **交互优化**: 优化移动端交互体验

### 4. 条件显示
- **智能显示**: 根据条件智能控制显示内容
- **性能考虑**: 避免渲染不必要的元素
- **用户体验**: 提供清晰的视觉层次
- **可访问性**: 保持良好的可访问性

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- **功能继承**: 继承基类的公司切换功能
- **行为扩展**: 扩展移动端特有的行为
- **接口保持**: 保持与基类的接口一致性

### 2. 状态模式 (State Pattern)
- **状态管理**: 管理折叠/展开状态
- **行为变化**: 根据状态改变显示行为
- **状态转换**: 提供清晰的状态转换逻辑

### 3. 策略模式 (Strategy Pattern)
- **显示策略**: 不同情况下的显示策略
- **交互策略**: 移动端特有的交互策略
- **渲染策略**: 按需渲染的策略

## 注意事项

1. **性能优化**: 避免渲染过多的公司项目
2. **用户体验**: 提供清晰的折叠/展开指示
3. **状态同步**: 确保状态与显示的同步
4. **触摸优化**: 优化触摸操作的响应

## 扩展建议

1. **搜索功能**: 添加公司搜索功能
2. **收藏功能**: 支持收藏常用公司
3. **最近使用**: 显示最近使用的公司
4. **动画效果**: 添加折叠/展开动画
5. **快速切换**: 提供快速切换功能

该移动端公司切换菜单组件为Odoo Web移动端提供了高效的多公司环境下的公司切换功能，通过智能的折叠机制和移动端优化确保了良好的用户体验。

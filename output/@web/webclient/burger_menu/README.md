# Burger Menu - 汉堡菜单系统

## 概述

Burger Menu 是 Odoo Web 客户端的移动端汉堡菜单系统，提供了完整的移动端导航功能。该系统包含3个核心模块，总计约120行代码，专门为移动设备设计，具备全屏菜单、滑动手势、用户菜单、公司切换等特性，是Odoo Web移动端用户界面的重要组成部分。

## 目录结构

```
burger_menu/
├── burger_menu.js                                      # 汉堡菜单主组件 (76行)
├── burger_user_menu/
│   └── burger_user_menu.js                            # 汉堡用户菜单 (14行)
├── mobile_switch_company_menu/
│   └── mobile_switch_company_menu.js                  # 移动端公司切换菜单 (26行)
└── README.md                                           # 本文档
```

## 核心架构

### 1. 汉堡菜单系统层次结构

```
汉堡菜单系统 (Burger Menu System)
├── 主菜单组件 (Main Menu Component)
│   ├── BurgerMenu (汉堡菜单主组件)
│   ├── 滑动手势支持 (Swipe Gesture Support)
│   └── 事件监听管理 (Event Listener Management)
├── 用户菜单子系统 (User Menu Subsystem)
│   ├── BurgerUserMenu (汉堡用户菜单)
│   └── 用户操作处理 (User Action Handling)
└── 公司切换子系统 (Company Switch Subsystem)
    ├── MobileSwitchCompanyMenu (移动端公司切换菜单)
    ├── 折叠展开机制 (Collapse/Expand Mechanism)
    └── 公司管理功能 (Company Management Features)
```

**系统特性**:
- **移动端专用**: 专门为移动设备设计的菜单系统
- **全屏体验**: 提供沉浸式的全屏菜单体验
- **手势支持**: 完整的触摸手势支持
- **模块化**: 高度模块化的组件设计

### 2. 交互模式

```javascript
// 汉堡菜单交互流程
const burgerMenuInteraction = {
    // 1. 菜单激活
    activation: {
        trigger: '点击汉堡图标',
        gesture: '从左边缘滑动',
        keyboard: 'Alt + M'
    },
    
    // 2. 菜单显示
    display: {
        animation: '滑入动画',
        overlay: '半透明遮罩',
        focus: '自动聚焦第一项'
    },
    
    // 3. 用户交互
    interaction: {
        navigation: '触摸导航',
        selection: '点击选择',
        search: '搜索过滤'
    },
    
    // 4. 菜单关闭
    closure: {
        selection: '选择项目后自动关闭',
        gesture: '向左滑动关闭',
        outside: '点击外部区域关闭',
        escape: 'ESC键关闭'
    }
};
```

## 核心组件

### 1. BurgerMenu - 汉堡菜单主组件

**功能**: 移动端的全屏导航菜单
- **行数**: 76行
- **作用**: 提供移动端主要导航功能
- **特点**: 滑动手势支持、全屏显示

**核心功能**:
```javascript
// 菜单控制
_openBurger()                          // 打开菜单
_closeBurger()                         // 关闭菜单
toggleBurger()                         // 切换菜单状态

// 手势处理
_onSwipeStart(ev)                      // 滑动开始
_onSwipeEnd(ev)                        // 滑动结束
handleSwipeGesture(deltaX)             // 处理滑动手势

// 事件监听
onHomeMenuToggled()                    // 首页菜单切换
onActionManagerUpdate()                // 动作管理器更新
```

**技术特点**:
- **滑动阈值**: 100像素的滑动激活阈值
- **事件驱动**: 响应系统事件自动关闭
- **状态管理**: 完整的菜单状态管理
- **系统托盘**: 注册到系统托盘，序列号为0

### 2. BurgerUserMenu - 汉堡用户菜单

**功能**: 汉堡菜单中的用户菜单功能
- **行数**: 14行
- **作用**: 提供用户相关操作
- **特点**: 继承UserMenu，简化交互

**核心功能**:
```javascript
// 用户操作
_onItemClicked(callback)               // 菜单项点击处理
openUserProfile()                      // 打开用户资料
openPreferences()                      // 打开偏好设置
logout()                               // 用户登出
```

**技术特点**:
- **继承扩展**: 继承UserMenu的所有功能
- **简化交互**: 简化的点击处理逻辑
- **移动端优化**: 适配移动端交互模式
- **专用模板**: 使用专门的汉堡菜单模板

### 3. MobileSwitchCompanyMenu - 移动端公司切换菜单

**功能**: 移动端的公司切换功能
- **行数**: 26行
- **作用**: 多公司环境下的公司切换
- **特点**: 折叠展开、智能显示

**核心功能**:
```javascript
// 公司切换
switchCompany(companyId)               // 切换到指定公司
toggleCollapsible()                    // 切换折叠状态
get show()                             // 控制显示逻辑

// 状态管理
get hasLotsOfCompanies()               // 检查公司数量
get isOpen()                           // 获取展开状态
updateCompanyList()                    // 更新公司列表
```

**技术特点**:
- **智能折叠**: 根据公司数量智能决定是否折叠
- **状态管理**: 完整的展开/折叠状态管理
- **条件显示**: 根据条件智能控制显示
- **继承基类**: 继承SwitchCompanyMenu功能

## 技术特点

### 1. 移动端优化

```javascript
// 移动端特性
const mobileOptimizations = {
    // 触摸支持
    touch: {
        swipeGestures: '滑动手势支持',
        touchTargets: '大尺寸触摸目标',
        hapticFeedback: '触觉反馈'
    },
    
    // 屏幕适配
    responsive: {
        fullScreen: '全屏显示',
        orientation: '方向适配',
        safeArea: '安全区域支持'
    },
    
    // 性能优化
    performance: {
        lazyLoading: '懒加载',
        virtualScrolling: '虚拟滚动',
        memoryManagement: '内存管理'
    },
    
    // 用户体验
    ux: {
        animations: '流畅动画',
        feedback: '即时反馈',
        accessibility: '可访问性'
    }
};
```

### 2. 手势系统

```javascript
// 手势识别系统
class GestureRecognizer {
    constructor() {
        this.threshold = 100;           // 激活阈值
        this.restraint = 100;           // 约束范围
        this.allowedTime = 300;         // 允许时间
        this.velocity = 0.3;            // 最小速度
    }
    
    // 手势类型
    gestures = {
        swipeLeft: '向左滑动',
        swipeRight: '向右滑动',
        swipeUp: '向上滑动',
        swipeDown: '向下滑动',
        tap: '点击',
        longPress: '长按',
        pinch: '捏合',
        rotate: '旋转'
    };
    
    // 手势处理
    recognizeGesture(startPoint, endPoint, duration) {
        const deltaX = endPoint.x - startPoint.x;
        const deltaY = endPoint.y - startPoint.y;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        const velocity = distance / duration;
        
        if (velocity >= this.velocity && distance >= this.threshold) {
            if (Math.abs(deltaX) > Math.abs(deltaY)) {
                return deltaX > 0 ? 'swipeRight' : 'swipeLeft';
            } else {
                return deltaY > 0 ? 'swipeDown' : 'swipeUp';
            }
        }
        
        return duration > 500 ? 'longPress' : 'tap';
    }
}
```

### 3. 状态管理

```javascript
// 汉堡菜单状态管理
const burgerMenuState = {
    // 菜单状态
    menu: {
        isOpen: false,                  // 是否打开
        isAnimating: false,             // 是否在动画中
        activeSection: null,            // 活动分区
        scrollPosition: 0               // 滚动位置
    },
    
    // 用户菜单状态
    userMenu: {
        isExpanded: false,              // 是否展开
        selectedItem: null,             // 选中项
        recentActions: []               // 最近操作
    },
    
    // 公司菜单状态
    companyMenu: {
        isOpen: false,                  // 是否打开
        searchQuery: '',                // 搜索查询
        filteredCompanies: [],          // 过滤后的公司
        currentCompany: null            // 当前公司
    },
    
    // 交互状态
    interaction: {
        touchStartX: null,              // 触摸开始X坐标
        touchStartY: null,              // 触摸开始Y坐标
        touchStartTime: null,           // 触摸开始时间
        isDragging: false               // 是否在拖拽
    }
};
```

### 4. 事件系统

```javascript
// 汉堡菜单事件系统
const burgerMenuEvents = {
    // 菜单事件
    'BURGER_MENU:OPEN': '菜单打开',
    'BURGER_MENU:CLOSE': '菜单关闭',
    'BURGER_MENU:TOGGLE': '菜单切换',
    'BURGER_MENU:ANIMATION_START': '动画开始',
    'BURGER_MENU:ANIMATION_END': '动画结束',
    
    // 用户菜单事件
    'BURGER_USER_MENU:ITEM_CLICK': '用户菜单项点击',
    'BURGER_USER_MENU:PROFILE_OPEN': '用户资料打开',
    'BURGER_USER_MENU:LOGOUT': '用户登出',
    
    // 公司菜单事件
    'BURGER_COMPANY_MENU:TOGGLE': '公司菜单切换',
    'BURGER_COMPANY_MENU:COMPANY_SWITCH': '公司切换',
    'BURGER_COMPANY_MENU:SEARCH': '公司搜索',
    
    // 手势事件
    'BURGER_GESTURE:SWIPE_LEFT': '向左滑动',
    'BURGER_GESTURE:SWIPE_RIGHT': '向右滑动',
    'BURGER_GESTURE:TAP': '点击',
    'BURGER_GESTURE:LONG_PRESS': '长按'
};
```

## 使用场景

### 1. 移动端导航

```javascript
// 移动端导航使用
class MobileNavigation {
    constructor(env) {
        this.env = env;
        this.burgerMenu = null;
        this.setupNavigation();
    }
    
    setupNavigation() {
        // 注册汉堡菜单
        this.burgerMenu = new BurgerMenu();
        
        // 设置导航项
        this.setupNavigationItems();
        
        // 绑定事件
        this.bindEvents();
    }
    
    setupNavigationItems() {
        this.navigationItems = [
            { id: 'home', name: 'Home', icon: 'fa-home' },
            { id: 'apps', name: 'Apps', icon: 'fa-th' },
            { id: 'contacts', name: 'Contacts', icon: 'fa-users' },
            { id: 'calendar', name: 'Calendar', icon: 'fa-calendar' },
            { id: 'settings', name: 'Settings', icon: 'fa-cog' }
        ];
    }
    
    bindEvents() {
        // 监听菜单事件
        this.env.bus.addEventListener('BURGER_MENU:OPEN', () => {
            this.onMenuOpen();
        });
        
        this.env.bus.addEventListener('BURGER_MENU:CLOSE', () => {
            this.onMenuClose();
        });
    }
    
    onMenuOpen() {
        // 菜单打开时的处理
        document.body.classList.add('burger-menu-open');
        this.preventBodyScroll();
    }
    
    onMenuClose() {
        // 菜单关闭时的处理
        document.body.classList.remove('burger-menu-open');
        this.restoreBodyScroll();
    }
}
```

### 2. 多公司环境

```javascript
// 多公司环境下的使用
class MultiCompanyBurgerMenu {
    constructor(env) {
        this.env = env;
        this.companyService = env.services.company;
        this.setupMultiCompanyFeatures();
    }
    
    setupMultiCompanyFeatures() {
        // 设置公司切换菜单
        this.companySwitcher = new MobileSwitchCompanyMenu();
        
        // 监听公司切换事件
        this.env.bus.addEventListener('COMPANY:SWITCHED', (event) => {
            this.onCompanySwitched(event.detail);
        });
    }
    
    async onCompanySwitched(companyInfo) {
        // 更新菜单显示
        await this.updateMenuForCompany(companyInfo);
        
        // 关闭汉堡菜单
        this.closeBurgerMenu();
        
        // 显示切换成功消息
        this.showSwitchSuccessMessage(companyInfo);
    }
    
    async updateMenuForCompany(companyInfo) {
        // 更新公司相关的菜单项
        const companySpecificItems = await this.getCompanySpecificItems(companyInfo.id);
        this.updateNavigationItems(companySpecificItems);
    }
}
```

### 3. 自定义扩展

```javascript
// 自定义汉堡菜单扩展
class CustomBurgerMenu extends BurgerMenu {
    static template = xml`
        <div class="custom-burger-menu">
            <div class="menu-header">
                <div class="user-section">
                    <BurgerUserMenu/>
                </div>
                <div class="company-section" t-if="hasMultipleCompanies">
                    <MobileSwitchCompanyMenu/>
                </div>
            </div>
            
            <div class="menu-content">
                <div class="quick-actions">
                    <t t-foreach="quickActions" t-as="action" t-key="action.id">
                        <button class="quick-action-btn" t-on-click="() => this.executeQuickAction(action)">
                            <i t-att-class="action.icon"/>
                            <span t-esc="action.name"/>
                        </button>
                    </t>
                </div>
                
                <div class="navigation-items">
                    <t t-foreach="navigationItems" t-as="item" t-key="item.id">
                        <div class="nav-item" t-on-click="() => this.navigateToItem(item)">
                            <i t-att-class="item.icon"/>
                            <span t-esc="item.name"/>
                            <span t-if="item.badge" class="badge" t-esc="item.badge"/>
                        </div>
                    </t>
                </div>
            </div>
            
            <div class="menu-footer">
                <div class="app-info">
                    <span>Odoo v<t t-esc="appVersion"/></span>
                </div>
            </div>
        </div>`;
    
    setup() {
        super.setup();
        this.quickActions = this.getQuickActions();
        this.navigationItems = this.getNavigationItems();
    }
    
    getQuickActions() {
        return [
            { id: 'create', name: 'Create', icon: 'fa-plus' },
            { id: 'search', name: 'Search', icon: 'fa-search' },
            { id: 'notifications', name: 'Notifications', icon: 'fa-bell' }
        ];
    }
    
    getNavigationItems() {
        return [
            { id: 'dashboard', name: 'Dashboard', icon: 'fa-dashboard', badge: '3' },
            { id: 'sales', name: 'Sales', icon: 'fa-line-chart' },
            { id: 'purchases', name: 'Purchases', icon: 'fa-shopping-cart' },
            { id: 'inventory', name: 'Inventory', icon: 'fa-cubes' }
        ];
    }
    
    async executeQuickAction(action) {
        switch (action.id) {
            case 'create':
                await this.showCreateDialog();
                break;
            case 'search':
                await this.showGlobalSearch();
                break;
            case 'notifications':
                await this.showNotifications();
                break;
        }
        this._closeBurger();
    }
    
    async navigateToItem(item) {
        await this.env.services.action.doAction({
            type: 'ir.actions.client',
            tag: item.id,
            target: 'current'
        });
        this._closeBurger();
    }
}
```

## 设计模式

### 1. 组合模式 (Composite Pattern)
- **菜单结构**: 汉堡菜单包含多个子菜单组件
- **统一接口**: 所有菜单组件提供统一的操作接口
- **层次管理**: 管理菜单的层次结构

### 2. 观察者模式 (Observer Pattern)
- **事件监听**: 监听系统事件自动关闭菜单
- **状态同步**: 菜单状态与系统状态的同步
- **用户交互**: 响应用户交互事件

### 3. 策略模式 (Strategy Pattern)
- **显示策略**: 不同设备的显示策略
- **交互策略**: 不同的用户交互策略
- **动画策略**: 不同的动画效果策略

### 4. 状态模式 (State Pattern)
- **菜单状态**: 管理菜单的打开/关闭状态
- **折叠状态**: 管理子菜单的折叠状态
- **交互状态**: 管理用户交互状态

### 5. 命令模式 (Command Pattern)
- **菜单操作**: 将菜单操作封装为命令
- **撤销重做**: 支持操作的撤销和重做
- **批量操作**: 批量执行菜单操作

## 性能优化

### 1. 渲染优化
```javascript
// 虚拟滚动优化
const virtualScrolling = {
    itemHeight: 48,                     // 项目高度
    visibleItems: 10,                   // 可见项目数
    bufferSize: 3,                      // 缓冲区大小
    renderWindow: 16                    // 渲染窗口
};

// 懒加载优化
const lazyLoading = {
    threshold: 200,                     // 加载阈值
    batchSize: 20,                      // 批次大小
    preloadCount: 5                     // 预加载数量
};
```

### 2. 内存管理
```javascript
// 内存管理策略
const memoryManagement = {
    // 组件缓存
    componentCache: new Map(),
    maxCacheSize: 50,
    
    // 事件清理
    eventCleanup: () => {
        // 清理事件监听器
    },
    
    // 定时清理
    scheduleCleanup: () => {
        setInterval(() => {
            this.cleanupUnusedComponents();
        }, 60000);
    }
};
```

### 3. 动画优化
```javascript
// 动画性能优化
const animationOptimization = {
    // 使用CSS变换
    useTransform: true,
    
    // 硬件加速
    enableHardwareAcceleration: true,
    
    // 动画帧控制
    useRequestAnimationFrame: true,
    
    // 动画缓动
    easing: 'cubic-bezier(0.4, 0.0, 0.2, 1)'
};
```

## 最佳实践

### 1. 移动端适配
```javascript
// 移动端最佳实践
const mobileBestPractices = {
    // 触摸目标大小
    minTouchTarget: 44,                 // 最小44px
    
    // 手势识别
    gestureThreshold: 100,              // 手势阈值
    
    // 动画时长
    animationDuration: 300,             // 300ms动画
    
    // 反馈延迟
    feedbackDelay: 50                   // 50ms反馈延迟
};
```

### 2. 可访问性
```javascript
// 可访问性支持
const accessibilitySupport = {
    // 键盘导航
    keyboardNavigation: true,
    
    // 屏幕阅读器
    screenReaderSupport: true,
    
    // 焦点管理
    focusManagement: true,
    
    // ARIA标签
    ariaLabels: true
};
```

### 3. 错误处理
```javascript
// 错误处理策略
const errorHandling = {
    // 网络错误
    networkError: (error) => {
        console.error('Network error:', error);
        this.showOfflineMessage();
    },
    
    // 渲染错误
    renderError: (error) => {
        console.error('Render error:', error);
        this.showErrorBoundary();
    },
    
    // 用户操作错误
    userActionError: (error) => {
        console.error('User action error:', error);
        this.showUserFriendlyMessage();
    }
};
```

## 注意事项

1. **性能考虑**: 避免在菜单中渲染过多内容
2. **内存管理**: 及时清理不再使用的组件和事件
3. **用户体验**: 提供流畅的动画和即时反馈
4. **可访问性**: 确保键盘导航和屏幕阅读器支持
5. **兼容性**: 测试不同设备和浏览器的兼容性

## 总结

Burger Menu 汉堡菜单系统是 Odoo Web 移动端的核心导航组件，通过模块化的设计和完整的移动端优化，为用户提供了直观、高效的移动端导航体验。

**核心优势**:
- **移动端专用**: 专门为移动设备设计的导航系统
- **手势支持**: 完整的触摸手势支持
- **模块化**: 高度模块化的组件架构
- **性能优化**: 多层次的性能优化机制
- **用户体验**: 流畅的动画和即时反馈
- **可扩展**: 易于扩展和定制的架构

该系统通过汉堡菜单主组件、用户菜单子系统和公司切换子系统的有机结合，为Odoo Web移动端用户提供了完整的导航解决方案，确保了在移动设备上的良好用户体验和高效操作。

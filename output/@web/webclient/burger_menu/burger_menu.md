# BurgerMenu - 汉堡菜单组件

## 概述

`burger_menu.js` 是 Odoo Web 客户端的汉堡菜单组件，提供了移动端的全屏菜单功能。该模块包含76行代码，是一个OWL组件，专门用于移动设备上的导航菜单，具备滑动手势支持、全屏显示、用户菜单集成、公司切换等特性，是Odoo Web移动端用户界面的重要组成部分。

## 文件信息
- **路径**: `/web/static/src/webclient/burger_menu/burger_menu.js`
- **行数**: 76
- **模块**: `@web/webclient/burger_menu/burger_menu`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                                                    // 注册表系统
'@web/core/transition'                                                  // 过渡动画
'@web/core/user'                                                        // 用户服务
'@web/core/utils/hooks'                                                 // 钩子工具
'@web/webclient/burger_menu/burger_user_menu/burger_user_menu'          // 汉堡用户菜单
'@web/webclient/burger_menu/mobile_switch_company_menu/mobile_switch_company_menu' // 移动端公司切换菜单
'@odoo/owl'                                                             // OWL框架
```

## 常量定义

```javascript
// 滑动激活阈值
const SWIPE_ACTIVATION_THRESHOLD = 100;
```

**常量功能**:
- **SWIPE_ACTIVATION_THRESHOLD**: 滑动手势激活的最小距离（像素）

## 主组件定义

### 1. BurgerMenu 组件

```javascript
class BurgerMenu extends Component {
    static template = "web.BurgerMenu";
    static props = {};
    static components = {
        BurgerUserMenu,
        MobileSwitchCompanyMenu,
        Transition,
    };

    setup() {
        this.company = useService("company");
        this.user = user;
        this.state = useState({
            isBurgerOpened: false,
        });
        this.swipeStartX = null;

        onMounted(() => {
            this.env.bus.addEventListener("HOME-MENU:TOGGLED", () => {
                this._closeBurger();
            });
            this.env.bus.addEventListener("ACTION_MANAGER:UPDATE", ({ detail: req }) => {
                if (req.id) {
                    this._closeBurger();
                }
            });
        });
    }
}
```

**组件特性**:
- **移动端专用**: 专门为移动设备设计的菜单组件
- **全屏显示**: 打开时占据整个屏幕
- **手势支持**: 支持滑动手势操作
- **事件响应**: 响应系统事件自动关闭

## 核心属性

### 1. 状态属性

```javascript
// 组件状态
this.state = useState({
    isBurgerOpened: false,  // 菜单是否打开
});

// 滑动状态
this.swipeStartX = null;    // 滑动开始的X坐标
```

**状态属性功能**:
- **isBurgerOpened**: 控制菜单的打开/关闭状态
- **swipeStartX**: 记录滑动手势的起始位置

### 2. 服务属性

```javascript
// 服务引用
this.company = useService("company");  // 公司服务
this.user = user;                      // 用户信息
```

**服务属性功能**:
- **company**: 用于公司相关功能
- **user**: 用于用户信息显示

## 核心功能

### 1. 菜单控制

```javascript
// 关闭菜单
_closeBurger() {
    this.state.isBurgerOpened = false;
}

// 打开菜单
_openBurger() {
    this.state.isBurgerOpened = true;
}
```

**菜单控制功能**:
- **_closeBurger**: 关闭汉堡菜单
- **_openBurger**: 打开汉堡菜单
- **状态切换**: 通过状态控制菜单显示

### 2. 滑动手势

```javascript
// 滑动开始
_onSwipeStart(ev) {
    this.swipeStartX = ev.changedTouches[0].clientX;
}

// 滑动结束
_onSwipeEnd(ev) {
    if (!this.swipeStartX) {
        return;
    }
    const deltaX = ev.changedTouches[0].clientX - this.swipeStartX;
    if (deltaX < SWIPE_ACTIVATION_THRESHOLD) {
        return;
    }
    this._closeBurger();
    this.swipeStartX = null;
}
```

**滑动手势功能**:
- **触摸检测**: 检测触摸屏幕的滑动操作
- **距离计算**: 计算滑动的距离
- **阈值判断**: 判断是否达到激活阈值
- **自动关闭**: 滑动距离足够时自动关闭菜单

### 3. 事件监听

```javascript
onMounted(() => {
    // 监听首页菜单切换事件
    this.env.bus.addEventListener("HOME-MENU:TOGGLED", () => {
        this._closeBurger();
    });

    // 监听动作管理器更新事件
    this.env.bus.addEventListener("ACTION_MANAGER:UPDATE", ({ detail: req }) => {
        if (req.id) {
            this._closeBurger();
        }
    });
});
```

**事件监听功能**:
- **HOME-MENU:TOGGLED**: 首页菜单切换时自动关闭
- **ACTION_MANAGER:UPDATE**: 动作更新时自动关闭
- **自动关闭**: 在特定事件发生时自动关闭菜单

## 系统托盘注册

```javascript
const systrayItem = {
    Component: BurgerMenu,
};

registry.category("systray").add("burger_menu", systrayItem, { sequence: 0 });
```

**注册功能**:
- **系统托盘**: 注册到系统托盘
- **优先级**: 序列号为0，具有最高优先级
- **移动端**: 主要在移动端显示

## 使用场景

### 1. 移动端导航管理器

```javascript
// 移动端导航管理器
class MobileNavigationManager {
    constructor(env) {
        this.env = env;
        this.burgerMenu = null;
        this.isMenuOpen = false;
        this.gestureHandler = new GestureHandler();
        this.setupEventListeners();
    }

    // 设置事件监听器
    setupEventListeners() {
        // 监听菜单状态变化
        this.env.bus.addEventListener("BURGER_MENU:OPENED", () => {
            this.onMenuOpened();
        });

        this.env.bus.addEventListener("BURGER_MENU:CLOSED", () => {
            this.onMenuClosed();
        });

        // 监听设备方向变化
        window.addEventListener("orientationchange", () => {
            this.handleOrientationChange();
        });

        // 监听返回按钮
        window.addEventListener("popstate", (event) => {
            if (this.isMenuOpen) {
                this.closeMenu();
                event.preventDefault();
            }
        });
    }

    // 打开菜单
    openMenu() {
        if (this.burgerMenu) {
            this.burgerMenu._openBurger();
            this.isMenuOpen = true;
            this.addBodyClass();
            this.preventBodyScroll();
            this.env.bus.trigger("BURGER_MENU:OPENED");
        }
    }

    // 关闭菜单
    closeMenu() {
        if (this.burgerMenu) {
            this.burgerMenu._closeBurger();
            this.isMenuOpen = false;
            this.removeBodyClass();
            this.restoreBodyScroll();
            this.env.bus.trigger("BURGER_MENU:CLOSED");
        }
    }

    // 切换菜单状态
    toggleMenu() {
        if (this.isMenuOpen) {
            this.closeMenu();
        } else {
            this.openMenu();
        }
    }

    // 菜单打开时的处理
    onMenuOpened() {
        // 添加历史记录条目
        if (window.history.pushState) {
            window.history.pushState({ burgerMenu: true }, '', '');
        }

        // 设置焦点管理
        this.setupFocusManagement();

        // 禁用页面滚动
        document.body.style.overflow = 'hidden';

        // 添加键盘事件监听
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
    }

    // 菜单关闭时的处理
    onMenuClosed() {
        // 恢复页面滚动
        document.body.style.overflow = '';

        // 移除键盘事件监听
        document.removeEventListener('keydown', this.handleKeyDown.bind(this));

        // 恢复焦点
        this.restoreFocus();
    }

    // 处理键盘事件
    handleKeyDown(event) {
        if (event.key === 'Escape') {
            this.closeMenu();
        } else if (event.key === 'Tab') {
            this.handleTabNavigation(event);
        }
    }

    // 处理Tab导航
    handleTabNavigation(event) {
        const focusableElements = this.getFocusableElements();
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (event.shiftKey) {
            // Shift + Tab
            if (document.activeElement === firstElement) {
                event.preventDefault();
                lastElement.focus();
            }
        } else {
            // Tab
            if (document.activeElement === lastElement) {
                event.preventDefault();
                firstElement.focus();
            }
        }
    }

    // 获取可聚焦元素
    getFocusableElements() {
        const selector = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';
        const menuElement = document.querySelector('.o_burger_menu');
        return menuElement ? Array.from(menuElement.querySelectorAll(selector)) : [];
    }

    // 设置焦点管理
    setupFocusManagement() {
        this.previousActiveElement = document.activeElement;
        const firstFocusable = this.getFocusableElements()[0];
        if (firstFocusable) {
            firstFocusable.focus();
        }
    }

    // 恢复焦点
    restoreFocus() {
        if (this.previousActiveElement) {
            this.previousActiveElement.focus();
        }
    }

    // 处理设备方向变化
    handleOrientationChange() {
        // 延迟处理，等待方向变化完成
        setTimeout(() => {
            if (this.isMenuOpen) {
                this.adjustMenuLayout();
            }
        }, 100);
    }

    // 调整菜单布局
    adjustMenuLayout() {
        const menuElement = document.querySelector('.o_burger_menu');
        if (menuElement) {
            // 重新计算菜单高度
            menuElement.style.height = window.innerHeight + 'px';
        }
    }

    // 添加body类
    addBodyClass() {
        document.body.classList.add('o_burger_menu_opened');
    }

    // 移除body类
    removeBodyClass() {
        document.body.classList.remove('o_burger_menu_opened');
    }

    // 阻止body滚动
    preventBodyScroll() {
        this.originalBodyStyle = {
            overflow: document.body.style.overflow,
            position: document.body.style.position,
            top: document.body.style.top,
            width: document.body.style.width
        };

        const scrollY = window.scrollY;
        document.body.style.overflow = 'hidden';
        document.body.style.position = 'fixed';
        document.body.style.top = `-${scrollY}px`;
        document.body.style.width = '100%';
    }

    // 恢复body滚动
    restoreBodyScroll() {
        if (this.originalBodyStyle) {
            const scrollY = parseInt(document.body.style.top || '0') * -1;

            Object.assign(document.body.style, this.originalBodyStyle);

            window.scrollTo(0, scrollY);
            this.originalBodyStyle = null;
        }
    }

    // 注册菜单实例
    registerBurgerMenu(burgerMenuInstance) {
        this.burgerMenu = burgerMenuInstance;
    }

    // 检查是否为移动设备
    isMobileDevice() {
        return window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    // 获取菜单状态
    getMenuState() {
        return {
            isOpen: this.isMenuOpen,
            isMobile: this.isMobileDevice(),
            orientation: window.innerHeight > window.innerWidth ? 'portrait' : 'landscape'
        };
    }
}

// 手势处理器
class GestureHandler {
    constructor() {
        this.startX = null;
        this.startY = null;
        this.threshold = 100;
        this.restraint = 100;
        this.allowedTime = 300;
        this.startTime = null;
    }

    // 处理触摸开始
    handleTouchStart(event) {
        const touch = event.changedTouches[0];
        this.startX = touch.clientX;
        this.startY = touch.clientY;
        this.startTime = Date.now();
    }

    // 处理触摸结束
    handleTouchEnd(event, callback) {
        if (!this.startX || !this.startY) return;

        const touch = event.changedTouches[0];
        const distX = touch.clientX - this.startX;
        const distY = touch.clientY - this.startY;
        const elapsedTime = Date.now() - this.startTime;

        // 检查是否为有效的滑动手势
        if (elapsedTime <= this.allowedTime) {
            if (Math.abs(distX) >= this.threshold && Math.abs(distY) <= this.restraint) {
                const direction = distX > 0 ? 'right' : 'left';
                callback(direction, { distX, distY, elapsedTime });
            }
        }

        this.reset();
    }

    // 重置状态
    reset() {
        this.startX = null;
        this.startY = null;
        this.startTime = null;
    }
}

// 使用示例
const navigationManager = new MobileNavigationManager(env);

// 注册汉堡菜单实例
navigationManager.registerBurgerMenu(burgerMenuComponent);

// 打开菜单
navigationManager.openMenu();

// 关闭菜单
navigationManager.closeMenu();

// 获取菜单状态
const menuState = navigationManager.getMenuState();
console.log('Menu state:', menuState);
```
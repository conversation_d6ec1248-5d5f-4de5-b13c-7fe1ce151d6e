# @web/core/main_components_container.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/main_components_container.js`
- **原始路径**: `/web/static/src/core/main_components_container.js`
- **代码行数**: 49行
- **作用**: 提供主组件容器，管理全局级别的UI组件渲染和错误处理

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 主组件容器的设计原理和架构思想
- 全局组件注册和动态渲染机制
- 组件错误处理和容错策略
- 注册表驱动的组件管理模式
- 企业级应用的组件架构设计

## 📚 核心概念

### 什么是MainComponentsContainer？
MainComponentsContainer是Odoo Web框架中的**全局组件容器**，主要功能：
- **全局渲染**: 渲染注册到main_components类别的所有组件
- **错误隔离**: 为每个组件提供独立的错误边界
- **动态管理**: 支持组件的动态添加和移除
- **容错机制**: 故障组件不影响其他组件的正常运行

### 核心架构组成
```javascript
// 主组件注册表
const mainComponents = registry.category("main_components");

// 组件验证规则
mainComponents.addValidation({
    Component: { validate: (c) => c.prototype instanceof Component },
    props: { type: Object, optional: true }
});

// 容器组件结构
class MainComponentsContainer extends Component {
    static components = { ErrorHandler };
    static template = xml`
        <div class="o-main-components-container">
            <t t-foreach="Components.entries" t-as="C" t-key="C[0]">
                <ErrorHandler onError="error => this.handleComponentError(error, C)">
                    <t t-component="C[1].Component" t-props="C[1].props"/>
                </ErrorHandler>
            </t>
        </div>
    `;
}
```

### 基本使用模式
```javascript
// 注册全局组件
registry.category("main_components").add("notification_container", {
    Component: NotificationContainer,
    props: {}
});

registry.category("main_components").add("dialog_container", {
    Component: DialogContainer,
    props: { 
        closeOnClickAway: true 
    }
});

// 在应用根组件中使用
class App extends Component {
    static components = { MainComponentsContainer };
    static template = xml`
        <div class="o-app">
            <MainContent/>
            <MainComponentsContainer/>
        </div>
    `;
}
```

## 🔍 核心实现详解

### 1. 组件注册表配置

#### 注册表类别定义
```javascript
const mainComponents = registry.category("main_components");
```

这创建了一个专门用于主组件的注册表类别，所有需要全局渲染的组件都注册到这个类别中。

#### 验证规则设置
```javascript
mainComponents.addValidation({
    Component: { validate: (c) => c.prototype instanceof Component },
    props: { type: Object, optional: true }
});
```

**验证规则分析**：
- **Component验证**: 确保注册的是有效的Owl组件类
- **props验证**: props必须是对象类型，但是可选的
- **类型安全**: 在注册时就进行验证，避免运行时错误

### 2. MainComponentsContainer类实现

#### 组件配置
```javascript
class MainComponentsContainer extends Component {
    static components = { ErrorHandler };
    static props = {};
}
```

**设计特点**：
- **无props**: 容器本身不需要外部属性
- **ErrorHandler**: 使用错误处理组件包装每个子组件
- **纯容器**: 专注于组件的渲染和管理

#### 模板结构
```javascript
static template = xml`
    <div class="o-main-components-container">
        <t t-foreach="Components.entries" t-as="C" t-key="C[0]">
            <ErrorHandler onError="error => this.handleComponentError(error, C)">
                <t t-component="C[1].Component" t-props="C[1].props"/>
            </ErrorHandler>
        </t>
    </div>
`;
```

**模板分析**：
- **动态遍历**: 使用t-foreach遍历注册表中的所有组件
- **错误边界**: 每个组件都被ErrorHandler包装
- **动态组件**: 使用t-component动态渲染组件
- **属性传递**: 通过t-props传递组件属性

#### setup()方法
```javascript
setup() {
    this.Components = useRegistry(mainComponents);
}
```

**关键设计**：
- **响应式注册表**: 使用useRegistry钩子获取响应式的注册表
- **自动更新**: 注册表变化时组件会自动重新渲染

### 3. 错误处理机制

#### handleComponentError()方法
```javascript
handleComponentError(error, C) {
    // 1. 移除故障组件
    this.Components.entries.splice(this.Components.entries.indexOf(C), 1);
    
    // 2. 重新渲染
    this.render();
    
    // 3. 异步抛出错误
    Promise.resolve().then(() => {
        throw error;
    });
}
```

**错误处理策略**：
1. **故障隔离**: 立即移除出错的组件
2. **继续服务**: 重新渲染剩余的正常组件
3. **错误通知**: 在下一个tick中重新抛出错误，确保用户知道发生了问题
4. **渲染完整性**: 确保Owl完成当前渲染周期后再抛出错误

## 🎨 实际应用场景

### 1. 通知系统集成
```javascript
// 通知容器组件
class NotificationContainer extends Component {
    static template = xml`
        <div class="o-notification-container">
            <t t-foreach="notifications" t-as="notification" t-key="notification.id">
                <div class="notification" t-att-class="notification.type">
                    <span t-esc="notification.message"/>
                    <button t-on-click="() => this.close(notification.id)">×</button>
                </div>
            </t>
        </div>
    `;
    
    setup() {
        this.notification = useService("notification");
        this.notifications = useState([]);
        
        // 监听通知事件
        this.notification.bus.addEventListener("ADD", this.onNotificationAdd.bind(this));
    }
    
    onNotificationAdd(event) {
        this.notifications.push(event.detail);
        
        // 自动关闭
        if (event.detail.duration) {
            setTimeout(() => {
                this.close(event.detail.id);
            }, event.detail.duration);
        }
    }
    
    close(id) {
        const index = this.notifications.findIndex(n => n.id === id);
        if (index >= 0) {
            this.notifications.splice(index, 1);
        }
    }
}

// 注册通知容器
registry.category("main_components").add("notification_container", {
    Component: NotificationContainer,
    props: {}
});

// 通知服务
class NotificationService {
    constructor() {
        this.bus = new EventTarget();
        this.nextId = 1;
    }
    
    add(message, type = "info", duration = 3000) {
        const notification = {
            id: this.nextId++,
            message,
            type,
            duration,
            timestamp: Date.now()
        };
        
        this.bus.dispatchEvent(new CustomEvent("ADD", {
            detail: notification
        }));
        
        return notification.id;
    }
    
    success(message, duration) {
        return this.add(message, "success", duration);
    }
    
    error(message, duration = 5000) {
        return this.add(message, "error", duration);
    }
    
    warning(message, duration) {
        return this.add(message, "warning", duration);
    }
}

// 注册通知服务
registry.category("services").add("notification", {
    dependencies: [],
    start() {
        return new NotificationService();
    }
});
```

### 2. 对话框系统集成
```javascript
// 对话框容器组件
class DialogContainer extends Component {
    static template = xml`
        <div class="o-dialog-container">
            <t t-foreach="dialogs" t-as="dialog" t-key="dialog.id">
                <div class="dialog-backdrop" t-on-click="() => this.onBackdropClick(dialog)">
                    <div class="dialog-content" t-on-click.stop="">
                        <t t-component="dialog.Component" t-props="dialog.props"/>
                    </div>
                </div>
            </t>
        </div>
    `;
    
    setup() {
        this.dialog = useService("dialog");
        this.dialogs = useState([]);
        
        this.dialog.bus.addEventListener("ADD", this.onDialogAdd.bind(this));
        this.dialog.bus.addEventListener("REMOVE", this.onDialogRemove.bind(this));
    }
    
    onDialogAdd(event) {
        this.dialogs.push(event.detail);
        document.body.classList.add("modal-open");
    }
    
    onDialogRemove(event) {
        const index = this.dialogs.findIndex(d => d.id === event.detail.id);
        if (index >= 0) {
            this.dialogs.splice(index, 1);
        }
        
        if (this.dialogs.length === 0) {
            document.body.classList.remove("modal-open");
        }
    }
    
    onBackdropClick(dialog) {
        if (dialog.props.closeOnClickAway !== false) {
            this.dialog.close(dialog.id);
        }
    }
}

// 对话框服务
class DialogService {
    constructor() {
        this.bus = new EventTarget();
        this.dialogs = new Map();
        this.nextId = 1;
    }
    
    add(Component, props = {}, options = {}) {
        const dialog = {
            id: this.nextId++,
            Component,
            props: {
                ...props,
                close: () => this.close(dialog.id)
            },
            options
        };
        
        this.dialogs.set(dialog.id, dialog);
        
        this.bus.dispatchEvent(new CustomEvent("ADD", {
            detail: dialog
        }));
        
        return dialog.id;
    }
    
    close(id) {
        const dialog = this.dialogs.get(id);
        if (dialog) {
            this.dialogs.delete(id);
            
            this.bus.dispatchEvent(new CustomEvent("REMOVE", {
                detail: { id }
            }));
        }
    }
    
    closeAll() {
        for (const id of this.dialogs.keys()) {
            this.close(id);
        }
    }
}

// 注册对话框系统
registry.category("main_components").add("dialog_container", {
    Component: DialogContainer,
    props: {}
});

registry.category("services").add("dialog", {
    dependencies: [],
    start() {
        return new DialogService();
    }
});
```

### 3. 加载指示器集成
```javascript
// 加载指示器组件
class LoadingIndicator extends Component {
    static template = xml`
        <div class="o-loading-indicator" t-if="isLoading">
            <div class="loading-backdrop">
                <div class="loading-spinner">
                    <div class="spinner"></div>
                    <div class="loading-text" t-if="loadingText" t-esc="loadingText"/>
                </div>
            </div>
        </div>
    `;
    
    setup() {
        this.ui = useService("ui");
        this.state = useState({
            isLoading: false,
            loadingText: ""
        });
        
        this.ui.bus.addEventListener("LOADING_START", this.onLoadingStart.bind(this));
        this.ui.bus.addEventListener("LOADING_STOP", this.onLoadingStop.bind(this));
    }
    
    get isLoading() {
        return this.state.isLoading;
    }
    
    get loadingText() {
        return this.state.loadingText;
    }
    
    onLoadingStart(event) {
        this.state.isLoading = true;
        this.state.loadingText = event.detail.text || "";
    }
    
    onLoadingStop() {
        this.state.isLoading = false;
        this.state.loadingText = "";
    }
}

// UI服务扩展
class UIService {
    constructor() {
        this.bus = new EventTarget();
        this.loadingCount = 0;
    }
    
    startLoading(text = "") {
        this.loadingCount++;
        if (this.loadingCount === 1) {
            this.bus.dispatchEvent(new CustomEvent("LOADING_START", {
                detail: { text }
            }));
        }
    }
    
    stopLoading() {
        this.loadingCount = Math.max(0, this.loadingCount - 1);
        if (this.loadingCount === 0) {
            this.bus.dispatchEvent(new CustomEvent("LOADING_STOP"));
        }
    }
    
    async withLoading(promise, text = "") {
        this.startLoading(text);
        try {
            return await promise;
        } finally {
            this.stopLoading();
        }
    }
}

// 注册加载指示器
registry.category("main_components").add("loading_indicator", {
    Component: LoadingIndicator,
    props: {}
});
```

## 🛠️ 实践练习

### 练习1: 主组件管理器
```javascript
class MainComponentManager {
    constructor() {
        this.registry = registry.category("main_components");
        this.componentStates = new Map();
        this.listeners = new Map();
    }
    
    // 注册组件
    register(name, Component, props = {}, options = {}) {
        const componentConfig = {
            Component,
            props,
            enabled: options.enabled !== false,
            priority: options.priority || 0,
            dependencies: options.dependencies || [],
            conditions: options.conditions || []
        };
        
        this.componentStates.set(name, componentConfig);
        
        if (componentConfig.enabled && this.checkConditions(componentConfig)) {
            this.registry.add(name, {
                Component: componentConfig.Component,
                props: componentConfig.props
            });
        }
        
        return this;
    }
    
    // 启用组件
    enable(name) {
        const config = this.componentStates.get(name);
        if (config) {
            config.enabled = true;
            
            if (this.checkConditions(config)) {
                this.registry.add(name, {
                    Component: config.Component,
                    props: config.props
                });
            }
        }
        
        return this;
    }
    
    // 禁用组件
    disable(name) {
        const config = this.componentStates.get(name);
        if (config) {
            config.enabled = false;
            this.registry.remove(name);
        }
        
        return this;
    }
    
    // 更新组件属性
    updateProps(name, newProps) {
        const config = this.componentStates.get(name);
        if (config) {
            config.props = { ...config.props, ...newProps };
            
            if (config.enabled && this.registry.contains(name)) {
                this.registry.add(name, {
                    Component: config.Component,
                    props: config.props
                });
            }
        }
        
        return this;
    }
    
    // 检查条件
    checkConditions(config) {
        // 检查依赖
        for (const dep of config.dependencies) {
            if (!this.isEnabled(dep)) {
                return false;
            }
        }
        
        // 检查自定义条件
        for (const condition of config.conditions) {
            if (!condition()) {
                return false;
            }
        }
        
        return true;
    }
    
    // 检查组件是否启用
    isEnabled(name) {
        const config = this.componentStates.get(name);
        return config && config.enabled && this.registry.contains(name);
    }
    
    // 获取组件列表
    getComponents() {
        return Array.from(this.componentStates.entries()).map(([name, config]) => ({
            name,
            enabled: config.enabled,
            registered: this.registry.contains(name),
            priority: config.priority,
            dependencies: config.dependencies
        }));
    }
    
    // 按优先级排序
    sortByPriority() {
        const entries = Array.from(this.componentStates.entries())
            .sort(([,a], [,b]) => b.priority - a.priority);
        
        // 重新注册以应用新顺序
        this.registry.clear();
        for (const [name, config] of entries) {
            if (config.enabled && this.checkConditions(config)) {
                this.registry.add(name, {
                    Component: config.Component,
                    props: config.props
                });
            }
        }
        
        return this;
    }
    
    // 监听组件状态变化
    onStateChange(callback) {
        const id = Date.now() + Math.random();
        this.listeners.set(id, callback);
        
        return () => {
            this.listeners.delete(id);
        };
    }
    
    // 触发状态变化事件
    notifyStateChange(name, action, config) {
        for (const callback of this.listeners.values()) {
            try {
                callback({ name, action, config });
            } catch (error) {
                console.error('Component state change listener error:', error);
            }
        }
    }
    
    // 批量操作
    batch(operations) {
        for (const op of operations) {
            switch (op.type) {
                case 'register':
                    this.register(op.name, op.Component, op.props, op.options);
                    break;
                case 'enable':
                    this.enable(op.name);
                    break;
                case 'disable':
                    this.disable(op.name);
                    break;
                case 'updateProps':
                    this.updateProps(op.name, op.props);
                    break;
            }
        }
        
        return this;
    }
}

// 使用示例
const componentManager = new MainComponentManager();

// 注册组件
componentManager
    .register("notification_container", NotificationContainer, {}, {
        priority: 100,
        enabled: true
    })
    .register("dialog_container", DialogContainer, {}, {
        priority: 90,
        dependencies: ["notification_container"]
    })
    .register("loading_indicator", LoadingIndicator, {}, {
        priority: 80,
        conditions: [() => env.debug]
    });

// 监听状态变化
componentManager.onStateChange(({ name, action, config }) => {
    console.log(`Component ${name} ${action}:`, config);
});

// 批量操作
componentManager.batch([
    { type: 'enable', name: 'notification_container' },
    { type: 'updateProps', name: 'dialog_container', props: { theme: 'dark' } }
]);
```

### 练习2: 组件生命周期管理器
```javascript
class ComponentLifecycleManager {
    constructor() {
        this.registry = registry.category("main_components");
        this.lifecycleHooks = new Map();
        this.componentInstances = new Map();
        this.setupRegistryHooks();
    }

    setupRegistryHooks() {
        // 监听组件注册
        const originalAdd = this.registry.add.bind(this.registry);
        this.registry.add = (name, config) => {
            this.onComponentAdd(name, config);
            return originalAdd(name, config);
        };

        // 监听组件移除
        const originalRemove = this.registry.remove.bind(this.registry);
        this.registry.remove = (name) => {
            this.onComponentRemove(name);
            return originalRemove(name);
        };
    }

    // 注册生命周期钩子
    registerHooks(name, hooks) {
        this.lifecycleHooks.set(name, {
            onMount: hooks.onMount || (() => {}),
            onUnmount: hooks.onUnmount || (() => {}),
            onError: hooks.onError || (() => {}),
            onUpdate: hooks.onUpdate || (() => {})
        });

        return this;
    }

    // 组件添加时的处理
    onComponentAdd(name, config) {
        console.log(`Component ${name} registered`);

        // 包装组件以添加生命周期钩子
        const OriginalComponent = config.Component;
        const hooks = this.lifecycleHooks.get(name);

        if (hooks) {
            config.Component = class WrappedComponent extends OriginalComponent {
                setup() {
                    super.setup();

                    // 注册实例
                    this.lifecycleManager = ComponentLifecycleManager.getInstance();
                    this.lifecycleManager.componentInstances.set(name, this);

                    // 挂载钩子
                    onMounted(() => {
                        hooks.onMount(this);
                    });

                    // 卸载钩子
                    onWillUnmount(() => {
                        hooks.onUnmount(this);
                        this.lifecycleManager.componentInstances.delete(name);
                    });

                    // 更新钩子
                    onWillUpdateProps(() => {
                        hooks.onUpdate(this);
                    });
                }

                __handleError(error) {
                    hooks.onError(error, this);
                    return super.__handleError(error);
                }
            };
        }
    }

    // 组件移除时的处理
    onComponentRemove(name) {
        console.log(`Component ${name} unregistered`);

        const instance = this.componentInstances.get(name);
        if (instance) {
            const hooks = this.lifecycleHooks.get(name);
            if (hooks) {
                hooks.onUnmount(instance);
            }
            this.componentInstances.delete(name);
        }
    }

    // 获取组件实例
    getInstance(name) {
        return this.componentInstances.get(name);
    }

    // 获取所有活动组件
    getActiveComponents() {
        return Array.from(this.componentInstances.keys());
    }

    // 执行组件方法
    executeMethod(name, methodName, ...args) {
        const instance = this.componentInstances.get(name);
        if (instance && typeof instance[methodName] === 'function') {
            return instance[methodName](...args);
        }
        throw new Error(`Method ${methodName} not found on component ${name}`);
    }

    // 广播消息给所有组件
    broadcast(message, data) {
        for (const [name, instance] of this.componentInstances.entries()) {
            if (instance.onMessage) {
                try {
                    instance.onMessage(message, data);
                } catch (error) {
                    console.error(`Error broadcasting to ${name}:`, error);
                }
            }
        }
    }

    // 获取组件状态
    getComponentState(name) {
        const instance = this.componentInstances.get(name);
        if (instance && instance.state) {
            return { ...instance.state };
        }
        return null;
    }

    // 单例模式
    static getInstance() {
        if (!ComponentLifecycleManager.instance) {
            ComponentLifecycleManager.instance = new ComponentLifecycleManager();
        }
        return ComponentLifecycleManager.instance;
    }
}

// 使用示例
const lifecycleManager = ComponentLifecycleManager.getInstance();

// 注册生命周期钩子
lifecycleManager.registerHooks("notification_container", {
    onMount: (instance) => {
        console.log("Notification container mounted");
        instance.startPeriodicCleanup();
    },
    onUnmount: (instance) => {
        console.log("Notification container unmounted");
        instance.stopPeriodicCleanup();
    },
    onError: (error, instance) => {
        console.error("Notification container error:", error);
        instance.handleError(error);
    }
});

// 扩展通知容器以支持生命周期
class EnhancedNotificationContainer extends NotificationContainer {
    setup() {
        super.setup();
        this.cleanupInterval = null;
    }

    startPeriodicCleanup() {
        this.cleanupInterval = setInterval(() => {
            this.cleanupExpiredNotifications();
        }, 30000); // 每30秒清理一次
    }

    stopPeriodicCleanup() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }
    }

    cleanupExpiredNotifications() {
        const now = Date.now();
        this.notifications = this.notifications.filter(notification => {
            return !notification.expireAt || notification.expireAt > now;
        });
    }

    onMessage(message, data) {
        switch (message) {
            case 'CLEAR_ALL':
                this.notifications.splice(0);
                break;
            case 'ADD_NOTIFICATION':
                this.notifications.push(data);
                break;
        }
    }

    handleError(error) {
        // 错误恢复逻辑
        this.notifications.splice(0); // 清空可能损坏的通知
        this.add("System error occurred", "error");
    }
}
```

### 练习3: 组件性能监控器
```javascript
class ComponentPerformanceMonitor {
    constructor() {
        this.metrics = new Map();
        this.observers = new Map();
        this.isMonitoring = false;
    }

    // 开始监控
    startMonitoring() {
        if (this.isMonitoring) return;

        this.isMonitoring = true;
        this.setupPerformanceObserver();
        this.wrapMainComponentsContainer();

        console.log("Component performance monitoring started");
    }

    // 停止监控
    stopMonitoring() {
        this.isMonitoring = false;

        for (const observer of this.observers.values()) {
            observer.disconnect();
        }
        this.observers.clear();

        console.log("Component performance monitoring stopped");
    }

    // 设置性能观察器
    setupPerformanceObserver() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    this.recordPerformanceEntry(entry);
                }
            });

            observer.observe({ entryTypes: ['measure', 'navigation', 'paint'] });
            this.observers.set('performance', observer);
        }
    }

    // 包装主组件容器
    wrapMainComponentsContainer() {
        const originalContainer = MainComponentsContainer;

        class MonitoredMainComponentsContainer extends originalContainer {
            setup() {
                super.setup();
                this.performanceMonitor = ComponentPerformanceMonitor.getInstance();
            }

            async __render() {
                const startTime = performance.now();

                try {
                    const result = await super.__render();

                    const endTime = performance.now();
                    this.performanceMonitor.recordRenderTime('main_container', endTime - startTime);

                    return result;
                } catch (error) {
                    const endTime = performance.now();
                    this.performanceMonitor.recordRenderError('main_container', endTime - startTime, error);
                    throw error;
                }
            }

            handleComponentError(error, C) {
                this.performanceMonitor.recordComponentError(C[0], error);
                return super.handleComponentError(error, C);
            }
        }

        // 替换原始类
        window.MainComponentsContainer = MonitoredMainComponentsContainer;
    }

    // 记录渲染时间
    recordRenderTime(componentName, duration) {
        if (!this.metrics.has(componentName)) {
            this.metrics.set(componentName, {
                renderTimes: [],
                errorCount: 0,
                totalRenders: 0,
                averageRenderTime: 0,
                maxRenderTime: 0,
                minRenderTime: Infinity
            });
        }

        const metrics = this.metrics.get(componentName);
        metrics.renderTimes.push({
            duration,
            timestamp: Date.now()
        });

        metrics.totalRenders++;
        metrics.maxRenderTime = Math.max(metrics.maxRenderTime, duration);
        metrics.minRenderTime = Math.min(metrics.minRenderTime, duration);

        // 计算平均渲染时间（最近100次）
        const recentTimes = metrics.renderTimes.slice(-100);
        metrics.averageRenderTime = recentTimes.reduce((sum, entry) => sum + entry.duration, 0) / recentTimes.length;

        // 性能警告
        if (duration > 100) { // 100ms阈值
            console.warn(`Slow render detected for ${componentName}: ${duration.toFixed(2)}ms`);
        }
    }

    // 记录渲染错误
    recordRenderError(componentName, duration, error) {
        this.recordRenderTime(componentName, duration);

        const metrics = this.metrics.get(componentName);
        metrics.errorCount++;

        console.error(`Render error in ${componentName}:`, error);
    }

    // 记录组件错误
    recordComponentError(componentName, error) {
        if (!this.metrics.has(componentName)) {
            this.metrics.set(componentName, {
                renderTimes: [],
                errorCount: 0,
                totalRenders: 0,
                averageRenderTime: 0,
                maxRenderTime: 0,
                minRenderTime: Infinity
            });
        }

        const metrics = this.metrics.get(componentName);
        metrics.errorCount++;

        console.error(`Component error in ${componentName}:`, error);
    }

    // 记录性能条目
    recordPerformanceEntry(entry) {
        if (entry.entryType === 'measure' && entry.name.includes('component')) {
            this.recordRenderTime(entry.name, entry.duration);
        }
    }

    // 获取性能报告
    getPerformanceReport() {
        const report = {
            summary: {
                totalComponents: this.metrics.size,
                totalRenders: 0,
                totalErrors: 0,
                averageRenderTime: 0
            },
            components: {},
            recommendations: []
        };

        let totalRenderTime = 0;

        for (const [name, metrics] of this.metrics.entries()) {
            report.summary.totalRenders += metrics.totalRenders;
            report.summary.totalErrors += metrics.errorCount;
            totalRenderTime += metrics.averageRenderTime * metrics.totalRenders;

            report.components[name] = {
                ...metrics,
                errorRate: metrics.totalRenders > 0 ? (metrics.errorCount / metrics.totalRenders * 100).toFixed(2) + '%' : '0%',
                recentRenderTimes: metrics.renderTimes.slice(-10)
            };

            // 生成建议
            if (metrics.averageRenderTime > 50) {
                report.recommendations.push({
                    component: name,
                    type: 'performance',
                    message: `Component ${name} has slow average render time: ${metrics.averageRenderTime.toFixed(2)}ms`
                });
            }

            if (metrics.errorCount > 0) {
                report.recommendations.push({
                    component: name,
                    type: 'reliability',
                    message: `Component ${name} has ${metrics.errorCount} errors`
                });
            }
        }

        report.summary.averageRenderTime = report.summary.totalRenders > 0 ?
            totalRenderTime / report.summary.totalRenders : 0;

        return report;
    }

    // 清除指标
    clearMetrics() {
        this.metrics.clear();
    }

    // 导出指标
    exportMetrics() {
        return {
            timestamp: Date.now(),
            metrics: Object.fromEntries(this.metrics.entries())
        };
    }

    // 单例模式
    static getInstance() {
        if (!ComponentPerformanceMonitor.instance) {
            ComponentPerformanceMonitor.instance = new ComponentPerformanceMonitor();
        }
        return ComponentPerformanceMonitor.instance;
    }
}

// 使用示例
const performanceMonitor = ComponentPerformanceMonitor.getInstance();

// 开始监控
performanceMonitor.startMonitoring();

// 定期生成报告
setInterval(() => {
    const report = performanceMonitor.getPerformanceReport();
    console.log('Performance Report:', report);

    // 发送到分析服务
    if (report.recommendations.length > 0) {
        console.warn('Performance recommendations:', report.recommendations);
    }
}, 60000); // 每分钟生成一次报告
```

## 🔧 调试技巧

### 查看主组件状态
```javascript
function debugMainComponents() {
    const mainComponents = registry.category("main_components");

    console.group('Main Components Debug');
    console.log('Registered components:', mainComponents.getEntries());
    console.log('Component count:', mainComponents.getEntries().length);

    for (const [name, config] of mainComponents.getEntries()) {
        console.log(`Component ${name}:`, {
            Component: config.Component.name,
            props: config.props,
            hasErrorHandler: !!config.Component.components?.ErrorHandler
        });
    }

    console.groupEnd();
}

// 在控制台中调用
debugMainComponents();
```

### 监控组件错误
```javascript
function monitorComponentErrors() {
    const originalHandleError = MainComponentsContainer.prototype.handleComponentError;

    MainComponentsContainer.prototype.handleComponentError = function(error, C) {
        console.group(`Component Error: ${C[0]}`);
        console.error('Error:', error);
        console.log('Component config:', C[1]);
        console.log('Container state:', this);
        console.groupEnd();

        return originalHandleError.call(this, error, C);
    };
}

// 启用错误监控
monitorComponentErrors();
```

## 📊 性能考虑

### 优化策略
1. **错误隔离**: 每个组件独立的错误边界
2. **懒加载**: 按需注册和渲染组件
3. **内存管理**: 及时清理故障组件
4. **渲染优化**: 避免不必要的重新渲染

### 最佳实践
```javascript
// ✅ 好的做法：轻量级组件
registry.category("main_components").add("simple_notification", {
    Component: SimpleNotificationComponent,
    props: {}
});

// ❌ 不好的做法：重量级组件
registry.category("main_components").add("heavy_component", {
    Component: HeavyComponentWithManyDependencies,
    props: { complexData: massiveDataSet }
});

// ✅ 好的做法：条件注册
if (env.debug) {
    registry.category("main_components").add("debug_panel", {
        Component: DebugPanel,
        props: {}
    });
}

// ❌ 不好的做法：无条件注册所有组件
registry.category("main_components").add("debug_panel", {
    Component: DebugPanel,
    props: {}
});
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解主组件容器的设计原理和架构
- [ ] 掌握全局组件的注册和管理方法
- [ ] 理解组件错误处理和容错机制
- [ ] 能够创建自定义的组件管理系统
- [ ] 掌握组件性能监控和优化技巧
- [ ] 了解企业级应用的组件架构设计

## 🚀 下一步学习
学完MainComponentsContainer后，建议继续学习：
1. **错误处理** (`@web/core/utils/components.js`) - 深入理解ErrorHandler
2. **注册表系统** (回顾`@web/core/registry.js`) - 理解注册表的高级用法
3. **组件架构** (`@web/core/utils/components.js`) - 学习组件设计模式

## 💡 重要提示
- MainComponentsContainer是全局UI架构的核心
- 理解错误隔离对构建稳定应用很重要
- 组件注册表提供了灵活的组件管理机制
- 性能监控对大型应用至关重要

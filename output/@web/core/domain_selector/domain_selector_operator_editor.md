# DomainSelectorOperatorEditor - 域选择器操作符编辑器

## 概述

`domain_selector_operator_editor.js` 是 Odoo Web 核心模块的域选择器操作符编辑器，提供了根据字段类型获取可用操作符的功能。该模块定义了完整的字段类型到操作符的映射关系，支持标准字段、属性字段和特殊字段类型，为域选择器提供了准确的操作符选择，确保了域构建过程中操作符的正确性和完整性。

## 文件信息
- **路径**: `/web/static/src/core/domain_selector/domain_selector_operator_editor.js`
- **行数**: 108
- **模块**: `@web/core/domain_selector/domain_selector_operator_editor`

## 核心函数

### 1. getDomainDisplayedOperators

```javascript
function getDomainDisplayedOperators(fieldDef) {
    if (!fieldDef) {
        fieldDef = {};
    }
    const { type, is_property } = fieldDef;
    
    // 属性字段的特殊处理
    if (is_property) {
        switch (type) {
            case "many2many":
            case "tags":
                return ["in", "not in", "set", "not_set"];
            case "many2one":
            case "selection":
                return ["=", "!=", "set", "not_set"];
        }
    }
    
    // 标准字段类型处理
    switch (type) {
        case "boolean":
            return ["is", "is_not"];
        // ... 其他类型
    }
}
```

**函数功能**:
- **类型映射**: 根据字段类型返回可用操作符列表
- **属性字段**: 特殊处理属性字段的操作符
- **默认处理**: 提供未知类型的默认操作符
- **完整覆盖**: 覆盖所有Odoo字段类型

## 字段类型操作符映射

### 1. 布尔字段

```javascript
case "boolean":
    return ["is", "is_not"];
```

**布尔字段操作符**:
- **is**: 字段为真
- **is_not**: 字段为假
- **语义化**: 提供更直观的布尔操作

### 2. 选择字段

```javascript
case "selection":
    return ["=", "!=", "in", "not in", "set", "not_set"];
```

**选择字段操作符**:
- **=, !=**: 等于/不等于特定值
- **in, not in**: 在/不在选项列表中
- **set, not_set**: 字段是否已设置
- **完整支持**: 支持单选和多选操作

### 3. 文本字段

```javascript
case "char":
case "text":
case "html":
    return [
        "=", "!=",           // 精确匹配
        "ilike", "not ilike", // 模糊匹配
        "in", "not in",      // 列表匹配
        "set", "not_set",    // 设置状态
        "starts_with",       // 前缀匹配
        "ends_with",         // 后缀匹配
    ];
```

**文本字段操作符**:
- **精确匹配**: 等于/不等于操作
- **模糊匹配**: 包含/不包含操作
- **位置匹配**: 前缀和后缀匹配
- **列表操作**: 在/不在列表中
- **状态检查**: 是否设置值

### 4. 日期时间字段

```javascript
case "date":
case "datetime":
    return ["=", "!=", ">", ">=", "<", "<=", "between", "within", "set", "not_set"];
```

**日期时间操作符**:
- **比较操作**: 等于、大于、小于等比较
- **范围操作**: between指定范围
- **相对时间**: within相对时间范围
- **状态检查**: 是否设置日期

### 5. 数值字段

```javascript
case "integer":
case "float":
case "monetary":
    return [
        "=", "!=",           // 精确匹配
        ">", ">=", "<", "<=", // 比较操作
        "between",           // 范围操作
        "ilike", "not ilike", // 文本搜索
        "set", "not_set",    // 设置状态
    ];
```

**数值字段操作符**:
- **数值比较**: 完整的数值比较操作
- **范围查询**: between范围查询
- **文本搜索**: 支持数值的文本搜索
- **状态检查**: 是否设置数值

### 6. 关联字段

```javascript
case "many2one":
case "many2many":
case "one2many":
    return [
        "in", "not in",      // 记录列表操作
        "=", "!=",           // 精确匹配
        "ilike", "not ilike", // 名称搜索
        "set", "not_set",    // 关联状态
        "starts_with", "ends_with", // 名称匹配
        "any", "not any",    // 任意匹配
    ];
```

**关联字段操作符**:
- **记录操作**: 在/不在记录列表中
- **名称搜索**: 基于显示名称的搜索
- **关联状态**: 是否建立关联
- **模式匹配**: 名称的模式匹配
- **任意匹配**: 任意条件匹配

### 7. 特殊字段类型

```javascript
case "json":
    return ["=", "!=", "ilike", "not ilike", "set", "not_set"];

case "properties":
    return ["set", "not_set"];

case undefined:
    return ["="];
```

**特殊字段功能**:
- **JSON字段**: 支持JSON数据的基本操作
- **属性字段**: 仅支持设置状态检查
- **未定义类型**: 提供基本的等于操作

### 8. 属性字段特殊处理

```javascript
if (is_property) {
    switch (type) {
        case "many2many":
        case "tags":
            return ["in", "not in", "set", "not_set"];
        case "many2one":
        case "selection":
            return ["=", "!=", "set", "not_set"];
    }
}
```

**属性字段功能**:
- **简化操作**: 属性字段提供简化的操作符集合
- **类型区分**: 根据属性类型提供不同操作符
- **状态优先**: 重点关注设置状态操作

## 使用场景

### 1. 基础操作符获取

```javascript
// 基础操作符获取示例
class FieldOperatorSelector extends Component {
    setup() {
        this.state = useState({
            fieldType: 'char',
            availableOperators: [],
            selectedOperator: null
        });

        this.updateOperators();
    }

    updateOperators() {
        const fieldDef = { type: this.state.fieldType };
        this.state.availableOperators = getDomainDisplayedOperators(fieldDef);
        this.state.selectedOperator = this.state.availableOperators[0];
    }

    onFieldTypeChange(newType) {
        this.state.fieldType = newType;
        this.updateOperators();
    }

    render() {
        return xml`
            <div class="field-operator-selector">
                <div class="field-type-selector mb-3">
                    <label class="form-label">字段类型:</label>
                    <select 
                        class="form-select"
                        t-model="state.fieldType"
                        t-on-change="(ev) => this.onFieldTypeChange(ev.target.value)"
                    >
                        <option value="char">字符</option>
                        <option value="integer">整数</option>
                        <option value="boolean">布尔</option>
                        <option value="date">日期</option>
                        <option value="many2one">多对一</option>
                        <option value="selection">选择</option>
                    </select>
                </div>

                <div class="operator-selector">
                    <label class="form-label">可用操作符:</label>
                    <div class="operator-list">
                        <t t-foreach="state.availableOperators" t-as="operator" t-key="operator">
                            <button 
                                class="btn btn-outline-primary me-2 mb-2"
                                t-att-class="operator === state.selectedOperator ? 'active' : ''"
                                t-on-click="() => this.state.selectedOperator = operator"
                            >
                                <t t-esc="operator"/>
                            </button>
                        </t>
                    </div>
                </div>

                <div class="selected-operator mt-3">
                    <strong>选中的操作符: </strong>
                    <code t-esc="state.selectedOperator"/>
                </div>
            </div>
        `;
    }
}
```

### 2. 动态字段操作符管理

```javascript
// 动态字段操作符管理
class DynamicFieldOperatorManager extends Component {
    setup() {
        this.state = useState({
            fields: [
                { name: 'name', type: 'char', label: '名称' },
                { name: 'age', type: 'integer', label: '年龄' },
                { name: 'active', type: 'boolean', label: '活跃' },
                { name: 'partner_id', type: 'many2one', label: '合作伙伴' },
                { name: 'category_ids', type: 'many2many', label: '分类' }
            ],
            selectedField: null,
            operatorGroups: {}
        });

        this.loadOperatorGroups();
    }

    loadOperatorGroups() {
        const groups = {};
        this.state.fields.forEach(field => {
            const operators = getDomainDisplayedOperators({ type: field.type });
            groups[field.name] = {
                field: field,
                operators: operators,
                grouped: this.groupOperators(operators)
            };
        });
        this.state.operatorGroups = groups;
    }

    groupOperators(operators) {
        const groups = {
            comparison: [],
            text: [],
            list: [],
            state: [],
            special: []
        };

        operators.forEach(op => {
            if (['=', '!=', '>', '>=', '<', '<='].includes(op)) {
                groups.comparison.push(op);
            } else if (['ilike', 'not ilike', 'like', 'not like', 'starts_with', 'ends_with'].includes(op)) {
                groups.text.push(op);
            } else if (['in', 'not in', 'any', 'not any'].includes(op)) {
                groups.list.push(op);
            } else if (['set', 'not_set', 'is', 'is_not'].includes(op)) {
                groups.state.push(op);
            } else {
                groups.special.push(op);
            }
        });

        return groups;
    }

    selectField(fieldName) {
        this.state.selectedField = fieldName;
    }

    getOperatorDescription(operator) {
        const descriptions = {
            '=': '等于',
            '!=': '不等于',
            '>': '大于',
            '>=': '大于等于',
            '<': '小于',
            '<=': '小于等于',
            'ilike': '包含',
            'not ilike': '不包含',
            'in': '在列表中',
            'not in': '不在列表中',
            'set': '已设置',
            'not_set': '未设置',
            'is': '是',
            'is_not': '不是',
            'between': '在范围内',
            'within': '在时间范围内',
            'starts_with': '开始于',
            'ends_with': '结束于',
            'any': '任意匹配',
            'not any': '无匹配'
        };
        return descriptions[operator] || operator;
    }

    render() {
        const selectedGroup = this.state.selectedField ? 
            this.state.operatorGroups[this.state.selectedField] : null;

        return xml`
            <div class="dynamic-field-operator-manager">
                <h5 class="mb-4">动态字段操作符管理器</h5>
                
                <div class="row">
                    <div class="col-md-4">
                        <h6>字段列表</h6>
                        <div class="list-group">
                            <t t-foreach="state.fields" t-as="field" t-key="field.name">
                                <button 
                                    class="list-group-item list-group-item-action"
                                    t-att-class="state.selectedField === field.name ? 'active' : ''"
                                    t-on-click="() => this.selectField(field.name)"
                                >
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong t-esc="field.label"/>
                                            <br/>
                                            <small class="text-muted" t-esc="field.name"/>
                                        </div>
                                        <span class="badge bg-secondary" t-esc="field.type"/>
                                    </div>
                                </button>
                            </t>
                        </div>
                    </div>

                    <div class="col-md-8" t-if="selectedGroup">
                        <h6>操作符详情: <t t-esc="selectedGroup.field.label"/></h6>
                        
                        <div class="operator-groups">
                            <div class="group mb-3" t-if="selectedGroup.grouped.comparison.length">
                                <h6 class="text-primary">比较操作符</h6>
                                <div class="operator-badges">
                                    <t t-foreach="selectedGroup.grouped.comparison" t-as="op" t-key="op">
                                        <span 
                                            class="badge bg-primary me-2 mb-2"
                                            title="${this.getOperatorDescription(op)}"
                                        >
                                            <t t-esc="op"/>
                                        </span>
                                    </t>
                                </div>
                            </div>

                            <div class="group mb-3" t-if="selectedGroup.grouped.text.length">
                                <h6 class="text-success">文本操作符</h6>
                                <div class="operator-badges">
                                    <t t-foreach="selectedGroup.grouped.text" t-as="op" t-key="op">
                                        <span 
                                            class="badge bg-success me-2 mb-2"
                                            title="${this.getOperatorDescription(op)}"
                                        >
                                            <t t-esc="op"/>
                                        </span>
                                    </t>
                                </div>
                            </div>

                            <div class="group mb-3" t-if="selectedGroup.grouped.list.length">
                                <h6 class="text-warning">列表操作符</h6>
                                <div class="operator-badges">
                                    <t t-foreach="selectedGroup.grouped.list" t-as="op" t-key="op">
                                        <span 
                                            class="badge bg-warning me-2 mb-2"
                                            title="${this.getOperatorDescription(op)}"
                                        >
                                            <t t-esc="op"/>
                                        </span>
                                    </t>
                                </div>
                            </div>

                            <div class="group mb-3" t-if="selectedGroup.grouped.state.length">
                                <h6 class="text-info">状态操作符</h6>
                                <div class="operator-badges">
                                    <t t-foreach="selectedGroup.grouped.state" t-as="op" t-key="op">
                                        <span 
                                            class="badge bg-info me-2 mb-2"
                                            title="${this.getOperatorDescription(op)}"
                                        >
                                            <t t-esc="op"/>
                                        </span>
                                    </t>
                                </div>
                            </div>

                            <div class="group mb-3" t-if="selectedGroup.grouped.special.length">
                                <h6 class="text-secondary">特殊操作符</h6>
                                <div class="operator-badges">
                                    <t t-foreach="selectedGroup.grouped.special" t-as="op" t-key="op">
                                        <span 
                                            class="badge bg-secondary me-2 mb-2"
                                            title="${this.getOperatorDescription(op)}"
                                        >
                                            <t t-esc="op"/>
                                        </span>
                                    </t>
                                </div>
                            </div>
                        </div>

                        <div class="operator-summary mt-4">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">操作符统计</h6>
                                    <p class="card-text">
                                        总计: <strong>${selectedGroup.operators.length}</strong> 个操作符
                                    </p>
                                    <div class="row">
                                        <div class="col-6">
                                            <small class="text-muted">
                                                比较: ${selectedGroup.grouped.comparison.length}<br/>
                                                文本: ${selectedGroup.grouped.text.length}
                                            </small>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">
                                                列表: ${selectedGroup.grouped.list.length}<br/>
                                                状态: ${selectedGroup.grouped.state.length}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 完整的类型覆盖
- 支持所有Odoo字段类型
- 特殊处理属性字段
- 提供默认操作符

### 2. 操作符分类
- 比较操作符(=, !=, >, <等)
- 文本操作符(ilike, starts_with等)
- 列表操作符(in, not in等)
- 状态操作符(set, not_set等)

### 3. 语义化操作
- 布尔字段的is/is_not
- 关联字段的any/not any
- 时间字段的within操作

### 4. 扩展性设计
- 易于添加新字段类型
- 灵活的操作符配置
- 向后兼容性保证

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- 不同字段类型的操作符策略
- 可扩展的类型处理

### 2. 工厂模式 (Factory Pattern)
- 根据字段类型生成操作符列表
- 统一的创建接口

### 3. 映射模式 (Mapping Pattern)
- 字段类型到操作符的映射
- 清晰的对应关系

## 注意事项

1. **类型准确性**: 确保字段类型的准确识别
2. **操作符完整性**: 保持操作符列表的完整性
3. **向后兼容**: 保持与旧版本的兼容性
4. **性能考虑**: 避免重复的操作符计算

## 扩展建议

1. **自定义操作符**: 支持用户自定义操作符
2. **操作符分组**: 提供操作符的逻辑分组
3. **国际化**: 操作符的多语言支持
4. **动态配置**: 运行时的操作符配置
5. **权限控制**: 基于权限的操作符过滤

该域选择器操作符编辑器为Odoo Web应用提供了完整的字段类型到操作符的映射功能，通过精确的类型识别和丰富的操作符支持确保了域构建的准确性和灵活性。

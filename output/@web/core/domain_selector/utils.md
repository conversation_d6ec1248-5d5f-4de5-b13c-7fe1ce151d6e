# DomainSelectorUtils - 域选择器工具函数

## 概述

`utils.js` 是 Odoo Web 核心模块的域选择器工具函数库，提供了域选择器相关的实用工具函数。该模块包含默认条件生成、默认域创建和异步域获取等功能，为域选择器组件提供了便捷的工具支持，简化了域构建过程中的常见操作，确保了域选择器的易用性和一致性。

## 文件信息
- **路径**: `/web/static/src/core/domain_selector/utils.js`
- **行数**: 31
- **模块**: `@web/core/domain_selector/utils`

## 依赖关系

```javascript
// 核心依赖
'@web/core/tree_editor/tree_editor_value_editors'    // 值编辑器
'@web/core/domain_selector/domain_selector_operator_editor' // 操作符编辑器
'@web/core/utils/hooks'                              // 工具钩子
'@web/core/tree_editor/condition_tree'               // 条件树
'@web/core/tree_editor/utils'                        // 树编辑器工具
```

## 核心工具函数

### 1. getDefaultCondition

```javascript
function getDefaultCondition(fieldDefs) {
    const defaultPath = getDefaultPath(fieldDefs);
    const fieldDef = fieldDefs[defaultPath];
    const operator = getDomainDisplayedOperators(fieldDef)[0];
    const value = getDefaultValue(fieldDef, operator);
    return condition(fieldDef.name, operator, value);
}
```

**默认条件功能**:
- **路径选择**: 自动选择合适的默认字段路径
- **操作符获取**: 获取字段类型的第一个可用操作符
- **默认值**: 生成操作符对应的默认值
- **条件创建**: 创建完整的条件对象

**使用场景**:
- 域选择器的初始条件
- 新增条件时的默认设置
- 重置操作的默认状态

### 2. getDefaultDomain

```javascript
function getDefaultDomain(fieldDefs) {
    return domainFromTree(getDefaultCondition(fieldDefs));
}
```

**默认域功能**:
- **域转换**: 将默认条件转换为域字符串
- **简化接口**: 提供简化的域获取接口
- **一致性**: 确保默认域的一致性

**使用场景**:
- 初始化域选择器
- 重置域到默认状态
- 提供域的起始点

### 3. useGetDefaultLeafDomain

```javascript
function useGetDefaultLeafDomain() {
    const fieldService = useService("field");
    return async (resModel) => {
        const fieldDefs = await fieldService.loadFields(resModel);
        return getDefaultDomain(fieldDefs);
    };
}
```

**异步域钩子功能**:
- **服务集成**: 集成字段服务获取字段定义
- **异步处理**: 异步加载字段定义
- **钩子模式**: 提供可重用的钩子函数
- **模型感知**: 根据模型动态获取默认域

**使用场景**:
- 组件初始化时获取默认域
- 模型切换时更新默认域
- 异步的域初始化

## 使用场景

### 1. 基础默认条件生成

```javascript
// 基础默认条件生成示例
class DefaultConditionGenerator extends Component {
    setup() {
        this.fieldService = useService('field');
        this.state = useState({
            resModel: 'res.partner',
            fieldDefs: {},
            defaultCondition: null,
            defaultDomain: null
        });

        this.loadDefaultCondition();
    }

    async loadDefaultCondition() {
        try {
            const fieldDefs = await this.fieldService.loadFields(this.state.resModel);
            this.state.fieldDefs = fieldDefs;
            
            // 生成默认条件
            const defaultCondition = getDefaultCondition(fieldDefs);
            this.state.defaultCondition = defaultCondition;
            
            // 生成默认域
            const defaultDomain = getDefaultDomain(fieldDefs);
            this.state.defaultDomain = defaultDomain;
            
            console.log('Default condition:', defaultCondition);
            console.log('Default domain:', defaultDomain);
        } catch (error) {
            console.error('Failed to load default condition:', error);
        }
    }

    async changeModel(newModel) {
        this.state.resModel = newModel;
        await this.loadDefaultCondition();
    }

    render() {
        return xml`
            <div class="default-condition-generator">
                <h5>默认条件生成器</h5>
                
                <div class="model-selector mb-3">
                    <label class="form-label">选择模型:</label>
                    <select 
                        class="form-select"
                        t-model="state.resModel"
                        t-on-change="(ev) => this.changeModel(ev.target.value)"
                    >
                        <option value="res.partner">合作伙伴</option>
                        <option value="sale.order">销售订单</option>
                        <option value="product.product">产品</option>
                        <option value="res.users">用户</option>
                    </select>
                </div>

                <div class="condition-display" t-if="state.defaultCondition">
                    <h6>默认条件</h6>
                    <div class="card">
                        <div class="card-body">
                            <p><strong>字段:</strong> <code t-esc="state.defaultCondition.path"/></p>
                            <p><strong>操作符:</strong> <code t-esc="state.defaultCondition.operator"/></p>
                            <p><strong>值:</strong> <code t-esc="JSON.stringify(state.defaultCondition.value)"/></p>
                        </div>
                    </div>
                </div>

                <div class="domain-display mt-3" t-if="state.defaultDomain">
                    <h6>默认域</h6>
                    <div class="card">
                        <div class="card-body">
                            <pre t-esc="state.defaultDomain"/>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

### 2. 智能域初始化器

```javascript
// 智能域初始化器
class SmartDomainInitializer extends Component {
    setup() {
        this.getDefaultLeafDomain = useGetDefaultLeafDomain();
        this.state = useState({
            models: [
                { key: 'res.partner', name: '合作伙伴' },
                { key: 'sale.order', name: '销售订单' },
                { key: 'product.product', name: '产品' },
                { key: 'account.move', name: '会计凭证' }
            ],
            selectedModel: 'res.partner',
            domains: {},
            isLoading: false
        });

        this.initializeAllDomains();
    }

    async initializeAllDomains() {
        this.state.isLoading = true;
        const domains = {};

        try {
            // 并行加载所有模型的默认域
            const promises = this.state.models.map(async (model) => {
                const domain = await this.getDefaultLeafDomain(model.key);
                return { model: model.key, domain };
            });

            const results = await Promise.all(promises);
            results.forEach(({ model, domain }) => {
                domains[model] = domain;
            });

            this.state.domains = domains;
        } catch (error) {
            console.error('Failed to initialize domains:', error);
            this.notification.add('初始化域失败', { type: 'danger' });
        } finally {
            this.state.isLoading = false;
        }
    }

    async refreshDomain(modelKey) {
        try {
            const domain = await this.getDefaultLeafDomain(modelKey);
            this.state.domains[modelKey] = domain;
            this.notification.add(`${modelKey} 域已刷新`, { type: 'success' });
        } catch (error) {
            console.error(`Failed to refresh domain for ${modelKey}:`, error);
            this.notification.add('刷新域失败', { type: 'danger' });
        }
    }

    exportDomains() {
        const exportData = {
            timestamp: new Date().toISOString(),
            domains: this.state.domains
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json'
        });

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `default_domains_${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    render() {
        return xml`
            <div class="smart-domain-initializer">
                <div class="header mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>智能域初始化器</h5>
                        <div class="btn-group">
                            <button 
                                class="btn btn-primary"
                                t-on-click="initializeAllDomains"
                                t-att-disabled="state.isLoading"
                            >
                                <i class="fa fa-refresh"/> 刷新全部
                            </button>
                            <button 
                                class="btn btn-outline-secondary"
                                t-on-click="exportDomains"
                                t-att-disabled="Object.keys(state.domains).length === 0"
                            >
                                <i class="fa fa-download"/> 导出
                            </button>
                        </div>
                    </div>
                </div>

                <div class="loading-indicator text-center py-4" t-if="state.isLoading">
                    <i class="fa fa-spinner fa-spin fa-2x"/>
                    <p class="mt-2">正在加载默认域...</p>
                </div>

                <div class="domains-grid" t-if="!state.isLoading">
                    <div class="row">
                        <t t-foreach="state.models" t-as="model" t-key="model.key">
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0" t-esc="model.name"/>
                                        <button 
                                            class="btn btn-sm btn-outline-primary"
                                            t-on-click="() => this.refreshDomain(model.key)"
                                        >
                                            <i class="fa fa-refresh"/>
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <p class="card-text">
                                            <strong>模型:</strong> <code t-esc="model.key"/>
                                        </p>
                                        <div class="domain-content">
                                            <strong>默认域:</strong>
                                            <pre class="mt-2 bg-light p-2 rounded" t-if="state.domains[model.key]">
                                                <t t-esc="state.domains[model.key]"/>
                                            </pre>
                                            <div class="text-muted" t-else="">
                                                <em>暂无域数据</em>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>
                </div>

                <div class="statistics mt-4" t-if="Object.keys(state.domains).length">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">统计信息</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="stat-item">
                                        <div class="stat-value">${Object.keys(state.domains).length}</div>
                                        <div class="stat-label">已加载模型</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="stat-item">
                                        <div class="stat-value">${state.models.length}</div>
                                        <div class="stat-label">总模型数</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="stat-item">
                                        <div class="stat-value">
                                            ${Math.round((Object.keys(state.domains).length / state.models.length) * 100)}%
                                        </div>
                                        <div class="stat-label">完成率</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

### 3. 条件模板管理器

```javascript
// 条件模板管理器
class ConditionTemplateManager extends Component {
    setup() {
        this.fieldService = useService('field');
        this.state = useState({
            templates: [],
            selectedModel: 'res.partner',
            fieldDefs: {},
            isCreatingTemplate: false,
            newTemplateName: ''
        });

        this.loadFieldDefs();
        this.loadTemplates();
    }

    async loadFieldDefs() {
        try {
            const fieldDefs = await this.fieldService.loadFields(this.state.selectedModel);
            this.state.fieldDefs = fieldDefs;
        } catch (error) {
            console.error('Failed to load field definitions:', error);
        }
    }

    loadTemplates() {
        // 从本地存储加载模板
        const stored = localStorage.getItem('condition_templates');
        if (stored) {
            this.state.templates = JSON.parse(stored);
        }
    }

    saveTemplates() {
        localStorage.setItem('condition_templates', JSON.stringify(this.state.templates));
    }

    createDefaultTemplate() {
        if (!this.state.newTemplateName) {
            this.notification.add('请输入模板名称', { type: 'warning' });
            return;
        }

        const defaultCondition = getDefaultCondition(this.state.fieldDefs);
        const defaultDomain = getDefaultDomain(this.state.fieldDefs);

        const template = {
            id: Date.now(),
            name: this.state.newTemplateName,
            model: this.state.selectedModel,
            condition: defaultCondition,
            domain: defaultDomain,
            createdAt: new Date().toISOString()
        };

        this.state.templates.push(template);
        this.saveTemplates();
        
        this.state.newTemplateName = '';
        this.state.isCreatingTemplate = false;
        
        this.notification.add('模板创建成功', { type: 'success' });
    }

    deleteTemplate(templateId) {
        if (!confirm('确定要删除这个模板吗？')) return;

        this.state.templates = this.state.templates.filter(t => t.id !== templateId);
        this.saveTemplates();
        this.notification.add('模板删除成功', { type: 'success' });
    }

    applyTemplate(template) {
        console.log('Applying template:', template);
        // 这里可以触发应用模板的逻辑
        this.notification.add(`已应用模板: ${template.name}`, { type: 'info' });
    }

    async changeModel(newModel) {
        this.state.selectedModel = newModel;
        await this.loadFieldDefs();
    }

    render() {
        const modelTemplates = this.state.templates.filter(t => t.model === this.state.selectedModel);

        return xml`
            <div class="condition-template-manager">
                <div class="header mb-4">
                    <h5>条件模板管理器</h5>
                    <div class="model-selector mt-2">
                        <label class="form-label">当前模型:</label>
                        <select 
                            class="form-select"
                            t-model="state.selectedModel"
                            t-on-change="(ev) => this.changeModel(ev.target.value)"
                        >
                            <option value="res.partner">合作伙伴</option>
                            <option value="sale.order">销售订单</option>
                            <option value="product.product">产品</option>
                        </select>
                    </div>
                </div>

                <div class="template-creation mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">创建默认条件模板</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <input 
                                        type="text" 
                                        class="form-control"
                                        placeholder="输入模板名称"
                                        t-model="state.newTemplateName"
                                    />
                                </div>
                                <div class="col-md-4">
                                    <button 
                                        class="btn btn-primary w-100"
                                        t-on-click="createDefaultTemplate"
                                        t-att-disabled="!state.newTemplateName"
                                    >
                                        创建模板
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="templates-list">
                    <h6>模板列表 (${modelTemplates.length})</h6>
                    
                    <div class="templates-grid" t-if="modelTemplates.length">
                        <t t-foreach="modelTemplates" t-as="template" t-key="template.id">
                            <div class="card mb-3">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0" t-esc="template.name"/>
                                    <div class="btn-group">
                                        <button 
                                            class="btn btn-sm btn-outline-primary"
                                            t-on-click="() => this.applyTemplate(template)"
                                        >
                                            应用
                                        </button>
                                        <button 
                                            class="btn btn-sm btn-outline-danger"
                                            t-on-click="() => this.deleteTemplate(template.id)"
                                        >
                                            删除
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="template-details">
                                        <p><strong>模型:</strong> <code t-esc="template.model"/></p>
                                        <p><strong>条件:</strong></p>
                                        <ul class="list-unstyled ms-3">
                                            <li>字段: <code t-esc="template.condition.path"/></li>
                                            <li>操作符: <code t-esc="template.condition.operator"/></li>
                                            <li>值: <code t-esc="JSON.stringify(template.condition.value)"/></li>
                                        </ul>
                                        <p><strong>域:</strong></p>
                                        <pre class="bg-light p-2 rounded small" t-esc="template.domain"/>
                                        <p class="text-muted small mb-0">
                                            创建时间: ${new Date(template.createdAt).toLocaleString()}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>

                    <div class="empty-state text-center py-4" t-if="!modelTemplates.length">
                        <i class="fa fa-file-o fa-3x text-muted mb-3"/>
                        <h6 class="text-muted">暂无模板</h6>
                        <p class="text-muted">为当前模型创建第一个条件模板</p>
                    </div>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 简化接口
- 提供简洁的API接口
- 隐藏复杂的实现细节
- 统一的函数命名规范

### 2. 智能默认值
- 自动选择合适的默认字段
- 智能的操作符选择
- 合理的默认值生成

### 3. 异步支持
- 完整的异步操作支持
- 钩子模式的异步处理
- 错误处理和状态管理

### 4. 集成性
- 与树编辑器的深度集成
- 与字段服务的无缝集成
- 与操作符编辑器的协同工作

## 设计模式

### 1. 工厂模式 (Factory Pattern)
- 默认条件和域的创建
- 统一的创建接口

### 2. 钩子模式 (Hook Pattern)
- 可重用的异步域获取
- 组件间的逻辑共享

### 3. 适配器模式 (Adapter Pattern)
- 不同组件间的接口适配
- 统一的数据格式

## 注意事项

1. **字段定义**: 确保字段定义的正确加载
2. **错误处理**: 提供完善的错误处理机制
3. **性能优化**: 避免重复的字段定义加载
4. **缓存策略**: 合理使用缓存提升性能

## 扩展建议

1. **模板系统**: 默认条件的模板管理
2. **配置化**: 可配置的默认值策略
3. **智能推荐**: 基于历史的智能推荐
4. **批量操作**: 批量的默认条件生成
5. **验证机制**: 默认条件的有效性验证

该域选择器工具函数库为Odoo Web应用提供了便捷的域构建工具，通过智能的默认值生成和简化的API接口确保了域选择器的易用性和一致性。

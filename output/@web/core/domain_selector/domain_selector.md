# DomainSelector - 域选择器组件

## 概述

`domain_selector.js` 是 Odoo Web 核心模块的域选择器组件，提供了可视化的域编辑功能。该组件基于树编辑器构建，支持复杂域条件的创建和编辑，包括归档记录的处理、调试模式支持和实时域更新，为用户提供了直观的域构建界面，广泛应用于过滤器配置、搜索条件设置和业务规则定义等场景。

## 文件信息
- **路径**: `/web/static/src/core/domain_selector/domain_selector.js`
- **行数**: 169
- **模块**: `@web/core/domain_selector/domain_selector`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                      // OWL框架
'@web/core/domain'                               // 域处理核心
'@web/core/tree_editor/tree_editor'              // 树编辑器
'@web/core/tree_editor/condition_tree'           // 条件树
'@web/core/model_field_selector/utils'           // 字段选择器工具
'@web/core/checkbox/checkbox'                    // 复选框组件
'@web/core/utils/objects'                        // 对象工具
'@web/core/domain_selector/domain_selector_operator_editor' // 域操作符编辑器
'@web/core/tree_editor/tree_editor_operator_editor' // 树编辑器操作符编辑器
'@web/core/l10n/translation'                     // 国际化翻译
'@web/core/model_field_selector/model_field_selector' // 字段选择器
'@web/core/utils/hooks'                          // 工具钩子
'@web/core/tree_editor/utils'                    // 树编辑器工具
'@web/core/domain_selector/utils'                // 域选择器工具
```

## 核心常量

### 1. 归档条件定义

```javascript
const ARCHIVED_CONDITION = condition("active", "in", [true, false]);
const ARCHIVED_DOMAIN = `[("active", "in", [True, False])]`;
```

**归档常量功能**:
- **归档条件**: 定义包含归档记录的条件
- **域表示**: 归档条件的域字符串表示
- **标准化**: 提供统一的归档记录处理方式
- **兼容性**: 确保与Odoo域格式的兼容性

## 主组件类

### 1. 组件属性定义

```javascript
static props = {
    domain: String,                    // 域字符串
    resModel: String,                  // 资源模型名称
    className: { type: String, optional: true },        // CSS类名
    defaultConnector: { type: [{ value: "&" }, { value: "|" }], optional: true }, // 默认连接器
    isDebugMode: { type: Boolean, optional: true },     // 调试模式
    readonly: { type: Boolean, optional: true },        // 只读模式
    update: { type: Function, optional: true },         // 更新回调
    debugUpdate: { type: Function, optional: true },    // 调试更新回调
};

static defaultProps = {
    isDebugMode: false,
    readonly: true,
    update: () => {},
};
```

**属性功能**:
- **域管理**: domain定义要编辑的域字符串
- **模型信息**: resModel指定目标数据模型
- **模式控制**: readonly、isDebugMode控制组件行为
- **回调函数**: update和debugUpdate处理域变化
- **样式配置**: className和defaultConnector配置外观

### 2. 组件初始化

```javascript
setup() {
    this.fieldService = useService("field");
    this.loadFieldInfo = useLoadFieldInfo(this.fieldService);
    this.makeGetFieldDef = useMakeGetFieldDef(this.fieldService);

    this.tree = null;
    this.showArchivedCheckbox = false;
    this.includeArchived = false;

    onWillStart(() => this.onPropsUpdated(this.props));
    onWillUpdateProps((np) => this.onPropsUpdated(np));
}
```

**初始化功能**:
- **服务注入**: 注入字段服务和相关工具
- **状态初始化**: 初始化树结构和归档状态
- **生命周期**: 绑定属性更新处理
- **字段管理**: 准备字段定义获取功能

### 3. 属性更新处理

```javascript
async onPropsUpdated(p) {
    let domain;
    let isSupported = true;
    try {
        domain = new Domain(p.domain);
    } catch {
        isSupported = false;
    }
    if (!isSupported) {
        this.tree = null;
        this.showArchivedCheckbox = false;
        this.includeArchived = false;
        return;
    }

    const tree = treeFromDomain(domain);
    const getFieldDef = await this.makeGetFieldDef(p.resModel, tree, ["active"]);

    this.tree = treeFromDomain(domain, {
        getFieldDef,
        distributeNot: !p.isDebugMode,
    });

    this.showArchivedCheckbox = this.getShowArchivedCheckBox(Boolean(getFieldDef("active")), p);
    this.includeArchived = false;
    
    // 处理归档条件
    if (this.showArchivedCheckbox) {
        if (this.tree.value === "&") {
            this.tree.children = this.tree.children.filter((child) => {
                if (deepEqual(child, ARCHIVED_CONDITION)) {
                    this.includeArchived = true;
                    return false;
                }
                return true;
            });
            if (this.tree.children.length === 1) {
                this.tree = this.tree.children[0];
            }
        } else if (deepEqual(this.tree, ARCHIVED_CONDITION)) {
            this.includeArchived = true;
            this.tree = treeFromDomain(`[]`);
        }
    }
}
```

**更新处理功能**:
- **域解析**: 解析和验证输入的域字符串
- **错误处理**: 优雅处理无效的域格式
- **树转换**: 将域转换为可编辑的树结构
- **字段加载**: 异步加载字段定义信息
- **归档处理**: 智能处理归档记录的显示和编辑

## 核心方法

### 1. 编辑器配置方法

```javascript
// 获取默认条件
getDefaultCondition(fieldDefs) {
    return getDefaultCondition(fieldDefs);
}

// 获取默认操作符
getDefaultOperator(fieldDef) {
    return getDomainDisplayedOperators(fieldDef)[0];
}

// 获取操作符编辑器信息
getOperatorEditorInfo(fieldDef) {
    const operators = getDomainDisplayedOperators(fieldDef);
    return getOperatorEditorInfo(operators, fieldDef);
}

// 获取路径编辑器信息
getPathEditorInfo(resModel, defaultCondition) {
    const { isDebugMode } = this.props;
    return {
        component: ModelFieldSelector,
        extractProps: ({ update, value: path }) => {
            return {
                path,
                update,
                resModel,
                isDebugMode,
                readonly: false,
            };
        },
        isSupported: (path) => [0, 1].includes(path) || typeof path === "string",
        defaultValue: () => defaultCondition.path,
        stringify: (path) => formatValue(path),
        message: _t("Invalid field chain"),
    };
}
```

**配置方法功能**:
- **默认值**: 提供合理的默认条件和操作符
- **编辑器集成**: 配置各种编辑器组件
- **字段选择**: 集成字段选择器组件
- **验证支持**: 提供路径验证和错误消息

### 2. 归档记录处理

```javascript
// 获取是否显示归档复选框
getShowArchivedCheckBox(hasActiveField, props) {
    return hasActiveField;
}

// 切换包含归档记录
toggleIncludeArchived() {
    this.includeArchived = !this.includeArchived;
    this.update(this.tree);
}
```

**归档处理功能**:
- **条件检查**: 检查模型是否有active字段
- **状态切换**: 切换是否包含归档记录
- **自动更新**: 状态变化时自动更新域

### 3. 域更新方法

```javascript
// 重置域
resetDomain() {
    this.props.update("[]");
}

// 域输入处理
onDomainInput(domain) {
    if (this.props.debugUpdate) {
        this.props.debugUpdate(domain);
    }
}

// 域变化处理
onDomainChange(domain) {
    this.props.update(domain, true);
}

// 更新域
update(tree) {
    const archiveDomain = this.includeArchived ? ARCHIVED_DOMAIN : `[]`;
    const domain = tree
        ? Domain.and([domainFromTree(tree), archiveDomain]).toString()
        : archiveDomain;
    this.props.update(domain);
}
```

**更新方法功能**:
- **重置功能**: 重置域为空域
- **调试支持**: 支持调试模式的域更新
- **归档集成**: 自动集成归档条件到最终域
- **域合并**: 智能合并主域和归档域

## 使用场景

### 1. 基础域选择器

```javascript
// 基础域选择器使用
class BasicDomainSelector extends Component {
    setup() {
        this.state = useState({
            domain: "[]",
            resModel: "res.partner"
        });
    }

    updateDomain(newDomain) {
        this.state.domain = newDomain;
        this.onDomainChanged(newDomain);
    }

    onDomainChanged(domain) {
        console.log('Domain changed:', domain);
        // 处理域变化逻辑
    }

    render() {
        return xml`
            <div class="basic-domain-selector">
                <DomainSelector
                    domain="state.domain"
                    resModel="state.resModel"
                    update="updateDomain"
                    readonly="false"
                />
            </div>
        `;
    }
}
```

### 2. 高级过滤器配置器

```javascript
// 高级过滤器配置器
class AdvancedFilterConfigurator extends Component {
    setup() {
        this.state = useState({
            domain: "[]",
            resModel: "sale.order",
            isDebugMode: false,
            includeArchived: false,
            filterName: "",
            savedFilters: []
        });

        this.orm = useService('orm');
        this.notification = useService('notification');
    }

    updateDomain(newDomain, isDebugChange = false) {
        this.state.domain = newDomain;
        
        if (!isDebugChange) {
            this.validateDomain(newDomain);
        }
        
        this.onDomainChanged(newDomain);
    }

    debugUpdateDomain(newDomain) {
        // 调试模式下的域更新
        console.log('Debug domain update:', newDomain);
        this.state.domain = newDomain;
    }

    validateDomain(domain) {
        try {
            new Domain(domain);
            this.notification.add('域格式正确', { type: 'success' });
        } catch (error) {
            this.notification.add(
                `域格式错误: ${error.message}`,
                { type: 'danger' }
            );
        }
    }

    async saveFilter() {
        if (!this.state.filterName) {
            this.notification.add('请输入过滤器名称', { type: 'warning' });
            return;
        }

        try {
            await this.orm.create('ir.filters', [{
                name: this.state.filterName,
                model_id: this.state.resModel,
                domain: this.state.domain,
                user_id: this.env.user.userId
            }]);

            this.notification.add('过滤器保存成功', { type: 'success' });
            this.loadSavedFilters();
        } catch (error) {
            this.notification.add('保存过滤器失败', { type: 'danger' });
        }
    }

    async loadSavedFilters() {
        try {
            const filters = await this.orm.searchRead(
                'ir.filters',
                [['model_id', '=', this.state.resModel]],
                ['name', 'domain']
            );
            this.state.savedFilters = filters;
        } catch (error) {
            console.error('Failed to load filters:', error);
        }
    }

    loadFilter(filter) {
        this.state.domain = filter.domain;
        this.state.filterName = filter.name;
    }

    toggleDebugMode() {
        this.state.isDebugMode = !this.state.isDebugMode;
    }

    render() {
        return xml`
            <div class="advanced-filter-configurator">
                <div class="filter-header mb-3">
                    <div class="row">
                        <div class="col-md-6">
                            <input 
                                type="text" 
                                class="form-control"
                                placeholder="过滤器名称"
                                t-model="state.filterName"
                            />
                        </div>
                        <div class="col-md-6">
                            <div class="btn-group">
                                <button 
                                    class="btn btn-primary"
                                    t-on-click="saveFilter"
                                >
                                    保存过滤器
                                </button>
                                <button 
                                    class="btn btn-secondary"
                                    t-on-click="toggleDebugMode"
                                    t-att-class="state.isDebugMode ? 'active' : ''"
                                >
                                    调试模式
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <DomainSelector
                    domain="state.domain"
                    resModel="state.resModel"
                    update="updateDomain"
                    debugUpdate="debugUpdateDomain"
                    isDebugMode="state.isDebugMode"
                    readonly="false"
                    className="mb-3"
                />

                <div class="saved-filters" t-if="state.savedFilters.length">
                    <h6>已保存的过滤器</h6>
                    <div class="list-group">
                        <t t-foreach="state.savedFilters" t-as="filter" t-key="filter.id">
                            <button 
                                class="list-group-item list-group-item-action"
                                t-on-click="() => this.loadFilter(filter)"
                            >
                                <t t-esc="filter.name"/>
                            </button>
                        </t>
                    </div>
                </div>

                <div class="domain-preview mt-3" t-if="state.isDebugMode">
                    <h6>域预览</h6>
                    <pre class="bg-light p-2"><t t-esc="state.domain"/></pre>
                </div>
            </div>
        `;
    }
}
```

### 3. 动态域构建器

```javascript
// 动态域构建器
class DynamicDomainBuilder extends Component {
    setup() {
        this.state = useState({
            domains: [
                { id: 1, name: '主要条件', domain: '[]', resModel: 'res.partner' },
                { id: 2, name: '次要条件', domain: '[]', resModel: 'res.partner' }
            ],
            combinedDomain: '[]',
            combineOperator: '&'
        });
    }

    updateDomain(domainId, newDomain) {
        const domainObj = this.state.domains.find(d => d.id === domainId);
        if (domainObj) {
            domainObj.domain = newDomain;
            this.updateCombinedDomain();
        }
    }

    updateCombinedDomain() {
        const validDomains = this.state.domains
            .map(d => d.domain)
            .filter(domain => domain !== '[]');

        if (validDomains.length === 0) {
            this.state.combinedDomain = '[]';
        } else if (validDomains.length === 1) {
            this.state.combinedDomain = validDomains[0];
        } else {
            // 合并多个域
            const domains = validDomains.map(d => new Domain(d));
            const combined = this.state.combineOperator === '&'
                ? Domain.and(domains)
                : Domain.or(domains);
            this.state.combinedDomain = combined.toString();
        }
    }

    addDomain() {
        const newId = Math.max(...this.state.domains.map(d => d.id)) + 1;
        this.state.domains.push({
            id: newId,
            name: `条件 ${newId}`,
            domain: '[]',
            resModel: 'res.partner'
        });
    }

    removeDomain(domainId) {
        this.state.domains = this.state.domains.filter(d => d.id !== domainId);
        this.updateCombinedDomain();
    }

    setCombineOperator(operator) {
        this.state.combineOperator = operator;
        this.updateCombinedDomain();
    }

    render() {
        return xml`
            <div class="dynamic-domain-builder">
                <div class="builder-header mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>动态域构建器</h5>
                        <button 
                            class="btn btn-primary"
                            t-on-click="addDomain"
                        >
                            <i class="fa fa-plus"/> 添加条件
                        </button>
                    </div>
                </div>

                <div class="combine-operator mb-3">
                    <label class="form-label">组合方式:</label>
                    <div class="btn-group" role="group">
                        <input 
                            type="radio" 
                            class="btn-check" 
                            name="combineOp" 
                            id="andOp"
                            value="&amp;"
                            t-model="state.combineOperator"
                            t-on-change="() => this.setCombineOperator('&amp;')"
                        />
                        <label class="btn btn-outline-primary" for="andOp">AND (并且)</label>
                        
                        <input 
                            type="radio" 
                            class="btn-check" 
                            name="combineOp" 
                            id="orOp"
                            value="|"
                            t-model="state.combineOperator"
                            t-on-change="() => this.setCombineOperator('|')"
                        />
                        <label class="btn btn-outline-primary" for="orOp">OR (或者)</label>
                    </div>
                </div>

                <div class="domains-list">
                    <t t-foreach="state.domains" t-as="domainObj" t-key="domainObj.id">
                        <div class="domain-item card mb-3">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <input 
                                    type="text" 
                                    class="form-control form-control-sm"
                                    t-model="domainObj.name"
                                    style="max-width: 200px;"
                                />
                                <button 
                                    class="btn btn-sm btn-outline-danger"
                                    t-on-click="() => this.removeDomain(domainObj.id)"
                                    t-if="state.domains.length > 1"
                                >
                                    <i class="fa fa-trash"/>
                                </button>
                            </div>
                            <div class="card-body">
                                <DomainSelector
                                    domain="domainObj.domain"
                                    resModel="domainObj.resModel"
                                    update="(domain) => this.updateDomain(domainObj.id, domain)"
                                    readonly="false"
                                />
                            </div>
                        </div>
                    </t>
                </div>

                <div class="combined-result mt-4">
                    <h6>合并结果</h6>
                    <div class="card">
                        <div class="card-body">
                            <pre class="mb-0"><t t-esc="state.combinedDomain"/></pre>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 树编辑器集成
- 基于强大的树编辑器构建
- 支持复杂的嵌套条件
- 可视化的条件编辑界面

### 2. 归档记录处理
- 智能的归档记录检测
- 自动的归档条件分离
- 用户友好的归档选项

### 3. 调试模式支持
- 完整的调试模式支持
- 实时的域字符串预览
- 开发者友好的调试工具

### 4. 字段集成
- 完整的字段选择器集成
- 智能的字段类型检测
- 动态的操作符选择

## 设计模式

### 1. 组合模式 (Composite Pattern)
- 树编辑器的组合使用
- 复杂域的层次化构建

### 2. 策略模式 (Strategy Pattern)
- 不同字段类型的处理策略
- 可配置的编辑器选择

### 3. 观察者模式 (Observer Pattern)
- 域变化的通知机制
- 状态同步和更新

## 注意事项

1. **域格式**: 确保域字符串格式的正确性
2. **性能优化**: 避免频繁的域解析和转换
3. **错误处理**: 提供完善的错误处理和用户反馈
4. **兼容性**: 保持与Odoo域格式的兼容性

## 扩展建议

1. **模板系统**: 常用域的模板管理
2. **导入导出**: 域配置的导入导出功能
3. **历史记录**: 域编辑的历史记录
4. **批量操作**: 多个域的批量编辑
5. **可视化**: 域结构的图形化显示

该域选择器组件为Odoo Web应用提供了完整的域编辑解决方案，通过树编辑器集成和智能的归档处理确保了域构建的准确性和用户体验。

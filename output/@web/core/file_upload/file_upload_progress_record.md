# @web/core/file_upload/file_upload_progress_record.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/file_upload/file_upload_progress_record.js`
- **原始路径**: `/web/static/src/core/file_upload/file_upload_progress_record.js`
- **代码行数**: 50行
- **作用**: 实现文件上传进度记录组件，为不同视图类型提供专门的上传进度显示

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 组件继承和特化的设计模式
- 视图特定组件的实现策略
- 文件上传进度文本的格式化技术
- 组件模板的动态选择机制
- 国际化在进度显示中的应用

## 📚 核心概念

### 什么是文件上传进度记录？
文件上传进度记录是一套**视图特化的进度组件**，主要功能：
- **基础抽象**: 提供通用的进度文本格式化逻辑
- **视图适配**: 为不同视图类型提供专门的显示组件
- **进度计算**: 智能计算和格式化上传进度信息
- **状态展示**: 区分上传中和处理中的不同状态

### 核心架构组成
```javascript
// 组件继承层次
FileUploadProgressRecord                    // 基础抽象组件
├── FileUploadProgressKanbanRecord         // 看板视图专用
└── FileUploadProgressDataRow              // 列表视图专用

// 基础组件结构
const FileUploadProgressRecord = {
    static template: "",                    // 空模板（抽象组件）
    static components: {
        FileUploadProgressBar               // 复用进度条组件
    },
    static props: {
        fileUpload: Object,                 // 上传对象
        selector: String                    // 可选选择器
    },
    getProgressTexts()                      // 进度文本格式化
};

// 特化组件
const SpecializedComponents = {
    FileUploadProgressKanbanRecord: {
        template: "web.FileUploadProgressKanbanRecord"
    },
    FileUploadProgressDataRow: {
        template: "web.FileUploadProgressDataRow"
    }
};
```

### 基本使用模式
```javascript
import { 
    FileUploadProgressKanbanRecord,
    FileUploadProgressDataRow 
} from '@web/core/file_upload/file_upload_progress_record';

// 在看板视图中使用
<FileUploadProgressKanbanRecord 
    fileUpload={upload}
    selector=".o_kanban_record"/>

// 在列表视图中使用
<FileUploadProgressDataRow 
    fileUpload={upload}
    selector="tr.o_data_row"/>

// 获取进度文本
const progressTexts = component.getProgressTexts();
console.log(progressTexts.left);  // "Uploading... (45%)"
console.log(progressTexts.right); // "(2/5MB)"
```

## 🔍 核心实现详解

### 1. 基础抽象组件

#### 组件定义结构
```javascript
const FileUploadProgressRecord = class FileUploadProgressRecord extends Component {
    static template = "";
    static components = {
        FileUploadProgressBar,
    };
    static props = {
        fileUpload: Object,
        selector: { type: String, optional: true },
    };
}
```

**抽象组件特点**：
- **空模板**: 使用空字符串模板表示这是抽象组件
- **组件复用**: 复用FileUploadProgressBar组件
- **属性定义**: 定义通用的属性接口
- **继承基础**: 为特化组件提供继承基础

#### 属性系统设计
```javascript
static props = {
    fileUpload: Object,
    selector: { type: String, optional: true },
};
```

**属性设计特点**：
- **fileUpload**: 必需的上传对象，包含所有上传状态
- **selector**: 可选的CSS选择器，用于定位目标元素
- **类型安全**: 严格的属性类型验证
- **灵活性**: 可选属性提供使用灵活性

### 2. 进度文本格式化

#### 智能进度计算
```javascript
getProgressTexts() {
    const fileUpload = this.props.fileUpload;
    const percent = Math.round(fileUpload.progress * 100);
    if (percent === 100) {
        return {
            left: _t("Processing..."),
            right: "",
        };
    } else {
        const mbLoaded = Math.round(fileUpload.loaded / 1000000);
        const mbTotal = Math.round(fileUpload.total / 1000000);
        return {
            left: _t("Uploading... (%s%)", percent),
            right: _t("(%(mbLoaded)s/%(mbTotal)sMB)", { mbLoaded, mbTotal }),
        };
    }
}
```

**格式化逻辑特点**：
- **状态区分**: 区分上传中(Uploading)和处理中(Processing)状态
- **百分比计算**: 精确计算并四舍五入百分比
- **大小转换**: 将字节转换为MB单位显示
- **国际化**: 所有文本都支持国际化翻译

#### 文本结构设计
```javascript
// 上传中状态
{
    left: "Uploading... (45%)",           // 左侧：状态和百分比
    right: "(2/5MB)"                      // 右侧：已上传/总大小
}

// 处理中状态
{
    left: "Processing...",                // 左侧：处理状态
    right: ""                             // 右侧：空字符串
}
```

**文本结构特点**：
- **左右分离**: 将进度信息分为左右两部分
- **信息层次**: 左侧显示主要状态，右侧显示详细信息
- **状态适配**: 根据上传状态显示不同的文本内容
- **简洁明了**: 信息简洁但包含关键数据

### 3. 视图特化组件

#### 看板视图组件
```javascript
const FileUploadProgressKanbanRecord = class FileUploadProgressKanbanRecord extends FileUploadProgressRecord {
    static template = "web.FileUploadProgressKanbanRecord";
}
```

**看板组件特点**：
- **继承复用**: 继承基础组件的所有功能
- **模板特化**: 使用专门的看板模板
- **视图适配**: 适配看板视图的布局和样式
- **简洁实现**: 只需指定模板即可完成特化

#### 列表视图组件
```javascript
const FileUploadProgressDataRow = class FileUploadProgressDataRow extends FileUploadProgressRecord {
    static template = "web.FileUploadProgressDataRow";
}
```

**列表组件特点**：
- **表格适配**: 适配列表视图的表格布局
- **行级显示**: 在数据行中显示上传进度
- **模板特化**: 使用专门的数据行模板
- **一致性**: 保持与列表视图的视觉一致性

## 🎨 实际应用场景

### 1. 高级视图适配进度系统
```javascript
class AdvancedViewProgressSystem {
    constructor() {
        this.viewAdapters = new Map();
        this.progressFormatters = new Map();
        this.templateRegistry = new Map();
        this.setupViewAdapters();
        this.setupProgressFormatters();
    }
    
    setupViewAdapters() {
        // 看板视图适配器
        this.viewAdapters.set('kanban', {
            componentClass: EnhancedKanbanProgressRecord,
            template: 'web.EnhancedKanbanProgressRecord',
            containerSelector: '.o_kanban_record',
            positionStrategy: 'overlay',
            animationStyle: 'slide-up',
            autoHide: true,
            showThumbnail: true,
            compactMode: false
        });
        
        // 列表视图适配器
        this.viewAdapters.set('list', {
            componentClass: EnhancedListProgressRecord,
            template: 'web.EnhancedListProgressRecord',
            containerSelector: 'tr.o_data_row',
            positionStrategy: 'inline',
            animationStyle: 'fade-in',
            autoHide: false,
            showThumbnail: false,
            compactMode: true
        });
        
        // 表单视图适配器
        this.viewAdapters.set('form', {
            componentClass: EnhancedFormProgressRecord,
            template: 'web.EnhancedFormProgressRecord',
            containerSelector: '.o_form_view',
            positionStrategy: 'modal',
            animationStyle: 'zoom-in',
            autoHide: false,
            showThumbnail: true,
            compactMode: false
        });
        
        // 网格视图适配器
        this.viewAdapters.set('grid', {
            componentClass: EnhancedGridProgressRecord,
            template: 'web.EnhancedGridProgressRecord',
            containerSelector: '.o_grid_cell',
            positionStrategy: 'tooltip',
            animationStyle: 'bounce',
            autoHide: true,
            showThumbnail: false,
            compactMode: true
        });
        
        // 日历视图适配器
        this.viewAdapters.set('calendar', {
            componentClass: EnhancedCalendarProgressRecord,
            template: 'web.EnhancedCalendarProgressRecord',
            containerSelector: '.fc-event',
            positionStrategy: 'popover',
            animationStyle: 'slide-down',
            autoHide: true,
            showThumbnail: false,
            compactMode: true
        });
    }
    
    setupProgressFormatters() {
        // 标准格式化器
        this.progressFormatters.set('standard', {
            formatProgress: (upload) => {
                const percent = Math.round(upload.progress * 100);
                if (percent === 100) {
                    return {
                        left: _t("Processing..."),
                        right: "",
                        status: 'processing'
                    };
                } else {
                    const mbLoaded = Math.round(upload.loaded / 1000000);
                    const mbTotal = Math.round(upload.total / 1000000);
                    return {
                        left: _t("Uploading... (%s%)", percent),
                        right: _t("(%(mbLoaded)s/%(mbTotal)sMB)", { mbLoaded, mbTotal }),
                        status: 'uploading'
                    };
                }
            }
        });
        
        // 紧凑格式化器
        this.progressFormatters.set('compact', {
            formatProgress: (upload) => {
                const percent = Math.round(upload.progress * 100);
                if (percent === 100) {
                    return {
                        left: _t("Processing"),
                        right: "",
                        status: 'processing'
                    };
                } else {
                    return {
                        left: `${percent}%`,
                        right: this.formatFileSize(upload.loaded),
                        status: 'uploading'
                    };
                }
            }
        });
        
        // 详细格式化器
        this.progressFormatters.set('detailed', {
            formatProgress: (upload) => {
                const percent = Math.round(upload.progress * 100);
                const speed = this.calculateSpeed(upload);
                const eta = this.calculateETA(upload);
                
                if (percent === 100) {
                    return {
                        left: _t("Processing file..."),
                        right: _t("Almost done"),
                        status: 'processing',
                        details: {
                            speed: null,
                            eta: null,
                            totalTime: Date.now() - upload.startTime
                        }
                    };
                } else {
                    return {
                        left: _t("Uploading %(filename)s", { filename: upload.title }),
                        right: _t("%(percent)s% - %(speed)s", { 
                            percent, 
                            speed: this.formatSpeed(speed) 
                        }),
                        status: 'uploading',
                        details: {
                            speed,
                            eta,
                            loaded: upload.loaded,
                            total: upload.total
                        }
                    };
                }
            }
        });
        
        // 极简格式化器
        this.progressFormatters.set('minimal', {
            formatProgress: (upload) => {
                const percent = Math.round(upload.progress * 100);
                return {
                    left: percent === 100 ? "✓" : `${percent}%`,
                    right: "",
                    status: percent === 100 ? 'processing' : 'uploading'
                };
            }
        });
    }
    
    createProgressRecord(viewType, upload, options = {}) {
        const adapter = this.viewAdapters.get(viewType);
        if (!adapter) {
            throw new Error(`Unsupported view type: ${viewType}`);
        }
        
        const formatter = this.progressFormatters.get(
            options.formatter || (adapter.compactMode ? 'compact' : 'standard')
        );
        
        const progressRecord = new adapter.componentClass({
            fileUpload: upload,
            selector: options.selector || adapter.containerSelector,
            formatter: formatter,
            adapter: adapter,
            options: {
                ...adapter,
                ...options
            }
        });
        
        return progressRecord;
    }
    
    calculateSpeed(upload) {
        if (!upload.startTime || !upload.loaded) return 0;
        
        const elapsed = (Date.now() - upload.startTime) / 1000;
        return elapsed > 0 ? upload.loaded / elapsed : 0;
    }
    
    calculateETA(upload) {
        const speed = this.calculateSpeed(upload);
        if (speed === 0 || !upload.total) return null;
        
        const remaining = upload.total - upload.loaded;
        return remaining / speed;
    }
    
    formatSpeed(bytesPerSecond) {
        if (bytesPerSecond < 1024) {
            return `${bytesPerSecond.toFixed(0)} B/s`;
        } else if (bytesPerSecond < 1024 * 1024) {
            return `${(bytesPerSecond / 1024).toFixed(1)} KB/s`;
        } else {
            return `${(bytesPerSecond / (1024 * 1024)).toFixed(1)} MB/s`;
        }
    }
    
    formatFileSize(bytes) {
        if (bytes < 1024) {
            return `${bytes} B`;
        } else if (bytes < 1024 * 1024) {
            return `${(bytes / 1024).toFixed(1)} KB`;
        } else {
            return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
        }
    }
    
    formatETA(seconds) {
        if (!seconds || seconds < 0) return null;
        
        if (seconds < 60) {
            return `${Math.ceil(seconds)}s`;
        } else if (seconds < 3600) {
            return `${Math.ceil(seconds / 60)}m`;
        } else {
            return `${Math.ceil(seconds / 3600)}h`;
        }
    }
}

// 增强的进度记录组件基类
class EnhancedProgressRecord extends FileUploadProgressRecord {
    setup() {
        super.setup();
        this.state = useState({
            visible: true,
            animating: false,
            error: null
        });
        
        this.setupAnimations();
        this.setupEventListeners();
    }
    
    setupAnimations() {
        const adapter = this.props.adapter;
        
        // 入场动画
        useEffect(() => {
            if (this.el && adapter.animationStyle) {
                this.playEntranceAnimation();
            }
        }, () => []);
        
        // 自动隐藏
        if (adapter.autoHide) {
            useEffect(() => {
                const timer = setTimeout(() => {
                    this.hide();
                }, 5000);
                
                return () => clearTimeout(timer);
            }, () => []);
        }
    }
    
    setupEventListeners() {
        // 监听上传状态变化
        useEffect(() => {
            const upload = this.props.fileUpload;
            
            const handleProgress = () => {
                this.updateProgress();
            };
            
            const handleComplete = () => {
                this.onUploadComplete();
            };
            
            const handleError = () => {
                this.onUploadError();
            };
            
            // 添加事件监听器
            upload.addEventListener?.('progress', handleProgress);
            upload.addEventListener?.('complete', handleComplete);
            upload.addEventListener?.('error', handleError);
            
            return () => {
                upload.removeEventListener?.('progress', handleProgress);
                upload.removeEventListener?.('complete', handleComplete);
                upload.removeEventListener?.('error', handleError);
            };
        }, () => [this.props.fileUpload]);
    }
    
    getProgressTexts() {
        const formatter = this.props.formatter;
        return formatter.formatProgress(this.props.fileUpload);
    }
    
    playEntranceAnimation() {
        const animationStyle = this.props.adapter.animationStyle;
        
        this.state.animating = true;
        this.el.classList.add(`animate-${animationStyle}`);
        
        setTimeout(() => {
            this.state.animating = false;
            this.el.classList.remove(`animate-${animationStyle}`);
        }, 300);
    }
    
    playExitAnimation() {
        const animationStyle = this.props.adapter.animationStyle;
        
        this.state.animating = true;
        this.el.classList.add(`animate-${animationStyle}-reverse`);
        
        return new Promise(resolve => {
            setTimeout(() => {
                this.state.animating = false;
                resolve();
            }, 300);
        });
    }
    
    updateProgress() {
        // 触发重新渲染
        this.render();
    }
    
    onUploadComplete() {
        if (this.props.adapter.autoHide) {
            setTimeout(() => {
                this.hide();
            }, 2000);
        }
    }
    
    onUploadError() {
        this.state.error = true;
        // 显示错误状态
    }
    
    async hide() {
        await this.playExitAnimation();
        this.state.visible = false;
    }
    
    show() {
        this.state.visible = true;
        this.playEntranceAnimation();
    }
}

// 特化的视图组件
class EnhancedKanbanProgressRecord extends EnhancedProgressRecord {
    static template = "web.EnhancedKanbanProgressRecord";
    
    setup() {
        super.setup();
        this.setupKanbanSpecific();
    }
    
    setupKanbanSpecific() {
        // 看板特定的设置
        if (this.props.options.showThumbnail) {
            this.setupThumbnail();
        }
    }
    
    setupThumbnail() {
        const upload = this.props.fileUpload;
        if (upload.file && upload.file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (e) => {
                this.state.thumbnail = e.target.result;
            };
            reader.readAsDataURL(upload.file);
        }
    }
}

class EnhancedListProgressRecord extends EnhancedProgressRecord {
    static template = "web.EnhancedListProgressRecord";
    
    setup() {
        super.setup();
        this.setupListSpecific();
    }
    
    setupListSpecific() {
        // 列表特定的设置
        this.state.expanded = false;
    }
    
    toggleDetails() {
        this.state.expanded = !this.state.expanded;
    }
}

// 使用示例
const progressSystem = new AdvancedViewProgressSystem();

// 在不同视图中创建进度记录
const kanbanProgress = progressSystem.createProgressRecord('kanban', upload, {
    formatter: 'detailed',
    showThumbnail: true
});

const listProgress = progressSystem.createProgressRecord('list', upload, {
    formatter: 'compact'
});

const formProgress = progressSystem.createProgressRecord('form', upload, {
    formatter: 'standard',
    autoHide: false
});
```

### 2. 多语言进度文本系统
```javascript
class MultiLanguageProgressTextSystem {
    constructor() {
        this.textTemplates = new Map();
        this.formatters = new Map();
        this.localeSettings = new Map();
        this.setupTextTemplates();
        this.setupFormatters();
        this.setupLocaleSettings();
    }

    setupTextTemplates() {
        // 英语模板
        this.textTemplates.set('en', {
            uploading: "Uploading... (%s%)",
            processing: "Processing...",
            sizeInfo: "(%(loaded)s/%(total)s)",
            completed: "Upload completed",
            failed: "Upload failed",
            cancelled: "Upload cancelled",
            paused: "Upload paused",
            waiting: "Waiting...",
            preparing: "Preparing upload...",
            verifying: "Verifying file...",
            timeRemaining: "%(time)s remaining",
            speed: "%(speed)s/s",
            fileCount: "%(current)s of %(total)s files"
        });

        // 中文模板
        this.textTemplates.set('zh', {
            uploading: "上传中... (%s%)",
            processing: "处理中...",
            sizeInfo: "(%(loaded)s/%(total)s)",
            completed: "上传完成",
            failed: "上传失败",
            cancelled: "上传已取消",
            paused: "上传已暂停",
            waiting: "等待中...",
            preparing: "准备上传...",
            verifying: "验证文件中...",
            timeRemaining: "剩余 %(time)s",
            speed: "%(speed)s/秒",
            fileCount: "第 %(current)s 个，共 %(total)s 个文件"
        });

        // 法语模板
        this.textTemplates.set('fr', {
            uploading: "Téléchargement... (%s%)",
            processing: "Traitement...",
            sizeInfo: "(%(loaded)s/%(total)s)",
            completed: "Téléchargement terminé",
            failed: "Échec du téléchargement",
            cancelled: "Téléchargement annulé",
            paused: "Téléchargement en pause",
            waiting: "En attente...",
            preparing: "Préparation du téléchargement...",
            verifying: "Vérification du fichier...",
            timeRemaining: "%(time)s restant",
            speed: "%(speed)s/s",
            fileCount: "%(current)s sur %(total)s fichiers"
        });

        // 西班牙语模板
        this.textTemplates.set('es', {
            uploading: "Subiendo... (%s%)",
            processing: "Procesando...",
            sizeInfo: "(%(loaded)s/%(total)s)",
            completed: "Subida completada",
            failed: "Error en la subida",
            cancelled: "Subida cancelada",
            paused: "Subida pausada",
            waiting: "Esperando...",
            preparing: "Preparando subida...",
            verifying: "Verificando archivo...",
            timeRemaining: "%(time)s restante",
            speed: "%(speed)s/s",
            fileCount: "%(current)s de %(total)s archivos"
        });
    }

    setupFormatters() {
        // 数字格式化器
        this.formatters.set('number', {
            format: (value, locale) => {
                return new Intl.NumberFormat(locale).format(value);
            }
        });

        // 文件大小格式化器
        this.formatters.set('fileSize', {
            format: (bytes, locale) => {
                const units = this.getFileSizeUnits(locale);

                if (bytes === 0) return `0 ${units.bytes}`;

                const k = 1024;
                const sizes = [units.bytes, units.kb, units.mb, units.gb, units.tb];
                const i = Math.floor(Math.log(bytes) / Math.log(k));

                const value = parseFloat((bytes / Math.pow(k, i)).toFixed(2));
                const formattedValue = new Intl.NumberFormat(locale, {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 2
                }).format(value);

                return `${formattedValue} ${sizes[i]}`;
            }
        });

        // 时间格式化器
        this.formatters.set('time', {
            format: (seconds, locale) => {
                if (seconds < 60) {
                    const value = Math.ceil(seconds);
                    const unit = this.getTimeUnits(locale).seconds;
                    return `${value} ${unit}`;
                } else if (seconds < 3600) {
                    const value = Math.ceil(seconds / 60);
                    const unit = this.getTimeUnits(locale).minutes;
                    return `${value} ${unit}`;
                } else {
                    const value = Math.ceil(seconds / 3600);
                    const unit = this.getTimeUnits(locale).hours;
                    return `${value} ${unit}`;
                }
            }
        });

        // 速度格式化器
        this.formatters.set('speed', {
            format: (bytesPerSecond, locale) => {
                return this.formatters.get('fileSize').format(bytesPerSecond, locale);
            }
        });
    }

    setupLocaleSettings() {
        // 英语设置
        this.localeSettings.set('en', {
            fileSizeUnits: {
                bytes: 'B',
                kb: 'KB',
                mb: 'MB',
                gb: 'GB',
                tb: 'TB'
            },
            timeUnits: {
                seconds: 'sec',
                minutes: 'min',
                hours: 'hr'
            },
            decimalSeparator: '.',
            thousandsSeparator: ','
        });

        // 中文设置
        this.localeSettings.set('zh', {
            fileSizeUnits: {
                bytes: '字节',
                kb: 'KB',
                mb: 'MB',
                gb: 'GB',
                tb: 'TB'
            },
            timeUnits: {
                seconds: '秒',
                minutes: '分钟',
                hours: '小时'
            },
            decimalSeparator: '.',
            thousandsSeparator: ','
        });

        // 法语设置
        this.localeSettings.set('fr', {
            fileSizeUnits: {
                bytes: 'o',
                kb: 'Ko',
                mb: 'Mo',
                gb: 'Go',
                tb: 'To'
            },
            timeUnits: {
                seconds: 's',
                minutes: 'min',
                hours: 'h'
            },
            decimalSeparator: ',',
            thousandsSeparator: ' '
        });
    }

    getProgressTexts(upload, locale = 'en', options = {}) {
        const templates = this.textTemplates.get(locale) || this.textTemplates.get('en');
        const percent = Math.round(upload.progress * 100);

        if (percent === 100) {
            return {
                left: templates.processing,
                right: "",
                status: 'processing'
            };
        } else {
            const loadedSize = this.formatters.get('fileSize').format(upload.loaded, locale);
            const totalSize = this.formatters.get('fileSize').format(upload.total, locale);

            let leftText = templates.uploading.replace('%s', percent);
            let rightText = templates.sizeInfo
                .replace('%(loaded)s', loadedSize)
                .replace('%(total)s', totalSize);

            // 添加额外信息
            if (options.showSpeed && upload.speed) {
                const speed = this.formatters.get('speed').format(upload.speed, locale);
                rightText += ` - ${templates.speed.replace('%(speed)s', speed)}`;
            }

            if (options.showETA && upload.eta) {
                const eta = this.formatters.get('time').format(upload.eta, locale);
                rightText += ` - ${templates.timeRemaining.replace('%(time)s', eta)}`;
            }

            if (options.showFileCount && upload.fileIndex && upload.totalFiles) {
                const fileCountText = templates.fileCount
                    .replace('%(current)s', upload.fileIndex)
                    .replace('%(total)s', upload.totalFiles);
                leftText += ` - ${fileCountText}`;
            }

            return {
                left: leftText,
                right: rightText,
                status: 'uploading'
            };
        }
    }

    getFileSizeUnits(locale) {
        const settings = this.localeSettings.get(locale) || this.localeSettings.get('en');
        return settings.fileSizeUnits;
    }

    getTimeUnits(locale) {
        const settings = this.localeSettings.get(locale) || this.localeSettings.get('en');
        return settings.timeUnits;
    }

    // 状态文本获取
    getStatusText(status, locale = 'en') {
        const templates = this.textTemplates.get(locale) || this.textTemplates.get('en');
        return templates[status] || status;
    }

    // 自定义模板注册
    registerTemplate(locale, templates) {
        const existing = this.textTemplates.get(locale) || {};
        this.textTemplates.set(locale, { ...existing, ...templates });
    }

    // 自定义格式化器注册
    registerFormatter(name, formatter) {
        this.formatters.set(name, formatter);
    }
}

// 增强的进度记录组件（支持多语言）
class MultiLanguageProgressRecord extends FileUploadProgressRecord {
    setup() {
        super.setup();
        this.textSystem = new MultiLanguageProgressTextSystem();
        this.locale = this.env.services.localization?.locale || 'en';
    }

    getProgressTexts() {
        return this.textSystem.getProgressTexts(this.props.fileUpload, this.locale, {
            showSpeed: this.props.showSpeed,
            showETA: this.props.showETA,
            showFileCount: this.props.showFileCount
        });
    }

    getStatusText(status) {
        return this.textSystem.getStatusText(status, this.locale);
    }
}

// 使用示例
const textSystem = new MultiLanguageProgressTextSystem();

// 获取不同语言的进度文本
const upload = {
    progress: 0.45,
    loaded: 2 * 1024 * 1024,
    total: 5 * 1024 * 1024,
    speed: 1024 * 1024,
    eta: 3
};

const englishTexts = textSystem.getProgressTexts(upload, 'en', {
    showSpeed: true,
    showETA: true
});

const chineseTexts = textSystem.getProgressTexts(upload, 'zh', {
    showSpeed: true,
    showETA: true
});

console.log('English:', englishTexts);
console.log('Chinese:', chineseTexts);
```

## 🔧 调试技巧

### 进度记录组件测试
```javascript
function testProgressRecord() {
    console.group('📊 Progress Record Test');

    // 创建测试上传对象
    const testUpload = {
        progress: 0.75,
        loaded: 3 * 1024 * 1024,
        total: 4 * 1024 * 1024,
        title: 'test-document.pdf',
        status: 'uploading'
    };

    // 测试基础组件
    const baseRecord = new FileUploadProgressRecord();
    baseRecord.props = { fileUpload: testUpload };

    const progressTexts = baseRecord.getProgressTexts();
    console.log('Progress texts:', progressTexts);

    // 测试完成状态
    testUpload.progress = 1.0;
    const completedTexts = baseRecord.getProgressTexts();
    console.log('Completed texts:', completedTexts);

    console.groupEnd();
}

// 运行测试
testProgressRecord();
```

### 视图适配测试
```javascript
function testViewAdaptation() {
    console.group('🎨 View Adaptation Test');

    const viewTypes = ['kanban', 'list', 'form'];
    const testUpload = {
        progress: 0.5,
        loaded: 1024 * 1024,
        total: 2 * 1024 * 1024,
        title: 'example.jpg'
    };

    viewTypes.forEach(viewType => {
        console.group(`${viewType.toUpperCase()} View`);

        // 测试组件创建
        try {
            const component = createProgressRecordForView(viewType, testUpload);
            console.log('Component created successfully:', component);

            // 测试模板
            console.log('Template:', component.constructor.template);

            // 测试进度文本
            const texts = component.getProgressTexts();
            console.log('Progress texts:', texts);

        } catch (error) {
            console.error('Failed to create component:', error);
        }

        console.groupEnd();
    });

    console.groupEnd();
}

function createProgressRecordForView(viewType, upload) {
    switch (viewType) {
        case 'kanban':
            return new FileUploadProgressKanbanRecord({ fileUpload: upload });
        case 'list':
            return new FileUploadProgressDataRow({ fileUpload: upload });
        default:
            return new FileUploadProgressRecord({ fileUpload: upload });
    }
}

// 运行测试
testViewAdaptation();
```

## 📊 性能考虑

### 优化策略
1. **组件复用**: 通过继承复用基础组件逻辑
2. **模板特化**: 只为不同视图提供不同的模板
3. **计算缓存**: 缓存进度文本计算结果
4. **按需渲染**: 只在进度变化时重新计算文本

### 最佳实践
```javascript
// ✅ 好的做法：继承复用
class FileUploadProgressKanbanRecord extends FileUploadProgressRecord {
    static template = "web.FileUploadProgressKanbanRecord";
}

// ❌ 不好的做法：重复实现
class FileUploadProgressKanbanRecord extends Component {
    // 重复实现所有逻辑
}

// ✅ 好的做法：智能状态判断
if (percent === 100) {
    return { left: _t("Processing..."), right: "" };
}

// ❌ 不好的做法：复杂条件
if (percent >= 99.9 && percent <= 100.1) {
    // 复杂判断逻辑
}

// ✅ 好的做法：单位转换
const mbLoaded = Math.round(fileUpload.loaded / 1000000);
const mbTotal = Math.round(fileUpload.total / 1000000);

// ❌ 不好的做法：精确计算
const mbLoaded = (fileUpload.loaded / 1048576).toFixed(3);
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解组件继承和特化的设计模式
- [ ] 掌握视图特定组件的实现策略
- [ ] 理解文件上传进度文本的格式化技术
- [ ] 能够实现组件模板的动态选择机制
- [ ] 掌握国际化在进度显示中的应用
- [ ] 了解进度记录组件的测试和调试技术

## 🚀 下一步学习
学完进度记录组件后，建议继续学习：
1. **文件上传服务** (`@web/core/file_upload/file_upload_service.js`) - 学习上传服务核心
2. **进度条组件** (`@web/core/file_upload/file_upload_progress_bar.js`) - 理解进度条实现
3. **视图系统** (`@web/core/views/`) - 掌握视图架构

## 💡 重要提示
- 进度记录组件展示了优秀的组件设计模式
- 继承和特化提供了代码复用的最佳实践
- 进度文本格式化需要考虑用户体验
- 视图适配是现代Web应用的重要技术

# @web/core/file_upload/file_upload_progress_bar.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/file_upload/file_upload_progress_bar.js`
- **原始路径**: `/web/static/src/core/file_upload/file_upload_progress_bar.js`
- **代码行数**: 34行
- **作用**: 实现文件上传进度条组件，显示文件上传进度并提供取消上传功能

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 文件上传进度条组件的设计和实现
- 用户交互确认对话框的集成
- XMLHttpRequest取消机制的应用
- 文件上传状态的可视化展示
- 组件属性验证和类型安全

## 📚 核心概念

### 什么是文件上传进度条？
文件上传进度条是一个**可视化进度组件**，主要功能：
- **进度显示**: 实时显示文件上传的进度状态
- **用户反馈**: 为用户提供上传过程的视觉反馈
- **操作控制**: 允许用户取消正在进行的上传
- **状态管理**: 管理上传过程中的各种状态

### 核心架构组成
```javascript
// 组件类定义
const FileUploadProgressBar = class extends Component {
    static template,              // 组件模板
    static props,                 // 属性定义
    
    setup(),                      // 组件设置
    onCancel()                    // 取消上传处理
};

// 属性结构
const props = {
    fileUpload: {                 // 文件上传对象
        type: Object,             // 对象类型
        title: "string",          // 文件标题
        xhr: XMLHttpRequest,      // 上传请求对象
        progress: "number",       // 上传进度
        status: "string"          // 上传状态
    }
};

// 依赖服务
const services = {
    dialog: "对话框服务"          // 用于显示确认对话框
};
```

### 基本使用模式
```javascript
import { FileUploadProgressBar } from '@web/core/file_upload/file_upload_progress_bar';

// 基本使用
<FileUploadProgressBar 
    fileUpload={{
        title: "document.pdf",
        xhr: uploadRequest,
        progress: 45,
        status: "uploading"
    }}/>

// 在文件上传容器中使用
class FileUploadContainer extends Component {
    setup() {
        this.uploads = useState([]);
    }
    
    render() {
        return xml`
            <div class="file-upload-container">
                <t t-foreach="uploads" t-as="upload" t-key="upload.id">
                    <FileUploadProgressBar fileUpload="upload"/>
                </t>
            </div>
        `;
    }
}
```

## 🔍 核心实现详解

### 1. 组件定义和属性

#### 组件类结构
```javascript
const FileUploadProgressBar = class FileUploadProgressBar extends Component {
    static template = "web.FileUploadProgressBar";
    static props = {
        fileUpload: { type: Object },
    };
}
```

**组件定义特点**：
- **继承关系**: 继承自Owl的Component基类
- **模板绑定**: 使用预定义的模板"web.FileUploadProgressBar"
- **属性验证**: 严格的属性类型验证确保数据正确性
- **单一职责**: 专注于进度条的显示和交互

#### 属性类型验证
```javascript
static props = {
    fileUpload: { type: Object },
};
```

**属性验证特点**：
- **类型安全**: 确保fileUpload是对象类型
- **必需属性**: fileUpload是必需的属性
- **运行时检查**: Owl框架在运行时验证属性类型
- **开发友好**: 类型错误时提供清晰的错误信息

### 2. 组件设置和服务集成

#### 服务依赖注入
```javascript
setup() {
    this.dialogService = useService("dialog");
}
```

**服务集成特点**：
- **依赖注入**: 使用useService钩子注入对话框服务
- **服务缓存**: 服务实例在组件生命周期内保持不变
- **类型安全**: 服务注入提供类型安全的API访问
- **解耦设计**: 组件与具体服务实现解耦

### 3. 取消上传功能

#### 取消确认机制
```javascript
onCancel() {
    if (!this.props.fileUpload.xhr) {
        return;
    }
    this.dialogService.add(ConfirmationDialog, {
        body: _t("Do you really want to cancel the upload of %s?", this.props.fileUpload.title),
        confirm: () => {
            this.props.fileUpload.xhr.abort();
        },
    });
}
```

**取消机制特点**：
- **状态检查**: 检查xhr对象是否存在避免无效操作
- **用户确认**: 使用确认对话框防止误操作
- **国际化**: 使用翻译函数支持多语言
- **安全取消**: 调用xhr.abort()安全取消上传

#### XMLHttpRequest取消
```javascript
this.props.fileUpload.xhr.abort();
```

**取消处理特点**：
- **标准API**: 使用标准的XMLHttpRequest.abort()方法
- **立即生效**: 取消操作立即中断网络请求
- **资源清理**: 浏览器自动清理相关资源
- **状态更新**: 触发相应的错误事件和状态更新

## 🎨 实际应用场景

### 1. 高级文件上传进度系统
```javascript
class AdvancedFileUploadProgressSystem {
    constructor() {
        this.progressBars = new Map();
        this.uploadMetrics = new Map();
        this.progressAnimations = new Map();
        this.userPreferences = {
            showDetailedProgress: true,
            enableProgressAnimations: true,
            autoHideOnComplete: true,
            showUploadSpeed: true
        };
        this.setupProgressTracking();
    }
    
    setupProgressTracking() {
        // 进度条工厂
        this.progressBarFactory = {
            create: (fileUpload, options = {}) => {
                const progressBar = new EnhancedFileUploadProgressBar({
                    fileUpload,
                    showSpeed: options.showSpeed || this.userPreferences.showUploadSpeed,
                    showETA: options.showETA || true,
                    showThumbnail: options.showThumbnail || false,
                    allowPause: options.allowPause || false,
                    allowRetry: options.allowRetry || true,
                    onProgress: (progress) => this.onProgressUpdate(fileUpload.id, progress),
                    onComplete: (result) => this.onUploadComplete(fileUpload.id, result),
                    onError: (error) => this.onUploadError(fileUpload.id, error),
                    onCancel: () => this.onUploadCancel(fileUpload.id)
                });
                
                this.progressBars.set(fileUpload.id, progressBar);
                this.initializeMetrics(fileUpload.id);
                
                return progressBar;
            },
            
            remove: (uploadId) => {
                const progressBar = this.progressBars.get(uploadId);
                if (progressBar) {
                    progressBar.destroy();
                    this.progressBars.delete(uploadId);
                    this.uploadMetrics.delete(uploadId);
                    this.progressAnimations.delete(uploadId);
                }
            }
        };
    }
    
    initializeMetrics(uploadId) {
        this.uploadMetrics.set(uploadId, {
            startTime: Date.now(),
            lastProgressTime: Date.now(),
            bytesUploaded: 0,
            totalBytes: 0,
            speed: 0,
            eta: 0,
            progressHistory: [],
            pausedTime: 0,
            resumedTime: 0
        });
    }
    
    onProgressUpdate(uploadId, progressData) {
        const metrics = this.uploadMetrics.get(uploadId);
        if (!metrics) return;
        
        const now = Date.now();
        const timeDiff = now - metrics.lastProgressTime;
        const bytesDiff = progressData.loaded - metrics.bytesUploaded;
        
        // 计算上传速度
        if (timeDiff > 0) {
            metrics.speed = bytesDiff / (timeDiff / 1000); // bytes per second
        }
        
        // 计算预计剩余时间
        if (metrics.speed > 0) {
            const remainingBytes = progressData.total - progressData.loaded;
            metrics.eta = remainingBytes / metrics.speed;
        }
        
        // 更新指标
        metrics.lastProgressTime = now;
        metrics.bytesUploaded = progressData.loaded;
        metrics.totalBytes = progressData.total;
        
        // 记录进度历史
        metrics.progressHistory.push({
            timestamp: now,
            progress: (progressData.loaded / progressData.total) * 100,
            speed: metrics.speed
        });
        
        // 限制历史记录长度
        if (metrics.progressHistory.length > 100) {
            metrics.progressHistory.shift();
        }
        
        // 更新进度条显示
        this.updateProgressBarDisplay(uploadId, progressData, metrics);
        
        // 触发进度动画
        if (this.userPreferences.enableProgressAnimations) {
            this.animateProgress(uploadId, progressData);
        }
    }
    
    updateProgressBarDisplay(uploadId, progressData, metrics) {
        const progressBar = this.progressBars.get(uploadId);
        if (!progressBar) return;
        
        const progressPercent = (progressData.loaded / progressData.total) * 100;
        
        // 更新进度条
        progressBar.updateProgress({
            percent: progressPercent,
            speed: this.formatSpeed(metrics.speed),
            eta: this.formatETA(metrics.eta),
            bytesUploaded: this.formatBytes(progressData.loaded),
            totalBytes: this.formatBytes(progressData.total)
        });
        
        // 更新状态指示器
        if (progressPercent < 100) {
            progressBar.setStatus('uploading');
        } else {
            progressBar.setStatus('processing');
        }
    }
    
    animateProgress(uploadId, progressData) {
        const progressBar = this.progressBars.get(uploadId);
        if (!progressBar) return;
        
        // 取消之前的动画
        const existingAnimation = this.progressAnimations.get(uploadId);
        if (existingAnimation) {
            existingAnimation.cancel();
        }
        
        // 创建新的进度动画
        const progressElement = progressBar.getProgressElement();
        const targetWidth = (progressData.loaded / progressData.total) * 100;
        
        const animation = progressElement.animate([
            { width: progressElement.style.width || '0%' },
            { width: `${targetWidth}%` }
        ], {
            duration: 300,
            easing: 'ease-out',
            fill: 'forwards'
        });
        
        this.progressAnimations.set(uploadId, animation);
        
        // 添加脉冲效果
        if (targetWidth > 0 && targetWidth < 100) {
            progressElement.classList.add('pulsing');
        } else {
            progressElement.classList.remove('pulsing');
        }
    }
    
    onUploadComplete(uploadId, result) {
        const metrics = this.uploadMetrics.get(uploadId);
        const progressBar = this.progressBars.get(uploadId);
        
        if (metrics) {
            metrics.endTime = Date.now();
            metrics.totalTime = metrics.endTime - metrics.startTime;
        }
        
        if (progressBar) {
            progressBar.setStatus('completed');
            progressBar.showSuccessMessage(result.message || 'Upload completed successfully');
            
            // 自动隐藏
            if (this.userPreferences.autoHideOnComplete) {
                setTimeout(() => {
                    this.progressBarFactory.remove(uploadId);
                }, 3000);
            }
        }
        
        // 记录成功指标
        this.recordUploadMetrics(uploadId, 'success', result);
    }
    
    onUploadError(uploadId, error) {
        const progressBar = this.progressBars.get(uploadId);
        
        if (progressBar) {
            progressBar.setStatus('error');
            progressBar.showErrorMessage(error.message || 'Upload failed');
            progressBar.showRetryButton();
        }
        
        // 记录错误指标
        this.recordUploadMetrics(uploadId, 'error', error);
    }
    
    onUploadCancel(uploadId) {
        const progressBar = this.progressBars.get(uploadId);
        
        if (progressBar) {
            progressBar.setStatus('cancelled');
            progressBar.showCancelMessage('Upload cancelled by user');
        }
        
        // 清理资源
        setTimeout(() => {
            this.progressBarFactory.remove(uploadId);
        }, 1000);
        
        // 记录取消指标
        this.recordUploadMetrics(uploadId, 'cancelled');
    }
    
    recordUploadMetrics(uploadId, status, data = {}) {
        const metrics = this.uploadMetrics.get(uploadId);
        if (!metrics) return;
        
        const uploadRecord = {
            uploadId,
            status,
            startTime: metrics.startTime,
            endTime: Date.now(),
            totalTime: Date.now() - metrics.startTime,
            totalBytes: metrics.totalBytes,
            averageSpeed: metrics.totalBytes / ((Date.now() - metrics.startTime) / 1000),
            progressHistory: metrics.progressHistory,
            data
        };
        
        // 发送到分析服务
        this.sendUploadAnalytics(uploadRecord);
    }
    
    sendUploadAnalytics(record) {
        fetch('/web/upload_analytics', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(record)
        }).catch(error => {
            console.warn('Failed to send upload analytics:', error);
        });
    }
    
    // 工具方法
    formatSpeed(bytesPerSecond) {
        if (bytesPerSecond < 1024) {
            return `${bytesPerSecond.toFixed(0)} B/s`;
        } else if (bytesPerSecond < 1024 * 1024) {
            return `${(bytesPerSecond / 1024).toFixed(1)} KB/s`;
        } else {
            return `${(bytesPerSecond / (1024 * 1024)).toFixed(1)} MB/s`;
        }
    }
    
    formatETA(seconds) {
        if (seconds < 60) {
            return `${Math.ceil(seconds)}s`;
        } else if (seconds < 3600) {
            return `${Math.ceil(seconds / 60)}m`;
        } else {
            return `${Math.ceil(seconds / 3600)}h`;
        }
    }
    
    formatBytes(bytes) {
        if (bytes < 1024) {
            return `${bytes} B`;
        } else if (bytes < 1024 * 1024) {
            return `${(bytes / 1024).toFixed(1)} KB`;
        } else if (bytes < 1024 * 1024 * 1024) {
            return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
        } else {
            return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
        }
    }
    
    // 批量操作
    cancelAllUploads() {
        for (const [uploadId, progressBar] of this.progressBars.entries()) {
            if (progressBar.getStatus() === 'uploading') {
                progressBar.cancel();
            }
        }
    }
    
    pauseAllUploads() {
        for (const [uploadId, progressBar] of this.progressBars.entries()) {
            if (progressBar.getStatus() === 'uploading' && progressBar.canPause()) {
                progressBar.pause();
            }
        }
    }
    
    resumeAllUploads() {
        for (const [uploadId, progressBar] of this.progressBars.entries()) {
            if (progressBar.getStatus() === 'paused') {
                progressBar.resume();
            }
        }
    }
    
    getUploadStatistics() {
        const stats = {
            totalUploads: this.progressBars.size,
            activeUploads: 0,
            completedUploads: 0,
            failedUploads: 0,
            totalBytesUploaded: 0,
            averageSpeed: 0
        };
        
        let totalSpeed = 0;
        let speedCount = 0;
        
        for (const [uploadId, progressBar] of this.progressBars.entries()) {
            const status = progressBar.getStatus();
            const metrics = this.uploadMetrics.get(uploadId);
            
            switch (status) {
                case 'uploading':
                    stats.activeUploads++;
                    if (metrics && metrics.speed > 0) {
                        totalSpeed += metrics.speed;
                        speedCount++;
                    }
                    break;
                case 'completed':
                    stats.completedUploads++;
                    break;
                case 'error':
                    stats.failedUploads++;
                    break;
            }
            
            if (metrics) {
                stats.totalBytesUploaded += metrics.bytesUploaded;
            }
        }
        
        if (speedCount > 0) {
            stats.averageSpeed = totalSpeed / speedCount;
        }
        
        return stats;
    }
}

// 增强的进度条组件
class EnhancedFileUploadProgressBar extends FileUploadProgressBar {
    static template = "web.EnhancedFileUploadProgressBar";
    
    setup() {
        super.setup();
        this.state = useState({
            status: 'uploading',
            progress: 0,
            speed: '',
            eta: '',
            message: '',
            showDetails: false
        });
    }
    
    updateProgress(data) {
        Object.assign(this.state, data);
    }
    
    setStatus(status) {
        this.state.status = status;
    }
    
    showSuccessMessage(message) {
        this.state.message = message;
        this.state.status = 'completed';
    }
    
    showErrorMessage(message) {
        this.state.message = message;
        this.state.status = 'error';
    }
    
    showCancelMessage(message) {
        this.state.message = message;
        this.state.status = 'cancelled';
    }
    
    toggleDetails() {
        this.state.showDetails = !this.state.showDetails;
    }
    
    getProgressElement() {
        return this.el.querySelector('.progress-bar');
    }
    
    getStatus() {
        return this.state.status;
    }
    
    canPause() {
        return this.props.allowPause && this.state.status === 'uploading';
    }
    
    pause() {
        if (this.canPause() && this.props.fileUpload.xhr) {
            // 实现暂停逻辑
            this.state.status = 'paused';
        }
    }
    
    resume() {
        if (this.state.status === 'paused') {
            // 实现恢复逻辑
            this.state.status = 'uploading';
        }
    }
    
    retry() {
        if (this.state.status === 'error' && this.props.allowRetry) {
            // 实现重试逻辑
            this.state.status = 'uploading';
            this.state.progress = 0;
            this.state.message = '';
        }
    }
    
    cancel() {
        this.onCancel();
    }
    
    destroy() {
        // 清理资源
        if (this.props.fileUpload.xhr) {
            this.props.fileUpload.xhr.abort();
        }
    }
}

// 使用示例
const progressSystem = new AdvancedFileUploadProgressSystem();

// 创建进度条
const fileUpload = {
    id: 'upload_123',
    title: 'document.pdf',
    xhr: new XMLHttpRequest(),
    size: 1024 * 1024 * 5 // 5MB
};

const progressBar = progressSystem.progressBarFactory.create(fileUpload, {
    showSpeed: true,
    showETA: true,
    allowPause: true,
    allowRetry: true
});
```

# @web/core/file_upload/file_upload_service.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/file_upload/file_upload_service.js`
- **原始路径**: `/web/static/src/core/file_upload/file_upload_service.js`
- **代码行数**: 111行
- **作用**: 实现文件上传服务核心，提供文件上传的统一API和状态管理

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 文件上传服务的架构设计和实现
- XMLHttpRequest的高级使用和事件处理
- 响应式状态管理在文件上传中的应用
- 事件总线模式在服务中的使用
- 文件上传的错误处理和通知机制

## 📚 核心概念

### 什么是文件上传服务？
文件上传服务是一个**统一的文件上传管理中心**，主要功能：
- **上传管理**: 统一管理所有文件上传请求
- **状态跟踪**: 实时跟踪上传进度和状态
- **事件通知**: 通过事件总线通知上传状态变化
- **错误处理**: 统一处理上传错误和异常情况

### 核心架构组成
```javascript
// 服务定义
const fileUploadService = {
    dependencies: ["notification"],   // 服务依赖
    createXhr(),                     // XHR创建工厂
    start(env, services)             // 服务启动方法
};

// 服务返回接口
const serviceAPI = {
    bus: EventBus,                   // 事件总线
    upload: Function,                // 上传方法
    uploads: Object                  // 上传状态集合
};

// 上传对象结构
const uploadObject = {
    id: Number,                      // 唯一标识
    xhr: XMLHttpRequest,             // 请求对象
    data: FormData,                  // 表单数据
    progress: Number,                // 进度百分比
    loaded: Number,                  // 已上传字节
    total: Number,                   // 总字节数
    state: String,                   // 上传状态
    title: String,                   // 显示标题
    type: String                     // 文件类型
};
```

### 基本使用模式
```javascript
import { useService } from '@odoo/owl';

// 在组件中使用
class FileUploadComponent extends Component {
    setup() {
        this.fileUploadService = useService("file_upload");
        
        // 监听上传事件
        this.fileUploadService.bus.addEventListener("FILE_UPLOAD_LOADED", (event) => {
            console.log("Upload completed:", event.detail.upload);
        });
    }
    
    async uploadFiles(files) {
        const upload = await this.fileUploadService.upload("/web/binary/upload", files, {
            buildFormData: (formData) => {
                formData.append("model", "ir.attachment");
                formData.append("field", "datas");
            },
            displayErrorNotification: true
        });
        
        return upload;
    }
}
```

## 🔍 核心实现详解

### 1. 服务定义和依赖

#### 服务结构定义
```javascript
const fileUploadService = {
    dependencies: ["notification"],
    createXhr() {
        return new window.XMLHttpRequest();
    },
    start(env, { notificationService }) {
        // 服务实现
    }
};
```

**服务定义特点**：
- **依赖声明**: 明确声明对通知服务的依赖
- **工厂方法**: createXhr提供XHR对象创建的抽象
- **测试友好**: 可在测试中覆盖createXhr方法
- **环境注入**: 通过start方法接收环境和依赖服务

#### XHR工厂方法
```javascript
createXhr() {
    return new window.XMLHttpRequest();
}
```

**工厂方法特点**：
- **抽象创建**: 抽象XMLHttpRequest的创建过程
- **测试支持**: 在测试中可以返回模拟的XHR对象
- **一致性**: 确保所有上传使用相同的XHR配置
- **扩展性**: 未来可以添加XHR的通用配置

### 2. 状态管理系统

#### 响应式状态管理
```javascript
const uploads = reactive({});
let nextId = 1;
const bus = new EventBus();
```

**状态管理特点**：
- **响应式**: 使用reactive创建响应式状态对象
- **唯一标识**: 使用递增ID确保上传对象的唯一性
- **事件驱动**: 使用EventBus实现事件驱动的状态通知
- **集中管理**: 所有上传状态集中在uploads对象中

#### 上传对象创建
```javascript
const upload = reactive({
    id: nextId++,
    xhr,
    data: formData,
    progress: 0,
    loaded: 0,
    total: 0,
    state: "pending",
    title: files.length === 1 ? files[0].name : _t("%s Files", files.length),
    type: files.length === 1 ? files[0].type : undefined,
});
```

**对象创建特点**：
- **响应式对象**: 每个上传对象都是响应式的
- **完整信息**: 包含上传所需的所有状态信息
- **智能标题**: 根据文件数量生成合适的显示标题
- **类型识别**: 单文件时保留文件类型信息

### 3. 文件上传核心逻辑

#### 上传方法实现
```javascript
const upload = async (route, files, params = {}) => {
    const xhr = this.createXhr();
    xhr.open("POST", route);
    const formData = new FormData();
    formData.append("csrf_token", odoo.csrf_token);
    for (const file of files) {
        formData.append("ufile", file);
    }
    if (params.buildFormData) {
        params.buildFormData(formData);
    }
    // ... 创建上传对象和事件监听
    xhr.send(formData);
    return upload;
};
```

**上传逻辑特点**：
- **异步处理**: 使用async/await支持异步操作
- **CSRF保护**: 自动添加CSRF令牌确保安全
- **多文件支持**: 支持单文件和多文件上传
- **自定义数据**: 通过buildFormData回调支持自定义表单数据

#### FormData构建
```javascript
const formData = new FormData();
formData.append("csrf_token", odoo.csrf_token);
for (const file of files) {
    formData.append("ufile", file);
}
if (params.buildFormData) {
    params.buildFormData(formData);
}
```

**数据构建特点**：
- **安全令牌**: 自动添加CSRF令牌
- **文件添加**: 遍历文件列表添加到表单
- **扩展机制**: 支持通过回调添加额外数据
- **标准格式**: 使用标准的multipart/form-data格式

### 4. 事件处理机制

#### 进度事件处理
```javascript
xhr.upload.addEventListener("progress", async (ev) => {
    upload.progress = ev.loaded / ev.total;
    upload.loaded = ev.loaded;
    upload.total = ev.total;
    upload.state = "loading";
});
```

**进度处理特点**：
- **实时更新**: 实时更新上传进度信息
- **百分比计算**: 自动计算进度百分比
- **状态同步**: 同步更新上传状态
- **响应式**: 利用响应式对象自动触发UI更新

#### 完成和错误事件
```javascript
xhr.addEventListener("load", () => {
    delete uploads[upload.id];
    upload.state = "loaded";
    bus.trigger("FILE_UPLOAD_LOADED", { upload });
});

xhr.addEventListener("error", async () => {
    delete uploads[upload.id];
    upload.state = "error";
    if (params.displayErrorNotification) {
        notificationService.add(_t("An error occured while uploading."), {
            title: _t("Error"),
            sticky: true,
        });
    }
    bus.trigger("FILE_UPLOAD_ERROR", { upload });
});
```

**事件处理特点**：
- **状态清理**: 完成或失败后从uploads中移除
- **事件通知**: 通过事件总线通知状态变化
- **错误通知**: 可选的错误通知显示
- **国际化**: 错误消息支持国际化

## 🎨 实际应用场景

### 1. 企业级文件上传服务系统
```javascript
class EnterpriseFileUploadService {
    constructor() {
        this.baseService = fileUploadService;
        this.uploadPolicies = new Map();
        this.uploadQueues = new Map();
        this.uploadMetrics = new Map();
        this.retryStrategies = new Map();
        this.setupEnterpriseFeatures();
    }
    
    setupEnterpriseFeatures() {
        // 上传策略配置
        this.uploadPolicies.set('default', {
            maxFileSize: 10 * 1024 * 1024,      // 10MB
            maxConcurrentUploads: 3,
            allowedTypes: ['image/*', 'application/pdf', 'text/*'],
            requireAuth: true,
            virusScan: true,
            autoRetry: true,
            maxRetries: 3
        });
        
        this.uploadPolicies.set('premium', {
            maxFileSize: 100 * 1024 * 1024,     // 100MB
            maxConcurrentUploads: 10,
            allowedTypes: ['*/*'],
            requireAuth: true,
            virusScan: true,
            autoRetry: true,
            maxRetries: 5,
            priorityQueue: true
        });
        
        // 重试策略
        this.retryStrategies.set('exponential', {
            baseDelay: 1000,
            maxDelay: 30000,
            backoffFactor: 2,
            jitter: true
        });
        
        this.retryStrategies.set('linear', {
            baseDelay: 2000,
            maxDelay: 10000,
            backoffFactor: 1,
            jitter: false
        });
    }
    
    async upload(route, files, options = {}) {
        // 验证上传策略
        const policy = this.uploadPolicies.get(options.policy || 'default');
        const validationResult = await this.validateUpload(files, policy);
        
        if (!validationResult.valid) {
            throw new Error(`Upload validation failed: ${validationResult.errors.join(', ')}`);
        }
        
        // 检查并发限制
        await this.checkConcurrencyLimit(policy);
        
        // 创建增强的上传对象
        const enhancedUpload = await this.createEnhancedUpload(route, files, options, policy);
        
        // 添加到队列
        this.addToQueue(enhancedUpload, policy);
        
        // 开始上传
        return this.startUpload(enhancedUpload);
    }
    
    async validateUpload(files, policy) {
        const errors = [];
        
        for (const file of files) {
            // 文件大小检查
            if (file.size > policy.maxFileSize) {
                errors.push(`File ${file.name} exceeds maximum size of ${this.formatBytes(policy.maxFileSize)}`);
            }
            
            // 文件类型检查
            if (!this.isTypeAllowed(file.type, policy.allowedTypes)) {
                errors.push(`File type ${file.type} is not allowed`);
            }
            
            // 文件名检查
            if (!this.isFilenameValid(file.name)) {
                errors.push(`Invalid filename: ${file.name}`);
            }
        }
        
        // 病毒扫描（如果启用）
        if (policy.virusScan) {
            const scanResults = await this.performVirusScan(files);
            errors.push(...scanResults.errors);
        }
        
        return {
            valid: errors.length === 0,
            errors
        };
    }
    
    async checkConcurrencyLimit(policy) {
        const activeUploads = this.getActiveUploadsCount();
        
        if (activeUploads >= policy.maxConcurrentUploads) {
            // 等待有空闲槽位
            await this.waitForAvailableSlot(policy);
        }
    }
    
    async createEnhancedUpload(route, files, options, policy) {
        // 使用基础服务创建上传
        const baseUpload = await this.baseService.upload(route, files, {
            ...options,
            displayErrorNotification: false // 我们自己处理错误
        });
        
        // 增强上传对象
        const enhancedUpload = {
            ...baseUpload,
            policy,
            retryCount: 0,
            maxRetries: policy.maxRetries,
            startTime: Date.now(),
            estimatedTime: null,
            speed: 0,
            priority: options.priority || 'normal',
            metadata: {
                userAgent: navigator.userAgent,
                uploadSource: options.source || 'web',
                sessionId: this.getSessionId()
            }
        };
        
        // 添加增强的事件监听
        this.setupEnhancedEventListeners(enhancedUpload);
        
        return enhancedUpload;
    }
    
    setupEnhancedEventListeners(upload) {
        const originalXhr = upload.xhr;
        
        // 增强进度监听
        originalXhr.upload.addEventListener("progress", (ev) => {
            const now = Date.now();
            const elapsed = now - upload.startTime;
            
            // 计算上传速度
            upload.speed = ev.loaded / (elapsed / 1000);
            
            // 估算剩余时间
            if (upload.speed > 0) {
                const remaining = ev.total - ev.loaded;
                upload.estimatedTime = remaining / upload.speed;
            }
            
            // 记录指标
            this.recordUploadMetrics(upload, {
                timestamp: now,
                loaded: ev.loaded,
                total: ev.total,
                speed: upload.speed
            });
        });
        
        // 增强错误处理
        originalXhr.addEventListener("error", async () => {
            await this.handleUploadError(upload);
        });
        
        // 增强完成处理
        originalXhr.addEventListener("load", async () => {
            await this.handleUploadComplete(upload);
        });
    }
    
    async handleUploadError(upload) {
        upload.retryCount++;
        
        // 检查是否应该重试
        if (upload.policy.autoRetry && upload.retryCount <= upload.maxRetries) {
            const retryStrategy = this.retryStrategies.get('exponential');
            const delay = this.calculateRetryDelay(upload.retryCount, retryStrategy);
            
            console.log(`Upload failed, retrying in ${delay}ms (attempt ${upload.retryCount}/${upload.maxRetries})`);
            
            setTimeout(() => {
                this.retryUpload(upload);
            }, delay);
        } else {
            // 重试次数用尽，标记为失败
            this.markUploadAsFailed(upload);
        }
    }
    
    calculateRetryDelay(retryCount, strategy) {
        let delay = strategy.baseDelay * Math.pow(strategy.backoffFactor, retryCount - 1);
        delay = Math.min(delay, strategy.maxDelay);
        
        if (strategy.jitter) {
            delay += Math.random() * 1000;
        }
        
        return delay;
    }
    
    async retryUpload(upload) {
        // 重新创建XHR
        const newXhr = this.baseService.createXhr();
        upload.xhr = newXhr;
        
        // 重新设置事件监听
        this.setupEnhancedEventListeners(upload);
        
        // 重新发送请求
        newXhr.open("POST", upload.route);
        newXhr.send(upload.data);
    }
    
    async handleUploadComplete(upload) {
        const totalTime = Date.now() - upload.startTime;
        
        // 记录完成指标
        this.recordUploadMetrics(upload, {
            completed: true,
            totalTime,
            averageSpeed: upload.total / (totalTime / 1000),
            retryCount: upload.retryCount
        });
        
        // 从队列中移除
        this.removeFromQueue(upload);
        
        // 触发完成事件
        this.triggerUploadEvent('upload:completed', upload);
    }
    
    addToQueue(upload, policy) {
        const queueName = policy.priorityQueue ? upload.priority : 'default';
        
        if (!this.uploadQueues.has(queueName)) {
            this.uploadQueues.set(queueName, []);
        }
        
        const queue = this.uploadQueues.get(queueName);
        
        if (upload.priority === 'high') {
            queue.unshift(upload);
        } else {
            queue.push(upload);
        }
    }
    
    removeFromQueue(upload) {
        for (const [queueName, queue] of this.uploadQueues.entries()) {
            const index = queue.findIndex(u => u.id === upload.id);
            if (index !== -1) {
                queue.splice(index, 1);
                break;
            }
        }
    }
    
    async startUpload(upload) {
        // 开始上传
        upload.xhr.open("POST", upload.route);
        upload.xhr.send(upload.data);
        
        // 触发开始事件
        this.triggerUploadEvent('upload:started', upload);
        
        return upload;
    }
    
    recordUploadMetrics(upload, metrics) {
        if (!this.uploadMetrics.has(upload.id)) {
            this.uploadMetrics.set(upload.id, []);
        }
        
        this.uploadMetrics.get(upload.id).push({
            ...metrics,
            timestamp: Date.now()
        });
    }
    
    getActiveUploadsCount() {
        let count = 0;
        for (const queue of this.uploadQueues.values()) {
            count += queue.filter(upload => 
                ['pending', 'loading'].includes(upload.state)
            ).length;
        }
        return count;
    }
    
    async waitForAvailableSlot(policy) {
        return new Promise((resolve) => {
            const checkSlot = () => {
                if (this.getActiveUploadsCount() < policy.maxConcurrentUploads) {
                    resolve();
                } else {
                    setTimeout(checkSlot, 100);
                }
            };
            checkSlot();
        });
    }
    
    async performVirusScan(files) {
        // 模拟病毒扫描
        const errors = [];
        
        for (const file of files) {
            // 简单的文件名检查
            if (file.name.toLowerCase().includes('virus')) {
                errors.push(`Potential threat detected in file: ${file.name}`);
            }
        }
        
        return { errors };
    }
    
    isTypeAllowed(fileType, allowedTypes) {
        return allowedTypes.some(pattern => {
            if (pattern === '*/*') return true;
            if (pattern.endsWith('/*')) {
                return fileType.startsWith(pattern.slice(0, -1));
            }
            return fileType === pattern;
        });
    }
    
    isFilenameValid(filename) {
        // 检查文件名是否包含危险字符
        const dangerousChars = /[<>:"/\\|?*]/;
        return !dangerousChars.test(filename);
    }
    
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    getSessionId() {
        return window.odoo?.session?.session_id || 'unknown';
    }
    
    triggerUploadEvent(eventType, upload) {
        const event = new CustomEvent(eventType, {
            detail: { upload }
        });
        document.dispatchEvent(event);
    }
    
    // 批量操作
    async uploadMultiple(uploads) {
        const results = [];
        
        for (const uploadConfig of uploads) {
            try {
                const result = await this.upload(
                    uploadConfig.route,
                    uploadConfig.files,
                    uploadConfig.options
                );
                results.push({ success: true, upload: result });
            } catch (error) {
                results.push({ success: false, error, config: uploadConfig });
            }
        }
        
        return results;
    }
    
    cancelAllUploads() {
        for (const queue of this.uploadQueues.values()) {
            queue.forEach(upload => {
                if (upload.xhr) {
                    upload.xhr.abort();
                }
            });
        }
        this.uploadQueues.clear();
    }
    
    getUploadStatistics() {
        const stats = {
            totalUploads: 0,
            activeUploads: 0,
            completedUploads: 0,
            failedUploads: 0,
            totalBytes: 0,
            averageSpeed: 0,
            queueSizes: {}
        };
        
        for (const [queueName, queue] of this.uploadQueues.entries()) {
            stats.queueSizes[queueName] = queue.length;
            stats.totalUploads += queue.length;
            
            queue.forEach(upload => {
                if (['pending', 'loading'].includes(upload.state)) {
                    stats.activeUploads++;
                } else if (upload.state === 'loaded') {
                    stats.completedUploads++;
                } else if (upload.state === 'error') {
                    stats.failedUploads++;
                }
                
                stats.totalBytes += upload.total || 0;
            });
        }
        
        return stats;
    }
}

// 使用示例
const enterpriseUploadService = new EnterpriseFileUploadService();

// 高级上传
async function uploadWithPolicy(files) {
    try {
        const upload = await enterpriseUploadService.upload('/web/binary/upload', files, {
            policy: 'premium',
            priority: 'high',
            source: 'drag_drop',
            buildFormData: (formData) => {
                formData.append('model', 'ir.attachment');
                formData.append('field', 'datas');
            }
        });
        
        console.log('Upload started:', upload);
        return upload;
        
    } catch (error) {
        console.error('Upload failed:', error);
        throw error;
    }
}
```

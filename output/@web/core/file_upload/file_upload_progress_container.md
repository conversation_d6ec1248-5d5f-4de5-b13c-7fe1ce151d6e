# @web/core/file_upload/file_upload_progress_container.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/file_upload/file_upload_progress_container.js`
- **原始路径**: `/web/static/src/core/file_upload/file_upload_progress_container.js`
- **代码行数**: 16行
- **作用**: 实现文件上传进度容器组件，管理和展示多个文件上传进度条

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 文件上传容器组件的设计模式
- 动态组件渲染和管理技术
- 条件显示逻辑的实现方式
- 组件组合和复用的最佳实践
- 文件上传状态的集中管理

## 📚 核心概念

### 什么是文件上传进度容器？
文件上传进度容器是一个**容器管理组件**，主要功能：
- **集中管理**: 统一管理多个文件上传进度条
- **动态渲染**: 根据上传状态动态显示进度条
- **组件复用**: 支持自定义进度条组件的复用
- **条件显示**: 根据业务逻辑控制容器的显示

### 核心架构组成
```javascript
// 容器组件定义
const FileUploadProgressContainer = class extends Component {
    static template,              // 容器模板
    static props                  // 属性定义
};

// 属性结构
const props = {
    Component: {                  // 进度条组件类
        optional: false           // 必需属性
    },
    shouldDisplay: {              // 显示条件函数
        type: Function,           // 函数类型
        optional: true            // 可选属性
    },
    fileUploads: {                // 文件上传集合
        type: Object              // 对象类型
    }
};

// 使用模式
<FileUploadProgressContainer 
    Component={FileUploadProgressBar}
    shouldDisplay={(uploads) => uploads.size > 0}
    fileUploads={uploadManager.uploads}/>
```

### 基本使用模式
```javascript
import { FileUploadProgressContainer } from '@web/core/file_upload/file_upload_progress_container';
import { FileUploadProgressBar } from '@web/core/file_upload/file_upload_progress_bar';

// 基本使用
<FileUploadProgressContainer 
    Component={FileUploadProgressBar}
    fileUploads={this.uploadState.uploads}/>

// 带条件显示
<FileUploadProgressContainer 
    Component={FileUploadProgressBar}
    shouldDisplay={(uploads) => uploads.size > 0}
    fileUploads={this.uploadState.uploads}/>

// 自定义进度条组件
<FileUploadProgressContainer 
    Component={CustomProgressBar}
    shouldDisplay={(uploads) => this.showProgress}
    fileUploads={this.uploadState.uploads}/>
```

## 🔍 核心实现详解

### 1. 组件定义和模板

#### 组件类结构
```javascript
const FileUploadProgressContainer = class FileUploadProgressContainer extends Component {
    static template = "web.FileUploadProgressContainer";
    static props = {
        Component: { optional: false },
        shouldDisplay: { type: Function, optional: true },
        fileUploads: { type: Object },
    };
}
```

**组件定义特点**：
- **容器模式**: 作为容器组件管理子组件
- **模板绑定**: 使用预定义模板进行渲染
- **属性验证**: 严格的属性类型和必需性验证
- **简洁设计**: 专注于容器功能，逻辑简单清晰

### 2. 属性系统设计

#### 组件属性定义
```javascript
static props = {
    Component: { optional: false },
    shouldDisplay: { type: Function, optional: true },
    fileUploads: { type: Object },
};
```

**属性设计特点**：
- **Component**: 必需的进度条组件类，支持组件复用
- **shouldDisplay**: 可选的显示条件函数，支持条件渲染
- **fileUploads**: 文件上传对象集合，包含所有上传状态
- **类型安全**: 严格的类型检查确保属性正确性

#### 动态组件渲染
```javascript
// 模板中的动态组件渲染逻辑（推测）
<t t-if="shouldDisplay ? shouldDisplay(props.fileUploads) : true">
    <t t-foreach="props.fileUploads" t-as="upload" t-key="upload.id">
        <t t-component="props.Component" fileUpload="upload"/>
    </t>
</t>
```

**渲染机制特点**：
- **条件渲染**: 根据shouldDisplay函数决定是否显示
- **动态遍历**: 遍历fileUploads集合渲染进度条
- **组件传递**: 将Component作为动态组件进行渲染
- **属性传递**: 将单个upload对象传递给子组件

### 3. 容器管理模式

#### 集中状态管理
```javascript
// 容器管理的典型使用场景
class FileUploadManager {
    constructor() {
        this.uploads = reactive(new Map());
        this.containerVisible = ref(false);
    }
    
    addUpload(file) {
        const upload = {
            id: generateId(),
            file,
            progress: 0,
            status: 'pending',
            xhr: null
        };
        this.uploads.set(upload.id, upload);
        this.containerVisible.value = true;
    }
    
    removeUpload(uploadId) {
        this.uploads.delete(uploadId);
        if (this.uploads.size === 0) {
            this.containerVisible.value = false;
        }
    }
    
    shouldDisplayContainer() {
        return this.uploads.size > 0 && this.containerVisible.value;
    }
}
```

**管理模式特点**：
- **响应式状态**: 使用响应式数据管理上传状态
- **自动显示**: 根据上传数量自动控制容器显示
- **生命周期**: 管理上传的完整生命周期
- **状态同步**: 确保容器状态与上传状态同步

## 🎨 实际应用场景

### 1. 高级文件上传容器系统
```javascript
class AdvancedFileUploadContainerSystem {
    constructor() {
        this.containers = new Map();
        this.uploadGroups = new Map();
        this.containerTemplates = new Map();
        this.displayRules = new Map();
        this.setupContainerSystem();
    }
    
    setupContainerSystem() {
        // 注册容器模板
        this.containerTemplates.set('default', {
            Component: FileUploadProgressBar,
            layout: 'vertical',
            maxVisible: 5,
            autoHide: true,
            groupBy: null
        });
        
        this.containerTemplates.set('compact', {
            Component: CompactProgressBar,
            layout: 'horizontal',
            maxVisible: 3,
            autoHide: true,
            groupBy: 'type'
        });
        
        this.containerTemplates.set('detailed', {
            Component: DetailedProgressBar,
            layout: 'grid',
            maxVisible: 10,
            autoHide: false,
            groupBy: 'category'
        });
        
        // 注册显示规则
        this.displayRules.set('always', () => true);
        this.displayRules.set('hasUploads', (uploads) => uploads.size > 0);
        this.displayRules.set('hasActiveUploads', (uploads) => {
            return Array.from(uploads.values()).some(upload => 
                ['pending', 'uploading'].includes(upload.status)
            );
        });
        this.displayRules.set('hasErrors', (uploads) => {
            return Array.from(uploads.values()).some(upload => 
                upload.status === 'error'
            );
        });
    }
    
    createContainer(containerId, options = {}) {
        const template = this.containerTemplates.get(options.template || 'default');
        const displayRule = this.displayRules.get(options.displayRule || 'hasUploads');
        
        const container = new EnhancedFileUploadContainer({
            id: containerId,
            Component: template.Component,
            shouldDisplay: displayRule,
            fileUploads: new Map(),
            layout: template.layout,
            maxVisible: template.maxVisible,
            autoHide: template.autoHide,
            groupBy: template.groupBy,
            onUploadComplete: (upload) => this.onUploadComplete(containerId, upload),
            onUploadError: (upload, error) => this.onUploadError(containerId, upload, error),
            onContainerEmpty: () => this.onContainerEmpty(containerId)
        });
        
        this.containers.set(containerId, container);
        return container;
    }
    
    addUploadToContainer(containerId, upload) {
        const container = this.containers.get(containerId);
        if (!container) {
            throw new Error(`Container ${containerId} not found`);
        }
        
        // 添加到容器
        container.addUpload(upload);
        
        // 添加到分组（如果启用）
        if (container.groupBy) {
            this.addToGroup(containerId, upload);
        }
        
        // 触发容器更新
        container.update();
    }
    
    addToGroup(containerId, upload) {
        const container = this.containers.get(containerId);
        const groupKey = this.getGroupKey(upload, container.groupBy);
        
        if (!this.uploadGroups.has(containerId)) {
            this.uploadGroups.set(containerId, new Map());
        }
        
        const containerGroups = this.uploadGroups.get(containerId);
        if (!containerGroups.has(groupKey)) {
            containerGroups.set(groupKey, []);
        }
        
        containerGroups.get(groupKey).push(upload);
    }
    
    getGroupKey(upload, groupBy) {
        switch (groupBy) {
            case 'type':
                return upload.file.type.split('/')[0]; // image, video, etc.
            case 'category':
                return upload.category || 'uncategorized';
            case 'size':
                if (upload.file.size < 1024 * 1024) return 'small';
                if (upload.file.size < 10 * 1024 * 1024) return 'medium';
                return 'large';
            default:
                return 'default';
        }
    }
    
    onUploadComplete(containerId, upload) {
        const container = this.containers.get(containerId);
        if (!container) return;
        
        // 更新上传状态
        upload.status = 'completed';
        upload.completedAt = Date.now();
        
        // 自动隐藏逻辑
        if (container.autoHide) {
            setTimeout(() => {
                container.removeUpload(upload.id);
                this.removeFromGroup(containerId, upload);
            }, 3000);
        }
        
        // 触发完成事件
        this.triggerEvent('uploadComplete', { containerId, upload });
    }
    
    onUploadError(containerId, upload, error) {
        const container = this.containers.get(containerId);
        if (!container) return;
        
        // 更新错误状态
        upload.status = 'error';
        upload.error = error;
        upload.errorAt = Date.now();
        
        // 显示错误通知
        container.showErrorNotification(upload, error);
        
        // 触发错误事件
        this.triggerEvent('uploadError', { containerId, upload, error });
    }
    
    onContainerEmpty(containerId) {
        const container = this.containers.get(containerId);
        if (!container) return;
        
        // 清理分组
        this.uploadGroups.delete(containerId);
        
        // 隐藏容器
        if (container.autoHide) {
            container.hide();
        }
        
        // 触发空容器事件
        this.triggerEvent('containerEmpty', { containerId });
    }
    
    removeFromGroup(containerId, upload) {
        const containerGroups = this.uploadGroups.get(containerId);
        if (!containerGroups) return;
        
        for (const [groupKey, uploads] of containerGroups.entries()) {
            const index = uploads.findIndex(u => u.id === upload.id);
            if (index !== -1) {
                uploads.splice(index, 1);
                if (uploads.length === 0) {
                    containerGroups.delete(groupKey);
                }
                break;
            }
        }
    }
    
    // 批量操作
    pauseAllUploads(containerId) {
        const container = this.containers.get(containerId);
        if (!container) return;
        
        container.fileUploads.forEach(upload => {
            if (upload.status === 'uploading' && upload.xhr) {
                upload.xhr.pause?.();
                upload.status = 'paused';
            }
        });
        
        container.update();
    }
    
    resumeAllUploads(containerId) {
        const container = this.containers.get(containerId);
        if (!container) return;
        
        container.fileUploads.forEach(upload => {
            if (upload.status === 'paused' && upload.xhr) {
                upload.xhr.resume?.();
                upload.status = 'uploading';
            }
        });
        
        container.update();
    }
    
    cancelAllUploads(containerId) {
        const container = this.containers.get(containerId);
        if (!container) return;
        
        container.fileUploads.forEach(upload => {
            if (['pending', 'uploading', 'paused'].includes(upload.status)) {
                if (upload.xhr) {
                    upload.xhr.abort();
                }
                upload.status = 'cancelled';
            }
        });
        
        container.clear();
    }
    
    retryFailedUploads(containerId) {
        const container = this.containers.get(containerId);
        if (!container) return;
        
        const failedUploads = Array.from(container.fileUploads.values())
            .filter(upload => upload.status === 'error');
        
        failedUploads.forEach(upload => {
            upload.status = 'pending';
            upload.error = null;
            upload.progress = 0;
            // 重新启动上传
            this.startUpload(upload);
        });
        
        container.update();
    }
    
    // 统计和监控
    getContainerStatistics(containerId) {
        const container = this.containers.get(containerId);
        if (!container) return null;
        
        const uploads = Array.from(container.fileUploads.values());
        const stats = {
            total: uploads.length,
            pending: 0,
            uploading: 0,
            paused: 0,
            completed: 0,
            error: 0,
            cancelled: 0,
            totalSize: 0,
            uploadedSize: 0,
            averageProgress: 0
        };
        
        uploads.forEach(upload => {
            stats[upload.status]++;
            stats.totalSize += upload.file.size;
            stats.uploadedSize += (upload.file.size * upload.progress / 100);
        });
        
        if (uploads.length > 0) {
            stats.averageProgress = uploads.reduce((sum, upload) => 
                sum + upload.progress, 0) / uploads.length;
        }
        
        return stats;
    }
    
    getAllContainerStatistics() {
        const allStats = {};
        
        for (const [containerId, container] of this.containers.entries()) {
            allStats[containerId] = this.getContainerStatistics(containerId);
        }
        
        return allStats;
    }
    
    // 事件系统
    triggerEvent(eventType, data) {
        const event = new CustomEvent(`fileUploadContainer:${eventType}`, {
            detail: data
        });
        document.dispatchEvent(event);
    }
    
    // 清理
    destroyContainer(containerId) {
        const container = this.containers.get(containerId);
        if (!container) return;
        
        // 取消所有上传
        this.cancelAllUploads(containerId);
        
        // 清理资源
        container.destroy();
        this.containers.delete(containerId);
        this.uploadGroups.delete(containerId);
    }
    
    destroyAllContainers() {
        for (const containerId of this.containers.keys()) {
            this.destroyContainer(containerId);
        }
    }
}

// 增强的容器组件
class EnhancedFileUploadContainer extends FileUploadProgressContainer {
    static template = "web.EnhancedFileUploadContainer";
    
    constructor(options) {
        super();
        this.id = options.id;
        this.layout = options.layout || 'vertical';
        this.maxVisible = options.maxVisible || 5;
        this.autoHide = options.autoHide || true;
        this.groupBy = options.groupBy;
        this.onUploadComplete = options.onUploadComplete;
        this.onUploadError = options.onUploadError;
        this.onContainerEmpty = options.onContainerEmpty;
        
        this.state = useState({
            visible: false,
            uploads: new Map(),
            groups: new Map(),
            showAll: false
        });
    }
    
    addUpload(upload) {
        this.state.uploads.set(upload.id, upload);
        this.state.visible = true;
        
        if (this.groupBy) {
            this.addToGroup(upload);
        }
    }
    
    removeUpload(uploadId) {
        this.state.uploads.delete(uploadId);
        
        if (this.state.uploads.size === 0) {
            this.state.visible = false;
            if (this.onContainerEmpty) {
                this.onContainerEmpty();
            }
        }
    }
    
    addToGroup(upload) {
        const groupKey = this.getGroupKey(upload);
        
        if (!this.state.groups.has(groupKey)) {
            this.state.groups.set(groupKey, []);
        }
        
        this.state.groups.get(groupKey).push(upload);
    }
    
    getGroupKey(upload) {
        // 实现分组逻辑
        return 'default';
    }
    
    update() {
        // 触发重新渲染
        this.render();
    }
    
    clear() {
        this.state.uploads.clear();
        this.state.groups.clear();
        this.state.visible = false;
    }
    
    hide() {
        this.state.visible = false;
    }
    
    show() {
        this.state.visible = true;
    }
    
    toggleShowAll() {
        this.state.showAll = !this.state.showAll;
    }
    
    getVisibleUploads() {
        const uploads = Array.from(this.state.uploads.values());
        
        if (this.state.showAll || uploads.length <= this.maxVisible) {
            return uploads;
        }
        
        return uploads.slice(0, this.maxVisible);
    }
    
    getHiddenCount() {
        const totalCount = this.state.uploads.size;
        return Math.max(0, totalCount - this.maxVisible);
    }
    
    showErrorNotification(upload, error) {
        // 显示错误通知
        console.error(`Upload failed for ${upload.file.name}:`, error);
    }
    
    destroy() {
        this.clear();
        // 清理其他资源
    }
}

// 使用示例
const containerSystem = new AdvancedFileUploadContainerSystem();

// 创建不同类型的容器
const mainContainer = containerSystem.createContainer('main', {
    template: 'default',
    displayRule: 'hasUploads'
});

const compactContainer = containerSystem.createContainer('sidebar', {
    template: 'compact',
    displayRule: 'hasActiveUploads'
});

// 添加上传到容器
const fileUpload = {
    id: 'upload_123',
    file: new File(['content'], 'document.pdf'),
    progress: 0,
    status: 'pending'
};

containerSystem.addUploadToContainer('main', fileUpload);
```

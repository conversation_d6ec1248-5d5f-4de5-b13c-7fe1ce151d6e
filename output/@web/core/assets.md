# @web/core/assets.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/assets.js`
- **原始路径**: `/web/static/src/core/assets.js`
- **代码行数**: 226行
- **作用**: 动态加载JavaScript和CSS资源，管理资源包和懒加载组件

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 动态资源加载的实现原理和最佳实践
- 资源缓存机制和重试策略
- Bundle系统的工作原理
- 懒加载组件的设计模式
- 错误处理和性能优化技巧

## 📚 核心概念

### 什么是Assets系统？
Assets系统是Odoo Web框架中的**动态资源管理器**，主要功能：
- **动态加载**: 按需加载JavaScript和CSS文件
- **缓存管理**: 避免重复加载相同资源
- **Bundle管理**: 管理资源包的加载和依赖
- **懒加载**: 支持组件的延迟加载

### 基本使用模式
```javascript
// 导入assets模块
const { loadJS, loadCSS, loadBundle } = require("@web/core/assets");

// 动态加载JavaScript文件
await loadJS("/web/static/src/my_script.js");

// 动态加载CSS文件
await loadCSS("/web/static/src/my_styles.css");

// 加载资源包
await loadBundle("web.assets_backend");

// 使用懒加载组件
<LazyComponent Component="MyComponent" bundle="my.assets" props="{{componentProps}}"/>
```

## 🏗️ 核心架构分析

### 缓存系统
```javascript
const cacheMap = new Map();

const computeCacheMap = () => {
    // 扫描已存在的script标签
    for (const script of document.head.querySelectorAll("script[src]")) {
        cacheMap.set(script.src, Promise.resolve(true));
    }
    // 扫描已存在的link标签
    for (const link of document.head.querySelectorAll("link[rel=stylesheet][href]")) {
        cacheMap.set(link.href, Promise.resolve(true));
    }
};
```

**缓存机制特点**：
- **初始化扫描**: 页面加载时扫描已存在的资源
- **Promise缓存**: 缓存Promise而非结果，避免重复请求
- **自动清理**: 加载失败时自动清除缓存

### 重试策略
```javascript
const assets = {
    retries: {
        count: 3,           // 最大重试次数
        delay: 5000,        // 基础延迟时间(ms)
        extraDelay: 2500,   // 额外延迟时间(ms)
    },
};
```

**重试算法**：
- **指数退避**: 每次重试延迟时间递增
- **最大次数**: 限制重试次数避免无限重试
- **仅CSS**: 只对CSS加载失败进行重试

## 🔍 核心方法详解

### 1. loadJS() - 动态加载JavaScript
```javascript
assets.loadJS = async function loadJS(url) {
    // 1. 检查缓存
    if (cacheMap.has(url)) {
        return cacheMap.get(url);
    }

    // 2. 创建script元素
    const scriptEl = document.createElement("script");
    scriptEl.type = url.includes("web/static/lib/pdfjs/") ? "module" : "text/javascript";
    scriptEl.src = url;

    // 3. 创建Promise处理加载结果
    const promise = new Promise((resolve, reject) => {
        onLoadAndError(scriptEl, resolve, () => {
            cacheMap.delete(url);
            reject(new AssetsLoadingError(`The loading of ${url} failed`));
        });
    });

    // 4. 缓存Promise并添加到DOM
    cacheMap.set(url, promise);
    document.head.appendChild(scriptEl);

    return promise;
};
```

**关键特性**：
- **模块类型检测**: PDF.js使用ES模块类型
- **错误处理**: 加载失败时清除缓存并抛出错误
- **DOM操作**: 动态添加script标签到head

### 2. loadCSS() - 动态加载CSS
```javascript
assets.loadCSS = async function loadCSS(url, retryCount = 0) {
    // 1. 检查缓存
    if (cacheMap.has(url)) {
        return cacheMap.get(url);
    }

    // 2. 创建link元素
    const linkEl = document.createElement("link");
    linkEl.type = "text/css";
    linkEl.rel = "stylesheet";
    linkEl.href = url;

    // 3. 创建Promise处理加载结果（包含重试逻辑）
    const promise = new Promise((resolve, reject) => {
        const onError = (...args) => {
            cacheMap.delete(url);
            return reject(...args);
        };

        onLoadAndError(linkEl, resolve, async () => {
            cacheMap.delete(url);
            if (retryCount < assets.retries.count) {
                // 计算延迟时间：基础延迟 + 重试次数 * 额外延迟
                await new Promise((resolve) =>
                    setTimeout(
                        resolve,
                        assets.retries.delay + assets.retries.extraDelay * retryCount
                    )
                );
                linkEl.remove();
                // 递归重试
                loadCSS(url, retryCount + 1)
                    .then(resolve)
                    .catch(onError);
            } else {
                onError(new AssetsLoadingError(`The loading of ${url} failed`));
            }
        });
    });

    cacheMap.set(url, promise);
    document.head.appendChild(linkEl);
    return promise;
};
```

**重试机制详解**：
- **指数退避**: 第1次重试5s，第2次重试7.5s，第3次重试10s
- **元素清理**: 重试前移除失败的link元素
- **递归调用**: 使用递归实现重试逻辑

### 3. getBundle() - 获取资源包信息
```javascript
assets.getBundle = async function getBundle(bundleName) {
    if (!cacheMap.has(bundleName)) {
        // 1. 构建Bundle URL
        const url = new URL(`/web/bundle/${bundleName}`, location.origin);
        for (const [key, value] of Object.entries(session.bundle_params || {})) {
            url.searchParams.set(key, value);
        }

        // 2. 获取Bundle描述信息
        const promise = new Promise((resolve, reject) => {
            browser
                .fetch(url.href)
                .then((response) => {
                    return response.json().then((json) => {
                        const assets = {
                            cssLibs: [],
                            jsLibs: [],
                        };
                        // 3. 解析Bundle内容
                        for (const key in json) {
                            const file = json[key];
                            if (file.type === "link" && file.src) {
                                assets.cssLibs.push(file.src);
                            } else if (file.type === "script" && file.src) {
                                assets.jsLibs.push(file.src);
                            }
                        }
                        resolve(assets);
                    });
                })
                .catch((...args) => {
                    cacheMap.delete(bundleName);
                    reject(...args);
                });
        });
        cacheMap.set(bundleName, promise);
    }
    return cacheMap.get(bundleName);
};
```

**Bundle系统特点**：
- **服务器端生成**: Bundle信息由服务器动态生成
- **参数传递**: 支持通过session.bundle_params传递参数
- **类型分离**: 自动分离CSS和JavaScript资源

### 4. loadBundle() - 加载资源包
```javascript
assets.loadBundle = async function loadBundle(bundleName) {
    if (typeof bundleName === "string") {
        // 1. 获取Bundle描述
        const desc = await assets.getBundle(bundleName);

        // 2. 并行加载所有资源
        return Promise.all([
            ...(desc.cssLibs || []).map(assets.loadCSS),
            ...(desc.jsLibs || []).map(assets.loadJS),
        ]);
    } else {
        throw new Error(
            `loadBundle(bundleName:string) accepts only bundleName argument as a string ! Not ${JSON.stringify(
                bundleName
            )} as ${typeof bundleName}`
        );
    }
};
```

**并行加载策略**：
- **Promise.all**: 并行加载所有资源提高性能
- **类型无关**: CSS和JavaScript资源同时加载
- **错误传播**: 任一资源加载失败都会导致整个Bundle加载失败

## 🎨 LazyComponent - 懒加载组件

### 组件定义
```javascript
const LazyComponent = class LazyComponent extends Component {
    static template = xml`<t t-component="Component" t-props="props.props"/>`;
    static props = {
        Component: String,      // 组件名称
        bundle: String,         // 资源包名称
        props: { type: Object, optional: true }, // 传递给组件的属性
    };

    setup() {
        onWillStart(async () => {
            // 1. 加载资源包
            await loadBundle(this.props.bundle);
            // 2. 从注册表获取组件
            this.Component = registry.category("lazy_components").get(this.props.Component);
        });
    }
};
```

### 使用示例
```javascript
// 在模板中使用懒加载组件
static template = xml`
    <div class="main-content">
        <LazyComponent
            Component="'AdvancedChart'"
            bundle="'web.chart_assets'"
            props="chartProps"/>
    </div>
`;

// 注册懒加载组件
registry.category("lazy_components").add("AdvancedChart", AdvancedChartComponent);
```

**懒加载优势**：
- **按需加载**: 只在需要时加载组件和相关资源
- **性能优化**: 减少初始页面加载时间
- **代码分割**: 实现应用的代码分割策略

## 🔄 事件处理机制

### onLoadAndError 工具函数
```javascript
const onLoadAndError = (el, onLoad, onError) => {
    const onLoadListener = (event) => {
        removeListeners();
        onLoad(event);
    };

    const onErrorListener = (error) => {
        removeListeners();
        onError(error);
    };

    const removeListeners = () => {
        el.removeEventListener("load", onLoadListener);
        el.removeEventListener("error", onErrorListener);
    };

    el.addEventListener("load", onLoadListener);
    el.addEventListener("error", onErrorListener);
};
```

**设计特点**：
- **自动清理**: 事件触发后自动移除监听器
- **内存安全**: 避免事件监听器泄漏
- **统一接口**: 为script和link元素提供统一的事件处理

## 🎯 实际应用场景

### 1. 动态功能模块加载
```javascript
class FeatureManager {
    constructor() {
        this.loadedFeatures = new Set();
    }

    async loadFeature(featureName) {
        if (this.loadedFeatures.has(featureName)) {
            return;
        }

        try {
            // 加载功能相关的资源包
            await loadBundle(`feature.${featureName}`);

            // 标记功能已加载
            this.loadedFeatures.add(featureName);

            console.log(`Feature ${featureName} loaded successfully`);
        } catch (error) {
            console.error(`Failed to load feature ${featureName}:`, error);
            throw error;
        }
    }

    async loadMultipleFeatures(featureNames) {
        // 并行加载多个功能
        const promises = featureNames.map(name => this.loadFeature(name));
        return Promise.all(promises);
    }
}

// 使用示例
const featureManager = new FeatureManager();
await featureManager.loadFeature('advanced_reporting');
```

### 2. 主题和样式动态切换
```javascript
class ThemeManager {
    constructor() {
        this.currentTheme = 'default';
        this.loadedThemes = new Set(['default']);
    }

    async switchTheme(themeName) {
        if (themeName === this.currentTheme) {
            return;
        }

        // 加载新主题的CSS
        if (!this.loadedThemes.has(themeName)) {
            try {
                await loadCSS(`/web/static/src/themes/${themeName}/theme.css`);
                this.loadedThemes.add(themeName);
            } catch (error) {
                console.error(`Failed to load theme ${themeName}:`, error);
                return;
            }
        }

        // 切换主题类名
        document.body.className = document.body.className
            .replace(/theme-\w+/, `theme-${themeName}`);

        this.currentTheme = themeName;

        // 保存用户偏好
        localStorage.setItem('preferred_theme', themeName);
    }

    async preloadThemes(themeNames) {
        // 预加载主题资源
        const promises = themeNames.map(async (theme) => {
            if (!this.loadedThemes.has(theme)) {
                try {
                    await loadCSS(`/web/static/src/themes/${theme}/theme.css`);
                    this.loadedThemes.add(theme);
                } catch (error) {
                    console.warn(`Failed to preload theme ${theme}:`, error);
                }
            }
        });

        return Promise.allSettled(promises);
    }
}
```

### 3. 插件系统实现
```javascript
class PluginManager {
    constructor() {
        this.plugins = new Map();
        this.pluginDependencies = new Map();
    }

    async loadPlugin(pluginName, config = {}) {
        if (this.plugins.has(pluginName)) {
            return this.plugins.get(pluginName);
        }

        try {
            // 1. 加载插件资源包
            await loadBundle(`plugin.${pluginName}`);

            // 2. 从注册表获取插件类
            const PluginClass = registry.category("plugins").get(pluginName);

            // 3. 实例化插件
            const plugin = new PluginClass(config);

            // 4. 初始化插件
            if (plugin.initialize) {
                await plugin.initialize();
            }

            // 5. 注册插件
            this.plugins.set(pluginName, plugin);

            console.log(`Plugin ${pluginName} loaded and initialized`);
            return plugin;

        } catch (error) {
            console.error(`Failed to load plugin ${pluginName}:`, error);
            throw error;
        }
    }

    async loadPluginWithDependencies(pluginName, config = {}) {
        // 获取插件依赖
        const dependencies = this.pluginDependencies.get(pluginName) || [];

        // 先加载依赖
        for (const dep of dependencies) {
            await this.loadPlugin(dep);
        }

        // 再加载主插件
        return this.loadPlugin(pluginName, config);
    }

    registerDependency(pluginName, dependencies) {
        this.pluginDependencies.set(pluginName, dependencies);
    }
}

// 使用示例
const pluginManager = new PluginManager();

// 注册插件依赖
pluginManager.registerDependency('advanced_editor', ['syntax_highlighter', 'autocomplete']);

// 加载插件
await pluginManager.loadPluginWithDependencies('advanced_editor', {
    theme: 'dark',
    language: 'javascript'
});
```

## 🛠️ 实践练习

### 练习1: 资源加载监控器
```javascript
class AssetLoadMonitor {
    constructor() {
        this.loadStats = {
            js: { success: 0, failed: 0, totalTime: 0 },
            css: { success: 0, failed: 0, totalTime: 0 },
            bundles: { success: 0, failed: 0, totalTime: 0 }
        };
        this.loadHistory = [];
        this.wrapAssetMethods();
    }

    wrapAssetMethods() {
        // 包装loadJS方法
        const originalLoadJS = assets.loadJS;
        assets.loadJS = async (url) => {
            const startTime = performance.now();
            try {
                const result = await originalLoadJS(url);
                this.recordLoad('js', url, performance.now() - startTime, true);
                return result;
            } catch (error) {
                this.recordLoad('js', url, performance.now() - startTime, false, error);
                throw error;
            }
        };

        // 包装loadCSS方法
        const originalLoadCSS = assets.loadCSS;
        assets.loadCSS = async (url, retryCount) => {
            const startTime = performance.now();
            try {
                const result = await originalLoadCSS(url, retryCount);
                this.recordLoad('css', url, performance.now() - startTime, true);
                return result;
            } catch (error) {
                this.recordLoad('css', url, performance.now() - startTime, false, error);
                throw error;
            }
        };

        // 包装loadBundle方法
        const originalLoadBundle = assets.loadBundle;
        assets.loadBundle = async (bundleName) => {
            const startTime = performance.now();
            try {
                const result = await originalLoadBundle(bundleName);
                this.recordLoad('bundles', bundleName, performance.now() - startTime, true);
                return result;
            } catch (error) {
                this.recordLoad('bundles', bundleName, performance.now() - startTime, false, error);
                throw error;
            }
        };
    }

    recordLoad(type, url, duration, success, error = null) {
        // 更新统计
        const stats = this.loadStats[type];
        if (success) {
            stats.success++;
        } else {
            stats.failed++;
        }
        stats.totalTime += duration;

        // 记录历史
        this.loadHistory.push({
            type,
            url,
            duration,
            success,
            error: error?.message,
            timestamp: Date.now()
        });

        // 保持最近100条记录
        if (this.loadHistory.length > 100) {
            this.loadHistory = this.loadHistory.slice(-100);
        }

        // 输出慢加载警告
        if (duration > 3000) { // 3秒
            console.warn(`Slow asset loading detected: ${url} took ${duration.toFixed(2)}ms`);
        }
    }

    getReport() {
        const report = {
            summary: {},
            slowLoads: this.loadHistory
                .filter(load => load.duration > 1000)
                .sort((a, b) => b.duration - a.duration)
                .slice(0, 10),
            recentFailures: this.loadHistory
                .filter(load => !load.success)
                .slice(-10)
        };

        // 计算各类型统计
        for (const [type, stats] of Object.entries(this.loadStats)) {
            const total = stats.success + stats.failed;
            report.summary[type] = {
                total,
                successRate: total > 0 ? (stats.success / total * 100).toFixed(2) + '%' : '0%',
                averageTime: stats.success > 0 ? (stats.totalTime / stats.success).toFixed(2) + 'ms' : '0ms',
                totalTime: stats.totalTime.toFixed(2) + 'ms'
            };
        }

        return report;
    }
}

// 启用监控
const monitor = new AssetLoadMonitor();

// 定期输出报告
setInterval(() => {
    console.log('Asset Load Report:', monitor.getReport());
}, 30000); // 每30秒
```

### 练习2: 智能资源预加载器
```javascript
class SmartPreloader {
    constructor() {
        this.preloadQueue = new Set();
        this.preloadHistory = new Map();
        this.userBehaviorPatterns = new Map();
        this.setupBehaviorTracking();
    }

    setupBehaviorTracking() {
        // 跟踪用户行为模式
        document.addEventListener('click', (event) => {
            this.recordUserAction('click', event.target);
        });

        // 跟踪路由变化
        window.addEventListener('hashchange', () => {
            this.recordUserAction('navigation', window.location.hash);
        });
    }

    recordUserAction(type, target) {
        const key = `${type}:${this.getElementIdentifier(target)}`;
        const count = this.userBehaviorPatterns.get(key) || 0;
        this.userBehaviorPatterns.set(key, count + 1);

        // 基于行为模式预测需要的资源
        this.predictAndPreload(type, target);
    }

    getElementIdentifier(element) {
        if (typeof element === 'string') return element;
        if (!element) return 'unknown';

        return element.id ||
               element.className ||
               element.tagName ||
               'unknown';
    }

    async predictAndPreload(actionType, target) {
        // 简单的预测逻辑
        const predictions = this.getPredictions(actionType, target);

        for (const prediction of predictions) {
            await this.preloadResource(prediction);
        }
    }

    getPredictions(actionType, target) {
        const predictions = [];

        // 基于元素类型预测
        if (actionType === 'click') {
            const identifier = this.getElementIdentifier(target);

            if (identifier.includes('chart')) {
                predictions.push({ type: 'bundle', name: 'web.chart_assets' });
            } else if (identifier.includes('report')) {
                predictions.push({ type: 'bundle', name: 'web.report_assets' });
            } else if (identifier.includes('calendar')) {
                predictions.push({ type: 'bundle', name: 'web.calendar_assets' });
            }
        }

        return predictions;
    }

    async preloadResource(prediction) {
        const key = `${prediction.type}:${prediction.name}`;

        // 避免重复预加载
        if (this.preloadQueue.has(key)) {
            return;
        }

        this.preloadQueue.add(key);

        try {
            const startTime = performance.now();

            if (prediction.type === 'bundle') {
                await loadBundle(prediction.name);
            } else if (prediction.type === 'js') {
                await loadJS(prediction.name);
            } else if (prediction.type === 'css') {
                await loadCSS(prediction.name);
            }

            const duration = performance.now() - startTime;
            this.preloadHistory.set(key, {
                success: true,
                duration,
                timestamp: Date.now()
            });

            console.log(`Preloaded ${key} in ${duration.toFixed(2)}ms`);

        } catch (error) {
            this.preloadHistory.set(key, {
                success: false,
                error: error.message,
                timestamp: Date.now()
            });

            console.warn(`Failed to preload ${key}:`, error);
        } finally {
            this.preloadQueue.delete(key);
        }
    }

    // 手动预加载接口
    async preloadBundles(bundleNames) {
        const promises = bundleNames.map(name =>
            this.preloadResource({ type: 'bundle', name })
        );
        return Promise.allSettled(promises);
    }

    // 获取预加载统计
    getPreloadStats() {
        const stats = {
            total: this.preloadHistory.size,
            successful: 0,
            failed: 0,
            totalTime: 0,
            averageTime: 0
        };

        for (const [key, result] of this.preloadHistory.entries()) {
            if (result.success) {
                stats.successful++;
                stats.totalTime += result.duration;
            } else {
                stats.failed++;
            }
        }

        stats.averageTime = stats.successful > 0 ?
            stats.totalTime / stats.successful : 0;

        return stats;
    }
}

// 使用示例
const preloader = new SmartPreloader();

// 手动预加载常用资源
await preloader.preloadBundles([
    'web.chart_assets',
    'web.report_assets',
    'web.calendar_assets'
]);
```

### 练习3: 资源依赖管理器
```javascript
class AssetDependencyManager {
    constructor() {
        this.dependencies = new Map();
        this.loadOrder = [];
        this.loadedAssets = new Set();
    }

    // 注册资源依赖关系
    registerDependency(asset, dependencies = []) {
        this.dependencies.set(asset, dependencies);
    }

    // 批量注册依赖
    registerDependencies(dependencyMap) {
        for (const [asset, deps] of Object.entries(dependencyMap)) {
            this.registerDependency(asset, deps);
        }
    }

    // 解析依赖顺序
    resolveDependencies(assets) {
        const resolved = [];
        const visiting = new Set();
        const visited = new Set();

        const visit = (asset) => {
            if (visited.has(asset)) return;
            if (visiting.has(asset)) {
                throw new Error(`Circular dependency detected: ${asset}`);
            }

            visiting.add(asset);

            const deps = this.dependencies.get(asset) || [];
            for (const dep of deps) {
                visit(dep);
            }

            visiting.delete(asset);
            visited.add(asset);
            resolved.push(asset);
        };

        for (const asset of assets) {
            visit(asset);
        }

        return resolved;
    }

    // 按依赖顺序加载资源
    async loadAssetsWithDependencies(assets) {
        try {
            // 解析加载顺序
            const loadOrder = this.resolveDependencies(assets);

            // 按顺序加载
            for (const asset of loadOrder) {
                if (!this.loadedAssets.has(asset)) {
                    await this.loadSingleAsset(asset);
                    this.loadedAssets.add(asset);
                }
            }

            this.loadOrder = loadOrder;
            return loadOrder;

        } catch (error) {
            console.error('Failed to load assets with dependencies:', error);
            throw error;
        }
    }

    async loadSingleAsset(asset) {
        // 判断资源类型并加载
        if (asset.endsWith('.js')) {
            return loadJS(asset);
        } else if (asset.endsWith('.css')) {
            return loadCSS(asset);
        } else {
            // 假设是bundle名称
            return loadBundle(asset);
        }
    }

    // 获取资源的依赖树
    getDependencyTree(asset) {
        const tree = { name: asset, dependencies: [] };
        const deps = this.dependencies.get(asset) || [];

        for (const dep of deps) {
            tree.dependencies.push(this.getDependencyTree(dep));
        }

        return tree;
    }

    // 检查循环依赖
    checkCircularDependencies() {
        const issues = [];

        for (const asset of this.dependencies.keys()) {
            try {
                this.resolveDependencies([asset]);
            } catch (error) {
                if (error.message.includes('Circular dependency')) {
                    issues.push(asset);
                }
            }
        }

        return issues;
    }

    // 生成依赖图的可视化数据
    generateDependencyGraph() {
        const nodes = [];
        const edges = [];
        const nodeSet = new Set();

        for (const [asset, deps] of this.dependencies.entries()) {
            if (!nodeSet.has(asset)) {
                nodes.push({ id: asset, label: asset });
                nodeSet.add(asset);
            }

            for (const dep of deps) {
                if (!nodeSet.has(dep)) {
                    nodes.push({ id: dep, label: dep });
                    nodeSet.add(dep);
                }

                edges.push({ from: dep, to: asset });
            }
        }

        return { nodes, edges };
    }
}

// 使用示例
const depManager = new AssetDependencyManager();

// 注册依赖关系
depManager.registerDependencies({
    'web.chart_assets': ['web.core_assets', 'web.utils_assets'],
    'web.report_assets': ['web.core_assets', 'web.chart_assets'],
    'web.advanced_features': ['web.report_assets', 'web.calendar_assets'],
    'web.calendar_assets': ['web.core_assets', 'web.utils_assets']
});

// 检查循环依赖
const circularIssues = depManager.checkCircularDependencies();
if (circularIssues.length > 0) {
    console.warn('Circular dependencies detected:', circularIssues);
}

// 按依赖顺序加载资源
try {
    const loadOrder = await depManager.loadAssetsWithDependencies([
        'web.advanced_features'
    ]);
    console.log('Assets loaded in order:', loadOrder);
} catch (error) {
    console.error('Failed to load assets:', error);
}
```

## 🔧 调试技巧

### 查看资源加载状态
```javascript
// 查看缓存状态
console.log('Asset cache:', cacheMap);

// 查看已加载的脚本
const loadedScripts = Array.from(document.querySelectorAll('script[src]'))
    .map(script => script.src);
console.log('Loaded scripts:', loadedScripts);

// 查看已加载的样式表
const loadedStyles = Array.from(document.querySelectorAll('link[rel="stylesheet"]'))
    .map(link => link.href);
console.log('Loaded stylesheets:', loadedStyles);
```

### 监控资源加载性能
```javascript
// 监控资源加载时间
const originalLoadJS = assets.loadJS;
assets.loadJS = async function(url) {
    const start = performance.now();
    try {
        const result = await originalLoadJS(url);
        console.log(`JS loaded: ${url} (${(performance.now() - start).toFixed(2)}ms)`);
        return result;
    } catch (error) {
        console.error(`JS failed: ${url} (${(performance.now() - start).toFixed(2)}ms)`, error);
        throw error;
    }
};
```

## 📊 性能考虑

### 优化策略
1. **并行加载**: 使用Promise.all并行加载多个资源
2. **缓存机制**: 避免重复加载相同资源
3. **重试策略**: CSS加载失败时自动重试
4. **懒加载**: 按需加载非关键资源

### 最佳实践
```javascript
// ✅ 好的做法：并行加载
const resources = await Promise.all([
    loadJS('/path/to/script1.js'),
    loadJS('/path/to/script2.js'),
    loadCSS('/path/to/style1.css')
]);

// ❌ 不好的做法：串行加载
await loadJS('/path/to/script1.js');
await loadJS('/path/to/script2.js');
await loadCSS('/path/to/style1.css');

// ✅ 好的做法：使用Bundle
await loadBundle('my.feature_bundle');

// ❌ 不好的做法：逐个加载
await loadJS('/path/to/feature/script1.js');
await loadJS('/path/to/feature/script2.js');
await loadCSS('/path/to/feature/style.css');
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解动态资源加载的实现原理
- [ ] 掌握缓存机制和重试策略
- [ ] 理解Bundle系统的工作原理
- [ ] 能够使用LazyComponent实现懒加载
- [ ] 掌握资源加载的性能优化技巧
- [ ] 能够实现自定义的资源管理功能

## 🚀 下一步学习
学完Assets系统后，建议继续学习：
1. **浏览器API** (`@web/core/browser/`) - 理解浏览器兼容性处理
2. **网络层** (`@web/core/network/`) - 学习RPC和网络请求
3. **模块系统** (回顾module_loader) - 深入理解模块加载机制

## 💡 重要提示
- Assets系统是性能优化的关键组件
- 理解缓存机制对避免重复加载至关重要
- Bundle系统实现了代码分割和按需加载
- LazyComponent是实现懒加载的最佳实践
```
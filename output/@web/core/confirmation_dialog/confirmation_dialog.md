# ConfirmationDialog - 确认对话框组件

## 概述

`confirmation_dialog.js` 是 Odoo Web 核心模块的确认对话框组件，提供了标准化的用户确认交互界面。该模块包含ConfirmationDialog和AlertDialog两个组件，支持自定义标题、内容、按钮样式和回调函数，为Odoo Web应用提供了一致的确认和警告对话框体验，广泛应用于删除确认、操作警告和用户决策等场景。

## 文件信息
- **路径**: `/web/static/src/core/confirmation_dialog/confirmation_dialog.js`
- **行数**: 109
- **模块**: `@web/core/confirmation_dialog/confirmation_dialog`

## 依赖关系

```javascript
// 核心依赖
'@web/core/dialog/dialog'        // 基础对话框组件
'@web/core/l10n/translation'     // 国际化翻译
'@web/core/utils/hooks'          // 工具钩子
'@odoo/owl'                      // OWL框架
```

## 核心功能

### 1. 删除确认消息

```javascript
const deleteConfirmationMessage = _t(
    `Ready to make your record disappear into thin air? Are you sure?
It will be gone forever!

Think twice before you click that 'Delete' button!`
);
```

**删除确认功能**:
- **友好提示**: 使用幽默友好的语言提示用户
- **严重性强调**: 强调删除操作的不可逆性
- **国际化**: 支持多语言翻译
- **标准化**: 提供统一的删除确认消息

### 2. ConfirmationDialog组件

#### 属性定义
```javascript
static props = {
    close: Function,
    title: {
        validate: (m) => {
            return (
                typeof m === "string" ||
                (typeof m === "object" && typeof m.toString === "function")
            );
        },
        optional: true,
    },
    body: { type: String, optional: true },
    confirm: { type: Function, optional: true },
    confirmLabel: { type: String, optional: true },
    confirmClass: { type: String, optional: true },
    cancel: { type: Function, optional: true },
    cancelLabel: { type: String, optional: true },
    dismiss: { type: Function, optional: true },
};
```

**属性功能**:
- **关闭控制**: close函数控制对话框关闭
- **标题定制**: 支持字符串或可转换为字符串的对象
- **内容定制**: body属性设置对话框内容
- **按钮定制**: 支持自定义确认和取消按钮的标签和样式
- **回调函数**: 完整的确认、取消和关闭回调支持

#### 默认属性
```javascript
static defaultProps = {
    confirmLabel: _t("Ok"),
    cancelLabel: _t("Cancel"),
    confirmClass: "btn-primary",
    title: _t("Confirmation"),
};
```

**默认属性功能**:
- **国际化标签**: 默认的确认和取消按钮标签
- **主要按钮样式**: 确认按钮使用主要样式
- **标准标题**: 默认的确认对话框标题

### 3. 组件初始化

```javascript
setup() {
    this.env.dialogData.dismiss = () => this._dismiss();
    this.modalRef = useChildRef();
    this.isProcess = false;
}
```

**初始化功能**:
- **关闭绑定**: 将dismiss函数绑定到环境数据
- **引用管理**: 创建模态框的子引用
- **处理状态**: 初始化按钮处理状态

### 4. 按钮执行逻辑

```javascript
async execButton(callback) {
    if (this.isProcess) {
        return;
    }
    this.setButtonsDisabled(true);
    if (callback) {
        let shouldClose;
        try {
            shouldClose = await callback();
        } catch (e) {
            this.props.close();
            throw e;
        }
        if (shouldClose === false) {
            this.setButtonsDisabled(false);
            return;
        }
    }
    this.props.close();
}
```

**按钮执行功能**:
- **重复点击防护**: 防止按钮被重复点击
- **按钮禁用**: 执行期间禁用所有按钮
- **异步支持**: 支持异步回调函数
- **错误处理**: 捕获回调错误并关闭对话框
- **条件关闭**: 根据回调返回值决定是否关闭

### 5. 按钮状态管理

```javascript
setButtonsDisabled(disabled) {
    this.isProcess = disabled;
    if (!this.modalRef.el) {
        return; // safety belt for stable versions
    }
    for (const button of [...this.modalRef.el.querySelectorAll(".modal-footer button")]) {
        button.disabled = disabled;
    }
}
```

**状态管理功能**:
- **全局状态**: 更新组件的处理状态
- **安全检查**: 检查模态框元素是否存在
- **批量操作**: 批量设置所有按钮的禁用状态
- **DOM操作**: 直接操作DOM元素的disabled属性

### 6. 事件处理方法

```javascript
async _cancel() {
    return this.execButton(this.props.cancel);
}

async _confirm() {
    return this.execButton(this.props.confirm);
}

async _dismiss() {
    return this.execButton(this.props.dismiss || this.props.cancel);
}
```

**事件处理功能**:
- **取消处理**: 执行取消回调
- **确认处理**: 执行确认回调
- **关闭处理**: 执行关闭回调，降级到取消回调
- **统一执行**: 所有事件都通过execButton统一处理

### 7. AlertDialog组件

```javascript
const AlertDialog = class AlertDialog extends ConfirmationDialog {
    static template = "web.AlertDialog";
    static props = {
        ...ConfirmationDialog.props,
        contentClass: { type: String, optional: true },
    };
    static defaultProps = {
        ...ConfirmationDialog.defaultProps,
        title: _t("Alert"),
    };
}
```

**AlertDialog功能**:
- **继承扩展**: 继承ConfirmationDialog的所有功能
- **样式定制**: 添加contentClass属性支持内容样式定制
- **警告标题**: 默认使用"Alert"作为标题
- **模板差异**: 使用专门的AlertDialog模板

## 使用场景

### 1. 删除确认

```javascript
// 删除记录确认
this.dialogService.add(ConfirmationDialog, {
    title: _t("Delete Record"),
    body: deleteConfirmationMessage,
    confirmLabel: _t("Delete"),
    confirmClass: "btn-danger",
    confirm: async () => {
        await this.deleteRecord();
        this.notification.add(_t("Record deleted successfully"), {
            type: "success"
        });
    },
    cancel: () => {
        console.log("Delete cancelled");
    }
});
```

### 2. 操作确认

```javascript
// 批量操作确认
this.dialogService.add(ConfirmationDialog, {
    title: _t("Batch Operation"),
    body: _t("This will affect %s records. Continue?", selectedCount),
    confirmLabel: _t("Proceed"),
    confirm: async () => {
        const result = await this.performBatchOperation();
        if (result.errors.length > 0) {
            // 返回false阻止对话框关闭
            this.showErrors(result.errors);
            return false;
        }
        return true; // 允许关闭
    }
});
```

### 3. 警告提示

```javascript
// 使用AlertDialog显示警告
this.dialogService.add(AlertDialog, {
    title: _t("Warning"),
    body: _t("Your session will expire in 5 minutes. Please save your work."),
    confirmLabel: _t("OK"),
    contentClass: "text-warning"
});
```

### 4. 自定义确认

```javascript
// 自定义样式和行为的确认对话框
this.dialogService.add(ConfirmationDialog, {
    title: _t("Custom Confirmation"),
    body: _t("Do you want to apply these changes?"),
    confirmLabel: _t("Apply"),
    confirmClass: "btn-success",
    cancelLabel: _t("Discard"),
    confirm: async () => {
        try {
            await this.applyChanges();
            this.notification.add(_t("Changes applied"), { type: "success" });
        } catch (error) {
            this.notification.add(_t("Failed to apply changes"), { type: "danger" });
            throw error; // 重新抛出错误
        }
    },
    dismiss: () => {
        // 自定义关闭行为
        this.discardChanges();
    }
});
```

## 增强示例

```javascript
// 增强的确认对话框
const EnhancedConfirmationDialog = {
    createAdvancedDialog: () => {
        class AdvancedConfirmationDialog extends ConfirmationDialog {
            static props = {
                ...ConfirmationDialog.props,
                icon: { type: String, optional: true },
                timeout: { type: Number, optional: true },
                showProgress: { type: Boolean, optional: true },
                customButtons: { type: Array, optional: true },
                onTimeout: { type: Function, optional: true }
            };

            static defaultProps = {
                ...ConfirmationDialog.defaultProps,
                showProgress: false,
                customButtons: []
            };

            setup() {
                super.setup();
                
                // 增强状态
                this.enhancedState = useState({
                    timeLeft: this.props.timeout,
                    progress: 0,
                    isTimedOut: false
                });

                // 超时处理
                if (this.props.timeout) {
                    this.setupTimeout();
                }
            }

            // 设置超时
            setupTimeout() {
                const startTime = Date.now();
                const timeout = this.props.timeout * 1000;

                this.timeoutInterval = setInterval(() => {
                    const elapsed = Date.now() - startTime;
                    const remaining = Math.max(0, timeout - elapsed);
                    
                    this.enhancedState.timeLeft = Math.ceil(remaining / 1000);
                    this.enhancedState.progress = (elapsed / timeout) * 100;

                    if (remaining <= 0) {
                        this.handleTimeout();
                    }
                }, 100);
            }

            // 处理超时
            handleTimeout() {
                clearInterval(this.timeoutInterval);
                this.enhancedState.isTimedOut = true;
                
                if (this.props.onTimeout) {
                    this.props.onTimeout();
                } else {
                    this._dismiss();
                }
            }

            // 增强的按钮执行
            async execButton(callback) {
                // 清除超时
                if (this.timeoutInterval) {
                    clearInterval(this.timeoutInterval);
                }

                // 显示进度
                if (this.props.showProgress) {
                    this.enhancedState.progress = 0;
                    this.startProgress();
                }

                try {
                    return await super.execButton(callback);
                } finally {
                    if (this.progressInterval) {
                        clearInterval(this.progressInterval);
                    }
                }
            }

            // 启动进度显示
            startProgress() {
                let progress = 0;
                this.progressInterval = setInterval(() => {
                    progress += Math.random() * 10;
                    this.enhancedState.progress = Math.min(progress, 90);
                }, 100);
            }

            // 自定义按钮处理
            async executeCustomButton(button) {
                if (button.callback) {
                    return this.execButton(button.callback);
                }
            }

            // 获取对话框状态
            getDialogState() {
                return {
                    isProcessing: this.isProcess,
                    timeLeft: this.enhancedState.timeLeft,
                    progress: this.enhancedState.progress,
                    isTimedOut: this.enhancedState.isTimedOut
                };
            }

            // 组件销毁时清理
            willDestroy() {
                if (this.timeoutInterval) {
                    clearInterval(this.timeoutInterval);
                }
                if (this.progressInterval) {
                    clearInterval(this.progressInterval);
                }
                super.willDestroy && super.willDestroy();
            }
        }

        return AdvancedConfirmationDialog;
    }
};

// 使用示例
const AdvancedDialog = EnhancedConfirmationDialog.createAdvancedDialog();

// 带超时的确认对话框
this.dialogService.add(AdvancedDialog, {
    title: _t("Session Timeout"),
    body: _t("Your session will expire soon. Do you want to extend it?"),
    icon: "fa-clock-o",
    timeout: 30, // 30秒超时
    showProgress: true,
    confirmLabel: _t("Extend Session"),
    confirm: async () => {
        await this.extendSession();
    },
    onTimeout: () => {
        this.logout();
    },
    customButtons: [
        {
            label: _t("Save & Logout"),
            class: "btn-warning",
            callback: async () => {
                await this.saveWork();
                this.logout();
            }
        }
    ]
});
```

## 技术特点

### 1. 继承设计
- ConfirmationDialog作为基础类
- AlertDialog继承并扩展功能
- 良好的代码复用

### 2. 异步支持
- 支持异步回调函数
- 完善的错误处理
- 条件关闭机制

### 3. 用户体验
- 防止重复点击
- 按钮状态管理
- 友好的提示信息

### 4. 国际化
- 完整的多语言支持
- 标准化的消息文本
- 可定制的标签

## 设计模式

### 1. 模板方法模式 (Template Method Pattern)
- 定义对话框的基本流程
- 子类可以重写特定步骤

### 2. 策略模式 (Strategy Pattern)
- 不同的回调策略
- 可配置的按钮行为

### 3. 观察者模式 (Observer Pattern)
- 事件驱动的交互
- 回调函数机制

## 注意事项

1. **异步处理**: 正确处理异步回调函数
2. **错误处理**: 捕获和处理回调中的错误
3. **用户体验**: 防止重复点击和提供清晰反馈
4. **内存管理**: 及时清理定时器和事件监听器

## 扩展建议

1. **动画效果**: 添加对话框显示和隐藏动画
2. **键盘支持**: 支持ESC键关闭和Enter键确认
3. **拖拽功能**: 支持对话框拖拽移动
4. **主题定制**: 支持不同的视觉主题
5. **声音提示**: 添加音效反馈

该确认对话框组件为Odoo Web应用提供了标准化的用户确认交互，通过完善的异步支持和错误处理确保了可靠的用户体验。

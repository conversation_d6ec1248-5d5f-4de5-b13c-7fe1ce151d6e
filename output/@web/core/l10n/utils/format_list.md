# Odoo 列表格式化工具 (Format List Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/l10n/utils/format_list.js`  
**原始路径**: `/web/static/src/core/l10n/utils/format_list.js`  
**模块类型**: 核心基础模块 - 国际化列表格式化  
**代码行数**: 81 行  
**依赖关系**: 
- `@web/core/user` - 用户信息（获取用户语言设置）

## 模块功能

列表格式化工具模块是 Odoo Web 客户端国际化系统的重要组件，负责：
- 根据用户语言环境格式化数组为本地化列表字符串
- 支持多种列表样式（标准、简短、或、单位等）
- 遵循 Unicode TR35-49 规范
- 使用浏览器原生 `Intl.ListFormat` API

## 核心概念

### 1. Unicode TR35-49 规范
Unicode TR35-49 定义了国际化列表模式的标准，包括：
- **连接列表** (conjunction): "A, B, and C"
- **分离列表** (disjunction): "A, B, or C"  
- **单位列表** (unit): "3 feet, 7 inches"

**参考**: [Unicode TR35 List Patterns](https://www.unicode.org/reports/tr35/tr35-49/tr35-general.html#ListPatterns)

### 2. Intl.ListFormat API
现代浏览器提供的原生国际化列表格式化 API：
- **自动本地化**: 根据语言环境自动选择合适的连接符
- **多种样式**: 支持不同长度和类型的列表格式
- **性能优化**: 浏览器原生实现，性能优异

## 列表样式配置

### 1. 样式映射表
```javascript
const LIST_STYLES = {
    standard: {
        type: "conjunction",
        style: "long",
    },
    "standard-short": {
        type: "conjunction", 
        style: "short",
    },
    or: {
        type: "disjunction",
        style: "long",
    },
    "or-short": {
        type: "disjunction",
        style: "short", 
    },
    unit: {
        type: "unit",
        style: "long",
    },
    "unit-short": {
        type: "unit",
        style: "short",
    },
    "unit-narrow": {
        type: "unit",
        style: "narrow",
    },
};
```

### 2. 样式详解

#### standard (标准连接列表)
- **类型**: conjunction (连接)
- **样式**: long (完整)
- **用途**: 一般的"和"列表
- **示例**: 
  - 英语: "January, February, and March"
  - 中文: "一月、二月和三月"
  - 法语: "janvier, février et mars"

#### standard-short (简短连接列表)
- **类型**: conjunction (连接)
- **样式**: short (简短)
- **用途**: 缩写值的"和"列表
- **示例**:
  - 英语: "Jan., Feb., and Mar."
  - 中文: "1月、2月和3月"

#### or (标准分离列表)
- **类型**: disjunction (分离)
- **样式**: long (完整)
- **用途**: 一般的"或"列表
- **示例**:
  - 英语: "January, February, or March"
  - 中文: "一月、二月或三月"
  - 法语: "janvier, février ou mars"

#### or-short (简短分离列表)
- **类型**: disjunction (分离)
- **样式**: short (简短)
- **用途**: 缩写值的"或"列表
- **示例**:
  - 英语: "Jan., Feb., or Mar."

#### unit (标准单位列表)
- **类型**: unit (单位)
- **样式**: long (完整)
- **用途**: 完整单位名称列表
- **示例**:
  - 英语: "3 feet, 7 inches"
  - 中文: "3英尺7英寸"

#### unit-short (简短单位列表)
- **类型**: unit (单位)
- **样式**: short (简短)
- **用途**: 简短单位名称列表
- **示例**:
  - 英语: "3 ft, 7 in"
  - 中文: "3尺7寸"

#### unit-narrow (紧凑单位列表)
- **类型**: unit (单位)
- **样式**: narrow (紧凑)
- **用途**: 极简单位符号列表，适用于空间受限场景
- **示例**:
  - 英语: "3′ 7″"
  - 中文: "3′7″"

## 核心函数分析

### formatList() 函数
```javascript
function formatList(list, { localeCode = "", style = "standard" } = {}) {
    const locale = localeCode || user.lang || "en-US";
    const formatter = new Intl.ListFormat(locale, LIST_STYLES[style]);
    return formatter.format(list);
}
```

**参数分析**:
- `list`: 要格式化的字符串数组
- `localeCode`: 可选的语言代码，默认使用用户语言设置
- `style`: 列表样式，默认为 "standard"

**实现逻辑**:
1. **语言检测**: 优先使用指定语言，回退到用户语言，最终回退到 "en-US"
2. **格式器创建**: 使用 `Intl.ListFormat` 创建格式化器
3. **样式应用**: 通过 `LIST_STYLES` 映射表获取对应的格式化选项
4. **列表格式化**: 调用格式化器的 `format` 方法

## 实际使用示例

### 1. 基本用法
```javascript
import { formatList } from "@web/core/l10n/utils/format_list";

// 标准连接列表
const fruits = ["Apple", "Banana", "Cherry"];
console.log(formatList(fruits));
// 英语: "Apple, Banana, and Cherry"
// 中文: "Apple、Banana和Cherry"

// 分离列表
const options = ["Save", "Cancel", "Delete"];
console.log(formatList(options, { style: "or" }));
// 英语: "Save, Cancel, or Delete"
// 中文: "Save、Cancel或Delete"
```

### 2. 在组件中使用
```javascript
class UserListComponent extends Component {
    setup() {
        this.users = [
            { name: "Alice" },
            { name: "Bob" }, 
            { name: "Charlie" }
        ];
    }
    
    get userNamesText() {
        const names = this.users.map(user => user.name);
        return formatList(names);
    }
    
    get availableActionsText() {
        const actions = ["Edit", "Delete", "Archive"];
        return formatList(actions, { style: "or" });
    }
    
    render() {
        return html`
            <div>
                <p>Users: ${this.userNamesText}</p>
                <p>Available actions: ${this.availableActionsText}</p>
            </div>
        `;
    }
}
```

### 3. 多语言支持
```javascript
class MultiLanguageList extends Component {
    formatUserList(users, locale) {
        const names = users.map(user => user.name);
        return formatList(names, { localeCode: locale });
    }
    
    showInDifferentLanguages() {
        const users = [
            { name: "张三" },
            { name: "李四" },
            { name: "王五" }
        ];
        
        console.log("English:", this.formatUserList(users, "en-US"));
        // "张三, 李四, and 王五"
        
        console.log("Chinese:", this.formatUserList(users, "zh-CN"));
        // "张三、李四和王五"
        
        console.log("French:", this.formatUserList(users, "fr-FR"));
        // "张三, 李四 et 王五"
    }
}
```

### 4. 单位格式化
```javascript
class MeasurementDisplay extends Component {
    formatMeasurement(value, unit) {
        return `${value} ${unit}`;
    }
    
    formatDimensions(length, width, height) {
        const measurements = [
            this.formatMeasurement(length, "feet"),
            this.formatMeasurement(width, "inches"),
            this.formatMeasurement(height, "inches")
        ];
        
        return formatList(measurements, { style: "unit" });
        // "3 feet, 7 inches, 2 inches"
    }
    
    formatCompactDimensions(length, width, height) {
        const measurements = [
            `${length}′`,
            `${width}″`,
            `${height}″`
        ];
        
        return formatList(measurements, { style: "unit-narrow" });
        // "3′ 7″ 2″"
    }
}
```

### 5. 动态样式选择
```javascript
class DynamicListFormatter {
    formatItems(items, context) {
        let style = "standard";
        
        // 根据上下文选择样式
        if (context.isChoice) {
            style = "or";
        } else if (context.isCompact) {
            style = "standard-short";
        } else if (context.isUnit) {
            style = context.isNarrow ? "unit-narrow" : "unit";
        }
        
        return formatList(items, { style });
    }
    
    // 使用示例
    formatChoices(choices) {
        return this.formatItems(choices, { isChoice: true });
    }
    
    formatCompactList(items) {
        return this.formatItems(items, { isCompact: true });
    }
    
    formatUnits(units, narrow = false) {
        return this.formatItems(units, { 
            isUnit: true, 
            isNarrow: narrow 
        });
    }
}
```

### 6. 错误处理和边界情况
```javascript
class SafeListFormatter {
    safeFormatList(items, options = {}) {
        // 输入验证
        if (!Array.isArray(items)) {
            console.warn("formatList expects an array");
            return String(items || "");
        }
        
        if (items.length === 0) {
            return "";
        }
        
        if (items.length === 1) {
            return String(items[0]);
        }
        
        // 过滤无效项
        const validItems = items
            .filter(item => item != null)
            .map(item => String(item));
        
        if (validItems.length === 0) {
            return "";
        }
        
        try {
            return formatList(validItems, options);
        } catch (error) {
            console.error("List formatting failed:", error);
            // 回退到简单连接
            return validItems.join(", ");
        }
    }
}
```

## 浏览器兼容性

### 1. Intl.ListFormat 支持情况
- **Chrome**: 72+
- **Firefox**: 78+
- **Safari**: 14.1+
- **Edge**: 79+

### 2. 兼容性处理
```javascript
function formatListWithFallback(list, options = {}) {
    if (typeof Intl !== 'undefined' && Intl.ListFormat) {
        return formatList(list, options);
    } else {
        // 回退实现
        return fallbackFormatList(list, options);
    }
}

function fallbackFormatList(list, { style = "standard" } = {}) {
    if (list.length === 0) return "";
    if (list.length === 1) return list[0];
    if (list.length === 2) {
        const connector = style.startsWith("or") ? " or " : " and ";
        return list.join(connector);
    }
    
    const lastItem = list[list.length - 1];
    const otherItems = list.slice(0, -1);
    const connector = style.startsWith("or") ? ", or " : ", and ";
    
    return otherItems.join(", ") + connector + lastItem;
}
```

## 性能优化

### 1. 格式化器缓存
```javascript
const formatterCache = new Map();

function getCachedFormatter(locale, style) {
    const key = `${locale}-${style}`;
    if (!formatterCache.has(key)) {
        formatterCache.set(
            key, 
            new Intl.ListFormat(locale, LIST_STYLES[style])
        );
    }
    return formatterCache.get(key);
}

function optimizedFormatList(list, options = {}) {
    const locale = options.localeCode || user.lang || "en-US";
    const style = options.style || "standard";
    const formatter = getCachedFormatter(locale, style);
    return formatter.format(list);
}
```

### 2. 批量格式化
```javascript
function batchFormatLists(listsData) {
    const results = [];
    const formatterMap = new Map();
    
    for (const { list, locale, style } of listsData) {
        const key = `${locale}-${style}`;
        if (!formatterMap.has(key)) {
            formatterMap.set(
                key,
                new Intl.ListFormat(locale, LIST_STYLES[style])
            );
        }
        
        const formatter = formatterMap.get(key);
        results.push(formatter.format(list));
    }
    
    return results;
}
```

## 最佳实践

### 1. 样式选择
```javascript
// ✅ 推荐：根据内容类型选择合适样式
const userNames = formatList(names, { style: "standard" });
const choices = formatList(options, { style: "or" });
const measurements = formatList(units, { style: "unit" });

// ❌ 避免：所有场景都使用默认样式
const everything = formatList(items); // 可能不合适
```

### 2. 语言代码处理
```javascript
// ✅ 推荐：使用标准语言代码
formatList(items, { localeCode: "zh-CN" });

// ❌ 避免：使用非标准代码
formatList(items, { localeCode: "chinese" });
```

### 3. 输入验证
```javascript
// ✅ 推荐：验证输入
function safeFormatList(items, options) {
    if (!Array.isArray(items) || items.length === 0) {
        return "";
    }
    return formatList(items, options);
}

// ❌ 避免：直接使用未验证的输入
formatList(unknownData);
```

## 总结

列表格式化工具模块是 Odoo Web 客户端国际化系统的重要组成部分，它：
- **标准兼容**: 遵循 Unicode TR35-49 国际化列表模式规范
- **多样化支持**: 提供7种不同的列表格式化样式
- **本地化友好**: 自动根据用户语言环境选择合适的连接符
- **性能优化**: 使用浏览器原生 API，性能优异
- **易于使用**: 简洁的 API 设计，支持多种使用场景

这个模块为 Odoo 的多语言用户界面提供了专业的列表格式化能力，确保了不同语言环境下列表显示的准确性和一致性。

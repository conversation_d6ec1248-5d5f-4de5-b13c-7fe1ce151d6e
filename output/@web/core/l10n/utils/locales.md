# Odoo 语言环境工具 (Locales Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/l10n/utils/locales.js`  
**原始路径**: `/web/static/src/core/l10n/utils/locales.js`  
**模块类型**: 核心基础模块 - 语言环境转换工具  
**代码行数**: 96 行  
**依赖关系**: 无外部依赖

## 模块功能

语言环境工具模块是 Odoo Web 客户端处理多语言环境的核心工具，负责：
- JavaScript 和 Python 语言代码格式之间的转换
- BCP 47 标准和 XPG 语法之间的映射
- 特殊语言脚本（如塞尔维亚语）的处理
- 跨平台语言代码的标准化

## 核心概念

### 1. 语言代码标准

#### BCP 47 标准 (JavaScript)
```
language[-extlang][-script][-region][-variant][-extension][-privateuse]
```
**示例**:
- `en-US`: 美式英语
- `fr-BE`: 比利时法语
- `sr-Latn`: 拉丁文塞尔维亚语
- `sr-Cyrl`: 西里尔文塞尔维亚语

**参考**: [RFC 5646](https://www.ietf.org/rfc/rfc5646.txt)

#### XPG 语法 (Python)
```
language[_territory][.codeset][@modifier]
```
**示例**:
- `en_US`: 美式英语
- `fr_BE`: 比利时法语
- `sr@latin`: 拉丁文塞尔维亚语
- `sr@Cyrl`: 西里尔文塞尔维亚语

**参考**: [GNU Locale Names](https://www.gnu.org/software/libc/manual/html_node/Locale-Names.html)

### 2. 转换规则

#### 基本转换规则
- **分隔符**: JavaScript 使用 `-`，Python 使用 `_`
- **大小写**: 保持原有的大小写规则
- **顺序**: 语言-脚本-地区 vs 语言_地区@修饰符

#### 特殊处理：塞尔维亚语
塞尔维亚语可以使用两种文字系统：
- **拉丁文**: `sr-Latn` ↔ `sr@latin`
- **西里尔文**: `sr-Cyrl` ↔ `sr@Cyrl`

## 核心函数分析

### 1. jsToPyLocale() 函数

```javascript
function jsToPyLocale(locale) {
    if (!locale) {
        return "";
    }
    try {
        var { language, script, region } = new Intl.Locale(locale);
    } catch {
        return locale;
    }
    let xpgLocale = language;
    if (region) {
        xpgLocale += `_${region}`;
    }
    switch (script) {
        case "Cyrl":
            xpgLocale += "@Cyrl";
            break;
        case "Latn":
            xpgLocale += "@latin";
            break;
    }
    return xpgLocale;
}
```

**功能分析**:
- **输入验证**: 处理空值和无效输入
- **标准解析**: 使用 `Intl.Locale` API 解析 BCP 47 格式
- **错误处理**: 解析失败时返回原始值
- **格式构建**: 按 XPG 语法重新组装语言代码

**转换示例**:
```javascript
jsToPyLocale("en-US")     // → "en_US"
jsToPyLocale("fr-BE")     // → "fr_BE"
jsToPyLocale("sr-Latn")   // → "sr@latin"
jsToPyLocale("sr-Cyrl")   // → "sr@Cyrl"
jsToPyLocale("zh-CN")     // → "zh_CN"
jsToPyLocale("")          // → ""
jsToPyLocale("invalid")   // → "invalid"
```

### 2. pyToJsLocale() 函数

```javascript
function pyToJsLocale(locale) {
    if (!locale) {
        return "";
    }
    const regex = /^([a-z]+)(_[A-Z\d]+)?(@.+)?$/;
    const match = locale.match(regex);
    if (!match) {
        return locale;
    }
    const [, language, territory, modifier] = match;
    const subtags = [language];
    switch (modifier) {
        case "@Cyrl":
            subtags.push("Cyrl");
            break;
        case "@latin":
            subtags.push("Latn");
            break;
    }
    if (territory) {
        subtags.push(territory.slice(1));
    }
    return subtags.join("-");
}
```

**功能分析**:
- **正则解析**: 使用正则表达式解析 XPG 格式
- **组件提取**: 分离语言、地区和修饰符
- **脚本映射**: 特殊处理塞尔维亚语脚本修饰符
- **标签重组**: 按 BCP 47 标准重新组装

**转换示例**:
```javascript
pyToJsLocale("en_US")     // → "en-US"
pyToJsLocale("fr_BE")     // → "fr-BE"
pyToJsLocale("sr@latin")  // → "sr-Latn"
pyToJsLocale("sr@Cyrl")   // → "sr-Cyrl"
pyToJsLocale("zh_CN")     // → "zh-CN"
pyToJsLocale("")          // → ""
pyToJsLocale("invalid")   // → "invalid"
```

## 技术实现细节

### 1. Intl.Locale API 使用
```javascript
try {
    var { language, script, region } = new Intl.Locale(locale);
} catch {
    return locale;
}
```

**优势**:
- **标准化**: 使用浏览器原生 API
- **准确性**: 符合国际标准的解析
- **容错性**: 解析失败时优雅降级

**浏览器兼容性**:
- Chrome 74+
- Firefox 75+
- Safari 14+

### 2. 正则表达式解析
```javascript
const regex = /^([a-z]+)(_[A-Z\d]+)?(@.+)?$/;
```

**模式分析**:
- `([a-z]+)`: 语言代码（必需）
- `(_[A-Z\d]+)?`: 地区代码（可选）
- `(@.+)?`: 修饰符（可选）

**匹配示例**:
- `en_US` → `["en_US", "en", "_US", undefined]`
- `sr@latin` → `["sr@latin", "sr", undefined, "@latin"]`
- `zh_CN@utf8` → `["zh_CN@utf8", "zh", "_CN", "@utf8"]`

### 3. 脚本标签映射
```javascript
switch (script) {
    case "Cyrl":
        xpgLocale += "@Cyrl";
        break;
    case "Latn":
        xpgLocale += "@latin";
        break;
}
```

**映射规则**:
- `Cyrl` (西里尔文) → `@Cyrl`
- `Latn` (拉丁文) → `@latin`
- 其他脚本暂不处理

## 实际使用示例

### 1. 在本地化服务中使用
```javascript
import { jsToPyLocale } from "@web/core/l10n/utils/locales";

const localizationService = {
    start: async () => {
        // 获取用户语言设置
        const userLang = user.lang || navigator.language;
        
        // 转换为 Python 格式用于服务器通信
        const pythonLang = jsToPyLocale(userLang);
        
        // 构建翻译请求 URL
        const url = `/web/webclient/translations/${hash}?lang=${pythonLang}`;
        
        // 发起请求...
    }
};
```

### 2. 在用户设置中使用
```javascript
import { pyToJsLocale, jsToPyLocale } from "@web/core/l10n/utils/locales";

class UserPreferences extends Component {
    setup() {
        this.availableLanguages = [
            { code: "en_US", name: "English (US)" },
            { code: "fr_FR", name: "Français" },
            { code: "sr@latin", name: "Srpski (latinica)" },
            { code: "sr@Cyrl", name: "Српски (ћирилица)" }
        ];
    }
    
    get displayLanguages() {
        return this.availableLanguages.map(lang => ({
            ...lang,
            jsCode: pyToJsLocale(lang.code),
            displayName: this.getLocalizedName(lang.code)
        }));
    }
    
    async changeLanguage(pythonCode) {
        const jsCode = pyToJsLocale(pythonCode);
        
        // 更新浏览器语言设置
        document.documentElement.lang = jsCode;
        
        // 保存到服务器
        await this.orm.call("res.users", "write", [
            [this.user.userId], 
            { lang: pythonCode }
        ]);
        
        // 重新加载页面应用新语言
        window.location.reload();
    }
}
```

### 3. 在国际化组件中使用
```javascript
import { jsToPyLocale, pyToJsLocale } from "@web/core/l10n/utils/locales";

class InternationalizationHelper {
    constructor() {
        this.currentLocale = this.detectLocale();
    }
    
    detectLocale() {
        // 优先使用用户设置
        if (user.lang) {
            return pyToJsLocale(user.lang);
        }
        
        // 回退到浏览器语言
        return navigator.language || "en-US";
    }
    
    formatForServer(jsLocale) {
        return jsToPyLocale(jsLocale);
    }
    
    formatForClient(pythonLocale) {
        return pyToJsLocale(pythonLocale);
    }
    
    isRTLLanguage(locale) {
        const pythonLocale = jsToPyLocale(locale);
        const rtlLanguages = ["ar", "he", "fa", "ur"];
        return rtlLanguages.some(lang => pythonLocale.startsWith(lang));
    }
    
    getLanguageDirection(locale) {
        return this.isRTLLanguage(locale) ? "rtl" : "ltr";
    }
}
```

### 4. 在数据处理中使用
```javascript
class LocaleDataProcessor {
    processUserData(userData) {
        return userData.map(user => ({
            ...user,
            // 标准化语言代码格式
            locale_js: pyToJsLocale(user.lang),
            locale_py: user.lang,
            // 添加显示友好的语言名称
            language_display: this.getLanguageDisplayName(user.lang)
        }));
    }
    
    prepareServerPayload(clientData) {
        return {
            ...clientData,
            // 转换为服务器期望的格式
            lang: jsToPyLocale(clientData.locale),
            // 移除客户端特有的字段
            locale_js: undefined
        };
    }
    
    getLanguageDisplayName(pythonLocale) {
        const jsLocale = pyToJsLocale(pythonLocale);
        try {
            return new Intl.DisplayNames([jsLocale], { type: 'language' })
                .of(jsLocale.split('-')[0]);
        } catch {
            return pythonLocale;
        }
    }
}
```

## 错误处理和边界情况

### 1. 输入验证
```javascript
// 处理空值和无效输入
if (!locale) {
    return "";
}
```

### 2. 解析错误处理
```javascript
// jsToPyLocale 中的错误处理
try {
    var { language, script, region } = new Intl.Locale(locale);
} catch {
    return locale; // 返回原始值
}

// pyToJsLocale 中的错误处理
const match = locale.match(regex);
if (!match) {
    return locale; // 返回原始值
}
```

### 3. 边界情况测试
```javascript
// 测试各种边界情况
console.log(jsToPyLocale(""));           // ""
console.log(jsToPyLocale(null));         // ""
console.log(jsToPyLocale("invalid"));    // "invalid"
console.log(jsToPyLocale("en"));         // "en"
console.log(jsToPyLocale("en-US-x-foo")); // "en_US"

console.log(pyToJsLocale(""));           // ""
console.log(pyToJsLocale(null));         // ""
console.log(pyToJsLocale("invalid"));    // "invalid"
console.log(pyToJsLocale("en"));         // "en"
console.log(pyToJsLocale("en_US.utf8")); // "en-US"
```

## 性能优化建议

### 1. 缓存转换结果
```javascript
const localeCache = new Map();

function cachedJsToPyLocale(locale) {
    if (!localeCache.has(locale)) {
        localeCache.set(locale, jsToPyLocale(locale));
    }
    return localeCache.get(locale);
}
```

### 2. 批量转换
```javascript
function batchConvertLocales(locales, converter) {
    return locales.map(locale => ({
        original: locale,
        converted: converter(locale)
    }));
}
```

## 最佳实践

### 1. 一致性使用
```javascript
// ✅ 推荐：始终使用转换函数
const serverLang = jsToPyLocale(clientLang);

// ❌ 避免：手动字符串替换
const serverLang = clientLang.replace("-", "_");
```

### 2. 错误处理
```javascript
// ✅ 推荐：处理转换可能的异常
try {
    const converted = jsToPyLocale(userInput);
    // 使用转换结果
} catch (error) {
    console.warn("Locale conversion failed:", error);
    // 使用默认值
}
```

### 3. 类型检查
```javascript
// ✅ 推荐：验证输入类型
function safeJsToPyLocale(locale) {
    if (typeof locale !== 'string') {
        return "";
    }
    return jsToPyLocale(locale);
}
```

## 总结

语言环境工具模块是 Odoo Web 客户端国际化系统的基础组件，它：
- **标准兼容**: 支持 BCP 47 和 XPG 两种主要语言代码标准
- **双向转换**: 提供 JavaScript 和 Python 格式之间的无缝转换
- **特殊处理**: 正确处理塞尔维亚语等特殊语言的脚本变体
- **容错设计**: 具备完善的错误处理和边界情况处理
- **性能友好**: 使用原生 API 和高效的正则表达式

这个模块为 Odoo 的多语言支持提供了坚实的基础，确保了前后端语言代码格式的一致性和准确性。

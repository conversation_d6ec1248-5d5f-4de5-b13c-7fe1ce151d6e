# Odoo 本地化服务 (Localization Service) 学习资料

## 文件概述

**文件路径**: `output/@web/core/l10n/localization_service.js`  
**原始路径**: `/web/static/src/core/l10n/localization_service.js`  
**模块类型**: 核心基础模块 - 本地化服务  
**代码行数**: 98 行  
**依赖关系**: 
- `@web/session` - 会话信息
- `@web/core/l10n/utils` - 本地化工具
- `@web/core/user` - 用户信息
- `@web/core/browser/browser` - 浏览器抽象
- `@web/core/registry` - 服务注册表
- `@web/core/l10n/dates` - 日期本地化
- `@web/core/l10n/localization` - 本地化配置
- `@web/core/l10n/translation` - 翻译核心

## 模块功能

本地化服务是 Odoo Web 客户端国际化系统的核心服务，负责：
- 从服务器获取翻译数据和本地化配置
- 初始化全局本地化设置
- 配置 Luxon 日期库的本地化参数
- 设置数字系统和格式化规则
- 管理多语言环境配置

## 核心常量分析

### 数字系统映射表
```javascript
const NUMBERING_SYSTEMS = [
    [/^ar-(sa|sy|001)$/i, "arab"],    // 阿拉伯数字系统
    [/^bn/i, "beng"],                 // 孟加拉数字系统
    [/^bo/i, "tibt"],                 // 藏文数字系统
    [/^pa-in/i, "guru"],              // 古吉拉特数字系统
    [/^ta/i, "tamldec"],              // 泰米尔数字系统
    [/.*/i, "latn"],                  // 拉丁数字系统（默认）
];
```

**设计分析**:
- **正则匹配**: 根据语言代码匹配对应的数字系统
- **国际化支持**: 支持多种文字的数字显示
- **回退机制**: 默认使用拉丁数字系统
- **区域特定**: 考虑了地区差异（如 ar-sa, ar-sy）

## 服务架构分析

### 1. 服务定义结构
```javascript
const localizationService = {
    start: async () => {
        // 服务启动逻辑
    }
};
```

**特点**:
- **异步启动**: 使用 async/await 处理异步初始化
- **单一职责**: 专注于本地化配置的加载和设置
- **无依赖注入**: 直接导入所需模块

### 2. 翻译数据获取流程

```javascript
// 1. 构建请求 URL
const cacheHashes = session.cache_hashes || {};
const translationsHash = cacheHashes.translations || new Date().getTime().toString();
const lang = jsToPyLocale(user.lang || document.documentElement.getAttribute("lang"));
const translationURL = session.translationURL || "/web/webclient/translations";
let url = `${translationURL}/${translationsHash}`;
if (lang) {
    url += `?lang=${lang}`;
}

// 2. 发起网络请求
const response = await browser.fetch(url);
if (!response.ok) {
    throw new Error("Error while fetching translations");
}

// 3. 解析响应数据
const {
    lang_parameters: userLocalization,
    modules: modules,
    multi_lang: multiLang,
} = await response.json();
```

**流程分析**:
- **缓存控制**: 使用哈希值进行缓存控制
- **语言检测**: 优先使用用户设置，回退到 HTML lang 属性
- **URL 构建**: 动态构建翻译数据请求 URL
- **错误处理**: 检查响应状态并抛出明确错误

### 3. 翻译数据处理

```javascript
// 扁平化翻译数据
const terms = {};
for (const addon of Object.keys(modules)) {
    for (const message of modules[addon].messages) {
        terms[message.id] = message.string;
    }
}

// 更新全局翻译状态
Object.assign(translatedTerms, terms);
translatedTerms[translationLoaded] = true;
translationIsReady.resolve(true);
```

**处理逻辑**:
- **数据扁平化**: 将模块化的翻译数据合并为单一对象
- **状态更新**: 标记翻译数据已加载
- **Promise 解决**: 通知等待翻译的代码可以继续执行

## Luxon 库配置

### 1. 语言环境设置
```javascript
const locale = user.lang || browser.navigator.language;
Settings.defaultLocale = locale;
```

### 2. 数字系统配置
```javascript
for (const [re, numberingSystem] of NUMBERING_SYSTEMS) {
    if (re.test(locale)) {
        Settings.defaultNumberingSystem = numberingSystem;
        break;
    }
}
```

**配置特点**:
- **全局设置**: 影响所有 Luxon 日期操作
- **智能匹配**: 根据语言代码自动选择数字系统
- **性能优化**: 使用 break 避免不必要的匹配

## 本地化配置构建

### 1. 日期时间格式转换
```javascript
const dateFormat = strftimeToLuxonFormat(userLocalization.date_format);
const timeFormat = strftimeToLuxonFormat(userLocalization.time_format);
const shortTimeFormat = strftimeToLuxonFormat(userLocalization.short_time_format);
const dateTimeFormat = `${dateFormat} ${timeFormat}`;
```

### 2. 数字格式配置
```javascript
const grouping = JSON.parse(userLocalization.grouping);

Object.assign(localization, {
    dateFormat,
    timeFormat,
    shortTimeFormat,
    dateTimeFormat,
    decimalPoint: userLocalization.decimal_point,
    direction: userLocalization.direction,
    grouping,
    multiLang,
    thousandsSep: userLocalization.thousands_sep,
    weekStart: userLocalization.week_start,
    code: jsToPyLocale(locale),
});
```

**配置项说明**:
- **dateFormat**: 日期显示格式
- **timeFormat**: 时间显示格式
- **shortTimeFormat**: 短时间格式
- **dateTimeFormat**: 日期时间组合格式
- **decimalPoint**: 小数点符号
- **direction**: 文本方向 (ltr/rtl)
- **grouping**: 数字分组规则
- **thousandsSep**: 千位分隔符
- **weekStart**: 一周开始日期
- **code**: 标准化的语言代码

## 使用示例

### 1. 服务注册和使用
```javascript
// 服务已自动注册到注册表
registry.category("services").add("localization", localizationService);

// 在组件中使用
class MyComponent extends Component {
    setup() {
        this.localization = useService("localization");
    }
    
    formatDate(date) {
        return date.toFormat(this.localization.dateFormat);
    }
}
```

### 2. 等待本地化初始化
```javascript
import { translationIsReady } from "@web/core/l10n/translation";

async function initializeApp() {
    // 等待本地化服务完成初始化
    await translationIsReady;
    
    // 现在可以安全使用本地化功能
    const localization = env.services.localization;
    console.log("Date format:", localization.dateFormat);
}
```

### 3. 动态语言切换
```javascript
class LanguageSwitcher extends Component {
    async switchLanguage(newLang) {
        // 更新用户语言设置
        await this.env.services.user.updateContext({ lang: newLang });
        
        // 重新加载页面以应用新的本地化设置
        window.location.reload();
    }
}
```

## 设计模式分析

### 1. 服务模式
- 作为全局服务提供本地化功能
- 统一管理本地化配置
- 支持依赖注入

### 2. 初始化模式
- 异步初始化避免阻塞应用启动
- 集中处理所有本地化配置
- 提供初始化完成通知

### 3. 配置模式
- 从服务器获取配置数据
- 支持缓存和版本控制
- 提供默认值和回退机制

## 性能优化策略

### 1. 缓存机制
```javascript
const translationsHash = cacheHashes.translations || new Date().getTime().toString();
```
- 使用哈希值控制缓存
- 避免重复下载翻译数据
- 支持增量更新

### 2. 延迟加载
- 异步加载翻译数据
- 不阻塞应用启动
- 提供加载状态通知

### 3. 数据扁平化
- 将嵌套的模块数据扁平化
- 提高翻译查找性能
- 减少内存占用

## 错误处理机制

### 1. 网络错误处理
```javascript
if (!response.ok) {
    throw new Error("Error while fetching translations");
}
```

### 2. 数据格式错误处理
```javascript
const grouping = JSON.parse(userLocalization.grouping);
```
- JSON 解析可能抛出异常
- 需要在调用方处理解析错误

### 3. 回退机制
```javascript
const lang = jsToPyLocale(user.lang || document.documentElement.getAttribute("lang"));
const translationURL = session.translationURL || "/web/webclient/translations";
```

## 与其他模块的关系

### 1. 翻译模块
- 更新翻译状态和数据
- 解决翻译 Promise
- 提供翻译查找数据

### 2. 日期模块
- 使用日期格式转换函数
- 配置 Luxon 库参数
- 提供日期本地化支持

### 3. 用户模块
- 获取用户语言偏好
- 支持用户级别的本地化
- 响应语言切换

## 最佳实践

### 1. 服务使用
```javascript
// 推荐：通过服务系统获取
const localization = useService("localization");

// 避免：直接导入模块
import { localization } from "@web/core/l10n/localization";
```

### 2. 异步处理
```javascript
// 推荐：等待初始化完成
await translationIsReady;
const format = localization.dateFormat;

// 避免：同步访问可能未初始化的数据
const format = localization.dateFormat; // 可能为 undefined
```

### 3. 错误处理
```javascript
// 推荐：处理初始化错误
try {
    await env.services.localization;
} catch (error) {
    console.error("Localization failed:", error);
    // 提供回退方案
}
```

## 总结

本地化服务是 Odoo Web 国际化系统的核心组件，它：
- **统一管理**: 集中处理所有本地化配置
- **异步加载**: 不阻塞应用启动过程
- **全面支持**: 涵盖翻译、日期、数字、文本方向等
- **性能优化**: 缓存机制和数据扁平化
- **错误处理**: 完善的异常处理和回退机制

这个服务为 Odoo 的多语言和多地区支持提供了坚实的基础，是现代 Web 应用国际化的典型实现。

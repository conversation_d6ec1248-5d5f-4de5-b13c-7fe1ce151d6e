# Odoo L10n 模块架构总览与使用指南

## 📁 模块概览

**目录路径**: `output/@web/core/l10n/`  
**模块类型**: 核心基础模块 - 国际化与本地化系统  
**功能范围**: 翻译、本地化、日期时间、语言环境处理

## 🏗️ 架构图

```
@web/core/l10n/
├── 📄 translation.js           # 翻译核心模块
├── 📄 localization_service.js  # 本地化服务
├── 📄 localization.js          # 本地化配置对象
├── 📄 dates.js                 # 日期本地化模块
├── 📄 utils.js                 # 工具函数聚合器
└── 📁 utils/
    ├── 📄 locales.js           # 语言环境转换工具
    └── 📄 format_list.js       # 列表格式化工具
```

## 🎯 模块功能矩阵

| 模块 | 主要功能 | 核心API | 依赖关系 |
|------|----------|---------|----------|
| **translation.js** | 翻译管理 | `_t()`, `loadLanguages()` | concurrency, strings |
| **localization_service.js** | 本地化服务 | `localizationService` | session, user, browser |
| **localization.js** | 配置存储 | `localization` 对象 | 无 |
| **dates.js** | 日期处理 | `formatDate()`, `parseDate()` | localization, translation |
| **utils.js** | 工具聚合 | 聚合所有工具函数 | utils/* |
| **utils/locales.js** | 语言转换 | `jsToPyLocale()`, `pyToJsLocale()` | 无 |
| **utils/format_list.js** | 列表格式化 | `formatList()` | user |

## 🔄 数据流图

```mermaid
graph TD
    A[用户请求] --> B[localization_service]
    B --> C[获取翻译数据]
    B --> D[设置本地化配置]
    C --> E[translation.js]
    D --> F[localization.js]
    E --> G[_t() 翻译函数]
    F --> H[本地化配置]
    H --> I[dates.js]
    H --> J[utils/format_list.js]
    I --> K[日期格式化]
    J --> L[列表格式化]
    G --> M[用户界面]
    K --> M
    L --> M
```

## 🚀 快速开始

### 1. 基本翻译使用
```javascript
import { _t } from "@web/core/l10n/translation";

// 简单翻译
const message = _t("Hello World");

// 带参数翻译
const greeting = _t("Welcome, %s!", userName);
```

### 2. 日期格式化
```javascript
import { formatDate, parseDate } from "@web/core/l10n/dates";

// 格式化日期
const formatted = formatDate(DateTime.local());

// 解析用户输入
const parsed = parseDate("2024-03-15");
```

### 3. 列表格式化
```javascript
import { formatList } from "@web/core/l10n/utils/format_list";

// 标准列表
const items = ["Apple", "Banana", "Cherry"];
const formatted = formatList(items); // "Apple, Banana, and Cherry"

// 选择列表
const choices = formatList(items, { style: "or" }); // "Apple, Banana, or Cherry"
```

### 4. 语言代码转换
```javascript
import { jsToPyLocale, pyToJsLocale } from "@web/core/l10n/utils/locales";

// JavaScript 到 Python 格式
const pythonLocale = jsToPyLocale("en-US"); // "en_US"

// Python 到 JavaScript 格式
const jsLocale = pyToJsLocale("fr_FR"); // "fr-FR"
```

## 📚 核心概念详解

### 1. 翻译系统
- **延迟翻译**: 支持翻译数据异步加载
- **参数化翻译**: 使用 sprintf 格式化参数
- **回退机制**: 未找到翻译时使用原始字符串

### 2. 本地化配置
- **代理模式**: 安全的配置访问控制
- **异步初始化**: 不阻塞应用启动
- **全局访问**: 整个应用共享配置

### 3. 日期处理
- **格式转换**: Python strftime ↔ Luxon 格式
- **智能解析**: 支持多种输入格式
- **本地化周**: 根据用户设置处理周开始日期

### 4. 语言环境
- **标准兼容**: 支持 BCP 47 和 XPG 语法
- **特殊处理**: 塞尔维亚语脚本变体
- **双向转换**: JavaScript ↔ Python 格式

## 🔧 高级用法

### 1. 自定义翻译加载
```javascript
import { translationIsReady, translatedTerms } from "@web/core/l10n/translation";

async function loadCustomTranslations() {
    await translationIsReady;
    
    // 添加自定义翻译
    Object.assign(translatedTerms, {
        "Custom Message": "自定义消息"
    });
}
```

### 2. 动态本地化切换
```javascript
class LanguageSwitcher {
    async switchLanguage(newLang) {
        // 更新用户语言设置
        await this.env.services.user.updateContext({ lang: newLang });
        
        // 重新初始化本地化
        window.location.reload();
    }
}
```

### 3. 复杂日期处理
```javascript
import { 
    formatDateTime, 
    getStartOfLocalWeek, 
    getEndOfLocalWeek 
} from "@web/core/l10n/dates";

class WeeklyReport {
    getCurrentWeekRange() {
        const today = DateTime.local();
        return {
            start: getStartOfLocalWeek(today),
            end: getEndOfLocalWeek(today)
        };
    }
    
    formatWeekRange(start, end) {
        const startStr = formatDateTime(start, { showSeconds: false });
        const endStr = formatDateTime(end, { showSeconds: false });
        return `${startStr} - ${endStr}`;
    }
}
```

## 🎨 最佳实践

### 1. 翻译字符串规范
```javascript
// ✅ 推荐：使用描述性字符串
_t("Save changes to document");
_t("Delete selected items");

// ❌ 避免：使用简短或模糊字符串
_t("OK");
_t("Error");
```

### 2. 日期格式化
```javascript
// ✅ 推荐：使用本地化格式
const formatted = formatDate(date);

// ❌ 避免：硬编码格式
const formatted = date.toFormat("MM/dd/yyyy");
```

### 3. 列表格式化
```javascript
// ✅ 推荐：根据内容选择样式
const userList = formatList(names, { style: "standard" });
const choiceList = formatList(options, { style: "or" });

// ❌ 避免：手动拼接
const manual = names.join(", ") + " and " + names[names.length - 1];
```

### 4. 语言代码处理
```javascript
// ✅ 推荐：使用转换函数
const serverLang = jsToPyLocale(clientLang);

// ❌ 避免：手动替换
const serverLang = clientLang.replace("-", "_");
```

## 🔍 调试技巧

### 1. 翻译调试
```javascript
// 检查翻译状态
console.log("Translation loaded:", translatedTerms[translationLoaded]);

// 查看可用翻译
console.log("Available translations:", Object.keys(translatedTerms));
```

### 2. 本地化调试
```javascript
// 检查本地化配置
console.log("Localization config:", localization);

// 验证日期格式
console.log("Date format:", localization.dateFormat);
```

### 3. 日期调试
```javascript
// 测试日期解析
try {
    const parsed = parseDate(userInput);
    console.log("Parsed date:", parsed);
} catch (error) {
    console.error("Parse error:", error.message);
}
```

## ⚡ 性能优化

### 1. 翻译缓存
- 翻译数据自动缓存
- 语言列表缓存
- 避免重复请求

### 2. 日期缓存
- 序列化结果缓存 (WeakMap)
- 格式转换缓存 (memoize)
- 减少重复计算

### 3. 格式化器缓存
- Intl.ListFormat 实例缓存
- 减少对象创建开销
- 提高批量处理性能

## 🌍 国际化支持

### 支持的语言特性
- **文本方向**: LTR/RTL 支持
- **数字系统**: 阿拉伯、孟加拉、藏文等
- **日期格式**: 各国日期时间格式
- **列表格式**: 多语言列表连接符

### 特殊语言处理
- **塞尔维亚语**: 拉丁文/西里尔文脚本
- **阿拉伯语**: RTL 文本方向
- **中文**: 简体/繁体支持

## 🔮 扩展指南

### 1. 添加新的工具函数
```javascript
// 在 utils/ 目录下创建新模块
// 然后在 utils.js 中聚合
Object.assign(__exports, require("@web/core/l10n/utils/new_module"));
```

### 2. 自定义日期格式
```javascript
// 扩展日期格式转换表
const customFormats = {
    // 添加自定义格式映射
};
```

### 3. 新增列表样式
```javascript
// 扩展列表样式配置
const CUSTOM_LIST_STYLES = {
    ...LIST_STYLES,
    "custom-style": {
        type: "conjunction",
        style: "custom"
    }
};
```

## 📖 相关资源

- [Unicode TR35 规范](https://www.unicode.org/reports/tr35/)
- [BCP 47 语言标签](https://www.ietf.org/rfc/rfc5646.txt)
- [Luxon 日期库文档](https://moment.github.io/luxon/)
- [Intl API 文档](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl)

## 🎯 总结

Odoo L10n 模块是一个完整的国际化解决方案，提供了：
- **完整的翻译系统**: 从加载到使用的全流程支持
- **强大的日期处理**: 格式化、解析、本地化一体化
- **灵活的工具集**: 语言转换、列表格式化等实用工具
- **现代化架构**: 使用最新的 Web 标准和最佳实践

这个模块为 Odoo 的全球化应用提供了坚实的基础，确保了在不同语言和地区环境下的一致用户体验。

# Odoo 日期本地化模块 (Dates Localization) 学习资料

## 文件概述

**文件路径**: `output/@web/core/l10n/dates.js`  
**原始路径**: `/web/static/src/core/l10n/dates.js`  
**模块类型**: 核心基础模块 - 日期本地化  
**代码行数**: 623 行  
**依赖关系**: 
- `@web/core/l10n/localization` - 本地化配置
- `@web/core/l10n/translation` - 翻译功能
- `@web/core/utils/functions` - 工具函数
- `@web/core/utils/arrays` - 数组工具
- `luxon` - 日期时间库

## 模块功能

日期本地化模块是 Odoo Web 客户端日期时间处理的核心，负责：
- 日期时间的格式化和解析
- Python strftime 格式到 Luxon 格式的转换
- 智能日期输入处理
- 服务器日期格式的序列化和反序列化
- 本地化周开始日期处理
- 日期范围和有效性验证

## 核心类型定义

### 1. ConversionOptions 类型
```javascript
/**
 * @typedef ConversionOptions
 * @property {string} [format] - 格式化字符串
 * @property {boolean} [condensed] - 是否使用紧凑格式（无前导零）
 */
```

### 2. 日期时间类型
```javascript
/**
 * @typedef {luxon.DateTime} DateTime
 * @typedef {[NullableDateTime, NullableDateTime]} NullableDateRange
 * @typedef {DateTime | false | null | undefined} NullableDateTime
 */
```

## 核心常量分析

### 1. 日期有效范围
```javascript
const MIN_VALID_DATE = DateTime.fromObject({ year: 1000 });
const MAX_VALID_DATE = DateTime.fromObject({ year: 9999 }).endOf("year");
```
**设计原因**: 服务器只支持4位年份，限制有效日期范围

### 2. 服务器格式常量
```javascript
const SERVER_DATE_FORMAT = "yyyy-MM-dd";
const SERVER_TIME_FORMAT = "HH:mm:ss";
const SERVER_DATETIME_FORMAT = `${SERVER_DATE_FORMAT} ${SERVER_TIME_FORMAT}`;
```
**用途**: 与服务器通信时的标准格式

### 3. Python strftime 到 Luxon 转换表
```javascript
const normalizeFormatTable = {
    a: "ccc",    // 星期几缩写
    A: "cccc",   // 星期几全名
    b: "MMM",    // 月份缩写
    B: "MMMM",   // 月份全名
    d: "dd",     // 日期（两位数）
    H: "HH",     // 小时（24小时制）
    I: "hh",     // 小时（12小时制）
    m: "MM",     // 月份（两位数）
    M: "mm",     // 分钟
    p: "a",      // AM/PM
    S: "ss",     // 秒
    y: "yy",     // 年份（两位数）
    Y: "yyyy",   // 年份（四位数）
    // ... 更多格式
};
```

### 4. 智能日期单位
```javascript
const smartDateUnits = {
    d: "days",
    m: "months", 
    w: "weeks",
    y: "years",
};
```

## 核心功能分析

### 1. 日期比较函数
```javascript
function areDatesEqual(d1, d2) {
    if (Array.isArray(d1) || Array.isArray(d2)) {
        // 处理日期范围比较
        d1 = ensureArray(d1);
        d2 = ensureArray(d2);
        return d1.length === d2.length && d1.every((d1Val, i) => areDatesEqual(d1Val, d2[i]));
    }
    if (d1 instanceof DateTime && d2 instanceof DateTime && d1 !== d2) {
        // 使用 Luxon 的比较方法
        return d1.equals(d2);
    } else {
        // 回退到严格相等比较
        return d1 === d2;
    }
}
```

**设计亮点**:
- **递归处理**: 支持日期范围的深度比较
- **类型检测**: 区分 DateTime 对象和其他类型
- **性能优化**: 引用相等时直接返回

### 2. 本地化周处理
```javascript
function getStartOfLocalWeek(date) {
    const { weekStart } = localization;
    const weekday = date.weekday < weekStart ? weekStart - 7 : weekStart;
    return date.set({ weekday }).startOf("day");
}

function getEndOfLocalWeek(date) {
    return getStartOfLocalWeek(date).plus({ days: 6 }).endOf("day");
}
```

**功能特点**:
- **本地化支持**: 根据用户设置确定周开始日期
- **跨平台兼容**: 不依赖浏览器的 Intl API
- **精确计算**: 处理跨周边界情况

### 3. 智能日期解析
```javascript
function parseSmartDateInput(value) {
    const match = value.match(smartDateRegex);
    if (match) {
        let date = DateTime.local();
        const offset = parseInt(match[2], 10);
        const unit = smartDateUnits[(match[3] || "d").toLowerCase()];
        if (match[1] === "+") {
            date = date.plus({ [unit]: offset });
        } else {
            date = date.minus({ [unit]: offset });
        }
        return date;
    }
    return false;
}
```

**支持格式**:
- `+1d` 或 `+1`: 明天
- `-2w`: 两周前
- `+3m`: 三个月后
- `-4y`: 四年前

## 格式转换核心

### 1. strftime 到 Luxon 格式转换
```javascript
const strftimeToLuxonFormat = memoize(function strftimeToLuxonFormat(format) {
    const output = [];
    let inToken = false;
    for (let index = 0; index < format.length; ++index) {
        let character = format[index];
        if (character === "%" && !inToken) {
            inToken = true;
            continue;
        }
        if (/[a-z]/gi.test(character)) {
            if (inToken && normalizeFormatTable[character] !== undefined) {
                character = normalizeFormatTable[character];
            } else {
                character = `'${character}'`; // luxon escape
            }
        }
        output.push(character);
        inToken = false;
    }
    return output.join("");
});
```

**转换逻辑**:
- **状态机**: 使用 `inToken` 标志跟踪转换状态
- **查表转换**: 通过 `normalizeFormatTable` 映射格式
- **字符转义**: 未识别字符用单引号转义
- **缓存优化**: 使用 `memoize` 缓存转换结果

### 2. 紧凑格式处理
```javascript
function getCondensedFormat(format) {
    const originalFormat = format;
    if (!condensedFormats[originalFormat]) {
        format = format.replace(/(^|[^M])M{2}([^M]|$)/, "$1M$2");
        format = format.replace(/(^|[^d])d{2}([^d]|$)/, "$1d$2");
        format = format.replace(/(^|[^H])H{2}([^H]|$)/, "$1H$2");
        condensedFormats[originalFormat] = format;
    }
    return condensedFormats[originalFormat];
}
```

**功能**: 移除前导零，如 `03/05/2024 08:00:00` → `3/5/2024 8:00:00`

## 格式化函数

### 1. 日期格式化
```javascript
function formatDate(value, options = {}) {
    if (!value) {
        return "";
    }
    let format = options.format;
    if (!format) {
        format = localization.dateFormat;
        if (options.condensed) {
            format = getCondensedFormat(format);
        }
    }
    return value.toFormat(format);
}
```

### 2. 日期时间格式化
```javascript
function formatDateTime(value, options = {}) {
    if (!value) {
        return "";
    }
    let format = options.format;
    if (!format) {
        if (options.showSeconds === false) {
            format = `${localization.dateFormat} ${localization.shortTimeFormat}`;
        } else {
            format = localization.dateTimeFormat;
        }
        if (options.condensed) {
            format = getCondensedFormat(format);
        }
    }
    return value.setZone(options.tz || "default").toFormat(format);
}
```

### 3. 持续时间格式化
```javascript
function formatDuration(seconds, showFullDuration) {
    const displayStyle = showFullDuration ? "long" : "narrow";
    const numberOfValuesToDisplay = showFullDuration ? 2 : 1;
    const durationKeys = ["years", "months", "days", "hours", "minutes"];

    if (seconds < 60) {
        seconds = 60;
    }
    seconds -= seconds % 60;

    let duration = luxon.Duration.fromObject({ seconds: seconds }).shiftTo(...durationKeys);
    duration = duration.shiftTo(...durationKeys.filter((key) => duration.get(key)));
    const durationSplit = duration.toHuman({ unitDisplay: displayStyle }).split(",");

    if (!showFullDuration && duration.loc.locale.includes("en") && duration.months > 0) {
        durationSplit[0] = durationSplit[0].replace("m", "M");
    }
    return durationSplit.slice(0, numberOfValuesToDisplay).join(",");
}
```

**特点**:
- **智能舍入**: 小于60秒的时长显示为1分钟
- **多语言支持**: 使用 Luxon 的本地化功能
- **灵活显示**: 支持完整和简短两种显示模式

## 序列化函数

### 1. 日期序列化（带缓存）
```javascript
function serializeDate(value) {
    if (!dateCache.has(value)) {
        dateCache.set(value, value.toFormat(SERVER_DATE_FORMAT, { numberingSystem: "latn" }));
    }
    return dateCache.get(value);
}
```

### 2. 日期时间序列化（带缓存）
```javascript
function serializeDateTime(value) {
    if (!dateTimeCache.has(value)) {
        dateTimeCache.set(
            value,
            value.setZone("utc").toFormat(SERVER_DATETIME_FORMAT, { numberingSystem: "latn" })
        );
    }
    return dateTimeCache.get(value);
}
```

**缓存策略**:
- 使用 `WeakMap` 避免内存泄漏
- 缓存序列化结果提高性能
- 强制使用拉丁数字系统确保服务器兼容性

## 解析函数深度分析

### 1. 日期解析
```javascript
function parseDate(value, options = {}) {
    const parsed = parseDateTime(value, {
        ...options,
        format: options.format || localization.dateFormat,
    });
    return parsed && parsed.startOf("day");
}
```

### 2. 复杂日期时间解析
```javascript
function parseDateTime(value, options = {}) {
    if (!value) {
        return false;
    }

    const fmt = options.format || localization.dateTimeFormat;
    const parseOpts = {
        setZone: true,
        zone: options.tz || "default",
    };
    const switchToLatin = Settings.defaultNumberingSystem !== "latn" && /[0-9]/.test(value);

    // 强制使用拉丁数字系统
    if (switchToLatin) {
        parseOpts.numberingSystem = "latn";
    }

    // 基础解析：使用给定格式
    let result = DateTime.fromFormat(value, fmt, parseOpts);

    // 尝试智能日期解析
    if (!isValidDate(result)) {
        result = parseSmartDateInput(value);
    }

    // 尝试部分日期解析
    if (!isValidDate(result)) {
        const fmtWoZero = stripAlphaDupes(fmt);
        result = DateTime.fromFormat(value, fmtWoZero, parseOpts);
    }

    // 尝试自定义简写日期解析
    if (!isValidDate(result)) {
        // 复杂的格式适配逻辑...
    }

    // 尝试 ISO 和 SQL 格式
    if (!isValidDate(result)) {
        const valueDigits = value.replace(nonDigitRegex, "");
        if (valueDigits.length > 4) {
            result = DateTime.fromISO(value, parseOpts);
            if (!isValidDate(result)) {
                result = DateTime.fromSQL(value, parseOpts);
            }
        }
    }

    // 解析失败时抛出错误
    if (!isValidDate(result)) {
        throw new ConversionError(_t("'%s' is not a correct date or datetime", value));
    }

    return result.setZone(options.tz || "default");
}
```

**解析策略**:
1. **格式匹配**: 首先尝试指定格式
2. **智能解析**: 支持 `+1d`, `-2w` 等快捷输入
3. **容错解析**: 处理缺少前导零的情况
4. **格式适配**: 动态调整格式以匹配输入
5. **标准格式**: 回退到 ISO8601 和 SQL 格式
6. **错误处理**: 解析失败时提供清晰错误信息

### 3. 反序列化函数
```javascript
function deserializeDate(value, options = {}) {
    options = { numberingSystem: "latn", zone: "default", ...options };
    return DateTime.fromSQL(value, options).reconfigure({
        numberingSystem: Settings.defaultNumberingSystem,
    });
}

function deserializeDateTime(value, options = {}) {
    return DateTime.fromSQL(value, { numberingSystem: "latn", zone: "utc" })
        .setZone(options?.tz || "default")
        .reconfigure({
            numberingSystem: Settings.defaultNumberingSystem,
        });
}
```

## 实际使用示例

### 1. 基本日期格式化
```javascript
import { formatDate, formatDateTime, parseDate } from "@web/core/l10n/dates";

class DateComponent extends Component {
    setup() {
        this.currentDate = DateTime.local();
    }

    get formattedDate() {
        // 使用本地化日期格式
        return formatDate(this.currentDate);
    }

    get formattedDateTime() {
        // 使用本地化日期时间格式
        return formatDateTime(this.currentDate);
    }

    get condensedDate() {
        // 紧凑格式（无前导零）
        return formatDate(this.currentDate, { condensed: true });
    }
}
```

### 2. 智能日期输入处理
```javascript
class SmartDateInput extends Component {
    onDateInput(event) {
        const value = event.target.value;
        try {
            const parsed = parseDate(value);
            if (parsed) {
                this.selectedDate = parsed;
                this.error = null;
            }
        } catch (error) {
            this.error = error.message;
        }
    }

    // 支持的输入格式示例：
    // "2024-03-15"     -> 标准日期
    // "+1d"            -> 明天
    // "-1w"            -> 一周前
    // "15/03/2024"     -> 本地化格式
    // "15/3/24"        -> 简写格式
}
```

### 3. 服务器通信
```javascript
class DataService {
    async saveRecord(data) {
        // 序列化日期用于服务器通信
        const payload = {
            ...data,
            date_field: serializeDate(data.date_field),
            datetime_field: serializeDateTime(data.datetime_field)
        };

        return await this.rpc("/api/save", payload);
    }

    processServerData(serverData) {
        // 反序列化服务器返回的日期
        return {
            ...serverData,
            date_field: deserializeDate(serverData.date_field),
            datetime_field: deserializeDateTime(serverData.datetime_field)
        };
    }
}
```

### 4. 本地化周处理
```javascript
class WeekView extends Component {
    setup() {
        this.currentWeek = this.getCurrentWeek();
    }

    getCurrentWeek() {
        const today = DateTime.local();
        return {
            start: getStartOfLocalWeek(today),
            end: getEndOfLocalWeek(today)
        };
    }

    getWeekDays() {
        const { start } = this.currentWeek;
        const days = [];
        for (let i = 0; i < 7; i++) {
            days.push(start.plus({ days: i }));
        }
        return days;
    }

    navigateWeek(direction) {
        const offset = direction === 'next' ? 7 : -7;
        const newStart = this.currentWeek.start.plus({ days: offset });
        this.currentWeek = {
            start: getStartOfLocalWeek(newStart),
            end: getEndOfLocalWeek(newStart)
        };
    }
}
```

### 5. 持续时间格式化
```javascript
class DurationDisplay extends Component {
    formatTaskDuration(seconds) {
        // 简短格式：如 "2h"
        return formatDuration(seconds, false);
    }

    formatDetailedDuration(seconds) {
        // 详细格式：如 "2 hours, 30 minutes"
        return formatDuration(seconds, true);
    }

    // 使用示例
    render() {
        const taskDuration = 7320; // 2小时2分钟
        return html`
            <div>
                <span class="short">${this.formatTaskDuration(taskDuration)}</span>
                <span class="detailed">${this.formatDetailedDuration(taskDuration)}</span>
            </div>
        `;
    }
}
```

## 性能优化策略

### 1. 缓存机制
```javascript
// WeakMap 缓存避免内存泄漏
const dateCache = new WeakMap();
const dateTimeCache = new WeakMap();

// memoize 缓存格式转换结果
const strftimeToLuxonFormat = memoize(function(format) {
    // 转换逻辑...
});
```

### 2. 延迟计算
```javascript
// 只在需要时计算今天的日期
function today() {
    return DateTime.local().startOf("day");
}
```

### 3. 格式预处理
```javascript
// 预处理和缓存紧凑格式
const condensedFormats = {};
function getCondensedFormat(format) {
    if (!condensedFormats[format]) {
        // 计算并缓存结果
    }
    return condensedFormats[format];
}
```

## 错误处理机制

### 1. 自定义错误类
```javascript
class ConversionError extends Error {
    name = "ConversionError";
}
```

### 2. 有效性检查
```javascript
function isValidDate(date) {
    return date && date.isValid && isInRange(date, [MIN_VALID_DATE, MAX_VALID_DATE]);
}
```

### 3. 解析错误处理
```javascript
if (!isValidDate(result)) {
    throw new ConversionError(_t("'%s' is not a correct date or datetime", value));
}
```

## 最佳实践

### 1. 格式化输出
```javascript
// ✅ 推荐：使用本地化格式
const formatted = formatDate(date);

// ❌ 避免：硬编码格式
const formatted = date.toFormat("MM/dd/yyyy");
```

### 2. 解析用户输入
```javascript
// ✅ 推荐：使用容错解析
try {
    const parsed = parseDate(userInput);
} catch (error) {
    // 处理解析错误
}

// ❌ 避免：假设输入格式
const parsed = DateTime.fromFormat(userInput, "yyyy-MM-dd");
```

### 3. 服务器通信
```javascript
// ✅ 推荐：使用序列化函数
const serverDate = serializeDate(clientDate);

// ❌ 避免：手动格式化
const serverDate = clientDate.toFormat("yyyy-MM-dd");
```

## 总结

日期本地化模块是 Odoo Web 客户端处理日期时间的核心组件，它提供了：
- **全面的格式化**: 支持多种日期时间格式
- **智能解析**: 容错性强的日期解析机制
- **本地化支持**: 完整的国际化日期处理
- **性能优化**: 缓存和记忆化策略
- **服务器兼容**: 标准化的序列化格式
- **用户友好**: 智能日期输入和错误处理

这个模块展现了现代 Web 应用中日期时间处理的最佳实践，为 Odoo 的全球化应用提供了强大的日期本地化支持。

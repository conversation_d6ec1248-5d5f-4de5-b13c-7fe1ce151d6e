# Odoo 翻译核心模块 (Translation Core) 学习资料

## 文件概述

**文件路径**: `output/@web/core/l10n/translation.js`  
**原始路径**: `/web/static/src/core/l10n/translation.js`  
**模块类型**: 核心基础模块 - 国际化翻译  
**代码行数**: 86 行  
**依赖关系**: 
- `@web/core/utils/concurrency` - 并发控制工具
- `@web/core/utils/strings` - 字符串格式化工具

## 模块功能

翻译核心模块是 Odoo Web 客户端国际化系统的核心，负责：
- 提供全局翻译函数 `_t()`
- 管理翻译状态和翻译数据
- 实现延迟翻译机制
- 处理带参数的翻译字符串
- 管理已安装语言列表

## 核心概念

### 1. 翻译状态管理
```javascript
const translationLoaded = Symbol("translationLoaded");
const translatedTerms = {
    [translationLoaded]: false,
};
const translationIsReady = new Deferred();
```

**设计分析**:
- **Symbol 标识**: 使用 Symbol 避免键名冲突
- **状态标志**: `translationLoaded` 标识翻译数据是否已加载
- **Promise 机制**: `translationIsReady` 提供异步等待翻译加载完成

### 2. 翻译函数 `_t()`

```javascript
function _t(term, ...values) {
    if (translatedTerms[translationLoaded]) {
        const translation = translatedTerms[term] ?? term;
        if (values.length === 0) {
            return translation;
        }
        return sprintf(translation, ...values);
    } else {
        return new LazyTranslatedString(term, ...values);
    }
}
```

**功能分析**:
- **条件翻译**: 检查翻译数据是否已加载
- **回退机制**: 未找到翻译时返回原始字符串
- **参数支持**: 使用 `sprintf` 处理格式化参数
- **延迟翻译**: 翻译未加载时返回 `LazyTranslatedString`

### 3. 延迟翻译类 `LazyTranslatedString`

```javascript
class LazyTranslatedString extends String {
    constructor(term, ...values) {
        super(term);
        this.values = values;
    }
    
    valueOf() {
        const term = super.valueOf();
        if (translatedTerms[translationLoaded]) {
            const translation = translatedTerms[term] ?? term;
            if (this.values.length === 0) {
                return translation;
            }
            return sprintf(translation, ...this.values);
        } else {
            throw new Error(`translation error`);
        }
    }
    
    toString() {
        return this.valueOf();
    }
}
```

**设计亮点**:
- **继承 String**: 保持字符串的基本行为
- **延迟求值**: 在实际使用时才进行翻译
- **参数保存**: 保存格式化参数供后续使用
- **错误处理**: 翻译未准备好时抛出错误

## 核心 API 分析

### 1. _t() 翻译函数

#### 基本用法
```javascript
// 简单翻译
const message = _t("Hello World");

// 带参数翻译
const greeting = _t("Hello %s", userName);
const count = _t("You have %d messages", messageCount);
```

#### 高级用法
```javascript
// 多参数翻译
const complex = _t("User %s has %d items in %s", userName, count, location);

// 延迟翻译（翻译数据未加载时）
const lazy = _t("Loading..."); // 返回 LazyTranslatedString 实例
```

### 2. loadLanguages() 语言加载函数

```javascript
async function loadLanguages(orm) {
    if (!loadLanguages.installedLanguages) {
        loadLanguages.installedLanguages = await orm.call("res.lang", "get_installed");
    }
    return loadLanguages.installedLanguages;
}
```

**功能特点**:
- **缓存机制**: 结果缓存在函数属性中
- **ORM 调用**: 通过 ORM 获取已安装语言
- **单次加载**: 避免重复请求服务器

## 时间相关翻译预定义

模块预定义了一系列时间相关的翻译字符串，用于 jQuery timeago 插件：

```javascript
_t("less than a minute ago");
_t("about a minute ago");
_t("%d minutes ago");
_t("about an hour ago");
_t("%d hours ago");
_t("a day ago");
_t("%d days ago");
_t("about a month ago");
_t("%d months ago");
_t("about a year ago");
_t("%d years ago");
```

**目的**:
- **翻译提取**: 帮助翻译提取工具识别这些字符串
- **时间显示**: 为相对时间显示提供本地化支持
- **用户体验**: 提供一致的时间表达方式

## 使用示例

### 1. 基本翻译使用

```javascript
import { _t } from "@web/core/l10n/translation";

// 在组件中使用
class MyComponent extends Component {
    setup() {
        this.title = _t("My Application");
        this.welcomeMessage = _t("Welcome, %s!", this.props.userName);
    }
}
```

### 2. 等待翻译加载

```javascript
import { translationIsReady } from "@web/core/l10n/translation";

async function initializeApp() {
    // 等待翻译数据加载完成
    await translationIsReady;
    
    // 现在可以安全使用翻译
    const message = _t("Application initialized");
    console.log(message);
}
```

### 3. 检查翻译状态

```javascript
import { translatedTerms, translationLoaded } from "@web/core/l10n/translation";

function isTranslationReady() {
    return translatedTerms[translationLoaded];
}

// 条件性使用翻译
const message = isTranslationReady() 
    ? _t("Ready") 
    : "Loading...";
```

### 4. 语言列表获取

```javascript
import { loadLanguages } from "@web/core/l10n/translation";

class LanguageSelector extends Component {
    async setup() {
        this.languages = await loadLanguages(this.env.services.orm);
    }
    
    renderLanguageOptions() {
        return this.languages.map(lang => ({
            value: lang[0],  // 语言代码
            label: lang[1]   // 语言名称
        }));
    }
}
```

## 设计模式分析

### 1. 单例模式
- 全局翻译状态管理
- 统一的翻译数据存储
- 避免重复加载语言数据

### 2. 延迟加载模式
- `LazyTranslatedString` 实现延迟翻译
- 翻译数据异步加载
- 提高应用启动性能

### 3. 代理模式
- `LazyTranslatedString` 代理真实的翻译字符串
- 透明的延迟求值
- 保持字符串接口一致性

### 4. 缓存模式
- 翻译数据缓存
- 语言列表缓存
- 减少服务器请求

## 性能优化策略

### 1. 延迟翻译
- 避免阻塞应用启动
- 按需进行翻译计算
- 减少初始化时间

### 2. 缓存机制
- 翻译结果缓存
- 语言列表缓存
- 避免重复计算

### 3. 符号键优化
- 使用 Symbol 避免字符串键冲突
- 提高对象属性访问性能
- 减少内存占用

## 错误处理机制

### 1. 翻译回退
```javascript
const translation = translatedTerms[term] ?? term;
```
- 未找到翻译时使用原始字符串
- 保证应用不会因翻译缺失而崩溃

### 2. 延迟翻译错误
```javascript
if (!translatedTerms[translationLoaded]) {
    throw new Error(`translation error`);
}
```
- 翻译数据未准备好时抛出明确错误
- 帮助开发者识别时序问题

## 最佳实践

### 1. 翻译字符串规范
```javascript
// 推荐：使用描述性的英文字符串
_t("Save changes");
_t("Delete selected items");

// 避免：使用简短或模糊的字符串
_t("OK");
_t("Error");
```

### 2. 参数化翻译
```javascript
// 推荐：使用参数化翻译
_t("Welcome back, %s!", userName);
_t("Found %d results", count);

// 避免：字符串拼接
_t("Welcome back, ") + userName + "!";
```

### 3. 翻译提取支持
```javascript
// 确保翻译提取工具能识别
_t("Static string");

// 避免动态字符串（提取工具无法识别）
const key = "dynamic_key";
_t(key);
```

## 与其他模块的关系

### 1. 本地化服务
- 本地化服务负责加载翻译数据
- 翻译模块提供翻译接口
- 协同工作完成国际化功能

### 2. 字符串工具
- 依赖 `sprintf` 进行格式化
- 支持复杂的参数替换
- 保持格式化一致性

### 3. 并发控制
- 使用 `Deferred` 管理异步状态
- 协调翻译加载时序
- 提供等待机制

## 总结

翻译核心模块是 Odoo Web 国际化系统的基石，它提供了：
- **简洁的 API**: `_t()` 函数易于使用
- **延迟加载**: 不阻塞应用启动
- **参数支持**: 灵活的字符串格式化
- **错误处理**: 完善的异常处理机制
- **性能优化**: 缓存和延迟求值策略

这个模块的设计体现了现代 Web 应用国际化的最佳实践，为 Odoo 的多语言支持提供了坚实的基础。

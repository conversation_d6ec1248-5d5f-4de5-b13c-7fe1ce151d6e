# Odoo 本地化配置对象 (Localization Object) 学习资料

## 文件概述

**文件路径**: `output/@web/core/l10n/localization.js`  
**原始路径**: `/web/static/src/core/l10n/localization.js`  
**模块类型**: 核心基础模块 - 本地化配置  
**代码行数**: 46 行  
**依赖关系**: 无外部依赖

## 模块功能

本地化配置对象是 Odoo Web 客户端本地化系统的核心数据容器，负责：
- 存储用户特定的本地化配置数据
- 提供全局访问的本地化参数
- 实现安全的配置访问控制
- 作为 Python `res.lang` 模型的 JavaScript 对应物

## 核心类型定义

### Localization 类型
```javascript
/**
 * @typedef Localization
 * @property {string} dateFormat        - 日期格式
 * @property {string} dateTimeFormat    - 日期时间格式
 * @property {string} timeFormat        - 时间格式
 * @property {string} decimalPoint      - 小数点符号
 * @property {"ltr" | "rtl"} direction  - 文本方向
 * @property {[number, number]} grouping - 数字分组规则
 * @property {boolean} multiLang        - 多语言支持标志
 * @property {string} thousandsSep      - 千位分隔符
 * @property {number} weekStart         - 一周开始日期
 * @property {string} code              - 语言代码
 */
```

**类型分析**:
- **完整性**: 涵盖了本地化的所有关键方面
- **类型安全**: 使用 TypeScript 风格的类型注释
- **标准化**: 遵循国际化标准和约定

## 核心设计：Proxy 模式

### 1. Proxy 实现
```javascript
const localization = new Proxy(
    {},
    {
        get: (target, p) => {
            // 允许访问已存在的属性和 "then" 属性
            if (p in target || p === "then") {
                return Reflect.get(target, p);
            }
            // 抛出明确的错误信息
            throw new Error(
                `could not access localization parameter "${p}": parameters are not ready yet. Maybe add 'localization' to your dependencies?`
            );
        },
    }
);
```

### 2. 设计亮点分析

#### 安全访问控制
- **未初始化检测**: 防止访问未初始化的属性
- **明确错误信息**: 提供有用的调试信息
- **依赖提示**: 建议正确的使用方式

#### 异步兼容性
```javascript
if (p in target || p === "then") {
    return Reflect.get(target, p);
}
```
- **then 属性处理**: 支持 async/await 语法
- **Promise 兼容**: 避免在异步上下文中出错

#### 反射机制
```javascript
return Reflect.get(target, p);
```
- **标准访问**: 使用 Reflect API 进行属性访问
- **性能优化**: 避免额外的处理开销

## 使用模式分析

### 1. 错误的使用方式
```javascript
// ❌ 错误：直接导入使用（数据可能未初始化）
import { localization } from "@web/core/l10n/localization";
const dateFormat = localization.dateFormat; // 可能抛出错误
```

### 2. 正确的使用方式
```javascript
// ✅ 正确：通过服务系统使用
class MyComponent extends Component {
    setup() {
        this.localization = useService("localization");
    }
    
    formatDate(date) {
        return date.toFormat(this.localization.dateFormat);
    }
}
```

### 3. 异步上下文使用
```javascript
// ✅ 正确：在异步函数中使用
async function processData() {
    const localizationService = env.services.localization;
    await localizationService; // 等待服务初始化
    
    // 现在可以安全访问
    const format = localization.dateFormat;
}
```

## 配置属性详解

### 1. 日期时间相关
```javascript
// 日期格式示例
localization.dateFormat      // "MM/dd/yyyy" 或 "dd/MM/yyyy"
localization.timeFormat      // "HH:mm:ss" 或 "h:mm:ss a"
localization.dateTimeFormat  // "MM/dd/yyyy HH:mm:ss"
```

### 2. 数字格式相关
```javascript
// 数字格式示例
localization.decimalPoint    // "." 或 ","
localization.thousandsSep    // "," 或 "." 或 " "
localization.grouping        // [3, 0] 表示每3位分组
```

### 3. 文化相关
```javascript
// 文化设置示例
localization.direction       // "ltr" 或 "rtl"
localization.weekStart       // 0 (周日) 或 1 (周一)
localization.multiLang       // true 或 false
localization.code           // "en_US", "zh_CN" 等
```

## 实际使用示例

### 1. 日期格式化
```javascript
class DateFormatter {
    constructor(localization) {
        this.localization = localization;
    }
    
    formatDate(date) {
        // 使用本地化的日期格式
        return luxon.DateTime.fromJSDate(date)
            .toFormat(this.localization.dateFormat);
    }
    
    formatDateTime(date) {
        return luxon.DateTime.fromJSDate(date)
            .toFormat(this.localization.dateTimeFormat);
    }
}
```

### 2. 数字格式化
```javascript
class NumberFormatter {
    constructor(localization) {
        this.localization = localization;
    }
    
    formatNumber(number) {
        const options = {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2,
        };
        
        // 根据本地化设置格式化数字
        const formatted = new Intl.NumberFormat(
            this.localization.code.replace('_', '-'),
            options
        ).format(number);
        
        return formatted;
    }
    
    formatCurrency(amount, currency = 'USD') {
        return new Intl.NumberFormat(
            this.localization.code.replace('_', '-'),
            {
                style: 'currency',
                currency: currency
            }
        ).format(amount);
    }
}
```

### 3. 文本方向处理
```javascript
class TextDirectionHandler {
    constructor(localization) {
        this.localization = localization;
    }
    
    applyDirection(element) {
        element.dir = this.localization.direction;
        
        if (this.localization.direction === 'rtl') {
            element.classList.add('rtl-layout');
        } else {
            element.classList.add('ltr-layout');
        }
    }
    
    isRTL() {
        return this.localization.direction === 'rtl';
    }
}
```

### 4. 周开始日期处理
```javascript
class CalendarHelper {
    constructor(localization) {
        this.localization = localization;
    }
    
    getWeekStart() {
        // 0 = 周日, 1 = 周一
        return this.localization.weekStart;
    }
    
    adjustWeekDays(days) {
        const weekStart = this.getWeekStart();
        if (weekStart === 0) {
            return days; // 周日开始，无需调整
        }
        
        // 周一开始，调整数组顺序
        return [...days.slice(1), days[0]];
    }
}
```

## 设计模式深度分析

### 1. 代理模式 (Proxy Pattern)
```javascript
const localization = new Proxy(target, handler);
```

**优势**:
- **透明访问**: 对使用者透明的属性访问
- **动态控制**: 可以动态控制访问行为
- **错误处理**: 统一的错误处理机制

**应用场景**:
- 延迟初始化的对象
- 需要访问控制的对象
- 需要记录访问日志的对象

### 2. 单例模式 (Singleton Pattern)
```javascript
const localization = __exports.localization = new Proxy(/* ... */);
```

**特点**:
- **全局唯一**: 整个应用只有一个实例
- **全局访问**: 可以在任何地方访问
- **状态共享**: 所有组件共享同一份配置

### 3. 延迟初始化模式 (Lazy Initialization)
- 对象创建时不包含实际数据
- 数据由本地化服务异步加载
- 访问时检查数据是否已准备好

## 错误处理机制

### 1. 访问控制错误
```javascript
throw new Error(
    `could not access localization parameter "${p}": parameters are not ready yet. Maybe add 'localization' to your dependencies?`
);
```

**错误信息特点**:
- **具体参数**: 显示尝试访问的参数名
- **原因说明**: 解释为什么访问失败
- **解决建议**: 提供解决问题的建议

### 2. 调试友好性
- 错误信息包含具体的属性名
- 提供明确的解决方案
- 帮助开发者理解正确的使用方式

## 与其他模块的关系

### 1. 本地化服务
- 服务负责加载和设置配置数据
- 配置对象负责存储和提供数据
- 通过 `Object.assign` 更新配置

### 2. 组件系统
- 组件通过服务系统访问配置
- 避免直接导入配置对象
- 确保配置数据已初始化

### 3. 格式化工具
- 日期格式化工具使用日期格式配置
- 数字格式化工具使用数字格式配置
- 文本处理工具使用方向配置

## 最佳实践

### 1. 服务依赖
```javascript
// ✅ 推荐：通过服务系统访问
class MyComponent extends Component {
    static services = ["localization"];
    
    setup() {
        this.localization = useService("localization");
    }
}
```

### 2. 异步处理
```javascript
// ✅ 推荐：等待初始化完成
async function useLocalization() {
    await env.services.localization;
    return localization;
}
```

### 3. 错误处理
```javascript
// ✅ 推荐：捕获访问错误
try {
    const format = localization.dateFormat;
} catch (error) {
    console.warn("Localization not ready:", error.message);
    // 使用默认格式
    const format = "MM/dd/yyyy";
}
```

## 性能考虑

### 1. Proxy 性能
- Proxy 会增加属性访问的开销
- 对于频繁访问的属性，考虑缓存
- 在性能关键路径中谨慎使用

### 2. 错误处理开销
- 异常抛出和捕获有性能成本
- 正确使用可以避免异常
- 开发环境中的调试价值更高

## 总结

本地化配置对象是 Odoo Web 国际化系统的核心数据容器，它：
- **安全设计**: 使用 Proxy 模式防止未初始化访问
- **类型完整**: 涵盖所有本地化配置需求
- **错误友好**: 提供清晰的错误信息和解决建议
- **全局访问**: 支持在整个应用中访问配置
- **异步兼容**: 与异步初始化流程完美配合

这个设计体现了现代 JavaScript 应用中安全性和可用性的平衡，为 Odoo 的国际化功能提供了坚实的基础。

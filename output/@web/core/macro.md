# @web/core/macro.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/macro.js`
- **原始路径**: `/web/static/src/core/macro.js`
- **代码行数**: 324行
- **作用**: 实现自动化宏系统，支持UI自动化测试、用户引导和重复任务自动化

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 宏系统的完整架构和设计思想
- 自动化UI操作的实现原理
- DOM变化监听和响应机制
- 异步任务调度和并发控制
- 错误处理和超时管理策略

## 📚 核心概念

### 什么是Macro系统？
Macro系统是Odoo Web框架中的**自动化执行引擎**，主要功能：
- **UI自动化**: 自动执行点击、输入等用户操作
- **测试支持**: 为自动化测试提供基础设施
- **用户引导**: 实现产品导览和新手指引
- **任务自动化**: 执行重复性的UI操作任务

### 核心组件架构
```javascript
// 宏步骤定义
const macroStep = {
    trigger: ".button-selector",     // 触发条件（CSS选择器或函数）
    action: "click",                 // 执行动作（预定义动作或自定义函数）
    value: "input text",            // 动作参数
    timeout: 5000                   // 步骤超时时间
};

// 宏描述符
const macroDescriptor = {
    name: "User Onboarding",        // 宏名称
    steps: [step1, step2, step3],   // 步骤列表
    timeout: 30000,                 // 全局超时
    checkDelay: 500,                // 检查间隔
    onStep: (el, step) => {},       // 步骤回调
    onError: (error, step) => {},   // 错误回调
    onTimeout: (step, index) => {}  // 超时回调
};
```

### 基本使用模式
```javascript
// 导入宏系统
const { MacroEngine, ACTION_HELPERS } = require("@web/core/macro");

// 创建宏引擎
const macroEngine = new MacroEngine({
    target: document.body,          // 监听目标
    defaultCheckDelay: 750         // 默认检查延迟
});

// 定义宏步骤
const onboardingMacro = {
    name: "Product Tour",
    steps: [
        {
            trigger: ".o_menu_toggle",
            action: "click"
        },
        {
            trigger: ".o_app[data-menu-xmlid='sale.sale_menu_root']",
            action: "click"
        },
        {
            trigger: ".o_list_button_add",
            action: "click"
        },
        {
            trigger: "input[name='name']",
            action: "text",
            value: "New Sale Order"
        }
    ],
    timeout: 30000,
    onStep: (el, step) => {
        console.log("Executing step:", step);
    }
};

// 激活宏
await macroEngine.activate(onboardingMacro);
```

## 🔍 核心类详解

### 1. Macro类 - 单个宏的执行器

#### 构造函数和初始化
```javascript
class Macro {
    constructor(descr) {
        this.name = descr.name || "anonymous";
        this.timeoutDuration = descr.timeout || 0;
        this.timeout = null;
        this.currentIndex = 0;                    // 当前步骤索引
        this.checkDelay = descr.checkDelay || 0;  // 检查延迟
        this.isComplete = false;                  // 完成标志
        this.steps = descr.steps;                 // 步骤列表
        this.onStep = descr.onStep || (() => {}); // 步骤回调
        this.onError = descr.onError;             // 错误回调
        this.onTimeout = descr.onTimeout;         // 超时回调
        this.setTimer();                          // 设置超时定时器
    }
}
```

#### advance() - 步骤推进方法
```javascript
async advance() {
    if (this.isComplete) {
        return; // 已完成，直接返回
    }
    
    const step = this.steps[this.currentIndex];
    const [proceedToAction, el] = this.checkTrigger(step);
    
    if (proceedToAction) {
        // 1. 执行步骤回调
        this.safeCall(this.onStep, el, step);
        
        // 2. 执行动作
        const actionResult = await this.performAction(el, step);
        
        if (!actionResult) {
            // 3. 动作成功，推进到下一步
            this.currentIndex++;
            
            if (this.currentIndex === this.steps.length) {
                // 4. 所有步骤完成
                this.isComplete = true;
                browser.clearTimeout(this.timeout);
            } else {
                // 5. 继续下一步
                this.setTimer();
                await this.advance();
            }
        }
    }
}
```

**推进逻辑分析**：
- **触发检查**: 验证当前步骤的触发条件
- **动作执行**: 执行步骤定义的动作
- **结果判断**: 根据动作结果决定是否推进
- **递归调用**: 成功后递归执行下一步

#### checkTrigger() - 触发条件检查
```javascript
checkTrigger({ trigger }) {
    let el;

    if (!trigger) {
        return [true, el]; // 无触发条件，直接执行
    }

    if (typeof trigger === "function") {
        // 函数触发器：执行函数获取元素
        el = this.safeCall(trigger);
    } else if (typeof trigger === "string") {
        // 字符串触发器：CSS选择器查找可见元素
        const triggerEl = document.querySelector(trigger);
        el = isVisible(triggerEl) && triggerEl;
    } else {
        throw new Error(`Trigger can only be string or function.`);
    }

    if (el) {
        return [true, el];  // 找到元素，可以执行动作
    } else {
        return [false, el]; // 未找到元素，等待下次检查
    }
}
```

**触发器类型**：
- **字符串触发器**: CSS选择器，查找可见的DOM元素
- **函数触发器**: 自定义函数，返回目标元素或null
- **空触发器**: 无条件执行动作

#### performAction() - 动作执行
```javascript
async performAction(el, step) {
    const action = step.action;
    let actionResult;
    
    if (action in ACTION_HELPERS) {
        // 使用预定义的动作助手
        actionResult = ACTION_HELPERS[action](el, step);
    } else if (typeof action === "function") {
        // 使用自定义动作函数
        actionResult = await this.safeCall(action, el, step);
    }
    
    return actionResult;
}
```

### 2. ACTION_HELPERS - 预定义动作

#### click() - 点击动作
```javascript
click(el, _step) {
    // 模拟完整的鼠标事件序列
    el.dispatchEvent(new MouseEvent("mouseover"));
    el.dispatchEvent(new MouseEvent("mouseenter"));
    el.dispatchEvent(new MouseEvent("mousedown"));
    el.dispatchEvent(new MouseEvent("mouseup"));
    el.click();                                      // 实际点击
    el.dispatchEvent(new MouseEvent("mouseout"));
    el.dispatchEvent(new MouseEvent("mouseleave"));
}
```

**事件序列设计**：
- **鼠标悬停**: mouseover, mouseenter
- **鼠标按下**: mousedown
- **鼠标释放**: mouseup
- **实际点击**: click()
- **鼠标离开**: mouseout, mouseleave

#### text() - 文本输入动作
```javascript
text(el, step) {
    // 1. 先点击元素获得焦点
    this.click(el, step);
    
    // 2. 设置输入值
    el.value = step.value;
    
    // 3. 触发输入事件
    el.dispatchEvent(new InputEvent("input", { bubbles: true }));
    el.dispatchEvent(new InputEvent("change", { bubbles: true }));
}
```

### 3. MacroEngine类 - 宏引擎

#### 引擎初始化和配置
```javascript
class MacroEngine {
    constructor(params = {}) {
        this.isRunning = false;                           // 运行状态
        this.timeout = null;                              // 检查定时器
        this.target = params.target || document.body;    // 监听目标
        this.defaultCheckDelay = params.defaultCheckDelay ?? 750; // 默认检查延迟
        this.macros = new Set();                          // 活动宏集合
        this.macroMutationObserver = new MacroMutationObserver(() => this.delayedCheck());
    }
}
```

#### activate() - 宏激活
```javascript
async activate(descr, exclusive = false) {
    if (this.exclusive) {
        return; // 独占模式下拒绝新宏
    }
    
    this.exclusive = exclusive;
    
    // 微任务延迟确保在新调用栈中添加宏
    await Promise.resolve();
    
    const macro = new Macro(descr);
    
    if (exclusive) {
        this.macros = new Set([macro]); // 独占模式：清除其他宏
    } else {
        this.macros.add(macro);         // 并发模式：添加到集合
    }
    
    this.start();
}
```

**激活模式**：
- **并发模式**: 多个宏可以同时运行
- **独占模式**: 只允许一个宏运行，清除其他宏

#### advanceMacros() - 宏推进调度
```javascript
async advanceMacros() {
    // 1. 并行推进所有活动宏
    await Promise.all([...this.macros].map((macro) => macro.advance()));
    
    // 2. 清理已完成的宏
    for (const macro of this.macros) {
        if (macro.isComplete) {
            this.macros.delete(macro);
        }
    }
    
    // 3. 无活动宏时停止引擎
    if (this.macros.size === 0) {
        this.stop();
    }
}
```

**调度策略**：
- **并行执行**: 使用Promise.all同时推进所有宏
- **自动清理**: 移除已完成的宏实例
- **自动停止**: 无活动宏时停止引擎

#### getCheckDelay() - 动态延迟计算
```javascript
getCheckDelay() {
    // 选择所有宏中最小的非零延迟值
    return [...this.macros]
        .map((m) => m.checkDelay)
        .filter((delay) => delay > 0)
        .reduce((m, v) => Math.min(m, v), this.defaultCheckDelay);
}
```

**延迟策略**：
- **最小延迟**: 选择所有宏中最小的检查延迟
- **性能优化**: 避免因慢宏影响快宏的执行
- **默认回退**: 无自定义延迟时使用默认值

### 4. MacroMutationObserver类 - DOM变化监听

#### 观察器配置
```javascript
class MacroMutationObserver {
    observerOptions = {
        attributes: true,      // 监听属性变化
        childList: true,       // 监听子节点变化
        subtree: true,         // 监听子树变化
        characterData: true,   // 监听文本内容变化
    };
}
```

#### 高级DOM监听
```javascript
constructor(callback) {
    this.callback = callback;
    this.observer = new MutationObserver((mutationList, observer) => {
        callback(); // 触发宏检查
        
        mutationList.forEach((mutationRecord) =>
            Array.from(mutationRecord.addedNodes).forEach((node) => {
                // 1. 监听新增的iframe
                let iframes = [];
                if (String(node.tagName).toLowerCase() === "iframe") {
                    iframes = [node];
                } else if (node instanceof HTMLElement) {
                    iframes = Array.from(node.querySelectorAll("iframe"));
                }
                iframes.forEach((iframeEl) =>
                    this.observeIframe(iframeEl, observer, () => callback())
                );
                
                // 2. 监听Shadow DOM
                this.findAllShadowRoots(node).forEach((shadowRoot) =>
                    observer.observe(shadowRoot, this.observerOptions)
                );
            })
        );
    });
}
```

**高级特性**：
- **iframe监听**: 自动监听新增iframe的内容变化
- **Shadow DOM**: 支持Web Components的Shadow DOM监听
- **递归监听**: 深度监听所有子树变化

## 🎨 实际应用场景

### 1. 用户引导系统
```javascript
class UserOnboardingService {
    constructor() {
        this.macroEngine = new MacroEngine();
        this.tours = new Map();
    }
    
    // 注册产品导览
    registerTour(name, tourConfig) {
        this.tours.set(name, {
            name: tourConfig.name || name,
            steps: tourConfig.steps,
            timeout: tourConfig.timeout || 60000,
            onStep: (el, step) => {
                this.showTooltip(el, step);
            },
            onError: (error, step, index) => {
                console.error(`Tour ${name} failed at step ${index}:`, error);
                this.showErrorMessage(step, error);
            },
            onTimeout: (step, index) => {
                console.warn(`Tour ${name} timeout at step ${index}`);
                this.showTimeoutMessage(step);
            }
        });
    }
    
    // 启动导览
    async startTour(name) {
        const tour = this.tours.get(name);
        if (!tour) {
            throw new Error(`Tour ${name} not found`);
        }
        
        // 显示欢迎消息
        this.showWelcomeMessage(tour);
        
        // 激活宏
        await this.macroEngine.activate(tour, true); // 独占模式
    }
    
    showTooltip(el, step) {
        // 创建工具提示
        const tooltip = document.createElement('div');
        tooltip.className = 'tour-tooltip';
        tooltip.innerHTML = `
            <div class="tooltip-content">
                <h4>${step.title || 'Next Step'}</h4>
                <p>${step.description || 'Follow the highlighted element'}</p>
                <div class="tooltip-actions">
                    <button class="btn-skip">Skip Tour</button>
                    <button class="btn-next">Next</button>
                </div>
            </div>
        `;
        
        // 定位工具提示
        this.positionTooltip(tooltip, el);
        
        // 高亮目标元素
        this.highlightElement(el);
        
        document.body.appendChild(tooltip);
        
        // 绑定事件
        tooltip.querySelector('.btn-skip').onclick = () => this.stopTour();
        tooltip.querySelector('.btn-next').onclick = () => this.hideTooltip(tooltip);
    }
    
    positionTooltip(tooltip, targetEl) {
        const rect = targetEl.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();
        
        // 计算最佳位置
        let top = rect.bottom + 10;
        let left = rect.left + (rect.width - tooltipRect.width) / 2;
        
        // 边界检查
        if (left < 10) left = 10;
        if (left + tooltipRect.width > window.innerWidth - 10) {
            left = window.innerWidth - tooltipRect.width - 10;
        }
        if (top + tooltipRect.height > window.innerHeight - 10) {
            top = rect.top - tooltipRect.height - 10;
        }
        
        tooltip.style.position = 'fixed';
        tooltip.style.top = top + 'px';
        tooltip.style.left = left + 'px';
        tooltip.style.zIndex = '9999';
    }
    
    highlightElement(el) {
        // 移除之前的高亮
        document.querySelectorAll('.tour-highlight').forEach(elem => {
            elem.classList.remove('tour-highlight');
        });
        
        // 添加高亮样式
        el.classList.add('tour-highlight');
        
        // 滚动到视图中
        el.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
}

// 使用示例
const onboardingService = new UserOnboardingService();

// 注册销售模块导览
onboardingService.registerTour('sales_tour', {
    name: 'Sales Module Tour',
    steps: [
        {
            trigger: '.o_app[data-menu-xmlid="sale.sale_menu_root"]',
            action: 'click',
            title: 'Open Sales App',
            description: 'Click here to open the Sales application'
        },
        {
            trigger: '.o_list_button_add',
            action: 'click',
            title: 'Create New Order',
            description: 'Click this button to create a new sales order'
        },
        {
            trigger: 'input[name="partner_id"]',
            action: 'click',
            title: 'Select Customer',
            description: 'Choose a customer for this order'
        }
    ]
});

// 启动导览
await onboardingService.startTour('sales_tour');
```

### 2. 自动化测试框架
```javascript
class AutomatedTestRunner {
    constructor() {
        this.macroEngine = new MacroEngine({
            defaultCheckDelay: 100 // 测试时使用更短的延迟
        });
        this.testSuites = new Map();
        this.results = [];
    }

    // 注册测试套件
    registerTestSuite(name, config) {
        this.testSuites.set(name, {
            name: config.name || name,
            description: config.description,
            setup: config.setup || (() => {}),
            teardown: config.teardown || (() => {}),
            tests: config.tests || []
        });
    }

    // 运行单个测试
    async runTest(testConfig) {
        const startTime = Date.now();
        let result = {
            name: testConfig.name,
            status: 'running',
            startTime,
            endTime: null,
            duration: null,
            error: null,
            steps: []
        };

        try {
            // 创建测试宏
            const testMacro = {
                name: testConfig.name,
                steps: testConfig.steps,
                timeout: testConfig.timeout || 30000,
                onStep: (el, step) => {
                    result.steps.push({
                        step: step,
                        element: el,
                        timestamp: Date.now()
                    });
                },
                onError: (error, step, index) => {
                    result.status = 'failed';
                    result.error = {
                        message: error.message,
                        step: step,
                        stepIndex: index
                    };
                },
                onTimeout: (step, index) => {
                    result.status = 'timeout';
                    result.error = {
                        message: 'Test step timeout',
                        step: step,
                        stepIndex: index
                    };
                }
            };

            // 执行测试
            await this.macroEngine.activate(testMacro, true);

            // 等待测试完成
            await this.waitForTestCompletion(testMacro.name);

            if (result.status === 'running') {
                result.status = 'passed';
            }

        } catch (error) {
            result.status = 'failed';
            result.error = {
                message: error.message,
                stack: error.stack
            };
        } finally {
            result.endTime = Date.now();
            result.duration = result.endTime - result.startTime;
        }

        return result;
    }

    // 运行测试套件
    async runTestSuite(suiteName) {
        const suite = this.testSuites.get(suiteName);
        if (!suite) {
            throw new Error(`Test suite ${suiteName} not found`);
        }

        const suiteResult = {
            name: suiteName,
            description: suite.description,
            startTime: Date.now(),
            endTime: null,
            duration: null,
            tests: [],
            summary: {
                total: suite.tests.length,
                passed: 0,
                failed: 0,
                timeout: 0
            }
        };

        try {
            // 执行setup
            await suite.setup();

            // 运行所有测试
            for (const test of suite.tests) {
                const testResult = await this.runTest(test);
                suiteResult.tests.push(testResult);
                suiteResult.summary[testResult.status]++;
            }

        } finally {
            // 执行teardown
            await suite.teardown();

            suiteResult.endTime = Date.now();
            suiteResult.duration = suiteResult.endTime - suiteResult.startTime;
        }

        this.results.push(suiteResult);
        return suiteResult;
    }

    // 等待测试完成
    async waitForTestCompletion(testName, maxWait = 60000) {
        const startTime = Date.now();

        while (Date.now() - startTime < maxWait) {
            if (this.macroEngine.macros.size === 0) {
                return; // 所有宏都已完成
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        throw new Error(`Test ${testName} did not complete within ${maxWait}ms`);
    }

    // 生成测试报告
    generateReport() {
        const totalTests = this.results.reduce((sum, suite) => sum + suite.summary.total, 0);
        const totalPassed = this.results.reduce((sum, suite) => sum + suite.summary.passed, 0);
        const totalFailed = this.results.reduce((sum, suite) => sum + suite.summary.failed, 0);
        const totalTimeout = this.results.reduce((sum, suite) => sum + suite.summary.timeout, 0);

        return {
            summary: {
                totalSuites: this.results.length,
                totalTests,
                totalPassed,
                totalFailed,
                totalTimeout,
                successRate: totalTests > 0 ? (totalPassed / totalTests * 100).toFixed(2) + '%' : '0%'
            },
            suites: this.results,
            failedTests: this.results.flatMap(suite =>
                suite.tests.filter(test => test.status === 'failed')
            )
        };
    }
}

// 使用示例
const testRunner = new AutomatedTestRunner();

// 注册测试套件
testRunner.registerTestSuite('sales_workflow', {
    name: 'Sales Workflow Tests',
    description: 'Test the complete sales order workflow',
    setup: async () => {
        // 测试前准备
        console.log('Setting up test environment...');
    },
    teardown: async () => {
        // 测试后清理
        console.log('Cleaning up test environment...');
    },
    tests: [
        {
            name: 'Create Sales Order',
            steps: [
                {
                    trigger: '.o_app[data-menu-xmlid="sale.sale_menu_root"]',
                    action: 'click'
                },
                {
                    trigger: '.o_list_button_add',
                    action: 'click'
                },
                {
                    trigger: 'input[name="partner_id"]',
                    action: 'click'
                },
                {
                    trigger: '.ui-menu-item:first',
                    action: 'click'
                },
                {
                    trigger: '.o_form_button_save',
                    action: 'click'
                }
            ]
        },
        {
            name: 'Confirm Sales Order',
            steps: [
                {
                    trigger: '.o_statusbar_buttons button[name="action_confirm"]',
                    action: 'click'
                },
                {
                    trigger: '.o_form_statusbar .o_arrow_button_current:contains("Sales Order")',
                    action: () => {
                        // 验证状态变化
                        const statusElement = document.querySelector('.o_form_statusbar .o_arrow_button_current');
                        return statusElement && statusElement.textContent.includes('Sales Order');
                    }
                }
            ]
        }
    ]
});

// 运行测试
const result = await testRunner.runTestSuite('sales_workflow');
console.log('Test Results:', testRunner.generateReport());
```

### 3. 重复任务自动化
```javascript
class TaskAutomationService {
    constructor() {
        this.macroEngine = new MacroEngine();
        this.automations = new Map();
        this.schedules = new Map();
    }

    // 注册自动化任务
    registerAutomation(name, config) {
        this.automations.set(name, {
            name: config.name || name,
            description: config.description,
            steps: config.steps,
            condition: config.condition || (() => true),
            interval: config.interval || 0, // 0表示一次性任务
            maxRetries: config.maxRetries || 3,
            onSuccess: config.onSuccess || (() => {}),
            onFailure: config.onFailure || (() => {})
        });
    }

    // 执行自动化任务
    async executeAutomation(name) {
        const automation = this.automations.get(name);
        if (!automation) {
            throw new Error(`Automation ${name} not found`);
        }

        // 检查执行条件
        if (!automation.condition()) {
            console.log(`Automation ${name} condition not met, skipping`);
            return;
        }

        let retries = 0;
        let success = false;

        while (retries <= automation.maxRetries && !success) {
            try {
                const macro = {
                    name: `${automation.name} (attempt ${retries + 1})`,
                    steps: automation.steps,
                    timeout: 30000,
                    onError: (error, step, index) => {
                        console.error(`Automation ${name} failed at step ${index}:`, error);
                    }
                };

                await this.macroEngine.activate(macro, true);
                await this.waitForCompletion();

                success = true;
                automation.onSuccess();
                console.log(`Automation ${name} completed successfully`);

            } catch (error) {
                retries++;
                console.warn(`Automation ${name} attempt ${retries} failed:`, error);

                if (retries > automation.maxRetries) {
                    automation.onFailure(error);
                    throw error;
                }

                // 重试前等待
                await new Promise(resolve => setTimeout(resolve, 1000 * retries));
            }
        }
    }

    // 调度定期任务
    scheduleAutomation(name, cronExpression) {
        const automation = this.automations.get(name);
        if (!automation) {
            throw new Error(`Automation ${name} not found`);
        }

        if (automation.interval > 0) {
            const intervalId = setInterval(async () => {
                try {
                    await this.executeAutomation(name);
                } catch (error) {
                    console.error(`Scheduled automation ${name} failed:`, error);
                }
            }, automation.interval);

            this.schedules.set(name, intervalId);
            console.log(`Automation ${name} scheduled with interval ${automation.interval}ms`);
        }
    }

    // 停止调度任务
    unscheduleAutomation(name) {
        const intervalId = this.schedules.get(name);
        if (intervalId) {
            clearInterval(intervalId);
            this.schedules.delete(name);
            console.log(`Automation ${name} unscheduled`);
        }
    }

    async waitForCompletion() {
        while (this.macroEngine.macros.size > 0) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }
}

// 使用示例
const automationService = new TaskAutomationService();

// 注册数据备份自动化
automationService.registerAutomation('daily_backup', {
    name: 'Daily Data Backup',
    description: 'Automatically backup important data',
    steps: [
        {
            trigger: '.o_menu_toggle',
            action: 'click'
        },
        {
            trigger: '.o_app[data-menu-xmlid="base.menu_administration"]',
            action: 'click'
        },
        {
            trigger: 'a[data-menu-xmlid="base.menu_ir_config_menu"]',
            action: 'click'
        },
        {
            trigger: 'button[name="backup_database"]',
            action: 'click'
        }
    ],
    condition: () => {
        // 只在工作时间执行
        const hour = new Date().getHours();
        return hour >= 9 && hour <= 17;
    },
    interval: 24 * 60 * 60 * 1000, // 每24小时执行一次
    onSuccess: () => {
        console.log('Backup completed successfully');
        // 发送通知邮件
    },
    onFailure: (error) => {
        console.error('Backup failed:', error);
        // 发送警报
    }
});

// 启动调度
automationService.scheduleAutomation('daily_backup');
```

## 🛠️ 实践练习

### 练习1: 智能宏调度器
```javascript
class SmartMacroScheduler {
    constructor() {
        this.macroEngine = new MacroEngine();
        this.queue = [];
        this.running = false;
        this.priorities = new Map();
        this.dependencies = new Map();
        this.results = new Map();
    }

    // 添加宏到队列
    addMacro(macro, options = {}) {
        const macroItem = {
            id: this.generateId(),
            macro: macro,
            priority: options.priority || 0,
            dependencies: options.dependencies || [],
            retries: options.retries || 0,
            maxRetries: options.maxRetries || 3,
            delay: options.delay || 0,
            addedAt: Date.now()
        };

        this.queue.push(macroItem);
        this.sortQueue();

        if (!this.running) {
            this.start();
        }

        return macroItem.id;
    }

    // 按优先级排序队列
    sortQueue() {
        this.queue.sort((a, b) => {
            // 优先级高的在前
            if (a.priority !== b.priority) {
                return b.priority - a.priority;
            }
            // 相同优先级按添加时间排序
            return a.addedAt - b.addedAt;
        });
    }

    // 检查依赖是否满足
    checkDependencies(macroItem) {
        return macroItem.dependencies.every(depId => {
            const result = this.results.get(depId);
            return result && result.status === 'success';
        });
    }

    // 开始执行队列
    async start() {
        if (this.running) return;

        this.running = true;

        while (this.queue.length > 0) {
            const macroItem = this.findNextExecutable();

            if (!macroItem) {
                // 没有可执行的宏，等待一段时间后重试
                await new Promise(resolve => setTimeout(resolve, 1000));
                continue;
            }

            // 从队列中移除
            this.queue = this.queue.filter(item => item.id !== macroItem.id);

            // 执行宏
            await this.executeMacro(macroItem);
        }

        this.running = false;
    }

    // 查找下一个可执行的宏
    findNextExecutable() {
        for (const macroItem of this.queue) {
            if (this.checkDependencies(macroItem)) {
                return macroItem;
            }
        }
        return null;
    }

    // 执行单个宏
    async executeMacro(macroItem) {
        const startTime = Date.now();

        try {
            // 应用延迟
            if (macroItem.delay > 0) {
                await new Promise(resolve => setTimeout(resolve, macroItem.delay));
            }

            // 创建增强的宏配置
            const enhancedMacro = {
                ...macroItem.macro,
                name: `${macroItem.macro.name} (${macroItem.id})`,
                onError: (error, step, index) => {
                    console.error(`Macro ${macroItem.id} failed:`, error);
                    if (macroItem.macro.onError) {
                        macroItem.macro.onError(error, step, index);
                    }
                }
            };

            // 执行宏
            await this.macroEngine.activate(enhancedMacro, true);
            await this.waitForCompletion();

            // 记录成功结果
            this.results.set(macroItem.id, {
                status: 'success',
                startTime,
                endTime: Date.now(),
                retries: macroItem.retries
            });

            console.log(`Macro ${macroItem.id} completed successfully`);

        } catch (error) {
            macroItem.retries++;

            if (macroItem.retries <= macroItem.maxRetries) {
                // 重新加入队列重试
                console.log(`Retrying macro ${macroItem.id} (attempt ${macroItem.retries})`);
                this.queue.unshift(macroItem);
            } else {
                // 记录失败结果
                this.results.set(macroItem.id, {
                    status: 'failed',
                    error: error.message,
                    startTime,
                    endTime: Date.now(),
                    retries: macroItem.retries
                });

                console.error(`Macro ${macroItem.id} failed after ${macroItem.retries} attempts`);
            }
        }
    }

    async waitForCompletion() {
        while (this.macroEngine.macros.size > 0) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }

    generateId() {
        return 'macro_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // 获取队列状态
    getStatus() {
        return {
            running: this.running,
            queueLength: this.queue.length,
            completedCount: this.results.size,
            successCount: Array.from(this.results.values()).filter(r => r.status === 'success').length,
            failedCount: Array.from(this.results.values()).filter(r => r.status === 'failed').length
        };
    }
}

// 使用示例
const scheduler = new SmartMacroScheduler();

// 添加高优先级宏
const highPriorityId = scheduler.addMacro({
    name: 'Critical Task',
    steps: [/* 步骤定义 */]
}, {
    priority: 10,
    maxRetries: 5
});

// 添加依赖宏
const dependentId = scheduler.addMacro({
    name: 'Dependent Task',
    steps: [/* 步骤定义 */]
}, {
    priority: 5,
    dependencies: [highPriorityId],
    delay: 2000
});
```

### 练习2: 宏录制器
```javascript
class MacroRecorder {
    constructor() {
        this.isRecording = false;
        this.recordedSteps = [];
        this.startTime = null;
        this.eventListeners = new Map();
        this.excludeSelectors = [
            '.macro-recorder-ui',
            '.tooltip',
            '.dropdown-menu'
        ];
    }

    // 开始录制
    startRecording() {
        if (this.isRecording) {
            console.warn('Already recording');
            return;
        }

        this.isRecording = true;
        this.recordedSteps = [];
        this.startTime = Date.now();

        this.setupEventListeners();
        this.showRecordingUI();

        console.log('Macro recording started');
    }

    // 停止录制
    stopRecording() {
        if (!this.isRecording) {
            console.warn('Not currently recording');
            return null;
        }

        this.isRecording = false;
        this.removeEventListeners();
        this.hideRecordingUI();

        const macro = {
            name: `Recorded Macro ${new Date().toISOString()}`,
            steps: this.recordedSteps,
            recordedAt: new Date().toISOString(),
            duration: Date.now() - this.startTime
        };

        console.log('Macro recording stopped', macro);
        return macro;
    }

    // 设置事件监听器
    setupEventListeners() {
        // 点击事件
        const clickHandler = (event) => {
            if (this.shouldIgnoreElement(event.target)) return;

            this.recordStep({
                action: 'click',
                trigger: this.generateSelector(event.target),
                timestamp: Date.now() - this.startTime,
                element: this.getElementInfo(event.target)
            });
        };

        // 输入事件
        const inputHandler = (event) => {
            if (this.shouldIgnoreElement(event.target)) return;

            this.recordStep({
                action: 'text',
                trigger: this.generateSelector(event.target),
                value: event.target.value,
                timestamp: Date.now() - this.startTime,
                element: this.getElementInfo(event.target)
            });
        };

        // 键盘事件
        const keyHandler = (event) => {
            if (this.shouldIgnoreElement(event.target)) return;

            // 只记录特殊键
            if (['Enter', 'Tab', 'Escape'].includes(event.key)) {
                this.recordStep({
                    action: 'key',
                    trigger: this.generateSelector(event.target),
                    key: event.key,
                    timestamp: Date.now() - this.startTime,
                    element: this.getElementInfo(event.target)
                });
            }
        };

        document.addEventListener('click', clickHandler, true);
        document.addEventListener('input', inputHandler, true);
        document.addEventListener('keydown', keyHandler, true);

        this.eventListeners.set('click', clickHandler);
        this.eventListeners.set('input', inputHandler);
        this.eventListeners.set('keydown', keyHandler);
    }

    // 移除事件监听器
    removeEventListeners() {
        for (const [eventType, handler] of this.eventListeners.entries()) {
            document.removeEventListener(eventType, handler, true);
        }
        this.eventListeners.clear();
    }

    // 记录步骤
    recordStep(step) {
        // 去重：如果与上一步相同则跳过
        const lastStep = this.recordedSteps[this.recordedSteps.length - 1];
        if (lastStep && this.isSameStep(lastStep, step)) {
            return;
        }

        this.recordedSteps.push(step);
        this.updateRecordingUI();

        console.log('Recorded step:', step);
    }

    // 检查是否为相同步骤
    isSameStep(step1, step2) {
        return step1.action === step2.action &&
               step1.trigger === step2.trigger &&
               step1.value === step2.value;
    }

    // 生成CSS选择器
    generateSelector(element) {
        // 优先使用ID
        if (element.id) {
            return `#${element.id}`;
        }

        // 使用类名
        if (element.className) {
            const classes = element.className.split(' ')
                .filter(cls => cls && !cls.startsWith('o_'))
                .slice(0, 2);
            if (classes.length > 0) {
                return `.${classes.join('.')}`;
            }
        }

        // 使用属性
        const attributes = ['name', 'data-menu-xmlid', 'data-action'];
        for (const attr of attributes) {
            if (element.hasAttribute(attr)) {
                return `[${attr}="${element.getAttribute(attr)}"]`;
            }
        }

        // 使用标签名和位置
        const tagName = element.tagName.toLowerCase();
        const parent = element.parentElement;
        if (parent) {
            const siblings = Array.from(parent.children).filter(el => el.tagName === element.tagName);
            const index = siblings.indexOf(element);
            return `${tagName}:nth-of-type(${index + 1})`;
        }

        return tagName;
    }

    // 获取元素信息
    getElementInfo(element) {
        return {
            tagName: element.tagName,
            className: element.className,
            id: element.id,
            text: element.textContent?.trim().substring(0, 50),
            attributes: this.getRelevantAttributes(element)
        };
    }

    getRelevantAttributes(element) {
        const relevantAttrs = ['name', 'type', 'data-menu-xmlid', 'data-action'];
        const attrs = {};

        for (const attr of relevantAttrs) {
            if (element.hasAttribute(attr)) {
                attrs[attr] = element.getAttribute(attr);
            }
        }

        return attrs;
    }

    // 检查是否应该忽略元素
    shouldIgnoreElement(element) {
        return this.excludeSelectors.some(selector =>
            element.closest(selector)
        );
    }

    // 显示录制UI
    showRecordingUI() {
        const ui = document.createElement('div');
        ui.className = 'macro-recorder-ui';
        ui.innerHTML = `
            <div class="recorder-panel">
                <div class="recorder-header">
                    <span class="recording-indicator">🔴 Recording</span>
                    <span class="step-counter">Steps: 0</span>
                </div>
                <div class="recorder-controls">
                    <button class="btn-stop">Stop</button>
                    <button class="btn-pause">Pause</button>
                    <button class="btn-clear">Clear</button>
                </div>
            </div>
        `;

        // 样式
        ui.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            background: white;
            border: 2px solid #dc3545;
            border-radius: 8px;
            padding: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            font-family: Arial, sans-serif;
        `;

        // 绑定事件
        ui.querySelector('.btn-stop').onclick = () => this.stopRecording();
        ui.querySelector('.btn-pause').onclick = () => this.togglePause();
        ui.querySelector('.btn-clear').onclick = () => this.clearSteps();

        document.body.appendChild(ui);
        this.recordingUI = ui;
    }

    // 隐藏录制UI
    hideRecordingUI() {
        if (this.recordingUI) {
            this.recordingUI.remove();
            this.recordingUI = null;
        }
    }

    // 更新录制UI
    updateRecordingUI() {
        if (this.recordingUI) {
            const counter = this.recordingUI.querySelector('.step-counter');
            counter.textContent = `Steps: ${this.recordedSteps.length}`;
        }
    }

    // 切换暂停状态
    togglePause() {
        this.isPaused = !this.isPaused;
        const btn = this.recordingUI.querySelector('.btn-pause');
        btn.textContent = this.isPaused ? 'Resume' : 'Pause';

        if (this.isPaused) {
            this.removeEventListeners();
        } else {
            this.setupEventListeners();
        }
    }

    // 清除步骤
    clearSteps() {
        this.recordedSteps = [];
        this.updateRecordingUI();
    }

    // 导出宏
    exportMacro(format = 'json') {
        const macro = {
            name: `Recorded Macro ${new Date().toISOString()}`,
            steps: this.recordedSteps,
            metadata: {
                recordedAt: new Date().toISOString(),
                duration: Date.now() - this.startTime,
                stepCount: this.recordedSteps.length,
                userAgent: navigator.userAgent
            }
        };

        switch (format) {
            case 'json':
                return JSON.stringify(macro, null, 2);

            case 'javascript':
                return this.generateJavaScriptCode(macro);

            default:
                return macro;
        }
    }

    generateJavaScriptCode(macro) {
        const stepsCode = macro.steps.map(step => {
            const stepObj = {
                trigger: step.trigger,
                action: step.action
            };

            if (step.value) stepObj.value = step.value;
            if (step.key) stepObj.key = step.key;

            return `        ${JSON.stringify(stepObj)}`;
        }).join(',\n');

        return `
// Generated macro: ${macro.name}
const macro = {
    name: "${macro.name}",
    steps: [
${stepsCode}
    ],
    timeout: 30000
};

// Execute the macro
await macroEngine.activate(macro);
        `.trim();
    }
}

// 使用示例
const recorder = new MacroRecorder();

// 开始录制
recorder.startRecording();

// 用户执行操作...

// 停止录制并获取宏
const recordedMacro = recorder.stopRecording();

// 导出为JavaScript代码
const jsCode = recorder.exportMacro('javascript');
console.log(jsCode);
```

### 练习3: 宏调试器
```javascript
class MacroDebugger {
    constructor(macroEngine) {
        this.macroEngine = macroEngine;
        this.breakpoints = new Set();
        this.watchedElements = new Map();
        this.executionLog = [];
        this.isDebugging = false;
        this.currentMacro = null;
        this.stepIndex = 0;
    }

    // 开始调试模式
    startDebugging(macro) {
        this.isDebugging = true;
        this.currentMacro = macro;
        this.stepIndex = 0;
        this.executionLog = [];

        // 包装宏步骤以添加调试功能
        const debugMacro = this.wrapMacroForDebugging(macro);

        this.showDebugUI();
        return debugMacro;
    }

    // 包装宏以添加调试功能
    wrapMacroForDebugging(macro) {
        const originalSteps = macro.steps;
        const debugSteps = originalSteps.map((step, index) => ({
            ...step,
            originalIndex: index,
            action: this.wrapAction(step.action, index),
            trigger: this.wrapTrigger(step.trigger, index)
        }));

        return {
            ...macro,
            steps: debugSteps,
            onStep: (el, step) => {
                this.onStepExecuted(el, step);
                if (macro.onStep) {
                    macro.onStep(el, step);
                }
            },
            onError: (error, step, index) => {
                this.onStepError(error, step, index);
                if (macro.onError) {
                    macro.onError(error, step, index);
                }
            }
        };
    }

    // 包装动作以添加调试
    wrapAction(originalAction, stepIndex) {
        return async (el, step) => {
            // 检查断点
            if (this.breakpoints.has(stepIndex)) {
                await this.handleBreakpoint(stepIndex, el, step);
            }

            // 记录执行前状态
            this.logStepStart(stepIndex, el, step);

            try {
                let result;
                if (typeof originalAction === 'string' && originalAction in ACTION_HELPERS) {
                    result = ACTION_HELPERS[originalAction](el, step);
                } else if (typeof originalAction === 'function') {
                    result = await originalAction(el, step);
                }

                this.logStepSuccess(stepIndex, result);
                return result;

            } catch (error) {
                this.logStepError(stepIndex, error);
                throw error;
            }
        };
    }

    // 包装触发器以添加调试
    wrapTrigger(originalTrigger, stepIndex) {
        if (typeof originalTrigger === 'function') {
            return () => {
                const result = originalTrigger();
                this.logTriggerCheck(stepIndex, result);
                return result;
            };
        }
        return originalTrigger;
    }

    // 处理断点
    async handleBreakpoint(stepIndex, el, step) {
        console.log(`Breakpoint hit at step ${stepIndex}`);

        this.highlightElement(el);
        this.updateDebugUI(stepIndex, el, step);

        // 等待用户继续
        return new Promise(resolve => {
            this.continueCallback = resolve;
        });
    }

    // 添加断点
    addBreakpoint(stepIndex) {
        this.breakpoints.add(stepIndex);
        this.updateBreakpointUI();
    }

    // 移除断点
    removeBreakpoint(stepIndex) {
        this.breakpoints.delete(stepIndex);
        this.updateBreakpointUI();
    }

    // 监听元素变化
    watchElement(selector, callback) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                callback(mutation, mutation.target);
            });
        });

        const element = document.querySelector(selector);
        if (element) {
            observer.observe(element, {
                attributes: true,
                childList: true,
                subtree: true,
                characterData: true
            });

            this.watchedElements.set(selector, observer);
        }
    }

    // 记录步骤开始
    logStepStart(stepIndex, el, step) {
        const logEntry = {
            type: 'step_start',
            stepIndex,
            timestamp: Date.now(),
            element: this.getElementSnapshot(el),
            step: { ...step },
            domSnapshot: this.getDOMSnapshot(el)
        };

        this.executionLog.push(logEntry);
        this.updateLogUI();
    }

    // 记录步骤成功
    logStepSuccess(stepIndex, result) {
        const logEntry = {
            type: 'step_success',
            stepIndex,
            timestamp: Date.now(),
            result
        };

        this.executionLog.push(logEntry);
        this.updateLogUI();
    }

    // 记录步骤错误
    logStepError(stepIndex, error) {
        const logEntry = {
            type: 'step_error',
            stepIndex,
            timestamp: Date.now(),
            error: {
                message: error.message,
                stack: error.stack
            }
        };

        this.executionLog.push(logEntry);
        this.updateLogUI();
    }

    // 记录触发器检查
    logTriggerCheck(stepIndex, result) {
        const logEntry = {
            type: 'trigger_check',
            stepIndex,
            timestamp: Date.now(),
            found: !!result,
            element: result ? this.getElementSnapshot(result) : null
        };

        this.executionLog.push(logEntry);
    }

    // 获取元素快照
    getElementSnapshot(el) {
        if (!el) return null;

        return {
            tagName: el.tagName,
            id: el.id,
            className: el.className,
            textContent: el.textContent?.substring(0, 100),
            attributes: this.getElementAttributes(el),
            boundingRect: el.getBoundingClientRect(),
            visible: this.isElementVisible(el)
        };
    }

    getElementAttributes(el) {
        const attrs = {};
        for (const attr of el.attributes) {
            attrs[attr.name] = attr.value;
        }
        return attrs;
    }

    isElementVisible(el) {
        const rect = el.getBoundingClientRect();
        return rect.width > 0 && rect.height > 0 &&
               window.getComputedStyle(el).visibility !== 'hidden';
    }

    // 获取DOM快照
    getDOMSnapshot(el) {
        return {
            innerHTML: el.innerHTML.substring(0, 500),
            childElementCount: el.childElementCount,
            parentElement: el.parentElement ? {
                tagName: el.parentElement.tagName,
                className: el.parentElement.className
            } : null
        };
    }

    // 高亮元素
    highlightElement(el) {
        // 移除之前的高亮
        document.querySelectorAll('.macro-debug-highlight').forEach(elem => {
            elem.classList.remove('macro-debug-highlight');
        });

        // 添加高亮样式
        if (el) {
            el.classList.add('macro-debug-highlight');
            el.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    // 显示调试UI
    showDebugUI() {
        const ui = document.createElement('div');
        ui.className = 'macro-debugger-ui';
        ui.innerHTML = `
            <div class="debugger-panel">
                <div class="debugger-header">
                    <h3>Macro Debugger</h3>
                    <button class="btn-close">×</button>
                </div>
                <div class="debugger-content">
                    <div class="debugger-controls">
                        <button class="btn-continue">Continue</button>
                        <button class="btn-step">Step</button>
                        <button class="btn-stop">Stop</button>
                    </div>
                    <div class="debugger-info">
                        <div class="current-step">Step: 0</div>
                        <div class="breakpoints">Breakpoints: 0</div>
                    </div>
                    <div class="debugger-log">
                        <h4>Execution Log</h4>
                        <div class="log-entries"></div>
                    </div>
                </div>
            </div>
        `;

        // 样式
        ui.style.cssText = `
            position: fixed;
            top: 50px;
            left: 50px;
            width: 400px;
            height: 500px;
            z-index: 10001;
            background: white;
            border: 1px solid #ccc;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            font-family: Arial, sans-serif;
            overflow: hidden;
        `;

        // 绑定事件
        ui.querySelector('.btn-continue').onclick = () => this.continue();
        ui.querySelector('.btn-step').onclick = () => this.step();
        ui.querySelector('.btn-stop').onclick = () => this.stop();
        ui.querySelector('.btn-close').onclick = () => this.hideDebugUI();

        document.body.appendChild(ui);
        this.debugUI = ui;
    }

    // 继续执行
    continue() {
        if (this.continueCallback) {
            this.continueCallback();
            this.continueCallback = null;
        }
    }

    // 单步执行
    step() {
        this.addBreakpoint(this.stepIndex + 1);
        this.continue();
    }

    // 停止调试
    stop() {
        this.isDebugging = false;
        this.macroEngine.stop();
        this.hideDebugUI();
    }

    // 更新调试UI
    updateDebugUI(stepIndex, el, step) {
        if (this.debugUI) {
            this.debugUI.querySelector('.current-step').textContent = `Step: ${stepIndex}`;
            // 更新其他UI元素...
        }
    }

    // 更新日志UI
    updateLogUI() {
        if (this.debugUI) {
            const logContainer = this.debugUI.querySelector('.log-entries');
            const lastEntry = this.executionLog[this.executionLog.length - 1];

            const entryElement = document.createElement('div');
            entryElement.className = `log-entry log-${lastEntry.type}`;
            entryElement.textContent = this.formatLogEntry(lastEntry);

            logContainer.appendChild(entryElement);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
    }

    formatLogEntry(entry) {
        const time = new Date(entry.timestamp).toLocaleTimeString();
        switch (entry.type) {
            case 'step_start':
                return `[${time}] Step ${entry.stepIndex}: ${entry.step.action}`;
            case 'step_success':
                return `[${time}] Step ${entry.stepIndex}: Success`;
            case 'step_error':
                return `[${time}] Step ${entry.stepIndex}: Error - ${entry.error.message}`;
            case 'trigger_check':
                return `[${time}] Step ${entry.stepIndex}: Trigger ${entry.found ? 'found' : 'not found'}`;
            default:
                return `[${time}] ${entry.type}`;
        }
    }

    // 导出调试报告
    exportDebugReport() {
        return {
            macro: this.currentMacro,
            executionLog: this.executionLog,
            breakpoints: Array.from(this.breakpoints),
            summary: {
                totalSteps: this.currentMacro?.steps.length || 0,
                executedSteps: this.stepIndex,
                errors: this.executionLog.filter(e => e.type === 'step_error').length,
                duration: this.executionLog.length > 0 ?
                    this.executionLog[this.executionLog.length - 1].timestamp - this.executionLog[0].timestamp : 0
            }
        };
    }
}

// 使用示例
const debugger = new MacroDebugger(macroEngine);

// 开始调试宏
const debugMacro = debugger.startDebugging({
    name: 'Test Macro',
    steps: [
        { trigger: '.button1', action: 'click' },
        { trigger: '.input1', action: 'text', value: 'test' },
        { trigger: '.button2', action: 'click' }
    ]
});

// 添加断点
debugger.addBreakpoint(1);

// 执行调试宏
await macroEngine.activate(debugMacro);
```

## 🔧 调试技巧

### 查看宏引擎状态
```javascript
function debugMacroEngine(engine) {
    console.group('Macro Engine Debug');
    console.log('Running:', engine.isRunning);
    console.log('Active macros:', engine.macros.size);
    console.log('Check delay:', engine.getCheckDelay());

    for (const macro of engine.macros) {
        console.log(`Macro "${macro.name}":`, {
            currentStep: macro.currentIndex,
            totalSteps: macro.steps.length,
            isComplete: macro.isComplete
        });
    }

    console.groupEnd();
}

// 在控制台中调用
debugMacroEngine(macroEngine);
```

### 监控宏执行
```javascript
function monitorMacroExecution(engine) {
    const originalActivate = engine.activate;

    engine.activate = async function(descr, exclusive) {
        console.log('Activating macro:', descr.name);
        console.log('Steps:', descr.steps.length);
        console.log('Exclusive:', exclusive);

        const result = await originalActivate.call(this, descr, exclusive);

        console.log('Macro activated successfully');
        return result;
    };
}

// 启用监控
monitorMacroExecution(macroEngine);
```

## 📊 性能考虑

### 优化策略
1. **检查延迟**: 合理设置检查延迟避免过度轮询
2. **选择器优化**: 使用高效的CSS选择器
3. **事件节流**: 避免频繁的DOM查询
4. **内存管理**: 及时清理完成的宏

### 最佳实践
```javascript
// ✅ 好的做法：使用具体的选择器
{
    trigger: '#specific-button',
    action: 'click'
}

// ❌ 不好的做法：使用模糊的选择器
{
    trigger: 'button',
    action: 'click'
}

// ✅ 好的做法：设置合理的超时
{
    name: 'Quick Task',
    timeout: 5000,
    steps: [...]
}

// ❌ 不好的做法：无限等待
{
    name: 'Risky Task',
    timeout: 0,
    steps: [...]
}
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解宏系统的完整架构和设计思想
- [ ] 掌握宏的创建、执行和调试方法
- [ ] 理解DOM变化监听和事件处理机制
- [ ] 能够创建自动化测试和用户引导系统
- [ ] 掌握宏的错误处理和性能优化
- [ ] 了解高级功能如录制、调试和调度

## 🚀 下一步学习
学完Macro系统后，建议继续学习：
1. **测试框架** (`@web/tests/`) - 深入理解自动化测试
2. **UI工具** (`@web/core/utils/ui.js`) - 学习UI操作工具
3. **并发控制** (`@web/core/utils/concurrency.js`) - 理解异步任务管理

## 💡 重要提示
- Macro系统是自动化和测试的基础设施
- 理解DOM监听对构建响应式宏很重要
- 错误处理和超时管理是宏稳定性的关键
- 合理的延迟设置对性能至关重要
```

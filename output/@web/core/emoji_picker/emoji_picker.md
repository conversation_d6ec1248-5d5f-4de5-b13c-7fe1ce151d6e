# EmojiPicker - 表情符号选择器

## 概述

`emoji_picker.js` 是 Odoo Web 核心模块的表情符号选择器组件，提供了完整的表情符号选择和管理功能。该组件支持表情符号分类浏览、搜索过滤、使用频率记录、键盘导航等功能，具备弹出框显示、移动端适配、本地存储等特性，为用户提供了丰富便捷的表情符号输入体验，广泛应用于聊天、评论、文档编辑等需要表情符号的场景。

## 文件信息
- **路径**: `/web/static/src/core/emoji_picker/emoji_picker.js`
- **行数**: 387
- **模块**: `@web/core/emoji_picker/emoji_picker`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/misc'               // 工具函数
'@odoo/owl'                          // OWL框架
'@web/core/assets'                   // 资源加载
'@web/core/browser/browser'          // 浏览器工具
'@web/core/l10n/translation'         // 国际化
'@web/core/popover/popover_hook'     // 弹出框钩子
'@web/core/utils/search'             // 搜索工具
'@web/core/utils/hooks'              // 工具钩子
'@web/core/browser/feature_detection' // 特性检测
```

## 核心常量

```javascript
const EMOJI_PER_ROW = 9;                    // 每行表情符号数量
const EMOJI_PICKER_PROPS = [                // 组件属性定义
    "close?", "onClose?", "onSelect", 
    "state?", "storeScroll?"
];
```

## 核心钩子函数

### 1. useEmojiPicker

```javascript
function useEmojiPicker(ref, props, options = {}) {
    const targets = [];
    const state = useState({ isOpen: false });
    const popover = usePopover(EmojiPicker, { ...options, animation: false });
    
    // 滚动位置存储
    props.storeScroll = {
        scrollValue: 0,
        set: (value) => { props.storeScroll.scrollValue = value; },
        get: () => { return props.storeScroll.scrollValue; }
    };

    function add(ref, onSelect, { show = false } = {}) {
        const toggler = () => toggle(ref, onSelect);
        targets.push([ref, toggler]);
        // 绑定事件监听器
        if (ref.el) {
            ref.el.addEventListener("click", toggler);
            ref.el.addEventListener("mouseenter", loadEmoji);
        }
    }

    function toggle(ref, onSelect = props.onSelect) {
        if (popover.isOpen) {
            popover.close();
        } else {
            state.isOpen = true;
            popover.open(ref.el, { ...props, onSelect });
        }
    }

    return { ...state, add };
}
```

**钩子功能**:
- **多目标支持**: 支持多个触发元素
- **弹出框管理**: 集成弹出框服务
- **滚动记忆**: 记住滚动位置
- **事件绑定**: 自动绑定点击和悬停事件
- **生命周期**: 完整的组件生命周期管理

### 2. 表情符号加载器

```javascript
const loader = {
    loadEmoji: () => loadBundle("web.assets_emoji"),
    loaded: undefined,
    onEmojiLoaded(cb) {
        loadingListeners.push(cb);
    }
};

async function loadEmoji() {
    const res = { categories: [], emojis: [] };
    try {
        await loader.loadEmoji();
        const { getCategories, getEmojis } = odoo.loader.modules.get(
            "@web/core/emoji_picker/emoji_data"
        );
        res.categories = getCategories();
        res.emojis = getEmojis();
        return res;
    } catch {
        return res;
    } finally {
        if (!loader.loaded) {
            loader.loaded = { emojiValueToShortcode: {} };
            for (const emoji of res.emojis) {
                const value = emoji.codepoints;
                const shortcode = emoji.shortcodes[0];
                loader.loaded.emojiValueToShortcode[value] = shortcode;
            }
        }
    }
}
```

**加载器功能**:
- **异步加载**: 按需加载表情符号数据
- **缓存机制**: 避免重复加载
- **错误处理**: 优雅处理加载失败
- **映射构建**: 构建表情符号到短代码的映射

## 主组件类

### 1. 组件初始化

```javascript
setup() {
    this.gridRef = useRef("emoji-grid");
    this.ui = useState(useService("ui"));
    this.isMobileOS = isMobileOS();
    this.state = useState({
        activeEmojiIndex: 0,
        categoryId: null,
        recent: JSON.parse(browser.localStorage.getItem("web.emoji.frequent") || "{}"),
        searchTerm: "",
    });

    // 本地存储监听
    const onStorage = (ev) => {
        if (ev.key === "web.emoji.frequent") {
            this.state.recent = ev.newValue ? JSON.parse(ev.newValue) : {};
        }
    };
    browser.addEventListener("storage", onStorage);
    
    onWillStart(async () => {
        const { categories, emojis } = await loadEmoji();
        this.categories = categories;
        this.emojis = emojis;
        this.emojiByCodepoints = Object.fromEntries(
            this.emojis.map((emoji) => [emoji.codepoints, emoji])
        );
        this.state.categoryId = this.categories[0]?.sortId;
    });
}
```

**初始化功能**:
- **引用管理**: 获取网格容器引用
- **状态管理**: 管理活动索引、分类、搜索等状态
- **本地存储**: 读取和监听常用表情符号
- **数据加载**: 异步加载表情符号数据
- **索引构建**: 构建快速查找索引

### 2. 搜索和过滤

```javascript
get recentEmojis() {
    const recent = Object.entries(this.state.recent)
        .sort(([, usage_1], [, usage_2]) => usage_2 - usage_1)
        .map(([codepoints]) => this.emojiByCodepoints[codepoints]);
    
    if (this.searchTerm && recent.length > 0) {
        return fuzzyLookup(this.searchTerm, recent, (emoji) =>
            [emoji.name, ...emoji.keywords, ...emoji.emoticons, ...emoji.shortcodes].join(" ")
        );
    }
    return recent.slice(0, 42);
}

getEmojis() {
    let emojisToDisplay = [...this.emojis];
    const recentEmojis = this.recentEmojis;
    
    if (recentEmojis.length > 0 && this.searchTerm) {
        emojisToDisplay = emojisToDisplay.filter((emoji) => !recentEmojis.includes(emoji));
    }
    
    if (this.searchTerm.length > 1) {
        return fuzzyLookup(this.searchTerm, emojisToDisplay, (emoji) =>
            [emoji.name, ...emoji.keywords, ...emoji.emoticons, ...emoji.shortcodes].join(" ")
        );
    }
    return emojisToDisplay;
}
```

**搜索过滤功能**:
- **使用频率排序**: 按使用频率排序常用表情符号
- **模糊搜索**: 支持名称、关键词、表情符号的模糊搜索
- **去重处理**: 搜索时避免常用和全部列表重复
- **多字段匹配**: 支持多个字段的综合搜索

### 3. 键盘导航

```javascript
onKeydown(ev) {
    switch (ev.key) {
        case "ArrowUp": {
            const newIndex = this.state.activeEmojiIndex - EMOJI_PER_ROW;
            if (newIndex >= 0) {
                this.state.activeEmojiIndex = newIndex;
            }
            break;
        }
        case "ArrowDown": {
            const newIndex = this.state.activeEmojiIndex + EMOJI_PER_ROW;
            if (newIndex < this.itemsNumber) {
                this.state.activeEmojiIndex = newIndex;
            }
            break;
        }
        case "ArrowRight": {
            if (this.state.activeEmojiIndex + 1 === this.itemsNumber) {
                break;
            }
            this.state.activeEmojiIndex++;
            ev.preventDefault();
            break;
        }
        case "ArrowLeft": {
            const newIndex = Math.max(this.state.activeEmojiIndex - 1, 0);
            if (newIndex !== this.state.activeEmojiIndex) {
                this.state.activeEmojiIndex = newIndex;
                ev.preventDefault();
            }
            break;
        }
        case "Enter":
            ev.preventDefault();
            this.gridRef.el
                .querySelector(`.o-Emoji[data-index="${this.state.activeEmojiIndex}"]`)
                ?.click();
            break;
        case "Escape":
            this.props.close?.();
            this.props.onClose?.();
            ev.stopPropagation();
    }
}
```

**键盘导航功能**:
- **方向键**: 支持上下左右方向键导航
- **网格导航**: 按网格布局进行导航
- **边界检查**: 防止导航超出边界
- **回车选择**: 回车键选择当前表情符号
- **ESC关闭**: ESC键关闭选择器

### 4. 表情符号选择

```javascript
selectEmoji(ev) {
    const codepoints = ev.currentTarget.dataset.codepoints;
    const resetOnSelect = !ev.shiftKey && !this.ui.isSmall;
    
    // 调用选择回调
    this.props.onSelect(codepoints, resetOnSelect);
    
    // 更新使用频率
    this.state.recent[codepoints] ??= 0;
    this.state.recent[codepoints]++;
    browser.localStorage.setItem("web.emoji.frequent", JSON.stringify(this.state.recent));
    
    // 重置状态
    if (resetOnSelect) {
        this.gridRef.el.scrollTop = 0;
        this.props.close?.();
        this.props.onClose?.();
    }
}
```

**选择功能**:
- **数据提取**: 从DOM元素提取表情符号数据
- **条件重置**: 根据按键和屏幕大小决定是否重置
- **频率记录**: 记录和更新使用频率
- **本地存储**: 持久化存储使用数据
- **状态重置**: 选择后重置滚动位置和关闭选择器

## 使用场景

### 1. 基础表情符号选择器

```javascript
// 基础表情符号选择器使用
class BasicEmojiPicker extends Component {
    setup() {
        this.inputRef = useRef("textInput");
        this.emojiButtonRef = useRef("emojiButton");
        
        this.state = useState({
            text: '',
            cursorPosition: 0
        });

        this.emojiPicker = useEmojiPicker(
            this.emojiButtonRef,
            {
                onSelect: (codepoints, resetOnSelect) => this.insertEmoji(codepoints, resetOnSelect)
            }
        );
    }

    insertEmoji(codepoints, resetOnSelect) {
        const emoji = String.fromCodePoint(...codepoints.split('-').map(cp => parseInt(cp, 16)));
        const input = this.inputRef.el;
        const start = input.selectionStart;
        const end = input.selectionEnd;
        
        const newText = this.state.text.slice(0, start) + emoji + this.state.text.slice(end);
        this.state.text = newText;
        
        // 设置光标位置
        setTimeout(() => {
            const newPosition = start + emoji.length;
            input.setSelectionRange(newPosition, newPosition);
            input.focus();
        }, 0);

        console.log('Emoji inserted:', emoji);
    }

    onTextChange(ev) {
        this.state.text = ev.target.value;
        this.state.cursorPosition = ev.target.selectionStart;
    }

    render() {
        return xml`
            <div class="basic-emoji-picker">
                <h5>基础表情符号选择器</h5>
                
                <div class="input-group mb-3">
                    <textarea 
                        t-ref="textInput"
                        class="form-control"
                        rows="4"
                        placeholder="输入文本，点击表情符号按钮添加表情..."
                        t-model="state.text"
                        t-on-input="onTextChange"
                    />
                    <button 
                        t-ref="emojiButton"
                        class="btn btn-outline-secondary"
                        type="button"
                    >
                        😀
                    </button>
                </div>

                <div class="text-info">
                    <p><strong>当前文本:</strong></p>
                    <div class="border p-2 bg-light">
                        <t t-esc="state.text || '(空)'" />
                    </div>
                </div>
            </div>
        `;
    }
}
```

### 2. 聊天消息表情符号

```javascript
// 聊天消息表情符号组件
class ChatEmojiPicker extends Component {
    setup() {
        this.messageInputRef = useRef("messageInput");
        this.emojiButtonRef = useRef("emojiButton");
        
        this.state = useState({
            messages: [
                { id: 1, text: '你好！👋', sender: 'Alice', time: '10:30' },
                { id: 2, text: '嗨！今天天气真好 ☀️', sender: 'Bob', time: '10:31' },
                { id: 3, text: '是的，很适合出去玩 🎉', sender: 'Alice', time: '10:32' }
            ],
            currentMessage: '',
            isTyping: false
        });

        this.emojiPicker = useEmojiPicker(
            this.emojiButtonRef,
            {
                onSelect: (codepoints, resetOnSelect) => this.addEmojiToMessage(codepoints)
            },
            {
                position: 'top'
            }
        );
    }

    addEmojiToMessage(codepoints) {
        const emoji = String.fromCodePoint(...codepoints.split('-').map(cp => parseInt(cp, 16)));
        const input = this.messageInputRef.el;
        const start = input.selectionStart;
        const end = input.selectionEnd;
        
        const newMessage = this.state.currentMessage.slice(0, start) + emoji + this.state.currentMessage.slice(end);
        this.state.currentMessage = newMessage;
        
        // 恢复焦点和光标位置
        setTimeout(() => {
            const newPosition = start + emoji.length;
            input.setSelectionRange(newPosition, newPosition);
            input.focus();
        }, 0);
    }

    sendMessage() {
        if (!this.state.currentMessage.trim()) return;

        const newMessage = {
            id: Date.now(),
            text: this.state.currentMessage,
            sender: 'You',
            time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
        };

        this.state.messages.push(newMessage);
        this.state.currentMessage = '';
        
        // 滚动到底部
        setTimeout(() => {
            const messagesContainer = this.el.querySelector('.messages-container');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }, 0);
    }

    onKeyPress(ev) {
        if (ev.key === 'Enter' && !ev.shiftKey) {
            ev.preventDefault();
            this.sendMessage();
        }
    }

    render() {
        return xml`
            <div class="chat-emoji-picker">
                <h5>聊天表情符号</h5>
                
                <div class="chat-container border rounded">
                    <div class="messages-container p-3" style="height: 300px; overflow-y: auto;">
                        <t t-foreach="state.messages" t-as="message" t-key="message.id">
                            <div class="message mb-2">
                                <div t-att-class="'d-flex ' + (message.sender === 'You' ? 'justify-content-end' : 'justify-content-start')">
                                    <div t-att-class="'message-bubble p-2 rounded ' + (message.sender === 'You' ? 'bg-primary text-white' : 'bg-light')">
                                        <div class="message-text" t-esc="message.text"/>
                                        <div class="message-meta">
                                            <small t-att-class="message.sender === 'You' ? 'text-white-50' : 'text-muted'">
                                                <t t-esc="message.sender"/> · <t t-esc="message.time"/>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>
                    
                    <div class="message-input border-top p-3">
                        <div class="input-group">
                            <textarea 
                                t-ref="messageInput"
                                class="form-control"
                                rows="2"
                                placeholder="输入消息..."
                                t-model="state.currentMessage"
                                t-on-keypress="onKeyPress"
                            />
                            <button 
                                t-ref="emojiButton"
                                class="btn btn-outline-secondary"
                                type="button"
                                title="添加表情符号"
                            >
                                😀
                            </button>
                            <button 
                                class="btn btn-primary"
                                t-on-click="sendMessage"
                                t-att-disabled="!state.currentMessage.trim()"
                            >
                                发送
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

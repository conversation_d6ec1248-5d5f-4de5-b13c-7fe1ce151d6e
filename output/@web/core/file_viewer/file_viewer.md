# FileViewer - 文件查看器组件

## 概述

`file_viewer.js` 是 Odoo Web 核心模块的文件查看器组件，提供了多媒体文件的预览和查看功能。该组件支持图片、PDF、视频、文本等多种文件类型的查看，具备缩放、旋转、拖拽、导航等交互功能，为用户提供了完整的文件预览体验，广泛应用于附件查看、文档预览、媒体浏览等场景。

## 文件信息
- **路径**: `/web/static/src/core/file_viewer/file_viewer.js`
- **行数**: 256
- **模块**: `@web/core/file_viewer/file_viewer`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                          // OWL框架
'@web/core/utils/hooks'              // 工具钩子
```

## 数据类型定义

### 1. File类型

```javascript
/**
 * @typedef {Object} File
 * @property {string} displayName - 显示名称
 * @property {string} downloadUrl - 下载URL
 * @property {boolean} [isImage] - 是否为图片
 * @property {boolean} [isPdf] - 是否为PDF
 * @property {boolean} [isVideo] - 是否为视频
 * @property {boolean} [isText] - 是否为文本
 * @property {string} [defaultSource] - 默认源URL
 * @property {boolean} [isUrlYoutube] - 是否为YouTube链接
 * @property {string} [mimetype] - MIME类型
 * @property {boolean} [isViewable] - 是否可查看
 */
```

**文件类型功能**:
- **类型识别**: 自动识别文件类型
- **URL管理**: 管理文件的各种URL
- **可查看性**: 判断文件是否支持预览
- **元数据**: 包含文件的元数据信息

### 2. Props类型

```javascript
/**
 * @typedef {Object} Props
 * @property {Array<File>} files - 文件列表
 * @property {number} startIndex - 起始索引
 * @property {function} close - 关闭回调
 * @property {boolean} [modal] - 是否为模态框
 */
```

**属性功能**:
- **文件集合**: 支持多文件查看
- **起始位置**: 指定初始显示的文件
- **关闭控制**: 提供关闭查看器的回调
- **模态模式**: 控制是否以模态框形式显示

## 主组件类

### 1. 组件属性定义

```javascript
static props = ["files", "startIndex", "close?", "modal?"];
static defaultProps = {
    modal: true,
};
```

**属性配置**:
- **必需属性**: files和startIndex为必需属性
- **可选属性**: close和modal为可选属性
- **默认模态**: 默认以模态框形式显示

### 2. 组件初始化

```javascript
setup() {
    useAutofocus();
    this.imageRef = useRef("image");
    this.zoomerRef = useRef("zoomer");

    this.isDragging = false;
    this.dragStartX = 0;
    this.dragStartY = 0;

    this.scrollZoomStep = 0.1;
    this.zoomStep = 0.5;
    this.minScale = 0.5;
    this.translate = {
        dx: 0, dy: 0, x: 0, y: 0,
    };

    this.state = useState({
        index: this.props.startIndex,
        file: this.props.files[this.props.startIndex],
        imageLoaded: false,
        scale: 1,
        angle: 0,
    });
    this.ui = useState(useService("ui"));
}
```

**初始化功能**:
- **自动聚焦**: 组件加载时自动获得焦点
- **引用管理**: 获取图片和缩放容器的引用
- **拖拽状态**: 初始化拖拽相关状态
- **缩放配置**: 设置缩放步长和最小缩放比例
- **平移状态**: 管理图片的平移位置
- **组件状态**: 管理当前文件、缩放、旋转等状态

## 核心功能

### 1. 文件导航

```javascript
// 下一个文件
next() {
    const last = this.props.files.length - 1;
    this.activateFile(this.state.index === last ? 0 : this.state.index + 1);
}

// 上一个文件
previous() {
    const last = this.props.files.length - 1;
    this.activateFile(this.state.index === 0 ? last : this.state.index - 1);
}

// 激活指定文件
activateFile(index) {
    this.state.index = index;
    this.state.file = this.props.files[index];
}
```

**导航功能**:
- **循环导航**: 支持首尾循环的文件导航
- **索引管理**: 自动管理当前文件索引
- **状态更新**: 切换文件时更新相关状态
- **边界处理**: 正确处理列表边界情况

### 2. 键盘控制

```javascript
onKeydown(ev) {
    switch (ev.key) {
        case "ArrowRight":
            this.next();
            break;
        case "ArrowLeft":
            this.previous();
            break;
        case "Escape":
        case "q":
            this.close();
            break;
    }
    if (this.state.file.isImage) {
        switch (ev.key) {
            case "r":
                this.rotate();
                break;
            case "+":
                this.zoomIn();
                break;
            case "-":
                this.zoomOut();
                break;
            case "0":
                this.resetZoom();
                break;
        }
    }
}
```

**键盘功能**:
- **导航控制**: 左右箭头键切换文件
- **退出控制**: ESC和q键关闭查看器
- **图片操作**: 针对图片的特殊键盘操作
- **缩放控制**: +/-键控制缩放，0键重置
- **旋转控制**: r键旋转图片

### 3. 图片缩放和拖拽

```javascript
// 缩放功能
zoomIn({ scroll = false } = {}) {
    this.state.scale = this.state.scale + (scroll ? this.scrollZoomStep : this.zoomStep);
    this.updateZoomerStyle();
}

zoomOut({ scroll = false } = {}) {
    if (this.state.scale === this.minScale) {
        return;
    }
    const unflooredAdaptedScale = this.state.scale - (scroll ? this.scrollZoomStep : this.zoomStep);
    this.state.scale = Math.max(this.minScale, unflooredAdaptedScale);
    this.updateZoomerStyle();
}

// 拖拽功能
onMousedownImage(ev) {
    if (this.isDragging || ev.button !== 0) {
        return;
    }
    this.isDragging = true;
    this.dragStartX = ev.clientX;
    this.dragStartY = ev.clientY;
}

onMousemoveView(ev) {
    if (!this.isDragging) {
        return;
    }
    this.translate.dx = ev.clientX - this.dragStartX;
    this.translate.dy = ev.clientY - this.dragStartY;
    this.updateZoomerStyle();
}
```

**缩放拖拽功能**:
- **多种缩放**: 支持键盘和滚轮缩放
- **缩放限制**: 设置最小缩放比例防止过度缩小
- **拖拽移动**: 支持鼠标拖拽移动图片
- **实时更新**: 拖拽过程中实时更新位置
- **状态管理**: 完整的拖拽状态管理

### 4. 图片旋转和样式

```javascript
// 旋转功能
rotate() {
    this.state.angle += 90;
}

// 图片样式计算
get imageStyle() {
    let style = "transform: " +
        `scale3d(${this.state.scale}, ${this.state.scale}, 1) ` +
        `rotate(${this.state.angle}deg);`;

    if (this.state.angle % 180 !== 0) {
        style += `max-height: ${window.innerWidth}px; max-width: ${window.innerHeight}px;`;
    } else {
        style += "max-height: 100%; max-width: 100%;";
    }
    return style;
}

// 缩放容器样式更新
updateZoomerStyle() {
    const tx = this.imageRef.el.offsetWidth * this.state.scale > this.zoomerRef.el.offsetWidth
        ? this.translate.x + this.translate.dx : 0;
    const ty = this.imageRef.el.offsetHeight * this.state.scale > this.zoomerRef.el.offsetHeight
        ? this.translate.y + this.translate.dy : 0;

    if (tx === 0) this.translate.x = 0;
    if (ty === 0) this.translate.y = 0;

    this.zoomerRef.el.style = "transform: " + `translate(${tx}px, ${ty}px)`;
}
```

**旋转样式功能**:
- **90度旋转**: 每次旋转90度
- **动态样式**: 根据旋转角度动态调整尺寸限制
- **3D变换**: 使用CSS 3D变换提升性能
- **边界检测**: 检测图片是否超出容器边界
- **智能平移**: 只在必要时允许平移

### 5. 打印功能

```javascript
onClickPrint() {
    const printWindow = window.open("about:blank", "_new");
    printWindow.document.open();
    printWindow.document.write(`
        <html>
            <head>
                <script>
                    function onloadImage() {
                        setTimeout('printImage()', 10);
                    }
                    function printImage() {
                        window.print();
                        window.close();
                    }
                </script>
            </head>
            <body onload='onloadImage()'>
                <img src="${this.state.file.defaultSource}" alt=""/>
            </body>
        </html>`);
    printWindow.document.close();
}
```

**打印功能**:
- **新窗口打印**: 在新窗口中打开图片进行打印
- **自动打印**: 图片加载完成后自动触发打印
- **窗口管理**: 打印完成后自动关闭窗口
- **简洁布局**: 提供专门的打印布局

## 使用场景

### 1. 基础文件查看器

```javascript
// 基础文件查看器使用
class BasicFileViewer extends Component {
    setup() {
        this.state = useState({
            files: [
                {
                    displayName: "图片1.jpg",
                    downloadUrl: "/web/content/123",
                    defaultSource: "/web/image/123",
                    isImage: true,
                    isViewable: true,
                    mimetype: "image/jpeg"
                },
                {
                    displayName: "文档.pdf",
                    downloadUrl: "/web/content/124",
                    defaultSource: "/web/content/124",
                    isPdf: true,
                    isViewable: true,
                    mimetype: "application/pdf"
                }
            ],
            showViewer: false,
            currentIndex: 0
        });
    }

    openViewer(index = 0) {
        this.state.currentIndex = index;
        this.state.showViewer = true;
    }

    closeViewer() {
        this.state.showViewer = false;
    }

    render() {
        return xml`
            <div class="basic-file-viewer">
                <h5>文件列表</h5>

                <div class="file-list">
                    <t t-foreach="state.files" t-as="file" t-key="file_index">
                        <div class="file-item card mb-2">
                            <div class="card-body d-flex justify-content-between align-items-center">
                                <div class="file-info">
                                    <h6 class="mb-1" t-esc="file.displayName"/>
                                    <small class="text-muted" t-esc="file.mimetype"/>
                                </div>
                                <button
                                    class="btn btn-primary"
                                    t-on-click="() => this.openViewer(file_index)"
                                    t-if="file.isViewable"
                                >
                                    查看
                                </button>
                            </div>
                        </div>
                    </t>
                </div>

                <FileViewer
                    t-if="state.showViewer"
                    files="state.files"
                    startIndex="state.currentIndex"
                    close="closeViewer"
                    modal="true"
                />
            </div>
        `;
    }
}
```

### 2. 附件查看器

```javascript
// 附件查看器
class AttachmentViewer extends Component {
    setup() {
        this.orm = useService('orm');
        this.state = useState({
            attachments: [],
            viewableFiles: [],
            showViewer: false,
            currentIndex: 0,
            resModel: 'sale.order',
            resId: 1
        });

        this.loadAttachments();
    }

    async loadAttachments() {
        try {
            const attachments = await this.orm.searchRead(
                'ir.attachment',
                [
                    ['res_model', '=', this.state.resModel],
                    ['res_id', '=', this.state.resId]
                ],
                ['name', 'datas_fname', 'mimetype', 'file_size']
            );

            this.state.attachments = attachments;
            this.processViewableFiles(attachments);
        } catch (error) {
            console.error('Failed to load attachments:', error);
        }
    }

    processViewableFiles(attachments) {
        this.state.viewableFiles = attachments
            .filter(att => this.isViewable(att.mimetype))
            .map(att => ({
                displayName: att.datas_fname || att.name,
                downloadUrl: `/web/content/${att.id}?download=true`,
                defaultSource: `/web/content/${att.id}`,
                isImage: att.mimetype.startsWith('image/'),
                isPdf: att.mimetype === 'application/pdf',
                isVideo: att.mimetype.startsWith('video/'),
                isText: att.mimetype.startsWith('text/'),
                isViewable: true,
                mimetype: att.mimetype,
                id: att.id,
                size: att.file_size
            }));
    }

    isViewable(mimetype) {
        return mimetype.startsWith('image/') ||
               mimetype === 'application/pdf' ||
               mimetype.startsWith('video/') ||
               mimetype.startsWith('text/');
    }

    openViewer(fileIndex) {
        this.state.currentIndex = fileIndex;
        this.state.showViewer = true;
    }

    closeViewer() {
        this.state.showViewer = false;
    }

    getFileIcon(mimetype) {
        if (mimetype.startsWith('image/')) return 'fa-file-image-o';
        if (mimetype === 'application/pdf') return 'fa-file-pdf-o';
        if (mimetype.startsWith('video/')) return 'fa-file-video-o';
        if (mimetype.startsWith('text/')) return 'fa-file-text-o';
        return 'fa-file-o';
    }

    formatFileSize(size) {
        if (size < 1024) return size + ' B';
        if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB';
        return (size / (1024 * 1024)).toFixed(1) + ' MB';
    }

    render() {
        return xml`
            <div class="attachment-viewer">
                <div class="header mb-3">
                    <h5>附件查看器</h5>
                    <p class="text-muted">
                        ${this.state.resModel} #${this.state.resId} 的附件
                        (${this.state.viewableFiles.length}/${this.state.attachments.length} 可预览)
                    </p>
                </div>

                <div class="attachments-grid">
                    <div class="row">
                        <t t-foreach="state.viewableFiles" t-as="file" t-key="file.id">
                            <div class="col-md-4 mb-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i t-att-class="'fa ' + getFileIcon(file.mimetype) + ' fa-3x mb-3'"/>
                                        <h6 class="card-title" t-esc="file.displayName"/>
                                        <p class="card-text">
                                            <small class="text-muted">
                                                ${file.mimetype}<br/>
                                                ${this.formatFileSize(file.size)}
                                            </small>
                                        </p>
                                        <button
                                            class="btn btn-primary"
                                            t-on-click="() => this.openViewer(file_index)"
                                        >
                                            <i class="fa fa-eye"/> 预览
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>
                </div>

                <div class="empty-state text-center py-5" t-if="!state.viewableFiles.length">
                    <i class="fa fa-file-o fa-3x text-muted mb-3"/>
                    <h6 class="text-muted">暂无可预览的附件</h6>
                </div>

                <FileViewer
                    t-if="state.showViewer"
                    files="state.viewableFiles"
                    startIndex="state.currentIndex"
                    close="closeViewer"
                />
            </div>
        `;
    }
}
```
# FileModel - 文件模型

## 概述

`file_model.js` 是 Odoo Web 核心模块的文件模型，提供了文件对象的数据模型和业务逻辑。该模块定义了文件的属性、类型判断、URL生成等核心功能，支持图片、PDF、视频、文本、YouTube链接等多种文件类型，为文件查看器和其他文件处理组件提供了统一的数据模型，确保了文件处理的一致性和可扩展性。

## 文件信息
- **路径**: `/web/static/src/core/file_viewer/file_model.js`
- **行数**: 137
- **模块**: `@web/core/file_viewer/file_model`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/urls'               // URL工具函数
```

## 核心类定义

### 1. FileModelMixin

```javascript
const FileModelMixin = T => (class extends T {
    // 文件属性
    access_token;     // 访问令牌
    checksum;         // 文件校验和
    extension;        // 文件扩展名
    filename;         // 文件名
    id;              // 文件ID
    mimetype;        // MIME类型
    name;            // 显示名称
    type;            // 文件类型
    tmpUrl;          // 临时URL
    url;             // 文件URL
    uploading;       // 上传状态
});
```

**Mixin功能**:
- **属性定义**: 定义文件的基本属性
- **类型扩展**: 通过Mixin模式扩展任意基类
- **状态管理**: 管理文件的各种状态
- **元数据**: 包含文件的完整元数据

### 2. FileModel类

```javascript
const FileModel = class FileModel extends FileModelMixin(Object) {}
```

**模型功能**:
- **基础实现**: 基于Object的基础文件模型
- **Mixin应用**: 应用FileModelMixin的所有功能
- **实例化**: 可直接实例化使用

## 核心属性和方法

### 1. 显示相关属性

```javascript
get displayName() {
    return this.name || this.filename;
}

get defaultSource() {
    const route = url(this.urlRoute, this.urlQueryParams);
    const encodedRoute = encodeURIComponent(route);
    
    if (this.isPdf) {
        return `/web/static/lib/pdfjs/web/viewer.html?file=${encodedRoute}#pagemode=none`;
    }
    
    if (this.isUrlYoutube) {
        const urlArr = this.url.split("/");
        let token = urlArr[urlArr.length - 1];
        if (token.includes("watch")) {
            token = token.split("v=")[1];
            const amp = token.indexOf("&");
            if (amp !== -1) {
                token = token.substring(0, amp);
            }
        }
        return `https://www.youtube.com/embed/${token}`;
    }
    
    return route;
}

get downloadUrl() {
    return url(this.urlRoute, { ...this.urlQueryParams, download: true });
}
```

**显示属性功能**:
- **显示名称**: 优先使用name，回退到filename
- **默认源**: 根据文件类型生成合适的显示URL
- **PDF处理**: 使用PDF.js查看器显示PDF文件
- **YouTube处理**: 转换YouTube链接为嵌入格式
- **下载URL**: 生成带下载标识的URL

### 2. 文件类型判断

```javascript
get isImage() {
    const imageMimetypes = [
        "image/bmp", "image/gif", "image/jpeg", "image/png",
        "image/svg+xml", "image/tiff", "image/x-icon", "image/webp"
    ];
    return imageMimetypes.includes(this.mimetype);
}

get isPdf() {
    return this.mimetype && this.mimetype.startsWith("application/pdf");
}

get isText() {
    const textMimeType = [
        "application/javascript", "application/json",
        "text/css", "text/html", "text/plain"
    ];
    return textMimeType.includes(this.mimetype);
}

get isVideo() {
    const videoMimeTypes = ["audio/mpeg", "video/x-matroska", "video/mp4", "video/webm"];
    return videoMimeTypes.includes(this.mimetype);
}

get isUrl() {
    return this.type === "url" && this.url;
}

get isUrlYoutube() {
    return !!this.url && this.url.includes("youtu");
}

get isViewable() {
    return (
        (this.isText || this.isImage || this.isVideo || this.isPdf || this.isUrlYoutube) &&
        !this.uploading
    );
}
```

**类型判断功能**:
- **图片类型**: 支持常见的图片格式
- **PDF类型**: 识别PDF文档
- **文本类型**: 支持多种文本和代码文件
- **视频类型**: 支持常见的视频和音频格式
- **URL类型**: 识别外部链接
- **YouTube**: 特殊处理YouTube链接
- **可查看性**: 综合判断文件是否可预览

### 3. URL生成

```javascript
get urlQueryParams() {
    if (this.uploading && this.tmpUrl) {
        return {};
    }
    
    const params = {
        access_token: this.access_token,
        filename: this.name,
        unique: this.checksum,
    };
    
    // 清理空参数
    for (const prop in params) {
        if (!params[prop]) {
            delete params[prop];
        }
    }
    
    return params;
}

get urlRoute() {
    if (this.isUrl) {
        return this.url;
    }
    
    if (this.uploading && this.tmpUrl) {
        return this.tmpUrl;
    }
    
    return this.isImage ? `/web/image/${this.id}` : `/web/content/${this.id}`;
}
```

**URL生成功能**:
- **查询参数**: 生成URL的查询参数
- **参数清理**: 自动清理空值参数
- **路由选择**: 根据文件类型选择合适的路由
- **临时URL**: 支持上传中的临时URL
- **外部URL**: 处理外部链接

## 使用场景

### 1. 基础文件模型使用

```javascript
// 基础文件模型使用
class FileManager extends Component {
    setup() {
        this.state = useState({
            files: []
        });
        
        this.createSampleFiles();
    }

    createSampleFiles() {
        // 创建不同类型的文件模型
        const imageFile = new FileModel();
        Object.assign(imageFile, {
            id: 1,
            name: "示例图片.jpg",
            filename: "sample.jpg",
            mimetype: "image/jpeg",
            checksum: "abc123",
            access_token: "token123"
        });

        const pdfFile = new FileModel();
        Object.assign(pdfFile, {
            id: 2,
            name: "文档.pdf",
            filename: "document.pdf",
            mimetype: "application/pdf",
            checksum: "def456"
        });

        const videoFile = new FileModel();
        Object.assign(videoFile, {
            id: 3,
            name: "视频.mp4",
            filename: "video.mp4",
            mimetype: "video/mp4",
            checksum: "ghi789"
        });

        const youtubeFile = new FileModel();
        Object.assign(youtubeFile, {
            type: "url",
            url: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            name: "YouTube视频"
        });

        this.state.files = [imageFile, pdfFile, videoFile, youtubeFile];
    }

    getFileTypeIcon(file) {
        if (file.isImage) return 'fa-file-image-o';
        if (file.isPdf) return 'fa-file-pdf-o';
        if (file.isVideo) return 'fa-file-video-o';
        if (file.isText) return 'fa-file-text-o';
        if (file.isUrlYoutube) return 'fa-youtube-play';
        return 'fa-file-o';
    }

    getFileTypeLabel(file) {
        if (file.isImage) return '图片';
        if (file.isPdf) return 'PDF';
        if (file.isVideo) return '视频';
        if (file.isText) return '文本';
        if (file.isUrlYoutube) return 'YouTube';
        return '文件';
    }

    openFile(file) {
        if (file.isViewable) {
            window.open(file.defaultSource, '_blank');
        } else {
            this.notification.add('此文件类型不支持预览', { type: 'info' });
        }
    }

    downloadFile(file) {
        if (file.downloadUrl) {
            window.open(file.downloadUrl, '_blank');
        }
    }

    render() {
        return xml`
            <div class="file-manager">
                <h5>文件管理器</h5>
                
                <div class="file-list">
                    <t t-foreach="state.files" t-as="file" t-key="file_index">
                        <div class="file-item card mb-3">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-1 text-center">
                                        <i t-att-class="'fa ' + getFileTypeIcon(file) + ' fa-2x'"/>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="mb-1" t-esc="file.displayName"/>
                                        <p class="mb-0">
                                            <span class="badge bg-secondary me-2" t-esc="getFileTypeLabel(file)"/>
                                            <small class="text-muted" t-esc="file.mimetype"/>
                                        </p>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="file-info">
                                            <p class="mb-0">
                                                <strong>ID:</strong> <t t-esc="file.id || 'N/A'"/>
                                            </p>
                                            <p class="mb-0">
                                                <strong>可预览:</strong> 
                                                <span t-att-class="file.isViewable ? 'text-success' : 'text-danger'">
                                                    <t t-esc="file.isViewable ? '是' : '否'"/>
                                                </span>
                                            </p>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="btn-group">
                                            <button 
                                                class="btn btn-sm btn-primary"
                                                t-on-click="() => this.openFile(file)"
                                                t-att-disabled="!file.isViewable"
                                            >
                                                <i class="fa fa-eye"/> 预览
                                            </button>
                                            <button 
                                                class="btn btn-sm btn-secondary"
                                                t-on-click="() => this.downloadFile(file)"
                                                t-if="file.downloadUrl"
                                            >
                                                <i class="fa fa-download"/> 下载
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </div>
            </div>
        `;
    }
}
```

### 2. 文件类型分析器

```javascript
// 文件类型分析器
class FileTypeAnalyzer extends Component {
    setup() {
        this.state = useState({
            files: [],
            analysis: {
                total: 0,
                byType: {},
                viewable: 0,
                nonViewable: 0
            }
        });
    }

    analyzeFiles(files) {
        const analysis = {
            total: files.length,
            byType: {},
            viewable: 0,
            nonViewable: 0
        };

        files.forEach(file => {
            // 按类型分组
            const types = [];
            if (file.isImage) types.push('image');
            if (file.isPdf) types.push('pdf');
            if (file.isVideo) types.push('video');
            if (file.isText) types.push('text');
            if (file.isUrlYoutube) types.push('youtube');
            if (types.length === 0) types.push('other');

            types.forEach(type => {
                analysis.byType[type] = (analysis.byType[type] || 0) + 1;
            });

            // 可查看性统计
            if (file.isViewable) {
                analysis.viewable++;
            } else {
                analysis.nonViewable++;
            }
        });

        this.state.analysis = analysis;
    }

    addTestFiles() {
        const testFiles = [
            { mimetype: 'image/jpeg', name: 'photo1.jpg' },
            { mimetype: 'image/png', name: 'photo2.png' },
            { mimetype: 'application/pdf', name: 'document.pdf' },
            { mimetype: 'video/mp4', name: 'video.mp4' },
            { mimetype: 'text/plain', name: 'readme.txt' },
            { mimetype: 'application/zip', name: 'archive.zip' },
            { type: 'url', url: 'https://youtube.com/watch?v=123', name: 'YouTube Video' }
        ];

        const files = testFiles.map((data, index) => {
            const file = new FileModel();
            Object.assign(file, { id: index + 1, ...data });
            return file;
        });

        this.state.files = files;
        this.analyzeFiles(files);
    }

    getTypeColor(type) {
        const colors = {
            image: 'primary',
            pdf: 'danger',
            video: 'success',
            text: 'info',
            youtube: 'warning',
            other: 'secondary'
        };
        return colors[type] || 'secondary';
    }

    getTypeIcon(type) {
        const icons = {
            image: 'fa-file-image-o',
            pdf: 'fa-file-pdf-o',
            video: 'fa-file-video-o',
            text: 'fa-file-text-o',
            youtube: 'fa-youtube-play',
            other: 'fa-file-o'
        };
        return icons[type] || 'fa-file-o';
    }

    render() {
        const { analysis } = this.state;
        
        return xml`
            <div class="file-type-analyzer">
                <div class="header mb-4">
                    <h5>文件类型分析器</h5>
                    <button 
                        class="btn btn-primary"
                        t-on-click="addTestFiles"
                    >
                        添加测试文件
                    </button>
                </div>

                <div class="analysis-summary" t-if="analysis.total > 0">
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h3 class="card-title text-primary" t-esc="analysis.total"/>
                                    <p class="card-text">总文件数</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h3 class="card-title text-success" t-esc="analysis.viewable"/>
                                    <p class="card-text">可预览</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h3 class="card-title text-danger" t-esc="analysis.nonViewable"/>
                                    <p class="card-text">不可预览</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h3 class="card-title text-info" t-esc="Object.keys(analysis.byType).length"/>
                                    <p class="card-text">文件类型</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="type-breakdown">
                        <h6>按类型分布</h6>
                        <div class="row">
                            <t t-foreach="Object.entries(analysis.byType)" t-as="entry" t-key="entry[0]">
                                <div class="col-md-4 mb-3">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <i t-att-class="'fa ' + getTypeIcon(entry[0]) + ' fa-2x mb-2'"/>
                                            <h5 t-esc="entry[1]"/>
                                            <p class="mb-0">
                                                <span 
                                                    class="badge"
                                                    t-att-class="'bg-' + getTypeColor(entry[0])"
                                                    t-esc="entry[0]"
                                                />
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </t>
                        </div>
                    </div>
                </div>

                <div class="empty-state text-center py-5" t-if="analysis.total === 0">
                    <i class="fa fa-file-o fa-3x text-muted mb-3"/>
                    <h6 class="text-muted">暂无文件</h6>
                    <p class="text-muted">点击上方按钮添加测试文件</p>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. Mixin设计模式
- 灵活的类扩展机制
- 可重用的功能模块
- 支持多重继承

### 2. 类型系统
- 完整的文件类型识别
- 基于MIME类型的判断
- 特殊类型的处理

### 3. URL管理
- 智能的URL生成
- 参数化的查询字符串
- 多种URL类型支持

### 4. 状态感知
- 上传状态的处理
- 临时URL的支持
- 动态属性计算

## 设计模式

### 1. Mixin模式 (Mixin Pattern)
- 功能的模块化组合
- 避免深层继承链

### 2. 策略模式 (Strategy Pattern)
- 不同文件类型的处理策略
- 可扩展的类型系统

### 3. 工厂模式 (Factory Pattern)
- 统一的文件模型创建
- 类型化的对象构建

## 注意事项

1. **类型安全**: 确保MIME类型的正确识别
2. **URL安全**: 正确编码URL参数
3. **性能考虑**: 避免重复的类型判断
4. **扩展性**: 支持新文件类型的添加

## 扩展建议

1. **缓存机制**: 添加计算属性的缓存
2. **验证系统**: 文件属性的验证机制
3. **转换器**: 文件格式的转换支持
4. **元数据**: 更丰富的文件元数据
5. **权限控制**: 基于权限的访问控制

该文件模型为Odoo Web应用提供了完整的文件数据抽象，通过统一的接口和智能的类型识别确保了文件处理的一致性和可扩展性。

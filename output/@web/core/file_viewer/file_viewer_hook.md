# FileViewerHook - 文件查看器钩子

## 概述

`file_viewer_hook.js` 是 Odoo Web 核心模块的文件查看器钩子，提供了便捷的文件查看器管理功能。该钩子封装了文件查看器的创建、打开、关闭等操作，支持单文件和多文件查看，自动过滤可查看的文件，并提供了组件生命周期管理，为开发者提供了简单易用的文件预览功能集成方式。

## 文件信息
- **路径**: `/web/static/src/core/file_viewer/file_viewer_hook.js`
- **行数**: 43
- **模块**: `@web/core/file_viewer/file_viewer_hook`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                      // OWL框架
'@web/core/registry'                             // 注册表系统
'@web/core/file_viewer/file_viewer'              // 文件查看器组件
```

## 核心函数

### 1. createFileViewer

```javascript
function createFileViewer() {
    const fileViewerId = `web.file_viewer${id++}`;
    
    function open(file, files = [file]) {
        if (!file.isViewable) {
            return;
        }
        if (files.length > 0) {
            const viewableFiles = files.filter((file) => file.isViewable);
            const index = viewableFiles.indexOf(file);
            registry.category("main_components").add(fileViewerId, {
                Component: FileViewer,
                props: { files: viewableFiles, startIndex: index, close },
            });
        }
    }

    function close() {
        registry.category("main_components").remove(fileViewerId);
    }
    
    return { open, close };
}
```

**创建器功能**:
- **唯一ID**: 为每个文件查看器生成唯一标识
- **文件过滤**: 自动过滤出可查看的文件
- **索引计算**: 计算当前文件在可查看文件列表中的索引
- **组件注册**: 动态注册文件查看器组件
- **关闭管理**: 提供关闭文件查看器的方法

**参数说明**:
- **file**: 要查看的目标文件
- **files**: 文件列表，默认为包含目标文件的数组
- **返回值**: 包含open和close方法的对象

### 2. useFileViewer

```javascript
function useFileViewer() {
    const { open, close } = createFileViewer();
    onWillDestroy(close);
    return { open, close };
}
```

**钩子功能**:
- **生命周期**: 集成OWL组件生命周期
- **自动清理**: 组件销毁时自动关闭文件查看器
- **简化接口**: 提供简化的钩子接口
- **内存管理**: 防止内存泄漏

## 使用场景

### 1. 基础文件查看

```javascript
// 基础文件查看使用
class FileList extends Component {
    setup() {
        this.fileViewer = useFileViewer();
        this.state = useState({
            files: [
                {
                    id: 1,
                    name: "图片1.jpg",
                    mimetype: "image/jpeg",
                    isViewable: true,
                    defaultSource: "/web/image/1"
                },
                {
                    id: 2,
                    name: "文档.pdf",
                    mimetype: "application/pdf",
                    isViewable: true,
                    defaultSource: "/web/content/2"
                },
                {
                    id: 3,
                    name: "压缩包.zip",
                    mimetype: "application/zip",
                    isViewable: false
                }
            ]
        });
    }

    viewFile(file) {
        // 查看单个文件
        this.fileViewer.open(file);
    }

    viewFileInContext(file) {
        // 在文件列表上下文中查看文件
        this.fileViewer.open(file, this.state.files);
    }

    render() {
        return xml`
            <div class="file-list">
                <h5>文件列表</h5>
                
                <div class="files">
                    <t t-foreach="state.files" t-as="file" t-key="file.id">
                        <div class="file-item card mb-2">
                            <div class="card-body d-flex justify-content-between align-items-center">
                                <div class="file-info">
                                    <h6 class="mb-1" t-esc="file.name"/>
                                    <small class="text-muted" t-esc="file.mimetype"/>
                                </div>
                                <div class="file-actions">
                                    <button 
                                        class="btn btn-sm btn-primary me-2"
                                        t-on-click="() => this.viewFile(file)"
                                        t-if="file.isViewable"
                                    >
                                        单独查看
                                    </button>
                                    <button 
                                        class="btn btn-sm btn-outline-primary"
                                        t-on-click="() => this.viewFileInContext(file)"
                                        t-if="file.isViewable"
                                    >
                                        列表查看
                                    </button>
                                    <span 
                                        class="badge bg-secondary"
                                        t-if="!file.isViewable"
                                    >
                                        不可预览
                                    </span>
                                </div>
                            </div>
                        </div>
                    </t>
                </div>
            </div>
        `;
    }
}
```

### 2. 图片画廊

```javascript
// 图片画廊组件
class ImageGallery extends Component {
    setup() {
        this.fileViewer = useFileViewer();
        this.state = useState({
            images: [],
            loading: false
        });

        this.loadImages();
    }

    async loadImages() {
        this.state.loading = true;
        try {
            // 模拟加载图片数据
            const imageData = [
                { id: 1, name: "风景1.jpg", mimetype: "image/jpeg" },
                { id: 2, name: "风景2.png", mimetype: "image/png" },
                { id: 3, name: "风景3.gif", mimetype: "image/gif" },
                { id: 4, name: "风景4.webp", mimetype: "image/webp" }
            ];

            this.state.images = imageData.map(img => ({
                ...img,
                isViewable: true,
                isImage: true,
                defaultSource: `/web/image/${img.id}`,
                downloadUrl: `/web/content/${img.id}?download=true`,
                displayName: img.name
            }));
        } catch (error) {
            console.error('Failed to load images:', error);
        } finally {
            this.state.loading = false;
        }
    }

    openGallery(startImage) {
        // 在画廊模式下打开图片
        this.fileViewer.open(startImage, this.state.images);
    }

    render() {
        return xml`
            <div class="image-gallery">
                <h5>图片画廊</h5>
                
                <div class="loading text-center py-4" t-if="state.loading">
                    <i class="fa fa-spinner fa-spin"/>
                    <p class="mt-2">加载中...</p>
                </div>

                <div class="gallery-grid" t-if="!state.loading">
                    <div class="row">
                        <t t-foreach="state.images" t-as="image" t-key="image.id">
                            <div class="col-md-3 mb-3">
                                <div class="card">
                                    <div 
                                        class="image-thumbnail"
                                        style="height: 200px; overflow: hidden; cursor: pointer;"
                                        t-on-click="() => this.openGallery(image)"
                                    >
                                        <img 
                                            t-att-src="image.defaultSource"
                                            class="card-img-top"
                                            style="width: 100%; height: 100%; object-fit: cover;"
                                            t-att-alt="image.name"
                                        />
                                    </div>
                                    <div class="card-body p-2">
                                        <h6 class="card-title small mb-1" t-esc="image.name"/>
                                        <button 
                                            class="btn btn-sm btn-primary w-100"
                                            t-on-click="() => this.openGallery(image)"
                                        >
                                            <i class="fa fa-eye"/> 查看
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>
                </div>

                <div class="empty-state text-center py-5" t-if="!state.loading && !state.images.length">
                    <i class="fa fa-image fa-3x text-muted mb-3"/>
                    <h6 class="text-muted">暂无图片</h6>
                </div>
            </div>
        `;
    }
}
```

### 3. 文档管理器

```javascript
// 文档管理器
class DocumentManager extends Component {
    setup() {
        this.fileViewer = useFileViewer();
        this.orm = useService('orm');
        this.notification = useService('notification');
        
        this.state = useState({
            documents: [],
            selectedCategory: 'all',
            categories: ['all', 'images', 'pdfs', 'videos', 'texts'],
            searchTerm: ''
        });

        this.loadDocuments();
    }

    async loadDocuments() {
        try {
            // 模拟加载文档数据
            const docData = [
                { id: 1, name: "报告.pdf", mimetype: "application/pdf", category: "pdfs" },
                { id: 2, name: "图表.jpg", mimetype: "image/jpeg", category: "images" },
                { id: 3, name: "演示.mp4", mimetype: "video/mp4", category: "videos" },
                { id: 4, name: "说明.txt", mimetype: "text/plain", category: "texts" },
                { id: 5, name: "数据.json", mimetype: "application/json", category: "texts" }
            ];

            this.state.documents = docData.map(doc => ({
                ...doc,
                isViewable: this.isViewable(doc.mimetype),
                isImage: doc.mimetype.startsWith('image/'),
                isPdf: doc.mimetype === 'application/pdf',
                isVideo: doc.mimetype.startsWith('video/'),
                isText: doc.mimetype.startsWith('text/') || doc.mimetype.includes('json'),
                defaultSource: `/web/content/${doc.id}`,
                downloadUrl: `/web/content/${doc.id}?download=true`,
                displayName: doc.name
            }));
        } catch (error) {
            this.notification.add('加载文档失败', { type: 'danger' });
        }
    }

    isViewable(mimetype) {
        return mimetype.startsWith('image/') ||
               mimetype === 'application/pdf' ||
               mimetype.startsWith('video/') ||
               mimetype.startsWith('text/') ||
               mimetype.includes('json');
    }

    get filteredDocuments() {
        let docs = this.state.documents;

        // 按分类过滤
        if (this.state.selectedCategory !== 'all') {
            docs = docs.filter(doc => doc.category === this.state.selectedCategory);
        }

        // 按搜索词过滤
        if (this.state.searchTerm) {
            const term = this.state.searchTerm.toLowerCase();
            docs = docs.filter(doc => 
                doc.name.toLowerCase().includes(term) ||
                doc.mimetype.toLowerCase().includes(term)
            );
        }

        return docs;
    }

    viewDocument(doc) {
        if (!doc.isViewable) {
            this.notification.add('此文档类型不支持预览', { type: 'info' });
            return;
        }

        // 在当前过滤结果中查看文档
        const viewableDocs = this.filteredDocuments.filter(d => d.isViewable);
        this.fileViewer.open(doc, viewableDocs);
    }

    getCategoryLabel(category) {
        const labels = {
            all: '全部',
            images: '图片',
            pdfs: 'PDF',
            videos: '视频',
            texts: '文本'
        };
        return labels[category] || category;
    }

    getCategoryCount(category) {
        if (category === 'all') {
            return this.state.documents.length;
        }
        return this.state.documents.filter(doc => doc.category === category).length;
    }

    render() {
        const filteredDocs = this.filteredDocuments;

        return xml`
            <div class="document-manager">
                <div class="header mb-4">
                    <h5>文档管理器</h5>
                    
                    <div class="filters mt-3">
                        <div class="row">
                            <div class="col-md-6">
                                <input 
                                    type="text" 
                                    class="form-control"
                                    placeholder="搜索文档..."
                                    t-model="state.searchTerm"
                                />
                            </div>
                            <div class="col-md-6">
                                <select class="form-select" t-model="state.selectedCategory">
                                    <t t-foreach="state.categories" t-as="category" t-key="category">
                                        <option t-att-value="category">
                                            ${this.getCategoryLabel(category)} (${this.getCategoryCount(category)})
                                        </option>
                                    </t>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="documents-list">
                    <div class="row">
                        <t t-foreach="filteredDocs" t-as="doc" t-key="doc.id">
                            <div class="col-md-4 mb-3">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fa fa-file-o fa-2x me-3"/>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1" t-esc="doc.name"/>
                                                <small class="text-muted" t-esc="doc.mimetype"/>
                                            </div>
                                        </div>
                                        
                                        <div class="document-meta mb-3">
                                            <span 
                                                class="badge me-2"
                                                t-att-class="doc.isViewable ? 'bg-success' : 'bg-secondary'"
                                            >
                                                <t t-esc="doc.isViewable ? '可预览' : '不可预览'"/>
                                            </span>
                                            <span class="badge bg-info" t-esc="getCategoryLabel(doc.category)"/>
                                        </div>

                                        <button 
                                            class="btn btn-primary w-100"
                                            t-on-click="() => this.viewDocument(doc)"
                                            t-att-disabled="!doc.isViewable"
                                        >
                                            <i class="fa fa-eye"/> 查看文档
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>
                </div>

                <div class="empty-state text-center py-5" t-if="!filteredDocs.length">
                    <i class="fa fa-file-o fa-3x text-muted mb-3"/>
                    <h6 class="text-muted">没有找到匹配的文档</h6>
                    <p class="text-muted">尝试调整搜索条件或分类筛选</p>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 钩子模式
- 简化的API接口
- 自动生命周期管理
- 可重用的逻辑封装

### 2. 智能过滤
- 自动过滤可查看文件
- 索引自动计算
- 空列表处理

### 3. 组件注册
- 动态组件管理
- 唯一ID生成
- 内存泄漏防护

### 4. 灵活使用
- 单文件查看
- 多文件上下文查看
- 自定义文件列表

## 设计模式

### 1. 钩子模式 (Hook Pattern)
- 可重用的状态逻辑
- 组件间的功能共享

### 2. 工厂模式 (Factory Pattern)
- 文件查看器的创建
- 统一的创建接口

### 3. 策略模式 (Strategy Pattern)
- 不同查看模式的处理
- 灵活的文件过滤策略

## 注意事项

1. **内存管理**: 确保组件销毁时正确清理
2. **文件验证**: 检查文件的可查看性
3. **错误处理**: 处理文件加载失败的情况
4. **性能优化**: 避免重复的文件过滤操作

## 扩展建议

1. **缓存机制**: 文件查看器的缓存复用
2. **预加载**: 相邻文件的预加载
3. **快捷键**: 键盘快捷键支持
4. **全屏模式**: 全屏查看模式
5. **分享功能**: 文件分享和链接生成

该文件查看器钩子为Odoo Web应用提供了便捷的文件预览功能集成方式，通过简化的API和自动化的生命周期管理确保了良好的开发体验和用户体验。

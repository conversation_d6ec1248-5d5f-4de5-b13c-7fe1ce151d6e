# FileInput - 文件输入组件

## 概述

`file_input.js` 是 Odoo Web 核心模块的自定义文件输入组件，提供了增强的文件上传功能。该组件封装了原生的文件输入元素，支持自定义触发器、多文件上传、文件类型限制和上传状态管理，为用户提供了更好的文件上传体验，广泛应用于附件上传、文档管理、图片上传等需要文件输入的场景。

## 文件信息
- **路径**: `/web/static/src/core/file_input/file_input.js`
- **行数**: 116
- **模块**: `@web/core/file_input/file_input`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                          // OWL框架
'@web/core/utils/files'              // 文件工具函数
```

## 主组件类

### 1. 组件属性定义

```javascript
static props = {
    acceptedFileExtensions: { type: String, optional: true },  // 接受的文件扩展名
    autoOpen: { type: Boolean, optional: true },               // 自动打开文件选择器
    hidden: { type: Boolean, optional: true },                 // 是否隐藏
    multiUpload: { type: Boolean, optional: true },            // 多文件上传
    onUpload: { type: Function, optional: true },              // 上传回调
    beforeOpen: { type: Function, optional: true },            // 打开前回调
    resId: { type: Number, optional: true },                   // 资源ID
    resModel: { type: String, optional: true },                // 资源模型
    route: { type: String, optional: true },                   // 上传路由
    "*": true,                                                  // 允许其他属性
};

static defaultProps = {
    acceptedFileExtensions: "*",
    hidden: false,
    multiUpload: false,
    onUpload: () => {},
    route: "/web/binary/upload_attachment",
    beforeOpen: async () => true,
};
```

**属性功能**:
- **文件限制**: acceptedFileExtensions限制可上传的文件类型
- **上传模式**: multiUpload控制单文件或多文件上传
- **回调函数**: onUpload和beforeOpen提供上传生命周期控制
- **资源绑定**: resId和resModel绑定到特定资源
- **路由配置**: route指定上传的服务端路由

### 2. 组件初始化

```javascript
setup() {
    this.uploadFiles = useFileUploader();
    this.fileInputRef = useRef("file-input");
    this.state = useState({
        // Disables upload button if currently uploading.
        isDisable: false,
    });

    onMounted(() => {
        if (this.props.autoOpen) {
            this.onTriggerClicked();
        }
    });
}
```

**初始化功能**:
- **文件上传器**: 使用文件上传工具钩子
- **引用管理**: 获取文件输入元素的引用
- **状态管理**: 管理上传禁用状态
- **自动打开**: 支持组件挂载时自动打开文件选择器

### 3. HTTP参数生成

```javascript
get httpParams() {
    const { resId, resModel } = this.props;
    const params = {
        csrf_token: odoo.csrf_token,
        ufile: [...this.fileInputRef.el.files],
    };
    if (resModel) {
        params.model = resModel;
    }
    if (resId !== undefined) {
        params.id = resId;
    }
    return params;
}
```

**参数生成功能**:
- **CSRF保护**: 自动添加CSRF令牌
- **文件数据**: 包含选中的文件列表
- **资源绑定**: 可选的模型和ID绑定
- **动态参数**: 根据属性动态生成参数

## 核心方法

### 1. 文件上传处理

```javascript
async onFileInputChange() {
    this.state.isDisable = true;
    const parsedFileData = await this.uploadFiles(this.props.route, this.httpParams);
    if (parsedFileData) {
        // When calling onUpload, also pass the files to allow to get data like their names
        this.props.onUpload(
            parsedFileData,
            this.fileInputRef.el ? this.fileInputRef.el.files : []
        );
        // Because the input would not trigger this method if the same file name is uploaded,
        // we must clear the value after handling the upload
        this.fileInputRef.el.value = null;
    }
    this.state.isDisable = false;
}
```

**上传处理功能**:
- **状态控制**: 上传期间禁用组件
- **文件上传**: 调用文件上传工具进行上传
- **回调通知**: 上传成功后调用回调函数
- **状态重置**: 清空文件输入值以支持重复上传
- **错误处理**: 上传失败时恢复组件状态

### 2. 触发器点击处理

```javascript
async onTriggerClicked() {
    if (await this.props.beforeOpen()) {
        this.fileInputRef.el.click();
    }
}
```

**触发器功能**:
- **前置检查**: 调用beforeOpen进行前置验证
- **文件选择**: 触发原生文件选择对话框
- **异步支持**: 支持异步的前置检查

## 使用场景

### 1. 基础文件上传

```javascript
// 基础文件上传使用
class BasicFileUpload extends Component {
    setup() {
        this.state = useState({
            uploadedFiles: [],
            isUploading: false
        });
    }

    onFileUpload(parsedFileData, files) {
        console.log('Files uploaded:', parsedFileData);
        console.log('Original files:', files);
        
        // 处理上传的文件数据
        this.state.uploadedFiles = [...this.state.uploadedFiles, ...parsedFileData];
        this.notification.add(`成功上传 ${files.length} 个文件`, { type: 'success' });
    }

    async beforeFileOpen() {
        // 上传前的验证
        if (this.state.uploadedFiles.length >= 10) {
            this.notification.add('最多只能上传10个文件', { type: 'warning' });
            return false;
        }
        return true;
    }

    render() {
        return xml`
            <div class="basic-file-upload">
                <h5>文件上传</h5>
                
                <FileInput
                    acceptedFileExtensions=".jpg,.png,.pdf,.doc,.docx"
                    multiUpload="true"
                    onUpload="onFileUpload"
                    beforeOpen="beforeFileOpen"
                >
                    <button class="btn btn-primary">
                        <i class="fa fa-upload"/> 选择文件
                    </button>
                </FileInput>

                <div class="uploaded-files mt-3" t-if="state.uploadedFiles.length">
                    <h6>已上传的文件 (${state.uploadedFiles.length})</h6>
                    <ul class="list-group">
                        <t t-foreach="state.uploadedFiles" t-as="file" t-key="file.id">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span t-esc="file.name"/>
                                <span class="badge bg-primary" t-esc="(file.size / 1024).toFixed(1) + ' KB'"/>
                            </li>
                        </t>
                    </ul>
                </div>
            </div>
        `;
    }
}
```

### 2. 附件管理器

```javascript
// 附件管理器
class AttachmentManager extends Component {
    setup() {
        this.orm = useService('orm');
        this.notification = useService('notification');
        
        this.state = useState({
            attachments: [],
            resModel: 'res.partner',
            resId: 1,
            isLoading: false,
            uploadProgress: {}
        });

        this.loadAttachments();
    }

    async loadAttachments() {
        this.state.isLoading = true;
        try {
            const attachments = await this.orm.searchRead(
                'ir.attachment',
                [
                    ['res_model', '=', this.state.resModel],
                    ['res_id', '=', this.state.resId]
                ],
                ['name', 'datas_fname', 'file_size', 'mimetype', 'create_date']
            );
            this.state.attachments = attachments;
        } catch (error) {
            this.notification.add('加载附件失败', { type: 'danger' });
        } finally {
            this.state.isLoading = false;
        }
    }

    async onAttachmentUpload(parsedFileData, files) {
        console.log('Attachments uploaded:', parsedFileData);
        
        // 刷新附件列表
        await this.loadAttachments();
        
        this.notification.add(
            `成功上传 ${files.length} 个附件`,
            { type: 'success' }
        );
    }

    async beforeAttachmentOpen() {
        // 检查权限
        if (!this.state.resId) {
            this.notification.add('请先选择记录', { type: 'warning' });
            return false;
        }

        // 检查附件数量限制
        if (this.state.attachments.length >= 20) {
            this.notification.add('每个记录最多只能有20个附件', { type: 'warning' });
            return false;
        }

        return true;
    }

    async deleteAttachment(attachmentId) {
        if (!confirm('确定要删除这个附件吗？')) return;

        try {
            await this.orm.unlink('ir.attachment', [attachmentId]);
            await this.loadAttachments();
            this.notification.add('附件删除成功', { type: 'success' });
        } catch (error) {
            this.notification.add('删除附件失败', { type: 'danger' });
        }
    }

    downloadAttachment(attachment) {
        const url = `/web/content/${attachment.id}?download=true`;
        window.open(url, '_blank');
    }

    getFileIcon(mimetype) {
        if (mimetype.startsWith('image/')) return 'fa-file-image-o';
        if (mimetype.includes('pdf')) return 'fa-file-pdf-o';
        if (mimetype.includes('word')) return 'fa-file-word-o';
        if (mimetype.includes('excel')) return 'fa-file-excel-o';
        return 'fa-file-o';
    }

    formatFileSize(size) {
        if (size < 1024) return size + ' B';
        if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB';
        return (size / (1024 * 1024)).toFixed(1) + ' MB';
    }

    render() {
        return xml`
            <div class="attachment-manager">
                <div class="header mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>附件管理器</h5>
                        <div class="record-info">
                            <small class="text-muted">
                                模型: ${this.state.resModel} | ID: ${this.state.resId}
                            </small>
                        </div>
                    </div>
                </div>

                <div class="upload-section mb-4">
                    <FileInput
                        acceptedFileExtensions="*"
                        multiUpload="true"
                        onUpload="onAttachmentUpload"
                        beforeOpen="beforeAttachmentOpen"
                        resModel="state.resModel"
                        resId="state.resId"
                    >
                        <div class="upload-area text-center p-4 border border-dashed rounded">
                            <i class="fa fa-cloud-upload fa-3x text-muted mb-3"/>
                            <h6>点击上传附件</h6>
                            <p class="text-muted mb-0">支持多文件上传</p>
                        </div>
                    </FileInput>
                </div>

                <div class="attachments-section">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6>附件列表 (${this.state.attachments.length})</h6>
                        <button 
                            class="btn btn-sm btn-outline-secondary"
                            t-on-click="loadAttachments"
                            t-att-disabled="state.isLoading"
                        >
                            <i class="fa fa-refresh"/> 刷新
                        </button>
                    </div>

                    <div class="loading text-center py-4" t-if="state.isLoading">
                        <i class="fa fa-spinner fa-spin"/>
                        <p class="mt-2">加载中...</p>
                    </div>

                    <div class="attachments-list" t-if="!state.isLoading">
                        <div class="table-responsive" t-if="state.attachments.length">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>文件</th>
                                        <th>大小</th>
                                        <th>类型</th>
                                        <th>上传时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-foreach="state.attachments" t-as="attachment" t-key="attachment.id">
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i t-att-class="'fa ' + getFileIcon(attachment.mimetype) + ' me-2'"/>
                                                    <span t-esc="attachment.datas_fname || attachment.name"/>
                                                </div>
                                            </td>
                                            <td t-esc="formatFileSize(attachment.file_size)"/>
                                            <td>
                                                <small class="text-muted" t-esc="attachment.mimetype"/>
                                            </td>
                                            <td>
                                                <small t-esc="new Date(attachment.create_date).toLocaleString()"/>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <button 
                                                        class="btn btn-sm btn-outline-primary"
                                                        t-on-click="() => this.downloadAttachment(attachment)"
                                                    >
                                                        <i class="fa fa-download"/>
                                                    </button>
                                                    <button 
                                                        class="btn btn-sm btn-outline-danger"
                                                        t-on-click="() => this.deleteAttachment(attachment.id)"
                                                    >
                                                        <i class="fa fa-trash"/>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </t>
                                </tbody>
                            </table>
                        </div>

                        <div class="empty-state text-center py-5" t-if="!state.attachments.length">
                            <i class="fa fa-file-o fa-3x text-muted mb-3"/>
                            <h6 class="text-muted">暂无附件</h6>
                            <p class="text-muted">点击上方区域上传第一个附件</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

### 3. 图片上传预览器

```javascript
// 图片上传预览器
class ImageUploadPreview extends Component {
    setup() {
        this.state = useState({
            images: [],
            previewUrls: new Map(),
            maxImages: 5,
            maxFileSize: 5 * 1024 * 1024 // 5MB
        });
    }

    async onImageUpload(parsedFileData, files) {
        // 验证文件类型
        const imageFiles = Array.from(files).filter(file => 
            file.type.startsWith('image/')
        );

        if (imageFiles.length !== files.length) {
            this.notification.add('只能上传图片文件', { type: 'warning' });
        }

        if (imageFiles.length === 0) return;

        // 验证文件大小
        const oversizedFiles = imageFiles.filter(file => 
            file.size > this.state.maxFileSize
        );

        if (oversizedFiles.length > 0) {
            this.notification.add(
                `${oversizedFiles.length} 个文件超过大小限制 (5MB)`,
                { type: 'warning' }
            );
            return;
        }

        // 添加图片到列表
        this.state.images = [...this.state.images, ...parsedFileData];

        // 生成预览URL
        imageFiles.forEach((file, index) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const fileData = parsedFileData[index];
                if (fileData) {
                    this.state.previewUrls.set(fileData.id, e.target.result);
                }
            };
            reader.readAsDataURL(file);
        });

        this.notification.add(`成功上传 ${imageFiles.length} 张图片`, { type: 'success' });
    }

    async beforeImageOpen() {
        if (this.state.images.length >= this.state.maxImages) {
            this.notification.add(
                `最多只能上传 ${this.state.maxImages} 张图片`,
                { type: 'warning' }
            );
            return false;
        }
        return true;
    }

    removeImage(imageId) {
        this.state.images = this.state.images.filter(img => img.id !== imageId);
        this.state.previewUrls.delete(imageId);
    }

    render() {
        return xml`
            <div class="image-upload-preview">
                <h5>图片上传预览器</h5>
                
                <div class="upload-section mb-4">
                    <FileInput
                        acceptedFileExtensions=".jpg,.jpeg,.png,.gif,.webp"
                        multiUpload="true"
                        onUpload="onImageUpload"
                        beforeOpen="beforeImageOpen"
                    >
                        <div class="upload-trigger">
                            <div class="card text-center" style="cursor: pointer;">
                                <div class="card-body">
                                    <i class="fa fa-image fa-3x text-primary mb-3"/>
                                    <h6>点击上传图片</h6>
                                    <p class="text-muted mb-0">
                                        支持 JPG, PNG, GIF, WebP<br/>
                                        最大 5MB，最多 ${this.state.maxImages} 张
                                    </p>
                                </div>
                            </div>
                        </div>
                    </FileInput>
                </div>

                <div class="images-grid" t-if="state.images.length">
                    <h6>已上传的图片 (${state.images.length}/${state.maxImages})</h6>
                    <div class="row">
                        <t t-foreach="state.images" t-as="image" t-key="image.id">
                            <div class="col-md-3 mb-3">
                                <div class="card">
                                    <div class="image-container" style="height: 200px; overflow: hidden;">
                                        <img 
                                            t-if="state.previewUrls.has(image.id)"
                                            t-att-src="state.previewUrls.get(image.id)"
                                            class="card-img-top"
                                            style="width: 100%; height: 100%; object-fit: cover;"
                                            t-att-alt="image.name"
                                        />
                                        <div 
                                            t-else=""
                                            class="d-flex align-items-center justify-content-center h-100 bg-light"
                                        >
                                            <i class="fa fa-image fa-2x text-muted"/>
                                        </div>
                                    </div>
                                    <div class="card-body p-2">
                                        <h6 class="card-title small mb-1" t-esc="image.name"/>
                                        <p class="card-text">
                                            <small class="text-muted" t-esc="(image.size / 1024).toFixed(1) + ' KB'"/>
                                        </p>
                                        <button 
                                            class="btn btn-sm btn-outline-danger w-100"
                                            t-on-click="() => this.removeImage(image.id)"
                                        >
                                            <i class="fa fa-trash"/> 删除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>
                </div>

                <div class="empty-state text-center py-5" t-if="!state.images.length">
                    <i class="fa fa-image fa-3x text-muted mb-3"/>
                    <h6 class="text-muted">暂无图片</h6>
                    <p class="text-muted">点击上方区域上传第一张图片</p>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 自定义触发器
- 支持任意HTML元素作为触发器
- 隐藏原生文件输入元素
- 灵活的UI设计

### 2. 文件类型控制
- 支持文件扩展名限制
- 客户端文件类型验证
- 安全的文件上传

### 3. 状态管理
- 上传状态的实时跟踪
- 防止重复上传
- 用户友好的状态反馈

### 4. 生命周期控制
- 上传前的验证钩子
- 上传完成的回调处理
- 完整的错误处理

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 增强原生文件输入功能
- 保持原有接口不变

### 2. 策略模式 (Strategy Pattern)
- 可配置的上传策略
- 灵活的验证规则

### 3. 观察者模式 (Observer Pattern)
- 文件变化的监听
- 状态变化的通知

## 注意事项

1. **文件大小**: 注意文件大小限制和服务器配置
2. **安全性**: 验证文件类型和内容安全
3. **用户体验**: 提供清晰的上传状态反馈
4. **错误处理**: 妥善处理上传失败的情况

## 扩展建议

1. **进度显示**: 文件上传进度的可视化显示
2. **拖拽支持**: 集成拖拽上传功能
3. **预览功能**: 文件上传前的预览功能
4. **批量管理**: 批量文件的管理和操作
5. **云存储**: 支持云存储服务的集成

该文件输入组件为Odoo Web应用提供了强大的文件上传功能，通过自定义触发器和完整的生命周期控制确保了良好的用户体验和开发者友好性。

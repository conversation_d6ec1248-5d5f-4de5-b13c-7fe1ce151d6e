# @web/core/currency.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/currency.js`
- **原始路径**: `/web/static/src/core/currency.js`
- **代码行数**: 57行
- **作用**: 提供货币格式化功能，支持多币种显示、符号位置、精度控制和人性化数字格式

## 🎯 学习目标
通过学习这个文件，您将掌握：
- Odoo货币系统的前端实现原理
- 货币格式化的各种选项和配置
- 国际化货币显示的最佳实践
- 数字格式化和人性化显示技巧
- 货币数据的管理和访问方式

## 📚 核心概念

### 什么是Currency系统？
Currency系统是Odoo Web框架中的**货币格式化工具**，主要功能：
- **多币种支持**: 支持全球各种货币的显示
- **格式化**: 根据货币配置格式化金额显示
- **国际化**: 支持不同地区的货币显示习惯
- **精度控制**: 精确控制小数位数和舍入规则

### 货币数据结构
```javascript
// 货币对象的典型结构
const currency = {
    id: 1,
    name: "USD",
    symbol: "$",           // 货币符号
    position: "before",    // 符号位置: "before" 或 "after"
    digits: [16, 2],      // 精度: [总位数, 小数位数]
    decimal_places: 2,     // 小数位数
    rounding: 0.01        // 舍入精度
};

// 全局货币映射
const currencies = {
    1: { name: "USD", symbol: "$", position: "before", digits: [16, 2] },
    2: { name: "EUR", symbol: "€", position: "after", digits: [16, 2] },
    3: { name: "JPY", symbol: "¥", position: "before", digits: [16, 0] },
    4: { name: "CNY", symbol: "¥", position: "before", digits: [16, 2] }
};
```

### 基本使用模式
```javascript
// 导入currency模块
const { formatCurrency, getCurrency } = require("@web/core/currency");

// 基础格式化
const formatted = formatCurrency(1234.56, 1); // "$1,234.56"

// 带选项的格式化
const humanReadable = formatCurrency(1234567.89, 1, {
    humanReadable: true
}); // "$1.23M"

// 无符号格式化
const noSymbol = formatCurrency(1234.56, 1, {
    noSymbol: true
}); // "1,234.56"

// 自定义精度
const customDigits = formatCurrency(1234.5678, 1, {
    digits: [16, 4]
}); // "$1,234.5678"
```

## 🔍 核心功能详解

### 1. 货币数据管理

#### currencies对象 - 全局货币存储
```javascript
const currencies = session.currencies || {};
// 确保代码从这里读取货币数据
delete session.currencies;
```

**设计原则**：
- **单一数据源**: 从session中提取后删除，确保唯一数据源
- **全局访问**: 提供全局的货币数据访问点
- **内存效率**: 避免重复存储货币数据

#### getCurrency() - 获取货币信息
```javascript
function getCurrency(id) {
    return currencies[id];
}
```

**使用示例**：
```javascript
// 获取美元信息
const usd = getCurrency(1);
console.log(usd); // { name: "USD", symbol: "$", position: "before", digits: [16, 2] }

// 检查货币是否存在
if (getCurrency(currencyId)) {
    // 货币存在，可以安全使用
}
```

### 2. formatCurrency() - 核心格式化函数

#### 函数签名和参数
```javascript
function formatCurrency(amount, currencyId, options = {})
```

**参数详解**：
- **amount**: 要格式化的数值
- **currencyId**: 货币ID（对应res.currency记录）
- **options**: 格式化选项对象

#### 选项参数详解
```javascript
const options = {
    // 数字格式选项
    digits: [16, 2],           // 自定义精度 [总位数, 小数位数]
    humanReadable: false,      // 是否使用人性化格式 (1.2M, 3.4K)
    
    // 显示选项
    noSymbol: false,          // 是否隐藏货币符号
    
    // 数据上下文（用于动态货币字段）
    data: {},                 // 字段数据映射
    currencyField: null       // 货币字段名
};
```

#### 格式化流程分析
```javascript
function formatCurrency(amount, currencyId, options = {}) {
    // 1. 获取货币信息
    const currency = getCurrency(currencyId);
    const digits = options.digits || (currency && currency.digits);

    // 2. 格式化数字
    let formattedAmount;
    if (options.humanReadable) {
        // 人性化格式: 1234567 -> "1.23M"
        formattedAmount = humanNumber(amount, { decimals: digits ? digits[1] : 2 });
    } else {
        // 标准格式: 1234.56 -> "1,234.56"
        formattedAmount = formatFloat(amount, { digits });
    }

    // 3. 处理货币符号
    if (!currency || options.noSymbol) {
        return formattedAmount; // 只返回数字
    }
    
    // 4. 组合符号和数字
    const formatted = [currency.symbol, formattedAmount];
    if (currency.position === "after") {
        formatted.reverse(); // 符号在后
    }
    
    // 5. 使用非断行空格连接
    return formatted.join(nbsp);
}
```

## 🎨 实际应用场景

### 1. 在字段组件中的使用
```javascript
class MonetaryField extends Component {
    setup() {
        this.currency = getCurrency(this.props.currencyId);
    }
    
    get formattedValue() {
        return formatCurrency(
            this.props.value, 
            this.props.currencyId,
            {
                digits: this.props.digits,
                noSymbol: this.props.noSymbol
            }
        );
    }
    
    static template = xml`
        <span class="o_monetary_field" t-esc="formattedValue"/>
    `;
}
```

### 2. 在列表视图中的使用
```javascript
class ListRenderer extends Component {
    formatMonetaryCell(value, field, record) {
        // 动态获取货币ID
        const currencyId = record[field.currency_field] || field.currency_id;
        
        return formatCurrency(value, currencyId, {
            humanReadable: field.widget === 'monetary_human',
            digits: field.digits
        });
    }
}
```

### 3. 在报表中的使用
```javascript
class FinancialReport extends Component {
    formatAmount(amount, currencyId, options = {}) {
        return formatCurrency(amount, currencyId, {
            humanReadable: this.props.humanReadable,
            ...options
        });
    }
    
    get totalFormatted() {
        return this.formatAmount(this.state.total, this.props.currencyId);
    }
    
    get summaryItems() {
        return this.state.items.map(item => ({
            ...item,
            amountFormatted: this.formatAmount(item.amount, item.currencyId)
        }));
    }
}
```

## 🛠️ 实践练习

### 练习1: 多币种格式化器
```javascript
class MultiCurrencyFormatter {
    constructor() {
        this.defaultOptions = {
            humanReadable: false,
            noSymbol: false,
            fallbackCurrency: 1 // USD作为后备货币
        };
    }
    
    // 格式化单个金额
    format(amount, currencyId, options = {}) {
        const mergedOptions = { ...this.defaultOptions, ...options };
        
        // 检查货币是否存在
        if (!getCurrency(currencyId)) {
            console.warn(`Currency ${currencyId} not found, using fallback`);
            currencyId = mergedOptions.fallbackCurrency;
        }
        
        return formatCurrency(amount, currencyId, mergedOptions);
    }
    
    // 批量格式化
    formatBatch(items, options = {}) {
        return items.map(item => ({
            ...item,
            formatted: this.format(item.amount, item.currencyId, options)
        }));
    }
    
    // 格式化为不同币种
    formatMultiple(amount, sourceCurrencyId, targetCurrencyIds, exchangeRates) {
        const results = {};
        
        for (const targetId of targetCurrencyIds) {
            const rate = exchangeRates[`${sourceCurrencyId}_${targetId}`] || 1;
            const convertedAmount = amount * rate;
            results[targetId] = this.format(convertedAmount, targetId);
        }
        
        return results;
    }
    
    // 比较不同货币格式
    compare(amount1, currencyId1, amount2, currencyId2, baseCurrencyId = 1) {
        // 转换为基础货币进行比较
        const rate1 = this.getExchangeRate(currencyId1, baseCurrencyId);
        const rate2 = this.getExchangeRate(currencyId2, baseCurrencyId);
        
        const baseAmount1 = amount1 * rate1;
        const baseAmount2 = amount2 * rate2;
        
        return {
            amount1: this.format(amount1, currencyId1),
            amount2: this.format(amount2, currencyId2),
            baseAmount1: this.format(baseAmount1, baseCurrencyId),
            baseAmount2: this.format(baseAmount2, baseCurrencyId),
            difference: this.format(baseAmount1 - baseAmount2, baseCurrencyId),
            ratio: baseAmount2 !== 0 ? baseAmount1 / baseAmount2 : null
        };
    }
    
    getExchangeRate(fromCurrencyId, toCurrencyId) {
        // 简化的汇率获取（实际应用中应该从服务器获取）
        if (fromCurrencyId === toCurrencyId) return 1;
        
        // 示例汇率
        const rates = {
            '1_2': 0.85,  // USD to EUR
            '2_1': 1.18,  // EUR to USD
            '1_3': 110,   // USD to JPY
            '3_1': 0.009  // JPY to USD
        };
        
        return rates[`${fromCurrencyId}_${toCurrencyId}`] || 1;
    }
    
    // 获取货币统计信息
    getCurrencyStats() {
        const stats = {
            total: Object.keys(currencies).length,
            byPosition: { before: 0, after: 0 },
            byDigits: {},
            symbols: []
        };
        
        for (const currency of Object.values(currencies)) {
            // 统计符号位置
            stats.byPosition[currency.position]++;
            
            // 统计小数位数
            const decimals = currency.digits ? currency.digits[1] : 2;
            stats.byDigits[decimals] = (stats.byDigits[decimals] || 0) + 1;
            
            // 收集符号
            if (currency.symbol && !stats.symbols.includes(currency.symbol)) {
                stats.symbols.push(currency.symbol);
            }
        }
        
        return stats;
    }
}

// 使用示例
const formatter = new MultiCurrencyFormatter();

// 基础格式化
console.log(formatter.format(1234.56, 1)); // "$1,234.56"

// 批量格式化
const items = [
    { amount: 1000, currencyId: 1, name: "Product A" },
    { amount: 850, currencyId: 2, name: "Product B" },
    { amount: 110000, currencyId: 3, name: "Product C" }
];

const formatted = formatter.formatBatch(items, { humanReadable: true });
console.log(formatted);
// [
//   { amount: 1000, currencyId: 1, name: "Product A", formatted: "$1.00K" },
//   { amount: 850, currencyId: 2, name: "Product B", formatted: "850.00 €" },
//   { amount: 110000, currencyId: 3, name: "Product C", formatted: "¥110K" }
// ]
```

### 练习2: 货币显示组件
```javascript
class CurrencyDisplay extends Component {
    static template = xml`
        <span class="currency-display" 
              t-att-class="getDisplayClass()"
              t-att-title="getTooltip()">
            <t t-if="props.showCode" t-esc="currencyCode"/>
            <span class="amount" t-esc="formattedAmount"/>
            <t t-if="props.showConversion and conversionRate !== 1">
                <span class="conversion">
                    (≈ <t t-esc="convertedAmount"/>)
                </span>
            </t>
        </span>
    `;
    
    static props = {
        amount: Number,
        currencyId: Number,
        humanReadable: { type: Boolean, optional: true },
        noSymbol: { type: Boolean, optional: true },
        showCode: { type: Boolean, optional: true },
        showConversion: { type: Boolean, optional: true },
        baseCurrencyId: { type: Number, optional: true },
        digits: { type: Array, optional: true },
        className: { type: String, optional: true }
    };
    
    setup() {
        this.currency = getCurrency(this.props.currencyId);
        this.baseCurrency = this.props.baseCurrencyId ? 
            getCurrency(this.props.baseCurrencyId) : null;
    }
    
    get formattedAmount() {
        return formatCurrency(this.props.amount, this.props.currencyId, {
            humanReadable: this.props.humanReadable,
            noSymbol: this.props.noSymbol,
            digits: this.props.digits
        });
    }
    
    get currencyCode() {
        return this.currency ? this.currency.name : '';
    }
    
    get conversionRate() {
        if (!this.props.showConversion || !this.baseCurrency) {
            return 1;
        }
        // 简化的汇率计算
        return this.getExchangeRate(this.props.currencyId, this.props.baseCurrencyId);
    }
    
    get convertedAmount() {
        if (this.conversionRate === 1) return '';
        
        const converted = this.props.amount * this.conversionRate;
        return formatCurrency(converted, this.props.baseCurrencyId, {
            humanReadable: this.props.humanReadable
        });
    }
    
    getDisplayClass() {
        const classes = ['currency-display'];
        
        if (this.props.className) {
            classes.push(this.props.className);
        }
        
        if (this.props.amount < 0) {
            classes.push('negative');
        } else if (this.props.amount > 0) {
            classes.push('positive');
        } else {
            classes.push('zero');
        }
        
        if (this.currency) {
            classes.push(`currency-${this.currency.name.toLowerCase()}`);
        }
        
        return classes.join(' ');
    }
    
    getTooltip() {
        if (!this.currency) return '';
        
        const parts = [
            `Currency: ${this.currency.name}`,
            `Amount: ${this.props.amount}`,
            `Precision: ${this.currency.digits ? this.currency.digits[1] : 2} decimals`
        ];
        
        if (this.props.showConversion && this.conversionRate !== 1) {
            parts.push(`Exchange rate: ${this.conversionRate}`);
        }
        
        return parts.join('\n');
    }
    
    getExchangeRate(fromCurrencyId, toCurrencyId) {
        // 实际应用中应该从服务器获取实时汇率
        const rates = {
            '1_2': 0.85, '2_1': 1.18,
            '1_3': 110, '3_1': 0.009,
            '2_3': 129, '3_2': 0.0078
        };
        return rates[`${fromCurrencyId}_${toCurrencyId}`] || 1;
    }
}

// 使用示例
// <CurrencyDisplay 
//     amount="1234.56" 
//     currencyId="1" 
//     humanReadable="true"
//     showConversion="true"
//     baseCurrencyId="2"/>
```

### 练习3: 货币计算器
```javascript
class CurrencyCalculator {
    constructor() {
        this.precision = 6; // 计算精度
    }
    
    // 加法运算
    add(amount1, currencyId1, amount2, currencyId2, resultCurrencyId = currencyId1) {
        const converted1 = this.convertTo(amount1, currencyId1, resultCurrencyId);
        const converted2 = this.convertTo(amount2, currencyId2, resultCurrencyId);
        
        return {
            result: this.round(converted1 + converted2, resultCurrencyId),
            formatted: formatCurrency(
                this.round(converted1 + converted2, resultCurrencyId), 
                resultCurrencyId
            ),
            breakdown: {
                amount1: { value: converted1, formatted: formatCurrency(converted1, resultCurrencyId) },
                amount2: { value: converted2, formatted: formatCurrency(converted2, resultCurrencyId) }
            }
        };
    }
    
    // 减法运算
    subtract(amount1, currencyId1, amount2, currencyId2, resultCurrencyId = currencyId1) {
        const converted1 = this.convertTo(amount1, currencyId1, resultCurrencyId);
        const converted2 = this.convertTo(amount2, currencyId2, resultCurrencyId);
        
        return {
            result: this.round(converted1 - converted2, resultCurrencyId),
            formatted: formatCurrency(
                this.round(converted1 - converted2, resultCurrencyId), 
                resultCurrencyId
            )
        };
    }
    
    // 乘法运算
    multiply(amount, currencyId, multiplier) {
        const result = this.round(amount * multiplier, currencyId);
        return {
            result,
            formatted: formatCurrency(result, currencyId)
        };
    }
    
    // 除法运算
    divide(amount, currencyId, divisor) {
        if (divisor === 0) {
            throw new Error('Division by zero');
        }
        
        const result = this.round(amount / divisor, currencyId);
        return {
            result,
            formatted: formatCurrency(result, currencyId)
        };
    }
    
    // 货币转换
    convertTo(amount, fromCurrencyId, toCurrencyId) {
        if (fromCurrencyId === toCurrencyId) {
            return amount;
        }
        
        const rate = this.getExchangeRate(fromCurrencyId, toCurrencyId);
        return amount * rate;
    }
    
    // 按货币精度舍入
    round(amount, currencyId) {
        const currency = getCurrency(currencyId);
        const decimals = currency && currency.digits ? currency.digits[1] : 2;
        const factor = Math.pow(10, decimals);
        return Math.round(amount * factor) / factor;
    }
    
    // 计算百分比
    percentage(amount, currencyId, percentage) {
        const result = this.round(amount * (percentage / 100), currencyId);
        return {
            result,
            formatted: formatCurrency(result, currencyId),
            percentage: percentage + '%'
        };
    }
    
    // 分摊计算
    distribute(totalAmount, currencyId, parts) {
        const totalParts = parts.reduce((sum, part) => sum + part.ratio, 0);
        const results = [];
        let distributedSum = 0;
        
        for (let i = 0; i < parts.length; i++) {
            const part = parts[i];
            let amount;
            
            if (i === parts.length - 1) {
                // 最后一部分使用剩余金额，避免舍入误差
                amount = totalAmount - distributedSum;
            } else {
                amount = this.round((totalAmount * part.ratio) / totalParts, currencyId);
                distributedSum += amount;
            }
            
            results.push({
                ...part,
                amount,
                formatted: formatCurrency(amount, currencyId)
            });
        }
        
        return {
            total: {
                amount: totalAmount,
                formatted: formatCurrency(totalAmount, currencyId)
            },
            parts: results,
            verification: {
                sum: results.reduce((sum, r) => sum + r.amount, 0),
                difference: totalAmount - results.reduce((sum, r) => sum + r.amount, 0)
            }
        };
    }
    
    getExchangeRate(fromCurrencyId, toCurrencyId) {
        // 简化的汇率获取
        const rates = {
            '1_2': 0.85, '2_1': 1.18,
            '1_3': 110, '3_1': 0.009,
            '2_3': 129, '3_2': 0.0078
        };
        return rates[`${fromCurrencyId}_${toCurrencyId}`] || 1;
    }
}

// 使用示例
const calculator = new CurrencyCalculator();

// 加法运算
const sum = calculator.add(100, 1, 85, 2, 1); // $100 + €85 = $200.30
console.log(sum.formatted); // "$200.30"

// 分摊计算
const distribution = calculator.distribute(1000, 1, [
    { name: "Part A", ratio: 3 },
    { name: "Part B", ratio: 2 },
    { name: "Part C", ratio: 1 }
]);
console.log(distribution);
// {
//   total: { amount: 1000, formatted: "$1,000.00" },
//   parts: [
//     { name: "Part A", ratio: 3, amount: 500, formatted: "$500.00" },
//     { name: "Part B", ratio: 2, amount: 333.33, formatted: "$333.33" },
//     { name: "Part C", ratio: 1, amount: 166.67, formatted: "$166.67" }
//   ]
// }
```

## 🔧 调试技巧

### 查看货币配置
```javascript
// 查看所有可用货币
console.log('Available currencies:', currencies);

// 查看特定货币配置
const usd = getCurrency(1);
console.log('USD configuration:', usd);

// 检查货币是否存在
function debugCurrency(currencyId) {
    const currency = getCurrency(currencyId);
    if (currency) {
        console.log(`Currency ${currencyId}:`, {
            name: currency.name,
            symbol: currency.symbol,
            position: currency.position,
            digits: currency.digits,
            sample: formatCurrency(1234.56, currencyId)
        });
    } else {
        console.warn(`Currency ${currencyId} not found`);
    }
}
```

### 测试格式化选项
```javascript
function testCurrencyFormatting(amount, currencyId) {
    console.group(`Testing currency formatting for ${amount} (currency ${currencyId})`);
    
    const tests = [
        { name: 'Default', options: {} },
        { name: 'No Symbol', options: { noSymbol: true } },
        { name: 'Human Readable', options: { humanReadable: true } },
        { name: 'Custom Digits', options: { digits: [16, 4] } },
        { name: 'Human + No Symbol', options: { humanReadable: true, noSymbol: true } }
    ];
    
    tests.forEach(test => {
        try {
            const result = formatCurrency(amount, currencyId, test.options);
            console.log(`${test.name}: ${result}`);
        } catch (error) {
            console.error(`${test.name}: Error - ${error.message}`);
        }
    });
    
    console.groupEnd();
}

// 测试不同金额和货币
testCurrencyFormatting(1234.56, 1);
testCurrencyFormatting(1234567.89, 2);
testCurrencyFormatting(123456, 3);
```

## 📊 性能考虑

### 优化策略
1. **缓存格式化结果**: 对相同参数的格式化结果进行缓存
2. **批量处理**: 批量格式化多个金额
3. **延迟加载**: 按需加载货币配置
4. **内存管理**: 及时清理不需要的货币数据

### 最佳实践
```javascript
// ✅ 好的做法：缓存货币对象
const currency = getCurrency(currencyId);
if (currency) {
    // 多次使用同一货币对象
}

// ❌ 不好的做法：重复获取
formatCurrency(amount1, currencyId);
formatCurrency(amount2, currencyId); // 重复获取货币信息

// ✅ 好的做法：批量格式化
const formatter = new MultiCurrencyFormatter();
const results = formatter.formatBatch(items);

// ❌ 不好的做法：逐个格式化
items.forEach(item => {
    item.formatted = formatCurrency(item.amount, item.currencyId);
});
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解Odoo货币系统的前端实现
- [ ] 掌握formatCurrency函数的各种选项
- [ ] 能够处理多币种显示需求
- [ ] 理解货币精度和舍入规则
- [ ] 掌握人性化数字格式的应用
- [ ] 能够创建自定义的货币格式化组件

## 🚀 下一步学习
学完Currency系统后，建议继续学习：
1. **数字工具** (`@web/core/utils/numbers.js`) - 深入理解数字格式化
2. **字符串工具** (`@web/core/utils/strings.js`) - 学习字符串处理工具
3. **国际化** (`@web/core/l10n/`) - 了解完整的国际化支持

## 💡 重要提示
- Currency系统是财务应用的基础组件
- 理解货币精度对财务计算至关重要
- 符号位置和格式因地区而异
- 人性化格式适用于大数值的显示

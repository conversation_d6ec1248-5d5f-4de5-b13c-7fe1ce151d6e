# @web/core/template_inheritance.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/template_inheritance.js`
- **原始路径**: `/web/static/src/core/template_inheritance.js`
- **代码行数**: 313行
- **作用**: 实现模板继承系统，支持XML模板的动态修改和扩展

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 模板继承的核心原理和设计思想
- XML DOM操作和XPath查询技术
- 复杂的文本节点处理和规范化
- 模板修改操作的完整实现
- 企业级模板系统的架构设计

## 📚 核心概念

### 什么是模板继承？
模板继承是Odoo Web框架中的**模板扩展机制**，主要功能：
- **模板修改**: 动态修改现有XML模板的结构和内容
- **位置操作**: 支持before、after、inside、replace等位置操作
- **属性修改**: 动态添加、删除、修改元素属性
- **XPath支持**: 使用XPath表达式精确定位目标元素

### 核心架构组成
```javascript
// 主要API
applyInheritance(root, operations, url)

// 支持的操作位置
const positions = [
    "before",      // 在目标元素之前插入
    "after",       // 在目标元素之后插入
    "inside",      // 在目标元素内部插入
    "replace",     // 替换目标元素
    "attributes"   // 修改目标元素属性
];

// 操作类型
const operationTypes = {
    element: "通过标签名和属性定位",
    xpath: "通过XPath表达式定位"
};
```

### 基本使用模式
```xml
<!-- 原始模板 -->
<template id="base_template">
    <div class="container">
        <h1>Title</h1>
        <div class="content">
            <p>Original content</p>
        </div>
    </div>
</template>

<!-- 继承操作 -->
<template id="extended_template" inherit_id="base_template">
    <!-- 在标题后添加副标题 -->
    <h1 position="after">
        <h2>Subtitle</h2>
    </h1>
    
    <!-- 在内容区域内部添加新段落 -->
    <div class="content" position="inside">
        <p>Additional content</p>
    </div>
    
    <!-- 修改容器属性 -->
    <div class="container" position="attributes">
        <attribute name="class" add="enhanced"/>
    </div>
    
    <!-- 使用XPath精确定位 -->
    <xpath expr="//p[text()='Original content']" position="replace">
        <p>Replaced content</p>
    </xpath>
</template>
```

## 🔍 核心实现详解

### 1. applyInheritance() - 主入口函数

#### 函数签名和流程
```javascript
function applyInheritance(root, operations, url = "") {
    for (const operation of operations.children) {
        // 1. 定位目标元素
        const target = getElement(root, operation);
        
        // 2. 获取操作位置
        const position = operation.getAttribute("position") || "inside";
        
        // 3. 添加调试信息
        if (odoo.debug && url) {
            // 添加注释标记操作来源
        }
        
        // 4. 执行对应的位置操作
        switch (position) {
            case "replace": root = replace(root, target, operation); break;
            case "attributes": modifyAttributes(target, operation); break;
            case "inside": /* 内部插入逻辑 */ break;
            case "after": /* 后置插入逻辑 */ break;
            case "before": addBefore(target, operation); break;
        }
    }
    return root;
}
```

**核心设计特点**：
- **批量处理**: 遍历所有操作并依次执行
- **位置驱动**: 根据position属性选择不同的操作策略
- **调试支持**: 在调试模式下添加操作来源注释
- **根节点更新**: replace操作可能改变根节点

### 2. 元素定位系统

#### getElement() - 安全的元素获取
```javascript
function getElement(element, operation) {
    const node = getNode(element, operation);
    if (!node) {
        throw new Error(`Element '${operation.outerHTML}' cannot be located in element tree`);
    }
    if (!(node instanceof Element)) {
        throw new Error(`Found node ${node} instead of an element`);
    }
    return node;
}
```

#### getNode() - 核心定位逻辑
```javascript
function getNode(element, operation) {
    const root = getRoot(element);
    const doc = new Document();
    doc.appendChild(root);
    
    if (operation.tagName === "xpath") {
        // XPath定位
        const xpath = getXpath(operation);
        const result = doc.evaluate(xpath, root, null, XPathResult.FIRST_ORDERED_NODE_TYPE);
        return result.singleNodeValue;
    }
    
    // 标签名和属性定位
    for (const elem of root.querySelectorAll(operation.tagName)) {
        if ([...operation.attributes].every(
            ({ name, value }) => name === "position" || elem.getAttribute(name) === value
        )) {
            return elem;
        }
    }
    return null;
}
```

**定位策略**：
- **XPath优先**: 如果操作标签是xpath，使用XPath表达式
- **属性匹配**: 通过标签名和属性值精确匹配
- **文档上下文**: 创建临时文档确保XPath正确执行

#### getXpath() - XPath表达式处理
```javascript
function getXpath(operation) {
    const xpath = operation.getAttribute("expr");
    
    // 处理hasclass()自定义函数
    return xpath.replaceAll(HASCLASS_REGEXP, (_, capturedGroup) => {
        return capturedGroup
            .split(",")
            .map((c) => `contains(concat(' ', @class, ' '), ' ${c.trim().slice(1, -1)} ')`)
            .join(" and ");
    });
}
```

**XPath增强**：
- **hasclass()函数**: 将自定义hasclass()转换为标准XPath表达式
- **类名匹配**: 使用concat和contains实现精确的类名匹配
- **多类支持**: 支持同时匹配多个CSS类名

### 3. 位置操作实现

#### addBefore() - 前置插入操作
```javascript
function addBefore(target, operation) {
    const nodes = getNodes(target, operation);
    if (nodes.length === 0) return;
    
    const { previousSibling } = target;
    target.before(...nodes);
    
    // 文本节点规范化
    if (previousSibling?.nodeType === Node.TEXT_NODE) {
        const [text1, text2] = previousSibling.data.split(RSTRIP_REGEXP);
        previousSibling.data = text1.trimEnd();
        
        if (nodes[0].nodeType === Node.TEXT_NODE) {
            mergeTextNodes(previousSibling, nodes[0]);
        }
        
        if (text2 && nodes.some((n) => n.nodeType !== Node.TEXT_NODE)) {
            const textNode = document.createTextNode(text2);
            target.before(textNode);
            if (textNode.previousSibling.nodeType === Node.TEXT_NODE) {
                mergeTextNodes(textNode.previousSibling, textNode);
            }
        }
    }
}
```

**文本处理策略**：
- **空白规范化**: 使用RSTRIP_REGEXP处理行尾空白
- **节点合并**: 自动合并相邻的文本节点
- **格式保持**: 保持原有的缩进和换行格式

#### replace() - 替换操作
```javascript
function replace(root, target, operation) {
    const mode = operation.getAttribute("mode") || "outer";
    
    switch (mode) {
        case "outer": {
            // 处理$0占位符
            const result = operation.ownerDocument.evaluate(
                ".//*[text()='$0']",
                operation,
                null,
                XPathResult.ORDERED_NODE_SNAPSHOT_TYPE
            );
            for (let i = 0; i < result.snapshotLength; i++) {
                const loc = result.snapshotItem(i);
                loc.firstChild.replaceWith(target.cloneNode(true));
            }
            
            if (target.parentElement) {
                const nodes = getNodes(target, operation);
                target.replaceWith(...nodes);
            } else {
                // 根节点替换逻辑
                // ...
            }
            break;
        }
        case "inner":
            while (target.firstChild) {
                target.removeChild(target.lastChild);
            }
            target.append(...operation.childNodes);
            break;
    }
    return root;
}
```

**替换模式**：
- **outer模式**: 替换整个元素（包括标签）
- **inner模式**: 只替换元素内容
- **$0占位符**: 在新内容中引用原始元素

#### modifyAttributes() - 属性修改
```javascript
function modifyAttributes(target, operation) {
    for (const child of operation.children) {
        if (child.tagName !== "attribute") continue;
        
        const attributeName = child.getAttribute("name");
        const firstNode = child.childNodes[0];
        let value = firstNode?.nodeType === Node.TEXT_NODE ? firstNode.data : "";
        
        const add = child.getAttribute("add") || "";
        const remove = child.getAttribute("remove") || "";
        
        if (add || remove) {
            const separator = child.getAttribute("separator") || ",";
            const toRemove = new Set(splitAndTrim(remove, separator));
            const values = splitAndTrim(target.getAttribute(attributeName) || "", separator)
                .filter((s) => !toRemove.has(s));
            values.push(...splitAndTrim(add, separator).filter((s) => s));
            value = values.join(separator);
        }
        
        if (value) {
            target.setAttribute(attributeName, value);
        } else {
            target.removeAttribute(attributeName);
        }
    }
}
```

**属性操作类型**：
- **直接设置**: 设置属性为指定值
- **添加值**: 向属性添加新值（支持分隔符）
- **移除值**: 从属性中移除指定值
- **清空属性**: 值为空时移除整个属性

### 4. 文本节点处理

#### mergeTextNodes() - 文本节点合并
```javascript
function mergeTextNodes(first, second, trimEnd = true) {
    first.data = (trimEnd ? first.data.trimEnd() : first.data) + second.data;
    second.remove();
}
```

#### removeNode() - 节点移除和规范化
```javascript
function removeNode(node) {
    const { nextSibling, previousSibling } = node;
    node.remove();
    
    if (nextSibling?.nodeType === Node.TEXT_NODE && 
        previousSibling?.nodeType === Node.TEXT_NODE) {
        mergeTextNodes(
            previousSibling,
            nextSibling,
            previousSibling.parentElement.firstChild === previousSibling
        );
    }
}
```

**文本规范化原则**：
- **自动合并**: 移除节点后自动合并相邻文本节点
- **空白处理**: 智能处理行首行尾空白
- **格式保持**: 保持文档的原有格式结构

## 🎨 实际应用场景

### 1. 视图模板扩展
```javascript
// 扩展列表视图模板
class ListView extends Component {
    static template = "web.ListView";
    
    static inheritTemplate() {
        return `
            <template id="enhanced_list_view" inherit_id="web.ListView">
                <!-- 添加搜索栏 -->
                <div class="o_list_view" position="inside">
                    <div class="o_search_bar">
                        <input type="text" placeholder="Search..."/>
                    </div>
                </div>
                
                <!-- 修改表格样式 -->
                <table position="attributes">
                    <attribute name="class" add="table-striped"/>
                </table>
                
                <!-- 在每行后添加操作按钮 -->
                <xpath expr="//tr[@t-foreach='records']" position="inside">
                    <td class="o_list_actions">
                        <button class="btn btn-sm btn-primary">Edit</button>
                        <button class="btn btn-sm btn-danger">Delete</button>
                    </td>
                </xpath>
            </template>
        `;
    }
}
```

### 2. 表单模板定制
```javascript
// 动态表单模板系统
class DynamicFormBuilder {
    constructor() {
        this.baseTemplate = this.createBaseTemplate();
        this.customizations = [];
    }

    createBaseTemplate() {
        return `
            <form class="o_form_view">
                <div class="o_form_sheet">
                    <div class="o_form_header">
                        <h1 class="o_form_title">Form Title</h1>
                    </div>
                    <div class="o_form_content">
                        <!-- 字段将在这里插入 -->
                    </div>
                    <div class="o_form_footer">
                        <button type="submit" class="btn btn-primary">Save</button>
                        <button type="button" class="btn btn-secondary">Cancel</button>
                    </div>
                </div>
            </form>
        `;
    }

    // 添加字段
    addField(fieldConfig) {
        const fieldTemplate = this.generateFieldTemplate(fieldConfig);

        this.customizations.push({
            type: 'field',
            config: fieldConfig,
            operation: `
                <div class="o_form_content" position="inside">
                    ${fieldTemplate}
                </div>
            `
        });

        return this;
    }

    // 添加分组
    addGroup(groupConfig) {
        this.customizations.push({
            type: 'group',
            config: groupConfig,
            operation: `
                <div class="o_form_content" position="inside">
                    <div class="o_group" data-group="${groupConfig.name}">
                        <h3>${groupConfig.title}</h3>
                        <div class="o_group_content">
                            <!-- 分组内容 -->
                        </div>
                    </div>
                </div>
            `
        });

        return this;
    }

    // 修改样式
    addStyle(selector, styles) {
        const styleAttributes = Object.entries(styles)
            .map(([key, value]) => `<attribute name="${key}" add="${value}"/>`)
            .join('\n');

        this.customizations.push({
            type: 'style',
            operation: `
                <xpath expr="${selector}" position="attributes">
                    ${styleAttributes}
                </xpath>
            `
        });

        return this;
    }

    // 插入自定义HTML
    insertHTML(position, selector, html) {
        this.customizations.push({
            type: 'html',
            operation: `
                <xpath expr="${selector}" position="${position}">
                    ${html}
                </xpath>
            `
        });

        return this;
    }

    // 生成字段模板
    generateFieldTemplate(fieldConfig) {
        const { name, type, label, required, readonly } = fieldConfig;

        const fieldClass = `o_field_${type}`;
        const requiredClass = required ? 'o_required_modifier' : '';
        const readonlyClass = readonly ? 'o_readonly_modifier' : '';

        return `
            <div class="o_field_wrapper ${fieldClass} ${requiredClass} ${readonlyClass}">
                <label class="o_form_label" for="${name}">
                    ${label}${required ? ' *' : ''}
                </label>
                <div class="o_field_widget">
                    ${this.generateFieldWidget(fieldConfig)}
                </div>
            </div>
        `;
    }

    generateFieldWidget(fieldConfig) {
        const { name, type, options = {} } = fieldConfig;

        switch (type) {
            case 'char':
                return `<input type="text" name="${name}" class="o_input" ${options.placeholder ? `placeholder="${options.placeholder}"` : ''}/>`;

            case 'text':
                return `<textarea name="${name}" class="o_input" rows="${options.rows || 3}"></textarea>`;

            case 'integer':
            case 'float':
                return `<input type="number" name="${name}" class="o_input" ${options.step ? `step="${options.step}"` : ''}/>`;

            case 'boolean':
                return `<input type="checkbox" name="${name}" class="o_input"/>`;

            case 'selection':
                const optionsHtml = options.selection?.map(([value, label]) =>
                    `<option value="${value}">${label}</option>`
                ).join('') || '';
                return `<select name="${name}" class="o_input">${optionsHtml}</select>`;

            case 'date':
                return `<input type="date" name="${name}" class="o_input"/>`;

            case 'datetime':
                return `<input type="datetime-local" name="${name}" class="o_input"/>`;

            default:
                return `<input type="text" name="${name}" class="o_input"/>`;
        }
    }

    // 构建最终模板
    build() {
        const parser = new DOMParser();
        const doc = parser.parseFromString(this.baseTemplate, 'text/xml');
        const root = doc.documentElement;

        // 应用所有定制
        for (const customization of this.customizations) {
            const operationDoc = parser.parseFromString(
                `<operations>${customization.operation}</operations>`,
                'text/xml'
            );

            applyInheritance(root, operationDoc.documentElement);
        }

        return new XMLSerializer().serializeToString(root);
    }

    // 预览模板
    preview() {
        const template = this.build();
        console.log('Generated Template:', template);
        return template;
    }

    // 重置构建器
    reset() {
        this.customizations = [];
        return this;
    }
}

// 使用示例
const formBuilder = new DynamicFormBuilder();

const customerForm = formBuilder
    .addField({
        name: 'name',
        type: 'char',
        label: 'Customer Name',
        required: true,
        options: { placeholder: 'Enter customer name' }
    })
    .addField({
        name: 'email',
        type: 'char',
        label: 'Email Address',
        required: true,
        options: { placeholder: '<EMAIL>' }
    })
    .addGroup({
        name: 'contact_info',
        title: 'Contact Information'
    })
    .addField({
        name: 'phone',
        type: 'char',
        label: 'Phone Number',
        options: { placeholder: '+****************' }
    })
    .addField({
        name: 'active',
        type: 'boolean',
        label: 'Active Customer'
    })
    .addStyle('//form', {
        'class': 'enhanced-form',
        'data-form-type': 'customer'
    })
    .insertHTML('after', '//h1[@class="o_form_title"]', `
        <div class="o_form_subtitle">
            <p>Manage customer information</p>
        </div>
    `)
    .build();

console.log('Customer Form Template:', customerForm);
```

### 3. 模板继承管理器
```javascript
class TemplateInheritanceManager {
    constructor() {
        this.templates = new Map();
        this.inheritanceChains = new Map();
        this.compiledTemplates = new Map();
        this.debugMode = false;
    }

    // 注册基础模板
    registerTemplate(id, template) {
        this.templates.set(id, {
            id,
            template,
            type: 'base',
            children: new Set(),
            parent: null
        });

        return this;
    }

    // 注册继承模板
    registerInheritance(id, parentId, operations) {
        if (!this.templates.has(parentId)) {
            throw new Error(`Parent template ${parentId} not found`);
        }

        const parent = this.templates.get(parentId);
        const inheritance = {
            id,
            operations,
            type: 'inheritance',
            parent: parentId,
            children: new Set()
        };

        this.templates.set(id, inheritance);
        parent.children.add(id);

        // 构建继承链
        this.buildInheritanceChain(id);

        return this;
    }

    // 构建继承链
    buildInheritanceChain(templateId) {
        const chain = [];
        let current = templateId;

        while (current) {
            const template = this.templates.get(current);
            if (!template) break;

            chain.unshift(current);
            current = template.parent;
        }

        this.inheritanceChains.set(templateId, chain);
    }

    // 编译模板
    compileTemplate(templateId) {
        if (this.compiledTemplates.has(templateId)) {
            return this.compiledTemplates.get(templateId);
        }

        const chain = this.inheritanceChains.get(templateId);
        if (!chain || chain.length === 0) {
            throw new Error(`Template ${templateId} not found or has no inheritance chain`);
        }

        // 从基础模板开始
        const baseTemplate = this.templates.get(chain[0]);
        if (baseTemplate.type !== 'base') {
            throw new Error(`First template in chain must be base template`);
        }

        const parser = new DOMParser();
        let doc = parser.parseFromString(baseTemplate.template, 'text/xml');
        let root = doc.documentElement;

        // 应用所有继承操作
        for (let i = 1; i < chain.length; i++) {
            const inheritanceTemplate = this.templates.get(chain[i]);
            const operationsDoc = parser.parseFromString(
                `<operations>${inheritanceTemplate.operations}</operations>`,
                'text/xml'
            );

            try {
                root = applyInheritance(root, operationsDoc.documentElement, chain[i]);
            } catch (error) {
                console.error(`Error applying inheritance ${chain[i]}:`, error);
                throw error;
            }
        }

        const compiledTemplate = new XMLSerializer().serializeToString(root);
        this.compiledTemplates.set(templateId, compiledTemplate);

        return compiledTemplate;
    }

    // 获取模板
    getTemplate(templateId) {
        return this.compileTemplate(templateId);
    }

    // 清除编译缓存
    clearCache(templateId = null) {
        if (templateId) {
            this.compiledTemplates.delete(templateId);

            // 清除所有依赖此模板的缓存
            for (const [id, template] of this.templates.entries()) {
                if (template.parent === templateId) {
                    this.clearCache(id);
                }
            }
        } else {
            this.compiledTemplates.clear();
        }

        return this;
    }

    // 获取继承树
    getInheritanceTree(templateId) {
        const template = this.templates.get(templateId);
        if (!template) return null;

        const tree = {
            id: templateId,
            type: template.type,
            children: []
        };

        for (const childId of template.children) {
            const childTree = this.getInheritanceTree(childId);
            if (childTree) {
                tree.children.push(childTree);
            }
        }

        return tree;
    }

    // 验证模板
    validateTemplate(templateId) {
        const errors = [];

        try {
            const compiled = this.compileTemplate(templateId);

            // 验证XML格式
            const parser = new DOMParser();
            const doc = parser.parseFromString(compiled, 'text/xml');
            const parseError = doc.querySelector('parsererror');

            if (parseError) {
                errors.push({
                    type: 'xml_error',
                    message: parseError.textContent
                });
            }

        } catch (error) {
            errors.push({
                type: 'compilation_error',
                message: error.message
            });
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }

    // 调试信息
    getDebugInfo(templateId) {
        const chain = this.inheritanceChains.get(templateId);
        const template = this.templates.get(templateId);

        return {
            templateId,
            type: template?.type,
            inheritanceChain: chain,
            hasCache: this.compiledTemplates.has(templateId),
            children: Array.from(template?.children || []),
            parent: template?.parent
        };
    }

    // 导出配置
    exportConfig() {
        const config = {
            templates: {},
            inheritanceChains: Object.fromEntries(this.inheritanceChains.entries())
        };

        for (const [id, template] of this.templates.entries()) {
            config.templates[id] = {
                id: template.id,
                type: template.type,
                parent: template.parent,
                children: Array.from(template.children),
                template: template.template,
                operations: template.operations
            };
        }

        return config;
    }

    // 导入配置
    importConfig(config) {
        this.templates.clear();
        this.inheritanceChains.clear();
        this.compiledTemplates.clear();

        // 导入模板
        for (const [id, template] of Object.entries(config.templates)) {
            this.templates.set(id, {
                ...template,
                children: new Set(template.children)
            });
        }

        // 导入继承链
        for (const [id, chain] of Object.entries(config.inheritanceChains)) {
            this.inheritanceChains.set(id, chain);
        }

        return this;
    }
}

// 使用示例
const templateManager = new TemplateInheritanceManager();

// 注册基础模板
templateManager.registerTemplate('base_form', `
    <form class="o_form">
        <div class="o_form_sheet">
            <h1>Base Form</h1>
            <div class="o_form_content">
                <!-- 内容区域 -->
            </div>
        </div>
    </form>
`);

// 注册继承模板
templateManager.registerInheritance('enhanced_form', 'base_form', `
    <h1 position="after">
        <p class="form-description">Enhanced form with additional features</p>
    </h1>

    <div class="o_form_content" position="inside">
        <div class="form-group">
            <label>Name:</label>
            <input type="text" name="name" class="form-control"/>
        </div>
    </div>

    <form position="attributes">
        <attribute name="class" add="enhanced"/>
    </form>
`);

// 编译并获取模板
const finalTemplate = templateManager.getTemplate('enhanced_form');
console.log('Final Template:', finalTemplate);

// 获取调试信息
const debugInfo = templateManager.getDebugInfo('enhanced_form');
console.log('Debug Info:', debugInfo);

// 验证模板
const validation = templateManager.validateTemplate('enhanced_form');
console.log('Validation:', validation);
```

## 🛠️ 实践练习

### 练习1: 模板差异分析器
```javascript
class TemplateDiffAnalyzer {
    constructor() {
        this.parser = new DOMParser();
        this.serializer = new XMLSerializer();
    }

    // 分析模板差异
    analyzeDiff(originalTemplate, modifiedTemplate) {
        const original = this.parseTemplate(originalTemplate);
        const modified = this.parseTemplate(modifiedTemplate);

        const differences = [];
        this.compareElements(original.documentElement, modified.documentElement, '', differences);

        return {
            hasDifferences: differences.length > 0,
            differences,
            summary: this.generateSummary(differences)
        };
    }

    parseTemplate(template) {
        return this.parser.parseFromString(template, 'text/xml');
    }

    compareElements(original, modified, path, differences) {
        // 比较标签名
        if (original.tagName !== modified.tagName) {
            differences.push({
                type: 'tag_changed',
                path,
                original: original.tagName,
                modified: modified.tagName
            });
        }

        // 比较属性
        this.compareAttributes(original, modified, path, differences);

        // 比较子元素
        this.compareChildren(original, modified, path, differences);

        // 比较文本内容
        this.compareTextContent(original, modified, path, differences);
    }

    compareAttributes(original, modified, path, differences) {
        const originalAttrs = new Map();
        const modifiedAttrs = new Map();

        // 收集原始属性
        for (const attr of original.attributes) {
            originalAttrs.set(attr.name, attr.value);
        }

        // 收集修改后属性
        for (const attr of modified.attributes) {
            modifiedAttrs.set(attr.name, attr.value);
        }

        // 检查新增和修改的属性
        for (const [name, value] of modifiedAttrs.entries()) {
            if (!originalAttrs.has(name)) {
                differences.push({
                    type: 'attribute_added',
                    path,
                    attribute: name,
                    value
                });
            } else if (originalAttrs.get(name) !== value) {
                differences.push({
                    type: 'attribute_changed',
                    path,
                    attribute: name,
                    original: originalAttrs.get(name),
                    modified: value
                });
            }
        }

        // 检查删除的属性
        for (const [name, value] of originalAttrs.entries()) {
            if (!modifiedAttrs.has(name)) {
                differences.push({
                    type: 'attribute_removed',
                    path,
                    attribute: name,
                    value
                });
            }
        }
    }

    compareChildren(original, modified, path, differences) {
        const originalChildren = Array.from(original.children);
        const modifiedChildren = Array.from(modified.children);

        const maxLength = Math.max(originalChildren.length, modifiedChildren.length);

        for (let i = 0; i < maxLength; i++) {
            const originalChild = originalChildren[i];
            const modifiedChild = modifiedChildren[i];
            const childPath = `${path}/${originalChild?.tagName || modifiedChild?.tagName}[${i}]`;

            if (!originalChild && modifiedChild) {
                differences.push({
                    type: 'element_added',
                    path: childPath,
                    element: this.serializer.serializeToString(modifiedChild)
                });
            } else if (originalChild && !modifiedChild) {
                differences.push({
                    type: 'element_removed',
                    path: childPath,
                    element: this.serializer.serializeToString(originalChild)
                });
            } else if (originalChild && modifiedChild) {
                this.compareElements(originalChild, modifiedChild, childPath, differences);
            }
        }
    }

    compareTextContent(original, modified, path, differences) {
        const originalText = this.getDirectTextContent(original);
        const modifiedText = this.getDirectTextContent(modified);

        if (originalText !== modifiedText) {
            differences.push({
                type: 'text_changed',
                path,
                original: originalText,
                modified: modifiedText
            });
        }
    }

    getDirectTextContent(element) {
        let text = '';
        for (const node of element.childNodes) {
            if (node.nodeType === Node.TEXT_NODE) {
                text += node.textContent;
            }
        }
        return text.trim();
    }

    generateSummary(differences) {
        const summary = {
            total: differences.length,
            byType: {},
            impactLevel: 'low'
        };

        for (const diff of differences) {
            summary.byType[diff.type] = (summary.byType[diff.type] || 0) + 1;
        }

        // 评估影响级别
        if (differences.some(d => d.type.includes('removed'))) {
            summary.impactLevel = 'high';
        } else if (differences.some(d => d.type.includes('added'))) {
            summary.impactLevel = 'medium';
        }

        return summary;
    }

    // 生成修复建议
    generateFixSuggestions(differences) {
        const suggestions = [];

        for (const diff of differences) {
            switch (diff.type) {
                case 'attribute_added':
                    suggestions.push({
                        type: 'add_attribute',
                        description: `Add attribute ${diff.attribute}="${diff.value}" to element at ${diff.path}`,
                        operation: `<xpath expr="${diff.path}" position="attributes"><attribute name="${diff.attribute}">${diff.value}</attribute></xpath>`
                    });
                    break;

                case 'attribute_removed':
                    suggestions.push({
                        type: 'remove_attribute',
                        description: `Remove attribute ${diff.attribute} from element at ${diff.path}`,
                        operation: `<xpath expr="${diff.path}" position="attributes"><attribute name="${diff.attribute}"></attribute></xpath>`
                    });
                    break;

                case 'element_added':
                    suggestions.push({
                        type: 'add_element',
                        description: `Add element at ${diff.path}`,
                        operation: `<xpath expr="${diff.path.split('/').slice(0, -1).join('/')}" position="inside">${diff.element}</xpath>`
                    });
                    break;

                case 'element_removed':
                    suggestions.push({
                        type: 'remove_element',
                        description: `Remove element at ${diff.path}`,
                        operation: `<xpath expr="${diff.path}" position="replace"></xpath>`
                    });
                    break;
            }
        }

        return suggestions;
    }
}

// 使用示例
const diffAnalyzer = new TemplateDiffAnalyzer();

const originalTemplate = `
    <form class="o_form">
        <h1>Original Title</h1>
        <div class="content">
            <p>Original content</p>
        </div>
    </form>
`;

const modifiedTemplate = `
    <form class="o_form enhanced">
        <h1>Modified Title</h1>
        <div class="content">
            <p>Modified content</p>
            <button>New Button</button>
        </div>
    </form>
`;

const analysis = diffAnalyzer.analyzeDiff(originalTemplate, modifiedTemplate);
console.log('Diff Analysis:', analysis);

const suggestions = diffAnalyzer.generateFixSuggestions(analysis.differences);
console.log('Fix Suggestions:', suggestions);
```

### 练习2: 模板性能优化器
```javascript
class TemplatePerformanceOptimizer {
    constructor() {
        this.optimizations = new Map();
        this.metrics = new Map();
    }

    // 分析模板性能
    analyzePerformance(template) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(template, 'text/xml');
        const root = doc.documentElement;

        const analysis = {
            nodeCount: this.countNodes(root),
            depth: this.calculateDepth(root),
            complexSelectors: this.findComplexSelectors(template),
            redundantElements: this.findRedundantElements(root),
            largeAttributes: this.findLargeAttributes(root),
            suggestions: []
        };

        // 生成优化建议
        this.generateOptimizationSuggestions(analysis);

        return analysis;
    }

    countNodes(element) {
        let count = 1;
        for (const child of element.children) {
            count += this.countNodes(child);
        }
        return count;
    }

    calculateDepth(element, currentDepth = 0) {
        let maxDepth = currentDepth;
        for (const child of element.children) {
            const childDepth = this.calculateDepth(child, currentDepth + 1);
            maxDepth = Math.max(maxDepth, childDepth);
        }
        return maxDepth;
    }

    findComplexSelectors(template) {
        const complexSelectors = [];
        const xpathMatches = template.match(/expr="[^"]*"/g) || [];

        for (const match of xpathMatches) {
            const xpath = match.slice(6, -1); // 移除 expr=" 和 "

            // 检查复杂的XPath表达式
            if (xpath.includes('//') && xpath.split('//').length > 2) {
                complexSelectors.push({
                    type: 'deep_descendant',
                    xpath,
                    complexity: 'high'
                });
            }

            if (xpath.includes('[') && xpath.includes(']')) {
                const predicateCount = (xpath.match(/\[/g) || []).length;
                if (predicateCount > 2) {
                    complexSelectors.push({
                        type: 'multiple_predicates',
                        xpath,
                        complexity: 'medium',
                        predicateCount
                    });
                }
            }
        }

        return complexSelectors;
    }

    findRedundantElements(element) {
        const redundant = [];
        const elementMap = new Map();

        this.traverseElements(element, (el, path) => {
            const signature = this.getElementSignature(el);

            if (!elementMap.has(signature)) {
                elementMap.set(signature, []);
            }
            elementMap.get(signature).push({ element: el, path });
        });

        for (const [signature, elements] of elementMap.entries()) {
            if (elements.length > 1) {
                redundant.push({
                    signature,
                    count: elements.length,
                    elements: elements.map(e => e.path)
                });
            }
        }

        return redundant;
    }

    findLargeAttributes(element) {
        const largeAttributes = [];

        this.traverseElements(element, (el, path) => {
            for (const attr of el.attributes) {
                if (attr.value.length > 200) {
                    largeAttributes.push({
                        path,
                        attribute: attr.name,
                        size: attr.value.length,
                        value: attr.value.substring(0, 50) + '...'
                    });
                }
            }
        });

        return largeAttributes;
    }

    traverseElements(element, callback, path = '') {
        const currentPath = path ? `${path}/${element.tagName}` : element.tagName;
        callback(element, currentPath);

        for (const child of element.children) {
            this.traverseElements(child, callback, currentPath);
        }
    }

    getElementSignature(element) {
        const attrs = Array.from(element.attributes)
            .map(attr => `${attr.name}="${attr.value}"`)
            .sort()
            .join(' ');

        return `${element.tagName}[${attrs}]`;
    }

    generateOptimizationSuggestions(analysis) {
        // 节点数量过多
        if (analysis.nodeCount > 1000) {
            analysis.suggestions.push({
                type: 'reduce_nodes',
                priority: 'high',
                description: `Template has ${analysis.nodeCount} nodes. Consider breaking into smaller components.`,
                impact: 'performance'
            });
        }

        // 嵌套深度过深
        if (analysis.depth > 15) {
            analysis.suggestions.push({
                type: 'reduce_depth',
                priority: 'medium',
                description: `Template depth is ${analysis.depth}. Consider flattening the structure.`,
                impact: 'maintainability'
            });
        }

        // 复杂选择器
        if (analysis.complexSelectors.length > 0) {
            analysis.suggestions.push({
                type: 'simplify_selectors',
                priority: 'medium',
                description: `Found ${analysis.complexSelectors.length} complex XPath selectors. Consider using simpler alternatives.`,
                impact: 'performance',
                details: analysis.complexSelectors
            });
        }

        // 冗余元素
        if (analysis.redundantElements.length > 0) {
            analysis.suggestions.push({
                type: 'remove_redundancy',
                priority: 'low',
                description: `Found ${analysis.redundantElements.length} groups of redundant elements. Consider extracting to reusable components.`,
                impact: 'maintainability',
                details: analysis.redundantElements
            });
        }

        // 大属性值
        if (analysis.largeAttributes.length > 0) {
            analysis.suggestions.push({
                type: 'optimize_attributes',
                priority: 'low',
                description: `Found ${analysis.largeAttributes.length} large attribute values. Consider moving to external resources.`,
                impact: 'memory',
                details: analysis.largeAttributes
            });
        }
    }

    // 应用优化
    applyOptimizations(template, optimizations) {
        let optimizedTemplate = template;

        for (const optimization of optimizations) {
            switch (optimization.type) {
                case 'simplify_xpath':
                    optimizedTemplate = this.simplifyXPath(optimizedTemplate, optimization);
                    break;

                case 'extract_component':
                    optimizedTemplate = this.extractComponent(optimizedTemplate, optimization);
                    break;

                case 'merge_attributes':
                    optimizedTemplate = this.mergeAttributes(optimizedTemplate, optimization);
                    break;

                case 'remove_redundancy':
                    optimizedTemplate = this.removeRedundancy(optimizedTemplate, optimization);
                    break;
            }
        }

        return optimizedTemplate;
    }

    simplifyXPath(template, optimization) {
        // 简化复杂的XPath表达式
        const { originalXPath, simplifiedXPath } = optimization;
        return template.replace(
            new RegExp(`expr="${originalXPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}"`, 'g'),
            `expr="${simplifiedXPath}"`
        );
    }

    extractComponent(template, optimization) {
        // 提取重复的模板片段为组件
        const { pattern, componentName } = optimization;
        const componentTemplate = `<t t-call="${componentName}"/>`;

        return template.replace(new RegExp(pattern, 'g'), componentTemplate);
    }

    mergeAttributes(template, optimization) {
        // 合并相似的属性操作
        const { selector, attributes } = optimization;
        const mergedAttributes = attributes.map(attr =>
            `<attribute name="${attr.name}">${attr.value}</attribute>`
        ).join('\n');

        const mergedOperation = `
            <xpath expr="${selector}" position="attributes">
                ${mergedAttributes}
            </xpath>
        `;

        // 替换原有的分散属性操作
        return template.replace(optimization.originalOperations, mergedOperation);
    }

    removeRedundancy(template, optimization) {
        // 移除冗余的模板片段
        const { redundantPatterns } = optimization;

        let result = template;
        for (const pattern of redundantPatterns) {
            result = result.replace(new RegExp(pattern, 'g'), '');
        }

        return result;
    }

    // 性能基准测试
    benchmark(template, iterations = 100) {
        const parser = new DOMParser();
        const serializer = new XMLSerializer();

        const results = {
            parsing: [],
            inheritance: [],
            serialization: []
        };

        for (let i = 0; i < iterations; i++) {
            // 测试解析性能
            const parseStart = performance.now();
            const doc = parser.parseFromString(template, 'text/xml');
            const parseEnd = performance.now();
            results.parsing.push(parseEnd - parseStart);

            // 测试继承应用性能
            const inheritanceStart = performance.now();
            const operations = doc.createElement('operations');
            applyInheritance(doc.documentElement, operations);
            const inheritanceEnd = performance.now();
            results.inheritance.push(inheritanceEnd - inheritanceStart);

            // 测试序列化性能
            const serializeStart = performance.now();
            serializer.serializeToString(doc.documentElement);
            const serializeEnd = performance.now();
            results.serialization.push(serializeEnd - serializeStart);
        }

        return {
            parsing: this.calculateStats(results.parsing),
            inheritance: this.calculateStats(results.inheritance),
            serialization: this.calculateStats(results.serialization),
            total: this.calculateStats([
                ...results.parsing,
                ...results.inheritance,
                ...results.serialization
            ])
        };
    }

    calculateStats(values) {
        const sorted = values.sort((a, b) => a - b);
        const sum = values.reduce((a, b) => a + b, 0);

        return {
            min: Math.min(...values),
            max: Math.max(...values),
            avg: sum / values.length,
            median: sorted[Math.floor(sorted.length / 2)],
            p95: sorted[Math.floor(sorted.length * 0.95)],
            p99: sorted[Math.floor(sorted.length * 0.99)]
        };
    }
}

// 使用示例
const optimizer = new TemplatePerformanceOptimizer();

const complexTemplate = `
    <template id="complex_template">
        <div class="container">
            <xpath expr="//div[@class='header']//span[contains(@class, 'title')]" position="inside">
                <strong>Complex Title</strong>
            </xpath>
            <xpath expr="//div[@class='content']//p[position()>1 and position()<5]" position="after">
                <div class="additional-content">More content</div>
            </xpath>
            <!-- 更多复杂操作... -->
        </div>
    </template>
`;

// 分析性能
const analysis = optimizer.analyzePerformance(complexTemplate);
console.log('Performance Analysis:', analysis);

// 运行基准测试
const benchmark = optimizer.benchmark(complexTemplate, 50);
console.log('Benchmark Results:', benchmark);

// 应用优化
const optimizations = [
    {
        type: 'simplify_xpath',
        originalXPath: '//div[@class="header"]//span[contains(@class, "title")]',
        simplifiedXPath: '.header .title'
    }
];

const optimizedTemplate = optimizer.applyOptimizations(complexTemplate, optimizations);
console.log('Optimized Template:', optimizedTemplate);
```

## 🔧 调试技巧

### 查看模板继承过程
```javascript
function debugTemplateInheritance(root, operations, url) {
    console.group(`Template Inheritance Debug: ${url}`);

    const originalHTML = new XMLSerializer().serializeToString(root);
    console.log('Original Template:', originalHTML);

    // 包装applyInheritance以添加调试信息
    const originalApply = applyInheritance;
    let operationCount = 0;

    for (const operation of operations.children) {
        operationCount++;
        console.group(`Operation ${operationCount}: ${operation.tagName}`);

        const position = operation.getAttribute("position") || "inside";
        console.log('Position:', position);
        console.log('Operation HTML:', operation.outerHTML);

        try {
            const target = getElement(root, operation);
            console.log('Target Element:', target.outerHTML);

            // 应用单个操作
            const tempOperations = document.createElement('operations');
            tempOperations.appendChild(operation.cloneNode(true));

            root = originalApply(root, tempOperations, url);

            const resultHTML = new XMLSerializer().serializeToString(root);
            console.log('Result after operation:', resultHTML);

        } catch (error) {
            console.error('Operation failed:', error);
        }

        console.groupEnd();
    }

    console.log('Final Template:', new XMLSerializer().serializeToString(root));
    console.groupEnd();

    return root;
}

// 使用调试版本
// debugTemplateInheritance(root, operations, 'debug_template.xml');
```

### 模板继承可视化
```javascript
function visualizeTemplateInheritance(templateId) {
    const manager = new TemplateInheritanceManager();
    const tree = manager.getInheritanceTree(templateId);

    function renderTree(node, level = 0) {
        const indent = '  '.repeat(level);
        console.log(`${indent}├─ ${node.id} (${node.type})`);

        for (const child of node.children) {
            renderTree(child, level + 1);
        }
    }

    console.log('Template Inheritance Tree:');
    renderTree(tree);
}

// 可视化继承树
// visualizeTemplateInheritance('my_template');
```

## 📊 性能考虑

### 优化策略
1. **XPath优化**: 使用简单的选择器替代复杂的XPath
2. **批量操作**: 合并多个相似的属性修改操作
3. **缓存机制**: 缓存编译后的模板避免重复处理
4. **懒加载**: 按需编译模板而不是预编译所有

### 最佳实践
```javascript
// ✅ 好的做法：使用简单选择器
<div class="target" position="inside">
    <span>New content</span>
</div>

// ❌ 不好的做法：复杂的XPath
<xpath expr="//div[contains(@class, 'container')]//div[@class='target'][position()=1]" position="inside">
    <span>New content</span>
</xpath>

// ✅ 好的做法：批量属性修改
<div class="target" position="attributes">
    <attribute name="class" add="new-class"/>
    <attribute name="data-value" add="123"/>
</div>

// ❌ 不好的做法：分散的属性操作
<div class="target" position="attributes">
    <attribute name="class" add="new-class"/>
</div>
<div class="target" position="attributes">
    <attribute name="data-value" add="123"/>
</div>
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解模板继承的核心原理和设计思想
- [ ] 掌握各种位置操作的实现机制
- [ ] 理解XPath查询和DOM操作技术
- [ ] 能够创建复杂的模板继承系统
- [ ] 掌握模板性能优化和调试技巧
- [ ] 了解企业级模板系统的架构设计

## 🚀 下一步学习
学完Template Inheritance后，建议继续学习：
1. **模板系统** (`@web/core/templates/`) - 了解完整的模板管理
2. **视图系统** (`@web/views/`) - 学习模板在视图中的应用
3. **组件系统** (`@odoo/owl`) - 理解组件模板的工作原理

## 💡 重要提示
- 模板继承是Odoo视图定制的核心机制
- 理解XPath对精确定位元素很重要
- 文本节点处理是模板继承的复杂部分
- 性能优化对大型模板系统至关重要
```
```

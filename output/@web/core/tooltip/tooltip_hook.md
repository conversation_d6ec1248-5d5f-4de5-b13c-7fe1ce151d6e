# Odoo 工具提示 Hook (Tooltip Hook) 学习资料

## 文件概述

**文件路径**: `output/@web/core/tooltip/tooltip_hook.js`  
**原始路径**: `/web/static/src/core/tooltip/tooltip_hook.js`  
**模块类型**: 核心基础模块 - 工具提示 Hook  
**代码行数**: 18 行  
**依赖关系**: 
- `@web/core/utils/hooks` - 通用 Hook (useService)
- `@odoo/owl` - OWL 框架 (useEffect, useRef)

## 模块功能

工具提示 Hook 是 Odoo Web 客户端工具提示系统的便捷封装，负责：
- 提供声明式的工具提示绑定方式
- 自动处理元素引用和服务调用
- 简化工具提示的使用模式
- 集成 OWL 的响应式系统
- 提供组件级别的工具提示管理

## Hook 架构分析

### 1. Hook 定义
```javascript
function useTooltip(refName, params) {
    const tooltip = useService("tooltip");
    const ref = useRef(refName);
    useEffect(
        (el) => tooltip.add(el, params),
        () => [ref.el]
    );
}
```

**设计特点**:
- **极简接口**: 只需两个参数即可完成工具提示绑定
- **声明式**: 通过 Hook 声明式地添加工具提示
- **自动化**: 自动处理元素引用和服务调用
- **响应式**: 集成 OWL 的响应式更新机制

### 2. 参数分析

#### refName 参数
```javascript
refName: string
```
- **类型**: 字符串
- **用途**: 元素引用的名称
- **对应**: 模板中的 `t-ref` 属性值
- **示例**: `"tooltipTarget"`

#### params 参数
```javascript
params: object
```
- **类型**: 对象
- **用途**: 传递给工具提示服务的参数
- **内容**: 工具提示的配置选项
- **示例**: `{ tooltip: "提示文本", position: "top" }`

### 3. 内部机制

#### 服务依赖注入
```javascript
const tooltip = useService("tooltip");
```
- **依赖注入**: 通过服务系统获取工具提示服务
- **解耦**: Hook 不直接依赖具体的服务实现
- **可测试**: 便于单元测试和模拟

#### 元素引用管理
```javascript
const ref = useRef(refName);
```
- **引用创建**: 创建对应名称的元素引用
- **生命周期**: 与组件生命周期绑定
- **自动清理**: 组件销毁时自动清理

#### 响应式效果
```javascript
useEffect(
    (el) => tooltip.add(el, params),
    () => [ref.el]
);
```
- **依赖追踪**: 监听 `ref.el` 的变化
- **自动执行**: 元素可用时自动添加工具提示
- **清理机制**: 效果函数的返回值用于清理

## 实际使用示例

### 1. 基本工具提示使用
```javascript
import { useTooltip } from "@web/core/tooltip/tooltip_hook";

class BasicTooltipExample extends Component {
    setup() {
        // 为按钮添加工具提示
        useTooltip("saveButton", {
            tooltip: "保存当前文档"
        });
        
        // 为图标添加工具提示
        useTooltip("helpIcon", {
            tooltip: "点击获取帮助信息"
        });
    }
    
    render() {
        return xml`
            <div>
                <button t-ref="saveButton" t-on-click="save">
                    保存
                </button>
                <i t-ref="helpIcon" class="fa fa-question-circle"/>
            </div>
        `;
    }
}
```

### 2. 动态工具提示内容
```javascript
class DynamicTooltipExample extends Component {
    setup() {
        this.state = useState({
            saveStatus: "未保存",
            itemCount: 0
        });
        
        // 动态工具提示内容
        useTooltip("statusIndicator", {
            tooltip: () => `状态: ${this.state.saveStatus}`
        });
        
        useTooltip("itemCounter", {
            tooltip: () => `共有 ${this.state.itemCount} 个项目`
        });
    }
    
    updateStatus(status) {
        this.state.saveStatus = status;
    }
    
    addItem() {
        this.state.itemCount++;
    }
    
    render() {
        return xml`
            <div>
                <span t-ref="statusIndicator" class="status-badge">
                    <t t-esc="state.saveStatus"/>
                </span>
                
                <button t-ref="itemCounter" t-on-click="addItem">
                    添加项目 (<t t-esc="state.itemCount"/>)
                </button>
            </div>
        `;
    }
}
```

### 3. 条件工具提示
```javascript
class ConditionalTooltipExample extends Component {
    setup() {
        this.user = useService("user");
        
        // 根据用户权限显示不同提示
        useTooltip("actionButton", {
            tooltip: () => {
                if (!this.user.hasPermission('edit')) {
                    return "您没有编辑权限";
                }
                return "点击编辑项目";
            }
        });
        
        // 根据状态显示提示
        useTooltip("statusIcon", {
            tooltip: () => this.getStatusTooltip()
        });
    }
    
    getStatusTooltip() {
        const status = this.props.item.status;
        const tooltips = {
            'active': '项目正在进行中',
            'pending': '项目等待审批',
            'completed': '项目已完成',
            'cancelled': '项目已取消'
        };
        return tooltips[status] || '未知状态';
    }
    
    render() {
        return xml`
            <div>
                <button t-ref="actionButton" 
                        t-att-disabled="!user.hasPermission('edit')"
                        t-on-click="editItem">
                    编辑
                </button>
                
                <i t-ref="statusIcon" 
                   t-att-class="'fa fa-circle status-' + props.item.status"/>
            </div>
        `;
    }
}
```

### 4. 复杂工具提示配置
```javascript
class AdvancedTooltipExample extends Component {
    setup() {
        // 带位置配置的工具提示
        useTooltip("topButton", {
            tooltip: "这个提示显示在上方",
            position: "top"
        });
        
        // 带延迟的工具提示
        useTooltip("delayedTooltip", {
            tooltip: "延迟显示的提示",
            delay: 1000
        });
        
        // 模板化工具提示
        useTooltip("richTooltip", {
            template: "custom.RichTooltip",
            info: () => ({
                title: "详细信息",
                content: "这是一个复杂的工具提示",
                items: this.getTooltipItems()
            })
        });
        
        // 持久化工具提示
        useTooltip("persistentTooltip", {
            tooltip: "点击关闭的提示",
            persistent: true
        });
    }
    
    getTooltipItems() {
        return [
            { label: "创建时间", value: "2024-01-01" },
            { label: "修改时间", value: "2024-01-20" },
            { label: "作者", value: "用户名" }
        ];
    }
    
    render() {
        return xml`
            <div class="tooltip-demo">
                <button t-ref="topButton">上方提示</button>
                <button t-ref="delayedTooltip">延迟提示</button>
                <button t-ref="richTooltip">复杂提示</button>
                <button t-ref="persistentTooltip">持久提示</button>
            </div>
        `;
    }
}
```

### 5. 多元素工具提示管理
```javascript
class MultiTooltipExample extends Component {
    setup() {
        this.tooltipConfigs = [
            { ref: "button1", tooltip: "第一个按钮" },
            { ref: "button2", tooltip: "第二个按钮" },
            { ref: "button3", tooltip: "第三个按钮" }
        ];
        
        // 批量添加工具提示
        this.tooltipConfigs.forEach(config => {
            useTooltip(config.ref, {
                tooltip: config.tooltip
            });
        });
        
        // 动态列表的工具提示
        this.setupDynamicTooltips();
    }
    
    setupDynamicTooltips() {
        // 为动态生成的元素添加工具提示
        useEffect(() => {
            this.props.items.forEach((item, index) => {
                const refName = `item_${index}`;
                useTooltip(refName, {
                    tooltip: `项目: ${item.name} - ${item.description}`
                });
            });
        }, () => [this.props.items]);
    }
    
    render() {
        return xml`
            <div>
                <!-- 静态按钮 -->
                <div class="static-buttons">
                    <button t-ref="button1">按钮1</button>
                    <button t-ref="button2">按钮2</button>
                    <button t-ref="button3">按钮3</button>
                </div>
                
                <!-- 动态列表 -->
                <div class="dynamic-items">
                    <div t-foreach="props.items" t-as="item" t-key="item.id"
                         t-ref="'item_' + item_index"
                         class="item">
                        <t t-esc="item.name"/>
                    </div>
                </div>
            </div>
        `;
    }
}
```

### 6. 自定义工具提示 Hook
```javascript
// 创建专用的工具提示 Hook
function useHelpTooltip(refName, helpText) {
    return useTooltip(refName, {
        tooltip: helpText,
        position: "top",
        theme: "help",
        delay: 500
    });
}

function useErrorTooltip(refName, errorMessage) {
    return useTooltip(refName, {
        tooltip: errorMessage,
        position: "bottom",
        theme: "error",
        persistent: true
    });
}

function useStatusTooltip(refName, getStatus) {
    return useTooltip(refName, {
        tooltip: () => {
            const status = getStatus();
            return `当前状态: ${status.label} (${status.description})`;
        },
        position: "right"
    });
}

// 使用自定义 Hook
class CustomHookExample extends Component {
    setup() {
        // 使用帮助工具提示
        useHelpTooltip("helpButton", "点击获取详细帮助信息");
        
        // 使用错误工具提示
        useErrorTooltip("errorField", "此字段包含无效数据");
        
        // 使用状态工具提示
        useStatusTooltip("statusIndicator", () => this.getCurrentStatus());
    }
    
    getCurrentStatus() {
        return {
            label: "运行中",
            description: "系统正常运行"
        };
    }
    
    render() {
        return xml`
            <div>
                <button t-ref="helpButton" class="help-btn">
                    <i class="fa fa-question"/>
                </button>
                
                <input t-ref="errorField" class="error-field"/>
                
                <span t-ref="statusIndicator" class="status-indicator">
                    状态
                </span>
            </div>
        `;
    }
}
```

### 7. 工具提示组合 Hook
```javascript
// 组合多个工具提示功能的 Hook
function useTooltipGroup(tooltips) {
    tooltips.forEach(({ ref, ...params }) => {
        useTooltip(ref, params);
    });
}

function useFormTooltips(fields) {
    const tooltips = fields.map(field => ({
        ref: field.name,
        tooltip: field.help || `请输入${field.label}`,
        position: "top"
    }));
    
    useTooltipGroup(tooltips);
}

function useActionTooltips(actions) {
    const tooltips = actions.map(action => ({
        ref: action.ref,
        tooltip: action.description,
        position: "bottom"
    }));
    
    useTooltipGroup(tooltips);
}

// 使用组合 Hook
class FormWithTooltips extends Component {
    setup() {
        const formFields = [
            { name: "name", label: "姓名", help: "请输入您的真实姓名" },
            { name: "email", label: "邮箱", help: "请输入有效的邮箱地址" },
            { name: "phone", label: "电话" }
        ];
        
        const formActions = [
            { ref: "saveBtn", description: "保存表单数据" },
            { ref: "cancelBtn", description: "取消并返回" },
            { ref: "resetBtn", description: "重置表单到初始状态" }
        ];
        
        // 批量添加表单字段工具提示
        useFormTooltips(formFields);
        
        // 批量添加操作按钮工具提示
        useActionTooltips(formActions);
    }
    
    render() {
        return xml`
            <form>
                <div class="form-group">
                    <label>姓名</label>
                    <input t-ref="name" type="text"/>
                </div>
                
                <div class="form-group">
                    <label>邮箱</label>
                    <input t-ref="email" type="email"/>
                </div>
                
                <div class="form-group">
                    <label>电话</label>
                    <input t-ref="phone" type="tel"/>
                </div>
                
                <div class="form-actions">
                    <button t-ref="saveBtn" type="submit">保存</button>
                    <button t-ref="cancelBtn" type="button">取消</button>
                    <button t-ref="resetBtn" type="reset">重置</button>
                </div>
            </form>
        `;
    }
}
```

## 设计模式分析

### 1. Hook 模式 (Hook Pattern)
```javascript
function useTooltip(refName, params) {
    // Hook 逻辑
}
```

**优势**:
- **逻辑复用**: 可以在多个组件中复用工具提示逻辑
- **关注分离**: 将工具提示逻辑从组件中分离
- **组合性**: 可以与其他 Hook 组合使用

### 2. 适配器模式 (Adapter Pattern)
```javascript
useEffect(
    (el) => tooltip.add(el, params),
    () => [ref.el]
);
```

**作用**:
- **接口适配**: 将服务接口适配到 Hook 接口
- **简化使用**: 隐藏复杂的服务调用细节
- **统一接口**: 提供一致的使用方式

### 3. 依赖注入模式 (Dependency Injection)
```javascript
const tooltip = useService("tooltip");
```

**特点**:
- **解耦**: Hook 不直接依赖具体服务实现
- **可测试**: 便于单元测试和模拟
- **可配置**: 可以注入不同的服务实现

### 4. 声明式模式 (Declarative Pattern)
```javascript
// 声明式地添加工具提示
useTooltip("buttonRef", { tooltip: "提示文本" });
```

**优势**:
- **简洁**: 一行代码完成工具提示绑定
- **直观**: 代码意图清晰明确
- **维护**: 易于理解和维护

## 性能优化

### 1. 依赖优化
```javascript
useEffect(
    (el) => tooltip.add(el, params),
    () => [ref.el] // 只依赖元素引用
);
```

**优化特点**:
- **精确依赖**: 只在元素变化时重新执行
- **避免重复**: 避免不必要的重复调用
- **内存效率**: 及时清理不需要的效果

### 2. 参数缓存
```javascript
// 可以通过 useMemo 缓存复杂参数
const tooltipParams = useMemo(() => ({
    tooltip: computeExpensiveTooltip(),
    position: "top"
}), [dependency]);

useTooltip("ref", tooltipParams);
```

### 3. 条件执行
```javascript
// 只在需要时添加工具提示
if (shouldShowTooltip) {
    useTooltip("ref", params);
}
```

## 最佳实践

### 1. 引用命名
```javascript
// ✅ 推荐：使用描述性的引用名称
useTooltip("saveButton", { tooltip: "保存文档" });
useTooltip("helpIcon", { tooltip: "获取帮助" });

// ❌ 避免：使用模糊的引用名称
useTooltip("ref1", { tooltip: "提示" });
useTooltip("element", { tooltip: "信息" });
```

### 2. 参数结构
```javascript
// ✅ 推荐：结构化的参数对象
useTooltip("button", {
    tooltip: "明确的提示文本",
    position: "top",
    delay: 500
});

// ❌ 避免：不完整的参数
useTooltip("button", { tooltip: "" }); // 空提示文本
```

### 3. 动态内容
```javascript
// ✅ 推荐：使用函数返回动态内容
useTooltip("status", {
    tooltip: () => `当前状态: ${this.state.status}`
});

// ❌ 避免：静态内容无法更新
useTooltip("status", {
    tooltip: `当前状态: ${this.state.status}` // 不会更新
});
```

### 4. 错误处理
```javascript
// ✅ 推荐：提供降级方案
useTooltip("element", {
    tooltip: () => {
        try {
            return this.getComplexTooltip();
        } catch (error) {
            console.error("工具提示生成失败:", error);
            return "信息不可用";
        }
    }
});

// ❌ 避免：忽略可能的错误
useTooltip("element", {
    tooltip: () => this.getComplexTooltip() // 可能抛出错误
});
```

## 扩展和自定义

### 1. 增强的工具提示 Hook
```javascript
function useEnhancedTooltip(refName, options) {
    const { 
        content, 
        position = "top", 
        delay = 0, 
        persistent = false,
        theme = "default",
        onShow,
        onHide
    } = options;
    
    const tooltip = useService("tooltip");
    const ref = useRef(refName);
    
    useEffect((el) => {
        if (!el) return;
        
        const params = {
            tooltip: typeof content === 'function' ? content : () => content,
            position,
            delay,
            persistent,
            theme,
            onShow,
            onHide
        };
        
        return tooltip.add(el, params);
    }, () => [ref.el]);
}
```

### 2. 工具提示管理器 Hook
```javascript
function useTooltipManager() {
    const tooltips = useRef(new Map());
    
    const addTooltip = (refName, params) => {
        useTooltip(refName, params);
        tooltips.current.set(refName, params);
    };
    
    const removeTooltip = (refName) => {
        tooltips.current.delete(refName);
    };
    
    const updateTooltip = (refName, newParams) => {
        if (tooltips.current.has(refName)) {
            tooltips.current.set(refName, newParams);
            // 重新添加工具提示
            addTooltip(refName, newParams);
        }
    };
    
    const getTooltip = (refName) => {
        return tooltips.current.get(refName);
    };
    
    return {
        addTooltip,
        removeTooltip,
        updateTooltip,
        getTooltip
    };
}
```

## 总结

工具提示 Hook 是 Odoo Web 客户端工具提示系统的便捷封装，它提供了：
- **声明式接口**: 通过简单的 Hook 调用添加工具提示
- **自动化管理**: 自动处理元素引用和服务调用
- **响应式更新**: 集成 OWL 的响应式系统
- **简化使用**: 大大简化了工具提示的使用模式
- **高度可复用**: 可以在任何组件中轻松使用

这个 Hook 为开发者提供了更加便捷、直观的工具提示使用方式，是现代 React-like 开发模式在 Odoo 中的优秀实践。

# Odoo Tooltip 模块架构总览与使用指南

## 📁 模块概览

**目录路径**: `output/@web/core/tooltip/`  
**模块类型**: 核心基础模块 - 工具提示系统  
**功能范围**: 帮助提示、状态说明、信息展示等工具提示功能

## 🏗️ 架构图

```
@web/core/tooltip/
├── 📄 tooltip.js           # 工具提示组件
├── 📄 tooltip_hook.js      # 工具提示 Hook
└── 📄 tooltip_service.js   # 工具提示服务
```

## 🎯 模块功能矩阵

| 模块 | 主要功能 | 核心API | 依赖关系 |
|------|----------|---------|----------|
| **tooltip.js** | 内容显示 | `Tooltip` 组件 | owl |
| **tooltip_hook.js** | Hook 封装 | `useTooltip()` | hooks, owl |
| **tooltip_service.js** | 全局管理 | `add()`, data属性支持 | browser, registry, tooltip, feature_detection, owl |

## 🔄 数据流图

```mermaid
graph TD
    A[用户交互] --> B{交互类型}
    
    B -->|鼠标悬停| C[mouseenter事件]
    B -->|触摸操作| D[touchstart事件]
    B -->|API调用| E[tooltip.add()]
    
    C --> F[onMouseenter]
    D --> G[onTouchStart]
    E --> H[elementsWithTooltips]
    
    F --> I[openElementsTooltip]
    G --> I
    H --> I
    
    I --> J{检查配置}
    J -->|data属性| K[解析data属性]
    J -->|API注册| L[使用注册配置]
    
    K --> M[openTooltip]
    L --> M
    
    M --> N[延迟定时器]
    N --> O[popover.add]
    O --> P[Tooltip组件]
    P --> Q[内容显示]
    
    R[mouseleave/touchend] --> S[cleanup]
    S --> T[关闭工具提示]
    
    U[定期检查] --> V[shouldCleanup]
    V --> S
```

## 🚀 快速开始

### 1. 声明式工具提示（推荐）
```html
<!-- 基本文本工具提示 -->
<button data-tooltip="保存当前文档">保存</button>

<!-- 带位置的工具提示 -->
<button data-tooltip="删除项目" data-tooltip-position="top">删除</button>

<!-- 自定义延迟 -->
<button data-tooltip="即时显示" data-tooltip-delay="0">快速提示</button>

<!-- 模板工具提示 -->
<span data-tooltip-template="user.info"
      data-tooltip-info='{"name":"张三","role":"管理员"}'>
    用户信息
</span>

<!-- 触摸设备优化 -->
<button data-tooltip="点击显示" 
        data-tooltip-touch-tap-to-show="true">
    移动端按钮
</button>
```

### 2. Hook 方式使用
```javascript
import { useTooltip } from "@web/core/tooltip/tooltip_hook";

class HookExample extends Component {
    setup() {
        // 为按钮添加工具提示
        useTooltip("saveButton", {
            tooltip: "保存当前文档"
        });
        
        // 动态内容工具提示
        useTooltip("statusIcon", {
            tooltip: () => `状态: ${this.state.status}`
        });
    }
    
    render() {
        return xml`
            <div>
                <button t-ref="saveButton">保存</button>
                <i t-ref="statusIcon" class="fa fa-info"/>
            </div>
        `;
    }
}
```

### 3. 服务 API 使用
```javascript
import { useService } from "@web/core/utils/hooks";

class ServiceExample extends Component {
    setup() {
        this.tooltip = useService("tooltip");
        this.buttonRef = useRef("button");
    }
    
    onMounted() {
        // 编程式添加工具提示
        this.removeTooltip = this.tooltip.add(this.buttonRef.el, {
            tooltip: "这是通过API添加的工具提示",
            position: "bottom",
            delay: 200
        });
    }
    
    onWillUnmount() {
        this.removeTooltip?.();
    }
    
    render() {
        return xml`<button t-ref="button">API工具提示</button>`;
    }
}
```

## 📚 核心概念详解

### 1. 工具提示组件 (Tooltip Component)
- **纯展示组件**: 专注于内容显示，无业务逻辑
- **双模式支持**: 支持纯文本和模板化内容
- **极简设计**: 最小化的组件实现，高性能
- **统一接口**: 通过 props 提供一致的配置

### 2. 工具提示 Hook (Tooltip Hook)
- **声明式绑定**: 通过 Hook 声明式地添加工具提示
- **自动化管理**: 自动处理元素引用和服务调用
- **响应式更新**: 集成 OWL 的响应式系统
- **简化使用**: 大大简化工具提示的使用模式

### 3. 工具提示服务 (Tooltip Service)
- **全局管理**: 统一管理整个应用的工具提示
- **事件委托**: 高效的全局事件处理机制
- **设备适配**: 针对触摸和桌面设备的优化
- **生命周期**: 完整的工具提示生命周期管理

### 4. Data 属性系统
- **声明式配置**: 通过 HTML 属性配置工具提示
- **零代码**: 无需编写 JavaScript 代码
- **动态解析**: 运行时解析属性配置
- **向后兼容**: 与传统 title 属性兼容

## 🔧 高级用法

### 1. 自定义工具提示模板
```javascript
// 定义自定义模板
class RichTooltipTemplate extends Component {
    static template = xml`
        <div class="rich-tooltip">
            <div class="tooltip-header">
                <h6 t-esc="props.info.title"/>
                <span class="badge" t-esc="props.info.badge"/>
            </div>
            <div class="tooltip-body">
                <p t-esc="props.info.description"/>
                <ul t-if="props.info.items">
                    <li t-foreach="props.info.items" t-as="item" t-key="item.id">
                        <strong t-esc="item.label"/>: <span t-esc="item.value"/>
                    </li>
                </ul>
            </div>
            <div class="tooltip-footer" t-if="props.info.actions">
                <button t-foreach="props.info.actions" t-as="action" t-key="action_index"
                        t-on-click="action.onClick"
                        class="btn btn-sm btn-outline-primary">
                    <t t-esc="action.label"/>
                </button>
            </div>
        </div>
    `;
    
    static props = {
        info: Object,
        close: Function
    };
}

// 注册模板
registry.category("tooltip_templates").add("rich_tooltip", RichTooltipTemplate);

// 使用自定义模板
<div data-tooltip-template="rich_tooltip"
     data-tooltip-info='{"title":"用户信息","badge":"VIP","description":"高级用户"}'>
    用户头像
</div>
```

### 2. 工具提示管理器
```javascript
class TooltipManager {
    constructor(tooltipService) {
        this.tooltip = tooltipService;
        this.activeTooltips = new Map();
        this.cache = new Map();
    }
    
    addTooltip(element, config) {
        const key = this.getElementKey(element);
        
        // 移除已存在的工具提示
        this.removeTooltip(key);
        
        const removeTooltip = this.tooltip.add(element, config);
        this.activeTooltips.set(key, removeTooltip);
        
        return removeTooltip;
    }
    
    removeTooltip(key) {
        const removeTooltip = this.activeTooltips.get(key);
        if (removeTooltip) {
            removeTooltip();
            this.activeTooltips.delete(key);
        }
    }
    
    updateTooltip(element, newConfig) {
        const key = this.getElementKey(element);
        this.removeTooltip(key);
        return this.addTooltip(element, newConfig);
    }
    
    addCachedTooltip(element, cacheKey, configFactory) {
        if (this.cache.has(cacheKey)) {
            return this.addTooltip(element, this.cache.get(cacheKey));
        }
        
        const config = configFactory();
        this.cache.set(cacheKey, config);
        return this.addTooltip(element, config);
    }
    
    clearCache(cacheKey = null) {
        if (cacheKey) {
            this.cache.delete(cacheKey);
        } else {
            this.cache.clear();
        }
    }
    
    removeAllTooltips() {
        for (const removeTooltip of this.activeTooltips.values()) {
            removeTooltip();
        }
        this.activeTooltips.clear();
    }
    
    getElementKey(element) {
        return element.dataset.tooltipKey || 
               element.id || 
               `element_${Date.now()}_${Math.random()}`;
    }
}
```

### 3. 异步内容工具提示
```javascript
class AsyncTooltipLoader {
    constructor(tooltipService, httpService) {
        this.tooltip = tooltipService;
        this.http = httpService;
        this.loadingCache = new Map();
        this.contentCache = new Map();
    }
    
    async addAsyncTooltip(element, url, options = {}) {
        const cacheKey = `${url}_${JSON.stringify(options)}`;
        
        // 检查内容缓存
        if (this.contentCache.has(cacheKey)) {
            const cachedContent = this.contentCache.get(cacheKey);
            return this.tooltip.add(element, {
                ...options,
                ...cachedContent
            });
        }
        
        // 检查是否正在加载
        if (this.loadingCache.has(cacheKey)) {
            return this.loadingCache.get(cacheKey);
        }
        
        // 显示加载状态
        const loadingRemove = this.tooltip.add(element, {
            tooltip: options.loadingText || "加载中...",
            position: options.position || "top"
        });
        
        // 创建加载Promise
        const loadingPromise = this.loadContent(url, options)
            .then(content => {
                // 缓存内容
                this.contentCache.set(cacheKey, content);
                
                // 移除加载工具提示
                loadingRemove();
                
                // 显示内容工具提示
                return this.tooltip.add(element, {
                    ...options,
                    ...content
                });
            })
            .catch(error => {
                // 移除加载工具提示
                loadingRemove();
                
                // 显示错误工具提示
                return this.tooltip.add(element, {
                    tooltip: options.errorText || "加载失败",
                    position: options.position || "top"
                });
            })
            .finally(() => {
                // 清理加载缓存
                this.loadingCache.delete(cacheKey);
            });
        
        // 缓存加载Promise
        this.loadingCache.set(cacheKey, loadingPromise);
        
        return loadingPromise;
    }
    
    async loadContent(url, options) {
        const response = await this.http.get(url);
        
        if (options.template) {
            return {
                template: options.template,
                info: response.data
            };
        } else {
            return {
                tooltip: response.data.content || response.data.toString()
            };
        }
    }
    
    clearCache() {
        this.contentCache.clear();
        this.loadingCache.clear();
    }
}
```

### 4. 响应式工具提示系统
```javascript
class ReactiveTooltipSystem extends Component {
    setup() {
        this.tooltip = useService("tooltip");
        this.state = useState({
            tooltips: new Map(),
            globalConfig: {
                delay: 400,
                position: "top"
            }
        });
        
        // 监听全局配置变化
        useEffect(() => {
            this.updateAllTooltips();
        }, () => [this.state.globalConfig]);
    }
    
    addReactiveTooltip(element, config) {
        const key = this.getElementKey(element);
        
        // 创建响应式配置
        const reactiveConfig = reactive({
            ...this.state.globalConfig,
            ...config
        });
        
        // 监听配置变化
        const stopWatching = watch(
            () => reactiveConfig,
            () => this.updateTooltip(element, reactiveConfig),
            { deep: true }
        );
        
        // 添加工具提示
        const removeTooltip = this.tooltip.add(element, reactiveConfig);
        
        // 存储状态
        this.state.tooltips.set(key, {
            element,
            config: reactiveConfig,
            removeTooltip,
            stopWatching
        });
        
        return () => {
            removeTooltip();
            stopWatching();
            this.state.tooltips.delete(key);
        };
    }
    
    updateTooltip(element, config) {
        const key = this.getElementKey(element);
        const tooltipData = this.state.tooltips.get(key);
        
        if (tooltipData) {
            // 移除旧工具提示
            tooltipData.removeTooltip();
            
            // 添加新工具提示
            const removeTooltip = this.tooltip.add(element, config);
            
            // 更新状态
            tooltipData.removeTooltip = removeTooltip;
        }
    }
    
    updateAllTooltips() {
        for (const [key, tooltipData] of this.state.tooltips) {
            this.updateTooltip(tooltipData.element, tooltipData.config);
        }
    }
    
    updateGlobalConfig(newConfig) {
        Object.assign(this.state.globalConfig, newConfig);
    }
    
    onWillUnmount() {
        // 清理所有工具提示
        for (const [key, tooltipData] of this.state.tooltips) {
            tooltipData.removeTooltip();
            tooltipData.stopWatching();
        }
        this.state.tooltips.clear();
    }
}
```

### 5. 工具提示主题系统
```javascript
class TooltipThemeSystem {
    constructor(tooltipService) {
        this.tooltip = tooltipService;
        this.themes = new Map();
        this.currentTheme = "default";
        
        // 注册默认主题
        this.registerTheme("default", {
            position: "top",
            delay: 400,
            className: "tooltip-default"
        });
        
        this.registerTheme("success", {
            position: "top",
            delay: 200,
            className: "tooltip-success"
        });
        
        this.registerTheme("error", {
            position: "bottom",
            delay: 0,
            className: "tooltip-error"
        });
        
        this.registerTheme("info", {
            position: "right",
            delay: 600,
            className: "tooltip-info"
        });
    }
    
    registerTheme(name, config) {
        this.themes.set(name, config);
    }
    
    addThemedTooltip(element, content, themeName = this.currentTheme) {
        const themeConfig = this.themes.get(themeName) || this.themes.get("default");
        
        const config = {
            ...themeConfig,
            tooltip: content
        };
        
        return this.tooltip.add(element, config);
    }
    
    setGlobalTheme(themeName) {
        if (this.themes.has(themeName)) {
            this.currentTheme = themeName;
        }
    }
    
    createThemedTooltipDirective(themeName) {
        return (element, content) => {
            return this.addThemedTooltip(element, content, themeName);
        };
    }
}

// 使用主题系统
const themeSystem = new TooltipThemeSystem(tooltipService);

// 添加不同主题的工具提示
themeSystem.addThemedTooltip(element, "成功消息", "success");
themeSystem.addThemedTooltip(element, "错误消息", "error");
themeSystem.addThemedTooltip(element, "信息提示", "info");

// 创建主题化的工具提示函数
const addSuccessTooltip = themeSystem.createThemedTooltipDirective("success");
const addErrorTooltip = themeSystem.createThemedTooltipDirective("error");
```

## 🎨 最佳实践

### 1. 内容设计原则
```javascript
// ✅ 推荐：简洁明确的工具提示
data-tooltip="保存当前文档"
data-tooltip="删除选中的项目"
data-tooltip="导出为PDF格式"

// ❌ 避免：模糊或冗长的工具提示
data-tooltip="操作"
data-tooltip="这个按钮用于执行某些操作，点击后会发生一些事情"
```

### 2. 位置选择策略
```javascript
// ✅ 推荐：根据界面布局选择合适位置
const positionStrategies = {
    topBar: "bottom",        // 顶部工具栏向下显示
    sidebar: "right",        // 侧边栏向右显示
    bottomBar: "top",        // 底部工具栏向上显示
    centerContent: "top"     // 中心内容向上显示
};

// ❌ 避免：所有工具提示使用相同位置
data-tooltip-position="top" // 可能被界面边界遮挡
```

### 3. 延迟时间配置
```javascript
// ✅ 推荐：根据内容类型设置合适延迟
const delayStrategies = {
    quickAction: 0,          // 快速操作，无延迟
    standardInfo: 400,       // 标准信息，默认延迟
    detailedHelp: 800,       // 详细帮助，较长延迟
    warningMessage: 200      // 警告信息，较短延迟
};

// ❌ 避免：不合理的延迟设置
data-tooltip-delay="2000"    // 过长延迟影响用户体验
data-tooltip-delay="-100"    // 无效的负数延迟
```

### 4. 触摸设备优化
```javascript
// ✅ 推荐：为触摸设备提供合适的交互
class TouchOptimizedTooltips extends Component {
    setup() {
        this.hasTouch = hasTouch();
    }
    
    getTooltipConfig(content) {
        if (this.hasTouch) {
            return {
                tooltip: content,
                delay: 0,
                touchTapToShow: true
            };
        } else {
            return {
                tooltip: content,
                delay: 400
            };
        }
    }
}

// ❌ 避免：忽略触摸设备的特殊需求
// 触摸设备上悬停事件行为不一致
```

### 5. 性能优化策略
```javascript
// ✅ 推荐：合理使用缓存和批量操作
class OptimizedTooltips extends Component {
    setup() {
        this.tooltip = useService("tooltip");
        this.tooltipCache = new Map();
        this.batchOperations = [];
    }
    
    addCachedTooltip(element, cacheKey, configFactory) {
        if (this.tooltipCache.has(cacheKey)) {
            return this.tooltip.add(element, this.tooltipCache.get(cacheKey));
        }
        
        const config = configFactory();
        this.tooltipCache.set(cacheKey, config);
        return this.tooltip.add(element, config);
    }
    
    batchAddTooltips(operations) {
        // 批量添加工具提示，减少DOM操作
        const removeCallbacks = operations.map(({ element, config }) => {
            return this.tooltip.add(element, config);
        });
        
        return () => {
            removeCallbacks.forEach(remove => remove());
        };
    }
}

// ❌ 避免：频繁的单个操作
// 每次都重新计算配置，没有缓存机制
```

## ⚡ 性能优化

### 1. 事件委托优化
```javascript
// 服务内部使用事件委托，避免为每个元素添加监听器
document.body.addEventListener("mouseenter", onMouseenter, { capture: true });
```

### 2. 延迟加载和缓存
```javascript
// 延迟显示避免频繁创建
openTooltipTimeout = browser.setTimeout(() => {
    // 创建工具提示
}, delay);

// 缓存配置避免重复解析
const elementsWithTooltips = new Map();
```

### 3. DOM 检查优化
```javascript
// 显示前检查元素连接状态
if (target.isConnected) {
    // 创建工具提示
}

// 定期清理无效的工具提示
browser.setInterval(() => {
    if (shouldCleanup()) {
        cleanup();
    }
}, CLOSE_DELAY);
```

### 4. 内存管理
```javascript
// 自动清理机制
function cleanup() {
    browser.clearTimeout(openTooltipTimeout);
    if (closeTooltip) {
        closeTooltip();
    }
}

// 组件卸载时清理
onWillUnmount() {
    this.removeTooltip?.();
}
```

## 🔍 调试技巧

### 1. 工具提示状态监控
```javascript
// 开发环境下的工具提示监控
if (odoo.debug) {
    const originalAdd = tooltipService.add;
    tooltipService.add = function(element, config) {
        console.group('💬 Tooltip Added');
        console.log('Element:', element);
        console.log('Config:', config);
        console.log('Stack:', new Error().stack);
        console.groupEnd();
        
        const remove = originalAdd.call(this, element, config);
        
        return () => {
            console.log('💬 Tooltip Removed:', element);
            remove();
        };
    };
}
```

### 2. Data 属性验证工具
```javascript
class TooltipValidator {
    static validateDataAttributes() {
        const elements = document.querySelectorAll('[data-tooltip], [data-tooltip-template]');
        const issues = [];
        
        elements.forEach(el => {
            // 检查必需属性
            if (!el.dataset.tooltip && !el.dataset.tooltipTemplate) {
                issues.push({
                    element: el,
                    issue: 'Missing tooltip content'
                });
            }
            
            // 检查位置属性
            if (el.dataset.tooltipPosition) {
                const validPositions = ['top', 'bottom', 'left', 'right'];
                if (!validPositions.includes(el.dataset.tooltipPosition)) {
                    issues.push({
                        element: el,
                        issue: `Invalid position: ${el.dataset.tooltipPosition}`
                    });
                }
            }
            
            // 检查延迟属性
            if (el.dataset.tooltipDelay) {
                const delay = parseInt(el.dataset.tooltipDelay, 10);
                if (isNaN(delay) || delay < 0) {
                    issues.push({
                        element: el,
                        issue: `Invalid delay: ${el.dataset.tooltipDelay}`
                    });
                }
            }
            
            // 检查JSON格式
            if (el.dataset.tooltipInfo) {
                try {
                    JSON.parse(el.dataset.tooltipInfo);
                } catch (error) {
                    issues.push({
                        element: el,
                        issue: `Invalid JSON in tooltip-info: ${error.message}`
                    });
                }
            }
        });
        
        if (issues.length > 0) {
            console.warn('Tooltip validation issues:', issues);
        }
        
        return issues;
    }
}
```

### 3. 性能分析工具
```javascript
class TooltipPerformanceMonitor {
    constructor() {
        this.metrics = {
            totalTooltips: 0,
            averageDelay: 0,
            peakConcurrent: 0,
            currentActive: 0
        };
        this.delays = [];
        this.startTimes = new Map();
    }
    
    onTooltipAdd(element, config) {
        this.metrics.totalTooltips++;
        this.metrics.currentActive++;
        this.metrics.peakConcurrent = Math.max(
            this.metrics.peakConcurrent,
            this.metrics.currentActive
        );
        
        this.delays.push(config.delay || 400);
        this.metrics.averageDelay = 
            this.delays.reduce((a, b) => a + b, 0) / this.delays.length;
        
        this.startTimes.set(element, performance.now());
    }
    
    onTooltipRemove(element) {
        this.metrics.currentActive--;
        
        const startTime = this.startTimes.get(element);
        if (startTime) {
            const duration = performance.now() - startTime;
            console.log(`Tooltip lifetime: ${duration.toFixed(2)}ms`);
            this.startTimes.delete(element);
        }
    }
    
    getReport() {
        return {
            ...this.metrics,
            memoryUsage: this.startTimes.size
        };
    }
}
```

## 📖 相关资源

- [OWL 组件文档](https://github.com/odoo/owl)
- [Web Accessibility Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [Touch Events Specification](https://www.w3.org/TR/touch-events/)
- [CSS Positioning Guide](https://developer.mozilla.org/en-US/docs/Web/CSS/position)

## 🎯 总结

Odoo Tooltip 模块是一个完整的工具提示解决方案，提供了：
- **三层架构**: 组件、Hook、服务的清晰分层设计
- **多种使用方式**: 声明式、Hook、API三种使用模式
- **设备适配**: 针对触摸和桌面设备的优化
- **高性能**: 事件委托、延迟加载、缓存机制等优化
- **易于使用**: 简洁的API和丰富的配置选项
- **完善生命周期**: 自动的内存管理和资源清理

这个模块为 Odoo 的用户界面提供了专业、可靠的工具提示功能，支持从简单的帮助文本到复杂的交互式内容，是提升用户体验的重要基础设施。

# Odoo 工具提示服务 (Tooltip Service) 学习资料

## 文件概述

**文件路径**: `output/@web/core/tooltip/tooltip_service.js`  
**原始路径**: `/web/static/src/core/tooltip/tooltip_service.js`  
**模块类型**: 核心基础模块 - 工具提示服务  
**代码行数**: 236 行  
**依赖关系**: 
- `@web/core/browser/browser` - 浏览器抽象层
- `@web/core/registry` - 服务注册表
- `@web/core/tooltip/tooltip` - 工具提示组件
- `@web/core/browser/feature_detection` - 特性检测
- `@odoo/owl` - OWL 框架 (whenReady)

## 模块功能

工具提示服务是 Odoo Web 客户端工具提示系统的核心服务，负责：
- 提供全局的工具提示管理和显示
- 支持基于 data 属性的声明式工具提示
- 处理鼠标和触摸设备的交互事件
- 管理工具提示的生命周期和清理
- 集成弹出框服务实现工具提示显示

## 核心常量

### 时间常量
```javascript
const OPEN_DELAY = 400;   // 打开延迟：400毫秒
const CLOSE_DELAY = 200;  // 清理检查间隔：200毫秒
```

**设计考虑**:
- **OPEN_DELAY**: 避免鼠标快速划过时频繁显示工具提示
- **CLOSE_DELAY**: 定期检查目标元素是否仍在DOM中

## 服务架构分析

### 1. 服务定义
```javascript
const tooltipService = {
    dependencies: ["popover"],
    start(env, { popover }) {
        // 服务启动逻辑
    }
};
```

**设计特点**:
- **依赖注入**: 依赖弹出框服务实现显示
- **全局管理**: 管理整个应用的工具提示
- **事件驱动**: 基于DOM事件的交互处理

### 2. 状态管理
```javascript
let openTooltipTimeout;      // 打开工具提示的定时器
let closeTooltip;            // 关闭当前工具提示的函数
let target = null;           // 当前目标元素
let touchPressed;            // 触摸状态
let mouseEntered;            // 鼠标进入状态
const elementsWithTooltips = new Map(); // 注册的工具提示元素
```

**状态说明**:
- **openTooltipTimeout**: 延迟显示的定时器ID
- **closeTooltip**: 当前活动工具提示的关闭函数
- **target**: 当前触发工具提示的目标元素
- **touchPressed/mouseEntered**: 交互状态标志
- **elementsWithTooltips**: 通过API注册的元素映射

## 核心功能分析

### 1. cleanup() 函数
```javascript
function cleanup() {
    browser.clearTimeout(openTooltipTimeout);
    if (closeTooltip) {
        closeTooltip();
    }
}
```

**功能**:
- **清理定时器**: 取消待执行的打开操作
- **关闭工具提示**: 关闭当前显示的工具提示
- **状态重置**: 重置相关状态变量

### 2. shouldCleanup() 函数
```javascript
function shouldCleanup() {
    if (!target) {
        return false;
    }
    if (!document.body.contains(target)) {
        return true; // 目标不再在DOM中
    }
    if (hasTouch() && !mouseEntered) {
        return !touchPressed;
    }
    return false;
}
```

**清理条件**:
- **无目标**: 没有当前目标元素
- **DOM移除**: 目标元素已从DOM中移除
- **触摸设备**: 触摸结束且鼠标未进入

### 3. openTooltip() 函数
```javascript
function openTooltip(el, { tooltip = "", template, info, position, delay = OPEN_DELAY }) {
    target = el;
    cleanup();
    if (!tooltip && !template) {
        return;
    }

    openTooltipTimeout = browser.setTimeout(() => {
        if (target.isConnected) {
            closeTooltip = popover.add(
                target,
                Tooltip,
                { tooltip, template, info },
                { position }
            );
            target.title = ""; // 防止原生title同时显示
        }
    }, delay);
}
```

**参数说明**:
- **el**: 目标元素
- **tooltip**: 简单文本内容
- **template**: 模板名称
- **info**: 模板数据
- **position**: 显示位置
- **delay**: 延迟时间

**执行流程**:
1. **设置目标**: 记录当前目标元素
2. **清理状态**: 清理之前的工具提示
3. **内容检查**: 验证是否有有效内容
4. **延迟显示**: 设置定时器延迟显示
5. **DOM检查**: 显示前再次检查元素连接状态
6. **创建工具提示**: 通过弹出框服务创建
7. **禁用原生**: 清空title属性避免冲突

### 4. openElementsTooltip() 函数
```javascript
function openElementsTooltip(el) {
    if (el.nodeType === Node.TEXT_NODE) {
        return; // 修复Firefox文本节点事件问题
    }
    if (elementsWithTooltips.has(el)) {
        openTooltip(el, elementsWithTooltips.get(el));
    } else if (el.matches("[data-tooltip], [data-tooltip-template]")) {
        const dataset = el.dataset;
        const params = {
            tooltip: dataset.tooltip,
            template: dataset.tooltipTemplate,
            position: dataset.tooltipPosition,
        };
        if (dataset.tooltipInfo) {
            params.info = JSON.parse(dataset.tooltipInfo);
        }
        if (dataset.tooltipDelay) {
            params.delay = parseInt(dataset.tooltipDelay, 10);
        }
        openTooltip(el, params);
    }
}
```

**处理逻辑**:
1. **节点类型检查**: 过滤文本节点
2. **注册元素**: 优先使用API注册的配置
3. **属性解析**: 解析data属性配置
4. **参数构建**: 构建工具提示参数
5. **JSON解析**: 解析info数据
6. **数值转换**: 转换delay为数字

## 事件处理机制

### 1. 鼠标事件处理
```javascript
function onMouseenter(ev) {
    mouseEntered = true;
    openElementsTooltip(ev.target);
}

function onMouseleave(ev) {
    if (target === ev.target) {
        mouseEntered = false;
        cleanup();
    }
}
```

**鼠标交互**:
- **mouseenter**: 设置状态并尝试打开工具提示
- **mouseleave**: 清理状态并关闭工具提示

### 2. 触摸事件处理
```javascript
function onTouchStart(ev) {
    touchPressed = true;
    openElementsTooltip(ev.target);
}

// touchend 和 touchcancel 事件处理
document.body.addEventListener("touchend", (ev) => {
    if (ev.target.matches("[data-tooltip], [data-tooltip-template]")) {
        if (!ev.target.dataset.tooltipTouchTapToShow) {
            touchPressed = false;
        }
    }
});
```

**触摸交互**:
- **touchstart**: 开始触摸，尝试显示工具提示
- **touchend/touchcancel**: 结束触摸，根据配置决定是否关闭

### 3. 全局事件监听
```javascript
whenReady(() => {
    // 定期清理检查
    browser.setInterval(() => {
        if (shouldCleanup()) {
            cleanup();
        }
    }, CLOSE_DELAY);

    // 触摸设备事件
    if (hasTouch()) {
        document.body.addEventListener("touchstart", onTouchStart);
        // 其他触摸事件...
    }

    // 鼠标事件（使用事件委托）
    document.body.addEventListener("mouseenter", onMouseenter, { capture: true });
    document.body.addEventListener("mouseleave", onMouseleave, { capture: true });
});
```

**事件委托优势**:
- **性能优化**: 只需要一个全局监听器
- **动态支持**: 支持动态添加的元素
- **内存效率**: 避免为每个元素添加监听器

## Data 属性支持

### 1. 基本属性
```html
<!-- 基本文本工具提示 -->
<button data-tooltip="这是一个工具提示">按钮</button>

<!-- 指定位置 -->
<button data-tooltip="左侧提示" data-tooltip-position="left">按钮</button>

<!-- 自定义延迟 -->
<button data-tooltip="即时显示" data-tooltip-delay="0">按钮</button>
```

### 2. 模板属性
```html
<!-- 模板工具提示 -->
<button data-tooltip-template="custom.template" 
        data-tooltip-info='{"name":"用户","role":"管理员"}'>
    用户信息
</button>
```

### 3. 触摸设备属性
```html
<!-- 触摸设备点击显示 -->
<button data-tooltip="点击显示" 
        data-tooltip-touch-tap-to-show="true">
    移动端按钮
</button>
```

## 实际使用示例

### 1. 声明式工具提示
```javascript
class DeclarativeTooltipExample extends Component {
    render() {
        return xml`
            <div>
                <!-- 基本文本工具提示 -->
                <button data-tooltip="保存当前文档">保存</button>
                
                <!-- 带位置的工具提示 -->
                <button data-tooltip="删除项目" 
                        data-tooltip-position="top">删除</button>
                
                <!-- 即时显示的工具提示 -->
                <button data-tooltip="立即显示的提示" 
                        data-tooltip-delay="0">快速提示</button>
                
                <!-- 模板工具提示 -->
                <span data-tooltip-template="user.info"
                      data-tooltip-info='{"name":"张三","role":"管理员","lastLogin":"2024-01-20"}'>
                    用户信息
                </span>
            </div>
        `;
    }
}
```

### 2. 编程式工具提示
```javascript
class ProgrammaticTooltipExample extends Component {
    setup() {
        this.tooltip = useService("tooltip");
        this.buttonRef = useRef("button");
        this.dynamicRef = useRef("dynamic");
    }
    
    onMounted() {
        // 为按钮添加工具提示
        this.removeButtonTooltip = this.tooltip.add(this.buttonRef.el, {
            tooltip: "这是通过API添加的工具提示",
            position: "bottom"
        });
        
        // 为动态元素添加工具提示
        this.removeDynamicTooltip = this.tooltip.add(this.dynamicRef.el, {
            template: "dynamic.tooltip",
            info: () => this.getDynamicInfo(),
            delay: 200
        });
    }
    
    onWillUnmount() {
        // 清理工具提示
        this.removeButtonTooltip?.();
        this.removeDynamicTooltip?.();
    }
    
    getDynamicInfo() {
        return {
            timestamp: new Date().toLocaleString(),
            status: this.state.status,
            count: this.state.count
        };
    }
    
    render() {
        return xml`
            <div>
                <button t-ref="button">API工具提示</button>
                <span t-ref="dynamic">动态工具提示</span>
            </div>
        `;
    }
}
```

### 3. 条件工具提示
```javascript
class ConditionalTooltipExample extends Component {
    setup() {
        this.tooltip = useService("tooltip");
        this.user = useService("user");
    }
    
    onMounted() {
        // 根据权限显示不同工具提示
        this.elements.forEach(el => {
            const action = el.dataset.action;
            if (!this.user.hasPermission(action)) {
                this.tooltip.add(el, {
                    tooltip: `您没有${action}权限`,
                    position: "top"
                });
            } else {
                this.tooltip.add(el, {
                    tooltip: `点击执行${action}操作`,
                    position: "top"
                });
            }
        });
    }
    
    render() {
        return xml`
            <div>
                <button data-action="edit">编辑</button>
                <button data-action="delete">删除</button>
                <button data-action="share">分享</button>
            </div>
        `;
    }
}
```

### 4. 动态内容工具提示
```javascript
class DynamicContentExample extends Component {
    setup() {
        this.tooltip = useService("tooltip");
        this.http = useService("http");
        this.cache = new Map();
    }
    
    async addAsyncTooltip(element, dataId) {
        // 先添加加载状态工具提示
        const removeLoading = this.tooltip.add(element, {
            tooltip: "加载中...",
            delay: 100
        });
        
        try {
            // 检查缓存
            let data;
            if (this.cache.has(dataId)) {
                data = this.cache.get(dataId);
            } else {
                data = await this.http.get(`/api/tooltip-data/${dataId}`);
                this.cache.set(dataId, data);
            }
            
            // 移除加载工具提示
            removeLoading();
            
            // 添加内容工具提示
            return this.tooltip.add(element, {
                template: "async.tooltip",
                info: data,
                position: "right"
            });
            
        } catch (error) {
            // 移除加载工具提示
            removeLoading();
            
            // 添加错误工具提示
            return this.tooltip.add(element, {
                tooltip: "加载失败，请重试",
                position: "top"
            });
        }
    }
    
    render() {
        return xml`
            <div>
                <div t-foreach="items" t-as="item" t-key="item.id"
                     t-on-mouseenter="(ev) => this.addAsyncTooltip(ev.target, item.id)"
                     class="item">
                    <t t-esc="item.name"/>
                </div>
            </div>
        `;
    }
}
```

### 5. 触摸设备优化
```javascript
class TouchOptimizedExample extends Component {
    setup() {
        this.tooltip = useService("tooltip");
        this.hasTouch = hasTouch();
    }
    
    onMounted() {
        this.elements.forEach(el => {
            if (this.hasTouch) {
                // 触摸设备使用点击显示
                el.dataset.tooltipTouchTapToShow = "true";
                this.tooltip.add(el, {
                    tooltip: "点击查看详情",
                    delay: 0 // 触摸设备无延迟
                });
            } else {
                // 桌面设备使用悬停显示
                this.tooltip.add(el, {
                    tooltip: "悬停查看详情",
                    delay: 400
                });
            }
        });
    }
    
    render() {
        return xml`
            <div>
                <div t-foreach="items" t-as="item" t-key="item.id"
                     class="touch-item">
                    <t t-esc="item.name"/>
                </div>
            </div>
        `;
    }
}
```

## 设计模式分析

### 1. 服务模式 (Service Pattern)
```javascript
const tooltipService = {
    dependencies: ["popover"],
    start(env, { popover }) {
        return { add };
    }
};
```

**优势**:
- **全局管理**: 统一管理所有工具提示
- **依赖注入**: 通过服务系统注入依赖
- **生命周期**: 与应用生命周期绑定

### 2. 事件委托模式 (Event Delegation)
```javascript
document.body.addEventListener("mouseenter", onMouseenter, { capture: true });
```

**优势**:
- **性能优化**: 只需要一个全局监听器
- **动态支持**: 自动支持动态添加的元素
- **内存效率**: 避免为每个元素添加监听器

### 3. 策略模式 (Strategy Pattern)
```javascript
// 根据设备类型选择不同的交互策略
if (hasTouch()) {
    // 触摸设备策略
} else {
    // 桌面设备策略
}
```

**应用**:
- **设备适配**: 不同设备使用不同交互方式
- **显示策略**: 不同的延迟和触发方式
- **内容策略**: 文本模式vs模板模式

### 4. 观察者模式 (Observer Pattern)
```javascript
browser.setInterval(() => {
    if (shouldCleanup()) {
        cleanup();
    }
}, CLOSE_DELAY);
```

**实现**:
- **状态监控**: 定期检查目标元素状态
- **自动清理**: 自动清理无效的工具提示
- **内存安全**: 防止内存泄漏

## 性能优化

### 1. 事件委托优化
```javascript
// 使用事件委托避免大量事件监听器
document.body.addEventListener("mouseenter", onMouseenter, { capture: true });
```

### 2. 延迟加载
```javascript
// 延迟显示避免频繁创建
openTooltipTimeout = browser.setTimeout(() => {
    // 创建工具提示
}, delay);
```

### 3. 状态缓存
```javascript
// 缓存元素配置避免重复解析
const elementsWithTooltips = new Map();
```

### 4. DOM检查优化
```javascript
// 显示前检查元素连接状态
if (target.isConnected) {
    // 创建工具提示
}
```

## 最佳实践

### 1. 属性使用
```javascript
// ✅ 推荐：使用语义化的工具提示
<button data-tooltip="保存当前文档">保存</button>

// ❌ 避免：模糊的工具提示
<button data-tooltip="操作">按钮</button>
```

### 2. 延迟设置
```javascript
// ✅ 推荐：根据内容复杂度设置延迟
data-tooltip-delay="0"    // 简单提示，无延迟
data-tooltip-delay="400"  // 标准延迟
data-tooltip-delay="800"  // 复杂内容，较长延迟

// ❌ 避免：所有工具提示使用相同延迟
```

### 3. 触摸设备优化
```javascript
// ✅ 推荐：为触摸设备提供合适的交互
if (hasTouch()) {
    element.dataset.tooltipTouchTapToShow = "true";
}

// ❌ 避免：忽略触摸设备的特殊需求
```

### 4. 内存管理
```javascript
// ✅ 推荐：正确清理工具提示
onWillUnmount() {
    this.removeTooltip?.();
}

// ❌ 避免：忘记清理注册的工具提示
```

## 总结

工具提示服务是 Odoo Web 客户端工具提示系统的核心服务，它提供了：
- **全局管理**: 统一管理整个应用的工具提示
- **声明式支持**: 通过data属性的声明式工具提示
- **设备适配**: 针对触摸和桌面设备的优化
- **性能优化**: 事件委托、延迟加载、状态缓存等优化
- **生命周期管理**: 完整的工具提示生命周期和清理机制

这个服务为 Odoo 的用户界面提供了可靠、高效的工具提示功能，是用户体验的重要组成部分。

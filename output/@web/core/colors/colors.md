# Colors - 颜色工具模块

## 概述

`colors.js` 是 Odoo Web 核心模块的颜色工具模块，提供了完整的颜色管理和处理功能。该模块包含了多套精心设计的调色板、颜色格式转换工具、主题适配功能和颜色操作函数，为Odoo Web应用的数据可视化、图表绘制和界面主题提供了专业的颜色解决方案。

## 文件信息
- **路径**: `/web/static/src/core/colors/colors.js`
- **行数**: 223
- **模块**: `@web/core/colors/colors`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/numbers'    // 数字工具（clamp函数）
```

## 核心功能

### 1. 调色板定义

#### 企业版调色板
```javascript
const COLORS_ENT_BRIGHT = ["#875A7B", "#A5D8D7", "#DCD0D9"];
const COLORS_ENT_DARK = ["#6B3E66", "#147875", "#5A395A"];
```

**企业版调色板功能**:
- **明亮主题**: 适用于明亮主题的企业级颜色
- **暗色主题**: 适用于暗色主题的企业级颜色
- **品牌一致性**: 保持Odoo企业版的品牌色彩一致性

#### 标准调色板
```javascript
const COLORS_SM = [
    "#4EA7F2", // Blue
    "#EA6175", // Red
    "#43C5B1", // Teal
    "#F4A261", // Orange
    "#8481DD", // Purple
    "#FFD86D", // Yellow
];
```

**小型调色板功能**:
- **6种颜色**: 适用于简单的数据可视化
- **高对比度**: 确保颜色之间有良好的对比度
- **色彩平衡**: 涵盖色谱的主要区域

#### 扩展调色板
```javascript
const COLORS_MD = [...]; // 12种颜色
const COLORS_LG = [...]; // 24种颜色
const COLORS_XL = [...]; // 32种颜色
```

**扩展调色板功能**:
- **渐进扩展**: 从6色到32色的渐进扩展
- **色调变化**: 每种基础颜色提供多个色调
- **丰富选择**: 满足复杂数据可视化需求

### 2. 颜色获取函数

```javascript
function getColors(colorScheme, paletteName) {
    switch (paletteName) {
        case "odoo":
            return colorScheme === "dark" ? COLORS_ENT_DARK : COLORS_ENT_BRIGHT;
        case "sm":
            return COLORS_SM;
        case "md":
            return COLORS_MD;
        case "lg":
            return COLORS_LG;
        default:
            return COLORS_XL;
    }
}
```

**颜色获取功能**:
- **调色板选择**: 根据名称选择对应的调色板
- **主题适配**: 根据颜色方案选择明亮或暗色版本
- **默认处理**: 提供默认的超大调色板

### 3. 智能颜色选择

```javascript
function getColor(index, colorScheme, paletteSizeOrName) {
    let paletteName;
    if (paletteSizeOrName === "odoo") {
        paletteName = "odoo";
    } else if (paletteSizeOrName <= 6 || paletteSizeOrName === "sm") {
        paletteName = "sm";
    } else if (paletteSizeOrName <= 12 || paletteSizeOrName === "md") {
        paletteName = "md";
    } else if (paletteSizeOrName <= 24 || paletteSizeOrName === "lg") {
        paletteName = "lg";
    } else {
        paletteName = "xl";
    }
    const colors = getColors(colorScheme, paletteName);
    return colors[index % colors.length];
}
```

**智能选择功能**:
- **自动适配**: 根据需要的颜色数量自动选择合适的调色板
- **循环使用**: 超出调色板范围时循环使用颜色
- **灵活参数**: 支持数字和字符串参数

### 4. 颜色格式转换

```javascript
function hexToRGBA(hex, opacity) {
    const rgb = RGB_REGEX.exec(hex)
        .slice(1, 4)
        .map((n) => parseInt(n, 16))
        .join(",");
    return `rgba(${rgb},${opacity})`;
}
```

**格式转换功能**:
- **HEX到RGBA**: 将十六进制颜色转换为RGBA格式
- **透明度支持**: 支持设置透明度值
- **正则解析**: 使用正则表达式解析十六进制颜色

### 5. 主题颜色适配

```javascript
function getCustomColor(colorScheme, brightModeColor, darkModeColor) {
    if (darkModeColor === undefined) {
        return brightModeColor;
    } else {
        return colorScheme === "dark" ? darkModeColor : brightModeColor;
    }
}
```

**主题适配功能**:
- **双主题支持**: 支持明亮和暗色主题
- **智能选择**: 根据当前主题自动选择合适的颜色
- **降级处理**: 未提供暗色版本时使用明亮版本

### 6. 颜色操作函数

#### 颜色变亮
```javascript
function lightenColor(color, factor) {
    factor = clamp(factor, 0, 1);
    
    let r = parseInt(color.substring(1, 3), 16);
    let g = parseInt(color.substring(3, 5), 16);
    let b = parseInt(color.substring(5, 7), 16);
    
    r = Math.round(r + (255 - r) * factor);
    g = Math.round(g + (255 - g) * factor);
    b = Math.round(b + (255 - b) * factor);
    
    return `#${r.toString(16).padStart(2, "0")}${g.toString(16).padStart(2, "0")}${b.toString(16).padStart(2, "0")}`;
}
```

#### 颜色变暗
```javascript
function darkenColor(color, factor) {
    factor = clamp(factor, 0, 1);
    
    let r = parseInt(color.substring(1, 3), 16);
    let g = parseInt(color.substring(3, 5), 16);
    let b = parseInt(color.substring(5, 7), 16);
    
    r = Math.round(r * (1 - factor));
    g = Math.round(g * (1 - factor));
    b = Math.round(b * (1 - factor));
    
    return `#${r.toString(16).padStart(2, "0")}${g.toString(16).padStart(2, "0")}${b.toString(16).padStart(2, "0")}`;
}
```

**颜色操作功能**:
- **安全范围**: 使用clamp确保因子在有效范围内
- **RGB分解**: 将十六进制颜色分解为RGB分量
- **数学计算**: 使用数学公式计算新的颜色值
- **格式化输出**: 确保输出格式的正确性

## 使用场景

### 1. 图表颜色配置

```javascript
// 为图表配置颜色
const chartColors = getColors("light", "md");
const pieChartConfig = {
    data: {
        datasets: [{
            backgroundColor: chartColors,
            data: [10, 20, 30, 40]
        }]
    }
};

// 动态获取颜色
const seriesColors = [];
for (let i = 0; i < dataSeriesCount; i++) {
    seriesColors.push(getColor(i, "light", dataSeriesCount));
}
```

### 2. 主题适配

```javascript
// 根据主题获取合适的颜色
const backgroundColor = getCustomColor(
    currentTheme,
    "#FFFFFF", // 明亮主题背景色
    "#2F3349"  // 暗色主题背景色
);

const textColor = getCustomColor(
    currentTheme,
    "#000000", // 明亮主题文字色
    "#FFFFFF"  // 暗色主题文字色
);
```

### 3. 颜色操作

```javascript
// 创建悬停效果颜色
const baseColor = "#4EA7F2";
const hoverColor = lightenColor(baseColor, 0.2);
const activeColor = darkenColor(baseColor, 0.1);

// 创建带透明度的颜色
const overlayColor = hexToRGBA(baseColor, 0.3);
```

### 4. 数据可视化

```javascript
// 为不同数据系列分配颜色
function assignColorsToSeries(dataSeries) {
    return dataSeries.map((series, index) => ({
        ...series,
        color: getColor(index, this.colorScheme, dataSeries.length),
        backgroundColor: hexToRGBA(
            getColor(index, this.colorScheme, dataSeries.length),
            0.2
        )
    }));
}
```

## 增强示例

```javascript
// 增强的颜色工具
const EnhancedColorUtils = {
    // 颜色缓存
    colorCache: new Map(),
    
    // 获取缓存的颜色
    getCachedColor(key, generator) {
        if (!this.colorCache.has(key)) {
            this.colorCache.set(key, generator());
        }
        return this.colorCache.get(key);
    },
    
    // 生成渐变色
    generateGradient(startColor, endColor, steps) {
        const gradient = [];
        const start = this.hexToRgb(startColor);
        const end = this.hexToRgb(endColor);
        
        for (let i = 0; i < steps; i++) {
            const ratio = i / (steps - 1);
            const r = Math.round(start.r + (end.r - start.r) * ratio);
            const g = Math.round(start.g + (end.g - start.g) * ratio);
            const b = Math.round(start.b + (end.b - start.b) * ratio);
            
            gradient.push(this.rgbToHex(r, g, b));
        }
        
        return gradient;
    },
    
    // 十六进制转RGB
    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    },
    
    // RGB转十六进制
    rgbToHex(r, g, b) {
        return "#" + [r, g, b].map(x => {
            const hex = x.toString(16);
            return hex.length === 1 ? "0" + hex : hex;
        }).join("");
    },
    
    // 获取对比色
    getContrastColor(backgroundColor) {
        const rgb = this.hexToRgb(backgroundColor);
        const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
        return brightness > 128 ? "#000000" : "#FFFFFF";
    },
    
    // 颜色混合
    blendColors(color1, color2, ratio = 0.5) {
        const rgb1 = this.hexToRgb(color1);
        const rgb2 = this.hexToRgb(color2);
        
        const r = Math.round(rgb1.r * (1 - ratio) + rgb2.r * ratio);
        const g = Math.round(rgb1.g * (1 - ratio) + rgb2.g * ratio);
        const b = Math.round(rgb1.b * (1 - ratio) + rgb2.b * ratio);
        
        return this.rgbToHex(r, g, b);
    },
    
    // 生成调色板
    generatePalette(baseColor, variations = 5) {
        const palette = [baseColor];
        
        // 生成更亮的变化
        for (let i = 1; i <= variations; i++) {
            const factor = i / (variations + 1);
            palette.unshift(lightenColor(baseColor, factor));
        }
        
        // 生成更暗的变化
        for (let i = 1; i <= variations; i++) {
            const factor = i / (variations + 1);
            palette.push(darkenColor(baseColor, factor));
        }
        
        return palette;
    },
    
    // 颜色和谐度检查
    checkColorHarmony(colors) {
        const harmony = {
            contrast: true,
            accessibility: true,
            balance: true
        };
        
        // 检查对比度
        for (let i = 0; i < colors.length - 1; i++) {
            for (let j = i + 1; j < colors.length; j++) {
                if (this.getColorDistance(colors[i], colors[j]) < 50) {
                    harmony.contrast = false;
                }
            }
        }
        
        return harmony;
    },
    
    // 计算颜色距离
    getColorDistance(color1, color2) {
        const rgb1 = this.hexToRgb(color1);
        const rgb2 = this.hexToRgb(color2);
        
        const rDiff = rgb1.r - rgb2.r;
        const gDiff = rgb1.g - rgb2.g;
        const bDiff = rgb1.b - rgb2.b;
        
        return Math.sqrt(rDiff * rDiff + gDiff * gDiff + bDiff * bDiff);
    }
};

// 使用示例
const gradient = EnhancedColorUtils.generateGradient("#FF0000", "#0000FF", 10);
const palette = EnhancedColorUtils.generatePalette("#4EA7F2", 3);
const contrastColor = EnhancedColorUtils.getContrastColor("#4EA7F2");
```

## 技术特点

### 1. 科学配色
- 基于色彩理论的调色板设计
- 确保颜色间的良好对比度
- 支持色盲友好的颜色选择

### 2. 主题适配
- 明亮和暗色主题支持
- 自动颜色适配
- 品牌色彩一致性

### 3. 性能优化
- 预定义的调色板避免运行时计算
- 高效的颜色转换算法
- 最小化的依赖关系

### 4. 灵活扩展
- 支持自定义调色板
- 可扩展的颜色操作函数
- 模块化的设计

## 设计模式

### 1. 工厂模式 (Factory Pattern)
- 根据参数生成合适的颜色
- 统一的颜色创建接口

### 2. 策略模式 (Strategy Pattern)
- 不同主题的颜色策略
- 可配置的调色板选择

### 3. 单例模式 (Singleton Pattern)
- 全局的颜色配置
- 统一的颜色管理

## 注意事项

1. **颜色对比度**: 确保颜色间有足够的对比度
2. **无障碍性**: 考虑色盲用户的需求
3. **品牌一致性**: 保持与品牌色彩的一致性
4. **性能影响**: 避免频繁的颜色计算

## 扩展建议

1. **色彩空间**: 支持HSL、LAB等更多色彩空间
2. **智能配色**: 基于AI的智能配色建议
3. **颜色分析**: 颜色和谐度和可读性分析
4. **动态主题**: 支持动态主题切换
5. **颜色管理**: 完整的颜色管理系统

该颜色工具模块为Odoo Web应用提供了专业的颜色管理功能，通过科学的配色方案和完善的颜色操作工具确保了优秀的视觉体验和品牌一致性。

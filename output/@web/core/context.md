# @web/core/context.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/context.js`
- **原始路径**: `/web/static/src/core/context.js`
- **代码行数**: 86行
- **作用**: 处理Odoo上下文的创建、合并和动态求值，支持Python表达式的解析和执行

## 🎯 学习目标
通过学习这个文件，您将掌握：
- Odoo上下文系统的工作原理和设计思想
- Python表达式在JavaScript中的求值机制
- 上下文合并和继承的策略
- 部分求值和错误处理的技巧
- AST（抽象语法树）的基础应用

## 📚 核心概念

### 什么是Context？
在Odoo中，Context是一个**键值对象**，用于传递执行环境信息：
- **用户信息**: 当前用户ID、语言、时区等
- **业务状态**: 当前公司、部门、项目等
- **UI状态**: 视图模式、过滤条件等
- **动态计算**: 基于Python表达式的动态值

### Context的类型定义
```javascript
/**
 * @typedef {{[key: string]: any}} Context - 上下文对象
 * @typedef {Context | string | undefined} ContextDescription - 上下文描述
 */

// 示例Context对象
const context = {
    uid: 1,                    // 用户ID
    lang: "en_US",            // 语言
    tz: "UTC",                // 时区
    allowed_company_ids: [1], // 允许的公司ID
    active_id: 123,           // 当前记录ID
    active_ids: [123, 124],   // 当前记录ID列表
    default_customer: true,   // 默认值
    search_default_open: 1    // 搜索默认过滤器
};
```

### 基本使用模式
```javascript
// 导入context模块
const { makeContext, evalPartialContext } = require("@web/core/context");

// 创建合并的上下文
const mergedContext = makeContext([
    { uid: 1, lang: "en_US" },           // 基础上下文
    "{'active_id': uid, 'today': datetime.date.today()}", // Python表达式
    { custom_flag: true }                 // 额外上下文
]);

// 部分求值上下文
const partialContext = evalPartialContext(
    "{'computed_value': active_id * 2, 'unknown_var': some_undefined_var}",
    { active_id: 100 }
);
// 结果: { computed_value: 200 } (unknown_var被忽略)
```

## 🔍 核心方法详解

### 1. makeContext() - 创建合并上下文
```javascript
function makeContext(contexts, initialEvaluationContext) {
    const evaluationContext = Object.assign({}, initialEvaluationContext);
    const context = {};
    
    for (let ctx of contexts) {
        if (ctx !== "") {
            // 1. 处理字符串类型的上下文（Python表达式）
            ctx = typeof ctx === "string" ? evaluateExpr(ctx, evaluationContext) : ctx;
            
            // 2. 合并到结果上下文
            Object.assign(context, ctx);
            
            // 3. 更新求值上下文（关键设计）
            Object.assign(evaluationContext, context);
        }
    }
    return context;
}
```

**工作流程分析**：
1. **初始化**: 创建求值上下文和结果上下文
2. **遍历处理**: 逐个处理输入的上下文描述
3. **类型判断**: 字符串类型使用Python求值器处理
4. **合并策略**: 后面的上下文覆盖前面的同名键
5. **上下文传播**: 已求值的结果影响后续求值

**关键设计点**：
```javascript
Object.assign(evaluationContext, context); // 这行代码很关键！
```
这确保了后续的Python表达式可以引用前面已经计算出的值。

### 2. evalPartialContext() - 部分求值上下文
```javascript
function evalPartialContext(_context, evaluationContext = {}) {
    const ast = parseExpr(_context);  // 解析为AST
    const context = {};
    
    for (const key in ast.value) {
        const value = ast.value[key];
        
        // 1. 检查是否所有变量都可用
        if (getPartialNames(value).some((name) => 
            !(name in evaluationContext || name in BUILTINS)
        )) {
            continue; // 跳过无法求值的键
        }
        
        try {
            // 2. 尝试求值
            context[key] = evaluate(value, evaluationContext);
        } catch {
            // 3. 忽略求值失败的键
        }
    }
    return context;
}
```

**部分求值策略**：
- **变量检查**: 使用AST分析检查所需变量是否可用
- **安全求值**: 只求值可以安全计算的表达式
- **错误忽略**: 求值失败的键被静默忽略
- **渐进式**: 支持不完整的求值上下文

### 3. getPartialNames() - 提取变量名
```javascript
function getPartialNames(ast) {
    if (ast.type === 5) {        // Name节点
        return [ast.value];
    }
    if (ast.type === 6) {        // UnaryOp节点
        return getPartialNames(ast.right);
    }
    if (ast.type === 14 || ast.type === 7) { // BinOp节点
        return getPartialNames(ast.left).concat(getPartialNames(ast.right));
    }
    if (ast.type === 15) {       // Attribute节点
        return getPartialNames(ast.obj);
    }
    return [];
}
```

**AST节点类型**：
- **Type 5**: 变量名节点 (如 `uid`, `active_id`)
- **Type 6**: 一元操作符 (如 `-x`, `not flag`)
- **Type 7/14**: 二元操作符 (如 `a + b`, `x and y`)
- **Type 15**: 属性访问 (如 `obj.attr`)

## 🎨 实际应用场景

### 1. 视图上下文处理
```javascript
class FormView extends Component {
    setup() {
        this.context = makeContext([
            this.env.context,                    // 全局上下文
            this.props.context,                  // 视图上下文
            "{'default_state': 'draft'}",        // 默认值
            "{'active_id': active_id}"           // 动态值
        ], {
            active_id: this.props.resId,
            uid: this.env.services.user.userId
        });
    }
    
    async loadRecord() {
        // 使用合并后的上下文加载记录
        const record = await this.orm.read(
            this.props.resModel,
            [this.props.resId],
            [],
            { context: this.context }
        );
    }
}
```

### 2. 动作上下文处理
```javascript
class ActionManager {
    async executeAction(action) {
        // 合并动作上下文
        const actionContext = makeContext([
            this.env.context,                    // 环境上下文
            action.context || {},                // 动作定义的上下文
            "{'active_model': active_model}",    // 动态模型名
            "{'active_ids': active_ids}"         // 动态ID列表
        ], {
            active_model: action.res_model,
            active_ids: action.res_ids || [],
            uid: this.env.services.user.userId
        });
        
        // 执行动作时使用合并的上下文
        return this.doAction(action, { context: actionContext });
    }
}
```

### 3. 搜索上下文处理
```javascript
class SearchModel {
    getSearchContext() {
        const searchContext = makeContext([
            this.env.context,                           // 基础上下文
            "{'search_default_' + filter_name: 1}",    // 默认过滤器
            "{'group_by': group_by_field}"              // 分组字段
        ], {
            filter_name: this.defaultFilter,
            group_by_field: this.defaultGroupBy,
            uid: this.env.services.user.userId
        });
        
        return searchContext;
    }
}
```

## 🛠️ 实践练习

### 练习1: 上下文管理器
```javascript
class ContextManager {
    constructor(baseContext = {}) {
        this.baseContext = baseContext;
        this.contextStack = [];
    }
    
    // 推入新的上下文层
    pushContext(contextDescription, evaluationContext = {}) {
        const currentContext = this.getCurrentContext();
        const newContext = makeContext([
            currentContext,
            contextDescription
        ], evaluationContext);
        
        this.contextStack.push(newContext);
        return newContext;
    }
    
    // 弹出上下文层
    popContext() {
        if (this.contextStack.length > 0) {
            return this.contextStack.pop();
        }
        return this.baseContext;
    }
    
    // 获取当前上下文
    getCurrentContext() {
        if (this.contextStack.length > 0) {
            return this.contextStack[this.contextStack.length - 1];
        }
        return this.baseContext;
    }
    
    // 临时上下文执行
    withContext(contextDescription, callback, evaluationContext = {}) {
        this.pushContext(contextDescription, evaluationContext);
        try {
            return callback(this.getCurrentContext());
        } finally {
            this.popContext();
        }
    }
    
    // 异步版本
    async withContextAsync(contextDescription, asyncCallback, evaluationContext = {}) {
        this.pushContext(contextDescription, evaluationContext);
        try {
            return await asyncCallback(this.getCurrentContext());
        } finally {
            this.popContext();
        }
    }
    
    // 合并多个上下文描述
    mergeContexts(contextDescriptions, evaluationContext = {}) {
        return makeContext([
            this.getCurrentContext(),
            ...contextDescriptions
        ], evaluationContext);
    }
    
    // 创建子上下文管理器
    createChild(additionalContext = {}) {
        const childContext = makeContext([
            this.getCurrentContext(),
            additionalContext
        ]);
        return new ContextManager(childContext);
    }
}

// 使用示例
const contextManager = new ContextManager({
    uid: 1,
    lang: "en_US",
    company_id: 1
});

// 临时切换公司执行操作
const result = await contextManager.withContextAsync(
    "{'allowed_company_ids': [company_id]}",
    async (context) => {
        return orm.searchRead('sale.order', [], ['name'], { context });
    },
    { company_id: 2 }
);
```

### 练习2: 智能上下文求值器
```javascript
class SmartContextEvaluator {
    constructor() {
        this.cache = new Map();
        this.dependencies = new Map();
    }
    
    // 分析上下文表达式的依赖
    analyzeDependencies(contextString) {
        try {
            const ast = parseExpr(contextString);
            const dependencies = new Set();
            
            for (const key in ast.value) {
                const names = getPartialNames(ast.value[key]);
                names.forEach(name => dependencies.add(name));
            }
            
            return Array.from(dependencies);
        } catch (error) {
            console.warn('Failed to analyze dependencies:', error);
            return [];
        }
    }
    
    // 缓存上下文求值结果
    evaluateWithCache(contextString, evaluationContext = {}) {
        const cacheKey = this.generateCacheKey(contextString, evaluationContext);
        
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }
        
        try {
            const result = evalPartialContext(contextString, evaluationContext);
            this.cache.set(cacheKey, result);
            
            // 记录依赖关系
            const deps = this.analyzeDependencies(contextString);
            this.dependencies.set(cacheKey, deps);
            
            return result;
        } catch (error) {
            console.error('Context evaluation failed:', error);
            return {};
        }
    }
    
    generateCacheKey(contextString, evaluationContext) {
        const contextKeys = Object.keys(evaluationContext).sort();
        const contextValues = contextKeys.map(key => evaluationContext[key]);
        return JSON.stringify([contextString, contextKeys, contextValues]);
    }
    
    // 智能缓存失效
    invalidateCache(changedVariables) {
        const toDelete = [];
        
        for (const [cacheKey, deps] of this.dependencies.entries()) {
            if (deps.some(dep => changedVariables.includes(dep))) {
                toDelete.push(cacheKey);
            }
        }
        
        toDelete.forEach(key => {
            this.cache.delete(key);
            this.dependencies.delete(key);
        });
        
        console.log(`Invalidated ${toDelete.length} cache entries`);
    }
    
    // 批量求值
    evaluateBatch(contextStrings, evaluationContext = {}) {
        return contextStrings.map(ctx => 
            this.evaluateWithCache(ctx, evaluationContext)
        );
    }
    
    // 获取缓存统计
    getCacheStats() {
        return {
            size: this.cache.size,
            dependencies: this.dependencies.size,
            hitRate: this.hitCount / (this.hitCount + this.missCount) || 0
        };
    }
    
    // 预热缓存
    warmupCache(commonContexts, commonEvaluationContexts) {
        for (const ctx of commonContexts) {
            for (const evalCtx of commonEvaluationContexts) {
                this.evaluateWithCache(ctx, evalCtx);
            }
        }
    }
}

// 使用示例
const evaluator = new SmartContextEvaluator();

// 预热常用上下文
evaluator.warmupCache([
    "{'default_state': 'draft'}",
    "{'active_id': active_id}",
    "{'search_default_open': 1}"
], [
    { active_id: 1, uid: 1 },
    { active_id: 2, uid: 1 },
    { active_id: 3, uid: 2 }
]);

// 使用缓存求值
const result = evaluator.evaluateWithCache(
    "{'computed_value': active_id * 2, 'user_name': 'User ' + str(uid)}",
    { active_id: 100, uid: 1 }
);
```

### 练习3: 上下文验证器
```javascript
class ContextValidator {
    constructor() {
        this.rules = new Map();
        this.warnings = [];
    }
    
    // 注册验证规则
    addRule(name, validator, description) {
        this.rules.set(name, { validator, description });
    }
    
    // 验证上下文
    validate(context, strict = false) {
        this.warnings = [];
        const errors = [];
        
        for (const [ruleName, rule] of this.rules.entries()) {
            try {
                const result = rule.validator(context);
                if (result !== true) {
                    const message = `Rule '${ruleName}' failed: ${result}`;
                    if (strict) {
                        errors.push(message);
                    } else {
                        this.warnings.push(message);
                    }
                }
            } catch (error) {
                const message = `Rule '${ruleName}' error: ${error.message}`;
                errors.push(message);
            }
        }
        
        return {
            valid: errors.length === 0,
            errors,
            warnings: this.warnings
        };
    }
    
    // 验证上下文表达式
    validateExpression(contextString, evaluationContext = {}) {
        try {
            // 检查语法
            const ast = parseExpr(contextString);
            
            // 检查依赖
            const dependencies = this.analyzeDependencies(contextString);
            const missingDeps = dependencies.filter(dep => 
                !(dep in evaluationContext || dep in BUILTINS)
            );
            
            if (missingDeps.length > 0) {
                return {
                    valid: false,
                    errors: [`Missing dependencies: ${missingDeps.join(', ')}`],
                    warnings: []
                };
            }
            
            // 尝试求值
            evalPartialContext(contextString, evaluationContext);
            
            return { valid: true, errors: [], warnings: [] };
            
        } catch (error) {
            return {
                valid: false,
                errors: [`Expression error: ${error.message}`],
                warnings: []
            };
        }
    }
    
    analyzeDependencies(contextString) {
        const ast = parseExpr(contextString);
        const dependencies = new Set();
        
        for (const key in ast.value) {
            const names = getPartialNames(ast.value[key]);
            names.forEach(name => dependencies.add(name));
        }
        
        return Array.from(dependencies);
    }
    
    // 预定义验证规则
    setupDefaultRules() {
        // 检查必需的用户上下文
        this.addRule('required_user_context', (context) => {
            const required = ['uid', 'lang'];
            const missing = required.filter(key => !(key in context));
            return missing.length === 0 || `Missing required keys: ${missing.join(', ')}`;
        }, 'Ensures required user context is present');
        
        // 检查公司上下文
        this.addRule('company_context', (context) => {
            if ('allowed_company_ids' in context) {
                const companies = context.allowed_company_ids;
                if (!Array.isArray(companies) || companies.length === 0) {
                    return 'allowed_company_ids must be a non-empty array';
                }
            }
            return true;
        }, 'Validates company context structure');
        
        // 检查时区格式
        this.addRule('timezone_format', (context) => {
            if ('tz' in context) {
                const tz = context.tz;
                if (typeof tz !== 'string' || tz.length === 0) {
                    return 'tz must be a non-empty string';
                }
            }
            return true;
        }, 'Validates timezone format');
        
        // 检查语言代码
        this.addRule('language_code', (context) => {
            if ('lang' in context) {
                const lang = context.lang;
                if (!/^[a-z]{2}_[A-Z]{2}$/.test(lang)) {
                    return 'lang must be in format xx_XX (e.g., en_US)';
                }
            }
            return true;
        }, 'Validates language code format');
    }
}

// 使用示例
const validator = new ContextValidator();
validator.setupDefaultRules();

// 验证上下文对象
const context = {
    uid: 1,
    lang: "en_US",
    tz: "UTC",
    allowed_company_ids: [1, 2]
};

const validation = validator.validate(context);
if (!validation.valid) {
    console.error('Context validation failed:', validation.errors);
}

// 验证上下文表达式
const expressionValidation = validator.validateExpression(
    "{'computed_id': active_id + 1, 'user_company': allowed_company_ids[0]}",
    { active_id: 100, allowed_company_ids: [1, 2] }
);
```

## 🔧 调试技巧

### 查看上下文处理过程
```javascript
// 包装makeContext以添加调试信息
const originalMakeContext = makeContext;
function debugMakeContext(contexts, initialEvaluationContext) {
    console.group('Context Creation');
    console.log('Input contexts:', contexts);
    console.log('Initial evaluation context:', initialEvaluationContext);
    
    const result = originalMakeContext(contexts, initialEvaluationContext);
    
    console.log('Final context:', result);
    console.groupEnd();
    
    return result;
}
```

### 分析上下文表达式
```javascript
function analyzeContextExpression(contextString) {
    try {
        const ast = parseExpr(contextString);
        console.log('AST:', ast);
        
        const dependencies = [];
        for (const key in ast.value) {
            const names = getPartialNames(ast.value[key]);
            dependencies.push({ key, dependencies: names });
        }
        
        console.log('Dependencies:', dependencies);
        return dependencies;
    } catch (error) {
        console.error('Failed to analyze expression:', error);
        return null;
    }
}

// 使用示例
analyzeContextExpression("{'computed': active_id * 2, 'flag': uid > 0}");
```

## 📊 性能考虑

### 优化策略
1. **缓存求值结果**: 避免重复计算相同表达式
2. **依赖分析**: 只在依赖变化时重新求值
3. **部分求值**: 跳过无法求值的表达式
4. **错误处理**: 优雅处理求值失败

### 最佳实践
```javascript
// ✅ 好的做法：使用部分求值
const context = evalPartialContext(
    "{'computed': active_id * 2, 'unknown': undefined_var}",
    { active_id: 100 }
);
// 结果: { computed: 200 } (unknown被忽略)

// ❌ 不好的做法：强制求值所有表达式
try {
    const context = makeContext([
        "{'computed': active_id * 2, 'unknown': undefined_var}"
    ], { active_id: 100 });
} catch (error) {
    // 整个求值失败
}

// ✅ 好的做法：合理的上下文层次
const context = makeContext([
    baseContext,           // 基础上下文
    userContext,          // 用户上下文
    "{'dynamic': expr}"   // 动态表达式
]);

// ❌ 不好的做法：过度复杂的表达式
const context = makeContext([
    "{'complex': very_complex_expression_that_is_hard_to_debug}"
]);
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解Odoo上下文系统的设计原理
- [ ] 掌握makeContext和evalPartialContext的使用
- [ ] 理解Python表达式在JavaScript中的求值
- [ ] 能够分析和调试上下文表达式
- [ ] 掌握上下文合并和继承策略
- [ ] 了解AST在上下文处理中的应用

## 🚀 下一步学习
学完Context系统后，建议继续学习：
1. **Python解释器** (`@web/core/py_js/`) - 深入理解Python表达式求值
2. **域系统** (`@web/core/domain.js`) - 学习搜索域的处理
3. **用户服务** (回顾`@web/core/user.js`) - 理解用户上下文的来源

## 💡 重要提示
- Context是Odoo数据流的核心机制
- 理解上下文合并对调试业务逻辑至关重要
- Python表达式求值提供了强大的动态计算能力
- 部分求值机制提高了系统的容错性

## 🔍 深入理解：上下文系统的设计精髓

### 为什么需要上下文系统？
```python
# 在Odoo后端，上下文无处不在
def create_sale_order(self):
    # 上下文影响默认值
    order = self.env['sale.order'].with_context(
        default_partner_id=self.partner_id.id,
        default_currency_id=self.currency_id.id
    ).create({
        'name': 'SO001'
    })

    # 上下文影响权限检查
    if self.env.context.get('force_company'):
        order = order.with_context(force_company=True)

    return order
```

```javascript
// 在前端，上下文同样重要
const context = makeContext([
    env.context,                           // 全局环境
    "{'default_state': 'draft'}",         // 默认值
    "{'active_id': active_id}",           // 当前记录
    { search_default_open: 1 }            // 搜索过滤器
]);

// 所有ORM操作都会携带上下文
await orm.create('sale.order', [data], { context });
```

**上下文的价值**：
- **环境传递**: 在调用链中传递执行环境
- **默认值**: 为新记录提供默认字段值
- **权限控制**: 影响访问权限和数据可见性
- **业务逻辑**: 控制业务流程的执行路径

### 上下文合并的巧妙设计
```javascript
// 关键设计：渐进式求值
function makeContext(contexts, initialEvaluationContext) {
    const evaluationContext = Object.assign({}, initialEvaluationContext);
    const context = {};

    for (let ctx of contexts) {
        if (ctx !== "") {
            ctx = typeof ctx === "string" ? evaluateExpr(ctx, evaluationContext) : ctx;
            Object.assign(context, ctx);
            Object.assign(evaluationContext, context); // 🔑 关键行
        }
    }
    return context;
}
```

**渐进式求值的优势**：
```javascript
const result = makeContext([
    { uid: 1, company_id: 2 },                    // 基础值
    "{'user_company': company_id}",               // 引用前面的值
    "{'display_name': 'User ' + str(uid)}",      // 引用更早的值
    "{'full_context': user_company + uid}"       // 引用计算出的值
]);

// 结果：
// {
//   uid: 1,
//   company_id: 2,
//   user_company: 2,
//   display_name: "User 1",
//   full_context: 3
// }
```

### 部分求值的容错设计
```javascript
// 问题场景：不完整的求值环境
const incompleteContext = evalPartialContext(
    "{'available': uid * 2, 'unavailable': unknown_var + 1}",
    { uid: 1 } // 缺少 unknown_var
);

// 传统方式：全部失败
// 部分求值：{ available: 2 } (只保留可求值的部分)
```

**容错机制的实现**：
1. **依赖分析**: 通过AST分析表达式依赖的变量
2. **可用性检查**: 检查变量是否在求值环境中存在
3. **安全求值**: 只求值安全的表达式
4. **错误隔离**: 单个表达式失败不影响其他表达式

## 🎓 高级应用模式

### 1. 上下文中间件系统
```javascript
class ContextMiddleware {
    constructor() {
        this.middlewares = [];
    }

    use(middleware) {
        this.middlewares.push(middleware);
        return this;
    }

    async process(contexts, evaluationContext = {}) {
        let result = { contexts, evaluationContext };

        // 前置中间件
        for (const middleware of this.middlewares) {
            if (middleware.before) {
                result = await middleware.before(result.contexts, result.evaluationContext);
            }
        }

        // 核心处理
        const finalContext = makeContext(result.contexts, result.evaluationContext);

        // 后置中间件
        let processedContext = finalContext;
        for (const middleware of this.middlewares.reverse()) {
            if (middleware.after) {
                processedContext = await middleware.after(processedContext);
            }
        }

        return processedContext;
    }
}

// 中间件示例
const contextProcessor = new ContextMiddleware();

// 日志中间件
contextProcessor.use({
    before: (contexts, evalCtx) => {
        console.log('Processing contexts:', contexts);
        return { contexts, evaluationContext: evalCtx };
    },
    after: (context) => {
        console.log('Final context:', context);
        return context;
    }
});

// 安全中间件
contextProcessor.use({
    before: (contexts, evalCtx) => {
        // 过滤敏感信息
        const sanitizedContexts = contexts.map(ctx => {
            if (typeof ctx === 'string' && ctx.includes('password')) {
                return "{}"; // 移除包含密码的表达式
            }
            return ctx;
        });
        return { contexts: sanitizedContexts, evaluationContext: evalCtx };
    }
});

// 缓存中间件
contextProcessor.use({
    cache: new Map(),
    before: function(contexts, evalCtx) {
        const key = JSON.stringify([contexts, evalCtx]);
        if (this.cache.has(key)) {
            throw { cached: this.cache.get(key) }; // 特殊异常表示缓存命中
        }
        return { contexts, evaluationContext: evalCtx };
    },
    after: function(context) {
        const key = JSON.stringify([contexts, evalCtx]);
        this.cache.set(key, context);
        return context;
    }
});
```

### 2. 响应式上下文系统
```javascript
class ReactiveContext {
    constructor(initialContext = {}) {
        this.context = { ...initialContext };
        this.watchers = new Map();
        this.computedProperties = new Map();
        this.dependencies = new Map();
    }

    // 设置响应式属性
    set(key, value) {
        const oldValue = this.context[key];
        this.context[key] = value;

        // 触发观察者
        this.notifyWatchers(key, value, oldValue);

        // 重新计算依赖属性
        this.recomputeDependents(key);
    }

    // 获取属性值
    get(key) {
        return this.context[key];
    }

    // 定义计算属性
    computed(key, expression, dependencies = []) {
        this.computedProperties.set(key, expression);
        this.dependencies.set(key, dependencies);

        // 立即计算初始值
        this.recompute(key);

        return this;
    }

    // 监听属性变化
    watch(key, callback) {
        if (!this.watchers.has(key)) {
            this.watchers.set(key, new Set());
        }
        this.watchers.get(key).add(callback);

        // 返回取消监听函数
        return () => {
            this.watchers.get(key).delete(callback);
        };
    }

    notifyWatchers(key, newValue, oldValue) {
        const callbacks = this.watchers.get(key);
        if (callbacks) {
            callbacks.forEach(callback => {
                try {
                    callback(newValue, oldValue, key);
                } catch (error) {
                    console.error('Watcher error:', error);
                }
            });
        }
    }

    recomputeDependents(changedKey) {
        for (const [computedKey, deps] of this.dependencies.entries()) {
            if (deps.includes(changedKey)) {
                this.recompute(computedKey);
            }
        }
    }

    recompute(key) {
        const expression = this.computedProperties.get(key);
        if (expression) {
            try {
                const result = evalPartialContext(
                    `{'${key}': ${expression}}`,
                    this.context
                );
                if (key in result) {
                    const oldValue = this.context[key];
                    this.context[key] = result[key];
                    this.notifyWatchers(key, result[key], oldValue);
                }
            } catch (error) {
                console.error(`Failed to compute ${key}:`, error);
            }
        }
    }

    // 批量更新
    batch(updates) {
        const oldValues = {};

        // 记录旧值
        for (const key in updates) {
            oldValues[key] = this.context[key];
        }

        // 批量设置新值
        Object.assign(this.context, updates);

        // 批量触发观察者
        for (const [key, value] of Object.entries(updates)) {
            this.notifyWatchers(key, value, oldValues[key]);
        }

        // 重新计算所有依赖
        const changedKeys = Object.keys(updates);
        const toRecompute = new Set();

        for (const [computedKey, deps] of this.dependencies.entries()) {
            if (deps.some(dep => changedKeys.includes(dep))) {
                toRecompute.add(computedKey);
            }
        }

        toRecompute.forEach(key => this.recompute(key));
    }

    // 导出为普通上下文
    toPlainContext() {
        return { ...this.context };
    }
}

// 使用示例
const reactiveCtx = new ReactiveContext({
    uid: 1,
    company_id: 1
});

// 定义计算属性
reactiveCtx
    .computed('user_company', 'uid + company_id', ['uid', 'company_id'])
    .computed('display_name', '"User " + str(uid)', ['uid']);

// 监听变化
reactiveCtx.watch('user_company', (newVal, oldVal) => {
    console.log(`user_company changed: ${oldVal} -> ${newVal}`);
});

// 触发变化
reactiveCtx.set('uid', 2); // 自动重新计算 user_company 和 display_name
```

### 3. 上下文模板系统
```javascript
class ContextTemplate {
    constructor(template, defaultValues = {}) {
        this.template = template;
        this.defaultValues = defaultValues;
        this.compiledTemplate = this.compile(template);
    }

    compile(template) {
        // 解析模板中的占位符
        const placeholders = [];
        const regex = /\{\{(\w+)\}\}/g;
        let match;

        while ((match = regex.exec(template)) !== null) {
            placeholders.push(match[1]);
        }

        return {
            template,
            placeholders: [...new Set(placeholders)]
        };
    }

    render(variables = {}) {
        const mergedVars = { ...this.defaultValues, ...variables };
        let result = this.compiledTemplate.template;

        // 替换占位符
        for (const placeholder of this.compiledTemplate.placeholders) {
            const value = mergedVars[placeholder];
            if (value !== undefined) {
                const regex = new RegExp(`\\{\\{${placeholder}\\}\\}`, 'g');
                result = result.replace(regex, JSON.stringify(value));
            }
        }

        return result;
    }

    // 验证模板
    validate(variables = {}) {
        const mergedVars = { ...this.defaultValues, ...variables };
        const missing = this.compiledTemplate.placeholders.filter(
            placeholder => !(placeholder in mergedVars)
        );

        if (missing.length > 0) {
            return {
                valid: false,
                missing,
                message: `Missing variables: ${missing.join(', ')}`
            };
        }

        // 尝试渲染和求值
        try {
            const rendered = this.render(variables);
            evalPartialContext(rendered, mergedVars);
            return { valid: true };
        } catch (error) {
            return {
                valid: false,
                error: error.message,
                message: `Template validation failed: ${error.message}`
            };
        }
    }

    // 获取依赖变量
    getDependencies() {
        return this.compiledTemplate.placeholders;
    }
}

// 预定义模板
const templates = {
    defaultRecord: new ContextTemplate(
        "{'default_{{field}}': {{value}}, 'active_id': {{record_id}}}",
        { record_id: 'active_id' }
    ),

    searchFilter: new ContextTemplate(
        "{'search_default_{{filter}}': 1, 'group_by': '{{group_field}}'}",
        { filter: 'all' }
    ),

    userContext: new ContextTemplate(
        "{'uid': {{user_id}}, 'company_id': {{company}}, 'lang': '{{language}}'}",
        { language: 'en_US' }
    )
};

// 使用模板
const recordContext = templates.defaultRecord.render({
    field: 'state',
    value: 'draft',
    record_id: 123
});
// 结果: "{'default_state': 'draft', 'active_id': 123}"

const searchContext = templates.searchFilter.render({
    filter: 'open',
    group_field: 'stage_id'
});
// 结果: "{'search_default_open': 1, 'group_by': 'stage_id'}"
```

## 🔧 调试和分析工具

### 上下文调试器
```javascript
class ContextDebugger {
    constructor() {
        this.history = [];
        this.breakpoints = new Set();
        this.enabled = false;
    }

    enable() {
        this.enabled = true;
        this.wrapContextFunctions();
    }

    disable() {
        this.enabled = false;
    }

    wrapContextFunctions() {
        // 包装makeContext
        const originalMakeContext = makeContext;
        window.makeContext = (...args) => {
            if (this.enabled) {
                return this.debugMakeContext(originalMakeContext, ...args);
            }
            return originalMakeContext(...args);
        };

        // 包装evalPartialContext
        const originalEvalPartial = evalPartialContext;
        window.evalPartialContext = (...args) => {
            if (this.enabled) {
                return this.debugEvalPartial(originalEvalPartial, ...args);
            }
            return originalEvalPartial(...args);
        };
    }

    debugMakeContext(originalFn, contexts, evaluationContext) {
        const debugInfo = {
            type: 'makeContext',
            timestamp: Date.now(),
            input: { contexts, evaluationContext },
            steps: []
        };

        console.group('🔍 Context Creation Debug');
        console.log('Input contexts:', contexts);
        console.log('Evaluation context:', evaluationContext);

        // 逐步执行
        const evalCtx = Object.assign({}, evaluationContext);
        const result = {};

        for (let i = 0; i < contexts.length; i++) {
            let ctx = contexts[i];
            if (ctx !== "") {
                const stepInfo = {
                    step: i,
                    input: ctx,
                    type: typeof ctx
                };

                if (typeof ctx === "string") {
                    console.log(`Step ${i}: Evaluating expression:`, ctx);
                    try {
                        ctx = evaluateExpr(ctx, evalCtx);
                        stepInfo.evaluated = ctx;
                        console.log(`Step ${i}: Result:`, ctx);
                    } catch (error) {
                        stepInfo.error = error.message;
                        console.error(`Step ${i}: Error:`, error);
                        throw error;
                    }
                } else {
                    console.log(`Step ${i}: Using object:`, ctx);
                }

                Object.assign(result, ctx);
                Object.assign(evalCtx, result);

                stepInfo.resultSoFar = { ...result };
                stepInfo.evalContextSoFar = { ...evalCtx };
                debugInfo.steps.push(stepInfo);

                console.log(`Step ${i}: Context so far:`, result);
            }
        }

        debugInfo.output = result;
        this.history.push(debugInfo);

        console.log('Final result:', result);
        console.groupEnd();

        return result;
    }

    debugEvalPartial(originalFn, contextString, evaluationContext) {
        const debugInfo = {
            type: 'evalPartialContext',
            timestamp: Date.now(),
            input: { contextString, evaluationContext }
        };

        console.group('🔍 Partial Context Evaluation Debug');
        console.log('Context string:', contextString);
        console.log('Evaluation context:', evaluationContext);

        try {
            const ast = parseExpr(contextString);
            console.log('Parsed AST:', ast);

            const result = {};
            const skipped = [];

            for (const key in ast.value) {
                const value = ast.value[key];
                const dependencies = getPartialNames(value);

                console.log(`Processing key '${key}':`, {
                    expression: value,
                    dependencies
                });

                const missingDeps = dependencies.filter(name =>
                    !(name in evaluationContext || name in BUILTINS)
                );

                if (missingDeps.length > 0) {
                    console.warn(`Skipping '${key}' - missing dependencies:`, missingDeps);
                    skipped.push({ key, missingDeps });
                    continue;
                }

                try {
                    const evaluated = evaluate(value, evaluationContext);
                    result[key] = evaluated;
                    console.log(`✅ '${key}' = ${JSON.stringify(evaluated)}`);
                } catch (error) {
                    console.error(`❌ Failed to evaluate '${key}':`, error);
                    skipped.push({ key, error: error.message });
                }
            }

            debugInfo.output = result;
            debugInfo.skipped = skipped;
            this.history.push(debugInfo);

            console.log('Final result:', result);
            if (skipped.length > 0) {
                console.log('Skipped keys:', skipped);
            }
            console.groupEnd();

            return result;

        } catch (error) {
            debugInfo.error = error.message;
            this.history.push(debugInfo);

            console.error('Parse error:', error);
            console.groupEnd();

            throw error;
        }
    }

    // 获取调试历史
    getHistory() {
        return this.history;
    }

    // 清除历史
    clearHistory() {
        this.history = [];
    }

    // 生成调试报告
    generateReport() {
        const report = {
            totalOperations: this.history.length,
            makeContextCalls: this.history.filter(h => h.type === 'makeContext').length,
            evalPartialCalls: this.history.filter(h => h.type === 'evalPartialContext').length,
            errors: this.history.filter(h => h.error).length,
            timeline: this.history.map(h => ({
                type: h.type,
                timestamp: new Date(h.timestamp).toISOString(),
                hasError: !!h.error
            }))
        };

        return report;
    }
}

// 使用调试器
const debugger = new ContextDebugger();
debugger.enable();

// 现在所有的上下文操作都会被调试
const context = makeContext([
    { uid: 1, active_id: 100 },
    "{'computed': active_id * 2}",
    "{'display': 'User ' + str(uid)}"
]);

// 查看调试历史
console.log('Debug history:', debugger.getHistory());
```

---

**Context系统是Odoo数据流的核心，掌握它的设计思想和实现细节对理解整个Odoo架构至关重要！** 🚀

# ModelSelector - 模型选择器

## 概述

`model_selector.js` 是 Odoo Web 核心模块的模型选择器组件，提供了数据模型的选择功能。该组件基于自动完成组件构建，支持模型搜索、动态加载、模糊匹配等功能，具备权限过滤、显示名称映射、CSS类定制等特性，为用户提供了便捷的模型选择体验，广泛应用于关联属性配置、报表设置、数据导入等需要选择数据模型的场景。

## 文件信息
- **路径**: `/web/static/src/core/model_selector/model_selector.js`
- **行数**: 101
- **模块**: `@web/core/model_selector/model_selector`

## 依赖关系

```javascript
// 核心依赖
'@web/core/autocomplete/autocomplete'    // 自动完成组件
'@web/core/utils/hooks'                  // 工具钩子
'@web/core/utils/search'                 // 搜索工具
'@web/core/l10n/translation'             // 国际化
'@odoo/owl'                              // OWL框架
```

## 主组件类

### 1. ModelSelector 组件

```javascript
class ModelSelector extends Component {
    static template = "web.ModelSelector";
    static components = { AutoComplete };
    static props = {
        onModelSelected: Function,                   // 模型选择回调
        id: { type: String, optional: true },       // 组件ID
        value: { type: String, optional: true },    // 当前值
        models: { type: Array, optional: true },    // 模型列表
    };
}
```

**组件功能**:
- **自动完成**: 基于AutoComplete组件提供搜索功能
- **模型选择**: 提供数据模型的选择功能
- **权限控制**: 只显示用户有权限访问的模型
- **自定义列表**: 支持自定义模型列表

**属性说明**:
- **onModelSelected**: 模型选择时的回调函数
- **id**: 组件的唯一标识符
- **value**: 当前选择的模型值
- **models**: 可选的模型技术名称列表，不设置时获取所有可访问模型

### 2. 组件初始化

```javascript
setup() {
    this.orm = useService("orm");

    onWillStart(async () => {
        if (!this.props.models) {
            this.models = await this._fetchAvailableModels();
        } else {
            this.models = await this.orm.call("ir.model", "display_name_for", [
                this.props.models,
            ]);
        }

        this.models = this.models.map((record) => ({
            label: record.display_name,
            technical: record.model,
            classList: {
                [`o_model_selector_${record.model}`]: 1,
            },
        }));
    });
}
```

**初始化功能**:
- **ORM服务**: 获取ORM服务用于数据操作
- **模型加载**: 根据属性决定加载方式
- **数据转换**: 将模型数据转换为选择器格式
- **CSS类**: 为每个模型添加特定的CSS类

## 核心属性

### 1. 数据源配置

```javascript
get sources() {
    return [this.optionsSource];
}

get optionsSource() {
    return {
        placeholder: _t("Loading..."),
        options: this.loadOptionsSource.bind(this),
    };
}
```

**数据源功能**:
- **占位符**: 提供加载时的占位文本
- **选项加载**: 绑定选项加载函数
- **单一数据源**: 使用单一数据源配置

## 核心方法

### 1. 模型选择处理

```javascript
onSelect(option) {
    this.props.onModelSelected({
        label: option.label,
        technical: option.technical,
    });
}
```

**选择处理功能**:
- **回调触发**: 触发父组件的选择回调
- **数据传递**: 传递显示名称和技术名称
- **标准格式**: 使用标准的数据格式

### 2. 模型过滤

```javascript
filterModels(name) {
    if (!name) {
        const visibleModels = this.models.slice(0, 8);
        if (this.models.length - visibleModels.length > 0) {
            visibleModels.push({
                label: _t("Start typing..."),
                unselectable: true,
                classList: "o_m2o_start_typing",
            });
        }
        return visibleModels;
    }
    return fuzzyLookup(name, this.models, (model) => model.technical + model.label);
}
```

**过滤功能**:
- **空查询处理**: 无搜索词时显示前8个模型
- **提示信息**: 显示"开始输入..."提示
- **模糊搜索**: 使用fuzzyLookup进行模糊匹配
- **多字段搜索**: 同时搜索技术名称和显示名称

### 3. 选项加载

```javascript
loadOptionsSource(request) {
    const options = this.filterModels(request);

    if (!options.length) {
        options.push({
            label: _t("No records"),
            classList: "o_m2o_no_result",
            unselectable: true,
        });
    }
    return options;
}
```

**加载功能**:
- **过滤调用**: 调用filterModels进行过滤
- **空结果处理**: 无结果时显示"无记录"提示
- **不可选项**: 提示项标记为不可选择
- **CSS类**: 添加特定的CSS类用于样式

### 4. 可用模型获取

```javascript
async _fetchAvailableModels() {
    const result = await this.orm.call("ir.model", "get_available_models");
    return result || [];
}
```

**获取功能**:
- **RPC调用**: 调用后端方法获取可用模型
- **权限过滤**: 后端自动过滤用户有权限的模型
- **错误处理**: 失败时返回空数组

## 使用场景

### 1. 基础模型选择器

```javascript
// 基础模型选择器使用
class BasicModelSelector extends Component {
    setup() {
        this.state = useState({
            selectedModel: null,
            availableModels: [
                'res.partner',
                'sale.order',
                'product.product',
                'account.invoice',
                'project.project'
            ],
            useCustomList: false,
            selectionHistory: []
        });
    }

    onModelSelected(modelData) {
        this.state.selectedModel = modelData;
        
        // 记录选择历史
        this.state.selectionHistory.unshift({
            timestamp: new Date().toLocaleTimeString(),
            label: modelData.label,
            technical: modelData.technical
        });

        // 限制历史记录数量
        if (this.state.selectionHistory.length > 10) {
            this.state.selectionHistory.pop();
        }

        console.log('Model selected:', modelData);
    }

    toggleCustomList() {
        this.state.useCustomList = !this.state.useCustomList;
        this.state.selectedModel = null;
    }

    clearSelection() {
        this.state.selectedModel = null;
    }

    render() {
        return xml`
            <div class="basic-model-selector">
                <h5>基础模型选择器</h5>
                
                <div class="controls mb-3">
                    <div class="form-check">
                        <input 
                            class="form-check-input" 
                            type="checkbox" 
                            t-model="state.useCustomList"
                            t-on-change="toggleCustomList"
                        />
                        <label class="form-check-label">使用自定义模型列表</label>
                    </div>
                </div>

                <div class="model-selector-container mb-3">
                    <label class="form-label">选择数据模型:</label>
                    <ModelSelector
                        onModelSelected="onModelSelected"
                        models="state.useCustomList ? state.availableModels : undefined"
                        id="basic-model-selector"
                    />
                </div>

                <div class="selected-model-info" t-if="state.selectedModel">
                    <div class="card">
                        <div class="card-header">
                            <h6>已选择模型</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>显示名称:</strong></p>
                                    <p t-esc="state.selectedModel.label"/>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>技术名称:</strong></p>
                                    <code t-esc="state.selectedModel.technical"/>
                                </div>
                            </div>
                            <button 
                                class="btn btn-sm btn-secondary"
                                t-on-click="clearSelection"
                            >
                                清除选择
                            </button>
                        </div>
                    </div>
                </div>

                <div class="selection-history mt-4" t-if="state.selectionHistory.length">
                    <h6>选择历史</h6>
                    <div class="list-group">
                        <t t-foreach="state.selectionHistory" t-as="record" t-key="record_index">
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong t-esc="record.label"/>
                                        <br/>
                                        <small class="text-muted">
                                            <code t-esc="record.technical"/>
                                        </small>
                                    </div>
                                    <small class="text-muted" t-esc="record.timestamp"/>
                                </div>
                            </div>
                        </t>
                    </div>
                </div>

                <div class="instructions mt-3">
                    <div class="alert alert-info">
                        <h6>使用说明</h6>
                        <ul class="mb-0">
                            <li>开始输入模型名称进行搜索</li>
                            <li>支持模糊匹配和多字段搜索</li>
                            <li>可以使用自定义模型列表或获取所有可访问模型</li>
                            <li>选择模型后会显示详细信息</li>
                        </ul>
                    </div>
                </div>
            </div>
        `;
    }
}
```

### 2. 高级模型配置器

```javascript
// 高级模型配置器
class AdvancedModelConfigurator extends Component {
    setup() {
        this.state = useState({
            configurations: [
                {
                    id: 1,
                    name: '销售配置',
                    sourceModel: null,
                    targetModel: null,
                    description: '销售相关的数据配置'
                },
                {
                    id: 2,
                    name: '库存配置',
                    sourceModel: null,
                    targetModel: null,
                    description: '库存管理的数据配置'
                }
            ],
            currentConfig: null,
            isEditing: false,
            modelStats: {
                totalSelections: 0,
                uniqueModels: new Set()
            }
        });
    }

    editConfiguration(config) {
        this.state.currentConfig = { ...config };
        this.state.isEditing = true;
    }

    saveConfiguration() {
        if (!this.state.currentConfig) return;

        const index = this.state.configurations.findIndex(c => c.id === this.state.currentConfig.id);
        if (index !== -1) {
            this.state.configurations[index] = { ...this.state.currentConfig };
        }

        this.updateStats();
        this.state.isEditing = false;
        this.state.currentConfig = null;
    }

    cancelEdit() {
        this.state.isEditing = false;
        this.state.currentConfig = null;
    }

    onSourceModelSelected(modelData) {
        if (this.state.currentConfig) {
            this.state.currentConfig.sourceModel = modelData;
            this.updateStats();
        }
    }

    onTargetModelSelected(modelData) {
        if (this.state.currentConfig) {
            this.state.currentConfig.targetModel = modelData;
            this.updateStats();
        }
    }

    updateStats() {
        let totalSelections = 0;
        const uniqueModels = new Set();

        this.state.configurations.forEach(config => {
            if (config.sourceModel) {
                totalSelections++;
                uniqueModels.add(config.sourceModel.technical);
            }
            if (config.targetModel) {
                totalSelections++;
                uniqueModels.add(config.targetModel.technical);
            }
        });

        this.state.modelStats = { totalSelections, uniqueModels };
    }

    addConfiguration() {
        const newConfig = {
            id: Date.now(),
            name: '新配置',
            sourceModel: null,
            targetModel: null,
            description: ''
        };
        this.state.configurations.push(newConfig);
        this.editConfiguration(newConfig);
    }

    deleteConfiguration(configId) {
        if (!confirm('确定要删除这个配置吗？')) return;
        
        this.state.configurations = this.state.configurations.filter(c => c.id !== configId);
        this.updateStats();
    }

    exportConfigurations() {
        const data = JSON.stringify(this.state.configurations, null, 2);
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'model_configurations.json';
        a.click();
        URL.revokeObjectURL(url);
    }

    render() {
        return xml`
            <div class="advanced-model-configurator">
                <div class="header mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>高级模型配置器</h5>
                        <div class="header-actions">
                            <button 
                                class="btn btn-primary me-2"
                                t-on-click="addConfiguration"
                            >
                                <i class="fa fa-plus"/> 添加配置
                            </button>
                            <button 
                                class="btn btn-outline-secondary"
                                t-on-click="exportConfigurations"
                            >
                                <i class="fa fa-download"/> 导出
                            </button>
                        </div>
                    </div>
                </div>

                <div class="stats-summary mb-4">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <h6>配置数量</h6>
                                    <p class="h4 text-primary" t-esc="state.configurations.length"/>
                                </div>
                                <div class="col-md-4">
                                    <h6>模型选择总数</h6>
                                    <p class="h4 text-success" t-esc="state.modelStats.totalSelections"/>
                                </div>
                                <div class="col-md-4">
                                    <h6>唯一模型数</h6>
                                    <p class="h4 text-info" t-esc="state.modelStats.uniqueModels.size"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 编辑表单 -->
                <div class="edit-form mb-4" t-if="state.isEditing and state.currentConfig">
                    <div class="card">
                        <div class="card-header">
                            <h6>编辑配置: <t t-esc="state.currentConfig.name"/></h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">配置名称</label>
                                    <input 
                                        type="text" 
                                        class="form-control"
                                        t-model="state.currentConfig.name"
                                    />
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">描述</label>
                                    <input 
                                        type="text" 
                                        class="form-control"
                                        t-model="state.currentConfig.description"
                                    />
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">源模型</label>
                                    <ModelSelector
                                        onModelSelected="onSourceModelSelected"
                                        id="source-model-selector"
                                    />
                                    <div class="mt-2" t-if="state.currentConfig.sourceModel">
                                        <small class="text-success">
                                            已选择: <strong t-esc="state.currentConfig.sourceModel.label"/>
                                        </small>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">目标模型</label>
                                    <ModelSelector
                                        onModelSelected="onTargetModelSelected"
                                        id="target-model-selector"
                                    />
                                    <div class="mt-2" t-if="state.currentConfig.targetModel">
                                        <small class="text-success">
                                            已选择: <strong t-esc="state.currentConfig.targetModel.label"/>
                                        </small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-actions">
                                <button 
                                    class="btn btn-success me-2"
                                    t-on-click="saveConfiguration"
                                >
                                    保存配置
                                </button>
                                <button 
                                    class="btn btn-secondary"
                                    t-on-click="cancelEdit"
                                >
                                    取消
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 配置列表 -->
                <div class="configurations-list">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>配置名称</th>
                                    <th>源模型</th>
                                    <th>目标模型</th>
                                    <th>描述</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-foreach="state.configurations" t-as="config" t-key="config.id">
                                    <tr>
                                        <td>
                                            <strong t-esc="config.name"/>
                                        </td>
                                        <td>
                                            <span t-if="config.sourceModel" class="badge bg-primary">
                                                <t t-esc="config.sourceModel.label"/>
                                            </span>
                                            <span t-else="" class="text-muted">未设置</span>
                                        </td>
                                        <td>
                                            <span t-if="config.targetModel" class="badge bg-success">
                                                <t t-esc="config.targetModel.label"/>
                                            </span>
                                            <span t-else="" class="text-muted">未设置</span>
                                        </td>
                                        <td>
                                            <small t-esc="config.description || '无描述'"/>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button 
                                                    class="btn btn-outline-primary"
                                                    t-on-click="() => this.editConfiguration(config)"
                                                >
                                                    编辑
                                                </button>
                                                <button 
                                                    class="btn btn-outline-danger"
                                                    t-on-click="() => this.deleteConfiguration(config.id)"
                                                >
                                                    删除
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </t>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="empty-state text-center py-5" t-if="!state.configurations.length">
                    <i class="fa fa-database fa-3x text-muted mb-3"/>
                    <h6 class="text-muted">暂无配置</h6>
                    <p class="text-muted">点击"添加配置"按钮创建第一个模型配置</p>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 智能搜索
- 模糊匹配搜索
- 多字段搜索支持
- 实时过滤结果

### 2. 权限感知
- 自动过滤可访问模型
- 后端权限验证
- 安全的数据获取

### 3. 用户体验
- 渐进式显示
- 搜索提示信息
- 无结果友好提示

### 4. 灵活配置
- 支持自定义模型列表
- 可选的模型范围
- 动态数据加载

## 设计模式

### 1. 适配器模式 (Adapter Pattern)
- 自动完成组件的适配
- 数据格式的转换适配

### 2. 策略模式 (Strategy Pattern)
- 不同数据源的加载策略
- 搜索过滤的策略选择

### 3. 观察者模式 (Observer Pattern)
- 选择事件的通知机制
- 状态变化的响应

## 注意事项

1. **权限控制**: 确保只显示用户有权限的模型
2. **性能优化**: 避免加载过多模型数据
3. **用户体验**: 提供清晰的搜索和选择反馈
4. **错误处理**: 处理网络请求失败的情况

## 扩展建议

1. **分类显示**: 按模块或类别分组显示模型
2. **收藏功能**: 支持常用模型的收藏
3. **最近使用**: 显示最近使用的模型
4. **批量选择**: 支持多模型的批量选择
5. **模型信息**: 显示模型的详细信息和字段

该模型选择器组件为Odoo Web应用提供了便捷的数据模型选择功能，通过智能搜索和权限控制确保了良好的用户体验和系统安全性。

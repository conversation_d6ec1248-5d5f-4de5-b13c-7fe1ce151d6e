# TagNavigationHook - 标签导航钩子

## 概述

`tag_navigation_hook.js` 是 Odoo Web 核心模块的标签导航钩子，提供了在记录选择器中进行标签间导航和删除的键盘交互功能。该钩子专门用于包含自动完成组件和标签列表的组件，支持箭头键导航、退格键删除和焦点管理，为用户提供了流畅的键盘操作体验，提升了多记录选择器的可用性和无障碍性。

## 文件信息
- **路径**: `/web/static/src/core/record_selectors/tag_navigation_hook.js`
- **行数**: 141
- **模块**: `@web/core/record_selectors/tag_navigation_hook`

## 依赖关系

```javascript
// 核心依赖
'@web/core/hotkeys/hotkey_service'    // 热键服务
'@odoo/owl'                          // OWL框架
```

## 核心功能

### 1. 钩子函数定义

```javascript
function useTagNavigation(refName, deleteTag) {
    const ref = useRef(refName);
    // 钩子实现...
    return onTagKeydown;
}
```

**钩子参数**:
- **refName**: 包含自动完成和标签列表组件的引用名称
- **deleteTag**: 删除标签时调用的函数，接收标签索引作为参数
- **返回值**: 标签按键处理函数，用于绑定到标签组件的onKeydown属性

### 2. 自动完成键盘事件监听

```javascript
useEffect(
    (autocomplete) => {
        if (!autocomplete) {
            return;
        }
        autocomplete.addEventListener("keydown", onAutoCompleteKeydown);
        return () => {
            autocomplete.removeEventListener("keydown", onAutoCompleteKeydown);
        };
    },
    () => [ref.el?.querySelector(".o-autocomplete")]
);
```

**事件监听功能**:
- **动态绑定**: 自动查找并绑定自动完成组件
- **生命周期管理**: 组件销毁时自动清理事件监听器
- **响应式更新**: 当自动完成组件变化时重新绑定
- **内存安全**: 防止内存泄漏的清理机制

### 3. 标签焦点管理

```javascript
function focusTag(index) {
    const tags = ref.el.getElementsByClassName("o_tag");
    if (tags.length) {
        if (index === undefined) {
            tags[tags.length - 1].focus();
        } else {
            tags[index].focus();
        }
    }
}
```

**焦点管理功能**:
- **索引聚焦**: 根据索引聚焦指定标签
- **默认聚焦**: 无索引时聚焦最右侧标签
- **边界检查**: 检查标签是否存在
- **DOM操作**: 直接操作DOM元素的焦点

### 4. 自动完成键盘处理

```javascript
function onAutoCompleteKeydown(ev) {
    if (ev.isComposing) {
        return;
    }
    const hotkey = getActiveHotkey(ev);
    const input = ev.target.closest(".o-autocomplete--input");
    const autoCompleteMenuOpened = !!ref.el.querySelector(".o-autocomplete--dropdown-menu");
    
    switch (hotkey) {
        case "arrowleft": {
            if (input.selectionStart || autoCompleteMenuOpened) {
                return;
            }
            focusTag();
            break;
        }
        case "arrowright": {
            if (input.selectionStart !== input.value.length || autoCompleteMenuOpened) {
                return;
            }
            focusTag(0);
            break;
        }
        case "backspace": {
            if (input.value) {
                return;
            }
            const tags = ref.el.getElementsByClassName("o_tag");
            if (tags.length) {
                deleteTag(tags.length - 1);
            }
            break;
        }
    }
    ev.preventDefault();
    ev.stopPropagation();
}
```

**自动完成键盘功能**:
- **输入法支持**: 检查isComposing状态，支持IME输入
- **热键识别**: 使用热键服务识别按键
- **状态检查**: 检查输入框光标位置和下拉菜单状态
- **左箭头**: 光标在开始位置时聚焦最右侧标签
- **右箭头**: 光标在结束位置时聚焦最左侧标签
- **退格键**: 输入框为空时删除最后一个标签

### 5. 标签键盘处理

```javascript
function onTagKeydown(ev) {
    const hotkey = getActiveHotkey(ev);
    const tags = [...ref.el.getElementsByClassName("o_tag")];
    const closestTag = ev.target.closest(".o_tag");
    const tagIndex = tags.indexOf(closestTag);
    const input = ref.el.querySelector(".o-autocomplete--input");
    
    switch (hotkey) {
        case "arrowleft": {
            if (tagIndex === 0) {
                input.focus();
            } else {
                focusTag(tagIndex - 1);
            }
            break;
        }
        case "arrowright": {
            if (tagIndex === tags.length - 1) {
                input.focus();
            } else {
                focusTag(tagIndex + 1);
            }
            break;
        }
        case "backspace": {
            input.focus();
            deleteTag(tagIndex);
            break;
        }
    }
    ev.preventDefault();
    ev.stopPropagation();
}
```

**标签键盘功能**:
- **索引计算**: 计算当前标签在列表中的索引
- **边界处理**: 处理第一个和最后一个标签的特殊情况
- **左箭头**: 向左导航或回到输入框
- **右箭头**: 向右导航或回到输入框
- **退格键**: 删除当前标签并回到输入框

## 使用场景

### 1. 基础标签导航

```javascript
// 在多记录选择器中使用标签导航
class TaggedRecordSelector extends Component {
    setup() {
        this.state = useState({
            selectedRecords: [1, 2, 3],
            tags: []
        });

        // 使用标签导航钩子
        this.onTagKeydown = useTagNavigation("recordSelector", this.deleteTag.bind(this));
    }

    deleteTag(index) {
        const newRecords = [...this.state.selectedRecords];
        newRecords.splice(index, 1);
        this.state.selectedRecords = newRecords;
        this.updateTags();
    }

    updateTags() {
        // 更新标签显示
        this.state.tags = this.state.selectedRecords.map((id, index) => ({
            id,
            text: `Record ${id}`,
            onDelete: () => this.deleteTag(index),
            onKeydown: this.onTagKeydown
        }));
    }

    render() {
        return xml`
            <div t-ref="recordSelector" class="tagged-record-selector">
                <AutoComplete/>
                <TagsList tags="state.tags"/>
            </div>
        `;
    }
}
```

### 2. 自定义标签导航行为

```javascript
// 自定义标签导航行为
class CustomTagNavigation extends Component {
    setup() {
        this.state = useState({
            tags: [],
            navigationMode: 'normal', // normal, circular, disabled
            allowDeletion: true
        });

        // 创建增强的删除函数
        this.enhancedDeleteTag = this.createEnhancedDeleteTag();
        this.onTagKeydown = useTagNavigation("tagContainer", this.enhancedDeleteTag);
    }

    createEnhancedDeleteTag() {
        return (index) => {
            if (!this.state.allowDeletion) {
                this.notification.add('删除功能已禁用', { type: 'warning' });
                return;
            }

            const tag = this.state.tags[index];
            
            // 确认删除
            if (tag.requireConfirmation) {
                this.showDeleteConfirmation(index);
                return;
            }

            this.performDelete(index);
        };
    }

    showDeleteConfirmation(index) {
        const tag = this.state.tags[index];
        this.dialog.add(ConfirmationDialog, {
            title: '确认删除',
            body: `确定要删除标签 "${tag.text}" 吗？`,
            confirm: () => this.performDelete(index),
            cancel: () => {}
        });
    }

    performDelete(index) {
        // 记录删除操作
        this.logTagDeletion(this.state.tags[index]);
        
        // 执行删除
        this.state.tags.splice(index, 1);
        
        // 触发删除事件
        this.onTagDeleted(index);
    }

    logTagDeletion(tag) {
        console.log('Tag deleted:', tag);
        // 可以发送分析事件
    }

    onTagDeleted(index) {
        // 处理删除后的逻辑
        this.updateRelatedData();
    }

    render() {
        return xml`
            <div t-ref="tagContainer" class="custom-tag-navigation">
                <div class="controls mb-2">
                    <div class="form-check form-check-inline">
                        <input 
                            class="form-check-input" 
                            type="checkbox" 
                            t-model="state.allowDeletion"
                        />
                        <label class="form-check-label">允许删除</label>
                    </div>
                </div>
                
                <AutoComplete/>
                <TagsList tags="state.tags"/>
            </div>
        `;
    }
}
```

### 3. 高级标签导航管理器

```javascript
// 高级标签导航管理器
class AdvancedTagNavigationManager extends Component {
    setup() {
        this.state = useState({
            tags: [],
            navigationHistory: [],
            focusedTagIndex: -1,
            keyboardShortcuts: {
                'ctrl+a': 'selectAll',
                'ctrl+d': 'deleteSelected',
                'escape': 'clearFocus'
            }
        });

        // 创建智能删除函数
        this.smartDeleteTag = this.createSmartDeleteTag();
        this.onTagKeydown = useTagNavigation("advancedTagContainer", this.smartDeleteTag);

        // 监听全局键盘事件
        this.setupGlobalKeyboardListeners();
    }

    createSmartDeleteTag() {
        return (index) => {
            const tag = this.state.tags[index];
            
            // 记录导航历史
            this.addToNavigationHistory('delete', index, tag);
            
            // 智能删除逻辑
            if (tag.protected) {
                this.handleProtectedTagDeletion(index, tag);
                return;
            }

            if (tag.hasRelatedData) {
                this.handleRelatedDataDeletion(index, tag);
                return;
            }

            // 普通删除
            this.performStandardDeletion(index);
        };
    }

    addToNavigationHistory(action, index, tag) {
        this.state.navigationHistory.push({
            action,
            index,
            tag: { ...tag },
            timestamp: Date.now()
        });

        // 保留最近50个操作
        if (this.state.navigationHistory.length > 50) {
            this.state.navigationHistory.shift();
        }
    }

    handleProtectedTagDeletion(index, tag) {
        this.notification.add(
            `标签 "${tag.text}" 受保护，无法删除`,
            { type: 'warning' }
        );
    }

    async handleRelatedDataDeletion(index, tag) {
        try {
            // 检查相关数据
            const relatedData = await this.checkRelatedData(tag);
            
            if (relatedData.length > 0) {
                this.showRelatedDataWarning(index, tag, relatedData);
            } else {
                this.performStandardDeletion(index);
            }
        } catch (error) {
            this.notification.add('检查相关数据时出错', { type: 'danger' });
        }
    }

    showRelatedDataWarning(index, tag, relatedData) {
        this.dialog.add(ConfirmationDialog, {
            title: '警告：存在相关数据',
            body: `删除标签 "${tag.text}" 将影响 ${relatedData.length} 个相关记录。确定继续吗？`,
            confirm: () => this.performStandardDeletion(index),
            cancel: () => {}
        });
    }

    performStandardDeletion(index) {
        // 动画效果
        this.animateTagDeletion(index);
        
        // 延迟删除以显示动画
        setTimeout(() => {
            this.state.tags.splice(index, 1);
            this.onTagDeleted(index);
        }, 200);
    }

    animateTagDeletion(index) {
        const tagElement = this.el.querySelectorAll('.o_tag')[index];
        if (tagElement) {
            tagElement.classList.add('deleting');
        }
    }

    setupGlobalKeyboardListeners() {
        useEffect(() => {
            const handleGlobalKeydown = (ev) => {
                const hotkey = getActiveHotkey(ev);
                const action = this.state.keyboardShortcuts[hotkey];
                
                if (action && this.isTagContainerFocused()) {
                    ev.preventDefault();
                    this[action]();
                }
            };

            document.addEventListener('keydown', handleGlobalKeydown);
            return () => {
                document.removeEventListener('keydown', handleGlobalKeydown);
            };
        });
    }

    isTagContainerFocused() {
        const container = this.el.querySelector('[t-ref="advancedTagContainer"]');
        return container && container.contains(document.activeElement);
    }

    selectAll() {
        const tags = this.el.querySelectorAll('.o_tag');
        tags.forEach(tag => tag.classList.add('selected'));
        this.notification.add('已选择所有标签', { type: 'info' });
    }

    deleteSelected() {
        const selectedTags = this.el.querySelectorAll('.o_tag.selected');
        const indices = Array.from(selectedTags).map(tag => {
            const allTags = Array.from(this.el.querySelectorAll('.o_tag'));
            return allTags.indexOf(tag);
        }).sort((a, b) => b - a); // 从后往前删除

        indices.forEach(index => {
            this.smartDeleteTag(index);
        });
    }

    clearFocus() {
        const focusedElement = document.activeElement;
        if (focusedElement && this.el.contains(focusedElement)) {
            focusedElement.blur();
        }
        this.state.focusedTagIndex = -1;
    }

    undoLastAction() {
        const lastAction = this.state.navigationHistory.pop();
        if (lastAction && lastAction.action === 'delete') {
            // 恢复删除的标签
            this.state.tags.splice(lastAction.index, 0, lastAction.tag);
            this.notification.add('已撤销删除操作', { type: 'success' });
        }
    }

    getNavigationStats() {
        const stats = {
            totalActions: this.state.navigationHistory.length,
            deleteActions: this.state.navigationHistory.filter(h => h.action === 'delete').length,
            averageActionsPerMinute: 0
        };

        if (stats.totalActions > 0) {
            const firstAction = this.state.navigationHistory[0];
            const lastAction = this.state.navigationHistory[this.state.navigationHistory.length - 1];
            const timeSpan = (lastAction.timestamp - firstAction.timestamp) / 60000; // 分钟
            stats.averageActionsPerMinute = stats.totalActions / timeSpan;
        }

        return stats;
    }

    render() {
        return xml`
            <div t-ref="advancedTagContainer" class="advanced-tag-navigation-manager">
                <div class="controls mb-3">
                    <div class="btn-group">
                        <button 
                            class="btn btn-sm btn-outline-primary"
                            t-on-click="selectAll"
                            title="全选 (Ctrl+A)"
                        >
                            <i class="fa fa-check-square"/> 全选
                        </button>
                        <button 
                            class="btn btn-sm btn-outline-danger"
                            t-on-click="deleteSelected"
                            title="删除选中 (Ctrl+D)"
                        >
                            <i class="fa fa-trash"/> 删除选中
                        </button>
                        <button 
                            class="btn btn-sm btn-outline-secondary"
                            t-on-click="undoLastAction"
                            title="撤销"
                        >
                            <i class="fa fa-undo"/> 撤销
                        </button>
                    </div>
                </div>

                <AutoComplete/>
                <TagsList tags="state.tags"/>

                <div class="navigation-stats mt-3">
                    <small class="text-muted">
                        操作历史: ${this.state.navigationHistory.length} 次操作
                    </small>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 键盘导航
- 完整的箭头键导航支持
- 智能的焦点管理
- 边界条件处理

### 2. 输入法支持
- IME输入状态检测
- 组合输入的正确处理
- 国际化键盘支持

### 3. 状态感知
- 自动完成菜单状态检测
- 输入框光标位置检测
- 标签索引计算

### 4. 事件管理
- 自动的事件监听器绑定
- 生命周期清理
- 事件传播控制

## 设计模式

### 1. 钩子模式 (Hook Pattern)
- 可重用的导航逻辑
- 组件间的逻辑共享

### 2. 观察者模式 (Observer Pattern)
- 键盘事件的监听和处理
- 状态变化的响应

### 3. 策略模式 (Strategy Pattern)
- 不同按键的处理策略
- 可配置的导航行为

## 注意事项

1. **无障碍性**: 确保键盘导航符合无障碍标准
2. **性能优化**: 避免频繁的DOM查询
3. **事件清理**: 正确清理事件监听器防止内存泄漏
4. **边界处理**: 正确处理边界情况和异常状态

## 扩展建议

1. **自定义快捷键**: 支持用户自定义键盘快捷键
2. **导航历史**: 记录和管理导航历史
3. **批量操作**: 支持批量选择和删除
4. **动画效果**: 添加导航和删除的动画效果
5. **触摸支持**: 支持触摸设备的手势导航

该标签导航钩子为Odoo Web应用提供了专业的键盘导航能力，通过智能的焦点管理和事件处理确保了优秀的用户体验和无障碍访问支持。

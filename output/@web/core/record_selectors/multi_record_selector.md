# MultiRecordSelector - 多记录选择器组件

## 概述

`multi_record_selector.js` 是 Odoo Web 核心模块的多记录选择器组件，提供了选择和管理多个数据库记录的用户界面。该组件结合了自动完成输入和标签列表显示，支持记录的添加、删除和导航，为用户提供了直观的多记录选择体验，广泛应用于关联字段、多选字段和记录集合管理等场景。

## 文件信息
- **路径**: `/web/static/src/core/record_selectors/multi_record_selector.js`
- **行数**: 83
- **模块**: `@web/core/record_selectors/multi_record_selector`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                              // OWL框架
'@web/core/tags_list/tags_list'                          // 标签列表组件
'@web/core/utils/hooks'                                  // 工具钩子
'@web/core/record_selectors/record_autocomplete'         // 记录自动完成组件
'@web/core/l10n/translation'                             // 国际化翻译
'@web/core/record_selectors/tag_navigation_hook'         // 标签导航钩子
```

## 核心功能

### 1. 组件属性定义

```javascript
static props = {
    resIds: { type: Array, element: Number },
    resModel: String,
    update: Function,
    domain: { type: Array, optional: true },
    context: { type: Object, optional: true },
    fieldString: { type: String, optional: true },
    placeholder: { type: String, optional: true },
};
```

**属性功能**:
- **resIds**: 当前选中的记录ID数组
- **resModel**: 目标数据模型名称
- **update**: 更新选中记录的回调函数
- **domain**: 可选的域过滤条件
- **context**: 可选的上下文对象
- **fieldString**: 可选的字段显示名称
- **placeholder**: 可选的占位符文本

### 2. 组件初始化

```javascript
setup() {
    this.nameService = useService("name");
    this.onTagKeydown = useTagNavigation("multiRecordSelector", this.deleteTag.bind(this));
    onWillStart(() => this.computeDerivedParams());
    onWillUpdateProps((nextProps) => this.computeDerivedParams(nextProps));
}
```

**初始化功能**:
- **名称服务**: 获取名称服务用于加载显示名称
- **标签导航**: 设置标签的键盘导航处理
- **参数计算**: 组件启动和属性更新时计算派生参数
- **生命周期**: 绑定组件生命周期钩子

### 3. 派生参数计算

```javascript
async computeDerivedParams(props = this.props) {
    const displayNames = await this.getDisplayNames(props);
    this.tags = this.getTags(props, displayNames);
}

async getDisplayNames(props) {
    const ids = this.getIds(props);
    return this.nameService.loadDisplayNames(props.resModel, ids);
}
```

**参数计算功能**:
- **显示名称**: 异步加载记录的显示名称
- **标签生成**: 根据记录ID和显示名称生成标签
- **数据同步**: 确保显示数据与属性数据同步
- **错误处理**: 处理无法访问的记录

### 4. 占位符逻辑

```javascript
get placeholder() {
    return this.getIds().length ? "" : this.props.placeholder;
}
```

**占位符功能**:
- **动态显示**: 有标签时隐藏占位符
- **用户体验**: 避免占位符与标签冲突
- **智能切换**: 根据选中状态智能切换
- **视觉清晰**: 保持界面的视觉清晰度

### 5. 标签生成

```javascript
getTags(props, displayNames) {
    return props.resIds.map((id, index) => {
        const text =
            typeof displayNames[id] === "string"
                ? displayNames[id]
                : _t("Inaccessible/missing record ID: %s", id);
        return {
            text,
            onDelete: () => {
                this.deleteTag(index);
            },
            onKeydown: this.onTagKeydown,
        };
    });
}
```

**标签生成功能**:
- **文本显示**: 使用显示名称或错误信息
- **删除回调**: 为每个标签绑定删除回调
- **键盘导航**: 绑定键盘导航处理
- **错误处理**: 处理无法访问的记录显示

### 6. 记录管理

```javascript
deleteTag(index) {
    this.props.update([
        ...this.props.resIds.slice(0, index),
        ...this.props.resIds.slice(index + 1),
    ]);
}

update(resIds) {
    this.props.update([...this.props.resIds, ...resIds]);
}
```

**记录管理功能**:
- **删除记录**: 通过索引删除指定记录
- **添加记录**: 添加新选中的记录
- **数组操作**: 使用数组切片进行不可变更新
- **状态同步**: 通过回调同步父组件状态

## 使用场景

### 1. 基础多记录选择

```javascript
// 基础多记录选择器
class BasicMultiSelector extends Component {
    setup() {
        this.state = useState({
            selectedIds: [1, 2, 3],
            resModel: 'res.partner'
        });
    }

    updateSelection(newIds) {
        this.state.selectedIds = newIds;
    }

    render() {
        return xml`
            <MultiRecordSelector
                resIds="state.selectedIds"
                resModel="state.resModel"
                update="updateSelection"
                placeholder="选择合作伙伴..."
            />
        `;
    }
}
```

### 2. 带域过滤的选择器

```javascript
// 带域过滤的多记录选择器
class FilteredMultiSelector extends Component {
    setup() {
        this.state = useState({
            selectedUserIds: [],
            domain: [['active', '=', true], ['is_company', '=', false]]
        });
    }

    updateUsers(userIds) {
        this.state.selectedUserIds = userIds;
        this.onUsersChanged(userIds);
    }

    onUsersChanged(userIds) {
        // 处理用户选择变化
        console.log('Selected users:', userIds);
    }

    render() {
        return xml`
            <div class="user-selector">
                <label class="form-label">选择用户</label>
                <MultiRecordSelector
                    resIds="state.selectedUserIds"
                    resModel="res.users"
                    domain="state.domain"
                    update="updateUsers"
                    fieldString="用户"
                    placeholder="搜索并选择用户..."
                />
            </div>
        `;
    }
}
```

### 3. 动态上下文选择器

```javascript
// 带动态上下文的多记录选择器
class ContextualMultiSelector extends Component {
    setup() {
        this.state = useState({
            selectedProductIds: [],
            categoryId: null,
            context: {}
        });

        // 监听分类变化
        useEffect(() => {
            this.updateContext();
        }, () => [this.state.categoryId]);
    }

    updateContext() {
        this.state.context = {
            default_categ_id: this.state.categoryId,
            active_test: true
        };
    }

    updateProducts(productIds) {
        this.state.selectedProductIds = productIds;
        this.validateSelection(productIds);
    }

    validateSelection(productIds) {
        // 验证选择的产品
        if (productIds.length > 10) {
            this.notification.add('最多只能选择10个产品', { type: 'warning' });
        }
    }

    setCategoryFilter(categoryId) {
        this.state.categoryId = categoryId;
        // 清空当前选择，因为分类已改变
        this.state.selectedProductIds = [];
    }

    render() {
        return xml`
            <div class="product-selector">
                <div class="mb-3">
                    <label class="form-label">产品分类</label>
                    <select 
                        class="form-select"
                        t-model="state.categoryId"
                        t-on-change="() => this.setCategoryFilter(state.categoryId)"
                    >
                        <option value="">所有分类</option>
                        <option value="1">电子产品</option>
                        <option value="2">服装</option>
                        <option value="3">家具</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">
                        选择产品 (${this.state.selectedProductIds.length}/10)
                    </label>
                    <MultiRecordSelector
                        resIds="state.selectedProductIds"
                        resModel="product.product"
                        domain="[['categ_id', '=', state.categoryId || false]]"
                        context="state.context"
                        update="updateProducts"
                        fieldString="产品"
                        placeholder="搜索产品..."
                    />
                </div>
            </div>
        `;
    }
}
```

### 4. 高级多记录管理器

```javascript
// 高级多记录管理器
class AdvancedMultiRecordManager extends Component {
    setup() {
        this.state = useState({
            selectedRecords: [],
            recentSelections: [],
            favorites: [],
            searchHistory: [],
            groupedSelections: {}
        });

        this.orm = useService('orm');
        this.notification = useService('notification');
    }

    async updateSelection(recordIds) {
        // 更新选择
        this.state.selectedRecords = recordIds;
        
        // 更新最近选择
        this.updateRecentSelections(recordIds);
        
        // 验证选择
        await this.validateSelection(recordIds);
        
        // 分组显示
        this.groupSelections(recordIds);
    }

    updateRecentSelections(recordIds) {
        const newSelections = recordIds.filter(id => 
            !this.state.recentSelections.includes(id)
        );
        
        this.state.recentSelections = [
            ...newSelections,
            ...this.state.recentSelections
        ].slice(0, 20); // 保留最近20个
    }

    async validateSelection(recordIds) {
        try {
            // 检查记录是否存在且可访问
            const records = await this.orm.read(
                this.props.resModel,
                recordIds,
                ['id', 'name']
            );
            
            const validIds = records.map(r => r.id);
            const invalidIds = recordIds.filter(id => !validIds.includes(id));
            
            if (invalidIds.length > 0) {
                this.notification.add(
                    `发现 ${invalidIds.length} 个无效记录`,
                    { type: 'warning' }
                );
            }
        } catch (error) {
            this.notification.add(
                '验证记录时出错',
                { type: 'danger' }
            );
        }
    }

    groupSelections(recordIds) {
        // 按某种逻辑分组（这里简单按ID范围分组）
        const groups = {
            recent: [],
            older: []
        };
        
        recordIds.forEach(id => {
            if (id > 1000) {
                groups.recent.push(id);
            } else {
                groups.older.push(id);
            }
        });
        
        this.state.groupedSelections = groups;
    }

    addToFavorites() {
        const newFavorites = this.state.selectedRecords.filter(id =>
            !this.state.favorites.includes(id)
        );
        
        this.state.favorites = [...this.state.favorites, ...newFavorites];
        
        this.notification.add(
            `已添加 ${newFavorites.length} 个记录到收藏`,
            { type: 'success' }
        );
    }

    loadFavorites() {
        this.updateSelection([...this.state.favorites]);
    }

    clearSelection() {
        this.updateSelection([]);
    }

    exportSelection() {
        const data = {
            selectedRecords: this.state.selectedRecords,
            timestamp: new Date().toISOString(),
            model: this.props.resModel
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `selection_${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    render() {
        return xml`
            <div class="advanced-multi-record-manager">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>记录选择器</h5>
                    <div class="btn-group">
                        <button 
                            class="btn btn-sm btn-outline-primary"
                            t-on-click="addToFavorites"
                            t-att-disabled="!state.selectedRecords.length"
                        >
                            <i class="fa fa-star"/> 添加到收藏
                        </button>
                        <button 
                            class="btn btn-sm btn-outline-secondary"
                            t-on-click="loadFavorites"
                            t-att-disabled="!state.favorites.length"
                        >
                            <i class="fa fa-heart"/> 加载收藏
                        </button>
                        <button 
                            class="btn btn-sm btn-outline-info"
                            t-on-click="exportSelection"
                            t-att-disabled="!state.selectedRecords.length"
                        >
                            <i class="fa fa-download"/> 导出
                        </button>
                        <button 
                            class="btn btn-sm btn-outline-danger"
                            t-on-click="clearSelection"
                            t-att-disabled="!state.selectedRecords.length"
                        >
                            <i class="fa fa-trash"/> 清空
                        </button>
                    </div>
                </div>

                <MultiRecordSelector
                    resIds="state.selectedRecords"
                    resModel="props.resModel"
                    domain="props.domain"
                    context="props.context"
                    update="updateSelection"
                    fieldString="props.fieldString"
                    placeholder="搜索并选择记录..."
                />

                <div class="mt-3" t-if="state.selectedRecords.length">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>选择统计</h6>
                            <ul class="list-unstyled">
                                <li>总计: ${state.selectedRecords.length}</li>
                                <li>最近: ${state.groupedSelections.recent?.length || 0}</li>
                                <li>较早: ${state.groupedSelections.older?.length || 0}</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>收藏记录</h6>
                            <p class="text-muted">
                                ${state.favorites.length} 个收藏记录
                            </p>
                        </div>
                    </div>
                </div>

                <div class="mt-3" t-if="state.recentSelections.length">
                    <h6>最近选择</h6>
                    <div class="d-flex flex-wrap gap-1">
                        <t t-foreach="state.recentSelections.slice(0, 10)" t-as="recentId" t-key="recentId">
                            <span 
                                class="badge bg-light text-dark cursor-pointer"
                                t-on-click="() => this.updateSelection([...state.selectedRecords, recentId])"
                            >
                                ID: ${recentId}
                            </span>
                        </t>
                    </div>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 组件组合
- 结合自动完成和标签列表
- 模块化的组件设计
- 清晰的职责分离

### 2. 异步数据处理
- 异步加载显示名称
- 优雅的错误处理
- 数据同步机制

### 3. 键盘导航
- 完整的键盘支持
- 标签间导航
- 删除操作快捷键

### 4. 状态管理
- 不可变状态更新
- 父子组件通信
- 生命周期管理

## 设计模式

### 1. 组合模式 (Composition Pattern)
- 组合多个子组件
- 统一的接口管理

### 2. 观察者模式 (Observer Pattern)
- 属性变化监听
- 状态同步更新

### 3. 策略模式 (Strategy Pattern)
- 不同的更新策略
- 灵活的配置选项

## 注意事项

1. **性能优化**: 避免频繁的名称服务调用
2. **错误处理**: 妥善处理无法访问的记录
3. **用户体验**: 提供清晰的视觉反馈
4. **数据一致性**: 确保显示数据与实际数据同步

## 扩展建议

1. **批量操作**: 支持批量添加和删除
2. **拖拽排序**: 支持标签的拖拽排序
3. **分组显示**: 支持记录的分组显示
4. **搜索历史**: 记录和管理搜索历史
5. **收藏功能**: 支持常用记录的收藏

该多记录选择器组件为Odoo Web应用提供了强大的多记录选择和管理能力，通过组件组合和异步数据处理确保了良好的用户体验。

# RecordSelector - 单记录选择器组件

## 概述

`record_selector.js` 是 Odoo Web 核心模块的单记录选择器组件，提供了选择单个数据库记录的用户界面。该组件基于RecordAutocomplete构建，专门用于单选场景，支持记录的搜索、选择和显示，为用户提供了简洁直观的单记录选择体验，广泛应用于Many2one字段、单选关联字段和记录引用等场景。

## 文件信息
- **路径**: `/web/static/src/core/record_selectors/record_selector.js`
- **行数**: 62
- **模块**: `@web/core/record_selectors/record_selector`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                              // OWL框架
'@web/core/utils/hooks'                                  // 工具钩子
'@web/core/record_selectors/record_autocomplete'         // 记录自动完成组件
'@web/core/l10n/translation'                             // 国际化翻译
```

## 核心功能

### 1. 组件属性定义

```javascript
static props = {
    resId: [Number, { value: false }],
    resModel: String,
    update: Function,
    domain: { type: Array, optional: true },
    context: { type: Object, optional: true },
    fieldString: { type: String, optional: true },
    placeholder: { type: String, optional: true },
};
```

**属性功能**:
- **resId**: 当前选中的记录ID，可以是数字或false
- **resModel**: 目标数据模型名称
- **update**: 更新选中记录的回调函数
- **domain**: 可选的域过滤条件
- **context**: 可选的上下文对象
- **fieldString**: 可选的字段显示名称
- **placeholder**: 可选的占位符文本

### 2. 组件初始化

```javascript
setup() {
    this.nameService = useService("name");
    onWillStart(() => this.computeDerivedParams());
    onWillUpdateProps((nextProps) => this.computeDerivedParams(nextProps));
}
```

**初始化功能**:
- **名称服务**: 获取名称服务用于加载显示名称
- **参数计算**: 组件启动时计算派生参数
- **属性监听**: 属性更新时重新计算参数
- **生命周期**: 绑定组件生命周期钩子

### 3. 派生参数计算

```javascript
async computeDerivedParams(props = this.props) {
    const displayNames = await this.getDisplayNames(props);
    this.displayName = this.getDisplayName(props, displayNames);
}

async getDisplayNames(props) {
    const ids = this.getIds(props);
    return this.nameService.loadDisplayNames(props.resModel, ids);
}
```

**参数计算功能**:
- **显示名称**: 异步加载记录的显示名称
- **数据同步**: 确保显示数据与属性数据同步
- **缓存利用**: 利用名称服务的缓存机制
- **错误处理**: 处理无法访问的记录

### 4. 显示名称处理

```javascript
getDisplayName(props = this.props, displayNames) {
    const { resId } = props;
    if (resId === false) {
        return "";
    }
    return typeof displayNames[resId] === "string"
        ? displayNames[resId]
        : _t("Inaccessible/missing record ID: %s", resId);
}
```

**显示名称功能**:
- **空值处理**: resId为false时返回空字符串
- **名称获取**: 从缓存中获取显示名称
- **错误显示**: 无法访问的记录显示错误信息
- **国际化**: 错误信息支持国际化

### 5. ID管理

```javascript
getIds(props = this.props) {
    if (props.resId) {
        return [props.resId];
    }
    return [];
}
```

**ID管理功能**:
- **数组转换**: 将单个ID转换为数组格式
- **空值处理**: 无ID时返回空数组
- **兼容性**: 与多选组件保持接口一致
- **简化逻辑**: 简化单选场景的处理

### 6. 更新处理

```javascript
update(resIds) {
    this.props.update(resIds[0] || false);
    this.render(true);
}
```

**更新处理功能**:
- **单选提取**: 从数组中提取第一个ID
- **空值处理**: 无选择时传递false
- **回调调用**: 调用父组件的更新回调
- **强制渲染**: 强制重新渲染组件

## 使用场景

### 1. 基础单记录选择

```javascript
// 基础单记录选择器
class BasicRecordSelector extends Component {
    setup() {
        this.state = useState({
            selectedPartnerId: false,
            partnerModel: 'res.partner'
        });
    }

    updatePartner(partnerId) {
        this.state.selectedPartnerId = partnerId;
        this.onPartnerChanged(partnerId);
    }

    onPartnerChanged(partnerId) {
        console.log('Selected partner:', partnerId);
        // 处理合作伙伴选择变化
    }

    render() {
        return xml`
            <div class="partner-selector">
                <label class="form-label">选择合作伙伴</label>
                <RecordSelector
                    resId="state.selectedPartnerId"
                    resModel="state.partnerModel"
                    update="updatePartner"
                    placeholder="搜索合作伙伴..."
                />
            </div>
        `;
    }
}
```

### 2. 带过滤条件的选择器

```javascript
// 带过滤条件的单记录选择器
class FilteredRecordSelector extends Component {
    setup() {
        this.state = useState({
            selectedUserId: false,
            companyId: 1,
            activeOnly: true
        });
    }

    get userDomain() {
        const domain = [];
        
        if (this.state.activeOnly) {
            domain.push(['active', '=', true]);
        }
        
        if (this.state.companyId) {
            domain.push(['company_id', '=', this.state.companyId]);
        }
        
        return domain;
    }

    get userContext() {
        return {
            default_company_id: this.state.companyId,
            active_test: this.state.activeOnly
        };
    }

    updateUser(userId) {
        this.state.selectedUserId = userId;
        this.validateUserSelection(userId);
    }

    async validateUserSelection(userId) {
        if (userId) {
            try {
                const user = await this.orm.read('res.users', [userId], ['active', 'company_id']);
                if (user.length > 0) {
                    const userData = user[0];
                    if (!userData.active) {
                        this.notification.add('选择的用户已被停用', { type: 'warning' });
                    }
                }
            } catch (error) {
                this.notification.add('验证用户信息失败', { type: 'danger' });
            }
        }
    }

    toggleActiveFilter() {
        this.state.activeOnly = !this.state.activeOnly;
        // 如果当前选中的用户不符合新的过滤条件，清空选择
        if (this.state.selectedUserId && !this.state.activeOnly) {
            this.state.selectedUserId = false;
        }
    }

    render() {
        return xml`
            <div class="filtered-user-selector">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">公司</label>
                        <select class="form-select" t-model="state.companyId">
                            <option value="1">主公司</option>
                            <option value="2">分公司A</option>
                            <option value="3">分公司B</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check mt-4">
                            <input 
                                class="form-check-input" 
                                type="checkbox" 
                                t-model="state.activeOnly"
                                t-on-change="toggleActiveFilter"
                            />
                            <label class="form-check-label">
                                只显示活跃用户
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">选择用户</label>
                    <RecordSelector
                        resId="state.selectedUserId"
                        resModel="res.users"
                        domain="userDomain"
                        context="userContext"
                        update="updateUser"
                        fieldString="用户"
                        placeholder="搜索用户..."
                    />
                </div>
            </div>
        `;
    }
}
```

### 3. 级联选择器

```javascript
// 级联单记录选择器
class CascadingRecordSelector extends Component {
    setup() {
        this.state = useState({
            selectedCountryId: false,
            selectedStateId: false,
            selectedCityId: false
        });

        this.orm = useService('orm');
    }

    get stateDomain() {
        return this.state.selectedCountryId 
            ? [['country_id', '=', this.state.selectedCountryId]]
            : [['id', '=', false]]; // 没有选择国家时不显示任何州
    }

    get cityDomain() {
        return this.state.selectedStateId
            ? [['state_id', '=', this.state.selectedStateId]]
            : [['id', '=', false]]; // 没有选择州时不显示任何城市
    }

    updateCountry(countryId) {
        this.state.selectedCountryId = countryId;
        // 清空下级选择
        this.state.selectedStateId = false;
        this.state.selectedCityId = false;
    }

    updateState(stateId) {
        this.state.selectedStateId = stateId;
        // 清空下级选择
        this.state.selectedCityId = false;
    }

    updateCity(cityId) {
        this.state.selectedCityId = cityId;
        this.onLocationChanged();
    }

    onLocationChanged() {
        const location = {
            country: this.state.selectedCountryId,
            state: this.state.selectedStateId,
            city: this.state.selectedCityId
        };
        console.log('Location changed:', location);
        // 处理位置变化
    }

    render() {
        return xml`
            <div class="cascading-selector">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label">国家</label>
                        <RecordSelector
                            resId="state.selectedCountryId"
                            resModel="res.country"
                            update="updateCountry"
                            placeholder="选择国家..."
                        />
                    </div>
                    
                    <div class="col-md-4">
                        <label class="form-label">州/省</label>
                        <RecordSelector
                            resId="state.selectedStateId"
                            resModel="res.country.state"
                            domain="stateDomain"
                            update="updateState"
                            placeholder="选择州/省..."
                            t-att-disabled="!state.selectedCountryId"
                        />
                    </div>
                    
                    <div class="col-md-4">
                        <label class="form-label">城市</label>
                        <RecordSelector
                            resId="state.selectedCityId"
                            resModel="res.city"
                            domain="cityDomain"
                            update="updateCity"
                            placeholder="选择城市..."
                            t-att-disabled="!state.selectedStateId"
                        />
                    </div>
                </div>
            </div>
        `;
    }
}
```

### 4. 高级单记录管理器

```javascript
// 高级单记录管理器
class AdvancedRecordManager extends Component {
    setup() {
        this.state = useState({
            selectedRecordId: false,
            recordHistory: [],
            favorites: [],
            recentSelections: [],
            recordDetails: null,
            showDetails: false
        });

        this.orm = useService('orm');
        this.notification = useService('notification');
    }

    async updateRecord(recordId) {
        const previousId = this.state.selectedRecordId;
        this.state.selectedRecordId = recordId;
        
        // 添加到历史记录
        if (previousId && previousId !== recordId) {
            this.addToHistory(previousId);
        }
        
        // 添加到最近选择
        if (recordId) {
            this.addToRecentSelections(recordId);
            await this.loadRecordDetails(recordId);
        } else {
            this.state.recordDetails = null;
            this.state.showDetails = false;
        }
    }

    addToHistory(recordId) {
        if (!this.state.recordHistory.includes(recordId)) {
            this.state.recordHistory = [recordId, ...this.state.recordHistory].slice(0, 10);
        }
    }

    addToRecentSelections(recordId) {
        this.state.recentSelections = [
            recordId,
            ...this.state.recentSelections.filter(id => id !== recordId)
        ].slice(0, 20);
    }

    async loadRecordDetails(recordId) {
        try {
            const records = await this.orm.read(
                this.props.resModel,
                [recordId],
                ['id', 'name', 'display_name', 'create_date', 'write_date']
            );
            
            if (records.length > 0) {
                this.state.recordDetails = records[0];
            }
        } catch (error) {
            this.notification.add('加载记录详情失败', { type: 'danger' });
        }
    }

    addToFavorites() {
        const recordId = this.state.selectedRecordId;
        if (recordId && !this.state.favorites.includes(recordId)) {
            this.state.favorites = [...this.state.favorites, recordId];
            this.notification.add('已添加到收藏', { type: 'success' });
        }
    }

    removeFromFavorites(recordId) {
        this.state.favorites = this.state.favorites.filter(id => id !== recordId);
    }

    selectFromHistory(recordId) {
        this.updateRecord(recordId);
    }

    selectFromFavorites(recordId) {
        this.updateRecord(recordId);
    }

    toggleDetails() {
        this.state.showDetails = !this.state.showDetails;
    }

    clearSelection() {
        this.updateRecord(false);
    }

    render() {
        return xml`
            <div class="advanced-record-manager">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>记录选择器</h5>
                    <div class="btn-group">
                        <button 
                            class="btn btn-sm btn-outline-warning"
                            t-on-click="addToFavorites"
                            t-att-disabled="!state.selectedRecordId"
                            title="添加到收藏"
                        >
                            <i class="fa fa-star"/>
                        </button>
                        <button 
                            class="btn btn-sm btn-outline-info"
                            t-on-click="toggleDetails"
                            t-att-disabled="!state.selectedRecordId"
                            title="显示详情"
                        >
                            <i class="fa fa-info-circle"/>
                        </button>
                        <button 
                            class="btn btn-sm btn-outline-danger"
                            t-on-click="clearSelection"
                            t-att-disabled="!state.selectedRecordId"
                            title="清空选择"
                        >
                            <i class="fa fa-times"/>
                        </button>
                    </div>
                </div>

                <RecordSelector
                    resId="state.selectedRecordId"
                    resModel="props.resModel"
                    domain="props.domain"
                    context="props.context"
                    update="updateRecord"
                    fieldString="props.fieldString"
                    placeholder="搜索记录..."
                />

                <div class="record-details mt-3" t-if="state.showDetails and state.recordDetails">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">记录详情</h6>
                        </div>
                        <div class="card-body">
                            <dl class="row">
                                <dt class="col-sm-3">ID:</dt>
                                <dd class="col-sm-9">${state.recordDetails.id}</dd>
                                
                                <dt class="col-sm-3">名称:</dt>
                                <dd class="col-sm-9">${state.recordDetails.display_name || state.recordDetails.name}</dd>
                                
                                <dt class="col-sm-3">创建时间:</dt>
                                <dd class="col-sm-9">${state.recordDetails.create_date}</dd>
                                
                                <dt class="col-sm-3">修改时间:</dt>
                                <dd class="col-sm-9">${state.recordDetails.write_date}</dd>
                            </dl>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6" t-if="state.recordHistory.length">
                        <h6>历史记录</h6>
                        <div class="list-group">
                            <t t-foreach="state.recordHistory.slice(0, 5)" t-as="historyId" t-key="historyId">
                                <button 
                                    class="list-group-item list-group-item-action"
                                    t-on-click="() => this.selectFromHistory(historyId)"
                                >
                                    ID: ${historyId}
                                </button>
                            </t>
                        </div>
                    </div>
                    
                    <div class="col-md-6" t-if="state.favorites.length">
                        <h6>收藏记录</h6>
                        <div class="list-group">
                            <t t-foreach="state.favorites.slice(0, 5)" t-as="favoriteId" t-key="favoriteId">
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <button 
                                        class="btn btn-link p-0"
                                        t-on-click="() => this.selectFromFavorites(favoriteId)"
                                    >
                                        ID: ${favoriteId}
                                    </button>
                                    <button 
                                        class="btn btn-sm btn-outline-danger"
                                        t-on-click="() => this.removeFromFavorites(favoriteId)"
                                    >
                                        <i class="fa fa-trash"/>
                                    </button>
                                </div>
                            </t>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 单选专用
- 专门为单选场景设计
- 简化的接口和逻辑
- 优化的用户体验

### 2. 自动完成集成
- 基于RecordAutocomplete构建
- 继承所有搜索功能
- 保持接口一致性

### 3. 异步数据处理
- 异步加载显示名称
- 智能的缓存利用
- 优雅的错误处理

### 4. 生命周期管理
- 完整的生命周期钩子
- 属性变化监听
- 自动的数据同步

## 设计模式

### 1. 适配器模式 (Adapter Pattern)
- 将多选接口适配为单选
- 简化复杂的多选逻辑

### 2. 装饰器模式 (Decorator Pattern)
- 在自动完成基础上添加单选功能
- 保持原有功能不变

### 3. 模板方法模式 (Template Method Pattern)
- 定义单选的基本流程
- 可扩展的处理逻辑

## 注意事项

1. **数据一致性**: 确保显示数据与实际数据同步
2. **性能优化**: 避免不必要的名称服务调用
3. **错误处理**: 妥善处理无法访问的记录
4. **用户体验**: 提供清晰的选择反馈

## 扩展建议

1. **快速选择**: 支持最近选择和收藏功能
2. **详情显示**: 显示选中记录的详细信息
3. **历史记录**: 记录和管理选择历史
4. **验证功能**: 添加记录选择的验证逻辑
5. **批量操作**: 支持批量相关操作

该单记录选择器组件为Odoo Web应用提供了简洁高效的单记录选择能力，通过专门的单选设计确保了优秀的用户体验和性能表现。

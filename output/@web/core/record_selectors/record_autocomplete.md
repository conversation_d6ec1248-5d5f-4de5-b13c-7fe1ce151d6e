# RecordAutocomplete - 记录自动完成组件

## 概述

`record_autocomplete.js` 是 Odoo Web 核心模块的记录自动完成组件，提供了基于输入搜索和选择数据库记录的功能。该组件集成了自动完成输入框、搜索功能、选择对话框和名称缓存，支持单选和多选模式，为用户提供了高效的记录查找和选择体验，广泛应用于关联字段、查找字段和记录引用等场景。

## 文件信息
- **路径**: `/web/static/src/core/record_selectors/record_autocomplete.js`
- **行数**: 143
- **模块**: `@web/core/record_selectors/record_autocomplete`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                              // OWL框架
'@web/core/autocomplete/autocomplete'    // 自动完成组件
'@web/core/l10n/translation'             // 国际化翻译
'@web/core/domain'                       // 域处理
'@web/core/registry'                     // 注册表系统
'@web/core/utils/hooks'                  // 工具钩子
```

## 核心功能

### 1. 常量定义

```javascript
const SEARCH_LIMIT = 7;
const SEARCH_MORE_LIMIT = 320;
```

**常量功能**:
- **搜索限制**: 默认显示7个搜索结果
- **扩展搜索**: "搜索更多"时最多显示320个结果
- **性能优化**: 限制结果数量提升性能
- **用户体验**: 平衡显示数量和加载速度

### 2. 组件属性定义

```javascript
static props = {
    resModel: String,
    update: Function,
    multiSelect: Boolean,
    getIds: Function,
    value: String,
    domain: { type: Array, optional: true },
    context: { type: Object, optional: true },
    className: { type: String, optional: true },
    fieldString: { type: String, optional: true },
    placeholder: { type: String, optional: true },
};
```

**属性功能**:
- **resModel**: 目标数据模型名称
- **update**: 选择记录时的更新回调
- **multiSelect**: 是否支持多选模式
- **getIds**: 获取当前选中ID的函数
- **value**: 当前输入值
- **domain**: 可选的域过滤条件
- **context**: 可选的上下文对象
- **className**: 可选的自定义CSS类
- **fieldString**: 可选的字段显示名称
- **placeholder**: 可选的占位符文本

### 3. 组件初始化

```javascript
setup() {
    this.orm = useService("orm");
    this.nameService = useService("name");
    this.addDialog = useOwnedDialogs();
    this.sources = [
        {
            placeholder: _t("Loading..."),
            options: this.loadOptionsSource.bind(this),
        },
    ];
}
```

**初始化功能**:
- **ORM服务**: 获取ORM服务用于数据操作
- **名称服务**: 获取名称服务用于缓存显示名称
- **对话框**: 设置对话框管理
- **数据源**: 配置自动完成的数据源

### 4. 名称缓存管理

```javascript
addNames(options) {
    const displayNames = Object.fromEntries(options);
    this.nameService.addDisplayNames(this.props.resModel, displayNames);
}
```

**缓存管理功能**:
- **名称缓存**: 将搜索结果添加到名称缓存
- **性能优化**: 避免重复加载相同记录的名称
- **数据共享**: 与其他组件共享名称数据
- **内存管理**: 统一的名称缓存管理

### 5. 选项加载

```javascript
async loadOptionsSource(name) {
    if (this.lastProm) {
        this.lastProm.abort(false);
    }
    this.lastProm = this.search(name, SEARCH_LIMIT + 1);
    const nameGets = (await this.lastProm).map(([id, label]) => ([id, label ? label.split("\n")[0] : _t("Unnamed")]));
    this.addNames(nameGets);
    const options = nameGets.map(([value, label]) => ({value, label}));
    if (SEARCH_LIMIT < nameGets.length) {
        options.push({
            label: _t("Search More..."),
            action: this.onSearchMore.bind(this, name),
            classList: "o_m2o_dropdown_option",
        });
    }
    if (options.length === 0) {
        options.push({ label: _t("(no result)"), unselectable: true });
    }
    return options;
}
```

**选项加载功能**:
- **请求取消**: 取消之前的搜索请求
- **结果处理**: 处理搜索结果并格式化
- **名称处理**: 处理多行名称，只显示第一行
- **扩展搜索**: 结果超过限制时提供"搜索更多"选项
- **空结果**: 无结果时显示提示信息

### 6. 扩展搜索

```javascript
async onSearchMore(name) {
    const { fieldString, multiSelect, resModel } = this.props;
    let operator;
    const ids = [];
    if (name) {
        const nameGets = await this.search(name, SEARCH_MORE_LIMIT);
        this.addNames(nameGets);
        operator = "in";
        ids.push(...nameGets.map((nameGet) => nameGet[0]));
    } else {
        operator = "not in";
        ids.push(...this.getIds());
    }
    const dynamicFilters = ids.length
        ? [
              {
                  description: _t("Quick search: %s", name),
                  domain: [["id", operator, ids]],
              },
          ]
        : undefined;
    
    const SelectCreateDialog = registry.category("dialogs").get("select_create");
    this.addDialog(SelectCreateDialog, {
        title: _t("Search: %s", fieldString),
        dynamicFilters,
        domain: this.getDomain(),
        resModel,
        noCreate: true,
        multiSelect,
        context: this.props.context || {},
        onSelected: (resId) => {
            const resIds = Array.isArray(resId) ? resId : [resId];
            this.props.update([...resIds]);
        },
    });
}
```

**扩展搜索功能**:
- **大量搜索**: 搜索更多记录（最多320个）
- **动态过滤**: 根据搜索词创建动态过滤器
- **对话框**: 打开选择创建对话框
- **多选支持**: 支持单选和多选模式
- **结果处理**: 统一处理选择结果

### 7. 域处理

```javascript
getDomain() {
    const domainIds = Domain.not([["id", "in", this.getIds()]]);
    if (this.props.domain) {
        return Domain.and([this.props.domain, domainIds]).toList();
    }
    return domainIds.toList();
}
```

**域处理功能**:
- **排除已选**: 排除已选中的记录ID
- **域合并**: 合并用户提供的域和排除域
- **域转换**: 将域对象转换为列表格式
- **过滤逻辑**: 确保搜索结果的相关性

### 8. 搜索和选择

```javascript
search(name, limit) {
    const domain = this.getDomain();
    return this.orm.call(this.props.resModel, "name_search", [], {
        name,
        args: domain,
        limit,
        context: this.props.context || {},
    });
}

onSelect({ value: resId, action }, params) {
    if (action) {
        return action(params);
    }
    this.props.update([resId]);
}
```

**搜索选择功能**:
- **名称搜索**: 调用模型的name_search方法
- **域应用**: 应用过滤域限制搜索范围
- **选择处理**: 处理记录选择和动作执行
- **更新回调**: 调用更新回调通知父组件

## 使用场景

### 1. 基础记录自动完成

```javascript
// 基础记录自动完成
class BasicRecordAutocomplete extends Component {
    setup() {
        this.state = useState({
            selectedId: null,
            inputValue: ''
        });
    }

    updateRecord(recordIds) {
        this.state.selectedId = recordIds[0] || null;
        this.state.inputValue = '';
    }

    getCurrentIds() {
        return this.state.selectedId ? [this.state.selectedId] : [];
    }

    render() {
        return xml`
            <RecordAutocomplete
                resModel="res.partner"
                update="updateRecord"
                multiSelect="false"
                getIds="getCurrentIds"
                value="state.inputValue"
                placeholder="搜索合作伙伴..."
            />
        `;
    }
}
```

### 2. 多选记录自动完成

```javascript
// 多选记录自动完成
class MultiSelectAutocomplete extends Component {
    setup() {
        this.state = useState({
            selectedIds: [],
            searchValue: ''
        });
    }

    updateRecords(recordIds) {
        // 添加新选中的记录，避免重复
        const newIds = recordIds.filter(id => !this.state.selectedIds.includes(id));
        this.state.selectedIds = [...this.state.selectedIds, ...newIds];
        this.state.searchValue = '';
    }

    removeRecord(recordId) {
        this.state.selectedIds = this.state.selectedIds.filter(id => id !== recordId);
    }

    getCurrentIds() {
        return this.state.selectedIds;
    }

    render() {
        return xml`
            <div class="multi-select-autocomplete">
                <div class="selected-records mb-2" t-if="state.selectedIds.length">
                    <t t-foreach="state.selectedIds" t-as="recordId" t-key="recordId">
                        <span class="badge bg-primary me-1">
                            ID: ${recordId}
                            <button 
                                class="btn-close btn-close-white ms-1"
                                t-on-click="() => this.removeRecord(recordId)"
                            />
                        </span>
                    </t>
                </div>
                
                <RecordAutocomplete
                    resModel="product.product"
                    update="updateRecords"
                    multiSelect="true"
                    getIds="getCurrentIds"
                    value="state.searchValue"
                    domain="[['sale_ok', '=', true]]"
                    fieldString="产品"
                    placeholder="搜索产品..."
                />
            </div>
        `;
    }
}
```

### 3. 带上下文的记录自动完成

```javascript
// 带上下文的记录自动完成
class ContextualRecordAutocomplete extends Component {
    setup() {
        this.state = useState({
            selectedUserId: null,
            companyId: 1,
            departmentId: null
        });
    }

    get searchContext() {
        return {
            default_company_id: this.state.companyId,
            default_department_id: this.state.departmentId,
            active_test: true
        };
    }

    get searchDomain() {
        const domain = [['active', '=', true]];
        
        if (this.state.companyId) {
            domain.push(['company_id', '=', this.state.companyId]);
        }
        
        if (this.state.departmentId) {
            domain.push(['department_id', '=', this.state.departmentId]);
        }
        
        return domain;
    }

    updateUser(userIds) {
        this.state.selectedUserId = userIds[0] || null;
        this.onUserChanged(this.state.selectedUserId);
    }

    onUserChanged(userId) {
        // 处理用户选择变化
        console.log('Selected user:', userId);
    }

    getCurrentUserIds() {
        return this.state.selectedUserId ? [this.state.selectedUserId] : [];
    }

    render() {
        return xml`
            <div class="contextual-autocomplete">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">公司</label>
                        <select class="form-select" t-model="state.companyId">
                            <option value="1">主公司</option>
                            <option value="2">分公司A</option>
                            <option value="3">分公司B</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">部门</label>
                        <select class="form-select" t-model="state.departmentId">
                            <option value="">所有部门</option>
                            <option value="1">销售部</option>
                            <option value="2">技术部</option>
                            <option value="3">财务部</option>
                        </select>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">选择用户</label>
                    <RecordAutocomplete
                        resModel="res.users"
                        update="updateUser"
                        multiSelect="false"
                        getIds="getCurrentUserIds"
                        value=""
                        domain="searchDomain"
                        context="searchContext"
                        fieldString="用户"
                        placeholder="搜索用户..."
                    />
                </div>
            </div>
        `;
    }
}
```

### 4. 高级记录搜索器

```javascript
// 高级记录搜索器
class AdvancedRecordSearcher extends Component {
    setup() {
        this.state = useState({
            selectedRecords: [],
            searchHistory: [],
            favorites: [],
            recentSearches: [],
            searchStats: {
                totalSearches: 0,
                successfulSelections: 0
            }
        });

        this.orm = useService('orm');
        this.notification = useService('notification');
    }

    async updateRecords(recordIds) {
        // 更新选中记录
        const newIds = recordIds.filter(id => 
            !this.state.selectedRecords.some(record => record.id === id)
        );
        
        if (newIds.length > 0) {
            try {
                // 获取新记录的详细信息
                const records = await this.orm.read(
                    this.props.resModel,
                    newIds,
                    ['id', 'name', 'display_name']
                );
                
                this.state.selectedRecords = [
                    ...this.state.selectedRecords,
                    ...records
                ];
                
                // 更新统计
                this.state.searchStats.successfulSelections += newIds.length;
                
                // 添加到最近选择
                this.addToRecentSearches(records);
                
            } catch (error) {
                this.notification.add(
                    '获取记录信息失败',
                    { type: 'danger' }
                );
            }
        }
    }

    addToRecentSearches(records) {
        const recentIds = records.map(r => r.id);
        this.state.recentSearches = [
            ...recentIds,
            ...this.state.recentSearches.filter(id => !recentIds.includes(id))
        ].slice(0, 20); // 保留最近20个
    }

    removeRecord(recordId) {
        this.state.selectedRecords = this.state.selectedRecords.filter(
            record => record.id !== recordId
        );
    }

    addToFavorites(recordId) {
        if (!this.state.favorites.includes(recordId)) {
            this.state.favorites = [...this.state.favorites, recordId];
            this.notification.add('已添加到收藏', { type: 'success' });
        }
    }

    getCurrentIds() {
        return this.state.selectedRecords.map(record => record.id);
    }

    onSearchStart() {
        this.state.searchStats.totalSearches++;
    }

    exportSelection() {
        const data = {
            records: this.state.selectedRecords,
            timestamp: new Date().toISOString(),
            model: this.props.resModel
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `records_${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    render() {
        return xml`
            <div class="advanced-record-searcher">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>高级记录搜索器</h5>
                    <div class="btn-group">
                        <button 
                            class="btn btn-sm btn-outline-info"
                            t-on-click="exportSelection"
                            t-att-disabled="!state.selectedRecords.length"
                        >
                            <i class="fa fa-download"/> 导出
                        </button>
                    </div>
                </div>

                <RecordAutocomplete
                    resModel="props.resModel"
                    update="updateRecords"
                    multiSelect="true"
                    getIds="getCurrentIds"
                    value=""
                    domain="props.domain"
                    context="props.context"
                    fieldString="props.fieldString"
                    placeholder="搜索记录..."
                    onSearchStart="onSearchStart"
                />

                <div class="selected-records mt-3" t-if="state.selectedRecords.length">
                    <h6>已选记录 (${state.selectedRecords.length})</h6>
                    <div class="list-group">
                        <t t-foreach="state.selectedRecords" t-as="record" t-key="record.id">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong t-esc="record.display_name || record.name"/>
                                    <small class="text-muted d-block">ID: ${record.id}</small>
                                </div>
                                <div class="btn-group">
                                    <button 
                                        class="btn btn-sm btn-outline-warning"
                                        t-on-click="() => this.addToFavorites(record.id)"
                                        title="添加到收藏"
                                    >
                                        <i class="fa fa-star"/>
                                    </button>
                                    <button 
                                        class="btn btn-sm btn-outline-danger"
                                        t-on-click="() => this.removeRecord(record.id)"
                                        title="移除"
                                    >
                                        <i class="fa fa-trash"/>
                                    </button>
                                </div>
                            </div>
                        </t>
                    </div>
                </div>

                <div class="search-stats mt-3">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 class="card-title">${state.searchStats.totalSearches}</h5>
                                    <p class="card-text">总搜索次数</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 class="card-title">${state.searchStats.successfulSelections}</h5>
                                    <p class="card-text">成功选择</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 class="card-title">${state.favorites.length}</h5>
                                    <p class="card-text">收藏记录</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 异步搜索
- 支持请求取消
- 智能的搜索限制
- 高效的结果处理

### 2. 名称缓存
- 统一的名称管理
- 性能优化机制
- 数据共享支持

### 3. 扩展搜索
- 大量结果处理
- 对话框集成
- 动态过滤支持

### 4. 域处理
- 智能的域合并
- 排除已选记录
- 灵活的过滤逻辑

## 设计模式

### 1. 组合模式 (Composition Pattern)
- 集成自动完成组件
- 统一的接口管理

### 2. 策略模式 (Strategy Pattern)
- 不同的搜索策略
- 灵活的配置选项

### 3. 观察者模式 (Observer Pattern)
- 搜索结果通知
- 选择事件处理

## 注意事项

1. **性能优化**: 合理设置搜索限制和缓存策略
2. **请求管理**: 正确取消之前的搜索请求
3. **错误处理**: 妥善处理搜索和选择错误
4. **用户体验**: 提供清晰的搜索反馈

## 扩展建议

1. **搜索历史**: 记录和管理搜索历史
2. **智能推荐**: 基于历史的智能推荐
3. **批量操作**: 支持批量选择和操作
4. **自定义过滤**: 支持用户自定义过滤条件
5. **离线支持**: 支持离线搜索和缓存

该记录自动完成组件为Odoo Web应用提供了强大的记录搜索和选择能力，通过异步搜索和智能缓存确保了优秀的性能和用户体验。

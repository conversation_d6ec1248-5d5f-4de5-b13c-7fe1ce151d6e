# ColorList - 颜色列表组件

## 概述

`colorlist.js` 是 Odoo Web 核心模块的颜色列表组件，专门用于提供颜色选择功能。该组件提供了一个预定义的颜色调色板，支持展开/收起状态管理、外部点击检测和颜色选择回调，广泛应用于标签颜色选择、状态颜色设置、主题颜色配置等场景。

## 文件信息
- **路径**: `/web/static/src/core/colorlist/colorlist.js`
- **行数**: 68
- **模块**: `@web/core/colorlist/colorlist`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'    // 翻译服务
'@odoo/owl'                     // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const ColorList = class ColorList extends Component {
    static COLORS = [
        _t("No color"),
        _t("Red"),
        _t("Orange"),
        _t("Yellow"),
        _t("<PERSON><PERSON>"),
        _t("<PERSON>"),
        _t("Almond"),
        _t("<PERSON>l"),
        _t("Blue"),
        _t("Raspberry"),
        _t("Green"),
        _t("Violet"),
    ];
    static template = "web.ColorList";
```

**组件特性**:
- **预定义颜色**: 提供12种预定义颜色选项
- **国际化支持**: 所有颜色名称支持多语言翻译
- **模板驱动**: 使用专门的颜色列表模板
- **静态配置**: 颜色列表作为静态属性定义

### 2. 属性配置

```javascript
static props = {
    canToggle: { type: Boolean, optional: true },
    colors: Array,
    forceExpanded: { type: Boolean, optional: true },
    isExpanded: { type: Boolean, optional: true },
    onColorSelected: Function,
    selectedColor: { type: Number, optional: true },
};

static defaultProps = {
    forceExpanded: false,
    isExpanded: false,
};
```

**属性功能**:
- **切换控制**: canToggle控制是否可以切换展开状态
- **颜色数组**: colors提供自定义颜色列表
- **强制展开**: forceExpanded强制保持展开状态
- **展开状态**: isExpanded控制初始展开状态
- **选择回调**: onColorSelected处理颜色选择事件
- **当前选择**: selectedColor标识当前选中的颜色

### 3. 组件初始化

```javascript
setup() {
    this.colorlistRef = useRef("colorlist");
    this.state = useState({ isExpanded: this.props.isExpanded });
    useExternalListener(window, "click", this.onOutsideClick);
}
```

**初始化功能**:
- **引用管理**: 创建颜色列表元素的引用
- **状态管理**: 使用useState管理展开状态
- **外部监听**: 监听窗口点击事件处理外部点击

### 4. 颜色获取

```javascript
get colors() {
    return this.constructor.COLORS;
}
```

**颜色获取功能**:
- **静态访问**: 通过getter访问静态颜色列表
- **可重写**: 子类可以重写此方法提供自定义颜色
- **简洁接口**: 提供简洁的颜色访问接口

### 5. 颜色选择处理

```javascript
onColorSelected(id) {
    this.props.onColorSelected(id);
    if (!this.props.forceExpanded) {
        this.state.isExpanded = false;
    }
}
```

**选择处理功能**:
- **回调触发**: 触发父组件的颜色选择回调
- **自动收起**: 选择后自动收起（除非强制展开）
- **状态更新**: 更新组件的展开状态

### 6. 外部点击处理

```javascript
onOutsideClick(ev) {
    if (this.colorlistRef.el.contains(ev.target) || this.props.forceExpanded) {
        return;
    }
    this.state.isExpanded = false;
}
```

**外部点击功能**:
- **范围检测**: 检测点击是否在组件内部
- **强制展开检查**: 考虑强制展开状态
- **自动收起**: 外部点击时自动收起组件

### 7. 切换处理

```javascript
onToggle(ev) {
    if (this.props.canToggle) {
        ev.preventDefault();
        ev.stopPropagation();
        this.state.isExpanded = !this.state.isExpanded;
        this.colorlistRef.el.firstElementChild.focus();
    }
}
```

**切换处理功能**:
- **权限检查**: 检查是否允许切换
- **事件阻止**: 阻止默认行为和事件冒泡
- **状态切换**: 切换展开/收起状态
- **焦点管理**: 展开时设置焦点到第一个元素

## 使用场景

### 1. 标签颜色选择

```javascript
// 在标签组件中使用颜色选择
<ColorList
    canToggle={true}
    onColorSelected={(colorId) => this.setTagColor(colorId)}
    selectedColor={this.state.currentColor}
    isExpanded={false}
/>
```

### 2. 状态颜色配置

```javascript
// 在状态配置中使用颜色选择
<ColorList
    forceExpanded={true}
    onColorSelected={(colorId) => this.updateStatusColor(colorId)}
    selectedColor={this.props.statusColor}
/>
```

### 3. 自定义颜色列表

```javascript
// 使用自定义颜色列表
const customColors = [
    _t("Default"),
    _t("Primary"),
    _t("Secondary"),
    _t("Success"),
    _t("Warning"),
    _t("Danger")
];

<ColorList
    colors={customColors}
    onColorSelected={(colorId) => this.applyThemeColor(colorId)}
/>
```

## 增强示例

```javascript
// 增强的颜色列表组件
const EnhancedColorList = {
    createAdvancedColorList: () => {
        class AdvancedColorList extends ColorList {
            static COLORS = [
                ...ColorList.COLORS,
                _t("Pink"),
                _t("Brown"),
                _t("Gray"),
                _t("Black"),
                _t("White")
            ];

            static props = {
                ...ColorList.props,
                enableCustomColor: { type: Boolean, optional: true },
                enableColorPreview: { type: Boolean, optional: true },
                enableColorGroups: { type: Boolean, optional: true },
                colorGroups: { type: Array, optional: true },
                enableSearch: { type: Boolean, optional: true },
                enableRecentColors: { type: Boolean, optional: true },
                maxRecentColors: { type: Number, optional: true },
                onCustomColorSelected: { type: Function, optional: true }
            };

            static defaultProps = {
                ...ColorList.defaultProps,
                enableCustomColor: false,
                enableColorPreview: false,
                enableColorGroups: false,
                colorGroups: [],
                enableSearch: false,
                enableRecentColors: false,
                maxRecentColors: 5
            };

            setup() {
                super.setup();
                
                // 增强的状态
                this.enhancedState = useState({
                    searchQuery: '',
                    recentColors: this.loadRecentColors(),
                    customColor: '#000000',
                    previewColor: null,
                    activeGroup: 0
                });

                // 颜色选择器引用
                if (this.props.enableCustomColor) {
                    this.colorPickerRef = useRef("colorPicker");
                }
            }

            // 增强的颜色获取
            get colors() {
                let colors = super.colors;

                // 应用搜索过滤
                if (this.props.enableSearch && this.enhancedState.searchQuery) {
                    const query = this.enhancedState.searchQuery.toLowerCase();
                    colors = colors.filter(color => 
                        color.toLowerCase().includes(query)
                    );
                }

                // 添加最近使用的颜色
                if (this.props.enableRecentColors) {
                    const recentColors = this.enhancedState.recentColors.map(id => 
                        this.constructor.COLORS[id]
                    ).filter(Boolean);
                    
                    if (recentColors.length > 0) {
                        colors = [...recentColors, ...colors.filter(c => !recentColors.includes(c))];
                    }
                }

                return colors;
            }

            // 获取颜色分组
            get colorGroups() {
                if (!this.props.enableColorGroups) {
                    return [{ name: _t('All Colors'), colors: this.colors }];
                }

                return this.props.colorGroups.length > 0 ? this.props.colorGroups : [
                    {
                        name: _t('Basic Colors'),
                        colors: this.colors.slice(0, 6)
                    },
                    {
                        name: _t('Extended Colors'),
                        colors: this.colors.slice(6)
                    }
                ];
            }

            // 增强的颜色选择处理
            onColorSelected(id) {
                // 记录到最近使用
                if (this.props.enableRecentColors) {
                    this.addToRecentColors(id);
                }

                // 调用父类方法
                super.onColorSelected(id);
            }

            // 自定义颜色选择
            onCustomColorSelected() {
                const customColor = this.enhancedState.customColor;
                
                if (this.props.onCustomColorSelected) {
                    this.props.onCustomColorSelected(customColor);
                }

                // 添加到最近使用
                if (this.props.enableRecentColors) {
                    this.addCustomColorToRecent(customColor);
                }

                if (!this.props.forceExpanded) {
                    this.state.isExpanded = false;
                }
            }

            // 颜色预览
            onColorHover(id) {
                if (this.props.enableColorPreview) {
                    this.enhancedState.previewColor = id;
                }
            }

            onColorLeave() {
                if (this.props.enableColorPreview) {
                    this.enhancedState.previewColor = null;
                }
            }

            // 搜索处理
            onSearchInput(event) {
                this.enhancedState.searchQuery = event.target.value;
            }

            // 分组切换
            onGroupChange(groupIndex) {
                this.enhancedState.activeGroup = groupIndex;
            }

            // 添加到最近使用
            addToRecentColors(colorId) {
                const recent = [...this.enhancedState.recentColors];
                const index = recent.indexOf(colorId);
                
                if (index > -1) {
                    recent.splice(index, 1);
                }
                
                recent.unshift(colorId);
                
                if (recent.length > this.props.maxRecentColors) {
                    recent.splice(this.props.maxRecentColors);
                }
                
                this.enhancedState.recentColors = recent;
                this.saveRecentColors();
            }

            // 添加自定义颜色到最近使用
            addCustomColorToRecent(color) {
                // 这里可以实现自定义颜色的最近使用逻辑
                console.log('Adding custom color to recent:', color);
            }

            // 加载最近使用的颜色
            loadRecentColors() {
                try {
                    const saved = localStorage.getItem('odoo_recent_colors');
                    return saved ? JSON.parse(saved) : [];
                } catch (error) {
                    console.warn('Failed to load recent colors:', error);
                    return [];
                }
            }

            // 保存最近使用的颜色
            saveRecentColors() {
                try {
                    localStorage.setItem('odoo_recent_colors', 
                        JSON.stringify(this.enhancedState.recentColors));
                } catch (error) {
                    console.warn('Failed to save recent colors:', error);
                }
            }

            // 获取颜色的CSS值
            getColorValue(colorId) {
                const colorMap = {
                    0: 'transparent',
                    1: '#ff0000',
                    2: '#ffa500',
                    3: '#ffff00',
                    4: '#00ffff',
                    5: '#800080',
                    6: '#f5deb3',
                    7: '#008080',
                    8: '#0000ff',
                    9: '#e30b5c',
                    10: '#008000',
                    11: '#8b00ff'
                };
                
                return colorMap[colorId] || 'transparent';
            }

            // 获取颜色的对比文本颜色
            getContrastTextColor(colorId) {
                const color = this.getColorValue(colorId);
                
                // 简单的对比度计算
                if (color === 'transparent') return '#000000';
                
                const hex = color.replace('#', '');
                const r = parseInt(hex.substr(0, 2), 16);
                const g = parseInt(hex.substr(2, 2), 16);
                const b = parseInt(hex.substr(4, 2), 16);
                
                // 计算亮度
                const brightness = (r * 299 + g * 587 + b * 114) / 1000;
                
                return brightness > 128 ? '#000000' : '#ffffff';
            }

            // 清除最近使用的颜色
            clearRecentColors() {
                this.enhancedState.recentColors = [];
                this.saveRecentColors();
            }

            // 重置搜索
            clearSearch() {
                this.enhancedState.searchQuery = '';
            }

            // 获取颜色统计
            getColorStats() {
                return {
                    totalColors: this.colors.length,
                    recentColors: this.enhancedState.recentColors.length,
                    searchResults: this.props.enableSearch ? 
                        this.colors.length : null
                };
            }
        }

        return AdvancedColorList;
    },

    // 颜色工具函数
    colorUtils: {
        // 十六进制转RGB
        hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        },

        // RGB转十六进制
        rgbToHex(r, g, b) {
            return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
        },

        // 计算颜色亮度
        getLuminance(hex) {
            const rgb = this.hexToRgb(hex);
            if (!rgb) return 0;

            const { r, g, b } = rgb;
            return (0.299 * r + 0.587 * g + 0.114 * b) / 255;
        },

        // 获取对比色
        getContrastColor(hex) {
            const luminance = this.getLuminance(hex);
            return luminance > 0.5 ? '#000000' : '#ffffff';
        },

        // 生成颜色变体
        generateColorVariants(baseColor, count = 5) {
            const variants = [];
            const rgb = this.hexToRgb(baseColor);
            
            if (!rgb) return variants;

            for (let i = 0; i < count; i++) {
                const factor = (i + 1) / (count + 1);
                const r = Math.round(rgb.r + (255 - rgb.r) * factor);
                const g = Math.round(rgb.g + (255 - rgb.g) * factor);
                const b = Math.round(rgb.b + (255 - rgb.b) * factor);
                
                variants.push(this.rgbToHex(r, g, b));
            }

            return variants;
        }
    }
};

// 使用示例
const AdvancedColorList = EnhancedColorList.createAdvancedColorList();

// 在组件中使用
<AdvancedColorList
    canToggle={true}
    enableCustomColor={true}
    enableColorPreview={true}
    enableSearch={true}
    enableRecentColors={true}
    maxRecentColors={8}
    onColorSelected={(colorId) => this.handleColorSelection(colorId)}
    onCustomColorSelected={(color) => this.handleCustomColor(color)}
/>
```

## 技术特点

### 1. 状态管理
- 响应式的展开状态管理
- 外部点击自动收起
- 可配置的强制展开模式

### 2. 事件处理
- 完善的事件处理机制
- 事件冒泡控制
- 焦点管理

### 3. 国际化支持
- 所有颜色名称支持翻译
- 多语言环境适配
- 本地化友好

### 4. 可配置性
- 灵活的属性配置
- 可重写的颜色列表
- 可选的功能开关

## 设计模式

### 1. 组件模式 (Component Pattern)
- 封装颜色选择逻辑
- 可重用的组件设计

### 2. 观察者模式 (Observer Pattern)
- 外部点击事件监听
- 状态变化响应

### 3. 策略模式 (Strategy Pattern)
- 可配置的行为模式
- 灵活的展开策略

## 注意事项

1. **内存管理**: 及时清理外部事件监听器
2. **性能优化**: 避免频繁的状态更新
3. **可访问性**: 支持键盘导航和屏幕阅读器
4. **用户体验**: 提供清晰的视觉反馈

## 扩展建议

1. **自定义颜色**: 支持用户自定义颜色
2. **颜色分组**: 支持颜色的分类显示
3. **搜索功能**: 添加颜色搜索功能
4. **最近使用**: 记录最近使用的颜色
5. **颜色预览**: 提供颜色预览功能

该颜色列表组件为Odoo Web应用提供了标准化的颜色选择功能，通过简洁的API和灵活的配置满足了各种颜色选择场景的需求。

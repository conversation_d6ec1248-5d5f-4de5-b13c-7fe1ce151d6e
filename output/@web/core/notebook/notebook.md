# Notebook - 笔记本组件

## 概述

`notebook.js` 是 Odoo Web 核心模块的笔记本组件，提供了标签页式的内容组织功能。该组件支持多页面切换、动态页面管理、锚点导航和自定义页面组件，具备水平和垂直布局、页面可见性控制、禁用状态管理等功能，为用户提供了清晰的信息分组和导航体验，广泛应用于表单视图、设置页面、详情展示等需要分页显示的场景。

## 文件信息
- **路径**: `/web/static/src/core/notebook/notebook.js`
- **行数**: 200
- **模块**: `@web/core/notebook/notebook`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/scrolling'          // 滚动工具函数
'@odoo/owl'                          // OWL框架
'@web/core/browser/browser'          // 浏览器工具
```

## 主组件类

### 1. 组件属性定义

```javascript
static props = {
    slots: { type: Object, optional: true },        // 插槽页面
    pages: { type: Object, optional: true },        // 页面配置
    class: { optional: true },                      // CSS类
    className: { type: String, optional: true },    // 额外CSS类名
    anchors: { type: Object, optional: true },      // 锚点配置
    defaultPage: { type: String, optional: true },  // 默认页面
    orientation: { type: String, optional: true },  // 布局方向
    icons: { type: Object, optional: true },        // 图标配置
    onPageUpdate: { type: Function, optional: true }, // 页面更新回调
};

static defaultProps = {
    className: "",
    orientation: "horizontal",
    onPageUpdate: () => {},
};
```

**属性功能**:
- **页面定义**: 支持slots和pages两种页面定义方式
- **布局控制**: orientation控制水平或垂直布局
- **锚点导航**: anchors支持页面内锚点跳转
- **回调机制**: onPageUpdate提供页面切换回调
- **样式定制**: 支持自定义CSS类和图标

### 2. 组件初始化

```javascript
setup() {
    this.activePane = useRef("activePane");
    this.anchorTarget = null;
    this.pages = this.computePages(this.props);
    this.state = useState({ currentPage: null });
    this.state.currentPage = this.computeActivePage(this.props.defaultPage, true);
    
    useExternalListener(browser, "click", this.onAnchorClicked);
    
    useEffect(() => {
        this.props.onPageUpdate(this.state.currentPage);
        if (this.anchorTarget) {
            const matchingEl = this.activePane.el.querySelector(`#${this.anchorTarget}`);
            scrollTo(matchingEl, { isAnchor: true });
            this.anchorTarget = null;
        }
        this.activePane.el?.classList.add("show");
    }, () => [this.state.currentPage]);
    
    onWillUpdateProps((nextProps) => {
        const activateDefault = this.props.defaultPage !== nextProps.defaultPage || !this.defaultVisible;
        this.pages = this.computePages(nextProps);
        this.state.currentPage = this.computeActivePage(nextProps.defaultPage, activateDefault);
    });
}
```

**初始化功能**:
- **引用管理**: 获取活动面板的引用
- **页面计算**: 计算和管理页面列表
- **状态管理**: 管理当前活动页面状态
- **锚点监听**: 监听全局点击事件处理锚点导航
- **效果处理**: 页面切换时的滚动和动画效果
- **属性更新**: 响应属性变化更新页面状态

## 核心方法

### 1. 页面计算

```javascript
computePages(props) {
    if (!props.slots && !props.pages) {
        return [];
    }
    
    if (props.pages) {
        for (const page of props.pages) {
            page.isVisible = true;
        }
    }
    
    this.disabledPages = [];
    const pages = [];
    const pagesWithIndex = [];
    
    for (const [k, v] of Object.entries({ ...props.slots, ...props.pages })) {
        const id = v.id || k;
        if (v.index) {
            pagesWithIndex.push([id, v]);
        } else {
            pages.push([id, v]);
        }
        if (v.isDisabled) {
            this.disabledPages.push(k);
        }
    }
    
    for (const page of pagesWithIndex) {
        pages.splice(page[1].index, 0, page);
    }
    
    return pages;
}

computeActivePage(defaultPage, activateDefault) {
    if (!this.pages.length) {
        return null;
    }
    
    const pages = this.pages.filter((e) => e[1].isVisible).map((e) => e[0]);

    if (defaultPage) {
        if (!pages.includes(defaultPage)) {
            this.defaultVisible = false;
        } else {
            this.defaultVisible = true;
            if (activateDefault) {
                return defaultPage;
            }
        }
    }
    
    const current = this.state.currentPage;
    if (!current || (current && !pages.includes(current))) {
        return pages[0];
    }

    return current;
}
```

**页面计算功能**:
- **页面合并**: 合并slots和pages配置
- **索引排序**: 支持自定义页面索引排序
- **禁用管理**: 管理禁用页面列表
- **可见性**: 处理页面可见性逻辑
- **默认页面**: 智能选择默认活动页面
- **状态保持**: 保持当前页面状态的有效性

### 2. 页面导航

```javascript
activatePage(pageIndex) {
    if (!this.disabledPages.includes(pageIndex) && this.state.currentPage !== pageIndex) {
        this.activePane.el?.classList.remove("show");
        this.state.currentPage = pageIndex;
    }
}

get navItems() {
    return this.pages.filter((e) => e[1].isVisible);
}

get page() {
    const page = this.pages.find((e) => e[0] === this.state.currentPage)[1];
    return page.Component && page;
}
```

**导航功能**:
- **页面激活**: 激活指定页面并处理动画
- **导航项**: 获取可见的导航项列表
- **当前页面**: 获取当前活动页面的组件信息
- **禁用检查**: 检查页面是否被禁用

### 3. 锚点导航

```javascript
onAnchorClicked(ev) {
    if (!this.props.anchors) {
        return;
    }
    
    const href = ev.target.closest("a")?.getAttribute("href");
    if (!href) {
        return;
    }
    
    const id = href.substring(1);
    if (this.props.anchors[id]) {
        if (this.state.currentPage !== this.props.anchors[id].target) {
            ev.preventDefault();
            this.anchorTarget = id;
            this.state.currentPage = this.props.anchors[id].target;
        }
    }
}
```

**锚点功能**:
- **链接监听**: 监听页面中的锚点链接点击
- **页面跳转**: 根据锚点配置跳转到目标页面
- **滚动定位**: 跳转后滚动到目标元素位置
- **事件阻止**: 阻止默认的锚点跳转行为

## 使用场景

### 1. 基础笔记本使用

```javascript
// 基础笔记本使用
class BasicNotebook extends Component {
    setup() {
        this.state = useState({
            currentPage: 'general',
            formData: {
                name: '',
                email: '',
                phone: '',
                address: '',
                notes: ''
            }
        });
    }

    onPageUpdate(pageId) {
        console.log('Current page:', pageId);
        this.state.currentPage = pageId;
    }

    render() {
        return xml`
            <div class="basic-notebook">
                <h5>用户信息编辑</h5>
                
                <Notebook 
                    defaultPage="'general'"
                    onPageUpdate="onPageUpdate"
                    className="mt-3"
                >
                    <t t-set-slot="general" title="基本信息" isVisible="true">
                        <div class="general-info p-3">
                            <h6>基本信息</h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">姓名</label>
                                    <input 
                                        type="text" 
                                        class="form-control"
                                        t-model="state.formData.name"
                                    />
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">邮箱</label>
                                    <input 
                                        type="email" 
                                        class="form-control"
                                        t-model="state.formData.email"
                                    />
                                </div>
                            </div>
                        </div>
                    </t>
                    
                    <t t-set-slot="contact" title="联系方式" isVisible="true">
                        <div class="contact-info p-3">
                            <h6>联系方式</h6>
                            <div class="mb-3">
                                <label class="form-label">电话</label>
                                <input 
                                    type="tel" 
                                    class="form-control"
                                    t-model="state.formData.phone"
                                />
                            </div>
                            <div class="mb-3">
                                <label class="form-label">地址</label>
                                <textarea 
                                    class="form-control"
                                    rows="3"
                                    t-model="state.formData.address"
                                />
                            </div>
                        </div>
                    </t>
                    
                    <t t-set-slot="notes" title="备注" isVisible="true">
                        <div class="notes-info p-3">
                            <h6>备注信息</h6>
                            <textarea 
                                class="form-control"
                                rows="5"
                                placeholder="请输入备注信息..."
                                t-model="state.formData.notes"
                            />
                        </div>
                    </t>
                </Notebook>

                <div class="form-actions mt-3">
                    <button class="btn btn-primary me-2">保存</button>
                    <button class="btn btn-secondary">取消</button>
                </div>
            </div>
        `;
    }
}
```

### 2. 动态页面笔记本

```javascript
// 动态页面笔记本
class DynamicNotebook extends Component {
    setup() {
        this.state = useState({
            pages: [
                {
                    Component: this.createPageComponent('概览', '这是概览页面的内容'),
                    id: 'overview',
                    title: '概览',
                    isVisible: true,
                    index: 0
                },
                {
                    Component: this.createPageComponent('详情', '这是详情页面的内容'),
                    id: 'details',
                    title: '详情',
                    isVisible: true,
                    index: 1
                },
                {
                    Component: this.createPageComponent('设置', '这是设置页面的内容'),
                    id: 'settings',
                    title: '设置',
                    isVisible: true,
                    index: 2
                }
            ],
            currentPage: 'overview',
            newPageTitle: '',
            newPageContent: ''
        });
    }

    createPageComponent(title, content) {
        return class extends Component {
            static template = xml`
                <div class="dynamic-page p-3">
                    <h6 t-esc="props.title"/>
                    <p t-esc="props.content"/>
                    <div class="page-actions mt-3">
                        <button 
                            class="btn btn-sm btn-outline-danger"
                            t-on-click="props.onRemove"
                        >
                            删除此页面
                        </button>
                    </div>
                </div>
            `;
            static props = ['title', 'content', 'onRemove'];
        };
    }

    addPage() {
        if (!this.state.newPageTitle) {
            this.notification.add('请输入页面标题', { type: 'warning' });
            return;
        }

        const newId = `page_${Date.now()}`;
        const newPage = {
            Component: this.createPageComponent(this.state.newPageTitle, this.state.newPageContent),
            id: newId,
            title: this.state.newPageTitle,
            isVisible: true,
            index: this.state.pages.length,
            props: {
                title: this.state.newPageTitle,
                content: this.state.newPageContent || '暂无内容',
                onRemove: () => this.removePage(newId)
            }
        };

        this.state.pages.push(newPage);
        this.state.newPageTitle = '';
        this.state.newPageContent = '';
        this.state.currentPage = newId;
    }

    removePage(pageId) {
        if (this.state.pages.length <= 1) {
            this.notification.add('至少需要保留一个页面', { type: 'warning' });
            return;
        }

        if (!confirm('确定要删除这个页面吗？')) {
            return;
        }

        this.state.pages = this.state.pages.filter(page => page.id !== pageId);
        
        // 如果删除的是当前页面，切换到第一个页面
        if (this.state.currentPage === pageId) {
            this.state.currentPage = this.state.pages[0]?.id;
        }
    }

    onPageUpdate(pageId) {
        this.state.currentPage = pageId;
    }

    render() {
        return xml`
            <div class="dynamic-notebook">
                <div class="header mb-3">
                    <h5>动态页面笔记本</h5>
                    <p class="text-muted">当前页面: ${this.state.currentPage}</p>
                </div>

                <div class="page-manager mb-3">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">添加新页面</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-2">
                                    <input 
                                        type="text" 
                                        class="form-control"
                                        placeholder="页面标题"
                                        t-model="state.newPageTitle"
                                    />
                                </div>
                                <div class="col-md-6 mb-2">
                                    <input 
                                        type="text" 
                                        class="form-control"
                                        placeholder="页面内容"
                                        t-model="state.newPageContent"
                                    />
                                </div>
                                <div class="col-md-2 mb-2">
                                    <button 
                                        class="btn btn-primary w-100"
                                        t-on-click="addPage"
                                    >
                                        添加
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <Notebook 
                    pages="state.pages"
                    defaultPage="state.currentPage"
                    onPageUpdate="onPageUpdate"
                    className="dynamic-pages"
                />

                <div class="page-info mt-3">
                    <div class="card">
                        <div class="card-body">
                            <h6>页面统计</h6>
                            <p class="mb-0">
                                总页面数: <strong>${this.state.pages.length}</strong> |
                                当前页面: <strong>${this.state.currentPage}</strong>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

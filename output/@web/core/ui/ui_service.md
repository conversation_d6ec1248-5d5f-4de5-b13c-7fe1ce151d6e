# Odoo UI服务 (UI Service) 学习资料

## 文件概述

**文件路径**: `output/@web/core/ui/ui_service.js`
**原始路径**: `/web/static/src/core/ui/ui_service.js`
**模块类型**: 核心基础模块 - UI服务
**代码行数**: 242 行
**依赖关系**:
- `@web/core/utils/hooks` - 通用 Hook
- `@web/core/registry` - 服务注册表
- `@web/core/utils/timing` - 时间工具
- `@web/core/ui/block_ui` - 阻塞UI组件
- `@web/core/browser/browser` - 浏览器抽象层
- `@web/core/utils/ui` - UI工具
- `@web/core/hotkeys/hotkey_service` - 热键服务
- `@odoo/owl` - OWL 框架

## 模块功能

UI服务是 Odoo Web 客户端用户界面的核心服务，负责：
- 管理全局UI状态和响应式设计
- 提供屏幕尺寸检测和断点管理
- 处理UI阻塞和解除阻塞操作
- 管理活动元素和焦点控制
- 提供焦点陷阱和键盘导航支持
- 集成事件总线进行UI状态通信

## 核心常量和配置

### 1. 屏幕尺寸枚举
```javascript
const SIZES = { XS: 0, VSM: 1, SM: 2, MD: 3, LG: 4, XL: 5, XXL: 6 };
```

**尺寸级别**:
- **XS (0)**: 超小屏幕 (≤474px)
- **VSM (1)**: 很小屏幕 (475-575px)
- **SM (2)**: 小屏幕 (576-767px)
- **MD (3)**: 中等屏幕 (768-991px)
- **LG (4)**: 大屏幕 (992-1199px)
- **XL (5)**: 超大屏幕 (1200-1533px)
- **XXL (6)**: 超超大屏幕 (≥1534px)

### 2. 媒体查询断点
```javascript
const MEDIAS_BREAKPOINTS = [
    { maxWidth: 474 },                    // XS
    { minWidth: 475, maxWidth: 575 },     // VSM
    { minWidth: 576, maxWidth: 767 },     // SM
    { minWidth: 768, maxWidth: 991 },     // MD
    { minWidth: 992, maxWidth: 1199 },    // LG
    { minWidth: 1200, maxWidth: 1533 },   // XL
    { minWidth: 1534 },                   // XXL
];
```

**断点设计**:
- **移动优先**: 从小屏幕开始设计
- **Bootstrap兼容**: 与Bootstrap断点基本一致
- **精确控制**: 每个断点都有明确的像素范围

## 核心工具函数

### 1. getFirstAndLastTabableElements()
```javascript
function getFirstAndLastTabableElements(el) {
    const tabableEls = getTabableElements(el);
    return [tabableEls[0], tabableEls[tabableEls.length - 1]];
}
```

**功能**:
- **获取可聚焦元素**: 找到容器内所有可通过Tab键聚焦的元素
- **返回首尾**: 返回第一个和最后一个可聚焦元素
- **焦点陷阱**: 为焦点陷阱功能提供支持

### 2. getMediaQueryLists()
```javascript
function getMediaQueryLists() {
    return MEDIAS_BREAKPOINTS.map(({ minWidth, maxWidth }) => {
        if (!maxWidth) {
            return window.matchMedia(`(min-width: ${minWidth}px)`);
        }
        if (!minWidth) {
            return window.matchMedia(`(max-width: ${maxWidth}px)`);
        }
        return window.matchMedia(`(min-width: ${minWidth}px) and (max-width: ${maxWidth}px)`);
    });
}
```

**媒体查询生成**:
- **动态创建**: 根据断点配置动态创建MediaQueryList
- **范围查询**: 支持最小宽度、最大宽度和范围查询
- **实时监听**: 返回可监听变化的MediaQueryList对象

### 3. utils 工具对象
```javascript
const utils = {
    getSize() {
        return MEDIAS.findIndex((media) => media.matches);
    },
    isSmall(ui = {}) {
        return (ui.size || utils.getSize()) <= SIZES.SM;
    },
};
```

**工具方法**:
- **getSize()**: 获取当前屏幕尺寸级别
- **isSmall()**: 判断是否为小屏幕设备

## useActiveElement Hook 详解

### 1. Hook 定义
```javascript
function useActiveElement(refName) {
    if (!refName) {
        throw new Error("refName not given to useActiveElement");
    }
    const uiService = useService("ui");
    const ref = useRef(refName);
    // ... 实现逻辑
}
```

**功能**:
- **活动元素管理**: 设置和管理UI活动元素
- **焦点控制**: 自动聚焦到第一个可聚焦元素
- **焦点陷阱**: 实现键盘导航的焦点陷阱
- **生命周期**: 自动处理元素激活和去激活

### 2. 焦点陷阱实现
```javascript
function trapFocus(e) {
    const hotkey = getActiveHotkey(e);
    if (!["tab", "shift+tab"].includes(hotkey)) {
        return;
    }
    const el = e.currentTarget;
    const [firstTabableEl, lastTabableEl] = getFirstAndLastTabableElements(el);
    switch (hotkey) {
        case "tab":
            if (document.activeElement === lastTabableEl) {
                firstTabableEl.focus();
                e.preventDefault();
                e.stopPropagation();
            }
            break;
        case "shift+tab":
            if (document.activeElement === firstTabableEl) {
                lastTabableEl.focus();
                e.preventDefault();
                e.stopPropagation();
            }
            break;
    }
}
```

**陷阱机制**:
- **Tab键处理**: 在最后一个元素时跳转到第一个
- **Shift+Tab处理**: 在第一个元素时跳转到最后一个
- **事件阻止**: 阻止默认行为和事件冒泡
- **循环导航**: 实现容器内的循环键盘导航

### 3. 生命周期管理
```javascript
useEffect(
    (el) => {
        if (el) {
            const [firstTabableEl] = getFirstAndLastTabableElements(el);
            if (!firstTabableEl) {
                return; // 无可聚焦元素
            }
            const oldActiveElement = document.activeElement;
            uiService.activateElement(el);

            el.addEventListener("keydown", trapFocus);

            if (!el.contains(document.activeElement)) {
                firstTabableEl.focus();
            }
            return () => {
                uiService.deactivateElement(el);
                el.removeEventListener("keydown", trapFocus);

                if (
                    el.contains(document.activeElement) ||
                    document.activeElement === document.body
                ) {
                    oldActiveElement.focus();
                }
            };
        }
    },
    () => [ref.el]
);
```

**生命周期步骤**:
1. **元素检查**: 验证元素存在和可聚焦性
2. **状态保存**: 保存当前活动元素
3. **元素激活**: 注册为UI活动元素
4. **事件绑定**: 添加焦点陷阱事件监听
5. **初始聚焦**: 聚焦到第一个可聚焦元素
6. **清理函数**: 返回清理函数处理组件卸载

## UI服务架构分析

### 1. 服务定义
```javascript
const uiService = {
    start(env) {
        // 服务启动逻辑
        return ui; // 返回响应式UI对象
    }
};
```

**设计特点**:
- **工厂模式**: start方法返回服务实例
- **环境集成**: 与OWL环境系统集成
- **响应式**: 返回响应式的UI状态对象

### 2. 阻塞UI管理
```javascript
let blockCount = 0;
function block(data) {
    blockCount++;
    if (blockCount === 1) {
        bus.trigger("BLOCK", {
            message: data?.message,
            delay: data?.delay,
        });
    }
}
function unblock() {
    blockCount--;
    if (blockCount < 0) {
        console.warn("Unblock ui was called more times than block...");
        blockCount = 0;
    }
    if (blockCount === 0) {
        bus.trigger("UNBLOCK");
    }
}
```

**阻塞机制**:
- **引用计数**: 使用计数器管理多重阻塞
- **防重复**: 只在第一次阻塞时触发事件
- **错误处理**: 检测和警告不匹配的解除阻塞调用
- **事件驱动**: 通过事件总线与BlockUI组件通信

### 3. 活动元素管理
```javascript
let activeElems = [document];

function activateElement(el) {
    activeElems.push(el);
    bus.trigger("active-element-changed", el);
}

function deactivateElement(el) {
    activeElems = activeElems.filter((x) => x !== el);
    bus.trigger("active-element-changed", ui.activeElement);
}

function getActiveElementOf(el) {
    for (const activeElement of [...activeElems].reverse()) {
        if (activeElement.contains(el)) {
            return activeElement;
        }
    }
}
```

**活动元素系统**:
- **栈结构**: 使用数组维护活动元素栈
- **层级管理**: 后激活的元素具有更高优先级
- **包含检测**: 通过contains方法检测元素关系
- **事件通知**: 活动元素变化时触发事件

### 4. 响应式UI对象
```javascript
const ui = reactive({
    bus,
    size: utils.getSize(),
    get activeElement() {
        return activeElems[activeElems.length - 1];
    },
    get isBlocked() {
        return blockCount > 0;
    },
    isSmall: utils.isSmall(),
    block,
    unblock,
    activateElement,
    deactivateElement,
    getActiveElementOf,
});
```

**响应式属性**:
- **bus**: 事件总线实例
- **size**: 当前屏幕尺寸级别
- **activeElement**: 当前活动元素（计算属性）
- **isBlocked**: 是否处于阻塞状态（计算属性）
- **isSmall**: 是否为小屏幕设备
- **方法**: 各种UI操作方法

### 5. 尺寸变化监听
```javascript
const updateSize = () => {
    const prevSize = ui.size;
    ui.size = utils.getSize();
    if (ui.size !== prevSize) {
        ui.isSmall = utils.isSmall(ui);
        bus.trigger("resize");
    }
};
browser.addEventListener("resize", throttleForAnimation(updateSize));
```

**尺寸监听机制**:
- **变化检测**: 只在尺寸真正改变时更新
- **节流处理**: 使用动画帧节流避免频繁更新
- **状态同步**: 同时更新size和isSmall属性
- **事件通知**: 触发resize事件通知其他组件

### 6. 环境集成
```javascript
Object.defineProperty(env, "isSmall", {
    get() {
        return ui.isSmall;
    },
});
```

**环境属性**:
- **全局访问**: 在OWL环境中提供isSmall属性
- **响应式**: 通过getter保持与UI状态同步
- **便捷访问**: 组件可直接通过env.isSmall访问

## 实际使用示例

### 1. 基本UI服务使用
```javascript
import { useService } from "@web/core/utils/hooks";

class ResponsiveComponent extends Component {
    setup() {
        this.ui = useService("ui");

        // 监听尺寸变化
        this.ui.bus.addEventListener("resize", () => {
            this.render();
        });
    }

    get layoutClass() {
        if (this.ui.isSmall) {
            return "mobile-layout";
        } else {
            return "desktop-layout";
        }
    }

    render() {
        return xml`
            <div t-att-class="layoutClass">
                <t t-if="ui.isSmall">
                    <!-- 移动端布局 -->
                    <div class="mobile-content">移动端内容</div>
                </t>
                <t t-else="">
                    <!-- 桌面端布局 -->
                    <div class="desktop-content">桌面端内容</div>
                </t>
            </div>
        `;
    }
}
```

### 2. 阻塞UI使用
```javascript
class BlockingOperationExample extends Component {
    setup() {
        this.ui = useService("ui");
    }

    async performOperation() {
        // 阻塞UI
        this.ui.block({
            message: "正在处理数据...",
            delay: 500
        });

        try {
            await this.longRunningOperation();
        } finally {
            // 确保解除阻塞
            this.ui.unblock();
        }
    }

    async concurrentOperations() {
        // 多个并发操作，UI只阻塞一次
        const operations = [
            this.operation1(),
            this.operation2(),
            this.operation3()
        ];

        // 每个操作都调用block/unblock
        operations.forEach(async (op) => {
            this.ui.block();
            try {
                await op;
            } finally {
                this.ui.unblock();
            }
        });

        await Promise.all(operations);
        // UI会在所有操作完成后自动解除阻塞
    }
}
```

### 3. 活动元素和焦点管理
```javascript
import { useActiveElement } from "@web/core/ui/ui_service";

class ModalComponent extends Component {
    setup() {
        this.ui = useService("ui");

        // 使用活动元素Hook
        useActiveElement("modal");

        // 监听活动元素变化
        this.ui.bus.addEventListener("active-element-changed", (activeEl) => {
            console.log("活动元素变化:", activeEl);
        });
    }

    onKeydown(event) {
        if (event.key === "Escape") {
            this.close();
        }
    }

    render() {
        return xml`
            <div t-ref="modal"
                 class="modal"
                 tabindex="-1"
                 t-on-keydown="onKeydown">
                <div class="modal-content">
                    <input type="text" placeholder="第一个输入框"/>
                    <input type="text" placeholder="第二个输入框"/>
                    <button t-on-click="close">关闭</button>
                </div>
            </div>
        `;
    }
}
```

### 4. 响应式设计实现
```javascript
class ResponsiveLayoutManager extends Component {
    setup() {
        this.ui = useService("ui");
        this.state = useState({
            sidebarVisible: !this.ui.isSmall
        });

        // 监听尺寸变化
        this.ui.bus.addEventListener("resize", () => {
            this.handleResize();
        });
    }

    handleResize() {
        // 小屏幕时自动隐藏侧边栏
        if (this.ui.isSmall && this.state.sidebarVisible) {
            this.state.sidebarVisible = false;
        }
    }

    toggleSidebar() {
        this.state.sidebarVisible = !this.state.sidebarVisible;
    }

    get layoutClasses() {
        const classes = ["main-layout"];

        // 根据屏幕尺寸添加类
        if (this.ui.size <= SIZES.SM) {
            classes.push("layout-mobile");
        } else if (this.ui.size <= SIZES.MD) {
            classes.push("layout-tablet");
        } else {
            classes.push("layout-desktop");
        }

        // 侧边栏状态
        if (this.state.sidebarVisible) {
            classes.push("sidebar-visible");
        }

        return classes.join(" ");
    }

    render() {
        return xml`
            <div t-att-class="layoutClasses">
                <header class="app-header">
                    <button t-if="ui.isSmall"
                            t-on-click="toggleSidebar"
                            class="sidebar-toggle">
                        ☰
                    </button>
                    <h1>应用标题</h1>
                </header>

                <aside t-if="state.sidebarVisible" class="app-sidebar">
                    侧边栏内容
                </aside>

                <main class="app-content">
                    主要内容
                </main>
            </div>
        `;
    }
}
```

### 5. 自定义断点检测
```javascript
class CustomBreakpointManager extends Component {
    setup() {
        this.ui = useService("ui");
        this.customBreakpoints = {
            isMobile: () => this.ui.size <= SIZES.SM,
            isTablet: () => this.ui.size >= SIZES.MD && this.ui.size <= SIZES.LG,
            isDesktop: () => this.ui.size >= SIZES.XL,
            isLargeScreen: () => this.ui.size >= SIZES.XXL
        };

        this.state = useState({
            currentBreakpoint: this.getCurrentBreakpoint()
        });

        this.ui.bus.addEventListener("resize", () => {
            this.state.currentBreakpoint = this.getCurrentBreakpoint();
        });
    }

    getCurrentBreakpoint() {
        for (const [name, check] of Object.entries(this.customBreakpoints)) {
            if (check()) {
                return name;
            }
        }
        return "unknown";
    }

    render() {
        return xml`
            <div class="breakpoint-info">
                <p>当前断点: <t t-esc="state.currentBreakpoint"/></p>
                <p>屏幕尺寸: <t t-esc="ui.size"/></p>
                <p>是否小屏幕: <t t-esc="ui.isSmall ? '是' : '否'"/></p>
            </div>
        `;
    }
}
```

### 6. UI状态管理器
```javascript
class UIStateManager {
    constructor(uiService) {
        this.ui = uiService;
        this.subscribers = new Map();
        this.state = reactive({
            theme: "light",
            language: "zh-CN",
            sidebarCollapsed: false,
            notifications: []
        });

        // 监听UI变化
        this.ui.bus.addEventListener("resize", () => {
            this.handleResize();
        });
    }

    subscribe(key, callback) {
        if (!this.subscribers.has(key)) {
            this.subscribers.set(key, []);
        }
        this.subscribers.get(key).push(callback);

        // 返回取消订阅函数
        return () => {
            const callbacks = this.subscribers.get(key);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        };
    }

    notify(key, data) {
        const callbacks = this.subscribers.get(key) || [];
        callbacks.forEach(callback => callback(data));
    }

    handleResize() {
        // 小屏幕时自动折叠侧边栏
        if (this.ui.isSmall && !this.state.sidebarCollapsed) {
            this.setSidebarCollapsed(true);
        }
    }

    setTheme(theme) {
        this.state.theme = theme;
        this.notify("theme-changed", theme);
    }

    setSidebarCollapsed(collapsed) {
        this.state.sidebarCollapsed = collapsed;
        this.notify("sidebar-changed", collapsed);
    }

    addNotification(notification) {
        this.state.notifications.push(notification);
        this.notify("notification-added", notification);
    }

    removeNotification(id) {
        const index = this.state.notifications.findIndex(n => n.id === id);
        if (index > -1) {
            const notification = this.state.notifications.splice(index, 1)[0];
            this.notify("notification-removed", notification);
        }
    }
}
```

## 设计模式分析

### 1. 服务模式 (Service Pattern)
```javascript
const uiService = {
    start(env) {
        return ui; // 返回服务实例
    }
};
```

**优势**:
- **全局访问**: 整个应用可以访问UI状态
- **单例**: 确保UI状态的一致性
- **依赖注入**: 通过服务系统注入

### 2. 观察者模式 (Observer Pattern)
```javascript
this.ui.bus.addEventListener("resize", () => {
    this.handleResize();
});
```

**实现**:
- **事件总线**: 通过EventBus实现发布-订阅
- **解耦**: UI状态变化与组件响应解耦
- **多订阅**: 支持多个组件订阅同一事件

### 3. 响应式模式 (Reactive Pattern)
```javascript
const ui = reactive({
    size: utils.getSize(),
    get activeElement() {
        return activeElems[activeElems.length - 1];
    },
    // ...
});
```

**特点**:
- **自动更新**: 状态变化自动触发重渲染
- **计算属性**: 支持基于其他状态的计算属性
- **依赖追踪**: 自动追踪状态依赖关系

### 4. 策略模式 (Strategy Pattern)
```javascript
const utils = {
    getSize() {
        return MEDIAS.findIndex((media) => media.matches);
    },
    isSmall(ui = {}) {
        return (ui.size || utils.getSize()) <= SIZES.SM;
    },
};
```

**应用**:
- **尺寸检测策略**: 不同的尺寸检测方法
- **断点策略**: 不同的断点判断逻辑
- **布局策略**: 不同屏幕尺寸的布局策略

### 5. 代理模式 (Proxy Pattern)
```javascript
Object.defineProperty(env, "isSmall", {
    get() {
        return ui.isSmall;
    },
});
```

**作用**:
- **属性代理**: 将UI属性代理到环境对象
- **透明访问**: 组件可以透明地访问UI状态
- **同步更新**: 保持环境属性与UI状态同步

## 性能优化

### 1. 节流处理
```javascript
browser.addEventListener("resize", throttleForAnimation(updateSize));
```

**优化效果**:
- **减少调用**: 使用动画帧节流减少resize事件处理
- **性能提升**: 避免频繁的DOM查询和状态更新
- **流畅体验**: 保持动画的流畅性

### 2. 媒体查询缓存
```javascript
const MEDIAS = getMediaQueryLists();
```

**缓存机制**:
- **一次创建**: 启动时创建所有MediaQueryList
- **重复使用**: 避免重复创建媒体查询对象
- **内存效率**: 减少对象创建开销

### 3. 计算属性优化
```javascript
get activeElement() {
    return activeElems[activeElems.length - 1];
}
```

**优化特点**:
- **懒计算**: 只在访问时计算
- **缓存结果**: OWL自动缓存计算结果
- **依赖追踪**: 只在依赖变化时重新计算

### 4. 事件优化
```javascript
if (ui.size !== prevSize) {
    ui.isSmall = utils.isSmall(ui);
    bus.trigger("resize");
}
```

**事件优化**:
- **变化检测**: 只在真正变化时触发事件
- **批量更新**: 一次性更新多个相关状态
- **减少通知**: 避免不必要的事件通知

## 最佳实践

### 1. 响应式设计
```javascript
// ✅ 推荐：使用UI服务进行响应式设计
class ResponsiveComponent extends Component {
    setup() {
        this.ui = useService("ui");
    }

    get layoutClass() {
        return this.ui.isSmall ? "mobile-layout" : "desktop-layout";
    }
}

// ❌ 避免：手动检测屏幕尺寸
class BadResponsiveComponent extends Component {
    get isMobile() {
        return window.innerWidth < 768; // 硬编码断点
    }
}
```

### 2. 阻塞UI管理
```javascript
// ✅ 推荐：正确的阻塞UI使用
async performOperation() {
    this.ui.block();
    try {
        await this.operation();
    } finally {
        this.ui.unblock(); // 确保解除阻塞
    }
}

// ❌ 避免：忘记解除阻塞
async badOperation() {
    this.ui.block();
    await this.operation(); // 如果出错，UI永远阻塞
    this.ui.unblock();
}
```

### 3. 活动元素使用
```javascript
// ✅ 推荐：使用useActiveElement Hook
class ModalComponent extends Component {
    setup() {
        useActiveElement("modal"); // 自动管理焦点
    }
}

// ❌ 避免：手动管理焦点
class BadModalComponent extends Component {
    setup() {
        // 手动实现焦点管理，容易出错
    }
}
```

### 4. 事件监听
```javascript
// ✅ 推荐：正确的事件监听和清理
setup() {
    this.ui = useService("ui");

    const handleResize = () => this.handleResize();
    this.ui.bus.addEventListener("resize", handleResize);

    onWillUnmount(() => {
        this.ui.bus.removeEventListener("resize", handleResize);
    });
}

// ❌ 避免：忘记清理事件监听器
setup() {
    this.ui.bus.addEventListener("resize", () => {
        // 匿名函数无法清理
    });
}
```

## 扩展和自定义

### 1. 自定义断点系统
```javascript
class CustomBreakpointService {
    constructor(uiService) {
        this.ui = uiService;
        this.customBreakpoints = new Map();
        this.state = reactive({
            activeBreakpoints: new Set()
        });

        this.ui.bus.addEventListener("resize", () => {
            this.updateActiveBreakpoints();
        });
    }

    addBreakpoint(name, condition) {
        this.customBreakpoints.set(name, condition);
        this.updateActiveBreakpoints();
    }

    removeBreakpoint(name) {
        this.customBreakpoints.delete(name);
        this.updateActiveBreakpoints();
    }

    updateActiveBreakpoints() {
        const newActive = new Set();

        for (const [name, condition] of this.customBreakpoints) {
            if (condition(this.ui)) {
                newActive.add(name);
            }
        }

        this.state.activeBreakpoints = newActive;
    }

    isActive(breakpointName) {
        return this.state.activeBreakpoints.has(breakpointName);
    }
}
```

### 2. UI主题管理器
```javascript
class UIThemeManager {
    constructor(uiService) {
        this.ui = uiService;
        this.themes = new Map();
        this.state = reactive({
            currentTheme: "default",
            isDarkMode: false
        });

        // 监听系统主题变化
        const darkModeQuery = window.matchMedia("(prefers-color-scheme: dark)");
        darkModeQuery.addEventListener("change", (e) => {
            this.state.isDarkMode = e.matches;
            this.applySystemTheme();
        });
    }

    registerTheme(name, config) {
        this.themes.set(name, config);
    }

    setTheme(themeName) {
        if (this.themes.has(themeName)) {
            this.state.currentTheme = themeName;
            this.applyTheme(themeName);
        }
    }

    applyTheme(themeName) {
        const theme = this.themes.get(themeName);
        if (theme) {
            // 应用CSS变量
            Object.entries(theme.cssVariables || {}).forEach(([key, value]) => {
                document.documentElement.style.setProperty(key, value);
            });

            // 应用CSS类
            document.body.className = theme.bodyClass || "";
        }
    }

    applySystemTheme() {
        const systemTheme = this.state.isDarkMode ? "dark" : "light";
        if (this.themes.has(systemTheme)) {
            this.setTheme(systemTheme);
        }
    }
}
```

## 总结

UI服务是 Odoo Web 客户端用户界面的核心服务，它提供了：
- **响应式设计**: 完整的屏幕尺寸检测和断点管理
- **UI状态管理**: 全局的UI状态和活动元素管理
- **阻塞UI**: 智能的UI阻塞和解除阻塞机制
- **焦点管理**: 完善的焦点控制和键盘导航支持
- **事件系统**: 基于EventBus的UI事件通信
- **性能优化**: 节流、缓存等多种性能优化策略

这个服务为 Odoo 的用户界面提供了可靠、高效的基础设施，是现代响应式Web应用的重要组成部分。
```
```
# Odoo UI 模块架构总览与使用指南

## 📁 模块概览

**目录路径**: `output/@web/core/ui/`  
**模块类型**: 核心基础模块 - 用户界面系统  
**功能范围**: UI状态管理、响应式设计、阻塞UI、焦点管理等核心UI功能

## 🏗️ 架构图

```
@web/core/ui/
├── 📄 block_ui.js          # 阻塞UI组件
└── 📄 ui_service.js        # UI服务
```

## 🎯 模块功能矩阵

| 模块 | 主要功能 | 核心API | 依赖关系 |
|------|----------|---------|----------|
| **block_ui.js** | 阻塞UI显示 | `BlockUI` 组件 | l10n, browser, owl |
| **ui_service.js** | UI状态管理 | `ui` 服务, `useActiveElement` | hooks, registry, timing, block_ui, browser, utils, hotkeys, owl |

## 🔄 数据流图

```mermaid
graph TD
    A[用户交互] --> B{交互类型}
    
    B -->|屏幕尺寸变化| C[resize事件]
    B -->|长时间操作| D[block/unblock调用]
    B -->|焦点管理| E[useActiveElement]
    
    C --> F[MediaQueryList监听]
    F --> G[updateSize]
    G --> H[ui.size更新]
    H --> I[resize事件触发]
    I --> J[组件响应式更新]
    
    D --> K[blockCount管理]
    K --> L[BLOCK/UNBLOCK事件]
    L --> M[BlockUI组件]
    M --> N[阻塞界面显示]
    
    E --> O[activateElement]
    O --> P[activeElems栈]
    P --> Q[焦点陷阱]
    Q --> R[键盘导航]
    
    S[断点检测] --> T[MEDIAS数组]
    T --> U[isSmall计算]
    U --> V[响应式布局]
```

## 🚀 快速开始

### 1. 响应式设计
```javascript
import { useService } from "@web/core/utils/hooks";

class ResponsiveComponent extends Component {
    setup() {
        this.ui = useService("ui");
        
        // 监听尺寸变化
        this.ui.bus.addEventListener("resize", () => {
            this.render();
        });
    }
    
    render() {
        return xml`
            <div t-att-class="ui.isSmall ? 'mobile-layout' : 'desktop-layout'">
                <t t-if="ui.isSmall">
                    <!-- 移动端布局 -->
                    <div class="mobile-content">移动端内容</div>
                </t>
                <t t-else="">
                    <!-- 桌面端布局 -->
                    <div class="desktop-content">桌面端内容</div>
                </t>
            </div>
        `;
    }
}
```

### 2. 阻塞UI使用
```javascript
class BlockingOperationExample extends Component {
    setup() {
        this.ui = useService("ui");
    }
    
    async performOperation() {
        this.ui.block({ 
            message: "正在处理数据...",
            delay: 500 
        });
        
        try {
            await this.longRunningOperation();
        } finally {
            this.ui.unblock();
        }
    }
    
    render() {
        return xml`
            <div>
                <button t-on-click="performOperation">执行操作</button>
                <BlockUI bus="ui.bus"/>
            </div>
        `;
    }
}
```

### 3. 焦点管理
```javascript
import { useActiveElement } from "@web/core/ui/ui_service";

class ModalComponent extends Component {
    setup() {
        // 自动管理焦点和键盘导航
        useActiveElement("modal");
    }
    
    render() {
        return xml`
            <div t-ref="modal" class="modal" tabindex="-1">
                <input type="text" placeholder="第一个输入框"/>
                <input type="text" placeholder="第二个输入框"/>
                <button t-on-click="close">关闭</button>
            </div>
        `;
    }
}
```

## 📚 核心概念详解

### 1. 阻塞UI组件 (Block UI Component)
- **用户体验**: 在长时间操作期间提供清晰的视觉反馈
- **渐进式消息**: 随时间推移显示不同的幽默和实用消息
- **状态管理**: 三种阻塞状态的完整管理
- **事件驱动**: 通过EventBus实现解耦的控制机制

### 2. UI服务 (UI Service)
- **响应式设计**: 完整的屏幕尺寸检测和断点管理
- **状态管理**: 全局UI状态和活动元素管理
- **焦点控制**: 完善的焦点管理和键盘导航支持
- **性能优化**: 节流、缓存等多种优化策略

### 3. 屏幕尺寸系统
- **7级断点**: XS、VSM、SM、MD、LG、XL、XXL
- **媒体查询**: 基于CSS媒体查询的响应式检测
- **实时监听**: 自动监听屏幕尺寸变化
- **Bootstrap兼容**: 与Bootstrap断点系统兼容

### 4. 活动元素管理
- **栈结构**: 使用栈管理活动元素层级
- **焦点陷阱**: 自动实现模态框的焦点陷阱
- **键盘导航**: 完整的Tab键循环导航
- **生命周期**: 自动处理元素激活和去激活

## 🔧 高级用法

### 1. 自定义断点系统
```javascript
class CustomBreakpointManager extends Component {
    setup() {
        this.ui = useService("ui");
        this.customBreakpoints = {
            isMobile: () => this.ui.size <= SIZES.SM,
            isTablet: () => this.ui.size >= SIZES.MD && this.ui.size <= SIZES.LG,
            isDesktop: () => this.ui.size >= SIZES.XL,
            isLargeScreen: () => this.ui.size >= SIZES.XXL
        };
        
        this.state = useState({
            currentBreakpoint: this.getCurrentBreakpoint()
        });
        
        this.ui.bus.addEventListener("resize", () => {
            this.state.currentBreakpoint = this.getCurrentBreakpoint();
        });
    }
    
    getCurrentBreakpoint() {
        for (const [name, check] of Object.entries(this.customBreakpoints)) {
            if (check()) {
                return name;
            }
        }
        return "unknown";
    }
}
```

### 2. 阻塞UI管理器
```javascript
class BlockUIManager {
    constructor(uiService) {
        this.ui = uiService;
        this.blockCount = 0;
        this.isBlocked = false;
    }
    
    block(options = {}) {
        this.blockCount++;
        if (!this.isBlocked) {
            this.isBlocked = true;
            this.ui.block(options);
        }
    }
    
    unblock() {
        if (this.blockCount > 0) {
            this.blockCount--;
        }
        if (this.blockCount === 0 && this.isBlocked) {
            this.isBlocked = false;
            this.ui.unblock();
        }
    }
    
    async withBlock(asyncFn, options = {}) {
        this.block(options);
        try {
            return await asyncFn();
        } finally {
            this.unblock();
        }
    }
}
```

### 3. 响应式布局管理器
```javascript
class ResponsiveLayoutManager extends Component {
    setup() {
        this.ui = useService("ui");
        this.state = useState({
            sidebarVisible: !this.ui.isSmall,
            navigationCollapsed: this.ui.isSmall
        });
        
        this.ui.bus.addEventListener("resize", () => {
            this.handleResize();
        });
    }
    
    handleResize() {
        // 自适应布局调整
        if (this.ui.isSmall) {
            this.state.sidebarVisible = false;
            this.state.navigationCollapsed = true;
        } else {
            this.state.navigationCollapsed = false;
        }
    }
    
    get layoutClasses() {
        const classes = ["main-layout"];
        
        // 根据屏幕尺寸添加类
        if (this.ui.size <= SIZES.SM) {
            classes.push("layout-mobile");
        } else if (this.ui.size <= SIZES.MD) {
            classes.push("layout-tablet");
        } else {
            classes.push("layout-desktop");
        }
        
        return classes.join(" ");
    }
}
```

### 4. UI状态管理器
```javascript
class UIStateManager {
    constructor(uiService) {
        this.ui = uiService;
        this.state = reactive({
            theme: "light",
            sidebarCollapsed: false,
            notifications: [],
            modals: []
        });
        
        this.ui.bus.addEventListener("resize", () => {
            this.handleResize();
        });
    }
    
    handleResize() {
        // 小屏幕时自动折叠侧边栏
        if (this.ui.isSmall && !this.state.sidebarCollapsed) {
            this.setSidebarCollapsed(true);
        }
    }
    
    setTheme(theme) {
        this.state.theme = theme;
        document.body.setAttribute("data-theme", theme);
    }
    
    setSidebarCollapsed(collapsed) {
        this.state.sidebarCollapsed = collapsed;
    }
    
    addModal(modal) {
        this.state.modals.push(modal);
    }
    
    removeModal(modalId) {
        const index = this.state.modals.findIndex(m => m.id === modalId);
        if (index > -1) {
            this.state.modals.splice(index, 1);
        }
    }
}
```

### 5. 自定义阻塞UI组件
```javascript
class CustomBlockUI extends Component {
    static template = xml`
        <t t-if="state.blockState === BLOCK_STATES.UNBLOCKED">
            <div/>
        </t>
        <t t-else="">
            <div class="custom-block-ui" 
                 t-att-class="state.blockState === BLOCK_STATES.VISIBLY_BLOCKED ? 'visible' : 'hidden'">
                <div class="custom-spinner">
                    <div class="spinner-ring"></div>
                </div>
                <div class="custom-message">
                    <h4 t-esc="state.line1"/>
                    <p t-esc="state.line2"/>
                </div>
                <div class="progress-bar" t-if="state.progress !== undefined">
                    <div class="progress-fill" 
                         t-att-style="'width: ' + state.progress + '%'"/>
                </div>
            </div>
        </t>
    `;
    
    static props = {
        bus: EventBus,
    };
    
    setup() {
        // 继承基础BlockUI的逻辑
        super.setup();
        
        // 添加进度条支持
        this.state.progress = undefined;
        
        // 监听进度更新事件
        this.props.bus.addEventListener("PROGRESS", (ev) => {
            this.state.progress = ev.detail.progress;
        });
    }
}
```

## 🎨 最佳实践

### 1. 响应式设计原则
```javascript
// ✅ 推荐：使用UI服务进行响应式设计
class ResponsiveComponent extends Component {
    setup() {
        this.ui = useService("ui");
    }
    
    get layoutClass() {
        return this.ui.isSmall ? "mobile-layout" : "desktop-layout";
    }
}

// ❌ 避免：硬编码屏幕尺寸检测
class BadResponsiveComponent extends Component {
    get isMobile() {
        return window.innerWidth < 768; // 硬编码断点
    }
}
```

### 2. 阻塞UI管理
```javascript
// ✅ 推荐：正确的阻塞UI使用
async performOperation() {
    this.ui.block({ delay: 500 });
    try {
        await this.operation();
    } finally {
        this.ui.unblock(); // 确保解除阻塞
    }
}

// ❌ 避免：忘记解除阻塞
async badOperation() {
    this.ui.block();
    await this.operation(); // 如果出错，UI永远阻塞
    this.ui.unblock();
}
```

### 3. 焦点管理
```javascript
// ✅ 推荐：使用useActiveElement Hook
class ModalComponent extends Component {
    setup() {
        useActiveElement("modal"); // 自动管理焦点
    }
}

// ❌ 避免：手动管理焦点
class BadModalComponent extends Component {
    setup() {
        // 手动实现焦点管理，容易出错
    }
}
```

### 4. 事件监听和清理
```javascript
// ✅ 推荐：正确的事件监听和清理
setup() {
    this.ui = useService("ui");
    
    const handleResize = () => this.handleResize();
    this.ui.bus.addEventListener("resize", handleResize);
    
    onWillUnmount(() => {
        this.ui.bus.removeEventListener("resize", handleResize);
    });
}

// ❌ 避免：忘记清理事件监听器
setup() {
    this.ui.bus.addEventListener("resize", () => {
        // 匿名函数无法清理
    });
}
```

## ⚡ 性能优化

### 1. 响应式更新优化
```javascript
// 使用节流避免频繁更新
browser.addEventListener("resize", throttleForAnimation(updateSize));

// 只在真正变化时触发事件
if (ui.size !== prevSize) {
    ui.isSmall = utils.isSmall(ui);
    bus.trigger("resize");
}
```

### 2. 媒体查询缓存
```javascript
// 启动时创建所有MediaQueryList，避免重复创建
const MEDIAS = getMediaQueryLists();
```

### 3. 计算属性优化
```javascript
// 使用计算属性实现懒计算和缓存
get activeElement() {
    return activeElems[activeElems.length - 1];
}
```

### 4. 事件优化
```javascript
// 变化检测避免不必要的事件触发
if (ui.size !== prevSize) {
    // 只在真正变化时更新
}
```

## 🔍 调试技巧

### 1. UI状态监控
```javascript
// 开发环境下的UI状态监控
if (odoo.debug) {
    const ui = useService("ui");
    
    // 监控尺寸变化
    ui.bus.addEventListener("resize", () => {
        console.log("屏幕尺寸变化:", {
            size: ui.size,
            isSmall: ui.isSmall,
            activeElement: ui.activeElement
        });
    });
    
    // 监控阻塞状态
    ui.bus.addEventListener("BLOCK", () => {
        console.log("UI已阻塞");
    });
    
    ui.bus.addEventListener("UNBLOCK", () => {
        console.log("UI已解除阻塞");
    });
}
```

### 2. 断点调试工具
```javascript
class BreakpointDebugger {
    static enable() {
        const ui = useService("ui");
        
        // 在页面上显示当前断点信息
        const debugInfo = document.createElement("div");
        debugInfo.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            z-index: 9999;
        `;
        
        const updateInfo = () => {
            debugInfo.innerHTML = `
                Size: ${ui.size}<br>
                IsSmall: ${ui.isSmall}<br>
                Width: ${window.innerWidth}px<br>
                Height: ${window.innerHeight}px
            `;
        };
        
        updateInfo();
        ui.bus.addEventListener("resize", updateInfo);
        document.body.appendChild(debugInfo);
    }
}
```

## 📖 相关资源

- [OWL 框架文档](https://github.com/odoo/owl)
- [CSS 媒体查询](https://developer.mozilla.org/en-US/docs/Web/CSS/Media_Queries)
- [Web Accessibility Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [响应式设计原则](https://web.dev/responsive-web-design-basics/)

## 🎯 总结

Odoo UI 模块是一个完整的用户界面管理解决方案，提供了：
- **响应式设计**: 完整的屏幕尺寸检测和断点管理系统
- **阻塞UI**: 优雅的长时间操作用户反馈机制
- **焦点管理**: 完善的焦点控制和键盘导航支持
- **状态管理**: 全局UI状态的统一管理
- **性能优化**: 节流、缓存等多种性能优化策略
- **事件系统**: 基于EventBus的UI事件通信机制

这个模块为 Odoo 的用户界面提供了可靠、高效的基础设施，支持现代响应式Web应用的各种需求，是提升用户体验的重要基础组件。

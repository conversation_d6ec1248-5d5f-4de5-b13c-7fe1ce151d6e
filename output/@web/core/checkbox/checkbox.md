# CheckBox - 复选框组件

## 概述

`checkbox.js` 是 Odoo Web 核心模块的复选框组件，提供了一个功能完整的自定义复选框实现。该组件支持键盘导航、点击事件处理、禁用状态、自定义样式和插槽内容，为Odoo Web应用提供了标准化的复选框用户界面元素，确保了一致的用户体验和无障碍访问。

## 文件信息
- **路径**: `/web/static/src/core/checkbox/checkbox.js`
- **行数**: 100
- **模块**: `@web/core/checkbox/checkbox`

## 依赖关系

```javascript
// 核心依赖
'@web/core/hotkeys/hotkey_hook'    // 快捷键钩子
'@odoo/owl'                        // OWL框架
```

## 核心功能

### 1. 组件属性定义

```javascript
static props = {
    id: {
        type: true,
        optional: true,
    },
    disabled: {
        type: Boolean,
        optional: true,
    },
    value: {
        type: Boolean,
        optional: true,
    },
    slots: {
        type: Object,
        optional: true,
    },
    onChange: {
        type: Function,
        optional: true,
    },
    className: {
        type: String,
        optional: true,
    },
    name: {
        type: String,
        optional: true,
    },
};
```

**属性功能**:
- **标识符**: 支持自定义ID和name属性
- **状态控制**: value控制选中状态，disabled控制禁用状态
- **事件处理**: onChange回调处理状态变化
- **样式定制**: className支持自定义CSS类
- **内容插槽**: slots支持自定义标签内容

### 2. 默认属性

```javascript
static defaultProps = {
    onChange: () => {},
};
```

**默认属性功能**:
- **空回调**: 提供默认的空onChange回调
- **安全调用**: 确保onChange始终可调用

### 3. 组件初始化

```javascript
setup() {
    this.id = `checkbox-comp-${CheckBox.nextId++}`;
    this.rootRef = useRef("root");

    // Make it toggleable through the Enter hotkey
    // when the focus is inside the root element
    useHotkey(
        "Enter",
        ({ area }) => {
            const oldValue = area.querySelector("input").checked;
            this.props.onChange(!oldValue);
        },
        { area: () => this.rootRef.el, bypassEditableProtection: true }
    );
}
```

**初始化功能**:
- **唯一ID**: 生成唯一的组件ID
- **引用管理**: 创建根元素引用
- **键盘支持**: 注册Enter键快捷键
- **焦点区域**: 限制快捷键作用范围
- **编辑保护**: 绕过可编辑元素的保护机制

### 4. 点击事件处理

```javascript
onClick(ev) {
    if (ev.composedPath().find((el) => ["INPUT", "LABEL"].includes(el.tagName))) {
        // The onChange will handle these cases.
        ev.stopPropagation();
        return;
    }

    // Reproduce the click event behavior as if it comes from the input element.
    const input = this.rootRef.el.querySelector("input");
    input.focus();
    if (!this.props.disabled) {
        ev.stopPropagation();
        input.checked = !input.checked;
        this.props.onChange(input.checked);
    }
}
```

**点击处理功能**:
- **事件路径检查**: 检查点击是否来自INPUT或LABEL元素
- **事件委托**: 将外部点击转发到input元素
- **焦点管理**: 点击时设置焦点到input元素
- **状态切换**: 切换复选框的选中状态
- **禁用检查**: 禁用状态下不响应点击

### 5. 变化事件处理

```javascript
onChange(ev) {
    if (!this.props.disabled) {
        this.props.onChange(ev.target.checked);
    }
}
```

**变化处理功能**:
- **禁用检查**: 禁用状态下不触发变化回调
- **状态传递**: 将新的选中状态传递给父组件
- **事件封装**: 封装原生change事件

### 6. 静态属性

```javascript
static template = "web.CheckBox";
static nextId = 1;
```

**静态属性功能**:
- **模板引用**: 指定组件使用的模板
- **ID计数器**: 全局ID计数器确保唯一性

## 使用场景

### 1. 基础复选框

```javascript
// 基础复选框使用
<CheckBox
    value={this.state.isChecked}
    onChange={(checked) => {
        this.state.isChecked = checked;
        console.log('复选框状态:', checked);
    }}
>
    同意服务条款
</CheckBox>
```

### 2. 禁用状态复选框

```javascript
// 禁用状态的复选框
<CheckBox
    value={true}
    disabled={true}
    onChange={(checked) => {
        // 禁用状态下不会触发
    }}
>
    只读选项
</CheckBox>
```

### 3. 自定义样式复选框

```javascript
// 带自定义样式的复选框
<CheckBox
    value={this.state.isSelected}
    className="custom-checkbox large-checkbox"
    onChange={(checked) => {
        this.handleSelectionChange(checked);
    }}
>
    <span class="checkbox-label">
        <i class="fa fa-star"/> 标记为重要
    </span>
</CheckBox>
```

### 4. 表单中的复选框

```javascript
// 在表单中使用
<form>
    <CheckBox
        name="newsletter"
        value={this.state.subscribeNewsletter}
        onChange={(checked) => {
            this.state.subscribeNewsletter = checked;
        }}
    >
        订阅邮件通讯
    </CheckBox>
    
    <CheckBox
        name="terms"
        value={this.state.acceptTerms}
        onChange={(checked) => {
            this.state.acceptTerms = checked;
        }}
    >
        我已阅读并同意<a href="/terms">使用条款</a>
    </CheckBox>
</form>
```

## 增强示例

```javascript
// 增强的复选框组件
const EnhancedCheckBox = {
    createAdvancedCheckBox: () => {
        class AdvancedCheckBox extends CheckBox {
            static props = {
                ...CheckBox.props,
                indeterminate: { type: Boolean, optional: true },
                size: { type: String, optional: true },
                color: { type: String, optional: true },
                animation: { type: Boolean, optional: true },
                tooltip: { type: String, optional: true },
                validation: { type: Object, optional: true },
                group: { type: String, optional: true },
                onFocus: { type: Function, optional: true },
                onBlur: { type: Function, optional: true }
            };

            static defaultProps = {
                ...CheckBox.defaultProps,
                size: 'medium',
                color: 'primary',
                animation: true,
                onFocus: () => {},
                onBlur: () => {}
            };

            setup() {
                super.setup();
                
                // 增强状态
                this.enhancedState = useState({
                    isFocused: false,
                    isHovered: false,
                    validationState: null,
                    animationClass: ''
                });

                // 配置选项
                this.config = {
                    enableTooltip: !!this.props.tooltip,
                    enableValidation: !!this.props.validation,
                    enableGrouping: !!this.props.group,
                    enableAnimation: this.props.animation
                };

                // 验证规则
                this.validationRules = this.props.validation || {};

                // 组管理
                if (this.config.enableGrouping) {
                    this.registerToGroup();
                }

                // 工具提示
                if (this.config.enableTooltip) {
                    this.setupTooltip();
                }
            }

            // 增强的点击处理
            onClick(ev) {
                // 验证检查
                if (this.config.enableValidation && !this.validateChange(!this.getCurrentValue())) {
                    return;
                }

                // 动画效果
                if (this.config.enableAnimation) {
                    this.playClickAnimation();
                }

                // 调用父类方法
                super.onClick(ev);

                // 组处理
                if (this.config.enableGrouping) {
                    this.handleGroupChange();
                }
            }

            // 增强的变化处理
            onChange(ev) {
                const newValue = ev.target.checked;

                // 验证新值
                if (this.config.enableValidation) {
                    const validationResult = this.validateValue(newValue);
                    this.enhancedState.validationState = validationResult;
                    
                    if (!validationResult.isValid) {
                        this.showValidationError(validationResult.message);
                        return;
                    }
                }

                // 调用父类方法
                super.onChange(ev);

                // 触发增强回调
                this.triggerEnhancedCallbacks(newValue);
            }

            // 获取当前值
            getCurrentValue() {
                const input = this.rootRef.el?.querySelector("input");
                return input ? input.checked : this.props.value;
            }

            // 验证值变化
            validateChange(newValue) {
                if (!this.config.enableValidation) {
                    return true;
                }

                const result = this.validateValue(newValue);
                return result.isValid;
            }

            // 验证值
            validateValue(value) {
                const rules = this.validationRules;
                
                // 必填验证
                if (rules.required && !value) {
                    return {
                        isValid: false,
                        message: rules.requiredMessage || '此项为必填项'
                    };
                }

                // 自定义验证
                if (rules.custom && typeof rules.custom === 'function') {
                    const customResult = rules.custom(value);
                    if (!customResult.isValid) {
                        return customResult;
                    }
                }

                return { isValid: true };
            }

            // 显示验证错误
            showValidationError(message) {
                // 可以集成通知服务或显示错误提示
                console.warn('Checkbox validation error:', message);
                
                // 添加错误样式
                this.enhancedState.validationState = {
                    isValid: false,
                    message
                };
            }

            // 播放点击动画
            playClickAnimation() {
                this.enhancedState.animationClass = 'checkbox-click-animation';
                
                setTimeout(() => {
                    this.enhancedState.animationClass = '';
                }, 300);
            }

            // 注册到组
            registerToGroup() {
                const groupName = this.props.group;
                
                if (!window.checkboxGroups) {
                    window.checkboxGroups = new Map();
                }

                if (!window.checkboxGroups.has(groupName)) {
                    window.checkboxGroups.set(groupName, new Set());
                }

                window.checkboxGroups.get(groupName).add(this);
            }

            // 处理组变化
            handleGroupChange() {
                const groupName = this.props.group;
                const group = window.checkboxGroups?.get(groupName);
                
                if (group) {
                    // 通知组内其他复选框
                    for (const checkbox of group) {
                        if (checkbox !== this) {
                            checkbox.onGroupMemberChange?.(this.getCurrentValue());
                        }
                    }
                }
            }

            // 组成员变化回调
            onGroupMemberChange(memberValue) {
                // 子类可以重写此方法处理组变化
                console.log('Group member changed:', memberValue);
            }

            // 设置工具提示
            setupTooltip() {
                // 简单的工具提示实现
                this.tooltipElement = null;
            }

            // 显示工具提示
            showTooltip() {
                if (!this.config.enableTooltip || this.tooltipElement) {
                    return;
                }

                this.tooltipElement = document.createElement('div');
                this.tooltipElement.className = 'checkbox-tooltip';
                this.tooltipElement.textContent = this.props.tooltip;
                
                document.body.appendChild(this.tooltipElement);
                
                // 定位工具提示
                this.positionTooltip();
            }

            // 隐藏工具提示
            hideTooltip() {
                if (this.tooltipElement) {
                    document.body.removeChild(this.tooltipElement);
                    this.tooltipElement = null;
                }
            }

            // 定位工具提示
            positionTooltip() {
                if (!this.tooltipElement || !this.rootRef.el) {
                    return;
                }

                const rect = this.rootRef.el.getBoundingClientRect();
                const tooltip = this.tooltipElement;
                
                tooltip.style.position = 'absolute';
                tooltip.style.left = `${rect.left}px`;
                tooltip.style.top = `${rect.bottom + 5}px`;
                tooltip.style.zIndex = '1000';
            }

            // 焦点处理
            onFocus(ev) {
                this.enhancedState.isFocused = true;
                this.props.onFocus(ev);
                
                if (this.config.enableTooltip) {
                    this.showTooltip();
                }
            }

            // 失焦处理
            onBlur(ev) {
                this.enhancedState.isFocused = false;
                this.props.onBlur(ev);
                
                if (this.config.enableTooltip) {
                    this.hideTooltip();
                }
            }

            // 鼠标悬停处理
            onMouseEnter() {
                this.enhancedState.isHovered = true;
                
                if (this.config.enableTooltip) {
                    this.showTooltip();
                }
            }

            // 鼠标离开处理
            onMouseLeave() {
                this.enhancedState.isHovered = false;
                
                if (this.config.enableTooltip && !this.enhancedState.isFocused) {
                    this.hideTooltip();
                }
            }

            // 触发增强回调
            triggerEnhancedCallbacks(value) {
                // 可以添加更多的回调逻辑
                if (this.props.onValueChange) {
                    this.props.onValueChange(value, this);
                }
            }

            // 设置不确定状态
            setIndeterminate(indeterminate) {
                const input = this.rootRef.el?.querySelector("input");
                if (input) {
                    input.indeterminate = indeterminate;
                }
            }

            // 获取组件状态
            getState() {
                return {
                    value: this.getCurrentValue(),
                    disabled: this.props.disabled,
                    focused: this.enhancedState.isFocused,
                    hovered: this.enhancedState.isHovered,
                    validationState: this.enhancedState.validationState
                };
            }

            // 组件销毁时清理
            willDestroy() {
                // 清理工具提示
                this.hideTooltip();

                // 从组中移除
                if (this.config.enableGrouping) {
                    const group = window.checkboxGroups?.get(this.props.group);
                    if (group) {
                        group.delete(this);
                    }
                }

                super.willDestroy && super.willDestroy();
            }
        }

        return AdvancedCheckBox;
    }
};

// 使用示例
const AdvancedCheckBox = EnhancedCheckBox.createAdvancedCheckBox();

// 带验证的复选框
<AdvancedCheckBox
    value={this.state.acceptTerms}
    validation={{
        required: true,
        requiredMessage: '必须同意条款才能继续',
        custom: (value) => {
            if (!value) {
                return { isValid: false, message: '请仔细阅读条款' };
            }
            return { isValid: true };
        }
    }}
    onChange={(checked) => {
        this.state.acceptTerms = checked;
    }}
    tooltip="点击同意我们的服务条款"
    size="large"
    color="primary"
    animation={true}
>
    我同意服务条款
</AdvancedCheckBox>

// 分组复选框
<AdvancedCheckBox
    value={this.state.option1}
    group="options"
    onChange={(checked) => {
        this.state.option1 = checked;
    }}
>
    选项 1
</AdvancedCheckBox>

<AdvancedCheckBox
    value={this.state.option2}
    group="options"
    onChange={(checked) => {
        this.state.option2 = checked;
    }}
>
    选项 2
</AdvancedCheckBox>
```

## 技术特点

### 1. 键盘导航
- Enter键切换状态
- 焦点管理
- 无障碍访问支持

### 2. 事件处理
- 完整的点击事件处理
- 事件委托机制
- 状态同步

### 3. 组件封装
- 标准化的属性接口
- 插槽内容支持
- 样式定制能力

### 4. 状态管理
- 受控组件模式
- 禁用状态支持
- 变化回调机制

## 设计模式

### 1. 组件模式 (Component Pattern)
- 封装复选框逻辑
- 可重用的UI组件

### 2. 观察者模式 (Observer Pattern)
- onChange回调机制
- 状态变化通知

### 3. 委托模式 (Delegation Pattern)
- 事件委托处理
- 统一的事件管理

## 注意事项

1. **无障碍性**: 确保键盘导航和屏幕阅读器支持
2. **事件处理**: 正确处理各种点击场景
3. **状态同步**: 保持组件状态与props同步
4. **性能优化**: 避免不必要的重渲染

## 扩展建议

1. **样式主题**: 支持多种视觉主题
2. **动画效果**: 添加状态切换动画
3. **验证集成**: 集成表单验证系统
4. **组合功能**: 支持复选框组和全选功能
5. **自定义图标**: 支持自定义选中图标

该复选框组件为Odoo Web应用提供了标准化的复选框功能，通过完善的事件处理和键盘支持确保了良好的用户体验和无障碍访问。

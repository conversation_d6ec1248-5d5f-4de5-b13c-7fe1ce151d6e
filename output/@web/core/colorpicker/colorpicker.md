# Colorpicker - 颜色选择器组件

## 概述

`colorpicker.js` 是 Odoo Web 核心模块的颜色选择器组件，提供了一个功能完整的颜色选择界面。该组件支持HSL和RGB颜色模式、透明度控制、实时预览、拖拽交互和多种输入方式，为Odoo Web应用提供了专业级的颜色选择功能，广泛应用于主题定制、样式编辑和设计工具等场景。

## 文件信息
- **路径**: `/web/static/src/core/colorpicker/colorpicker.js`
- **行数**: 468
- **模块**: `@web/core/colorpicker/colorpicker`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/colors'           // 颜色工具
'@web/core/utils/functions'        // 函数工具
'@web/core/utils/numbers'          // 数字工具
'@web/core/utils/timing'           // 时间工具
'@odoo/owl'                        // OWL框架
```

## 核心功能

### 1. 组件属性定义

```javascript
static props = {
    document: { type: true, optional: true },
    defaultColor: { type: String, optional: true },
    selectedColor: { type: String, optional: true },
    noTransparency: { type: Boolean, optional: true },
    stopClickPropagation: { type: Boolean, optional: true },
    onColorSelect: { type: Function, optional: true },
    onColorPreview: { type: Function, optional: true },
    onInputEnter: { type: Function, optional: true },
};
```

**属性功能**:
- **文档对象**: 支持自定义文档对象（用于iframe场景）
- **颜色控制**: defaultColor和selectedColor控制初始和当前颜色
- **透明度**: noTransparency控制是否禁用透明度
- **事件控制**: stopClickPropagation控制事件传播
- **回调函数**: 完整的颜色选择和预览回调

### 2. 默认属性

```javascript
static defaultProps = {
    document: window.document,
    defaultColor: "#FF0000",
    noTransparency: false,
    stopClickPropagation: false,
    onColorSelect: () => {},
    onColorPreview: () => {},
    onInputEnter: () => {},
};
```

**默认属性功能**:
- **红色默认**: 默认红色作为初始颜色
- **透明度启用**: 默认启用透明度控制
- **事件传播**: 默认允许事件传播
- **空回调**: 提供默认的空回调函数

### 3. 组件初始化

```javascript
setup() {
    this.pickerFlag = false;
    this.sliderFlag = false;
    this.opacitySliderFlag = false;
    this.colorComponents = {};
    this.uniqueId = uniqueId("colorpicker");
    this.selectedHexValue = "";

    this.debouncedOnChangeInputs = debounce(this.onChangeInputs.bind(this), 10, true);

    // 引用管理
    this.elRef = useRef("el");
    this.colorPickerAreaRef = useRef("colorPickerArea");
    this.colorPickerPointerRef = useRef("colorPickerPointer");
    this.colorSliderRef = useRef("colorSlider");
    this.colorSliderPointerRef = useRef("colorSliderPointer");
    this.opacitySliderRef = useRef("opacitySlider");
    this.opacitySliderPointerRef = useRef("opacitySliderPointer");
}
```

**初始化功能**:
- **拖拽状态**: 管理各个区域的拖拽状态
- **颜色组件**: 存储颜色的各种表示形式
- **唯一标识**: 生成组件的唯一ID
- **防抖处理**: 对输入变化进行防抖处理
- **引用管理**: 创建各个UI元素的引用

### 4. 颜色初始化

```javascript
onWillStart() {
    this.colorComponents = this.getColorComponents(this.props.selectedColor || this.props.defaultColor);
    this.selectedHexValue = this.colorComponents.hex;
}
```

**颜色初始化功能**:
- **颜色解析**: 解析初始颜色为各种格式
- **组件存储**: 存储颜色的HSL、RGB、HEX等表示
- **值同步**: 同步选中的十六进制值

### 5. 颜色组件获取

```javascript
getColorComponents(color) {
    const rgba = convertCSSColorToRgba(color);
    if (!rgba) {
        return false;
    }
    const [r, g, b, a] = rgba;
    const [h, s, l] = convertRgbToHsl(r, g, b);
    return {
        red: r,
        green: g,
        blue: b,
        opacity: a,
        hue: h,
        saturation: s,
        lightness: l,
        hex: convertRgbToHex(r, g, b),
        cssColor: `rgba(${r}, ${g}, ${b}, ${a})`,
    };
}
```

**颜色组件功能**:
- **格式转换**: 支持CSS颜色到RGBA的转换
- **多格式支持**: 提供RGB、HSL、HEX等多种格式
- **透明度**: 完整的透明度支持
- **CSS兼容**: 生成CSS兼容的颜色字符串

### 6. 拖拽事件处理

```javascript
onMouseDown(ev) {
    if (ev.which !== 1) return;

    const target = ev.target;
    if (target.closest(".o_color_picker_area")) {
        this.pickerFlag = true;
        this.onMouseMove(ev);
    } else if (target.closest(".o_color_slider")) {
        this.sliderFlag = true;
        this.onMouseMove(ev);
    } else if (target.closest(".o_opacity_slider")) {
        this.opacitySliderFlag = true;
        this.onMouseMove(ev);
    }

    ev.preventDefault();
}

onMouseMove(ev) {
    if (this.pickerFlag) {
        this.moveColorPicker(ev);
    } else if (this.sliderFlag) {
        this.moveColorSlider(ev);
    } else if (this.opacitySliderFlag) {
        this.moveOpacitySlider(ev);
    }
}

onMouseUp() {
    this.pickerFlag = false;
    this.sliderFlag = false;
    this.opacitySliderFlag = false;
}
```

**拖拽处理功能**:
- **区域识别**: 识别不同的拖拽区域
- **状态管理**: 管理各个区域的拖拽状态
- **鼠标跟踪**: 跟踪鼠标移动进行颜色调整
- **事件阻止**: 阻止默认的鼠标事件

### 7. 颜色选择器移动

```javascript
moveColorPicker(ev) {
    const area = this.colorPickerAreaRef.el;
    const pointer = this.colorPickerPointerRef.el;
    const areaRect = area.getBoundingClientRect();

    let x = ev.clientX - areaRect.left;
    let y = ev.clientY - areaRect.top;

    x = clamp(x, 0, areaRect.width);
    y = clamp(y, 0, areaRect.height);

    const saturation = Math.round((x / areaRect.width) * 100);
    const lightness = Math.round(100 - (y / areaRect.height) * 100);

    this.colorComponents.saturation = saturation;
    this.colorComponents.lightness = lightness;

    this.updateColorComponents();
    this.updateUI();
    this.triggerColorPreview();
}
```

**颜色选择功能**:
- **坐标计算**: 计算鼠标在颜色区域的相对位置
- **边界限制**: 限制坐标在有效范围内
- **饱和度计算**: 根据X坐标计算饱和度
- **亮度计算**: 根据Y坐标计算亮度
- **实时更新**: 实时更新颜色组件和UI

### 8. 使用场景

```javascript
// 基础颜色选择器
<Colorpicker
    selectedColor="#FF5733"
    onColorSelect={(color) => {
        console.log('选择的颜色:', color);
        this.updateThemeColor(color);
    }}
    onColorPreview={(color) => {
        this.previewColor(color);
    }}
/>

// 禁用透明度的颜色选择器
<Colorpicker
    defaultColor="#00FF00"
    noTransparency={true}
    onColorSelect={(color) => {
        this.setBackgroundColor(color);
    }}
/>

// 带输入回调的颜色选择器
<Colorpicker
    selectedColor={this.state.currentColor}
    onColorSelect={(color) => {
        this.state.currentColor = color;
    }}
    onInputEnter={(color) => {
        this.applyColorImmediately(color);
    }}
    stopClickPropagation={true}
/>
```

## 技术特点

### 1. 多格式支持
- HSL颜色模式
- RGB颜色模式
- 十六进制格式
- CSS颜色字符串

### 2. 交互体验
- 拖拽式颜色选择
- 实时颜色预览
- 键盘输入支持
- 透明度控制

### 3. 性能优化
- 防抖输入处理
- 高效的颜色转换
- 最小化重渲染

### 4. 可定制性
- 可选透明度控制
- 自定义默认颜色
- 灵活的回调机制

## 设计模式

### 1. 组件模式 (Component Pattern)
- 封装颜色选择逻辑
- 可重用的UI组件

### 2. 观察者模式 (Observer Pattern)
- 颜色变化通知
- 实时预览更新

### 3. 状态模式 (State Pattern)
- 拖拽状态管理
- 交互状态控制

## 注意事项

1. **颜色格式**: 确保输入颜色格式的有效性
2. **性能优化**: 合理使用防抖处理频繁操作
3. **用户体验**: 提供清晰的视觉反馈
4. **无障碍性**: 支持键盘操作和屏幕阅读器

## 扩展建议

1. **预设颜色**: 添加常用颜色预设
2. **颜色历史**: 记录最近使用的颜色
3. **调色板**: 支持自定义调色板
4. **颜色格式**: 支持更多颜色格式
5. **主题集成**: 与应用主题系统集成

该颜色选择器组件为Odoo Web应用提供了专业级的颜色选择功能，通过直观的拖拽交互和完整的颜色格式支持确保了优秀的用户体验。
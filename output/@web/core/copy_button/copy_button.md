# CopyButton - 复制按钮组件

## 概述

`copy_button.js` 是 Odoo Web 核心模块的复制按钮组件，提供了一键复制功能的用户界面元素。该组件支持文本和对象内容的复制、自定义样式、成功提示和错误处理，通过现代浏览器的Clipboard API实现了安全可靠的复制功能，为Odoo Web应用提供了便捷的数据复制体验。

## 文件信息
- **路径**: `/web/static/src/core/copy_button/copy_button.js`
- **行数**: 49
- **模块**: `@web/core/copy_button/copy_button`

## 依赖关系

```javascript
// 核心依赖
'@web/core/browser/browser'        // 浏览器服务
'@web/core/tooltip/tooltip'        // 工具提示组件
'@web/core/popover/popover_hook'   // 弹出框钩子
'@odoo/owl'                        // OWL框架
```

## 核心功能

### 1. 组件属性定义

```javascript
static props = {
    className: { type: String, optional: true },
    copyText: { type: String, optional: true },
    disabled: { type: Boolean, optional: true },
    successText: { type: String, optional: true },
    icon: { type: String, optional: true },
    content: { type: [String, Object], optional: true },
};
```

**属性功能**:
- **样式定制**: className支持自定义CSS类
- **复制文本**: copyText设置要复制的文本内容
- **禁用状态**: disabled控制按钮的可用性
- **成功提示**: successText设置复制成功后的提示文本
- **图标定制**: icon支持自定义按钮图标
- **内容类型**: content支持字符串和对象类型的复制内容

### 2. 组件初始化

```javascript
setup() {
    this.button = useRef("button");
    this.popover = usePopover(Tooltip);
}
```

**初始化功能**:
- **按钮引用**: 创建按钮元素的引用
- **弹出框**: 初始化工具提示弹出框
- **钩子集成**: 集成OWL的钩子系统

### 3. 成功提示显示

```javascript
showTooltip() {
    this.popover.open(this.button.el, { tooltip: this.props.successText });
    browser.setTimeout(this.popover.close, 800);
}
```

**提示显示功能**:
- **弹出提示**: 在按钮位置显示成功提示
- **自动关闭**: 800毫秒后自动关闭提示
- **位置定位**: 相对于按钮元素定位提示框
- **用户反馈**: 提供即时的操作反馈

### 4. 复制操作处理

```javascript
async onClick() {
    let write;
    // any kind of content can be copied into the clipboard using
    // the appropriate native methods
    if (typeof this.props.content === "string" || this.props.content instanceof String) {
        write = (value) => browser.navigator.clipboard.writeText(value);
    } else {
        write = (value) => browser.navigator.clipboard.write(value);
    }
    try {
        await write(this.props.content);
    } catch (error) {
        return browser.console.warn(error);
    }
    this.showTooltip();
}
```

**复制处理功能**:
- **类型检测**: 检测内容类型选择合适的复制方法
- **文本复制**: 使用writeText方法复制字符串内容
- **对象复制**: 使用write方法复制复杂对象
- **异步操作**: 支持异步的剪贴板操作
- **错误处理**: 捕获并记录复制失败的错误
- **成功反馈**: 复制成功后显示提示

## 使用场景

### 1. 基础文本复制

```javascript
// 复制简单文本
<CopyButton
    content="Hello, World!"
    successText="Text copied!"
    icon="fa-copy"
    className="btn btn-sm btn-outline-secondary"
/>
```

### 2. 复制用户数据

```javascript
// 复制用户信息
<CopyButton
    content={`Name: ${user.name}\nEmail: ${user.email}\nPhone: ${user.phone}`}
    successText="User info copied to clipboard"
    icon="fa-user"
    className="copy-user-btn"
/>
```

### 3. 复制链接地址

```javascript
// 复制分享链接
<CopyButton
    content={window.location.href}
    successText="Link copied!"
    icon="fa-link"
    className="btn btn-primary btn-sm"
    disabled={!this.canShare}
/>
```

### 4. 复制JSON数据

```javascript
// 复制结构化数据
<CopyButton
    content={JSON.stringify(this.exportData, null, 2)}
    successText="Data exported to clipboard"
    icon="fa-download"
    className="export-btn"
/>
```

### 5. 复制富文本内容

```javascript
// 复制富文本（需要ClipboardItem）
const richTextContent = new ClipboardItem({
    'text/html': new Blob([htmlContent], { type: 'text/html' }),
    'text/plain': new Blob([plainText], { type: 'text/plain' })
});

<CopyButton
    content={richTextContent}
    successText="Rich content copied!"
    icon="fa-clipboard"
    className="rich-copy-btn"
/>
```

## 增强示例

```javascript
// 增强的复制按钮组件
const EnhancedCopyButton = {
    createAdvancedCopyButton: () => {
        class AdvancedCopyButton extends CopyButton {
            static props = {
                ...CopyButton.props,
                onCopySuccess: { type: Function, optional: true },
                onCopyError: { type: Function, optional: true },
                confirmBeforeCopy: { type: Boolean, optional: true },
                copyFormat: { type: String, optional: true },
                maxLength: { type: Number, optional: true },
                showPreview: { type: Boolean, optional: true },
                analytics: { type: Boolean, optional: true }
            };

            static defaultProps = {
                confirmBeforeCopy: false,
                copyFormat: 'text',
                showPreview: false,
                analytics: false
            };

            setup() {
                super.setup();
                
                // 增强状态
                this.enhancedState = useState({
                    isProcessing: false,
                    copyCount: 0,
                    lastCopyTime: null,
                    previewVisible: false
                });

                // 配置选项
                this.config = {
                    enableAnalytics: this.props.analytics,
                    enablePreview: this.props.showPreview,
                    enableConfirmation: this.props.confirmBeforeCopy
                };
            }

            // 增强的点击处理
            async onClick() {
                if (this.enhancedState.isProcessing) {
                    return;
                }

                // 确认对话框
                if (this.config.enableConfirmation) {
                    const confirmed = await this.showConfirmation();
                    if (!confirmed) {
                        return;
                    }
                }

                // 预览功能
                if (this.config.enablePreview) {
                    this.showPreview();
                    return;
                }

                await this.performCopy();
            }

            // 执行复制操作
            async performCopy() {
                this.enhancedState.isProcessing = true;

                try {
                    // 处理内容
                    const processedContent = this.processContent();
                    
                    // 执行复制
                    await this.copyToClipboard(processedContent);
                    
                    // 成功处理
                    this.handleCopySuccess();
                    
                } catch (error) {
                    this.handleCopyError(error);
                } finally {
                    this.enhancedState.isProcessing = false;
                }
            }

            // 处理复制内容
            processContent() {
                let content = this.props.content;

                // 长度限制
                if (this.props.maxLength && typeof content === 'string') {
                    if (content.length > this.props.maxLength) {
                        content = content.substring(0, this.props.maxLength) + '...';
                    }
                }

                // 格式转换
                switch (this.props.copyFormat) {
                    case 'json':
                        if (typeof content === 'object') {
                            content = JSON.stringify(content, null, 2);
                        }
                        break;
                    case 'csv':
                        if (Array.isArray(content)) {
                            content = this.arrayToCsv(content);
                        }
                        break;
                    case 'markdown':
                        content = this.toMarkdown(content);
                        break;
                }

                return content;
            }

            // 复制到剪贴板
            async copyToClipboard(content) {
                let write;
                
                if (typeof content === "string" || content instanceof String) {
                    write = (value) => browser.navigator.clipboard.writeText(value);
                } else {
                    write = (value) => browser.navigator.clipboard.write(value);
                }

                await write(content);
            }

            // 处理复制成功
            handleCopySuccess() {
                this.enhancedState.copyCount++;
                this.enhancedState.lastCopyTime = Date.now();

                // 显示提示
                this.showTooltip();

                // 分析统计
                if (this.config.enableAnalytics) {
                    this.trackCopyEvent('success');
                }

                // 回调通知
                if (this.props.onCopySuccess) {
                    this.props.onCopySuccess({
                        content: this.props.content,
                        copyCount: this.enhancedState.copyCount
                    });
                }
            }

            // 处理复制错误
            handleCopyError(error) {
                browser.console.warn('Copy failed:', error);

                // 分析统计
                if (this.config.enableAnalytics) {
                    this.trackCopyEvent('error', error.message);
                }

                // 回调通知
                if (this.props.onCopyError) {
                    this.props.onCopyError(error);
                }

                // 显示错误提示
                this.showErrorTooltip(error.message);
            }

            // 显示确认对话框
            async showConfirmation() {
                return new Promise((resolve) => {
                    const content = this.getPreviewContent();
                    const confirmed = confirm(`Copy the following content?\n\n${content}`);
                    resolve(confirmed);
                });
            }

            // 显示预览
            showPreview() {
                this.enhancedState.previewVisible = true;
                
                // 3秒后自动隐藏
                setTimeout(() => {
                    this.enhancedState.previewVisible = false;
                }, 3000);
            }

            // 获取预览内容
            getPreviewContent() {
                const content = this.props.content;
                if (typeof content === 'string') {
                    return content.length > 100 ? content.substring(0, 100) + '...' : content;
                }
                return JSON.stringify(content, null, 2).substring(0, 100) + '...';
            }

            // 显示错误提示
            showErrorTooltip(message) {
                this.popover.open(this.button.el, { 
                    tooltip: `Copy failed: ${message}`,
                    class: 'error-tooltip'
                });
                browser.setTimeout(this.popover.close, 2000);
            }

            // 数组转CSV
            arrayToCsv(array) {
                if (!Array.isArray(array) || array.length === 0) {
                    return '';
                }

                const headers = Object.keys(array[0]);
                const csvContent = [
                    headers.join(','),
                    ...array.map(row => 
                        headers.map(header => 
                            JSON.stringify(row[header] || '')
                        ).join(',')
                    )
                ].join('\n');

                return csvContent;
            }

            // 转换为Markdown
            toMarkdown(content) {
                if (typeof content === 'object') {
                    return '```json\n' + JSON.stringify(content, null, 2) + '\n```';
                }
                return content;
            }

            // 跟踪复制事件
            trackCopyEvent(type, details = null) {
                const event = {
                    type: 'copy_button_' + type,
                    timestamp: Date.now(),
                    contentType: typeof this.props.content,
                    contentLength: typeof this.props.content === 'string' 
                        ? this.props.content.length 
                        : JSON.stringify(this.props.content).length,
                    copyCount: this.enhancedState.copyCount,
                    details
                };

                // 发送到分析服务
                if (window.analytics) {
                    window.analytics.track(event);
                }
            }

            // 获取复制统计
            getCopyStats() {
                return {
                    copyCount: this.enhancedState.copyCount,
                    lastCopyTime: this.enhancedState.lastCopyTime,
                    isProcessing: this.enhancedState.isProcessing
                };
            }

            // 重置统计
            resetStats() {
                this.enhancedState.copyCount = 0;
                this.enhancedState.lastCopyTime = null;
            }
        }

        return AdvancedCopyButton;
    }
};

// 使用示例
const AdvancedCopyButton = EnhancedCopyButton.createAdvancedCopyButton();

// 带确认的复制按钮
<AdvancedCopyButton
    content={sensitiveData}
    successText="Sensitive data copied"
    confirmBeforeCopy={true}
    analytics={true}
    onCopySuccess={(data) => {
        console.log('Copy successful:', data);
    }}
    onCopyError={(error) => {
        this.notification.add('Copy failed: ' + error.message, { type: 'danger' });
    }}
/>

// 带预览的复制按钮
<AdvancedCopyButton
    content={largeDataSet}
    successText="Dataset copied"
    showPreview={true}
    copyFormat="csv"
    maxLength={10000}
    className="btn btn-info"
/>
```

## 技术特点

### 1. 现代API支持
- 使用Clipboard API
- 支持文本和对象复制
- 异步操作处理

### 2. 用户体验
- 即时成功反馈
- 错误处理和提示
- 自动关闭的工具提示

### 3. 灵活配置
- 可定制的样式和图标
- 支持禁用状态
- 可配置的提示文本

### 4. 浏览器兼容
- 基于标准Clipboard API
- 优雅的错误降级
- 跨浏览器支持

## 设计模式

### 1. 组件模式 (Component Pattern)
- 封装复制功能
- 可重用的UI组件

### 2. 策略模式 (Strategy Pattern)
- 不同内容类型的复制策略
- 可配置的复制行为

### 3. 观察者模式 (Observer Pattern)
- 复制事件的通知机制
- 用户反馈系统

## 注意事项

1. **浏览器支持**: 确保目标浏览器支持Clipboard API
2. **权限处理**: 处理剪贴板权限被拒绝的情况
3. **内容安全**: 注意复制敏感数据的安全性
4. **用户反馈**: 提供清晰的操作反馈

## 扩展建议

1. **格式支持**: 支持更多复制格式（HTML、RTF等）
2. **批量复制**: 支持批量内容的复制
3. **历史记录**: 维护复制历史记录
4. **快捷键**: 支持键盘快捷键
5. **拖拽支持**: 支持拖拽复制功能

该复制按钮组件为Odoo Web应用提供了便捷的数据复制功能，通过现代浏览器API和完善的用户反馈确保了优秀的用户体验。

# @web/core/browser/title_service.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/browser/title_service.js`
- **原始路径**: `/web/static/src/core/browser/title_service.js`
- **代码行数**: 42行
- **作用**: 提供页面标题管理服务，支持动态标题组合和更新

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 服务架构的设计模式和实现原理
- 页面标题的动态管理策略
- 组件化标题系统的设计思想
- 浏览器API的封装和抽象
- 企业级应用的用户体验优化

## 📚 核心概念

### 什么是标题服务？
标题服务是一个**页面标题管理系统**，主要功能：
- **动态标题**: 根据应用状态动态更新页面标题
- **组件化**: 将标题分解为多个可管理的部分
- **优先级**: 支持不同组件设置标题的不同部分
- **用户体验**: 提供清晰的页面标识和导航信息

### 核心架构组成
```javascript
const titleService = {
    start() {
        return {
            current,        // 获取当前标题
            getParts(),     // 获取标题部分
            setParts(parts) // 设置标题部分
        };
    }
};

// 标题部分结构
const titleParts = {
    app: "Odoo",           // 应用名称
    module: "Sales",       // 模块名称
    action: "Orders",      // 动作名称
    record: "SO001"        // 记录标识
};

// 最终标题: "SO001 - Orders - Sales - Odoo"
```

### 基本使用模式
```javascript
import { useService } from '@web/core/utils/hooks';

// 在组件中使用标题服务
class MyComponent extends Component {
    setup() {
        this.titleService = useService('title');
        
        // 设置标题部分
        this.titleService.setParts({
            module: 'Sales',
            action: 'Orders'
        });
    }
    
    onRecordChange(record) {
        // 动态更新标题
        this.titleService.setParts({
            record: record.name
        });
    }
    
    onWillUnmount() {
        // 清理标题部分
        this.titleService.setParts({
            module: null,
            action: null,
            record: null
        });
    }
}
```

## 🔍 核心实现详解

### 1. 服务初始化和状态管理

#### 标题部分存储
```javascript
function start() {
    const titleParts = {};
    
    function getParts() {
        return Object.assign({}, titleParts);
    }
    
    function setParts(parts) {
        for (const key in parts) {
            const val = parts[key];
            if (!val) {
                delete titleParts[key];
            } else {
                titleParts[key] = val;
            }
        }
        document.title = Object.values(titleParts).join(" - ") || "Odoo";
    }
    
    return {
        get current() {
            return document.title;
        },
        getParts,
        setParts,
    };
}
```

**状态管理特点**：
- **内部状态**: 使用闭包维护私有的titleParts对象
- **不可变返回**: getParts返回副本，避免外部修改
- **自动清理**: 空值自动从titleParts中删除
- **即时更新**: setParts立即更新document.title

#### 标题组合算法
```javascript
document.title = Object.values(titleParts).join(" - ") || "Odoo";
```

**组合策略**：
- **分隔符**: 使用" - "连接各个标题部分
- **顺序**: 按照对象属性的插入顺序组合
- **默认值**: 无标题部分时显示"Odoo"
- **空值过滤**: 自动过滤掉空值和null值

### 2. API设计和接口

#### current属性 - 当前标题获取
```javascript
get current() {
    return document.title;
}
```

**设计优势**：
- **实时性**: 直接返回当前的document.title
- **一致性**: 确保返回值与浏览器标题栏一致
- **简洁性**: 提供简单的只读访问接口

#### getParts方法 - 标题部分获取
```javascript
function getParts() {
    return Object.assign({}, titleParts);
}
```

**方法特点**：
- **不可变性**: 返回副本，防止外部修改内部状态
- **完整性**: 返回所有当前的标题部分
- **调试友好**: 便于调试和状态检查

#### setParts方法 - 标题部分设置
```javascript
function setParts(parts) {
    for (const key in parts) {
        const val = parts[key];
        if (!val) {
            delete titleParts[key];
        } else {
            titleParts[key] = val;
        }
    }
    document.title = Object.values(titleParts).join(" - ") || "Odoo";
}
```

**方法设计**：
- **批量更新**: 支持一次设置多个标题部分
- **增量更新**: 只更新传入的部分，保留其他部分
- **自动清理**: null或空值自动删除对应部分
- **即时生效**: 立即更新浏览器标题

## 🎨 实际应用场景

### 1. 高级标题管理系统
```javascript
class AdvancedTitleManager {
    constructor(titleService) {
        this.titleService = titleService;
        this.titleStack = [];
        this.titleTemplates = new Map();
        this.titleHistory = [];
        this.maxHistorySize = 50;
        
        this.setupTitleTemplates();
    }
    
    setupTitleTemplates() {
        // 定义不同场景的标题模板
        this.titleTemplates.set('list', {
            pattern: '{module} - {action}',
            priority: ['module', 'action', 'app']
        });
        
        this.titleTemplates.set('form', {
            pattern: '{record} - {action} - {module}',
            priority: ['record', 'action', 'module', 'app']
        });
        
        this.titleTemplates.set('dashboard', {
            pattern: '{dashboard} - {module}',
            priority: ['dashboard', 'module', 'app']
        });
        
        this.titleTemplates.set('report', {
            pattern: '{report} - {module}',
            priority: ['report', 'module', 'app']
        });
    }
    
    // 推送标题上下文
    pushContext(context, template = 'default') {
        const titleContext = {
            id: this.generateContextId(),
            context,
            template,
            timestamp: Date.now(),
            previousTitle: this.titleService.current
        };
        
        this.titleStack.push(titleContext);
        this.applyContext(titleContext);
        this.recordTitleChange(titleContext);
        
        return titleContext.id;
    }
    
    // 弹出标题上下文
    popContext(contextId) {
        const index = this.titleStack.findIndex(ctx => ctx.id === contextId);
        if (index === -1) {
            console.warn(`Title context ${contextId} not found`);
            return;
        }
        
        // 移除指定上下文及其之后的所有上下文
        const removedContexts = this.titleStack.splice(index);
        
        // 应用栈顶上下文或清空标题
        if (this.titleStack.length > 0) {
            this.applyContext(this.titleStack[this.titleStack.length - 1]);
        } else {
            this.titleService.setParts({});
        }
        
        return removedContexts;
    }
    
    // 应用标题上下文
    applyContext(titleContext) {
        const { context, template } = titleContext;
        const templateConfig = this.titleTemplates.get(template);
        
        if (templateConfig) {
            this.applyTemplate(context, templateConfig);
        } else {
            // 默认应用所有上下文
            this.titleService.setParts(context);
        }
    }
    
    // 应用标题模板
    applyTemplate(context, templateConfig) {
        const { pattern, priority } = templateConfig;
        
        // 按优先级过滤和排序上下文
        const filteredContext = {};
        for (const key of priority) {
            if (context[key]) {
                filteredContext[key] = context[key];
            }
        }
        
        this.titleService.setParts(filteredContext);
    }
    
    // 生成上下文ID
    generateContextId() {
        return `ctx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    // 记录标题变化
    recordTitleChange(titleContext) {
        this.titleHistory.push({
            title: this.titleService.current,
            context: titleContext.context,
            template: titleContext.template,
            timestamp: titleContext.timestamp
        });
        
        // 限制历史记录大小
        if (this.titleHistory.length > this.maxHistorySize) {
            this.titleHistory.shift();
        }
    }
    
    // 智能标题建议
    suggestTitle(context) {
        const suggestions = [];
        
        for (const [templateName, templateConfig] of this.titleTemplates.entries()) {
            const score = this.calculateTemplateScore(context, templateConfig);
            if (score > 0) {
                suggestions.push({
                    template: templateName,
                    score,
                    preview: this.previewTitle(context, templateConfig)
                });
            }
        }
        
        return suggestions.sort((a, b) => b.score - a.score);
    }
    
    calculateTemplateScore(context, templateConfig) {
        const { priority } = templateConfig;
        let score = 0;
        
        for (let i = 0; i < priority.length; i++) {
            const key = priority[i];
            if (context[key]) {
                // 优先级越高，权重越大
                score += (priority.length - i) * 10;
            }
        }
        
        return score;
    }
    
    previewTitle(context, templateConfig) {
        const { priority } = templateConfig;
        const parts = [];
        
        for (const key of priority) {
            if (context[key]) {
                parts.push(context[key]);
            }
        }
        
        return parts.join(' - ') || 'Odoo';
    }
    
    // 标题历史分析
    analyzeTitleHistory() {
        const analysis = {
            totalChanges: this.titleHistory.length,
            uniqueTitles: new Set(this.titleHistory.map(h => h.title)).size,
            mostUsedTemplates: this.getMostUsedTemplates(),
            averageSessionLength: this.calculateAverageSessionLength(),
            titlePatterns: this.analyzeTitlePatterns()
        };
        
        return analysis;
    }
    
    getMostUsedTemplates() {
        const templateCounts = {};
        this.titleHistory.forEach(entry => {
            const template = entry.template || 'default';
            templateCounts[template] = (templateCounts[template] || 0) + 1;
        });
        
        return Object.entries(templateCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([template, count]) => ({ template, count }));
    }
    
    calculateAverageSessionLength() {
        if (this.titleHistory.length < 2) return 0;
        
        const sessions = [];
        let sessionStart = this.titleHistory[0].timestamp;
        
        for (let i = 1; i < this.titleHistory.length; i++) {
            const current = this.titleHistory[i];
            const previous = this.titleHistory[i - 1];
            
            // 如果间隔超过30分钟，认为是新会话
            if (current.timestamp - previous.timestamp > 30 * 60 * 1000) {
                sessions.push(previous.timestamp - sessionStart);
                sessionStart = current.timestamp;
            }
        }
        
        // 添加最后一个会话
        sessions.push(this.titleHistory[this.titleHistory.length - 1].timestamp - sessionStart);
        
        const totalTime = sessions.reduce((sum, time) => sum + time, 0);
        return sessions.length > 0 ? totalTime / sessions.length : 0;
    }
    
    analyzeTitlePatterns() {
        const patterns = {};
        
        this.titleHistory.forEach(entry => {
            const parts = entry.title.split(' - ');
            const pattern = parts.map(() => 'X').join(' - ');
            patterns[pattern] = (patterns[pattern] || 0) + 1;
        });
        
        return Object.entries(patterns)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10)
            .map(([pattern, count]) => ({ pattern, count }));
    }
    
    // 导出和导入标题配置
    exportConfiguration() {
        return {
            templates: Array.from(this.titleTemplates.entries()),
            history: this.titleHistory.slice(-20), // 只导出最近20条
            currentStack: this.titleStack.map(ctx => ({
                context: ctx.context,
                template: ctx.template
            }))
        };
    }
    
    importConfiguration(config) {
        if (config.templates) {
            this.titleTemplates.clear();
            config.templates.forEach(([name, template]) => {
                this.titleTemplates.set(name, template);
            });
        }
        
        if (config.history) {
            this.titleHistory = [...config.history];
        }
        
        if (config.currentStack) {
            this.titleStack = [];
            config.currentStack.forEach(({ context, template }) => {
                this.pushContext(context, template);
            });
        }
    }
    
    // 获取当前状态
    getCurrentState() {
        return {
            currentTitle: this.titleService.current,
            titleParts: this.titleService.getParts(),
            stackDepth: this.titleStack.length,
            topContext: this.titleStack[this.titleStack.length - 1] || null
        };
    }
}

// 使用示例
class TitleAwareComponent extends Component {
    setup() {
        this.titleService = useService('title');
        this.titleManager = new AdvancedTitleManager(this.titleService);
        
        // 推送组件的标题上下文
        this.titleContextId = this.titleManager.pushContext({
            module: 'Sales',
            action: 'Orders',
            view: 'List'
        }, 'list');
    }
    
    onRecordSelect(record) {
        // 切换到表单视图的标题上下文
        this.titleManager.popContext(this.titleContextId);
        this.titleContextId = this.titleManager.pushContext({
            module: 'Sales',
            action: 'Orders',
            record: record.name
        }, 'form');
    }
    
    onWillUnmount() {
        // 清理标题上下文
        this.titleManager.popContext(this.titleContextId);
    }
}
```

### 2. 标题国际化和本地化系统
```javascript
class InternationalizedTitleService {
    constructor(titleService, translationService) {
        this.titleService = titleService;
        this.translationService = translationService;
        this.titleTranslations = new Map();
        this.currentLocale = 'en_US';
        
        this.setupTranslations();
    }
    
    setupTranslations() {
        // 设置标题翻译
        this.titleTranslations.set('en_US', {
            app: 'Odoo',
            modules: {
                'sale': 'Sales',
                'purchase': 'Purchase',
                'account': 'Accounting',
                'hr': 'Human Resources'
            },
            actions: {
                'orders': 'Orders',
                'invoices': 'Invoices',
                'employees': 'Employees'
            },
            views: {
                'list': 'List',
                'form': 'Form',
                'kanban': 'Kanban'
            }
        });
        
        this.titleTranslations.set('zh_CN', {
            app: 'Odoo',
            modules: {
                'sale': '销售',
                'purchase': '采购',
                'account': '会计',
                'hr': '人力资源'
            },
            actions: {
                'orders': '订单',
                'invoices': '发票',
                'employees': '员工'
            },
            views: {
                'list': '列表',
                'form': '表单',
                'kanban': '看板'
            }
        });
        
        this.titleTranslations.set('fr_FR', {
            app: 'Odoo',
            modules: {
                'sale': 'Ventes',
                'purchase': 'Achats',
                'account': 'Comptabilité',
                'hr': 'Ressources Humaines'
            },
            actions: {
                'orders': 'Commandes',
                'invoices': 'Factures',
                'employees': 'Employés'
            },
            views: {
                'list': 'Liste',
                'form': 'Formulaire',
                'kanban': 'Kanban'
            }
        });
    }
    
    // 设置本地化标题
    setLocalizedParts(parts) {
        const localizedParts = {};
        const translations = this.titleTranslations.get(this.currentLocale) || {};
        
        for (const [key, value] of Object.entries(parts)) {
            if (value) {
                localizedParts[key] = this.translateTitlePart(key, value, translations);
            } else {
                localizedParts[key] = null;
            }
        }
        
        this.titleService.setParts(localizedParts);
    }
    
    translateTitlePart(key, value, translations) {
        // 尝试从不同的翻译类别中查找
        const categories = ['modules', 'actions', 'views'];
        
        for (const category of categories) {
            if (translations[category] && translations[category][value]) {
                return translations[category][value];
            }
        }
        
        // 如果没有找到翻译，尝试使用翻译服务
        const translated = this.translationService.translate(value);
        if (translated !== value) {
            return translated;
        }
        
        // 返回原始值
        return value;
    }
    
    // 切换语言
    switchLocale(locale) {
        this.currentLocale = locale;
        
        // 重新应用当前标题部分
        const currentParts = this.titleService.getParts();
        this.setLocalizedParts(currentParts);
    }
    
    // 添加翻译
    addTranslations(locale, translations) {
        if (!this.titleTranslations.has(locale)) {
            this.titleTranslations.set(locale, {});
        }
        
        const existingTranslations = this.titleTranslations.get(locale);
        Object.assign(existingTranslations, translations);
    }
    
    // 获取支持的语言
    getSupportedLocales() {
        return Array.from(this.titleTranslations.keys());
    }
    
    // 获取当前语言的翻译
    getCurrentTranslations() {
        return this.titleTranslations.get(this.currentLocale) || {};
    }
}

// 标题格式化服务
class TitleFormattingService {
    constructor(titleService) {
        this.titleService = titleService;
        this.formatters = new Map();
        this.setupDefaultFormatters();
    }
    
    setupDefaultFormatters() {
        // 默认格式化器
        this.formatters.set('default', (parts) => {
            return Object.values(parts).join(' - ') || 'Odoo';
        });
        
        // 层次结构格式化器
        this.formatters.set('hierarchical', (parts) => {
            const hierarchy = ['record', 'action', 'module', 'app'];
            const orderedParts = [];
            
            for (const key of hierarchy) {
                if (parts[key]) {
                    orderedParts.push(parts[key]);
                }
            }
            
            return orderedParts.join(' › ') || 'Odoo';
        });
        
        // 简洁格式化器
        this.formatters.set('compact', (parts) => {
            if (parts.record) {
                return `${parts.record} | ${parts.app || 'Odoo'}`;
            } else if (parts.action) {
                return `${parts.action} | ${parts.app || 'Odoo'}`;
            } else {
                return parts.app || 'Odoo';
            }
        });
        
        // 详细格式化器
        this.formatters.set('verbose', (parts) => {
            const segments = [];
            
            if (parts.record) {
                segments.push(`Record: ${parts.record}`);
            }
            if (parts.action) {
                segments.push(`Action: ${parts.action}`);
            }
            if (parts.module) {
                segments.push(`Module: ${parts.module}`);
            }
            
            segments.push(parts.app || 'Odoo');
            return segments.join(' | ');
        });
    }
    
    // 设置格式化的标题部分
    setFormattedParts(parts, formatter = 'default') {
        const formatterFn = this.formatters.get(formatter);
        if (!formatterFn) {
            console.warn(`Formatter '${formatter}' not found, using default`);
            return this.setFormattedParts(parts, 'default');
        }
        
        const formattedTitle = formatterFn(parts);
        document.title = formattedTitle;
    }
    
    // 添加自定义格式化器
    addFormatter(name, formatterFn) {
        this.formatters.set(name, formatterFn);
    }
    
    // 预览格式化结果
    previewFormat(parts, formatter = 'default') {
        const formatterFn = this.formatters.get(formatter);
        return formatterFn ? formatterFn(parts) : 'Invalid formatter';
    }
    
    // 获取所有格式化器
    getAvailableFormatters() {
        return Array.from(this.formatters.keys());
    }
}

// 使用示例
class LocalizedTitleComponent extends Component {
    setup() {
        this.titleService = useService('title');
        this.translationService = useService('translation');
        
        this.i18nTitleService = new InternationalizedTitleService(
            this.titleService,
            this.translationService
        );
        
        this.formattingService = new TitleFormattingService(this.titleService);
        
        // 设置本地化标题
        this.i18nTitleService.setLocalizedParts({
            module: 'sale',
            action: 'orders',
            view: 'list'
        });
    }
    
    onLanguageChange(locale) {
        this.i18nTitleService.switchLocale(locale);
    }
    
    onFormatChange(formatter) {
        const parts = this.titleService.getParts();
        this.formattingService.setFormattedParts(parts, formatter);
    }
}
```

## 🔧 调试技巧

### 标题状态查看
```javascript
function debugTitleService() {
    const titleService = odoo.__DEBUG__.services.title;
    
    console.group('📄 Title Service Debug');
    console.log('Current Title:', titleService.current);
    console.log('Title Parts:', titleService.getParts());
    console.log('Document Title:', document.title);
    console.groupEnd();
}

// 在控制台中调用
debugTitleService();
```

### 标题变化监控
```javascript
function monitorTitleChanges() {
    const originalSetParts = titleService.setParts;
    
    titleService.setParts = function(parts) {
        console.log('🔄 Title Parts Changed:', {
            before: this.getParts(),
            changes: parts,
            after: { ...this.getParts(), ...parts }
        });
        
        return originalSetParts.call(this, parts);
    };
    
    console.log('Title change monitoring enabled');
}

// 启用监控
monitorTitleChanges();
```

## 📊 性能考虑

### 优化策略
1. **批量更新**: 一次性设置多个标题部分
2. **避免频繁更新**: 合理控制标题更新频率
3. **内存管理**: 及时清理不需要的标题部分
4. **缓存机制**: 缓存翻译和格式化结果

### 最佳实践
```javascript
// ✅ 好的做法：批量设置标题部分
titleService.setParts({
    module: 'Sales',
    action: 'Orders',
    record: 'SO001'
});

// ❌ 不好的做法：多次单独设置
titleService.setParts({ module: 'Sales' });
titleService.setParts({ action: 'Orders' });
titleService.setParts({ record: 'SO001' });

// ✅ 好的做法：组件卸载时清理
onWillUnmount() {
    titleService.setParts({
        module: null,
        action: null,
        record: null
    });
}

// ❌ 不好的做法：忘记清理标题部分
onWillUnmount() {
    // 没有清理标题，可能影响其他组件
}
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解服务架构的设计模式和实现原理
- [ ] 掌握页面标题的动态管理策略
- [ ] 理解组件化标题系统的设计思想
- [ ] 能够实现浏览器API的封装和抽象
- [ ] 掌握企业级应用的用户体验优化
- [ ] 了解标题国际化和格式化技术

## 🚀 下一步学习
学完标题服务后，建议继续学习：
1. **服务注册表** (`@web/core/registry.js`) - 学习服务注册机制
2. **钩子系统** (`@web/core/utils/hooks.js`) - 理解服务使用方式
3. **国际化系统** (`@web/core/l10n/`) - 掌握多语言支持

## 💡 重要提示
- 标题服务是用户体验的重要组成部分
- 合理的标题结构有助于用户导航和理解
- 组件化设计使标题管理更加灵活
- 国际化支持对全球化应用很重要

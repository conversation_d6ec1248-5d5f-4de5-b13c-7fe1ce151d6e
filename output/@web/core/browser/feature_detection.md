# @web/core/browser/feature_detection.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/browser/feature_detection.js`
- **原始路径**: `/web/static/src/core/browser/feature_detection.js`
- **代码行数**: 74行
- **作用**: 提供浏览器和设备特性检测功能，支持跨平台兼容性判断

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 浏览器特性检测的原理和最佳实践
- User Agent字符串解析技巧
- 移动设备和桌面设备的识别方法
- 渐进式增强的设计思想
- 跨平台兼容性的处理策略

## 📚 核心概念

### 什么是特性检测？
特性检测是一种**渐进式增强技术**，主要功能：
- **环境识别**: 识别浏览器类型、操作系统和设备特性
- **功能检测**: 检测浏览器支持的API和功能
- **适配策略**: 根据检测结果提供不同的用户体验
- **兼容处理**: 为不同环境提供合适的降级方案

### 核心架构组成
```javascript
// 浏览器检测
isBrowserChrome()    // Chrome系浏览器
isBrowserFirefox()   // Firefox浏览器
isBrowserSafari()    // Safari系浏览器

// 操作系统检测
isAndroid()          // Android系统
isIOS()              // iOS系统
isMacOS()            // macOS系统
isMobileOS()         // 移动操作系统

// 应用环境检测
isIosApp()           // iOS应用内
isAndroidApp()       // Android应用内
isDisplayStandalone() // PWA独立模式

// 交互特性检测
hasTouch()           // 触摸支持
maxTouchPoints()     // 最大触摸点数
```

### 基本使用模式
```javascript
import {
    isBrowserChrome,
    isMobileOS,
    hasTouch,
    isDisplayStandalone
} from '@web/core/browser/feature_detection';

// 根据浏览器类型调整样式
if (isBrowserChrome()) {
    document.body.classList.add('chrome-browser');
}

// 移动设备适配
if (isMobileOS()) {
    document.body.classList.add('mobile-device');
    // 启用移动端特定功能
    enableMobileFeatures();
}

// 触摸设备优化
if (hasTouch()) {
    // 增大点击区域
    document.body.classList.add('touch-device');
}

// PWA模式检测
if (isDisplayStandalone()) {
    // 隐藏浏览器相关UI
    hideBrowserUI();
}
```

## 🔍 核心实现详解

### 1. 浏览器检测算法

#### Chrome系浏览器检测
```javascript
function isBrowserChrome() {
    return /Chrome/i.test(browser.navigator.userAgent);
}
```

**检测策略**：
- **User Agent匹配**: 使用正则表达式匹配"Chrome"字符串
- **大小写不敏感**: 使用`/i`标志忽略大小写
- **广泛覆盖**: 包括Chrome、Edge、Opera等Chromium内核浏览器
- **简单高效**: 单一条件判断，性能优秀

#### Safari浏览器检测
```javascript
function isBrowserSafari() {
    return !isBrowserChrome() && browser.navigator.userAgent.includes("Safari");
}
```

**检测逻辑**：
- **排除法**: 先排除Chrome系浏览器（Chrome的UA也包含Safari）
- **正向匹配**: 检查UA中是否包含"Safari"
- **精确识别**: 避免Chrome浏览器被误识别为Safari
- **兼容性**: 支持Safari和基于WebKit的其他浏览器

#### Firefox浏览器检测
```javascript
function isBrowserFirefox() {
    return /Firefox/i.test(browser.navigator.userAgent);
}
```

**检测特点**：
- **独特标识**: Firefox有独特的UA标识
- **简单匹配**: 直接匹配"Firefox"字符串
- **稳定可靠**: Firefox的UA格式相对稳定

### 2. 操作系统检测

#### iOS设备检测
```javascript
function isIOS() {
    return (
        /(iPad|iPhone|iPod)/i.test(browser.navigator.userAgent) ||
        (browser.navigator.platform === "MacIntel" && maxTouchPoints() > 1)
    );
}
```

**检测策略分析**：
- **传统检测**: 匹配iPad、iPhone、iPod等设备标识
- **新iPad检测**: iOS 13+的iPad在桌面模式下UA显示为MacIntel
- **触摸点判断**: 通过maxTouchPoints > 1识别触摸设备
- **双重保险**: 结合UA和硬件特性确保准确性

#### Android设备检测
```javascript
function isAndroid() {
    return /Android/i.test(browser.navigator.userAgent);
}
```

**检测原理**：
- **标准标识**: Android设备UA中都包含"Android"
- **统一格式**: Android UA格式相对统一
- **简单可靠**: 单一条件即可准确识别

#### 移动操作系统综合检测
```javascript
function isMobileOS() {
    return isAndroid() || isIOS() || isOtherMobileOS();
}

function isOtherMobileOS() {
    return /(webOS|BlackBerry|Windows Phone)/i.test(browser.navigator.userAgent);
}
```

**综合策略**：
- **全面覆盖**: 包含主流和小众移动操作系统
- **模块化**: 分别检测不同系统，便于维护
- **扩展性**: 易于添加新的移动系统支持

### 3. 应用环境检测

#### Odoo移动应用检测
```javascript
function isIosApp() {
    return /OdooMobile \(iOS\)/i.test(browser.navigator.userAgent);
}

function isAndroidApp() {
    return /OdooMobile.+Android/i.test(browser.navigator.userAgent);
}
```

**应用检测特点**：
- **自定义UA**: Odoo移动应用使用自定义User Agent
- **平台区分**: 分别识别iOS和Android应用
- **精确匹配**: 使用特定的正则表达式确保准确性
- **业务相关**: 针对Odoo生态系统的特定需求

#### PWA独立模式检测
```javascript
function isDisplayStandalone() {
    return browser.matchMedia("(display-mode: standalone)").matches;
}
```

**PWA检测原理**：
- **媒体查询**: 使用CSS媒体查询检测显示模式
- **标准API**: 基于Web标准的检测方法
- **实时检测**: 可以动态检测模式变化
- **现代特性**: 支持PWA的现代浏览器特性

### 4. 交互特性检测

#### 触摸支持检测
```javascript
function hasTouch() {
    return browser.ontouchstart !== undefined || browser.matchMedia("(pointer:coarse)").matches;
}
```

**触摸检测策略**：
- **事件检测**: 检查ontouchstart事件是否存在
- **指针类型**: 使用媒体查询检测粗糙指针（触摸）
- **双重验证**: 结合两种方法提高准确性
- **现代标准**: 优先使用标准的媒体查询方法

#### 触摸点数检测
```javascript
function maxTouchPoints() {
    return browser.navigator.maxTouchPoints || 1;
}
```

**触摸点检测**：
- **硬件特性**: 检测设备支持的最大同时触摸点数
- **默认值**: 不支持时返回1（单点触摸）
- **多点触摸**: 支持多点触摸手势的检测基础
- **设备能力**: 反映设备的触摸交互能力

## 🎨 实际应用场景

### 1. 响应式UI适配系统
```javascript
class ResponsiveUIAdapter {
    constructor() {
        this.deviceInfo = this.detectDeviceInfo();
        this.setupAdaptations();
    }

    detectDeviceInfo() {
        return {
            // 浏览器信息
            browser: {
                isChrome: isBrowserChrome(),
                isFirefox: isBrowserFirefox(),
                isSafari: isBrowserSafari()
            },

            // 操作系统信息
            os: {
                isAndroid: isAndroid(),
                isIOS: isIOS(),
                isMacOS: isMacOS(),
                isMobile: isMobileOS()
            },

            // 交互特性
            interaction: {
                hasTouch: hasTouch(),
                maxTouchPoints: maxTouchPoints(),
                isStandalone: isDisplayStandalone()
            },

            // 应用环境
            app: {
                isIosApp: isIosApp(),
                isAndroidApp: isAndroidApp()
            }
        };
    }

    setupAdaptations() {
        this.adaptLayout();
        this.adaptInteraction();
        this.adaptPerformance();
        this.adaptFeatures();
    }

    adaptLayout() {
        const { os, interaction } = this.deviceInfo;

        // 移动设备布局适配
        if (os.isMobile) {
            document.body.classList.add('mobile-layout');

            // iOS特殊适配
            if (os.isIOS) {
                document.body.classList.add('ios-layout');
                this.handleIOSSafeArea();
            }

            // Android特殊适配
            if (os.isAndroid) {
                document.body.classList.add('android-layout');
                this.handleAndroidKeyboard();
            }
        }

        // 触摸设备适配
        if (interaction.hasTouch) {
            document.body.classList.add('touch-layout');
            this.enlargeTouchTargets();
        }

        // PWA独立模式适配
        if (interaction.isStandalone) {
            document.body.classList.add('standalone-layout');
            this.hideBrowserChrome();
        }
    }

    adaptInteraction() {
        const { interaction, os } = this.deviceInfo;

        // 触摸交互优化
        if (interaction.hasTouch) {
            this.enableTouchGestures();
            this.optimizeScrolling();

            // 多点触摸支持
            if (interaction.maxTouchPoints > 1) {
                this.enableMultiTouch();
            }
        }

        // 移动设备交互优化
        if (os.isMobile) {
            this.enableMobileNavigation();
            this.optimizeFormInputs();
        }
    }

    adaptPerformance() {
        const { browser, os } = this.deviceInfo;

        // 浏览器性能优化
        if (browser.isSafari) {
            this.applySafariOptimizations();
        }

        if (browser.isFirefox) {
            this.applyFirefoxOptimizations();
        }

        // 移动设备性能优化
        if (os.isMobile) {
            this.reduceMobileAnimations();
            this.optimizeMobileImages();
        }
    }

    adaptFeatures() {
        const { app, os, browser } = this.deviceInfo;

        // 应用内特性
        if (app.isIosApp || app.isAndroidApp) {
            this.enableAppFeatures();
            this.hideWebFeatures();
        }

        // 平台特定功能
        if (os.isIOS && browser.isSafari) {
            this.enableIOSWebFeatures();
        }

        if (os.isAndroid && browser.isChrome) {
            this.enableAndroidWebFeatures();
        }
    }

    // iOS安全区域处理
    handleIOSSafeArea() {
        const style = document.createElement('style');
        style.textContent = `
            .ios-layout {
                padding-top: env(safe-area-inset-top);
                padding-bottom: env(safe-area-inset-bottom);
                padding-left: env(safe-area-inset-left);
                padding-right: env(safe-area-inset-right);
            }
        `;
        document.head.appendChild(style);
    }

    // Android键盘处理
    handleAndroidKeyboard() {
        let initialViewportHeight = window.innerHeight;

        window.addEventListener('resize', () => {
            const currentHeight = window.innerHeight;
            const heightDifference = initialViewportHeight - currentHeight;

            if (heightDifference > 150) { // 键盘可能打开
                document.body.classList.add('keyboard-open');
            } else {
                document.body.classList.remove('keyboard-open');
            }
        });
    }

    // 放大触摸目标
    enlargeTouchTargets() {
        const style = document.createElement('style');
        style.textContent = `
            .touch-layout button,
            .touch-layout .clickable {
                min-height: 44px;
                min-width: 44px;
                padding: 12px;
            }
        `;
        document.head.appendChild(style);
    }

    // 启用触摸手势
    enableTouchGestures() {
        // 实现滑动手势
        let startX, startY;

        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });

        document.addEventListener('touchend', (e) => {
            if (!startX || !startY) return;

            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;

            const deltaX = endX - startX;
            const deltaY = endY - startY;

            // 检测滑动方向
            if (Math.abs(deltaX) > Math.abs(deltaY)) {
                if (deltaX > 50) {
                    this.handleSwipeRight();
                } else if (deltaX < -50) {
                    this.handleSwipeLeft();
                }
            }
        });
    }

    // Safari优化
    applySafariOptimizations() {
        // 禁用Safari的弹性滚动
        document.body.style.overscrollBehavior = 'none';

        // 优化Safari的渲染性能
        const style = document.createElement('style');
        style.textContent = `
            * {
                -webkit-transform: translateZ(0);
                -webkit-backface-visibility: hidden;
            }
        `;
        document.head.appendChild(style);
    }

    // 移动端动画优化
    reduceMobileAnimations() {
        const style = document.createElement('style');
        style.textContent = `
            .mobile-layout * {
                animation-duration: 0.2s !important;
                transition-duration: 0.2s !important;
            }
        `;
        document.head.appendChild(style);
    }

    // 获取设备信息摘要
    getDeviceSummary() {
        const { browser, os, interaction, app } = this.deviceInfo;

        return {
            platform: os.isMobile ? 'mobile' : 'desktop',
            browser: Object.keys(browser).find(key => browser[key]) || 'unknown',
            os: Object.keys(os).find(key => os[key]) || 'unknown',
            touchSupport: interaction.hasTouch,
            appEnvironment: app.isIosApp || app.isAndroidApp,
            standaloneMode: interaction.isStandalone
        };
    }

    // 生成适配报告
    generateAdaptationReport() {
        const summary = this.getDeviceSummary();

        console.group('📱 Device Adaptation Report');
        console.log('Platform:', summary.platform);
        console.log('Browser:', summary.browser);
        console.log('OS:', summary.os);
        console.log('Touch Support:', summary.touchSupport);
        console.log('App Environment:', summary.appEnvironment);
        console.log('Standalone Mode:', summary.standaloneMode);
        console.groupEnd();

        return summary;
    }
}

// 使用示例
const uiAdapter = new ResponsiveUIAdapter();
const deviceSummary = uiAdapter.generateAdaptationReport();
```

### 2. 功能降级和渐进增强系统
```javascript
class FeatureEnhancementManager {
    constructor() {
        this.features = this.detectFeatures();
        this.setupEnhancements();
    }

    detectFeatures() {
        return {
            // 基础特性
            basic: {
                touch: hasTouch(),
                mobile: isMobileOS(),
                standalone: isDisplayStandalone()
            },

            // 浏览器特性
            browser: {
                chrome: isBrowserChrome(),
                firefox: isBrowserFirefox(),
                safari: isBrowserSafari()
            },

            // 高级特性检测
            advanced: {
                serviceWorker: 'serviceWorker' in navigator,
                pushNotifications: 'PushManager' in window,
                webShare: 'share' in navigator,
                webRTC: 'RTCPeerConnection' in window,
                webGL: this.detectWebGL(),
                webAssembly: 'WebAssembly' in window,
                intersectionObserver: 'IntersectionObserver' in window,
                resizeObserver: 'ResizeObserver' in window
            }
        };
    }

    detectWebGL() {
        try {
            const canvas = document.createElement('canvas');
            return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
        } catch {
            return false;
        }
    }

    setupEnhancements() {
        this.setupBasicEnhancements();
        this.setupAdvancedEnhancements();
        this.setupFallbacks();
    }

    setupBasicEnhancements() {
        const { basic, browser } = this.features;

        // 触摸增强
        if (basic.touch) {
            this.enableTouchEnhancements();
        }

        // 移动端增强
        if (basic.mobile) {
            this.enableMobileEnhancements();
        }

        // PWA增强
        if (basic.standalone) {
            this.enablePWAEnhancements();
        }

        // 浏览器特定增强
        if (browser.safari) {
            this.enableSafariEnhancements();
        }
    }

    setupAdvancedEnhancements() {
        const { advanced } = this.features;

        // Service Worker增强
        if (advanced.serviceWorker) {
            this.enableOfflineSupport();
        }

        // 推送通知增强
        if (advanced.pushNotifications) {
            this.enablePushNotifications();
        }

        // Web Share增强
        if (advanced.webShare) {
            this.enableNativeSharing();
        }

        // 交叉观察器增强
        if (advanced.intersectionObserver) {
            this.enableLazyLoading();
        }

        // WebGL增强
        if (advanced.webGL) {
            this.enableWebGLFeatures();
        }
    }

    setupFallbacks() {
        const { advanced } = this.features;

        // 没有Service Worker时的降级
        if (!advanced.serviceWorker) {
            this.setupOfflineFallback();
        }

        // 没有Intersection Observer时的降级
        if (!advanced.intersectionObserver) {
            this.setupScrollBasedLazyLoading();
        }

        // 没有Web Share时的降级
        if (!advanced.webShare) {
            this.setupCustomShareDialog();
        }
    }

    enableTouchEnhancements() {
        // 启用触摸友好的UI元素
        document.body.classList.add('touch-enhanced');

        // 添加触摸反馈
        const style = document.createElement('style');
        style.textContent = `
            .touch-enhanced button:active,
            .touch-enhanced .clickable:active {
                transform: scale(0.95);
                transition: transform 0.1s;
            }
        `;
        document.head.appendChild(style);
    }

    enableMobileEnhancements() {
        // 启用移动端优化
        document.body.classList.add('mobile-enhanced');

        // 优化移动端表单
        this.optimizeMobileForms();

        // 启用移动端导航
        this.enableMobileNavigation();
    }

    enableOfflineSupport() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('Service Worker registered:', registration);
                    this.showOfflineIndicator();
                })
                .catch(error => {
                    console.log('Service Worker registration failed:', error);
                });
        }
    }

    enableLazyLoading() {
        const images = document.querySelectorAll('img[data-src]');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                    observer.unobserve(img);
                }
            });
        });

        images.forEach(img => observer.observe(img));
    }

    setupScrollBasedLazyLoading() {
        // 降级到基于滚动的懒加载
        const images = document.querySelectorAll('img[data-src]');

        function loadVisibleImages() {
            images.forEach(img => {
                if (img.dataset.src) {
                    const rect = img.getBoundingClientRect();
                    if (rect.top < window.innerHeight && rect.bottom > 0) {
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                    }
                }
            });
        }

        window.addEventListener('scroll', loadVisibleImages);
        window.addEventListener('resize', loadVisibleImages);
        loadVisibleImages(); // 初始加载
    }

    enableNativeSharing() {
        // 使用原生分享API
        window.shareContent = async (data) => {
            try {
                await navigator.share(data);
                return true;
            } catch (error) {
                console.log('Native sharing failed:', error);
                return false;
            }
        };
    }

    setupCustomShareDialog() {
        // 自定义分享对话框
        window.shareContent = (data) => {
            const shareDialog = document.createElement('div');
            shareDialog.className = 'custom-share-dialog';
            shareDialog.innerHTML = `
                <div class="share-content">
                    <h3>Share</h3>
                    <div class="share-options">
                        <button onclick="shareToTwitter('${data.url}', '${data.title}')">Twitter</button>
                        <button onclick="shareToFacebook('${data.url}')">Facebook</button>
                        <button onclick="copyToClipboard('${data.url}')">Copy Link</button>
                    </div>
                    <button onclick="closeShareDialog()">Close</button>
                </div>
            `;

            document.body.appendChild(shareDialog);
            return true;
        };
    }

    // 获取功能支持报告
    getFeatureReport() {
        const { basic, browser, advanced } = this.features;

        const report = {
            basicSupport: {
                touch: basic.touch,
                mobile: basic.mobile,
                standalone: basic.standalone
            },
            browserInfo: {
                type: Object.keys(browser).find(key => browser[key]) || 'unknown',
                capabilities: browser
            },
            advancedFeatures: {
                supported: Object.keys(advanced).filter(key => advanced[key]),
                unsupported: Object.keys(advanced).filter(key => !advanced[key]),
                total: Object.keys(advanced).length,
                supportRate: (Object.values(advanced).filter(Boolean).length / Object.keys(advanced).length * 100).toFixed(1) + '%'
            }
        };

        return report;
    }

    // 生成兼容性建议
    getCompatibilityRecommendations() {
        const { advanced } = this.features;
        const recommendations = [];

        if (!advanced.serviceWorker) {
            recommendations.push({
                feature: 'Service Worker',
                impact: 'high',
                suggestion: 'Consider providing offline fallback messaging'
            });
        }

        if (!advanced.intersectionObserver) {
            recommendations.push({
                feature: 'Intersection Observer',
                impact: 'medium',
                suggestion: 'Use scroll-based lazy loading as fallback'
            });
        }

        if (!advanced.webShare) {
            recommendations.push({
                feature: 'Web Share API',
                impact: 'low',
                suggestion: 'Provide custom share dialog'
            });
        }

        if (!advanced.pushNotifications) {
            recommendations.push({
                feature: 'Push Notifications',
                impact: 'medium',
                suggestion: 'Use email notifications as alternative'
            });
        }

        return recommendations;
    }
}

// 使用示例
const enhancementManager = new FeatureEnhancementManager();
const featureReport = enhancementManager.getFeatureReport();
const recommendations = enhancementManager.getCompatibilityRecommendations();

console.log('Feature Support Report:', featureReport);
console.log('Compatibility Recommendations:', recommendations);
```

## 🔧 调试技巧

### 特性检测状态查看
```javascript
function debugFeatureDetection() {
    console.group('🔍 Feature Detection Debug');

    console.log('Browser Detection:');
    console.log('- Chrome:', isBrowserChrome());
    console.log('- Firefox:', isBrowserFirefox());
    console.log('- Safari:', isBrowserSafari());

    console.log('\nOS Detection:');
    console.log('- Android:', isAndroid());
    console.log('- iOS:', isIOS());
    console.log('- macOS:', isMacOS());
    console.log('- Mobile:', isMobileOS());

    console.log('\nApp Environment:');
    console.log('- iOS App:', isIosApp());
    console.log('- Android App:', isAndroidApp());
    console.log('- Standalone:', isDisplayStandalone());

    console.log('\nInteraction:');
    console.log('- Touch Support:', hasTouch());
    console.log('- Max Touch Points:', maxTouchPoints());

    console.log('\nUser Agent:', navigator.userAgent);

    console.groupEnd();
}

// 在控制台中调用
debugFeatureDetection();
```

### User Agent分析工具
```javascript
function analyzeUserAgent() {
    const ua = navigator.userAgent;

    console.group('📱 User Agent Analysis');
    console.log('Full UA:', ua);

    // 提取关键信息
    const patterns = {
        'Browser Engine': /(WebKit|Gecko|Trident|EdgeHTML)/i,
        'Browser Name': /(Chrome|Firefox|Safari|Edge|Opera)/i,
        'OS': /(Windows|Mac|Linux|Android|iOS)/i,
        'Device': /(iPhone|iPad|iPod|Android)/i,
        'Version': /Version\/([0-9.]+)/i
    };

    for (const [category, pattern] of Object.entries(patterns)) {
        const match = ua.match(pattern);
        console.log(`${category}:`, match ? match[1] : 'Not detected');
    }

    console.groupEnd();
}

// 分析当前User Agent
analyzeUserAgent();
```

## 📊 性能考虑

### 优化策略
1. **缓存结果**: 特性检测结果通常不变，可以缓存
2. **延迟检测**: 非关键特性可以延迟检测
3. **批量检测**: 一次性检测多个相关特性
4. **避免重复**: 使用单例模式避免重复检测

### 最佳实践
```javascript
// ✅ 好的做法：缓存检测结果
const deviceFeatures = {
    isMobile: isMobileOS(),
    hasTouch: hasTouch(),
    isStandalone: isDisplayStandalone()
};

// ❌ 不好的做法：重复检测
function someFunction() {
    if (isMobileOS()) { // 每次都重新检测
        // ...
    }
}

// ✅ 好的做法：基于特性而非浏览器
if (hasTouch()) {
    enableTouchFeatures();
}

// ❌ 不好的做法：基于浏览器假设特性
if (isMobileOS()) {
    enableTouchFeatures(); // 假设移动设备都有触摸
}
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解浏览器特性检测的原理和最佳实践
- [ ] 掌握User Agent字符串解析技巧
- [ ] 理解移动设备和桌面设备的识别方法
- [ ] 能够实现渐进式增强的设计
- [ ] 掌握跨平台兼容性的处理策略
- [ ] 了解功能降级和增强技术

## 🚀 下一步学习
学完特性检测后，建议继续学习：
1. **路由系统** (`@web/core/browser/router.js`) - 学习前端路由
2. **标题服务** (`@web/core/browser/title_service.js`) - 掌握页面标题管理
3. **浏览器封装** (`@web/core/browser/browser.js`) - 理解API封装

## 💡 重要提示
- 特性检测比浏览器检测更可靠
- User Agent可能被伪造，不应过度依赖
- 渐进式增强比优雅降级更好
- 定期更新检测逻辑以支持新设备和浏览器
```
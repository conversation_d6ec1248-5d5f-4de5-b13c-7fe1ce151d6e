# @web/core/browser/cookie.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/browser/cookie.js`
- **原始路径**: `/web/static/src/core/browser/cookie.js`
- **代码行数**: 43行
- **作用**: 提供Cookie操作的工具函数，简化Cookie的读取、设置和删除操作

## 🎯 学习目标
通过学习这个文件，您将掌握：
- Cookie操作的核心原理和最佳实践
- 字符串解析和处理技巧
- Web存储的选择策略和使用场景
- 安全的Cookie管理方法
- 轻量级工具库的设计思想

## 📚 核心概念

### 什么是Cookie工具？
Cookie工具是一个**轻量级存储操作库**，主要功能：
- **简化操作**: 提供简洁的Cookie读写API
- **解析处理**: 自动处理Cookie字符串的解析
- **生命周期**: 管理Cookie的过期时间
- **安全设置**: 提供合理的默认配置

### 核心架构组成
```javascript
const cookie = {
    // 内部属性
    _cookieMonster,     // Cookie字符串的getter/setter
    
    // 公共方法
    get(key),           // 获取Cookie值
    set(key, value, ttl), // 设置Cookie
    delete(key)         // 删除Cookie
};

// 默认配置
const COOKIE_TTL = 24 * 60 * 60 * 365; // 1年（秒）
```

### 基本使用模式
```javascript
import { cookie } from '@web/core/browser/cookie';

// 设置Cookie
cookie.set('username', 'john_doe');
cookie.set('theme', 'dark', 7 * 24 * 60 * 60); // 7天

// 获取Cookie
const username = cookie.get('username');
const theme = cookie.get('theme');

// 删除Cookie
cookie.delete('username');

// 检查Cookie是否存在
if (cookie.get('session_id')) {
    console.log('User is logged in');
}
```

## 🔍 核心实现详解

### 1. Cookie访问器设计

#### _cookieMonster属性
```javascript
get _cookieMonster() {
    return document.cookie;
},
set _cookieMonster(value) {
    document.cookie = value;
}
```

**访问器模式优势**：
- **封装性**: 隐藏直接的document.cookie访问
- **一致性**: 提供统一的内部访问接口
- **可测试性**: 便于在测试中模拟Cookie行为
- **命名趣味**: 使用有趣的命名增加代码可读性

### 2. Cookie解析算法

#### get()方法实现
```javascript
get(str) {
    const parts = this._cookieMonster.split("; ");
    for (const part of parts) {
        const [key, value] = part.split(/=(.*)/);
        if (key === str) {
            return value || "";
        }
    }
}
```

**解析策略分析**：
- **分割策略**: 使用"; "分割多个Cookie项
- **键值分离**: 使用正则表达式`/=(.*)/`处理值中的等号
- **空值处理**: 返回空字符串而非undefined
- **线性查找**: 简单的遍历查找，适合少量Cookie

**正则表达式解析**：
```javascript
const [key, value] = part.split(/=(.*)/);
```
- **捕获组**: `(.*)`捕获第一个等号后的所有内容
- **贪婪匹配**: 确保值中的等号不会被误分割
- **数组解构**: 直接解构为key和value

### 3. Cookie设置机制

#### set()方法实现
```javascript
set(key, value, ttl = COOKIE_TTL) {
    let fullCookie = [];
    if (value !== undefined) {
        fullCookie.push(`${key}=${value}`);
    }
    fullCookie = fullCookie.concat(["path=/", `max-age=${ttl}`]);
    this._cookieMonster = fullCookie.join("; ");
}
```

**设置策略详解**：
- **条件设置**: 只有value不为undefined才设置键值对
- **路径设置**: 默认path=/使Cookie在整个域下可用
- **过期控制**: 使用max-age而非expires，更精确
- **字符串构建**: 使用数组join提高性能

**默认配置分析**：
```javascript
const COOKIE_TTL = 24 * 60 * 60 * 365; // 1年
```
- **长期存储**: 默认1年的生存时间
- **秒为单位**: max-age使用秒作为时间单位
- **合理默认**: 平衡用户体验和隐私考虑

### 4. Cookie删除机制

#### delete()方法实现
```javascript
delete(key) {
    this.set(key, "kill", 0);
}
```

**删除策略**：
- **过期删除**: 通过设置max-age=0立即过期
- **占位值**: 使用"kill"作为占位值
- **简洁实现**: 复用set方法，代码简洁
- **立即生效**: max-age=0确保立即删除

## 🎨 实际应用场景

### 1. 用户偏好管理系统
```javascript
class UserPreferencesManager {
    constructor() {
        this.prefix = 'user_pref_';
        this.defaultTTL = 30 * 24 * 60 * 60; // 30天
    }
    
    // 设置用户偏好
    setPreference(key, value, persistent = true) {
        const cookieKey = this.prefix + key;
        const ttl = persistent ? this.defaultTTL : 0; // 0表示会话Cookie
        
        try {
            const serializedValue = JSON.stringify(value);
            cookie.set(cookieKey, serializedValue, ttl);
            return true;
        } catch (error) {
            console.warn(`Failed to set preference ${key}:`, error);
            return false;
        }
    }
    
    // 获取用户偏好
    getPreference(key, defaultValue = null) {
        const cookieKey = this.prefix + key;
        const value = cookie.get(cookieKey);
        
        if (!value) {
            return defaultValue;
        }
        
        try {
            return JSON.parse(value);
        } catch (error) {
            console.warn(`Failed to parse preference ${key}:`, error);
            return defaultValue;
        }
    }
    
    // 删除用户偏好
    removePreference(key) {
        const cookieKey = this.prefix + key;
        cookie.delete(cookieKey);
    }
    
    // 获取所有偏好
    getAllPreferences() {
        const preferences = {};
        const allCookies = this.getAllCookies();
        
        for (const [key, value] of Object.entries(allCookies)) {
            if (key.startsWith(this.prefix)) {
                const prefKey = key.substring(this.prefix.length);
                preferences[prefKey] = this.getPreference(prefKey);
            }
        }
        
        return preferences;
    }
    
    // 清除所有偏好
    clearAllPreferences() {
        const allCookies = this.getAllCookies();
        
        for (const key of Object.keys(allCookies)) {
            if (key.startsWith(this.prefix)) {
                cookie.delete(key);
            }
        }
    }
    
    // 获取所有Cookie（辅助方法）
    getAllCookies() {
        const cookies = {};
        const cookieString = document.cookie;
        
        if (cookieString) {
            const parts = cookieString.split('; ');
            for (const part of parts) {
                const [key, value] = part.split(/=(.*)/);
                if (key && value !== undefined) {
                    cookies[key] = value;
                }
            }
        }
        
        return cookies;
    }
    
    // 导出偏好设置
    exportPreferences() {
        const preferences = this.getAllPreferences();
        const exportData = {
            timestamp: new Date().toISOString(),
            preferences
        };
        
        return JSON.stringify(exportData, null, 2);
    }
    
    // 导入偏好设置
    importPreferences(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            const preferences = data.preferences || {};
            
            for (const [key, value] of Object.entries(preferences)) {
                this.setPreference(key, value);
            }
            
            return true;
        } catch (error) {
            console.error('Failed to import preferences:', error);
            return false;
        }
    }
}

// 使用示例
const prefsManager = new UserPreferencesManager();

// 设置各种偏好
prefsManager.setPreference('theme', 'dark');
prefsManager.setPreference('language', 'en');
prefsManager.setPreference('notifications', {
    email: true,
    push: false,
    sms: true
});

// 获取偏好
const theme = prefsManager.getPreference('theme', 'light');
const notifications = prefsManager.getPreference('notifications', {});

// 导出和导入
const exported = prefsManager.exportPreferences();
console.log('Exported preferences:', exported);
```

### 2. 会话管理系统
```javascript
class SessionManager {
    constructor() {
        this.sessionKey = 'session_token';
        this.userKey = 'user_info';
        this.sessionTTL = 8 * 60 * 60; // 8小时
    }
    
    // 创建会话
    createSession(token, userInfo) {
        // 设置会话令牌
        cookie.set(this.sessionKey, token, this.sessionTTL);
        
        // 设置用户信息（较短的过期时间）
        const userInfoStr = JSON.stringify(userInfo);
        cookie.set(this.userKey, userInfoStr, this.sessionTTL);
        
        // 设置会话创建时间
        cookie.set('session_created', Date.now(), this.sessionTTL);
        
        console.log('Session created for user:', userInfo.username);
    }
    
    // 获取会话令牌
    getSessionToken() {
        return cookie.get(this.sessionKey);
    }
    
    // 获取用户信息
    getUserInfo() {
        const userInfoStr = cookie.get(this.userKey);
        if (userInfoStr) {
            try {
                return JSON.parse(userInfoStr);
            } catch (error) {
                console.warn('Failed to parse user info:', error);
                this.clearSession();
                return null;
            }
        }
        return null;
    }
    
    // 检查会话是否有效
    isSessionValid() {
        const token = this.getSessionToken();
        const userInfo = this.getUserInfo();
        const createdTime = cookie.get('session_created');
        
        if (!token || !userInfo || !createdTime) {
            return false;
        }
        
        // 检查会话是否过期
        const now = Date.now();
        const sessionAge = now - parseInt(createdTime);
        const maxAge = this.sessionTTL * 1000; // 转换为毫秒
        
        if (sessionAge > maxAge) {
            this.clearSession();
            return false;
        }
        
        return true;
    }
    
    // 刷新会话
    refreshSession() {
        const token = this.getSessionToken();
        const userInfo = this.getUserInfo();
        
        if (token && userInfo) {
            this.createSession(token, userInfo);
            return true;
        }
        
        return false;
    }
    
    // 清除会话
    clearSession() {
        cookie.delete(this.sessionKey);
        cookie.delete(this.userKey);
        cookie.delete('session_created');
        console.log('Session cleared');
    }
    
    // 获取会话信息
    getSessionInfo() {
        if (!this.isSessionValid()) {
            return null;
        }
        
        const createdTime = parseInt(cookie.get('session_created'));
        const now = Date.now();
        const age = now - createdTime;
        const remaining = (this.sessionTTL * 1000) - age;
        
        return {
            token: this.getSessionToken(),
            user: this.getUserInfo(),
            createdAt: new Date(createdTime),
            age: Math.floor(age / 1000),
            remaining: Math.floor(remaining / 1000)
        };
    }
    
    // 自动刷新会话
    setupAutoRefresh() {
        const refreshInterval = 30 * 60 * 1000; // 30分钟
        
        setInterval(() => {
            if (this.isSessionValid()) {
                const sessionInfo = this.getSessionInfo();
                const remainingTime = sessionInfo.remaining * 1000;
                
                // 如果剩余时间少于1小时，自动刷新
                if (remainingTime < 60 * 60 * 1000) {
                    this.refreshSession();
                    console.log('Session auto-refreshed');
                }
            }
        }, refreshInterval);
    }
}

// 使用示例
const sessionManager = new SessionManager();

// 登录时创建会话
function login(username, password) {
    // 模拟登录API调用
    return fetch('/api/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            sessionManager.createSession(data.token, data.user);
            sessionManager.setupAutoRefresh();
            return true;
        }
        return false;
    });
}

// 检查登录状态
function checkLoginStatus() {
    return sessionManager.isSessionValid();
}

// 登出
function logout() {
    sessionManager.clearSession();
    window.location.href = '/login';
}
```

### 3. Cookie分析和调试工具
```javascript
class CookieAnalyzer {
    constructor() {
        this.originalCookie = document.cookie;
    }
    
    // 分析所有Cookie
    analyzeCookies() {
        const cookies = this.getAllCookies();
        const analysis = {
            total: Object.keys(cookies).length,
            totalSize: this.calculateTotalSize(cookies),
            categories: this.categorizeCookies(cookies),
            security: this.analyzeSecurityIssues(cookies),
            duplicates: this.findDuplicates(cookies)
        };
        
        return analysis;
    }
    
    // 获取所有Cookie
    getAllCookies() {
        const cookies = {};
        const cookieString = document.cookie;
        
        if (cookieString) {
            const parts = cookieString.split('; ');
            for (const part of parts) {
                const [key, value] = part.split(/=(.*)/);
                if (key && value !== undefined) {
                    cookies[key] = {
                        value,
                        size: (key + value).length,
                        encoded: this.isEncoded(value)
                    };
                }
            }
        }
        
        return cookies;
    }
    
    // 计算总大小
    calculateTotalSize(cookies) {
        return Object.entries(cookies).reduce((total, [key, data]) => {
            return total + key.length + data.value.length + 1; // +1 for '='
        }, 0);
    }
    
    // 分类Cookie
    categorizeCookies(cookies) {
        const categories = {
            session: [],
            tracking: [],
            preferences: [],
            security: [],
            other: []
        };
        
        for (const [key, data] of Object.entries(cookies)) {
            const lowerKey = key.toLowerCase();
            
            if (lowerKey.includes('session') || lowerKey.includes('token')) {
                categories.session.push(key);
            } else if (lowerKey.includes('track') || lowerKey.includes('analytics') || lowerKey.includes('ga')) {
                categories.tracking.push(key);
            } else if (lowerKey.includes('pref') || lowerKey.includes('setting') || lowerKey.includes('theme')) {
                categories.preferences.push(key);
            } else if (lowerKey.includes('csrf') || lowerKey.includes('auth') || lowerKey.includes('security')) {
                categories.security.push(key);
            } else {
                categories.other.push(key);
            }
        }
        
        return categories;
    }
    
    // 分析安全问题
    analyzeSecurityIssues(cookies) {
        const issues = [];
        
        for (const [key, data] of Object.entries(cookies)) {
            // 检查敏感信息
            if (this.containsSensitiveData(key, data.value)) {
                issues.push({
                    type: 'sensitive_data',
                    cookie: key,
                    message: 'Cookie may contain sensitive information'
                });
            }
            
            // 检查大小
            if (data.size > 4000) {
                issues.push({
                    type: 'large_cookie',
                    cookie: key,
                    size: data.size,
                    message: 'Cookie size exceeds recommended limit'
                });
            }
        }
        
        return issues;
    }
    
    // 查找重复Cookie
    findDuplicates(cookies) {
        const valueMap = new Map();
        const duplicates = [];
        
        for (const [key, data] of Object.entries(cookies)) {
            if (valueMap.has(data.value)) {
                duplicates.push({
                    keys: [valueMap.get(data.value), key],
                    value: data.value
                });
            } else {
                valueMap.set(data.value, key);
            }
        }
        
        return duplicates;
    }
    
    // 检查是否编码
    isEncoded(value) {
        try {
            return decodeURIComponent(value) !== value;
        } catch {
            return false;
        }
    }
    
    // 检查敏感数据
    containsSensitiveData(key, value) {
        const sensitivePatterns = [
            /password/i,
            /secret/i,
            /private/i,
            /\d{4}-\d{4}-\d{4}-\d{4}/, // 信用卡号模式
            /\d{3}-\d{2}-\d{4}/, // SSN模式
        ];
        
        const combined = key + value;
        return sensitivePatterns.some(pattern => pattern.test(combined));
    }
    
    // 生成报告
    generateReport() {
        const analysis = this.analyzeCookies();
        
        console.group('🍪 Cookie Analysis Report');
        
        console.log(`📊 Total Cookies: ${analysis.total}`);
        console.log(`📏 Total Size: ${analysis.totalSize} bytes`);
        
        if (analysis.totalSize > 4096) {
            console.warn('⚠️ Total cookie size exceeds 4KB limit');
        }
        
        console.group('📂 Categories');
        for (const [category, cookies] of Object.entries(analysis.categories)) {
            if (cookies.length > 0) {
                console.log(`${category}: ${cookies.length} cookies`, cookies);
            }
        }
        console.groupEnd();
        
        if (analysis.security.length > 0) {
            console.group('🔒 Security Issues');
            analysis.security.forEach(issue => {
                console.warn(`${issue.type}: ${issue.message}`, issue);
            });
            console.groupEnd();
        }
        
        if (analysis.duplicates.length > 0) {
            console.group('🔄 Duplicate Values');
            analysis.duplicates.forEach(dup => {
                console.log(`Duplicate value found in: ${dup.keys.join(', ')}`);
            });
            console.groupEnd();
        }
        
        console.groupEnd();
        
        return analysis;
    }
    
    // 清理建议
    getCleanupSuggestions() {
        const analysis = this.analyzeCookies();
        const suggestions = [];
        
        if (analysis.totalSize > 4096) {
            suggestions.push('Consider reducing cookie size or using localStorage for large data');
        }
        
        if (analysis.duplicates.length > 0) {
            suggestions.push('Remove duplicate cookies to save space');
        }
        
        if (analysis.security.length > 0) {
            suggestions.push('Review cookies for sensitive information');
        }
        
        return suggestions;
    }
}

// 使用示例
const analyzer = new CookieAnalyzer();

// 生成分析报告
const report = analyzer.generateReport();

// 获取清理建议
const suggestions = analyzer.getCleanupSuggestions();
console.log('Cleanup suggestions:', suggestions);
```

## 🔧 调试技巧

### Cookie状态查看
```javascript
function debugCookies() {
    console.group('Cookie Debug');
    console.log('Raw cookie string:', document.cookie);
    
    const cookies = {};
    if (document.cookie) {
        document.cookie.split('; ').forEach(part => {
            const [key, value] = part.split(/=(.*)/);
            cookies[key] = value;
        });
    }
    
    console.table(cookies);
    console.groupEnd();
}

// 在控制台中调用
debugCookies();
```

### Cookie操作追踪
```javascript
function traceCookieOperations() {
    const originalGet = cookie.get;
    const originalSet = cookie.set;
    const originalDelete = cookie.delete;
    
    cookie.get = function(key) {
        console.log(`🍪 GET: ${key}`);
        return originalGet.call(this, key);
    };
    
    cookie.set = function(key, value, ttl) {
        console.log(`🍪 SET: ${key} = ${value} (TTL: ${ttl})`);
        return originalSet.call(this, key, value, ttl);
    };
    
    cookie.delete = function(key) {
        console.log(`🍪 DELETE: ${key}`);
        return originalDelete.call(this, key);
    };
}

// 启用追踪
traceCookieOperations();
```

## 📊 性能考虑

### 优化策略
1. **最小化大小**: Cookie有4KB限制，避免存储大量数据
2. **合理TTL**: 设置适当的过期时间，避免不必要的持久化
3. **减少数量**: 每个请求都会发送Cookie，减少数量提升性能
4. **使用localStorage**: 对于大量数据，优先使用localStorage

### 最佳实践
```javascript
// ✅ 好的做法：存储简单标识符
cookie.set('user_id', '12345');

// ❌ 不好的做法：存储大量数据
cookie.set('user_data', JSON.stringify(largeUserObject));

// ✅ 好的做法：设置合理的过期时间
cookie.set('session', 'abc123', 8 * 60 * 60); // 8小时

// ❌ 不好的做法：使用默认的长期过期
cookie.set('temp_data', 'value'); // 默认1年
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解Cookie操作的核心原理和最佳实践
- [ ] 掌握字符串解析和处理技巧
- [ ] 理解Web存储的选择策略和使用场景
- [ ] 能够实现安全的Cookie管理方法
- [ ] 掌握轻量级工具库的设计思想
- [ ] 了解Cookie的安全和性能考虑

## 🚀 下一步学习
学完Cookie工具后，建议继续学习：
1. **特性检测** (`@web/core/browser/feature_detection.js`) - 学习浏览器特性检测
2. **路由系统** (`@web/core/browser/router.js`) - 理解前端路由
3. **标题服务** (`@web/core/browser/title_service.js`) - 掌握页面标题管理

## 💡 重要提示
- Cookie主要用于小量数据和会话管理
- 大量数据应使用localStorage或sessionStorage
- 注意Cookie的安全性和隐私问题
- 合理设置过期时间和路径范围

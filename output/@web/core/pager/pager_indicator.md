# PagerIndicator - 分页指示器

## 概述

`pager_indicator.js` 是 Odoo Web 核心模块的分页指示器组件，提供了分页状态的可视化指示功能。该组件监听分页器更新事件，在移动端设备上显示当前分页信息，具备自动显示隐藏、过渡动画、事件总线通信等特性，为用户提供了清晰的分页状态反馈，主要应用于移动端界面的分页状态提示。

## 文件信息
- **路径**: `/web/static/src/core/pager/pager_indicator.js`
- **行数**: 43
- **模块**: `@web/core/pager/pager_indicator`

## 依赖关系

```javascript
// 核心依赖
'@web/core/browser/browser'          // 浏览器服务
'@web/core/registry'                 // 注册表系统
'@web/core/transition'               // 过渡动画
'@web/core/utils/hooks'              // 工具钩子
'@odoo/owl'                          // OWL框架
'@web/core/pager/pager'              // 分页器组件
```

## 主组件类

### 1. 组件定义

```javascript
class PagerIndicator extends Component {
    static template = "web.PagerIndicator";
    static components = { Transition };
    static props = {};
}
```

**组件特点**:
- **无属性**: 不需要外部属性，完全基于事件驱动
- **过渡动画**: 集成Transition组件提供平滑动画
- **全局组件**: 注册为主要组件，全局可用

### 2. 组件初始化

```javascript
setup() {
    this.state = useState({
        show: false,        // 是否显示指示器
        value: "-",         // 当前分页值
        total: 0,           // 总记录数
    });
    this.startShowTimer = null;
    useBus(pagerBus, PAGER_UPDATED_EVENT, this.pagerUpdate);
}
```

**初始化功能**:
- **状态管理**: 管理显示状态和分页信息
- **定时器**: 管理自动隐藏的定时器
- **事件监听**: 监听分页器更新事件

## 核心方法

### 1. 分页更新处理

```javascript
pagerUpdate({ detail }) {
    this.state.value = detail.value;
    this.state.total = detail.total;
    browser.clearTimeout(this.startShowTimer);
    this.state.show = true;
    this.startShowTimer = browser.setTimeout(() => {
        this.state.show = false;
    }, 1400);
}
```

**更新处理功能**:
- **信息更新**: 更新当前分页值和总记录数
- **定时器管理**: 清除旧定时器，设置新的自动隐藏定时器
- **显示控制**: 立即显示指示器，1.4秒后自动隐藏
- **事件响应**: 响应分页器的更新事件

## 组件注册

```javascript
registry.category("main_components").add("PagerIndicator", {
    Component: PagerIndicator,
});
```

**注册功能**:
- **全局注册**: 注册为主要组件
- **自动加载**: 系统自动加载和管理
- **单例模式**: 全局唯一实例

## 使用场景

### 1. 移动端分页指示

```javascript
// 移动端分页指示示例
class MobilePagerDemo extends Component {
    setup() {
        this.state = useState({
            data: [],
            offset: 0,
            limit: 5,
            total: 0,
            isLoading: false
        });

        // 模拟数据
        this.allData = Array.from({ length: 47 }, (_, i) => ({
            id: i + 1,
            name: `项目 ${i + 1}`,
            description: `这是第 ${i + 1} 个项目的描述`
        }));

        this.loadData();
    }

    async loadData() {
        this.state.isLoading = true;
        
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 300));
        
        this.state.total = this.allData.length;
        this.state.data = this.allData.slice(this.state.offset, this.state.offset + this.state.limit);
        this.state.isLoading = false;
    }

    async onPagerUpdate({ offset, limit }, hasNavigated) {
        console.log('Pager update:', { offset, limit, hasNavigated });
        
        this.state.offset = offset;
        this.state.limit = limit;
        
        await this.loadData();
    }

    render() {
        return xml`
            <div class="mobile-pager-demo">
                <h5>移动端分页指示示例</h5>
                
                <div class="mobile-viewport border rounded p-3" style="max-width: 375px; margin: 0 auto;">
                    <div class="data-list mb-3">
                        <div class="loading text-center py-3" t-if="state.isLoading">
                            <div class="spinner-border spinner-border-sm me-2"/>
                            <span>加载中...</span>
                        </div>
                        
                        <div class="list-group" t-if="!state.isLoading">
                            <t t-foreach="state.data" t-as="item" t-key="item.id">
                                <div class="list-group-item">
                                    <h6 class="mb-1" t-esc="item.name"/>
                                    <p class="mb-1 text-muted" t-esc="item.description"/>
                                    <small class="text-muted">ID: <t t-esc="item.id"/></small>
                                </div>
                            </t>
                        </div>
                    </div>

                    <div class="pager-container d-flex justify-content-center">
                        <Pager
                            offset="state.offset"
                            limit="state.limit"
                            total="state.total"
                            onUpdate="onPagerUpdate"
                            isEditable="true"
                            withAccessKey="false"
                        />
                    </div>
                </div>

                <div class="instructions mt-3">
                    <div class="alert alert-info">
                        <h6>移动端分页指示器说明</h6>
                        <ul class="mb-0">
                            <li>在移动端设备上，分页器更新时会显示分页指示器</li>
                            <li>指示器显示当前页面范围和总记录数</li>
                            <li>指示器会在1.4秒后自动隐藏</li>
                            <li>使用过渡动画提供平滑的显示效果</li>
                        </ul>
                    </div>
                </div>

                <!-- 模拟分页指示器（用于演示） -->
                <div class="demo-pager-indicator mt-3">
                    <h6>分页指示器预览</h6>
                    <div class="position-relative border rounded p-3" style="height: 100px;">
                        <DemoPagerIndicator
                            value="${this.state.offset + 1}-${Math.min(this.state.offset + this.state.limit, this.state.total)}"
                            total="state.total"
                        />
                    </div>
                </div>
            </div>
        `;
    }
}

// 演示用的分页指示器组件
class DemoPagerIndicator extends Component {
    static template = xml`
        <div class="demo-pager-indicator-overlay">
            <div class="pager-indicator-content bg-dark text-white rounded px-3 py-2">
                <div class="text-center">
                    <div class="fw-bold" t-esc="props.value"/>
                    <small>/ <t t-esc="props.total"/> 条记录</small>
                </div>
            </div>
        </div>
    `;
    
    static props = {
        value: String,
        total: Number,
    };
}
```

### 2. 分页指示器测试工具

```javascript
// 分页指示器测试工具
class PagerIndicatorTester extends Component {
    setup() {
        this.state = useState({
            testCases: [
                { value: "1-10", total: 100, description: "第一页" },
                { value: "11-20", total: 100, description: "中间页" },
                { value: "91-100", total: 100, description: "最后一页" },
                { value: "1", total: 1, description: "单条记录" },
                { value: "1-5", total: 5, description: "全部记录" }
            ],
            currentTest: null,
            autoTest: false,
            testInterval: null
        });
    }

    triggerPagerUpdate(testCase) {
        // 模拟分页器更新事件
        pagerBus.trigger(PAGER_UPDATED_EVENT, {
            value: testCase.value,
            total: testCase.total
        });
        
        this.state.currentTest = testCase;
        console.log('Triggered pager update:', testCase);
    }

    startAutoTest() {
        if (this.state.autoTest) {
            this.stopAutoTest();
            return;
        }

        this.state.autoTest = true;
        let index = 0;
        
        this.state.testInterval = setInterval(() => {
            const testCase = this.state.testCases[index];
            this.triggerPagerUpdate(testCase);
            
            index = (index + 1) % this.state.testCases.length;
        }, 2000);
    }

    stopAutoTest() {
        this.state.autoTest = false;
        if (this.state.testInterval) {
            clearInterval(this.state.testInterval);
            this.state.testInterval = null;
        }
    }

    onWillUnmount() {
        this.stopAutoTest();
    }

    render() {
        return xml`
            <div class="pager-indicator-tester">
                <h5>分页指示器测试工具</h5>
                
                <div class="test-controls mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6>测试用例</h6>
                        <button 
                            class="btn"
                            t-att-class="state.autoTest ? 'btn-danger' : 'btn-success'"
                            t-on-click="startAutoTest"
                        >
                            <i t-att-class="state.autoTest ? 'fa fa-stop' : 'fa fa-play'"/>
                            <t t-if="state.autoTest">停止自动测试</t>
                            <t t-else="">开始自动测试</t>
                        </button>
                    </div>
                </div>

                <div class="test-cases">
                    <div class="row">
                        <t t-foreach="state.testCases" t-as="testCase" t-key="testCase_index">
                            <div class="col-md-6 mb-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title" t-esc="testCase.description"/>
                                        <div class="test-case-info mb-3">
                                            <p class="mb-1">
                                                <strong>值:</strong> <code t-esc="testCase.value"/>
                                            </p>
                                            <p class="mb-1">
                                                <strong>总数:</strong> <t t-esc="testCase.total"/>
                                            </p>
                                        </div>
                                        <button 
                                            class="btn btn-primary btn-sm"
                                            t-on-click="() => this.triggerPagerUpdate(testCase)"
                                            t-att-disabled="state.autoTest"
                                        >
                                            触发更新
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>
                </div>

                <div class="current-test mt-4" t-if="state.currentTest">
                    <div class="alert alert-info">
                        <h6>当前测试</h6>
                        <p class="mb-0">
                            <strong><t t-esc="state.currentTest.description"/>:</strong>
                            显示 <code t-esc="state.currentTest.value"/></code>
                            / 共 <t t-esc="state.currentTest.total"/> 条记录
                        </p>
                    </div>
                </div>

                <div class="technical-info mt-4">
                    <div class="card">
                        <div class="card-header">
                            <h6>技术信息</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>事件总线</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>事件名:</strong> <code>PAGER:UPDATED</code></li>
                                        <li><strong>总线:</strong> <code>pagerBus</code></li>
                                        <li><strong>数据:</strong> <code>{value, total}</code></li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>显示逻辑</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>显示时长:</strong> 1.4秒</li>
                                        <li><strong>触发条件:</strong> 分页器更新</li>
                                        <li><strong>目标设备:</strong> 移动端</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="usage-example mt-4">
                    <div class="card">
                        <div class="card-header">
                            <h6>使用示例</h6>
                        </div>
                        <div class="card-body">
                            <pre><code>// 触发分页指示器更新
pagerBus.trigger(PAGER_UPDATED_EVENT, {
    value: "1-10",
    total: 100
});

// 组件会自动显示并在1.4秒后隐藏</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 事件驱动
- 基于事件总线的通信机制
- 松耦合的组件间通信
- 响应式的状态更新

### 2. 自动管理
- 自动显示和隐藏
- 智能的定时器管理
- 无需手动控制

### 3. 移动端优化
- 专为移动端设计
- 简洁的信息展示
- 不干扰用户操作

### 4. 平滑动画
- 集成过渡动画组件
- 优雅的显示隐藏效果
- 提升用户体验

## 设计模式

### 1. 观察者模式 (Observer Pattern)
- 监听分页器更新事件
- 响应式的状态更新

### 2. 单例模式 (Singleton Pattern)
- 全局唯一的指示器实例
- 统一的状态管理

### 3. 发布订阅模式 (Pub-Sub Pattern)
- 基于事件总线的通信
- 解耦的组件交互

## 注意事项

1. **定时器管理**: 正确清理定时器避免内存泄漏
2. **事件监听**: 确保事件监听器的正确绑定和解绑
3. **移动端适配**: 主要针对移动端设备优化
4. **性能考虑**: 避免频繁的DOM更新

## 扩展建议

1. **自定义样式**: 支持自定义指示器样式
2. **位置配置**: 可配置的显示位置
3. **动画选项**: 更多的动画效果选择
4. **持续时间**: 可配置的显示持续时间
5. **条件显示**: 基于条件的显示控制

该分页指示器组件为Odoo Web应用提供了简洁有效的分页状态反馈，通过事件驱动的设计和自动管理确保了良好的用户体验和开发者友好性。

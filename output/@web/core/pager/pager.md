# Pager - 分页器组件

## 概述

`pager.js` 是 Odoo Web 核心模块的分页器组件，提供了数据分页导航功能。该组件支持页面范围显示、手动页面输入、前后页导航、总数更新等功能，具备可编辑模式、键盘快捷键、移动端适配等特性，为用户提供了灵活便捷的分页体验，广泛应用于列表视图、搜索结果、数据表格等需要分页显示的场景。

## 文件信息
- **路径**: `/web/static/src/core/pager/pager.js`
- **行数**: 207
- **模块**: `@web/core/pager/pager`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/hooks'              // 工具钩子
'@web/core/utils/numbers'            // 数值工具
'@odoo/owl'                          // OWL框架
```

## 核心常量

```javascript
const PAGER_UPDATED_EVENT = "PAGER:UPDATED";    // 分页器更新事件
const pagerBus = new EventBus();                // 分页器事件总线
```

## 主组件类

### 1. 组件属性定义

```javascript
static props = {
    offset: Number,                              // 偏移量
    limit: Number,                               // 每页限制
    total: Number,                               // 总记录数
    onUpdate: Function,                          // 更新回调
    isEditable: { type: Boolean, optional: true }, // 是否可编辑
    withAccessKey: { type: Boolean, optional: true }, // 是否支持快捷键
    updateTotal: { type: Function, optional: true }, // 更新总数回调
};

static defaultProps = {
    isEditable: true,
    withAccessKey: true,
};
```

**属性功能**:
- **分页参数**: offset、limit、total控制分页逻辑
- **交互控制**: isEditable控制是否可手动编辑页码
- **快捷键**: withAccessKey控制键盘快捷键支持
- **动态总数**: updateTotal支持动态获取总记录数

### 2. 组件初始化

```javascript
setup() {
    this.state = useState({
        isEditing: false,
        isDisabled: false,
    });
    this.inputRef = useAutofocus();
    useExternalListener(document, "mousedown", this.onClickAway, { capture: true });
}
```

**初始化功能**:
- **状态管理**: 管理编辑和禁用状态
- **自动聚焦**: 编辑时自动聚焦输入框
- **外部监听**: 监听外部点击事件退出编辑模式

## 核心属性

### 1. 页面范围计算

```javascript
get minimum() {
    return this.props.offset + 1;
}

get maximum() {
    return Math.min(this.props.offset + this.props.limit, this.props.total);
}

get value() {
    const parts = [this.minimum];
    if (this.props.limit > 1) {
        parts.push(this.maximum);
    }
    return parts.join("-");
}
```

**范围计算功能**:
- **最小值**: 当前页的起始记录号
- **最大值**: 当前页的结束记录号
- **显示值**: 单记录显示数字，多记录显示范围

### 2. 单页检测

```javascript
get isSinglePage() {
    return !this.props.updateTotal && this.minimum === 1 && this.maximum === this.props.total;
}
```

**单页检测功能**:
- **条件判断**: 检查是否只有一页数据
- **动态总数**: 考虑动态总数的情况
- **UI优化**: 单页时可隐藏分页器

## 核心方法

### 1. 页面导航

```javascript
async navigate(direction) {
    let minimum = this.props.offset + this.props.limit * direction;
    let total = this.props.total;
    if (this.props.updateTotal && minimum < 0) {
        // 需要知道真实总数才能循环到最后一页
        total = await this.props.updateTotal();
    }
    if (minimum >= total) {
        if (!this.props.updateTotal) {
            // 只有在知道真实总数时才循环到第一页
            minimum = 0;
        }
    } else if (minimum < 0 && this.props.limit === 1) {
        minimum = total - 1;
    } else if (minimum < 0 && this.props.limit > 1) {
        minimum = total - (total % this.props.limit || this.props.limit);
    }
    this.update(minimum, this.props.limit, true);
}
```

**导航功能**:
- **方向控制**: direction为-1(上一页)或1(下一页)
- **边界处理**: 处理首页和末页的循环逻辑
- **动态总数**: 支持动态获取总数的场景
- **智能计算**: 计算最后一页的正确偏移量

### 2. 值解析

```javascript
async parse(value) {
    let [minimum, maximum] = value.trim().split(/\s*[-\s,;]\s*/);
    minimum = parseInt(minimum, 10);
    maximum = maximum ? parseInt(maximum, 10) : minimum;
    if (this.props.updateTotal) {
        // 不知道真实总数，无法限制范围
        return { minimum: minimum - 1, maximum };
    }
    return {
        minimum: clamp(minimum, 1, this.props.total) - 1,
        maximum: clamp(maximum, 1, this.props.total),
    };
}
```

**解析功能**:
- **格式支持**: 支持多种分隔符(-, 空格, 逗号, 分号)
- **范围解析**: 解析单个数字或范围
- **边界限制**: 限制在有效范围内
- **动态适配**: 适配动态总数的情况

### 3. 值设置

```javascript
async setValue(value) {
    const { minimum, maximum } = await this.parse(value);

    if (!isNaN(minimum) && !isNaN(maximum) && minimum < maximum) {
        this.update(minimum, maximum - minimum);
    }
}
```

**设置功能**:
- **值验证**: 验证解析后的值有效性
- **范围检查**: 确保最小值小于最大值
- **更新触发**: 触发分页器更新

### 4. 分页更新

```javascript
async update(offset, limit, hasNavigated) {
    this.state.isDisabled = true;
    try {
        await this.props.onUpdate({ offset, limit }, hasNavigated);
    } finally {
        if (this.env.isSmall) {
            pagerBus.trigger(PAGER_UPDATED_EVENT, {
                value: this.value,
                total: this.props.total,
            });
        }
        this.state.isDisabled = false;
        this.state.isEditing = false;
    }
}
```

**更新功能**:
- **状态控制**: 更新期间禁用组件
- **回调通知**: 调用父组件的更新回调
- **事件广播**: 在小屏幕设备上广播更新事件
- **状态重置**: 更新完成后重置状态

## 事件处理

### 1. 键盘事件

```javascript
onInputKeydown(ev) {
    switch (ev.key) {
        case "Enter":
            ev.preventDefault();
            ev.stopPropagation();
            this.setValue(ev.currentTarget.value);
            break;
        case "Escape":
            ev.preventDefault();
            ev.stopPropagation();
            this.state.isEditing = false;
            break;
    }
}
```

**键盘事件功能**:
- **Enter确认**: 回车键确认输入
- **Escape取消**: ESC键取消编辑
- **事件阻止**: 防止事件冒泡

### 2. 鼠标事件

```javascript
onClickAway(ev) {
    if (ev.target !== this.inputRef.el) {
        this.state.isEditing = false;
    }
}

onValueClick() {
    if (this.props.isEditable && !this.state.isEditing && !this.state.isDisabled) {
        if (this.inputRef.el) {
            this.inputRef.el.focus();
        }
        this.state.isEditing = true;
    }
}
```

**鼠标事件功能**:
- **点击外部**: 点击外部区域退出编辑
- **点击编辑**: 点击值进入编辑模式
- **状态检查**: 检查可编辑和禁用状态

## 使用场景

### 1. 基础分页器

```javascript
// 基础分页器使用
class BasicPager extends Component {
    setup() {
        this.state = useState({
            data: [],
            offset: 0,
            limit: 10,
            total: 0,
            isLoading: false
        });

        this.loadData();
    }

    async loadData() {
        this.state.isLoading = true;
        try {
            // 模拟数据加载
            const allData = Array.from({ length: 157 }, (_, i) => ({
                id: i + 1,
                name: `项目 ${i + 1}`,
                description: `这是第 ${i + 1} 个项目的描述`
            }));

            this.state.total = allData.length;
            this.state.data = allData.slice(this.state.offset, this.state.offset + this.state.limit);
        } catch (error) {
            console.error('Failed to load data:', error);
        } finally {
            this.state.isLoading = false;
        }
    }

    async onPagerUpdate({ offset, limit }, hasNavigated) {
        console.log('Pager update:', { offset, limit, hasNavigated });
        
        this.state.offset = offset;
        this.state.limit = limit;
        
        await this.loadData();
    }

    render() {
        return xml`
            <div class="basic-pager">
                <h5>基础分页器示例</h5>
                
                <div class="data-list mb-3">
                    <div class="loading text-center py-3" t-if="state.isLoading">
                        <i class="fa fa-spinner fa-spin"/>
                        <span class="ms-2">加载中...</span>
                    </div>
                    
                    <div class="list-group" t-if="!state.isLoading">
                        <t t-foreach="state.data" t-as="item" t-key="item.id">
                            <div class="list-group-item">
                                <h6 class="mb-1" t-esc="item.name"/>
                                <p class="mb-1" t-esc="item.description"/>
                                <small class="text-muted">ID: <t t-esc="item.id"/></small>
                            </div>
                        </t>
                    </div>
                </div>

                <div class="pager-container d-flex justify-content-between align-items-center">
                    <div class="pager-info">
                        <small class="text-muted">
                            显示 ${this.state.offset + 1} - ${Math.min(this.state.offset + this.state.limit, this.state.total)} 
                            / 共 ${this.state.total} 条记录
                        </small>
                    </div>
                    
                    <Pager
                        offset="state.offset"
                        limit="state.limit"
                        total="state.total"
                        onUpdate="onPagerUpdate"
                        isEditable="true"
                        withAccessKey="true"
                    />
                </div>
            </div>
        `;
    }
}
```

### 2. 高级分页管理器

```javascript
// 高级分页管理器
class AdvancedPagerManager extends Component {
    setup() {
        this.state = useState({
            datasets: [
                { 
                    name: '用户列表', 
                    total: 1234, 
                    offset: 0, 
                    limit: 20,
                    allowEdit: true 
                },
                { 
                    name: '订单列表', 
                    total: 5678, 
                    offset: 100, 
                    limit: 50,
                    allowEdit: true 
                },
                { 
                    name: '产品列表', 
                    total: 999, 
                    offset: 0, 
                    limit: 25,
                    allowEdit: false 
                }
            ],
            selectedDataset: 0,
            history: []
        });
    }

    get currentDataset() {
        return this.state.datasets[this.state.selectedDataset];
    }

    async onPagerUpdate(datasetIndex, { offset, limit }, hasNavigated) {
        const dataset = this.state.datasets[datasetIndex];
        const oldState = { offset: dataset.offset, limit: dataset.limit };
        
        // 记录历史
        this.state.history.unshift({
            timestamp: new Date().toLocaleTimeString(),
            dataset: dataset.name,
            action: hasNavigated ? '导航' : '手动输入',
            from: `${oldState.offset + 1}-${Math.min(oldState.offset + oldState.limit, dataset.total)}`,
            to: `${offset + 1}-${Math.min(offset + limit, dataset.total)}`
        });

        // 限制历史记录数量
        if (this.state.history.length > 10) {
            this.state.history.pop();
        }

        // 更新数据集
        dataset.offset = offset;
        dataset.limit = limit;

        console.log(`Dataset "${dataset.name}" updated:`, { offset, limit, hasNavigated });
        
        // 模拟数据加载延迟
        await new Promise(resolve => setTimeout(resolve, 300));
    }

    selectDataset(index) {
        this.state.selectedDataset = index;
    }

    resetDataset(index) {
        const dataset = this.state.datasets[index];
        dataset.offset = 0;
        dataset.limit = 20;
    }

    async updateTotal(datasetIndex) {
        const dataset = this.state.datasets[datasetIndex];
        
        // 模拟动态获取总数
        const newTotal = Math.floor(Math.random() * 10000) + 1000;
        dataset.total = newTotal;
        
        console.log(`Dataset "${dataset.name}" total updated to:`, newTotal);
        return newTotal;
    }

    getPageInfo(dataset) {
        const currentPage = Math.floor(dataset.offset / dataset.limit) + 1;
        const totalPages = Math.ceil(dataset.total / dataset.limit);
        return { currentPage, totalPages };
    }

    render() {
        const current = this.currentDataset;
        const pageInfo = this.getPageInfo(current);

        return xml`
            <div class="advanced-pager-manager">
                <h5>高级分页管理器</h5>
                
                <div class="dataset-selector mb-3">
                    <label class="form-label">选择数据集:</label>
                    <div class="btn-group w-100">
                        <t t-foreach="state.datasets" t-as="dataset" t-key="dataset_index">
                            <button 
                                class="btn"
                                t-att-class="state.selectedDataset === dataset_index ? 'btn-primary' : 'btn-outline-primary'"
                                t-on-click="() => this.selectDataset(dataset_index)"
                            >
                                <t t-esc="dataset.name"/>
                                <br/>
                                <small>${dataset.total} 条记录</small>
                            </button>
                        </t>
                    </div>
                </div>

                <div class="current-dataset-info mb-3">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0" t-esc="current.name"/>
                            <div class="dataset-actions">
                                <button 
                                    class="btn btn-sm btn-outline-secondary me-2"
                                    t-on-click="() => this.resetDataset(state.selectedDataset)"
                                >
                                    重置
                                </button>
                                <button 
                                    class="btn btn-sm btn-outline-info"
                                    t-on-click="() => this.updateTotal(state.selectedDataset)"
                                >
                                    更新总数
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>当前页:</strong> ${pageInfo.currentPage}
                                </div>
                                <div class="col-md-3">
                                    <strong>总页数:</strong> ${pageInfo.totalPages}
                                </div>
                                <div class="col-md-3">
                                    <strong>每页:</strong> ${current.limit} 条
                                </div>
                                <div class="col-md-3">
                                    <strong>总记录:</strong> ${current.total} 条
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="pager-section mb-4">
                    <div class="d-flex justify-content-center">
                        <Pager
                            offset="current.offset"
                            limit="current.limit"
                            total="current.total"
                            onUpdate="(params, hasNavigated) => this.onPagerUpdate(state.selectedDataset, params, hasNavigated)"
                            isEditable="current.allowEdit"
                            withAccessKey="true"
                            updateTotal="() => this.updateTotal(state.selectedDataset)"
                        />
                    </div>
                </div>

                <div class="datasets-overview">
                    <h6>所有数据集概览</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>数据集</th>
                                    <th>当前范围</th>
                                    <th>每页记录</th>
                                    <th>总记录</th>
                                    <th>可编辑</th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-foreach="state.datasets" t-as="dataset" t-key="dataset_index">
                                    <tr t-att-class="state.selectedDataset === dataset_index ? 'table-primary' : ''">
                                        <td t-esc="dataset.name"/>
                                        <td>
                                            ${dataset.offset + 1} - ${Math.min(dataset.offset + dataset.limit, dataset.total)}
                                        </td>
                                        <td t-esc="dataset.limit"/>
                                        <td t-esc="dataset.total"/>
                                        <td>
                                            <i t-att-class="'fa ' + (dataset.allowEdit ? 'fa-check text-success' : 'fa-times text-danger')"/>
                                        </td>
                                    </tr>
                                </t>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="history-section mt-4" t-if="state.history.length">
                    <h6>操作历史</h6>
                    <div class="history-list">
                        <t t-foreach="state.history" t-as="record" t-key="record_index">
                            <div class="history-item border-bottom py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong t-esc="record.dataset"/>
                                        <span class="badge bg-secondary ms-2" t-esc="record.action"/>
                                    </div>
                                    <small class="text-muted" t-esc="record.timestamp"/>
                                </div>
                                <div class="text-muted">
                                    <t t-esc="record.from"/> → <t t-esc="record.to"/>
                                </div>
                            </div>
                        </t>
                    </div>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 灵活的范围显示
- 单记录显示数字
- 多记录显示范围
- 智能的边界计算

### 2. 多格式输入支持
- 支持多种分隔符
- 范围和单值输入
- 智能解析和验证

### 3. 动态总数支持
- 支持未知总数的场景
- 按需获取总数
- 智能的导航逻辑

### 4. 移动端适配
- 事件总线通信
- 小屏幕优化
- 响应式设计

## 设计模式

### 1. 观察者模式 (Observer Pattern)
- 分页更新的事件通知
- 状态变化的响应机制

### 2. 策略模式 (Strategy Pattern)
- 不同场景的导航策略
- 可配置的行为模式

### 3. 状态模式 (State Pattern)
- 编辑和禁用状态的管理
- 状态转换的控制

## 注意事项

1. **边界处理**: 正确处理首页和末页的边界情况
2. **用户体验**: 提供清晰的分页状态反馈
3. **性能优化**: 避免频繁的数据加载
4. **输入验证**: 验证用户输入的有效性

## 扩展建议

1. **页面大小选择**: 支持动态调整每页记录数
2. **快速跳转**: 提供快速跳转到指定页面
3. **加载状态**: 显示数据加载的进度状态
4. **无限滚动**: 支持无限滚动模式
5. **统计信息**: 显示更详细的分页统计信息

该分页器组件为Odoo Web应用提供了完整的分页导航功能，通过灵活的配置和智能的边界处理确保了良好的用户体验和开发者友好性。

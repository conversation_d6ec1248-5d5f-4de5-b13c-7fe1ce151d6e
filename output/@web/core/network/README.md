# Odoo Network 模块架构总览与使用指南

## 📁 模块概览

**目录路径**: `output/@web/core/network/`  
**模块类型**: 核心基础模块 - 网络通信系统  
**功能范围**: HTTP 请求、RPC 调用、文件下载

## 🏗️ 架构图

```
@web/core/network/
├── 📄 http_service.js      # HTTP 基础服务
├── 📄 rpc.js              # RPC 远程调用
└── 📄 download.js         # 文件下载功能
```

## 🎯 模块功能矩阵

| 模块 | 主要功能 | 核心API | 依赖关系 |
|------|----------|---------|----------|
| **http_service.js** | HTTP 基础服务 | `get()`, `post()` | browser, registry |
| **rpc.js** | RPC 远程调用 | `rpc()`, `rpcBus` | owl, browser |
| **download.js** | 文件下载 | `downloadFile()`, `download()` | translation, rpc, browser |

## 🔄 数据流图

```mermaid
graph TD
    A[用户操作] --> B{请求类型}
    B -->|简单HTTP| C[http_service]
    B -->|业务调用| D[rpc]
    B -->|文件下载| E[download]
    
    C --> F[browser.fetch]
    D --> G[XMLHttpRequest]
    E --> H{下载方式}
    
    H -->|客户端| I[downloadFile]
    H -->|服务器| J[download + rpc]
    
    F --> K[响应处理]
    G --> L[JSON-RPC 2.0]
    I --> M[Blob/URL 下载]
    J --> N[文件流下载]
    
    K --> O[用户界面]
    L --> O
    M --> O
    N --> O
```

## 🚀 快速开始

### 1. HTTP 基础请求
```javascript
import { useService } from "@web/core/utils/hooks";

class ApiComponent extends Component {
    setup() {
        this.http = useService("http");
    }
    
    async loadData() {
        // GET 请求
        const users = await this.http.get("/api/users");
        
        // POST 请求
        const result = await this.http.post("/api/users", {
            name: "John",
            email: "<EMAIL>"
        });
    }
}
```

### 2. RPC 业务调用
```javascript
import { rpc } from "@web/core/network/rpc";

// Odoo 模型调用
const users = await rpc("/web/dataset/call_kw", {
    model: "res.users",
    method: "search_read",
    args: [[]],
    kwargs: { fields: ["name", "email"] }
});

// 自定义控制器调用
const result = await rpc("/my/custom/endpoint", {
    action: "process_data",
    data: { key: "value" }
});
```

### 3. 文件下载
```javascript
import { downloadFile, download } from "@web/core/network/download";

// 客户端文件下载
const jsonData = JSON.stringify({name: "test"});
downloadFile(jsonData, "data.json", "application/json");

// 服务器文件下载
await download({
    url: "/web/content",
    data: { model: "ir.attachment", id: 123 }
});
```

## 📚 核心概念详解

### 1. HTTP 服务层
- **基础封装**: 对 Fetch API 的简单封装
- **统一接口**: 提供 GET/POST 方法
- **错误检测**: 502 错误的特殊处理
- **格式支持**: JSON/Text/Blob 等多种响应格式

### 2. RPC 通信层
- **JSON-RPC 2.0**: 标准协议实现
- **事件系统**: 基于 EventBus 的监控
- **错误分类**: 连接错误、服务器错误、中断错误
- **请求管理**: 支持请求取消和生命周期管理

### 3. 文件下载层
- **多种方式**: 客户端下载和服务器下载
- **格式支持**: Blob/File/String/URL 等
- **浏览器兼容**: 跨浏览器下载策略
- **错误处理**: 完善的下载错误处理

## 🔧 高级用法

### 1. HTTP 服务扩展
```javascript
class EnhancedHttpService {
    constructor(baseHttp) {
        this.http = baseHttp;
        this.interceptors = [];
    }
    
    addInterceptor(interceptor) {
        this.interceptors.push(interceptor);
    }
    
    async get(url, readMethod = "json") {
        // 应用请求拦截器
        for (const interceptor of this.interceptors) {
            if (interceptor.request) {
                url = await interceptor.request(url);
            }
        }
        
        try {
            let result = await this.http.get(url, readMethod);
            
            // 应用响应拦截器
            for (const interceptor of this.interceptors) {
                if (interceptor.response) {
                    result = await interceptor.response(result);
                }
            }
            
            return result;
        } catch (error) {
            // 应用错误拦截器
            for (const interceptor of this.interceptors) {
                if (interceptor.error) {
                    error = await interceptor.error(error);
                }
            }
            throw error;
        }
    }
}
```

### 2. RPC 监控和管理
```javascript
import { rpcBus } from "@web/core/network/rpc";

class RpcManager {
    constructor() {
        this.activeRequests = new Map();
        this.setupMonitoring();
    }
    
    setupMonitoring() {
        rpcBus.addEventListener("RPC:REQUEST", (event) => {
            const { data } = event.detail;
            this.activeRequests.set(data.id, {
                timestamp: Date.now(),
                url: event.detail.url,
                params: data.params
            });
            this.updateLoadingState();
        });
        
        rpcBus.addEventListener("RPC:RESPONSE", (event) => {
            const { data, error } = event.detail;
            this.activeRequests.delete(data.id);
            
            if (error) {
                this.handleError(error);
            }
            
            this.updateLoadingState();
        });
    }
    
    updateLoadingState() {
        const isLoading = this.activeRequests.size > 0;
        document.body.classList.toggle("rpc-loading", isLoading);
    }
    
    handleError(error) {
        console.error("RPC Error:", error);
        // 可以添加全局错误处理逻辑
    }
    
    getActiveRequests() {
        return Array.from(this.activeRequests.values());
    }
}
```

### 3. 统一网络客户端
```javascript
class NetworkClient {
    constructor() {
        this.http = useService("http");
        this.setupRpcMonitoring();
    }
    
    // HTTP 方法
    async get(url, options = {}) {
        return this.http.get(url, options.responseType || "json");
    }
    
    async post(url, data, options = {}) {
        return this.http.post(url, data, options.responseType || "json");
    }
    
    // RPC 方法
    async call(model, method, args = [], kwargs = {}) {
        return rpc("/web/dataset/call_kw", {
            model,
            method,
            args,
            kwargs
        });
    }
    
    async searchRead(model, domain = [], fields = [], options = {}) {
        return this.call(model, "search_read", [domain], {
            fields,
            limit: options.limit,
            offset: options.offset,
            order: options.order
        });
    }
    
    async create(model, values) {
        return this.call(model, "create", [values]);
    }
    
    async write(model, ids, values) {
        return this.call(model, "write", [ids, values]);
    }
    
    async unlink(model, ids) {
        return this.call(model, "unlink", [ids]);
    }
    
    // 文件下载方法
    async downloadFile(data, filename, mimetype) {
        return downloadFile(data, filename, mimetype);
    }
    
    async downloadFromServer(url, data = {}) {
        return download({ url, data });
    }
    
    async downloadReport(reportName, ids, format = "pdf") {
        return download({
            url: "/web/report/download",
            data: {
                report_name: reportName,
                ids: JSON.stringify(ids),
                format: format
            }
        });
    }
}
```

## 🎨 最佳实践

### 1. 错误处理策略
```javascript
class ErrorHandler {
    static async handleNetworkCall(networkFn) {
        try {
            return await networkFn();
        } catch (error) {
            return this.processError(error);
        }
    }
    
    static processError(error) {
        if (error.message === "Failed to fetch") {
            throw new Error("网络连接失败，请检查网络设置");
        } else if (error instanceof RPCError) {
            return this.handleRpcError(error);
        } else if (error instanceof ConnectionLostError) {
            throw new Error("服务器连接中断，请稍后重试");
        } else {
            throw error;
        }
    }
    
    static handleRpcError(error) {
        switch (error.exceptionName) {
            case "AccessError":
                throw new Error("您没有权限执行此操作");
            case "ValidationError":
                throw new Error(`数据验证失败: ${error.message}`);
            case "UserError":
                throw new Error(error.message);
            default:
                throw new Error(`服务器错误: ${error.message}`);
        }
    }
}

// 使用示例
const result = await ErrorHandler.handleNetworkCall(async () => {
    return await rpc("/web/dataset/call_kw", params);
});
```

### 2. 请求优化
```javascript
class RequestOptimizer {
    constructor() {
        this.cache = new Map();
        this.pendingRequests = new Map();
    }
    
    // 请求去重
    async deduplicatedRpc(url, params, settings = {}) {
        const key = `${url}:${JSON.stringify(params)}`;
        
        if (this.pendingRequests.has(key)) {
            return this.pendingRequests.get(key);
        }
        
        const promise = rpc(url, params, settings);
        this.pendingRequests.set(key, promise);
        
        try {
            const result = await promise;
            return result;
        } finally {
            this.pendingRequests.delete(key);
        }
    }
    
    // 请求缓存
    async cachedRpc(url, params, settings = {}, ttl = 60000) {
        const key = `${url}:${JSON.stringify(params)}`;
        const cached = this.cache.get(key);
        
        if (cached && Date.now() - cached.timestamp < ttl) {
            return cached.result;
        }
        
        const result = await rpc(url, params, settings);
        this.cache.set(key, {
            result,
            timestamp: Date.now()
        });
        
        return result;
    }
    
    // 批量请求
    async batchRpc(requests) {
        const promises = requests.map(({ url, params, settings }) =>
            rpc(url, params, settings)
                .then(result => ({ success: true, result }))
                .catch(error => ({ success: false, error }))
        );
        
        return Promise.all(promises);
    }
}
```

### 3. 性能监控
```javascript
class NetworkMonitor {
    constructor() {
        this.metrics = {
            requests: 0,
            errors: 0,
            totalTime: 0,
            slowRequests: []
        };
        this.setupMonitoring();
    }
    
    setupMonitoring() {
        rpcBus.addEventListener("RPC:REQUEST", (event) => {
            this.metrics.requests++;
            event.detail.startTime = Date.now();
        });
        
        rpcBus.addEventListener("RPC:RESPONSE", (event) => {
            const duration = Date.now() - event.detail.startTime;
            this.metrics.totalTime += duration;
            
            if (event.detail.error) {
                this.metrics.errors++;
            }
            
            if (duration > 5000) { // 超过5秒的慢请求
                this.metrics.slowRequests.push({
                    url: event.detail.url,
                    duration,
                    timestamp: Date.now()
                });
            }
        });
    }
    
    getStats() {
        return {
            ...this.metrics,
            averageTime: this.metrics.totalTime / this.metrics.requests,
            errorRate: this.metrics.errors / this.metrics.requests
        };
    }
    
    getSlowRequests() {
        return this.metrics.slowRequests
            .sort((a, b) => b.duration - a.duration)
            .slice(0, 10);
    }
}
```

## 🔍 调试技巧

### 1. 网络请求调试
```javascript
// 开启详细日志
window.debugNetwork = true;

// 在浏览器控制台中监控
rpcBus.addEventListener("RPC:REQUEST", (event) => {
    if (window.debugNetwork) {
        console.group(`🚀 RPC Request #${event.detail.data.id}`);
        console.log("URL:", event.detail.url);
        console.log("Params:", event.detail.data.params);
        console.groupEnd();
    }
});

rpcBus.addEventListener("RPC:RESPONSE", (event) => {
    if (window.debugNetwork) {
        const { data, error, result } = event.detail;
        console.group(`📥 RPC Response #${data.id}`);
        if (error) {
            console.error("Error:", error);
        } else {
            console.log("Result:", result);
        }
        console.groupEnd();
    }
});
```

### 2. 性能分析
```javascript
// 测量网络性能
class NetworkProfiler {
    static profile(name, networkFn) {
        return async (...args) => {
            const start = performance.now();
            try {
                const result = await networkFn(...args);
                const end = performance.now();
                console.log(`${name} took ${end - start} milliseconds`);
                return result;
            } catch (error) {
                const end = performance.now();
                console.error(`${name} failed after ${end - start} milliseconds:`, error);
                throw error;
            }
        };
    }
}

// 使用示例
const profiledRpc = NetworkProfiler.profile("User Search", rpc);
const users = await profiledRpc("/web/dataset/call_kw", params);
```

## ⚡ 性能优化

### 1. 连接管理
- **连接复用**: 浏览器自动管理 HTTP/2 连接池
- **并发控制**: 限制同时进行的请求数量
- **请求优先级**: 重要请求优先处理

### 2. 数据优化
- **请求去重**: 避免重复的相同请求
- **响应缓存**: 缓存不变的数据
- **数据压缩**: 启用 gzip 压缩

### 3. 错误恢复
- **自动重试**: 网络错误时自动重试
- **降级策略**: 服务不可用时的备选方案
- **用户反馈**: 及时的错误提示和状态更新

## 🌍 扩展指南

### 1. 添加新的网络服务
```javascript
// 创建新的网络服务
const websocketService = {
    start() {
        const ws = new WebSocket("ws://localhost:8080");
        return {
            send: (data) => ws.send(JSON.stringify(data)),
            onMessage: (callback) => ws.onmessage = callback,
            close: () => ws.close()
        };
    }
};

// 注册服务
registry.category("services").add("websocket", websocketService);
```

### 2. 自定义下载处理器
```javascript
// 扩展下载功能
function downloadExcel(data, filename) {
    const blob = new Blob([data], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    });
    downloadFile(blob, filename);
}

function downloadPDF(data, filename) {
    const blob = new Blob([data], { type: "application/pdf" });
    downloadFile(blob, filename);
}
```

## 📖 相关资源

- [JSON-RPC 2.0 规范](https://www.jsonrpc.org/specification)
- [Fetch API 文档](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API)
- [XMLHttpRequest 文档](https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest)
- [Blob API 文档](https://developer.mozilla.org/en-US/docs/Web/API/Blob)

## 🎯 总结

Odoo Network 模块是一个完整的网络通信解决方案，提供了：
- **分层架构**: HTTP 基础层、RPC 业务层、下载功能层
- **标准协议**: 支持 HTTP/1.1、JSON-RPC 2.0 等标准
- **错误处理**: 完善的错误分类和处理机制
- **性能优化**: 请求去重、缓存、监控等优化策略
- **扩展性**: 易于扩展和自定义的模块化设计

这个模块为 Odoo 的前后端通信提供了可靠、高效的技术基础，是整个 Web 客户端架构的重要组成部分。

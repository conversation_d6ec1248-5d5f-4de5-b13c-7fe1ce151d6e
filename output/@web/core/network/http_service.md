# Odoo HTTP 服务模块 (HTTP Service) 学习资料

## 文件概述

**文件路径**: `output/@web/core/network/http_service.js`  
**原始路径**: `/web/static/src/core/network/http_service.js`  
**模块类型**: 核心基础模块 - HTTP 网络服务  
**代码行数**: 51 行  
**依赖关系**: 
- `@web/core/browser/browser` - 浏览器抽象层
- `@web/core/registry` - 服务注册表

## 模块功能

HTTP 服务模块是 Odoo Web 客户端网络通信的基础服务，负责：
- 提供统一的 HTTP 请求接口
- 封装浏览器 Fetch API
- 处理 GET 和 POST 请求
- 统一的错误状态检查
- FormData 自动转换

## 核心架构

### 1. 服务结构
```javascript
const httpService = {
    start() {
        return { get, post };
    }
};
```

**设计特点**:
- **简洁设计**: 只提供最基本的 HTTP 方法
- **函数式**: 返回纯函数而非类实例
- **无状态**: 不维护内部状态
- **可测试**: 易于单元测试和模拟

### 2. 依赖注入
```javascript
registry.category("services").add("http", httpService);
```

**集成方式**:
- 注册到服务注册表
- 支持依赖注入
- 可在组件中通过 `useService("http")` 使用

## 核心函数分析

### 1. checkResponseStatus() 函数
```javascript
function checkResponseStatus(response) {
    if (response.status === 502) {
        throw new Error("Failed to fetch");
    }
}
```

**功能分析**:
- **错误检测**: 检查 502 Bad Gateway 错误
- **异常抛出**: 将网络错误转换为 JavaScript 异常
- **统一处理**: 为所有 HTTP 请求提供一致的错误处理

**502 错误含义**:
- 通常表示代理服务器（如 nginx）无法连接到后端 Odoo 服务器
- 可能是服务器重启、维护或网络问题
- 需要特殊处理以提供用户友好的错误信息

### 2. get() 函数
```javascript
async function get(route, readMethod = "json") {
    const response = await browser.fetch(route, { method: "GET" });
    checkResponseStatus(response);
    return response[readMethod]();
}
```

**参数分析**:
- `route`: 请求的 URL 路径
- `readMethod`: 响应读取方法，默认为 "json"

**支持的读取方法**:
- `"json"`: 解析 JSON 响应
- `"text"`: 获取文本内容
- `"blob"`: 获取二进制数据
- `"arrayBuffer"`: 获取数组缓冲区
- `"formData"`: 解析表单数据

**使用示例**:
```javascript
// JSON 响应
const data = await get("/api/users");

// 文本响应
const html = await get("/web/template", "text");

// 二进制响应
const blob = await get("/web/image/123", "blob");
```

### 3. post() 函数
```javascript
async function post(route, params = {}, readMethod = "json") {
    let formData = params;
    if (!(formData instanceof FormData)) {
        formData = new FormData();
        for (const key in params) {
            const value = params[key];
            if (Array.isArray(value) && value.length) {
                for (const val of value) {
                    formData.append(key, val);
                }
            } else {
                formData.append(key, value);
            }
        }
    }
    const response = await browser.fetch(route, {
        body: formData,
        method: "POST",
    });
    checkResponseStatus(response);
    return response[readMethod]();
}
```

**功能特点**:
- **自动转换**: 将普通对象转换为 FormData
- **数组支持**: 正确处理数组参数
- **FormData 直传**: 支持直接传递 FormData 对象
- **灵活读取**: 支持多种响应格式

**参数转换逻辑**:
```javascript
// 输入对象
const params = {
    name: "John",
    tags: ["admin", "user"],
    active: true
};

// 转换为 FormData
// name: "John"
// tags: "admin"
// tags: "user"  
// active: "true"
```

## 实际使用示例

### 1. 在组件中使用
```javascript
import { Component } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";

class DataComponent extends Component {
    setup() {
        this.http = useService("http");
    }
    
    async loadData() {
        try {
            const users = await this.http.get("/api/users");
            this.users = users;
        } catch (error) {
            console.error("Failed to load users:", error);
        }
    }
    
    async saveUser(userData) {
        try {
            const result = await this.http.post("/api/users", userData);
            console.log("User saved:", result);
        } catch (error) {
            console.error("Failed to save user:", error);
        }
    }
}
```

### 2. 不同数据格式处理
```javascript
class ApiClient {
    constructor(httpService) {
        this.http = httpService;
    }
    
    // JSON API 调用
    async getUsers() {
        return await this.http.get("/api/users");
    }
    
    // 获取 HTML 模板
    async getTemplate(templateName) {
        return await this.http.get(`/web/template/${templateName}`, "text");
    }
    
    // 下载文件
    async downloadFile(fileId) {
        return await this.http.get(`/web/content/${fileId}`, "blob");
    }
    
    // 上传文件
    async uploadFile(file, metadata = {}) {
        const formData = new FormData();
        formData.append("file", file);
        Object.entries(metadata).forEach(([key, value]) => {
            formData.append(key, value);
        });
        
        return await this.http.post("/web/upload", formData);
    }
}
```

### 3. 错误处理策略
```javascript
class RobustApiClient {
    constructor(httpService) {
        this.http = httpService;
    }
    
    async safeGet(route, readMethod = "json", retries = 3) {
        for (let i = 0; i < retries; i++) {
            try {
                return await this.http.get(route, readMethod);
            } catch (error) {
                if (error.message === "Failed to fetch" && i < retries - 1) {
                    // 502 错误，等待后重试
                    await this.delay(1000 * (i + 1));
                    continue;
                }
                throw error;
            }
        }
    }
    
    async safePost(route, params = {}, readMethod = "json") {
        try {
            return await this.http.post(route, params, readMethod);
        } catch (error) {
            if (error.message === "Failed to fetch") {
                throw new Error("服务器暂时不可用，请稍后重试");
            }
            throw error;
        }
    }
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
```

### 4. 批量请求处理
```javascript
class BatchApiClient {
    constructor(httpService) {
        this.http = httpService;
    }
    
    async batchGet(routes) {
        const promises = routes.map(route => 
            this.http.get(route).catch(error => ({ error, route }))
        );
        return await Promise.all(promises);
    }
    
    async sequentialPost(requests) {
        const results = [];
        for (const { route, params } of requests) {
            try {
                const result = await this.http.post(route, params);
                results.push({ success: true, result });
            } catch (error) {
                results.push({ success: false, error, route });
            }
        }
        return results;
    }
}
```

## 设计模式分析

### 1. 服务模式 (Service Pattern)
```javascript
const httpService = {
    start() {
        return { get, post };
    }
};
```

**优势**:
- **单一职责**: 专注于 HTTP 通信
- **依赖注入**: 支持测试和模拟
- **生命周期管理**: 通过服务系统管理

### 2. 适配器模式 (Adapter Pattern)
```javascript
// 将 Fetch API 适配为更简单的接口
async function get(route, readMethod = "json") {
    const response = await browser.fetch(route, { method: "GET" });
    return response[readMethod]();
}
```

**作用**:
- 简化 Fetch API 的使用
- 提供统一的错误处理
- 隐藏底层实现细节

### 3. 策略模式 (Strategy Pattern)
```javascript
// 通过 readMethod 参数选择不同的响应处理策略
return response[readMethod]();
```

**灵活性**:
- 支持多种响应格式
- 运行时选择处理策略
- 易于扩展新的响应类型

## 扩展和增强

### 1. 添加请求拦截器
```javascript
class EnhancedHttpService {
    constructor(baseHttpService) {
        this.base = baseHttpService;
        this.interceptors = [];
    }
    
    addInterceptor(interceptor) {
        this.interceptors.push(interceptor);
    }
    
    async get(route, readMethod = "json") {
        // 应用请求拦截器
        for (const interceptor of this.interceptors) {
            if (interceptor.request) {
                route = await interceptor.request(route);
            }
        }
        
        try {
            let result = await this.base.get(route, readMethod);
            
            // 应用响应拦截器
            for (const interceptor of this.interceptors) {
                if (interceptor.response) {
                    result = await interceptor.response(result);
                }
            }
            
            return result;
        } catch (error) {
            // 应用错误拦截器
            for (const interceptor of this.interceptors) {
                if (interceptor.error) {
                    error = await interceptor.error(error);
                }
            }
            throw error;
        }
    }
}
```

### 2. 添加缓存支持
```javascript
class CachedHttpService {
    constructor(baseHttpService) {
        this.base = baseHttpService;
        this.cache = new Map();
    }
    
    async get(route, readMethod = "json", useCache = true) {
        const cacheKey = `${route}:${readMethod}`;
        
        if (useCache && this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }
        
        const result = await this.base.get(route, readMethod);
        
        if (useCache) {
            this.cache.set(cacheKey, result);
        }
        
        return result;
    }
    
    clearCache(pattern) {
        if (pattern) {
            for (const key of this.cache.keys()) {
                if (key.includes(pattern)) {
                    this.cache.delete(key);
                }
            }
        } else {
            this.cache.clear();
        }
    }
}
```

### 3. 添加超时支持
```javascript
class TimeoutHttpService {
    constructor(baseHttpService, defaultTimeout = 30000) {
        this.base = baseHttpService;
        this.defaultTimeout = defaultTimeout;
    }
    
    async get(route, readMethod = "json", timeout = this.defaultTimeout) {
        return this.withTimeout(
            this.base.get(route, readMethod),
            timeout
        );
    }
    
    async post(route, params = {}, readMethod = "json", timeout = this.defaultTimeout) {
        return this.withTimeout(
            this.base.post(route, params, readMethod),
            timeout
        );
    }
    
    withTimeout(promise, timeout) {
        return Promise.race([
            promise,
            new Promise((_, reject) => 
                setTimeout(() => reject(new Error("Request timeout")), timeout)
            )
        ]);
    }
}
```

## 测试策略

### 1. 单元测试
```javascript
// 模拟 HTTP 服务
const mockHttpService = {
    get: jest.fn(),
    post: jest.fn()
};

// 测试用例
describe("HTTP Service", () => {
    test("should handle GET requests", async () => {
        mockHttpService.get.mockResolvedValue({ data: "test" });
        
        const result = await mockHttpService.get("/api/test");
        
        expect(result).toEqual({ data: "test" });
        expect(mockHttpService.get).toHaveBeenCalledWith("/api/test");
    });
    
    test("should handle POST requests", async () => {
        const postData = { name: "test" };
        mockHttpService.post.mockResolvedValue({ id: 1 });
        
        const result = await mockHttpService.post("/api/create", postData);
        
        expect(result).toEqual({ id: 1 });
        expect(mockHttpService.post).toHaveBeenCalledWith("/api/create", postData);
    });
});
```

### 2. 集成测试
```javascript
// 使用真实的 HTTP 服务进行集成测试
describe("HTTP Service Integration", () => {
    let httpService;
    
    beforeEach(() => {
        httpService = registry.category("services").get("http").start();
    });
    
    test("should fetch real data", async () => {
        // 需要测试服务器支持
        const result = await httpService.get("/web/session/get_session_info");
        expect(result).toBeDefined();
    });
});
```

## 性能优化

### 1. 请求去重
```javascript
class DeduplicatedHttpService {
    constructor(baseHttpService) {
        this.base = baseHttpService;
        this.pendingRequests = new Map();
    }
    
    async get(route, readMethod = "json") {
        const key = `GET:${route}:${readMethod}`;
        
        if (this.pendingRequests.has(key)) {
            return this.pendingRequests.get(key);
        }
        
        const promise = this.base.get(route, readMethod);
        this.pendingRequests.set(key, promise);
        
        try {
            const result = await promise;
            return result;
        } finally {
            this.pendingRequests.delete(key);
        }
    }
}
```

### 2. 连接池管理
```javascript
// 虽然浏览器会自动管理连接池，但可以控制并发请求数
class ThrottledHttpService {
    constructor(baseHttpService, maxConcurrent = 6) {
        this.base = baseHttpService;
        this.maxConcurrent = maxConcurrent;
        this.activeRequests = 0;
        this.queue = [];
    }
    
    async get(route, readMethod = "json") {
        return this.throttle(() => this.base.get(route, readMethod));
    }
    
    async throttle(requestFn) {
        if (this.activeRequests >= this.maxConcurrent) {
            await new Promise(resolve => this.queue.push(resolve));
        }
        
        this.activeRequests++;
        
        try {
            return await requestFn();
        } finally {
            this.activeRequests--;
            if (this.queue.length > 0) {
                const next = this.queue.shift();
                next();
            }
        }
    }
}
```

## 最佳实践

### 1. 错误处理
```javascript
// ✅ 推荐：完整的错误处理
try {
    const data = await http.get("/api/data");
    return data;
} catch (error) {
    if (error.message === "Failed to fetch") {
        throw new Error("网络连接失败，请检查网络设置");
    }
    throw error;
}

// ❌ 避免：忽略错误
const data = await http.get("/api/data"); // 可能抛出未处理的异常
```

### 2. 参数验证
```javascript
// ✅ 推荐：验证参数
function validateRoute(route) {
    if (!route || typeof route !== "string") {
        throw new Error("Route must be a non-empty string");
    }
}

// ❌ 避免：直接使用未验证的参数
await http.get(userInput); // 可能导致安全问题
```

### 3. 响应格式选择
```javascript
// ✅ 推荐：明确指定响应格式
const html = await http.get("/template", "text");
const blob = await http.get("/file", "blob");

// ❌ 避免：假设默认格式
const data = await http.get("/template"); // 可能解析失败
```

## 总结

HTTP 服务模块是 Odoo Web 客户端网络通信的基础组件，它提供了：
- **简洁的 API**: 封装了复杂的 Fetch API
- **统一的错误处理**: 一致的网络错误检测
- **灵活的数据处理**: 支持多种响应格式
- **易于扩展**: 简单的架构便于功能增强
- **测试友好**: 支持模拟和单元测试

虽然功能相对简单，但这个模块为 Odoo 的网络通信提供了可靠的基础，是构建复杂网络功能的重要基石。

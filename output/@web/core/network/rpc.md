# Odoo RPC 模块 (Remote Procedure Call) 学习资料

## 文件概述

**文件路径**: `output/@web/core/network/rpc.js`  
**原始路径**: `/web/static/src/core/network/rpc.js`  
**模块类型**: 核心基础模块 - 远程过程调用  
**代码行数**: 134 行  
**依赖关系**: 
- `@odoo/owl` - OWL 框架 (EventBus)
- `@web/core/browser/browser` - 浏览器抽象层

## 模块功能

RPC 模块是 Odoo Web 客户端与服务器通信的核心组件，负责：
- 实现 JSON-RPC 2.0 协议
- 提供统一的远程调用接口
- 处理网络错误和服务器错误
- 支持请求取消和中断
- 提供事件总线用于监控 RPC 调用

## 核心架构

### 1. 事件总线
```javascript
const rpcBus = new EventBus();
```

**功能**:
- **请求监控**: 触发 `RPC:REQUEST` 事件
- **响应监控**: 触发 `RPC:RESPONSE` 事件
- **全局监听**: 允许其他模块监听 RPC 活动
- **调试支持**: 便于调试和性能分析

### 2. 错误类型体系
```
Error
├── RPCError (服务器业务错误)
├── ConnectionLostError (连接丢失)
└── ConnectionAbortedError (请求中断)
```

## 错误类型详解

### 1. RPCError 类
```javascript
class RPCError extends Error {
    constructor() {
        super(...arguments);
        this.name = "RPC_ERROR";
        this.type = "server";
        this.code = null;
        this.data = null;
        this.exceptionName = null;
        this.subType = null;
    }
}
```

**属性说明**:
- `name`: 错误类型标识
- `type`: 错误来源 ("server")
- `code`: 错误代码
- `data`: 详细错误数据
- `exceptionName`: Python 异常类名
- `subType`: 错误子类型

**使用场景**:
- 服务器端业务逻辑错误
- 权限验证失败
- 数据验证错误
- Python 异常传递

### 2. ConnectionLostError 类
```javascript
class ConnectionLostError extends Error {
    constructor(url, ...args) {
        super(`Connection to "${url}" couldn't be established or was interrupted`, ...args);
        this.url = url;
    }
}
```

**触发条件**:
- HTTP 502 Bad Gateway 错误
- 网络连接中断
- 服务器无响应
- JSON 解析失败

### 3. ConnectionAbortedError 类
```javascript
class ConnectionAbortedError extends Error {}
```

**触发条件**:
- 用户主动取消请求
- 页面导航中断
- 超时取消

## 核心函数分析

### 1. rpc() 主函数
```javascript
function rpc(url, params = {}, settings = {}) {
    return rpc._rpc(url, params, settings);
}
```

**参数说明**:
- `url`: 服务器端点 URL
- `params`: 调用参数对象
- `settings`: 请求配置选项

**返回值**: 带有 `abort()` 方法的 Promise

### 2. JSON-RPC 2.0 协议实现
```javascript
const data = {
    id: rpcId++,
    jsonrpc: "2.0",
    method: "call",
    params: params,
};
```

**协议特点**:
- **标准兼容**: 遵循 JSON-RPC 2.0 规范
- **唯一标识**: 每个请求有唯一 ID
- **统一方法**: 所有调用使用 "call" 方法
- **参数封装**: 将实际参数包装在 params 字段中

### 3. 错误响应处理
```javascript
function makeErrorFromResponse(response) {
    const { code, data: errorData, message, type: subType } = response;
    const error = new RPCError();
    error.exceptionName = errorData.name;
    error.subType = subType;
    error.data = errorData;
    error.message = message;
    error.code = code;
    return error;
}
```

**处理流程**:
1. 解构响应错误信息
2. 创建 RPCError 实例
3. 设置错误属性
4. 返回结构化错误对象

### 4. 请求生命周期管理
```javascript
// 成功处理
request.addEventListener("load", () => {
    if (request.status === 502) {
        const error = new ConnectionLostError(url);
        rpcBus.trigger("RPC:RESPONSE", { data, settings, error });
        reject(error);
        return;
    }
    
    let params;
    try {
        params = JSON.parse(request.response);
    } catch {
        const error = new ConnectionLostError(url);
        rpcBus.trigger("RPC:RESPONSE", { data, settings, error });
        return reject(error);
    }
    
    const { error: responseError, result: responseResult } = params;
    if (!params.error) {
        rpcBus.trigger("RPC:RESPONSE", { data, settings, result: params.result });
        return resolve(responseResult);
    }
    
    const error = makeErrorFromResponse(responseError);
    error.id = data.id;
    error.model = data.params.model;
    rpcBus.trigger("RPC:RESPONSE", { data, settings, error });
    reject(error);
});

// 失败处理
request.addEventListener("error", () => {
    const error = new ConnectionLostError(url);
    rpcBus.trigger("RPC:RESPONSE", { data, settings, error });
    reject(error);
});
```

### 5. 请求取消机制
```javascript
promise.abort = function (rejectError = true) {
    if (request.abort) {
        request.abort();
    }
    const error = new ConnectionAbortedError("XmlHttpRequestError abort");
    rpcBus.trigger("RPC:RESPONSE", { data, settings, error });
    if (rejectError) {
        rejectFn(error);
    }
};
```

**特性**:
- **可选拒绝**: 可以选择是否抛出错误
- **事件通知**: 触发响应事件
- **资源清理**: 中断底层 XHR 请求

## 实际使用示例

### 1. 基本 RPC 调用
```javascript
import { rpc } from "@web/core/network/rpc";

// 简单调用
async function getUsers() {
    try {
        const result = await rpc("/web/dataset/call_kw", {
            model: "res.users",
            method: "search_read",
            args: [[]],
            kwargs: {
                fields: ["name", "email"],
                limit: 10
            }
        });
        return result;
    } catch (error) {
        console.error("Failed to fetch users:", error);
        throw error;
    }
}

// 带设置的调用
async function createUser(userData) {
    const result = await rpc("/web/dataset/call_kw", {
        model: "res.users",
        method: "create",
        args: [userData],
        kwargs: {}
    }, {
        headers: {
            "X-Custom-Header": "value"
        }
    });
    return result;
}
```

### 2. 错误处理策略
```javascript
class RpcErrorHandler {
    async safeRpc(url, params, settings = {}) {
        try {
            return await rpc(url, params, settings);
        } catch (error) {
            return this.handleError(error);
        }
    }
    
    handleError(error) {
        if (error instanceof ConnectionLostError) {
            this.showConnectionError();
            throw new Error("网络连接已断开，请检查网络设置");
        } else if (error instanceof ConnectionAbortedError) {
            console.log("Request was cancelled");
            return null;
        } else if (error instanceof RPCError) {
            return this.handleServerError(error);
        } else {
            console.error("Unknown error:", error);
            throw error;
        }
    }
    
    handleServerError(error) {
        switch (error.exceptionName) {
            case "AccessError":
                throw new Error("您没有权限执行此操作");
            case "ValidationError":
                throw new Error(`数据验证失败: ${error.message}`);
            case "UserError":
                throw new Error(error.message);
            default:
                throw new Error(`服务器错误: ${error.message}`);
        }
    }
    
    showConnectionError() {
        // 显示连接错误提示
        console.warn("Connection lost, please try again");
    }
}
```

### 3. 请求取消管理
```javascript
class CancellableRpcManager {
    constructor() {
        this.activeRequests = new Map();
    }
    
    async call(key, url, params, settings = {}) {
        // 取消之前的同类请求
        this.cancel(key);
        
        const promise = rpc(url, params, settings);
        this.activeRequests.set(key, promise);
        
        try {
            const result = await promise;
            this.activeRequests.delete(key);
            return result;
        } catch (error) {
            this.activeRequests.delete(key);
            if (!(error instanceof ConnectionAbortedError)) {
                throw error;
            }
            return null;
        }
    }
    
    cancel(key) {
        const request = this.activeRequests.get(key);
        if (request && request.abort) {
            request.abort(false); // 不抛出错误
        }
    }
    
    cancelAll() {
        for (const [key, request] of this.activeRequests) {
            if (request.abort) {
                request.abort(false);
            }
        }
        this.activeRequests.clear();
    }
}
```

### 4. RPC 事件监听
```javascript
import { rpcBus } from "@web/core/network/rpc";

class RpcMonitor {
    constructor() {
        this.setupEventListeners();
        this.activeRequests = 0;
        this.totalRequests = 0;
        this.errors = [];
    }
    
    setupEventListeners() {
        rpcBus.addEventListener("RPC:REQUEST", this.onRequest.bind(this));
        rpcBus.addEventListener("RPC:RESPONSE", this.onResponse.bind(this));
    }
    
    onRequest(event) {
        this.activeRequests++;
        this.totalRequests++;
        console.log(`RPC Request #${event.detail.data.id}:`, {
            url: event.detail.url,
            params: event.detail.data.params,
            active: this.activeRequests
        });
        
        // 显示加载指示器
        this.showLoading();
    }
    
    onResponse(event) {
        this.activeRequests--;
        const { error, result } = event.detail;
        
        if (error) {
            this.errors.push({
                timestamp: Date.now(),
                error: error,
                url: event.detail.url
            });
            console.error(`RPC Error #${event.detail.data.id}:`, error);
        } else {
            console.log(`RPC Success #${event.detail.data.id}:`, result);
        }
        
        // 隐藏加载指示器
        if (this.activeRequests === 0) {
            this.hideLoading();
        }
    }
    
    showLoading() {
        document.body.classList.add("rpc-loading");
    }
    
    hideLoading() {
        document.body.classList.remove("rpc-loading");
    }
    
    getStats() {
        return {
            active: this.activeRequests,
            total: this.totalRequests,
            errors: this.errors.length,
            errorRate: this.errors.length / this.totalRequests
        };
    }
}
```

### 5. 批量 RPC 调用
```javascript
class BatchRpcManager {
    async batchCall(calls) {
        const promises = calls.map(({ url, params, settings }, index) => 
            rpc(url, params, settings)
                .then(result => ({ index, success: true, result }))
                .catch(error => ({ index, success: false, error }))
        );
        
        const results = await Promise.all(promises);
        
        // 按原始顺序排序结果
        results.sort((a, b) => a.index - b.index);
        
        return results;
    }
    
    async sequentialCall(calls) {
        const results = [];
        
        for (const { url, params, settings } of calls) {
            try {
                const result = await rpc(url, params, settings);
                results.push({ success: true, result });
            } catch (error) {
                results.push({ success: false, error });
                // 可以选择在第一个错误时停止
                // break;
            }
        }
        
        return results;
    }
}
```

## 设计模式分析

### 1. 观察者模式 (Observer Pattern)
```javascript
rpcBus.trigger("RPC:REQUEST", { data, url, settings });
rpcBus.trigger("RPC:RESPONSE", { data, settings, result });
```

**优势**:
- **解耦**: RPC 模块与监听者解耦
- **扩展性**: 易于添加新的监听器
- **调试友好**: 便于监控和调试

### 2. 工厂模式 (Factory Pattern)
```javascript
function makeErrorFromResponse(response) {
    const error = new RPCError();
    // 配置错误对象
    return error;
}
```

**作用**:
- **统一创建**: 统一的错误对象创建
- **配置封装**: 隐藏错误对象配置细节
- **类型安全**: 确保错误对象结构一致

### 3. 命令模式 (Command Pattern)
```javascript
const data = {
    id: rpcId++,
    jsonrpc: "2.0",
    method: "call",
    params: params,
};
```

**特点**:
- **请求封装**: 将调用封装为数据对象
- **序列化**: 便于网络传输
- **标准化**: 遵循 JSON-RPC 协议

## 性能优化

### 1. 请求去重
```javascript
class DeduplicatedRpc {
    constructor() {
        this.pendingRequests = new Map();
    }
    
    async call(url, params, settings = {}) {
        const key = this.generateKey(url, params);
        
        if (this.pendingRequests.has(key)) {
            return this.pendingRequests.get(key);
        }
        
        const promise = rpc(url, params, settings);
        this.pendingRequests.set(key, promise);
        
        try {
            const result = await promise;
            return result;
        } finally {
            this.pendingRequests.delete(key);
        }
    }
    
    generateKey(url, params) {
        return `${url}:${JSON.stringify(params)}`;
    }
}
```

### 2. 请求缓存
```javascript
class CachedRpc {
    constructor(ttl = 60000) {
        this.cache = new Map();
        this.ttl = ttl;
    }
    
    async call(url, params, settings = {}, useCache = true) {
        if (!useCache) {
            return rpc(url, params, settings);
        }
        
        const key = this.generateKey(url, params);
        const cached = this.cache.get(key);
        
        if (cached && Date.now() - cached.timestamp < this.ttl) {
            return cached.result;
        }
        
        const result = await rpc(url, params, settings);
        this.cache.set(key, {
            result,
            timestamp: Date.now()
        });
        
        return result;
    }
    
    clearCache() {
        this.cache.clear();
    }
}
```

## 测试策略

### 1. 模拟 RPC 调用
```javascript
// 测试用的 RPC 模拟
const mockRpc = {
    _responses: new Map(),
    
    setResponse(url, params, response) {
        const key = `${url}:${JSON.stringify(params)}`;
        this._responses.set(key, response);
    },
    
    async call(url, params, settings = {}) {
        const key = `${url}:${JSON.stringify(params)}`;
        const response = this._responses.get(key);
        
        if (!response) {
            throw new Error(`No mock response for ${key}`);
        }
        
        if (response.error) {
            throw makeErrorFromResponse(response.error);
        }
        
        return response.result;
    }
};

// 测试用例
describe("RPC Module", () => {
    beforeEach(() => {
        // 替换真实的 RPC 函数
        rpc._rpc = mockRpc.call.bind(mockRpc);
    });
    
    test("should handle successful response", async () => {
        mockRpc.setResponse("/test", { method: "test" }, {
            result: { success: true }
        });
        
        const result = await rpc("/test", { method: "test" });
        expect(result).toEqual({ success: true });
    });
    
    test("should handle error response", async () => {
        mockRpc.setResponse("/test", { method: "error" }, {
            error: {
                code: 500,
                message: "Test error",
                data: { name: "TestError" }
            }
        });
        
        await expect(rpc("/test", { method: "error" }))
            .rejects.toThrow(RPCError);
    });
});
```

## 最佳实践

### 1. 错误处理
```javascript
// ✅ 推荐：完整的错误处理
try {
    const result = await rpc(url, params);
    return result;
} catch (error) {
    if (error instanceof ConnectionLostError) {
        // 处理连接错误
    } else if (error instanceof RPCError) {
        // 处理服务器错误
    }
    throw error;
}

// ❌ 避免：忽略错误类型
try {
    return await rpc(url, params);
} catch (error) {
    console.error(error); // 不区分错误类型
}
```

### 2. 请求取消
```javascript
// ✅ 推荐：适当的请求取消
const request = rpc(url, params);
// 在组件卸载时取消
onWillUnmount(() => {
    request.abort(false);
});

// ❌ 避免：忘记取消长时间运行的请求
const request = rpc(url, params); // 可能导致内存泄漏
```

### 3. 事件监听
```javascript
// ✅ 推荐：清理事件监听器
class Component {
    setup() {
        this.onRequest = this.onRequest.bind(this);
        rpcBus.addEventListener("RPC:REQUEST", this.onRequest);
    }
    
    onWillUnmount() {
        rpcBus.removeEventListener("RPC:REQUEST", this.onRequest);
    }
}

// ❌ 避免：忘记清理监听器
rpcBus.addEventListener("RPC:REQUEST", callback); // 可能导致内存泄漏
```

## 总结

RPC 模块是 Odoo Web 客户端与服务器通信的核心组件，它提供了：
- **标准协议**: 完整的 JSON-RPC 2.0 实现
- **错误处理**: 完善的错误类型体系和处理机制
- **事件系统**: 基于 EventBus 的监控和调试支持
- **请求管理**: 支持请求取消和生命周期管理
- **扩展性**: 易于扩展和自定义的架构设计

这个模块为 Odoo 的前后端通信提供了可靠、高效的技术基础，是整个 Web 客户端架构的重要基石。

# Odoo 文件下载模块 (Download Module) 学习资料

## 文件概述

**文件路径**: `output/@web/core/network/download.js`  
**原始路径**: `/web/static/src/core/network/download.js`  
**模块类型**: 核心基础模块 - 网络文件下载  
**代码行数**: 579 行  
**依赖关系**: 
- `@web/core/l10n/translation` - 国际化翻译
- `@web/core/network/rpc` - RPC 错误处理
- `@web/core/browser/browser` - 浏览器抽象层

## 模块功能

文件下载模块是 Odoo Web 客户端处理文件下载的核心组件，负责：
- 客户端文件下载功能
- Content-Disposition 头解析
- 多种下载方式的兼容性处理
- 服务器文件下载和错误处理
- 跨浏览器下载支持

## 架构组成

### 1. 第三方库集成
模块集成了两个重要的第三方库：

#### Content-Disposition 库 (MIT License)
- **作者**: <PERSON> Wilson
- **功能**: 解析 HTTP Content-Disposition 头
- **用途**: 提取下载文件名和参数

#### download.js 库 (MIT License)  
- **作者**: dandavis
- **版本**: v4.2
- **功能**: 客户端文件下载实现
- **特性**: 支持多种数据格式和浏览器兼容性

### 2. 核心模块结构
```
download.js
├── Content-Disposition 解析器
│   ├── RFC 2616/5987/6266 规范实现
│   ├── 参数解析和字符编码处理
│   └── 文件名提取功能
├── download.js 库
│   ├── 多格式数据支持 (Blob/File/String)
│   ├── 跨浏览器兼容性处理
│   └── 多种下载策略
└── Odoo 集成层
    ├── downloadFile() - 直接文件下载
    ├── download() - 服务器文件下载
    └── configureBlobDownloadXHR() - XHR 配置
```

## Content-Disposition 解析器

### 1. 核心正则表达式
```javascript
// 参数解析正则
const PARAM_REGEXP = /;[\x09\x20]*([!#$%&'*+.0-9A-Z^_`a-z|~-]+)[\x09\x20]*=[\x09\x20]*("(?:[\x20!\x23-\x5b\x5d-\x7e\x80-\xff]|\\[\x20-\x7e])*"|[!#$%&'*+.0-9A-Z^_`a-z|~-]+)[\x09\x20]*/g;

// 扩展值解析正则 (RFC 5987)
const EXT_VALUE_REGEXP = /^([A-Za-z0-9!#$%&+\-^_`{}~]+)'(?:[A-Za-z]{2,3}(?:-[A-Za-z]{3}){0,3}|[A-Za-z]{4,8}|)'((?:%[0-9A-Fa-f]{2}|[A-Za-z0-9!#$&+.^_`|~-])+)$/;

// 处置类型正则 (RFC 6266)
const DISPOSITION_TYPE_REGEXP = /^([!#$%&'*+.0-9A-Z^_`a-z|~-]+)[\x09\x20]*(?:$|;)/;
```

### 2. 解析流程
```javascript
function parse(string) {
    // 1. 验证输入
    if (!string || typeof string !== "string") {
        throw new TypeError("argument string is required");
    }
    
    // 2. 解析处置类型
    let match = DISPOSITION_TYPE_REGEXP.exec(string);
    if (!match) {
        throw new TypeError("invalid type format");
    }
    
    // 3. 提取参数
    const type = match[1].toLowerCase();
    const params = {};
    
    // 4. 处理扩展值和标准值
    while ((match = PARAM_REGEXP.exec(string))) {
        let key = match[1].toLowerCase();
        let value = match[2];
        
        if (key.indexOf("*") + 1 === key.length) {
            // RFC 5987 扩展值
            key = key.slice(0, -1);
            value = decodefield(value);
        } else if (value[0] === '"') {
            // 引用字符串
            value = value.substr(1, value.length - 2)
                .replace(QESC_REGEXP, "$1");
        }
        
        params[key] = value;
    }
    
    return new ContentDisposition(type, params);
}
```

### 3. 字符编码处理
```javascript
function decodefield(str) {
    const match = EXT_VALUE_REGEXP.exec(str);
    if (!match) {
        throw new TypeError("invalid extended field value");
    }
    
    const charset = match[1].toLowerCase();
    const encoded = match[2];
    
    switch (charset) {
        case "iso-8859-1":
            return encoded
                .replace(HEX_ESCAPE_REPLACE_REGEXP, pdecode)
                .replace(NON_LATIN1_REGEXP, "?");
        case "utf-8":
            return decodeURIComponent(encoded);
        default:
            throw new TypeError("unsupported charset in extended field");
    }
}
```

## 下载库核心功能

### 1. 多格式数据支持
```javascript
function _download(data, filename, mimetype) {
    let payload = data;
    let mimeType = mimetype || "application/octet-stream";
    let fileName = filename || "download";
    
    // URL 下载支持
    if (url && url.length < 2048) {
        return new Promise((resolve, reject) => {
            let xhr = new browser.XMLHttpRequest();
            xhr.open("GET", url, true);
            configureBlobDownloadXHR(xhr, {
                onSuccess: resolve,
                onFailure: reject,
                url
            });
            xhr.send();
        });
    }
    
    // Data URL 处理
    if (/^data:[\w+\-]+\/[\w+\-]+[,;]/.test(payload)) {
        if (payload.length > 1024 * 1024 * 1.999) {
            payload = dataUrlToBlob(payload);
        }
    }
    
    // Blob 创建
    const blob = payload instanceof Blob ? 
        payload : new Blob([payload], { type: mimeType });
}
```

### 2. 跨浏览器下载策略
```javascript
function saver(url, winMode) {
    // HTML5 a[download] 支持
    if ("download" in anchor) {
        anchor.href = url;
        anchor.setAttribute("download", fileName);
        anchor.click();
        return true;
    }
    
    // Safari 特殊处理
    if (/(Version)\/(\d+)\.(\d+)(?:\.(\d+))?.*Safari\//.test(navigator.userAgent)) {
        if (!window.open(url)) {
            if (confirm("Displaying New Document\n\nUse Save As...")) {
                location.href = url;
            }
        }
        return true;
    }
    
    // iframe 下载回退
    let f = document.createElement("iframe");
    document.body.appendChild(f);
    f.src = url;
    setTimeout(() => document.body.removeChild(f), 333);
}
```

### 3. IE 兼容性处理
```javascript
// IE10+ 支持
if (navigator.msSaveBlob) {
    return navigator.msSaveBlob(blob, fileName);
}

// 现代浏览器
if (self.URL) {
    saver(self.URL.createObjectURL(blob), true);
} else {
    // 回退到 Data URL
    if (typeof blob === "string") {
        try {
            return saver(`data:${mimeType};base64,${self.btoa(blob)}`);
        } catch {
            return saver(`data:${mimeType},${encodeURIComponent(blob)}`);
        }
    }
}
```

## Odoo 集成 API

### 1. downloadFile() 函数
```javascript
function downloadFile(data, filename, mimetype) {
    return downloadFile._download(data, filename, mimetype);
}
```

**用途**: 直接下载内存中的数据
**参数**:
- `data`: 要下载的数据 (Blob/File/String)
- `filename`: 文件名
- `mimetype`: MIME 类型

**使用示例**:
```javascript
// 下载文本文件
downloadFile("Hello World", "hello.txt", "text/plain");

// 下载 JSON 数据
const jsonData = JSON.stringify({name: "test"});
downloadFile(jsonData, "data.json", "application/json");

// 下载 Blob
const blob = new Blob(["content"], {type: "text/plain"});
downloadFile(blob, "file.txt");
```

### 2. download() 函数
```javascript
function download(options) {
    return download._download(options);
}
```

**用途**: 从服务器下载文件
**参数**:
- `options.url`: 服务器 URL
- `options.data`: POST 数据
- `options.form`: HTML 表单元素

**使用示例**:
```javascript
// URL 下载
download({
    url: "/web/content/123",
    data: { model: "ir.attachment", id: 123 }
});

// 表单下载
const form = document.getElementById("downloadForm");
download({ form: form });
```

### 3. configureBlobDownloadXHR() 函数
```javascript
function configureBlobDownloadXHR(xhr, { onSuccess, onFailure, url } = {}) {
    xhr.responseType = "blob";
    xhr.onload = () => {
        const mimetype = xhr.response.type;
        const header = xhr.getResponseHeader("Content-Disposition");
        const filename = header ? parse(header).parameters.filename : null;
        
        if (xhr.status === 200 && (mimetype !== "text/html" || filename)) {
            _download(xhr.response, filename, mimetype);
            onSuccess(filename);
        } else {
            // 错误处理
            handleDownloadError(xhr, onFailure);
        }
    };
}
```

**功能**: 配置 XHR 请求用于文件下载
**特性**:
- 自动解析 Content-Disposition 头
- 错误响应处理
- 连接丢失检测

## 错误处理机制

### 1. 下载错误处理
```javascript
function handleDownloadError(xhr, onFailure) {
    if (xhr.status === 502) {
        // 服务器连接问题 (nginx 代理)
        onFailure(new ConnectionLostError(url));
    } else {
        // 解析错误响应
        const decoder = new FileReader();
        decoder.onload = () => {
            const contents = decoder.result;
            const doc = new DOMParser().parseFromString(contents, "text/html");
            const nodes = doc.body.children.length === 0 ?
                [doc.body] : doc.body.children;

            let error;
            try {
                // 尝试解析序列化的 Python 错误
                const node = nodes[1] || nodes[0];
                error = JSON.parse(node.textContent);
            } catch {
                // 通用错误格式
                error = {
                    message: "Arbitrary Uncaught Python Exception",
                    data: {
                        debug: `${xhr.status}\n${nodes[0]?.textContent || ""}`
                    }
                };
            }
            error = makeErrorFromResponse(error);
            onFailure(error);
        };
        decoder.readAsText(xhr.response);
    }
}
```

### 2. 连接错误处理
```javascript
xhr.onerror = () => {
    onFailure(new ConnectionLostError(url));
};
```

### 3. Content-Disposition 解析错误
```javascript
// 类型格式错误
if (!match) {
    throw new TypeError("invalid type format");
}

// 参数格式错误
if (match.index !== index) {
    throw new TypeError("invalid parameter format");
}

// 重复参数错误
if (names.indexOf(key) !== -1) {
    throw new TypeError("invalid duplicate parameter");
}
```

## 实际使用示例

### 1. 基本文件下载
```javascript
import { downloadFile } from "@web/core/network/download";

class FileDownloader {
    downloadText(content, filename) {
        downloadFile(content, filename, "text/plain");
    }

    downloadJSON(data, filename) {
        const jsonString = JSON.stringify(data, null, 2);
        downloadFile(jsonString, filename, "application/json");
    }

    downloadCSV(csvData, filename) {
        downloadFile(csvData, filename, "text/csv");
    }

    downloadBlob(blob, filename) {
        downloadFile(blob, filename);
    }
}
```

### 2. 服务器文件下载
```javascript
import { download } from "@web/core/network/download";

class ServerDownloader {
    async downloadReport(reportId, format = "pdf") {
        try {
            await download({
                url: "/web/report/download",
                data: {
                    report_id: reportId,
                    format: format,
                    context: JSON.stringify(this.env.context)
                }
            });
        } catch (error) {
            this.notification.add(
                _t("Download failed: %s", error.message),
                { type: "danger" }
            );
        }
    }

    async downloadAttachment(attachmentId) {
        await download({
            url: "/web/content",
            data: {
                model: "ir.attachment",
                id: attachmentId,
                download: true
            }
        });
    }

    async exportData(model, domain, fields) {
        await download({
            url: "/web/export/csv",
            data: {
                model: model,
                domain: JSON.stringify(domain),
                fields: JSON.stringify(fields)
            }
        });
    }
}
```

### 3. 表单下载
```javascript
class FormDownloader {
    setupDownloadForm() {
        const form = document.createElement("form");
        form.method = "POST";
        form.action = "/web/report/pdf";
        form.style.display = "none";

        // 添加隐藏字段
        const fields = {
            report_name: "account.invoice",
            context: JSON.stringify(this.env.context),
            ids: JSON.stringify([1, 2, 3])
        };

        Object.entries(fields).forEach(([key, value]) => {
            const input = document.createElement("input");
            input.type = "hidden";
            input.name = key;
            input.value = value;
            form.appendChild(input);
        });

        document.body.appendChild(form);
        return form;
    }

    async downloadFromForm() {
        const form = this.setupDownloadForm();
        try {
            await download({ form: form });
        } finally {
            document.body.removeChild(form);
        }
    }
}
```

### 4. 高级下载配置
```javascript
import { configureBlobDownloadXHR } from "@web/core/network/download";

class AdvancedDownloader {
    async downloadWithProgress(url, data) {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            xhr.open("POST", url);

            // 配置下载处理
            configureBlobDownloadXHR(xhr, {
                onSuccess: (filename) => {
                    console.log(`Downloaded: ${filename}`);
                    resolve(filename);
                },
                onFailure: (error) => {
                    console.error("Download failed:", error);
                    reject(error);
                },
                url: url
            });

            // 添加进度监听
            xhr.upload.onprogress = (event) => {
                if (event.lengthComputable) {
                    const progress = (event.loaded / event.total) * 100;
                    this.updateProgress(progress);
                }
            };

            // 发送数据
            const formData = new FormData();
            Object.entries(data).forEach(([key, value]) => {
                formData.append(key, value);
            });

            xhr.send(formData);
        });
    }

    updateProgress(progress) {
        console.log(`Download progress: ${progress.toFixed(2)}%`);
    }
}
```

## 浏览器兼容性

### 1. 现代浏览器支持
- **Chrome 14+**: 完整支持
- **Firefox 20+**: 完整支持
- **Safari 10+**: 完整支持
- **Edge 12+**: 完整支持

### 2. 功能特性支持
```javascript
// HTML5 download 属性检测
const supportsDownload = "download" in document.createElement("a");

// Blob 支持检测
const supportsBlob = typeof Blob !== "undefined";

// URL.createObjectURL 支持检测
const supportsObjectURL = typeof URL !== "undefined" && URL.createObjectURL;

// FileReader 支持检测
const supportsFileReader = typeof FileReader !== "undefined";
```

### 3. 回退策略
```javascript
// 1. HTML5 a[download] (首选)
// 2. URL.createObjectURL + click
// 3. msSaveBlob (IE10+)
// 4. Data URL + window.open
// 5. iframe 下载 (最后回退)
```

## 性能优化

### 1. 内存管理
```javascript
// 及时释放 Object URL
setTimeout(() => {
    self.URL.revokeObjectURL(anchor.href);
}, 250);

// 清理临时 DOM 元素
setTimeout(() => {
    document.body.removeChild(anchor);
}, 66);
```

### 2. 大文件处理
```javascript
// Data URL 大小限制
if (payload.length > 1024 * 1024 * 1.999 && myBlob !== toString) {
    payload = dataUrlToBlob(payload);
    mimeType = payload.type || defaultMime;
}
```

### 3. 缓存策略
```javascript
// 避免重复解析 Content-Disposition
const dispositionCache = new Map();

function getCachedDisposition(header) {
    if (!dispositionCache.has(header)) {
        dispositionCache.set(header, parse(header));
    }
    return dispositionCache.get(header);
}
```

## 安全考虑

### 1. CSRF 保护
```javascript
// 自动添加 CSRF 令牌
if (odoo.csrf_token) {
    data.append("csrf_token", odoo.csrf_token);
}
```

### 2. 文件名安全
```javascript
// 文件名清理
function sanitizeFilename(filename) {
    return filename.replace(/[<>:"/\\|?*]/g, "_");
}
```

### 3. MIME 类型验证
```javascript
// 验证 MIME 类型
function isValidMimeType(mimetype) {
    return /^[\w-]+\/[\w-]+$/.test(mimetype);
}
```

## 调试技巧

### 1. 下载状态监控
```javascript
// 添加下载日志
console.log("Download started:", { url, filename, mimetype });

// 监控下载完成
xhr.onload = () => {
    console.log("Download completed:", {
        status: xhr.status,
        contentType: xhr.response.type,
        size: xhr.response.size
    });
};
```

### 2. 错误诊断
```javascript
// 详细错误信息
xhr.onerror = (event) => {
    console.error("Download error:", {
        event,
        readyState: xhr.readyState,
        status: xhr.status,
        statusText: xhr.statusText
    });
};
```

## 最佳实践

### 1. 错误处理
```javascript
// ✅ 推荐：完整的错误处理
try {
    await download(options);
    this.notification.add(_t("Download completed"), { type: "success" });
} catch (error) {
    this.notification.add(_t("Download failed: %s", error.message), { type: "danger" });
}

// ❌ 避免：忽略错误
download(options); // 可能静默失败
```

### 2. 文件名处理
```javascript
// ✅ 推荐：提供默认文件名
downloadFile(data, filename || "download.txt", mimetype);

// ❌ 避免：空文件名
downloadFile(data, "", mimetype);
```

### 3. MIME 类型指定
```javascript
// ✅ 推荐：明确指定 MIME 类型
downloadFile(jsonData, "data.json", "application/json");

// ❌ 避免：依赖默认类型
downloadFile(jsonData, "data.json"); // 可能被识别为 octet-stream
```

## 总结

文件下载模块是 Odoo Web 客户端的重要组件，它提供了：
- **完整的下载解决方案**: 支持多种数据格式和下载方式
- **跨浏览器兼容性**: 从现代浏览器到传统 IE 的全面支持
- **标准协议支持**: 完整的 HTTP Content-Disposition 解析
- **错误处理机制**: 完善的错误检测和用户反馈
- **性能优化**: 内存管理和大文件处理优化

这个模块为 Odoo 的文件下载功能提供了可靠、高效的技术基础，确保用户在各种环境下都能获得一致的下载体验。

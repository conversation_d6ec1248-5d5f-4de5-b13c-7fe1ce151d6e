# Odoo 拖拽工具 (Draggable Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/draggable.js`  
**原始路径**: `/web/static/src/core/utils/draggable.js`  
**模块类型**: 核心工具模块 - 拖拽功能工具  
**代码行数**: 55 行  
**依赖关系**: 
- `@web/core/utils/draggable_hook_builder_owl` - OWL拖拽Hook构建器
- `@web/core/utils/objects` - 对象工具 (pick函数)

## 模块功能

拖拽工具模块是 Odoo Web 客户端的核心工具库，提供了强大的拖拽功能支持。该模块基于 OWL 框架，实现了一个高级的拖拽Hook，支持：
- 元素拖拽检测和处理
- 拖拽生命周期管理
- 自定义拖拽句柄和忽略区域
- 拖拽状态跟踪
- 丰富的事件回调系统

这个拖拽系统是构建现代交互式用户界面的重要基础。

## 核心类型定义

### DraggableParams - 拖拽参数配置
```typescript
interface DraggableParams {
    // 必需参数
    ref: { el: HTMLElement | null };
    elements: string;
    
    // 可选参数
    enable?: boolean | (() => boolean);
    handle?: string | (() => string);
    ignore?: string | (() => string);
    cursor?: string | (() => string);
    
    // 事件处理器
    onDragStart?: (params: DraggableHandlerParams) => any;
    onDrag?: (params: DraggableHandlerParams) => any;
    onDragEnd?: (params: DraggableHandlerParams) => any;
    onDrop?: (params: DraggableHandlerParams) => any;
}
```

**参数说明**:
- **ref**: 包含目标DOM元素的引用对象
- **elements**: 定义可拖拽元素的CSS选择器
- **enable**: 是否启用拖拽功能（可以是布尔值或函数）
- **handle**: 拖拽句柄选择器（只有点击句柄才能拖拽）
- **ignore**: 忽略区域选择器（点击这些区域不会触发拖拽）
- **cursor**: 拖拽时的鼠标样式

### DraggableState - 拖拽状态
```typescript
interface DraggableState {
    dragging: boolean;
}
```

**状态属性**:
- **dragging**: 当前是否正在拖拽

## 核心Hook详解

### useDraggable() - 拖拽Hook
```javascript
const useDraggable = makeDraggableHook({
    name: "useDraggable",
    onWillStartDrag: ({ ctx }) => pick(ctx.current, "element"),
    onDragStart: ({ ctx }) => pick(ctx.current, "element"),
    onDrag: ({ ctx }) => pick(ctx.current, "element"),
    onDragEnd: ({ ctx }) => pick(ctx.current, "element"),
    onDrop: ({ ctx }) => pick(ctx.current, "element"),
});
```

**功能特性**:
- **生命周期管理**: 完整的拖拽生命周期事件处理
- **上下文传递**: 自动传递当前拖拽元素信息
- **状态跟踪**: 实时跟踪拖拽状态
- **事件绑定**: 自动处理鼠标事件绑定和解绑

**使用示例**:
```javascript
// 基本拖拽实现
class DraggableComponent extends Component {
    setup() {
        this.containerRef = useRef("container");
        
        this.draggableState = useDraggable({
            ref: this.containerRef,
            elements: ".draggable-item",
            
            onDragStart: ({ element }) => {
                console.log("开始拖拽:", element);
                element.classList.add("dragging");
            },
            
            onDrag: ({ element, dx, dy }) => {
                // 更新元素位置
                const rect = element.getBoundingClientRect();
                element.style.left = (rect.left + dx) + "px";
                element.style.top = (rect.top + dy) + "px";
            },
            
            onDragEnd: ({ element }) => {
                console.log("拖拽结束:", element);
                element.classList.remove("dragging");
            },
            
            onDrop: ({ element }) => {
                console.log("拖拽放下:", element);
                this.handleDrop(element);
            }
        });
    }
    
    handleDrop(element) {
        // 处理拖拽放下逻辑
        const dropZone = document.elementFromPoint(event.clientX, event.clientY);
        if (dropZone && dropZone.classList.contains("drop-zone")) {
            dropZone.appendChild(element);
        }
    }
}
```

**实际应用场景**:
```javascript
// 1. 卡片拖拽排序
class CardSortingComponent extends Component {
    setup() {
        this.containerRef = useRef("cardContainer");
        this.cards = useState([
            { id: 1, title: "任务 1", order: 1 },
            { id: 2, title: "任务 2", order: 2 },
            { id: 3, title: "任务 3", order: 3 }
        ]);
        
        this.draggableState = useDraggable({
            ref: this.containerRef,
            elements: ".card",
            handle: ".card-header", // 只能通过卡片头部拖拽
            cursor: "grabbing",
            
            onDragStart: ({ element }) => {
                this.draggedCard = element;
                element.style.opacity = "0.5";
                element.style.transform = "rotate(5deg)";
                
                // 创建拖拽占位符
                this.placeholder = document.createElement("div");
                this.placeholder.className = "card-placeholder";
                this.placeholder.style.height = element.offsetHeight + "px";
                element.parentNode.insertBefore(this.placeholder, element.nextSibling);
            },
            
            onDrag: ({ element, clientX, clientY }) => {
                // 更新元素位置跟随鼠标
                element.style.position = "fixed";
                element.style.left = clientX - element.offsetWidth / 2 + "px";
                element.style.top = clientY - element.offsetHeight / 2 + "px";
                element.style.zIndex = "1000";
                
                // 检测插入位置
                this.updatePlaceholderPosition(clientY);
            },
            
            onDrop: ({ element }) => {
                // 恢复样式
                element.style.opacity = "";
                element.style.transform = "";
                element.style.position = "";
                element.style.left = "";
                element.style.top = "";
                element.style.zIndex = "";
                
                // 插入到占位符位置
                if (this.placeholder && this.placeholder.parentNode) {
                    this.placeholder.parentNode.insertBefore(element, this.placeholder);
                    this.placeholder.remove();
                }
                
                // 更新数据顺序
                this.updateCardOrder();
                
                this.draggedCard = null;
                this.placeholder = null;
            }
        });
    }
    
    updatePlaceholderPosition(mouseY) {
        const cards = this.containerRef.el.querySelectorAll(".card:not(.dragging)");
        let insertBefore = null;
        
        for (const card of cards) {
            const rect = card.getBoundingClientRect();
            if (mouseY < rect.top + rect.height / 2) {
                insertBefore = card;
                break;
            }
        }
        
        if (insertBefore) {
            insertBefore.parentNode.insertBefore(this.placeholder, insertBefore);
        } else {
            this.containerRef.el.appendChild(this.placeholder);
        }
    }
    
    updateCardOrder() {
        const cardElements = this.containerRef.el.querySelectorAll(".card");
        cardElements.forEach((cardEl, index) => {
            const cardId = parseInt(cardEl.dataset.cardId);
            const card = this.cards.find(c => c.id === cardId);
            if (card) {
                card.order = index + 1;
            }
        });
        
        // 重新排序
        this.cards.sort((a, b) => a.order - b.order);
    }
}

// 2. 文件拖拽上传
class FileDropZoneComponent extends Component {
    setup() {
        this.dropZoneRef = useRef("dropZone");
        this.uploadState = useState({
            isDragOver: false,
            files: []
        });
        
        this.draggableState = useDraggable({
            ref: this.dropZoneRef,
            elements: ".file-item",
            
            onDragStart: ({ element }) => {
                element.classList.add("file-dragging");
            },
            
            onDrag: ({ element, clientX, clientY }) => {
                // 检测是否拖拽到删除区域
                const deleteZone = document.querySelector(".delete-zone");
                if (deleteZone) {
                    const rect = deleteZone.getBoundingClientRect();
                    const isOverDelete = (
                        clientX >= rect.left && clientX <= rect.right &&
                        clientY >= rect.top && clientY <= rect.bottom
                    );
                    
                    deleteZone.classList.toggle("delete-active", isOverDelete);
                }
            },
            
            onDrop: ({ element, clientX, clientY }) => {
                element.classList.remove("file-dragging");
                
                // 检查是否放到删除区域
                const deleteZone = document.querySelector(".delete-zone");
                if (deleteZone && deleteZone.classList.contains("delete-active")) {
                    this.removeFile(element.dataset.fileId);
                    deleteZone.classList.remove("delete-active");
                }
            }
        });
        
        // 处理外部文件拖拽
        this.setupExternalFileDrop();
    }
    
    setupExternalFileDrop() {
        const dropZone = this.dropZoneRef.el;
        
        dropZone.addEventListener("dragover", (event) => {
            event.preventDefault();
            this.uploadState.isDragOver = true;
        });
        
        dropZone.addEventListener("dragleave", (event) => {
            if (!dropZone.contains(event.relatedTarget)) {
                this.uploadState.isDragOver = false;
            }
        });
        
        dropZone.addEventListener("drop", (event) => {
            event.preventDefault();
            this.uploadState.isDragOver = false;
            
            const files = Array.from(event.dataTransfer.files);
            this.handleFileUpload(files);
        });
    }
    
    handleFileUpload(files) {
        files.forEach(file => {
            const fileItem = {
                id: Date.now() + Math.random(),
                name: file.name,
                size: file.size,
                file: file,
                uploading: false
            };
            
            this.uploadState.files.push(fileItem);
        });
    }
    
    removeFile(fileId) {
        const index = this.uploadState.files.findIndex(f => f.id === fileId);
        if (index > -1) {
            this.uploadState.files.splice(index, 1);
        }
    }
}

// 3. 看板拖拽
class KanbanBoardComponent extends Component {
    setup() {
        this.boardRef = useRef("kanbanBoard");
        this.columns = useState([
            { id: 1, title: "待办", tasks: [] },
            { id: 2, title: "进行中", tasks: [] },
            { id: 3, title: "已完成", tasks: [] }
        ]);
        
        this.draggableState = useDraggable({
            ref: this.boardRef,
            elements: ".kanban-task",
            handle: ".task-handle",
            
            onDragStart: ({ element }) => {
                this.draggedTask = {
                    element: element,
                    taskId: element.dataset.taskId,
                    sourceColumn: element.closest(".kanban-column").dataset.columnId
                };
                
                element.classList.add("task-dragging");
                
                // 高亮可放置的列
                this.boardRef.el.querySelectorAll(".kanban-column").forEach(col => {
                    col.classList.add("drop-target");
                });
            },
            
            onDrag: ({ element, clientX, clientY }) => {
                // 更新任务位置
                element.style.position = "fixed";
                element.style.left = clientX - element.offsetWidth / 2 + "px";
                element.style.top = clientY - element.offsetHeight / 2 + "px";
                element.style.zIndex = "1000";
                element.style.transform = "rotate(3deg)";
                
                // 检测目标列
                const targetColumn = this.getColumnUnderMouse(clientX, clientY);
                this.highlightTargetColumn(targetColumn);
            },
            
            onDrop: ({ element, clientX, clientY }) => {
                element.classList.remove("task-dragging");
                element.style.position = "";
                element.style.left = "";
                element.style.top = "";
                element.style.zIndex = "";
                element.style.transform = "";
                
                // 移除高亮
                this.boardRef.el.querySelectorAll(".kanban-column").forEach(col => {
                    col.classList.remove("drop-target", "drop-hover");
                });
                
                // 处理任务移动
                const targetColumn = this.getColumnUnderMouse(clientX, clientY);
                if (targetColumn) {
                    this.moveTask(this.draggedTask, targetColumn.dataset.columnId);
                }
                
                this.draggedTask = null;
            }
        });
    }
    
    getColumnUnderMouse(x, y) {
        const columns = this.boardRef.el.querySelectorAll(".kanban-column");
        
        for (const column of columns) {
            const rect = column.getBoundingClientRect();
            if (x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom) {
                return column;
            }
        }
        
        return null;
    }
    
    highlightTargetColumn(targetColumn) {
        // 移除之前的高亮
        this.boardRef.el.querySelectorAll(".kanban-column").forEach(col => {
            col.classList.remove("drop-hover");
        });
        
        // 添加新的高亮
        if (targetColumn) {
            targetColumn.classList.add("drop-hover");
        }
    }
    
    moveTask(draggedTask, targetColumnId) {
        const sourceColumnId = draggedTask.sourceColumn;
        const taskId = draggedTask.taskId;
        
        if (sourceColumnId === targetColumnId) {
            return; // 没有移动
        }
        
        // 从源列移除任务
        const sourceColumn = this.columns.find(col => col.id == sourceColumnId);
        const taskIndex = sourceColumn.tasks.findIndex(task => task.id == taskId);
        const task = sourceColumn.tasks.splice(taskIndex, 1)[0];
        
        // 添加到目标列
        const targetColumn = this.columns.find(col => col.id == targetColumnId);
        targetColumn.tasks.push(task);
        
        // 更新任务状态
        task.status = targetColumn.title;
        
        console.log(`任务 ${task.title} 从 ${sourceColumn.title} 移动到 ${targetColumn.title}`);
    }
}
```

## 高级应用模式

### 1. 多类型拖拽系统
```javascript
class MultiTypeDragSystem extends Component {
    setup() {
        this.containerRef = useRef("container");
        this.dragState = useState({
            dragType: null,
            dragData: null
        });

        // 文件拖拽
        this.fileDraggable = useDraggable({
            ref: this.containerRef,
            elements: ".file-item",

            onDragStart: ({ element }) => {
                this.dragState.dragType = "file";
                this.dragState.dragData = {
                    fileId: element.dataset.fileId,
                    fileName: element.dataset.fileName
                };
                element.classList.add("dragging-file");
            },

            onDrop: ({ element, clientX, clientY }) => {
                this.handleFileDrop(element, clientX, clientY);
                this.resetDragState();
            }
        });

        // 文本拖拽
        this.textDraggable = useDraggable({
            ref: this.containerRef,
            elements: ".text-item",

            onDragStart: ({ element }) => {
                this.dragState.dragType = "text";
                this.dragState.dragData = {
                    textId: element.dataset.textId,
                    content: element.textContent
                };
                element.classList.add("dragging-text");
            },

            onDrop: ({ element, clientX, clientY }) => {
                this.handleTextDrop(element, clientX, clientY);
                this.resetDragState();
            }
        });

        // 图片拖拽
        this.imageDraggable = useDraggable({
            ref: this.containerRef,
            elements: ".image-item",

            onDragStart: ({ element }) => {
                this.dragState.dragType = "image";
                this.dragState.dragData = {
                    imageId: element.dataset.imageId,
                    src: element.src
                };
                element.classList.add("dragging-image");
            },

            onDrop: ({ element, clientX, clientY }) => {
                this.handleImageDrop(element, clientX, clientY);
                this.resetDragState();
            }
        });
    }

    handleFileDrop(element, x, y) {
        const dropZone = this.getDropZoneAt(x, y);
        if (dropZone) {
            switch (dropZone.dataset.type) {
                case "folder":
                    this.moveFileToFolder(this.dragState.dragData.fileId, dropZone.dataset.folderId);
                    break;
                case "trash":
                    this.deleteFile(this.dragState.dragData.fileId);
                    break;
                case "share":
                    this.shareFile(this.dragState.dragData.fileId);
                    break;
            }
        }
    }

    handleTextDrop(element, x, y) {
        const dropZone = this.getDropZoneAt(x, y);
        if (dropZone) {
            switch (dropZone.dataset.type) {
                case "editor":
                    this.insertTextToEditor(this.dragState.dragData.content, dropZone);
                    break;
                case "note":
                    this.addTextToNote(this.dragState.dragData.content, dropZone.dataset.noteId);
                    break;
            }
        }
    }

    handleImageDrop(element, x, y) {
        const dropZone = this.getDropZoneAt(x, y);
        if (dropZone) {
            switch (dropZone.dataset.type) {
                case "gallery":
                    this.addImageToGallery(this.dragState.dragData.imageId, dropZone.dataset.galleryId);
                    break;
                case "canvas":
                    this.addImageToCanvas(this.dragState.dragData.src, x, y);
                    break;
            }
        }
    }

    getDropZoneAt(x, y) {
        const element = document.elementFromPoint(x, y);
        return element ? element.closest(".drop-zone") : null;
    }

    resetDragState() {
        this.dragState.dragType = null;
        this.dragState.dragData = null;

        // 移除所有拖拽样式
        this.containerRef.el.querySelectorAll(".dragging-file, .dragging-text, .dragging-image")
            .forEach(el => {
                el.classList.remove("dragging-file", "dragging-text", "dragging-image");
            });
    }
}
```

### 2. 拖拽约束系统
```javascript
class ConstrainedDragSystem extends Component {
    setup() {
        this.containerRef = useRef("container");
        this.constraints = useState({
            horizontal: false,
            vertical: false,
            bounds: null,
            grid: null,
            magneticPoints: []
        });

        this.draggableState = useDraggable({
            ref: this.containerRef,
            elements: ".constrained-item",

            onDragStart: ({ element }) => {
                this.originalPosition = {
                    x: parseInt(element.style.left) || 0,
                    y: parseInt(element.style.top) || 0
                };

                // 根据元素类型设置约束
                this.setConstraintsForElement(element);
            },

            onDrag: ({ element, dx, dy, clientX, clientY }) => {
                let newX = this.originalPosition.x + dx;
                let newY = this.originalPosition.y + dy;

                // 应用约束
                const constrainedPos = this.applyConstraints(newX, newY, element);

                element.style.left = constrainedPos.x + "px";
                element.style.top = constrainedPos.y + "px";

                // 显示约束指示器
                this.showConstraintIndicators(constrainedPos, element);
            },

            onDrop: ({ element }) => {
                this.hideConstraintIndicators();
                this.snapToFinalPosition(element);
            }
        });
    }

    setConstraintsForElement(element) {
        const type = element.dataset.constraintType;

        switch (type) {
            case "horizontal":
                this.constraints.horizontal = true;
                this.constraints.vertical = false;
                break;
            case "vertical":
                this.constraints.horizontal = false;
                this.constraints.vertical = true;
                break;
            case "grid":
                this.constraints.grid = {
                    x: parseInt(element.dataset.gridX) || 20,
                    y: parseInt(element.dataset.gridY) || 20
                };
                break;
            case "bounded":
                this.constraints.bounds = this.getBoundsForElement(element);
                break;
            case "magnetic":
                this.constraints.magneticPoints = this.getMagneticPoints();
                break;
        }
    }

    applyConstraints(x, y, element) {
        let constrainedX = x;
        let constrainedY = y;

        // 水平约束
        if (this.constraints.horizontal) {
            constrainedY = this.originalPosition.y;
        }

        // 垂直约束
        if (this.constraints.vertical) {
            constrainedX = this.originalPosition.x;
        }

        // 网格约束
        if (this.constraints.grid) {
            constrainedX = Math.round(constrainedX / this.constraints.grid.x) * this.constraints.grid.x;
            constrainedY = Math.round(constrainedY / this.constraints.grid.y) * this.constraints.grid.y;
        }

        // 边界约束
        if (this.constraints.bounds) {
            constrainedX = Math.max(this.constraints.bounds.left,
                          Math.min(this.constraints.bounds.right - element.offsetWidth, constrainedX));
            constrainedY = Math.max(this.constraints.bounds.top,
                          Math.min(this.constraints.bounds.bottom - element.offsetHeight, constrainedY));
        }

        // 磁性约束
        if (this.constraints.magneticPoints.length > 0) {
            const magnetic = this.findNearestMagneticPoint(constrainedX, constrainedY);
            if (magnetic.distance < 20) {
                constrainedX = magnetic.point.x;
                constrainedY = magnetic.point.y;
            }
        }

        return { x: constrainedX, y: constrainedY };
    }

    findNearestMagneticPoint(x, y) {
        let nearest = null;
        let minDistance = Infinity;

        for (const point of this.constraints.magneticPoints) {
            const distance = Math.sqrt(Math.pow(x - point.x, 2) + Math.pow(y - point.y, 2));
            if (distance < minDistance) {
                minDistance = distance;
                nearest = point;
            }
        }

        return { point: nearest, distance: minDistance };
    }

    showConstraintIndicators(position, element) {
        // 显示网格线
        if (this.constraints.grid) {
            this.showGridLines();
        }

        // 显示磁性点
        if (this.constraints.magneticPoints.length > 0) {
            this.highlightNearbyMagneticPoints(position.x, position.y);
        }

        // 显示边界
        if (this.constraints.bounds) {
            this.showBoundaryIndicator();
        }
    }

    showGridLines() {
        // 创建网格线覆盖层
        if (!this.gridOverlay) {
            this.gridOverlay = document.createElement("div");
            this.gridOverlay.className = "grid-overlay";
            this.containerRef.el.appendChild(this.gridOverlay);
        }
        this.gridOverlay.style.display = "block";
    }

    highlightNearbyMagneticPoints(x, y) {
        this.constraints.magneticPoints.forEach(point => {
            const distance = Math.sqrt(Math.pow(x - point.x, 2) + Math.pow(y - point.y, 2));
            const pointElement = document.querySelector(`[data-magnetic-id="${point.id}"]`);
            if (pointElement) {
                pointElement.classList.toggle("magnetic-active", distance < 30);
            }
        });
    }
}
```

### 3. 拖拽性能优化
```javascript
class OptimizedDragSystem extends Component {
    setup() {
        this.containerRef = useRef("container");
        this.dragState = useState({
            isDragging: false,
            draggedElement: null
        });

        // 性能优化配置
        this.performanceConfig = {
            useTransform: true,
            throttleInterval: 16, // 60fps
            useVirtualization: true,
            batchUpdates: true
        };

        this.draggableState = useDraggable({
            ref: this.containerRef,
            elements: ".optimized-item",

            onDragStart: ({ element }) => {
                this.dragState.isDragging = true;
                this.dragState.draggedElement = element;

                // 优化：使用 transform 而不是 left/top
                if (this.performanceConfig.useTransform) {
                    element.style.transform = "translate3d(0, 0, 0)";
                    element.style.willChange = "transform";
                }

                // 优化：减少重绘
                element.style.pointerEvents = "none";

                // 优化：创建拖拽代理
                this.createDragProxy(element);
            },

            onDrag: this.throttle(({ element, dx, dy }) => {
                if (this.performanceConfig.useTransform) {
                    // 使用 transform 提高性能
                    element.style.transform = `translate3d(${dx}px, ${dy}px, 0)`;
                } else {
                    // 传统方式
                    element.style.left = (element.offsetLeft + dx) + "px";
                    element.style.top = (element.offsetTop + dy) + "px";
                }

                // 批量更新其他元素
                if (this.performanceConfig.batchUpdates) {
                    this.batchUpdateOtherElements();
                }
            }, this.performanceConfig.throttleInterval),

            onDrop: ({ element }) => {
                this.dragState.isDragging = false;
                this.dragState.draggedElement = null;

                // 恢复样式
                element.style.pointerEvents = "";
                element.style.willChange = "";

                // 移除拖拽代理
                this.removeDragProxy();

                // 最终位置计算
                this.finalizePosition(element);
            }
        });

        // 虚拟化支持
        if (this.performanceConfig.useVirtualization) {
            this.setupVirtualization();
        }
    }

    throttle(func, delay) {
        let timeoutId;
        let lastExecTime = 0;

        return function (...args) {
            const currentTime = Date.now();

            if (currentTime - lastExecTime > delay) {
                func.apply(this, args);
                lastExecTime = currentTime;
            } else {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => {
                    func.apply(this, args);
                    lastExecTime = Date.now();
                }, delay - (currentTime - lastExecTime));
            }
        };
    }

    createDragProxy(element) {
        this.dragProxy = element.cloneNode(true);
        this.dragProxy.className += " drag-proxy";
        this.dragProxy.style.position = "fixed";
        this.dragProxy.style.zIndex = "9999";
        this.dragProxy.style.pointerEvents = "none";
        this.dragProxy.style.opacity = "0.8";

        document.body.appendChild(this.dragProxy);

        // 隐藏原始元素
        element.style.opacity = "0.3";
    }

    removeDragProxy() {
        if (this.dragProxy) {
            this.dragProxy.remove();
            this.dragProxy = null;
        }

        // 恢复原始元素
        if (this.dragState.draggedElement) {
            this.dragState.draggedElement.style.opacity = "";
        }
    }

    batchUpdateOtherElements() {
        // 使用 requestAnimationFrame 批量更新
        if (!this.updateScheduled) {
            this.updateScheduled = true;
            requestAnimationFrame(() => {
                this.performBatchUpdates();
                this.updateScheduled = false;
            });
        }
    }

    performBatchUpdates() {
        // 批量更新其他需要响应拖拽的元素
        const otherElements = this.containerRef.el.querySelectorAll(".responsive-item");

        otherElements.forEach(element => {
            if (element !== this.dragState.draggedElement) {
                this.updateElementResponse(element);
            }
        });
    }

    setupVirtualization() {
        // 只渲染可见区域的元素
        this.visibleItems = new Set();
        this.observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.visibleItems.add(entry.target);
                } else {
                    this.visibleItems.delete(entry.target);
                }
            });
        });

        // 观察所有可拖拽元素
        this.containerRef.el.querySelectorAll(".optimized-item").forEach(item => {
            this.observer.observe(item);
        });
    }
}
```

## 最佳实践

### 1. 性能优化
```javascript
// ✅ 推荐：使用 transform 而不是 left/top
const optimizedDraggable = useDraggable({
    ref: containerRef,
    elements: ".item",

    onDrag: ({ element, dx, dy }) => {
        // 使用 transform 提高性能
        element.style.transform = `translate3d(${dx}px, ${dy}px, 0)`;
        element.style.willChange = "transform";
    },

    onDrop: ({ element }) => {
        // 清理性能优化属性
        element.style.willChange = "";
    }
});

// ❌ 避免：频繁修改 left/top 属性
const slowDraggable = useDraggable({
    ref: containerRef,
    elements: ".item",

    onDrag: ({ element, dx, dy }) => {
        // 这会触发重排，性能较差
        element.style.left = (element.offsetLeft + dx) + "px";
        element.style.top = (element.offsetTop + dy) + "px";
    }
});
```

### 2. 内存管理
```javascript
// ✅ 推荐：正确清理资源
class DraggableComponent extends Component {
    setup() {
        this.containerRef = useRef("container");
        this.dragState = useState({ dragging: false });

        this.draggableState = useDraggable({
            ref: this.containerRef,
            elements: ".item",

            onDragStart: ({ element }) => {
                this.dragState.dragging = true;
                // 存储必要的状态
                this.dragContext = {
                    startTime: Date.now(),
                    element: element
                };
            },

            onDrop: ({ element }) => {
                this.dragState.dragging = false;
                // 清理状态
                this.dragContext = null;
            }
        });
    }

    willUnmount() {
        // 组件卸载时清理
        if (this.dragContext) {
            this.dragContext = null;
        }
    }
}
```

### 3. 错误处理
```javascript
// ✅ 推荐：添加错误处理
const robustDraggable = useDraggable({
    ref: containerRef,
    elements: ".item",

    onDragStart: ({ element }) => {
        try {
            // 验证元素状态
            if (!element || !element.parentNode) {
                throw new Error("无效的拖拽元素");
            }

            // 执行拖拽开始逻辑
            element.classList.add("dragging");
        } catch (error) {
            console.error("拖拽开始失败:", error);
        }
    },

    onDrop: ({ element }) => {
        try {
            // 安全地处理拖拽结束
            if (element && element.classList) {
                element.classList.remove("dragging");
            }
        } catch (error) {
            console.error("拖拽结束失败:", error);
        }
    }
});
```

## 总结

Odoo 拖拽工具模块提供了强大而灵活的拖拽功能：

**核心优势**:
- **生命周期管理**: 完整的拖拽生命周期事件处理
- **灵活配置**: 支持句柄、忽略区域、启用条件等配置
- **状态跟踪**: 实时跟踪拖拽状态
- **事件丰富**: 提供多种拖拽事件回调
- **OWL集成**: 与OWL框架完美集成

**适用场景**:
- 卡片拖拽排序
- 文件拖拽上传
- 看板任务管理
- 图形编辑器
- 数据可视化

**性能优化**:
- 使用transform而不是left/top
- 节流拖拽事件处理
- 虚拟化大量元素
- 批量更新DOM

这个拖拽工具为 Odoo Web 客户端提供了强大的交互能力，是构建现代用户界面的重要基础。

# Odoo 时间工具 (Timing Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/timing.js`  
**原始路径**: `/web/static/src/core/utils/timing.js`  
**模块类型**: 核心工具模块 - 时间和定时工具  
**代码行数**: 216 行  
**依赖关系**: 
- `@web/core/browser/browser` - 浏览器抽象层
- `@odoo/owl` - OWL 框架 (onWillUnmount, useComponent)

## 模块功能

时间工具模块是 Odoo Web 客户端的核心工具库，提供了丰富的时间控制和性能优化功能，包括：
- 函数防抖（Debounce）
- 动画帧节流（Animation Frame Throttling）
- 批处理执行（Batched Execution）
- 递归动画帧（Recurring Animation Frame）
- Hook 集成（useDebounced, useThrottleForAnimation）

## 核心函数详解

### 1. debounce() - 防抖函数
```javascript
function debounce(func, delay, options) {
    // 支持 leading 和 trailing 边缘执行
    // 支持 Promise 返回值
    // 支持动画帧延迟
    // 提供 cancel 方法
}
```

**功能特性**:
- **延迟执行**: 在指定延迟后执行函数
- **重复调用合并**: 重复调用会重置延迟计时器
- **边缘控制**: 支持前沿（leading）和后沿（trailing）执行
- **Promise 支持**: 返回 Promise 以支持异步操作
- **动画帧支持**: 支持 "animationFrame" 作为延迟参数
- **取消功能**: 提供 cancel 方法取消待执行的函数

**使用示例**:
```javascript
// 基本防抖 - 300ms 延迟
const debouncedSearch = debounce((query) => {
    console.log("搜索:", query);
    return fetch(`/api/search?q=${query}`);
}, 300);

// 调用示例
debouncedSearch("hello");
debouncedSearch("hello world"); // 前一个调用被取消
// 只有最后一个调用会在 300ms 后执行

// 前沿执行（立即执行第一次调用）
const debouncedSave = debounce(saveData, 1000, { leading: true });

// 动画帧防抖（在下一个动画帧执行）
const debouncedRender = debounce(updateUI, "animationFrame");

// 取消待执行的函数
debouncedSearch.cancel(); // 取消执行
debouncedSearch.cancel(true); // 立即执行并取消
```

**实际应用场景**:
```javascript
// 1. 搜索输入优化
class SearchComponent extends Component {
    setup() {
        this.debouncedSearch = debounce(this.performSearch.bind(this), 300);
    }
    
    onInputChange(event) {
        const query = event.target.value;
        this.debouncedSearch(query);
    }
    
    async performSearch(query) {
        if (query.length < 2) return;
        
        try {
            const results = await fetch(`/api/search?q=${query}`);
            this.updateResults(await results.json());
        } catch (error) {
            console.error("搜索失败:", error);
        }
    }
}

// 2. 窗口大小调整处理
class ResponsiveLayout {
    constructor() {
        this.debouncedResize = debounce(this.handleResize.bind(this), 250);
        window.addEventListener('resize', this.debouncedResize);
    }
    
    handleResize() {
        console.log("窗口大小变化:", window.innerWidth, window.innerHeight);
        this.updateLayout();
    }
    
    updateLayout() {
        // 重新计算布局
        this.calculateDimensions();
        this.repositionElements();
    }
}

// 3. 表单自动保存
class AutoSaveForm {
    constructor(formElement) {
        this.form = formElement;
        this.debouncedSave = debounce(this.saveForm.bind(this), 2000);
        
        // 监听表单变化
        this.form.addEventListener('input', () => {
            this.debouncedSave();
        });
    }
    
    async saveForm() {
        const formData = new FormData(this.form);
        
        try {
            await fetch('/api/save-draft', {
                method: 'POST',
                body: formData
            });
            console.log("草稿已保存");
        } catch (error) {
            console.error("保存失败:", error);
        }
    }
}

// 4. API 请求防抖
class APIClient {
    constructor() {
        this.debouncedRequest = debounce(this.makeRequest.bind(this), 100);
    }
    
    async makeRequest(endpoint, options) {
        return fetch(endpoint, options);
    }
    
    // 防止重复请求
    async get(endpoint) {
        return this.debouncedRequest(endpoint, { method: 'GET' });
    }
}
```

### 2. throttleForAnimation() - 动画帧节流
```javascript
function throttleForAnimation(func) {
    // 将函数调用限制在动画帧频率
    // 第一次调用立即执行（leading edge）
    // 后续调用在下一个动画帧执行
    // 只保留最后一次调用的参数
}
```

**功能特性**:
- **动画帧同步**: 与浏览器的重绘周期同步
- **性能优化**: 避免不必要的高频调用
- **立即执行**: 第一次调用立即执行
- **参数保留**: 保留最后一次调用的参数
- **Promise 支持**: 返回 Promise 以支持异步操作

**使用示例**:
```javascript
// 基本动画帧节流
const throttledUpdate = throttleForAnimation((data) => {
    console.log("更新UI:", data);
    updateDOM(data);
});

// 高频调用，但只在动画帧执行
throttledUpdate("data1");
throttledUpdate("data2");
throttledUpdate("data3");
// 只有 "data1" 立即执行，"data3" 在下一个动画帧执行

// 取消待执行的函数
throttledUpdate.cancel();
```

**实际应用场景**:
```javascript
// 1. 滚动事件优化
class ScrollHandler {
    constructor() {
        this.throttledScroll = throttleForAnimation(this.handleScroll.bind(this));
        window.addEventListener('scroll', this.throttledScroll);
    }
    
    handleScroll() {
        const scrollTop = window.pageYOffset;
        const scrollPercent = scrollTop / (document.body.scrollHeight - window.innerHeight);
        
        // 更新滚动指示器
        this.updateScrollIndicator(scrollPercent);
        
        // 处理懒加载
        this.checkLazyLoad();
    }
    
    updateScrollIndicator(percent) {
        const indicator = document.querySelector('.scroll-indicator');
        if (indicator) {
            indicator.style.width = `${percent * 100}%`;
        }
    }
}

// 2. 鼠标移动跟踪
class MouseTracker {
    constructor() {
        this.throttledMouseMove = throttleForAnimation(this.handleMouseMove.bind(this));
        document.addEventListener('mousemove', this.throttledMouseMove);
    }
    
    handleMouseMove(event) {
        const { clientX, clientY } = event;
        
        // 更新自定义光标位置
        this.updateCursor(clientX, clientY);
        
        // 更新视差效果
        this.updateParallax(clientX, clientY);
    }
    
    updateCursor(x, y) {
        const cursor = document.querySelector('.custom-cursor');
        if (cursor) {
            cursor.style.transform = `translate(${x}px, ${y}px)`;
        }
    }
}

// 3. 实时数据可视化
class DataVisualization {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.data = [];
        
        this.throttledRender = throttleForAnimation(this.render.bind(this));
    }
    
    addDataPoint(point) {
        this.data.push(point);
        
        // 限制数据点数量
        if (this.data.length > 1000) {
            this.data.shift();
        }
        
        // 触发重绘（节流）
        this.throttledRender();
    }
    
    render() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 绘制数据点
        this.ctx.beginPath();
        this.data.forEach((point, index) => {
            const x = (index / this.data.length) * this.canvas.width;
            const y = this.canvas.height - (point.value / 100) * this.canvas.height;
            
            if (index === 0) {
                this.ctx.moveTo(x, y);
            } else {
                this.ctx.lineTo(x, y);
            }
        });
        this.ctx.stroke();
    }
}

// 4. 拖拽操作优化
class DragHandler {
    constructor(element) {
        this.element = element;
        this.isDragging = false;
        
        this.throttledDrag = throttleForAnimation(this.handleDrag.bind(this));
        
        element.addEventListener('mousedown', this.startDrag.bind(this));
        document.addEventListener('mousemove', this.throttledDrag);
        document.addEventListener('mouseup', this.endDrag.bind(this));
    }
    
    startDrag(event) {
        this.isDragging = true;
        this.startX = event.clientX;
        this.startY = event.clientY;
    }
    
    handleDrag(event) {
        if (!this.isDragging) return;
        
        const deltaX = event.clientX - this.startX;
        const deltaY = event.clientY - this.startY;
        
        // 更新元素位置
        this.element.style.transform = `translate(${deltaX}px, ${deltaY}px)`;
    }
    
    endDrag() {
        this.isDragging = false;
    }
}
```

### 3. batched() - 批处理函数
```javascript
function batched(callback, synchronize = () => Promise.resolve()) {
    let scheduled = false;
    return async (...args) => {
        if (!scheduled) {
            scheduled = true;
            await synchronize();
            scheduled = false;
            callback(...args);
        }
    };
}
```

**功能特性**:
- **批处理**: 在同一时间帧内的多次调用只执行一次
- **自定义同步**: 可自定义同步函数决定批处理粒度
- **异步支持**: 支持异步同步函数
- **参数保留**: 保留最后一次调用的参数

**使用示例**:
```javascript
// 基本批处理（微任务级别）
const batchedUpdate = batched((data) => {
    console.log("批处理更新:", data);
    updateUI(data);
});

// 同一微任务中的多次调用
batchedUpdate("data1");
batchedUpdate("data2");
batchedUpdate("data3");
// 只执行一次，参数为 "data3"

// 自定义同步函数（动画帧级别）
const animationBatched = batched(
    updateDOM,
    () => new Promise(resolve => requestAnimationFrame(resolve))
);
```

**实际应用场景**:
```javascript
// 1. DOM 更新批处理
class DOMBatcher {
    constructor() {
        this.batchedUpdate = batched(this.flushUpdates.bind(this));
        this.pendingUpdates = [];
    }
    
    scheduleUpdate(element, property, value) {
        this.pendingUpdates.push({ element, property, value });
        this.batchedUpdate();
    }
    
    flushUpdates() {
        // 批量应用所有 DOM 更新
        this.pendingUpdates.forEach(({ element, property, value }) => {
            element.style[property] = value;
        });
        this.pendingUpdates = [];
    }
}

// 使用示例
const batcher = new DOMBatcher();
batcher.scheduleUpdate(element1, 'left', '100px');
batcher.scheduleUpdate(element2, 'top', '200px');
batcher.scheduleUpdate(element3, 'opacity', '0.5');
// 所有更新在同一批次中应用

// 2. 状态更新批处理
class StateBatcher {
    constructor() {
        this.state = {};
        this.listeners = [];
        this.batchedNotify = batched(this.notifyListeners.bind(this));
    }
    
    setState(updates) {
        Object.assign(this.state, updates);
        this.batchedNotify();
    }
    
    notifyListeners() {
        this.listeners.forEach(listener => listener(this.state));
    }
    
    subscribe(listener) {
        this.listeners.push(listener);
    }
}
```

### 4. setRecurringAnimationFrame() - 递归动画帧
```javascript
function setRecurringAnimationFrame(callback) {
    const handler = (timestamp) => {
        callback(timestamp - lastTimestamp);
        lastTimestamp = timestamp;
        handle = browser.requestAnimationFrame(handler);
    };

    let lastTimestamp = browser.performance.now();
    let handle = browser.requestAnimationFrame(handler);

    return stop; // 返回停止函数
}
```

**功能特性**:
- **连续执行**: 持续在每个动画帧执行回调
- **时间差计算**: 提供上一帧的时间差（deltaTime）
- **高精度**: 使用 performance.now() 获取高精度时间戳
- **可控停止**: 返回停止函数以终止递归

**使用示例**:
```javascript
// 基本递归动画帧
const stopAnimation = setRecurringAnimationFrame((deltaTime) => {
    console.log(`帧时间差: ${deltaTime}ms`);
    // 执行动画逻辑
    updateAnimation(deltaTime);
});

// 停止动画
setTimeout(() => {
    stopAnimation();
}, 5000); // 5秒后停止
```

**实际应用场景**:
```javascript
// 1. 游戏循环
class GameLoop {
    constructor() {
        this.isRunning = false;
        this.entities = [];
        this.stopLoop = null;
    }

    start() {
        if (this.isRunning) return;

        this.isRunning = true;
        this.stopLoop = setRecurringAnimationFrame((deltaTime) => {
            this.update(deltaTime);
            this.render();
        });
    }

    stop() {
        if (this.stopLoop) {
            this.stopLoop();
            this.stopLoop = null;
            this.isRunning = false;
        }
    }

    update(deltaTime) {
        // 更新游戏实体
        this.entities.forEach(entity => {
            entity.update(deltaTime);
        });
    }

    render() {
        // 渲染游戏画面
        this.clearCanvas();
        this.entities.forEach(entity => {
            entity.render();
        });
    }
}

// 2. 粒子系统
class ParticleSystem {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.particles = [];
        this.isActive = false;
    }

    start() {
        this.isActive = true;
        this.stopAnimation = setRecurringAnimationFrame((deltaTime) => {
            this.updateParticles(deltaTime);
            this.renderParticles();
        });
    }

    stop() {
        this.isActive = false;
        if (this.stopAnimation) {
            this.stopAnimation();
        }
    }

    updateParticles(deltaTime) {
        // 更新粒子位置和生命周期
        this.particles = this.particles.filter(particle => {
            particle.update(deltaTime);
            return particle.isAlive();
        });

        // 生成新粒子
        if (this.particles.length < 100) {
            this.particles.push(new Particle());
        }
    }

    renderParticles() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        this.particles.forEach(particle => {
            particle.render(this.ctx);
        });
    }
}
```

## Hook 集成

### 1. useDebounced() - 防抖 Hook
```javascript
function useDebounced(callback, delay, { execBeforeUnmount = false, immediate = false } = {}) {
    const component = useComponent();
    const debounced = debounce(callback.bind(component), delay, immediate);
    onWillUnmount(() => debounced.cancel(execBeforeUnmount));
    return debounced;
}
```

**功能特性**:
- **组件绑定**: 自动绑定组件上下文
- **生命周期管理**: 组件卸载时自动清理
- **卸载前执行**: 可选择在卸载前执行待执行的函数
- **即时执行**: 支持立即执行模式

**使用示例**:
```javascript
class SearchComponent extends Component {
    setup() {
        this.state = useState({ query: "", results: [] });

        // 防抖搜索
        this.debouncedSearch = useDebounced(this.performSearch, 300);

        // 防抖保存（卸载前执行）
        this.debouncedSave = useDebounced(
            this.saveData,
            2000,
            { execBeforeUnmount: true }
        );
    }

    onInputChange(event) {
        this.state.query = event.target.value;
        this.debouncedSearch(this.state.query);
    }

    async performSearch(query) {
        if (query.length < 2) {
            this.state.results = [];
            return;
        }

        try {
            const response = await fetch(`/api/search?q=${query}`);
            this.state.results = await response.json();
        } catch (error) {
            console.error("搜索失败:", error);
        }
    }

    saveData() {
        // 保存当前状态
        localStorage.setItem('searchQuery', this.state.query);
    }
}
```

### 2. useThrottleForAnimation() - 动画帧节流 Hook
```javascript
function useThrottleForAnimation(func) {
    const component = useComponent();
    const throttledForAnimation = throttleForAnimation(func.bind(component));
    onWillUnmount(() => throttledForAnimation.cancel());
    return throttledForAnimation;
}
```

**功能特性**:
- **组件绑定**: 自动绑定组件上下文
- **生命周期管理**: 组件卸载时自动清理
- **动画同步**: 与浏览器重绘周期同步

**使用示例**:
```javascript
class AnimatedComponent extends Component {
    setup() {
        this.state = useState({ x: 0, y: 0 });

        // 节流的鼠标移动处理
        this.throttledMouseMove = useThrottleForAnimation(this.handleMouseMove);
    }

    handleMouseMove(event) {
        this.state.x = event.clientX;
        this.state.y = event.clientY;
    }

    render() {
        return xml`
            <div class="animated-container"
                 t-on-mousemove="throttledMouseMove">
                <div class="cursor-follower"
                     t-att-style="'transform: translate(' + state.x + 'px, ' + state.y + 'px)'">
                </div>
            </div>
        `;
    }
}
```

## 最佳实践

### 1. 选择合适的时间控制策略
```javascript
// ✅ 推荐：根据场景选择合适的策略

// 用户输入 - 使用防抖
const debouncedSearch = debounce(search, 300);

// 滚动/鼠标移动 - 使用动画帧节流
const throttledScroll = throttleForAnimation(handleScroll);

// 状态更新 - 使用批处理
const batchedUpdate = batched(updateState);

// 连续动画 - 使用递归动画帧
const stopAnimation = setRecurringAnimationFrame(animate);
```

### 2. 内存管理
```javascript
// ✅ 推荐：及时清理定时器
class ComponentWithTiming {
    constructor() {
        this.debouncedFn = debounce(this.handler, 300);
        this.stopAnimation = setRecurringAnimationFrame(this.animate);
    }

    destroy() {
        // 清理防抖函数
        this.debouncedFn.cancel();

        // 停止动画循环
        this.stopAnimation();
    }
}
```

## 总结

Odoo 时间工具模块提供了完整的时间控制和性能优化功能：

**核心优势**:
- **性能优化**: 通过防抖、节流、批处理减少不必要的函数调用
- **动画支持**: 与浏览器重绘周期同步的动画帧控制
- **Hook 集成**: 与 OWL 组件生命周期完美集成
- **灵活配置**: 支持多种配置选项和自定义同步函数

**适用场景**:
- 用户输入处理（搜索、表单验证）
- 滚动和鼠标事件优化
- 动画和游戏循环
- 状态更新批处理
- 性能监控和优化

这个工具模块为 Odoo Web 客户端提供了强大的时间控制能力，是构建高性能、响应式用户界面的重要基础。

# Odoo 数字工具 (Numbers Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/numbers.js`  
**原始路径**: `/web/static/src/core/utils/numbers.js`  
**模块类型**: 核心工具模块 - 数字处理工具  
**代码行数**: 262 行  
**依赖关系**: 
- `@web/core/l10n/localization` - 本地化配置
- `@web/core/l10n/translation` - 翻译服务
- `@web/core/utils/strings` - 字符串工具 (intersperse)

## 模块功能

数字工具模块是 Odoo Web 客户端的核心工具库，提供了丰富的数字处理和格式化功能，包括：
- 数字范围和约束（clamp, range）
- 高精度舍入（roundPrecision, roundDecimals）
- 浮点数比较（floatIsZero）
- 数字格式化（formatFloat, humanNumber）
- 千分位分隔符处理（insertThousandsSep）
- 浮点数反转（invertFloat）

## 核心函数详解

### 1. clamp() - 数值约束
```javascript
function clamp(num, min, max) {
    return Math.max(Math.min(num, max), min);
}
```

**功能特性**:
- **范围限制**: 将数值限制在指定的最小值和最大值之间
- **边界处理**: 自动处理超出范围的值
- **简单高效**: 基于 Math.max 和 Math.min 的高效实现
- **类型安全**: 适用于所有数字类型

**使用示例**:
```javascript
// 基本约束
clamp(5, 0, 10);    // 5 (在范围内)
clamp(-5, 0, 10);   // 0 (小于最小值)
clamp(15, 0, 10);   // 10 (大于最大值)

// 百分比约束
clamp(150, 0, 100); // 100 (限制在0-100%)
clamp(-20, 0, 100); // 0

// 透明度约束
clamp(1.5, 0, 1);   // 1.0
clamp(-0.2, 0, 1);  // 0.0
```

**实际应用场景**:
```javascript
// 1. 滑块组件
class SliderComponent {
    constructor(min, max, value) {
        this.min = min;
        this.max = max;
        this.value = clamp(value, min, max);
    }
    
    setValue(newValue) {
        this.value = clamp(newValue, this.min, this.max);
        this.updateDisplay();
    }
    
    updateDisplay() {
        const percentage = ((this.value - this.min) / (this.max - this.min)) * 100;
        this.slider.style.left = clamp(percentage, 0, 100) + '%';
    }
}

// 2. 音量控制
class VolumeController {
    setVolume(volume) {
        // 确保音量在0-100范围内
        this.volume = clamp(volume, 0, 100);
        this.audioElement.volume = this.volume / 100;
    }
    
    adjustVolume(delta) {
        this.setVolume(this.volume + delta);
    }
}

// 3. 颜色值处理
class ColorUtils {
    static normalizeRGB(r, g, b) {
        return {
            r: clamp(Math.round(r), 0, 255),
            g: clamp(Math.round(g), 0, 255),
            b: clamp(Math.round(b), 0, 255)
        };
    }
    
    static adjustBrightness(color, factor) {
        return {
            r: clamp(color.r * factor, 0, 255),
            g: clamp(color.g * factor, 0, 255),
            b: clamp(color.b * factor, 0, 255)
        };
    }
}

// 4. 分页控制
class PaginationController {
    constructor(totalPages) {
        this.totalPages = totalPages;
        this.currentPage = 1;
    }
    
    goToPage(page) {
        this.currentPage = clamp(page, 1, this.totalPages);
        this.updatePagination();
    }
    
    nextPage() {
        this.goToPage(this.currentPage + 1);
    }
    
    previousPage() {
        this.goToPage(this.currentPage - 1);
    }
}
```

### 2. range() - 数字范围生成
```javascript
function range(start, stop, step = 1) {
    const array = [];
    const nsteps = Math.floor((stop - start) / step);
    for (let i = 0; i < nsteps; i++) {
        array.push(start + step * i);
    }
    return array;
}
```

**功能特性**:
- **灵活范围**: 支持自定义起始值、结束值和步长
- **Python风格**: 类似Python的range函数
- **高效生成**: 一次性生成整个数组
- **步长控制**: 支持正负步长

**使用示例**:
```javascript
// 基本范围
range(0, 5);        // [0, 1, 2, 3, 4]
range(1, 6);        // [1, 2, 3, 4, 5]
range(0, 10, 2);    // [0, 2, 4, 6, 8]

// 倒序范围
range(10, 0, -1);   // [10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
range(5, 0, -2);    // [5, 3, 1]

// 小数步长
range(0, 1, 0.1);   // [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
```

**实际应用场景**:
```javascript
// 1. 表格行生成
class TableGenerator {
    generateRows(count) {
        return range(0, count).map(index => ({
            id: index,
            name: `Row ${index + 1}`,
            data: this.generateRowData(index)
        }));
    }
    
    generatePageNumbers(totalPages, currentPage) {
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 3);
        return range(startPage, endPage + 1);
    }
}

// 2. 图表数据生成
class ChartDataGenerator {
    generateTimeSeriesData(days) {
        const today = new Date();
        return range(0, days).map(i => {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            return {
                date: date.toISOString().split('T')[0],
                value: Math.random() * 100
            };
        });
    }
    
    generateXAxisLabels(min, max, step) {
        return range(min, max + 1, step).map(value => ({
            value,
            label: value.toString()
        }));
    }
}

// 3. 动画帧生成
class AnimationFrameGenerator {
    generateKeyframes(duration, fps = 60) {
        const totalFrames = Math.floor(duration * fps / 1000);
        return range(0, totalFrames).map(frame => ({
            time: (frame / totalFrames) * duration,
            progress: frame / totalFrames
        }));
    }
    
    generateEasingSteps(steps) {
        return range(0, steps + 1).map(i => {
            const t = i / steps;
            return this.easeInOutCubic(t);
        });
    }
    
    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
    }
}

// 4. 批处理操作
class BatchProcessor {
    async processBatches(items, batchSize) {
        const batchCount = Math.ceil(items.length / batchSize);
        const results = [];
        
        for (const batchIndex of range(0, batchCount)) {
            const start = batchIndex * batchSize;
            const end = Math.min(start + batchSize, items.length);
            const batch = items.slice(start, end);
            
            const batchResult = await this.processBatch(batch);
            results.push(...batchResult);
        }
        
        return results;
    }
}
```

### 3. roundPrecision() - 高精度舍入
```javascript
function roundPrecision(value, precision, method = "HALF-UP") {
    // 复杂的IEEE-754浮点数舍入实现
    // 支持多种舍入方法：HALF-UP, HALF-DOWN, HALF-EVEN, UP, DOWN
}
```

**功能特性**:
- **高精度**: 最小化IEEE-754浮点数表示错误
- **多种方法**: 支持5种不同的舍入方法
- **精度控制**: 可指定任意精度参数
- **数学准确**: 符合数学舍入规则

**舍入方法说明**:
- **HALF-UP**: 四舍五入，0.5向上舍入（默认）
- **HALF-DOWN**: 四舍五入，0.5向下舍入
- **HALF-EVEN**: 银行家舍入，0.5向最近的偶数舍入
- **UP**: 总是向上舍入（远离零）
- **DOWN**: 总是向下舍入（趋向零）

**使用示例**:
```javascript
// 基本舍入
roundPrecision(1.235, 0.01);           // 1.24 (HALF-UP)
roundPrecision(1.235, 0.01, "HALF-DOWN"); // 1.23
roundPrecision(1.235, 0.01, "HALF-EVEN"); // 1.24

// 不同精度
roundPrecision(1234.5678, 1);          // 1235 (整数)
roundPrecision(1234.5678, 0.1);        // 1234.6 (一位小数)
roundPrecision(1234.5678, 0.01);       // 1234.57 (两位小数)

// 特殊舍入方法
roundPrecision(1.5, 1, "HALF-EVEN");   // 2 (向偶数)
roundPrecision(2.5, 1, "HALF-EVEN");   // 2 (向偶数)
roundPrecision(1.1, 1, "UP");          // 2 (向上)
roundPrecision(1.9, 1, "DOWN");        // 1 (向下)
```

**实际应用场景**:
```javascript
// 1. 财务计算
class FinancialCalculator {
    calculateTax(amount, rate) {
        const tax = amount * rate;
        // 税金计算使用银行家舍入
        return roundPrecision(tax, 0.01, "HALF-EVEN");
    }
    
    calculateDiscount(price, discountPercent) {
        const discount = price * (discountPercent / 100);
        // 折扣向下舍入，对客户有利
        return roundPrecision(discount, 0.01, "DOWN");
    }
    
    calculateTotal(items) {
        const subtotal = items.reduce((sum, item) => {
            return sum + roundPrecision(item.price * item.quantity, 0.01);
        }, 0);
        
        return roundPrecision(subtotal, 0.01, "HALF-UP");
    }
}

// 2. 测量和工程计算
class MeasurementUtils {
    roundToNearestUnit(value, unit) {
        // 根据单位精度舍入
        const precisionMap = {
            'mm': 0.1,
            'cm': 0.01,
            'm': 0.001,
            'inch': 0.01,
            'ft': 0.001
        };
        
        const precision = precisionMap[unit] || 0.01;
        return roundPrecision(value, precision, "HALF-EVEN");
    }
    
    calculateArea(length, width, unit = 'm') {
        const area = length * width;
        const precision = unit === 'mm' ? 0.01 : 0.001;
        return roundPrecision(area, precision, "HALF-UP");
    }
}

// 3. 统计计算
class StatisticsCalculator {
    calculateMean(values) {
        const sum = values.reduce((a, b) => a + b, 0);
        const mean = sum / values.length;
        return roundPrecision(mean, 0.001, "HALF-EVEN");
    }
    
    calculateStandardDeviation(values) {
        const mean = this.calculateMean(values);
        const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
        const variance = squaredDiffs.reduce((a, b) => a + b, 0) / values.length;
        const stdDev = Math.sqrt(variance);
        return roundPrecision(stdDev, 0.001, "HALF-UP");
    }
}
```

### 4. roundDecimals() - 小数位舍入
```javascript
function roundDecimals(value, decimals) {
    return roundPrecision(value, parseFloat("1e" + -decimals));
}
```

**功能特性**:
- **小数位控制**: 直接指定保留的小数位数
- **科学计数法**: 使用科学计数法避免浮点精度问题
- **高精度基础**: 基于 roundPrecision 实现
- **Python兼容**: 与Python的舍入行为保持一致

**使用示例**:
```javascript
// 基本小数位舍入
roundDecimals(3.14159, 2);    // 3.14
roundDecimals(3.14159, 4);    // 3.1416
roundDecimals(123.456, 1);    // 123.5
roundDecimals(123.456, 0);    // 123

// 处理浮点精度问题
roundDecimals(0.1 + 0.2, 2);  // 0.30 (而不是 0.30000000000000004)
roundDecimals(1.005, 2);      // 1.01 (正确处理边界情况)
```

### 5. floatIsZero() - 浮点数零值检测
```javascript
function floatIsZero(value, decimals) {
    return value === 0 || roundDecimals(value, decimals) === 0;
}
```

**功能特性**:
- **精度容忍**: 在指定精度下判断是否为零
- **浮点安全**: 处理浮点数精度问题
- **业务逻辑**: 适用于业务逻辑中的零值判断
- **简单高效**: 基于舍入的简单实现

**使用示例**:
```javascript
// 精确零值
floatIsZero(0, 2);           // true
floatIsZero(0.0, 2);         // true

// 精度范围内的零值
floatIsZero(0.001, 2);       // true (在2位小数精度下为零)
floatIsZero(0.009, 2);       // true
floatIsZero(0.01, 2);        // false

// 浮点精度问题
floatIsZero(0.1 + 0.2 - 0.3, 10); // true (处理浮点精度误差)
```

### 6. formatFloat() - 浮点数格式化
```javascript
function formatFloat(value, options = {}) {
    // 支持人类可读格式、千分位分隔符、小数点等
    // 完整的本地化支持
}
```

**功能特性**:
- **本地化支持**: 自动使用用户的本地化设置
- **灵活配置**: 支持多种格式化选项
- **人类可读**: 支持 K、M、G 等单位缩写
- **千分位分隔**: 自动添加千分位分隔符

**配置选项**:
- **digits**: 数字精度 [总位数, 小数位数]
- **humanReadable**: 是否使用人类可读格式
- **decimalPoint**: 自定义小数点
- **thousandsSep**: 自定义千分位分隔符
- **grouping**: 分组规则
- **trailingZeros**: 是否保留尾随零

**使用示例**:
```javascript
// 基本格式化
formatFloat(1234.567);                    // "1,234.57"
formatFloat(1234.567, { digits: [0, 3] }); // "1,234.567"

// 人类可读格式
formatFloat(1234567, { humanReadable: true }); // "1.23M"
formatFloat(1500, { humanReadable: true });    // "1.5k"

// 自定义分隔符
formatFloat(1234.567, {
    decimalPoint: ",",
    thousandsSep: "."
}); // "1.234,57"

// 移除尾随零
formatFloat(123.400, {
    digits: [0, 3],
    trailingZeros: false
}); // "123.4"
```

### 7. humanNumber() - 人类可读数字
```javascript
function humanNumber(number, options = { decimals: 0, minDigits: 1 }) {
    // 将大数字转换为 K、M、G、T、P、E 格式
    // 支持科学计数法表示超大数字
}
```

**功能特性**:
- **单位缩写**: 自动使用 K、M、G、T、P、E 单位
- **科学计数法**: 超大数字使用科学计数法
- **最小位数**: 控制切换到下一级单位的最小位数
- **国际化**: 单位符号支持国际化

**使用示例**:
```javascript
// 基本人类可读格式
humanNumber(1000);        // "1k"
humanNumber(1500);        // "2k"
humanNumber(1000000);     // "1M"
humanNumber(1234567890);  // "1G"

// 保留小数
humanNumber(1234, { decimals: 1 });     // "1.2k"
humanNumber(1234567, { decimals: 2 });  // "1.23M"

// 最小位数控制
humanNumber(1234, { minDigits: 2 });    // "1234" (不转换)
humanNumber(12345, { minDigits: 2 });   // "12k" (转换)

// 超大数字
humanNumber(1e25);        // "1e+25"
```

### 8. insertThousandsSep() - 千分位分隔符
```javascript
function insertThousandsSep(number, thousandsSep = ",", grouping = []) {
    const negative = number[0] === "-";
    number = negative ? number.slice(1) : number;
    return (negative ? "-" : "") + intersperse(number, grouping, thousandsSep);
}
```

**功能特性**:
- **负数处理**: 正确处理负数的符号
- **自定义分隔符**: 支持不同的千分位分隔符
- **分组规则**: 支持自定义分组规则
- **国际化**: 适应不同地区的数字格式

**使用示例**:
```javascript
// 基本千分位分隔
insertThousandsSep("1234567");           // "1,234,567"
insertThousandsSep("-1234567");          // "-1,234,567"

// 自定义分隔符
insertThousandsSep("1234567", ".");      // "1.234.567"
insertThousandsSep("1234567", " ");      // "1 234 567"

// 自定义分组 (印度数字格式)
insertThousandsSep("1234567", ",", [3, 2, 0]); // "12,34,567"
```

## 最佳实践

### 1. 选择合适的舍入方法
```javascript
// ✅ 推荐：根据业务场景选择舍入方法

// 财务计算 - 使用银行家舍入
function calculateTax(amount, rate) {
    return roundPrecision(amount * rate, 0.01, "HALF-EVEN");
}

// 用户友好的折扣 - 向下舍入
function calculateDiscount(price, percent) {
    return roundPrecision(price * percent / 100, 0.01, "DOWN");
}

// 一般计算 - 使用标准四舍五入
function calculateAverage(values) {
    const sum = values.reduce((a, b) => a + b, 0);
    return roundPrecision(sum / values.length, 0.01, "HALF-UP");
}
```

### 2. 浮点数比较
```javascript
// ✅ 推荐：使用 floatIsZero 进行零值比较
function isEffectivelyZero(value, precision = 2) {
    return floatIsZero(value, precision);
}

// ✅ 推荐：浮点数相等比较
function floatEquals(a, b, precision = 6) {
    return floatIsZero(a - b, precision);
}

// ❌ 避免：直接比较浮点数
function badFloatComparison(a, b) {
    return a === b; // 可能因精度问题失败
}
```

### 3. 数字格式化
```javascript
// ✅ 推荐：根据上下文选择格式化方式
class NumberDisplayManager {
    formatForDisplay(value, context) {
        switch (context) {
            case 'table':
                return formatFloat(value, { digits: [0, 2] });
            case 'chart':
                return humanNumber(value, { decimals: 1 });
            case 'currency':
                return '$' + formatFloat(value, { digits: [0, 2] });
            case 'percentage':
                return formatFloat(value * 100, { digits: [0, 1] }) + '%';
            default:
                return formatFloat(value);
        }
    }
}
```

### 4. 性能优化
```javascript
// ✅ 推荐：缓存格式化结果
class OptimizedNumberFormatter {
    constructor() {
        this.formatCache = new Map();
    }

    formatWithCache(value, options) {
        const key = `${value}_${JSON.stringify(options)}`;

        if (this.formatCache.has(key)) {
            return this.formatCache.get(key);
        }

        const result = formatFloat(value, options);
        this.formatCache.set(key, result);
        return result;
    }
}

// ✅ 推荐：批量处理
function formatNumberArray(numbers, options) {
    return numbers.map(num => formatFloat(num, options));
}
```

### 5. 错误处理
```javascript
// ✅ 推荐：安全的数字处理
function safeFormatFloat(value, options = {}) {
    if (typeof value !== 'number' || !isFinite(value)) {
        return 'N/A';
    }

    try {
        return formatFloat(value, options);
    } catch (error) {
        console.warn('数字格式化失败:', error);
        return value.toString();
    }
}

// ✅ 推荐：输入验证
function validateAndClamp(value, min, max) {
    if (typeof value !== 'number' || isNaN(value)) {
        throw new Error('Invalid number input');
    }

    return clamp(value, min, max);
}
```

## 常见陷阱和注意事项

### 1. 浮点精度问题
```javascript
// ❌ 错误：直接比较浮点数
if (0.1 + 0.2 === 0.3) {
    // 永远不会执行
}

// ✅ 正确：使用精度比较
if (floatIsZero(0.1 + 0.2 - 0.3, 10)) {
    // 正确执行
}
```

### 2. 舍入方法选择
```javascript
// ❌ 错误：财务计算使用不当舍入
function badTaxCalculation(amount, rate) {
    return Math.round(amount * rate * 100) / 100; // 可能不公平
}

// ✅ 正确：使用银行家舍入
function goodTaxCalculation(amount, rate) {
    return roundPrecision(amount * rate, 0.01, "HALF-EVEN");
}
```

### 3. 格式化性能
```javascript
// ❌ 错误：在循环中重复格式化
function badTableRender(data) {
    return data.map(row => ({
        ...row,
        formattedPrice: formatFloat(row.price, { digits: [0, 2] })
    }));
}

// ✅ 正确：预先准备格式化选项
function goodTableRender(data) {
    const priceOptions = { digits: [0, 2] };
    return data.map(row => ({
        ...row,
        formattedPrice: formatFloat(row.price, priceOptions)
    }));
}
```

## 总结

Odoo 数字工具模块提供了完整的数字处理和格式化功能：

**核心优势**:
- **高精度计算**: 解决JavaScript浮点数精度问题
- **多种舍入方法**: 支持不同业务场景的舍入需求
- **国际化支持**: 完整的本地化数字格式支持
- **人类可读**: 大数字的友好显示格式
- **灵活配置**: 丰富的格式化选项

**适用场景**:
- 财务和会计计算
- 数据可视化和图表
- 用户界面数字显示
- 统计和分析报告
- 国际化应用开发

这个工具模块为 Odoo Web 客户端提供了强大的数字处理能力，是构建精确、用户友好的数字应用的重要基础。

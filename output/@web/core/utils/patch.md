# Odoo 补丁工具 (Patch Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/patch.js`  
**原始路径**: `/web/static/src/core/utils/patch.js`  
**模块类型**: 核心工具模块 - 对象补丁工具  
**代码行数**: 144 行  
**依赖关系**: 无外部依赖

## 模块功能

补丁工具模块是 Odoo Web 客户端的核心工具库，提供了强大的运行时对象修改功能。该模块实现了一个高级的补丁系统，支持：
- 动态对象属性修改
- 类原型链补丁
- 多层补丁叠加
- 补丁撤销和恢复
- getter/setter 属性处理
- 原型链继承支持

这个补丁系统是 Odoo 模块化架构的重要基础，允许模块间的灵活扩展和定制。

## 核心数据结构

### PatchDescription - 补丁描述对象
```typescript
interface PatchDescription {
    originalProperties: Map<string, PropertyDescriptor>;
    skeleton: object;
    extensions: Set<object>;
}
```

**字段说明**:
- **originalProperties**: 存储原始属性描述符，用于撤销补丁
- **skeleton**: 骨架对象，维护补丁的原型链结构
- **extensions**: 已应用的扩展对象集合

### patchDescriptions - 补丁存储
```javascript
const patchDescriptions = new WeakMap();
```

**设计特点**:
- **WeakMap**: 确保对象被垃圾回收时，补丁描述也会自动清理
- **内存安全**: 避免内存泄漏
- **私有存储**: 为每个被补丁的对象存储独立的补丁信息

## 核心函数详解

### 1. patch() - 主补丁函数
```javascript
function patch(objToPatch, extension) {
    // 复杂的补丁应用逻辑
    // 返回撤销函数
    return () => {
        // 撤销补丁的逻辑
    };
}
```

**功能特性**:
- **动态修改**: 运行时修改对象属性和方法
- **原型支持**: 正确处理类原型的补丁
- **属性描述符**: 完整支持 getter/setter 和属性特性
- **撤销机制**: 返回撤销函数，可以完全恢复原始状态
- **多层叠加**: 支持多个补丁的叠加应用

**使用示例**:
```javascript
// 基本对象补丁
const myObject = {
    name: 'original',
    getValue() {
        return this.name;
    }
};

const unpatch = patch(myObject, {
    name: 'patched',
    getValue() {
        return `Modified: ${this.name}`;
    },
    newMethod() {
        return 'This is a new method';
    }
});

console.log(myObject.getValue()); // "Modified: patched"
console.log(myObject.newMethod()); // "This is a new method"

// 撤销补丁
unpatch();
console.log(myObject.getValue()); // "original"
console.log(myObject.newMethod); // undefined
```

**实际应用场景**:
```javascript
// 1. 类方法扩展
class UserService {
    getUser(id) {
        return fetch(`/api/users/${id}`).then(r => r.json());
    }
    
    createUser(userData) {
        return fetch('/api/users', {
            method: 'POST',
            body: JSON.stringify(userData)
        }).then(r => r.json());
    }
}

// 添加缓存功能
const userCache = new Map();

const unpatchUserService = patch(UserService.prototype, {
    getUser(id) {
        // 检查缓存
        if (userCache.has(id)) {
            return Promise.resolve(userCache.get(id));
        }
        
        // 调用原始方法
        return this._super(id).then(user => {
            userCache.set(id, user);
            return user;
        });
    },
    
    createUser(userData) {
        return this._super(userData).then(user => {
            // 创建后添加到缓存
            userCache.set(user.id, user);
            return user;
        });
    }
});

// 2. 组件功能增强
class BaseComponent {
    constructor(props) {
        this.props = props;
        this.state = {};
    }
    
    render() {
        return '<div>Base Component</div>';
    }
    
    setState(newState) {
        Object.assign(this.state, newState);
        this.forceUpdate();
    }
}

// 添加生命周期钩子
const unpatchComponent = patch(BaseComponent.prototype, {
    constructor(props) {
        this._super(props);
        this.lifecycle = {
            mounted: false,
            updated: 0
        };
        
        if (this.componentDidMount) {
            setTimeout(() => {
                this.componentDidMount();
                this.lifecycle.mounted = true;
            }, 0);
        }
    },
    
    setState(newState) {
        const oldState = { ...this.state };
        this._super(newState);
        
        if (this.componentDidUpdate) {
            this.componentDidUpdate(this.props, oldState);
            this.lifecycle.updated++;
        }
    },
    
    destroy() {
        if (this.componentWillUnmount) {
            this.componentWillUnmount();
        }
        this.lifecycle.mounted = false;
    }
});

// 3. API 客户端增强
class ApiClient {
    constructor(baseURL) {
        this.baseURL = baseURL;
    }
    
    request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        return fetch(url, options);
    }
}

// 添加认证和重试功能
const unpatchApiClient = patch(ApiClient.prototype, {
    constructor(baseURL, authToken) {
        this._super(baseURL);
        this.authToken = authToken;
        this.retryCount = 3;
    },
    
    async request(endpoint, options = {}) {
        // 添加认证头
        const authOptions = {
            ...options,
            headers: {
                ...options.headers,
                'Authorization': `Bearer ${this.authToken}`
            }
        };
        
        // 重试逻辑
        for (let attempt = 1; attempt <= this.retryCount; attempt++) {
            try {
                const response = await this._super(endpoint, authOptions);
                
                if (response.ok) {
                    return response;
                }
                
                if (response.status === 401) {
                    await this.refreshToken();
                    continue;
                }
                
                if (attempt === this.retryCount) {
                    throw new Error(`Request failed after ${this.retryCount} attempts`);
                }
                
            } catch (error) {
                if (attempt === this.retryCount) {
                    throw error;
                }
                
                // 指数退避
                await new Promise(resolve => 
                    setTimeout(resolve, Math.pow(2, attempt) * 1000)
                );
            }
        }
    },
    
    async refreshToken() {
        // 刷新令牌逻辑
        const response = await fetch('/api/auth/refresh', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.authToken}`
            }
        });
        
        if (response.ok) {
            const { token } = await response.json();
            this.authToken = token;
        }
    }
});

// 4. 数据模型扩展
class BaseModel {
    constructor(data = {}) {
        Object.assign(this, data);
    }
    
    save() {
        return fetch(`/api/${this.constructor.name.toLowerCase()}s/${this.id}`, {
            method: 'PUT',
            body: JSON.stringify(this)
        });
    }
    
    delete() {
        return fetch(`/api/${this.constructor.name.toLowerCase()}s/${this.id}`, {
            method: 'DELETE'
        });
    }
}

// 添加验证和事件功能
const unpatchModel = patch(BaseModel.prototype, {
    constructor(data = {}) {
        this._super(data);
        this.errors = {};
        this.listeners = {};
    },
    
    validate() {
        this.errors = {};
        
        // 运行验证规则
        if (this.validationRules) {
            for (const [field, rules] of Object.entries(this.validationRules)) {
                for (const rule of rules) {
                    if (!rule.validator(this[field])) {
                        this.errors[field] = this.errors[field] || [];
                        this.errors[field].push(rule.message);
                    }
                }
            }
        }
        
        return Object.keys(this.errors).length === 0;
    },
    
    async save() {
        if (!this.validate()) {
            throw new Error('Validation failed');
        }
        
        this.emit('beforeSave');
        
        try {
            const response = await this._super();
            this.emit('afterSave');
            return response;
        } catch (error) {
            this.emit('saveError', error);
            throw error;
        }
    },
    
    on(event, callback) {
        this.listeners[event] = this.listeners[event] || [];
        this.listeners[event].push(callback);
    },
    
    emit(event, ...args) {
        if (this.listeners[event]) {
            this.listeners[event].forEach(callback => callback(...args));
        }
    }
});
```

### 2. getPatchDescription() - 获取补丁描述
```javascript
function getPatchDescription(objToPatch) {
    if (!patchDescriptions.has(objToPatch)) {
        patchDescriptions.set(objToPatch, {
            originalProperties: new Map(),
            skeleton: Object.create(Object.getPrototypeOf(objToPatch)),
            extensions: new Set(),
        });
    }
    return patchDescriptions.get(objToPatch);
}
```

**功能特性**:
- **懒初始化**: 只有在需要时才创建补丁描述
- **原型继承**: 正确设置骨架对象的原型链
- **状态管理**: 维护补丁的完整状态信息
- **内存优化**: 使用 WeakMap 确保内存安全

### 3. isClassPrototype() - 类原型检测
```javascript
function isClassPrototype(objToPatch) {
    return (
        Object.hasOwn(objToPatch, "constructor") && 
        objToPatch.constructor?.prototype === objToPatch
    );
}
```

**功能特性**:
- **精确检测**: 准确识别类的原型对象
- **属性处理**: 为类原型设置正确的属性特性
- **兼容性**: 处理不同类型对象的差异
- **边界处理**: 正确处理各种边界情况

### 4. findAncestorPropertyDescriptor() - 原型链属性查找
```javascript
function findAncestorPropertyDescriptor(objToPatch, key) {
    let descriptor = null;
    let prototype = objToPatch;
    do {
        descriptor = Object.getOwnPropertyDescriptor(prototype, key);
        prototype = Object.getPrototypeOf(prototype);
    } while (!descriptor && prototype);
    return descriptor;
}
```

**功能特性**:
- **原型链遍历**: 沿着原型链查找属性描述符
- **完整描述符**: 返回完整的属性描述符信息
- **getter/setter**: 正确处理访问器属性
- **继承支持**: 支持复杂的继承结构

## 高级应用模式

### 1. 模块化补丁系统
```javascript
class PatchManager {
    constructor() {
        this.patches = new Map();
        this.dependencies = new Map();
    }

    registerPatch(name, targetObject, extension, dependencies = []) {
        this.patches.set(name, {
            target: targetObject,
            extension,
            dependencies,
            applied: false,
            unpatch: null
        });

        this.dependencies.set(name, dependencies);
    }

    applyPatch(name) {
        const patchInfo = this.patches.get(name);
        if (!patchInfo || patchInfo.applied) {
            return;
        }

        // 先应用依赖的补丁
        for (const dep of patchInfo.dependencies) {
            this.applyPatch(dep);
        }

        // 应用当前补丁
        patchInfo.unpatch = patch(patchInfo.target, patchInfo.extension);
        patchInfo.applied = true;

        console.log(`补丁 ${name} 已应用`);
    }

    removePatch(name) {
        const patchInfo = this.patches.get(name);
        if (!patchInfo || !patchInfo.applied) {
            return;
        }

        // 移除依赖于此补丁的其他补丁
        for (const [patchName, deps] of this.dependencies) {
            if (deps.includes(name)) {
                this.removePatch(patchName);
            }
        }

        // 移除当前补丁
        if (patchInfo.unpatch) {
            patchInfo.unpatch();
            patchInfo.applied = false;
            patchInfo.unpatch = null;
        }

        console.log(`补丁 ${name} 已移除`);
    }

    applyAllPatches() {
        for (const name of this.patches.keys()) {
            this.applyPatch(name);
        }
    }

    removeAllPatches() {
        for (const name of this.patches.keys()) {
            this.removePatch(name);
        }
    }
}

// 使用示例
const patchManager = new PatchManager();

// 注册基础补丁
patchManager.registerPatch('logging', BaseService.prototype, {
    request(url, options) {
        console.log(`请求: ${url}`);
        const result = this._super(url, options);
        console.log(`响应: ${url}`);
        return result;
    }
});

// 注册依赖于基础补丁的高级补丁
patchManager.registerPatch('caching', BaseService.prototype, {
    request(url, options) {
        const cacheKey = `${url}_${JSON.stringify(options)}`;
        if (this.cache && this.cache.has(cacheKey)) {
            return Promise.resolve(this.cache.get(cacheKey));
        }

        return this._super(url, options).then(result => {
            if (this.cache) {
                this.cache.set(cacheKey, result);
            }
            return result;
        });
    }
}, ['logging']);

// 应用所有补丁
patchManager.applyAllPatches();
```

### 2. 条件补丁系统
```javascript
class ConditionalPatchSystem {
    constructor() {
        this.conditions = new Map();
        this.patches = new Map();
    }

    registerCondition(name, condition) {
        this.conditions.set(name, condition);
    }

    registerPatch(name, target, extension, conditionName) {
        this.patches.set(name, {
            target,
            extension,
            conditionName,
            unpatch: null,
            applied: false
        });
    }

    evaluateAndApplyPatches() {
        for (const [name, patchInfo] of this.patches) {
            const condition = this.conditions.get(patchInfo.conditionName);
            const shouldApply = condition ? condition() : true;

            if (shouldApply && !patchInfo.applied) {
                patchInfo.unpatch = patch(patchInfo.target, patchInfo.extension);
                patchInfo.applied = true;
                console.log(`条件补丁 ${name} 已应用`);
            } else if (!shouldApply && patchInfo.applied) {
                if (patchInfo.unpatch) {
                    patchInfo.unpatch();
                    patchInfo.unpatch = null;
                    patchInfo.applied = false;
                    console.log(`条件补丁 ${name} 已移除`);
                }
            }
        }
    }
}

// 使用示例
const conditionalPatcher = new ConditionalPatchSystem();

// 注册条件
conditionalPatcher.registerCondition('isDevelopment', () => {
    return process.env.NODE_ENV === 'development';
});

conditionalPatcher.registerCondition('hasFeatureFlag', () => {
    return localStorage.getItem('enableAdvancedFeatures') === 'true';
});

// 注册条件补丁
conditionalPatcher.registerPatch('debugLogging', ApiClient.prototype, {
    request(url, options) {
        console.group(`API Request: ${url}`);
        console.log('Options:', options);
        console.time('Request Duration');

        return this._super(url, options).then(response => {
            console.timeEnd('Request Duration');
            console.log('Response:', response);
            console.groupEnd();
            return response;
        });
    }
}, 'isDevelopment');

conditionalPatcher.registerPatch('advancedFeatures', UserInterface.prototype, {
    render() {
        const result = this._super();

        // 添加高级功能按钮
        const advancedButton = document.createElement('button');
        advancedButton.textContent = 'Advanced Features';
        advancedButton.onclick = () => this.showAdvancedPanel();

        result.appendChild(advancedButton);
        return result;
    }
}, 'hasFeatureFlag');

// 定期评估条件
setInterval(() => {
    conditionalPatcher.evaluateAndApplyPatches();
}, 5000);
```

### 3. 版本化补丁系统
```javascript
class VersionedPatchSystem {
    constructor() {
        this.patches = new Map();
        this.currentVersion = '1.0.0';
    }

    registerPatch(name, target, extension, version, compatibility = []) {
        if (!this.patches.has(name)) {
            this.patches.set(name, new Map());
        }

        this.patches.get(name).set(version, {
            target,
            extension,
            compatibility,
            unpatch: null
        });
    }

    applyPatchForVersion(name, version = this.currentVersion) {
        const patchVersions = this.patches.get(name);
        if (!patchVersions) {
            throw new Error(`补丁 ${name} 不存在`);
        }

        // 查找兼容的版本
        let compatiblePatch = patchVersions.get(version);

        if (!compatiblePatch) {
            // 查找兼容版本
            for (const [patchVersion, patchInfo] of patchVersions) {
                if (patchInfo.compatibility.includes(version)) {
                    compatiblePatch = patchInfo;
                    break;
                }
            }
        }

        if (!compatiblePatch) {
            throw new Error(`没有找到与版本 ${version} 兼容的补丁 ${name}`);
        }

        // 应用补丁
        compatiblePatch.unpatch = patch(compatiblePatch.target, compatiblePatch.extension);
        console.log(`补丁 ${name} (版本 ${version}) 已应用`);

        return compatiblePatch.unpatch;
    }

    setVersion(version) {
        this.currentVersion = version;
        console.log(`系统版本设置为: ${version}`);
    }
}

// 使用示例
const versionedPatcher = new VersionedPatchSystem();

// 注册不同版本的补丁
versionedPatcher.registerPatch('userService', UserService.prototype, {
    // v1.0.0 的实现
    getUser(id) {
        return this._super(id);
    }
}, '1.0.0');

versionedPatcher.registerPatch('userService', UserService.prototype, {
    // v2.0.0 的实现，添加了缓存
    getUser(id) {
        if (this.userCache && this.userCache.has(id)) {
            return Promise.resolve(this.userCache.get(id));
        }
        return this._super(id);
    }
}, '2.0.0', ['1.5.0', '1.9.0']); // 兼容 1.5.0 和 1.9.0

// 应用特定版本的补丁
versionedPatcher.setVersion('2.0.0');
versionedPatcher.applyPatchForVersion('userService');
```

### 4. 补丁组合模式
```javascript
class PatchComposer {
    constructor() {
        this.mixins = new Map();
        this.compositions = new Map();
    }

    registerMixin(name, mixin) {
        this.mixins.set(name, mixin);
    }

    composePatch(name, mixinNames, customExtensions = {}) {
        const composition = {};

        // 合并所有 mixin
        for (const mixinName of mixinNames) {
            const mixin = this.mixins.get(mixinName);
            if (mixin) {
                Object.assign(composition, mixin);
            }
        }

        // 添加自定义扩展
        Object.assign(composition, customExtensions);

        this.compositions.set(name, composition);
        return composition;
    }

    applyComposition(name, target) {
        const composition = this.compositions.get(name);
        if (!composition) {
            throw new Error(`组合 ${name} 不存在`);
        }

        return patch(target, composition);
    }
}

// 使用示例
const composer = new PatchComposer();

// 注册基础 mixin
composer.registerMixin('observable', {
    on(event, callback) {
        this._listeners = this._listeners || {};
        this._listeners[event] = this._listeners[event] || [];
        this._listeners[event].push(callback);
    },

    emit(event, ...args) {
        if (this._listeners && this._listeners[event]) {
            this._listeners[event].forEach(cb => cb(...args));
        }
    }
});

composer.registerMixin('cacheable', {
    _initCache() {
        this._cache = new Map();
    },

    _getCached(key) {
        return this._cache ? this._cache.get(key) : undefined;
    },

    _setCache(key, value) {
        if (!this._cache) this._initCache();
        this._cache.set(key, value);
    }
});

composer.registerMixin('validatable', {
    validate() {
        this._errors = {};
        if (this.validationRules) {
            // 验证逻辑
        }
        return Object.keys(this._errors).length === 0;
    }
});

// 组合补丁
const userModelPatch = composer.composePatch('userModel',
    ['observable', 'cacheable', 'validatable'],
    {
        // 自定义方法
        save() {
            if (!this.validate()) {
                this.emit('validationError', this._errors);
                return Promise.reject(new Error('Validation failed'));
            }

            this.emit('beforeSave');
            return this._super().then(result => {
                this.emit('afterSave', result);
                return result;
            });
        }
    }
);

// 应用组合补丁
const unpatchUserModel = composer.applyComposition('userModel', UserModel.prototype);
```

## 最佳实践

### 1. 安全的补丁应用
```javascript
// ✅ 推荐：安全的补丁应用
class SafePatchManager {
    constructor() {
        this.appliedPatches = new Map();
    }

    safelyPatch(target, extension, patchName) {
        try {
            // 验证目标对象
            if (!target || typeof target !== 'object') {
                throw new Error('目标对象无效');
            }

            // 验证扩展对象
            if (!extension || typeof extension !== 'object') {
                throw new Error('扩展对象无效');
            }

            // 检查是否已经应用过
            if (this.appliedPatches.has(patchName)) {
                console.warn(`补丁 ${patchName} 已经应用过`);
                return this.appliedPatches.get(patchName);
            }

            // 应用补丁
            const unpatch = patch(target, extension);
            this.appliedPatches.set(patchName, unpatch);

            console.log(`补丁 ${patchName} 应用成功`);
            return unpatch;

        } catch (error) {
            console.error(`补丁 ${patchName} 应用失败:`, error);
            throw error;
        }
    }

    safelyUnpatch(patchName) {
        try {
            const unpatch = this.appliedPatches.get(patchName);
            if (unpatch) {
                unpatch();
                this.appliedPatches.delete(patchName);
                console.log(`补丁 ${patchName} 移除成功`);
            } else {
                console.warn(`补丁 ${patchName} 未找到或未应用`);
            }
        } catch (error) {
            console.error(`补丁 ${patchName} 移除失败:`, error);
            throw error;
        }
    }
}
```

### 2. 性能优化
```javascript
// ✅ 推荐：延迟补丁应用
class LazyPatchManager {
    constructor() {
        this.pendingPatches = new Map();
        this.appliedPatches = new Set();
    }

    registerLazyPatch(target, extension, condition) {
        const patchId = Symbol('patch');

        this.pendingPatches.set(patchId, {
            target,
            extension,
            condition,
            applied: false
        });

        // 设置条件检查
        this.checkCondition(patchId);

        return patchId;
    }

    checkCondition(patchId) {
        const patchInfo = this.pendingPatches.get(patchId);
        if (!patchInfo || patchInfo.applied) return;

        if (patchInfo.condition()) {
            // 条件满足，应用补丁
            patchInfo.unpatch = patch(patchInfo.target, patchInfo.extension);
            patchInfo.applied = true;
            this.appliedPatches.add(patchId);

            console.log('延迟补丁已应用:', patchId);
        } else {
            // 条件不满足，稍后再检查
            setTimeout(() => this.checkCondition(patchId), 1000);
        }
    }
}

// ✅ 推荐：批量补丁应用
class BatchPatchManager {
    constructor() {
        this.batchQueue = [];
        this.batchTimeout = null;
    }

    queuePatch(target, extension) {
        this.batchQueue.push({ target, extension });

        // 延迟批量应用
        if (this.batchTimeout) {
            clearTimeout(this.batchTimeout);
        }

        this.batchTimeout = setTimeout(() => {
            this.applyBatch();
        }, 100);
    }

    applyBatch() {
        console.log(`批量应用 ${this.batchQueue.length} 个补丁`);

        const unpatchers = this.batchQueue.map(({ target, extension }) => {
            return patch(target, extension);
        });

        this.batchQueue = [];
        this.batchTimeout = null;

        return () => {
            unpatchers.forEach(unpatch => unpatch());
        };
    }
}
```

### 3. 调试支持
```javascript
// ✅ 推荐：调试友好的补丁系统
class DebuggablePatchManager {
    constructor() {
        this.patches = new Map();
        this.debugMode = false;
    }

    enableDebug() {
        this.debugMode = true;
        console.log('补丁调试模式已启用');
    }

    disableDebug() {
        this.debugMode = false;
        console.log('补丁调试模式已禁用');
    }

    debugPatch(target, extension, name) {
        if (this.debugMode) {
            console.group(`应用补丁: ${name}`);
            console.log('目标对象:', target);
            console.log('扩展对象:', extension);
            console.log('补丁前的属性:', Object.getOwnPropertyNames(target));
        }

        const unpatch = patch(target, extension);

        if (this.debugMode) {
            console.log('补丁后的属性:', Object.getOwnPropertyNames(target));
            console.groupEnd();
        }

        this.patches.set(name, {
            target,
            extension,
            unpatch,
            appliedAt: new Date()
        });

        return () => {
            if (this.debugMode) {
                console.log(`移除补丁: ${name}`);
            }
            unpatch();
            this.patches.delete(name);
        };
    }

    getPatchInfo(name) {
        return this.patches.get(name);
    }

    listPatches() {
        return Array.from(this.patches.keys());
    }

    inspectPatch(name) {
        const patchInfo = this.patches.get(name);
        if (patchInfo) {
            console.table({
                name,
                target: patchInfo.target.constructor.name,
                appliedAt: patchInfo.appliedAt,
                properties: Object.keys(patchInfo.extension)
            });
        }
    }
}
```

## 总结

Odoo 补丁工具模块提供了强大而灵活的运行时对象修改功能：

**核心优势**:
- **动态修改**: 运行时修改对象属性和方法
- **原型支持**: 正确处理类原型的补丁
- **撤销机制**: 完整的补丁撤销和恢复功能
- **多层叠加**: 支持多个补丁的叠加应用
- **内存安全**: 使用WeakMap避免内存泄漏

**适用场景**:
- 模块化系统扩展
- 第三方库功能增强
- 运行时行为修改
- 插件系统实现
- 测试和调试工具

**设计优势**:
- 完整的属性描述符支持
- 正确的原型链处理
- 安全的内存管理
- 灵活的扩展机制

这个补丁工具为 Odoo Web 客户端提供了强大的运行时扩展能力，是构建模块化和可扩展应用的重要基础。

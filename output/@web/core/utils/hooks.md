# Odoo Hook 工具函数 (Hooks Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/hooks.js`  
**原始路径**: `/web/static/src/core/utils/hooks.js`  
**模块类型**: 核心工具模块 - Hook 工具函数  
**代码行数**: 280 行  
**依赖关系**: 
- `@web/core/browser/feature_detection` - 特性检测 (hasTouch, isMobileOS)
- `@odoo/owl` - OWL 框架 (status, useComponent, useEffect, useRef, onWillUnmount)

## 模块功能

Hook 工具模块是 Odoo Web 客户端的核心工具库，提供了一系列自定义 Hook，用于：
- 自动聚焦管理
- 事件总线集成
- 服务安全访问
- 拼写检查控制
- 引用转发机制
- 对话框生命周期管理
- 事件监听器管理

## 核心 Hook 详解

### 1. useAutofocus() - 自动聚焦 Hook
```javascript
function useAutofocus({ refName, selectAll, mobile } = {}) {
    const ref = useRef(refName || "autofocus");
    const uiService = useService("ui");

    // 防止触摸设备意外弹出虚拟键盘
    if (!mobile && hasTouch()) {
        return ref;
    }

    useEffect(
        (el) => {
            if (el && (!uiService.activeElement || uiService.activeElement.contains(el))) {
                el.focus();
                if (["INPUT", "TEXTAREA"].includes(el.tagName) && el.type !== "number") {
                    el.selectionEnd = el.value.length;
                    el.selectionStart = selectAll ? 0 : el.value.length;
                }
            }
        },
        () => [ref.el]
    );
    return ref;
}
```

**功能特性**:
- **智能聚焦**: 元素出现在DOM时自动聚焦
- **设备适配**: 默认在触摸设备上禁用以避免虚拟键盘
- **文本选择**: 支持全选或光标定位到末尾
- **活动元素检查**: 只在合适的上下文中聚焦

**参数说明**:
- **refName**: 引用名称，默认为 "autofocus"
- **selectAll**: 是否全选文本内容
- **mobile**: 是否在移动设备上强制启用

**使用示例**:
```javascript
class SearchComponent extends Component {
    setup() {
        // 基本自动聚焦
        this.searchRef = useAutofocus();
        
        // 自定义引用名称
        this.inputRef = useAutofocus({ refName: "searchInput" });
        
        // 全选文本内容
        this.editRef = useAutofocus({ selectAll: true });
        
        // 移动设备上也启用
        this.mobileRef = useAutofocus({ mobile: true });
    }
    
    render() {
        return xml`
            <div>
                <input t-ref="autofocus" placeholder="自动聚焦"/>
                <input t-ref="searchInput" placeholder="搜索"/>
                <input t-ref="editInput" value="编辑我" placeholder="全选文本"/>
                <input t-ref="mobileInput" placeholder="移动端聚焦"/>
            </div>
        `;
    }
}
```

### 2. useBus() - 事件总线 Hook
```javascript
function useBus(bus, eventName, callback) {
    const component = useComponent();
    useEffect(
        () => {
            const listener = callback.bind(component);
            bus.addEventListener(eventName, listener);
            return () => bus.removeEventListener(eventName, listener);
        },
        () => []
    );
}
```

**功能特性**:
- **自动绑定**: 自动绑定回调函数的 this 上下文
- **生命周期管理**: 组件卸载时自动清理事件监听器
- **类型安全**: 完整的 TypeScript 类型支持

**使用示例**:
```javascript
class NotificationComponent extends Component {
    setup() {
        this.state = useState({ messages: [] });
        
        // 监听通知事件
        useBus(this.env.bus, "notification", this.onNotification);
        
        // 监听多个事件
        useBus(this.env.bus, "user-login", this.onUserLogin);
        useBus(this.env.bus, "user-logout", this.onUserLogout);
    }
    
    onNotification(event) {
        this.state.messages.push(event.detail);
    }
    
    onUserLogin(event) {
        console.log("用户登录:", event.detail.user);
    }
    
    onUserLogout(event) {
        console.log("用户登出");
        this.state.messages = [];
    }
}
```

### 3. useService() - 服务访问 Hook
```javascript
function useService(serviceName) {
    const component = useComponent();
    const { services } = component.env;
    
    if (!(serviceName in services)) {
        throw new Error(`Service ${serviceName} is not available`);
    }
    
    const service = services[serviceName];
    
    // 如果服务有保护方法元数据，则包装方法
    if (serviceName in SERVICES_METADATA) {
        if (service instanceof Function) {
            return _protectMethod(component, service);
        } else {
            const methods = SERVICES_METADATA[serviceName];
            const result = Object.create(service);
            for (const method of methods) {
                result[method] = _protectMethod(component, service[method]);
            }
            return result;
        }
    }
    
    return service;
}
```

**功能特性**:
- **服务注入**: 从环境中注入指定服务
- **错误检查**: 检查服务是否可用
- **方法保护**: 自动保护异步方法，防止组件销毁后的调用
- **类型安全**: 完整的 TypeScript 类型推断

**方法保护机制**:
```javascript
function _protectMethod(component, fn) {
    return function (...args) {
        if (status(component) === "destroyed") {
            return useServiceProtectMethodHandling.fn();
        }

        const prom = Promise.resolve(fn.call(this, ...args));
        const protectedProm = prom.then((result) =>
            status(component) === "destroyed" ? new Promise(() => {}) : result
        );
        return Object.assign(protectedProm, {
            abort: prom.abort,
            cancel: prom.cancel,
        });
    };
}
```

**使用示例**:
```javascript
class DataComponent extends Component {
    setup() {
        // 注入各种服务
        this.http = useService("http");
        this.notification = useService("notification");
        this.dialog = useService("dialog");
        this.router = useService("router");
    }
    
    async loadData() {
        try {
            // 即使组件在请求期间被销毁，也不会导致错误
            const data = await this.http.get("/api/data");
            this.state.data = data;
        } catch (error) {
            this.notification.add("加载失败", { type: "danger" });
        }
    }
    
    showDialog() {
        this.dialog.add(ConfirmDialog, {
            title: "确认操作",
            body: "您确定要执行此操作吗？",
            confirm: () => this.performAction()
        });
    }
}
```

### 4. useSpellCheck() - 拼写检查 Hook
```javascript
function useSpellCheck({ refName } = {}) {
    const elements = [];
    const ref = useRef(refName || "spellcheck");
    
    function toggleSpellcheck(ev) {
        ev.target.spellcheck = document.activeElement === ev.target;
    }
    
    useEffect(
        (el) => {
            if (el) {
                const inputs = ["INPUT", "TEXTAREA"].includes(el.nodeName) || el.contenteditable
                    ? [el]
                    : el.querySelectorAll("input, textarea, [contenteditable=true]");
                    
                inputs.forEach((input) => {
                    if (input.spellcheck !== false) {
                        elements.push(input);
                        input.addEventListener("focus", toggleSpellcheck);
                        input.addEventListener("blur", toggleSpellcheck);
                    }
                });
            }
            return () => {
                elements.forEach((input) => {
                    input.removeEventListener("focus", toggleSpellcheck);
                    input.removeEventListener("blur", toggleSpellcheck);
                });
            };
        },
        () => [ref.el]
    );
}
```

**功能特性**:
- **动态控制**: 只在聚焦时启用拼写检查
- **性能优化**: 避免元素失焦后保持拼写检查外观
- **自动发现**: 自动查找容器内的输入元素
- **尊重设置**: 尊重元素的 spellcheck 属性设置

**使用示例**:
```javascript
class EditorComponent extends Component {
    setup() {
        // 为整个编辑器容器启用智能拼写检查
        useSpellCheck({ refName: "editor" });
    }
    
    render() {
        return xml`
            <div t-ref="editor" class="editor-container">
                <input type="text" placeholder="标题"/>
                <textarea placeholder="内容"></textarea>
                <div contenteditable="true">富文本编辑器</div>
            </div>
        `;
    }
}
```

### 5. useChildRef() 和 useForwardRefToParent() - 引用转发
```javascript
// 父组件中使用
function useChildRef() {
    let defined = false;
    let value;
    return function ref(v) {
        value = v;
        if (defined) {
            return;
        }
        Object.defineProperty(ref, "el", {
            get() {
                return value.el;
            },
        });
        defined = true;
    };
}

// 子组件中使用
function useForwardRefToParent(refName) {
    const component = useComponent();
    const ref = useRef(refName);
    if (component.props[refName]) {
        component.props[refName](ref);
    }
    return ref;
}
```

**功能特性**:
- **引用转发**: 子组件可以将引用转发给父组件
- **类型安全**: 保持引用的类型安全性
- **延迟定义**: 支持引用的延迟定义

**使用示例**:
```javascript
// 子组件
class InputComponent extends Component {
    setup() {
        // 将输入框引用转发给父组件
        this.inputRef = useForwardRefToParent("inputRef");
    }
    
    render() {
        return xml`
            <input t-ref="inputRef" type="text" placeholder="输入内容"/>
        `;
    }
}

// 父组件
class FormComponent extends Component {
    setup() {
        // 接收子组件转发的引用
        this.childInputRef = useChildRef();
    }
    
    focusInput() {
        this.childInputRef.el?.focus();
    }
    
    render() {
        return xml`
            <div>
                <InputComponent inputRef="childInputRef"/>
                <button t-on-click="focusInput">聚焦输入框</button>
            </div>
        `;
    }
}
```

### 6. useOwnedDialogs() - 对话框生命周期管理
```javascript
function useOwnedDialogs() {
    const dialogService = useService("dialog");
    const cbs = [];
    
    onWillUnmount(() => {
        cbs.forEach((cb) => cb());
    });
    
    const addDialog = (...args) => {
        const close = dialogService.add(...args);
        cbs.push(close);
        return close;
    };
    
    return addDialog;
}
```

**功能特性**:
- **自动清理**: 组件卸载时自动关闭所有对话框
- **生命周期绑定**: 对话框生命周期与组件绑定
- **内存安全**: 防止对话框内存泄漏

**使用示例**:
```javascript
class ManagerComponent extends Component {
    setup() {
        // 使用自动管理的对话框服务
        this.addDialog = useOwnedDialogs();
    }
    
    showConfirmDialog() {
        this.addDialog(ConfirmDialog, {
            title: "确认删除",
            body: "此操作不可撤销，确定要删除吗？",
            confirm: () => this.deleteItem()
        });
    }
    
    showInfoDialog() {
        this.addDialog(InfoDialog, {
            title: "信息",
            body: "操作已完成"
        });
    }
    
    // 组件卸载时，所有对话框会自动关闭
}
```

### 7. useRefListener() - 引用事件监听器
```javascript
function useRefListener(ref, ...listener) {
    useEffect(
        (el) => {
            el?.addEventListener(...listener);
            return () => el?.removeEventListener(...listener);
        },
        () => [ref.el]
    );
}
```

**功能特性**:
- **引用绑定**: 为引用元素添加事件监听器
- **自动清理**: 组件卸载时自动移除监听器
- **灵活参数**: 支持 addEventListener 的所有参数

**使用示例**:
```javascript
class InteractiveComponent extends Component {
    setup() {
        this.containerRef = useRef("container");
        
        // 添加多个事件监听器
        useRefListener(this.containerRef, "click", this.onClick.bind(this));
        useRefListener(this.containerRef, "mouseover", this.onMouseOver.bind(this));
        useRefListener(this.containerRef, "scroll", this.onScroll.bind(this), { passive: true });
    }
    
    onClick(event) {
        console.log("点击事件:", event.target);
    }
    
    onMouseOver(event) {
        console.log("鼠标悬停:", event.target);
    }
    
    onScroll(event) {
        console.log("滚动事件:", event.target.scrollTop);
    }
    
    render() {
        return xml`
            <div t-ref="container" class="interactive-container">
                <p>交互式内容</p>
                <button>点击我</button>
            </div>
        `;
    }
}
```

## 高级应用场景

### 1. 复合 Hook 模式
```javascript
// 创建复合 Hook，组合多个基础 Hook
function useFormField(refName, options = {}) {
    const ref = useAutofocus({
        refName,
        selectAll: options.selectAll
    });

    // 添加拼写检查
    if (options.spellCheck) {
        useSpellCheck({ refName });
    }

    // 添加验证事件监听
    if (options.validate) {
        useRefListener(ref, "blur", (event) => {
            options.validate(event.target.value);
        });
    }

    return ref;
}

// 使用复合 Hook
class FormComponent extends Component {
    setup() {
        this.nameRef = useFormField("name", {
            selectAll: true,
            validate: (value) => this.validateName(value)
        });

        this.emailRef = useFormField("email", {
            spellCheck: false,
            validate: (value) => this.validateEmail(value)
        });
    }
}
```

### 2. 服务组合模式
```javascript
// 创建专用的数据管理 Hook
function useDataManager() {
    const http = useService("http");
    const notification = useService("notification");
    const ui = useService("ui");

    const loadData = async (url) => {
        ui.block({ message: "加载数据中..." });
        try {
            const data = await http.get(url);
            notification.add("数据加载成功", { type: "success" });
            return data;
        } catch (error) {
            notification.add("数据加载失败", { type: "danger" });
            throw error;
        } finally {
            ui.unblock();
        }
    };

    const saveData = async (url, data) => {
        ui.block({ message: "保存数据中..." });
        try {
            const result = await http.post(url, data);
            notification.add("数据保存成功", { type: "success" });
            return result;
        } catch (error) {
            notification.add("数据保存失败", { type: "danger" });
            throw error;
        } finally {
            ui.unblock();
        }
    };

    return { loadData, saveData };
}

// 使用数据管理 Hook
class DataComponent extends Component {
    setup() {
        this.dataManager = useDataManager();
        this.state = useState({ data: null });
    }

    async loadUserData() {
        try {
            this.state.data = await this.dataManager.loadData("/api/users");
        } catch (error) {
            console.error("加载用户数据失败:", error);
        }
    }
}
```

### 3. 事件总线模式
```javascript
// 创建专用的通信 Hook
function useComponentCommunication() {
    const bus = useService("bus");

    // 发送消息
    const sendMessage = (type, data) => {
        bus.trigger(`component:${type}`, data);
    };

    // 监听消息
    const onMessage = (type, callback) => {
        useBus(bus, `component:${type}`, callback);
    };

    // 广播状态变化
    const broadcastStateChange = (componentId, state) => {
        sendMessage("state-change", { componentId, state });
    };

    return { sendMessage, onMessage, broadcastStateChange };
}

// 使用通信 Hook
class ParentComponent extends Component {
    setup() {
        this.comm = useComponentCommunication();
        this.state = useState({ childStates: {} });

        // 监听子组件状态变化
        this.comm.onMessage("state-change", (event) => {
            const { componentId, state } = event.detail;
            this.state.childStates[componentId] = state;
        });
    }
}

class ChildComponent extends Component {
    setup() {
        this.comm = useComponentCommunication();
        this.state = useState({ value: 0 });

        // 状态变化时广播
        useEffect(() => {
            this.comm.broadcastStateChange(this.props.id, this.state);
        }, () => [this.state.value]);
    }
}
```

## 最佳实践

### 1. Hook 命名规范
```javascript
// ✅ 推荐：使用 use 前缀和描述性名称
function useFormValidation() { /* ... */ }
function useDataFetching() { /* ... */ }
function useKeyboardNavigation() { /* ... */ }

// ❌ 避免：模糊的名称
function formHook() { /* ... */ }
function dataStuff() { /* ... */ }
function keyHandler() { /* ... */ }
```

### 2. Hook 组合原则
```javascript
// ✅ 推荐：创建专用的复合 Hook
function useModalForm(formConfig) {
    const addDialog = useOwnedDialogs();
    const notification = useService("notification");

    const showForm = useCallback((initialData = {}) => {
        return new Promise((resolve, reject) => {
            addDialog(FormDialog, {
                ...formConfig,
                initialData,
                onSave: async (data) => {
                    try {
                        const result = await saveData(data);
                        notification.add("保存成功", { type: "success" });
                        resolve(result);
                    } catch (error) {
                        notification.add("保存失败", { type: "danger" });
                        reject(error);
                    }
                },
                onCancel: () => reject(new Error("用户取消"))
            });
        });
    }, [addDialog, notification, formConfig]);

    return { showForm };
}
```

### 3. 错误处理
```javascript
// ✅ 推荐：在 Hook 中提供错误处理
function useSafeService(serviceName) {
    try {
        return useService(serviceName);
    } catch (error) {
        console.warn(`Service ${serviceName} not available:`, error);
        return null;
    }
}

// ✅ 推荐：提供降级方案
function useOptionalAutofocus(options = {}) {
    try {
        return useAutofocus(options);
    } catch (error) {
        console.warn("Autofocus failed:", error);
        return useRef(options.refName || "autofocus");
    }
}
```

### 4. 性能优化
```javascript
// ✅ 推荐：使用 useCallback 优化事件处理器
function OptimizedComponent() {
    const [count, setCount] = useState(0);
    const containerRef = useRef("container");

    const handleClick = useCallback((event) => {
        console.log("Clicked:", event.target);
        setCount(c => c + 1);
    }, []); // 空依赖数组，函数不会重新创建

    useRefListener(containerRef, "click", handleClick);
}
```

## 常见陷阱和注意事项

### 1. Hook 调用顺序
```javascript
// ❌ 错误：条件性调用 Hook
function BadComponent({ showFeature }) {
    if (showFeature) {
        const service = useService("feature"); // 错误！Hook 不能条件性调用
    }
}

// ✅ 正确：始终调用 Hook，条件性使用结果
function GoodComponent({ showFeature }) {
    const service = useService("feature");

    if (showFeature) {
        // 条件性使用服务
        service.doSomething();
    }
}
```

### 2. 事件监听器内存泄漏
```javascript
// ❌ 错误：忘记清理事件监听器
function BadComponent() {
    const ref = useRef("element");

    useEffect(() => {
        const element = ref.el;
        if (element) {
            element.addEventListener("click", handleClick);
            // 忘记返回清理函数
        }
    }, () => [ref.el]);
}

// ✅ 正确：使用 useRefListener 或手动清理
function GoodComponent() {
    const ref = useRef("element");

    useRefListener(ref, "click", handleClick); // 自动清理
}
```

## 总结

Odoo Hook 工具模块提供了丰富而强大的 Hook 功能：

**核心优势**:
- **生命周期管理**: 自动处理组件生命周期相关的清理工作
- **服务集成**: 安全、便捷的服务访问机制
- **事件处理**: 简化的事件监听器管理
- **引用转发**: 灵活的组件间引用传递
- **性能优化**: 内置的性能优化和内存管理

**适用场景**:
- 表单输入管理
- 事件总线通信
- 服务依赖注入
- 对话框生命周期管理
- 键盘快捷键处理

这个工具模块为 Odoo Web 客户端提供了强大的 Hook 基础设施，是构建现代 React-like 组件的重要工具。

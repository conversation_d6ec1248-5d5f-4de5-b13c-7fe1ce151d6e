# Odoo 搜索工具 (Search Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/search.js`  
**原始路径**: `/web/static/src/core/utils/search.js`  
**模块类型**: 核心工具模块 - 模糊搜索工具  
**代码行数**: 96 行  
**依赖关系**: 
- `@web/core/utils/strings` - 字符串工具 (unaccent函数)

## 模块功能

搜索工具模块是 Odoo Web 客户端的核心工具库，提供了强大的模糊搜索功能。该模块支持：
- 模糊字符串匹配
- 智能评分算法
- 多字符串匹配
- 重音符号处理
- 搜索结果排序

这些功能是构建现代搜索体验的重要基础，广泛应用于命令面板、自动完成、数据过滤等场景。

## 模糊搜索原理

### 评分算法
模糊搜索基于智能评分算法，考虑以下因素：
1. **字符匹配**: 模式中的字符必须按顺序出现在目标字符串中
2. **连续性奖励**: 连续匹配的字符获得更高分数
3. **位置权重**: 越靠前的匹配获得更高分数
4. **累积评分**: 每个匹配字符都会累积分数

### 评分公式
```javascript
currentScore += 100 + currentScore - i / 200;
```
- **基础分**: 每个匹配字符获得100分
- **连续奖励**: 连续匹配时累积当前分数
- **位置惩罚**: 位置越靠后，分数略微减少

### 重音符号处理
使用 `unaccent` 函数处理重音符号，确保搜索的准确性：
- `café` 可以匹配 `cafe`
- `naïve` 可以匹配 `naive`

## 核心函数详解

### 1. fuzzyLookup() - 模糊搜索查找
```javascript
function fuzzyLookup(pattern, list, fn) {
    const results = [];
    list.forEach((data) => {
        const score = match(pattern, fn(data));
        if (score > 0) {
            results.push({ score, elem: data });
        }
    });

    // 按分数降序排列
    results.sort((a, b) => b.score - a.score);

    return results.map((r) => r.elem);
}
```

**功能特性**:
- **泛型支持**: 支持任意类型的数据列表
- **自定义提取**: 通过函数参数自定义字符串提取逻辑
- **智能排序**: 按匹配分数自动排序结果
- **过滤无效**: 自动过滤掉不匹配的项目

**使用示例**:
```javascript
// 基本用法：搜索字符串数组
const fruits = ['apple', 'banana', 'orange', 'grape', 'pineapple'];
const results = fuzzyLookup('app', fruits, (item) => item);
console.log(results); // ['apple', 'pineapple']

// 搜索对象数组
const users = [
    { id: 1, name: 'John Doe', email: '<EMAIL>' },
    { id: 2, name: 'Jane Smith', email: '<EMAIL>' },
    { id: 3, name: 'Bob Johnson', email: '<EMAIL>' }
];

// 按姓名搜索
const nameResults = fuzzyLookup('jo', users, (user) => user.name);
console.log(nameResults); // [{ id: 1, name: 'John Doe', ... }, { id: 3, name: 'Bob Johnson', ... }]

// 按多个字段搜索
const multiResults = fuzzyLookup('jo', users, (user) => [user.name, user.email]);
console.log(multiResults); // 同时匹配姓名和邮箱
```

**实际应用场景**:
```javascript
// 1. 命令面板搜索
class CommandPalette {
    constructor() {
        this.commands = [
            { id: 'new-file', name: 'New File', description: 'Create a new file', shortcut: 'Ctrl+N' },
            { id: 'open-file', name: 'Open File', description: 'Open an existing file', shortcut: 'Ctrl+O' },
            { id: 'save-file', name: 'Save File', description: 'Save current file', shortcut: 'Ctrl+S' },
            { id: 'find-replace', name: 'Find and Replace', description: 'Find and replace text', shortcut: 'Ctrl+H' }
        ];
    }
    
    search(query) {
        if (!query.trim()) {
            return this.commands;
        }
        
        return fuzzyLookup(query, this.commands, (cmd) => [
            cmd.name,
            cmd.description,
            cmd.shortcut
        ]);
    }
    
    renderResults(query) {
        const results = this.search(query);
        return results.map(cmd => ({
            ...cmd,
            highlighted: this.highlightMatch(cmd.name, query)
        }));
    }
    
    highlightMatch(text, pattern) {
        // 高亮匹配的字符
        let result = '';
        let patternIndex = 0;
        
        for (let i = 0; i < text.length; i++) {
            const char = text[i];
            if (patternIndex < pattern.length && 
                char.toLowerCase() === pattern[patternIndex].toLowerCase()) {
                result += `<mark>${char}</mark>`;
                patternIndex++;
            } else {
                result += char;
            }
        }
        
        return result;
    }
}

// 2. 产品搜索系统
class ProductSearchEngine {
    constructor(products) {
        this.products = products;
        this.searchHistory = [];
        this.maxHistorySize = 100;
    }
    
    search(query, options = {}) {
        const {
            category = null,
            priceRange = null,
            inStock = null,
            limit = 50
        } = options;
        
        // 记录搜索历史
        this.addToHistory(query, options);
        
        // 预过滤
        let filteredProducts = this.preFilter(this.products, {
            category,
            priceRange,
            inStock
        });
        
        // 模糊搜索
        const results = fuzzyLookup(query, filteredProducts, (product) => [
            product.name,
            product.description,
            product.brand,
            product.category,
            product.tags?.join(' ') || ''
        ]);
        
        // 限制结果数量
        return results.slice(0, limit);
    }
    
    preFilter(products, filters) {
        return products.filter(product => {
            if (filters.category && product.category !== filters.category) {
                return false;
            }
            
            if (filters.priceRange) {
                const { min, max } = filters.priceRange;
                if (product.price < min || product.price > max) {
                    return false;
                }
            }
            
            if (filters.inStock !== null && product.inStock !== filters.inStock) {
                return false;
            }
            
            return true;
        });
    }
    
    addToHistory(query, options) {
        const historyItem = {
            query,
            options,
            timestamp: Date.now()
        };
        
        this.searchHistory.unshift(historyItem);
        
        if (this.searchHistory.length > this.maxHistorySize) {
            this.searchHistory = this.searchHistory.slice(0, this.maxHistorySize);
        }
    }
    
    getSearchSuggestions(query) {
        // 基于搜索历史生成建议
        const historySuggestions = this.searchHistory
            .filter(item => item.query.toLowerCase().includes(query.toLowerCase()))
            .map(item => item.query)
            .slice(0, 5);
        
        // 基于产品名称生成建议
        const productSuggestions = fuzzyLookup(query, this.products, (product) => product.name)
            .slice(0, 5)
            .map(product => product.name);
        
        // 合并并去重
        const allSuggestions = [...new Set([...historySuggestions, ...productSuggestions])];
        return allSuggestions.slice(0, 8);
    }
}

// 3. 智能自动完成组件
class SmartAutocomplete {
    constructor(inputElement, dataSource, options = {}) {
        this.input = inputElement;
        this.dataSource = dataSource;
        this.options = {
            minLength: 2,
            maxResults: 10,
            debounceDelay: 300,
            highlightMatch: true,
            ...options
        };
        
        this.suggestions = [];
        this.selectedIndex = -1;
        this.isOpen = false;
        
        this.setupEventListeners();
        this.createSuggestionsList();
    }
    
    setupEventListeners() {
        let debounceTimeout;
        
        this.input.addEventListener('input', (e) => {
            clearTimeout(debounceTimeout);
            debounceTimeout = setTimeout(() => {
                this.handleInput(e.target.value);
            }, this.options.debounceDelay);
        });
        
        this.input.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });
        
        this.input.addEventListener('blur', () => {
            setTimeout(() => this.closeSuggestions(), 150);
        });
    }
    
    createSuggestionsList() {
        this.suggestionsList = document.createElement('ul');
        this.suggestionsList.className = 'autocomplete-suggestions';
        this.suggestionsList.style.display = 'none';
        
        this.input.parentNode.appendChild(this.suggestionsList);
    }
    
    handleInput(value) {
        if (value.length < this.options.minLength) {
            this.closeSuggestions();
            return;
        }
        
        this.suggestions = this.search(value);
        this.renderSuggestions();
        
        if (this.suggestions.length > 0) {
            this.openSuggestions();
        } else {
            this.closeSuggestions();
        }
    }
    
    search(query) {
        if (typeof this.dataSource === 'function') {
            return this.dataSource(query);
        } else if (Array.isArray(this.dataSource)) {
            return fuzzyLookup(query, this.dataSource, this.options.extractText || ((item) => item))
                .slice(0, this.options.maxResults);
        }
        return [];
    }
    
    renderSuggestions() {
        this.suggestionsList.innerHTML = '';
        
        this.suggestions.forEach((suggestion, index) => {
            const li = document.createElement('li');
            li.className = 'autocomplete-suggestion';
            li.dataset.index = index;
            
            if (this.options.highlightMatch) {
                li.innerHTML = this.highlightMatch(
                    this.options.extractText ? this.options.extractText(suggestion) : suggestion,
                    this.input.value
                );
            } else {
                li.textContent = this.options.extractText ? this.options.extractText(suggestion) : suggestion;
            }
            
            li.addEventListener('click', () => {
                this.selectSuggestion(index);
            });
            
            this.suggestionsList.appendChild(li);
        });
    }
    
    highlightMatch(text, pattern) {
        let result = '';
        let patternIndex = 0;
        const patternLower = pattern.toLowerCase();
        
        for (let i = 0; i < text.length; i++) {
            const char = text[i];
            if (patternIndex < patternLower.length && 
                char.toLowerCase() === patternLower[patternIndex]) {
                result += `<strong>${char}</strong>`;
                patternIndex++;
            } else {
                result += char;
            }
        }
        
        return result;
    }
    
    handleKeydown(e) {
        if (!this.isOpen) return;
        
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.selectedIndex = Math.min(this.selectedIndex + 1, this.suggestions.length - 1);
                this.updateSelection();
                break;
                
            case 'ArrowUp':
                e.preventDefault();
                this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
                this.updateSelection();
                break;
                
            case 'Enter':
                e.preventDefault();
                if (this.selectedIndex >= 0) {
                    this.selectSuggestion(this.selectedIndex);
                }
                break;
                
            case 'Escape':
                this.closeSuggestions();
                break;
        }
    }
    
    updateSelection() {
        const items = this.suggestionsList.querySelectorAll('.autocomplete-suggestion');
        items.forEach((item, index) => {
            item.classList.toggle('selected', index === this.selectedIndex);
        });
    }
    
    selectSuggestion(index) {
        const suggestion = this.suggestions[index];
        const text = this.options.extractText ? this.options.extractText(suggestion) : suggestion;
        
        this.input.value = text;
        this.closeSuggestions();
        
        // 触发自定义事件
        this.input.dispatchEvent(new CustomEvent('autocomplete:select', {
            detail: { suggestion, text }
        }));
    }
    
    openSuggestions() {
        this.isOpen = true;
        this.suggestionsList.style.display = 'block';
    }
    
    closeSuggestions() {
        this.isOpen = false;
        this.selectedIndex = -1;
        this.suggestionsList.style.display = 'none';
    }
}
```

### 2. fuzzyTest() - 模糊匹配测试
```javascript
function fuzzyTest(pattern, string) {
    return _match(pattern, string) !== 0;
}
```

**功能特性**:
- **布尔返回**: 简单的是/否匹配判断
- **性能优化**: 只需要知道是否匹配，不需要计算分数
- **快速过滤**: 适合大量数据的快速过滤
- **条件判断**: 适合在条件语句中使用

**使用示例**:
```javascript
// 基本匹配测试
console.log(fuzzyTest('app', 'apple'));      // true
console.log(fuzzyTest('app', 'banana'));     // false
console.log(fuzzyTest('js', 'javascript'));  // true

// 数组过滤
const items = ['apple', 'banana', 'grape', 'pineapple'];
const filtered = items.filter(item => fuzzyTest('app', item));
console.log(filtered); // ['apple', 'pineapple']

// 条件判断
const searchQuery = 'doc';
const fileName = 'document.pdf';
if (fuzzyTest(searchQuery, fileName)) {
    console.log('文件匹配搜索条件');
}
```

### 3. match() - 匹配评分
```javascript
function match(pattern, strs) {
    if (!Array.isArray(strs)) {
        strs = [strs];
    }
    let globalScore = 0;
    for (const str of strs) {
        globalScore = Math.max(globalScore, _match(pattern, str));
    }
    return globalScore;
}
```

**功能特性**:
- **多字符串支持**: 可以同时匹配多个字符串
- **最高分选择**: 返回所有字符串中的最高匹配分数
- **灵活输入**: 支持单个字符串或字符串数组
- **精确评分**: 提供详细的匹配分数

**使用示例**:
```javascript
// 单字符串匹配
const score1 = match('app', 'apple');
console.log(score1); // 返回具体分数

// 多字符串匹配
const score2 = match('js', ['javascript', 'java', 'json']);
console.log(score2); // 返回最高分数

// 对象属性匹配
const user = { name: 'John Doe', email: '<EMAIL>' };
const score3 = match('jo', [user.name, user.email]);
console.log(score3); // 匹配姓名和邮箱中的最高分
```

### 4. _match() - 内部匹配算法
```javascript
function _match(pattern, str) {
    let totalScore = 0;
    let currentScore = 0;
    const len = str.length;
    let patternIndex = 0;

    pattern = unaccent(pattern, false);
    str = unaccent(str, false);

    for (let i = 0; i < len; i++) {
        if (str[i] === pattern[patternIndex]) {
            patternIndex++;
            currentScore += 100 + currentScore - i / 200;
        } else {
            currentScore = 0;
        }
        totalScore = totalScore + currentScore;
    }

    return patternIndex === pattern.length ? totalScore : 0;
}
```

**算法特点**:
- **重音符号处理**: 自动处理重音符号和特殊字符
- **连续性奖励**: 连续匹配获得指数级分数增长
- **位置权重**: 早期匹配获得更高分数
- **完整匹配**: 只有完全匹配模式才返回分数

## 高级应用模式

### 1. 多维度搜索引擎
```javascript
class MultiDimensionalSearchEngine {
    constructor() {
        this.dimensions = new Map();
        this.weights = new Map();
        this.cache = new Map();
        this.cacheSize = 1000;
    }

    addDimension(name, extractor, weight = 1) {
        this.dimensions.set(name, extractor);
        this.weights.set(name, weight);
        this.clearCache();
    }

    search(query, data, options = {}) {
        const cacheKey = `${query}_${JSON.stringify(options)}`;

        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        const results = data.map(item => {
            let totalScore = 0;
            let maxScore = 0;

            for (const [dimension, extractor] of this.dimensions) {
                const text = extractor(item);
                const score = match(query, text);
                const weight = this.weights.get(dimension);

                totalScore += score * weight;
                maxScore = Math.max(maxScore, score);
            }

            return {
                item,
                totalScore,
                maxScore,
                normalizedScore: totalScore / this.getTotalWeight()
            };
        })
        .filter(result => result.maxScore > 0)
        .sort((a, b) => {
            // 优先按最高分排序，然后按总分排序
            if (b.maxScore !== a.maxScore) {
                return b.maxScore - a.maxScore;
            }
            return b.totalScore - a.totalScore;
        })
        .map(result => result.item);

        // 缓存结果
        this.addToCache(cacheKey, results);

        return results;
    }

    getTotalWeight() {
        return Array.from(this.weights.values()).reduce((sum, weight) => sum + weight, 0);
    }

    addToCache(key, value) {
        if (this.cache.size >= this.cacheSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        this.cache.set(key, value);
    }

    clearCache() {
        this.cache.clear();
    }
}

// 使用示例
const searchEngine = new MultiDimensionalSearchEngine();

// 配置搜索维度
searchEngine.addDimension('title', (item) => item.title, 3);      // 标题权重最高
searchEngine.addDimension('content', (item) => item.content, 2);  // 内容权重中等
searchEngine.addDimension('tags', (item) => item.tags.join(' '), 1); // 标签权重最低

const articles = [
    { title: 'JavaScript Basics', content: 'Learn JS fundamentals', tags: ['js', 'programming'] },
    { title: 'Advanced React', content: 'Deep dive into React', tags: ['react', 'frontend'] },
    { title: 'Node.js Guide', content: 'Server-side JavaScript', tags: ['nodejs', 'backend'] }
];

const results = searchEngine.search('js', articles);
console.log(results); // 按多维度评分排序的结果
```

### 2. 实时搜索建议系统
```javascript
class RealTimeSearchSuggestions {
    constructor(options = {}) {
        this.options = {
            maxSuggestions: 10,
            minQueryLength: 2,
            debounceDelay: 200,
            cacheTimeout: 5 * 60 * 1000, // 5分钟
            ...options
        };

        this.cache = new Map();
        this.pendingRequests = new Map();
        this.analytics = {
            searches: 0,
            cacheHits: 0,
            avgResponseTime: 0
        };
    }

    async getSuggestions(query, dataSources = []) {
        const startTime = performance.now();
        this.analytics.searches++;

        if (query.length < this.options.minQueryLength) {
            return [];
        }

        // 检查缓存
        const cacheKey = this.getCacheKey(query, dataSources);
        const cached = this.getFromCache(cacheKey);
        if (cached) {
            this.analytics.cacheHits++;
            return cached;
        }

        // 检查是否有相同的请求正在进行
        if (this.pendingRequests.has(cacheKey)) {
            return this.pendingRequests.get(cacheKey);
        }

        // 创建新的搜索请求
        const searchPromise = this.performSearch(query, dataSources);
        this.pendingRequests.set(cacheKey, searchPromise);

        try {
            const results = await searchPromise;

            // 缓存结果
            this.addToCache(cacheKey, results);

            // 更新分析数据
            const endTime = performance.now();
            this.updateAnalytics(endTime - startTime);

            return results;
        } finally {
            this.pendingRequests.delete(cacheKey);
        }
    }

    async performSearch(query, dataSources) {
        const allResults = [];

        // 并行搜索所有数据源
        const searchPromises = dataSources.map(async (source) => {
            try {
                const data = await this.loadDataSource(source);
                return fuzzyLookup(query, data, source.extractor);
            } catch (error) {
                console.error(`搜索数据源失败: ${source.name}`, error);
                return [];
            }
        });

        const results = await Promise.all(searchPromises);

        // 合并和排序结果
        results.forEach((sourceResults, index) => {
            const source = dataSources[index];
            sourceResults.forEach(item => {
                allResults.push({
                    item,
                    source: source.name,
                    score: match(query, source.extractor(item))
                });
            });
        });

        // 按分数排序并限制数量
        return allResults
            .sort((a, b) => b.score - a.score)
            .slice(0, this.options.maxSuggestions)
            .map(result => ({
                ...result.item,
                _source: result.source,
                _score: result.score
            }));
    }

    async loadDataSource(source) {
        if (typeof source.data === 'function') {
            return await source.data();
        } else if (Array.isArray(source.data)) {
            return source.data;
        } else if (typeof source.data === 'string') {
            // URL数据源
            const response = await fetch(source.data);
            return await response.json();
        }
        return [];
    }

    getCacheKey(query, dataSources) {
        const sourceKeys = dataSources.map(s => s.name).join(',');
        return `${query.toLowerCase()}_${sourceKeys}`;
    }

    getFromCache(key) {
        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < this.options.cacheTimeout) {
            return cached.data;
        }
        if (cached) {
            this.cache.delete(key);
        }
        return null;
    }

    addToCache(key, data) {
        this.cache.set(key, {
            data,
            timestamp: Date.now()
        });

        // 清理过期缓存
        this.cleanupCache();
    }

    cleanupCache() {
        const now = Date.now();
        for (const [key, value] of this.cache) {
            if (now - value.timestamp > this.options.cacheTimeout) {
                this.cache.delete(key);
            }
        }
    }

    updateAnalytics(responseTime) {
        const { searches, avgResponseTime } = this.analytics;
        this.analytics.avgResponseTime =
            (avgResponseTime * (searches - 1) + responseTime) / searches;
    }

    getAnalytics() {
        return {
            ...this.analytics,
            cacheHitRate: this.analytics.cacheHits / this.analytics.searches * 100
        };
    }
}

// 使用示例
const suggestionSystem = new RealTimeSearchSuggestions({
    maxSuggestions: 8,
    debounceDelay: 150
});

const dataSources = [
    {
        name: 'users',
        data: async () => {
            const response = await fetch('/api/users');
            return response.json();
        },
        extractor: (user) => [user.name, user.email]
    },
    {
        name: 'documents',
        data: [
            { title: 'User Guide', type: 'pdf' },
            { title: 'API Documentation', type: 'html' }
        ],
        extractor: (doc) => doc.title
    }
];

// 获取搜索建议
suggestionSystem.getSuggestions('john', dataSources)
    .then(suggestions => {
        console.log('搜索建议:', suggestions);
    });
```

### 3. 智能搜索分析器
```javascript
class SearchAnalyzer {
    constructor() {
        this.searchLogs = [];
        this.patterns = new Map();
        this.performance = new Map();
    }

    logSearch(query, results, responseTime) {
        const logEntry = {
            query: query.toLowerCase(),
            resultCount: results.length,
            responseTime,
            timestamp: Date.now(),
            hasResults: results.length > 0
        };

        this.searchLogs.push(logEntry);
        this.analyzePattern(query);
        this.updatePerformance(query, responseTime);

        // 限制日志大小
        if (this.searchLogs.length > 10000) {
            this.searchLogs = this.searchLogs.slice(-5000);
        }
    }

    analyzePattern(query) {
        const pattern = this.extractPattern(query);
        const current = this.patterns.get(pattern) || { count: 0, queries: new Set() };

        current.count++;
        current.queries.add(query);

        this.patterns.set(pattern, current);
    }

    extractPattern(query) {
        // 提取搜索模式
        const length = query.length;
        const hasNumbers = /\d/.test(query);
        const hasSpecialChars = /[^a-zA-Z0-9\s]/.test(query);
        const wordCount = query.split(/\s+/).length;

        return `len:${length}_words:${wordCount}_num:${hasNumbers}_special:${hasSpecialChars}`;
    }

    updatePerformance(query, responseTime) {
        const key = query.toLowerCase();
        const current = this.performance.get(key) || {
            count: 0,
            totalTime: 0,
            minTime: Infinity,
            maxTime: 0
        };

        current.count++;
        current.totalTime += responseTime;
        current.minTime = Math.min(current.minTime, responseTime);
        current.maxTime = Math.max(current.maxTime, responseTime);
        current.avgTime = current.totalTime / current.count;

        this.performance.set(key, current);
    }

    getPopularQueries(limit = 10) {
        const queryFrequency = new Map();

        this.searchLogs.forEach(log => {
            const count = queryFrequency.get(log.query) || 0;
            queryFrequency.set(log.query, count + 1);
        });

        return Array.from(queryFrequency.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, limit)
            .map(([query, count]) => ({ query, count }));
    }

    getFailedQueries(limit = 10) {
        return this.searchLogs
            .filter(log => !log.hasResults)
            .reduce((acc, log) => {
                const existing = acc.find(item => item.query === log.query);
                if (existing) {
                    existing.count++;
                } else {
                    acc.push({ query: log.query, count: 1 });
                }
                return acc;
            }, [])
            .sort((a, b) => b.count - a.count)
            .slice(0, limit);
    }

    getPerformanceReport() {
        const allTimes = this.searchLogs.map(log => log.responseTime);
        const avgResponseTime = allTimes.reduce((a, b) => a + b, 0) / allTimes.length;

        return {
            totalSearches: this.searchLogs.length,
            avgResponseTime: avgResponseTime.toFixed(2),
            slowQueries: this.getSlowQueries(),
            popularPatterns: this.getPopularPatterns(),
            successRate: this.getSuccessRate()
        };
    }

    getSlowQueries(threshold = 1000) {
        return Array.from(this.performance.entries())
            .filter(([query, stats]) => stats.avgTime > threshold)
            .sort((a, b) => b[1].avgTime - a[1].avgTime)
            .slice(0, 10)
            .map(([query, stats]) => ({
                query,
                avgTime: stats.avgTime.toFixed(2),
                count: stats.count
            }));
    }

    getPopularPatterns() {
        return Array.from(this.patterns.entries())
            .sort((a, b) => b[1].count - a[1].count)
            .slice(0, 5)
            .map(([pattern, data]) => ({
                pattern,
                count: data.count,
                examples: Array.from(data.queries).slice(0, 3)
            }));
    }

    getSuccessRate() {
        const successful = this.searchLogs.filter(log => log.hasResults).length;
        return (successful / this.searchLogs.length * 100).toFixed(2);
    }
}
```

## 最佳实践

### 1. 性能优化
```javascript
// ✅ 推荐：使用防抖优化搜索
function debounce(func, delay) {
    let timeoutId;
    return function (...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
}

const debouncedSearch = debounce((query) => {
    const results = fuzzyLookup(query, data, extractor);
    displayResults(results);
}, 300);

// ✅ 推荐：缓存搜索结果
class SearchCache {
    constructor(maxSize = 100) {
        this.cache = new Map();
        this.maxSize = maxSize;
    }

    get(key) {
        const item = this.cache.get(key);
        if (item) {
            // 更新访问时间
            this.cache.delete(key);
            this.cache.set(key, item);
            return item;
        }
        return null;
    }

    set(key, value) {
        if (this.cache.size >= this.maxSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        this.cache.set(key, value);
    }
}
```

### 2. 错误处理
```javascript
// ✅ 推荐：安全的搜索处理
function safeSearch(query, data, extractor) {
    try {
        if (!query || typeof query !== 'string') {
            return [];
        }

        if (!Array.isArray(data)) {
            console.warn('搜索数据必须是数组');
            return [];
        }

        return fuzzyLookup(query.trim(), data, extractor);
    } catch (error) {
        console.error('搜索失败:', error);
        return [];
    }
}
```

### 3. 内存管理
```javascript
// ✅ 推荐：正确的内存管理
class ManagedSearch {
    constructor() {
        this.cache = new WeakMap(); // 使用WeakMap避免内存泄漏
        this.abortController = new AbortController();
    }

    async search(query, data) {
        // 检查是否已取消
        if (this.abortController.signal.aborted) {
            return [];
        }

        return fuzzyLookup(query, data, (item) => item.text);
    }

    cancel() {
        this.abortController.abort();
    }

    destroy() {
        this.cancel();
        this.cache = null;
    }
}
```

## 总结

Odoo 搜索工具模块提供了强大的模糊搜索功能：

**核心优势**:
- **智能评分**: 基于连续性和位置的智能评分算法
- **多字符串支持**: 可以同时搜索多个字段
- **重音符号处理**: 自动处理重音符号和特殊字符
- **高性能**: 优化的匹配算法，适合大量数据
- **易于集成**: 简单的API，易于集成到各种应用中

**适用场景**:
- 命令面板搜索
- 自动完成功能
- 产品搜索系统
- 文档搜索
- 用户搜索

**设计优势**:
- 简洁的API设计
- 灵活的数据提取
- 智能的排序算法
- 完整的Unicode支持

这个搜索工具为 Odoo Web 客户端提供了强大的搜索能力，是构建现代搜索体验的重要基础。

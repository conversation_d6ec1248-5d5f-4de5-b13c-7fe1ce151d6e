# Odoo 文件工具 (Files Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/files.js`  
**原始路径**: `/web/static/src/core/utils/files.js`  
**模块类型**: 核心工具模块 - 文件处理工具  
**代码行数**: 62 行  
**依赖关系**: 
- `@web/core/utils/numbers` - 数字工具 (humanNumber)
- `@web/core/utils/hooks` - Hook工具 (useService)
- `@web/session` - 会话管理 (session)
- `@web/core/l10n/translation` - 国际化 (_t)

## 模块功能

文件工具模块是 Odoo Web 客户端的核心工具库，提供了文件处理的基础功能，包括：
- 文件大小检查和验证
- 文件上传Hook
- 文件大小限制管理
- 错误处理和用户通知

虽然代码量不大，但这些功能是文件上传和管理的重要基础设施。

## 核心常量

### DEFAULT_MAX_FILE_SIZE - 默认最大文件大小
```javascript
const DEFAULT_MAX_FILE_SIZE = 128 * 1024 * 1024; // 128MB
```

**用途**: 定义默认的最大文件上传大小限制（128MB）

## 核心函数详解

### 1. checkFileSize() - 文件大小检查
```javascript
function checkFileSize(fileSize, notificationService) {
    const maxUploadSize = session.max_file_upload_size || DEFAULT_MAX_FILE_SIZE;
    if (fileSize > maxUploadSize) {
        notificationService.add(
            _t(
                "The selected file (%(size)sB) is larger than the maximum allowed file size (%(maxSize)sB).",
                { size: humanNumber(fileSize), maxSize: humanNumber(maxUploadSize) }
            ),
            {
                type: "danger",
            }
        );
        return false;
    }
    return true;
}
```

**功能特性**:
- **大小验证**: 检查文件是否超过允许的最大大小
- **动态限制**: 使用会话配置的大小限制，回退到默认值
- **用户友好**: 使用人类可读的文件大小格式显示错误
- **国际化**: 支持多语言错误消息
- **通知集成**: 自动显示错误通知给用户

**使用示例**:
```javascript
// 基本文件大小检查
const file = document.getElementById('fileInput').files[0];
const notificationService = useService("notification");

if (checkFileSize(file.size, notificationService)) {
    console.log("文件大小符合要求");
    // 继续处理文件
} else {
    console.log("文件太大，已显示错误通知");
    // 文件被拒绝
}

// 检查多个文件
const files = Array.from(document.getElementById('multiFileInput').files);
const validFiles = files.filter(file => 
    checkFileSize(file.size, notificationService)
);

console.log(`${validFiles.length}/${files.length} 文件通过大小检查`);
```

**实际应用场景**:
```javascript
// 1. 文件上传组件
class FileUploadComponent extends Component {
    setup() {
        this.notification = useService("notification");
        this.state = useState({
            selectedFiles: [],
            validFiles: []
        });
    }
    
    onFileSelect(event) {
        const files = Array.from(event.target.files);
        this.state.selectedFiles = files;
        
        // 验证所有文件大小
        this.state.validFiles = files.filter(file => 
            checkFileSize(file.size, this.notification)
        );
        
        if (this.state.validFiles.length !== files.length) {
            const rejectedCount = files.length - this.state.validFiles.length;
            this.notification.add(
                `${rejectedCount} 个文件因大小超限被拒绝`,
                { type: "warning" }
            );
        }
    }
    
    async uploadFiles() {
        for (const file of this.state.validFiles) {
            await this.uploadSingleFile(file);
        }
    }
}

// 2. 拖拽上传处理
class DragDropUploader {
    constructor(dropZone, notificationService) {
        this.dropZone = dropZone;
        this.notification = notificationService;
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        this.dropZone.addEventListener('drop', (event) => {
            event.preventDefault();
            this.handleFileDrop(event);
        });
        
        this.dropZone.addEventListener('dragover', (event) => {
            event.preventDefault();
        });
    }
    
    handleFileDrop(event) {
        const files = Array.from(event.dataTransfer.files);
        const validFiles = [];
        
        for (const file of files) {
            if (checkFileSize(file.size, this.notification)) {
                validFiles.push(file);
            }
        }
        
        if (validFiles.length > 0) {
            this.processFiles(validFiles);
        }
    }
    
    processFiles(files) {
        // 处理有效的文件
        files.forEach(file => {
            console.log(`处理文件: ${file.name} (${file.size} bytes)`);
        });
    }
}

// 3. 批量文件处理
class BatchFileProcessor {
    constructor() {
        this.notification = useService("notification");
        this.maxConcurrentUploads = 3;
    }
    
    async processBatch(files) {
        // 首先检查所有文件大小
        const validFiles = files.filter(file => 
            checkFileSize(file.size, this.notification)
        );
        
        if (validFiles.length === 0) {
            this.notification.add("没有有效的文件可以处理", { type: "info" });
            return [];
        }
        
        // 分批处理文件
        const results = [];
        for (let i = 0; i < validFiles.length; i += this.maxConcurrentUploads) {
            const batch = validFiles.slice(i, i + this.maxConcurrentUploads);
            const batchResults = await Promise.all(
                batch.map(file => this.processFile(file))
            );
            results.push(...batchResults);
        }
        
        return results;
    }
    
    async processFile(file) {
        // 处理单个文件
        return {
            name: file.name,
            size: file.size,
            processed: true
        };
    }
}

// 4. 文件类型和大小验证器
class FileValidator {
    constructor(notificationService) {
        this.notification = notificationService;
        this.allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
        this.maxFileSize = 10 * 1024 * 1024; // 10MB
    }
    
    validateFile(file) {
        const errors = [];
        
        // 检查文件类型
        if (!this.allowedTypes.includes(file.type)) {
            errors.push(`不支持的文件类型: ${file.type}`);
        }
        
        // 检查文件大小（使用自定义限制）
        if (file.size > this.maxFileSize) {
            const maxSizeHuman = humanNumber(this.maxFileSize);
            const fileSizeHuman = humanNumber(file.size);
            errors.push(`文件大小 ${fileSizeHuman}B 超过限制 ${maxSizeHuman}B`);
        }
        
        // 也可以使用系统的文件大小检查
        const systemSizeCheck = checkFileSize(file.size, this.notification);
        
        if (errors.length > 0) {
            errors.forEach(error => {
                this.notification.add(error, { type: "danger" });
            });
            return false;
        }
        
        return systemSizeCheck;
    }
    
    validateFiles(files) {
        return files.filter(file => this.validateFile(file));
    }
}
```

### 2. useFileUploader() - 文件上传Hook
```javascript
function useFileUploader() {
    const http = useService("http");
    const notification = useService("notification");
    
    return async (route, params) => {
        if ((params.ufile && params.ufile.length) || params.file) {
            const fileSize = (params.ufile && params.ufile[0].size) || params.file.size;
            if (!checkFileSize(fileSize, notification)) {
                return null;
            }
        }
        const fileData = await http.post(route, params, "text");
        const parsedFileData = JSON.parse(fileData);
        if (parsedFileData.error) {
            throw new Error(parsedFileData.error);
        }
        return parsedFileData;
    };
}
```

**功能特性**:
- **Hook集成**: 与OWL组件生命周期集成
- **自动验证**: 自动检查文件大小
- **服务注入**: 自动注入HTTP和通知服务
- **错误处理**: 统一的错误处理机制
- **灵活参数**: 支持单文件和多文件上传

**参数格式**:
- **route**: 上传的API端点
- **params**: 包含文件和其他参数的对象
  - `params.file`: 单个文件对象
  - `params.ufile`: 文件数组
  - 其他自定义参数

**使用示例**:
```javascript
// 在组件中使用文件上传Hook
class FileUploadComponent extends Component {
    setup() {
        this.uploadFile = useFileUploader();
        this.state = useState({
            uploading: false,
            progress: 0
        });
    }
    
    async handleFileUpload(file) {
        this.state.uploading = true;
        
        try {
            const result = await this.uploadFile('/web/binary/upload', {
                file: file,
                model: 'ir.attachment',
                field: 'datas'
            });
            
            console.log('上传成功:', result);
            this.onUploadSuccess(result);
        } catch (error) {
            console.error('上传失败:', error);
            this.onUploadError(error);
        } finally {
            this.state.uploading = false;
        }
    }
    
    async handleMultipleFileUpload(files) {
        this.state.uploading = true;
        
        try {
            const result = await this.uploadFile('/web/binary/upload_multiple', {
                ufile: files,
                model: 'ir.attachment'
            });
            
            console.log('批量上传成功:', result);
        } catch (error) {
            console.error('批量上传失败:', error);
        } finally {
            this.state.uploading = false;
        }
    }
    
    onUploadSuccess(result) {
        // 处理上传成功
        this.notification.add('文件上传成功', { type: 'success' });
    }
    
    onUploadError(error) {
        // 处理上传错误
        this.notification.add(`上传失败: ${error.message}`, { type: 'danger' });
    }
}
```

**实际应用场景**:
```javascript
// 1. 头像上传组件
class AvatarUploadComponent extends Component {
    setup() {
        this.uploadFile = useFileUploader();
        this.state = useState({
            avatarUrl: this.props.currentAvatar,
            uploading: false
        });
    }
    
    async uploadAvatar(file) {
        // 验证文件类型
        if (!file.type.startsWith('image/')) {
            this.notification.add('请选择图片文件', { type: 'warning' });
            return;
        }
        
        this.state.uploading = true;
        
        try {
            const result = await this.uploadFile('/web/binary/upload_avatar', {
                file: file,
                user_id: this.props.userId
            });
            
            this.state.avatarUrl = result.avatar_url;
            this.notification.add('头像更新成功', { type: 'success' });
        } catch (error) {
            this.notification.add('头像上传失败', { type: 'danger' });
        } finally {
            this.state.uploading = false;
        }
    }
}

// 2. 文档上传管理器
class DocumentUploadManager extends Component {
    setup() {
        this.uploadFile = useFileUploader();
        this.state = useState({
            documents: [],
            uploadQueue: []
        });
    }
    
    async uploadDocument(file, metadata = {}) {
        const uploadTask = {
            id: Date.now(),
            file: file,
            status: 'pending',
            progress: 0,
            metadata: metadata
        };
        
        this.state.uploadQueue.push(uploadTask);
        
        try {
            uploadTask.status = 'uploading';
            
            const result = await this.uploadFile('/web/document/upload', {
                file: file,
                title: metadata.title || file.name,
                description: metadata.description || '',
                category: metadata.category || 'general'
            });
            
            uploadTask.status = 'completed';
            uploadTask.result = result;
            
            this.state.documents.push({
                id: result.id,
                name: result.name,
                size: file.size,
                uploadedAt: new Date()
            });
            
        } catch (error) {
            uploadTask.status = 'failed';
            uploadTask.error = error.message;
        }
    }
    
    async uploadMultipleDocuments(files, metadata = {}) {
        const uploadPromises = files.map(file => 
            this.uploadDocument(file, metadata)
        );
        
        await Promise.allSettled(uploadPromises);
        
        const completed = this.state.uploadQueue.filter(task => 
            task.status === 'completed'
        ).length;
        
        this.notification.add(
            `${completed}/${files.length} 个文件上传完成`,
            { type: 'info' }
        );
    }
}

// 3. 进度跟踪的文件上传
class ProgressiveFileUploader extends Component {
    setup() {
        this.uploadFile = useFileUploader();
        this.http = useService("http");
        this.state = useState({
            uploads: new Map()
        });
    }
    
    async uploadWithProgress(file, onProgress) {
        const uploadId = Date.now();
        
        this.state.uploads.set(uploadId, {
            file: file,
            progress: 0,
            status: 'uploading'
        });
        
        try {
            // 使用自定义HTTP请求以支持进度跟踪
            const formData = new FormData();
            formData.append('file', file);
            
            const result = await this.http.post('/web/binary/upload_with_progress', formData, {
                onUploadProgress: (progressEvent) => {
                    const progress = Math.round(
                        (progressEvent.loaded * 100) / progressEvent.total
                    );
                    
                    const upload = this.state.uploads.get(uploadId);
                    upload.progress = progress;
                    
                    if (onProgress) {
                        onProgress(progress);
                    }
                }
            });
            
            const upload = this.state.uploads.get(uploadId);
            upload.status = 'completed';
            upload.result = result;
            
            return result;
            
        } catch (error) {
            const upload = this.state.uploads.get(uploadId);
            upload.status = 'failed';
            upload.error = error.message;
            throw error;
        }
    }
}

// 4. 文件上传队列管理器
class FileUploadQueue extends Component {
    setup() {
        this.uploadFile = useFileUploader();
        this.state = useState({
            queue: [],
            processing: false,
            maxConcurrent: 3
        });
    }
    
    addToQueue(files, options = {}) {
        const tasks = files.map(file => ({
            id: Date.now() + Math.random(),
            file: file,
            options: options,
            status: 'queued',
            retries: 0,
            maxRetries: 3
        }));
        
        this.state.queue.push(...tasks);
        
        if (!this.state.processing) {
            this.processQueue();
        }
    }
    
    async processQueue() {
        this.state.processing = true;
        
        while (this.state.queue.length > 0) {
            const activeTasks = this.state.queue
                .filter(task => task.status === 'uploading')
                .length;
                
            if (activeTasks >= this.state.maxConcurrent) {
                await new Promise(resolve => setTimeout(resolve, 100));
                continue;
            }
            
            const nextTask = this.state.queue.find(task => 
                task.status === 'queued'
            );
            
            if (!nextTask) {
                await new Promise(resolve => setTimeout(resolve, 100));
                continue;
            }
            
            this.processTask(nextTask);
        }
        
        this.state.processing = false;
    }
    
    async processTask(task) {
        task.status = 'uploading';
        
        try {
            const result = await this.uploadFile(
                task.options.route || '/web/binary/upload',
                {
                    file: task.file,
                    ...task.options.params
                }
            );
            
            task.status = 'completed';
            task.result = result;
            
            // 从队列中移除完成的任务
            const index = this.state.queue.indexOf(task);
            this.state.queue.splice(index, 1);
            
        } catch (error) {
            task.retries++;
            
            if (task.retries >= task.maxRetries) {
                task.status = 'failed';
                task.error = error.message;
                
                // 从队列中移除失败的任务
                const index = this.state.queue.indexOf(task);
                this.state.queue.splice(index, 1);
            } else {
                task.status = 'queued'; // 重新排队
            }
        }
    }
}
```

## 最佳实践

### 1. 错误处理和用户反馈
```javascript
// ✅ 推荐：完整的错误处理
class RobustFileUploader {
    constructor() {
        this.uploadFile = useFileUploader();
        this.notification = useService("notification");
    }

    async uploadWithErrorHandling(file, route, params = {}) {
        try {
            // 预检查
            if (!this.preValidateFile(file)) {
                return null;
            }

            // 显示上传开始通知
            this.notification.add(
                `开始上传 ${file.name}`,
                { type: "info" }
            );

            const result = await this.uploadFile(route, { file, ...params });

            // 成功通知
            this.notification.add(
                `${file.name} 上传成功`,
                { type: "success" }
            );

            return result;

        } catch (error) {
            this.handleUploadError(error, file);
            throw error;
        }
    }

    preValidateFile(file) {
        // 文件类型检查
        const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
        if (!allowedTypes.includes(file.type)) {
            this.notification.add(
                `不支持的文件类型: ${file.type}`,
                { type: "warning" }
            );
            return false;
        }

        return true;
    }

    handleUploadError(error, file) {
        let message = `${file.name} 上传失败`;

        if (error.message.includes('size')) {
            message += ': 文件太大';
        } else if (error.message.includes('network')) {
            message += ': 网络错误';
        } else if (error.message.includes('timeout')) {
            message += ': 上传超时';
        } else {
            message += `: ${error.message}`;
        }

        this.notification.add(message, { type: "danger" });
    }
}
```

### 2. 性能优化
```javascript
// ✅ 推荐：批量上传优化
class OptimizedBatchUploader {
    constructor() {
        this.uploadFile = useFileUploader();
        this.maxConcurrent = 3;
        this.retryDelay = 1000;
    }

    async uploadBatch(files, route, params = {}) {
        const results = [];
        const semaphore = new Semaphore(this.maxConcurrent);

        const uploadPromises = files.map(async (file) => {
            await semaphore.acquire();

            try {
                const result = await this.uploadWithRetry(file, route, params);
                results.push({ file, result, success: true });
            } catch (error) {
                results.push({ file, error, success: false });
            } finally {
                semaphore.release();
            }
        });

        await Promise.allSettled(uploadPromises);
        return results;
    }

    async uploadWithRetry(file, route, params, maxRetries = 3) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await this.uploadFile(route, { file, ...params });
            } catch (error) {
                if (attempt === maxRetries) {
                    throw error;
                }

                // 指数退避
                const delay = this.retryDelay * Math.pow(2, attempt - 1);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }
}
```

### 3. 安全性考虑
```javascript
// ✅ 推荐：安全的文件上传
class SecureFileUploader {
    constructor() {
        this.uploadFile = useFileUploader();
        this.notification = useService("notification");

        // 安全配置
        this.allowedTypes = new Set([
            'image/jpeg', 'image/png', 'image/gif',
            'application/pdf', 'text/plain'
        ]);

        this.maxFileSize = 10 * 1024 * 1024; // 10MB
        this.dangerousExtensions = new Set([
            '.exe', '.bat', '.cmd', '.scr', '.pif', '.com'
        ]);
    }

    async secureUpload(file, route, params = {}) {
        // 安全检查
        if (!this.validateFileSecurity(file)) {
            return null;
        }

        // 添加安全头
        const secureParams = {
            ...params,
            file,
            checksum: await this.calculateChecksum(file),
            timestamp: Date.now()
        };

        return this.uploadFile(route, secureParams);
    }

    validateFileSecurity(file) {
        // 检查文件类型
        if (!this.allowedTypes.has(file.type)) {
            this.notification.add(
                `不允许的文件类型: ${file.type}`,
                { type: "danger" }
            );
            return false;
        }

        // 检查文件扩展名
        const extension = '.' + file.name.split('.').pop().toLowerCase();
        if (this.dangerousExtensions.has(extension)) {
            this.notification.add(
                `危险的文件扩展名: ${extension}`,
                { type: "danger" }
            );
            return false;
        }

        return true;
    }

    async calculateChecksum(file) {
        const buffer = await file.arrayBuffer();
        const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    }
}
```

## 总结

Odoo 文件工具模块虽然简洁，但提供了文件处理的核心功能：

**核心优势**:
- **大小验证**: 自动检查文件大小限制，防止无效上传
- **用户友好**: 人类可读的错误消息和通知
- **Hook集成**: 与OWL组件生命周期完美集成
- **错误处理**: 统一的错误处理和用户反馈机制
- **国际化**: 完整的多语言支持

**适用场景**:
- 文件上传组件开发
- 批量文件处理
- 文档管理系统
- 图片和媒体上传
- 安全文件验证

这个工具模块为 Odoo Web 客户端提供了可靠的文件处理基础，是构建文件上传和管理功能的重要工具。

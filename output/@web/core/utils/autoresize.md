# Odoo 自动调整大小工具 (Autoresize Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/autoresize.js`  
**原始路径**: `/web/static/src/core/utils/autoresize.js`  
**模块类型**: 核心工具模块 - 自动调整大小工具  
**代码行数**: 108 行  
**依赖关系**: 
- `@odoo/owl` - OWL框架 (useEffect)
- `@web/core/browser/browser` - 浏览器工具

## 模块功能

自动调整大小工具模块是 Odoo Web 客户端的核心工具库，提供了智能的输入框和文本域自动调整大小功能。该模块支持：
- 输入框宽度自动调整
- 文本域高度自动调整
- 内容变化响应
- 浏览器兼容性处理
- 性能优化机制

这些功能是构建现代响应式表单的重要基础，广泛应用于聊天输入、评论框、搜索框等场景。

## 自动调整原理

### 调整机制
自动调整基于以下核心原理：
1. **内容测量**: 通过 `scrollWidth` 和 `scrollHeight` 测量内容尺寸
2. **动态调整**: 根据内容尺寸动态调整元素尺寸
3. **边界控制**: 设置最小/最大尺寸限制
4. **样式计算**: 考虑边框、内边距等样式因素

### 触发条件
- **输入事件**: 用户输入内容时触发
- **尺寸变化**: 容器尺寸变化时触发
- **程序调用**: 程序主动调用时触发

### 性能优化
- **ResizeObserver**: 使用现代API监听尺寸变化
- **重复检测**: 避免重复调整造成的性能问题
- **样式缓存**: 缓存计算样式减少重排

## 核心函数详解

### 1. useAutoresize() - 自动调整Hook
```javascript
function useAutoresize(ref, options = {}) {
    let wasProgrammaticallyResized = false;
    let resize = null;
    
    useEffect((el) => {
        if (el) {
            resize = (programmaticResize = false) => {
                wasProgrammaticallyResized = programmaticResize;
                if (el instanceof HTMLInputElement) {
                    resizeInput(el, options);
                } else {
                    resizeTextArea(el, options);
                }
                options.onResize?.(el, options);
            };
            
            el.addEventListener("input", () => resize(true));
            const resizeObserver = new ResizeObserver(() => {
                if (wasProgrammaticallyResized) {
                    wasProgrammaticallyResized = false;
                    return;
                }
                resize();
            });
            resizeObserver.observe(el);
            
            return () => {
                el.removeEventListener("input", resize);
                resizeObserver.unobserve(el);
                resizeObserver.disconnect();
                resize = null;
            };
        }
    }, () => [ref.el]);
    
    useEffect(() => {
        if (resize) {
            resize(true);
        }
    });
}
```

**功能特性**:
- **智能检测**: 自动检测输入框或文本域类型
- **事件监听**: 监听输入事件和尺寸变化
- **重复避免**: 防止重复调整造成的性能问题
- **回调支持**: 支持调整完成后的回调函数
- **自动清理**: 组件卸载时自动清理资源

**使用示例**:
```javascript
// 基本使用
class AutoResizeInput extends Component {
    static template = xml`
        <input t-ref="input" 
               type="text" 
               placeholder="输入内容，宽度自动调整..."
               class="auto-resize-input" />
    `;
    
    setup() {
        this.inputRef = useRef("input");
        
        useAutoresize(this.inputRef, {
            onResize: (element, options) => {
                console.log('输入框已调整:', element.style.width);
            }
        });
    }
}

// 文本域自动调整
class AutoResizeTextarea extends Component {
    static template = xml`
        <div class="textarea-container">
            <textarea t-ref="textarea" 
                      placeholder="输入内容，高度自动调整..."
                      class="auto-resize-textarea"></textarea>
        </div>
    `;
    
    setup() {
        this.textareaRef = useRef("textarea");
        
        useAutoresize(this.textareaRef, {
            minimumHeight: 40,
            onResize: (element, options) => {
                console.log('文本域已调整:', element.style.height);
            }
        });
    }
}
```

**实际应用场景**:
```javascript
// 1. 聊天输入框
class ChatInput extends Component {
    static template = xml`
        <div class="chat-input-container">
            <textarea t-ref="chatInput" 
                      t-model="message"
                      placeholder="输入消息..."
                      class="chat-input"
                      t-on-keydown="onKeyDown"></textarea>
            <button t-on-click="sendMessage" 
                    t-att-disabled="!message.trim()"
                    class="send-button">发送</button>
        </div>
    `;
    
    setup() {
        this.chatInputRef = useRef("chatInput");
        this.message = useState("");
        
        useAutoresize(this.chatInputRef, {
            minimumHeight: 36,
            onResize: (element) => {
                // 调整发送按钮位置
                this.adjustSendButtonPosition(element);
            }
        });
    }
    
    onKeyDown(event) {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            this.sendMessage();
        }
    }
    
    sendMessage() {
        if (this.message.trim()) {
            this.props.onSendMessage(this.message.trim());
            this.message = "";
            
            // 重置文本域高度
            this.$nextTick(() => {
                this.chatInputRef.el.style.height = 'auto';
            });
        }
    }
    
    adjustSendButtonPosition(textarea) {
        const sendButton = this.el.querySelector('.send-button');
        const textareaHeight = parseInt(textarea.style.height);
        
        if (textareaHeight > 36) {
            sendButton.style.alignSelf = 'flex-end';
            sendButton.style.marginBottom = '8px';
        } else {
            sendButton.style.alignSelf = 'center';
            sendButton.style.marginBottom = '0';
        }
    }
}

// 2. 评论编辑器
class CommentEditor extends Component {
    static template = xml`
        <div class="comment-editor">
            <div class="editor-header">
                <span class="editor-title">添加评论</span>
                <span class="character-count" t-esc="characterCount"></span>
            </div>
            <div class="editor-body">
                <textarea t-ref="commentTextarea"
                          t-model="comment"
                          placeholder="写下你的评论..."
                          class="comment-textarea"
                          t-att-maxlength="maxLength"></textarea>
            </div>
            <div class="editor-footer">
                <button t-on-click="cancel" class="btn btn-secondary">取消</button>
                <button t-on-click="submit" 
                        t-att-disabled="!canSubmit"
                        class="btn btn-primary">提交评论</button>
            </div>
        </div>
    `;
    
    setup() {
        this.textareaRef = useRef("commentTextarea");
        this.comment = useState("");
        this.maxLength = 1000;
        
        useAutoresize(this.textareaRef, {
            minimumHeight: 80,
            onResize: (element) => {
                this.updateEditorLayout(element);
            }
        });
    }
    
    get characterCount() {
        return `${this.comment.length}/${this.maxLength}`;
    }
    
    get canSubmit() {
        return this.comment.trim().length > 0 && 
               this.comment.length <= this.maxLength;
    }
    
    updateEditorLayout(textarea) {
        const editorBody = this.el.querySelector('.editor-body');
        const textareaHeight = parseInt(textarea.style.height);
        
        // 限制最大高度
        if (textareaHeight > 200) {
            textarea.style.height = '200px';
            textarea.style.overflowY = 'auto';
        } else {
            textarea.style.overflowY = 'hidden';
        }
    }
    
    submit() {
        if (this.canSubmit) {
            this.props.onSubmit(this.comment.trim());
            this.comment = "";
        }
    }
    
    cancel() {
        this.comment = "";
        this.props.onCancel?.();
    }
}

// 3. 搜索建议输入框
class SearchInput extends Component {
    static template = xml`
        <div class="search-container">
            <input t-ref="searchInput"
                   t-model="searchQuery"
                   type="text"
                   placeholder="搜索..."
                   class="search-input"
                   t-on-input="onInput"
                   t-on-focus="onFocus"
                   t-on-blur="onBlur" />
            <div t-if="showSuggestions" class="search-suggestions">
                <div t-foreach="suggestions" t-as="suggestion" t-key="suggestion.id"
                     class="suggestion-item"
                     t-on-click="() => this.selectSuggestion(suggestion)">
                    <span t-esc="suggestion.text"></span>
                </div>
            </div>
        </div>
    `;
    
    setup() {
        this.searchInputRef = useRef("searchInput");
        this.searchQuery = useState("");
        this.suggestions = useState([]);
        this.showSuggestions = useState(false);
        
        useAutoresize(this.searchInputRef, {
            onResize: (element) => {
                this.adjustSuggestionsWidth(element);
            }
        });
        
        this.debouncedSearch = this.debounce(this.performSearch.bind(this), 300);
    }
    
    onInput() {
        this.debouncedSearch(this.searchQuery);
    }
    
    onFocus() {
        if (this.suggestions.length > 0) {
            this.showSuggestions = true;
        }
    }
    
    onBlur() {
        // 延迟隐藏，允许点击建议项
        setTimeout(() => {
            this.showSuggestions = false;
        }, 150);
    }
    
    async performSearch(query) {
        if (query.trim().length < 2) {
            this.suggestions = [];
            this.showSuggestions = false;
            return;
        }
        
        try {
            const results = await this.props.searchFunction(query);
            this.suggestions = results.slice(0, 8);
            this.showSuggestions = this.suggestions.length > 0;
        } catch (error) {
            console.error('搜索失败:', error);
            this.suggestions = [];
            this.showSuggestions = false;
        }
    }
    
    selectSuggestion(suggestion) {
        this.searchQuery = suggestion.text;
        this.showSuggestions = false;
        this.props.onSelect?.(suggestion);
    }
    
    adjustSuggestionsWidth(input) {
        const suggestions = this.el.querySelector('.search-suggestions');
        if (suggestions) {
            suggestions.style.width = input.style.width;
        }
    }
    
    debounce(func, delay) {
        let timeoutId;
        return function (...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    }
}
```

### 2. resizeInput() - 输入框宽度调整
```javascript
function resizeInput(input) {
    // 测量输入框的最大宽度
    input.style.width = "100%";
    const maxWidth = input.clientWidth;

    // Safari 16 兼容性处理
    const isSafari16 = /Version\/16.+Safari/i.test(browser.navigator.userAgent);

    // 设置最小宽度
    input.style.width = "10px";

    // 处理空值和占位符
    if (input.value === "" && input.placeholder !== "") {
        input.style.width = "auto";
        return;
    }

    // 计算合适的宽度
    const requiredWidth = input.scrollWidth + 5 + (isSafari16 ? 8 : 0);
    if (requiredWidth > maxWidth) {
        input.style.width = "100%";
    } else {
        input.style.width = requiredWidth + "px";
    }
}
```

**功能特性**:
- **动态宽度**: 根据内容长度动态调整宽度
- **最大宽度限制**: 不超过容器的最大宽度
- **浏览器兼容**: 特殊处理Safari 16的尺寸计算问题
- **占位符处理**: 空值时考虑占位符的显示
- **边距补偿**: 添加适当的边距避免内容截断

### 3. resizeTextArea() - 文本域高度调整
```javascript
function resizeTextArea(textarea, options = {}) {
    const minimumHeight = options.minimumHeight || 0;
    let heightOffset = 0;

    // 获取计算样式
    const style = window.getComputedStyle(textarea);

    // 处理box-sizing
    if (style.boxSizing === "border-box") {
        const paddingHeight = parseFloat(style.paddingTop) + parseFloat(style.paddingBottom);
        const borderHeight = parseFloat(style.borderTopWidth) + parseFloat(style.borderBottomWidth);
        heightOffset = borderHeight + paddingHeight;
    }

    // 保存原始样式
    const previousStyle = {
        borderTopWidth: style.borderTopWidth,
        borderBottomWidth: style.borderBottomWidth,
        padding: style.padding,
    };

    // 临时移除影响高度的样式
    Object.assign(textarea.style, {
        height: "auto",
        borderTopWidth: 0,
        borderBottomWidth: 0,
        paddingTop: 0,
        paddingRight: style.paddingRight,
        paddingBottom: 0,
        paddingLeft: style.paddingLeft,
    });

    // 计算所需高度
    const height = Math.max(minimumHeight, textarea.scrollHeight + heightOffset);

    // 恢复样式并设置新高度
    Object.assign(textarea.style, previousStyle, { height: `${height}px` });
    textarea.parentElement.style.height = `${height}px`;
}
```

**功能特性**:
- **精确测量**: 准确测量内容所需的高度
- **样式考虑**: 正确处理边框、内边距等样式
- **最小高度**: 支持设置最小高度限制
- **父元素同步**: 同时调整父元素高度
- **box-sizing兼容**: 正确处理不同的盒模型

## 最佳实践

### 1. 性能优化
```javascript
// ✅ 推荐：使用防抖优化频繁调整
class OptimizedAutoresize {
    constructor(element, options = {}) {
        this.element = element;
        this.options = options;
        this.resizeTimeout = null;
        this.isResizing = false;

        this.debouncedResize = this.debounce(this.performResize.bind(this), 16);
        this.setupListeners();
    }

    setupListeners() {
        this.element.addEventListener('input', () => {
            this.debouncedResize();
        });
    }

    performResize() {
        if (this.isResizing) return;

        this.isResizing = true;
        requestAnimationFrame(() => {
            if (this.element instanceof HTMLInputElement) {
                resizeInput(this.element);
            } else {
                resizeTextArea(this.element, this.options);
            }
            this.isResizing = false;
        });
    }

    debounce(func, delay) {
        return (...args) => {
            clearTimeout(this.resizeTimeout);
            this.resizeTimeout = setTimeout(() => func.apply(this, args), delay);
        };
    }
}
```

### 2. 错误处理
```javascript
// ✅ 推荐：安全的自动调整
function safeAutoresize(ref, options = {}) {
    try {
        if (!ref || !ref.el) {
            console.warn('自动调整: 无效的元素引用');
            return;
        }

        const element = ref.el;
        if (!(element instanceof HTMLInputElement) &&
            !(element instanceof HTMLTextAreaElement)) {
            console.warn('自动调整: 元素必须是input或textarea');
            return;
        }

        return useAutoresize(ref, options);
    } catch (error) {
        console.error('自动调整失败:', error);
    }
}
```

### 3. 内存管理
```javascript
// ✅ 推荐：正确的资源清理
class ManagedAutoresize {
    constructor() {
        this.activeResizers = new Set();
        this.observers = new WeakMap();
    }

    addAutoresize(ref, options = {}) {
        const cleanup = useAutoresize(ref, options);
        this.activeResizers.add(cleanup);

        return () => {
            cleanup();
            this.activeResizers.delete(cleanup);
        };
    }

    destroy() {
        this.activeResizers.forEach(cleanup => {
            try {
                cleanup();
            } catch (error) {
                console.error('清理自动调整失败:', error);
            }
        });
        this.activeResizers.clear();
    }
}
```

## 总结

Odoo 自动调整大小工具模块提供了智能的输入框和文本域自动调整功能：

**核心优势**:
- **智能检测**: 自动检测元素类型并应用相应的调整策略
- **性能优化**: 使用ResizeObserver和防重复机制优化性能
- **浏览器兼容**: 处理不同浏览器的兼容性问题
- **样式精确**: 正确处理边框、内边距等样式因素
- **OWL集成**: 与OWL框架完美集成的Hook设计

**适用场景**:
- 聊天输入框
- 评论编辑器
- 搜索输入框
- 表单字段
- 代码编辑器

**设计优势**:
- Hook模式设计
- 自动资源清理
- 回调函数支持
- 选项配置灵活

这个自动调整工具为 Odoo Web 客户端提供了现代化的输入体验，是构建响应式表单界面的重要工具。

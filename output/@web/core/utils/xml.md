# Odoo XML工具 (XML Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/xml.js`  
**原始路径**: `/web/static/src/core/utils/xml.js`  
**模块类型**: 核心工具模块 - XML处理工具  
**代码行数**: 166 行  
**依赖关系**: 
- `@web/core/utils/arrays` - 数组工具 (isIterable)

## 模块功能

XML工具模块是 Odoo Web 客户端的核心工具库，提供了完整的XML文档处理功能。该模块支持：
- XML解析和序列化
- XML元素创建和操作
- XML文档遍历
- 属性管理和提取
- 安全的XML处理
- 大小写敏感的XML操作

这些功能是构建动态XML内容和模板处理的重要基础，广泛应用于QWeb模板、配置文件、数据交换等场景。

## XML处理原理

### XML文档模型
模块使用标准的DOM API进行XML处理：
1. **解析器**: 使用DOMParser解析XML字符串
2. **序列化器**: 使用XMLSerializer将XML转换为字符串
3. **文档对象**: 创建专用的XML文档用于元素创建
4. **大小写敏感**: 使用"text/xml"确保大小写敏感

### 错误处理机制
- **解析错误检测**: 检查parsererror元素
- **异常抛出**: 解析失败时抛出详细错误信息
- **安全处理**: 防止恶意XML注入
- **验证机制**: 确保XML格式的正确性

### 遍历模式
- **深度优先**: 使用深度优先算法遍历XML树
- **回调控制**: 通过回调函数控制遍历行为
- **子节点访问**: 提供visitChildren函数控制子节点遍历
- **条件遍历**: 支持条件性跳过子节点

## 核心函数详解

### 1. parseXML() - XML解析
```javascript
function parseXML(str) {
    const xml = parser.parseFromString(str, "text/xml");
    if (hasParsingError(xml)) {
        throw new Error(
            `An error occured while parsing ${str}: ${xml.getElementsByTagName("parsererror")}`
        );
    }
    return xml.documentElement;
}
```

**功能特性**:
- **字符串解析**: 将XML字符串解析为DOM元素
- **错误检测**: 自动检测和报告解析错误
- **异常处理**: 解析失败时抛出详细错误信息
- **根元素返回**: 返回文档的根元素
- **大小写敏感**: 保持XML标签和属性的大小写

**使用示例**:
```javascript
// 基本XML解析
const xmlString = `
    <root>
        <item id="1" name="First Item">
            <description>This is the first item</description>
        </item>
        <item id="2" name="Second Item">
            <description>This is the second item</description>
        </item>
    </root>
`;

try {
    const xmlElement = parseXML(xmlString);
    console.log('根元素:', xmlElement.tagName); // "root"
    console.log('子元素数量:', xmlElement.children.length); // 2
} catch (error) {
    console.error('XML解析失败:', error.message);
}

// 解析错误处理
const invalidXml = '<root><item>未闭合标签</root>';
try {
    parseXML(invalidXml);
} catch (error) {
    console.error('解析错误:', error.message);
}
```

**实际应用场景**:
```javascript
// 1. 配置文件解析器
class ConfigParser {
    constructor() {
        this.config = {};
        this.validators = new Map();
    }
    
    addValidator(tagName, validator) {
        this.validators.set(tagName, validator);
        return this;
    }
    
    parseConfig(xmlString) {
        try {
            const configElement = parseXML(xmlString);
            this.config = this.parseElement(configElement);
            return this.config;
        } catch (error) {
            throw new Error(`配置解析失败: ${error.message}`);
        }
    }
    
    parseElement(element) {
        const result = {};
        
        // 解析属性
        for (const attr of element.attributes) {
            result[`@${attr.name}`] = attr.value;
        }
        
        // 解析子元素
        const children = {};
        for (const child of element.children) {
            const tagName = child.tagName;
            const childData = this.parseElement(child);
            
            // 验证元素
            if (this.validators.has(tagName)) {
                const validator = this.validators.get(tagName);
                if (!validator(childData)) {
                    throw new Error(`元素验证失败: ${tagName}`);
                }
            }
            
            if (children[tagName]) {
                if (!Array.isArray(children[tagName])) {
                    children[tagName] = [children[tagName]];
                }
                children[tagName].push(childData);
            } else {
                children[tagName] = childData;
            }
        }
        
        // 解析文本内容
        const textContent = element.textContent.trim();
        if (textContent && Object.keys(children).length === 0) {
            return textContent;
        }
        
        return { ...result, ...children };
    }
}

// 使用示例
const configParser = new ConfigParser();

configParser
    .addValidator('database', (data) => data['@host'] && data['@name'])
    .addValidator('server', (data) => data['@port'] && !isNaN(data['@port']));

const configXml = `
    <config>
        <database host="localhost" name="odoo" user="admin">
            <password>secret</password>
        </database>
        <server port="8069" workers="4">
            <ssl enabled="true" cert="/path/to/cert.pem" />
        </server>
    </config>
`;

const config = configParser.parseConfig(configXml);
console.log('解析的配置:', config);

// 2. 数据转换器
class XMLDataTransformer {
    constructor() {
        this.transformers = new Map();
    }
    
    addTransformer(tagName, transformer) {
        this.transformers.set(tagName, transformer);
        return this;
    }
    
    transform(xmlString) {
        const xmlElement = parseXML(xmlString);
        return this.transformElement(xmlElement);
    }
    
    transformElement(element) {
        const tagName = element.tagName;
        
        if (this.transformers.has(tagName)) {
            const transformer = this.transformers.get(tagName);
            return transformer(element, (child) => this.transformElement(child));
        }
        
        // 默认转换
        const result = {
            tag: tagName,
            attributes: this.getAttributes(element),
            children: Array.from(element.children).map(child => 
                this.transformElement(child)
            )
        };
        
        const textContent = this.getTextContent(element);
        if (textContent) {
            result.text = textContent;
        }
        
        return result;
    }
    
    getAttributes(element) {
        const attrs = {};
        for (const attr of element.attributes) {
            attrs[attr.name] = attr.value;
        }
        return attrs;
    }
    
    getTextContent(element) {
        let text = '';
        for (const node of element.childNodes) {
            if (node.nodeType === Node.TEXT_NODE) {
                text += node.textContent;
            }
        }
        return text.trim();
    }
}

// 使用示例
const transformer = new XMLDataTransformer();

transformer
    .addTransformer('user', (element, transformChild) => ({
        id: element.getAttribute('id'),
        name: element.getAttribute('name'),
        profile: transformChild(element.querySelector('profile')),
        permissions: Array.from(element.querySelectorAll('permission'))
            .map(p => p.textContent)
    }))
    .addTransformer('profile', (element) => ({
        email: element.querySelector('email')?.textContent,
        phone: element.querySelector('phone')?.textContent,
        address: element.querySelector('address')?.textContent
    }));

const userData = `
    <user id="123" name="John Doe">
        <profile>
            <email><EMAIL></email>
            <phone>+1234567890</phone>
            <address>123 Main St</address>
        </profile>
        <permission>read</permission>
        <permission>write</permission>
    </user>
`;

const transformedData = transformer.transform(userData);
console.log('转换后的数据:', transformedData);

// 3. XML模板处理器
class XMLTemplateProcessor {
    constructor() {
        this.variables = {};
        this.functions = new Map();
    }
    
    setVariable(name, value) {
        this.variables[name] = value;
        return this;
    }
    
    setVariables(vars) {
        Object.assign(this.variables, vars);
        return this;
    }
    
    addFunction(name, func) {
        this.functions.set(name, func);
        return this;
    }
    
    process(templateXml) {
        const template = parseXML(templateXml);
        return this.processElement(template);
    }
    
    processElement(element) {
        // 处理条件指令
        if (element.hasAttribute('t-if')) {
            const condition = element.getAttribute('t-if');
            if (!this.evaluateCondition(condition)) {
                return null;
            }
            element.removeAttribute('t-if');
        }
        
        // 处理循环指令
        if (element.hasAttribute('t-foreach')) {
            const foreach = element.getAttribute('t-foreach');
            const as = element.getAttribute('t-as') || 'item';
            element.removeAttribute('t-foreach');
            element.removeAttribute('t-as');
            
            return this.processLoop(element, foreach, as);
        }
        
        // 处理变量替换
        this.processVariables(element);
        
        // 处理子元素
        const processedChildren = [];
        for (const child of Array.from(element.children)) {
            const processed = this.processElement(child);
            if (processed) {
                if (Array.isArray(processed)) {
                    processedChildren.push(...processed);
                } else {
                    processedChildren.push(processed);
                }
            }
        }
        
        // 替换子元素
        element.innerHTML = '';
        processedChildren.forEach(child => {
            if (typeof child === 'string') {
                element.appendChild(createTextNode(child));
            } else {
                element.appendChild(child);
            }
        });
        
        return element;
    }
    
    processLoop(element, foreach, as) {
        const items = this.evaluateExpression(foreach);
        const results = [];
        
        if (Array.isArray(items)) {
            items.forEach((item, index) => {
                const oldVar = this.variables[as];
                const oldIndex = this.variables[`${as}_index`];
                
                this.variables[as] = item;
                this.variables[`${as}_index`] = index;
                
                const cloned = element.cloneNode(true);
                const processed = this.processElement(cloned);
                if (processed) {
                    results.push(processed);
                }
                
                // 恢复变量
                if (oldVar !== undefined) {
                    this.variables[as] = oldVar;
                } else {
                    delete this.variables[as];
                }
                
                if (oldIndex !== undefined) {
                    this.variables[`${as}_index`] = oldIndex;
                } else {
                    delete this.variables[`${as}_index`];
                }
            });
        }
        
        return results;
    }
    
    processVariables(element) {
        // 处理属性中的变量
        for (const attr of Array.from(element.attributes)) {
            const value = attr.value;
            const processed = this.replaceVariables(value);
            if (processed !== value) {
                element.setAttribute(attr.name, processed);
            }
        }
        
        // 处理文本内容中的变量
        for (const node of Array.from(element.childNodes)) {
            if (node.nodeType === Node.TEXT_NODE) {
                const processed = this.replaceVariables(node.textContent);
                if (processed !== node.textContent) {
                    node.textContent = processed;
                }
            }
        }
    }
    
    replaceVariables(text) {
        return text.replace(/\{\{([^}]+)\}\}/g, (match, expression) => {
            try {
                return this.evaluateExpression(expression.trim());
            } catch (error) {
                console.warn(`变量替换失败: ${expression}`, error);
                return match;
            }
        });
    }
    
    evaluateExpression(expression) {
        // 简单的表达式求值（实际应用中可能需要更复杂的解析器）
        try {
            const func = new Function(...Object.keys(this.variables), `return ${expression}`);
            return func(...Object.values(this.variables));
        } catch (error) {
            throw new Error(`表达式求值失败: ${expression}`);
        }
    }
    
    evaluateCondition(condition) {
        try {
            return Boolean(this.evaluateExpression(condition));
        } catch (error) {
            console.warn(`条件求值失败: ${condition}`, error);
            return false;
        }
    }
}

// 使用示例
const templateProcessor = new XMLTemplateProcessor();

templateProcessor
    .setVariables({
        title: 'User List',
        users: [
            { name: 'John', email: '<EMAIL>', active: true },
            { name: 'Jane', email: '<EMAIL>', active: false },
            { name: 'Bob', email: '<EMAIL>', active: true }
        ],
        showInactive: false
    });

const template = `
    <div class="user-list">
        <h1>{{title}}</h1>
        <div t-foreach="users" t-as="user" class="user-item">
            <div t-if="user.active || showInactive" class="user-card">
                <h3>{{user.name}}</h3>
                <p>{{user.email}}</p>
                <span t-if="user.active" class="status active">Active</span>
                <span t-if="!user.active" class="status inactive">Inactive</span>
            </div>
        </div>
    </div>
`;

const processed = templateProcessor.process(template);
console.log('处理后的模板:', serializeXML(processed));
```

### 2. serializeXML() - XML序列化
```javascript
function serializeXML(xml) {
    return serializer.serializeToString(xml);
}
```

**功能特性**:
- **DOM转字符串**: 将XML DOM元素转换为字符串
- **标准序列化**: 使用XMLSerializer确保标准格式
- **完整输出**: 包含所有属性、子元素和文本内容
- **格式保持**: 保持原始的XML格式和结构
- **编码处理**: 正确处理特殊字符和编码

### 3. visitXML() - XML遍历
```javascript
function visitXML(xml, callback) {
    const visit = (el) => {
        if (el) {
            let didVisitChildren = false;
            const visitChildren = () => {
                for (const child of el.children) {
                    visit(child);
                }
                didVisitChildren = true;
            };
            const shouldVisitChildren = callback(el, visitChildren);
            if (shouldVisitChildren !== false && !didVisitChildren) {
                visitChildren();
            }
        }
    };
    const xmlDoc = typeof xml === "string" ? parseXML(xml) : xml;
    visit(xmlDoc);
}
```

**功能特性**:
- **深度遍历**: 深度优先遍历XML树结构
- **回调控制**: 通过回调函数处理每个元素
- **子节点控制**: 可以控制是否访问子节点
- **字符串支持**: 支持直接传入XML字符串
- **灵活控制**: 回调返回false可跳过子节点

**使用示例**:
```javascript
// 基本遍历
const xmlString = `
    <root>
        <section id="1">
            <item>Item 1</item>
            <item>Item 2</item>
        </section>
        <section id="2">
            <item>Item 3</item>
        </section>
    </root>
`;

// 收集所有元素信息
const elements = [];
visitXML(xmlString, (element, visitChildren) => {
    elements.push({
        tagName: element.tagName,
        attributes: Array.from(element.attributes).map(attr => ({
            name: attr.name,
            value: attr.value
        })),
        textContent: element.textContent.trim()
    });

    visitChildren(); // 继续访问子元素
});

console.log('所有元素:', elements);

// 条件遍历 - 跳过特定元素的子节点
visitXML(xmlString, (element, visitChildren) => {
    console.log('访问元素:', element.tagName);

    // 跳过id为"2"的section的子节点
    if (element.tagName === 'section' && element.getAttribute('id') === '2') {
        console.log('跳过section 2的子节点');
        return false; // 不访问子节点
    }

    visitChildren();
});
```

**实际应用场景**:
```javascript
// 1. XML验证器
class XMLValidator {
    constructor() {
        this.rules = new Map();
        this.errors = [];
    }

    addRule(tagName, rule) {
        if (!this.rules.has(tagName)) {
            this.rules.set(tagName, []);
        }
        this.rules.get(tagName).push(rule);
        return this;
    }

    validate(xml) {
        this.errors = [];

        visitXML(xml, (element, visitChildren) => {
            this.validateElement(element);
            visitChildren();
        });

        return {
            isValid: this.errors.length === 0,
            errors: this.errors
        };
    }

    validateElement(element) {
        const tagName = element.tagName;
        const rules = this.rules.get(tagName) || [];

        rules.forEach(rule => {
            try {
                const result = rule(element);
                if (result !== true) {
                    this.errors.push({
                        element: tagName,
                        message: result || '验证失败',
                        path: this.getElementPath(element)
                    });
                }
            } catch (error) {
                this.errors.push({
                    element: tagName,
                    message: `验证规则执行失败: ${error.message}`,
                    path: this.getElementPath(element)
                });
            }
        });
    }

    getElementPath(element) {
        const path = [];
        let current = element;

        while (current && current.tagName) {
            let selector = current.tagName;

            // 添加ID或索引
            if (current.id) {
                selector += `#${current.id}`;
            } else if (current.parentElement) {
                const siblings = Array.from(current.parentElement.children)
                    .filter(child => child.tagName === current.tagName);
                if (siblings.length > 1) {
                    const index = siblings.indexOf(current);
                    selector += `[${index}]`;
                }
            }

            path.unshift(selector);
            current = current.parentElement;
        }

        return path.join(' > ');
    }
}

// 使用示例
const validator = new XMLValidator();

validator
    .addRule('user', (element) => {
        if (!element.hasAttribute('id')) {
            return 'user元素必须有id属性';
        }
        return true;
    })
    .addRule('email', (element) => {
        const email = element.textContent.trim();
        if (!email.includes('@')) {
            return '邮箱格式不正确';
        }
        return true;
    })
    .addRule('age', (element) => {
        const age = parseInt(element.textContent);
        if (isNaN(age) || age < 0 || age > 150) {
            return '年龄必须是0-150之间的数字';
        }
        return true;
    });

const userXml = `
    <users>
        <user id="1">
            <name>John Doe</name>
            <email><EMAIL></email>
            <age>30</age>
        </user>
        <user>
            <name>Jane Smith</name>
            <email>invalid-email</email>
            <age>-5</age>
        </user>
    </users>
`;

const validationResult = validator.validate(userXml);
console.log('验证结果:', validationResult);

// 2. XML转换器
class XMLTransformer {
    constructor() {
        this.transformations = new Map();
    }

    addTransformation(tagName, transformer) {
        this.transformations.set(tagName, transformer);
        return this;
    }

    transform(xml) {
        const result = typeof xml === 'string' ? parseXML(xml) : xml.cloneNode(true);

        visitXML(result, (element, visitChildren) => {
            const tagName = element.tagName;

            if (this.transformations.has(tagName)) {
                const transformer = this.transformations.get(tagName);
                transformer(element);
            }

            visitChildren();
        });

        return result;
    }
}

// 使用示例
const transformer = new XMLTransformer();

transformer
    .addTransformation('deprecated', (element) => {
        // 将deprecated元素重命名为warning
        const warning = createElement('warning',
            { class: 'deprecated-warning' },
            Array.from(element.childNodes)
        );
        element.parentElement.replaceChild(warning, element);
    })
    .addTransformation('link', (element) => {
        // 为链接添加target="_blank"
        if (!element.hasAttribute('target')) {
            element.setAttribute('target', '_blank');
        }
    });

// 3. XML统计分析器
class XMLAnalyzer {
    constructor() {
        this.stats = {
            totalElements: 0,
            elementCounts: {},
            maxDepth: 0,
            attributeCounts: {},
            textLength: 0
        };
    }

    analyze(xml) {
        this.stats = {
            totalElements: 0,
            elementCounts: {},
            maxDepth: 0,
            attributeCounts: {},
            textLength: 0
        };

        this.analyzeElement(xml, 0);
        return this.stats;
    }

    analyzeElement(xml, depth = 0) {
        visitXML(xml, (element, visitChildren) => {
            this.stats.totalElements++;
            this.stats.maxDepth = Math.max(this.stats.maxDepth, depth);

            // 统计元素类型
            const tagName = element.tagName;
            this.stats.elementCounts[tagName] = (this.stats.elementCounts[tagName] || 0) + 1;

            // 统计属性
            for (const attr of element.attributes) {
                this.stats.attributeCounts[attr.name] = (this.stats.attributeCounts[attr.name] || 0) + 1;
            }

            // 统计文本长度
            for (const node of element.childNodes) {
                if (node.nodeType === Node.TEXT_NODE) {
                    this.stats.textLength += node.textContent.trim().length;
                }
            }

            visitChildren();
        });
    }

    getReport() {
        const { totalElements, elementCounts, maxDepth, attributeCounts, textLength } = this.stats;

        return {
            summary: {
                totalElements,
                uniqueElementTypes: Object.keys(elementCounts).length,
                maxDepth,
                totalTextLength: textLength,
                uniqueAttributes: Object.keys(attributeCounts).length
            },
            mostUsedElements: Object.entries(elementCounts)
                .sort(([, a], [, b]) => b - a)
                .slice(0, 5),
            mostUsedAttributes: Object.entries(attributeCounts)
                .sort(([, a], [, b]) => b - a)
                .slice(0, 5)
        };
    }
}

// 使用示例
const analyzer = new XMLAnalyzer();
const stats = analyzer.analyze(xmlString);
const report = analyzer.getReport();
console.log('XML分析报告:', report);
```

### 4. createElement() - 创建XML元素
```javascript
function createElement(tagName, ...args) {
    const el = xmlDocument.createElement(tagName);
    for (const arg of args) {
        if (!arg) {
            continue;
        }
        if (isIterable(arg)) {
            // Children list
            el.append(...arg);
        } else if (typeof arg === "object") {
            // Attributes
            for (const name in arg) {
                el.setAttribute(name, arg[name]);
            }
        }
    }
    return el;
}
```

**功能特性**:
- **灵活参数**: 支持属性对象和子元素数组的任意组合
- **自动识别**: 自动识别参数类型（属性或子元素）
- **批量添加**: 支持批量添加属性和子元素
- **空值过滤**: 自动过滤空值参数
- **XML文档**: 使用专用XML文档创建元素

### 5. combineAttributes() - 合并属性
```javascript
function combineAttributes(el, attr, parts, glue = " ") {
    const allValues = [];
    if (el.hasAttribute(attr)) {
        allValues.push(el.getAttribute(attr));
    }
    parts = Array.isArray(parts) ? parts : [parts];
    parts = parts.filter((part) => !!part);
    allValues.push(...parts);
    el.setAttribute(attr, allValues.join(glue));
}
```

**功能特性**:
- **值合并**: 将现有属性值与新值合并
- **自定义分隔符**: 支持自定义连接符（默认空格）
- **数组支持**: 支持传入数组或单个值
- **空值过滤**: 自动过滤空值和假值
- **就地修改**: 直接修改元素的属性值

**使用示例**:
```javascript
// 创建元素
const div = createElement('div',
    { class: 'container', id: 'main' },
    [
        createElement('h1', {}, [createTextNode('标题')]),
        createElement('p', { class: 'text' }, [createTextNode('内容')])
    ]
);

// 合并class属性
combineAttributes(div, 'class', ['responsive', 'dark-theme']);
console.log(div.getAttribute('class')); // "container responsive dark-theme"

// 合并style属性
combineAttributes(div, 'style', ['color: red', 'font-size: 14px'], '; ');
console.log(div.getAttribute('style')); // "color: red; font-size: 14px"

// 序列化输出
console.log(serializeXML(div));
```

### 6. 其他重要函数

#### append() - 追加节点
```javascript
function append(parent, node) {
    const nodes = Array.isArray(node) ? node : [node];
    parent.append(...nodes.filter(Boolean));
    return parent;
}
```

#### extractAttributes() - 提取属性
```javascript
function extractAttributes(el, attributes) {
    const attrs = Object.create(null);
    for (const attr of attributes) {
        attrs[attr] = el.getAttribute(attr) || "";
        el.removeAttribute(attr);
    }
    return attrs;
}
```

#### getTag() - 获取标签名
```javascript
function getTag(node, lower = false) {
    const tag = (node && node.nodeName) || "";
    return lower ? tag.toLowerCase() : tag;
}
```

#### setAttributes() - 设置属性
```javascript
function setAttributes(node, attributes) {
    for (const [name, value] of Object.entries(attributes)) {
        node.setAttribute(name, value);
    }
}
```

## 高级应用模式

### 1. XML构建器
```javascript
class XMLBuilder {
    constructor(rootTag = 'root') {
        this.root = createElement(rootTag);
        this.current = this.root;
        this.stack = [];
    }

    element(tagName, attributes = {}, content = null) {
        const el = createElement(tagName, attributes);

        if (content !== null) {
            if (typeof content === 'string') {
                el.appendChild(createTextNode(content));
            } else if (Array.isArray(content)) {
                content.forEach(child => {
                    if (typeof child === 'string') {
                        el.appendChild(createTextNode(child));
                    } else {
                        el.appendChild(child);
                    }
                });
            } else {
                el.appendChild(content);
            }
        }

        this.current.appendChild(el);
        return this;
    }

    open(tagName, attributes = {}) {
        const el = createElement(tagName, attributes);
        this.current.appendChild(el);
        this.stack.push(this.current);
        this.current = el;
        return this;
    }

    close() {
        if (this.stack.length > 0) {
            this.current = this.stack.pop();
        }
        return this;
    }

    text(content) {
        this.current.appendChild(createTextNode(content));
        return this;
    }

    comment(content) {
        const comment = xmlDocument.createComment(content);
        this.current.appendChild(comment);
        return this;
    }

    cdata(content) {
        const cdata = xmlDocument.createCDATASection(content);
        this.current.appendChild(cdata);
        return this;
    }

    build() {
        return this.root;
    }

    toString() {
        return serializeXML(this.root);
    }
}

// 使用示例
const builder = new XMLBuilder('document');
const xml = builder
    .element('header', { version: '1.0' })
    .open('body')
        .open('section', { id: 'content' })
            .element('title', {}, 'Main Content')
            .element('paragraph', { class: 'intro' }, 'This is the introduction.')
            .open('list', { type: 'ordered' })
                .element('item', {}, 'First item')
                .element('item', {}, 'Second item')
                .element('item', {}, 'Third item')
            .close()
        .close()
        .comment('End of body section')
    .close()
    .build();

console.log('构建的XML:', serializeXML(xml));
```

### 2. XML差异比较器
```javascript
class XMLDiffer {
    constructor() {
        this.differences = [];
    }

    compare(xml1, xml2) {
        this.differences = [];

        const element1 = typeof xml1 === 'string' ? parseXML(xml1) : xml1;
        const element2 = typeof xml2 === 'string' ? parseXML(xml2) : xml2;

        this.compareElements(element1, element2, '');

        return {
            identical: this.differences.length === 0,
            differences: this.differences
        };
    }

    compareElements(el1, el2, path) {
        // 比较标签名
        if (el1.tagName !== el2.tagName) {
            this.addDifference('tag', path, el1.tagName, el2.tagName);
            return;
        }

        // 比较属性
        this.compareAttributes(el1, el2, path);

        // 比较文本内容
        this.compareTextContent(el1, el2, path);

        // 比较子元素
        this.compareChildren(el1, el2, path);
    }

    compareAttributes(el1, el2, path) {
        const attrs1 = this.getAttributeMap(el1);
        const attrs2 = this.getAttributeMap(el2);

        const allAttrs = new Set([...Object.keys(attrs1), ...Object.keys(attrs2)]);

        for (const attr of allAttrs) {
            const value1 = attrs1[attr];
            const value2 = attrs2[attr];

            if (value1 !== value2) {
                this.addDifference('attribute', `${path}@${attr}`, value1, value2);
            }
        }
    }

    compareTextContent(el1, el2, path) {
        const text1 = this.getDirectTextContent(el1);
        const text2 = this.getDirectTextContent(el2);

        if (text1 !== text2) {
            this.addDifference('text', `${path}#text`, text1, text2);
        }
    }

    compareChildren(el1, el2, path) {
        const children1 = Array.from(el1.children);
        const children2 = Array.from(el2.children);

        const maxLength = Math.max(children1.length, children2.length);

        for (let i = 0; i < maxLength; i++) {
            const child1 = children1[i];
            const child2 = children2[i];
            const childPath = `${path}/${child1?.tagName || child2?.tagName}[${i}]`;

            if (!child1) {
                this.addDifference('missing', childPath, null, child2.tagName);
            } else if (!child2) {
                this.addDifference('extra', childPath, child1.tagName, null);
            } else {
                this.compareElements(child1, child2, childPath);
            }
        }
    }

    getAttributeMap(element) {
        const attrs = {};
        for (const attr of element.attributes) {
            attrs[attr.name] = attr.value;
        }
        return attrs;
    }

    getDirectTextContent(element) {
        let text = '';
        for (const node of element.childNodes) {
            if (node.nodeType === Node.TEXT_NODE) {
                text += node.textContent;
            }
        }
        return text.trim();
    }

    addDifference(type, path, value1, value2) {
        this.differences.push({
            type,
            path,
            expected: value1,
            actual: value2
        });
    }

    generateReport() {
        if (this.differences.length === 0) {
            return 'XML文档完全相同';
        }

        const report = ['XML差异报告:', ''];

        this.differences.forEach((diff, index) => {
            report.push(`${index + 1}. ${diff.type.toUpperCase()} - ${diff.path}`);
            report.push(`   期望: ${diff.expected || '(无)'}`);
            report.push(`   实际: ${diff.actual || '(无)'}`);
            report.push('');
        });

        return report.join('\n');
    }
}

// 使用示例
const differ = new XMLDiffer();

const xml1 = `
    <user id="123" name="John">
        <email><EMAIL></email>
        <age>30</age>
    </user>
`;

const xml2 = `
    <user id="123" name="Jane">
        <email><EMAIL></email>
        <phone>************</phone>
    </user>
`;

const result = differ.compare(xml1, xml2);
console.log('比较结果:', result);
console.log('差异报告:\n', differ.generateReport());
```

### 3. XML查询引擎
```javascript
class XMLQuery {
    constructor(xml) {
        this.root = typeof xml === 'string' ? parseXML(xml) : xml;
    }

    find(selector) {
        const results = [];

        if (selector.startsWith('//')) {
            // 全局搜索
            const tagName = selector.substring(2);
            this.findByTagName(this.root, tagName, results);
        } else if (selector.includes('[') && selector.includes(']')) {
            // 属性选择器
            this.findByAttributeSelector(selector, results);
        } else {
            // 简单标签名搜索
            this.findByTagName(this.root, selector, results);
        }

        return new XMLQueryResult(results);
    }

    findByTagName(element, tagName, results) {
        visitXML(element, (el, visitChildren) => {
            if (tagName === '*' || el.tagName === tagName) {
                results.push(el);
            }
            visitChildren();
        });
    }

    findByAttributeSelector(selector, results) {
        const match = selector.match(/^(\w+)\[(@?\w+)(?:([=!~])['"]?([^'"]*?)['"]?)?\]$/);
        if (!match) return;

        const [, tagName, attrName, operator, value] = match;
        const isAttribute = attrName.startsWith('@');
        const actualAttrName = isAttribute ? attrName.substring(1) : attrName;

        visitXML(this.root, (el, visitChildren) => {
            if (tagName === '*' || el.tagName === tagName) {
                if (this.matchesAttributeCondition(el, actualAttrName, operator, value, isAttribute)) {
                    results.push(el);
                }
            }
            visitChildren();
        });
    }

    matchesAttributeCondition(element, attrName, operator, value, isAttribute) {
        if (isAttribute) {
            const attrValue = element.getAttribute(attrName);

            if (!operator) {
                return attrValue !== null;
            }

            switch (operator) {
                case '=':
                    return attrValue === value;
                case '!':
                    return attrValue !== value;
                case '~':
                    return attrValue && attrValue.includes(value);
                default:
                    return false;
            }
        } else {
            // 文本内容匹配
            const textContent = element.textContent.trim();

            if (!operator) {
                return textContent.length > 0;
            }

            switch (operator) {
                case '=':
                    return textContent === value;
                case '!':
                    return textContent !== value;
                case '~':
                    return textContent.includes(value);
                default:
                    return false;
            }
        }
    }

    xpath(expression) {
        // 简化的XPath实现
        try {
            const result = xmlDocument.evaluate(
                expression,
                this.root,
                null,
                XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
                null
            );

            const nodes = [];
            for (let i = 0; i < result.snapshotLength; i++) {
                nodes.push(result.snapshotItem(i));
            }

            return new XMLQueryResult(nodes);
        } catch (error) {
            console.error('XPath查询失败:', error);
            return new XMLQueryResult([]);
        }
    }
}

class XMLQueryResult {
    constructor(elements) {
        this.elements = elements;
        this.length = elements.length;
    }

    first() {
        return this.elements[0] || null;
    }

    last() {
        return this.elements[this.elements.length - 1] || null;
    }

    get(index) {
        return this.elements[index] || null;
    }

    each(callback) {
        this.elements.forEach(callback);
        return this;
    }

    map(callback) {
        return this.elements.map(callback);
    }

    filter(callback) {
        const filtered = this.elements.filter(callback);
        return new XMLQueryResult(filtered);
    }

    attr(name, value) {
        if (value === undefined) {
            // 获取属性
            return this.first()?.getAttribute(name);
        } else {
            // 设置属性
            this.each(el => el.setAttribute(name, value));
            return this;
        }
    }

    text(content) {
        if (content === undefined) {
            // 获取文本
            return this.first()?.textContent || '';
        } else {
            // 设置文本
            this.each(el => {
                el.textContent = content;
            });
            return this;
        }
    }

    remove() {
        this.each(el => {
            if (el.parentElement) {
                el.parentElement.removeChild(el);
            }
        });
        return this;
    }

    toArray() {
        return [...this.elements];
    }
}

// 使用示例
const queryXml = `
    <library>
        <book id="1" category="fiction">
            <title>Book One</title>
            <author>Author A</author>
            <price>29.99</price>
        </book>
        <book id="2" category="science">
            <title>Book Two</title>
            <author>Author B</author>
            <price>39.99</price>
        </book>
        <magazine id="3" category="tech">
            <title>Tech Magazine</title>
            <issue>2024-01</issue>
        </magazine>
    </library>
`;

const query = new XMLQuery(queryXml);

// 查找所有书籍
const books = query.find('book');
console.log('找到书籍数量:', books.length);

// 查找特定类别的书籍
const fictionBooks = query.find('book[@category="fiction"]');
console.log('小说类书籍:', fictionBooks.map(el => el.querySelector('title').textContent));

// 使用XPath查询
const expensiveBooks = query.xpath('//book[price > 35]');
console.log('价格超过35的书籍:', expensiveBooks.length);

// 修改属性
books.attr('available', 'true');
console.log('修改后的XML:', serializeXML(query.root));
```

## 最佳实践

### 1. 安全性
```javascript
// ✅ 推荐：安全的XML解析
function safeParseXML(xmlString, options = {}) {
    try {
        // 限制XML大小
        if (xmlString.length > (options.maxSize || 1024 * 1024)) {
            throw new Error('XML文档过大');
        }

        // 检查恶意内容
        if (xmlString.includes('<!ENTITY') || xmlString.includes('<!DOCTYPE')) {
            throw new Error('不允许的XML实体或DOCTYPE声明');
        }

        return parseXML(xmlString);
    } catch (error) {
        console.error('XML解析失败:', error);
        throw error;
    }
}

// ✅ 推荐：输入验证
function validateXMLInput(input) {
    if (typeof input !== 'string') {
        throw new Error('XML输入必须是字符串');
    }

    if (input.trim().length === 0) {
        throw new Error('XML输入不能为空');
    }

    return input.trim();
}
```

### 2. 性能优化
```javascript
// ✅ 推荐：XML缓存
const xmlCache = new Map();
function cachedParseXML(xmlString) {
    if (xmlCache.has(xmlString)) {
        return xmlCache.get(xmlString).cloneNode(true);
    }

    const parsed = parseXML(xmlString);
    xmlCache.set(xmlString, parsed);

    return parsed.cloneNode(true);
}

// ✅ 推荐：批量操作
function batchCreateElements(definitions) {
    return definitions.map(def =>
        createElement(def.tag, def.attributes, def.children)
    );
}
```

### 3. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
function robustXMLOperation(operation) {
    try {
        return operation();
    } catch (error) {
        if (error.name === 'SyntaxError') {
            throw new Error(`XML语法错误: ${error.message}`);
        } else if (error.name === 'TypeError') {
            throw new Error(`XML类型错误: ${error.message}`);
        } else {
            throw new Error(`XML操作失败: ${error.message}`);
        }
    }
}
```

## 总结

Odoo XML工具模块提供了完整的XML处理功能：

**核心优势**:
- **标准兼容**: 使用标准DOM API确保兼容性
- **大小写敏感**: 正确处理XML的大小写敏感特性
- **错误处理**: 完善的解析错误检测和处理
- **灵活操作**: 提供丰富的XML操作和遍历功能
- **性能优化**: 高效的XML处理和缓存机制

**适用场景**:
- QWeb模板处理
- 配置文件解析
- 数据交换格式
- 文档结构化处理
- API响应解析

**设计优势**:
- 函数式API设计
- 类型安全处理
- 内存效率优化
- 扩展性良好

这个XML工具为 Odoo Web 客户端提供了强大的XML处理能力，是构建现代Web应用的重要基础。

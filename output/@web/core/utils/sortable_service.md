# Odoo 排序服务 (Sortable Service) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/sortable_service.js`  
**原始路径**: `/web/static/src/core/utils/sortable_service.js`  
**模块类型**: 核心服务模块 - 排序功能服务  
**代码行数**: 102 行  
**依赖关系**: 
- `@web/core/registry` - 服务注册表
- `@web/core/utils/sortable` - 排序工具
- `@web/core/utils/timing` - 时间工具 (throttleForAnimation)
- `@odoo/owl` - OWL框架 (reactive)

## 模块功能

排序服务模块是 Odoo Web 客户端的核心服务，提供了全局的排序功能管理。该服务的主要作用是：
- 统一管理排序实例
- 避免重复绑定同一元素
- 提供服务级别的排序功能
- 自动处理资源清理
- 支持多个排序实例共存

这个服务确保排序功能能够在整个应用中统一管理，避免内存泄漏和重复绑定问题。

## 核心设计理念

### 服务模式
排序服务采用了服务模式设计，将排序功能作为全局服务提供：

```javascript
const sortableService = {
    start() {
        const boundElements = new Map();
        return {
            create: (hookParams) => {
                // 创建排序实例
            }
        };
    }
};
```

### 实例管理
服务维护一个全局的元素绑定映射，避免重复绑定：

```javascript
// 避免重复绑定检查
if (boundElements.has(element)) {
    const boundElement = boundElements.get(element);
    if (sortableId in boundElement) {
        return { enable() { /* 返回已存在的实例 */ } };
    }
}
```

### 资源清理
服务提供自动的资源清理机制：

```javascript
const cleanup = () => {
    const boundElement = boundElements.get(element);
    if (sortableId in boundElement) {
        delete boundElement[sortableId];
        if (boundElement.length === 0) {
            boundElements.delete(element);
        }
    }
    cleanupFunctions.forEach((fn) => fn());
};
```

## 核心类型定义

### SortableServiceHookParams - 服务Hook参数
```typescript
interface SortableServiceHookParams extends SortableParams {
    ref: { el: HTMLElement } | ReturnType<typeof useRef>;
    sortableId?: string | Symbol;
}
```

## 核心服务详解

### sortableService - 排序服务
```javascript
const sortableService = {
    start() {
        const boundElements = new Map();
        return {
            create: (hookParams) => {
                // 服务实现
            }
        };
    }
};
```

**功能特性**:
- **实例管理**: 统一管理所有排序实例
- **重复检测**: 避免重复绑定同一元素
- **资源清理**: 自动清理不再使用的资源
- **多实例支持**: 支持同一元素的多个排序实例
- **生命周期管理**: 完整的创建、启用、清理生命周期

**使用示例**:
```javascript
// 在组件中使用排序服务
class SortableComponent extends Component {
    setup() {
        this.listRef = useRef("sortableList");
        this.sortableService = useService("sortable");
        
        // 创建排序实例
        this.sortableInstance = this.sortableService.create({
            ref: this.listRef,
            elements: ".sortable-item",
            sortableId: "main-list",
            
            onDragStart: ({ element }) => {
                console.log("开始拖拽:", element);
            },
            
            onDrop: ({ element, previous, next }) => {
                this.handleItemDrop(element, previous, next);
            }
        });
        
        // 启用排序
        onMounted(() => {
            this.sortableCleanup = this.sortableInstance.enable();
        });
        
        // 清理资源
        onWillUnmount(() => {
            if (this.sortableCleanup) {
                this.sortableCleanup.cleanup();
            }
        });
    }
    
    handleItemDrop(element, previous, next) {
        const itemId = element.dataset.itemId;
        const newOrder = this.calculateNewOrder(previous, next);
        
        // 更新数据模型
        this.updateItemOrder(itemId, newOrder);
    }
}
```

**实际应用场景**:
```javascript
// 1. 全局排序管理器
class GlobalSortableManager {
    constructor() {
        this.sortableService = null;
        this.activeSortables = new Map();
    }
    
    init(env) {
        this.sortableService = env.services.sortable;
    }
    
    createSortable(containerId, config) {
        const container = document.getElementById(containerId);
        if (!container) {
            throw new Error(`容器不存在: ${containerId}`);
        }
        
        const sortableInstance = this.sortableService.create({
            ref: { el: container },
            elements: config.elements || ".sortable-item",
            sortableId: config.id || containerId,
            
            onDragStart: (params) => {
                this.onGlobalDragStart(containerId, params);
                config.onDragStart?.(params);
            },
            
            onDrop: (params) => {
                this.onGlobalDrop(containerId, params);
                config.onDrop?.(params);
            },
            
            onDragEnd: (params) => {
                this.onGlobalDragEnd(containerId, params);
                config.onDragEnd?.(params);
            }
        });
        
        this.activeSortables.set(containerId, {
            instance: sortableInstance,
            config: config,
            enabled: false
        });
        
        return sortableInstance;
    }
    
    enableSortable(containerId) {
        const sortable = this.activeSortables.get(containerId);
        if (sortable && !sortable.enabled) {
            sortable.cleanup = sortable.instance.enable();
            sortable.enabled = true;
            
            console.log(`排序功能已启用: ${containerId}`);
        }
    }
    
    disableSortable(containerId) {
        const sortable = this.activeSortables.get(containerId);
        if (sortable && sortable.enabled) {
            sortable.cleanup?.cleanup();
            sortable.enabled = false;
            
            console.log(`排序功能已禁用: ${containerId}`);
        }
    }
    
    destroySortable(containerId) {
        const sortable = this.activeSortables.get(containerId);
        if (sortable) {
            if (sortable.enabled) {
                sortable.cleanup?.cleanup();
            }
            this.activeSortables.delete(containerId);
            
            console.log(`排序实例已销毁: ${containerId}`);
        }
    }
    
    onGlobalDragStart(containerId, params) {
        // 全局拖拽开始处理
        this.currentDrag = {
            containerId: containerId,
            element: params.element,
            startTime: Date.now()
        };
        
        // 禁用其他容器的排序功能
        this.activeSortables.forEach((sortable, id) => {
            if (id !== containerId && sortable.enabled) {
                sortable.instance.disable?.();
            }
        });
    }
    
    onGlobalDrop(containerId, params) {
        // 全局拖放处理
        if (this.currentDrag) {
            const duration = Date.now() - this.currentDrag.startTime;
            console.log(`拖拽完成: ${containerId}, 耗时: ${duration}ms`);
            
            // 记录拖拽统计
            this.recordDragStats(containerId, duration);
        }
    }
    
    onGlobalDragEnd(containerId, params) {
        // 重新启用其他容器的排序功能
        this.activeSortables.forEach((sortable, id) => {
            if (id !== containerId && sortable.enabled) {
                sortable.instance.enable?.();
            }
        });
        
        this.currentDrag = null;
    }
    
    recordDragStats(containerId, duration) {
        // 记录拖拽统计信息
        const stats = this.getStats(containerId);
        stats.dragCount++;
        stats.totalDuration += duration;
        stats.averageDuration = stats.totalDuration / stats.dragCount;
        
        console.log(`${containerId} 拖拽统计:`, stats);
    }
    
    getStats(containerId) {
        if (!this.stats) {
            this.stats = new Map();
        }
        
        if (!this.stats.has(containerId)) {
            this.stats.set(containerId, {
                dragCount: 0,
                totalDuration: 0,
                averageDuration: 0
            });
        }
        
        return this.stats.get(containerId);
    }
    
    getAllStats() {
        const allStats = {};
        if (this.stats) {
            this.stats.forEach((stats, containerId) => {
                allStats[containerId] = { ...stats };
            });
        }
        return allStats;
    }
}

// 2. 动态排序配置管理器
class DynamicSortableConfigManager {
    constructor(sortableService) {
        this.sortableService = sortableService;
        this.configurations = new Map();
        this.activeInstances = new Map();
    }
    
    registerConfiguration(name, config) {
        this.configurations.set(name, {
            ...config,
            name: name,
            created: Date.now()
        });
    }
    
    applySortableToElement(element, configName, overrides = {}) {
        const config = this.configurations.get(configName);
        if (!config) {
            throw new Error(`排序配置不存在: ${configName}`);
        }
        
        const instanceId = `${configName}_${Date.now()}`;
        const finalConfig = {
            ref: { el: element },
            sortableId: instanceId,
            ...config,
            ...overrides
        };
        
        const instance = this.sortableService.create(finalConfig);
        const cleanup = instance.enable();
        
        this.activeInstances.set(instanceId, {
            instance: instance,
            cleanup: cleanup,
            element: element,
            configName: configName,
            created: Date.now()
        });
        
        return instanceId;
    }
    
    updateConfiguration(name, updates) {
        const config = this.configurations.get(name);
        if (config) {
            Object.assign(config, updates);
            
            // 重新应用到所有使用此配置的实例
            this.reapplyConfiguration(name);
        }
    }
    
    reapplyConfiguration(configName) {
        const instancesToUpdate = [];
        
        this.activeInstances.forEach((instanceData, instanceId) => {
            if (instanceData.configName === configName) {
                instancesToUpdate.push({ instanceId, instanceData });
            }
        });
        
        instancesToUpdate.forEach(({ instanceId, instanceData }) => {
            // 清理旧实例
            instanceData.cleanup.cleanup();
            this.activeInstances.delete(instanceId);
            
            // 创建新实例
            this.applySortableToElement(
                instanceData.element,
                configName
            );
        });
    }
    
    removeInstance(instanceId) {
        const instanceData = this.activeInstances.get(instanceId);
        if (instanceData) {
            instanceData.cleanup.cleanup();
            this.activeInstances.delete(instanceId);
        }
    }
    
    removeAllInstances() {
        this.activeInstances.forEach((instanceData) => {
            instanceData.cleanup.cleanup();
        });
        this.activeInstances.clear();
    }
    
    getInstanceInfo(instanceId) {
        return this.activeInstances.get(instanceId);
    }
    
    getAllInstances() {
        const instances = {};
        this.activeInstances.forEach((instanceData, instanceId) => {
            instances[instanceId] = {
                configName: instanceData.configName,
                created: instanceData.created,
                element: instanceData.element.tagName
            };
        });
        return instances;
    }
}

// 3. 排序性能监控器
class SortablePerformanceMonitor {
    constructor(sortableService) {
        this.sortableService = sortableService;
        this.metrics = new Map();
        this.isMonitoring = false;
    }
    
    startMonitoring() {
        this.isMonitoring = true;
        console.log('排序性能监控已启动');
    }
    
    stopMonitoring() {
        this.isMonitoring = false;
        console.log('排序性能监控已停止');
    }
    
    createMonitoredSortable(config) {
        const originalConfig = { ...config };
        
        // 包装事件处理器以收集性能数据
        const wrappedConfig = {
            ...originalConfig,
            
            onDragStart: (params) => {
                if (this.isMonitoring) {
                    this.recordDragStart(config.sortableId || 'default', params);
                }
                originalConfig.onDragStart?.(params);
            },
            
            onDrag: (params) => {
                if (this.isMonitoring) {
                    this.recordDragMove(config.sortableId || 'default', params);
                }
                originalConfig.onDrag?.(params);
            },
            
            onDrop: (params) => {
                if (this.isMonitoring) {
                    this.recordDrop(config.sortableId || 'default', params);
                }
                originalConfig.onDrop?.(params);
            },
            
            onDragEnd: (params) => {
                if (this.isMonitoring) {
                    this.recordDragEnd(config.sortableId || 'default', params);
                }
                originalConfig.onDragEnd?.(params);
            }
        };
        
        return this.sortableService.create(wrappedConfig);
    }
    
    recordDragStart(sortableId, params) {
        const metrics = this.getMetrics(sortableId);
        metrics.dragStartTime = performance.now();
        metrics.dragCount++;
        
        console.log(`[${sortableId}] 拖拽开始 #${metrics.dragCount}`);
    }
    
    recordDragMove(sortableId, params) {
        const metrics = this.getMetrics(sortableId);
        if (!metrics.dragMoveCount) {
            metrics.dragMoveCount = 0;
        }
        metrics.dragMoveCount++;
    }
    
    recordDrop(sortableId, params) {
        const metrics = this.getMetrics(sortableId);
        const dropTime = performance.now();
        
        if (metrics.dragStartTime) {
            const duration = dropTime - metrics.dragStartTime;
            metrics.totalDragDuration += duration;
            metrics.averageDragDuration = metrics.totalDragDuration / metrics.dragCount;
            
            console.log(`[${sortableId}] 拖放完成，耗时: ${duration.toFixed(2)}ms`);
        }
        
        metrics.dropCount++;
    }
    
    recordDragEnd(sortableId, params) {
        const metrics = this.getMetrics(sortableId);
        metrics.dragMoveCount = 0; // 重置移动计数
        
        console.log(`[${sortableId}] 拖拽结束`);
    }
    
    getMetrics(sortableId) {
        if (!this.metrics.has(sortableId)) {
            this.metrics.set(sortableId, {
                dragCount: 0,
                dropCount: 0,
                dragMoveCount: 0,
                totalDragDuration: 0,
                averageDragDuration: 0,
                dragStartTime: null
            });
        }
        return this.metrics.get(sortableId);
    }
    
    getPerformanceReport() {
        const report = {};
        this.metrics.forEach((metrics, sortableId) => {
            report[sortableId] = {
                ...metrics,
                dragStartTime: undefined // 不包含临时数据
            };
        });
        return report;
    }
    
    resetMetrics(sortableId = null) {
        if (sortableId) {
            this.metrics.delete(sortableId);
        } else {
            this.metrics.clear();
        }
    }
}
```

## 最佳实践

### 1. 服务使用模式
```javascript
// ✅ 推荐：正确的服务使用模式
class BestPracticeSortableComponent extends Component {
    setup() {
        this.listRef = useRef("sortableList");
        this.sortableService = useService("sortable");
        this.sortableInstance = null;
        this.sortableCleanup = null;

        onMounted(() => {
            this.initializeSortable();
        });

        onWillUnmount(() => {
            this.cleanupSortable();
        });
    }

    initializeSortable() {
        try {
            this.sortableInstance = this.sortableService.create({
                ref: this.listRef,
                elements: ".sortable-item",
                sortableId: `list-${this.props.listId}`,

                onDrop: ({ element, previous, next }) => {
                    this.handleDrop(element, previous, next);
                }
            });

            this.sortableCleanup = this.sortableInstance.enable();
        } catch (error) {
            console.error('初始化排序失败:', error);
        }
    }

    cleanupSortable() {
        if (this.sortableCleanup) {
            this.sortableCleanup.cleanup();
            this.sortableCleanup = null;
        }
        this.sortableInstance = null;
    }

    handleDrop(element, previous, next) {
        // 处理拖放逻辑
    }
}
```

### 2. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
class SafeSortableService {
    constructor(sortableService) {
        this.sortableService = sortableService;
        this.errorHandlers = [];
    }

    addErrorHandler(handler) {
        this.errorHandlers.push(handler);
    }

    create(config) {
        try {
            return this.sortableService.create(config);
        } catch (error) {
            this.handleError(error, config);
            throw error;
        }
    }

    handleError(error, config) {
        const errorInfo = {
            error: error,
            config: config,
            timestamp: Date.now(),
            userAgent: navigator.userAgent
        };

        this.errorHandlers.forEach(handler => {
            try {
                handler(errorInfo);
            } catch (handlerError) {
                console.error('错误处理器失败:', handlerError);
            }
        });
    }
}
```

### 3. 内存管理
```javascript
// ✅ 推荐：正确的内存管理
class MemoryEfficientSortableManager {
    constructor(sortableService) {
        this.sortableService = sortableService;
        this.instances = new WeakMap(); // 使用WeakMap避免内存泄漏
        this.cleanupCallbacks = new Set();
    }

    createSortable(element, config) {
        const instance = this.sortableService.create({
            ref: { el: element },
            ...config
        });

        const cleanup = instance.enable();

        // 使用WeakMap存储实例
        this.instances.set(element, { instance, cleanup });
        this.cleanupCallbacks.add(cleanup.cleanup);

        return instance;
    }

    destroyAll() {
        this.cleanupCallbacks.forEach(cleanup => {
            try {
                cleanup();
            } catch (error) {
                console.error('清理失败:', error);
            }
        });

        this.cleanupCallbacks.clear();
    }
}
```

## 总结

Odoo 排序服务模块提供了企业级的排序功能管理：

**核心优势**:
- **统一管理**: 全局统一管理所有排序实例
- **重复检测**: 避免重复绑定同一元素
- **资源清理**: 自动处理资源清理和内存管理
- **多实例支持**: 支持同一元素的多个排序实例
- **服务模式**: 采用服务模式提供全局功能

**适用场景**:
- 企业级应用的排序管理
- 需要统一管理的排序功能
- 复杂的排序实例生命周期
- 性能监控和分析需求

**设计优势**:
- 服务注册表集成
- 完整的生命周期管理
- 灵活的配置系统
- 企业级功能支持

这个排序服务为 Odoo Web 客户端提供了企业级的排序功能管理能力，是构建大型应用排序系统的重要基础。

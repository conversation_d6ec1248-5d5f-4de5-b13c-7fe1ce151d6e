# Odoo 二进制工具 (Binary Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/binary.js`  
**原始路径**: `/web/static/src/core/utils/binary.js`  
**模块类型**: 核心工具模块 - 二进制数据处理工具  
**代码行数**: 38 行  
**依赖关系**: 
- `@web/core/l10n/translation` - 国际化翻译 (_t函数)

## 模块功能

二进制工具模块是 Odoo Web 客户端的核心工具库，提供了二进制数据处理的基础功能。该模块专注于：
- 二进制大小格式检测
- Base64编码长度计算
- 人类可读的文件大小格式化
- 国际化的单位显示

虽然代码量不大，但这些功能是文件处理、数据传输和用户界面显示的重要基础。

## 核心函数详解

### 1. isBinarySize() - 二进制大小格式检测
```javascript
function isBinarySize(value) {
    return /^\d+(\.\d*)? [^0-9]+$/.test(value);
}
```

**功能特性**:
- **格式检测**: 检测字符串是否为二进制大小格式（如 "1.5 MB"）
- **正则匹配**: 使用正则表达式精确匹配格式模式
- **小数支持**: 支持整数和小数形式的大小值
- **单位验证**: 验证是否包含非数字的单位部分

**正则表达式解析**:
- `^\d+`: 以一个或多个数字开头
- `(\.\d*)?`: 可选的小数部分（点号后跟零个或多个数字）
- ` `: 一个空格分隔符
- `[^0-9]+`: 一个或多个非数字字符（单位部分）
- `$`: 字符串结尾

**使用示例**:
```javascript
// 有效的二进制大小格式
console.log(isBinarySize("1024 Bytes"));    // true
console.log(isBinarySize("1.5 MB"));        // true
console.log(isBinarySize("2.0 GB"));        // true
console.log(isBinarySize("500 KB"));        // true
console.log(isBinarySize("0.5 TB"));        // true

// 无效的格式
console.log(isBinarySize("1024"));          // false (缺少单位)
console.log(isBinarySize("MB 1024"));       // false (顺序错误)
console.log(isBinarySize("1,024 MB"));      // false (包含逗号)
console.log(isBinarySize("1024MB"));        // false (缺少空格)
console.log(isBinarySize(""));              // false (空字符串)
```

**实际应用场景**:
```javascript
// 1. 文件大小验证
class FileValidator {
    validateFileSize(sizeString, maxSize) {
        if (!isBinarySize(sizeString)) {
            throw new Error("无效的文件大小格式");
        }
        
        const actualSize = this.parseBinarySize(sizeString);
        if (actualSize > maxSize) {
            throw new Error(`文件大小超过限制: ${sizeString}`);
        }
        
        return true;
    }
    
    parseBinarySize(sizeString) {
        const [value, unit] = sizeString.split(' ');
        const numValue = parseFloat(value);
        
        const multipliers = {
            'Bytes': 1,
            'KB': 1024,
            'MB': 1024 * 1024,
            'GB': 1024 * 1024 * 1024,
            'TB': 1024 * 1024 * 1024 * 1024
        };
        
        return numValue * (multipliers[unit] || 1);
    }
}

// 2. 数据格式检测器
class DataFormatDetector {
    detectFormat(input) {
        if (isBinarySize(input)) {
            return {
                type: 'binary_size',
                value: input,
                parsed: this.parseBinarySize(input)
            };
        }
        
        if (this.isUrl(input)) {
            return { type: 'url', value: input };
        }
        
        if (this.isEmail(input)) {
            return { type: 'email', value: input };
        }
        
        return { type: 'unknown', value: input };
    }
    
    parseBinarySize(sizeString) {
        const [value, unit] = sizeString.split(' ');
        return {
            value: parseFloat(value),
            unit: unit,
            bytes: this.convertToBytes(parseFloat(value), unit)
        };
    }
}

// 3. 表单字段验证
class FormFieldValidator {
    validateSizeField(fieldValue) {
        if (!fieldValue) {
            return { valid: false, error: "文件大小不能为空" };
        }
        
        if (!isBinarySize(fieldValue)) {
            return { 
                valid: false, 
                error: "文件大小格式无效，请使用格式如 '1.5 MB'" 
            };
        }
        
        return { valid: true };
    }
    
    validateForm(formData) {
        const errors = {};
        
        if (formData.maxFileSize && !this.validateSizeField(formData.maxFileSize).valid) {
            errors.maxFileSize = this.validateSizeField(formData.maxFileSize).error;
        }
        
        if (formData.minFileSize && !this.validateSizeField(formData.minFileSize).valid) {
            errors.minFileSize = this.validateSizeField(formData.minFileSize).error;
        }
        
        return {
            valid: Object.keys(errors).length === 0,
            errors
        };
    }
}

// 4. 配置文件解析器
class ConfigParser {
    parseStorageConfig(config) {
        const parsedConfig = {};
        
        for (const [key, value] of Object.entries(config)) {
            if (typeof value === 'string' && isBinarySize(value)) {
                parsedConfig[key] = {
                    original: value,
                    bytes: this.convertToBytes(value),
                    type: 'size'
                };
            } else {
                parsedConfig[key] = {
                    original: value,
                    type: 'other'
                };
            }
        }
        
        return parsedConfig;
    }
    
    convertToBytes(sizeString) {
        const [value, unit] = sizeString.split(' ');
        const numValue = parseFloat(value);
        
        const units = {
            'Bytes': 1,
            'KB': 1024,
            'MB': 1024 ** 2,
            'GB': 1024 ** 3,
            'TB': 1024 ** 4
        };
        
        return numValue * (units[unit] || 1);
    }
}
```

### 2. toBase64Length() - Base64编码长度计算
```javascript
function toBase64Length(maxBytes) {
    return Math.ceil(maxBytes * 4 / 3);
}
```

**功能特性**:
- **编码长度**: 计算指定字节数编码为Base64后的字符长度
- **数学精确**: 基于Base64编码规则的精确计算
- **向上取整**: 使用Math.ceil确保长度足够
- **性能优化**: 简单的数学计算，性能优异

**Base64编码原理**:
- Base64将每3个字节（24位）编码为4个字符
- 编码比率为 4/3，即编码后长度约为原长度的133%
- 不足3字节的部分会用填充字符补齐

**使用示例**:
```javascript
// 基本长度计算
console.log(toBase64Length(3));     // 4 (3字节 -> 4字符)
console.log(toBase64Length(6));     // 8 (6字节 -> 8字符)
console.log(toBase64Length(1));     // 4 (1字节 -> 4字符，包含填充)
console.log(toBase64Length(2));     // 4 (2字节 -> 4字符，包含填充)
console.log(toBase64Length(1024));  // 1366 (1KB -> 1366字符)

// 文件大小计算
const fileSizeBytes = 1024 * 1024; // 1MB
const base64Length = toBase64Length(fileSizeBytes);
console.log(`1MB文件编码为Base64后长度: ${base64Length} 字符`);
```

**实际应用场景**:
```javascript
// 1. 文件上传预检查
class FileUploadManager {
    constructor(maxBase64Length = 10 * 1024 * 1024) { // 10MB limit
        this.maxBase64Length = maxBase64Length;
    }
    
    canUploadFile(fileSize) {
        const base64Length = toBase64Length(fileSize);
        
        if (base64Length > this.maxBase64Length) {
            return {
                canUpload: false,
                reason: `文件编码后大小 ${base64Length} 字符超过限制 ${this.maxBase64Length} 字符`,
                base64Size: base64Length,
                originalSize: fileSize
            };
        }
        
        return {
            canUpload: true,
            base64Size: base64Length,
            originalSize: fileSize
        };
    }
    
    calculateUploadMetrics(files) {
        let totalOriginalSize = 0;
        let totalBase64Size = 0;
        const fileMetrics = [];
        
        for (const file of files) {
            const base64Size = toBase64Length(file.size);
            totalOriginalSize += file.size;
            totalBase64Size += base64Size;
            
            fileMetrics.push({
                name: file.name,
                originalSize: file.size,
                base64Size: base64Size,
                overhead: base64Size - file.size
            });
        }
        
        return {
            files: fileMetrics,
            totalOriginalSize,
            totalBase64Size,
            totalOverhead: totalBase64Size - totalOriginalSize,
            compressionRatio: totalBase64Size / totalOriginalSize
        };
    }
}

// 2. 内存使用估算
class MemoryEstimator {
    estimateBase64Memory(dataSize) {
        const base64Length = toBase64Length(dataSize);
        
        // JavaScript字符串在内存中通常占用2字节每字符（UTF-16）
        const memoryUsage = base64Length * 2;
        
        return {
            originalSize: dataSize,
            base64Length: base64Length,
            memoryUsage: memoryUsage,
            overhead: memoryUsage - dataSize,
            ratio: memoryUsage / dataSize
        };
    }
    
    estimateBatchMemory(dataSizes) {
        let totalMemory = 0;
        const estimates = [];
        
        for (const size of dataSizes) {
            const estimate = this.estimateBase64Memory(size);
            estimates.push(estimate);
            totalMemory += estimate.memoryUsage;
        }
        
        return {
            individual: estimates,
            totalMemory: totalMemory,
            averageOverhead: estimates.reduce((sum, est) => sum + est.overhead, 0) / estimates.length
        };
    }
}

// 3. 网络传输优化
class NetworkOptimizer {
    shouldCompressBeforeBase64(dataSize, compressionRatio = 0.7) {
        const originalBase64Size = toBase64Length(dataSize);
        const compressedSize = dataSize * compressionRatio;
        const compressedBase64Size = toBase64Length(compressedSize);
        
        return {
            shouldCompress: compressedBase64Size < originalBase64Size,
            originalBase64Size: originalBase64Size,
            compressedBase64Size: compressedBase64Size,
            savings: originalBase64Size - compressedBase64Size,
            savingsPercent: ((originalBase64Size - compressedBase64Size) / originalBase64Size) * 100
        };
    }
    
    optimizeTransfer(dataSize) {
        const compressionAnalysis = this.shouldCompressBeforeBase64(dataSize);
        
        if (compressionAnalysis.shouldCompress) {
            return {
                strategy: 'compress_then_encode',
                finalSize: compressionAnalysis.compressedBase64Size,
                savings: compressionAnalysis.savings
            };
        } else {
            return {
                strategy: 'direct_encode',
                finalSize: compressionAnalysis.originalBase64Size,
                savings: 0
            };
        }
    }
}

// 4. 缓冲区大小计算
class BufferSizeCalculator {
    calculateOptimalBufferSize(totalDataSize, maxMemoryUsage) {
        // 计算可以同时处理的最大数据量
        const maxDataSize = Math.floor(maxMemoryUsage * 3 / 4); // Base64编码开销
        
        if (totalDataSize <= maxDataSize) {
            return {
                bufferSize: totalDataSize,
                chunks: 1,
                base64Size: toBase64Length(totalDataSize)
            };
        }
        
        // 需要分块处理
        const chunks = Math.ceil(totalDataSize / maxDataSize);
        const bufferSize = Math.ceil(totalDataSize / chunks);
        
        return {
            bufferSize: bufferSize,
            chunks: chunks,
            base64SizePerChunk: toBase64Length(bufferSize),
            totalBase64Size: toBase64Length(totalDataSize)
        };
    }
}
```

### 3. humanSize() - 人类可读文件大小
```javascript
function humanSize(size) {
    const units = _t("Bytes|Kb|Mb|Gb|Tb|Pb|Eb|Zb|Yb").split("|");
    let i = 0;
    while (size >= 1024) {
        size /= 1024;
        ++i;
    }
    return `${size.toFixed(2)} ${units[i].trim()}`;
}
```

**功能特性**:
- **国际化支持**: 使用_t函数支持多语言单位显示
- **自动单位**: 自动选择最合适的单位（Bytes到Yb）
- **精度控制**: 固定显示2位小数
- **完整范围**: 支持从字节到尧字节的完整范围

**单位说明**:
- **Bytes**: 字节
- **Kb**: 千字节 (1024 Bytes)
- **Mb**: 兆字节 (1024 KB)
- **Gb**: 吉字节 (1024 MB)
- **Tb**: 太字节 (1024 GB)
- **Pb**: 拍字节 (1024 TB)
- **Eb**: 艾字节 (1024 PB)
- **Zb**: 泽字节 (1024 EB)
- **Yb**: 尧字节 (1024 ZB)

**使用示例**:
```javascript
// 基本大小格式化
console.log(humanSize(512));           // "512.00 Bytes"
console.log(humanSize(1024));          // "1.00 Kb"
console.log(humanSize(1536));          // "1.50 Kb"
console.log(humanSize(1048576));       // "1.00 Mb"
console.log(humanSize(1073741824));    // "1.00 Gb"

// 实际文件大小
console.log(humanSize(2147483648));    // "2.00 Gb"
console.log(humanSize(5368709120));    // "5.00 Gb"

// 极大数值
console.log(humanSize(1024 ** 8));     // "1.00 Yb"
```

**实际应用场景**:
```javascript
// 1. 文件管理器
class FileManager {
    constructor() {
        this.files = [];
    }

    addFile(file) {
        const fileInfo = {
            name: file.name,
            size: file.size,
            humanSize: humanSize(file.size),
            type: file.type,
            lastModified: file.lastModified
        };

        this.files.push(fileInfo);
        return fileInfo;
    }

    getFileList() {
        return this.files.map(file => ({
            ...file,
            displaySize: file.humanSize
        }));
    }

    getTotalSize() {
        const totalBytes = this.files.reduce((sum, file) => sum + file.size, 0);
        return {
            bytes: totalBytes,
            human: humanSize(totalBytes)
        };
    }

    getFilesGroupedBySize() {
        const groups = {
            small: [],    // < 1MB
            medium: [],   // 1MB - 100MB
            large: [],    // 100MB - 1GB
            huge: []      // > 1GB
        };

        this.files.forEach(file => {
            if (file.size < 1024 * 1024) {
                groups.small.push(file);
            } else if (file.size < 100 * 1024 * 1024) {
                groups.medium.push(file);
            } else if (file.size < 1024 * 1024 * 1024) {
                groups.large.push(file);
            } else {
                groups.huge.push(file);
            }
        });

        return groups;
    }
}

// 2. 存储空间监控
class StorageMonitor {
    constructor() {
        this.quotas = new Map();
        this.usage = new Map();
    }

    setQuota(userId, quotaBytes) {
        this.quotas.set(userId, {
            bytes: quotaBytes,
            human: humanSize(quotaBytes)
        });
    }

    updateUsage(userId, usedBytes) {
        const quota = this.quotas.get(userId);
        if (!quota) return null;

        const usage = {
            used: usedBytes,
            quota: quota.bytes,
            remaining: quota.bytes - usedBytes,
            percentage: (usedBytes / quota.bytes) * 100,
            humanUsed: humanSize(usedBytes),
            humanQuota: quota.human,
            humanRemaining: humanSize(quota.bytes - usedBytes)
        };

        this.usage.set(userId, usage);
        return usage;
    }

    getStorageReport(userId) {
        const usage = this.usage.get(userId);
        if (!usage) return null;

        return {
            ...usage,
            status: this.getStorageStatus(usage.percentage),
            warning: usage.percentage > 80,
            critical: usage.percentage > 95
        };
    }

    getStorageStatus(percentage) {
        if (percentage < 50) return 'healthy';
        if (percentage < 80) return 'moderate';
        if (percentage < 95) return 'warning';
        return 'critical';
    }
}

// 3. 下载进度显示
class DownloadProgressTracker {
    constructor() {
        this.downloads = new Map();
    }

    startDownload(downloadId, totalSize) {
        this.downloads.set(downloadId, {
            id: downloadId,
            totalSize: totalSize,
            downloadedSize: 0,
            startTime: Date.now(),
            humanTotal: humanSize(totalSize),
            humanDownloaded: humanSize(0),
            percentage: 0,
            speed: 0,
            eta: null
        });
    }

    updateProgress(downloadId, downloadedSize) {
        const download = this.downloads.get(downloadId);
        if (!download) return null;

        const now = Date.now();
        const elapsed = (now - download.startTime) / 1000; // 秒
        const speed = downloadedSize / elapsed; // 字节/秒
        const remaining = download.totalSize - downloadedSize;
        const eta = speed > 0 ? remaining / speed : null;

        const updated = {
            ...download,
            downloadedSize,
            humanDownloaded: humanSize(downloadedSize),
            percentage: (downloadedSize / download.totalSize) * 100,
            speed: speed,
            humanSpeed: humanSize(speed) + '/s',
            eta: eta,
            humanEta: eta ? this.formatTime(eta) : null
        };

        this.downloads.set(downloadId, updated);
        return updated;
    }

    formatTime(seconds) {
        if (seconds < 60) return `${Math.round(seconds)}秒`;
        if (seconds < 3600) return `${Math.round(seconds / 60)}分钟`;
        return `${Math.round(seconds / 3600)}小时`;
    }

    getDownloadSummary() {
        const downloads = Array.from(this.downloads.values());
        const totalSize = downloads.reduce((sum, d) => sum + d.totalSize, 0);
        const totalDownloaded = downloads.reduce((sum, d) => sum + d.downloadedSize, 0);

        return {
            activeDownloads: downloads.length,
            totalSize: humanSize(totalSize),
            totalDownloaded: humanSize(totalDownloaded),
            overallProgress: totalSize > 0 ? (totalDownloaded / totalSize) * 100 : 0
        };
    }
}

// 4. 系统资源显示
class SystemResourceDisplay {
    constructor() {
        this.resources = {};
    }

    updateMemoryUsage(used, total) {
        this.resources.memory = {
            used: used,
            total: total,
            free: total - used,
            percentage: (used / total) * 100,
            humanUsed: humanSize(used),
            humanTotal: humanSize(total),
            humanFree: humanSize(total - used)
        };
    }

    updateDiskUsage(used, total) {
        this.resources.disk = {
            used: used,
            total: total,
            free: total - used,
            percentage: (used / total) * 100,
            humanUsed: humanSize(used),
            humanTotal: humanSize(total),
            humanFree: humanSize(total - used)
        };
    }

    updateNetworkUsage(sent, received) {
        this.resources.network = {
            sent: sent,
            received: received,
            total: sent + received,
            humanSent: humanSize(sent),
            humanReceived: humanSize(received),
            humanTotal: humanSize(sent + received)
        };
    }

    getResourceSummary() {
        return {
            memory: this.resources.memory,
            disk: this.resources.disk,
            network: this.resources.network,
            alerts: this.getResourceAlerts()
        };
    }

    getResourceAlerts() {
        const alerts = [];

        if (this.resources.memory && this.resources.memory.percentage > 90) {
            alerts.push({
                type: 'memory',
                level: 'critical',
                message: `内存使用率过高: ${this.resources.memory.percentage.toFixed(1)}%`
            });
        }

        if (this.resources.disk && this.resources.disk.percentage > 95) {
            alerts.push({
                type: 'disk',
                level: 'critical',
                message: `磁盘空间不足: ${this.resources.disk.percentage.toFixed(1)}%`
            });
        }

        return alerts;
    }
}
```

## 高级应用模式

### 1. 智能文件大小处理器
```javascript
class SmartFileSizeProcessor {
    constructor() {
        this.sizeCache = new Map();
        this.formatOptions = {
            precision: 2,
            useShortUnits: false,
            locale: 'zh-CN'
        };
    }

    processFileSize(input) {
        // 缓存检查
        if (this.sizeCache.has(input)) {
            return this.sizeCache.get(input);
        }

        let result;

        if (typeof input === 'number') {
            // 数字输入，直接格式化
            result = this.formatNumericSize(input);
        } else if (typeof input === 'string') {
            if (isBinarySize(input)) {
                // 已经是格式化的大小
                result = this.parseAndReformat(input);
            } else {
                // 尝试解析为数字
                const numValue = parseFloat(input);
                if (!isNaN(numValue)) {
                    result = this.formatNumericSize(numValue);
                } else {
                    result = { error: '无效的文件大小格式' };
                }
            }
        } else {
            result = { error: '不支持的输入类型' };
        }

        // 缓存结果
        this.sizeCache.set(input, result);
        return result;
    }

    formatNumericSize(bytes) {
        return {
            bytes: bytes,
            human: humanSize(bytes),
            base64Length: toBase64Length(bytes),
            category: this.getSizeCategory(bytes),
            recommendations: this.getSizeRecommendations(bytes)
        };
    }

    parseAndReformat(sizeString) {
        const [value, unit] = sizeString.split(' ');
        const bytes = this.convertToBytes(parseFloat(value), unit.trim());

        return this.formatNumericSize(bytes);
    }

    convertToBytes(value, unit) {
        const multipliers = {
            'Bytes': 1,
            'Kb': 1024,
            'Mb': 1024 ** 2,
            'Gb': 1024 ** 3,
            'Tb': 1024 ** 4,
            'Pb': 1024 ** 5
        };

        return value * (multipliers[unit] || 1);
    }

    getSizeCategory(bytes) {
        if (bytes < 1024) return 'tiny';
        if (bytes < 1024 ** 2) return 'small';
        if (bytes < 10 * 1024 ** 2) return 'medium';
        if (bytes < 100 * 1024 ** 2) return 'large';
        if (bytes < 1024 ** 3) return 'very_large';
        return 'huge';
    }

    getSizeRecommendations(bytes) {
        const recommendations = [];

        if (bytes > 100 * 1024 ** 2) {
            recommendations.push('考虑压缩文件以减少大小');
        }

        if (bytes > 1024 ** 3) {
            recommendations.push('建议分割为多个较小的文件');
        }

        const base64Size = toBase64Length(bytes);
        if (base64Size > 10 * 1024 ** 2) {
            recommendations.push('Base64编码后会显著增大，考虑其他传输方式');
        }

        return recommendations;
    }
}
```

### 2. 文件大小比较器
```javascript
class FileSizeComparator {
    compare(size1, size2) {
        const bytes1 = this.normalizeToBytes(size1);
        const bytes2 = this.normalizeToBytes(size2);

        return {
            size1: { input: size1, bytes: bytes1, human: humanSize(bytes1) },
            size2: { input: size2, bytes: bytes2, human: humanSize(bytes2) },
            comparison: this.getComparison(bytes1, bytes2),
            difference: Math.abs(bytes1 - bytes2),
            humanDifference: humanSize(Math.abs(bytes1 - bytes2)),
            ratio: bytes2 > 0 ? bytes1 / bytes2 : Infinity
        };
    }

    normalizeToBytes(input) {
        if (typeof input === 'number') {
            return input;
        }

        if (typeof input === 'string' && isBinarySize(input)) {
            const [value, unit] = input.split(' ');
            return this.convertToBytes(parseFloat(value), unit.trim());
        }

        throw new Error(`无法解析文件大小: ${input}`);
    }

    convertToBytes(value, unit) {
        const multipliers = {
            'Bytes': 1,
            'Kb': 1024,
            'Mb': 1024 ** 2,
            'Gb': 1024 ** 3,
            'Tb': 1024 ** 4
        };

        return value * (multipliers[unit] || 1);
    }

    getComparison(bytes1, bytes2) {
        if (bytes1 === bytes2) return 'equal';
        if (bytes1 > bytes2) return 'greater';
        return 'less';
    }

    findLargest(sizes) {
        let largest = null;
        let largestBytes = -1;

        for (const size of sizes) {
            const bytes = this.normalizeToBytes(size);
            if (bytes > largestBytes) {
                largestBytes = bytes;
                largest = size;
            }
        }

        return {
            size: largest,
            bytes: largestBytes,
            human: humanSize(largestBytes)
        };
    }

    sortSizes(sizes, ascending = true) {
        const normalized = sizes.map(size => ({
            original: size,
            bytes: this.normalizeToBytes(size),
            human: humanSize(this.normalizeToBytes(size))
        }));

        normalized.sort((a, b) => ascending ? a.bytes - b.bytes : b.bytes - a.bytes);

        return normalized;
    }
}
```

## 最佳实践

### 1. 性能优化
```javascript
// ✅ 推荐：缓存格式化结果
class OptimizedSizeFormatter {
    constructor() {
        this.cache = new Map();
        this.maxCacheSize = 1000;
    }

    formatSize(bytes) {
        if (this.cache.has(bytes)) {
            return this.cache.get(bytes);
        }

        const formatted = humanSize(bytes);

        // 限制缓存大小
        if (this.cache.size >= this.maxCacheSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }

        this.cache.set(bytes, formatted);
        return formatted;
    }
}

// ✅ 推荐：批量处理
function formatSizesBatch(sizes) {
    return sizes.map(size => ({
        original: size,
        formatted: humanSize(size),
        base64Length: toBase64Length(size)
    }));
}
```

### 2. 错误处理
```javascript
// ✅ 推荐：安全的大小处理
function safeHumanSize(size) {
    try {
        if (typeof size !== 'number' || size < 0 || !isFinite(size)) {
            return 'Invalid size';
        }

        return humanSize(size);
    } catch (error) {
        console.error('文件大小格式化失败:', error);
        return 'Error formatting size';
    }
}

// ✅ 推荐：输入验证
function validateBinarySize(input) {
    if (typeof input !== 'string') {
        return { valid: false, error: '输入必须是字符串' };
    }

    if (!isBinarySize(input)) {
        return { valid: false, error: '无效的二进制大小格式' };
    }

    return { valid: true };
}
```

### 3. 国际化支持
```javascript
// ✅ 推荐：支持多语言
class InternationalizedSizeFormatter {
    constructor(locale = 'zh-CN') {
        this.locale = locale;
        this.units = this.getLocalizedUnits(locale);
    }

    getLocalizedUnits(locale) {
        const unitMaps = {
            'zh-CN': ['字节', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'],
            'en-US': ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'],
            'ja-JP': ['バイト', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
        };

        return unitMaps[locale] || unitMaps['en-US'];
    }

    formatSize(size) {
        let i = 0;
        while (size >= 1024 && i < this.units.length - 1) {
            size /= 1024;
            i++;
        }

        return `${size.toFixed(2)} ${this.units[i]}`;
    }
}
```

## 总结

Odoo 二进制工具模块提供了完整的二进制数据处理功能：

**核心优势**:
- **格式检测**: 精确的二进制大小格式检测
- **编码计算**: 准确的Base64编码长度计算
- **人类可读**: 国际化的文件大小格式化
- **简单高效**: 轻量级实现，性能优异

**适用场景**:
- 文件上传和管理
- 存储空间监控
- 网络传输优化
- 系统资源显示
- 数据格式验证

**设计优势**:
- 国际化支持
- 数学精确性
- 简单易用的API
- 完整的单位范围

这个工具模块为 Odoo Web 客户端提供了可靠的二进制数据处理能力，是文件处理和数据传输的重要基础。

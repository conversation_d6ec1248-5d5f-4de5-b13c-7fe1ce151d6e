# Odoo OWL排序工具 (Sortable OWL Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/sortable_owl.js`  
**原始路径**: `/web/static/src/core/utils/sortable_owl.js`  
**模块类型**: 核心工具模块 - OWL集成排序工具  
**代码行数**: 30 行  
**依赖关系**: 
- `@odoo/owl` - OWL框架 (onWillUnmount, reactive, useEffect, useExternalListener)
- `@web/core/utils/timing` - 时间工具 (useThrottleForAnimation)
- `@web/core/utils/sortable` - 原生排序工具 (useSortable)

## 模块功能

OWL排序工具模块是 Odoo Web 客户端的核心工具库，它是原生排序工具的OWL框架适配器。该模块的主要作用是：
- 将原生排序功能与OWL组件生命周期集成
- 提供响应式状态管理
- 自动处理事件监听器的清理
- 集成OWL的性能优化Hook
- 简化在OWL组件中使用排序功能

这个适配器确保排序功能能够完美地与OWL组件系统协作，提供更好的开发体验和性能表现。

## 核心设计理念

### OWL集成模式
OWL排序工具采用了适配器模式，将原生排序功能包装为OWL兼容的Hook：

```javascript
function useSortable(params) {
    return nativeUseSortable({
        ...params,
        setupHooks: {
            addListener: useExternalListener,
            setup: useEffect,
            teardown: onWillUnmount,
            throttle: useThrottleForAnimation,
            wrapState: reactive,
        },
    });
}
```

### Hook映射关系
| 原生Hook | OWL Hook | 功能描述 |
|---------|----------|----------|
| addListener | useExternalListener | 外部事件监听器管理 |
| setup | useEffect | 副作用设置和更新 |
| teardown | onWillUnmount | 组件卸载时清理 |
| throttle | useThrottleForAnimation | 动画节流优化 |
| wrapState | reactive | 响应式状态包装 |

## 核心Hook详解

### useSortable() - OWL排序Hook
```javascript
function useSortable(params) {
    return nativeUseSortable({
        ...params,
        setupHooks: {
            addListener: useExternalListener,
            setup: useEffect,
            teardown: onWillUnmount,
            throttle: useThrottleForAnimation,
            wrapState: reactive,
        },
    });
}
```

**功能特性**:
- **生命周期集成**: 自动与OWL组件生命周期同步
- **响应式状态**: 使用OWL的响应式系统管理状态
- **自动清理**: 组件卸载时自动清理资源
- **性能优化**: 集成OWL的性能优化Hook
- **事件管理**: 使用OWL的外部事件监听器

**使用示例**:
```javascript
// 在OWL组件中使用排序功能
class SortableListComponent extends Component {
    static template = xml`
        <div t-ref="sortableList" class="sortable-container">
            <div t-foreach="items" t-as="item" t-key="item.id" 
                 class="sortable-item" t-att-data-item-id="item.id">
                <span t-esc="item.name"/>
            </div>
        </div>
    `;
    
    setup() {
        this.listRef = useRef("sortableList");
        this.items = useState([
            { id: 1, name: "项目 1", order: 1 },
            { id: 2, name: "项目 2", order: 2 },
            { id: 3, name: "项目 3", order: 3 }
        ]);
        
        // 使用OWL集成的排序Hook
        this.sortableState = useSortable({
            ref: this.listRef,
            elements: ".sortable-item",
            clone: true,
            placeholderClasses: ["item-placeholder"],
            
            onDragStart: ({ element }) => {
                console.log("开始拖拽:", element.dataset.itemId);
                element.classList.add("dragging");
            },
            
            onDrop: ({ element, previous, next }) => {
                const itemId = parseInt(element.dataset.itemId);
                this.reorderItem(itemId, previous, next);
            },
            
            onDragEnd: ({ element }) => {
                element.classList.remove("dragging");
            }
        });
    }
    
    reorderItem(itemId, previous, next) {
        const item = this.items.find(i => i.id === itemId);
        if (!item) return;
        
        // 计算新的排序位置
        let newOrder;
        if (!previous && !next) {
            newOrder = 1;
        } else if (!previous) {
            const nextItem = this.items.find(i => i.id == next.dataset.itemId);
            newOrder = nextItem ? nextItem.order - 1 : 1;
        } else if (!next) {
            const prevItem = this.items.find(i => i.id == previous.dataset.itemId);
            newOrder = prevItem ? prevItem.order + 1 : this.items.length + 1;
        } else {
            const prevItem = this.items.find(i => i.id == previous.dataset.itemId);
            const nextItem = this.items.find(i => i.id == next.dataset.itemId);
            newOrder = prevItem && nextItem ? (prevItem.order + nextItem.order) / 2 : 1;
        }
        
        // 更新项目排序（响应式更新）
        item.order = newOrder;
        
        // 重新排序所有项目
        this.items.sort((a, b) => a.order - b.order);
        this.items.forEach((item, index) => {
            item.order = index + 1;
        });
    }
}
```

## 实际应用场景

### 1. 响应式任务看板
```javascript
class ResponsiveKanbanBoard extends Component {
    static template = xml`
        <div t-ref="kanbanBoard" class="kanban-board">
            <div t-foreach="columns" t-as="column" t-key="column.id" 
                 class="kanban-column" t-att-data-column-id="column.id">
                <h3 t-esc="column.title"/>
                <div class="task-list">
                    <div t-foreach="getColumnTasks(column.id)" t-as="task" t-key="task.id"
                         class="kanban-task" t-att-data-task-id="task.id">
                        <div class="task-content">
                            <h4 t-esc="task.title"/>
                            <p t-esc="task.description"/>
                            <span class="task-priority" t-att-class="'priority-' + task.priority"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setup() {
        this.boardRef = useRef("kanbanBoard");
        
        // 响应式状态管理
        this.columns = useState([
            { id: 'todo', title: '待办', order: 1 },
            { id: 'doing', title: '进行中', order: 2 },
            { id: 'done', title: '已完成', order: 3 }
        ]);
        
        this.tasks = useState([
            { id: 1, title: '任务1', description: '描述1', column: 'todo', order: 1, priority: 'high' },
            { id: 2, title: '任务2', description: '描述2', column: 'todo', order: 2, priority: 'medium' },
            { id: 3, title: '任务3', description: '描述3', column: 'doing', order: 1, priority: 'low' }
        ]);
        
        // 使用OWL排序Hook
        this.sortableState = useSortable({
            ref: this.boardRef,
            elements: ".kanban-task",
            groups: ".kanban-column",
            connectGroups: true,
            clone: true,
            placeholderClasses: ["task-placeholder"],
            followingElementClasses: ["task-following"],
            
            onDragStart: ({ element, group }) => {
                this.draggedTask = {
                    id: parseInt(element.dataset.taskId),
                    sourceColumn: group.dataset.columnId
                };
                
                // 响应式状态更新
                this.isDragging = true;
                this.highlightDropZones();
            },
            
            onGroupEnter: ({ group }) => {
                // 响应式UI更新
                const columnId = group.dataset.columnId;
                const column = this.columns.find(c => c.id === columnId);
                if (column) {
                    column.isDropTarget = true;
                }
            },
            
            onGroupLeave: ({ group }) => {
                const columnId = group.dataset.columnId;
                const column = this.columns.find(c => c.id === columnId);
                if (column) {
                    column.isDropTarget = false;
                }
            },
            
            onDrop: ({ element, group, previous, next }) => {
                const taskId = parseInt(element.dataset.taskId);
                const newColumn = group.dataset.columnId;
                const task = this.tasks.find(t => t.id === taskId);
                
                if (task) {
                    // 响应式状态更新
                    task.column = newColumn;
                    task.order = this.calculateNewOrder(previous, next, newColumn);
                    
                    // 重新排序任务
                    this.reorderColumnTasks(newColumn);
                    
                    // 触发业务逻辑
                    this.onTaskMoved(task, this.draggedTask.sourceColumn, newColumn);
                }
            },
            
            onDragEnd: () => {
                // 清理响应式状态
                this.isDragging = false;
                this.clearDropZoneHighlights();
                this.draggedTask = null;
            }
        });
        
        // 响应式计算属性
        this.totalTasks = computed(() => this.tasks.length);
        this.completedTasks = computed(() => 
            this.tasks.filter(t => t.column === 'done').length
        );
        this.progress = computed(() => 
            this.totalTasks > 0 ? (this.completedTasks / this.totalTasks) * 100 : 0
        );
    }
    
    getColumnTasks(columnId) {
        return this.tasks
            .filter(task => task.column === columnId)
            .sort((a, b) => a.order - b.order);
    }
    
    calculateNewOrder(previous, next, columnId) {
        const columnTasks = this.getColumnTasks(columnId);
        
        if (!previous && !next) {
            return 1;
        } else if (!previous) {
            const nextTask = columnTasks.find(t => t.id == next.dataset.taskId);
            return nextTask ? nextTask.order - 1 : 1;
        } else if (!next) {
            const prevTask = columnTasks.find(t => t.id == previous.dataset.taskId);
            return prevTask ? prevTask.order + 1 : columnTasks.length + 1;
        } else {
            const prevTask = columnTasks.find(t => t.id == previous.dataset.taskId);
            const nextTask = columnTasks.find(t => t.id == next.dataset.taskId);
            return prevTask && nextTask ? (prevTask.order + nextTask.order) / 2 : 1;
        }
    }
    
    reorderColumnTasks(columnId) {
        const columnTasks = this.getColumnTasks(columnId);
        columnTasks.forEach((task, index) => {
            task.order = index + 1;
        });
    }
    
    highlightDropZones() {
        this.columns.forEach(column => {
            column.canDrop = this.canDropInColumn(column.id);
        });
    }
    
    clearDropZoneHighlights() {
        this.columns.forEach(column => {
            column.isDropTarget = false;
            column.canDrop = false;
        });
    }
    
    canDropInColumn(columnId) {
        // 业务规则检查
        if (!this.draggedTask) return false;
        
        const task = this.tasks.find(t => t.id === this.draggedTask.id);
        if (!task) return false;
        
        // 例如：高优先级任务不能直接到已完成
        if (task.priority === 'high' && columnId === 'done') {
            return false;
        }
        
        return true;
    }
    
    async onTaskMoved(task, fromColumn, toColumn) {
        try {
            // 调用后端API更新任务状态
            await this.rpc('/web/dataset/call_kw', {
                model: 'project.task',
                method: 'write',
                args: [[task.id], { 
                    stage_id: this.getStageId(toColumn),
                    sequence: task.order 
                }],
                kwargs: {}
            });
            
            console.log(`任务 ${task.title} 从 ${fromColumn} 移动到 ${toColumn}`);
        } catch (error) {
            console.error('更新任务失败:', error);
            
            // 回滚状态
            task.column = fromColumn;
            this.reorderColumnTasks(fromColumn);
        }
    }
    
    getStageId(columnId) {
        const stageMap = {
            'todo': 1,
            'doing': 2,
            'done': 3
        };
        return stageMap[columnId];
    }
}
```

### 2. 动态表格排序
```javascript
class DynamicSortableTable extends Component {
    static template = xml`
        <div class="sortable-table-container">
            <table t-ref="sortableTable" class="table">
                <thead>
                    <tr>
                        <th>拖拽</th>
                        <th>姓名</th>
                        <th>年龄</th>
                        <th>部门</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr t-foreach="sortedRows" t-as="row" t-key="row.id"
                        class="table-row" t-att-data-row-id="row.id"
                        t-att-class="{ 'row-dragging': row.isDragging }">
                        <td class="drag-handle">⋮⋮</td>
                        <td t-esc="row.name"/>
                        <td t-esc="row.age"/>
                        <td t-esc="row.department"/>
                        <td>
                            <button class="btn btn-sm" t-on-click="() => this.editRow(row)">编辑</button>
                            <button class="btn btn-sm btn-danger" t-on-click="() => this.deleteRow(row)">删除</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    `;
    
    setup() {
        this.tableRef = useRef("sortableTable");
        
        // 响应式数据
        this.rows = useState([
            { id: 1, name: "张三", age: 25, department: "开发部", order: 1, isDragging: false },
            { id: 2, name: "李四", age: 30, department: "设计部", order: 2, isDragging: false },
            { id: 3, name: "王五", age: 28, department: "测试部", order: 3, isDragging: false }
        ]);
        
        // 计算属性：排序后的行
        this.sortedRows = computed(() => 
            [...this.rows].sort((a, b) => a.order - b.order)
        );
        
        // OWL排序Hook
        this.sortableState = useSortable({
            ref: this.tableRef,
            elements: "tbody tr",
            handle: ".drag-handle",
            clone: false,
            placeholderClasses: ["row-placeholder"],
            applyChangeOnDrop: true,
            
            onDragStart: ({ element }) => {
                const rowId = parseInt(element.dataset.rowId);
                const row = this.rows.find(r => r.id === rowId);
                if (row) {
                    row.isDragging = true; // 响应式状态更新
                }
            },
            
            onElementEnter: ({ element }) => {
                element.classList.add("drop-target");
            },
            
            onElementLeave: ({ element }) => {
                element.classList.remove("drop-target");
            },
            
            onDrop: ({ element, previous, next }) => {
                const rowId = parseInt(element.dataset.rowId);
                const row = this.rows.find(r => r.id === rowId);
                
                if (row) {
                    // 计算新的排序位置
                    const newOrder = this.calculateNewOrder(previous, next);
                    row.order = newOrder;
                    
                    // 重新排序所有行
                    this.reorderAllRows();
                    
                    // 保存到服务器
                    this.saveRowOrder();
                }
            },
            
            onDragEnd: ({ element }) => {
                const rowId = parseInt(element.dataset.rowId);
                const row = this.rows.find(r => r.id === rowId);
                if (row) {
                    row.isDragging = false; // 响应式状态更新
                }
                
                // 清除所有拖拽样式
                this.tableRef.el.querySelectorAll('.drop-target')
                    .forEach(el => el.classList.remove('drop-target'));
            }
        });
        
        // 监听数据变化
        useEffect(() => {
            console.log('表格数据已更新:', this.sortedRows.length, '行');
        }, () => [this.sortedRows]);
    }
    
    calculateNewOrder(previous, next) {
        if (!previous && !next) {
            return 1;
        } else if (!previous) {
            const nextRow = this.rows.find(r => r.id == next.dataset.rowId);
            return nextRow ? nextRow.order - 1 : 1;
        } else if (!next) {
            const prevRow = this.rows.find(r => r.id == previous.dataset.rowId);
            return prevRow ? prevRow.order + 1 : this.rows.length + 1;
        } else {
            const prevRow = this.rows.find(r => r.id == previous.dataset.rowId);
            const nextRow = this.rows.find(r => r.id == next.dataset.rowId);
            return prevRow && nextRow ? (prevRow.order + nextRow.order) / 2 : 1;
        }
    }
    
    reorderAllRows() {
        const sortedRows = [...this.rows].sort((a, b) => a.order - b.order);
        sortedRows.forEach((row, index) => {
            row.order = index + 1;
        });
    }
    
    async saveRowOrder() {
        const orderData = this.rows.map(row => ({
            id: row.id,
            order: row.order
        }));
        
        try {
            await this.rpc('/web/dataset/resequence', {
                model: 'employee',
                ids: orderData.map(item => item.id),
                orders: orderData.map(item => item.order)
            });
            
            console.log('行排序已保存到服务器');
        } catch (error) {
            console.error('保存排序失败:', error);
        }
    }
    
    editRow(row) {
        // 编辑行逻辑
        console.log('编辑行:', row);
    }
    
    deleteRow(row) {
        // 删除行逻辑
        const index = this.rows.findIndex(r => r.id === row.id);
        if (index > -1) {
            this.rows.splice(index, 1);
            this.reorderAllRows();
        }
    }
}
```

## OWL集成优势

### 1. 生命周期管理
```javascript
// ✅ 自动生命周期管理
class AutoManagedSortable extends Component {
    setup() {
        // OWL自动处理组件生命周期
        this.sortableState = useSortable({
            ref: this.listRef,
            elements: ".item",
            // 无需手动清理，OWL会自动处理
        });

        // 组件卸载时自动清理所有资源
        // onWillUnmount 已经集成在 useSortable 中
    }
}

// ❌ 原生方式需要手动管理
class ManualManagedSortable extends Component {
    setup() {
        this.sortableState = nativeUseSortable({
            ref: this.listRef,
            elements: ".item",
        });

        // 需要手动清理
        onWillUnmount(() => {
            this.sortableState.destroy();
        });
    }
}
```

### 2. 响应式状态
```javascript
// ✅ OWL响应式状态
class ReactiveStateSortable extends Component {
    setup() {
        // 响应式状态自动触发重渲染
        this.items = useState([]);

        this.sortableState = useSortable({
            ref: this.listRef,
            elements: ".item",

            onDrop: ({ element, previous, next }) => {
                // 直接修改响应式状态
                const item = this.items.find(i => i.id == element.dataset.itemId);
                item.order = this.calculateNewOrder(previous, next);
                // UI自动更新
            }
        });
    }
}

// ❌ 原生方式需要手动更新UI
class ManualUpdateSortable extends Component {
    setup() {
        this.items = [];

        this.sortableState = nativeUseSortable({
            ref: this.listRef,
            elements: ".item",

            onDrop: ({ element, previous, next }) => {
                const item = this.items.find(i => i.id == element.dataset.itemId);
                item.order = this.calculateNewOrder(previous, next);

                // 需要手动触发重渲染
                this.render();
            }
        });
    }
}
```

### 3. 性能优化
```javascript
// ✅ OWL性能优化
class OptimizedSortable extends Component {
    setup() {
        this.sortableState = useSortable({
            ref: this.listRef,
            elements: ".item",

            // 自动使用 useThrottleForAnimation
            onDrag: ({ element, clientX, clientY }) => {
                // 已经被节流优化
                this.updateDragPosition(element, clientX, clientY);
            }
        });
    }
}

// ❌ 原生方式需要手动优化
class ManualOptimizedSortable extends Component {
    setup() {
        this.sortableState = nativeUseSortable({
            ref: this.listRef,
            elements: ".item",

            onDrag: throttle(({ element, clientX, clientY }) => {
                this.updateDragPosition(element, clientX, clientY);
            }, 16) // 手动节流
        });
    }
}
```

## 最佳实践

### 1. 响应式状态管理
```javascript
// ✅ 推荐：使用响应式状态
class BestPracticeSortable extends Component {
    setup() {
        // 使用 useState 创建响应式状态
        this.items = useState([]);

        // 使用 computed 创建计算属性
        this.sortedItems = computed(() =>
            [...this.items].sort((a, b) => a.order - b.order)
        );

        this.sortableState = useSortable({
            ref: this.listRef,
            elements: ".item",

            onDrop: ({ element, previous, next }) => {
                // 直接修改响应式状态
                const item = this.items.find(i => i.id == element.dataset.itemId);
                if (item) {
                    item.order = this.calculateNewOrder(previous, next);
                    this.reorderItems();
                }
            }
        });
    }
}
```

### 2. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
class SafeSortable extends Component {
    setup() {
        this.sortableState = useSortable({
            ref: this.listRef,
            elements: ".item",

            onDrop: ({ element, previous, next }) => {
                try {
                    const result = this.validateAndUpdateOrder(element, previous, next);
                    if (!result.success) {
                        this.showError(result.message);
                        this.revertDragOperation(element);
                    }
                } catch (error) {
                    console.error('排序操作失败:', error);
                    this.revertDragOperation(element);
                }
            }
        });
    }

    validateAndUpdateOrder(element, previous, next) {
        // 验证排序规则
        const validation = this.validateSortRules(element, previous, next);
        if (!validation.valid) {
            return { success: false, message: validation.message };
        }

        // 更新排序
        this.updateItemOrder(element, previous, next);
        return { success: true };
    }

    revertDragOperation(element) {
        // 恢复到原始位置
        const originalPosition = element.dataset.originalPosition;
        if (originalPosition) {
            this.moveElementToPosition(element, originalPosition);
        }
    }
}
```

### 3. 内存管理
```javascript
// ✅ 推荐：正确的内存管理
class MemoryEfficientSortable extends Component {
    setup() {
        this.sortableState = useSortable({
            ref: this.listRef,
            elements: ".item",
            // OWL会自动清理资源
        });

        // 大数据集的优化
        this.virtualizedItems = computed(() => {
            const startIndex = Math.floor(this.scrollTop / this.itemHeight);
            const endIndex = startIndex + this.visibleCount;
            return this.items.slice(startIndex, endIndex);
        });

        // 清理大对象引用
        onWillUnmount(() => {
            this.items = null;
            this.virtualizedItems = null;
        });
    }
}
```

## 总结

Odoo OWL排序工具模块提供了与OWL框架完美集成的排序功能：

**核心优势**:
- **生命周期集成**: 自动与OWL组件生命周期同步
- **响应式状态**: 使用OWL的响应式系统管理状态
- **性能优化**: 集成OWL的性能优化Hook
- **自动清理**: 组件卸载时自动清理资源
- **开发体验**: 简化在OWL组件中使用排序功能

**适用场景**:
- OWL组件中的排序功能
- 响应式数据的排序操作
- 需要生命周期管理的排序
- 高性能要求的排序应用

**设计优势**:
- 适配器模式设计
- 完整的OWL集成
- 零配置的生命周期管理
- 自动的性能优化

这个OWL排序工具为 Odoo Web 客户端提供了与OWL框架完美集成的排序能力，是构建现代响应式用户界面的重要工具。

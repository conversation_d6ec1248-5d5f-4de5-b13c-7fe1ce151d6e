# Odoo 字符串工具 (Strings Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/strings.js`  
**原始路径**: `/web/static/src/core/utils/strings.js`  
**模块类型**: 核心工具模块 - 字符串处理工具  
**代码行数**: 363 行  
**依赖关系**: 无外部依赖

## 模块功能

字符串工具模块是 Odoo Web 客户端的核心工具库，提供了丰富的字符串处理功能，包括：
- HTML 转义和安全处理
- 正则表达式转义
- 字符串格式化和插值
- 字符串分割和重组
- 字符串验证和检测
- 国际化字符处理（去音标）
- 布尔值解析

## 核心常量

### nbsp - 不间断空格
```javascript
const nbsp = "\u00a0";
```

**用途**: Unicode 不间断空格字符，用于防止文本换行。

## 核心函数详解

### 1. escape() - HTML 转义
```javascript
function escape(str) {
    if (str === undefined) {
        return "";
    }
    if (typeof str === "number") {
        return String(str);
    }
    [
        ["&", "&amp;"],
        ["<", "&lt;"],
        [">", "&gt;"],
        ["'", "&#x27;"],
        ['"', "&quot;"],
        ["`", "&#x60;"],
    ].forEach((pairs) => {
        str = String(str).replaceAll(pairs[0], pairs[1]);
    });
    return str;
}
```

**功能特性**:
- **XSS 防护**: 防止跨站脚本攻击
- **HTML 安全**: 转义所有危险的 HTML 字符
- **类型处理**: 自动处理 undefined 和数字类型
- **完整转义**: 包括引号和反引号

**使用示例**:
```javascript
// 基本 HTML 转义
escape("<script>alert('xss')</script>");
// "&lt;script&gt;alert(&#x27;xss&#x27;)&lt;/script&gt;"

escape('Hello "World" & <Friends>');
// "Hello &quot;World&quot; &amp; &lt;Friends&gt;"

// 处理特殊值
escape(undefined);  // ""
escape(42);         // "42"
escape(null);       // "null"

// 模板字符串转义
escape(`Template with "quotes" and 'apostrophes'`);
// "Template with &quot;quotes&quot; and &#x27;apostrophes&#x27;"
```

**实际应用场景**:
```javascript
// 1. 用户输入安全显示
function displayUserComment(comment) {
    const safeComment = escape(comment);
    document.getElementById('comment').innerHTML = safeComment;
}

// 2. 动态 HTML 生成
function createUserCard(user) {
    return `
        <div class="user-card">
            <h3>${escape(user.name)}</h3>
            <p>${escape(user.bio)}</p>
            <span title="${escape(user.tooltip)}">${escape(user.status)}</span>
        </div>
    `;
}

// 3. 表单数据处理
class FormRenderer {
    renderField(field) {
        return `
            <label for="${escape(field.id)}">${escape(field.label)}</label>
            <input id="${escape(field.id)}" 
                   value="${escape(field.value)}" 
                   placeholder="${escape(field.placeholder)}">
        `;
    }
}
```

### 2. escapeRegExp() - 正则表达式转义
```javascript
function escapeRegExp(str) {
    return str.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
}
```

**功能特性**:
- **正则安全**: 转义所有正则表达式特殊字符
- **字面匹配**: 确保字符串被当作字面值处理
- **搜索安全**: 用于用户输入的搜索功能

**使用示例**:
```javascript
// 转义正则表达式特殊字符
escapeRegExp("Hello (world)");     // "Hello \\(world\\)"
escapeRegExp("Price: $10.99");     // "Price: \\$10\\.99"
escapeRegExp("[Important]");       // "\\[Important\\]"
escapeRegExp("C++ & C#");          // "C\\+\\+ & C#"

// 用于搜索功能
function createSearchRegex(userInput) {
    const escaped = escapeRegExp(userInput);
    return new RegExp(escaped, 'gi');
}

// 安全的字符串替换
function safeReplace(text, searchTerm, replacement) {
    const escapedSearch = escapeRegExp(searchTerm);
    const regex = new RegExp(escapedSearch, 'g');
    return text.replace(regex, replacement);
}
```

**实际应用场景**:
```javascript
// 1. 搜索高亮功能
class SearchHighlighter {
    highlight(text, searchTerm) {
        if (!searchTerm) return text;
        
        const escaped = escapeRegExp(searchTerm);
        const regex = new RegExp(`(${escaped})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }
}

// 2. 过滤器实现
class DataFilter {
    filterByText(items, searchText) {
        if (!searchText) return items;
        
        const escaped = escapeRegExp(searchText);
        const regex = new RegExp(escaped, 'i');
        
        return items.filter(item => 
            regex.test(item.name) || regex.test(item.description)
        );
    }
}

// 3. 模板字符串安全替换
class TemplateProcessor {
    processTemplate(template, variables) {
        let result = template;
        
        Object.entries(variables).forEach(([key, value]) => {
            const placeholder = escapeRegExp(`{{${key}}}`);
            const regex = new RegExp(placeholder, 'g');
            result = result.replace(regex, escape(value));
        });
        
        return result;
    }
}
```

### 3. intersperse() - 字符串插入分隔符
```javascript
function intersperse(str, indices, separator = "") {
    separator = separator || "";
    const result = [];
    let last = str.length;
    for (let i = 0; i < indices.length; ++i) {
        let section = indices[i];
        if (section === -1 || last <= 0) {
            break;
        } else if (section === 0 && i === 0) {
            break;
        } else if (section === 0) {
            section = indices[--i];
        }
        result.push(str.substring(last - section, last));
        last -= section;
    }
    const s = str.substring(0, last);
    if (s) {
        result.push(s);
    }
    return result.reverse().join(separator);
}
```

**功能特性**:
- **灵活分割**: 根据指定位置插入分隔符
- **模式重复**: 支持重复模式（0 值）
- **提前终止**: 支持提前终止（-1 值）
- **从右到左**: 从字符串末尾开始处理

**使用示例**:
```javascript
// 数字格式化（千分位）
intersperse("1234567", [3, 0], ",");  // "1,234,567"
intersperse("9876543210", [3, 0], ","); // "9,876,543,210"

// 电话号码格式化
intersperse("1234567890", [4, 3, 3], "-"); // "123-456-7890"
intersperse("15551234567", [4, 3, 3, 1], "-"); // "1-555-123-4567"

// 信用卡号格式化
intersperse("1234567890123456", [4, 0], " "); // "1234 5678 9012 3456"

// 自定义分割模式
intersperse("abcdefghijk", [2, 3, 0], "-"); // "ab-cde-fgh-ijk"

// 提前终止
intersperse("1234567890", [3, -1], ","); // "1234567,890"
```

**实际应用场景**:
```javascript
// 1. 数字格式化工具
class NumberFormatter {
    formatCurrency(amount, currency = "USD") {
        const formatted = intersperse(String(Math.floor(amount)), [3, 0], ",");
        const decimal = (amount % 1).toFixed(2).slice(2);
        return `${currency} ${formatted}.${decimal}`;
    }
    
    formatFileSize(bytes) {
        const str = String(bytes);
        return intersperse(str, [3, 0], ",") + " bytes";
    }
}

// 2. 身份证号格式化
class IDFormatter {
    formatSSN(ssn) {
        // 美国社会安全号：XXX-XX-XXXX
        return intersperse(ssn, [4, 2, 3], "-");
    }
    
    formatCreditCard(cardNumber) {
        // 信用卡号：XXXX XXXX XXXX XXXX
        return intersperse(cardNumber, [4, 0], " ");
    }
}

// 3. 代码格式化
class CodeFormatter {
    formatHexColor(hex) {
        // 将长十六进制颜色分组
        if (hex.length === 6) {
            return intersperse(hex, [2, 0], " ");
        }
        return hex;
    }
}
```

### 4. sprintf() - 字符串格式化
```javascript
function sprintf(s, ...values) {
    if (values.length === 1 && Object.prototype.toString.call(values[0]) === "[object Object]") {
        const valuesDict = values[0];
        s = s.replace(/%\(([^)]+)\)s/g, (match, value) => valuesDict[value]);
    } else if (values.length > 0) {
        s = s.replace(/%s/g, () => values.shift());
    }
    return s;
}
```

**功能特性**:
- **位置参数**: 支持 %s 占位符
- **命名参数**: 支持 %(name)s 占位符
- **灵活格式**: 两种格式化方式
- **类型兼容**: 兼容多种数据类型

**使用示例**:
```javascript
// 位置参数格式化
sprintf("Hello %s, you have %s messages", "Alice", 5);
// "Hello Alice, you have 5 messages"

sprintf("Today is %s %s, %s", "March", 15, 2024);
// "Today is March 15, 2024"

// 命名参数格式化
sprintf("Hello %(name)s, you are %(age)s years old", {
    name: "Bob",
    age: 30
});
// "Hello Bob, you are 30 years old"

sprintf("%(product)s costs %(price)s %(currency)s", {
    product: "Laptop",
    price: 999,
    currency: "USD"
});
// "Laptop costs 999 USD"

// 混合使用（但不推荐）
sprintf("User: %(name)s", { name: "Charlie" });
// "User: Charlie"
```

**实际应用场景**:
```javascript
// 1. 国际化消息格式化
class I18nFormatter {
    formatMessage(template, params) {
        return sprintf(template, params);
    }
    
    // 使用示例
    getWelcomeMessage(user) {
        const template = "Welcome %(name)s! You have %(count)s notifications.";
        return this.formatMessage(template, {
            name: user.name,
            count: user.notifications.length
        });
    }
}

// 2. 日志格式化
class Logger {
    log(level, message, ...args) {
        const timestamp = new Date().toISOString();
        const formatted = sprintf(message, ...args);
        console.log(`[${timestamp}] ${level}: ${formatted}`);
    }
    
    info(message, ...args) {
        this.log("INFO", message, ...args);
    }
    
    error(message, ...args) {
        this.log("ERROR", message, ...args);
    }
}

// 使用示例
const logger = new Logger();
logger.info("User %s logged in from %s", "alice", "***********");
logger.error("Failed to save %s: %s", "document.pdf", "Permission denied");

// 3. SQL 查询构建器
class QueryBuilder {
    select(table, conditions = {}) {
        if (Object.keys(conditions).length === 0) {
            return `SELECT * FROM ${table}`;
        }
        
        const whereClause = Object.keys(conditions)
            .map(key => `${key} = %(${key})s`)
            .join(" AND ");
            
        const query = `SELECT * FROM ${table} WHERE ${whereClause}`;
        return sprintf(query, conditions);
    }
}

// 使用示例
const qb = new QueryBuilder();
const query = qb.select("users", { status: "active", role: "admin" });
// "SELECT * FROM users WHERE status = active AND role = admin"
```

### 5. capitalize() - 字符串首字母大写
```javascript
function capitalize(s) {
    return s ? s[0].toUpperCase() + s.slice(1) : "";
}
```

**功能特性**:
- **首字母大写**: 只将第一个字符转为大写
- **保持其余**: 保持其余字符不变
- **安全处理**: 处理空字符串和 falsy 值

**使用示例**:
```javascript
// 基本首字母大写
capitalize("hello world");     // "Hello world"
capitalize("javaScript");      // "JavaScript"
capitalize("HTML");            // "HTML"

// 处理特殊情况
capitalize("");                // ""
capitalize(null);              // ""
capitalize(undefined);         // ""

// 实际应用
const names = ["alice", "bob", "charlie"];
const capitalizedNames = names.map(capitalize);
// ["Alice", "Bob", "Charlie"]
```

**实际应用场景**:
```javascript
// 1. 用户名格式化
class UserFormatter {
    formatDisplayName(firstName, lastName) {
        return `${capitalize(firstName)} ${capitalize(lastName)}`;
    }
    
    formatUsername(username) {
        return capitalize(username.toLowerCase());
    }
}

// 2. 表单字段标签
class FormLabelGenerator {
    generateLabel(fieldName) {
        // 将 camelCase 转换为标签
        const words = fieldName.replace(/([A-Z])/g, ' $1').toLowerCase();
        return capitalize(words.trim());
    }
}

// 使用示例
const generator = new FormLabelGenerator();
generator.generateLabel("firstName");    // "First name"
generator.generateLabel("emailAddress"); // "Email address"

// 3. 菜单项格式化
class MenuFormatter {
    formatMenuItem(item) {
        return {
            ...item,
            label: capitalize(item.label),
            description: capitalize(item.description)
        };
    }
}
```

### 6. unaccent() - 去除音标字符
```javascript
function unaccent(str, caseSensitive) {
    str = str.replace(/[^\u0000-\u007E]/g, function (accented) {
        return diacriticsMap[accented] || accented;
    });
    return caseSensitive ? str : str.toLowerCase();
}
```

**功能特性**:
- **音标移除**: 将带音标的字符转换为基本 ASCII 字符
- **国际化支持**: 支持多种语言的音标字符
- **大小写控制**: 可选择是否保持大小写
- **完整映射**: 包含数百个字符的映射表

**使用示例**:
```javascript
// 基本去音标
unaccent("café");           // "cafe"
unaccent("naïve");          // "naive"
unaccent("résumé");         // "resume"
unaccent("Zürich");         // "zurich"

// 保持大小写
unaccent("Café", true);     // "Cafe"
unaccent("ZÜRICH", true);   // "ZURICH"

// 多语言支持
unaccent("Ñoño");           // "nono" (西班牙语)

// 复杂文本处理
unaccent("Crème brûlée à la française");
// "creme brulee a la francaise"
```

**实际应用场景**:
```javascript
// 1. 搜索功能优化
class SearchEngine {
    normalizeSearchTerm(term) {
        return unaccent(term.trim());
    }

    search(items, searchTerm) {
        const normalizedTerm = this.normalizeSearchTerm(searchTerm);

        return items.filter(item => {
            const normalizedName = unaccent(item.name);
            const normalizedDesc = unaccent(item.description);

            return normalizedName.includes(normalizedTerm) ||
                   normalizedDesc.includes(normalizedTerm);
        });
    }
}

// 2. URL 友好化
class URLGenerator {
    createSlug(title) {
        return unaccent(title)
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, '');
    }
}

// 使用示例
const urlGen = new URLGenerator();
urlGen.createSlug("Café & Restaurant Français");
// "cafe-restaurant-francais"
```

### 7. isEmail() - 邮箱验证
```javascript
function isEmail(value) {
    const re = /^(([^<>()\[\]\.,;:\s@\"]+(\.[^<>()\[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i;
    return re.test(value);
}
```

**功能特性**:
- **标准验证**: 基于常用的邮箱验证正则表达式
- **格式检查**: 检查邮箱的基本格式要求
- **大小写不敏感**: 忽略大小写差异
- **快速验证**: 高性能的客户端验证

**使用示例**:
```javascript
// 有效邮箱
isEmail("<EMAIL>");        // true
isEmail("<EMAIL>");   // true
isEmail("<EMAIL>");  // true

// 无效邮箱
isEmail("invalid-email");           // false
isEmail("user@");                   // false
isEmail("@domain.com");             // false
```

### 8. isNumeric() - 数字字符串验证
```javascript
function isNumeric(value) {
    return Boolean(value?.match(/^\d+$/));
}
```

**功能特性**:
- **纯数字检查**: 只接受纯数字字符串
- **严格验证**: 不接受小数点、负号等
- **安全检查**: 处理 null/undefined 值

**使用示例**:
```javascript
// 有效数字字符串
isNumeric("123");        // true
isNumeric("0");          // true

// 无效输入
isNumeric("123.45");     // false (包含小数点)
isNumeric("-123");       // false (包含负号)
isNumeric("12a3");       // false (包含字母)
```

### 9. exprToBoolean() - 表达式转布尔值
```javascript
function exprToBoolean(str, trueIfEmpty = false) {
    return str ? !/^false|0$/i.test(str) : trueIfEmpty;
}
```

**功能特性**:
- **字符串解析**: 将字符串表达式转换为布尔值
- **假值识别**: "false" 和 "0" 被识别为 false
- **空值处理**: 可配置空值的默认行为

**使用示例**:
```javascript
// 真值情况
exprToBoolean("true");       // true
exprToBoolean("1");          // true
exprToBoolean("yes");        // true

// 假值情况
exprToBoolean("false");      // false
exprToBoolean("0");          // false

// 空值处理
exprToBoolean("");           // false (默认)
exprToBoolean("", true);     // true (指定为真)
```

## 最佳实践

### 1. 安全性优先
```javascript
// ✅ 推荐：始终转义用户输入
function displayUserContent(content) {
    return escape(content);
}

// ❌ 避免：直接使用用户输入
function dangerousDisplay(content) {
    return content; // 可能导致 XSS 攻击
}
```

### 2. 搜索功能优化
```javascript
// ✅ 推荐：组合使用多个字符串工具
class SmartSearch {
    search(items, query) {
        const normalizedQuery = unaccent(query.toLowerCase());
        const escapedQuery = escapeRegExp(normalizedQuery);
        const regex = new RegExp(escapedQuery, 'i');

        return items.filter(item => {
            const normalizedText = unaccent(item.text.toLowerCase());
            return regex.test(normalizedText);
        });
    }
}
```

## 总结

Odoo 字符串工具模块提供了完整的字符串处理功能：

**核心优势**:
- **安全性**: HTML 转义和正则表达式转义防止安全漏洞
- **国际化**: 完整的音标字符处理支持
- **验证功能**: 邮箱、数字、布尔值的可靠验证
- **格式化**: 灵活的字符串格式化和分割功能

**适用场景**:
- 用户输入安全处理
- 搜索和过滤功能
- 表单验证和数据处理
- 国际化文本处理
- URL 和配置解析

这个工具模块为 Odoo Web 客户端提供了强大的字符串处理能力，是构建安全、国际化Web应用的重要基础。

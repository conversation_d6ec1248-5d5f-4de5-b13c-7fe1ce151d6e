# Odoo 数组工具函数 (Arrays Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/arrays.js`  
**原始路径**: `/web/static/src/core/utils/arrays.js`  
**模块类型**: 核心工具模块 - 数组处理工具  
**代码行数**: 280 行  
**依赖关系**: 
- `@web/core/utils/objects` - 对象工具 (shallowEqual)

## 模块功能

数组工具模块是 Odoo Web 客户端的核心工具库，提供了丰富的数组操作函数，包括：
- 数组转换和确保函数
- 集合运算（交集、对称差集等）
- 分组和排序功能
- 笛卡尔积和组合运算
- 数组切片和窗口操作
- 实用的数组处理工具

## 核心类型定义

### Criterion 类型
```typescript
/**
 * @template T
 * @template {string | number | symbol} K
 * @typedef {keyof T | ((item: T) => K)} Criterion
 */
```

**用途**: 定义分组和排序的标准，可以是：
- **属性名**: 对象的属性键名
- **函数**: 自定义提取函数

## 核心工具函数详解

### 1. ensureArray() - 数组确保函数
```javascript
function ensureArray(value) {
    return isIterable(value) ? [...value] : [value];
}
```

**功能**: 确保输入值转换为数组
- **可迭代对象**: 转换为数组
- **非可迭代值**: 包装为单元素数组

**使用示例**:
```javascript
ensureArray([1, 2, 3]);        // [1, 2, 3]
ensureArray("hello");          // ["hello"]
ensureArray(42);               // [42]
ensureArray(new Set([1, 2]));  // [1, 2]
```

### 2. isIterable() - 可迭代性检测
```javascript
function isIterable(value) {
    return Boolean(value && typeof value === "object" && value[Symbol.iterator]);
}
```

**功能**: 检测值是否为可迭代对象（排除字符串）
- **检测条件**: 非空对象且具有 Symbol.iterator
- **排除字符串**: 虽然字符串可迭代，但此函数返回 false

**使用示例**:
```javascript
isIterable([1, 2, 3]);         // true
isIterable(new Set());         // true
isIterable(new Map());         // true
isIterable("string");          // false
isIterable(42);                // false
isIterable(null);              // false
```

### 3. unique() - 数组去重
```javascript
function unique(iterable) {
    return [...new Set(iterable)];
}
```

**功能**: 移除数组中的重复元素
- **基于 Set**: 利用 Set 的唯一性特征
- **保持顺序**: 保持首次出现的元素顺序

**使用示例**:
```javascript
unique([1, 2, 2, 3, 1]);       // [1, 2, 3]
unique("hello");               // ['h', 'e', 'l', 'o']
unique([{a: 1}, {a: 1}]);      // [{a: 1}, {a: 1}] (对象引用不同)
```

### 4. intersection() - 数组交集
```javascript
function intersection(iter1, iter2) {
    const set2 = new Set(iter2);
    return unique(iter1).filter((v) => set2.has(v));
}
```

**功能**: 返回两个数组的交集
- **去重**: 自动去除重复元素
- **高效查找**: 使用 Set 提高查找效率

**使用示例**:
```javascript
intersection([1, 2, 3], [2, 3, 4]);     // [2, 3]
intersection("abc", "bcd");              // ['b', 'c']
intersection([1, 1, 2], [2, 2, 3]);     // [2]
```

### 5. symmetricalDifference() - 对称差集
```javascript
function symmetricalDifference(iter1, iter2) {
    const array1 = [...iter1];
    const array2 = [...iter2];
    return [
        ...array1.filter((value) => !array2.includes(value)),
        ...array2.filter((value) => !array1.includes(value)),
    ];
}
```

**功能**: 返回两个数组的对称差集（互不包含的元素）

**使用示例**:
```javascript
symmetricalDifference([1, 2, 3], [3, 4, 5]);  // [1, 2, 4, 5]
symmetricalDifference("abc", "bcd");           // ['a', 'd']
```

### 6. groupBy() - 数组分组
```javascript
function groupBy(iterable, criterion) {
    const extract = _getExtractorFrom(criterion);
    const groups = {};
    for (const element of iterable) {
        const group = String(extract(element));
        if (!(group in groups)) {
            groups[group] = [];
        }
        groups[group].push(element);
    }
    return groups;
}
```

**功能**: 根据指定标准对数组元素进行分组

**使用示例**:
```javascript
// 按属性分组
const users = [
    { name: "Alice", age: 25, department: "IT" },
    { name: "Bob", age: 30, department: "IT" },
    { name: "Charlie", age: 35, department: "HR" }
];

groupBy(users, "department");
// {
//   "IT": [{ name: "Alice", ... }, { name: "Bob", ... }],
//   "HR": [{ name: "Charlie", ... }]
// }

// 按函数分组
groupBy(users, user => user.age >= 30 ? "senior" : "junior");
// {
//   "junior": [{ name: "Alice", ... }],
//   "senior": [{ name: "Bob", ... }, { name: "Charlie", ... }]
// }

// 按数字分组
groupBy([1, 2, 3, 4, 5], x => x % 2);
// { "0": [2, 4], "1": [1, 3, 5] }
```

### 7. sortBy() - 数组排序
```javascript
function sortBy(iterable, criterion, order = "asc") {
    const extract = _getExtractorFrom(criterion);
    return [...iterable].sort((elA, elB) => {
        const a = extract(elA);
        const b = extract(elB);
        let result;
        if (isNaN(a) && isNaN(b)) {
            result = a > b ? 1 : a < b ? -1 : 0;
        } else {
            result = a - b;
        }
        return order === "asc" ? result : -result;
    });
}
```

**功能**: 根据指定标准对数组进行排序
- **智能比较**: 自动处理数字和字符串比较
- **浅拷贝**: 不修改原数组，返回新数组

**使用示例**:
```javascript
const products = [
    { name: "Laptop", price: 1000 },
    { name: "Phone", price: 500 },
    { name: "Tablet", price: 300 }
];

// 按价格升序排序
sortBy(products, "price");
// [{ name: "Tablet", price: 300 }, ...]

// 按价格降序排序
sortBy(products, "price", "desc");
// [{ name: "Laptop", price: 1000 }, ...]

// 按名称排序
sortBy(products, "name");
// [{ name: "Laptop", ... }, { name: "Phone", ... }, { name: "Tablet", ... }]

// 自定义排序函数
sortBy(products, p => p.name.length);
// 按名称长度排序
```

### 8. cartesian() - 笛卡尔积
```javascript
function cartesian(...args) {
    if (args.length === 0) {
        return [undefined];
    } else if (args.length === 1) {
        return args[0];
    } else {
        return _cartesian(...args);
    }
}
```

**功能**: 计算多个数组的笛卡尔积

**使用示例**:
```javascript
cartesian([1, 2], ['a', 'b']);
// [[1, 'a'], [1, 'b'], [2, 'a'], [2, 'b']]

cartesian([1, 2], ['a', 'b'], ['x', 'y']);
// [[1, 'a', 'x'], [1, 'a', 'y'], [1, 'b', 'x'], [1, 'b', 'y'], 
//  [2, 'a', 'x'], [2, 'a', 'y'], [2, 'b', 'x'], [2, 'b', 'y']]

cartesian([1, 2]);  // [1, 2] (单个数组直接返回)
cartesian();        // [undefined] (空参数返回单位元)
```

### 9. sections() - 数组切片
```javascript
function sections(iterable) {
    const array = [...iterable];
    const sections = [];
    for (let i = 0; i < array.length + 1; i++) {
        sections.push(array.slice(0, i));
    }
    return sections;
}
```

**功能**: 返回数组的所有初始段

**使用示例**:
```javascript
sections([1, 2, 3]);
// [[], [1], [1, 2], [1, 2, 3]]

sections("abc");
// [[], ['a'], ['a', 'b'], ['a', 'b', 'c']]
```

### 10. zip() - 数组压缩
```javascript
function zip(iter1, iter2, fill = false) {
    const array1 = [...iter1];
    const array2 = [...iter2];
    const result = [];
    const getLength = fill ? Math.max : Math.min;
    for (let i = 0; i < getLength(array1.length, array2.length); i++) {
        result.push([array1[i], array2[i]]);
    }
    return result;
}
```

**功能**: 将两个数组按索引配对

**使用示例**:
```javascript
zip([1, 2, 3], ['a', 'b', 'c']);
// [[1, 'a'], [2, 'b'], [3, 'c']]

zip([1, 2], ['a', 'b', 'c']);
// [[1, 'a'], [2, 'b']] (默认取最短长度)

zip([1, 2], ['a', 'b', 'c'], true);
// [[1, 'a'], [2, 'b'], [undefined, 'c']] (fill=true 取最长长度)
```

### 11. zipWith() - 带映射的压缩
```javascript
function zipWith(iter1, iter2, mapFn) {
    return zip(iter1, iter2).map(([e1, e2]) => mapFn(e1, e2));
}
```

**功能**: 压缩两个数组并应用映射函数

**使用示例**:
```javascript
zipWith([1, 2, 3], [4, 5, 6], (a, b) => a + b);
// [5, 7, 9]

zipWith(['a', 'b'], [1, 2], (str, num) => str.repeat(num));
// ['a', 'bb']
```

### 12. slidingWindow() - 滑动窗口
```javascript
function slidingWindow(arr, width) {
    const res = [];
    for (let i = 0; i <= arr.length - width; i++) {
        res.push(arr.slice(i, i + width));
    }
    return res;
}
```

**功能**: 创建指定宽度的滑动窗口

**使用示例**:
```javascript
slidingWindow([1, 2, 3, 4, 5], 3);
// [[1, 2, 3], [2, 3, 4], [3, 4, 5]]

slidingWindow("hello", 2);
// [['h', 'e'], ['e', 'l'], ['l', 'l'], ['l', 'o']]
```

### 13. rotate() - 数组旋转
```javascript
function rotate(i, arr, inc = 1) {
    return (arr.length + i + inc) % arr.length;
}
```

**功能**: 计算数组旋转后的索引

**使用示例**:
```javascript
const arr = ['a', 'b', 'c', 'd'];
rotate(0, arr, 1);  // 1 (索引0向右旋转1位到索引1)
rotate(3, arr, 1);  // 0 (索引3向右旋转1位到索引0，循环)
rotate(1, arr, -1); // 0 (索引1向左旋转1位到索引0)
```

## 辅助函数分析

### _getExtractorFrom() - 提取器工厂
```javascript
function _getExtractorFrom(criterion) {
    if (criterion) {
        switch (typeof criterion) {
            case "string":
                return (element) => element[criterion];
            case "function":
                return criterion;
            default:
                throw new Error(`Expected criterion of type 'string' or 'function'`);
        }
    } else {
        return (element) => element;
    }
}
```

**功能**: 根据标准类型创建相应的提取函数
- **字符串**: 创建属性访问器
- **函数**: 直接返回函数
- **空值**: 返回恒等函数

### _cartesian() - 内部笛卡尔积实现
```javascript
function _cartesian(...args) {
    if (args.length === 0) {
        return [undefined];
    }
    const firstArray = args.shift().map((elem) => [elem]);
    if (args.length === 0) {
        return firstArray;
    }
    const result = [];
    const productOfOtherArrays = _cartesian(...args);
    for (const array of firstArray) {
        for (const tuple of productOfOtherArrays) {
            result.push([...array, ...tuple]);
        }
    }
    return result;
}
```

**功能**: 递归实现多数组笛卡尔积
- **递归分解**: 将问题分解为更小的子问题
- **结果组合**: 将子结果组合成最终结果

## 实际应用场景

### 1. 数据处理和分析
```javascript
// 用户数据分析
const users = [
    { name: "Alice", age: 25, department: "IT", salary: 5000 },
    { name: "Bob", age: 30, department: "IT", salary: 6000 },
    { name: "Charlie", age: 35, department: "HR", salary: 5500 },
    { name: "Diana", age: 28, department: "HR", salary: 5200 }
];

// 按部门分组
const usersByDept = groupBy(users, "department");
console.log(usersByDept);
// { "IT": [...], "HR": [...] }

// 按薪资排序
const usersBySalary = sortBy(users, "salary", "desc");
console.log(usersBySalary);
// 从高薪到低薪排序

// 按年龄段分组
const usersByAgeGroup = groupBy(users, user => {
    if (user.age < 30) return "young";
    if (user.age < 35) return "middle";
    return "senior";
});
```

### 2. 表单选项组合
```javascript
// 生成产品配置选项
const colors = ["red", "blue", "green"];
const sizes = ["S", "M", "L"];
const materials = ["cotton", "polyester"];

// 生成所有可能的产品变体
const productVariants = cartesian(colors, sizes, materials);
console.log(productVariants);
// [["red", "S", "cotton"], ["red", "S", "polyester"], ...]

// 为每个变体生成SKU
const skus = productVariants.map(([color, size, material]) =>
    `${color.toUpperCase()}-${size}-${material.substring(0, 3).toUpperCase()}`
);
```

### 3. 数据去重和清理
```javascript
// 清理重复的用户ID
const userIds = [1, 2, 2, 3, 1, 4, 5, 3];
const uniqueUserIds = unique(userIds);
console.log(uniqueUserIds); // [1, 2, 3, 4, 5]

// 确保输入始终为数组
function processItems(items) {
    const itemArray = ensureArray(items);
    return itemArray.map(item => processItem(item));
}

processItems(singleItem);     // 处理单个项目
processItems([item1, item2]); // 处理多个项目
```

### 4. 时间序列数据处理
```javascript
// 股价数据滑动窗口分析
const prices = [100, 102, 98, 105, 110, 108, 115];

// 3日移动平均
const movingAverages = slidingWindow(prices, 3)
    .map(window => window.reduce((sum, price) => sum + price, 0) / window.length);

console.log(movingAverages); // [100, 101.67, 101, 107.67, 111, 111]

// 价格变化趋势
const priceChanges = zipWith(prices.slice(0, -1), prices.slice(1),
    (prev, curr) => curr - prev);
console.log(priceChanges); // [2, -4, 7, 5, -2, 7]
```

### 5. 权限和角色管理
```javascript
// 用户权限交集
const userPermissions = ["read", "write", "delete"];
const rolePermissions = ["read", "write", "admin"];

const effectivePermissions = intersection(userPermissions, rolePermissions);
console.log(effectivePermissions); // ["read", "write"]

// 权限差异分析
const permissionDiff = symmetricalDifference(userPermissions, rolePermissions);
console.log(permissionDiff); // ["delete", "admin"]
```

### 6. 搜索和过滤
```javascript
// 多条件搜索结果合并
const searchResults1 = [1, 2, 3, 4];
const searchResults2 = [3, 4, 5, 6];

// 搜索结果交集（同时满足多个条件）
const commonResults = intersection(searchResults1, searchResults2);

// 搜索结果并集（满足任一条件）
const allResults = unique([...searchResults1, ...searchResults2]);
```

## 性能优化技巧

### 1. 大数据集处理
```javascript
// ✅ 推荐：对大数据集使用 Set 进行快速查找
function efficientIntersection(largeArray, smallArray) {
    const smallSet = new Set(smallArray);
    return largeArray.filter(item => smallSet.has(item));
}

// ❌ 避免：在大数据集上使用 includes
function inefficientIntersection(largeArray, smallArray) {
    return largeArray.filter(item => smallArray.includes(item)); // O(n²)
}
```

### 2. 内存优化
```javascript
// ✅ 推荐：使用生成器处理大型笛卡尔积
function* cartesianGenerator(...arrays) {
    if (arrays.length === 0) {
        yield [];
        return;
    }

    const [first, ...rest] = arrays;
    for (const item of first) {
        for (const combination of cartesianGenerator(...rest)) {
            yield [item, ...combination];
        }
    }
}

// 处理大型组合而不占用过多内存
for (const combination of cartesianGenerator(colors, sizes, materials)) {
    processCombination(combination);
}
```

### 3. 排序优化
```javascript
// ✅ 推荐：预计算排序键
function optimizedSortBy(array, criterion) {
    const extract = _getExtractorFrom(criterion);

    // 预计算所有排序键
    const itemsWithKeys = array.map(item => ({
        item,
        key: extract(item)
    }));

    // 排序
    itemsWithKeys.sort((a, b) => {
        if (typeof a.key === 'number' && typeof b.key === 'number') {
            return a.key - b.key;
        }
        return a.key > b.key ? 1 : a.key < b.key ? -1 : 0;
    });

    // 返回排序后的项目
    return itemsWithKeys.map(({ item }) => item);
}
```

## 最佳实践

### 1. 类型安全
```javascript
// ✅ 推荐：使用类型检查
function safeGroupBy(iterable, criterion) {
    if (!isIterable(iterable)) {
        throw new Error("First argument must be iterable");
    }
    return groupBy(iterable, criterion);
}

// ✅ 推荐：处理边界情况
function safeSlidingWindow(array, width) {
    if (!Array.isArray(array)) {
        throw new Error("First argument must be an array");
    }
    if (width <= 0 || width > array.length) {
        return [];
    }
    return slidingWindow(array, width);
}
```

### 2. 函数组合
```javascript
// ✅ 推荐：组合多个数组操作
const processUserData = (users) =>
    sortBy(
        unique(
            users.filter(user => user.active)
        ),
        "name"
    );

// 或使用管道模式
const pipe = (...fns) => (value) => fns.reduce((acc, fn) => fn(acc), value);

const processUserDataPipe = pipe(
    users => users.filter(user => user.active),
    unique,
    users => sortBy(users, "name")
);
```

### 3. 错误处理
```javascript
// ✅ 推荐：提供默认值和错误处理
function safeIntersection(iter1, iter2, defaultValue = []) {
    try {
        if (!isIterable(iter1) || !isIterable(iter2)) {
            return defaultValue;
        }
        return intersection(iter1, iter2);
    } catch (error) {
        console.warn("Intersection failed:", error);
        return defaultValue;
    }
}
```

### 4. 可读性优化
```javascript
// ✅ 推荐：使用描述性的标准函数
const groupUsersByDepartment = (users) => groupBy(users, "department");
const groupUsersByAgeGroup = (users) => groupBy(users, user => {
    if (user.age < 30) return "young";
    if (user.age < 40) return "middle";
    return "senior";
});

// ✅ 推荐：为复杂操作添加注释
const analyzeUserData = (users) => {
    // 按部门分组用户
    const byDepartment = groupBy(users, "department");

    // 计算每个部门的平均薪资
    const avgSalaryByDept = Object.fromEntries(
        Object.entries(byDepartment).map(([dept, deptUsers]) => [
            dept,
            deptUsers.reduce((sum, user) => sum + user.salary, 0) / deptUsers.length
        ])
    );

    return avgSalaryByDept;
};
```

## 常见陷阱和注意事项

### 1. 引用vs值
```javascript
// ⚠️ 注意：对象数组的去重基于引用
const objects = [{ id: 1 }, { id: 1 }];
unique(objects); // 仍然是两个元素，因为是不同的对象引用

// ✅ 解决方案：基于属性去重
function uniqueBy(array, criterion) {
    const seen = new Set();
    const extract = _getExtractorFrom(criterion);
    return array.filter(item => {
        const key = extract(item);
        if (seen.has(key)) {
            return false;
        }
        seen.add(key);
        return true;
    });
}

uniqueBy(objects, "id"); // [{ id: 1 }]
```

### 2. 空数组处理
```javascript
// ⚠️ 注意：某些函数对空数组的行为
cartesian(); // [undefined]
sections([]); // [[]]
slidingWindow([], 2); // []

// ✅ 推荐：明确处理空输入
function safeCartesian(...arrays) {
    if (arrays.length === 0 || arrays.some(arr => arr.length === 0)) {
        return [];
    }
    return cartesian(...arrays);
}
```

### 3. 性能考虑
```javascript
// ❌ 避免：在循环中重复调用昂贵的操作
for (const item of items) {
    const sorted = sortBy(allItems, "priority"); // 每次都重新排序
    // ...
}

// ✅ 推荐：预先计算
const sortedItems = sortBy(allItems, "priority");
for (const item of items) {
    // 使用预排序的数据
}
```

## 总结

Odoo 数组工具模块提供了丰富而强大的数组操作功能：

**核心优势**:
- **功能完整**: 涵盖了常见的数组操作需求
- **类型安全**: 完整的 TypeScript 类型定义
- **性能优化**: 使用高效的算法和数据结构
- **易于使用**: 直观的 API 设计

**适用场景**:
- 数据处理和分析
- 用户界面状态管理
- 搜索和过滤功能
- 配置选项生成
- 权限和角色管理

这个工具模块为 Odoo Web 客户端提供了强大的数组处理能力，是构建复杂数据操作功能的重要基础。

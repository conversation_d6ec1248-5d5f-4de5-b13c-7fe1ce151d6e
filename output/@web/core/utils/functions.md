# Odoo 函数工具 (Functions Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/functions.js`  
**原始路径**: `/web/static/src/core/utils/functions.js`  
**模块类型**: 核心工具模块 - 函数工具  
**代码行数**: 39 行  
**依赖关系**: 无外部依赖

## 模块功能

函数工具模块是 Odoo Web 客户端的核心工具库，提供了实用的函数操作工具，包括：
- 函数记忆化（Memoization）
- 唯一ID生成器

虽然函数数量不多，但这些都是高频使用的核心工具函数。

## 核心函数详解

### 1. memoize() - 函数记忆化
```javascript
function memoize(func) {
    const cache = new Map();
    const funcName = func.name ? func.name + " (memoized)" : "memoized";
    return {
        [funcName](...args) {
            if (!cache.has(args[0])) {
                cache.set(args[0], func(...args));
            }
            return cache.get(args[0]);
        },
    }[funcName];
}
```

**功能特性**:
- **缓存机制**: 基于第一个参数值进行缓存
- **性能优化**: 避免重复计算相同输入的结果
- **函数名保持**: 保持原函数名并添加 "(memoized)" 标识
- **类型安全**: 完整的 TypeScript 类型支持

**工作原理**:
1. **缓存创建**: 使用 Map 作为缓存存储
2. **参数检查**: 检查第一个参数是否已缓存
3. **结果缓存**: 首次调用时缓存结果
4. **缓存返回**: 后续调用直接返回缓存结果

**使用示例**:
```javascript
// 基本使用
function expensiveCalculation(n) {
    console.log(`计算 ${n} 的阶乘`);
    if (n <= 1) return 1;
    return n * expensiveCalculation(n - 1);
}

const memoizedFactorial = memoize(expensiveCalculation);

// 第一次调用 - 执行计算
console.log(memoizedFactorial(5)); // 输出: "计算 5 的阶乘" 然后返回 120

// 第二次调用相同参数 - 直接返回缓存结果
console.log(memoizedFactorial(5)); // 直接返回 120，不输出计算信息

// 不同参数 - 执行新的计算
console.log(memoizedFactorial(6)); // 输出: "计算 6 的阶乘" 然后返回 720
```

**实际应用场景**:
```javascript
// 1. 复杂数据转换
function transformUserData(userId) {
    // 模拟复杂的数据转换过程
    const userData = fetchUserFromAPI(userId);
    return {
        ...userData,
        fullName: `${userData.firstName} ${userData.lastName}`,
        permissions: calculatePermissions(userData.roles),
        preferences: processPreferences(userData.settings)
    };
}

const memoizedTransform = memoize(transformUserData);

// 在组件中使用
class UserComponent extends Component {
    setup() {
        this.state = useState({
            user: memoizedTransform(this.props.userId)
        });
    }
}

// 2. 模板编译缓存
function compileTemplate(templateString) {
    console.log("编译模板:", templateString);
    // 模拟模板编译过程
    return templateString.replace(/\{\{(\w+)\}\}/g, (match, prop) => {
        return `\${data.${prop}}`;
    });
}

const memoizedCompile = memoize(compileTemplate);

// 多次使用相同模板时，只编译一次
const template1 = memoizedCompile("Hello {{name}}!"); // 执行编译
const template2 = memoizedCompile("Hello {{name}}!"); // 使用缓存

// 3. 计算密集型函数
function calculateHash(data) {
    // 模拟计算密集型操作
    let hash = 0;
    const str = JSON.stringify(data);
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(16);
}

const memoizedHash = memoize(calculateHash);

// 4. API响应处理
function processApiResponse(endpoint) {
    // 模拟API响应处理
    return fetch(endpoint)
        .then(response => response.json())
        .then(data => ({
            ...data,
            processedAt: Date.now(),
            normalized: normalizeData(data)
        }));
}

const memoizedApiProcess = memoize(processApiResponse);
```

**性能对比示例**:
```javascript
// 性能测试函数
function performanceTest() {
    // 原始函数
    function fibonacci(n) {
        if (n <= 1) return n;
        return fibonacci(n - 1) + fibonacci(n - 2);
    }
    
    // 记忆化版本
    const memoizedFib = memoize(fibonacci);
    
    console.time("原始函数");
    fibonacci(35);
    console.timeEnd("原始函数"); // 可能需要几秒钟
    
    console.time("记忆化函数 - 首次");
    memoizedFib(35);
    console.timeEnd("记忆化函数 - 首次"); // 仍然需要时间
    
    console.time("记忆化函数 - 缓存");
    memoizedFib(35);
    console.timeEnd("记忆化函数 - 缓存"); // 几乎瞬间完成
}
```

### 2. uniqueId() - 唯一ID生成器
```javascript
function uniqueId(prefix = "") {
    return `${prefix}${++uniqueId.nextId}`;
}
// set nextId on the function itself to be able to patch then
uniqueId.nextId = 0;
```

**功能特性**:
- **全局唯一**: 在整个客户端会话中保证唯一性
- **前缀支持**: 支持自定义前缀
- **递增计数**: 基于递增计数器生成
- **可测试**: nextId 属性可在测试中重置

**使用示例**:
```javascript
// 基本使用
console.log(uniqueId());        // "1"
console.log(uniqueId());        // "2"
console.log(uniqueId("item_")); // "item_3"
console.log(uniqueId("btn-"));  // "btn-4"

// DOM元素ID生成
class FormComponent extends Component {
    setup() {
        this.inputId = uniqueId("input_");
        this.labelId = uniqueId("label_");
        this.formId = uniqueId("form_");
    }
    
    render() {
        return xml`
            <form t-att-id="formId">
                <label t-att-id="labelId" t-att-for="inputId">用户名:</label>
                <input t-att-id="inputId" type="text" t-att-aria-labelledby="labelId"/>
            </form>
        `;
    }
}

// 临时数据标识
class DataManager {
    constructor() {
        this.tempData = new Map();
    }
    
    addTempItem(data) {
        const id = uniqueId("temp_");
        this.tempData.set(id, {
            ...data,
            id,
            createdAt: Date.now()
        });
        return id;
    }
    
    getTempItem(id) {
        return this.tempData.get(id);
    }
}

// 组件实例标识
class ComponentTracker {
    constructor() {
        this.components = new Map();
    }
    
    registerComponent(component) {
        const id = uniqueId("comp_");
        this.components.set(id, {
            component,
            id,
            registeredAt: Date.now()
        });
        return id;
    }
}
```

**实际应用场景**:
```javascript
// 1. 动态表单字段
class DynamicForm extends Component {
    setup() {
        this.state = useState({
            fields: []
        });
    }
    
    addField(type) {
        const field = {
            id: uniqueId("field_"),
            type,
            name: uniqueId("input_"),
            label: `字段 ${this.state.fields.length + 1}`
        };
        this.state.fields.push(field);
    }
    
    render() {
        return xml`
            <form>
                <t t-foreach="state.fields" t-as="field" t-key="field.id">
                    <div class="form-group">
                        <label t-att-for="field.name" t-esc="field.label"/>
                        <input t-att-id="field.name" 
                               t-att-name="field.name"
                               t-att-type="field.type"/>
                    </div>
                </t>
                <button type="button" t-on-click="() => this.addField('text')">
                    添加文本字段
                </button>
            </form>
        `;
    }
}

// 2. 标签页管理
class TabManager extends Component {
    setup() {
        this.state = useState({
            tabs: [
                { id: uniqueId("tab_"), title: "首页", content: "首页内容" }
            ],
            activeTab: null
        });
        this.state.activeTab = this.state.tabs[0].id;
    }
    
    addTab(title, content) {
        const tab = {
            id: uniqueId("tab_"),
            title,
            content
        };
        this.state.tabs.push(tab);
        this.state.activeTab = tab.id;
    }
    
    render() {
        return xml`
            <div class="tab-container">
                <div class="tab-headers">
                    <t t-foreach="state.tabs" t-as="tab" t-key="tab.id">
                        <button class="tab-header"
                                t-att-class="{ active: tab.id === state.activeTab }"
                                t-on-click="() => this.state.activeTab = tab.id"
                                t-esc="tab.title"/>
                    </t>
                </div>
                <div class="tab-content">
                    <t t-foreach="state.tabs" t-as="tab" t-key="tab.id">
                        <div t-if="tab.id === state.activeTab" 
                             t-esc="tab.content"/>
                    </t>
                </div>
            </div>
        `;
    }
}

// 3. 通知系统
class NotificationService {
    constructor() {
        this.notifications = reactive([]);
    }
    
    add(message, type = "info", duration = 5000) {
        const notification = {
            id: uniqueId("notif_"),
            message,
            type,
            createdAt: Date.now()
        };
        
        this.notifications.push(notification);
        
        if (duration > 0) {
            setTimeout(() => {
                this.remove(notification.id);
            }, duration);
        }
        
        return notification.id;
    }
    
    remove(id) {
        const index = this.notifications.findIndex(n => n.id === id);
        if (index > -1) {
            this.notifications.splice(index, 1);
        }
    }
}

// 4. 拖拽系统
class DragDropManager {
    constructor() {
        this.dragItems = new Map();
        this.dropZones = new Map();
    }
    
    registerDragItem(element, data) {
        const id = uniqueId("drag_");
        this.dragItems.set(id, { element, data, id });
        element.setAttribute("data-drag-id", id);
        return id;
    }
    
    registerDropZone(element, handler) {
        const id = uniqueId("drop_");
        this.dropZones.set(id, { element, handler, id });
        element.setAttribute("data-drop-id", id);
        return id;
    }
}
```

## 高级使用技巧

### 1. 记忆化函数的缓存管理
```javascript
// 创建带缓存清理功能的记忆化函数
function memoizeWithClear(func) {
    const memoized = memoize(func);
    
    // 添加清理缓存的方法
    memoized.clearCache = function() {
        // 重新创建记忆化函数以清空缓存
        const newMemoized = memoize(func);
        Object.setPrototypeOf(this, newMemoized);
        Object.assign(this, newMemoized);
    };
    
    return memoized;
}

// 使用示例
const memoizedFunc = memoizeWithClear(expensiveFunction);
memoizedFunc(1); // 计算并缓存
memoizedFunc(1); // 使用缓存
memoizedFunc.clearCache(); // 清空缓存
memoizedFunc(1); // 重新计算
```

### 2. 条件记忆化
```javascript
// 只对特定条件的输入进行记忆化
function conditionalMemoize(func, shouldMemoize) {
    const cache = new Map();
    
    return function(...args) {
        const key = args[0];
        
        if (shouldMemoize(key) && cache.has(key)) {
            return cache.get(key);
        }
        
        const result = func(...args);
        
        if (shouldMemoize(key)) {
            cache.set(key, result);
        }
        
        return result;
    };
}

// 使用示例：只缓存大于10的数字的计算结果
const selectiveMemoized = conditionalMemoize(
    expensiveCalculation,
    (n) => n > 10
);
```

### 3. 唯一ID的命名空间
```javascript
// 创建命名空间的ID生成器
function createIdGenerator(namespace) {
    let counter = 0;
    
    return function(prefix = "") {
        return `${namespace}_${prefix}${++counter}`;
    };
}

// 使用示例
const formIdGen = createIdGenerator("form");
const modalIdGen = createIdGenerator("modal");

console.log(formIdGen("input_"));  // "form_input_1"
console.log(modalIdGen("dialog_")); // "modal_dialog_1"
console.log(formIdGen("button_")); // "form_button_2"
```

### 4. 测试中的ID重置
```javascript
// 测试辅助函数
function resetUniqueId() {
    uniqueId.nextId = 0;
}

// 在测试中使用
describe("组件测试", () => {
    beforeEach(() => {
        resetUniqueId(); // 每个测试前重置ID计数器
    });
    
    it("应该生成一致的ID", () => {
        const id1 = uniqueId("test_");
        const id2 = uniqueId("test_");
        
        expect(id1).toBe("test_1");
        expect(id2).toBe("test_2");
    });
});
```

## 性能考虑

### 1. 记忆化的内存使用
```javascript
// ⚠️ 注意：记忆化可能导致内存泄漏
function potentialMemoryLeak() {
    const memoized = memoize((obj) => {
        return processLargeObject(obj);
    });
    
    // 如果传入大量不同的对象，缓存会无限增长
    for (let i = 0; i < 10000; i++) {
        memoized({ id: i, data: new Array(1000).fill(i) });
    }
}

// ✅ 解决方案：限制缓存大小
function memoizeWithLimit(func, limit = 100) {
    const cache = new Map();
    
    return function(...args) {
        const key = args[0];
        
        if (cache.has(key)) {
            return cache.get(key);
        }
        
        const result = func(...args);
        
        if (cache.size >= limit) {
            // 删除最旧的缓存项
            const firstKey = cache.keys().next().value;
            cache.delete(firstKey);
        }
        
        cache.set(key, result);
        return result;
    };
}
```

### 2. 唯一ID的性能
```javascript
// uniqueId 的性能特点
console.time("生成10000个ID");
for (let i = 0; i < 10000; i++) {
    uniqueId("item_");
}
console.timeEnd("生成10000个ID"); // 通常 < 10ms

// 对比其他ID生成方法
console.time("UUID生成");
for (let i = 0; i < 10000; i++) {
    crypto.randomUUID(); // 更慢但更安全
}
console.timeEnd("UUID生成");
```

## 最佳实践

### 1. 何时使用记忆化
```javascript
// ✅ 适合记忆化的场景
// - 纯函数（相同输入总是产生相同输出）
// - 计算密集型操作
// - 频繁调用的函数
// - 输入参数相对有限

// ❌ 不适合记忆化的场景
// - 有副作用的函数
// - 输入参数变化很大的函数
// - 计算很快的简单函数
// - 内存敏感的环境
```

### 2. 唯一ID的使用原则
```javascript
// ✅ 推荐的使用场景
// - 临时DOM元素ID
// - 组件实例标识
// - 临时数据标识
// - 测试中的标识符

// ❌ 不推荐的使用场景
// - 持久化数据的主键
// - 跨会话的标识符
// - 安全敏感的标识符
// - 需要全局唯一的标识符
```

## 总结

Odoo 函数工具模块虽然简洁，但提供了两个非常实用的核心功能：

**memoize() 函数**:
- **性能优化**: 通过缓存避免重复计算
- **简单易用**: 一行代码即可为函数添加记忆化
- **类型安全**: 完整的 TypeScript 支持
- **适用广泛**: 适合各种计算密集型场景

**uniqueId() 函数**:
- **全局唯一**: 会话内保证唯一性
- **灵活前缀**: 支持自定义前缀
- **高性能**: 基于简单计数器，性能优异
- **测试友好**: 支持测试中的状态重置

这两个工具函数是 Odoo Web 客户端的基础设施，为性能优化和唯一标识生成提供了可靠的解决方案。

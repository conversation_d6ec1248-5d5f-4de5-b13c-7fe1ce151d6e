# Odoo 嵌套排序工具 (Nested Sortable Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/nested_sortable.js`  
**原始路径**: `/web/static/src/core/utils/nested_sortable.js`  
**模块类型**: 核心工具模块 - 嵌套排序工具  
**代码行数**: 404 行  
**依赖关系**: 
- `@web/core/l10n/localization` - 本地化工具 (localization)
- `@web/core/utils/draggable_hook_builder_owl` - OWL拖拽Hook构建器 (makeDraggableHook)

## 模块功能

嵌套排序工具模块是 Odoo Web 客户端的核心工具库，提供了强大的嵌套列表排序功能。该模块支持：
- 多层级嵌套排序
- 跨组拖拽移动
- 智能占位符定位
- 层级深度控制
- RTL布局支持
- 自定义验证规则
- 边缘滚动支持

这个模块是构建复杂层级结构界面的重要基础，广泛应用于菜单管理、分类管理、组织架构等场景。

## 嵌套排序原理

### 层级结构管理
嵌套排序基于HTML的层级结构：
1. **容器元素**: 使用 `ul` 或 `ol` 作为列表容器
2. **项目元素**: 使用 `li` 作为可排序的项目
3. **嵌套关系**: 子列表嵌套在父项目内部
4. **占位符**: 动态创建占位符指示拖拽位置

### 拖拽检测机制
- **垂直移动**: 检测鼠标垂直位置变化，调整项目顺序
- **水平移动**: 检测鼠标水平位置变化，调整层级关系
- **嵌套间隔**: 通过 `nestInterval` 控制层级变化的敏感度
- **最大层级**: 通过 `maxLevels` 限制嵌套深度

### RTL布局支持
- **方向检测**: 自动检测RTL（从右到左）布局
- **坐标调整**: 根据布局方向调整拖拽坐标计算
- **嵌套逻辑**: 适配RTL布局的嵌套移动逻辑

## 核心函数详解

### useNestedSortable() - 嵌套排序Hook
```javascript
const useNestedSortable = makeDraggableHook({
    name: "useNestedSortable",
    acceptedParams: {
        groups: [String, Function],
        connectGroups: [Boolean, Function],
        nest: [Boolean],
        listTagName: [String],
        nestInterval: [Number],
        maxLevels: [Number],
        isAllowed: [Function],
        useElementSize: [Boolean],
    },
    defaultParams: {
        connectGroups: false,
        currentGroup: null,
        cursor: "grabbing",
        edgeScrolling: { speed: 20, threshold: 60 },
        elements: "li",
        groupSelector: null,
        nest: false,
        listTagName: "ul",
        nestInterval: 15,
        maxLevels: 0,
        isAllowed: (ctx) => true,
        useElementSize: false,
    },
    // ... 实现细节
});
```

**功能特性**:
- **多组支持**: 支持多个独立的排序组
- **跨组连接**: 可以在不同组之间拖拽移动
- **嵌套控制**: 灵活的嵌套层级控制
- **自定义验证**: 支持自定义拖拽验证规则
- **占位符样式**: 支持自定义占位符大小和样式

**参数说明**:
- `groups`: 组选择器，用于定义排序组
- `connectGroups`: 是否允许跨组拖拽
- `nest`: 是否启用嵌套功能
- `listTagName`: 列表标签名（ul或ol）
- `nestInterval`: 嵌套触发的水平距离
- `maxLevels`: 最大嵌套层级（0表示无限制）
- `isAllowed`: 自定义验证函数
- `useElementSize`: 占位符是否使用元素实际大小

**使用示例**:
```javascript
// 基本嵌套排序
class NestedList extends Component {
    static template = xml`
        <ul t-ref="nestedList" class="nested-sortable-list">
            <li t-foreach="items" t-as="item" t-key="item.id" 
                class="nested-item" t-att-data-id="item.id">
                <div class="item-content">
                    <span t-esc="item.name" />
                </div>
                <ul t-if="item.children and item.children.length" class="nested-children">
                    <li t-foreach="item.children" t-as="child" t-key="child.id"
                        class="nested-item" t-att-data-id="child.id">
                        <div class="item-content">
                            <span t-esc="child.name" />
                        </div>
                    </li>
                </ul>
            </li>
        </ul>
    `;
    
    setup() {
        this.listRef = useRef("nestedList");
        this.items = useState([
            {
                id: 1,
                name: "Parent 1",
                children: [
                    { id: 11, name: "Child 1.1" },
                    { id: 12, name: "Child 1.2" }
                ]
            },
            {
                id: 2,
                name: "Parent 2",
                children: [
                    { id: 21, name: "Child 2.1" }
                ]
            }
        ]);
        
        this.sortableState = useNestedSortable({
            ref: this.listRef,
            nest: true,
            maxLevels: 3,
            onMove: this.onItemMove.bind(this),
            onDrop: this.onItemDrop.bind(this)
        });
    }
    
    onItemMove(params) {
        console.log('项目移动:', params);
        // 实时更新UI反馈
        this.updateMovePreview(params);
    }
    
    onItemDrop(params) {
        console.log('项目放置:', params);
        // 更新数据结构
        this.updateItemsStructure(params);
    }
    
    updateItemsStructure(params) {
        const { element, previous, next, parent } = params;
        const itemId = parseInt(element.dataset.id);
        
        // 从原位置移除项目
        const item = this.removeItemFromStructure(itemId);
        
        // 插入到新位置
        this.insertItemToStructure(item, previous, next, parent);
    }
    
    removeItemFromStructure(itemId) {
        // 递归查找并移除项目
        const findAndRemove = (items) => {
            for (let i = 0; i < items.length; i++) {
                if (items[i].id === itemId) {
                    return items.splice(i, 1)[0];
                }
                if (items[i].children) {
                    const found = findAndRemove(items[i].children);
                    if (found) return found;
                }
            }
            return null;
        };
        
        return findAndRemove(this.items);
    }
    
    insertItemToStructure(item, previous, next, parent) {
        const parentId = parent ? parseInt(parent.dataset.id) : null;
        const previousId = previous ? parseInt(previous.dataset.id) : null;
        const nextId = next ? parseInt(next.dataset.id) : null;
        
        // 找到目标容器
        const targetContainer = parentId ? 
            this.findItemById(parentId) : 
            { children: this.items };
        
        if (!targetContainer.children) {
            targetContainer.children = [];
        }
        
        // 确定插入位置
        let insertIndex = 0;
        if (previousId) {
            insertIndex = targetContainer.children.findIndex(child => child.id === previousId) + 1;
        } else if (nextId) {
            insertIndex = targetContainer.children.findIndex(child => child.id === nextId);
        }
        
        // 插入项目
        targetContainer.children.splice(insertIndex, 0, item);
    }
    
    findItemById(id) {
        const search = (items) => {
            for (const item of items) {
                if (item.id === id) return item;
                if (item.children) {
                    const found = search(item.children);
                    if (found) return found;
                }
            }
            return null;
        };
        
        return search(this.items);
    }
}

// 高级嵌套排序 with 多组支持
class MultiGroupNestedList extends Component {
    static template = xml`
        <div class="multi-group-container">
            <div t-foreach="groups" t-as="group" t-key="group.id" 
                 class="sortable-group" t-att-data-group-id="group.id">
                <h3 t-esc="group.name" />
                <ul class="nested-sortable-list">
                    <li t-foreach="group.items" t-as="item" t-key="item.id"
                        class="nested-item" t-att-data-id="item.id">
                        <div class="item-content">
                            <span t-esc="item.name" />
                        </div>
                        <ul t-if="item.children and item.children.length" class="nested-children">
                            <li t-foreach="item.children" t-as="child" t-key="child.id"
                                class="nested-item" t-att-data-id="child.id">
                                <div class="item-content">
                                    <span t-esc="child.name" />
                                </div>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    `;
    
    setup() {
        this.containerRef = useRef("container");
        this.groups = useState([
            {
                id: 'group1',
                name: 'Group 1',
                items: [
                    { id: 1, name: 'Item 1', children: [] },
                    { id: 2, name: 'Item 2', children: [] }
                ]
            },
            {
                id: 'group2',
                name: 'Group 2',
                items: [
                    { id: 3, name: 'Item 3', children: [] }
                ]
            }
        ]);
        
        this.sortableState = useNestedSortable({
            ref: this.containerRef,
            groups: '.sortable-group',
            connectGroups: true,
            nest: true,
            maxLevels: 2,
            isAllowed: this.isDropAllowed.bind(this),
            onMove: this.onItemMove.bind(this),
            onDrop: this.onItemDrop.bind(this),
            onGroupEnter: this.onGroupEnter.bind(this),
            onGroupLeave: this.onGroupLeave.bind(this)
        });
    }
    
    isDropAllowed(ctx) {
        // 自定义验证逻辑
        const element = ctx.element;
        const targetGroup = ctx.placeholder?.closest('.sortable-group');
        
        // 例如：某些项目不能移动到特定组
        if (element.dataset.restricted && targetGroup?.dataset.groupId === 'group2') {
            return false;
        }
        
        return true;
    }
    
    onGroupEnter(params) {
        console.log('进入组:', params.group);
        params.group.classList.add('drop-target');
    }
    
    onGroupLeave(params) {
        console.log('离开组:', params.group);
        params.group.classList.remove('drop-target');
    }
    
    onItemMove(params) {
        // 实时反馈
        this.updateDropIndicators(params);
    }
    
    onItemDrop(params) {
        const { element, newGroup, group } = params;
        
        if (newGroup && newGroup !== group) {
            // 跨组移动
            this.moveItemBetweenGroups(element, group, newGroup, params);
        } else {
            // 组内移动
            this.moveItemWithinGroup(element, group, params);
        }
    }
    
    moveItemBetweenGroups(element, fromGroup, toGroup, params) {
        const itemId = parseInt(element.dataset.id);
        const fromGroupId = fromGroup.dataset.groupId;
        const toGroupId = toGroup.dataset.groupId;
        
        // 从源组移除
        const fromGroupData = this.groups.find(g => g.id === fromGroupId);
        const item = this.removeItemFromGroup(fromGroupData, itemId);
        
        // 添加到目标组
        const toGroupData = this.groups.find(g => g.id === toGroupId);
        this.insertItemToGroup(toGroupData, item, params);
        
        console.log(`项目 ${itemId} 从 ${fromGroupId} 移动到 ${toGroupId}`);
    }
    
    moveItemWithinGroup(element, group, params) {
        const itemId = parseInt(element.dataset.id);
        const groupId = group.dataset.groupId;
        const groupData = this.groups.find(g => g.id === groupId);
        
        // 重新排序组内项目
        this.reorderItemsInGroup(groupData, itemId, params);
        
        console.log(`项目 ${itemId} 在组 ${groupId} 内重新排序`);
    }
}
```

## 高级应用模式

### 1. 菜单管理系统
```javascript
class MenuManager extends Component {
    static template = xml`
        <div class="menu-manager">
            <div class="menu-toolbar">
                <button t-on-click="addMenuItem" class="btn btn-primary">添加菜单项</button>
                <button t-on-click="saveMenuStructure" class="btn btn-success">保存结构</button>
                <button t-on-click="resetMenu" class="btn btn-secondary">重置</button>
            </div>
            <ul t-ref="menuList" class="menu-list">
                <li t-foreach="menuItems" t-as="item" t-key="item.id"
                    class="menu-item" t-att-data-id="item.id"
                    t-att-data-type="item.type">
                    <div class="menu-item-content">
                        <i t-att-class="item.icon" />
                        <span t-esc="item.label" />
                        <div class="menu-item-actions">
                            <button t-on-click="() => this.editMenuItem(item)" class="btn-edit">编辑</button>
                            <button t-on-click="() => this.deleteMenuItem(item)" class="btn-delete">删除</button>
                        </div>
                    </div>
                    <ul t-if="item.children and item.children.length" class="menu-submenu">
                        <li t-foreach="item.children" t-as="child" t-key="child.id"
                            class="menu-item" t-att-data-id="child.id"
                            t-att-data-type="child.type">
                            <div class="menu-item-content">
                                <i t-att-class="child.icon" />
                                <span t-esc="child.label" />
                                <div class="menu-item-actions">
                                    <button t-on-click="() => this.editMenuItem(child)" class="btn-edit">编辑</button>
                                    <button t-on-click="() => this.deleteMenuItem(child)" class="btn-delete">删除</button>
                                </div>
                            </div>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    `;

    setup() {
        this.menuListRef = useRef("menuList");
        this.menuItems = useState([
            {
                id: 1,
                label: "首页",
                icon: "fa fa-home",
                type: "page",
                url: "/",
                children: []
            },
            {
                id: 2,
                label: "产品",
                icon: "fa fa-box",
                type: "category",
                children: [
                    { id: 21, label: "产品列表", icon: "fa fa-list", type: "page", url: "/products" },
                    { id: 22, label: "产品分类", icon: "fa fa-tags", type: "page", url: "/categories" }
                ]
            },
            {
                id: 3,
                label: "关于我们",
                icon: "fa fa-info",
                type: "page",
                url: "/about",
                children: []
            }
        ]);

        this.sortableState = useNestedSortable({
            ref: this.menuListRef,
            nest: true,
            maxLevels: 3,
            nestInterval: 20,
            isAllowed: this.isMenuMoveAllowed.bind(this),
            onMove: this.onMenuMove.bind(this),
            onDrop: this.onMenuDrop.bind(this)
        });

        this.menuHistory = [];
        this.maxHistorySize = 10;
    }

    isMenuMoveAllowed(ctx) {
        const element = ctx.element;
        const placeholder = ctx.placeholder;

        // 检查菜单项类型限制
        const itemType = element.dataset.type;
        const targetParent = placeholder.parentElement.closest('.menu-item');
        const targetType = targetParent?.dataset?.type;

        // 例如：页面类型不能包含子菜单
        if (targetType === 'page') {
            return false;
        }

        // 检查循环引用
        if (this.wouldCreateCircularReference(element, placeholder)) {
            return false;
        }

        return true;
    }

    wouldCreateCircularReference(element, placeholder) {
        const elementId = element.dataset.id;
        let current = placeholder.parentElement;

        while (current) {
            if (current.classList.contains('menu-item') &&
                current.dataset.id === elementId) {
                return true;
            }
            current = current.parentElement.closest('.menu-item');
        }

        return false;
    }

    onMenuMove(params) {
        // 实时显示移动预览
        this.updateMovePreview(params);

        // 验证移动的有效性
        if (!this.isValidMenuMove(params)) {
            params.placeholder.classList.add('invalid-drop');
        } else {
            params.placeholder.classList.remove('invalid-drop');
        }
    }

    onMenuDrop(params) {
        if (!this.isValidMenuMove(params)) {
            console.warn('无效的菜单移动');
            return;
        }

        // 保存当前状态到历史记录
        this.saveToHistory();

        // 更新菜单结构
        this.updateMenuStructure(params);

        // 触发变更事件
        this.triggerMenuChange();
    }

    updateMenuStructure(params) {
        const { element, previous, next, parent } = params;
        const itemId = parseInt(element.dataset.id);

        // 从原位置移除
        const item = this.removeMenuItemFromStructure(itemId);

        // 插入到新位置
        this.insertMenuItemToStructure(item, previous, next, parent);

        // 更新序号
        this.updateMenuOrder();
    }

    removeMenuItemFromStructure(itemId) {
        const findAndRemove = (items) => {
            for (let i = 0; i < items.length; i++) {
                if (items[i].id === itemId) {
                    return items.splice(i, 1)[0];
                }
                if (items[i].children) {
                    const found = findAndRemove(items[i].children);
                    if (found) return found;
                }
            }
            return null;
        };

        return findAndRemove(this.menuItems);
    }

    insertMenuItemToStructure(item, previous, next, parent) {
        const parentId = parent ? parseInt(parent.dataset.id) : null;

        // 找到目标容器
        const targetContainer = parentId ?
            this.findMenuItemById(parentId) :
            { children: this.menuItems };

        if (!targetContainer.children) {
            targetContainer.children = [];
        }

        // 确定插入位置
        let insertIndex = targetContainer.children.length;
        if (previous) {
            const prevId = parseInt(previous.dataset.id);
            insertIndex = targetContainer.children.findIndex(child => child.id === prevId) + 1;
        } else if (next) {
            const nextId = parseInt(next.dataset.id);
            insertIndex = targetContainer.children.findIndex(child => child.id === nextId);
        }

        // 插入项目
        targetContainer.children.splice(insertIndex, 0, item);
    }

    updateMenuOrder() {
        // 递归更新菜单项的排序
        const updateOrder = (items, parentPath = '') => {
            items.forEach((item, index) => {
                item.order = index;
                item.path = parentPath ? `${parentPath}.${index}` : index.toString();

                if (item.children && item.children.length > 0) {
                    updateOrder(item.children, item.path);
                }
            });
        };

        updateOrder(this.menuItems);
    }

    saveToHistory() {
        const currentState = JSON.parse(JSON.stringify(this.menuItems));
        this.menuHistory.push(currentState);

        if (this.menuHistory.length > this.maxHistorySize) {
            this.menuHistory.shift();
        }
    }

    undoLastChange() {
        if (this.menuHistory.length > 0) {
            const previousState = this.menuHistory.pop();
            this.menuItems.splice(0, this.menuItems.length, ...previousState);
        }
    }

    addMenuItem() {
        const newItem = {
            id: Date.now(),
            label: "新菜单项",
            icon: "fa fa-circle",
            type: "page",
            url: "",
            children: []
        };

        this.menuItems.push(newItem);
        this.saveToHistory();
    }

    editMenuItem(item) {
        // 打开编辑对话框
        this.openEditDialog(item);
    }

    deleteMenuItem(item) {
        if (confirm(`确定要删除菜单项 "${item.label}" 吗？`)) {
            this.saveToHistory();
            this.removeMenuItemFromStructure(item.id);
        }
    }

    saveMenuStructure() {
        // 保存菜单结构到服务器
        const menuData = this.serializeMenuStructure();
        this.env.services.rpc('/menu/save', { menu: menuData })
            .then(() => {
                this.env.services.notification.add('菜单结构已保存', { type: 'success' });
            })
            .catch(error => {
                this.env.services.notification.add('保存失败: ' + error.message, { type: 'danger' });
            });
    }

    serializeMenuStructure() {
        const serialize = (items) => {
            return items.map(item => ({
                id: item.id,
                label: item.label,
                icon: item.icon,
                type: item.type,
                url: item.url,
                order: item.order,
                path: item.path,
                children: item.children ? serialize(item.children) : []
            }));
        };

        return serialize(this.menuItems);
    }
}
```

### 2. 组织架构管理器
```javascript
class OrganizationChart extends Component {
    static template = xml`
        <div class="org-chart">
            <div class="org-toolbar">
                <button t-on-click="addDepartment" class="btn btn-primary">添加部门</button>
                <button t-on-click="addEmployee" class="btn btn-secondary">添加员工</button>
                <select t-model="viewMode" t-on-change="onViewModeChange">
                    <option value="tree">树形视图</option>
                    <option value="flat">平铺视图</option>
                </select>
            </div>
            <ul t-ref="orgList" class="org-list" t-att-class="{ 'flat-view': viewMode === 'flat' }">
                <li t-foreach="organization" t-as="unit" t-key="unit.id"
                    class="org-unit" t-att-data-id="unit.id"
                    t-att-data-type="unit.type"
                    t-att-data-level="unit.level">
                    <div class="org-unit-content">
                        <div class="unit-header">
                            <i t-att-class="getUnitIcon(unit)" />
                            <span class="unit-name" t-esc="unit.name" />
                            <span class="unit-type" t-esc="unit.type" />
                            <span t-if="unit.employeeCount" class="employee-count">
                                ({{unit.employeeCount}} 人)
                            </span>
                        </div>
                        <div t-if="unit.manager" class="unit-manager">
                            负责人: <span t-esc="unit.manager.name" />
                        </div>
                    </div>
                    <ul t-if="unit.children and unit.children.length" class="org-children">
                        <li t-foreach="unit.children" t-as="child" t-key="child.id"
                            class="org-unit" t-att-data-id="child.id"
                            t-att-data-type="child.type"
                            t-att-data-level="child.level">
                            <div class="org-unit-content">
                                <div class="unit-header">
                                    <i t-att-class="getUnitIcon(child)" />
                                    <span class="unit-name" t-esc="child.name" />
                                    <span class="unit-type" t-esc="child.type" />
                                </div>
                                <div t-if="child.manager" class="unit-manager">
                                    负责人: <span t-esc="child.manager.name" />
                                </div>
                            </div>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    `;

    setup() {
        this.orgListRef = useRef("orgList");
        this.viewMode = useState("tree");
        this.organization = useState([
            {
                id: 1,
                name: "总公司",
                type: "company",
                level: 0,
                manager: { id: 1, name: "张总" },
                employeeCount: 150,
                children: [
                    {
                        id: 11,
                        name: "技术部",
                        type: "department",
                        level: 1,
                        manager: { id: 11, name: "李经理" },
                        employeeCount: 50,
                        children: [
                            { id: 111, name: "前端组", type: "team", level: 2, manager: { id: 111, name: "王组长" } },
                            { id: 112, name: "后端组", type: "team", level: 2, manager: { id: 112, name: "赵组长" } }
                        ]
                    },
                    {
                        id: 12,
                        name: "销售部",
                        type: "department",
                        level: 1,
                        manager: { id: 12, name: "陈经理" },
                        employeeCount: 30,
                        children: []
                    }
                ]
            }
        ]);

        this.sortableState = useNestedSortable({
            ref: this.orgListRef,
            nest: true,
            maxLevels: 5,
            nestInterval: 25,
            isAllowed: this.isOrgMoveAllowed.bind(this),
            onMove: this.onOrgMove.bind(this),
            onDrop: this.onOrgDrop.bind(this)
        });
    }

    isOrgMoveAllowed(ctx) {
        const element = ctx.element;
        const placeholder = ctx.placeholder;
        const elementType = element.dataset.type;
        const elementLevel = parseInt(element.dataset.level);

        // 检查组织层级规则
        const targetParent = placeholder.parentElement.closest('.org-unit');
        const targetType = targetParent?.dataset?.type;
        const targetLevel = targetParent ? parseInt(targetParent.dataset.level) : -1;

        // 公司不能移动到其他单位下
        if (elementType === 'company' && targetParent) {
            return false;
        }

        // 部门只能在公司下
        if (elementType === 'department' && targetType !== 'company') {
            return false;
        }

        // 团队只能在部门下
        if (elementType === 'team' && targetType !== 'department') {
            return false;
        }

        // 检查层级深度
        if (targetLevel + 1 > 4) { // 最多5层
            return false;
        }

        return true;
    }

    onOrgMove(params) {
        // 显示移动预览和层级指示
        this.updateOrgMovePreview(params);
    }

    onOrgDrop(params) {
        // 更新组织结构
        this.updateOrganizationStructure(params);

        // 重新计算层级
        this.recalculateLevels();

        // 更新员工统计
        this.updateEmployeeCounts();
    }

    updateOrganizationStructure(params) {
        const { element, previous, next, parent } = params;
        const unitId = parseInt(element.dataset.id);

        // 移除并重新插入
        const unit = this.removeOrgUnitFromStructure(unitId);
        this.insertOrgUnitToStructure(unit, previous, next, parent);
    }

    recalculateLevels() {
        const updateLevels = (units, level = 0) => {
            units.forEach(unit => {
                unit.level = level;
                if (unit.children && unit.children.length > 0) {
                    updateLevels(unit.children, level + 1);
                }
            });
        };

        updateLevels(this.organization);
    }

    updateEmployeeCounts() {
        const calculateCounts = (unit) => {
            let count = unit.directEmployees || 0;

            if (unit.children && unit.children.length > 0) {
                unit.children.forEach(child => {
                    count += calculateCounts(child);
                });
            }

            unit.employeeCount = count;
            return count;
        };

        this.organization.forEach(unit => calculateCounts(unit));
    }

    getUnitIcon(unit) {
        const icons = {
            company: 'fa fa-building',
            department: 'fa fa-users',
            team: 'fa fa-user-friends',
            employee: 'fa fa-user'
        };
        return icons[unit.type] || 'fa fa-circle';
    }

    onViewModeChange() {
        // 切换视图模式时重新初始化排序
        this.$nextTick(() => {
            this.sortableState = useNestedSortable({
                ref: this.orgListRef,
                nest: this.viewMode === 'tree',
                maxLevels: this.viewMode === 'tree' ? 5 : 1,
                isAllowed: this.isOrgMoveAllowed.bind(this),
                onMove: this.onOrgMove.bind(this),
                onDrop: this.onOrgDrop.bind(this)
            });
        });
    }
}
```

### 3. 文件夹树管理器
```javascript
class FileTreeManager extends Component {
    static template = xml`
        <div class="file-tree-manager">
            <div class="tree-toolbar">
                <button t-on-click="createFolder" class="btn btn-sm btn-primary">新建文件夹</button>
                <button t-on-click="uploadFile" class="btn btn-sm btn-secondary">上传文件</button>
                <input t-ref="fileInput" type="file" multiple="true" style="display: none;" />
            </div>
            <ul t-ref="fileTree" class="file-tree">
                <li t-foreach="fileSystem" t-as="item" t-key="item.id"
                    class="file-item" t-att-data-id="item.id"
                    t-att-data-type="item.type"
                    t-att-class="{ 'expanded': item.expanded }">
                    <div class="file-item-content">
                        <span t-if="item.type === 'folder'"
                              t-on-click="() => this.toggleFolder(item)"
                              class="folder-toggle">
                            <i t-att-class="item.expanded ? 'fa fa-chevron-down' : 'fa fa-chevron-right'" />
                        </span>
                        <i t-att-class="getFileIcon(item)" />
                        <span class="file-name" t-esc="item.name" />
                        <span t-if="item.size" class="file-size" t-esc="formatFileSize(item.size)" />
                    </div>
                    <ul t-if="item.type === 'folder' and item.expanded and item.children"
                        class="file-children">
                        <li t-foreach="item.children" t-as="child" t-key="child.id"
                            class="file-item" t-att-data-id="child.id"
                            t-att-data-type="child.type">
                            <div class="file-item-content">
                                <i t-att-class="getFileIcon(child)" />
                                <span class="file-name" t-esc="child.name" />
                                <span t-if="child.size" class="file-size" t-esc="formatFileSize(child.size)" />
                            </div>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    `;

    setup() {
        this.fileTreeRef = useRef("fileTree");
        this.fileInputRef = useRef("fileInput");

        this.fileSystem = useState([
            {
                id: 1,
                name: "Documents",
                type: "folder",
                expanded: true,
                children: [
                    { id: 11, name: "report.pdf", type: "file", size: 1024000 },
                    { id: 12, name: "presentation.pptx", type: "file", size: 2048000 }
                ]
            },
            {
                id: 2,
                name: "Images",
                type: "folder",
                expanded: false,
                children: [
                    { id: 21, name: "photo1.jpg", type: "file", size: 512000 },
                    { id: 22, name: "photo2.png", type: "file", size: 768000 }
                ]
            }
        ]);

        this.sortableState = useNestedSortable({
            ref: this.fileTreeRef,
            nest: true,
            maxLevels: 10,
            isAllowed: this.isFileMoveAllowed.bind(this),
            onMove: this.onFileMove.bind(this),
            onDrop: this.onFileDrop.bind(this)
        });
    }

    isFileMoveAllowed(ctx) {
        const element = ctx.element;
        const placeholder = ctx.placeholder;
        const elementType = element.dataset.type;

        // 只有文件夹可以包含其他项目
        const targetParent = placeholder.parentElement.closest('.file-item');
        const targetType = targetParent?.dataset?.type;

        if (targetParent && targetType !== 'folder') {
            return false;
        }

        // 防止文件夹移动到自己内部
        if (elementType === 'folder') {
            let current = placeholder.parentElement;
            while (current) {
                if (current.classList.contains('file-item') &&
                    current.dataset.id === element.dataset.id) {
                    return false;
                }
                current = current.parentElement.closest('.file-item');
            }
        }

        return true;
    }

    onFileMove(params) {
        // 显示拖拽反馈
        this.updateFileMovePreview(params);
    }

    onFileDrop(params) {
        // 更新文件系统结构
        this.updateFileSystemStructure(params);

        // 触发文件移动事件
        this.triggerFileMoveEvent(params);
    }

    toggleFolder(folder) {
        folder.expanded = !folder.expanded;
    }

    getFileIcon(item) {
        if (item.type === 'folder') {
            return item.expanded ? 'fa fa-folder-open' : 'fa fa-folder';
        }

        const extension = item.name.split('.').pop().toLowerCase();
        const iconMap = {
            pdf: 'fa fa-file-pdf',
            doc: 'fa fa-file-word',
            docx: 'fa fa-file-word',
            xls: 'fa fa-file-excel',
            xlsx: 'fa fa-file-excel',
            ppt: 'fa fa-file-powerpoint',
            pptx: 'fa fa-file-powerpoint',
            jpg: 'fa fa-file-image',
            jpeg: 'fa fa-file-image',
            png: 'fa fa-file-image',
            gif: 'fa fa-file-image',
            txt: 'fa fa-file-text',
            zip: 'fa fa-file-archive',
            rar: 'fa fa-file-archive'
        };

        return iconMap[extension] || 'fa fa-file';
    }

    formatFileSize(bytes) {
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        if (bytes === 0) return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }
}
```

## 最佳实践

### 1. 性能优化
```javascript
// ✅ 推荐：限制嵌套层级
const useNestedSortable = makeDraggableHook({
    maxLevels: 5, // 限制最大层级
    nestInterval: 20, // 适当的嵌套间隔
    useElementSize: false // 使用轻量级占位符
});

// ✅ 推荐：虚拟化大量数据
class VirtualizedNestedList extends Component {
    setup() {
        this.visibleItems = useState([]);
        this.allItems = this.props.items;

        // 只渲染可见项目
        this.updateVisibleItems();
    }

    updateVisibleItems() {
        // 根据滚动位置计算可见项目
        const startIndex = Math.floor(this.scrollTop / this.itemHeight);
        const endIndex = Math.min(startIndex + this.visibleCount, this.allItems.length);

        this.visibleItems = this.allItems.slice(startIndex, endIndex);
    }
}
```

### 2. 数据一致性
```javascript
// ✅ 推荐：事务性更新
class TransactionalNestedSortable extends Component {
    setup() {
        this.pendingChanges = [];
        this.isUpdating = false;
    }

    onDrop(params) {
        if (this.isUpdating) return;

        this.isUpdating = true;

        try {
            // 开始事务
            this.beginTransaction();

            // 执行更新
            this.updateStructure(params);

            // 验证数据一致性
            if (this.validateStructure()) {
                this.commitTransaction();
            } else {
                this.rollbackTransaction();
            }
        } catch (error) {
            this.rollbackTransaction();
            console.error('更新失败:', error);
        } finally {
            this.isUpdating = false;
        }
    }

    validateStructure() {
        // 验证数据结构的完整性
        return this.checkCircularReferences() &&
               this.checkLevelConstraints() &&
               this.checkTypeConstraints();
    }
}
```

### 3. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
const useNestedSortable = makeDraggableHook({
    isAllowed: (ctx) => {
        try {
            return validateMove(ctx);
        } catch (error) {
            console.error('验证移动失败:', error);
            return false;
        }
    },
    onDrop: (params) => {
        try {
            updateStructure(params);
        } catch (error) {
            console.error('更新结构失败:', error);
            // 恢复到之前的状态
            restorePreviousState();
        }
    }
});
```

### 4. 可访问性
```javascript
// ✅ 推荐：支持键盘操作
class AccessibleNestedSortable extends Component {
    setup() {
        this.selectedItem = useState(null);

        useExternalListener(window, 'keydown', this.handleKeyboard);
    }

    handleKeyboard(event) {
        if (!this.selectedItem) return;

        switch (event.key) {
            case 'ArrowUp':
                this.selectPreviousItem();
                break;
            case 'ArrowDown':
                this.selectNextItem();
                break;
            case 'ArrowLeft':
                this.moveItemLeft();
                break;
            case 'ArrowRight':
                this.moveItemRight();
                break;
            case 'Enter':
                this.activateItem();
                break;
        }
    }
}
```

## 总结

Odoo 嵌套排序工具模块提供了强大的层级结构排序功能：

**核心优势**:
- **多层嵌套**: 支持无限层级的嵌套结构
- **智能检测**: 精确的拖拽位置检测和层级判断
- **跨组支持**: 支持多组之间的拖拽移动
- **自定义验证**: 灵活的移动验证机制
- **RTL支持**: 完整的从右到左布局支持

**适用场景**:
- 菜单管理系统
- 组织架构管理
- 文件夹树结构
- 分类管理系统
- 任务层级管理

**设计优势**:
- 基于拖拽Hook构建器的扩展
- 完整的生命周期管理
- 智能的占位符定位
- 丰富的事件回调

这个嵌套排序工具为 Odoo Web 客户端提供了强大的层级结构管理能力，是构建复杂树形界面的重要基础。

# Odoo 颜色工具 (Colors Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/colors.js`  
**原始路径**: `/web/static/src/core/utils/colors.js`  
**模块类型**: 核心工具模块 - 颜色处理工具  
**代码行数**: 272 行  
**依赖关系**: 无外部依赖

## 模块功能

颜色工具模块是 Odoo Web 客户端的核心工具库，提供了完整的颜色处理和转换功能。该模块支持：
- RGB与HSL颜色空间转换
- CSS颜色格式解析和生成
- 颜色格式标准化
- 颜色混合计算
- 多种颜色格式验证

这些功能是构建现代Web应用颜色系统的重要基础，广泛应用于主题系统、图表可视化、UI组件等场景。

## 颜色空间基础知识

### RGB颜色空间
- **Red (红)**: 0-255
- **Green (绿)**: 0-255  
- **Blue (蓝)**: 0-255
- **用途**: 显示器、数字图像的标准颜色空间

### HSL颜色空间
- **Hue (色相)**: 0-360度，颜色在色轮上的位置
- **Saturation (饱和度)**: 0-100%，颜色的纯度
- **Lightness (亮度)**: 0-100%，颜色的明暗程度
- **用途**: 更直观的颜色调整和生成

## 核心函数详解

### 1. convertRgbToHsl() - RGB转HSL
```javascript
function convertRgbToHsl(r, g, b) {
    // 输入验证
    if (typeof (r) !== 'number' || isNaN(r) || r < 0 || r > 255
            || typeof (g) !== 'number' || isNaN(g) || g < 0 || g > 255
            || typeof (b) !== 'number' || isNaN(b) || b < 0 || b > 255) {
        return false;
    }
    
    // 转换算法实现
    // ...
    
    return {
        hue: hue < 0 ? hue + 360 : hue,
        saturation: saturation * 100,
        lightness: lightness * 100,
    };
}
```

**功能特性**:
- **严格验证**: 验证RGB值范围和类型
- **精确算法**: 基于标准HSL转换算法
- **边界处理**: 正确处理色相的360度循环
- **百分比输出**: 饱和度和亮度以百分比形式输出

**使用示例**:
```javascript
// 基本颜色转换
console.log(convertRgbToHsl(255, 0, 0));    // 红色: {hue: 0, saturation: 100, lightness: 50}
console.log(convertRgbToHsl(0, 255, 0));    // 绿色: {hue: 120, saturation: 100, lightness: 50}
console.log(convertRgbToHsl(0, 0, 255));    // 蓝色: {hue: 240, saturation: 100, lightness: 50}
console.log(convertRgbToHsl(255, 255, 255)); // 白色: {hue: 0, saturation: 0, lightness: 100}
console.log(convertRgbToHsl(0, 0, 0));      // 黑色: {hue: 0, saturation: 0, lightness: 0}

// 中间色调
console.log(convertRgbToHsl(128, 128, 128)); // 灰色: {hue: 0, saturation: 0, lightness: 50}
console.log(convertRgbToHsl(255, 128, 0));   // 橙色: {hue: 30, saturation: 100, lightness: 50}
```

**实际应用场景**:
```javascript
// 1. 颜色分析器
class ColorAnalyzer {
    analyzeColor(r, g, b) {
        const hsl = convertRgbToHsl(r, g, b);
        if (!hsl) {
            return { error: '无效的RGB值' };
        }
        
        return {
            rgb: { r, g, b },
            hsl: hsl,
            analysis: {
                colorFamily: this.getColorFamily(hsl.hue),
                brightness: this.getBrightness(hsl.lightness),
                saturation: this.getSaturationLevel(hsl.saturation),
                temperature: this.getColorTemperature(hsl.hue)
            }
        };
    }
    
    getColorFamily(hue) {
        if (hue >= 0 && hue < 30) return '红色系';
        if (hue >= 30 && hue < 60) return '橙色系';
        if (hue >= 60 && hue < 120) return '黄色系';
        if (hue >= 120 && hue < 180) return '绿色系';
        if (hue >= 180 && hue < 240) return '青色系';
        if (hue >= 240 && hue < 300) return '蓝色系';
        if (hue >= 300 && hue < 360) return '紫色系';
        return '无色系';
    }
    
    getBrightness(lightness) {
        if (lightness < 25) return '很暗';
        if (lightness < 50) return '较暗';
        if (lightness < 75) return '较亮';
        return '很亮';
    }
    
    getSaturationLevel(saturation) {
        if (saturation < 25) return '低饱和度';
        if (saturation < 50) return '中低饱和度';
        if (saturation < 75) return '中高饱和度';
        return '高饱和度';
    }
    
    getColorTemperature(hue) {
        // 暖色调: 红、橙、黄 (0-60, 300-360)
        // 冷色调: 绿、青、蓝 (120-240)
        if ((hue >= 0 && hue <= 60) || (hue >= 300 && hue <= 360)) {
            return '暖色调';
        } else if (hue >= 120 && hue <= 240) {
            return '冷色调';
        } else {
            return '中性色调';
        }
    }
}

// 2. 颜色主题生成器
class ColorThemeGenerator {
    generateTheme(baseR, baseG, baseB) {
        const baseHsl = convertRgbToHsl(baseR, baseG, baseB);
        if (!baseHsl) {
            throw new Error('无效的基础颜色');
        }
        
        return {
            primary: { r: baseR, g: baseG, b: baseB },
            secondary: this.generateSecondary(baseHsl),
            accent: this.generateAccent(baseHsl),
            neutral: this.generateNeutral(baseHsl),
            variants: this.generateVariants(baseHsl)
        };
    }
    
    generateSecondary(baseHsl) {
        // 生成互补色 (色相+180度)
        const complementaryHue = (baseHsl.hue + 180) % 360;
        return convertHslToRgb(complementaryHue, baseHsl.saturation, baseHsl.lightness);
    }
    
    generateAccent(baseHsl) {
        // 生成三角色 (色相+120度)
        const triadicHue = (baseHsl.hue + 120) % 360;
        return convertHslToRgb(triadicHue, baseHsl.saturation * 0.8, baseHsl.lightness);
    }
    
    generateNeutral(baseHsl) {
        // 生成中性色 (降低饱和度)
        return convertHslToRgb(baseHsl.hue, baseHsl.saturation * 0.1, baseHsl.lightness);
    }
    
    generateVariants(baseHsl) {
        const variants = {};
        
        // 生成不同亮度的变体
        for (let i = 1; i <= 5; i++) {
            const lightness = Math.min(100, baseHsl.lightness + (i * 10));
            variants[`light${i}`] = convertHslToRgb(baseHsl.hue, baseHsl.saturation, lightness);
            
            const darkness = Math.max(0, baseHsl.lightness - (i * 10));
            variants[`dark${i}`] = convertHslToRgb(baseHsl.hue, baseHsl.saturation, darkness);
        }
        
        return variants;
    }
}

// 3. 颜色可访问性检查器
class ColorAccessibilityChecker {
    checkContrast(foregroundRgb, backgroundRgb) {
        const fgHsl = convertRgbToHsl(foregroundRgb.r, foregroundRgb.g, foregroundRgb.b);
        const bgHsl = convertRgbToHsl(backgroundRgb.r, backgroundRgb.g, backgroundRgb.b);
        
        if (!fgHsl || !bgHsl) {
            return { error: '无效的颜色值' };
        }
        
        // 计算相对亮度
        const fgLuminance = this.calculateLuminance(foregroundRgb);
        const bgLuminance = this.calculateLuminance(backgroundRgb);
        
        // 计算对比度
        const contrastRatio = (Math.max(fgLuminance, bgLuminance) + 0.05) / 
                             (Math.min(fgLuminance, bgLuminance) + 0.05);
        
        return {
            contrastRatio: contrastRatio,
            wcagAA: contrastRatio >= 4.5,
            wcagAAA: contrastRatio >= 7,
            recommendation: this.getContrastRecommendation(contrastRatio)
        };
    }
    
    calculateLuminance(rgb) {
        // 计算相对亮度 (WCAG标准)
        const rsRGB = rgb.r / 255;
        const gsRGB = rgb.g / 255;
        const bsRGB = rgb.b / 255;
        
        const r = rsRGB <= 0.03928 ? rsRGB / 12.92 : Math.pow((rsRGB + 0.055) / 1.055, 2.4);
        const g = gsRGB <= 0.03928 ? gsRGB / 12.92 : Math.pow((gsRGB + 0.055) / 1.055, 2.4);
        const b = bsRGB <= 0.03928 ? bsRGB / 12.92 : Math.pow((bsRGB + 0.055) / 1.055, 2.4);
        
        return 0.2126 * r + 0.7152 * g + 0.0722 * b;
    }
    
    getContrastRecommendation(ratio) {
        if (ratio >= 7) return '优秀 - 符合WCAG AAA标准';
        if (ratio >= 4.5) return '良好 - 符合WCAG AA标准';
        if (ratio >= 3) return '一般 - 仅适用于大文本';
        return '差 - 不符合无障碍标准';
    }
}
```

### 2. convertHslToRgb() - HSL转RGB
```javascript
function convertHslToRgb(h, s, l) {
    // 输入验证
    if (typeof (h) !== 'number' || isNaN(h) || h < 0 || h > 360
            || typeof (s) !== 'number' || isNaN(s) || s < 0 || s > 100
            || typeof (l) !== 'number' || isNaN(l) || l < 0 || l > 100) {
        return false;
    }
    
    // 转换算法实现
    // ...
    
    return {
        red: chroma,
        green: secondComponent,
        blue: lightnessAdjustment,
    };
}
```

**功能特性**:
- **HSL输入**: 接受标准HSL格式输入
- **精确转换**: 基于标准算法的精确转换
- **整数输出**: RGB值输出为0-255的整数
- **色相处理**: 正确处理360度色相环

**使用示例**:
```javascript
// 基本HSL转RGB
console.log(convertHslToRgb(0, 100, 50));    // 红色: {red: 255, green: 0, blue: 0}
console.log(convertHslToRgb(120, 100, 50));  // 绿色: {red: 0, green: 255, blue: 0}
console.log(convertHslToRgb(240, 100, 50));  // 蓝色: {red: 0, green: 0, blue: 255}

// 不同饱和度和亮度
console.log(convertHslToRgb(0, 50, 50));     // 暗红色
console.log(convertHslToRgb(0, 100, 25));    // 深红色
console.log(convertHslToRgb(0, 100, 75));    // 浅红色
```

### 3. convertRgbaToCSSColor() - RGBA转CSS颜色
```javascript
function convertRgbaToCSSColor(r, g, b, a) {
    // 输入验证
    if (typeof (r) !== 'number' || isNaN(r) || r < 0 || r > 255
            || typeof (g) !== 'number' || isNaN(g) || g < 0 || g > 255
            || typeof (b) !== 'number' || isNaN(b) || b < 0 || b > 255) {
        return false;
    }

    // 如果透明度无效或等于100%，返回十六进制格式
    if (typeof (a) !== 'number' || isNaN(a) || a < 0 || Math.abs(a - 100) < Number.EPSILON) {
        const rr = r < 16 ? '0' + r.toString(16) : r.toString(16);
        const gg = g < 16 ? '0' + g.toString(16) : g.toString(16);
        const bb = b < 16 ? '0' + b.toString(16) : b.toString(16);
        return (`#${rr}${gg}${bb}`).toUpperCase();
    }

    // 返回rgba()格式
    return `rgba(${r}, ${g}, ${b}, ${parseFloat((a / 100.0).toFixed(3))})`;
}
```

**功能特性**:
- **智能格式**: 根据透明度自动选择最佳CSS格式
- **十六进制优先**: 不透明颜色使用更简洁的十六进制格式
- **RGBA支持**: 透明颜色使用rgba()函数格式
- **精度控制**: 透明度保留3位小数精度

**使用示例**:
```javascript
// 不透明颜色 -> 十六进制
console.log(convertRgbaToCSSColor(255, 0, 0, 100));    // "#FF0000"
console.log(convertRgbaToCSSColor(0, 255, 0));         // "#00FF00"
console.log(convertRgbaToCSSColor(128, 128, 128, 100)); // "#808080"

// 透明颜色 -> rgba()
console.log(convertRgbaToCSSColor(255, 0, 0, 50));     // "rgba(255, 0, 0, 0.5)"
console.log(convertRgbaToCSSColor(0, 0, 255, 25));     // "rgba(0, 0, 255, 0.25)"
console.log(convertRgbaToCSSColor(255, 255, 255, 0));  // "rgba(255, 255, 255, 0)"
```

### 4. convertCSSColorToRgba() - CSS颜色转RGBA
```javascript
function convertCSSColorToRgba(cssColor) {
    // 检查是否为rgba()或rgb()格式
    const rgba = cssColor.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)$/);
    if (rgba) {
        if (rgba[4] === undefined) {
            rgba[4] = 1;
        }
        return {
            red: parseInt(rgba[1]),
            green: parseInt(rgba[2]),
            blue: parseInt(rgba[3]),
            opacity: Math.round(parseFloat(rgba[4]) * 100),
        };
    }

    // 检查是否为十六进制格式
    if (/^#([0-9A-F]{6}|[0-9A-F]{8})$/i.test(cssColor)) {
        return {
            red: parseInt(cssColor.substr(1, 2), 16),
            green: parseInt(cssColor.substr(3, 2), 16),
            blue: parseInt(cssColor.substr(5, 2), 16),
            opacity: (cssColor.length === 9 ? (parseInt(cssColor.substr(7, 2), 16) / 255) : 1) * 100,
        };
    }

    // 检查是否为color()函数格式
    if (/color\(.+\)/.test(cssColor)) {
        const canvasEl = document.createElement("canvas");
        canvasEl.height = 1;
        canvasEl.width = 1;
        const ctx = canvasEl.getContext("2d");
        ctx.fillStyle = cssColor;
        ctx.fillRect(0, 0, 1, 1);
        const data = ctx.getImageData(0, 0, 1, 1).data;
        return {
            red: data[0],
            green: data[1],
            blue: data[2],
            opacity: data[3] / 2.55, // 转换0-255到百分比
        };
    }

    return false;
}
```

**功能特性**:
- **多格式支持**: 支持rgb()、rgba()、十六进制、color()等格式
- **正则解析**: 使用正则表达式精确解析CSS颜色
- **Canvas回退**: 对于复杂格式使用Canvas API解析
- **透明度处理**: 正确处理各种透明度表示方式

**使用示例**:
```javascript
// RGB/RGBA格式
console.log(convertCSSColorToRgba("rgb(255, 0, 0)"));
// {red: 255, green: 0, blue: 0, opacity: 100}

console.log(convertCSSColorToRgba("rgba(255, 0, 0, 0.5)"));
// {red: 255, green: 0, blue: 0, opacity: 50}

// 十六进制格式
console.log(convertCSSColorToRgba("#FF0000"));
// {red: 255, green: 0, blue: 0, opacity: 100}

console.log(convertCSSColorToRgba("#FF000080"));
// {red: 255, green: 0, blue: 0, opacity: 50}

// Color()函数格式
console.log(convertCSSColorToRgba("color(srgb 1 0 0)"));
// {red: 255, green: 0, blue: 0, opacity: 100}
```

### 5. normalizeCSSColor() - CSS颜色标准化
```javascript
function normalizeCSSColor(cssColor) {
    const rgba = convertCSSColorToRgba(cssColor);
    if (!rgba) {
        return cssColor;
    }
    return convertRgbaToCSSColor(rgba.red, rgba.green, rgba.blue, rgba.opacity);
}
```

**功能特性**:
- **格式统一**: 将各种CSS颜色格式转换为标准格式
- **安全比较**: 标准化后的颜色可以安全地进行字符串比较
- **容错处理**: 无法解析的颜色返回原始值
- **最优格式**: 自动选择最简洁的表示格式

**使用示例**:
```javascript
// 格式标准化
console.log(normalizeCSSColor("rgb(255, 0, 0)"));      // "#FF0000"
console.log(normalizeCSSColor("rgba(255, 0, 0, 1)"));  // "#FF0000"
console.log(normalizeCSSColor("rgba(255, 0, 0, 0.5)")); // "rgba(255, 0, 0, 0.5)"
console.log(normalizeCSSColor("#ff0000"));             // "#FF0000"

// 颜色比较
const color1 = normalizeCSSColor("rgb(255, 0, 0)");
const color2 = normalizeCSSColor("#FF0000");
console.log(color1 === color2); // true
```

### 6. isCSSColor() - CSS颜色验证
```javascript
function isCSSColor(cssColor) {
    return convertCSSColorToRgba(cssColor) !== false;
}
```

**功能特性**:
- **格式验证**: 检查字符串是否为有效的CSS颜色
- **全面支持**: 支持所有convertCSSColorToRgba支持的格式
- **简单接口**: 返回布尔值，易于使用
- **性能优化**: 基于现有解析函数，避免重复实现

**使用示例**:
```javascript
// 有效的CSS颜色
console.log(isCSSColor("#FF0000"));           // true
console.log(isCSSColor("rgb(255, 0, 0)"));    // true
console.log(isCSSColor("rgba(255, 0, 0, 0.5)")); // true
console.log(isCSSColor("color(srgb 1 0 0)")); // true

// 无效的CSS颜色
console.log(isCSSColor("not-a-color"));       // false
console.log(isCSSColor("rgb(300, 0, 0)"));    // false
console.log(isCSSColor("#GGGGGG"));           // false
console.log(isCSSColor(""));                  // false
```

### 7. mixCssColors() - CSS颜色混合
```javascript
function mixCssColors(cssColor1, cssColor2, weight) {
    const rgba1 = convertCSSColorToRgba(cssColor1);
    const rgba2 = convertCSSColorToRgba(cssColor2);
    const rgb1 = [rgba1.red, rgba1.green, rgba1.blue];
    const rgb2 = [rgba2.red, rgba2.green, rgba2.blue];
    const [r, g, b] = rgb1.map((_, idx) => Math.round(rgb2[idx] + (rgb1[idx] - rgb2[idx]) * weight));
    return convertRgbaToCSSColor(r, g, b);
}
```

**功能特性**:
- **加权混合**: 根据权重值混合两种颜色
- **线性插值**: 使用线性插值算法计算混合结果
- **格式灵活**: 接受任何有效的CSS颜色格式
- **标准输出**: 输出标准化的CSS颜色格式

**使用示例**:
```javascript
// 颜色混合
console.log(mixCssColors("#FF0000", "#0000FF", 0.5));   // 红蓝混合50%
console.log(mixCssColors("#FF0000", "#0000FF", 0.25));  // 75%蓝色 + 25%红色
console.log(mixCssColors("#FF0000", "#0000FF", 0.75));  // 25%蓝色 + 75%红色

// 不同格式混合
console.log(mixCssColors("rgb(255, 0, 0)", "#0000FF", 0.5));
console.log(mixCssColors("#FF0000", "rgba(0, 0, 255, 1)", 0.5));
```

## 高级应用模式

### 1. 动态主题系统
```javascript
class DynamicThemeSystem {
    constructor() {
        this.baseColors = {};
        this.generatedTheme = {};
        this.cssVariables = new Map();
    }

    setBaseColor(name, cssColor) {
        if (!isCSSColor(cssColor)) {
            throw new Error(`无效的颜色格式: ${cssColor}`);
        }

        this.baseColors[name] = normalizeCSSColor(cssColor);
        this.regenerateTheme();
    }

    regenerateTheme() {
        this.generatedTheme = {};

        for (const [name, color] of Object.entries(this.baseColors)) {
            const rgba = convertCSSColorToRgba(color);
            const hsl = convertRgbToHsl(rgba.red, rgba.green, rgba.blue);

            // 生成颜色变体
            this.generatedTheme[name] = {
                base: color,
                light: this.generateLightVariant(hsl),
                dark: this.generateDarkVariant(hsl),
                muted: this.generateMutedVariant(hsl),
                contrast: this.generateContrastColor(hsl)
            };
        }

        this.updateCSSVariables();
    }

    generateLightVariant(hsl) {
        const lightHsl = {
            ...hsl,
            lightness: Math.min(95, hsl.lightness + 30),
            saturation: Math.max(10, hsl.saturation - 20)
        };
        const rgb = convertHslToRgb(lightHsl.hue, lightHsl.saturation, lightHsl.lightness);
        return convertRgbaToCSSColor(rgb.red, rgb.green, rgb.blue);
    }

    generateDarkVariant(hsl) {
        const darkHsl = {
            ...hsl,
            lightness: Math.max(5, hsl.lightness - 30),
            saturation: Math.min(100, hsl.saturation + 10)
        };
        const rgb = convertHslToRgb(darkHsl.hue, darkHsl.saturation, darkHsl.lightness);
        return convertRgbaToCSSColor(rgb.red, rgb.green, rgb.blue);
    }

    generateMutedVariant(hsl) {
        const mutedHsl = {
            ...hsl,
            saturation: Math.max(5, hsl.saturation - 40)
        };
        const rgb = convertHslToRgb(mutedHsl.hue, mutedHsl.saturation, mutedHsl.lightness);
        return convertRgbaToCSSColor(rgb.red, rgb.green, rgb.blue);
    }

    generateContrastColor(hsl) {
        // 生成高对比度颜色
        const contrastLightness = hsl.lightness > 50 ? 10 : 90;
        const rgb = convertHslToRgb(hsl.hue, hsl.saturation, contrastLightness);
        return convertRgbaToCSSColor(rgb.red, rgb.green, rgb.blue);
    }

    updateCSSVariables() {
        const root = document.documentElement;

        for (const [colorName, variants] of Object.entries(this.generatedTheme)) {
            for (const [variantName, color] of Object.entries(variants)) {
                const cssVarName = `--color-${colorName}-${variantName}`;
                root.style.setProperty(cssVarName, color);
                this.cssVariables.set(cssVarName, color);
            }
        }
    }

    getTheme() {
        return { ...this.generatedTheme };
    }

    exportThemeCSS() {
        let css = ':root {\n';
        for (const [varName, color] of this.cssVariables) {
            css += `  ${varName}: ${color};\n`;
        }
        css += '}';
        return css;
    }
}

// 使用示例
const themeSystem = new DynamicThemeSystem();
themeSystem.setBaseColor('primary', '#3498db');
themeSystem.setBaseColor('secondary', '#e74c3c');
themeSystem.setBaseColor('success', '#2ecc71');

console.log(themeSystem.getTheme());
console.log(themeSystem.exportThemeCSS());
```

### 2. 颜色渐变生成器
```javascript
class ColorGradientGenerator {
    generateLinearGradient(startColor, endColor, steps = 10) {
        if (!isCSSColor(startColor) || !isCSSColor(endColor)) {
            throw new Error('无效的颜色格式');
        }

        const gradient = [];

        for (let i = 0; i < steps; i++) {
            const weight = i / (steps - 1);
            const mixedColor = mixCssColors(startColor, endColor, weight);
            gradient.push({
                step: i,
                weight: weight,
                color: mixedColor,
                position: `${(weight * 100).toFixed(1)}%`
            });
        }

        return gradient;
    }

    generateRadialGradient(centerColor, edgeColor, steps = 10) {
        return this.generateLinearGradient(centerColor, edgeColor, steps);
    }

    generateHueGradient(saturation = 100, lightness = 50, steps = 12) {
        const gradient = [];
        const hueStep = 360 / steps;

        for (let i = 0; i < steps; i++) {
            const hue = i * hueStep;
            const rgb = convertHslToRgb(hue, saturation, lightness);
            const color = convertRgbaToCSSColor(rgb.red, rgb.green, rgb.blue);

            gradient.push({
                step: i,
                hue: hue,
                color: color,
                position: `${((i / (steps - 1)) * 100).toFixed(1)}%`
            });
        }

        return gradient;
    }

    generateCSSGradient(gradient, direction = 'to right') {
        const colorStops = gradient.map(step => `${step.color} ${step.position}`).join(', ');
        return `linear-gradient(${direction}, ${colorStops})`;
    }

    generateAnalogousColors(baseColor, count = 5, angle = 30) {
        const rgba = convertCSSColorToRgba(baseColor);
        const hsl = convertRgbToHsl(rgba.red, rgba.green, rgba.blue);

        const colors = [];
        const startHue = hsl.hue - (angle * Math.floor(count / 2));

        for (let i = 0; i < count; i++) {
            const hue = (startHue + (i * angle)) % 360;
            const rgb = convertHslToRgb(hue < 0 ? hue + 360 : hue, hsl.saturation, hsl.lightness);
            colors.push(convertRgbaToCSSColor(rgb.red, rgb.green, rgb.blue));
        }

        return colors;
    }
}

// 使用示例
const gradientGen = new ColorGradientGenerator();

// 线性渐变
const blueToRed = gradientGen.generateLinearGradient('#0000FF', '#FF0000', 5);
console.log('蓝到红渐变:', blueToRed);

// 色相渐变
const rainbowGradient = gradientGen.generateHueGradient(100, 50, 7);
console.log('彩虹渐变:', rainbowGradient);

// 生成CSS
const cssGradient = gradientGen.generateCSSGradient(blueToRed, 'to right');
console.log('CSS渐变:', cssGradient);

// 类似色
const analogous = gradientGen.generateAnalogousColors('#3498db', 5, 30);
console.log('类似色:', analogous);
```

### 3. 颜色调色板管理器
```javascript
class ColorPaletteManager {
    constructor() {
        this.palettes = new Map();
        this.history = [];
        this.maxHistorySize = 50;
    }

    createPalette(name, colors = []) {
        const validColors = colors.filter(color => isCSSColor(color))
                                 .map(color => normalizeCSSColor(color));

        const palette = {
            name: name,
            colors: validColors,
            created: new Date(),
            modified: new Date(),
            metadata: this.analyzePalette(validColors)
        };

        this.palettes.set(name, palette);
        this.addToHistory('create', name, palette);

        return palette;
    }

    addColorToPalette(paletteName, color) {
        if (!isCSSColor(color)) {
            throw new Error(`无效的颜色: ${color}`);
        }

        const palette = this.palettes.get(paletteName);
        if (!palette) {
            throw new Error(`调色板不存在: ${paletteName}`);
        }

        const normalizedColor = normalizeCSSColor(color);
        if (!palette.colors.includes(normalizedColor)) {
            palette.colors.push(normalizedColor);
            palette.modified = new Date();
            palette.metadata = this.analyzePalette(palette.colors);

            this.addToHistory('addColor', paletteName, { color: normalizedColor });
        }

        return palette;
    }

    removeColorFromPalette(paletteName, color) {
        const palette = this.palettes.get(paletteName);
        if (!palette) {
            throw new Error(`调色板不存在: ${paletteName}`);
        }

        const normalizedColor = normalizeCSSColor(color);
        const index = palette.colors.indexOf(normalizedColor);

        if (index > -1) {
            palette.colors.splice(index, 1);
            palette.modified = new Date();
            palette.metadata = this.analyzePalette(palette.colors);

            this.addToHistory('removeColor', paletteName, { color: normalizedColor });
        }

        return palette;
    }

    analyzePalette(colors) {
        if (colors.length === 0) {
            return { colorCount: 0, dominantHue: null, averageSaturation: 0, averageLightness: 0 };
        }

        const hslColors = colors.map(color => {
            const rgba = convertCSSColorToRgba(color);
            return convertRgbToHsl(rgba.red, rgba.green, rgba.blue);
        }).filter(hsl => hsl !== false);

        const hues = hslColors.map(hsl => hsl.hue);
        const saturations = hslColors.map(hsl => hsl.saturation);
        const lightnesses = hslColors.map(hsl => hsl.lightness);

        return {
            colorCount: colors.length,
            dominantHue: this.findDominantHue(hues),
            averageSaturation: saturations.reduce((a, b) => a + b, 0) / saturations.length,
            averageLightness: lightnesses.reduce((a, b) => a + b, 0) / lightnesses.length,
            hueRange: Math.max(...hues) - Math.min(...hues),
            saturationRange: Math.max(...saturations) - Math.min(...saturations),
            lightnessRange: Math.max(...lightnesses) - Math.min(...lightnesses)
        };
    }

    findDominantHue(hues) {
        if (hues.length === 0) return null;

        // 将色相分组到30度区间内
        const hueGroups = {};
        hues.forEach(hue => {
            const group = Math.floor(hue / 30) * 30;
            hueGroups[group] = (hueGroups[group] || 0) + 1;
        });

        // 找到最大的组
        let maxCount = 0;
        let dominantHue = 0;

        for (const [hue, count] of Object.entries(hueGroups)) {
            if (count > maxCount) {
                maxCount = count;
                dominantHue = parseInt(hue);
            }
        }

        return dominantHue;
    }

    generateHarmoniousPalette(baseColor, harmonyType = 'complementary') {
        const rgba = convertCSSColorToRgba(baseColor);
        const hsl = convertRgbToHsl(rgba.red, rgba.green, rgba.blue);

        let colors = [baseColor];

        switch (harmonyType) {
            case 'complementary':
                colors.push(this.generateComplementary(hsl));
                break;
            case 'triadic':
                colors.push(...this.generateTriadic(hsl));
                break;
            case 'analogous':
                colors.push(...this.generateAnalogous(hsl));
                break;
            case 'split-complementary':
                colors.push(...this.generateSplitComplementary(hsl));
                break;
            case 'tetradic':
                colors.push(...this.generateTetradic(hsl));
                break;
        }

        return colors;
    }

    generateComplementary(hsl) {
        const complementaryHue = (hsl.hue + 180) % 360;
        const rgb = convertHslToRgb(complementaryHue, hsl.saturation, hsl.lightness);
        return convertRgbaToCSSColor(rgb.red, rgb.green, rgb.blue);
    }

    generateTriadic(hsl) {
        const hue1 = (hsl.hue + 120) % 360;
        const hue2 = (hsl.hue + 240) % 360;

        const rgb1 = convertHslToRgb(hue1, hsl.saturation, hsl.lightness);
        const rgb2 = convertHslToRgb(hue2, hsl.saturation, hsl.lightness);

        return [
            convertRgbaToCSSColor(rgb1.red, rgb1.green, rgb1.blue),
            convertRgbaToCSSColor(rgb2.red, rgb2.green, rgb2.blue)
        ];
    }

    generateAnalogous(hsl) {
        const hue1 = (hsl.hue + 30) % 360;
        const hue2 = (hsl.hue - 30 + 360) % 360;

        const rgb1 = convertHslToRgb(hue1, hsl.saturation, hsl.lightness);
        const rgb2 = convertHslToRgb(hue2, hsl.saturation, hsl.lightness);

        return [
            convertRgbaToCSSColor(rgb1.red, rgb1.green, rgb1.blue),
            convertRgbaToCSSColor(rgb2.red, rgb2.green, rgb2.blue)
        ];
    }

    addToHistory(action, paletteName, data) {
        this.history.push({
            action,
            paletteName,
            data,
            timestamp: new Date()
        });

        if (this.history.length > this.maxHistorySize) {
            this.history.shift();
        }
    }

    exportPalette(paletteName, format = 'json') {
        const palette = this.palettes.get(paletteName);
        if (!palette) {
            throw new Error(`调色板不存在: ${paletteName}`);
        }

        switch (format) {
            case 'json':
                return JSON.stringify(palette, null, 2);
            case 'css':
                return this.exportToCSSVariables(palette);
            case 'scss':
                return this.exportToSCSSVariables(palette);
            case 'ase':
                return this.exportToASE(palette);
            default:
                throw new Error(`不支持的导出格式: ${format}`);
        }
    }

    exportToCSSVariables(palette) {
        let css = `:root {\n`;
        palette.colors.forEach((color, index) => {
            css += `  --${palette.name}-${index + 1}: ${color};\n`;
        });
        css += `}`;
        return css;
    }

    exportToSCSSVariables(palette) {
        let scss = '';
        palette.colors.forEach((color, index) => {
            scss += `$${palette.name}-${index + 1}: ${color};\n`;
        });
        return scss;
    }
}
```

## 最佳实践

### 1. 性能优化
```javascript
// ✅ 推荐：缓存颜色转换结果
class OptimizedColorConverter {
    constructor() {
        this.conversionCache = new Map();
        this.maxCacheSize = 1000;
    }

    convertWithCache(color, targetFormat) {
        const cacheKey = `${color}_${targetFormat}`;

        if (this.conversionCache.has(cacheKey)) {
            return this.conversionCache.get(cacheKey);
        }

        let result;
        switch (targetFormat) {
            case 'rgba':
                result = convertCSSColorToRgba(color);
                break;
            case 'normalized':
                result = normalizeCSSColor(color);
                break;
            default:
                result = color;
        }

        // 限制缓存大小
        if (this.conversionCache.size >= this.maxCacheSize) {
            const firstKey = this.conversionCache.keys().next().value;
            this.conversionCache.delete(firstKey);
        }

        this.conversionCache.set(cacheKey, result);
        return result;
    }
}

// ✅ 推荐：批量颜色处理
function processColorsBatch(colors, processor) {
    return colors.map(color => {
        try {
            return processor(color);
        } catch (error) {
            console.warn(`颜色处理失败: ${color}`, error);
            return color; // 返回原始颜色作为回退
        }
    });
}
```

### 2. 错误处理
```javascript
// ✅ 推荐：安全的颜色处理
function safeColorConversion(color, converter) {
    try {
        if (!isCSSColor(color)) {
            throw new Error(`无效的CSS颜色: ${color}`);
        }

        return converter(color);
    } catch (error) {
        console.error('颜色转换失败:', error);
        return null;
    }
}

// ✅ 推荐：输入验证
function validateColorInput(color) {
    if (typeof color !== 'string') {
        return { valid: false, error: '颜色必须是字符串' };
    }

    if (!isCSSColor(color)) {
        return { valid: false, error: '无效的CSS颜色格式' };
    }

    return { valid: true };
}
```

### 3. 可访问性考虑
```javascript
// ✅ 推荐：确保颜色对比度
function ensureAccessibleContrast(foreground, background, minRatio = 4.5) {
    const fgRgba = convertCSSColorToRgba(foreground);
    const bgRgba = convertCSSColorToRgba(background);

    // 计算当前对比度
    const currentRatio = calculateContrastRatio(fgRgba, bgRgba);

    if (currentRatio >= minRatio) {
        return foreground; // 已满足要求
    }

    // 调整前景色以满足对比度要求
    return adjustColorForContrast(fgRgba, bgRgba, minRatio);
}

function calculateContrastRatio(color1, color2) {
    const l1 = calculateLuminance(color1);
    const l2 = calculateLuminance(color2);

    const lighter = Math.max(l1, l2);
    const darker = Math.min(l1, l2);

    return (lighter + 0.05) / (darker + 0.05);
}
```

## 总结

Odoo 颜色工具模块提供了完整的颜色处理和转换功能：

**核心优势**:
- **多格式支持**: 支持RGB、HSL、十六进制、rgba()等多种格式
- **精确转换**: 基于标准算法的精确颜色空间转换
- **智能格式化**: 自动选择最优的CSS颜色表示格式
- **颜色混合**: 支持颜色混合和渐变生成
- **格式验证**: 完整的CSS颜色格式验证

**适用场景**:
- 动态主题系统
- 颜色选择器组件
- 数据可视化图表
- UI组件样式生成
- 品牌色彩管理

**设计优势**:
- 严格的输入验证
- 容错处理机制
- 性能优化考虑
- 可访问性支持

这个颜色工具为 Odoo Web 客户端提供了强大的颜色处理能力，是构建现代用户界面和可视化应用的重要基础。

# Odoo URL工具 (URL Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/urls.js`  
**原始路径**: `/web/static/src/core/utils/urls.js`  
**模块类型**: 核心工具模块 - URL处理工具  
**代码行数**: 163 行  
**依赖关系**: 
- `@web/session` - 会话管理
- `@web/core/browser/browser` - 浏览器工具
- `@web/core/utils/objects` - 对象工具 (shallowEqual)
- `luxon` - 日期时间库

## 模块功能

URL工具模块是 Odoo Web 客户端的核心工具库，提供了完整的URL处理和操作功能。该模块支持：
- URL构建和参数编码
- 图片URL生成
- 安全重定向
- URL比较和验证
- 文件数据URL转换
- 跨域安全检查

这些功能是构建Web应用URL管理的重要基础，广泛应用于路由、资源加载、文件处理等场景。

## URL处理原理

### URL构建流程
1. **路由处理**: 处理相对路径和绝对路径
2. **参数编码**: 将对象参数转换为URL查询字符串
3. **域名处理**: 自动添加域名前缀或保持跨域URL
4. **安全检查**: 验证URL的安全性和有效性

### 编码规则
- **URI编码**: 使用 `encodeURIComponent` 进行安全编码
- **参数格式**: `key=value&key2=value2` 格式
- **空值处理**: 空值转换为空字符串
- **特殊字符**: 正确处理特殊字符和Unicode

### 安全机制
- **同源检查**: 重定向时检查同源策略
- **XSS防护**: 防止恶意URL注入
- **CSRF保护**: 确保请求来源的合法性

## 核心函数详解

### 1. objectToUrlEncodedString() - 对象转URL编码字符串
```javascript
function objectToUrlEncodedString(obj) {
    return Object.entries(obj)
        .map(([k, v]) => `${encodeURIComponent(k)}=${encodeURIComponent(v || "")}`)
        .join("&");
}
```

**功能特性**:
- **对象转换**: 将JavaScript对象转换为URL查询字符串
- **安全编码**: 使用encodeURIComponent确保安全编码
- **空值处理**: 自动处理null/undefined值
- **键值对**: 标准的key=value&key2=value2格式
- **特殊字符**: 正确处理特殊字符和Unicode

**使用示例**:
```javascript
// 基本对象转换
const params = { name: 'John Doe', age: 30, city: 'New York' };
const queryString = objectToUrlEncodedString(params);
console.log(queryString); // "name=John%20Doe&age=30&city=New%20York"

// 包含特殊字符
const specialParams = { 
    search: 'hello world!', 
    filter: 'type=user&status=active',
    unicode: '你好世界'
};
const encoded = objectToUrlEncodedString(specialParams);
console.log(encoded); // 正确编码的查询字符串

// 空值处理
const withNulls = { name: 'John', age: null, city: undefined, active: true };
const result = objectToUrlEncodedString(withNulls);
console.log(result); // "name=John&age=&city=&active=true"
```

**实际应用场景**:
```javascript
// 1. API请求参数构建器
class APIRequestBuilder {
    constructor(baseUrl) {
        this.baseUrl = baseUrl;
        this.params = {};
        this.headers = {};
    }
    
    addParam(key, value) {
        this.params[key] = value;
        return this;
    }
    
    addParams(params) {
        Object.assign(this.params, params);
        return this;
    }
    
    buildUrl() {
        const queryString = objectToUrlEncodedString(this.params);
        return queryString ? `${this.baseUrl}?${queryString}` : this.baseUrl;
    }
    
    async get() {
        const url = this.buildUrl();
        const response = await fetch(url, { headers: this.headers });
        return response.json();
    }
    
    async post(data) {
        const response = await fetch(this.baseUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...this.headers
            },
            body: JSON.stringify(data)
        });
        return response.json();
    }
}

// 使用示例
const api = new APIRequestBuilder('/api/users');
const users = await api
    .addParam('page', 1)
    .addParam('limit', 20)
    .addParam('search', 'john')
    .addParams({ status: 'active', role: 'admin' })
    .get();

// 2. 表单数据序列化器
class FormSerializer {
    constructor(form) {
        this.form = form;
    }
    
    serialize() {
        const formData = new FormData(this.form);
        const data = {};
        
        for (const [key, value] of formData.entries()) {
            if (data[key]) {
                // 处理多值字段
                if (Array.isArray(data[key])) {
                    data[key].push(value);
                } else {
                    data[key] = [data[key], value];
                }
            } else {
                data[key] = value;
            }
        }
        
        return objectToUrlEncodedString(data);
    }
    
    serializeToObject() {
        const formData = new FormData(this.form);
        const data = {};
        
        for (const [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        return data;
    }
    
    submitAsUrlEncoded() {
        const serialized = this.serialize();
        
        return fetch(this.form.action, {
            method: this.form.method || 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: serialized
        });
    }
}

// 3. 搜索参数管理器
class SearchParamsManager {
    constructor() {
        this.params = new URLSearchParams(window.location.search);
        this.listeners = new Set();
    }
    
    get(key) {
        return this.params.get(key);
    }
    
    set(key, value) {
        this.params.set(key, value);
        this.updateUrl();
        this.notifyListeners();
        return this;
    }
    
    setMultiple(params) {
        Object.entries(params).forEach(([key, value]) => {
            this.params.set(key, value);
        });
        this.updateUrl();
        this.notifyListeners();
        return this;
    }
    
    delete(key) {
        this.params.delete(key);
        this.updateUrl();
        this.notifyListeners();
        return this;
    }
    
    clear() {
        this.params = new URLSearchParams();
        this.updateUrl();
        this.notifyListeners();
        return this;
    }
    
    toObject() {
        const obj = {};
        for (const [key, value] of this.params.entries()) {
            obj[key] = value;
        }
        return obj;
    }
    
    toString() {
        return objectToUrlEncodedString(this.toObject());
    }
    
    updateUrl() {
        const newUrl = `${window.location.pathname}?${this.params.toString()}`;
        window.history.replaceState({}, '', newUrl);
    }
    
    addListener(callback) {
        this.listeners.add(callback);
        return () => this.listeners.delete(callback);
    }
    
    notifyListeners() {
        this.listeners.forEach(callback => {
            try {
                callback(this.toObject());
            } catch (error) {
                console.error('搜索参数监听器错误:', error);
            }
        });
    }
}

// 使用示例
const searchParams = new SearchParamsManager();

// 监听参数变化
searchParams.addListener((params) => {
    console.log('搜索参数已更新:', params);
    updateSearchResults(params);
});

// 更新参数
searchParams
    .set('query', 'javascript')
    .set('category', 'programming')
    .set('page', 1);
```

### 2. getOrigin() - 获取源URL
```javascript
function getOrigin(origin) {
    if (origin) {
        // remove trailing slashes
        origin = origin.replace(/\/+$/, "");
    } else {
        const { host, protocol } = browser.location;
        origin = `${protocol}//${host}`;
    }
    return origin;
}
```

**功能特性**:
- **自动获取**: 自动获取当前页面的源URL
- **清理处理**: 移除尾部斜杠确保一致性
- **参数覆盖**: 支持手动指定源URL
- **协议包含**: 包含完整的协议和主机信息
- **标准化**: 返回标准化的源URL格式

### 3. url() - URL构建
```javascript
function url(route, queryParams, options = {}) {
    const origin = getOrigin(options.origin ?? session.origin);
    if (!route) {
        return origin;
    }

    let queryString = objectToUrlEncodedString(queryParams || {});
    queryString = queryString.length > 0 ? `?${queryString}` : queryString;

    // Compare the wanted url against the current origin
    let prefix = ["http://", "https://", "//"].some(
        (el) => route.length >= el.length && route.slice(0, el.length) === el
    );
    prefix = prefix ? "" : origin;
    return `${prefix}${route}${queryString}`;
}
```

**功能特性**:
- **智能前缀**: 自动判断是否需要添加域名前缀
- **参数处理**: 自动处理查询参数的编码和拼接
- **跨域支持**: 支持绝对URL和相对URL
- **会话集成**: 集成会话管理的源URL配置
- **选项配置**: 支持自定义源URL选项

**使用示例**:
```javascript
// 相对路径
const relativeUrl = url('/api/users', { page: 1, limit: 20 });
console.log(relativeUrl); // "https://example.com/api/users?page=1&limit=20"

// 绝对路径
const absoluteUrl = url('https://api.external.com/data', { token: 'abc123' });
console.log(absoluteUrl); // "https://api.external.com/data?token=abc123"

// 无参数
const simpleUrl = url('/dashboard');
console.log(simpleUrl); // "https://example.com/dashboard"

// 自定义源
const customUrl = url('/api/data', { id: 123 }, { origin: 'https://custom.com' });
console.log(customUrl); // "https://custom.com/api/data?id=123"
```

### 4. imageUrl() - 图片URL生成
```javascript
function imageUrl(model, id, field, { filename, height, unique, width } = {}) {
    let route = `/web/image/${model}/${id}/${field}`;
    if (width && height) {
        route = `${route}/${width}x${height}`;
    }
    if (filename) {
        route = `${route}/${filename}`;
    }
    const urlParams = {};
    if (unique) {
        if (unique instanceof DateTime) {
            urlParams.unique = unique.ts;
        } else {
            const dateTimeFromUnique = DateTime.fromSQL(unique);
            if (dateTimeFromUnique.isValid) {
                urlParams.unique = dateTimeFromUnique.ts;
            } else if (typeof unique === "string" && unique.length > 0) {
                urlParams.unique = unique;
            }
        }
    }
    return url(route, urlParams);
}
```

**功能特性**:
- **模型集成**: 基于Odoo模型和字段生成图片URL
- **尺寸控制**: 支持指定图片的宽度和高度
- **文件名**: 支持自定义下载文件名
- **缓存控制**: 通过unique参数控制缓存
- **时间戳**: 支持DateTime对象和SQL时间格式

**使用示例**:
```javascript
// 基本图片URL
const basicImageUrl = imageUrl('res.partner', 123, 'image_1920');
console.log(basicImageUrl); // "/web/image/res.partner/123/image_1920"

// 指定尺寸
const resizedImageUrl = imageUrl('product.product', 456, 'image', {
    width: 300,
    height: 200
});
console.log(resizedImageUrl); // "/web/image/product.product/456/image/300x200"

// 带文件名和缓存控制
const namedImageUrl = imageUrl('hr.employee', 789, 'avatar', {
    filename: 'employee_avatar.jpg',
    unique: new Date().getTime()
});

// 使用DateTime对象
const { DateTime } = luxon;
const timestampedUrl = imageUrl('res.company', 1, 'logo', {
    unique: DateTime.now()
});
```

**实际应用场景**:
```javascript
// 1. 图片组件管理器
class ImageManager {
    constructor() {
        this.cache = new Map();
        this.loadingImages = new Set();
    }

    generateImageUrl(model, id, field, options = {}) {
        const cacheKey = this.getCacheKey(model, id, field, options);

        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        const url = imageUrl(model, id, field, options);
        this.cache.set(cacheKey, url);

        return url;
    }

    async loadImage(model, id, field, options = {}) {
        const url = this.generateImageUrl(model, id, field, options);
        const cacheKey = this.getCacheKey(model, id, field, options);

        if (this.loadingImages.has(cacheKey)) {
            return this.waitForImageLoad(cacheKey);
        }

        this.loadingImages.add(cacheKey);

        try {
            const img = new Image();
            img.crossOrigin = 'anonymous';

            const loadPromise = new Promise((resolve, reject) => {
                img.onload = () => resolve(img);
                img.onerror = reject;
            });

            img.src = url;
            const loadedImage = await loadPromise;

            this.loadingImages.delete(cacheKey);
            return loadedImage;

        } catch (error) {
            this.loadingImages.delete(cacheKey);
            throw error;
        }
    }

    preloadImages(imageConfigs) {
        const promises = imageConfigs.map(config =>
            this.loadImage(config.model, config.id, config.field, config.options)
                .catch(error => {
                    console.warn('图片预加载失败:', config, error);
                    return null;
                })
        );

        return Promise.allSettled(promises);
    }

    getCacheKey(model, id, field, options) {
        return `${model}_${id}_${field}_${JSON.stringify(options)}`;
    }

    clearCache() {
        this.cache.clear();
    }
}

// 2. 响应式图片组件
class ResponsiveImage {
    constructor(container, model, id, field, options = {}) {
        this.container = container;
        this.model = model;
        this.id = id;
        this.field = field;
        this.options = options;

        this.breakpoints = {
            xs: { width: 150, height: 150 },
            sm: { width: 200, height: 200 },
            md: { width: 300, height: 300 },
            lg: { width: 400, height: 400 },
            xl: { width: 500, height: 500 }
        };

        this.currentBreakpoint = this.getCurrentBreakpoint();
        this.setupImage();
        this.setupResizeListener();
    }

    getCurrentBreakpoint() {
        const width = window.innerWidth;

        if (width < 576) return 'xs';
        if (width < 768) return 'sm';
        if (width < 992) return 'md';
        if (width < 1200) return 'lg';
        return 'xl';
    }

    setupImage() {
        const img = document.createElement('img');
        img.className = 'responsive-image';
        img.alt = this.options.alt || '';

        this.updateImageSrc(img);
        this.container.appendChild(img);

        this.img = img;
    }

    updateImageSrc(img) {
        const breakpoint = this.breakpoints[this.currentBreakpoint];
        const url = imageUrl(this.model, this.id, this.field, {
            ...this.options,
            width: breakpoint.width,
            height: breakpoint.height
        });

        img.src = url;
    }

    setupResizeListener() {
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                const newBreakpoint = this.getCurrentBreakpoint();
                if (newBreakpoint !== this.currentBreakpoint) {
                    this.currentBreakpoint = newBreakpoint;
                    this.updateImageSrc(this.img);
                }
            }, 100);
        });
    }
}

// 3. 图片画廊管理器
class ImageGallery {
    constructor(container) {
        this.container = container;
        this.images = [];
        this.currentIndex = 0;
        this.isFullscreen = false;

        this.setupGallery();
    }

    addImage(model, id, field, options = {}) {
        const imageConfig = { model, id, field, options };
        this.images.push(imageConfig);
        this.renderThumbnail(imageConfig, this.images.length - 1);
    }

    renderThumbnail(imageConfig, index) {
        const thumbnail = document.createElement('div');
        thumbnail.className = 'gallery-thumbnail';
        thumbnail.dataset.index = index;

        const img = document.createElement('img');
        img.src = imageUrl(imageConfig.model, imageConfig.id, imageConfig.field, {
            ...imageConfig.options,
            width: 150,
            height: 150
        });

        img.addEventListener('click', () => {
            this.showFullsize(index);
        });

        thumbnail.appendChild(img);
        this.container.appendChild(thumbnail);
    }

    showFullsize(index) {
        this.currentIndex = index;
        const imageConfig = this.images[index];

        const fullsizeUrl = imageUrl(imageConfig.model, imageConfig.id, imageConfig.field, {
            ...imageConfig.options,
            width: 1200,
            height: 800
        });

        this.createFullsizeOverlay(fullsizeUrl);
    }

    createFullsizeOverlay(imageUrl) {
        const overlay = document.createElement('div');
        overlay.className = 'gallery-overlay';

        const img = document.createElement('img');
        img.src = imageUrl;
        img.className = 'gallery-fullsize';

        const closeBtn = document.createElement('button');
        closeBtn.textContent = '×';
        closeBtn.className = 'gallery-close';
        closeBtn.addEventListener('click', () => {
            this.closeFullsize();
        });

        overlay.appendChild(img);
        overlay.appendChild(closeBtn);
        document.body.appendChild(overlay);

        this.overlay = overlay;
        this.isFullscreen = true;

        // 键盘导航
        this.setupKeyboardNavigation();
    }

    closeFullsize() {
        if (this.overlay) {
            this.overlay.remove();
            this.overlay = null;
        }
        this.isFullscreen = false;
        this.removeKeyboardNavigation();
    }

    setupKeyboardNavigation() {
        this.keyHandler = (e) => {
            if (!this.isFullscreen) return;

            switch (e.key) {
                case 'Escape':
                    this.closeFullsize();
                    break;
                case 'ArrowLeft':
                    this.previousImage();
                    break;
                case 'ArrowRight':
                    this.nextImage();
                    break;
            }
        };

        document.addEventListener('keydown', this.keyHandler);
    }

    removeKeyboardNavigation() {
        if (this.keyHandler) {
            document.removeEventListener('keydown', this.keyHandler);
            this.keyHandler = null;
        }
    }

    previousImage() {
        if (this.currentIndex > 0) {
            this.showFullsize(this.currentIndex - 1);
        }
    }

    nextImage() {
        if (this.currentIndex < this.images.length - 1) {
            this.showFullsize(this.currentIndex + 1);
        }
    }
}
```

### 5. getDataURLFromFile() - 文件转数据URL
```javascript
function getDataURLFromFile(file) {
    if (!file) {
        return Promise.reject();
    }
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.addEventListener("load", () => {
            // Handle Chrome bug that creates invalid data URLs for empty files
            if (reader.result === "data:") {
                resolve(`data:${file.type};base64,`);
            } else {
                resolve(reader.result);
            }
        });
        reader.addEventListener("abort", reject);
        reader.addEventListener("error", reject);
        reader.readAsDataURL(file);
    });
}
```

**功能特性**:
- **文件读取**: 将File或Blob对象转换为base64数据URL
- **Promise封装**: 使用Promise包装FileReader API
- **错误处理**: 完整的错误和中断处理
- **Chrome兼容**: 处理Chrome浏览器的空文件bug
- **类型保留**: 保留原始文件的MIME类型

**使用示例**:
```javascript
// 基本文件转换
const fileInput = document.querySelector('#file-input');
fileInput.addEventListener('change', async (e) => {
    const file = e.target.files[0];
    if (file) {
        try {
            const dataURL = await getDataURLFromFile(file);
            console.log('文件数据URL:', dataURL);

            // 显示图片预览
            const img = document.createElement('img');
            img.src = dataURL;
            document.body.appendChild(img);
        } catch (error) {
            console.error('文件读取失败:', error);
        }
    }
});

// 批量文件处理
async function processFiles(files) {
    const results = [];

    for (const file of files) {
        try {
            const dataURL = await getDataURLFromFile(file);
            results.push({
                file: file,
                dataURL: dataURL,
                success: true
            });
        } catch (error) {
            results.push({
                file: file,
                error: error,
                success: false
            });
        }
    }

    return results;
}
```

### 6. redirect() - 安全重定向
```javascript
function redirect(url) {
    const { origin, pathname } = browser.location;
    const _url = new URL(url, `${origin}${pathname}`);
    if (_url.origin !== origin) {
        throw new RedirectionError("Can't redirect to another origin");
    }
    browser.location.assign(_url.href);
}
```

**功能特性**:
- **同源检查**: 严格检查重定向URL的同源性
- **安全防护**: 防止恶意重定向到外部站点
- **URL解析**: 使用标准URL API进行解析
- **错误抛出**: 跨域重定向时抛出RedirectionError
- **浏览器集成**: 使用浏览器location API执行重定向

### 7. compareUrls() - URL比较
```javascript
function compareUrls(_url1, _url2) {
    const url1 = new URL(_url1);
    const url2 = new URL(_url2);
    return (
        url1.origin === url2.origin &&
        url1.pathname === url2.pathname &&
        shallowEqual(
            Object.fromEntries(url1.searchParams),
            Object.fromEntries(url2.searchParams)
        ) &&
        url1.hash === url2.hash
    );
}
```

**功能特性**:
- **完整比较**: 比较URL的所有组成部分
- **参数无序**: 查询参数顺序不影响比较结果
- **哈希包含**: 包含URL哈希部分的比较
- **标准解析**: 使用标准URL API确保准确性
- **浅层比较**: 使用浅层比较优化性能

**使用示例**:
```javascript
// URL比较
const url1 = 'https://example.com/page?a=1&b=2#section1';
const url2 = 'https://example.com/page?b=2&a=1#section1';
const url3 = 'https://example.com/page?a=1&b=2#section2';

console.log(compareUrls(url1, url2)); // true (参数顺序不同但内容相同)
console.log(compareUrls(url1, url3)); // false (哈希不同)

// 安全重定向
try {
    redirect('/dashboard?tab=overview');
    console.log('重定向成功');
} catch (error) {
    if (error instanceof RedirectionError) {
        console.error('重定向被阻止:', error.message);
    }
}

// 跨域重定向会失败
try {
    redirect('https://malicious-site.com/steal-data');
} catch (error) {
    console.error('跨域重定向被阻止:', error.message);
}
```

## 高级应用模式

### 1. URL路由管理器
```javascript
class URLRouter {
    constructor() {
        this.routes = new Map();
        this.middlewares = [];
        this.currentRoute = null;

        this.setupPopstateListener();
    }

    addRoute(pattern, handler, options = {}) {
        const route = {
            pattern: this.compilePattern(pattern),
            handler: handler,
            middleware: options.middleware || [],
            name: options.name
        };

        this.routes.set(pattern, route);
        return this;
    }

    addMiddleware(middleware) {
        this.middlewares.push(middleware);
        return this;
    }

    navigate(path, params = {}, options = {}) {
        const fullUrl = url(path, params, options);

        if (options.replace) {
            window.history.replaceState({}, '', fullUrl);
        } else {
            window.history.pushState({}, '', fullUrl);
        }

        this.handleRoute(fullUrl);
    }

    handleRoute(fullUrl) {
        const urlObj = new URL(fullUrl);
        const path = urlObj.pathname;

        for (const [pattern, route] of this.routes) {
            const match = this.matchPattern(route.pattern, path);
            if (match) {
                this.executeRoute(route, match, urlObj);
                return;
            }
        }

        this.handle404(path);
    }

    executeRoute(route, match, urlObj) {
        const context = {
            params: match.params,
            query: Object.fromEntries(urlObj.searchParams),
            hash: urlObj.hash,
            url: urlObj.href,
            route: route
        };

        // 执行中间件
        this.executeMiddlewares([...this.middlewares, ...route.middleware], context)
            .then(() => {
                // 执行路由处理器
                return route.handler(context);
            })
            .then(() => {
                this.currentRoute = route;
            })
            .catch(error => {
                console.error('路由执行失败:', error);
                this.handleError(error, context);
            });
    }

    async executeMiddlewares(middlewares, context) {
        for (const middleware of middlewares) {
            await middleware(context);
        }
    }

    compilePattern(pattern) {
        const paramNames = [];
        const regexPattern = pattern.replace(/:([^/]+)/g, (match, paramName) => {
            paramNames.push(paramName);
            return '([^/]+)';
        });

        return {
            regex: new RegExp(`^${regexPattern}$`),
            paramNames: paramNames
        };
    }

    matchPattern(compiledPattern, path) {
        const match = path.match(compiledPattern.regex);
        if (!match) return null;

        const params = {};
        compiledPattern.paramNames.forEach((name, index) => {
            params[name] = match[index + 1];
        });

        return { params };
    }

    setupPopstateListener() {
        window.addEventListener('popstate', () => {
            this.handleRoute(window.location.href);
        });
    }

    handle404(path) {
        console.warn(`404: 路由未找到 - ${path}`);
        // 可以重定向到404页面或显示错误信息
    }

    handleError(error, context) {
        console.error('路由错误:', error, context);
        // 可以重定向到错误页面或显示错误信息
    }
}

// 使用示例
const router = new URLRouter();

// 添加中间件
router.addMiddleware(async (context) => {
    console.log('访问路由:', context.url);
    // 可以进行权限检查、日志记录等
});

// 添加路由
router
    .addRoute('/users/:id', async (context) => {
        const userId = context.params.id;
        console.log('显示用户:', userId);
        // 加载和显示用户信息
    }, { name: 'user-detail' })
    .addRoute('/products/:category/:id', async (context) => {
        const { category, id } = context.params;
        console.log('显示产品:', category, id);
        // 加载和显示产品信息
    })
    .addRoute('/search', async (context) => {
        const query = context.query.q;
        console.log('搜索:', query);
        // 执行搜索
    });

// 导航
router.navigate('/users/123');
router.navigate('/search', { q: 'javascript', category: 'books' });
```

### 2. URL状态管理器
```javascript
class URLStateManager {
    constructor() {
        this.state = this.parseCurrentUrl();
        this.listeners = new Set();
        this.history = [];
        this.maxHistorySize = 50;

        this.setupPopstateListener();
    }

    parseCurrentUrl() {
        const urlObj = new URL(window.location.href);
        return {
            pathname: urlObj.pathname,
            search: Object.fromEntries(urlObj.searchParams),
            hash: urlObj.hash.substring(1)
        };
    }

    updateState(updates, options = {}) {
        const newState = { ...this.state, ...updates };

        // 构建新URL
        const newUrl = this.buildUrl(newState);

        // 更新浏览器历史
        if (options.replace) {
            window.history.replaceState(newState, '', newUrl);
        } else {
            window.history.pushState(newState, '', newUrl);
        }

        // 保存到历史记录
        this.addToHistory(this.state);

        // 更新状态
        const oldState = this.state;
        this.state = newState;

        // 通知监听器
        this.notifyListeners(newState, oldState);
    }

    buildUrl(state) {
        const searchParams = objectToUrlEncodedString(state.search);
        const search = searchParams ? `?${searchParams}` : '';
        const hash = state.hash ? `#${state.hash}` : '';

        return `${state.pathname}${search}${hash}`;
    }

    setPath(pathname) {
        this.updateState({ pathname });
    }

    setSearch(search) {
        this.updateState({ search });
    }

    setHash(hash) {
        this.updateState({ hash });
    }

    setSearchParam(key, value) {
        const newSearch = { ...this.state.search };
        if (value === null || value === undefined) {
            delete newSearch[key];
        } else {
            newSearch[key] = value;
        }
        this.updateState({ search: newSearch });
    }

    getSearchParam(key) {
        return this.state.search[key];
    }

    addListener(callback) {
        this.listeners.add(callback);
        return () => this.listeners.delete(callback);
    }

    notifyListeners(newState, oldState) {
        this.listeners.forEach(callback => {
            try {
                callback(newState, oldState);
            } catch (error) {
                console.error('URL状态监听器错误:', error);
            }
        });
    }

    setupPopstateListener() {
        window.addEventListener('popstate', (event) => {
            const oldState = this.state;
            this.state = event.state || this.parseCurrentUrl();
            this.notifyListeners(this.state, oldState);
        });
    }

    addToHistory(state) {
        this.history.unshift({ ...state, timestamp: Date.now() });

        if (this.history.length > this.maxHistorySize) {
            this.history = this.history.slice(0, this.maxHistorySize);
        }
    }

    getHistory() {
        return [...this.history];
    }

    canGoBack() {
        return this.history.length > 0;
    }

    goBack() {
        if (this.canGoBack()) {
            const previousState = this.history[0];
            this.updateState(previousState, { replace: true });
        }
    }
}

// 使用示例
const urlState = new URLStateManager();

// 监听状态变化
urlState.addListener((newState, oldState) => {
    console.log('URL状态变化:', { newState, oldState });
    updatePageContent(newState);
});

// 更新状态
urlState.setSearchParam('page', 2);
urlState.setSearchParam('filter', 'active');
urlState.setHash('section1');
```

## 最佳实践

### 1. 安全性
```javascript
// ✅ 推荐：安全的URL处理
function safeRedirect(url) {
    try {
        // 验证URL格式
        const urlObj = new URL(url, window.location.origin);

        // 检查协议
        if (!['http:', 'https:'].includes(urlObj.protocol)) {
            throw new Error('不支持的协议');
        }

        // 执行重定向
        redirect(urlObj.href);
    } catch (error) {
        console.error('重定向失败:', error);
        // 可以重定向到默认页面
        redirect('/');
    }
}

// ✅ 推荐：参数验证
function validateUrlParams(params) {
    const validated = {};

    Object.entries(params).forEach(([key, value]) => {
        // 验证键名
        if (!/^[a-zA-Z0-9_-]+$/.test(key)) {
            console.warn(`无效的参数键: ${key}`);
            return;
        }

        // 验证值
        if (typeof value === 'string' && value.length > 1000) {
            console.warn(`参数值过长: ${key}`);
            return;
        }

        validated[key] = value;
    });

    return validated;
}
```

### 2. 性能优化
```javascript
// ✅ 推荐：URL缓存
const urlCache = new Map();
function cachedUrl(route, params, options) {
    const cacheKey = JSON.stringify({ route, params, options });

    if (urlCache.has(cacheKey)) {
        return urlCache.get(cacheKey);
    }

    const result = url(route, params, options);
    urlCache.set(cacheKey, result);

    return result;
}

// ✅ 推荐：批量URL生成
function generateBatchUrls(configs) {
    return configs.map(config => ({
        ...config,
        url: url(config.route, config.params, config.options)
    }));
}
```

### 3. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
async function safeGetDataURL(file) {
    try {
        if (!file || !(file instanceof File || file instanceof Blob)) {
            throw new Error('无效的文件对象');
        }

        if (file.size > 10 * 1024 * 1024) { // 10MB限制
            throw new Error('文件过大');
        }

        return await getDataURLFromFile(file);
    } catch (error) {
        console.error('文件转换失败:', error);
        throw error;
    }
}
```

## 总结

Odoo URL工具模块提供了完整的URL处理和管理功能：

**核心优势**:
- **安全性**: 严格的同源检查和XSS防护
- **灵活性**: 支持多种URL格式和参数类型
- **集成性**: 与Odoo模型和会话系统深度集成
- **标准化**: 使用Web标准API确保兼容性
- **性能**: 高效的URL构建和比较算法

**适用场景**:
- API请求URL构建
- 图片资源管理
- 路由和导航
- 文件上传处理
- 状态管理

**设计优势**:
- 类型安全处理
- 完整的错误处理
- 浏览器兼容性
- 缓存和性能优化

这个URL工具为 Odoo Web 客户端提供了强大的URL管理能力，是构建现代Web应用的重要基础。

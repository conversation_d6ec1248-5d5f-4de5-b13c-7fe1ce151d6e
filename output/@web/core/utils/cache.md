# Odoo 缓存工具 (Cache Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/cache.js`  
**原始路径**: `/web/static/src/core/utils/cache.js`  
**模块类型**: 核心工具模块 - 缓存管理工具  
**代码行数**: 40 行  
**依赖关系**: 无外部依赖

## 模块功能

缓存工具模块是 Odoo Web 客户端的核心工具库，提供了强大的缓存管理功能。该模块实现了一个通用的缓存类，支持：
- 多层级缓存结构
- 自定义键生成策略
- 懒加载值计算
- 缓存清理和失效
- 灵活的路径访问

这个缓存系统是提升应用性能的重要工具，特别适用于昂贵计算结果的缓存。

## 核心类详解

### Cache 类 - 通用缓存管理器

```javascript
class Cache {
    constructor(getValue, getKey) {
        this.cache = {};
        this.getKey = getKey;
        this.getValue = getValue;
    }
}
```

**构造函数参数**:
- **getValue**: 值计算函数，当缓存未命中时调用
- **getKey**: 可选的键生成函数，用于自定义缓存键策略

**核心特性**:
- **懒加载**: 只有在需要时才计算值
- **多层级**: 支持嵌套的缓存结构
- **灵活键策略**: 支持自定义键生成逻辑
- **内存管理**: 提供清理和失效机制

## 核心方法详解

### 1. read() - 读取缓存值
```javascript
read(...path) {
    const { cache, key } = this._getCacheAndKey(...path);
    if (!(key in cache)) {
        cache[key] = this.getValue(...path);
    }
    return cache[key];
}
```

**功能特性**:
- **懒计算**: 缓存未命中时自动计算值
- **路径访问**: 支持多层级路径访问
- **自动存储**: 计算结果自动存储到缓存
- **即时返回**: 缓存命中时立即返回值

**使用示例**:
```javascript
// 创建简单缓存
const mathCache = new Cache((x, y) => {
    console.log(`计算 ${x} + ${y}`);
    return x + y;
});

// 第一次访问 - 会计算
const result1 = mathCache.read(2, 3); // 输出: "计算 2 + 3", 返回: 5

// 第二次访问 - 从缓存返回
const result2 = mathCache.read(2, 3); // 直接返回: 5

// 不同参数 - 会重新计算
const result3 = mathCache.read(4, 5); // 输出: "计算 4 + 5", 返回: 9
```

**实际应用场景**:
```javascript
// 1. API 请求缓存
class ApiCache {
    constructor() {
        this.cache = new Cache(async (endpoint, params) => {
            console.log(`请求 API: ${endpoint}`);
            const response = await fetch(endpoint, {
                method: 'POST',
                body: JSON.stringify(params),
                headers: { 'Content-Type': 'application/json' }
            });
            return response.json();
        });
    }
    
    async getUserData(userId) {
        return this.cache.read('/api/users', { id: userId });
    }
    
    async getOrderData(orderId) {
        return this.cache.read('/api/orders', { id: orderId });
    }
}

// 使用示例
const apiCache = new ApiCache();

// 第一次请求 - 会发送 HTTP 请求
const user1 = await apiCache.getUserData(123);

// 第二次请求相同用户 - 从缓存返回
const user2 = await apiCache.getUserData(123);

// 2. 计算结果缓存
class ComputationCache {
    constructor() {
        this.fibonacciCache = new Cache((n) => {
            if (n <= 1) return n;
            return this.fibonacciCache.read(n - 1) + this.fibonacciCache.read(n - 2);
        });
        
        this.primeCache = new Cache((n) => {
            console.log(`检查 ${n} 是否为质数`);
            if (n < 2) return false;
            for (let i = 2; i <= Math.sqrt(n); i++) {
                if (n % i === 0) return false;
            }
            return true;
        });
    }
    
    fibonacci(n) {
        return this.fibonacciCache.read(n);
    }
    
    isPrime(n) {
        return this.primeCache.read(n);
    }
}

// 3. 模板编译缓存
class TemplateCache {
    constructor() {
        this.cache = new Cache((templateString, context) => {
            console.log('编译模板:', templateString.substring(0, 50) + '...');
            
            // 模拟模板编译过程
            const compiled = templateString.replace(/\{\{(\w+)\}\}/g, (match, key) => {
                return context[key] || '';
            });
            
            return compiled;
        });
    }
    
    render(template, context) {
        return this.cache.read(template, JSON.stringify(context));
    }
}

// 4. 数据转换缓存
class DataTransformCache {
    constructor() {
        this.cache = new Cache((data, transformType) => {
            console.log(`执行 ${transformType} 转换`);
            
            switch (transformType) {
                case 'normalize':
                    return this.normalizeData(data);
                case 'aggregate':
                    return this.aggregateData(data);
                case 'filter':
                    return this.filterData(data);
                default:
                    return data;
            }
        });
    }
    
    transform(data, type) {
        const dataKey = JSON.stringify(data);
        return this.cache.read(dataKey, type);
    }
    
    normalizeData(data) {
        // 数据标准化逻辑
        return data.map(item => ({
            ...item,
            normalized: true,
            timestamp: Date.now()
        }));
    }
    
    aggregateData(data) {
        // 数据聚合逻辑
        return data.reduce((acc, item) => {
            acc.total += item.value || 0;
            acc.count += 1;
            return acc;
        }, { total: 0, count: 0 });
    }
    
    filterData(data) {
        // 数据过滤逻辑
        return data.filter(item => item.active && item.value > 0);
    }
}
```

### 2. clear() - 清除特定缓存
```javascript
clear(...path) {
    const { cache, key } = this._getCacheAndKey(...path);
    delete cache[key];
}
```

**功能特性**:
- **精确清除**: 只清除指定路径的缓存项
- **路径支持**: 支持多层级路径清除
- **安全操作**: 不会影响其他缓存项
- **即时生效**: 清除后下次访问会重新计算

**使用示例**:
```javascript
const cache = new Cache((type, id) => {
    return `${type}-${id}-${Date.now()}`;
});

// 添加一些缓存项
cache.read('user', 1);
cache.read('user', 2);
cache.read('order', 1);

// 清除特定缓存项
cache.clear('user', 1);

// 下次访问会重新计算
const newValue = cache.read('user', 1); // 重新计算
```

**实际应用场景**:
```javascript
// 1. 用户数据缓存管理
class UserDataManager {
    constructor() {
        this.cache = new Cache(async (userId) => {
            const response = await fetch(`/api/users/${userId}`);
            return response.json();
        });
    }
    
    async getUser(userId) {
        return this.cache.read(userId);
    }
    
    updateUser(userId, userData) {
        // 更新用户数据后清除缓存
        this.cache.clear(userId);
        
        // 发送更新请求
        return fetch(`/api/users/${userId}`, {
            method: 'PUT',
            body: JSON.stringify(userData),
            headers: { 'Content-Type': 'application/json' }
        });
    }
    
    deleteUser(userId) {
        // 删除用户后清除缓存
        this.cache.clear(userId);
        
        return fetch(`/api/users/${userId}`, {
            method: 'DELETE'
        });
    }
}

// 2. 配置缓存管理
class ConfigManager {
    constructor() {
        this.cache = new Cache(async (configKey) => {
            const response = await fetch(`/api/config/${configKey}`);
            return response.json();
        });
    }
    
    async getConfig(key) {
        return this.cache.read(key);
    }
    
    async updateConfig(key, value) {
        // 更新配置
        await fetch(`/api/config/${key}`, {
            method: 'PUT',
            body: JSON.stringify({ value }),
            headers: { 'Content-Type': 'application/json' }
        });
        
        // 清除缓存以确保下次获取最新值
        this.cache.clear(key);
    }
    
    async reloadConfig(key) {
        // 强制重新加载配置
        this.cache.clear(key);
        return this.getConfig(key);
    }
}

// 3. 权限缓存管理
class PermissionManager {
    constructor() {
        this.cache = new Cache(async (userId, resource) => {
            const response = await fetch(`/api/permissions/${userId}/${resource}`);
            return response.json();
        });
    }
    
    async hasPermission(userId, resource) {
        const permissions = await this.cache.read(userId, resource);
        return permissions.allowed;
    }
    
    async updateUserPermissions(userId) {
        // 用户权限更新后，清除所有相关缓存
        // 这里需要清除所有以 userId 开头的缓存项
        this.cache.invalidate(); // 简单起见，清除所有缓存
        
        // 在实际应用中，可能需要更精细的清除策略
    }
}
```

### 3. invalidate() - 清空所有缓存
```javascript
invalidate() {
    this.cache = {};
}
```

**功能特性**:
- **全量清除**: 清空所有缓存数据
- **重置状态**: 将缓存重置为初始状态
- **内存释放**: 释放所有缓存占用的内存
- **即时生效**: 清除后所有访问都会重新计算

**使用示例**:
```javascript
const cache = new Cache((x) => x * x);

// 添加一些缓存项
cache.read(1); // 1
cache.read(2); // 4
cache.read(3); // 9

// 清空所有缓存
cache.invalidate();

// 所有访问都会重新计算
cache.read(1); // 重新计算: 1
cache.read(2); // 重新计算: 4
```

**实际应用场景**:
```javascript
// 1. 应用状态重置
class ApplicationCache {
    constructor() {
        this.userCache = new Cache(async (userId) => {
            return await this.fetchUserData(userId);
        });

        this.settingsCache = new Cache(async (section) => {
            return await this.fetchSettings(section);
        });
    }

    async logout() {
        // 用户登出时清空所有缓存
        this.userCache.invalidate();
        this.settingsCache.invalidate();

        console.log('所有缓存已清空');
    }

    async switchTenant(tenantId) {
        // 切换租户时清空所有缓存
        this.userCache.invalidate();
        this.settingsCache.invalidate();

        // 重新初始化当前租户的数据
        await this.initializeTenant(tenantId);
    }
}

// 2. 开发模式缓存管理
class DevelopmentCacheManager {
    constructor() {
        this.cache = new Cache((module, version) => {
            return this.loadModule(module, version);
        });

        // 开发模式下定期清空缓存
        if (process.env.NODE_ENV === 'development') {
            setInterval(() => {
                this.cache.invalidate();
                console.log('开发模式: 缓存已自动清空');
            }, 60000); // 每分钟清空一次
        }
    }

    forceRefresh() {
        this.cache.invalidate();
        console.log('强制刷新: 所有缓存已清空');
    }
}
```

### 4. _getCacheAndKey() - 内部缓存路径解析
```javascript
_getCacheAndKey(...path) {
    let cache = this.cache;
    let key;
    if (this.getKey) {
        key = this.getKey(...path);
    } else {
        for (let i = 0; i < path.length - 1; i++) {
            cache = cache[path[i]] = cache[path[i]] || {};
        }
        key = path[path.length - 1];
    }
    return { cache, key };
}
```

**功能特性**:
- **路径解析**: 将多层级路径解析为缓存对象和键
- **自动创建**: 自动创建不存在的中间层级
- **键策略**: 支持自定义键生成函数
- **灵活访问**: 支持任意深度的嵌套访问

## 高级应用模式

### 1. 自定义键生成策略
```javascript
// 基于对象内容的键生成
const objectCache = new Cache(
    (obj) => {
        // 复杂的对象处理逻辑
        return JSON.stringify(obj).toUpperCase();
    },
    (obj) => {
        // 生成基于对象内容的稳定键
        const keys = Object.keys(obj).sort();
        return keys.map(k => `${k}:${obj[k]}`).join('|');
    }
);

// 使用示例
const result1 = objectCache.read({ name: 'John', age: 30 });
const result2 = objectCache.read({ age: 30, name: 'John' }); // 相同的键，从缓存返回

// 基于时间的键生成
const timeBasedCache = new Cache(
    (data) => {
        return this.processData(data);
    },
    (data) => {
        // 每小时生成不同的键，实现自动过期
        const hour = Math.floor(Date.now() / (1000 * 60 * 60));
        return `${JSON.stringify(data)}_${hour}`;
    }
);
```

### 2. 多层级缓存结构
```javascript
class HierarchicalCache {
    constructor() {
        this.cache = new Cache((category, subcategory, id) => {
            console.log(`加载数据: ${category}/${subcategory}/${id}`);
            return this.loadData(category, subcategory, id);
        });
    }

    // 获取用户数据
    getUser(userId) {
        return this.cache.read('users', 'profiles', userId);
    }

    // 获取用户设置
    getUserSettings(userId) {
        return this.cache.read('users', 'settings', userId);
    }

    // 获取产品数据
    getProduct(productId) {
        return this.cache.read('products', 'details', productId);
    }

    // 获取产品库存
    getProductStock(productId) {
        return this.cache.read('products', 'stock', productId);
    }

    // 清除用户相关的所有缓存
    clearUserCache(userId) {
        this.cache.clear('users', 'profiles', userId);
        this.cache.clear('users', 'settings', userId);
    }

    // 清除产品相关的所有缓存
    clearProductCache(productId) {
        this.cache.clear('products', 'details', productId);
        this.cache.clear('products', 'stock', productId);
    }
}
```

### 3. 缓存装饰器模式
```javascript
// 缓存装饰器函数
function cached(getValue, getKey) {
    const cache = new Cache(getValue, getKey);

    return function(...args) {
        return cache.read(...args);
    };
}

// 使用装饰器
class DataService {
    constructor() {
        // 将方法包装为缓存版本
        this.getExpensiveData = cached(
            this._getExpensiveData.bind(this),
            (id, options) => `${id}_${JSON.stringify(options)}`
        );

        this.calculateComplexResult = cached(
            this._calculateComplexResult.bind(this)
        );
    }

    _getExpensiveData(id, options) {
        console.log(`执行昂贵的数据获取: ${id}`);
        // 模拟昂贵的操作
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({ id, data: 'expensive data', options });
            }, 1000);
        });
    }

    _calculateComplexResult(input) {
        console.log(`执行复杂计算: ${input}`);
        // 模拟复杂计算
        let result = 0;
        for (let i = 0; i < 1000000; i++) {
            result += Math.sin(input + i);
        }
        return result;
    }
}
```

### 4. 缓存组合模式
```javascript
class CacheManager {
    constructor() {
        this.caches = {
            api: new Cache(this.fetchFromApi.bind(this)),
            computation: new Cache(this.performComputation.bind(this)),
            template: new Cache(this.compileTemplate.bind(this)),
            image: new Cache(this.processImage.bind(this))
        };
    }

    // 统一的缓存访问接口
    get(cacheType, ...args) {
        if (!this.caches[cacheType]) {
            throw new Error(`未知的缓存类型: ${cacheType}`);
        }
        return this.caches[cacheType].read(...args);
    }

    // 清除特定类型的缓存
    clear(cacheType, ...args) {
        if (!this.caches[cacheType]) {
            throw new Error(`未知的缓存类型: ${cacheType}`);
        }
        this.caches[cacheType].clear(...args);
    }

    // 清除所有缓存
    clearAll() {
        Object.values(this.caches).forEach(cache => {
            cache.invalidate();
        });
    }

    // 获取缓存统计信息
    getStats() {
        const stats = {};
        Object.keys(this.caches).forEach(type => {
            stats[type] = {
                size: Object.keys(this.caches[type].cache).length
            };
        });
        return stats;
    }

    async fetchFromApi(endpoint, params) {
        const response = await fetch(endpoint, {
            method: 'POST',
            body: JSON.stringify(params),
            headers: { 'Content-Type': 'application/json' }
        });
        return response.json();
    }

    performComputation(formula, variables) {
        // 复杂计算逻辑
        return eval(formula.replace(/\w+/g, (match) => variables[match] || 0));
    }

    compileTemplate(template, helpers) {
        // 模板编译逻辑
        return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
            return helpers[key] || '';
        });
    }

    processImage(imageData, filters) {
        // 图像处理逻辑
        return `processed_${imageData}_${JSON.stringify(filters)}`;
    }
}

// 使用示例
const cacheManager = new CacheManager();

// 使用不同类型的缓存
const apiData = await cacheManager.get('api', '/users', { id: 123 });
const result = cacheManager.get('computation', 'x + y * 2', { x: 10, y: 5 });
const html = cacheManager.get('template', '<h1>{{title}}</h1>', { title: 'Hello' });

// 查看缓存统计
console.log(cacheManager.getStats());
```

## 最佳实践

### 1. 内存管理
```javascript
// ✅ 推荐：带有大小限制的缓存
class LimitedCache extends Cache {
    constructor(getValue, getKey, maxSize = 100) {
        super(getValue, getKey);
        this.maxSize = maxSize;
        this.accessOrder = [];
    }

    read(...path) {
        const result = super.read(...path);

        // 更新访问顺序
        const key = JSON.stringify(path);
        const index = this.accessOrder.indexOf(key);
        if (index > -1) {
            this.accessOrder.splice(index, 1);
        }
        this.accessOrder.push(key);

        // 检查大小限制
        if (this.accessOrder.length > this.maxSize) {
            const oldestKey = this.accessOrder.shift();
            const oldestPath = JSON.parse(oldestKey);
            this.clear(...oldestPath);
        }

        return result;
    }
}

// ✅ 推荐：带有过期时间的缓存
class TTLCache extends Cache {
    constructor(getValue, getKey, ttl = 60000) {
        super(getValue, getKey);
        this.ttl = ttl;
        this.timestamps = new Map();
    }

    read(...path) {
        const key = JSON.stringify(path);
        const now = Date.now();

        // 检查是否过期
        if (this.timestamps.has(key)) {
            const timestamp = this.timestamps.get(key);
            if (now - timestamp > this.ttl) {
                this.clear(...path);
                this.timestamps.delete(key);
            }
        }

        const result = super.read(...path);
        this.timestamps.set(key, now);

        return result;
    }

    clear(...path) {
        super.clear(...path);
        const key = JSON.stringify(path);
        this.timestamps.delete(key);
    }

    invalidate() {
        super.invalidate();
        this.timestamps.clear();
    }
}
```

### 2. 错误处理
```javascript
// ✅ 推荐：带有错误处理的缓存
class SafeCache extends Cache {
    constructor(getValue, getKey, options = {}) {
        super(getValue, getKey);
        this.retryCount = options.retryCount || 3;
        this.retryDelay = options.retryDelay || 1000;
        this.fallbackValue = options.fallbackValue;
    }

    async read(...path) {
        const { cache, key } = this._getCacheAndKey(...path);

        if (!(key in cache)) {
            cache[key] = await this.safeGetValue(...path);
        }

        return cache[key];
    }

    async safeGetValue(...path) {
        let lastError;

        for (let attempt = 1; attempt <= this.retryCount; attempt++) {
            try {
                return await this.getValue(...path);
            } catch (error) {
                lastError = error;
                console.warn(`缓存值获取失败 (尝试 ${attempt}/${this.retryCount}):`, error);

                if (attempt < this.retryCount) {
                    await new Promise(resolve =>
                        setTimeout(resolve, this.retryDelay * attempt)
                    );
                }
            }
        }

        // 所有重试都失败，返回回退值或抛出错误
        if (this.fallbackValue !== undefined) {
            console.warn('使用回退值:', this.fallbackValue);
            return this.fallbackValue;
        }

        throw lastError;
    }
}
```

### 3. 性能监控
```javascript
// ✅ 推荐：带有性能监控的缓存
class MonitoredCache extends Cache {
    constructor(getValue, getKey) {
        super(getValue, getKey);
        this.stats = {
            hits: 0,
            misses: 0,
            totalTime: 0,
            avgTime: 0
        };
    }

    read(...path) {
        const startTime = performance.now();
        const { cache, key } = this._getCacheAndKey(...path);

        if (key in cache) {
            this.stats.hits++;
            const endTime = performance.now();
            this.updateTiming(endTime - startTime);
            return cache[key];
        } else {
            this.stats.misses++;
            const result = this.getValue(...path);
            cache[key] = result;

            const endTime = performance.now();
            this.updateTiming(endTime - startTime);
            return result;
        }
    }

    updateTiming(duration) {
        this.stats.totalTime += duration;
        const totalOperations = this.stats.hits + this.stats.misses;
        this.stats.avgTime = this.stats.totalTime / totalOperations;
    }

    getStats() {
        const totalOperations = this.stats.hits + this.stats.misses;
        return {
            ...this.stats,
            hitRate: totalOperations > 0 ? this.stats.hits / totalOperations : 0,
            totalOperations
        };
    }

    resetStats() {
        this.stats = {
            hits: 0,
            misses: 0,
            totalTime: 0,
            avgTime: 0
        };
    }
}
```

## 总结

Odoo 缓存工具模块提供了强大而灵活的缓存管理功能：

**核心优势**:
- **懒加载**: 只有在需要时才计算值，提高性能
- **多层级**: 支持复杂的嵌套缓存结构
- **灵活键策略**: 支持自定义键生成逻辑
- **内存管理**: 提供清理和失效机制
- **简单API**: 直观易用的接口设计

**适用场景**:
- API 请求结果缓存
- 复杂计算结果缓存
- 模板编译缓存
- 数据转换缓存
- 配置和权限缓存

**扩展建议**:
- 添加过期时间支持
- 实现大小限制机制
- 增加性能监控功能
- 支持持久化存储
- 添加缓存预热功能

这个缓存工具为 Odoo Web 客户端提供了高效的缓存管理能力，是提升应用性能的重要基础设施。

# Odoo UI 工具 (UI Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/ui.js`  
**原始路径**: `/web/static/src/core/utils/ui.js`  
**模块类型**: 核心工具模块 - UI 交互工具  
**代码行数**: 235 行  
**依赖关系**: 无外部依赖

## 模块功能

UI 工具模块是 Odoo Web 客户端的核心工具库，提供了丰富的用户界面交互功能，包括：
- 元素可见性检测
- 距离计算和最近元素查找
- 焦点管理和Tab导航
- 元素碰撞检测
- 按钮加载效果
- 可见元素筛选

这些工具是构建现代Web用户界面的重要基础设施。

## 核心函数详解

### 1. isVisible() - 元素可见性检测
```javascript
function isVisible(el) {
    if (el === document || el === window) {
        return true;
    }
    if (!el) {
        return false;
    }
    let _isVisible = false;
    if ("offsetWidth" in el && "offsetHeight" in el) {
        _isVisible = el.offsetWidth > 0 && el.offsetHeight > 0;
    } else if ("getBoundingClientRect" in el) {
        // for example, svgelements
        const rect = el.getBoundingClientRect();
        _isVisible = rect.width > 0 && rect.height > 0;
    }
    if (!_isVisible && getComputedStyle(el).display === "contents") {
        for (const child of el.children) {
            if (isVisible(child)) {
                return true;
            }
        }
    }
    return _isVisible;
}
```

**功能特性**:
- **精确检测**: 检查元素是否真正可见（宽度和高度都大于0）
- **SVG支持**: 特殊处理SVG元素的可见性检测
- **Contents显示**: 处理 `display: contents` 的特殊情况
- **递归检查**: 对于contents元素，递归检查子元素
- **边界处理**: 正确处理document、window和null元素

**使用示例**:
```javascript
// 基本可见性检测
const element = document.getElementById('myElement');
if (isVisible(element)) {
    console.log('元素可见');
} else {
    console.log('元素不可见');
}

// 检查多个元素
const elements = document.querySelectorAll('.item');
const visibleElements = Array.from(elements).filter(isVisible);
console.log(`${visibleElements.length}/${elements.length} 个元素可见`);

// SVG元素检测
const svgElement = document.querySelector('svg circle');
if (isVisible(svgElement)) {
    console.log('SVG圆形可见');
}
```

**实际应用场景**:
```javascript
// 1. 懒加载实现
class LazyLoader {
    constructor() {
        this.images = document.querySelectorAll('img[data-src]');
        this.observer = new IntersectionObserver(this.handleIntersection.bind(this));
        this.init();
    }
    
    init() {
        this.images.forEach(img => {
            if (isVisible(img)) {
                this.loadImage(img);
            } else {
                this.observer.observe(img);
            }
        });
    }
    
    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting && isVisible(entry.target)) {
                this.loadImage(entry.target);
                this.observer.unobserve(entry.target);
            }
        });
    }
    
    loadImage(img) {
        img.src = img.dataset.src;
        img.removeAttribute('data-src');
    }
}

// 2. 动画触发器
class AnimationTrigger {
    constructor() {
        this.animatedElements = document.querySelectorAll('.animate-on-visible');
        this.checkVisibility();
        window.addEventListener('scroll', this.checkVisibility.bind(this));
    }
    
    checkVisibility() {
        this.animatedElements.forEach(element => {
            if (isVisible(element) && !element.classList.contains('animated')) {
                element.classList.add('animate-in');
                element.classList.add('animated');
            }
        });
    }
}

// 3. 表单验证显示
class FormValidator {
    validateForm(form) {
        const fields = form.querySelectorAll('input, select, textarea');
        const errors = [];
        
        fields.forEach(field => {
            if (!this.validateField(field)) {
                // 只对可见字段显示错误
                if (isVisible(field)) {
                    this.showFieldError(field);
                    errors.push(field);
                }
            }
        });
        
        return errors.length === 0;
    }
    
    showFieldError(field) {
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error';
        errorElement.textContent = '此字段必填';
        field.parentNode.appendChild(errorElement);
    }
}

// 4. 工具提示管理
class TooltipManager {
    constructor() {
        this.tooltips = new Map();
        this.init();
    }
    
    init() {
        document.addEventListener('mouseover', (event) => {
            const target = event.target.closest('[data-tooltip]');
            if (target && isVisible(target)) {
                this.showTooltip(target);
            }
        });
        
        document.addEventListener('mouseout', (event) => {
            const target = event.target.closest('[data-tooltip]');
            if (target) {
                this.hideTooltip(target);
            }
        });
    }
    
    showTooltip(element) {
        if (!isVisible(element)) return;
        
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = element.dataset.tooltip;
        
        document.body.appendChild(tooltip);
        this.positionTooltip(tooltip, element);
        this.tooltips.set(element, tooltip);
    }
    
    hideTooltip(element) {
        const tooltip = this.tooltips.get(element);
        if (tooltip) {
            tooltip.remove();
            this.tooltips.delete(element);
        }
    }
}
```

### 2. closest() - 最近元素查找
```javascript
function closest(elements, targetPos) {
    let closestEl = null;
    let closestDistance = Infinity;
    for (const el of elements) {
        const rect = el.getBoundingClientRect();
        const distance = getQuadrance(rect, targetPos);
        if (!closestEl || distance < closestDistance) {
            closestEl = el;
            closestDistance = distance;
        }
    }
    return closestEl;
}
```

**功能特性**:
- **距离计算**: 计算元素与目标位置的最短距离
- **高效查找**: 遍历所有元素找到最近的一个
- **位置精确**: 基于元素的边界矩形计算距离
- **灵活输入**: 接受任何可迭代的元素集合

**使用示例**:
```javascript
// 查找鼠标位置最近的元素
document.addEventListener('mousemove', (event) => {
    const elements = document.querySelectorAll('.draggable');
    const mousePos = { x: event.clientX, y: event.clientY };
    const nearestElement = closest(elements, mousePos);
    
    if (nearestElement) {
        // 高亮最近的元素
        document.querySelectorAll('.draggable').forEach(el => 
            el.classList.remove('nearest')
        );
        nearestElement.classList.add('nearest');
    }
});

// 查找点击位置最近的按钮
function findNearestButton(clickPos) {
    const buttons = document.querySelectorAll('button');
    return closest(buttons, clickPos);
}
```

**实际应用场景**:
```javascript
// 1. 拖拽排序
class DragSortable {
    constructor(container) {
        this.container = container;
        this.draggedElement = null;
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        this.container.addEventListener('dragstart', this.handleDragStart.bind(this));
        this.container.addEventListener('dragover', this.handleDragOver.bind(this));
        this.container.addEventListener('drop', this.handleDrop.bind(this));
    }
    
    handleDragStart(event) {
        this.draggedElement = event.target;
    }
    
    handleDragOver(event) {
        event.preventDefault();
        
        const mousePos = { x: event.clientX, y: event.clientY };
        const sortableItems = this.container.querySelectorAll('.sortable-item');
        const nearestItem = closest(sortableItems, mousePos);
        
        if (nearestItem && nearestItem !== this.draggedElement) {
            this.showDropIndicator(nearestItem, mousePos);
        }
    }
    
    showDropIndicator(nearestItem, mousePos) {
        const rect = nearestItem.getBoundingClientRect();
        const isAbove = mousePos.y < rect.top + rect.height / 2;
        
        // 显示插入指示器
        this.clearDropIndicators();
        const indicator = document.createElement('div');
        indicator.className = 'drop-indicator';
        
        if (isAbove) {
            nearestItem.parentNode.insertBefore(indicator, nearestItem);
        } else {
            nearestItem.parentNode.insertBefore(indicator, nearestItem.nextSibling);
        }
    }
}

// 2. 智能菜单定位
class ContextMenu {
    show(event, menuItems) {
        const menu = this.createMenu(menuItems);
        document.body.appendChild(menu);
        
        // 查找最近的可用空间
        const clickPos = { x: event.clientX, y: event.clientY };
        const availableSpaces = this.getAvailableSpaces();
        const bestSpace = closest(availableSpaces, clickPos);
        
        this.positionMenu(menu, bestSpace);
    }
    
    getAvailableSpaces() {
        // 返回屏幕上可用的空间区域
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        return [
            { x: 0, y: 0, width: viewportWidth / 2, height: viewportHeight / 2 },
            { x: viewportWidth / 2, y: 0, width: viewportWidth / 2, height: viewportHeight / 2 },
            { x: 0, y: viewportHeight / 2, width: viewportWidth / 2, height: viewportHeight / 2 },
            { x: viewportWidth / 2, y: viewportHeight / 2, width: viewportWidth / 2, height: viewportHeight / 2 }
        ];
    }
}

// 3. 磁性对齐
class MagneticAlignment {
    constructor(container) {
        this.container = container;
        this.snapDistance = 20; // 磁性吸附距离
    }
    
    alignElement(movingElement, mousePos) {
        const staticElements = this.container.querySelectorAll('.static-element');
        const nearestElement = closest(staticElements, mousePos);
        
        if (nearestElement) {
            const distance = this.calculateDistance(movingElement, nearestElement);
            
            if (distance < this.snapDistance) {
                this.snapToElement(movingElement, nearestElement);
            }
        }
    }
    
    snapToElement(movingElement, targetElement) {
        const targetRect = targetElement.getBoundingClientRect();
        const containerRect = this.container.getBoundingClientRect();
        
        // 对齐到目标元素
        movingElement.style.left = (targetRect.left - containerRect.left) + 'px';
        movingElement.style.top = (targetRect.bottom - containerRect.top) + 'px';
    }
}
```

### 3. getQuadrance() - 距离平方计算
```javascript
function getQuadrance(rect, pos) {
    let q = 0;
    if (pos.x < rect.x) {
        q += (rect.x - pos.x) ** 2;
    } else if (rect.x + rect.width < pos.x) {
        q += (pos.x - (rect.x + rect.width)) ** 2;
    }
    if (pos.y < rect.y) {
        q += (rect.y - pos.y) ** 2;
    } else if (rect.y + rect.height < pos.y) {
        q += (pos.y - (rect.y + rect.height)) ** 2;
    }
    return q;
}
```

**功能特性**:
- **高效计算**: 计算点到矩形的距离平方（避免开方运算）
- **边界处理**: 正确处理点在矩形内部、边缘和外部的情况
- **性能优化**: 使用距离平方比较，避免昂贵的平方根计算
- **精确算法**: 基于几何学的精确距离计算

### 4. getTabableElements() - 获取可Tab导航元素
```javascript
function getTabableElements(container = document.body) {
    const elements = [...container.querySelectorAll(TABABLE_SELECTOR)].filter(isVisible);
    const byTabIndex = {};
    for (const el of [...elements]) {
        if (!byTabIndex[el.tabIndex]) {
            byTabIndex[el.tabIndex] = [];
        }
        byTabIndex[el.tabIndex].push(el);
    }

    const withTabIndexZero = byTabIndex[0] || [];
    delete byTabIndex[0];
    return [...Object.values(byTabIndex).flat(), ...withTabIndexZero];
}
```

**功能特性**:
- **标准兼容**: 基于HTML规范的Tab导航标准
- **智能排序**: 按tabindex值排序，0值元素放在最后
- **可见性过滤**: 只返回可见的可Tab元素
- **完整支持**: 支持所有标准的可聚焦元素类型

### 5. addLoadingEffect() - 按钮加载效果
```javascript
function addLoadingEffect(btnEl) {
    btnEl.classList.add("o_btn_loading", "disabled", "pe-none");
    btnEl.disabled = true;
    const loaderEl = document.createElement("span");
    loaderEl.classList.add("fa", "fa-refresh", "fa-spin", "me-2");
    btnEl.prepend(loaderEl);
    return () => {
        btnEl.classList.remove("o_btn_loading", "disabled", "pe-none");
        btnEl.disabled = false;
        loaderEl.remove();
    };
}
```

**功能特性**:
- **视觉反馈**: 添加旋转的加载图标
- **交互禁用**: 禁用按钮防止重复点击
- **样式控制**: 添加加载状态的CSS类
- **恢复函数**: 返回恢复按钮原始状态的函数

## 最佳实践

### 1. 可见性检测优化
```javascript
// ✅ 推荐：缓存可见性检测结果
class VisibilityCache {
    constructor() {
        this.cache = new WeakMap();
        this.observer = new MutationObserver(this.clearCache.bind(this));
        this.observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'class']
        });
    }

    isVisible(element) {
        if (this.cache.has(element)) {
            return this.cache.get(element);
        }

        const visible = isVisible(element);
        this.cache.set(element, visible);
        return visible;
    }

    clearCache() {
        this.cache = new WeakMap();
    }
}
```

### 2. 焦点管理
```javascript
// ✅ 推荐：完整的焦点管理
class FocusManager {
    constructor(container) {
        this.container = container;
        this.focusHistory = [];
    }

    trapFocus() {
        const tabableElements = getTabableElements(this.container);

        if (tabableElements.length === 0) return;

        // 聚焦第一个元素
        tabableElements[0].focus();

        this.container.addEventListener('keydown', (event) => {
            if (event.key === 'Tab') {
                this.handleTabKey(event, tabableElements);
            }
        });
    }

    handleTabKey(event, tabableElements) {
        const currentIndex = tabableElements.indexOf(document.activeElement);

        if (event.shiftKey) {
            const prevIndex = currentIndex <= 0 ? tabableElements.length - 1 : currentIndex - 1;
            tabableElements[prevIndex].focus();
        } else {
            const nextIndex = currentIndex >= tabableElements.length - 1 ? 0 : currentIndex + 1;
            tabableElements[nextIndex].focus();
        }

        event.preventDefault();
    }

    saveFocus() {
        this.focusHistory.push(document.activeElement);
    }

    restoreFocus() {
        const lastFocused = this.focusHistory.pop();
        if (lastFocused && isVisible(lastFocused)) {
            lastFocused.focus();
        }
    }
}
```

### 3. 性能优化
```javascript
// ✅ 推荐：批量DOM操作
class BatchDOMUpdater {
    constructor() {
        this.updates = [];
        this.scheduled = false;
    }

    addUpdate(element, property, value) {
        this.updates.push({ element, property, value });
        this.scheduleUpdate();
    }

    scheduleUpdate() {
        if (this.scheduled) return;

        this.scheduled = true;
        requestAnimationFrame(() => {
            this.flushUpdates();
            this.scheduled = false;
        });
    }

    flushUpdates() {
        this.updates.forEach(({ element, property, value }) => {
            if (isVisible(element)) {
                element.style[property] = value;
            }
        });
        this.updates = [];
    }
}
```

## 总结

Odoo UI 工具模块提供了完整的用户界面交互功能：

**核心优势**:
- **可见性检测**: 精确的元素可见性判断，支持各种边界情况
- **距离计算**: 高效的几何距离计算，支持最近元素查找
- **焦点管理**: 完整的Tab导航和焦点管理功能
- **碰撞检测**: 精确的元素重叠检测算法
- **用户反馈**: 优雅的按钮加载效果

**适用场景**:
- 拖拽和排序功能
- 模态框和焦点管理
- 懒加载和动画触发
- 选择框和碰撞检测
- 表单提交和用户反馈

这个工具模块为 Odoo Web 客户端提供了强大的UI交互能力，是构建现代用户界面的重要基础。

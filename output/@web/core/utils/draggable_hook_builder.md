# Odoo 拖拽Hook构建器 (Draggable Hook Builder) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/draggable_hook_builder.js`  
**原始路径**: `/web/static/src/core/utils/draggable_hook_builder.js`  
**模块类型**: 核心工具模块 - 拖拽Hook构建器  
**代码行数**: 1082 行  
**依赖关系**: 
- `@web/core/utils/numbers` - 数字工具 (clamp)
- `@web/core/utils/objects` - 对象工具 (omit)
- `@web/core/utils/scrolling` - 滚动工具 (closestScrollableX, closestScrollableY)
- `@web/core/utils/timing` - 时间工具 (setRecurringAnimationFrame)
- `@web/core/browser/browser` - 浏览器工具
- `@web/core/browser/feature_detection` - 特性检测 (hasTouch, isBrowserFirefox, isIOS)

## 模块功能

拖拽Hook构建器是 Odoo Web 客户端的核心工具库，提供了强大的拖拽功能构建框架。该模块是一个高级的Hook工厂，用于创建各种拖拽相关的Hook，支持：
- 自定义拖拽Hook创建
- 完整的拖拽生命周期管理
- 边缘滚动功能
- 触摸设备支持
- 跨浏览器兼容性
- 高度可配置的拖拽行为

这个构建器是 Odoo 拖拽系统的核心，为排序、拖放等高级功能提供了基础架构。

## 核心设计理念

### Hook工厂模式
拖拽Hook构建器采用工厂模式，通过配置生成特定的拖拽Hook：

```javascript
function makeDraggableHook(hookParams) {
    // 根据参数构建特定的拖拽Hook
    return function useCustomDraggable(params) {
        // 返回具体的Hook实现
    };
}
```

### 生命周期管理
提供完整的拖拽生命周期管理：
- **onWillStartDrag**: 即将开始拖拽
- **onDragStart**: 拖拽开始
- **onDrag**: 拖拽进行中
- **onDrop**: 拖放完成
- **onDragEnd**: 拖拽结束

### 清理管理
自动管理资源清理，避免内存泄漏：

```javascript
function makeCleanupManager(defaultCleanupFn) {
    const cleanups = [];
    const add = (cleanupFn) => cleanups.push(cleanupFn);
    const cleanup = () => cleanups.forEach(fn => fn());
    return { add, cleanup };
}
```

## 核心类型定义

### DraggableBuilderParams - 构建器参数
```typescript
interface DraggableBuilderParams {
    // Hook参数
    name?: string;
    edgeScrolling?: EdgeScrollingOptions;
    acceptedParams?: Record<string, string[]>;
    defaultParams?: Record<string, any>;
    
    // 设置Hook
    setupHooks: {
        addListener: typeof useExternalListener;
        setup: typeof useEffect;
        teardown: typeof onWillUnmount;
        throttle: typeof useThrottleForAnimation;
        wrapState: typeof reactive;
    };
    
    // 构建Hook
    onComputeParams?: (params: DraggableBuildHandlerParams) => any;
    
    // 运行时Hook
    onDragStart?: (params: DraggableBuildHandlerParams) => any;
    onDrag?: (params: DraggableBuildHandlerParams) => any;
    onDragEnd?: (params: DraggableBuildHandlerParams) => any;
    onDrop?: (params: DraggableBuildHandlerParams) => any;
    onWillStartDrag?: (params: DraggableBuildHandlerParams) => any;
}
```

### DraggableHookContext - Hook上下文
```typescript
interface DraggableHookContext {
    ref: { el: HTMLElement | null };
    elementSelector?: string | null;
    ignoreSelector?: string | null;
    fullSelector?: string | null;
    followCursor?: boolean;
    cursor?: string | null;
    enable?: () => boolean;
    preventDrag?: (HTMLElement) => boolean;
    pointer?: Position;
    edgeScrolling?: EdgeScrollingOptions;
    delay?: number;
    tolerance?: number;
    current: DraggableHookCurrentContext;
}
```

### EdgeScrollingOptions - 边缘滚动选项
```typescript
interface EdgeScrollingOptions {
    enabled?: boolean;
    speed?: number;
    threshold?: number;
    direction?: "horizontal" | "vertical";
}
```

## 核心函数详解

### makeDraggableHook() - 拖拽Hook构建器
```javascript
function makeDraggableHook(hookParams) {
    hookParams = getReturnValue(hookParams);
    
    const hookName = hookParams.name || "useAnonymousDraggable";
    const { setupHooks } = hookParams;
    const allAcceptedParams = { ...DEFAULT_ACCEPTED_PARAMS, ...hookParams.acceptedParams };
    const allDefaultParams = { ...DEFAULT_DEFAULT_PARAMS, ...hookParams.defaultParams };
    
    // 返回具体的Hook函数
    return function useCustomDraggable(params) {
        // Hook实现
    };
}
```

**功能特性**:
- **Hook工厂**: 根据配置生成特定的拖拽Hook
- **参数验证**: 自动验证和处理Hook参数
- **生命周期集成**: 与框架生命周期完美集成
- **事件管理**: 统一的事件监听和清理
- **状态管理**: 响应式状态管理

**使用示例**:
```javascript
// 创建基本拖拽Hook
const useBasicDraggable = makeDraggableHook({
    name: "useBasicDraggable",
    
    onDragStart: ({ ctx, element, addCleanup }) => {
        console.log("拖拽开始:", element);
        
        // 添加拖拽样式
        element.classList.add("dragging");
        
        // 注册清理函数
        addCleanup(() => {
            element.classList.remove("dragging");
        });
    },
    
    onDrag: ({ ctx, element, x, y }) => {
        // 更新元素位置
        element.style.transform = `translate(${x}px, ${y}px)`;
    },
    
    onDragEnd: ({ ctx, element }) => {
        console.log("拖拽结束:", element);
        element.style.transform = "";
    }
});

// 在组件中使用
class DraggableComponent extends Component {
    setup() {
        this.containerRef = useRef("container");
        
        this.draggableState = useBasicDraggable({
            ref: this.containerRef,
            elements: ".draggable-item",
            
            onDragStart: ({ element }) => {
                console.log("组件级拖拽开始:", element);
            }
        });
    }
}
```

**实际应用场景**:
```javascript
// 1. 自定义排序Hook
const useSortableDraggable = makeDraggableHook({
    name: "useSortableDraggable",
    
    defaultParams: {
        clone: true,
        placeholder: true
    },
    
    onComputeParams: ({ ctx, params }) => {
        // 计算排序相关参数
        ctx.sortableConfig = {
            clone: params.clone,
            placeholder: params.placeholder,
            placeholderClass: params.placeholderClass || "sortable-placeholder"
        };
    },
    
    onDragStart: ({ ctx, element, addCleanup }) => {
        const config = ctx.sortableConfig;
        
        if (config.clone) {
            // 创建克隆元素
            const clone = element.cloneNode(true);
            clone.classList.add("sortable-clone");
            element.parentNode.appendChild(clone);
            
            addCleanup(() => {
                clone.remove();
            });
        }
        
        if (config.placeholder) {
            // 创建占位符
            const placeholder = document.createElement("div");
            placeholder.className = config.placeholderClass;
            placeholder.style.height = element.offsetHeight + "px";
            element.parentNode.insertBefore(placeholder, element.nextSibling);
            
            ctx.placeholder = placeholder;
            
            addCleanup(() => {
                if (ctx.placeholder) {
                    ctx.placeholder.remove();
                    ctx.placeholder = null;
                }
            });
        }
    },
    
    onDrag: ({ ctx, element, x, y }) => {
        // 更新克隆元素位置
        const clone = element.parentNode.querySelector(".sortable-clone");
        if (clone) {
            clone.style.transform = `translate(${x}px, ${y}px)`;
        }
        
        // 更新占位符位置
        if (ctx.placeholder) {
            const targetElement = this.getDropTarget(x, y);
            if (targetElement && targetElement !== element) {
                this.updatePlaceholderPosition(ctx.placeholder, targetElement);
            }
        }
    },
    
    onDrop: ({ ctx, element, x, y }) => {
        const targetElement = this.getDropTarget(x, y);
        if (targetElement && targetElement !== element) {
            // 执行排序逻辑
            this.performSort(element, targetElement);
        }
    }
});

// 2. 文件拖放Hook
const useFileDrop = makeDraggableHook({
    name: "useFileDrop",
    
    acceptedParams: {
        ...DEFAULT_ACCEPTED_PARAMS,
        acceptedTypes: [Array],
        maxFileSize: [Number],
        multiple: [Boolean]
    },
    
    defaultParams: {
        acceptedTypes: ["*/*"],
        maxFileSize: 10 * 1024 * 1024, // 10MB
        multiple: true
    },
    
    onComputeParams: ({ ctx, params }) => {
        ctx.fileConfig = {
            acceptedTypes: params.acceptedTypes,
            maxFileSize: params.maxFileSize,
            multiple: params.multiple
        };
    },
    
    onDragStart: ({ ctx, element }) => {
        // 检查是否为文件拖拽
        if (!this.isFileDrag(ctx)) {
            return false; // 阻止拖拽
        }
        
        element.classList.add("file-drag-over");
    },
    
    onDrag: ({ ctx, element, x, y }) => {
        // 高亮拖放区域
        const dropZone = this.getDropZone(x, y);
        if (dropZone) {
            dropZone.classList.add("drop-zone-active");
        }
    },
    
    onDrop: ({ ctx, element, x, y }) => {
        const dropZone = this.getDropZone(x, y);
        if (dropZone) {
            const files = this.getDroppedFiles(ctx);
            const validFiles = this.validateFiles(files, ctx.fileConfig);
            
            if (validFiles.length > 0) {
                this.handleFilesDrop(validFiles, dropZone);
            }
        }
    },
    
    onDragEnd: ({ ctx, element }) => {
        element.classList.remove("file-drag-over");
        
        // 清除所有高亮
        document.querySelectorAll(".drop-zone-active")
            .forEach(zone => zone.classList.remove("drop-zone-active"));
    }
});

// 3. 画布绘制Hook
const useCanvasDraw = makeDraggableHook({
    name: "useCanvasDraw",
    
    acceptedParams: {
        ...DEFAULT_ACCEPTED_PARAMS,
        brushSize: [Number],
        brushColor: [String],
        brushType: [String]
    },
    
    defaultParams: {
        brushSize: 5,
        brushColor: "#000000",
        brushType: "round"
    },
    
    onComputeParams: ({ ctx, params }) => {
        ctx.drawConfig = {
            brushSize: params.brushSize,
            brushColor: params.brushColor,
            brushType: params.brushType
        };
        
        // 获取画布上下文
        const canvas = ctx.ref.el;
        if (canvas && canvas.tagName === "CANVAS") {
            ctx.canvasContext = canvas.getContext("2d");
            this.setupCanvasContext(ctx.canvasContext, ctx.drawConfig);
        }
    },
    
    onDragStart: ({ ctx, element, x, y }) => {
        if (!ctx.canvasContext) return false;
        
        // 开始绘制路径
        ctx.canvasContext.beginPath();
        ctx.canvasContext.moveTo(x, y);
        ctx.isDrawing = true;
    },
    
    onDrag: ({ ctx, x, y }) => {
        if (!ctx.isDrawing || !ctx.canvasContext) return;
        
        // 绘制线条
        ctx.canvasContext.lineTo(x, y);
        ctx.canvasContext.stroke();
    },
    
    onDragEnd: ({ ctx }) => {
        if (ctx.canvasContext && ctx.isDrawing) {
            ctx.canvasContext.closePath();
            ctx.isDrawing = false;
        }
    }
});
```

## 核心辅助函数

### makeCleanupManager() - 清理管理器
```javascript
function makeCleanupManager(defaultCleanupFn) {
    const cleanups = [];

    const add = (cleanupFn) => {
        if (typeof cleanupFn === "function") {
            cleanups.push(cleanupFn);
        }
    };

    const cleanup = () => {
        cleanups.forEach(fn => {
            try {
                fn();
            } catch (error) {
                console.error("清理函数执行失败:", error);
            }
        });
        cleanups.length = 0;

        if (defaultCleanupFn) {
            defaultCleanupFn();
        }
    };

    return { add, cleanup };
}
```

**功能特性**:
- **资源管理**: 统一管理需要清理的资源
- **错误处理**: 安全执行清理函数，避免错误中断
- **默认清理**: 支持默认清理函数
- **批量清理**: 一次性清理所有注册的资源

### makeDOMHelpers() - DOM辅助工具
```javascript
function makeDOMHelpers(cleanup) {
    const addClass = (el, ...classNames) => {
        classNames.forEach(className => {
            if (!el.classList.contains(className)) {
                el.classList.add(className);
                cleanup.add(() => el.classList.remove(className));
            }
        });
    };

    const removeClass = (el, ...classNames) => {
        classNames.forEach(className => {
            if (el.classList.contains(className)) {
                el.classList.remove(className);
                cleanup.add(() => el.classList.add(className));
            }
        });
    };

    const setStyle = (el, property, value) => {
        const originalValue = el.style[property];
        el.style[property] = value;
        cleanup.add(() => {
            el.style[property] = originalValue;
        });
    };

    const setAttribute = (el, attribute, value) => {
        const hasOriginal = el.hasAttribute(attribute);
        const originalValue = el.getAttribute(attribute);

        el.setAttribute(attribute, value);

        cleanup.add(() => {
            if (hasOriginal) {
                el.setAttribute(attribute, originalValue);
            } else {
                el.removeAttribute(attribute);
            }
        });
    };

    const addListener = (el, type, listener, options = {}) => {
        el.addEventListener(type, listener, options);
        cleanup.add(() => {
            el.removeEventListener(type, listener, options);
        });
    };

    return {
        addClass,
        removeClass,
        setStyle,
        setAttribute,
        addListener
    };
}
```

**功能特性**:
- **自动清理**: 所有DOM操作都会自动注册清理函数
- **状态恢复**: 清理时恢复元素的原始状态
- **批量操作**: 支持批量添加/移除类名
- **事件管理**: 自动管理事件监听器的添加和移除

### 边缘滚动功能
```javascript
// 边缘滚动实现
function setupEdgeScrolling(ctx) {
    const { edgeScrolling } = ctx;
    if (!edgeScrolling.enabled) return;

    const checkEdgeScrolling = (x, y) => {
        const { threshold, speed } = edgeScrolling;
        const container = ctx.current.container;
        const rect = container.getBoundingClientRect();

        let scrollDirection = null;
        let scrollAmount = 0;

        // 检查水平滚动
        if (edgeScrolling.direction !== "vertical") {
            if (x < rect.left + threshold) {
                scrollDirection = "left";
                scrollAmount = speed * ((threshold - (x - rect.left)) / threshold);
            } else if (x > rect.right - threshold) {
                scrollDirection = "right";
                scrollAmount = speed * ((threshold - (rect.right - x)) / threshold);
            }
        }

        // 检查垂直滚动
        if (edgeScrolling.direction !== "horizontal") {
            if (y < rect.top + threshold) {
                scrollDirection = "up";
                scrollAmount = speed * ((threshold - (y - rect.top)) / threshold);
            } else if (y > rect.bottom - threshold) {
                scrollDirection = "down";
                scrollAmount = speed * ((threshold - (rect.bottom - y)) / threshold);
            }
        }

        if (scrollDirection) {
            performEdgeScroll(container, scrollDirection, scrollAmount);
        }
    };

    const performEdgeScroll = (container, direction, amount) => {
        switch (direction) {
            case "left":
                container.scrollLeft = Math.max(0, container.scrollLeft - amount);
                break;
            case "right":
                container.scrollLeft = Math.min(
                    container.scrollWidth - container.clientWidth,
                    container.scrollLeft + amount
                );
                break;
            case "up":
                container.scrollTop = Math.max(0, container.scrollTop - amount);
                break;
            case "down":
                container.scrollTop = Math.min(
                    container.scrollHeight - container.clientHeight,
                    container.scrollTop + amount
                );
                break;
        }
    };

    return checkEdgeScrolling;
}
```

## 高级应用模式

### 1. 复杂拖拽系统构建器
```javascript
class AdvancedDraggableBuilder {
    constructor() {
        this.plugins = new Map();
        this.middlewares = [];
        this.globalConfig = {
            enableLogging: false,
            enableAnalytics: false,
            performanceMode: false
        };
    }

    addPlugin(name, plugin) {
        this.plugins.set(name, plugin);
        return this;
    }

    addMiddleware(middleware) {
        this.middlewares.push(middleware);
        return this;
    }

    setGlobalConfig(config) {
        Object.assign(this.globalConfig, config);
        return this;
    }

    build(hookParams) {
        // 应用中间件
        const processedParams = this.applyMiddlewares(hookParams);

        // 集成插件
        const enhancedParams = this.integratePlugins(processedParams);

        // 添加全局功能
        const finalParams = this.addGlobalFeatures(enhancedParams);

        return makeDraggableHook(finalParams);
    }

    applyMiddlewares(hookParams) {
        return this.middlewares.reduce((params, middleware) => {
            return middleware(params) || params;
        }, { ...hookParams });
    }

    integratePlugins(hookParams) {
        const enhancedParams = { ...hookParams };

        // 集成所有插件
        this.plugins.forEach((plugin, name) => {
            if (plugin.onComputeParams) {
                const originalOnComputeParams = enhancedParams.onComputeParams;
                enhancedParams.onComputeParams = (params) => {
                    originalOnComputeParams?.(params);
                    plugin.onComputeParams(params);
                };
            }

            if (plugin.onDragStart) {
                const originalOnDragStart = enhancedParams.onDragStart;
                enhancedParams.onDragStart = (params) => {
                    originalOnDragStart?.(params);
                    plugin.onDragStart(params);
                };
            }

            // 集成其他生命周期Hook...
        });

        return enhancedParams;
    }

    addGlobalFeatures(hookParams) {
        const enhancedParams = { ...hookParams };

        if (this.globalConfig.enableLogging) {
            this.addLoggingFeature(enhancedParams);
        }

        if (this.globalConfig.enableAnalytics) {
            this.addAnalyticsFeature(enhancedParams);
        }

        if (this.globalConfig.performanceMode) {
            this.addPerformanceOptimizations(enhancedParams);
        }

        return enhancedParams;
    }

    addLoggingFeature(params) {
        const originalOnDragStart = params.onDragStart;
        params.onDragStart = (hookParams) => {
            console.log(`[${params.name}] 拖拽开始:`, hookParams.element);
            originalOnDragStart?.(hookParams);
        };

        const originalOnDragEnd = params.onDragEnd;
        params.onDragEnd = (hookParams) => {
            console.log(`[${params.name}] 拖拽结束:`, hookParams.element);
            originalOnDragEnd?.(hookParams);
        };
    }

    addAnalyticsFeature(params) {
        const originalOnDragStart = params.onDragStart;
        params.onDragStart = (hookParams) => {
            this.recordAnalytics('drag_start', {
                hookName: params.name,
                element: hookParams.element.tagName,
                timestamp: Date.now()
            });
            originalOnDragStart?.(hookParams);
        };
    }

    addPerformanceOptimizations(params) {
        // 添加性能优化
        params.defaultParams = {
            ...params.defaultParams,
            tolerance: 5, // 减少灵敏度以提高性能
            delay: 50     // 增加延迟以减少误触发
        };
    }

    recordAnalytics(event, data) {
        // 发送分析数据
        if (typeof gtag !== 'undefined') {
            gtag('event', event, data);
        }
    }
}

// 使用示例
const builder = new AdvancedDraggableBuilder();

// 添加插件
builder.addPlugin('validation', {
    onDragStart: ({ element }) => {
        if (!element.dataset.draggable) {
            throw new Error('元素不允许拖拽');
        }
    }
});

builder.addPlugin('persistence', {
    onDrop: ({ element, x, y }) => {
        localStorage.setItem(`element_${element.id}_position`, JSON.stringify({ x, y }));
    }
});

// 添加中间件
builder.addMiddleware((params) => {
    // 参数验证中间件
    if (!params.ref) {
        throw new Error('缺少ref参数');
    }
    return params;
});

// 构建Hook
const useAdvancedDraggable = builder
    .setGlobalConfig({ enableLogging: true, enableAnalytics: true })
    .build({
        name: "useAdvancedDraggable",
        onDragStart: ({ element }) => {
            element.classList.add("advanced-dragging");
        }
    });
```

### 2. 拖拽状态管理系统
```javascript
class DraggableStateManager {
    constructor() {
        this.states = new Map();
        this.globalState = {
            isDragging: false,
            currentDragElement: null,
            dragStartTime: null,
            dragCount: 0
        };
        this.subscribers = new Set();
    }

    createStatefulDraggable(hookParams) {
        const stateId = hookParams.stateId || `draggable_${Date.now()}`;

        // 初始化状态
        this.states.set(stateId, {
            isDragging: false,
            element: null,
            startPosition: null,
            currentPosition: null,
            dragDistance: 0,
            dragDuration: 0
        });

        return makeDraggableHook({
            ...hookParams,

            onDragStart: (params) => {
                const state = this.states.get(stateId);
                state.isDragging = true;
                state.element = params.element;
                state.startPosition = { x: params.x, y: params.y };
                state.currentPosition = { x: params.x, y: params.y };
                state.dragStartTime = Date.now();

                this.globalState.isDragging = true;
                this.globalState.currentDragElement = params.element;
                this.globalState.dragStartTime = Date.now();
                this.globalState.dragCount++;

                this.notifySubscribers('dragStart', { stateId, state, globalState: this.globalState });

                hookParams.onDragStart?.(params);
            },

            onDrag: (params) => {
                const state = this.states.get(stateId);
                state.currentPosition = { x: params.x, y: params.y };
                state.dragDistance = this.calculateDistance(state.startPosition, state.currentPosition);
                state.dragDuration = Date.now() - state.dragStartTime;

                this.notifySubscribers('drag', { stateId, state, globalState: this.globalState });

                hookParams.onDrag?.(params);
            },

            onDragEnd: (params) => {
                const state = this.states.get(stateId);
                state.isDragging = false;
                state.dragDuration = Date.now() - state.dragStartTime;

                this.globalState.isDragging = false;
                this.globalState.currentDragElement = null;

                this.notifySubscribers('dragEnd', { stateId, state, globalState: this.globalState });

                hookParams.onDragEnd?.(params);

                // 重置状态
                setTimeout(() => {
                    state.element = null;
                    state.startPosition = null;
                    state.currentPosition = null;
                    state.dragDistance = 0;
                    state.dragDuration = 0;
                }, 100);
            }
        });
    }

    calculateDistance(start, end) {
        const dx = end.x - start.x;
        const dy = end.y - start.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('状态订阅者回调失败:', error);
            }
        });
    }

    getState(stateId) {
        return this.states.get(stateId);
    }

    getGlobalState() {
        return { ...this.globalState };
    }

    getAllStates() {
        const allStates = {};
        this.states.forEach((state, stateId) => {
            allStates[stateId] = { ...state };
        });
        return allStates;
    }
}

// 使用示例
const stateManager = new DraggableStateManager();

// 订阅状态变化
const unsubscribe = stateManager.subscribe((event, data) => {
    console.log(`拖拽事件: ${event}`, data);

    if (event === 'dragStart') {
        document.body.classList.add('dragging-active');
    } else if (event === 'dragEnd') {
        document.body.classList.remove('dragging-active');
    }
});

// 创建状态管理的拖拽Hook
const useStatefulDraggable = stateManager.createStatefulDraggable({
    name: "useStatefulDraggable",
    stateId: "main-draggable",

    onDragStart: ({ element }) => {
        console.log("开始拖拽:", element);
    }
});
```

## 最佳实践

### 1. Hook构建模式
```javascript
// ✅ 推荐：模块化Hook构建
const createDraggableHook = (baseConfig) => {
    return makeDraggableHook({
        name: baseConfig.name,

        // 参数验证
        onComputeParams: ({ ctx, params }) => {
            if (!params.ref || !params.ref.el) {
                throw new Error(`${baseConfig.name}: 缺少有效的ref参数`);
            }

            if (!params.elements) {
                throw new Error(`${baseConfig.name}: 缺少elements选择器`);
            }

            // 应用基础配置
            Object.assign(ctx, baseConfig.defaultContext || {});

            // 调用自定义参数处理
            baseConfig.onComputeParams?.(ctx, params);
        },

        // 统一的错误处理
        onDragStart: (hookParams) => {
            try {
                baseConfig.onDragStart?.(hookParams);
            } catch (error) {
                console.error(`${baseConfig.name} 拖拽开始失败:`, error);
                return false; // 阻止拖拽
            }
        },

        onDrag: (hookParams) => {
            try {
                baseConfig.onDrag?.(hookParams);
            } catch (error) {
                console.error(`${baseConfig.name} 拖拽处理失败:`, error);
            }
        },

        onDragEnd: (hookParams) => {
            try {
                baseConfig.onDragEnd?.(hookParams);
            } catch (error) {
                console.error(`${baseConfig.name} 拖拽结束失败:`, error);
            }
        }
    });
};

// 使用示例
const useImageDraggable = createDraggableHook({
    name: "useImageDraggable",

    defaultContext: {
        imageConfig: {
            preserveAspectRatio: true,
            maxSize: { width: 800, height: 600 }
        }
    },

    onComputeParams: (ctx, params) => {
        // 验证是否为图片元素
        const element = ctx.ref.el.querySelector(params.elements);
        if (element && element.tagName !== 'IMG') {
            throw new Error('只能拖拽图片元素');
        }
    },

    onDragStart: ({ element, addCleanup }) => {
        element.style.opacity = '0.7';
        addCleanup(() => {
            element.style.opacity = '';
        });
    }
});
```

### 2. 性能优化
```javascript
// ✅ 推荐：性能优化的Hook构建
const useOptimizedDraggable = makeDraggableHook({
    name: "useOptimizedDraggable",

    defaultParams: {
        tolerance: 5,        // 减少触发频率
        delay: 100,          // 增加延迟避免误触发
        throttleInterval: 16 // 60fps节流
    },

    onComputeParams: ({ ctx, params }) => {
        // 预计算常用值
        ctx.optimizations = {
            useTransform: true,
            useWillChange: true,
            batchUpdates: true
        };
    },

    onDragStart: ({ ctx, element, addCleanup }) => {
        if (ctx.optimizations.useWillChange) {
            element.style.willChange = 'transform';
            addCleanup(() => {
                element.style.willChange = '';
            });
        }

        // 批量DOM更新
        if (ctx.optimizations.batchUpdates) {
            ctx.pendingUpdates = [];
            ctx.updateScheduled = false;
        }
    },

    onDrag: ({ ctx, element, x, y }) => {
        if (ctx.optimizations.batchUpdates) {
            ctx.pendingUpdates.push({ x, y });

            if (!ctx.updateScheduled) {
                ctx.updateScheduled = true;
                requestAnimationFrame(() => {
                    const lastUpdate = ctx.pendingUpdates[ctx.pendingUpdates.length - 1];
                    if (lastUpdate) {
                        element.style.transform = `translate(${lastUpdate.x}px, ${lastUpdate.y}px)`;
                    }
                    ctx.pendingUpdates.length = 0;
                    ctx.updateScheduled = false;
                });
            }
        } else {
            element.style.transform = `translate(${x}px, ${y}px)`;
        }
    }
});
```

### 3. 错误处理和调试
```javascript
// ✅ 推荐：完善的错误处理
const useRobustDraggable = makeDraggableHook({
    name: "useRobustDraggable",

    onComputeParams: ({ ctx, params }) => {
        // 参数验证
        const validations = [
            {
                condition: !params.ref || !params.ref.el,
                message: "缺少有效的ref参数"
            },
            {
                condition: !params.elements,
                message: "缺少elements选择器"
            },
            {
                condition: typeof params.elements !== 'string',
                message: "elements必须是字符串选择器"
            }
        ];

        const errors = validations.filter(v => v.condition);
        if (errors.length > 0) {
            throw new Error(`参数验证失败: ${errors.map(e => e.message).join(', ')}`);
        }

        // 调试信息
        if (params.debug) {
            ctx.debug = {
                startTime: null,
                dragCount: 0,
                errorCount: 0,
                log: (message, data) => {
                    console.log(`[${ctx.name}] ${message}`, data);
                }
            };
        }
    },

    onDragStart: ({ ctx, element }) => {
        try {
            if (ctx.debug) {
                ctx.debug.startTime = performance.now();
                ctx.debug.dragCount++;
                ctx.debug.log('拖拽开始', { element, count: ctx.debug.dragCount });
            }

            // 检查元素状态
            if (!element.isConnected) {
                throw new Error('拖拽元素已从DOM中移除');
            }

            if (element.style.display === 'none') {
                throw new Error('不能拖拽隐藏元素');
            }

        } catch (error) {
            if (ctx.debug) {
                ctx.debug.errorCount++;
                ctx.debug.log('拖拽开始失败', { error: error.message });
            }
            throw error;
        }
    },

    onDragEnd: ({ ctx, element }) => {
        if (ctx.debug && ctx.debug.startTime) {
            const duration = performance.now() - ctx.debug.startTime;
            ctx.debug.log('拖拽结束', {
                duration: `${duration.toFixed(2)}ms`,
                element: element.tagName,
                totalDrags: ctx.debug.dragCount,
                errors: ctx.debug.errorCount
            });
        }
    }
});
```

### 4. 内存管理
```javascript
// ✅ 推荐：正确的内存管理
const useMemoryEfficientDraggable = makeDraggableHook({
    name: "useMemoryEfficientDraggable",

    onComputeParams: ({ ctx, params }) => {
        // 使用WeakMap避免内存泄漏
        ctx.elementData = new WeakMap();
        ctx.cleanupFunctions = new Set();
    },

    onDragStart: ({ ctx, element, addCleanup }) => {
        // 存储元素相关数据
        ctx.elementData.set(element, {
            originalPosition: {
                x: element.offsetLeft,
                y: element.offsetTop
            },
            startTime: Date.now()
        });

        // 注册清理函数
        const cleanup = () => {
            ctx.elementData.delete(element);
        };

        ctx.cleanupFunctions.add(cleanup);
        addCleanup(() => {
            cleanup();
            ctx.cleanupFunctions.delete(cleanup);
        });
    },

    onDragEnd: ({ ctx }) => {
        // 清理所有临时数据
        ctx.cleanupFunctions.forEach(cleanup => {
            try {
                cleanup();
            } catch (error) {
                console.error('清理函数执行失败:', error);
            }
        });
        ctx.cleanupFunctions.clear();
    }
});
```

## 总结

Odoo 拖拽Hook构建器是一个强大而灵活的拖拽系统构建框架：

**核心优势**:
- **Hook工厂模式**: 通过配置生成特定的拖拽Hook
- **完整生命周期**: 提供完整的拖拽生命周期管理
- **自动清理**: 智能的资源管理和清理机制
- **跨浏览器兼容**: 处理不同浏览器的兼容性问题
- **高度可配置**: 支持丰富的配置选项和自定义

**适用场景**:
- 自定义拖拽功能开发
- 复杂交互系统构建
- 排序和拖放功能实现
- 画布和绘图应用
- 文件拖放处理

**设计优势**:
- 模块化架构设计
- 插件和中间件支持
- 完整的错误处理机制
- 性能优化考虑
- 内存管理优化

这个拖拽Hook构建器为 Odoo Web 客户端提供了构建各种拖拽功能的强大基础，是实现复杂交互的重要工具。

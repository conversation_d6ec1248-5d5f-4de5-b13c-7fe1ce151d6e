# Odoo 对象工具 (Objects Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/objects.js`  
**原始路径**: `/web/static/src/core/utils/objects.js`  
**模块类型**: 核心工具模块 - 对象处理工具  
**代码行数**: 127 行  
**依赖关系**: 无外部依赖

## 模块功能

对象工具模块是 Odoo Web 客户端的核心工具库，提供了丰富的对象操作功能，包括：
- 对象比较（浅比较和深比较）
- 对象复制（深拷贝）
- 对象属性选择和排除
- 对象深度合并
- 对象类型检测

## 核心函数详解

### 1. shallowEqual() - 浅比较
```javascript
function shallowEqual(obj1, obj2, comparisonFn = (a, b) => a === b) {
    if (!isObject(obj1) || !isObject(obj2)) {
        return obj1 === obj2;
    }
    const obj1Keys = Reflect.ownKeys(obj1);
    return (
        obj1Keys.length === Reflect.ownKeys(obj2).length &&
        obj1Keys.every((key) => comparisonFn(obj1[key], obj2[key]))
    );
}
```

**功能特性**:
- **浅层比较**: 只比较对象的第一层属性
- **自定义比较**: 支持自定义比较函数
- **类型安全**: 完整的 TypeScript 类型支持
- **性能优化**: 使用 Reflect.ownKeys 获取所有属性

**使用示例**:
```javascript
// 基本浅比较
const obj1 = { a: 1, b: 2, c: { d: 3 } };
const obj2 = { a: 1, b: 2, c: { d: 3 } };
const obj3 = { a: 1, b: 2, c: { d: 4 } };

console.log(shallowEqual(obj1, obj2)); // false (不同的对象引用)
console.log(shallowEqual(obj1, obj1)); // true (相同引用)

// 基本类型比较
console.log(shallowEqual(1, 1));       // true
console.log(shallowEqual("a", "a"));   // true
console.log(shallowEqual(null, null)); // true

// 数组比较
const arr1 = [1, 2, 3];
const arr2 = [1, 2, 3];
console.log(shallowEqual(arr1, arr2)); // false (不同数组引用)

// 自定义比较函数
const customEqual = shallowEqual(
    { a: "1", b: "2" },
    { a: 1, b: 2 },
    (a, b) => String(a) === String(b) // 字符串比较
);
console.log(customEqual); // true
```

**实际应用场景**:
```javascript
// 1. React-like 组件优化
class OptimizedComponent extends Component {
    shouldUpdate(nextProps) {
        // 只有 props 真正改变时才重新渲染
        return !shallowEqual(this.props, nextProps);
    }
}

// 2. 状态变化检测
class StateManager {
    constructor() {
        this.state = {};
        this.listeners = [];
    }
    
    setState(newState) {
        if (!shallowEqual(this.state, newState)) {
            const oldState = this.state;
            this.state = { ...this.state, ...newState };
            this.notifyListeners(oldState, this.state);
        }
    }
    
    notifyListeners(oldState, newState) {
        this.listeners.forEach(listener => listener(oldState, newState));
    }
}

// 3. 缓存键生成
class CacheManager {
    constructor() {
        this.cache = new Map();
    }
    
    get(params) {
        // 查找具有相同参数的缓存项
        for (const [cachedParams, result] of this.cache) {
            if (shallowEqual(params, cachedParams)) {
                return result;
            }
        }
        return null;
    }
    
    set(params, result) {
        this.cache.set(params, result);
    }
}
```

### 2. deepEqual() - 深比较
```javascript
const deepEqual = (obj1, obj2) => shallowEqual(obj1, obj2, deepEqual);
```

**功能特性**:
- **递归比较**: 递归比较对象的所有层级
- **基于浅比较**: 复用 shallowEqual 的逻辑
- **完整比较**: 比较对象的完整结构和值

**使用示例**:
```javascript
// 深层对象比较
const obj1 = {
    a: 1,
    b: {
        c: 2,
        d: {
            e: 3,
            f: [1, 2, 3]
        }
    }
};

const obj2 = {
    a: 1,
    b: {
        c: 2,
        d: {
            e: 3,
            f: [1, 2, 3]
        }
    }
};

const obj3 = {
    a: 1,
    b: {
        c: 2,
        d: {
            e: 4, // 不同的值
            f: [1, 2, 3]
        }
    }
};

console.log(deepEqual(obj1, obj2)); // true (结构和值完全相同)
console.log(deepEqual(obj1, obj3)); // false (深层值不同)

// 复杂数据结构比较
const config1 = {
    database: {
        host: "localhost",
        port: 5432,
        credentials: {
            username: "admin",
            password: "secret"
        }
    },
    features: ["auth", "logging", "cache"]
};

const config2 = {
    database: {
        host: "localhost",
        port: 5432,
        credentials: {
            username: "admin",
            password: "secret"
        }
    },
    features: ["auth", "logging", "cache"]
};

console.log(deepEqual(config1, config2)); // true
```

**实际应用场景**:
```javascript
// 1. 配置变化检测
class ConfigManager {
    constructor() {
        this.config = {};
        this.changeListeners = [];
    }
    
    updateConfig(newConfig) {
        if (!deepEqual(this.config, newConfig)) {
            const oldConfig = deepCopy(this.config);
            this.config = newConfig;
            this.notifyConfigChange(oldConfig, newConfig);
        }
    }
    
    notifyConfigChange(oldConfig, newConfig) {
        this.changeListeners.forEach(listener => {
            listener(oldConfig, newConfig);
        });
    }
}

// 2. 表单数据验证
class FormValidator {
    constructor(initialData) {
        this.initialData = deepCopy(initialData);
    }
    
    hasChanges(currentData) {
        return !deepEqual(this.initialData, currentData);
    }
    
    getChangedFields(currentData) {
        const changes = {};
        for (const key in currentData) {
            if (!deepEqual(this.initialData[key], currentData[key])) {
                changes[key] = {
                    old: this.initialData[key],
                    new: currentData[key]
                };
            }
        }
        return changes;
    }
}
```

### 3. deepCopy() - 深拷贝
```javascript
function deepCopy(object) {
    return object && JSON.parse(JSON.stringify(object));
}
```

**功能特性**:
- **完整复制**: 创建对象的完全独立副本
- **JSON 基础**: 基于 JSON 序列化/反序列化
- **简单高效**: 实现简单，性能良好
- **限制明确**: 有明确的使用限制

**限制说明**:
- 不支持循环引用对象
- 不支持特殊类（Map、Set、Date、RegExp等）
- 函数会被忽略
- undefined 值会被忽略

**使用示例**:
```javascript
// 基本深拷贝
const original = {
    a: 1,
    b: {
        c: 2,
        d: [3, 4, 5]
    }
};

const copied = deepCopy(original);
copied.b.c = 999;

console.log(original.b.c); // 2 (原对象未被修改)
console.log(copied.b.c);   // 999 (副本被修改)

// 数组深拷贝
const originalArray = [
    { id: 1, name: "Alice" },
    { id: 2, name: "Bob" }
];

const copiedArray = deepCopy(originalArray);
copiedArray[0].name = "Charlie";

console.log(originalArray[0].name); // "Alice"
console.log(copiedArray[0].name);   // "Charlie"

// 复杂对象拷贝
const complexObject = {
    users: [
        { id: 1, profile: { name: "Alice", age: 25 } },
        { id: 2, profile: { name: "Bob", age: 30 } }
    ],
    settings: {
        theme: "dark",
        notifications: {
            email: true,
            push: false
        }
    }
};

const copiedComplex = deepCopy(complexObject);
```

**实际应用场景**:
```javascript
// 1. 状态管理
class StateStore {
    constructor(initialState) {
        this.state = deepCopy(initialState);
        this.history = [deepCopy(initialState)];
    }
    
    setState(newState) {
        this.state = deepCopy(newState);
        this.history.push(deepCopy(newState));
    }
    
    undo() {
        if (this.history.length > 1) {
            this.history.pop();
            this.state = deepCopy(this.history[this.history.length - 1]);
        }
    }
}

// 2. 表单数据处理
class FormManager {
    constructor(schema) {
        this.schema = schema;
        this.originalData = {};
    }
    
    loadData(data) {
        this.originalData = deepCopy(data);
        return deepCopy(data); // 返回副本供编辑
    }
    
    resetForm() {
        return deepCopy(this.originalData);
    }
    
    saveData(formData) {
        // 保存前创建副本，避免意外修改
        const dataToSave = deepCopy(formData);
        return this.submitToServer(dataToSave);
    }
}

// 3. 配置模板
class ConfigTemplate {
    constructor() {
        this.templates = {
            development: {
                database: { host: "localhost", port: 5432 },
                debug: true,
                logging: { level: "debug" }
            },
            production: {
                database: { host: "prod-db", port: 5432 },
                debug: false,
                logging: { level: "error" }
            }
        };
    }
    
    getConfig(environment) {
        // 返回模板的深拷贝，避免意外修改模板
        return deepCopy(this.templates[environment]);
    }
}
```

### 4. isObject() - 对象类型检测
```javascript
function isObject(object) {
    return !!object && (typeof object === "object" || typeof object === "function");
}
```

**功能特性**:
- **类型检测**: 检测值是否为对象类型
- **包含函数**: 函数也被视为对象
- **排除 null**: null 不被视为对象
- **简单高效**: 实现简单，性能优异

**使用示例**:
```javascript
// 基本类型检测
console.log(isObject({}));          // true
console.log(isObject([]));          // true
console.log(isObject(function(){})); // true
console.log(isObject(new Date()));  // true
console.log(isObject(/regex/));     // true

console.log(isObject(null));        // false
console.log(isObject(undefined));   // false
console.log(isObject(42));          // false
console.log(isObject("string"));    // false
console.log(isObject(true));        // false

// 实际应用
function processValue(value) {
    if (isObject(value)) {
        // 处理对象类型
        return processObject(value);
    } else {
        // 处理基本类型
        return processPrimitive(value);
    }
}
```

### 5. omit() - 属性排除
```javascript
function omit(object, ...properties) {
    const result = {};
    const propertiesSet = new Set(properties);
    for (const key in object) {
        if (!propertiesSet.has(key)) {
            result[key] = object[key];
        }
    }
    return result;
}
```

**功能特性**:
- **属性排除**: 创建排除指定属性的新对象
- **多属性支持**: 支持同时排除多个属性
- **类型安全**: 完整的 TypeScript 类型推断
- **性能优化**: 使用 Set 进行快速查找

**使用示例**:
```javascript
// 基本属性排除
const user = {
    id: 1,
    name: "Alice",
    email: "<EMAIL>",
    password: "secret123",
    role: "admin"
};

// 排除敏感信息
const publicUser = omit(user, "password");
console.log(publicUser);
// { id: 1, name: "Alice", email: "<EMAIL>", role: "admin" }

// 排除多个属性
const basicUser = omit(user, "password", "role", "email");
console.log(basicUser);
// { id: 1, name: "Alice" }

// API 响应处理
const apiResponse = {
    data: { /* 用户数据 */ },
    meta: { /* 元数据 */ },
    _internal: { /* 内部数据 */ },
    _cache: { /* 缓存数据 */ }
};

const cleanResponse = omit(apiResponse, "_internal", "_cache");
```

**实际应用场景**:
```javascript
// 1. API 数据清理
class ApiService {
    async getUser(userId) {
        const rawUser = await this.fetchUser(userId);
        
        // 移除内部字段
        return omit(rawUser, "_id", "_rev", "_internal", "password");
    }
    
    async updateUser(userId, userData) {
        // 移除只读字段
        const updateData = omit(userData, "id", "createdAt", "updatedAt");
        return this.patchUser(userId, updateData);
    }
}

// 2. 表单数据处理
class FormProcessor {
    processFormData(formData) {
        // 移除表单控制字段
        const cleanData = omit(formData, 
            "_token", "_method", "_redirect", "submit"
        );
        
        return this.validateAndSanitize(cleanData);
    }
}

// 3. 组件 Props 处理
class Component {
    constructor(props) {
        // 分离组件专用 props 和传递给子元素的 props
        this.componentProps = pick(props, "className", "style", "onClick");
        this.elementProps = omit(props, "className", "style", "onClick");
    }
}
```

### 6. pick() - 属性选择
```javascript
function pick(object, ...properties) {
    return Object.fromEntries(
        properties.filter((prop) => prop in object).map((prop) => [prop, object[prop]])
    );
}
```

**功能特性**:
- **属性选择**: 创建只包含指定属性的新对象
- **多属性支持**: 支持同时选择多个属性
- **存在性检查**: 只选择对象中实际存在的属性
- **类型安全**: 完整的 TypeScript 类型推断

**使用示例**:
```javascript
// 基本属性选择
const user = {
    id: 1,
    name: "Alice",
    email: "<EMAIL>",
    password: "secret123",
    role: "admin",
    lastLogin: "2023-01-01"
};

// 选择公开信息
const publicInfo = pick(user, "id", "name", "email");
console.log(publicInfo);
// { id: 1, name: "Alice", email: "<EMAIL>" }

// 选择认证信息
const authInfo = pick(user, "id", "role", "lastLogin");
console.log(authInfo);
// { id: 1, role: "admin", lastLogin: "2023-01-01" }

// 选择不存在的属性
const partial = pick(user, "id", "name", "nonexistent");
console.log(partial);
// { id: 1, name: "Alice" } (不存在的属性被忽略)
```

**实际应用场景**:
```javascript
// 1. API 响应格式化
class UserController {
    async getProfile(req, res) {
        const user = await User.findById(req.userId);

        // 只返回用户资料相关字段
        const profile = pick(user,
            "id", "name", "email", "avatar", "bio", "location"
        );

        res.json(profile);
    }

    async getSettings(req, res) {
        const user = await User.findById(req.userId);

        // 只返回设置相关字段
        const settings = pick(user,
            "theme", "language", "notifications", "privacy"
        );

        res.json(settings);
    }
}

// 2. 表单字段提取
class FormValidator {
    validateUserForm(formData) {
        // 只验证用户相关字段
        const userFields = pick(formData,
            "name", "email", "phone", "address"
        );

        return this.validate(userFields, this.userSchema);
    }

    validateCompanyForm(formData) {
        // 只验证公司相关字段
        const companyFields = pick(formData,
            "companyName", "industry", "size", "website"
        );

        return this.validate(companyFields, this.companySchema);
    }
}

// 3. 数据库查询优化
class DatabaseService {
    async getUserSummary(userId) {
        const user = await this.getUser(userId);

        // 只返回摘要信息，减少数据传输
        return pick(user, "id", "name", "status", "lastActive");
    }

    async getUserDetails(userId) {
        const user = await this.getUser(userId);

        // 返回详细信息
        return pick(user,
            "id", "name", "email", "profile", "settings", "permissions"
        );
    }
}
```

### 7. deepMerge() - 深度合并
```javascript
function deepMerge(target, extension) {
    if (!isObject(target) && !isObject(extension)) {
        return;
    }

    target = target || {};
    const output = Object.assign({}, target);
    if (isObject(extension)) {
        for (const key of Reflect.ownKeys(extension)) {
            if (
                key in target &&
                isObject(extension[key]) &&
                !Array.isArray(extension[key]) &&
                typeof extension[key] !== "function"
            ) {
                output[key] = deepMerge(target[key], extension[key]);
            } else {
                Object.assign(output, { [key]: extension[key] });
            }
        }
    }

    return output;
}
```

**功能特性**:
- **递归合并**: 递归合并嵌套对象
- **数组替换**: 数组不合并，直接替换
- **函数替换**: 函数不合并，直接替换
- **类型安全**: 保持原对象不变，返回新对象

**使用示例**:
```javascript
// 基本深度合并
const target = {
    a: 1,
    b: {
        c: 2,
        d: 3
    },
    e: [1, 2, 3]
};

const extension = {
    a: 2,        // 覆盖基本值
    b: {
        d: 4,    // 覆盖嵌套值
        f: 5     // 添加新值
    },
    e: [4, 5],   // 替换数组
    g: 6         // 添加新属性
};

const result = deepMerge(target, extension);
console.log(result);
// {
//   a: 2,
//   b: { c: 2, d: 4, f: 5 },
//   e: [4, 5],
//   g: 6
// }
```

## 最佳实践

### 1. 选择合适的比较方法
```javascript
// ✅ 推荐：根据需求选择比较方法
function shouldUpdate(prevProps, nextProps) {
    // 对于简单 props，使用浅比较
    if (hasOnlyPrimitives(prevProps) && hasOnlyPrimitives(nextProps)) {
        return !shallowEqual(prevProps, nextProps);
    }

    // 对于复杂 props，使用深比较
    return !deepEqual(prevProps, nextProps);
}

// ❌ 避免：总是使用深比较
function inefficientShouldUpdate(prevProps, nextProps) {
    return !deepEqual(prevProps, nextProps); // 可能很慢
}
```

### 2. 合理使用深拷贝
```javascript
// ✅ 推荐：只在必要时使用深拷贝
function processUserData(userData) {
    // 对于不会修改的操作，不需要拷贝
    const validation = validateUser(userData);

    // 对于会修改的操作，使用深拷贝
    if (validation.needsNormalization) {
        const normalizedData = deepCopy(userData);
        return normalizeUserData(normalizedData);
    }

    return userData;
}

// ❌ 避免：不必要的深拷贝
function inefficientProcess(data) {
    const copy = deepCopy(data); // 不必要的拷贝
    return copy.id; // 只是读取属性
}
```

## 总结

Odoo 对象工具模块提供了完整的对象操作功能：

**核心优势**:
- **功能完整**: 涵盖对象比较、复制、选择、合并等常见需求
- **性能优化**: 使用高效的算法和数据结构
- **类型安全**: 完整的 TypeScript 类型支持
- **易于使用**: 直观的 API 设计

**适用场景**:
- 状态管理和比较
- 配置系统和主题管理
- API 数据处理
- 表单数据操作
- 组件属性处理

这个工具模块为 Odoo Web 客户端提供了强大的对象处理能力，是构建复杂数据操作功能的重要基础。

# Odoo 并发控制工具 (Concurrency Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/concurrency.js`  
**原始路径**: `/web/static/src/core/utils/concurrency.js`  
**模块类型**: 核心工具模块 - 并发控制工具  
**代码行数**: 198 行  
**依赖关系**: 无外部依赖

## 模块功能

并发控制工具模块是 Odoo Web 客户端的核心工具库，提供了强大的异步操作和并发控制功能，包括：
- 延迟执行（Delay）
- 保持最后任务（KeepLast）
- 互斥锁（Mutex）
- 竞态控制（Race）
- 延迟对象（Deferred）

这些工具解决了现代Web应用中常见的并发问题，如竞态条件、资源竞争、异步操作管理等。

## 核心工具详解

### 1. delay() - 延迟执行
```javascript
function delay(wait) {
    return new Promise(function (resolve) {
        setTimeout(resolve, wait);
    });
}
```

**功能特性**:
- **简单延迟**: 创建指定时间后解析的 Promise
- **异步等待**: 可与 async/await 配合使用
- **无返回值**: 纯粹的时间延迟工具
- **轻量实现**: 基于 setTimeout 的简单封装

**使用示例**:
```javascript
// 基本延迟
await delay(1000); // 等待1秒
console.log("1秒后执行");

// 在异步函数中使用
async function processWithDelay() {
    console.log("开始处理");
    await delay(500);
    console.log("500ms后继续");
    await delay(1000);
    console.log("再等1秒后完成");
}

// 模拟网络延迟
async function simulateNetworkRequest() {
    console.log("发送请求...");
    await delay(Math.random() * 2000); // 随机延迟0-2秒
    console.log("请求完成");
    return { data: "模拟数据" };
}
```

**实际应用场景**:
```javascript
// 1. 重试机制
class RetryableRequest {
    async makeRequest(url, maxRetries = 3) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                const response = await fetch(url);
                if (response.ok) {
                    return await response.json();
                }
                throw new Error(`HTTP ${response.status}`);
            } catch (error) {
                console.log(`尝试 ${attempt} 失败:`, error.message);
                
                if (attempt === maxRetries) {
                    throw error;
                }
                
                // 指数退避延迟
                const delayTime = Math.pow(2, attempt) * 1000;
                await delay(delayTime);
            }
        }
    }
}

// 2. 动画序列
class AnimationSequence {
    async playSequence() {
        // 淡入
        this.element.style.opacity = '0';
        this.element.style.transition = 'opacity 0.5s';
        this.element.style.opacity = '1';
        
        await delay(500); // 等待淡入完成
        
        // 移动
        this.element.style.transition = 'transform 1s';
        this.element.style.transform = 'translateX(100px)';
        
        await delay(1000); // 等待移动完成
        
        // 缩放
        this.element.style.transition = 'transform 0.3s';
        this.element.style.transform = 'translateX(100px) scale(1.2)';
        
        await delay(300); // 等待缩放完成
    }
}

// 3. 分批处理
class BatchProcessor {
    async processBatch(items, batchSize = 10, delayBetweenBatches = 100) {
        const results = [];
        
        for (let i = 0; i < items.length; i += batchSize) {
            const batch = items.slice(i, i + batchSize);
            
            // 处理当前批次
            const batchResults = await Promise.all(
                batch.map(item => this.processItem(item))
            );
            
            results.push(...batchResults);
            
            // 批次间延迟，避免过载
            if (i + batchSize < items.length) {
                await delay(delayBetweenBatches);
            }
        }
        
        return results;
    }
    
    async processItem(item) {
        // 模拟处理时间
        await delay(Math.random() * 100);
        return `处理完成: ${item}`;
    }
}
```

### 2. KeepLast - 保持最后任务
```javascript
class KeepLast {
    constructor() {
        this._id = 0;
    }
    
    add(promise) {
        this._id++;
        const currentId = this._id;
        return new Promise((resolve, reject) => {
            promise
                .then((value) => {
                    if (this._id === currentId) {
                        resolve(value);
                    }
                })
                .catch((reason) => {
                    if (this._id === currentId) {
                        reject(reason);
                    }
                });
        });
    }
}
```

**功能特性**:
- **任务管理**: 管理多个异步任务，只保留最后一个
- **自动取消**: 新任务会自动取消之前的任务
- **ID 跟踪**: 使用递增ID跟踪任务顺序
- **竞态解决**: 解决异步操作的竞态条件问题

**使用示例**:
```javascript
const keepLast = new KeepLast();

// 快速连续的搜索请求
async function search(query) {
    const searchPromise = fetch(`/api/search?q=${query}`);
    
    try {
        const response = await keepLast.add(searchPromise);
        const results = await response.json();
        updateSearchResults(results);
    } catch (error) {
        // 如果不是最后一个请求，这里不会执行
        console.error("搜索失败:", error);
    }
}

// 用户快速输入
search("a");    // 被取消
search("ab");   // 被取消
search("abc");  // 被取消
search("abcd"); // 只有这个会执行回调
```

**实际应用场景**:
```javascript
// 1. 搜索建议
class SearchSuggestions {
    constructor() {
        this.keepLast = new KeepLast();
    }
    
    async getSuggestions(query) {
        if (query.length < 2) {
            this.clearSuggestions();
            return;
        }
        
        const searchPromise = fetch(`/api/suggestions?q=${query}`);
        
        try {
            const response = await this.keepLast.add(searchPromise);
            const suggestions = await response.json();
            this.displaySuggestions(suggestions);
        } catch (error) {
            // 只有最后一个请求的错误会被处理
            console.error("获取建议失败:", error);
        }
    }
    
    displaySuggestions(suggestions) {
        const container = document.getElementById('suggestions');
        container.innerHTML = suggestions
            .map(s => `<div class="suggestion">${s}</div>`)
            .join('');
    }
    
    clearSuggestions() {
        document.getElementById('suggestions').innerHTML = '';
    }
}

// 2. 数据加载
class DataLoader {
    constructor() {
        this.keepLast = new KeepLast();
    }
    
    async loadUserData(userId) {
        const loadPromise = fetch(`/api/users/${userId}`);
        
        try {
            const response = await this.keepLast.add(loadPromise);
            const userData = await response.json();
            this.updateUserDisplay(userData);
        } catch (error) {
            console.error("加载用户数据失败:", error);
        }
    }
    
    updateUserDisplay(userData) {
        // 只有最后一个用户的数据会被显示
        document.getElementById('user-name').textContent = userData.name;
        document.getElementById('user-email').textContent = userData.email;
    }
}

// 3. 实时预览
class PreviewGenerator {
    constructor() {
        this.keepLast = new KeepLast();
    }
    
    async generatePreview(content) {
        const previewPromise = fetch('/api/preview', {
            method: 'POST',
            body: JSON.stringify({ content }),
            headers: { 'Content-Type': 'application/json' }
        });
        
        try {
            const response = await this.keepLast.add(previewPromise);
            const previewHtml = await response.text();
            this.updatePreview(previewHtml);
        } catch (error) {
            console.error("生成预览失败:", error);
        }
    }
    
    updatePreview(html) {
        document.getElementById('preview').innerHTML = html;
    }
}
```

### 3. Mutex - 互斥锁
```javascript
class Mutex {
    constructor() {
        this._lock = Promise.resolve();
        this._queueSize = 0;
        this._unlockedProm = undefined;
        this._unlock = undefined;
    }
    
    async exec(action) {
        this._queueSize++;
        if (!this._unlockedProm) {
            this._unlockedProm = new Promise((resolve) => {
                this._unlock = () => {
                    resolve();
                    this._unlockedProm = undefined;
                };
            });
        }
        const always = () => {
            return Promise.resolve(action()).finally(() => {
                if (--this._queueSize === 0) {
                    this._unlock();
                }
            });
        };
        this._lock = this._lock.then(always, always);
        return this._lock;
    }
    
    getUnlockedDef() {
        return this._unlockedProm || Promise.resolve();
    }
}
```

**功能特性**:
- **串行执行**: 确保操作按顺序执行，避免竞态条件
- **队列管理**: 维护操作队列，按添加顺序执行
- **状态跟踪**: 跟踪锁定状态和队列大小
- **异常安全**: 即使操作失败也会正确释放锁

**使用示例**:
```javascript
const mutex = new Mutex();

// 保护共享资源
let sharedCounter = 0;

async function incrementCounter() {
    await mutex.exec(async () => {
        const current = sharedCounter;
        await delay(10); // 模拟异步操作
        sharedCounter = current + 1;
        console.log("计数器:", sharedCounter);
    });
}

// 并发调用，但串行执行
incrementCounter(); // 输出: 计数器: 1
incrementCounter(); // 输出: 计数器: 2
incrementCounter(); // 输出: 计数器: 3
```

**实际应用场景**:
```javascript
// 1. 数据库操作保护
class DatabaseManager {
    constructor() {
        this.mutex = new Mutex();
        this.data = {};
    }

    async updateRecord(id, updates) {
        return this.mutex.exec(async () => {
            // 读取当前数据
            const current = await this.loadRecord(id);

            // 应用更新
            const updated = { ...current, ...updates };

            // 保存数据
            await this.saveRecord(id, updated);

            return updated;
        });
    }

    async loadRecord(id) {
        // 模拟数据库读取
        await delay(50);
        return this.data[id] || {};
    }

    async saveRecord(id, record) {
        // 模拟数据库写入
        await delay(100);
        this.data[id] = record;
    }
}

// 2. 文件操作保护
class FileManager {
    constructor() {
        this.mutex = new Mutex();
    }

    async writeFile(filename, content) {
        return this.mutex.exec(async () => {
            console.log(`开始写入文件: ${filename}`);

            // 模拟文件写入
            await delay(200);

            console.log(`文件写入完成: ${filename}`);
            return { success: true, filename };
        });
    }

    async appendToFile(filename, content) {
        return this.mutex.exec(async () => {
            // 读取现有内容
            const existing = await this.readFile(filename);

            // 追加新内容
            const newContent = existing + content;

            // 写入文件
            await this.writeFileContent(filename, newContent);

            return newContent;
        });
    }
}

// 3. 状态管理保护
class StateManager {
    constructor() {
        this.mutex = new Mutex();
        this.state = {};
        this.listeners = [];
    }

    async setState(updates) {
        return this.mutex.exec(async () => {
            const oldState = { ...this.state };

            // 应用状态更新
            this.state = { ...this.state, ...updates };

            // 通知监听器
            await this.notifyListeners(oldState, this.state);
        });
    }

    async notifyListeners(oldState, newState) {
        // 串行通知所有监听器
        for (const listener of this.listeners) {
            await listener(oldState, newState);
        }
    }

    subscribe(listener) {
        this.listeners.push(listener);
    }
}
```

### 4. Race - 竞态控制
```javascript
class Race {
    constructor() {
        this.currentProm = null;
        this.currentPromResolver = null;
        this.currentPromRejecter = null;
    }

    add(promise) {
        if (!this.currentProm) {
            this.currentProm = new Promise((resolve, reject) => {
                this.currentPromResolver = (value) => {
                    this.currentProm = null;
                    this.currentPromResolver = null;
                    this.currentPromRejecter = null;
                    resolve(value);
                };
                this.currentPromRejecter = (error) => {
                    this.currentProm = null;
                    this.currentPromResolver = null;
                    this.currentPromRejecter = null;
                    reject(error);
                };
            });
        }
        promise.then(this.currentPromResolver).catch(this.currentPromRejecter);
        return this.currentProm;
    }

    getCurrentProm() {
        return this.currentProm;
    }
}
```

**功能特性**:
- **动态竞态**: 可以动态添加 Promise 到竞态中
- **首个获胜**: 第一个解析的 Promise 决定结果
- **自动重置**: 竞态结束后自动重置，准备下一轮
- **错误处理**: 正确处理 Promise 的拒绝情况

**使用示例**:
```javascript
const race = new Race();

// 多个数据源竞态
const promise1 = fetch('/api/data/source1');
const promise2 = fetch('/api/data/source2');
const promise3 = fetch('/api/data/source3');

// 添加到竞态中
const result1 = race.add(promise1);
const result2 = race.add(promise2);
const result3 = race.add(promise3);

// 所有返回的 Promise 都会解析为最快的结果
Promise.all([result1, result2, result3]).then(([r1, r2, r3]) => {
    console.log(r1 === r2 && r2 === r3); // true，都是同一个结果
});
```

**实际应用场景**:
```javascript
// 1. 多源数据获取
class MultiSourceDataLoader {
    constructor() {
        this.race = new Race();
    }

    async loadFromMultipleSources(id) {
        // 从多个数据源同时请求
        const primarySource = fetch(`/api/primary/data/${id}`);
        const backupSource = fetch(`/api/backup/data/${id}`);
        const cacheSource = this.loadFromCache(id);

        // 添加到竞态中，最快的获胜
        const fastestResult = await this.race.add(primarySource);
        this.race.add(backupSource);
        this.race.add(cacheSource);

        return fastestResult;
    }

    async loadFromCache(id) {
        // 模拟缓存查找
        await delay(10);
        const cached = localStorage.getItem(`data_${id}`);
        if (cached) {
            return { json: () => JSON.parse(cached) };
        }
        throw new Error("Cache miss");
    }
}

// 2. 超时控制
class TimeoutController {
    constructor() {
        this.race = new Race();
    }

    async withTimeout(promise, timeoutMs) {
        const timeoutPromise = delay(timeoutMs).then(() => {
            throw new Error(`操作超时 (${timeoutMs}ms)`);
        });

        // 原始 Promise 和超时 Promise 竞态
        return this.race.add(promise);
        this.race.add(timeoutPromise);
    }
}

// 使用示例
const controller = new TimeoutController();

try {
    const result = await controller.withTimeout(
        fetch('/api/slow-endpoint'),
        5000 // 5秒超时
    );
    console.log("请求成功:", result);
} catch (error) {
    console.error("请求失败或超时:", error.message);
}

// 3. 健康检查
class HealthChecker {
    constructor() {
        this.race = new Race();
    }

    async checkHealth(services) {
        const healthChecks = services.map(service =>
            fetch(`${service.url}/health`)
                .then(response => ({ service: service.name, healthy: response.ok }))
                .catch(() => ({ service: service.name, healthy: false }))
        );

        // 等待第一个健康检查完成
        const firstResult = await this.race.add(healthChecks[0]);
        healthChecks.slice(1).forEach(check => this.race.add(check));

        return firstResult;
    }
}
```

### 5. Deferred - 延迟对象
```javascript
class Deferred extends Promise {
    constructor() {
        let resolve;
        let reject;
        const prom = new Promise((res, rej) => {
            resolve = res;
            reject = rej;
        });
        return Object.assign(prom, { resolve, reject });
    }
}
```

**功能特性**:
- **外部控制**: 可以从外部控制 Promise 的解析和拒绝
- **Promise 兼容**: 完全兼容 Promise API
- **灵活时机**: 可以在任意时机解析或拒绝
- **事件驱动**: 适合事件驱动的异步编程

**使用示例**:
```javascript
// 创建延迟对象
const deferred = new Deferred();

// 可以像普通 Promise 一样使用
deferred.then(result => {
    console.log("结果:", result);
}).catch(error => {
    console.error("错误:", error);
});

// 在某个时机解析
setTimeout(() => {
    deferred.resolve("延迟解析的结果");
}, 2000);

// 或者拒绝
// deferred.reject(new Error("出错了"));
```

**实际应用场景**:
```javascript
// 1. 事件等待器
class EventWaiter {
    constructor(element, eventType) {
        this.deferred = new Deferred();

        const handler = (event) => {
            element.removeEventListener(eventType, handler);
            this.deferred.resolve(event);
        };

        element.addEventListener(eventType, handler);
    }

    promise() {
        return this.deferred;
    }

    cancel() {
        this.deferred.reject(new Error("等待被取消"));
    }
}

// 使用示例
const button = document.getElementById('myButton');
const waiter = new EventWaiter(button, 'click');

waiter.promise().then(event => {
    console.log("按钮被点击了:", event);
});

// 2. 资源加载器
class ResourceLoader {
    constructor() {
        this.loadingPromises = new Map();
    }

    loadResource(url) {
        if (this.loadingPromises.has(url)) {
            return this.loadingPromises.get(url);
        }

        const deferred = new Deferred();
        this.loadingPromises.set(url, deferred);

        // 开始加载
        this.startLoading(url, deferred);

        return deferred;
    }

    startLoading(url, deferred) {
        const img = new Image();

        img.onload = () => {
            this.loadingPromises.delete(url);
            deferred.resolve(img);
        };

        img.onerror = () => {
            this.loadingPromises.delete(url);
            deferred.reject(new Error(`加载失败: ${url}`));
        };

        img.src = url;
    }
}

// 3. 条件等待器
class ConditionalWaiter {
    constructor(condition, checkInterval = 100) {
        this.deferred = new Deferred();
        this.condition = condition;
        this.checkInterval = checkInterval;
        this.intervalId = null;

        this.startChecking();
    }

    startChecking() {
        this.intervalId = setInterval(() => {
            if (this.condition()) {
                this.stop();
                this.deferred.resolve();
            }
        }, this.checkInterval);
    }

    stop() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }

    promise() {
        return this.deferred;
    }

    cancel() {
        this.stop();
        this.deferred.reject(new Error("等待被取消"));
    }
}

// 使用示例
const waiter = new ConditionalWaiter(() => {
    return document.getElementById('target').offsetHeight > 0;
});

waiter.promise().then(() => {
    console.log("元素已可见");
});
```

## 高级应用模式

### 1. 组合使用多种并发控制
```javascript
class AdvancedDataManager {
    constructor() {
        this.mutex = new Mutex();           // 保护写操作
        this.keepLast = new KeepLast();     // 保持最新搜索
        this.race = new Race();             // 多源数据竞态
        this.cache = new Map();
    }

    async search(query) {
        // 使用 KeepLast 确保只处理最新搜索
        const searchPromise = this.performSearch(query);
        return this.keepLast.add(searchPromise);
    }

    async performSearch(query) {
        // 检查缓存
        if (this.cache.has(query)) {
            return this.cache.get(query);
        }

        // 多源搜索竞态
        const primarySearch = fetch(`/api/search/primary?q=${query}`);
        const backupSearch = fetch(`/api/search/backup?q=${query}`);

        const result = await this.race.add(primarySearch);
        this.race.add(backupSearch);

        // 使用互斥锁保护缓存写入
        await this.mutex.exec(async () => {
            this.cache.set(query, result);
        });

        return result;
    }
}

// 2. 请求管理器
class RequestManager {
    constructor() {
        this.pendingRequests = new Map();
        this.mutex = new Mutex();
    }

    async request(url, options = {}) {
        const key = `${url}_${JSON.stringify(options)}`;

        // 如果相同请求正在进行，返回现有 Promise
        if (this.pendingRequests.has(key)) {
            return this.pendingRequests.get(key);
        }

        const deferred = new Deferred();
        this.pendingRequests.set(key, deferred);

        try {
            const response = await fetch(url, options);
            const data = await response.json();

            // 使用互斥锁保护清理操作
            await this.mutex.exec(async () => {
                this.pendingRequests.delete(key);
            });

            deferred.resolve(data);
            return data;
        } catch (error) {
            await this.mutex.exec(async () => {
                this.pendingRequests.delete(key);
            });

            deferred.reject(error);
            throw error;
        }
    }
}
```

### 2. 错误处理和重试机制
```javascript
class RobustAsyncManager {
    constructor() {
        this.mutex = new Mutex();
        this.retryDelays = [1000, 2000, 4000, 8000]; // 指数退避
    }

    async executeWithRetry(operation, maxRetries = 3) {
        return this.mutex.exec(async () => {
            let lastError;

            for (let attempt = 0; attempt <= maxRetries; attempt++) {
                try {
                    return await operation();
                } catch (error) {
                    lastError = error;

                    if (attempt === maxRetries) {
                        throw error;
                    }

                    // 等待重试延迟
                    const delayTime = this.retryDelays[attempt] || 8000;
                    await delay(delayTime);

                    console.log(`重试 ${attempt + 1}/${maxRetries}...`);
                }
            }

            throw lastError;
        });
    }

    async executeWithTimeout(operation, timeoutMs) {
        const race = new Race();

        const operationPromise = operation();
        const timeoutPromise = delay(timeoutMs).then(() => {
            throw new Error(`操作超时 (${timeoutMs}ms)`);
        });

        const result = await race.add(operationPromise);
        race.add(timeoutPromise);

        return result;
    }
}
```

## 最佳实践

### 1. 选择合适的并发控制工具
```javascript
// ✅ 推荐：根据场景选择合适的工具

// 用户输入搜索 - 使用 KeepLast
const searchKeepLast = new KeepLast();
function handleSearch(query) {
    const searchPromise = fetch(`/api/search?q=${query}`);
    return searchKeepLast.add(searchPromise);
}

// 共享资源保护 - 使用 Mutex
const dataMutex = new Mutex();
function updateSharedData(updates) {
    return dataMutex.exec(async () => {
        // 安全地更新共享数据
        await applyUpdates(updates);
    });
}

// 多源数据获取 - 使用 Race
const dataRace = new Race();
function loadFromMultipleSources() {
    const source1 = fetch('/api/source1');
    const source2 = fetch('/api/source2');

    const result = dataRace.add(source1);
    dataRace.add(source2);

    return result;
}

// 事件驱动 - 使用 Deferred
function waitForUserAction() {
    const deferred = new Deferred();

    document.addEventListener('click', () => {
        deferred.resolve('用户点击了');
    }, { once: true });

    return deferred;
}
```

### 2. 错误处理
```javascript
// ✅ 推荐：正确处理异步错误
class SafeAsyncManager {
    constructor() {
        this.mutex = new Mutex();
        this.keepLast = new KeepLast();
    }

    async safeOperation(operation) {
        try {
            return await this.mutex.exec(operation);
        } catch (error) {
            console.error("操作失败:", error);
            // 记录错误，但不重新抛出
            this.logError(error);
            return null;
        }
    }

    async safeSearch(query) {
        try {
            const searchPromise = this.performSearch(query);
            return await this.keepLast.add(searchPromise);
        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error("搜索失败:", error);
            }
            return [];
        }
    }
}
```

### 3. 内存管理
```javascript
// ✅ 推荐：及时清理资源
class ManagedAsyncOperations {
    constructor() {
        this.operations = new Set();
        this.deferreds = new Set();
    }

    addOperation(promise) {
        this.operations.add(promise);

        promise.finally(() => {
            this.operations.delete(promise);
        });

        return promise;
    }

    createDeferred() {
        const deferred = new Deferred();
        this.deferreds.add(deferred);

        deferred.finally(() => {
            this.deferreds.delete(deferred);
        });

        return deferred;
    }

    cleanup() {
        // 取消所有待处理的操作
        this.deferreds.forEach(deferred => {
            deferred.reject(new Error("清理时取消"));
        });

        this.operations.clear();
        this.deferreds.clear();
    }
}
```

### 4. 性能优化
```javascript
// ✅ 推荐：优化并发性能
class OptimizedConcurrencyManager {
    constructor() {
        this.requestCache = new Map();
        this.mutex = new Mutex();
    }

    async cachedRequest(url) {
        // 检查缓存
        if (this.requestCache.has(url)) {
            return this.requestCache.get(url);
        }

        // 创建请求 Promise
        const requestPromise = fetch(url).then(r => r.json());

        // 缓存 Promise（而不是结果）
        this.requestCache.set(url, requestPromise);

        try {
            const result = await requestPromise;
            return result;
        } catch (error) {
            // 失败时清除缓存
            this.requestCache.delete(url);
            throw error;
        }
    }

    async batchOperation(items, batchSize = 10) {
        const results = [];

        for (let i = 0; i < items.length; i += batchSize) {
            const batch = items.slice(i, i + batchSize);

            // 并行处理批次内的项目
            const batchResults = await Promise.all(
                batch.map(item => this.processItem(item))
            );

            results.push(...batchResults);

            // 批次间短暂延迟
            if (i + batchSize < items.length) {
                await delay(10);
            }
        }

        return results;
    }
}
```

## 常见陷阱和注意事项

### 1. 避免死锁
```javascript
// ❌ 错误：可能导致死锁
class BadMutexUsage {
    constructor() {
        this.mutex1 = new Mutex();
        this.mutex2 = new Mutex();
    }

    async operation1() {
        await this.mutex1.exec(async () => {
            await this.mutex2.exec(async () => {
                // 操作
            });
        });
    }

    async operation2() {
        await this.mutex2.exec(async () => {
            await this.mutex1.exec(async () => {
                // 操作 - 可能死锁
            });
        });
    }
}

// ✅ 正确：避免嵌套锁或使用统一顺序
class GoodMutexUsage {
    constructor() {
        this.globalMutex = new Mutex();
    }

    async operation1() {
        await this.globalMutex.exec(async () => {
            // 所有操作使用同一个锁
        });
    }

    async operation2() {
        await this.globalMutex.exec(async () => {
            // 避免死锁
        });
    }
}
```

### 2. 正确使用 KeepLast
```javascript
// ❌ 错误：忘记处理被取消的操作
async function badSearch(query) {
    const result = await keepLast.add(fetch(`/api/search?q=${query}`));
    updateUI(result); // 可能更新已过时的结果
}

// ✅ 正确：检查操作是否仍然有效
async function goodSearch(query) {
    try {
        const result = await keepLast.add(fetch(`/api/search?q=${query}`));
        // 只有最新的搜索会到达这里
        updateUI(result);
    } catch (error) {
        // 被取消的操作不会执行这里
        console.error("搜索失败:", error);
    }
}
```

## 总结

Odoo 并发控制工具模块提供了完整的异步操作管理功能：

**核心优势**:
- **竞态解决**: KeepLast 解决快速连续操作的竞态问题
- **资源保护**: Mutex 确保共享资源的安全访问
- **性能优化**: Race 实现多源数据的快速获取
- **灵活控制**: Deferred 提供外部可控的 Promise
- **简单易用**: delay 提供基础的异步延迟功能

**适用场景**:
- 搜索建议和实时预览
- 数据库操作和文件管理
- 多源数据获取和缓存
- 事件驱动的异步编程
- 超时控制和重试机制

这个工具模块为 Odoo Web 客户端提供了强大的并发控制能力，是构建可靠、高性能异步应用的重要基础。

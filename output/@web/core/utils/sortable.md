# Odoo 排序工具 (Sortable Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/utils/sortable.js`  
**原始路径**: `/web/static/src/core/utils/sortable.js`  
**模块类型**: 核心工具模块 - 拖拽排序工具  
**代码行数**: 353 行  
**依赖关系**: 
- `@web/core/utils/draggable_hook_builder` - 拖拽Hook构建器
- `@web/core/utils/objects` - 对象工具 (pick函数)

## 模块功能

排序工具模块是 Odoo Web 客户端的核心工具库，提供了强大的拖拽排序功能。该模块基于拖拽Hook构建器，实现了一个高级的排序系统，支持：
- 元素拖拽排序
- 跨组排序
- 占位符系统
- 复杂的排序逻辑
- 丰富的事件回调
- 自动DOM更新

这个排序系统是构建现代交互式列表、看板、表格等组件的重要基础。

## 核心类型定义

### SortableParams - 排序参数配置
```typescript
interface SortableParams {
    // 必需参数
    ref: { el: HTMLElement | null };
    elements: string;
    
    // 可选参数
    enable?: boolean | (() => boolean);
    delay?: number;
    touchDelay?: number;
    groups?: string | (() => string);
    handle?: string | (() => string);
    ignore?: string | (() => string);
    connectGroups?: boolean | (() => boolean);
    cursor?: string | (() => string);
    clone?: boolean;
    placeholderClasses?: string[];
    applyChangeOnDrop?: boolean;
    followingElementClasses?: string[];
    
    // 事件处理器
    onDragStart?: (params: SortableHandlerParams) => any;
    onElementEnter?: (params: DraggableHandlerParams) => any;
    onElementLeave?: (params: DraggableHandlerParams) => any;
    onGroupEnter?: (params: SortableHandlerParams) => any;
    onGroupLeave?: (params: SortableHandlerParams) => any;
    onDragEnd?: (params: SortableHandlerParams) => any;
    onDrop?: (params: DropParams) => any;
}
```

### DropParams - 拖放结果参数
```typescript
interface DropParams {
    element: HTMLElement;
    group: HTMLElement | null;
    previous: HTMLElement | null;
    next: HTMLElement | null;
    parent: HTMLElement | null;
}
```

### SortableState - 排序状态
```typescript
interface SortableState {
    dragging: boolean;
}
```

## 核心Hook详解

### useSortable() - 排序Hook
```javascript
const useSortable = (sortableParams) => {
    const { setupHooks } = sortableParams;
    delete sortableParams.setupHooks;
    return nativeMakeDraggableHook({ ...hookParams, setupHooks })(sortableParams);
};
```

**功能特性**:
- **拖拽排序**: 完整的拖拽排序生命周期管理
- **占位符系统**: 智能的占位符定位和显示
- **跨组支持**: 支持元素在不同组之间移动
- **自动更新**: 可选的自动DOM更新功能
- **事件丰富**: 提供完整的排序事件回调

**使用示例**:
```javascript
// 基本排序实现
class SortableList extends Component {
    setup() {
        this.listRef = useRef("sortableList");
        
        this.sortableState = useSortable({
            ref: this.listRef,
            elements: ".sortable-item",
            
            onDragStart: ({ element, group }) => {
                console.log("开始拖拽:", element);
                element.classList.add("dragging");
            },
            
            onDrop: ({ element, previous, next, parent }) => {
                console.log("拖拽完成:", {
                    element: element,
                    insertAfter: previous,
                    insertBefore: next,
                    newParent: parent
                });
                
                // 更新数据模型
                this.updateItemOrder(element, previous, next);
            },
            
            onDragEnd: ({ element }) => {
                element.classList.remove("dragging");
            }
        });
    }
    
    updateItemOrder(element, previous, next) {
        const itemId = element.dataset.itemId;
        const previousId = previous?.dataset.itemId;
        const nextId = next?.dataset.itemId;
        
        // 更新排序逻辑
        this.reorderItems(itemId, previousId, nextId);
    }
}
```

**实际应用场景**:
```javascript
// 1. 看板任务排序
class KanbanBoard extends Component {
    setup() {
        this.boardRef = useRef("kanbanBoard");
        this.tasks = useState([
            { id: 1, title: "任务1", column: "todo", order: 1 },
            { id: 2, title: "任务2", column: "todo", order: 2 },
            { id: 3, title: "任务3", column: "doing", order: 1 }
        ]);
        
        this.sortableState = useSortable({
            ref: this.boardRef,
            elements: ".kanban-task",
            groups: ".kanban-column",
            connectGroups: true, // 允许跨列拖拽
            clone: true,
            placeholderClasses: ["task-placeholder"],
            followingElementClasses: ["task-dragging"],
            
            onDragStart: ({ element, group }) => {
                this.draggedTask = {
                    id: element.dataset.taskId,
                    sourceColumn: group.dataset.columnId
                };
                
                // 高亮可放置的列
                this.highlightDropZones(true);
            },
            
            onGroupEnter: ({ group }) => {
                group.classList.add("drop-target");
            },
            
            onGroupLeave: ({ group }) => {
                group.classList.remove("drop-target");
            },
            
            onDrop: ({ element, group, previous, next }) => {
                const taskId = parseInt(element.dataset.taskId);
                const newColumn = group.dataset.columnId;
                const task = this.tasks.find(t => t.id === taskId);
                
                if (task) {
                    // 更新任务列
                    task.column = newColumn;
                    
                    // 计算新的排序位置
                    const newOrder = this.calculateNewOrder(previous, next, newColumn);
                    task.order = newOrder;
                    
                    // 重新排序任务
                    this.reorderTasks(newColumn);
                    
                    console.log(`任务 ${task.title} 移动到 ${newColumn}`);
                }
            },
            
            onDragEnd: () => {
                this.highlightDropZones(false);
                this.draggedTask = null;
            }
        });
    }
    
    calculateNewOrder(previous, next, column) {
        const columnTasks = this.tasks.filter(t => t.column === column);
        
        if (!previous && !next) {
            // 空列，设为第一个
            return 1;
        } else if (!previous) {
            // 插入到开头
            const nextTask = columnTasks.find(t => t.id == next.dataset.taskId);
            return nextTask ? nextTask.order - 1 : 1;
        } else if (!next) {
            // 插入到末尾
            const prevTask = columnTasks.find(t => t.id == previous.dataset.taskId);
            return prevTask ? prevTask.order + 1 : columnTasks.length + 1;
        } else {
            // 插入到中间
            const prevTask = columnTasks.find(t => t.id == previous.dataset.taskId);
            const nextTask = columnTasks.find(t => t.id == next.dataset.taskId);
            return prevTask && nextTask ? (prevTask.order + nextTask.order) / 2 : 1;
        }
    }
    
    reorderTasks(column) {
        const columnTasks = this.tasks.filter(t => t.column === column)
                                     .sort((a, b) => a.order - b.order);
        
        columnTasks.forEach((task, index) => {
            task.order = index + 1;
        });
    }
    
    highlightDropZones(highlight) {
        const columns = this.boardRef.el.querySelectorAll('.kanban-column');
        columns.forEach(column => {
            column.classList.toggle('can-drop', highlight);
        });
    }
}

// 2. 表格行排序
class SortableTable extends Component {
    setup() {
        this.tableRef = useRef("sortableTable");
        this.rows = useState([
            { id: 1, name: "张三", age: 25, order: 1 },
            { id: 2, name: "李四", age: 30, order: 2 },
            { id: 3, name: "王五", age: 28, order: 3 }
        ]);
        
        this.sortableState = useSortable({
            ref: this.tableRef,
            elements: "tbody tr",
            handle: ".drag-handle", // 只能通过拖拽手柄排序
            clone: false, // 不使用克隆占位符
            placeholderClasses: ["row-placeholder"],
            applyChangeOnDrop: true, // 自动应用DOM变化
            
            onDragStart: ({ element }) => {
                element.style.opacity = "0.5";
                this.draggedRowId = element.dataset.rowId;
            },
            
            onElementEnter: ({ element }) => {
                element.classList.add("drop-target");
            },
            
            onElementLeave: ({ element }) => {
                element.classList.remove("drop-target");
            },
            
            onDrop: ({ element, previous, next }) => {
                const rowId = parseInt(element.dataset.rowId);
                const row = this.rows.find(r => r.id === rowId);
                
                if (row) {
                    // 计算新位置
                    const newOrder = this.calculateRowOrder(previous, next);
                    row.order = newOrder;
                    
                    // 重新排序所有行
                    this.reorderAllRows();
                    
                    // 保存到服务器
                    this.saveRowOrder();
                }
            },
            
            onDragEnd: ({ element }) => {
                element.style.opacity = "";
                this.draggedRowId = null;
                
                // 清除所有高亮
                this.tableRef.el.querySelectorAll('.drop-target')
                    .forEach(el => el.classList.remove('drop-target'));
            }
        });
    }
    
    calculateRowOrder(previous, next) {
        if (!previous && !next) {
            return 1;
        } else if (!previous) {
            const nextRow = this.rows.find(r => r.id == next.dataset.rowId);
            return nextRow ? nextRow.order - 1 : 1;
        } else if (!next) {
            const prevRow = this.rows.find(r => r.id == previous.dataset.rowId);
            return prevRow ? prevRow.order + 1 : this.rows.length + 1;
        } else {
            const prevRow = this.rows.find(r => r.id == previous.dataset.rowId);
            const nextRow = this.rows.find(r => r.id == next.dataset.rowId);
            return prevRow && nextRow ? (prevRow.order + nextRow.order) / 2 : 1;
        }
    }
    
    reorderAllRows() {
        this.rows.sort((a, b) => a.order - b.order);
        this.rows.forEach((row, index) => {
            row.order = index + 1;
        });
    }
    
    async saveRowOrder() {
        const orderData = this.rows.map(row => ({
            id: row.id,
            order: row.order
        }));
        
        try {
            await this.rpc('/web/dataset/resequence', {
                model: 'table.row',
                ids: orderData.map(item => item.id),
                orders: orderData.map(item => item.order)
            });
            
            console.log('行排序已保存');
        } catch (error) {
            console.error('保存排序失败:', error);
        }
    }
}

// 3. 文件列表排序
class FileManager extends Component {
    setup() {
        this.fileListRef = useRef("fileList");
        this.files = useState([
            { id: 1, name: "document.pdf", type: "pdf", order: 1 },
            { id: 2, name: "image.jpg", type: "image", order: 2 },
            { id: 3, name: "spreadsheet.xlsx", type: "excel", order: 3 }
        ]);
        
        this.sortableState = useSortable({
            ref: this.fileListRef,
            elements: ".file-item",
            groups: ".file-folder",
            connectGroups: true,
            clone: true,
            delay: 150, // 延迟150ms开始拖拽
            touchDelay: 300, // 触摸设备延迟300ms
            
            onDragStart: ({ element, group }) => {
                this.draggedFile = {
                    id: element.dataset.fileId,
                    sourceFolder: group?.dataset.folderId || 'root'
                };
                
                element.classList.add("file-dragging");
                this.showDropIndicators(true);
            },
            
            onGroupEnter: ({ group }) => {
                if (this.canDropInFolder(group)) {
                    group.classList.add("folder-drop-target");
                }
            },
            
            onGroupLeave: ({ group }) => {
                group.classList.remove("folder-drop-target");
            },
            
            onDrop: ({ element, group, previous, next }) => {
                const fileId = parseInt(element.dataset.fileId);
                const targetFolder = group?.dataset.folderId || 'root';
                const file = this.files.find(f => f.id === fileId);
                
                if (file && this.canDropInFolder(group)) {
                    // 移动文件到新文件夹
                    file.folderId = targetFolder;
                    
                    // 计算新的排序位置
                    const newOrder = this.calculateFileOrder(previous, next, targetFolder);
                    file.order = newOrder;
                    
                    // 重新排序文件
                    this.reorderFiles(targetFolder);
                    
                    // 更新服务器
                    this.moveFile(fileId, targetFolder, newOrder);
                    
                    console.log(`文件 ${file.name} 移动到文件夹 ${targetFolder}`);
                }
            },
            
            onDragEnd: ({ element }) => {
                element.classList.remove("file-dragging");
                this.showDropIndicators(false);
                this.clearDropTargets();
                this.draggedFile = null;
            }
        });
    }
    
    canDropInFolder(group) {
        if (!group) return true; // 根目录总是可以放置
        
        const folderId = group.dataset.folderId;
        const draggedFileId = this.draggedFile?.id;
        
        // 检查权限、文件类型限制等
        return this.checkDropPermission(folderId, draggedFileId);
    }
    
    calculateFileOrder(previous, next, folderId) {
        const folderFiles = this.files.filter(f => 
            (f.folderId || 'root') === folderId
        );
        
        if (!previous && !next) {
            return 1;
        } else if (!previous) {
            const nextFile = folderFiles.find(f => f.id == next.dataset.fileId);
            return nextFile ? nextFile.order - 1 : 1;
        } else if (!next) {
            const prevFile = folderFiles.find(f => f.id == previous.dataset.fileId);
            return prevFile ? prevFile.order + 1 : folderFiles.length + 1;
        } else {
            const prevFile = folderFiles.find(f => f.id == previous.dataset.fileId);
            const nextFile = folderFiles.find(f => f.id == next.dataset.fileId);
            return prevFile && nextFile ? (prevFile.order + nextFile.order) / 2 : 1;
        }
    }
    
    reorderFiles(folderId) {
        const folderFiles = this.files.filter(f => 
            (f.folderId || 'root') === folderId
        ).sort((a, b) => a.order - b.order);
        
        folderFiles.forEach((file, index) => {
            file.order = index + 1;
        });
    }
    
    showDropIndicators(show) {
        const folders = this.fileListRef.el.querySelectorAll('.file-folder');
        folders.forEach(folder => {
            folder.classList.toggle('can-drop', show);
        });
    }
    
    clearDropTargets() {
        const targets = this.fileListRef.el.querySelectorAll('.folder-drop-target');
        targets.forEach(target => {
            target.classList.remove('folder-drop-target');
        });
    }
    
    async moveFile(fileId, targetFolder, newOrder) {
        try {
            await this.rpc('/web/dataset/call_kw', {
                model: 'file.manager',
                method: 'move_file',
                args: [fileId, targetFolder, newOrder],
                kwargs: {}
            });
        } catch (error) {
            console.error('移动文件失败:', error);
        }
    }
}
```

## 高级应用模式

### 1. 多级嵌套排序系统
```javascript
class NestedSortableTree extends Component {
    setup() {
        this.treeRef = useRef("nestedTree");
        this.treeData = useState([
            {
                id: 1, title: "根节点1", children: [
                    { id: 11, title: "子节点1.1", children: [] },
                    { id: 12, title: "子节点1.2", children: [] }
                ]
            },
            {
                id: 2, title: "根节点2", children: [
                    { id: 21, title: "子节点2.1", children: [] }
                ]
            }
        ]);

        this.sortableState = useSortable({
            ref: this.treeRef,
            elements: ".tree-node",
            groups: ".tree-level",
            connectGroups: true,
            clone: true,
            placeholderClasses: ["tree-placeholder"],

            onDragStart: ({ element, group }) => {
                this.draggedNode = {
                    id: element.dataset.nodeId,
                    level: parseInt(group.dataset.level),
                    sourceParent: group.dataset.parentId
                };

                // 折叠所有展开的节点以简化拖拽
                this.collapseAllNodes();
                element.classList.add("node-dragging");
            },

            onGroupEnter: ({ group }) => {
                const targetLevel = parseInt(group.dataset.level);
                const maxLevel = 5; // 最大嵌套层级

                if (targetLevel <= maxLevel) {
                    group.classList.add("level-drop-target");
                    this.highlightValidDropZones(targetLevel);
                }
            },

            onGroupLeave: ({ group }) => {
                group.classList.remove("level-drop-target");
            },

            onDrop: ({ element, group, previous, next }) => {
                const nodeId = parseInt(element.dataset.nodeId);
                const targetLevel = parseInt(group.dataset.level);
                const targetParent = group.dataset.parentId;

                // 验证拖放是否有效
                if (this.isValidDrop(nodeId, targetParent, targetLevel)) {
                    this.moveNode(nodeId, targetParent, previous, next);
                    this.saveTreeStructure();
                } else {
                    this.showError("无法移动到此位置");
                }
            },

            onDragEnd: ({ element }) => {
                element.classList.remove("node-dragging");
                this.clearAllHighlights();
                this.expandPreviouslyExpandedNodes();
                this.draggedNode = null;
            }
        });
    }

    isValidDrop(nodeId, targetParentId, targetLevel) {
        // 不能移动到自己的子节点中
        if (this.isDescendant(nodeId, targetParentId)) {
            return false;
        }

        // 检查层级限制
        if (targetLevel > 5) {
            return false;
        }

        // 检查权限
        return this.hasPermissionToMove(nodeId, targetParentId);
    }

    isDescendant(ancestorId, descendantId) {
        const findNode = (nodes, id) => {
            for (const node of nodes) {
                if (node.id === id) return node;
                const found = findNode(node.children, id);
                if (found) return found;
            }
            return null;
        };

        const ancestor = findNode(this.treeData, ancestorId);
        if (!ancestor) return false;

        const checkDescendant = (node) => {
            if (node.id === descendantId) return true;
            return node.children.some(child => checkDescendant(child));
        };

        return checkDescendant(ancestor);
    }

    moveNode(nodeId, targetParentId, previous, next) {
        // 从原位置移除节点
        const node = this.removeNodeFromTree(nodeId);
        if (!node) return;

        // 插入到新位置
        this.insertNodeIntoTree(node, targetParentId, previous, next);

        console.log(`节点 ${node.title} 移动完成`);
    }

    removeNodeFromTree(nodeId) {
        const removeFromNodes = (nodes) => {
            for (let i = 0; i < nodes.length; i++) {
                if (nodes[i].id === nodeId) {
                    return nodes.splice(i, 1)[0];
                }
                const found = removeFromNodes(nodes[i].children);
                if (found) return found;
            }
            return null;
        };

        return removeFromNodes(this.treeData);
    }

    insertNodeIntoTree(node, parentId, previous, next) {
        const findParent = (nodes, id) => {
            for (const n of nodes) {
                if (n.id === parseInt(id)) return n;
                const found = findParent(n.children, id);
                if (found) return found;
            }
            return null;
        };

        const parent = parentId ? findParent(this.treeData, parentId) : { children: this.treeData };

        if (parent) {
            let insertIndex = parent.children.length;

            if (previous) {
                const prevIndex = parent.children.findIndex(n => n.id === parseInt(previous.dataset.nodeId));
                insertIndex = prevIndex + 1;
            } else if (next) {
                const nextIndex = parent.children.findIndex(n => n.id === parseInt(next.dataset.nodeId));
                insertIndex = nextIndex;
            }

            parent.children.splice(insertIndex, 0, node);
        }
    }
}
```

### 2. 条件排序系统
```javascript
class ConditionalSortableList extends Component {
    setup() {
        this.listRef = useRef("conditionalList");
        this.items = useState([
            { id: 1, name: "项目1", category: "A", locked: false, order: 1 },
            { id: 2, name: "项目2", category: "B", locked: true, order: 2 },
            { id: 3, name: "项目3", category: "A", locked: false, order: 3 }
        ]);

        this.sortableState = useSortable({
            ref: this.listRef,
            elements: ".list-item:not(.locked)",
            clone: true,
            placeholderClasses: ["item-placeholder"],

            // 动态启用/禁用排序
            enable: () => this.isSortingEnabled(),

            onDragStart: ({ element }) => {
                const itemId = parseInt(element.dataset.itemId);
                const item = this.items.find(i => i.id === itemId);

                // 检查是否可以拖拽
                if (!this.canDragItem(item)) {
                    return false; // 阻止拖拽
                }

                this.draggedItem = item;
                element.classList.add("item-dragging");

                // 高亮可放置的位置
                this.highlightValidDropZones(item);
            },

            onElementEnter: ({ element }) => {
                const targetItemId = parseInt(element.dataset.itemId);
                const targetItem = this.items.find(i => i.id === targetItemId);

                // 检查是否可以在此位置放置
                if (this.canDropAt(this.draggedItem, targetItem)) {
                    element.classList.add("valid-drop-target");
                } else {
                    element.classList.add("invalid-drop-target");
                }
            },

            onElementLeave: ({ element }) => {
                element.classList.remove("valid-drop-target", "invalid-drop-target");
            },

            onDrop: ({ element, previous, next }) => {
                const itemId = parseInt(element.dataset.itemId);
                const item = this.items.find(i => i.id === itemId);

                // 验证拖放规则
                if (this.validateDrop(item, previous, next)) {
                    this.reorderItems(item, previous, next);
                    this.saveOrder();
                } else {
                    this.showError("不能在此位置放置该项目");
                }
            },

            onDragEnd: ({ element }) => {
                element.classList.remove("item-dragging");
                this.clearAllHighlights();
                this.draggedItem = null;
            }
        });
    }

    isSortingEnabled() {
        // 根据用户权限、编辑模式等条件决定是否启用排序
        return this.hasEditPermission && this.isEditMode && !this.isLoading;
    }

    canDragItem(item) {
        // 检查项目是否可以拖拽
        return !item.locked &&
               this.hasPermission('move', item) &&
               item.status !== 'archived';
    }

    canDropAt(draggedItem, targetItem) {
        // 检查是否可以在目标位置放置
        if (!targetItem) return true;

        // 同类别的项目可以互相排序
        if (draggedItem.category === targetItem.category) {
            return true;
        }

        // 特殊权限可以跨类别排序
        return this.hasPermission('cross_category_move');
    }

    validateDrop(item, previous, next) {
        // 验证拖放是否符合业务规则
        const rules = [
            this.validateCategoryRule(item, previous, next),
            this.validatePriorityRule(item, previous, next),
            this.validateDependencyRule(item, previous, next)
        ];

        return rules.every(rule => rule.valid);
    }

    validateCategoryRule(item, previous, next) {
        // 验证类别规则
        const prevItem = previous ? this.items.find(i => i.id == previous.dataset.itemId) : null;
        const nextItem = next ? this.items.find(i => i.id == next.dataset.itemId) : null;

        if (prevItem && prevItem.category !== item.category) {
            return { valid: false, message: "不能跨类别排序" };
        }

        if (nextItem && nextItem.category !== item.category) {
            return { valid: false, message: "不能跨类别排序" };
        }

        return { valid: true };
    }

    validatePriorityRule(item, previous, next) {
        // 验证优先级规则
        if (item.priority === 'high') {
            // 高优先级项目只能在列表前部
            const maxPosition = Math.floor(this.items.length * 0.3);
            const newPosition = this.calculateNewPosition(previous, next);

            if (newPosition > maxPosition) {
                return { valid: false, message: "高优先级项目必须在前面" };
            }
        }

        return { valid: true };
    }

    validateDependencyRule(item, previous, next) {
        // 验证依赖关系规则
        if (item.dependencies && item.dependencies.length > 0) {
            const newPosition = this.calculateNewPosition(previous, next);

            // 检查所有依赖项是否在新位置之前
            for (const depId of item.dependencies) {
                const depItem = this.items.find(i => i.id === depId);
                if (depItem && depItem.order >= newPosition) {
                    return {
                        valid: false,
                        message: `必须在依赖项 ${depItem.name} 之后`
                    };
                }
            }
        }

        return { valid: true };
    }

    highlightValidDropZones(item) {
        this.items.forEach(targetItem => {
            const element = this.listRef.el.querySelector(`[data-item-id="${targetItem.id}"]`);
            if (element && this.canDropAt(item, targetItem)) {
                element.classList.add("potential-drop-zone");
            }
        });
    }

    clearAllHighlights() {
        const elements = this.listRef.el.querySelectorAll('.valid-drop-target, .invalid-drop-target, .potential-drop-zone');
        elements.forEach(el => {
            el.classList.remove('valid-drop-target', 'invalid-drop-target', 'potential-drop-zone');
        });
    }
}
```

### 3. 性能优化排序系统
```javascript
class OptimizedSortableList extends Component {
    setup() {
        this.listRef = useRef("optimizedList");
        this.items = useState([]);
        this.virtualizedItems = useState([]);
        this.sortingEnabled = useState(true);

        // 虚拟化配置
        this.virtualization = {
            itemHeight: 50,
            visibleCount: 20,
            bufferSize: 5
        };

        this.sortableState = useSortable({
            ref: this.listRef,
            elements: ".virtual-item",
            clone: true,

            // 性能优化：延迟启动拖拽
            delay: 100,
            touchDelay: 200,

            onDragStart: ({ element }) => {
                // 暂停虚拟化更新
                this.pauseVirtualization();

                // 预加载相邻项目
                this.preloadAdjacentItems(element);

                element.classList.add("optimized-dragging");
            },

            onDrag: throttle(({ element, clientX, clientY }) => {
                // 节流拖拽事件处理
                this.updateDragPosition(element, clientX, clientY);

                // 检查是否需要滚动
                this.checkAutoScroll(clientY);
            }, 16), // 60fps

            onDrop: ({ element, previous, next }) => {
                // 批量更新DOM
                this.batchUpdateItems(element, previous, next);

                // 恢复虚拟化
                this.resumeVirtualization();
            },

            onDragEnd: ({ element }) => {
                element.classList.remove("optimized-dragging");
                this.resumeVirtualization();
                this.clearPreloadedItems();
            }
        });

        // 设置虚拟化
        this.setupVirtualization();
    }

    setupVirtualization() {
        // 只渲染可见区域的项目
        effect(() => {
            this.updateVirtualizedItems();
        }, [this.items, this.scrollTop]);
    }

    updateVirtualizedItems() {
        const { itemHeight, visibleCount, bufferSize } = this.virtualization;
        const startIndex = Math.max(0, Math.floor(this.scrollTop / itemHeight) - bufferSize);
        const endIndex = Math.min(this.items.length, startIndex + visibleCount + bufferSize * 2);

        this.virtualizedItems = this.items.slice(startIndex, endIndex).map((item, index) => ({
            ...item,
            virtualIndex: startIndex + index,
            top: (startIndex + index) * itemHeight
        }));
    }

    pauseVirtualization() {
        this.virtualizationPaused = true;
    }

    resumeVirtualization() {
        this.virtualizationPaused = false;
        this.updateVirtualizedItems();
    }

    preloadAdjacentItems(element) {
        const itemIndex = parseInt(element.dataset.itemIndex);
        const preloadRange = 10;

        // 预加载前后10个项目的数据
        for (let i = Math.max(0, itemIndex - preloadRange);
             i < Math.min(this.items.length, itemIndex + preloadRange);
             i++) {
            this.ensureItemLoaded(i);
        }
    }

    batchUpdateItems(element, previous, next) {
        // 使用DocumentFragment进行批量DOM操作
        const fragment = document.createDocumentFragment();

        // 批量移动元素
        const elementsToMove = this.getElementsToMove(element, previous, next);
        elementsToMove.forEach(el => fragment.appendChild(el));

        // 一次性插入到正确位置
        const insertPoint = this.getInsertPoint(previous, next);
        insertPoint.appendChild(fragment);

        // 批量更新数据模型
        this.updateDataModel(element, previous, next);
    }

    checkAutoScroll(clientY) {
        const container = this.listRef.el;
        const rect = container.getBoundingClientRect();
        const scrollThreshold = 50;
        const scrollSpeed = 10;

        if (clientY < rect.top + scrollThreshold) {
            // 向上滚动
            container.scrollTop = Math.max(0, container.scrollTop - scrollSpeed);
        } else if (clientY > rect.bottom - scrollThreshold) {
            // 向下滚动
            container.scrollTop = Math.min(
                container.scrollHeight - container.clientHeight,
                container.scrollTop + scrollSpeed
            );
        }
    }

    // 节流函数
    throttle(func, delay) {
        let timeoutId;
        let lastExecTime = 0;

        return function (...args) {
            const currentTime = Date.now();

            if (currentTime - lastExecTime > delay) {
                func.apply(this, args);
                lastExecTime = currentTime;
            } else {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => {
                    func.apply(this, args);
                    lastExecTime = Date.now();
                }, delay - (currentTime - lastExecTime));
            }
        };
    }
}
```

## 最佳实践

### 1. 性能优化
```javascript
// ✅ 推荐：使用节流处理拖拽事件
const optimizedSortable = useSortable({
    ref: listRef,
    elements: ".item",

    onDrag: throttle(({ element, clientX, clientY }) => {
        // 高频事件处理
        updatePosition(element, clientX, clientY);
    }, 16), // 60fps

    onDrop: ({ element, previous, next }) => {
        // 批量DOM更新
        requestAnimationFrame(() => {
            updateItemOrder(element, previous, next);
        });
    }
});

// ✅ 推荐：虚拟化大列表
function useVirtualizedSortable(items, itemHeight) {
    const visibleItems = computed(() => {
        const startIndex = Math.floor(scrollTop.value / itemHeight);
        const endIndex = startIndex + visibleCount;
        return items.slice(startIndex, endIndex);
    });

    return useSortable({
        ref: listRef,
        elements: ".virtual-item",
        // 只对可见项目启用排序
    });
}
```

### 2. 错误处理
```javascript
// ✅ 推荐：安全的排序处理
const safeSortable = useSortable({
    ref: listRef,
    elements: ".item",

    onDrop: ({ element, previous, next }) => {
        try {
            const result = validateAndUpdateOrder(element, previous, next);
            if (!result.success) {
                showError(result.message);
                revertToOriginalPosition(element);
            }
        } catch (error) {
            console.error('排序失败:', error);
            revertToOriginalPosition(element);
        }
    }
});

function validateAndUpdateOrder(element, previous, next) {
    // 验证排序规则
    if (!canMoveElement(element, previous, next)) {
        return { success: false, message: '不能移动到此位置' };
    }

    // 更新排序
    updateOrder(element, previous, next);
    return { success: true };
}
```

### 3. 内存管理
```javascript
// ✅ 推荐：正确清理排序资源
class SortableComponent extends Component {
    setup() {
        this.listRef = useRef("list");
        this.sortableState = useSortable({
            ref: this.listRef,
            elements: ".item",
            // 配置...
        });

        // 组件卸载时清理
        onWillUnmount(() => {
            this.cleanup();
        });
    }

    cleanup() {
        // 清理事件监听器
        if (this.sortableState) {
            this.sortableState.destroy?.();
        }

        // 清理DOM引用
        this.listRef = null;
    }
}
```

## 总结

Odoo 排序工具模块提供了强大而灵活的拖拽排序功能：

**核心优势**:
- **完整的排序系统**: 从简单列表到复杂嵌套结构的完整支持
- **跨组排序**: 支持元素在不同组之间移动
- **智能占位符**: 自动计算和显示占位符位置
- **丰富的事件**: 提供完整的排序生命周期事件
- **高性能**: 支持虚拟化和性能优化

**适用场景**:
- 看板任务排序
- 表格行排序
- 文件列表管理
- 树形结构排序
- 菜单项排序

**设计优势**:
- 基于拖拽Hook构建器
- 灵活的配置选项
- 完整的事件系统
- 性能优化支持

这个排序工具为 Odoo Web 客户端提供了强大的拖拽排序能力，是构建现代交互式用户界面的重要基础。

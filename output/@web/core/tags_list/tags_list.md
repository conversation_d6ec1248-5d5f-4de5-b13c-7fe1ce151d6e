# TagsList - 标签列表组件

## 概述

`tags_list.js` 是 Odoo Web 核心模块的标签列表组件，提供了标签的展示和管理功能。该组件支持标签的可见数量限制、溢出标签的工具提示显示、文本显示控制等功能，为用户提供了清晰简洁的标签展示方式，广泛应用于记录标签、分类标签、状态标签等需要展示多个标签的场景。

## 文件信息
- **路径**: `/web/static/src/core/tags_list/tags_list.js`
- **行数**: 45
- **模块**: `@web/core/tags_list/tags_list`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                          // OWL框架
```

## 主组件类

### 1. 组件属性定义

```javascript
static props = {
    displayText: { type: Boolean, optional: true },     // 是否显示文本
    visibleItemsLimit: { type: Number, optional: true }, // 可见项目限制
    tags: { type: Object },                             // 标签数据
};

static defaultProps = {
    displayText: true,
};
```

**属性功能**:
- **显示控制**: displayText控制是否显示标签文本
- **数量限制**: visibleItemsLimit限制可见标签的数量
- **标签数据**: tags包含所有标签的数据数组

### 2. 计算属性

```javascript
get visibleTagsCount() {
    return this.props.visibleItemsLimit - 1;
}

get visibleTags() {
    if (this.props.visibleItemsLimit && this.props.tags.length > this.props.visibleItemsLimit) {
        return this.props.tags.slice(0, this.visibleTagsCount);
    }
    return this.props.tags;
}

get otherTags() {
    if (!this.props.visibleItemsLimit || this.props.tags.length <= this.props.visibleItemsLimit) {
        return [];
    }
    return this.props.tags.slice(this.visibleTagsCount);
}

get tooltipInfo() {
    return JSON.stringify({
        tags: this.otherTags.map((tag) => ({
            text: tag.text,
            id: tag.id,
        })),
    });
}
```

**计算属性功能**:
- **可见标签**: 根据限制数量计算可见的标签列表
- **隐藏标签**: 计算超出限制的标签列表
- **工具提示**: 为隐藏标签生成工具提示信息
- **智能分割**: 自动处理标签的显示和隐藏逻辑

## 核心功能

### 1. 标签显示管理

```javascript
// 可见标签计算
get visibleTags() {
    if (this.props.visibleItemsLimit && this.props.tags.length > this.props.visibleItemsLimit) {
        return this.props.tags.slice(0, this.visibleTagsCount);
    }
    return this.props.tags;
}
```

**显示管理功能**:
- **数量控制**: 根据visibleItemsLimit控制显示数量
- **自动截取**: 超出限制时自动截取前N个标签
- **完整显示**: 未超出限制时显示所有标签
- **动态计算**: 实时计算可见标签列表

### 2. 溢出处理

```javascript
// 溢出标签处理
get otherTags() {
    if (!this.props.visibleItemsLimit || this.props.tags.length <= this.props.visibleItemsLimit) {
        return [];
    }
    return this.props.tags.slice(this.visibleTagsCount);
}
```

**溢出处理功能**:
- **溢出检测**: 检测是否有标签超出显示限制
- **溢出收集**: 收集超出限制的标签
- **空数组处理**: 无溢出时返回空数组
- **切片操作**: 使用数组切片获取溢出标签

### 3. 工具提示生成

```javascript
// 工具提示信息生成
get tooltipInfo() {
    return JSON.stringify({
        tags: this.otherTags.map((tag) => ({
            text: tag.text,
            id: tag.id,
        })),
    });
}
```

**工具提示功能**:
- **数据序列化**: 将溢出标签序列化为JSON
- **信息提取**: 提取标签的关键信息(text, id)
- **格式化**: 格式化为工具提示可用的数据
- **动态更新**: 根据溢出标签动态更新提示内容

## 使用场景

### 1. 基础标签列表

```javascript
// 基础标签列表使用
class BasicTagsList extends Component {
    setup() {
        this.state = useState({
            tags: [
                { id: 1, text: 'JavaScript', color: '#f39c12' },
                { id: 2, text: 'Python', color: '#3498db' },
                { id: 3, text: 'React', color: '#61dafb' },
                { id: 4, text: 'Vue.js', color: '#4fc08d' },
                { id: 5, text: 'Node.js', color: '#68a063' },
                { id: 6, text: 'Django', color: '#092e20' },
                { id: 7, text: 'Flask', color: '#000000' },
                { id: 8, text: 'Express', color: '#000000' }
            ],
            visibleLimit: 5,
            showText: true
        });
    }

    addTag() {
        const newTag = {
            id: Date.now(),
            text: `Tag ${this.state.tags.length + 1}`,
            color: this.getRandomColor()
        };
        this.state.tags.push(newTag);
    }

    removeTag(tagId) {
        this.state.tags = this.state.tags.filter(tag => tag.id !== tagId);
    }

    getRandomColor() {
        const colors = ['#e74c3c', '#3498db', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c'];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    render() {
        return xml`
            <div class="basic-tags-list">
                <h5>基础标签列表</h5>
                
                <div class="controls mb-3">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">可见标签数量:</label>
                            <input 
                                type="number" 
                                class="form-control"
                                min="1"
                                max="10"
                                t-model="state.visibleLimit"
                            />
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">显示设置:</label>
                            <div class="form-check">
                                <input 
                                    class="form-check-input" 
                                    type="checkbox" 
                                    t-model="state.showText"
                                />
                                <label class="form-check-label">显示文本</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">操作:</label>
                            <div>
                                <button 
                                    class="btn btn-sm btn-primary me-2"
                                    t-on-click="addTag"
                                >
                                    添加标签
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tags-display mb-3">
                    <h6>标签展示 (总计: ${this.state.tags.length})</h6>
                    <TagsList
                        tags="state.tags"
                        visibleItemsLimit="state.visibleLimit"
                        displayText="state.showText"
                    />
                </div>

                <div class="tags-management">
                    <h6>标签管理</h6>
                    <div class="tags-grid">
                        <t t-foreach="state.tags" t-as="tag" t-key="tag.id">
                            <span 
                                class="badge me-2 mb-2"
                                t-att-style="'background-color: ' + tag.color"
                            >
                                <t t-esc="tag.text"/>
                                <button 
                                    class="btn-close btn-close-white ms-2"
                                    t-on-click="() => this.removeTag(tag.id)"
                                    style="font-size: 0.7em;"
                                />
                            </span>
                        </t>
                    </div>
                </div>
            </div>
        `;
    }
}
```

### 2. 产品标签管理

```javascript
// 产品标签管理组件
class ProductTagsManager extends Component {
    setup() {
        this.state = useState({
            products: [
                {
                    id: 1,
                    name: 'iPhone 14',
                    tags: [
                        { id: 1, text: '热销', color: '#e74c3c' },
                        { id: 2, text: '新品', color: '#f39c12' },
                        { id: 3, text: '5G', color: '#3498db' },
                        { id: 4, text: '高端', color: '#9b59b6' }
                    ]
                },
                {
                    id: 2,
                    name: 'MacBook Pro',
                    tags: [
                        { id: 5, text: '专业', color: '#2ecc71' },
                        { id: 6, text: '创作', color: '#1abc9c' },
                        { id: 7, text: 'M2芯片', color: '#34495e' },
                        { id: 8, text: '轻薄', color: '#95a5a6' },
                        { id: 9, text: '长续航', color: '#27ae60' },
                        { id: 10, text: '高性能', color: '#8e44ad' }
                    ]
                },
                {
                    id: 3,
                    name: 'AirPods Pro',
                    tags: [
                        { id: 11, text: '降噪', color: '#e67e22' },
                        { id: 12, text: '无线', color: '#3498db' },
                        { id: 13, text: '便携', color: '#2ecc71' }
                    ]
                }
            ],
            selectedProduct: null,
            tagLimit: 3
        });
    }

    selectProduct(product) {
        this.state.selectedProduct = product;
    }

    addTagToProduct(productId, tagText) {
        const product = this.state.products.find(p => p.id === productId);
        if (product && tagText.trim()) {
            const newTag = {
                id: Date.now(),
                text: tagText.trim(),
                color: this.getRandomColor()
            };
            product.tags.push(newTag);
        }
    }

    removeTagFromProduct(productId, tagId) {
        const product = this.state.products.find(p => p.id === productId);
        if (product) {
            product.tags = product.tags.filter(tag => tag.id !== tagId);
        }
    }

    getRandomColor() {
        const colors = [
            '#e74c3c', '#3498db', '#2ecc71', '#f39c12', 
            '#9b59b6', '#1abc9c', '#e67e22', '#34495e'
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    getTagsStats() {
        const allTags = this.state.products.flatMap(p => p.tags);
        const tagCounts = {};
        
        allTags.forEach(tag => {
            tagCounts[tag.text] = (tagCounts[tag.text] || 0) + 1;
        });

        return Object.entries(tagCounts)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 10);
    }

    render() {
        const tagsStats = this.getTagsStats();

        return xml`
            <div class="product-tags-manager">
                <div class="header mb-4">
                    <h5>产品标签管理</h5>
                    <div class="controls">
                        <label class="form-label">标签显示限制:</label>
                        <input 
                            type="number" 
                            class="form-control d-inline-block w-auto"
                            min="1"
                            max="10"
                            t-model="state.tagLimit"
                        />
                    </div>
                </div>

                <div class="products-grid">
                    <div class="row">
                        <t t-foreach="state.products" t-as="product" t-key="product.id">
                            <div class="col-md-4 mb-3">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h6 class="card-title" t-esc="product.name"/>
                                        
                                        <div class="product-tags mb-3">
                                            <TagsList
                                                tags="product.tags"
                                                visibleItemsLimit="state.tagLimit"
                                                displayText="true"
                                            />
                                        </div>

                                        <div class="tag-stats mb-2">
                                            <small class="text-muted">
                                                总标签数: ${product.tags.length}
                                            </small>
                                        </div>

                                        <button 
                                            class="btn btn-sm btn-outline-primary w-100"
                                            t-on-click="() => this.selectProduct(product)"
                                        >
                                            管理标签
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>
                </div>

                <div class="product-detail" t-if="state.selectedProduct">
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6>管理标签: <t t-esc="state.selectedProduct.name"/></h6>
                        </div>
                        <div class="card-body">
                            <div class="current-tags mb-3">
                                <h6>当前标签</h6>
                                <div class="tags-list">
                                    <t t-foreach="state.selectedProduct.tags" t-as="tag" t-key="tag.id">
                                        <span 
                                            class="badge me-2 mb-2"
                                            t-att-style="'background-color: ' + tag.color"
                                        >
                                            <t t-esc="tag.text"/>
                                            <button 
                                                class="btn-close btn-close-white ms-2"
                                                t-on-click="() => this.removeTagFromProduct(state.selectedProduct.id, tag.id)"
                                                style="font-size: 0.7em;"
                                            />
                                        </span>
                                    </t>
                                </div>
                            </div>

                            <div class="add-tag">
                                <h6>添加标签</h6>
                                <div class="input-group">
                                    <input 
                                        type="text" 
                                        class="form-control"
                                        placeholder="输入标签名称"
                                        t-ref="newTagInput"
                                    />
                                    <button 
                                        class="btn btn-primary"
                                        t-on-click="() => {
                                            const input = this.$refs.newTagInput;
                                            this.addTagToProduct(state.selectedProduct.id, input.value);
                                            input.value = '';
                                        }"
                                    >
                                        添加
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tags-statistics mt-4">
                    <div class="card">
                        <div class="card-header">
                            <h6>标签统计</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <t t-foreach="tagsStats" t-as="stat" t-key="stat[0]">
                                    <div class="col-md-3 mb-2">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span t-esc="stat[0]"/>
                                            <span class="badge bg-secondary" t-esc="stat[1]"/>
                                        </div>
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 智能显示
- 自动计算可见和隐藏标签
- 动态调整显示数量
- 溢出标签的工具提示

### 2. 灵活配置
- 可配置的显示限制
- 可选的文本显示
- 简洁的API接口

### 3. 性能优化
- 计算属性的缓存机制
- 高效的数组切片操作
- 最小化的重新渲染

### 4. 用户体验
- 清晰的视觉层次
- 直观的溢出指示
- 响应式的布局适配

## 设计模式

### 1. 组合模式 (Composite Pattern)
- 标签的组合显示
- 统一的标签接口

### 2. 策略模式 (Strategy Pattern)
- 不同的显示策略
- 可配置的行为模式

### 3. 观察者模式 (Observer Pattern)
- 标签数据的响应式更新
- 属性变化的自动重计算

## 注意事项

1. **性能考虑**: 大量标签时的渲染性能
2. **用户体验**: 提供清晰的溢出指示
3. **响应式**: 确保在不同屏幕尺寸下的正确显示
4. **可访问性**: 支持键盘导航和屏幕阅读器

## 扩展建议

1. **拖拽排序**: 支持标签的拖拽排序
2. **分组显示**: 按类别分组显示标签
3. **搜索过滤**: 标签的搜索和过滤功能
4. **批量操作**: 支持标签的批量选择和操作
5. **主题定制**: 支持标签的主题和样式定制

该标签列表组件为Odoo Web应用提供了灵活的标签展示功能，通过智能的显示控制和简洁的API确保了良好的用户体验和开发者友好性。

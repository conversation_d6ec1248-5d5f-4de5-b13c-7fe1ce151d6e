# Odoo Notifications 模块架构总览与使用指南

## 📁 模块概览

**目录路径**: `output/@web/core/notifications/`  
**模块类型**: 核心基础模块 - 用户通知系统  
**功能范围**: 通知显示、用户反馈、消息管理

## 🏗️ 架构图

```
@web/core/notifications/
├── 📄 notification.js           # 通知组件
├── 📄 notification_container.js # 通知容器组件
└── 📄 notification_service.js   # 通知服务
```

## 🎯 模块功能矩阵

| 模块 | 主要功能 | 核心API | 依赖关系 |
|------|----------|---------|----------|
| **notification.js** | 通知显示 | `Notification` 组件 | owl |
| **notification_container.js** | 容器管理 | `NotificationContainer` 组件 | notification, transition, owl |
| **notification_service.js** | 通知服务 | `add()`, `close()` | browser, registry, container, owl |

## 🔄 数据流图

```mermaid
graph TD
    A[用户操作] --> B[业务组件]
    B --> C[notification.add()]
    C --> D[notificationService]
    D --> E[创建通知对象]
    E --> F[reactive notifications]
    F --> G[NotificationContainer]
    G --> H[Transition 动画]
    H --> I[Notification 组件]
    I --> J[用户界面显示]
    
    K[自动关闭定时器] --> L[close()]
    L --> M[删除通知对象]
    M --> F
    
    N[用户交互] --> O[按钮点击/关闭]
    O --> L
```

## 🚀 快速开始

### 1. 基本通知使用
```javascript
import { useService } from "@web/core/utils/hooks";

class MyComponent extends Component {
    setup() {
        this.notification = useService("notification");
    }
    
    showNotifications() {
        // 成功通知
        this.notification.add("操作成功", {
            type: "success",
            title: "成功"
        });
        
        // 错误通知
        this.notification.add("操作失败", {
            type: "danger",
            title: "错误",
            sticky: true
        });
        
        // 警告通知
        this.notification.add("请确认操作", {
            type: "warning",
            title: "警告",
            buttons: [
                {
                    name: "确认",
                    primary: true,
                    onClick: () => this.confirm()
                }
            ]
        });
    }
}
```

### 2. 高级通知功能
```javascript
class AdvancedNotifications extends Component {
    setup() {
        this.notification = useService("notification");
    }
    
    showProgressNotification() {
        const closeFn = this.notification.add("处理中...", {
            type: "info",
            sticky: true,
            className: "progress-notification"
        });
        
        // 模拟进度更新
        setTimeout(() => {
            closeFn();
            this.notification.add("处理完成", { type: "success" });
        }, 3000);
    }
    
    showUndoNotification() {
        this.notification.add("项目已删除", {
            type: "info",
            buttons: [
                {
                    name: "撤销",
                    icon: "fa-undo",
                    onClick: () => this.undoDelete()
                }
            ]
        });
    }
}
```

## 📚 核心概念详解

### 1. 通知组件 (Notification)
- **纯展示组件**: 专注于通知的显示和交互
- **属性驱动**: 通过 props 完全控制组件行为
- **类型安全**: 完整的属性验证机制
- **交互支持**: 按钮、关闭、冻结等操作

### 2. 通知容器 (NotificationContainer)
- **集中管理**: 统一管理所有通知的显示
- **动画支持**: 流畅的进入和退出动画
- **响应式更新**: 自动响应通知状态变化
- **布局控制**: 统一的定位和样式管理

### 3. 通知服务 (NotificationService)
- **API 接口**: 提供统一的通知创建接口
- **生命周期**: 管理通知的完整生命周期
- **自动化**: 自动关闭、刷新、冻结功能
- **状态管理**: 响应式的通知状态管理

## 🔧 高级用法

### 1. 自定义通知类型
```javascript
class CustomNotificationService {
    constructor(baseService) {
        this.notification = baseService;
    }
    
    showLoading(message) {
        return this.notification.add(message, {
            type: "info",
            sticky: true,
            className: "loading-notification",
            buttons: []
        });
    }
    
    showConfirmation(message, onConfirm) {
        return this.notification.add(message, {
            type: "warning",
            title: "确认",
            sticky: true,
            buttons: [
                {
                    name: "确认",
                    primary: true,
                    onClick: () => {
                        onConfirm();
                        // 通知会自动关闭
                    }
                },
                {
                    name: "取消",
                    onClick: () => {} // 自动关闭
                }
            ]
        });
    }
    
    showError(error, options = {}) {
        return this.notification.add(error.message || "发生未知错误", {
            type: "danger",
            title: "错误",
            sticky: true,
            buttons: [
                {
                    name: "查看详情",
                    onClick: () => this.showErrorDetails(error)
                },
                ...(options.retry ? [{
                    name: "重试",
                    primary: true,
                    onClick: options.retry
                }] : [])
            ]
        });
    }
}
```

### 2. 通知队列管理
```javascript
class NotificationQueue {
    constructor(notificationService) {
        this.notification = notificationService;
        this.queue = [];
        this.maxVisible = 5;
        this.activeNotifications = new Map();
    }
    
    add(message, options = {}) {
        if (this.activeNotifications.size >= this.maxVisible) {
            this.queue.push({ message, options });
            return;
        }
        
        const closeFn = this.notification.add(message, {
            ...options,
            onClose: () => {
                this.activeNotifications.delete(closeFn);
                this.processQueue();
                if (options.onClose) {
                    options.onClose();
                }
            }
        });
        
        this.activeNotifications.set(closeFn, { message, options });
        return closeFn;
    }
    
    processQueue() {
        while (this.queue.length > 0 && this.activeNotifications.size < this.maxVisible) {
            const { message, options } = this.queue.shift();
            this.add(message, options);
        }
    }
    
    clear() {
        // 关闭所有活动通知
        for (const closeFn of this.activeNotifications.keys()) {
            closeFn();
        }
        this.activeNotifications.clear();
        this.queue = [];
    }
}
```

### 3. 通知分组管理
```javascript
class GroupedNotificationManager {
    constructor(notificationService) {
        this.notification = notificationService;
        this.groups = new Map();
    }
    
    addToGroup(groupId, message, options = {}) {
        // 如果组已存在，先关闭旧通知
        if (this.groups.has(groupId)) {
            const oldCloseFn = this.groups.get(groupId);
            oldCloseFn();
        }
        
        const closeFn = this.notification.add(message, {
            ...options,
            onClose: () => {
                this.groups.delete(groupId);
                if (options.onClose) {
                    options.onClose();
                }
            }
        });
        
        this.groups.set(groupId, closeFn);
        return closeFn;
    }
    
    updateGroup(groupId, message, options = {}) {
        return this.addToGroup(groupId, message, options);
    }
    
    closeGroup(groupId) {
        if (this.groups.has(groupId)) {
            const closeFn = this.groups.get(groupId);
            closeFn();
        }
    }
    
    hasGroup(groupId) {
        return this.groups.has(groupId);
    }
}
```

### 4. 响应式通知状态
```javascript
class ReactiveNotificationState {
    constructor(notificationService) {
        this.notification = notificationService;
        this.state = reactive({
            hasErrors: false,
            hasWarnings: false,
            hasInfo: false,
            count: 0
        });
        this.activeNotifications = new Map();
    }
    
    add(message, options = {}) {
        const closeFn = this.notification.add(message, {
            ...options,
            onClose: () => {
                this.removeFromState(closeFn, options.type);
                if (options.onClose) {
                    options.onClose();
                }
            }
        });
        
        this.addToState(closeFn, options.type);
        return closeFn;
    }
    
    addToState(closeFn, type) {
        this.activeNotifications.set(closeFn, type);
        this.updateState();
    }
    
    removeFromState(closeFn, type) {
        this.activeNotifications.delete(closeFn);
        this.updateState();
    }
    
    updateState() {
        const types = Array.from(this.activeNotifications.values());
        this.state.hasErrors = types.includes('danger');
        this.state.hasWarnings = types.includes('warning');
        this.state.hasInfo = types.includes('info');
        this.state.count = types.length;
    }
    
    get hasNotifications() {
        return this.state.count > 0;
    }
    
    get hasImportantNotifications() {
        return this.state.hasErrors || this.state.hasWarnings;
    }
}
```

## 🎨 最佳实践

### 1. 通知类型选择
```javascript
// ✅ 推荐：根据操作结果选择合适类型
const showOperationResult = (success, message) => {
    if (success) {
        this.notification.add(message, { type: "success" });
    } else {
        this.notification.add(message, { 
            type: "danger", 
            sticky: true,
            buttons: [{ name: "重试", onClick: this.retry }]
        });
    }
};

// ❌ 避免：类型与内容不匹配
this.notification.add("操作失败", { type: "success" });
```

### 2. 消息内容设计
```javascript
// ✅ 推荐：清晰、具体的消息
this.notification.add("用户 'John Doe' 已成功创建", {
    type: "success",
    title: "用户创建成功"
});

// ✅ 推荐：提供有用的错误信息
this.notification.add("网络连接失败，请检查网络设置后重试", {
    type: "danger",
    title: "连接错误",
    buttons: [
        { name: "重试", onClick: this.retry },
        { name: "检查网络", onClick: this.checkNetwork }
    ]
});

// ❌ 避免：模糊的消息
this.notification.add("操作完成", { type: "info" });
this.notification.add("错误", { type: "danger" });
```

### 3. 生命周期管理
```javascript
// ✅ 推荐：正确管理通知生命周期
class ComponentWithNotifications extends Component {
    setup() {
        this.notification = useService("notification");
        this.activeNotifications = new Set();
    }
    
    addNotification(message, options = {}) {
        const closeFn = this.notification.add(message, {
            ...options,
            onClose: () => {
                this.activeNotifications.delete(closeFn);
                if (options.onClose) {
                    options.onClose();
                }
            }
        });
        
        this.activeNotifications.add(closeFn);
        return closeFn;
    }
    
    onWillUnmount() {
        // 组件卸载时清理所有通知
        for (const closeFn of this.activeNotifications) {
            closeFn();
        }
    }
}

// ❌ 避免：忘记清理通知
class BadComponent extends Component {
    showNotification() {
        this.notification.add("消息", { sticky: true });
        // 组件卸载后通知仍然显示
    }
}
```

### 4. 用户体验优化
```javascript
// ✅ 推荐：合理的通知时机和内容
class UserExperienceOptimized extends Component {
    async saveData() {
        // 显示加载状态
        const loadingNotification = this.notification.add("正在保存...", {
            type: "info",
            sticky: true
        });
        
        try {
            await this.performSave();
            
            // 关闭加载通知
            loadingNotification();
            
            // 显示成功通知
            this.notification.add("数据保存成功", {
                type: "success"
            });
            
        } catch (error) {
            // 关闭加载通知
            loadingNotification();
            
            // 显示错误通知
            this.notification.add("保存失败：" + error.message, {
                type: "danger",
                sticky: true,
                buttons: [
                    { name: "重试", onClick: () => this.saveData() }
                ]
            });
        }
    }
}
```

## ⚡ 性能优化

### 1. 通知数量限制
```javascript
// 限制同时显示的通知数量
const MAX_NOTIFICATIONS = 5;

class OptimizedNotificationService {
    constructor(baseService) {
        this.notification = baseService;
        this.activeCount = 0;
    }
    
    add(message, options = {}) {
        if (this.activeCount >= MAX_NOTIFICATIONS) {
            // 可以选择排队或替换最旧的通知
            return;
        }
        
        this.activeCount++;
        return this.notification.add(message, {
            ...options,
            onClose: () => {
                this.activeCount--;
                if (options.onClose) {
                    options.onClose();
                }
            }
        });
    }
}
```

### 2. 内存泄漏防护
```javascript
// 防止内存泄漏的通知管理
class MemorySafeNotifications {
    constructor(notificationService) {
        this.notification = notificationService;
        this.timers = new Set();
        this.notifications = new WeakMap();
    }
    
    add(message, options = {}) {
        const closeFn = this.notification.add(message, options);
        
        // 设置最大生存时间
        const maxLifetime = options.maxLifetime || 30000; // 30秒
        const timer = setTimeout(() => {
            closeFn();
            this.timers.delete(timer);
        }, maxLifetime);
        
        this.timers.add(timer);
        this.notifications.set(closeFn, timer);
        
        return closeFn;
    }
    
    cleanup() {
        for (const timer of this.timers) {
            clearTimeout(timer);
        }
        this.timers.clear();
    }
}
```

## 🔍 调试技巧

### 1. 通知状态监控
```javascript
// 开发环境下的通知监控
if (odoo.debug) {
    const originalAdd = notificationService.add;
    notificationService.add = function(message, options = {}) {
        console.group('🔔 Notification Added');
        console.log('Message:', message);
        console.log('Options:', options);
        console.log('Stack:', new Error().stack);
        console.groupEnd();
        
        return originalAdd.call(this, message, {
            ...options,
            onClose: () => {
                console.log('🔕 Notification Closed:', message);
                if (options.onClose) {
                    options.onClose();
                }
            }
        });
    };
}
```

### 2. 性能分析
```javascript
// 通知性能监控
class NotificationPerformanceMonitor {
    constructor() {
        this.metrics = {
            totalNotifications: 0,
            averageLifetime: 0,
            peakConcurrent: 0,
            currentActive: 0
        };
        this.lifetimes = [];
        this.startTimes = new Map();
    }
    
    onNotificationAdd(closeFn) {
        this.metrics.totalNotifications++;
        this.metrics.currentActive++;
        this.metrics.peakConcurrent = Math.max(
            this.metrics.peakConcurrent, 
            this.metrics.currentActive
        );
        this.startTimes.set(closeFn, Date.now());
    }
    
    onNotificationClose(closeFn) {
        this.metrics.currentActive--;
        const startTime = this.startTimes.get(closeFn);
        if (startTime) {
            const lifetime = Date.now() - startTime;
            this.lifetimes.push(lifetime);
            this.metrics.averageLifetime = 
                this.lifetimes.reduce((a, b) => a + b, 0) / this.lifetimes.length;
            this.startTimes.delete(closeFn);
        }
    }
    
    getReport() {
        return {
            ...this.metrics,
            medianLifetime: this.getMedian(this.lifetimes)
        };
    }
    
    getMedian(arr) {
        const sorted = [...arr].sort((a, b) => a - b);
        const mid = Math.floor(sorted.length / 2);
        return sorted.length % 2 === 0 
            ? (sorted[mid - 1] + sorted[mid]) / 2 
            : sorted[mid];
    }
}
```

## 🌍 扩展指南

### 1. 自定义通知组件
```javascript
// 创建自定义通知组件
class RichNotification extends Component {
    static template = xml`
        <div class="rich-notification" t-att-class="props.className">
            <div class="notification-header">
                <i t-if="props.icon" t-att-class="props.icon"/>
                <h4 t-if="props.title" t-esc="props.title"/>
                <button class="close-btn" t-on-click="props.close">×</button>
            </div>
            <div class="notification-body">
                <p t-esc="props.message"/>
                <div t-if="props.details" class="notification-details">
                    <pre t-esc="props.details"/>
                </div>
            </div>
            <div t-if="props.buttons" class="notification-actions">
                <t t-foreach="props.buttons" t-as="button" t-key="button_index">
                    <button 
                        t-att-class="'btn ' + (button.primary ? 'btn-primary' : 'btn-secondary')"
                        t-on-click="button.onClick">
                        <i t-if="button.icon" t-att-class="button.icon"/>
                        <t t-esc="button.name"/>
                    </button>
                </t>
            </div>
        </div>
    `;
    
    static props = {
        ...Notification.props,
        icon: { type: String, optional: true },
        details: { type: String, optional: true }
    };
}
```

### 2. 主题化支持
```javascript
// 主题化通知服务
class ThemedNotificationService {
    constructor(baseService, theme = 'default') {
        this.notification = baseService;
        this.theme = theme;
    }
    
    add(message, options = {}) {
        const themedOptions = {
            ...options,
            className: `${options.className || ''} theme-${this.theme}`.trim()
        };
        
        return this.notification.add(message, themedOptions);
    }
    
    setTheme(theme) {
        this.theme = theme;
    }
}
```

## 📖 相关资源

- [OWL 组件文档](https://github.com/odoo/owl)
- [CSS 动画指南](https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Animations)
- [Web Accessibility Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [用户体验设计原则](https://www.nngroup.com/articles/)

## 🎯 总结

Odoo Notifications 模块是一个完整的用户反馈解决方案，提供了：
- **分层架构**: 组件、容器、服务的清晰分层
- **丰富功能**: 多种通知类型、交互按钮、生命周期管理
- **用户体验**: 流畅动画、智能定位、响应式设计
- **开发友好**: 简洁API、类型安全、易于扩展
- **性能优化**: 内存管理、批量操作、状态优化

这个模块为 Odoo 的用户界面提供了专业、可靠的通知反馈能力，是现代 Web 应用用户体验的重要组成部分。

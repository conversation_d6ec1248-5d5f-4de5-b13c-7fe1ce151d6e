# Odoo 通知服务 (Notification Service) 学习资料

## 文件概述

**文件路径**: `output/@web/core/notifications/notification_service.js`  
**原始路径**: `/web/static/src/core/notifications/notification_service.js`  
**模块类型**: 核心基础模块 - 通知服务  
**代码行数**: 115 行  
**依赖关系**: 
- `@web/core/browser/browser` - 浏览器抽象层
- `@web/core/registry` - 服务注册表
- `@web/core/notifications/notification_container` - 通知容器组件
- `@odoo/owl` - OWL 框架 (reactive)

## 模块功能

通知服务是 Odoo Web 客户端用户反馈系统的核心服务，负责：
- 管理通知的创建、显示和销毁
- 提供统一的通知 API 接口
- 处理通知的自动关闭和生命周期
- 集成通知容器到主组件系统
- 支持粘性通知和临时通知

## 核心常量

### 自动关闭延迟
```javascript
const AUTOCLOSE_DELAY = 4000;
```

**配置说明**:
- **延迟时间**: 4000毫秒（4秒）
- **适用范围**: 非粘性通知的自动关闭时间
- **用户体验**: 给用户足够时间阅读通知内容

## 类型定义

### 1. NotificationButton 类型
```javascript
/**
 * @typedef {Object} NotificationButton
 * @property {string} name - 按钮显示文本
 * @property {string} [icon] - 可选的图标类名
 * @property {boolean} [primary=false] - 是否为主要按钮
 * @property {function(): void} onClick - 点击事件处理函数
 */
```

### 2. NotificationOptions 类型
```javascript
/**
 * @typedef {Object} NotificationOptions
 * @property {string} [title] - 通知标题
 * @property {"warning" | "danger" | "success" | "info"} [type] - 通知类型
 * @property {boolean} [sticky=false] - 是否为粘性通知
 * @property {string} [className] - 自定义CSS类名
 * @property {function(): void} [onClose] - 关闭回调函数
 * @property {NotificationButton[]} [buttons] - 操作按钮数组
 */
```

## 服务架构分析

### 1. 服务定义
```javascript
const notificationService = {
    notificationContainer: NotificationContainer,
    start() {
        // 服务启动逻辑
    }
};
```

**设计特点**:
- **容器引用**: 持有通知容器组件的引用
- **工厂模式**: start 方法返回服务实例
- **状态管理**: 使用 reactive 管理通知状态

### 2. 服务启动流程
```javascript
start() {
    let notifId = 0;
    const notifications = reactive({});
    
    // 注册主组件
    registry.category("main_components").add(
        this.notificationContainer.name,
        {
            Component: this.notificationContainer,
            props: { notifications },
        },
        { sequence: 100 }
    );
    
    // 返回公共API
    return { add };
}
```

**启动步骤**:
1. **初始化状态**: 创建通知ID计数器和响应式通知对象
2. **注册容器**: 将通知容器注册为主组件
3. **返回接口**: 返回公共API方法

### 3. 主组件注册
```javascript
registry.category("main_components").add(
    this.notificationContainer.name,
    {
        Component: this.notificationContainer,
        props: { notifications },
    },
    { sequence: 100 }
);
```

**注册配置**:
- **组件**: NotificationContainer 组件
- **属性**: 传递响应式通知对象
- **序列**: 100（较高优先级，确保在页面顶层显示）

## 核心功能分析

### 1. add() 方法 - 添加通知
```javascript
function add(message, options = {}) {
    const id = ++notifId;
    const closeFn = () => close(id);
    const props = Object.assign({}, options, { message, close: closeFn });
    const sticky = props.sticky;
    delete props.sticky;
    delete props.onClose;
    
    let closeTimeout;
    const refresh = sticky
        ? () => {}
        : () => {
              closeTimeout = browser.setTimeout(closeFn, AUTOCLOSE_DELAY);
          };
    const freeze = sticky
        ? () => {}
        : () => {
              browser.clearTimeout(closeTimeout);
          };
    
    props.refresh = refreshAll;
    props.freeze = freezeAll;
    
    const notification = {
        id,
        props,
        onClose: options.onClose,
        refresh,
        freeze,
    };
    
    notifications[id] = notification;
    
    if (!sticky) {
        closeTimeout = browser.setTimeout(closeFn, AUTOCLOSE_DELAY);
    }
    
    return closeFn;
}
```

**功能流程**:
1. **生成ID**: 递增生成唯一通知ID
2. **创建关闭函数**: 绑定ID的关闭函数
3. **处理属性**: 合并选项和必需属性
4. **生命周期管理**: 根据粘性设置配置刷新和冻结函数
5. **自动关闭**: 非粘性通知设置自动关闭定时器
6. **返回控制**: 返回关闭函数供外部调用

### 2. close() 方法 - 关闭通知
```javascript
function close(id) {
    if (notifications[id]) {
        const notification = notifications[id];
        if (notification.onClose) {
            notification.onClose();
        }
        delete notifications[id];
    }
}
```

**关闭流程**:
1. **存在检查**: 验证通知是否存在
2. **回调执行**: 执行关闭回调函数
3. **状态清理**: 从通知对象中删除

### 3. refreshAll() 方法 - 刷新所有通知
```javascript
function refreshAll() {
    for (const id in notifications) {
        notifications[id].refresh();
    }
}
```

**功能**: 重置所有通知的自动关闭定时器

### 4. freezeAll() 方法 - 冻结所有通知
```javascript
function freezeAll() {
    for (const id in notifications) {
        notifications[id].freeze();
    }
}
```

**功能**: 暂停所有通知的自动关闭定时器

## 生命周期管理

### 1. 通知生命周期
```
创建 → 显示 → [刷新/冻结] → 关闭 → 销毁
```

### 2. 自动关闭机制
```javascript
// 非粘性通知的自动关闭
if (!sticky) {
    closeTimeout = browser.setTimeout(closeFn, AUTOCLOSE_DELAY);
}

// 刷新机制（重置定时器）
const refresh = () => {
    closeTimeout = browser.setTimeout(closeFn, AUTOCLOSE_DELAY);
};

// 冻结机制（暂停定时器）
const freeze = () => {
    browser.clearTimeout(closeTimeout);
};
```

### 3. 粘性通知处理
```javascript
const sticky = props.sticky;
const refresh = sticky ? () => {} : refreshFunction;
const freeze = sticky ? () => {} : freezeFunction;
```

**粘性通知特点**:
- **不自动关闭**: 需要用户手动关闭
- **空操作**: refresh 和 freeze 为空函数
- **持久显示**: 适用于重要信息或错误提示

## 实际使用示例

### 1. 基本通知使用
```javascript
import { useService } from "@web/core/utils/hooks";

class MyComponent extends Component {
    setup() {
        this.notification = useService("notification");
    }
    
    showSuccess() {
        this.notification.add("操作成功完成", {
            type: "success",
            title: "成功"
        });
    }
    
    showError() {
        this.notification.add("操作失败，请重试", {
            type: "danger",
            title: "错误",
            sticky: true // 需要手动关闭
        });
    }
    
    showWarning() {
        this.notification.add("请确认您的操作", {
            type: "warning",
            title: "警告",
            buttons: [
                {
                    name: "确认",
                    primary: true,
                    onClick: () => this.confirm()
                },
                {
                    name: "取消",
                    onClick: () => {} // 自动关闭
                }
            ]
        });
    }
}
```

### 2. 高级通知功能
```javascript
class AdvancedNotificationExample extends Component {
    setup() {
        this.notification = useService("notification");
    }
    
    showProgressNotification() {
        let progress = 0;
        const closeFn = this.notification.add("处理中...", {
            type: "info",
            sticky: true,
            className: "progress-notification"
        });
        
        const interval = setInterval(() => {
            progress += 10;
            if (progress <= 100) {
                // 更新通知内容（需要重新创建）
                closeFn();
                if (progress < 100) {
                    this.notification.add(`处理中... ${progress}%`, {
                        type: "info",
                        sticky: true,
                        className: "progress-notification"
                    });
                } else {
                    this.notification.add("处理完成", {
                        type: "success"
                    });
                }
            } else {
                clearInterval(interval);
            }
        }, 500);
    }
    
    showUndoNotification(action) {
        this.notification.add("操作已执行", {
            type: "info",
            buttons: [
                {
                    name: "撤销",
                    icon: "fa-undo",
                    onClick: () => {
                        action.undo();
                        this.notification.add("操作已撤销", {
                            type: "success"
                        });
                    }
                }
            ]
        });
    }
    
    showCustomNotification() {
        const closeFn = this.notification.add("自定义通知", {
            type: "warning",
            className: "custom-notification",
            onClose: () => {
                console.log("通知已关闭");
                this.handleNotificationClose();
            },
            buttons: [
                {
                    name: "详情",
                    onClick: () => this.showDetails()
                }
            ]
        });
        
        // 5秒后自动关闭
        setTimeout(closeFn, 5000);
    }
}
```

### 3. 批量通知管理
```javascript
class BatchNotificationManager {
    constructor(notificationService) {
        this.notification = notificationService;
        this.activeNotifications = new Set();
    }
    
    addNotification(message, options = {}) {
        const closeFn = this.notification.add(message, {
            ...options,
            onClose: () => {
                this.activeNotifications.delete(closeFn);
                if (options.onClose) {
                    options.onClose();
                }
            }
        });
        
        this.activeNotifications.add(closeFn);
        return closeFn;
    }
    
    closeAll() {
        for (const closeFn of this.activeNotifications) {
            closeFn();
        }
        this.activeNotifications.clear();
    }
    
    showBatchResult(results) {
        const successful = results.filter(r => r.success).length;
        const failed = results.filter(r => !r.success).length;
        
        if (successful > 0) {
            this.addNotification(`成功处理 ${successful} 项`, {
                type: "success"
            });
        }
        
        if (failed > 0) {
            this.addNotification(`失败 ${failed} 项`, {
                type: "danger",
                sticky: true,
                buttons: [
                    {
                        name: "查看详情",
                        onClick: () => this.showFailureDetails(results)
                    }
                ]
            });
        }
    }
}
```

### 4. 通知队列管理
```javascript
class NotificationQueue {
    constructor(notificationService) {
        this.notification = notificationService;
        this.queue = [];
        this.processing = false;
        this.maxConcurrent = 3;
        this.currentCount = 0;
    }
    
    enqueue(message, options = {}) {
        return new Promise((resolve) => {
            this.queue.push({ message, options, resolve });
            this.processQueue();
        });
    }
    
    async processQueue() {
        if (this.processing || this.currentCount >= this.maxConcurrent) {
            return;
        }
        
        this.processing = true;
        
        while (this.queue.length > 0 && this.currentCount < this.maxConcurrent) {
            const { message, options, resolve } = this.queue.shift();
            this.currentCount++;
            
            const closeFn = this.notification.add(message, {
                ...options,
                onClose: () => {
                    this.currentCount--;
                    this.processQueue(); // 处理下一个
                    if (options.onClose) {
                        options.onClose();
                    }
                }
            });
            
            resolve(closeFn);
        }
        
        this.processing = false;
    }
    
    clear() {
        this.queue = [];
    }
}
```

## 设计模式分析

### 1. 服务模式 (Service Pattern)
```javascript
const notificationService = {
    start() {
        return { add };
    }
};
```

**优势**:
- **单例**: 全局唯一的通知管理器
- **依赖注入**: 通过服务系统注入
- **生命周期**: 与应用生命周期绑定

### 2. 工厂模式 (Factory Pattern)
```javascript
function add(message, options = {}) {
    const notification = {
        id,
        props,
        onClose: options.onClose,
        refresh,
        freeze,
    };
    return closeFn;
}
```

**特点**:
- **对象创建**: 统一创建通知对象
- **配置封装**: 隐藏复杂的配置逻辑
- **返回控制**: 返回控制函数

### 3. 观察者模式 (Observer Pattern)
```javascript
const notifications = reactive({});
```

**实现**:
- **响应式状态**: 自动通知视图更新
- **状态同步**: 服务状态与UI状态同步
- **解耦**: 服务与视图解耦

### 4. 命令模式 (Command Pattern)
```javascript
const closeFn = () => close(id);
return closeFn;
```

**应用**:
- **操作封装**: 将关闭操作封装为函数
- **延迟执行**: 支持延迟或条件执行
- **撤销支持**: 可以实现撤销功能

## 性能优化

### 1. 内存管理
```javascript
function close(id) {
    if (notifications[id]) {
        const notification = notifications[id];
        // 清理定时器
        if (notification.closeTimeout) {
            browser.clearTimeout(notification.closeTimeout);
        }
        // 清理回调
        if (notification.onClose) {
            notification.onClose();
        }
        // 删除引用
        delete notifications[id];
    }
}
```

### 2. 定时器管理
```javascript
// 避免定时器泄漏
const freeze = () => {
    browser.clearTimeout(closeTimeout);
};

// 重置定时器
const refresh = () => {
    browser.clearTimeout(closeTimeout);
    closeTimeout = browser.setTimeout(closeFn, AUTOCLOSE_DELAY);
};
```

### 3. 批量操作优化
```javascript
// 批量关闭优化
function closeMultiple(ids) {
    const toClose = ids.filter(id => notifications[id]);
    toClose.forEach(id => {
        const notification = notifications[id];
        if (notification.onClose) {
            notification.onClose();
        }
    });
    
    // 批量删除
    toClose.forEach(id => delete notifications[id]);
}
```

## 最佳实践

### 1. 通知类型选择
```javascript
// ✅ 推荐：根据操作结果选择合适类型
this.notification.add("保存成功", { type: "success" });
this.notification.add("网络错误", { type: "danger", sticky: true });
this.notification.add("请确认操作", { type: "warning" });
this.notification.add("提示信息", { type: "info" });

// ❌ 避免：类型与内容不匹配
this.notification.add("操作失败", { type: "success" });
```

### 2. 粘性通知使用
```javascript
// ✅ 推荐：重要错误使用粘性通知
this.notification.add("网络连接失败", {
    type: "danger",
    sticky: true,
    buttons: [{ name: "重试", onClick: this.retry }]
});

// ❌ 避免：普通信息使用粘性通知
this.notification.add("操作完成", { sticky: true }); // 会一直显示
```

### 3. 回调函数处理
```javascript
// ✅ 推荐：正确处理回调
this.notification.add("确认删除？", {
    type: "warning",
    onClose: () => {
        console.log("用户关闭了确认对话框");
        this.handleDialogClose();
    },
    buttons: [
        {
            name: "确认",
            onClick: () => {
                this.delete();
                // 通知会自动关闭，触发 onClose
            }
        }
    ]
});

// ❌ 避免：忘记处理回调
this.notification.add("重要操作", {
    onClose: undefined // 可能导致状态不一致
});
```

## 总结

通知服务是 Odoo Web 客户端用户反馈系统的核心，它提供了：
- **统一接口**: 简洁的 add() 方法创建各种通知
- **生命周期管理**: 完整的通知创建、显示、关闭流程
- **自动化功能**: 自动关闭、刷新、冻结等智能功能
- **灵活配置**: 支持粘性通知、按钮、回调等丰富选项
- **性能优化**: 响应式状态管理和内存清理机制

这个服务为 Odoo 的用户体验提供了可靠、高效的通知反馈能力，是现代 Web 应用用户界面的重要基础设施。

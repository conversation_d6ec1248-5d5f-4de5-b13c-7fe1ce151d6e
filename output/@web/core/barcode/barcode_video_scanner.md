# BarcodeVideoScanner - 条码视频扫描器

## 概述

`barcode_video_scanner.js` 是 Odoo Web 核心模块的条码视频扫描器组件，提供了基于摄像头的实时条码扫描功能。该组件支持原生BarcodeDetector API和ZXing库的fallback，具备摄像头访问、视频流处理、实时检测、裁剪覆盖层和缩放适配等完整功能，为Odoo Web应用提供了强大的条码扫描核心组件。

## 文件信息
- **路径**: `/web/static/src/core/barcode/barcode_video_scanner.js`
- **行数**: 214
- **模块**: `@web/core/barcode/barcode_video_scanner`

## 依赖关系

```javascript
// 核心依赖
'@web/core/browser/browser'              // 浏览器服务
'@web/core/utils/concurrency'           // 并发工具
'@web/core/assets'                       // 资源加载
'@web/core/barcode/ZXingBarcodeDetector' // ZXing检测器
'@web/core/barcode/crop_overlay'         // 裁剪覆盖层
'@odoo/owl'                              // OWL框架
'@web/core/l10n/translation'             // 翻译服务
'@web/core/utils/objects'                // 对象工具
```

## 核心功能

### 1. 组件属性定义

```javascript
static props = {
    cssClass: { type: String, optional: true },
    facingMode: {
        type: String,
        validate: (fm) => ["environment", "left", "right", "user"].includes(fm),
    },
    close: { type: Function, optional: true },
    onReady: { type: Function, optional: true },
    onResult: Function,
    onError: Function,
    delayBetweenScan: { type: Number, optional: true },
};

static defaultProps = {
    cssClass: "w-100 h-100",
};
```

**属性功能**:
- **摄像头模式**: 支持前置、后置等不同摄像头模式
- **样式定制**: 支持自定义CSS类
- **事件回调**: 完整的事件生命周期回调
- **扫描间隔**: 支持设置扫描间隔时间
- **验证机制**: 对摄像头模式进行验证

### 2. 组件初始化

```javascript
setup() {
    this.videoPreviewRef = useRef("videoPreview");
    this.detectorTimeout = null;
    this.stream = null;
    this.detector = null;
    this.overlayInfo = {};
    this.zoomRatio = 1;
    this.scanPaused = false;
    this.state = useState({
        isReady: false,
    });
}
```

**初始化功能**:
- **引用管理**: 创建视频预览元素引用
- **状态管理**: 管理检测器、流、覆盖层等状态
- **缩放比例**: 计算和管理视频缩放比例
- **扫描控制**: 管理扫描的暂停和恢复

### 3. 检测器初始化

```javascript
onWillStart(async () => {
    let DetectorClass;
    // 优先使用原生BarcodeDetector API
    if ("BarcodeDetector" in window) {
        DetectorClass = BarcodeDetector;
    } else {
        // 降级使用ZXing库
        await loadJS("/web/static/lib/zxing-library/zxing-library.js");
        DetectorClass = buildZXingBarcodeDetector(window.ZXing);
    }
    const formats = await DetectorClass.getSupportedFormats();
    this.detector = new DetectorClass({ formats });
});
```

**检测器初始化功能**:
- **API检测**: 检测浏览器是否支持原生BarcodeDetector
- **降级处理**: 不支持时自动降级到ZXing库
- **格式获取**: 获取支持的条码格式
- **动态加载**: 按需加载ZXing库

### 4. 摄像头初始化

```javascript
onMounted(async () => {
    const constraints = {
        video: { facingMode: this.props.facingMode },
        audio: false,
    };

    try {
        this.stream = await browser.navigator.mediaDevices.getUserMedia(constraints);
    } catch (err) {
        const errors = {
            NotFoundError: _t("No device can be found."),
            NotAllowedError: _t("Odoo needs your authorization first."),
        };
        const errorMessage = _t("Could not start scanning. %(message)s", {
            message: errors[err.name] || err.message,
        });
        this.props.onError(new Error(errorMessage));
        return;
    }
    
    this.videoPreviewRef.el.srcObject = this.stream;
    await this.isVideoReady();
    
    // 计算缩放比例
    const { height, width } = getComputedStyle(this.videoPreviewRef.el);
    const divWidth = width.slice(0, -2);
    const divHeight = height.slice(0, -2);
    const tracks = this.stream.getVideoTracks();
    if (tracks.length) {
        const [track] = tracks;
        const settings = track.getSettings();
        this.zoomRatio = Math.min(divWidth / settings.width, divHeight / settings.height);
    }
    
    this.detectorTimeout = setTimeout(this.detectCode.bind(this), 100);
});
```

**摄像头初始化功能**:
- **权限请求**: 请求摄像头访问权限
- **错误处理**: 处理各种摄像头访问错误
- **流设置**: 将媒体流设置到视频元素
- **缩放计算**: 计算视频显示的缩放比例
- **检测启动**: 启动条码检测循环

### 5. 视频就绪检查

```javascript
async isVideoReady() {
    while (!isVideoElementReady(this.videoPreviewRef.el)) {
        await delay(10);
    }
    this.state.isReady = true;
    if (this.props.onReady) {
        this.props.onReady();
    }
}
```

**就绪检查功能**:
- **状态轮询**: 轮询检查视频元素就绪状态
- **延迟等待**: 使用延迟避免CPU占用过高
- **状态更新**: 更新组件就绪状态
- **回调触发**: 触发就绪回调通知父组件

### 6. 条码检测

```javascript
async detectCode() {
    let barcodeDetected = false;
    let codes = [];
    try {
        codes = await this.detector.detect(this.videoPreviewRef.el);
    } catch (err) {
        this.props.onError(err);
    }
    
    for (const code of codes) {
        // 检查是否在裁剪区域内
        if (
            !this.isZXingBarcodeDetector() &&
            this.overlayInfo.x !== undefined &&
            this.overlayInfo.y !== undefined
        ) {
            const { x, y, width, height } = this.adaptValuesWithRatio(code.boundingBox);
            if (
                x < this.overlayInfo.x ||
                x + width > this.overlayInfo.x + this.overlayInfo.width ||
                y < this.overlayInfo.y ||
                y + height > this.overlayInfo.y + this.overlayInfo.height
            ) {
                continue;
            }
        }
        barcodeDetected = true;
        this.barcodeDetected(code.rawValue);
        break;
    }
    
    if (this.stream && (!barcodeDetected || !this.props.delayBetweenScan)) {
        this.detectorTimeout = setTimeout(this.detectCode.bind(this), 100);
    }
}
```

**检测功能**:
- **异步检测**: 异步调用检测器检测条码
- **区域过滤**: 检查检测结果是否在指定区域内
- **坐标转换**: 适配缩放比例进行坐标转换
- **循环检测**: 持续进行条码检测
- **错误处理**: 处理检测过程中的错误

### 7. 条码检测结果处理

```javascript
barcodeDetected(barcode) {
    if (this.props.delayBetweenScan && !this.scanPaused) {
        this.scanPaused = true;
        this.detectorTimeout = setTimeout(() => {
            this.scanPaused = false;
            this.detectorTimeout = setTimeout(this.detectCode.bind(this), 100);
        }, this.props.delayBetweenScan);
    }
    this.props.onResult(barcode);
}
```

**结果处理功能**:
- **扫描间隔**: 支持设置扫描间隔避免重复检测
- **暂停控制**: 管理扫描的暂停和恢复
- **结果回调**: 调用结果回调函数
- **定时恢复**: 定时恢复扫描检测

### 8. 覆盖层调整

```javascript
onResize(overlayInfo) {
    this.overlayInfo = overlayInfo;
    if (this.isZXingBarcodeDetector()) {
        this.detector.setCropArea(this.adaptValuesWithRatio(this.overlayInfo, true));
    }
}
```

**覆盖层功能**:
- **信息更新**: 更新覆盖层信息
- **裁剪设置**: 为ZXing检测器设置裁剪区域
- **坐标适配**: 适配缩放比例的坐标转换

### 9. 坐标适配

```javascript
adaptValuesWithRatio(domRect, dividerRatio = false) {
    const newObject = pick(domRect, "x", "y", "width", "height");
    for (const key of Object.keys(newObject)) {
        if (dividerRatio) {
            newObject[key] /= this.zoomRatio;
        } else {
            newObject[key] *= this.zoomRatio;
        }
    }
    return newObject;
}
```

**坐标适配功能**:
- **比例转换**: 根据缩放比例转换坐标
- **双向转换**: 支持放大和缩小转换
- **对象复制**: 创建新对象避免修改原始数据

### 10. 资源清理

```javascript
cleanStreamAndTimeout() {
    clearTimeout(this.detectorTimeout);
    this.detectorTimeout = null;
    if (this.stream) {
        this.stream.getTracks().forEach((track) => track.stop());
        this.stream = null;
    }
}
```

**清理功能**:
- **定时器清理**: 清理检测定时器
- **流停止**: 停止媒体流和所有轨道
- **内存释放**: 释放相关资源

## 使用场景

### 1. 基础视频扫描

```javascript
// 基础条码扫描组件
<BarcodeVideoScanner
    facingMode="environment"
    onResult={(barcode) => {
        console.log('扫描到条码:', barcode);
        this.handleBarcodeResult(barcode);
    }}
    onError={(error) => {
        console.error('扫描错误:', error);
        this.handleScanError(error);
    }}
    onReady={() => {
        console.log('扫描器已就绪');
    }}
/>
```

### 2. 带间隔的扫描

```javascript
// 设置扫描间隔避免重复检测
<BarcodeVideoScanner
    facingMode="environment"
    delayBetweenScan={2000} // 2秒间隔
    onResult={(barcode) => {
        this.processBarcodeData(barcode);
    }}
    onError={(error) => {
        this.showErrorMessage(error.message);
    }}
/>
```

### 3. 自定义样式扫描

```javascript
// 自定义样式的扫描器
<BarcodeVideoScanner
    cssClass="custom-scanner-style"
    facingMode="user" // 前置摄像头
    onResult={(barcode) => {
        this.handleFrontCameraScan(barcode);
    }}
    onError={(error) => {
        this.handleCameraError(error);
    }}
/>
```

## 增强示例

```javascript
// 增强的条码视频扫描器
const EnhancedBarcodeVideoScanner = {
    createAdvancedScanner: () => {
        class AdvancedBarcodeVideoScanner extends BarcodeVideoScanner {
            setup() {
                super.setup();
                
                // 增强状态
                this.enhancedState = useState({
                    scanCount: 0,
                    lastScanTime: null,
                    averageScanTime: 0,
                    detectionHistory: [],
                    cameraInfo: null,
                    lightLevel: 0,
                    focusMode: 'auto',
                    torchEnabled: false
                });
                
                // 配置选项
                this.config = {
                    enableMetrics: true,
                    enableLightDetection: true,
                    enableAutoFocus: true,
                    enableTorch: true,
                    maxHistorySize: 50,
                    lightThreshold: 50,
                    autoFocusInterval: 5000
                };
                
                // 性能监控
                this.scanStartTime = null;
                this.lightDetectionInterval = null;
                this.autoFocusInterval = null;
            }
            
            // 增强的组件挂载
            async onMounted() {
                await super.onMounted();
                
                // 获取摄像头信息
                if (this.stream) {
                    await this.getCameraInfo();
                }
                
                // 启动光线检测
                if (this.config.enableLightDetection) {
                    this.startLightDetection();
                }
                
                // 启动自动对焦
                if (this.config.enableAutoFocus) {
                    this.startAutoFocus();
                }
            }
            
            // 获取摄像头信息
            async getCameraInfo() {
                const tracks = this.stream.getVideoTracks();
                if (tracks.length > 0) {
                    const track = tracks[0];
                    const capabilities = track.getCapabilities();
                    const settings = track.getSettings();
                    
                    this.enhancedState.cameraInfo = {
                        label: track.label,
                        capabilities,
                        settings,
                        supportsTorch: 'torch' in capabilities,
                        supportsFocus: 'focusMode' in capabilities,
                        supportsZoom: 'zoom' in capabilities
                    };
                }
            }
            
            // 增强的条码检测
            async detectCode() {
                if (this.config.enableMetrics) {
                    this.scanStartTime = performance.now();
                }
                
                await super.detectCode();
            }
            
            // 增强的条码检测结果处理
            barcodeDetected(barcode) {
                // 记录性能指标
                if (this.config.enableMetrics && this.scanStartTime) {
                    const scanTime = performance.now() - this.scanStartTime;
                    this.recordScanMetrics(barcode, scanTime);
                }
                
                // 添加到检测历史
                this.addToDetectionHistory(barcode);
                
                // 调用父类方法
                super.barcodeDetected(barcode);
            }
            
            // 记录扫描指标
            recordScanMetrics(barcode, scanTime) {
                this.enhancedState.scanCount++;
                this.enhancedState.lastScanTime = Date.now();
                
                // 计算平均扫描时间
                const totalTime = this.enhancedState.averageScanTime * (this.enhancedState.scanCount - 1) + scanTime;
                this.enhancedState.averageScanTime = totalTime / this.enhancedState.scanCount;
            }
            
            // 添加到检测历史
            addToDetectionHistory(barcode) {
                const historyEntry = {
                    barcode,
                    timestamp: Date.now(),
                    lightLevel: this.enhancedState.lightLevel,
                    cameraSettings: this.enhancedState.cameraInfo?.settings
                };
                
                this.enhancedState.detectionHistory.unshift(historyEntry);
                
                // 限制历史大小
                if (this.enhancedState.detectionHistory.length > this.config.maxHistorySize) {
                    this.enhancedState.detectionHistory.pop();
                }
            }
            
            // 启动光线检测
            startLightDetection() {
                this.lightDetectionInterval = setInterval(() => {
                    this.detectLightLevel();
                }, 1000);
            }
            
            // 检测光线水平
            detectLightLevel() {
                if (!this.videoPreviewRef.el) return;
                
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                canvas.width = 100;
                canvas.height = 100;
                
                ctx.drawImage(this.videoPreviewRef.el, 0, 0, 100, 100);
                
                const imageData = ctx.getImageData(0, 0, 100, 100);
                const data = imageData.data;
                
                let totalBrightness = 0;
                for (let i = 0; i < data.length; i += 4) {
                    const r = data[i];
                    const g = data[i + 1];
                    const b = data[i + 2];
                    totalBrightness += (r + g + b) / 3;
                }
                
                this.enhancedState.lightLevel = totalBrightness / (data.length / 4);
                
                // 自动开启手电筒
                if (this.config.enableTorch && 
                    this.enhancedState.lightLevel < this.config.lightThreshold &&
                    !this.enhancedState.torchEnabled) {
                    this.toggleTorch(true);
                }
            }
            
            // 启动自动对焦
            startAutoFocus() {
                this.autoFocusInterval = setInterval(() => {
                    this.performAutoFocus();
                }, this.config.autoFocusInterval);
            }
            
            // 执行自动对焦
            async performAutoFocus() {
                if (!this.stream) return;
                
                const tracks = this.stream.getVideoTracks();
                if (tracks.length > 0) {
                    const track = tracks[0];
                    const capabilities = track.getCapabilities();
                    
                    if (capabilities.focusMode && capabilities.focusMode.includes('continuous')) {
                        try {
                            await track.applyConstraints({
                                advanced: [{ focusMode: 'continuous' }]
                            });
                        } catch (error) {
                            console.warn('Auto focus failed:', error);
                        }
                    }
                }
            }
            
            // 切换手电筒
            async toggleTorch(enable = null) {
                if (!this.stream) return;
                
                const tracks = this.stream.getVideoTracks();
                if (tracks.length > 0) {
                    const track = tracks[0];
                    const capabilities = track.getCapabilities();
                    
                    if (capabilities.torch) {
                        const newState = enable !== null ? enable : !this.enhancedState.torchEnabled;
                        
                        try {
                            await track.applyConstraints({
                                advanced: [{ torch: newState }]
                            });
                            this.enhancedState.torchEnabled = newState;
                        } catch (error) {
                            console.warn('Torch toggle failed:', error);
                        }
                    }
                }
            }
            
            // 设置缩放
            async setZoom(zoomLevel) {
                if (!this.stream) return;
                
                const tracks = this.stream.getVideoTracks();
                if (tracks.length > 0) {
                    const track = tracks[0];
                    const capabilities = track.getCapabilities();
                    
                    if (capabilities.zoom) {
                        const { min, max } = capabilities.zoom;
                        const clampedZoom = Math.max(min, Math.min(max, zoomLevel));
                        
                        try {
                            await track.applyConstraints({
                                advanced: [{ zoom: clampedZoom }]
                            });
                        } catch (error) {
                            console.warn('Zoom setting failed:', error);
                        }
                    }
                }
            }
            
            // 获取扫描统计
            getScanStatistics() {
                return {
                    scanCount: this.enhancedState.scanCount,
                    lastScanTime: this.enhancedState.lastScanTime,
                    averageScanTime: this.enhancedState.averageScanTime,
                    historySize: this.enhancedState.detectionHistory.length,
                    currentLightLevel: this.enhancedState.lightLevel,
                    torchEnabled: this.enhancedState.torchEnabled,
                    cameraInfo: this.enhancedState.cameraInfo
                };
            }
            
            // 获取检测历史
            getDetectionHistory(limit = 10) {
                return this.enhancedState.detectionHistory.slice(0, limit);
            }
            
            // 清理增强功能
            cleanStreamAndTimeout() {
                super.cleanStreamAndTimeout();
                
                if (this.lightDetectionInterval) {
                    clearInterval(this.lightDetectionInterval);
                    this.lightDetectionInterval = null;
                }
                
                if (this.autoFocusInterval) {
                    clearInterval(this.autoFocusInterval);
                    this.autoFocusInterval = null;
                }
            }
            
            // 重置统计
            resetStatistics() {
                this.enhancedState.scanCount = 0;
                this.enhancedState.lastScanTime = null;
                this.enhancedState.averageScanTime = 0;
                this.enhancedState.detectionHistory = [];
            }
        }
        
        return AdvancedBarcodeVideoScanner;
    }
};

// 使用示例
const AdvancedScanner = EnhancedBarcodeVideoScanner.createAdvancedScanner();

<AdvancedScanner
    facingMode="environment"
    delayBetweenScan={1000}
    onResult={(barcode) => {
        console.log('扫描结果:', barcode);
        // 获取统计信息
        const stats = this.scanner.getScanStatistics();
        console.log('扫描统计:', stats);
    }}
    onError={(error) => {
        console.error('扫描错误:', error);
    }}
    onReady={() => {
        console.log('增强扫描器已就绪');
        // 可以设置缩放
        this.scanner.setZoom(2.0);
    }}
    ref={(ref) => { this.scanner = ref; }}
/>
```

## 技术特点

### 1. 双重检测支持
- 原生BarcodeDetector API优先
- ZXing库作为fallback
- 自动检测和切换

### 2. 实时处理
- 持续的视频流检测
- 高效的检测循环
- 可配置的检测间隔

### 3. 坐标适配
- 自动计算缩放比例
- 精确的坐标转换
- 支持裁剪区域

### 4. 资源管理
- 完善的资源清理
- 内存泄漏防护
- 生命周期管理

## 设计模式

### 1. 适配器模式 (Adapter Pattern)
- 适配不同的检测器API
- 统一的检测接口

### 2. 观察者模式 (Observer Pattern)
- 事件驱动的结果处理
- 状态变化通知

### 3. 策略模式 (Strategy Pattern)
- 不同检测器的策略选择
- 可配置的检测行为

## 注意事项

1. **权限管理**: 确保获得摄像头访问权限
2. **性能优化**: 合理设置检测间隔
3. **内存管理**: 及时清理视频流和定时器
4. **错误处理**: 处理各种摄像头和检测错误

## 扩展建议

1. **多摄像头**: 支持多摄像头切换
2. **图像增强**: 添加图像增强功能
3. **批量检测**: 支持同时检测多个条码
4. **性能监控**: 监控检测性能和准确率
5. **离线检测**: 支持离线图像检测

该条码视频扫描器为Odoo Web应用提供了强大的实时条码扫描能力，通过双重检测支持和完善的资源管理确保了在各种环境下的可靠使用。

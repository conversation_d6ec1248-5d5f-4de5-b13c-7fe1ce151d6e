# BarcodeDialog - 条码扫描对话框

## 概述

`barcode_dialog.js` 是 Odoo Web 核心模块的条码扫描对话框组件，提供了一个用户友好的条码扫描界面。该组件封装了条码视频扫描器，提供模态对话框形式的扫描体验，支持摄像头权限检查、错误处理和结果回调，为Odoo Web应用提供了便捷的条码扫描入口。

## 文件信息
- **路径**: `/web/static/src/core/barcode/barcode_dialog.js`
- **行数**: 66
- **模块**: `@web/core/barcode/barcode_dialog`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'           // 翻译服务
'@web/core/dialog/dialog'              // 对话框组件
'@odoo/owl'                            // OWL框架
'@web/core/barcode/barcode_video_scanner' // 条码视频扫描器
```

## 核心功能

### 1. BarcodeDialog组件

```javascript
const BarcodeDialog = class BarcodeDialog extends Component {
    static template = "web.BarcodeDialog";
    static components = {
        BarcodeVideoScanner,
        Dialog,
    };
    static props = ["facingMode", "close", "onResult", "onError"];
}
```

**组件定义功能**:
- **模板驱动**: 使用专门的条码对话框模板
- **组件组合**: 组合对话框和视频扫描器组件
- **属性配置**: 支持摄像头模式、回调函数等配置
- **事件处理**: 处理扫描结果和错误事件

### 2. 组件初始化

```javascript
setup() {
    this.state = useState({
        barcodeScannerSupported: isBarcodeScannerSupported(),
        errorMessage: _t("Check your browser permissions"),
    });
}
```

**初始化功能**:
- **支持检测**: 检测浏览器是否支持条码扫描
- **状态管理**: 管理扫描器支持状态和错误信息
- **国际化**: 提供多语言的错误提示信息
- **响应式状态**: 使用useState实现响应式状态管理

### 3. 扫描结果处理

```javascript
onResult(result) {
    this.props.close();
    this.props.onResult(result);
}
```

**结果处理功能**:
- **对话框关闭**: 扫描成功后自动关闭对话框
- **结果回调**: 调用父组件提供的结果处理回调
- **简洁流程**: 提供简洁的成功处理流程

### 4. 错误处理

```javascript
onError(error) {
    this.state.barcodeScannerSupported = false;
    this.state.errorMessage = error.message;
}
```

**错误处理功能**:
- **状态更新**: 更新扫描器支持状态
- **错误信息**: 显示具体的错误信息
- **用户反馈**: 为用户提供清晰的错误反馈

### 5. 扫描函数

```javascript
async function scanBarcode(env, facingMode = "environment") {
    let res;
    let rej;
    const promise = new Promise((resolve, reject) => {
        res = resolve;
        rej = reject;
    });
    env.services.dialog.add(BarcodeDialog, {
        facingMode,
        onResult: (result) => res(result),
        onError: (error) => rej(error),
    });
    return promise;
}
```

**扫描函数功能**:
- **Promise封装**: 将对话框操作封装为Promise
- **服务集成**: 使用对话框服务显示扫描对话框
- **摄像头配置**: 支持配置前置或后置摄像头
- **异步处理**: 提供异步的扫描接口

## 使用场景

### 1. 基础条码扫描

```javascript
// 扫描条码
try {
    const result = await scanBarcode(env);
    console.log('扫描结果:', result);
    // 处理扫描结果
    this.handleBarcodeResult(result);
} catch (error) {
    console.error('扫描失败:', error);
    // 处理扫描错误
    this.handleScanError(error);
}
```

### 2. 指定摄像头模式

```javascript
// 使用前置摄像头扫描
const result = await scanBarcode(env, "user");

// 使用后置摄像头扫描（默认）
const result = await scanBarcode(env, "environment");
```

### 3. 在组件中使用

```javascript
class MyComponent extends Component {
    async startScan() {
        try {
            const barcode = await scanBarcode(this.env);
            this.processBarcodeData(barcode);
        } catch (error) {
            this.env.services.notification.add(
                "扫描失败: " + error.message,
                { type: "danger" }
            );
        }
    }
    
    processBarcodeData(barcode) {
        // 处理条码数据
        console.log('处理条码:', barcode);
    }
}
```

## 增强示例

```javascript
// 增强的条码扫描对话框
const EnhancedBarcodeDialog = {
    createAdvancedDialog: () => {
        class AdvancedBarcodeDialog extends BarcodeDialog {
            setup() {
                super.setup();
                
                // 增强状态
                this.enhancedState = useState({
                    scanHistory: [],
                    scanCount: 0,
                    lastScanTime: null,
                    isScanning: false,
                    scanDuration: 0,
                    validationRules: [],
                    autoClose: true,
                    showPreview: true
                });
                
                // 配置选项
                this.config = {
                    enableHistory: true,
                    enableValidation: true,
                    enableMetrics: true,
                    maxHistorySize: 10,
                    autoCloseDelay: 1000,
                    enableSound: true,
                    enableVibration: true
                };
                
                // 扫描开始时间
                this.scanStartTime = null;
            }
            
            // 增强的结果处理
            onResult(result) {
                this.enhancedState.isScanning = false;
                this.enhancedState.scanCount++;
                this.enhancedState.lastScanTime = Date.now();
                
                // 计算扫描时长
                if (this.scanStartTime) {
                    this.enhancedState.scanDuration = Date.now() - this.scanStartTime;
                }
                
                // 验证结果
                if (this.config.enableValidation) {
                    const validationResult = this.validateResult(result);
                    if (!validationResult.isValid) {
                        this.showValidationError(validationResult.error);
                        return;
                    }
                }
                
                // 添加到历史记录
                if (this.config.enableHistory) {
                    this.addToHistory(result);
                }
                
                // 播放成功音效
                if (this.config.enableSound) {
                    this.playSuccessSound();
                }
                
                // 震动反馈
                if (this.config.enableVibration && navigator.vibrate) {
                    navigator.vibrate(200);
                }
                
                // 延迟关闭或立即关闭
                if (this.config.autoClose && this.enhancedState.autoClose) {
                    setTimeout(() => {
                        super.onResult(result);
                    }, this.config.autoCloseDelay);
                } else {
                    super.onResult(result);
                }
            }
            
            // 增强的错误处理
            onError(error) {
                this.enhancedState.isScanning = false;
                
                // 记录错误
                console.error('Barcode scan error:', error);
                
                // 根据错误类型提供不同的处理
                let errorMessage = error.message;
                
                switch (error.name) {
                    case 'NotAllowedError':
                        errorMessage = _t("Camera access denied. Please allow camera access and try again.");
                        break;
                    case 'NotFoundError':
                        errorMessage = _t("No camera found. Please check your device.");
                        break;
                    case 'NotSupportedError':
                        errorMessage = _t("Barcode scanning is not supported in this browser.");
                        break;
                    case 'NotReadableError':
                        errorMessage = _t("Camera is already in use by another application.");
                        break;
                    default:
                        errorMessage = _t("An error occurred while scanning: ") + error.message;
                }
                
                this.state.errorMessage = errorMessage;
                super.onError(error);
            }
            
            // 验证扫描结果
            validateResult(result) {
                for (const rule of this.enhancedState.validationRules) {
                    const validation = this.applyValidationRule(result, rule);
                    if (!validation.isValid) {
                        return validation;
                    }
                }
                
                return { isValid: true };
            }
            
            // 应用验证规则
            applyValidationRule(result, rule) {
                switch (rule.type) {
                    case 'length':
                        if (result.length < rule.min || result.length > rule.max) {
                            return {
                                isValid: false,
                                error: _t("Barcode length must be between %s and %s characters", rule.min, rule.max)
                            };
                        }
                        break;
                        
                    case 'pattern':
                        if (!rule.pattern.test(result)) {
                            return {
                                isValid: false,
                                error: _t("Barcode format is invalid")
                            };
                        }
                        break;
                        
                    case 'prefix':
                        if (!result.startsWith(rule.prefix)) {
                            return {
                                isValid: false,
                                error: _t("Barcode must start with %s", rule.prefix)
                            };
                        }
                        break;
                        
                    case 'custom':
                        const customResult = rule.validator(result);
                        if (!customResult.isValid) {
                            return customResult;
                        }
                        break;
                }
                
                return { isValid: true };
            }
            
            // 显示验证错误
            showValidationError(error) {
                this.env.services.notification.add(error, {
                    type: "warning",
                    title: _t("Invalid Barcode")
                });
            }
            
            // 添加到历史记录
            addToHistory(result) {
                const historyEntry = {
                    value: result,
                    timestamp: Date.now(),
                    scanDuration: this.enhancedState.scanDuration
                };
                
                this.enhancedState.scanHistory.unshift(historyEntry);
                
                // 限制历史记录大小
                if (this.enhancedState.scanHistory.length > this.config.maxHistorySize) {
                    this.enhancedState.scanHistory.pop();
                }
            }
            
            // 播放成功音效
            playSuccessSound() {
                try {
                    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    const oscillator = audioContext.createOscillator();
                    const gainNode = audioContext.createGain();
                    
                    oscillator.connect(gainNode);
                    gainNode.connect(audioContext.destination);
                    
                    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                    oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);
                    
                    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
                    
                    oscillator.start(audioContext.currentTime);
                    oscillator.stop(audioContext.currentTime + 0.2);
                } catch (error) {
                    console.warn('Could not play success sound:', error);
                }
            }
            
            // 开始扫描
            startScan() {
                this.enhancedState.isScanning = true;
                this.scanStartTime = Date.now();
            }
            
            // 停止扫描
            stopScan() {
                this.enhancedState.isScanning = false;
                this.scanStartTime = null;
            }
            
            // 清除历史记录
            clearHistory() {
                this.enhancedState.scanHistory = [];
            }
            
            // 获取扫描统计
            getScanStatistics() {
                const history = this.enhancedState.scanHistory;
                const totalScans = this.enhancedState.scanCount;
                
                let averageDuration = 0;
                if (history.length > 0) {
                    const totalDuration = history.reduce((sum, entry) => sum + entry.scanDuration, 0);
                    averageDuration = totalDuration / history.length;
                }
                
                return {
                    totalScans,
                    successfulScans: history.length,
                    averageScanDuration: averageDuration,
                    lastScanTime: this.enhancedState.lastScanTime,
                    historySize: history.length
                };
            }
            
            // 设置验证规则
            setValidationRules(rules) {
                this.enhancedState.validationRules = rules;
            }
            
            // 切换自动关闭
            toggleAutoClose() {
                this.enhancedState.autoClose = !this.enhancedState.autoClose;
            }
        }
        
        return AdvancedBarcodeDialog;
    },
    
    // 增强的扫描函数
    createAdvancedScanFunction: () => {
        return async function scanBarcodeAdvanced(env, options = {}) {
            const {
                facingMode = "environment",
                validationRules = [],
                enableHistory = true,
                enableSound = true,
                enableVibration = true,
                autoClose = true,
                timeout = 30000 // 30秒超时
            } = options;
            
            let res, rej;
            let timeoutId;
            
            const promise = new Promise((resolve, reject) => {
                res = resolve;
                rej = reject;
                
                // 设置超时
                if (timeout > 0) {
                    timeoutId = setTimeout(() => {
                        reject(new Error(_t("Scan timeout after %s seconds", timeout / 1000)));
                    }, timeout);
                }
            });
            
            const AdvancedDialog = EnhancedBarcodeDialog.createAdvancedDialog();
            
            env.services.dialog.add(AdvancedDialog, {
                facingMode,
                onResult: (result) => {
                    if (timeoutId) clearTimeout(timeoutId);
                    res(result);
                },
                onError: (error) => {
                    if (timeoutId) clearTimeout(timeoutId);
                    rej(error);
                },
                validationRules,
                enableHistory,
                enableSound,
                enableVibration,
                autoClose
            });
            
            return promise;
        };
    }
};

// 使用示例
const advancedScanBarcode = EnhancedBarcodeDialog.createAdvancedScanFunction();

// 带验证规则的扫描
try {
    const result = await advancedScanBarcode(env, {
        facingMode: "environment",
        validationRules: [
            {
                type: 'length',
                min: 8,
                max: 20
            },
            {
                type: 'pattern',
                pattern: /^[0-9]+$/
            },
            {
                type: 'prefix',
                prefix: '123'
            }
        ],
        enableHistory: true,
        enableSound: true,
        timeout: 60000 // 60秒超时
    });
    
    console.log('扫描成功:', result);
} catch (error) {
    console.error('扫描失败:', error);
}
```

## 技术特点

### 1. 组件组合
- 对话框和扫描器的完美结合
- 模块化的组件设计
- 可重用的组件架构

### 2. 异步处理
- Promise-based的API设计
- 优雅的异步错误处理
- 非阻塞的用户体验

### 3. 用户体验
- 友好的错误提示
- 自动权限检查
- 国际化支持

### 4. 服务集成
- 与对话框服务的无缝集成
- 标准化的服务调用方式
- 统一的UI管理

## 设计模式

### 1. 组合模式 (Composite Pattern)
- 组合多个组件形成完整功能
- 统一的组件接口

### 2. 观察者模式 (Observer Pattern)
- 事件驱动的结果处理
- 回调函数机制

### 3. 门面模式 (Facade Pattern)
- scanBarcode函数提供简化的接口
- 隐藏复杂的对话框管理逻辑

## 注意事项

1. **权限管理**: 确保用户授予摄像头权限
2. **浏览器兼容**: 检查浏览器对条码扫描的支持
3. **错误处理**: 提供清晰的错误信息和处理方案
4. **用户体验**: 保持界面响应和操作流畅

## 扩展建议

1. **多格式支持**: 支持指定扫描的条码格式
2. **扫描历史**: 记录和管理扫描历史
3. **批量扫描**: 支持连续扫描多个条码
4. **自定义验证**: 支持自定义的条码验证规则
5. **离线支持**: 支持离线条码扫描功能

该条码扫描对话框为Odoo Web应用提供了便捷的条码扫描入口，通过模态对话框的形式提供了用户友好的扫描体验，是条码功能的重要用户界面组件。

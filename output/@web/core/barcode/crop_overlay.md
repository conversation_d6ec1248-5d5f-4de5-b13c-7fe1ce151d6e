# CropOverlay - 裁剪覆盖层组件

## 概述

`crop_overlay.js` 是 Odoo Web 核心模块的裁剪覆盖层组件，专门用于条码扫描时的区域选择和裁剪功能。该组件提供了可拖拽的裁剪框，支持触摸和鼠标交互、位置持久化、边界限制和实时回调，为条码扫描器提供了精确的扫描区域控制功能，提高了扫描的准确性和用户体验。

## 文件信息
- **路径**: `/web/static/src/core/barcode/crop_overlay.js`
- **行数**: 159
- **模块**: `@web/core/barcode/crop_overlay`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                    // OWL框架
'@web/core/browser/browser'    // 浏览器服务
'@web/core/utils/numbers'      // 数字工具
```

## 核心功能

### 1. 组件属性定义

```javascript
static props = {
    onResize: Function,
    isReady: Boolean,
    slots: {
        type: Object,
        shape: {
            default: {},
        },
    },
};
```

**属性功能**:
- **调整回调**: onResize回调函数，当裁剪区域改变时触发
- **就绪状态**: isReady标识组件是否准备就绪
- **插槽支持**: 支持插槽内容的自定义

### 2. 组件初始化

```javascript
setup() {
    this.localStorageKey = "o-barcode-scanner-overlay";
    this.cropContainerRef = useRef("crop-container");
    this.isMoving = false;
    this.boundaryOverlay = {};
    this.relativePosition = {
        x: 0,
        y: 0,
    };
    onPatched(() => {
        this.setupCropRect();
    });
}
```

**初始化功能**:
- **本地存储**: 设置本地存储键用于持久化位置
- **引用管理**: 创建裁剪容器的引用
- **状态管理**: 管理拖拽状态和位置信息
- **生命周期**: 在组件更新后重新设置裁剪矩形

### 3. 裁剪矩形设置

```javascript
setupCropRect() {
    if (!this.props.isReady) {
        return;
    }
    this.computeDefaultPoint();
    this.computeOverlayPosition();
    this.calculateAndSetTransparentRect();
    this.executeOnResizeCallback();
}
```

**设置功能**:
- **就绪检查**: 检查组件是否准备就绪
- **默认位置**: 计算默认的裁剪位置
- **覆盖层位置**: 计算覆盖层的边界位置
- **透明区域**: 计算和设置透明裁剪区域
- **回调执行**: 执行调整回调通知父组件

### 4. 默认位置计算

```javascript
computeDefaultPoint() {
    const firstChildComputedStyle = getComputedStyle(this.cropContainerRef.el.firstChild);
    const elementWidth = firstChildComputedStyle.width.slice(0, -2);
    const elementHeight = firstChildComputedStyle.height.slice(0, -2);

    const stringSavedPoint = browser.localStorage.getItem(this.localStorageKey);
    if (stringSavedPoint) {
        const savedPoint = JSON.parse(stringSavedPoint);
        this.relativePosition = {
            x: clamp(savedPoint.x, 0, elementWidth),
            y: clamp(savedPoint.y, 0, elementHeight),
        };
    } else {
        const stepWidth = elementWidth / 10;
        const width = stepWidth * 8;
        const height = width / 4;
        const startY = elementHeight / 2 - height / 2;
        this.relativePosition = {
            x: stepWidth + width,
            y: startY + height,
        };
    }
}
```

**位置计算功能**:
- **尺寸获取**: 获取容器元素的实际尺寸
- **持久化恢复**: 从本地存储恢复上次的位置
- **边界限制**: 使用clamp函数限制位置在有效范围内
- **默认计算**: 计算合理的默认裁剪位置（80%宽度，4:1比例）

### 5. 边界限制

```javascript
boundPoint(pointValue, boundaryRect) {
    return {
        x: clamp(pointValue.x, boundaryRect.left, boundaryRect.left + boundaryRect.width),
        y: clamp(pointValue.y, boundaryRect.top, boundaryRect.top + boundaryRect.height),
    };
}
```

**边界限制功能**:
- **X轴限制**: 限制X坐标在边界矩形内
- **Y轴限制**: 限制Y坐标在边界矩形内
- **安全边界**: 确保裁剪位置不超出容器范围

### 6. 透明区域计算

```javascript
getTransparentRec(point, rect) {
    const middleX = rect.width / 2;
    const middleY = rect.height / 2;
    const newDeltaX = Math.abs(point.x - middleX);
    const newDeltaY = Math.abs(point.y - middleY);
    return {
        x: middleX - newDeltaX,
        y: middleY - newDeltaY,
    };
}
```

**透明区域功能**:
- **中心计算**: 计算容器的中心点
- **距离计算**: 计算点到中心的距离
- **对称计算**: 计算对称的透明区域位置
- **矩形生成**: 生成透明裁剪矩形的坐标

### 7. CSS变量设置

```javascript
setCropValue(point, iconPoint) {
    if (!iconPoint) {
        iconPoint = point;
    }
    this.cropContainerRef.el.style.setProperty("--o-crop-x", `${point.x}px`);
    this.cropContainerRef.el.style.setProperty("--o-crop-y", `${point.y}px`);
    this.cropContainerRef.el.style.setProperty("--o-crop-icon-x", `${iconPoint.x}px`);
    this.cropContainerRef.el.style.setProperty("--o-crop-icon-y", `${iconPoint.y}px`);
}
```

**CSS变量功能**:
- **裁剪位置**: 设置裁剪区域的X、Y坐标
- **图标位置**: 设置拖拽图标的位置
- **CSS集成**: 通过CSS变量与样式系统集成
- **实时更新**: 实时更新视觉效果

### 8. 指针事件处理

```javascript
pointerDown(event) {
    event.preventDefault();
    if (event.target.matches(".o_crop_icon")) {
        this.computeOverlayPosition();
        this.isMoving = true;
    }
}

pointerMove(event) {
    if (!this.isMoving) {
        return;
    }
    let eventPosition;
    if (event.touches && event.touches.length) {
        eventPosition = event.touches[0];
    } else {
        eventPosition = event;
    }
    const { clientX, clientY } = eventPosition;
    const restrictedPosition = this.boundPoint(
        {
            x: clientX,
            y: clientY,
        },
        this.boundaryOverlay
    );
    this.relativePosition = {
        x: restrictedPosition.x - this.boundaryOverlay.left,
        y: restrictedPosition.y - this.boundaryOverlay.top,
    };
    this.calculateAndSetTransparentRect(this.relativePosition);
}

pointerUp(event) {
    this.isMoving = false;
    this.executeOnResizeCallback();
}
```

**事件处理功能**:
- **拖拽开始**: 检测拖拽图标点击并开始拖拽
- **触摸支持**: 同时支持鼠标和触摸事件
- **位置限制**: 限制拖拽位置在边界内
- **实时更新**: 拖拽过程中实时更新裁剪区域
- **拖拽结束**: 结束拖拽并触发回调

### 9. 回调执行

```javascript
executeOnResizeCallback() {
    const transparentRec = this.getTransparentRec(this.relativePosition, this.boundaryOverlay);
    browser.localStorage.setItem(this.localStorageKey, JSON.stringify(transparentRec));
    this.props.onResize({
        ...transparentRec,
        width: this.boundaryOverlay.width - 2 * transparentRec.x,
        height: this.boundaryOverlay.height - 2 * transparentRec.y,
    });
}
```

**回调功能**:
- **位置保存**: 将当前位置保存到本地存储
- **尺寸计算**: 计算裁剪区域的宽度和高度
- **回调触发**: 触发父组件的调整回调
- **数据传递**: 传递完整的裁剪区域信息

## 使用场景

### 1. 条码扫描裁剪

```javascript
// 在条码扫描器中使用裁剪覆盖层
<CropOverlay
    isReady={this.state.isReady}
    onResize={(cropInfo) => {
        console.log('裁剪区域:', cropInfo);
        this.updateScanArea(cropInfo);
    }}
>
    <div slot="default">
        <video ref="videoElement" />
    </div>
</CropOverlay>
```

### 2. 图像裁剪

```javascript
// 用于图像裁剪的覆盖层
<CropOverlay
    isReady={true}
    onResize={(cropArea) => {
        this.setCropArea(cropArea);
        this.updatePreview(cropArea);
    }}
>
    <img src={this.imageUrl} />
</CropOverlay>
```

### 3. 区域选择

```javascript
// 通用区域选择组件
<CropOverlay
    isReady={this.props.contentLoaded}
    onResize={(selection) => {
        this.props.onSelectionChange(selection);
    }}
>
    <div class="selectable-content">
        {this.props.children}
    </div>
</CropOverlay>
```

## 增强示例

```javascript
// 增强的裁剪覆盖层组件
const EnhancedCropOverlay = {
    createAdvancedOverlay: () => {
        class AdvancedCropOverlay extends CropOverlay {
            setup() {
                super.setup();
                
                // 增强状态
                this.enhancedState = useState({
                    aspectRatio: null,
                    minSize: { width: 50, height: 50 },
                    maxSize: { width: Infinity, height: Infinity },
                    snapToGrid: false,
                    gridSize: 10,
                    showGuides: true,
                    allowResize: true,
                    presets: [],
                    currentPreset: null
                });
                
                // 配置选项
                this.config = {
                    enableKeyboard: true,
                    enableAnimation: true,
                    enableMagnifier: false,
                    magneticEdges: false,
                    magneticThreshold: 10
                };
                
                // 键盘监听
                if (this.config.enableKeyboard) {
                    this.setupKeyboardHandlers();
                }
            }
            
            // 增强的默认位置计算
            computeDefaultPoint() {
                super.computeDefaultPoint();
                
                // 应用宽高比约束
                if (this.enhancedState.aspectRatio) {
                    this.applyAspectRatio();
                }
                
                // 应用尺寸限制
                this.applySizeConstraints();
                
                // 网格对齐
                if (this.enhancedState.snapToGrid) {
                    this.snapToGrid();
                }
            }
            
            // 应用宽高比
            applyAspectRatio() {
                const ratio = this.enhancedState.aspectRatio;
                if (!ratio) return;
                
                const currentWidth = this.boundaryOverlay.width - 2 * this.relativePosition.x;
                const currentHeight = this.boundaryOverlay.height - 2 * this.relativePosition.y;
                
                let newWidth, newHeight;
                
                if (currentWidth / currentHeight > ratio) {
                    // 当前太宽，调整宽度
                    newHeight = currentHeight;
                    newWidth = newHeight * ratio;
                } else {
                    // 当前太高，调整高度
                    newWidth = currentWidth;
                    newHeight = newWidth / ratio;
                }
                
                // 重新计算位置
                const newX = (this.boundaryOverlay.width - newWidth) / 2;
                const newY = (this.boundaryOverlay.height - newHeight) / 2;
                
                this.relativePosition = { x: newX, y: newY };
            }
            
            // 应用尺寸约束
            applySizeConstraints() {
                const { minSize, maxSize } = this.enhancedState;
                
                let width = this.boundaryOverlay.width - 2 * this.relativePosition.x;
                let height = this.boundaryOverlay.height - 2 * this.relativePosition.y;
                
                // 应用最小尺寸
                width = Math.max(width, minSize.width);
                height = Math.max(height, minSize.height);
                
                // 应用最大尺寸
                width = Math.min(width, maxSize.width);
                height = Math.min(height, maxSize.height);
                
                // 重新计算位置
                const newX = (this.boundaryOverlay.width - width) / 2;
                const newY = (this.boundaryOverlay.height - height) / 2;
                
                this.relativePosition = { x: newX, y: newY };
            }
            
            // 网格对齐
            snapToGrid() {
                const { gridSize } = this.enhancedState;
                
                this.relativePosition.x = Math.round(this.relativePosition.x / gridSize) * gridSize;
                this.relativePosition.y = Math.round(this.relativePosition.y / gridSize) * gridSize;
            }
            
            // 增强的指针移动处理
            pointerMove(event) {
                if (!this.isMoving) {
                    return;
                }
                
                // 调用父类方法
                super.pointerMove(event);
                
                // 应用增强约束
                if (this.enhancedState.aspectRatio) {
                    this.applyAspectRatio();
                }
                
                if (this.enhancedState.snapToGrid) {
                    this.snapToGrid();
                }
                
                // 磁性边缘
                if (this.config.magneticEdges) {
                    this.applyMagneticEdges();
                }
                
                // 更新视觉效果
                this.calculateAndSetTransparentRect();
            }
            
            // 磁性边缘
            applyMagneticEdges() {
                const threshold = this.config.magneticThreshold;
                const { width, height } = this.boundaryOverlay;
                
                // 检查是否接近边缘
                if (this.relativePosition.x < threshold) {
                    this.relativePosition.x = 0;
                }
                if (this.relativePosition.y < threshold) {
                    this.relativePosition.y = 0;
                }
                if (width - this.relativePosition.x < threshold) {
                    this.relativePosition.x = width;
                }
                if (height - this.relativePosition.y < threshold) {
                    this.relativePosition.y = height;
                }
                
                // 检查是否接近中心
                const centerX = width / 2;
                const centerY = height / 2;
                
                if (Math.abs(this.relativePosition.x - centerX) < threshold) {
                    this.relativePosition.x = centerX;
                }
                if (Math.abs(this.relativePosition.y - centerY) < threshold) {
                    this.relativePosition.y = centerY;
                }
            }
            
            // 设置键盘处理
            setupKeyboardHandlers() {
                this.keyboardHandler = (event) => {
                    if (!this.isMoving) return;
                    
                    const step = event.shiftKey ? 10 : 1;
                    
                    switch (event.key) {
                        case 'ArrowLeft':
                            this.relativePosition.x -= step;
                            break;
                        case 'ArrowRight':
                            this.relativePosition.x += step;
                            break;
                        case 'ArrowUp':
                            this.relativePosition.y -= step;
                            break;
                        case 'ArrowDown':
                            this.relativePosition.y += step;
                            break;
                        case 'Escape':
                            this.resetToDefault();
                            break;
                        default:
                            return;
                    }
                    
                    event.preventDefault();
                    this.calculateAndSetTransparentRect();
                };
                
                document.addEventListener('keydown', this.keyboardHandler);
            }
            
            // 设置宽高比
            setAspectRatio(ratio) {
                this.enhancedState.aspectRatio = ratio;
                this.applyAspectRatio();
                this.calculateAndSetTransparentRect();
                this.executeOnResizeCallback();
            }
            
            // 设置尺寸限制
            setSizeConstraints(minSize, maxSize) {
                this.enhancedState.minSize = minSize;
                this.enhancedState.maxSize = maxSize;
                this.applySizeConstraints();
                this.calculateAndSetTransparentRect();
                this.executeOnResizeCallback();
            }
            
            // 设置预设位置
            setPreset(preset) {
                this.enhancedState.currentPreset = preset;
                this.relativePosition = { ...preset.position };
                this.calculateAndSetTransparentRect();
                this.executeOnResizeCallback();
            }
            
            // 添加预设
            addPreset(name, position = null) {
                const preset = {
                    name,
                    position: position || { ...this.relativePosition }
                };
                
                this.enhancedState.presets.push(preset);
                return preset;
            }
            
            // 重置到默认位置
            resetToDefault() {
                this.computeDefaultPoint();
                this.calculateAndSetTransparentRect();
                this.executeOnResizeCallback();
            }
            
            // 获取裁剪信息
            getCropInfo() {
                const transparentRec = this.getTransparentRec(this.relativePosition, this.boundaryOverlay);
                return {
                    ...transparentRec,
                    width: this.boundaryOverlay.width - 2 * transparentRec.x,
                    height: this.boundaryOverlay.height - 2 * transparentRec.y,
                    aspectRatio: this.enhancedState.aspectRatio,
                    preset: this.enhancedState.currentPreset
                };
            }
            
            // 清理资源
            willDestroy() {
                if (this.keyboardHandler) {
                    document.removeEventListener('keydown', this.keyboardHandler);
                }
                
                super.willDestroy && super.willDestroy();
            }
        }
        
        return AdvancedCropOverlay;
    }
};

// 使用示例
const AdvancedOverlay = EnhancedCropOverlay.createAdvancedOverlay();

<AdvancedOverlay
    isReady={true}
    onResize={(cropInfo) => {
        console.log('增强裁剪信息:', cropInfo);
    }}
    aspectRatio={16/9}
    minSize={{ width: 100, height: 60 }}
    snapToGrid={true}
    gridSize={20}
    enableKeyboard={true}
    magneticEdges={true}
>
    <img src="image.jpg" />
</AdvancedOverlay>
```

## 技术特点

### 1. 交互支持
- 鼠标和触摸事件支持
- 拖拽操作
- 实时视觉反馈

### 2. 位置持久化
- 本地存储集成
- 位置恢复
- 用户偏好保存

### 3. 边界控制
- 严格的边界限制
- 安全的位置计算
- 容器适配

### 4. CSS集成
- CSS变量驱动
- 样式系统集成
- 动态样式更新

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可重用的裁剪组件
- 插槽支持

### 2. 观察者模式 (Observer Pattern)
- 位置变化通知
- 回调机制

### 3. 状态模式 (State Pattern)
- 拖拽状态管理
- 交互状态控制

## 注意事项

1. **性能优化**: 避免频繁的DOM操作
2. **内存管理**: 及时清理事件监听器
3. **边界检查**: 确保位置在有效范围内
4. **用户体验**: 提供流畅的拖拽体验

## 扩展建议

1. **多点触控**: 支持多点触控缩放
2. **手势识别**: 支持手势操作
3. **动画效果**: 添加平滑的动画过渡
4. **预设位置**: 提供常用的预设位置
5. **键盘控制**: 支持键盘精确控制

该裁剪覆盖层组件为Odoo Web应用的条码扫描功能提供了精确的区域控制能力，通过直观的拖拽交互和位置持久化确保了良好的用户体验。

# @web/core/user.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/user.js`
- **原始路径**: `/web/static/src/core/user.js`
- **代码行数**: 169行
- **作用**: 管理当前用户信息、权限检查、用户设置和上下文管理

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 用户信息的结构和管理方式
- 权限检查和访问控制机制
- 用户设置的读取和更新
- 上下文管理和国际化支持
- 缓存机制在权限检查中的应用

## 📚 核心概念

### 什么是User对象？
User对象是Odoo Web框架中的**当前用户信息管理器**，主要功能：
- **用户信息**: 存储当前登录用户的基本信息
- **权限检查**: 提供组权限和访问权限检查
- **上下文管理**: 管理用户上下文和国际化设置
- **设置管理**: 处理用户个人设置的读写

### 基本使用模式
```javascript
// 导入用户对象
const { user } = require("@web/core/user");

// 获取用户信息
console.log(user.name);        // 用户姓名
console.log(user.login);       // 登录名
console.log(user.userId);      // 用户ID
console.log(user.isAdmin);     // 是否管理员

// 权限检查
const hasGroup = await user.hasGroup("sales_team.group_sale_manager");
const canWrite = await user.checkAccessRight("res.partner", "write");

// 用户设置
await user.setUserSettings("theme", "dark");
console.log(user.settings);
```

## 🏗️ 用户对象结构分析

### 用户基本信息
```javascript
const user = {
    name,                    // 用户显示名称
    login,                   // 登录用户名
    userId,                  // 用户ID
    partnerId,              // 关联的合作伙伴ID
    homeActionId,           // 主页动作ID
    isAdmin,                // 是否管理员
    isSystem,               // 是否系统用户
    showEffect,             // 是否显示特效
    writeDate,              // 合作伙伴写入日期
    
    // 动态属性
    get context(),          // 用户上下文
    get lang(),             // 用户语言
    get tz(),               // 用户时区
    get settings(),         // 用户设置
    
    // 方法
    updateContext(),        // 更新上下文
    hasGroup(),             // 检查组权限
    checkAccessRight(),     // 检查访问权限
    setUserSettings(),      // 设置用户配置
};
```

### 从Session到User的转换
```javascript
function _makeUser(session) {
    // 1. 从session中提取用户信息
    const {
        home_action_id: homeActionId,
        is_admin: isAdmin,
        is_internal_user: isInternalUser,
        is_system: isSystem,
        name,
        partner_id: partnerId,
        show_effect: showEffect,
        uid: userId,
        username: login,
        user_context: context,
        user_settings,
        partner_write_date: writeDate,
    } = session;
    
    // 2. 从session中删除用户信息（单一数据源原则）
    delete session.home_action_id;
    delete session.is_admin;
    // ... 删除其他用户相关字段
    
    // 3. 创建用户对象
    return user;
}
```

**设计原则**：
- **单一数据源**: 用户信息只在user对象中维护
- **数据清理**: 从session中移除用户信息避免重复
- **结构化**: 将平铺的session数据结构化为user对象

## 🔍 核心功能详解

### 1. 权限检查系统

#### hasGroup() - 组权限检查
```javascript
hasGroup(group) {
    return groupCache.read(group, this.context);
}
```

**缓存机制**：
```javascript
const getGroupCacheValue = (group, context) => {
    if (!userId) {
        return Promise.resolve(false);
    }
    return rpc("/web/dataset/call_kw/res.users/has_group", {
        model: "res.users",
        method: "has_group",
        args: [userId, group],
        kwargs: { context },
    });
};

const groupCache = new Cache(getGroupCacheValue, getGroupCacheKey);
// 预填充常用权限
groupCache.cache["base.group_user"] = Promise.resolve(isInternalUser);
groupCache.cache["base.group_system"] = Promise.resolve(isSystem);
```

**使用示例**：
```javascript
// 检查销售管理员权限
const isSalesManager = await user.hasGroup("sales_team.group_sale_manager");

// 检查会计权限
const isAccountant = await user.hasGroup("account.group_account_user");

// 检查系统管理员权限
const isSystemAdmin = await user.hasGroup("base.group_system");
```

#### checkAccessRight() - 访问权限检查
```javascript
checkAccessRight(model, operation, ids = []) {
    return accessRightCache.read(model, operation, ensureArray(ids), this.context);
}
```

**支持的操作类型**：
- **read**: 读取权限
- **write**: 写入权限
- **create**: 创建权限
- **unlink**: 删除权限

**使用示例**：
```javascript
// 检查是否可以写入合作伙伴
const canWritePartner = await user.checkAccessRight("res.partner", "write");

// 检查是否可以删除特定记录
const canDeleteOrder = await user.checkAccessRight("sale.order", "unlink", [orderId]);

// 检查是否可以创建产品
const canCreateProduct = await user.checkAccessRight("product.product", "create");
```

### 2. 上下文管理

#### context属性 - 动态上下文
```javascript
get context() {
    return Object.assign({}, context, { uid: this.userId });
}
```

**上下文内容**：
```javascript
const userContext = {
    uid: 1,                    // 用户ID
    lang: "en_US",            // 语言
    tz: "UTC",                // 时区
    allowed_company_ids: [1], // 允许的公司ID
    // ... 其他上下文信息
};
```

#### updateContext() - 更新上下文
```javascript
updateContext(update) {
    Object.assign(context, update);
}
```

**使用示例**：
```javascript
// 切换语言
user.updateContext({ lang: "zh_CN" });

// 切换时区
user.updateContext({ tz: "Asia/Shanghai" });

// 切换公司
user.updateContext({ allowed_company_ids: [2] });
```

### 3. 用户设置管理

#### setUserSettings() - 设置用户配置
```javascript
async setUserSettings(key, value) {
    const model = "res.users.settings";
    const method = "set_res_users_settings";
    const changedSettings = await rpc(`/web/dataset/call_kw/${model}/${method}`, {
        model,
        method,
        args: [[this.settings.id]],
        kwargs: {
            new_settings: {
                [key]: value,
            },
            context: this.context,
        },
    });
    Object.assign(settings, changedSettings);
}
```

**常用设置项**：
```javascript
// 主题设置
await user.setUserSettings("theme", "dark");

// 语言设置
await user.setUserSettings("lang", "zh_CN");

// 时区设置
await user.setUserSettings("tz", "Asia/Shanghai");

// 自定义设置
await user.setUserSettings("sidebar_collapsed", true);
```

### 4. 国际化支持

#### lang属性 - 语言设置
```javascript
get lang() {
    return lang; // 从pyToJsLocale(context?.lang)转换而来
}
```

#### tz属性 - 时区设置
```javascript
get tz() {
    return this.context.tz;
}
```

**国际化使用**：
```javascript
// 获取用户语言
const userLang = user.lang; // "en-US", "zh-CN" 等

// 获取用户时区
const userTz = user.tz; // "UTC", "Asia/Shanghai" 等

// 在日期格式化中使用
const formattedDate = new Intl.DateTimeFormat(user.lang, {
    timeZone: user.tz
}).format(new Date());
```

## 🔄 最近连接用户管理

### 本地存储管理
```javascript
const LAST_CONNECTED_USER_KEY = "web.lastConnectedUser";

const getLastConnectedUsers = () => {
    const lastConnectedUsers = browser.localStorage.getItem(LAST_CONNECTED_USER_KEY);
    return lastConnectedUsers ? JSON.parse(lastConnectedUsers) : [];
};

const setLastConnectedUsers = (users) => {
    browser.localStorage.setItem(LAST_CONNECTED_USER_KEY, JSON.stringify(users.slice(0, 5)));
};
```

### 自动记录当前用户
```javascript
if (user.login && user.login !== "__system__") {
    const users = getLastConnectedUsers();
    const lastConnectedUsers = [
        {
            login: user.login,
            name: user.name,
            partnerId: user.partnerId,
            partnerWriteDate: user.writeDate,
            userId: user.userId,
        },
        ...users.filter((u) => u.userId !== user.userId),
    ];
    setLastConnectedUsers(lastConnectedUsers);
}
```

**功能特点**：
- **自动记录**: 用户登录时自动记录到本地存储
- **去重处理**: 避免重复记录同一用户
- **限制数量**: 最多保存5个最近用户
- **排除系统用户**: 不记录系统用户

## 🎨 实际应用场景

### 1. 在组件中使用用户信息
```javascript
class UserProfileComponent extends Component {
    setup() {
        this.user = user;
    }
    
    get displayInfo() {
        return {
            name: this.user.name,
            login: this.user.login,
            isAdmin: this.user.isAdmin,
            lang: this.user.lang,
            tz: this.user.tz
        };
    }
}
```

### 2. 权限控制的按钮组件
```javascript
class PermissionButton extends Component {
    setup() {
        this.user = user;
        this.state = useState({ hasPermission: false });
        this.checkPermission();
    }
    
    async checkPermission() {
        if (this.props.group) {
            this.state.hasPermission = await this.user.hasGroup(this.props.group);
        } else if (this.props.model && this.props.operation) {
            this.state.hasPermission = await this.user.checkAccessRight(
                this.props.model, 
                this.props.operation
            );
        }
    }
    
    static template = xml`
        <button t-if="state.hasPermission" t-on-click="props.onClick">
            <t t-esc="props.text"/>
        </button>
    `;
}
```

### 3. 多语言支持组件
```javascript
class LocalizedComponent extends Component {
    setup() {
        this.user = user;
    }
    
    formatDate(date) {
        return new Intl.DateTimeFormat(this.user.lang, {
            timeZone: this.user.tz,
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }).format(date);
    }
    
    formatCurrency(amount) {
        return new Intl.NumberFormat(this.user.lang, {
            style: 'currency',
            currency: this.user.context.currency || 'USD'
        }).format(amount);
    }
}
```

## 🛠️ 实践练习

### 练习1: 用户权限管理器
```javascript
class UserPermissionManager {
    constructor(user) {
        this.user = user;
        this.permissionCache = new Map();
    }
    
    async hasAnyGroup(groups) {
        const promises = groups.map(group => this.user.hasGroup(group));
        const results = await Promise.all(promises);
        return results.some(result => result);
    }
    
    async hasAllGroups(groups) {
        const promises = groups.map(group => this.user.hasGroup(group));
        const results = await Promise.all(promises);
        return results.every(result => result);
    }
    
    async canPerformOperation(model, operation, recordIds = []) {
        const cacheKey = `${model}:${operation}:${recordIds.join(',')}`;
        
        if (this.permissionCache.has(cacheKey)) {
            return this.permissionCache.get(cacheKey);
        }
        
        const hasPermission = await this.user.checkAccessRight(model, operation, recordIds);
        this.permissionCache.set(cacheKey, hasPermission);
        
        return hasPermission;
    }
    
    async getModelPermissions(model) {
        const operations = ['read', 'write', 'create', 'unlink'];
        const promises = operations.map(op => 
            this.user.checkAccessRight(model, op).then(result => [op, result])
        );
        const results = await Promise.all(promises);
        
        return Object.fromEntries(results);
    }
    
    clearCache() {
        this.permissionCache.clear();
    }
}

// 使用示例
const permissionManager = new UserPermissionManager(user);

// 检查是否有任一销售权限
const hasSalesPermission = await permissionManager.hasAnyGroup([
    'sales_team.group_sale_user',
    'sales_team.group_sale_manager'
]);

// 获取产品模型的所有权限
const productPermissions = await permissionManager.getModelPermissions('product.product');
```

### 练习2: 用户设置管理器
```javascript
class UserSettingsManager {
    constructor(user) {
        this.user = user;
        this.localSettings = new Map();
    }
    
    async setSetting(key, value, persistent = true) {
        if (persistent) {
            await this.user.setUserSettings(key, value);
        } else {
            this.localSettings.set(key, value);
        }
    }
    
    getSetting(key, defaultValue = null) {
        // 优先从本地设置获取
        if (this.localSettings.has(key)) {
            return this.localSettings.get(key);
        }
        
        // 然后从用户设置获取
        return this.user.settings[key] || defaultValue;
    }
    
    async setTheme(theme) {
        await this.setSetting('theme', theme);
        document.body.className = document.body.className.replace(/theme-\w+/, `theme-${theme}`);
    }
    
    async setLanguage(lang) {
        this.user.updateContext({ lang });
        await this.setSetting('lang', lang);
        // 触发页面重新加载以应用新语言
        window.location.reload();
    }
    
    async setTimezone(tz) {
        this.user.updateContext({ tz });
        await this.setSetting('tz', tz);
    }
    
    exportSettings() {
        return {
            persistent: { ...this.user.settings },
            local: Object.fromEntries(this.localSettings)
        };
    }
    
    async importSettings(settings) {
        // 导入持久化设置
        for (const [key, value] of Object.entries(settings.persistent || {})) {
            await this.setSetting(key, value, true);
        }
        
        // 导入本地设置
        for (const [key, value] of Object.entries(settings.local || {})) {
            this.setSetting(key, value, false);
        }
    }
}
```

### 练习3: 用户会话管理器
```javascript
class UserSessionManager {
    constructor(user) {
        this.user = user;
        this.sessionData = new Map();
        this.sessionTimeout = 30 * 60 * 1000; // 30分钟
        this.lastActivity = Date.now();
        this.setupActivityTracking();
    }
    
    setupActivityTracking() {
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
        
        events.forEach(event => {
            document.addEventListener(event, () => {
                this.updateLastActivity();
            }, true);
        });
        
        // 定期检查会话超时
        setInterval(() => {
            this.checkSessionTimeout();
        }, 60000); // 每分钟检查一次
    }
    
    updateLastActivity() {
        this.lastActivity = Date.now();
    }
    
    checkSessionTimeout() {
        const now = Date.now();
        const timeSinceLastActivity = now - this.lastActivity;
        
        if (timeSinceLastActivity > this.sessionTimeout) {
            this.handleSessionTimeout();
        }
    }
    
    handleSessionTimeout() {
        // 显示会话超时警告
        console.warn('User session timeout detected');
        
        // 可以触发重新登录或刷新会话
        this.refreshSession();
    }
    
    async refreshSession() {
        try {
            // 发送心跳请求刷新会话
            await rpc('/web/session/heartbeat', {});
            this.updateLastActivity();
        } catch (error) {
            console.error('Failed to refresh session:', error);
            // 重定向到登录页面
            window.location.href = '/web/login';
        }
    }
    
    setSessionData(key, value) {
        this.sessionData.set(key, {
            value,
            timestamp: Date.now()
        });
    }
    
    getSessionData(key) {
        const data = this.sessionData.get(key);
        return data ? data.value : null;
    }
    
    clearSessionData() {
        this.sessionData.clear();
    }
    
    getSessionInfo() {
        return {
            userId: this.user.userId,
            login: this.user.login,
            lastActivity: new Date(this.lastActivity).toISOString(),
            sessionDataSize: this.sessionData.size,
            isActive: Date.now() - this.lastActivity < this.sessionTimeout
        };
    }
}
```

## 🔧 调试技巧

### 查看用户信息
```javascript
// 查看完整用户对象
console.log('User object:', user);

// 查看用户上下文
console.log('User context:', user.context);

// 查看用户设置
console.log('User settings:', user.settings);

// 查看最近连接用户
console.log('Last connected users:', getLastConnectedUsers());
```

### 权限调试
```javascript
// 测试权限检查
async function debugPermissions() {
    const groups = [
        'base.group_user',
        'base.group_system',
        'sales_team.group_sale_manager'
    ];
    
    for (const group of groups) {
        const hasGroup = await user.hasGroup(group);
        console.log(`${group}: ${hasGroup}`);
    }
    
    const models = ['res.partner', 'sale.order', 'product.product'];
    const operations = ['read', 'write', 'create', 'unlink'];
    
    for (const model of models) {
        for (const operation of operations) {
            const hasAccess = await user.checkAccessRight(model, operation);
            console.log(`${model}.${operation}: ${hasAccess}`);
        }
    }
}
```

## 📊 性能考虑

### 缓存优化
- **权限缓存**: 使用Cache类缓存权限检查结果
- **预填充**: 预填充常用权限避免网络请求
- **上下文感知**: 缓存键包含上下文信息

### 最佳实践
```javascript
// ✅ 好的做法：批量权限检查
const permissions = await Promise.all([
    user.hasGroup('group1'),
    user.hasGroup('group2'),
    user.checkAccessRight('model1', 'write')
]);

// ❌ 不好的做法：串行权限检查
const perm1 = await user.hasGroup('group1');
const perm2 = await user.hasGroup('group2');
const perm3 = await user.checkAccessRight('model1', 'write');
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解用户对象的结构和属性
- [ ] 掌握权限检查的使用方法
- [ ] 理解上下文管理和国际化支持
- [ ] 能够管理用户设置和会话
- [ ] 掌握缓存机制在权限检查中的应用
- [ ] 了解最近连接用户的管理方式

## 🚀 下一步学习
学完用户系统后，建议继续学习：
1. **会话管理** (`@web/session.js`) - 理解会话信息管理
2. **国际化** (`@web/core/l10n/`) - 深入学习多语言支持
3. **权限系统** (`@web/core/security/`) - 了解安全和权限机制

## 💡 重要提示
- 用户对象是权限控制的核心
- 缓存机制对权限检查性能至关重要
- 上下文管理影响所有后端调用
- 用户设置提供个性化体验

## 🔍 深入理解：用户系统的设计精髓

### 单一数据源原则
```javascript
// 设计问题：数据重复和不一致
const userFromSession = session.name;
const userFromUserObject = user.name;
// 如果两者不同步会导致问题

// 解决方案：单一数据源
function _makeUser(session) {
    // 1. 提取用户信息
    const { name, uid, ... } = session;

    // 2. 从session中删除，确保单一数据源
    delete session.name;
    delete session.uid;

    // 3. 只在user对象中维护
    return { name, userId: uid, ... };
}
```

**设计优势**：
- **数据一致性**: 避免多处维护相同数据
- **清晰职责**: session负责传输，user负责管理
- **易于维护**: 修改用户信息只需要更新一处

### 权限缓存的智能设计
```javascript
// 缓存键设计
const getGroupCacheKey = (group) => group;
const getAccessRightCacheKey = (model, operation, ids) =>
    JSON.stringify([model, operation, ids]);

// 预填充策略
groupCache.cache["base.group_user"] = Promise.resolve(isInternalUser);
groupCache.cache["base.group_system"] = Promise.resolve(isSystem);
```

**缓存策略分析**：
- **简单键**: 组权限使用组名作为键
- **复合键**: 访问权限使用JSON序列化的复合键
- **预填充**: 预填充常用权限避免网络请求
- **Promise缓存**: 缓存Promise而非结果，避免重复请求

### 动态属性的巧妙实现
```javascript
const user = {
    // 静态属性
    name,
    userId,

    // 动态属性 - 每次访问都重新计算
    get context() {
        return Object.assign({}, context, { uid: this.userId });
    },

    get settings() {
        return Object.assign({}, settings); // 返回副本防止修改
    }
};
```

**设计考虑**：
- **实时性**: context总是包含最新的uid
- **不可变性**: settings返回副本防止意外修改
- **性能**: 简单计算，不需要缓存

## 🎓 高级应用模式

### 1. 用户权限装饰器
```javascript
// 权限装饰器工厂
function requirePermission(groupOrModel, operation = null) {
    return function(target, propertyKey, descriptor) {
        const originalMethod = descriptor.value;

        descriptor.value = async function(...args) {
            let hasPermission;

            if (operation) {
                // 检查访问权限
                hasPermission = await user.checkAccessRight(groupOrModel, operation);
            } else {
                // 检查组权限
                hasPermission = await user.hasGroup(groupOrModel);
            }

            if (!hasPermission) {
                throw new Error(`Permission denied: ${groupOrModel}${operation ? '.' + operation : ''}`);
            }

            return originalMethod.apply(this, args);
        };

        return descriptor;
    };
}

// 使用装饰器
class SalesController {
    @requirePermission('sales_team.group_sale_manager')
    async approveSale(saleId) {
        // 只有销售经理可以执行
        return this.orm.write('sale.order', [saleId], { state: 'sale' });
    }

    @requirePermission('res.partner', 'write')
    async updatePartner(partnerId, data) {
        // 需要合作伙伴写权限
        return this.orm.write('res.partner', [partnerId], data);
    }
}
```

### 2. 上下文管理器
```javascript
class ContextManager {
    constructor(user) {
        this.user = user;
        this.contextStack = [];
    }

    // 临时切换上下文
    withContext(contextUpdate, callback) {
        // 保存当前上下文
        const originalContext = { ...this.user.context };
        this.contextStack.push(originalContext);

        try {
            // 应用临时上下文
            this.user.updateContext(contextUpdate);

            // 执行回调
            return callback();
        } finally {
            // 恢复原始上下文
            const restored = this.contextStack.pop();
            this.user.updateContext(restored);
        }
    }

    // 异步版本
    async withContextAsync(contextUpdate, asyncCallback) {
        const originalContext = { ...this.user.context };
        this.contextStack.push(originalContext);

        try {
            this.user.updateContext(contextUpdate);
            return await asyncCallback();
        } finally {
            const restored = this.contextStack.pop();
            this.user.updateContext(restored);
        }
    }

    // 切换公司上下文
    async withCompany(companyId, callback) {
        return this.withContextAsync(
            { allowed_company_ids: [companyId] },
            callback
        );
    }

    // 切换语言上下文
    async withLanguage(lang, callback) {
        return this.withContextAsync(
            { lang },
            callback
        );
    }
}

// 使用示例
const contextManager = new ContextManager(user);

// 临时切换到中文环境执行操作
const chineseResult = await contextManager.withLanguage('zh_CN', async () => {
    return orm.searchRead('res.partner', [], ['name']);
});

// 临时切换到其他公司执行操作
const companyResult = await contextManager.withCompany(2, async () => {
    return orm.searchRead('sale.order', [], ['name', 'amount_total']);
});
```

### 3. 用户偏好管理系统
```javascript
class UserPreferenceManager {
    constructor(user) {
        this.user = user;
        this.preferences = new Map();
        this.watchers = new Map();
        this.loadPreferences();
    }

    async loadPreferences() {
        // 从用户设置加载偏好
        const settings = this.user.settings;
        for (const [key, value] of Object.entries(settings)) {
            this.preferences.set(key, value);
        }
    }

    async setPreference(key, value, options = {}) {
        const {
            persistent = true,
            notify = true,
            validate = null
        } = options;

        // 验证值
        if (validate && !validate(value)) {
            throw new Error(`Invalid value for preference ${key}: ${value}`);
        }

        // 保存偏好
        this.preferences.set(key, value);

        // 持久化到服务器
        if (persistent) {
            await this.user.setUserSettings(key, value);
        }

        // 通知观察者
        if (notify) {
            this.notifyWatchers(key, value);
        }
    }

    getPreference(key, defaultValue = null) {
        return this.preferences.get(key) ?? defaultValue;
    }

    // 监听偏好变化
    watch(key, callback) {
        if (!this.watchers.has(key)) {
            this.watchers.set(key, new Set());
        }
        this.watchers.get(key).add(callback);

        // 返回取消监听函数
        return () => {
            this.watchers.get(key).delete(callback);
        };
    }

    notifyWatchers(key, value) {
        const callbacks = this.watchers.get(key);
        if (callbacks) {
            callbacks.forEach(callback => {
                try {
                    callback(value, key);
                } catch (error) {
                    console.error('Preference watcher error:', error);
                }
            });
        }
    }

    // 预定义的偏好管理
    async setTheme(theme) {
        await this.setPreference('theme', theme, {
            validate: (value) => ['light', 'dark', 'auto'].includes(value)
        });

        // 应用主题
        document.documentElement.setAttribute('data-theme', theme);
    }

    async setDensity(density) {
        await this.setPreference('density', density, {
            validate: (value) => ['compact', 'normal', 'comfortable'].includes(value)
        });

        // 应用密度
        document.body.className = document.body.className
            .replace(/density-\w+/, `density-${density}`);
    }

    async setSidebarCollapsed(collapsed) {
        await this.setPreference('sidebar_collapsed', collapsed);

        // 触发侧边栏状态变化
        document.body.classList.toggle('sidebar-collapsed', collapsed);
    }

    // 导出/导入偏好
    exportPreferences() {
        return Object.fromEntries(this.preferences);
    }

    async importPreferences(preferences) {
        for (const [key, value] of Object.entries(preferences)) {
            await this.setPreference(key, value);
        }
    }
}

// 使用示例
const preferenceManager = new UserPreferenceManager(user);

// 监听主题变化
const unwatch = preferenceManager.watch('theme', (theme) => {
    console.log('Theme changed to:', theme);
    // 更新UI
});

// 设置偏好
await preferenceManager.setTheme('dark');
await preferenceManager.setSidebarCollapsed(true);
```

## 🔧 调试和监控工具

### 用户状态监控器
```javascript
class UserStateMonitor {
    constructor(user) {
        this.user = user;
        this.permissionCache = new Map();
        this.accessLog = [];
        this.startMonitoring();
    }

    startMonitoring() {
        // 包装权限检查方法
        this.wrapPermissionMethods();

        // 监控上下文变化
        this.monitorContextChanges();

        // 定期生成报告
        setInterval(() => {
            this.generateReport();
        }, 5 * 60 * 1000); // 每5分钟
    }

    wrapPermissionMethods() {
        const originalHasGroup = this.user.hasGroup;
        const originalCheckAccessRight = this.user.checkAccessRight;

        this.user.hasGroup = async (group) => {
            const startTime = performance.now();
            const result = await originalHasGroup.call(this.user, group);
            const duration = performance.now() - startTime;

            this.logAccess('hasGroup', { group, result, duration });
            return result;
        };

        this.user.checkAccessRight = async (model, operation, ids) => {
            const startTime = performance.now();
            const result = await originalCheckAccessRight.call(this.user, model, operation, ids);
            const duration = performance.now() - startTime;

            this.logAccess('checkAccessRight', { model, operation, ids, result, duration });
            return result;
        };
    }

    logAccess(method, details) {
        this.accessLog.push({
            timestamp: Date.now(),
            method,
            ...details
        });

        // 保持最近1000条记录
        if (this.accessLog.length > 1000) {
            this.accessLog = this.accessLog.slice(-1000);
        }
    }

    monitorContextChanges() {
        const originalUpdateContext = this.user.updateContext;

        this.user.updateContext = (update) => {
            console.log('Context updated:', update);
            this.logAccess('updateContext', { update });
            return originalUpdateContext.call(this.user, update);
        };
    }

    generateReport() {
        const now = Date.now();
        const lastHour = this.accessLog.filter(log => now - log.timestamp < 60 * 60 * 1000);

        const groupChecks = lastHour.filter(log => log.method === 'hasGroup');
        const accessChecks = lastHour.filter(log => log.method === 'checkAccessRight');

        const report = {
            summary: {
                totalChecks: lastHour.length,
                groupChecks: groupChecks.length,
                accessChecks: accessChecks.length,
                averageResponseTime: this.calculateAverageResponseTime(lastHour)
            },
            mostCheckedGroups: this.getMostChecked(groupChecks, 'group'),
            mostCheckedModels: this.getMostChecked(accessChecks, 'model'),
            slowestChecks: lastHour
                .sort((a, b) => b.duration - a.duration)
                .slice(0, 10)
        };

        console.log('User State Report:', report);
        return report;
    }

    calculateAverageResponseTime(logs) {
        if (logs.length === 0) return 0;
        const total = logs.reduce((sum, log) => sum + (log.duration || 0), 0);
        return (total / logs.length).toFixed(2);
    }

    getMostChecked(logs, field) {
        const counts = {};
        logs.forEach(log => {
            const key = log[field];
            counts[key] = (counts[key] || 0) + 1;
        });

        return Object.entries(counts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([key, count]) => ({ [field]: key, count }));
    }
}

// 在开发模式下启用监控
if (user.isAdmin && odoo.debug) {
    const monitor = new UserStateMonitor(user);
    window.__USER_MONITOR__ = monitor;
}
```

---

**用户系统是Odoo Web框架的权限控制核心，理解它的设计和实现对开发安全可靠的应用至关重要！** 🚀

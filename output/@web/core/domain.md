# @web/core/domain.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/domain.js`
- **原始路径**: `/web/static/src/core/domain.js`
- **代码行数**: 442行
- **作用**: 实现Odoo域（Domain）系统，提供搜索条件的表示、组合、求值和匹配功能

## 🎯 学习目标
通过学习这个文件，您将掌握：
- Odoo域系统的完整实现原理和设计思想
- 域的各种表示形式和转换方法
- 域的逻辑运算和组合策略
- 域匹配算法和求值机制
- AST（抽象语法树）在域处理中的应用

## 📚 核心概念

### 什么是Domain？
Domain是Odoo中的**搜索条件表示系统**，用于定义记录的过滤条件：
- **搜索过滤**: 在列表视图中过滤记录
- **权限控制**: 定义用户可访问的记录范围
- **业务规则**: 表示复杂的业务逻辑条件
- **数据验证**: 检查记录是否满足特定条件

### Domain的表示形式

#### 1. 列表表示（DomainListRepr）
```javascript
// 基础条件：[字段名, 操作符, 值]
const simpleDomain = [["name", "=", "John"]];

// 复合条件：使用逻辑操作符
const complexDomain = [
    "&",                           // AND操作符
    ["name", "ilike", "John"],     // 名称包含John
    ["age", ">", 18]               // 年龄大于18
];

// 更复杂的条件
const advancedDomain = [
    "|",                           // OR操作符
    "&",                           // AND操作符
    ["is_company", "=", true],     // 是公司
    ["country_id", "=", 1],        // 国家ID为1
    ["name", "ilike", "tech"]      // 或者名称包含tech
];
```

#### 2. 字符串表示
```javascript
// Python表达式字符串
const stringDomain = "[('name', '=', 'John'), ('age', '>', 18)]";

// 带上下文变量的字符串
const contextualDomain = "[('user_id', '=', uid), ('active', '=', True)]";
```

#### 3. Domain对象
```javascript
// 从列表创建
const domain1 = new Domain([["name", "=", "John"]]);

// 从字符串创建
const domain2 = new Domain("[('name', '=', 'John')]");

// 空域（匹配所有记录）
const emptyDomain = new Domain([]);
```

### 基本使用模式
```javascript
// 导入domain模块
const { Domain, evalDomain } = require("@web/core/domain");

// 创建域
const domain = new Domain([["name", "ilike", "john"], ["active", "=", true]]);

// 检查记录是否匹配
const record = { name: "John Doe", active: true, age: 25 };
const matches = domain.contains(record); // true

// 域的逻辑运算
const domain1 = new Domain([["name", "=", "John"]]);
const domain2 = new Domain([["age", ">", 18]]);
const combined = Domain.and([domain1, domain2]);

// 转换为不同格式
const listRepr = domain.toList({}); // 转为列表表示
const stringRepr = domain.toString(); // 转为字符串表示
```

## 🔍 核心类和方法详解

### 1. Domain类的静态方法

#### combine() - 域组合
```javascript
static combine(domains, operator) {
    if (domains.length === 0) {
        return new Domain([]);
    }
    const domain1 = domains[0] instanceof Domain ? domains[0] : new Domain(domains[0]);
    if (domains.length === 1) {
        return domain1;
    }
    const domain2 = Domain.combine(domains.slice(1), operator);
    const result = new Domain([]);
    const astValues1 = domain1.ast.value;
    const astValues2 = domain2.ast.value;
    const op = operator === "AND" ? "&" : "|";
    const combinedAST = { type: 4 /* List */, value: astValues1.concat(astValues2) };
    result.ast = normalizeDomainAST(combinedAST, op);
    return result;
}
```

**递归组合策略**：
- **基础情况**: 空数组返回空域，单个域直接返回
- **递归分解**: 将多个域分解为两个域的组合
- **AST合并**: 在AST级别进行域的合并
- **标准化**: 使用normalizeDomainAST确保结果的正确性

#### and() / or() - 逻辑运算
```javascript
// AND运算：所有条件都必须满足
const andDomain = Domain.and([
    [["name", "=", "John"]],
    [["age", ">", 18]],
    [["active", "=", true]]
]);
// 结果: [["&"], ["&"], ["name", "=", "John"], ["age", ">", 18], ["active", "=", true]]

// OR运算：任一条件满足即可
const orDomain = Domain.or([
    [["name", "=", "John"]],
    [["name", "=", "Jane"]]
]);
// 结果: [["|"], ["name", "=", "John"], ["name", "=", "Jane"]]
```

#### not() - 否定运算
```javascript
const originalDomain = new Domain([["active", "=", true]]);
const negatedDomain = Domain.not(originalDomain);
// 结果: [["!"], ["active", "=", true]]
```

#### removeDomainLeaves() - 移除特定字段的条件
```javascript
const domain = new Domain([
    "&",
    ["name", "=", "John"],
    ["department_id", "=", 1]
]);

const filtered = Domain.removeDomainLeaves(domain, ["department_id"]);
// 移除department_id相关的条件，保留其他条件
```

### 2. Domain实例方法

#### contains() - 记录匹配检查
```javascript
contains(record) {
    const expr = evaluate(this.ast, record);
    return matchDomain(record, expr);
}
```

**匹配流程**：
1. **AST求值**: 使用Python解释器求值域表达式
2. **域匹配**: 使用matchDomain函数检查记录是否满足条件
3. **返回结果**: 布尔值表示是否匹配

#### toString() / toList() / toJson() - 格式转换
```javascript
const domain = new Domain([["name", "=", "John"]]);

// 转为字符串（Python表达式）
const str = domain.toString(); // "[('name', '=', 'John')]"

// 转为列表（JavaScript数组）
const list = domain.toList({}); // [["name", "=", "John"]]

// 转为JSON（智能选择格式）
const json = domain.toJson(); // 列表或字符串，取决于是否包含上下文变量
```

## 🎨 操作符系统详解

### 比较操作符
```javascript
// 相等比较
["field", "=", "value"]     // 等于
["field", "!=", "value"]    // 不等于
["field", "=?", "value"]    // 等于或为空

// 数值比较
["age", ">", 18]           // 大于
["age", ">=", 18]          // 大于等于
["age", "<", 65]           // 小于
["age", "<=", 65]          // 小于等于

// 集合操作
["id", "in", [1, 2, 3]]           // 在集合中
["id", "not in", [1, 2, 3]]       // 不在集合中
```

### 字符串操作符
```javascript
// 模糊匹配（区分大小写）
["name", "like", "John%"]          // 以John开头
["name", "like", "%Doe"]           // 以Doe结尾
["name", "like", "%John%"]         // 包含John
["name", "not like", "%test%"]     // 不包含test

// 模糊匹配（不区分大小写）
["name", "ilike", "john%"]         // 以john开头（忽略大小写）
["name", "not ilike", "%TEST%"]    // 不包含test（忽略大小写）

// 精确模式匹配
["name", "=like", "John*"]         // 使用*作为通配符
["name", "=ilike", "john*"]        // 不区分大小写的通配符匹配
```

### 特殊操作符
```javascript
// 任意匹配（总是返回true）
["field", "any", "value"]          // 总是匹配
["field", "not_any", "value"]      // 总是匹配
```

## 🛠️ 实际应用场景

### 1. 在搜索视图中的使用
```javascript
class SearchModel {
    constructor() {
        this.domain = new Domain([]);
        this.filters = new Map();
    }
    
    addFilter(name, domain) {
        this.filters.set(name, new Domain(domain));
        this.updateDomain();
    }
    
    removeFilter(name) {
        this.filters.delete(name);
        this.updateDomain();
    }
    
    updateDomain() {
        const filterDomains = Array.from(this.filters.values());
        this.domain = Domain.and(filterDomains);
    }
    
    search(records) {
        return records.filter(record => this.domain.contains(record));
    }
}

// 使用示例
const searchModel = new SearchModel();
searchModel.addFilter("active", [["active", "=", true]]);
searchModel.addFilter("recent", [["create_date", ">=", "2024-01-01"]]);

const filteredRecords = searchModel.search(allRecords);
```

### 2. 在权限控制中的使用
```javascript
class RecordRuleManager {
    constructor() {
        this.rules = new Map();
    }
    
    addRule(model, domain, groups = []) {
        if (!this.rules.has(model)) {
            this.rules.set(model, []);
        }
        this.rules.get(model).push({
            domain: new Domain(domain),
            groups
        });
    }
    
    getAccessibleRecords(model, records, userGroups) {
        const modelRules = this.rules.get(model) || [];
        
        // 获取适用于用户的规则
        const applicableRules = modelRules.filter(rule => 
            rule.groups.length === 0 || 
            rule.groups.some(group => userGroups.includes(group))
        );
        
        if (applicableRules.length === 0) {
            return records; // 无规则限制
        }
        
        // 组合所有规则（OR关系）
        const ruleDomains = applicableRules.map(rule => rule.domain);
        const combinedDomain = Domain.or(ruleDomains);
        
        return records.filter(record => combinedDomain.contains(record));
    }
}

// 使用示例
const ruleManager = new RecordRuleManager();

// 添加规则：用户只能看到自己的记录
ruleManager.addRule("res.partner", [["user_id", "=", "uid"]], ["base.group_user"]);

// 添加规则：管理员可以看到所有记录
ruleManager.addRule("res.partner", [], ["base.group_system"]);

const accessibleRecords = ruleManager.getAccessibleRecords(
    "res.partner", 
    allPartners, 
    ["base.group_user"]
);
```

### 3. 在动态字段显示中的使用
```javascript
class ConditionalFieldManager {
    constructor() {
        this.fieldRules = new Map();
    }
    
    addFieldRule(fieldName, condition, action) {
        this.fieldRules.set(fieldName, {
            domain: new Domain(condition),
            action // 'show', 'hide', 'required', 'readonly'
        });
    }
    
    evaluateFields(record) {
        const fieldStates = {};
        
        for (const [fieldName, rule] of this.fieldRules.entries()) {
            const matches = rule.domain.contains(record);
            fieldStates[fieldName] = {
                visible: rule.action === 'show' ? matches : !matches,
                required: rule.action === 'required' && matches,
                readonly: rule.action === 'readonly' && matches
            };
        }
        
        return fieldStates;
    }
}

// 使用示例
const fieldManager = new ConditionalFieldManager();

// 当is_company为true时显示company_type字段
fieldManager.addFieldRule("company_type", [["is_company", "=", true]], "show");

// 当state为'done'时设置字段为只读
fieldManager.addFieldRule("amount", [["state", "=", "done"]], "readonly");

const record = { is_company: true, state: "draft", amount: 1000 };
const fieldStates = fieldManager.evaluateFields(record);
// 结果: { company_type: { visible: true, ... }, amount: { readonly: false, ... } }
```

## 🛠️ 实践练习

### 练习1: 域构建器
```javascript
class DomainBuilder {
    constructor() {
        this.conditions = [];
        this.operator = "AND";
    }
    
    // 设置逻辑操作符
    setOperator(operator) {
        this.operator = operator;
        return this;
    }
    
    // 添加条件
    where(field, operator, value) {
        this.conditions.push([field, operator, value]);
        return this;
    }
    
    // 便捷方法
    equals(field, value) {
        return this.where(field, "=", value);
    }
    
    notEquals(field, value) {
        return this.where(field, "!=", value);
    }
    
    greaterThan(field, value) {
        return this.where(field, ">", value);
    }
    
    lessThan(field, value) {
        return this.where(field, "<", value);
    }
    
    like(field, pattern) {
        return this.where(field, "ilike", pattern);
    }
    
    in(field, values) {
        return this.where(field, "in", values);
    }
    
    // 日期范围
    dateRange(field, startDate, endDate) {
        this.where(field, ">=", startDate);
        this.where(field, "<=", endDate);
        return this;
    }
    
    // 构建域
    build() {
        if (this.conditions.length === 0) {
            return new Domain([]);
        }
        
        if (this.conditions.length === 1) {
            return new Domain([this.conditions[0]]);
        }
        
        const domains = this.conditions.map(condition => new Domain([condition]));
        return this.operator === "AND" ? Domain.and(domains) : Domain.or(domains);
    }
    
    // 重置构建器
    reset() {
        this.conditions = [];
        this.operator = "AND";
        return this;
    }
    
    // 克隆构建器
    clone() {
        const builder = new DomainBuilder();
        builder.conditions = [...this.conditions];
        builder.operator = this.operator;
        return builder;
    }
}

// 使用示例
const builder = new DomainBuilder();

// 构建复杂查询
const domain = builder
    .equals("active", true)
    .like("name", "%tech%")
    .greaterThan("create_date", "2024-01-01")
    .in("state", ["draft", "confirmed"])
    .build();

// 构建OR查询
const orDomain = builder
    .reset()
    .setOperator("OR")
    .equals("is_company", true)
    .like("name", "%corp%")
    .build();

// 日期范围查询
const dateRangeDomain = builder
    .reset()
    .dateRange("create_date", "2024-01-01", "2024-12-31")
    .equals("active", true)
    .build();
```

### 练习2: 域分析器
```javascript
class DomainAnalyzer {
    constructor(domain) {
        this.domain = domain instanceof Domain ? domain : new Domain(domain);
        this.ast = this.domain.ast;
    }

    // 提取所有字段名
    getFields() {
        const fields = new Set();
        this.extractFields(this.ast, fields);
        return Array.from(fields);
    }

    extractFields(node, fields) {
        if (node.type === 10) { // Tuple (condition)
            const fieldNode = node.value[0];
            if (fieldNode.type === 1) { // String
                fields.add(fieldNode.value);
            }
        } else if (node.type === 4) { // List
            node.value.forEach(child => this.extractFields(child, fields));
        }
    }

    // 提取所有操作符
    getOperators() {
        const operators = new Set();
        this.extractOperators(this.ast, operators);
        return Array.from(operators);
    }

    extractOperators(node, operators) {
        if (node.type === 10) { // Tuple (condition)
            const operatorNode = node.value[1];
            if (operatorNode.type === 1) { // String
                operators.add(operatorNode.value);
            }
        } else if (node.type === 4) { // List
            node.value.forEach(child => this.extractOperators(child, operators));
        } else if (node.type === 1 && ["&", "|", "!"].includes(node.value)) {
            operators.add(node.value);
        }
    }

    // 计算域的复杂度
    getComplexity() {
        return this.calculateComplexity(this.ast);
    }

    calculateComplexity(node) {
        if (node.type === 10) { // Tuple (condition)
            return 1;
        } else if (node.type === 4) { // List
            return node.value.reduce((sum, child) => {
                if (child.type === 1 && ["&", "|", "!"].includes(child.value)) {
                    return sum + 0.5; // 逻辑操作符增加复杂度
                }
                return sum + this.calculateComplexity(child);
            }, 0);
        }
        return 0;
    }

    // 检查域是否包含特定字段
    hasField(fieldName) {
        return this.getFields().includes(fieldName);
    }

    // 检查域是否使用了特定操作符
    hasOperator(operator) {
        return this.getOperators().includes(operator);
    }

    // 获取字段的所有条件
    getFieldConditions(fieldName) {
        const conditions = [];
        this.extractFieldConditions(this.ast, fieldName, conditions);
        return conditions;
    }

    extractFieldConditions(node, fieldName, conditions) {
        if (node.type === 10) { // Tuple (condition)
            const fieldNode = node.value[0];
            if (fieldNode.type === 1 && fieldNode.value === fieldName) {
                const operator = node.value[1].value;
                const value = this.nodeToValue(node.value[2]);
                conditions.push({ operator, value });
            }
        } else if (node.type === 4) { // List
            node.value.forEach(child => this.extractFieldConditions(child, fieldName, conditions));
        }
    }

    nodeToValue(node) {
        switch (node.type) {
            case 1: return node.value; // String
            case 2: return node.value; // Number
            case 3: return node.value; // Boolean
            case 4: return node.value.map(child => this.nodeToValue(child)); // List
            default: return node.value;
        }
    }

    // 生成域的统计报告
    generateReport() {
        const fields = this.getFields();
        const operators = this.getOperators();
        const complexity = this.getComplexity();

        const fieldStats = {};
        fields.forEach(field => {
            fieldStats[field] = this.getFieldConditions(field);
        });

        return {
            summary: {
                totalFields: fields.length,
                totalOperators: operators.length,
                complexity: complexity,
                domainString: this.domain.toString()
            },
            fields: fields,
            operators: operators,
            fieldConditions: fieldStats,
            recommendations: this.generateRecommendations(complexity, operators)
        };
    }

    generateRecommendations(complexity, operators) {
        const recommendations = [];

        if (complexity > 10) {
            recommendations.push("Domain is very complex, consider simplifying");
        }

        if (operators.includes("like") || operators.includes("ilike")) {
            recommendations.push("Using LIKE operators may impact performance on large datasets");
        }

        if (operators.includes("!=")) {
            recommendations.push("Consider using 'not in' instead of '!=' for better performance");
        }

        return recommendations;
    }

    // 优化域建议
    optimize() {
        // 简化的优化逻辑
        const optimized = new Domain(this.domain.toString());

        // 这里可以添加各种优化策略
        // 例如：合并相同字段的条件、移除冗余条件等

        return optimized;
    }
}

// 使用示例
const domain = new Domain([
    "&",
    ["name", "ilike", "%john%"],
    "|",
    ["age", ">", 18],
    ["is_company", "=", true]
]);

const analyzer = new DomainAnalyzer(domain);
const report = analyzer.generateReport();

console.log("Domain Analysis Report:", report);
// {
//   summary: { totalFields: 3, totalOperators: 4, complexity: 3.5, ... },
//   fields: ["name", "age", "is_company"],
//   operators: ["&", "ilike", "|", ">", "="],
//   fieldConditions: {
//     name: [{ operator: "ilike", value: "%john%" }],
//     age: [{ operator: ">", value: 18 }],
//     is_company: [{ operator: "=", value: true }]
//   },
//   recommendations: ["Using LIKE operators may impact performance on large datasets"]
// }
```

### 练习3: 域验证器
```javascript
class DomainValidator {
    constructor() {
        this.rules = new Map();
        this.setupDefaultRules();
    }

    // 添加验证规则
    addRule(name, validator, description) {
        this.rules.set(name, { validator, description });
    }

    // 验证域
    validate(domain) {
        const domainObj = domain instanceof Domain ? domain : new Domain(domain);
        const results = {
            valid: true,
            errors: [],
            warnings: [],
            suggestions: []
        };

        for (const [ruleName, rule] of this.rules.entries()) {
            try {
                const result = rule.validator(domainObj);
                if (result !== true) {
                    if (result.type === 'error') {
                        results.valid = false;
                        results.errors.push({
                            rule: ruleName,
                            message: result.message,
                            description: rule.description
                        });
                    } else if (result.type === 'warning') {
                        results.warnings.push({
                            rule: ruleName,
                            message: result.message,
                            description: rule.description
                        });
                    } else if (result.type === 'suggestion') {
                        results.suggestions.push({
                            rule: ruleName,
                            message: result.message,
                            description: rule.description
                        });
                    }
                }
            } catch (error) {
                results.valid = false;
                results.errors.push({
                    rule: ruleName,
                    message: `Validation error: ${error.message}`,
                    description: rule.description
                });
            }
        }

        return results;
    }

    setupDefaultRules() {
        // 检查域的语法正确性
        this.addRule('syntax', (domain) => {
            try {
                domain.toString();
                return true;
            } catch (error) {
                return { type: 'error', message: `Syntax error: ${error.message}` };
            }
        }, 'Checks domain syntax correctness');

        // 检查复杂度
        this.addRule('complexity', (domain) => {
            const analyzer = new DomainAnalyzer(domain);
            const complexity = analyzer.getComplexity();

            if (complexity > 15) {
                return { type: 'warning', message: `High complexity (${complexity}), consider simplifying` };
            } else if (complexity > 10) {
                return { type: 'suggestion', message: `Moderate complexity (${complexity}), could be optimized` };
            }
            return true;
        }, 'Checks domain complexity');

        // 检查性能影响的操作符
        this.addRule('performance', (domain) => {
            const analyzer = new DomainAnalyzer(domain);
            const operators = analyzer.getOperators();

            const slowOperators = operators.filter(op =>
                ['like', 'ilike', 'not like', 'not ilike'].includes(op)
            );

            if (slowOperators.length > 0) {
                return {
                    type: 'warning',
                    message: `Performance impact: using ${slowOperators.join(', ')} operators`
                };
            }
            return true;
        }, 'Checks for performance-impacting operators');

        // 检查字段命名规范
        this.addRule('field_naming', (domain) => {
            const analyzer = new DomainAnalyzer(domain);
            const fields = analyzer.getFields();

            const invalidFields = fields.filter(field =>
                !/^[a-z][a-z0-9_]*$/.test(field) && !field.includes('.')
            );

            if (invalidFields.length > 0) {
                return {
                    type: 'suggestion',
                    message: `Non-standard field names: ${invalidFields.join(', ')}`
                };
            }
            return true;
        }, 'Checks field naming conventions');

        // 检查逻辑一致性
        this.addRule('logic_consistency', (domain) => {
            const analyzer = new DomainAnalyzer(domain);
            const fields = analyzer.getFields();

            for (const field of fields) {
                const conditions = analyzer.getFieldConditions(field);

                // 检查矛盾条件
                const equalConditions = conditions.filter(c => c.operator === '=');
                if (equalConditions.length > 1) {
                    const values = equalConditions.map(c => c.value);
                    const uniqueValues = [...new Set(values)];
                    if (uniqueValues.length > 1) {
                        return {
                            type: 'error',
                            message: `Contradictory conditions for field '${field}': cannot equal multiple values`
                        };
                    }
                }

                // 检查范围冲突
                const gtConditions = conditions.filter(c => c.operator === '>');
                const ltConditions = conditions.filter(c => c.operator === '<');

                if (gtConditions.length > 0 && ltConditions.length > 0) {
                    const maxGt = Math.max(...gtConditions.map(c => c.value));
                    const minLt = Math.min(...ltConditions.map(c => c.value));

                    if (maxGt >= minLt) {
                        return {
                            type: 'error',
                            message: `Impossible range for field '${field}': > ${maxGt} and < ${minLt}`
                        };
                    }
                }
            }

            return true;
        }, 'Checks for logical inconsistencies');
    }

    // 验证并提供修复建议
    validateWithSuggestions(domain) {
        const validation = this.validate(domain);

        if (!validation.valid) {
            validation.fixes = this.generateFixes(domain, validation.errors);
        }

        return validation;
    }

    generateFixes(domain, errors) {
        const fixes = [];

        for (const error of errors) {
            switch (error.rule) {
                case 'syntax':
                    fixes.push({
                        type: 'syntax_fix',
                        description: 'Try using Domain constructor with array format',
                        example: 'new Domain([["field", "=", "value"]])'
                    });
                    break;

                case 'logic_consistency':
                    if (error.message.includes('contradictory')) {
                        fixes.push({
                            type: 'logic_fix',
                            description: 'Remove contradictory conditions or use OR operator',
                            example: 'Domain.or([domain1, domain2])'
                        });
                    }
                    break;
            }
        }

        return fixes;
    }
}

// 使用示例
const validator = new DomainValidator();

// 验证正常域
const validDomain = new Domain([["name", "=", "John"]]);
const validResult = validator.validate(validDomain);
console.log("Valid domain result:", validResult);

// 验证复杂域
const complexDomain = new Domain([
    "&", "&", "&", "&",
    ["name", "ilike", "%john%"],
    ["age", ">", 18],
    ["age", "<", 65],
    ["department", "=", "IT"],
    ["active", "=", true]
]);
const complexResult = validator.validate(complexDomain);
console.log("Complex domain result:", complexResult);

// 验证有问题的域
const problematicDomain = new Domain([
    "&",
    ["age", ">", 30],
    ["age", "<", 25]  // 逻辑矛盾
]);
const problematicResult = validator.validateWithSuggestions(problematicDomain);
console.log("Problematic domain result:", problematicResult);
```

## 🔧 调试技巧

### 查看域的内部结构
```javascript
function debugDomain(domain) {
    const domainObj = domain instanceof Domain ? domain : new Domain(domain);

    console.group('Domain Debug Info');
    console.log('String representation:', domainObj.toString());
    console.log('AST structure:', domainObj.ast);
    console.log('List representation:', domainObj.toList({}));

    const analyzer = new DomainAnalyzer(domainObj);
    console.log('Fields:', analyzer.getFields());
    console.log('Operators:', analyzer.getOperators());
    console.log('Complexity:', analyzer.getComplexity());
    console.groupEnd();
}

// 使用示例
debugDomain([["name", "ilike", "%john%"], ["age", ">", 18]]);
```

### 测试域匹配
```javascript
function testDomainMatching(domain, testRecords) {
    const domainObj = domain instanceof Domain ? domain : new Domain(domain);

    console.group(`Testing domain: ${domainObj.toString()}`);

    testRecords.forEach((record, index) => {
        const matches = domainObj.contains(record);
        console.log(`Record ${index + 1}:`, record, '→', matches ? '✅ MATCH' : '❌ NO MATCH');
    });

    console.groupEnd();
}

// 测试示例
const testDomain = new Domain([["age", ">", 18], ["active", "=", true]]);
const testRecords = [
    { name: "John", age: 25, active: true },
    { name: "Jane", age: 16, active: true },
    { name: "Bob", age: 30, active: false }
];

testDomainMatching(testDomain, testRecords);
```

## 📊 性能考虑

### 优化策略
1. **域简化**: 移除冗余条件，合并相似条件
2. **操作符选择**: 避免使用性能较差的LIKE操作符
3. **字段索引**: 确保查询字段有适当的数据库索引
4. **缓存结果**: 对频繁使用的域进行缓存

### 最佳实践
```javascript
// ✅ 好的做法：使用精确匹配
const goodDomain = new Domain([["state", "in", ["draft", "confirmed"]]]);

// ❌ 不好的做法：使用模糊匹配
const badDomain = new Domain([["state", "like", "draft"], ["state", "like", "confirmed"]]);

// ✅ 好的做法：合理的域组合
const combinedDomain = Domain.and([
    new Domain([["active", "=", true]]),
    new Domain([["create_date", ">=", "2024-01-01"]])
]);

// ❌ 不好的做法：过度复杂的域
const complexDomain = new Domain([
    "&", "&", "&", "&", "&",
    ["field1", "=", "value1"],
    ["field2", "like", "%value2%"],
    ["field3", "!=", "value3"],
    ["field4", "in", [1, 2, 3]],
    ["field5", ">", 100],
    ["field6", "ilike", "%value6%"]
]);
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解Odoo域系统的完整架构和设计思想
- [ ] 掌握域的各种表示形式和转换方法
- [ ] 熟练使用域的逻辑运算和组合操作
- [ ] 理解域匹配算法和AST处理机制
- [ ] 能够分析和优化域的性能
- [ ] 掌握域在实际应用中的使用模式

## 🚀 下一步学习
学完Domain系统后，建议继续学习：
1. **Python解释器** (`@web/core/py_js/`) - 深入理解域中Python表达式的处理
2. **搜索系统** (`@web/search/`) - 学习域在搜索功能中的应用
3. **视图系统** (`@web/views/`) - 了解域在各种视图中的使用

## 💡 重要提示
- Domain是Odoo数据过滤的核心机制
- 理解AST结构对调试复杂域至关重要
- 域的性能直接影响查询效率
- 逻辑操作符的正确使用是构建有效域的关键
```

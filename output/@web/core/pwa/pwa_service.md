# Odoo PWA 服务 (PWA Service) 学习资料

## 文件概述

**文件路径**: `output/@web/core/pwa/pwa_service.js`  
**原始路径**: `/web/static/src/core/pwa/pwa_service.js`  
**模块类型**: 核心基础模块 - PWA 服务  
**代码行数**: 184 行  
**依赖关系**: 
- `@odoo/owl` - OWL 框架 (reactive)
- `@web/core/browser/browser` - 浏览器抽象层
- `@web/core/browser/feature_detection` - 特性检测
- `@web/core/network/http_service` - HTTP 服务
- `@web/core/registry` - 服务注册表
- `@web/core/pwa/install_prompt` - 安装提示组件

## 模块功能

PWA 服务是 Odoo Web 客户端渐进式 Web 应用功能的核心服务，负责：
- 检测 PWA 安装支持和可用性
- 管理 PWA 安装提示和流程
- 处理不同平台的安装机制
- 维护安装状态和用户偏好
- 提供统一的 PWA 管理接口

## 核心概念

### 1. PWA (Progressive Web App)
渐进式 Web 应用是一种使用现代 Web 技术构建的应用程序，具有：
- **离线功能**: 通过 Service Worker 实现离线访问
- **安装能力**: 可以像原生应用一样安装到设备
- **响应式设计**: 适配各种设备和屏幕尺寸
- **安全性**: 必须通过 HTTPS 提供服务

### 2. 安装提示事件
```javascript
let BEFOREINSTALLPROMPT_EVENT;
let REGISTER_BEFOREINSTALLPROMPT_EVENT;

browser.addEventListener("beforeinstallprompt", (ev) => {
    ev.preventDefault();
    if (REGISTER_BEFOREINSTALLPROMPT_EVENT) {
        return REGISTER_BEFOREINSTALLPROMPT_EVENT(ev);
    } else {
        BEFOREINSTALLPROMPT_EVENT = ev;
    }
});
```

**事件处理机制**:
- **全局监听**: 在模块加载时立即监听事件
- **事件缓存**: 如果服务未启动，缓存事件供后续使用
- **防止默认**: 阻止浏览器默认的安装提示
- **延迟处理**: 等待服务启动后再处理事件

## 服务架构分析

### 1. 服务定义
```javascript
const pwaService = {
    dependencies: ["dialog"],
    start(env, { dialog }) {
        // 服务启动逻辑
    }
};
```

**设计特点**:
- **依赖注入**: 依赖对话框服务
- **状态管理**: 使用 reactive 管理 PWA 状态
- **平台适配**: 支持不同浏览器和平台

### 2. 响应式状态
```javascript
const state = reactive({
    canPromptToInstall: false,      // 是否可以显示安装提示
    isAvailable: false,             // PWA 功能是否可用
    isScopedApp: false,             // 是否为作用域应用
    isSupportedOnBrowser: false,    // 浏览器是否支持 PWA
    startUrl: "/odoo",              // 应用启动 URL
    decline,                        // 拒绝安装方法
    getManifest,                    // 获取清单方法
    hasScopeBeenInstalled,          // 检查安装状态方法
    show,                           // 显示安装提示方法
});
```

**状态属性详解**:
- **canPromptToInstall**: 控制是否显示安装按钮
- **isAvailable**: PWA 功能的总体可用性
- **isScopedApp**: 支持多个独立的 PWA 实例
- **isSupportedOnBrowser**: 浏览器兼容性检查

### 3. 平台支持检测
```javascript
state.isSupportedOnBrowser =
    browser.BeforeInstallPromptEvent !== undefined ||
    (isBrowserSafari() &&
        !isDisplayStandalone() &&
        (isIOS() ||
            (isMacOS() && browser.navigator.userAgent.match(/Version\/(\d+)/)[1] >= 17)));
```

**支持条件**:
- **Chrome/Edge**: 支持 BeforeInstallPromptEvent
- **Safari iOS**: 所有移动版本
- **Safari macOS**: 版本 17 及以上
- **排除条件**: 已在独立模式下运行

## 核心功能分析

### 1. 安装状态管理
```javascript
function _getInstallationState(scope = state.startUrl) {
    const installationState = browser.localStorage.getItem("pwaService.installationState");
    return installationState ? JSON.parse(installationState)[scope] : "";
}

function _setInstallationState(value) {
    const ls = JSON.parse(
        browser.localStorage.getItem("pwaService.installationState") || "{}"
    );
    ls[state.startUrl] = value;
    browser.localStorage.setItem("pwaService.installationState", JSON.stringify(ls));
}
```

**状态值**:
- **"accepted"**: 用户已接受安装
- **"dismissed"**: 用户已拒绝安装
- **""**: 未设置状态

**存储结构**:
```json
{
    "/odoo": "accepted",
    "/scoped_app/project1": "dismissed",
    "/scoped_app/project2": "accepted"
}
```

### 2. 作用域应用支持
```javascript
if (state.isScopedApp) {
    if (browser.location.pathname === "/scoped_app") {
        state.startUrl = "/" + new URL(browser.location.href).searchParams.get("path");
    } else {
        state.startUrl = browser.location.pathname;
    }
}
```

**作用域应用特点**:
- **独立实例**: 每个作用域可以独立安装
- **URL 参数**: 通过 URL 参数指定路径
- **独立状态**: 每个作用域维护独立的安装状态

### 3. 安装提示处理
```javascript
function _handleBeforeInstallPrompt(ev, installationState) {
    nativePrompt = ev;
    if (installationState === "accepted") {
        if (!isDisplayStandalone()) {
            _removeInstallationState();
        }
    }
    state.canPromptToInstall = installationState !== "dismissed";
    state.isAvailable = true;
}
```

**处理逻辑**:
- **事件存储**: 保存原生安装提示事件
- **状态检查**: 检查之前的安装状态
- **状态重置**: 如果应用被卸载，重置状态
- **提示控制**: 根据历史状态决定是否显示提示

### 4. 清单文件获取
```javascript
async function getManifest() {
    if (!_manifest) {
        const manifest = await get(
            document.querySelector("link[rel=manifest")?.getAttribute("href"),
            "text"
        );
        _manifest = JSON.parse(manifest);
    }
    return _manifest;
}
```

**清单文件**:
- **缓存机制**: 只获取一次，后续使用缓存
- **动态获取**: 从 HTML 中查找清单链接
- **JSON 解析**: 解析清单文件内容

### 5. 安装提示显示
```javascript
async function show({ onDone } = {}) {
    if (!state.isAvailable) {
        return;
    }
    if (nativePrompt) {
        const res = await nativePrompt.prompt();
        _setInstallationState(res.outcome);
        state.canPromptToInstall = false;
        if (onDone) {
            onDone(res);
        }
    } else if (isBrowserSafari()) {
        dialog.add(InstallPrompt, {
            onClose: () => {
                if (onDone) {
                    onDone({});
                }
                this.decline();
            },
        });
    }
}
```

**显示逻辑**:
- **可用性检查**: 确保 PWA 功能可用
- **原生提示**: 优先使用浏览器原生提示
- **自定义提示**: Safari 使用自定义对话框
- **状态更新**: 根据用户选择更新状态

## 实际使用示例

### 1. 基本 PWA 状态检查
```javascript
import { useService } from "@web/core/utils/hooks";

class PWAStatusComponent extends Component {
    setup() {
        this.pwa = useService("pwa");
    }
    
    get pwaStatus() {
        return {
            isSupported: this.pwa.isSupportedOnBrowser,
            isAvailable: this.pwa.isAvailable,
            canInstall: this.pwa.canPromptToInstall,
            isScoped: this.pwa.isScopedApp
        };
    }
    
    render() {
        return xml`
            <div class="pwa-status">
                <div t-if="pwaStatus.isSupported">
                    PWA 功能已支持
                </div>
                <div t-if="pwaStatus.isAvailable">
                    PWA 安装可用
                </div>
                <button t-if="pwaStatus.canInstall" 
                        t-on-click="() => this.pwa.show()">
                    安装应用
                </button>
            </div>
        `;
    }
}
```

### 2. 安装提示组件
```javascript
class InstallPromptComponent extends Component {
    setup() {
        this.pwa = useService("pwa");
        this.notification = useService("notification");
    }
    
    async showInstallPrompt() {
        if (!this.pwa.canPromptToInstall) {
            this.notification.add("应用安装不可用", { type: "warning" });
            return;
        }
        
        try {
            await this.pwa.show({
                onDone: (result) => {
                    if (result.outcome === "accepted") {
                        this.notification.add("应用安装成功", { type: "success" });
                    } else if (result.outcome === "dismissed") {
                        this.notification.add("用户取消了安装", { type: "info" });
                    }
                }
            });
        } catch (error) {
            this.notification.add("安装过程出错", { type: "danger" });
        }
    }
    
    declineInstall() {
        this.pwa.decline();
        this.notification.add("已隐藏安装提示", { type: "info" });
    }
    
    render() {
        return xml`
            <div class="install-prompt" t-if="pwa.canPromptToInstall">
                <div class="prompt-content">
                    <h4>安装 Odoo 应用</h4>
                    <p>将 Odoo 安装到您的设备，享受更好的体验</p>
                    <div class="prompt-actions">
                        <button t-on-click="showInstallPrompt" class="btn btn-primary">
                            安装
                        </button>
                        <button t-on-click="declineInstall" class="btn btn-secondary">
                            不再提示
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
}
```

### 3. PWA 管理器
```javascript
class PWAManager {
    constructor(pwaService, notificationService) {
        this.pwa = pwaService;
        this.notification = notificationService;
    }
    
    async checkInstallationStatus() {
        const manifest = await this.pwa.getManifest();
        
        return {
            appName: manifest.name,
            isInstallable: this.pwa.canPromptToInstall,
            isInstalled: this.pwa.hasScopeBeenInstalled(),
            platform: this.detectPlatform()
        };
    }
    
    detectPlatform() {
        if (isIOS()) return "iOS";
        if (isMacOS()) return "macOS";
        if (isAndroid()) return "Android";
        return "Desktop";
    }
    
    async promptInstallation(options = {}) {
        const status = await this.checkInstallationStatus();
        
        if (!status.isInstallable) {
            throw new Error("应用当前不可安装");
        }
        
        return new Promise((resolve) => {
            this.pwa.show({
                onDone: (result) => {
                    this.handleInstallationResult(result, options);
                    resolve(result);
                }
            });
        });
    }
    
    handleInstallationResult(result, options) {
        switch (result.outcome) {
            case "accepted":
                this.notification.add("应用安装成功！", { type: "success" });
                if (options.onSuccess) {
                    options.onSuccess(result);
                }
                break;
                
            case "dismissed":
                this.notification.add("安装已取消", { type: "info" });
                if (options.onCancel) {
                    options.onCancel(result);
                }
                break;
                
            default:
                if (options.onComplete) {
                    options.onComplete(result);
                }
        }
    }
    
    hideInstallPrompt() {
        this.pwa.decline();
        this.notification.add("安装提示已隐藏", { type: "info" });
    }
    
    async getAppInfo() {
        const manifest = await this.pwa.getManifest();
        
        return {
            name: manifest.name,
            shortName: manifest.short_name,
            description: manifest.description,
            icons: manifest.icons,
            themeColor: manifest.theme_color,
            backgroundColor: manifest.background_color,
            startUrl: manifest.start_url,
            scope: manifest.scope
        };
    }
}
```

### 4. 作用域应用管理
```javascript
class ScopedAppManager {
    constructor(pwaService) {
        this.pwa = pwaService;
        this.installedApps = new Map();
    }
    
    checkScopeInstallation(scope) {
        return this.pwa.hasScopeBeenInstalled(scope);
    }
    
    async installScopedApp(scope, appInfo) {
        if (this.checkScopeInstallation(scope)) {
            throw new Error(`作用域 ${scope} 已经安装`);
        }
        
        // 导航到作用域安装页面
        const installUrl = `/scoped_app?path=${encodeURIComponent(scope)}`;
        window.location.href = installUrl;
    }
    
    getInstalledScopes() {
        const installationState = JSON.parse(
            localStorage.getItem("pwaService.installationState") || "{}"
        );
        
        return Object.entries(installationState)
            .filter(([scope, state]) => state === "accepted")
            .map(([scope]) => scope);
    }
    
    uninstallScope(scope) {
        const ls = JSON.parse(
            localStorage.getItem("pwaService.installationState") || "{}"
        );
        delete ls[scope];
        localStorage.setItem("pwaService.installationState", JSON.stringify(ls));
        
        this.installedApps.delete(scope);
    }
    
    async getScopeManifest(scope) {
        try {
            const response = await fetch(`${scope}/manifest.json`);
            return await response.json();
        } catch (error) {
            console.error(`获取作用域 ${scope} 的清单失败:`, error);
            return null;
        }
    }
}
```

### 5. PWA 状态监控
```javascript
class PWAStatusMonitor extends Component {
    setup() {
        this.pwa = useService("pwa");
        this.state = useState({
            installationHistory: [],
            currentStatus: "unknown"
        });
        
        // 监听 PWA 状态变化
        useEffect(() => {
            this.updateStatus();
        }, () => [
            this.pwa.canPromptToInstall,
            this.pwa.isAvailable,
            this.pwa.isSupportedOnBrowser
        ]);
    }
    
    updateStatus() {
        let status = "unknown";
        
        if (!this.pwa.isSupportedOnBrowser) {
            status = "unsupported";
        } else if (!this.pwa.isAvailable) {
            status = "unavailable";
        } else if (this.pwa.canPromptToInstall) {
            status = "installable";
        } else {
            status = "installed_or_dismissed";
        }
        
        if (status !== this.state.currentStatus) {
            this.state.installationHistory.push({
                timestamp: Date.now(),
                status: status,
                previousStatus: this.state.currentStatus
            });
            this.state.currentStatus = status;
        }
    }
    
    getStatusMessage() {
        const messages = {
            unsupported: "您的浏览器不支持 PWA 功能",
            unavailable: "PWA 功能当前不可用",
            installable: "应用可以安装到您的设备",
            installed_or_dismissed: "应用已安装或用户已拒绝安装"
        };
        
        return messages[this.state.currentStatus] || "状态未知";
    }
    
    render() {
        return xml`
            <div class="pwa-status-monitor">
                <div class="current-status">
                    <strong>PWA 状态:</strong>
                    <span t-esc="getStatusMessage()"/>
                </div>
                
                <div t-if="state.installationHistory.length" class="status-history">
                    <h5>状态历史:</h5>
                    <ul>
                        <t t-foreach="state.installationHistory" t-as="entry" t-key="entry_index">
                            <li>
                                <span t-esc="new Date(entry.timestamp).toLocaleString()"/>:
                                <span t-esc="entry.status"/>
                            </li>
                        </t>
                    </ul>
                </div>
            </div>
        `;
    }
}
```

## 设计模式分析

### 1. 服务模式 (Service Pattern)
```javascript
const pwaService = {
    dependencies: ["dialog"],
    start(env, { dialog }) {
        return state;
    }
};
```

**优势**:
- **单例**: 全局唯一的 PWA 管理器
- **依赖注入**: 通过服务系统注入依赖
- **状态共享**: 全局共享 PWA 状态

### 2. 状态模式 (State Pattern)
```javascript
const state = reactive({
    canPromptToInstall: false,
    isAvailable: false,
    // 其他状态...
});
```

**实现**:
- **响应式状态**: 使用 reactive 实现状态管理
- **状态转换**: 根据事件和条件转换状态
- **状态持久化**: 使用 localStorage 持久化状态

### 3. 策略模式 (Strategy Pattern)
```javascript
if (nativePrompt) {
    // 使用原生提示策略
} else if (isBrowserSafari()) {
    // 使用自定义对话框策略
}
```

**应用**:
- **平台适配**: 不同平台使用不同的安装策略
- **降级处理**: 不支持原生提示时的降级策略
- **用户体验**: 根据平台特性优化用户体验

### 4. 观察者模式 (Observer Pattern)
```javascript
REGISTER_BEFOREINSTALLPROMPT_EVENT = (ev) => {
    _handleBeforeInstallPrompt(ev, installationState);
};
```

**实现**:
- **事件监听**: 监听浏览器的安装提示事件
- **状态通知**: 状态变化时通知相关组件
- **解耦**: 事件处理与业务逻辑解耦

## 最佳实践

### 1. 安装提示时机
```javascript
// ✅ 推荐：在用户完成关键操作后提示安装
async function onUserEngagement() {
    if (this.pwa.canPromptToInstall && this.userHasEngaged()) {
        await this.pwa.show();
    }
}

// ❌ 避免：页面加载时立即提示
// 用户体验不佳，容易被拒绝
```

### 2. 状态检查
```javascript
// ✅ 推荐：检查所有必要条件
if (this.pwa.isSupportedOnBrowser && 
    this.pwa.isAvailable && 
    this.pwa.canPromptToInstall) {
    // 显示安装按钮
}

// ❌ 避免：只检查单一条件
if (this.pwa.canPromptToInstall) {
    // 可能在不支持的浏览器上显示
}
```

### 3. 错误处理
```javascript
// ✅ 推荐：提供完善的错误处理
try {
    await this.pwa.show({
        onDone: (result) => {
            this.handleInstallResult(result);
        }
    });
} catch (error) {
    console.error("PWA 安装失败:", error);
    this.showErrorMessage("安装过程中出现错误");
}

// ❌ 避免：忽略错误
this.pwa.show(); // 没有错误处理
```

### 4. 用户体验优化
```javascript
// ✅ 推荐：提供清晰的安装说明
class InstallGuide extends Component {
    render() {
        return xml`
            <div class="install-guide">
                <h4>安装 Odoo 应用</h4>
                <ul>
                    <li>享受更快的加载速度</li>
                    <li>离线访问部分功能</li>
                    <li>桌面快捷方式</li>
                    <li>原生应用体验</li>
                </ul>
            </div>
        `;
    }
}

// ❌ 避免：没有说明安装的好处
// 用户不知道为什么要安装
```

## 总结

PWA 服务是 Odoo Web 客户端渐进式 Web 应用功能的核心，它提供了：
- **跨平台支持**: 支持 Chrome、Safari 等主流浏览器
- **智能检测**: 自动检测 PWA 支持和安装状态
- **状态管理**: 完整的安装状态持久化和管理
- **用户体验**: 优化的安装流程和用户交互
- **作用域支持**: 支持多个独立的 PWA 实例

这个服务为 Odoo 提供了现代化的应用安装和管理能力，让用户能够像使用原生应用一样使用 Odoo，提升了用户体验和应用的可访问性。

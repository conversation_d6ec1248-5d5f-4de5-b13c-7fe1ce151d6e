# Odoo PWA 模块架构总览与使用指南

## 📁 模块概览

**目录路径**: `output/@web/core/pwa/`  
**模块类型**: 核心基础模块 - 渐进式 Web 应用系统  
**功能范围**: PWA 安装、管理、用户体验优化

## 🏗️ 架构图

```
@web/core/pwa/
├── 📄 pwa_service.js      # PWA 服务
└── 📄 install_prompt.js   # 安装提示组件
```

## 🎯 模块功能矩阵

| 模块 | 主要功能 | 核心API | 依赖关系 |
|------|----------|---------|----------|
| **pwa_service.js** | PWA 管理 | `show()`, `decline()`, `getManifest()` | owl, browser, http, registry |
| **install_prompt.js** | 安装提示 | `InstallPrompt` 组件 | owl, dialog, feature_detection |

## 🔄 数据流图

```mermaid
graph TD
    A[用户访问应用] --> B[PWA 服务启动]
    B --> C[检测浏览器支持]
    C --> D{支持 PWA?}
    
    D -->|是| E[监听安装事件]
    D -->|否| F[功能不可用]
    
    E --> G[beforeinstallprompt 事件]
    G --> H[更新服务状态]
    H --> I[canPromptToInstall = true]
    
    I --> J[用户触发安装]
    J --> K{有原生提示?}
    
    K -->|是| L[显示原生提示]
    K -->|否| M[显示自定义提示]
    
    L --> N[用户选择]
    M --> O[InstallPrompt 组件]
    O --> P[平台特定指导]
    
    N --> Q{接受安装?}
    Q -->|是| R[安装应用]
    Q -->|否| S[拒绝安装]
    
    R --> T[更新安装状态]
    S --> U[标记为已拒绝]
    
    T --> V[localStorage 持久化]
    U --> V
```

## 🚀 快速开始

### 1. 基本 PWA 状态检查
```javascript
import { useService } from "@web/core/utils/hooks";

class PWAStatusComponent extends Component {
    setup() {
        this.pwa = useService("pwa");
    }
    
    get canInstall() {
        return this.pwa.isSupportedOnBrowser && 
               this.pwa.isAvailable && 
               this.pwa.canPromptToInstall;
    }
    
    render() {
        return xml`
            <div class="pwa-status">
                <div t-if="pwa.isSupportedOnBrowser">
                    <span class="badge badge-success">PWA 支持</span>
                </div>
                <button t-if="canInstall" 
                        t-on-click="() => this.pwa.show()"
                        class="btn btn-primary">
                    <i class="fa fa-download"/> 安装应用
                </button>
            </div>
        `;
    }
}
```

### 2. 安装提示集成
```javascript
class PWAInstallManager extends Component {
    setup() {
        this.pwa = useService("pwa");
        this.notification = useService("notification");
    }
    
    async showInstallPrompt() {
        try {
            await this.pwa.show({
                onDone: (result) => {
                    this.handleInstallResult(result);
                }
            });
        } catch (error) {
            this.notification.add("安装过程出错", { type: "danger" });
        }
    }
    
    handleInstallResult(result) {
        if (result.outcome === "accepted") {
            this.notification.add("应用安装成功！", { type: "success" });
        } else if (result.outcome === "dismissed") {
            this.notification.add("安装已取消", { type: "info" });
        }
    }
    
    declineInstall() {
        this.pwa.decline();
        this.notification.add("已隐藏安装提示", { type: "info" });
    }
}
```

## 📚 核心概念详解

### 1. PWA 服务 (PWA Service)
- **状态管理**: 管理 PWA 的安装状态和可用性
- **平台检测**: 检测不同浏览器和平台的 PWA 支持
- **事件处理**: 处理浏览器的安装提示事件
- **持久化**: 使用 localStorage 持久化用户偏好

### 2. 安装提示组件 (Install Prompt)
- **自定义界面**: 为不支持原生提示的浏览器提供自定义界面
- **平台适配**: 根据不同平台显示相应的安装指导
- **用户体验**: 提供清晰的安装步骤和说明
- **对话框集成**: 基于对话框系统构建

### 3. 浏览器支持策略
- **Chrome/Edge**: 使用原生 beforeinstallprompt 事件
- **Safari iOS**: 所有移动版本支持，显示自定义指导
- **Safari macOS**: 版本 17+ 支持，显示自定义指导
- **其他浏览器**: 根据特性检测决定支持程度

### 4. 安装状态管理
- **accepted**: 用户已接受安装
- **dismissed**: 用户已拒绝安装
- **未设置**: 首次访问或状态已清除

## 🔧 高级用法

### 1. PWA 管理器
```javascript
class PWAManager {
    constructor(pwaService, notificationService) {
        this.pwa = pwaService;
        this.notification = notificationService;
        this.installationHistory = [];
    }
    
    async checkInstallationStatus() {
        const manifest = await this.pwa.getManifest();
        
        return {
            appName: manifest.name,
            isInstallable: this.pwa.canPromptToInstall,
            isInstalled: this.pwa.hasScopeBeenInstalled(),
            platform: this.detectPlatform(),
            supportLevel: this.getSupportLevel()
        };
    }
    
    detectPlatform() {
        if (isIOS()) return "iOS";
        if (isMacOS()) return "macOS";
        if (isAndroid()) return "Android";
        return "Desktop";
    }
    
    getSupportLevel() {
        if (!this.pwa.isSupportedOnBrowser) return "unsupported";
        if (this.pwa.isAvailable) return "full";
        return "limited";
    }
    
    async promptInstallation(options = {}) {
        const status = await this.checkInstallationStatus();
        
        if (!status.isInstallable) {
            throw new Error("应用当前不可安装");
        }
        
        // 记录安装尝试
        this.installationHistory.push({
            timestamp: Date.now(),
            platform: status.platform,
            action: "prompt_shown"
        });
        
        return new Promise((resolve) => {
            this.pwa.show({
                onDone: (result) => {
                    this.recordInstallationResult(result, status.platform);
                    this.handleInstallationResult(result, options);
                    resolve(result);
                }
            });
        });
    }
    
    recordInstallationResult(result, platform) {
        this.installationHistory.push({
            timestamp: Date.now(),
            platform: platform,
            action: "user_response",
            outcome: result.outcome || "unknown"
        });
    }
    
    getInstallationMetrics() {
        const total = this.installationHistory.filter(h => h.action === "user_response").length;
        const accepted = this.installationHistory.filter(h => h.outcome === "accepted").length;
        
        return {
            totalPrompts: total,
            acceptedInstalls: accepted,
            conversionRate: total > 0 ? (accepted / total) * 100 : 0,
            history: this.installationHistory
        };
    }
}
```

### 2. 作用域应用管理
```javascript
class ScopedPWAManager {
    constructor(pwaService) {
        this.pwa = pwaService;
        this.scopeRegistry = new Map();
    }
    
    registerScope(scope, config) {
        this.scopeRegistry.set(scope, {
            ...config,
            registeredAt: Date.now()
        });
    }
    
    async installScopedApp(scope, appInfo) {
        if (!this.scopeRegistry.has(scope)) {
            throw new Error(`未注册的作用域: ${scope}`);
        }
        
        const config = this.scopeRegistry.get(scope);
        
        if (this.pwa.hasScopeBeenInstalled(scope)) {
            throw new Error(`作用域 ${scope} 已经安装`);
        }
        
        // 导航到作用域安装页面
        const installUrl = `/scoped_app?path=${encodeURIComponent(scope)}`;
        const installWindow = window.open(installUrl, '_blank');
        
        // 监听安装完成
        return this.waitForScopeInstallation(scope, installWindow);
    }
    
    async waitForScopeInstallation(scope, installWindow) {
        return new Promise((resolve, reject) => {
            const checkInterval = setInterval(() => {
                if (installWindow.closed) {
                    clearInterval(checkInterval);
                    
                    // 检查是否安装成功
                    if (this.pwa.hasScopeBeenInstalled(scope)) {
                        resolve({ scope, installed: true });
                    } else {
                        reject(new Error("安装被取消或失败"));
                    }
                }
            }, 1000);
            
            // 超时处理
            setTimeout(() => {
                clearInterval(checkInterval);
                if (!installWindow.closed) {
                    installWindow.close();
                }
                reject(new Error("安装超时"));
            }, 300000); // 5分钟超时
        });
    }
    
    getInstalledScopes() {
        const installationState = JSON.parse(
            localStorage.getItem("pwaService.installationState") || "{}"
        );
        
        return Object.entries(installationState)
            .filter(([scope, state]) => state === "accepted")
            .map(([scope]) => ({
                scope,
                config: this.scopeRegistry.get(scope),
                installedAt: this.getInstallationTime(scope)
            }));
    }
    
    uninstallScope(scope) {
        const ls = JSON.parse(
            localStorage.getItem("pwaService.installationState") || "{}"
        );
        delete ls[scope];
        localStorage.setItem("pwaService.installationState", JSON.stringify(ls));
        
        // 触发卸载事件
        this.onScopeUninstalled(scope);
    }
    
    onScopeUninstalled(scope) {
        const config = this.scopeRegistry.get(scope);
        if (config && config.onUninstall) {
            config.onUninstall(scope);
        }
    }
}
```

### 3. PWA 状态监控
```javascript
class PWAStatusMonitor extends Component {
    setup() {
        this.pwa = useService("pwa");
        this.state = useState({
            statusHistory: [],
            currentStatus: "unknown",
            metrics: {
                promptsShown: 0,
                installsCompleted: 0,
                installsDeclined: 0
            }
        });
        
        // 监听 PWA 状态变化
        useEffect(() => {
            this.updateStatus();
        }, () => [
            this.pwa.canPromptToInstall,
            this.pwa.isAvailable,
            this.pwa.isSupportedOnBrowser
        ]);
        
        // 定期检查状态
        this.statusCheckInterval = setInterval(() => {
            this.performStatusCheck();
        }, 30000); // 30秒检查一次
        
        onWillUnmount(() => {
            clearInterval(this.statusCheckInterval);
        });
    }
    
    updateStatus() {
        let status = "unknown";
        
        if (!this.pwa.isSupportedOnBrowser) {
            status = "unsupported";
        } else if (!this.pwa.isAvailable) {
            status = "unavailable";
        } else if (this.pwa.canPromptToInstall) {
            status = "installable";
        } else {
            status = "installed_or_dismissed";
        }
        
        if (status !== this.state.currentStatus) {
            this.state.statusHistory.push({
                timestamp: Date.now(),
                status: status,
                previousStatus: this.state.currentStatus,
                trigger: "state_change"
            });
            this.state.currentStatus = status;
        }
    }
    
    performStatusCheck() {
        // 检查是否有新的安装机会
        if (this.pwa.isSupportedOnBrowser && !this.pwa.isAvailable) {
            // 可能需要重新检查
            this.checkForNewInstallOpportunity();
        }
    }
    
    async checkForNewInstallOpportunity() {
        try {
            // 检查清单文件是否更新
            const manifest = await this.pwa.getManifest();
            const lastChecked = localStorage.getItem("pwa.lastManifestCheck");
            const now = Date.now();
            
            if (!lastChecked || (now - parseInt(lastChecked)) > 86400000) { // 24小时
                localStorage.setItem("pwa.lastManifestCheck", now.toString());
                
                // 记录检查
                this.state.statusHistory.push({
                    timestamp: now,
                    status: this.state.currentStatus,
                    trigger: "periodic_check",
                    manifestVersion: manifest.version || "unknown"
                });
            }
        } catch (error) {
            console.error("检查安装机会失败:", error);
        }
    }
    
    getStatusMessage() {
        const messages = {
            unsupported: "您的浏览器不支持 PWA 功能",
            unavailable: "PWA 功能当前不可用",
            installable: "应用可以安装到您的设备",
            installed_or_dismissed: "应用已安装或用户已拒绝安装"
        };
        
        return messages[this.state.currentStatus] || "状态未知";
    }
    
    exportStatusReport() {
        return {
            currentStatus: this.state.currentStatus,
            statusMessage: this.getStatusMessage(),
            history: this.state.statusHistory,
            metrics: this.state.metrics,
            browserInfo: {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                standalone: window.navigator.standalone,
                displayMode: window.matchMedia('(display-mode: standalone)').matches
            },
            timestamp: Date.now()
        };
    }
}
```

### 4. 智能安装提示
```javascript
class SmartInstallPrompt {
    constructor(pwaService, analyticsService) {
        this.pwa = pwaService;
        this.analytics = analyticsService;
        this.userEngagement = {
            pageViews: 0,
            timeSpent: 0,
            interactions: 0,
            lastVisit: null
        };
    }
    
    trackUserEngagement() {
        this.userEngagement.pageViews++;
        this.userEngagement.lastVisit = Date.now();
        
        // 跟踪页面停留时间
        const startTime = Date.now();
        window.addEventListener('beforeunload', () => {
            this.userEngagement.timeSpent += Date.now() - startTime;
        });
        
        // 跟踪用户交互
        ['click', 'scroll', 'keydown'].forEach(event => {
            document.addEventListener(event, () => {
                this.userEngagement.interactions++;
            }, { once: true });
        });
    }
    
    shouldShowInstallPrompt() {
        if (!this.pwa.canPromptToInstall) {
            return false;
        }
        
        // 基于用户参与度的智能提示
        const engagementScore = this.calculateEngagementScore();
        const timingScore = this.calculateTimingScore();
        
        return engagementScore >= 0.6 && timingScore >= 0.5;
    }
    
    calculateEngagementScore() {
        const weights = {
            pageViews: 0.3,
            timeSpent: 0.4,
            interactions: 0.3
        };
        
        // 标准化分数 (0-1)
        const normalizedPageViews = Math.min(this.userEngagement.pageViews / 5, 1);
        const normalizedTimeSpent = Math.min(this.userEngagement.timeSpent / 300000, 1); // 5分钟
        const normalizedInteractions = Math.min(this.userEngagement.interactions / 20, 1);
        
        return (
            normalizedPageViews * weights.pageViews +
            normalizedTimeSpent * weights.timeSpent +
            normalizedInteractions * weights.interactions
        );
    }
    
    calculateTimingScore() {
        const now = Date.now();
        const lastPrompt = localStorage.getItem("pwa.lastPromptTime");
        
        if (!lastPrompt) {
            return 1; // 首次提示
        }
        
        const timeSinceLastPrompt = now - parseInt(lastPrompt);
        const minInterval = 7 * 24 * 60 * 60 * 1000; // 7天
        
        return Math.min(timeSinceLastPrompt / minInterval, 1);
    }
    
    async showSmartPrompt() {
        if (!this.shouldShowInstallPrompt()) {
            return false;
        }
        
        // 记录提示时间
        localStorage.setItem("pwa.lastPromptTime", Date.now().toString());
        
        // 记录分析数据
        this.analytics.track("smart_install_prompt_shown", {
            engagementScore: this.calculateEngagementScore(),
            timingScore: this.calculateTimingScore(),
            userEngagement: this.userEngagement
        });
        
        try {
            const result = await this.pwa.show({
                onDone: (result) => {
                    this.analytics.track("smart_install_prompt_result", {
                        outcome: result.outcome,
                        engagementScore: this.calculateEngagementScore()
                    });
                }
            });
            
            return result;
        } catch (error) {
            this.analytics.track("smart_install_prompt_error", {
                error: error.message
            });
            throw error;
        }
    }
}
```

## 🎨 最佳实践

### 1. 安装时机选择
```javascript
// ✅ 推荐：在用户完成有意义的操作后提示
class OptimalTimingInstaller {
    constructor(pwaService) {
        this.pwa = pwaService;
        this.userMilestones = [];
    }
    
    onUserMilestone(milestone) {
        this.userMilestones.push({
            type: milestone,
            timestamp: Date.now()
        });
        
        // 在关键里程碑后提示安装
        const criticalMilestones = ['first_save', 'profile_completed', 'feature_used'];
        if (criticalMilestones.includes(milestone) && this.pwa.canPromptToInstall) {
            setTimeout(() => this.promptInstall(), 2000); // 延迟2秒
        }
    }
}

// ❌ 避免：页面加载时立即提示
window.addEventListener('load', () => {
    pwa.show(); // 用户体验不佳
});
```

### 2. 平台特定优化
```javascript
// ✅ 推荐：根据平台优化体验
class PlatformOptimizedInstaller {
    getInstallMessage() {
        if (isIOS()) {
            return "将 Odoo 添加到主屏幕，享受原生应用体验";
        } else if (isAndroid()) {
            return "安装 Odoo 应用，获得更快的访问速度";
        } else {
            return "将 Odoo 安装到桌面，提升工作效率";
        }
    }
    
    getInstallBenefits() {
        const commonBenefits = ["更快启动", "离线访问"];
        
        if (isMobile()) {
            return [...commonBenefits, "全屏体验", "推送通知"];
        } else {
            return [...commonBenefits, "桌面快捷方式", "独立窗口"];
        }
    }
}
```

### 3. 用户反馈处理
```javascript
// ✅ 推荐：处理用户反馈和偏好
class UserPreferenceManager {
    constructor(pwaService) {
        this.pwa = pwaService;
        this.preferences = this.loadPreferences();
    }
    
    loadPreferences() {
        const stored = localStorage.getItem("pwa.userPreferences");
        return stored ? JSON.parse(stored) : {
            reminderFrequency: "weekly",
            preferredInstallTime: "after_engagement",
            showBenefits: true
        };
    }
    
    updatePreference(key, value) {
        this.preferences[key] = value;
        localStorage.setItem("pwa.userPreferences", JSON.stringify(this.preferences));
    }
    
    shouldShowReminder() {
        const lastReminder = localStorage.getItem("pwa.lastReminder");
        if (!lastReminder) return true;
        
        const intervals = {
            never: Infinity,
            weekly: 7 * 24 * 60 * 60 * 1000,
            monthly: 30 * 24 * 60 * 60 * 1000
        };
        
        const interval = intervals[this.preferences.reminderFrequency];
        return (Date.now() - parseInt(lastReminder)) > interval;
    }
}
```

### 4. 错误处理和降级
```javascript
// ✅ 推荐：提供完善的错误处理
class RobustPWAInstaller {
    async attemptInstallation() {
        try {
            if (!this.pwa.isSupportedOnBrowser) {
                this.showUnsupportedMessage();
                return;
            }
            
            if (!this.pwa.isAvailable) {
                this.showUnavailableMessage();
                return;
            }
            
            const result = await this.pwa.show();
            this.handleInstallResult(result);
            
        } catch (error) {
            console.error("PWA 安装失败:", error);
            this.showFallbackOptions();
        }
    }
    
    showUnsupportedMessage() {
        this.notification.add(
            "您的浏览器不支持应用安装，建议使用 Chrome 或 Safari",
            { type: "info", sticky: true }
        );
    }
    
    showFallbackOptions() {
        this.notification.add(
            "安装失败，您可以将此页面添加到书签以便快速访问",
            { 
                type: "warning",
                buttons: [
                    {
                        name: "添加书签",
                        onClick: () => this.showBookmarkInstructions()
                    }
                ]
            }
        );
    }
}
```

## ⚡ 性能优化

### 1. 懒加载和按需加载
```javascript
// 懒加载安装提示组件
const LazyInstallPrompt = lazy(() => import("./install_prompt"));

// 按需检查 PWA 状态
class LazyPWAChecker {
    constructor() {
        this.checked = false;
        this.checkPromise = null;
    }
    
    async checkPWASupport() {
        if (this.checked) return this.result;
        if (this.checkPromise) return this.checkPromise;
        
        this.checkPromise = this.performCheck();
        this.result = await this.checkPromise;
        this.checked = true;
        
        return this.result;
    }
    
    async performCheck() {
        // 延迟检查，避免阻塞主线程
        await new Promise(resolve => setTimeout(resolve, 100));
        
        return {
            supported: 'serviceWorker' in navigator,
            installable: 'BeforeInstallPromptEvent' in window,
            standalone: window.matchMedia('(display-mode: standalone)').matches
        };
    }
}
```

### 2. 缓存和存储优化
```javascript
// 优化本地存储使用
class OptimizedPWAStorage {
    constructor() {
        this.cache = new Map();
        this.storageKey = "pwa.optimizedData";
    }
    
    get(key) {
        if (this.cache.has(key)) {
            return this.cache.get(key);
        }
        
        const stored = localStorage.getItem(this.storageKey);
        if (stored) {
            const data = JSON.parse(stored);
            this.cache.set(key, data[key]);
            return data[key];
        }
        
        return null;
    }
    
    set(key, value) {
        this.cache.set(key, value);
        
        // 批量写入，减少 localStorage 操作
        this.debouncedSave();
    }
    
    debouncedSave = debounce(() => {
        const data = Object.fromEntries(this.cache);
        localStorage.setItem(this.storageKey, JSON.stringify(data));
    }, 1000);
}
```

## 🔍 调试技巧

### 1. PWA 状态调试
```javascript
// 开发环境下的 PWA 调试工具
if (odoo.debug) {
    window.pwaDebug = {
        getStatus: () => {
            const pwa = odoo.__DEBUG__.services.pwa;
            return {
                canPromptToInstall: pwa.canPromptToInstall,
                isAvailable: pwa.isAvailable,
                isSupportedOnBrowser: pwa.isSupportedOnBrowser,
                isScopedApp: pwa.isScopedApp,
                startUrl: pwa.startUrl
            };
        },
        
        forcePrompt: () => {
            const pwa = odoo.__DEBUG__.services.pwa;
            pwa.show();
        },
        
        resetState: () => {
            localStorage.removeItem("pwaService.installationState");
            location.reload();
        },
        
        simulateInstall: () => {
            const pwa = odoo.__DEBUG__.services.pwa;
            pwa._setInstallationState("accepted");
        }
    };
}
```

### 2. 安装流程监控
```javascript
class PWAInstallationMonitor {
    constructor() {
        this.events = [];
        this.startTime = Date.now();
    }
    
    logEvent(event, data = {}) {
        this.events.push({
            timestamp: Date.now() - this.startTime,
            event: event,
            data: data
        });
        
        if (odoo.debug) {
            console.log(`PWA Event: ${event}`, data);
        }
    }
    
    getInstallationReport() {
        return {
            duration: Date.now() - this.startTime,
            events: this.events,
            userAgent: navigator.userAgent,
            platform: navigator.platform
        };
    }
}
```

## 📖 相关资源

- [PWA 开发指南](https://web.dev/progressive-web-apps/)
- [Web App Manifest](https://developer.mozilla.org/en-US/docs/Web/Manifest)
- [Service Worker API](https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API)
- [BeforeInstallPromptEvent](https://developer.mozilla.org/en-US/docs/Web/API/BeforeInstallPromptEvent)

## 🎯 总结

Odoo PWA 模块是一个完整的渐进式 Web 应用解决方案，提供了：
- **跨平台支持**: 支持 Chrome、Safari 等主流浏览器的 PWA 功能
- **智能检测**: 自动检测浏览器支持和安装状态
- **用户体验**: 优化的安装流程和平台特定的用户指导
- **状态管理**: 完整的安装状态持久化和管理
- **作用域支持**: 支持多个独立的 PWA 实例
- **错误处理**: 完善的错误处理和降级方案

这个模块为 Odoo 提供了现代化的应用安装和管理能力，让用户能够像使用原生应用一样使用 Odoo，显著提升了用户体验和应用的可访问性。通过智能的安装提示和平台适配，确保了在不同设备和浏览器上的一致体验。

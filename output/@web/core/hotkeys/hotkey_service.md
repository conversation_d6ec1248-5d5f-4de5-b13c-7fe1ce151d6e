# Odoo 热键服务 (Hotkey Service) 学习资料

## 文件概述

**文件路径**: `output/@web/core/hotkeys/hotkey_service.js`  
**原始路径**: `/web/static/src/core/hotkeys/hotkey_service.js`  
**模块类型**: 核心基础模块 - 服务层  
**代码行数**: 485 行  
**依赖关系**: 
- `@web/core/browser/feature_detection` - 浏览器特性检测
- `@web/core/registry` - 服务注册表
- `@web/core/browser/browser` - 浏览器抽象层
- `@web/core/utils/ui` - UI 工具函数

## 模块功能

热键服务是 Odoo Web 客户端的核心服务之一，负责：
- 全局热键事件监听和分发
- 热键注册和注销管理
- 热键冲突解决和优先级处理
- 热键覆盖层显示
- 跨平台热键兼容性处理

## 核心类型定义

### 1. HotkeyCallback 类型
```javascript
/**
 * @typedef {(context: { area: HTMLElement, target: EventTarget }) => void} HotkeyCallback
 */
```
热键触发时的回调函数类型，接收上下文信息。

### 2. HotkeyOptions 类型
```javascript
/**
 * @typedef {Object} HotkeyOptions
 * @property {boolean} [allowRepeat] - 允许重复触发
 * @property {boolean} [bypassEditableProtection] - 绕过可编辑元素保护
 * @property {boolean} [global] - 全局热键
 * @property {() => HTMLElement} [area] - 限制作用区域
 * @property {() => boolean} [isAvailable] - 可用性检查
 * @property {() => HTMLElement} [withOverlay] - 覆盖层元素
 */
```

### 3. HotkeyRegistration 类型
```javascript
/**
 * @typedef {HotkeyOptions & {
 *  hotkey: string,
 *  callback: HotkeyCallback,
 *  activeElement: HTMLElement,
 * }} HotkeyRegistration
 */
```
完整的热键注册信息。

## 常量定义

### 1. 键盘按键分类
```javascript
const ALPHANUM_KEYS = "abcdefghijklmnopqrstuvwxyz0123456789".split("");
const NAV_KEYS = [
    "arrowleft", "arrowright", "arrowup", "arrowdown",
    "pageup", "pagedown", "home", "end", "backspace",
    "enter", "tab", "delete", "space"
];
const MODIFIERS = ["alt", "control", "shift"];
const AUTHORIZED_KEYS = [...ALPHANUM_KEYS, ...NAV_KEYS, "escape"];
```

**分析**:
- `ALPHANUM_KEYS`: 字母数字键
- `NAV_KEYS`: 导航和功能键
- `MODIFIERS`: 修饰键
- `AUTHORIZED_KEYS`: 所有允许的热键

## 核心函数分析

### 1. getActiveHotkey 函数

```javascript
function getActiveHotkey(ev) {
    if (!ev.key || ev.isComposing) return "";
    
    const hotkey = [];
    
    // 处理修饰键
    if (isMacOS() ? ev.ctrlKey : ev.altKey) {
        hotkey.push("alt");
    }
    if (isMacOS() ? ev.metaKey : ev.ctrlKey) {
        hotkey.push("control");
    }
    if (ev.shiftKey) {
        hotkey.push("shift");
    }
    
    // 处理主键
    let key = ev.key.toLowerCase();
    if (key === " ") key = "space";
    
    // 处理数字键
    if (ev.code && ev.code.indexOf("Digit") === 0) {
        key = ev.code.slice(-1);
    }
    
    // 处理非拉丁键盘布局
    if (!AUTHORIZED_KEYS.includes(key) && ev.code && ev.code.indexOf("Key") === 0) {
        key = ev.code.slice(-1).toLowerCase();
    }
    
    if (!MODIFIERS.includes(key)) {
        hotkey.push(key);
    }
    
    return hotkey.join("+");
}
```

**功能分析**:
- 跨平台修饰键处理 (macOS vs Windows/Linux)
- 键盘布局兼容性处理
- 输入法组合状态检测
- 标准化热键格式输出

## 服务架构分析

### 1. 服务定义结构
```javascript
const hotkeyService = {
    dependencies: ["ui"],
    overlayModifier: "alt",
    start(env, { ui }) {
        // 服务实现
    }
};
```

### 2. 核心状态管理
```javascript
const registrations = new Map();  // 热键注册表
let nextToken = 0;               // 注册令牌计数器
let overlaysVisible = false;     // 覆盖层显示状态
```

### 3. 事件监听器设置
```javascript
function addListeners(target) {
    target.addEventListener("keydown", onKeydown);
    target.addEventListener("keyup", removeHotkeyOverlays);
    target.addEventListener("blur", removeHotkeyOverlays);
    target.addEventListener("click", removeHotkeyOverlays);
}
```

## 热键分发机制

### 1. onKeydown 事件处理流程
```
键盘事件 → 热键解析 → 权限检查 → 分发处理 → 阻止默认行为
```

### 2. 分发优先级规则
1. **服务注册的热键** (通过 `hotkeyService.add()`)
   - 按注册时间倒序 (新注册的优先)
2. **DOM 属性注册的热键** (通过 `[data-hotkey]`)
   - 按 DOM 结构顺序

### 3. 候选筛选条件
```javascript
const candidates = allRegistrations.filter(reg =>
    reg.hotkey === hotkey &&                           // 热键匹配
    (reg.allowRepeat || !isRepeated) &&               // 重复检查
    (reg.bypassEditableProtection || !shouldProtectEditable) && // 可编辑保护
    (reg.global || reg.activeElement === activeElement) &&      // 作用域检查
    (!reg.isAvailable || reg.isAvailable()) &&        // 可用性检查
    (!reg.area || (target && reg.area() && reg.area().contains(target))) // 区域检查
);
```

## 覆盖层系统

### 1. 覆盖层触发
- 按下 `alt` 键时显示所有可用热键
- 自动收集 DOM 和服务注册的热键
- 动态生成视觉提示

### 2. 覆盖层实现
```javascript
function addHotkeyOverlays(activeElement) {
    // 收集钩子注册的热键
    const hotkeysFromHookToHighlight = [];
    for (const [, registration] of registrations) {
        const overlayElement = registration.withOverlay?.();
        if (overlayElement) {
            hotkeysFromHookToHighlight.push({
                hotkey: registration.hotkey.replace(`${hotkeyService.overlayModifier}+`, ""),
                el: overlayElement
            });
        }
    }
    
    // 收集 DOM 注册的热键
    const hotkeysFromDomToHighlight = getVisibleElements(
        activeElement,
        "[data-hotkey]:not(:disabled)"
    ).map(el => ({ hotkey: el.dataset.hotkey, el }));
    
    // 创建覆盖层元素
    const items = [...hotkeysFromDomToHighlight, ...hotkeysFromHookToHighlight];
    for (const item of items) {
        // 创建覆盖层 DOM 元素
    }
}
```

## API 接口

### 1. add 方法
```javascript
add(hotkey, callback, options = {}) {
    const token = registerHotkey(hotkey, callback, options);
    return () => {
        unregisterHotkey(token);
    };
}
```
**功能**: 注册热键并返回注销函数

### 2. registerIframe 方法
```javascript
registerIframe(iframe) {
    addListeners(iframe.contentWindow);
}
```
**功能**: 为 iframe 注册热键监听器

## 安全和保护机制

### 1. 可编辑元素保护
```javascript
const targetIsEditable = 
    event.target instanceof HTMLElement &&
    (/input|textarea/i.test(event.target.tagName) || event.target.isContentEditable) &&
    !event.target.matches("input[type=checkbox], input[type=radio]");

const shouldProtectEditable = 
    targetIsEditable && 
    !event.target.dataset.allowHotkeys && 
    singleKey !== "escape";
```

### 2. UI 阻塞检查
```javascript
if (isBlocked) {
    return; // 不处理热键
}
```

### 3. 键盘布局兼容性
- 支持非拉丁键盘布局
- 处理输入法组合状态
- 跨平台修饰键映射

## 使用示例

### 1. 基本注册
```javascript
const hotkeyService = env.services.hotkey;
const unregister = hotkeyService.add("control+s", ({ target, area }) => {
    console.log("保存操作", { target, area });
});

// 清理
unregister();
```

### 2. 高级选项
```javascript
hotkeyService.add("alt+h", callback, {
    global: true,                    // 全局热键
    allowRepeat: false,             // 不允许重复
    bypassEditableProtection: true, // 绕过可编辑保护
    area: () => document.querySelector('.my-area'), // 限制区域
    isAvailable: () => this.canShowHelp(),         // 可用性检查
    withOverlay: () => this.helpButton             // 覆盖层元素
});
```

## 最佳实践

### 1. 热键命名规范
- 使用小写字母
- 修饰键顺序: alt → control → shift
- 使用标准键名 (space, escape, enter 等)

### 2. 性能优化
- 避免频繁注册/注销
- 合理使用 `area` 限制作用域
- 实现 `isAvailable` 检查避免无效调用

### 3. 用户体验
- 提供热键覆盖层提示
- 避免与浏览器默认热键冲突
- 考虑不同平台的习惯差异

## 深入技术分析

### 1. 注册令牌机制
```javascript
function registerHotkey(hotkey, callback, options = {}) {
    const token = nextToken++;
    const registration = {
        hotkey: hotkey.toLowerCase(),
        callback,
        activeElement: null,
        // ... 其他选项
    };

    // 异步设置 activeElement
    Promise.resolve().then(() => {
        registration.activeElement = ui.activeElement;
    });

    registrations.set(token, registration);
    return token;
}
```

**设计亮点**:
- **令牌机制**: 使用数字令牌管理注册，避免内存泄漏
- **异步上下文**: 使用 Promise.resolve() 确保正确的 activeElement
- **不可变性**: 注册后的热键字符串不可修改

### 2. DOM 热键集成
```javascript
function getDomRegistrations(hotkey, activeElement) {
    const overlayModParts = hotkeyService.overlayModifier.split("+");
    if (!overlayModParts.every(el => hotkey.includes(el))) {
        return [];
    }

    const cleanHotkey = hotkey
        .split("+")
        .filter(key => !overlayModParts.includes(key))
        .join("+");

    const elems = getVisibleElements(activeElement, `[data-hotkey='${cleanHotkey}' i]`);
    return elems.map(el => ({
        hotkey,
        activeElement,
        bypassEditableProtection: true,
        callback: () => {
            el.focus();
            el.click();
        }
    }));
}
```

**功能分析**:
- **覆盖层修饰键处理**: 自动处理 alt 修饰键
- **可见性检查**: 只处理可见的 DOM 元素
- **自动交互**: 自动 focus 和 click 目标元素

### 3. 区域优先级算法
```javascript
let winner = candidates.shift();
if (winner && winner.area) {
    // 找到最接近的区域
    for (const candidate of candidates.filter(c => Boolean(c.area))) {
        if (candidate.area() && winner.area().contains(candidate.area())) {
            winner = candidate;
        }
    }
}
```

**算法逻辑**:
- 优先选择第一个候选者
- 如果有区域限制，选择最内层的区域
- 实现了热键的就近原则

### 4. 跨平台修饰键映射
```javascript
// macOS 特殊处理
if (isMacOS() ? ev.ctrlKey : ev.altKey) {
    hotkey.push("alt");
}
if (isMacOS() ? ev.metaKey : ev.ctrlKey) {
    hotkey.push("control");
}
```

**平台差异处理**:
- **macOS**: Cmd 键映射为 control，Ctrl 键映射为 alt
- **Windows/Linux**: 标准映射
- **统一接口**: 开发者使用统一的热键字符串

## 错误处理和验证

### 1. 热键格式验证
```javascript
const keys = hotkey
    .toLowerCase()
    .split("+")
    .filter(k => !MODIFIERS.includes(k));

if (keys.some(k => !AUTHORIZED_KEYS.includes(k))) {
    throw new Error(`热键包含未授权的按键: ${keys.join(", ")}`);
}

if (keys.length > 1) {
    throw new Error(`热键包含多个非修饰键: ${keys.join("+")}`);
}
```

### 2. 回调函数验证
```javascript
if (!callback || typeof callback !== "function") {
    throw new Error("必须提供回调函数");
}
```

### 3. 特殊情况处理
```javascript
// Chrome 自动完成问题
if (!ev.key) {
    return "";
}

// 输入法组合状态
if (ev.isComposing) {
    return "";
}

// 数字键盘忽略
if (event.code && event.code.indexOf("Numpad") === 0 && /^\d$/.test(event.key)) {
    return;
}
```

## 性能优化策略

### 1. 事件委托
- 在 browser 对象上统一监听
- 避免为每个组件单独绑定事件

### 2. 延迟计算
- activeElement 异步设置
- 覆盖层按需创建

### 3. 缓存机制
- 注册表使用 Map 结构
- 可见元素查询结果缓存

## 扩展性设计

### 1. 插件化架构
```javascript
// 支持 iframe 扩展
registerIframe(iframe) {
    addListeners(iframe.contentWindow);
}
```

### 2. 配置化选项
- 覆盖层修饰键可配置
- 保护机制可绕过
- 作用域可限制

### 3. 钩子系统集成
- 与 `useHotkey` 钩子无缝集成
- 支持组件生命周期管理

## 调试和监控

### 1. 开发者工具支持
```javascript
// 在控制台中查看注册表
console.log(env.services.hotkey.registrations);
```

### 2. 热键冲突检测
- 运行时检测重复注册
- 优先级规则清晰可见

### 3. 性能监控
- 事件处理时间统计
- 注册数量监控

## 安全考虑

### 1. XSS 防护
- DOM 热键属性过滤
- 回调函数沙箱执行

### 2. 权限控制
- 可编辑元素保护
- UI 阻塞状态检查

### 3. 资源限制
- 注册数量限制
- 内存使用监控

## 总结

热键服务是 Odoo Web 客户端的核心基础设施，它提供了：
- **完整的生命周期管理**: 注册、分发、清理
- **跨平台兼容性**: macOS、Windows、Linux 统一支持
- **灵活的配置机制**: 丰富的选项和扩展点
- **用户友好的体验**: 覆盖层提示和智能保护
- **高性能架构**: 事件委托和延迟计算
- **安全可靠**: 完善的验证和错误处理

这个服务为整个 Odoo Web 应用提供了统一、可靠、高效的热键处理能力，是现代 Web 应用架构的典型代表。

# @web/core/orm_service.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/orm_service.js`
- **原始路径**: `/web/static/src/core/orm_service.js`
- **代码行数**: 341行
- **作用**: Odoo Web框架的数据访问层，提供与后端ORM交互的标准接口

## 🎯 学习目标
通过学习这个文件，您将掌握：
- ORM服务的完整API和使用方法
- x2Many关系字段的命令操作
- 数据验证和错误处理机制
- 静默模式和上下文管理
- 前后端数据交互的最佳实践

## 📚 核心概念

### 什么是ORM Service？
ORM Service是Odoo Web框架中的**数据访问层**，主要功能：
- **统一接口**: 为前端提供统一的数据操作API
- **类型安全**: 严格的参数验证和类型检查
- **上下文管理**: 自动处理用户上下文和权限
- **错误处理**: 统一的错误处理和静默模式

### 基本使用模式
```javascript
// 在组件中使用ORM服务
const orm = useService("orm");

// 读取记录
const partners = await orm.searchRead("res.partner", [["is_company", "=", true]], ["name", "email"]);

// 创建记录
const newId = await orm.create("res.partner", [{ name: "New Partner", email: "<EMAIL>" }]);

// 更新记录
await orm.write("res.partner", [newId], { phone: "+1234567890" });

// 删除记录
await orm.unlink("res.partner", [newId]);
```

## 🏗️ 服务架构分析

### 服务定义
```javascript
const ormService = {
    async: [                    // 声明异步方法
        "call", "create", "read", "searchRead", 
        "write", "unlink", // ... 其他方法
    ],
    start() {
        return new ORM();       // 返回ORM实例
    }
};
```

### ORM类结构
```javascript
class ORM {
    constructor() {
        this.rpc = rpc;         // RPC调用函数
        this._silent = false;   // 静默模式标志
    }
    
    get silent() {              // 静默模式访问器
        return Object.assign(Object.create(this), { _silent: true });
    }
}
```

**关键特性**：
- **原型继承**: 使用Object.create创建静默模式实例
- **RPC封装**: 所有数据操作都通过RPC调用
- **上下文合并**: 自动合并用户上下文

## 🔍 核心方法详解

### 1. call() - 通用方法调用
```javascript
call(model, method, args = [], kwargs = {}) {
    validateModel(model);
    const url = `/web/dataset/call_kw/${model}/${method}`;
    const fullContext = Object.assign({}, user.context, kwargs.context || {});
    const fullKwargs = Object.assign({}, kwargs, { context: fullContext });
    const params = { model, method, args, kwargs: fullKwargs };
    return this.rpc(url, params, { silent: this._silent });
}
```

**工作流程**：
1. **模型验证**: 检查模型名称有效性
2. **URL构建**: 构建RPC调用URL
3. **上下文合并**: 合并用户上下文和传入上下文
4. **参数封装**: 封装调用参数
5. **RPC调用**: 执行远程过程调用

### 2. CRUD操作方法

#### create() - 创建记录
```javascript
create(model, records, kwargs = {}) {
    validateArray("records", records);
    for (const record of records) {
        validateObject("record", record);
    }
    return this.call(model, "create", [records], kwargs);
}
```

**使用示例**：
```javascript
// 创建单个记录
const partnerId = await orm.create("res.partner", [{ 
    name: "John Doe", 
    email: "<EMAIL>" 
}]);

// 批量创建记录
const partnerIds = await orm.create("res.partner", [
    { name: "Partner 1", email: "<EMAIL>" },
    { name: "Partner 2", email: "<EMAIL>" }
]);
```

#### read() - 读取记录
```javascript
read(model, ids, fields, kwargs = {}) {
    validatePrimitiveList("ids", "number", ids);
    if (fields) {
        validatePrimitiveList("fields", "string", fields);
    }
    if (!ids.length) {
        return Promise.resolve([]);
    }
    return this.call(model, "read", [ids, fields], kwargs);
}
```

**使用示例**：
```javascript
// 读取指定字段
const partners = await orm.read("res.partner", [1, 2, 3], ["name", "email"]);

// 读取所有字段
const allFields = await orm.read("res.partner", [1]);
```

#### write() - 更新记录
```javascript
write(model, ids, data, kwargs = {}) {
    validatePrimitiveList("ids", "number", ids);
    validateObject("data", data);
    return this.call(model, "write", [ids, data], kwargs);
}
```

#### unlink() - 删除记录
```javascript
unlink(model, ids, kwargs = {}) {
    validatePrimitiveList("ids", "number", ids);
    if (!ids.length) {
        return Promise.resolve(true);
    }
    return this.call(model, "unlink", [ids], kwargs);
}
```

### 3. 搜索方法

#### search() - 搜索记录ID
```javascript
search(model, domain, kwargs = {}) {
    validateArray("domain", domain);
    return this.call(model, "search", [domain], kwargs);
}
```

#### searchRead() - 搜索并读取记录
```javascript
searchRead(model, domain, fields, kwargs = {}) {
    validateArray("domain", domain);
    if (fields) {
        validatePrimitiveList("fields", "string", fields);
    }
    return this.call(model, "search_read", [], { ...kwargs, domain, fields });
}
```

**使用示例**：
```javascript
// 搜索公司类型的合作伙伴
const companies = await orm.searchRead(
    "res.partner", 
    [["is_company", "=", true]], 
    ["name", "email", "phone"]
);

// 带分页的搜索
const pagedResults = await orm.searchRead(
    "res.partner", 
    [], 
    ["name"], 
    { limit: 10, offset: 20 }
);
```

#### searchCount() - 计数搜索结果
```javascript
searchCount(model, domain, kwargs = {}) {
    validateArray("domain", domain);
    return this.call(model, "search_count", [domain], kwargs);
}
```

### 4. Web专用方法

#### webSearchRead() - Web搜索读取
```javascript
webSearchRead(model, domain, kwargs = {}) {
    validateArray("domain", domain);
    return this.call(model, "web_search_read", [], { ...kwargs, domain });
}
```

**与searchRead的区别**：
- **specification**: 支持字段规格说明
- **context**: 更好的上下文处理
- **性能优化**: 针对Web界面优化

#### webRead() - Web读取
```javascript
webRead(model, ids, kwargs = {}) {
    validatePrimitiveList("ids", "number", ids);
    return this.call(model, "web_read", [ids], kwargs);
}
```

#### webSave() - Web保存
```javascript
webSave(model, ids, data, kwargs = {}) {
    validatePrimitiveList("ids", "number", ids);
    validateObject("data", data);
    return this.call(model, "web_save", [ids, data], kwargs);
}
```

## 🔗 x2Many关系命令

### x2ManyCommands对象
```javascript
const x2ManyCommands = {
    CREATE: 0,    // 创建新记录
    UPDATE: 1,    // 更新现有记录
    DELETE: 2,    // 删除记录
    UNLINK: 3,    // 取消关联（不删除记录）
    LINK: 4,      // 建立关联
    CLEAR: 5,     // 清除所有关联
    SET: 6,       // 设置关联列表
};
```

### 命令使用示例
```javascript
// 创建新的关联记录
const createCommand = x2ManyCommands.create(false, {
    name: "New Line",
    quantity: 1
});

// 更新现有关联记录
const updateCommand = x2ManyCommands.update(lineId, {
    quantity: 2
});

// 删除关联记录
const deleteCommand = x2ManyCommands.delete(lineId);

// 取消关联（保留记录）
const unlinkCommand = x2ManyCommands.unlink(lineId);

// 建立新关联
const linkCommand = x2ManyCommands.link(existingLineId);

// 清除所有关联
const clearCommand = x2ManyCommands.clear();

// 设置完整的关联列表
const setCommand = x2ManyCommands.set([id1, id2, id3]);

// 在写入操作中使用
await orm.write("sale.order", [orderId], {
    order_line: [
        createCommand,
        updateCommand,
        deleteCommand
    ]
});
```

## 🎨 实际应用场景

### 1. 在表单视图中的使用
```javascript
class FormController extends Component {
    setup() {
        this.orm = useService("orm");
    }
    
    async saveRecord() {
        const recordData = this.getFormData();
        
        if (this.props.resId) {
            // 更新现有记录
            await this.orm.write(this.props.resModel, [this.props.resId], recordData);
        } else {
            // 创建新记录
            const newId = await this.orm.create(this.props.resModel, [recordData]);
            this.props.resId = newId;
        }
    }
    
    async deleteRecord() {
        if (this.props.resId) {
            await this.orm.unlink(this.props.resModel, [this.props.resId]);
            // 导航到列表视图
        }
    }
}
```

### 2. 在列表视图中的使用
```javascript
class ListController extends Component {
    setup() {
        this.orm = useService("orm");
        this.loadRecords();
    }
    
    async loadRecords() {
        const domain = this.getDomain();
        const fields = this.getFields();
        
        this.records = await this.orm.searchRead(
            this.props.resModel,
            domain,
            fields,
            {
                limit: this.props.limit,
                offset: this.props.offset,
                order: this.props.orderBy
            }
        );
    }
    
    async deleteSelectedRecords(selectedIds) {
        await this.orm.unlink(this.props.resModel, selectedIds);
        await this.loadRecords(); // 重新加载
    }
}
```

### 3. 在搜索组件中的使用
```javascript
class SearchComponent extends Component {
    setup() {
        this.orm = useService("orm");
    }
    
    async searchRecords(searchTerm) {
        const domain = [
            "|",
            ["name", "ilike", searchTerm],
            ["email", "ilike", searchTerm]
        ];
        
        const results = await this.orm.searchRead(
            "res.partner",
            domain,
            ["name", "email"],
            { limit: 10 }
        );
        
        return results;
    }
    
    async getRecordCount(domain) {
        return await this.orm.searchCount("res.partner", domain);
    }
}
```

## 🛠️ 实践练习

### 练习1: 自定义ORM扩展
```javascript
// 扩展ORM类，添加缓存功能
class CachedORM extends ORM {
    constructor() {
        super();
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5分钟
    }
    
    async read(model, ids, fields, kwargs = {}) {
        const cacheKey = `${model}:${ids.join(',')}:${fields?.join(',')}`;
        const cached = this.cache.get(cacheKey);
        
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            console.log('Cache hit:', cacheKey);
            return cached.data;
        }
        
        const result = await super.read(model, ids, fields, kwargs);
        
        this.cache.set(cacheKey, {
            data: result,
            timestamp: Date.now()
        });
        
        return result;
    }
    
    clearCache(model = null) {
        if (model) {
            for (const key of this.cache.keys()) {
                if (key.startsWith(`${model}:`)) {
                    this.cache.delete(key);
                }
            }
        } else {
            this.cache.clear();
        }
    }
}
```

### 练习2: 批量操作工具
```javascript
// 批量操作工具类
class BatchORM {
    constructor(orm) {
        this.orm = orm;
        this.batchSize = 100;
    }
    
    async batchCreate(model, records) {
        const results = [];
        
        for (let i = 0; i < records.length; i += this.batchSize) {
            const batch = records.slice(i, i + this.batchSize);
            const batchResults = await this.orm.create(model, batch);
            results.push(...batchResults);
        }
        
        return results;
    }
    
    async batchUpdate(model, updates) {
        // updates: [{ ids: [1,2,3], data: {...} }, ...]
        const promises = updates.map(update => 
            this.orm.write(model, update.ids, update.data)
        );
        
        return Promise.all(promises);
    }
    
    async batchDelete(model, ids) {
        const results = [];
        
        for (let i = 0; i < ids.length; i += this.batchSize) {
            const batch = ids.slice(i, i + this.batchSize);
            const result = await this.orm.unlink(model, batch);
            results.push(result);
        }
        
        return results.every(r => r);
    }
}
```

### 练习3: ORM操作监控器
```javascript
// ORM操作监控器
class ORMMonitor {
    constructor(orm) {
        this.orm = orm;
        this.stats = {
            calls: 0,
            errors: 0,
            totalTime: 0,
            operations: new Map()
        };
        this.wrapMethods();
    }
    
    wrapMethods() {
        const methods = ['call', 'create', 'read', 'write', 'unlink', 'searchRead'];
        
        methods.forEach(method => {
            const original = this.orm[method];
            
            this.orm[method] = async (...args) => {
                const startTime = performance.now();
                this.stats.calls++;
                
                try {
                    const result = await original.apply(this.orm, args);
                    this.recordSuccess(method, performance.now() - startTime);
                    return result;
                } catch (error) {
                    this.recordError(method, error);
                    throw error;
                }
            };
        });
    }
    
    recordSuccess(method, duration) {
        this.stats.totalTime += duration;
        
        if (!this.stats.operations.has(method)) {
            this.stats.operations.set(method, { count: 0, totalTime: 0, errors: 0 });
        }
        
        const op = this.stats.operations.get(method);
        op.count++;
        op.totalTime += duration;
    }
    
    recordError(method, error) {
        this.stats.errors++;
        
        if (!this.stats.operations.has(method)) {
            this.stats.operations.set(method, { count: 0, totalTime: 0, errors: 0 });
        }
        
        this.stats.operations.get(method).errors++;
    }
    
    getReport() {
        const avgTime = this.stats.totalTime / this.stats.calls;
        const errorRate = (this.stats.errors / this.stats.calls) * 100;
        
        return {
            summary: {
                totalCalls: this.stats.calls,
                totalErrors: this.stats.errors,
                errorRate: errorRate.toFixed(2) + '%',
                averageTime: avgTime.toFixed(2) + 'ms'
            },
            operations: Object.fromEntries(
                Array.from(this.stats.operations.entries()).map(([method, stats]) => [
                    method,
                    {
                        ...stats,
                        averageTime: (stats.totalTime / stats.count).toFixed(2) + 'ms',
                        errorRate: ((stats.errors / stats.count) * 100).toFixed(2) + '%'
                    }
                ])
            )
        };
    }
}
```

## 🔧 调试技巧

### 查看ORM调用
```javascript
// 监控所有ORM调用
const originalCall = env.services.orm.call;
env.services.orm.call = function(model, method, args, kwargs) {
    console.log(`ORM Call: ${model}.${method}`, { args, kwargs });
    return originalCall.apply(this, arguments);
};
```

### 静默模式使用
```javascript
// 使用静默模式避免显示加载指示器
const result = await orm.silent.searchRead("res.partner", [], ["name"]);

// 或者在批量操作中使用
for (const batch of batches) {
    await orm.silent.create("res.partner", batch);
}
```

## 📊 性能考虑

### 优化策略
1. **批量操作**: 尽量使用批量创建、更新、删除
2. **字段选择**: 只读取需要的字段
3. **分页查询**: 使用limit和offset进行分页
4. **静默模式**: 在后台操作中使用静默模式

### 最佳实践
```javascript
// ✅ 好的做法：批量操作
const records = await orm.create("res.partner", multipleRecords);

// ❌ 不好的做法：循环单个操作
for (const record of multipleRecords) {
    await orm.create("res.partner", [record]);
}

// ✅ 好的做法：选择性字段读取
const partners = await orm.read("res.partner", ids, ["name", "email"]);

// ❌ 不好的做法：读取所有字段
const partners = await orm.read("res.partner", ids);
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解ORM服务的完整API和使用方法
- [ ] 掌握CRUD操作的最佳实践
- [ ] 熟练使用x2Many关系命令
- [ ] 理解静默模式和上下文管理
- [ ] 能够进行性能优化和错误处理
- [ ] 掌握ORM服务的扩展和监控方法

## 🚀 下一步学习
学完ORM服务后，建议继续学习：
1. **RPC服务** (`@web/core/network/rpc.js`) - 理解底层网络通信
2. **用户服务** (`@web/core/user.js`) - 掌握用户上下文管理
3. **视图模型** (`@web/views/`) - 学习数据模型在视图中的应用

## 💡 重要提示
- ORM服务是前后端数据交互的核心
- 理解x2Many命令对关系字段操作至关重要
- 性能优化需要考虑批量操作和字段选择
- 静默模式在后台操作中非常有用

## 🔍 深入理解：ORM服务的设计精髓

### 为什么需要ORM服务？
```javascript
// 问题：直接RPC调用的复杂性
await rpc('/web/dataset/call_kw/res.partner/search_read', {
    model: 'res.partner',
    method: 'search_read',
    args: [],
    kwargs: {
        domain: [['is_company', '=', true]],
        fields: ['name', 'email'],
        context: { ...user.context, lang: 'en_US' }
    }
});

// 解决：ORM服务的简洁API
await orm.searchRead('res.partner', [['is_company', '=', true]], ['name', 'email']);
```

**ORM服务的价值**：
- **简化API**: 复杂的RPC调用 → 简洁的方法调用
- **类型安全**: 自动参数验证和类型检查
- **上下文管理**: 自动处理用户上下文和权限
- **错误统一**: 统一的错误处理和用户反馈

### 静默模式的巧妙设计
```javascript
// 静默模式的实现原理
get silent() {
    return Object.assign(Object.create(this), { _silent: true });
}
```

**设计分析**：
- **原型继承**: 使用Object.create避免修改原实例
- **属性覆盖**: 只覆盖_silent属性，其他方法保持不变
- **链式调用**: 支持 `orm.silent.searchRead()` 的流畅API

### x2Many命令的设计哲学
```javascript
// 传统方式：复杂的关系操作
const orderLines = await orm.read('sale.order.line', lineIds, ['product_id', 'quantity']);
// 修改数据...
await orm.write('sale.order.line', [lineId], updatedData);
await orm.create('sale.order.line', [newLineData]);
await orm.unlink('sale.order.line', [deletedLineId]);

// x2Many命令：声明式操作
await orm.write('sale.order', [orderId], {
    order_line: [
        x2ManyCommands.update(lineId, updatedData),
        x2ManyCommands.create(false, newLineData),
        x2ManyCommands.delete(deletedLineId)
    ]
});
```

**优势对比**：
- **原子性**: 所有操作在一个事务中完成
- **一致性**: 避免中间状态的数据不一致
- **性能**: 减少网络往返次数
- **简洁性**: 声明式的操作描述

## 🎓 高级应用模式

### 1. 事务性ORM包装器
```javascript
class TransactionalORM {
    constructor(orm) {
        this.orm = orm;
        this.operations = [];
        this.inTransaction = false;
    }

    beginTransaction() {
        this.inTransaction = true;
        this.operations = [];
    }

    async commit() {
        if (!this.inTransaction) {
            throw new Error('No active transaction');
        }

        try {
            // 按模型分组操作
            const groupedOps = this.groupOperationsByModel();

            // 按依赖顺序执行
            for (const [model, ops] of groupedOps) {
                await this.executeOperations(model, ops);
            }

            this.inTransaction = false;
            this.operations = [];
        } catch (error) {
            await this.rollback();
            throw error;
        }
    }

    async rollback() {
        // 实现回滚逻辑
        this.inTransaction = false;
        this.operations = [];
    }

    create(model, records, kwargs = {}) {
        if (this.inTransaction) {
            this.operations.push({ type: 'create', model, records, kwargs });
            return Promise.resolve(null); // 返回占位符
        }
        return this.orm.create(model, records, kwargs);
    }

    write(model, ids, data, kwargs = {}) {
        if (this.inTransaction) {
            this.operations.push({ type: 'write', model, ids, data, kwargs });
            return Promise.resolve(true);
        }
        return this.orm.write(model, ids, data, kwargs);
    }

    unlink(model, ids, kwargs = {}) {
        if (this.inTransaction) {
            this.operations.push({ type: 'unlink', model, ids, kwargs });
            return Promise.resolve(true);
        }
        return this.orm.unlink(model, ids, kwargs);
    }
}

// 使用示例
const txOrm = new TransactionalORM(orm);

txOrm.beginTransaction();
try {
    await txOrm.create('res.partner', [{ name: 'Partner 1' }]);
    await txOrm.create('sale.order', [{ partner_id: partnerId }]);
    await txOrm.commit();
} catch (error) {
    await txOrm.rollback();
    throw error;
}
```

### 2. 智能缓存ORM
```javascript
class SmartCachedORM {
    constructor(orm) {
        this.orm = orm;
        this.cache = new Map();
        this.dependencies = new Map(); // 模型依赖关系
        this.invalidationRules = new Map(); // 失效规则
    }

    // 注册模型依赖关系
    registerDependency(model, dependsOn) {
        if (!this.dependencies.has(model)) {
            this.dependencies.set(model, new Set());
        }
        this.dependencies.get(model).add(dependsOn);
    }

    // 注册缓存失效规则
    registerInvalidationRule(model, rule) {
        this.invalidationRules.set(model, rule);
    }

    async read(model, ids, fields, kwargs = {}) {
        const cacheKey = this.generateCacheKey(model, ids, fields, kwargs);

        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (this.isCacheValid(cached)) {
                return cached.data;
            }
        }

        const result = await this.orm.read(model, ids, fields, kwargs);

        this.cache.set(cacheKey, {
            data: result,
            timestamp: Date.now(),
            model,
            ids,
            fields
        });

        return result;
    }

    async write(model, ids, data, kwargs = {}) {
        const result = await this.orm.write(model, ids, data, kwargs);

        // 智能缓存失效
        this.invalidateCache(model, ids, data);

        return result;
    }

    invalidateCache(model, ids, data = null) {
        // 失效直接相关的缓存
        for (const [key, cached] of this.cache.entries()) {
            if (cached.model === model &&
                cached.ids.some(id => ids.includes(id))) {
                this.cache.delete(key);
            }
        }

        // 失效依赖模型的缓存
        const dependents = this.findDependentModels(model);
        for (const dependent of dependents) {
            this.invalidateModelCache(dependent);
        }

        // 应用自定义失效规则
        const rule = this.invalidationRules.get(model);
        if (rule) {
            rule(this.cache, ids, data);
        }
    }

    findDependentModels(model) {
        const dependents = [];
        for (const [depModel, deps] of this.dependencies.entries()) {
            if (deps.has(model)) {
                dependents.push(depModel);
            }
        }
        return dependents;
    }
}

// 配置示例
const smartOrm = new SmartCachedORM(orm);

// 配置依赖关系
smartOrm.registerDependency('sale.order.line', 'sale.order');
smartOrm.registerDependency('sale.order.line', 'product.product');

// 配置失效规则
smartOrm.registerInvalidationRule('res.partner', (cache, ids, data) => {
    if (data && 'name' in data) {
        // 如果合作伙伴名称改变，失效所有相关的显示名称缓存
        for (const [key, cached] of cache.entries()) {
            if (key.includes('display_name')) {
                cache.delete(key);
            }
        }
    }
});
```

### 3. ORM查询构建器
```javascript
class ORMQueryBuilder {
    constructor(orm, model) {
        this.orm = orm;
        this.model = model;
        this.domain = [];
        this.fields = null;
        this.orderBy = null;
        this.limitValue = null;
        this.offsetValue = null;
    }

    where(field, operator, value) {
        this.domain.push([field, operator, value]);
        return this;
    }

    whereIn(field, values) {
        this.domain.push([field, 'in', values]);
        return this;
    }

    whereLike(field, pattern) {
        this.domain.push([field, 'ilike', pattern]);
        return this;
    }

    orWhere(conditions) {
        if (conditions.length > 0) {
            const orDomain = ['|'].concat(conditions);
            this.domain.push(orDomain);
        }
        return this;
    }

    select(fields) {
        this.fields = Array.isArray(fields) ? fields : [fields];
        return this;
    }

    orderBy(field, direction = 'asc') {
        this.orderBy = direction === 'desc' ? `${field} desc` : field;
        return this;
    }

    limit(count) {
        this.limitValue = count;
        return this;
    }

    offset(count) {
        this.offsetValue = count;
        return this;
    }

    async get() {
        const kwargs = {};
        if (this.orderBy) kwargs.order = this.orderBy;
        if (this.limitValue) kwargs.limit = this.limitValue;
        if (this.offsetValue) kwargs.offset = this.offsetValue;

        return this.orm.searchRead(this.model, this.domain, this.fields, kwargs);
    }

    async count() {
        return this.orm.searchCount(this.model, this.domain);
    }

    async first() {
        const results = await this.limit(1).get();
        return results.length > 0 ? results[0] : null;
    }

    async exists() {
        const count = await this.count();
        return count > 0;
    }
}

// 扩展ORM类
class QueryableORM extends ORM {
    query(model) {
        return new ORMQueryBuilder(this, model);
    }
}

// 使用示例
const queryableOrm = new QueryableORM();

// 流畅的查询API
const activePartners = await queryableOrm
    .query('res.partner')
    .where('active', '=', true)
    .where('is_company', '=', true)
    .whereLike('name', '%Tech%')
    .select(['name', 'email', 'phone'])
    .orderBy('name')
    .limit(10)
    .get();

// 复杂查询
const orders = await queryableOrm
    .query('sale.order')
    .where('state', 'in', ['sale', 'done'])
    .where('date_order', '>=', '2024-01-01')
    .orWhere([
        ['amount_total', '>', 1000],
        ['partner_id.is_company', '=', true]
    ])
    .select(['name', 'partner_id', 'amount_total', 'state'])
    .orderBy('date_order', 'desc')
    .get();
```

## 🔧 调试和性能分析工具

### ORM性能分析器
```javascript
class ORMPerformanceAnalyzer {
    constructor() {
        this.queries = [];
        this.slowQueryThreshold = 1000; // 1秒
    }

    wrapORM(orm) {
        const analyzer = this;
        const methods = ['call', 'create', 'read', 'write', 'unlink', 'searchRead'];

        methods.forEach(method => {
            const original = orm[method];
            orm[method] = async function(...args) {
                const startTime = performance.now();
                const queryInfo = {
                    method,
                    args: args.slice(0, 2), // 只记录model和主要参数
                    startTime,
                    endTime: null,
                    duration: null,
                    error: null
                };

                try {
                    const result = await original.apply(this, args);
                    queryInfo.endTime = performance.now();
                    queryInfo.duration = queryInfo.endTime - startTime;

                    analyzer.recordQuery(queryInfo);

                    if (queryInfo.duration > analyzer.slowQueryThreshold) {
                        console.warn('Slow ORM query detected:', queryInfo);
                    }

                    return result;
                } catch (error) {
                    queryInfo.error = error.message;
                    queryInfo.endTime = performance.now();
                    queryInfo.duration = queryInfo.endTime - startTime;

                    analyzer.recordQuery(queryInfo);
                    throw error;
                }
            };
        });

        return orm;
    }

    recordQuery(queryInfo) {
        this.queries.push(queryInfo);

        // 保持最近1000个查询
        if (this.queries.length > 1000) {
            this.queries = this.queries.slice(-1000);
        }
    }

    generateReport() {
        const totalQueries = this.queries.length;
        const successfulQueries = this.queries.filter(q => !q.error);
        const failedQueries = this.queries.filter(q => q.error);
        const slowQueries = this.queries.filter(q => q.duration > this.slowQueryThreshold);

        const avgDuration = successfulQueries.reduce((sum, q) => sum + q.duration, 0) / successfulQueries.length;

        const methodStats = {};
        this.queries.forEach(query => {
            if (!methodStats[query.method]) {
                methodStats[query.method] = { count: 0, totalTime: 0, errors: 0 };
            }
            methodStats[query.method].count++;
            methodStats[query.method].totalTime += query.duration;
            if (query.error) methodStats[query.method].errors++;
        });

        return {
            summary: {
                totalQueries,
                successfulQueries: successfulQueries.length,
                failedQueries: failedQueries.length,
                slowQueries: slowQueries.length,
                averageDuration: avgDuration.toFixed(2) + 'ms',
                successRate: ((successfulQueries.length / totalQueries) * 100).toFixed(2) + '%'
            },
            methodBreakdown: Object.fromEntries(
                Object.entries(methodStats).map(([method, stats]) => [
                    method,
                    {
                        ...stats,
                        averageDuration: (stats.totalTime / stats.count).toFixed(2) + 'ms',
                        errorRate: ((stats.errors / stats.count) * 100).toFixed(2) + '%'
                    }
                ])
            ),
            slowQueries: slowQueries.map(q => ({
                method: q.method,
                args: q.args,
                duration: q.duration.toFixed(2) + 'ms',
                timestamp: new Date(q.startTime).toISOString()
            })),
            recentErrors: failedQueries.slice(-10).map(q => ({
                method: q.method,
                args: q.args,
                error: q.error,
                timestamp: new Date(q.startTime).toISOString()
            }))
        };
    }
}

// 使用示例
const analyzer = new ORMPerformanceAnalyzer();
const monitoredORM = analyzer.wrapORM(env.services.orm);

// 运行一段时间后生成报告
setTimeout(() => {
    console.log('ORM Performance Report:', analyzer.generateReport());
}, 60000);
```

---

**ORM服务是Odoo Web框架的数据访问核心，掌握它的API和最佳实践对开发高质量的Odoo应用至关重要！** 🚀

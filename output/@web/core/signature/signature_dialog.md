# SignatureDialog - 签名对话框

## 概述

`signature_dialog.js` 是 Odoo Web 核心模块的签名对话框组件，提供了模态化的数字签名功能。该组件封装了NameAndSignature组件，以对话框形式呈现签名界面，支持签名确认、取消操作和签名数据上传，为用户提供了便捷的弹窗式签名体验，广泛应用于需要在特定流程中收集用户签名的场景。

## 文件信息
- **路径**: `/web/static/src/core/signature/signature_dialog.js`
- **行数**: 51
- **模块**: `@web/core/signature/signature_dialog`

## 依赖关系

```javascript
// 核心依赖
'@web/core/dialog/dialog'                    // 对话框组件
'@web/core/signature/name_and_signature'     // 姓名和签名组件
'@odoo/owl'                                  // OWL框架
```

## 主组件类

### 1. 组件属性定义

```javascript
static props = {
    defaultName: { type: String, optional: true },      // 默认姓名
    nameAndSignatureProps: Object,                       // 签名组件属性
    uploadSignature: Function,                           // 上传签名回调
    close: Function,                                     // 关闭对话框回调
};

static defaultProps = {
    defaultName: "",
};
```

**属性功能**:
- **默认姓名**: defaultName设置签名人的默认姓名
- **组件配置**: nameAndSignatureProps传递给内部签名组件的属性
- **上传回调**: uploadSignature处理签名完成后的上传操作
- **关闭回调**: close处理对话框的关闭操作

### 2. 组件初始化

```javascript
setup() {
    this.signature = useState({
        name: this.props.defaultName,
        isSignatureEmpty: true,
    });
}
```

**初始化功能**:
- **签名状态**: 创建响应式的签名状态对象
- **默认值**: 使用传入的默认姓名初始化
- **空状态**: 初始化签名为空状态

### 3. 属性计算

```javascript
get nameAndSignatureProps() {
    return {
        ...this.props.nameAndSignatureProps,
        signature: this.signature,
    };
}
```

**属性计算功能**:
- **属性合并**: 合并外部传入的属性和内部签名状态
- **状态注入**: 将签名状态注入到子组件
- **配置传递**: 透传配置给NameAndSignature组件

## 核心方法

### 1. 确认签名

```javascript
onClickConfirm() {
    this.props.uploadSignature({
        name: this.signature.name,
        signatureImage: this.signature.getSignatureImage(),
    });
    this.props.close();
}
```

**确认功能**:
- **数据收集**: 收集签名人姓名和签名图像
- **上传处理**: 调用上传回调处理签名数据
- **对话框关闭**: 完成后关闭对话框

## 使用场景

### 1. 基础签名对话框

```javascript
// 基础签名对话框使用
class BasicSignatureDialog extends Component {
    setup() {
        this.dialog = useService('dialog');
        this.state = useState({
            signatures: [],
            currentUser: 'John Doe'
        });
    }

    openSignatureDialog() {
        this.dialog.add(SignatureDialog, {
            defaultName: this.state.currentUser,
            nameAndSignatureProps: {
                defaultFont: 'Allura',
                fontColor: 'DarkBlue',
                signatureType: 'signature',
                displaySignatureRatio: 3.0
            },
            uploadSignature: (signatureData) => this.handleSignatureUpload(signatureData),
        });
    }

    handleSignatureUpload(signatureData) {
        console.log('Signature received:', signatureData);
        
        const newSignature = {
            id: Date.now(),
            name: signatureData.name,
            image: signatureData.signatureImage,
            timestamp: new Date().toISOString()
        };

        this.state.signatures.push(newSignature);
        this.notification.add(`${signatureData.name} 的签名已保存`, { type: 'success' });
    }

    removeSignature(signatureId) {
        this.state.signatures = this.state.signatures.filter(sig => sig.id !== signatureId);
    }

    render() {
        return xml`
            <div class="basic-signature-dialog">
                <h5>签名管理</h5>
                
                <div class="signature-actions mb-3">
                    <button 
                        class="btn btn-primary"
                        t-on-click="openSignatureDialog"
                    >
                        <i class="fa fa-edit"/> 添加签名
                    </button>
                </div>

                <div class="signatures-list">
                    <h6>已保存的签名 (${this.state.signatures.length})</h6>
                    
                    <div class="signatures-grid" t-if="state.signatures.length">
                        <div class="row">
                            <t t-foreach="state.signatures" t-as="signature" t-key="signature.id">
                                <div class="col-md-4 mb-3">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <h6 t-esc="signature.name"/>
                                            <div class="signature-preview mb-2" style="height: 80px; border: 1px solid #ddd;">
                                                <img 
                                                    t-att-src="signature.image"
                                                    style="max-width: 100%; max-height: 100%;"
                                                    t-att-alt="signature.name + '的签名'"
                                                />
                                            </div>
                                            <small class="text-muted d-block mb-2">
                                                ${new Date(signature.timestamp).toLocaleString()}
                                            </small>
                                            <button 
                                                class="btn btn-sm btn-outline-danger"
                                                t-on-click="() => this.removeSignature(signature.id)"
                                            >
                                                删除
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </t>
                        </div>
                    </div>

                    <div class="empty-state text-center py-4" t-if="!state.signatures.length">
                        <i class="fa fa-edit fa-3x text-muted mb-3"/>
                        <h6 class="text-muted">暂无签名</h6>
                        <p class="text-muted">点击上方按钮添加第一个签名</p>
                    </div>
                </div>
            </div>
        `;
    }
}
```

### 2. 审批流程签名

```javascript
// 审批流程签名组件
class ApprovalSignature extends Component {
    setup() {
        this.dialog = useService('dialog');
        this.orm = useService('orm');
        this.notification = useService('notification');
        
        this.state = useState({
            document: {
                id: 1,
                title: '采购申请',
                content: '申请采购办公用品...',
                status: 'pending',
                requester: 'Alice Wang',
                amount: 5000
            },
            approvalSteps: [
                {
                    id: 1,
                    role: '部门经理',
                    approver: 'Bob Chen',
                    status: 'pending',
                    signature: null,
                    comments: ''
                },
                {
                    id: 2,
                    role: '财务经理',
                    approver: 'Carol Liu',
                    status: 'waiting',
                    signature: null,
                    comments: ''
                },
                {
                    id: 3,
                    role: '总经理',
                    approver: 'David Zhang',
                    status: 'waiting',
                    signature: null,
                    comments: ''
                }
            ],
            currentStep: 0
        });
    }

    get currentApprovalStep() {
        return this.state.approvalSteps[this.state.currentStep];
    }

    openApprovalSignature(stepIndex) {
        const step = this.state.approvalSteps[stepIndex];
        
        if (step.status !== 'pending') {
            this.notification.add('当前步骤不可操作', { type: 'warning' });
            return;
        }

        this.dialog.add(SignatureDialog, {
            defaultName: step.approver,
            nameAndSignatureProps: {
                defaultFont: 'Allura',
                fontColor: 'DarkGreen',
                signatureType: 'signature',
                displaySignatureRatio: 4.0,
                noInputName: true
            },
            uploadSignature: (signatureData) => this.handleApprovalSignature(stepIndex, signatureData),
        });
    }

    async handleApprovalSignature(stepIndex, signatureData) {
        const step = this.state.approvalSteps[stepIndex];
        
        // 更新步骤状态
        step.signature = signatureData.signatureImage;
        step.status = 'approved';
        step.approvedAt = new Date().toISOString();

        // 移动到下一步
        if (stepIndex < this.state.approvalSteps.length - 1) {
            this.state.approvalSteps[stepIndex + 1].status = 'pending';
            this.state.currentStep = stepIndex + 1;
        } else {
            // 所有步骤完成
            this.state.document.status = 'approved';
            this.notification.add('文档审批完成！', { type: 'success' });
        }

        this.notification.add(`${step.approver} 已完成审批`, { type: 'success' });

        // 模拟保存到服务器
        try {
            await this.orm.write('approval.document', [this.state.document.id], {
                approval_steps: this.state.approvalSteps,
                status: this.state.document.status
            });
        } catch (error) {
            console.error('Failed to save approval:', error);
        }
    }

    rejectDocument(stepIndex) {
        const step = this.state.approvalSteps[stepIndex];
        step.status = 'rejected';
        this.state.document.status = 'rejected';
        
        this.notification.add(`${step.approver} 已拒绝审批`, { type: 'danger' });
    }

    getStepStatusClass(status) {
        const classes = {
            'waiting': 'text-muted',
            'pending': 'text-warning',
            'approved': 'text-success',
            'rejected': 'text-danger'
        };
        return classes[status] || 'text-muted';
    }

    getStepStatusIcon(status) {
        const icons = {
            'waiting': 'fa-clock-o',
            'pending': 'fa-hourglass-half',
            'approved': 'fa-check-circle',
            'rejected': 'fa-times-circle'
        };
        return icons[status] || 'fa-circle-o';
    }

    render() {
        const doc = this.state.document;
        const currentStep = this.currentApprovalStep;

        return xml`
            <div class="approval-signature">
                <div class="document-header mb-4">
                    <h4 t-esc="doc.title"/>
                    <div class="document-meta">
                        <p><strong>申请人:</strong> <t t-esc="doc.requester"/></p>
                        <p><strong>金额:</strong> ¥<t t-esc="doc.amount.toLocaleString()"/></p>
                        <p><strong>状态:</strong> 
                            <span t-att-class="getStepStatusClass(doc.status)">
                                <i t-att-class="'fa ' + getStepStatusIcon(doc.status)"/>
                                <t t-esc="doc.status"/>
                            </span>
                        </p>
                    </div>
                </div>

                <div class="document-content mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6>申请内容</h6>
                        </div>
                        <div class="card-body">
                            <p t-esc="doc.content"/>
                        </div>
                    </div>
                </div>

                <div class="approval-steps">
                    <h5>审批流程</h5>
                    
                    <div class="steps-timeline">
                        <t t-foreach="state.approvalSteps" t-as="step" t-key="step.id">
                            <div class="step-item mb-3">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="row align-items-center">
                                            <div class="col-md-3">
                                                <h6 t-esc="step.role"/>
                                                <p class="mb-0 text-muted" t-esc="step.approver"/>
                                            </div>
                                            <div class="col-md-3">
                                                <span t-att-class="'badge ' + (step.status === 'approved' ? 'bg-success' : step.status === 'rejected' ? 'bg-danger' : step.status === 'pending' ? 'bg-warning' : 'bg-secondary')">
                                                    <i t-att-class="'fa ' + getStepStatusIcon(step.status)"/>
                                                    <t t-esc="step.status"/>
                                                </span>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="signature-preview" t-if="step.signature">
                                                    <img 
                                                        t-att-src="step.signature"
                                                        style="max-height: 40px; max-width: 120px;"
                                                        alt="签名"
                                                    />
                                                </div>
                                                <span class="text-muted" t-else="">未签名</span>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="step-actions" t-if="step.status === 'pending'">
                                                    <button 
                                                        class="btn btn-sm btn-success me-2"
                                                        t-on-click="() => this.openApprovalSignature(step_index)"
                                                    >
                                                        <i class="fa fa-check"/> 批准
                                                    </button>
                                                    <button 
                                                        class="btn btn-sm btn-danger"
                                                        t-on-click="() => this.rejectDocument(step_index)"
                                                    >
                                                        <i class="fa fa-times"/> 拒绝
                                                    </button>
                                                </div>
                                                <small class="text-muted" t-if="step.approvedAt">
                                                    ${new Date(step.approvedAt).toLocaleString()}
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>
                </div>

                <div class="approval-summary mt-4" t-if="doc.status === 'approved'">
                    <div class="alert alert-success">
                        <h5><i class="fa fa-check-circle"/> 审批完成</h5>
                        <p>所有审批步骤已完成，申请已通过。</p>
                    </div>
                </div>

                <div class="approval-summary mt-4" t-if="doc.status === 'rejected'">
                    <div class="alert alert-danger">
                        <h5><i class="fa fa-times-circle"/> 审批被拒绝</h5>
                        <p>申请在审批过程中被拒绝。</p>
                    </div>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 模态化设计
- 基于Dialog组件的模态对话框
- 独立的签名操作空间
- 清晰的确认和取消流程

### 2. 组件封装
- 封装NameAndSignature组件
- 简化的API接口
- 配置透传机制

### 3. 状态管理
- 内部签名状态管理
- 外部回调机制
- 数据收集和传递

### 4. 灵活配置
- 可配置的签名组件属性
- 自定义上传处理逻辑
- 默认值支持

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 为NameAndSignature添加对话框功能
- 保持原有组件接口不变

### 2. 代理模式 (Proxy Pattern)
- 代理签名组件的配置和状态
- 统一的接口管理

### 3. 观察者模式 (Observer Pattern)
- 签名完成的回调通知
- 状态变化的响应机制

## 注意事项

1. **数据安全**: 确保签名数据的安全传输和存储
2. **用户体验**: 提供清晰的操作指引和反馈
3. **错误处理**: 处理签名失败和网络错误
4. **性能考虑**: 优化签名图像的大小和质量

## 扩展建议

1. **批量签名**: 支持多人批量签名
2. **签名验证**: 添加签名的验证机制
3. **模板支持**: 支持签名模板和预设
4. **审计日志**: 记录签名的详细审计信息
5. **移动优化**: 针对移动设备的签名优化

该签名对话框为Odoo Web应用提供了便捷的模态化签名功能，通过简洁的API和灵活的配置确保了良好的用户体验和开发者友好性。

# NameAndSignature - 姓名和签名组件

## 概述

`name_and_signature.js` 是 Odoo Web 核心模块的姓名和签名组件，提供了完整的数字签名功能。该组件支持手绘签名、自动字体签名、图片上传签名等多种签名模式，具备字体选择、签名清除、图片处理、移动端适配等功能，为用户提供了灵活便捷的数字签名体验，广泛应用于合同签署、文档确认、电子表单等需要签名的业务场景。

## 文件信息
- **路径**: `/web/static/src/core/signature/name_and_signature.js`
- **行数**: 331
- **模块**: `@web/core/signature/name_and_signature`

## 依赖关系

```javascript
// 核心依赖
'@web/core/assets'                           // 资源加载
'@web/core/browser/feature_detection'        // 特性检测
'@web/core/dropdown/dropdown'                // 下拉菜单
'@web/core/dropdown/dropdown_item'           // 下拉项
'@web/core/network/rpc'                      // RPC调用
'@web/core/utils/hooks'                      // 工具钩子
'@web/core/utils/render'                     // 渲染工具
'@web/core/utils/urls'                       // URL工具
'@odoo/owl'                                  // OWL框架

// 外部依赖
SignaturePad                                 // 签名板库
```

## 主组件类

### 1. 组件属性定义

```javascript
static props = {
    signature: { type: Object },                        // 签名对象
    defaultFont: { type: String, optional: true },      // 默认字体
    displaySignatureRatio: { type: Number, optional: true }, // 显示比例
    fontColor: { type: String, optional: true },        // 字体颜色
    signatureType: { type: String, optional: true },    // 签名类型
    noInputName: { type: Boolean, optional: true },     // 禁用姓名输入
    mode: { type: String, optional: true },             // 签名模式
};

static defaultProps = {
    defaultFont: "",
    displaySignatureRatio: 3.0,
    fontColor: "DarkBlue",
    signatureType: "signature",
    noInputName: false,
};
```

**属性功能**:
- **签名对象**: signature包含签名的所有数据和方法
- **字体配置**: defaultFont和fontColor控制字体样式
- **显示比例**: displaySignatureRatio控制签名区域的宽高比
- **签名类型**: signatureType支持完整签名或首字母缩写
- **模式控制**: mode和noInputName控制组件的显示模式

### 2. 组件初始化

```javascript
setup() {
    this.htmlId = htmlId++;
    this.defaultName = this.props.signature.name || "";
    this.currentFont = 0;
    this.drawTimeout = null;

    this.state = useState({
        signMode: this.props.mode || (this.props.noInputName && !this.defaultName ? "draw" : "auto"),
        showSignatureArea: !!(this.props.noInputName || this.defaultName),
        showFontList: false,
    });

    // 引用和自动聚焦
    this.signNameInputRef = useRef("signNameInput");
    this.signInputLoad = useRef("signInputLoad");
    useAutofocus({ refName: "signNameInput" });

    // 异步初始化
    onWillStart(async () => {
        this.fonts = await rpc(`/web/sign/get_fonts/${this.props.defaultFont}`);
        await loadJS("/web/static/lib/signature_pad/signature_pad.umd.js");
    });

    // 签名板初始化
    this.signatureRef = useRef("signature");
    useEffect((el) => {
        if (el) {
            this.signaturePad = new SignaturePad(el, {
                penColor: this.props.fontColor,
                backgroundColor: "rgba(255,255,255,0)",
                minWidth: 2,
                maxWidth: 2,
            });
            this.setupSignaturePad();
        }
    }, () => [this.signatureRef.el]);
}
```

**初始化功能**:
- **唯一标识**: 为每个组件生成唯一的HTML ID
- **状态管理**: 管理签名模式、显示状态等
- **引用管理**: 获取关键元素的引用
- **异步加载**: 加载字体数据和签名板库
- **签名板配置**: 配置签名板的样式和行为

## 核心功能

### 1. 签名模式管理

```javascript
setMode(mode, reset) {
    if (reset !== true && mode === this.signMode) {
        return; // 防止闪烁和不必要的计算
    }

    this.state.signMode = mode;
    this.signaturePad[this.state.signMode === "draw" ? "on" : "off"]();
    this.clear();

    if (this.state.signMode === "auto") {
        this.drawCurrentName(); // 基于姓名绘制
    }
}
```

**签名模式**:
- **draw**: 用户手动绘制签名
- **auto**: 基于选择的字体自动生成签名
- **load**: 从图片文件加载签名

**模式功能**:
- **动态切换**: 支持运行时切换签名模式
- **状态同步**: 切换模式时同步签名板状态
- **自动绘制**: auto模式下自动绘制当前姓名

### 2. 自动字体签名

```javascript
async drawCurrentName() {
    const font = this.fonts[this.currentFont];
    const text = this.getCleanedName();
    const canvas = this.signatureRef.el;
    const img = this.getSVGText(font, text, canvas.width, canvas.height);
    await this.printImage(img);
}

getCleanedName() {
    const text = this.props.signature.name;
    if (this.props.signatureType === "initial" && text) {
        return text.split(" ")
            .map(function (w) { return w[0]; })
            .join(".") + ".";
    }
    return text;
}

getSVGText(font, text, width, height) {
    const svg = renderToString("web.sign_svg_text", {
        width: width,
        height: height,
        font: font,
        text: text,
        type: this.props.signatureType,
        color: this.props.fontColor,
    });
    return "data:image/svg+xml," + encodeURI(svg);
}
```

**自动签名功能**:
- **字体渲染**: 使用SVG渲染选定字体的签名
- **姓名清理**: 根据签名类型处理姓名文本
- **首字母模式**: 支持首字母缩写签名
- **动态生成**: 实时生成签名图像

### 3. 图片处理

```javascript
async printImage(imgSrc) {
    this.clear();
    const c = this.signaturePad.canvas;
    const img = new Image();
    img.onload = () => {
        const ctx = c.getContext("2d");
        var ratio = ((img.width / img.height) > (c.width / c.height)) ? 
            c.width / img.width : c.height / img.height;
        ctx.drawImage( 
            img,
            (c.width / 2) - (img.width * ratio / 2),
            (c.height / 2) - (img.height * ratio / 2),
            img.width * ratio,
            img.height * ratio
        );
        this.props.signature.isSignatureEmpty = this.isSignatureEmpty;
    };
    img.src = imgSrc;
    this.signaturePad._isEmpty = false;
}

async onChangeSignLoadInput(ev) {
    var file = ev.target.files[0];
    if (file === undefined) {
        return false;
    }
    if (file.type.substr(0, 5) !== "image") {
        this.clear();
        this.state.loadIsInvalid = true;
        return false;
    }
    this.state.loadIsInvalid = false;

    const result = await getDataURLFromFile(file);
    await this.printImage(result);
}
```

**图片处理功能**:
- **比例缩放**: 自动调整图片大小适应签名区域
- **居中显示**: 图片在签名区域中居中显示
- **格式验证**: 验证上传文件是否为图片格式
- **错误处理**: 处理无效文件的情况

### 4. 签名区域管理

```javascript
resetSignature() {
    this.resizeSignature();
    this.clear();
    this.setMode(this.state.signMode, true);
    this.focusName();
}

resizeSignature() {
    const width = this.signatureRef.el.clientWidth;
    const height = parseInt(width / this.props.displaySignatureRatio);
    Object.assign(this.signatureRef.el, { width, height });
}

clear() {
    this.signaturePad.clear();
    this.props.signature.isSignatureEmpty = this.isSignatureEmpty;
}

get isSignatureEmpty() {
    return this.signaturePad.isEmpty();
}
```

**区域管理功能**:
- **动态调整**: 根据容器宽度和比例调整签名区域大小
- **重置功能**: 完整重置签名区域和状态
- **清除功能**: 清除签名内容
- **状态检测**: 检测签名区域是否为空

## 使用场景

### 1. 基础签名组件

```javascript
// 基础签名组件使用
class BasicSignature extends Component {
    setup() {
        this.state = useState({
            signature: {
                name: '',
                isSignatureEmpty: true,
                signatureImage: null,
                getSignatureImage: null,
                resetSignature: null
            },
            signatureMode: 'auto'
        });
    }

    onSignatureUpdate() {
        if (this.state.signature.getSignatureImage) {
            this.state.signature.signatureImage = this.state.signature.getSignatureImage();
            console.log('Signature updated:', this.state.signature.signatureImage);
        }
    }

    resetSignature() {
        if (this.state.signature.resetSignature) {
            this.state.signature.resetSignature();
        }
    }

    saveSignature() {
        if (this.state.signature.isSignatureEmpty) {
            this.notification.add('请先完成签名', { type: 'warning' });
            return;
        }

        const signatureData = {
            name: this.state.signature.name,
            image: this.state.signature.signatureImage,
            timestamp: new Date().toISOString()
        };

        console.log('Saving signature:', signatureData);
        this.notification.add('签名保存成功', { type: 'success' });
    }

    render() {
        return xml`
            <div class="basic-signature">
                <h5>数字签名</h5>
                
                <div class="signature-container">
                    <NameAndSignature
                        signature="state.signature"
                        defaultFont="'Allura'"
                        fontColor="'DarkBlue'"
                        signatureType="'signature'"
                        displaySignatureRatio="3.0"
                        mode="state.signatureMode"
                    />
                </div>

                <div class="signature-info mt-3">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>签名人:</strong> <t t-esc="state.signature.name || '未输入'"/></p>
                            <p><strong>状态:</strong> 
                                <span t-att-class="state.signature.isSignatureEmpty ? 'text-danger' : 'text-success'">
                                    <t t-esc="state.signature.isSignatureEmpty ? '未签名' : '已签名'"/>
                                </span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <div class="signature-actions">
                                <button 
                                    class="btn btn-primary me-2"
                                    t-on-click="saveSignature"
                                    t-att-disabled="state.signature.isSignatureEmpty"
                                >
                                    保存签名
                                </button>
                                <button 
                                    class="btn btn-secondary me-2"
                                    t-on-click="resetSignature"
                                >
                                    重置
                                </button>
                                <button 
                                    class="btn btn-info"
                                    t-on-click="onSignatureUpdate"
                                >
                                    更新预览
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

### 2. 合同签署组件

```javascript
// 合同签署组件
class ContractSigning extends Component {
    setup() {
        this.state = useState({
            contract: {
                id: 1,
                title: '服务协议',
                content: '这是一份重要的服务协议...',
                status: 'pending'
            },
            signers: [
                {
                    id: 1,
                    name: '张三',
                    role: '甲方代表',
                    signature: {
                        name: '张三',
                        isSignatureEmpty: true,
                        signatureImage: null,
                        getSignatureImage: null,
                        resetSignature: null
                    },
                    signed: false
                },
                {
                    id: 2,
                    name: '李四',
                    role: '乙方代表',
                    signature: {
                        name: '李四',
                        isSignatureEmpty: true,
                        signatureImage: null,
                        getSignatureImage: null,
                        resetSignature: null
                    },
                    signed: false
                }
            ],
            currentSigner: 0,
            allSigned: false
        });
    }

    get currentSignerData() {
        return this.state.signers[this.state.currentSigner];
    }

    nextSigner() {
        if (this.currentSignerData.signature.isSignatureEmpty) {
            this.notification.add('请先完成当前签名', { type: 'warning' });
            return;
        }

        // 保存当前签名
        this.currentSignerData.signature.signatureImage = this.currentSignerData.signature.getSignatureImage();
        this.currentSignerData.signed = true;

        // 切换到下一个签名人
        if (this.state.currentSigner < this.state.signers.length - 1) {
            this.state.currentSigner++;
        } else {
            this.state.allSigned = true;
            this.completeContract();
        }
    }

    previousSigner() {
        if (this.state.currentSigner > 0) {
            this.state.currentSigner--;
        }
    }

    completeContract() {
        this.state.contract.status = 'completed';
        this.notification.add('合同签署完成！', { type: 'success' });
        
        // 生成签署记录
        const signingRecord = {
            contractId: this.state.contract.id,
            signers: this.state.signers.map(signer => ({
                name: signer.name,
                role: signer.role,
                signature: signer.signature.signatureImage,
                timestamp: new Date().toISOString()
            })),
            completedAt: new Date().toISOString()
        };

        console.log('Contract signing completed:', signingRecord);
    }

    resetCurrentSignature() {
        if (this.currentSignerData.signature.resetSignature) {
            this.currentSignerData.signature.resetSignature();
        }
    }

    render() {
        const signer = this.currentSignerData;
        const progress = ((this.state.currentSigner + 1) / this.state.signers.length) * 100;

        return xml`
            <div class="contract-signing">
                <div class="contract-header mb-4">
                    <h4 t-esc="state.contract.title"/>
                    <div class="progress mt-2">
                        <div 
                            class="progress-bar" 
                            role="progressbar" 
                            t-att-style="'width: ' + progress + '%'"
                        >
                            ${Math.round(progress)}%
                        </div>
                    </div>
                </div>

                <div class="contract-content mb-4" t-if="!state.allSigned">
                    <div class="card">
                        <div class="card-body">
                            <p t-esc="state.contract.content"/>
                        </div>
                    </div>
                </div>

                <div class="signing-section" t-if="!state.allSigned">
                    <div class="signer-info mb-3">
                        <h5>当前签署人: <t t-esc="signer.name"/> (<t t-esc="signer.role"/>)</h5>
                        <p class="text-muted">请在下方区域完成签名</p>
                    </div>

                    <div class="signature-area">
                        <NameAndSignature
                            signature="signer.signature"
                            defaultFont="'Allura'"
                            fontColor="'DarkBlue'"
                            signatureType="'signature'"
                            displaySignatureRatio="4.0"
                            noInputName="true"
                        />
                    </div>

                    <div class="signing-actions mt-3">
                        <div class="row">
                            <div class="col-md-6">
                                <button 
                                    class="btn btn-outline-secondary me-2"
                                    t-on-click="previousSigner"
                                    t-att-disabled="state.currentSigner === 0"
                                >
                                    <i class="fa fa-arrow-left"/> 上一位
                                </button>
                                <button 
                                    class="btn btn-secondary"
                                    t-on-click="resetCurrentSignature"
                                >
                                    重新签名
                                </button>
                            </div>
                            <div class="col-md-6 text-end">
                                <button 
                                    class="btn btn-primary"
                                    t-on-click="nextSigner"
                                    t-att-disabled="signer.signature.isSignatureEmpty"
                                >
                                    <t t-if="state.currentSigner &lt; state.signers.length - 1">
                                        下一位 <i class="fa fa-arrow-right"/>
                                    </t>
                                    <t t-else="">
                                        完成签署 <i class="fa fa-check"/>
                                    </t>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="completion-summary" t-if="state.allSigned">
                    <div class="alert alert-success">
                        <h5><i class="fa fa-check-circle"/> 合同签署完成</h5>
                        <p>所有签署人已完成签名，合同正式生效。</p>
                    </div>

                    <div class="signers-summary">
                        <h6>签署记录</h6>
                        <div class="row">
                            <t t-foreach="state.signers" t-as="signer" t-key="signer.id">
                                <div class="col-md-6 mb-3">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <h6 t-esc="signer.name"/>
                                            <p class="text-muted" t-esc="signer.role"/>
                                            <div class="signature-preview" style="height: 60px; border: 1px solid #ddd;">
                                                <img 
                                                    t-if="signer.signature.signatureImage"
                                                    t-att-src="signer.signature.signatureImage"
                                                    style="max-width: 100%; max-height: 100%;"
                                                />
                                            </div>
                                            <small class="text-success">
                                                <i class="fa fa-check"/> 已签署
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </t>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

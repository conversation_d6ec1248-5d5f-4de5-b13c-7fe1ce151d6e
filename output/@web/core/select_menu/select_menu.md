# SelectMenu - 选择菜单组件

## 概述

`select_menu.js` 是 Odoo Web 核心模块的选择菜单组件，提供了功能丰富的下拉选择功能。该组件支持单选多选、搜索过滤、分组显示、虚拟滚动等功能，具备自动排序、标签显示、动态加载等特性，为用户提供了灵活强大的选择体验，广泛应用于表单字段、过滤器、配置选项等需要选择操作的场景。

## 文件信息
- **路径**: `/web/static/src/core/select_menu/select_menu.js`
- **行数**: 357
- **模块**: `@web/core/select_menu/select_menu`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                          // OWL框架
'@web/core/dropdown/dropdown'        // 下拉菜单
'@web/core/dropdown/dropdown_item'   // 下拉菜单项
'@web/core/l10n/translation'         // 国际化
'@web/core/tags_list/tags_list'      // 标签列表
'@web/core/utils/classname'          // 类名工具
'@web/core/utils/hooks'              // 工具钩子
'@web/core/utils/scrolling'          // 滚动工具
'@web/core/utils/search'             // 搜索工具
'@web/core/utils/timing'             // 时间工具
```

## 主组件类

### 1. 组件属性定义

```javascript
static props = {
    choices: Array,                              // 选择项列表
    groups: Array,                               // 分组列表
    class: String,                               // 组件CSS类
    menuClass: String,                           // 菜单CSS类
    togglerClass: String,                        // 切换器CSS类
    required: Boolean,                           // 是否必选
    searchable: Boolean,                         // 是否可搜索
    autoSort: Boolean,                           // 是否自动排序
    placeholder: String,                         // 占位符
    searchPlaceholder: String,                   // 搜索占位符
    value: Any,                                  // 当前值
    multiSelect: Boolean,                        // 是否多选
    onInput: Function,                           // 输入回调
    onSelect: Function,                          // 选择回调
    disabled: Boolean,                           // 是否禁用
};

static defaultProps = {
    value: undefined,
    class: "",
    togglerClass: "",
    multiSelect: false,
    onSelect: () => {},
    required: false,
    searchable: true,
    autoSort: true,
    searchPlaceholder: _t("Search..."),
    choices: [],
    groups: [],
    disabled: false,
};
```

**属性功能**:
- **数据源**: choices和groups提供选择项数据
- **交互控制**: multiSelect、searchable、required控制交互行为
- **样式定制**: class、menuClass、togglerClass控制样式
- **回调处理**: onInput、onSelect处理用户操作

### 2. 滚动设置

```javascript
static SCROLL_SETTINGS = {
    defaultCount: 500,          // 默认显示数量
    increaseAmount: 300,        // 增量加载数量
    distanceBeforeReload: 500,  // 触发加载的距离
};
```

**滚动优化**:
- **虚拟滚动**: 只渲染可见的选项
- **增量加载**: 滚动时动态加载更多选项
- **性能优化**: 避免一次性渲染大量选项

### 3. 组件初始化

```javascript
setup() {
    this.state = useState({
        choices: [],
        displayedOptions: [],
        searchValue: "",
    });
    this.inputRef = useRef("inputRef");
    this.menuRef = useChildRef();
    this.debouncedOnInput = useDebounced(
        () => this.onInput(this.inputRef.el ? this.inputRef.el.value.trim() : ""),
        250
    );
    this.isOpen = false;
    this.selectedChoice = this.getSelectedChoice(this.props);
}
```

**初始化功能**:
- **状态管理**: 管理选择项、显示选项、搜索值
- **引用管理**: 管理输入框和菜单引用
- **防抖处理**: 防抖处理输入事件
- **选择状态**: 初始化选择状态

## 核心属性

### 1. 显示值计算

```javascript
get displayValue() {
    return this.selectedChoice ? this.selectedChoice.label : "";
}

get canDeselect() {
    return !this.props.required && this.selectedChoice !== undefined;
}

get multiSelectChoices() {
    return this.selectedChoice.map((c) => {
        return {
            id: c.value,
            text: c.label,
            onDelete: () => {
                const values = [...this.props.value];
                values.splice(values.indexOf(c.value), 1);
                this.props.onSelect(values);
            },
        };
    });
}
```

**属性功能**:
- **显示值**: 计算当前显示的文本
- **取消选择**: 判断是否可以取消选择
- **多选标签**: 生成多选模式的标签数据

### 2. 菜单样式

```javascript
get menuClass() {
    return mergeClasses(
        {
            "o_select_menu_menu border bg-light": true,
            "py-0": this.props.searchable,
            o_select_menu_multi_select: this.props.multiSelect,
        },
        this.props.menuClass
    );
}
```

## 核心方法

### 1. 选项过滤

```javascript
filterOptions(searchString = "", groups) {
    const groupsList = groups || [{ choices: this.props.choices }, ...this.props.groups];
    this.state.choices = [];

    for (const group of groupsList) {
        let filteredOptions = [];

        if (searchString) {
            filteredOptions = fuzzyLookup(
                searchString,
                group.choices,
                (choice) => choice.label
            );
        } else {
            filteredOptions = group.choices;
            if (this.props.autoSort) {
                filteredOptions.sort((optionA, optionB) =>
                    optionA.label.localeCompare(optionB.label)
                );
            }
        }

        if (filteredOptions.length === 0) continue;

        if (group.label) {
            this.state.choices.push({ ...group, isGroup: true });
        }
        this.state.choices.push(...filteredOptions);
    }

    this.sliceDisplayedOptions();
}
```

**过滤功能**:
- **模糊搜索**: 使用fuzzyLookup进行模糊匹配
- **自动排序**: 可选的字母排序
- **分组处理**: 保持分组结构
- **性能优化**: 切片显示选项

### 2. 虚拟滚动

```javascript
onScroll(event) {
    const el = event.target;
    const hasReachMax = this.state.displayedOptions.length >= this.state.choices.length;
    const remainingDistance = el.scrollHeight - el.scrollTop;
    const distanceToReload = el.clientHeight + this.constructor.SCROLL_SETTINGS.distanceBeforeReload;

    if (!hasReachMax && remainingDistance < distanceToReload) {
        const displayCount = this.state.displayedOptions.length + 
                           this.constructor.SCROLL_SETTINGS.increaseAmount;
        this.state.displayedOptions = this.state.choices.slice(0, displayCount);
    }
}

sliceDisplayedOptions() {
    const selectedIndex = this.getSelectedOptionIndex();
    const defaultCount = this.constructor.SCROLL_SETTINGS.defaultCount;

    if (selectedIndex === -1) {
        this.state.displayedOptions = this.state.choices.slice(0, defaultCount);
    } else {
        const endIndex = Math.max(
            selectedIndex + this.constructor.SCROLL_SETTINGS.increaseAmount,
            defaultCount
        );
        this.state.displayedOptions = this.state.choices.slice(0, endIndex);
    }
}
```

**滚动功能**:
- **距离检测**: 检测滚动距离触发加载
- **增量加载**: 按配置数量增量加载
- **选择项优先**: 确保选择项可见
- **性能优化**: 避免渲染过多DOM元素

### 3. 选择处理

```javascript
onItemSelected(value) {
    if (this.props.multiSelect) {
        const values = [...this.props.value];
        const valueIndex = values.indexOf(value);

        if (valueIndex !== -1) {
            values.splice(valueIndex, 1);
            this.props.onSelect(values);
        } else {
            this.props.onSelect([...this.props.value, value]);
        }
    } else if (!this.selectedChoice || this.selectedChoice.value !== value) {
        this.props.onSelect(value);
    }
    
    if (this.inputRef.el) {
        this.inputRef.el.value = "";
        this.state.searchValue = "";
    }
}
```

**选择功能**:
- **多选处理**: 添加或移除选择项
- **单选处理**: 设置单个选择值
- **状态清理**: 清理搜索状态

## 使用场景

### 1. 基础选择菜单

```javascript
// 基础选择菜单使用
class BasicSelectMenu extends Component {
    setup() {
        this.state = useState({
            singleValue: null,
            multiValue: [],
            choices: [
                { value: 'option1', label: '选项 1' },
                { value: 'option2', label: '选项 2' },
                { value: 'option3', label: '选项 3' },
                { value: 'option4', label: '选项 4' },
                { value: 'option5', label: '选项 5' }
            ],
            groups: [
                {
                    label: '分组 A',
                    choices: [
                        { value: 'groupA1', label: '分组A选项1' },
                        { value: 'groupA2', label: '分组A选项2' }
                    ]
                },
                {
                    label: '分组 B',
                    choices: [
                        { value: 'groupB1', label: '分组B选项1' },
                        { value: 'groupB2', label: '分组B选项2' }
                    ]
                }
            ]
        });
    }

    onSingleSelect(value) {
        this.state.singleValue = value;
        console.log('Single select:', value);
    }

    onMultiSelect(values) {
        this.state.multiValue = values;
        console.log('Multi select:', values);
    }

    render() {
        return xml`
            <div class="basic-select-menu">
                <h5>基础选择菜单示例</h5>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">单选菜单</label>
                        <SelectMenu
                            choices="state.choices"
                            value="state.singleValue"
                            onSelect="onSingleSelect"
                            placeholder="请选择一个选项"
                            searchable="true"
                            required="false"
                        />
                        <div class="mt-2" t-if="state.singleValue">
                            <small class="text-muted">
                                已选择: <strong t-esc="state.singleValue"/>
                            </small>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label">多选菜单</label>
                        <SelectMenu
                            choices="state.choices"
                            value="state.multiValue"
                            onSelect="onMultiSelect"
                            placeholder="请选择多个选项"
                            multiSelect="true"
                            searchable="true"
                        />
                        <div class="mt-2" t-if="state.multiValue.length">
                            <small class="text-muted">
                                已选择 <strong t-esc="state.multiValue.length"/> 个选项
                            </small>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">分组菜单</label>
                    <SelectMenu
                        groups="state.groups"
                        value="state.singleValue"
                        onSelect="onSingleSelect"
                        placeholder="请选择分组选项"
                        searchable="true"
                        autoSort="false"
                    />
                </div>

                <div class="selection-info">
                    <div class="card">
                        <div class="card-header">
                            <h6>选择信息</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>单选值:</strong></p>
                                    <code t-esc="JSON.stringify(state.singleValue)"/>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>多选值:</strong></p>
                                    <code t-esc="JSON.stringify(state.multiValue)"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

### 2. 动态加载选择菜单

```javascript
// 动态加载选择菜单
class DynamicSelectMenu extends Component {
    setup() {
        this.state = useState({
            selectedCountry: null,
            selectedCity: null,
            countries: [
                { value: 'cn', label: '中国' },
                { value: 'us', label: '美国' },
                { value: 'jp', label: '日本' }
            ],
            cities: [],
            isLoadingCities: false,
            searchResults: [],
            isSearching: false
        });

        this.cityData = {
            cn: [
                { value: 'beijing', label: '北京' },
                { value: 'shanghai', label: '上海' },
                { value: 'guangzhou', label: '广州' },
                { value: 'shenzhen', label: '深圳' }
            ],
            us: [
                { value: 'newyork', label: '纽约' },
                { value: 'losangeles', label: '洛杉矶' },
                { value: 'chicago', label: '芝加哥' },
                { value: 'houston', label: '休斯顿' }
            ],
            jp: [
                { value: 'tokyo', label: '东京' },
                { value: 'osaka', label: '大阪' },
                { value: 'kyoto', label: '京都' },
                { value: 'yokohama', label: '横滨' }
            ]
        };
    }

    async onCountrySelect(countryValue) {
        this.state.selectedCountry = countryValue;
        this.state.selectedCity = null;
        
        if (countryValue) {
            this.state.isLoadingCities = true;
            
            // 模拟异步加载
            await new Promise(resolve => setTimeout(resolve, 500));
            
            this.state.cities = this.cityData[countryValue] || [];
            this.state.isLoadingCities = false;
        } else {
            this.state.cities = [];
        }
    }

    onCitySelect(cityValue) {
        this.state.selectedCity = cityValue;
        console.log('Selected city:', cityValue);
    }

    async onSearchInput(searchValue) {
        if (!searchValue) {
            this.state.searchResults = [];
            return;
        }

        this.state.isSearching = true;
        
        // 模拟搜索API调用
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // 模拟搜索结果
        const allCities = Object.values(this.cityData).flat();
        this.state.searchResults = allCities.filter(city => 
            city.label.toLowerCase().includes(searchValue.toLowerCase())
        ).slice(0, 10);
        
        this.state.isSearching = false;
    }

    render() {
        return xml`
            <div class="dynamic-select-menu">
                <h5>动态加载选择菜单</h5>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">选择国家</label>
                        <SelectMenu
                            choices="state.countries"
                            value="state.selectedCountry"
                            onSelect="onCountrySelect"
                            placeholder="请选择国家"
                            searchable="true"
                            required="true"
                        />
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label">选择城市</label>
                        <div class="position-relative">
                            <SelectMenu
                                choices="state.cities"
                                value="state.selectedCity"
                                onSelect="onCitySelect"
                                placeholder="请先选择国家"
                                searchable="true"
                                disabled="!state.selectedCountry || state.isLoadingCities"
                            />
                            <div class="loading-overlay" t-if="state.isLoadingCities">
                                <div class="spinner-border spinner-border-sm"/>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">搜索城市</label>
                    <SelectMenu
                        choices="state.searchResults"
                        value="state.selectedCity"
                        onSelect="onCitySelect"
                        onInput="onSearchInput"
                        placeholder="输入城市名称搜索"
                        searchable="true"
                        class="search-select"
                    />
                    <div class="mt-1" t-if="state.isSearching">
                        <small class="text-muted">
                            <i class="fa fa-spinner fa-spin"/> 搜索中...
                        </small>
                    </div>
                </div>

                <div class="selection-summary">
                    <div class="card">
                        <div class="card-header">
                            <h6>选择摘要</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>国家:</strong></p>
                                    <span t-if="state.selectedCountry" class="badge bg-primary">
                                        <t t-esc="state.countries.find(c => c.value === state.selectedCountry)?.label"/>
                                    </span>
                                    <span t-else="" class="text-muted">未选择</span>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>城市:</strong></p>
                                    <span t-if="state.selectedCity" class="badge bg-success">
                                        <t t-esc="[...state.cities, ...state.searchResults].find(c => c.value === state.selectedCity)?.label"/>
                                    </span>
                                    <span t-else="" class="text-muted">未选择</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="technical-info mt-4">
                    <div class="card">
                        <div class="card-header">
                            <h6>技术信息</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <h6>可用城市数量</h6>
                                    <p t-esc="state.cities.length"/>
                                </div>
                                <div class="col-md-4">
                                    <h6>搜索结果数量</h6>
                                    <p t-esc="state.searchResults.length"/>
                                </div>
                                <div class="col-md-4">
                                    <h6>加载状态</h6>
                                    <p t-esc="state.isLoadingCities ? '加载中' : '已完成'"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 虚拟滚动
- 只渲染可见选项
- 动态加载更多选项
- 优化大数据集性能

### 2. 智能搜索
- 模糊匹配搜索
- 防抖优化输入
- 动态过滤结果

### 3. 多选支持
- 标签式多选显示
- 便捷的删除操作
- 批量选择管理

### 4. 分组显示
- 层次化选项组织
- 分组标题显示
- 保持分组结构

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- 单选和多选的不同策略
- 搜索和排序的策略选择

### 2. 观察者模式 (Observer Pattern)
- 属性变化的响应式更新
- 搜索状态的监听

### 3. 虚拟代理模式 (Virtual Proxy Pattern)
- 虚拟滚动的实现
- 延迟加载选项

## 注意事项

1. **性能优化**: 使用虚拟滚动处理大数据集
2. **内存管理**: 及时清理事件监听器和定时器
3. **用户体验**: 提供清晰的加载和搜索反馈
4. **可访问性**: 支持键盘导航和屏幕阅读器

## 扩展建议

1. **自定义渲染**: 支持自定义选项渲染
2. **异步验证**: 选择项的异步验证
3. **批量操作**: 多选模式的批量操作
4. **历史记录**: 选择历史的记录和恢复
5. **主题定制**: 更灵活的主题和样式定制

该选择菜单组件为Odoo Web应用提供了功能完整的选择功能，通过虚拟滚动和智能搜索确保了良好的性能和用户体验。

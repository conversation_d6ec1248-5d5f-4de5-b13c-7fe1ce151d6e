# Odoo Python 解释器 (Python Interpreter) 学习资料

## 文件概述

**文件路径**: `output/@web/core/py_js/py_interpreter.js`  
**原始路径**: `/web/static/src/core/py_js/py_interpreter.js`  
**模块类型**: 核心基础模块 - Python 解释器  
**代码行数**: 501 行  
**依赖关系**: 
- `@web/core/py_js/py_builtin` - Python 内置函数
- `@web/core/py_js/py_date` - Python 日期时间类型
- `@web/core/py_js/py_utils` - Python 工具函数
- `@web/core/py_js/py_parser` - Python 语法分析器

## 模块功能

Python 解释器是 Odoo Web 客户端 Python-JavaScript 桥接系统的执行引擎，负责：
- 执行抽象语法树（AST）并计算表达式的值
- 实现 Python 语义的运算符和操作
- 处理 Python 数据类型的比较和转换
- 管理变量作用域和上下文环境
- 提供与 Python 一致的表达式求值结果

## 核心工具函数

### 1. 类型系统和比较

#### pytypeIndex 函数
```javascript
function pytypeIndex(val) {
    switch (typeof val) {
        case "object":
            // None, List, Object, Dict
            return val === null ? 1 : Array.isArray(val) ? 5 : 3;
        case "number":
            return 2;
        case "string":
            return 4;
    }
    throw new EvaluationError(`Unknown type: ${typeof val}`);
}
```

**Python 类型优先级**:
1. **None** (null) - 索引 1
2. **Number** (boolean/number) - 索引 2  
3. **Object** (dict) - 索引 3
4. **String** - 索引 4
5. **List** (array) - 索引 5

**用途**: 实现 Python 的类型比较语义，确保不同类型间的比较结果与 Python 一致

#### isLess 函数
```javascript
function isLess(left, right) {
    if (typeof left === "number" && typeof right === "number") {
        return left < right;
    }
    if (typeof left === "boolean") {
        left = left ? 1 : 0;
    }
    if (typeof right === "boolean") {
        right = right ? 1 : 0;
    }
    const leftIndex = pytypeIndex(left);
    const rightIndex = pytypeIndex(right);
    if (leftIndex === rightIndex) {
        return left < right;
    }
    return leftIndex < rightIndex;
}
```

**比较逻辑**:
- **同类型**: 使用 JavaScript 原生比较
- **布尔转换**: 布尔值转换为数字（True=1, False=0）
- **跨类型**: 使用类型索引比较

#### isEqual 函数
```javascript
function isEqual(left, right) {
    if (typeof left !== typeof right) {
        if (typeof left === "boolean" && typeof right === "number") {
            return right === (left ? 1 : 0);
        }
        if (typeof left === "number" && typeof right === "boolean") {
            return left === (right ? 1 : 0);
        }
        return false;
    }
    if (left instanceof Object && left.isEqual) {
        return left.isEqual(right);
    }
    return left === right;
}
```

**相等性检查**:
- **类型不同**: 只有数字和布尔值可以相等
- **自定义相等**: 支持对象的 `isEqual` 方法
- **默认相等**: 使用 JavaScript 严格相等

#### isIn 函数
```javascript
function isIn(left, right) {
    if (Array.isArray(right)) {
        return right.includes(left);
    }
    if (typeof right === "string" && typeof left === "string") {
        return right.includes(left);
    }
    if (typeof right === "object") {
        return left in right;
    }
    return false;
}
```

**成员检测**:
- **数组**: 使用 `includes` 方法
- **字符串**: 子字符串检测
- **对象**: 属性存在检测

### 2. 运算符实现

#### applyUnaryOp 函数
```javascript
function applyUnaryOp(ast, context) {
    const value = evaluate(ast.right, context);
    switch (ast.op) {
        case "-":
            if (value instanceof Object && value.negate) {
                return value.negate();
            }
            return -value;
        case "+":
            return value;
        case "not":
            return !isTrue(value);
    }
    throw new EvaluationError(`Unknown unary operator: ${ast.op}`);
}
```

**一元操作符**:
- **负号 (-)**: 支持自定义 `negate` 方法
- **正号 (+)**: 直接返回值
- **逻辑非 (not)**: 使用 Python 真值语义

#### applyBinaryOp 函数
```javascript
function applyBinaryOp(ast, context) {
    const left = evaluate(ast.left, context);
    const right = evaluate(ast.right, context);
    switch (ast.op) {
        case "+": {
            // 特殊类型处理
            const relativeDeltaOnLeft = left instanceof PyRelativeDelta;
            const relativeDeltaOnRight = right instanceof PyRelativeDelta;
            if (relativeDeltaOnLeft || relativeDeltaOnRight) {
                const date = relativeDeltaOnLeft ? right : left;
                const delta = relativeDeltaOnLeft ? left : right;
                return PyRelativeDelta.add(date, delta);
            }
            
            // 时间差处理
            const timeDeltaOnLeft = left instanceof PyTimeDelta;
            const timeDeltaOnRight = right instanceof PyTimeDelta;
            if (timeDeltaOnLeft && timeDeltaOnRight) {
                return left.add(right);
            }
            
            // 数组连接
            if (left instanceof Array && right instanceof Array) {
                return [...left, ...right];
            }
            
            return left + right;
        }
        // 其他操作符...
    }
}
```

**二元操作符特点**:
- **类型感知**: 根据操作数类型选择不同的处理逻辑
- **Python 语义**: 实现 Python 特有的操作语义
- **扩展支持**: 支持自定义类型的操作方法

### 3. 比较操作符实现
```javascript
function applyComparison(ast, context) {
    const left = evaluate(ast.left, context);
    const right = evaluate(ast.right, context);
    switch (ast.op) {
        case "<":
            return isLess(left, right);
        case "<=":
            return isLess(left, right) || isEqual(left, right);
        case ">":
            return !isLess(left, right) && !isEqual(left, right);
        case ">=":
            return !isLess(left, right);
        case "==":
            return isEqual(left, right);
        case "!=":
        case "<>":
            return !isEqual(left, right);
        case "in":
            return isIn(left, right);
        case "not in":
            return !isIn(left, right);
        case "is":
            return left === right;
        case "is not":
            return left !== right;
    }
    throw new EvaluationError(`Unknown comparison operator: ${ast.op}`);
}
```

## 主要 evaluate 函数

### 1. 函数签名
```javascript
function evaluate(ast, context) {
    // AST 节点类型分发
    switch (ast.type) {
        case 0 /* Number */: return ast.value;
        case 1 /* String */: return ast.value;
        case 2 /* Boolean */: return ast.value;
        case 3 /* None */: return null;
        // 更多类型处理...
    }
}
```

### 2. 基本类型处理
```javascript
case 0 /* Number */: return ast.value;
case 1 /* String */: return ast.value;
case 2 /* Boolean */: return ast.value;
case 3 /* None */: return null;
```

### 3. 容器类型处理
```javascript
case 4 /* List */: {
    return ast.value.map((x) => evaluate(x, context));
}
case 10 /* Tuple */: {
    return ast.value.map((x) => evaluate(x, context));
}
case 11 /* Dictionary */: {
    const result = {};
    for (const [key, value] of Object.entries(ast.value)) {
        result[key] = evaluate(value, context);
    }
    return result;
}
```

### 4. 变量和函数调用
```javascript
case 5 /* Name */: {
    if (ast.value in context) {
        return context[ast.value];
    }
    throw new EvaluationError(`Unknown variable: ${ast.value}`);
}

case 8 /* FunctionCall */: {
    const fn = evaluate(ast.fn, context);
    if (!fn || typeof fn !== "function") {
        throw new EvaluationError(`${ast.fn.value} is not a function`);
    }
    const args = ast.args.map((arg) => evaluate(arg, context));
    const kwargs = {};
    for (const [key, value] of Object.entries(ast.kwargs)) {
        kwargs[key] = evaluate(value, context);
    }
    args.push(kwargs);
    return fn(...args);
}
```

### 5. 索引和属性访问
```javascript
case 12 /* Lookup */: {
    const target = evaluate(ast.target, context);
    const key = evaluate(ast.key, context);
    if (target === null || target === undefined) {
        throw new EvaluationError(`Cannot read property of ${target}`);
    }
    return target[key];
}

case 15 /* ObjLookup */: {
    const obj = evaluate(ast.obj, context);
    if (obj === null || obj === undefined) {
        throw new EvaluationError(`Cannot read property of ${obj}`);
    }
    return obj[ast.key];
}
```

### 6. 条件表达式
```javascript
case 13 /* If */: {
    const condition = evaluate(ast.condition, context);
    if (isTrue(condition)) {
        return evaluate(ast.ifTrue, context);
    } else {
        return evaluate(ast.ifFalse, context);
    }
}
```

## 实际使用示例

### 1. 基本表达式求值
```javascript
import { evaluate } from "@web/core/py_js/py_interpreter";
import { parse } from "@web/core/py_js/py_parser";
import { tokenize } from "@web/core/py_js/py_tokenizer";

function evaluateExpression(expression, context = {}) {
    const tokens = tokenize(expression);
    const ast = parse(tokens);
    return evaluate(ast, context);
}

// 数学运算
console.log(evaluateExpression("2 + 3 * 4")); // 14

// 字符串操作
console.log(evaluateExpression("'hello' + ' world'")); // "hello world"

// 布尔运算
console.log(evaluateExpression("True and False")); // false

// 比较运算
console.log(evaluateExpression("5 > 3")); // true
```

### 2. 变量和上下文
```javascript
const context = {
    x: 10,
    y: 20,
    name: "Alice",
    items: [1, 2, 3, 4, 5]
};

// 变量访问
console.log(evaluateExpression("x + y", context)); // 30

// 列表操作
console.log(evaluateExpression("len(items)", { ...context, len: (arr) => arr.length })); // 5

// 成员检测
console.log(evaluateExpression("3 in items", context)); // true

// 字符串格式化
console.log(evaluateExpression("'Hello, ' + name", context)); // "Hello, Alice"
```

### 3. 复杂数据结构
```javascript
const complexContext = {
    user: {
        name: "Bob",
        age: 25,
        skills: ["Python", "JavaScript", "SQL"]
    },
    config: {
        debug: true,
        timeout: 30
    }
};

// 对象属性访问
console.log(evaluateExpression("user.name", complexContext)); // "Bob"

// 嵌套访问
console.log(evaluateExpression("user.skills[0]", complexContext)); // "Python"

// 条件表达式
console.log(evaluateExpression("'Debug mode' if config.debug else 'Production'", complexContext)); 
// "Debug mode"

// 复杂条件
console.log(evaluateExpression("user.age >= 18 and 'Python' in user.skills", complexContext)); 
// true
```

### 4. 函数调用
```javascript
const functionContext = {
    max: Math.max,
    min: Math.min,
    abs: Math.abs,
    len: (arr) => arr.length,
    sum: (arr) => arr.reduce((a, b) => a + b, 0),
    filter: (arr, fn) => arr.filter(fn),
    map: (arr, fn) => arr.map(fn)
};

// 内置函数
console.log(evaluateExpression("max(1, 5, 3)", functionContext)); // 5
console.log(evaluateExpression("abs(-10)", functionContext)); // 10

// 自定义函数
console.log(evaluateExpression("sum([1, 2, 3, 4])", functionContext)); // 10

// 带关键字参数的函数
const advancedContext = {
    ...functionContext,
    range: (start, stop = null, step = 1, kwargs = {}) => {
        if (stop === null) {
            stop = start;
            start = 0;
        }
        const result = [];
        for (let i = start; i < stop; i += step) {
            result.push(i);
        }
        return result;
    }
};

console.log(evaluateExpression("range(5)", advancedContext)); // [0, 1, 2, 3, 4]
console.log(evaluateExpression("range(1, 10, step=2)", advancedContext)); // [1, 3, 5, 7, 9]
```

### 5. 日期时间处理
```javascript
import { PyDate, PyDateTime, PyTimeDelta } from "@web/core/py_js/py_date";

const dateContext = {
    today: PyDate.today(),
    now: PyDateTime.now(),
    timedelta: (days = 0, kwargs = {}) => new PyTimeDelta(days * 24 * 60 * 60 * 1000)
};

// 日期运算
const tomorrow = evaluateExpression("today + timedelta(days=1)", dateContext);
console.log(tomorrow.strftime("%Y-%m-%d"));

// 日期比较
console.log(evaluateExpression("today < now", dateContext)); // true (日期 < 日期时间)

// 日期格式化
console.log(evaluateExpression("today.strftime('%Y年%m月%d日')", dateContext));
```

### 6. 表达式求值器类
```javascript
class PythonExpressionEvaluator {
    constructor(baseContext = {}) {
        this.baseContext = {
            // 内置函数
            bool: BUILTINS.bool,
            len: (obj) => obj.length || Object.keys(obj).length,
            str: (obj) => String(obj),
            int: (obj) => parseInt(obj),
            float: (obj) => parseFloat(obj),
            
            // 数学函数
            abs: Math.abs,
            max: Math.max,
            min: Math.min,
            round: Math.round,
            
            // 日期时间
            today: PyDate.today(),
            now: PyDateTime.now(),
            
            ...baseContext
        };
    }
    
    evaluate(expression, context = {}) {
        const fullContext = { ...this.baseContext, ...context };
        
        try {
            const tokens = tokenize(expression);
            const ast = parse(tokens);
            return evaluate(ast, fullContext);
        } catch (error) {
            throw new Error(`Expression evaluation failed: ${error.message}`);
        }
    }
    
    evaluateCondition(expression, context = {}) {
        const result = this.evaluate(expression, context);
        return BUILTINS.bool(result);
    }
    
    evaluateTemplate(template, context = {}) {
        // 简单的模板求值：支持 ${expression} 语法
        return template.replace(/\$\{([^}]+)\}/g, (match, expr) => {
            try {
                return String(this.evaluate(expr, context));
            } catch (error) {
                return match; // 保留原始表达式
            }
        });
    }
    
    validateExpression(expression) {
        try {
            const tokens = tokenize(expression);
            const ast = parse(tokens);
            return { valid: true, ast };
        } catch (error) {
            return { valid: false, error: error.message };
        }
    }
    
    getVariables(expression) {
        try {
            const tokens = tokenize(expression);
            const ast = parse(tokens);
            const variables = new Set();
            this.extractVariables(ast, variables);
            return Array.from(variables);
        } catch (error) {
            return [];
        }
    }
    
    extractVariables(ast, variables) {
        if (!ast || typeof ast !== 'object') return;
        
        if (ast.type === 5 /* Name */) {
            variables.add(ast.value);
        }
        
        // 递归提取
        if (ast.left) this.extractVariables(ast.left, variables);
        if (ast.right) this.extractVariables(ast.right, variables);
        if (ast.args) ast.args.forEach(arg => this.extractVariables(arg, variables));
        if (ast.value && Array.isArray(ast.value)) {
            ast.value.forEach(item => this.extractVariables(item, variables));
        }
    }
}

// 使用示例
const evaluator = new PythonExpressionEvaluator();

// 基本求值
console.log(evaluator.evaluate("2 + 3 * 4")); // 14

// 条件求值
console.log(evaluator.evaluateCondition("5 > 3")); // true

// 模板求值
const template = "Hello ${name}, you have ${count} messages";
const result = evaluator.evaluateTemplate(template, { name: "Alice", count: 5 });
console.log(result); // "Hello Alice, you have 5 messages"

// 表达式验证
const validation = evaluator.validateExpression("x + y * z");
console.log(validation.valid); // true

// 变量提取
const variables = evaluator.getVariables("user.name + ' (' + user.age + ')'");
console.log(variables); // ["user"]
```

## 设计模式分析

### 1. 解释器模式 (Interpreter Pattern)
```javascript
function evaluate(ast, context) {
    switch (ast.type) {
        case 0: return ast.value;
        case 5: return context[ast.value];
        case 7: return applyBinaryOp(ast, context);
        // ...
    }
}
```

**特点**:
- **语法树遍历**: 递归遍历 AST 节点
- **类型分发**: 根据节点类型选择处理方法
- **上下文传递**: 在递归过程中传递执行上下文

### 2. 策略模式 (Strategy Pattern)
```javascript
function applyBinaryOp(ast, context) {
    switch (ast.op) {
        case "+": return handleAddition(left, right);
        case "-": return handleSubtraction(left, right);
        // ...
    }
}
```

**应用**:
- **操作符策略**: 不同操作符使用不同的处理策略
- **类型策略**: 不同数据类型使用不同的操作策略
- **比较策略**: 不同比较操作使用不同的比较策略

### 3. 访问者模式 (Visitor Pattern)
```javascript
// 每个 AST 节点类型都有对应的处理方法
case 8 /* FunctionCall */: return handleFunctionCall(ast, context);
case 12 /* Lookup */: return handleLookup(ast, context);
case 13 /* If */: return handleConditional(ast, context);
```

### 4. 上下文模式 (Context Pattern)
```javascript
function evaluate(ast, context) {
    // context 对象维护执行环境
    if (ast.value in context) {
        return context[ast.value];
    }
}
```

## 性能优化

### 1. 类型检查优化
```javascript
// 快速类型检查
if (typeof left === "number" && typeof right === "number") {
    return left + right; // 快速路径
}
// 复杂类型处理
```

### 2. 上下文缓存
```javascript
class CachedEvaluator {
    constructor() {
        this.cache = new Map();
    }
    
    evaluate(expression, context) {
        const key = `${expression}:${JSON.stringify(context)}`;
        if (this.cache.has(key)) {
            return this.cache.get(key);
        }
        
        const result = this.doEvaluate(expression, context);
        this.cache.set(key, result);
        return result;
    }
}
```

### 3. AST 重用
```javascript
class OptimizedEvaluator {
    constructor() {
        this.astCache = new Map();
    }
    
    evaluate(expression, context) {
        let ast = this.astCache.get(expression);
        if (!ast) {
            const tokens = tokenize(expression);
            ast = parse(tokens);
            this.astCache.set(expression, ast);
        }
        
        return evaluate(ast, context);
    }
}
```

## 最佳实践

### 1. 错误处理
```javascript
// ✅ 推荐：提供详细的错误信息
if (!(ast.value in context)) {
    throw new EvaluationError(`Undefined variable: ${ast.value}`);
}

// ❌ 避免：模糊的错误信息
throw new Error("Evaluation failed");
```

### 2. 类型安全
```javascript
// ✅ 推荐：严格的类型检查
if (typeof fn !== "function") {
    throw new EvaluationError(`${ast.fn.value} is not a function`);
}

// ❌ 避免：不安全的类型假设
return fn(...args); // fn 可能不是函数
```

### 3. 上下文管理
```javascript
// ✅ 推荐：不可变的上下文传递
const newContext = { ...context, localVar: value };
return evaluate(ast, newContext);

// ❌ 避免：修改原始上下文
context.localVar = value; // 可能影响其他求值
```

## 总结

Python 解释器是 Odoo Web 客户端 Python-JavaScript 桥接系统的执行引擎，它提供了：
- **完整的 Python 语义**: 实现与 Python 一致的表达式求值语义
- **类型系统支持**: 正确处理 Python 的类型比较和转换
- **丰富的操作符**: 支持所有 Python 表达式操作符
- **上下文管理**: 灵活的变量作用域和环境管理
- **错误处理**: 详细的错误报告和异常处理

这个解释器为 Odoo 的 Python 表达式系统提供了可靠的执行环境，确保了 Python 代码在 JavaScript 环境中的正确执行和一致的行为。

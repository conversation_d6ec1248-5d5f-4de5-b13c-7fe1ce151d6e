# Odoo Python 词法分析器 (Python Tokenizer) 学习资料

## 文件概述

**文件路径**: `output/@web/core/py_js/py_tokenizer.js`  
**原始路径**: `/web/static/src/core/py_js/py_tokenizer.js`  
**模块类型**: 核心基础模块 - Python 词法分析器  
**代码行数**: 322 行  
**依赖关系**: 无外部依赖

## 模块功能

Python 词法分析器是 Odoo Web 客户端 Python-JavaScript 桥接系统的词法分析组件，负责：
- 将 Python 源代码字符串分解为标记（tokens）
- 处理 Python 字符串字面量的转义序列
- 识别 Python 的各种语法元素（数字、字符串、符号、名称、常量）
- 提供准确的 Python 词法分析，为语法分析器提供输入

## 类型定义

### Token 类型系统
```javascript
/**
 * @typedef {{type: 0, value: number}} TokenNumber
 * @typedef {{type: 1, value: string}} TokenString
 * @typedef {{type: 2, value: string}} TokenSymbol
 * @typedef {{type: 3, value: string}} TokenName
 * @typedef {{type: 4, value: string}} TokenConstant
 * @typedef {TokenNumber | TokenString | TokenSymbol | TokenName | TokenConstant} Token
 */
```

**Token 类型枚举**:
- **0 (Number)**: 数字字面量（整数、浮点数）
- **1 (String)**: 字符串字面量
- **2 (Symbol)**: 符号（操作符、分隔符、关键字）
- **3 (Name)**: 标识符（变量名、函数名等）
- **4 (Constant)**: 常量（None、True、False）

### TokenizerError 异常类
```javascript
const TokenizerError = class TokenizerError extends Error {}
```

**功能**: 专门用于词法分析过程中的错误处理

## 字符串字面量处理

### 1. 转义字符映射
```javascript
const directMap = {
    "\\": "\\",
    '"': '"',
    "'": "'",
    a: "\x07",    // 响铃
    b: "\x08",    // 退格
    f: "\x0c",    // 换页
    n: "\n",      // 换行
    r: "\r",      // 回车
    t: "\t",      // 制表符
    v: "\v",      // 垂直制表符
};
```

### 2. decodeStringLiteral 函数
```javascript
function decodeStringLiteral(str, unicode) {
    const out = [];
    let code;
    for (var i = 0; i < str.length; ++i) {
        if (str[i] !== "\\") {
            out.push(str[i]);
            continue;
        }
        var escape = str[i + 1];
        // 处理各种转义序列...
    }
    return out.join("");
}
```

**支持的转义序列**:

#### 直接映射转义
- `\\` → `\`
- `\"` → `"`
- `\'` → `'`
- `\a` → 响铃字符
- `\b` → 退格字符
- `\f` → 换页字符
- `\n` → 换行字符
- `\r` → 回车字符
- `\t` → 制表符
- `\v` → 垂直制表符

#### Unicode 转义（仅在 unicode 模式下）
```javascript
case "u":
    if (!unicode) break;
    var uni = str.slice(i + 2, i + 6);
    if (!/[0-9a-f]{4}/i.test(uni)) {
        throw new TokenizerError("truncated \\uXXXX escape");
    }
    code = parseInt(uni, 16);
    out.push(String.fromCharCode(code));
    i += 5;
    continue;
```

#### 十六进制转义
```javascript
case "x":
    var hex = str.slice(i + 2, i + 4);
    if (!/[0-9a-f]{2}/i.test(hex)) {
        throw new TokenizerError("invalid \\x escape");
    }
    code = parseInt(hex, 16);
    out.push(String.fromCharCode(code));
    i += 3;
    continue;
```

#### 八进制转义
```javascript
default:
    if (!/[0-8]/.test(escape)) break;
    var r = /[0-8]{1,3}/g;
    r.lastIndex = i + 1;
    var m = r.exec(str);
    var oct = m[0];
    code = parseInt(oct, 8);
    out.push(String.fromCharCode(code));
    i += oct.length;
    continue;
```

## 语法元素定义

### 1. 常量集合
```javascript
const constants = new Set(["None", "False", "True"]);
```

### 2. 比较操作符
```javascript
const comparators = [
    "in", "not", "not in", "is", "is not",
    "<", "<=", ">", ">=", "<>", "!=", "=="
];
```

### 3. 二元操作符
```javascript
const binaryOperators = [
    "or", "and", "|", "^", "&", "<<", ">>",
    "+", "-", "*", "/", "//", "%", "~", "**", "."
];
```

### 4. 一元操作符
```javascript
const unaryOperators = ["-"];
```

### 5. 符号集合
```javascript
const symbols = new Set([
    ...["(", ")", "[", "]", "{", "}", ":", ","],
    ...["if", "else", "lambda", "="],
    ...comparators,
    ...binaryOperators,
    ...unaryOperators,
]);
```

## 正则表达式模式

### 1. 基础模式
```javascript
const Name = "[a-zA-Z_]\\w*";                    // 标识符
const Whitespace = "[ \\f\\t]*";                 // 空白字符
const DecNumber = "\\d+(L|l)?";                  // 十进制数
const IntNumber = DecNumber;                     // 整数
```

### 2. 浮点数模式
```javascript
const Exponent = "[eE][+-]?\\d+";                // 指数部分
const PointFloat = group(
    `\\d+\\.\\d*(${Exponent})?`,                 // 123.456e10
    `\\.\\d+(${Exponent})?`                      // .456e10
);
const FloatNumber = group(PointFloat, `\\d+${Exponent}`); // 123e10
```

### 3. 复合模式
```javascript
const Number = group(FloatNumber, IntNumber);     // 所有数字
const Operator = group(
    "\\*\\*=?", ">>=?", "<<=?", "<>", "!=", 
    "//=?", "[+\\-*/%&|^=<>]=?", "~"
);
const Bracket = "[\\[\\]\\(\\)\\{\\}]";          // 括号
const Special = "[:;.,`@]";                       // 特殊字符
const Funny = group(Operator, Bracket, Special);  // 有趣字符
```

### 4. 字符串模式
```javascript
const ContStr = group(
    "([uU])?'([^\n'\\\\]*(?:\\\\.[^\n'\\\\]*)*)'",    // 单引号字符串
    '([uU])?"([^\n"\\\\]*(?:\\\\.[^\n"\\\\]*)*)"'     // 双引号字符串
);
```

### 5. 伪标记模式
```javascript
const PseudoToken = Whitespace + group(Number, Funny, ContStr, Name);
```

## 核心 tokenize 函数

### 1. 函数签名
```javascript
function tokenize(str) {
    const tokens = [];
    const max = str.length;
    let start = 0;
    let end = 0;
    const pseudoprog = new RegExp(PseudoToken, "g");
    // 主循环...
    return tokens;
}
```

### 2. 主要处理流程

#### 正则匹配循环
```javascript
while (pseudoprog.lastIndex < max) {
    const pseudomatch = pseudoprog.exec(str);
    if (!pseudomatch) {
        // 处理匹配失败
        if (/^\s+$/.test(str.slice(end))) {
            break; // 尾部空白，结束分析
        }
        throw new TokenizerError("Failed to tokenize");
    }
    // 处理匹配成功的情况...
}
```

#### 空白字符处理
```javascript
if (pseudomatch.index > end) {
    if (str.slice(end, pseudomatch.index).trim()) {
        throw new TokenizerError("Invalid expression");
    }
}
```

#### 标记分类处理
```javascript
let token = str.slice(start, end).replace(strip, "");

if (NumberPattern.test(token)) {
    // 数字标记
    tokens.push({
        type: 0 /* Number */,
        value: parseFloat(token),
    });
} else if (StringPattern.test(token)) {
    // 字符串标记
    var m = StringPattern.exec(token);
    tokens.push({
        type: 1 /* String */,
        value: decodeStringLiteral(
            m[3] !== undefined ? m[3] : m[5], 
            !!(m[2] || m[4])
        ),
    });
} else if (symbols.has(token)) {
    // 符号标记（包括特殊处理）
    // ...
} else if (constants.has(token)) {
    // 常量标记
    tokens.push({
        type: 4 /* Constant */,
        value: token,
    });
} else if (NamePattern.test(token)) {
    // 名称标记
    tokens.push({
        type: 3 /* Name */,
        value: token,
    });
}
```

### 3. 特殊符号处理
```javascript
// 处理复合操作符
if (token === "in" && tokens.length > 0 && tokens[tokens.length - 1].value === "not") {
    token = "not in";
    tokens.pop();
} else if (
    token === "not" &&
    tokens.length > 0 &&
    tokens[tokens.length - 1].value === "is"
) {
    token = "is not";
    tokens.pop();
}
```

## 实际使用示例

### 1. 基本词法分析
```javascript
import { tokenize } from "@web/core/py_js/py_tokenizer";

// 数字分析
const numberTokens = tokenize("123 45.67 1.23e-4");
console.log(numberTokens);
// [
//   {type: 0, value: 123},
//   {type: 0, value: 45.67},
//   {type: 0, value: 0.000123}
// ]

// 字符串分析
const stringTokens = tokenize('"hello" \'world\' u"unicode"');
console.log(stringTokens);
// [
//   {type: 1, value: "hello"},
//   {type: 1, value: "world"},
//   {type: 1, value: "unicode"}
// ]

// 表达式分析
const exprTokens = tokenize("x + y * 2");
console.log(exprTokens);
// [
//   {type: 3, value: "x"},
//   {type: 2, value: "+"},
//   {type: 3, value: "y"},
//   {type: 2, value: "*"},
//   {type: 0, value: 2}
// ]
```

### 2. 复杂表达式分析
```javascript
class PythonExpressionTokenizer {
    constructor() {
        this.tokenize = tokenize;
    }
    
    analyzeExpression(expression) {
        try {
            const tokens = this.tokenize(expression);
            return this.categorizeTokens(tokens);
        } catch (error) {
            if (error instanceof TokenizerError) {
                throw new Error(`Tokenization failed: ${error.message}`);
            }
            throw error;
        }
    }
    
    categorizeTokens(tokens) {
        const categories = {
            numbers: [],
            strings: [],
            symbols: [],
            names: [],
            constants: []
        };
        
        tokens.forEach(token => {
            switch (token.type) {
                case 0: categories.numbers.push(token.value); break;
                case 1: categories.strings.push(token.value); break;
                case 2: categories.symbols.push(token.value); break;
                case 3: categories.names.push(token.value); break;
                case 4: categories.constants.push(token.value); break;
            }
        });
        
        return categories;
    }
    
    validateSyntax(expression) {
        const tokens = this.tokenize(expression);
        
        // 检查括号匹配
        const brackets = { '(': ')', '[': ']', '{': '}' };
        const stack = [];
        
        for (const token of tokens) {
            if (token.type === 2) { // Symbol
                if (token.value in brackets) {
                    stack.push(token.value);
                } else if (Object.values(brackets).includes(token.value)) {
                    const last = stack.pop();
                    if (brackets[last] !== token.value) {
                        throw new Error(`Mismatched brackets: ${last} and ${token.value}`);
                    }
                }
            }
        }
        
        if (stack.length > 0) {
            throw new Error(`Unclosed brackets: ${stack.join(', ')}`);
        }
        
        return true;
    }
}

// 使用示例
const tokenizer = new PythonExpressionTokenizer();

// 分析复杂表达式
const analysis = tokenizer.analyzeExpression("func(x, 'hello', [1, 2, 3])");
console.log(analysis);
// {
//   numbers: [1, 2, 3],
//   strings: ["hello"],
//   symbols: ["(", ",", ",", "[", ",", "]", ")"],
//   names: ["func", "x"],
//   constants: []
// }

// 语法验证
tokenizer.validateSyntax("(x + y) * [1, 2]"); // true
// tokenizer.validateSyntax("(x + y] * [1, 2)"); // Error: Mismatched brackets
```

### 3. 字符串转义处理
```javascript
class StringLiteralProcessor {
    constructor() {
        this.tokenize = tokenize;
    }
    
    processStringLiterals(code) {
        const tokens = this.tokenize(code);
        const stringTokens = tokens.filter(token => token.type === 1);
        
        return stringTokens.map(token => ({
            original: this.findOriginalString(code, token.value),
            decoded: token.value,
            length: token.value.length,
            hasEscapes: this.hasEscapeSequences(token.value)
        }));
    }
    
    findOriginalString(code, decodedValue) {
        // 简化实现：在实际代码中查找原始字符串
        const stringPattern = /(['"])((?:\\.|(?!\1)[^\\])*)\1/g;
        let match;
        
        while ((match = stringPattern.exec(code)) !== null) {
            try {
                const testTokens = this.tokenize(match[0]);
                if (testTokens.length === 1 && testTokens[0].value === decodedValue) {
                    return match[0];
                }
            } catch (e) {
                // 继续查找
            }
        }
        return null;
    }
    
    hasEscapeSequences(str) {
        return /\\[\\'"abfnrtv]|\\x[0-9a-fA-F]{2}|\\u[0-9a-fA-F]{4}|\\[0-7]{1,3}/.test(str);
    }
}

// 使用示例
const processor = new StringLiteralProcessor();

const result = processor.processStringLiterals('"hello\\nworld" \'test\\x41\'');
console.log(result);
// [
//   {
//     original: '"hello\\nworld"',
//     decoded: "hello\nworld",
//     length: 11,
//     hasEscapes: true
//   },
//   {
//     original: "'test\\x41'",
//     decoded: "testA",
//     length: 5,
//     hasEscapes: true
//   }
// ]
```

### 4. 词法分析器扩展
```javascript
class ExtendedPythonTokenizer {
    constructor() {
        this.baseTokenize = tokenize;
        this.customKeywords = new Set(['async', 'await', 'nonlocal']);
    }
    
    tokenizeWithExtensions(code) {
        const baseTokens = this.baseTokenize(code);
        
        return baseTokens.map(token => {
            if (token.type === 3 && this.customKeywords.has(token.value)) {
                // 将自定义关键字标记为符号
                return { ...token, type: 2 };
            }
            return token;
        });
    }
    
    getTokenStatistics(code) {
        const tokens = this.baseTokenize(code);
        const stats = {
            total: tokens.length,
            byType: { 0: 0, 1: 0, 2: 0, 3: 0, 4: 0 },
            uniqueNames: new Set(),
            operators: new Set(),
            complexity: 0
        };
        
        tokens.forEach(token => {
            stats.byType[token.type]++;
            
            if (token.type === 3) {
                stats.uniqueNames.add(token.value);
            } else if (token.type === 2) {
                stats.operators.add(token.value);
                if (['+', '-', '*', '/', '**', '//', '%'].includes(token.value)) {
                    stats.complexity++;
                }
            }
        });
        
        return {
            ...stats,
            uniqueNames: Array.from(stats.uniqueNames),
            operators: Array.from(stats.operators)
        };
    }
    
    formatTokens(code) {
        const tokens = this.baseTokenize(code);
        
        return tokens.map(token => {
            const typeNames = ['Number', 'String', 'Symbol', 'Name', 'Constant'];
            return `${typeNames[token.type]}(${JSON.stringify(token.value)})`;
        }).join(' ');
    }
}

// 使用示例
const extendedTokenizer = new ExtendedPythonTokenizer();

// 获取统计信息
const stats = extendedTokenizer.getTokenStatistics("x + y * func(a, b) ** 2");
console.log(stats);
// {
//   total: 11,
//   byType: {0: 1, 1: 0, 2: 6, 3: 4, 4: 0},
//   uniqueNames: ["x", "y", "func", "a", "b"],
//   operators: ["+", "*", "(", ",", ")", "**"],
//   complexity: 3
// }

// 格式化输出
const formatted = extendedTokenizer.formatTokens("x + 1");
console.log(formatted);
// Name("x") Symbol("+") Number(1)
```

### 5. 错误处理和调试
```javascript
class DebugTokenizer {
    constructor() {
        this.tokenize = tokenize;
        this.debugMode = false;
    }
    
    setDebugMode(enabled) {
        this.debugMode = enabled;
    }
    
    safeTokenize(code) {
        try {
            const tokens = this.tokenize(code);
            
            if (this.debugMode) {
                console.log(`Successfully tokenized: "${code}"`);
                console.log(`Generated ${tokens.length} tokens:`, tokens);
            }
            
            return { success: true, tokens, error: null };
        } catch (error) {
            if (this.debugMode) {
                console.error(`Tokenization failed for: "${code}"`);
                console.error(`Error: ${error.message}`);
            }
            
            return { 
                success: false, 
                tokens: [], 
                error: error.message,
                position: this.extractErrorPosition(error.message)
            };
        }
    }
    
    extractErrorPosition(errorMessage) {
        const match = errorMessage.match(/at index (\d+)/);
        return match ? parseInt(match[1]) : null;
    }
    
    validateAndTokenize(code) {
        // 预检查常见问题
        const issues = this.preValidate(code);
        if (issues.length > 0) {
            return {
                success: false,
                tokens: [],
                error: `Pre-validation failed: ${issues.join(', ')}`,
                issues
            };
        }
        
        return this.safeTokenize(code);
    }
    
    preValidate(code) {
        const issues = [];
        
        // 检查未闭合的字符串
        const stringPattern = /(['"])/g;
        const quotes = [];
        let match;
        
        while ((match = stringPattern.exec(code)) !== null) {
            quotes.push({ quote: match[1], index: match.index });
        }
        
        // 简单的字符串闭合检查
        let openQuote = null;
        for (const quote of quotes) {
            if (openQuote === null) {
                openQuote = quote;
            } else if (openQuote.quote === quote.quote) {
                openQuote = null;
            }
        }
        
        if (openQuote !== null) {
            issues.push(`Unclosed string starting at position ${openQuote.index}`);
        }
        
        return issues;
    }
}

// 使用示例
const debugTokenizer = new DebugTokenizer();
debugTokenizer.setDebugMode(true);

// 成功的情况
const result1 = debugTokenizer.validateAndTokenize("x + 1");
console.log(result1.success); // true

// 失败的情况
const result2 = debugTokenizer.validateAndTokenize('"unclosed string');
console.log(result2.success); // false
console.log(result2.issues); // ["Unclosed string starting at position 0"]
```

## 设计模式分析

### 1. 状态机模式 (State Machine Pattern)
```javascript
function decodeStringLiteral(str, unicode) {
    // 状态：普通字符 vs 转义序列
    for (var i = 0; i < str.length; ++i) {
        if (str[i] !== "\\") {
            // 普通字符状态
            out.push(str[i]);
        } else {
            // 转义序列状态
            // 根据下一个字符决定处理方式
        }
    }
}
```

### 2. 策略模式 (Strategy Pattern)
```javascript
// 不同类型的标记使用不同的处理策略
if (NumberPattern.test(token)) {
    // 数字处理策略
} else if (StringPattern.test(token)) {
    // 字符串处理策略
} else if (symbols.has(token)) {
    // 符号处理策略
}
```

### 3. 工厂模式 (Factory Pattern)
```javascript
// 根据匹配结果创建不同类型的标记
function createToken(tokenString) {
    if (NumberPattern.test(tokenString)) {
        return { type: 0, value: parseFloat(tokenString) };
    }
    // 其他类型...
}
```

## 性能优化

### 1. 正则表达式优化
```javascript
// 预编译正则表达式
const NumberPattern = new RegExp("^" + Number + "$");
const StringPattern = new RegExp("^" + ContStr + "$");
const NamePattern = new RegExp("^" + Name + "$");

// 使用全局标志进行连续匹配
const pseudoprog = new RegExp(PseudoToken, "g");
```

### 2. 字符串处理优化
```javascript
// 使用数组拼接而不是字符串连接
const out = [];
// ... 处理过程
return out.join("");
```

### 3. 集合查找优化
```javascript
// 使用 Set 进行快速查找
const constants = new Set(["None", "False", "True"]);
const symbols = new Set([...]);

// O(1) 查找时间
if (constants.has(token)) { /* ... */ }
```

## 最佳实践

### 1. 错误处理
```javascript
// ✅ 推荐：提供详细的错误信息
throw new TokenizerError(
    "Failed to tokenize <<" + str + ">> at index " + (end || 0) + 
    "; parsed so far: " + tokens
);

// ❌ 避免：模糊的错误信息
throw new Error("Tokenization failed");
```

### 2. 类型安全
```javascript
// ✅ 推荐：明确的类型标记
tokens.push({
    type: 0 /* Number */,
    value: parseFloat(token),
});

// ❌ 避免：魔法数字
tokens.push({ type: 0, value: parseFloat(token) });
```

### 3. 性能考虑
```javascript
// ✅ 推荐：预编译正则表达式
const pattern = new RegExp(expression);

// ❌ 避免：重复编译
while (condition) {
    const pattern = new RegExp(expression); // 每次都编译
}
```

## 总结

Python 词法分析器是 Odoo Web 客户端 Python-JavaScript 桥接系统的基础组件，它提供了：
- **完整的 Python 词法支持**: 支持数字、字符串、符号、名称、常量等所有 Python 语法元素
- **准确的字符串处理**: 正确处理 Python 字符串字面量的各种转义序列
- **高性能的分析**: 使用优化的正则表达式和数据结构
- **详细的错误报告**: 提供准确的错误位置和上下文信息
- **扩展性**: 易于扩展支持新的语法元素

这个词法分析器为 Python 表达式的语法分析和执行提供了可靠的基础，确保了 Python 代码在 JavaScript 环境中的正确解析。

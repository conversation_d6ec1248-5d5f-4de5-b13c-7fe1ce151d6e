# Odoo Python 日期时间处理 (Python Date/Time) 学习资料

## 文件概述

**文件路径**: `output/@web/core/py_js/py_date.js`  
**原始路径**: `/web/static/src/core/py_js/py_date.js`  
**模块类型**: 核心基础模块 - Python 日期时间类型  
**代码行数**: 903 行  
**依赖关系**: 
- `@web/core/py_js/py_parser` - Python 语法分析器（parseArgs 函数）

## 模块功能

Python 日期时间模块是 Odoo Web 客户端 Python-JavaScript 桥接系统的日期时间处理组件，负责：
- 实现 Python datetime 模块的核心类（date、time、datetime、timedelta、relativedelta）
- 提供与 Python 一致的日期时间计算和格式化
- 处理闰年、月份天数、时区等复杂的日期时间逻辑
- 支持日期时间的算术运算和比较操作
- 实现 strftime 格式化功能

## 异常类型

### 1. 自定义异常
```javascript
const AssertionError = class AssertionError extends Error {}
const ValueError = class ValueError extends Error {}
const NotSupportedError = class NotSupportedError extends Error {}
```

**用途**:
- **AssertionError**: 断言失败时抛出
- **ValueError**: 值错误（如无效的日期）
- **NotSupportedError**: 不支持的操作

## 核心工具函数

### 1. 格式化函数
```javascript
function fmt2(n) {
    return String(n).padStart(2, "0");
}
function fmt4(n) {
    return String(n).padStart(4, "0");
}
```

**功能**: 数字格式化，确保固定位数（2位或4位，前导零填充）

### 2. divmod 函数
```javascript
function divmod(a, b, fn) {
    let mod = a % b;
    // Python 语义：sign(a % b) === sign(b)
    if ((mod > 0 && b < 0) || (mod < 0 && b > 0)) {
        mod += b;
    }
    return fn(Math.floor(a / b), mod);
}
```

**Python 语义实现**:
- **除法**: 向下取整除法
- **取模**: 结果符号与除数相同
- **回调**: 将商和余数传递给回调函数

### 3. 日期计算常量
```javascript
const DAYS_IN_MONTH = [null, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
const DAYS_BEFORE_MONTH = [null]; // 动态计算每月前的累计天数

const DI400Y = daysBeforeYear(401);  // 400年的天数
const DI100Y = daysBeforeYear(101);  // 100年的天数
const DI4Y = daysBeforeYear(5);      // 4年的天数
```

### 4. 闰年判断
```javascript
function isLeap(year) {
    return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);
}
```

**闰年规则**:
- 能被4整除 **且** (不能被100整除 **或** 能被400整除)
- 例：2000年是闰年，1900年不是闰年

### 5. 日期序数转换
```javascript
function ymd2ord(year, month, day) {
    const dim = daysInMonth(year, month);
    if (!(1 <= day && day <= dim)) {
        throw new ValueError(`day must be in 1..${dim}`);
    }
    return daysBeforeYear(year) + daysBeforeMonth(year, month) + day;
}

function ord2ymd(n) {
    // 复杂的算法将序数转换回年月日
    // 使用400年、100年、4年的周期性优化计算
}
```

**序数系统**:
- **ymd2ord**: 将年月日转换为从公元1年1月1日开始的天数
- **ord2ymd**: 将天数转换回年月日
- **用途**: 日期算术运算的基础

## PyDate 类

### 1. 类定义和构造
```javascript
class PyDate {
    constructor(year, month, day) {
        this.year = year;
        this.month = month; // 1-indexed (1=January)
        this.day = day;     // 1-indexed (1=first day)
    }
    
    static create(...args) {
        const { year, month, day } = parseArgs(args, ["year", "month", "day"]);
        return new PyDate(year, month, day);
    }
    
    static today() {
        return this.convertDate(new Date());
    }
    
    static convertDate(date) {
        const year = date.getFullYear();
        const month = date.getMonth() + 1; // JavaScript 月份是0-indexed
        const day = date.getDate();
        return new PyDate(year, month, day);
    }
}
```

### 2. 日期运算
```javascript
add(timedelta) {
    const s = tmxxx(this.year, this.month, this.day + timedelta.days);
    return new PyDate(s.year, s.month, s.day);
}

substract(other) {
    if (other instanceof PyDate) {
        const a = ymd2ord(this.year, this.month, this.day);
        const b = ymd2ord(other.year, other.month, other.day);
        return new PyTimeDelta((a - b) * 24 * 60 * 60 * 1000);
    }
    if (other instanceof PyTimeDelta) {
        return this.add(other.negate());
    }
    throw new NotSupportedError();
}
```

### 3. 格式化方法
```javascript
strftime(format) {
    return format.replace(/%([A-Za-z])/g, (m, c) => {
        switch (c) {
            case "Y": return fmt4(this.year);      // 4位年份
            case "m": return fmt2(this.month);     // 2位月份
            case "d": return fmt2(this.day);       // 2位日期
            case "j": return String(this.dayOfYear()); // 年中第几天
            case "w": return String(this.weekday()); // 星期几
            case "A": return this.strfweekday();   // 星期名称
            case "B": return this.strfmonth();     // 月份名称
            default: return m;
        }
    });
}
```

## PyTime 类

### 1. 时间表示
```javascript
class PyTime {
    constructor(hour = 0, minute = 0, second = 0, microsecond = 0) {
        this.hour = hour;
        this.minute = minute;
        this.second = second;
        this.microsecond = microsecond;
    }
    
    static create(...args) {
        const { hour, minute, second, microsecond } = parseArgs(args, 
            ["hour", "minute", "second", "microsecond"]);
        return new PyTime(hour, minute, second, microsecond);
    }
}
```

### 2. 时间格式化
```javascript
strftime(format) {
    return format.replace(/%([A-Za-z])/g, (m, c) => {
        switch (c) {
            case "H": return fmt2(this.hour);      // 24小时制小时
            case "I": return fmt2(this.hour % 12 || 12); // 12小时制小时
            case "M": return fmt2(this.minute);    // 分钟
            case "S": return fmt2(this.second);    // 秒
            case "f": return String(this.microsecond).padStart(6, "0"); // 微秒
            case "p": return this.hour < 12 ? "AM" : "PM"; // AM/PM
            default: return m;
        }
    });
}
```

## PyDateTime 类

### 1. 日期时间组合
```javascript
class PyDateTime extends PyDate {
    constructor(year, month, day, hour = 0, minute = 0, second = 0, microsecond = 0) {
        super(year, month, day);
        this.hour = hour;
        this.minute = minute;
        this.second = second;
        this.microsecond = microsecond;
    }
    
    static now() {
        const d = new Date();
        return new PyDateTime(
            d.getFullYear(),
            d.getMonth() + 1,
            d.getDate(),
            d.getHours(),
            d.getMinutes(),
            d.getSeconds(),
            d.getMilliseconds() * 1000
        );
    }
    
    static combine(date, time) {
        return new PyDateTime(
            date.year, date.month, date.day,
            time.hour, time.minute, time.second, time.microsecond
        );
    }
}
```

### 2. 日期时间运算
```javascript
add(timedelta) {
    const totalMicroseconds = this.microsecond + timedelta.microseconds;
    const totalSeconds = this.second + Math.floor(totalMicroseconds / 1000000);
    const totalMinutes = this.minute + Math.floor(totalSeconds / 60);
    const totalHours = this.hour + Math.floor(totalMinutes / 60);
    const totalDays = this.day + Math.floor(totalHours / 24) + timedelta.days;
    
    const s = tmxxx(this.year, this.month, totalDays, 
                   totalHours % 24, totalMinutes % 60, 
                   totalSeconds % 60, totalMicroseconds % 1000000);
    
    return new PyDateTime(s.year, s.month, s.day, s.hour, s.minute, s.second, s.microsecond);
}
```

## PyTimeDelta 类

### 1. 时间差表示
```javascript
class PyTimeDelta {
    constructor(milliseconds = 0) {
        this.milliseconds = milliseconds;
    }
    
    static create(...args) {
        const { days, seconds, microseconds, milliseconds, minutes, hours, weeks } = 
            parseArgs(args, ["days", "seconds", "microseconds", "milliseconds", "minutes", "hours", "weeks"]);
        
        let totalMs = 0;
        if (days) totalMs += days * 24 * 60 * 60 * 1000;
        if (hours) totalMs += hours * 60 * 60 * 1000;
        if (minutes) totalMs += minutes * 60 * 1000;
        if (seconds) totalMs += seconds * 1000;
        if (milliseconds) totalMs += milliseconds;
        if (microseconds) totalMs += microseconds / 1000;
        if (weeks) totalMs += weeks * 7 * 24 * 60 * 60 * 1000;
        
        return new PyTimeDelta(totalMs);
    }
    
    get days() {
        return Math.floor(this.milliseconds / (24 * 60 * 60 * 1000));
    }
    
    get seconds() {
        return Math.floor((this.milliseconds % (24 * 60 * 60 * 1000)) / 1000);
    }
    
    get microseconds() {
        return (this.milliseconds % 1000) * 1000;
    }
}
```

### 2. 时间差运算
```javascript
add(other) {
    return new PyTimeDelta(this.milliseconds + other.milliseconds);
}

substract(other) {
    return new PyTimeDelta(this.milliseconds - other.milliseconds);
}

multiply(factor) {
    return new PyTimeDelta(this.milliseconds * factor);
}

divide(divisor) {
    return new PyTimeDelta(this.milliseconds / divisor);
}

negate() {
    return new PyTimeDelta(-this.milliseconds);
}
```

## PyRelativeDelta 类

### 1. 相对日期差
```javascript
class PyRelativeDelta {
    constructor(years = 0, months = 0, days = 0, hours = 0, minutes = 0, seconds = 0) {
        this.years = years;
        this.months = months;
        this.days = days;
        this.hours = hours;
        this.minutes = minutes;
        this.seconds = seconds;
    }
    
    static create(...args) {
        const { years, months, days, hours, minutes, seconds } = 
            parseArgs(args, ["years", "months", "days", "hours", "minutes", "seconds"]);
        return new PyRelativeDelta(years, months, days, hours, minutes, seconds);
    }
}
```

### 2. 相对日期运算
```javascript
static add(date, delta) {
    let year = date.year + delta.years;
    let month = date.month + delta.months;
    let day = date.day + delta.days;
    let hour = (date.hour || 0) + delta.hours;
    let minute = (date.minute || 0) + delta.minutes;
    let second = (date.second || 0) + delta.seconds;
    
    // 处理月份溢出
    while (month > 12) {
        month -= 12;
        year += 1;
    }
    while (month < 1) {
        month += 12;
        year -= 1;
    }
    
    // 处理日期溢出（考虑月份天数）
    const maxDay = daysInMonth(year, month);
    if (day > maxDay) {
        day = maxDay;
    }
    
    const result = tmxxx(year, month, day, hour, minute, second);
    
    if (date instanceof PyDateTime) {
        return new PyDateTime(result.year, result.month, result.day, 
                            result.hour, result.minute, result.second);
    } else {
        return new PyDate(result.year, result.month, result.day);
    }
}
```

## 实际使用示例

### 1. 基本日期操作
```javascript
import { PyDate, PyDateTime, PyTimeDelta, PyRelativeDelta } from "@web/core/py_js/py_date";

// 创建日期
const today = PyDate.today();
const specificDate = new PyDate(2024, 1, 15);
const fromJS = PyDate.convertDate(new Date());

// 日期格式化
console.log(today.strftime("%Y-%m-%d"));        // "2024-01-15"
console.log(today.strftime("%Y年%m月%d日"));      // "2024年01月15日"
console.log(today.strftime("%A, %B %d, %Y"));   // "Monday, January 15, 2024"

// 日期运算
const tomorrow = today.add(new PyTimeDelta(24 * 60 * 60 * 1000)); // 加1天
const nextWeek = today.add(PyTimeDelta.create(7, 0, 0, 0, 0, 0, 1)); // 加1周
const nextMonth = PyRelativeDelta.add(today, new PyRelativeDelta(0, 1)); // 加1月
```

### 2. 时间操作
```javascript
// 创建时间
const now = PyDateTime.now();
const specificTime = new PyTime(14, 30, 0);
const combined = PyDateTime.combine(today, specificTime);

// 时间格式化
console.log(now.strftime("%H:%M:%S"));          // "14:30:25"
console.log(now.strftime("%I:%M %p"));          // "02:30 PM"
console.log(now.strftime("%Y-%m-%d %H:%M:%S")); // "2024-01-15 14:30:25"

// 时间运算
const inOneHour = now.add(PyTimeDelta.create(0, 0, 0, 0, 0, 1)); // 加1小时
const yesterday = now.add(PyTimeDelta.create(-1)); // 减1天
```

### 3. 复杂日期计算
```javascript
class DateCalculator {
    constructor() {
        this.today = PyDate.today();
    }
    
    // 计算年龄
    calculateAge(birthDate) {
        const today = this.today;
        let age = today.year - birthDate.year;
        
        if (today.month < birthDate.month || 
            (today.month === birthDate.month && today.day < birthDate.day)) {
            age--;
        }
        
        return age;
    }
    
    // 计算工作日
    addBusinessDays(startDate, days) {
        let currentDate = startDate;
        let remainingDays = days;
        
        while (remainingDays > 0) {
            currentDate = currentDate.add(new PyTimeDelta(24 * 60 * 60 * 1000));
            const weekday = currentDate.weekday();
            
            // 跳过周末（周六=5，周日=6）
            if (weekday < 5) {
                remainingDays--;
            }
        }
        
        return currentDate;
    }
    
    // 计算月末
    getMonthEnd(date) {
        const nextMonth = PyRelativeDelta.add(date, new PyRelativeDelta(0, 1));
        const firstOfNextMonth = new PyDate(nextMonth.year, nextMonth.month, 1);
        return firstOfNextMonth.add(new PyTimeDelta(-24 * 60 * 60 * 1000));
    }
    
    // 计算季度
    getQuarter(date) {
        return Math.ceil(date.month / 3);
    }
    
    // 计算季度开始日期
    getQuarterStart(date) {
        const quarter = this.getQuarter(date);
        const startMonth = (quarter - 1) * 3 + 1;
        return new PyDate(date.year, startMonth, 1);
    }
}

// 使用示例
const calculator = new DateCalculator();

const birthDate = new PyDate(1990, 5, 15);
const age = calculator.calculateAge(birthDate);
console.log(`年龄: ${age}岁`);

const workDate = calculator.addBusinessDays(PyDate.today(), 10);
console.log(`10个工作日后: ${workDate.strftime("%Y-%m-%d")}`);

const monthEnd = calculator.getMonthEnd(PyDate.today());
console.log(`本月末: ${monthEnd.strftime("%Y-%m-%d")}`);
```

### 4. 日期范围和迭代
```javascript
class DateRange {
    constructor(startDate, endDate, step = null) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.step = step || new PyTimeDelta(24 * 60 * 60 * 1000); // 默认1天
    }
    
    * [Symbol.iterator]() {
        let currentDate = this.startDate;
        
        while (currentDate.isLess(this.endDate) || currentDate.isEqual(this.endDate)) {
            yield currentDate;
            currentDate = currentDate.add(this.step);
        }
    }
    
    toArray() {
        return Array.from(this);
    }
    
    filter(predicate) {
        return this.toArray().filter(predicate);
    }
    
    map(mapper) {
        return this.toArray().map(mapper);
    }
}

// 使用示例
const start = new PyDate(2024, 1, 1);
const end = new PyDate(2024, 1, 31);
const range = new DateRange(start, end);

// 获取所有日期
const allDates = range.toArray();
console.log(`1月共有 ${allDates.length} 天`);

// 获取所有周一
const mondays = range.filter(date => date.weekday() === 0);
console.log(`1月的周一: ${mondays.map(d => d.strftime("%m-%d")).join(", ")}`);

// 格式化所有日期
const formatted = range.map(date => date.strftime("%Y-%m-%d"));
console.log(formatted);
```

### 5. 时区和本地化
```javascript
class LocalizedDateTime {
    constructor(datetime, timezone = "UTC", locale = "en-US") {
        this.datetime = datetime;
        this.timezone = timezone;
        this.locale = locale;
    }
    
    format(options = {}) {
        // 转换为 JavaScript Date 进行本地化格式化
        const jsDate = new Date(
            this.datetime.year,
            this.datetime.month - 1, // JavaScript 月份是0-indexed
            this.datetime.day,
            this.datetime.hour || 0,
            this.datetime.minute || 0,
            this.datetime.second || 0,
            (this.datetime.microsecond || 0) / 1000
        );
        
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            timeZone: this.timezone
        };
        
        return jsDate.toLocaleString(this.locale, { ...defaultOptions, ...options });
    }
    
    toISOString() {
        return this.datetime.strftime("%Y-%m-%dT%H:%M:%S");
    }
    
    toTimestamp() {
        const jsDate = new Date(
            this.datetime.year,
            this.datetime.month - 1,
            this.datetime.day,
            this.datetime.hour || 0,
            this.datetime.minute || 0,
            this.datetime.second || 0
        );
        return jsDate.getTime();
    }
}

// 使用示例
const now = PyDateTime.now();
const localized = new LocalizedDateTime(now, "Asia/Shanghai", "zh-CN");

console.log(localized.format()); // "2024年1月15日 14:30:25"
console.log(localized.toISOString()); // "2024-01-15T14:30:25"
console.log(localized.toTimestamp()); // 1705298425000
```

## 设计模式分析

### 1. 工厂模式 (Factory Pattern)
```javascript
static today() {
    return this.convertDate(new Date());
}

static create(...args) {
    const { year, month, day } = parseArgs(args, ["year", "month", "day"]);
    return new PyDate(year, month, day);
}
```

### 2. 模板方法模式 (Template Method Pattern)
```javascript
strftime(format) {
    return format.replace(/%([A-Za-z])/g, (m, c) => {
        // 子类可以重写特定的格式化逻辑
        return this.formatCode(c) || m;
    });
}
```

### 3. 策略模式 (Strategy Pattern)
```javascript
// 不同的日期运算策略
add(other) {
    if (other instanceof PyTimeDelta) {
        return this.addTimeDelta(other);
    }
    if (other instanceof PyRelativeDelta) {
        return PyRelativeDelta.add(this, other);
    }
}
```

## 性能优化

### 1. 常量预计算
```javascript
// 预计算常用的日期常量
const DI400Y = daysBeforeYear(401);
const DI100Y = daysBeforeYear(101);
const DI4Y = daysBeforeYear(5);
```

### 2. 缓存机制
```javascript
class CachedDateCalculations {
    constructor() {
        this.cache = new Map();
    }
    
    daysInMonth(year, month) {
        const key = `${year}-${month}`;
        if (!this.cache.has(key)) {
            this.cache.set(key, this.calculateDaysInMonth(year, month));
        }
        return this.cache.get(key);
    }
}
```

## 最佳实践

### 1. 不可变性
```javascript
// ✅ 推荐：返回新对象
add(timedelta) {
    return new PyDate(newYear, newMonth, newDay);
}

// ❌ 避免：修改原对象
add(timedelta) {
    this.day += timedelta.days; // 破坏不可变性
    return this;
}
```

### 2. 输入验证
```javascript
// ✅ 推荐：严格的输入验证
constructor(year, month, day) {
    if (month < 1 || month > 12) {
        throw new ValueError(`month must be in 1..12`);
    }
    if (day < 1 || day > daysInMonth(year, month)) {
        throw new ValueError(`day must be in 1..${daysInMonth(year, month)}`);
    }
    // ...
}
```

### 3. 类型检查
```javascript
// ✅ 推荐：明确的类型检查
isEqual(other) {
    if (!(other instanceof PyDate)) {
        return false;
    }
    return this.year === other.year && this.month === other.month && this.day === other.day;
}
```

## 总结

Python 日期时间模块是 Odoo Web 客户端 Python-JavaScript 桥接系统的重要组件，它提供了：
- **完整的日期时间类型**: date、time、datetime、timedelta、relativedelta
- **Python 兼容性**: 与 Python datetime 模块行为一致
- **丰富的运算**: 支持日期时间的加减、比较、格式化
- **复杂计算**: 处理闰年、月份溢出、时区等复杂逻辑
- **高性能**: 优化的算法和缓存机制

这个模块为 Odoo 的日期时间处理提供了可靠的基础，确保了 Python 日期时间代码在 JavaScript 环境中的正确执行。

# Odoo Python 语法分析器 (Python Parser) 学习资料

## 文件概述

**文件路径**: `output/@web/core/py_js/py_parser.js`  
**原始路径**: `/web/static/src/core/py_js/py_parser.js`  
**模块类型**: 核心基础模块 - Python 语法分析器  
**代码行数**: 402 行  
**依赖关系**: 
- `@web/core/py_js/py_tokenizer` - Python 词法分析器

## 模块功能

Python 语法分析器是 Odoo Web 客户端 Python-JavaScript 桥接系统的语法分析组件，负责：
- 将词法分析器产生的标记序列转换为抽象语法树（AST）
- 实现 Python 表达式的语法分析
- 处理运算符优先级和结合性
- 支持 Python 的各种语法结构（函数调用、列表、字典、条件表达式等）
- 提供准确的 Python 语法解析，为解释器提供输入

## AST 类型系统

### 1. 基本类型
```javascript
/**
 * @typedef {{type: 0, value: number}} ASTNumber
 * @typedef {{type: 1, value: string}} ASTString
 * @typedef {{type: 2, value: boolean}} ASTBoolean
 * @typedef {{type: 3}} ASTNone
 */
```

### 2. 容器类型
```javascript
/**
 * @typedef {{type: 4, value: AST[]}} ASTList
 * @typedef {{type: 10, value: AST[]}} ASTTuple
 * @typedef {{type: 11, value: { [key: string]: AST}}} ASTDictionary
 */
```

### 3. 标识符和操作符
```javascript
/**
 * @typedef {{type: 5, value: string}} ASTName
 * @typedef {{type: 6, op: string, right: AST}} ASTUnaryOperator
 * @typedef {{type: 7, op: string, left: AST, right: AST}} ASTBinaryOperator
 * @typedef {{type: 14, op: string, left: AST, right: AST}} ASTBooleanOperator
 */
```

### 4. 高级结构
```javascript
/**
 * @typedef {{type: 8, fn: AST, args: AST[], kwargs: {[key: string]: AST}}} ASTFunctionCall
 * @typedef {{type: 9, name: ASTName, value: AST}} ASTAssignment
 * @typedef {{type: 12, target: AST, key: AST}} ASTLookup
 * @typedef {{type: 13, condition: AST, ifTrue: AST, ifFalse: AST}} ASTIf
 * @typedef {{type: 15, obj: AST, key: string}} ASTObjLookup
 */
```

## 运算符优先级系统

### 1. 绑定力（Binding Power）函数
```javascript
function bp(symbol) {
    switch (symbol) {
        case "=": return 10;           // 赋值
        case "if": return 20;          // 条件表达式
        case "or": return 30;          // 逻辑或
        case "and": return 40;         // 逻辑与
        case "not": return 50;         // 逻辑非
        case "in": case "not in":      // 成员测试
        case "is": case "is not":      // 身份测试
        case "<": case "<=":           // 比较操作符
        case ">": case ">=":
        case "<>": case "==": case "!=":
            return 60;
        case "|": return 70;           // 按位或
        case "^": return 80;           // 按位异或
        case "&": return 90;           // 按位与
        case "<<": case ">>": return 100; // 位移
        case "+": case "-": return 110;   // 加减
        case "*": case "/":               // 乘除
        case "//": case "%": return 120;
        case "**": return 140;            // 幂运算
        case ".": case "(": case "[": return 150; // 最高优先级
    }
    return 0;
}
```

**优先级设计原则**:
- **数值越高优先级越高**: 150 > 140 > ... > 10
- **符合 Python 语义**: 与 Python 官方优先级一致
- **左结合性**: 大部分操作符为左结合
- **特殊处理**: 幂运算（**）为右结合

### 2. 绑定力计算
```javascript
function bindingPower(token) {
    return token.type === 2 /* Symbol */ ? bp(token.value) : 0;
}
```

## 核心解析算法

### 1. Pratt 解析器架构
```javascript
function _parse(tokens, bp = 0) {
    const token = tokens.shift();
    let expr = parsePrefix(token, tokens);
    while (tokens[0] && bindingPower(tokens[0]) > bp) {
        expr = parseInfix(expr, tokens.shift(), tokens);
    }
    return expr;
}
```

**算法特点**:
- **递归下降**: 基于递归下降的解析方法
- **运算符优先级**: 使用绑定力处理优先级
- **左递归消除**: 通过循环避免左递归问题
- **高效**: O(n) 时间复杂度

### 2. 前缀解析（parsePrefix）
```javascript
function parsePrefix(current, tokens) {
    switch (current.type) {
        case 0 /* Number */:
            return { type: 0, value: current.value };
        case 1 /* String */:
            return { type: 1, value: current.value };
        case 4 /* Constant */:
            if (current.value === "None") {
                return { type: 3 /* None */ };
            } else {
                return { type: 2, value: current.value === "True" };
            }
        case 3 /* Name */:
            return { type: 5, value: current.value };
        case 2 /* Symbol */:
            // 处理各种符号...
    }
}
```

**处理的前缀元素**:

#### 字面量
- **数字**: 直接转换为 ASTNumber
- **字符串**: 直接转换为 ASTString
- **常量**: None → ASTNone, True/False → ASTBoolean
- **名称**: 转换为 ASTName

#### 一元操作符
```javascript
case "-":
case "+":
case "~":
    return {
        type: 6 /* UnaryOperator */,
        op: current.value,
        right: _parse(tokens, 130),
    };
case "not":
    return {
        type: 6 /* UnaryOperator */,
        op: current.value,
        right: _parse(tokens, 50),
    };
```

#### 括号表达式和元组
```javascript
case "(": {
    const content = [];
    let isTuple = false;
    while (tokens[0] && !isSymbol(tokens[0], ")")) {
        content.push(_parse(tokens, 0));
        if (tokens[0] && isSymbol(tokens[0], ",")) {
            isTuple = true;
            tokens.shift();
        }
    }
    tokens.shift(); // 消费 ")"
    isTuple = isTuple || content.length === 0;
    return isTuple ? { type: 10 /* Tuple */, value: content } : content[0];
}
```

#### 列表字面量
```javascript
case "[": {
    const value = [];
    while (tokens[0] && !isSymbol(tokens[0], "]")) {
        value.push(_parse(tokens, 0));
        if (isSymbol(tokens[0], ",")) {
            tokens.shift();
        }
    }
    tokens.shift(); // 消费 "]"
    return { type: 4 /* List */, value };
}
```

#### 字典字面量
```javascript
case "{": {
    const dict = {};
    while (tokens[0] && !isSymbol(tokens[0], "}")) {
        const key = _parse(tokens, 0);
        if ((key.type !== 1 && key.type !== 0) || !isSymbol(tokens[0], ":")) {
            throw new ParserError("parsing error");
        }
        tokens.shift(); // 消费 ":"
        const value = _parse(tokens, 0);
        dict[key.value] = value;
        if (isSymbol(tokens[0], ",")) {
            tokens.shift();
        }
    }
    tokens.shift(); // 消费 "}"
    return { type: 11 /* Dictionary */, value: dict };
}
```

### 3. 中缀解析（parseInfix）
```javascript
function parseInfix(left, current, tokens) {
    switch (current.type) {
        case 2 /* Symbol */:
            if (infixOperators.has(current.value)) {
                // 处理二元操作符
            }
            switch (current.value) {
                case "(": // 函数调用
                case "=": // 赋值
                case "[": // 索引访问
                case "if": // 条件表达式
            }
    }
}
```

**处理的中缀元素**:

#### 二元操作符
```javascript
if (infixOperators.has(current.value)) {
    let right = _parse(tokens, bindingPower(current));
    if (current.value === "and" || current.value === "or") {
        return {
            type: 14 /* BooleanOperator */,
            op: current.value,
            left,
            right,
        };
    }
    // 处理链式比较操作符
    let op = { type: 7, op: current.value, left, right };
    while (chainedOperators.has(current.value) && 
           tokens[0] && chainedOperators.has(tokens[0].value)) {
        // 转换为 and 连接的比较链
    }
    return op;
}
```

#### 函数调用
```javascript
case "(": {
    const args = [];
    const kwargs = {};
    while (tokens[0] && !isSymbol(tokens[0], ")")) {
        const arg = _parse(tokens, 0);
        if (arg.type === 9 /* Assignment */) {
            kwargs[arg.name.value] = arg.value;
        } else {
            args.push(arg);
        }
        if (isSymbol(tokens[0], ",")) {
            tokens.shift();
        }
    }
    tokens.shift(); // 消费 ")"
    return { type: 8 /* FunctionCall */, fn: left, args, kwargs };
}
```

#### 索引访问
```javascript
case "[": {
    const key = _parse(tokens);
    if (!isSymbol(tokens[0], "]")) {
        throw new ParserError("parsing error");
    }
    tokens.shift(); // 消费 "]"
    return {
        type: 12 /* Lookup */,
        target: left,
        key: key,
    };
}
```

#### 条件表达式
```javascript
case "if": {
    const condition = _parse(tokens);
    if (!isSymbol(tokens[0], "else")) {
        throw new ParserError("parsing error");
    }
    tokens.shift(); // 消费 "else"
    const ifFalse = _parse(tokens);
    return {
        type: 13 /* If */,
        condition,
        ifTrue: left,
        ifFalse,
    };
}
```

## 特殊语法处理

### 1. 链式比较操作符
```javascript
// Python: a < b < c 等价于 a < b and b < c
while (chainedOperators.has(current.value) && 
       tokens[0] && chainedOperators.has(tokens[0].value)) {
    const nextToken = tokens.shift();
    op = {
        type: 14 /* BooleanOperator */,
        op: "and",
        left: op,
        right: {
            type: 7 /* BinaryOperator */,
            op: nextToken.value,
            left: right,
            right: _parse(tokens, bindingPower(nextToken)),
        },
    };
    right = op.right.right;
}
```

### 2. 对象属性访问
```javascript
if (current.value === ".") {
    if (right.type === 5 /* Name */) {
        return {
            type: 15 /* ObjLookup */,
            obj: left,
            key: right.value,
        };
    } else {
        throw new ParserError("invalid obj lookup");
    }
}
```

### 3. 元组和括号表达式区分
```javascript
// 空括号或有逗号 → 元组
// 单个表达式无逗号 → 括号表达式
isTuple = isTuple || content.length === 0;
return isTuple ? { type: 10 /* Tuple */, value: content } : content[0];
```

## 辅助函数

### 1. parseArgs 函数
```javascript
function parseArgs(args, spec) {
    const last = args[args.length - 1];
    const unnamedArgs = typeof last === "object" ? args.slice(0, -1) : args;
    const kwargs = typeof last === "object" ? last : {};
    for (const [index, val] of unnamedArgs.entries()) {
        kwargs[spec[index]] = val;
    }
    return kwargs;
}
```

**功能**: 将位置参数和关键字参数合并为统一的参数对象

## 实际使用示例

### 1. 基本表达式解析
```javascript
import { parse } from "@web/core/py_js/py_parser";
import { tokenize } from "@web/core/py_js/py_tokenizer";

// 数学表达式
const tokens1 = tokenize("2 + 3 * 4");
const ast1 = parse(tokens1);
console.log(ast1);
// {
//   type: 7, op: "+",
//   left: {type: 0, value: 2},
//   right: {
//     type: 7, op: "*",
//     left: {type: 0, value: 3},
//     right: {type: 0, value: 4}
//   }
// }

// 比较表达式
const tokens2 = tokenize("x < y <= z");
const ast2 = parse(tokens2);
console.log(ast2);
// {
//   type: 14, op: "and",
//   left: {type: 7, op: "<", left: {type: 5, value: "x"}, right: {type: 5, value: "y"}},
//   right: {type: 7, op: "<=", left: {type: 5, value: "y"}, right: {type: 5, value: "z"}}
// }
```

### 2. 复杂数据结构解析
```javascript
// 列表解析
const listTokens = tokenize("[1, 2, 'hello']");
const listAst = parse(listTokens);
console.log(listAst);
// {
//   type: 4,
//   value: [
//     {type: 0, value: 1},
//     {type: 0, value: 2},
//     {type: 1, value: "hello"}
//   ]
// }

// 字典解析
const dictTokens = tokenize("{'key': 'value', 'num': 42}");
const dictAst = parse(dictTokens);
console.log(dictAst);
// {
//   type: 11,
//   value: {
//     "key": {type: 1, value: "value"},
//     "num": {type: 0, value: 42}
//   }
// }

// 函数调用解析
const funcTokens = tokenize("func(a, b=2, c='test')");
const funcAst = parse(funcTokens);
console.log(funcAst);
// {
//   type: 8,
//   fn: {type: 5, value: "func"},
//   args: [{type: 5, value: "a"}],
//   kwargs: {
//     "b": {type: 0, value: 2},
//     "c": {type: 1, value: "test"}
//   }
// }
```

### 3. 表达式分析器
```javascript
class PythonExpressionAnalyzer {
    constructor() {
        this.parse = parse;
        this.tokenize = tokenize;
    }
    
    analyzeExpression(expression) {
        try {
            const tokens = this.tokenize(expression);
            const ast = this.parse(tokens);
            return this.analyzeAST(ast);
        } catch (error) {
            return { error: error.message, valid: false };
        }
    }
    
    analyzeAST(ast) {
        const analysis = {
            type: this.getASTTypeName(ast.type),
            complexity: this.calculateComplexity(ast),
            variables: this.extractVariables(ast),
            functions: this.extractFunctions(ast),
            operators: this.extractOperators(ast),
            literals: this.extractLiterals(ast)
        };
        
        return { ...analysis, valid: true };
    }
    
    getASTTypeName(type) {
        const typeNames = {
            0: "Number", 1: "String", 2: "Boolean", 3: "None",
            4: "List", 5: "Name", 6: "UnaryOperator", 7: "BinaryOperator",
            8: "FunctionCall", 9: "Assignment", 10: "Tuple",
            11: "Dictionary", 12: "Lookup", 13: "If", 14: "BooleanOperator",
            15: "ObjLookup"
        };
        return typeNames[type] || "Unknown";
    }
    
    calculateComplexity(ast) {
        if (!ast || typeof ast !== 'object') return 0;
        
        let complexity = 1;
        
        // 递归计算子节点复杂度
        if (ast.left) complexity += this.calculateComplexity(ast.left);
        if (ast.right) complexity += this.calculateComplexity(ast.right);
        if (ast.args) {
            complexity += ast.args.reduce((sum, arg) => sum + this.calculateComplexity(arg), 0);
        }
        if (ast.value && Array.isArray(ast.value)) {
            complexity += ast.value.reduce((sum, item) => sum + this.calculateComplexity(item), 0);
        }
        
        return complexity;
    }
    
    extractVariables(ast, variables = new Set()) {
        if (!ast || typeof ast !== 'object') return variables;
        
        if (ast.type === 5 /* Name */) {
            variables.add(ast.value);
        }
        
        // 递归提取
        if (ast.left) this.extractVariables(ast.left, variables);
        if (ast.right) this.extractVariables(ast.right, variables);
        if (ast.args) ast.args.forEach(arg => this.extractVariables(arg, variables));
        if (ast.value && Array.isArray(ast.value)) {
            ast.value.forEach(item => this.extractVariables(item, variables));
        }
        
        return Array.from(variables);
    }
    
    extractFunctions(ast, functions = new Set()) {
        if (!ast || typeof ast !== 'object') return functions;
        
        if (ast.type === 8 /* FunctionCall */ && ast.fn.type === 5 /* Name */) {
            functions.add(ast.fn.value);
        }
        
        // 递归提取
        if (ast.left) this.extractFunctions(ast.left, functions);
        if (ast.right) this.extractFunctions(ast.right, functions);
        if (ast.args) ast.args.forEach(arg => this.extractFunctions(arg, functions));
        
        return Array.from(functions);
    }
    
    extractOperators(ast, operators = new Set()) {
        if (!ast || typeof ast !== 'object') return operators;
        
        if (ast.op) {
            operators.add(ast.op);
        }
        
        // 递归提取
        if (ast.left) this.extractOperators(ast.left, operators);
        if (ast.right) this.extractOperators(ast.right, operators);
        if (ast.args) ast.args.forEach(arg => this.extractOperators(arg, operators));
        
        return Array.from(operators);
    }
    
    extractLiterals(ast, literals = []) {
        if (!ast || typeof ast !== 'object') return literals;
        
        if (ast.type === 0 /* Number */ || ast.type === 1 /* String */ || 
            ast.type === 2 /* Boolean */ || ast.type === 3 /* None */) {
            literals.push({ type: this.getASTTypeName(ast.type), value: ast.value });
        }
        
        // 递归提取
        if (ast.left) this.extractLiterals(ast.left, literals);
        if (ast.right) this.extractLiterals(ast.right, literals);
        if (ast.args) ast.args.forEach(arg => this.extractLiterals(arg, literals));
        
        return literals;
    }
}

// 使用示例
const analyzer = new PythonExpressionAnalyzer();

const result = analyzer.analyzeExpression("func(x + 1, y=2) if condition else default");
console.log(result);
// {
//   type: "If",
//   complexity: 8,
//   variables: ["x", "y", "condition", "default"],
//   functions: ["func"],
//   operators: ["+", "="],
//   literals: [{type: "Number", value: 1}, {type: "Number", value: 2}],
//   valid: true
// }
```

### 4. AST 遍历器
```javascript
class ASTVisitor {
    visit(ast) {
        const methodName = `visit${this.getASTTypeName(ast.type)}`;
        if (this[methodName]) {
            return this[methodName](ast);
        }
        return this.genericVisit(ast);
    }
    
    genericVisit(ast) {
        // 默认遍历所有子节点
        if (ast.left) this.visit(ast.left);
        if (ast.right) this.visit(ast.right);
        if (ast.args) ast.args.forEach(arg => this.visit(arg));
        if (ast.value && Array.isArray(ast.value)) {
            ast.value.forEach(item => this.visit(item));
        }
    }
    
    visitFunctionCall(ast) {
        console.log(`Function call: ${ast.fn.value}`);
        this.visit(ast.fn);
        ast.args.forEach(arg => this.visit(arg));
        Object.values(ast.kwargs).forEach(kwarg => this.visit(kwarg));
    }
    
    visitBinaryOperator(ast) {
        console.log(`Binary operation: ${ast.op}`);
        this.visit(ast.left);
        this.visit(ast.right);
    }
    
    visitName(ast) {
        console.log(`Variable: ${ast.value}`);
    }
    
    getASTTypeName(type) {
        const typeNames = {
            0: "Number", 1: "String", 2: "Boolean", 3: "None",
            4: "List", 5: "Name", 6: "UnaryOperator", 7: "BinaryOperator",
            8: "FunctionCall", 9: "Assignment", 10: "Tuple",
            11: "Dictionary", 12: "Lookup", 13: "If", 14: "BooleanOperator",
            15: "ObjLookup"
        };
        return typeNames[type] || "Unknown";
    }
}

// 使用示例
const visitor = new ASTVisitor();
const tokens = tokenize("func(x + y, z=1)");
const ast = parse(tokens);
visitor.visit(ast);
// 输出:
// Function call: func
// Variable: x
// Binary operation: +
// Variable: x
// Variable: y
// Variable: z
// Number: 1
```

## 设计模式分析

### 1. 递归下降解析器模式
```javascript
function _parse(tokens, bp = 0) {
    const token = tokens.shift();
    let expr = parsePrefix(token, tokens);
    while (tokens[0] && bindingPower(tokens[0]) > bp) {
        expr = parseInfix(expr, tokens.shift(), tokens);
    }
    return expr;
}
```

**特点**:
- **递归结构**: 自然处理嵌套表达式
- **优先级驱动**: 通过绑定力处理优先级
- **左递归消除**: 使用循环避免左递归

### 2. 访问者模式 (Visitor Pattern)
```javascript
// AST 节点类型决定处理方式
switch (current.type) {
    case 0 /* Number */: return handleNumber(current);
    case 1 /* String */: return handleString(current);
    // ...
}
```

### 3. 工厂模式 (Factory Pattern)
```javascript
// 根据标记类型创建相应的 AST 节点
function createASTNode(token) {
    switch (token.type) {
        case 0: return { type: 0, value: token.value };
        case 1: return { type: 1, value: token.value };
        // ...
    }
}
```

## 最佳实践

### 1. 错误处理
```javascript
// ✅ 推荐：提供详细的错误信息
if (!tokens[0] || !isSymbol(tokens[0], ")")) {
    throw new ParserError(`Expected ')' but found ${tokens[0]?.value || 'EOF'}`);
}

// ❌ 避免：模糊的错误信息
throw new ParserError("parsing error");
```

### 2. AST 节点验证
```javascript
// ✅ 推荐：验证 AST 节点的有效性
if (key.type !== 1 /* String */ && key.type !== 0 /* Number */) {
    throw new ParserError("Dictionary key must be string or number");
}

// ❌ 避免：不验证节点类型
dict[key.value] = value; // 可能导致运行时错误
```

### 3. 性能优化
```javascript
// ✅ 推荐：预计算常量集合
const infixOperators = new Set(binaryOperators.concat(comparators));

// ❌ 避免：重复计算
if (binaryOperators.includes(op) || comparators.includes(op)) {
    // 每次都要遍历数组
}
```

## 总结

Python 语法分析器是 Odoo Web 客户端 Python-JavaScript 桥接系统的核心组件，它提供了：
- **完整的 Python 语法支持**: 支持表达式、函数调用、数据结构等所有常用语法
- **准确的优先级处理**: 实现与 Python 一致的运算符优先级和结合性
- **高效的解析算法**: 基于 Pratt 解析器的 O(n) 时间复杂度
- **丰富的 AST 类型**: 提供详细的抽象语法树表示
- **错误处理**: 提供准确的语法错误定位和报告

这个语法分析器为 Python 表达式的执行提供了可靠的语法树基础，确保了 Python 代码在 JavaScript 环境中的正确解析和执行。

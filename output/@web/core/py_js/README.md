# Odoo Python-JavaScript 桥接模块架构总览与使用指南

## 📁 模块概览

**目录路径**: `output/@web/core/py_js/`  
**模块类型**: 核心基础模块 - Python-JavaScript 桥接系统  
**功能范围**: Python 表达式解析、求值、类型转换、日期时间处理

## 🏗️ 架构图

```
@web/core/py_js/
├── 📄 py_builtin.js      # Python 内置函数实现
├── 📄 py_tokenizer.js    # Python 词法分析器
├── 📄 py_parser.js       # Python 语法分析器
├── 📄 py_interpreter.js  # Python 解释器
├── 📄 py_date.js         # Python 日期时间类型
├── 📄 py_utils.js        # Python 工具函数
└── 📄 py.js              # Python 主入口模块
```

## 🎯 模块功能矩阵

| 模块 | 主要功能 | 核心API | 依赖关系 |
|------|----------|---------|----------|
| **py_builtin.js** | 内置函数 | `BUILTINS` 对象 | py_date |
| **py_tokenizer.js** | 词法分析 | `tokenize()` | 无 |
| **py_parser.js** | 语法分析 | `parse()` | py_tokenizer |
| **py_interpreter.js** | 表达式求值 | `evaluate()` | py_builtin, py_date, py_utils, py_parser |
| **py_date.js** | 日期时间 | `PyDate`, `PyDateTime`, `PyTimeDelta` | py_parser |
| **py_utils.js** | 工具函数 | `toPyValue()`, `formatAST()` | py_parser, py_date |
| **py.js** | 主入口 | `evaluateExpr()`, `parseExpr()` | 所有模块 |

## 🔄 数据流图

```mermaid
graph TD
    A[Python 表达式字符串] --> B[py_tokenizer]
    B --> C[Token 序列]
    C --> D[py_parser]
    D --> E[AST 抽象语法树]
    E --> F[py_interpreter]
    F --> G[JavaScript 值]
    
    H[JavaScript 值] --> I[py_utils.toPyValue]
    I --> J[Python AST 节点]
    J --> K[py_utils.formatAST]
    K --> L[Python 代码字符串]
    
    M[日期时间操作] --> N[py_date]
    N --> O[PyDate/PyDateTime/PyTimeDelta]
    
    P[内置函数调用] --> Q[py_builtin]
    Q --> R[Python 语义实现]
    
    S[统一入口] --> T[py.js]
    T --> U[简化的 API]
```

## 🚀 快速开始

### 1. 基本表达式求值
```javascript
import { evaluateExpr } from "@web/core/py_js/py";

// 数学运算
console.log(evaluateExpr("2 + 3 * 4"));        // 14
console.log(evaluateExpr("(2 + 3) * 4"));      // 20

// 字符串操作
console.log(evaluateExpr("'hello' + ' world'")); // "hello world"

// 布尔运算
console.log(evaluateExpr("True and False"));    // false
console.log(evaluateExpr("5 > 3"));            // true

// 列表操作
console.log(evaluateExpr("[1, 2, 3][1]"));     // 2
console.log(evaluateExpr("len([1, 2, 3])", { len: arr => arr.length })); // 3
```

### 2. 带上下文的求值
```javascript
const context = {
    user: { name: "Alice", age: 25, active: true },
    items: [1, 2, 3, 4, 5],
    config: { debug: false, timeout: 30 }
};

// 变量访问
console.log(evaluateExpr("user.name", context));           // "Alice"
console.log(evaluateExpr("user.age >= 18", context));      // true

// 复杂表达式
console.log(evaluateExpr("user.active and not config.debug", context)); // true
console.log(evaluateExpr("len(items) > 3", { ...context, len: arr => arr.length })); // true
```

## 📚 核心概念详解

### 1. 词法分析 (Tokenization)
- **功能**: 将 Python 源代码分解为标记序列
- **支持**: 数字、字符串、符号、名称、常量
- **特性**: 完整的 Python 字符串转义支持

### 2. 语法分析 (Parsing)
- **算法**: 基于 Pratt 解析器的递归下降算法
- **优先级**: 完整的 Python 运算符优先级支持
- **结构**: 生成抽象语法树 (AST)

### 3. 解释执行 (Interpretation)
- **语义**: 实现 Python 表达式语义
- **类型**: 支持 Python 类型系统和比较
- **上下文**: 灵活的变量作用域管理

### 4. 日期时间处理
- **类型**: PyDate、PyDateTime、PyTime、PyTimeDelta、PyRelativeDelta
- **运算**: 完整的日期时间算术运算
- **格式化**: strftime 格式化支持

### 5. 内置函数
- **函数**: bool()、set()、max()、min() 等
- **语义**: 与 Python 行为完全一致
- **扩展**: 支持自定义内置函数

## 🔧 高级用法

### 1. 完整的表达式求值器
```javascript
import { evaluateExpr, evaluateBooleanExpr, parseExpr, formatAST } from "@web/core/py_js/py";
import { BUILTINS } from "@web/core/py_js/py_builtin";

class AdvancedPythonEvaluator {
    constructor() {
        this.baseContext = {
            // Python 内置函数
            ...BUILTINS,
            
            // 扩展函数
            len: (obj) => obj?.length ?? Object.keys(obj || {}).length,
            sum: (arr) => arr.reduce((a, b) => a + b, 0),
            any: (arr) => arr.some(Boolean),
            all: (arr) => arr.every(Boolean),
            
            // 字符串函数
            str: String,
            int: parseInt,
            float: parseFloat,
            
            // 数学函数
            abs: Math.abs,
            max: Math.max,
            min: Math.min,
            round: Math.round,
            
            // 类型检查
            isinstance: (obj, type) => typeof obj === type,
            hasattr: (obj, attr) => attr in obj
        };
    }
    
    evaluate(expression, context = {}) {
        const fullContext = { ...this.baseContext, ...context };
        return evaluateExpr(expression, fullContext);
    }
    
    evaluateBoolean(expression, context = {}) {
        const fullContext = { ...this.baseContext, ...context };
        return evaluateBooleanExpr(expression, fullContext);
    }
    
    evaluateMultiple(expressions, context = {}) {
        const results = {};
        const fullContext = { ...this.baseContext, ...context };
        
        for (const [key, expr] of Object.entries(expressions)) {
            try {
                results[key] = evaluateExpr(expr, fullContext);
            } catch (error) {
                results[key] = { error: error.message };
            }
        }
        
        return results;
    }
    
    validateExpression(expression) {
        try {
            parseExpr(expression);
            return { valid: true, error: null };
        } catch (error) {
            return { valid: false, error: error.message };
        }
    }
    
    formatExpression(expression) {
        try {
            const ast = parseExpr(expression);
            return formatAST(ast);
        } catch (error) {
            throw new Error(`Cannot format expression: ${error.message}`);
        }
    }
    
    analyzeExpression(expression) {
        try {
            const ast = parseExpr(expression);
            return {
                valid: true,
                ast: ast,
                formatted: formatAST(ast),
                variables: this.extractVariables(ast),
                functions: this.extractFunctions(ast),
                complexity: this.calculateComplexity(ast)
            };
        } catch (error) {
            return { valid: false, error: error.message };
        }
    }
    
    extractVariables(ast, variables = new Set()) {
        if (!ast || typeof ast !== 'object') return variables;
        
        if (ast.type === 5 /* Name */) {
            variables.add(ast.value);
        }
        
        // 递归提取
        this.traverseAST(ast, (node) => {
            if (node.type === 5 /* Name */) {
                variables.add(node.value);
            }
        });
        
        return Array.from(variables);
    }
    
    extractFunctions(ast, functions = new Set()) {
        this.traverseAST(ast, (node) => {
            if (node.type === 8 /* FunctionCall */ && node.fn.type === 5 /* Name */) {
                functions.add(node.fn.value);
            }
        });
        
        return Array.from(functions);
    }
    
    calculateComplexity(ast) {
        let complexity = 0;
        
        this.traverseAST(ast, (node) => {
            if (node.type === 7 /* BinaryOperator */ || 
                node.type === 6 /* UnaryOperator */ ||
                node.type === 8 /* FunctionCall */ ||
                node.type === 13 /* If */) {
                complexity++;
            }
        });
        
        return complexity;
    }
    
    traverseAST(ast, callback) {
        if (!ast || typeof ast !== 'object') return;
        
        callback(ast);
        
        // 遍历子节点
        if (ast.left) this.traverseAST(ast.left, callback);
        if (ast.right) this.traverseAST(ast.right, callback);
        if (ast.args) ast.args.forEach(arg => this.traverseAST(arg, callback));
        if (ast.value && Array.isArray(ast.value)) {
            ast.value.forEach(item => this.traverseAST(item, callback));
        }
        if (ast.kwargs) {
            Object.values(ast.kwargs).forEach(kwarg => this.traverseAST(kwarg, callback));
        }
    }
}

// 使用示例
const evaluator = new AdvancedPythonEvaluator();

// 复杂表达式求值
const context = {
    users: [
        { name: "Alice", age: 25, active: true, role: "admin" },
        { name: "Bob", age: 30, active: false, role: "user" },
        { name: "Charlie", age: 35, active: true, role: "editor" }
    ],
    config: { min_age: 18, max_users: 100 }
};

// 多表达式求值
const expressions = {
    active_users: "len([u for u in users if u.active])",
    admin_count: "len([u for u in users if u.role == 'admin'])",
    avg_age: "sum([u.age for u in users]) / len(users)",
    has_capacity: "len(users) < config.max_users"
};

const results = evaluator.evaluateMultiple(expressions, context);
console.log(results);

// 表达式分析
const analysis = evaluator.analyzeExpression("sum([u.age for u in users if u.active]) / len(users)");
console.log(analysis);
```

### 2. 动态规则引擎
```javascript
class PythonRuleEngine {
    constructor() {
        this.evaluator = new AdvancedPythonEvaluator();
        this.rules = new Map();
    }
    
    addRule(name, condition, action, priority = 0) {
        this.rules.set(name, {
            condition,
            action,
            priority,
            compiled: null
        });
    }
    
    compileRules() {
        for (const [name, rule] of this.rules) {
            try {
                const ast = parseExpr(rule.condition);
                rule.compiled = ast;
            } catch (error) {
                console.error(`Failed to compile rule ${name}:`, error);
                rule.compiled = null;
            }
        }
    }
    
    evaluateRules(context) {
        const results = [];
        const sortedRules = Array.from(this.rules.entries())
            .sort(([,a], [,b]) => b.priority - a.priority);
        
        for (const [name, rule] of sortedRules) {
            try {
                const conditionResult = this.evaluator.evaluateBoolean(rule.condition, context);
                
                if (conditionResult) {
                    const actionResult = typeof rule.action === 'function' 
                        ? rule.action(context)
                        : this.evaluator.evaluate(rule.action, context);
                    
                    results.push({
                        rule: name,
                        condition: rule.condition,
                        result: actionResult,
                        priority: rule.priority
                    });
                }
            } catch (error) {
                console.error(`Rule ${name} evaluation failed:`, error);
            }
        }
        
        return results;
    }
    
    evaluateFirstMatch(context) {
        const results = this.evaluateRules(context);
        return results.length > 0 ? results[0] : null;
    }
    
    validateRules() {
        const errors = [];
        
        for (const [name, rule] of this.rules) {
            const validation = this.evaluator.validateExpression(rule.condition);
            if (!validation.valid) {
                errors.push({
                    rule: name,
                    error: validation.error
                });
            }
        }
        
        return errors;
    }
}

// 使用示例
const ruleEngine = new PythonRuleEngine();

// 添加规则
ruleEngine.addRule("admin_access", 
    "user.role == 'admin' and user.active", 
    (ctx) => ({ access: "full", permissions: ["read", "write", "delete", "admin"] }),
    100);

ruleEngine.addRule("editor_access",
    "user.role == 'editor' and user.active",
    (ctx) => ({ access: "edit", permissions: ["read", "write"] }),
    80);

ruleEngine.addRule("user_access",
    "user.role == 'user' and user.active",
    (ctx) => ({ access: "read", permissions: ["read"] }),
    60);

ruleEngine.addRule("guest_access",
    "not user.active or user.role == 'guest'",
    (ctx) => ({ access: "none", permissions: [] }),
    10);

// 评估规则
const userContext = {
    user: { role: "editor", active: true, name: "Alice" }
};

const firstMatch = ruleEngine.evaluateFirstMatch(userContext);
console.log(firstMatch);
// { rule: "editor_access", result: { access: "edit", permissions: ["read", "write"] }, ... }

// 验证所有规则
const errors = ruleEngine.validateRules();
if (errors.length > 0) {
    console.error("Rule validation errors:", errors);
}
```

### 3. 表达式缓存和优化
```javascript
class OptimizedPythonEvaluator {
    constructor() {
        this.evaluator = new AdvancedPythonEvaluator();
        this.astCache = new Map();
        this.resultCache = new Map();
        this.cacheStats = { hits: 0, misses: 0 };
    }
    
    evaluate(expression, context = {}) {
        // AST 缓存
        let ast = this.astCache.get(expression);
        if (!ast) {
            ast = parseExpr(expression);
            this.astCache.set(expression, ast);
        }
        
        // 结果缓存（仅对纯表达式）
        if (this.isPureExpression(ast)) {
            const cacheKey = this.getCacheKey(expression, context);
            const cached = this.resultCache.get(cacheKey);
            
            if (cached !== undefined) {
                this.cacheStats.hits++;
                return cached;
            }
        }
        
        this.cacheStats.misses++;
        const result = this.evaluator.evaluate(expression, context);
        
        // 缓存结果
        if (this.isPureExpression(ast)) {
            const cacheKey = this.getCacheKey(expression, context);
            this.resultCache.set(cacheKey, result);
        }
        
        return result;
    }
    
    isPureExpression(ast) {
        // 检查表达式是否为纯表达式（无副作用）
        return !this.hasFunctionCalls(ast) || this.hasOnlyPureFunctions(ast);
    }
    
    hasFunctionCalls(ast) {
        if (!ast || typeof ast !== 'object') return false;
        
        if (ast.type === 8 /* FunctionCall */) {
            return true;
        }
        
        return this.traverseASTCheck(ast, node => node.type === 8);
    }
    
    hasOnlyPureFunctions(ast) {
        const pureFunctions = new Set(['len', 'sum', 'max', 'min', 'abs', 'round', 'str', 'int', 'float', 'bool']);
        
        let hasImpureFunction = false;
        this.traverseAST(ast, (node) => {
            if (node.type === 8 /* FunctionCall */ && node.fn.type === 5 /* Name */) {
                if (!pureFunctions.has(node.fn.value)) {
                    hasImpureFunction = true;
                }
            }
        });
        
        return !hasImpureFunction;
    }
    
    getCacheKey(expression, context) {
        // 简化的缓存键生成
        const contextKeys = Object.keys(context).sort();
        const contextHash = contextKeys.map(key => `${key}:${JSON.stringify(context[key])}`).join('|');
        return `${expression}::${contextHash}`;
    }
    
    clearCache() {
        this.astCache.clear();
        this.resultCache.clear();
        this.cacheStats = { hits: 0, misses: 0 };
    }
    
    getCacheStats() {
        const total = this.cacheStats.hits + this.cacheStats.misses;
        return {
            ...this.cacheStats,
            total,
            hitRate: total > 0 ? (this.cacheStats.hits / total * 100).toFixed(2) + '%' : '0%',
            astCacheSize: this.astCache.size,
            resultCacheSize: this.resultCache.size
        };
    }
    
    // 辅助方法
    traverseAST(ast, callback) {
        if (!ast || typeof ast !== 'object') return;
        
        callback(ast);
        
        if (ast.left) this.traverseAST(ast.left, callback);
        if (ast.right) this.traverseAST(ast.right, callback);
        if (ast.args) ast.args.forEach(arg => this.traverseAST(arg, callback));
        if (ast.value && Array.isArray(ast.value)) {
            ast.value.forEach(item => this.traverseAST(item, callback));
        }
    }
    
    traverseASTCheck(ast, predicate) {
        if (!ast || typeof ast !== 'object') return false;
        
        if (predicate(ast)) return true;
        
        return (ast.left && this.traverseASTCheck(ast.left, predicate)) ||
               (ast.right && this.traverseASTCheck(ast.right, predicate)) ||
               (ast.args && ast.args.some(arg => this.traverseASTCheck(arg, predicate))) ||
               (ast.value && Array.isArray(ast.value) && ast.value.some(item => this.traverseASTCheck(item, predicate)));
    }
}

// 使用示例
const optimizedEvaluator = new OptimizedPythonEvaluator();

// 重复求值相同表达式
const context = { x: 10, y: 20, items: [1, 2, 3, 4, 5] };

console.time("First evaluation");
const result1 = optimizedEvaluator.evaluate("x + y + len(items)", context);
console.timeEnd("First evaluation");

console.time("Cached evaluation");
const result2 = optimizedEvaluator.evaluate("x + y + len(items)", context);
console.timeEnd("Cached evaluation");

console.log("Results:", result1, result2); // 应该相同
console.log("Cache stats:", optimizedEvaluator.getCacheStats());
```

## 🎨 最佳实践

### 1. 表达式设计
```javascript
// ✅ 推荐：简洁明确的表达式
"user.active and user.age >= 18"
"len(items) > 0"
"config.debug or user.is_admin"

// ❌ 避免：过于复杂的表达式
"((user.active and user.age >= 18) or (user.role == 'admin' and user.permissions.includes('override'))) and not (config.maintenance_mode and not user.is_superuser)"
```

### 2. 上下文管理
```javascript
// ✅ 推荐：结构化的上下文
const context = {
    user: { name: "Alice", role: "admin", active: true },
    config: { debug: false, timeout: 30 },
    data: { items: [], count: 0 },
    
    // 工具函数
    len: (obj) => obj?.length ?? 0,
    sum: (arr) => arr.reduce((a, b) => a + b, 0)
};

// ❌ 避免：扁平化的上下文
const badContext = {
    userName: "Alice",
    userRole: "admin",
    userActive: true,
    configDebug: false,
    configTimeout: 30
    // 难以维护和理解
};
```

### 3. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
function safeEvaluate(expression, context) {
    try {
        return {
            success: true,
            result: evaluateExpr(expression, context),
            error: null
        };
    } catch (error) {
        return {
            success: false,
            result: null,
            error: error.message
        };
    }
}

// ❌ 避免：忽略错误
function unsafeEvaluate(expression, context) {
    return evaluateExpr(expression, context); // 可能抛出异常
}
```

### 4. 性能优化
```javascript
// ✅ 推荐：预编译和缓存
class ExpressionManager {
    constructor() {
        this.compiledExpressions = new Map();
    }
    
    compile(name, expression) {
        try {
            const ast = parseExpr(expression);
            this.compiledExpressions.set(name, { ast, expression });
            return true;
        } catch (error) {
            console.error(`Failed to compile ${name}:`, error);
            return false;
        }
    }
    
    evaluate(name, context) {
        const compiled = this.compiledExpressions.get(name);
        if (!compiled) {
            throw new Error(`Expression not found: ${name}`);
        }
        
        return evaluate(compiled.ast, context);
    }
}

// ❌ 避免：重复解析
function inefficientEvaluate(expressions, context) {
    return expressions.map(expr => evaluateExpr(expr, context)); // 每次都重新解析
}
```

## ⚡ 性能优化

### 1. 表达式预编译
```javascript
// 预编译常用表达式
const precompiledExpressions = new Map([
    ['user_active', parseExpr('user.active')],
    ['admin_check', parseExpr('user.role == "admin"')],
    ['age_check', parseExpr('user.age >= 18')]
]);
```

### 2. 上下文优化
```javascript
// 使用不可变的基础上下文
const baseContext = Object.freeze({
    len: (obj) => obj?.length ?? 0,
    sum: (arr) => arr.reduce((a, b) => a + b, 0),
    max: Math.max,
    min: Math.min
});
```

### 3. 结果缓存
```javascript
// 缓存纯函数的结果
const resultCache = new Map();

function cachedEvaluate(expression, context) {
    const key = `${expression}:${JSON.stringify(context)}`;
    
    if (resultCache.has(key)) {
        return resultCache.get(key);
    }
    
    const result = evaluateExpr(expression, context);
    resultCache.set(key, result);
    return result;
}
```

## 🔍 调试技巧

### 1. 表达式调试器
```javascript
class ExpressionDebugger {
    static debug(expression, context) {
        console.group(`🐍 Python Expression Debug: ${expression}`);
        
        try {
            // 词法分析
            console.log("📝 Tokens:", tokenize(expression));
            
            // 语法分析
            const ast = parseExpr(expression);
            console.log("🌳 AST:", ast);
            console.log("📄 Formatted:", formatAST(ast));
            
            // 求值
            const result = evaluateExpr(expression, context);
            console.log("✅ Result:", result);
            
        } catch (error) {
            console.error("❌ Error:", error.message);
        } finally {
            console.groupEnd();
        }
    }
}

// 使用示例
ExpressionDebugger.debug("user.age >= 18 and user.active", {
    user: { age: 25, active: true }
});
```

### 2. 性能分析
```javascript
class PerformanceProfiler {
    static profile(expression, context, iterations = 1000) {
        console.time(`Expression: ${expression}`);
        
        for (let i = 0; i < iterations; i++) {
            evaluateExpr(expression, context);
        }
        
        console.timeEnd(`Expression: ${expression}`);
    }
}
```

## 🛠️ 扩展开发

### 1. 自定义内置函数
```javascript
import { BUILTINS } from "@web/core/py_js/py_builtin";

// 扩展内置函数
const customBuiltins = {
    ...BUILTINS,

    // 自定义字符串函数
    capitalize: (str) => str.charAt(0).toUpperCase() + str.slice(1).toLowerCase(),
    reverse: (str) => str.split('').reverse().join(''),

    // 自定义数组函数
    unique: (arr) => [...new Set(arr)],
    flatten: (arr) => arr.flat(),

    // 自定义数学函数
    clamp: (value, min, max) => Math.max(min, Math.min(max, value)),
    lerp: (a, b, t) => a + (b - a) * t,

    // 自定义日期函数
    now: () => new Date(),
    timestamp: () => Date.now(),

    // 自定义验证函数
    is_email: (str) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(str),
    is_phone: (str) => /^\+?[\d\s\-\(\)]+$/.test(str)
};

// 使用自定义函数
const result = evaluateExpr("capitalize('hello world')", customBuiltins);
console.log(result); // "Hello world"
```

### 2. 自定义数据类型
```javascript
// 自定义货币类型
class PyCurrency {
    constructor(amount, currency = 'USD') {
        this.amount = amount;
        this.currency = currency;
    }

    add(other) {
        if (other instanceof PyCurrency) {
            if (this.currency !== other.currency) {
                throw new Error(`Cannot add different currencies: ${this.currency} and ${other.currency}`);
            }
            return new PyCurrency(this.amount + other.amount, this.currency);
        }
        return new PyCurrency(this.amount + other, this.currency);
    }

    multiply(factor) {
        return new PyCurrency(this.amount * factor, this.currency);
    }

    toString() {
        return `${this.amount.toFixed(2)} ${this.currency}`;
    }

    isEqual(other) {
        return other instanceof PyCurrency &&
               this.amount === other.amount &&
               this.currency === other.currency;
    }
}

// 在上下文中使用
const context = {
    price: new PyCurrency(100, 'USD'),
    tax_rate: 0.1,
    PyCurrency: PyCurrency
};

const total = evaluateExpr("price.add(price.multiply(tax_rate))", context);
console.log(total.toString()); // "110.00 USD"
```

### 3. 表达式宏系统
```javascript
class ExpressionMacroSystem {
    constructor() {
        this.macros = new Map();
        this.evaluator = new AdvancedPythonEvaluator();
    }

    defineMacro(name, parameters, body) {
        this.macros.set(name, { parameters, body });
    }

    expandMacros(expression) {
        let expanded = expression;

        // 简单的宏展开（实际实现会更复杂）
        for (const [name, macro] of this.macros) {
            const pattern = new RegExp(`${name}\\(([^)]+)\\)`, 'g');
            expanded = expanded.replace(pattern, (match, args) => {
                const argList = args.split(',').map(arg => arg.trim());
                let macroBody = macro.body;

                macro.parameters.forEach((param, index) => {
                    if (index < argList.length) {
                        macroBody = macroBody.replace(new RegExp(`\\b${param}\\b`, 'g'), argList[index]);
                    }
                });

                return `(${macroBody})`;
            });
        }

        return expanded;
    }

    evaluate(expression, context = {}) {
        const expanded = this.expandMacros(expression);
        return this.evaluator.evaluate(expanded, context);
    }
}

// 使用示例
const macroSystem = new ExpressionMacroSystem();

// 定义宏
macroSystem.defineMacro('is_adult', ['age'], 'age >= 18');
macroSystem.defineMacro('full_name', ['first', 'last'], 'first + " " + last');
macroSystem.defineMacro('discount_price', ['price', 'discount'], 'price * (1 - discount)');

// 使用宏
const context = { user: { age: 25, first_name: "John", last_name: "Doe" }, product: { price: 100 } };

console.log(macroSystem.evaluate('is_adult(user.age)', context)); // true
console.log(macroSystem.evaluate('full_name(user.first_name, user.last_name)', context)); // "John Doe"
console.log(macroSystem.evaluate('discount_price(product.price, 0.1)', context)); // 90
```

## 🧪 测试和验证

### 1. 表达式测试套件
```javascript
class ExpressionTestSuite {
    constructor() {
        this.tests = [];
        this.evaluator = new AdvancedPythonEvaluator();
    }

    addTest(name, expression, context, expected, description = '') {
        this.tests.push({ name, expression, context, expected, description });
    }

    runTests() {
        const results = {
            passed: 0,
            failed: 0,
            errors: []
        };

        for (const test of this.tests) {
            try {
                const actual = this.evaluator.evaluate(test.expression, test.context);

                if (this.deepEqual(actual, test.expected)) {
                    results.passed++;
                    console.log(`✅ ${test.name}: PASSED`);
                } else {
                    results.failed++;
                    console.log(`❌ ${test.name}: FAILED`);
                    console.log(`   Expected: ${JSON.stringify(test.expected)}`);
                    console.log(`   Actual: ${JSON.stringify(actual)}`);
                    results.errors.push({
                        test: test.name,
                        expression: test.expression,
                        expected: test.expected,
                        actual: actual
                    });
                }
            } catch (error) {
                results.failed++;
                console.log(`💥 ${test.name}: ERROR - ${error.message}`);
                results.errors.push({
                    test: test.name,
                    expression: test.expression,
                    error: error.message
                });
            }
        }

        console.log(`\n📊 Test Results: ${results.passed} passed, ${results.failed} failed`);
        return results;
    }

    deepEqual(a, b) {
        if (a === b) return true;
        if (a == null || b == null) return false;
        if (typeof a !== typeof b) return false;

        if (typeof a === 'object') {
            const keysA = Object.keys(a);
            const keysB = Object.keys(b);

            if (keysA.length !== keysB.length) return false;

            for (const key of keysA) {
                if (!keysB.includes(key) || !this.deepEqual(a[key], b[key])) {
                    return false;
                }
            }

            return true;
        }

        return false;
    }
}

// 使用示例
const testSuite = new ExpressionTestSuite();

// 添加测试用例
testSuite.addTest('basic_math', '2 + 3 * 4', {}, 14, 'Basic arithmetic with precedence');
testSuite.addTest('string_concat', '"hello" + " " + "world"', {}, 'hello world', 'String concatenation');
testSuite.addTest('boolean_logic', 'True and False or True', {}, true, 'Boolean logic with precedence');
testSuite.addTest('list_access', '[1, 2, 3][1]', {}, 2, 'List element access');
testSuite.addTest('dict_access', '{"a": 1, "b": 2}["a"]', {}, 1, 'Dictionary element access');

// 上下文相关测试
const context = {
    user: { name: 'Alice', age: 25, active: true },
    items: [1, 2, 3, 4, 5],
    len: (arr) => arr.length
};

testSuite.addTest('context_access', 'user.name', context, 'Alice', 'Context variable access');
testSuite.addTest('function_call', 'len(items)', context, 5, 'Function call with context');
testSuite.addTest('complex_expression', 'user.active and user.age >= 18', context, true, 'Complex boolean expression');

// 运行测试
const results = testSuite.runTests();
```

### 2. 性能基准测试
```javascript
class PerformanceBenchmark {
    constructor() {
        this.benchmarks = [];
    }

    addBenchmark(name, expression, context, iterations = 10000) {
        this.benchmarks.push({ name, expression, context, iterations });
    }

    runBenchmarks() {
        console.log('🚀 Running Performance Benchmarks...\n');

        for (const benchmark of this.benchmarks) {
            this.runSingleBenchmark(benchmark);
        }
    }

    runSingleBenchmark({ name, expression, context, iterations }) {
        // 预热
        for (let i = 0; i < 100; i++) {
            evaluateExpr(expression, context);
        }

        // 基准测试
        const startTime = performance.now();

        for (let i = 0; i < iterations; i++) {
            evaluateExpr(expression, context);
        }

        const endTime = performance.now();
        const totalTime = endTime - startTime;
        const avgTime = totalTime / iterations;
        const opsPerSecond = 1000 / avgTime;

        console.log(`📈 ${name}:`);
        console.log(`   Expression: ${expression}`);
        console.log(`   Iterations: ${iterations.toLocaleString()}`);
        console.log(`   Total time: ${totalTime.toFixed(2)}ms`);
        console.log(`   Average time: ${avgTime.toFixed(4)}ms`);
        console.log(`   Operations/sec: ${opsPerSecond.toFixed(0)}`);
        console.log('');
    }
}

// 使用示例
const benchmark = new PerformanceBenchmark();

const context = {
    user: { name: 'Alice', age: 25, active: true },
    items: Array.from({ length: 1000 }, (_, i) => i),
    len: (arr) => arr.length,
    sum: (arr) => arr.reduce((a, b) => a + b, 0)
};

benchmark.addBenchmark('Simple Math', '2 + 3 * 4', {});
benchmark.addBenchmark('String Operations', '"hello" + " " + "world"', {});
benchmark.addBenchmark('Context Access', 'user.name', context);
benchmark.addBenchmark('Function Call', 'len(items)', context);
benchmark.addBenchmark('Complex Expression', 'user.active and user.age >= 18 and len(items) > 100', context);

benchmark.runBenchmarks();
```

## 📖 相关资源

- [Python 表达式语法参考](https://docs.python.org/3/reference/expressions.html)
- [Pratt 解析器算法](https://en.wikipedia.org/wiki/Pratt_parser)
- [抽象语法树 (AST)](https://en.wikipedia.org/wiki/Abstract_syntax_tree)
- [Python datetime 模块](https://docs.python.org/3/library/datetime.html)
- [JavaScript Proxy 对象](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Proxy)

## 🎯 总结

Odoo Python-JavaScript 桥接模块是一个完整的 Python 表达式处理系统，提供了：
- **完整的 Python 语法支持**: 从词法分析到语法分析再到解释执行
- **类型系统兼容**: 实现与 Python 一致的类型系统和操作语义
- **丰富的内置功能**: 内置函数、日期时间处理、工具函数等
- **高性能实现**: 优化的算法和缓存机制
- **易用的接口**: 简洁的 API 和完善的错误处理
- **扩展性**: 支持自定义函数和类型扩展
- **测试支持**: 完整的测试和基准测试框架

这个模块为 Odoo Web 客户端提供了强大的 Python 表达式处理能力，使得复杂的业务逻辑可以用熟悉的 Python 语法在 JavaScript 环境中执行，大大提升了开发效率和代码可维护性。通过其灵活的扩展机制，开发者可以轻松添加自定义功能，满足特定的业务需求。

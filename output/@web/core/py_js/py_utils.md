# Odoo Python 工具函数 (Python Utils) 学习资料

## 文件概述

**文件路径**: `output/@web/core/py_js/py_utils.js`  
**原始路径**: `/web/static/src/core/py_js/py_utils.js`  
**模块类型**: 核心基础模块 - Python 工具函数  
**代码行数**: 138 行  
**依赖关系**: 
- `@web/core/py_js/py_parser` - Python 语法分析器（bp 函数）
- `@web/core/py_js/py_date` - Python 日期时间类型

## 模块功能

Python 工具函数模块是 Odoo Web 客户端 Python-JavaScript 桥接系统的辅助工具组件，负责：
- 将 JavaScript 值转换为 Python AST 节点
- 将 Python AST 节点格式化为可读的 Python 代码
- 提供 Python 字典的代理实现
- 处理类型转换和格式化的复杂逻辑
- 为其他 Python 模块提供通用的工具函数

## 核心函数详解

### 1. toPyValue 函数

#### 函数签名
```javascript
function toPyValue(value) {
    // 将任意 JavaScript 值转换为 Python AST 节点
}
```

#### 类型转换逻辑

##### 基本类型转换
```javascript
switch (typeof value) {
    case "string":
        return { type: 1 /* String */, value };
    case "number":
        return { type: 0 /* Number */, value };
    case "boolean":
        return { type: 2 /* Boolean */, value };
}
```

**转换规则**:
- **字符串**: 直接转换为 AST String 节点
- **数字**: 直接转换为 AST Number 节点
- **布尔值**: 直接转换为 AST Boolean 节点

##### 对象类型转换
```javascript
case "object":
    if (Array.isArray(value)) {
        return { type: 4 /* List */, value: value.map(toPyValue) };
    } else if (value === null) {
        return { type: 3 /* None */ };
    } else if (value instanceof Date) {
        return { type: 1, value: PyDateTime.convertDate(value) };
    } else if (value instanceof PyDate || value instanceof PyDateTime) {
        return { type: 1, value };
    } else {
        const content = {};
        for (const key in value) {
            content[key] = toPyValue(value[key]);
        }
        return { type: 11 /* Dictionary */, value: content };
    }
```

**对象转换规则**:
- **数组**: 递归转换为 AST List 节点
- **null**: 转换为 AST None 节点
- **Date 对象**: 转换为 PyDateTime 后包装为 String 节点
- **PyDate/PyDateTime**: 直接包装为 String 节点
- **普通对象**: 递归转换为 AST Dictionary 节点

#### 使用示例
```javascript
import { toPyValue } from "@web/core/py_js/py_utils";

// 基本类型
console.log(toPyValue("hello"));     // {type: 1, value: "hello"}
console.log(toPyValue(42));          // {type: 0, value: 42}
console.log(toPyValue(true));        // {type: 2, value: true}
console.log(toPyValue(null));        // {type: 3}

// 复合类型
console.log(toPyValue([1, 2, 3]));   
// {type: 4, value: [{type: 0, value: 1}, {type: 0, value: 2}, {type: 0, value: 3}]}

console.log(toPyValue({a: 1, b: "test"}));
// {type: 11, value: {a: {type: 0, value: 1}, b: {type: 1, value: "test"}}}

// 日期类型
const date = new Date();
console.log(toPyValue(date));        // {type: 1, value: PyDateTime}
```

### 2. formatAST 函数

#### 函数签名
```javascript
function formatAST(ast, lbp = 0) {
    // 将 Python AST 节点格式化为可读的 Python 代码字符串
}
```

#### 格式化逻辑

##### 基本类型格式化
```javascript
switch (ast.type) {
    case 3 /* None */:
        return "None";
    case 1 /* String */:
        return JSON.stringify(ast.value);
    case 0 /* Number */:
        return String(ast.value);
    case 2 /* Boolean */:
        return ast.value ? "True" : "False";
}
```

##### 容器类型格式化
```javascript
case 4 /* List */:
    return `[${ast.value.map(formatAST).join(", ")}]`;
case 10 /* Tuple */:
    return `(${ast.value.map(formatAST).join(", ")})`;
case 11 /* Dictionary */: {
    const pairs = [];
    for (const k in ast.value) {
        pairs.push(`"${k}": ${formatAST(ast.value[k])}`);
    }
    return `{` + pairs.join(", ") + `}`;
}
```

##### 操作符格式化
```javascript
case 6 /* UnaryOperator */:
    if (ast.op === "not") {
        return `not ` + formatAST(ast.right, 50);
    }
    return ast.op + formatAST(ast.right, 130);

case 7 /* BinaryOperator */: {
    const abp = bp(ast.op);
    const str = `${formatAST(ast.left, abp)} ${ast.op} ${formatAST(ast.right, abp)}`;
    return abp < lbp ? `(${str})` : str;
}
```

**优先级处理**:
- **绑定力 (bp)**: 使用解析器的绑定力函数确定优先级
- **括号添加**: 当操作符优先级低于上下文时添加括号
- **递归格式化**: 递归处理子表达式

##### 高级结构格式化
```javascript
case 12 /* Lookup */:
    return `${formatAST(ast.target)}[${formatAST(ast.key)}]`;

case 15 /* ObjLookup */:
    return `${formatAST(ast.obj, 150)}.${ast.key}`;

case 13 /* If */: {
    const { ifTrue, condition, ifFalse } = ast;
    return `${formatAST(ifTrue)} if ${formatAST(condition)} else ${formatAST(ifFalse)}`;
}

case 8 /* FunctionCall */: {
    const args = ast.args.map(formatAST);
    const kwargs = [];
    for (const kwarg in ast.kwargs) {
        kwargs.push(`${kwarg} = ${formatAST(ast.kwargs[kwarg])}`);
    }
    const argStr = args.concat(kwargs).join(", ");
    return `${formatAST(ast.fn)}(${argStr})`;
}
```

#### 使用示例
```javascript
import { formatAST } from "@web/core/py_js/py_utils";
import { parse } from "@web/core/py_js/py_parser";
import { tokenize } from "@web/core/py_js/py_tokenizer";

// 解析并格式化表达式
function formatExpression(expression) {
    const tokens = tokenize(expression);
    const ast = parse(tokens);
    return formatAST(ast);
}

// 基本表达式
console.log(formatExpression("2 + 3 * 4"));        // "2 + 3 * 4"
console.log(formatExpression("(2 + 3) * 4"));      // "(2 + 3) * 4"

// 复杂表达式
console.log(formatExpression("func(a, b=2)"));     // "func(a, b = 2)"
console.log(formatExpression("x if y else z"));    // "x if y else z"
console.log(formatExpression("obj.attr[key]"));    // "obj.attr[key]"

// 数据结构
console.log(formatExpression("[1, 2, 3]"));        // "[1, 2, 3]"
console.log(formatExpression("{'a': 1, 'b': 2}")); // "{'a': 1, 'b': 2}"
```

### 3. Python 字典代理

#### PY_DICT 原型
```javascript
const PY_DICT = Object.create(null);
```

**特点**:
- **无原型**: 使用 `Object.create(null)` 创建无原型对象
- **纯净**: 避免继承 Object.prototype 的方法
- **标识**: 作为 Python 字典的原型标识

#### toPyDict 函数
```javascript
function toPyDict(obj) {
    return new Proxy(obj, {
        getPrototypeOf() {
            return PY_DICT;
        },
    });
}
```

**代理功能**:
- **原型伪装**: 使对象看起来像 Python 字典
- **类型检查**: 支持 `instanceof` 和原型检查
- **透明代理**: 保持原对象的所有功能

#### 使用示例
```javascript
import { toPyDict, PY_DICT } from "@web/core/py_js/py_utils";

// 创建 Python 字典代理
const jsObj = { a: 1, b: 2, c: 3 };
const pyDict = toPyDict(jsObj);

// 类型检查
console.log(Object.getPrototypeOf(pyDict) === PY_DICT); // true

// 正常使用
console.log(pyDict.a);        // 1
pyDict.d = 4;
console.log(pyDict.d);        // 4

// 与原对象同步
console.log(jsObj.d);         // 4
```

## 实际使用示例

### 1. 数据转换器
```javascript
class PythonDataConverter {
    constructor() {
        this.toPyValue = toPyValue;
        this.formatAST = formatAST;
    }
    
    convertJSData(data) {
        return this.toPyValue(data);
    }
    
    convertToPythonCode(data) {
        const ast = this.toPyValue(data);
        return this.formatAST(ast);
    }
    
    convertComplexData(data) {
        // 处理复杂的嵌套数据结构
        if (data instanceof Map) {
            const obj = {};
            for (const [key, value] of data) {
                obj[key] = value;
            }
            return this.toPyValue(obj);
        }
        
        if (data instanceof Set) {
            return this.toPyValue(Array.from(data));
        }
        
        return this.toPyValue(data);
    }
    
    formatWithContext(ast, context = {}) {
        // 带上下文的格式化
        const formatted = this.formatAST(ast);
        
        // 替换变量名
        let result = formatted;
        for (const [oldName, newName] of Object.entries(context)) {
            const regex = new RegExp(`\\b${oldName}\\b`, 'g');
            result = result.replace(regex, newName);
        }
        
        return result;
    }
}

// 使用示例
const converter = new PythonDataConverter();

// 转换复杂数据
const complexData = {
    users: [
        { name: "Alice", age: 25, active: true },
        { name: "Bob", age: 30, active: false }
    ],
    config: {
        debug: true,
        timeout: 30,
        features: ["auth", "logging"]
    },
    timestamp: new Date()
};

const pythonCode = converter.convertToPythonCode(complexData);
console.log(pythonCode);
// 输出类似：
// {"users": [{"name": "Alice", "age": 25, "active": True}, {"name": "Bob", "age": 30, "active": False}], "config": {"debug": True, "timeout": 30, "features": ["auth", "logging"]}, "timestamp": "2024-01-15 14:30:25"}
```

### 2. AST 美化器
```javascript
class ASTFormatter {
    constructor() {
        this.formatAST = formatAST;
        this.indentLevel = 0;
        this.indentSize = 4;
    }
    
    formatWithIndentation(ast) {
        return this.formatASTIndented(ast);
    }
    
    formatASTIndented(ast, level = 0) {
        const indent = " ".repeat(level * this.indentSize);
        
        switch (ast.type) {
            case 11 /* Dictionary */: {
                if (Object.keys(ast.value).length === 0) {
                    return "{}";
                }
                
                const pairs = [];
                for (const k in ast.value) {
                    const key = JSON.stringify(k);
                    const value = this.formatASTIndented(ast.value[k], level + 1);
                    pairs.push(`${indent}    ${key}: ${value}`);
                }
                
                return `{\n${pairs.join(",\n")}\n${indent}}`;
            }
            
            case 4 /* List */: {
                if (ast.value.length === 0) {
                    return "[]";
                }
                
                if (ast.value.length <= 3 && ast.value.every(item => 
                    item.type <= 3 /* 基本类型 */)) {
                    return `[${ast.value.map(item => this.formatAST(item)).join(", ")}]`;
                }
                
                const items = ast.value.map(item => 
                    `${indent}    ${this.formatASTIndented(item, level + 1)}`
                );
                
                return `[\n${items.join(",\n")}\n${indent}]`;
            }
            
            default:
                return this.formatAST(ast);
        }
    }
    
    formatMultiline(ast) {
        // 多行格式化，适合复杂表达式
        switch (ast.type) {
            case 8 /* FunctionCall */: {
                const fn = this.formatAST(ast.fn);
                const args = ast.args.map(arg => this.formatASTIndented(arg, 1));
                const kwargs = [];
                
                for (const kwarg in ast.kwargs) {
                    const value = this.formatASTIndented(ast.kwargs[kwarg], 1);
                    kwargs.push(`    ${kwarg}=${value}`);
                }
                
                const allArgs = args.concat(kwargs);
                
                if (allArgs.length <= 2) {
                    return `${fn}(${allArgs.join(", ")})`;
                }
                
                return `${fn}(\n${allArgs.join(",\n")}\n)`;
            }
            
            default:
                return this.formatASTIndented(ast);
        }
    }
}

// 使用示例
const formatter = new ASTFormatter();

const complexAST = toPyValue({
    config: {
        database: {
            host: "localhost",
            port: 5432,
            name: "odoo_db"
        },
        features: ["auth", "logging", "caching"],
        debug: true
    }
});

console.log(formatter.formatWithIndentation(complexAST));
// 输出：
// {
//     "config": {
//         "database": {
//             "host": "localhost",
//             "port": 5432,
//             "name": "odoo_db"
//         },
//         "features": ["auth", "logging", "caching"],
//         "debug": True
//     }
// }
```

### 3. 表达式构建器
```javascript
class PythonExpressionBuilder {
    constructor() {
        this.toPyValue = toPyValue;
        this.formatAST = formatAST;
    }
    
    // 构建比较表达式
    buildComparison(left, operator, right) {
        const leftAST = typeof left === 'string' ? 
            { type: 5 /* Name */, value: left } : this.toPyValue(left);
        const rightAST = typeof right === 'string' ? 
            { type: 5 /* Name */, value: right } : this.toPyValue(right);
        
        const ast = {
            type: 7 /* BinaryOperator */,
            op: operator,
            left: leftAST,
            right: rightAST
        };
        
        return this.formatAST(ast);
    }
    
    // 构建函数调用
    buildFunctionCall(funcName, args = [], kwargs = {}) {
        const argsAST = args.map(arg => 
            typeof arg === 'string' ? 
                { type: 5 /* Name */, value: arg } : this.toPyValue(arg)
        );
        
        const kwargsAST = {};
        for (const [key, value] of Object.entries(kwargs)) {
            kwargsAST[key] = typeof value === 'string' ? 
                { type: 5 /* Name */, value: value } : this.toPyValue(value);
        }
        
        const ast = {
            type: 8 /* FunctionCall */,
            fn: { type: 5 /* Name */, value: funcName },
            args: argsAST,
            kwargs: kwargsAST
        };
        
        return this.formatAST(ast);
    }
    
    // 构建条件表达式
    buildConditional(condition, ifTrue, ifFalse) {
        const conditionAST = typeof condition === 'string' ? 
            { type: 5 /* Name */, value: condition } : this.toPyValue(condition);
        const ifTrueAST = typeof ifTrue === 'string' ? 
            { type: 5 /* Name */, value: ifTrue } : this.toPyValue(ifTrue);
        const ifFalseAST = typeof ifFalse === 'string' ? 
            { type: 5 /* Name */, value: ifFalse } : this.toPyValue(ifFalse);
        
        const ast = {
            type: 13 /* If */,
            condition: conditionAST,
            ifTrue: ifTrueAST,
            ifFalse: ifFalseAST
        };
        
        return this.formatAST(ast);
    }
    
    // 构建复合表达式
    buildCompoundExpression(expressions, operator = "and") {
        if (expressions.length === 0) return "True";
        if (expressions.length === 1) return expressions[0];
        
        let result = expressions[0];
        for (let i = 1; i < expressions.length; i++) {
            result = `(${result}) ${operator} (${expressions[i]})`;
        }
        
        return result;
    }
}

// 使用示例
const builder = new PythonExpressionBuilder();

// 构建比较表达式
console.log(builder.buildComparison("age", ">=", 18));
// "age >= 18"

// 构建函数调用
console.log(builder.buildFunctionCall("filter", ["items"], { key: "lambda x: x.active" }));
// "filter(items, key = lambda x: x.active)"

// 构建条件表达式
console.log(builder.buildConditional("user.is_admin", "admin_panel", "user_panel"));
// "admin_panel if user.is_admin else user_panel"

// 构建复合表达式
const conditions = [
    builder.buildComparison("age", ">=", 18),
    builder.buildComparison("status", "==", "active"),
    builder.buildFunctionCall("has_permission", ["user", "read"])
];
console.log(builder.buildCompoundExpression(conditions));
// "(age >= 18) and (status == \"active\") and (has_permission(user, \"read\"))"
```

### 4. 类型安全的转换器
```javascript
class TypeSafePythonConverter {
    constructor() {
        this.typeValidators = new Map([
            ['string', (v) => typeof v === 'string'],
            ['number', (v) => typeof v === 'number' && !isNaN(v)],
            ['boolean', (v) => typeof v === 'boolean'],
            ['array', (v) => Array.isArray(v)],
            ['object', (v) => v !== null && typeof v === 'object' && !Array.isArray(v)],
            ['date', (v) => v instanceof Date],
            ['pydate', (v) => v instanceof PyDate || v instanceof PyDateTime]
        ]);
    }
    
    validateType(value, expectedType) {
        const validator = this.typeValidators.get(expectedType);
        if (!validator) {
            throw new Error(`Unknown type: ${expectedType}`);
        }
        return validator(value);
    }
    
    convertWithValidation(value, schema) {
        if (schema.type && !this.validateType(value, schema.type)) {
            throw new Error(`Type validation failed: expected ${schema.type}, got ${typeof value}`);
        }
        
        if (schema.required && (value === null || value === undefined)) {
            throw new Error(`Required value is missing`);
        }
        
        if (schema.validate && !schema.validate(value)) {
            throw new Error(`Custom validation failed`);
        }
        
        return toPyValue(value);
    }
    
    convertObject(obj, schema) {
        const result = {};
        
        for (const [key, fieldSchema] of Object.entries(schema)) {
            if (key in obj) {
                result[key] = this.convertWithValidation(obj[key], fieldSchema);
            } else if (fieldSchema.required) {
                throw new Error(`Required field missing: ${key}`);
            } else if (fieldSchema.default !== undefined) {
                result[key] = toPyValue(fieldSchema.default);
            }
        }
        
        return { type: 11 /* Dictionary */, value: result };
    }
}

// 使用示例
const safeConverter = new TypeSafePythonConverter();

const userSchema = {
    name: { type: 'string', required: true },
    age: { 
        type: 'number', 
        required: true,
        validate: (v) => v >= 0 && v <= 150
    },
    email: { 
        type: 'string',
        validate: (v) => v.includes('@')
    },
    active: { type: 'boolean', default: true },
    tags: { type: 'array', default: [] }
};

try {
    const userData = {
        name: "Alice",
        age: 25,
        email: "<EMAIL>"
    };
    
    const convertedUser = safeConverter.convertObject(userData, userSchema);
    console.log(formatAST(convertedUser));
    // {"name": "Alice", "age": 25, "email": "<EMAIL>", "active": True, "tags": []}
    
} catch (error) {
    console.error("Conversion failed:", error.message);
}
```

## 设计模式分析

### 1. 访问者模式 (Visitor Pattern)
```javascript
function formatAST(ast, lbp = 0) {
    switch (ast.type) {
        case 0: return formatNumber(ast);
        case 1: return formatString(ast);
        // ...
    }
}
```

**特点**:
- **类型分发**: 根据 AST 节点类型选择处理方法
- **递归遍历**: 递归处理子节点
- **扩展性**: 易于添加新的节点类型处理

### 2. 代理模式 (Proxy Pattern)
```javascript
function toPyDict(obj) {
    return new Proxy(obj, {
        getPrototypeOf() {
            return PY_DICT;
        },
    });
}
```

**应用**:
- **透明代理**: 保持原对象功能的同时添加新特性
- **类型伪装**: 使 JavaScript 对象看起来像 Python 字典
- **拦截控制**: 可以拦截和修改对象操作

### 3. 工厂模式 (Factory Pattern)
```javascript
function toPyValue(value) {
    // 根据输入类型创建相应的 AST 节点
    switch (typeof value) {
        case "string": return createStringNode(value);
        case "number": return createNumberNode(value);
        // ...
    }
}
```

### 4. 策略模式 (Strategy Pattern)
```javascript
// 不同类型使用不同的转换策略
const conversionStrategies = {
    string: (v) => ({ type: 1, value: v }),
    number: (v) => ({ type: 0, value: v }),
    boolean: (v) => ({ type: 2, value: v })
};
```

## 性能优化

### 1. 类型检查优化
```javascript
// 快速类型检查
const typeCheckers = {
    isString: (v) => typeof v === 'string',
    isNumber: (v) => typeof v === 'number',
    isArray: Array.isArray,
    isDate: (v) => v instanceof Date
};
```

### 2. 缓存机制
```javascript
class CachedConverter {
    constructor() {
        this.cache = new Map();
    }
    
    toPyValue(value) {
        const key = this.getCacheKey(value);
        if (this.cache.has(key)) {
            return this.cache.get(key);
        }
        
        const result = toPyValue(value);
        this.cache.set(key, result);
        return result;
    }
}
```

### 3. 字符串构建优化
```javascript
// 使用数组拼接而不是字符串连接
function formatList(items) {
    const parts = ["["];
    for (let i = 0; i < items.length; i++) {
        if (i > 0) parts.push(", ");
        parts.push(formatAST(items[i]));
    }
    parts.push("]");
    return parts.join("");
}
```

## 最佳实践

### 1. 类型安全
```javascript
// ✅ 推荐：严格的类型检查
function toPyValue(value) {
    if (value === null) return { type: 3 };
    if (typeof value === 'string') return { type: 1, value };
    // ...
}

// ❌ 避免：不安全的类型假设
function badToPyValue(value) {
    return { type: 1, value }; // 假设所有值都是字符串
}
```

### 2. 错误处理
```javascript
// ✅ 推荐：提供详细的错误信息
function formatAST(ast) {
    if (!ast || typeof ast !== 'object') {
        throw new Error(`Invalid AST node: ${ast}`);
    }
    if (typeof ast.type !== 'number') {
        throw new Error(`AST node missing type: ${JSON.stringify(ast)}`);
    }
    // ...
}

// ❌ 避免：模糊的错误信息
function badFormatAST(ast) {
    return ast.value; // 可能失败但没有错误处理
}
```

### 3. 性能考虑
```javascript
// ✅ 推荐：避免重复计算
const formatters = new Map([
    [0, (ast) => String(ast.value)],
    [1, (ast) => JSON.stringify(ast.value)],
    [2, (ast) => ast.value ? "True" : "False"]
]);

function formatAST(ast) {
    const formatter = formatters.get(ast.type);
    return formatter ? formatter(ast) : defaultFormat(ast);
}

// ❌ 避免：重复的 switch 语句
function slowFormatAST(ast) {
    switch (ast.type) {
        // 每次都要执行完整的 switch
    }
}
```

## 总结

Python 工具函数模块是 Odoo Web 客户端 Python-JavaScript 桥接系统的重要辅助组件，它提供了：
- **类型转换**: 将 JavaScript 值转换为 Python AST 节点
- **代码格式化**: 将 AST 节点格式化为可读的 Python 代码
- **字典代理**: 提供 Python 字典的 JavaScript 实现
- **工具函数**: 为其他模块提供通用的辅助功能
- **类型安全**: 确保转换过程的类型安全和错误处理

这个模块为 Odoo 的 Python 表达式系统提供了可靠的工具支持，简化了 Python 和 JavaScript 之间的数据交换和代码生成。

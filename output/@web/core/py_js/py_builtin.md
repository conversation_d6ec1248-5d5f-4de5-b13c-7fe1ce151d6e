# Odoo Python 内置函数 JavaScript 实现 (Python Builtins) 学习资料

## 文件概述

**文件路径**: `output/@web/core/py_js/py_builtin.js`  
**原始路径**: `/web/static/src/core/py_js/py_builtin.js`  
**模块类型**: 核心基础模块 - Python 内置函数 JavaScript 实现  
**代码行数**: 119 行  
**依赖关系**: 
- `@web/core/py_js/py_date` - Python 日期时间类型

## 模块功能

Python 内置函数模块是 Odoo Web 客户端 Python-JavaScript 桥接系统的核心组件，负责：
- 提供 Python 内置函数的 JavaScript 实现
- 实现 Python 数据类型的转换和操作
- 处理可迭代对象的操作
- 提供日期时间相关的内置函数
- 确保 Python 语义在 JavaScript 中的正确实现

## 核心组件分析

### 1. EvaluationError 异常类
```javascript
const EvaluationError = class EvaluationError extends Error {}
```

**功能**:
- **专用异常**: 用于 Python 表达式求值过程中的错误
- **错误传播**: 保持与 Python 异常处理的一致性
- **调试支持**: 提供清晰的错误信息

### 2. execOnIterable 工具函数
```javascript
function execOnIterable(iterable, func) {
    if (iterable === null) {
        throw new EvaluationError(`value not iterable`);
    }
    if (typeof iterable === "object" && !Array.isArray(iterable) && !(iterable instanceof Set)) {
        // dicts are considered as iterable in Python
        iterable = Object.keys(iterable);
    }
    if (typeof iterable?.[Symbol.iterator] !== "function") {
        throw new EvaluationError(`value not iterable`);
    }
    return func(iterable);
}
```

**功能分析**:

#### 空值检查
```javascript
if (iterable === null) {
    throw new EvaluationError(`value not iterable`);
}
```
- **Python 语义**: Python 中 `set(None)` 会抛出 TypeError
- **JavaScript 差异**: JavaScript 中 `new Set(null)` 是合法的
- **一致性保证**: 确保行为与 Python 一致

#### 字典处理
```javascript
if (typeof iterable === "object" && !Array.isArray(iterable) && !(iterable instanceof Set)) {
    iterable = Object.keys(iterable);
}
```
- **Python 字典**: Python 中字典是可迭代的，迭代键
- **JavaScript 对象**: 转换为键数组进行迭代
- **类型排除**: 排除数组和 Set，它们已经是可迭代的

#### 迭代器检查
```javascript
if (typeof iterable?.[Symbol.iterator] !== "function") {
    throw new EvaluationError(`value not iterable`);
}
```
- **标准检查**: 使用 JavaScript 标准的迭代器协议
- **可选链**: 安全地检查迭代器方法
- **错误处理**: 不可迭代时抛出异常

## BUILTINS 对象详解

### 1. bool() 函数
```javascript
bool(value) {
    switch (typeof value) {
        case "number":
            return value !== 0;
        case "string":
            return value !== "";
        case "boolean":
            return value;
        case "object":
            if (value === null || value === undefined) {
                return false;
            }
            if (value.isTrue) {
                return value.isTrue();
            }
            if (value instanceof Array) {
                return !!value.length;
            }
            if (value instanceof Set) {
                return !!value.size;
            }
            return Object.keys(value).length !== 0;
    }
    return true;
}
```

**Python 真值语义实现**:

#### 数值类型
- **Python**: `bool(0)` → `False`, `bool(非0)` → `True`
- **实现**: `value !== 0`

#### 字符串类型
- **Python**: `bool("")` → `False`, `bool("非空")` → `True`
- **实现**: `value !== ""`

#### 布尔类型
- **Python**: 直接返回布尔值
- **实现**: `return value`

#### 对象类型
- **null/undefined**: 对应 Python 的 `None` → `False`
- **自定义真值**: 支持 `isTrue()` 方法的对象
- **数组**: 空数组 → `False`, 非空数组 → `True`
- **Set**: 空集合 → `False`, 非空集合 → `True`
- **普通对象**: 无属性 → `False`, 有属性 → `True`

### 2. set() 函数
```javascript
set(iterable) {
    if (arguments.length > 2) {
        throw new EvaluationError(
            `set expected at most 1 argument, got (${arguments.length - 1}`
        );
    }
    return execOnIterable(iterable, (iterable) => {
        return new Set(iterable);
    });
}
```

**功能特点**:
- **参数检查**: 最多接受1个参数（加上隐式的 kwargs）
- **可迭代检查**: 使用 `execOnIterable` 确保参数可迭代
- **Set 创建**: 创建 JavaScript Set 对象

### 3. max() 和 min() 函数
```javascript
max(...args) {
    return Math.max(...args.slice(0, -1));
},

min(...args) {
    return Math.min(...args.slice(0, -1));
}
```

**实现细节**:
- **参数处理**: 移除最后一个参数（kwargs）
- **委托实现**: 使用 JavaScript 内置的 Math.max/min
- **展开操作**: 使用展开语法传递参数

### 4. 时间相关函数

#### time.strftime()
```javascript
time: {
    strftime(format) {
        return PyDateTime.now().strftime(format);
    },
}
```

#### 日期快捷方式
```javascript
context_today() {
    return PyDate.today();
},

get current_date() {
    // deprecated: today should be prefered
    return this.today;
},

get today() {
    return PyDate.today().strftime("%Y-%m-%d");
},

get now() {
    return PyDateTime.now().strftime("%Y-%m-%d %H:%M:%S");
}
```

**功能说明**:
- **context_today()**: 返回 PyDate 对象
- **today**: 返回格式化的日期字符串 "YYYY-MM-DD"
- **now**: 返回格式化的日期时间字符串 "YYYY-MM-DD HH:MM:SS"
- **current_date**: 已弃用，建议使用 today

### 5. datetime 模块
```javascript
datetime: {
    time: PyTime,
    timedelta: PyTimeDelta,
    datetime: PyDateTime,
    date: PyDate,
}
```

**模块结构**:
- **time**: PyTime 类
- **timedelta**: PyTimeDelta 类
- **datetime**: PyDateTime 类
- **date**: PyDate 类

### 6. 其他内置对象
```javascript
relativedelta: PyRelativeDelta,
true: true,
false: false,
```

## 实际使用示例

### 1. 基本内置函数使用
```javascript
import { BUILTINS } from "@web/core/py_js/py_builtin";

// bool() 函数测试
console.log(BUILTINS.bool(0));        // false
console.log(BUILTINS.bool(""));       // false
console.log(BUILTINS.bool([]));       // false
console.log(BUILTINS.bool({}));       // false
console.log(BUILTINS.bool(1));        // true
console.log(BUILTINS.bool("hello"));  // true
console.log(BUILTINS.bool([1, 2]));   // true

// set() 函数测试
const mySet = BUILTINS.set([1, 2, 2, 3]);
console.log(mySet);  // Set {1, 2, 3}

// max/min 函数测试
console.log(BUILTINS.max(1, 5, 3, {}));  // 5 (忽略最后的 kwargs)
console.log(BUILTINS.min(1, 5, 3, {}));  // 1
```

### 2. 日期时间函数使用
```javascript
// 获取今天的日期
const today = BUILTINS.today;
console.log(today);  // "2024-01-15"

// 获取当前时间
const now = BUILTINS.now;
console.log(now);  // "2024-01-15 14:30:25"

// 使用 context_today
const todayObj = BUILTINS.context_today();
console.log(todayObj.strftime("%Y-%m-%d"));  // "2024-01-15"

// 时间格式化
const formatted = BUILTINS.time.strftime("%Y年%m月%d日");
console.log(formatted);  // "2024年01月15日"
```

### 3. Python 表达式求值器中的使用
```javascript
class PythonExpressionEvaluator {
    constructor() {
        this.builtins = BUILTINS;
        this.context = {};
    }
    
    evaluate(expression, context = {}) {
        // 合并上下文和内置函数
        const evalContext = {
            ...this.builtins,
            ...context
        };
        
        try {
            // 这里会使用实际的 Python 解析器
            return this.parseAndEvaluate(expression, evalContext);
        } catch (error) {
            if (error instanceof EvaluationError) {
                throw new Error(`Python evaluation error: ${error.message}`);
            }
            throw error;
        }
    }
    
    // 示例：简单的表达式求值
    evaluateSimple(expression, context = {}) {
        const evalContext = {
            ...this.builtins,
            ...context
        };
        
        // 替换 Python 语法为 JavaScript
        let jsExpression = expression
            .replace(/\bTrue\b/g, 'true')
            .replace(/\bFalse\b/g, 'false')
            .replace(/\bNone\b/g, 'null');
        
        // 在安全的上下文中求值
        return Function(...Object.keys(evalContext), `return ${jsExpression}`)
            (...Object.values(evalContext));
    }
}

// 使用示例
const evaluator = new PythonExpressionEvaluator();

// 测试 bool 函数
console.log(evaluator.evaluateSimple('bool(0)'));     // false
console.log(evaluator.evaluateSimple('bool("test")')); // true

// 测试日期函数
console.log(evaluator.evaluateSimple('today'));       // "2024-01-15"
console.log(evaluator.evaluateSimple('now'));         // "2024-01-15 14:30:25"

// 测试集合函数
const result = evaluator.evaluateSimple('set([1, 2, 2, 3])');
console.log(result);  // Set {1, 2, 3}
```

### 4. 自定义对象的真值测试
```javascript
class CustomObject {
    constructor(value) {
        this.value = value;
    }
    
    isTrue() {
        return this.value > 0;
    }
}

// 测试自定义对象
const obj1 = new CustomObject(5);
const obj2 = new CustomObject(-1);

console.log(BUILTINS.bool(obj1));  // true
console.log(BUILTINS.bool(obj2));  // false
```

### 5. 错误处理示例
```javascript
class SafePythonEvaluator {
    constructor() {
        this.builtins = BUILTINS;
    }
    
    safeSet(iterable) {
        try {
            return this.builtins.set(iterable);
        } catch (error) {
            if (error instanceof EvaluationError) {
                console.error("Set creation failed:", error.message);
                return new Set(); // 返回空集合作为默认值
            }
            throw error;
        }
    }
    
    safeBool(value) {
        try {
            return this.builtins.bool(value);
        } catch (error) {
            console.error("Bool conversion failed:", error.message);
            return false; // 默认返回 false
        }
    }
}

// 使用示例
const safeEvaluator = new SafePythonEvaluator();

// 测试错误处理
console.log(safeEvaluator.safeSet(null));      // Set {} (空集合)
console.log(safeEvaluator.safeSet([1, 2, 3])); // Set {1, 2, 3}
console.log(safeEvaluator.safeBool(undefined)); // false
```

## 设计模式分析

### 1. 适配器模式 (Adapter Pattern)
```javascript
const BUILTINS = {
    bool(value) {
        // 适配 Python bool() 语义到 JavaScript
    },
    set(iterable) {
        // 适配 Python set() 语义到 JavaScript
    }
};
```

**作用**:
- **语义适配**: 将 Python 语义适配到 JavaScript 环境
- **接口统一**: 提供统一的内置函数接口
- **行为一致**: 确保与 Python 行为一致

### 2. 工厂模式 (Factory Pattern)
```javascript
set(iterable) {
    return execOnIterable(iterable, (iterable) => {
        return new Set(iterable);
    });
}
```

**实现**:
- **对象创建**: 统一创建 Set 对象的方式
- **参数验证**: 在创建前验证参数
- **错误处理**: 统一的错误处理逻辑

### 3. 策略模式 (Strategy Pattern)
```javascript
bool(value) {
    switch (typeof value) {
        case "number": return value !== 0;
        case "string": return value !== "";
        case "boolean": return value;
        case "object": // 复杂的对象处理策略
    }
}
```

**应用**:
- **类型策略**: 根据不同类型采用不同的真值判断策略
- **可扩展**: 易于添加新的类型处理策略
- **清晰**: 每种类型的处理逻辑清晰分离

### 4. 代理模式 (Proxy Pattern)
```javascript
get today() {
    return PyDate.today().strftime("%Y-%m-%d");
},

get now() {
    return PyDateTime.now().strftime("%Y-%m-%d %H:%M:%S");
}
```

**特点**:
- **延迟计算**: 使用 getter 实现延迟计算
- **实时值**: 每次访问都获取最新值
- **透明访问**: 对用户透明的代理访问

## 性能优化

### 1. 类型检查优化
```javascript
// 优化的 bool 函数
bool(value) {
    // 快速路径：处理最常见的情况
    if (value === 0 || value === "" || value === false || value === null || value === undefined) {
        return false;
    }
    if (value === true || (typeof value === "number" && value !== 0)) {
        return true;
    }
    
    // 复杂类型的处理
    return this.complexBool(value);
}
```

### 2. 缓存机制
```javascript
class OptimizedBuiltins {
    constructor() {
        this.dateCache = new Map();
        this.cacheTimeout = 1000; // 1秒缓存
    }
    
    get today() {
        const now = Date.now();
        const cached = this.dateCache.get('today');
        
        if (cached && (now - cached.timestamp) < this.cacheTimeout) {
            return cached.value;
        }
        
        const value = PyDate.today().strftime("%Y-%m-%d");
        this.dateCache.set('today', { value, timestamp: now });
        return value;
    }
}
```

### 3. 参数验证优化
```javascript
// 优化的 execOnIterable
function execOnIterableFast(iterable, func) {
    // 快速检查常见类型
    if (Array.isArray(iterable) || iterable instanceof Set) {
        return func(iterable);
    }
    
    // 完整检查
    return execOnIterable(iterable, func);
}
```

## 最佳实践

### 1. 错误处理
```javascript
// ✅ 推荐：提供清晰的错误信息
function validateIterable(iterable) {
    if (iterable === null || iterable === undefined) {
        throw new EvaluationError(`'${iterable}' object is not iterable`);
    }
    if (typeof iterable[Symbol.iterator] !== "function") {
        throw new EvaluationError(`'${typeof iterable}' object is not iterable`);
    }
}

// ❌ 避免：模糊的错误信息
function badValidation(iterable) {
    if (!iterable) {
        throw new Error("Invalid input");
    }
}
```

### 2. 类型安全
```javascript
// ✅ 推荐：严格的类型检查
function safeBool(value) {
    if (value === null || value === undefined) {
        return false;
    }
    
    switch (typeof value) {
        case "boolean": return value;
        case "number": return value !== 0 && !isNaN(value);
        case "string": return value.length > 0;
        default: return Boolean(value);
    }
}

// ❌ 避免：不安全的类型转换
function unsafeBool(value) {
    return !!value; // 可能不符合 Python 语义
}
```

### 3. 性能考虑
```javascript
// ✅ 推荐：缓存昂贵的计算
const dateFormatCache = new Map();

function getCachedDateFormat(format) {
    if (!dateFormatCache.has(format)) {
        dateFormatCache.set(format, PyDateTime.now().strftime(format));
    }
    return dateFormatCache.get(format);
}

// ❌ 避免：重复的昂贵计算
function expensiveFormat(format) {
    return PyDateTime.now().strftime(format); // 每次都重新计算
}
```

## 总结

Python 内置函数模块是 Odoo Web 客户端 Python-JavaScript 桥接系统的基础组件，它提供了：
- **语义一致性**: 确保 Python 内置函数在 JavaScript 中的正确实现
- **类型适配**: 处理 Python 和 JavaScript 之间的类型差异
- **错误处理**: 提供与 Python 一致的错误处理机制
- **性能优化**: 针对 JavaScript 环境的性能优化
- **扩展性**: 支持自定义对象的 Python 语义

这个模块为 Odoo 的 Python 表达式求值系统提供了可靠的基础，确保了 Python 代码在 JavaScript 环境中的正确执行。

# Odoo Python 主模块 (Python Main Module) 学习资料

## 文件概述

**文件路径**: `output/@web/core/py_js/py.js`  
**原始路径**: `/web/static/src/core/py_js/py.js`  
**模块类型**: 核心基础模块 - Python 主入口模块  
**代码行数**: 72 行  
**依赖关系**: 
- `@web/core/py_js/py_interpreter` - Python 解释器
- `@web/core/py_js/py_parser` - Python 语法分析器
- `@web/core/py_js/py_tokenizer` - Python 词法分析器
- `@web/core/py_js/py_utils` - Python 工具函数

## 模块功能

Python 主模块是 Odoo Web 客户端 Python-JavaScript 桥接系统的统一入口，负责：
- 提供简化的 Python 表达式解析和求值 API
- 整合词法分析、语法分析和解释执行的完整流程
- 处理表达式解析和求值过程中的错误
- 提供专门的布尔表达式求值功能
- 为外部模块提供统一的 Python 表达式处理接口

## 导出的核心函数

### 1. 基础函数重导出
```javascript
// 从各个子模块重导出核心函数
const { evaluate } = require("@web/core/py_js/py_interpreter");
const { parse } = require("@web/core/py_js/py_parser");
const { tokenize } = require("@web/core/py_js/py_tokenizer");
const { formatAST } = require("@web/core/py_js/py_utils");

// 统一导出
Object.assign(__exports, { evaluate, parse, tokenize, formatAST });
```

**重导出的函数**:
- **tokenize**: 词法分析函数
- **parse**: 语法分析函数
- **evaluate**: 表达式求值函数
- **formatAST**: AST 格式化函数

### 2. parseExpr 函数

#### 函数签名
```javascript
function parseExpr(expr) {
    const tokens = tokenize(expr);
    return parse(tokens);
}
```

**功能**:
- **一步解析**: 将字符串表达式直接解析为 AST
- **流程简化**: 封装词法分析和语法分析的两步过程
- **错误传播**: 自动传播解析过程中的错误

#### 使用示例
```javascript
import { parseExpr } from "@web/core/py_js/py";

// 解析简单表达式
const ast1 = parseExpr("2 + 3");
console.log(ast1);
// {
//   type: 7, op: "+",
//   left: {type: 0, value: 2},
//   right: {type: 0, value: 3}
// }

// 解析复杂表达式
const ast2 = parseExpr("func(x, y=2) if condition else default");
console.log(ast2.type); // 13 (If expression)

// 解析数据结构
const ast3 = parseExpr("[1, 2, {'key': 'value'}]");
console.log(ast3.type); // 4 (List)
```

### 3. evaluateExpr 函数

#### 函数签名
```javascript
function evaluateExpr(expr, context = {}) {
    let ast;
    try {
        ast = parseExpr(expr);
    } catch (error) {
        throw new EvalError(`Can not parse python expression: (${expr})\nError: ${error.message}`);
    }
    try {
        return evaluate(ast, context);
    } catch (error) {
        throw new EvalError(`Can not evaluate python expression: (${expr})\nError: ${error.message}`);
    }
}
```

**功能特点**:

#### 完整流程
1. **解析阶段**: 将表达式字符串解析为 AST
2. **求值阶段**: 在给定上下文中执行 AST
3. **错误处理**: 分别处理解析错误和求值错误

#### 错误处理
```javascript
// 解析错误处理
try {
    ast = parseExpr(expr);
} catch (error) {
    throw new EvalError(`Can not parse python expression: (${expr})\nError: ${error.message}`);
}

// 求值错误处理
try {
    return evaluate(ast, context);
} catch (error) {
    throw new EvalError(`Can not evaluate python expression: (${expr})\nError: ${error.message}`);
}
```

**错误信息格式**:
- **解析错误**: `Can not parse python expression: (表达式)\nError: 具体错误`
- **求值错误**: `Can not evaluate python expression: (表达式)\nError: 具体错误`

#### 使用示例
```javascript
import { evaluateExpr } from "@web/core/py_js/py";

// 基本表达式求值
console.log(evaluateExpr("2 + 3 * 4")); // 14
console.log(evaluateExpr("'hello' + ' world'")); // "hello world"

// 带上下文的求值
const context = {
    x: 10,
    y: 20,
    user: { name: "Alice", age: 25 },
    items: [1, 2, 3, 4, 5]
};

console.log(evaluateExpr("x + y", context)); // 30
console.log(evaluateExpr("user.name", context)); // "Alice"
console.log(evaluateExpr("len(items)", { ...context, len: arr => arr.length })); // 5

// 复杂表达式
console.log(evaluateExpr("user.age >= 18 and 'Alice' in user.name", context)); // true
console.log(evaluateExpr("sum(items) / len(items)", {
    ...context,
    sum: arr => arr.reduce((a, b) => a + b, 0),
    len: arr => arr.length
})); // 3 (平均值)

// 错误处理示例
try {
    evaluateExpr("invalid syntax +++", context);
} catch (error) {
    console.error(error.message);
    // "Can not parse python expression: (invalid syntax +++)\nError: ..."
}

try {
    evaluateExpr("undefined_variable", context);
} catch (error) {
    console.error(error.message);
    // "Can not evaluate python expression: (undefined_variable)\nError: Unknown variable: undefined_variable"
}
```

### 4. evaluateBooleanExpr 函数

#### 函数签名
```javascript
function evaluateBooleanExpr(expr, context = {}) {
    if (!expr || expr === 'False' || expr === '0') {
        return false;
    }
    if (expr === 'True' || expr === '1') {
        return true;
    }
    return evaluateExpr(`bool(${expr})`, context);
}
```

**功能特点**:

#### 快速路径优化
```javascript
// 常见布尔值的快速处理
if (!expr || expr === 'False' || expr === '0') {
    return false;
}
if (expr === 'True' || expr === '1') {
    return true;
}
```

**快速路径条件**:
- **假值**: 空字符串、"False"、"0" → `false`
- **真值**: "True"、"1" → `true`

#### 通用布尔转换
```javascript
return evaluateExpr(`bool(${expr})`, context);
```

**转换逻辑**:
- 将表达式包装在 `bool()` 函数中
- 使用 Python 的布尔转换语义
- 支持复杂表达式的布尔求值

#### 使用示例
```javascript
import { evaluateBooleanExpr } from "@web/core/py_js/py";

// 快速路径
console.log(evaluateBooleanExpr("True"));  // true
console.log(evaluateBooleanExpr("False")); // false
console.log(evaluateBooleanExpr("1"));     // true
console.log(evaluateBooleanExpr("0"));     // false
console.log(evaluateBooleanExpr(""));      // false

// 复杂表达式的布尔求值
const context = {
    user: { active: true, age: 25 },
    items: [1, 2, 3],
    config: { debug: false }
};

console.log(evaluateBooleanExpr("user.active", context)); // true
console.log(evaluateBooleanExpr("items", context)); // true (非空列表)
console.log(evaluateBooleanExpr("[]", context)); // false (空列表)
console.log(evaluateBooleanExpr("user.age > 18", context)); // true
console.log(evaluateBooleanExpr("user.active and not config.debug", context)); // true
```

## 实际使用示例

### 1. 表达式求值器类
```javascript
import { evaluateExpr, evaluateBooleanExpr, parseExpr, formatAST } from "@web/core/py_js/py";

class PythonExpressionEvaluator {
    constructor(baseContext = {}) {
        this.baseContext = {
            // 内置函数
            len: (obj) => obj.length || Object.keys(obj).length,
            sum: (arr) => arr.reduce((a, b) => a + b, 0),
            max: Math.max,
            min: Math.min,
            abs: Math.abs,
            round: Math.round,
            
            // 字符串函数
            str: String,
            int: parseInt,
            float: parseFloat,
            
            // 布尔函数
            bool: (val) => Boolean(val),
            
            ...baseContext
        };
    }
    
    evaluate(expression, context = {}) {
        const fullContext = { ...this.baseContext, ...context };
        return evaluateExpr(expression, fullContext);
    }
    
    evaluateBoolean(expression, context = {}) {
        const fullContext = { ...this.baseContext, ...context };
        return evaluateBooleanExpr(expression, fullContext);
    }
    
    parse(expression) {
        return parseExpr(expression);
    }
    
    format(expression) {
        const ast = parseExpr(expression);
        return formatAST(ast);
    }
    
    validate(expression) {
        try {
            parseExpr(expression);
            return { valid: true, error: null };
        } catch (error) {
            return { valid: false, error: error.message };
        }
    }
    
    analyzeExpression(expression) {
        try {
            const ast = parseExpr(expression);
            return {
                valid: true,
                ast: ast,
                formatted: formatAST(ast),
                variables: this.extractVariables(ast),
                functions: this.extractFunctions(ast)
            };
        } catch (error) {
            return {
                valid: false,
                error: error.message
            };
        }
    }
    
    extractVariables(ast, variables = new Set()) {
        if (!ast || typeof ast !== 'object') return variables;
        
        if (ast.type === 5 /* Name */) {
            variables.add(ast.value);
        }
        
        // 递归提取
        if (ast.left) this.extractVariables(ast.left, variables);
        if (ast.right) this.extractVariables(ast.right, variables);
        if (ast.args) ast.args.forEach(arg => this.extractVariables(arg, variables));
        if (ast.value && Array.isArray(ast.value)) {
            ast.value.forEach(item => this.extractVariables(item, variables));
        }
        
        return Array.from(variables);
    }
    
    extractFunctions(ast, functions = new Set()) {
        if (!ast || typeof ast !== 'object') return functions;
        
        if (ast.type === 8 /* FunctionCall */ && ast.fn.type === 5 /* Name */) {
            functions.add(ast.fn.value);
        }
        
        // 递归提取
        if (ast.left) this.extractFunctions(ast.left, functions);
        if (ast.right) this.extractFunctions(ast.right, functions);
        if (ast.args) ast.args.forEach(arg => this.extractFunctions(arg, functions));
        
        return Array.from(functions);
    }
}

// 使用示例
const evaluator = new PythonExpressionEvaluator();

// 基本求值
console.log(evaluator.evaluate("2 + 3 * 4")); // 14

// 带上下文求值
const context = { x: 10, y: [1, 2, 3, 4, 5] };
console.log(evaluator.evaluate("x + sum(y)", context)); // 25

// 布尔求值
console.log(evaluator.evaluateBoolean("x > 5 and len(y) > 3", context)); // true

// 表达式分析
const analysis = evaluator.analyzeExpression("func(x + y, z=2) if condition else default");
console.log(analysis);
// {
//   valid: true,
//   ast: {...},
//   formatted: "func(x + y, z = 2) if condition else default",
//   variables: ["x", "y", "z", "condition", "default"],
//   functions: ["func"]
// }
```

### 2. 条件表达式处理器
```javascript
class ConditionalExpressionProcessor {
    constructor() {
        this.evaluator = new PythonExpressionEvaluator();
    }
    
    processConditions(conditions, context) {
        const results = [];
        
        for (const condition of conditions) {
            try {
                const result = this.evaluator.evaluateBoolean(condition.expression, context);
                results.push({
                    expression: condition.expression,
                    result: result,
                    action: result ? condition.trueAction : condition.falseAction,
                    error: null
                });
            } catch (error) {
                results.push({
                    expression: condition.expression,
                    result: false,
                    action: condition.errorAction || condition.falseAction,
                    error: error.message
                });
            }
        }
        
        return results;
    }
    
    evaluateRules(rules, context) {
        for (const rule of rules) {
            try {
                if (this.evaluator.evaluateBoolean(rule.condition, context)) {
                    return {
                        matched: true,
                        rule: rule,
                        result: rule.action(context)
                    };
                }
            } catch (error) {
                console.warn(`Rule evaluation failed: ${rule.condition}`, error);
            }
        }
        
        return { matched: false, rule: null, result: null };
    }
    
    buildDynamicFilter(filterExpressions, context) {
        const validFilters = [];
        
        for (const expr of filterExpressions) {
            const validation = this.evaluator.validate(expr);
            if (validation.valid) {
                validFilters.push(expr);
            } else {
                console.warn(`Invalid filter expression: ${expr}`, validation.error);
            }
        }
        
        if (validFilters.length === 0) {
            return () => true; // 无有效过滤器，返回所有项
        }
        
        const combinedExpression = validFilters.join(" and ");
        
        return (item) => {
            try {
                return this.evaluator.evaluateBoolean(combinedExpression, { ...context, item });
            } catch (error) {
                console.warn(`Filter evaluation failed for item:`, item, error);
                return false;
            }
        };
    }
}

// 使用示例
const processor = new ConditionalExpressionProcessor();

// 处理条件列表
const conditions = [
    {
        expression: "user.age >= 18",
        trueAction: "allow_access",
        falseAction: "deny_access"
    },
    {
        expression: "user.role == 'admin'",
        trueAction: "full_access",
        falseAction: "limited_access"
    }
];

const userContext = { user: { age: 25, role: "user" } };
const results = processor.processConditions(conditions, userContext);
console.log(results);
// [
//   { expression: "user.age >= 18", result: true, action: "allow_access", error: null },
//   { expression: "user.role == 'admin'", result: false, action: "limited_access", error: null }
// ]

// 规则引擎
const rules = [
    {
        condition: "user.role == 'admin'",
        action: (ctx) => ({ access: "full", permissions: ["read", "write", "delete"] })
    },
    {
        condition: "user.role == 'editor'",
        action: (ctx) => ({ access: "limited", permissions: ["read", "write"] })
    },
    {
        condition: "user.role == 'viewer'",
        action: (ctx) => ({ access: "readonly", permissions: ["read"] })
    }
];

const ruleResult = processor.evaluateRules(rules, userContext);
console.log(ruleResult);
// { matched: false, rule: null, result: null } (因为用户角色是 "user")

// 动态过滤器
const filterExpressions = [
    "item.active == True",
    "item.price > 0",
    "len(item.tags) > 0"
];

const items = [
    { active: true, price: 100, tags: ["new"] },
    { active: false, price: 50, tags: [] },
    { active: true, price: 0, tags: ["sale"] },
    { active: true, price: 200, tags: ["featured", "new"] }
];

const filter = processor.buildDynamicFilter(filterExpressions, {});
const filteredItems = items.filter(filter);
console.log(filteredItems);
// [{ active: true, price: 100, tags: ["new"] }, { active: true, price: 200, tags: ["featured", "new"] }]
```

### 3. 表达式模板系统
```javascript
class ExpressionTemplateSystem {
    constructor() {
        this.evaluator = new PythonExpressionEvaluator();
        this.templates = new Map();
    }
    
    registerTemplate(name, template) {
        this.templates.set(name, template);
    }
    
    renderTemplate(name, context) {
        const template = this.templates.get(name);
        if (!template) {
            throw new Error(`Template not found: ${name}`);
        }
        
        return this.processTemplate(template, context);
    }
    
    processTemplate(template, context) {
        // 处理表达式插值：${expression}
        return template.replace(/\$\{([^}]+)\}/g, (match, expression) => {
            try {
                const result = this.evaluator.evaluate(expression, context);
                return String(result);
            } catch (error) {
                console.warn(`Template expression failed: ${expression}`, error);
                return match; // 保留原始表达式
            }
        });
    }
    
    processConditionalTemplate(template, context) {
        // 处理条件块：{% if condition %}...{% endif %}
        let result = template;
        
        // 处理 if 块
        result = result.replace(/\{% if ([^%]+) %\}(.*?)\{% endif %\}/gs, (match, condition, content) => {
            try {
                const isTrue = this.evaluator.evaluateBoolean(condition, context);
                return isTrue ? content : '';
            } catch (error) {
                console.warn(`Template condition failed: ${condition}`, error);
                return '';
            }
        });
        
        // 处理 if-else 块
        result = result.replace(/\{% if ([^%]+) %\}(.*?)\{% else %\}(.*?)\{% endif %\}/gs, 
            (match, condition, trueContent, falseContent) => {
                try {
                    const isTrue = this.evaluator.evaluateBoolean(condition, context);
                    return isTrue ? trueContent : falseContent;
                } catch (error) {
                    console.warn(`Template condition failed: ${condition}`, error);
                    return falseContent;
                }
            });
        
        // 处理表达式插值
        return this.processTemplate(result, context);
    }
    
    validateTemplate(template) {
        const expressions = [];
        const conditions = [];
        
        // 提取表达式
        template.replace(/\$\{([^}]+)\}/g, (match, expression) => {
            expressions.push(expression);
            return match;
        });
        
        // 提取条件
        template.replace(/\{% if ([^%]+) %\}/g, (match, condition) => {
            conditions.push(condition);
            return match;
        });
        
        const errors = [];
        
        // 验证表达式
        for (const expr of expressions) {
            const validation = this.evaluator.validate(expr);
            if (!validation.valid) {
                errors.push(`Invalid expression: ${expr} - ${validation.error}`);
            }
        }
        
        // 验证条件
        for (const condition of conditions) {
            const validation = this.evaluator.validate(condition);
            if (!validation.valid) {
                errors.push(`Invalid condition: ${condition} - ${validation.error}`);
            }
        }
        
        return {
            valid: errors.length === 0,
            errors: errors,
            expressions: expressions,
            conditions: conditions
        };
    }
}

// 使用示例
const templateSystem = new ExpressionTemplateSystem();

// 注册模板
templateSystem.registerTemplate('user_greeting', 
    'Hello ${user.name}, you have ${len(messages)} new messages.');

templateSystem.registerTemplate('conditional_message',
    `{% if user.is_admin %}
        Welcome, Administrator ${user.name}!
        You have full access to the system.
    {% else %}
        Welcome, ${user.name}!
        Your access level is: ${user.role}
    {% endif %}`);

// 渲染模板
const context = {
    user: { name: "Alice", is_admin: false, role: "editor" },
    messages: ["msg1", "msg2", "msg3"],
    len: (arr) => arr.length
};

const greeting = templateSystem.renderTemplate('user_greeting', context);
console.log(greeting);
// "Hello Alice, you have 3 new messages."

const conditionalMessage = templateSystem.renderTemplate('conditional_message', context);
console.log(conditionalMessage);
// "Welcome, Alice!\nYour access level is: editor"

// 模板验证
const validation = templateSystem.validateTemplate('${user.name} - ${invalid_function(x)}');
console.log(validation);
// {
//   valid: false,
//   errors: ["Invalid expression: invalid_function(x) - Unknown variable: invalid_function"],
//   expressions: ["user.name", "invalid_function(x)"],
//   conditions: []
// }
```

## 设计模式分析

### 1. 外观模式 (Facade Pattern)
```javascript
function evaluateExpr(expr, context = {}) {
    // 隐藏复杂的解析和求值过程
    const ast = parseExpr(expr);
    return evaluate(ast, context);
}
```

**优势**:
- **简化接口**: 提供简单的一步式 API
- **隐藏复杂性**: 隐藏内部的多步处理过程
- **统一入口**: 为整个 Python 系统提供统一入口

### 2. 模板方法模式 (Template Method Pattern)
```javascript
function evaluateBooleanExpr(expr, context = {}) {
    // 快速路径检查
    if (fastPathCheck(expr)) {
        return fastPathResult(expr);
    }
    // 通用处理
    return evaluateExpr(`bool(${expr})`, context);
}
```

### 3. 策略模式 (Strategy Pattern)
```javascript
// 不同类型的表达式使用不同的处理策略
const expressionStrategies = {
    boolean: evaluateBooleanExpr,
    general: evaluateExpr,
    parse: parseExpr
};
```

## 性能优化

### 1. 快速路径优化
```javascript
// 布尔表达式的快速路径
if (!expr || expr === 'False' || expr === '0') {
    return false; // 避免解析和求值
}
```

### 2. 错误信息优化
```javascript
// 提供详细但不冗余的错误信息
throw new EvalError(`Can not parse python expression: (${expr})\nError: ${error.message}`);
```

### 3. 上下文合并优化
```javascript
// 避免重复的上下文合并
const fullContext = { ...this.baseContext, ...context };
```

## 最佳实践

### 1. 错误处理
```javascript
// ✅ 推荐：分别处理解析和求值错误
try {
    ast = parseExpr(expr);
} catch (error) {
    throw new EvalError(`Parse error: ${error.message}`);
}
try {
    return evaluate(ast, context);
} catch (error) {
    throw new EvalError(`Evaluation error: ${error.message}`);
}

// ❌ 避免：模糊的错误处理
try {
    return evaluate(parseExpr(expr), context);
} catch (error) {
    throw new Error("Something went wrong");
}
```

### 2. 上下文管理
```javascript
// ✅ 推荐：提供默认上下文
const defaultContext = {
    len: (obj) => obj.length,
    bool: (val) => Boolean(val)
};
const fullContext = { ...defaultContext, ...userContext };

// ❌ 避免：空上下文
evaluateExpr(expr, {}); // 可能缺少必要的函数
```

### 3. 表达式验证
```javascript
// ✅ 推荐：在使用前验证表达式
function safeEvaluate(expr, context) {
    try {
        parseExpr(expr); // 验证语法
        return evaluateExpr(expr, context);
    } catch (error) {
        console.warn(`Invalid expression: ${expr}`, error);
        return null;
    }
}

// ❌ 避免：直接使用未验证的表达式
evaluateExpr(userInput, context); // 可能导致错误
```

## 总结

Python 主模块是 Odoo Web 客户端 Python-JavaScript 桥接系统的统一入口，它提供了：
- **简化的 API**: 一步式的表达式解析和求值接口
- **完整的流程**: 整合词法分析、语法分析和解释执行
- **专门的功能**: 针对布尔表达式的优化处理
- **错误处理**: 详细的错误信息和分类处理
- **统一入口**: 为外部模块提供一致的 Python 表达式处理接口

这个模块为 Odoo 的 Python 表达式系统提供了简洁、可靠的使用接口，大大简化了 Python 表达式在 JavaScript 环境中的使用。

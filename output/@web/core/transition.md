# @web/core/transition.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/transition.js`
- **原始路径**: `/web/static/src/core/transition.js`
- **代码行数**: 161行
- **作用**: 提供CSS过渡动画系统，支持元素的进入和离开动画效果

## 🎯 学习目标
通过学习这个文件，您将掌握：
- CSS过渡动画的实现原理和设计模式
- 状态机驱动的动画控制机制
- 组件生命周期与动画的协调
- 高阶组件和钩子的动画封装
- 企业级动画系统的架构设计

## 📚 核心概念

### 什么是Transition系统？
Transition系统是Odoo Web框架的**CSS动画引擎**，主要功能：
- **进入动画**: 元素显示时的过渡效果
- **离开动画**: 元素隐藏时的过渡效果
- **状态管理**: 动画状态的精确控制
- **性能优化**: 避免不必要的DOM操作和重绘

### 核心架构组成
```javascript
// 全局配置
const config = {
    disabled: false  // 全局禁用动画（测试模式）
};

// 动画状态机
const states = {
    "enter": "元素准备进入",
    "enter-active": "元素进入动画中", 
    "leave": "元素准备离开",
    "leave-active": "元素离开动画中"
};

// 核心API
useTransition(options)  // 钩子函数
Transition             // 高阶组件
```

### 基本使用模式
```javascript
// 在组件中使用钩子
class MyComponent extends Component {
    setup() {
        this.fadeTransition = useTransition({
            name: "fade",
            initialVisibility: false,
            leaveDuration: 300,
            onLeave: () => console.log("Element left")
        });
    }
    
    toggleVisibility() {
        this.fadeTransition.shouldMount = !this.fadeTransition.shouldMount;
    }
}

// 在模板中使用
static template = xml`
    <div>
        <button t-on-click="toggleVisibility">Toggle</button>
        <div t-if="fadeTransition.shouldMount" 
             t-att-class="fadeTransition.className">
            Animated Content
        </div>
    </div>
`;

// 对应的CSS
.fade {
    transition: opacity 0.3s ease;
}
.fade-enter {
    opacity: 0;
}
.fade-enter-active {
    opacity: 1;
}
.fade-leave {
    opacity: 1;
}
.fade-leave-active {
    opacity: 0;
}
```

## 🔍 核心实现详解

### 1. useTransition钩子函数

#### 参数配置和初始化
```javascript
function useTransition({
    name,                    // CSS类名前缀
    initialVisibility = true, // 初始可见性
    immediate = false,       // 是否立即动画
    leaveDuration = 500,     // 离开动画持续时间
    onLeave = () => {},      // 离开回调
}) {
    const component = useComponent();
    const state = useState({
        shouldMount: initialVisibility,
        stage: initialVisibility ? "enter" : "leave",
    });
}
```

**参数设计思想**：
- **name**: 提供CSS类名命名空间，避免冲突
- **initialVisibility**: 控制初始状态，支持预设动画
- **immediate**: 处理组件挂载时的动画需求
- **leaveDuration**: 精确控制离开动画时机
- **onLeave**: 提供动画完成后的清理机制

#### 禁用模式处理
```javascript
if (config.disabled) {
    return {
        get shouldMount() {
            return state.shouldMount;
        },
        set shouldMount(val) {
            state.shouldMount = val;
        },
        get className() {
            return `${name} ${name}-enter-active`;
        },
        get stage() {
            return "enter-active";
        },
    };
}
```

**禁用模式特点**：
- **测试友好**: 在测试环境中跳过动画
- **无障碍支持**: 为减少动画需求的用户提供选项
- **性能优化**: 在低性能设备上禁用动画
- **一致接口**: 保持API接口不变

#### 核心状态机实现
```javascript
const transition = {
    get shouldMount() {
        return state.shouldMount;
    },
    set shouldMount(newState) {
        if (newState === prevState) {
            return; // 避免重复状态变更
        }
        browser.clearTimeout(timer);
        prevState = newState;
        
        if (newState) {
            // 进入动画逻辑
            if (status(component) === "mounted" || immediate) {
                state.stage = "enter";
                component.render(); // 强制渲染以触发动画
                onNextPatch = () => {
                    state.stage = "enter-active";
                };
            } else {
                state.stage = "enter-active";
            }
            state.shouldMount = true;
        } else {
            // 离开动画逻辑
            state.stage = "leave";
            timer = browser.setTimeout(() => {
                state.shouldMount = false;
                onLeave();
            }, leaveDuration);
        }
    },
    get className() {
        return `${name} ${name}-${state.stage}`;
    },
    get stage() {
        return state.stage;
    },
};
```

**状态机设计**：
- **进入流程**: enter → enter-active
- **离开流程**: leave → (延迟) → unmount
- **时机控制**: 使用onNextPatch确保DOM更新时机
- **清理机制**: 自动清理定时器避免内存泄漏

#### onNextPatch机制
```javascript
let onNextPatch = null;
useEffect(() => {
    if (onNextPatch) {
        onNextPatch();
        onNextPatch = null;
    }
});
```

**时机控制原理**：
- **渲染同步**: 确保状态变更在下次渲染时生效
- **动画触发**: 保证CSS类变更能触发过渡动画
- **性能优化**: 避免不必要的DOM操作

### 2. Transition高阶组件

#### 组件定义和属性
```javascript
class Transition extends Component {
    static template = xml`
        <t t-slot="default" 
           t-if="transition.shouldMount" 
           className="transition.className"/>
    `;
    
    static props = {
        name: String,
        visible: { type: Boolean, optional: true },
        immediate: { type: Boolean, optional: true },
        leaveDuration: { type: Number, optional: true },
        onLeave: { type: Function, optional: true },
        slots: Object,
    };
}
```

#### 组件初始化和属性监听
```javascript
setup() {
    const { immediate, visible, leaveDuration, name, onLeave } = this.props;
    this.transition = useTransition({
        initialVisibility: visible,
        immediate,
        leaveDuration,
        name,
        onLeave,
    });
    
    onWillUpdateProps(({ visible = true }) => {
        this.transition.shouldMount = visible;
    });
}
```

**高阶组件优势**：
- **动态创建**: 支持在渲染时动态创建过渡
- **循环友好**: 适合在t-foreach中使用
- **属性驱动**: 通过props控制动画状态
- **插槽支持**: 灵活的内容插入机制

## 🎨 实际应用场景

### 1. 模态框动画系统
```javascript
class AnimatedModal extends Component {
    static template = xml`
        <div class="modal-container">
            <!-- 背景遮罩动画 -->
            <div t-if="backdropTransition.shouldMount"
                 t-att-class="backdropTransition.className"
                 class="modal-backdrop"
                 t-on-click="close"/>
            
            <!-- 模态框内容动画 -->
            <div t-if="modalTransition.shouldMount"
                 t-att-class="modalTransition.className"
                 class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 t-esc="props.title"/>
                        <button t-on-click="close" class="btn-close">×</button>
                    </div>
                    <div class="modal-body">
                        <t t-slot="default"/>
                    </div>
                    <div class="modal-footer" t-if="props.showFooter">
                        <button t-on-click="close" class="btn btn-secondary">Cancel</button>
                        <button t-on-click="confirm" class="btn btn-primary">Confirm</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    static props = {
        visible: Boolean,
        title: String,
        showFooter: { type: Boolean, optional: true },
        onConfirm: { type: Function, optional: true },
        onClose: { type: Function, optional: true },
        slots: Object
    };
    
    setup() {
        // 背景遮罩淡入淡出
        this.backdropTransition = useTransition({
            name: "modal-backdrop",
            initialVisibility: this.props.visible,
            leaveDuration: 150
        });
        
        // 模态框缩放动画
        this.modalTransition = useTransition({
            name: "modal-dialog",
            initialVisibility: this.props.visible,
            leaveDuration: 200,
            onLeave: () => {
                // 模态框动画完成后通知父组件
                this.props.onClose?.();
            }
        });
        
        onWillUpdateProps((nextProps) => {
            this.backdropTransition.shouldMount = nextProps.visible;
            this.modalTransition.shouldMount = nextProps.visible;
        });
        
        // 键盘事件处理
        useEffect(() => {
            const handleKeydown = (event) => {
                if (event.key === 'Escape' && this.props.visible) {
                    this.close();
                }
            };
            
            document.addEventListener('keydown', handleKeydown);
            return () => document.removeEventListener('keydown', handleKeydown);
        });
    }
    
    close() {
        this.backdropTransition.shouldMount = false;
        this.modalTransition.shouldMount = false;
    }
    
    confirm() {
        this.props.onConfirm?.();
        this.close();
    }
}

// 对应的CSS样式
const modalStyles = `
/* 背景遮罩动画 */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    transition: opacity 0.15s ease;
}

.modal-backdrop-enter {
    opacity: 0;
}

.modal-backdrop-enter-active {
    opacity: 1;
}

.modal-backdrop-leave {
    opacity: 1;
}

.modal-backdrop-leave-active {
    opacity: 0;
}

/* 模态框动画 */
.modal-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1001;
    transition: all 0.2s ease;
}

.modal-dialog-enter {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
}

.modal-dialog-enter-active {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

.modal-dialog-leave {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

.modal-dialog-leave-active {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
}
`;
```

### 2. 通知消息动画系统
```javascript
class NotificationManager extends Component {
    static template = xml`
        <div class="notification-container">
            <t t-foreach="notifications" t-as="notification" t-key="notification.id">
                <Transition name="notification-slide" 
                           t-props="{ visible: notification.visible, leaveDuration: 300 }">
                    <div class="notification" 
                         t-att-class="getNotificationClass(notification)">
                        <div class="notification-icon">
                            <i t-att-class="getIconClass(notification.type)"/>
                        </div>
                        <div class="notification-content">
                            <h5 t-if="notification.title" t-esc="notification.title"/>
                            <p t-esc="notification.message"/>
                        </div>
                        <button class="notification-close" 
                                t-on-click="() => this.removeNotification(notification.id)">
                            ×
                        </button>
                    </div>
                </Transition>
            </t>
        </div>
    `;
    
    static components = { Transition };
    
    setup() {
        this.notifications = useState([]);
        this.nextId = 1;
        
        // 监听全局通知事件
        this.notificationService = useService('notification');
        this.notificationService.bus.addEventListener('add', this.onNotificationAdd.bind(this));
    }
    
    onNotificationAdd(event) {
        const notification = {
            id: this.nextId++,
            ...event.detail,
            visible: true,
            createdAt: Date.now()
        };
        
        this.notifications.push(notification);
        
        // 自动移除通知
        if (notification.duration) {
            setTimeout(() => {
                this.removeNotification(notification.id);
            }, notification.duration);
        }
    }
    
    removeNotification(id) {
        const notification = this.notifications.find(n => n.id === id);
        if (notification) {
            notification.visible = false;
            
            // 动画完成后从数组中移除
            setTimeout(() => {
                const index = this.notifications.findIndex(n => n.id === id);
                if (index >= 0) {
                    this.notifications.splice(index, 1);
                }
            }, 300);
        }
    }
    
    getNotificationClass(notification) {
        return `notification-${notification.type || 'info'}`;
    }
    
    getIconClass(type) {
        const iconMap = {
            success: 'fa fa-check-circle',
            error: 'fa fa-exclamation-circle',
            warning: 'fa fa-exclamation-triangle',
            info: 'fa fa-info-circle'
        };
        return iconMap[type] || iconMap.info;
    }
}

// 通知服务
class NotificationService {
    constructor() {
        this.bus = new EventTarget();
    }
    
    add(message, type = 'info', options = {}) {
        this.bus.dispatchEvent(new CustomEvent('add', {
            detail: {
                message,
                type,
                title: options.title,
                duration: options.duration || 5000,
                sticky: options.sticky || false
            }
        }));
    }
    
    success(message, options) {
        this.add(message, 'success', options);
    }
    
    error(message, options) {
        this.add(message, 'error', { ...options, duration: 0 }); // 错误消息不自动消失
    }
    
    warning(message, options) {
        this.add(message, 'warning', options);
    }
    
    info(message, options) {
        this.add(message, 'info', options);
    }
}

// 对应的CSS样式
const notificationStyles = `
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 2000;
    max-width: 400px;
}

.notification {
    display: flex;
    align-items: flex-start;
    padding: 12px 16px;
    margin-bottom: 8px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: white;
    border-left: 4px solid;
}

.notification-info { border-left-color: #17a2b8; }
.notification-success { border-left-color: #28a745; }
.notification-warning { border-left-color: #ffc107; }
.notification-error { border-left-color: #dc3545; }

/* 滑入动画 */
.notification-slide {
    transition: all 0.3s ease;
}

.notification-slide-enter {
    opacity: 0;
    transform: translateX(100%);
}

.notification-slide-enter-active {
    opacity: 1;
    transform: translateX(0);
}

.notification-slide-leave {
    opacity: 1;
    transform: translateX(0);
}

.notification-slide-leave-active {
    opacity: 0;
    transform: translateX(100%);
}
`;
```

### 3. 列表项动画系统
```javascript
class AnimatedList extends Component {
    static template = xml`
        <div class="animated-list">
            <div class="list-header">
                <h3 t-esc="props.title"/>
                <button t-on-click="addItem" class="btn btn-primary">Add Item</button>
            </div>
            <div class="list-body">
                <t t-foreach="items" t-as="item" t-key="item.id">
                    <Transition name="list-item"
                               t-props="{
                                   visible: item.visible,
                                   leaveDuration: 250,
                                   onLeave: () => this.onItemLeave(item.id)
                               }">
                        <div class="list-item" t-att-data-id="item.id">
                            <div class="item-content">
                                <h5 t-esc="item.title"/>
                                <p t-esc="item.description"/>
                            </div>
                            <div class="item-actions">
                                <button t-on-click="() => this.editItem(item.id)"
                                        class="btn btn-sm btn-outline-primary">Edit</button>
                                <button t-on-click="() => this.removeItem(item.id)"
                                        class="btn btn-sm btn-outline-danger">Remove</button>
                            </div>
                        </div>
                    </Transition>
                </t>
            </div>
        </div>
    `;

    static components = { Transition };
    static props = {
        title: String,
        initialItems: { type: Array, optional: true },
        onItemAdd: { type: Function, optional: true },
        onItemEdit: { type: Function, optional: true },
        onItemRemove: { type: Function, optional: true }
    };

    setup() {
        this.items = useState(
            (this.props.initialItems || []).map(item => ({
                ...item,
                visible: true
            }))
        );
        this.nextId = Math.max(...this.items.map(i => i.id), 0) + 1;
    }

    addItem() {
        const newItem = {
            id: this.nextId++,
            title: `Item ${this.nextId - 1}`,
            description: `Description for item ${this.nextId - 1}`,
            visible: true
        };

        this.items.push(newItem);
        this.props.onItemAdd?.(newItem);
    }

    editItem(id) {
        const item = this.items.find(i => i.id === id);
        if (item) {
            this.props.onItemEdit?.(item);
        }
    }

    removeItem(id) {
        const item = this.items.find(i => i.id === id);
        if (item) {
            item.visible = false;
        }
    }

    onItemLeave(id) {
        const index = this.items.findIndex(i => i.id === id);
        if (index >= 0) {
            const removedItem = this.items.splice(index, 1)[0];
            this.props.onItemRemove?.(removedItem);
        }
    }
}

// 对应的CSS样式
const listStyles = `
.animated-list {
    max-width: 600px;
    margin: 0 auto;
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dee2e6;
}

.list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    margin-bottom: 10px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 列表项动画 */
.list-item {
    transition: all 0.25s ease;
}

.list-item-enter {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
}

.list-item-enter-active {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.list-item-leave {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.list-item-leave-active {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
}
`;
```

## 🛠️ 实践练习

### 练习1: 高级过渡管理器
```javascript
class AdvancedTransitionManager {
    constructor() {
        this.transitions = new Map();
        this.groups = new Map();
        this.globalConfig = {
            disabled: false,
            defaultDuration: 300,
            easing: 'ease'
        };
    }

    // 创建过渡组
    createGroup(name, options = {}) {
        const group = {
            name,
            transitions: new Set(),
            stagger: options.stagger || 0,
            direction: options.direction || 'normal', // 'normal' | 'reverse'
            onComplete: options.onComplete || (() => {})
        };

        this.groups.set(name, group);
        return group;
    }

    // 注册过渡到组
    registerToGroup(groupName, transitionId) {
        const group = this.groups.get(groupName);
        if (group) {
            group.transitions.add(transitionId);
        }
    }

    // 创建增强的过渡
    createTransition(id, options) {
        const transition = {
            id,
            ...options,
            state: 'idle',
            startTime: null,
            endTime: null,
            callbacks: {
                onStart: options.onStart || (() => {}),
                onEnd: options.onEnd || (() => {}),
                onCancel: options.onCancel || (() => {})
            }
        };

        this.transitions.set(id, transition);
        return this.createTransitionController(transition);
    }

    createTransitionController(transition) {
        return {
            start: () => this.startTransition(transition.id),
            stop: () => this.stopTransition(transition.id),
            pause: () => this.pauseTransition(transition.id),
            resume: () => this.resumeTransition(transition.id),
            reset: () => this.resetTransition(transition.id),
            getState: () => transition.state,
            getDuration: () => transition.endTime - transition.startTime
        };
    }

    // 启动过渡组
    startGroup(groupName, targetState = true) {
        const group = this.groups.get(groupName);
        if (!group) return;

        const transitions = Array.from(group.transitions);
        const staggerDelay = group.stagger;

        if (group.direction === 'reverse') {
            transitions.reverse();
        }

        transitions.forEach((transitionId, index) => {
            const delay = index * staggerDelay;
            setTimeout(() => {
                this.setTransitionState(transitionId, targetState);
            }, delay);
        });

        // 计算组完成时间
        const totalDuration = this.calculateGroupDuration(group);
        setTimeout(() => {
            group.onComplete();
        }, totalDuration);
    }

    calculateGroupDuration(group) {
        const transitions = Array.from(group.transitions);
        const maxDuration = Math.max(...transitions.map(id => {
            const transition = this.transitions.get(id);
            return transition ? transition.leaveDuration || this.globalConfig.defaultDuration : 0;
        }));

        return maxDuration + (transitions.length - 1) * group.stagger;
    }

    // 设置过渡状态
    setTransitionState(transitionId, state) {
        const transition = this.transitions.get(transitionId);
        if (transition && transition.controller) {
            transition.controller.shouldMount = state;
        }
    }

    // 批量控制
    batchControl(transitionIds, action, ...args) {
        transitionIds.forEach(id => {
            const transition = this.transitions.get(id);
            if (transition && transition.controller && transition.controller[action]) {
                transition.controller[action](...args);
            }
        });
    }

    // 性能监控
    getPerformanceMetrics() {
        const activeTransitions = Array.from(this.transitions.values())
            .filter(t => t.state !== 'idle');

        return {
            activeCount: activeTransitions.length,
            totalCount: this.transitions.size,
            groupCount: this.groups.size,
            averageDuration: this.calculateAverageDuration(),
            memoryUsage: this.estimateMemoryUsage()
        };
    }

    calculateAverageDuration() {
        const completedTransitions = Array.from(this.transitions.values())
            .filter(t => t.endTime && t.startTime);

        if (completedTransitions.length === 0) return 0;

        const totalDuration = completedTransitions.reduce((sum, t) =>
            sum + (t.endTime - t.startTime), 0);

        return totalDuration / completedTransitions.length;
    }

    estimateMemoryUsage() {
        // 简化的内存使用估算
        const transitionSize = JSON.stringify(Array.from(this.transitions.values())).length;
        const groupSize = JSON.stringify(Array.from(this.groups.values())).length;
        return transitionSize + groupSize;
    }

    // 清理过期过渡
    cleanup() {
        const now = Date.now();
        const maxAge = 5 * 60 * 1000; // 5分钟

        for (const [id, transition] of this.transitions.entries()) {
            if (transition.endTime && (now - transition.endTime) > maxAge) {
                this.transitions.delete(id);
            }
        }
    }

    // 导出配置
    exportConfig() {
        return {
            globalConfig: this.globalConfig,
            transitions: Array.from(this.transitions.entries()),
            groups: Array.from(this.groups.entries()),
            metrics: this.getPerformanceMetrics()
        };
    }
}

// 使用示例
const transitionManager = new AdvancedTransitionManager();

// 创建过渡组
const menuGroup = transitionManager.createGroup('main_menu', {
    stagger: 50,
    direction: 'normal',
    onComplete: () => console.log('Menu animation complete')
});

// 在组件中使用
class EnhancedMenu extends Component {
    setup() {
        this.menuItems = [
            { id: 'home', label: 'Home' },
            { id: 'about', label: 'About' },
            { id: 'services', label: 'Services' },
            { id: 'contact', label: 'Contact' }
        ];

        // 为每个菜单项创建过渡
        this.itemTransitions = this.menuItems.map(item => {
            const transition = useTransition({
                name: 'menu-item',
                initialVisibility: false,
                leaveDuration: 200
            });

            // 注册到管理器
            transitionManager.registerToGroup('main_menu', item.id);

            return { ...item, transition };
        });
    }

    showMenu() {
        transitionManager.startGroup('main_menu', true);
    }

    hideMenu() {
        transitionManager.startGroup('main_menu', false);
    }
}
```

## 🔧 调试技巧

### 查看过渡状态
```javascript
function debugTransition(transition) {
    console.group('Transition Debug');
    console.log('Should Mount:', transition.shouldMount);
    console.log('Class Name:', transition.className);
    console.log('Stage:', transition.stage);
    console.groupEnd();
}

// 在组件中使用
setup() {
    this.transition = useTransition({ name: 'fade' });

    // 调试过渡状态变化
    useEffect(() => {
        debugTransition(this.transition);
    });
}
```

### 过渡时机可视化
```javascript
function visualizeTransitionTiming(transitionName) {
    const style = document.createElement('style');
    style.textContent = `
        .${transitionName}-enter,
        .${transitionName}-enter-active,
        .${transitionName}-leave,
        .${transitionName}-leave-active {
            position: relative;
        }

        .${transitionName}-enter::before,
        .${transitionName}-enter-active::before,
        .${transitionName}-leave::before,
        .${transitionName}-leave-active::before {
            content: attr(class);
            position: absolute;
            top: -20px;
            left: 0;
            font-size: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 2px 4px;
            border-radius: 2px;
            z-index: 10000;
        }
    `;
    document.head.appendChild(style);
}

// 可视化特定过渡
// visualizeTransitionTiming('modal-dialog');
```

## 📊 性能考虑

### 优化策略
1. **CSS优化**: 使用transform和opacity而非layout属性
2. **时机控制**: 精确控制动画的开始和结束时机
3. **内存管理**: 及时清理定时器和事件监听器
4. **批量操作**: 避免频繁的状态变更

### 最佳实践
```javascript
// ✅ 好的做法：使用transform和opacity
.fade-enter-active {
    transition: opacity 0.3s ease;
}

// ❌ 不好的做法：使用layout属性
.slide-enter-active {
    transition: height 0.3s ease; /* 会触发重排 */
}

// ✅ 好的做法：合理的动画时长
useTransition({
    name: 'quick-fade',
    leaveDuration: 200 // 快速响应
});

// ❌ 不好的做法：过长的动画
useTransition({
    name: 'slow-fade',
    leaveDuration: 2000 // 用户体验差
});
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解CSS过渡动画的实现原理和设计模式
- [ ] 掌握状态机驱动的动画控制机制
- [ ] 理解组件生命周期与动画的协调
- [ ] 能够创建复杂的动画系统和管理器
- [ ] 掌握动画性能优化和调试技巧
- [ ] 了解企业级动画系统的架构设计

## 🚀 下一步学习
学完Transition系统后，建议继续学习：
1. **UI工具** (`@web/core/utils/ui.js`) - 学习UI操作工具
2. **组件系统** (`@odoo/owl`) - 深入理解组件生命周期
3. **浏览器API** (`@web/core/browser/`) - 掌握浏览器集成

## 💡 重要提示
- Transition系统是现代Web应用用户体验的重要组成部分
- 理解状态机对构建复杂动画很重要
- 性能优化对动画系统至关重要
- 合理的动画时长和缓动函数能显著提升用户体验
```

# ExpressionEditorDialog - 表达式编辑器对话框

## 概述

`expression_editor_dialog.js` 是 Odoo Web 核心模块的表达式编辑器对话框组件，提供了模态对话框形式的表达式编辑功能。该组件集成了表达式编辑器和对话框，支持表达式验证、默认记录生成、用户上下文集成等功能，具备确认取消操作、错误提示、按钮状态管理等特性，为用户提供了完整的表达式编辑体验，广泛应用于计算字段、过滤条件、业务规则等需要表达式配置的场景。

## 文件信息
- **路径**: `/web/static/src/core/expression_editor_dialog/expression_editor_dialog.js`
- **行数**: 87
- **模块**: `@web/core/expression_editor_dialog/expression_editor_dialog`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                      // OWL框架
'@web/core/dialog/dialog'                        // 对话框组件
'@web/core/expression_editor/expression_editor'  // 表达式编辑器
'@web/core/py_js/py'                            // Python表达式评估
'@web/core/utils/hooks'                         // 工具钩子
'@web/core/l10n/translation'                    // 国际化
'@web/core/user'                                // 用户服务
```

## 主组件类

### 1. ExpressionEditorDialog 组件

```javascript
class ExpressionEditorDialog extends Component {
    static components = { Dialog, ExpressionEditor };
    static template = "web.ExpressionEditorDialog";
    static props = {
        close: Function,         // 关闭对话框回调
        resModel: String,        // 资源模型
        fields: Object,          // 字段定义
        expression: String,      // 初始表达式
        onConfirm: Function,     // 确认回调
    };
}
```

**组件功能**:
- **对话框集成**: 基于Dialog组件提供模态对话框
- **表达式编辑**: 集成ExpressionEditor提供编辑功能
- **验证机制**: 内置表达式验证和错误处理
- **用户体验**: 完整的确认取消流程

**属性说明**:
- **close**: 关闭对话框的回调函数
- **resModel**: 目标数据模型名称
- **fields**: 可用字段的定义对象
- **expression**: 初始的表达式字符串
- **onConfirm**: 确认表达式时的回调函数

### 2. 组件初始化

```javascript
setup() {
    this.notification = useService("notification");
    this.state = useState({
        expression: this.props.expression,
    });
    this.confirmButtonRef = useRef("confirm");
}
```

**初始化功能**:
- **通知服务**: 获取通知服务用于错误提示
- **状态管理**: 管理当前编辑的表达式
- **按钮引用**: 获取确认按钮的引用用于状态控制

## 核心属性

### 1. 表达式编辑器属性

```javascript
get expressionEditorProps() {
    return {
        resModel: this.props.resModel,
        fields: this.props.fields,
        expression: this.state.expression,
        update: (expression) => {
            this.state.expression = expression;
        },
    };
}
```

**属性功能**:
- **属性传递**: 将对话框属性传递给表达式编辑器
- **状态同步**: 同步表达式编辑器的状态变化
- **更新回调**: 处理表达式的实时更新

## 核心方法

### 1. 默认记录生成

```javascript
makeDefaultRecord() {
    const record = {};
    for (const [name, { type }] of Object.entries(this.props.fields)) {
        switch (type) {
            case "integer":
            case "float":
            case "monetary":
                record[name] = name === "id" ? false : 0;
                break;
            case "one2many":
            case "many2many":
                record[name] = [];
                break;
            default:
                record[name] = false;
        }
    }
    return record;
}
```

**记录生成功能**:
- **类型映射**: 根据字段类型生成默认值
- **数值字段**: 数值类型字段默认为0，id字段为false
- **关联字段**: 一对多和多对多字段默认为空数组
- **其他字段**: 其他类型字段默认为false
- **验证准备**: 为表达式验证提供测试数据

### 2. 确认操作

```javascript
async onConfirm() {
    this.confirmButtonRef.el.disabled = true;
    const record = this.makeDefaultRecord();
    const evalContext = { ...user.context, ...record };
    try {
        evaluateExpr(this.state.expression, evalContext);
    } catch {
        if (this.confirmButtonRef.el) {
            this.confirmButtonRef.el.disabled = false;
        }
        this.notification.add(_t("Expression is invalid. Please correct it"), {
            type: "danger",
        });
        return;
    }
    this.props.onConfirm(this.state.expression);
    this.props.close();
}
```

**确认功能**:
- **按钮禁用**: 防止重复提交
- **上下文构建**: 合并用户上下文和默认记录
- **表达式验证**: 使用evaluateExpr验证表达式
- **错误处理**: 验证失败时显示错误通知
- **成功处理**: 验证通过时调用确认回调并关闭对话框

### 3. 取消操作

```javascript
onDiscard() {
    this.props.close();
}
```

**取消功能**:
- **直接关闭**: 不保存更改直接关闭对话框
- **状态重置**: 放弃当前的编辑状态

## 使用场景

### 1. 计算字段配置

```javascript
// 计算字段配置示例
class ComputedFieldConfigurator extends Component {
    setup() {
        this.dialog = useService("dialog");
        this.state = useState({
            fields: [
                {
                    id: 1,
                    name: 'total_amount',
                    label: '总金额',
                    expression: 'price * quantity',
                    model: 'sale.order.line'
                },
                {
                    id: 2,
                    name: 'discount_amount',
                    label: '折扣金额',
                    expression: 'price * quantity * discount / 100',
                    model: 'sale.order.line'
                }
            ],
            currentField: null,
            availableFields: {
                price: { name: 'price', string: '单价', type: 'float' },
                quantity: { name: 'quantity', string: '数量', type: 'float' },
                discount: { name: 'discount', string: '折扣', type: 'float' },
                product_id: { name: 'product_id', string: '产品', type: 'many2one' },
                order_id: { name: 'order_id', string: '订单', type: 'many2one' }
            }
        });
    }

    editFieldExpression(field) {
        this.state.currentField = field;
        this.dialog.add(ExpressionEditorDialog, {
            resModel: field.model,
            fields: this.state.availableFields,
            expression: field.expression,
            onConfirm: (newExpression) => {
                this.onExpressionConfirmed(field.id, newExpression);
            }
        });
    }

    onExpressionConfirmed(fieldId, newExpression) {
        const field = this.state.fields.find(f => f.id === fieldId);
        if (field) {
            field.expression = newExpression;
            console.log(`Field ${field.name} expression updated:`, newExpression);
            this.notification.add(`字段 "${field.label}" 的表达式已更新`, { type: 'success' });
        }
    }

    addComputedField() {
        const newField = {
            id: Date.now(),
            name: 'new_field',
            label: '新计算字段',
            expression: 'True',
            model: 'sale.order.line'
        };
        
        this.state.fields.push(newField);
        this.editFieldExpression(newField);
    }

    deleteField(fieldId) {
        if (!confirm('确定要删除这个计算字段吗？')) return;
        
        this.state.fields = this.state.fields.filter(f => f.id !== fieldId);
    }

    testExpression(field) {
        // 模拟表达式测试
        console.log(`Testing expression for ${field.name}:`, field.expression);
        
        try {
            // 这里可以添加实际的表达式测试逻辑
            this.notification.add(`字段 "${field.label}" 表达式测试通过`, { type: 'success' });
        } catch (error) {
            this.notification.add(`字段 "${field.label}" 表达式测试失败: ${error.message}`, { type: 'danger' });
        }
    }

    render() {
        return xml`
            <div class="computed-field-configurator">
                <div class="header mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>计算字段配置器</h5>
                        <button 
                            class="btn btn-primary"
                            t-on-click="addComputedField"
                        >
                            <i class="fa fa-plus"/> 添加计算字段
                        </button>
                    </div>
                </div>

                <div class="fields-table">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>字段名</th>
                                    <th>显示标签</th>
                                    <th>表达式</th>
                                    <th>模型</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-foreach="state.fields" t-as="field" t-key="field.id">
                                    <tr>
                                        <td><code t-esc="field.name"/></td>
                                        <td t-esc="field.label"/>
                                        <td>
                                            <code class="text-truncate d-inline-block" style="max-width: 200px;" t-esc="field.expression"/>
                                        </td>
                                        <td><small t-esc="field.model"/></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button 
                                                    class="btn btn-outline-primary"
                                                    t-on-click="() => this.editFieldExpression(field)"
                                                    title="编辑表达式"
                                                >
                                                    <i class="fa fa-edit"/>
                                                </button>
                                                <button 
                                                    class="btn btn-outline-info"
                                                    t-on-click="() => this.testExpression(field)"
                                                    title="测试表达式"
                                                >
                                                    <i class="fa fa-play"/>
                                                </button>
                                                <button 
                                                    class="btn btn-outline-danger"
                                                    t-on-click="() => this.deleteField(field.id)"
                                                    title="删除字段"
                                                >
                                                    <i class="fa fa-trash"/>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </t>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="available-fields mt-4">
                    <div class="card">
                        <div class="card-header">
                            <h6>可用字段</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <t t-foreach="Object.values(state.availableFields)" t-as="field" t-key="field.name">
                                    <div class="col-md-3 mb-2">
                                        <div class="field-info">
                                            <strong t-esc="field.string"/>
                                            <br/>
                                            <small class="text-muted">
                                                <code t-esc="field.name"/> (<t t-esc="field.type"/>)
                                            </small>
                                        </div>
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="instructions mt-3">
                    <div class="alert alert-info">
                        <h6>使用说明</h6>
                        <ul class="mb-0">
                            <li>点击"编辑表达式"按钮打开表达式编辑器对话框</li>
                            <li>在对话框中可以可视化编辑Python表达式</li>
                            <li>表达式会在确认前自动验证</li>
                            <li>可以使用上方显示的可用字段构建表达式</li>
                        </ul>
                    </div>
                </div>
            </div>
        `;
    }
}
```

### 2. 业务规则配置器

```javascript
// 业务规则配置器
class BusinessRuleConfigurator extends Component {
    setup() {
        this.dialog = useService("dialog");
        this.state = useState({
            rules: [
                {
                    id: 1,
                    name: '销售折扣规则',
                    condition: 'quantity >= 10',
                    action: 'discount = 0.1',
                    model: 'sale.order.line',
                    active: true
                },
                {
                    id: 2,
                    name: '库存警告规则',
                    condition: 'product_id.qty_available < 5',
                    action: 'warning = "库存不足"',
                    model: 'sale.order.line',
                    active: true
                }
            ],
            currentRule: null,
            editingType: null, // 'condition' or 'action'
            modelFields: {
                quantity: { name: 'quantity', string: '数量', type: 'float' },
                discount: { name: 'discount', string: '折扣', type: 'float' },
                product_id: { name: 'product_id', string: '产品', type: 'many2one' },
                warning: { name: 'warning', string: '警告', type: 'char' }
            }
        });
    }

    editRuleCondition(rule) {
        this.state.currentRule = rule;
        this.state.editingType = 'condition';
        
        this.dialog.add(ExpressionEditorDialog, {
            resModel: rule.model,
            fields: this.state.modelFields,
            expression: rule.condition,
            onConfirm: (newExpression) => {
                rule.condition = newExpression;
                this.onRuleUpdated(rule, 'condition');
            }
        });
    }

    editRuleAction(rule) {
        this.state.currentRule = rule;
        this.state.editingType = 'action';
        
        this.dialog.add(ExpressionEditorDialog, {
            resModel: rule.model,
            fields: this.state.modelFields,
            expression: rule.action,
            onConfirm: (newExpression) => {
                rule.action = newExpression;
                this.onRuleUpdated(rule, 'action');
            }
        });
    }

    onRuleUpdated(rule, type) {
        console.log(`Rule ${rule.name} ${type} updated:`, rule[type]);
        this.notification.add(`规则 "${rule.name}" 的${type === 'condition' ? '条件' : '动作'}已更新`, { type: 'success' });
    }

    toggleRuleActive(rule) {
        rule.active = !rule.active;
        this.notification.add(`规则 "${rule.name}" 已${rule.active ? '启用' : '禁用'}`, { 
            type: rule.active ? 'success' : 'warning' 
        });
    }

    addRule() {
        const newRule = {
            id: Date.now(),
            name: '新业务规则',
            condition: 'True',
            action: 'pass',
            model: 'sale.order.line',
            active: true
        };
        
        this.state.rules.push(newRule);
        this.editRuleCondition(newRule);
    }

    deleteRule(ruleId) {
        if (!confirm('确定要删除这个业务规则吗？')) return;
        
        this.state.rules = this.state.rules.filter(r => r.id !== ruleId);
    }

    testRule(rule) {
        console.log(`Testing rule: ${rule.name}`);
        console.log(`Condition: ${rule.condition}`);
        console.log(`Action: ${rule.action}`);
        
        // 模拟规则测试
        this.notification.add(`规则 "${rule.name}" 测试完成`, { type: 'info' });
    }

    render() {
        return xml`
            <div class="business-rule-configurator">
                <div class="header mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>业务规则配置器</h5>
                        <button 
                            class="btn btn-primary"
                            t-on-click="addRule"
                        >
                            <i class="fa fa-plus"/> 添加规则
                        </button>
                    </div>
                </div>

                <div class="rules-list">
                    <t t-foreach="state.rules" t-as="rule" t-key="rule.id">
                        <div class="card mb-3">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <h6 class="mb-0 me-3" t-esc="rule.name"/>
                                        <span t-att-class="'badge ' + (rule.active ? 'bg-success' : 'bg-secondary')">
                                            <t t-esc="rule.active ? '启用' : '禁用'"/>
                                        </span>
                                    </div>
                                    <div class="rule-actions">
                                        <div class="btn-group btn-group-sm">
                                            <button 
                                                class="btn btn-outline-info"
                                                t-on-click="() => this.testRule(rule)"
                                                title="测试规则"
                                            >
                                                <i class="fa fa-play"/>
                                            </button>
                                            <button 
                                                class="btn"
                                                t-att-class="rule.active ? 'btn-outline-warning' : 'btn-outline-success'"
                                                t-on-click="() => this.toggleRuleActive(rule)"
                                                t-att-title="rule.active ? '禁用规则' : '启用规则'"
                                            >
                                                <i t-att-class="rule.active ? 'fa fa-pause' : 'fa fa-play'"/>
                                            </button>
                                            <button 
                                                class="btn btn-outline-danger"
                                                t-on-click="() => this.deleteRule(rule.id)"
                                                title="删除规则"
                                            >
                                                <i class="fa fa-trash"/>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="rule-section">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <h6>条件</h6>
                                                <button 
                                                    class="btn btn-sm btn-outline-primary"
                                                    t-on-click="() => this.editRuleCondition(rule)"
                                                >
                                                    <i class="fa fa-edit"/> 编辑
                                                </button>
                                            </div>
                                            <div class="expression-display">
                                                <code t-esc="rule.condition"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="rule-section">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <h6>动作</h6>
                                                <button 
                                                    class="btn btn-sm btn-outline-primary"
                                                    t-on-click="() => this.editRuleAction(rule)"
                                                >
                                                    <i class="fa fa-edit"/> 编辑
                                                </button>
                                            </div>
                                            <div class="expression-display">
                                                <code t-esc="rule.action"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="rule-info mt-3">
                                    <small class="text-muted">
                                        模型: <code t-esc="rule.model"/>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </t>
                </div>

                <div class="empty-state text-center py-5" t-if="!state.rules.length">
                    <i class="fa fa-cogs fa-3x text-muted mb-3"/>
                    <h6 class="text-muted">暂无业务规则</h6>
                    <p class="text-muted">点击"添加规则"按钮创建第一个业务规则</p>
                </div>

                <div class="rule-stats mt-4" t-if="state.rules.length">
                    <div class="card">
                        <div class="card-header">
                            <h6>规则统计</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <h6>总规则数</h6>
                                    <p class="h4 text-primary" t-esc="state.rules.length"/>
                                </div>
                                <div class="col-md-4">
                                    <h6>启用规则</h6>
                                    <p class="h4 text-success" t-esc="state.rules.filter(r => r.active).length"/>
                                </div>
                                <div class="col-md-4">
                                    <h6>禁用规则</h6>
                                    <p class="h4 text-warning" t-esc="state.rules.filter(r => !r.active).length"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 表达式验证
- 实时表达式验证
- 用户上下文集成
- 错误提示和处理

### 2. 对话框集成
- 模态对话框形式
- 完整的确认取消流程
- 按钮状态管理

### 3. 默认数据生成
- 智能的默认值生成
- 类型感知的数据创建
- 验证环境准备

### 4. 用户体验
- 清晰的操作反馈
- 防重复提交保护
- 友好的错误提示

## 设计模式

### 1. 对话框模式 (Dialog Pattern)
- 模态对话框的标准实现
- 用户交互的隔离处理

### 2. 验证器模式 (Validator Pattern)
- 表达式的验证机制
- 错误处理的统一管理

### 3. 适配器模式 (Adapter Pattern)
- 表达式编辑器的对话框适配
- 不同组件间的接口适配

## 注意事项

1. **表达式安全**: 确保表达式验证的安全性
2. **用户体验**: 提供清晰的操作指引和错误反馈
3. **性能考虑**: 避免频繁的表达式验证
4. **状态管理**: 正确管理对话框和编辑器的状态

## 扩展建议

1. **表达式模板**: 提供常用表达式的模板库
2. **语法高亮**: 表达式的语法高亮显示
3. **自动完成**: 字段和函数的自动完成
4. **历史记录**: 表达式编辑的历史记录
5. **批量编辑**: 支持批量编辑多个表达式

该表达式编辑器对话框为Odoo Web应用提供了完整的表达式编辑体验，通过对话框形式和验证机制确保了表达式的正确性和用户操作的便捷性。

# @web/core/ensure_jquery.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/ensure_jquery.js`
- **原始路径**: `/web/static/src/core/ensure_jquery.js`
- **代码行数**: 29行
- **作用**: 确保jQuery库的可用性，并集成Bootstrap组件的jQuery接口

## 🎯 学习目标
通过学习这个文件，您将掌握：
- jQuery按需加载的实现策略
- Bootstrap与jQuery集成的技术细节
- 第三方库兼容性处理的最佳实践
- 动态库加载和接口桥接的设计模式
- 现代前端框架中遗留库的集成方案

## 📚 核心概念

### 什么是ensureJQuery？
ensureJQuery是一个**库依赖管理工具**，主要功能：
- **按需加载**: 只在需要时加载jQuery库
- **兼容性桥接**: 为Bootstrap组件提供jQuery接口
- **全局状态检查**: 检查jQuery是否已经加载
- **接口统一**: 确保jQuery插件的标准化接口

### 为什么需要ensureJQuery？
在现代前端开发中，jQuery不再是必需的依赖，但某些场景仍需要：
- **遗留代码**: 现有的jQuery代码需要继续工作
- **第三方插件**: 某些插件仍然依赖jQuery
- **Bootstrap集成**: Bootstrap组件的jQuery接口
- **渐进迁移**: 从jQuery逐步迁移到现代框架

### 基本使用模式
```javascript
// 导入ensureJQuery
const { ensureJQuery } = require("@web/core/ensure_jquery");

// 在需要jQuery的地方调用
async function useJQuery() {
    await ensureJQuery();

    // 现在可以安全使用jQuery
    const $ = window.jQuery;
    $(".my-element").addClass("active");

    // 也可以使用Bootstrap组件
    $(".dropdown").dropdown();
    $(".modal").modal("show");
}

// 在组件中使用
class LegacyComponent extends Component {
    async setup() {
        await ensureJQuery();
        this.$ = window.jQuery;
    }

    mounted() {
        // 使用jQuery操作DOM
        this.$(".legacy-widget").somePlugin();
    }
}
```

## 🔍 核心实现详解

### 1. jQuery可用性检查
```javascript
if (!window.jQuery) {
    await loadBundle("web._assets_jquery");
    // ... Bootstrap集成代码
}
```

**检查策略**：
- **全局检查**: 检查`window.jQuery`是否存在
- **避免重复**: 如果已存在则跳过加载
- **异步加载**: 使用`loadBundle`动态加载jQuery资源包

### 2. Bootstrap组件集成
```javascript
const BTS_CLASSES = ["Carousel", "Dropdown", "Modal", "Popover", "Tooltip", "Collapse"];
const $ = window.jQuery;

for (const CLS of BTS_CLASSES) {
    const plugin = window[CLS];
    if (plugin) {
        const name = plugin.NAME;
        const JQUERY_NO_CONFLICT = $.fn[name];
        $.fn[name] = plugin.jQueryInterface;
        $.fn[name].Constructor = plugin;
        $.fn[name].noConflict = () => {
            $.fn[name] = JQUERY_NO_CONFLICT;
            return plugin.jQueryInterface;
        };
    }
}
```

**集成流程分析**：

#### 步骤1: 定义Bootstrap组件列表
```javascript
const BTS_CLASSES = ["Carousel", "Dropdown", "Modal", "Popover", "Tooltip", "Collapse"];
```
这些是需要jQuery接口的Bootstrap组件。

#### 步骤2: 遍历并集成每个组件
```javascript
for (const CLS of BTS_CLASSES) {
    const plugin = window[CLS]; // 获取全局Bootstrap类
    if (plugin) {
        // 进行jQuery集成
    }
}
```

#### 步骤3: 创建jQuery插件接口
```javascript
const name = plugin.NAME;                    // 获取插件名称（如"dropdown"）
const JQUERY_NO_CONFLICT = $.fn[name];      // 保存现有的jQuery方法
$.fn[name] = plugin.jQueryInterface;        // 设置新的jQuery接口
$.fn[name].Constructor = plugin;             // 设置构造函数引用
```

#### 步骤4: 实现noConflict方法
```javascript
$.fn[name].noConflict = () => {
    $.fn[name] = JQUERY_NO_CONFLICT;         // 恢复原有方法
    return plugin.jQueryInterface;           // 返回Bootstrap接口
};
```

### 3. jQuery接口的工作原理
```javascript
// Bootstrap组件通常有这样的结构
class Dropdown {
    static NAME = 'dropdown';

    static jQueryInterface(config) {
        return this.each(function() {
            // 在每个匹配的元素上操作
            const data = Dropdown.getOrCreateInstance(this, config);

            if (typeof config === 'string') {
                data[config](); // 调用方法
            }
        });
    }
}

// 集成后可以这样使用
$('.dropdown-toggle').dropdown('toggle');
$('.dropdown-toggle').dropdown({ boundary: 'viewport' });
```

## 🎨 实际应用场景

### 1. 在遗留组件中使用jQuery
```javascript
class LegacyFormWidget extends Component {
    static template = xml`
        <div class="legacy-widget" t-ref="widget">
            <input type="text" class="form-control"/>
            <div class="dropdown">
                <button class="btn dropdown-toggle" data-bs-toggle="dropdown">
                    Options
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#">Action</a></li>
                </ul>
            </div>
        </div>
    `;

    async setup() {
        // 确保jQuery可用
        await ensureJQuery();
        this.$ = window.jQuery;
    }

    mounted() {
        // 使用jQuery初始化遗留插件
        this.$(this.refs.widget).find('.form-control').someOldPlugin({
            validation: true,
            format: 'currency'
        });

        // 初始化Bootstrap组件
        this.$(this.refs.widget).find('.dropdown').dropdown({
            boundary: 'viewport'
        });
    }

    willUnmount() {
        // 清理jQuery插件
        this.$(this.refs.widget).find('.form-control').someOldPlugin('destroy');
    }
}
```

### 2. 在服务中按需使用jQuery
```javascript
class LegacyIntegrationService {
    dependencies = [];

    async start() {
        this.jqueryReady = false;
    }

    async ensureJQueryReady() {
        if (!this.jqueryReady) {
            await ensureJQuery();
            this.$ = window.jQuery;
            this.jqueryReady = true;
        }
    }

    async initializeLegacyWidget(element, config) {
        await this.ensureJQueryReady();

        // 使用jQuery初始化遗留组件
        return this.$(element).legacyWidget(config);
    }

    async showBootstrapModal(selector, options = {}) {
        await this.ensureJQueryReady();

        // 使用Bootstrap模态框
        this.$(selector).modal(options);
    }

    async createDatePicker(selector, options = {}) {
        await this.ensureJQueryReady();

        // 初始化日期选择器（假设是jQuery插件）
        return this.$(selector).datepicker(options);
    }
}

// 注册服务
registry.category("services").add("legacy_integration", {
    dependencies: [],
    start() {
        return new LegacyIntegrationService();
    }
});
```

### 3. 在动作中使用jQuery
```javascript
class LegacyReportAction extends Component {
    static template = xml`
        <div class="legacy-report">
            <div class="report-controls">
                <button class="btn btn-primary" t-on-click="generateReport">
                    Generate Report
                </button>
                <div class="modal fade" t-ref="progressModal">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-body">
                                <div class="progress">
                                    <div class="progress-bar" t-ref="progressBar"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="report-content" t-ref="reportContent"></div>
        </div>
    `;

    async setup() {
        await ensureJQuery();
        this.$ = window.jQuery;
    }

    async generateReport() {
        // 显示进度模态框
        this.$(this.refs.progressModal).modal('show');

        try {
            // 模拟报表生成过程
            for (let i = 0; i <= 100; i += 10) {
                await new Promise(resolve => setTimeout(resolve, 200));
                this.updateProgress(i);
            }

            // 生成报表内容
            await this.renderReport();

        } finally {
            // 隐藏进度模态框
            this.$(this.refs.progressModal).modal('hide');
        }
    }

    updateProgress(percentage) {
        this.$(this.refs.progressBar).css('width', percentage + '%');
    }

    async renderReport() {
        // 使用jQuery渲染复杂的报表
        const reportData = await this.fetchReportData();

        this.$(this.refs.reportContent).empty();

        // 使用jQuery构建复杂的DOM结构
        const $table = this.$('<table class="table table-striped">');
        const $thead = this.$('<thead>').appendTo($table);
        const $tbody = this.$('<tbody>').appendTo($table);

        // 构建表头
        const $headerRow = this.$('<tr>').appendTo($thead);
        reportData.columns.forEach(col => {
            this.$('<th>').text(col.label).appendTo($headerRow);
        });

        // 构建数据行
        reportData.rows.forEach(row => {
            const $row = this.$('<tr>').appendTo($tbody);
            row.forEach(cell => {
                this.$('<td>').text(cell).appendTo($row);
            });
        });

        this.$(this.refs.reportContent).append($table);

        // 初始化表格插件
        $table.dataTable({
            paging: true,
            searching: true,
            ordering: true
        });
    }

    async fetchReportData() {
        // 模拟获取报表数据
        return {
            columns: [
                { label: 'Name' },
                { label: 'Amount' },
                { label: 'Date' }
            ],
            rows: [
                ['Product A', '$1,000', '2024-01-01'],
                ['Product B', '$2,000', '2024-01-02'],
                ['Product C', '$1,500', '2024-01-03']
            ]
        };
    }
}
```

## 🛠️ 实践练习

### 练习1: jQuery兼容性管理器
```javascript
class JQueryCompatibilityManager {
    constructor() {
        this.isReady = false;
        this.readyPromise = null;
        this.plugins = new Map();
        this.conflicts = new Map();
    }

    // 确保jQuery就绪
    async ensureReady() {
        if (this.isReady) {
            return;
        }

        if (!this.readyPromise) {
            this.readyPromise = this.initialize();
        }

        return this.readyPromise;
    }

    async initialize() {
        await ensureJQuery();
        this.$ = window.jQuery;
        this.isReady = true;

        // 设置全局错误处理
        this.setupErrorHandling();

        // 注册默认插件
        this.registerDefaultPlugins();
    }

    setupErrorHandling() {
        // 捕获jQuery错误
        this.$(document).on('error', '[data-jquery-plugin]', (event) => {
            console.error('jQuery plugin error:', event);
        });
    }

    registerDefaultPlugins() {
        // 注册常用的jQuery插件
        this.registerPlugin('datepicker', {
            init: (element, options) => this.$(element).datepicker(options),
            destroy: (element) => this.$(element).datepicker('destroy')
        });

        this.registerPlugin('select2', {
            init: (element, options) => this.$(element).select2(options),
            destroy: (element) => this.$(element).select2('destroy')
        });
    }

    // 注册jQuery插件
    registerPlugin(name, config) {
        this.plugins.set(name, config);
    }

    // 初始化插件
    async initPlugin(name, element, options = {}) {
        await this.ensureReady();

        const plugin = this.plugins.get(name);
        if (!plugin) {
            throw new Error(`Plugin ${name} not registered`);
        }

        try {
            return plugin.init(element, options);
        } catch (error) {
            console.error(`Failed to initialize plugin ${name}:`, error);
            throw error;
        }
    }

    // 销毁插件
    async destroyPlugin(name, element) {
        await this.ensureReady();

        const plugin = this.plugins.get(name);
        if (plugin && plugin.destroy) {
            try {
                plugin.destroy(element);
            } catch (error) {
                console.error(`Failed to destroy plugin ${name}:`, error);
            }
        }
    }

    // 处理命名冲突
    handleConflict(pluginName, conflictResolver) {
        this.conflicts.set(pluginName, conflictResolver);
    }

    // 安全执行jQuery代码
    async safeExecute(callback) {
        await this.ensureReady();

        try {
            return callback(this.$);
        } catch (error) {
            console.error('jQuery execution error:', error);
            throw error;
        }
    }

    // 批量初始化
    async batchInit(elements) {
        await this.ensureReady();

        const promises = elements.map(async ({ element, plugin, options }) => {
            try {
                return await this.initPlugin(plugin, element, options);
            } catch (error) {
                console.error(`Batch init failed for ${plugin}:`, error);
                return null;
            }
        });

        return Promise.allSettled(promises);
    }

    // 获取jQuery版本信息
    getVersionInfo() {
        if (!this.isReady) {
            return null;
        }

        return {
            jquery: this.$.fn.jquery,
            plugins: Array.from(this.plugins.keys()),
            conflicts: Array.from(this.conflicts.keys())
        };
    }
}

// 全局实例
const jqueryManager = new JQueryCompatibilityManager();

// 使用示例
class FormComponent extends Component {
    async setup() {
        this.jqueryManager = jqueryManager;
    }

    async mounted() {
        // 初始化日期选择器
        await this.jqueryManager.initPlugin(
            'datepicker',
            this.refs.dateInput,
            { format: 'yyyy-mm-dd' }
        );

        // 初始化下拉选择
        await this.jqueryManager.initPlugin(
            'select2',
            this.refs.selectInput,
            { placeholder: 'Select an option' }
        );
    }

    async willUnmount() {
        // 清理插件
        await this.jqueryManager.destroyPlugin('datepicker', this.refs.dateInput);
        await this.jqueryManager.destroyPlugin('select2', this.refs.selectInput);
    }
}
```

### 练习2: Bootstrap组件包装器
```javascript
class BootstrapComponentWrapper {
    constructor() {
        this.components = new Map();
        this.defaultOptions = new Map();
    }

    // 设置默认选项
    setDefaultOptions(componentName, options) {
        this.defaultOptions.set(componentName, options);
    }

    // 创建Bootstrap组件
    async createComponent(type, element, options = {}) {
        await ensureJQuery();
        const $ = window.jQuery;

        // 合并默认选项
        const defaultOpts = this.defaultOptions.get(type) || {};
        const finalOptions = { ...defaultOpts, ...options };

        let component;

        switch (type) {
            case 'modal':
                component = new BootstrapModal(element, finalOptions, $);
                break;
            case 'dropdown':
                component = new BootstrapDropdown(element, finalOptions, $);
                break;
            case 'tooltip':
                component = new BootstrapTooltip(element, finalOptions, $);
                break;
            case 'popover':
                component = new BootstrapPopover(element, finalOptions, $);
                break;
            default:
                throw new Error(`Unsupported component type: ${type}`);
        }

        this.components.set(element, component);
        return component;
    }

    // 获取组件实例
    getComponent(element) {
        return this.components.get(element);
    }

    // 销毁组件
    destroyComponent(element) {
        const component = this.components.get(element);
        if (component) {
            component.destroy();
            this.components.delete(element);
        }
    }

    // 销毁所有组件
    destroyAll() {
        for (const [element, component] of this.components.entries()) {
            component.destroy();
        }
        this.components.clear();
    }
}

// Bootstrap组件基类
class BootstrapComponent {
    constructor(element, options, $) {
        this.element = element;
        this.options = options;
        this.$ = $;
        this.isDestroyed = false;
        this.init();
    }

    init() {
        // 子类实现
    }

    destroy() {
        if (!this.isDestroyed) {
            this.cleanup();
            this.isDestroyed = true;
        }
    }

    cleanup() {
        // 子类实现
    }
}

// Modal组件包装
class BootstrapModal extends BootstrapComponent {
    init() {
        this.$element = this.$(this.element);
        this.$element.modal(this.options);

        // 绑定事件
        this.$element.on('shown.bs.modal', () => this.onShown());
        this.$element.on('hidden.bs.modal', () => this.onHidden());
    }

    show() {
        if (!this.isDestroyed) {
            this.$element.modal('show');
        }
    }

    hide() {
        if (!this.isDestroyed) {
            this.$element.modal('hide');
        }
    }

    toggle() {
        if (!this.isDestroyed) {
            this.$element.modal('toggle');
        }
    }

    onShown() {
        // 模态框显示后的回调
        this.options.onShown && this.options.onShown();
    }

    onHidden() {
        // 模态框隐藏后的回调
        this.options.onHidden && this.options.onHidden();
    }

    cleanup() {
        this.$element.off('.bs.modal');
        this.$element.modal('dispose');
    }
}

// Dropdown组件包装
class BootstrapDropdown extends BootstrapComponent {
    init() {
        this.$element = this.$(this.element);
        this.$element.dropdown(this.options);

        this.$element.on('shown.bs.dropdown', () => this.onShown());
        this.$element.on('hidden.bs.dropdown', () => this.onHidden());
    }

    show() {
        if (!this.isDestroyed) {
            this.$element.dropdown('show');
        }
    }

    hide() {
        if (!this.isDestroyed) {
            this.$element.dropdown('hide');
        }
    }

    toggle() {
        if (!this.isDestroyed) {
            this.$element.dropdown('toggle');
        }
    }

    onShown() {
        this.options.onShown && this.options.onShown();
    }

    onHidden() {
        this.options.onHidden && this.options.onHidden();
    }

    cleanup() {
        this.$element.off('.bs.dropdown');
        this.$element.dropdown('dispose');
    }
}

// Tooltip组件包装
class BootstrapTooltip extends BootstrapComponent {
    init() {
        this.$element = this.$(this.element);
        this.$element.tooltip(this.options);
    }

    show() {
        if (!this.isDestroyed) {
            this.$element.tooltip('show');
        }
    }

    hide() {
        if (!this.isDestroyed) {
            this.$element.tooltip('hide');
        }
    }

    cleanup() {
        this.$element.tooltip('dispose');
    }
}

// Popover组件包装
class BootstrapPopover extends BootstrapComponent {
    init() {
        this.$element = this.$(this.element);
        this.$element.popover(this.options);
    }

    show() {
        if (!this.isDestroyed) {
            this.$element.popover('show');
        }
    }

    hide() {
        if (!this.isDestroyed) {
            this.$element.popover('hide');
        }
    }

    cleanup() {
        this.$element.popover('dispose');
    }
}

// 使用示例
class UIComponent extends Component {
    static template = xml`
        <div class="ui-component">
            <button class="btn btn-primary" t-ref="modalTrigger" data-bs-toggle="modal" data-bs-target="#myModal">
                Open Modal
            </button>

            <div class="modal fade" id="myModal" t-ref="modal">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Modal Title</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>Modal content goes here.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dropdown" t-ref="dropdown">
                <button class="btn btn-secondary dropdown-toggle" type="button">
                    Dropdown
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#">Action</a></li>
                    <li><a class="dropdown-item" href="#">Another action</a></li>
                </ul>
            </div>
        </div>
    `;

    setup() {
        this.bootstrapWrapper = new BootstrapComponentWrapper();

        // 设置默认选项
        this.bootstrapWrapper.setDefaultOptions('modal', {
            backdrop: 'static',
            keyboard: false
        });
    }

    async mounted() {
        // 创建Modal组件
        this.modal = await this.bootstrapWrapper.createComponent(
            'modal',
            this.refs.modal,
            {
                onShown: () => console.log('Modal shown'),
                onHidden: () => console.log('Modal hidden')
            }
        );

        // 创建Dropdown组件
        this.dropdown = await this.bootstrapWrapper.createComponent(
            'dropdown',
            this.refs.dropdown
        );
    }

    willUnmount() {
        // 清理所有Bootstrap组件
        this.bootstrapWrapper.destroyAll();
    }
}
```

### 练习3: jQuery迁移助手
```javascript
class JQueryMigrationHelper {
    constructor() {
        this.migrationRules = new Map();
        this.warnings = [];
        this.setupDefaultRules();
    }

    setupDefaultRules() {
        // 添加迁移规则
        this.addRule('deprecated_methods', {
            check: (code) => {
                const deprecatedMethods = [
                    'live', 'die', 'bind', 'unbind', 'delegate', 'undelegate'
                ];
                return deprecatedMethods.some(method =>
                    code.includes(`$.${method}`) || code.includes(`.${method}(`)
                );
            },
            message: 'Using deprecated jQuery methods',
            suggestion: 'Use .on() and .off() instead'
        });

        this.addRule('document_ready', {
            check: (code) => code.includes('$(document).ready(') || code.includes('$(function()'),
            message: 'Using jQuery document ready',
            suggestion: 'Use modern component lifecycle methods'
        });

        this.addRule('global_jquery', {
            check: (code) => code.includes('$') && !code.includes('this.$'),
            message: 'Using global jQuery',
            suggestion: 'Use ensureJQuery() and local $ reference'
        });
    }

    addRule(name, rule) {
        this.migrationRules.set(name, rule);
    }

    // 分析代码中的jQuery使用
    analyzeCode(code) {
        const issues = [];

        for (const [ruleName, rule] of this.migrationRules.entries()) {
            if (rule.check(code)) {
                issues.push({
                    rule: ruleName,
                    message: rule.message,
                    suggestion: rule.suggestion
                });
            }
        }

        return issues;
    }

    // 生成迁移建议
    generateMigrationPlan(codeFiles) {
        const plan = {
            summary: {
                totalFiles: codeFiles.length,
                filesWithIssues: 0,
                totalIssues: 0
            },
            files: [],
            recommendations: []
        };

        for (const file of codeFiles) {
            const issues = this.analyzeCode(file.content);

            if (issues.length > 0) {
                plan.summary.filesWithIssues++;
                plan.summary.totalIssues += issues.length;

                plan.files.push({
                    path: file.path,
                    issues: issues
                });
            }
        }

        // 生成总体建议
        plan.recommendations = this.generateRecommendations(plan);

        return plan;
    }

    generateRecommendations(plan) {
        const recommendations = [];

        if (plan.summary.totalIssues > 0) {
            recommendations.push({
                priority: 'high',
                action: 'Replace deprecated jQuery methods',
                description: 'Update code to use modern jQuery methods'
            });
        }

        if (plan.summary.filesWithIssues > plan.summary.totalFiles * 0.5) {
            recommendations.push({
                priority: 'medium',
                action: 'Consider gradual migration',
                description: 'Plan a phased migration from jQuery to modern alternatives'
            });
        }

        recommendations.push({
            priority: 'low',
            action: 'Use ensureJQuery() pattern',
            description: 'Wrap jQuery usage with ensureJQuery() for better control'
        });

        return recommendations;
    }

    // 自动修复简单问题
    autoFix(code) {
        let fixedCode = code;

        // 修复document.ready
        fixedCode = fixedCode.replace(
            /\$\(document\)\.ready\(function\(\)\s*\{/g,
            'async mounted() {'
        );

        // 修复全局$使用
        fixedCode = fixedCode.replace(
            /(?<!this\.)\$\(/g,
            'this.$('
        );

        return fixedCode;
    }

    // 生成现代化的替代代码
    generateModernAlternative(jqueryCode) {
        const alternatives = {
            '$(element).addClass(className)': 'element.classList.add(className)',
            '$(element).removeClass(className)': 'element.classList.remove(className)',
            '$(element).hasClass(className)': 'element.classList.contains(className)',
            '$(element).attr(name, value)': 'element.setAttribute(name, value)',
            '$(element).removeAttr(name)': 'element.removeAttribute(name)',
            '$(element).text()': 'element.textContent',
            '$(element).text(value)': 'element.textContent = value',
            '$(element).html()': 'element.innerHTML',
            '$(element).html(value)': 'element.innerHTML = value',
            '$(element).show()': 'element.style.display = "block"',
            '$(element).hide()': 'element.style.display = "none"'
        };

        let modernCode = jqueryCode;

        for (const [jquery, modern] of Object.entries(alternatives)) {
            const regex = new RegExp(jquery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
            modernCode = modernCode.replace(regex, modern);
        }

        return modernCode;
    }
}

// 使用示例
const migrationHelper = new JQueryMigrationHelper();

// 分析现有代码
const codeFiles = [
    {
        path: 'legacy_component.js',
        content: `
            $(document).ready(function() {
                $('.button').click(function() {
                    $(this).addClass('active');
                });
            });
        `
    }
];

const migrationPlan = migrationHelper.generateMigrationPlan(codeFiles);
console.log('Migration Plan:', migrationPlan);

// 自动修复
const originalCode = `
    $(document).ready(function() {
        $('.dropdown').dropdown();
    });
`;

const fixedCode = migrationHelper.autoFix(originalCode);
console.log('Fixed Code:', fixedCode);
```

## 🔧 调试技巧

### 检查jQuery加载状态
```javascript
function checkJQueryStatus() {
    console.group('jQuery Status Check');

    if (window.jQuery) {
        console.log('✅ jQuery is loaded');
        console.log('Version:', window.jQuery.fn.jquery);

        // 检查Bootstrap集成
        const bootstrapComponents = ['dropdown', 'modal', 'tooltip', 'popover'];
        bootstrapComponents.forEach(component => {
            if (window.jQuery.fn[component]) {
                console.log(`✅ Bootstrap ${component} integrated`);
            } else {
                console.log(`❌ Bootstrap ${component} not integrated`);
            }
        });
    } else {
        console.log('❌ jQuery is not loaded');
    }

    console.groupEnd();
}

// 在控制台中调用
checkJQueryStatus();
```

### 监控jQuery使用
```javascript
function monitorJQueryUsage() {
    if (window.jQuery) {
        const originalFn = window.jQuery.fn.init;

        window.jQuery.fn.init = function(selector, context) {
            console.log('jQuery selector used:', selector);
            return originalFn.call(this, selector, context);
        };

        window.jQuery.fn.init.prototype = originalFn.prototype;
    }
}

// 启用监控
monitorJQueryUsage();
```

## 📊 性能考虑

### 优化策略
1. **按需加载**: 只在需要时加载jQuery
2. **避免全局污染**: 使用局部引用而非全局$
3. **及时清理**: 组件销毁时清理jQuery插件
4. **缓存选择器**: 避免重复的DOM查询

### 最佳实践
```javascript
// ✅ 好的做法：按需加载和局部使用
class Component {
    async setup() {
        await ensureJQuery();
        this.$ = window.jQuery;
    }

    mounted() {
        this.$element = this.$(this.refs.element);
        this.$element.somePlugin();
    }

    willUnmount() {
        this.$element.somePlugin('destroy');
    }
}

// ❌ 不好的做法：假设jQuery总是可用
class BadComponent {
    mounted() {
        $('.element').somePlugin(); // 可能失败
    }
}

// ✅ 好的做法：缓存jQuery对象
const $element = this.$(this.refs.element);
$element.addClass('active');
$element.attr('data-state', 'loaded');

// ❌ 不好的做法：重复查询
this.$(this.refs.element).addClass('active');
this.$(this.refs.element).attr('data-state', 'loaded');
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解ensureJQuery的设计目的和实现原理
- [ ] 掌握Bootstrap组件的jQuery集成机制
- [ ] 能够在现代框架中安全使用jQuery
- [ ] 理解jQuery插件的生命周期管理
- [ ] 掌握jQuery代码的迁移和现代化策略
- [ ] 了解第三方库集成的最佳实践

## 🚀 下一步学习
学完ensureJQuery后，建议继续学习：
1. **资源管理** (回顾`@web/core/assets.js`) - 理解动态资源加载
2. **组件生命周期** (`@web/core/component.js`) - 掌握组件的生命周期管理
3. **现代化迁移** - 学习从jQuery迁移到现代前端技术

## 💡 重要提示
- ensureJQuery是遗留代码兼容性的桥梁
- 理解Bootstrap集成对UI组件开发很重要
- 按需加载是现代前端开发的最佳实践
- jQuery应该被视为过渡性解决方案，而非长期依赖
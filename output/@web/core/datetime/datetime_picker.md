# @web/core/datetime/datetime_picker.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/datetime/datetime_picker.js`
- **原始路径**: `/web/static/src/core/datetime/datetime_picker.js`
- **代码行数**: 738行
- **作用**: 实现完整的日期时间选择器组件，提供日历视图、时间选择和多精度级别支持

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 复杂日期时间组件的架构设计
- 多精度级别的日历视图实现
- 日期范围选择的交互逻辑
- 时间系统的处理和本地化
- 企业级日期选择器的完整实现

## 📚 核心概念

### 什么是日期时间选择器？
日期时间选择器是一个**全功能日历组件**，主要功能：
- **多视图支持**: 支持日、月、年、十年等多个精度级别
- **范围选择**: 支持单日期和日期范围选择
- **时间选择**: 集成时间选择功能
- **本地化**: 完整的国际化和本地化支持

### 核心架构组成
```javascript
// 主要组件类
const DateTimePicker = class extends Component {
    static props,           // 组件属性定义
    static template,        // 组件模板
    setup(),               // 组件设置
    
    // 核心方法
    zoomIn(),              // 放大视图
    zoomOut(),             // 缩小视图
    validateAndSelect(),   // 验证并选择日期
    next(),                // 下一个周期
    previous()             // 上一个周期
};

// 精度级别系统
const PRECISION_LEVELS = new Map([
    ["days", DaysPrecision],      // 日视图
    ["months", MonthsPrecision],  // 月视图
    ["years", YearsPrecision],    // 年视图
    ["decades", DecadesPrecision] // 十年视图
]);

// 类型定义
const DateTimePickerProps = {
    value: "DateTime | DateRange",     // 当前值
    type: "date | datetime",           // 选择器类型
    range: "boolean",                  // 是否范围选择
    minDate: "DateTime",               // 最小日期
    maxDate: "DateTime",               // 最大日期
    onSelect: "function"               // 选择回调
};
```

### 基本使用模式
```javascript
import { DateTimePicker } from '@web/core/datetime/datetime_picker';

// 单日期选择
<DateTimePicker 
    type="date"
    value="state.selectedDate"
    onSelect="(date) => this.onDateSelect(date)"
    minDate="DateTime.local().minus({days: 30})"
    maxDate="DateTime.local().plus({days: 30})"/>

// 日期范围选择
<DateTimePicker 
    type="datetime"
    range="true"
    value="[state.startDate, state.endDate]"
    onSelect="(range) => this.onRangeSelect(range)"
    showWeekNumbers="true"/>

// 自定义精度级别
<DateTimePicker 
    minPrecision="months"
    maxPrecision="years"
    value="state.selectedMonth"
    onSelect="(date) => this.onMonthSelect(date)"/>
```

## 🔍 核心实现详解

### 1. 精度级别系统

#### 精度级别定义
```javascript
const PRECISION_LEVELS = new Map()
    .set("days", {
        mainTitle: _t("Select month"),
        nextTitle: _t("Next month"),
        prevTitle: _t("Previous month"),
        step: { month: 1 },
        getTitle: (date, { additionalMonth }) => {
            const titles = [`${date.monthLong} ${date.year}`];
            if (additionalMonth) {
                const next = date.plus({ month: 1 });
                titles.push(`${next.monthLong} ${next.year}`);
            }
            return titles;
        },
        getItems: (date, params) => {
            // 生成日历项目
        }
    })
    .set("months", {
        mainTitle: _t("Select year"),
        nextTitle: _t("Next year"),
        prevTitle: _t("Previous year"),
        step: { year: 1 },
        getTitle: (date) => String(date.year),
        getItems: (date, { maxDate, minDate }) => {
            // 生成月份项目
        }
    });
```

**精度级别特点**：
- **层次结构**: 从日到十年的完整层次结构
- **导航信息**: 每个级别包含导航标题和步长
- **动态生成**: 通过getTitle和getItems动态生成内容
- **国际化**: 所有文本都支持国际化

#### 日视图实现
```javascript
getItems: (date, { additionalMonth, maxDate, minDate, showWeekNumbers, isDateValid, dayCellClass }) => {
    const startDates = [date];
    if (additionalMonth) {
        startDates.push(date.plus({ month: 1 }));
    }
    
    return startDates.map((date, i) => {
        const monthRange = [date.startOf("month"), date.endOf("month")];
        const weeks = [];
        
        // 生成6周的日历
        let startOfNextWeek = getStartOfWeek(monthRange[0]);
        for (let w = 0; w < 6; w++) {
            const weekDayItems = [];
            // 生成一周的所有日期
            for (let d = 0; d < 7; d++) {
                const day = startOfNextWeek.plus({ day: d });
                const range = [day, day.endOf("day")];
                const dayItem = toDateItem({
                    isOutOfRange: !isInRange(day, monthRange),
                    isValid: isInRange(range, [minDate, maxDate]) && isDateValid?.(day),
                    label: "day",
                    range,
                    extraClass: dayCellClass?.(day) || "",
                });
                weekDayItems.push(dayItem);
            }
            weeks.push(toWeekItem(weekDayItems));
        }
        
        return {
            id: `__month__${i}`,
            number: monthRange[0].month,
            daysOfWeek: generateDaysOfWeek(weeks[0], showWeekNumbers),
            weeks,
        };
    });
}
```

**日视图特点**：
- **完整月份**: 显示完整的6周日历
- **跨月显示**: 包含上月末和下月初的日期
- **周数支持**: 可选显示周数
- **验证集成**: 集成日期验证和样式定制

### 2. 状态管理系统

#### 组件状态结构
```javascript
this.state = useState({
    /** @type {DateTime | null} */
    focusDate: null,           // 当前焦点日期
    /** @type {DateTime | null} */
    hoveredDate: null,         // 悬停日期
    /** @type {[number, number, number][]} */
    timeValues: [],            // 时间值数组
    /** @type {PrecisionLevel} */
    precision: this.props.minPrecision,  // 当前精度级别
});
```

**状态设计原则**：
- **焦点管理**: focusDate控制当前显示的日期范围
- **交互状态**: hoveredDate支持悬停预览效果
- **时间分离**: timeValues单独管理时间选择
- **精度控制**: precision控制当前视图级别

#### 属性更新处理
```javascript
onPropsUpdated(props) {
    // 处理值数组
    this.values = ensureArray(props.value).map((value) =>
        value && !value.isValid ? null : value
    );
    
    // 处理时间精度
    this.availableMinutes = MINUTES.filter((minute) => !(minute[0] % props.rounding));
    this.availableSeconds = props.rounding ? [] : SECONDS;
    
    // 处理精度级别
    this.allowedPrecisionLevels = this.filterPrecisionLevels(
        props.minPrecision,
        props.maxPrecision
    );
    
    // 处理日期限制
    this.maxDate = parseLimitDate(props.maxDate, MAX_VALID_DATE);
    this.minDate = parseLimitDate(props.minDate, MIN_VALID_DATE);
    
    // 调整焦点和时间
    this.adjustFocus(this.values, props.focusedDateIndex);
    this.handle12HourSystem();
}
```

**更新策略**：
- **值验证**: 确保所有日期值都是有效的
- **时间处理**: 根据舍入设置过滤可用时间
- **范围验证**: 验证最大最小日期的合理性
- **焦点调整**: 智能调整焦点日期

### 3. 范围选择逻辑

#### 范围计算机制
```javascript
onWillRender() {
    const { hoveredDate } = this.state;
    
    // 选中范围：当前值加上悬停日期
    this.selectedRange = [...this.values];
    // 高亮范围：当前值和选中范围的并集
    this.highlightedRange = [...this.values];
    
    // 应用悬停日期到选中范围
    if (hoveredDate) {
        [this.selectedRange] = this.applyValueAtIndex(hoveredDate, this.props.focusedDateIndex);
        if (this.props.range && this.selectedRange.every(Boolean)) {
            this.highlightedRange = [
                earliest(this.selectedRange[0], this.values[0]),
                latest(this.selectedRange[1], this.values[1]),
            ];
        }
    }
}
```

**范围逻辑特点**：
- **实时预览**: 悬停时实时显示预期范围
- **智能排序**: 自动处理开始和结束日期的顺序
- **视觉反馈**: 区分选中、高亮和当前状态
- **交互优化**: 提供直观的范围选择体验

#### 值应用策略
```javascript
applyValueAtIndex(value, valueIndex) {
    const result = [...this.values];
    if (this.props.range) {
        if (
            (result[0] && value.endOf("day") < result[0].startOf("day")) ||
            (result[1] && !result[0])
        ) {
            valueIndex = 0;  // 设置为开始日期
        } else if (
            (result[1] && result[1].endOf("day") < value.startOf("day")) ||
            (result[0] && !result[1])
        ) {
            valueIndex = 1;  // 设置为结束日期
        }
    }
    result[valueIndex] = value;
    return [result, valueIndex];
}
```

**应用策略**：
- **智能索引**: 根据日期关系自动确定索引
- **范围保持**: 确保开始日期不晚于结束日期
- **灵活处理**: 支持部分范围的情况
- **返回信息**: 返回结果和实际使用的索引

### 4. 时间系统处理

#### 12小时制支持
```javascript
handle12HourSystem() {
    if (isMeridiemFormat()) {
        this.meridiems = MERIDIEMS.map((m) => [m, m]);
        for (const timeValues of this.state.timeValues) {
            if (timeValues) {
                timeValues.push(MERIDIEMS[Math.floor(timeValues[0] / 12) || 0]);
            }
        }
    }
    
    this.is12HourFormat = !is24HourFormat();
    if (this.is12HourFormat) {
        this.availableHours = [[0, HOURS[12][1]], ...HOURS.slice(1, 12)];
        for (const timeValues of this.state.timeValues) {
            if (timeValues) {
                timeValues[0] %= 12;
            }
        }
    }
}
```

**时间系统特点**：
- **格式检测**: 自动检测当前地区的时间格式
- **AM/PM支持**: 完整的上午下午支持
- **小时调整**: 12小时制下的小时值调整
- **本地化**: 根据地区设置调整显示

#### 时间值获取
```javascript
getTimeValues(valueIndex) {
    let [hour, minute, second] = this.state.timeValues[valueIndex].map(Number);
    if (
        this.is12HourFormat &&
        this.meridiems &&
        this.state.timeValues[valueIndex][3] === "PM"
    ) {
        hour += 12;
    }
    return [hour, minute, second];
}
```

**值处理特点**：
- **类型转换**: 字符串到数字的安全转换
- **12小时转换**: PM时自动加12小时
- **完整时间**: 返回小时、分钟、秒的完整时间
- **索引支持**: 支持多个时间值的管理

## 🎨 实际应用场景

### 1. 高级日期时间选择器系统
```javascript
class AdvancedDateTimePickerManager {
    constructor() {
        this.pickerInstances = new Map();
        this.customPrecisionLevels = new Map();
        this.dateValidators = new Map();
        this.dateFormatters = new Map();
        this.themes = new Map();
        this.setupDefaultConfigurations();
    }
    
    setupDefaultConfigurations() {
        // 业务日期选择器配置
        this.addConfiguration('business', {
            name: 'Business Date Picker',
            props: {
                type: 'date',
                minPrecision: 'days',
                maxPrecision: 'months',
                isDateValid: (date) => {
                    const day = date.weekday;
                    return day >= 1 && day <= 5; // 只允许工作日
                },
                dayCellClass: (date) => {
                    const day = date.weekday;
                    if (day === 6 || day === 7) return 'weekend-disabled';
                    return '';
                }
            },
            validators: ['businessDay', 'notHoliday'],
            formatters: ['businessDate']
        });
        
        // 项目时间线选择器配置
        this.addConfiguration('timeline', {
            name: 'Project Timeline Picker',
            props: {
                type: 'datetime',
                range: true,
                minPrecision: 'days',
                maxPrecision: 'years',
                rounding: 15, // 15分钟间隔
                showWeekNumbers: true
            },
            validators: ['projectDuration', 'resourceAvailability'],
            formatters: ['projectTimeline']
        });
        
        // 财务期间选择器配置
        this.addConfiguration('fiscal', {
            name: 'Fiscal Period Picker',
            props: {
                type: 'date',
                minPrecision: 'months',
                maxPrecision: 'years',
                range: true
            },
            validators: ['fiscalPeriod'],
            formatters: ['fiscalDate'],
            customPrecision: 'quarters'
        });
        
        // 生日选择器配置
        this.addConfiguration('birthday', {
            name: 'Birthday Picker',
            props: {
                type: 'date',
                minPrecision: 'days',
                maxPrecision: 'decades',
                maxDate: 'today',
                minDate: DateTime.local().minus({ years: 120 })
            },
            validators: ['validAge'],
            formatters: ['birthdayFormat']
        });
    }
    
    addConfiguration(name, config) {
        this.configurations.set(name, config);
        
        // 如果有自定义精度级别，注册它
        if (config.customPrecision) {
            this.registerCustomPrecisionLevel(config.customPrecision);
        }
    }
    
    registerCustomPrecisionLevel(name) {
        switch (name) {
            case 'quarters':
                this.customPrecisionLevels.set('quarters', {
                    mainTitle: _t("Select year"),
                    nextTitle: _t("Next year"),
                    prevTitle: _t("Previous year"),
                    step: { year: 1 },
                    getTitle: (date) => String(date.year),
                    getItems: (date, { maxDate, minDate }) => {
                        const startOfYear = date.startOf("year");
                        return [0, 3, 6, 9].map((monthOffset) => {
                            const startOfQuarter = startOfYear.plus({ month: monthOffset });
                            const endOfQuarter = startOfQuarter.plus({ month: 3, millisecond: -1 });
                            const range = [startOfQuarter, endOfQuarter];
                            
                            return toDateItem({
                                isValid: isInRange(range, [minDate, maxDate]),
                                label: "quarter",
                                range,
                                extraClass: `quarter-${Math.floor(monthOffset / 3) + 1}`
                            });
                        });
                    }
                });
                break;
                
            case 'weeks':
                this.customPrecisionLevels.set('weeks', {
                    mainTitle: _t("Select month"),
                    nextTitle: _t("Next month"),
                    prevTitle: _t("Previous month"),
                    step: { month: 1 },
                    getTitle: (date) => `${date.monthLong} ${date.year}`,
                    getItems: (date, { maxDate, minDate }) => {
                        const startOfMonth = date.startOf("month");
                        const endOfMonth = date.endOf("month");
                        const weeks = [];
                        
                        let currentWeekStart = getStartOfWeek(startOfMonth);
                        while (currentWeekStart <= endOfMonth) {
                            const weekEnd = currentWeekStart.plus({ days: 6 });
                            const range = [currentWeekStart, weekEnd];
                            
                            weeks.push(toDateItem({
                                isValid: isInRange(range, [minDate, maxDate]),
                                label: "weekNumber",
                                range,
                                extraClass: `week-${currentWeekStart.weekNumber}`
                            }));
                            
                            currentWeekStart = currentWeekStart.plus({ week: 1 });
                        }
                        
                        return weeks;
                    }
                });
                break;
        }
    }
    
    createEnhancedPicker(configName, customProps = {}) {
        const config = this.configurations.get(configName);
        if (!config) {
            throw new Error(`Unknown picker configuration: ${configName}`);
        }
        
        return class EnhancedDateTimePicker extends DateTimePicker {
            static template = `web.Enhanced${configName.charAt(0).toUpperCase() + configName.slice(1)}Picker`;
            
            setup() {
                // 合并配置属性
                const mergedProps = {
                    ...config.props,
                    ...customProps,
                    ...this.props
                };
                
                // 应用自定义精度级别
                if (config.customPrecision && this.parent.customPrecisionLevels.has(config.customPrecision)) {
                    const customLevel = this.parent.customPrecisionLevels.get(config.customPrecision);
                    PRECISION_LEVELS.set(config.customPrecision, customLevel);
                }
                
                // 增强的选择处理
                const originalOnSelect = mergedProps.onSelect;
                mergedProps.onSelect = (value) => {
                    // 应用验证器
                    if (config.validators) {
                        const isValid = this.validateWithRules(value, config.validators);
                        if (!isValid) {
                            this.showValidationError(value);
                            return;
                        }
                    }
                    
                    // 应用格式化器
                    if (config.formatters) {
                        value = this.formatWithRules(value, config.formatters);
                    }
                    
                    // 记录使用情况
                    this.recordUsage(configName, value);
                    
                    // 调用原始回调
                    if (originalOnSelect) {
                        originalOnSelect(value);
                    }
                };
                
                // 调用基础设置
                super.setup();
                
                // 应用主题
                if (config.theme) {
                    this.applyTheme(config.theme);
                }
            }
            
            validateWithRules(value, validatorNames) {
                return validatorNames.every(name => {
                    const validator = this.parent.getValidator(name);
                    return validator ? validator(value) : true;
                });
            }
            
            formatWithRules(value, formatterNames) {
                return formatterNames.reduce((result, name) => {
                    const formatter = this.parent.getFormatter(name);
                    return formatter ? formatter(result) : result;
                }, value);
            }
            
            showValidationError(value) {
                // 显示验证错误
                console.warn(`Invalid date selection for ${configName}:`, value);
                // 可以集成通知系统显示用户友好的错误消息
            }
            
            recordUsage(configName, value) {
                // 记录使用统计
                const usage = {
                    config: configName,
                    value: value,
                    timestamp: Date.now()
                };
                
                this.parent.recordPickerUsage(usage);
            }
            
            applyTheme(themeName) {
                const theme = this.parent.getTheme(themeName);
                if (theme) {
                    this.el.classList.add(`theme-${themeName}`);
                }
            }
        };
    }
    
    // 验证器管理
    addValidator(name, validator) {
        this.dateValidators.set(name, validator);
    }
    
    getValidator(name) {
        return this.dateValidators.get(name);
    }
    
    // 格式化器管理
    addFormatter(name, formatter) {
        this.dateFormatters.set(name, formatter);
    }
    
    getFormatter(name) {
        return this.dateFormatters.get(name);
    }
    
    // 主题管理
    addTheme(name, theme) {
        this.themes.set(name, theme);
    }
    
    getTheme(name) {
        return this.themes.get(name);
    }
    
    // 使用统计
    recordPickerUsage(usage) {
        const key = `picker_usage_${usage.config}`;
        const history = JSON.parse(localStorage.getItem(key) || '[]');
        history.push(usage);
        
        // 保留最近100条记录
        if (history.length > 100) {
            history.splice(0, history.length - 100);
        }
        
        localStorage.setItem(key, JSON.stringify(history));
    }
    
    getUsageStats(configName) {
        const key = `picker_usage_${configName}`;
        const history = JSON.parse(localStorage.getItem(key) || '[]');
        
        return {
            totalUsage: history.length,
            recentUsage: history.filter(u => Date.now() - u.timestamp < 7 * 24 * 60 * 60 * 1000).length,
            lastUsed: history.length > 0 ? new Date(history[history.length - 1].timestamp) : null,
            mostUsedValues: this.analyzeMostUsedValues(history)
        };
    }
    
    analyzeMostUsedValues(history) {
        const valueCount = {};
        history.forEach(usage => {
            const key = Array.isArray(usage.value) 
                ? usage.value.map(v => v?.toISODate()).join(' - ')
                : usage.value?.toISODate();
            
            valueCount[key] = (valueCount[key] || 0) + 1;
        });
        
        return Object.entries(valueCount)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([value, count]) => ({ value, count }));
    }
}

// 预定义验证器
const CommonValidators = {
    businessDay: (date) => {
        if (Array.isArray(date)) {
            return date.every(d => d && d.weekday >= 1 && d.weekday <= 5);
        }
        return date && date.weekday >= 1 && date.weekday <= 5;
    },
    
    notHoliday: (date) => {
        // 简化的假期检查
        const holidays = [
            '2023-01-01', '2023-07-04', '2023-12-25'
        ];
        
        if (Array.isArray(date)) {
            return date.every(d => d && !holidays.includes(d.toISODate()));
        }
        return date && !holidays.includes(date.toISODate());
    },
    
    projectDuration: (dateRange) => {
        if (!Array.isArray(dateRange) || dateRange.length !== 2) return false;
        const [start, end] = dateRange;
        if (!start || !end) return false;
        
        const duration = end.diff(start, 'days').days;
        return duration >= 1 && duration <= 365; // 1天到1年
    },
    
    fiscalPeriod: (dateRange) => {
        if (!Array.isArray(dateRange)) return true;
        const [start, end] = dateRange;
        if (!start || !end) return true;
        
        // 财务期间应该是完整的月份
        return start.day === 1 && end.equals(end.endOf('month'));
    },
    
    validAge: (date) => {
        if (!date) return false;
        const age = DateTime.local().diff(date, 'years').years;
        return age >= 0 && age <= 120;
    }
};

// 预定义格式化器
const CommonFormatters = {
    businessDate: (date) => {
        if (Array.isArray(date)) {
            return date.map(d => d?.toFormat('yyyy-MM-dd'));
        }
        return date?.toFormat('yyyy-MM-dd');
    },
    
    projectTimeline: (dateRange) => {
        if (!Array.isArray(dateRange)) return dateRange;
        const [start, end] = dateRange;
        return {
            start: start?.toISO(),
            end: end?.toISO(),
            duration: end && start ? end.diff(start, 'days').days : null
        };
    },
    
    fiscalDate: (dateRange) => {
        if (!Array.isArray(dateRange)) return dateRange;
        const [start, end] = dateRange;
        return {
            fiscalYear: start?.year,
            quarter: Math.ceil(start?.month / 3),
            period: `${start?.toFormat('yyyy-MM')} to ${end?.toFormat('yyyy-MM')}`
        };
    },
    
    birthdayFormat: (date) => {
        if (!date) return null;
        const age = DateTime.local().diff(date, 'years').years;
        return {
            date: date.toFormat('yyyy-MM-dd'),
            age: Math.floor(age),
            zodiac: this.getZodiacSign(date)
        };
    }
};

// 使用示例
const pickerManager = new AdvancedDateTimePickerManager();

// 添加验证器和格式化器
Object.entries(CommonValidators).forEach(([name, validator]) => {
    pickerManager.addValidator(name, validator);
});

Object.entries(CommonFormatters).forEach(([name, formatter]) => {
    pickerManager.addFormatter(name, formatter);
});

// 创建业务日期选择器
const BusinessDatePicker = pickerManager.createEnhancedPicker('business', {
    onSelect: (date) => {
        console.log('Business date selected:', date);
    }
});

// 在组件中使用
class ProjectPlanningComponent extends Component {
    static template = xml`
        <div class="project-planning">
            <BusinessDatePicker 
                value="state.projectStartDate"
                onSelect="(date) => this.onStartDateSelect(date)"/>
        </div>
    `;
    
    static components = { BusinessDatePicker };
    
    setup() {
        this.state = useState({
            projectStartDate: null
        });
    }
    
    onStartDateSelect(date) {
        this.state.projectStartDate = date;
    }
}
```

### 2. 日期时间选择器测试框架
```javascript
class DateTimePickerTestFramework {
    constructor() {
        this.testSuites = new Map();
        this.mockData = this.createMockData();
        this.testResults = [];
        this.setupTestSuites();
    }

    createMockData() {
        const now = DateTime.local();
        return {
            dates: {
                today: now,
                yesterday: now.minus({ days: 1 }),
                tomorrow: now.plus({ days: 1 }),
                lastWeek: now.minus({ weeks: 1 }),
                nextWeek: now.plus({ weeks: 1 }),
                lastMonth: now.minus({ months: 1 }),
                nextMonth: now.plus({ months: 1 })
            },
            ranges: {
                thisWeek: [now.startOf('week'), now.endOf('week')],
                thisMonth: [now.startOf('month'), now.endOf('month')],
                lastQuarter: [
                    now.minus({ quarters: 1 }).startOf('quarter'),
                    now.minus({ quarters: 1 }).endOf('quarter')
                ]
            },
            invalidDates: [
                null,
                undefined,
                DateTime.invalid('invalid'),
                'invalid-date-string'
            ]
        };
    }

    setupTestSuites() {
        // 基础功能测试
        this.testSuites.set('basic_functionality', {
            name: 'Basic Functionality Tests',
            tests: [
                () => this.testComponentInitialization(),
                () => this.testDateSelection(),
                () => this.testRangeSelection(),
                () => this.testPrecisionLevels(),
                () => this.testTimeSelection()
            ]
        });

        // 精度级别测试
        this.testSuites.set('precision_levels', {
            name: 'Precision Level Tests',
            tests: [
                () => this.testDaysPrecision(),
                () => this.testMonthsPrecision(),
                () => this.testYearsPrecision(),
                () => this.testDecadesPrecision(),
                () => this.testPrecisionNavigation()
            ]
        });

        // 验证和边界测试
        this.testSuites.set('validation', {
            name: 'Validation and Boundary Tests',
            tests: [
                () => this.testDateLimits(),
                () => this.testInvalidDates(),
                () => this.testCustomValidation(),
                () => this.testRangeValidation(),
                () => this.testTimeValidation()
            ]
        });

        // 本地化测试
        this.testSuites.set('localization', {
            name: 'Localization Tests',
            tests: [
                () => this.testTimeFormats(),
                () => this.testWeekStart(),
                () => this.testDateFormats(),
                () => this.testLanguageSupport()
            ]
        });

        // 性能测试
        this.testSuites.set('performance', {
            name: 'Performance Tests',
            tests: [
                () => this.testRenderingPerformance(),
                () => this.testNavigationPerformance(),
                () => this.testLargeRangePerformance(),
                () => this.testMemoryUsage()
            ]
        });
    }

    // 基础功能测试
    testComponentInitialization() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            // 测试默认属性
            const picker = this.createMockPicker();

            if (picker.state.precision === 'days') {
                results.tests.push({
                    name: 'Default precision is days',
                    status: 'passed',
                    message: 'Component initializes with correct default precision'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Default precision is days',
                    status: 'failed',
                    message: `Expected 'days', got '${picker.state.precision}'`
                });
                results.failed++;
            }

            // 测试状态初始化
            if (picker.state.focusDate && picker.state.timeValues) {
                results.tests.push({
                    name: 'State initialization',
                    status: 'passed',
                    message: 'Component state is properly initialized'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'State initialization',
                    status: 'failed',
                    message: 'Component state is not properly initialized'
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Component initialization',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    testDateSelection() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            const picker = this.createMockPicker({
                onSelect: jest.fn()
            });

            // 模拟日期选择
            const testDate = this.mockData.dates.today;
            const dateItem = {
                range: [testDate, testDate.endOf('day')],
                isValid: true
            };

            picker.zoomOrSelect(dateItem);

            if (picker.props.onSelect.mock.calls.length > 0) {
                results.tests.push({
                    name: 'Date selection triggers callback',
                    status: 'passed',
                    message: 'onSelect callback is called when date is selected'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Date selection triggers callback',
                    status: 'failed',
                    message: 'onSelect callback was not called'
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Date selection',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    testRangeSelection() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            const picker = this.createMockPicker({
                range: true,
                onSelect: jest.fn()
            });

            // 模拟范围选择
            const [startDate, endDate] = this.mockData.ranges.thisWeek;

            // 选择开始日期
            picker.validateAndSelect(startDate, 0);
            // 选择结束日期
            picker.validateAndSelect(endDate, 1);

            if (picker.props.onSelect.mock.calls.length >= 2) {
                results.tests.push({
                    name: 'Range selection works',
                    status: 'passed',
                    message: 'Range selection triggers multiple callbacks'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Range selection works',
                    status: 'failed',
                    message: 'Range selection did not work properly'
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Range selection',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    testPrecisionLevels() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            const picker = this.createMockPicker();

            // 测试精度级别切换
            const initialPrecision = picker.state.precision;
            const zoomResult = picker.zoomOut();

            if (zoomResult && picker.state.precision !== initialPrecision) {
                results.tests.push({
                    name: 'Precision level zoom out',
                    status: 'passed',
                    message: 'Zoom out changes precision level'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Precision level zoom out',
                    status: 'failed',
                    message: 'Zoom out did not change precision level'
                });
                results.failed++;
            }

            // 测试精度级别限制
            picker.state.precision = 'decades';
            const zoomOutResult = picker.zoomOut();

            if (!zoomOutResult) {
                results.tests.push({
                    name: 'Precision level limits',
                    status: 'passed',
                    message: 'Cannot zoom out beyond maximum precision'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Precision level limits',
                    status: 'failed',
                    message: 'Zoom out should be limited at maximum precision'
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Precision levels',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    testDateLimits() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            const minDate = this.mockData.dates.today;
            const maxDate = this.mockData.dates.nextMonth;

            const picker = this.createMockPicker({
                minDate,
                maxDate,
                onSelect: jest.fn()
            });

            // 测试超出范围的日期
            const invalidDate = this.mockData.dates.yesterday;
            const isValid = picker.validateAndSelect(invalidDate, 0);

            if (!isValid) {
                results.tests.push({
                    name: 'Date limits validation',
                    status: 'passed',
                    message: 'Dates outside limits are rejected'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Date limits validation',
                    status: 'failed',
                    message: 'Date outside limits was accepted'
                });
                results.failed++;
            }

            // 测试有效范围内的日期
            const validDate = this.mockData.dates.tomorrow;
            const isValidDate = picker.validateAndSelect(validDate, 0);

            if (isValidDate) {
                results.tests.push({
                    name: 'Valid date acceptance',
                    status: 'passed',
                    message: 'Valid dates within limits are accepted'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Valid date acceptance',
                    status: 'failed',
                    message: 'Valid date within limits was rejected'
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Date limits',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    testRenderingPerformance() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            const startTime = performance.now();

            // 创建多个选择器实例
            const pickers = [];
            for (let i = 0; i < 10; i++) {
                pickers.push(this.createMockPicker());
            }

            const endTime = performance.now();
            const duration = endTime - startTime;

            if (duration < 100) { // 100ms内完成
                results.tests.push({
                    name: 'Multiple picker creation performance',
                    status: 'passed',
                    message: `Created 10 pickers in ${duration.toFixed(2)}ms`
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Multiple picker creation performance',
                    status: 'failed',
                    message: `Creation took too long: ${duration.toFixed(2)}ms`
                });
                results.failed++;
            }

            // 测试渲染性能
            const renderStartTime = performance.now();

            pickers.forEach(picker => {
                picker.onWillRender();
            });

            const renderEndTime = performance.now();
            const renderDuration = renderEndTime - renderStartTime;

            if (renderDuration < 50) { // 50ms内完成
                results.tests.push({
                    name: 'Rendering performance',
                    status: 'passed',
                    message: `Rendered 10 pickers in ${renderDuration.toFixed(2)}ms`
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Rendering performance',
                    status: 'failed',
                    message: `Rendering took too long: ${renderDuration.toFixed(2)}ms`
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Rendering performance',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    createMockPicker(props = {}) {
        const defaultProps = {
            type: 'datetime',
            minPrecision: 'days',
            maxPrecision: 'decades',
            focusedDateIndex: 0,
            rounding: 5,
            ...props
        };

        // 创建模拟的选择器实例
        const mockPicker = {
            props: defaultProps,
            state: {
                focusDate: this.mockData.dates.today,
                hoveredDate: null,
                timeValues: [['12', '00', '00']],
                precision: defaultProps.minPrecision
            },
            values: [],
            allowedPrecisionLevels: ['days', 'months', 'years', 'decades'],

            // 模拟方法
            onPropsUpdated: function(props) {
                this.values = ensureArray(props.value || []);
                this.maxDate = parseLimitDate(props.maxDate, MAX_VALID_DATE);
                this.minDate = parseLimitDate(props.minDate, MIN_VALID_DATE);
            },

            onWillRender: function() {
                this.selectedRange = [...this.values];
                this.highlightedRange = [...this.values];
            },

            validateAndSelect: function(value, valueIndex) {
                if (!this.props.onSelect) return false;

                const [result] = this.applyValueAtIndex(value, valueIndex);
                if (!isInRange(result[valueIndex], [this.minDate, this.maxDate])) {
                    return false;
                }

                this.props.onSelect(result.length === 2 ? result : result[0]);
                return true;
            },

            applyValueAtIndex: function(value, valueIndex) {
                const result = [...this.values];
                result[valueIndex] = value;
                return [result, valueIndex];
            },

            zoomIn: function(date) {
                const index = this.allowedPrecisionLevels.indexOf(this.state.precision) - 1;
                if (index >= 0) {
                    this.state.precision = this.allowedPrecisionLevels[index];
                    return true;
                }
                return false;
            },

            zoomOut: function() {
                const index = this.allowedPrecisionLevels.indexOf(this.state.precision) + 1;
                if (index < this.allowedPrecisionLevels.length) {
                    this.state.precision = this.allowedPrecisionLevels[index];
                    return true;
                }
                return false;
            },

            zoomOrSelect: function(dateItem) {
                if (!dateItem.isValid) return;
                if (this.zoomIn(dateItem.range[0])) return;
                this.validateAndSelect(dateItem.range[0], this.props.focusedDateIndex);
            }
        };

        // 初始化
        mockPicker.onPropsUpdated(defaultProps);

        return mockPicker;
    }

    runAllTests() {
        const allResults = {};

        for (const [suiteId, suite] of this.testSuites.entries()) {
            console.log(`Running ${suite.name}...`);

            const suiteResults = {
                name: suite.name,
                passed: 0,
                failed: 0,
                tests: []
            };

            for (const test of suite.tests) {
                try {
                    const testResult = test();
                    suiteResults.passed += testResult.passed;
                    suiteResults.failed += testResult.failed;
                    suiteResults.tests.push(...testResult.tests);
                } catch (error) {
                    suiteResults.tests.push({
                        name: 'Test execution',
                        status: 'failed',
                        message: `Test failed to execute: ${error.message}`
                    });
                    suiteResults.failed++;
                }
            }

            allResults[suiteId] = suiteResults;
        }

        return allResults;
    }

    generateTestReport() {
        const results = this.runAllTests();

        const summary = {
            totalSuites: Object.keys(results).length,
            totalTests: 0,
            totalPassed: 0,
            totalFailed: 0,
            suites: {}
        };

        for (const [suiteId, result] of Object.entries(results)) {
            summary.totalTests += result.tests.length;
            summary.totalPassed += result.passed;
            summary.totalFailed += result.failed;
            summary.suites[suiteId] = {
                passed: result.passed,
                failed: result.failed,
                total: result.tests.length
            };
        }

        console.group('🧪 DateTime Picker Test Report');
        console.log(`Total Test Suites: ${summary.totalSuites}`);
        console.log(`Total Tests: ${summary.totalTests}`);
        console.log(`Passed: ${summary.totalPassed}`);
        console.log(`Failed: ${summary.totalFailed}`);
        console.log(`Success Rate: ${(summary.totalPassed / summary.totalTests * 100).toFixed(1)}%`);

        for (const [suiteId, suite] of Object.entries(results)) {
            console.group(`${suite.name}`);
            console.log(`Passed: ${suite.passed}/${suite.tests.length}`);

            suite.tests.forEach(test => {
                const icon = test.status === 'passed' ? '✅' : '❌';
                console.log(`${icon} ${test.name}: ${test.message}`);
            });

            console.groupEnd();
        }

        console.groupEnd();

        return { summary, details: results };
    }
}

// 使用示例
const testFramework = new DateTimePickerTestFramework();
const testReport = testFramework.generateTestReport();
```

## 🔧 调试技巧

### 选择器状态监控
```javascript
function debugDateTimePicker() {
    const pickers = document.querySelectorAll('.o_datetime_picker');

    console.group('📅 DateTime Picker Debug');
    console.log(`Found ${pickers.length} picker instances`);

    pickers.forEach((picker, index) => {
        const component = picker.__owl__.component;

        console.group(`Picker ${index + 1}`);
        console.log('State:', component.state);
        console.log('Props:', component.props);
        console.log('Values:', component.values);
        console.log('Precision:', component.state.precision);
        console.log('Focus date:', component.state.focusDate?.toISODate());
        console.log('Time values:', component.state.timeValues);
        console.groupEnd();
    });

    console.groupEnd();
}

// 在控制台中调用
debugDateTimePicker();
```

### 精度级别检查
```javascript
function checkPrecisionLevels() {
    console.group('🔍 Precision Levels Check');

    for (const [level, info] of PRECISION_LEVELS.entries()) {
        console.group(`Level: ${level}`);
        console.log('Main title:', info.mainTitle);
        console.log('Step:', info.step);
        console.log('Has getTitle:', typeof info.getTitle === 'function');
        console.log('Has getItems:', typeof info.getItems === 'function');
        console.groupEnd();
    }

    console.groupEnd();
}

// 检查精度级别
checkPrecisionLevels();
```

## 📊 性能考虑

### 优化策略
1. **延迟渲染**: 只渲染可见的日期项目
2. **状态缓存**: 缓存计算结果避免重复计算
3. **事件委托**: 使用事件委托减少事件监听器
4. **虚拟滚动**: 对于大范围日期使用虚拟滚动

### 最佳实践
```javascript
// ✅ 好的做法：使用精确的日期范围
<DateTimePicker
    minDate={DateTime.local().minus({years: 1})}
    maxDate={DateTime.local().plus({years: 1})}/>

// ❌ 不好的做法：无限制的日期范围
<DateTimePicker /> // 可能导致性能问题

// ✅ 好的做法：合理的时间舍入
<DateTimePicker rounding={15}/> // 15分钟间隔

// ❌ 不好的做法：过细的时间精度
<DateTimePicker rounding={1}/> // 1分钟间隔可能过多

// ✅ 好的做法：条件渲染额外月份
additionalMonth={this.props.range && !this.env.isSmall}

// ❌ 不好的做法：总是显示额外月份
additionalMonth={true} // 在小屏幕上可能有问题
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解复杂日期时间组件的架构设计
- [ ] 掌握多精度级别的日历视图实现
- [ ] 理解日期范围选择的交互逻辑
- [ ] 能够处理时间系统和本地化
- [ ] 掌握企业级日期选择器的完整实现
- [ ] 了解选择器的测试和调试技术

## 🚀 下一步学习
学完日期时间选择器后，建议继续学习：
1. **选择器弹出框** (`@web/core/datetime/datetime_picker_popover.js`) - 学习弹出框实现
2. **选择器服务** (`@web/core/datetime/datetimepicker_service.js`) - 理解服务架构
3. **日期工具** (`@web/core/l10n/dates.js`) - 掌握日期处理工具

## 💡 重要提示
- 日期时间选择器是复杂的交互组件
- 精度级别系统提供了灵活的视图切换
- 范围选择需要复杂的状态管理
- 本地化支持确保了全球可用性
```

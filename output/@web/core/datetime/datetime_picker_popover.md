# @web/core/datetime/datetime_picker_popover.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/datetime/datetime_picker_popover.js`
- **原始路径**: `/web/static/src/core/datetime/datetime_picker_popover.js`
- **代码行数**: 44行
- **作用**: 提供日期时间选择器的弹出框包装组件，集成弹出框服务和快捷键支持

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 弹出框组件的设计模式和实现
- 组件包装和属性传递策略
- 快捷键集成和用户体验优化
- 服务集成的最佳实践
- 轻量级包装组件的架构设计

## 📚 核心概念

### 什么是日期时间选择器弹出框？
日期时间选择器弹出框是一个**包装组件**，主要功能：
- **弹出框集成**: 将日期时间选择器包装在弹出框中
- **快捷键支持**: 提供键盘快捷键支持
- **属性代理**: 透传选择器属性到内部组件
- **服务桥接**: 连接弹出框服务和选择器组件

### 核心架构组成
```javascript
// 主要组件
const DateTimePickerPopover = class extends Component {
    static components,      // 子组件定义
    static props,          // 属性定义
    static template,       // 模板定义
    setup()               // 组件设置
};

// 属性结构
const props = {
    close: "function",              // 关闭弹出框函数
    pickerProps: "object"           // 选择器属性对象
};

// 组件关系
const structure = {
    DateTimePickerPopover: {        // 弹出框包装
        DateTimePicker              // 内部选择器组件
    }
};
```

### 基本使用模式
```javascript
import { DateTimePickerPopover } from '@web/core/datetime/datetime_picker_popover';

// 通过弹出框服务使用
const popoverService = useService('popover');

function openDatePicker() {
    popoverService.add(
        DateTimePickerPopover,
        {
            pickerProps: {
                type: 'datetime',
                value: this.state.selectedDate,
                onSelect: (date) => {
                    this.state.selectedDate = date;
                    popoverService.close();
                }
            }
        },
        {
            target: this.inputRef.el,
            position: 'bottom-start'
        }
    );
}

// 在模板中触发
<input t-ref="input" 
       t-on-click="openDatePicker"
       t-att-value="state.selectedDate"/>
```

## 🔍 核心实现详解

### 1. 组件定义和结构

#### 类型定义
```javascript
/**
 * @typedef {import("./datetime_picker").DateTimePickerProps} DateTimePickerProps
 *
 * @typedef DateTimePickerPopoverProps
 * @property {() => void} close
 * @property {DateTimePickerProps} pickerProps
 */
```

**类型系统特点**：
- **类型导入**: 导入并重用DateTimePickerProps类型
- **简洁定义**: 只定义弹出框特有的属性
- **函数类型**: 明确定义close函数的签名
- **对象形状**: 使用shape验证pickerProps的结构

#### 组件属性定义
```javascript
static props = {
    close: Function, // Given by the Popover service
    pickerProps: { type: Object, shape: DateTimePicker.props },
};
```

**属性设计原则**：
- **服务集成**: close函数由弹出框服务提供
- **形状验证**: 使用shape确保pickerProps符合选择器要求
- **类型安全**: 严格的类型检查确保属性正确性
- **文档注释**: 清晰说明属性来源和用途

### 2. 组件包装策略

#### 子组件声明
```javascript
static components = { DateTimePicker };
```

**包装特点**：
- **单一职责**: 只包装一个核心组件
- **透明代理**: 不修改内部组件的行为
- **组合模式**: 通过组合而非继承实现功能
- **松耦合**: 弹出框和选择器保持独立

#### 属性传递机制
```javascript
// 在模板中传递属性
<DateTimePicker t-props="props.pickerProps"/>
```

**传递策略**：
- **完整传递**: 将所有选择器属性完整传递
- **无损代理**: 不丢失任何属性信息
- **动态绑定**: 支持属性的动态更新
- **类型保持**: 保持原始属性的类型和结构

### 3. 快捷键集成

#### 快捷键设置
```javascript
setup() {
    useHotkey("enter", () => this.props.close());
}
```

**快捷键特点**：
- **简单绑定**: 使用useHotkey钩子绑定快捷键
- **用户友好**: Enter键关闭弹出框符合用户习惯
- **自动清理**: 组件销毁时自动清理快捷键绑定
- **无冲突**: 不与选择器内部快捷键冲突

#### 用户体验优化
```javascript
// 快捷键的用户体验考虑
const keyboardInteractions = {
    "enter": "关闭弹出框",           // 确认选择
    "escape": "取消选择",           // 由弹出框服务处理
    "tab": "焦点导航",             // 浏览器默认行为
    "arrow": "日期导航"            // 选择器内部处理
};
```

**体验设计**：
- **直观操作**: 符合用户对弹出框的操作预期
- **键盘友好**: 完整的键盘操作支持
- **无障碍**: 支持屏幕阅读器等辅助技术
- **一致性**: 与其他弹出框组件保持一致

### 4. 条件逻辑处理

#### 日期时间范围检测
```javascript
get isDateTimeRange() {
    return (
        this.props.pickerProps.type === "datetime" ||
        Array.isArray(this.props.pickerProps.value)
    );
}
```

**检测逻辑分析**：
- **类型检测**: 检查选择器类型是否为datetime
- **值检测**: 检查值是否为数组（范围选择）
- **组合条件**: 两个条件任一满足即为范围选择
- **模板使用**: 为模板提供条件渲染依据

#### 条件渲染应用
```javascript
// 在模板中使用条件
<div t-if="isDateTimeRange" class="datetime-range-footer">
    <span>Select date and time range</span>
</div>
<div t-else="" class="datetime-single-footer">
    <span>Select date and time</span>
</div>
```

**渲染策略**：
- **动态内容**: 根据选择器类型显示不同内容
- **用户指导**: 提供清晰的操作指导
- **视觉区分**: 通过样式区分不同模式
- **响应式**: 自动响应属性变化

## 🎨 实际应用场景

### 1. 高级弹出框管理系统
```javascript
class AdvancedPopoverManager {
    constructor() {
        this.popoverInstances = new Map();
        this.popoverConfigs = new Map();
        this.popoverMiddleware = [];
        this.setupDefaultConfigurations();
    }
    
    setupDefaultConfigurations() {
        // 标准日期选择器弹出框
        this.addConfiguration('standard', {
            name: 'Standard Date Picker',
            component: DateTimePickerPopover,
            defaultProps: {
                pickerProps: {
                    type: 'date',
                    minPrecision: 'days',
                    maxPrecision: 'months'
                }
            },
            position: 'bottom-start',
            middleware: ['autoPosition', 'closeOnClickOutside']
        });
        
        // 时间范围选择器弹出框
        this.addConfiguration('timerange', {
            name: 'Time Range Picker',
            component: DateTimePickerPopover,
            defaultProps: {
                pickerProps: {
                    type: 'datetime',
                    range: true,
                    rounding: 15
                }
            },
            position: 'bottom-center',
            middleware: ['autoPosition', 'preventOverflow'],
            size: 'large'
        });
        
        // 快速日期选择器弹出框
        this.addConfiguration('quick', {
            name: 'Quick Date Picker',
            component: DateTimePickerPopover,
            defaultProps: {
                pickerProps: {
                    type: 'date',
                    minPrecision: 'days',
                    maxPrecision: 'days'
                }
            },
            position: 'bottom-start',
            middleware: ['autoPosition'],
            size: 'compact',
            quickActions: true
        });
        
        // 业务日期选择器弹出框
        this.addConfiguration('business', {
            name: 'Business Date Picker',
            component: DateTimePickerPopover,
            defaultProps: {
                pickerProps: {
                    type: 'date',
                    isDateValid: (date) => {
                        const day = date.weekday;
                        return day >= 1 && day <= 5;
                    }
                }
            },
            position: 'bottom-start',
            middleware: ['autoPosition', 'businessDayValidation']
        });
    }
    
    addConfiguration(name, config) {
        this.popoverConfigs.set(name, config);
    }
    
    createEnhancedPopover(configName) {
        const config = this.popoverConfigs.get(configName);
        if (!config) {
            throw new Error(`Unknown popover configuration: ${configName}`);
        }
        
        return class EnhancedDateTimePickerPopover extends config.component {
            static template = `web.Enhanced${configName.charAt(0).toUpperCase() + configName.slice(1)}Popover`;
            
            setup() {
                super.setup();
                
                // 应用中间件
                config.middleware?.forEach(middlewareName => {
                    this.applyMiddleware(middlewareName);
                });
                
                // 添加快速操作
                if (config.quickActions) {
                    this.setupQuickActions();
                }
                
                // 添加尺寸类
                if (config.size) {
                    this.el?.classList.add(`popover-${config.size}`);
                }
            }
            
            applyMiddleware(middlewareName) {
                const middleware = this.getMiddleware(middlewareName);
                if (middleware) {
                    middleware.apply(this);
                }
            }
            
            getMiddleware(name) {
                const middlewareMap = {
                    autoPosition: () => {
                        // 自动定位中间件
                        this.autoPosition();
                    },
                    closeOnClickOutside: () => {
                        // 点击外部关闭中间件
                        this.setupClickOutsideClose();
                    },
                    preventOverflow: () => {
                        // 防止溢出中间件
                        this.preventOverflow();
                    },
                    businessDayValidation: () => {
                        // 业务日验证中间件
                        this.setupBusinessDayValidation();
                    }
                };
                
                return middlewareMap[name];
            }
            
            setupQuickActions() {
                // 添加快速操作按钮
                this.quickActions = [
                    {
                        label: 'Today',
                        action: () => this.selectToday()
                    },
                    {
                        label: 'Tomorrow',
                        action: () => this.selectTomorrow()
                    },
                    {
                        label: 'Next Week',
                        action: () => this.selectNextWeek()
                    },
                    {
                        label: 'Clear',
                        action: () => this.clearSelection()
                    }
                ];
            }
            
            selectToday() {
                const today = DateTime.local();
                this.selectDate(today);
            }
            
            selectTomorrow() {
                const tomorrow = DateTime.local().plus({ days: 1 });
                this.selectDate(tomorrow);
            }
            
            selectNextWeek() {
                const nextWeek = DateTime.local().plus({ weeks: 1 });
                this.selectDate(nextWeek);
            }
            
            clearSelection() {
                this.selectDate(null);
            }
            
            selectDate(date) {
                if (this.props.pickerProps.onSelect) {
                    this.props.pickerProps.onSelect(date);
                }
                this.props.close();
            }
            
            autoPosition() {
                // 实现自动定位逻辑
                const rect = this.el.getBoundingClientRect();
                const viewport = {
                    width: window.innerWidth,
                    height: window.innerHeight
                };
                
                // 检查是否需要调整位置
                if (rect.right > viewport.width) {
                    this.el.style.left = 'auto';
                    this.el.style.right = '0';
                }
                
                if (rect.bottom > viewport.height) {
                    this.el.style.top = 'auto';
                    this.el.style.bottom = '100%';
                }
            }
            
            setupClickOutsideClose() {
                const handleClickOutside = (event) => {
                    if (!this.el.contains(event.target)) {
                        this.props.close();
                    }
                };
                
                document.addEventListener('click', handleClickOutside);
                
                this.onWillUnmount(() => {
                    document.removeEventListener('click', handleClickOutside);
                });
            }
            
            preventOverflow() {
                // 防止弹出框溢出视口
                const observer = new ResizeObserver(() => {
                    this.adjustPosition();
                });
                
                observer.observe(this.el);
                
                this.onWillUnmount(() => {
                    observer.disconnect();
                });
            }
            
            adjustPosition() {
                const rect = this.el.getBoundingClientRect();
                const margin = 10;
                
                if (rect.left < margin) {
                    this.el.style.left = `${margin}px`;
                }
                
                if (rect.right > window.innerWidth - margin) {
                    this.el.style.left = `${window.innerWidth - rect.width - margin}px`;
                }
                
                if (rect.top < margin) {
                    this.el.style.top = `${margin}px`;
                }
                
                if (rect.bottom > window.innerHeight - margin) {
                    this.el.style.top = `${window.innerHeight - rect.height - margin}px`;
                }
            }
            
            setupBusinessDayValidation() {
                // 业务日验证设置
                const originalOnSelect = this.props.pickerProps.onSelect;
                
                this.props.pickerProps.onSelect = (date) => {
                    if (this.isBusinessDay(date)) {
                        originalOnSelect?.(date);
                    } else {
                        this.showBusinessDayWarning();
                    }
                };
            }
            
            isBusinessDay(date) {
                if (!date) return true;
                if (Array.isArray(date)) {
                    return date.every(d => d && d.weekday >= 1 && d.weekday <= 5);
                }
                return date.weekday >= 1 && date.weekday <= 5;
            }
            
            showBusinessDayWarning() {
                // 显示业务日警告
                console.warn('Please select a business day (Monday to Friday)');
                // 可以集成通知系统显示用户友好的警告
            }
        };
    }
    
    openPopover(configName, target, customProps = {}) {
        const config = this.popoverConfigs.get(configName);
        if (!config) {
            throw new Error(`Unknown popover configuration: ${configName}`);
        }
        
        const EnhancedPopover = this.createEnhancedPopover(configName);
        const popoverService = this.getPopoverService();
        
        const mergedProps = {
            ...config.defaultProps,
            ...customProps
        };
        
        const popoverId = popoverService.add(
            EnhancedPopover,
            mergedProps,
            {
                target,
                position: config.position || 'bottom-start'
            }
        );
        
        this.popoverInstances.set(popoverId, {
            config: configName,
            target,
            props: mergedProps,
            createdAt: Date.now()
        });
        
        return popoverId;
    }
    
    closePopover(popoverId) {
        const popoverService = this.getPopoverService();
        popoverService.close(popoverId);
        this.popoverInstances.delete(popoverId);
    }
    
    closeAllPopovers() {
        const popoverService = this.getPopoverService();
        for (const popoverId of this.popoverInstances.keys()) {
            popoverService.close(popoverId);
        }
        this.popoverInstances.clear();
    }
    
    getActivePopovers() {
        return Array.from(this.popoverInstances.entries()).map(([id, instance]) => ({
            id,
            ...instance
        }));
    }
    
    getPopoverService() {
        // 获取弹出框服务实例
        return odoo.__DEBUG__.services.popover;
    }
}

// 使用示例
const popoverManager = new AdvancedPopoverManager();

// 在组件中使用
class DateInputComponent extends Component {
    static template = xml`
        <div class="date-input-container">
            <input t-ref="dateInput" 
                   type="text" 
                   t-att-value="state.selectedDate"
                   t-on-click="openDatePicker"
                   placeholder="Select date"/>
            <button t-on-click="openQuickPicker" class="quick-picker-btn">
                Quick
            </button>
        </div>
    `;
    
    setup() {
        this.state = useState({
            selectedDate: null
        });
        
        this.popoverManager = popoverManager;
    }
    
    openDatePicker() {
        this.popoverManager.openPopover('standard', this.refs.dateInput, {
            pickerProps: {
                value: this.state.selectedDate,
                onSelect: (date) => {
                    this.state.selectedDate = date?.toISODate();
                }
            }
        });
    }
    
    openQuickPicker() {
        this.popoverManager.openPopover('quick', this.refs.dateInput, {
            pickerProps: {
                value: this.state.selectedDate,
                onSelect: (date) => {
                    this.state.selectedDate = date?.toISODate();
                }
            }
        });
    }
}
```

### 2. 弹出框测试工具
```javascript
class PopoverTestingFramework {
    constructor() {
        this.testResults = [];
        this.mockServices = this.createMockServices();
        this.testScenarios = new Map();
        this.setupTestScenarios();
    }

    createMockServices() {
        return {
            popover: {
                add: jest.fn((component, props, options) => {
                    return `popover_${Date.now()}`;
                }),
                close: jest.fn(),
                remove: jest.fn()
            },
            hotkey: {
                add: jest.fn(() => jest.fn()),
                remove: jest.fn()
            }
        };
    }

    setupTestScenarios() {
        this.testScenarios.set('component_structure', {
            name: 'Component Structure Test',
            description: 'Test component definition and structure',
            test: () => this.testComponentStructure()
        });

        this.testScenarios.set('props_validation', {
            name: 'Props Validation Test',
            description: 'Test property validation and types',
            test: () => this.testPropsValidation()
        });

        this.testScenarios.set('hotkey_integration', {
            name: 'Hotkey Integration Test',
            description: 'Test hotkey binding and functionality',
            test: () => this.testHotkeyIntegration()
        });

        this.testScenarios.set('popover_service', {
            name: 'Popover Service Test',
            description: 'Test integration with popover service',
            test: () => this.testPopoverService()
        });

        this.testScenarios.set('conditional_logic', {
            name: 'Conditional Logic Test',
            description: 'Test conditional rendering logic',
            test: () => this.testConditionalLogic()
        });
    }

    testComponentStructure() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            // 测试组件定义
            if (DateTimePickerPopover && typeof DateTimePickerPopover === 'function') {
                results.tests.push({
                    name: 'Component is defined',
                    status: 'passed',
                    message: 'DateTimePickerPopover component is properly defined'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Component is defined',
                    status: 'failed',
                    message: 'DateTimePickerPopover component is not defined'
                });
                results.failed++;
            }

            // 测试子组件
            if (DateTimePickerPopover.components && DateTimePickerPopover.components.DateTimePicker) {
                results.tests.push({
                    name: 'Child components defined',
                    status: 'passed',
                    message: 'DateTimePicker child component is defined'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Child components defined',
                    status: 'failed',
                    message: 'DateTimePicker child component is missing'
                });
                results.failed++;
            }

            // 测试模板
            if (DateTimePickerPopover.template === "web.DateTimePickerPopover") {
                results.tests.push({
                    name: 'Template is defined',
                    status: 'passed',
                    message: 'Component template is correctly defined'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Template is defined',
                    status: 'failed',
                    message: 'Component template is not defined'
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Component structure',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    testPropsValidation() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            // 测试属性定义
            const props = DateTimePickerPopover.props;

            if (props && props.close && props.close === Function) {
                results.tests.push({
                    name: 'Close prop is defined',
                    status: 'passed',
                    message: 'Close function prop is properly defined'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Close prop is defined',
                    status: 'failed',
                    message: 'Close function prop is missing or incorrect'
                });
                results.failed++;
            }

            if (props && props.pickerProps && props.pickerProps.type === Object) {
                results.tests.push({
                    name: 'PickerProps prop is defined',
                    status: 'passed',
                    message: 'PickerProps object prop is properly defined'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'PickerProps prop is defined',
                    status: 'failed',
                    message: 'PickerProps object prop is missing or incorrect'
                });
                results.failed++;
            }

            // 测试属性形状验证
            if (props.pickerProps.shape) {
                results.tests.push({
                    name: 'PickerProps shape validation',
                    status: 'passed',
                    message: 'PickerProps has shape validation'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'PickerProps shape validation',
                    status: 'failed',
                    message: 'PickerProps lacks shape validation'
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Props validation',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    testHotkeyIntegration() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            // 模拟组件实例
            const mockComponent = this.createMockPopover({
                close: jest.fn()
            });

            // 模拟快捷键触发
            const hotkeyCallback = this.mockServices.hotkey.add.mock.calls[0]?.[1];

            if (hotkeyCallback && typeof hotkeyCallback === 'function') {
                results.tests.push({
                    name: 'Hotkey callback is registered',
                    status: 'passed',
                    message: 'Enter hotkey callback is properly registered'
                });
                results.passed++;

                // 测试快捷键执行
                hotkeyCallback();

                if (mockComponent.props.close.mock.calls.length > 0) {
                    results.tests.push({
                        name: 'Hotkey triggers close',
                        status: 'passed',
                        message: 'Enter hotkey correctly triggers close function'
                    });
                    results.passed++;
                } else {
                    results.tests.push({
                        name: 'Hotkey triggers close',
                        status: 'failed',
                        message: 'Enter hotkey does not trigger close function'
                    });
                    results.failed++;
                }
            } else {
                results.tests.push({
                    name: 'Hotkey callback is registered',
                    status: 'failed',
                    message: 'Enter hotkey callback is not registered'
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Hotkey integration',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    testPopoverService() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            // 测试弹出框服务集成
            const popoverService = this.mockServices.popover;

            // 模拟弹出框创建
            const popoverId = popoverService.add(
                DateTimePickerPopover,
                {
                    close: jest.fn(),
                    pickerProps: {
                        type: 'date',
                        value: null
                    }
                },
                {
                    target: document.createElement('input'),
                    position: 'bottom-start'
                }
            );

            if (popoverId && typeof popoverId === 'string') {
                results.tests.push({
                    name: 'Popover service integration',
                    status: 'passed',
                    message: 'Popover service correctly creates popover instance'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Popover service integration',
                    status: 'failed',
                    message: 'Popover service failed to create popover instance'
                });
                results.failed++;
            }

            // 测试弹出框关闭
            popoverService.close(popoverId);

            if (popoverService.close.mock.calls.length > 0) {
                results.tests.push({
                    name: 'Popover service close',
                    status: 'passed',
                    message: 'Popover service close method is called'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Popover service close',
                    status: 'failed',
                    message: 'Popover service close method was not called'
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Popover service',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    testConditionalLogic() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            // 测试日期时间范围检测
            const mockComponent1 = this.createMockPopover({
                pickerProps: {
                    type: 'datetime',
                    value: null
                }
            });

            if (mockComponent1.isDateTimeRange) {
                results.tests.push({
                    name: 'DateTime type detection',
                    status: 'passed',
                    message: 'Correctly detects datetime type as range'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'DateTime type detection',
                    status: 'failed',
                    message: 'Failed to detect datetime type as range'
                });
                results.failed++;
            }

            // 测试数组值检测
            const mockComponent2 = this.createMockPopover({
                pickerProps: {
                    type: 'date',
                    value: [null, null]
                }
            });

            if (mockComponent2.isDateTimeRange) {
                results.tests.push({
                    name: 'Array value detection',
                    status: 'passed',
                    message: 'Correctly detects array value as range'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Array value detection',
                    status: 'failed',
                    message: 'Failed to detect array value as range'
                });
                results.failed++;
            }

            // 测试单值检测
            const mockComponent3 = this.createMockPopover({
                pickerProps: {
                    type: 'date',
                    value: null
                }
            });

            if (!mockComponent3.isDateTimeRange) {
                results.tests.push({
                    name: 'Single value detection',
                    status: 'passed',
                    message: 'Correctly detects single value as non-range'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Single value detection',
                    status: 'failed',
                    message: 'Incorrectly detects single value as range'
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Conditional logic',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    createMockPopover(props = {}) {
        const defaultProps = {
            close: jest.fn(),
            pickerProps: {
                type: 'date',
                value: null,
                ...props.pickerProps
            },
            ...props
        };

        // 创建模拟的弹出框实例
        const mockPopover = {
            props: defaultProps,

            get isDateTimeRange() {
                return (
                    this.props.pickerProps.type === "datetime" ||
                    Array.isArray(this.props.pickerProps.value)
                );
            },

            setup: function() {
                // 模拟快捷键绑定
                this.mockServices.hotkey.add("enter", () => this.props.close());
            }
        };

        // 模拟setup调用
        mockPopover.setup();

        return mockPopover;
    }

    runAllTests() {
        const allResults = {};

        for (const [scenarioId, scenario] of this.testScenarios.entries()) {
            console.log(`Running ${scenario.name}...`);
            allResults[scenarioId] = scenario.test();
        }

        return allResults;
    }

    generateTestReport() {
        const results = this.runAllTests();

        const summary = {
            totalScenarios: Object.keys(results).length,
            totalTests: 0,
            totalPassed: 0,
            totalFailed: 0,
            scenarios: {}
        };

        for (const [scenarioId, result] of Object.entries(results)) {
            summary.totalTests += result.tests.length;
            summary.totalPassed += result.passed;
            summary.totalFailed += result.failed;
            summary.scenarios[scenarioId] = {
                passed: result.passed,
                failed: result.failed,
                total: result.tests.length
            };
        }

        console.group('🧪 DateTime Picker Popover Test Report');
        console.log(`Total Scenarios: ${summary.totalScenarios}`);
        console.log(`Total Tests: ${summary.totalTests}`);
        console.log(`Passed: ${summary.totalPassed}`);
        console.log(`Failed: ${summary.totalFailed}`);
        console.log(`Success Rate: ${(summary.totalPassed / summary.totalTests * 100).toFixed(1)}%`);

        for (const [scenarioId, scenario] of this.testScenarios.entries()) {
            const result = results[scenarioId];
            console.group(`${scenario.name}`);
            console.log(`Passed: ${result.passed}/${result.tests.length}`);

            result.tests.forEach(test => {
                const icon = test.status === 'passed' ? '✅' : '❌';
                console.log(`${icon} ${test.name}: ${test.message}`);
            });

            console.groupEnd();
        }

        console.groupEnd();

        return { summary, details: results };
    }
}

// 使用示例
const testFramework = new PopoverTestingFramework();
const testReport = testFramework.generateTestReport();
```

## 🔧 调试技巧

### 弹出框状态监控
```javascript
function debugDateTimePickerPopover() {
    const popovers = document.querySelectorAll('.o_popover');

    console.group('📅 DateTime Picker Popover Debug');
    console.log(`Found ${popovers.length} popover instances`);

    popovers.forEach((popover, index) => {
        const component = popover.__owl__?.component;

        if (component && component.constructor.name.includes('DateTimePickerPopover')) {
            console.group(`Popover ${index + 1}`);
            console.log('Props:', component.props);
            console.log('Picker props:', component.props.pickerProps);
            console.log('Is range:', component.isDateTimeRange);
            console.log('Close function:', typeof component.props.close);
            console.groupEnd();
        }
    });

    console.groupEnd();
}

// 在控制台中调用
debugDateTimePickerPopover();
```

### 快捷键绑定检查
```javascript
function checkPopoverHotkeys() {
    const hotkeyService = odoo.__DEBUG__.services.hotkey;

    console.group('⌨️ Popover Hotkeys Check');

    // 检查已注册的快捷键
    if (hotkeyService && hotkeyService.registrations) {
        const popoverHotkeys = hotkeyService.registrations.filter(reg =>
            reg.hotkey === 'enter' && reg.callback.toString().includes('close')
        );

        console.log(`Found ${popoverHotkeys.length} popover enter hotkeys`);
        popoverHotkeys.forEach((hotkey, index) => {
            console.log(`Hotkey ${index + 1}:`, hotkey);
        });
    }

    console.groupEnd();
}

// 检查快捷键绑定
checkPopoverHotkeys();
```

## 📊 性能考虑

### 优化策略
1. **轻量级包装**: 最小化包装组件的开销
2. **属性透传**: 高效的属性传递机制
3. **快捷键管理**: 自动清理快捷键绑定
4. **条件渲染**: 智能的条件渲染逻辑

### 最佳实践
```javascript
// ✅ 好的做法：使用简洁的属性传递
<DateTimePicker t-props="props.pickerProps"/>

// ❌ 不好的做法：手动传递每个属性
<DateTimePicker
    type="props.pickerProps.type"
    value="props.pickerProps.value"
    onSelect="props.pickerProps.onSelect"/>

// ✅ 好的做法：使用计算属性
get isDateTimeRange() {
    return this.props.pickerProps.type === "datetime" ||
           Array.isArray(this.props.pickerProps.value);
}

// ❌ 不好的做法：在模板中计算
<div t-if="props.pickerProps.type === 'datetime' || Array.isArray(props.pickerProps.value)">

// ✅ 好的做法：自动清理快捷键
useHotkey("enter", () => this.props.close());

// ❌ 不好的做法：手动管理快捷键
setup() {
    this.hotkeyRemove = hotkeyService.add("enter", () => this.props.close());
}
onWillUnmount() {
    this.hotkeyRemove();
}
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解弹出框组件的设计模式和实现
- [ ] 掌握组件包装和属性传递策略
- [ ] 理解快捷键集成和用户体验优化
- [ ] 能够实现服务集成的最佳实践
- [ ] 掌握轻量级包装组件的架构设计
- [ ] 了解弹出框的测试和调试技术

## 🚀 下一步学习
学完日期时间选择器弹出框后，建议继续学习：
1. **选择器服务** (`@web/core/datetime/datetimepicker_service.js`) - 学习服务架构
2. **弹出框服务** (`@web/core/popover/`) - 理解弹出框系统
3. **快捷键服务** (`@web/core/hotkeys/`) - 掌握快捷键管理

## 💡 重要提示
- 弹出框组件是轻量级的包装组件
- 属性透传确保了功能的完整性
- 快捷键集成提供了良好的用户体验
- 服务集成是现代组件架构的关键
```

# @web/core/datetime/datetimepicker_service.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/datetime/datetimepicker_service.js`
- **原始路径**: `/web/static/src/core/datetime/datetimepicker_service.js`
- **代码行数**: 499行
- **作用**: 提供日期时间选择器服务，管理输入框集成、弹出框控制和值同步

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 企业级服务架构的设计和实现
- 复杂状态管理和同步机制
- 输入框与选择器的双向绑定
- 弹出框生命周期管理
- 事件处理和用户交互优化

## 📚 核心概念

### 什么是日期时间选择器服务？
日期时间选择器服务是一个**核心服务**，主要功能：
- **输入框集成**: 将日期时间选择器与HTML输入框无缝集成
- **状态同步**: 管理选择器和输入框之间的双向数据同步
- **弹出框控制**: 控制选择器弹出框的显示和隐藏
- **事件协调**: 协调键盘、鼠标和焦点事件

### 核心架构组成
```javascript
// 服务定义
const datetimePickerService = {
    dependencies: ["popover"],     // 依赖弹出框服务
    start(env, services) {         // 服务启动函数
        return {
            create()               // 创建选择器实例
        };
    }
};

// 钩子参数类型
const DateTimePickerHookParams = {
    format: "string",              // 日期格式
    onChange: "function",          // 值变化回调
    onApply: "function",          // 值应用回调
    pickerProps: "object",        // 选择器属性
    target: "string | Element",   // 目标元素
    createPopover: "function"     // 弹出框创建函数
};

// 返回值结构
const hookResult = {
    state: "object",              // 响应式状态
    open: "function",             // 打开选择器
    computeBasePickerProps: "function",  // 计算属性
    focusIfNeeded: "function",    // 条件聚焦
    enable: "function"            // 启用功能
};
```

### 基本使用模式
```javascript
import { useService } from '@web/core/utils/hooks';

class DateInputComponent extends Component {
    setup() {
        this.datetimePickerService = useService('datetime_picker');
        
        // 创建日期时间选择器实例
        this.dateTimePicker = this.datetimePickerService.create({
            format: 'YYYY-MM-DD',
            pickerProps: {
                type: 'date',
                value: this.props.value,
                onSelect: (value) => {
                    this.props.onValueChange(value);
                }
            },
            onChange: (value) => {
                console.log('Value changing:', value);
            },
            onApply: (value) => {
                console.log('Value applied:', value);
                this.saveValue(value);
            }
        }, () => [this.inputRef.el]);
        
        // 生命周期集成
        onWillRender(this.dateTimePicker.computeBasePickerProps);
        useEffect(this.dateTimePicker.enable, () => [this.inputRef.el]);
        onPatched(this.dateTimePicker.focusIfNeeded);
    }
}
```

## 🔍 核心实现详解

### 1. 服务架构设计

#### 服务定义和依赖
```javascript
const datetimePickerService = {
    dependencies: ["popover"],
    start(env, { popover: popoverService }) {
        return {
            create: (hookParams, getInputs = () => [hookParams.target, null]) => {
                // 服务实现
            }
        };
    }
};
```

**架构特点**：
- **依赖注入**: 明确声明对弹出框服务的依赖
- **工厂模式**: create方法作为工厂函数创建实例
- **参数灵活**: 支持自定义输入框获取函数
- **环境访问**: 通过env参数访问全局环境

#### 弹出框集成策略
```javascript
const createPopover =
    hookParams.createPopover ??
    ((...args) => makePopover(popoverService.add, ...args));

const popover = createPopover(DateTimePickerPopover, {
    onClose: () => {
        if (!allowOnClose) return;
        updateValueFromInputs();
        apply();
        setFocusClass(null);
        if (restoreTargetMargin) {
            restoreTargetMargin();
            restoreTargetMargin = null;
        }
    }
});
```

**集成特点**：
- **可定制**: 支持自定义弹出框创建函数
- **默认实现**: 提供基于弹出框服务的默认实现
- **生命周期**: 完整的弹出框生命周期管理
- **清理机制**: 自动清理资源和恢复状态

### 2. 状态管理系统

#### 响应式状态创建
```javascript
const rawPickerProps = {
    ...DateTimePicker.defaultProps,
    onSelect: (value) => {
        value &&= markRaw(value);
        updateValue(value);
        if (!pickerProps.range && pickerProps.type === "date") {
            saveAndClose();
        }
    },
    ...markValuesRaw(hookParams.pickerProps),
};

const pickerProps = reactive(rawPickerProps, () => {
    // 响应式更新逻辑
    const currentIsRange = pickerProps.range;
    if (popover.isOpen && lastIsRange !== currentIsRange) {
        allowOnClose = false;
        popover.open(getPopoverTarget(), { pickerProps });
        allowOnClose = true;
    }
    lastIsRange = currentIsRange;
    
    // 更新输入框
    for (const [el, value] of zip(getInputs(), ensureArray(pickerProps.value), true)) {
        if (el) {
            updateInput(el, value);
        }
    }
    
    shouldFocus = true;
});
```

**状态管理特点**：
- **响应式**: 使用reactive创建响应式状态对象
- **默认值**: 继承选择器组件的默认属性
- **回调集成**: 集成选择器的onSelect回调
- **自动同步**: 状态变化时自动同步输入框

#### 属性比较和更新
```javascript
const arePropsEqual = (obj1, obj2) =>
    shallowEqual(obj1, obj2, (a, b) => areDatesEqual(a, b) || shallowEqual(a, b));

const computeBasePickerProps = () => {
    const nextInitialProps = markValuesRaw(hookParams.pickerProps);
    const propsCopy = deepCopy(nextInitialProps);

    if (lastInitialProps && arePropsEqual(lastInitialProps, propsCopy)) {
        return;
    }

    lastInitialProps = propsCopy;
    inputsChanged = ensureArray(lastInitialProps.value).map(() => false);

    for (const [key, value] of Object.entries(nextInitialProps)) {
        if (pickerProps[key] !== value && !areDatesEqual(pickerProps[key], value)) {
            pickerProps[key] = value;
        }
    }
};
```

**更新策略**：
- **智能比较**: 使用自定义比较函数处理日期对象
- **避免重复**: 只在属性真正变化时更新
- **深拷贝**: 使用深拷贝避免引用问题
- **增量更新**: 只更新变化的属性

### 3. 输入框事件处理

#### 事件监听器注册
```javascript
const enable = () => {
    let editableInputs = 0;
    for (const [el, value] of zip(getInputs(), ensureArray(pickerProps.value), true)) {
        updateInput(el, value);
        if (el && !el.disabled && !el.readOnly && !listenedElements.has(el)) {
            listenedElements.add(el);
            el.addEventListener("change", onInputChange);
            el.addEventListener("click", onInputClick);
            el.addEventListener("focus", onInputFocus);
            el.addEventListener("keydown", onInputKeydown);
            editableInputs++;
        }
    }
    
    // 日历图标处理
    const calendarIconGroupEl = getInput(0)?.parentElement.querySelector(".o_input_group_date_icon");
    if (calendarIconGroupEl) {
        calendarIconGroupEl.classList.add("cursor-pointer");
        calendarIconGroupEl.addEventListener("click", () => openPicker(0));
    }
    
    if (!editableInputs && popover.isOpen) {
        saveAndClose();
    }
    return () => {};
};
```

**事件处理特点**：
- **条件注册**: 只为可编辑的输入框注册事件
- **重复检查**: 使用WeakSet避免重复注册
- **图标支持**: 支持日历图标点击
- **自动清理**: 在没有可编辑输入框时自动关闭

#### 键盘事件处理
```javascript
const onInputKeydown = (ev) => {
    if (ev.key == "Enter" && ev.ctrlKey) {
        ev.preventDefault();
        updateValueFromInputs();
        return openPicker(ev.target === getInput(1) ? 1 : 0);
    }
    switch (ev.key) {
        case "Enter":
        case "Escape": {
            return saveAndClose();
        }
        case "Tab": {
            if (
                !getInput(0) ||
                !getInput(1) ||
                ev.target !== getInput(ev.shiftKey ? 1 : 0)
            ) {
                return saveAndClose();
            }
        }
    }
};
```

**键盘交互**：
- **Ctrl+Enter**: 强制打开选择器
- **Enter/Escape**: 保存并关闭
- **Tab**: 智能的Tab导航处理
- **Shift+Tab**: 反向Tab导航支持

### 4. 值转换和验证

#### 安全转换机制
```javascript
const safeConvert = (operation, value) => {
    const { type } = pickerProps;
    const convertFn = (operation === "format" ? formatters : parsers)[type];
    const options = { tz: pickerProps.tz, format: hookParams.format };
    if (operation === "format") {
        options.showSeconds = hookParams.showSeconds ?? true;
        options.condensed = hookParams.condensed || false;
    }
    try {
        return [convertFn(value, options), null];
    } catch (error) {
        if (error?.name === "ConversionError") {
            return [null, error];
        } else {
            throw error;
        }
    }
};
```

**转换特点**：
- **双向转换**: 支持格式化和解析两个方向
- **错误处理**: 安全的错误捕获和处理
- **选项配置**: 支持时区、格式等选项
- **类型区分**: 根据选择器类型选择转换函数

#### 输入框同步机制
```javascript
const updateValueFromInputs = () => {
    const values = zipWith(
        getInputs(),
        ensureArray(pickerProps.value),
        (el, currentValue) => {
            if (!el) {
                return currentValue;
            }
            const [parsedValue, error] = safeConvert("parse", el.value);
            if (error) {
                updateInput(el, currentValue);
                return currentValue;
            } else {
                return parsedValue;
            }
        }
    );
    updateValue(values.length === 2 ? values : values[0]);
};
```

**同步策略**：
- **容错处理**: 解析失败时恢复原值
- **数组处理**: 智能处理单值和范围值
- **输入验证**: 实时验证输入的有效性
- **状态一致**: 确保输入框和状态的一致性

## 🎨 实际应用场景

### 1. 企业级日期时间服务扩展
```javascript
class EnterpriseDateTime PickerService {
    constructor() {
        this.baseService = null;
        this.instances = new Map();
        this.globalConfig = {
            defaultFormat: 'YYYY-MM-DD',
            defaultTimezone: 'UTC',
            validationRules: new Map(),
            formatters: new Map(),
            parsers: new Map()
        };
        this.middleware = [];
        this.analytics = {
            usage: new Map(),
            errors: new Map(),
            performance: new Map()
        };
        this.setupDefaultConfiguration();
    }
    
    setupDefaultConfiguration() {
        // 业务日期验证规则
        this.addValidationRule('businessDay', (date) => {
            if (!date) return true;
            const day = date.weekday;
            return day >= 1 && day <= 5;
        });
        
        // 财务期间验证规则
        this.addValidationRule('fiscalPeriod', (date) => {
            if (!date) return true;
            // 财务年度从4月1日开始
            const fiscalYearStart = date.set({ month: 4, day: 1 });
            if (date.month < 4) {
                fiscalYearStart = fiscalYearStart.minus({ years: 1 });
            }
            return date >= fiscalYearStart;
        });
        
        // 自定义格式化器
        this.addFormatter('businessDate', (date, options) => {
            if (!date) return '';
            const formatted = date.toFormat(options.format || 'yyyy-MM-dd');
            const isBusinessDay = date.weekday >= 1 && date.weekday <= 5;
            return isBusinessDay ? formatted : `${formatted} (Weekend)`;
        });
        
        // 自定义解析器
        this.addParser('flexibleDate', (value, options) => {
            if (!value) return null;
            
            // 支持多种日期格式
            const formats = [
                'yyyy-MM-dd',
                'MM/dd/yyyy',
                'dd-MM-yyyy',
                'yyyy/MM/dd'
            ];
            
            for (const format of formats) {
                try {
                    const parsed = DateTime.fromFormat(value, format);
                    if (parsed.isValid) return parsed;
                } catch (error) {
                    continue;
                }
            }
            
            throw new Error(`Unable to parse date: ${value}`);
        });
    }
    
    initialize(baseService) {
        this.baseService = baseService;
        
        // 包装原始create方法
        const originalCreate = baseService.create;
        baseService.create = (hookParams, getInputs) => {
            return this.createEnhanced(originalCreate, hookParams, getInputs);
        };
    }
    
    createEnhanced(originalCreate, hookParams, getInputs) {
        const instanceId = `picker_${Date.now()}_${Math.random()}`;
        const startTime = performance.now();
        
        // 应用全局配置
        const enhancedParams = this.applyGlobalConfig(hookParams);
        
        // 应用中间件
        const processedParams = this.applyMiddleware(enhancedParams);
        
        // 创建基础实例
        const baseInstance = originalCreate(processedParams, getInputs);
        
        // 增强实例
        const enhancedInstance = this.enhanceInstance(baseInstance, instanceId, processedParams);
        
        // 记录创建时间
        const endTime = performance.now();
        this.recordPerformance(instanceId, 'creation', endTime - startTime);
        
        // 注册实例
        this.instances.set(instanceId, {
            instance: enhancedInstance,
            params: processedParams,
            createdAt: Date.now(),
            lastUsed: Date.now()
        });
        
        return enhancedInstance;
    }
    
    applyGlobalConfig(hookParams) {
        return {
            format: this.globalConfig.defaultFormat,
            ...hookParams,
            pickerProps: {
                tz: this.globalConfig.defaultTimezone,
                ...hookParams.pickerProps
            }
        };
    }
    
    applyMiddleware(hookParams) {
        return this.middleware.reduce((params, middleware) => {
            return middleware(params) || params;
        }, hookParams);
    }
    
    enhanceInstance(baseInstance, instanceId, params) {
        const originalOpen = baseInstance.open;
        const originalEnable = baseInstance.enable;
        
        return {
            ...baseInstance,
            
            // 增强的打开方法
            open: (inputIndex) => {
                this.recordUsage(instanceId, 'open');
                
                // 应用验证规则
                if (params.validationRules) {
                    const isValid = this.validateValue(baseInstance.state.value, params.validationRules);
                    if (!isValid) {
                        this.recordError(instanceId, 'validation', 'Invalid value');
                        return;
                    }
                }
                
                return originalOpen(inputIndex);
            },
            
            // 增强的启用方法
            enable: () => {
                const result = originalEnable();
                this.recordUsage(instanceId, 'enable');
                return result;
            },
            
            // 新增方法
            validate: (value) => {
                return this.validateValue(value, params.validationRules);
            },
            
            getStats: () => {
                return this.getInstanceStats(instanceId);
            },
            
            destroy: () => {
                this.destroyInstance(instanceId);
            }
        };
    }
    
    addValidationRule(name, rule) {
        this.globalConfig.validationRules.set(name, rule);
    }
    
    addFormatter(name, formatter) {
        this.globalConfig.formatters.set(name, formatter);
    }
    
    addParser(name, parser) {
        this.globalConfig.parsers.set(name, parser);
    }
    
    addMiddleware(middleware) {
        this.middleware.push(middleware);
    }
    
    validateValue(value, rules) {
        if (!rules || rules.length === 0) return true;
        
        return rules.every(ruleName => {
            const rule = this.globalConfig.validationRules.get(ruleName);
            return rule ? rule(value) : true;
        });
    }
    
    recordUsage(instanceId, action) {
        const key = `${instanceId}_${action}`;
        const current = this.analytics.usage.get(key) || 0;
        this.analytics.usage.set(key, current + 1);
        
        // 更新最后使用时间
        const instance = this.instances.get(instanceId);
        if (instance) {
            instance.lastUsed = Date.now();
        }
    }
    
    recordError(instanceId, type, message) {
        const key = `${instanceId}_${type}`;
        const errors = this.analytics.errors.get(key) || [];
        errors.push({
            message,
            timestamp: Date.now()
        });
        this.analytics.errors.set(key, errors);
    }
    
    recordPerformance(instanceId, operation, duration) {
        const key = `${instanceId}_${operation}`;
        const metrics = this.analytics.performance.get(key) || [];
        metrics.push({
            duration,
            timestamp: Date.now()
        });
        this.analytics.performance.set(key, metrics);
    }
    
    getInstanceStats(instanceId) {
        const instance = this.instances.get(instanceId);
        if (!instance) return null;
        
        const usageStats = {};
        const errorStats = {};
        const performanceStats = {};
        
        // 收集使用统计
        for (const [key, count] of this.analytics.usage.entries()) {
            if (key.startsWith(instanceId)) {
                const action = key.replace(`${instanceId}_`, '');
                usageStats[action] = count;
            }
        }
        
        // 收集错误统计
        for (const [key, errors] of this.analytics.errors.entries()) {
            if (key.startsWith(instanceId)) {
                const type = key.replace(`${instanceId}_`, '');
                errorStats[type] = errors.length;
            }
        }
        
        // 收集性能统计
        for (const [key, metrics] of this.analytics.performance.entries()) {
            if (key.startsWith(instanceId)) {
                const operation = key.replace(`${instanceId}_`, '');
                const durations = metrics.map(m => m.duration);
                performanceStats[operation] = {
                    count: durations.length,
                    average: durations.reduce((a, b) => a + b, 0) / durations.length,
                    min: Math.min(...durations),
                    max: Math.max(...durations)
                };
            }
        }
        
        return {
            instanceId,
            createdAt: new Date(instance.createdAt),
            lastUsed: new Date(instance.lastUsed),
            uptime: Date.now() - instance.createdAt,
            usage: usageStats,
            errors: errorStats,
            performance: performanceStats
        };
    }
    
    destroyInstance(instanceId) {
        // 清理分析数据
        for (const map of Object.values(this.analytics)) {
            for (const key of map.keys()) {
                if (key.startsWith(instanceId)) {
                    map.delete(key);
                }
            }
        }
        
        // 移除实例
        this.instances.delete(instanceId);
    }
    
    getGlobalStats() {
        const stats = {
            totalInstances: this.instances.size,
            activeInstances: 0,
            totalUsage: 0,
            totalErrors: 0,
            averageUptime: 0,
            topActions: [],
            errorSummary: {}
        };
        
        const now = Date.now();
        let totalUptime = 0;
        
        // 统计实例信息
        for (const instance of this.instances.values()) {
            totalUptime += now - instance.createdAt;
            
            if (now - instance.lastUsed < 300000) { // 5分钟内活跃
                stats.activeInstances++;
            }
        }
        
        stats.averageUptime = totalUptime / this.instances.size;
        
        // 统计使用情况
        const actionCounts = {};
        for (const [key, count] of this.analytics.usage.entries()) {
            const action = key.split('_').pop();
            actionCounts[action] = (actionCounts[action] || 0) + count;
            stats.totalUsage += count;
        }
        
        stats.topActions = Object.entries(actionCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([action, count]) => ({ action, count }));
        
        // 统计错误情况
        const errorCounts = {};
        for (const [key, errors] of this.analytics.errors.entries()) {
            const type = key.split('_').pop();
            errorCounts[type] = (errorCounts[type] || 0) + errors.length;
            stats.totalErrors += errors.length;
        }
        
        stats.errorSummary = errorCounts;
        
        return stats;
    }
    
    exportAnalytics() {
        return {
            instances: Object.fromEntries(
                Array.from(this.instances.entries()).map(([id, instance]) => [
                    id,
                    {
                        createdAt: instance.createdAt,
                        lastUsed: instance.lastUsed,
                        params: instance.params
                    }
                ])
            ),
            usage: Object.fromEntries(this.analytics.usage.entries()),
            errors: Object.fromEntries(this.analytics.errors.entries()),
            performance: Object.fromEntries(this.analytics.performance.entries())
        };
    }
    
    importAnalytics(data) {
        if (data.usage) {
            this.analytics.usage = new Map(Object.entries(data.usage));
        }
        if (data.errors) {
            this.analytics.errors = new Map(Object.entries(data.errors));
        }
        if (data.performance) {
            this.analytics.performance = new Map(Object.entries(data.performance));
        }
    }
}

// 中间件示例
const validationMiddleware = (params) => {
    if (params.pickerProps.type === 'date') {
        params.validationRules = params.validationRules || [];
        if (!params.validationRules.includes('businessDay')) {
            params.validationRules.push('businessDay');
        }
    }
    return params;
};

const loggingMiddleware = (params) => {
    console.log('Creating datetime picker with params:', params);
    return params;
};

// 使用示例
const enterpriseService = new EnterpriseDateTimePickerService();

// 添加中间件
enterpriseService.addMiddleware(validationMiddleware);
enterpriseService.addMiddleware(loggingMiddleware);

// 初始化服务
const baseService = odoo.__DEBUG__.services.datetime_picker;
enterpriseService.initialize(baseService);

// 在组件中使用
class EnhancedDateInputComponent extends Component {
    setup() {
        this.datetimePickerService = useService('datetime_picker');
        
        this.dateTimePicker = this.datetimePickerService.create({
            format: 'YYYY-MM-DD',
            validationRules: ['businessDay', 'fiscalPeriod'],
            pickerProps: {
                type: 'date',
                value: this.props.value,
                onSelect: (value) => {
                    this.props.onValueChange(value);
                }
            },
            onApply: (value) => {
                this.saveValue(value);
            }
        }, () => [this.inputRef.el]);
        
        // 生命周期集成
        onWillRender(this.dateTimePicker.computeBasePickerProps);
        useEffect(this.dateTimePicker.enable, () => [this.inputRef.el]);
        onPatched(this.dateTimePicker.focusIfNeeded);
    }
    
    onWillUnmount() {
        this.dateTimePicker.destroy();
    }
    
    getPickerStats() {
        return this.dateTimePicker.getStats();
    }
}
```

### 2. 服务测试和监控框架
```javascript
class DateTimePickerServiceTestFramework {
    constructor() {
        this.testSuites = new Map();
        this.mockServices = this.createMockServices();
        this.testResults = [];
        this.setupTestSuites();
    }

    createMockServices() {
        return {
            popover: {
                add: jest.fn(() => ({
                    open: jest.fn(),
                    close: jest.fn(),
                    isOpen: false
                }))
            }
        };
    }

    setupTestSuites() {
        this.testSuites.set('service_initialization', {
            name: 'Service Initialization Tests',
            tests: [
                () => this.testServiceDefinition(),
                () => this.testDependencyInjection(),
                () => this.testFactoryMethod(),
                () => this.testInstanceCreation()
            ]
        });

        this.testSuites.set('state_management', {
            name: 'State Management Tests',
            tests: [
                () => this.testReactiveState(),
                () => this.testStateSync(),
                () => this.testPropertyUpdates(),
                () => this.testValueConversion()
            ]
        });

        this.testSuites.set('input_integration', {
            name: 'Input Integration Tests',
            tests: [
                () => this.testEventListeners(),
                () => this.testKeyboardHandling(),
                () => this.testInputSync(),
                () => this.testValidation()
            ]
        });

        this.testSuites.set('popover_management', {
            name: 'Popover Management Tests',
            tests: [
                () => this.testPopoverCreation(),
                () => this.testPopoverLifecycle(),
                () => this.testPositioning(),
                () => this.testCleanup()
            ]
        });

        this.testSuites.set('performance', {
            name: 'Performance Tests',
            tests: [
                () => this.testCreationPerformance(),
                () => this.testUpdatePerformance(),
                () => this.testMemoryUsage(),
                () => this.testEventHandling()
            ]
        });
    }

    testServiceDefinition() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            // 测试服务定义
            if (datetimePickerService && typeof datetimePickerService === 'object') {
                results.tests.push({
                    name: 'Service is defined',
                    status: 'passed',
                    message: 'DatetimePickerService is properly defined'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Service is defined',
                    status: 'failed',
                    message: 'DatetimePickerService is not defined'
                });
                results.failed++;
            }

            // 测试依赖声明
            if (datetimePickerService.dependencies && datetimePickerService.dependencies.includes('popover')) {
                results.tests.push({
                    name: 'Dependencies declared',
                    status: 'passed',
                    message: 'Service dependencies are properly declared'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Dependencies declared',
                    status: 'failed',
                    message: 'Service dependencies are not properly declared'
                });
                results.failed++;
            }

            // 测试启动函数
            if (typeof datetimePickerService.start === 'function') {
                results.tests.push({
                    name: 'Start function exists',
                    status: 'passed',
                    message: 'Service start function is defined'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Start function exists',
                    status: 'failed',
                    message: 'Service start function is missing'
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Service definition',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    testInstanceCreation() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            // 创建服务实例
            const serviceInstance = datetimePickerService.start({}, this.mockServices);

            if (serviceInstance && typeof serviceInstance.create === 'function') {
                results.tests.push({
                    name: 'Service instance creation',
                    status: 'passed',
                    message: 'Service instance is created with create method'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Service instance creation',
                    status: 'failed',
                    message: 'Service instance is not properly created'
                });
                results.failed++;
            }

            // 测试picker实例创建
            const pickerInstance = serviceInstance.create({
                pickerProps: {
                    type: 'date',
                    value: null
                }
            }, () => [document.createElement('input')]);

            if (pickerInstance && pickerInstance.state && pickerInstance.open) {
                results.tests.push({
                    name: 'Picker instance creation',
                    status: 'passed',
                    message: 'Picker instance is created with required methods'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Picker instance creation',
                    status: 'failed',
                    message: 'Picker instance is not properly created'
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Instance creation',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    testReactiveState() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            const serviceInstance = datetimePickerService.start({}, this.mockServices);
            const pickerInstance = serviceInstance.create({
                pickerProps: {
                    type: 'date',
                    value: null
                }
            }, () => [document.createElement('input')]);

            // 测试状态对象
            if (pickerInstance.state && typeof pickerInstance.state === 'object') {
                results.tests.push({
                    name: 'State object exists',
                    status: 'passed',
                    message: 'Reactive state object is available'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'State object exists',
                    status: 'failed',
                    message: 'Reactive state object is missing'
                });
                results.failed++;
            }

            // 测试状态属性
            const requiredProps = ['value', 'type', 'onSelect'];
            const hasRequiredProps = requiredProps.every(prop =>
                pickerInstance.state.hasOwnProperty(prop)
            );

            if (hasRequiredProps) {
                results.tests.push({
                    name: 'State properties',
                    status: 'passed',
                    message: 'Required state properties are present'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'State properties',
                    status: 'failed',
                    message: 'Some required state properties are missing'
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Reactive state',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    testEventListeners() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            const serviceInstance = datetimePickerService.start({}, this.mockServices);
            const mockInput = document.createElement('input');

            // 模拟事件监听器添加
            const originalAddEventListener = mockInput.addEventListener;
            const eventListeners = [];
            mockInput.addEventListener = function(event, handler) {
                eventListeners.push({ event, handler });
                return originalAddEventListener.call(this, event, handler);
            };

            const pickerInstance = serviceInstance.create({
                pickerProps: {
                    type: 'date',
                    value: null
                }
            }, () => [mockInput]);

            // 启用事件监听
            pickerInstance.enable();

            // 检查事件监听器
            const expectedEvents = ['change', 'click', 'focus', 'keydown'];
            const registeredEvents = eventListeners.map(l => l.event);
            const hasAllEvents = expectedEvents.every(event =>
                registeredEvents.includes(event)
            );

            if (hasAllEvents) {
                results.tests.push({
                    name: 'Event listeners registration',
                    status: 'passed',
                    message: 'All required event listeners are registered'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Event listeners registration',
                    status: 'failed',
                    message: `Missing events: ${expectedEvents.filter(e => !registeredEvents.includes(e)).join(', ')}`
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Event listeners',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    testCreationPerformance() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            const serviceInstance = datetimePickerService.start({}, this.mockServices);

            // 测试单个实例创建性能
            const startTime = performance.now();

            const pickerInstance = serviceInstance.create({
                pickerProps: {
                    type: 'date',
                    value: null
                }
            }, () => [document.createElement('input')]);

            const endTime = performance.now();
            const duration = endTime - startTime;

            if (duration < 10) { // 10ms内完成
                results.tests.push({
                    name: 'Single instance creation performance',
                    status: 'passed',
                    message: `Instance created in ${duration.toFixed(2)}ms`
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Single instance creation performance',
                    status: 'failed',
                    message: `Creation took too long: ${duration.toFixed(2)}ms`
                });
                results.failed++;
            }

            // 测试批量创建性能
            const batchStartTime = performance.now();
            const instances = [];

            for (let i = 0; i < 10; i++) {
                instances.push(serviceInstance.create({
                    pickerProps: {
                        type: 'date',
                        value: null
                    }
                }, () => [document.createElement('input')]));
            }

            const batchEndTime = performance.now();
            const batchDuration = batchEndTime - batchStartTime;

            if (batchDuration < 100) { // 100ms内完成
                results.tests.push({
                    name: 'Batch creation performance',
                    status: 'passed',
                    message: `10 instances created in ${batchDuration.toFixed(2)}ms`
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Batch creation performance',
                    status: 'failed',
                    message: `Batch creation took too long: ${batchDuration.toFixed(2)}ms`
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Creation performance',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    runAllTests() {
        const allResults = {};

        for (const [suiteId, suite] of this.testSuites.entries()) {
            console.log(`Running ${suite.name}...`);

            const suiteResults = {
                name: suite.name,
                passed: 0,
                failed: 0,
                tests: []
            };

            for (const test of suite.tests) {
                try {
                    const testResult = test();
                    suiteResults.passed += testResult.passed;
                    suiteResults.failed += testResult.failed;
                    suiteResults.tests.push(...testResult.tests);
                } catch (error) {
                    suiteResults.tests.push({
                        name: 'Test execution',
                        status: 'failed',
                        message: `Test failed to execute: ${error.message}`
                    });
                    suiteResults.failed++;
                }
            }

            allResults[suiteId] = suiteResults;
        }

        return allResults;
    }

    generateTestReport() {
        const results = this.runAllTests();

        const summary = {
            totalSuites: Object.keys(results).length,
            totalTests: 0,
            totalPassed: 0,
            totalFailed: 0,
            suites: {}
        };

        for (const [suiteId, result] of Object.entries(results)) {
            summary.totalTests += result.tests.length;
            summary.totalPassed += result.passed;
            summary.totalFailed += result.failed;
            summary.suites[suiteId] = {
                passed: result.passed,
                failed: result.failed,
                total: result.tests.length
            };
        }

        console.group('🧪 DateTime Picker Service Test Report');
        console.log(`Total Test Suites: ${summary.totalSuites}`);
        console.log(`Total Tests: ${summary.totalTests}`);
        console.log(`Passed: ${summary.totalPassed}`);
        console.log(`Failed: ${summary.totalFailed}`);
        console.log(`Success Rate: ${(summary.totalPassed / summary.totalTests * 100).toFixed(1)}%`);

        for (const [suiteId, suite] of Object.entries(results)) {
            console.group(`${suite.name}`);
            console.log(`Passed: ${suite.passed}/${suite.tests.length}`);

            suite.tests.forEach(test => {
                const icon = test.status === 'passed' ? '✅' : '❌';
                console.log(`${icon} ${test.name}: ${test.message}`);
            });

            console.groupEnd();
        }

        console.groupEnd();

        return { summary, details: results };
    }
}

// 使用示例
const testFramework = new DateTimePickerServiceTestFramework();
const testReport = testFramework.generateTestReport();
```

## 🔧 调试技巧

### 服务状态监控
```javascript
function debugDateTimePickerService() {
    const service = odoo.__DEBUG__.services.datetime_picker;

    console.group('⚙️ DateTime Picker Service Debug');
    console.log('Service instance:', service);

    // 创建测试实例
    const testInstance = service.create({
        pickerProps: {
            type: 'date',
            value: null
        }
    }, () => [document.createElement('input')]);

    console.log('Test instance:', testInstance);
    console.log('State:', testInstance.state);
    console.log('Methods:', Object.keys(testInstance));

    console.groupEnd();
}

// 在控制台中调用
debugDateTimePickerService();
```

### 输入框事件追踪
```javascript
function traceInputEvents() {
    const inputs = document.querySelectorAll('input[type="text"]');

    console.group('📝 Input Events Trace');
    console.log(`Found ${inputs.length} text inputs`);

    inputs.forEach((input, index) => {
        const events = ['change', 'click', 'focus', 'keydown'];

        events.forEach(eventType => {
            input.addEventListener(eventType, (event) => {
                console.log(`Input ${index} - ${eventType}:`, {
                    value: input.value,
                    target: event.target,
                    timestamp: Date.now()
                });
            });
        });
    });

    console.log('Event tracing enabled for all text inputs');
    console.groupEnd();
}

// 启用事件追踪
traceInputEvents();
```

## 📊 性能考虑

### 优化策略
1. **事件去重**: 使用WeakSet避免重复注册事件监听器
2. **状态缓存**: 缓存计算结果避免重复计算
3. **延迟更新**: 使用防抖机制减少频繁更新
4. **内存管理**: 及时清理不再使用的实例

### 最佳实践
```javascript
// ✅ 好的做法：使用WeakSet避免重复注册
if (!listenedElements.has(el)) {
    listenedElements.add(el);
    el.addEventListener("change", onInputChange);
}

// ❌ 不好的做法：重复注册事件监听器
el.addEventListener("change", onInputChange); // 可能重复注册

// ✅ 好的做法：智能属性比较
if (lastInitialProps && arePropsEqual(lastInitialProps, propsCopy)) {
    return; // 避免不必要的更新
}

// ❌ 不好的做法：总是更新属性
for (const [key, value] of Object.entries(nextInitialProps)) {
    pickerProps[key] = value; // 可能导致不必要的更新
}

// ✅ 好的做法：安全的值转换
const [parsedValue, error] = safeConvert("parse", el.value);
if (error) {
    updateInput(el, currentValue); // 恢复原值
    return currentValue;
}

// ❌ 不好的做法：不处理转换错误
const parsedValue = parseDate(el.value); // 可能抛出异常
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解企业级服务架构的设计和实现
- [ ] 掌握复杂状态管理和同步机制
- [ ] 理解输入框与选择器的双向绑定
- [ ] 能够管理弹出框生命周期
- [ ] 掌握事件处理和用户交互优化
- [ ] 了解服务的测试和监控技术

## 🚀 下一步学习
学完日期时间选择器服务后，建议继续学习：
1. **弹出框服务** (`@web/core/popover/`) - 学习弹出框系统
2. **日期工具** (`@web/core/l10n/dates.js`) - 理解日期处理
3. **服务注册表** (`@web/core/registry.js`) - 掌握服务管理

## 💡 重要提示
- 日期时间选择器服务是整个系统的核心
- 状态同步确保了数据的一致性
- 事件处理提供了良好的用户体验
- 服务架构支持了高度的可扩展性
```

# @web/core/datetime/datetime_input.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/datetime/datetime_input.js`
- **原始路径**: `/web/static/src/core/datetime/datetime_input.js`
- **代码行数**: 51行
- **作用**: 提供日期时间输入组件，封装日期时间选择器为可复用的输入控件

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 组件封装和属性继承的设计模式
- 钩子与组件的集成应用
- 属性过滤和传递的处理策略
- 可复用输入组件的架构设计
- TypeScript类型定义在组件中的应用

## 📚 核心概念

### 什么是日期时间输入组件？
日期时间输入组件是一个**封装组件**，主要功能：
- **简化使用**: 将复杂的日期时间选择器封装为简单的输入组件
- **属性继承**: 继承基础选择器的所有功能和属性
- **事件处理**: 统一处理日期时间变化事件
- **模板驱动**: 使用模板系统渲染用户界面

### 核心架构组成
```javascript
// 主要组件
const DateTimeInput = class extends Component {
    static props,           // 组件属性定义
    static template,        // 组件模板
    setup()                // 组件设置
};

// 属性结构
const props = {
    // 继承自DateTimePicker的属性
    ...DateTimePicker.props,
    
    // 自有属性
    format: "string",       // 日期格式
    id: "string",          // 输入框ID
    onChange: "function",   // 变化回调
    onApply: "function",   // 应用回调
    placeholder: "string"   // 占位符文本
};

// 钩子集成
const hookParams = {
    format,                // 日期格式
    pickerProps,          // 选择器属性
    onApply,              // 应用回调
    onChange              // 变化回调
};
```

### 基本使用模式
```javascript
import { DateTimeInput } from '@web/core/datetime/datetime_input';

// 在模板中使用
<DateTimeInput 
    value="state.selectedDate"
    format="YYYY-MM-DD HH:mm"
    placeholder="Select date and time"
    onChange="(value) => this.onDateChange(value)"
    onApply="(value) => this.onDateApply(value)"
    enableTime="true"
    enableRange="false"/>

// 在组件中处理事件
class MyComponent extends Component {
    setup() {
        this.state = useState({
            selectedDate: null
        });
    }
    
    onDateChange(value) {
        console.log("Date changing:", value);
        this.state.selectedDate = value;
    }
    
    onDateApply(value) {
        console.log("Date applied:", value);
        this.saveDate(value);
    }
}
```

## 🔍 核心实现详解

### 1. 组件类定义

#### 类型定义和继承
```javascript
/**
 * @typedef {import("./datetime_picker").DateTimePickerProps & {
 *  format?: string;
 *  id?: string;
 *  onApply?: (value: DateTime) => any;
 *  onChange?: (value: DateTime) => any;
 *  placeholder?: string;
 * }} DateTimeInputProps
 */
```

**类型系统特点**：
- **类型继承**: 继承DateTimePickerProps的所有属性
- **扩展属性**: 添加输入组件特有的属性
- **可选属性**: 使用?标记可选属性
- **函数类型**: 明确定义回调函数的签名

#### 属性定义策略
```javascript
const dateTimeInputOwnProps = {
    format: { type: String, optional: true },
    id: { type: String, optional: true },
    onChange: { type: Function, optional: true },
    onApply: { type: Function, optional: true },
    placeholder: { type: String, optional: true },
};
```

**属性设计原则**：
- **类型安全**: 明确指定每个属性的类型
- **可选性**: 所有自有属性都是可选的
- **分离关注**: 将自有属性单独定义便于管理
- **验证支持**: 支持运行时属性验证

### 2. 属性继承机制

#### 属性合并策略
```javascript
static props = {
    ...DateTimePicker.props,
    ...dateTimeInputOwnProps,
};
```

**继承特点**：
- **展开语法**: 使用展开语法合并属性定义
- **优先级**: 自有属性覆盖继承的同名属性
- **完整性**: 继承所有基础组件的功能
- **扩展性**: 易于添加新的自有属性

#### 属性过滤机制
```javascript
const getPickerProps = () => omit(this.props, ...Object.keys(dateTimeInputOwnProps));
```

**过滤策略**：
- **排除自有**: 排除输入组件自有的属性
- **传递继承**: 只传递继承自基础组件的属性
- **动态计算**: 每次调用时动态计算属性
- **工具函数**: 使用omit工具函数简化实现

### 3. 钩子集成应用

#### 钩子参数配置
```javascript
useDateTimePicker({
    format: this.props.format,
    get pickerProps() {
        return getPickerProps();
    },
    onApply: (...args) => this.props.onApply?.(...args),
    onChange: (...args) => this.props.onChange?.(...args),
});
```

**配置策略分析**：
- **格式传递**: 直接传递format属性
- **动态属性**: 使用getter动态获取选择器属性
- **事件代理**: 使用箭头函数代理事件回调
- **可选调用**: 使用可选链操作符安全调用回调

#### Getter属性设计
```javascript
get pickerProps() {
    return getPickerProps();
}
```

**Getter优势**：
- **延迟计算**: 只在需要时计算属性
- **实时更新**: 确保属性始终是最新的
- **性能优化**: 避免不必要的属性计算
- **响应式**: 自动响应属性变化

### 4. 事件处理机制

#### 事件代理模式
```javascript
onApply: (...args) => this.props.onApply?.(...args),
onChange: (...args) => this.props.onChange?.(...args),
```

**代理特点**：
- **参数透传**: 使用展开语法透传所有参数
- **安全调用**: 使用可选链避免未定义错误
- **上下文保持**: 箭头函数保持正确的this上下文
- **灵活性**: 支持任意数量的参数

## 🎨 实际应用场景

### 1. 高级日期时间输入组件系统
```javascript
class AdvancedDateTimeInputManager {
    constructor() {
        this.inputComponents = new Map();
        this.validators = new Map();
        this.formatters = new Map();
        this.themes = new Map();
        this.setupDefaultComponents();
    }
    
    setupDefaultComponents() {
        // 基础日期输入组件
        this.registerComponent('date', {
            baseComponent: DateTimeInput,
            defaultProps: {
                format: 'YYYY-MM-DD',
                enableTime: false,
                placeholder: 'Select date'
            },
            validators: ['dateRange', 'businessDay'],
            formatters: ['dateOnly']
        });
        
        // 时间输入组件
        this.registerComponent('time', {
            baseComponent: DateTimeInput,
            defaultProps: {
                format: 'HH:mm',
                enableTime: true,
                enableDate: false,
                placeholder: 'Select time'
            },
            validators: ['timeRange'],
            formatters: ['timeOnly']
        });
        
        // 日期时间输入组件
        this.registerComponent('datetime', {
            baseComponent: DateTimeInput,
            defaultProps: {
                format: 'YYYY-MM-DD HH:mm',
                enableTime: true,
                placeholder: 'Select date and time'
            },
            validators: ['dateTimeRange'],
            formatters: ['dateTime']
        });
        
        // 日期范围输入组件
        this.registerComponent('daterange', {
            baseComponent: DateTimeInput,
            defaultProps: {
                format: 'YYYY-MM-DD',
                enableRange: true,
                placeholder: 'Select date range'
            },
            validators: ['rangeValidation'],
            formatters: ['dateRange']
        });
        
        // 业务日期输入组件
        this.registerComponent('businessdate', {
            baseComponent: DateTimeInput,
            defaultProps: {
                format: 'YYYY-MM-DD',
                enableTime: false,
                placeholder: 'Select business date'
            },
            validators: ['businessDay', 'notWeekend'],
            formatters: ['businessDate']
        });
    }
    
    registerComponent(name, config) {
        this.inputComponents.set(name, config);
    }
    
    createComponent(type, customProps = {}) {
        const config = this.inputComponents.get(type);
        if (!config) {
            throw new Error(`Unknown component type: ${type}`);
        }
        
        return class EnhancedDateTimeInput extends config.baseComponent {
            static template = `web.Enhanced${type.charAt(0).toUpperCase() + type.slice(1)}Input`;
            
            setup() {
                // 合并默认属性和自定义属性
                const mergedProps = {
                    ...config.defaultProps,
                    ...customProps,
                    ...this.props
                };
                
                // 应用验证器
                const validators = config.validators || [];
                const validationRules = validators.map(name => 
                    this.getValidator(name)
                ).filter(Boolean);
                
                // 应用格式化器
                const formatters = config.formatters || [];
                const formatRules = formatters.map(name => 
                    this.getFormatter(name)
                ).filter(Boolean);
                
                // 增强的钩子参数
                const enhancedHookParams = {
                    ...mergedProps,
                    onApply: (value) => {
                        // 应用验证
                        const isValid = this.validateValue(value, validationRules);
                        if (!isValid) {
                            console.warn(`Invalid value for ${type}:`, value);
                            return;
                        }
                        
                        // 应用格式化
                        const formattedValue = this.formatValue(value, formatRules);
                        
                        // 调用原始回调
                        if (this.props.onApply) {
                            this.props.onApply(formattedValue);
                        }
                    },
                    onChange: (value) => {
                        // 实时验证（可选）
                        if (this.props.realTimeValidation) {
                            const isValid = this.validateValue(value, validationRules);
                            this.updateValidationState(isValid);
                        }
                        
                        // 调用原始回调
                        if (this.props.onChange) {
                            this.props.onChange(value);
                        }
                    }
                };
                
                // 调用基础设置
                super.setup();
                
                // 使用增强的钩子
                useDateTimePicker(enhancedHookParams);
            }
            
            validateValue(value, validators) {
                return validators.every(validator => validator(value));
            }
            
            formatValue(value, formatters) {
                return formatters.reduce((result, formatter) => {
                    return formatter(result);
                }, value);
            }
            
            updateValidationState(isValid) {
                // 更新UI状态显示验证结果
                const inputElement = this.el.querySelector('input');
                if (inputElement) {
                    inputElement.classList.toggle('is-invalid', !isValid);
                    inputElement.classList.toggle('is-valid', isValid);
                }
            }
            
            getValidator(name) {
                return this.parent.getValidator(name);
            }
            
            getFormatter(name) {
                return this.parent.getFormatter(name);
            }
        };
    }
    
    // 验证器管理
    addValidator(name, validator) {
        this.validators.set(name, validator);
    }
    
    getValidator(name) {
        return this.validators.get(name);
    }
    
    // 格式化器管理
    addFormatter(name, formatter) {
        this.formatters.set(name, formatter);
    }
    
    getFormatter(name) {
        return this.formatters.get(name);
    }
    
    // 主题管理
    addTheme(name, theme) {
        this.themes.set(name, theme);
    }
    
    applyTheme(name) {
        const theme = this.themes.get(name);
        if (theme) {
            // 应用主题样式
            this.injectThemeCSS(theme);
        }
    }
    
    injectThemeCSS(theme) {
        const styleId = 'datetime-input-theme';
        let styleElement = document.getElementById(styleId);
        
        if (!styleElement) {
            styleElement = document.createElement('style');
            styleElement.id = styleId;
            document.head.appendChild(styleElement);
        }
        
        styleElement.textContent = this.generateThemeCSS(theme);
    }
    
    generateThemeCSS(theme) {
        return `
            .datetime-input {
                background-color: ${theme.backgroundColor || '#ffffff'};
                border-color: ${theme.borderColor || '#ced4da'};
                color: ${theme.textColor || '#495057'};
            }
            
            .datetime-input:focus {
                border-color: ${theme.focusColor || '#80bdff'};
                box-shadow: 0 0 0 0.2rem ${theme.focusColor || '#80bdff'}25;
            }
            
            .datetime-input.is-invalid {
                border-color: ${theme.errorColor || '#dc3545'};
            }
            
            .datetime-input.is-valid {
                border-color: ${theme.successColor || '#28a745'};
            }
        `;
    }
}

// 预定义验证器
const CommonValidators = {
    dateRange: (value) => {
        const date = new Date(value);
        const minDate = new Date('1900-01-01');
        const maxDate = new Date('2100-12-31');
        return date >= minDate && date <= maxDate;
    },
    
    businessDay: (value) => {
        const date = new Date(value);
        const day = date.getDay();
        return day >= 1 && day <= 5; // Monday to Friday
    },
    
    notWeekend: (value) => {
        const date = new Date(value);
        const day = date.getDay();
        return day !== 0 && day !== 6; // Not Sunday or Saturday
    },
    
    timeRange: (value) => {
        const time = new Date(`1970-01-01 ${value}`);
        const minTime = new Date('1970-01-01 00:00:00');
        const maxTime = new Date('1970-01-01 23:59:59');
        return time >= minTime && time <= maxTime;
    },
    
    rangeValidation: (value) => {
        if (Array.isArray(value) && value.length === 2) {
            const [start, end] = value;
            return new Date(start) <= new Date(end);
        }
        return true;
    }
};

// 预定义格式化器
const CommonFormatters = {
    dateOnly: (value) => {
        return new Date(value).toISOString().split('T')[0];
    },
    
    timeOnly: (value) => {
        return new Date(`1970-01-01 ${value}`).toTimeString().split(' ')[0].substring(0, 5);
    },
    
    dateTime: (value) => {
        return new Date(value).toISOString().replace('T', ' ').substring(0, 19);
    },
    
    businessDate: (value) => {
        const date = new Date(value);
        // 如果是周末，调整到下一个工作日
        while (date.getDay() === 0 || date.getDay() === 6) {
            date.setDate(date.getDate() + 1);
        }
        return date.toISOString().split('T')[0];
    },
    
    dateRange: (value) => {
        if (Array.isArray(value) && value.length === 2) {
            return value.map(date => new Date(date).toISOString().split('T')[0]);
        }
        return value;
    }
};

// 使用示例
const inputManager = new AdvancedDateTimeInputManager();

// 添加验证器和格式化器
Object.entries(CommonValidators).forEach(([name, validator]) => {
    inputManager.addValidator(name, validator);
});

Object.entries(CommonFormatters).forEach(([name, formatter]) => {
    inputManager.addFormatter(name, formatter);
});

// 创建自定义组件
const BusinessDateInput = inputManager.createComponent('businessdate', {
    realTimeValidation: true,
    onApply: (value) => {
        console.log('Business date selected:', value);
    }
});

// 在模板中使用
class BusinessFormComponent extends Component {
    static template = xml`
        <div class="business-form">
            <BusinessDateInput 
                value="state.meetingDate"
                onChange="(value) => this.onMeetingDateChange(value)"
                placeholder="Select meeting date"/>
        </div>
    `;
    
    static components = { BusinessDateInput };
    
    setup() {
        this.state = useState({
            meetingDate: null
        });
    }
    
    onMeetingDateChange(value) {
        this.state.meetingDate = value;
    }
}
```

### 2. 日期时间输入组件测试套件
```javascript
class DateTimeInputTestSuite {
    constructor() {
        this.testResults = [];
        this.mockComponents = this.createMockComponents();
        this.testScenarios = new Map();
        this.setupTestScenarios();
    }

    createMockComponents() {
        return {
            DateTimeInput: class MockDateTimeInput extends Component {
                static template = xml`<input t-ref="input" type="text"/>`;
                static props = DateTimeInput.props;

                setup() {
                    this.mockSetup();
                }

                mockSetup() {
                    // 模拟钩子调用
                    this.dateTimePicker = {
                        state: { value: null },
                        open: jest.fn()
                    };
                }
            }
        };
    }

    setupTestScenarios() {
        this.testScenarios.set('props_inheritance', {
            name: 'Properties Inheritance Test',
            description: 'Test property inheritance from DateTimePicker',
            test: () => this.testPropsInheritance()
        });

        this.testScenarios.set('props_filtering', {
            name: 'Properties Filtering Test',
            description: 'Test filtering of own properties',
            test: () => this.testPropsFiltering()
        });

        this.testScenarios.set('event_handling', {
            name: 'Event Handling Test',
            description: 'Test onChange and onApply events',
            test: () => this.testEventHandling()
        });

        this.testScenarios.set('hook_integration', {
            name: 'Hook Integration Test',
            description: 'Test integration with useDateTimePicker hook',
            test: () => this.testHookIntegration()
        });

        this.testScenarios.set('template_rendering', {
            name: 'Template Rendering Test',
            description: 'Test component template rendering',
            test: () => this.testTemplateRendering()
        });
    }

    testPropsInheritance() {
        const results = {
            passed: 0,
            failed: 0,
            tests: []
        };

        try {
            // 检查属性继承
            const inputProps = DateTimeInput.props;
            const pickerProps = DateTimePicker.props;

            // 验证继承了基础属性
            const inheritedProps = Object.keys(pickerProps);
            const hasInheritedProps = inheritedProps.every(prop =>
                inputProps.hasOwnProperty(prop)
            );

            if (hasInheritedProps) {
                results.tests.push({
                    name: 'Inherits DateTimePicker properties',
                    status: 'passed',
                    message: 'All DateTimePicker properties are inherited'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Inherits DateTimePicker properties',
                    status: 'failed',
                    message: 'Some DateTimePicker properties are missing'
                });
                results.failed++;
            }

            // 验证自有属性
            const ownProps = ['format', 'id', 'onChange', 'onApply', 'placeholder'];
            const hasOwnProps = ownProps.every(prop =>
                inputProps.hasOwnProperty(prop)
            );

            if (hasOwnProps) {
                results.tests.push({
                    name: 'Has own properties',
                    status: 'passed',
                    message: 'All own properties are defined'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Has own properties',
                    status: 'failed',
                    message: 'Some own properties are missing'
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Properties inheritance',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    testPropsFiltering() {
        const results = {
            passed: 0,
            failed: 0,
            tests: []
        };

        try {
            // 模拟属性过滤
            const allProps = {
                format: 'YYYY-MM-DD',
                id: 'test-input',
                onChange: () => {},
                onApply: () => {},
                placeholder: 'Test placeholder',
                enableTime: true,
                value: '2023-01-01'
            };

            const ownPropKeys = ['format', 'id', 'onChange', 'onApply', 'placeholder'];
            const filteredProps = omit(allProps, ...ownPropKeys);

            // 验证过滤结果
            const hasOwnProps = ownPropKeys.some(key =>
                filteredProps.hasOwnProperty(key)
            );

            if (!hasOwnProps) {
                results.tests.push({
                    name: 'Filters own properties',
                    status: 'passed',
                    message: 'Own properties are correctly filtered out'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Filters own properties',
                    status: 'failed',
                    message: 'Some own properties were not filtered'
                });
                results.failed++;
            }

            // 验证保留了继承属性
            if (filteredProps.enableTime && filteredProps.value) {
                results.tests.push({
                    name: 'Preserves inherited properties',
                    status: 'passed',
                    message: 'Inherited properties are preserved'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Preserves inherited properties',
                    status: 'failed',
                    message: 'Some inherited properties were lost'
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Properties filtering',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    testEventHandling() {
        const results = {
            passed: 0,
            failed: 0,
            tests: []
        };

        try {
            // 模拟事件处理
            const onChangeMock = jest.fn();
            const onApplyMock = jest.fn();

            const mockComponent = {
                props: {
                    onChange: onChangeMock,
                    onApply: onApplyMock
                }
            };

            // 模拟事件代理函数
            const onChangeProxy = (...args) => mockComponent.props.onChange?.(...args);
            const onApplyProxy = (...args) => mockComponent.props.onApply?.(...args);

            // 测试事件调用
            const testValue = '2023-01-01';
            onChangeProxy(testValue);
            onApplyProxy(testValue);

            if (onChangeMock.mock.calls.length === 1) {
                results.tests.push({
                    name: 'onChange event handling',
                    status: 'passed',
                    message: 'onChange event is correctly handled'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'onChange event handling',
                    status: 'failed',
                    message: 'onChange event was not called'
                });
                results.failed++;
            }

            if (onApplyMock.mock.calls.length === 1) {
                results.tests.push({
                    name: 'onApply event handling',
                    status: 'passed',
                    message: 'onApply event is correctly handled'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'onApply event handling',
                    status: 'failed',
                    message: 'onApply event was not called'
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Event handling',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    testHookIntegration() {
        const results = {
            passed: 0,
            failed: 0,
            tests: []
        };

        try {
            // 模拟钩子集成
            const mockUseDateTimePicker = jest.fn(() => ({
                state: { value: null },
                open: jest.fn()
            }));

            // 模拟组件设置
            const mockProps = {
                format: 'YYYY-MM-DD',
                enableTime: true,
                onChange: jest.fn(),
                onApply: jest.fn()
            };

            // 模拟钩子调用
            const hookParams = {
                format: mockProps.format,
                pickerProps: omit(mockProps, 'format', 'onChange', 'onApply'),
                onApply: mockProps.onApply,
                onChange: mockProps.onChange
            };

            mockUseDateTimePicker(hookParams);

            if (mockUseDateTimePicker.mock.calls.length === 1) {
                results.tests.push({
                    name: 'Hook is called',
                    status: 'passed',
                    message: 'useDateTimePicker hook is called'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Hook is called',
                    status: 'failed',
                    message: 'useDateTimePicker hook was not called'
                });
                results.failed++;
            }

            // 验证钩子参数
            const calledParams = mockUseDateTimePicker.mock.calls[0][0];
            if (calledParams.format === mockProps.format) {
                results.tests.push({
                    name: 'Hook parameters are correct',
                    status: 'passed',
                    message: 'Hook parameters are correctly passed'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Hook parameters are correct',
                    status: 'failed',
                    message: 'Hook parameters are incorrect'
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Hook integration',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    testTemplateRendering() {
        const results = {
            passed: 0,
            failed: 0,
            tests: []
        };

        try {
            // 验证模板定义
            if (DateTimeInput.template === "web.DateTimeInput") {
                results.tests.push({
                    name: 'Template is defined',
                    status: 'passed',
                    message: 'Component template is correctly defined'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Template is defined',
                    status: 'failed',
                    message: 'Component template is not defined'
                });
                results.failed++;
            }

            // 模拟模板渲染测试
            const mockElement = document.createElement('div');
            mockElement.innerHTML = '<input type="text" class="datetime-input"/>';

            if (mockElement.querySelector('input')) {
                results.tests.push({
                    name: 'Template renders input',
                    status: 'passed',
                    message: 'Template correctly renders input element'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Template renders input',
                    status: 'failed',
                    message: 'Template does not render input element'
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Template rendering',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    runAllTests() {
        const allResults = {};

        for (const [scenarioId, scenario] of this.testScenarios.entries()) {
            console.log(`Running ${scenario.name}...`);
            allResults[scenarioId] = scenario.test();
        }

        return allResults;
    }

    generateTestReport() {
        const results = this.runAllTests();

        const summary = {
            totalScenarios: Object.keys(results).length,
            totalTests: 0,
            totalPassed: 0,
            totalFailed: 0,
            scenarios: {}
        };

        for (const [scenarioId, result] of Object.entries(results)) {
            summary.totalTests += result.tests.length;
            summary.totalPassed += result.passed;
            summary.totalFailed += result.failed;
            summary.scenarios[scenarioId] = {
                passed: result.passed,
                failed: result.failed,
                total: result.tests.length
            };
        }

        console.group('🧪 DateTime Input Test Report');
        console.log(`Total Scenarios: ${summary.totalScenarios}`);
        console.log(`Total Tests: ${summary.totalTests}`);
        console.log(`Passed: ${summary.totalPassed}`);
        console.log(`Failed: ${summary.totalFailed}`);
        console.log(`Success Rate: ${(summary.totalPassed / summary.totalTests * 100).toFixed(1)}%`);

        for (const [scenarioId, scenario] of this.testScenarios.entries()) {
            const result = results[scenarioId];
            console.group(`${scenario.name}`);
            console.log(`Passed: ${result.passed}/${result.tests.length}`);

            result.tests.forEach(test => {
                const icon = test.status === 'passed' ? '✅' : '❌';
                console.log(`${icon} ${test.name}: ${test.message}`);
            });

            console.groupEnd();
        }

        console.groupEnd();

        return { summary, details: results };
    }
}

// 使用示例
const testSuite = new DateTimeInputTestSuite();
const testReport = testSuite.generateTestReport();
```

## 🔧 调试技巧

### 组件属性检查
```javascript
function debugDateTimeInputProps() {
    console.group('📅 DateTime Input Props Debug');

    // 检查属性定义
    console.log('Component props:', DateTimeInput.props);
    console.log('Own props:', dateTimeInputOwnProps);

    // 检查属性继承
    const inheritedProps = Object.keys(DateTimePicker.props);
    const ownProps = Object.keys(dateTimeInputOwnProps);

    console.log('Inherited props:', inheritedProps);
    console.log('Own props:', ownProps);

    // 检查属性冲突
    const conflicts = inheritedProps.filter(prop => ownProps.includes(prop));
    if (conflicts.length > 0) {
        console.warn('Property conflicts:', conflicts);
    } else {
        console.log('No property conflicts found');
    }

    console.groupEnd();
}

// 在控制台中调用
debugDateTimeInputProps();
```

### 钩子调用追踪
```javascript
function traceDateTimeInputHook() {
    const originalUseDateTimePicker = useDateTimePicker;

    window.useDateTimePicker = function(hookParams) {
        console.log('📅 DateTime Input Hook called with params:', hookParams);

        // 检查pickerProps getter
        if (hookParams.pickerProps && typeof hookParams.pickerProps === 'object') {
            console.log('Picker props:', hookParams.pickerProps);
        }

        const result = originalUseDateTimePicker(hookParams);

        console.log('📅 DateTime Input Hook result:', result);

        return result;
    };

    console.log('DateTime Input hook tracing enabled');
}

// 启用追踪
traceDateTimeInputHook();
```

## 📊 性能考虑

### 优化策略
1. **属性缓存**: 缓存计算后的属性避免重复计算
2. **事件代理**: 使用箭头函数避免重复绑定
3. **延迟计算**: 使用getter实现属性的延迟计算
4. **类型检查**: 在开发环境进行严格的类型检查

### 最佳实践
```javascript
// ✅ 好的做法：使用getter延迟计算
get pickerProps() {
    return getPickerProps();
}

// ❌ 不好的做法：每次都重新计算
pickerProps: getPickerProps()

// ✅ 好的做法：使用可选链安全调用
onApply: (...args) => this.props.onApply?.(...args)

// ❌ 不好的做法：不安全的调用
onApply: (...args) => this.props.onApply(...args)

// ✅ 好的做法：明确的属性类型定义
format: { type: String, optional: true }

// ❌ 不好的做法：缺少类型定义
format: true
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解组件封装和属性继承的设计模式
- [ ] 掌握钩子与组件的集成应用
- [ ] 理解属性过滤和传递的处理策略
- [ ] 能够设计可复用输入组件的架构
- [ ] 掌握TypeScript类型定义在组件中的应用
- [ ] 了解组件的测试和调试技术

## 🚀 下一步学习
学完日期时间输入组件后，建议继续学习：
1. **日期时间选择器** (`@web/core/datetime/datetime_picker.js`) - 学习选择器实现
2. **选择器弹出框** (`@web/core/datetime/datetime_picker_popover.js`) - 理解弹出框机制
3. **选择器服务** (`@web/core/datetime/datetimepicker_service.js`) - 掌握服务架构

## 💡 重要提示
- 日期时间输入组件是选择器的简化封装
- 属性继承机制提供了强大的扩展能力
- 事件代理确保了正确的上下文和参数传递
- 类型定义提供了开发时的安全保障
```

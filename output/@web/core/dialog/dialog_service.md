# DialogService - 对话框服务

## 概述

`dialog_service.js` 是 Odoo Web 核心模块的对话框服务，提供了对话框的生命周期管理和堆栈控制功能。该服务通过overlay系统管理多个对话框的显示、层级关系、状态切换和资源清理，为Odoo Web应用提供了统一的对话框管理解决方案，确保了对话框的正确显示顺序和用户交互体验。

## 文件信息
- **路径**: `/web/static/src/core/dialog/dialog_service.js`
- **行数**: 101
- **模块**: `@web/core/dialog/dialog_service`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                // OWL框架
'@web/core/registry'       // 注册表系统
```

## 核心功能

### 1. DialogWrapper组件

```javascript
class DialogWrapper extends Component {
    static template = xml`<t t-component="props.subComponent" t-props="props.subProps" />`;
    static props = ["*"];
    setup() {
        useChildSubEnv({ dialogData: this.props.subEnv });
    }
}
```

**包装器功能**:
- **组件代理**: 动态渲染传入的对话框组件
- **属性传递**: 透传所有属性到子组件
- **环境注入**: 为子组件提供对话框数据环境
- **灵活渲染**: 支持任意对话框组件的渲染

### 2. 类型定义

```javascript
/**
 *  @typedef {{
 *      onClose?(): void;
 *  }} DialogServiceInterfaceAddOptions
 */
/**
 *  @typedef {{
 *      add(
 *          Component: typeof import("@odoo/owl").Component,
 *          props: {},
 *          options?: DialogServiceInterfaceAddOptions
 *      ): () => void;
 *  }} DialogServiceInterface
 */
```

**类型定义功能**:
- **选项接口**: 定义添加对话框时的可选参数
- **服务接口**: 定义对话框服务的公共API
- **类型安全**: 提供TypeScript类型支持
- **文档说明**: 为开发者提供API文档

### 3. 对话框服务定义

```javascript
const dialogService = {
    dependencies: ["overlay"],
    start(env, { overlay }) {
        const stack = [];
        let nextId = 0;
        // ... 服务实现
    },
};
```

**服务定义功能**:
- **依赖声明**: 声明对overlay服务的依赖
- **堆栈管理**: 维护对话框的显示堆栈
- **ID生成**: 为每个对话框生成唯一标识
- **服务启动**: 提供服务的启动逻辑

### 4. 对话框堆栈管理

```javascript
const deactivate = () => {
    for (const subEnv of stack) {
        subEnv.isActive = false;
    }
};
```

**堆栈管理功能**:
- **状态控制**: 管理对话框的活跃状态
- **批量操作**: 批量设置所有对话框为非活跃
- **层级管理**: 确保只有顶层对话框处于活跃状态
- **状态同步**: 保持堆栈状态的一致性

### 5. 添加对话框

```javascript
const add = (dialogClass, props, options = {}) => {
    const id = nextId++;
    const close = () => remove();
    const subEnv = reactive({
        id,
        close,
        isActive: true,
    });

    deactivate();
    stack.push(subEnv);
    document.body.classList.add("modal-open");

    const scrollOrigin = { top: window.scrollY, left: window.scrollX };
    subEnv.scrollToOrigin = () => {
        if (!stack.length) {
            window.scrollTo(scrollOrigin);
        }
    };

    const remove = overlay.add(
        DialogWrapper,
        {
            subComponent: dialogClass,
            subProps: markRaw({ ...props, close }),
            subEnv,
        },
        {
            onRemove: () => {
                stack.pop();
                deactivate();
                if (stack.length) {
                    stack.at(-1).isActive = true;
                } else {
                    document.body.classList.remove("modal-open");
                }
                options.onClose?.();
            },
            rootId: options.context?.root?.el.getRootNode()?.host?.id,
        }
    );

    return remove;
};
```

**添加对话框功能**:
- **唯一标识**: 为每个对话框分配唯一ID
- **关闭函数**: 创建对话框的关闭函数
- **响应式环境**: 创建响应式的对话框环境
- **状态管理**: 管理对话框的活跃状态
- **滚动记录**: 记录打开对话框前的滚动位置
- **overlay集成**: 通过overlay系统显示对话框
- **清理回调**: 设置对话框移除时的清理逻辑

### 6. 关闭所有对话框

```javascript
function closeAll() {
    for (const dialog of [...stack].reverse()) {
        dialog.close();
    }
}
```

**批量关闭功能**:
- **逆序关闭**: 从顶层开始逆序关闭对话框
- **堆栈复制**: 复制堆栈避免迭代过程中的修改
- **递归关闭**: 依次调用每个对话框的关闭方法
- **状态清理**: 确保所有对话框状态被正确清理

### 7. 服务注册

```javascript
registry.category("services").add("dialog", dialogService);
```

**服务注册功能**:
- **全局注册**: 将对话框服务注册到全局服务注册表
- **依赖注入**: 支持其他服务和组件的依赖注入
- **服务发现**: 允许通过名称查找和使用服务
- **生命周期管理**: 集成到Odoo的服务生命周期管理

## 使用场景

### 1. 基础对话框显示

```javascript
// 在组件中使用对话框服务
class MyComponent extends Component {
    setup() {
        this.dialogService = useService("dialog");
    }

    showDialog() {
        const closeDialog = this.dialogService.add(
            MyDialogComponent,
            {
                title: "示例对话框",
                message: "这是一个示例对话框"
            },
            {
                onClose: () => {
                    console.log("对话框已关闭");
                }
            }
        );

        // 可以手动关闭对话框
        // closeDialog();
    }
}
```

### 2. 确认对话框

```javascript
// 显示确认对话框
async showConfirmation() {
    return new Promise((resolve) => {
        this.dialogService.add(
            ConfirmationDialog,
            {
                title: "确认操作",
                body: "您确定要执行此操作吗？",
                confirm: () => {
                    resolve(true);
                },
                cancel: () => {
                    resolve(false);
                }
            }
        );
    });
}

// 使用确认对话框
async deleteRecord() {
    const confirmed = await this.showConfirmation();
    if (confirmed) {
        // 执行删除操作
        await this.performDelete();
    }
}
```

### 3. 表单对话框

```javascript
// 显示表单编辑对话框
editRecord(recordId) {
    this.dialogService.add(
        FormDialog,
        {
            resModel: "res.partner",
            resId: recordId,
            title: "编辑联系人",
            mode: "edit"
        },
        {
            onClose: () => {
                // 刷新列表视图
                this.refreshView();
            }
        }
    );
}
```

### 4. 多层对话框

```javascript
// 显示多层对话框
showNestedDialogs() {
    // 第一层对话框
    this.dialogService.add(
        FirstDialog,
        { title: "第一层对话框" },
        {
            onClose: () => console.log("第一层关闭")
        }
    );

    // 第二层对话框
    setTimeout(() => {
        this.dialogService.add(
            SecondDialog,
            { title: "第二层对话框" },
            {
                onClose: () => console.log("第二层关闭")
            }
        );
    }, 1000);
}

// 关闭所有对话框
closeAllDialogs() {
    this.dialogService.closeAll();
}
```

## 增强示例

```javascript
// 增强的对话框服务
const EnhancedDialogService = {
    createAdvancedService: () => {
        return {
            dependencies: ["overlay", "notification"],
            start(env, { overlay, notification }) {
                const stack = [];
                let nextId = 0;
                const dialogHistory = [];
                const maxHistorySize = 50;

                // 增强的状态管理
                const state = reactive({
                    activeDialogs: 0,
                    totalDialogs: 0,
                    maxConcurrent: 5
                });

                // 对话框统计
                const stats = reactive({
                    opened: 0,
                    closed: 0,
                    cancelled: 0,
                    confirmed: 0
                });

                // 增强的添加方法
                const add = (dialogClass, props, options = {}) => {
                    // 检查并发限制
                    if (state.activeDialogs >= state.maxConcurrent) {
                        notification.add("Too many dialogs open", { type: "warning" });
                        return () => {};
                    }

                    const id = nextId++;
                    const openTime = Date.now();
                    
                    // 记录对话框信息
                    const dialogInfo = {
                        id,
                        component: dialogClass.name,
                        props: { ...props },
                        openTime,
                        closeTime: null,
                        duration: null,
                        result: null
                    };

                    const close = (result = null) => {
                        dialogInfo.closeTime = Date.now();
                        dialogInfo.duration = dialogInfo.closeTime - dialogInfo.openTime;
                        dialogInfo.result = result;
                        
                        // 更新统计
                        stats.closed++;
                        if (result === 'confirmed') {
                            stats.confirmed++;
                        } else if (result === 'cancelled') {
                            stats.cancelled++;
                        }

                        // 添加到历史记录
                        addToHistory(dialogInfo);
                        
                        return remove();
                    };

                    const subEnv = reactive({
                        id,
                        close,
                        isActive: true,
                        openTime,
                        dialogInfo
                    });

                    // 更新状态
                    state.activeDialogs++;
                    state.totalDialogs++;
                    stats.opened++;

                    deactivate();
                    stack.push(subEnv);
                    document.body.classList.add("modal-open");

                    const scrollOrigin = { top: window.scrollY, left: window.scrollX };
                    subEnv.scrollToOrigin = () => {
                        if (!stack.length) {
                            window.scrollTo(scrollOrigin);
                        }
                    };

                    const remove = overlay.add(
                        DialogWrapper,
                        {
                            subComponent: dialogClass,
                            subProps: markRaw({ ...props, close }),
                            subEnv,
                        },
                        {
                            onRemove: () => {
                                stack.pop();
                                state.activeDialogs--;
                                
                                deactivate();
                                if (stack.length) {
                                    stack.at(-1).isActive = true;
                                } else {
                                    document.body.classList.remove("modal-open");
                                }
                                
                                options.onClose?.();
                            },
                            rootId: options.context?.root?.el.getRootNode()?.host?.id,
                        }
                    );

                    return remove;
                };

                // 添加到历史记录
                const addToHistory = (dialogInfo) => {
                    dialogHistory.unshift(dialogInfo);
                    if (dialogHistory.length > maxHistorySize) {
                        dialogHistory.pop();
                    }
                };

                // 获取对话框统计
                const getStats = () => ({ ...stats });

                // 获取历史记录
                const getHistory = (limit = 10) => {
                    return dialogHistory.slice(0, limit);
                };

                // 查找对话框
                const findDialog = (predicate) => {
                    return stack.find(predicate);
                };

                // 获取活跃对话框
                const getActiveDialog = () => {
                    return stack.find(dialog => dialog.isActive);
                };

                // 关闭特定对话框
                const closeById = (id) => {
                    const dialog = stack.find(d => d.id === id);
                    if (dialog) {
                        dialog.close();
                    }
                };

                // 增强的关闭所有方法
                const closeAll = (result = 'cancelled') => {
                    for (const dialog of [...stack].reverse()) {
                        dialog.close(result);
                    }
                };

                // 获取对话框状态
                const getState = () => ({ ...state });

                // 设置最大并发数
                const setMaxConcurrent = (max) => {
                    state.maxConcurrent = Math.max(1, max);
                };

                return {
                    add,
                    closeAll,
                    closeById,
                    findDialog,
                    getActiveDialog,
                    getStats,
                    getHistory,
                    getState,
                    setMaxConcurrent
                };
            }
        };
    }
};

// 使用增强服务
const enhancedDialogService = EnhancedDialogService.createAdvancedService();
registry.category("services").add("enhancedDialog", enhancedDialogService);

// 在组件中使用
class MyComponent extends Component {
    setup() {
        this.dialogService = useService("enhancedDialog");
    }

    async showAdvancedDialog() {
        const closeDialog = this.dialogService.add(
            MyDialog,
            { title: "高级对话框" },
            {
                onClose: () => {
                    const stats = this.dialogService.getStats();
                    console.log("对话框统计:", stats);
                }
            }
        );

        // 获取当前状态
        const state = this.dialogService.getState();
        console.log("当前活跃对话框数:", state.activeDialogs);

        // 获取历史记录
        const history = this.dialogService.getHistory(5);
        console.log("最近5个对话框:", history);
    }
}
```

## 技术特点

### 1. 堆栈管理
- 维护对话框显示顺序
- 自动状态切换
- 层级关系管理

### 2. 生命周期控制
- 完整的创建和销毁流程
- 资源清理机制
- 状态同步管理

### 3. 环境隔离
- 独立的对话框环境
- 属性和状态隔离
- 安全的数据传递

### 4. 服务集成
- 与overlay系统集成
- 注册表系统支持
- 依赖注入机制

## 设计模式

### 1. 服务模式 (Service Pattern)
- 提供全局的对话框管理服务
- 统一的API接口

### 2. 代理模式 (Proxy Pattern)
- DialogWrapper作为组件代理
- 透明的属性传递

### 3. 观察者模式 (Observer Pattern)
- 响应式的状态管理
- 事件驱动的生命周期

## 注意事项

1. **内存管理**: 确保对话框关闭时正确清理资源
2. **状态同步**: 保持堆栈状态与实际显示的一致性
3. **性能优化**: 避免创建过多的对话框实例
4. **用户体验**: 合理控制对话框的层级和数量

## 扩展建议

1. **持久化**: 支持对话框状态的持久化
2. **动画管理**: 集成对话框的显示和隐藏动画
3. **主题支持**: 支持不同的对话框主题
4. **权限控制**: 基于用户权限控制对话框显示
5. **性能监控**: 监控对话框的性能指标

该对话框服务为Odoo Web应用提供了完整的对话框管理解决方案，通过堆栈管理和生命周期控制确保了对话框的正确显示和用户体验。

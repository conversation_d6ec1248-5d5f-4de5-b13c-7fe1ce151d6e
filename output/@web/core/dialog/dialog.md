# Dialog - 对话框组件

## 概述

`dialog.js` 是 Odoo Web 核心模块的对话框组件，提供了功能完整的模态对话框实现。该组件支持拖拽移动、多种尺寸、全屏模式、键盘导航、自定义样式和插槽内容，为Odoo Web应用提供了标准化的对话框用户界面，广泛应用于表单编辑、确认提示、内容展示和用户交互等场景。

## 文件信息
- **路径**: `/web/static/src/core/dialog/dialog.js`
- **行数**: 151
- **模块**: `@web/core/dialog/dialog`

## 依赖关系

```javascript
// 核心依赖
'@web/core/hotkeys/hotkey_hook'                    // 快捷键钩子
'@web/core/ui/ui_service'                          // UI服务
'@web/core/utils/hooks'                            // 工具钩子
'@odoo/owl'                                        // OWL框架
'@web/core/utils/timing'                           // 时间工具
'@web/core/utils/draggable_hook_builder_owl'       // 拖拽钩子构建器
```

## 核心功能

### 1. 拖拽钩子定义

```javascript
const useDialogDraggable = makeDraggableHook({
    name: "useDialogDraggable",
    onWillStartDrag({ ctx, addCleanup, addStyle, getRect }) {
        const { height, width } = getRect(ctx.current.element);
        ctx.current.container = document.createElement("div");
        addStyle(ctx.current.container, {
            position: "fixed",
            top: "0",
            bottom: `${70 - height}px`,
            left: `${70 - width}px`,
            right: `${70 - width}px`,
        });
        ctx.current.element.after(ctx.current.container);
        addCleanup(() => ctx.current.container.remove());
    },
    onDrop({ ctx, getRect }) {
        const { top, left } = getRect(ctx.current.element);
        return {
            left: left - ctx.current.elementRect.left,
            top: top - ctx.current.elementRect.top,
        };
    },
});
```

**拖拽钩子功能**:
- **拖拽准备**: 在拖拽开始时创建容器元素
- **边界限制**: 设置拖拽的边界范围
- **位置计算**: 计算拖拽后的相对位置
- **资源清理**: 拖拽结束后清理临时元素

### 2. 组件属性定义

```javascript
static props = {
    contentClass: { type: String, optional: true },
    bodyClass: { type: String, optional: true },
    fullscreen: { type: Boolean, optional: true },
    footer: { type: Boolean, optional: true },
    header: { type: Boolean, optional: true },
    size: {
        type: String,
        optional: true,
        validate: (s) => ["sm", "md", "lg", "xl", "fs", "fullscreen"].includes(s),
    },
    technical: { type: Boolean, optional: true },
    title: { type: String, optional: true },
    modalRef: { type: Function, optional: true },
    slots: {
        type: Object,
        shape: {
            default: Object, // Content is not optional
            header: { type: Object, optional: true },
            footer: { type: Object, optional: true },
        },
    },
    withBodyPadding: { type: Boolean, optional: true },
    onExpand: { type: Function, optional: true },
};
```

**属性功能**:
- **样式定制**: contentClass和bodyClass支持自定义样式
- **显示控制**: fullscreen、footer、header控制显示模式
- **尺寸选择**: size支持多种预定义尺寸
- **技术模式**: technical控制是否为技术对话框
- **内容插槽**: slots支持自定义头部、主体和底部内容
- **填充控制**: withBodyPadding控制主体内边距

### 3. 默认属性

```javascript
static defaultProps = {
    contentClass: "",
    bodyClass: "",
    fullscreen: false,
    footer: true,
    header: true,
    size: "lg",
    technical: true,
    title: "Odoo",
    withBodyPadding: true,
};
```

**默认属性功能**:
- **标准尺寸**: 默认使用大尺寸(lg)
- **完整结构**: 默认显示头部和底部
- **技术对话框**: 默认为技术类型对话框
- **标准标题**: 默认使用"Odoo"作为标题

### 4. 组件初始化

```javascript
setup() {
    this.modalRef = useForwardRefToParent("modalRef");
    useActiveElement("modalRef");
    this.data = useState(this.env.dialogData);
    
    // 快捷键设置
    useHotkey("escape", () => this.onEscape());
    useHotkey("control+enter", () => {
        const btns = document.querySelectorAll(
            ".o_dialog:not(.o_inactive_modal) .modal-footer button"
        );
        const firstVisibleBtn = Array.from(btns).find((btn) => {
            const styles = getComputedStyle(btn);
            return styles.display !== "none";
        });
        if (firstVisibleBtn) {
            firstVisibleBtn.click();
        }
    }, { bypassEditableProtection: true });
    
    this.id = `dialog_${this.data.id}`;
    useChildSubEnv({ inDialog: true, dialogId: this.id });
}
```

**初始化功能**:
- **引用转发**: 将模态框引用转发给父组件
- **活跃元素**: 设置活跃元素管理
- **状态管理**: 管理对话框数据状态
- **快捷键**: 设置ESC关闭和Ctrl+Enter确认
- **环境设置**: 为子组件提供对话框环境

### 5. 拖拽功能设置

```javascript
this.isMovable = this.props.header;
if (this.isMovable) {
    this.position = useState({ left: 0, top: 0 });
    useDialogDraggable({
        enable: () => !this.env.isSmall,
        ref: this.modalRef,
        elements: ".modal-content",
        handle: ".modal-header",
        ignore: "button, input",
        edgeScrolling: { enabled: false },
        onDrop: ({ top, left }) => {
            this.position.left += left;
            this.position.top += top;
        },
    });
    const throttledResize = throttleForAnimation(this.onResize.bind(this));
    useExternalListener(window, "resize", throttledResize);
}
```

**拖拽功能**:
- **条件启用**: 只有显示头部时才可拖拽
- **位置状态**: 管理对话框的位置状态
- **拖拽配置**: 配置拖拽的元素、手柄和忽略区域
- **位置更新**: 拖拽结束后更新位置
- **窗口调整**: 监听窗口大小变化重置位置

### 6. 计算属性

```javascript
get isFullscreen() {
    return this.props.fullscreen || this.env.isSmall;
}

get contentStyle() {
    if (this.isMovable) {
        return `top: ${this.position.top}px; left: ${this.position.left}px;`;
    }
    return "";
}
```

**计算属性功能**:
- **全屏判断**: 根据属性或环境判断是否全屏
- **内容样式**: 根据位置生成内容样式

### 7. 事件处理方法

```javascript
onResize() {
    this.position.left = 0;
    this.position.top = 0;
}

onEscape() {
    return this.dismiss();
}

async dismiss() {
    if (this.data.dismiss) {
        await this.data.dismiss();
    }
    return this.data.close();
}
```

**事件处理功能**:
- **窗口调整**: 重置对话框位置
- **ESC键**: 触发关闭操作
- **关闭处理**: 执行关闭回调并关闭对话框

## 使用场景

### 1. 基础对话框

```javascript
// 基础对话框使用
<Dialog
    title="基础对话框"
    size="md"
    onExpand={() => console.log('对话框展开')}
>
    <div slot="default">
        <p>这是对话框的主要内容</p>
    </div>
    <div slot="footer">
        <button class="btn btn-primary">确认</button>
        <button class="btn btn-secondary">取消</button>
    </div>
</Dialog>
```

### 2. 全屏对话框

```javascript
// 全屏对话框
<Dialog
    title="全屏编辑器"
    fullscreen={true}
    withBodyPadding={false}
    contentClass="h-100"
>
    <div slot="default" class="h-100">
        <textarea class="form-control h-100" placeholder="输入内容..."></textarea>
    </div>
</Dialog>
```

### 3. 自定义样式对话框

```javascript
// 自定义样式对话框
<Dialog
    title="自定义对话框"
    size="xl"
    contentClass="custom-dialog-content"
    bodyClass="p-0"
    technical={false}
>
    <div slot="header">
        <h4 class="modal-title">
            <i class="fa fa-cog"></i> 高级设置
        </h4>
    </div>
    <div slot="default">
        <div class="row">
            <div class="col-md-6">左侧内容</div>
            <div class="col-md-6">右侧内容</div>
        </div>
    </div>
</Dialog>
```

### 4. 无头部/底部对话框

```javascript
// 简洁对话框
<Dialog
    header={false}
    footer={false}
    size="sm"
    contentClass="border-0 shadow-lg"
>
    <div slot="default" class="text-center p-4">
        <i class="fa fa-spinner fa-spin fa-3x mb-3"></i>
        <p>正在处理，请稍候...</p>
    </div>
</Dialog>
```

## 增强示例

```javascript
// 增强的对话框组件
const EnhancedDialog = {
    createAdvancedDialog: () => {
        class AdvancedDialog extends Dialog {
            static props = {
                ...Dialog.props,
                resizable: { type: Boolean, optional: true },
                minimizable: { type: Boolean, optional: true },
                maximizable: { type: Boolean, optional: true },
                modal: { type: Boolean, optional: true },
                zIndex: { type: Number, optional: true },
                animation: { type: String, optional: true },
                onResize: { type: Function, optional: true },
                onMinimize: { type: Function, optional: true },
                onMaximize: { type: Function, optional: true }
            };

            static defaultProps = {
                ...Dialog.defaultProps,
                resizable: false,
                minimizable: false,
                maximizable: false,
                modal: true,
                animation: 'fade'
            };

            setup() {
                super.setup();
                
                // 增强状态
                this.enhancedState = useState({
                    isMinimized: false,
                    isMaximized: false,
                    originalSize: null,
                    originalPosition: null,
                    currentSize: { width: 'auto', height: 'auto' }
                });

                // 配置选项
                this.config = {
                    enableResize: this.props.resizable,
                    enableMinimize: this.props.minimizable,
                    enableMaximize: this.props.maximizable,
                    enableAnimation: !!this.props.animation
                };

                // 设置调整大小功能
                if (this.config.enableResize) {
                    this.setupResizable();
                }

                // 设置动画
                if (this.config.enableAnimation) {
                    this.setupAnimation();
                }
            }

            // 设置可调整大小
            setupResizable() {
                // 简化的调整大小实现
                this.resizeObserver = new ResizeObserver((entries) => {
                    for (const entry of entries) {
                        const { width, height } = entry.contentRect;
                        this.enhancedState.currentSize = { width, height };
                        
                        if (this.props.onResize) {
                            this.props.onResize({ width, height });
                        }
                    }
                });
            }

            // 设置动画
            setupAnimation() {
                const modalElement = this.modalRef.el;
                if (modalElement) {
                    modalElement.classList.add(`dialog-${this.props.animation}`);
                }
            }

            // 最小化对话框
            minimize() {
                if (this.enhancedState.isMinimized) {
                    return;
                }

                // 保存当前状态
                this.enhancedState.originalSize = { ...this.enhancedState.currentSize };
                this.enhancedState.originalPosition = { ...this.position };

                // 设置最小化状态
                this.enhancedState.isMinimized = true;
                this.enhancedState.isMaximized = false;

                // 触发回调
                if (this.props.onMinimize) {
                    this.props.onMinimize();
                }
            }

            // 最大化对话框
            maximize() {
                if (this.enhancedState.isMaximized) {
                    this.restore();
                    return;
                }

                // 保存当前状态
                if (!this.enhancedState.isMinimized) {
                    this.enhancedState.originalSize = { ...this.enhancedState.currentSize };
                    this.enhancedState.originalPosition = { ...this.position };
                }

                // 设置最大化状态
                this.enhancedState.isMaximized = true;
                this.enhancedState.isMinimized = false;

                // 重置位置
                this.position.left = 0;
                this.position.top = 0;

                // 触发回调
                if (this.props.onMaximize) {
                    this.props.onMaximize();
                }
            }

            // 恢复对话框
            restore() {
                if (this.enhancedState.originalSize) {
                    this.enhancedState.currentSize = { ...this.enhancedState.originalSize };
                }

                if (this.enhancedState.originalPosition) {
                    this.position.left = this.enhancedState.originalPosition.left;
                    this.position.top = this.enhancedState.originalPosition.top;
                }

                this.enhancedState.isMinimized = false;
                this.enhancedState.isMaximized = false;
                this.enhancedState.originalSize = null;
                this.enhancedState.originalPosition = null;
            }

            // 增强的内容样式
            get contentStyle() {
                let style = super.contentStyle;

                // 添加z-index
                if (this.props.zIndex) {
                    style += `z-index: ${this.props.zIndex};`;
                }

                // 添加尺寸样式
                if (this.config.enableResize && !this.enhancedState.isMaximized) {
                    const { width, height } = this.enhancedState.currentSize;
                    if (width !== 'auto') {
                        style += `width: ${width}px;`;
                    }
                    if (height !== 'auto') {
                        style += `height: ${height}px;`;
                    }
                }

                // 最小化样式
                if (this.enhancedState.isMinimized) {
                    style += 'transform: scale(0.1); opacity: 0.5;';
                }

                // 最大化样式
                if (this.enhancedState.isMaximized) {
                    style += 'width: 100vw; height: 100vh; max-width: none; max-height: none;';
                }

                return style;
            }

            // 获取对话框状态
            getDialogState() {
                return {
                    isMinimized: this.enhancedState.isMinimized,
                    isMaximized: this.enhancedState.isMaximized,
                    isFullscreen: this.isFullscreen,
                    position: { ...this.position },
                    size: { ...this.enhancedState.currentSize }
                };
            }

            // 设置对话框位置
            setPosition(left, top) {
                this.position.left = left;
                this.position.top = top;
            }

            // 设置对话框大小
            setSize(width, height) {
                this.enhancedState.currentSize = { width, height };
            }

            // 居中对话框
            center() {
                const modalElement = this.modalRef.el;
                if (modalElement) {
                    const rect = modalElement.getBoundingClientRect();
                    const windowWidth = window.innerWidth;
                    const windowHeight = window.innerHeight;

                    this.position.left = (windowWidth - rect.width) / 2;
                    this.position.top = (windowHeight - rect.height) / 2;
                }
            }

            // 组件销毁时清理
            willDestroy() {
                if (this.resizeObserver) {
                    this.resizeObserver.disconnect();
                }
                super.willDestroy && super.willDestroy();
            }
        }

        return AdvancedDialog;
    }
};

// 使用示例
const AdvancedDialog = EnhancedDialog.createAdvancedDialog();

// 可调整大小的对话框
<AdvancedDialog
    title="高级对话框"
    size="lg"
    resizable={true}
    minimizable={true}
    maximizable={true}
    animation="slide"
    zIndex={1050}
    onResize={(size) => {
        console.log('对话框大小变化:', size);
    }}
    onMinimize={() => {
        console.log('对话框最小化');
    }}
    onMaximize={() => {
        console.log('对话框最大化');
    }}
>
    <div slot="header">
        <h4 class="modal-title">可调整大小的对话框</h4>
        <div class="dialog-controls">
            <button class="btn btn-sm" onClick={() => this.minimize()}>
                <i class="fa fa-minus"></i>
            </button>
            <button class="btn btn-sm" onClick={() => this.maximize()}>
                <i class="fa fa-expand"></i>
            </button>
        </div>
    </div>
    <div slot="default">
        <p>这是一个功能增强的对话框，支持调整大小、最小化和最大化。</p>
    </div>
</AdvancedDialog>
```

## 技术特点

### 1. 拖拽支持
- 基于头部的拖拽移动
- 边界限制和位置计算
- 响应式拖拽禁用

### 2. 键盘导航
- ESC键关闭对话框
- Ctrl+Enter快速确认
- 可编辑元素保护

### 3. 响应式设计
- 小屏幕自动全屏
- 窗口调整时重置位置
- 灵活的尺寸选项

### 4. 插槽系统
- 头部、主体、底部插槽
- 灵活的内容定制
- 可选的结构元素

## 设计模式

### 1. 组件模式 (Component Pattern)
- 封装对话框逻辑
- 可重用的UI组件

### 2. 模板方法模式 (Template Method Pattern)
- 定义对话框的基本结构
- 子类可以重写特定部分

### 3. 观察者模式 (Observer Pattern)
- 事件驱动的交互
- 状态变化通知

## 注意事项

1. **性能优化**: 合理使用拖拽和动画功能
2. **无障碍性**: 确保键盘导航和屏幕阅读器支持
3. **移动适配**: 在小屏幕设备上的适配
4. **内存管理**: 及时清理事件监听器和观察器

## 扩展建议

1. **动画效果**: 添加更多的显示和隐藏动画
2. **主题支持**: 支持多种视觉主题
3. **状态持久化**: 保存对话框的位置和大小
4. **多对话框管理**: 支持多个对话框的层级管理
5. **插件系统**: 支持插件扩展对话框功能

该对话框组件为Odoo Web应用提供了功能完整的模态对话框解决方案，通过拖拽、键盘导航和响应式设计确保了优秀的用户体验。

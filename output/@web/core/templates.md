# @web/core/templates.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/templates.js`
- **原始路径**: `/web/static/src/core/templates.js`
- **代码行数**: 180行
- **作用**: 提供模板注册、继承和管理系统，是Odoo Web框架的模板引擎核心

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 模板注册和管理的完整机制
- 模板继承链的构建和解析
- 模板扩展系统的设计原理
- 模板缓存和性能优化策略
- 企业级模板引擎的架构设计

## 📚 核心概念

### 什么是Templates系统？
Templates系统是Odoo Web框架的**模板引擎核心**，主要功能：
- **模板注册**: 注册和管理XML模板
- **继承处理**: 处理模板的继承关系
- **扩展支持**: 支持模板的动态扩展
- **缓存管理**: 智能的模板缓存和性能优化

### 核心架构组成
```javascript
// 模板存储
const templates = {};              // 原始模板字符串
const parsedTemplates = {};        // 解析后的模板DOM
const processedTemplates = {};     // 处理后的最终模板

// 扩展系统
const templateExtensions = {};     // 模板扩展定义
const parsedTemplateExtensions = {}; // 解析后的扩展

// 处理器系统
const templateProcessors = [];     // 模板处理器列表
let urlFilters = [];              // URL过滤器
```

### 基本使用模式
```javascript
// 注册基础模板
registerTemplate("base_form", "forms/base.xml", `
    <form t-name="base_form" class="o_form">
        <div class="o_form_sheet">
            <h1>Base Form</h1>
            <div class="o_form_content">
                <!-- 内容区域 -->
            </div>
        </div>
    </form>
`);

// 注册继承模板
registerTemplate("enhanced_form", "forms/enhanced.xml", `
    <form t-name="enhanced_form" t-inherit="base_form" class="o_form enhanced">
        <h1 position="after">
            <p class="form-subtitle">Enhanced Form</p>
        </h1>
        <div class="o_form_content" position="inside">
            <div class="enhanced-controls">
                <button class="btn btn-primary">Save</button>
            </div>
        </div>
    </form>
`);

// 注册模板扩展
registerTemplateExtension("base_form", "extensions/form_ext.xml", `
    <xpath expr="//div[@class='o_form_content']" position="inside">
        <div class="extension-content">
            <p>This is an extension</p>
        </div>
    </xpath>
`);

// 获取处理后的模板
const template = getTemplate("enhanced_form");
```

## 🔍 核心实现详解

### 1. 模板注册系统

#### registerTemplate() - 基础模板注册
```javascript
function registerTemplate(name, url, templateString) {
    // 1. 防重复注册检查
    if (isRegistered(...arguments)) {
        return;
    }
    
    // 2. 块类型管理
    if (blockType !== "templates") {
        blockType = "templates";
        blockId++;
    }
    
    // 3. 冲突检查
    if (name in templates && (info[name].url !== url || templates[name] !== templateString)) {
        throw new Error(`Template ${name} already exists`);
    }
    
    // 4. 存储模板
    templates[name] = templateString;
    info[name] = { blockId, url };
}
```

**注册机制特点**：
- **防重复**: 使用参数签名防止重复注册
- **块管理**: 通过blockId管理模板的加载顺序
- **冲突检测**: 检测同名模板的内容冲突
- **元信息**: 记录模板的来源URL和块ID

#### registerTemplateExtension() - 模板扩展注册
```javascript
function registerTemplateExtension(inheritFrom, url, templateString) {
    if (isRegistered(...arguments)) {
        return;
    }
    
    // 块类型切换
    if (blockType !== "extensions") {
        blockType = "extensions";
        blockId++;
    }
    
    // 初始化扩展结构
    if (!templateExtensions[inheritFrom]) {
        templateExtensions[inheritFrom] = [];
    }
    if (!templateExtensions[inheritFrom][blockId]) {
        templateExtensions[inheritFrom][blockId] = [];
    }
    
    // 添加扩展
    templateExtensions[inheritFrom][blockId].push({
        templateString,
        url,
    });
}
```

**扩展系统设计**：
- **目标导向**: 按目标模板名称组织扩展
- **块分组**: 同一块ID的扩展一起处理
- **顺序保证**: 保持扩展的注册和应用顺序

### 2. 模板解析和处理

#### getParsedTemplate() - 模板解析
```javascript
function getParsedTemplate(templateString) {
    const doc = parser.parseFromString(templateString, "text/xml");
    
    // 应用所有模板处理器
    for (const processor of templateProcessors) {
        processor(doc);
    }
    
    return doc.firstChild;
}
```

#### _getTemplate() - 核心模板获取逻辑
```javascript
function _getTemplate(name, blockId = null) {
    // 1. 基础模板解析
    if (!(name in parsedTemplates)) {
        if (!(name in templates)) {
            return null;
        }
        const templateString = templates[name];
        parsedTemplates[name] = getParsedTemplate(templateString);
    }
    let processedTemplate = parsedTemplates[name];
    
    // 2. 处理继承关系
    const inheritFrom = processedTemplate.getAttribute("t-inherit");
    if (inheritFrom) {
        const parentTemplate = _getTemplate(inheritFrom, blockId || info[name].blockId);
        if (!parentTemplate) {
            throw new Error(`Template parent ${inheritFrom} not found`);
        }
        
        const element = getClone(processedTemplate);
        processedTemplate = applyInheritance(getClone(parentTemplate), element, info[name].url);
        
        // 处理标签名不匹配的情况
        if (processedTemplate.tagName !== element.tagName) {
            const temp = processedTemplate;
            processedTemplate = new Document().createElement(element.tagName);
            processedTemplate.append(...temp.childNodes);
        }
        
        // 复制属性（排除继承相关属性）
        for (const { name, value } of element.attributes) {
            if (!["t-inherit", "t-inherit-mode"].includes(name)) {
                processedTemplate.setAttribute(name, value);
            }
        }
    }
    
    // 3. 应用模板扩展
    for (const otherBlockId in templateExtensions[name] || {}) {
        if (blockId && otherBlockId > blockId) {
            break; // 只应用当前块ID之前的扩展
        }
        
        // 解析扩展（如果尚未解析）
        if (!(name in parsedTemplateExtensions)) {
            parsedTemplateExtensions[name] = {};
        }
        if (!(otherBlockId in parsedTemplateExtensions[name])) {
            parsedTemplateExtensions[name][otherBlockId] = [];
            for (const { templateString, url } of templateExtensions[name][otherBlockId]) {
                parsedTemplateExtensions[name][otherBlockId].push({
                    template: getParsedTemplate(templateString),
                    url,
                });
            }
        }
        
        // 应用扩展
        for (const { template, url } of parsedTemplateExtensions[name][otherBlockId]) {
            // URL过滤检查
            if (!urlFilters.every((filter) => filter(url))) {
                continue;
            }
            
            processedTemplate = applyInheritance(
                inheritFrom ? processedTemplate : getClone(processedTemplate),
                getClone(template),
                url
            );
        }
    }
    
    return processedTemplate;
}
```

**处理流程**：
1. **基础解析**: 解析原始模板字符串为DOM
2. **继承处理**: 递归处理模板继承链
3. **扩展应用**: 按块ID顺序应用所有扩展
4. **过滤检查**: 应用URL过滤器筛选扩展

### 3. 缓存和性能优化

#### getTemplate() - 带缓存的模板获取
```javascript
function getTemplate(name) {
    if (!processedTemplates[name]) {
        processedTemplates[name] = _getTemplate(name);
    }
    return processedTemplates[name];
}
```

#### getClone() - 安全的模板克隆
```javascript
function getClone(template) {
    const c = template.cloneNode(true);
    new Document().append(c); // 确保克隆节点有正确的文档上下文
    return c;
}
```

**缓存策略**：
- **三级缓存**: 原始模板、解析模板、处理模板的分层缓存
- **懒加载**: 只在需要时解析和处理模板
- **安全克隆**: 确保模板修改不影响缓存

### 4. 工具函数和辅助系统

#### isRegistered() - 重复注册检查
```javascript
const registered = new Set();
function isRegistered(...args) {
    const key = JSON.stringify([...args]);
    if (registered.has(key)) {
        return true;
    }
    registered.add(key);
    return false;
}
```

#### 块管理系统
```javascript
let blockType = null;  // 当前块类型: "templates" | "extensions"
let blockId = 0;       // 当前块ID，用于排序
```

**块管理作用**：
- **加载顺序**: 确保模板和扩展的正确加载顺序
- **依赖管理**: 处理模板间的依赖关系
- **扩展控制**: 控制扩展的应用范围

## 🎨 实际应用场景

### 1. 模块化视图系统
```javascript
// 基础列表视图模板
registerTemplate("base_list_view", "views/list.xml", `
    <div t-name="base_list_view" class="o_list_view">
        <div class="o_list_header">
            <h2>List View</h2>
        </div>
        <table class="o_list_table">
            <thead>
                <tr class="o_list_header_row">
                    <!-- 表头将在这里插入 -->
                </tr>
            </thead>
            <tbody class="o_list_body">
                <!-- 数据行将在这里插入 -->
            </tbody>
        </table>
        <div class="o_list_footer">
            <!-- 分页控件 -->
        </div>
    </div>
`);

// 增强的列表视图（继承基础视图）
registerTemplate("enhanced_list_view", "views/enhanced_list.xml", `
    <div t-name="enhanced_list_view" t-inherit="base_list_view" class="o_list_view enhanced">
        <div class="o_list_header" position="inside">
            <div class="o_list_controls">
                <button class="btn btn-primary o_list_button_add">Add</button>
                <button class="btn btn-secondary o_list_button_import">Import</button>
            </div>
        </div>

        <table class="o_list_table" position="attributes">
            <attribute name="class" add="table-striped table-hover"/>
        </table>

        <div class="o_list_footer" position="inside">
            <div class="o_list_pagination">
                <button class="btn btn-sm btn-outline-secondary">Previous</button>
                <span class="o_list_page_info">Page 1 of 10</span>
                <button class="btn btn-sm btn-outline-secondary">Next</button>
            </div>
        </div>
    </div>
`);

// 为特定模型扩展列表视图
registerTemplateExtension("enhanced_list_view", "models/partner_list_ext.xml", `
    <div class="o_list_controls" position="inside">
        <button class="btn btn-info o_partner_merge">Merge Partners</button>
    </div>

    <thead position="inside">
        <tr class="o_list_header_row" position="inside">
            <th>Name</th>
            <th>Email</th>
            <th>Phone</th>
            <th>Actions</th>
        </tr>
    </thead>
`);

// 使用模板系统的组件
class ListView extends Component {
    static template = "enhanced_list_view";

    setup() {
        this.records = useState([]);
        this.pagination = useState({
            currentPage: 1,
            totalPages: 10,
            pageSize: 20
        });
    }

    addRecord() {
        // 添加记录逻辑
    }

    importRecords() {
        // 导入记录逻辑
    }

    mergePartners() {
        // 合并伙伴逻辑（只在partner模型中可用）
    }
}
```

### 2. 动态表单模板系统
```javascript
class DynamicFormTemplateManager {
    constructor() {
        this.formTemplates = new Map();
        this.fieldTemplates = new Map();
        this.initializeBaseTemplates();
    }

    initializeBaseTemplates() {
        // 注册基础表单模板
        registerTemplate("base_form", "forms/base.xml", `
            <form t-name="base_form" class="o_form_view">
                <div class="o_form_sheet">
                    <div class="o_form_header">
                        <h1 class="o_form_title">Form</h1>
                    </div>
                    <div class="o_form_content">
                        <!-- 字段内容区域 -->
                    </div>
                    <div class="o_form_footer">
                        <button type="submit" class="btn btn-primary">Save</button>
                        <button type="button" class="btn btn-secondary">Cancel</button>
                    </div>
                </div>
            </form>
        `);

        // 注册字段模板
        this.registerFieldTemplates();
    }

    registerFieldTemplates() {
        const fieldTypes = {
            char: `
                <div t-name="field_char" class="o_field_char">
                    <input type="text" class="o_input" t-att-name="props.name" t-model="props.value"/>
                </div>
            `,
            text: `
                <div t-name="field_text" class="o_field_text">
                    <textarea class="o_input" t-att-name="props.name" t-model="props.value" rows="3"/>
                </div>
            `,
            integer: `
                <div t-name="field_integer" class="o_field_integer">
                    <input type="number" class="o_input" t-att-name="props.name" t-model="props.value"/>
                </div>
            `,
            boolean: `
                <div t-name="field_boolean" class="o_field_boolean">
                    <input type="checkbox" class="o_input" t-att-name="props.name" t-model="props.value"/>
                </div>
            `,
            selection: `
                <div t-name="field_selection" class="o_field_selection">
                    <select class="o_input" t-att-name="props.name" t-model="props.value">
                        <option value="">-- Select --</option>
                        <t t-foreach="props.options" t-as="option" t-key="option[0]">
                            <option t-att-value="option[0]" t-esc="option[1]"/>
                        </t>
                    </select>
                </div>
            `
        };

        for (const [type, template] of Object.entries(fieldTypes)) {
            registerTemplate(`field_${type}`, `fields/${type}.xml`, template);
        }
    }

    // 创建自定义表单模板
    createFormTemplate(formName, config) {
        const { title, fields, layout = "vertical" } = config;

        // 构建字段扩展
        const fieldExtensions = fields.map(field => {
            const fieldTemplate = this.generateFieldTemplate(field);
            return `
                <div class="o_form_content" position="inside">
                    ${fieldTemplate}
                </div>
            `;
        }).join('\n');

        // 注册继承模板
        registerTemplate(formName, `forms/${formName}.xml`, `
            <form t-name="${formName}" t-inherit="base_form" class="o_form_view ${layout}">
                <h1 class="o_form_title" position="replace">
                    <h1 class="o_form_title">${title}</h1>
                </h1>
                ${fieldExtensions}
            </form>
        `);

        return formName;
    }

    generateFieldTemplate(fieldConfig) {
        const { name, type, label, required, readonly, options } = fieldConfig;

        const fieldClass = `o_field_${type}`;
        const requiredClass = required ? 'o_required_modifier' : '';
        const readonlyClass = readonly ? 'o_readonly_modifier' : '';

        return `
            <div class="o_field_wrapper ${fieldClass} ${requiredClass} ${readonlyClass}">
                <label class="o_form_label" for="${name}">
                    ${label}${required ? ' *' : ''}
                </label>
                <div class="o_field_widget">
                    <t t-call="field_${type}" t-props="{
                        name: '${name}',
                        value: record.${name},
                        options: ${JSON.stringify(options || {})}
                    }"/>
                </div>
            </div>
        `;
    }

    // 添加字段到现有表单
    addFieldToForm(formName, fieldConfig) {
        const fieldTemplate = this.generateFieldTemplate(fieldConfig);

        registerTemplateExtension(formName, `forms/${formName}_field_${fieldConfig.name}.xml`, `
            <div class="o_form_content" position="inside">
                ${fieldTemplate}
            </div>
        `);
    }

    // 修改表单样式
    modifyFormStyle(formName, styleConfig) {
        const { className, attributes } = styleConfig;

        let attributeModifications = '';
        if (attributes) {
            attributeModifications = Object.entries(attributes)
                .map(([name, value]) => `<attribute name="${name}">${value}</attribute>`)
                .join('\n');
        }

        registerTemplateExtension(formName, `forms/${formName}_style.xml`, `
            <form position="attributes">
                ${className ? `<attribute name="class" add="${className}"/>` : ''}
                ${attributeModifications}
            </form>
        `);
    }
}

// 使用示例
const formManager = new DynamicFormTemplateManager();

// 创建客户表单
const customerFormName = formManager.createFormTemplate("customer_form", {
    title: "Customer Information",
    fields: [
        {
            name: "name",
            type: "char",
            label: "Customer Name",
            required: true
        },
        {
            name: "email",
            type: "char",
            label: "Email Address",
            required: true
        },
        {
            name: "phone",
            type: "char",
            label: "Phone Number"
        },
        {
            name: "active",
            type: "boolean",
            label: "Active"
        },
        {
            name: "category",
            type: "selection",
            label: "Category",
            options: [
                ["individual", "Individual"],
                ["company", "Company"]
            ]
        }
    ],
    layout: "vertical"
});

// 添加额外字段
formManager.addFieldToForm(customerFormName, {
    name: "notes",
    type: "text",
    label: "Notes"
});

// 修改表单样式
formManager.modifyFormStyle(customerFormName, {
    className: "customer-form-enhanced",
    attributes: {
        "data-form-type": "customer"
    }
});

// 在组件中使用
class CustomerForm extends Component {
    static template = "customer_form";

    setup() {
        this.record = useState({
            name: "",
            email: "",
            phone: "",
            active: true,
            category: "",
            notes: ""
        });
    }
}
```

### 3. 模板缓存管理器
```javascript
class TemplateCacheManager {
    constructor() {
        this.cacheStats = {
            hits: 0,
            misses: 0,
            invalidations: 0
        };
        this.dependencyGraph = new Map();
        this.observers = new Set();
    }

    // 构建依赖图
    buildDependencyGraph() {
        this.dependencyGraph.clear();

        // 分析模板继承关系
        for (const [templateName, templateString] of Object.entries(templates)) {
            const parsed = getParsedTemplate(templateString);
            const inheritFrom = parsed.getAttribute("t-inherit");

            if (inheritFrom) {
                if (!this.dependencyGraph.has(inheritFrom)) {
                    this.dependencyGraph.set(inheritFrom, new Set());
                }
                this.dependencyGraph.get(inheritFrom).add(templateName);
            }
        }

        // 分析扩展关系
        for (const [targetTemplate, extensions] of Object.entries(templateExtensions)) {
            if (!this.dependencyGraph.has(targetTemplate)) {
                this.dependencyGraph.set(targetTemplate, new Set());
            }
            // 扩展也会影响目标模板
        }
    }

    // 智能缓存失效
    invalidateTemplate(templateName) {
        this.cacheStats.invalidations++;

        // 清除直接缓存
        delete processedTemplates[templateName];
        delete parsedTemplates[templateName];

        // 递归清除依赖模板的缓存
        const dependents = this.dependencyGraph.get(templateName);
        if (dependents) {
            for (const dependent of dependents) {
                this.invalidateTemplate(dependent);
            }
        }

        // 通知观察者
        this.notifyObservers('invalidate', templateName);
    }

    // 预热缓存
    async warmupCache(templateNames) {
        const warmupPromises = templateNames.map(async (name) => {
            try {
                const template = getTemplate(name);
                if (template) {
                    console.log(`Warmed up template: ${name}`);
                }
            } catch (error) {
                console.warn(`Failed to warm up template ${name}:`, error);
            }
        });

        await Promise.all(warmupPromises);
    }

    // 获取缓存统计
    getCacheStats() {
        const totalRequests = this.cacheStats.hits + this.cacheStats.misses;
        const hitRate = totalRequests > 0 ? (this.cacheStats.hits / totalRequests * 100).toFixed(2) : 0;

        return {
            ...this.cacheStats,
            hitRate: `${hitRate}%`,
            totalRequests,
            cacheSize: {
                templates: Object.keys(templates).length,
                parsedTemplates: Object.keys(parsedTemplates).length,
                processedTemplates: Object.keys(processedTemplates).length,
                extensions: Object.keys(templateExtensions).length
            }
        };
    }

    // 内存使用分析
    analyzeMemoryUsage() {
        const analysis = {
            templates: this.calculateObjectSize(templates),
            parsedTemplates: this.calculateObjectSize(parsedTemplates),
            processedTemplates: this.calculateObjectSize(processedTemplates),
            extensions: this.calculateObjectSize(templateExtensions)
        };

        analysis.total = Object.values(analysis).reduce((sum, size) => sum + size, 0);

        return analysis;
    }

    calculateObjectSize(obj) {
        // 简化的内存大小估算
        const jsonString = JSON.stringify(obj);
        return jsonString.length * 2; // 假设每个字符占用2字节
    }

    // 清理未使用的缓存
    cleanupUnusedCache() {
        const usageTracker = new Map();

        // 包装getTemplate以跟踪使用情况
        const originalGetTemplate = getTemplate;
        getTemplate = function(name) {
            usageTracker.set(name, Date.now());
            return originalGetTemplate(name);
        };

        // 定期清理
        setInterval(() => {
            const now = Date.now();
            const maxAge = 30 * 60 * 1000; // 30分钟

            for (const [templateName, lastUsed] of usageTracker.entries()) {
                if (now - lastUsed > maxAge) {
                    delete processedTemplates[templateName];
                    usageTracker.delete(templateName);
                    console.log(`Cleaned up unused template: ${templateName}`);
                }
            }
        }, 5 * 60 * 1000); // 每5分钟检查一次
    }

    // 添加观察者
    addObserver(callback) {
        this.observers.add(callback);
        return () => this.observers.delete(callback);
    }

    // 通知观察者
    notifyObservers(event, data) {
        for (const observer of this.observers) {
            try {
                observer(event, data);
            } catch (error) {
                console.error('Observer error:', error);
            }
        }
    }

    // 导出缓存状态
    exportCacheState() {
        return {
            templates: { ...templates },
            parsedTemplates: Object.keys(parsedTemplates),
            processedTemplates: Object.keys(processedTemplates),
            extensions: { ...templateExtensions },
            stats: this.getCacheStats(),
            dependencyGraph: Object.fromEntries(
                Array.from(this.dependencyGraph.entries()).map(([key, value]) => [
                    key,
                    Array.from(value)
                ])
            )
        };
    }
}

// 使用示例
const cacheManager = new TemplateCacheManager();

// 构建依赖图
cacheManager.buildDependencyGraph();

// 预热常用模板
await cacheManager.warmupCache([
    'base_form',
    'enhanced_list_view',
    'customer_form'
]);

// 监听缓存事件
cacheManager.addObserver((event, data) => {
    console.log(`Cache event: ${event}`, data);
});

// 获取缓存统计
const stats = cacheManager.getCacheStats();
console.log('Cache Statistics:', stats);

// 分析内存使用
const memoryUsage = cacheManager.analyzeMemoryUsage();
console.log('Memory Usage:', memoryUsage);

// 启用自动清理
cacheManager.cleanupUnusedCache();
```

## 🛠️ 实践练习

### 练习1: 模板版本管理器
```javascript
class TemplateVersionManager {
    constructor() {
        this.versions = new Map();
        this.currentVersion = "1.0.0";
        this.migrations = new Map();
    }

    // 注册版本化模板
    registerVersionedTemplate(name, version, url, templateString) {
        if (!this.versions.has(name)) {
            this.versions.set(name, new Map());
        }

        this.versions.get(name).set(version, {
            url,
            templateString,
            registeredAt: Date.now()
        });

        // 注册到主模板系统
        registerTemplate(`${name}_v${version}`, url, templateString);
    }

    // 获取指定版本的模板
    getVersionedTemplate(name, version = null) {
        const templateVersions = this.versions.get(name);
        if (!templateVersions) {
            return null;
        }

        const targetVersion = version || this.getLatestVersion(name);
        return templateVersions.get(targetVersion);
    }

    // 获取最新版本
    getLatestVersion(templateName) {
        const versions = this.versions.get(templateName);
        if (!versions) return null;

        const versionNumbers = Array.from(versions.keys())
            .sort((a, b) => this.compareVersions(b, a));

        return versionNumbers[0];
    }

    // 版本比较
    compareVersions(v1, v2) {
        const parts1 = v1.split('.').map(Number);
        const parts2 = v2.split('.').map(Number);

        for (let i = 0; i < Math.max(parts1.length, parts2.length); i++) {
            const part1 = parts1[i] || 0;
            const part2 = parts2[i] || 0;

            if (part1 > part2) return 1;
            if (part1 < part2) return -1;
        }

        return 0;
    }

    // 注册迁移规则
    registerMigration(templateName, fromVersion, toVersion, migrationFn) {
        const key = `${templateName}:${fromVersion}->${toVersion}`;
        this.migrations.set(key, migrationFn);
    }

    // 执行模板迁移
    migrateTemplate(templateName, fromVersion, toVersion) {
        const migrationKey = `${templateName}:${fromVersion}->${toVersion}`;
        const migration = this.migrations.get(migrationKey);

        if (!migration) {
            throw new Error(`No migration found for ${migrationKey}`);
        }

        const sourceTemplate = this.getVersionedTemplate(templateName, fromVersion);
        if (!sourceTemplate) {
            throw new Error(`Source template ${templateName} v${fromVersion} not found`);
        }

        const migratedTemplate = migration(sourceTemplate.templateString);

        // 注册迁移后的模板
        this.registerVersionedTemplate(
            templateName,
            toVersion,
            sourceTemplate.url,
            migratedTemplate
        );

        return migratedTemplate;
    }

    // 批量迁移
    batchMigrate(templateName, targetVersion) {
        const versions = Array.from(this.versions.get(templateName).keys())
            .sort((a, b) => this.compareVersions(a, b));

        let currentVersion = versions[0];

        while (this.compareVersions(currentVersion, targetVersion) < 0) {
            const nextVersionIndex = versions.indexOf(currentVersion) + 1;
            if (nextVersionIndex >= versions.length) break;

            const nextVersion = versions[nextVersionIndex];
            this.migrateTemplate(templateName, currentVersion, nextVersion);
            currentVersion = nextVersion;
        }
    }
}

// 使用示例
const versionManager = new TemplateVersionManager();

// 注册不同版本的模板
versionManager.registerVersionedTemplate("user_form", "1.0.0", "forms/user_v1.xml", `
    <form t-name="user_form_v1.0.0" class="user-form">
        <input name="name" type="text"/>
        <input name="email" type="email"/>
    </form>
`);

versionManager.registerVersionedTemplate("user_form", "2.0.0", "forms/user_v2.xml", `
    <form t-name="user_form_v2.0.0" class="user-form enhanced">
        <div class="form-group">
            <label>Name:</label>
            <input name="name" type="text" required=""/>
        </div>
        <div class="form-group">
            <label>Email:</label>
            <input name="email" type="email" required=""/>
        </div>
        <div class="form-group">
            <label>Phone:</label>
            <input name="phone" type="tel"/>
        </div>
    </form>
`);

// 注册迁移规则
versionManager.registerMigration("user_form", "1.0.0", "2.0.0", (templateString) => {
    // 将简单输入转换为表单组
    return templateString
        .replace(/<input name="(\w+)" type="(\w+)"\/>/g,
            '<div class="form-group"><label>$1:</label><input name="$1" type="$2" required=""/></div>')
        .replace('class="user-form"', 'class="user-form enhanced"')
        + '<div class="form-group"><label>Phone:</label><input name="phone" type="tel"/></div>';
});

// 执行迁移
const migratedTemplate = versionManager.migrateTemplate("user_form", "1.0.0", "2.0.0");
console.log('Migrated template:', migratedTemplate);
```

## 🔧 调试技巧

### 查看模板系统状态
```javascript
function debugTemplateSystem() {
    console.group('Template System Debug');

    console.log('Registered templates:', Object.keys(templates));
    console.log('Parsed templates:', Object.keys(parsedTemplates));
    console.log('Processed templates:', Object.keys(processedTemplates));
    console.log('Template extensions:', Object.keys(templateExtensions));

    // 显示继承关系
    const inheritanceMap = {};
    for (const [name, templateString] of Object.entries(templates)) {
        const parsed = getParsedTemplate(templateString);
        const inheritFrom = parsed.getAttribute("t-inherit");
        if (inheritFrom) {
            inheritanceMap[name] = inheritFrom;
        }
    }
    console.log('Inheritance relationships:', inheritanceMap);

    console.groupEnd();
}

// 在控制台中调用
debugTemplateSystem();
```

### 模板处理过程跟踪
```javascript
function traceTemplateProcessing(templateName) {
    console.group(`Template Processing Trace: ${templateName}`);

    // 包装关键函数以添加跟踪
    const originalGetTemplate = _getTemplate;
    _getTemplate = function(name, blockId) {
        console.log(`Processing template: ${name}, blockId: ${blockId}`);

        const result = originalGetTemplate.call(this, name, blockId);

        if (result) {
            console.log(`Template processed successfully: ${name}`);
            console.log('Final template:', new XMLSerializer().serializeToString(result));
        } else {
            console.warn(`Template not found: ${name}`);
        }

        return result;
    };

    // 执行模板获取
    const template = getTemplate(templateName);

    // 恢复原始函数
    _getTemplate = originalGetTemplate;

    console.groupEnd();
    return template;
}

// 跟踪特定模板的处理过程
// traceTemplateProcessing('enhanced_form');
```

## 📊 性能考虑

### 优化策略
1. **分层缓存**: 原始、解析、处理三级缓存减少重复工作
2. **懒加载**: 只在需要时解析和处理模板
3. **块管理**: 通过块ID控制扩展的应用顺序
4. **URL过滤**: 条件性应用扩展减少不必要的处理

### 最佳实践
```javascript
// ✅ 好的做法：使用简单的继承结构
registerTemplate("enhanced_form", "forms/enhanced.xml", `
    <form t-name="enhanced_form" t-inherit="base_form">
        <!-- 简单的扩展 -->
    </form>
`);

// ❌ 不好的做法：复杂的多层继承
registerTemplate("complex_form", "forms/complex.xml", `
    <form t-name="complex_form" t-inherit="enhanced_form">
        <!-- 继承已经继承的模板 -->
    </form>
`);

// ✅ 好的做法：合理使用扩展
registerTemplateExtension("base_form", "extensions/simple.xml", `
    <div class="content" position="inside">
        <p>Simple extension</p>
    </div>
`);

// ❌ 不好的做法：过多的小扩展
registerTemplateExtension("base_form", "extensions/tiny1.xml", `<span>1</span>`);
registerTemplateExtension("base_form", "extensions/tiny2.xml", `<span>2</span>`);
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解模板注册和管理的完整机制
- [ ] 掌握模板继承链的构建和解析
- [ ] 理解模板扩展系统的设计原理
- [ ] 能够创建复杂的模板管理系统
- [ ] 掌握模板缓存和性能优化策略
- [ ] 了解企业级模板引擎的架构设计

## 🚀 下一步学习
学完Templates系统后，建议继续学习：
1. **模板继承** (回顾`@web/core/template_inheritance.js`) - 深入理解继承机制
2. **视图系统** (`@web/views/`) - 学习模板在视图中的应用
3. **组件系统** (`@odoo/owl`) - 理解组件与模板的关系

## 💡 重要提示
- Templates系统是Odoo视图定制的基础
- 理解块管理对控制扩展顺序很重要
- 缓存机制对大型应用的性能至关重要
- 模板继承和扩展提供了强大的定制能力
```
```

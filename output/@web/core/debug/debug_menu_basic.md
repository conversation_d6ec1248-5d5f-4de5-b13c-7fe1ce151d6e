# DebugMenuBasic - 基础调试菜单组件

## 概述

`debug_menu_basic.js` 是 Odoo Web 核心模块的基础调试菜单组件，提供了开发者调试工具的用户界面。该组件通过下拉菜单的形式组织和展示各种调试功能，支持分组显示、动态加载和国际化，为Odoo Web应用的开发和调试提供了便捷的工具入口，帮助开发者快速访问记录信息、用户界面工具、安全检查和测试功能。

## 文件信息
- **路径**: `/web/static/src/core/debug/debug_menu_basic.js`
- **行数**: 50
- **模块**: `@web/core/debug/debug_menu_basic`

## 依赖关系

```javascript
// 核心依赖
'@web/core/debug/debug_context'     // 调试上下文
'@web/core/dropdown/dropdown'       // 下拉菜单组件
'@web/core/dropdown/dropdown_item'  // 下拉菜单项组件
'@web/core/l10n/translation'        // 国际化翻译
'@web/core/utils/arrays'            // 数组工具
'@odoo/owl'                         // OWL框架
'@web/core/registry'                // 注册表系统
```

## 核心功能

### 1. 调试分组注册表

```javascript
const debugSectionRegistry = registry.category("debug_section");

debugSectionRegistry
    .add("record", { label: _t("Record"), sequence: 10 })
    .add("records", { label: _t("Records"), sequence: 10 })
    .add("ui", { label: _t("User Interface"), sequence: 20 })
    .add("security", { label: _t("Security"), sequence: 30 })
    .add("testing", { label: _t("Testing"), sequence: 40 })
    .add("tools", { label: _t("Tools"), sequence: 50 });
```

**分组注册功能**:
- **记录分组**: 单个记录和多个记录的调试功能
- **界面分组**: 用户界面相关的调试工具
- **安全分组**: 安全和权限相关的调试功能
- **测试分组**: 测试和验证相关的工具
- **工具分组**: 通用的开发工具
- **序列排序**: 通过sequence控制分组的显示顺序

### 2. 组件定义

```javascript
const DebugMenuBasic = class DebugMenuBasic extends Component {
    static template = "web.DebugMenu";
    static components = {
        Dropdown,
        DropdownItem,
    };
    static props = {};
}
```

**组件定义功能**:
- **模板绑定**: 使用web.DebugMenu模板
- **组件依赖**: 集成Dropdown和DropdownItem组件
- **无属性**: 不需要外部属性传入
- **自包含**: 组件功能完全自包含

### 3. 组件初始化

```javascript
setup() {
    this.debugContext = useEnvDebugContext();
}
```

**初始化功能**:
- **上下文获取**: 从环境中获取调试上下文
- **状态管理**: 建立与调试系统的连接
- **依赖注入**: 通过钩子获取调试功能

### 4. 分组项目加载

```javascript
async loadGroupedItems() {
    const items = await this.debugContext.getItems(this.env);
    const sections = groupBy(items, (item) => item.section || "");
    this.sectionEntries = sortBy(
        Object.entries(sections),
        ([section]) => debugSectionRegistry.get(section, { sequence: 50 }).sequence
    );
}
```

**项目加载功能**:
- **异步加载**: 异步获取调试项目列表
- **分组处理**: 按section字段对项目进行分组
- **排序处理**: 根据注册表中的sequence进行排序
- **默认分组**: 未指定section的项目归入空字符串分组
- **默认序列**: 未注册的分组使用默认序列50

### 5. 分组标签获取

```javascript
getSectionLabel(section) {
    return debugSectionRegistry.get(section, { label: section }).label;
}
```

**标签获取功能**:
- **标签查找**: 从注册表中查找分组标签
- **默认处理**: 未注册的分组使用section名称作为标签
- **国际化**: 支持多语言标签显示
- **容错机制**: 提供默认值避免显示错误

## 使用场景

### 1. 基础调试菜单

```javascript
// 在开发模式下显示调试菜单
<DebugMenuBasic />
```

### 2. 自定义调试项目

```javascript
// 注册自定义调试项目
const debugItemRegistry = registry.category("debug");

debugItemRegistry.add("my_custom_tool", {
    type: "item",
    description: "My Custom Debug Tool",
    section: "tools",
    callback: () => {
        console.log("Custom debug tool executed");
    },
    sequence: 10
});
```

### 3. 添加新的调试分组

```javascript
// 添加新的调试分组
const debugSectionRegistry = registry.category("debug_section");

debugSectionRegistry.add("performance", {
    label: _t("Performance"),
    sequence: 25
});

// 在新分组中添加调试项目
debugItemRegistry.add("performance_monitor", {
    type: "item",
    description: "Performance Monitor",
    section: "performance",
    callback: () => {
        this.showPerformanceMonitor();
    }
});
```

### 4. 条件性调试项目

```javascript
// 根据条件显示调试项目
debugItemRegistry.add("admin_tools", {
    type: "item",
    description: "Admin Tools",
    section: "security",
    callback: () => {
        this.openAdminPanel();
    },
    isDisplayed: (env) => {
        return env.user && env.user.isAdmin;
    }
});
```

## 增强示例

```javascript
// 增强的调试菜单组件
const EnhancedDebugMenu = {
    createAdvancedMenu: () => {
        class AdvancedDebugMenuBasic extends DebugMenuBasic {
            static props = {
                ...DebugMenuBasic.props,
                customSections: { type: Array, optional: true },
                enableSearch: { type: Boolean, optional: true },
                enableFavorites: { type: Boolean, optional: true },
                maxRecentItems: { type: Number, optional: true }
            };

            static defaultProps = {
                customSections: [],
                enableSearch: false,
                enableFavorites: false,
                maxRecentItems: 5
            };

            setup() {
                super.setup();
                
                // 增强状态
                this.enhancedState = useState({
                    searchTerm: '',
                    favoriteItems: [],
                    recentItems: [],
                    expandedSections: new Set(),
                    isLoading: false
                });

                // 配置选项
                this.config = {
                    enableSearch: this.props.enableSearch,
                    enableFavorites: this.props.enableFavorites,
                    enableRecent: this.props.maxRecentItems > 0
                };

                // 加载用户偏好
                this.loadUserPreferences();

                // 注册自定义分组
                this.registerCustomSections();
            }

            // 注册自定义分组
            registerCustomSections() {
                this.props.customSections.forEach(section => {
                    debugSectionRegistry.add(section.key, {
                        label: section.label,
                        sequence: section.sequence || 100
                    });
                });
            }

            // 加载用户偏好
            loadUserPreferences() {
                try {
                    const prefs = localStorage.getItem('debug_menu_preferences');
                    if (prefs) {
                        const parsed = JSON.parse(prefs);
                        this.enhancedState.favoriteItems = parsed.favorites || [];
                        this.enhancedState.recentItems = parsed.recent || [];
                        this.enhancedState.expandedSections = new Set(parsed.expanded || []);
                    }
                } catch (error) {
                    console.warn('Failed to load debug menu preferences:', error);
                }
            }

            // 保存用户偏好
            saveUserPreferences() {
                try {
                    const prefs = {
                        favorites: this.enhancedState.favoriteItems,
                        recent: this.enhancedState.recentItems,
                        expanded: Array.from(this.enhancedState.expandedSections)
                    };
                    localStorage.setItem('debug_menu_preferences', JSON.stringify(prefs));
                } catch (error) {
                    console.warn('Failed to save debug menu preferences:', error);
                }
            }

            // 增强的项目加载
            async loadGroupedItems() {
                this.enhancedState.isLoading = true;
                
                try {
                    await super.loadGroupedItems();
                    
                    // 应用搜索过滤
                    if (this.enhancedState.searchTerm) {
                        this.filterItemsBySearch();
                    }
                    
                    // 添加收藏和最近使用的分组
                    this.addSpecialSections();
                    
                } finally {
                    this.enhancedState.isLoading = false;
                }
            }

            // 搜索过滤
            filterItemsBySearch() {
                const searchTerm = this.enhancedState.searchTerm.toLowerCase();
                
                this.sectionEntries = this.sectionEntries.map(([section, items]) => {
                    const filteredItems = items.filter(item => 
                        item.description.toLowerCase().includes(searchTerm) ||
                        item.section.toLowerCase().includes(searchTerm)
                    );
                    return [section, filteredItems];
                }).filter(([section, items]) => items.length > 0);
            }

            // 添加特殊分组
            addSpecialSections() {
                if (this.config.enableFavorites && this.enhancedState.favoriteItems.length > 0) {
                    this.sectionEntries.unshift(['favorites', this.enhancedState.favoriteItems]);
                }
                
                if (this.config.enableRecent && this.enhancedState.recentItems.length > 0) {
                    this.sectionEntries.unshift(['recent', this.enhancedState.recentItems]);
                }
            }

            // 执行调试项目
            async executeDebugItem(item) {
                // 添加到最近使用
                if (this.config.enableRecent) {
                    this.addToRecent(item);
                }

                // 执行原始回调
                if (item.callback) {
                    await item.callback();
                }

                // 保存偏好
                this.saveUserPreferences();
            }

            // 添加到最近使用
            addToRecent(item) {
                const recent = this.enhancedState.recentItems;
                const existingIndex = recent.findIndex(r => r.key === item.key);
                
                if (existingIndex >= 0) {
                    recent.splice(existingIndex, 1);
                }
                
                recent.unshift({ ...item, lastUsed: Date.now() });
                
                if (recent.length > this.props.maxRecentItems) {
                    recent.splice(this.props.maxRecentItems);
                }
            }

            // 切换收藏状态
            toggleFavorite(item) {
                const favorites = this.enhancedState.favoriteItems;
                const existingIndex = favorites.findIndex(f => f.key === item.key);
                
                if (existingIndex >= 0) {
                    favorites.splice(existingIndex, 1);
                } else {
                    favorites.push({ ...item, favorited: Date.now() });
                }
                
                this.saveUserPreferences();
            }

            // 检查是否为收藏项目
            isFavorite(item) {
                return this.enhancedState.favoriteItems.some(f => f.key === item.key);
            }

            // 切换分组展开状态
            toggleSection(section) {
                const expanded = this.enhancedState.expandedSections;
                if (expanded.has(section)) {
                    expanded.delete(section);
                } else {
                    expanded.add(section);
                }
                this.saveUserPreferences();
            }

            // 检查分组是否展开
            isSectionExpanded(section) {
                return this.enhancedState.expandedSections.has(section);
            }

            // 搜索处理
            onSearchInput(event) {
                this.enhancedState.searchTerm = event.target.value;
                this.loadGroupedItems();
            }

            // 清除搜索
            clearSearch() {
                this.enhancedState.searchTerm = '';
                this.loadGroupedItems();
            }

            // 增强的分组标签获取
            getSectionLabel(section) {
                switch (section) {
                    case 'favorites':
                        return _t('Favorites');
                    case 'recent':
                        return _t('Recent');
                    default:
                        return super.getSectionLabel(section);
                }
            }

            // 获取分组图标
            getSectionIcon(section) {
                const iconMap = {
                    'record': 'fa-file-o',
                    'records': 'fa-files-o',
                    'ui': 'fa-desktop',
                    'security': 'fa-shield',
                    'testing': 'fa-flask',
                    'tools': 'fa-wrench',
                    'favorites': 'fa-star',
                    'recent': 'fa-clock-o'
                };
                return iconMap[section] || 'fa-folder';
            }

            // 导出调试配置
            exportDebugConfig() {
                const config = {
                    favorites: this.enhancedState.favoriteItems,
                    recent: this.enhancedState.recentItems,
                    expanded: Array.from(this.enhancedState.expandedSections),
                    customSections: this.props.customSections
                };
                
                const blob = new Blob([JSON.stringify(config, null, 2)], {
                    type: 'application/json'
                });
                
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'debug_menu_config.json';
                a.click();
                URL.revokeObjectURL(url);
            }

            // 导入调试配置
            async importDebugConfig(file) {
                try {
                    const text = await file.text();
                    const config = JSON.parse(text);
                    
                    this.enhancedState.favoriteItems = config.favorites || [];
                    this.enhancedState.recentItems = config.recent || [];
                    this.enhancedState.expandedSections = new Set(config.expanded || []);
                    
                    this.saveUserPreferences();
                    this.loadGroupedItems();
                    
                } catch (error) {
                    console.error('Failed to import debug config:', error);
                }
            }

            // 重置调试菜单
            resetDebugMenu() {
                this.enhancedState.favoriteItems = [];
                this.enhancedState.recentItems = [];
                this.enhancedState.expandedSections = new Set();
                this.enhancedState.searchTerm = '';
                
                localStorage.removeItem('debug_menu_preferences');
                this.loadGroupedItems();
            }
        }

        return AdvancedDebugMenuBasic;
    }
};

// 使用示例
const AdvancedDebugMenu = EnhancedDebugMenu.createAdvancedMenu();

// 带搜索和收藏功能的调试菜单
<AdvancedDebugMenu
    enableSearch={true}
    enableFavorites={true}
    maxRecentItems={10}
    customSections={[
        {
            key: 'custom',
            label: _t('Custom Tools'),
            sequence: 60
        }
    ]}
/>
```

## 技术特点

### 1. 分组管理
- 预定义的调试分组
- 可扩展的分组注册表
- 序列化排序支持

### 2. 动态加载
- 异步项目加载
- 上下文相关的项目
- 条件性显示支持

### 3. 国际化
- 完整的多语言支持
- 标签和描述翻译
- 本地化的用户界面

### 4. 模块化设计
- 组件化的架构
- 可重用的下拉菜单
- 清晰的职责分离

## 设计模式

### 1. 注册表模式 (Registry Pattern)
- 调试分组的注册管理
- 可扩展的项目注册

### 2. 组合模式 (Composite Pattern)
- 下拉菜单的组合结构
- 分组和项目的层次关系

### 3. 策略模式 (Strategy Pattern)
- 不同类型调试项目的处理策略
- 可配置的显示逻辑

## 注意事项

1. **性能优化**: 避免频繁的项目重新加载
2. **用户体验**: 提供清晰的分组和标签
3. **安全考虑**: 确保调试功能只在开发模式下可用
4. **扩展性**: 支持第三方模块添加调试项目

## 扩展建议

1. **搜索功能**: 添加调试项目的搜索功能
2. **收藏功能**: 支持收藏常用的调试工具
3. **快捷键**: 为常用调试功能添加快捷键
4. **历史记录**: 记录调试操作的历史
5. **配置导出**: 支持调试配置的导入导出

该基础调试菜单组件为Odoo Web应用提供了结构化的调试工具入口，通过分组管理和动态加载确保了开发者能够快速访问所需的调试功能。

# DebugProviders - 调试命令提供者

## 概述

`debug_providers.js` 是 Odoo Web 核心模块的调试命令提供者，为命令面板系统提供调试相关的命令。该模块通过命令提供者模式，根据当前的调试状态动态生成可用的调试命令，支持调试模式的激活、停用、资源调试和单元测试运行等功能，为开发者提供了通过命令面板快速访问调试功能的便捷方式。

## 文件信息
- **路径**: `/web/static/src/core/debug/debug_providers.js`
- **行数**: 62
- **模块**: `@web/core/debug/debug_providers`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'    // 国际化翻译
'@web/core/registry'            // 注册表系统
'@web/core/browser/browser'     // 浏览器服务
'@web/core/browser/router'      // 路由服务
```

## 核心功能

### 1. 命令提供者注册

```javascript
const commandProviderRegistry = registry.category("command_provider");

commandProviderRegistry.add("debug", {
    provide: (env, options) => {
        // 命令提供逻辑
    },
});
```

**注册功能**:
- **提供者注册**: 注册到命令提供者注册表
- **标识符**: 使用"debug"作为提供者标识
- **动态提供**: 根据环境和选项动态提供命令
- **集成系统**: 与命令面板系统无缝集成

### 2. 调试模式下的命令

```javascript
if (env.debug) {
    if (!env.debug.includes("assets")) {
        result.push({
            action() {
                router.pushState({ debug: "assets" }, { reload: true });
            },
            category: "debug",
            name: _t("Activate debug mode (with assets)"),
        });
    }
    result.push({
        action() {
            router.pushState({ debug: 0 }, { reload: true });
        },
        category: "debug",
        name: _t("Deactivate debug mode"),
    });
    result.push({
        action() {
            browser.open("/web/tests?debug=assets");
        },
        category: "debug",
        name: _t("Run Unit Tests"),
    });
}
```

**调试模式命令功能**:
- **资源调试激活**: 当前未启用assets调试时，提供激活选项
- **调试模式停用**: 提供退出调试模式的命令
- **单元测试运行**: 在新窗口中运行单元测试
- **状态检查**: 根据当前调试状态提供相应命令
- **页面重载**: 模式切换后自动重新加载页面

### 3. 非调试模式下的命令

```javascript
else {
    const debugKey = "debug";
    if (options.searchValue.toLowerCase() === debugKey) {
        result.push({
            action() {
                router.pushState({ debug: "1" }, { reload: true });
            },
            category: "debug",
            name: `${_t("Activate debug mode")} (${debugKey})`,
        });
        result.push({
            action() {
                router.pushState({ debug: "assets" }, { reload: true });
            },
            category: "debug",
            name: `${_t("Activate debug mode (with assets)")} (${debugKey})`,
        });
    }
}
```

**非调试模式命令功能**:
- **搜索触发**: 只有搜索"debug"时才显示命令
- **基础调试激活**: 激活基础调试模式
- **资源调试激活**: 激活包含资源的调试模式
- **搜索提示**: 在命令名称中显示搜索关键词
- **隐藏机制**: 避免在非调试模式下过度暴露调试功能

### 4. 命令结构

```javascript
{
    action() {
        // 命令执行逻辑
    },
    category: "debug",
    name: _t("Command Name"),
}
```

**命令结构功能**:
- **执行函数**: action函数定义命令的执行逻辑
- **分类标识**: 所有调试命令归类到"debug"分类
- **命令名称**: 国际化的命令显示名称
- **标准格式**: 符合命令面板的标准命令格式

## 使用场景

### 1. 命令面板中的调试命令

```javascript
// 用户在命令面板中可以：
// 1. 在调试模式下看到：
//    - "Deactivate debug mode" - 退出调试模式
//    - "Activate debug mode (with assets)" - 激活资源调试（如果未启用）
//    - "Run Unit Tests" - 运行单元测试

// 2. 在非调试模式下搜索"debug"时看到：
//    - "Activate debug mode (debug)" - 激活基础调试模式
//    - "Activate debug mode (with assets) (debug)" - 激活资源调试模式
```

### 2. 自定义调试命令提供者

```javascript
// 创建自定义调试命令提供者
commandProviderRegistry.add("custom_debug", {
    provide: (env, options) => {
        const result = [];
        
        if (env.debug) {
            // 性能分析命令
            result.push({
                action() {
                    console.profile("Performance Analysis");
                    setTimeout(() => {
                        console.profileEnd("Performance Analysis");
                    }, 10000);
                },
                category: "debug",
                name: _t("Start Performance Profiling"),
            });
            
            // 内存分析命令
            if (performance.memory) {
                result.push({
                    action() {
                        const memory = performance.memory;
                        console.log("Memory Usage:", {
                            used: (memory.usedJSHeapSize / 1048576).toFixed(2) + " MB",
                            total: (memory.totalJSHeapSize / 1048576).toFixed(2) + " MB",
                            limit: (memory.jsHeapSizeLimit / 1048576).toFixed(2) + " MB"
                        });
                    },
                    category: "debug",
                    name: _t("Show Memory Usage"),
                });
            }
            
            // 清除缓存命令
            result.push({
                action() {
                    if ('caches' in window) {
                        caches.keys().then(names => {
                            names.forEach(name => {
                                caches.delete(name);
                            });
                        });
                    }
                    localStorage.clear();
                    sessionStorage.clear();
                    browser.location.reload();
                },
                category: "debug",
                name: _t("Clear All Caches"),
            });
        }
        
        return result;
    },
});
```

### 3. 条件性调试命令

```javascript
// 根据用户权限和环境提供命令
commandProviderRegistry.add("admin_debug", {
    provide: (env, options) => {
        const result = [];
        
        // 只有管理员在调试模式下才能看到
        if (env.debug && env.user && env.user.isAdmin) {
            result.push({
                action() {
                    browser.open("/web/database/manager");
                },
                category: "debug",
                name: _t("Database Manager"),
            });
            
            result.push({
                action() {
                    browser.open("/web/become");
                },
                category: "debug",
                name: _t("Become Superuser"),
            });
        }
        
        return result;
    },
});
```

### 4. 搜索敏感的调试命令

```javascript
// 只有特定搜索词才显示的命令
commandProviderRegistry.add("secret_debug", {
    provide: (env, options) => {
        const result = [];
        const searchValue = options.searchValue.toLowerCase();
        
        // 只有搜索"secret"或"advanced"时才显示
        if (env.debug && (searchValue.includes("secret") || searchValue.includes("advanced"))) {
            result.push({
                action() {
                    // 显示高级调试信息
                    console.log("Advanced Debug Info:", {
                        environment: env,
                        services: Object.keys(env.services),
                        registries: Object.keys(registry.categories)
                    });
                },
                category: "debug",
                name: _t("Show Advanced Debug Info"),
            });
        }
        
        return result;
    },
});
```

## 增强示例

```javascript
// 增强的调试命令提供者
const EnhancedDebugProviders = {
    // 开发工具提供者
    developmentTools: {
        provide: (env, options) => {
            const result = [];
            
            if (env.debug) {
                // 组件检查器
                result.push({
                    action() {
                        if (window.__ODOO_DEBUG__) {
                            window.__ODOO_DEBUG__.componentInspector.toggle();
                        } else {
                            console.warn("Component inspector not available");
                        }
                    },
                    category: "debug",
                    name: _t("Toggle Component Inspector"),
                });
                
                // 服务监控器
                result.push({
                    action() {
                        const services = env.services;
                        const serviceInfo = Object.keys(services).map(name => ({
                            name,
                            type: typeof services[name],
                            methods: Object.getOwnPropertyNames(services[name])
                        }));
                        
                        console.group("Service Monitor");
                        console.table(serviceInfo);
                        console.groupEnd();
                    },
                    category: "debug",
                    name: _t("Monitor Services"),
                });
                
                // 注册表查看器
                result.push({
                    action() {
                        const categories = Object.keys(registry.categories);
                        const registryInfo = categories.map(category => ({
                            category,
                            items: Object.keys(registry.category(category).content)
                        }));
                        
                        console.group("Registry Viewer");
                        console.table(registryInfo);
                        console.groupEnd();
                    },
                    category: "debug",
                    name: _t("View Registries"),
                });
            }
            
            return result;
        }
    },
    
    // 性能分析提供者
    performanceAnalysis: {
        provide: (env, options) => {
            const result = [];
            
            if (env.debug) {
                // 渲染性能分析
                result.push({
                    action() {
                        const observer = new PerformanceObserver((list) => {
                            const entries = list.getEntries();
                            console.log("Render Performance:", entries);
                        });
                        observer.observe({ entryTypes: ['measure', 'navigation'] });
                        
                        setTimeout(() => {
                            observer.disconnect();
                        }, 30000);
                    },
                    category: "debug",
                    name: _t("Analyze Render Performance"),
                });
                
                // 网络性能分析
                result.push({
                    action() {
                        const resources = performance.getEntriesByType('resource');
                        const networkInfo = resources.map(resource => ({
                            name: resource.name.split('/').pop(),
                            duration: resource.duration.toFixed(2),
                            size: resource.transferSize || 'N/A'
                        }));
                        
                        console.group("Network Performance");
                        console.table(networkInfo);
                        console.groupEnd();
                    },
                    category: "debug",
                    name: _t("Analyze Network Performance"),
                });
            }
            
            return result;
        }
    },
    
    // 数据分析提供者
    dataAnalysis: {
        provide: (env, options) => {
            const result = [];
            
            if (env.debug && env.user && env.user.isAdmin) {
                // 数据模型分析
                result.push({
                    action() {
                        env.services.orm.call("ir.model", "search_read", []).then(models => {
                            console.group("Data Models");
                            console.table(models);
                            console.groupEnd();
                        });
                    },
                    category: "debug",
                    name: _t("Analyze Data Models"),
                });
                
                // 数据库统计
                result.push({
                    action() {
                        env.services.orm.call("ir.attachment", "get_database_stats", []).then(stats => {
                            console.group("Database Statistics");
                            console.table(stats);
                            console.groupEnd();
                        });
                    },
                    category: "debug",
                    name: _t("Show Database Statistics"),
                });
            }
            
            return result;
        }
    }
};

// 注册增强的提供者
Object.entries(EnhancedDebugProviders).forEach(([key, provider]) => {
    commandProviderRegistry.add(`enhanced_debug_${key}`, provider);
});
```

## 技术特点

### 1. 动态命令生成
- 根据当前状态生成命令
- 条件性命令显示
- 搜索敏感的命令提供

### 2. 状态感知
- 检测当前调试模式状态
- 根据用户权限提供命令
- 环境相关的命令过滤

### 3. 集成设计
- 与命令面板系统集成
- 与路由系统集成
- 与浏览器服务集成

### 4. 国际化支持
- 完整的多语言支持
- 本地化的命令名称
- 用户友好的界面

## 设计模式

### 1. 提供者模式 (Provider Pattern)
- 命令的动态提供机制
- 可扩展的提供者架构

### 2. 策略模式 (Strategy Pattern)
- 不同状态下的命令策略
- 可配置的命令行为

### 3. 工厂模式 (Factory Pattern)
- 命令对象的工厂创建
- 统一的命令格式

## 注意事项

1. **性能考虑**: 避免在命令提供过程中执行耗时操作
2. **安全控制**: 确保敏感命令只对授权用户可见
3. **状态检查**: 正确检查环境状态避免无效命令
4. **用户体验**: 提供清晰的命令名称和分类

## 扩展建议

1. **命令分组**: 支持更细粒度的命令分组
2. **快捷键**: 为常用调试命令添加快捷键
3. **命令历史**: 记录和管理命令执行历史
4. **批量操作**: 支持批量执行多个调试命令
5. **命令模板**: 提供可参数化的命令模板

该调试命令提供者模块为Odoo Web应用的命令面板系统提供了丰富的调试命令，通过动态生成和状态感知确保了命令的相关性和可用性。

# Debug Context - 调试上下文管理器

## 概述

`debug_context.js` 是 Odoo Web 核心模块的调试上下文管理器，专门负责管理调试模式下的上下文信息和调试项目。该模块提供了完整的调试上下文创建、分类管理和权限检查功能，为开发者提供了强大的调试工具基础设施，支持动态的调试项目注册和上下文感知的调试功能。

## 文件信息
- **路径**: `/web/static/src/core/debug/debug_context.js`
- **行数**: 89
- **模块**: `@web/core/debug/debug_context`

## 依赖关系

```javascript
// 核心依赖
'@web/core/user'        // 用户服务
'@web/core/registry'    // 注册表
'@odoo/owl'            // OWL框架
```

## 核心功能

### 1. 权限检查

```javascript
const getAccessRights = async () => {
    const rightsToCheck = {
        "ir.ui.view": "write",
        "ir.rule": "read",
        "ir.model.access": "read",
    };
    const proms = Object.entries(rightsToCheck).map(([model, operation]) => {
        return user.checkAccessRight(model, operation);
    });
    const [canEditView, canSeeRecordRules, canSeeModelAccess] = await Promise.all(proms);
    const accessRights = { canEditView, canSeeRecordRules, canSeeModelAccess };
    return accessRights;
};
```

**权限检查功能**:
- **视图编辑权限**: 检查是否可以编辑UI视图
- **规则查看权限**: 检查是否可以查看记录规则
- **访问控制权限**: 检查是否可以查看模型访问控制
- **并行检查**: 使用Promise.all并行检查所有权限
- **结构化返回**: 返回结构化的权限对象

### 2. 调试上下文类

```javascript
class DebugContext {
    constructor(defaultCategories) {
        this.categories = new Map(defaultCategories.map((cat) => [cat, [{}]]));
    }

    activateCategory(category, context) {
        const contexts = this.categories.get(category) || new Set();
        contexts.add(context);
        this.categories.set(category, contexts);

        return () => {
            contexts.delete(context);
            if (contexts.size === 0) {
                this.categories.delete(category);
            }
        };
    }

    async getItems(env) {
        const accessRights = await getAccessRights();
        return [...this.categories.entries()]
            .flatMap(([category, contexts]) => {
                return debugRegistry
                    .category(category)
                    .getAll()
                    .map((factory) => factory(Object.assign({ env, accessRights }, ...contexts)));
            })
            .filter(Boolean)
            .sort((x, y) => {
                const xSeq = x.sequence || 1000;
                const ySeq = y.sequence || 1000;
                return xSeq - ySeq;
            });
    }
}
```

**调试上下文功能**:
- **分类管理**: 管理不同类别的调试上下文
- **动态激活**: 支持动态激活和停用调试分类
- **上下文合并**: 合并多个上下文对象
- **项目获取**: 获取排序后的调试项目列表
- **权限集成**: 集成权限检查到调试项目

### 3. 上下文创建

```javascript
const debugContextSymbol = Symbol("debugContext");

function createDebugContext({ categories = [] } = {}) {
    return { [debugContextSymbol]: new DebugContext(categories) };
}
```

**创建功能**:
- **符号标识**: 使用Symbol避免命名冲突
- **默认参数**: 提供默认的空分类数组
- **封装返回**: 返回包含调试上下文的对象

### 4. 钩子函数

```javascript
function useOwnDebugContext({ categories = [] } = {}) {
    useSubEnv(createDebugContext({ categories }));
}

function useEnvDebugContext() {
    const debugContext = useEnv()[debugContextSymbol];
    if (!debugContext) {
        throw new Error("There is no debug context available in the current environment.");
    }
    return debugContext;
}

function useDebugCategory(category, context = {}) {
    const env = useEnv();
    if (env.debug) {
        const debugContext = useEnvDebugContext();
        useEffect(
            () => debugContext.activateCategory(category, context),
            () => []
        );
    }
}
```

**钩子功能**:
- **子环境创建**: 创建包含调试上下文的子环境
- **上下文获取**: 从环境中获取调试上下文
- **错误检查**: 检查调试上下文的可用性
- **分类激活**: 在调试模式下激活指定分类
- **生命周期管理**: 使用useEffect管理分类的生命周期

## 使用场景

### 1. 组件中使用调试分类

```javascript
// 在组件中激活调试分类
function MyComponent() {
    // 激活视图调试分类
    useDebugCategory("view", {
        viewId: this.props.viewId,
        viewType: this.props.viewType
    });
    
    // 激活模型调试分类
    useDebugCategory("model", {
        modelName: this.props.modelName,
        recordId: this.props.recordId
    });
    
    return xml`<div>My Component</div>`;
}
```

### 2. 创建自定义调试上下文

```javascript
// 创建带有特定分类的调试上下文
function setupCustomDebugContext() {
    useOwnDebugContext({
        categories: ["custom", "advanced", "performance"]
    });
}
```

### 3. 获取调试项目

```javascript
// 获取当前上下文的调试项目
async function getDebugItems() {
    const debugContext = useEnvDebugContext();
    const items = await debugContext.getItems(env);
    
    // 按分类分组
    const groupedItems = items.reduce((groups, item) => {
        const category = item.category || 'default';
        if (!groups[category]) {
            groups[category] = [];
        }
        groups[category].push(item);
        return groups;
    }, {});
    
    return groupedItems;
}
```

## 增强示例

```javascript
// 增强的调试上下文管理器
const EnhancedDebugContext = {
    // 扩展的权限检查
    async getExtendedAccessRights() {
        const baseRights = await getAccessRights();
        
        // 检查额外的权限
        const extendedRightsToCheck = {
            "ir.actions.act_window": "write",
            "ir.model.fields": "read",
            "ir.translation": "write",
            "res.groups": "read",
            "ir.config_parameter": "read"
        };
        
        const extendedProms = Object.entries(extendedRightsToCheck).map(([model, operation]) => {
            return user.checkAccessRight(model, operation).catch(() => false);
        });
        
        const [canEditActions, canSeeFields, canEditTranslations, canSeeGroups, canSeeConfig] = 
            await Promise.all(extendedProms);
        
        return {
            ...baseRights,
            canEditActions,
            canSeeFields,
            canEditTranslations,
            canSeeGroups,
            canSeeConfig
        };
    },
    
    // 增强的调试上下文类
    createEnhancedDebugContext(options = {}) {
        const {
            categories = [],
            enableProfiling = false,
            enableLogging = false,
            maxHistorySize = 100
        } = options;
        
        class EnhancedDebugContextClass extends DebugContext {
            constructor(defaultCategories) {
                super(defaultCategories);
                this.history = [];
                this.profiling = enableProfiling;
                this.logging = enableLogging;
                this.maxHistorySize = maxHistorySize;
                this.performance = new Map();
            }
            
            activateCategory(category, context) {
                const startTime = this.profiling ? performance.now() : 0;
                
                if (this.logging) {
                    console.log(`Activating debug category: ${category}`, context);
                }
                
                // 记录历史
                this.addToHistory('activate', category, context);
                
                const deactivate = super.activateCategory(category, context);
                
                if (this.profiling) {
                    const duration = performance.now() - startTime;
                    this.recordPerformance('activateCategory', duration);
                }
                
                return () => {
                    if (this.logging) {
                        console.log(`Deactivating debug category: ${category}`);
                    }
                    
                    this.addToHistory('deactivate', category, context);
                    return deactivate();
                };
            }
            
            async getItems(env) {
                const startTime = this.profiling ? performance.now() : 0;
                
                try {
                    const accessRights = await this.getExtendedAccessRights();
                    const items = await this.getEnhancedItems(env, accessRights);
                    
                    if (this.profiling) {
                        const duration = performance.now() - startTime;
                        this.recordPerformance('getItems', duration);
                    }
                    
                    return items;
                } catch (error) {
                    if (this.logging) {
                        console.error('Error getting debug items:', error);
                    }
                    throw error;
                }
            }
            
            async getEnhancedItems(env, accessRights) {
                const items = [];
                
                for (const [category, contexts] of this.categories.entries()) {
                    try {
                        const categoryItems = debugRegistry
                            .category(category)
                            .getAll()
                            .map((factory) => {
                                try {
                                    return factory(Object.assign({ env, accessRights }, ...contexts));
                                } catch (error) {
                                    if (this.logging) {
                                        console.warn(`Error creating debug item in category ${category}:`, error);
                                    }
                                    return null;
                                }
                            })
                            .filter(Boolean);
                        
                        items.push(...categoryItems);
                    } catch (error) {
                        if (this.logging) {
                            console.warn(`Error processing debug category ${category}:`, error);
                        }
                    }
                }
                
                // 增强的排序逻辑
                return items.sort((x, y) => {
                    // 首先按优先级排序
                    const xPriority = x.priority || 0;
                    const yPriority = y.priority || 0;
                    if (xPriority !== yPriority) {
                        return yPriority - xPriority;
                    }
                    
                    // 然后按序列号排序
                    const xSeq = x.sequence || 1000;
                    const ySeq = y.sequence || 1000;
                    if (xSeq !== ySeq) {
                        return xSeq - ySeq;
                    }
                    
                    // 最后按名称排序
                    const xName = x.name || '';
                    const yName = y.name || '';
                    return xName.localeCompare(yName);
                });
            }
            
            async getExtendedAccessRights() {
                return EnhancedDebugContext.getExtendedAccessRights();
            }
            
            addToHistory(action, category, context) {
                const entry = {
                    timestamp: Date.now(),
                    action,
                    category,
                    context: JSON.parse(JSON.stringify(context)) // 深拷贝
                };
                
                this.history.push(entry);
                
                // 限制历史大小
                if (this.history.length > this.maxHistorySize) {
                    this.history.shift();
                }
            }
            
            recordPerformance(operation, duration) {
                if (!this.performance.has(operation)) {
                    this.performance.set(operation, []);
                }
                
                const records = this.performance.get(operation);
                records.push(duration);
                
                // 只保留最近的100条记录
                if (records.length > 100) {
                    records.shift();
                }
            }
            
            getHistory() {
                return [...this.history];
            }
            
            getPerformanceStats() {
                const stats = {};
                
                for (const [operation, durations] of this.performance.entries()) {
                    const avg = durations.reduce((sum, d) => sum + d, 0) / durations.length;
                    const min = Math.min(...durations);
                    const max = Math.max(...durations);
                    
                    stats[operation] = {
                        count: durations.length,
                        average: avg,
                        min,
                        max
                    };
                }
                
                return stats;
            }
            
            clearHistory() {
                this.history = [];
            }
            
            clearPerformanceStats() {
                this.performance.clear();
            }
        }
        
        return { [debugContextSymbol]: new EnhancedDebugContextClass(categories) };
    },
    
    // 增强的钩子函数
    useEnhancedDebugCategory(category, context = {}, options = {}) {
        const {
            enableConditional = false,
            condition = () => true,
            onActivate = null,
            onDeactivate = null
        } = options;
        
        const env = useEnv();
        
        if (env.debug && (!enableConditional || condition())) {
            const debugContext = useEnvDebugContext();
            
            useEffect(() => {
                if (onActivate) {
                    onActivate(category, context);
                }
                
                const deactivate = debugContext.activateCategory(category, context);
                
                return () => {
                    if (onDeactivate) {
                        onDeactivate(category, context);
                    }
                    deactivate();
                };
            }, [category, JSON.stringify(context)]);
        }
    },
    
    // 调试信息收集器
    useDebugInfoCollector() {
        const debugContext = useEnvDebugContext();
        
        return {
            getHistory: () => debugContext.getHistory?.() || [],
            getPerformanceStats: () => debugContext.getPerformanceStats?.() || {},
            clearHistory: () => debugContext.clearHistory?.(),
            clearPerformanceStats: () => debugContext.clearPerformanceStats?.(),
            exportDebugInfo: () => ({
                timestamp: Date.now(),
                history: debugContext.getHistory?.() || [],
                performance: debugContext.getPerformanceStats?.() || {},
                categories: Array.from(debugContext.categories?.keys() || [])
            })
        };
    }
};

// 使用示例
function MyEnhancedComponent() {
    // 使用增强的调试分类
    EnhancedDebugContext.useEnhancedDebugCategory('view', {
        viewId: this.props.viewId
    }, {
        enableConditional: true,
        condition: () => this.props.viewId > 0,
        onActivate: (category, context) => {
            console.log(`Debug category ${category} activated`, context);
        }
    });
    
    // 使用调试信息收集器
    const debugCollector = EnhancedDebugContext.useDebugInfoCollector();
    
    return xml`<div>Enhanced Component</div>`;
}
```

## 技术特点

### 1. 上下文管理
- 分类化的上下文管理
- 动态的上下文激活和停用
- 上下文合并和继承

### 2. 权限集成
- 异步权限检查
- 权限感知的调试项目
- 安全的调试功能访问

### 3. 生命周期管理
- 自动的资源清理
- 组件生命周期集成
- 内存泄漏防护

### 4. 扩展性
- 插件化的调试项目
- 可配置的调试分类
- 灵活的上下文结构

## 设计模式

### 1. 上下文模式 (Context Pattern)
- 管理调试相关的上下文信息
- 提供上下文感知的功能

### 2. 注册表模式 (Registry Pattern)
- 动态注册调试项目
- 分类化的项目管理

### 3. 钩子模式 (Hook Pattern)
- 组件生命周期集成
- 声明式的调试功能使用

## 注意事项

1. **性能影响**: 调试功能对性能的影响
2. **内存管理**: 避免调试上下文的内存泄漏
3. **权限安全**: 确保调试功能的权限安全
4. **生产环境**: 在生产环境中禁用调试功能

## 扩展建议

1. **性能监控**: 添加调试功能的性能监控
2. **历史记录**: 记录调试操作的历史
3. **导出功能**: 支持调试信息的导出
4. **可视化**: 提供调试信息的可视化界面
5. **远程调试**: 支持远程调试功能

该调试上下文管理器为Odoo Web应用提供了强大的调试基础设施，通过灵活的上下文管理和权限集成为开发者提供了安全、高效的调试工具支持。

# DebugMenuItems - 调试菜单项

## 概述

`debug_menu_items.js` 是 Odoo Web 核心模块的调试菜单项定义文件，提供了一系列预定义的调试工具和功能。该模块包含了资源重新生成、测试模式激活、超级用户切换和调试模式退出等核心调试功能，为开发者提供了常用的系统级调试工具，简化了开发和测试过程中的常见操作。

## 文件信息
- **路径**: `/web/static/src/core/debug/debug_menu_items.js`
- **行数**: 74
- **模块**: `@web/core/debug/debug_menu_items`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'    // 国际化翻译
'@web/core/browser/browser'     // 浏览器服务
'@web/core/browser/router'      // 路由服务
'@web/core/registry'            // 注册表系统
'@web/core/user'                // 用户服务
```

## 核心功能

### 1. 激活测试模式

```javascript
function activateTestsAssetsDebugging({ env }) {
    if (String(router.current.debug).includes("tests")) {
        return;
    }

    return {
        type: "item",
        description: _t("Activate Test Mode"),
        callback: () => {
            router.pushState({ debug: "assets,tests" }, { reload: true });
        },
        sequence: 580,
        section: "tools",
    };
}
```

**测试模式功能**:
- **条件检查**: 检查当前是否已经在测试模式
- **模式切换**: 激活assets和tests调试模式
- **页面重载**: 切换模式后重新加载页面
- **工具分组**: 归类到tools分组
- **序列排序**: 设置显示顺序为580

### 2. 重新生成资源

```javascript
function regenerateAssets({ env }) {
    return {
        type: "item",
        description: _t("Regenerate Assets"),
        callback: async () => {
            await env.services.orm.call("ir.attachment", "regenerate_assets_bundles");
            browser.location.reload();
        },
        sequence: 550,
        section: "tools",
    };
}
```

**资源重新生成功能**:
- **异步操作**: 异步调用ORM服务
- **服务调用**: 调用ir.attachment模型的regenerate_assets_bundles方法
- **自动刷新**: 重新生成后自动刷新页面
- **错误处理**: 支持异步错误处理
- **开发工具**: 用于开发过程中的资源更新

### 3. 成为超级用户

```javascript
function becomeSuperuser({ env }) {
    const becomeSuperuserURL = browser.location.origin + "/web/become";
    return {
        type: "item",
        description: _t("Become Superuser"),
        hide: !user.isAdmin,
        href: becomeSuperuserURL,
        callback: () => {
            browser.open(becomeSuperuserURL, "_self");
        },
        sequence: 560,
        section: "tools",
    };
}
```

**超级用户切换功能**:
- **权限检查**: 只有管理员用户才能看到此选项
- **URL构建**: 动态构建超级用户切换URL
- **链接属性**: 提供href属性支持右键操作
- **页面跳转**: 在当前窗口中跳转到超级用户模式
- **安全控制**: 通过hide属性控制显示权限

### 4. 退出调试模式

```javascript
function leaveDebugMode() {
    return {
        type: "item",
        description: _t("Leave Debug Mode"),
        callback: () => {
            router.pushState({ debug: 0 }, { reload: true });
        },
        sequence: 650,
    };
}
```

**退出调试模式功能**:
- **模式切换**: 将debug参数设置为0
- **页面重载**: 退出调试模式后重新加载页面
- **状态清理**: 清除调试相关的状态
- **用户体验**: 提供便捷的退出方式

### 5. 注册表注册

```javascript
registry
    .category("debug")
    .category("default")
    .add("regenerateAssets", regenerateAssets)
    .add("becomeSuperuser", becomeSuperuser)
    .add("activateTestsAssetsDebugging", activateTestsAssetsDebugging)
    .add("leaveDebugMode", leaveDebugMode);
```

**注册功能**:
- **分类注册**: 注册到debug.default分类
- **函数注册**: 注册调试功能函数
- **标识符**: 为每个功能提供唯一标识符
- **动态加载**: 支持运行时动态加载调试项目

## 使用场景

### 1. 开发环境调试

```javascript
// 开发者在开发过程中使用这些调试工具
// 1. 修改了CSS/JS文件后，使用"Regenerate Assets"重新生成资源
// 2. 需要测试功能时，使用"Activate Test Mode"激活测试模式
// 3. 需要管理员权限时，使用"Become Superuser"切换用户
// 4. 调试完成后，使用"Leave Debug Mode"退出调试模式
```

### 2. 自定义调试项目

```javascript
// 添加自定义调试项目
function customDebugTool({ env }) {
    return {
        type: "item",
        description: _t("Custom Debug Tool"),
        callback: async () => {
            // 执行自定义调试逻辑
            console.log("Custom debug tool executed");
            
            // 可以调用服务
            const result = await env.services.orm.call("res.users", "search_read", []);
            console.log("Users:", result);
        },
        sequence: 600,
        section: "tools",
        hide: !env.user.isSystemAdmin, // 只有系统管理员可见
    };
}

// 注册自定义调试项目
registry
    .category("debug")
    .category("default")
    .add("customDebugTool", customDebugTool);
```

### 3. 条件性调试项目

```javascript
// 根据环境条件显示调试项目
function environmentSpecificTool({ env }) {
    // 只在开发环境显示
    if (env.debug !== "assets") {
        return;
    }

    return {
        type: "item",
        description: _t("Development Tool"),
        callback: () => {
            // 开发环境特定的调试逻辑
            this.openDeveloperConsole();
        },
        sequence: 570,
        section: "tools",
    };
}
```

### 4. 分组调试项目

```javascript
// 添加新的调试分组
const debugSectionRegistry = registry.category("debug_section");
debugSectionRegistry.add("performance", {
    label: _t("Performance"),
    sequence: 25
});

// 在新分组中添加调试项目
function performanceProfiler({ env }) {
    return {
        type: "item",
        description: _t("Start Performance Profiler"),
        callback: () => {
            console.profile("Odoo Performance");
            setTimeout(() => {
                console.profileEnd("Odoo Performance");
            }, 10000);
        },
        sequence: 10,
        section: "performance",
    };
}

registry
    .category("debug")
    .category("default")
    .add("performanceProfiler", performanceProfiler);
```

## 增强示例

```javascript
// 增强的调试菜单项
const EnhancedDebugMenuItems = {
    // 高级资源管理
    advancedAssetManagement: ({ env }) => ({
        type: "item",
        description: _t("Advanced Asset Management"),
        callback: async () => {
            const dialog = env.services.dialog;
            
            dialog.add(AssetManagementDialog, {
                title: _t("Asset Management"),
                onRegenerateAssets: async () => {
                    await env.services.orm.call("ir.attachment", "regenerate_assets_bundles");
                    browser.location.reload();
                },
                onClearCache: async () => {
                    await env.services.orm.call("ir.attachment", "clear_assets_cache");
                    browser.location.reload();
                },
                onOptimizeAssets: async () => {
                    await env.services.orm.call("ir.attachment", "optimize_assets");
                    browser.location.reload();
                }
            });
        },
        sequence: 551,
        section: "tools"
    }),

    // 系统信息查看器
    systemInfoViewer: ({ env }) => ({
        type: "item",
        description: _t("System Information"),
        callback: async () => {
            const systemInfo = await env.services.orm.call("ir.config_parameter", "get_system_info");
            
            console.group("System Information");
            console.table(systemInfo);
            console.groupEnd();
            
            env.services.notification.add(
                _t("System information logged to console"),
                { type: "info" }
            );
        },
        sequence: 590,
        section: "tools"
    }),

    // 数据库查询工具
    databaseQueryTool: ({ env }) => ({
        type: "item",
        description: _t("Database Query Tool"),
        callback: () => {
            const dialog = env.services.dialog;
            
            dialog.add(DatabaseQueryDialog, {
                title: _t("Database Query Tool"),
                onExecuteQuery: async (query) => {
                    try {
                        const result = await env.services.orm.call("base", "execute_sql", [query]);
                        return result;
                    } catch (error) {
                        throw new Error(_t("Query execution failed: %s", error.message));
                    }
                }
            });
        },
        sequence: 600,
        section: "tools",
        hide: !env.user.isSystemAdmin
    }),

    // 性能监控器
    performanceMonitor: ({ env }) => ({
        type: "item",
        description: _t("Performance Monitor"),
        callback: () => {
            if (window.performanceMonitor) {
                window.performanceMonitor.toggle();
            } else {
                // 创建性能监控器
                window.performanceMonitor = new PerformanceMonitor({
                    onMetricsUpdate: (metrics) => {
                        console.log("Performance Metrics:", metrics);
                    }
                });
                window.performanceMonitor.start();
            }
        },
        sequence: 610,
        section: "tools"
    }),

    // 内存使用分析
    memoryAnalyzer: ({ env }) => ({
        type: "item",
        description: _t("Memory Usage Analyzer"),
        callback: () => {
            if (performance.memory) {
                const memory = performance.memory;
                const memoryInfo = {
                    usedJSHeapSize: (memory.usedJSHeapSize / 1048576).toFixed(2) + " MB",
                    totalJSHeapSize: (memory.totalJSHeapSize / 1048576).toFixed(2) + " MB",
                    jsHeapSizeLimit: (memory.jsHeapSizeLimit / 1048576).toFixed(2) + " MB"
                };
                
                console.group("Memory Usage");
                console.table(memoryInfo);
                console.groupEnd();
                
                env.services.notification.add(
                    _t("Memory usage logged to console"),
                    { type: "info" }
                );
            } else {
                env.services.notification.add(
                    _t("Memory API not available in this browser"),
                    { type: "warning" }
                );
            }
        },
        sequence: 620,
        section: "tools"
    }),

    // 网络请求监控
    networkMonitor: ({ env }) => ({
        type: "item",
        description: _t("Network Request Monitor"),
        callback: () => {
            if (window.networkMonitor) {
                window.networkMonitor.toggle();
            } else {
                window.networkMonitor = new NetworkMonitor({
                    onRequestComplete: (request) => {
                        console.log("Network Request:", request);
                    }
                });
                window.networkMonitor.start();
            }
        },
        sequence: 630,
        section: "tools"
    }),

    // 组件树查看器
    componentTreeViewer: ({ env }) => ({
        type: "item",
        description: _t("Component Tree Viewer"),
        callback: () => {
            const dialog = env.services.dialog;
            
            dialog.add(ComponentTreeDialog, {
                title: _t("Component Tree"),
                rootComponent: env.root,
                onComponentSelect: (component) => {
                    console.log("Selected Component:", component);
                }
            });
        },
        sequence: 640,
        section: "ui"
    }),

    // 事件监听器查看器
    eventListenerViewer: ({ env }) => ({
        type: "item",
        description: _t("Event Listener Viewer"),
        callback: () => {
            const listeners = [];
            
            // 收集所有事件监听器信息
            document.querySelectorAll("*").forEach(element => {
                const elementListeners = getEventListeners(element);
                if (Object.keys(elementListeners).length > 0) {
                    listeners.push({
                        element: element.tagName + (element.id ? "#" + element.id : ""),
                        listeners: elementListeners
                    });
                }
            });
            
            console.group("Event Listeners");
            console.table(listeners);
            console.groupEnd();
        },
        sequence: 650,
        section: "ui"
    })
};

// 注册增强的调试项目
Object.entries(EnhancedDebugMenuItems).forEach(([key, itemFunction]) => {
    registry
        .category("debug")
        .category("default")
        .add(key, itemFunction);
});
```

## 技术特点

### 1. 模块化设计
- 每个功能独立定义
- 清晰的职责分离
- 易于扩展和维护

### 2. 服务集成
- 与ORM服务集成
- 路由服务支持
- 用户权限检查

### 3. 条件显示
- 基于用户权限的显示控制
- 环境状态检查
- 动态功能启用

### 4. 国际化支持
- 完整的多语言支持
- 用户友好的描述文本
- 本地化的用户界面

## 设计模式

### 1. 工厂模式 (Factory Pattern)
- 调试项目的工厂函数
- 统一的创建接口

### 2. 注册表模式 (Registry Pattern)
- 调试功能的注册管理
- 动态功能发现

### 3. 策略模式 (Strategy Pattern)
- 不同调试功能的实现策略
- 可配置的行为模式

## 注意事项

1. **权限控制**: 确保敏感功能只对授权用户可见
2. **错误处理**: 妥善处理异步操作的错误
3. **性能影响**: 避免调试功能影响生产环境性能
4. **安全考虑**: 防止调试功能暴露敏感信息

## 扩展建议

1. **日志查看器**: 添加系统日志查看功能
2. **配置编辑器**: 提供系统配置的在线编辑
3. **模块管理**: 添加模块启用/禁用功能
4. **缓存管理**: 提供缓存清理和管理工具
5. **备份工具**: 集成数据备份和恢复功能

该调试菜单项模块为Odoo Web应用提供了丰富的系统级调试工具，通过模块化设计和权限控制确保了功能的安全性和可扩展性。

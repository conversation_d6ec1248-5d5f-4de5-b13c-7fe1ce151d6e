# DebugMenu - 调试菜单组件

## 概述

`debug_menu.js` 是 Odoo Web 核心模块的调试菜单组件，继承自DebugMenuBasic并扩展了命令面板集成功能。该组件不仅提供了传统的下拉菜单形式的调试工具，还通过命令系统支持快速搜索和执行调试命令，为开发者提供了更加便捷和高效的调试工具访问方式，提升了开发调试的效率和用户体验。

## 文件信息
- **路径**: `/web/static/src/core/debug/debug_menu.js`
- **行数**: 67
- **模块**: `@web/core/debug/debug_menu`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'           // 国际化翻译
'@web/core/dropdown/dropdown'          // 下拉菜单组件
'@web/core/dropdown/dropdown_item'     // 下拉菜单项组件
'@web/core/debug/debug_menu_basic'     // 基础调试菜单
'@web/core/commands/command_hook'      // 命令钩子
'@web/core/utils/hooks'                // 工具钩子
'@web/core/debug/debug_context'        // 调试上下文
```

## 核心功能

### 1. 组件定义

```javascript
const DebugMenu = class DebugMenu extends DebugMenuBasic {
    static components = { Dropdown, DropdownItem };
    static props = {};
}
```

**组件定义功能**:
- **继承扩展**: 继承DebugMenuBasic的所有功能
- **组件复用**: 复用下拉菜单相关组件
- **无额外属性**: 不需要额外的属性配置
- **功能增强**: 在基础功能上添加命令面板支持

### 2. 组件初始化

```javascript
setup() {
    super.setup();
    const debugContext = useEnvDebugContext();
    this.command = useService("command");
    // ... 命令注册逻辑
}
```

**初始化功能**:
- **父类初始化**: 调用父类的setup方法
- **上下文获取**: 获取调试上下文实例
- **服务注入**: 注入命令服务
- **命令注册**: 注册调试工具命令

### 3. 命令面板集成

```javascript
useCommand(
    _t("Debug tools..."),
    async () => {
        const items = await debugContext.getItems(this.env);
        let index = 0;
        const defaultCategories = items
            .filter((item) => item.type === "separator")
            .map(() => (index += 1));
        
        const provider = {
            async provide() {
                const categories = [...defaultCategories];
                let category = categories.shift();
                const result = [];
                items.forEach((item) => {
                    if (item.type === "item") {
                        result.push({
                            name: item.description.toString(),
                            action: item.callback,
                            category,
                        });
                    } else if (item.type === "separator") {
                        category = categories.shift();
                    }
                });
                return result;
            },
        };
        
        const configByNamespace = {
            default: {
                categories: defaultCategories,
                emptyMessage: _t("No debug command found"),
                placeholder: _t("Choose a debug command..."),
            },
        };
        
        const commandPaletteConfig = {
            configByNamespace,
            providers: [provider],
        };
        
        return commandPaletteConfig;
    },
    {
        category: "debug",
    }
);
```

**命令面板功能**:
- **命令注册**: 注册"Debug tools..."命令
- **动态加载**: 动态获取调试项目列表
- **分类处理**: 根据分隔符创建命令分类
- **提供者模式**: 实现命令提供者接口
- **配置生成**: 生成命令面板的配置对象
- **国际化**: 支持多语言的提示信息

### 4. 分类索引生成

```javascript
let index = 0;
const defaultCategories = items
    .filter((item) => item.type === "separator")
    .map(() => (index += 1));
```

**分类索引功能**:
- **分隔符过滤**: 筛选出分隔符类型的项目
- **索引生成**: 为每个分隔符生成递增的索引
- **分类标识**: 用于在命令面板中区分不同的命令分类
- **顺序保持**: 保持原有的分组顺序

### 5. 命令提供者实现

```javascript
const provider = {
    async provide() {
        const categories = [...defaultCategories];
        let category = categories.shift();
        const result = [];
        items.forEach((item) => {
            if (item.type === "item") {
                result.push({
                    name: item.description.toString(),
                    action: item.callback,
                    category,
                });
            } else if (item.type === "separator") {
                category = categories.shift();
            }
        });
        return result;
    },
};
```

**提供者功能**:
- **异步提供**: 异步提供命令列表
- **分类分配**: 为每个命令分配正确的分类
- **项目转换**: 将调试项目转换为命令格式
- **分隔符处理**: 根据分隔符切换当前分类
- **结果构建**: 构建符合命令面板要求的结果格式

## 使用场景

### 1. 基础调试菜单使用

```javascript
// 在开发模式下显示增强的调试菜单
<DebugMenu />
```

### 2. 命令面板访问

```javascript
// 用户可以通过以下方式访问调试工具：
// 1. 点击调试菜单下拉按钮
// 2. 使用命令面板搜索"Debug tools..."
// 3. 在命令面板中直接搜索具体的调试命令
```

### 3. 自定义调试命令

```javascript
// 注册自定义调试命令
const debugItemRegistry = registry.category("debug");

debugItemRegistry.add("custom_profiler", {
    type: "item",
    description: "Performance Profiler",
    section: "tools",
    callback: () => {
        console.profile("Custom Profiler");
        // 执行性能分析逻辑
        setTimeout(() => {
            console.profileEnd("Custom Profiler");
        }, 5000);
    },
    sequence: 10
});

// 添加分隔符
debugItemRegistry.add("separator_custom", {
    type: "separator",
    sequence: 15
});
```

### 4. 条件性调试命令

```javascript
// 根据环境条件显示调试命令
debugItemRegistry.add("production_tools", {
    type: "item",
    description: "Production Debug Tools",
    section: "tools",
    callback: () => {
        this.openProductionDebugPanel();
    },
    isDisplayed: (env) => {
        return env.debug && env.user.isSystemAdmin;
    }
});
```

## 增强示例

```javascript
// 增强的调试菜单组件
const EnhancedDebugMenu = {
    createAdvancedMenu: () => {
        class AdvancedDebugMenu extends DebugMenu {
            static props = {
                ...DebugMenu.props,
                enableKeyboardShortcuts: { type: Boolean, optional: true },
                enableCommandHistory: { type: Boolean, optional: true },
                enableQuickAccess: { type: Boolean, optional: true },
                maxHistorySize: { type: Number, optional: true }
            };

            static defaultProps = {
                enableKeyboardShortcuts: true,
                enableCommandHistory: true,
                enableQuickAccess: true,
                maxHistorySize: 20
            };

            setup() {
                super.setup();
                
                // 增强状态
                this.enhancedState = useState({
                    commandHistory: [],
                    quickAccessItems: [],
                    isCommandPaletteOpen: false,
                    lastExecutedCommand: null
                });

                // 配置选项
                this.config = {
                    enableKeyboardShortcuts: this.props.enableKeyboardShortcuts,
                    enableCommandHistory: this.props.enableCommandHistory,
                    enableQuickAccess: this.props.enableQuickAccess
                };

                // 设置键盘快捷键
                if (this.config.enableKeyboardShortcuts) {
                    this.setupKeyboardShortcuts();
                }

                // 加载命令历史
                if (this.config.enableCommandHistory) {
                    this.loadCommandHistory();
                }

                // 设置快速访问
                if (this.config.enableQuickAccess) {
                    this.setupQuickAccess();
                }

                // 注册增强的调试命令
                this.registerEnhancedCommands();
            }

            // 设置键盘快捷键
            setupKeyboardShortcuts() {
                // Ctrl+Shift+D 打开调试工具
                useCommand(
                    _t("Open Debug Tools"),
                    () => {
                        this.openDebugTools();
                    },
                    {
                        category: "debug",
                        hotkey: "ctrl+shift+d"
                    }
                );

                // Ctrl+Shift+H 显示命令历史
                useCommand(
                    _t("Debug Command History"),
                    () => {
                        this.showCommandHistory();
                    },
                    {
                        category: "debug",
                        hotkey: "ctrl+shift+h"
                    }
                );
            }

            // 注册增强的调试命令
            registerEnhancedCommands() {
                // 命令历史命令
                useCommand(
                    _t("Debug Command History"),
                    async () => {
                        return this.createHistoryCommandConfig();
                    },
                    {
                        category: "debug"
                    }
                );

                // 快速访问命令
                useCommand(
                    _t("Quick Debug Access"),
                    async () => {
                        return this.createQuickAccessConfig();
                    },
                    {
                        category: "debug"
                    }
                );

                // 调试统计命令
                useCommand(
                    _t("Debug Statistics"),
                    () => {
                        this.showDebugStatistics();
                    },
                    {
                        category: "debug"
                    }
                );
            }

            // 创建历史命令配置
            createHistoryCommandConfig() {
                const provider = {
                    async provide() {
                        return this.enhancedState.commandHistory.map((item, index) => ({
                            name: `${item.name} (${item.executedAt})`,
                            action: item.action,
                            category: 1,
                            priority: this.enhancedState.commandHistory.length - index
                        }));
                    }
                };

                return {
                    configByNamespace: {
                        default: {
                            categories: [1],
                            emptyMessage: _t("No command history found"),
                            placeholder: _t("Choose from command history...")
                        }
                    },
                    providers: [provider]
                };
            }

            // 创建快速访问配置
            createQuickAccessConfig() {
                const provider = {
                    async provide() {
                        return this.enhancedState.quickAccessItems.map(item => ({
                            name: `⭐ ${item.name}`,
                            action: item.action,
                            category: 1
                        }));
                    }
                };

                return {
                    configByNamespace: {
                        default: {
                            categories: [1],
                            emptyMessage: _t("No quick access items found"),
                            placeholder: _t("Choose quick access item...")
                        }
                    },
                    providers: [provider]
                };
            }

            // 执行调试命令
            async executeDebugCommand(command) {
                // 记录到历史
                if (this.config.enableCommandHistory) {
                    this.addToHistory(command);
                }

                // 执行命令
                try {
                    await command.action();
                    this.enhancedState.lastExecutedCommand = command;
                } catch (error) {
                    console.error('Debug command execution failed:', error);
                    this.notification.add(
                        _t('Debug command failed: %s', error.message),
                        { type: 'danger' }
                    );
                }
            }

            // 添加到历史记录
            addToHistory(command) {
                const history = this.enhancedState.commandHistory;
                const existingIndex = history.findIndex(h => h.name === command.name);
                
                if (existingIndex >= 0) {
                    history.splice(existingIndex, 1);
                }
                
                history.unshift({
                    ...command,
                    executedAt: new Date().toLocaleTimeString(),
                    timestamp: Date.now()
                });
                
                if (history.length > this.props.maxHistorySize) {
                    history.splice(this.props.maxHistorySize);
                }
                
                this.saveCommandHistory();
            }

            // 加载命令历史
            loadCommandHistory() {
                try {
                    const history = localStorage.getItem('debug_command_history');
                    if (history) {
                        this.enhancedState.commandHistory = JSON.parse(history);
                    }
                } catch (error) {
                    console.warn('Failed to load command history:', error);
                }
            }

            // 保存命令历史
            saveCommandHistory() {
                try {
                    localStorage.setItem(
                        'debug_command_history',
                        JSON.stringify(this.enhancedState.commandHistory)
                    );
                } catch (error) {
                    console.warn('Failed to save command history:', error);
                }
            }

            // 设置快速访问
            setupQuickAccess() {
                // 从本地存储加载快速访问项目
                try {
                    const quickAccess = localStorage.getItem('debug_quick_access');
                    if (quickAccess) {
                        this.enhancedState.quickAccessItems = JSON.parse(quickAccess);
                    }
                } catch (error) {
                    console.warn('Failed to load quick access items:', error);
                }
            }

            // 添加到快速访问
            addToQuickAccess(command) {
                const quickAccess = this.enhancedState.quickAccessItems;
                const existingIndex = quickAccess.findIndex(q => q.name === command.name);
                
                if (existingIndex >= 0) {
                    return; // 已存在
                }
                
                quickAccess.push({
                    name: command.name,
                    action: command.action,
                    addedAt: Date.now()
                });
                
                this.saveQuickAccess();
            }

            // 从快速访问移除
            removeFromQuickAccess(command) {
                const quickAccess = this.enhancedState.quickAccessItems;
                const index = quickAccess.findIndex(q => q.name === command.name);
                
                if (index >= 0) {
                    quickAccess.splice(index, 1);
                    this.saveQuickAccess();
                }
            }

            // 保存快速访问
            saveQuickAccess() {
                try {
                    localStorage.setItem(
                        'debug_quick_access',
                        JSON.stringify(this.enhancedState.quickAccessItems)
                    );
                } catch (error) {
                    console.warn('Failed to save quick access items:', error);
                }
            }

            // 显示调试统计
            showDebugStatistics() {
                const stats = {
                    totalCommands: this.enhancedState.commandHistory.length,
                    quickAccessItems: this.enhancedState.quickAccessItems.length,
                    lastExecuted: this.enhancedState.lastExecutedCommand?.name || 'None',
                    mostUsed: this.getMostUsedCommand()
                };

                console.table(stats);
                
                this.notification.add(
                    _t('Debug statistics logged to console'),
                    { type: 'info' }
                );
            }

            // 获取最常用的命令
            getMostUsedCommand() {
                const commandCounts = {};
                
                this.enhancedState.commandHistory.forEach(command => {
                    commandCounts[command.name] = (commandCounts[command.name] || 0) + 1;
                });
                
                let mostUsed = null;
                let maxCount = 0;
                
                for (const [name, count] of Object.entries(commandCounts)) {
                    if (count > maxCount) {
                        maxCount = count;
                        mostUsed = name;
                    }
                }
                
                return mostUsed || 'None';
            }

            // 清除历史记录
            clearHistory() {
                this.enhancedState.commandHistory = [];
                localStorage.removeItem('debug_command_history');
            }

            // 清除快速访问
            clearQuickAccess() {
                this.enhancedState.quickAccessItems = [];
                localStorage.removeItem('debug_quick_access');
            }
        }

        return AdvancedDebugMenu;
    }
};

// 使用示例
const AdvancedDebugMenu = EnhancedDebugMenu.createAdvancedMenu();

// 带历史记录和快速访问的调试菜单
<AdvancedDebugMenu
    enableKeyboardShortcuts={true}
    enableCommandHistory={true}
    enableQuickAccess={true}
    maxHistorySize={50}
/>
```

## 技术特点

### 1. 继承扩展
- 继承基础调试菜单功能
- 扩展命令面板集成
- 保持向后兼容性

### 2. 命令系统集成
- 无缝集成命令面板
- 支持搜索和快速访问
- 分类和组织命令

### 3. 动态配置
- 运行时生成命令配置
- 灵活的分类系统
- 可扩展的提供者模式

### 4. 用户体验
- 多种访问方式
- 直观的搜索界面
- 国际化支持

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- 扩展基础调试菜单功能
- 代码复用和功能增强

### 2. 提供者模式 (Provider Pattern)
- 命令提供者接口
- 动态内容生成

### 3. 适配器模式 (Adapter Pattern)
- 调试项目到命令的转换
- 不同系统间的接口适配

## 注意事项

1. **性能考虑**: 避免频繁的命令重新生成
2. **内存管理**: 及时清理不需要的命令引用
3. **用户体验**: 提供清晰的命令描述和分类
4. **安全性**: 确保调试命令只在适当的环境下可用

## 扩展建议

1. **命令历史**: 记录和管理命令执行历史
2. **快捷键**: 为常用调试命令添加快捷键
3. **命令收藏**: 支持收藏常用的调试命令
4. **批量执行**: 支持批量执行多个调试命令
5. **命令模板**: 提供可参数化的命令模板

该调试菜单组件通过集成命令面板系统，为开发者提供了更加便捷和高效的调试工具访问方式，大大提升了开发调试的效率。

# DebugUtils - 调试工具函数

## 概述

`debug_utils.js` 是 Odoo Web 核心模块的调试工具函数库，提供了调试过程中常用的实用函数。该模块目前包含了模型编辑调试功能，允许开发者在调试模式下快速打开和编辑指定的数据模型记录，为开发和调试过程提供了便捷的数据操作工具，简化了调试过程中的数据查看和修改操作。

## 文件信息
- **路径**: `/web/static/src/core/debug/debug_utils.js`
- **行数**: 17
- **模块**: `@web/core/debug/debug_utils`

## 依赖关系

```javascript
// 无外部依赖
// 该模块是纯工具函数库，不依赖其他模块
```

## 核心功能

### 1. 模型编辑调试函数

```javascript
function editModelDebug(env, title, model, id) {
    return env.services.action.doAction({
        res_model: model,
        res_id: id,
        name: title,
        type: "ir.actions.act_window",
        views: [[false, "form"]],
        view_mode: "form",
        target: "current",
    });
}
```

**模型编辑功能**:
- **动作执行**: 通过action服务执行窗口动作
- **表单视图**: 以表单模式打开指定记录
- **当前窗口**: 在当前窗口中打开编辑界面
- **灵活参数**: 支持自定义标题、模型和记录ID
- **调试专用**: 专为调试场景设计的快速编辑功能

### 2. 函数参数说明

```javascript
/**
 * 在调试模式下编辑模型记录
 * @param {Object} env - 环境对象，包含服务和上下文
 * @param {string} title - 窗口标题
 * @param {string} model - 数据模型名称
 * @param {number|string} id - 记录ID
 * @returns {Promise} 返回动作执行的Promise
 */
```

**参数说明**:
- **env**: 包含所有服务的环境对象
- **title**: 编辑窗口显示的标题
- **model**: 要编辑的Odoo数据模型名称
- **id**: 要编辑的记录的唯一标识符
- **返回值**: 返回动作执行的Promise对象

## 使用场景

### 1. 基础模型编辑

```javascript
// 编辑用户记录
editModelDebug(env, "Edit User", "res.users", 1);

// 编辑合作伙伴记录
editModelDebug(env, "Edit Partner", "res.partner", 123);

// 编辑产品记录
editModelDebug(env, "Edit Product", "product.product", 456);
```

### 2. 在调试菜单项中使用

```javascript
// 在调试菜单项中集成模型编辑功能
function editCurrentRecord({ env }) {
    const currentRecord = env.model.root.data;
    if (currentRecord && currentRecord.id) {
        return {
            type: "item",
            description: _t("Edit Current Record"),
            callback: () => {
                editModelDebug(
                    env,
                    `Edit ${env.model.config.resModel} Record`,
                    env.model.config.resModel,
                    currentRecord.id
                );
            },
            sequence: 100,
            section: "record",
        };
    }
}
```

### 3. 批量记录编辑

```javascript
// 批量编辑多个记录
function editMultipleRecords(env, model, ids) {
    const promises = ids.map(id => {
        return editModelDebug(
            env,
            `Edit ${model} Record ${id}`,
            model,
            id
        );
    });
    
    return Promise.all(promises);
}

// 使用示例
editMultipleRecords(env, "res.partner", [1, 2, 3, 4, 5]);
```

### 4. 条件性记录编辑

```javascript
// 根据权限编辑记录
async function editRecordWithPermissionCheck(env, title, model, id) {
    try {
        // 检查用户是否有编辑权限
        const hasPermission = await env.services.orm.call(
            model,
            "check_access_rights",
            ["write"]
        );
        
        if (hasPermission) {
            return editModelDebug(env, title, model, id);
        } else {
            env.services.notification.add(
                _t("You don't have permission to edit this record"),
                { type: "warning" }
            );
        }
    } catch (error) {
        env.services.notification.add(
            _t("Error checking permissions: %s", error.message),
            { type: "danger" }
        );
    }
}
```

## 增强示例

```javascript
// 增强的调试工具函数库
const EnhancedDebugUtils = {
    // 增强的模型编辑函数
    editModelDebugAdvanced: (env, options = {}) => {
        const {
            title,
            model,
            id,
            viewMode = "form",
            target = "current",
            context = {},
            domain = [],
            readonly = false
        } = options;

        const action = {
            res_model: model,
            res_id: id,
            name: title,
            type: "ir.actions.act_window",
            views: [[false, viewMode]],
            view_mode: viewMode,
            target: target,
            context: {
                ...context,
                debug: true,
                edit_mode: !readonly
            }
        };

        if (domain.length > 0) {
            action.domain = domain;
        }

        return env.services.action.doAction(action);
    },

    // 查看模型记录（只读模式）
    viewModelDebug: (env, title, model, id) => {
        return EnhancedDebugUtils.editModelDebugAdvanced(env, {
            title,
            model,
            id,
            readonly: true
        });
    },

    // 在新窗口中编辑模型
    editModelDebugNewWindow: (env, title, model, id) => {
        return EnhancedDebugUtils.editModelDebugAdvanced(env, {
            title,
            model,
            id,
            target: "new"
        });
    },

    // 编辑模型的列表视图
    editModelListDebug: (env, title, model, domain = []) => {
        return env.services.action.doAction({
            res_model: model,
            name: title,
            type: "ir.actions.act_window",
            views: [[false, "list"], [false, "form"]],
            view_mode: "list,form",
            target: "current",
            domain: domain,
            context: { debug: true }
        });
    },

    // 创建新记录
    createModelDebug: (env, title, model, defaultValues = {}) => {
        return env.services.action.doAction({
            res_model: model,
            name: title,
            type: "ir.actions.act_window",
            views: [[false, "form"]],
            view_mode: "form",
            target: "current",
            context: {
                debug: true,
                default_values: defaultValues
            }
        });
    },

    // 复制记录
    duplicateModelDebug: async (env, title, model, id) => {
        try {
            const duplicatedId = await env.services.orm.call(
                model,
                "copy",
                [id]
            );
            
            return EnhancedDebugUtils.editModelDebugAdvanced(env, {
                title: `${title} (Copy)`,
                model,
                id: duplicatedId
            });
        } catch (error) {
            env.services.notification.add(
                _t("Error duplicating record: %s", error.message),
                { type: "danger" }
            );
        }
    },

    // 删除记录（带确认）
    deleteModelDebug: async (env, model, id) => {
        const confirmed = await new Promise((resolve) => {
            env.services.dialog.add(ConfirmationDialog, {
                title: _t("Delete Record"),
                body: _t("Are you sure you want to delete this record?"),
                confirm: () => resolve(true),
                cancel: () => resolve(false)
            });
        });

        if (confirmed) {
            try {
                await env.services.orm.unlink(model, [id]);
                env.services.notification.add(
                    _t("Record deleted successfully"),
                    { type: "success" }
                );
            } catch (error) {
                env.services.notification.add(
                    _t("Error deleting record: %s", error.message),
                    { type: "danger" }
                );
            }
        }
    },

    // 获取记录信息
    getModelRecordInfo: async (env, model, id) => {
        try {
            const record = await env.services.orm.read(model, [id]);
            console.group(`Record Info: ${model}(${id})`);
            console.table(record[0]);
            console.groupEnd();
            return record[0];
        } catch (error) {
            console.error("Error fetching record info:", error);
            env.services.notification.add(
                _t("Error fetching record info: %s", error.message),
                { type: "danger" }
            );
        }
    },

    // 获取模型字段信息
    getModelFieldsInfo: async (env, model) => {
        try {
            const fields = await env.services.orm.call(
                model,
                "fields_get"
            );
            console.group(`Fields Info: ${model}`);
            console.table(fields);
            console.groupEnd();
            return fields;
        } catch (error) {
            console.error("Error fetching fields info:", error);
            env.services.notification.add(
                _t("Error fetching fields info: %s", error.message),
                { type: "danger" }
            );
        }
    },

    // 执行模型方法
    executeModelMethod: async (env, model, method, args = [], kwargs = {}) => {
        try {
            const result = await env.services.orm.call(model, method, args, kwargs);
            console.log(`Method ${method} result:`, result);
            env.services.notification.add(
                _t("Method executed successfully"),
                { type: "success" }
            );
            return result;
        } catch (error) {
            console.error("Error executing method:", error);
            env.services.notification.add(
                _t("Error executing method: %s", error.message),
                { type: "danger" }
            );
        }
    },

    // 搜索记录
    searchModelRecords: async (env, model, domain = [], limit = 10) => {
        try {
            const records = await env.services.orm.searchRead(
                model,
                domain,
                [],
                { limit }
            );
            console.group(`Search Results: ${model}`);
            console.table(records);
            console.groupEnd();
            return records;
        } catch (error) {
            console.error("Error searching records:", error);
            env.services.notification.add(
                _t("Error searching records: %s", error.message),
                { type: "danger" }
            );
        }
    },

    // 导出记录数据
    exportModelRecord: async (env, model, id) => {
        try {
            const record = await env.services.orm.read(model, [id]);
            const dataStr = JSON.stringify(record[0], null, 2);
            
            const blob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `${model}_${id}.json`;
            a.click();
            
            URL.revokeObjectURL(url);
            
            env.services.notification.add(
                _t("Record exported successfully"),
                { type: "success" }
            );
        } catch (error) {
            env.services.notification.add(
                _t("Error exporting record: %s", error.message),
                { type: "danger" }
            );
        }
    }
};

// 使用示例
// 高级编辑
EnhancedDebugUtils.editModelDebugAdvanced(env, {
    title: "Advanced Edit User",
    model: "res.users",
    id: 1,
    viewMode: "form",
    target: "new",
    context: { show_debug_info: true }
});

// 查看记录信息
EnhancedDebugUtils.getModelRecordInfo(env, "res.partner", 123);

// 搜索记录
EnhancedDebugUtils.searchModelRecords(env, "product.product", [["active", "=", true]], 20);
```

## 技术特点

### 1. 简洁设计
- 单一职责的工具函数
- 最小化的依赖关系
- 清晰的函数接口

### 2. 服务集成
- 与action服务无缝集成
- 标准的Odoo动作格式
- 完整的参数支持

### 3. 调试专用
- 专为调试场景设计
- 快速访问和编辑功能
- 开发者友好的接口

### 4. 可扩展性
- 易于扩展的函数库
- 模块化的设计
- 可重用的工具函数

## 设计模式

### 1. 工具模式 (Utility Pattern)
- 提供通用的工具函数
- 无状态的函数设计

### 2. 适配器模式 (Adapter Pattern)
- 简化复杂的动作调用
- 提供便捷的接口

### 3. 门面模式 (Facade Pattern)
- 隐藏底层复杂性
- 提供简单的调用接口

## 注意事项

1. **权限检查**: 确保用户有相应的模型访问权限
2. **错误处理**: 妥善处理模型操作的错误
3. **性能考虑**: 避免频繁的数据库操作
4. **安全性**: 验证输入参数的有效性

## 扩展建议

1. **批量操作**: 添加批量编辑和操作功能
2. **权限检查**: 集成权限验证机制
3. **缓存支持**: 添加数据缓存功能
4. **日志记录**: 记录调试操作的日志
5. **模板功能**: 提供常用操作的模板

该调试工具函数库为Odoo Web应用提供了便捷的数据模型操作功能，通过简洁的接口和完整的功能支持，大大简化了调试过程中的数据操作需求。

# @web/core/effects/effect_service.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/effects/effect_service.js`
- **原始路径**: `/web/static/src/core/effects/effect_service.js`
- **代码行数**: 93行
- **作用**: 提供视觉效果服务，管理和显示各种用户界面特效，如彩虹人等庆祝动画

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 视觉效果系统的架构设计和实现
- 服务注册表模式在效果管理中的应用
- 组件动态加载和覆盖层集成
- 用户偏好设置的条件渲染逻辑
- 企业级用户体验增强的实现方式

## 📚 核心概念

### 什么是效果服务？
效果服务是一个**用户体验增强系统**，主要功能：
- **视觉反馈**: 为用户操作提供即时的视觉反馈
- **庆祝动画**: 在完成重要操作时显示庆祝效果
- **用户偏好**: 根据用户设置决定是否显示效果
- **降级处理**: 在禁用效果时提供替代的通知方式

### 核心架构组成
```javascript
// 效果注册表
const effectRegistry = registry.category("effects");

// 效果服务
const effectService = {
    dependencies: ["overlay"],    // 依赖覆盖层服务
    start(env, services) {        // 服务启动
        return {
            add(params)           // 添加效果方法
        };
    }
};

// 彩虹人效果
function rainbowMan(env, params) {
    // 效果实现逻辑
    return { Component, props };
}

// 效果参数结构
const effectParams = {
    type: "string",              // 效果类型
    message: "string",           // 显示消息
    img_url: "string",          // 图片URL
    fadeout: "string",          // 淡出速度
    Component: "Component",      // 自定义组件
    props: "object"             // 组件属性
};
```

### 基本使用模式
```javascript
import { useService } from '@web/core/utils/hooks';

class SuccessComponent extends Component {
    setup() {
        this.effectService = useService('effect');
    }
    
    onTaskCompleted() {
        // 显示彩虹人庆祝效果
        this.effectService.add({
            type: 'rainbow_man',
            message: 'Task completed successfully!',
            fadeout: 'slow'
        });
    }
    
    onGoalAchieved() {
        // 显示自定义组件效果
        this.effectService.add({
            type: 'rainbow_man',
            message: 'Congratulations!',
            Component: CustomCelebrationComponent,
            props: {
                achievement: 'Monthly Goal',
                points: 1000
            }
        });
    }
}
```

## 🔍 核心实现详解

### 1. 效果注册表系统

#### 注册表初始化
```javascript
const effectRegistry = registry.category("effects");
```

**注册表特点**：
- **分类管理**: 使用registry的category功能分类管理效果
- **动态注册**: 支持运行时动态添加新的效果类型
- **类型安全**: 通过注册表确保效果类型的有效性
- **扩展性**: 易于扩展新的效果类型

#### 效果注册机制
```javascript
effectRegistry.add("rainbow_man", rainbowMan);
```

**注册策略**：
- **键值映射**: 使用字符串键映射到效果函数
- **函数注册**: 注册的是效果处理函数而非组件
- **标准接口**: 所有效果函数遵循统一的接口规范
- **命名约定**: 使用下划线分隔的命名约定

### 2. 彩虹人效果实现

#### 参数处理和验证
```javascript
function rainbowMan(env, params = {}) {
    let message = params.message;
    if (message instanceof Element) {
        console.log(
            "Providing an HTML element to an effect is deprecated. Note that all event handlers will be lost."
        );
        message = message.outerHTML;
    } else if (!message) {
        message = _t("Well Done!");
    }
    // ...
}
```

**参数处理特点**：
- **类型检查**: 检查message参数的类型
- **向后兼容**: 支持HTML元素但给出弃用警告
- **默认值**: 提供国际化的默认消息
- **安全转换**: 将HTML元素安全转换为字符串

#### 用户偏好检查
```javascript
if (user.showEffect) {
    const props = {
        imgUrl: params.img_url || "/web/static/img/smile.svg",
        fadeout: params.fadeout || "medium",
        message,
        Component: params.Component,
        props: params.props,
    };
    return { Component: RainbowMan, props };
}
env.services.notification.add(message);
```

**偏好处理策略**：
- **条件渲染**: 根据用户设置决定显示方式
- **降级处理**: 禁用效果时使用通知替代
- **属性映射**: 将参数映射到组件属性
- **默认配置**: 为所有属性提供合理默认值

### 3. 效果服务架构

#### 服务定义和依赖
```javascript
const effectService = {
    dependencies: ["overlay"],
    start(env, { overlay }) {
        // 服务实现
    }
};
```

**架构特点**：
- **依赖注入**: 明确声明对覆盖层服务的依赖
- **服务模式**: 遵循Odoo的标准服务模式
- **环境访问**: 通过env参数访问全局环境
- **资源管理**: 通过覆盖层服务管理组件生命周期

#### 效果添加机制
```javascript
const add = (params = {}) => {
    const type = params.type || "rainbow_man";
    const effect = effectRegistry.get(type);
    const { Component, props } = effect(env, params) || {};
    if (Component) {
        const remove = overlay.add(Component, {
            ...props,
            close: () => remove(),
        });
    }
};
```

**添加策略分析**：
- **类型解析**: 从参数中解析效果类型
- **注册表查询**: 从注册表获取对应的效果函数
- **组件创建**: 调用效果函数获取组件和属性
- **覆盖层集成**: 使用覆盖层服务显示组件

### 4. 组件生命周期管理

#### 自动清理机制
```javascript
const remove = overlay.add(Component, {
    ...props,
    close: () => remove(),
});
```

**生命周期特点**：
- **自引用清理**: close函数引用remove函数实现自清理
- **属性传递**: 将close函数作为属性传递给组件
- **资源管理**: 确保组件正确清理避免内存泄漏
- **用户控制**: 允许用户手动关闭效果

## 🎨 实际应用场景

### 1. 高级效果管理系统
```javascript
class AdvancedEffectManager {
    constructor() {
        this.effectService = null;
        this.effectQueue = [];
        this.activeEffects = new Map();
        this.effectHistory = [];
        this.userPreferences = {
            enableEffects: true,
            effectVolume: 0.5,
            animationSpeed: 'medium',
            maxConcurrentEffects: 3
        };
        this.setupCustomEffects();
    }
    
    initialize(effectService) {
        this.effectService = effectService;
        this.setupEffectInterceptor();
    }
    
    setupCustomEffects() {
        // 成就解锁效果
        this.registerEffect('achievement_unlock', (env, params) => {
            if (!this.userPreferences.enableEffects) {
                env.services.notification.add(`Achievement Unlocked: ${params.title}`);
                return;
            }
            
            return {
                Component: AchievementUnlockComponent,
                props: {
                    title: params.title,
                    description: params.description,
                    icon: params.icon,
                    rarity: params.rarity || 'common',
                    points: params.points || 0,
                    fadeout: this.calculateFadeoutTime(params.rarity),
                    onClose: () => this.onEffectClose('achievement_unlock')
                }
            };
        });
        
        // 等级提升效果
        this.registerEffect('level_up', (env, params) => {
            if (!this.userPreferences.enableEffects) {
                env.services.notification.add(`Level Up! You are now level ${params.newLevel}`);
                return;
            }
            
            return {
                Component: LevelUpComponent,
                props: {
                    oldLevel: params.oldLevel,
                    newLevel: params.newLevel,
                    experience: params.experience,
                    rewards: params.rewards || [],
                    animationSpeed: this.userPreferences.animationSpeed,
                    onClose: () => this.onEffectClose('level_up')
                }
            };
        });
        
        // 连击效果
        this.registerEffect('combo_streak', (env, params) => {
            if (!this.userPreferences.enableEffects) {
                env.services.notification.add(`${params.count}x Combo!`);
                return;
            }
            
            return {
                Component: ComboStreakComponent,
                props: {
                    count: params.count,
                    multiplier: params.multiplier || 1,
                    action: params.action,
                    intensity: this.calculateComboIntensity(params.count),
                    fadeout: 'fast',
                    onClose: () => this.onEffectClose('combo_streak')
                }
            };
        });
        
        // 错误反馈效果
        this.registerEffect('error_feedback', (env, params) => {
            return {
                Component: ErrorFeedbackComponent,
                props: {
                    message: params.message,
                    severity: params.severity || 'error',
                    suggestion: params.suggestion,
                    canRetry: params.canRetry || false,
                    onRetry: params.onRetry,
                    fadeout: 'medium',
                    onClose: () => this.onEffectClose('error_feedback')
                }
            };
        });
        
        // 进度庆祝效果
        this.registerEffect('progress_celebration', (env, params) => {
            if (!this.userPreferences.enableEffects) {
                env.services.notification.add(`Progress: ${params.percentage}% complete`);
                return;
            }
            
            return {
                Component: ProgressCelebrationComponent,
                props: {
                    percentage: params.percentage,
                    milestone: params.milestone,
                    totalSteps: params.totalSteps,
                    currentStep: params.currentStep,
                    celebrationType: this.getCelebrationType(params.percentage),
                    fadeout: this.calculateProgressFadeout(params.percentage),
                    onClose: () => this.onEffectClose('progress_celebration')
                }
            };
        });
    }
    
    registerEffect(type, effectFunction) {
        const effectRegistry = registry.category("effects");
        effectRegistry.add(type, effectFunction);
    }
    
    setupEffectInterceptor() {
        // 拦截原始的add方法
        const originalAdd = this.effectService.add;
        
        this.effectService.add = (params = {}) => {
            // 检查并发限制
            if (this.activeEffects.size >= this.userPreferences.maxConcurrentEffects) {
                this.queueEffect(params);
                return;
            }
            
            // 记录效果历史
            this.recordEffectHistory(params);
            
            // 应用用户偏好
            const enhancedParams = this.applyUserPreferences(params);
            
            // 执行原始添加逻辑
            const effectId = this.generateEffectId();
            this.activeEffects.set(effectId, {
                type: enhancedParams.type,
                startTime: Date.now(),
                params: enhancedParams
            });
            
            return originalAdd(enhancedParams);
        };
    }
    
    queueEffect(params) {
        this.effectQueue.push({
            params,
            timestamp: Date.now(),
            priority: this.calculateEffectPriority(params.type)
        });
        
        // 按优先级排序
        this.effectQueue.sort((a, b) => b.priority - a.priority);
    }
    
    processEffectQueue() {
        while (this.effectQueue.length > 0 && 
               this.activeEffects.size < this.userPreferences.maxConcurrentEffects) {
            const queuedEffect = this.effectQueue.shift();
            this.effectService.add(queuedEffect.params);
        }
    }
    
    onEffectClose(effectType) {
        // 从活跃效果中移除
        for (const [id, effect] of this.activeEffects.entries()) {
            if (effect.type === effectType) {
                this.activeEffects.delete(id);
                break;
            }
        }
        
        // 处理队列中的效果
        this.processEffectQueue();
        
        // 记录效果完成
        this.recordEffectCompletion(effectType);
    }
    
    applyUserPreferences(params) {
        const enhanced = { ...params };
        
        // 应用动画速度偏好
        if (enhanced.fadeout && this.userPreferences.animationSpeed !== 'medium') {
            enhanced.fadeout = this.userPreferences.animationSpeed;
        }
        
        // 应用音效设置
        if (enhanced.playSound && this.userPreferences.effectVolume === 0) {
            enhanced.playSound = false;
        }
        
        return enhanced;
    }
    
    calculateFadeoutTime(rarity) {
        const timeMap = {
            'common': 'fast',
            'rare': 'medium',
            'epic': 'slow',
            'legendary': 'no'
        };
        return timeMap[rarity] || 'medium';
    }
    
    calculateComboIntensity(count) {
        if (count < 5) return 'low';
        if (count < 10) return 'medium';
        if (count < 20) return 'high';
        return 'extreme';
    }
    
    getCelebrationType(percentage) {
        if (percentage >= 100) return 'completion';
        if (percentage >= 75) return 'major';
        if (percentage >= 50) return 'moderate';
        if (percentage >= 25) return 'minor';
        return 'initial';
    }
    
    calculateProgressFadeout(percentage) {
        if (percentage >= 100) return 'slow';
        if (percentage >= 50) return 'medium';
        return 'fast';
    }
    
    calculateEffectPriority(type) {
        const priorityMap = {
            'error_feedback': 100,
            'achievement_unlock': 80,
            'level_up': 70,
            'progress_celebration': 60,
            'combo_streak': 50,
            'rainbow_man': 40
        };
        return priorityMap[type] || 30;
    }
    
    generateEffectId() {
        return `effect_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    recordEffectHistory(params) {
        this.effectHistory.push({
            type: params.type,
            timestamp: Date.now(),
            params: { ...params }
        });
        
        // 限制历史记录长度
        if (this.effectHistory.length > 100) {
            this.effectHistory.shift();
        }
    }
    
    recordEffectCompletion(effectType) {
        const now = Date.now();
        const historyEntry = this.effectHistory
            .reverse()
            .find(entry => entry.type === effectType && !entry.completedAt);
        
        if (historyEntry) {
            historyEntry.completedAt = now;
            historyEntry.duration = now - historyEntry.timestamp;
        }
        
        this.effectHistory.reverse();
    }
    
    // 效果统计和分析
    getEffectStatistics() {
        const stats = {
            totalEffects: this.effectHistory.length,
            effectsByType: {},
            averageDuration: 0,
            mostUsedEffect: null,
            recentActivity: []
        };
        
        let totalDuration = 0;
        let completedEffects = 0;
        
        this.effectHistory.forEach(entry => {
            // 按类型统计
            stats.effectsByType[entry.type] = (stats.effectsByType[entry.type] || 0) + 1;
            
            // 计算平均持续时间
            if (entry.duration) {
                totalDuration += entry.duration;
                completedEffects++;
            }
            
            // 最近活动（最近10个）
            if (stats.recentActivity.length < 10) {
                stats.recentActivity.push({
                    type: entry.type,
                    timestamp: new Date(entry.timestamp),
                    duration: entry.duration
                });
            }
        });
        
        // 计算平均持续时间
        stats.averageDuration = completedEffects > 0 ? totalDuration / completedEffects : 0;
        
        // 找出最常用的效果
        let maxCount = 0;
        for (const [type, count] of Object.entries(stats.effectsByType)) {
            if (count > maxCount) {
                maxCount = count;
                stats.mostUsedEffect = type;
            }
        }
        
        return stats;
    }
    
    // 用户偏好管理
    updateUserPreferences(preferences) {
        this.userPreferences = { ...this.userPreferences, ...preferences };
        this.savePreferences();
    }
    
    savePreferences() {
        localStorage.setItem('effect_preferences', JSON.stringify(this.userPreferences));
    }
    
    loadPreferences() {
        try {
            const saved = localStorage.getItem('effect_preferences');
            if (saved) {
                this.userPreferences = { ...this.userPreferences, ...JSON.parse(saved) };
            }
        } catch (error) {
            console.warn('Failed to load effect preferences:', error);
        }
    }
    
    // 效果预览
    previewEffect(type, params = {}) {
        const previewParams = {
            ...params,
            type,
            fadeout: 'fast',
            preview: true
        };
        
        this.effectService.add(previewParams);
    }
    
    // 批量效果
    addEffectSequence(effects, delay = 500) {
        effects.forEach((effect, index) => {
            setTimeout(() => {
                this.effectService.add(effect);
            }, index * delay);
        });
    }
    
    // 清理所有效果
    clearAllEffects() {
        this.activeEffects.clear();
        this.effectQueue.length = 0;
        
        // 触发所有活跃效果的关闭
        document.querySelectorAll('.o_effect_component').forEach(el => {
            const closeBtn = el.querySelector('.o_effect_close');
            if (closeBtn) {
                closeBtn.click();
            }
        });
    }
}

// 自定义效果组件示例
class AchievementUnlockComponent extends Component {
    static template = xml`
        <div class="achievement-unlock-effect">
            <div class="achievement-badge">
                <img t-att-src="props.icon" alt="Achievement"/>
                <div class="achievement-rarity" t-att-class="props.rarity">
                    <span t-esc="props.rarity.toUpperCase()"/>
                </div>
            </div>
            <div class="achievement-content">
                <h3 class="achievement-title" t-esc="props.title"/>
                <p class="achievement-description" t-esc="props.description"/>
                <div class="achievement-points">
                    <span t-esc="props.points"/> points
                </div>
            </div>
            <button class="achievement-close" t-on-click="props.onClose">×</button>
        </div>
    `;
    
    setup() {
        // 自动关闭逻辑
        if (this.props.fadeout !== 'no') {
            const delay = this.getFadeoutDelay();
            setTimeout(() => {
                this.props.onClose();
            }, delay);
        }
    }
    
    getFadeoutDelay() {
        const delays = {
            'fast': 2000,
            'medium': 4000,
            'slow': 6000
        };
        return delays[this.props.fadeout] || 4000;
    }
}

// 使用示例
const effectManager = new AdvancedEffectManager();

// 在组件中使用
class GameComponent extends Component {
    setup() {
        this.effectService = useService('effect');
        effectManager.initialize(this.effectService);
        effectManager.loadPreferences();
    }
    
    onPlayerLevelUp(oldLevel, newLevel) {
        effectManager.effectService.add({
            type: 'level_up',
            oldLevel,
            newLevel,
            experience: this.player.experience,
            rewards: this.calculateLevelRewards(newLevel)
        });
    }
    
    onAchievementUnlocked(achievement) {
        effectManager.effectService.add({
            type: 'achievement_unlock',
            title: achievement.title,
            description: achievement.description,
            icon: achievement.icon,
            rarity: achievement.rarity,
            points: achievement.points
        });
    }
    
    onComboAchieved(count, action) {
        effectManager.effectService.add({
            type: 'combo_streak',
            count,
            action,
            multiplier: Math.floor(count / 5) + 1
        });
    }
}
```

### 2. 效果服务测试框架
```javascript
class EffectServiceTestFramework {
    constructor() {
        this.testSuites = new Map();
        this.mockServices = this.createMockServices();
        this.testResults = [];
        this.setupTestSuites();
    }

    createMockServices() {
        return {
            overlay: {
                add: jest.fn((Component, props) => {
                    const mockRemove = jest.fn();
                    return mockRemove;
                }),
                remove: jest.fn()
            },
            notification: {
                add: jest.fn()
            }
        };
    }

    setupTestSuites() {
        this.testSuites.set('service_initialization', {
            name: 'Service Initialization Tests',
            tests: [
                () => this.testServiceDefinition(),
                () => this.testDependencyInjection(),
                () => this.testServiceRegistration(),
                () => this.testServiceStart()
            ]
        });

        this.testSuites.set('effect_registry', {
            name: 'Effect Registry Tests',
            tests: [
                () => this.testEffectRegistration(),
                () => this.testEffectRetrieval(),
                () => this.testCustomEffectRegistration(),
                () => this.testEffectOverride()
            ]
        });

        this.testSuites.set('rainbow_man_effect', {
            name: 'Rainbow Man Effect Tests',
            tests: [
                () => this.testRainbowManBasic(),
                () => this.testRainbowManParameters(),
                () => this.testUserPreferences(),
                () => this.testMessageHandling()
            ]
        });

        this.testSuites.set('effect_execution', {
            name: 'Effect Execution Tests',
            tests: [
                () => this.testEffectExecution(),
                () => this.testEffectWithCustomComponent(),
                () => this.testEffectFallback(),
                () => this.testEffectCleanup()
            ]
        });

        this.testSuites.set('integration', {
            name: 'Integration Tests',
            tests: [
                () => this.testOverlayIntegration(),
                () => this.testNotificationFallback(),
                () => this.testMultipleEffects(),
                () => this.testEffectChaining()
            ]
        });
    }

    testServiceDefinition() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            // 测试服务对象存在
            if (effectService && typeof effectService === 'object') {
                results.tests.push({
                    name: 'Service object exists',
                    status: 'passed',
                    message: 'Effect service is properly defined'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Service object exists',
                    status: 'failed',
                    message: 'Effect service is not defined'
                });
                results.failed++;
            }

            // 测试依赖声明
            if (effectService.dependencies && effectService.dependencies.includes('overlay')) {
                results.tests.push({
                    name: 'Dependencies declared',
                    status: 'passed',
                    message: 'Service dependencies are properly declared'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Dependencies declared',
                    status: 'failed',
                    message: 'Service dependencies are missing or incorrect'
                });
                results.failed++;
            }

            // 测试start方法
            if (typeof effectService.start === 'function') {
                results.tests.push({
                    name: 'Start method exists',
                    status: 'passed',
                    message: 'Service start method is defined'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Start method exists',
                    status: 'failed',
                    message: 'Service start method is missing'
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Service definition',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    testEffectRegistration() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            const effectRegistry = registry.category("effects");

            // 测试注册表存在
            if (effectRegistry) {
                results.tests.push({
                    name: 'Effect registry exists',
                    status: 'passed',
                    message: 'Effect registry is available'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Effect registry exists',
                    status: 'failed',
                    message: 'Effect registry is not available'
                });
                results.failed++;
            }

            // 测试彩虹人效果注册
            const rainbowManEffect = effectRegistry.get('rainbow_man');
            if (rainbowManEffect && typeof rainbowManEffect === 'function') {
                results.tests.push({
                    name: 'Rainbow man effect registered',
                    status: 'passed',
                    message: 'Rainbow man effect is properly registered'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Rainbow man effect registered',
                    status: 'failed',
                    message: 'Rainbow man effect is not registered'
                });
                results.failed++;
            }

            // 测试自定义效果注册
            const testEffect = (env, params) => ({ Component: null, props: {} });
            effectRegistry.add('test_effect', testEffect);

            const retrievedEffect = effectRegistry.get('test_effect');
            if (retrievedEffect === testEffect) {
                results.tests.push({
                    name: 'Custom effect registration',
                    status: 'passed',
                    message: 'Custom effects can be registered'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Custom effect registration',
                    status: 'failed',
                    message: 'Custom effect registration failed'
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Effect registration',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    testRainbowManBasic() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            const mockEnv = {
                services: {
                    notification: this.mockServices.notification
                }
            };

            // 模拟用户启用效果
            const originalShowEffect = user.showEffect;
            user.showEffect = true;

            // 测试基本彩虹人效果
            const result = rainbowMan(mockEnv, {});

            if (result && result.Component && result.props) {
                results.tests.push({
                    name: 'Basic rainbow man effect',
                    status: 'passed',
                    message: 'Rainbow man effect returns component and props'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Basic rainbow man effect',
                    status: 'failed',
                    message: 'Rainbow man effect does not return expected structure'
                });
                results.failed++;
            }

            // 测试默认消息
            if (result.props.message === _t("Well Done!")) {
                results.tests.push({
                    name: 'Default message',
                    status: 'passed',
                    message: 'Default message is correctly set'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Default message',
                    status: 'failed',
                    message: 'Default message is incorrect'
                });
                results.failed++;
            }

            // 测试默认图片URL
            if (result.props.imgUrl === "/web/static/img/smile.svg") {
                results.tests.push({
                    name: 'Default image URL',
                    status: 'passed',
                    message: 'Default image URL is correctly set'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Default image URL',
                    status: 'failed',
                    message: 'Default image URL is incorrect'
                });
                results.failed++;
            }

            // 恢复原始设置
            user.showEffect = originalShowEffect;

        } catch (error) {
            results.tests.push({
                name: 'Rainbow man basic',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    testUserPreferences() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            const mockEnv = {
                services: {
                    notification: this.mockServices.notification
                }
            };

            // 测试效果启用时
            const originalShowEffect = user.showEffect;
            user.showEffect = true;

            const resultEnabled = rainbowMan(mockEnv, { message: 'Test message' });

            if (resultEnabled && resultEnabled.Component) {
                results.tests.push({
                    name: 'Effects enabled behavior',
                    status: 'passed',
                    message: 'Returns component when effects are enabled'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Effects enabled behavior',
                    status: 'failed',
                    message: 'Does not return component when effects are enabled'
                });
                results.failed++;
            }

            // 测试效果禁用时
            user.showEffect = false;

            const resultDisabled = rainbowMan(mockEnv, { message: 'Test message' });

            if (!resultDisabled || !resultDisabled.Component) {
                results.tests.push({
                    name: 'Effects disabled behavior',
                    status: 'passed',
                    message: 'Does not return component when effects are disabled'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Effects disabled behavior',
                    status: 'failed',
                    message: 'Returns component when effects should be disabled'
                });
                results.failed++;
            }

            // 测试通知回退
            if (this.mockServices.notification.add.mock.calls.length > 0) {
                results.tests.push({
                    name: 'Notification fallback',
                    status: 'passed',
                    message: 'Shows notification when effects are disabled'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Notification fallback',
                    status: 'failed',
                    message: 'Does not show notification when effects are disabled'
                });
                results.failed++;
            }

            // 恢复原始设置
            user.showEffect = originalShowEffect;

        } catch (error) {
            results.tests.push({
                name: 'User preferences',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    testEffectExecution() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            // 创建服务实例
            const serviceInstance = effectService.start({}, this.mockServices);

            // 测试add方法存在
            if (serviceInstance && typeof serviceInstance.add === 'function') {
                results.tests.push({
                    name: 'Add method exists',
                    status: 'passed',
                    message: 'Service instance has add method'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Add method exists',
                    status: 'failed',
                    message: 'Service instance does not have add method'
                });
                results.failed++;
            }

            // 模拟用户启用效果
            const originalShowEffect = user.showEffect;
            user.showEffect = true;

            // 测试效果执行
            serviceInstance.add({
                type: 'rainbow_man',
                message: 'Test effect'
            });

            if (this.mockServices.overlay.add.mock.calls.length > 0) {
                results.tests.push({
                    name: 'Effect execution',
                    status: 'passed',
                    message: 'Effect is executed and overlay is called'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Effect execution',
                    status: 'failed',
                    message: 'Effect execution does not call overlay'
                });
                results.failed++;
            }

            // 测试默认效果类型
            serviceInstance.add({});

            if (this.mockServices.overlay.add.mock.calls.length > 1) {
                results.tests.push({
                    name: 'Default effect type',
                    status: 'passed',
                    message: 'Default effect type works correctly'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Default effect type',
                    status: 'failed',
                    message: 'Default effect type does not work'
                });
                results.failed++;
            }

            // 恢复原始设置
            user.showEffect = originalShowEffect;

        } catch (error) {
            results.tests.push({
                name: 'Effect execution',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    testOverlayIntegration() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            const serviceInstance = effectService.start({}, this.mockServices);

            // 模拟用户启用效果
            const originalShowEffect = user.showEffect;
            user.showEffect = true;

            // 执行效果
            serviceInstance.add({
                type: 'rainbow_man',
                message: 'Integration test'
            });

            // 检查overlay.add调用
            const overlayCall = this.mockServices.overlay.add.mock.calls[0];

            if (overlayCall && overlayCall.length >= 2) {
                results.tests.push({
                    name: 'Overlay add called',
                    status: 'passed',
                    message: 'Overlay add method is called with correct parameters'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Overlay add called',
                    status: 'failed',
                    message: 'Overlay add method is not called correctly'
                });
                results.failed++;
            }

            // 检查组件参数
            const [Component, props] = overlayCall || [];

            if (Component && props && typeof props.close === 'function') {
                results.tests.push({
                    name: 'Component and props',
                    status: 'passed',
                    message: 'Component and props are passed correctly'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Component and props',
                    status: 'failed',
                    message: 'Component or props are not passed correctly'
                });
                results.failed++;
            }

            // 测试close函数
            if (props && typeof props.close === 'function') {
                props.close(); // 应该调用remove函数

                results.tests.push({
                    name: 'Close function',
                    status: 'passed',
                    message: 'Close function is properly implemented'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Close function',
                    status: 'failed',
                    message: 'Close function is not properly implemented'
                });
                results.failed++;
            }

            // 恢复原始设置
            user.showEffect = originalShowEffect;

        } catch (error) {
            results.tests.push({
                name: 'Overlay integration',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    runAllTests() {
        const allResults = {};

        for (const [suiteId, suite] of this.testSuites.entries()) {
            console.log(`Running ${suite.name}...`);

            const suiteResults = {
                name: suite.name,
                passed: 0,
                failed: 0,
                tests: []
            };

            for (const test of suite.tests) {
                try {
                    const testResult = test();
                    suiteResults.passed += testResult.passed;
                    suiteResults.failed += testResult.failed;
                    suiteResults.tests.push(...testResult.tests);
                } catch (error) {
                    suiteResults.tests.push({
                        name: 'Test execution',
                        status: 'failed',
                        message: `Test failed to execute: ${error.message}`
                    });
                    suiteResults.failed++;
                }
            }

            allResults[suiteId] = suiteResults;
        }

        return allResults;
    }

    generateTestReport() {
        const results = this.runAllTests();

        const summary = {
            totalSuites: Object.keys(results).length,
            totalTests: 0,
            totalPassed: 0,
            totalFailed: 0,
            suites: {}
        };

        for (const [suiteId, result] of Object.entries(results)) {
            summary.totalTests += result.tests.length;
            summary.totalPassed += result.passed;
            summary.totalFailed += result.failed;
            summary.suites[suiteId] = {
                passed: result.passed,
                failed: result.failed,
                total: result.tests.length
            };
        }

        console.group('🧪 Effect Service Test Report');
        console.log(`Total Test Suites: ${summary.totalSuites}`);
        console.log(`Total Tests: ${summary.totalTests}`);
        console.log(`Passed: ${summary.totalPassed}`);
        console.log(`Failed: ${summary.totalFailed}`);
        console.log(`Success Rate: ${(summary.totalPassed / summary.totalTests * 100).toFixed(1)}%`);

        for (const [suiteId, suite] of Object.entries(results)) {
            console.group(`${suite.name}`);
            console.log(`Passed: ${suite.passed}/${suite.tests.length}`);

            suite.tests.forEach(test => {
                const icon = test.status === 'passed' ? '✅' : '❌';
                console.log(`${icon} ${test.name}: ${test.message}`);
            });

            console.groupEnd();
        }

        console.groupEnd();

        return { summary, details: results };
    }
}

// 使用示例
const testFramework = new EffectServiceTestFramework();
const testReport = testFramework.generateTestReport();
```

## 🔧 调试技巧

### 效果服务状态监控
```javascript
function debugEffectService() {
    const effectService = odoo.__DEBUG__.services.effect;

    console.group('🎭 Effect Service Debug');
    console.log('Service instance:', effectService);

    // 检查效果注册表
    const effectRegistry = registry.category("effects");
    console.log('Registered effects:', effectRegistry.getEntries());

    // 测试效果执行
    console.log('Testing rainbow man effect...');
    effectService.add({
        type: 'rainbow_man',
        message: 'Debug test effect',
        fadeout: 'fast'
    });

    console.groupEnd();
}

// 在控制台中调用
debugEffectService();
```

### 效果执行追踪
```javascript
function traceEffectExecution() {
    const effectService = odoo.__DEBUG__.services.effect;
    const originalAdd = effectService.add;

    effectService.add = function(params = {}) {
        console.log('🎭 Effect triggered:', {
            type: params.type || 'rainbow_man',
            message: params.message,
            timestamp: new Date().toISOString(),
            userShowEffect: user.showEffect
        });

        const startTime = performance.now();
        const result = originalAdd.call(this, params);
        const endTime = performance.now();

        console.log(`🎭 Effect execution time: ${endTime - startTime}ms`);

        return result;
    };

    console.log('Effect execution tracing enabled');
}

// 启用追踪
traceEffectExecution();
```

## 📊 性能考虑

### 优化策略
1. **用户偏好检查**: 优先检查用户偏好避免不必要的组件创建
2. **组件懒加载**: 只在需要时加载效果组件
3. **资源清理**: 确保效果组件正确清理避免内存泄漏
4. **降级处理**: 提供轻量级的通知替代方案

### 最佳实践
```javascript
// ✅ 好的做法：检查用户偏好
if (user.showEffect) {
    return { Component: RainbowMan, props };
}
env.services.notification.add(message);

// ❌ 不好的做法：总是创建组件
return { Component: RainbowMan, props };

// ✅ 好的做法：提供默认值
const props = {
    imgUrl: params.img_url || "/web/static/img/smile.svg",
    fadeout: params.fadeout || "medium",
    message
};

// ❌ 不好的做法：不处理缺失参数
const props = {
    imgUrl: params.img_url,
    fadeout: params.fadeout,
    message
};

// ✅ 好的做法：自清理机制
const remove = overlay.add(Component, {
    ...props,
    close: () => remove(),
});

// ❌ 不好的做法：手动管理清理
const remove = overlay.add(Component, props);
// 忘记提供清理机制
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解视觉效果系统的架构设计和实现
- [ ] 掌握服务注册表模式在效果管理中的应用
- [ ] 理解组件动态加载和覆盖层集成
- [ ] 能够实现用户偏好设置的条件渲染逻辑
- [ ] 掌握企业级用户体验增强的实现方式
- [ ] 了解效果服务的测试和调试技术

## 🚀 下一步学习
学完效果服务后，建议继续学习：
1. **彩虹人组件** (`@web/core/effects/rainbow_man.js`) - 学习具体效果组件实现
2. **覆盖层服务** (`@web/core/overlay/`) - 理解覆盖层系统
3. **通知服务** (`@web/core/notifications/`) - 掌握通知系统

## 💡 重要提示
- 效果服务提供了重要的用户体验增强
- 用户偏好检查确保了个性化体验
- 降级处理保证了功能的可用性
- 注册表模式支持了系统的可扩展性
```

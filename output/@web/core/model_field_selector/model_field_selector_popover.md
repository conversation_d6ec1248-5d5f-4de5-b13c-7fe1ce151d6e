# ModelFieldSelectorPopover - 模型字段选择器弹出框

## 概述

`model_field_selector_popover.js` 是 Odoo Web 核心模块的模型字段选择器弹出框组件，提供了字段选择的弹出界面。该组件支持分页导航、搜索过滤、关联字段跟随、键盘导航等功能，具备调试模式、字段过滤、路径管理等特性，为用户提供了直观便捷的字段选择体验，广泛应用于报表配置、过滤器设置、表达式编辑等需要字段选择的场景。

## 文件信息
- **路径**: `/web/static/src/core/model_field_selector/model_field_selector_popover.js`
- **行数**: 304
- **模块**: `@web/core/model_field_selector/model_field_selector_popover`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                          // OWL框架
'@web/core/utils/timing'             // 时间工具
'@web/core/l10n/translation'         // 国际化
'@web/core/utils/search'             // 搜索工具
'@web/core/utils/concurrency'        // 并发控制
'@web/core/utils/arrays'             // 数组工具
'@web/core/utils/hooks'              // 工具钩子
```

## 核心类

### 1. Page 类

```javascript
class Page {
    constructor(resModel, fieldDefs, options = {}) {
        this.resModel = resModel;
        this.fieldDefs = fieldDefs;
        const { previousPage = null, selectedName = null, isDebugMode } = options;
        this.previousPage = previousPage;
        this.selectedName = selectedName;
        this.isDebugMode = isDebugMode;
        this.sortedFieldNames = sortBy(Object.keys(fieldDefs), (key) => fieldDefs[key].string);
        this.fieldNames = this.sortedFieldNames;
        this.query = "";
        this.focusedFieldName = null;
        this.resetFocusedFieldName();
    }
}
```

**Page类功能**:
- **模型管理**: 管理当前页面的模型和字段定义
- **导航支持**: 支持页面间的前后导航
- **搜索功能**: 提供字段搜索和过滤
- **焦点管理**: 管理当前聚焦的字段

#### 核心属性

```javascript
// 路径计算
get path() {
    const previousPath = this.previousPage?.path || "";
    if (this.selectedName) {
        if (previousPath) {
            return `${previousPath}.${this.selectedName}`;
        } else {
            return this.selectedName;
        }
    }
    return previousPath;
}

// 选中字段
get selectedField() {
    return this.fieldDefs[this.selectedName];
}

// 页面标题
get title() {
    const prefix = this.previousPage?.previousPage ? "... > " : "";
    const title = this.previousPage?.selectedField.string || "";
    if (prefix.length || title.length) {
        return `${prefix}${title}`;
    }
    return _t("Select a field");
}
```

#### 核心方法

```javascript
// 焦点导航
focus(direction) {
    if (!this.fieldNames.length) return;
    
    const index = this.fieldNames.indexOf(this.focusedFieldName);
    if (direction === "previous") {
        if (index === 0) {
            this.focusedFieldName = this.fieldNames[this.fieldNames.length - 1];
        } else {
            this.focusedFieldName = this.fieldNames[index - 1];
        }
    } else {
        if (index === this.fieldNames.length - 1) {
            this.focusedFieldName = this.fieldNames[0];
        } else {
            this.focusedFieldName = this.fieldNames[index + 1];
        }
    }
}

// 搜索字段
searchFields(query = "") {
    this.query = query;
    this.fieldNames = this.sortedFieldNames;
    if (query) {
        this.fieldNames = fuzzyLookup(query, this.fieldNames, (key) => {
            const vals = [this.fieldDefs[key].string];
            if (this.isDebugMode) {
                vals.push(key);
            }
            return vals;
        });
    }
    this.resetFocusedFieldName();
}
```

### 2. ModelFieldSelectorPopover 主组件

```javascript
class ModelFieldSelectorPopover extends Component {
    static props = {
        close: Function,                             // 关闭回调
        filter: { type: Function, optional: true }, // 字段过滤器
        followRelations: { type: Boolean, optional: true }, // 跟随关联
        showDebugInput: { type: Boolean, optional: true }, // 显示调试输入
        isDebugMode: { type: Boolean, optional: true }, // 调试模式
        path: { optional: true },                   // 当前路径
        resModel: String,                           // 资源模型
        showSearchInput: { type: Boolean, optional: true }, // 显示搜索输入
        update: Function,                           // 更新回调
    };

    static defaultProps = {
        filter: (fieldDef) => fieldDef.searchable,
        isDebugMode: false,
        followRelations: true,
    };
}
```

**组件功能**:
- **弹出界面**: 提供字段选择的弹出界面
- **分页导航**: 支持关联字段的分页导航
- **搜索过滤**: 实时搜索和过滤字段
- **键盘导航**: 完整的键盘导航支持

#### 组件初始化

```javascript
setup() {
    this.fieldService = useService("field");
    this.state = useState({ page: null });
    this.keepLast = new KeepLast();
    this.debouncedSearchFields = debounce(this.searchFields.bind(this), 250);

    onWillStart(async () => {
        this.state.page = await this.loadPages(this.props.resModel, this.props.path);
    });

    // 自动滚动到聚焦元素
    const rootRef = useRef("root");
    useEffect(() => {
        const focusedElement = rootRef.el.querySelector(
            ".o_model_field_selector_popover_item.active"
        );
        if (focusedElement) {
            focusedElement.scrollIntoView({ block: "center" });
        }
    });
}
```

## 核心方法

### 1. 页面加载

```javascript
async loadPages(resModel, path) {
    if (typeof path !== "string" || !path.length) {
        const fieldDefs = await this.fieldService.loadFields(resModel);
        return new Page(resModel, this.filter(fieldDefs, path), {
            isDebugMode: this.props.isDebugMode,
        });
    }
    
    const { isInvalid, modelsInfo, names } = await this.fieldService.loadPath(resModel, path);
    switch (isInvalid) {
        case "model":
            throw new Error(`Invalid model name: ${resModel}`);
        case "path": {
            const { resModel, fieldDefs } = modelsInfo[0];
            return new Page(resModel, this.filter(fieldDefs, path), {
                selectedName: path,
                isDebugMode: this.props.isDebugMode,
            });
        }
        default: {
            let page = null;
            for (let index = 0; index < names.length; index++) {
                const name = names[index];
                const { resModel, fieldDefs } = modelsInfo[index];
                page = new Page(resModel, this.filter(fieldDefs, path), {
                    previousPage: page,
                    selectedName: name,
                    isDebugMode: this.props.isDebugMode,
                });
            }
            return page;
        }
    }
}
```

**页面加载功能**:
- **路径解析**: 解析字段路径并构建页面层次
- **错误处理**: 处理无效模型和路径
- **递归构建**: 递归构建页面导航链
- **字段过滤**: 应用字段过滤器

### 2. 关联字段跟随

```javascript
async followRelation(fieldDef) {
    const { modelsInfo } = await this.keepLast.add(
        this.fieldService.loadPath(this.state.page.resModel, `${fieldDef.name}.*`)
    );
    this.state.page.selectedName = fieldDef.name;
    const { resModel, fieldDefs } = modelsInfo.at(-1);
    this.openPage(
        new Page(resModel, this.filter(fieldDefs, this.state.page.path), {
            previousPage: this.state.page,
            isDebugMode: this.props.isDebugMode,
        })
    );
}
```

**关联跟随功能**:
- **动态加载**: 动态加载关联模型的字段
- **页面创建**: 创建新的页面实例
- **导航链**: 维护页面导航链
- **并发控制**: 使用KeepLast防止竞态条件

### 3. 键盘导航

```javascript
async onInputKeydown(ev) {
    const { page } = this.state;
    switch (ev.key) {
        case "ArrowUp": {
            if (ev.target.selectionStart === 0) {
                page.focus("previous");
            }
            break;
        }
        case "ArrowDown": {
            if (ev.target.selectionStart === page.query.length) {
                page.focus("next");
            }
            break;
        }
        case "ArrowLeft": {
            if (ev.target.selectionStart === 0 && page.previousPage) {
                this.goToPreviousPage();
            }
            break;
        }
        case "ArrowRight": {
            if (ev.target.selectionStart === page.query.length) {
                const focusedFieldName = this.state.page.focusedFieldName;
                if (focusedFieldName) {
                    const fieldDef = this.state.page.fieldDefs[focusedFieldName];
                    if (fieldDef.relation || fieldDef.type === "properties") {
                        this.followRelation(fieldDef);
                    }
                }
            }
            break;
        }
        case "Enter": {
            const focusedFieldName = this.state.page.focusedFieldName;
            if (focusedFieldName) {
                const fieldDef = this.state.page.fieldDefs[focusedFieldName];
                this.selectField(fieldDef);
            }
            break;
        }
        case "Escape": {
            this.props.close();
            break;
        }
    }
}
```

**键盘导航功能**:
- **方向键**: 上下箭头键导航字段列表
- **左右导航**: 左右箭头键进行页面导航
- **回车选择**: 回车键选择当前字段
- **ESC退出**: ESC键关闭弹出框

## 使用场景

### 1. 基础字段选择弹出框

```javascript
// 基础字段选择弹出框使用
class FieldSelectorPopoverDemo extends Component {
    setup() {
        this.state = useState({
            isOpen: false,
            selectedPath: '',
            selectedField: null,
            resModel: 'res.partner',
            availableModels: [
                { key: 'res.partner', name: '合作伙伴' },
                { key: 'sale.order', name: '销售订单' },
                { key: 'product.product', name: '产品' }
            ]
        });
    }

    openPopover() {
        this.state.isOpen = true;
    }

    closePopover() {
        this.state.isOpen = false;
    }

    onFieldUpdate(path, fieldInfo) {
        this.state.selectedPath = path;
        this.state.selectedField = fieldInfo;
        console.log('Field updated:', path, fieldInfo);
    }

    onModelChange(newModel) {
        this.state.resModel = newModel;
        this.state.selectedPath = '';
        this.state.selectedField = null;
    }

    customFieldFilter(fieldDef, path) {
        // 自定义字段过滤逻辑
        if (fieldDef.type === 'binary') return false;
        if (fieldDef.name.startsWith('__')) return false;
        return fieldDef.searchable || fieldDef.store;
    }

    render() {
        return xml`
            <div class="field-selector-popover-demo">
                <h5>字段选择器弹出框示例</h5>
                
                <div class="model-selector mb-3">
                    <label class="form-label">选择模型:</label>
                    <select 
                        class="form-select"
                        t-model="state.resModel"
                        t-on-change="(ev) => this.onModelChange(ev.target.value)"
                    >
                        <t t-foreach="state.availableModels" t-as="model" t-key="model.key">
                            <option t-att-value="model.key" t-esc="model.name"/>
                        </t>
                    </select>
                </div>

                <div class="field-selector-trigger mb-3">
                    <button 
                        class="btn btn-outline-primary"
                        t-on-click="openPopover"
                        t-att-disabled="state.isOpen"
                    >
                        <i class="fa fa-list"/> 选择字段
                    </button>
                    
                    <div class="selected-field-display mt-2" t-if="state.selectedPath">
                        <div class="card">
                            <div class="card-body">
                                <h6>已选择字段</h6>
                                <p><strong>路径:</strong> <code t-esc="state.selectedPath"/></p>
                                <p t-if="state.selectedField">
                                    <strong>名称:</strong> <t t-esc="state.selectedField.string"/>
                                </p>
                                <p t-if="state.selectedField">
                                    <strong>类型:</strong> <t t-esc="state.selectedField.type"/>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="popover-container" t-if="state.isOpen">
                    <div class="popover-backdrop" t-on-click="closePopover"/>
                    <div class="popover-content">
                        <ModelFieldSelectorPopover
                            resModel="state.resModel"
                            path="state.selectedPath"
                            update="onFieldUpdate"
                            close="closePopover"
                            filter="customFieldFilter"
                            followRelations="true"
                            showSearchInput="true"
                            isDebugMode="false"
                        />
                    </div>
                </div>

                <div class="instructions mt-3">
                    <div class="card">
                        <div class="card-header">
                            <h6>使用说明</h6>
                        </div>
                        <div class="card-body">
                            <ul>
                                <li>点击"选择字段"按钮打开字段选择器</li>
                                <li>使用搜索框快速查找字段</li>
                                <li>使用方向键导航字段列表</li>
                                <li>点击或按回车选择字段</li>
                                <li>对于关联字段，可以继续深入选择</li>
                                <li>按ESC键关闭选择器</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

### 2. 高级字段路径构建器

```javascript
// 高级字段路径构建器
class AdvancedFieldPathBuilder extends Component {
    setup() {
        this.state = useState({
            paths: [],
            currentPath: {
                id: null,
                resModel: 'res.partner',
                path: '',
                description: '',
                isEditing: false
            },
            isPopoverOpen: false,
            pathHistory: []
        });
    }

    addNewPath() {
        const newPath = {
            id: Date.now(),
            resModel: this.state.currentPath.resModel,
            path: '',
            description: '',
            isEditing: true
        };
        this.state.currentPath = newPath;
        this.state.isPopoverOpen = true;
    }

    editPath(pathId) {
        const path = this.state.paths.find(p => p.id === pathId);
        if (path) {
            this.state.currentPath = { ...path, isEditing: true };
            this.state.isPopoverOpen = true;
        }
    }

    savePath() {
        if (!this.state.currentPath.path) {
            this.notification.add('请选择字段路径', { type: 'warning' });
            return;
        }

        const existingIndex = this.state.paths.findIndex(p => p.id === this.state.currentPath.id);
        if (existingIndex !== -1) {
            this.state.paths[existingIndex] = { ...this.state.currentPath, isEditing: false };
        } else {
            this.state.paths.push({ ...this.state.currentPath, isEditing: false });
        }

        this.resetCurrentPath();
        this.state.isPopoverOpen = false;
        this.notification.add('路径已保存', { type: 'success' });
    }

    deletePath(pathId) {
        if (!confirm('确定要删除这个路径吗？')) return;
        
        this.state.paths = this.state.paths.filter(p => p.id !== pathId);
        this.notification.add('路径已删除', { type: 'info' });
    }

    resetCurrentPath() {
        this.state.currentPath = {
            id: null,
            resModel: 'res.partner',
            path: '',
            description: '',
            isEditing: false
        };
    }

    onFieldUpdate(path, fieldInfo) {
        this.state.currentPath.path = path;
        
        // 记录路径历史
        this.state.pathHistory.unshift({
            timestamp: new Date().toLocaleTimeString(),
            path: path,
            fieldName: fieldInfo?.string || '未知字段'
        });

        // 限制历史记录数量
        if (this.state.pathHistory.length > 10) {
            this.state.pathHistory.pop();
        }
    }

    closePopover() {
        this.state.isPopoverOpen = false;
        if (!this.state.currentPath.path) {
            this.resetCurrentPath();
        }
    }

    exportPaths() {
        const data = JSON.stringify(this.state.paths, null, 2);
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'field_paths.json';
        a.click();
        URL.revokeObjectURL(url);
    }

    render() {
        return xml`
            <div class="advanced-field-path-builder">
                <div class="header mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>高级字段路径构建器</h5>
                        <div class="header-actions">
                            <button 
                                class="btn btn-primary me-2"
                                t-on-click="addNewPath"
                            >
                                <i class="fa fa-plus"/> 添加路径
                            </button>
                            <button 
                                class="btn btn-outline-secondary"
                                t-on-click="exportPaths"
                                t-att-disabled="!state.paths.length"
                            >
                                <i class="fa fa-download"/> 导出
                            </button>
                        </div>
                    </div>
                </div>

                <div class="paths-list">
                    <div class="table-responsive" t-if="state.paths.length">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>模型</th>
                                    <th>字段路径</th>
                                    <th>描述</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-foreach="state.paths" t-as="path" t-key="path.id">
                                    <tr>
                                        <td t-esc="path.resModel"/>
                                        <td><code t-esc="path.path"/></td>
                                        <td t-esc="path.description || '无描述'"/>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button 
                                                    class="btn btn-outline-primary"
                                                    t-on-click="() => this.editPath(path.id)"
                                                >
                                                    编辑
                                                </button>
                                                <button 
                                                    class="btn btn-outline-danger"
                                                    t-on-click="() => this.deletePath(path.id)"
                                                >
                                                    删除
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </t>
                            </tbody>
                        </table>
                    </div>

                    <div class="empty-state text-center py-5" t-if="!state.paths.length">
                        <i class="fa fa-sitemap fa-3x text-muted mb-3"/>
                        <h6 class="text-muted">暂无字段路径</h6>
                        <p class="text-muted">点击"添加路径"按钮创建第一个字段路径</p>
                    </div>
                </div>

                <div class="popover-modal" t-if="state.isPopoverOpen">
                    <div class="modal-backdrop" t-on-click="closePopover"/>
                    <div class="modal-content">
                        <div class="modal-header">
                            <h6>
                                <t t-if="state.currentPath.isEditing and state.currentPath.id">编辑字段路径</t>
                                <t t-else="">添加字段路径</t>
                            </h6>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">模型</label>
                                <select class="form-select" t-model="state.currentPath.resModel">
                                    <option value="res.partner">合作伙伴</option>
                                    <option value="sale.order">销售订单</option>
                                    <option value="product.product">产品</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">描述</label>
                                <input 
                                    type="text" 
                                    class="form-control"
                                    t-model="state.currentPath.description"
                                    placeholder="路径描述（可选）"
                                />
                            </div>

                            <div class="field-selector-container">
                                <ModelFieldSelectorPopover
                                    resModel="state.currentPath.resModel"
                                    path="state.currentPath.path"
                                    update="onFieldUpdate"
                                    close="() => {}"
                                    followRelations="true"
                                    showSearchInput="true"
                                    isDebugMode="true"
                                />
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button 
                                class="btn btn-primary me-2"
                                t-on-click="savePath"
                                t-att-disabled="!state.currentPath.path"
                            >
                                保存
                            </button>
                            <button 
                                class="btn btn-secondary"
                                t-on-click="closePopover"
                            >
                                取消
                            </button>
                        </div>
                    </div>
                </div>

                <div class="path-history mt-4" t-if="state.pathHistory.length">
                    <h6>路径选择历史</h6>
                    <div class="history-list">
                        <t t-foreach="state.pathHistory" t-as="record" t-key="record_index">
                            <div class="history-item border-bottom py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <code t-esc="record.path"/>
                                        <span class="text-muted ms-2">(<t t-esc="record.fieldName"/>)</span>
                                    </div>
                                    <small class="text-muted" t-esc="record.timestamp"/>
                                </div>
                            </div>
                        </t>
                    </div>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 分页导航
- 支持关联字段的深度导航
- 面包屑式的页面标题
- 前后页面的无缝切换

### 2. 智能搜索
- 实时的模糊搜索
- 多字段匹配（名称和技术名称）
- 防抖优化的搜索性能

### 3. 键盘友好
- 完整的键盘导航支持
- 智能的光标位置检测
- 直观的键盘操作逻辑

### 4. 并发安全
- 使用KeepLast防止竞态条件
- 异步操作的安全处理
- 状态更新的一致性

## 设计模式

### 1. 页面模式 (Page Pattern)
- 分页式的内容组织
- 页面间的导航管理

### 2. 状态模式 (State Pattern)
- 不同状态下的行为管理
- 状态转换的控制

### 3. 观察者模式 (Observer Pattern)
- 搜索状态的响应式更新
- 焦点变化的通知机制

## 注意事项

1. **性能优化**: 使用防抖优化搜索性能
2. **内存管理**: 正确清理异步操作和事件监听器
3. **用户体验**: 提供清晰的导航反馈
4. **错误处理**: 处理字段加载失败的情况

## 扩展建议

1. **字段预览**: 字段值的实时预览
2. **收藏功能**: 常用字段路径的收藏
3. **分组显示**: 按字段类型分组显示
4. **批量操作**: 支持多字段的批量选择
5. **自定义过滤**: 更灵活的字段过滤规则

该模型字段选择器弹出框为Odoo Web应用提供了强大的字段选择界面，通过分页导航和智能搜索确保了良好的用户体验和开发者友好性。

# ModelFieldSelector Utils - 模型字段选择器工具函数

## 概述

`utils.js` 是 Odoo Web 核心模块的模型字段选择器工具函数集合，提供了字段信息加载和路径描述生成的实用工具。该模块包含字段信息获取、路径验证、显示名称生成等功能，具备错误处理、类型检查、特殊字段过滤等特性，为模型字段选择器组件提供了核心的数据处理能力，广泛应用于字段选择、路径解析、数据验证等场景。

## 文件信息
- **路径**: `/web/static/src/core/model_field_selector/utils.js`
- **行数**: 58
- **模块**: `@web/core/model_field_selector/utils`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/hooks'              // 工具钩子
```

## 工具函数

### 1. makeString 函数

```javascript
function makeString(value) {
    return String(value ?? "-");
}
```

**功能说明**:
- **空值处理**: 将null或undefined转换为"-"
- **字符串转换**: 确保返回值为字符串类型
- **默认显示**: 提供统一的空值显示格式

## 核心钩子函数

### 1. useLoadFieldInfo

```javascript
function useLoadFieldInfo(fieldService) {
    fieldService ||= useService("field");
    return async (resModel, path) => {
        if (typeof path !== "string" || !path) {
            return { resModel, fieldDef: null };
        }
        const { isInvalid, names, modelsInfo } = await fieldService.loadPath(resModel, path);
        if (isInvalid) {
            return { resModel, fieldDef: null };
        }
        const name = names.at(-1);
        const modelInfo = modelsInfo.at(-1);
        return { resModel: modelInfo.resModel, fieldDef: modelInfo.fieldDefs[name] };
    };
}
```

**钩子功能**:
- **字段信息加载**: 根据模型和路径加载字段定义信息
- **路径解析**: 解析字段路径并获取最终字段信息
- **错误处理**: 处理无效路径和加载失败的情况
- **服务集成**: 集成字段服务进行数据获取

**参数说明**:
- **fieldService**: 可选的字段服务实例，默认使用系统服务
- **返回函数**: 异步函数，接受resModel和path参数

**返回值**:
```javascript
{
    resModel: String,    // 目标模型名称
    fieldDef: Object     // 字段定义对象，失败时为null
}
```

### 2. useLoadPathDescription

```javascript
function useLoadPathDescription(fieldService) {
    fieldService ||= useService("field");
    return async (resModel, path, allowEmpty) => {
        // 处理数值路径
        if ([0, 1].includes(path)) {
            return { isInvalid: false, displayNames: [makeString(path)] };
        }
        
        // 处理空路径
        if (allowEmpty && !path) {
            return { isInvalid: false, displayNames: [] };
        }
        
        // 验证路径类型
        if (typeof path !== "string" || !path) {
            return { isInvalid: true, displayNames: [makeString()] };
        }
        
        const { isInvalid, modelsInfo, names } = await fieldService.loadPath(resModel, path);
        const result = { isInvalid: !!isInvalid, displayNames: [] };
        
        if (!isInvalid) {
            const lastName = names.at(-1);
            const lastFieldDef = modelsInfo.at(-1).fieldDefs[lastName];
            // 过滤properties类型字段
            if (["properties", "properties_definition"].includes(lastFieldDef.type)) {
                result.isInvalid = true;
            }
        }
        
        // 生成显示名称
        for (let index = 0; index < names.length; index++) {
            const name = names[index];
            const fieldDef = modelsInfo[index]?.fieldDefs[name];
            result.displayNames.push(fieldDef?.string || makeString(name));
        }
        
        return result;
    };
}
```

**钩子功能**:
- **路径描述生成**: 生成字段路径的可读描述
- **特殊值处理**: 处理数值路径(0, 1)和空路径
- **字段过滤**: 过滤不支持的字段类型
- **显示名称**: 生成用户友好的显示名称

**参数说明**:
- **fieldService**: 可选的字段服务实例
- **返回函数**: 异步函数，接受resModel、path、allowEmpty参数

**返回值**:
```javascript
{
    isInvalid: Boolean,     // 路径是否无效
    displayNames: Array     // 显示名称数组
}
```

## 使用场景

### 1. 字段信息获取

```javascript
// 字段信息获取示例
class FieldInfoLoader extends Component {
    setup() {
        this.loadFieldInfo = useLoadFieldInfo();
        this.state = useState({
            resModel: 'res.partner',
            path: 'name',
            fieldInfo: null,
            isLoading: false,
            error: null
        });
    }

    async loadField() {
        if (!this.state.path) {
            this.state.error = '请输入字段路径';
            return;
        }

        this.state.isLoading = true;
        this.state.error = null;

        try {
            const fieldInfo = await this.loadFieldInfo(this.state.resModel, this.state.path);
            this.state.fieldInfo = fieldInfo;
            
            if (!fieldInfo.fieldDef) {
                this.state.error = '字段不存在或路径无效';
            }
        } catch (error) {
            this.state.error = `加载失败: ${error.message}`;
            console.error('Field loading error:', error);
        } finally {
            this.state.isLoading = false;
        }
    }

    onPathChange(ev) {
        this.state.path = ev.target.value;
        this.state.fieldInfo = null;
        this.state.error = null;
    }

    onModelChange(ev) {
        this.state.resModel = ev.target.value;
        this.state.fieldInfo = null;
        this.state.error = null;
    }

    render() {
        return xml`
            <div class="field-info-loader">
                <h5>字段信息加载器</h5>
                
                <div class="controls mb-3">
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <label class="form-label">模型</label>
                            <select 
                                class="form-select"
                                t-model="state.resModel"
                                t-on-change="onModelChange"
                            >
                                <option value="res.partner">合作伙伴</option>
                                <option value="sale.order">销售订单</option>
                                <option value="product.product">产品</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-2">
                            <label class="form-label">字段路径</label>
                            <input 
                                type="text" 
                                class="form-control"
                                t-model="state.path"
                                t-on-input="onPathChange"
                                placeholder="例如: name, category_id.name"
                            />
                        </div>
                    </div>
                    <button 
                        class="btn btn-primary"
                        t-on-click="loadField"
                        t-att-disabled="state.isLoading || !state.path"
                    >
                        <i t-att-class="state.isLoading ? 'fa fa-spinner fa-spin' : 'fa fa-search'"/>
                        <t t-if="state.isLoading">加载中...</t>
                        <t t-else="">加载字段信息</t>
                    </button>
                </div>

                <div class="error-message mb-3" t-if="state.error">
                    <div class="alert alert-danger">
                        <i class="fa fa-exclamation-triangle"/> <t t-esc="state.error"/>
                    </div>
                </div>

                <div class="field-info" t-if="state.fieldInfo and state.fieldInfo.fieldDef">
                    <div class="card">
                        <div class="card-header">
                            <h6>字段信息</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>字段名:</strong> <code t-esc="state.fieldInfo.fieldDef.name"/></p>
                                    <p><strong>显示名:</strong> <t t-esc="state.fieldInfo.fieldDef.string"/></p>
                                    <p><strong>字段类型:</strong> <span class="badge bg-secondary" t-esc="state.fieldInfo.fieldDef.type"/></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>目标模型:</strong> <code t-esc="state.fieldInfo.resModel"/></p>
                                    <p><strong>是否必填:</strong> 
                                        <span t-att-class="state.fieldInfo.fieldDef.required ? 'text-danger' : 'text-muted'">
                                            <t t-esc="state.fieldInfo.fieldDef.required ? '是' : '否'"/>
                                        </span>
                                    </p>
                                    <p><strong>是否可搜索:</strong> 
                                        <span t-att-class="state.fieldInfo.fieldDef.searchable ? 'text-success' : 'text-muted'">
                                            <t t-esc="state.fieldInfo.fieldDef.searchable ? '是' : '否'"/>
                                        </span>
                                    </p>
                                </div>
                            </div>
                            
                            <div class="field-details mt-3" t-if="state.fieldInfo.fieldDef.relation">
                                <h6>关联信息</h6>
                                <p><strong>关联模型:</strong> <code t-esc="state.fieldInfo.fieldDef.relation"/></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

### 2. 路径描述生成器

```javascript
// 路径描述生成器
class PathDescriptionGenerator extends Component {
    setup() {
        this.loadPathDescription = useLoadPathDescription();
        this.state = useState({
            resModel: 'res.partner',
            paths: [
                'name',
                'category_id.name',
                'country_id.code',
                'parent_id.name',
                'user_id.login'
            ],
            customPath: '',
            descriptions: new Map(),
            isLoading: false,
            allowEmpty: false
        });

        this.loadAllDescriptions();
    }

    async loadAllDescriptions() {
        this.state.isLoading = true;
        
        for (const path of this.state.paths) {
            try {
                const description = await this.loadPathDescription(
                    this.state.resModel, 
                    path, 
                    this.state.allowEmpty
                );
                this.state.descriptions.set(path, description);
            } catch (error) {
                console.error(`Failed to load description for ${path}:`, error);
                this.state.descriptions.set(path, {
                    isInvalid: true,
                    displayNames: ['加载失败']
                });
            }
        }
        
        this.state.isLoading = false;
    }

    async loadCustomPath() {
        if (!this.state.customPath) return;

        this.state.isLoading = true;
        
        try {
            const description = await this.loadPathDescription(
                this.state.resModel,
                this.state.customPath,
                this.state.allowEmpty
            );
            this.state.descriptions.set(this.state.customPath, description);
            
            if (!this.state.paths.includes(this.state.customPath)) {
                this.state.paths.push(this.state.customPath);
            }
        } catch (error) {
            console.error('Failed to load custom path:', error);
        } finally {
            this.state.isLoading = false;
        }
    }

    async onModelChange(newModel) {
        this.state.resModel = newModel;
        this.state.descriptions.clear();
        await this.loadAllDescriptions();
    }

    getDescriptionText(path) {
        const desc = this.state.descriptions.get(path);
        if (!desc) return '未加载';
        if (desc.isInvalid) return '无效路径';
        return desc.displayNames.join(' → ') || '空路径';
    }

    getDescriptionClass(path) {
        const desc = this.state.descriptions.get(path);
        if (!desc) return 'text-muted';
        if (desc.isInvalid) return 'text-danger';
        return 'text-success';
    }

    render() {
        return xml`
            <div class="path-description-generator">
                <h5>路径描述生成器</h5>
                
                <div class="controls mb-4">
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <label class="form-label">模型</label>
                            <select 
                                class="form-select"
                                t-model="state.resModel"
                                t-on-change="(ev) => this.onModelChange(ev.target.value)"
                            >
                                <option value="res.partner">合作伙伴</option>
                                <option value="sale.order">销售订单</option>
                                <option value="product.product">产品</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-2">
                            <label class="form-label">自定义路径</label>
                            <input 
                                type="text" 
                                class="form-control"
                                t-model="state.customPath"
                                placeholder="输入字段路径"
                            />
                        </div>
                        <div class="col-md-2 mb-2">
                            <label class="form-label">操作</label>
                            <button 
                                class="btn btn-primary w-100"
                                t-on-click="loadCustomPath"
                                t-att-disabled="!state.customPath || state.isLoading"
                            >
                                加载
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-check">
                        <input 
                            class="form-check-input" 
                            type="checkbox" 
                            t-model="state.allowEmpty"
                            t-on-change="loadAllDescriptions"
                        />
                        <label class="form-check-label">允许空路径</label>
                    </div>
                </div>

                <div class="loading-indicator mb-3" t-if="state.isLoading">
                    <div class="alert alert-info">
                        <i class="fa fa-spinner fa-spin"/> 加载路径描述中...
                    </div>
                </div>

                <div class="paths-table">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>字段路径</th>
                                    <th>描述</th>
                                    <th>状态</th>
                                    <th>显示名称数量</th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-foreach="state.paths" t-as="path" t-key="path">
                                    <tr>
                                        <td><code t-esc="path"/></td>
                                        <td t-att-class="getDescriptionClass(path)">
                                            <t t-esc="getDescriptionText(path)"/>
                                        </td>
                                        <td>
                                            <t t-set="desc" t-value="state.descriptions.get(path)"/>
                                            <span t-if="!desc" class="badge bg-secondary">未加载</span>
                                            <span t-elif="desc.isInvalid" class="badge bg-danger">无效</span>
                                            <span t-else="" class="badge bg-success">有效</span>
                                        </td>
                                        <td>
                                            <t t-set="desc" t-value="state.descriptions.get(path)"/>
                                            <t t-if="desc" t-esc="desc.displayNames.length"/>
                                            <t t-else="">-</t>
                                        </td>
                                    </tr>
                                </t>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="usage-examples mt-4">
                    <div class="card">
                        <div class="card-header">
                            <h6>使用示例</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>简单字段</h6>
                                    <ul class="list-unstyled">
                                        <li><code>name</code> → 名称</li>
                                        <li><code>email</code> → 邮箱</li>
                                        <li><code>phone</code> → 电话</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>关联字段</h6>
                                    <ul class="list-unstyled">
                                        <li><code>category_id.name</code> → 分类 → 名称</li>
                                        <li><code>country_id.code</code> → 国家 → 代码</li>
                                        <li><code>parent_id.name</code> → 上级 → 名称</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 错误处理
- 完善的参数验证
- 优雅的错误降级
- 统一的错误返回格式

### 2. 类型安全
- 严格的类型检查
- 空值安全处理
- 默认值提供

### 3. 服务集成
- 灵活的服务注入
- 默认服务使用
- 异步操作支持

### 4. 特殊处理
- Properties字段过滤
- 数值路径支持
- 空路径处理

## 设计模式

### 1. 工厂模式 (Factory Pattern)
- 钩子函数的工厂创建
- 服务实例的工厂提供

### 2. 适配器模式 (Adapter Pattern)
- 字段服务的适配封装
- 数据格式的转换适配

### 3. 策略模式 (Strategy Pattern)
- 不同路径类型的处理策略
- 错误处理的策略选择

## 注意事项

1. **异步处理**: 所有函数都是异步的，需要正确处理Promise
2. **错误处理**: 需要处理字段服务加载失败的情况
3. **类型验证**: 严格验证输入参数的类型和有效性
4. **性能考虑**: 避免重复加载相同的字段信息

## 扩展建议

1. **缓存机制**: 添加字段信息的缓存机制
2. **批量加载**: 支持批量加载多个字段信息
3. **类型推断**: 增强字段类型的推断能力
4. **验证规则**: 添加更多的字段验证规则
5. **国际化**: 支持多语言的字段描述

该模型字段选择器工具函数为Odoo Web应用提供了可靠的字段信息处理能力，通过完善的错误处理和类型安全确保了数据的准确性和系统的稳定性。

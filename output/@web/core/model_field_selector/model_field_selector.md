# ModelFieldSelector - 模型字段选择器

## 概述

`model_field_selector.js` 是 Odoo Web 核心模块的模型字段选择器组件，提供了可视化的字段选择功能。该组件支持字段路径选择、关联字段导航、字段过滤、搜索功能等，具备弹出框选择、只读模式、调试模式等特性，为用户提供了直观的字段选择体验，广泛应用于报表配置、过滤器设置、表达式编辑等需要字段选择的场景。

## 文件信息
- **路径**: `/web/static/src/core/model_field_selector/model_field_selector.js`
- **行数**: 97
- **模块**: `@web/core/model_field_selector/model_field_selector`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                      // OWL框架
'@web/core/utils/concurrency'                   // 并发工具
'@web/core/model_field_selector/model_field_selector_popover' // 字段选择器弹出框
'@web/core/model_field_selector/utils'          // 字段选择器工具
'@web/core/popover/popover_hook'                 // 弹出框钩子
```

## 主组件类

### 1. 组件属性定义

```javascript
static props = {
    resModel: String,                            // 资源模型
    path: { optional: true },                   // 字段路径
    allowEmpty: { type: Boolean, optional: true }, // 允许空值
    readonly: { type: Boolean, optional: true }, // 只读模式
    showSearchInput: { type: Boolean, optional: true }, // 显示搜索输入
    isDebugMode: { type: Boolean, optional: true }, // 调试模式
    update: { type: Function, optional: true },  // 更新回调
    filter: { type: Function, optional: true },  // 字段过滤器
    followRelations: { type: Boolean, optional: true }, // 跟随关联
    showDebugInput: { type: Boolean, optional: true }, // 显示调试输入
};

static defaultProps = {
    readonly: true,
    allowEmpty: false,
    isDebugMode: false,
    showSearchInput: true,
    update: () => {},
    followRelations: true,
};
```

**属性功能**:
- **模型绑定**: resModel指定目标数据模型
- **路径管理**: path表示当前选择的字段路径
- **交互控制**: readonly控制是否可编辑
- **搜索功能**: showSearchInput控制搜索框显示
- **关联导航**: followRelations控制是否可导航关联字段
- **过滤机制**: filter函数过滤可选字段

### 2. 组件初始化

```javascript
setup() {
    this.loadPathDescription = useLoadPathDescription();
    const loadFieldInfo = useLoadFieldInfo();
    this.popover = usePopover(this.constructor.components.Popover, {
        popoverClass: "o_popover_field_selector",
        onClose: async () => {
            if (this.newPath !== null) {
                const fieldInfo = await loadFieldInfo(this.props.resModel, this.newPath);
                this.props.update(this.newPath, fieldInfo);
            }
        },
    });
    this.keepLast = new KeepLast();
    this.state = useState({ isInvalid: false, displayNames: [] });
    onWillStart(() => this.updateState(this.props));
    onWillUpdateProps((nextProps) => this.updateState(nextProps));
}
```

**初始化功能**:
- **工具加载**: 加载路径描述和字段信息工具
- **弹出框配置**: 配置字段选择器弹出框
- **并发控制**: 使用KeepLast防止并发问题
- **状态管理**: 管理组件的显示状态
- **生命周期**: 监听属性变化更新状态

## 核心方法

### 1. 弹出框打开

```javascript
openPopover(currentTarget) {
    if (this.props.readonly) {
        return;
    }
    this.newPath = null;
    this.popover.open(currentTarget, {
        resModel: this.props.resModel,
        path: this.props.path,
        update: (path, _fieldInfo, debug = false) => {
            this.newPath = path;
            if (!debug) {
                this.updateState({ ...this.props, path }, true);
            }
        },
        showSearchInput: this.props.showSearchInput,
        isDebugMode: this.props.isDebugMode,
        filter: this.props.filter,
        followRelations: this.props.followRelations,
        showDebugInput: this.props.showDebugInput,
    });
}
```

**弹出框功能**:
- **只读检查**: 只读模式下不打开弹出框
- **路径重置**: 重置新路径状态
- **属性传递**: 传递所有必要属性给弹出框
- **更新回调**: 处理字段选择的更新
- **调试支持**: 支持调试模式的特殊处理

### 2. 状态更新

```javascript
async updateState(params, isConcurrent) {
    const { resModel, path, allowEmpty } = params;
    let prom = this.loadPathDescription(resModel, path, allowEmpty);
    if (isConcurrent) {
        prom = this.keepLast.add(prom);
    }
    const state = await prom;
    Object.assign(this.state, state);
}
```

**状态更新功能**:
- **路径描述**: 加载字段路径的描述信息
- **并发控制**: 使用KeepLast处理并发请求
- **状态合并**: 将新状态合并到组件状态
- **异步处理**: 异步加载字段信息

### 3. 清除选择

```javascript
clear() {
    if (this.popover.isOpen) {
        this.newPath = "";
        this.popover.close();
        return;
    }
    this.props.update("", { resModel: this.props.resModel, fieldDef: null });
}
```

**清除功能**:
- **弹出框处理**: 如果弹出框打开则关闭并清除
- **直接清除**: 直接清除选择的字段路径
- **回调通知**: 通知父组件字段已清除

## 使用场景

### 1. 基础字段选择器

```javascript
// 基础字段选择器使用
class BasicFieldSelector extends Component {
    setup() {
        this.state = useState({
            selectedField: '',
            fieldInfo: null,
            resModel: 'res.partner',
            availableModels: [
                { key: 'res.partner', name: '合作伙伴' },
                { key: 'sale.order', name: '销售订单' },
                { key: 'product.product', name: '产品' }
            ]
        });
    }

    onFieldUpdate(path, fieldInfo) {
        this.state.selectedField = path;
        this.state.fieldInfo = fieldInfo;
        console.log('Field selected:', path, fieldInfo);
    }

    onModelChange(newModel) {
        this.state.resModel = newModel;
        this.state.selectedField = '';
        this.state.fieldInfo = null;
    }

    getFieldTypeIcon(fieldType) {
        const icons = {
            'char': 'fa-font',
            'text': 'fa-align-left',
            'integer': 'fa-hashtag',
            'float': 'fa-calculator',
            'boolean': 'fa-check-square',
            'date': 'fa-calendar',
            'datetime': 'fa-clock-o',
            'many2one': 'fa-link',
            'many2many': 'fa-sitemap',
            'one2many': 'fa-list'
        };
        return icons[fieldType] || 'fa-question';
    }

    render() {
        return xml`
            <div class="basic-field-selector">
                <h5>基础字段选择器</h5>
                
                <div class="model-selector mb-3">
                    <label class="form-label">选择模型:</label>
                    <select 
                        class="form-select"
                        t-model="state.resModel"
                        t-on-change="(ev) => this.onModelChange(ev.target.value)"
                    >
                        <t t-foreach="state.availableModels" t-as="model" t-key="model.key">
                            <option t-att-value="model.key" t-esc="model.name"/>
                        </t>
                    </select>
                </div>

                <div class="field-selector-container mb-3">
                    <label class="form-label">选择字段:</label>
                    <ModelFieldSelector
                        resModel="state.resModel"
                        path="state.selectedField"
                        readonly="false"
                        allowEmpty="true"
                        showSearchInput="true"
                        followRelations="true"
                        update="onFieldUpdate"
                    />
                </div>

                <div class="field-info" t-if="state.fieldInfo">
                    <div class="card">
                        <div class="card-header">
                            <h6>字段信息</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>字段路径:</strong> <code t-esc="state.selectedField"/></p>
                                    <p><strong>字段名称:</strong> <t t-esc="state.fieldInfo.fieldDef?.string || '未知'"/></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>字段类型:</strong> 
                                        <i t-att-class="'fa ' + getFieldTypeIcon(state.fieldInfo.fieldDef?.type)"/>
                                        <t t-esc="state.fieldInfo.fieldDef?.type || '未知'"/>
                                    </p>
                                    <p><strong>模型:</strong> <t t-esc="state.fieldInfo.resModel"/></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="empty-state text-center py-4" t-if="!state.selectedField">
                    <i class="fa fa-mouse-pointer fa-3x text-muted mb-3"/>
                    <h6 class="text-muted">请选择一个字段</h6>
                    <p class="text-muted">点击上方的字段选择器来选择字段</p>
                </div>
            </div>
        `;
    }
}
```

### 2. 报表字段配置器

```javascript
// 报表字段配置器
class ReportFieldConfigurator extends Component {
    setup() {
        this.state = useState({
            reportConfig: {
                name: '销售报表',
                resModel: 'sale.order',
                fields: []
            },
            currentField: {
                path: '',
                label: '',
                type: 'field'
            },
            isEditing: false,
            editingIndex: -1
        });
    }

    addField() {
        if (!this.state.currentField.path) {
            this.notification.add('请选择字段', { type: 'warning' });
            return;
        }

        const newField = {
            id: Date.now(),
            path: this.state.currentField.path,
            label: this.state.currentField.label || this.state.currentField.path,
            type: this.state.currentField.type
        };

        if (this.state.isEditing) {
            this.state.reportConfig.fields[this.state.editingIndex] = newField;
            this.state.isEditing = false;
            this.state.editingIndex = -1;
        } else {
            this.state.reportConfig.fields.push(newField);
        }

        this.resetCurrentField();
        this.notification.add('字段配置已保存', { type: 'success' });
    }

    editField(index) {
        const field = this.state.reportConfig.fields[index];
        this.state.currentField = { ...field };
        this.state.isEditing = true;
        this.state.editingIndex = index;
    }

    removeField(index) {
        if (!confirm('确定要删除这个字段吗？')) return;
        
        this.state.reportConfig.fields.splice(index, 1);
        if (this.state.editingIndex === index) {
            this.cancelEdit();
        }
    }

    cancelEdit() {
        this.state.isEditing = false;
        this.state.editingIndex = -1;
        this.resetCurrentField();
    }

    resetCurrentField() {
        this.state.currentField = {
            path: '',
            label: '',
            type: 'field'
        };
    }

    onFieldUpdate(path, fieldInfo) {
        this.state.currentField.path = path;
        if (fieldInfo && fieldInfo.fieldDef) {
            this.state.currentField.label = fieldInfo.fieldDef.string || path;
        }
    }

    moveField(index, direction) {
        const fields = this.state.reportConfig.fields;
        const newIndex = index + direction;
        
        if (newIndex < 0 || newIndex >= fields.length) return;
        
        [fields[index], fields[newIndex]] = [fields[newIndex], fields[index]];
    }

    exportConfig() {
        const config = JSON.stringify(this.state.reportConfig, null, 2);
        console.log('Report configuration:', config);
        
        const blob = new Blob([config], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${this.state.reportConfig.name.replace(/\s+/g, '_')}_config.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    render() {
        return xml`
            <div class="report-field-configurator">
                <div class="header mb-4">
                    <h5>报表字段配置器</h5>
                    <div class="report-info">
                        <input 
                            type="text" 
                            class="form-control"
                            placeholder="报表名称"
                            t-model="state.reportConfig.name"
                        />
                    </div>
                </div>

                <div class="field-configuration">
                    <div class="card">
                        <div class="card-header">
                            <h6>
                                <t t-if="state.isEditing">编辑字段</t>
                                <t t-else="">添加字段</t>
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">字段选择</label>
                                    <ModelFieldSelector
                                        resModel="state.reportConfig.resModel"
                                        path="state.currentField.path"
                                        readonly="false"
                                        allowEmpty="true"
                                        showSearchInput="true"
                                        followRelations="true"
                                        update="onFieldUpdate"
                                    />
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">显示标签</label>
                                    <input 
                                        type="text" 
                                        class="form-control"
                                        t-model="state.currentField.label"
                                        placeholder="字段显示名称"
                                    />
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label class="form-label">类型</label>
                                    <select class="form-select" t-model="state.currentField.type">
                                        <option value="field">字段</option>
                                        <option value="computed">计算</option>
                                        <option value="aggregated">聚合</option>
                                    </select>
                                </div>
                            </div>
                            <div class="field-actions">
                                <button 
                                    class="btn btn-primary me-2"
                                    t-on-click="addField"
                                    t-att-disabled="!state.currentField.path"
                                >
                                    <t t-if="state.isEditing">更新字段</t>
                                    <t t-else="">添加字段</t>
                                </button>
                                <button 
                                    class="btn btn-secondary"
                                    t-on-click="cancelEdit"
                                    t-if="state.isEditing"
                                >
                                    取消
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="fields-list mt-4">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6>配置的字段 (${this.state.reportConfig.fields.length})</h6>
                        <button 
                            class="btn btn-outline-primary"
                            t-on-click="exportConfig"
                            t-att-disabled="!state.reportConfig.fields.length"
                        >
                            <i class="fa fa-download"/> 导出配置
                        </button>
                    </div>

                    <div class="fields-table" t-if="state.reportConfig.fields.length">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>顺序</th>
                                        <th>字段路径</th>
                                        <th>显示标签</th>
                                        <th>类型</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-foreach="state.reportConfig.fields" t-as="field" t-key="field.id">
                                        <tr t-att-class="state.editingIndex === field_index ? 'table-warning' : ''">
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button 
                                                        class="btn btn-outline-secondary"
                                                        t-on-click="() => this.moveField(field_index, -1)"
                                                        t-att-disabled="field_index === 0"
                                                    >
                                                        <i class="fa fa-arrow-up"/>
                                                    </button>
                                                    <button 
                                                        class="btn btn-outline-secondary"
                                                        t-on-click="() => this.moveField(field_index, 1)"
                                                        t-att-disabled="field_index === state.reportConfig.fields.length - 1"
                                                    >
                                                        <i class="fa fa-arrow-down"/>
                                                    </button>
                                                </div>
                                            </td>
                                            <td><code t-esc="field.path"/></td>
                                            <td t-esc="field.label"/>
                                            <td>
                                                <span class="badge bg-secondary" t-esc="field.type"/>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button 
                                                        class="btn btn-outline-primary"
                                                        t-on-click="() => this.editField(field_index)"
                                                    >
                                                        编辑
                                                    </button>
                                                    <button 
                                                        class="btn btn-outline-danger"
                                                        t-on-click="() => this.removeField(field_index)"
                                                    >
                                                        删除
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </t>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="empty-state text-center py-5" t-if="!state.reportConfig.fields.length">
                        <i class="fa fa-table fa-3x text-muted mb-3"/>
                        <h6 class="text-muted">暂无配置字段</h6>
                        <p class="text-muted">使用上方的字段选择器添加第一个字段</p>
                    </div>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 弹出框集成
- 基于弹出框的字段选择界面
- 灵活的弹出框配置
- 自动的位置调整

### 2. 并发控制
- 使用KeepLast防止竞态条件
- 异步操作的安全处理
- 状态更新的一致性

### 3. 路径导航
- 支持关联字段的深度导航
- 字段路径的可视化显示
- 智能的路径描述加载

### 4. 灵活配置
- 丰富的配置选项
- 可定制的过滤器
- 调试模式支持

## 设计模式

### 1. 组合模式 (Composite Pattern)
- 字段选择器与弹出框的组合
- 统一的组件接口

### 2. 策略模式 (Strategy Pattern)
- 可配置的字段过滤策略
- 不同模式的行为策略

### 3. 观察者模式 (Observer Pattern)
- 字段选择的事件通知
- 状态变化的响应机制

## 注意事项

1. **性能优化**: 避免频繁的字段信息加载
2. **用户体验**: 提供清晰的字段选择反馈
3. **错误处理**: 处理字段加载失败的情况
4. **内存管理**: 正确清理异步操作

## 扩展建议

1. **字段预览**: 字段值的预览功能
2. **批量选择**: 支持多字段的批量选择
3. **字段分组**: 按类别分组显示字段
4. **历史记录**: 字段选择的历史记录
5. **快捷方式**: 常用字段的快捷选择

该模型字段选择器为Odoo Web应用提供了强大的字段选择功能，通过直观的界面和灵活的配置确保了良好的用户体验和开发者友好性。

# Dropzone - 拖放区域组件

## 概述

`dropzone.js` 是 Odoo Web 核心模块的拖放区域组件，提供了文件拖放功能的基础UI组件。该组件创建一个覆盖在目标元素上的拖放区域，支持文件拖拽检测、视觉反馈和拖放事件处理，为用户提供了直观的文件上传交互体验，广泛应用于文件上传、附件管理、图片上传等需要拖放功能的场景。

## 文件信息
- **路径**: `/web/static/src/core/dropzone/dropzone.js`
- **行数**: 28
- **模块**: `@web/core/dropzone/dropzone`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                          // OWL框架
```

## 主组件类

### 1. 组件属性定义

```javascript
static props = {
    extraClass: { type: String, optional: true },  // 额外CSS类名
    onDrop: { type: Function, optional: true },     // 拖放事件回调
    ref: Object,                                    // 目标元素引用
};
```

**属性功能**:
- **样式定制**: extraClass允许添加自定义CSS类
- **事件处理**: onDrop处理文件拖放事件
- **目标引用**: ref指定要覆盖的目标元素
- **简洁设计**: 最小化的属性配置

### 2. 组件初始化

```javascript
setup() {
    super.setup();
    this.root = useRef("root");
    this.state = useState({
        isDraggingInside: false,
    });
    useEffect(() => {
        const { top, left, width, height } = this.props.ref.el.getBoundingClientRect();
        this.root.el.style = `top:${top}px;left:${left}px;width:${width}px;height:${height}px;`;
    });
}
```

**初始化功能**:
- **引用管理**: 获取根元素的引用
- **状态管理**: 管理拖拽状态
- **位置同步**: 使拖放区域与目标元素位置同步
- **动态定位**: 根据目标元素的边界矩形设置位置和大小

## 核心功能

### 1. 位置同步机制

```javascript
useEffect(() => {
    const { top, left, width, height } = this.props.ref.el.getBoundingClientRect();
    this.root.el.style = `top:${top}px;left:${left}px;width:${width}px;height:${height}px;`;
});
```

**位置同步功能**:
- **边界计算**: 获取目标元素的边界矩形
- **样式设置**: 设置拖放区域的位置和大小
- **完全覆盖**: 确保拖放区域完全覆盖目标元素
- **响应式**: 目标元素变化时自动更新位置

### 2. 拖拽状态管理

```javascript
this.state = useState({
    isDraggingInside: false,
});
```

**状态管理功能**:
- **拖拽检测**: 检测是否有文件在区域内拖拽
- **视觉反馈**: 为视觉反馈提供状态支持
- **事件响应**: 响应拖拽进入和离开事件

## 使用场景

### 1. 基础文件拖放

```javascript
// 基础文件拖放使用
class BasicFileDropzone extends Component {
    setup() {
        this.targetRef = useRef("target");
        this.state = useState({
            files: [],
            isDragging: false
        });
    }

    onFileDrop(files) {
        console.log('Files dropped:', files);
        this.state.files = [...this.state.files, ...files];
        this.processFiles(files);
    }

    processFiles(files) {
        files.forEach(file => {
            console.log(`Processing file: ${file.name}, size: ${file.size}`);
            // 处理文件逻辑
        });
    }

    render() {
        return xml`
            <div class="basic-file-dropzone">
                <div 
                    t-ref="target" 
                    class="drop-target"
                    style="width: 300px; height: 200px; border: 2px dashed #ccc; position: relative;"
                >
                    <div class="drop-content text-center p-4">
                        <i class="fa fa-cloud-upload fa-3x text-muted mb-3"/>
                        <p>拖拽文件到此处上传</p>
                        <p class="text-muted">或点击选择文件</p>
                    </div>
                    
                    <Dropzone
                        ref="targetRef"
                        onDrop="onFileDrop"
                        extraClass="custom-dropzone"
                    />
                </div>
                
                <div class="file-list mt-3" t-if="state.files.length">
                    <h6>已选择的文件:</h6>
                    <ul class="list-group">
                        <t t-foreach="state.files" t-as="file" t-key="file_index">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span t-esc="file.name"/>
                                <span class="badge bg-secondary" t-esc="(file.size / 1024).toFixed(1) + ' KB'"/>
                            </li>
                        </t>
                    </ul>
                </div>
            </div>
        `;
    }
}
```

### 2. 图片拖放预览

```javascript
// 图片拖放预览组件
class ImageDropzonePreview extends Component {
    setup() {
        this.targetRef = useRef("imageTarget");
        this.state = useState({
            images: [],
            previewUrls: [],
            isDragging: false
        });
    }

    onImageDrop(files) {
        const imageFiles = Array.from(files).filter(file => 
            file.type.startsWith('image/')
        );

        if (imageFiles.length === 0) {
            this.notification.add('请选择图片文件', { type: 'warning' });
            return;
        }

        this.state.images = [...this.state.images, ...imageFiles];
        this.generatePreviews(imageFiles);
    }

    generatePreviews(files) {
        files.forEach(file => {
            const reader = new FileReader();
            reader.onload = (e) => {
                this.state.previewUrls.push({
                    file: file,
                    url: e.target.result,
                    id: Date.now() + Math.random()
                });
            };
            reader.readAsDataURL(file);
        });
    }

    removeImage(imageId) {
        this.state.previewUrls = this.state.previewUrls.filter(
            img => img.id !== imageId
        );
        this.state.images = this.state.images.filter(
            file => !this.state.previewUrls.find(img => img.file === file)
        );
    }

    uploadImages() {
        if (this.state.images.length === 0) {
            this.notification.add('请先选择图片', { type: 'warning' });
            return;
        }

        // 模拟上传过程
        this.notification.add(`开始上传 ${this.state.images.length} 张图片`, { type: 'info' });
        
        this.state.images.forEach((file, index) => {
            setTimeout(() => {
                console.log(`Uploading image ${index + 1}: ${file.name}`);
                // 实际的上传逻辑
            }, index * 500);
        });
    }

    render() {
        return xml`
            <div class="image-dropzone-preview">
                <div 
                    t-ref="imageTarget"
                    class="image-drop-area"
                    style="width: 100%; height: 200px; border: 2px dashed #007bff; border-radius: 8px; position: relative; background: #f8f9fa;"
                >
                    <div class="drop-content text-center p-4">
                        <i class="fa fa-image fa-3x text-primary mb-3"/>
                        <h5>拖拽图片到此处</h5>
                        <p class="text-muted">支持 JPG, PNG, GIF 格式</p>
                    </div>
                    
                    <Dropzone
                        ref="targetRef"
                        onDrop="onImageDrop"
                        extraClass="image-dropzone"
                    />
                </div>

                <div class="image-previews mt-4" t-if="state.previewUrls.length">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6>图片预览 (${state.previewUrls.length})</h6>
                        <button 
                            class="btn btn-primary"
                            t-on-click="uploadImages"
                        >
                            <i class="fa fa-upload"/> 上传图片
                        </button>
                    </div>
                    
                    <div class="row">
                        <t t-foreach="state.previewUrls" t-as="image" t-key="image.id">
                            <div class="col-md-3 mb-3">
                                <div class="card">
                                    <img 
                                        t-att-src="image.url" 
                                        class="card-img-top"
                                        style="height: 150px; object-fit: cover;"
                                        t-att-alt="image.file.name"
                                    />
                                    <div class="card-body p-2">
                                        <p class="card-text small mb-1" t-esc="image.file.name"/>
                                        <p class="card-text">
                                            <small class="text-muted" t-esc="(image.file.size / 1024).toFixed(1) + ' KB'"/>
                                        </p>
                                        <button 
                                            class="btn btn-sm btn-outline-danger w-100"
                                            t-on-click="() => this.removeImage(image.id)"
                                        >
                                            <i class="fa fa-trash"/> 删除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>
                </div>
            </div>
        `;
    }
}
```

### 3. 高级拖放管理器

```javascript
// 高级拖放管理器
class AdvancedDropzoneManager extends Component {
    setup() {
        this.targetRef = useRef("dropTarget");
        this.state = useState({
            files: [],
            uploadProgress: {},
            dragCounter: 0,
            isDragging: false,
            allowedTypes: ['image/*', 'application/pdf', '.doc', '.docx'],
            maxFileSize: 10 * 1024 * 1024, // 10MB
            maxFiles: 5
        });

        this.notification = useService('notification');
    }

    onFileDrop(files) {
        const validFiles = this.validateFiles(Array.from(files));
        if (validFiles.length > 0) {
            this.addFiles(validFiles);
        }
    }

    validateFiles(files) {
        const validFiles = [];
        const errors = [];

        files.forEach(file => {
            // 检查文件数量限制
            if (this.state.files.length + validFiles.length >= this.state.maxFiles) {
                errors.push(`最多只能上传 ${this.state.maxFiles} 个文件`);
                return;
            }

            // 检查文件大小
            if (file.size > this.state.maxFileSize) {
                errors.push(`文件 "${file.name}" 超过大小限制 (${this.state.maxFileSize / 1024 / 1024}MB)`);
                return;
            }

            // 检查文件类型
            const isValidType = this.state.allowedTypes.some(type => {
                if (type.startsWith('.')) {
                    return file.name.toLowerCase().endsWith(type);
                } else if (type.includes('*')) {
                    const baseType = type.split('/')[0];
                    return file.type.startsWith(baseType);
                } else {
                    return file.type === type;
                }
            });

            if (!isValidType) {
                errors.push(`文件 "${file.name}" 类型不支持`);
                return;
            }

            // 检查重复文件
            const isDuplicate = this.state.files.some(existingFile => 
                existingFile.name === file.name && existingFile.size === file.size
            );

            if (isDuplicate) {
                errors.push(`文件 "${file.name}" 已存在`);
                return;
            }

            validFiles.push(file);
        });

        // 显示错误信息
        if (errors.length > 0) {
            this.notification.add(errors.join('\n'), { type: 'warning' });
        }

        return validFiles;
    }

    addFiles(files) {
        const newFiles = files.map(file => ({
            id: Date.now() + Math.random(),
            file: file,
            status: 'pending', // pending, uploading, completed, error
            progress: 0,
            error: null
        }));

        this.state.files = [...this.state.files, ...newFiles];
        this.notification.add(`添加了 ${files.length} 个文件`, { type: 'success' });
    }

    removeFile(fileId) {
        this.state.files = this.state.files.filter(f => f.id !== fileId);
        delete this.state.uploadProgress[fileId];
    }

    async uploadFile(fileObj) {
        fileObj.status = 'uploading';
        fileObj.progress = 0;

        try {
            // 模拟上传过程
            for (let progress = 0; progress <= 100; progress += 10) {
                await new Promise(resolve => setTimeout(resolve, 100));
                fileObj.progress = progress;
            }

            fileObj.status = 'completed';
            this.notification.add(`文件 "${fileObj.file.name}" 上传成功`, { type: 'success' });
        } catch (error) {
            fileObj.status = 'error';
            fileObj.error = error.message;
            this.notification.add(`文件 "${fileObj.file.name}" 上传失败`, { type: 'danger' });
        }
    }

    async uploadAllFiles() {
        const pendingFiles = this.state.files.filter(f => f.status === 'pending');
        
        if (pendingFiles.length === 0) {
            this.notification.add('没有待上传的文件', { type: 'info' });
            return;
        }

        // 并发上传，但限制并发数
        const concurrency = 3;
        for (let i = 0; i < pendingFiles.length; i += concurrency) {
            const batch = pendingFiles.slice(i, i + concurrency);
            await Promise.all(batch.map(file => this.uploadFile(file)));
        }
    }

    clearCompleted() {
        this.state.files = this.state.files.filter(f => f.status !== 'completed');
    }

    clearAll() {
        this.state.files = [];
        this.state.uploadProgress = {};
    }

    getStatusIcon(status) {
        switch (status) {
            case 'pending': return 'fa-clock-o';
            case 'uploading': return 'fa-spinner fa-spin';
            case 'completed': return 'fa-check text-success';
            case 'error': return 'fa-exclamation-triangle text-danger';
            default: return 'fa-file';
        }
    }

    render() {
        const pendingCount = this.state.files.filter(f => f.status === 'pending').length;
        const completedCount = this.state.files.filter(f => f.status === 'completed').length;
        const errorCount = this.state.files.filter(f => f.status === 'error').length;

        return xml`
            <div class="advanced-dropzone-manager">
                <div class="dropzone-header mb-3">
                    <h5>文件上传管理器</h5>
                    <div class="upload-stats">
                        <span class="badge bg-secondary me-2">待上传: ${pendingCount}</span>
                        <span class="badge bg-success me-2">已完成: ${completedCount}</span>
                        <span class="badge bg-danger" t-if="errorCount">错误: ${errorCount}</span>
                    </div>
                </div>

                <div 
                    t-ref="dropTarget"
                    class="drop-zone"
                    t-att-class="state.isDragging ? 'dragging' : ''"
                    style="min-height: 200px; border: 2px dashed #dee2e6; border-radius: 8px; position: relative;"
                >
                    <div class="drop-content text-center p-4">
                        <i class="fa fa-cloud-upload fa-4x text-muted mb-3"/>
                        <h4>拖拽文件到此处</h4>
                        <p class="text-muted">
                            支持: ${this.state.allowedTypes.join(', ')}<br/>
                            最大文件大小: ${this.state.maxFileSize / 1024 / 1024}MB<br/>
                            最多文件数: ${this.state.maxFiles}
                        </p>
                    </div>
                    
                    <Dropzone
                        ref="targetRef"
                        onDrop="onFileDrop"
                        extraClass="advanced-dropzone"
                    />
                </div>

                <div class="file-management mt-4" t-if="state.files.length">
                    <div class="management-header d-flex justify-content-between align-items-center mb-3">
                        <h6>文件列表 (${this.state.files.length})</h6>
                        <div class="btn-group">
                            <button 
                                class="btn btn-primary"
                                t-on-click="uploadAllFiles"
                                t-att-disabled="pendingCount === 0"
                            >
                                <i class="fa fa-upload"/> 上传全部
                            </button>
                            <button 
                                class="btn btn-outline-secondary"
                                t-on-click="clearCompleted"
                                t-att-disabled="completedCount === 0"
                            >
                                清除已完成
                            </button>
                            <button 
                                class="btn btn-outline-danger"
                                t-on-click="clearAll"
                            >
                                清除全部
                            </button>
                        </div>
                    </div>

                    <div class="file-list">
                        <t t-foreach="state.files" t-as="fileObj" t-key="fileObj.id">
                            <div class="file-item card mb-2">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="file-info d-flex align-items-center">
                                            <i t-att-class="'fa ' + getStatusIcon(fileObj.status) + ' me-2'"/>
                                            <div>
                                                <div class="file-name" t-esc="fileObj.file.name"/>
                                                <small class="text-muted" t-esc="(fileObj.file.size / 1024).toFixed(1) + ' KB'"/>
                                            </div>
                                        </div>
                                        
                                        <div class="file-actions">
                                            <button 
                                                class="btn btn-sm btn-outline-primary me-2"
                                                t-on-click="() => this.uploadFile(fileObj)"
                                                t-if="fileObj.status === 'pending'"
                                            >
                                                上传
                                            </button>
                                            <button 
                                                class="btn btn-sm btn-outline-danger"
                                                t-on-click="() => this.removeFile(fileObj.id)"
                                            >
                                                删除
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div class="progress mt-2" t-if="fileObj.status === 'uploading'">
                                        <div 
                                            class="progress-bar" 
                                            role="progressbar" 
                                            t-att-style="'width: ' + fileObj.progress + '%'"
                                            t-att-aria-valuenow="fileObj.progress"
                                            aria-valuemin="0" 
                                            aria-valuemax="100"
                                        >
                                            <t t-esc="fileObj.progress"/>%
                                        </div>
                                    </div>
                                    
                                    <div class="error-message mt-2" t-if="fileObj.status === 'error'">
                                        <small class="text-danger" t-esc="fileObj.error"/>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 位置同步
- 自动与目标元素位置同步
- 响应式的大小调整
- 精确的覆盖定位

### 2. 状态管理
- 拖拽状态的实时跟踪
- 视觉反馈的状态支持
- 简洁的状态结构

### 3. 事件处理
- 文件拖放事件的处理
- 可配置的回调函数
- 灵活的事件响应

### 4. 样式定制
- 可扩展的CSS类支持
- 灵活的样式配置
- 主题适配能力

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 为目标元素添加拖放功能
- 不改变原有元素结构

### 2. 观察者模式 (Observer Pattern)
- 拖放事件的监听和响应
- 状态变化的通知机制

### 3. 策略模式 (Strategy Pattern)
- 可配置的拖放处理策略
- 灵活的事件处理方式

## 注意事项

1. **位置同步**: 确保拖放区域与目标元素位置同步
2. **事件处理**: 正确处理拖放事件的冒泡和默认行为
3. **性能优化**: 避免频繁的位置计算和样式更新
4. **浏览器兼容**: 确保在不同浏览器中的兼容性

## 扩展建议

1. **文件验证**: 增强的文件类型和大小验证
2. **进度显示**: 文件上传进度的可视化显示
3. **批量操作**: 支持批量文件的处理
4. **拖拽预览**: 拖拽过程中的文件预览
5. **触摸支持**: 移动设备的触摸拖放支持

该拖放区域组件为Odoo Web应用提供了基础的文件拖放功能，通过简洁的设计和灵活的配置确保了良好的用户体验和开发者友好性。

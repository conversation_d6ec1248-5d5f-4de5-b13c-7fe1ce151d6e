# @web/core/name_service.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/name_service.js`
- **原始路径**: `/web/static/src/core/name_service.js`
- **代码行数**: 110行
- **作用**: 提供记录显示名称的缓存和批量加载服务，优化数据库查询性能

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 显示名称服务的工作原理和缓存机制
- 批量数据加载的优化策略
- Promise和Deferred的高级使用模式
- 服务间依赖和异步操作管理
- 性能优化的实际应用案例

## 📚 核心概念

### 什么是Name Service？
Name Service是Odoo Web框架中的**显示名称管理服务**，主要功能：
- **缓存管理**: 缓存已加载的记录显示名称
- **批量加载**: 将多个请求合并为单次数据库查询
- **异步处理**: 使用Promise管理异步数据加载
- **错误处理**: 处理不可访问或缺失的记录

### 基本使用场景
```javascript
// 在组件中使用name服务
const nameService = useService("name");

// 加载单个记录的显示名称
const displayNames = await nameService.loadDisplayNames("res.partner", [1, 2, 3]);
console.log(displayNames); // { 1: "John Doe", 2: "Jane Smith", 3: "Company ABC" }

// 添加已知的显示名称到缓存
nameService.addDisplayNames("res.partner", { 4: "New Partner" });
```

## 🏗️ 服务结构分析

### 服务定义
```javascript
const nameService = {
    dependencies: ["orm"],           // 依赖ORM服务
    async: ["loadDisplayNames"],     // 异步方法声明
    start(env, { orm }) {           // 服务启动函数
        // 服务实现
        return { addDisplayNames, clearCache, loadDisplayNames };
    }
};
```

**关键特性**：
- **依赖注入**: 依赖ORM服务进行数据库操作
- **异步声明**: 明确标识异步方法
- **返回接口**: 暴露三个核心方法

### 内部状态管理
```javascript
let cache = {};              // 全局缓存对象
const batches = {};          // 批量请求管理
```

**数据结构**：
```javascript
// cache结构
cache = {
    "res.partner": {
        1: Deferred,         // 每个ID对应一个Deferred对象
        2: Deferred,
        3: Deferred
    },
    "product.product": {
        10: Deferred,
        11: Deferred
    }
};

// batches结构
batches = {
    "res.partner": [1, 2, 3],    // 待批量加载的ID列表
    "product.product": [10, 11]
};
```

## 🔍 核心方法详解

### 1. loadDisplayNames() - 批量加载显示名称
```javascript
async function loadDisplayNames(resModel, resIds) {
    const mapping = getMapping(resModel);
    const proms = [];
    const resIdsToFetch = [];
    
    // 1. 检查缓存，收集需要加载的ID
    for (const resId of unique(resIds)) {
        if (!isId(resId)) {
            throw new Error(`Invalid ID: ${resId}`);
        }
        if (!(resId in mapping)) {
            mapping[resId] = new Deferred();
            resIdsToFetch.push(resId);
        }
        proms.push(mapping[resId]);
    }
    
    // 2. 批量处理新的请求
    if (resIdsToFetch.length) {
        // 批量合并逻辑
    }
    
    // 3. 等待所有Promise完成
    const names = await Promise.all(proms);
    return Object.fromEntries(zip(resIds, names));
}
```

**工作流程**：
1. **去重检查**: 使用unique()去除重复ID
2. **缓存查找**: 检查ID是否已在缓存中
3. **创建Promise**: 为新ID创建Deferred对象
4. **批量合并**: 将请求添加到批次中
5. **数据库查询**: 执行批量查询
6. **结果返回**: 返回ID到名称的映射

### 2. 批量处理机制
```javascript
if (resIdsToFetch.length) {
    if (batches[resModel]) {
        // 添加到现有批次
        batches[resModel].push(...resIdsToFetch);
    } else {
        // 创建新批次
        batches[resModel] = resIdsToFetch;
        await Promise.resolve(); // 等待下一个事件循环
        
        const idsInBatch = unique(batches[resModel]);
        delete batches[resModel];
        
        // 执行批量查询
        const specification = { display_name: {} };
        orm.silent.webSearchRead(resModel, [["id", "in", idsInBatch]], { specification })
            .then(({ records }) => {
                // 处理查询结果
            });
    }
}
```

**批量优化策略**：
- **微任务延迟**: 使用`await Promise.resolve()`延迟到下一个事件循环
- **请求合并**: 将同一事件循环中的多个请求合并
- **去重处理**: 确保不重复查询相同ID
- **静默查询**: 使用`orm.silent`避免触发加载指示器

### 3. addDisplayNames() - 添加已知名称
```javascript
function addDisplayNames(resModel, displayNames) {
    const mapping = getMapping(resModel);
    for (const resId in displayNames) {
        mapping[resId] = new Deferred();
        mapping[resId].resolve(displayNames[resId]);
    }
}
```

**使用场景**：
- **预填充缓存**: 在已知显示名称时直接添加到缓存
- **避免重复查询**: 减少不必要的数据库请求
- **性能优化**: 提高后续访问速度

### 4. clearCache() - 清除缓存
```javascript
function clearCache() {
    cache = {};
}

env.bus.addEventListener("ACTION_MANAGER:UPDATE", clearCache);
```

**缓存失效策略**：
- **动作更新**: 当页面动作改变时清除缓存
- **数据一致性**: 确保显示的数据是最新的
- **内存管理**: 防止缓存无限增长

## 🎨 实际应用场景

### 1. 在Many2one字段中的使用
```javascript
// Many2one字段组件
class Many2oneField extends Component {
    setup() {
        this.nameService = useService("name");
        this.displayName = "";
        this.loadDisplayName();
    }
    
    async loadDisplayName() {
        if (this.props.value) {
            const names = await this.nameService.loadDisplayNames(
                this.props.relation, 
                [this.props.value]
            );
            this.displayName = names[this.props.value];
        }
    }
}
```

### 2. 在列表视图中的批量使用
```javascript
// 列表渲染器
class ListRenderer extends Component {
    setup() {
        this.nameService = useService("name");
        this.loadAllDisplayNames();
    }
    
    async loadAllDisplayNames() {
        const relationFields = this.getRelationFields();
        
        for (const field of relationFields) {
            const ids = this.props.records
                .map(record => record[field.name])
                .filter(id => id);
            
            // 批量加载所有相关记录的显示名称
            await this.nameService.loadDisplayNames(field.relation, ids);
        }
    }
}
```

### 3. 在搜索建议中的使用
```javascript
// 搜索建议组件
class SearchSuggestion extends Component {
    setup() {
        this.nameService = useService("name");
    }
    
    async getSuggestions(model, term) {
        // 搜索记录ID
        const records = await this.orm.searchRead(model, [
            ["name", "ilike", term]
        ], ["id"]);
        
        const ids = records.map(r => r.id);
        
        // 批量加载显示名称
        const displayNames = await this.nameService.loadDisplayNames(model, ids);
        
        return ids.map(id => ({
            id,
            display_name: displayNames[id]
        }));
    }
}
```

## 🛠️ 实践练习

### 练习1: 自定义名称服务扩展
```javascript
// 扩展名称服务，添加本地化支持
class LocalizedNameService {
    constructor(baseNameService, localization) {
        this.baseService = baseNameService;
        this.localization = localization;
        this.localizedCache = {};
    }
    
    async loadDisplayNames(resModel, resIds, lang = null) {
        const currentLang = lang || this.localization.code;
        const cacheKey = `${resModel}_${currentLang}`;
        
        if (!this.localizedCache[cacheKey]) {
            this.localizedCache[cacheKey] = {};
        }
        
        // 检查本地化缓存
        const uncachedIds = resIds.filter(id => 
            !(id in this.localizedCache[cacheKey])
        );
        
        if (uncachedIds.length > 0) {
            // 加载本地化显示名称
            const names = await this.loadLocalizedNames(
                resModel, uncachedIds, currentLang
            );
            
            Object.assign(this.localizedCache[cacheKey], names);
        }
        
        return Object.fromEntries(
            resIds.map(id => [id, this.localizedCache[cacheKey][id]])
        );
    }
    
    async loadLocalizedNames(resModel, resIds, lang) {
        // 实现本地化名称加载逻辑
        const context = { lang };
        const records = await this.orm.webSearchRead(
            resModel, 
            [["id", "in", resIds]], 
            { specification: { display_name: {} }, context }
        );
        
        return Object.fromEntries(
            records.map(record => [record.id, record.display_name])
        );
    }
}
```

### 练习2: 名称服务性能监控
```javascript
// 名称服务性能监控器
class NameServiceMonitor {
    constructor(nameService) {
        this.nameService = nameService;
        this.stats = {
            cacheHits: 0,
            cacheMisses: 0,
            batchRequests: 0,
            totalRequests: 0
        };
        this.wrapMethods();
    }
    
    wrapMethods() {
        const originalLoad = this.nameService.loadDisplayNames;
        
        this.nameService.loadDisplayNames = async (resModel, resIds) => {
            this.stats.totalRequests++;
            const startTime = performance.now();
            
            const result = await originalLoad.call(this.nameService, resModel, resIds);
            
            const endTime = performance.now();
            console.log(`Name service request took ${endTime - startTime}ms`);
            
            return result;
        };
    }
    
    getStats() {
        return {
            ...this.stats,
            hitRate: this.stats.cacheHits / this.stats.totalRequests,
            averageResponseTime: this.totalTime / this.stats.totalRequests
        };
    }
}
```

### 练习3: 智能预加载策略
```javascript
// 智能预加载名称服务
class PredictiveNameService {
    constructor(baseNameService) {
        this.baseService = baseNameService;
        this.accessPatterns = new Map();
        this.preloadThreshold = 3; // 访问3次后开始预加载
    }
    
    async loadDisplayNames(resModel, resIds) {
        // 记录访问模式
        this.recordAccess(resModel, resIds);
        
        // 执行原始加载
        const result = await this.baseService.loadDisplayNames(resModel, resIds);
        
        // 触发智能预加载
        this.triggerPredictivePreload(resModel, resIds);
        
        return result;
    }
    
    recordAccess(resModel, resIds) {
        const key = resModel;
        if (!this.accessPatterns.has(key)) {
            this.accessPatterns.set(key, { count: 0, recentIds: [] });
        }
        
        const pattern = this.accessPatterns.get(key);
        pattern.count++;
        pattern.recentIds.push(...resIds);
        
        // 保持最近100个ID
        if (pattern.recentIds.length > 100) {
            pattern.recentIds = pattern.recentIds.slice(-100);
        }
    }
    
    triggerPredictivePreload(resModel, resIds) {
        const pattern = this.accessPatterns.get(resModel);
        
        if (pattern && pattern.count >= this.preloadThreshold) {
            // 预测可能需要的相关记录
            const predictedIds = this.predictRelatedIds(resModel, resIds);
            
            if (predictedIds.length > 0) {
                // 异步预加载，不阻塞当前请求
                setTimeout(() => {
                    this.baseService.loadDisplayNames(resModel, predictedIds);
                }, 100);
            }
        }
    }
    
    predictRelatedIds(resModel, currentIds) {
        // 简单的预测策略：加载相邻的ID
        const predicted = [];
        for (const id of currentIds) {
            // 预加载前后5个ID
            for (let i = id - 5; i <= id + 5; i++) {
                if (i > 0 && i !== id) {
                    predicted.push(i);
                }
            }
        }
        return predicted;
    }
}
```

## 🔧 调试技巧

### 查看缓存状态
```javascript
// 检查名称服务缓存
const nameService = env.services.name;

// 查看缓存内容（需要访问内部状态）
console.log("Name service cache:", nameService.__cache);

// 监控缓存命中率
let cacheHits = 0;
let totalRequests = 0;

const originalLoad = nameService.loadDisplayNames;
nameService.loadDisplayNames = async function(resModel, resIds) {
    totalRequests++;
    console.log(`Cache hit rate: ${(cacheHits / totalRequests * 100).toFixed(2)}%`);
    return originalLoad.apply(this, arguments);
};
```

### 批量请求监控
```javascript
// 监控批量请求
const originalWebSearchRead = env.services.orm.webSearchRead;
env.services.orm.webSearchRead = function(model, domain, options) {
    if (options?.specification?.display_name) {
        console.log(`Batch name request for ${model}:`, domain);
    }
    return originalWebSearchRead.apply(this, arguments);
};
```

## 📊 性能考虑

### 优化策略
1. **批量合并**: 将多个请求合并为单次查询
2. **缓存机制**: 避免重复查询相同数据
3. **异步处理**: 不阻塞UI渲染
4. **内存管理**: 适时清理缓存

### 最佳实践
```javascript
// ✅ 好的做法：批量加载
const allIds = records.map(r => r.partner_id).filter(Boolean);
const displayNames = await nameService.loadDisplayNames("res.partner", allIds);

// ❌ 不好的做法：逐个加载
for (const record of records) {
    if (record.partner_id) {
        const names = await nameService.loadDisplayNames("res.partner", [record.partner_id]);
    }
}
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解名称服务的缓存和批量加载机制
- [ ] 掌握Deferred对象的使用模式
- [ ] 理解微任务延迟的优化策略
- [ ] 能够监控和调试名称服务性能
- [ ] 掌握服务扩展和自定义方法

## 🚀 下一步学习
学完名称服务后，建议继续学习：
1. **ORM服务** (`@web/core/orm_service.js`) - 理解数据访问层
2. **UI服务** (`@web/core/ui_service.js`) - 学习UI状态管理
3. **通知服务** (`@web/core/notification_service.js`) - 掌握消息通知

## 💡 重要提示
- 名称服务是性能优化的典型案例
- 理解批量处理对大型应用至关重要
- 缓存策略需要平衡性能和数据一致性
- 异步编程模式在现代Web开发中非常重要

## 🔍 深入理解：批量处理的设计精髓

### 为什么需要批量处理？
```javascript
// 问题场景：列表视图显示100个订单，每个订单都有客户字段
// ❌ 朴素实现：100次数据库查询
for (const order of orders) {
    const customerName = await loadCustomerName(order.customer_id);
}

// ✅ 批量优化：1次数据库查询
const customerIds = orders.map(o => o.customer_id);
const customerNames = await nameService.loadDisplayNames("res.partner", customerIds);
```

**性能对比**：
- **朴素方式**: 100次查询 × 10ms = 1000ms
- **批量方式**: 1次查询 × 50ms = 50ms
- **性能提升**: 20倍性能提升！

### 微任务延迟的巧妙设计
```javascript
// 关键代码分析
batches[resModel] = resIdsToFetch;
await Promise.resolve(); // 🔑 关键：等待下一个事件循环
const idsInBatch = unique(batches[resModel]);
```

**时序分析**：
```
Event Loop Tick 1:
├── loadDisplayNames("res.partner", [1]) → 添加到batch
├── loadDisplayNames("res.partner", [2]) → 添加到batch
├── loadDisplayNames("res.partner", [3]) → 添加到batch
└── 所有同步代码执行完毕

Event Loop Tick 2:
└── Promise.resolve() 完成 → 执行批量查询 [1,2,3]
```

### Deferred模式的优雅应用
```javascript
// Deferred对象的生命周期
const deferred = new Deferred();

// 1. 创建阶段：立即返回Promise
const promise = deferred; // Deferred继承自Promise

// 2. 等待阶段：可以被await
const result = await promise;

// 3. 解决阶段：在异步回调中解决
setTimeout(() => {
    deferred.resolve("result");
}, 1000);
```

## 🎓 高级应用模式

### 1. 分层缓存策略
```javascript
class TieredNameService {
    constructor(baseService) {
        this.baseService = baseService;
        this.l1Cache = new Map(); // 内存缓存
        this.l2Cache = new Map(); // 会话缓存
    }

    async loadDisplayNames(resModel, resIds) {
        const results = {};
        const uncachedIds = [];

        // L1缓存检查
        for (const id of resIds) {
            const cacheKey = `${resModel}:${id}`;
            if (this.l1Cache.has(cacheKey)) {
                results[id] = this.l1Cache.get(cacheKey);
            } else {
                uncachedIds.push(id);
            }
        }

        if (uncachedIds.length > 0) {
            // L2缓存检查
            const l2Results = this.checkL2Cache(resModel, uncachedIds);
            Object.assign(results, l2Results);

            // 从基础服务加载剩余数据
            const stillUncached = uncachedIds.filter(id => !(id in l2Results));
            if (stillUncached.length > 0) {
                const freshResults = await this.baseService.loadDisplayNames(
                    resModel, stillUncached
                );
                Object.assign(results, freshResults);

                // 更新缓存
                this.updateCaches(resModel, freshResults);
            }
        }

        return results;
    }

    updateCaches(resModel, results) {
        for (const [id, name] of Object.entries(results)) {
            const cacheKey = `${resModel}:${id}`;
            this.l1Cache.set(cacheKey, name);
            this.l2Cache.set(cacheKey, name);
        }
    }
}
```

### 2. 智能预取策略
```javascript
class PredictiveNameService {
    constructor(baseService) {
        this.baseService = baseService;
        this.accessGraph = new Map(); // 访问关系图
        this.preloadQueue = new Set();
    }

    async loadDisplayNames(resModel, resIds) {
        // 记录访问模式
        this.recordAccessPattern(resModel, resIds);

        // 执行加载
        const result = await this.baseService.loadDisplayNames(resModel, resIds);

        // 触发智能预取
        this.schedulePreload(resModel, resIds);

        return result;
    }

    recordAccessPattern(resModel, resIds) {
        const key = resModel;
        if (!this.accessGraph.has(key)) {
            this.accessGraph.set(key, new Map());
        }

        const modelGraph = this.accessGraph.get(key);

        // 记录ID之间的关联关系
        for (let i = 0; i < resIds.length; i++) {
            for (let j = i + 1; j < resIds.length; j++) {
                const id1 = resIds[i];
                const id2 = resIds[j];

                if (!modelGraph.has(id1)) modelGraph.set(id1, new Set());
                if (!modelGraph.has(id2)) modelGraph.set(id2, new Set());

                modelGraph.get(id1).add(id2);
                modelGraph.get(id2).add(id1);
            }
        }
    }

    schedulePreload(resModel, resIds) {
        const modelGraph = this.accessGraph.get(resModel);
        if (!modelGraph) return;

        const candidatesForPreload = new Set();

        // 基于访问图预测相关ID
        for (const id of resIds) {
            const relatedIds = modelGraph.get(id);
            if (relatedIds) {
                relatedIds.forEach(relatedId => {
                    candidatesForPreload.add(relatedId);
                });
            }
        }

        // 异步预加载
        if (candidatesForPreload.size > 0) {
            setTimeout(() => {
                this.baseService.loadDisplayNames(
                    resModel,
                    Array.from(candidatesForPreload)
                );
            }, 50); // 50ms延迟，避免阻塞当前操作
        }
    }
}
```

### 3. 错误恢复和重试机制
```javascript
class ResilientNameService {
    constructor(baseService) {
        this.baseService = baseService;
        this.retryConfig = {
            maxRetries: 3,
            backoffMultiplier: 2,
            initialDelay: 100
        };
        this.failedRequests = new Map();
    }

    async loadDisplayNames(resModel, resIds) {
        const requestKey = `${resModel}:${resIds.join(',')}`;

        try {
            const result = await this.baseService.loadDisplayNames(resModel, resIds);

            // 成功后清除失败记录
            this.failedRequests.delete(requestKey);

            return result;
        } catch (error) {
            return this.handleFailure(requestKey, resModel, resIds, error);
        }
    }

    async handleFailure(requestKey, resModel, resIds, error) {
        const failureInfo = this.failedRequests.get(requestKey) || {
            attempts: 0,
            lastAttempt: 0
        };

        failureInfo.attempts++;
        failureInfo.lastAttempt = Date.now();
        this.failedRequests.set(requestKey, failureInfo);

        if (failureInfo.attempts <= this.retryConfig.maxRetries) {
            // 指数退避重试
            const delay = this.retryConfig.initialDelay *
                Math.pow(this.retryConfig.backoffMultiplier, failureInfo.attempts - 1);

            console.warn(`Name service retry ${failureInfo.attempts}/${this.retryConfig.maxRetries} after ${delay}ms`);

            await new Promise(resolve => setTimeout(resolve, delay));
            return this.loadDisplayNames(resModel, resIds);
        } else {
            // 超过重试次数，返回错误标记
            console.error(`Name service failed after ${this.retryConfig.maxRetries} retries:`, error);

            return Object.fromEntries(
                resIds.map(id => [id, ERROR_INACCESSIBLE_OR_MISSING])
            );
        }
    }
}
```

## 🔧 性能监控和分析工具

### 名称服务性能分析器
```javascript
class NameServiceProfiler {
    constructor() {
        this.metrics = {
            requests: [],
            cacheStats: { hits: 0, misses: 0 },
            batchStats: { count: 0, totalSize: 0, avgSize: 0 },
            responseTimeStats: { min: Infinity, max: 0, avg: 0, total: 0 }
        };
    }

    wrapNameService(nameService) {
        const profiler = this;
        const originalLoad = nameService.loadDisplayNames;

        nameService.loadDisplayNames = async function(resModel, resIds) {
            const startTime = performance.now();
            const requestId = `${Date.now()}-${Math.random()}`;

            profiler.recordRequestStart(requestId, resModel, resIds);

            try {
                const result = await originalLoad.call(this, resModel, resIds);
                const endTime = performance.now();

                profiler.recordRequestEnd(requestId, endTime - startTime, true);
                return result;
            } catch (error) {
                const endTime = performance.now();
                profiler.recordRequestEnd(requestId, endTime - startTime, false);
                throw error;
            }
        };

        return nameService;
    }

    recordRequestStart(requestId, resModel, resIds) {
        this.metrics.requests.push({
            id: requestId,
            model: resModel,
            idsCount: resIds.length,
            startTime: performance.now(),
            endTime: null,
            duration: null,
            success: null
        });
    }

    recordRequestEnd(requestId, duration, success) {
        const request = this.metrics.requests.find(r => r.id === requestId);
        if (request) {
            request.endTime = performance.now();
            request.duration = duration;
            request.success = success;

            this.updateStats(request);
        }
    }

    updateStats(request) {
        // 更新响应时间统计
        const stats = this.metrics.responseTimeStats;
        stats.min = Math.min(stats.min, request.duration);
        stats.max = Math.max(stats.max, request.duration);
        stats.total += request.duration;

        const completedRequests = this.metrics.requests.filter(r => r.duration !== null);
        stats.avg = stats.total / completedRequests.length;

        // 更新批次统计
        this.metrics.batchStats.count++;
        this.metrics.batchStats.totalSize += request.idsCount;
        this.metrics.batchStats.avgSize = this.metrics.batchStats.totalSize / this.metrics.batchStats.count;
    }

    generateReport() {
        const completedRequests = this.metrics.requests.filter(r => r.duration !== null);
        const successfulRequests = completedRequests.filter(r => r.success);

        return {
            summary: {
                totalRequests: completedRequests.length,
                successRate: (successfulRequests.length / completedRequests.length) * 100,
                avgResponseTime: this.metrics.responseTimeStats.avg,
                avgBatchSize: this.metrics.batchStats.avgSize
            },
            responseTime: this.metrics.responseTimeStats,
            batchStats: this.metrics.batchStats,
            modelBreakdown: this.getModelBreakdown(),
            timeline: this.getTimeline()
        };
    }

    getModelBreakdown() {
        const breakdown = {};
        this.metrics.requests.forEach(request => {
            if (!breakdown[request.model]) {
                breakdown[request.model] = { count: 0, totalIds: 0, avgDuration: 0 };
            }
            breakdown[request.model].count++;
            breakdown[request.model].totalIds += request.idsCount;
            if (request.duration) {
                breakdown[request.model].avgDuration =
                    (breakdown[request.model].avgDuration + request.duration) / 2;
            }
        });
        return breakdown;
    }

    getTimeline() {
        return this.metrics.requests
            .filter(r => r.duration !== null)
            .map(r => ({
                timestamp: r.startTime,
                model: r.model,
                idsCount: r.idsCount,
                duration: r.duration,
                success: r.success
            }))
            .sort((a, b) => a.timestamp - b.timestamp);
    }
}

// 使用示例
const profiler = new NameServiceProfiler();
const nameService = profiler.wrapNameService(env.services.name);

// 运行一段时间后生成报告
setTimeout(() => {
    console.log('Name Service Performance Report:', profiler.generateReport());
}, 30000);
```

---

**Name Service虽然代码简洁，但它展示了现代Web应用中批量处理、缓存优化和异步编程的最佳实践！** 🚀

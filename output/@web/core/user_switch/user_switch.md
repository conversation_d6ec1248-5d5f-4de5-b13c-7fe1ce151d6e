# UserSwitch - 用户切换组件

## 概述

`user_switch.js` 是 Odoo Web 核心模块的用户切换组件，提供了登录页面的用户快速切换功能。该组件支持最近登录用户显示、头像展示、用户移除、表单切换等功能，具备自动焦点管理、状态持久化、响应式显示等特性，为用户提供了便捷的多用户登录体验，主要应用于登录页面的用户选择和快速登录场景。

## 文件信息
- **路径**: `/web/static/src/core/user_switch/user_switch.js`
- **行数**: 57
- **模块**: `@web/core/user_switch/user_switch`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                          // OWL框架
'@web/core/registry'                 // 注册表系统
'@web/core/user'                     // 用户服务
'@web/core/utils/urls'               // URL工具
```

## 主组件类

### 1. UserSwitch 组件

```javascript
class UserSwitch extends Component {
    static template = "web.login_user_switch";
    static props = {};
}
```

**组件特点**:
- **无属性**: 不需要外部属性，完全基于内部状态
- **登录模板**: 使用专门的登录用户切换模板
- **公共组件**: 注册为公共组件，在登录页面可用

### 2. 组件初始化

```javascript
setup() {
    const users = getLastConnectedUsers();
    this.root = useRef("root");
    this.state = useState({
        users,                                    // 用户列表
        displayUserChoice: users.length > 1,     // 是否显示用户选择
    });
    
    this.form = document.querySelector("form.oe_login_form");
    this.form.classList.toggle("d-none", users.length > 1);
    this.form.querySelector(":placeholder-shown")?.focus();
    
    useEffect(
        (el) => el?.querySelector("button.list-group-item-action")?.focus(),
        () => [this.root.el]
    );
}
```

**初始化功能**:
- **用户加载**: 从本地存储加载最近登录的用户
- **状态管理**: 管理用户列表和显示状态
- **表单控制**: 控制登录表单的显示隐藏
- **焦点管理**: 自动管理焦点到合适的元素
- **响应式显示**: 根据用户数量决定显示模式

## 核心方法

### 1. 表单显示切换

```javascript
toggleFormDisplay() {
    this.state.displayUserChoice = !this.state.displayUserChoice && this.state.users.length;
    this.form.classList.toggle("d-none", this.state.displayUserChoice);
    this.form.querySelector(":placeholder-shown")?.focus();
}
```

**切换功能**:
- **状态切换**: 在用户选择和表单输入间切换
- **条件检查**: 只在有用户时才允许切换
- **样式控制**: 控制表单的显示隐藏
- **焦点管理**: 切换后自动聚焦到合适的输入框

### 2. 头像URL生成

```javascript
getAvatarUrl({ partnerId, partnerWriteDate: unique }) {
    return imageUrl("res.partner", partnerId, "avatar_128", { unique });
}
```

**头像功能**:
- **URL生成**: 生成用户头像的URL
- **缓存控制**: 使用写入日期作为唯一标识避免缓存
- **尺寸规格**: 使用128x128像素的头像尺寸
- **合作伙伴关联**: 基于合作伙伴记录获取头像

### 3. 用户移除

```javascript
remove(deletedUser) {
    this.state.users = this.state.users.filter((user) => user !== deletedUser);
    setLastConnectedUsers(this.state.users);
    if (!this.state.users.length) {
        this.fillForm();
    }
}
```

**移除功能**:
- **列表过滤**: 从用户列表中移除指定用户
- **持久化**: 更新本地存储的用户列表
- **状态检查**: 无用户时自动切换到表单模式
- **界面更新**: 自动更新界面显示

### 4. 表单填充

```javascript
fillForm(login = "") {
    this.form.querySelector("input#login").value = login;
    this.form.querySelector("input#password").value = "";
    this.toggleFormDisplay();
}
```

**填充功能**:
- **登录名设置**: 设置登录输入框的值
- **密码清空**: 清空密码输入框
- **表单显示**: 切换到表单显示模式
- **安全考虑**: 确保密码字段为空

## 组件注册

```javascript
registry.category("public_components").add("web.user_switch", UserSwitch);
```

**注册功能**:
- **公共组件**: 注册为公共组件类别
- **登录页面**: 专门用于登录页面的组件
- **全局可用**: 在公共页面中全局可用

## 使用场景

### 1. 登录页面用户切换

```javascript
// 登录页面用户切换示例
class LoginPageDemo extends Component {
    setup() {
        this.state = useState({
            mockUsers: [
                {
                    id: 1,
                    login: 'admin',
                    name: '管理员',
                    partnerId: 1,
                    partnerWriteDate: '2024-01-01 10:00:00'
                },
                {
                    id: 2,
                    login: 'demo',
                    name: '演示用户',
                    partnerId: 2,
                    partnerWriteDate: '2024-01-02 11:00:00'
                },
                {
                    id: 3,
                    login: 'user1',
                    name: '用户一',
                    partnerId: 3,
                    partnerWriteDate: '2024-01-03 12:00:00'
                }
            ],
            currentMode: 'userChoice', // 'userChoice' or 'form'
            selectedUser: null,
            loginForm: {
                login: '',
                password: ''
            }
        });
    }

    getAvatarUrl(user) {
        // 模拟头像URL生成
        return `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name)}&size=128&background=random`;
    }

    selectUser(user) {
        this.state.selectedUser = user;
        this.state.loginForm.login = user.login;
        this.state.loginForm.password = '';
        this.switchToForm();
    }

    removeUser(userToRemove) {
        if (!confirm(`确定要移除用户 "${userToRemove.name}" 吗？`)) return;
        
        this.state.mockUsers = this.state.mockUsers.filter(user => user.id !== userToRemove.id);
        
        if (this.state.mockUsers.length === 0) {
            this.switchToForm();
        }
        
        console.log('User removed:', userToRemove.name);
    }

    switchToForm() {
        this.state.currentMode = 'form';
    }

    switchToUserChoice() {
        if (this.state.mockUsers.length > 0) {
            this.state.currentMode = 'userChoice';
            this.state.selectedUser = null;
        }
    }

    addNewUser() {
        this.state.loginForm = { login: '', password: '' };
        this.switchToForm();
    }

    handleLogin() {
        if (!this.state.loginForm.login || !this.state.loginForm.password) {
            alert('请输入用户名和密码');
            return;
        }
        
        console.log('Login attempt:', this.state.loginForm);
        alert(`模拟登录: ${this.state.loginForm.login}`);
    }

    render() {
        return xml`
            <div class="login-page-demo">
                <h5>登录页面用户切换演示</h5>
                
                <div class="login-container border rounded p-4" style="max-width: 400px; margin: 0 auto;">
                    <div class="text-center mb-4">
                        <h4>Odoo</h4>
                        <p class="text-muted">登录到您的账户</p>
                    </div>

                    <!-- 用户选择模式 -->
                    <div t-if="state.currentMode === 'userChoice' and state.mockUsers.length > 0">
                        <div class="user-list">
                            <div class="list-group">
                                <t t-foreach="state.mockUsers" t-as="user" t-key="user.id">
                                    <div class="list-group-item list-group-item-action d-flex align-items-center">
                                        <img 
                                            t-att-src="getAvatarUrl(user)"
                                            class="rounded-circle me-3"
                                            style="width: 40px; height: 40px;"
                                            alt="Avatar"
                                        />
                                        <div class="flex-grow-1" t-on-click="() => this.selectUser(user)">
                                            <div class="fw-bold" t-esc="user.name"/>
                                            <small class="text-muted" t-esc="user.login"/>
                                        </div>
                                        <button 
                                            class="btn btn-sm btn-outline-danger"
                                            t-on-click="() => this.removeUser(user)"
                                            title="移除用户"
                                        >
                                            <i class="fa fa-times"/>
                                        </button>
                                    </div>
                                </t>
                            </div>
                        </div>
                        
                        <div class="text-center mt-3">
                            <button 
                                class="btn btn-link"
                                t-on-click="addNewUser"
                            >
                                使用其他账户登录
                            </button>
                        </div>
                    </div>

                    <!-- 表单输入模式 -->
                    <div t-if="state.currentMode === 'form'">
                        <form t-on-submit.prevent="handleLogin">
                            <div class="mb-3">
                                <label class="form-label">用户名</label>
                                <input 
                                    type="text" 
                                    class="form-control"
                                    t-model="state.loginForm.login"
                                    placeholder="输入用户名"
                                    required="true"
                                />
                            </div>
                            <div class="mb-3">
                                <label class="form-label">密码</label>
                                <input 
                                    type="password" 
                                    class="form-control"
                                    t-model="state.loginForm.password"
                                    placeholder="输入密码"
                                    required="true"
                                />
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                登录
                            </button>
                        </form>
                        
                        <div class="text-center mt-3" t-if="state.mockUsers.length > 0">
                            <button 
                                class="btn btn-link"
                                t-on-click="switchToUserChoice"
                            >
                                选择已保存的用户
                            </button>
                        </div>
                    </div>
                </div>

                <div class="demo-info mt-4">
                    <div class="card">
                        <div class="card-header">
                            <h6>演示信息</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>当前模式:</strong> 
                                        <span class="badge bg-secondary">
                                            <t t-esc="state.currentMode === 'userChoice' ? '用户选择' : '表单输入'"/>
                                        </span>
                                    </p>
                                    <p><strong>用户数量:</strong> <t t-esc="state.mockUsers.length"/></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>选中用户:</strong> 
                                        <t t-if="state.selectedUser" t-esc="state.selectedUser.name"/>
                                        <span t-else="" class="text-muted">无</span>
                                    </p>
                                    <p><strong>登录名:</strong> 
                                        <code t-esc="state.loginForm.login || '空'"/>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="instructions mt-3">
                    <div class="alert alert-info">
                        <h6>功能说明</h6>
                        <ul class="mb-0">
                            <li>多个用户时显示用户选择界面</li>
                            <li>点击用户头像快速选择并切换到登录表单</li>
                            <li>可以移除不需要的用户</li>
                            <li>支持手动输入新用户信息</li>
                            <li>自动管理焦点和界面切换</li>
                        </ul>
                    </div>
                </div>
            </div>
        `;
    }
}
```

### 2. 用户管理工具

```javascript
// 用户管理工具
class UserManagementTool extends Component {
    setup() {
        this.state = useState({
            users: [
                { id: 1, login: 'admin', name: '系统管理员', lastLogin: '2024-01-15 09:30:00' },
                { id: 2, login: 'manager', name: '部门经理', lastLogin: '2024-01-14 16:45:00' },
                { id: 3, login: 'employee', name: '普通员工', lastLogin: '2024-01-13 14:20:00' }
            ],
            newUser: {
                login: '',
                name: '',
                password: ''
            },
            editingUser: null,
            showAddForm: false
        });
    }

    addUser() {
        if (!this.state.newUser.login || !this.state.newUser.name) {
            alert('请填写用户名和姓名');
            return;
        }

        const newUser = {
            id: Date.now(),
            login: this.state.newUser.login,
            name: this.state.newUser.name,
            lastLogin: new Date().toLocaleString()
        };

        this.state.users.push(newUser);
        this.state.newUser = { login: '', name: '', password: '' };
        this.state.showAddForm = false;
        
        console.log('User added:', newUser);
    }

    editUser(user) {
        this.state.editingUser = { ...user };
    }

    saveUser() {
        const index = this.state.users.findIndex(u => u.id === this.state.editingUser.id);
        if (index !== -1) {
            this.state.users[index] = { ...this.state.editingUser };
        }
        this.state.editingUser = null;
    }

    deleteUser(userId) {
        if (!confirm('确定要删除这个用户吗？')) return;
        
        this.state.users = this.state.users.filter(u => u.id !== userId);
        if (this.state.editingUser && this.state.editingUser.id === userId) {
            this.state.editingUser = null;
        }
    }

    simulateLogin(user) {
        console.log('Simulating login for:', user.login);
        
        // 更新最后登录时间
        user.lastLogin = new Date().toLocaleString();
        
        alert(`模拟登录成功: ${user.name} (${user.login})`);
    }

    exportUsers() {
        const data = JSON.stringify(this.state.users, null, 2);
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'users.json';
        a.click();
        URL.revokeObjectURL(url);
    }

    render() {
        return xml`
            <div class="user-management-tool">
                <div class="header mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>用户管理工具</h5>
                        <div class="header-actions">
                            <button 
                                class="btn btn-primary me-2"
                                t-on-click="() => this.state.showAddForm = true"
                            >
                                <i class="fa fa-plus"/> 添加用户
                            </button>
                            <button 
                                class="btn btn-outline-secondary"
                                t-on-click="exportUsers"
                            >
                                <i class="fa fa-download"/> 导出
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 添加用户表单 -->
                <div class="add-user-form mb-4" t-if="state.showAddForm">
                    <div class="card">
                        <div class="card-header">
                            <h6>添加新用户</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">用户名</label>
                                    <input 
                                        type="text" 
                                        class="form-control"
                                        t-model="state.newUser.login"
                                        placeholder="输入用户名"
                                    />
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">姓名</label>
                                    <input 
                                        type="text" 
                                        class="form-control"
                                        t-model="state.newUser.name"
                                        placeholder="输入姓名"
                                    />
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">密码</label>
                                    <input 
                                        type="password" 
                                        class="form-control"
                                        t-model="state.newUser.password"
                                        placeholder="输入密码"
                                    />
                                </div>
                            </div>
                            <div class="form-actions">
                                <button 
                                    class="btn btn-success me-2"
                                    t-on-click="addUser"
                                >
                                    保存
                                </button>
                                <button 
                                    class="btn btn-secondary"
                                    t-on-click="() => this.state.showAddForm = false"
                                >
                                    取消
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户列表 -->
                <div class="users-table">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>用户名</th>
                                    <th>姓名</th>
                                    <th>最后登录</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-foreach="state.users" t-as="user" t-key="user.id">
                                    <tr t-if="!state.editingUser or state.editingUser.id !== user.id">
                                        <td t-esc="user.id"/>
                                        <td><code t-esc="user.login"/></td>
                                        <td t-esc="user.name"/>
                                        <td t-esc="user.lastLogin"/>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button 
                                                    class="btn btn-outline-success"
                                                    t-on-click="() => this.simulateLogin(user)"
                                                    title="模拟登录"
                                                >
                                                    <i class="fa fa-sign-in"/>
                                                </button>
                                                <button 
                                                    class="btn btn-outline-primary"
                                                    t-on-click="() => this.editUser(user)"
                                                    title="编辑"
                                                >
                                                    <i class="fa fa-edit"/>
                                                </button>
                                                <button 
                                                    class="btn btn-outline-danger"
                                                    t-on-click="() => this.deleteUser(user.id)"
                                                    title="删除"
                                                >
                                                    <i class="fa fa-trash"/>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    
                                    <!-- 编辑行 -->
                                    <tr t-if="state.editingUser and state.editingUser.id === user.id">
                                        <td t-esc="user.id"/>
                                        <td>
                                            <input 
                                                type="text" 
                                                class="form-control form-control-sm"
                                                t-model="state.editingUser.login"
                                            />
                                        </td>
                                        <td>
                                            <input 
                                                type="text" 
                                                class="form-control form-control-sm"
                                                t-model="state.editingUser.name"
                                            />
                                        </td>
                                        <td t-esc="user.lastLogin"/>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button 
                                                    class="btn btn-success"
                                                    t-on-click="saveUser"
                                                >
                                                    保存
                                                </button>
                                                <button 
                                                    class="btn btn-secondary"
                                                    t-on-click="() => this.state.editingUser = null"
                                                >
                                                    取消
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </t>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="users-stats mt-4">
                    <div class="card">
                        <div class="card-header">
                            <h6>用户统计</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <h6>总用户数</h6>
                                    <p class="h4 text-primary" t-esc="state.users.length"/>
                                </div>
                                <div class="col-md-4">
                                    <h6>最近登录</h6>
                                    <p t-if="state.users.length">
                                        <t t-esc="state.users.reduce((latest, user) => user.lastLogin > latest.lastLogin ? user : latest).name"/>
                                    </p>
                                    <p t-else="" class="text-muted">无用户</p>
                                </div>
                                <div class="col-md-4">
                                    <h6>编辑状态</h6>
                                    <p>
                                        <span t-if="state.editingUser" class="badge bg-warning">
                                            编辑中: <t t-esc="state.editingUser.name"/>
                                        </span>
                                        <span t-else="" class="badge bg-success">就绪</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 状态持久化
- 本地存储用户信息
- 自动加载历史用户
- 状态同步更新

### 2. 智能界面切换
- 根据用户数量自动切换模式
- 平滑的界面过渡
- 智能的焦点管理

### 3. 用户体验优化
- 头像显示增强识别
- 快速移除不需要的用户
- 自动表单填充

### 4. 安全考虑
- 密码字段自动清空
- 用户移除确认
- 安全的数据处理

## 设计模式

### 1. 状态模式 (State Pattern)
- 用户选择和表单输入的状态切换
- 不同状态下的行为管理

### 2. 观察者模式 (Observer Pattern)
- 用户列表变化的响应
- 界面状态的自动更新

### 3. 策略模式 (Strategy Pattern)
- 不同显示模式的策略
- 焦点管理的策略选择

## 注意事项

1. **数据安全**: 不在客户端存储敏感信息如密码
2. **用户体验**: 提供清晰的状态反馈和操作指引
3. **兼容性**: 确保在不同浏览器中的一致性
4. **性能考虑**: 避免频繁的DOM操作

## 扩展建议

1. **头像上传**: 支持用户自定义头像上传
2. **用户分组**: 按组织或角色分组显示用户
3. **搜索过滤**: 添加用户搜索和过滤功能
4. **最近活动**: 显示用户的最近活动信息
5. **批量操作**: 支持批量管理用户

该用户切换组件为Odoo Web应用提供了便捷的多用户登录功能，通过智能的界面切换和状态管理确保了良好的用户体验和系统安全性。

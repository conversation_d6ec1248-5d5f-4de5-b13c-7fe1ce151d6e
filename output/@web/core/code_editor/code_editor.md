# CodeEditor - 代码编辑器组件

## 概述

`code_editor.js` 是 Odoo Web 核心模块的代码编辑器组件，基于Ace Editor构建了一个功能完整的代码编辑器。该组件支持多种编程语言语法高亮、主题切换、会话管理、只读模式、自动调整大小和防抖处理，为Odoo Web应用提供了专业级的代码编辑功能，广泛应用于模板编辑、脚本编写和配置文件管理等场景。

## 文件信息
- **路径**: `/web/static/src/core/code_editor/code_editor.js`
- **行数**: 186
- **模块**: `@web/core/code_editor/code_editor`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                    // OWL框架
'@web/core/assets'             // 资源加载
'@web/core/utils/timing'       // 时间工具
```

## 核心功能

### 1. 组件属性定义

```javascript
static props = {
    mode: {
        type: String,
        optional: true,
        validate: (mode) => CodeEditor.MODES.includes(mode),
    },
    value: { validate: (v) => typeof v === "string", optional: true },
    readonly: { type: Boolean, optional: true },
    onChange: { type: Function, optional: true },
    onBlur: { type: Function, optional: true },
    class: { type: String, optional: true },
    theme: {
        type: String,
        optional: true,
        validate: (theme) => CodeEditor.THEMES.includes(theme),
    },
    maxLines: { type: Number, optional: true },
    sessionId: { type: [Number, String], optional: true },
};
```

**属性功能**:
- **语言模式**: 支持多种编程语言的语法高亮
- **内容管理**: value控制编辑器内容
- **状态控制**: readonly控制只读模式
- **事件回调**: onChange和onBlur事件处理
- **样式定制**: class和theme支持自定义样式
- **会话管理**: sessionId支持多会话管理
- **行数限制**: maxLines限制最大显示行数

### 2. 支持的模式和主题

```javascript
static MODES = ["javascript", "xml", "qweb", "scss", "python"];
static THEMES = ["", "monokai"];
```

**模式和主题功能**:
- **JavaScript**: JavaScript语法高亮
- **XML**: XML标记语言支持
- **QWeb**: Odoo QWeb模板语法
- **SCSS**: SCSS样式表语法
- **Python**: Python语言语法
- **主题支持**: 默认主题和Monokai暗色主题

### 3. 默认属性

```javascript
static defaultProps = {
    readonly: false,
    value: "",
    onChange: () => {},
    class: "",
    theme: "",
    sessionId: 1,
};
```

**默认属性功能**:
- **可编辑**: 默认为可编辑模式
- **空内容**: 默认空字符串内容
- **空回调**: 提供默认的空回调函数
- **默认会话**: 使用默认会话ID

### 4. 组件初始化

```javascript
setup() {
    this.editorRef = useRef("editorRef");
    this.state = useState({
        activeMode: undefined,
    });

    onWillStart(async () => await loadBundle("web.ace_lib"));
    
    const sessions = {};
    let ignoredAceChange = false;
    // ... 其他初始化逻辑
}
```

**初始化功能**:
- **引用管理**: 创建编辑器元素引用
- **状态管理**: 管理当前活跃的语言模式
- **资源加载**: 异步加载Ace Editor库
- **会话存储**: 管理多个编辑会话
- **变化过滤**: 过滤程序化的变化事件

### 5. 尺寸调整钩子

```javascript
function onResized(ref, callback) {
    const _ref = typeof ref === "string" ? useRef(ref) : ref;
    const resizeObserver = new ResizeObserver(callback);

    useEffect(
        (el) => {
            if (el) {
                resizeObserver.observe(el);
                return () => resizeObserver.unobserve(el);
            }
        },
        () => [_ref.el]
    );

    onWillDestroy(() => {
        resizeObserver.disconnect();
    });
}
```

**尺寸调整功能**:
- **观察器**: 使用ResizeObserver监听尺寸变化
- **自动清理**: 组件销毁时自动断开观察器
- **回调触发**: 尺寸变化时触发回调函数
- **引用支持**: 支持字符串和引用对象

### 6. Ace Editor初始化

```javascript
useEffect(
    (el) => {
        if (!el) {
            return;
        }

        const aceEditor = window.ace.edit(el);
        this.aceEditor = aceEditor;

        this.aceEditor.setOptions({
            maxLines: this.props.maxLines,
            showPrintMargin: false,
            useWorker: false,
        });
        this.aceEditor.$blockScrolling = true;

        this.aceEditor.on("changeMode", () => {
            this.state.activeMode = this.aceEditor.getSession().$modeId.split("/").at(-1);
        });

        // 会话和事件处理
        const session = aceEditor.getSession();
        session.setValue(this.props.value);
        session.on("change", () => {
            if (this.props.onChange && !ignoredAceChange) {
                this.props.onChange(this.aceEditor.getValue());
            }
        });

        return () => {
            aceEditor.destroy();
        };
    },
    () => [this.editorRef.el]
);
```

**Ace Editor功能**:
- **编辑器创建**: 基于DOM元素创建Ace编辑器
- **选项配置**: 配置编辑器的各种选项
- **事件监听**: 监听模式变化和内容变化
- **会话管理**: 管理编辑会话和内容
- **资源清理**: 组件销毁时清理编辑器

### 7. 主题效果

```javascript
useEffect(
    (theme) => this.aceEditor.setTheme(theme ? `ace/theme/${theme}` : ""),
    () => [this.props.theme]
);
```

**主题功能**:
- **动态切换**: 支持运行时主题切换
- **主题路径**: 自动构建Ace主题路径
- **默认主题**: 空字符串使用默认主题

### 8. 只读模式效果

```javascript
useEffect(
    (readonly) => {
        this.aceEditor.setOptions({
            readOnly: readonly,
            highlightActiveLine: !readonly,
            highlightGutterLine: !readonly,
        });

        this.aceEditor.renderer.setOptions({
            displayIndentGuides: !readonly,
            showGutter: !readonly,
        });

        this.aceEditor.renderer.$cursorLayer.element.style.display = readonly
            ? "none"
            : "block";
    },
    () => [this.props.readonly]
);
```

**只读模式功能**:
- **编辑控制**: 控制编辑器的可编辑性
- **视觉反馈**: 调整只读模式下的视觉效果
- **光标隐藏**: 只读模式下隐藏光标
- **行号显示**: 控制行号和缩进指南的显示

### 9. 会话管理效果

```javascript
useEffect(
    (sessionId, mode, value) => {
        let session = sessions[sessionId];
        if (session) {
            if (session.getValue() !== value) {
                ignoredAceChange = true;
                session.setValue(value);
                ignoredAceChange = false;
            }
        } else {
            session = new window.ace.EditSession(value);
            session.setUndoManager(new window.ace.UndoManager());
            session.setOptions({
                useWorker: false,
                tabSize: 2,
                useSoftTabs: true,
            });
            sessions[sessionId] = session;
        }
        session.setMode(mode ? `ace/mode/${mode}` : "");
        this.aceEditor.setSession(session);
    },
    () => [this.props.sessionId, this.props.mode, this.props.value]
);
```

**会话管理功能**:
- **会话复用**: 复用已存在的编辑会话
- **内容同步**: 同步会话内容与属性值
- **撤销管理**: 为每个会话配置撤销管理器
- **模式设置**: 为会话设置语言模式
- **变化过滤**: 过滤程序化的内容变化

### 10. 自动调整大小

```javascript
const debouncedResize = useDebounced(() => {
    if (this.aceEditor) {
        this.aceEditor.resize();
    }
}, 250);

onResized(this.editorRef, debouncedResize);
```

**自动调整功能**:
- **防抖处理**: 使用防抖避免频繁调整
- **尺寸监听**: 监听容器尺寸变化
- **编辑器调整**: 自动调整编辑器尺寸

## 使用场景

### 1. 基础代码编辑

```javascript
// 基础JavaScript代码编辑器
<CodeEditor
    mode="javascript"
    value={this.state.jsCode}
    onChange={(code) => {
        this.state.jsCode = code;
        console.log('代码已更改:', code);
    }}
    theme="monokai"
    maxLines={20}
/>
```

### 2. 模板编辑器

```javascript
// QWeb模板编辑器
<CodeEditor
    mode="qweb"
    value={this.state.templateCode}
    onChange={(template) => {
        this.state.templateCode = template;
        this.validateTemplate(template);
    }}
    onBlur={() => {
        this.saveTemplate();
    }}
    class="template-editor"
/>
```

### 3. 只读代码查看器

```javascript
// 只读代码查看器
<CodeEditor
    mode="python"
    value={this.props.sourceCode}
    readonly={true}
    theme="monokai"
    maxLines={50}
    class="code-viewer"
/>
```

### 4. 多会话编辑器

```javascript
// 多标签页代码编辑器
{this.state.tabs.map(tab => (
    <CodeEditor
        key={tab.id}
        sessionId={tab.id}
        mode={tab.mode}
        value={tab.content}
        onChange={(code) => {
            this.updateTabContent(tab.id, code);
        }}
        class={tab.active ? 'active-tab' : 'hidden-tab'}
    />
))}
```

## 增强示例

```javascript
// 增强的代码编辑器组件
const EnhancedCodeEditor = {
    createAdvancedEditor: () => {
        class AdvancedCodeEditor extends CodeEditor {
            static props = {
                ...CodeEditor.props,
                autoComplete: { type: Boolean, optional: true },
                lineNumbers: { type: Boolean, optional: true },
                wordWrap: { type: Boolean, optional: true },
                fontSize: { type: Number, optional: true },
                tabSize: { type: Number, optional: true },
                enableLinting: { type: Boolean, optional: true },
                enableSnippets: { type: Boolean, optional: true },
                onCursorChange: { type: Function, optional: true },
                onSelectionChange: { type: Function, optional: true },
                customCommands: { type: Array, optional: true }
            };

            static defaultProps = {
                ...CodeEditor.defaultProps,
                autoComplete: true,
                lineNumbers: true,
                wordWrap: false,
                fontSize: 14,
                tabSize: 4,
                enableLinting: true,
                enableSnippets: true,
                customCommands: []
            };

            static EXTENDED_MODES = [
                ...CodeEditor.MODES,
                "html", "css", "json", "yaml", "sql", "php", "java", "cpp"
            ];

            static EXTENDED_THEMES = [
                ...CodeEditor.THEMES,
                "github", "tomorrow", "twilight", "solarized_dark", "solarized_light"
            ];

            setup() {
                super.setup();
                
                // 增强状态
                this.enhancedState = useState({
                    cursorPosition: { row: 0, column: 0 },
                    selectionRange: null,
                    hasErrors: false,
                    errorCount: 0,
                    warningCount: 0,
                    isFullscreen: false
                });

                // 配置选项
                this.config = {
                    enableAdvancedFeatures: true,
                    enableCollaboration: false,
                    enableMinimap: false,
                    enableBracketMatching: true,
                    enableAutoIndent: true
                };

                // 自定义命令
                this.customCommands = new Map();
                
                // 代码片段
                this.snippets = new Map();
                
                // 错误标记
                this.errorMarkers = [];
            }

            // 增强的Ace Editor初始化
            setupAceEditor(aceEditor) {
                // 调用父类设置
                super.setupAceEditor && super.setupAceEditor(aceEditor);

                // 增强配置
                aceEditor.setOptions({
                    enableBasicAutocompletion: this.props.autoComplete,
                    enableLiveAutocompletion: this.props.autoComplete,
                    enableSnippets: this.props.enableSnippets,
                    fontSize: this.props.fontSize,
                    wrap: this.props.wordWrap,
                    showLineNumbers: this.props.lineNumbers,
                    tabSize: this.props.tabSize,
                    enableBracketMatching: this.config.enableBracketMatching
                });

                // 光标位置监听
                aceEditor.on("changeSelection", () => {
                    const cursor = aceEditor.getCursorPosition();
                    const selection = aceEditor.getSelectionRange();
                    
                    this.enhancedState.cursorPosition = cursor;
                    this.enhancedState.selectionRange = selection;
                    
                    if (this.props.onCursorChange) {
                        this.props.onCursorChange(cursor);
                    }
                    
                    if (this.props.onSelectionChange) {
                        this.props.onSelectionChange(selection);
                    }
                });

                // 注册自定义命令
                this.registerCustomCommands(aceEditor);

                // 设置代码片段
                if (this.props.enableSnippets) {
                    this.setupSnippets(aceEditor);
                }

                // 设置代码检查
                if (this.props.enableLinting) {
                    this.setupLinting(aceEditor);
                }
            }

            // 注册自定义命令
            registerCustomCommands(aceEditor) {
                // 全屏切换命令
                aceEditor.commands.addCommand({
                    name: 'toggleFullscreen',
                    bindKey: { win: 'F11', mac: 'F11' },
                    exec: () => {
                        this.toggleFullscreen();
                    }
                });

                // 格式化代码命令
                aceEditor.commands.addCommand({
                    name: 'formatCode',
                    bindKey: { win: 'Ctrl-Shift-F', mac: 'Cmd-Shift-F' },
                    exec: () => {
                        this.formatCode();
                    }
                });

                // 注册用户自定义命令
                this.props.customCommands.forEach(command => {
                    aceEditor.commands.addCommand(command);
                });
            }

            // 设置代码片段
            setupSnippets(aceEditor) {
                const snippetManager = window.ace.require("ace/snippets").snippetManager;
                
                // 添加自定义代码片段
                const customSnippets = this.getCustomSnippets();
                customSnippets.forEach(snippet => {
                    snippetManager.register(snippet, this.props.mode);
                });
            }

            // 获取自定义代码片段
            getCustomSnippets() {
                const snippets = [];
                
                if (this.props.mode === 'javascript') {
                    snippets.push({
                        name: 'function',
                        content: 'function ${1:name}(${2:params}) {\n\t${3:// body}\n}',
                        tabTrigger: 'func'
                    });
                    
                    snippets.push({
                        name: 'console.log',
                        content: 'console.log(${1:message});',
                        tabTrigger: 'log'
                    });
                }
                
                if (this.props.mode === 'python') {
                    snippets.push({
                        name: 'def',
                        content: 'def ${1:name}(${2:params}):\n\t${3:pass}',
                        tabTrigger: 'def'
                    });
                    
                    snippets.push({
                        name: 'class',
                        content: 'class ${1:ClassName}:\n\tdef __init__(self${2:, params}):\n\t\t${3:pass}',
                        tabTrigger: 'class'
                    });
                }
                
                return snippets;
            }

            // 设置代码检查
            setupLinting(aceEditor) {
                const session = aceEditor.getSession();
                
                // 启用语法检查
                session.setUseWorker(true);
                
                // 监听注释变化
                session.on("changeAnnotation", () => {
                    const annotations = session.getAnnotations();
                    this.updateErrorState(annotations);
                });
            }

            // 更新错误状态
            updateErrorState(annotations) {
                let errorCount = 0;
                let warningCount = 0;
                
                annotations.forEach(annotation => {
                    if (annotation.type === 'error') {
                        errorCount++;
                    } else if (annotation.type === 'warning') {
                        warningCount++;
                    }
                });
                
                this.enhancedState.errorCount = errorCount;
                this.enhancedState.warningCount = warningCount;
                this.enhancedState.hasErrors = errorCount > 0;
            }

            // 格式化代码
            formatCode() {
                if (!this.aceEditor) return;
                
                const session = this.aceEditor.getSession();
                const code = session.getValue();
                
                try {
                    let formattedCode;
                    
                    switch (this.props.mode) {
                        case 'javascript':
                            formattedCode = this.formatJavaScript(code);
                            break;
                        case 'json':
                            formattedCode = JSON.stringify(JSON.parse(code), null, 2);
                            break;
                        case 'xml':
                        case 'html':
                            formattedCode = this.formatXML(code);
                            break;
                        default:
                            console.warn(`Formatting not supported for mode: ${this.props.mode}`);
                            return;
                    }
                    
                    session.setValue(formattedCode);
                } catch (error) {
                    console.error('Code formatting failed:', error);
                }
            }

            // 格式化JavaScript
            formatJavaScript(code) {
                // 简单的JavaScript格式化
                return code
                    .replace(/;/g, ';\n')
                    .replace(/{/g, '{\n')
                    .replace(/}/g, '\n}')
                    .split('\n')
                    .map(line => line.trim())
                    .filter(line => line.length > 0)
                    .join('\n');
            }

            // 格式化XML
            formatXML(code) {
                // 简单的XML格式化
                const formatted = code
                    .replace(/></g, '>\n<')
                    .split('\n')
                    .map(line => line.trim())
                    .filter(line => line.length > 0);
                
                let indent = 0;
                return formatted.map(line => {
                    if (line.startsWith('</')) {
                        indent--;
                    }
                    
                    const indentedLine = '  '.repeat(Math.max(0, indent)) + line;
                    
                    if (line.startsWith('<') && !line.startsWith('</') && !line.endsWith('/>')) {
                        indent++;
                    }
                    
                    return indentedLine;
                }).join('\n');
            }

            // 切换全屏模式
            toggleFullscreen() {
                this.enhancedState.isFullscreen = !this.enhancedState.isFullscreen;
                
                const container = this.editorRef.el.parentElement;
                
                if (this.enhancedState.isFullscreen) {
                    container.classList.add('fullscreen-editor');
                    document.body.style.overflow = 'hidden';
                } else {
                    container.classList.remove('fullscreen-editor');
                    document.body.style.overflow = '';
                }
                
                // 调整编辑器大小
                setTimeout(() => {
                    this.aceEditor.resize();
                }, 100);
            }

            // 跳转到行
            goToLine(lineNumber) {
                if (this.aceEditor) {
                    this.aceEditor.gotoLine(lineNumber);
                    this.aceEditor.focus();
                }
            }

            // 查找和替换
            findAndReplace(searchText, replaceText, options = {}) {
                if (!this.aceEditor) return;
                
                const { 
                    caseSensitive = false, 
                    wholeWord = false, 
                    regExp = false,
                    replaceAll = false 
                } = options;
                
                this.aceEditor.find(searchText, {
                    backwards: false,
                    wrap: true,
                    caseSensitive,
                    wholeWord,
                    regExp
                });
                
                if (replaceText !== undefined) {
                    if (replaceAll) {
                        this.aceEditor.replaceAll(replaceText);
                    } else {
                        this.aceEditor.replace(replaceText);
                    }
                }
            }

            // 获取编辑器统计信息
            getStatistics() {
                if (!this.aceEditor) return null;
                
                const session = this.aceEditor.getSession();
                const code = session.getValue();
                
                return {
                    lines: session.getLength(),
                    characters: code.length,
                    charactersNoSpaces: code.replace(/\s/g, '').length,
                    words: code.split(/\s+/).filter(word => word.length > 0).length,
                    cursorPosition: this.enhancedState.cursorPosition,
                    hasSelection: !this.aceEditor.getSelectionRange().isEmpty(),
                    errorCount: this.enhancedState.errorCount,
                    warningCount: this.enhancedState.warningCount
                };
            }

            // 设置字体大小
            setFontSize(size) {
                if (this.aceEditor) {
                    this.aceEditor.setFontSize(size);
                }
            }

            // 设置主题
            setTheme(theme) {
                if (this.aceEditor) {
                    this.aceEditor.setTheme(`ace/theme/${theme}`);
                }
            }

            // 组件销毁时清理
            willDestroy() {
                // 退出全屏模式
                if (this.enhancedState.isFullscreen) {
                    document.body.style.overflow = '';
                }
                
                // 清理错误标记
                this.errorMarkers.forEach(marker => {
                    this.aceEditor.getSession().removeMarker(marker);
                });
                
                super.willDestroy && super.willDestroy();
            }
        }

        return AdvancedCodeEditor;
    }
};

// 使用示例
const AdvancedCodeEditor = EnhancedCodeEditor.createAdvancedEditor();

<AdvancedCodeEditor
    mode="javascript"
    value={this.state.code}
    theme="monokai"
    autoComplete={true}
    enableLinting={true}
    enableSnippets={true}
    fontSize={16}
    tabSize={2}
    wordWrap={true}
    onChange={(code) => {
        this.state.code = code;
    }}
    onCursorChange={(position) => {
        console.log('光标位置:', position);
    }}
    customCommands={[
        {
            name: 'saveFile',
            bindKey: { win: 'Ctrl-S', mac: 'Cmd-S' },
            exec: () => this.saveFile()
        }
    ]}
/>
```

## 技术特点

### 1. Ace Editor集成
- 完整的Ace Editor功能
- 多语言语法高亮
- 主题系统支持

### 2. 会话管理
- 多会话支持
- 会话状态保持
- 撤销/重做管理

### 3. 响应式设计
- 自动尺寸调整
- 防抖优化
- 容器适配

### 4. 事件处理
- 完整的事件回调
- 变化过滤机制
- 焦点管理

## 设计模式

### 1. 组件模式 (Component Pattern)
- 封装代码编辑器逻辑
- 可重用的编辑器组件

### 2. 观察者模式 (Observer Pattern)
- 内容变化监听
- 尺寸变化响应

### 3. 策略模式 (Strategy Pattern)
- 不同语言模式的处理策略
- 主题切换策略

## 注意事项

1. **资源加载**: 确保Ace Editor库正确加载
2. **内存管理**: 及时清理编辑器实例
3. **性能优化**: 使用防抖处理频繁操作
4. **会话管理**: 正确管理多个编辑会话

## 扩展建议

1. **协作编辑**: 支持多人协作编辑
2. **代码折叠**: 添加代码折叠功能
3. **智能提示**: 增强代码自动完成
4. **插件系统**: 支持插件扩展
5. **版本控制**: 集成版本控制功能

该代码编辑器组件为Odoo Web应用提供了专业级的代码编辑功能，通过Ace Editor的强大能力和完善的会话管理确保了优秀的编辑体验。

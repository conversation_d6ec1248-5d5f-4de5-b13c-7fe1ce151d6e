# @web/core/errors/error_handlers.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/errors/error_handlers.js`
- **原始路径**: `/web/static/src/core/errors/error_handlers.js`
- **代码行数**: 165行
- **作用**: 实现错误处理器系统，负责捕获、分类和处理各种类型的错误

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 错误处理器系统的架构设计和实现
- 不同类型错误的分类处理策略
- 错误恢复和重连机制的实现
- 错误处理的优先级和序列管理
- 企业级错误处理的最佳实践

## 📚 核心概念

### 什么是错误处理器系统？
错误处理器系统是一个**分层错误处理框架**，主要功能：
- **错误捕获**: 捕获各种类型的运行时错误
- **错误分类**: 根据错误类型选择合适的处理策略
- **错误恢复**: 实现自动恢复和重连机制
- **用户通知**: 向用户展示适当的错误信息

### 核心架构组成
```javascript
// 错误处理器注册表
const errorHandlerRegistry = registry.category("error_handlers");
const errorDialogRegistry = registry.category("error_dialogs");
const errorNotificationRegistry = registry.category("error_notifications");

// 错误处理器类型
const errorHandlers = {
    rpcErrorHandler,           // RPC错误处理器
    lostConnectionHandler,     // 连接丢失处理器
    defaultHandler            // 默认错误处理器
};

// 错误对话框映射
const defaultDialogs = new Map([
    [UncaughtClientError, ClientErrorDialog],
    [UncaughtPromiseError, ClientErrorDialog],
    [ThirdPartyScriptError, NetworkErrorDialog],
]);

// 处理器执行顺序
const handlerSequence = {
    rpcErrorHandler: 97,       // 高优先级
    lostConnectionHandler: 98, // 更高优先级
    defaultHandler: 100        // 最低优先级（兜底）
};
```

### 基本使用模式
```javascript
import { errorHandlerRegistry } from '@web/core/registry';

// 注册自定义错误处理器
errorHandlerRegistry.add("customErrorHandler", (env, error, originalError) => {
    if (error instanceof CustomError) {
        // 处理自定义错误
        env.services.notification.add("Custom error occurred");
        return true; // 表示已处理
    }
    return false; // 表示未处理，继续下一个处理器
}, { sequence: 95 });

// 错误处理器函数签名
function errorHandler(env, error, originalError) {
    // env: 环境对象，包含服务
    // error: 包装后的错误对象
    // originalError: 原始错误对象
    
    if (canHandle(error)) {
        handleError(env, error);
        return true; // 已处理
    }
    return false; // 未处理
}
```

## 🔍 核心实现详解

### 1. RPC错误处理器

#### 错误识别和分类
```javascript
function rpcErrorHandler(env, error, originalError) {
    if (!(error instanceof UncaughtPromiseError)) {
        return false;
    }
    if (originalError instanceof RPCError) {
        // 处理RPC错误
        error.unhandledRejectionEvent.preventDefault();
        const exceptionName = originalError.exceptionName;
        let ErrorComponent = originalError.Component;
        
        // 查找对应的错误组件
        if (!ErrorComponent && exceptionName) {
            if (errorNotificationRegistry.contains(exceptionName)) {
                const notif = errorNotificationRegistry.get(exceptionName);
                env.services.notification.add(notif.message || originalError.data.message, notif);
                return true;
            }
            if (errorDialogRegistry.contains(exceptionName)) {
                ErrorComponent = errorDialogRegistry.get(exceptionName);
            }
        }
        
        // 显示错误对话框
        env.services.dialog.add(ErrorComponent || RPCErrorDialog, {
            traceback: error.traceback,
            message: originalError.message,
            name: originalError.name,
            exceptionName: originalError.exceptionName,
            data: originalError.data,
            subType: originalError.subType,
            code: originalError.code,
            type: originalError.type,
            serverHost: error.event?.target?.location.host,
            id: originalError.id,
            model: originalError.model,
        });
        return true;
    }
}
```

**RPC错误处理特点**：
- **类型检查**: 严格检查错误类型确保正确处理
- **事件阻止**: 阻止默认的未处理Promise拒绝事件
- **组件查找**: 按优先级查找合适的错误显示组件
- **回退机制**: 提供默认的RPCErrorDialog作为回退

#### 异常名称映射机制
```javascript
if (!ErrorComponent && exceptionName) {
    if (errorNotificationRegistry.contains(exceptionName)) {
        const notif = errorNotificationRegistry.get(exceptionName);
        env.services.notification.add(notif.message || originalError.data.message, notif);
        return true;
    }
    if (errorDialogRegistry.contains(exceptionName)) {
        ErrorComponent = errorDialogRegistry.get(exceptionName);
    }
}
```

**映射策略**：
- **通知优先**: 首先检查是否应该显示通知而非对话框
- **注册表查找**: 使用注册表模式查找对应的组件
- **灵活配置**: 后端开发者可以通过异常名称控制前端显示

### 2. 连接丢失处理器

#### 连接状态管理
```javascript
let connectionLostNotifRemove = null;

function lostConnectionHandler(env, error, originalError) {
    if (!(error instanceof UncaughtPromiseError)) {
        return false;
    }
    if (originalError instanceof ConnectionLostError) {
        if (connectionLostNotifRemove) {
            // 避免重复显示通知
            return true;
        }
        
        connectionLostNotifRemove = env.services.notification.add(
            _t("Connection lost. Trying to reconnect..."),
            { sticky: true }
        );
        
        // 启动重连机制
        let delay = 2000;
        browser.setTimeout(function checkConnection() {
            rpc("/web/webclient/version_info", {})
                .then(function () {
                    // 连接恢复
                    if (connectionLostNotifRemove) {
                        connectionLostNotifRemove();
                        connectionLostNotifRemove = null;
                    }
                    env.services.notification.add(_t("Connection restored. You are back online."), {
                        type: "info",
                    });
                })
                .catch(() => {
                    // 指数退避重试
                    delay = delay * 1.5 + 500 * Math.random();
                    browser.setTimeout(checkConnection, delay);
                });
        }, delay);
        return true;
    }
}
```

**连接处理特点**：
- **状态跟踪**: 使用全局变量跟踪连接状态
- **重复检查**: 避免多个并发RPC导致的重复通知
- **指数退避**: 使用指数退避算法避免频繁重试
- **随机抖动**: 添加随机延迟避免雷群效应

#### 重连策略实现
```javascript
// 指数退避重试
delay = delay * 1.5 + 500 * Math.random();
browser.setTimeout(checkConnection, delay);
```

**重连策略特点**：
- **指数增长**: 延迟时间按1.5倍递增
- **随机抖动**: 添加0-500ms的随机延迟
- **递归重试**: 使用递归函数实现持续重试
- **成功恢复**: 连接恢复后清理状态和通知

### 3. 默认错误处理器

#### 通用错误分类
```javascript
const defaultDialogs = new Map([
    [UncaughtClientError, ClientErrorDialog],
    [UncaughtPromiseError, ClientErrorDialog],
    [ThirdPartyScriptError, NetworkErrorDialog],
]);

function defaultHandler(env, error) {
    const DialogComponent = defaultDialogs.get(error.constructor) || ErrorDialog;
    env.services.dialog.add(DialogComponent, {
        traceback: error.traceback,
        message: error.message,
        name: error.name,
        serverHost: error.event?.target?.location.host,
    });
    return true;
}
```

**默认处理特点**：
- **兜底机制**: 作为最后的错误处理器确保所有错误都被处理
- **类型映射**: 根据错误构造函数选择对应的对话框
- **通用回退**: 使用通用ErrorDialog作为最终回退
- **简化信息**: 只传递必要的错误信息

### 4. 处理器注册和优先级

#### 注册机制
```javascript
errorHandlerRegistry.add("rpcErrorHandler", rpcErrorHandler, { sequence: 97 });
errorHandlerRegistry.add("lostConnectionHandler", lostConnectionHandler, { sequence: 98 });
errorHandlerRegistry.add("defaultHandler", defaultHandler, { sequence: 100 });
```

**注册特点**：
- **序列控制**: 使用sequence控制处理器执行顺序
- **数值越小**: 优先级越高，越早执行
- **链式处理**: 处理器返回false时继续下一个处理器
- **确定性**: 保证处理器按预定顺序执行

## 🎨 实际应用场景

### 1. 高级错误处理系统
```javascript
class AdvancedErrorHandlerSystem {
    constructor() {
        this.handlerRegistry = new Map();
        this.errorMetrics = new Map();
        this.retryStrategies = new Map();
        this.circuitBreakers = new Map();
        this.setupAdvancedHandlers();
        this.setupMetricsCollection();
    }
    
    setupAdvancedHandlers() {
        // API限流错误处理器
        this.registerHandler('apiRateLimitHandler', (env, error, originalError) => {
            if (originalError.status === 429) {
                const retryAfter = originalError.headers?.['retry-after'] || 60;
                
                env.services.notification.add(
                    `API rate limit exceeded. Retrying in ${retryAfter} seconds...`,
                    { type: 'warning', sticky: true }
                );
                
                // 自动重试
                this.scheduleRetry(originalError.request, retryAfter * 1000);
                return true;
            }
            return false;
        }, { sequence: 90 });
        
        // 权限错误处理器
        this.registerHandler('permissionErrorHandler', (env, error, originalError) => {
            if (originalError.status === 403) {
                const resource = originalError.config?.url || 'resource';
                
                env.services.dialog.add(PermissionErrorDialog, {
                    resource,
                    message: 'You do not have permission to access this resource',
                    onRequestAccess: () => this.requestAccess(resource),
                    onContactAdmin: () => this.contactAdmin(resource)
                });
                return true;
            }
            return false;
        }, { sequence: 91 });
        
        // 数据验证错误处理器
        this.registerHandler('validationErrorHandler', (env, error, originalError) => {
            if (originalError.exceptionName === 'odoo.exceptions.ValidationError') {
                const validationErrors = this.parseValidationErrors(originalError.data);
                
                env.services.dialog.add(ValidationErrorDialog, {
                    errors: validationErrors,
                    onFixErrors: () => this.highlightValidationErrors(validationErrors),
                    onResetForm: () => this.resetCurrentForm()
                });
                return true;
            }
            return false;
        }, { sequence: 92 });
        
        // 网络超时处理器
        this.registerHandler('timeoutErrorHandler', (env, error, originalError) => {
            if (originalError.code === 'TIMEOUT' || originalError.message.includes('timeout')) {
                const circuitBreaker = this.getCircuitBreaker(originalError.config?.url);
                
                if (circuitBreaker.shouldBlock()) {
                    env.services.notification.add(
                        'Service temporarily unavailable. Please try again later.',
                        { type: 'error' }
                    );
                    return true;
                }
                
                // 记录失败并可能触发断路器
                circuitBreaker.recordFailure();
                
                env.services.dialog.add(TimeoutErrorDialog, {
                    url: originalError.config?.url,
                    timeout: originalError.config?.timeout,
                    onRetry: () => this.retryRequest(originalError.config),
                    onOfflineMode: () => this.enableOfflineMode()
                });
                return true;
            }
            return false;
        }, { sequence: 93 });
        
        // 内存不足处理器
        this.registerHandler('memoryErrorHandler', (env, error, originalError) => {
            if (originalError.name === 'QuotaExceededError' || 
                originalError.message.includes('memory')) {
                
                // 清理缓存
                this.clearCaches();
                
                env.services.dialog.add(MemoryErrorDialog, {
                    message: 'Application is running low on memory',
                    onClearCache: () => this.clearAllCaches(),
                    onReload: () => window.location.reload(),
                    onOptimize: () => this.optimizeMemoryUsage()
                });
                return true;
            }
            return false;
        }, { sequence: 94 });
    }
    
    registerHandler(name, handler, options = {}) {
        const sequence = options.sequence || 100;
        
        this.handlerRegistry.set(name, {
            handler,
            sequence,
            enabled: true,
            metrics: {
                calls: 0,
                successes: 0,
                failures: 0,
                lastCalled: null
            }
        });
        
        // 注册到全局注册表
        errorHandlerRegistry.add(name, (env, error, originalError) => {
            const handlerInfo = this.handlerRegistry.get(name);
            
            if (!handlerInfo.enabled) {
                return false;
            }
            
            handlerInfo.metrics.calls++;
            handlerInfo.metrics.lastCalled = Date.now();
            
            try {
                const result = handler(env, error, originalError);
                
                if (result) {
                    handlerInfo.metrics.successes++;
                    this.recordErrorMetric(name, error, 'handled');
                } else {
                    this.recordErrorMetric(name, error, 'skipped');
                }
                
                return result;
                
            } catch (handlerError) {
                handlerInfo.metrics.failures++;
                console.error(`Error in handler ${name}:`, handlerError);
                this.recordErrorMetric(name, error, 'failed');
                return false;
            }
        }, { sequence });
    }
    
    setupMetricsCollection() {
        // 错误指标收集
        this.errorMetrics.set('total_errors', 0);
        this.errorMetrics.set('handled_errors', 0);
        this.errorMetrics.set('unhandled_errors', 0);
        this.errorMetrics.set('error_types', new Map());
        this.errorMetrics.set('error_frequency', new Map());
        
        // 定期报告指标
        setInterval(() => {
            this.reportMetrics();
        }, 300000); // 每5分钟报告一次
    }
    
    recordErrorMetric(handlerName, error, status) {
        this.errorMetrics.set('total_errors', this.errorMetrics.get('total_errors') + 1);
        
        if (status === 'handled') {
            this.errorMetrics.set('handled_errors', this.errorMetrics.get('handled_errors') + 1);
        } else if (status === 'failed') {
            this.errorMetrics.set('unhandled_errors', this.errorMetrics.get('unhandled_errors') + 1);
        }
        
        // 记录错误类型
        const errorTypes = this.errorMetrics.get('error_types');
        const errorType = error.constructor.name;
        errorTypes.set(errorType, (errorTypes.get(errorType) || 0) + 1);
        
        // 记录错误频率
        const errorFreq = this.errorMetrics.get('error_frequency');
        const timeSlot = Math.floor(Date.now() / 60000); // 1分钟时间槽
        errorFreq.set(timeSlot, (errorFreq.get(timeSlot) || 0) + 1);
        
        // 清理旧的频率数据
        const cutoff = timeSlot - 60; // 保留1小时的数据
        for (const [slot] of errorFreq.entries()) {
            if (slot < cutoff) {
                errorFreq.delete(slot);
            }
        }
    }
    
    getCircuitBreaker(url) {
        if (!this.circuitBreakers.has(url)) {
            this.circuitBreakers.set(url, new CircuitBreaker({
                failureThreshold: 5,
                resetTimeout: 60000,
                monitoringPeriod: 300000
            }));
        }
        return this.circuitBreakers.get(url);
    }
    
    scheduleRetry(request, delay) {
        setTimeout(() => {
            // 重新发送请求
            this.retryRequest(request);
        }, delay);
    }
    
    retryRequest(config) {
        // 实现请求重试逻辑
        return rpc(config.url, config.params, config.options);
    }
    
    parseValidationErrors(data) {
        // 解析验证错误数据
        const errors = [];
        
        if (data.arguments && data.arguments[0]) {
            const errorData = data.arguments[0];
            
            for (const [field, messages] of Object.entries(errorData)) {
                errors.push({
                    field,
                    messages: Array.isArray(messages) ? messages : [messages]
                });
            }
        }
        
        return errors;
    }
    
    highlightValidationErrors(errors) {
        // 高亮显示验证错误字段
        errors.forEach(error => {
            const field = document.querySelector(`[name="${error.field}"]`);
            if (field) {
                field.classList.add('o_field_invalid');
                
                // 显示错误消息
                const errorMsg = document.createElement('div');
                errorMsg.className = 'o_field_error';
                errorMsg.textContent = error.messages.join(', ');
                field.parentNode.appendChild(errorMsg);
            }
        });
    }
    
    clearCaches() {
        // 清理各种缓存
        if ('caches' in window) {
            caches.keys().then(names => {
                names.forEach(name => {
                    caches.delete(name);
                });
            });
        }
        
        // 清理本地存储
        try {
            localStorage.clear();
            sessionStorage.clear();
        } catch (e) {
            console.warn('Failed to clear storage:', e);
        }
    }
    
    enableOfflineMode() {
        // 启用离线模式
        document.body.classList.add('o_offline_mode');
        
        // 显示离线指示器
        const offlineIndicator = document.createElement('div');
        offlineIndicator.className = 'o_offline_indicator';
        offlineIndicator.textContent = 'Offline Mode';
        document.body.appendChild(offlineIndicator);
    }
    
    reportMetrics() {
        const metrics = {
            total_errors: this.errorMetrics.get('total_errors'),
            handled_errors: this.errorMetrics.get('handled_errors'),
            unhandled_errors: this.errorMetrics.get('unhandled_errors'),
            error_types: Object.fromEntries(this.errorMetrics.get('error_types')),
            handlers: {}
        };
        
        // 收集处理器指标
        for (const [name, info] of this.handlerRegistry.entries()) {
            metrics.handlers[name] = { ...info.metrics };
        }
        
        // 发送指标到监控系统
        this.sendMetrics(metrics);
    }
    
    sendMetrics(metrics) {
        // 发送指标数据
        fetch('/web/error_metrics', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(metrics)
        }).catch(error => {
            console.warn('Failed to send error metrics:', error);
        });
    }
}

// 断路器实现
class CircuitBreaker {
    constructor(options = {}) {
        this.failureThreshold = options.failureThreshold || 5;
        this.resetTimeout = options.resetTimeout || 60000;
        this.monitoringPeriod = options.monitoringPeriod || 300000;
        
        this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
        this.failures = 0;
        this.lastFailureTime = null;
        this.nextAttemptTime = null;
    }
    
    shouldBlock() {
        if (this.state === 'CLOSED') {
            return false;
        }
        
        if (this.state === 'OPEN') {
            if (Date.now() >= this.nextAttemptTime) {
                this.state = 'HALF_OPEN';
                return false;
            }
            return true;
        }
        
        if (this.state === 'HALF_OPEN') {
            return false;
        }
        
        return false;
    }
    
    recordSuccess() {
        this.failures = 0;
        this.state = 'CLOSED';
        this.lastFailureTime = null;
        this.nextAttemptTime = null;
    }
    
    recordFailure() {
        this.failures++;
        this.lastFailureTime = Date.now();
        
        if (this.failures >= this.failureThreshold) {
            this.state = 'OPEN';
            this.nextAttemptTime = Date.now() + this.resetTimeout;
        }
    }
}

// 使用示例
const advancedErrorHandler = new AdvancedErrorHandlerSystem();
```

## 🔧 调试技巧

### 错误处理器状态监控
```javascript
function debugErrorHandlers() {
    console.group('🔧 Error Handlers Debug');

    // 检查注册的错误处理器
    const errorHandlers = registry.category("error_handlers").getEntries();
    console.log('Registered error handlers:', errorHandlers);

    // 检查处理器顺序
    const sortedHandlers = errorHandlers.sort((a, b) => a[1].sequence - b[1].sequence);
    console.log('Handler execution order:', sortedHandlers.map(([name, handler]) => ({
        name,
        sequence: handler.sequence || 100
    })));

    // 模拟错误测试
    console.log('Testing error handling chain...');
    const testError = new Error('Test error for debugging');
    console.log('Test error:', testError);

    console.groupEnd();
}

// 在控制台中调用
debugErrorHandlers();
```

### 连接状态监控
```javascript
function monitorConnectionStatus() {
    let connectionStatus = {
        isOnline: navigator.onLine,
        lastCheck: Date.now(),
        failureCount: 0,
        reconnectAttempts: 0
    };

    // 监听网络状态变化
    window.addEventListener('online', () => {
        connectionStatus.isOnline = true;
        connectionStatus.failureCount = 0;
        console.log('🌐 Connection restored');
    });

    window.addEventListener('offline', () => {
        connectionStatus.isOnline = false;
        console.log('🌐 Connection lost');
    });

    // 定期检查连接状态
    setInterval(() => {
        if (!connectionStatus.isOnline) {
            console.log('🔍 Checking connection status...', connectionStatus);
        }
    }, 5000);

    return connectionStatus;
}

// 启用连接监控
const connectionMonitor = monitorConnectionStatus();
```

## 📊 性能考虑

### 优化策略
1. **处理器序列**: 使用序列号控制处理器执行顺序
2. **短路机制**: 处理器返回true时停止后续处理
3. **异常保护**: 处理器异常不影响其他处理器
4. **指数退避**: 重连使用指数退避避免频繁请求

### 最佳实践
```javascript
// ✅ 好的做法：检查错误类型
if (!(error instanceof UncaughtPromiseError)) {
    return false;
}

// ❌ 不好的做法：不检查类型
// 可能处理不相关的错误

// ✅ 好的做法：避免重复处理
if (connectionLostNotifRemove) {
    return true;
}

// ❌ 不好的做法：重复显示通知
env.services.notification.add(message);

// ✅ 好的做法：指数退避重试
delay = delay * 1.5 + 500 * Math.random();

// ❌ 不好的做法：固定间隔重试
delay = 2000; // 可能导致服务器压力

// ✅ 好的做法：异常保护
try {
    if (handler(env, uncaughtError, originalError)) {
        break;
    }
} catch (e) {
    console.error(`Error in handler ${name}:`, e);
    return;
}

// ❌ 不好的做法：不保护异常
if (handler(env, uncaughtError, originalError)) {
    break;
}
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解错误处理器系统的架构设计和实现
- [ ] 掌握不同类型错误的分类处理策略
- [ ] 理解错误恢复和重连机制的实现
- [ ] 能够管理错误处理的优先级和序列
- [ ] 掌握企业级错误处理的最佳实践
- [ ] 了解错误处理器的调试和监控技术

## 🚀 下一步学习
学完错误处理器后，建议继续学习：
1. **错误服务** (`@web/core/errors/error_service.js`) - 学习错误服务核心
2. **错误对话框** (`@web/core/errors/error_dialogs.js`) - 理解错误展示
3. **RPC服务** (`@web/core/network/`) - 掌握网络通信

## 💡 重要提示
- 错误处理器提供了分层的错误处理策略
- 处理器序列确保了正确的执行顺序
- 连接恢复机制提高了系统的可靠性
- 异常保护确保了系统的稳定性

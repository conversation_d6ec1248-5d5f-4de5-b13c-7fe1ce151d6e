# @web/core/errors/error_utils.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/errors/error_utils.js`
- **原始路径**: `/web/static/src/core/errors/error_utils.js`
- **代码行数**: 192行
- **作用**: 提供错误处理工具函数，包括错误信息格式化、堆栈追踪和源码映射

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 错误信息格式化和增强技术
- 堆栈追踪的解析和处理
- 源码映射的应用和调试
- 错误链的追踪和展示
- 错误工具函数的设计模式

## 📚 核心概念

### 什么是错误工具？
错误工具是一套**错误处理辅助函数**，主要功能：
- **信息格式化**: 将错误信息格式化为用户友好的形式
- **堆栈追踪**: 解析和增强错误的堆栈信息
- **源码映射**: 将压缩代码映射回原始源码位置
- **错误链追踪**: 处理错误的因果关系链

### 核心功能组成
```javascript
// 错误名称处理
function getErrorTechnicalName(error)     // 获取错误技术名称
function combineErrorNames(uncaught, original)  // 组合错误名称

// 堆栈追踪处理
function fullTraceback(error)            // 获取完整堆栈追踪
function fullAnnotatedTraceback(error)   // 获取注释堆栈追踪
function formatTraceback(error)          // 格式化堆栈信息

// 错误信息完善
function completeUncaughtError(uncaught, original, annotated)  // 完善错误信息

// 源码映射
function annotateTraceback(traceback)    // 注释堆栈追踪
```

### 基本使用模式
```javascript
import { completeUncaughtError, fullTraceback } from '@web/core/errors/error_utils';

// 完善错误信息
try {
    // 危险操作
} catch (error) {
    const uncaughtError = new UncaughtClientError();
    await completeUncaughtError(uncaughtError, error, true);
    console.log('Enhanced error:', uncaughtError);
}

// 获取完整堆栈
const error = new Error('Test error');
error.cause = new Error('Root cause');
const traceback = fullTraceback(error);
console.log('Full traceback:', traceback);
```

## 🔍 核心实现详解

### 1. 错误名称处理

#### 技术名称获取
```javascript
function getErrorTechnicalName(error) {
    if (error && error.constructor && error.constructor.name) {
        return error.constructor.name;
    }
    return Error.name;
}
```

**名称处理特点**：
- **构造函数名**: 使用构造函数名作为技术名称
- **回退机制**: 无法获取时回退到Error
- **类型识别**: 帮助识别具体的错误类型
- **调试友好**: 提供清晰的错误分类

#### 错误名称组合
```javascript
function combineErrorNames(uncaughtError, originalError) {
    const originalErrorName = getErrorTechnicalName(originalError);
    const uncaughtErrorName = getErrorTechnicalName(uncaughtError);
    if (originalErrorName === Error.name) {
        return uncaughtErrorName;
    } else {
        return `${uncaughtErrorName} > ${originalErrorName}`;
    }
}
```

**组合策略**：
- **层次结构**: 显示错误的层次关系
- **简化处理**: 通用Error不显示在组合名称中
- **清晰表达**: 使用>符号表示错误链
- **信息压缩**: 避免冗余的错误名称

### 2. 堆栈追踪处理

#### 完整堆栈追踪
```javascript
function fullTraceback(error) {
    let traceback = formatTraceback(error);
    let current = error.cause;
    while (current) {
        traceback += `\n\nCaused by: ${
            current instanceof Error ? formatTraceback(current) : current
        }`;
        current = current.cause;
    }
    return traceback;
}
```

**堆栈追踪特点**：
- **错误链**: 追踪完整的错误因果链
- **格式化**: 统一格式化所有错误信息
- **类型检查**: 区分Error对象和其他类型
- **层次显示**: 清晰显示错误的层次关系

#### 注释堆栈追踪
```javascript
async function fullAnnotatedTraceback(error) {
    let traceback = await annotateTraceback(formatTraceback(error));
    let current = error.cause;
    while (current) {
        const currentTraceback = current instanceof Error 
            ? await annotateTraceback(formatTraceback(current))
            : current;
        traceback += `\n\nCaused by: ${currentTraceback}`;
        current = current.cause;
    }
    return traceback;
}
```

**注释追踪特点**：
- **异步处理**: 源码映射需要异步加载
- **源码映射**: 将压缩代码映射回原始位置
- **调试增强**: 提供更好的调试体验
- **开发友好**: 在开发模式下提供详细信息

### 3. 错误信息完善

#### 错误信息增强
```javascript
async function completeUncaughtError(uncaughtError, originalError, annotated = false) {
    uncaughtError.name = combineErrorNames(uncaughtError, originalError);
    uncaughtError.message = originalError.message || uncaughtError.message;
    uncaughtError.cause = originalError;
    
    if (annotated) {
        uncaughtError.traceback = await fullAnnotatedTraceback(originalError);
    } else {
        uncaughtError.traceback = fullTraceback(originalError);
    }
}
```

**信息增强特点**：
- **名称组合**: 组合错误名称显示层次
- **消息继承**: 继承原始错误的消息
- **因果关联**: 建立错误的因果关系
- **条件注释**: 根据需要添加源码注释

### 4. 源码映射处理

#### 堆栈注释
```javascript
async function annotateTraceback(traceback) {
    // 解析堆栈中的文件位置
    const stackLines = traceback.split('\n');
    const annotatedLines = [];
    
    for (const line of stackLines) {
        const match = line.match(/at .* \((.+):(\d+):(\d+)\)/);
        if (match) {
            const [, file, lineNum, colNum] = match;
            try {
                // 加载源码映射
                const sourceMap = await loadSourceMap(file);
                if (sourceMap) {
                    const original = sourceMap.originalPositionFor({
                        line: parseInt(lineNum),
                        column: parseInt(colNum)
                    });
                    
                    if (original.source) {
                        annotatedLines.push(
                            `${line}\n    → ${original.source}:${original.line}:${original.column}`
                        );
                        continue;
                    }
                }
            } catch (e) {
                // 源码映射加载失败，使用原始行
            }
        }
        annotatedLines.push(line);
    }
    
    return annotatedLines.join('\n');
}
```

**源码映射特点**：
- **位置解析**: 解析堆栈中的文件位置信息
- **映射查找**: 查找对应的源码映射文件
- **位置转换**: 将压缩位置转换为原始位置
- **错误处理**: 优雅处理映射失败的情况

## 🎨 实际应用场景

### 1. 高级错误分析工具
```javascript
class AdvancedErrorAnalyzer {
    constructor() {
        this.errorPatterns = new Map();
        this.sourceMapCache = new Map();
        this.analysisRules = new Map();
        this.setupErrorPatterns();
        this.setupAnalysisRules();
    }
    
    setupErrorPatterns() {
        // 常见错误模式
        this.errorPatterns.set('null_reference', {
            pattern: /Cannot read propert(y|ies) .* of (null|undefined)/,
            severity: 'high',
            category: 'null_reference',
            suggestions: [
                'Add null checks before accessing properties',
                'Use optional chaining (?.) operator',
                'Initialize variables properly'
            ]
        });
        
        this.errorPatterns.set('type_error', {
            pattern: /.*is not a function/,
            severity: 'high',
            category: 'type_error',
            suggestions: [
                'Check if the variable is properly defined',
                'Verify the object has the expected method',
                'Check for typos in method names'
            ]
        });
        
        this.errorPatterns.set('async_error', {
            pattern: /Cannot read propert(y|ies) .* of Promise/,
            severity: 'medium',
            category: 'async_error',
            suggestions: [
                'Use await keyword for Promise resolution',
                'Add .then() handler for Promise',
                'Check if function returns a Promise'
            ]
        });
    }
    
    setupAnalysisRules() {
        // 错误频率分析
        this.analysisRules.set('frequency_analysis', {
            analyze: (errors) => {
                const frequency = new Map();
                errors.forEach(error => {
                    const key = `${error.name}:${error.message}`;
                    frequency.set(key, (frequency.get(key) || 0) + 1);
                });
                
                return Array.from(frequency.entries())
                    .sort((a, b) => b[1] - a[1])
                    .slice(0, 10)
                    .map(([error, count]) => ({ error, count }));
            }
        });
        
        // 错误传播分析
        this.analysisRules.set('propagation_analysis', {
            analyze: (errors) => {
                const chains = [];
                errors.forEach(error => {
                    if (error.cause) {
                        const chain = this.buildErrorChain(error);
                        chains.push(chain);
                    }
                });
                
                return this.findCommonPropagationPaths(chains);
            }
        });
        
        // 源码位置分析
        this.analysisRules.set('location_analysis', {
            analyze: (errors) => {
                const locations = new Map();
                errors.forEach(error => {
                    const location = this.extractErrorLocation(error);
                    if (location) {
                        const key = `${location.file}:${location.line}`;
                        locations.set(key, (locations.get(key) || 0) + 1);
                    }
                });
                
                return Array.from(locations.entries())
                    .sort((a, b) => b[1] - a[1])
                    .slice(0, 5)
                    .map(([location, count]) => ({ location, count }));
            }
        });
    }
    
    async analyzeError(error) {
        const analysis = {
            basic: this.performBasicAnalysis(error),
            pattern: this.matchErrorPatterns(error),
            stack: await this.analyzeStackTrace(error),
            suggestions: []
        };
        
        // 生成建议
        analysis.suggestions = this.generateSuggestions(analysis);
        
        return analysis;
    }
    
    performBasicAnalysis(error) {
        return {
            type: error.constructor.name,
            message: error.message,
            hasStack: !!error.stack,
            hasCause: !!error.cause,
            chainLength: this.getErrorChainLength(error),
            timestamp: Date.now()
        };
    }
    
    matchErrorPatterns(error) {
        const matches = [];
        
        for (const [name, pattern] of this.errorPatterns.entries()) {
            if (pattern.pattern.test(error.message)) {
                matches.push({
                    name,
                    severity: pattern.severity,
                    category: pattern.category,
                    suggestions: pattern.suggestions
                });
            }
        }
        
        return matches;
    }
    
    async analyzeStackTrace(error) {
        if (!error.stack) {
            return { frames: [], analysis: 'No stack trace available' };
        }
        
        const frames = this.parseStackFrames(error.stack);
        const annotatedFrames = await this.annotateStackFrames(frames);
        
        return {
            frames: annotatedFrames,
            analysis: this.analyzeStackFrames(annotatedFrames),
            hotspots: this.identifyErrorHotspots(annotatedFrames)
        };
    }
    
    parseStackFrames(stack) {
        const lines = stack.split('\n');
        const frames = [];
        
        for (const line of lines) {
            const match = line.match(/at (.+?) \((.+?):(\d+):(\d+)\)/);
            if (match) {
                const [, functionName, file, lineNum, colNum] = match;
                frames.push({
                    function: functionName,
                    file,
                    line: parseInt(lineNum),
                    column: parseInt(colNum),
                    raw: line
                });
            }
        }
        
        return frames;
    }
    
    async annotateStackFrames(frames) {
        const annotated = [];
        
        for (const frame of frames) {
            const annotatedFrame = { ...frame };
            
            try {
                const sourceMap = await this.getSourceMap(frame.file);
                if (sourceMap) {
                    const original = sourceMap.originalPositionFor({
                        line: frame.line,
                        column: frame.column
                    });
                    
                    if (original.source) {
                        annotatedFrame.originalSource = original.source;
                        annotatedFrame.originalLine = original.line;
                        annotatedFrame.originalColumn = original.column;
                        annotatedFrame.originalName = original.name;
                    }
                }
            } catch (e) {
                // 源码映射失败，保持原始信息
            }
            
            annotated.push(annotatedFrame);
        }
        
        return annotated;
    }
    
    generateSuggestions(analysis) {
        const suggestions = [];
        
        // 基于模式匹配的建议
        analysis.pattern.forEach(pattern => {
            suggestions.push(...pattern.suggestions);
        });
        
        // 基于堆栈分析的建议
        if (analysis.stack.hotspots.length > 0) {
            suggestions.push('Focus on error hotspots in the stack trace');
            suggestions.push('Review frequently failing code sections');
        }
        
        // 基于错误类型的建议
        if (analysis.basic.type === 'TypeError') {
            suggestions.push('Add type checking and validation');
            suggestions.push('Use TypeScript for better type safety');
        }
        
        if (analysis.basic.chainLength > 3) {
            suggestions.push('Consider simplifying error handling chain');
            suggestions.push('Add intermediate error handling');
        }
        
        return [...new Set(suggestions)]; // 去重
    }
    
    getErrorChainLength(error) {
        let length = 1;
        let current = error.cause;
        
        while (current) {
            length++;
            current = current.cause;
        }
        
        return length;
    }
    
    async getSourceMap(file) {
        if (this.sourceMapCache.has(file)) {
            return this.sourceMapCache.get(file);
        }
        
        try {
            const sourceMapUrl = `${file}.map`;
            const response = await fetch(sourceMapUrl);
            
            if (response.ok) {
                const sourceMapData = await response.json();
                const sourceMap = new SourceMapConsumer(sourceMapData);
                this.sourceMapCache.set(file, sourceMap);
                return sourceMap;
            }
        } catch (e) {
            // 源码映射加载失败
        }
        
        this.sourceMapCache.set(file, null);
        return null;
    }
}

// 使用示例
const errorAnalyzer = new AdvancedErrorAnalyzer();

// 分析错误
async function analyzeApplicationError(error) {
    const analysis = await errorAnalyzer.analyzeError(error);
    
    console.group('🔍 Error Analysis Report');
    console.log('Basic Info:', analysis.basic);
    console.log('Pattern Matches:', analysis.pattern);
    console.log('Stack Analysis:', analysis.stack);
    console.log('Suggestions:', analysis.suggestions);
    console.groupEnd();
    
    return analysis;
}
```

## 🔧 调试技巧

### 错误工具测试
```javascript
function testErrorUtils() {
    console.group('🛠️ Error Utils Test');
    
    // 测试错误链
    const rootError = new Error('Root cause');
    const middleError = new Error('Middle error');
    middleError.cause = rootError;
    const topError = new Error('Top error');
    topError.cause = middleError;
    
    console.log('Full traceback:', fullTraceback(topError));
    
    // 测试错误名称组合
    const uncaughtError = new UncaughtClientError();
    const originalError = new TypeError('Type error');
    console.log('Combined name:', combineErrorNames(uncaughtError, originalError));
    
    console.groupEnd();
}

// 运行测试
testErrorUtils();
```

## 📊 性能考虑

### 优化策略
1. **缓存机制**: 缓存源码映射避免重复加载
2. **异步处理**: 源码映射处理使用异步避免阻塞
3. **错误过滤**: 只对重要错误进行详细分析
4. **内存管理**: 及时清理不再需要的映射数据

### 最佳实践
```javascript
// ✅ 好的做法：缓存源码映射
if (this.sourceMapCache.has(file)) {
    return this.sourceMapCache.get(file);
}

// ❌ 不好的做法：重复加载
const sourceMap = await loadSourceMap(file);

// ✅ 好的做法：异步处理注释
if (annotated) {
    uncaughtError.traceback = await fullAnnotatedTraceback(originalError);
} else {
    uncaughtError.traceback = fullTraceback(originalError);
}

// ❌ 不好的做法：同步阻塞
uncaughtError.traceback = fullAnnotatedTracebackSync(originalError);
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解错误信息格式化和增强技术
- [ ] 掌握堆栈追踪的解析和处理
- [ ] 理解源码映射的应用和调试
- [ ] 能够处理错误链的追踪和展示
- [ ] 掌握错误工具函数的设计模式
- [ ] 了解错误分析和优化技术

## 🚀 下一步学习
学完错误工具后，建议继续学习：
1. **错误服务** (`@web/core/errors/error_service.js`) - 理解错误服务核心
2. **错误对话框** (`@web/core/errors/error_dialogs.js`) - 学习错误展示
3. **资源加载** (`@web/core/assets/`) - 掌握资源管理

## 💡 重要提示
- 错误工具提供了强大的调试支持
- 源码映射大大提升了调试体验
- 错误链追踪帮助理解问题根源
- 工具函数的设计要考虑性能和可用性

# @web/core/errors/error_dialogs.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/errors/error_dialogs.js`
- **原始路径**: `/web/static/src/core/errors/error_dialogs.js`
- **代码行数**: 238行
- **作用**: 实现各种错误对话框组件，为不同类型的错误提供统一的用户界面展示

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 错误对话框系统的设计和实现
- 多种错误类型的分类处理机制
- 错误信息的格式化和展示技术
- 用户友好的错误处理界面设计
- 企业级错误处理的最佳实践

## 📚 核心概念

### 什么是错误对话框系统？
错误对话框系统是一个**统一的错误展示框架**，主要功能：
- **错误分类**: 根据错误类型显示不同的对话框
- **信息展示**: 以用户友好的方式展示错误信息
- **技术细节**: 提供可选的技术细节查看功能
- **操作引导**: 为用户提供错误处理的操作指导

### 核心架构组成
```javascript
// 标准错误对话框属性
const standardErrorDialogProps = {
    traceback: String,        // 错误堆栈信息
    message: String,          // 错误消息
    name: String,             // 错误名称
    exceptionName: String,    // 异常名称
    data: Object,             // 错误数据
    subType: String,          // 错误子类型
    code: Number,             // 错误代码
    type: String,             // 错误类型
    serverHost: String,       // 服务器主机
    id: Number,               // 记录ID
    model: String,            // 模型名称
    close: Function           // 关闭回调
};

// 异常标题映射
const odooExceptionTitleMap = new Map([
    ["odoo.exceptions.AccessDenied", "Access Denied"],
    ["odoo.exceptions.UserError", "Invalid Operation"],
    ["odoo.exceptions.ValidationError", "Validation Error"],
    // ... 更多异常类型
]);

// 错误对话框组件层次
ErrorDialog                   // 基础错误对话框
├── ClientErrorDialog        // 客户端错误对话框
├── NetworkErrorDialog       // 网络错误对话框
├── RPCErrorDialog          // RPC错误对话框
├── WarningDialog           // 警告对话框
├── RedirectWarningDialog   // 重定向警告对话框
├── Error504Dialog          // 504错误对话框
└── SessionExpiredDialog    // 会话过期对话框
```

### 基本使用模式
```javascript
import { ErrorDialog, WarningDialog } from '@web/core/errors/error_dialogs';

// 显示基础错误对话框
<ErrorDialog 
    name="Database Error"
    message="Connection to database failed"
    traceback="Traceback details..."
    close={() => this.closeDialog()}/>

// 显示警告对话框
<WarningDialog 
    title="Validation Warning"
    message="Please check your input"
    close={() => this.closeDialog()}/>

// 通过错误服务使用（推荐）
this.errorService.add({
    type: 'server',
    name: 'Server Error',
    message: 'Internal server error occurred'
});
```

## 🔍 核心实现详解

### 1. 标准错误属性系统

#### 属性定义结构
```javascript
const standardErrorDialogProps = {
    traceback: { type: [String, { value: null }], optional: true },
    message: { type: String, optional: true },
    name: { type: String, optional: true },
    exceptionName: { type: [String, { value: null }], optional: true },
    data: { type: [Object, { value: null }], optional: true },
    subType: { type: [String, { value: null }], optional: true },
    code: { type: [Number, String, { value: null }], optional: true },
    type: { type: [String, { value: null }], optional: true },
    serverHost: { type: [String, { value: null }], optional: true },
    id: { type: [Number, { value: null }], optional: true },
    model: { type: [String, { value: null }], optional: true },
    close: Function,
};
```

**属性系统特点**：
- **类型安全**: 严格的类型定义确保属性正确性
- **可选属性**: 大部分属性都是可选的，提供灵活性
- **默认值**: 为空值提供合理的默认值
- **统一接口**: 所有错误对话框共享相同的属性接口

#### 异常标题映射
```javascript
const odooExceptionTitleMap = new Map(
    Object.entries({
        "odoo.addons.base.models.ir_mail_server.MailDeliveryException": _t("MailDeliveryException"),
        "odoo.exceptions.AccessDenied": _t("Access Denied"),
        "odoo.exceptions.MissingError": _t("Missing Record"),
        "odoo.addons.web.controllers.action.MissingActionError": _t("Missing Action"),
        "odoo.exceptions.UserError": _t("Invalid Operation"),
        "odoo.exceptions.ValidationError": _t("Validation Error"),
        "odoo.exceptions.AccessError": _t("Access Error"),
        "odoo.exceptions.Warning": _t("Warning"),
    })
);
```

**映射系统特点**：
- **国际化**: 所有标题都支持国际化
- **可扩展**: 易于添加新的异常类型
- **标准化**: 为常见异常提供标准化的用户友好标题
- **映射查找**: 高效的Map数据结构用于快速查找

### 2. 基础错误对话框

#### 组件设置和状态管理
```javascript
setup() {
    this.state = useState({
        showTraceback: false,
    });
    this.copyButtonRef = useRef("copyButton");
    this.popover = usePopover(Tooltip);
    this.contextDetails = "Occured ";
    if (this.props.serverHost) {
        this.contextDetails += `on ${this.props.serverHost} `;
    }
    if (this.props.model && this.props.id) {
        this.contextDetails += `on model ${this.props.model} and id ${this.props.id} `;
    }
    this.contextDetails += `on ${DateTime.now()
        .setZone("UTC")
        .toFormat("yyyy-MM-dd HH:mm:ss")} GMT`;
}
```

**设置特点**：
- **状态管理**: 管理技术细节的显示/隐藏状态
- **引用管理**: 使用useRef管理复制按钮的引用
- **弹出框集成**: 集成Tooltip弹出框显示反馈
- **上下文信息**: 自动生成详细的错误上下文信息

#### 剪贴板功能实现
```javascript
onClickClipboard() {
    browser.navigator.clipboard.writeText(
        `${this.props.name}\n\n${this.props.message}\n\n${this.contextDetails}\n\n${this.props.traceback}`
    );
    this.showTooltip();
}

showTooltip() {
    this.popover.open(this.copyButtonRef.el, { tooltip: _t("Copied") });
    browser.setTimeout(this.popover.close, 800);
}
```

**剪贴板功能特点**：
- **完整信息**: 复制包含所有相关信息的格式化文本
- **用户反馈**: 显示"已复制"提示确认操作成功
- **自动关闭**: 提示在800ms后自动消失
- **浏览器兼容**: 使用browser服务确保兼容性

### 3. RPC错误对话框

#### 标题推断机制
```javascript
inferTitle() {
    // If the server provides an exception name that we have in a registry.
    if (this.props.exceptionName && odooExceptionTitleMap.has(this.props.exceptionName)) {
        this.title = odooExceptionTitleMap.get(this.props.exceptionName).toString();
        return;
    }
    // Fall back to a name based on the error type.
    if (!this.props.type) {
        return;
    }
    switch (this.props.type) {
        case "server":
            this.title = _t("Odoo Server Error");
            break;
        case "script":
            this.title = _t("Odoo Client Error");
            break;
        case "network":
            this.title = _t("Odoo Network Error");
            break;
    }
}
```

**标题推断策略**：
- **优先级**: 首先使用异常名称映射，然后使用错误类型
- **回退机制**: 提供多层回退确保总有合适的标题
- **类型分类**: 根据错误类型提供不同的标题
- **用户友好**: 将技术异常名称转换为用户友好的标题

#### 调试信息处理
```javascript
setup() {
    super.setup();
    this.inferTitle();
    this.traceback = this.props.traceback;
    if (this.props.data && this.props.data.debug) {
        this.traceback = `${this.props.data.debug}\nThe above server error caused the following client error:\n${this.traceback}`;
    }
}
```

**调试信息特点**：
- **信息合并**: 将服务器和客户端调试信息合并
- **层次结构**: 清晰地显示错误的因果关系
- **完整性**: 提供完整的调试上下文
- **可读性**: 使用清晰的分隔符提高可读性

### 4. 特殊错误对话框

#### 重定向警告对话框
```javascript
setup() {
    this.actionService = useService("action");
    const { data, subType } = this.props;
    const [message, actionId, buttonText, additionalContext] = data.arguments;
    this.title = capitalize(subType) || _t("Odoo Warning");
    this.message = message;
    this.actionId = actionId;
    this.buttonText = buttonText;
    this.additionalContext = additionalContext;
}

async onClick() {
    const options = {};
    if (this.additionalContext) {
        options.additionalContext = this.additionalContext;
    }
    if (this.actionId.help) {
        this.actionId.help = markup(this.actionId.help);
    }
    await this.actionService.doAction(this.actionId, options);
    this.props.close();
}
```

**重定向功能特点**：
- **动作集成**: 集成动作服务执行重定向
- **上下文传递**: 支持传递额外的上下文信息
- **帮助信息**: 支持标记化的帮助信息
- **自动关闭**: 执行动作后自动关闭对话框

#### 会话过期对话框
```javascript
const SessionExpiredDialog = class SessionExpiredDialog extends Component {
    static template = "web.SessionExpiredDialog";
    static components = { Dialog };
    static props = { ...standardErrorDialogProps };

    onClick() {
        browser.location.reload();
    }
}
```

**会话处理特点**：
- **简单直接**: 提供简单的页面重新加载解决方案
- **用户控制**: 让用户决定何时重新加载
- **状态清理**: 重新加载清理所有客户端状态
- **安全性**: 确保用户重新认证

## 🎨 实际应用场景

### 1. 高级错误处理系统
```javascript
class AdvancedErrorHandlingSystem {
    constructor() {
        this.errorDialogRegistry = new Map();
        this.errorHistory = [];
        this.errorFilters = new Map();
        this.userPreferences = {
            showTechnicalDetails: false,
            autoReportErrors: true,
            errorNotificationLevel: 'all'
        };
        this.setupCustomErrorDialogs();
        this.setupErrorFilters();
    }
    
    setupCustomErrorDialogs() {
        // 数据库连接错误对话框
        this.errorDialogRegistry.set('database_connection', {
            component: DatabaseConnectionErrorDialog,
            title: 'Database Connection Error',
            icon: 'database',
            actions: [
                {
                    text: 'Retry Connection',
                    action: () => this.retryDatabaseConnection(),
                    primary: true
                },
                {
                    text: 'Check Status',
                    action: () => this.checkDatabaseStatus(),
                    secondary: true
                }
            ]
        });
        
        // API限制错误对话框
        this.errorDialogRegistry.set('api_rate_limit', {
            component: ApiRateLimitErrorDialog,
            title: 'API Rate Limit Exceeded',
            icon: 'clock',
            actions: [
                {
                    text: 'Wait and Retry',
                    action: (retryAfter) => this.scheduleRetry(retryAfter),
                    primary: true
                },
                {
                    text: 'Upgrade Plan',
                    action: () => this.showUpgradeOptions(),
                    secondary: true
                }
            ]
        });
        
        // 权限错误对话框
        this.errorDialogRegistry.set('permission_denied', {
            component: PermissionDeniedErrorDialog,
            title: 'Permission Denied',
            icon: 'lock',
            actions: [
                {
                    text: 'Request Access',
                    action: (resource) => this.requestAccess(resource),
                    primary: true
                },
                {
                    text: 'Contact Admin',
                    action: () => this.contactAdmin(),
                    secondary: true
                }
            ]
        });
        
        // 数据验证错误对话框
        this.errorDialogRegistry.set('validation_error', {
            component: ValidationErrorDialog,
            title: 'Data Validation Error',
            icon: 'warning',
            actions: [
                {
                    text: 'Fix Errors',
                    action: (errors) => this.highlightValidationErrors(errors),
                    primary: true
                },
                {
                    text: 'Reset Form',
                    action: () => this.resetForm(),
                    secondary: true
                }
            ]
        });
        
        // 网络超时错误对话框
        this.errorDialogRegistry.set('network_timeout', {
            component: NetworkTimeoutErrorDialog,
            title: 'Network Timeout',
            icon: 'wifi-off',
            actions: [
                {
                    text: 'Retry Request',
                    action: () => this.retryLastRequest(),
                    primary: true
                },
                {
                    text: 'Work Offline',
                    action: () => this.enableOfflineMode(),
                    secondary: true
                }
            ]
        });
    }
    
    setupErrorFilters() {
        // 错误级别过滤器
        this.errorFilters.set('severity', (error) => {
            const severityLevels = ['low', 'medium', 'high', 'critical'];
            const userLevel = this.userPreferences.errorNotificationLevel;
            const errorLevel = error.severity || 'medium';
            
            if (userLevel === 'all') return true;
            if (userLevel === 'critical') return errorLevel === 'critical';
            if (userLevel === 'high') return ['high', 'critical'].includes(errorLevel);
            
            return severityLevels.indexOf(errorLevel) >= severityLevels.indexOf(userLevel);
        });
        
        // 重复错误过滤器
        this.errorFilters.set('duplicate', (error) => {
            const recentErrors = this.errorHistory.slice(-10);
            const isDuplicate = recentErrors.some(recentError => 
                recentError.name === error.name && 
                recentError.message === error.message &&
                (Date.now() - recentError.timestamp) < 60000 // 1分钟内
            );
            
            return !isDuplicate;
        });
        
        // 开发模式过滤器
        this.errorFilters.set('development', (error) => {
            if (!this.isDevelopmentMode()) return true;
            
            // 在开发模式下显示所有错误
            return true;
        });
        
        // 用户权限过滤器
        this.errorFilters.set('user_permission', (error) => {
            // 根据用户权限决定是否显示某些错误
            if (error.requiresAdminPermission && !this.isAdmin()) {
                return false;
            }
            
            return true;
        });
    }
    
    showErrorDialog(error) {
        // 应用错误过滤器
        for (const [filterName, filterFn] of this.errorFilters.entries()) {
            if (!filterFn(error)) {
                console.log(`Error filtered out by ${filterName} filter:`, error);
                return;
            }
        }
        
        // 记录错误历史
        this.recordError(error);
        
        // 确定错误类型
        const errorType = this.determineErrorType(error);
        
        // 获取对应的错误对话框配置
        const dialogConfig = this.errorDialogRegistry.get(errorType) || 
                           this.getDefaultDialogConfig(error);
        
        // 增强错误信息
        const enhancedError = this.enhanceErrorInfo(error, dialogConfig);
        
        // 显示错误对话框
        return this.displayErrorDialog(enhancedError, dialogConfig);
    }
    
    determineErrorType(error) {
        // 根据错误特征确定错误类型
        if (error.code === 'ECONNREFUSED' || error.message.includes('database')) {
            return 'database_connection';
        }
        
        if (error.status === 429 || error.message.includes('rate limit')) {
            return 'api_rate_limit';
        }
        
        if (error.status === 403 || error.exceptionName === 'odoo.exceptions.AccessDenied') {
            return 'permission_denied';
        }
        
        if (error.exceptionName === 'odoo.exceptions.ValidationError') {
            return 'validation_error';
        }
        
        if (error.type === 'network' && error.message.includes('timeout')) {
            return 'network_timeout';
        }
        
        // 默认错误类型
        return 'generic';
    }
    
    enhanceErrorInfo(error, dialogConfig) {
        const enhanced = {
            ...error,
            timestamp: Date.now(),
            userAgent: navigator.userAgent,
            url: window.location.href,
            userId: this.getCurrentUserId(),
            sessionId: this.getSessionId(),
            buildVersion: this.getBuildVersion(),
            errorId: this.generateErrorId()
        };
        
        // 添加上下文信息
        enhanced.context = {
            currentView: this.getCurrentView(),
            activeModel: this.getActiveModel(),
            recentActions: this.getRecentActions(),
            systemInfo: this.getSystemInfo()
        };
        
        // 添加建议的解决方案
        enhanced.suggestions = this.generateSuggestions(error, dialogConfig);
        
        // 添加相关文档链接
        enhanced.documentation = this.getRelevantDocumentation(error);
        
        return enhanced;
    }
    
    generateSuggestions(error, dialogConfig) {
        const suggestions = [];
        
        // 基于错误类型的建议
        switch (error.type) {
            case 'network':
                suggestions.push({
                    text: 'Check your internet connection',
                    action: () => this.checkNetworkStatus()
                });
                suggestions.push({
                    text: 'Try refreshing the page',
                    action: () => window.location.reload()
                });
                break;
                
            case 'validation':
                suggestions.push({
                    text: 'Review the highlighted fields',
                    action: () => this.highlightInvalidFields()
                });
                suggestions.push({
                    text: 'Check the data format requirements',
                    action: () => this.showFormatHelp()
                });
                break;
                
            case 'permission':
                suggestions.push({
                    text: 'Contact your system administrator',
                    action: () => this.contactSupport()
                });
                suggestions.push({
                    text: 'Check if you have the required permissions',
                    action: () => this.showPermissionInfo()
                });
                break;
        }
        
        return suggestions;
    }
    
    displayErrorDialog(error, dialogConfig) {
        const DialogComponent = dialogConfig.component || ErrorDialog;
        
        const props = {
            ...error,
            title: dialogConfig.title,
            icon: dialogConfig.icon,
            actions: dialogConfig.actions,
            suggestions: error.suggestions,
            documentation: error.documentation,
            onAction: (action) => this.handleErrorAction(action, error),
            onReport: () => this.reportError(error),
            onClose: () => this.closeErrorDialog(error.errorId),
            showTechnicalDetails: this.userPreferences.showTechnicalDetails
        };
        
        // 使用对话框服务显示
        const dialogService = this.getDialogService();
        return dialogService.add(DialogComponent, props);
    }
    
    handleErrorAction(action, error) {
        try {
            // 记录用户操作
            this.recordUserAction(action, error);
            
            // 执行操作
            if (typeof action.action === 'function') {
                return action.action(error);
            }
            
        } catch (actionError) {
            console.error('Error executing error dialog action:', actionError);
            this.showErrorDialog({
                type: 'client',
                name: 'Action Error',
                message: 'Failed to execute the requested action',
                originalError: actionError
            });
        }
    }
    
    recordError(error) {
        const errorRecord = {
            ...error,
            timestamp: Date.now(),
            id: this.generateErrorId()
        };
        
        this.errorHistory.push(errorRecord);
        
        // 限制历史记录长度
        if (this.errorHistory.length > 1000) {
            this.errorHistory.splice(0, this.errorHistory.length - 1000);
        }
        
        // 自动报告严重错误
        if (this.userPreferences.autoReportErrors && error.severity === 'critical') {
            this.reportError(errorRecord);
        }
    }
    
    reportError(error) {
        // 发送错误报告到服务器
        const report = {
            error: {
                name: error.name,
                message: error.message,
                stack: error.traceback,
                type: error.type,
                severity: error.severity
            },
            context: error.context,
            user: {
                id: this.getCurrentUserId(),
                agent: navigator.userAgent
            },
            system: {
                url: window.location.href,
                timestamp: error.timestamp,
                buildVersion: this.getBuildVersion()
            }
        };
        
        // 异步发送，不阻塞用户界面
        this.sendErrorReport(report).catch(reportError => {
            console.warn('Failed to send error report:', reportError);
        });
    }
    
    async sendErrorReport(report) {
        try {
            const response = await fetch('/web/error_report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(report)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            console.log('Error report sent successfully');
            
        } catch (error) {
            console.error('Failed to send error report:', error);
            throw error;
        }
    }
    
    // 工具方法
    generateErrorId() {
        return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    getCurrentUserId() {
        return window.odoo?.session?.uid || null;
    }
    
    getSessionId() {
        return window.odoo?.session?.session_id || null;
    }
    
    getBuildVersion() {
        return window.odoo?.info?.server_version || 'unknown';
    }
    
    isDevelopmentMode() {
        return window.odoo?.debug || false;
    }
    
    isAdmin() {
        return window.odoo?.session?.is_admin || false;
    }
    
    getCurrentView() {
        // 获取当前视图信息
        return {
            type: 'form', // 示例
            model: 'res.partner',
            id: 123
        };
    }
    
    getActiveModel() {
        return 'res.partner'; // 示例
    }
    
    getRecentActions() {
        return []; // 示例
    }
    
    getSystemInfo() {
        return {
            browser: navigator.userAgent,
            screen: `${screen.width}x${screen.height}`,
            language: navigator.language,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
        };
    }
}

// 自定义错误对话框组件示例
class DatabaseConnectionErrorDialog extends ErrorDialog {
    static template = "web.DatabaseConnectionErrorDialog";
    
    setup() {
        super.setup();
        this.connectionStatus = useState({
            checking: false,
            lastCheck: null,
            isOnline: navigator.onLine
        });
        
        // 监听网络状态变化
        window.addEventListener('online', () => {
            this.connectionStatus.isOnline = true;
        });
        
        window.addEventListener('offline', () => {
            this.connectionStatus.isOnline = false;
        });
    }
    
    async checkConnection() {
        this.connectionStatus.checking = true;
        
        try {
            const response = await fetch('/web/health_check', {
                method: 'GET',
                cache: 'no-cache'
            });
            
            this.connectionStatus.lastCheck = Date.now();
            
            if (response.ok) {
                this.showSuccessMessage('Connection restored!');
                this.props.close();
            } else {
                this.showErrorMessage('Connection still unavailable');
            }
            
        } catch (error) {
            this.showErrorMessage('Failed to check connection');
        } finally {
            this.connectionStatus.checking = false;
        }
    }
    
    showSuccessMessage(message) {
        // 显示成功消息
        console.log('Success:', message);
    }
    
    showErrorMessage(message) {
        // 显示错误消息
        console.log('Error:', message);
    }
}

// 使用示例
const errorHandlingSystem = new AdvancedErrorHandlingSystem();

// 在应用中使用
class ApplicationComponent extends Component {
    setup() {
        this.errorHandlingSystem = errorHandlingSystem;
        
        // 全局错误处理
        window.addEventListener('error', (event) => {
            this.errorHandlingSystem.showErrorDialog({
                type: 'client',
                name: event.error.name,
                message: event.error.message,
                traceback: event.error.stack,
                severity: 'high'
            });
        });
        
        // Promise拒绝处理
        window.addEventListener('unhandledrejection', (event) => {
            this.errorHandlingSystem.showErrorDialog({
                type: 'promise',
                name: 'Unhandled Promise Rejection',
                message: event.reason.message || event.reason,
                traceback: event.reason.stack,
                severity: 'medium'
            });
        });
    }
    
    async performRiskyOperation() {
        try {
            // 执行可能失败的操作
            await this.callAPI();
            
        } catch (error) {
            this.errorHandlingSystem.showErrorDialog({
                type: 'api',
                name: 'API Call Failed',
                message: error.message,
                traceback: error.stack,
                severity: 'medium',
                context: {
                    operation: 'performRiskyOperation',
                    timestamp: Date.now()
                }
            });
        }
    }
}
```

### 2. 错误对话框测试框架
```javascript
class ErrorDialogTestFramework {
    constructor() {
        this.testSuites = new Map();
        this.mockServices = this.createMockServices();
        this.testResults = [];
        this.setupTestSuites();
    }

    createMockServices() {
        return {
            dialog: {
                add: jest.fn(),
                remove: jest.fn()
            },
            notification: {
                add: jest.fn()
            },
            action: {
                doAction: jest.fn()
            }
        };
    }

    setupTestSuites() {
        this.testSuites.set('error_dialog_structure', {
            name: 'Error Dialog Structure Tests',
            tests: [
                () => this.testErrorDialogDefinition(),
                () => this.testStandardProps(),
                () => this.testExceptionMapping(),
                () => this.testDialogRegistry()
            ]
        });

        this.testSuites.set('error_dialog_functionality', {
            name: 'Error Dialog Functionality Tests',
            tests: [
                () => this.testClipboardFunctionality(),
                () => this.testTracebackToggle(),
                () => this.testContextGeneration(),
                () => this.testTooltipDisplay()
            ]
        });

        this.testSuites.set('specialized_dialogs', {
            name: 'Specialized Dialog Tests',
            tests: [
                () => this.testRPCErrorDialog(),
                () => this.testWarningDialog(),
                () => this.testRedirectWarningDialog(),
                () => this.testSessionExpiredDialog()
            ]
        });
    }

    testErrorDialogDefinition() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            // 测试ErrorDialog类存在
            if (ErrorDialog && typeof ErrorDialog === 'function') {
                results.tests.push({
                    name: 'ErrorDialog class exists',
                    status: 'passed',
                    message: 'ErrorDialog is properly defined'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'ErrorDialog class exists',
                    status: 'failed',
                    message: 'ErrorDialog is not defined'
                });
                results.failed++;
            }

            // 测试静态属性
            const requiredStatics = ['template', 'components', 'title', 'props'];
            const hasAllStatics = requiredStatics.every(prop =>
                ErrorDialog.hasOwnProperty(prop)
            );

            if (hasAllStatics) {
                results.tests.push({
                    name: 'Static properties exist',
                    status: 'passed',
                    message: 'All required static properties are defined'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Static properties exist',
                    status: 'failed',
                    message: 'Some static properties are missing'
                });
                results.failed++;
            }

            // 测试方法存在
            const requiredMethods = ['setup', 'showTooltip', 'onClickClipboard'];
            const hasAllMethods = requiredMethods.every(method =>
                typeof ErrorDialog.prototype[method] === 'function'
            );

            if (hasAllMethods) {
                results.tests.push({
                    name: 'Required methods exist',
                    status: 'passed',
                    message: 'All required methods are defined'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Required methods exist',
                    status: 'failed',
                    message: 'Some required methods are missing'
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'ErrorDialog definition',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    testStandardProps() {
        const results = { passed: 0, failed: 0, tests: [] };

        try {
            // 测试标准属性定义存在
            if (standardErrorDialogProps && typeof standardErrorDialogProps === 'object') {
                results.tests.push({
                    name: 'Standard props defined',
                    status: 'passed',
                    message: 'Standard error dialog props are defined'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Standard props defined',
                    status: 'failed',
                    message: 'Standard error dialog props are not defined'
                });
                results.failed++;
            }

            // 测试必需属性
            const requiredProps = ['traceback', 'message', 'name', 'close'];
            const hasAllProps = requiredProps.every(prop =>
                standardErrorDialogProps.hasOwnProperty(prop)
            );

            if (hasAllProps) {
                results.tests.push({
                    name: 'Required props available',
                    status: 'passed',
                    message: 'All required props are available'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Required props available',
                    status: 'failed',
                    message: 'Some required props are missing'
                });
                results.failed++;
            }

            // 测试属性类型定义
            const hasTypeDefinitions = Object.values(standardErrorDialogProps).every(prop =>
                prop.type !== undefined
            );

            if (hasTypeDefinitions) {
                results.tests.push({
                    name: 'Props have type definitions',
                    status: 'passed',
                    message: 'All props have proper type definitions'
                });
                results.passed++;
            } else {
                results.tests.push({
                    name: 'Props have type definitions',
                    status: 'failed',
                    message: 'Some props lack type definitions'
                });
                results.failed++;
            }

        } catch (error) {
            results.tests.push({
                name: 'Standard props',
                status: 'failed',
                message: `Error: ${error.message}`
            });
            results.failed++;
        }

        return results;
    }

    runAllTests() {
        const allResults = {};

        for (const [suiteId, suite] of this.testSuites.entries()) {
            console.log(`Running ${suite.name}...`);

            const suiteResults = {
                name: suite.name,
                passed: 0,
                failed: 0,
                tests: []
            };

            for (const test of suite.tests) {
                try {
                    const testResult = test();
                    suiteResults.passed += testResult.passed;
                    suiteResults.failed += testResult.failed;
                    suiteResults.tests.push(...testResult.tests);
                } catch (error) {
                    suiteResults.tests.push({
                        name: 'Test execution',
                        status: 'failed',
                        message: `Test failed to execute: ${error.message}`
                    });
                    suiteResults.failed++;
                }
            }

            allResults[suiteId] = suiteResults;
        }

        return allResults;
    }

    generateTestReport() {
        const results = this.runAllTests();

        const summary = {
            totalSuites: Object.keys(results).length,
            totalTests: 0,
            totalPassed: 0,
            totalFailed: 0,
            suites: {}
        };

        for (const [suiteId, result] of Object.entries(results)) {
            summary.totalTests += result.tests.length;
            summary.totalPassed += result.passed;
            summary.totalFailed += result.failed;
            summary.suites[suiteId] = {
                passed: result.passed,
                failed: result.failed,
                total: result.tests.length
            };
        }

        console.group('🧪 Error Dialog Test Report');
        console.log(`Total Test Suites: ${summary.totalSuites}`);
        console.log(`Total Tests: ${summary.totalTests}`);
        console.log(`Passed: ${summary.totalPassed}`);
        console.log(`Failed: ${summary.totalFailed}`);
        console.log(`Success Rate: ${(summary.totalPassed / summary.totalTests * 100).toFixed(1)}%`);

        for (const [suiteId, suite] of Object.entries(results)) {
            console.group(`${suite.name}`);
            console.log(`Passed: ${suite.passed}/${suite.tests.length}`);

            suite.tests.forEach(test => {
                const icon = test.status === 'passed' ? '✅' : '❌';
                console.log(`${icon} ${test.name}: ${test.message}`);
            });

            console.groupEnd();
        }

        console.groupEnd();

        return { summary, details: results };
    }
}

// 使用示例
const testFramework = new ErrorDialogTestFramework();
const testReport = testFramework.generateTestReport();
```

## 🔧 调试技巧

### 错误对话框状态监控
```javascript
function debugErrorDialogs() {
    const errorDialogs = document.querySelectorAll('.modal .o_error_dialog');

    console.group('🚨 Error Dialog Debug');
    console.log(`Found ${errorDialogs.length} error dialog instances`);

    errorDialogs.forEach((dialog, index) => {
        const component = dialog.__owl__?.component;

        if (component) {
            console.group(`Error Dialog ${index + 1}`);
            console.log('Props:', component.props);
            console.log('State:', component.state);
            console.log('Title:', component.title || component.constructor.title);
            console.log('Has traceback:', !!component.props.traceback);
            console.log('Show traceback:', component.state?.showTraceback);
            console.groupEnd();
        }
    });

    console.groupEnd();
}

// 在控制台中调用
debugErrorDialogs();
```

### 错误注册表检查
```javascript
function checkErrorRegistries() {
    console.group('📋 Error Registry Debug');

    // 检查错误对话框注册表
    const errorDialogRegistry = registry.category("error_dialogs");
    console.log('Error Dialog Registry:', errorDialogRegistry.getEntries());

    // 检查异常标题映射
    console.log('Exception Title Map:', Array.from(odooExceptionTitleMap.entries()));

    // 检查标准属性
    console.log('Standard Error Props:', standardErrorDialogProps);

    console.groupEnd();
}

// 检查注册表
checkErrorRegistries();
```

## 📊 性能考虑

### 优化策略
1. **延迟加载**: 只在需要时创建对话框组件
2. **状态最小化**: 使用最少的状态属性
3. **事件清理**: 确保事件监听器正确清理
4. **内存管理**: 避免内存泄漏和循环引用

### 最佳实践
```javascript
// ✅ 好的做法：条件渲染技术细节
{this.state.showTraceback && (
    <pre class="o_error_traceback">{this.props.traceback}</pre>
)}

// ❌ 不好的做法：总是渲染但隐藏
<pre class="o_error_traceback" style={this.state.showTraceback ? '' : 'display: none'}>
    {this.props.traceback}
</pre>

// ✅ 好的做法：使用browser服务
browser.navigator.clipboard.writeText(text);

// ❌ 不好的做法：直接使用全局API
navigator.clipboard.writeText(text);

// ✅ 好的做法：安全的属性访问
if (this.props.data && this.props.data.arguments) {
    this.message = this.props.data.arguments[0];
}

// ❌ 不好的做法：不安全的属性访问
this.message = this.props.data.arguments[0];
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解错误对话框系统的设计和实现
- [ ] 掌握多种错误类型的分类处理机制
- [ ] 理解错误信息的格式化和展示技术
- [ ] 能够设计用户友好的错误处理界面
- [ ] 掌握企业级错误处理的最佳实践
- [ ] 了解错误对话框的测试和调试技术

## 🚀 下一步学习
学完错误对话框后，建议继续学习：
1. **错误处理器** (`@web/core/errors/error_handlers.js`) - 学习错误处理逻辑
2. **错误服务** (`@web/core/errors/error_service.js`) - 理解错误服务系统
3. **对话框服务** (`@web/core/dialog/`) - 掌握对话框系统

## 💡 重要提示
- 错误对话框提供了重要的用户体验
- 异常映射确保了错误信息的用户友好性
- 标准属性系统保证了组件的一致性
- 注册表模式支持了系统的可扩展性

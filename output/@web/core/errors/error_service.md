# @web/core/errors/error_service.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/errors/error_service.js`
- **原始路径**: `/web/static/src/core/errors/error_service.js`
- **代码行数**: 134行
- **作用**: 实现错误服务核心，负责全局错误捕获、分类和分发处理

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 全局错误捕获机制的实现
- 错误分类和包装策略
- 错误处理器链的调度机制
- 第三方脚本错误的处理方式
- 企业级错误服务的架构设计

## 📚 核心概念

### 什么是错误服务？
错误服务是一个**全局错误管理中心**，主要功能：
- **错误捕获**: 捕获所有未处理的JavaScript错误和Promise拒绝
- **错误分类**: 将原始错误包装为标准化的错误对象
- **错误分发**: 将错误分发给相应的错误处理器
- **错误过滤**: 过滤和忽略不重要的错误

### 核心架构组成
```javascript
// 错误类型层次
UncaughtError                    // 基础错误类
├── UncaughtClientError         // 客户端JavaScript错误
├── UncaughtPromiseError        // Promise拒绝错误
└── ThirdPartyScriptError       // 第三方脚本错误

// 错误服务结构
const errorService = {
    start(env) {
        // 设置全局错误监听器
        browser.addEventListener("error", errorHandler);
        browser.addEventListener("unhandledrejection", promiseHandler);
    }
};

// 错误处理流程
1. 捕获原始错误
2. 包装为标准错误对象
3. 完善错误信息
4. 分发给处理器链
5. 记录和报告
```

## 🔍 核心实现详解

### 1. 错误类型定义

#### 基础错误类
```javascript
const UncaughtError = class UncaughtError extends Error {
    constructor(message) {
        super(message);
        this.name = getErrorTechnicalName(this);
        this.traceback = null;
    }
}
```

**基础错误特点**：
- **标准化**: 继承自Error提供标准接口
- **技术名称**: 自动生成技术名称用于分类
- **堆栈信息**: 提供详细的错误堆栈信息
- **可扩展**: 作为基类支持特定错误类型

#### 特定错误类型
```javascript
const UncaughtClientError = class UncaughtClientError extends UncaughtError {
    constructor(message = "Uncaught Javascript Error") {
        super(message);
    }
}

const UncaughtPromiseError = class UncaughtPromiseError extends UncaughtError {
    constructor(message = "Uncaught Promise") {
        super(message);
        this.unhandledRejectionEvent = null;
    }
}

const ThirdPartyScriptError = class ThirdPartyScriptError extends UncaughtError {
    constructor(message = "Third-Party Script Error") {
        super(message);
    }
}
```

**类型分类特点**：
- **客户端错误**: 处理JavaScript运行时错误
- **Promise错误**: 处理未捕获的Promise拒绝
- **第三方错误**: 处理外部脚本错误
- **事件关联**: 保留原始错误事件引用

### 2. 错误处理核心机制

#### 错误处理器调度
```javascript
function handleError(uncaughtError, retry = true) {
    let originalError = uncaughtError;
    while (originalError instanceof Error && "cause" in originalError) {
        originalError = originalError.cause;
    }
    for (const [name, handler] of registry.category("error_handlers").getEntries()) {
        try {
            if (handler(env, uncaughtError, originalError)) {
                break;
            }
        } catch (e) {
            console.error(
                `A crash occured in error handler ${name} while handling ${uncaughtError}:`,
                e
            );
            return;
        }
    }
}
```

**调度机制特点**：
- **原因链**: 追踪错误的原因链找到根本原因
- **处理器链**: 按序列调用注册的错误处理器
- **短路机制**: 处理器返回true时停止后续处理
- **异常保护**: 处理器本身的异常不会影响系统

### 3. JavaScript错误捕获

#### 全局错误监听
```javascript
browser.addEventListener("error", async (ev) => {
    const { colno, error, filename, lineno, message } = ev;
    const errorsToIgnore = [
        "ResizeObserver loop completed with undelivered notifications.",
        "ResizeObserver loop limit exceeded",
    ];
    if (!error && errorsToIgnore.includes(message)) {
        return;
    }
    
    const isRedactedError = !filename && !lineno && !colno;
    const isThirdPartyScriptError =
        isRedactedError ||
        (isBrowserFirefox() && new URL(filename).origin !== window.location.origin);
    
    if (isThirdPartyScriptError && !odoo.debug) {
        return;
    }
    
    let uncaughtError;
    if (isRedactedError) {
        uncaughtError = new ThirdPartyScriptError();
        uncaughtError.traceback = 
            `An error whose details cannot be accessed by the Odoo framework has occurred.\n` +
            `The error probably originates from a JavaScript file served from a different origin.\n` +
            `The full error is available in the browser console.`;
    } else {
        uncaughtError = new UncaughtClientError();
        uncaughtError.event = ev;
        if (error instanceof Error) {
            error.errorEvent = ev;
            const annotated = env.debug && env.debug.includes("assets");
            await completeUncaughtError(uncaughtError, error, annotated);
        }
    }
    uncaughtError.cause = error;
    handleError(uncaughtError);
});
```

**JavaScript错误处理特点**：
- **错误过滤**: 忽略已知的无害错误
- **跨域检测**: 识别第三方脚本错误
- **调试模式**: 在调试模式下显示更多错误
- **信息完善**: 异步完善错误的详细信息

### 4. Promise拒绝捕获

#### 未处理Promise监听
```javascript
browser.addEventListener("unhandledrejection", async (ev) => {
    const error = ev.reason;
    const uncaughtError = new UncaughtPromiseError();
    uncaughtError.unhandledRejectionEvent = ev;
    uncaughtError.event = ev;
    if (error instanceof Error) {
        error.errorEvent = ev;
        const annotated = env.debug && env.debug.includes("assets");
        await completeUncaughtError(uncaughtError, error, annotated);
    }
    uncaughtError.cause = error;
    handleError(uncaughtError);
});
```

**Promise错误处理特点**：
- **拒绝捕获**: 捕获所有未处理的Promise拒绝
- **事件保留**: 保留原始的拒绝事件
- **类型包装**: 包装为UncaughtPromiseError类型
- **信息增强**: 增强错误信息用于调试

## 🎨 实际应用场景

### 1. 企业级错误监控系统
```javascript
class EnterpriseErrorMonitoringSystem {
    constructor() {
        this.errorCollectors = new Map();
        this.errorAggregators = new Map();
        this.alertRules = new Map();
        this.reportingEndpoints = new Map();
        this.setupErrorCollectors();
        this.setupAggregation();
        this.setupAlerting();
    }
    
    setupErrorCollectors() {
        // 性能错误收集器
        this.errorCollectors.set('performance', {
            collect: (error) => {
                if (error.name.includes('Performance') || 
                    error.message.includes('slow') ||
                    error.traceback?.includes('timeout')) {
                    
                    return {
                        type: 'performance',
                        severity: 'medium',
                        metrics: {
                            loadTime: performance.now(),
                            memoryUsage: performance.memory?.usedJSHeapSize,
                            connectionType: navigator.connection?.effectiveType
                        }
                    };
                }
                return null;
            }
        });
        
        // 用户体验错误收集器
        this.errorCollectors.set('user_experience', {
            collect: (error) => {
                if (error instanceof UncaughtClientError) {
                    return {
                        type: 'user_experience',
                        severity: 'high',
                        context: {
                            userAgent: navigator.userAgent,
                            viewport: `${window.innerWidth}x${window.innerHeight}`,
                            currentPage: window.location.pathname,
                            userActions: this.getRecentUserActions()
                        }
                    };
                }
                return null;
            }
        });
        
        // 安全错误收集器
        this.errorCollectors.set('security', {
            collect: (error) => {
                if (error.message.includes('CSP') ||
                    error.message.includes('CORS') ||
                    error instanceof ThirdPartyScriptError) {
                    
                    return {
                        type: 'security',
                        severity: 'critical',
                        securityContext: {
                            origin: window.location.origin,
                            referrer: document.referrer,
                            userAgent: navigator.userAgent,
                            timestamp: Date.now()
                        }
                    };
                }
                return null;
            }
        });
    }
    
    setupAggregation() {
        // 错误聚合器 - 按类型聚合
        this.errorAggregators.set('by_type', {
            window: 300000, // 5分钟窗口
            data: new Map(),
            aggregate: (errorData) => {
                const key = `${errorData.type}_${errorData.severity}`;
                const current = this.errorAggregators.get('by_type').data.get(key) || {
                    count: 0,
                    firstSeen: Date.now(),
                    lastSeen: Date.now(),
                    samples: []
                };
                
                current.count++;
                current.lastSeen = Date.now();
                if (current.samples.length < 5) {
                    current.samples.push(errorData);
                }
                
                this.errorAggregators.get('by_type').data.set(key, current);
            }
        });
        
        // 错误聚合器 - 按用户聚合
        this.errorAggregators.set('by_user', {
            window: 600000, // 10分钟窗口
            data: new Map(),
            aggregate: (errorData) => {
                const userId = this.getCurrentUserId();
                const current = this.errorAggregators.get('by_user').data.get(userId) || {
                    errorCount: 0,
                    errorTypes: new Set(),
                    sessionStart: Date.now()
                };
                
                current.errorCount++;
                current.errorTypes.add(errorData.type);
                
                this.errorAggregators.get('by_user').data.set(userId, current);
            }
        });
    }
    
    setupAlerting() {
        // 高频错误告警
        this.alertRules.set('high_frequency', {
            condition: (aggregatedData) => {
                for (const [key, data] of aggregatedData.entries()) {
                    if (data.count > 10 && (Date.now() - data.firstSeen) < 300000) {
                        return {
                            level: 'warning',
                            message: `High frequency error detected: ${key}`,
                            data: data
                        };
                    }
                }
                return null;
            }
        });
        
        // 严重错误告警
        this.alertRules.set('critical_errors', {
            condition: (errorData) => {
                if (errorData.severity === 'critical') {
                    return {
                        level: 'critical',
                        message: `Critical error detected: ${errorData.type}`,
                        data: errorData
                    };
                }
                return null;
            }
        });
        
        // 用户影响告警
        this.alertRules.set('user_impact', {
            condition: (userAggregatedData) => {
                for (const [userId, data] of userAggregatedData.entries()) {
                    if (data.errorCount > 5 && data.errorTypes.size > 2) {
                        return {
                            level: 'warning',
                            message: `User experiencing multiple error types: ${userId}`,
                            data: data
                        };
                    }
                }
                return null;
            }
        });
    }
    
    processError(uncaughtError, originalError) {
        // 收集错误数据
        const collectedData = [];
        for (const [name, collector] of this.errorCollectors.entries()) {
            const data = collector.collect(uncaughtError);
            if (data) {
                collectedData.push({ ...data, collector: name });
            }
        }
        
        // 聚合错误数据
        collectedData.forEach(data => {
            for (const [name, aggregator] of this.errorAggregators.entries()) {
                aggregator.aggregate(data);
            }
        });
        
        // 检查告警规则
        this.checkAlerts(collectedData);
        
        // 报告错误
        this.reportErrors(collectedData);
    }
    
    checkAlerts(errorDataList) {
        errorDataList.forEach(errorData => {
            for (const [name, rule] of this.alertRules.entries()) {
                const alert = rule.condition(errorData);
                if (alert) {
                    this.sendAlert(alert);
                }
            }
        });
        
        // 检查聚合数据告警
        for (const [name, aggregator] of this.errorAggregators.entries()) {
            for (const [ruleName, rule] of this.alertRules.entries()) {
                const alert = rule.condition(aggregator.data);
                if (alert) {
                    this.sendAlert(alert);
                }
            }
        }
    }
    
    sendAlert(alert) {
        // 发送告警到监控系统
        console.warn(`🚨 Alert [${alert.level}]: ${alert.message}`, alert.data);
        
        // 发送到外部监控系统
        fetch('/web/monitoring/alert', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(alert)
        }).catch(err => console.error('Failed to send alert:', err));
    }
    
    reportErrors(errorDataList) {
        // 批量报告错误
        const report = {
            timestamp: Date.now(),
            session: this.getSessionId(),
            user: this.getCurrentUserId(),
            errors: errorDataList,
            systemInfo: this.getSystemInfo()
        };
        
        // 发送到错误报告服务
        fetch('/web/error_report', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(report)
        }).catch(err => console.error('Failed to report errors:', err));
    }
    
    getRecentUserActions() {
        // 获取最近的用户操作
        return []; // 实现用户操作追踪
    }
    
    getCurrentUserId() {
        return window.odoo?.session?.uid || 'anonymous';
    }
    
    getSessionId() {
        return window.odoo?.session?.session_id || 'unknown';
    }
    
    getSystemInfo() {
        return {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine,
            screen: `${screen.width}x${screen.height}`,
            viewport: `${window.innerWidth}x${window.innerHeight}`,
            memory: performance.memory ? {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit
            } : null
        };
    }
}

// 集成到错误服务
const monitoringSystem = new EnterpriseErrorMonitoringSystem();

// 扩展错误处理器
errorHandlerRegistry.add("monitoringHandler", (env, error, originalError) => {
    monitoringSystem.processError(error, originalError);
    return false; // 不阻止其他处理器
}, { sequence: 1 });
```

## 🔧 调试技巧

### 错误服务状态监控
```javascript
function debugErrorService() {
    console.group('🔧 Error Service Debug');
    
    // 检查错误服务注册
    const errorService = odoo.__DEBUG__.services.error;
    console.log('Error service:', errorService);
    
    // 检查错误处理器
    const errorHandlers = registry.category("error_handlers").getEntries();
    console.log('Registered error handlers:', errorHandlers);
    
    // 模拟错误测试
    console.log('Testing error handling...');
    try {
        throw new Error('Test error for debugging');
    } catch (e) {
        console.log('Test error caught:', e);
    }
    
    console.groupEnd();
}

// 在控制台中调用
debugErrorService();
```

## 📊 性能考虑

### 优化策略
1. **异步处理**: 错误信息完善使用异步处理避免阻塞
2. **错误过滤**: 过滤无关错误减少处理开销
3. **批量报告**: 批量发送错误报告减少网络请求
4. **内存管理**: 及时清理错误数据避免内存泄漏

### 最佳实践
```javascript
// ✅ 好的做法：异步完善错误信息
if (error instanceof Error) {
    const annotated = env.debug && env.debug.includes("assets");
    await completeUncaughtError(uncaughtError, error, annotated);
}

// ❌ 不好的做法：同步处理可能阻塞
completeUncaughtErrorSync(uncaughtError, error);

// ✅ 好的做法：过滤无关错误
if (!error && errorsToIgnore.includes(message)) {
    return;
}

// ❌ 不好的做法：处理所有错误
handleError(uncaughtError);
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解全局错误捕获机制的实现
- [ ] 掌握错误分类和包装策略
- [ ] 理解错误处理器链的调度机制
- [ ] 能够处理第三方脚本错误
- [ ] 掌握企业级错误服务的架构设计
- [ ] 了解错误监控和报告技术

## 🚀 下一步学习
学完错误服务后，建议继续学习：
1. **错误处理器** (`@web/core/errors/error_handlers.js`) - 学习具体错误处理逻辑
2. **错误工具** (`@web/core/errors/error_utils.js`) - 理解错误处理工具
3. **浏览器服务** (`@web/core/browser/`) - 掌握浏览器API封装

## 💡 重要提示
- 错误服务是系统稳定性的基础
- 全局错误捕获确保了错误不会被遗漏
- 错误分类提供了精确的处理策略
- 监控和报告支持了系统的可观测性

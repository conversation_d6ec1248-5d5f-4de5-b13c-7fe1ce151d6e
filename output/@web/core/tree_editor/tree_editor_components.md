# TreeEditorComponents - 树编辑器基础组件

## 概述

`tree_editor_components.js` 是 Odoo Web 核心模块的树编辑器基础组件库，提供了构建条件编辑器所需的各种基础UI组件。该模块包含输入框、选择器、范围编辑器、时间范围编辑器和列表编辑器等组件，为树编辑器提供了统一的用户界面元素，确保了条件编辑过程中各种值类型的正确输入和显示。

## 文件信息
- **路径**: `/web/static/src/core/tree_editor/tree_editor_components.js`
- **行数**: 79
- **模块**: `@web/core/tree_editor/tree_editor_components`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                          // OWL框架
'@web/core/tags_list/tags_list'      // 标签列表组件
'@web/core/l10n/translation'         // 国际化翻译
```

## 基础组件

### 1. Input - 输入框组件

```javascript
class Input extends Component {
    static props = ["value", "update", "startEmpty?"];
    static template = "web.TreeEditor.Input";
}
```

**输入框功能**:
- **值绑定**: 双向绑定输入值
- **更新回调**: 值变化时的更新通知
- **空值控制**: 可选的空值开始状态
- **模板渲染**: 使用专门的输入框模板

**使用场景**:
- 文本字段的值输入
- 数字字段的值输入
- 简单表达式的输入

### 2. Select - 选择器组件

```javascript
class Select extends Component {
    static props = ["value", "update", "options", "addBlankOption?"];
    static template = "web.TreeEditor.Select";

    // 反序列化值
    deserialize(value) {
        return JSON.parse(value);
    }

    // 序列化值
    serialize(value) {
        return JSON.stringify(value);
    }
}
```

**选择器功能**:
- **选项列表**: 支持预定义的选项列表
- **值序列化**: 自动处理复杂值的序列化
- **空选项**: 可选的空白选项支持
- **JSON处理**: 内置JSON序列化和反序列化

**使用场景**:
- 操作符选择
- 枚举值选择
- 布尔值选择
- 预定义选项选择

### 3. Range - 范围编辑器组件

```javascript
class Range extends Component {
    static props = ["value", "update", "editorInfo"];
    static template = "web.TreeEditor.Range";

    // 更新范围值
    update(index, newValue) {
        const result = [...this.props.value];
        result[index] = newValue;
        return this.props.update(result);
    }
}
```

**范围编辑器功能**:
- **双值编辑**: 支持开始和结束值的编辑
- **索引更新**: 通过索引更新特定位置的值
- **数组操作**: 不可变的数组更新操作
- **编辑器信息**: 支持自定义编辑器配置

**使用场景**:
- 数值范围输入(如年龄范围)
- 日期范围选择
- between操作符的值编辑

### 4. Within - 时间范围组件

```javascript
class Within extends Component {
    static props = ["value", "update", "amountEditorInfo", "optionEditorInfo"];
    static template = "web.TreeEditor.Within";
    static components = { Input, Select };
    
    // 时间单位选项
    static options = [
        ["days", _t("days")],
        ["weeks", _t("weeks")],
        ["months", _t("months")],
        ["years", _t("years")],
    ];

    // 更新时间范围值
    update(index, newValue) {
        const result = [...this.props.value];
        result[index] = newValue;
        return this.props.update(result);
    }
}
```

**时间范围功能**:
- **数量输入**: 时间数量的输入
- **单位选择**: 时间单位的选择(天/周/月/年)
- **组合编辑**: 数量和单位的组合编辑
- **国际化**: 时间单位的多语言支持
- **子组件**: 集成Input和Select子组件

**使用场景**:
- "最近X天"类型的条件
- 相对时间范围设置
- within操作符的值编辑

### 5. List - 列表编辑器组件

```javascript
class List extends Component {
    static components = { TagsList };
    static props = ["value", "update", "editorInfo"];
    static template = "web.TreeEditor.List";

    // 生成标签列表
    get tags() {
        const { isSupported, stringify } = this.props.editorInfo;
        return this.props.value.map((val, index) => ({
            text: stringify(val),
            colorIndex: isSupported(val) ? 0 : 2,
            onDelete: () => {
                this.props.update([
                    ...this.props.value.slice(0, index),
                    ...this.props.value.slice(index + 1),
                ]);
            },
        }));
    }

    // 添加新值
    update(newValue) {
        return this.props.update([...this.props.value, newValue]);
    }
}
```

**列表编辑器功能**:
- **标签显示**: 将列表值显示为标签
- **值验证**: 通过isSupported检查值的有效性
- **颜色标识**: 有效值和无效值的颜色区分
- **删除操作**: 支持单个值的删除
- **添加操作**: 支持新值的添加
- **字符串化**: 自定义值的字符串表示

**使用场景**:
- 多值选择(in操作符)
- 标签列表编辑
- 数组值的可视化编辑

## 使用场景

### 1. 基础值编辑器集成

```javascript
// 在条件编辑器中集成基础组件
class ConditionValueEditor extends Component {
    setup() {
        this.state = useState({
            fieldType: 'char',
            operator: '=',
            value: ''
        });
    }

    getEditorComponent() {
        const { fieldType, operator } = this.state;
        
        if (operator === 'between') {
            return Range;
        } else if (operator === 'within') {
            return Within;
        } else if (operator === 'in' || operator === 'not in') {
            return List;
        } else if (fieldType === 'selection') {
            return Select;
        } else {
            return Input;
        }
    }

    getEditorProps() {
        const { fieldType, operator, value } = this.state;
        const EditorComponent = this.getEditorComponent();
        
        const baseProps = {
            value: value,
            update: (newValue) => this.updateValue(newValue)
        };

        if (EditorComponent === Range) {
            return {
                ...baseProps,
                editorInfo: this.getRangeEditorInfo()
            };
        } else if (EditorComponent === Within) {
            return {
                ...baseProps,
                amountEditorInfo: { type: 'number' },
                optionEditorInfo: { options: Within.options }
            };
        } else if (EditorComponent === List) {
            return {
                ...baseProps,
                editorInfo: this.getListEditorInfo()
            };
        } else if (EditorComponent === Select) {
            return {
                ...baseProps,
                options: this.getSelectionOptions(),
                addBlankOption: true
            };
        } else {
            return {
                ...baseProps,
                startEmpty: false
            };
        }
    }

    updateValue(newValue) {
        this.state.value = newValue;
        this.onValueChanged(newValue);
    }

    render() {
        const EditorComponent = this.getEditorComponent();
        const editorProps = this.getEditorProps();
        
        return xml`
            <div class="condition-value-editor">
                <EditorComponent t-props="editorProps"/>
            </div>
        `;
    }
}
```

### 2. 自定义范围编辑器

```javascript
// 自定义日期范围编辑器
class DateRangeEditor extends Component {
    setup() {
        this.state = useState({
            dateRange: [null, null],
            format: 'YYYY-MM-DD'
        });
    }

    updateDateRange(index, newDate) {
        const result = [...this.state.dateRange];
        result[index] = newDate;
        this.state.dateRange = result;
        this.onRangeChanged(result);
    }

    onRangeChanged(range) {
        console.log('Date range changed:', range);
        // 验证日期范围
        if (range[0] && range[1] && range[0] > range[1]) {
            this.notification.add('开始日期不能晚于结束日期', { type: 'warning' });
        }
    }

    render() {
        return xml`
            <div class="date-range-editor">
                <label class="form-label">日期范围</label>
                <Range
                    value="state.dateRange"
                    update="updateDateRange"
                    editorInfo="{
                        startLabel: '开始日期',
                        endLabel: '结束日期',
                        inputType: 'date'
                    }"
                />
            </div>
        `;
    }
}
```

### 3. 智能列表编辑器

```javascript
// 智能列表编辑器，支持多种值类型
class SmartListEditor extends Component {
    setup() {
        this.state = useState({
            values: [],
            inputMode: 'text', // 'text', 'number', 'record'
            suggestions: []
        });
    }

    getListEditorInfo() {
        return {
            isSupported: (val) => {
                if (this.state.inputMode === 'number') {
                    return !isNaN(val) && isFinite(val);
                } else if (this.state.inputMode === 'record') {
                    return Number.isInteger(val) && val > 0;
                }
                return typeof val === 'string' && val.length > 0;
            },
            
            stringify: (val) => {
                if (this.state.inputMode === 'record') {
                    return `Record #${val}`;
                }
                return String(val);
            }
        };
    }

    updateValues(newValues) {
        this.state.values = newValues;
        this.validateValues(newValues);
    }

    validateValues(values) {
        const { isSupported } = this.getListEditorInfo();
        const invalidValues = values.filter(val => !isSupported(val));
        
        if (invalidValues.length > 0) {
            this.notification.add(
                `发现 ${invalidValues.length} 个无效值`,
                { type: 'warning' }
            );
        }
    }

    addValue(newValue) {
        if (newValue && !this.state.values.includes(newValue)) {
            this.updateValues([...this.state.values, newValue]);
        }
    }

    loadSuggestions() {
        // 根据输入模式加载建议值
        if (this.state.inputMode === 'record') {
            this.state.suggestions = [1, 2, 3, 4, 5]; // 示例记录ID
        } else if (this.state.inputMode === 'text') {
            this.state.suggestions = ['选项1', '选项2', '选项3'];
        } else {
            this.state.suggestions = [10, 20, 30, 40, 50];
        }
    }

    render() {
        return xml`
            <div class="smart-list-editor">
                <div class="input-mode-selector mb-2">
                    <div class="btn-group" role="group">
                        <input 
                            type="radio" 
                            class="btn-check" 
                            name="inputMode" 
                            id="textMode"
                            value="text"
                            t-model="state.inputMode"
                        />
                        <label class="btn btn-outline-primary" for="textMode">文本</label>
                        
                        <input 
                            type="radio" 
                            class="btn-check" 
                            name="inputMode" 
                            id="numberMode"
                            value="number"
                            t-model="state.inputMode"
                        />
                        <label class="btn btn-outline-primary" for="numberMode">数字</label>
                        
                        <input 
                            type="radio" 
                            class="btn-check" 
                            name="inputMode" 
                            id="recordMode"
                            value="record"
                            t-model="state.inputMode"
                        />
                        <label class="btn btn-outline-primary" for="recordMode">记录</label>
                    </div>
                </div>

                <List
                    value="state.values"
                    update="updateValues"
                    editorInfo="getListEditorInfo()"
                />

                <div class="suggestions mt-2" t-if="state.suggestions.length">
                    <small class="text-muted">建议值:</small>
                    <div class="d-flex flex-wrap gap-1 mt-1">
                        <t t-foreach="state.suggestions" t-as="suggestion" t-key="suggestion">
                            <button 
                                class="btn btn-sm btn-outline-secondary"
                                t-on-click="() => this.addValue(suggestion)"
                            >
                                <t t-esc="suggestion"/>
                            </button>
                        </t>
                    </div>
                </div>
            </div>
        `;
    }
}
```

### 4. 时间范围预设编辑器

```javascript
// 带预设选项的时间范围编辑器
class TimeRangePresetEditor extends Component {
    setup() {
        this.state = useState({
            timeRange: [1, 'days'],
            presets: [
                { label: '今天', value: [0, 'days'] },
                { label: '最近7天', value: [7, 'days'] },
                { label: '最近30天', value: [30, 'days'] },
                { label: '最近3个月', value: [3, 'months'] },
                { label: '最近1年', value: [1, 'years'] }
            ]
        });
    }

    updateTimeRange(index, newValue) {
        const result = [...this.state.timeRange];
        result[index] = newValue;
        this.state.timeRange = result;
        this.onTimeRangeChanged(result);
    }

    onTimeRangeChanged(range) {
        console.log('Time range changed:', range);
        // 生成人类可读的描述
        const description = this.getTimeRangeDescription(range);
        console.log('Description:', description);
    }

    getTimeRangeDescription(range) {
        const [amount, unit] = range;
        if (amount === 0) {
            return '今天';
        }
        
        const unitLabels = {
            'days': '天',
            'weeks': '周',
            'months': '个月',
            'years': '年'
        };
        
        return `最近${amount}${unitLabels[unit]}`;
    }

    applyPreset(preset) {
        this.state.timeRange = [...preset.value];
        this.onTimeRangeChanged(this.state.timeRange);
    }

    render() {
        return xml`
            <div class="time-range-preset-editor">
                <div class="presets mb-3">
                    <label class="form-label">快速选择:</label>
                    <div class="btn-group" role="group">
                        <t t-foreach="state.presets" t-as="preset" t-key="preset.label">
                            <button 
                                class="btn btn-outline-primary btn-sm"
                                t-on-click="() => this.applyPreset(preset)"
                            >
                                <t t-esc="preset.label"/>
                            </button>
                        </t>
                    </div>
                </div>

                <div class="custom-range">
                    <label class="form-label">自定义范围:</label>
                    <Within
                        value="state.timeRange"
                        update="updateTimeRange"
                        amountEditorInfo="{ type: 'number', min: 0 }"
                        optionEditorInfo="{ options: Within.options }"
                    />
                </div>

                <div class="range-description mt-2">
                    <small class="text-muted">
                        当前设置: <strong t-esc="getTimeRangeDescription(state.timeRange)"/>
                    </small>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 组件化设计
- 高度模块化的组件结构
- 可重用的基础UI元素
- 统一的属性接口

### 2. 值类型支持
- 多种数据类型的编辑支持
- 智能的值验证和转换
- 灵活的序列化机制

### 3. 用户体验
- 直观的视觉反馈
- 智能的默认值处理
- 流畅的交互体验

### 4. 国际化支持
- 完整的多语言支持
- 本地化的时间单位
- 可配置的显示文本

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可重用的UI组件
- 标准化的接口设计

### 2. 策略模式 (Strategy Pattern)
- 不同值类型的编辑策略
- 可配置的验证规则

### 3. 观察者模式 (Observer Pattern)
- 值变化的通知机制
- 父子组件的状态同步

## 注意事项

1. **类型安全**: 确保值的类型正确性
2. **性能优化**: 避免不必要的重新渲染
3. **用户体验**: 提供清晰的操作反馈
4. **数据验证**: 验证用户输入的有效性

## 扩展建议

1. **富文本编辑**: 支持富文本内容的编辑
2. **日期时间**: 专门的日期时间选择器
3. **文件上传**: 支持文件类型的值编辑
4. **颜色选择**: 颜色值的可视化编辑
5. **地理位置**: 地理坐标的编辑支持

该树编辑器基础组件库为Odoo Web应用提供了完整的条件值编辑能力，通过模块化设计和统一接口确保了良好的可维护性和扩展性。

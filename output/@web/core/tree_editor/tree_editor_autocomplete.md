# TreeEditorAutocomplete - 树编辑器自动完成组件

## 概述

`tree_editor_autocomplete.js` 是 Odoo Web 核心模块的树编辑器自动完成组件，提供了在条件编辑中选择记录的自动完成功能。该模块扩展了标准的记录选择器，支持表达式和记录ID的混合处理，为树编辑器中的关联字段值选择提供了专门的用户界面，确保了条件构建过程中记录选择的准确性和用户体验。

## 文件信息
- **路径**: `/web/static/src/core/tree_editor/tree_editor_autocomplete.js`
- **行数**: 82
- **模块**: `@web/core/tree_editor/tree_editor_autocomplete`

## 依赖关系

```javascript
// 核心依赖
'@web/core/record_selectors/multi_record_selector'    // 多记录选择器
'@web/core/l10n/translation'                          // 国际化翻译
'@web/core/py_js/py_utils'                             // Python工具函数
'@web/core/tree_editor/condition_tree'                // 条件树核心
'@web/core/record_selectors/record_selector'          // 单记录选择器
```

## 核心工具函数

### 1. ID验证函数

```javascript
const isId = (val) => Number.isInteger(val) && val >= 1;
```

**ID验证功能**:
- **类型检查**: 验证值是否为整数
- **范围检查**: 确保ID大于等于1
- **有效性**: 判断是否为有效的记录ID
- **过滤**: 用于过滤有效的记录ID

### 2. 格式化函数

```javascript
const getFormat = (val, displayNames) => {
    let text;
    let colorIndex;
    
    if (isId(val)) {
        // 处理有效的记录ID
        text = typeof displayNames[val] === "string"
            ? displayNames[val]
            : _t("Inaccessible/missing record ID: %s", val);
        colorIndex = typeof displayNames[val] === "string" ? 0 : 2; // 0 = grey, 2 = orange
    } else {
        // 处理表达式或无效ID
        text = val instanceof Expression
            ? String(val)
            : _t("Invalid record ID: %s", formatAST(toPyValue(val)));
        colorIndex = val instanceof Expression ? 2 : 1; // 1 = red
    }
    
    return { text, colorIndex };
};
```

**格式化功能**:
- **文本生成**: 根据值类型生成显示文本
- **颜色标识**: 为不同类型的值分配颜色索引
- **错误处理**: 处理无效或无法访问的记录
- **表达式支持**: 支持表达式对象的格式化
- **国际化**: 错误信息支持多语言

**颜色索引含义**:
- **0 (灰色)**: 正常的记录显示名称
- **1 (红色)**: 无效的记录ID
- **2 (橙色)**: 无法访问的记录或表达式

## 组件类

### 1. DomainSelectorAutocomplete - 多记录自动完成

```javascript
class DomainSelectorAutocomplete extends MultiRecordSelector {
    static props = {
        ...MultiRecordSelector.props,
        resIds: true, // resIds可以是ID数组或表达式数组
    };

    // 获取有效的记录ID
    getIds(props = this.props) {
        return props.resIds.filter((val) => isId(val));
    }

    // 生成标签
    getTags(props, displayNames) {
        return props.resIds.map((val, index) => {
            const { text, colorIndex } = getFormat(val, displayNames);
            return {
                text,
                colorIndex,
                onDelete: () => {
                    this.props.update([
                        ...this.props.resIds.slice(0, index),
                        ...this.props.resIds.slice(index + 1),
                    ]);
                },
            };
        });
    }
}
```

**多记录自动完成功能**:
- **混合支持**: 支持记录ID和表达式的混合处理
- **过滤机制**: 自动过滤出有效的记录ID
- **标签生成**: 为每个值生成带颜色的标签
- **删除操作**: 支持单个值的删除操作
- **继承扩展**: 继承并扩展多记录选择器功能

### 2. DomainSelectorSingleAutocomplete - 单记录自动完成

```javascript
class DomainSelectorSingleAutocomplete extends RecordSelector {
    static props = {
        ...RecordSelector.props,
        resId: true, // resId可以是ID或表达式
    };

    // 获取显示名称
    getDisplayName(props = this.props, displayNames) {
        const { resId } = props;
        if (resId === false) {
            return "";
        }
        const { text } = getFormat(resId, displayNames);
        return text;
    }

    // 获取有效的记录ID
    getIds(props = this.props) {
        if (isId(props.resId)) {
            return [props.resId];
        }
        return [];
    }
}
```

**单记录自动完成功能**:
- **单值处理**: 专门处理单个记录或表达式
- **显示名称**: 智能生成显示名称
- **空值处理**: 正确处理空值情况
- **ID提取**: 提取有效的记录ID用于查询
- **继承扩展**: 继承并扩展单记录选择器功能

## 使用场景

### 1. 多记录条件值选择

```javascript
// 在树编辑器中使用多记录自动完成
class MultiRecordConditionEditor extends Component {
    setup() {
        this.state = useState({
            selectedValues: [1, 2, new Expression("partner_id.id")],
            resModel: 'res.partner'
        });
    }

    updateValues(newValues) {
        this.state.selectedValues = newValues;
        this.onValuesChanged(newValues);
    }

    onValuesChanged(values) {
        // 处理值变化
        console.log('Selected values:', values);
        
        // 分离记录ID和表达式
        const recordIds = values.filter(val => isId(val));
        const expressions = values.filter(val => val instanceof Expression);
        
        console.log('Record IDs:', recordIds);
        console.log('Expressions:', expressions);
    }

    render() {
        return xml`
            <div class="multi-record-condition-editor">
                <label class="form-label">选择记录或输入表达式</label>
                <DomainSelectorAutocomplete
                    resIds="state.selectedValues"
                    resModel="state.resModel"
                    update="updateValues"
                    placeholder="搜索记录或输入表达式..."
                />
            </div>
        `;
    }
}
```

### 2. 单记录条件值选择

```javascript
// 在树编辑器中使用单记录自动完成
class SingleRecordConditionEditor extends Component {
    setup() {
        this.state = useState({
            selectedValue: false,
            resModel: 'res.users'
        });
    }

    updateValue(newValue) {
        this.state.selectedValue = newValue;
        this.onValueChanged(newValue);
    }

    onValueChanged(value) {
        // 处理值变化
        if (isId(value)) {
            console.log('Selected record ID:', value);
        } else if (value instanceof Expression) {
            console.log('Selected expression:', value.toString());
        } else {
            console.log('No selection');
        }
    }

    render() {
        return xml`
            <div class="single-record-condition-editor">
                <label class="form-label">选择用户</label>
                <DomainSelectorSingleAutocomplete
                    resId="state.selectedValue"
                    resModel="state.resModel"
                    update="updateValue"
                    placeholder="搜索用户..."
                />
            </div>
        `;
    }
}
```

### 3. 高级条件值编辑器

```javascript
// 高级条件值编辑器，支持多种输入模式
class AdvancedConditionValueEditor extends Component {
    setup() {
        this.state = useState({
            values: [],
            inputMode: 'record', // 'record', 'expression', 'mixed'
            resModel: 'product.product',
            allowExpressions: true
        });
    }

    updateValues(newValues) {
        this.state.values = newValues;
        this.validateValues(newValues);
    }

    validateValues(values) {
        const errors = [];
        
        values.forEach((value, index) => {
            if (!isId(value) && !(value instanceof Expression)) {
                errors.push(`值 ${index + 1} 无效: ${value}`);
            }
        });
        
        if (errors.length > 0) {
            this.notification.add(
                `验证失败: ${errors.join(', ')}`,
                { type: 'warning' }
            );
        }
    }

    addExpression() {
        const expression = prompt('请输入表达式:');
        if (expression) {
            try {
                const expr = new Expression(expression);
                this.state.values = [...this.state.values, expr];
            } catch (error) {
                this.notification.add(
                    `表达式无效: ${error.message}`,
                    { type: 'danger' }
                );
            }
        }
    }

    clearValues() {
        this.state.values = [];
    }

    exportValues() {
        const exportData = {
            values: this.state.values.map(val => {
                if (val instanceof Expression) {
                    return { type: 'expression', value: val.toString() };
                } else {
                    return { type: 'record_id', value: val };
                }
            }),
            resModel: this.state.resModel,
            timestamp: new Date().toISOString()
        };
        
        console.log('Export data:', exportData);
    }

    getValueStatistics() {
        const stats = {
            total: this.state.values.length,
            recordIds: this.state.values.filter(val => isId(val)).length,
            expressions: this.state.values.filter(val => val instanceof Expression).length,
            invalid: this.state.values.filter(val => !isId(val) && !(val instanceof Expression)).length
        };
        
        return stats;
    }

    render() {
        const stats = this.getValueStatistics();
        
        return xml`
            <div class="advanced-condition-value-editor">
                <div class="editor-header mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6>条件值编辑器</h6>
                        <div class="btn-group">
                            <button 
                                class="btn btn-sm btn-outline-primary"
                                t-on-click="addExpression"
                                t-if="state.allowExpressions"
                            >
                                <i class="fa fa-plus"/> 添加表达式
                            </button>
                            <button 
                                class="btn btn-sm btn-outline-secondary"
                                t-on-click="clearValues"
                            >
                                <i class="fa fa-trash"/> 清空
                            </button>
                            <button 
                                class="btn btn-sm btn-outline-info"
                                t-on-click="exportValues"
                            >
                                <i class="fa fa-download"/> 导出
                            </button>
                        </div>
                    </div>
                </div>

                <div class="input-mode-selector mb-3">
                    <div class="btn-group" role="group">
                        <input 
                            type="radio" 
                            class="btn-check" 
                            name="inputMode" 
                            id="recordMode"
                            value="record"
                            t-model="state.inputMode"
                        />
                        <label class="btn btn-outline-primary" for="recordMode">
                            记录选择
                        </label>
                        
                        <input 
                            type="radio" 
                            class="btn-check" 
                            name="inputMode" 
                            id="mixedMode"
                            value="mixed"
                            t-model="state.inputMode"
                        />
                        <label class="btn btn-outline-primary" for="mixedMode">
                            混合模式
                        </label>
                    </div>
                </div>

                <DomainSelectorAutocomplete
                    resIds="state.values"
                    resModel="state.resModel"
                    update="updateValues"
                    placeholder="搜索记录或输入表达式..."
                />

                <div class="value-statistics mt-3">
                    <div class="row">
                        <div class="col-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title">${stats.total}</h5>
                                    <p class="card-text">总计</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-success">${stats.recordIds}</h5>
                                    <p class="card-text">记录ID</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-warning">${stats.expressions}</h5>
                                    <p class="card-text">表达式</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-danger">${stats.invalid}</h5>
                                    <p class="card-text">无效值</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="value-preview mt-3" t-if="state.values.length">
                    <h6>值预览</h6>
                    <div class="list-group">
                        <t t-foreach="state.values" t-as="value" t-key="value_index">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <t t-if="isId(value)">
                                        <span class="badge bg-success me-2">ID</span>
                                        <span t-esc="value"/>
                                    </t>
                                    <t t-elif="value instanceof Expression">
                                        <span class="badge bg-warning me-2">表达式</span>
                                        <code t-esc="value.toString()"/>
                                    </t>
                                    <t t-else="">
                                        <span class="badge bg-danger me-2">无效</span>
                                        <span t-esc="value"/>
                                    </t>
                                </div>
                                <button 
                                    class="btn btn-sm btn-outline-danger"
                                    t-on-click="() => this.removeValue(value_index)"
                                >
                                    <i class="fa fa-times"/>
                                </button>
                            </div>
                        </t>
                    </div>
                </div>
            </div>
        `;
    }

    removeValue(index) {
        const newValues = [...this.state.values];
        newValues.splice(index, 1);
        this.state.values = newValues;
    }
}
```

## 技术特点

### 1. 混合类型支持
- 同时支持记录ID和表达式
- 智能的类型检测和处理
- 灵活的值格式化

### 2. 视觉区分
- 不同类型值的颜色标识
- 清晰的错误状态显示
- 直观的用户反馈

### 3. 继承扩展
- 基于标准记录选择器扩展
- 保持原有功能的完整性
- 添加专门的树编辑器功能

### 4. 错误处理
- 优雅的错误信息显示
- 国际化的错误提示
- 无效值的安全处理

## 设计模式

### 1. 适配器模式 (Adapter Pattern)
- 适配标准记录选择器用于树编辑器
- 统一的接口处理不同类型的值

### 2. 策略模式 (Strategy Pattern)
- 不同值类型的处理策略
- 可配置的格式化选项

### 3. 装饰器模式 (Decorator Pattern)
- 在基础功能上添加树编辑器特性
- 保持原有组件的功能不变

## 注意事项

1. **类型安全**: 确保正确处理不同类型的值
2. **性能优化**: 避免频繁的类型检查和格式化
3. **用户体验**: 提供清晰的视觉反馈
4. **错误处理**: 妥善处理无效值和表达式

## 扩展建议

1. **表达式编辑器**: 专门的表达式编辑界面
2. **值验证**: 更强的值验证和类型检查
3. **批量操作**: 支持批量添加和编辑
4. **模板支持**: 常用表达式的模板管理
5. **智能提示**: 表达式的智能提示和补全

该树编辑器自动完成组件为Odoo Web应用提供了专业的条件值选择能力，通过混合类型支持和智能格式化确保了条件构建的灵活性和准确性。

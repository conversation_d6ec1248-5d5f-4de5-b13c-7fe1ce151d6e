# TreeEditorOperatorEditor - 操作符编辑器

## 概述

`tree_editor_operator_editor.js` 是 Odoo Web 核心模块的操作符编辑器，提供了条件树中操作符的选择、显示和编辑功能。该模块定义了完整的操作符描述映射、标签生成、键值转换和编辑器配置，支持标准操作符、虚拟操作符和自定义操作符，为树编辑器提供了专业的操作符管理能力，确保了条件构建过程中操作符选择的准确性和用户友好性。

## 文件信息
- **路径**: `/web/static/src/core/tree_editor/tree_editor_operator_editor.js`
- **行数**: 140
- **模块**: `@web/core/tree_editor/tree_editor_operator_editor`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'                    // 国际化翻译
'@web/core/tree_editor/condition_tree'          // 条件树核心
'@web/core/utils/strings'                       // 字符串工具
'@web/core/py_js/py'                            // Python表达式解析
'@web/core/tree_editor/tree_editor_components'  // 树编辑器组件
```

## 操作符描述映射

### 1. 标准操作符

```javascript
const OPERATOR_DESCRIPTIONS = {
    // 比较操作符
    "=": "=",
    "!=": "!=", 
    "<=": "<=",
    "<": "<",
    ">": ">",
    ">=": ">=",
    "=?": "=?",
    
    // 字符串操作符
    "=like": _t("=like"),
    "=ilike": _t("=ilike"),
    like: _t("like"),
    "not like": _t("not like"),
    ilike: _t("contains"),
    "not ilike": _t("does not contain"),
    
    // 集合操作符
    in: _t("is in"),
    "not in": _t("is not in"),
    
    // 层级操作符
    child_of: _t("child of"),
    parent_of: _t("parent of"),
};
```

**标准操作符功能**:
- **比较操作**: 数值和日期的比较操作
- **字符串匹配**: 各种字符串匹配模式
- **集合操作**: 成员关系检查
- **层级关系**: 树形结构的父子关系
- **国际化**: 所有描述支持多语言

### 2. 虚拟操作符

```javascript
const OPERATOR_DESCRIPTIONS = {
    // 虚拟操作符 (在某些情况下替换 = 和 !=)
    is: _t("is"),
    is_not: _t("is not"),
    set: _t("is set"),
    not_set: _t("is not set"),
    
    // 字符串虚拟操作符
    starts_with: _t("starts with"),
    ends_with: _t("ends with"),
    
    // 范围虚拟操作符
    between: _t("is between"),
    within: _t("is within"),
};
```

**虚拟操作符功能**:
- **布尔操作**: 专门的布尔值操作符
- **空值检查**: 字段是否设置的检查
- **字符串模式**: 前缀和后缀匹配
- **范围操作**: 数值和日期范围操作
- **用户友好**: 更直观的操作符描述

### 3. 动态操作符

```javascript
const OPERATOR_DESCRIPTIONS = {
    // 动态操作符 (根据字段类型返回不同描述)
    any: (fieldDefType) => {
        switch (fieldDefType) {
            case "many2one":
                return _t("matches");
            default:
                return _t("match");
        }
    },
    "not any": (fieldDefType) => {
        switch (fieldDefType) {
            case "many2one":
                return _t("matches none of");
            default:
                return _t("match none of");
        }
    },
};
```

**动态操作符功能**:
- **类型感知**: 根据字段类型调整描述
- **上下文相关**: 提供更准确的语义描述
- **灵活性**: 支持复杂的操作符逻辑
- **可扩展**: 易于添加新的动态操作符

## 核心工具函数

### 1. 键值转换

```javascript
// 操作符转键值
function toKey(operator, negate = false) {
    if (!negate && typeof operator === "string" && operator in OPERATOR_DESCRIPTIONS) {
        // 主要情况，保持简单
        return operator;
    }
    return JSON.stringify([formatValue(operator), negate]);
}

// 键值转操作符
function toOperator(key) {
    if (!key.includes("[")) {
        return [key, false];
    }
    const [expr, negate] = JSON.parse(key);
    return [toValue(parseExpr(expr)), negate];
}
```

**键值转换功能**:
- **简化处理**: 标准操作符使用简单字符串键
- **复杂支持**: 复杂操作符使用JSON序列化
- **取反支持**: 完整的取反状态管理
- **双向转换**: 键值和操作符的双向转换

### 2. 描述生成

```javascript
// 获取操作符描述
function getOperatorDescription(operator, fieldDefType) {
    const description = OPERATOR_DESCRIPTIONS[operator];
    if (
        typeof description === "function" &&
        description.constructor?.name !== "LazyTranslatedString"
    ) {
        return description(fieldDefType);
    }
    return description;
}

// 获取操作符标签
function getOperatorLabel(operator, fieldDefType, negate = false) {
    let label;
    if (typeof operator === "string" && operator in OPERATOR_DESCRIPTIONS) {
        if (negate && operator in TERM_OPERATORS_NEGATION) {
            return getOperatorDescription(TERM_OPERATORS_NEGATION[operator], fieldDefType);
        }
        label = getOperatorDescription(operator, fieldDefType);
    } else {
        label = formatValue(operator);
    }
    if (negate) {
        return sprintf(`not %s`, label);
    }
    return label;
}
```

**描述生成功能**:
- **动态描述**: 支持函数式的动态描述生成
- **取反处理**: 智能的取反操作符映射
- **格式化**: 统一的值格式化处理
- **国际化**: 完整的多语言支持

### 3. 编辑器配置

```javascript
function getOperatorEditorInfo(operators, fieldDef) {
    const defaultOperator = operators[0];
    const operatorsInfo = operators.map((operator) => getOperatorInfo(operator, fieldDef?.type));
    
    return {
        component: Select,
        extractProps: ({ update, value: [operator, negate] }) => {
            const [operatorKey, operatorLabel] = getOperatorInfo(operator, fieldDef?.type, negate);
            const options = [...operatorsInfo];
            if (!options.some(([key]) => key === operatorKey)) {
                options.push([operatorKey, operatorLabel]);
            }
            return {
                value: operatorKey,
                update: (operatorKey) => update(...toOperator(operatorKey)),
                options,
            };
        },
        defaultValue: () => defaultOperator,
        isSupported: ([operator]) =>
            typeof operator === "string" && operator in OPERATOR_DESCRIPTIONS,
        message: _t("Operator not supported"),
        stringify: ([operator, negate]) => getOperatorLabel(operator, negate),
    };
}
```

**编辑器配置功能**:
- **组件选择**: 使用Select组件作为编辑器
- **属性提取**: 动态提取编辑器所需属性
- **选项生成**: 自动生成可用操作符选项
- **默认值**: 提供合理的默认操作符
- **验证支持**: 检查操作符的支持状态
- **字符串化**: 操作符的字符串表示

## 使用场景

### 1. 基础操作符选择器

```javascript
// 基础操作符选择器
class BasicOperatorSelector extends Component {
    setup() {
        this.state = useState({
            operator: '=',
            negate: false,
            fieldType: 'char',
            availableOperators: ['=', '!=', 'ilike', 'not ilike', 'in', 'not in']
        });
    }

    updateOperator(operator, negate) {
        this.state.operator = operator;
        this.state.negate = negate;
        this.onOperatorChanged(operator, negate);
    }

    onOperatorChanged(operator, negate) {
        console.log('Operator changed:', { operator, negate });
        const label = getOperatorLabel(operator, this.state.fieldType, negate);
        console.log('Operator label:', label);
    }

    getOperatorOptions() {
        return this.state.availableOperators.map(op => {
            const [key, label] = getOperatorInfo(op, this.state.fieldType);
            return [key, label];
        });
    }

    render() {
        const editorInfo = getOperatorEditorInfo(
            this.state.availableOperators,
            { type: this.state.fieldType }
        );

        return xml`
            <div class="basic-operator-selector">
                <label class="form-label">选择操作符</label>
                <Select
                    value="toKey(state.operator, state.negate)"
                    update="(key) => this.updateOperator(...toOperator(key))"
                    options="getOperatorOptions()"
                />
                <div class="operator-info mt-2">
                    <small class="text-muted">
                        当前操作符: <strong t-esc="getOperatorLabel(state.operator, state.fieldType, state.negate)"/>
                    </small>
                </div>
            </div>
        `;
    }
}
```

### 2. 字段类型感知的操作符编辑器

```javascript
// 字段类型感知的操作符编辑器
class FieldTypeAwareOperatorEditor extends Component {
    setup() {
        this.state = useState({
            fieldType: 'char',
            operator: '=',
            negate: false
        });

        // 字段类型到操作符的映射
        this.fieldTypeOperators = {
            'char': ['=', '!=', 'ilike', 'not ilike', 'like', 'not like', 'in', 'not in', 'starts_with', 'ends_with'],
            'text': ['=', '!=', 'ilike', 'not ilike', 'like', 'not like', 'in', 'not in'],
            'integer': ['=', '!=', '<', '>', '<=', '>=', 'in', 'not in', 'between'],
            'float': ['=', '!=', '<', '>', '<=', '>=', 'in', 'not in', 'between'],
            'boolean': ['=', '!=', 'is', 'is_not'],
            'date': ['=', '!=', '<', '>', '<=', '>=', 'between', 'within'],
            'datetime': ['=', '!=', '<', '>', '<=', '>=', 'between', 'within'],
            'many2one': ['=', '!=', 'in', 'not in', 'set', 'not_set', 'any', 'not any'],
            'one2many': ['set', 'not_set', 'any', 'not any'],
            'many2many': ['set', 'not_set', 'any', 'not any'],
            'selection': ['=', '!=', 'in', 'not in']
        };
    }

    get availableOperators() {
        return this.fieldTypeOperators[this.state.fieldType] || ['=', '!='];
    }

    updateFieldType(fieldType) {
        this.state.fieldType = fieldType;
        
        // 检查当前操作符是否仍然有效
        if (!this.availableOperators.includes(this.state.operator)) {
            this.state.operator = this.availableOperators[0];
            this.state.negate = false;
        }
        
        this.onFieldTypeChanged(fieldType);
    }

    updateOperator(operator, negate) {
        this.state.operator = operator;
        this.state.negate = negate;
        this.onOperatorChanged(operator, negate);
    }

    onFieldTypeChanged(fieldType) {
        console.log('Field type changed:', fieldType);
        console.log('Available operators:', this.availableOperators);
    }

    onOperatorChanged(operator, negate) {
        console.log('Operator changed:', { operator, negate });
        this.validateOperator(operator, negate);
    }

    validateOperator(operator, negate) {
        const isSupported = this.availableOperators.includes(operator);
        if (!isSupported) {
            this.notification.add(
                `操作符 "${operator}" 不支持字段类型 "${this.state.fieldType}"`,
                { type: 'warning' }
            );
        }
    }

    getOperatorDescription(operator, negate) {
        return getOperatorLabel(operator, this.state.fieldType, negate);
    }

    render() {
        const editorInfo = getOperatorEditorInfo(
            this.availableOperators,
            { type: this.state.fieldType }
        );

        return xml`
            <div class="field-type-aware-operator-editor">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">字段类型</label>
                        <select 
                            class="form-select"
                            t-model="state.fieldType"
                            t-on-change="(ev) => this.updateFieldType(ev.target.value)"
                        >
                            <option value="char">字符</option>
                            <option value="text">文本</option>
                            <option value="integer">整数</option>
                            <option value="float">浮点数</option>
                            <option value="boolean">布尔</option>
                            <option value="date">日期</option>
                            <option value="datetime">日期时间</option>
                            <option value="many2one">多对一</option>
                            <option value="one2many">一对多</option>
                            <option value="many2many">多对多</option>
                            <option value="selection">选择</option>
                        </select>
                    </div>
                    
                    <div class="col-md-6">
                        <label class="form-label">操作符</label>
                        <Select
                            value="toKey(state.operator, state.negate)"
                            update="(key) => this.updateOperator(...toOperator(key))"
                            options="availableOperators.map(op => getOperatorInfo(op, state.fieldType))"
                        />
                    </div>
                </div>

                <div class="operator-preview mt-3">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">操作符预览</h6>
                            <p class="card-text">
                                <strong>描述:</strong> 
                                <span t-esc="getOperatorDescription(state.operator, state.negate)"/>
                            </p>
                            <p class="card-text">
                                <strong>键值:</strong> 
                                <code t-esc="toKey(state.operator, state.negate)"/>
                            </p>
                            <p class="card-text">
                                <strong>支持状态:</strong>
                                <span 
                                    class="badge"
                                    t-att-class="availableOperators.includes(state.operator) ? 'bg-success' : 'bg-danger'"
                                >
                                    <t t-if="availableOperators.includes(state.operator)">支持</t>
                                    <t t-else="">不支持</t>
                                </span>
                            </p>
                        </div>
                    </div>
                </div>

                <div class="available-operators mt-3">
                    <h6>可用操作符</h6>
                    <div class="d-flex flex-wrap gap-1">
                        <t t-foreach="availableOperators" t-as="op" t-key="op">
                            <span 
                                class="badge bg-light text-dark cursor-pointer"
                                t-att-class="op === state.operator ? 'bg-primary text-white' : ''"
                                t-on-click="() => this.updateOperator(op, false)"
                                title="${getOperatorLabel(op, state.fieldType)}"
                            >
                                <t t-esc="op"/>
                            </span>
                        </t>
                    </div>
                </div>
            </div>
        `;
    }
}
```

### 3. 高级操作符管理器

```javascript
// 高级操作符管理器
class AdvancedOperatorManager extends Component {
    setup() {
        this.state = useState({
            customOperators: new Map(),
            operatorHistory: [],
            operatorStats: {},
            currentOperator: '=',
            currentNegate: false
        });

        this.loadOperatorHistory();
        this.loadOperatorStats();
    }

    // 注册自定义操作符
    registerCustomOperator(operator, description, fieldTypes = []) {
        this.state.customOperators.set(operator, {
            description,
            fieldTypes,
            isCustom: true,
            createdAt: new Date().toISOString()
        });
        
        this.saveCustomOperators();
    }

    // 获取扩展的操作符描述
    getExtendedOperatorDescriptions() {
        const extended = { ...OPERATOR_DESCRIPTIONS };
        
        this.state.customOperators.forEach((config, operator) => {
            extended[operator] = config.description;
        });
        
        return extended;
    }

    // 检查操作符是否支持特定字段类型
    isOperatorSupportedForField(operator, fieldType) {
        if (this.state.customOperators.has(operator)) {
            const config = this.state.customOperators.get(operator);
            return config.fieldTypes.length === 0 || config.fieldTypes.includes(fieldType);
        }
        
        // 使用默认的支持检查逻辑
        return typeof operator === "string" && operator in OPERATOR_DESCRIPTIONS;
    }

    // 记录操作符使用
    recordOperatorUsage(operator, negate) {
        const key = `${operator}${negate ? '_negated' : ''}`;
        
        this.state.operatorStats[key] = (this.state.operatorStats[key] || 0) + 1;
        
        this.state.operatorHistory.unshift({
            operator,
            negate,
            timestamp: Date.now()
        });
        
        // 保留最近100个使用记录
        if (this.state.operatorHistory.length > 100) {
            this.state.operatorHistory = this.state.operatorHistory.slice(0, 100);
        }
        
        this.saveOperatorStats();
        this.saveOperatorHistory();
    }

    // 获取最常用的操作符
    getMostUsedOperators(limit = 5) {
        return Object.entries(this.state.operatorStats)
            .sort(([,a], [,b]) => b - a)
            .slice(0, limit)
            .map(([key, count]) => {
                const negate = key.endsWith('_negated');
                const operator = negate ? key.slice(0, -8) : key;
                return { operator, negate, count };
            });
    }

    // 获取推荐操作符
    getRecommendedOperators(fieldType) {
        const mostUsed = this.getMostUsedOperators();
        const fieldSpecific = this.getFieldSpecificOperators(fieldType);
        
        // 合并并去重
        const recommended = new Set();
        
        mostUsed.forEach(({ operator }) => {
            if (this.isOperatorSupportedForField(operator, fieldType)) {
                recommended.add(operator);
            }
        });
        
        fieldSpecific.forEach(operator => {
            recommended.add(operator);
        });
        
        return Array.from(recommended);
    }

    // 导出操作符配置
    exportOperatorConfig() {
        const config = {
            customOperators: Object.fromEntries(this.state.customOperators),
            operatorStats: this.state.operatorStats,
            operatorHistory: this.state.operatorHistory.slice(0, 20), // 只导出最近20个
            exportedAt: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(config, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `operator_config_${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    // 导入操作符配置
    async importOperatorConfig(file) {
        try {
            const text = await file.text();
            const config = JSON.parse(text);
            
            if (config.customOperators) {
                this.state.customOperators = new Map(Object.entries(config.customOperators));
                this.saveCustomOperators();
            }
            
            if (config.operatorStats) {
                this.state.operatorStats = { ...this.state.operatorStats, ...config.operatorStats };
                this.saveOperatorStats();
            }
            
            this.notification.add('操作符配置导入成功', { type: 'success' });
        } catch (error) {
            this.notification.add('导入配置失败: ' + error.message, { type: 'danger' });
        }
    }

    render() {
        const mostUsed = this.getMostUsedOperators();
        
        return xml`
            <div class="advanced-operator-manager">
                <div class="manager-header mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>高级操作符管理器</h5>
                        <div class="btn-group">
                            <button 
                                class="btn btn-sm btn-outline-primary"
                                t-on-click="() => this.showCustomOperatorDialog()"
                            >
                                <i class="fa fa-plus"/> 添加自定义操作符
                            </button>
                            <button 
                                class="btn btn-sm btn-outline-info"
                                t-on-click="exportOperatorConfig"
                            >
                                <i class="fa fa-download"/> 导出配置
                            </button>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6>最常用操作符</h6>
                        <div class="list-group">
                            <t t-foreach="mostUsed" t-as="item" t-key="item.operator">
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong t-esc="item.operator"/>
                                        <span t-if="item.negate" class="text-muted"> (取反)</span>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">
                                        <t t-esc="item.count"/>
                                    </span>
                                </div>
                            </t>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h6>自定义操作符</h6>
                        <div class="list-group">
                            <t t-foreach="Array.from(state.customOperators.entries())" t-as="entry" t-key="entry[0]">
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong t-esc="entry[0]"/>
                                        <small class="text-muted d-block" t-esc="entry[1].description"/>
                                    </div>
                                    <button 
                                        class="btn btn-sm btn-outline-danger"
                                        t-on-click="() => this.removeCustomOperator(entry[0])"
                                    >
                                        <i class="fa fa-trash"/>
                                    </button>
                                </div>
                            </t>
                        </div>
                    </div>
                </div>

                <div class="operator-history mt-3">
                    <h6>使用历史</h6>
                    <div class="d-flex flex-wrap gap-1">
                        <t t-foreach="state.operatorHistory.slice(0, 10)" t-as="item" t-key="item_index">
                            <span 
                                class="badge bg-light text-dark"
                                title="${new Date(item.timestamp).toLocaleString()}"
                            >
                                <t t-esc="item.operator"/>
                                <span t-if="item.negate" class="text-muted">!</span>
                            </span>
                        </t>
                    </div>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 完整的操作符支持
- 标准Odoo操作符
- 虚拟操作符扩展
- 自定义操作符支持

### 2. 智能描述生成
- 动态的操作符描述
- 字段类型感知
- 国际化支持

### 3. 灵活的配置系统
- 可配置的编辑器
- 动态的选项生成
- 验证和错误处理

### 4. 用户体验优化
- 直观的操作符标签
- 智能的默认值
- 清晰的错误提示

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- 不同操作符的处理策略
- 可配置的描述生成

### 2. 工厂模式 (Factory Pattern)
- 编辑器配置的生成
- 统一的创建接口

### 3. 适配器模式 (Adapter Pattern)
- 键值和操作符的转换
- 统一的接口适配

## 注意事项

1. **类型安全**: 确保操作符与字段类型的兼容性
2. **性能优化**: 避免频繁的描述生成
3. **国际化**: 确保所有文本的正确翻译
4. **扩展性**: 支持新操作符的添加

## 扩展建议

1. **自定义操作符**: 支持用户定义的操作符
2. **操作符分组**: 按类别组织操作符
3. **智能推荐**: 基于上下文的操作符推荐
4. **快捷键**: 常用操作符的快捷键支持
5. **可视化**: 操作符效果的可视化预览

该操作符编辑器为Odoo Web应用提供了完整的操作符管理能力，通过智能描述生成和灵活配置确保了条件构建过程中操作符选择的准确性和用户友好性。

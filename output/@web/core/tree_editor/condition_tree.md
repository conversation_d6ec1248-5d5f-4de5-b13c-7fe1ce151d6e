# ConditionTree - 条件树核心模块

## 概述

`condition_tree.js` 是 Odoo Web 核心模块的条件树处理引擎，提供了条件表达式、域和树结构之间的转换功能。该模块是树编辑器的核心，支持复杂的逻辑条件构建、操作符处理、表达式解析和域转换，为Odoo Web应用提供了强大的条件逻辑处理能力，广泛应用于过滤器、搜索条件、业务规则和数据查询等场景。

## 文件信息
- **路径**: `/web/static/src/core/tree_editor/condition_tree.js`
- **行数**: 1108
- **模块**: `@web/core/tree_editor/condition_tree`

## 依赖关系

```javascript
// 核心依赖
'@web/core/domain'                    // 域处理模块
'@web/core/py_js/py'                  // Python表达式解析
'@web/core/py_js/py_utils'            // Python工具函数
'@web/core/utils/objects'             // 对象工具函数
```

## 核心数据结构

### 1. 类型定义

```javascript
/**
 * @typedef {Object} Condition - 基础条件
 * @property {"condition"} type
 * @property {Value} path - 字段路径
 * @property {Value} operator - 操作符
 * @property {Value} value - 值
 * @property {boolean} negate - 是否取反
 */

/**
 * @typedef {Object} ComplexCondition - 复杂条件
 * @property {"complex_condition"} type
 * @property {string} value - 表达式字符串
 */

/**
 * @typedef {Object} Connector - 连接器
 * @property {"connector"} type
 * @property {boolean} negate - 是否取反
 * @property {"|"|"&"} value - 连接类型(AND/OR)
 * @property {Tree[]} children - 子节点
 */

/**
 * @typedef {Connector|Condition|ComplexCondition} Tree - 树节点
 */
```

### 2. 操作符映射

```javascript
const TERM_OPERATORS_NEGATION = {
    "<": ">=",
    ">": "<=", 
    "<=": ">",
    ">=": "<",
    "=": "!=",
    "!=": "=",
    "in": "not in",
    "like": "not like",
    "ilike": "not ilike",
    "not in": "in",
    "not like": "like",
    "not ilike": "ilike"
};
```

**操作符功能**:
- **取反映射**: 定义操作符的取反关系
- **逻辑转换**: 支持逻辑条件的自动转换
- **优化处理**: 简化复杂的取反逻辑
- **标准化**: 统一的操作符处理规范

## 核心功能

### 1. 表达式类

```javascript
class Expression {
    constructor(ast) {
        if (typeof ast === "string") {
            ast = parseExpr(ast);
        }
        this._ast = ast;
        this._expr = formatAST(ast);
    }

    toAST() {
        return this._ast;
    }

    toString() {
        return this._expr;
    }
}
```

**表达式功能**:
- **AST封装**: 封装抽象语法树
- **字符串转换**: 支持字符串和AST的双向转换
- **表达式缓存**: 缓存格式化后的表达式
- **类型安全**: 提供类型安全的表达式处理

### 2. 树构造函数

```javascript
// 创建连接器
function connector(value, children = [], negate = false) {
    return { type: "connector", value, children, negate };
}

// 创建条件
function condition(path, operator, value, negate = false) {
    return { type: "condition", path, operator, value, negate };
}

// 创建复杂条件
function complexCondition(value) {
    parseExpr(value); // 验证表达式
    return { type: "complex_condition", value };
}
```

**构造功能**:
- **类型安全**: 确保创建正确类型的节点
- **参数验证**: 验证输入参数的有效性
- **默认值**: 提供合理的默认参数
- **表达式验证**: 验证复杂条件的表达式语法

### 3. 转换核心算法

```javascript
// 从表达式创建树
function treeFromExpression(expression, options = {}) {
    const ast = parseExpr(expression);
    const tree = _treeFromAST(ast, options);
    return createVirtualOperators(
        createWithinOperators(createBetweenOperators(tree), options),
        options
    );
}

// 从树生成表达式
function expressionFromTree(tree, options = {}) {
    const simplifiedTree = createComplexConditions(
        removeBetweenOperators(removeWithinOperators(removeVirtualOperators(tree)))
    );
    return _expressionFromTree(simplifiedTree, options, true);
}
```

**转换功能**:
- **双向转换**: 支持表达式和树的双向转换
- **操作符处理**: 自动处理虚拟操作符和特殊操作符
- **优化简化**: 自动优化和简化复杂结构
- **选项配置**: 支持灵活的转换选项配置

### 4. 域处理

```javascript
// 从域创建树
function treeFromDomain(domain, options = {}) {
    domain = new Domain(domain);
    const domainAST = domain.ast;
    const tree = construcTree(domainAST.value, options);
    return createVirtualOperators(
        createWithinOperators(createBetweenOperators(tree), options),
        options
    );
}

// 从树生成域
function domainFromTree(tree) {
    const simplifiedTree = removeBetweenOperators(
        removeWithinOperators(removeVirtualOperators(removeComplexConditions(tree)))
    );
    const domainAST = {
        type: 4,
        value: getASTs(simplifiedTree)
    };
    return formatAST(domainAST);
}
```

**域处理功能**:
- **域解析**: 解析Odoo域格式
- **AST转换**: 域和AST之间的转换
- **结构优化**: 优化域结构提升性能
- **兼容性**: 保持与Odoo域格式的兼容性

## 使用场景

### 1. 基础条件构建

```javascript
// 创建简单条件
const nameCondition = condition("name", "=", "John");
const ageCondition = condition("age", ">", 18);

// 创建连接器
const andConnector = connector("&", [nameCondition, ageCondition]);

// 转换为域
const domain = domainFromTree(andConnector);
console.log(domain); // [["name", "=", "John"], ["age", ">", 18]]
```

### 2. 复杂表达式处理

```javascript
// 从表达式创建树
const expression = "name == 'John' and (age > 18 or status == 'active')";
const tree = treeFromExpression(expression);

// 修改树结构
tree.children.push(condition("department", "=", "IT"));

// 转换回表达式
const newExpression = expressionFromTree(tree);
console.log(newExpression);
```

### 3. 高级条件管理器

```javascript
// 高级条件管理器
class AdvancedConditionManager {
    constructor(options = {}) {
        this.options = {
            getFieldDef: options.getFieldDef || (() => null),
            distributeNot: options.distributeNot || false,
            ...options
        };
        this.tree = connector("&");
        this.history = [];
    }

    // 添加条件
    addCondition(path, operator, value) {
        const newCondition = condition(path, operator, value);
        this.tree.children.push(newCondition);
        this.saveToHistory('add', newCondition);
        return this;
    }

    // 添加复杂条件
    addComplexCondition(expression) {
        try {
            const complexCond = complexCondition(expression);
            this.tree.children.push(complexCond);
            this.saveToHistory('addComplex', complexCond);
            return this;
        } catch (error) {
            throw new Error(`Invalid expression: ${expression}`);
        }
    }

    // 创建分组
    createGroup(connector = "&") {
        const group = connector(connector);
        this.tree.children.push(group);
        this.saveToHistory('createGroup', group);
        return new AdvancedConditionManager({
            ...this.options,
            tree: group
        });
    }

    // 应用取反
    negate() {
        this.tree.negate = !this.tree.negate;
        this.saveToHistory('negate', this.tree);
        return this;
    }

    // 优化树结构
    optimize() {
        this.tree = this.optimizeTree(this.tree);
        this.saveToHistory('optimize', this.tree);
        return this;
    }

    optimizeTree(tree) {
        if (tree.type === "condition" || tree.type === "complex_condition") {
            return tree;
        }

        // 移除空的连接器
        const optimizedChildren = tree.children
            .map(child => this.optimizeTree(child))
            .filter(child => {
                if (child.type === "connector" && child.children.length === 0) {
                    return false;
                }
                return true;
            });

        // 扁平化相同类型的连接器
        const flattenedChildren = [];
        for (const child of optimizedChildren) {
            if (child.type === "connector" && 
                child.value === tree.value && 
                !child.negate && !tree.negate) {
                flattenedChildren.push(...child.children);
            } else {
                flattenedChildren.push(child);
            }
        }

        return { ...tree, children: flattenedChildren };
    }

    // 转换为不同格式
    toDomain() {
        return domainFromTree(this.tree);
    }

    toExpression() {
        return expressionFromTree(this.tree, this.options);
    }

    toJSON() {
        return JSON.stringify(this.tree, null, 2);
    }

    // 从不同格式加载
    fromDomain(domain) {
        this.tree = treeFromDomain(domain, this.options);
        this.saveToHistory('fromDomain', this.tree);
        return this;
    }

    fromExpression(expression) {
        this.tree = treeFromExpression(expression, this.options);
        this.saveToHistory('fromExpression', this.tree);
        return this;
    }

    fromJSON(jsonString) {
        this.tree = JSON.parse(jsonString);
        this.saveToHistory('fromJSON', this.tree);
        return this;
    }

    // 历史管理
    saveToHistory(action, data) {
        this.history.push({
            action,
            data: cloneTree(data),
            timestamp: Date.now()
        });

        // 保留最近100个操作
        if (this.history.length > 100) {
            this.history.shift();
        }
    }

    undo() {
        if (this.history.length > 1) {
            this.history.pop(); // 移除当前状态
            const previousState = this.history[this.history.length - 1];
            this.tree = cloneTree(previousState.data);
        }
        return this;
    }

    // 验证和分析
    validate() {
        const errors = [];
        this.validateTree(this.tree, errors);
        return errors;
    }

    validateTree(tree, errors, path = []) {
        if (tree.type === "condition") {
            // 验证字段路径
            if (this.options.getFieldDef) {
                const fieldDef = this.options.getFieldDef(tree.path);
                if (!fieldDef) {
                    errors.push({
                        type: 'invalid_field',
                        path: [...path],
                        message: `Field '${tree.path}' not found`
                    });
                }
            }

            // 验证操作符
            if (typeof tree.operator === 'string') {
                const validOperators = ['=', '!=', '<', '>', '<=', '>=', 'in', 'not in', 'like', 'ilike'];
                if (!validOperators.includes(tree.operator)) {
                    errors.push({
                        type: 'invalid_operator',
                        path: [...path],
                        message: `Invalid operator '${tree.operator}'`
                    });
                }
            }
        } else if (tree.type === "complex_condition") {
            // 验证复杂表达式
            try {
                parseExpr(tree.value);
            } catch (error) {
                errors.push({
                    type: 'invalid_expression',
                    path: [...path],
                    message: `Invalid expression: ${error.message}`
                });
            }
        } else if (tree.type === "connector") {
            // 递归验证子节点
            tree.children.forEach((child, index) => {
                this.validateTree(child, errors, [...path, index]);
            });
        }
    }

    // 统计信息
    getStatistics() {
        const stats = {
            totalNodes: 0,
            conditions: 0,
            complexConditions: 0,
            connectors: 0,
            maxDepth: 0
        };

        this.calculateStats(this.tree, stats, 0);
        return stats;
    }

    calculateStats(tree, stats, depth) {
        stats.totalNodes++;
        stats.maxDepth = Math.max(stats.maxDepth, depth);

        if (tree.type === "condition") {
            stats.conditions++;
        } else if (tree.type === "complex_condition") {
            stats.complexConditions++;
        } else if (tree.type === "connector") {
            stats.connectors++;
            tree.children.forEach(child => {
                this.calculateStats(child, stats, depth + 1);
            });
        }
    }

    // 搜索和替换
    findConditions(predicate) {
        const results = [];
        this.searchTree(this.tree, predicate, results, []);
        return results;
    }

    searchTree(tree, predicate, results, path) {
        if (tree.type === "condition" && predicate(tree)) {
            results.push({ condition: tree, path: [...path] });
        } else if (tree.type === "connector") {
            tree.children.forEach((child, index) => {
                this.searchTree(child, predicate, results, [...path, index]);
            });
        }
    }

    replaceConditions(predicate, replacer) {
        this.tree = this.replaceInTree(this.tree, predicate, replacer);
        this.saveToHistory('replace', this.tree);
        return this;
    }

    replaceInTree(tree, predicate, replacer) {
        if (tree.type === "condition" && predicate(tree)) {
            return replacer(tree);
        } else if (tree.type === "connector") {
            return {
                ...tree,
                children: tree.children.map(child => 
                    this.replaceInTree(child, predicate, replacer)
                )
            };
        }
        return tree;
    }
}

// 使用示例
const manager = new AdvancedConditionManager({
    getFieldDef: (fieldName) => {
        const fields = {
            'name': { type: 'char' },
            'age': { type: 'integer' },
            'active': { type: 'boolean' },
            'partner_id': { type: 'many2one' }
        };
        return fields[fieldName] || null;
    }
});

// 构建复杂条件
manager
    .addCondition('name', 'ilike', '%John%')
    .addCondition('age', '>', 18)
    .createGroup('|')
        .addCondition('active', '=', true)
        .addComplexCondition('partner_id.country_id.code == "US"')
    .optimize();

console.log('Domain:', manager.toDomain());
console.log('Expression:', manager.toExpression());
console.log('Statistics:', manager.getStatistics());
console.log('Validation:', manager.validate());
```

### 4. 特殊操作符处理

```javascript
// 日期范围处理
class DateRangeProcessor {
    static createWithinCondition(field, amount, period, fieldType = 'date') {
        // 创建"within"操作符条件
        return condition(field, 'within', [amount, period, fieldType]);
    }

    static createBetweenCondition(field, startDate, endDate) {
        // 创建"between"操作符条件
        return condition(field, 'between', [startDate, endDate]);
    }

    static processTodayExpressions(tree) {
        // 处理"today"相关的表达式
        return createWithinOperators(tree, {
            getFieldDef: (field) => ({ type: 'date' })
        });
    }
}

// 虚拟操作符处理
class VirtualOperatorProcessor {
    static createVirtualOperators(tree, options) {
        return createVirtualOperators(tree, options);
    }

    static removeVirtualOperators(tree) {
        return removeVirtualOperators(tree);
    }

    static processBoolean(field, value) {
        // 处理布尔字段的特殊操作符
        return condition(field, value ? 'is' : 'is_not', true);
    }

    static processSet(field, isEmpty) {
        // 处理字段是否为空的操作符
        return condition(field, isEmpty ? 'not_set' : 'set', false);
    }
}
```

## 技术特点

### 1. 多格式支持
- 支持表达式、域和树结构的互转
- 兼容Python表达式语法
- 支持Odoo域格式
- 提供JSON序列化支持

### 2. 操作符扩展
- 虚拟操作符(is/is_not, set/not_set)
- 特殊操作符(between, within)
- 字符串操作符(starts_with, ends_with)
- 日期时间操作符

### 3. 智能优化
- 自动简化复杂结构
- 操作符取反优化
- 树结构扁平化
- 表达式标准化

### 4. 类型安全
- 完整的TypeScript类型定义
- 运行时类型检查
- 表达式语法验证
- 字段定义验证

## 设计模式

### 1. 访问者模式 (Visitor Pattern)
- 树结构的遍历和处理
- 不同操作的统一接口

### 2. 策略模式 (Strategy Pattern)
- 不同操作符的处理策略
- 可配置的转换选项

### 3. 建造者模式 (Builder Pattern)
- 复杂条件树的构建
- 链式调用接口

## 注意事项

1. **表达式安全**: 验证用户输入的表达式避免注入攻击
2. **性能优化**: 大型条件树的性能优化
3. **内存管理**: 避免循环引用和内存泄漏
4. **兼容性**: 保持与不同版本的兼容性

## 扩展建议

1. **可视化**: 条件树的可视化编辑器
2. **模板系统**: 常用条件的模板管理
3. **性能分析**: 条件执行的性能分析
4. **智能提示**: 字段和操作符的智能提示
5. **批量操作**: 条件的批量导入导出

该条件树模块为Odoo Web应用提供了强大的条件逻辑处理能力，通过多格式转换和智能优化确保了灵活性和性能的平衡。

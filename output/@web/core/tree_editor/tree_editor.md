# TreeEditor - 树编辑器主组件

## 概述

`tree_editor.js` 是 Odoo Web 核心模块的树编辑器主组件，提供了可视化编辑条件树的用户界面。该组件支持条件的增删改查、连接器切换、字段选择、操作符编辑和值编辑，为用户提供了直观的条件构建体验，广泛应用于过滤器编辑、搜索条件构建、业务规则配置和数据查询界面等场景。

## 文件信息
- **路径**: `/web/static/src/core/tree_editor/tree_editor.js`
- **行数**: 277
- **模块**: `@web/core/tree_editor/tree_editor`

## 依赖关系

```javascript
// 核心依赖
'@web/core/tree_editor/utils'                        // 树编辑器工具函数
'@odoo/owl'                                          // OWL框架
'@web/core/utils/hooks'                              // 工具钩子
'@web/core/dropdown/dropdown'                        // 下拉菜单组件
'@web/core/dropdown/dropdown_item'                   // 下拉菜单项
'@web/core/tree_editor/condition_tree'               // 条件树核心
'@web/core/tree_editor/tree_editor_value_editors'    // 值编辑器
'@web/core/model_field_selector/model_field_selector' // 字段选择器
'@web/core/model_field_selector/utils'               // 字段选择器工具
'@web/core/utils/objects'                            // 对象工具
```

## 核心常量和工具函数

### 1. 默认树结构

```javascript
const TRUE_TREE = condition(1, "=", 1);
```

**常量功能**:
- **默认条件**: 表示"总是为真"的条件
- **初始状态**: 用于初始化空的条件树
- **占位符**: 避免完全空的条件树
- **标准化**: 提供统一的默认条件

### 2. 差异收集函数

```javascript
function collectDifferences(tree, otherTree) {
    // 检查类型差异
    if (tree.type !== otherTree.type) {
        return [{ type: "other" }];
    }
    
    // 检查取反差异
    if (tree.negate !== otherTree.negate) {
        return [{ type: "other" }];
    }
    
    // 条件节点的特殊处理
    if (tree.type === "condition") {
        // 检查路径、值、操作符差异
        if (formatValue(tree.path) !== formatValue(otherTree.path)) {
            return [{ type: "other" }];
        }
        
        // 特殊操作符替换检测
        if (formatValue(tree.operator) !== formatValue(otherTree.operator)) {
            if (tree.operator === "!=" && otherTree.operator === "set") {
                return [{ type: "replacement", tree, operator: "set" }];
            } else if (tree.operator === "=" && otherTree.operator === "not_set") {
                return [{ type: "replacement", tree, operator: "not_set" }];
            }
            return [{ type: "other" }];
        }
    }
    
    // 递归检查子节点
    const diffs = [];
    for (let i = 0; i < tree.children.length; i++) {
        const childDiffs = collectDifferences(tree.children[i], otherTree.children[i]);
        diffs.push(...childDiffs);
    }
    return diffs;
}
```

**差异收集功能**:
- **结构比较**: 比较两个树的结构差异
- **类型检测**: 检测节点类型的变化
- **操作符映射**: 检测虚拟操作符的替换
- **递归分析**: 深度分析所有子节点差异

### 3. 虚拟操作符恢复

```javascript
function restoreVirtualOperators(tree, otherTree) {
    const diffs = collectDifferences(tree, otherTree);
    
    // 只处理替换类型的差异
    if (diffs.some((d) => d.type !== "replacement")) {
        return;
    }
    
    // 恢复虚拟操作符
    for (const { tree, operator } of diffs) {
        tree.operator = operator;
    }
}
```

**恢复功能**:
- **操作符恢复**: 恢复被转换的虚拟操作符
- **状态保持**: 保持用户界面的操作符状态
- **差异过滤**: 只处理可恢复的差异
- **批量处理**: 批量恢复多个操作符

## 主组件类

### 1. 组件属性定义

```javascript
static props = {
    tree: Object,                    // 条件树对象
    resModel: String,               // 资源模型名称
    update: Function,               // 更新回调函数
    getDefaultCondition: Function,  // 获取默认条件
    getPathEditorInfo: Function,    // 获取路径编辑器信息
    getOperatorEditorInfo: Function, // 获取操作符编辑器信息
    getDefaultOperator: Function,   // 获取默认操作符
    readonly: { type: Boolean, optional: true },      // 只读模式
    slots: { type: Object, optional: true },          // 插槽
    isDebugMode: { type: Boolean, optional: true },   // 调试模式
    defaultConnector: { type: [{ value: "&" }, { value: "|" }], optional: true }, // 默认连接器
    isSubTree: { type: Boolean, optional: true },     // 是否为子树
};
```

**属性功能**:
- **树数据**: tree定义要编辑的条件树
- **模型信息**: resModel指定数据模型
- **回调函数**: 各种获取信息和处理更新的回调
- **模式控制**: readonly、isDebugMode等控制组件行为
- **配置选项**: 默认连接器、子树标识等配置

### 2. 组件初始化

```javascript
setup() {
    this.isTree = isTree;
    this.fieldService = useService("field");
    this.nameService = useService("name");
    this.loadFieldInfo = useLoadFieldInfo(this.fieldService);
    this.makeGetFieldDef = useMakeGetFieldDef(this.fieldService);
    this.makeGetConditionDescription = useMakeGetConditionDescription(
        this.fieldService,
        this.nameService
    );
    
    onWillStart(() => this.onPropsUpdated(this.props));
    onWillUpdateProps((nextProps) => this.onPropsUpdated(nextProps));
}
```

**初始化功能**:
- **服务注入**: 注入字段服务和名称服务
- **工具函数**: 创建各种工具函数
- **生命周期**: 绑定属性更新处理
- **状态准备**: 准备组件所需的各种状态

### 3. 属性更新处理

```javascript
async onPropsUpdated(props) {
    // 克隆树结构
    this.tree = cloneTree(props.tree);
    
    // 处理特殊情况
    if (shallowEqual(this.tree, TRUE_TREE)) {
        this.tree = connector(props.defaultConnector);
    } else if (this.tree.type !== "connector") {
        this.tree = connector(props.defaultConnector, [this.tree]);
    }

    // 恢复虚拟操作符
    if (this.previousTree) {
        restoreVirtualOperators(this.tree, this.previousTree);
        this.previousTree = null;
    }

    // 加载字段定义
    const [fieldDefs, getFieldDef] = await Promise.all([
        this.fieldService.loadFields(props.resModel),
        this.makeGetFieldDef(props.resModel, this.tree),
    ]);
    
    this.getFieldDef = getFieldDef;
    this.defaultCondition = props.getDefaultCondition(fieldDefs);

    // 只读模式的特殊处理
    if (props.readonly) {
        this.getConditionDescription = await this.makeGetConditionDescription(
            props.resModel,
            this.tree,
            this.getFieldDef
        );
    }
}
```

**更新处理功能**:
- **树克隆**: 创建树的独立副本
- **结构标准化**: 确保树结构符合要求
- **操作符恢复**: 恢复用户界面状态
- **字段加载**: 异步加载字段定义
- **描述生成**: 为只读模式生成条件描述

## 核心操作方法

### 1. 树结构操作

```javascript
// 创建新叶子节点
createNewLeaf() {
    return cloneTree(this.defaultCondition);
}

// 创建新分支节点
createNewBranch(value) {
    return connector(value, [this.createNewLeaf(), this.createNewLeaf()]);
}

// 插入根级叶子节点
insertRootLeaf(parent) {
    parent.children.push(this.createNewLeaf());
    this.notifyChanges();
}

// 插入叶子节点
insertLeaf(parent, node) {
    const newNode = node.type !== "connector" ? cloneTree(node) : this.createNewLeaf();
    const index = parent.children.indexOf(node);
    parent.children.splice(index + 1, 0, newNode);
    this.notifyChanges();
}

// 插入分支节点
insertBranch(parent, node) {
    const nextConnector = parent.value === "&" ? "|" : "&";
    const newNode = this.createNewBranch(nextConnector);
    const index = parent.children.indexOf(node);
    parent.children.splice(index + 1, 0, newNode);
    this.notifyChanges();
}

// 删除节点
delete(parent, node) {
    const index = parent.children.indexOf(node);
    parent.children.splice(index, 1);
    this.notifyChanges();
}
```

**结构操作功能**:
- **节点创建**: 创建不同类型的新节点
- **智能插入**: 根据上下文智能插入节点
- **连接器切换**: 自动选择合适的连接器类型
- **索引管理**: 正确处理节点的插入位置

### 2. 条件更新操作

```javascript
// 更新连接器
updateConnector(node, value) {
    node.value = value;
    node.negate = false;
    this.notifyChanges();
}

// 更新复杂条件
updateComplexCondition(node, value) {
    node.value = value;
    this.notifyChanges();
}

// 更新字段路径
async updatePath(node, path) {
    const { fieldDef } = await this.loadFieldInfo(this.props.resModel, path);
    node.path = path;
    node.negate = false;
    node.operator = this.props.getDefaultOperator(fieldDef);
    node.value = getDefaultValue(fieldDef, node.operator);
    this.notifyChanges();
}

// 更新操作符
updateLeafOperator(node, operator, negate) {
    const previousNode = cloneTree(node);
    const fieldDef = this.getFieldDef(node.path);
    node.negate = negate;
    node.operator = operator;
    node.value = getDefaultValue(fieldDef, operator, node.value);
    
    // 检查是否需要强制重新渲染
    if (deepEqual(removeVirtualOperators(node), removeVirtualOperators(previousNode))) {
        this.render();
    }
    this.notifyChanges();
}

// 更新值
updateLeafValue(node, value) {
    node.value = value;
    this.notifyChanges();
}
```

**更新操作功能**:
- **级联更新**: 字段变化时自动更新相关属性
- **默认值**: 自动设置合适的默认值
- **状态重置**: 重置相关的状态标志
- **渲染控制**: 智能控制组件重新渲染

### 3. 信息获取方法

```javascript
// 获取资源模型
getResModel(node) {
    const fieldDef = this.getFieldDef(node.path);
    const resModel = getResModel(fieldDef);
    return resModel;
}

// 获取路径编辑器信息
getPathEditorInfo() {
    return this.props.getPathEditorInfo(this.props.resModel, this.defaultCondition);
}

// 获取操作符编辑器信息
getOperatorEditorInfo(node) {
    const fieldDef = this.getFieldDef(node.path);
    return this.props.getOperatorEditorInfo(fieldDef);
}

// 获取值编辑器信息
getValueEditorInfo(node) {
    const fieldDef = this.getFieldDef(node.path);
    return getValueEditorInfo(fieldDef, node.operator);
}
```

**信息获取功能**:
- **字段定义**: 获取字段的详细定义信息
- **编辑器配置**: 获取各种编辑器的配置信息
- **模型关系**: 处理关联字段的模型信息
- **动态配置**: 根据上下文动态获取配置

## 使用场景

### 1. 基础条件编辑器

```javascript
// 基础条件编辑器使用
class BasicConditionEditor extends Component {
    setup() {
        this.state = useState({
            tree: connector("&", [
                condition("name", "ilike", ""),
                condition("active", "=", true)
            ])
        });
    }

    updateTree(newTree) {
        this.state.tree = newTree;
        this.onConditionChanged(newTree);
    }

    onConditionChanged(tree) {
        // 处理条件变化
        const domain = domainFromTree(tree);
        console.log('Generated domain:', domain);
    }

    getDefaultCondition(fieldDefs) {
        // 返回默认条件
        const firstField = Object.keys(fieldDefs)[0];
        return condition(firstField, "=", "");
    }

    getPathEditorInfo(resModel, defaultCondition) {
        return {
            resModel,
            path: defaultCondition.path,
            readonly: false
        };
    }

    getOperatorEditorInfo(fieldDef) {
        const operators = this.getAvailableOperators(fieldDef);
        return {
            operators,
            readonly: false
        };
    }

    getDefaultOperator(fieldDef) {
        if (fieldDef.type === 'char' || fieldDef.type === 'text') {
            return 'ilike';
        } else if (fieldDef.type === 'boolean') {
            return '=';
        } else if (fieldDef.type === 'integer' || fieldDef.type === 'float') {
            return '=';
        }
        return '=';
    }

    getAvailableOperators(fieldDef) {
        const baseOperators = ['=', '!='];
        
        if (fieldDef.type === 'char' || fieldDef.type === 'text') {
            return [...baseOperators, 'ilike', 'not ilike', 'in', 'not in'];
        } else if (fieldDef.type === 'integer' || fieldDef.type === 'float') {
            return [...baseOperators, '<', '>', '<=', '>='];
        } else if (fieldDef.type === 'boolean') {
            return baseOperators;
        } else if (fieldDef.type === 'many2one') {
            return [...baseOperators, 'in', 'not in'];
        }
        
        return baseOperators;
    }

    render() {
        return xml`
            <div class="basic-condition-editor">
                <TreeEditor
                    tree="state.tree"
                    resModel="res.partner"
                    update="updateTree"
                    getDefaultCondition="getDefaultCondition"
                    getPathEditorInfo="getPathEditorInfo"
                    getOperatorEditorInfo="getOperatorEditorInfo"
                    getDefaultOperator="getDefaultOperator"
                />
            </div>
        `;
    }
}
```

### 2. 高级过滤器编辑器

```javascript
// 高级过滤器编辑器
class AdvancedFilterEditor extends Component {
    setup() {
        this.state = useState({
            tree: connector("&"),
            savedFilters: [],
            currentFilter: null,
            isModified: false
        });

        this.orm = useService('orm');
        this.notification = useService('notification');
    }

    async loadSavedFilters() {
        try {
            const filters = await this.orm.searchRead(
                'ir.filters',
                [['model_id', '=', this.props.resModel]],
                ['name', 'domain']
            );
            this.state.savedFilters = filters;
        } catch (error) {
            this.notification.add('加载过滤器失败', { type: 'danger' });
        }
    }

    updateTree(newTree) {
        this.state.tree = newTree;
        this.state.isModified = true;
        this.validateTree(newTree);
    }

    validateTree(tree) {
        // 验证树结构
        const errors = this.collectValidationErrors(tree);
        if (errors.length > 0) {
            this.notification.add(
                `条件验证失败: ${errors.join(', ')}`,
                { type: 'warning' }
            );
        }
    }

    collectValidationErrors(tree, errors = []) {
        if (tree.type === 'condition') {
            if (!tree.path) {
                errors.push('字段路径不能为空');
            }
            if (!tree.operator) {
                errors.push('操作符不能为空');
            }
        } else if (tree.type === 'connector') {
            if (tree.children.length === 0) {
                errors.push('连接器不能为空');
            }
            tree.children.forEach(child => {
                this.collectValidationErrors(child, errors);
            });
        }
        return errors;
    }

    async saveFilter() {
        if (!this.state.isModified) {
            return;
        }

        const domain = domainFromTree(this.state.tree);
        const filterName = prompt('请输入过滤器名称:');
        
        if (filterName) {
            try {
                await this.orm.create('ir.filters', [{
                    name: filterName,
                    model_id: this.props.resModel,
                    domain: JSON.stringify(domain),
                    user_id: this.env.user.userId
                }]);
                
                this.notification.add('过滤器保存成功', { type: 'success' });
                this.state.isModified = false;
                this.loadSavedFilters();
            } catch (error) {
                this.notification.add('保存过滤器失败', { type: 'danger' });
            }
        }
    }

    loadFilter(filter) {
        try {
            const domain = JSON.parse(filter.domain);
            const tree = treeFromDomain(domain);
            this.state.tree = tree;
            this.state.currentFilter = filter;
            this.state.isModified = false;
        } catch (error) {
            this.notification.add('加载过滤器失败', { type: 'danger' });
        }
    }

    clearFilter() {
        this.state.tree = connector("&");
        this.state.currentFilter = null;
        this.state.isModified = false;
    }

    exportFilter() {
        const domain = domainFromTree(this.state.tree);
        const expression = expressionFromTree(this.state.tree);
        
        const exportData = {
            domain,
            expression,
            tree: this.state.tree,
            timestamp: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `filter_${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    render() {
        return xml`
            <div class="advanced-filter-editor">
                <div class="filter-toolbar mb-3">
                    <div class="btn-group">
                        <button 
                            class="btn btn-primary"
                            t-on-click="saveFilter"
                            t-att-disabled="!state.isModified"
                        >
                            <i class="fa fa-save"/> 保存过滤器
                        </button>
                        <button 
                            class="btn btn-secondary"
                            t-on-click="clearFilter"
                        >
                            <i class="fa fa-trash"/> 清空
                        </button>
                        <button 
                            class="btn btn-info"
                            t-on-click="exportFilter"
                        >
                            <i class="fa fa-download"/> 导出
                        </button>
                    </div>
                    
                    <div class="saved-filters ms-3">
                        <select 
                            class="form-select"
                            t-on-change="(ev) => this.loadFilter(state.savedFilters.find(f => f.id == ev.target.value))"
                        >
                            <option value="">选择已保存的过滤器</option>
                            <t t-foreach="state.savedFilters" t-as="filter" t-key="filter.id">
                                <option t-att-value="filter.id">
                                    <t t-esc="filter.name"/>
                                </option>
                            </t>
                        </select>
                    </div>
                </div>

                <div class="filter-status mb-2" t-if="state.currentFilter">
                    <span class="badge bg-info">
                        当前过滤器: <t t-esc="state.currentFilter.name"/>
                        <span t-if="state.isModified" class="text-warning"> (已修改)</span>
                    </span>
                </div>

                <TreeEditor
                    tree="state.tree"
                    resModel="props.resModel"
                    update="updateTree"
                    getDefaultCondition="props.getDefaultCondition"
                    getPathEditorInfo="props.getPathEditorInfo"
                    getOperatorEditorInfo="props.getOperatorEditorInfo"
                    getDefaultOperator="props.getDefaultOperator"
                    isDebugMode="env.debug"
                />
            </div>
        `;
    }
}
```

## 技术特点

### 1. 递归组件设计
- 支持无限层级的嵌套编辑
- 自引用的组件结构
- 智能的渲染优化

### 2. 状态管理
- 不可变的树结构操作
- 智能的变化检测
- 虚拟操作符的状态保持

### 3. 异步数据处理
- 字段定义的异步加载
- 批量的数据获取
- 错误处理和重试机制

### 4. 用户体验优化
- 智能的默认值设置
- 流畅的交互反馈
- 直观的视觉提示

## 设计模式

### 1. 组合模式 (Composite Pattern)
- 统一处理叶子节点和分支节点
- 递归的树结构操作

### 2. 观察者模式 (Observer Pattern)
- 树变化的通知机制
- 父子组件的状态同步

### 3. 策略模式 (Strategy Pattern)
- 不同字段类型的编辑策略
- 可配置的操作符处理

## 注意事项

1. **性能优化**: 避免不必要的重新渲染
2. **内存管理**: 正确清理事件监听器和引用
3. **数据一致性**: 确保树结构的一致性
4. **用户体验**: 提供清晰的操作反馈

## 扩展建议

1. **拖拽支持**: 支持节点的拖拽重排
2. **模板系统**: 常用条件的模板管理
3. **历史记录**: 操作的撤销重做功能
4. **批量编辑**: 多个条件的批量编辑
5. **可视化**: 条件树的图形化显示

该树编辑器组件为Odoo Web应用提供了强大的条件编辑能力，通过递归设计和智能状态管理确保了良好的用户体验和系统性能。

# TreeEditorUtils - 树编辑器工具函数

## 概述

`utils.js` 是 Odoo Web 核心模块的树编辑器工具函数库，提供了条件树处理、字段定义管理、描述生成、值格式化等核心功能。该模块包含了树编辑器系统所需的各种辅助函数，支持树结构分析、字段信息加载、显示名称获取、条件描述生成等功能，为整个树编辑器系统提供了坚实的基础支持。

## 文件信息
- **路径**: `/web/static/src/core/tree_editor/utils.js`
- **行数**: 382
- **模块**: `@web/core/tree_editor/utils`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/arrays'                         // 数组工具函数
'@web/core/tree_editor/tree_editor_operator_editor' // 操作符编辑器
'@web/core/tree_editor/condition_tree'           // 条件树核心
'@web/core/utils/hooks'                          // 工具钩子
'@web/core/l10n/translation'                     // 国际化翻译
'@web/core/l10n/dates'                           // 日期本地化
'@web/core/model_field_selector/utils'           // 字段选择器工具
'@web/core/tree_editor/tree_editor_components'   // 树编辑器组件
```

## 核心工具函数

### 1. 值格式化

```javascript
function formatValue(val, disambiguate, fieldDef, displayNames) {
    if (val instanceof Expression) {
        return val.toString();
    }
    if (displayNames && isId(val)) {
        if (typeof displayNames[val] === "string") {
            val = displayNames[val];
        } else {
            return _t("Inaccessible/missing record ID: %s", val);
        }
    }
    if (fieldDef?.type === "selection") {
        const [, label] = (fieldDef.selection || []).find(([v]) => v === val) || [];
        if (label !== undefined) {
            val = label;
        }
    }
    if (typeof val === "string") {
        if (fieldDef?.type === "datetime") {
            return formatDateTime(deserializeDateTime(val));
        }
        if (fieldDef?.type === "date") {
            return formatDate(deserializeDate(val));
        }
    }
    if (disambiguate && typeof val === "string") {
        return JSON.stringify(val);
    }
    return val;
}
```

**格式化功能**:
- **表达式处理**: 处理Expression对象的字符串化
- **显示名称**: 使用记录的显示名称替换ID
- **选择字段**: 显示选择字段的标签而非值
- **日期格式**: 本地化的日期时间格式化
- **消歧义**: 可选的字符串消歧义处理

### 2. ID验证和消歧义

```javascript
function isId(value) {
    return Number.isInteger(value) && value >= 1;
}

function disambiguate(value, displayNames) {
    if (!Array.isArray(value)) {
        return value === "";
    }
    let hasSomeString = false;
    let hasSomethingElse = false;
    for (const val of value) {
        if (val === "") {
            return true;
        }
        if (typeof val === "string" || (displayNames && isId(val))) {
            hasSomeString = true;
        } else {
            hasSomethingElse = true;
        }
    }
    return hasSomeString && hasSomethingElse;
}
```

**验证消歧义功能**:
- **ID检查**: 验证值是否为有效的记录ID
- **类型混合**: 检测数组中是否包含混合类型
- **空值处理**: 特殊处理空字符串情况
- **显示策略**: 决定是否需要消歧义显示

### 3. 字段定义管理

```javascript
function useMakeGetFieldDef(fieldService) {
    fieldService ||= useService("field");
    const loadFieldInfo = useLoadFieldInfo(fieldService);
    return async (resModel, tree, additionalsPath = []) => {
        const pathsInTree = getPathsInTree(tree);
        const paths = new Set([...pathsInTree, ...additionalsPath]);
        const promises = [];
        const fieldDefs = {};
        for (const path of paths) {
            if (typeof path === "string") {
                promises.push(
                    loadFieldInfo(resModel, path).then(({ fieldDef }) => {
                        fieldDefs[path] = fieldDef;
                    })
                );
            }
        }
        await Promise.all(promises);
        return (path) => {
            if (typeof path === "string") {
                return fieldDefs[path];
            }
            return null;
        };
    };
}
```

**字段定义功能**:
- **批量加载**: 批量加载树中所有字段的定义
- **路径提取**: 自动提取树中使用的字段路径
- **异步处理**: 异步加载字段信息
- **缓存机制**: 返回缓存的字段定义获取函数

## 描述生成系统

### 1. 条件描述生成

```javascript
function useMakeGetConditionDescription(fieldService, nameService) {
    const makeGetPathDescriptions = useGetTreePathDescription(fieldService);
    return async (resModel, tree, getFieldDef) => {
        tree = simplifyTree(tree);
        const [displayNames, getPathDescription] = await Promise.all([
            getDisplayNames(tree, getFieldDef, nameService),
            makeGetPathDescriptions(resModel, tree),
        ]);
        return (node) =>
            _getConditionDescription(node, getFieldDef, getPathDescription, displayNames);
    };
}
```

**条件描述功能**:
- **树简化**: 预处理简化树结构
- **并行加载**: 并行加载显示名称和路径描述
- **描述生成**: 返回条件描述生成函数
- **上下文集成**: 集成字段定义和显示名称

### 2. 详细条件描述

```javascript
function _getConditionDescription(node, getFieldDef, getPathDescription, displayNames) {
    const nodeWithVirtualOperators = createVirtualOperators(node, { getFieldDef });
    const { operator, negate, value, path } = nodeWithVirtualOperators;
    const fieldDef = getFieldDef(path);
    const operatorLabel = getOperatorLabel(operator, fieldDef?.type, negate);
    const pathDescription = getPathDescription(path);
    const description = {
        pathDescription,
        operatorDescription: operatorLabel,
        valueDescription: null,
    };

    // 特殊操作符处理
    if (["set", "not_set"].includes(operator)) {
        return description;
    }
    if (["is", "is_not"].includes(operator)) {
        description.valueDescription = {
            values: [value ? _t("set") : _t("not set")],
            join: "",
            addParenthesis: false,
        };
        return description;
    }

    // 值描述生成
    const coModeldisplayNames = displayNames[getResModel(fieldDef)];
    const dis = disambiguate(value, coModeldisplayNames);
    const values = operator == "within"
        ? [value[0], Within.options.find((option) => option[0] === value[1])[1]]
        : (Array.isArray(value) ? value : [value]).map((val) =>
              formatValue(val, dis, fieldDef, coModeldisplayNames)
          );
    
    // 连接符处理
    let join;
    let addParenthesis = Array.isArray(value);
    switch (operator) {
        case "between":
            join = _t("and");
            addParenthesis = false;
            break;
        case "within":
            join = " ";
            addParenthesis = false;
            break;
        case "in":
        case "not in":
            join = ",";
            break;
        default:
            join = _t("or");
    }
    description.valueDescription = { values, join, addParenthesis };
    return description;
}
```

**详细描述功能**:
- **虚拟操作符**: 处理虚拟操作符的描述
- **路径描述**: 生成字段路径的人类可读描述
- **操作符标签**: 生成操作符的本地化标签
- **值描述**: 智能生成值的描述信息
- **连接处理**: 根据操作符选择合适的连接符

### 3. 树描述生成

```javascript
function useGetTreeDescription(fieldService, nameService) {
    fieldService ||= useService("field");
    nameService ||= useService("name");
    const makeGetFieldDef = useMakeGetFieldDef(fieldService);
    const makeGetConditionDescription = useMakeGetConditionDescription(fieldService, nameService);
    return async (resModel, tree) => {
        async function getTreeDescription(resModel, tree, isSubExpression = false) {
            tree = simplifyTree(tree);
            if (tree.type === "connector") {
                const childDescriptions = tree.children.map((node) =>
                    getTreeDescription(resModel, node, true)
                );
                const separator = tree.value === "&" ? _t("and") : _t("or");
                let description = await Promise.all(childDescriptions);
                description = description.join(` ${separator} `);
                if (isSubExpression || tree.negate) {
                    description = `( ${description} )`;
                }
                if (tree.negate) {
                    description = `! ${description}`;
                }
                return description;
            }
            // 条件节点处理...
            const getFieldDef = await makeGetFieldDef(resModel, tree);
            const getConditionDescription = await makeGetConditionDescription(
                resModel,
                tree,
                getFieldDef
            );
            const { pathDescription, operatorDescription, valueDescription } =
                getConditionDescription(tree);
            const stringDescription = [pathDescription, operatorDescription];
            if (valueDescription) {
                const { values, join, addParenthesis } = valueDescription;
                const jointedValues = values.join(` ${join} `);
                stringDescription.push(addParenthesis ? `( ${jointedValues} )` : jointedValues);
            }
            return stringDescription.join(" ");
        }
        return getTreeDescription(resModel, tree);
    };
}
```

**树描述功能**:
- **递归处理**: 递归处理树的所有节点
- **连接器处理**: 处理AND/OR连接器的描述
- **括号管理**: 智能添加括号避免歧义
- **取反处理**: 处理取反操作的描述
- **完整描述**: 生成完整的树描述文本

## 辅助工具函数

### 1. 模型关系

```javascript
function getResModel(fieldDef) {
    if (fieldDef) {
        return fieldDef.is_property ? fieldDef.comodel : fieldDef.relation;
    }
    return null;
}
```

**模型关系功能**:
- **关联模型**: 获取字段的关联模型
- **属性字段**: 处理属性字段的特殊情况
- **空值处理**: 安全处理空字段定义

### 2. 路径提取

```javascript
function getPathsInTree(tree) {
    const paths = [];
    if (tree.type === "condition") {
        paths.push(tree.path);
    }
    if (tree.type === "connector" && tree.children) {
        for (const child of tree.children) {
            paths.push(...getPathsInTree(child));
        }
    }
    return unique(paths);
}
```

**路径提取功能**:
- **递归遍历**: 递归遍历树的所有节点
- **路径收集**: 收集所有条件节点的路径
- **去重处理**: 自动去除重复的路径
- **扁平化**: 返回扁平化的路径数组

### 3. 默认路径选择

```javascript
const SPECIAL_FIELDS = ["country_id", "user_id", "partner_id", "stage_id", "id"];

function getDefaultPath(fieldDefs) {
    for (const name of SPECIAL_FIELDS) {
        const fieldDef = fieldDefs[name];
        if (fieldDef) {
            return fieldDef.name;
        }
    }
    const name = Object.keys(fieldDefs)[0];
    if (name) {
        return name;
    }
    throw new Error(`No field found`);
}
```

**默认路径功能**:
- **优先字段**: 优先选择常用的特殊字段
- **回退机制**: 没有特殊字段时选择第一个字段
- **错误处理**: 没有字段时抛出错误
- **智能选择**: 提供合理的默认字段选择

### 4. 树简化

```javascript
function simplifyTree(tree) {
    if (tree.type === "condition") {
        return tree;
    }
    const processedChildren = tree.children.map(simplifyTree);
    if (tree.value === "&") {
        return { ...tree, children: processedChildren };
    }
    
    // OR连接器的优化处理
    const children = [];
    const childrenByPath = {};
    for (const child of processedChildren) {
        if (
            child.type === "connector" ||
            typeof child.path !== "string" ||
            !["=", "in"].includes(child.operator)
        ) {
            children.push(child);
        } else {
            if (!childrenByPath[child.path]) {
                childrenByPath[child.path] = [];
            }
            childrenByPath[child.path].push(child);
        }
    }
    
    // 合并相同路径的条件
    for (const path in childrenByPath) {
        if (childrenByPath[path].length === 1) {
            children.push(childrenByPath[path][0]);
            continue;
        }
        const value = [];
        for (const child of childrenByPath[path]) {
            if (child.operator === "=") {
                value.push(child.value);
            } else {
                value.push(...child.value);
            }
        }
        children.push(condition(path, "in", normalizeValue(value)));
    }
    
    if (children.length === 1) {
        return { ...children[0] };
    }
    return { ...tree, children };
}
```

**树简化功能**:
- **递归简化**: 递归简化所有子树
- **条件合并**: 合并相同路径的多个条件
- **操作符优化**: 将多个等于条件合并为in条件
- **结构优化**: 简化单子节点的连接器
- **值标准化**: 标准化合并后的值

该工具函数库为Odoo Web树编辑器系统提供了完整的基础支持，通过智能的字段管理、描述生成和树优化确保了系统的高效运行和良好的用户体验。

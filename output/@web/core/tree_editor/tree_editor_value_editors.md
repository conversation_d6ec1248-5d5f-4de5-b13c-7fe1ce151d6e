# TreeEditorValueEditors - 值编辑器系统

## 概述

`tree_editor_value_editors.js` 是 Odoo Web 核心模块的值编辑器系统，提供了条件树中各种字段类型值的编辑功能。该模块根据字段类型和操作符动态选择合适的编辑器组件，支持字符串、数字、日期、布尔值、关联字段等多种数据类型的编辑，为树编辑器提供了完整的值输入解决方案，确保了条件构建过程中值编辑的准确性和用户体验。

## 文件信息
- **路径**: `/web/static/src/core/tree_editor/tree_editor_value_editors.js`
- **行数**: 343
- **模块**: `@web/core/tree_editor/tree_editor_value_editors`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/dates'                           // 日期本地化
'@web/core/l10n/translation'                     // 国际化翻译
'@web/core/registry'                             // 注册表系统
'@web/core/datetime/datetime_input'              // 日期时间输入组件
'@web/core/tree_editor/tree_editor_autocomplete' // 自动完成组件
'@web/core/utils/arrays'                         // 数组工具函数
'@web/core/tree_editor/tree_editor_components'   // 基础组件
'@web/core/tree_editor/condition_tree'           // 条件树核心
'@web/core/tree_editor/utils'                    // 工具函数
```

## 核心工具函数

### 1. 值解析和验证

```javascript
// 解析值
function parseValue(fieldType, value) {
    const parser = parsers.get(fieldType, (value) => value);
    try {
        return parser(value);
    } catch {
        return value;
    }
}

// 检查值是否可解析
function isParsable(fieldType, value) {
    const parser = parsers.get(fieldType, (value) => value);
    try {
        parser(value);
    } catch {
        return false;
    }
    return true;
}
```

**解析验证功能**:
- **类型解析**: 根据字段类型解析值
- **错误处理**: 安全的解析错误处理
- **验证检查**: 检查值是否符合类型要求
- **注册表集成**: 使用系统注册的解析器

### 2. 日期序列化

```javascript
// 通用日期序列化
function genericSerializeDate(type, value) {
    return type === "date" ? serializeDate(value) : serializeDateTime(value);
}

// 通用日期反序列化
function genericDeserializeDate(type, value) {
    return type === "date" ? deserializeDate(value) : deserializeDateTime(value);
}
```

**日期处理功能**:
- **类型区分**: 区分日期和日期时间类型
- **序列化**: 将日期对象转换为字符串
- **反序列化**: 将字符串转换为日期对象
- **本地化**: 支持本地化的日期格式

## 基础编辑器

### 1. 字符串编辑器

```javascript
const STRING_EDITOR = {
    component: Input,
    extractProps: ({ value, update }) => ({ value, update }),
    isSupported: (value) => typeof value === "string",
    defaultValue: () => "",
};
```

**字符串编辑器功能**:
- **简单输入**: 使用基础Input组件
- **类型检查**: 验证值为字符串类型
- **默认值**: 提供空字符串默认值
- **直接绑定**: 直接的值绑定和更新

### 2. 选择器编辑器工厂

```javascript
function makeSelectEditor(options, params = {}) {
    const getOption = (value) => options.find(([v]) => v === value) || null;
    return {
        component: Select,
        extractProps: ({ value, update }) => ({
            value,
            update,
            options,
            addBlankOption: params.addBlankOption,
        }),
        isSupported: (value) => Boolean(getOption(value)),
        defaultValue: () => options[0]?.[0] ?? false,
        stringify: (value, disambiguate) => {
            const option = getOption(value);
            return option ? option[1] : disambiguate ? formatValue(value) : String(value);
        },
        message: _t("Value not in selection"),
    };
}
```

**选择器编辑器功能**:
- **选项配置**: 支持自定义选项列表
- **值验证**: 检查值是否在选项中
- **字符串化**: 智能的值字符串表示
- **空选项**: 可选的空白选项支持
- **错误消息**: 提供验证错误消息

### 3. 自动完成编辑器工厂

```javascript
function makeAutoCompleteEditor(fieldDef) {
    return {
        component: DomainSelectorAutocomplete,
        extractProps: ({ value, update }) => {
            return {
                resModel: getResModel(fieldDef),
                fieldString: fieldDef.string,
                update: (value) => update(unique(value)),
                resIds: unique(value),
            };
        },
        isSupported: (value) => Array.isArray(value),
        defaultValue: () => [],
    };
}
```

**自动完成编辑器功能**:
- **模型集成**: 根据字段定义获取关联模型
- **去重处理**: 自动去除重复的记录ID
- **数组支持**: 支持多值选择
- **字段信息**: 传递字段字符串信息

## 操作符特定编辑器

### 1. 设置状态操作符

```javascript
case "set":
case "not_set":
    return {
        component: null,
        extractProps: null,
        isSupported: (value) => value === false,
        defaultValue: () => false,
    };
```

**设置状态功能**:
- **无组件**: 不需要用户输入组件
- **固定值**: 值固定为false
- **状态检查**: 检查字段是否设置

### 2. 范围操作符

```javascript
case "between": {
    const editorInfo = getValueEditorInfo(fieldDef, "=");
    return {
        component: Range,
        extractProps: ({ value, update }) => ({
            value,
            update,
            editorInfo,
        }),
        isSupported: (value) => Array.isArray(value) && value.length === 2,
        defaultValue: () => {
            const { defaultValue } = editorInfo;
            return [defaultValue(), defaultValue()];
        },
    };
}
```

**范围编辑器功能**:
- **双值输入**: 支持开始和结束值
- **递归配置**: 使用基础字段的编辑器配置
- **数组验证**: 验证值为长度为2的数组
- **默认值生成**: 自动生成合适的默认范围

### 3. 时间范围操作符

```javascript
case "within": {
    return {
        component: Within,
        extractProps: ({ value, update }) => ({
            value,
            update,
            amountEditorInfo: getValueEditorInfo({ type: "integer" }, "="),
            optionEditorInfo: makeSelectEditor(Within.options),
        }),
        isSupported: (value) =>
            Array.isArray(value) &&
            value.length === 3 &&
            typeof value[1] === "string" &&
            value[2] === fieldDef.type,
        defaultValue: () => {
            return [-1, "months", fieldDef.type];
        },
    };
}
```

**时间范围功能**:
- **组合输入**: 数量和时间单位的组合
- **类型验证**: 严格的值结构验证
- **字段类型**: 保存字段类型信息
- **默认设置**: 合理的默认时间范围

## 字段类型编辑器

### 1. 数值字段

```javascript
case "integer":
case "float":
case "monetary": {
    const formatType = type === "integer" ? "integer" : "float";
    return {
        component: Input,
        extractProps: ({ value, update }) => ({
            value: String(value),
            update: (value) => update(parseValue(formatType, value)),
            startEmpty: params.startEmpty,
        }),
        isSupported: () => true,
        defaultValue: () => 1,
        shouldResetValue: (value) => parseValue(formatType, value) === value,
    };
}
```

**数值编辑器功能**:
- **类型转换**: 自动的字符串和数值转换
- **解析验证**: 使用注册的解析器验证
- **重置检查**: 检查是否需要重置值
- **空值支持**: 可选的空值开始状态

### 2. 日期时间字段

```javascript
case "date":
case "datetime":
    return {
        component: DateTimeInput,
        extractProps: ({ value, update }) => ({
            value:
                params.startEmpty || value === false
                    ? false
                    : genericDeserializeDate(type, value),
            type,
            onApply: (value) => {
                if (!params.startEmpty || value) {
                    update(genericSerializeDate(type, value || DateTime.local()));
                }
            },
        }),
        isSupported: (value) =>
            value === false || (typeof value === "string" && isParsable(type, value)),
        defaultValue: () => genericSerializeDate(type, DateTime.local()),
        stringify: (value) => {
            if (value === false) {
                return _t("False");
            }
            if (typeof value === "string" && isParsable(type, value)) {
                const formatter = formatters.get(type, formatValue);
                return formatter(genericDeserializeDate(type, value));
            }
            return formatValue(value);
        },
        message: _t("Not a valid %s", type),
    };
```

**日期时间编辑器功能**:
- **专用组件**: 使用DateTimeInput组件
- **序列化处理**: 自动的序列化和反序列化
- **格式化显示**: 本地化的日期格式显示
- **验证消息**: 清晰的验证错误消息

### 3. 布尔字段

```javascript
case "boolean": {
    if (["is", "is_not"].includes(operator)) {
        const options = [
            [true, _t("set")],
            [false, _t("not set")],
        ];
        return makeSelectEditor(options, params);
    }
    const options = [
        [true, _t("True")],
        [false, _t("False")],
    ];
    return makeSelectEditor(options, params);
}
```

**布尔编辑器功能**:
- **操作符感知**: 根据操作符调整选项
- **语义选项**: 提供语义化的选项标签
- **国际化**: 选项标签支持多语言
- **选择器复用**: 复用选择器编辑器逻辑

### 4. 关联字段

```javascript
case "many2one": {
    if (["=", "!=", "parent_of", "child_of"].includes(operator)) {
        return {
            component: DomainSelectorSingleAutocomplete,
            extractProps: ({ value, update }) => {
                return {
                    resModel: getResModel(fieldDef),
                    fieldString: fieldDef.string,
                    update,
                    resId: value,
                };
            },
            isSupported: () => true,
            defaultValue: () => false,
            shouldResetValue: (value) => value !== false && !isId(value),
        };
    }
    break;
}
```

**关联字段功能**:
- **单记录选择**: 使用单记录自动完成组件
- **模型关联**: 自动获取关联模型信息
- **ID验证**: 验证记录ID的有效性
- **重置逻辑**: 智能的值重置判断

## 主要API函数

### 1. getValueEditorInfo

```javascript
function getValueEditorInfo(fieldDef, operator, options = {}) {
    const info = getPartialValueEditorInfo(fieldDef || {}, operator, options);
    return {
        extractProps: ({ value, update }) => ({ value, update }),
        message: _t("Value not supported"),
        stringify: (val, disambiguate = true) => {
            if (disambiguate) {
                return formatValue(val);
            }
            return String(val);
        },
        ...info,
    };
}
```

**API功能**:
- **编辑器配置**: 获取完整的编辑器配置信息
- **默认属性**: 提供默认的属性提取函数
- **错误消息**: 提供默认的错误消息
- **字符串化**: 提供默认的值字符串化函数

### 2. getDefaultValue

```javascript
function getDefaultValue(fieldDef, operator, value = null) {
    const { isSupported, shouldResetValue, defaultValue } = getValueEditorInfo(fieldDef, operator);
    if (value === null || !isSupported(value) || shouldResetValue?.(value)) {
        return defaultValue();
    }
    return value;
}
```

**默认值功能**:
- **智能判断**: 智能判断是否需要使用默认值
- **支持检查**: 检查当前值是否被支持
- **重置检查**: 检查是否需要重置值
- **保持原值**: 在可能的情况下保持原有值

该值编辑器系统为Odoo Web应用提供了完整的条件值编辑能力，通过智能的类型检测和组件选择确保了各种字段类型值编辑的准确性和用户体验。

# DomainSelectorDialog - 域选择器对话框

## 概述

`domain_selector_dialog.js` 是 Odoo Web 核心模块的域选择器对话框组件，提供了模态对话框形式的域编辑功能。该组件封装了域选择器，支持域的可视化编辑、验证和确认，为用户提供了便捷的域配置界面，广泛应用于过滤器设置、搜索条件配置、业务规则定义等需要弹窗式域编辑的场景。

## 文件信息
- **路径**: `/web/static/src/core/domain_selector_dialog/domain_selector_dialog.js`
- **行数**: 116
- **模块**: `@web/core/domain_selector_dialog/domain_selector_dialog`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'                     // 国际化翻译
'@odoo/owl'                                      // OWL框架
'@web/core/dialog/dialog'                        // 对话框组件
'@web/core/domain'                               // 域处理核心
'@web/core/domain_selector/domain_selector'      // 域选择器
'@web/core/network/rpc'                          // RPC网络请求
'@web/core/utils/hooks'                          // 工具钩子
'@web/core/user'                                 // 用户信息
```

## 主组件类

### 1. 组件属性定义

```javascript
static props = {
    close: Function,                    // 关闭对话框回调
    onConfirm: Function,               // 确认回调
    resModel: String,                  // 资源模型名称
    className: { type: String, optional: true },        // CSS类名
    defaultConnector: { type: [{ value: "&" }, { value: "|" }], optional: true }, // 默认连接器
    domain: String,                    // 初始域字符串
    isDebugMode: { type: Boolean, optional: true },     // 调试模式
    readonly: { type: Boolean, optional: true },        // 只读模式
    text: { type: String, optional: true },             // 对话框文本
    confirmButtonText: { type: String, optional: true }, // 确认按钮文本
    disableConfirmButton: { type: Function, optional: true }, // 禁用确认按钮函数
    discardButtonText: { type: String, optional: true }, // 取消按钮文本
    title: { type: String, optional: true },            // 对话框标题
    context: { type: Object, optional: true },          // 上下文对象
};

static defaultProps = {
    isDebugMode: false,
    readonly: false,
    context: {},
};
```

**属性功能**:
- **回调函数**: close和onConfirm处理对话框的关闭和确认
- **域配置**: domain、resModel等配置域编辑
- **界面定制**: title、text、按钮文本等定制界面
- **行为控制**: readonly、isDebugMode等控制行为
- **上下文**: context提供域验证的上下文信息

### 2. 组件初始化

```javascript
setup() {
    this.notification = useService("notification");
    this.orm = useService("orm");
    this.state = useState({ domain: this.props.domain });
    this.confirmButtonRef = useRef("confirm");
}
```

**初始化功能**:
- **服务注入**: 注入通知和ORM服务
- **状态管理**: 管理域的编辑状态
- **引用管理**: 获取确认按钮的引用
- **初始值**: 设置域的初始值

### 3. 计算属性

```javascript
// 确认按钮文本
get confirmButtonText() {
    return this.props.confirmButtonText || _t("Confirm");
}

// 对话框标题
get dialogTitle() {
    return this.props.title || _t("Domain");
}

// 确认按钮禁用状态
get disabled() {
    if (this.props.disableConfirmButton) {
        return this.props.disableConfirmButton(this.state.domain);
    }
    return false;
}

// 取消按钮文本
get discardButtonText() {
    return this.props.discardButtonText || _t("Discard");
}

// 域选择器属性
get domainSelectorProps() {
    return {
        className: this.props.className,
        resModel: this.props.resModel,
        readonly: this.props.readonly,
        isDebugMode: this.props.isDebugMode,
        defaultConnector: this.props.defaultConnector,
        domain: this.state.domain,
        update: (domain) => {
            this.state.domain = domain;
        },
    };
}
```

**计算属性功能**:
- **文本国际化**: 提供默认的国际化文本
- **状态计算**: 动态计算按钮状态
- **属性传递**: 构建域选择器所需属性
- **更新处理**: 处理域的更新逻辑

## 核心方法

### 1. 确认处理

```javascript
async onConfirm() {
    this.confirmButtonRef.el.disabled = true;
    let domain;
    let isValid;
    
    try {
        const evalContext = { ...user.context, ...this.props.context };
        domain = new Domain(this.state.domain).toList(evalContext);
    } catch {
        isValid = false;
    }
    
    if (isValid === undefined) {
        isValid = await rpc("/web/domain/validate", {
            model: this.props.resModel,
            domain,
        });
    }
    
    if (!isValid) {
        if (this.confirmButtonRef.el) {
            this.confirmButtonRef.el.disabled = false;
        }
        this.notification.add(_t("Domain is invalid. Please correct it"), {
            type: "danger",
        });
        return;
    }
    
    this.props.onConfirm(this.state.domain);
    this.props.close();
}
```

**确认处理功能**:
- **按钮禁用**: 防止重复提交
- **域解析**: 解析域字符串为列表格式
- **上下文合并**: 合并用户和传入的上下文
- **服务器验证**: 通过RPC验证域的有效性
- **错误处理**: 显示验证错误信息
- **成功回调**: 验证成功后调用确认回调

### 2. 取消处理

```javascript
onDiscard() {
    this.props.close();
}
```

**取消处理功能**:
- **简单关闭**: 直接关闭对话框
- **无状态保存**: 不保存任何编辑状态

## 使用场景

### 1. 基础域编辑对话框

```javascript
// 基础域编辑对话框使用
class BasicDomainEditor extends Component {
    setup() {
        this.dialog = useService('dialog');
        this.state = useState({
            currentDomain: "[]"
        });
    }

    openDomainDialog() {
        this.dialog.add(DomainSelectorDialog, {
            resModel: "res.partner",
            domain: this.state.currentDomain,
            title: "编辑过滤条件",
            onConfirm: (domain) => this.onDomainConfirmed(domain),
            confirmButtonText: "应用",
            discardButtonText: "取消"
        });
    }

    onDomainConfirmed(domain) {
        this.state.currentDomain = domain;
        console.log('Domain confirmed:', domain);
        // 处理确认的域
    }

    render() {
        return xml`
            <div class="basic-domain-editor">
                <button 
                    class="btn btn-primary"
                    t-on-click="openDomainDialog"
                >
                    编辑过滤条件
                </button>
                <div class="current-domain mt-2">
                    <small class="text-muted">当前域: </small>
                    <code t-esc="state.currentDomain"/>
                </div>
            </div>
        `;
    }
}
```

### 2. 高级过滤器管理器

```javascript
// 高级过滤器管理器
class AdvancedFilterManager extends Component {
    setup() {
        this.dialog = useService('dialog');
        this.notification = useService('notification');
        this.orm = useService('orm');
        
        this.state = useState({
            filters: [],
            selectedFilter: null
        });

        this.loadFilters();
    }

    async loadFilters() {
        try {
            const filters = await this.orm.searchRead(
                'ir.filters',
                [['model_id', '=', 'res.partner']],
                ['name', 'domain', 'user_id']
            );
            this.state.filters = filters;
        } catch (error) {
            this.notification.add('加载过滤器失败', { type: 'danger' });
        }
    }

    openCreateFilterDialog() {
        this.dialog.add(DomainSelectorDialog, {
            resModel: "res.partner",
            domain: "[]",
            title: "创建新过滤器",
            text: "请配置过滤条件",
            onConfirm: (domain) => this.createFilter(domain),
            confirmButtonText: "创建",
            discardButtonText: "取消",
            isDebugMode: this.env.debug
        });
    }

    openEditFilterDialog(filter) {
        this.dialog.add(DomainSelectorDialog, {
            resModel: "res.partner",
            domain: filter.domain,
            title: `编辑过滤器: ${filter.name}`,
            onConfirm: (domain) => this.updateFilter(filter.id, domain),
            confirmButtonText: "更新",
            discardButtonText: "取消",
            disableConfirmButton: (domain) => domain === filter.domain,
            isDebugMode: this.env.debug
        });
    }

    async createFilter(domain) {
        const name = prompt('请输入过滤器名称:');
        if (!name) return;

        try {
            const filterId = await this.orm.create('ir.filters', [{
                name: name,
                model_id: 'res.partner',
                domain: domain,
                user_id: this.env.user.userId
            }]);

            this.notification.add('过滤器创建成功', { type: 'success' });
            this.loadFilters();
        } catch (error) {
            this.notification.add('创建过滤器失败', { type: 'danger' });
        }
    }

    async updateFilter(filterId, domain) {
        try {
            await this.orm.write('ir.filters', [filterId], {
                domain: domain
            });

            this.notification.add('过滤器更新成功', { type: 'success' });
            this.loadFilters();
        } catch (error) {
            this.notification.add('更新过滤器失败', { type: 'danger' });
        }
    }

    async deleteFilter(filterId) {
        if (!confirm('确定要删除这个过滤器吗？')) return;

        try {
            await this.orm.unlink('ir.filters', [filterId]);
            this.notification.add('过滤器删除成功', { type: 'success' });
            this.loadFilters();
        } catch (error) {
            this.notification.add('删除过滤器失败', { type: 'danger' });
        }
    }

    render() {
        return xml`
            <div class="advanced-filter-manager">
                <div class="manager-header mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>过滤器管理</h5>
                        <button 
                            class="btn btn-primary"
                            t-on-click="openCreateFilterDialog"
                        >
                            <i class="fa fa-plus"/> 创建过滤器
                        </button>
                    </div>
                </div>

                <div class="filters-list">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>名称</th>
                                    <th>域</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-foreach="state.filters" t-as="filter" t-key="filter.id">
                                    <tr>
                                        <td t-esc="filter.name"/>
                                        <td>
                                            <code class="small" t-esc="filter.domain"/>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <button 
                                                    class="btn btn-sm btn-outline-primary"
                                                    t-on-click="() => this.openEditFilterDialog(filter)"
                                                >
                                                    <i class="fa fa-edit"/> 编辑
                                                </button>
                                                <button 
                                                    class="btn btn-sm btn-outline-danger"
                                                    t-on-click="() => this.deleteFilter(filter.id)"
                                                >
                                                    <i class="fa fa-trash"/> 删除
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </t>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="empty-state text-center py-5" t-if="!state.filters.length">
                    <i class="fa fa-filter fa-3x text-muted mb-3"/>
                    <h6 class="text-muted">暂无过滤器</h6>
                    <p class="text-muted">点击上方按钮创建第一个过滤器</p>
                </div>
            </div>
        `;
    }
}
```

### 3. 条件验证对话框

```javascript
// 条件验证对话框
class ConditionalValidationDialog extends Component {
    setup() {
        this.dialog = useService('dialog');
        this.state = useState({
            validationRules: []
        });
    }

    openValidationRuleDialog(rule = null) {
        const isEdit = Boolean(rule);
        
        this.dialog.add(DomainSelectorDialog, {
            resModel: "sale.order",
            domain: rule ? rule.domain : "[]",
            title: isEdit ? "编辑验证规则" : "创建验证规则",
            text: "请设置触发此验证规则的条件",
            onConfirm: (domain) => this.saveValidationRule(domain, rule),
            confirmButtonText: isEdit ? "更新规则" : "创建规则",
            discardButtonText: "取消",
            disableConfirmButton: (domain) => {
                // 禁用空域的确认
                return domain === "[]";
            },
            context: {
                // 提供额外的上下文用于域验证
                active_test: false,
                lang: this.env.user.lang
            }
        });
    }

    saveValidationRule(domain, existingRule) {
        if (existingRule) {
            // 更新现有规则
            existingRule.domain = domain;
            this.notification.add('验证规则更新成功', { type: 'success' });
        } else {
            // 创建新规则
            const newRule = {
                id: Date.now(),
                name: `规则 ${this.state.validationRules.length + 1}`,
                domain: domain,
                active: true
            };
            this.state.validationRules.push(newRule);
            this.notification.add('验证规则创建成功', { type: 'success' });
        }
    }

    testRule(rule) {
        // 测试规则的对话框
        this.dialog.add(DomainSelectorDialog, {
            resModel: "sale.order",
            domain: rule.domain,
            title: `测试规则: ${rule.name}`,
            text: "此规则将应用于以下条件的记录",
            readonly: true,
            confirmButtonText: "关闭",
            onConfirm: () => {}, // 只是查看，不需要处理
        });
    }

    render() {
        return xml`
            <div class="conditional-validation-dialog">
                <div class="header mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>条件验证规则</h5>
                        <button 
                            class="btn btn-primary"
                            t-on-click="() => this.openValidationRuleDialog()"
                        >
                            <i class="fa fa-plus"/> 添加规则
                        </button>
                    </div>
                </div>

                <div class="rules-list">
                    <t t-foreach="state.validationRules" t-as="rule" t-key="rule.id">
                        <div class="rule-item card mb-2">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="rule-info">
                                        <h6 class="card-title" t-esc="rule.name"/>
                                        <p class="card-text">
                                            <small class="text-muted">条件: </small>
                                            <code t-esc="rule.domain"/>
                                        </p>
                                    </div>
                                    <div class="rule-actions">
                                        <div class="btn-group">
                                            <button 
                                                class="btn btn-sm btn-outline-info"
                                                t-on-click="() => this.testRule(rule)"
                                            >
                                                <i class="fa fa-eye"/> 查看
                                            </button>
                                            <button 
                                                class="btn btn-sm btn-outline-primary"
                                                t-on-click="() => this.openValidationRuleDialog(rule)"
                                            >
                                                <i class="fa fa-edit"/> 编辑
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 对话框封装
- 完整的模态对话框功能
- 标准的确认/取消流程
- 可定制的界面文本

### 2. 域验证
- 客户端域解析验证
- 服务器端域有效性验证
- 上下文感知的验证

### 3. 状态管理
- 独立的域编辑状态
- 按钮状态的动态控制
- 错误状态的处理

### 4. 用户体验
- 防重复提交机制
- 清晰的错误提示
- 国际化的界面文本

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 对域选择器的对话框装饰
- 增加验证和确认功能

### 2. 策略模式 (Strategy Pattern)
- 可配置的确认按钮禁用策略
- 灵活的验证策略

### 3. 观察者模式 (Observer Pattern)
- 域变化的监听和响应
- 状态变化的通知机制

## 注意事项

1. **验证机制**: 确保域的客户端和服务器端验证
2. **用户体验**: 提供清晰的错误提示和状态反馈
3. **性能优化**: 避免频繁的验证请求
4. **错误处理**: 妥善处理网络错误和验证失败

## 扩展建议

1. **预设模板**: 常用域的预设模板
2. **历史记录**: 域编辑的历史记录
3. **批量验证**: 多个域的批量验证
4. **导入导出**: 域配置的导入导出
5. **实时预览**: 域效果的实时预览

该域选择器对话框为Odoo Web应用提供了便捷的模态域编辑功能，通过完整的验证机制和用户友好的界面确保了域配置的准确性和易用性。

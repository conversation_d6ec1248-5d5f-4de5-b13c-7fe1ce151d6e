# AutoComplete - 自动完成组件

## 概述

`autocomplete.js` 是 Odoo Web 核心模块的自动完成组件，专门提供智能的输入建议和选择功能。该组件支持多数据源、异步加载、键盘导航、防抖输入和灵活的模板定制，广泛应用于搜索框、选择器、标签输入等需要智能提示的用户界面场景。

## 文件信息
- **路径**: `/web/static/src/core/autocomplete/autocomplete.js`
- **行数**: 433
- **模块**: `@web/core/autocomplete/autocomplete`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/concurrency'       // 并发工具
'@web/core/utils/hooks'             // 钩子工具
'@web/core/utils/timing'            // 时间工具
'@web/core/hotkeys/hotkey_service'  // 快捷键服务
'@web/core/position/position_hook'  // 位置钩子
'@odoo/owl'                         // OWL框架
```

## 核心功能

### 1. 组件属性定义

```javascript
static props = {
    value: { type: String, optional: true },
    id: { type: String, optional: true },
    onSelect: { type: Function },
    sources: {
        type: Array,
        element: {
            type: Object,
            shape: {
                placeholder: { type: String, optional: true },
                optionTemplate: { type: String, optional: true },
                options: [Array, Function],
            },
        },
    },
    placeholder: { type: String, optional: true },
    autoSelect: { type: Boolean, optional: true },
    resetOnSelect: { type: Boolean, optional: true },
    onInput: { type: Function, optional: true },
    onCancel: { type: Function, optional: true },
    onChange: { type: Function, optional: true },
    onBlur: { type: Function, optional: true },
    onFocus: { type: Function, optional: true },
    input: { type: Function, optional: true },
    dropdown: { type: Boolean, optional: true },
    autofocus: { type: Boolean, optional: true },
    class: { type: String, optional: true },
};
```

**属性功能**:
- **数据源配置**: 支持多个数据源和自定义模板
- **事件回调**: 完整的事件生命周期回调
- **行为控制**: 自动选择、重置等行为配置
- **样式定制**: 支持自定义CSS类和下拉框模式

### 2. 组件初始化

```javascript
setup() {
    this.nextSourceId = 0;
    this.nextOptionId = 0;
    this.sources = [];
    this.inEdition = false;
    this.timeout = 250;

    this.state = useState({
        navigationRev: 0,
        optionsRev: 0,
        open: false,
        activeSourceOption: null,
        value: this.props.value,
    });

    this.inputRef = useForwardRefToParent("input");
    if (this.props.autofocus) {
        useAutofocus({ refName: "input" });
    }
    this.root = useRef("root");

    this.debouncedProcessInput = useDebounced(async () => {
        // 防抖输入处理逻辑
    }, this.timeout);

    useExternalListener(window, "scroll", this.externalClose, true);
    useExternalListener(window, "pointerdown", this.externalClose, true);
}
```

**初始化功能**:
- **状态管理**: 使用useState管理组件状态
- **引用管理**: 创建输入框和根元素引用
- **防抖处理**: 使用防抖优化输入性能
- **外部监听**: 监听外部事件自动关闭下拉框
- **自动聚焦**: 支持自动聚焦功能

### 3. 数据源加载

```javascript
async loadSources(useInput) {
    this.sources = [];
    this.state.activeSourceOption = null;
    const proms = [];
    for (const pSource of this.props.sources) {
        const source = this.makeSource(pSource);
        this.sources.push(source);

        const options = this.loadOptions(
            pSource.options,
            useInput ? this.inputRef.el.value.trim() : ""
        );
        if (options instanceof Promise) {
            source.isLoading = true;
            const prom = options.then((options) => {
                source.options = options.map((option) => this.makeOption(option));
                source.isLoading = false;
                this.state.optionsRev++;
            });
            proms.push(prom);
        } else {
            source.options = options.map((option) => this.makeOption(option));
        }
    }

    await Promise.all(proms);
    this.navigate(0);
}
```

**数据源功能**:
- **多源支持**: 支持多个数据源同时加载
- **异步加载**: 支持异步数据源和Promise
- **加载状态**: 管理数据源的加载状态
- **选项处理**: 为每个选项分配唯一ID

### 4. 键盘导航

```javascript
navigate(direction) {
    let step = Math.sign(direction);
    if (!step) {
        this.state.activeSourceOption = null;
        step = 1;
    } else {
        this.state.navigationRev++;
    }

    if (this.state.activeSourceOption) {
        let [sourceIndex, optionIndex] = this.state.activeSourceOption;
        let source = this.sources[sourceIndex];

        optionIndex += step;
        if (0 > optionIndex || optionIndex >= source.options.length) {
            sourceIndex += step;
            source = this.sources[sourceIndex];

            while (source && source.isLoading) {
                sourceIndex += step;
                source = this.sources[sourceIndex];
            }

            if (source) {
                optionIndex = step < 0 ? source.options.length - 1 : 0;
            }
        }

        this.state.activeSourceOption = source ? [sourceIndex, optionIndex] : null;
    }
    // ... 其他导航逻辑
}
```

**导航功能**:
- **方向控制**: 支持上下方向键导航
- **跨源导航**: 支持在多个数据源间导航
- **边界处理**: 处理导航的边界情况
- **加载跳过**: 跳过正在加载的数据源

### 5. 键盘事件处理

```javascript
async onInputKeydown(ev) {
    const hotkey = getActiveHotkey(ev);
    const isSelectKey = hotkey === "enter" || hotkey === "tab";

    if (this.loadingPromise && isSelectKey) {
        if (hotkey === "enter") {
            ev.stopPropagation();
            ev.preventDefault();
        }
        await this.loadingPromise;
    }

    switch (hotkey) {
        case "enter":
            if (!this.isOpened || !this.state.activeSourceOption) {
                return;
            }
            this.selectOption(this.state.activeSourceOption);
            break;
        case "escape":
            if (!this.isOpened) {
                return;
            }
            this.cancel();
            break;
        case "tab":
            if (!this.isOpened) {
                return;
            }
            if (
                this.props.autoSelect &&
                this.state.activeSourceOption &&
                (this.state.navigationRev > 0 || this.inputRef.el.value.length > 0)
            ) {
                this.selectOption(this.state.activeSourceOption);
            }
            this.close();
            return;
        case "arrowup":
            this.navigate(-1);
            if (!this.isOpened) {
                this.open(true);
            }
            break;
        case "arrowdown":
            this.navigate(+1);
            if (!this.isOpened) {
                this.open(true);
            }
            break;
        default:
            return;
    }

    ev.stopPropagation();
    ev.preventDefault();
}
```

**键盘事件功能**:
- **快捷键支持**: 支持Enter、Escape、Tab、方向键
- **异步等待**: 等待加载完成后再处理选择
- **自动选择**: 支持Tab键自动选择
- **事件阻止**: 阻止默认行为和事件冒泡

### 6. 选项选择

```javascript
selectOption(indices, params = {}) {
    const option = this.sources[indices[0]].options[indices[1]];
    this.inEdition = false;
    if (option.unselectable) {
        this.inputRef.el.value = "";
        this.close();
        return;
    }

    if (this.props.resetOnSelect) {
        this.inputRef.el.value = "";
    }

    this.forceValFromProp = true;
    this.props.onSelect(option, {
        ...params,
        input: this.inputRef.el,
    });
    this.close();
}
```

**选择功能**:
- **选项验证**: 检查选项是否可选择
- **重置支持**: 支持选择后重置输入框
- **回调触发**: 触发选择回调并传递相关参数
- **状态更新**: 更新组件状态和关闭下拉框

## 使用场景

### 1. 基础自动完成

```javascript
// 简单的自动完成组件
<AutoComplete
    placeholder="搜索用户..."
    sources={[{
        options: async (query) => {
            const response = await rpc('/web/dataset/search_read', {
                model: 'res.users',
                domain: [['name', 'ilike', query]],
                fields: ['name', 'email']
            });
            return response.records.map(user => ({
                label: user.name,
                value: user.id,
                email: user.email
            }));
        }
    }]}
    onSelect={(option) => {
        console.log('Selected user:', option);
    }}
/>
```

### 2. 多数据源自动完成

```javascript
// 多数据源自动完成
<AutoComplete
    placeholder="搜索..."
    sources={[
        {
            placeholder: "用户",
            options: async (query) => await searchUsers(query)
        },
        {
            placeholder: "产品",
            options: async (query) => await searchProducts(query)
        },
        {
            placeholder: "订单",
            options: async (query) => await searchOrders(query)
        }
    ]}
    onSelect={(option) => {
        this.handleSelection(option);
    }}
/>
```

### 3. 自定义模板

```javascript
// 使用自定义模板
<AutoComplete
    sources={[{
        optionTemplate: "custom.AutoCompleteOption",
        options: async (query) => {
            return await searchWithDetails(query);
        }
    }]}
    onSelect={(option) => {
        this.selectItem(option);
    }}
/>
```

## 增强示例

```javascript
// 增强的自动完成组件
const EnhancedAutoComplete = {
    createAdvancedAutoComplete: () => {
        class AdvancedAutoComplete extends AutoComplete {
            setup() {
                super.setup();
                
                // 增强功能
                this.enhancedState = useState({
                    searchHistory: [],
                    favorites: [],
                    recentSelections: [],
                    analytics: {
                        searchCount: 0,
                        selectionCount: 0,
                        averageSearchTime: 0
                    }
                });

                // 配置选项
                this.config = {
                    enableHistory: true,
                    enableFavorites: true,
                    enableAnalytics: true,
                    maxHistorySize: 10,
                    maxRecentSize: 5,
                    enableCaching: true,
                    cacheTimeout: 300000, // 5分钟
                    enableHighlight: true,
                    enableGrouping: true
                };

                // 缓存系统
                this.cache = new Map();
                
                // 搜索开始时间
                this.searchStartTime = null;
            }

            // 增强的数据源加载
            async loadSources(useInput) {
                const query = useInput ? this.inputRef.el.value.trim() : "";
                
                // 记录搜索开始时间
                if (this.config.enableAnalytics && query) {
                    this.searchStartTime = Date.now();
                    this.enhancedState.analytics.searchCount++;
                }

                // 检查缓存
                if (this.config.enableCaching && query) {
                    const cached = this.getCachedResults(query);
                    if (cached) {
                        this.sources = cached;
                        this.navigate(0);
                        return;
                    }
                }

                // 调用父类方法
                await super.loadSources(useInput);

                // 缓存结果
                if (this.config.enableCaching && query) {
                    this.cacheResults(query, this.sources);
                }

                // 记录搜索时间
                if (this.config.enableAnalytics && this.searchStartTime) {
                    const searchTime = Date.now() - this.searchStartTime;
                    this.updateSearchTime(searchTime);
                }

                // 添加历史记录和收藏夹
                if (this.config.enableHistory || this.config.enableFavorites) {
                    this.addSpecialSources(query);
                }
            }

            // 增强的选项选择
            selectOption(indices, params = {}) {
                const option = this.sources[indices[0]].options[indices[1]];
                
                // 记录选择
                if (this.config.enableAnalytics) {
                    this.enhancedState.analytics.selectionCount++;
                }

                // 添加到最近选择
                this.addToRecentSelections(option);

                // 添加到搜索历史
                if (this.config.enableHistory) {
                    this.addToSearchHistory(this.inputRef.el.value.trim());
                }

                // 调用父类方法
                super.selectOption(indices, params);
            }

            // 缓存管理
            getCachedResults(query) {
                const cacheKey = this.generateCacheKey(query);
                const cached = this.cache.get(cacheKey);
                
                if (cached && Date.now() - cached.timestamp < this.config.cacheTimeout) {
                    return cached.results;
                }
                
                return null;
            }

            cacheResults(query, results) {
                const cacheKey = this.generateCacheKey(query);
                this.cache.set(cacheKey, {
                    results: JSON.parse(JSON.stringify(results)),
                    timestamp: Date.now()
                });

                // 限制缓存大小
                if (this.cache.size > 100) {
                    const firstKey = this.cache.keys().next().value;
                    this.cache.delete(firstKey);
                }
            }

            generateCacheKey(query) {
                return `${query.toLowerCase()}_${JSON.stringify(this.props.sources.map(s => s.placeholder))}`;
            }

            // 添加特殊数据源
            addSpecialSources(query) {
                // 添加搜索历史
                if (this.config.enableHistory && this.enhancedState.searchHistory.length > 0) {
                    const historySource = {
                        id: 'history',
                        placeholder: '搜索历史',
                        options: this.enhancedState.searchHistory
                            .filter(item => item.toLowerCase().includes(query.toLowerCase()))
                            .map(item => ({
                                id: `history_${item}`,
                                label: item,
                                value: item,
                                type: 'history'
                            })),
                        isLoading: false
                    };
                    this.sources.unshift(historySource);
                }

                // 添加收藏夹
                if (this.config.enableFavorites && this.enhancedState.favorites.length > 0) {
                    const favoritesSource = {
                        id: 'favorites',
                        placeholder: '收藏夹',
                        options: this.enhancedState.favorites
                            .filter(item => item.label.toLowerCase().includes(query.toLowerCase()))
                            .map(item => ({
                                ...item,
                                type: 'favorite'
                            })),
                        isLoading: false
                    };
                    this.sources.unshift(favoritesSource);
                }
            }

            // 添加到搜索历史
            addToSearchHistory(query) {
                if (!query || this.enhancedState.searchHistory.includes(query)) {
                    return;
                }

                this.enhancedState.searchHistory.unshift(query);
                
                if (this.enhancedState.searchHistory.length > this.config.maxHistorySize) {
                    this.enhancedState.searchHistory.pop();
                }
            }

            // 添加到最近选择
            addToRecentSelections(option) {
                const existing = this.enhancedState.recentSelections.findIndex(
                    item => item.value === option.value
                );

                if (existing > -1) {
                    this.enhancedState.recentSelections.splice(existing, 1);
                }

                this.enhancedState.recentSelections.unshift(option);

                if (this.enhancedState.recentSelections.length > this.config.maxRecentSize) {
                    this.enhancedState.recentSelections.pop();
                }
            }

            // 更新搜索时间
            updateSearchTime(searchTime) {
                const analytics = this.enhancedState.analytics;
                const totalTime = analytics.averageSearchTime * (analytics.searchCount - 1) + searchTime;
                analytics.averageSearchTime = totalTime / analytics.searchCount;
            }

            // 添加到收藏夹
            addToFavorites(option) {
                if (!this.enhancedState.favorites.find(item => item.value === option.value)) {
                    this.enhancedState.favorites.push({
                        ...option,
                        addedAt: Date.now()
                    });
                }
            }

            // 从收藏夹移除
            removeFromFavorites(option) {
                const index = this.enhancedState.favorites.findIndex(
                    item => item.value === option.value
                );
                if (index > -1) {
                    this.enhancedState.favorites.splice(index, 1);
                }
            }

            // 清理缓存
            clearCache() {
                this.cache.clear();
            }

            // 清理历史
            clearHistory() {
                this.enhancedState.searchHistory = [];
            }

            // 获取统计信息
            getAnalytics() {
                return {
                    ...this.enhancedState.analytics,
                    cacheSize: this.cache.size,
                    historySize: this.enhancedState.searchHistory.length,
                    favoritesSize: this.enhancedState.favorites.length,
                    recentSelectionsSize: this.enhancedState.recentSelections.length
                };
            }

            // 导出数据
            exportData() {
                return {
                    searchHistory: this.enhancedState.searchHistory,
                    favorites: this.enhancedState.favorites,
                    recentSelections: this.enhancedState.recentSelections,
                    analytics: this.enhancedState.analytics
                };
            }

            // 导入数据
            importData(data) {
                if (data.searchHistory) {
                    this.enhancedState.searchHistory = data.searchHistory;
                }
                if (data.favorites) {
                    this.enhancedState.favorites = data.favorites;
                }
                if (data.recentSelections) {
                    this.enhancedState.recentSelections = data.recentSelections;
                }
                if (data.analytics) {
                    this.enhancedState.analytics = { ...this.enhancedState.analytics, ...data.analytics };
                }
            }
        }

        return AdvancedAutoComplete;
    }
};

// 使用示例
const AdvancedAutoComplete = EnhancedAutoComplete.createAdvancedAutoComplete();

<AdvancedAutoComplete
    placeholder="智能搜索..."
    sources={[
        {
            placeholder: "全局搜索",
            options: async (query) => await globalSearch(query)
        }
    ]}
    onSelect={(option) => {
        console.log('Selected:', option);
        // 可以添加到收藏夹
        if (option.type !== 'history' && option.type !== 'favorite') {
            this.autoComplete.addToFavorites(option);
        }
    }}
    ref={(ref) => { this.autoComplete = ref; }}
/>
```

## 技术特点

### 1. 异步支持
- 支持异步数据源
- Promise-based加载
- 加载状态管理

### 2. 性能优化
- 防抖输入处理
- 结果缓存机制
- 高效的DOM更新

### 3. 用户体验
- 键盘导航支持
- 鼠标交互
- 外部点击关闭

### 4. 扩展性
- 多数据源支持
- 自定义模板
- 灵活的配置选项

## 设计模式

### 1. 组合模式 (Composite Pattern)
- 多数据源的统一管理
- 选项的层级结构

### 2. 观察者模式 (Observer Pattern)
- 事件回调机制
- 状态变化通知

### 3. 策略模式 (Strategy Pattern)
- 不同的数据加载策略
- 可配置的行为模式

## 注意事项

1. **性能优化**: 合理使用防抖和缓存
2. **内存管理**: 及时清理事件监听器
3. **用户体验**: 提供清晰的加载状态
4. **可访问性**: 支持键盘导航和屏幕阅读器

## 扩展建议

1. **智能排序**: 基于使用频率的智能排序
2. **模糊搜索**: 支持模糊匹配算法
3. **分组显示**: 支持选项的分组显示
4. **无限滚动**: 支持大量数据的无限滚动
5. **多选支持**: 支持多选模式

该自动完成组件为Odoo Web应用提供了强大的智能输入功能，通过异步加载、键盘导航和灵活配置满足了各种复杂的用户交互需求。

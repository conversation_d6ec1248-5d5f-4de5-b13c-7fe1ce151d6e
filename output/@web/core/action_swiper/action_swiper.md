# Action Swiper - 动作滑动组件

## 概述

`action_swiper.js` 是 Odoo Web 核心模块的动作滑动组件，专门用于在触摸设备上实现滑动手势触发动作的功能。该组件支持左右滑动手势，可以配置不同的动作、图标和背景色，为移动端用户提供了直观的交互体验，广泛应用于列表项操作、卡片操作等场景。

## 文件信息
- **路径**: `/web/static/src/core/action_swiper/action_swiper.js`
- **行数**: 231
- **模块**: `@web/core/action_swiper/action_swiper`

## 依赖关系

```javascript
// 核心依赖
'@web/core/browser/browser'         // 浏览器服务
'@web/core/l10n/localization'       // 本地化服务
'@web/core/utils/numbers'           // 数字工具
'@odoo/owl'                         // OWL框架
'@web/core/utils/concurrency'       // 并发工具
```

## 核心功能

### 1. 组件定义

```javascript
const ActionSwiper = __exports.ActionSwiper = class ActionSwiper extends Component {
    static template = "web.ActionSwiper";
    static props = {
        onLeftSwipe: {
            type: Object,
            args: {
                action: Function,
                icon: String,
                bgColor: String,
            },
            optional: true,
        },
        onRightSwipe: {
            type: Object,
            args: {
                action: Function,
                icon: String,
                bgColor: String,
            },
            optional: true,
        },
        slots: Object,
        animationOnMove: { type: Boolean, optional: true },
        animationType: { type: String, optional: true },
        swipeDistanceRatio: { type: Number, optional: true },
        swipeInvalid: { type: Function, optional: true },
    };
```

**组件特性**:
- **滑动配置**: 支持左右滑动的独立配置
- **动作定义**: 每个滑动方向可配置动作函数、图标和背景色
- **动画控制**: 支持移动时动画和不同的动画类型
- **距离控制**: 可配置触发滑动的距离比例
- **条件验证**: 支持滑动有效性的条件检查

### 2. 默认属性

```javascript
static defaultProps = {
    onLeftSwipe: undefined,
    onRightSwipe: undefined,
    animationOnMove: true,
    animationType: "bounce",
    swipeDistanceRatio: 2,
};
```

**默认配置**:
- **滑动动作**: 默认不配置左右滑动动作
- **移动动画**: 默认启用移动时动画
- **动画类型**: 默认使用弹跳动画
- **距离比例**: 默认需要滑动宽度的一半才触发

### 3. 组件初始化

```javascript
setup() {
    this.actionTimeoutId = null;
    this.resetTimeoutId = null;
    this.defaultState = {
        containerStyle: "",
        isSwiping: false,
        width: undefined,
    };
    this.root = useRef("root");
    this.targetContainer = useRef("targetContainer");
    this.state = useState({ ...this.defaultState });
    this.scrollables = undefined;
    this.startX = undefined;
    this.swipedDistance = 0;
    this.isScrollValidated = false;
```

**初始化功能**:
- **定时器管理**: 管理动作和重置的定时器
- **状态管理**: 使用useState管理组件状态
- **引用管理**: 创建根元素和目标容器的引用
- **滑动状态**: 初始化滑动相关的状态变量
- **滚动处理**: 处理可滚动元素的冲突

### 4. 本地化属性

```javascript
get localizedProps() {
    return {
        onLeftSwipe:
            localization.direction === "rtl" ? this.props.onRightSwipe : this.props.onLeftSwipe,
        onRightSwipe:
            localization.direction === "rtl" ? this.props.onLeftSwipe : this.props.onRightSwipe,
    };
}
```

**本地化功能**:
- **RTL支持**: 支持从右到左的语言布局
- **方向映射**: 根据语言方向映射滑动动作
- **自动适配**: 自动适配不同的文本方向

### 5. 触摸事件处理

```javascript
_onTouchStartSwipe(ev) {
    this.scrollables = ev
        .composedPath()
        .filter(
            (e) =>
                e.nodeType === 1 &&
                this.targetContainer.el.contains(e) &&
                e.scrollWidth > e.getBoundingClientRect().width &&
                ["auto", "scroll"].includes(window.getComputedStyle(e)["overflow-x"])
        );
    if (!this.state.width) {
        this.state.width =
            this.targetContainer && this.targetContainer.el.getBoundingClientRect().width;
    }
    this.state.isSwiping = true;
    this.isScrollValidated = false;
    this.startX = ev.touches[0].clientX;
}
```

**触摸开始功能**:
- **滚动检测**: 检测触摸路径上的可滚动元素
- **宽度计算**: 计算目标容器的宽度
- **状态设置**: 设置滑动状态和起始位置
- **滚动验证**: 初始化滚动验证状态

### 6. 滑动移动处理

```javascript
_onTouchMoveSwipe(ev) {
    if (this.state.isSwiping) {
        if (this.props.swipeInvalid && this.props.swipeInvalid()) {
            this.state.isSwiping = false;
            return;
        }
        const { onLeftSwipe, onRightSwipe } = this.localizedProps;
        this.swipedDistance = clamp(
            ev.touches[0].clientX - this.startX,
            onLeftSwipe ? -this.state.width : 0,
            onRightSwipe ? this.state.width : 0
        );
        // Prevent the browser to navigate back/forward when using swipe
        // gestures while still allowing to scroll vertically.
        if (Math.abs(this.swipedDistance) > 40) {
            ev.preventDefault();
        }
        // If there are scrollable elements under touch pressure,
        // they must be at their limits to allow swiping.
        if (
            !this.isScrollValidated &&
            this.scrollables &&
            !isScrollSwipable(this.scrollables)[this.swipedDistance > 0 ? "left" : "right"]
        ) {
            return this._reset();
        }
        this.isScrollValidated = true;

        if (this.props.animationOnMove) {
            this.state.containerStyle = `transform: translateX(${this.swipedDistance}px)`;
        }
    }
}
```

**移动处理功能**:
- **有效性检查**: 检查滑动是否有效
- **距离计算**: 计算滑动距离并限制在有效范围内
- **浏览器行为**: 防止浏览器的前进后退手势
- **滚动冲突**: 处理与可滚动元素的冲突
- **动画更新**: 根据配置更新移动动画

### 7. 滑动结束处理

```javascript
_onTouchEndSwipe() {
    if (this.state.isSwiping) {
        this.state.isSwiping = false;
        if (
            this.localizedProps.onRightSwipe &&
            this.swipedDistance > this.state.width / this.props.swipeDistanceRatio
        ) {
            this.swipedDistance = this.state.width;
            this.handleSwipe(this.localizedProps.onRightSwipe.action);
        } else if (
            this.localizedProps.onLeftSwipe &&
            this.swipedDistance < -this.state.width / this.props.swipeDistanceRatio
        ) {
            this.swipedDistance = -this.state.width;
            this.handleSwipe(this.localizedProps.onLeftSwipe.action);
        } else {
            this.state.containerStyle = "";
        }
    }
}
```

**结束处理功能**:
- **状态重置**: 重置滑动状态
- **距离判断**: 判断滑动距离是否达到触发阈值
- **动作执行**: 执行对应方向的滑动动作
- **样式重置**: 重置容器样式

### 8. 动作处理

```javascript
handleSwipe(action) {
    if (this.props.animationType === "bounce") {
        this.state.containerStyle = `transform: translateX(${this.swipedDistance}px)`;
        this.actionTimeoutId = browser.setTimeout(async () => {
            await action(Promise.resolve());
            this._reset();
        }, 500);
    } else if (this.props.animationType === "forwards") {
        this.state.containerStyle = `transform: translateX(${this.swipedDistance}px)`;
        this.actionTimeoutId = browser.setTimeout(async () => {
            const prom = new Deferred();
            await action(prom);
            this.state.isSwiping = true;
            this.state.containerStyle = `transform: translateX(${-this.swipedDistance}px)`;
            this.resetTimeoutId = browser.setTimeout(() => {
                prom.resolve();
                this._reset();
            }, 100);
        }, 100);
    } else {
        return action(Promise.resolve());
    }
}
```

**动作处理功能**:
- **弹跳动画**: 执行弹跳动画效果
- **前进动画**: 执行前进动画效果
- **即时执行**: 不使用动画直接执行动作
- **异步处理**: 支持异步动作的执行

## 使用场景

### 1. 列表项操作

```javascript
// 在列表项中使用滑动操作
<ActionSwiper
    onLeftSwipe={{
        action: () => this.deleteItem(),
        icon: "fa-trash",
        bgColor: "#dc3545"
    }}
    onRightSwipe={{
        action: () => this.archiveItem(),
        icon: "fa-archive",
        bgColor: "#28a745"
    }}
    animationType="bounce"
    swipeDistanceRatio={3}
>
    <div class="list-item">
        <!-- 列表项内容 -->
    </div>
</ActionSwiper>
```

### 2. 卡片操作

```javascript
// 在卡片中使用滑动操作
<ActionSwiper
    onRightSwipe={{
        action: () => this.approveCard(),
        icon: "fa-check",
        bgColor: "#007bff"
    }}
    animationOnMove={true}
    swipeInvalid={() => this.isCardLocked()}
>
    <div class="card">
        <!-- 卡片内容 -->
    </div>
</ActionSwiper>
```

### 3. 邮件操作

```javascript
// 在邮件列表中使用滑动操作
<ActionSwiper
    onLeftSwipe={{
        action: () => this.markAsRead(),
        icon: "fa-envelope-open",
        bgColor: "#17a2b8"
    }}
    onRightSwipe={{
        action: () => this.deleteEmail(),
        icon: "fa-trash",
        bgColor: "#dc3545"
    }}
    animationType="forwards"
>
    <div class="email-item">
        <!-- 邮件项内容 -->
    </div>
</ActionSwiper>
```

## 技术特点

### 1. 触摸手势识别
- 精确的触摸事件处理
- 滑动距离和方向计算
- 多点触摸支持

### 2. 滚动冲突处理
- 智能检测可滚动元素
- 滚动边界验证
- 垂直滚动保护

### 3. 动画系统
- 多种动画类型支持
- 流畅的视觉反馈
- 可配置的动画参数

### 4. 本地化支持
- RTL语言支持
- 方向自动映射
- 国际化兼容

## 设计模式

### 1. 组件模式 (Component Pattern)
- 封装滑动逻辑
- 可重用的组件设计

### 2. 策略模式 (Strategy Pattern)
- 不同的动画策略
- 可配置的行为模式

### 3. 观察者模式 (Observer Pattern)
- 触摸事件监听
- 状态变化响应

## 注意事项

1. **性能优化**: 避免频繁的DOM操作和样式更新
2. **触摸冲突**: 正确处理与滚动的冲突
3. **浏览器兼容**: 确保在不同浏览器上的兼容性
4. **用户体验**: 提供清晰的视觉反馈

## 扩展建议

1. **手势增强**: 支持更多的手势类型
2. **动画扩展**: 添加更多的动画效果
3. **阈值配置**: 更灵活的触发阈值配置
4. **反馈增强**: 添加触觉反馈支持
5. **可访问性**: 改善可访问性支持

该动作滑动组件为Odoo Web应用提供了重要的移动端交互功能，通过直观的滑动手势让用户能够快速执行常用操作，显著提升了移动端的用户体验。

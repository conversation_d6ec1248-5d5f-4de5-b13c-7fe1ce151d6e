# CheckboxItem - 复选框菜单项组件

## 概述

`checkbox_item.js` 是 Odoo Web 核心模块的复选框菜单项组件，提供了带复选框功能的下拉菜单项实现。该组件继承自DropdownItem，添加了复选框状态管理，支持选中和未选中状态的切换，为下拉菜单系统提供了多选功能，适用于需要用户进行多项选择的场景，提供了直观的选择状态反馈。

## 文件信息
- **路径**: `/web/static/src/core/dropdown/checkbox_item.js`
- **行数**: 18
- **模块**: `@web/core/dropdown/checkbox_item`

## 依赖关系

```javascript
// 核心依赖
'@web/core/dropdown/dropdown_item'    // 下拉菜单项组件
```

## 核心功能

### 1. 组件继承

```javascript
class CheckboxItem extends DropdownItem {
    static template = "web.CheckboxItem";
}
```

**继承功能**:
- **基础功能**: 继承DropdownItem的所有基础功能
- **模板重写**: 使用专门的复选框模板
- **功能扩展**: 在基础功能上添加复选框特性
- **一致性**: 保持与其他菜单项的一致性

### 2. 属性扩展

```javascript
static props = {
    ...DropdownItem.props,
    checked: {
        type: Boolean,
        optional: false,
    },
};
```

**属性扩展功能**:
- **继承属性**: 继承所有DropdownItem的属性
- **复选状态**: 添加checked属性控制复选框状态
- **必需属性**: checked属性为必需，确保状态明确
- **类型安全**: 明确指定checked为布尔类型

## 使用场景

### 1. 基础复选框菜单项

```javascript
// 基础复选框菜单项
<CheckboxItem 
    checked={this.state.option1}
    onSelected={() => this.toggleOption('option1')}
>
    选项 1
</CheckboxItem>

<CheckboxItem 
    checked={this.state.option2}
    onSelected={() => this.toggleOption('option2')}
>
    选项 2
</CheckboxItem>
```

### 2. 多选菜单

```javascript
// 多选菜单组件
class MultiSelectDropdown extends Component {
    setup() {
        this.state = useState({
            selectedOptions: new Set(),
            options: [
                { id: 'email', label: '邮件通知' },
                { id: 'sms', label: '短信通知' },
                { id: 'push', label: '推送通知' },
                { id: 'desktop', label: '桌面通知' }
            ]
        });
    }

    toggleOption(optionId) {
        if (this.state.selectedOptions.has(optionId)) {
            this.state.selectedOptions.delete(optionId);
        } else {
            this.state.selectedOptions.add(optionId);
        }
        // 触发状态更新
        this.state.selectedOptions = new Set(this.state.selectedOptions);
    }

    selectAll() {
        this.state.selectedOptions = new Set(this.state.options.map(opt => opt.id));
    }

    clearAll() {
        this.state.selectedOptions.clear();
        this.state.selectedOptions = new Set();
    }

    render() {
        return xml`
            <Dropdown>
                <button class="btn btn-primary">
                    通知设置 (${this.state.selectedOptions.size})
                </button>
                <t t-set-slot="content">
                    <div class="px-3 py-2 border-bottom">
                        <button 
                            class="btn btn-sm btn-outline-primary me-2"
                            t-on-click="selectAll"
                        >
                            全选
                        </button>
                        <button 
                            class="btn btn-sm btn-outline-secondary"
                            t-on-click="clearAll"
                        >
                            清空
                        </button>
                    </div>
                    
                    <t t-foreach="state.options" t-as="option" t-key="option.id">
                        <CheckboxItem 
                            checked="state.selectedOptions.has(option.id)"
                            onSelected="() => this.toggleOption(option.id)"
                            closingMode="none"
                        >
                            <t t-esc="option.label"/>
                        </CheckboxItem>
                    </t>
                </t>
            </Dropdown>
        `;
    }
}
```

### 3. 分组复选框菜单

```javascript
// 分组复选框菜单
class GroupedCheckboxDropdown extends Component {
    setup() {
        this.state = useState({
            selectedItems: new Set(),
            groups: [
                {
                    id: 'display',
                    label: '显示选项',
                    items: [
                        { id: 'grid', label: '显示网格' },
                        { id: 'ruler', label: '显示标尺' },
                        { id: 'guides', label: '显示参考线' }
                    ]
                },
                {
                    id: 'behavior',
                    label: '行为选项',
                    items: [
                        { id: 'autosave', label: '自动保存' },
                        { id: 'autobackup', label: '自动备份' },
                        { id: 'smartselect', label: '智能选择' }
                    ]
                }
            ]
        });
    }

    toggleItem(itemId) {
        if (this.state.selectedItems.has(itemId)) {
            this.state.selectedItems.delete(itemId);
        } else {
            this.state.selectedItems.add(itemId);
        }
        this.state.selectedItems = new Set(this.state.selectedItems);
    }

    toggleGroup(groupId) {
        const group = this.state.groups.find(g => g.id === groupId);
        const groupItems = group.items.map(item => item.id);
        const allSelected = groupItems.every(id => this.state.selectedItems.has(id));
        
        if (allSelected) {
            // 取消选择整个分组
            groupItems.forEach(id => this.state.selectedItems.delete(id));
        } else {
            // 选择整个分组
            groupItems.forEach(id => this.state.selectedItems.add(id));
        }
        
        this.state.selectedItems = new Set(this.state.selectedItems);
    }

    isGroupSelected(groupId) {
        const group = this.state.groups.find(g => g.id === groupId);
        return group.items.every(item => this.state.selectedItems.has(item.id));
    }

    isGroupPartiallySelected(groupId) {
        const group = this.state.groups.find(g => g.id === groupId);
        const selectedCount = group.items.filter(item => 
            this.state.selectedItems.has(item.id)
        ).length;
        return selectedCount > 0 && selectedCount < group.items.length;
    }

    render() {
        return xml`
            <Dropdown>
                <button class="btn btn-primary">
                    设置选项
                </button>
                <t t-set-slot="content">
                    <t t-foreach="state.groups" t-as="group" t-key="group.id">
                        <h6 class="dropdown-header d-flex align-items-center">
                            <input 
                                type="checkbox" 
                                class="form-check-input me-2"
                                t-att-checked="isGroupSelected(group.id)"
                                t-att-indeterminate="isGroupPartiallySelected(group.id)"
                                t-on-change="() => this.toggleGroup(group.id)"
                            />
                            <t t-esc="group.label"/>
                        </h6>
                        
                        <t t-foreach="group.items" t-as="item" t-key="item.id">
                            <CheckboxItem 
                                checked="state.selectedItems.has(item.id)"
                                onSelected="() => this.toggleItem(item.id)"
                                closingMode="none"
                                class="ps-4"
                            >
                                <t t-esc="item.label"/>
                            </CheckboxItem>
                        </t>
                        
                        <div class="dropdown-divider" t-if="!group_last"/>
                    </t>
                </t>
            </Dropdown>
        `;
    }
}
```

### 4. 带搜索的复选框菜单

```javascript
// 带搜索功能的复选框菜单
class SearchableCheckboxDropdown extends Component {
    setup() {
        this.state = useState({
            searchTerm: '',
            selectedItems: new Set(),
            allItems: [
                { id: 'user1', label: '张三', email: '<EMAIL>' },
                { id: 'user2', label: '李四', email: '<EMAIL>' },
                { id: 'user3', label: '王五', email: '<EMAIL>' },
                { id: 'user4', label: '赵六', email: '<EMAIL>' }
            ]
        });
    }

    get filteredItems() {
        if (!this.state.searchTerm) {
            return this.state.allItems;
        }
        
        const searchTerm = this.state.searchTerm.toLowerCase();
        return this.state.allItems.filter(item => 
            item.label.toLowerCase().includes(searchTerm) ||
            item.email.toLowerCase().includes(searchTerm)
        );
    }

    toggleItem(itemId) {
        if (this.state.selectedItems.has(itemId)) {
            this.state.selectedItems.delete(itemId);
        } else {
            this.state.selectedItems.add(itemId);
        }
        this.state.selectedItems = new Set(this.state.selectedItems);
    }

    onSearchInput(event) {
        this.state.searchTerm = event.target.value;
    }

    render() {
        return xml`
            <Dropdown>
                <button class="btn btn-primary">
                    选择用户 (${this.state.selectedItems.size})
                </button>
                <t t-set-slot="content">
                    <div class="p-2 border-bottom">
                        <input 
                            type="text" 
                            class="form-control form-control-sm" 
                            placeholder="搜索用户..."
                            t-model="state.searchTerm"
                            t-on-input="onSearchInput"
                        />
                    </div>
                    
                    <div style="max-height: 300px; overflow-y: auto;">
                        <t t-if="filteredItems.length === 0">
                            <div class="p-3 text-center text-muted">
                                没有找到匹配的用户
                            </div>
                        </t>
                        
                        <t t-foreach="filteredItems" t-as="item" t-key="item.id">
                            <CheckboxItem 
                                checked="state.selectedItems.has(item.id)"
                                onSelected="() => this.toggleItem(item.id)"
                                closingMode="none"
                            >
                                <div class="d-flex flex-column">
                                    <span class="fw-bold">
                                        <t t-esc="item.label"/>
                                    </span>
                                    <small class="text-muted">
                                        <t t-esc="item.email"/>
                                    </small>
                                </div>
                            </CheckboxItem>
                        </t>
                    </div>
                </t>
            </Dropdown>
        `;
    }
}
```

## 增强示例

```javascript
// 增强的复选框菜单项组件
const EnhancedCheckboxItem = {
    createAdvancedCheckboxItem: () => {
        class AdvancedCheckboxItem extends CheckboxItem {
            static props = {
                ...CheckboxItem.props,
                
                // 增强属性
                indeterminate: { type: Boolean, optional: true },
                disabled: { type: Boolean, optional: true },
                icon: { type: String, optional: true },
                description: { type: String, optional: true },
                value: { type: [String, Number], optional: true },
                group: { type: String, optional: true },
                exclusive: { type: Boolean, optional: true },
                onChange: { type: Function, optional: true },
                checkboxClass: { type: String, optional: true },
                labelClass: { type: String, optional: true }
            };

            static defaultProps = {
                ...CheckboxItem.defaultProps,
                indeterminate: false,
                disabled: false,
                exclusive: false
            };

            setup() {
                super.setup();
                
                // 增强状态
                this.enhancedState = useState({
                    isHovered: false,
                    isFocused: false
                });

                // 复选框引用
                this.checkboxRef = useRef('checkbox');
            }

            // 增强的点击处理
            onClick(ev) {
                if (this.props.disabled) {
                    ev.preventDefault();
                    return;
                }

                // 处理互斥选择
                if (this.props.exclusive && this.props.group) {
                    this.handleExclusiveSelection();
                }

                // 调用原始点击处理
                super.onClick(ev);

                // 触发变化回调
                if (this.props.onChange) {
                    this.props.onChange(!this.props.checked, this.props.value);
                }
            }

            // 处理互斥选择
            handleExclusiveSelection() {
                // 在同一分组中，只能选择一个
                const groupItems = document.querySelectorAll(
                    `[data-checkbox-group="${this.props.group}"]`
                );
                
                groupItems.forEach(item => {
                    if (item !== this.checkboxRef.el) {
                        const component = item.__component__;
                        if (component && component.props.checked) {
                            component.props.onSelected?.();
                        }
                    }
                });
            }

            // 设置不确定状态
            setIndeterminate(indeterminate) {
                if (this.checkboxRef.el) {
                    this.checkboxRef.el.indeterminate = indeterminate;
                }
            }

            // 鼠标事件处理
            onMouseEnter() {
                this.enhancedState.isHovered = true;
            }

            onMouseLeave() {
                this.enhancedState.isHovered = false;
            }

            onFocus() {
                this.enhancedState.isFocused = true;
            }

            onBlur() {
                this.enhancedState.isFocused = false;
            }

            // 获取复选框类名
            getCheckboxClass() {
                const classes = ['form-check-input', this.props.checkboxClass || ''];
                
                if (this.props.disabled) {
                    classes.push('disabled');
                }
                
                return classes.filter(Boolean).join(' ');
            }

            // 获取标签类名
            getLabelClass() {
                const classes = ['form-check-label', this.props.labelClass || ''];
                
                if (this.props.disabled) {
                    classes.push('text-muted');
                }
                
                if (this.enhancedState.isHovered) {
                    classes.push('hovered');
                }
                
                return classes.filter(Boolean).join(' ');
            }

            // 获取项目类名
            getItemClass() {
                const classes = [this.props.class || ''];
                
                if (this.props.checked) {
                    classes.push('checked');
                }
                
                if (this.props.disabled) {
                    classes.push('disabled');
                }
                
                if (this.enhancedState.isFocused) {
                    classes.push('focused');
                }
                
                return classes.filter(Boolean).join(' ');
            }

            // 渲染图标
            renderIcon() {
                if (!this.props.icon) {
                    return '';
                }
                
                return xml`<i class="${this.props.icon} me-2"/>`;
            }

            // 渲染描述
            renderDescription() {
                if (!this.props.description) {
                    return '';
                }
                
                return xml`
                    <small class="text-muted d-block">
                        <t t-esc="props.description"/>
                    </small>
                `;
            }

            // 增强的模板
            static template = xml`
                <div 
                    class="advanced-checkbox-item dropdown-item ${this.getItemClass()}"
                    t-on-click="onClick"
                    t-on-mouseenter="onMouseEnter"
                    t-on-mouseleave="onMouseLeave"
                    t-att-data-checkbox-group="props.group"
                >
                    <div class="form-check d-flex align-items-start">
                        <input 
                            type="checkbox" 
                            class="${this.getCheckboxClass()}"
                            t-ref="checkbox"
                            t-att-checked="props.checked"
                            t-att-disabled="props.disabled"
                            t-att-indeterminate="props.indeterminate"
                            t-on-focus="onFocus"
                            t-on-blur="onBlur"
                        />
                        <label class="${this.getLabelClass()} flex-grow-1">
                            <div class="d-flex align-items-center">
                                <t t-call="renderIcon"/>
                                <div class="flex-grow-1">
                                    <t t-slot="default"/>
                                    <t t-call="renderDescription"/>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>
            `;
        }

        return AdvancedCheckboxItem;
    }
};

// 使用示例
const AdvancedCheckboxItem = EnhancedCheckboxItem.createAdvancedCheckboxItem();

// 带图标和描述的复选框项
<AdvancedCheckboxItem
    checked={this.state.emailNotifications}
    icon="fa fa-envelope"
    description="接收重要更新的邮件通知"
    onChange={(checked, value) => this.handleNotificationChange('email', checked)}
    onSelected={() => this.toggleEmailNotifications()}
>
    邮件通知
</AdvancedCheckboxItem>

// 互斥选择的复选框组
<AdvancedCheckboxItem
    checked={this.state.theme === 'light'}
    group="theme"
    exclusive={true}
    value="light"
    onChange={(checked, value) => this.setTheme(value)}
    onSelected={() => this.selectTheme('light')}
>
    浅色主题
</AdvancedCheckboxItem>

<AdvancedCheckboxItem
    checked={this.state.theme === 'dark'}
    group="theme"
    exclusive={true}
    value="dark"
    onChange={(checked, value) => this.setTheme(value)}
    onSelected={() => this.selectTheme('dark')}
>
    深色主题
</AdvancedCheckboxItem>

// 不确定状态的复选框项
<AdvancedCheckboxItem
    checked={this.isAllSelected()}
    indeterminate={this.isPartiallySelected()}
    onChange={(checked) => this.toggleSelectAll(checked)}
    onSelected={() => this.handleSelectAll()}
>
    全选
</AdvancedCheckboxItem>
```

## 技术特点

### 1. 继承设计
- 继承DropdownItem的所有功能
- 扩展复选框特性
- 保持接口一致性

### 2. 状态管理
- 明确的复选状态
- 必需的checked属性
- 类型安全的属性定义

### 3. 简洁实现
- 最小化的代码实现
- 专注于复选框功能
- 高效的组件设计

### 4. 模板重用
- 专门的复选框模板
- 保持视觉一致性
- 支持自定义样式

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- 继承基础组件功能
- 扩展特定功能

### 2. 组件模式 (Component Pattern)
- 可重用的复选框组件
- 标准化的接口

### 3. 状态模式 (State Pattern)
- 选中和未选中状态管理
- 状态转换控制

## 注意事项

1. **状态管理**: 确保复选框状态的正确管理
2. **事件处理**: 正确处理点击事件和状态变化
3. **无障碍性**: 确保复选框的无障碍访问
4. **性能优化**: 避免不必要的重渲染

## 扩展建议

1. **三态支持**: 支持不确定状态的复选框
2. **分组功能**: 支持复选框的分组管理
3. **批量操作**: 支持批量选择和取消选择
4. **动画效果**: 添加状态切换的动画效果
5. **键盘支持**: 增强键盘操作支持

该复选框菜单项组件为Odoo Web应用提供了简洁高效的多选功能，通过继承设计确保了与其他菜单项的一致性和兼容性。

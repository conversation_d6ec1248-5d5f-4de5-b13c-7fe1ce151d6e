# DropdownHooks - 下拉菜单钩子

## 概述

`dropdown_hooks.js` 是 Odoo Web 核心模块的下拉菜单钩子库，提供了与下拉菜单状态交互的React-style钩子函数。该模块包含了下拉状态管理钩子和下拉关闭控制钩子，为组件提供了便捷的下拉菜单状态管理和控制能力，简化了下拉菜单的集成和使用，确保了状态的一致性和操作的可靠性。

## 文件信息
- **路径**: `/web/static/src/core/dropdown/dropdown_hooks.js`
- **行数**: 53
- **模块**: `@web/core/dropdown/dropdown_hooks`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                              // OWL框架
'@web/core/dropdown/_behaviours/dropdown_nesting'       // 下拉嵌套行为
```

## 核心功能

### 1. 类型定义

```javascript
/**
 * @typedef {Object} DropdownState
 * @property {() => void} open
 * @property {() => void} close
 * @property {boolean} isOpen
 */
```

**类型定义功能**:
- **状态接口**: 定义下拉状态的标准接口
- **方法签名**: 明确open和close方法的签名
- **状态属性**: 定义isOpen布尔状态属性
- **文档支持**: 为TypeScript和IDE提供类型信息

### 2. useDropdownState钩子

```javascript
function useDropdownState({ onOpen, onClose } = {}) {
    const state = useState({
        isOpen: false,
        open: () => {
            state.isOpen = true;
            onOpen?.();
        },
        close: () => {
            state.isOpen = false;
            onClose?.();
        },
    });
    return state;
}
```

**状态钩子功能**:
- **状态管理**: 创建响应式的下拉状态对象
- **开启方法**: 提供打开下拉菜单的方法
- **关闭方法**: 提供关闭下拉菜单的方法
- **回调支持**: 支持开启和关闭时的回调函数
- **响应式**: 基于OWL的useState实现响应式状态

### 3. useDropdownCloser钩子

```javascript
function useDropdownCloser() {
    const env = useEnv();
    const dropdown = env[DROPDOWN_NESTING];
    return {
        close: () => dropdown?.close(),
        closeChildren: () => dropdown?.closeChildren(),
        closeAll: () => dropdown?.closeAllParents(),
    };
}
```

**关闭控制钩子功能**:
- **环境获取**: 从环境中获取下拉嵌套信息
- **关闭当前**: 关闭当前下拉菜单
- **关闭子级**: 关闭所有子级下拉菜单
- **关闭所有**: 关闭所有父级下拉菜单
- **安全调用**: 使用可选链操作符确保安全调用

## 使用场景

### 1. 基础下拉状态管理

```javascript
// 在组件中使用下拉状态钩子
class MyComponent extends Component {
    setup() {
        this.dropdownState = useDropdownState({
            onOpen: () => {
                console.log('下拉菜单已打开');
                this.loadMenuItems();
            },
            onClose: () => {
                console.log('下拉菜单已关闭');
                this.clearSelection();
            }
        });
    }

    render() {
        return xml`
            <Dropdown state="dropdownState">
                <button class="btn btn-primary">
                    菜单 ${this.dropdownState.isOpen ? '▲' : '▼'}
                </button>
                <t t-set-slot="content">
                    <DropdownItem onSelected={() => this.action1()}>
                        操作 1
                    </DropdownItem>
                </t>
            </Dropdown>
        `;
    }
}
```

### 2. 手动控制下拉状态

```javascript
// 手动控制下拉菜单的开启和关闭
class ControlledDropdown extends Component {
    setup() {
        this.dropdownState = useDropdownState();
    }

    openMenu() {
        this.dropdownState.open();
    }

    closeMenu() {
        this.dropdownState.close();
    }

    toggleMenu() {
        if (this.dropdownState.isOpen) {
            this.closeMenu();
        } else {
            this.openMenu();
        }
    }

    render() {
        return xml`
            <div>
                <button t-on-click="toggleMenu" class="btn btn-secondary">
                    切换菜单
                </button>
                <Dropdown state="dropdownState" manual={true}>
                    <div><!-- 触发器内容 --></div>
                    <t t-set-slot="content">
                        <!-- 菜单内容 -->
                    </t>
                </Dropdown>
            </div>
        `;
    }
}
```

### 3. 使用下拉关闭控制

```javascript
// 在菜单项中使用关闭控制钩子
class CustomDropdownItem extends Component {
    setup() {
        this.dropdownCloser = useDropdownCloser();
    }

    handleAction() {
        // 执行操作
        this.performAction();
        
        // 关闭当前下拉菜单
        this.dropdownCloser.close();
    }

    handleSubMenuAction() {
        // 执行子菜单操作
        this.performSubAction();
        
        // 关闭所有父级下拉菜单
        this.dropdownCloser.closeAll();
    }

    render() {
        return xml`
            <div class="dropdown-item" t-on-click="handleAction">
                自定义操作
            </div>
        `;
    }
}
```

### 4. 复杂的下拉状态管理

```javascript
// 复杂的下拉状态管理示例
class AdvancedDropdownManager extends Component {
    setup() {
        // 主下拉菜单状态
        this.mainDropdownState = useDropdownState({
            onOpen: () => {
                this.loadMainMenuItems();
                this.trackMenuOpen('main');
            },
            onClose: () => {
                this.clearMainMenuState();
                this.trackMenuClose('main');
            }
        });

        // 子下拉菜单状态
        this.subDropdownState = useDropdownState({
            onOpen: () => {
                this.loadSubMenuItems();
                this.trackMenuOpen('sub');
            },
            onClose: () => {
                this.clearSubMenuState();
                this.trackMenuClose('sub');
            }
        });

        // 下拉关闭控制
        this.dropdownCloser = useDropdownCloser();

        // 组件状态
        this.state = useState({
            mainMenuItems: [],
            subMenuItems: [],
            selectedItem: null
        });
    }

    async loadMainMenuItems() {
        this.state.mainMenuItems = await this.fetchMainMenuItems();
    }

    async loadSubMenuItems() {
        this.state.subMenuItems = await this.fetchSubMenuItems();
    }

    selectMainItem(item) {
        this.state.selectedItem = item;
        
        // 如果有子菜单，打开子菜单
        if (item.hasSubMenu) {
            this.subDropdownState.open();
        } else {
            // 否则关闭所有菜单
            this.dropdownCloser.closeAll();
        }
    }

    selectSubItem(item) {
        this.handleSubItemSelection(item);
        this.dropdownCloser.closeAll();
    }

    trackMenuOpen(menuType) {
        console.log(`${menuType} menu opened`);
        // 发送分析事件
    }

    trackMenuClose(menuType) {
        console.log(`${menuType} menu closed`);
        // 发送分析事件
    }

    render() {
        return xml`
            <div class="dropdown-manager">
                <Dropdown state="mainDropdownState">
                    <button class="btn btn-primary">
                        主菜单
                    </button>
                    <t t-set-slot="content">
                        <t t-foreach="state.mainMenuItems" t-as="item" t-key="item.id">
                            <DropdownItem onSelected="() => this.selectMainItem(item)">
                                <t t-esc="item.label"/>
                                <t t-if="item.hasSubMenu">
                                    <i class="fa fa-caret-right ms-auto"/>
                                </t>
                            </DropdownItem>
                        </t>
                    </t>
                </Dropdown>

                <Dropdown state="subDropdownState" t-if="state.selectedItem?.hasSubMenu">
                    <div><!-- 子菜单触发器 --></div>
                    <t t-set-slot="content">
                        <t t-foreach="state.subMenuItems" t-as="subItem" t-key="subItem.id">
                            <DropdownItem onSelected="() => this.selectSubItem(subItem)">
                                <t t-esc="subItem.label"/>
                            </DropdownItem>
                        </t>
                    </t>
                </Dropdown>
            </div>
        `;
    }
}
```

## 增强示例

```javascript
// 增强的下拉钩子
const EnhancedDropdownHooks = {
    // 增强的下拉状态钩子
    useAdvancedDropdownState: (options = {}) => {
        const {
            onOpen,
            onClose,
            autoClose = false,
            autoCloseDelay = 3000,
            persistent = false,
            onStateChange
        } = options;

        const state = useState({
            isOpen: false,
            isLoading: false,
            hasError: false,
            errorMessage: '',
            openCount: 0,
            lastOpenTime: null,
            lastCloseTime: null
        });

        let autoCloseTimer = null;

        const enhancedState = {
            ...state,
            open: async () => {
                if (state.isLoading) return;

                state.isLoading = true;
                state.hasError = false;

                try {
                    await onOpen?.();
                    state.isOpen = true;
                    state.openCount++;
                    state.lastOpenTime = Date.now();

                    // 自动关闭定时器
                    if (autoClose && !persistent) {
                        autoCloseTimer = setTimeout(() => {
                            enhancedState.close();
                        }, autoCloseDelay);
                    }

                    onStateChange?.({ isOpen: true, action: 'open' });
                } catch (error) {
                    state.hasError = true;
                    state.errorMessage = error.message;
                    console.error('Failed to open dropdown:', error);
                } finally {
                    state.isLoading = false;
                }
            },

            close: async () => {
                if (state.isLoading) return;

                // 清除自动关闭定时器
                if (autoCloseTimer) {
                    clearTimeout(autoCloseTimer);
                    autoCloseTimer = null;
                }

                state.isLoading = true;

                try {
                    await onClose?.();
                    state.isOpen = false;
                    state.lastCloseTime = Date.now();
                    onStateChange?.({ isOpen: false, action: 'close' });
                } catch (error) {
                    console.error('Failed to close dropdown:', error);
                } finally {
                    state.isLoading = false;
                }
            },

            toggle: () => {
                if (state.isOpen) {
                    enhancedState.close();
                } else {
                    enhancedState.open();
                }
            },

            clearError: () => {
                state.hasError = false;
                state.errorMessage = '';
            },

            getStats: () => ({
                openCount: state.openCount,
                lastOpenTime: state.lastOpenTime,
                lastCloseTime: state.lastCloseTime,
                isCurrentlyOpen: state.isOpen
            })
        };

        return enhancedState;
    },

    // 增强的下拉关闭控制钩子
    useAdvancedDropdownCloser: () => {
        const env = useEnv();
        const dropdown = env[DROPDOWN_NESTING];

        return {
            close: (options = {}) => {
                const { animated = true, delay = 0 } = options;
                
                if (delay > 0) {
                    setTimeout(() => dropdown?.close(), delay);
                } else {
                    dropdown?.close();
                }
            },

            closeChildren: (options = {}) => {
                const { recursive = true } = options;
                dropdown?.closeChildren();
            },

            closeAll: (options = {}) => {
                const { immediate = false } = options;
                
                if (immediate) {
                    dropdown?.closeAllParents();
                } else {
                    // 延迟关闭以允许动画
                    setTimeout(() => dropdown?.closeAllParents(), 100);
                }
            },

            closeConditional: (condition) => {
                if (typeof condition === 'function') {
                    if (condition()) {
                        dropdown?.close();
                    }
                } else if (condition) {
                    dropdown?.close();
                }
            },

            isNested: () => {
                return !!dropdown;
            },

            getDepth: () => {
                let depth = 0;
                let current = dropdown;
                while (current) {
                    depth++;
                    current = current.parent;
                }
                return depth;
            }
        };
    },

    // 下拉菜单组合钩子
    useDropdownCombo: (options = {}) => {
        const state = EnhancedDropdownHooks.useAdvancedDropdownState(options);
        const closer = EnhancedDropdownHooks.useAdvancedDropdownCloser();

        return {
            state,
            closer,
            
            // 便捷方法
            openWithDelay: (delay = 0) => {
                setTimeout(() => state.open(), delay);
            },

            closeWithDelay: (delay = 0) => {
                setTimeout(() => state.close(), delay);
            },

            toggleWithCondition: (condition) => {
                if (condition) {
                    state.toggle();
                }
            },

            // 批量操作
            batchOperation: async (operations) => {
                for (const operation of operations) {
                    await operation();
                }
            }
        };
    }
};

// 使用示例
class EnhancedDropdownComponent extends Component {
    setup() {
        // 使用增强的下拉状态
        this.dropdown = EnhancedDropdownHooks.useAdvancedDropdownState({
            onOpen: async () => {
                await this.loadData();
            },
            onClose: async () => {
                await this.saveState();
            },
            autoClose: true,
            autoCloseDelay: 5000,
            onStateChange: (change) => {
                console.log('Dropdown state changed:', change);
            }
        });

        // 使用增强的关闭控制
        this.closer = EnhancedDropdownHooks.useAdvancedDropdownCloser();
    }

    async loadData() {
        // 模拟数据加载
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    handleSpecialAction() {
        // 条件性关闭
        this.closer.closeConditional(() => {
            return this.shouldCloseAfterAction();
        });
    }

    render() {
        return xml`
            <Dropdown state="dropdown">
                <button class="btn btn-primary">
                    Enhanced Dropdown
                    <t t-if="dropdown.isLoading">
                        <i class="fa fa-spinner fa-spin ms-2"/>
                    </t>
                </button>
                <t t-set-slot="content">
                    <t t-if="dropdown.hasError">
                        <div class="alert alert-danger">
                            <t t-esc="dropdown.errorMessage"/>
                            <button t-on-click="dropdown.clearError" class="btn btn-sm btn-outline-danger">
                                Clear Error
                            </button>
                        </div>
                    </t>
                    <DropdownItem onSelected="handleSpecialAction">
                        Special Action
                    </DropdownItem>
                </t>
            </Dropdown>
        `;
    }
}
```

## 技术特点

### 1. 钩子模式
- React-style钩子设计
- 状态封装和复用
- 组件逻辑分离

### 2. 响应式状态
- 基于OWL的useState
- 自动UI更新
- 状态同步机制

### 3. 回调支持
- 开启和关闭回调
- 异步操作支持
- 错误处理机制

### 4. 嵌套支持
- 多层下拉菜单支持
- 父子关系管理
- 级联关闭控制

## 设计模式

### 1. 钩子模式 (Hook Pattern)
- 状态逻辑的封装和复用
- 组件间的状态共享

### 2. 观察者模式 (Observer Pattern)
- 状态变化的响应式更新
- 回调函数机制

### 3. 命令模式 (Command Pattern)
- 封装的操作命令
- 统一的控制接口

## 注意事项

1. **状态同步**: 确保状态与UI的同步更新
2. **内存管理**: 及时清理定时器和监听器
3. **错误处理**: 妥善处理异步操作的错误
4. **性能优化**: 避免不必要的状态更新

## 扩展建议

1. **动画支持**: 集成动画和过渡效果
2. **持久化**: 支持状态的持久化存储
3. **分析集成**: 集成用户行为分析
4. **性能监控**: 监控下拉菜单的性能指标
5. **测试工具**: 提供测试辅助工具

该下拉菜单钩子库为Odoo Web应用提供了便捷的下拉菜单状态管理和控制能力，通过钩子模式简化了下拉菜单的集成和使用。

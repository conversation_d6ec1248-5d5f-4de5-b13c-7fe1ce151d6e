# DropdownGroupHook - 下拉分组钩子

## 概述

`dropdown_group_hook.js` 是 Odoo Web 核心模块的下拉分组钩子，提供了下拉菜单分组管理的钩子函数。该模块允许下拉菜单组件加入到父级下拉分组中，并能够检测自身是否在分组内以及分组的开启状态，为实现下拉菜单的协调行为和状态同步提供了基础支持，确保了同一分组内下拉菜单的一致性管理。

## 文件信息
- **路径**: `/web/static/src/core/dropdown/_behaviours/dropdown_group_hook.js`
- **行数**: 42
- **模块**: `@web/core/dropdown/_behaviours/dropdown_group_hook`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                              // OWL框架
'@web/core/dropdown/dropdown_group'      // 下拉分组常量
```

## 核心功能

### 1. 类型定义

```javascript
/**
 * @typedef DropdownGroupState
 * @property {boolean} isInGroup
 * @property {boolean} isOpen
 */
```

**类型定义功能**:
- **分组状态**: 定义下拉分组状态的接口
- **分组检测**: isInGroup表示是否在分组中
- **开启状态**: isOpen表示分组是否有打开的下拉菜单
- **文档支持**: 为TypeScript和IDE提供类型信息

### 2. useDropdownGroup钩子

```javascript
function useDropdownGroup() {
    const env = useEnv();

    const group = {
        isInGroup: DROPDOWN_GROUP in env,
        get isOpen() {
            return this.isInGroup && [...env[DROPDOWN_GROUP]].some((dropdown) => dropdown.isOpen);
        },
    };

    if (group.isInGroup) {
        const dropdown = useComponent();
        useEffect(() => {
            env[DROPDOWN_GROUP].add(dropdown.state);
            return () => env[DROPDOWN_GROUP].delete(dropdown.state);
        });
    }

    return group;
}
```

**钩子功能**:
- **环境检测**: 检查环境中是否存在下拉分组
- **分组状态**: 创建分组状态对象
- **开启检测**: 通过getter动态检测分组中是否有打开的下拉菜单
- **自动注册**: 如果在分组中，自动注册当前组件到分组
- **自动清理**: 组件销毁时自动从分组中移除

### 3. 分组检测逻辑

```javascript
isInGroup: DROPDOWN_GROUP in env
```

**检测功能**:
- **环境检查**: 检查环境对象中是否存在DROPDOWN_GROUP键
- **简单判断**: 使用in操作符进行快速检测
- **布尔结果**: 返回明确的布尔值
- **性能优化**: 避免复杂的检测逻辑

### 4. 开启状态检测

```javascript
get isOpen() {
    return this.isInGroup && [...env[DROPDOWN_GROUP]].some((dropdown) => dropdown.isOpen);
}
```

**开启检测功能**:
- **条件检查**: 首先检查是否在分组中
- **集合遍历**: 遍历分组中的所有下拉菜单
- **状态检测**: 检查是否有任何下拉菜单处于打开状态
- **动态计算**: 使用getter实现动态状态计算

### 5. 组件注册管理

```javascript
if (group.isInGroup) {
    const dropdown = useComponent();
    useEffect(() => {
        env[DROPDOWN_GROUP].add(dropdown.state);
        return () => env[DROPDOWN_GROUP].delete(dropdown.state);
    });
}
```

**注册管理功能**:
- **条件注册**: 只有在分组中才进行注册
- **组件引用**: 获取当前组件的引用
- **状态注册**: 将组件状态添加到分组集合
- **自动清理**: 返回清理函数用于组件销毁时移除

## 使用场景

### 1. 基础分组检测

```javascript
// 在下拉菜单组件中使用分组钩子
class MyDropdown extends Component {
    setup() {
        this.group = useDropdownGroup();
        this.state = useDropdownState();
    }

    render() {
        return xml`
            <div class="dropdown ${this.group.isInGroup ? 'in-group' : 'standalone'}">
                <button class="btn btn-primary">
                    菜单 ${this.group.isOpen ? '(分组已打开)' : ''}
                </button>
                <!-- 下拉内容 -->
            </div>
        `;
    }
}
```

### 2. 条件性行为控制

```javascript
// 根据分组状态调整行为
class ConditionalDropdown extends Component {
    setup() {
        this.group = useDropdownGroup();
        this.state = useDropdownState();
    }

    handleClick() {
        if (this.group.isInGroup) {
            // 在分组中的行为
            if (this.group.isOpen) {
                // 如果分组中有其他打开的下拉菜单，先关闭它们
                this.closeOthersInGroup();
            }
        }
        
        // 切换当前下拉菜单
        this.state.toggle();
    }

    closeOthersInGroup() {
        // 通知分组关闭其他下拉菜单
        this.env[DROPDOWN_GROUP].forEach(dropdown => {
            if (dropdown !== this.state && dropdown.isOpen) {
                dropdown.close();
            }
        });
    }
}
```

### 3. 分组状态监听

```javascript
// 监听分组状态变化
class GroupAwareDropdown extends Component {
    setup() {
        this.group = useDropdownGroup();
        this.state = useDropdownState();

        // 监听分组状态变化
        useEffect(() => {
            if (this.group.isInGroup) {
                this.onGroupStateChange();
            }
        }, () => [this.group.isOpen]);
    }

    onGroupStateChange() {
        if (this.group.isOpen) {
            console.log('分组中有下拉菜单打开');
            this.handleGroupOpen();
        } else {
            console.log('分组中所有下拉菜单都已关闭');
            this.handleGroupClose();
        }
    }

    handleGroupOpen() {
        // 分组打开时的处理逻辑
        this.loadGroupData();
    }

    handleGroupClose() {
        // 分组关闭时的处理逻辑
        this.clearGroupData();
    }
}
```

### 4. 分组协调行为

```javascript
// 实现分组内的协调行为
class CoordinatedDropdown extends Component {
    setup() {
        this.group = useDropdownGroup();
        this.state = useDropdownState({
            onOpen: () => {
                if (this.group.isInGroup) {
                    this.coordinateWithGroup();
                }
            }
        });
    }

    coordinateWithGroup() {
        // 与分组内其他下拉菜单协调
        if (this.group.isOpen) {
            // 实现互斥行为：只允许一个下拉菜单打开
            this.env[DROPDOWN_GROUP].forEach(dropdown => {
                if (dropdown !== this.state && dropdown.isOpen) {
                    dropdown.close();
                }
            });
        }
    }

    render() {
        const groupStatus = this.group.isInGroup ? 
            (this.group.isOpen ? 'group-active' : 'group-inactive') : 
            'no-group';

        return xml`
            <div class="dropdown ${groupStatus}">
                <button class="btn btn-secondary">
                    协调菜单
                </button>
                <!-- 下拉内容 -->
            </div>
        `;
    }
}
```

## 增强示例

```javascript
// 增强的下拉分组钩子
const EnhancedDropdownGroupHook = {
    // 增强的分组钩子
    useAdvancedDropdownGroup: (options = {}) => {
        const {
            autoClose = true,
            maxOpen = 1,
            onGroupChange,
            trackStats = false
        } = options;

        const env = useEnv();
        const component = useComponent();

        // 基础分组状态
        const baseGroup = useDropdownGroup();

        // 增强状态
        const enhancedState = useState({
            groupSize: 0,
            openCount: 0,
            myIndex: -1,
            stats: {
                totalOpens: 0,
                totalCloses: 0,
                averageOpenTime: 0
            }
        });

        // 更新分组统计
        const updateGroupStats = () => {
            if (baseGroup.isInGroup) {
                const group = env[DROPDOWN_GROUP];
                enhancedState.groupSize = group.size;
                enhancedState.openCount = [...group].filter(d => d.isOpen).length;
                
                // 找到当前组件在分组中的索引
                const groupArray = [...group];
                enhancedState.myIndex = groupArray.findIndex(d => d === component.state);
            }
        };

        // 监听分组变化
        useEffect(() => {
            if (baseGroup.isInGroup) {
                updateGroupStats();
                onGroupChange?.({
                    isInGroup: baseGroup.isInGroup,
                    isOpen: baseGroup.isOpen,
                    groupSize: enhancedState.groupSize,
                    openCount: enhancedState.openCount
                });
            }
        }, () => [baseGroup.isOpen, enhancedState.groupSize]);

        const enhancedGroup = {
            ...baseGroup,
            
            // 增强属性
            groupSize: enhancedState.groupSize,
            openCount: enhancedState.openCount,
            myIndex: enhancedState.myIndex,
            canOpen: enhancedState.openCount < maxOpen,
            
            // 增强方法
            closeOthers: () => {
                if (baseGroup.isInGroup) {
                    const group = env[DROPDOWN_GROUP];
                    [...group].forEach(dropdown => {
                        if (dropdown !== component.state && dropdown.isOpen) {
                            dropdown.close();
                        }
                    });
                }
            },

            closeAll: () => {
                if (baseGroup.isInGroup) {
                    const group = env[DROPDOWN_GROUP];
                    [...group].forEach(dropdown => {
                        if (dropdown.isOpen) {
                            dropdown.close();
                        }
                    });
                }
            },

            getOpenDropdowns: () => {
                if (baseGroup.isInGroup) {
                    const group = env[DROPDOWN_GROUP];
                    return [...group].filter(d => d.isOpen);
                }
                return [];
            },

            getGroupMembers: () => {
                if (baseGroup.isInGroup) {
                    return [...env[DROPDOWN_GROUP]];
                }
                return [];
            },

            isFirst: () => enhancedState.myIndex === 0,
            isLast: () => enhancedState.myIndex === enhancedState.groupSize - 1,

            getStats: () => ({ ...enhancedState.stats }),

            // 协调打开
            coordinatedOpen: () => {
                if (!baseGroup.isInGroup) {
                    return component.state.open();
                }

                if (autoClose && enhancedState.openCount >= maxOpen) {
                    enhancedGroup.closeOthers();
                }

                if (enhancedGroup.canOpen || autoClose) {
                    return component.state.open();
                }
            }
        };

        // 统计跟踪
        if (trackStats && baseGroup.isInGroup) {
            useEffect(() => {
                const originalOpen = component.state.open;
                const originalClose = component.state.close;

                component.state.open = (...args) => {
                    enhancedState.stats.totalOpens++;
                    const startTime = Date.now();
                    
                    const result = originalOpen.apply(component.state, args);
                    
                    // 记录打开时间
                    component.state._openTime = startTime;
                    
                    return result;
                };

                component.state.close = (...args) => {
                    enhancedState.stats.totalCloses++;
                    
                    if (component.state._openTime) {
                        const openDuration = Date.now() - component.state._openTime;
                        const totalTime = enhancedState.stats.averageOpenTime * (enhancedState.stats.totalCloses - 1) + openDuration;
                        enhancedState.stats.averageOpenTime = totalTime / enhancedState.stats.totalCloses;
                    }
                    
                    return originalClose.apply(component.state, args);
                };

                return () => {
                    component.state.open = originalOpen;
                    component.state.close = originalClose;
                };
            });
        }

        return enhancedGroup;
    },

    // 分组管理器钩子
    useDropdownGroupManager: () => {
        const env = useEnv();
        
        return {
            createGroup: (groupId) => {
                if (!env[DROPDOWN_GROUP]) {
                    env[DROPDOWN_GROUP] = new Set();
                }
                return env[DROPDOWN_GROUP];
            },

            destroyGroup: () => {
                if (env[DROPDOWN_GROUP]) {
                    env[DROPDOWN_GROUP].clear();
                    delete env[DROPDOWN_GROUP];
                }
            },

            getGroupInfo: () => {
                if (env[DROPDOWN_GROUP]) {
                    const group = env[DROPDOWN_GROUP];
                    return {
                        size: group.size,
                        openCount: [...group].filter(d => d.isOpen).length,
                        members: [...group]
                    };
                }
                return null;
            },

            broadcastToGroup: (message) => {
                if (env[DROPDOWN_GROUP]) {
                    [...env[DROPDOWN_GROUP]].forEach(dropdown => {
                        if (dropdown.onGroupMessage) {
                            dropdown.onGroupMessage(message);
                        }
                    });
                }
            }
        };
    }
};

// 使用示例
class AdvancedGroupDropdown extends Component {
    setup() {
        this.group = EnhancedDropdownGroupHook.useAdvancedDropdownGroup({
            autoClose: true,
            maxOpen: 1,
            trackStats: true,
            onGroupChange: (info) => {
                console.log('Group changed:', info);
            }
        });

        this.state = useDropdownState();
    }

    handleSmartOpen() {
        // 使用协调打开
        this.group.coordinatedOpen();
    }

    showGroupStats() {
        const stats = this.group.getStats();
        console.log('Group statistics:', stats);
        console.log('Group info:', {
            size: this.group.groupSize,
            openCount: this.group.openCount,
            canOpen: this.group.canOpen,
            isFirst: this.group.isFirst(),
            isLast: this.group.isLast()
        });
    }

    render() {
        return xml`
            <div class="advanced-dropdown">
                <button 
                    class="btn btn-primary"
                    t-on-click="handleSmartOpen"
                >
                    Smart Menu (${this.group.myIndex + 1}/${this.group.groupSize})
                </button>
                
                <button 
                    class="btn btn-info btn-sm"
                    t-on-click="showGroupStats"
                    t-if="group.isInGroup"
                >
                    Stats
                </button>
                
                <button 
                    class="btn btn-warning btn-sm"
                    t-on-click="group.closeOthers"
                    t-if="group.openCount > 1"
                >
                    Close Others
                </button>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 轻量设计
- 最小化的代码实现
- 高效的状态检测
- 低开销的分组管理

### 2. 自动管理
- 自动注册和清理
- 动态状态计算
- 生命周期集成

### 3. 环境集成
- 基于环境的分组检测
- 无侵入式的集成
- 灵活的分组支持

### 4. 响应式状态
- 动态的开启状态检测
- 实时的分组状态更新
- 高效的状态同步

## 设计模式

### 1. 钩子模式 (Hook Pattern)
- 状态逻辑的封装和复用
- 组件间的状态共享

### 2. 观察者模式 (Observer Pattern)
- 分组状态的动态监听
- 状态变化的响应式更新

### 3. 注册表模式 (Registry Pattern)
- 组件的自动注册和管理
- 集中式的状态管理

## 注意事项

1. **内存管理**: 确保组件销毁时正确清理分组引用
2. **状态同步**: 保持分组状态与实际状态的一致性
3. **性能优化**: 避免频繁的分组状态检测
4. **环境依赖**: 正确处理环境中分组的存在性

## 扩展建议

1. **分组标识**: 支持命名分组和多分组管理
2. **事件系统**: 添加分组事件的发布订阅机制
3. **状态持久化**: 支持分组状态的持久化
4. **性能监控**: 监控分组操作的性能指标
5. **调试工具**: 提供分组状态的调试工具

该下拉分组钩子为Odoo Web应用提供了简洁高效的下拉菜单分组管理能力，通过自动注册和动态状态检测确保了分组内下拉菜单的协调行为。

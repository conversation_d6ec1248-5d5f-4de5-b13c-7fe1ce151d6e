# DropdownNesting - 下拉嵌套行为

## 概述

`dropdown_nesting.js` 是 Odoo Web 核心模块的下拉嵌套行为管理器，提供了多级下拉菜单的嵌套管理和通信机制。该模块通过状态管理类和钩子函数实现了下拉菜单的父子关系管理、状态同步、键盘导航和自动关闭逻辑，确保了嵌套下拉菜单的协调行为和良好的用户体验，支持复杂的多级菜单结构。

## 文件信息
- **路径**: `/web/static/src/core/dropdown/_behaviours/dropdown_nesting.js`
- **行数**: 152
- **模块**: `@web/core/dropdown/_behaviours/dropdown_nesting`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                        // OWL框架
'@web/core/l10n/localization'      // 本地化服务
'@web/core/utils/hooks'            // 工具钩子
'@web/core/utils/reactive'         // 响应式工具
```

## 核心功能

### 1. 常量定义

```javascript
const DROPDOWN_NESTING = Symbol("dropdownNesting");
const BUS = new EventBus();
```

**常量功能**:
- **嵌套标识**: 使用Symbol确保唯一的嵌套标识符
- **事件总线**: 创建全局事件总线用于下拉菜单通信
- **命名空间**: 避免与其他模块的命名冲突
- **通信机制**: 提供下拉菜单间的事件通信

### 2. DropdownNestingState类

```javascript
class DropdownNestingState {
    constructor({ parent, close }) {
        this._isOpen = false;
        this.parent = parent;
        this.children = new Set();
        this.close = close;

        parent?.children.add(this);
    }
}
```

**状态类功能**:
- **父子关系**: 管理下拉菜单的父子关系
- **状态跟踪**: 跟踪下拉菜单的开启状态
- **子集管理**: 使用Set管理子级下拉菜单
- **关闭回调**: 存储关闭下拉菜单的回调函数
- **自动注册**: 自动将自己注册到父级的子集中

### 3. 状态属性管理

```javascript
set isOpen(value) {
    this._isOpen = value;
    if (this._isOpen) {
        BUS.trigger("dropdown-opened", this);
    }
}

get isOpen() {
    return this._isOpen;
}
```

**状态管理功能**:
- **状态设置**: 设置下拉菜单的开启状态
- **事件触发**: 开启时触发全局事件
- **状态获取**: 获取当前的开启状态
- **通信机制**: 通过事件总线通知其他下拉菜单

### 4. 层级管理方法

```javascript
remove() {
    this.parent?.children.delete(this);
}

closeAllParents() {
    this.close();
    if (this.parent) {
        this.parent.closeAllParents();
    }
}

closeChildren() {
    this.children.forEach((child) => child.close());
}
```

**层级管理功能**:
- **移除管理**: 从父级的子集中移除自己
- **向上关闭**: 递归关闭所有父级下拉菜单
- **向下关闭**: 关闭所有子级下拉菜单
- **级联操作**: 支持级联的关闭操作

### 5. 变化处理逻辑

```javascript
shouldIgnoreChanges(other) {
    return (
        other === this ||
        other.activeEl !== this.activeEl ||
        [...this.children].some((child) => child.shouldIgnoreChanges(other))
    );
}

handleChange(other) {
    if (this.shouldIgnoreChanges(other)) {
        return;
    }

    if (other.isOpen && this.isOpen) {
        this.close();
    }
}
```

**变化处理功能**:
- **忽略检查**: 检查是否应该忽略其他下拉菜单的变化
- **活跃元素**: 基于活跃元素判断是否在同一上下文
- **子级检查**: 递归检查子级是否应该忽略变化
- **冲突解决**: 解决多个下拉菜单同时打开的冲突

### 6. useDropdownNesting钩子

```javascript
function useDropdownNesting(state) {
    const env = useEnv();
    const current = new DropdownNestingState({
        parent: env[DROPDOWN_NESTING],
        close: () => state.close(),
    });

    // UI活跃元素相关行为设置
    const uiService = useService("ui");
    useEffect(
        () => {
            Promise.resolve().then(() => {
                current.activeEl = uiService.activeElement;
            });
        },
        () => []
    );

    useChildSubEnv({ [DROPDOWN_NESTING]: current });
    useBus(BUS, "dropdown-opened", ({ detail: other }) => current.handleChange(other));

    effect(
        (state) => {
            current.isOpen = state.isOpen;
        },
        [state]
    );

    onWillDestroy(() => {
        current.remove();
    });

    return {
        get hasParent() {
            return Boolean(current.parent);
        },
        navigationOptions: {
            // 导航选项配置
        },
    };
}
```

**钩子功能**:
- **状态创建**: 创建嵌套状态实例
- **环境设置**: 为子组件提供嵌套环境
- **事件监听**: 监听其他下拉菜单的开启事件
- **状态同步**: 同步下拉状态与嵌套状态
- **清理管理**: 组件销毁时清理嵌套关系

### 7. 导航选项配置

```javascript
navigationOptions: {
    onEnabled: (items) => {
        if (current.parent) {
            items[0]?.focus();
        }
    },
    onMouseEnter: (item) => {
        if (item.target.classList.contains("o-dropdown")) {
            item.select();
        }
    },
    hotkeys: {
        escape: () => current.close(),
        arrowleft: (index, items) => {
            if (
                localization.direction === "rtl" &&
                items[index]?.target.classList.contains("o-dropdown")
            ) {
                items[index]?.select();
            } else if (current.parent) {
                current.close();
            }
        },
        arrowright: (index, items) => {
            if (localization.direction === "rtl" && current.parent) {
                current.close();
            } else if (items[index]?.target.classList.contains("o-dropdown")) {
                items[index]?.select();
            }
        },
    },
}
```

**导航配置功能**:
- **焦点管理**: 启用时自动聚焦第一个项目
- **鼠标交互**: 鼠标悬停时选择下拉项目
- **键盘快捷键**: 配置ESC、左右箭头键的行为
- **RTL支持**: 支持从右到左的文本方向
- **嵌套导航**: 支持嵌套菜单的键盘导航

## 使用场景

### 1. 基础嵌套下拉菜单

```javascript
// 父级下拉菜单
class ParentDropdown extends Component {
    setup() {
        this.state = useDropdownState();
        this.nesting = useDropdownNesting(this.state);
    }

    render() {
        return xml`
            <Dropdown state="state">
                <button class="btn btn-primary">
                    主菜单
                </button>
                <t t-set-slot="content">
                    <DropdownItem onSelected={() => this.action1()}>
                        操作 1
                    </DropdownItem>
                    <ChildDropdown/>
                </t>
            </Dropdown>
        `;
    }
}

// 子级下拉菜单
class ChildDropdown extends Component {
    setup() {
        this.state = useDropdownState();
        this.nesting = useDropdownNesting(this.state);
    }

    render() {
        return xml`
            <Dropdown state="state">
                <DropdownItem>
                    子菜单 <i class="fa fa-caret-right"/>
                </DropdownItem>
                <t t-set-slot="content">
                    <DropdownItem onSelected={() => this.subAction1()}>
                        子操作 1
                    </DropdownItem>
                    <DropdownItem onSelected={() => this.subAction2()}>
                        子操作 2
                    </DropdownItem>
                </t>
            </Dropdown>
        `;
    }
}
```

### 2. 多级嵌套菜单

```javascript
// 多级嵌套菜单组件
class MultiLevelDropdown extends Component {
    setup() {
        this.state = useDropdownState();
        this.nesting = useDropdownNesting(this.state);
    }

    render() {
        const level = this.getMenuLevel();
        
        return xml`
            <Dropdown state="state" position="${this.getPosition()}">
                <DropdownItem class="${this.getItemClass()}">
                    <t t-esc="props.label"/>
                    <i class="fa fa-caret-right ms-auto" t-if="props.hasChildren"/>
                </DropdownItem>
                <t t-set-slot="content" t-if="props.hasChildren">
                    <t t-foreach="props.children" t-as="child" t-key="child.id">
                        <t t-if="child.type === 'item'">
                            <DropdownItem onSelected="() => this.handleAction(child)">
                                <t t-esc="child.label"/>
                            </DropdownItem>
                        </t>
                        <t t-elif="child.type === 'submenu'">
                            <MultiLevelDropdown 
                                label="child.label"
                                hasChildren="child.children.length > 0"
                                children="child.children"
                            />
                        </t>
                    </t>
                </t>
            </Dropdown>
        `;
    }

    getMenuLevel() {
        let level = 0;
        let current = this.nesting;
        while (current.hasParent) {
            level++;
            current = current.parent;
        }
        return level;
    }

    getPosition() {
        return this.nesting.hasParent ? "right-start" : "bottom-start";
    }

    getItemClass() {
        return this.nesting.hasParent ? "dropdown-submenu" : "dropdown-item";
    }
}
```

### 3. 智能关闭行为

```javascript
// 智能关闭行为的下拉菜单
class SmartDropdown extends Component {
    setup() {
        this.state = useDropdownState({
            onOpen: () => {
                this.handleSmartOpen();
            },
            onClose: () => {
                this.handleSmartClose();
            }
        });
        this.nesting = useDropdownNesting(this.state);
    }

    handleSmartOpen() {
        // 智能打开逻辑
        if (this.nesting.hasParent) {
            // 如果有父级，关闭同级的其他下拉菜单
            this.closeSiblings();
        } else {
            // 如果是顶级，关闭所有其他顶级下拉菜单
            this.closeOtherTopLevel();
        }
    }

    handleSmartClose() {
        // 智能关闭逻辑
        if (this.props.cascadeClose) {
            // 级联关闭所有子级
            this.closeAllChildren();
        }
    }

    closeSiblings() {
        // 关闭同级的其他下拉菜单
        const parent = this.env[DROPDOWN_NESTING];
        if (parent) {
            parent.children.forEach(sibling => {
                if (sibling !== this.nesting.current && sibling.isOpen) {
                    sibling.close();
                }
            });
        }
    }

    closeAllChildren() {
        // 递归关闭所有子级
        const closeRecursively = (nestingState) => {
            nestingState.children.forEach(child => {
                child.close();
                closeRecursively(child);
            });
        };
        closeRecursively(this.nesting.current);
    }
}
```

### 4. 上下文感知的嵌套菜单

```javascript
// 上下文感知的嵌套菜单
class ContextAwareDropdown extends Component {
    setup() {
        this.state = useDropdownState();
        this.nesting = useDropdownNesting(this.state);
        
        // 监听嵌套状态变化
        useEffect(() => {
            this.updateContextualBehavior();
        }, () => [this.nesting.hasParent]);
    }

    updateContextualBehavior() {
        if (this.nesting.hasParent) {
            // 子级菜单的行为
            this.setupChildBehavior();
        } else {
            // 顶级菜单的行为
            this.setupParentBehavior();
        }
    }

    setupChildBehavior() {
        // 子级菜单特有的行为
        this.autoCloseDelay = 1000; // 更短的自动关闭延迟
        this.hoverOpenDelay = 200;  // 更快的悬停打开
    }

    setupParentBehavior() {
        // 顶级菜单特有的行为
        this.autoCloseDelay = 3000; // 更长的自动关闭延迟
        this.hoverOpenDelay = 500;  // 更慢的悬停打开
    }

    render() {
        const contextClass = this.nesting.hasParent ? 'nested-dropdown' : 'root-dropdown';
        
        return xml`
            <Dropdown 
                state="state" 
                class="${contextClass}"
                holdOnHover="${!this.nesting.hasParent}"
            >
                <DropdownItem class="${this.getItemClass()}">
                    <t t-esc="props.label"/>
                    <i class="fa fa-caret-down" t-if="!nesting.hasParent"/>
                    <i class="fa fa-caret-right" t-if="nesting.hasParent"/>
                </DropdownItem>
                <t t-set-slot="content">
                    <!-- 菜单内容 -->
                </t>
            </Dropdown>
        `;
    }

    getItemClass() {
        const baseClass = 'dropdown-item';
        const levelClass = this.nesting.hasParent ? 'submenu-item' : 'main-item';
        return `${baseClass} ${levelClass}`;
    }
}
```

## 增强示例

```javascript
// 增强的下拉嵌套管理
const EnhancedDropdownNesting = {
    // 增强的嵌套状态类
    createAdvancedNestingState: () => {
        class AdvancedDropdownNestingState extends DropdownNestingState {
            constructor(options) {
                super(options);
                
                this.id = options.id || Math.random().toString(36).substr(2, 9);
                this.level = this.calculateLevel();
                this.metadata = options.metadata || {};
                this.stats = {
                    openCount: 0,
                    totalOpenTime: 0,
                    averageOpenTime: 0,
                    lastOpenTime: null,
                    lastCloseTime: null
                };
                this.eventHandlers = new Map();
            }

            calculateLevel() {
                let level = 0;
                let current = this.parent;
                while (current) {
                    level++;
                    current = current.parent;
                }
                return level;
            }

            // 增强的打开方法
            open() {
                const openTime = Date.now();
                this.stats.lastOpenTime = openTime;
                this.stats.openCount++;
                
                this.isOpen = true;
                this.triggerEvent('open', { openTime, level: this.level });
            }

            // 增强的关闭方法
            close() {
                const closeTime = Date.now();
                this.stats.lastCloseTime = closeTime;
                
                if (this.stats.lastOpenTime) {
                    const openDuration = closeTime - this.stats.lastOpenTime;
                    this.stats.totalOpenTime += openDuration;
                    this.stats.averageOpenTime = this.stats.totalOpenTime / this.stats.openCount;
                }
                
                super.close();
                this.triggerEvent('close', { closeTime, level: this.level });
            }

            // 事件系统
            on(event, handler) {
                if (!this.eventHandlers.has(event)) {
                    this.eventHandlers.set(event, new Set());
                }
                this.eventHandlers.get(event).add(handler);
            }

            off(event, handler) {
                if (this.eventHandlers.has(event)) {
                    this.eventHandlers.get(event).delete(handler);
                }
            }

            triggerEvent(event, data) {
                if (this.eventHandlers.has(event)) {
                    this.eventHandlers.get(event).forEach(handler => {
                        handler(data);
                    });
                }
            }

            // 获取嵌套路径
            getPath() {
                const path = [];
                let current = this;
                while (current) {
                    path.unshift(current.id);
                    current = current.parent;
                }
                return path;
            }

            // 获取所有后代
            getAllDescendants() {
                const descendants = [];
                const traverse = (node) => {
                    node.children.forEach(child => {
                        descendants.push(child);
                        traverse(child);
                    });
                };
                traverse(this);
                return descendants;
            }

            // 查找特定后代
            findDescendant(predicate) {
                const descendants = this.getAllDescendants();
                return descendants.find(predicate);
            }

            // 获取统计信息
            getStats() {
                return { ...this.stats };
            }

            // 重置统计信息
            resetStats() {
                this.stats = {
                    openCount: 0,
                    totalOpenTime: 0,
                    averageOpenTime: 0,
                    lastOpenTime: null,
                    lastCloseTime: null
                };
            }
        }

        return AdvancedDropdownNestingState;
    },

    // 增强的嵌套钩子
    useAdvancedDropdownNesting: (state, options = {}) => {
        const {
            enableStats = false,
            enableEvents = false,
            maxDepth = 5,
            autoCloseDelay = 0,
            onLevelChange,
            onDepthExceeded
        } = options;

        const env = useEnv();
        const AdvancedNestingState = EnhancedDropdownNesting.createAdvancedNestingState();
        
        const current = new AdvancedNestingState({
            parent: env[DROPDOWN_NESTING],
            close: () => state.close(),
            id: options.id,
            metadata: options.metadata
        });

        // 深度检查
        if (current.level >= maxDepth) {
            console.warn(`Dropdown nesting depth exceeded: ${current.level}`);
            onDepthExceeded?.(current.level);
        }

        // 级别变化回调
        if (onLevelChange) {
            onLevelChange(current.level);
        }

        // 统计功能
        if (enableStats) {
            current.on('open', (data) => {
                console.log(`Dropdown opened at level ${data.level}:`, data);
            });
            
            current.on('close', (data) => {
                console.log(`Dropdown closed at level ${data.level}:`, data);
            });
        }

        // 自动关闭
        if (autoCloseDelay > 0) {
            let autoCloseTimer;
            current.on('open', () => {
                autoCloseTimer = setTimeout(() => {
                    current.close();
                }, autoCloseDelay);
            });
            
            current.on('close', () => {
                if (autoCloseTimer) {
                    clearTimeout(autoCloseTimer);
                }
            });
        }

        // 基础嵌套设置
        const baseNesting = useDropdownNesting(state);

        return {
            ...baseNesting,
            
            // 增强属性
            id: current.id,
            level: current.level,
            path: current.getPath(),
            
            // 增强方法
            getStats: () => current.getStats(),
            resetStats: () => current.resetStats(),
            getAllDescendants: () => current.getAllDescendants(),
            findDescendant: (predicate) => current.findDescendant(predicate),
            
            // 事件方法
            on: (event, handler) => current.on(event, handler),
            off: (event, handler) => current.off(event, handler),
            
            // 导航增强
            navigationOptions: {
                ...baseNesting.navigationOptions,
                
                // 增强的热键
                hotkeys: {
                    ...baseNesting.navigationOptions.hotkeys,
                    
                    // 快速关闭所有
                    'ctrl+escape': () => {
                        current.closeAllParents();
                    },
                    
                    // 关闭子级
                    'shift+escape': () => {
                        current.closeChildren();
                    },
                    
                    // 导航到父级
                    'alt+arrowup': () => {
                        if (current.parent) {
                            current.close();
                            // 聚焦到父级
                        }
                    }
                }
            }
        };
    }
};

// 使用示例
class AdvancedNestedDropdown extends Component {
    setup() {
        this.state = useDropdownState();
        this.nesting = EnhancedDropdownNesting.useAdvancedDropdownNesting(this.state, {
            enableStats: true,
            enableEvents: true,
            maxDepth: 3,
            autoCloseDelay: 5000,
            id: `dropdown-${this.props.id}`,
            metadata: { type: 'menu', category: this.props.category },
            onLevelChange: (level) => {
                console.log(`Dropdown level: ${level}`);
            },
            onDepthExceeded: (level) => {
                this.notification.add(`Maximum nesting depth exceeded: ${level}`, {
                    type: 'warning'
                });
            }
        });

        // 监听嵌套事件
        this.nesting.on('open', (data) => {
            this.trackMenuOpen(data);
        });

        this.nesting.on('close', (data) => {
            this.trackMenuClose(data);
        });
    }

    showNestingInfo() {
        const stats = this.nesting.getStats();
        const info = {
            id: this.nesting.id,
            level: this.nesting.level,
            path: this.nesting.path,
            stats: stats,
            descendants: this.nesting.getAllDescendants().length
        };
        
        console.log('Nesting Info:', info);
    }

    render() {
        return xml`
            <Dropdown state="state">
                <DropdownItem>
                    Menu Level ${this.nesting.level}
                    <small class="text-muted ms-2">(${this.nesting.id})</small>
                </DropdownItem>
                <t t-set-slot="content">
                    <DropdownItem onSelected="showNestingInfo">
                        Show Nesting Info
                    </DropdownItem>
                    <!-- 其他菜单项 -->
                </t>
            </Dropdown>
        `;
    }
}
```

## 技术特点

### 1. 层级管理
- 完整的父子关系管理
- 递归的层级操作
- 自动的关系维护

### 2. 事件通信
- 全局事件总线
- 状态变化通知
- 冲突自动解决

### 3. 键盘导航
- RTL文本方向支持
- 嵌套菜单导航
- 智能的焦点管理

### 4. 上下文感知
- 活跃元素检测
- 对话框隔离
- 智能的忽略逻辑

## 设计模式

### 1. 状态模式 (State Pattern)
- 嵌套状态的管理
- 状态转换控制

### 2. 观察者模式 (Observer Pattern)
- 事件驱动的通信
- 状态变化通知

### 3. 组合模式 (Composite Pattern)
- 树形结构的嵌套关系
- 统一的操作接口

## 注意事项

1. **内存管理**: 确保嵌套关系的正确清理
2. **性能优化**: 避免深度嵌套影响性能
3. **事件处理**: 正确处理事件的传播和冒泡
4. **状态同步**: 保持嵌套状态与组件状态的同步

## 扩展建议

1. **深度限制**: 添加嵌套深度的限制和警告
2. **性能监控**: 监控嵌套操作的性能指标
3. **动画支持**: 添加嵌套菜单的动画效果
4. **持久化**: 支持嵌套状态的持久化
5. **调试工具**: 提供嵌套关系的可视化调试工具

该下拉嵌套行为模块为Odoo Web应用提供了完整的多级下拉菜单管理能力，通过智能的状态管理和事件通信确保了复杂嵌套结构的稳定运行。

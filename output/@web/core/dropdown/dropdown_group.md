# DropdownGroup - 下拉分组组件

## 概述

`dropdown_group.js` 是 Odoo Web 核心模块的下拉分组组件，提供了下拉菜单的分组管理功能。该组件通过创建共享的分组环境，允许多个下拉菜单在同一分组内协调行为，支持命名分组和匿名分组，实现了下拉菜单的互斥显示和状态同步，为复杂的下拉菜单布局提供了组织和管理能力。

## 文件信息
- **路径**: `/web/static/src/core/dropdown/dropdown_group.js`
- **行数**: 47
- **模块**: `@web/core/dropdown/dropdown_group`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'    // OWL框架
```

## 核心功能

### 1. 全局分组管理

```javascript
const GROUPS = new Map();

function getGroup(id) {
    if (!GROUPS.has(id)) {
        GROUPS.set(id, {
            group: new Set(),
            count: 0,
        });
    }
    GROUPS.get(id).count++;
    return GROUPS.get(id).group;
}

function removeGroup(id) {
    const groupData = GROUPS.get(id);
    groupData.count--;
    if (groupData.count <= 0) {
        GROUPS.delete(id);
    }
}
```

**分组管理功能**:
- **全局存储**: 使用Map存储所有命名分组
- **引用计数**: 跟踪每个分组的使用次数
- **自动清理**: 当引用计数为0时自动删除分组
- **分组获取**: 按需创建和获取分组实例
- **内存管理**: 防止内存泄漏的自动清理机制

### 2. 分组标识符

```javascript
const DROPDOWN_GROUP = Symbol("dropdownGroup");
```

**标识符功能**:
- **唯一标识**: 使用Symbol确保分组标识的唯一性
- **命名空间**: 避免与其他模块的命名冲突
- **环境键**: 作为环境对象中的键使用
- **类型安全**: 提供类型安全的标识符

### 3. DropdownGroup组件

```javascript
class DropdownGroup extends Component {
    static template = xml`<t t-slot="default"/>`;
    static props = {
        group: { type: String, optional: true },
        slots: Object,
    };
}
```

**组件定义功能**:
- **透明容器**: 使用插槽模板作为透明容器
- **可选分组**: group属性可选，支持命名和匿名分组
- **插槽支持**: 支持默认插槽内容
- **简洁设计**: 最小化的组件实现

### 4. 组件初始化

```javascript
setup() {
    if (this.props.group) {
        const group = getGroup(this.props.group);
        onWillDestroy(() => removeGroup(this.props.group));
        useChildSubEnv({ [DROPDOWN_GROUP]: group });
    } else {
        useChildSubEnv({ [DROPDOWN_GROUP]: new Set() });
    }
}
```

**初始化功能**:
- **条件分组**: 根据是否提供group属性选择分组策略
- **命名分组**: 有group属性时使用全局命名分组
- **匿名分组**: 无group属性时创建独立的匿名分组
- **环境设置**: 为子组件提供分组环境
- **清理管理**: 组件销毁时自动清理分组引用

## 使用场景

### 1. 基础分组使用

```javascript
// 创建一个下拉分组
<DropdownGroup>
    <Dropdown>
        <button class="btn btn-primary">菜单 1</button>
        <t t-set-slot="content">
            <DropdownItem onSelected={() => this.action1()}>操作 1</DropdownItem>
        </t>
    </Dropdown>
    
    <Dropdown>
        <button class="btn btn-secondary">菜单 2</button>
        <t t-set-slot="content">
            <DropdownItem onSelected={() => this.action2()}>操作 2</DropdownItem>
        </t>
    </Dropdown>
</DropdownGroup>
```

### 2. 命名分组

```javascript
// 使用命名分组
<DropdownGroup group="toolbar">
    <Dropdown>
        <button class="btn btn-outline-primary">文件</button>
        <t t-set-slot="content">
            <DropdownItem onSelected={() => this.newFile()}>新建</DropdownItem>
            <DropdownItem onSelected={() => this.openFile()}>打开</DropdownItem>
            <DropdownItem onSelected={() => this.saveFile()}>保存</DropdownItem>
        </t>
    </Dropdown>
    
    <Dropdown>
        <button class="btn btn-outline-secondary">编辑</button>
        <t t-set-slot="content">
            <DropdownItem onSelected={() => this.undo()}>撤销</DropdownItem>
            <DropdownItem onSelected={() => this.redo()}>重做</DropdownItem>
            <DropdownItem onSelected={() => this.cut()}>剪切</DropdownItem>
        </t>
    </Dropdown>
</DropdownGroup>

<!-- 在其他地方使用相同的命名分组 -->
<DropdownGroup group="toolbar">
    <Dropdown>
        <button class="btn btn-outline-info">视图</button>
        <t t-set-slot="content">
            <DropdownItem onSelected={() => this.zoomIn()}>放大</DropdownItem>
            <DropdownItem onSelected={() => this.zoomOut()}>缩小</DropdownItem>
        </t>
    </Dropdown>
</DropdownGroup>
```

### 3. 嵌套分组

```javascript
// 嵌套的下拉分组
<DropdownGroup group="main">
    <Dropdown>
        <button class="btn btn-primary">主菜单</button>
        <t t-set-slot="content">
            <DropdownItem onSelected={() => this.mainAction()}>主操作</DropdownItem>
            
            <!-- 嵌套的子分组 -->
            <DropdownGroup group="submenu">
                <Dropdown>
                    <DropdownItem>
                        子菜单 <i class="fa fa-caret-right"/>
                    </DropdownItem>
                    <t t-set-slot="content">
                        <DropdownItem onSelected={() => this.subAction1()}>子操作 1</DropdownItem>
                        <DropdownItem onSelected={() => this.subAction2()}>子操作 2</DropdownItem>
                    </t>
                </Dropdown>
            </DropdownGroup>
        </t>
    </Dropdown>
</DropdownGroup>
```

### 4. 动态分组管理

```javascript
// 动态分组管理组件
class DynamicDropdownGroup extends Component {
    setup() {
        this.state = useState({
            groupId: 'default',
            dropdowns: []
        });
    }

    addDropdown(dropdown) {
        this.state.dropdowns.push(dropdown);
    }

    removeDropdown(dropdownId) {
        const index = this.state.dropdowns.findIndex(d => d.id === dropdownId);
        if (index >= 0) {
            this.state.dropdowns.splice(index, 1);
        }
    }

    changeGroup(newGroupId) {
        this.state.groupId = newGroupId;
    }

    render() {
        return xml`
            <div class="dynamic-dropdown-group">
                <div class="group-controls mb-2">
                    <label>分组ID:</label>
                    <input 
                        type="text" 
                        class="form-control d-inline-block w-auto ms-2"
                        t-model="state.groupId"
                    />
                    <button 
                        class="btn btn-sm btn-primary ms-2"
                        t-on-click="() => this.changeGroup(state.groupId)"
                    >
                        更新分组
                    </button>
                </div>
                
                <DropdownGroup group="${state.groupId}">
                    <t t-foreach="state.dropdowns" t-as="dropdown" t-key="dropdown.id">
                        <Dropdown>
                            <button class="btn btn-outline-primary me-2">
                                <t t-esc="dropdown.label"/>
                            </button>
                            <t t-set-slot="content">
                                <t t-foreach="dropdown.items" t-as="item" t-key="item.id">
                                    <DropdownItem onSelected="item.onSelected">
                                        <t t-esc="item.label"/>
                                    </DropdownItem>
                                </t>
                            </t>
                        </Dropdown>
                    </t>
                </DropdownGroup>
            </div>
        `;
    }
}
```

## 增强示例

```javascript
// 增强的下拉分组组件
const EnhancedDropdownGroup = {
    createAdvancedGroup: () => {
        class AdvancedDropdownGroup extends DropdownGroup {
            static props = {
                ...DropdownGroup.props,
                
                // 增强属性
                maxOpen: { type: Number, optional: true },
                autoClose: { type: Boolean, optional: true },
                exclusive: { type: Boolean, optional: true },
                onGroupChange: { type: Function, optional: true },
                trackStats: { type: Boolean, optional: true },
                groupClass: { type: String, optional: true },
                groupStyle: { type: Object, optional: true }
            };

            static defaultProps = {
                ...DropdownGroup.defaultProps,
                maxOpen: 1,
                autoClose: true,
                exclusive: true,
                trackStats: false
            };

            setup() {
                // 创建增强的分组管理
                const groupId = this.props.group || `anonymous_${Math.random().toString(36).substr(2, 9)}`;
                const enhancedGroup = this.createEnhancedGroup(groupId);
                
                // 设置环境
                useChildSubEnv({ 
                    [DROPDOWN_GROUP]: enhancedGroup.group,
                    [Symbol('groupManager')]: enhancedGroup.manager
                });

                // 清理管理
                onWillDestroy(() => {
                    enhancedGroup.destroy();
                });

                // 统计跟踪
                if (this.props.trackStats) {
                    this.setupStatsTracking(enhancedGroup);
                }
            }

            createEnhancedGroup(groupId) {
                const group = new Set();
                const stats = {
                    totalOpens: 0,
                    totalCloses: 0,
                    averageOpenTime: 0,
                    maxConcurrentOpen: 0,
                    groupCreatedAt: Date.now()
                };

                const manager = {
                    id: groupId,
                    group: group,
                    stats: stats,
                    
                    // 添加下拉菜单到分组
                    add: (dropdown) => {
                        group.add(dropdown);
                        this.onDropdownAdded(dropdown);
                    },
                    
                    // 从分组中移除下拉菜单
                    remove: (dropdown) => {
                        group.delete(dropdown);
                        this.onDropdownRemoved(dropdown);
                    },
                    
                    // 获取打开的下拉菜单
                    getOpenDropdowns: () => {
                        return [...group].filter(d => d.isOpen);
                    },
                    
                    // 关闭所有下拉菜单
                    closeAll: () => {
                        [...group].forEach(d => {
                            if (d.isOpen) {
                                d.close();
                            }
                        });
                    },
                    
                    // 关闭除指定外的所有下拉菜单
                    closeOthers: (except) => {
                        [...group].forEach(d => {
                            if (d !== except && d.isOpen) {
                                d.close();
                            }
                        });
                    },
                    
                    // 检查是否可以打开新的下拉菜单
                    canOpen: () => {
                        const openCount = manager.getOpenDropdowns().length;
                        return openCount < this.props.maxOpen;
                    },
                    
                    // 智能打开
                    smartOpen: (dropdown) => {
                        if (this.props.exclusive || !manager.canOpen()) {
                            manager.closeOthers(dropdown);
                        }
                        dropdown.open();
                    },
                    
                    // 获取统计信息
                    getStats: () => ({ ...stats }),
                    
                    // 重置统计信息
                    resetStats: () => {
                        Object.assign(stats, {
                            totalOpens: 0,
                            totalCloses: 0,
                            averageOpenTime: 0,
                            maxConcurrentOpen: 0
                        });
                    },
                    
                    // 销毁分组
                    destroy: () => {
                        group.clear();
                        if (this.props.group) {
                            removeGroup(this.props.group);
                        }
                    }
                };

                return { group, manager };
            }

            onDropdownAdded(dropdown) {
                // 包装原始的打开和关闭方法
                const originalOpen = dropdown.open;
                const originalClose = dropdown.close;

                dropdown.open = (...args) => {
                    const manager = this.env[Symbol('groupManager')];
                    
                    if (this.props.exclusive) {
                        manager.closeOthers(dropdown);
                    } else if (!manager.canOpen()) {
                        console.warn('Maximum open dropdowns reached');
                        return;
                    }
                    
                    const result = originalOpen.apply(dropdown, args);
                    
                    // 更新统计
                    if (this.props.trackStats) {
                        manager.stats.totalOpens++;
                        const openCount = manager.getOpenDropdowns().length;
                        manager.stats.maxConcurrentOpen = Math.max(
                            manager.stats.maxConcurrentOpen, 
                            openCount
                        );
                    }
                    
                    // 触发分组变化事件
                    this.props.onGroupChange?.({
                        action: 'open',
                        dropdown: dropdown,
                        openCount: manager.getOpenDropdowns().length
                    });
                    
                    return result;
                };

                dropdown.close = (...args) => {
                    const result = originalClose.apply(dropdown, args);
                    
                    // 更新统计
                    if (this.props.trackStats) {
                        const manager = this.env[Symbol('groupManager')];
                        manager.stats.totalCloses++;
                    }
                    
                    // 触发分组变化事件
                    this.props.onGroupChange?.({
                        action: 'close',
                        dropdown: dropdown,
                        openCount: manager.getOpenDropdowns().length
                    });
                    
                    return result;
                };
            }

            onDropdownRemoved(dropdown) {
                // 恢复原始方法（如果需要）
                // 这里可以添加清理逻辑
            }

            setupStatsTracking(enhancedGroup) {
                // 定期输出统计信息
                const interval = setInterval(() => {
                    const stats = enhancedGroup.manager.getStats();
                    console.log(`Group ${enhancedGroup.manager.id} stats:`, stats);
                }, 30000); // 每30秒

                onWillDestroy(() => {
                    clearInterval(interval);
                });
            }

            // 增强的模板
            static template = xml`
                <div 
                    class="advanced-dropdown-group ${props.groupClass || ''}"
                    t-att-style="props.groupStyle"
                >
                    <t t-slot="default"/>
                </div>
            `;
        }

        return AdvancedDropdownGroup;
    },

    // 分组管理器工具
    createGroupManager: () => {
        return {
            // 获取所有分组
            getAllGroups: () => {
                return new Map(GROUPS);
            },
            
            // 获取分组信息
            getGroupInfo: (groupId) => {
                const groupData = GROUPS.get(groupId);
                if (!groupData) return null;
                
                return {
                    id: groupId,
                    size: groupData.group.size,
                    count: groupData.count,
                    openCount: [...groupData.group].filter(d => d.isOpen).length,
                    members: [...groupData.group]
                };
            },
            
            // 清理所有分组
            clearAllGroups: () => {
                GROUPS.clear();
            },
            
            // 强制关闭分组
            forceCloseGroup: (groupId) => {
                const groupData = GROUPS.get(groupId);
                if (groupData) {
                    [...groupData.group].forEach(dropdown => {
                        if (dropdown.isOpen) {
                            dropdown.close();
                        }
                    });
                }
            },
            
            // 获取分组统计
            getGlobalStats: () => {
                const stats = {
                    totalGroups: GROUPS.size,
                    totalDropdowns: 0,
                    totalOpenDropdowns: 0
                };
                
                GROUPS.forEach(groupData => {
                    stats.totalDropdowns += groupData.group.size;
                    stats.totalOpenDropdowns += [...groupData.group].filter(d => d.isOpen).length;
                });
                
                return stats;
            }
        };
    }
};

// 使用示例
const AdvancedGroup = EnhancedDropdownGroup.createAdvancedGroup();
const GroupManager = EnhancedDropdownGroup.createGroupManager();

// 高级分组使用
<AdvancedGroup 
    group="toolbar"
    maxOpen={2}
    exclusive={false}
    trackStats={true}
    groupClass="custom-group"
    onGroupChange={(event) => console.log('Group changed:', event)}
>
    <Dropdown>
        <button class="btn btn-primary">菜单 1</button>
        <t t-set-slot="content">
            <DropdownItem onSelected={() => this.action1()}>操作 1</DropdownItem>
        </t>
    </Dropdown>
    
    <Dropdown>
        <button class="btn btn-secondary">菜单 2</button>
        <t t-set-slot="content">
            <DropdownItem onSelected={() => this.action2()}>操作 2</DropdownItem>
        </t>
    </Dropdown>
</AdvancedGroup>

// 分组管理
const groupInfo = GroupManager.getGroupInfo('toolbar');
console.log('Toolbar group info:', groupInfo);

const globalStats = GroupManager.getGlobalStats();
console.log('Global dropdown stats:', globalStats);
```

## 技术特点

### 1. 轻量设计
- 最小化的组件实现
- 透明的容器设计
- 高效的分组管理

### 2. 内存管理
- 引用计数机制
- 自动清理功能
- 防止内存泄漏

### 3. 灵活配置
- 命名和匿名分组
- 可选的分组属性
- 环境隔离支持

### 4. 全局管理
- 跨组件的分组共享
- 集中式的分组存储
- 统一的分组接口

## 设计模式

### 1. 容器模式 (Container Pattern)
- 透明的组件容器
- 环境提供功能

### 2. 单例模式 (Singleton Pattern)
- 全局的分组管理
- 唯一的分组实例

### 3. 工厂模式 (Factory Pattern)
- 按需创建分组
- 统一的创建接口

## 注意事项

1. **内存管理**: 确保分组的正确清理和引用计数
2. **命名冲突**: 避免分组名称的冲突
3. **性能优化**: 避免过多的分组创建和销毁
4. **环境隔离**: 正确设置子组件的环境

## 扩展建议

1. **分组策略**: 支持不同的分组策略和行为
2. **事件系统**: 添加分组级别的事件系统
3. **持久化**: 支持分组状态的持久化
4. **可视化**: 提供分组状态的可视化工具
5. **性能监控**: 监控分组操作的性能指标

该下拉分组组件为Odoo Web应用提供了灵活的下拉菜单分组管理能力，通过全局分组管理和环境隔离确保了下拉菜单的协调行为。

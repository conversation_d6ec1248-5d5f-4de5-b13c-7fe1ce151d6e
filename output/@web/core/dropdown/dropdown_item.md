# DropdownItem - 下拉菜单项组件

## 概述

`dropdown_item.js` 是 Odoo Web 核心模块的下拉菜单项组件，提供了下拉菜单中单个项目的实现。该组件支持点击事件处理、关闭模式控制、自定义属性和样式定制，为下拉菜单提供了标准化的菜单项元素，确保了一致的用户交互体验和灵活的功能配置。

## 文件信息
- **路径**: `/web/static/src/core/dropdown/dropdown_item.js`
- **行数**: 60
- **模块**: `@web/core/dropdown/dropdown_item`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                           // OWL框架
'@web/core/dropdown/dropdown_hooks'   // 下拉钩子
```

## 核心功能

### 1. 关闭模式枚举

```javascript
const ClosingMode = {
    None: "none",
    ClosestParent: "closest",
    AllParents: "all",
};
```

**关闭模式功能**:
- **None**: 点击后不关闭任何下拉菜单
- **ClosestParent**: 只关闭最近的父级下拉菜单
- **AllParents**: 关闭所有父级下拉菜单
- **灵活控制**: 提供不同的关闭行为选择

### 2. 组件属性定义

```javascript
static props = {
    class: {
        type: [String, Object],
        optional: true,
    },
    onSelected: {
        type: Function,
        optional: true,
    },
    closingMode: {
        type: ClosingMode,
        optional: true,
    },
    attrs: {
        type: Object,
        optional: true,
    },
    slots: { Object, optional: true },
};
```

**属性功能**:
- **样式定制**: class支持字符串或对象形式的CSS类
- **选择回调**: onSelected定义项目被选择时的回调函数
- **关闭模式**: closingMode控制点击后的关闭行为
- **自定义属性**: attrs支持添加自定义HTML属性
- **插槽支持**: slots支持自定义内容插槽

### 3. 默认属性

```javascript
static defaultProps = {
    closingMode: ClosingMode.AllParents,
    attrs: {},
};
```

**默认属性功能**:
- **默认关闭**: 默认关闭所有父级下拉菜单
- **空属性**: 默认提供空的属性对象
- **安全默认**: 提供合理的默认行为

### 4. 组件初始化

```javascript
setup() {
    this.dropdownControl = useDropdownCloser();
}
```

**初始化功能**:
- **关闭控制**: 获取下拉菜单关闭控制器
- **钩子集成**: 集成下拉菜单钩子系统
- **状态管理**: 建立与下拉菜单的连接

### 5. 点击事件处理

```javascript
onClick(ev) {
    if (this.props.attrs && this.props.attrs.href) {
        ev.preventDefault();
    }
    this.props.onSelected?.();
    switch (this.props.closingMode) {
        case ClosingMode.ClosestParent:
            this.dropdownControl.close();
            break;
        case ClosingMode.AllParents:
            this.dropdownControl.closeAll();
            break;
    }
}
```

**点击处理功能**:
- **链接处理**: 如果有href属性，阻止默认行为
- **回调执行**: 执行选择回调函数
- **关闭控制**: 根据关闭模式执行相应的关闭操作
- **模式分支**: 支持不同的关闭行为模式

## 使用场景

### 1. 基础菜单项

```javascript
// 基础下拉菜单项
<DropdownItem onSelected={() => this.handleAction()}>
    基础操作
</DropdownItem>
```

### 2. 带样式的菜单项

```javascript
// 带自定义样式的菜单项
<DropdownItem 
    class="text-danger"
    onSelected={() => this.deleteAction()}
>
    <i class="fa fa-trash me-2"/>
    删除
</DropdownItem>
```

### 3. 链接菜单项

```javascript
// 链接形式的菜单项
<DropdownItem 
    attrs={{ href: "/settings" }}
    onSelected={() => this.navigateToSettings()}
>
    <i class="fa fa-cog me-2"/>
    设置
</DropdownItem>
```

### 4. 不同关闭模式的菜单项

```javascript
// 不关闭下拉菜单的项目
<DropdownItem 
    closingMode="none"
    onSelected={() => this.toggleOption()}
>
    <input type="checkbox" class="me-2"/>
    保持菜单打开
</DropdownItem>

// 只关闭最近父级的项目
<DropdownItem 
    closingMode="closest"
    onSelected={() => this.subMenuAction()}
>
    子菜单操作
</DropdownItem>

// 关闭所有父级的项目（默认）
<DropdownItem 
    closingMode="all"
    onSelected={() => this.finalAction()}
>
    完成操作
</DropdownItem>
```

### 5. 复杂内容的菜单项

```javascript
// 包含复杂内容的菜单项
<DropdownItem 
    class="p-3"
    closingMode="none"
    onSelected={() => this.handleComplexAction()}
>
    <div class="d-flex align-items-center">
        <img src="/avatar.jpg" class="rounded-circle me-2" width="32" height="32"/>
        <div>
            <div class="fw-bold">用户名</div>
            <small class="text-muted"><EMAIL></small>
        </div>
    </div>
</DropdownItem>
```

## 增强示例

```javascript
// 增强的下拉菜单项组件
const EnhancedDropdownItem = {
    createAdvancedItem: () => {
        class AdvancedDropdownItem extends DropdownItem {
            static props = {
                ...DropdownItem.props,
                icon: { type: String, optional: true },
                badge: { type: [String, Number], optional: true },
                badgeClass: { type: String, optional: true },
                disabled: { type: Boolean, optional: true },
                tooltip: { type: String, optional: true },
                shortcut: { type: String, optional: true },
                confirmAction: { type: Boolean, optional: true },
                confirmMessage: { type: String, optional: true },
                loading: { type: Boolean, optional: true },
                selected: { type: Boolean, optional: true }
            };

            static defaultProps = {
                ...DropdownItem.defaultProps,
                disabled: false,
                confirmAction: false,
                confirmMessage: 'Are you sure?',
                loading: false,
                selected: false,
                badgeClass: 'badge-primary'
            };

            setup() {
                super.setup();
                
                // 增强状态
                this.enhancedState = useState({
                    isLoading: this.props.loading,
                    showTooltip: false
                });

                // 配置选项
                this.config = {
                    enableConfirmation: this.props.confirmAction,
                    enableTooltip: !!this.props.tooltip,
                    enableShortcut: !!this.props.shortcut
                };

                // 设置快捷键
                if (this.config.enableShortcut) {
                    this.setupShortcut();
                }
            }

            // 设置快捷键
            setupShortcut() {
                const shortcut = this.props.shortcut;
                if (shortcut) {
                    // 简化的快捷键实现
                    document.addEventListener('keydown', this.handleShortcut.bind(this));
                }
            }

            // 处理快捷键
            handleShortcut(event) {
                const shortcut = this.props.shortcut;
                if (this.matchesShortcut(event, shortcut)) {
                    event.preventDefault();
                    this.onClick(event);
                }
            }

            // 匹配快捷键
            matchesShortcut(event, shortcut) {
                // 简化的快捷键匹配逻辑
                const keys = shortcut.toLowerCase().split('+');
                let matches = true;

                if (keys.includes('ctrl') && !event.ctrlKey) matches = false;
                if (keys.includes('shift') && !event.shiftKey) matches = false;
                if (keys.includes('alt') && !event.altKey) matches = false;

                const key = keys[keys.length - 1];
                if (event.key.toLowerCase() !== key) matches = false;

                return matches;
            }

            // 增强的点击处理
            async onClick(ev) {
                if (this.props.disabled || this.enhancedState.isLoading) {
                    ev.preventDefault();
                    return;
                }

                // 确认对话框
                if (this.config.enableConfirmation) {
                    const confirmed = await this.showConfirmation();
                    if (!confirmed) {
                        return;
                    }
                }

                // 设置加载状态
                this.enhancedState.isLoading = true;

                try {
                    // 调用父类方法
                    super.onClick(ev);
                } finally {
                    // 重置加载状态
                    setTimeout(() => {
                        this.enhancedState.isLoading = false;
                    }, 500);
                }
            }

            // 显示确认对话框
            async showConfirmation() {
                return new Promise((resolve) => {
                    const confirmed = confirm(this.props.confirmMessage);
                    resolve(confirmed);
                });
            }

            // 鼠标悬停处理
            onMouseEnter() {
                if (this.config.enableTooltip) {
                    this.enhancedState.showTooltip = true;
                }
            }

            // 鼠标离开处理
            onMouseLeave() {
                if (this.config.enableTooltip) {
                    this.enhancedState.showTooltip = false;
                }
            }

            // 获取项目类名
            getItemClass() {
                const classes = [this.props.class || ''];
                
                if (this.props.disabled) {
                    classes.push('disabled');
                }
                
                if (this.props.selected) {
                    classes.push('selected');
                }
                
                if (this.enhancedState.isLoading) {
                    classes.push('loading');
                }
                
                return classes.filter(Boolean).join(' ');
            }

            // 渲染图标
            renderIcon() {
                if (!this.props.icon) {
                    return '';
                }
                
                return xml`<i class="${this.props.icon} me-2"/>`;
            }

            // 渲染徽章
            renderBadge() {
                if (!this.props.badge) {
                    return '';
                }
                
                return xml`
                    <span class="badge ${this.props.badgeClass} ms-auto">
                        ${this.props.badge}
                    </span>
                `;
            }

            // 渲染快捷键
            renderShortcut() {
                if (!this.props.shortcut) {
                    return '';
                }
                
                return xml`
                    <small class="text-muted ms-auto">
                        ${this.props.shortcut}
                    </small>
                `;
            }

            // 渲染加载指示器
            renderLoadingIndicator() {
                if (!this.enhancedState.isLoading) {
                    return '';
                }
                
                return xml`
                    <i class="fa fa-spinner fa-spin ms-auto"/>
                `;
            }

            // 渲染工具提示
            renderTooltip() {
                if (!this.config.enableTooltip || !this.enhancedState.showTooltip) {
                    return '';
                }
                
                return xml`
                    <div class="tooltip bs-tooltip-top show">
                        <div class="tooltip-inner">
                            ${this.props.tooltip}
                        </div>
                    </div>
                `;
            }

            // 组件销毁时清理
            willDestroy() {
                if (this.config.enableShortcut) {
                    document.removeEventListener('keydown', this.handleShortcut.bind(this));
                }
                super.willDestroy && super.willDestroy();
            }
        }

        return AdvancedDropdownItem;
    }
};

// 使用示例
const AdvancedDropdownItem = EnhancedDropdownItem.createAdvancedItem();

// 带图标和徽章的菜单项
<AdvancedDropdownItem
    icon="fa fa-envelope"
    badge={5}
    badgeClass="badge-danger"
    onSelected={() => this.openMessages()}
>
    消息
</AdvancedDropdownItem>

// 带确认的危险操作
<AdvancedDropdownItem
    icon="fa fa-trash"
    class="text-danger"
    confirmAction={true}
    confirmMessage="确定要删除这个项目吗？"
    onSelected={() => this.deleteItem()}
>
    删除
</AdvancedDropdownItem>

// 带快捷键的菜单项
<AdvancedDropdownItem
    icon="fa fa-save"
    shortcut="Ctrl+S"
    tooltip="保存文档"
    onSelected={() => this.saveDocument()}
>
    保存
</AdvancedDropdownItem>

// 禁用状态的菜单项
<AdvancedDropdownItem
    icon="fa fa-lock"
    disabled={true}
    tooltip="您没有权限执行此操作"
    onSelected={() => this.restrictedAction()}
>
    受限操作
</AdvancedDropdownItem>
```

## 技术特点

### 1. 简洁设计
- 最小化的组件实现
- 清晰的职责分离
- 高效的事件处理

### 2. 灵活配置
- 多种关闭模式
- 自定义属性支持
- 样式定制能力

### 3. 钩子集成
- 与下拉菜单系统集成
- 状态管理支持
- 事件传播控制

### 4. 无障碍支持
- 标准的HTML结构
- 键盘导航支持
- 屏幕阅读器兼容

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可重用的菜单项组件
- 标准化的接口

### 2. 策略模式 (Strategy Pattern)
- 不同的关闭模式策略
- 可配置的行为

### 3. 命令模式 (Command Pattern)
- 封装的操作命令
- 回调函数机制

## 注意事项

1. **事件处理**: 正确处理点击事件和默认行为
2. **状态管理**: 保持与父级下拉菜单的状态同步
3. **性能优化**: 避免不必要的重渲染
4. **无障碍性**: 确保键盘导航和屏幕阅读器支持

## 扩展建议

1. **图标支持**: 内置图标显示功能
2. **徽章系统**: 支持徽章和通知显示
3. **快捷键**: 支持键盘快捷键
4. **确认对话框**: 集成确认对话框功能
5. **加载状态**: 支持加载状态显示

该下拉菜单项组件为Odoo Web应用提供了标准化的菜单项实现，通过灵活的配置选项和完善的事件处理确保了良好的用户体验。

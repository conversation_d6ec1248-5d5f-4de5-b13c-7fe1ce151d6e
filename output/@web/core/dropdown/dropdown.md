# Dropdown - 下拉菜单组件

## 概述

`dropdown.js` 是 Odoo Web 核心模块的下拉菜单组件，提供了功能完整的下拉菜单实现。该组件支持嵌套菜单、分组管理、键盘导航、弹出框定位、状态管理和事件处理，为Odoo Web应用提供了标准化的下拉菜单用户界面，广泛应用于导航菜单、操作选择、选项列表和上下文菜单等场景。

## 文件信息
- **路径**: `/web/static/src/core/dropdown/dropdown.js`
- **行数**: 342
- **模块**: `@web/core/dropdown/dropdown`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                          // OWL框架
'@web/core/dropdown/_behaviours/dropdown_group_hook' // 下拉分组钩子
'@web/core/dropdown/_behaviours/dropdown_nesting'    // 下拉嵌套行为
'@web/core/dropdown/_behaviours/dropdown_popover'    // 下拉弹出框
'@web/core/dropdown/dropdown_hooks'                  // 下拉钩子
'@web/core/navigation/navigation'                     // 导航系统
'@web/core/popover/popover_hook'                     // 弹出框钩子
'@web/core/utils/classname'                          // 类名工具
'@web/core/utils/hooks'                              // 工具钩子
'@web/core/utils/objects'                            // 对象工具
'@web/core/utils/reactive'                           // 响应式工具
```

## 核心功能

### 1. 工具函数

```javascript
function getFirstElementOfNode(node) {
    if (!node) {
        return null;
    }
    if (node.el) {
        return node.el.nodeType === Node.ELEMENT_NODE ? node.el : null;
    }
    if (node.bdom || node.child) {
        return getFirstElementOfNode(node.bdom || node.child);
    }
    if (node.children) {
        for (const child of node.children) {
            const el = getFirstElementOfNode(child);
            if (el) {
                return el;
            }
        }
    }
    return null;
}
```

**工具函数功能**:
- **节点遍历**: 递归遍历虚拟DOM节点
- **元素查找**: 查找第一个有效的DOM元素
- **类型检查**: 检查节点类型和有效性
- **兼容性**: 支持不同的节点结构

### 2. 组件属性定义

```javascript
static props = {
    arrow: { optional: true },
    menuClass: { optional: true },
    position: { type: String, optional: true },
    slots: {
        type: Object,
        shape: {
            default: { optional: true },
            content: { optional: true },
        },
    },
    items: {
        optional: true,
        type: Array,
        elements: {
            type: Object,
            shape: {
                label: String,
                onSelected: Function,
                class: { optional: true },
                "*": true,
            },
        },
    },
    menuRef: { type: Function, optional: true },
    disabled: { type: Boolean, optional: true },
    holdOnHover: { type: Boolean, optional: true },
    beforeOpen: { type: Function, optional: true },
    onOpened: { type: Function, optional: true },
    onStateChanged: { type: Function, optional: true },
    state: {
        type: Object,
        shape: {
            isOpen: Boolean,
            close: Function,
            open: Function,
            "*": true,
        },
        optional: true,
    },
    manual: { type: Boolean, optional: true },
    navigationOptions: { type: Object, optional: true },
};
```

**属性功能**:
- **外观控制**: arrow、menuClass、position控制外观和位置
- **内容定义**: slots和items定义菜单内容
- **状态管理**: state、disabled、manual控制组件状态
- **交互行为**: holdOnHover、beforeOpen等控制交互行为
- **导航选项**: navigationOptions自定义导航行为

### 3. 组件初始化

```javascript
setup() {
    this.menuRef = this.props.menuRef || useChildRef();
    this.state = this.props.state || useDropdownState();
    this.nesting = useDropdownNesting(this.state);
    this.group = useDropdownGroup();
    this.navigation = useNavigation(this.menuRef, {
        focusInitialElementOnDisabled: () => !this.group.isInGroup,
        itemsSelector: ":scope .o-navigable, :scope .o-dropdown",
        ...deepMerge(this.nesting.navigationOptions, this.props.navigationOptions),
    });
    
    this.uiService = useService("ui");
    this.popover = usePopover(DropdownPopover, {
        animation: false,
        arrow: this.props.arrow,
        closeOnClickAway: (target) => {
            return this.popoverCloseOnClickAway(target, activeEl);
        },
        closeOnEscape: false,
        env: this.__owl__.childEnv,
        holdOnHover: this.props.holdOnHover,
        onClose: () => this.state.close(),
        onPositioned: (el, { direction }) => this.setTargetDirectionClass(direction),
        popoverClass: mergeClasses(
            "o-dropdown--menu dropdown-menu mx-0",
            { "o-dropdown--menu-submenu": this.hasParent },
            this.props.menuClass
        ),
        popoverRole: "menu",
        position: this.position,
        ref: this.menuRef,
        setActiveElement: false,
    });
}
```

**初始化功能**:
- **引用管理**: 创建菜单引用
- **状态管理**: 初始化下拉状态
- **嵌套支持**: 设置嵌套下拉行为
- **分组管理**: 初始化下拉分组
- **导航系统**: 配置键盘导航
- **弹出框**: 配置弹出框行为

### 4. 计算属性

```javascript
get position() {
    return this.props.position || (this.hasParent ? "right-start" : "bottom-start");
}

get hasParent() {
    return this.nesting.hasParent;
}

get target() {
    const target = getFirstElementOfNode(this.__owl__.bdom);
    if (!target) {
        throw new Error(
            "Could not find a valid dropdown toggler, prefer a single html element and put any dynamic content inside of it."
        );
    }
    return target;
}
```

**计算属性功能**:
- **位置计算**: 根据嵌套状态计算默认位置
- **父级检查**: 检查是否有父级下拉菜单
- **目标元素**: 获取触发下拉的目标元素

### 5. 事件处理

```javascript
handleClick(event) {
    if (this.props.disabled) {
        return;
    }
    event.stopPropagation();
    if (this.state.isOpen && !this.hasParent) {
        this.state.close();
    } else {
        this.state.open();
    }
}

handleMouseEnter() {
    if (this.props.disabled) {
        return;
    }
    if (this.hasParent || this.group.isOpen) {
        this.target.focus();
        this.state.open();
    }
}
```

**事件处理功能**:
- **点击处理**: 处理点击切换下拉状态
- **鼠标悬停**: 处理鼠标悬停打开下拉
- **禁用检查**: 检查组件是否被禁用
- **事件阻止**: 阻止事件冒泡

### 6. 目标元素设置

```javascript
setTargetElement(target) {
    if (!target) {
        return;
    }

    target.ariaExpanded = false;
    target.classList.add("o-dropdown");

    if (this.hasParent) {
        target.classList.add("o-dropdown--has-parent");
    }

    const tagName = target.tagName.toLowerCase();
    if (!["input", "textarea", "table", "thead", "tbody", "tr", "th", "td"].includes(tagName)) {
        target.classList.add("dropdown-toggle");
        if (this.hasParent) {
            target.classList.add("o-dropdown-item", "o-navigable", "dropdown-item");
            if (!target.classList.contains("o-dropdown--no-caret")) {
                target.classList.add("o-dropdown-caret");
            }
        }
    }

    this.defaultDirection = this.position.split("-")[0];
    this.setTargetDirectionClass(this.defaultDirection);

    if (!this.props.manual) {
        target.addEventListener("click", this.handleClick.bind(this));
        target.addEventListener("mouseenter", this.handleMouseEnter.bind(this));
        return () => {
            target.removeEventListener("click", this.handleClick.bind(this));
            target.removeEventListener("mouseenter", this.handleMouseEnter.bind(this));
        };
    }
}
```

**目标设置功能**:
- **无障碍属性**: 设置aria-expanded属性
- **样式类**: 添加相应的CSS类
- **嵌套支持**: 为嵌套下拉添加特殊样式
- **事件监听**: 添加点击和悬停事件监听器
- **清理函数**: 返回事件清理函数

## 使用场景

### 1. 基础下拉菜单

```javascript
// 基础下拉菜单
<Dropdown>
    <button class="btn btn-primary">
        操作 <i class="fa fa-caret-down"/>
    </button>
    <t t-set-slot="content">
        <DropdownItem onSelected={() => this.action1()}>
            操作 1
        </DropdownItem>
        <DropdownItem onSelected={() => this.action2()}>
            操作 2
        </DropdownItem>
    </t>
</Dropdown>
```

### 2. 带箭头的下拉菜单

```javascript
// 带箭头指示的下拉菜单
<Dropdown arrow={true} position="bottom-end">
    <button class="btn btn-secondary">
        选择选项
    </button>
    <t t-set-slot="content">
        <DropdownItem onSelected={() => this.selectOption('option1')}>
            选项 1
        </DropdownItem>
        <DropdownItem onSelected={() => this.selectOption('option2')}>
            选项 2
        </DropdownItem>
    </t>
</Dropdown>
```

### 3. 嵌套下拉菜单

```javascript
// 嵌套下拉菜单
<Dropdown>
    <button class="btn btn-outline-primary">
        主菜单
    </button>
    <t t-set-slot="content">
        <DropdownItem onSelected={() => this.mainAction()}>
            主要操作
        </DropdownItem>
        <Dropdown>
            <DropdownItem>
                子菜单 <i class="fa fa-caret-right"/>
            </DropdownItem>
            <t t-set-slot="content">
                <DropdownItem onSelected={() => this.subAction1()}>
                    子操作 1
                </DropdownItem>
                <DropdownItem onSelected={() => this.subAction2()}>
                    子操作 2
                </DropdownItem>
            </t>
        </Dropdown>
    </t>
</Dropdown>
```

### 4. 自定义状态管理

```javascript
// 使用自定义状态管理
class MyComponent extends Component {
    setup() {
        this.dropdownState = useDropdownState();
    }

    render() {
        return xml`
            <Dropdown state="dropdownState" manual={true}>
                <button class="btn btn-info" t-on-click="() => this.dropdownState.toggle()">
                    自定义控制
                </button>
                <t t-set-slot="content">
                    <DropdownItem onSelected={() => this.customAction()}>
                        自定义操作
                    </DropdownItem>
                </t>
            </Dropdown>
        `;
    }
}
```

## 增强示例

```javascript
// 增强的下拉菜单组件
const EnhancedDropdown = {
    createAdvancedDropdown: () => {
        class AdvancedDropdown extends Dropdown {
            static props = {
                ...Dropdown.props,
                searchable: { type: Boolean, optional: true },
                multiSelect: { type: Boolean, optional: true },
                maxHeight: { type: String, optional: true },
                loading: { type: Boolean, optional: true },
                emptyMessage: { type: String, optional: true },
                onSearch: { type: Function, optional: true },
                selectedItems: { type: Array, optional: true }
            };

            static defaultProps = {
                ...Dropdown.defaultProps,
                searchable: false,
                multiSelect: false,
                maxHeight: '300px',
                loading: false,
                emptyMessage: 'No items found',
                selectedItems: []
            };

            setup() {
                super.setup();
                
                // 增强状态
                this.enhancedState = useState({
                    searchTerm: '',
                    filteredItems: [],
                    selectedItems: [...(this.props.selectedItems || [])],
                    isLoading: this.props.loading
                });

                // 配置选项
                this.config = {
                    enableSearch: this.props.searchable,
                    enableMultiSelect: this.props.multiSelect,
                    enableVirtualScroll: false
                };

                // 初始化过滤项目
                this.updateFilteredItems();
            }

            // 更新过滤项目
            updateFilteredItems() {
                if (!this.props.items) {
                    this.enhancedState.filteredItems = [];
                    return;
                }

                const searchTerm = this.enhancedState.searchTerm.toLowerCase();
                this.enhancedState.filteredItems = this.props.items.filter(item => 
                    !searchTerm || item.label.toLowerCase().includes(searchTerm)
                );
            }

            // 搜索处理
            onSearchInput(event) {
                this.enhancedState.searchTerm = event.target.value;
                this.updateFilteredItems();
                
                if (this.props.onSearch) {
                    this.props.onSearch(event.target.value);
                }
            }

            // 项目选择处理
            onItemSelected(item) {
                if (this.config.enableMultiSelect) {
                    const selectedItems = [...this.enhancedState.selectedItems];
                    const existingIndex = selectedItems.findIndex(selected => 
                        selected.id === item.id
                    );
                    
                    if (existingIndex >= 0) {
                        selectedItems.splice(existingIndex, 1);
                    } else {
                        selectedItems.push(item);
                    }
                    
                    this.enhancedState.selectedItems = selectedItems;
                } else {
                    this.enhancedState.selectedItems = [item];
                    this.state.close();
                }
                
                if (item.onSelected) {
                    item.onSelected(item);
                }
            }

            // 检查项目是否被选中
            isItemSelected(item) {
                return this.enhancedState.selectedItems.some(selected => 
                    selected.id === item.id
                );
            }

            // 清除所有选择
            clearSelection() {
                this.enhancedState.selectedItems = [];
            }

            // 全选
            selectAll() {
                if (this.config.enableMultiSelect) {
                    this.enhancedState.selectedItems = [...this.enhancedState.filteredItems];
                }
            }

            // 获取选中项目
            getSelectedItems() {
                return [...this.enhancedState.selectedItems];
            }

            // 设置加载状态
            setLoading(loading) {
                this.enhancedState.isLoading = loading;
            }

            // 增强的弹出框打开
            openPopover() {
                // 重置搜索
                if (this.config.enableSearch) {
                    this.enhancedState.searchTerm = '';
                    this.updateFilteredItems();
                }
                
                super.openPopover();
            }

            // 渲染搜索框
            renderSearchBox() {
                if (!this.config.enableSearch) {
                    return '';
                }
                
                return xml`
                    <div class="dropdown-search p-2 border-bottom">
                        <input 
                            type="text" 
                            class="form-control form-control-sm" 
                            placeholder="Search..."
                            t-model="enhancedState.searchTerm"
                            t-on-input="onSearchInput"
                        />
                    </div>
                `;
            }

            // 渲染项目列表
            renderItemList() {
                const items = this.enhancedState.filteredItems;
                
                if (this.enhancedState.isLoading) {
                    return xml`
                        <div class="dropdown-loading p-3 text-center">
                            <i class="fa fa-spinner fa-spin"></i> Loading...
                        </div>
                    `;
                }
                
                if (items.length === 0) {
                    return xml`
                        <div class="dropdown-empty p-3 text-center text-muted">
                            ${this.props.emptyMessage}
                        </div>
                    `;
                }
                
                return xml`
                    <div class="dropdown-items" style="max-height: ${this.props.maxHeight}; overflow-y: auto;">
                        <t t-foreach="items" t-as="item" t-key="item.id">
                            <DropdownItem 
                                class="${this.isItemSelected(item) ? 'selected' : ''}"
                                onSelected="() => this.onItemSelected(item)"
                            >
                                <t t-if="config.enableMultiSelect">
                                    <input 
                                        type="checkbox" 
                                        class="form-check-input me-2"
                                        t-att-checked="isItemSelected(item)"
                                    />
                                </t>
                                <span t-esc="item.label"/>
                                <t t-if="isItemSelected(item) and !config.enableMultiSelect">
                                    <i class="fa fa-check ms-auto"/>
                                </t>
                            </DropdownItem>
                        </t>
                    </div>
                `;
            }

            // 渲染多选控制
            renderMultiSelectControls() {
                if (!this.config.enableMultiSelect) {
                    return '';
                }
                
                return xml`
                    <div class="dropdown-controls p-2 border-top">
                        <button 
                            class="btn btn-sm btn-outline-primary me-2"
                            t-on-click="selectAll"
                        >
                            Select All
                        </button>
                        <button 
                            class="btn btn-sm btn-outline-secondary"
                            t-on-click="clearSelection"
                        >
                            Clear
                        </button>
                        <span class="ms-auto text-muted">
                            ${this.enhancedState.selectedItems.length} selected
                        </span>
                    </div>
                `;
            }
        }

        return AdvancedDropdown;
    }
};

// 使用示例
const AdvancedDropdown = EnhancedDropdown.createAdvancedDropdown();

// 可搜索的下拉菜单
<AdvancedDropdown
    searchable={true}
    items={this.searchableItems}
    onSearch={(term) => this.performSearch(term)}
    emptyMessage="No results found"
>
    <button class="btn btn-primary">
        Search Items
    </button>
</AdvancedDropdown>

// 多选下拉菜单
<AdvancedDropdown
    multiSelect={true}
    items={this.multiSelectItems}
    selectedItems={this.selectedItems}
    maxHeight="400px"
>
    <button class="btn btn-secondary">
        Select Multiple ({this.selectedItems.length})
    </button>
</AdvancedDropdown>
```

## 技术特点

### 1. 嵌套支持
- 支持多级嵌套下拉菜单
- 智能的位置计算
- 父子关系管理

### 2. 键盘导航
- 完整的键盘导航支持
- 可配置的导航选项
- 无障碍访问支持

### 3. 弹出框集成
- 与弹出框系统集成
- 智能的定位算法
- 响应式位置调整

### 4. 状态管理
- 灵活的状态管理
- 手动和自动模式
- 状态同步机制

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可重用的下拉菜单组件
- 插槽系统支持

### 2. 状态模式 (State Pattern)
- 开启和关闭状态管理
- 状态转换控制

### 3. 观察者模式 (Observer Pattern)
- 状态变化通知
- 事件驱动的交互

## 注意事项

1. **性能优化**: 避免频繁的DOM操作
2. **内存管理**: 及时清理事件监听器
3. **无障碍性**: 确保键盘导航和屏幕阅读器支持
4. **响应式**: 在不同屏幕尺寸下的适配

## 扩展建议

1. **虚拟滚动**: 支持大量项目的虚拟滚动
2. **异步加载**: 支持异步加载菜单项目
3. **主题定制**: 支持多种视觉主题
4. **动画效果**: 添加平滑的动画过渡
5. **触摸支持**: 优化移动设备的触摸体验

该下拉菜单组件为Odoo Web应用提供了功能完整的下拉菜单解决方案，通过嵌套支持、键盘导航和弹出框集成确保了优秀的用户体验。

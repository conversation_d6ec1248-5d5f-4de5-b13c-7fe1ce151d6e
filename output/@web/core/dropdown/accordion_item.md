# AccordionItem - 手风琴项组件

## 概述

`accordion_item.js` 是 Odoo Web 核心模块的手风琴项组件，提供了手风琴式展开收缩的菜单项实现。该组件支持状态管理、父组件通信、选中状态和自定义样式，为下拉菜单系统提供了可展开的层级内容显示能力，适用于需要分层展示信息或选项的场景，提供了良好的空间利用和用户体验。

## 文件信息
- **路径**: `/web/static/src/core/dropdown/accordion_item.js`
- **行数**: 44
- **模块**: `@web/core/dropdown/accordion_item`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'    // OWL框架
```

## 核心功能

### 1. 手风琴标识符

```javascript
const ACCORDION = Symbol("Accordion");
```

**标识符功能**:
- **唯一标识**: 使用Symbol确保手风琴标识的唯一性
- **环境键**: 作为环境对象中的键使用
- **父子通信**: 用于子组件与父手风琴组件的通信
- **命名空间**: 避免与其他模块的命名冲突

### 2. 组件属性定义

```javascript
static props = {
    slots: {
        type: Object,
        shape: {
            default: {},
        },
    },
    description: String,
    selected: {
        type: Boolean,
        optional: true,
    },
    class: {
        type: String,
        optional: true,
    },
};
```

**属性功能**:
- **插槽支持**: slots定义默认插槽内容
- **描述文本**: description提供项目的描述信息
- **选中状态**: selected控制项目的选中状态
- **自定义样式**: class支持自定义CSS类
- **可选配置**: 除description外都是可选属性

### 3. 默认属性

```javascript
static defaultProps = {
    class: "",
    selected: false,
};
```

**默认属性功能**:
- **空样式**: 默认无自定义CSS类
- **未选中**: 默认为未选中状态
- **安全默认**: 提供合理的默认值

### 4. 组件初始化

```javascript
setup() {
    this.state = useState({
        open: false,
    });
    this.parentComponent = this.env[ACCORDION];
    onPatched(() => {
        this.parentComponent?.accordionStateChanged?.();
    });
}
```

**初始化功能**:
- **状态管理**: 创建响应式的开启状态
- **父组件引用**: 获取父手风琴组件的引用
- **状态通知**: 组件更新后通知父组件状态变化
- **可选调用**: 使用可选链确保安全调用

## 使用场景

### 1. 基础手风琴项

```javascript
// 基础手风琴项使用
<AccordionItem description="基础选项">
    <div class="p-3">
        <p>这是手风琴项的内容</p>
        <button class="btn btn-sm btn-primary">操作按钮</button>
    </div>
</AccordionItem>
```

### 2. 带选中状态的手风琴项

```javascript
// 带选中状态的手风琴项
<AccordionItem 
    description="可选择的选项"
    selected={this.state.selectedItem === 'option1'}
    class="border-primary"
>
    <div class="p-3">
        <h6>选项 1</h6>
        <p>这是一个可选择的手风琴项</p>
        <div class="form-check">
            <input 
                class="form-check-input" 
                type="radio" 
                name="options" 
                id="option1"
                t-on-change="() => this.selectOption('option1')"
            />
            <label class="form-check-label" for="option1">
                选择此选项
            </label>
        </div>
    </div>
</AccordionItem>
```

### 3. 嵌套内容的手风琴项

```javascript
// 包含复杂嵌套内容的手风琴项
<AccordionItem 
    description="高级设置"
    class="advanced-settings"
>
    <div class="p-3">
        <div class="row">
            <div class="col-md-6">
                <h6>显示选项</h6>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="showGrid"/>
                    <label class="form-check-label" for="showGrid">显示网格</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="showRuler"/>
                    <label class="form-check-label" for="showRuler">显示标尺</label>
                </div>
            </div>
            <div class="col-md-6">
                <h6>性能选项</h6>
                <div class="mb-2">
                    <label class="form-label">缓存大小</label>
                    <select class="form-select form-select-sm">
                        <option>小 (10MB)</option>
                        <option>中 (50MB)</option>
                        <option>大 (100MB)</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</AccordionItem>
```

### 4. 动态手风琴项列表

```javascript
// 动态生成的手风琴项列表
class DynamicAccordion extends Component {
    setup() {
        this.state = useState({
            items: [
                {
                    id: 'general',
                    description: '常规设置',
                    selected: true,
                    content: this.renderGeneralSettings()
                },
                {
                    id: 'advanced',
                    description: '高级设置',
                    selected: false,
                    content: this.renderAdvancedSettings()
                },
                {
                    id: 'security',
                    description: '安全设置',
                    selected: false,
                    content: this.renderSecuritySettings()
                }
            ],
            selectedItem: 'general'
        });
    }

    selectItem(itemId) {
        this.state.selectedItem = itemId;
        this.state.items.forEach(item => {
            item.selected = item.id === itemId;
        });
    }

    renderGeneralSettings() {
        return xml`
            <div class="p-3">
                <h6>常规设置</h6>
                <div class="mb-2">
                    <label class="form-label">用户名</label>
                    <input type="text" class="form-control"/>
                </div>
                <div class="mb-2">
                    <label class="form-label">邮箱</label>
                    <input type="email" class="form-control"/>
                </div>
            </div>
        `;
    }

    renderAdvancedSettings() {
        return xml`
            <div class="p-3">
                <h6>高级设置</h6>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox"/>
                    <label class="form-check-label">启用调试模式</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox"/>
                    <label class="form-check-label">自动保存</label>
                </div>
            </div>
        `;
    }

    renderSecuritySettings() {
        return xml`
            <div class="p-3">
                <h6>安全设置</h6>
                <div class="mb-2">
                    <label class="form-label">密码强度</label>
                    <select class="form-select">
                        <option>低</option>
                        <option>中</option>
                        <option>高</option>
                    </select>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox"/>
                    <label class="form-check-label">启用两步验证</label>
                </div>
            </div>
        `;
    }

    render() {
        return xml`
            <div class="dynamic-accordion">
                <t t-foreach="state.items" t-as="item" t-key="item.id">
                    <AccordionItem 
                        description="item.description"
                        selected="item.selected"
                        class="${item.selected ? 'selected' : ''}"
                        t-on-click="() => this.selectItem(item.id)"
                    >
                        <t t-out="item.content"/>
                    </AccordionItem>
                </t>
            </div>
        `;
    }
}
```

## 增强示例

```javascript
// 增强的手风琴项组件
const EnhancedAccordionItem = {
    createAdvancedItem: () => {
        class AdvancedAccordionItem extends AccordionItem {
            static props = {
                ...AccordionItem.props,
                
                // 增强属性
                icon: { type: String, optional: true },
                badge: { type: [String, Number], optional: true },
                badgeClass: { type: String, optional: true },
                disabled: { type: Boolean, optional: true },
                collapsible: { type: Boolean, optional: true },
                defaultOpen: { type: Boolean, optional: true },
                animationDuration: { type: Number, optional: true },
                onToggle: { type: Function, optional: true },
                onSelect: { type: Function, optional: true },
                headerClass: { type: String, optional: true },
                contentClass: { type: String, optional: true }
            };

            static defaultProps = {
                ...AccordionItem.defaultProps,
                disabled: false,
                collapsible: true,
                defaultOpen: false,
                animationDuration: 300,
                badgeClass: 'badge-primary'
            };

            setup() {
                super.setup();
                
                // 增强状态
                this.enhancedState = useState({
                    isAnimating: false,
                    contentHeight: 0,
                    isHovered: false
                });

                // 设置默认开启状态
                if (this.props.defaultOpen) {
                    this.state.open = true;
                }

                // 内容引用
                this.contentRef = useRef('content');

                // 监听状态变化
                useEffect(() => {
                    if (this.state.open) {
                        this.expandContent();
                    } else {
                        this.collapseContent();
                    }
                }, () => [this.state.open]);
            }

            // 切换展开状态
            toggle() {
                if (this.props.disabled || !this.props.collapsible) {
                    return;
                }

                this.state.open = !this.state.open;
                
                if (this.props.onToggle) {
                    this.props.onToggle(this.state.open);
                }
            }

            // 选择项目
            select() {
                if (this.props.disabled) {
                    return;
                }

                if (this.props.onSelect) {
                    this.props.onSelect();
                }
            }

            // 展开内容
            async expandContent() {
                if (!this.contentRef.el) return;

                this.enhancedState.isAnimating = true;
                
                // 计算内容高度
                const content = this.contentRef.el;
                content.style.height = 'auto';
                const height = content.scrollHeight;
                content.style.height = '0px';
                
                // 强制重绘
                content.offsetHeight;
                
                // 开始动画
                content.style.transition = `height ${this.props.animationDuration}ms ease`;
                content.style.height = `${height}px`;
                
                // 动画结束后清理
                setTimeout(() => {
                    content.style.height = 'auto';
                    content.style.transition = '';
                    this.enhancedState.isAnimating = false;
                }, this.props.animationDuration);
            }

            // 收缩内容
            async collapseContent() {
                if (!this.contentRef.el) return;

                this.enhancedState.isAnimating = true;
                
                const content = this.contentRef.el;
                const height = content.scrollHeight;
                
                content.style.height = `${height}px`;
                content.offsetHeight; // 强制重绘
                
                content.style.transition = `height ${this.props.animationDuration}ms ease`;
                content.style.height = '0px';
                
                setTimeout(() => {
                    content.style.transition = '';
                    this.enhancedState.isAnimating = false;
                }, this.props.animationDuration);
            }

            // 鼠标悬停处理
            onMouseEnter() {
                this.enhancedState.isHovered = true;
            }

            onMouseLeave() {
                this.enhancedState.isHovered = false;
            }

            // 获取头部类名
            getHeaderClass() {
                const classes = ['accordion-header', this.props.headerClass || ''];
                
                if (this.state.open) {
                    classes.push('open');
                }
                
                if (this.props.selected) {
                    classes.push('selected');
                }
                
                if (this.props.disabled) {
                    classes.push('disabled');
                }
                
                if (this.enhancedState.isHovered) {
                    classes.push('hovered');
                }
                
                return classes.filter(Boolean).join(' ');
            }

            // 获取内容类名
            getContentClass() {
                const classes = ['accordion-content', this.props.contentClass || ''];
                
                if (this.enhancedState.isAnimating) {
                    classes.push('animating');
                }
                
                return classes.filter(Boolean).join(' ');
            }

            // 渲染图标
            renderIcon() {
                if (!this.props.icon) {
                    return '';
                }
                
                return xml`<i class="${this.props.icon} me-2"/>`;
            }

            // 渲染徽章
            renderBadge() {
                if (!this.props.badge) {
                    return '';
                }
                
                return xml`
                    <span class="badge ${this.props.badgeClass} ms-auto">
                        ${this.props.badge}
                    </span>
                `;
            }

            // 渲染展开指示器
            renderExpandIndicator() {
                if (!this.props.collapsible) {
                    return '';
                }
                
                const iconClass = this.state.open ? 'fa-chevron-up' : 'fa-chevron-down';
                
                return xml`
                    <i class="fa ${iconClass} ms-auto accordion-indicator"/>
                `;
            }

            // 增强的模板
            static template = xml`
                <div class="advanced-accordion-item ${props.class}">
                    <div 
                        class="${this.getHeaderClass()}"
                        t-on-click="toggle"
                        t-on-mouseenter="onMouseEnter"
                        t-on-mouseleave="onMouseLeave"
                    >
                        <div class="d-flex align-items-center">
                            <t t-call="renderIcon"/>
                            <span class="accordion-description">
                                <t t-esc="props.description"/>
                            </span>
                            <t t-call="renderBadge"/>
                            <t t-call="renderExpandIndicator"/>
                        </div>
                    </div>
                    
                    <div 
                        class="${this.getContentClass()}"
                        t-ref="content"
                        style="overflow: hidden; height: ${state.open ? 'auto' : '0px'};"
                    >
                        <t t-slot="default"/>
                    </div>
                </div>
            `;
        }

        return AdvancedAccordionItem;
    }
};

// 使用示例
const AdvancedAccordionItem = EnhancedAccordionItem.createAdvancedItem();

// 带图标和徽章的手风琴项
<AdvancedAccordionItem
    description="通知设置"
    icon="fa fa-bell"
    badge={3}
    badgeClass="badge-danger"
    defaultOpen={true}
    onToggle={(isOpen) => console.log('Toggled:', isOpen)}
    onSelect={() => console.log('Selected')}
>
    <div class="p-3">
        <div class="form-check">
            <input class="form-check-input" type="checkbox"/>
            <label class="form-check-label">邮件通知</label>
        </div>
        <div class="form-check">
            <input class="form-check-input" type="checkbox"/>
            <label class="form-check-label">短信通知</label>
        </div>
    </div>
</AdvancedAccordionItem>

// 禁用的手风琴项
<AdvancedAccordionItem
    description="受限功能"
    icon="fa fa-lock"
    disabled={true}
    collapsible={false}
>
    <div class="p-3 text-muted">
        此功能需要高级权限
    </div>
</AdvancedAccordionItem>
```

## 技术特点

### 1. 状态管理
- 响应式的开启状态
- 父子组件通信
- 状态变化通知

### 2. 灵活配置
- 可选的选中状态
- 自定义样式支持
- 插槽内容支持

### 3. 环境集成
- 与父手风琴组件集成
- 环境变量通信
- 生命周期管理

### 4. 简洁设计
- 最小化的组件实现
- 清晰的属性定义
- 高效的渲染逻辑

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可重用的手风琴项组件
- 标准化的接口

### 2. 状态模式 (State Pattern)
- 开启和关闭状态管理
- 状态转换控制

### 3. 观察者模式 (Observer Pattern)
- 状态变化通知
- 父子组件通信

## 注意事项

1. **状态同步**: 确保与父组件的状态同步
2. **性能优化**: 避免不必要的重渲染
3. **无障碍性**: 确保键盘导航和屏幕阅读器支持
4. **动画性能**: 优化展开收缩动画的性能

## 扩展建议

1. **动画效果**: 添加平滑的展开收缩动画
2. **键盘导航**: 支持键盘操作
3. **拖拽排序**: 支持手风琴项的拖拽排序
4. **持久化**: 支持展开状态的持久化
5. **主题定制**: 支持多种视觉主题

该手风琴项组件为Odoo Web应用提供了灵活的可展开内容显示能力，通过状态管理和父子通信确保了良好的用户交互体验。

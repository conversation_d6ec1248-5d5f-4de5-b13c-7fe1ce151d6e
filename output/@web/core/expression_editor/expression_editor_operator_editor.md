# ExpressionEditorOperatorEditor - 表达式编辑器操作符编辑器

## 概述

`expression_editor_operator_editor.js` 是 Odoo Web 核心模块的表达式编辑器操作符编辑器，提供了表达式编辑器专用的操作符过滤功能。该模块基于域选择器操作符编辑器，通过白名单机制过滤出表达式编辑器支持的操作符，确保表达式的有效性和安全性，为表达式编辑器提供了准确的操作符选择，广泛应用于Python表达式构建、条件逻辑编辑等场景。

## 文件信息
- **路径**: `/web/static/src/core/expression_editor/expression_editor_operator_editor.js`
- **行数**: 31
- **模块**: `@web/core/expression_editor/expression_editor_operator_editor`

## 依赖关系

```javascript
// 核心依赖
'@web/core/domain_selector/domain_selector_operator_editor' // 域选择器操作符编辑器
```

## 核心常量

### 1. 表达式有效操作符

```javascript
const EXPRESSION_VALID_OPERATORS = [
    "<",           // 小于
    "<=",          // 小于等于
    ">",           // 大于
    ">=",          // 大于等于
    "between",     // 在范围内
    "within",      // 在时间范围内
    "in",          // 在列表中
    "not in",      // 不在列表中
    "=",           // 等于
    "!=",          // 不等于
    "set",         // 已设置
    "not_set",     // 未设置
    "starts_with", // 开始于
    "ends_with",   // 结束于
    "is",          // 是（布尔值）
    "is_not",      // 不是（布尔值）
];
```

**操作符分类**:
- **比较操作符**: <, <=, >, >=, =, !=
- **范围操作符**: between, within
- **列表操作符**: in, not in
- **状态操作符**: set, not_set, is, is_not
- **文本操作符**: starts_with, ends_with

## 核心函数

### 1. getExpressionDisplayedOperators

```javascript
function getExpressionDisplayedOperators(fieldDef) {
    const operators = getDomainDisplayedOperators(fieldDef);
    return operators.filter((operator) => EXPRESSION_VALID_OPERATORS.includes(operator));
}
```

**函数功能**:
- **操作符获取**: 从域选择器获取字段的所有可用操作符
- **白名单过滤**: 使用白名单过滤出表达式编辑器支持的操作符
- **安全保证**: 确保只返回表达式编辑器安全支持的操作符
- **类型适配**: 根据字段类型返回适合的操作符列表

**参数说明**:
- **fieldDef**: 字段定义对象，包含字段的类型和属性信息
- **返回值**: 过滤后的操作符数组

## 操作符支持矩阵

### 1. 数值字段操作符

```javascript
// 数值字段（integer, float, monetary）支持的操作符
const numericOperators = [
    "=", "!=",           // 精确匹配
    "<", "<=", ">", ">=", // 比较操作
    "between",           // 范围操作
    "set", "not_set",    // 状态检查
];
```

### 2. 文本字段操作符

```javascript
// 文本字段（char, text, html）支持的操作符
const textOperators = [
    "=", "!=",           // 精确匹配
    "in", "not in",      // 列表匹配
    "starts_with",       // 前缀匹配
    "ends_with",         // 后缀匹配
    "set", "not_set",    // 状态检查
];
```

### 3. 日期时间字段操作符

```javascript
// 日期时间字段（date, datetime）支持的操作符
const dateOperators = [
    "=", "!=",           // 精确匹配
    "<", "<=", ">", ">=", // 比较操作
    "between",           // 日期范围
    "within",            // 相对时间范围
    "set", "not_set",    // 状态检查
];
```

### 4. 布尔字段操作符

```javascript
// 布尔字段（boolean）支持的操作符
const booleanOperators = [
    "is",                // 为真
    "is_not",            // 为假
];
```

### 5. 关联字段操作符

```javascript
// 关联字段（many2one, many2many, one2many）支持的操作符
const relationOperators = [
    "=", "!=",           // 精确匹配
    "in", "not in",      // 列表匹配
    "set", "not_set",    // 关联状态
];
```

## 使用场景

### 1. 基础操作符过滤

```javascript
// 基础操作符过滤示例
class OperatorFilterDemo extends Component {
    setup() {
        this.state = useState({
            fieldTypes: [
                { type: 'char', name: '字符字段' },
                { type: 'integer', name: '整数字段' },
                { type: 'boolean', name: '布尔字段' },
                { type: 'date', name: '日期字段' },
                { type: 'many2one', name: '多对一字段' }
            ],
            selectedType: 'char',
            availableOperators: [],
            allOperators: []
        });

        this.updateOperators();
    }

    updateOperators() {
        const fieldDef = { type: this.state.selectedType };
        
        // 获取域选择器的所有操作符
        this.state.allOperators = getDomainDisplayedOperators(fieldDef);
        
        // 获取表达式编辑器过滤后的操作符
        this.state.availableOperators = getExpressionDisplayedOperators(fieldDef);
    }

    onFieldTypeChange(newType) {
        this.state.selectedType = newType;
        this.updateOperators();
    }

    getOperatorDescription(operator) {
        const descriptions = {
            '=': '等于',
            '!=': '不等于',
            '<': '小于',
            '<=': '小于等于',
            '>': '大于',
            '>=': '大于等于',
            'between': '在范围内',
            'within': '在时间范围内',
            'in': '在列表中',
            'not in': '不在列表中',
            'set': '已设置',
            'not_set': '未设置',
            'starts_with': '开始于',
            'ends_with': '结束于',
            'is': '是',
            'is_not': '不是'
        };
        return descriptions[operator] || operator;
    }

    render() {
        const filteredCount = this.state.allOperators.length - this.state.availableOperators.length;

        return xml`
            <div class="operator-filter-demo">
                <h5>操作符过滤演示</h5>
                
                <div class="field-type-selector mb-3">
                    <label class="form-label">选择字段类型:</label>
                    <select 
                        class="form-select"
                        t-model="state.selectedType"
                        t-on-change="(ev) => this.onFieldTypeChange(ev.target.value)"
                    >
                        <t t-foreach="state.fieldTypes" t-as="fieldType" t-key="fieldType.type">
                            <option t-att-value="fieldType.type" t-esc="fieldType.name"/>
                        </t>
                    </select>
                </div>

                <div class="operators-comparison">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>域选择器操作符 (${this.state.allOperators.length})</h6>
                                </div>
                                <div class="card-body">
                                    <div class="operators-list">
                                        <t t-foreach="state.allOperators" t-as="operator" t-key="operator">
                                            <span 
                                                class="badge me-2 mb-2"
                                                t-att-class="state.availableOperators.includes(operator) ? 'bg-success' : 'bg-secondary'"
                                                t-att-title="getOperatorDescription(operator)"
                                            >
                                                <t t-esc="operator"/>
                                            </span>
                                        </t>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>表达式编辑器操作符 (${this.state.availableOperators.length})</h6>
                                </div>
                                <div class="card-body">
                                    <div class="operators-list">
                                        <t t-foreach="state.availableOperators" t-as="operator" t-key="operator">
                                            <span 
                                                class="badge bg-primary me-2 mb-2"
                                                t-att-title="getOperatorDescription(operator)"
                                            >
                                                <t t-esc="operator"/>
                                            </span>
                                        </t>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="filter-summary mt-3">
                    <div class="alert alert-info">
                        <strong>过滤结果:</strong> 
                        从 ${this.state.allOperators.length} 个操作符中过滤出 ${this.state.availableOperators.length} 个，
                        过滤掉了 ${filteredCount} 个不支持的操作符。
                    </div>
                </div>
            </div>
        `;
    }
}
```

### 2. 操作符兼容性检查器

```javascript
// 操作符兼容性检查器
class OperatorCompatibilityChecker extends Component {
    setup() {
        this.state = useState({
            testCases: [
                { fieldType: 'char', operator: 'ilike', expected: false },
                { fieldType: 'char', operator: 'starts_with', expected: true },
                { fieldType: 'integer', operator: 'between', expected: true },
                { fieldType: 'boolean', operator: 'is', expected: true },
                { fieldType: 'boolean', operator: '=', expected: false },
                { fieldType: 'date', operator: 'within', expected: true },
                { fieldType: 'many2one', operator: 'child_of', expected: false },
                { fieldType: 'selection', operator: 'in', expected: true }
            ],
            customTest: {
                fieldType: 'char',
                operator: '='
            },
            testResults: []
        });

        this.runAllTests();
    }

    runAllTests() {
        this.state.testResults = this.state.testCases.map(testCase => {
            const fieldDef = { type: testCase.fieldType };
            const availableOperators = getExpressionDisplayedOperators(fieldDef);
            const isSupported = availableOperators.includes(testCase.operator);
            
            return {
                ...testCase,
                actual: isSupported,
                passed: isSupported === testCase.expected
            };
        });
    }

    runCustomTest() {
        const fieldDef = { type: this.state.customTest.fieldType };
        const availableOperators = getExpressionDisplayedOperators(fieldDef);
        const isSupported = availableOperators.includes(this.state.customTest.operator);
        
        const result = {
            fieldType: this.state.customTest.fieldType,
            operator: this.state.customTest.operator,
            isSupported: isSupported,
            availableOperators: availableOperators
        };

        console.log('Custom test result:', result);
        this.notification.add(
            `操作符 "${result.operator}" 在字段类型 "${result.fieldType}" 中${result.isSupported ? '支持' : '不支持'}`,
            { type: result.isSupported ? 'success' : 'warning' }
        );
    }

    getStatusIcon(passed) {
        return passed ? 'fa-check text-success' : 'fa-times text-danger';
    }

    render() {
        const passedTests = this.state.testResults.filter(r => r.passed).length;
        const totalTests = this.state.testResults.length;

        return xml`
            <div class="operator-compatibility-checker">
                <h5>操作符兼容性检查器</h5>
                
                <div class="test-summary mb-3">
                    <div class="alert alert-info">
                        <strong>测试结果:</strong> ${passedTests}/${totalTests} 个测试通过
                        <div class="progress mt-2">
                            <div 
                                class="progress-bar bg-success" 
                                role="progressbar" 
                                t-att-style="'width: ' + (passedTests / totalTests * 100) + '%'"
                            >
                                ${Math.round(passedTests / totalTests * 100)}%
                            </div>
                        </div>
                    </div>
                </div>

                <div class="test-results">
                    <h6>自动化测试结果</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>字段类型</th>
                                    <th>操作符</th>
                                    <th>预期</th>
                                    <th>实际</th>
                                    <th>结果</th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-foreach="state.testResults" t-as="result" t-key="result_index">
                                    <tr t-att-class="result.passed ? 'table-success' : 'table-danger'">
                                        <td t-esc="result.fieldType"/>
                                        <td><code t-esc="result.operator"/></td>
                                        <td>
                                            <i t-att-class="'fa ' + (result.expected ? 'fa-check text-success' : 'fa-times text-danger')"/>
                                            <t t-esc="result.expected ? '支持' : '不支持'"/>
                                        </td>
                                        <td>
                                            <i t-att-class="'fa ' + (result.actual ? 'fa-check text-success' : 'fa-times text-danger')"/>
                                            <t t-esc="result.actual ? '支持' : '不支持'"/>
                                        </td>
                                        <td>
                                            <i t-att-class="'fa ' + getStatusIcon(result.passed)"/>
                                            <t t-esc="result.passed ? '通过' : '失败'"/>
                                        </td>
                                    </tr>
                                </t>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="custom-test mt-4">
                    <h6>自定义测试</h6>
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <label class="form-label">字段类型</label>
                                    <select class="form-select" t-model="state.customTest.fieldType">
                                        <option value="char">字符</option>
                                        <option value="integer">整数</option>
                                        <option value="boolean">布尔</option>
                                        <option value="date">日期</option>
                                        <option value="many2one">多对一</option>
                                        <option value="selection">选择</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">操作符</label>
                                    <input 
                                        type="text" 
                                        class="form-control"
                                        t-model="state.customTest.operator"
                                        placeholder="输入操作符"
                                    />
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">操作</label>
                                    <button 
                                        class="btn btn-primary w-100"
                                        t-on-click="runCustomTest"
                                    >
                                        测试兼容性
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 白名单过滤
- 基于预定义的操作符白名单
- 确保操作符的安全性和有效性
- 防止不支持的操作符进入表达式

### 2. 继承复用
- 复用域选择器的操作符逻辑
- 避免重复的字段类型判断
- 保持操作符逻辑的一致性

### 3. 简洁设计
- 单一职责的过滤功能
- 最小化的代码实现
- 清晰的函数接口

### 4. 类型安全
- 严格的操作符验证
- 类型感知的过滤机制
- 运行时的安全保证

## 设计模式

### 1. 过滤器模式 (Filter Pattern)
- 操作符的过滤和筛选
- 基于条件的数据过滤

### 2. 装饰器模式 (Decorator Pattern)
- 在域选择器基础上添加过滤功能
- 增强而不修改原有逻辑

### 3. 策略模式 (Strategy Pattern)
- 不同的操作符过滤策略
- 可扩展的过滤规则

## 注意事项

1. **操作符同步**: 保持与域选择器操作符的同步
2. **白名单维护**: 及时更新操作符白名单
3. **向后兼容**: 确保新增操作符的向后兼容性
4. **安全考虑**: 验证操作符的安全性

## 扩展建议

1. **动态配置**: 支持动态配置操作符白名单
2. **分类过滤**: 按操作符类别进行过滤
3. **权限控制**: 基于用户权限的操作符过滤
4. **版本管理**: 操作符白名单的版本管理
5. **文档生成**: 自动生成操作符支持文档

该表达式编辑器操作符编辑器为Odoo Web应用提供了安全可靠的操作符过滤功能，通过白名单机制确保了表达式编辑器的操作符安全性和有效性。

# ExpressionEditor - 表达式编辑器

## 概述

`expression_editor.js` 是 Odoo Web 核心模块的表达式编辑器组件，提供了Python表达式的可视化编辑功能。该组件基于树编辑器构建，支持字段选择、操作符编辑、值输入等功能，具备表达式解析、树结构转换、字段过滤等特性，为用户提供了直观的表达式构建体验，广泛应用于过滤器、计算字段、条件逻辑等需要表达式编辑的场景。

## 文件信息
- **路径**: `/web/static/src/core/expression_editor/expression_editor.js`
- **行数**: 119
- **模块**: `@web/core/expression_editor/expression_editor`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                      // OWL框架
'@web/core/expression_editor/expression_editor_operator_editor' // 表达式操作符编辑器
'@web/core/tree_editor/condition_tree'          // 条件树
'@web/core/tree_editor/tree_editor'              // 树编辑器
'@web/core/tree_editor/tree_editor_operator_editor' // 树编辑器操作符编辑器
'@web/core/tree_editor/tree_editor_value_editors' // 树编辑器值编辑器
'@web/core/tree_editor/utils'                    // 树编辑器工具
'@web/core/model_field_selector/model_field_selector' // 模型字段选择器
'@web/core/l10n/translation'                     // 国际化
```

## 主组件类

### 1. 组件属性定义

```javascript
static props = {
    resModel: String,        // 资源模型
    fields: Object,          // 字段定义
    expression: String,      // 表达式字符串
    update: Function,        // 更新回调
};
```

**属性功能**:
- **模型绑定**: resModel指定目标数据模型
- **字段定义**: fields包含所有可用字段的定义
- **表达式**: expression为当前的表达式字符串
- **更新回调**: update处理表达式变化

### 2. 组件初始化

```javascript
setup() {
    onWillStart(() => this.onPropsUpdated(this.props));
    onWillUpdateProps((nextProps) => this.onPropsUpdated(nextProps));
}

async onPropsUpdated(props) {
    // 过滤字段，排除properties类型
    this.filteredFields = Object.fromEntries(
        Object.entries(props.fields).filter(([_, fieldDef]) => fieldDef.type !== "properties")
    );
    
    try {
        // 将表达式转换为树结构
        this.tree = treeFromExpression(props.expression, {
            getFieldDef: (name) => this.getFieldDef(name, props),
            distributeNot: !this.isDebugMode,
        });
    } catch {
        this.tree = null;
    }
}
```

**初始化功能**:
- **属性监听**: 监听属性变化并更新内部状态
- **字段过滤**: 过滤掉不支持的字段类型
- **表达式解析**: 将字符串表达式解析为树结构
- **错误处理**: 优雅处理解析失败的情况

## 核心方法

### 1. 字段定义获取

```javascript
getFieldDef(name, props = this.props) {
    if (typeof name === "string") {
        return props.fields[name] || null;
    }
    return null;
}
```

**字段定义功能**:
- **类型检查**: 确保字段名为字符串类型
- **字段查找**: 从字段定义中查找指定字段
- **空值处理**: 未找到字段时返回null

### 2. 默认条件生成

```javascript
getDefaultCondition() {
    const defaultPath = getDefaultPath(this.filteredFields);
    const fieldDef = this.filteredFields[defaultPath];
    const operator = getExpressionDisplayedOperators(fieldDef)[0];
    const value = getDefaultValue(fieldDef, operator);
    return condition(fieldDef.name, operator, value);
}
```

**默认条件功能**:
- **路径选择**: 选择合适的默认字段路径
- **操作符获取**: 获取字段的默认操作符
- **默认值**: 生成操作符对应的默认值
- **条件构建**: 构建完整的条件对象

### 3. 操作符编辑器信息

```javascript
getDefaultOperator(fieldDef) {
    return getExpressionDisplayedOperators(fieldDef)[0];
}

getOperatorEditorInfo(fieldDef) {
    const operators = getExpressionDisplayedOperators(fieldDef);
    return getOperatorEditorInfo(operators, fieldDef);
}
```

**操作符编辑器功能**:
- **默认操作符**: 获取字段类型的默认操作符
- **编辑器信息**: 获取操作符编辑器的配置信息
- **操作符列表**: 获取字段支持的所有操作符

### 4. 路径编辑器配置

```javascript
getPathEditorInfo(resModel, defaultCondition) {
    if (resModel !== this.props.resModel) {
        throw new Error(
            `Expression editor doesn't support tree as value so resModel has to be props.resModel`
        );
    }
    
    return {
        component: ModelFieldSelector,
        extractProps: ({ value, update }) => ({
            path: value,
            update,
            resModel: this.props.resModel,
            readonly: false,
            filter: (fieldDef) => fieldDef.name in this.filteredFields,
            showDebugInput: false,
            followRelations: false,
            isDebugMode: this.isDebugMode,
        }),
        isSupported: (value) => [0, 1].includes(value) || value in this.filteredFields,
        stringify: (value) => this.props.fields[value].string,
        defaultValue: () => defaultCondition.path,
        message: _t("Field properties not supported"),
    };
}
```

**路径编辑器功能**:
- **模型验证**: 验证模型一致性
- **组件配置**: 配置ModelFieldSelector组件
- **字段过滤**: 过滤可用字段
- **值支持**: 检查值是否被支持
- **字符串化**: 将字段值转换为显示字符串

### 5. 表达式更新

```javascript
onExpressionChange(expression) {
    this.props.update(expression);
}

resetExpression() {
    this.props.update("True");
}

update(tree) {
    const expression = expressionFromTree(tree, {
        getFieldDef: (name) => this.getFieldDef(name),
    });
    this.props.update(expression);
}
```

**更新功能**:
- **直接更新**: 直接更新表达式字符串
- **重置表达式**: 重置为默认的"True"表达式
- **树转换**: 将树结构转换为表达式字符串

## 使用场景

### 1. 基础表达式编辑器

```javascript
// 基础表达式编辑器使用
class BasicExpressionEditor extends Component {
    setup() {
        this.state = useState({
            expression: 'True',
            resModel: 'res.partner',
            fields: {
                name: { name: 'name', string: '名称', type: 'char' },
                email: { name: 'email', string: '邮箱', type: 'char' },
                active: { name: 'active', string: '活跃', type: 'boolean' },
                create_date: { name: 'create_date', string: '创建日期', type: 'datetime' },
                category_id: { name: 'category_id', string: '分类', type: 'many2one' }
            }
        });
    }

    onExpressionUpdate(newExpression) {
        this.state.expression = newExpression;
        console.log('Expression updated:', newExpression);
    }

    validateExpression() {
        try {
            // 这里可以添加表达式验证逻辑
            console.log('Expression is valid:', this.state.expression);
            this.notification.add('表达式验证通过', { type: 'success' });
        } catch (error) {
            console.error('Expression validation failed:', error);
            this.notification.add('表达式验证失败', { type: 'danger' });
        }
    }

    render() {
        return xml`
            <div class="basic-expression-editor">
                <h5>基础表达式编辑器</h5>
                
                <div class="expression-info mb-3">
                    <div class="card">
                        <div class="card-header">
                            <h6>当前表达式</h6>
                        </div>
                        <div class="card-body">
                            <code t-esc="state.expression"/>
                        </div>
                    </div>
                </div>

                <div class="expression-editor-container">
                    <ExpressionEditor
                        resModel="state.resModel"
                        fields="state.fields"
                        expression="state.expression"
                        update="onExpressionUpdate"
                    />
                </div>

                <div class="expression-actions mt-3">
                    <button 
                        class="btn btn-primary me-2"
                        t-on-click="validateExpression"
                    >
                        验证表达式
                    </button>
                    <button 
                        class="btn btn-secondary"
                        t-on-click="() => this.onExpressionUpdate('True')"
                    >
                        重置
                    </button>
                </div>
            </div>
        `;
    }
}
```

### 2. 高级过滤器编辑器

```javascript
// 高级过滤器编辑器
class AdvancedFilterEditor extends Component {
    setup() {
        this.orm = useService('orm');
        this.state = useState({
            filters: [
                {
                    id: 1,
                    name: '活跃用户',
                    expression: 'active == True',
                    description: '显示所有活跃用户'
                },
                {
                    id: 2,
                    name: '最近创建',
                    expression: 'create_date >= (datetime.now() - timedelta(days=30))',
                    description: '显示最近30天创建的记录'
                }
            ],
            currentFilter: null,
            resModel: 'res.partner',
            fields: {},
            isEditing: false
        });

        this.loadFields();
    }

    async loadFields() {
        try {
            const fields = await this.orm.call(
                'ir.model.fields',
                'get_model_fields',
                [this.state.resModel]
            );
            this.state.fields = fields;
        } catch (error) {
            console.error('Failed to load fields:', error);
        }
    }

    createNewFilter() {
        const newFilter = {
            id: Date.now(),
            name: '新过滤器',
            expression: 'True',
            description: ''
        };
        this.state.filters.push(newFilter);
        this.editFilter(newFilter);
    }

    editFilter(filter) {
        this.state.currentFilter = { ...filter };
        this.state.isEditing = true;
    }

    saveFilter() {
        if (!this.state.currentFilter) return;

        const index = this.state.filters.findIndex(f => f.id === this.state.currentFilter.id);
        if (index !== -1) {
            this.state.filters[index] = { ...this.state.currentFilter };
        }

        this.state.isEditing = false;
        this.state.currentFilter = null;
        this.notification.add('过滤器保存成功', { type: 'success' });
    }

    cancelEdit() {
        this.state.isEditing = false;
        this.state.currentFilter = null;
    }

    deleteFilter(filterId) {
        if (!confirm('确定要删除这个过滤器吗？')) return;

        this.state.filters = this.state.filters.filter(f => f.id !== filterId);
        if (this.state.currentFilter && this.state.currentFilter.id === filterId) {
            this.cancelEdit();
        }
    }

    onExpressionUpdate(newExpression) {
        if (this.state.currentFilter) {
            this.state.currentFilter.expression = newExpression;
        }
    }

    testFilter(filter) {
        console.log('Testing filter:', filter);
        // 这里可以添加过滤器测试逻辑
        this.notification.add(`过滤器 "${filter.name}" 测试完成`, { type: 'info' });
    }

    render() {
        return xml`
            <div class="advanced-filter-editor">
                <div class="header mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>高级过滤器编辑器</h5>
                        <button 
                            class="btn btn-primary"
                            t-on-click="createNewFilter"
                        >
                            <i class="fa fa-plus"/> 新建过滤器
                        </button>
                    </div>
                </div>

                <div class="filters-list" t-if="!state.isEditing">
                    <div class="row">
                        <t t-foreach="state.filters" t-as="filter" t-key="filter.id">
                            <div class="col-md-6 mb-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title" t-esc="filter.name"/>
                                        <p class="card-text text-muted" t-esc="filter.description"/>
                                        <div class="expression-preview mb-3">
                                            <small class="text-muted">表达式:</small>
                                            <code class="d-block" t-esc="filter.expression"/>
                                        </div>
                                        <div class="filter-actions">
                                            <button 
                                                class="btn btn-sm btn-outline-primary me-2"
                                                t-on-click="() => this.editFilter(filter)"
                                            >
                                                编辑
                                            </button>
                                            <button 
                                                class="btn btn-sm btn-outline-info me-2"
                                                t-on-click="() => this.testFilter(filter)"
                                            >
                                                测试
                                            </button>
                                            <button 
                                                class="btn btn-sm btn-outline-danger"
                                                t-on-click="() => this.deleteFilter(filter.id)"
                                            >
                                                删除
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>
                </div>

                <div class="filter-editor" t-if="state.isEditing and state.currentFilter">
                    <div class="card">
                        <div class="card-header">
                            <h6>编辑过滤器: <t t-esc="state.currentFilter.name"/></h6>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">过滤器名称</label>
                                    <input 
                                        type="text" 
                                        class="form-control"
                                        t-model="state.currentFilter.name"
                                    />
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">描述</label>
                                    <input 
                                        type="text" 
                                        class="form-control"
                                        t-model="state.currentFilter.description"
                                    />
                                </div>
                            </div>

                            <div class="expression-editor-section">
                                <label class="form-label">表达式</label>
                                <ExpressionEditor
                                    resModel="state.resModel"
                                    fields="state.fields"
                                    expression="state.currentFilter.expression"
                                    update="onExpressionUpdate"
                                />
                            </div>

                            <div class="editor-actions mt-3">
                                <button 
                                    class="btn btn-success me-2"
                                    t-on-click="saveFilter"
                                >
                                    保存
                                </button>
                                <button 
                                    class="btn btn-secondary"
                                    t-on-click="cancelEdit"
                                >
                                    取消
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="empty-state text-center py-5" t-if="!state.filters.length">
                    <i class="fa fa-filter fa-3x text-muted mb-3"/>
                    <h6 class="text-muted">暂无过滤器</h6>
                    <p class="text-muted">点击上方按钮创建第一个过滤器</p>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 树结构编辑
- 基于树编辑器的可视化编辑
- 表达式与树结构的双向转换
- 复杂表达式的结构化表示

### 2. 字段感知
- 模型字段的自动识别
- 字段类型的智能处理
- 字段过滤和验证

### 3. 操作符适配
- 根据字段类型提供合适的操作符
- 操作符的动态配置
- 表达式特定的操作符支持

### 4. 调试模式
- 调试模式的特殊处理
- 开发者友好的功能
- 高级用户的扩展功能

## 设计模式

### 1. 适配器模式 (Adapter Pattern)
- 树编辑器与表达式编辑器的适配
- 不同数据格式的转换

### 2. 策略模式 (Strategy Pattern)
- 不同字段类型的处理策略
- 可配置的编辑器行为

### 3. 组合模式 (Composite Pattern)
- 复杂表达式的组合结构
- 嵌套条件的统一处理

## 注意事项

1. **表达式安全**: 确保生成的表达式安全可执行
2. **字段验证**: 验证字段的存在性和类型
3. **错误处理**: 优雅处理表达式解析错误
4. **性能考虑**: 避免频繁的表达式转换

## 扩展建议

1. **语法高亮**: 表达式的语法高亮显示
2. **自动完成**: 字段和函数的自动完成
3. **表达式模板**: 常用表达式的模板库
4. **实时验证**: 表达式的实时语法验证
5. **导入导出**: 表达式的导入导出功能

该表达式编辑器为Odoo Web应用提供了强大的Python表达式可视化编辑功能，通过树结构编辑和智能字段处理确保了表达式构建的准确性和易用性。

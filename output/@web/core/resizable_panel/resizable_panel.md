# ResizablePanel - 可调整大小面板

## 概述

`resizable_panel.js` 是 Odoo Web 核心模块的可调整大小面板组件，提供了用户可拖拽调整宽度的面板功能。该组件支持鼠标拖拽调整、最小宽度限制、RTL语言支持、窗口自适应等功能，具备边界检测、方向感知、事件处理等特性，为用户提供了灵活的界面布局调整能力，广泛应用于侧边栏、工具面板、分割视图等需要动态调整大小的场景。

## 文件信息
- **路径**: `/web/static/src/core/resizable_panel/resizable_panel.js`
- **行数**: 170
- **模块**: `@web/core/resizable_panel/resizable_panel`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                          // OWL框架
```

## 核心钩子函数

### 1. useResizable

```javascript
function useResizable({
    containerRef,        // 容器引用
    handleRef,          // 拖拽手柄引用
    initialWidth = 400, // 初始宽度
    getMinWidth = () => 400,     // 获取最小宽度函数
    onResize = () => {},         // 调整大小回调
    getResizeSide = () => "end", // 获取调整边函数
}) {
    // 钩子实现
}
```

**钩子功能**:
- **拖拽调整**: 通过鼠标拖拽调整面板宽度
- **边界限制**: 限制最小宽度和最大宽度
- **方向支持**: 支持LTR和RTL文本方向
- **响应式**: 窗口大小变化时自动调整

**参数说明**:
- **containerRef**: 容器元素的引用
- **handleRef**: 拖拽手柄元素的引用
- **initialWidth**: 面板的初始宽度
- **getMinWidth**: 获取最小宽度的函数
- **onResize**: 宽度变化时的回调函数
- **getResizeSide**: 获取调整边的函数（start/end）

## 事件处理机制

### 1. 鼠标事件处理

```javascript
function onMouseDown() {
    isChangingSize = true;
    document.body.classList.add("pe-none", "user-select-none");
}

function onMouseUp() {
    isChangingSize = false;
    document.body.classList.remove("pe-none", "user-select-none");
}

function onMouseMove(ev) {
    if (!isChangingSize || !containerRef.el) {
        return;
    }
    const direction = (docDirection === "ltr" && resizeSide === "end") ||
                     (docDirection === "rtl" && resizeSide === "start") ? 1 : -1;
    const fixedSide = direction === 1 ? "left" : "right";
    const containerRect = getContainerRect();
    const newWidth = (ev.clientX - containerRect[fixedSide]) * direction;
    resize(computeFinalWidth(newWidth));
}
```

**事件处理功能**:
- **拖拽开始**: 设置拖拽状态，禁用页面交互
- **拖拽结束**: 清除拖拽状态，恢复页面交互
- **拖拽移动**: 计算新宽度并实时调整面板大小
- **方向计算**: 根据文本方向和调整边计算拖拽方向

### 2. 窗口调整处理

```javascript
useExternalListener(window, "resize", () => {
    const limit = getLimitWidth();
    if (getContainerRect().width >= limit) {
        resize(computeFinalWidth(limit));
    }
});
```

**窗口调整功能**:
- **自动适应**: 窗口大小变化时自动调整面板
- **边界检查**: 确保面板不超出可用空间
- **智能调整**: 只在必要时进行调整

## 宽度计算

### 1. 最终宽度计算

```javascript
function computeFinalWidth(targetContainerWidth) {
    const handlerSpacing = handleRef.el ? handleRef.el.offsetWidth / 2 : 10;
    const w = Math.max(minWidth, targetContainerWidth + handlerSpacing);
    const limit = getLimitWidth();
    return Math.min(w, limit - handlerSpacing);
}
```

**计算功能**:
- **手柄间距**: 考虑拖拽手柄的宽度
- **最小限制**: 确保不小于最小宽度
- **最大限制**: 确保不超出容器限制
- **精确计算**: 提供准确的宽度值

### 2. 容器矩形获取

```javascript
function getContainerRect() {
    const container = containerRef.el;
    const offsetParent = container.offsetParent;
    let containerRect = {};
    if (!offsetParent) {
        containerRect = container.getBoundingClientRect();
    } else {
        containerRect.left = container.offsetLeft;
        containerRect.right = container.offsetLeft + container.offsetWidth;
        containerRect.width = container.offsetWidth;
    }
    return containerRect;
}
```

## 主组件类

### 1. ResizablePanel 组件

```javascript
class ResizablePanel extends Component {
    static template = "web_studio.ResizablePanel";
    
    static props = {
        onResize: { type: Function, optional: true },     // 调整回调
        initialWidth: { type: Number, optional: true },   // 初始宽度
        minWidth: { type: Number, optional: true },       // 最小宽度
        class: { type: String, optional: true },          // CSS类
        slots: { type: Object },                          // 插槽内容
        handleSide: {                                     // 手柄位置
            validate: (val) => ["start", "end"].includes(val),
            optional: true,
        },
    };

    static defaultProps = {
        onResize: () => {},
        width: 400,
        minWidth: 400,
        class: "",
        handleSide: "end",
    };
}
```

**组件功能**:
- **属性验证**: 严格的属性类型验证
- **默认值**: 合理的默认属性值
- **插槽支持**: 支持自定义内容插槽
- **样式管理**: 自动添加必要的CSS类

### 2. 组件初始化

```javascript
setup() {
    useResizable({
        containerRef: "containerRef",
        handleRef: "handleRef",
        onResize: this.props.onResize,
        initialWidth: this.props.initialWidth,
        getMinWidth: (props) => props.minWidth,
        getResizeSide: (props) => props.handleSide,
    });
}

get class() {
    const classes = this.props.class.split(" ");
    if (!classes.some((cls) => cls.startsWith("position-"))) {
        classes.push("position-relative");
    }
    return classes.join(" ");
}
```

## 使用场景

### 1. 基础可调整面板

```javascript
// 基础可调整面板使用
class BasicResizablePanel extends Component {
    setup() {
        this.state = useState({
            panelWidth: 300,
            minWidth: 200,
            content: '这是面板内容，可以通过拖拽右边的手柄来调整宽度。',
            resizeCount: 0
        });
    }

    onPanelResize(newWidth) {
        this.state.panelWidth = Math.round(newWidth);
        this.state.resizeCount++;
        console.log('Panel resized to:', newWidth);
    }

    resetPanel() {
        // 通过改变key强制重新渲染来重置宽度
        this.state.panelWidth = 300;
        this.render();
    }

    render() {
        return xml`
            <div class="basic-resizable-panel-demo">
                <h5>基础可调整面板</h5>
                
                <div class="controls mb-3">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">最小宽度</label>
                            <input 
                                type="number" 
                                class="form-control"
                                t-model="state.minWidth"
                                min="100"
                                max="500"
                            />
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">当前宽度</label>
                            <input 
                                type="text" 
                                class="form-control"
                                t-att-value="state.panelWidth + 'px'"
                                readonly="true"
                            />
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">操作</label>
                            <button 
                                class="btn btn-secondary w-100"
                                t-on-click="resetPanel"
                            >
                                重置宽度
                            </button>
                        </div>
                    </div>
                </div>

                <div class="demo-container border rounded p-3" style="height: 400px; overflow: hidden;">
                    <div class="d-flex h-100">
                        <ResizablePanel
                            initialWidth="state.panelWidth"
                            minWidth="state.minWidth"
                            onResize="onPanelResize"
                            handleSide="end"
                            class="border-end bg-light"
                        >
                            <t t-set-slot="default">
                                <div class="p-3 h-100">
                                    <h6>可调整面板</h6>
                                    <p t-esc="state.content"/>
                                    <div class="panel-stats mt-3">
                                        <p><strong>当前宽度:</strong> <t t-esc="state.panelWidth"/>px</p>
                                        <p><strong>最小宽度:</strong> <t t-esc="state.minWidth"/>px</p>
                                        <p><strong>调整次数:</strong> <t t-esc="state.resizeCount"/></p>
                                    </div>
                                </div>
                            </t>
                        </ResizablePanel>
                        
                        <div class="flex-grow-1 p-3">
                            <h6>主内容区域</h6>
                            <p>这是主内容区域，会根据左侧面板的宽度自动调整。</p>
                            <div class="content-info">
                                <p>左侧面板可以通过拖拽边缘来调整宽度。</p>
                                <p>面板有最小宽度限制，不会小于设定值。</p>
                                <p>当窗口大小变化时，面板会自动适应。</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="instructions mt-3">
                    <div class="alert alert-info">
                        <h6>使用说明</h6>
                        <ul class="mb-0">
                            <li>拖拽面板右边缘的手柄来调整宽度</li>
                            <li>面板宽度不会小于设定的最小值</li>
                            <li>调整浏览器窗口大小观察自适应效果</li>
                            <li>使用重置按钮恢复默认宽度</li>
                        </ul>
                    </div>
                </div>
            </div>
        `;
    }
}
```

### 2. 多面板布局管理器

```javascript
// 多面板布局管理器
class MultiPanelLayoutManager extends Component {
    setup() {
        this.state = useState({
            panels: [
                { 
                    id: 'sidebar', 
                    name: '侧边栏', 
                    width: 250, 
                    minWidth: 200, 
                    maxWidth: 400,
                    handleSide: 'end',
                    visible: true 
                },
                { 
                    id: 'inspector', 
                    name: '检查器', 
                    width: 300, 
                    minWidth: 250, 
                    maxWidth: 500,
                    handleSide: 'start',
                    visible: true 
                }
            ],
            layout: 'three-column',
            totalResizes: 0
        });
    }

    onPanelResize(panelId, newWidth) {
        const panel = this.state.panels.find(p => p.id === panelId);
        if (panel) {
            panel.width = Math.round(newWidth);
            this.state.totalResizes++;
        }
    }

    togglePanel(panelId) {
        const panel = this.state.panels.find(p => p.id === panelId);
        if (panel) {
            panel.visible = !panel.visible;
        }
    }

    resetAllPanels() {
        this.state.panels.forEach(panel => {
            switch (panel.id) {
                case 'sidebar':
                    panel.width = 250;
                    break;
                case 'inspector':
                    panel.width = 300;
                    break;
            }
        });
        this.state.totalResizes = 0;
    }

    exportLayout() {
        const layout = {
            panels: this.state.panels.map(p => ({
                id: p.id,
                width: p.width,
                visible: p.visible
            })),
            layout: this.state.layout
        };
        
        const blob = new Blob([JSON.stringify(layout, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'panel_layout.json';
        a.click();
        URL.revokeObjectURL(url);
    }

    render() {
        const sidebarPanel = this.state.panels.find(p => p.id === 'sidebar');
        const inspectorPanel = this.state.panels.find(p => p.id === 'inspector');

        return xml`
            <div class="multi-panel-layout-manager">
                <div class="header mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>多面板布局管理器</h5>
                        <div class="header-actions">
                            <button 
                                class="btn btn-outline-primary me-2"
                                t-on-click="resetAllPanels"
                            >
                                重置布局
                            </button>
                            <button 
                                class="btn btn-outline-secondary"
                                t-on-click="exportLayout"
                            >
                                导出布局
                            </button>
                        </div>
                    </div>
                </div>

                <div class="layout-controls mb-3">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>面板控制</h6>
                            <div class="panel-toggles">
                                <t t-foreach="state.panels" t-as="panel" t-key="panel.id">
                                    <div class="form-check form-check-inline">
                                        <input 
                                            class="form-check-input" 
                                            type="checkbox" 
                                            t-model="panel.visible"
                                            t-on-change="() => this.togglePanel(panel.id)"
                                        />
                                        <label class="form-check-label" t-esc="panel.name"/>
                                    </div>
                                </t>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>布局统计</h6>
                            <p>总调整次数: <strong t-esc="state.totalResizes"/></p>
                        </div>
                    </div>
                </div>

                <div class="layout-container border rounded" style="height: 500px; overflow: hidden;">
                    <div class="d-flex h-100">
                        <!-- 左侧边栏 -->
                        <ResizablePanel
                            t-if="sidebarPanel.visible"
                            initialWidth="sidebarPanel.width"
                            minWidth="sidebarPanel.minWidth"
                            onResize="(width) => this.onPanelResize('sidebar', width)"
                            handleSide="sidebarPanel.handleSide"
                            class="border-end bg-light"
                        >
                            <t t-set-slot="default">
                                <div class="p-3 h-100">
                                    <h6 t-esc="sidebarPanel.name"/>
                                    <div class="panel-content">
                                        <p>这是左侧边栏内容。</p>
                                        <div class="panel-info">
                                            <p><strong>当前宽度:</strong> <t t-esc="sidebarPanel.width"/>px</p>
                                            <p><strong>最小宽度:</strong> <t t-esc="sidebarPanel.minWidth"/>px</p>
                                            <p><strong>最大宽度:</strong> <t t-esc="sidebarPanel.maxWidth"/>px</p>
                                        </div>
                                        <div class="sidebar-menu mt-3">
                                            <div class="list-group list-group-flush">
                                                <a href="#" class="list-group-item list-group-item-action">菜单项 1</a>
                                                <a href="#" class="list-group-item list-group-item-action">菜单项 2</a>
                                                <a href="#" class="list-group-item list-group-item-action">菜单项 3</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </t>
                        </ResizablePanel>

                        <!-- 主内容区域 -->
                        <div class="flex-grow-1 p-3 d-flex flex-column">
                            <h6>主内容区域</h6>
                            <div class="content-area flex-grow-1 border rounded p-3 bg-white">
                                <p>这是主内容区域，会根据两侧面板的宽度自动调整。</p>
                                <div class="content-stats">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>左侧面板</h6>
                                            <p>可见: <span t-att-class="sidebarPanel.visible ? 'text-success' : 'text-muted'">
                                                <t t-esc="sidebarPanel.visible ? '是' : '否'"/>
                                            </span></p>
                                            <p t-if="sidebarPanel.visible">宽度: <t t-esc="sidebarPanel.width"/>px</p>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>右侧面板</h6>
                                            <p>可见: <span t-att-class="inspectorPanel.visible ? 'text-success' : 'text-muted'">
                                                <t t-esc="inspectorPanel.visible ? '是' : '否'"/>
                                            </span></p>
                                            <p t-if="inspectorPanel.visible">宽度: <t t-esc="inspectorPanel.width"/>px</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 右侧检查器 -->
                        <ResizablePanel
                            t-if="inspectorPanel.visible"
                            initialWidth="inspectorPanel.width"
                            minWidth="inspectorPanel.minWidth"
                            onResize="(width) => this.onPanelResize('inspector', width)"
                            handleSide="inspectorPanel.handleSide"
                            class="border-start bg-light"
                        >
                            <t t-set-slot="default">
                                <div class="p-3 h-100">
                                    <h6 t-esc="inspectorPanel.name"/>
                                    <div class="panel-content">
                                        <p>这是右侧检查器内容。</p>
                                        <div class="panel-info">
                                            <p><strong>当前宽度:</strong> <t t-esc="inspectorPanel.width"/>px</p>
                                            <p><strong>最小宽度:</strong> <t t-esc="inspectorPanel.minWidth"/>px</p>
                                            <p><strong>最大宽度:</strong> <t t-esc="inspectorPanel.maxWidth"/>px</p>
                                        </div>
                                        <div class="inspector-content mt-3">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6>属性面板</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-2">
                                                        <label class="form-label">名称</label>
                                                        <input type="text" class="form-control form-control-sm" value="示例对象"/>
                                                    </div>
                                                    <div class="mb-2">
                                                        <label class="form-label">类型</label>
                                                        <select class="form-select form-select-sm">
                                                            <option>文本</option>
                                                            <option>数字</option>
                                                            <option>日期</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </t>
                        </ResizablePanel>
                    </div>
                </div>

                <div class="layout-info mt-3">
                    <div class="card">
                        <div class="card-header">
                            <h6>布局信息</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <h6>面板数量</h6>
                                    <p>总计: <t t-esc="state.panels.length"/></p>
                                    <p>可见: <t t-esc="state.panels.filter(p => p.visible).length"/></p>
                                </div>
                                <div class="col-md-4">
                                    <h6>总宽度</h6>
                                    <p><t t-esc="state.panels.filter(p => p.visible).reduce((sum, p) => sum + p.width, 0)"/>px</p>
                                </div>
                                <div class="col-md-4">
                                    <h6>调整统计</h6>
                                    <p>总次数: <t t-esc="state.totalResizes"/></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 响应式调整
- 实时的宽度调整
- 平滑的拖拽体验
- 智能的边界限制

### 2. 方向感知
- 支持LTR和RTL文本方向
- 智能的拖拽方向计算
- 灵活的手柄位置

### 3. 事件优化
- 防止页面交互干扰
- 高效的事件处理
- 内存安全的清理

### 4. 自适应布局
- 窗口大小变化适应
- 容器边界检测
- 智能的宽度计算

## 设计模式

### 1. 钩子模式 (Hook Pattern)
- 可重用的调整大小逻辑
- 组件间的逻辑共享

### 2. 观察者模式 (Observer Pattern)
- 宽度变化的事件通知
- 响应式的状态更新

### 3. 策略模式 (Strategy Pattern)
- 不同方向的处理策略
- 可配置的调整行为

## 注意事项

1. **性能优化**: 避免频繁的DOM操作和重绘
2. **内存管理**: 正确清理事件监听器
3. **边界检查**: 确保调整不超出容器限制
4. **用户体验**: 提供清晰的拖拽反馈

## 扩展建议

1. **垂直调整**: 支持高度的调整功能
2. **双向调整**: 同时支持宽度和高度调整
3. **网格对齐**: 调整时对齐到网格
4. **动画效果**: 添加平滑的调整动画
5. **触摸支持**: 支持触摸设备的调整操作

该可调整大小面板组件为Odoo Web应用提供了灵活的界面布局调整功能，通过智能的边界检测和方向感知确保了良好的用户体验和系统稳定性。

# InstallScopedApp - 安装作用域应用

## 概述

`install_scoped_app.js` 是 Odoo Web 核心模块的安装作用域应用组件，提供了PWA（渐进式Web应用）的安装功能。该组件支持应用名称自定义、安装提示显示、浏览器兼容性检测、独立显示模式检测等功能，具备PWA清单获取、安装状态管理、URL参数处理等特性，为用户提供了便捷的应用安装体验，主要应用于PWA应用的安装和管理场景。

## 文件信息
- **路径**: `/web/static/src/core/install_scoped_app/install_scoped_app.js`
- **行数**: 52
- **模块**: `@web/core/install_scoped_app/install_scoped_app`

## 依赖关系

```javascript
// 核心依赖
'@web/core/browser/browser'              // 浏览器服务
'@web/core/registry'                     // 注册表系统
'@odoo/owl'                              // OWL框架
'@web/core/browser/feature_detection'    // 特性检测
'@web/core/utils/hooks'                  // 工具钩子
'@web/core/dropdown/dropdown'            // 下拉菜单
```

## 主组件类

### 1. InstallScopedApp 组件

```javascript
class InstallScopedApp extends Component {
    static props = {};
    static template = "web.InstallScopedApp";
    static components = { Dropdown };
}
```

**组件功能**:
- **PWA安装**: 提供PWA应用的安装功能
- **名称定制**: 支持应用名称的自定义
- **状态检测**: 检测安装可能性和显示模式
- **用户界面**: 提供友好的安装界面

### 2. 组件初始化

```javascript
setup() {
    this.pwa = useState(useService("pwa"));
    this.state = useState({ manifest: {}, showInstallUI: false });
    this.isDisplayStandalone = isDisplayStandalone();
    // BeforeInstallPrompt事件可能需要一段时间才会被浏览器触发
    // 有些会立即显示，有些会等待用户与网站交互一段时间
    this.isInstallationPossible = browser.BeforeInstallPromptEvent !== undefined;
    onMounted(async () => {
        this.state.manifest = await this.pwa.getManifest();
        this.state.showInstallUI = true;
    });
}
```

**初始化功能**:
- **PWA服务**: 获取PWA服务用于应用管理
- **状态管理**: 管理清单数据和UI显示状态
- **模式检测**: 检测是否在独立显示模式下运行
- **安装检测**: 检测浏览器是否支持PWA安装
- **清单加载**: 组件挂载后异步加载PWA清单

## 核心方法

### 1. 应用名称更改

```javascript
onChangeName(ev) {
    const value = ev.target.value;
    if (value !== this.state.manifest.name) {
        const url = new URL(document.location.href);
        url.searchParams.set("app_name", encodeURIComponent(value));
        browser.location.replace(url);
    }
}
```

**名称更改功能**:
- **值比较**: 检查新名称是否与当前名称不同
- **URL构建**: 构建包含新应用名称的URL
- **参数编码**: 对应用名称进行URL编码
- **页面重定向**: 使用新URL重新加载页面

### 2. 应用安装

```javascript
onInstall() {
    this.state.showInstallUI = false;
    this.pwa.show({
        onDone: (res) => {
            if (res.outcome === "accepted") {
                browser.location.replace(this.state.manifest.start_url);
            } else {
                this.state.showInstallUI = true;
            }
        },
    });
}
```

**安装功能**:
- **UI隐藏**: 隐藏安装界面防止重复操作
- **安装提示**: 调用PWA服务显示安装提示
- **结果处理**: 根据用户选择处理安装结果
- **成功跳转**: 安装成功后跳转到应用起始URL
- **失败恢复**: 安装失败或取消后恢复UI显示

## 组件注册

```javascript
registry.category("public_components").add("web.install_scoped_app", InstallScopedApp);
```

**注册功能**:
- **公共组件**: 注册为公共组件类别
- **全局可用**: 在公共页面中全局可用
- **PWA支持**: 专门用于PWA功能的组件

## 使用场景

### 1. PWA安装管理器

```javascript
// PWA安装管理器示例
class PWAInstallManager extends Component {
    setup() {
        this.pwa = useService("pwa");
        this.state = useState({
            isInstallable: false,
            isInstalled: false,
            isStandalone: false,
            manifest: null,
            installPrompt: null,
            customAppName: '',
            installHistory: []
        });

        this.checkPWAStatus();
    }

    async checkPWAStatus() {
        // 检查PWA状态
        this.state.isStandalone = isDisplayStandalone();
        this.state.isInstallable = browser.BeforeInstallPromptEvent !== undefined;
        
        try {
            this.state.manifest = await this.pwa.getManifest();
            this.state.customAppName = this.state.manifest.name || '';
        } catch (error) {
            console.error('Failed to load PWA manifest:', error);
        }

        // 监听安装提示事件
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            this.state.installPrompt = e;
            this.state.isInstallable = true;
        });

        // 监听应用安装事件
        window.addEventListener('appinstalled', () => {
            this.state.isInstalled = true;
            this.addInstallRecord('应用已成功安装');
        });
    }

    async installApp() {
        if (!this.state.installPrompt) {
            this.addInstallRecord('安装提示不可用');
            return;
        }

        try {
            const result = await this.state.installPrompt.prompt();
            this.addInstallRecord(`用户选择: ${result.outcome}`);
            
            if (result.outcome === 'accepted') {
                this.state.isInstalled = true;
                // 跳转到应用起始页
                if (this.state.manifest?.start_url) {
                    window.location.href = this.state.manifest.start_url;
                }
            }
        } catch (error) {
            this.addInstallRecord(`安装失败: ${error.message}`);
        } finally {
            this.state.installPrompt = null;
        }
    }

    updateAppName() {
        if (this.state.customAppName !== this.state.manifest?.name) {
            const url = new URL(window.location.href);
            url.searchParams.set('app_name', encodeURIComponent(this.state.customAppName));
            window.location.href = url.toString();
        }
    }

    addInstallRecord(message) {
        this.state.installHistory.unshift({
            timestamp: new Date().toLocaleString(),
            message: message
        });

        // 限制历史记录数量
        if (this.state.installHistory.length > 10) {
            this.state.installHistory.pop();
        }
    }

    getInstallButtonText() {
        if (this.state.isInstalled) return '已安装';
        if (!this.state.isInstallable) return '不支持安装';
        return '安装应用';
    }

    render() {
        return xml`
            <div class="pwa-install-manager">
                <h5>PWA安装管理器</h5>
                
                <div class="pwa-status mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6>PWA状态</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="status-item">
                                        <h6>显示模式</h6>
                                        <span t-att-class="'badge ' + (state.isStandalone ? 'bg-success' : 'bg-secondary')">
                                            <t t-esc="state.isStandalone ? '独立模式' : '浏览器模式'"/>
                                        </span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="status-item">
                                        <h6>安装状态</h6>
                                        <span t-att-class="'badge ' + (state.isInstalled ? 'bg-success' : 'bg-warning')">
                                            <t t-esc="state.isInstalled ? '已安装' : '未安装'"/>
                                        </span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="status-item">
                                        <h6>安装支持</h6>
                                        <span t-att-class="'badge ' + (state.isInstallable ? 'bg-success' : 'bg-danger')">
                                            <t t-esc="state.isInstallable ? '支持' : '不支持'"/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="app-info mb-4" t-if="state.manifest">
                    <div class="card">
                        <div class="card-header">
                            <h6>应用信息</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">应用名称</label>
                                        <div class="input-group">
                                            <input 
                                                type="text" 
                                                class="form-control"
                                                t-model="state.customAppName"
                                                placeholder="输入应用名称"
                                            />
                                            <button 
                                                class="btn btn-outline-primary"
                                                t-on-click="updateAppName"
                                                t-att-disabled="state.customAppName === state.manifest.name"
                                            >
                                                更新
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">起始URL</label>
                                        <input 
                                            type="text" 
                                            class="form-control"
                                            t-att-value="state.manifest.start_url"
                                            readonly="true"
                                        />
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>主题颜色:</strong> 
                                        <span t-if="state.manifest.theme_color">
                                            <span 
                                                class="color-preview d-inline-block me-2"
                                                t-att-style="'background-color: ' + state.manifest.theme_color + '; width: 20px; height: 20px; border: 1px solid #ccc;'"
                                            />
                                            <code t-esc="state.manifest.theme_color"/>
                                        </span>
                                        <span t-else="" class="text-muted">未设置</span>
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>背景颜色:</strong> 
                                        <span t-if="state.manifest.background_color">
                                            <span 
                                                class="color-preview d-inline-block me-2"
                                                t-att-style="'background-color: ' + state.manifest.background_color + '; width: 20px; height: 20px; border: 1px solid #ccc;'"
                                            />
                                            <code t-esc="state.manifest.background_color"/>
                                        </span>
                                        <span t-else="" class="text-muted">未设置</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="install-section mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6>安装控制</h6>
                        </div>
                        <div class="card-body text-center">
                            <button 
                                class="btn btn-lg"
                                t-att-class="state.isInstalled ? 'btn-success' : (state.isInstallable ? 'btn-primary' : 'btn-secondary')"
                                t-on-click="installApp"
                                t-att-disabled="!state.isInstallable || state.isInstalled"
                            >
                                <i t-att-class="state.isInstalled ? 'fa fa-check' : 'fa fa-download'"/>
                                <t t-esc="getInstallButtonText()"/>
                            </button>
                            
                            <div class="install-help mt-3">
                                <small class="text-muted">
                                    <t t-if="state.isStandalone">
                                        应用正在独立模式下运行
                                    </t>
                                    <t t-elif="state.isInstalled">
                                        应用已安装到设备
                                    </t>
                                    <t t-elif="state.isInstallable">
                                        点击按钮将应用安装到设备
                                    </t>
                                    <t t-else="">
                                        当前浏览器不支持PWA安装
                                    </t>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="install-history" t-if="state.installHistory.length">
                    <div class="card">
                        <div class="card-header">
                            <h6>安装历史</h6>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                <t t-foreach="state.installHistory" t-as="record" t-key="record_index">
                                    <div class="list-group-item">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span t-esc="record.message"/>
                                            <small class="text-muted" t-esc="record.timestamp"/>
                                        </div>
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="technical-info mt-4">
                    <div class="card">
                        <div class="card-header">
                            <h6>技术信息</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>浏览器支持</h6>
                                    <ul class="list-unstyled">
                                        <li>
                                            <i t-att-class="'fa ' + (browser.BeforeInstallPromptEvent ? 'fa-check text-success' : 'fa-times text-danger')"/>
                                            BeforeInstallPrompt事件
                                        </li>
                                        <li>
                                            <i t-att-class="'fa ' + (navigator.serviceWorker ? 'fa-check text-success' : 'fa-times text-danger')"/>
                                            Service Worker
                                        </li>
                                        <li>
                                            <i t-att-class="'fa ' + (window.matchMedia ? 'fa-check text-success' : 'fa-times text-danger')"/>
                                            Media Queries
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>PWA特性</h6>
                                    <ul class="list-unstyled">
                                        <li>
                                            <i t-att-class="'fa ' + (state.manifest ? 'fa-check text-success' : 'fa-times text-danger')"/>
                                            Web App Manifest
                                        </li>
                                        <li>
                                            <i class="fa fa-info-circle text-info"/>
                                            独立显示模式: <t t-esc="state.isStandalone ? '是' : '否'"/>
                                        </li>
                                        <li>
                                            <i class="fa fa-info-circle text-info"/>
                                            安装提示可用: <t t-esc="state.installPrompt ? '是' : '否'"/>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. PWA集成
- 完整的PWA安装流程
- 清单文件的动态加载
- 浏览器兼容性检测

### 2. 状态管理
- 安装状态的实时跟踪
- UI显示的智能控制
- 用户交互的状态反馈

### 3. 名称定制
- 应用名称的动态修改
- URL参数的自动处理
- 页面的无缝重定向

### 4. 用户体验
- 友好的安装界面
- 清晰的状态指示
- 智能的错误处理

## 设计模式

### 1. 状态模式 (State Pattern)
- 不同安装状态的管理
- 状态转换的控制

### 2. 观察者模式 (Observer Pattern)
- PWA事件的监听和响应
- 状态变化的通知机制

### 3. 策略模式 (Strategy Pattern)
- 不同浏览器的安装策略
- 兼容性处理的策略选择

## 注意事项

1. **浏览器兼容性**: 确保在不同浏览器中的正确行为
2. **用户体验**: 提供清晰的安装指引和状态反馈
3. **错误处理**: 处理安装失败和网络错误
4. **性能考虑**: 避免频繁的清单加载和状态检查

## 扩展建议

1. **安装统计**: 记录和分析安装数据
2. **自定义图标**: 支持应用图标的自定义
3. **离线支持**: 增强离线功能的配置
4. **更新通知**: 应用更新的通知机制
5. **多平台支持**: 针对不同平台的优化

该安装作用域应用组件为Odoo Web应用提供了完整的PWA安装功能，通过智能的状态管理和用户友好的界面确保了良好的安装体验和应用可用性。

# @web/core/virtual_grid_hook.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/virtual_grid_hook.js`
- **原始路径**: `/web/static/src/core/virtual_grid_hook.js`
- **代码行数**: 186行
- **作用**: 提供虚拟网格钩子，实现大数据集的高性能网格渲染

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 虚拟滚动的核心原理和实现机制
- 大数据集的高性能渲染策略
- 缓冲区算法和性能优化技术
- 动态尺寸计算和索引管理
- 企业级数据表格的架构设计

## 📚 核心概念

### 什么是虚拟网格？
虚拟网格是一种**高性能渲染技术**，主要功能：
- **按需渲染**: 只渲染可见区域的元素
- **内存优化**: 避免创建大量DOM节点
- **滚动流畅**: 保持滚动性能不受数据量影响
- **缓冲机制**: 预渲染周围区域提升用户体验

### 核心架构组成
```javascript
// 主要API
useVirtualGrid(params)  // 虚拟网格钩子

// 核心参数
const params = {
    scrollableRef,      // 滚动容器引用
    initialScroll,      // 初始滚动位置
    onChange,           // 可见区域变化回调
    bufferCoef          // 缓冲区系数
};

// 返回值
const {
    columnsIndexes,     // 可见列索引范围
    rowsIndexes,        // 可见行索引范围
    setColumnsWidths,   // 设置列宽度
    setRowsHeights      // 设置行高度
} = useVirtualGrid(params);
```

### 基本使用模式
```javascript
class VirtualDataGrid extends Component {
    static template = xml`
        <div class="virtual-grid-container" t-ref="scrollable">
            <div class="virtual-grid-content" 
                 t-att-style="getContentStyle()">
                <t t-foreach="visibleItems" t-as="item" t-key="item.id">
                    <div class="grid-item" 
                         t-att-style="getItemStyle(item)">
                        <t t-esc="item.content"/>
                    </div>
                </t>
            </div>
        </div>
    `;
    
    setup() {
        this.scrollableRef = useRef("scrollable");
        
        // 使用虚拟网格钩子
        this.virtualGrid = useVirtualGrid({
            scrollableRef: this.scrollableRef,
            initialScroll: { left: 0, top: 0 },
            bufferCoef: 1,
            onChange: (changed) => {
                console.log('Visible area changed:', changed);
                this.render();
            }
        });
        
        // 设置数据尺寸
        onMounted(() => {
            this.virtualGrid.setColumnsWidths(this.columnWidths);
            this.virtualGrid.setRowsHeights(this.rowHeights);
        });
    }
    
    get visibleItems() {
        const [startRow, endRow] = this.virtualGrid.rowsIndexes || [0, 0];
        const [startCol, endCol] = this.virtualGrid.columnsIndexes || [0, 0];
        
        const items = [];
        for (let row = startRow; row <= endRow; row++) {
            for (let col = startCol; col <= endCol; col++) {
                items.push({
                    id: `${row}-${col}`,
                    row,
                    col,
                    content: `Cell ${row},${col}`
                });
            }
        }
        return items;
    }
}
```

## 🔍 核心实现详解

### 1. getIndexes函数 - 索引计算核心

#### 参数结构和算法原理
```javascript
function getIndexes({ sizes, start, span, prevStartIndex, bufferCoef = BUFFER_COEFFICIENT }) {
    // 1. 边界检查
    if (!sizes || !sizes.length) {
        return [];
    }
    
    // 2. 全部可见检查
    if (sizes.at(-1) < span) {
        return [0, sizes.length - 1];
    }
    
    // 3. 缓冲区计算
    const bufferSize = Math.round(span * bufferCoef);
    const bufferStart = start - bufferSize;
    const bufferEnd = start + span + bufferSize;
    
    // 4. 起始索引优化查找
    let startIndex = prevStartIndex ?? 0;
    while (startIndex > 0 && sizes[startIndex] > bufferStart) {
        startIndex--;
    }
    while (startIndex < sizes.length - 1 && sizes[startIndex] <= bufferStart) {
        startIndex++;
    }
    
    // 5. 结束索引计算
    let endIndex = startIndex;
    while (endIndex < sizes.length - 1 && (sizes[endIndex - 1] ?? 0) < bufferEnd) {
        endIndex++;
    }
    while (endIndex > startIndex && (sizes[endIndex - 1] ?? 0) >= bufferEnd) {
        endIndex--;
    }
    
    return [startIndex, endIndex];
}
```

**算法设计特点**：
- **累积尺寸**: sizes数组存储累积尺寸，便于二分查找
- **缓冲优化**: 使用bufferCoef控制预渲染区域大小
- **增量搜索**: 利用prevStartIndex优化搜索起点
- **双向查找**: 分别计算起始和结束索引

#### 缓冲区策略
```javascript
const bufferSize = Math.round(span * bufferCoef);
const bufferStart = start - bufferSize;
const bufferEnd = start + span + bufferSize;
```

**缓冲区设计原理**：
- **预渲染**: 在可见区域周围预渲染内容
- **流畅滚动**: 减少滚动时的白屏现象
- **性能平衡**: bufferCoef控制性能与体验的平衡
- **内存控制**: 避免过度渲染导致内存问题

### 2. useVirtualGrid钩子实现

#### 状态管理和计算函数
```javascript
function useVirtualGrid({ scrollableRef, initialScroll, onChange, bufferCoef }) {
    const comp = useComponent();
    onChange ||= () => comp.render();
    
    // 当前状态存储
    const current = { 
        scroll: { left: 0, top: 0, ...initialScroll },
        columnsIndexes: undefined,
        rowsIndexes: undefined,
        summedColumnsWidths: [],
        summedRowsHeights: []
    };
    
    // 列索引计算
    const computeColumnsIndexes = () => {
        return getIndexes({
            sizes: current.summedColumnsWidths,
            start: Math.abs(current.scroll.left),
            span: window.innerWidth,
            prevStartIndex: current.columnsIndexes?.[0],
            bufferCoef,
        });
    };
    
    // 行索引计算
    const computeRowsIndexes = () => {
        return getIndexes({
            sizes: current.summedRowsHeights,
            start: current.scroll.top,
            span: window.innerHeight,
            prevStartIndex: current.rowsIndexes?.[0],
            bufferCoef,
        });
    };
}
```

**状态设计原则**：
- **最小状态**: 只存储必要的计算状态
- **懒计算**: 按需计算索引范围
- **缓存优化**: 利用前一次结果优化计算
- **响应式**: 状态变化自动触发重新渲染

#### 性能优化的计算调度
```javascript
const throttledCompute = useThrottleForAnimation(() => {
    const changed = [];
    
    // 计算列索引变化
    const columnsVisibleIndexes = computeColumnsIndexes();
    if (!shallowEqual(columnsVisibleIndexes, current.columnsIndexes)) {
        current.columnsIndexes = columnsVisibleIndexes;
        changed.push("columnsIndexes");
    }
    
    // 计算行索引变化
    const rowsVisibleIndexes = computeRowsIndexes();
    if (!shallowEqual(rowsVisibleIndexes, current.rowsIndexes)) {
        current.rowsIndexes = rowsVisibleIndexes;
        changed.push("rowsIndexes");
    }
    
    // 只在有变化时触发回调
    if (changed.length) {
        onChange(pick(current, ...changed));
    }
});
```

**性能优化策略**：
- **动画帧节流**: 使用useThrottleForAnimation避免过度计算
- **变化检测**: 只在索引真正变化时触发更新
- **批量更新**: 收集所有变化一次性通知
- **浅比较**: 使用shallowEqual高效比较数组

#### 事件监听和生命周期管理
```javascript
// 滚动事件监听
const scrollListener = (ev) => {
    current.scroll.left = ev.target.scrollLeft;
    current.scroll.top = ev.target.scrollTop;
    throttledCompute();
};

// 滚动容器事件绑定
useEffect(
    (el) => {
        el?.addEventListener("scroll", scrollListener);
        return () => el?.removeEventListener("scroll", scrollListener);
    },
    () => [scrollableRef.el]
);

// 窗口大小变化监听
useExternalListener(window, "resize", () => throttledCompute());
```

**事件处理设计**：
- **直接监听**: 监听滚动容器的scroll事件
- **自动清理**: 使用useEffect自动管理事件监听器
- **窗口响应**: 监听resize事件适应窗口变化
- **节流处理**: 所有计算都经过节流处理

### 3. 尺寸设置器实现

#### 动态尺寸更新
```javascript
return {
    // 获取器
    get columnsIndexes() {
        return current.columnsIndexes;
    },
    get rowsIndexes() {
        return current.rowsIndexes;
    },
    
    // 列宽设置
    setColumnsWidths(widths) {
        let acc = 0;
        current.summedColumnsWidths = widths.map((w) => (acc += w));
        delete current.columnsIndexes;
        current.columnsIndexes = computeColumnsIndexes();
    },
    
    // 行高设置
    setRowsHeights(heights) {
        let acc = 0;
        current.summedRowsHeights = heights.map((h) => (acc += h));
        delete current.rowsIndexes;
        current.rowsIndexes = computeRowsIndexes();
    },
};
```

**尺寸管理特点**：
- **累积计算**: 将尺寸转换为累积值便于查找
- **立即更新**: 设置尺寸后立即重新计算索引
- **状态清理**: 删除旧索引确保重新计算
- **函数式**: 提供纯函数式的设置接口

## 🎨 实际应用场景

### 1. 大数据表格系统
```javascript
class VirtualDataTable extends Component {
    static template = xml`
        <div class="virtual-table-container">
            <div class="table-header">
                <div class="header-row">
                    <t t-foreach="columns" t-as="column" t-key="column.id">
                        <div class="header-cell" 
                             t-att-style="getColumnStyle(column_index)">
                            <span t-esc="column.title"/>
                            <button t-if="column.sortable" 
                                    t-on-click="() => this.sort(column.field)"
                                    class="sort-btn">
                                <i t-att-class="getSortIcon(column.field)"/>
                            </button>
                        </div>
                    </t>
                </div>
            </div>
            
            <div class="table-body" t-ref="scrollable">
                <div class="virtual-content" t-att-style="getVirtualContentStyle()">
                    <t t-foreach="visibleRows" t-as="row" t-key="row.id">
                        <div class="table-row" t-att-style="getRowStyle(row)">
                            <t t-foreach="visibleColumns" t-as="column" t-key="column.id">
                                <div class="table-cell" 
                                     t-att-style="getCellStyle(row, column)">
                                    <t t-component="getCellComponent(column)" 
                                       t-props="getCellProps(row, column)"/>
                                </div>
                            </t>
                        </div>
                    </t>
                </div>
            </div>
            
            <div class="table-footer" t-if="showFooter">
                <div class="pagination-info">
                    Showing <span t-esc="visibleRowsCount"/> of <span t-esc="totalRows"/> rows
                </div>
                <div class="performance-info" t-if="env.debug">
                    Rendered: <span t-esc="renderedCellsCount"/> cells
                </div>
            </div>
        </div>
    `;
    
    static props = {
        data: Array,
        columns: Array,
        rowHeight: { type: Number, optional: true },
        columnWidth: { type: Number, optional: true },
        showFooter: { type: Boolean, optional: true },
        onSort: { type: Function, optional: true },
        onCellClick: { type: Function, optional: true }
    };
    
    setup() {
        this.scrollableRef = useRef("scrollable");
        
        // 虚拟网格配置
        this.virtualGrid = useVirtualGrid({
            scrollableRef: this.scrollableRef,
            initialScroll: { left: 0, top: 0 },
            bufferCoef: 0.5, // 较小的缓冲区以优化性能
            onChange: (changed) => {
                this.onVirtualGridChange(changed);
            }
        });
        
        // 排序状态
        this.sortState = useState({
            field: null,
            direction: 'asc'
        });
        
        // 性能监控
        this.performanceMetrics = useState({
            renderedCells: 0,
            lastRenderTime: 0
        });
        
        onMounted(() => {
            this.initializeGrid();
        });
        
        onWillUpdateProps((nextProps) => {
            if (nextProps.data !== this.props.data || 
                nextProps.columns !== this.props.columns) {
                this.initializeGrid();
            }
        });
    }
    
    initializeGrid() {
        // 计算列宽
        const columnWidths = this.props.columns.map(col => 
            col.width || this.props.columnWidth || 150
        );
        
        // 计算行高
        const rowHeights = this.props.data.map(() => 
            this.props.rowHeight || 40
        );
        
        // 设置虚拟网格尺寸
        this.virtualGrid.setColumnsWidths(columnWidths);
        this.virtualGrid.setRowsHeights(rowHeights);
    }
    
    onVirtualGridChange(changed) {
        const startTime = performance.now();
        
        // 更新性能指标
        this.performanceMetrics.renderedCells = this.renderedCellsCount;
        this.performanceMetrics.lastRenderTime = performance.now() - startTime;
        
        // 触发重新渲染
        this.render();
    }
    
    get visibleRows() {
        const [startRow, endRow] = this.virtualGrid.rowsIndexes || [0, -1];
        return this.props.data.slice(startRow, endRow + 1).map((row, index) => ({
            ...row,
            virtualIndex: startRow + index,
            originalIndex: startRow + index
        }));
    }
    
    get visibleColumns() {
        const [startCol, endCol] = this.virtualGrid.columnsIndexes || [0, -1];
        return this.props.columns.slice(startCol, endCol + 1).map((col, index) => ({
            ...col,
            virtualIndex: startCol + index,
            originalIndex: startCol + index
        }));
    }
    
    get visibleRowsCount() {
        return this.visibleRows.length;
    }
    
    get renderedCellsCount() {
        return this.visibleRows.length * this.visibleColumns.length;
    }
    
    get totalRows() {
        return this.props.data.length;
    }
    
    getVirtualContentStyle() {
        const totalWidth = this.props.columns.reduce((sum, col) => 
            sum + (col.width || this.props.columnWidth || 150), 0);
        const totalHeight = this.props.data.length * (this.props.rowHeight || 40);
        
        return `width: ${totalWidth}px; height: ${totalHeight}px; position: relative;`;
    }
    
    getRowStyle(row) {
        const top = row.virtualIndex * (this.props.rowHeight || 40);
        return `position: absolute; top: ${top}px; left: 0; width: 100%;`;
    }
    
    getColumnStyle(columnIndex) {
        const width = this.props.columns[columnIndex].width || 
                     this.props.columnWidth || 150;
        return `width: ${width}px; min-width: ${width}px;`;
    }
    
    getCellStyle(row, column) {
        const left = this.props.columns.slice(0, column.originalIndex)
            .reduce((sum, col) => sum + (col.width || this.props.columnWidth || 150), 0);
        const width = column.width || this.props.columnWidth || 150;
        const height = this.props.rowHeight || 40;
        
        return `position: absolute; left: ${left}px; width: ${width}px; height: ${height}px;`;
    }
    
    getCellComponent(column) {
        return column.component || 'DefaultCell';
    }
    
    getCellProps(row, column) {
        return {
            value: row[column.field],
            row: row,
            column: column,
            onClick: () => this.props.onCellClick?.(row, column)
        };
    }
    
    sort(field) {
        const currentDirection = this.sortState.field === field ? 
            this.sortState.direction : 'asc';
        const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
        
        this.sortState.field = field;
        this.sortState.direction = newDirection;
        
        this.props.onSort?.(field, newDirection);
    }
    
    getSortIcon(field) {
        if (this.sortState.field !== field) {
            return 'fa fa-sort';
        }
        return this.sortState.direction === 'asc' ? 
            'fa fa-sort-up' : 'fa fa-sort-down';
    }
}

// 默认单元格组件
class DefaultCell extends Component {
    static template = xml`
        <div class="default-cell" t-on-click="onClick">
            <span t-esc="props.value"/>
        </div>
    `;
    
    static props = {
        value: true,
        row: Object,
        column: Object,
        onClick: { type: Function, optional: true }
    };
    
    onClick() {
        this.props.onClick?.();
    }
}

// 使用示例
const tableData = Array.from({ length: 100000 }, (_, i) => ({
    id: i,
    name: `User ${i}`,
    email: `user${i}@example.com`,
    age: 20 + (i % 50),
    department: ['Engineering', 'Sales', 'Marketing', 'HR'][i % 4],
    salary: 50000 + (i % 100) * 1000
}));

const tableColumns = [
    { id: 'name', field: 'name', title: 'Name', width: 200, sortable: true },
    { id: 'email', field: 'email', title: 'Email', width: 250, sortable: true },
    { id: 'age', field: 'age', title: 'Age', width: 100, sortable: true },
    { id: 'department', field: 'department', title: 'Department', width: 150, sortable: true },
    { id: 'salary', field: 'salary', title: 'Salary', width: 120, sortable: true }
];

// 在父组件中使用
class DataTableDemo extends Component {
    static template = xml`
        <div class="demo-container">
            <h2>Virtual Data Table Demo</h2>
            <VirtualDataTable 
                t-props="{
                    data: tableData,
                    columns: tableColumns,
                    rowHeight: 50,
                    showFooter: true,
                    onSort: this.handleSort,
                    onCellClick: this.handleCellClick
                }"/>
        </div>
    `;
    
    static components = { VirtualDataTable };
    
    setup() {
        this.tableData = tableData;
        this.tableColumns = tableColumns;
    }
    
    handleSort(field, direction) {
        console.log(`Sorting by ${field} ${direction}`);
        // 实现排序逻辑
    }
    
    handleCellClick(row, column) {
        console.log(`Clicked cell: ${column.field} = ${row[column.field]}`);
    }
}
```

### 2. 虚拟图片画廊
```javascript
class VirtualImageGallery extends Component {
    static template = xml`
        <div class="virtual-gallery">
            <div class="gallery-header">
                <h3>Image Gallery (<span t-esc="totalImages"/> images)</h3>
                <div class="gallery-controls">
                    <select t-model="itemsPerRow" t-on-change="updateLayout">
                        <option value="3">3 per row</option>
                        <option value="4">4 per row</option>
                        <option value="5">5 per row</option>
                        <option value="6">6 per row</option>
                    </select>
                    <button t-on-click="toggleViewMode" class="btn btn-sm">
                        <i t-att-class="viewModeIcon"/>
                    </button>
                </div>
            </div>

            <div class="gallery-container" t-ref="scrollable">
                <div class="virtual-content" t-att-style="getContentStyle()">
                    <t t-foreach="visibleImages" t-as="image" t-key="image.id">
                        <div class="image-item" t-att-style="getImageStyle(image)">
                            <div class="image-wrapper">
                                <img t-att-src="image.thumbnail"
                                     t-att-alt="image.title"
                                     t-on-click="() => this.openImage(image)"
                                     class="gallery-image"/>
                                <div class="image-overlay">
                                    <h5 t-esc="image.title"/>
                                    <p t-esc="image.description"/>
                                    <div class="image-actions">
                                        <button t-on-click="() => this.likeImage(image)"
                                                class="btn btn-sm btn-outline-light">
                                            <i class="fa fa-heart"/> <span t-esc="image.likes"/>
                                        </button>
                                        <button t-on-click="() => this.shareImage(image)"
                                                class="btn btn-sm btn-outline-light">
                                            <i class="fa fa-share"/>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </div>
            </div>

            <div class="gallery-footer">
                <div class="performance-stats" t-if="env.debug">
                    Rendered: <span t-esc="renderedImagesCount"/> / <span t-esc="totalImages"/> images
                </div>
            </div>
        </div>
    `;

    static props = {
        images: Array,
        itemsPerRow: { type: Number, optional: true },
        itemHeight: { type: Number, optional: true },
        onImageClick: { type: Function, optional: true },
        onImageLike: { type: Function, optional: true }
    };

    setup() {
        this.scrollableRef = useRef("scrollable");

        // 布局状态
        this.layoutState = useState({
            itemsPerRow: this.props.itemsPerRow || 4,
            itemHeight: this.props.itemHeight || 250,
            containerWidth: 0,
            viewMode: 'grid' // 'grid' | 'list'
        });

        // 虚拟网格配置
        this.virtualGrid = useVirtualGrid({
            scrollableRef: this.scrollableRef,
            bufferCoef: 0.8,
            onChange: () => this.render()
        });

        // 响应式布局
        this.resizeObserver = null;

        onMounted(() => {
            this.initializeLayout();
            this.setupResizeObserver();
        });

        onWillUnmount(() => {
            this.resizeObserver?.disconnect();
        });
    }

    initializeLayout() {
        const container = this.scrollableRef.el;
        if (container) {
            this.layoutState.containerWidth = container.clientWidth;
            this.updateVirtualGrid();
        }
    }

    setupResizeObserver() {
        if ('ResizeObserver' in window) {
            this.resizeObserver = new ResizeObserver((entries) => {
                for (const entry of entries) {
                    this.layoutState.containerWidth = entry.contentRect.width;
                    this.updateVirtualGrid();
                }
            });

            this.resizeObserver.observe(this.scrollableRef.el);
        }
    }

    updateVirtualGrid() {
        const itemWidth = this.layoutState.containerWidth / this.layoutState.itemsPerRow;
        const rowCount = Math.ceil(this.props.images.length / this.layoutState.itemsPerRow);

        // 设置列宽（每行的项目）
        const columnWidths = Array(this.layoutState.itemsPerRow).fill(itemWidth);
        this.virtualGrid.setColumnsWidths(columnWidths);

        // 设置行高
        const rowHeights = Array(rowCount).fill(this.layoutState.itemHeight);
        this.virtualGrid.setRowsHeights(rowHeights);
    }

    get visibleImages() {
        const [startRow, endRow] = this.virtualGrid.rowsIndexes || [0, -1];
        const [startCol, endCol] = this.virtualGrid.columnsIndexes || [0, -1];

        const visibleImages = [];

        for (let row = startRow; row <= endRow; row++) {
            for (let col = startCol; col <= endCol; col++) {
                const imageIndex = row * this.layoutState.itemsPerRow + col;
                if (imageIndex < this.props.images.length) {
                    const image = this.props.images[imageIndex];
                    visibleImages.push({
                        ...image,
                        row,
                        col,
                        imageIndex
                    });
                }
            }
        }

        return visibleImages;
    }

    get totalImages() {
        return this.props.images.length;
    }

    get renderedImagesCount() {
        return this.visibleImages.length;
    }

    get viewModeIcon() {
        return this.layoutState.viewMode === 'grid' ?
            'fa fa-list' : 'fa fa-th';
    }

    getContentStyle() {
        const rowCount = Math.ceil(this.props.images.length / this.layoutState.itemsPerRow);
        const totalHeight = rowCount * this.layoutState.itemHeight;

        return `height: ${totalHeight}px; position: relative;`;
    }

    getImageStyle(image) {
        const itemWidth = this.layoutState.containerWidth / this.layoutState.itemsPerRow;
        const left = image.col * itemWidth;
        const top = image.row * this.layoutState.itemHeight;

        return `
            position: absolute;
            left: ${left}px;
            top: ${top}px;
            width: ${itemWidth}px;
            height: ${this.layoutState.itemHeight}px;
            padding: 8px;
        `;
    }

    updateLayout() {
        this.updateVirtualGrid();
    }

    toggleViewMode() {
        this.layoutState.viewMode = this.layoutState.viewMode === 'grid' ? 'list' : 'grid';

        if (this.layoutState.viewMode === 'list') {
            this.layoutState.itemsPerRow = 1;
            this.layoutState.itemHeight = 120;
        } else {
            this.layoutState.itemsPerRow = 4;
            this.layoutState.itemHeight = 250;
        }

        this.updateVirtualGrid();
    }

    openImage(image) {
        this.props.onImageClick?.(image);
    }

    likeImage(image) {
        image.likes = (image.likes || 0) + 1;
        this.props.onImageLike?.(image);
    }

    shareImage(image) {
        if (navigator.share) {
            navigator.share({
                title: image.title,
                text: image.description,
                url: image.url
            });
        } else {
            // 降级处理
            navigator.clipboard.writeText(image.url);
            console.log('Image URL copied to clipboard');
        }
    }
}

// 使用示例
const galleryImages = Array.from({ length: 10000 }, (_, i) => ({
    id: i,
    title: `Image ${i + 1}`,
    description: `Description for image ${i + 1}`,
    thumbnail: `https://picsum.photos/300/200?random=${i}`,
    url: `https://picsum.photos/800/600?random=${i}`,
    likes: Math.floor(Math.random() * 100)
}));

class ImageGalleryDemo extends Component {
    static template = xml`
        <div class="demo-container">
            <VirtualImageGallery
                t-props="{
                    images: galleryImages,
                    onImageClick: this.handleImageClick,
                    onImageLike: this.handleImageLike
                }"/>
        </div>
    `;

    static components = { VirtualImageGallery };

    setup() {
        this.galleryImages = galleryImages;
    }

    handleImageClick(image) {
        console.log('Opening image:', image.title);
        // 实现图片查看器
    }

    handleImageLike(image) {
        console.log('Liked image:', image.title);
        // 实现点赞逻辑
    }
}
```

### 3. 虚拟网格性能监控器
```javascript
class VirtualGridPerformanceMonitor {
    constructor() {
        this.metrics = new Map();
        this.observers = new Set();
        this.isMonitoring = false;
        this.frameCount = 0;
        this.lastFrameTime = 0;
    }

    // 开始监控
    startMonitoring(gridId) {
        if (this.isMonitoring) return;

        this.isMonitoring = true;
        this.gridId = gridId;
        this.setupPerformanceObserver();
        this.startFrameMonitoring();

        console.log(`Virtual grid performance monitoring started for ${gridId}`);
    }

    // 停止监控
    stopMonitoring() {
        this.isMonitoring = false;

        for (const observer of this.observers) {
            observer.disconnect();
        }
        this.observers.clear();

        console.log('Virtual grid performance monitoring stopped');
    }

    // 设置性能观察器
    setupPerformanceObserver() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    this.recordPerformanceEntry(entry);
                }
            });

            observer.observe({ entryTypes: ['measure', 'paint', 'layout-shift'] });
            this.observers.add(observer);
        }
    }

    // 开始帧率监控
    startFrameMonitoring() {
        const measureFrame = (timestamp) => {
            if (this.lastFrameTime) {
                const frameDuration = timestamp - this.lastFrameTime;
                this.recordFrameMetric(frameDuration);
            }

            this.lastFrameTime = timestamp;
            this.frameCount++;

            if (this.isMonitoring) {
                requestAnimationFrame(measureFrame);
            }
        };

        requestAnimationFrame(measureFrame);
    }

    // 记录帧指标
    recordFrameMetric(duration) {
        if (!this.metrics.has('frames')) {
            this.metrics.set('frames', {
                durations: [],
                fps: 0,
                averageDuration: 0,
                maxDuration: 0,
                minDuration: Infinity
            });
        }

        const frameMetrics = this.metrics.get('frames');
        frameMetrics.durations.push(duration);

        // 保持最近100帧的数据
        if (frameMetrics.durations.length > 100) {
            frameMetrics.durations.shift();
        }

        // 计算统计信息
        const recentDurations = frameMetrics.durations;
        frameMetrics.averageDuration = recentDurations.reduce((sum, d) => sum + d, 0) / recentDurations.length;
        frameMetrics.fps = 1000 / frameMetrics.averageDuration;
        frameMetrics.maxDuration = Math.max(...recentDurations);
        frameMetrics.minDuration = Math.min(...recentDurations);
    }

    // 记录性能条目
    recordPerformanceEntry(entry) {
        const category = entry.entryType;

        if (!this.metrics.has(category)) {
            this.metrics.set(category, []);
        }

        this.metrics.get(category).push({
            name: entry.name,
            startTime: entry.startTime,
            duration: entry.duration,
            timestamp: Date.now()
        });
    }

    // 记录虚拟网格指标
    recordVirtualGridMetric(type, data) {
        const key = `virtual_grid_${type}`;

        if (!this.metrics.has(key)) {
            this.metrics.set(key, []);
        }

        this.metrics.get(key).push({
            ...data,
            timestamp: Date.now()
        });
    }

    // 包装虚拟网格钩子
    wrapVirtualGrid(originalUseVirtualGrid) {
        const monitor = this;

        return function(params) {
            const grid = originalUseVirtualGrid(params);

            // 包装onChange回调
            const originalOnChange = params.onChange || (() => {});
            params.onChange = function(changed) {
                const startTime = performance.now();

                // 记录变化指标
                monitor.recordVirtualGridMetric('change', {
                    changed,
                    columnsRange: grid.columnsIndexes,
                    rowsRange: grid.rowsIndexes
                });

                // 执行原始回调
                const result = originalOnChange.call(this, changed);

                // 记录执行时间
                const duration = performance.now() - startTime;
                monitor.recordVirtualGridMetric('render_time', {
                    duration,
                    changed
                });

                return result;
            };

            // 包装设置器
            const originalSetColumnsWidths = grid.setColumnsWidths;
            grid.setColumnsWidths = function(widths) {
                const startTime = performance.now();
                const result = originalSetColumnsWidths.call(this, widths);
                const duration = performance.now() - startTime;

                monitor.recordVirtualGridMetric('columns_update', {
                    duration,
                    columnCount: widths.length,
                    totalWidth: widths.reduce((sum, w) => sum + w, 0)
                });

                return result;
            };

            const originalSetRowsHeights = grid.setRowsHeights;
            grid.setRowsHeights = function(heights) {
                const startTime = performance.now();
                const result = originalSetRowsHeights.call(this, heights);
                const duration = performance.now() - startTime;

                monitor.recordVirtualGridMetric('rows_update', {
                    duration,
                    rowCount: heights.length,
                    totalHeight: heights.reduce((sum, h) => sum + h, 0)
                });

                return result;
            };

            return grid;
        };
    }

    // 生成性能报告
    generateReport() {
        const report = {
            gridId: this.gridId,
            timestamp: Date.now(),
            frameMetrics: this.metrics.get('frames'),
            virtualGridMetrics: {},
            recommendations: []
        };

        // 收集虚拟网格指标
        for (const [key, value] of this.metrics.entries()) {
            if (key.startsWith('virtual_grid_')) {
                report.virtualGridMetrics[key] = value;
            }
        }

        // 生成建议
        this.generateRecommendations(report);

        return report;
    }

    generateRecommendations(report) {
        const frameMetrics = report.frameMetrics;

        // 检查帧率
        if (frameMetrics && frameMetrics.fps < 30) {
            report.recommendations.push({
                type: 'performance',
                severity: 'high',
                message: `Low FPS detected: ${frameMetrics.fps.toFixed(1)}. Consider reducing buffer coefficient or optimizing render logic.`
            });
        }

        // 检查渲染时间
        const renderTimes = report.virtualGridMetrics.virtual_grid_render_time || [];
        const avgRenderTime = renderTimes.reduce((sum, entry) => sum + entry.duration, 0) / renderTimes.length;

        if (avgRenderTime > 16) { // 16ms = 60fps
            report.recommendations.push({
                type: 'render_optimization',
                severity: 'medium',
                message: `Average render time is ${avgRenderTime.toFixed(2)}ms. Consider optimizing component rendering.`
            });
        }

        // 检查频繁更新
        const changes = report.virtualGridMetrics.virtual_grid_change || [];
        const recentChanges = changes.filter(c => Date.now() - c.timestamp < 5000); // 最近5秒

        if (recentChanges.length > 50) {
            report.recommendations.push({
                type: 'update_frequency',
                severity: 'medium',
                message: `High update frequency detected: ${recentChanges.length} changes in 5 seconds. Consider throttling scroll events.`
            });
        }
    }

    // 导出数据
    exportData() {
        return {
            gridId: this.gridId,
            metrics: Object.fromEntries(this.metrics.entries()),
            report: this.generateReport(),
            frameCount: this.frameCount
        };
    }

    // 清理旧数据
    cleanup() {
        const maxAge = 5 * 60 * 1000; // 5分钟
        const now = Date.now();

        for (const [key, value] of this.metrics.entries()) {
            if (Array.isArray(value)) {
                this.metrics.set(key, value.filter(entry =>
                    now - entry.timestamp < maxAge
                ));
            }
        }
    }
}

// 使用示例
const performanceMonitor = new VirtualGridPerformanceMonitor();

// 包装useVirtualGrid
const originalUseVirtualGrid = useVirtualGrid;
const monitoredUseVirtualGrid = performanceMonitor.wrapVirtualGrid(originalUseVirtualGrid);

// 在组件中使用监控版本
class MonitoredVirtualGrid extends Component {
    setup() {
        // 使用监控版本的虚拟网格
        this.virtualGrid = monitoredUseVirtualGrid({
            scrollableRef: this.scrollableRef,
            bufferCoef: 1,
            onChange: (changed) => {
                console.log('Grid changed:', changed);
                this.render();
            }
        });

        onMounted(() => {
            performanceMonitor.startMonitoring('main_grid');
        });

        onWillUnmount(() => {
            performanceMonitor.stopMonitoring();
        });
    }
}

// 定期生成报告
setInterval(() => {
    const report = performanceMonitor.generateReport();
    console.log('Virtual Grid Performance Report:', report);

    // 清理旧数据
    performanceMonitor.cleanup();
}, 30000); // 每30秒
```

## 🛠️ 实践练习

### 练习1: 自适应虚拟网格
```javascript
class AdaptiveVirtualGrid {
    constructor(options = {}) {
        this.options = {
            minItemSize: 100,
            maxItemSize: 300,
            aspectRatio: 1,
            gap: 10,
            ...options
        };

        this.state = {
            containerSize: { width: 0, height: 0 },
            itemSize: { width: 0, height: 0 },
            itemsPerRow: 0,
            totalRows: 0
        };

        this.resizeObserver = null;
    }

    // 初始化自适应网格
    initialize(container, itemCount) {
        this.container = container;
        this.itemCount = itemCount;

        this.updateContainerSize();
        this.calculateLayout();
        this.setupResizeObserver();
    }

    // 更新容器尺寸
    updateContainerSize() {
        if (!this.container) return;

        const rect = this.container.getBoundingClientRect();
        this.state.containerSize = {
            width: rect.width,
            height: rect.height
        };
    }

    // 计算布局
    calculateLayout() {
        const { width } = this.state.containerSize;
        const { minItemSize, maxItemSize, aspectRatio, gap } = this.options;

        // 计算每行项目数
        let itemsPerRow = Math.floor((width + gap) / (minItemSize + gap));

        // 计算实际项目宽度
        let itemWidth = (width - (itemsPerRow - 1) * gap) / itemsPerRow;

        // 确保不超过最大尺寸
        if (itemWidth > maxItemSize) {
            itemWidth = maxItemSize;
            itemsPerRow = Math.floor((width + gap) / (itemWidth + gap));
        }

        // 计算项目高度
        const itemHeight = itemWidth / aspectRatio;

        // 计算总行数
        const totalRows = Math.ceil(this.itemCount / itemsPerRow);

        this.state.itemSize = { width: itemWidth, height: itemHeight };
        this.state.itemsPerRow = itemsPerRow;
        this.state.totalRows = totalRows;
    }

    // 设置响应式观察器
    setupResizeObserver() {
        if ('ResizeObserver' in window && !this.resizeObserver) {
            this.resizeObserver = new ResizeObserver(() => {
                this.updateContainerSize();
                this.calculateLayout();
                this.onLayoutChange?.();
            });

            this.resizeObserver.observe(this.container);
        }
    }

    // 获取项目位置
    getItemPosition(index) {
        const row = Math.floor(index / this.state.itemsPerRow);
        const col = index % this.state.itemsPerRow;

        const x = col * (this.state.itemSize.width + this.options.gap);
        const y = row * (this.state.itemSize.height + this.options.gap);

        return { x, y, row, col };
    }

    // 获取可见项目范围
    getVisibleRange(scrollTop, viewportHeight, bufferCoef = 1) {
        const itemHeight = this.state.itemSize.height + this.options.gap;
        const buffer = viewportHeight * bufferCoef;

        const startRow = Math.max(0, Math.floor((scrollTop - buffer) / itemHeight));
        const endRow = Math.min(
            this.state.totalRows - 1,
            Math.ceil((scrollTop + viewportHeight + buffer) / itemHeight)
        );

        const startIndex = startRow * this.state.itemsPerRow;
        const endIndex = Math.min(
            this.itemCount - 1,
            (endRow + 1) * this.state.itemsPerRow - 1
        );

        return { startIndex, endIndex, startRow, endRow };
    }

    // 获取总内容尺寸
    getContentSize() {
        return {
            width: this.state.containerSize.width,
            height: this.state.totalRows * (this.state.itemSize.height + this.options.gap) - this.options.gap
        };
    }

    // 销毁
    destroy() {
        this.resizeObserver?.disconnect();
        this.resizeObserver = null;
    }
}

// 使用自适应网格的组件
class AdaptiveVirtualGridComponent extends Component {
    static template = xml`
        <div class="adaptive-grid-container" t-ref="container">
            <div class="grid-content" t-att-style="getContentStyle()">
                <t t-foreach="visibleItems" t-as="item" t-key="item.id">
                    <div class="grid-item" t-att-style="getItemStyle(item)">
                        <div class="item-content">
                            <img t-if="item.image" t-att-src="item.image" alt=""/>
                            <h4 t-esc="item.title"/>
                            <p t-esc="item.description"/>
                        </div>
                    </div>
                </t>
            </div>
        </div>
    `;

    static props = {
        items: Array,
        minItemSize: { type: Number, optional: true },
        maxItemSize: { type: Number, optional: true },
        aspectRatio: { type: Number, optional: true }
    };

    setup() {
        this.containerRef = useRef("container");
        this.state = useState({
            visibleItems: [],
            scrollTop: 0
        });

        // 创建自适应网格
        this.adaptiveGrid = new AdaptiveVirtualGrid({
            minItemSize: this.props.minItemSize || 200,
            maxItemSize: this.props.maxItemSize || 400,
            aspectRatio: this.props.aspectRatio || 1.2,
            gap: 16
        });

        // 设置布局变化回调
        this.adaptiveGrid.onLayoutChange = () => {
            this.updateVisibleItems();
        };

        onMounted(() => {
            this.initializeGrid();
            this.setupScrollListener();
        });

        onWillUnmount(() => {
            this.adaptiveGrid.destroy();
        });
    }

    initializeGrid() {
        this.adaptiveGrid.initialize(this.containerRef.el, this.props.items.length);
        this.updateVisibleItems();
    }

    setupScrollListener() {
        const container = this.containerRef.el;

        const scrollListener = () => {
            this.state.scrollTop = container.scrollTop;
            this.updateVisibleItems();
        };

        container.addEventListener('scroll', scrollListener);

        onWillUnmount(() => {
            container.removeEventListener('scroll', scrollListener);
        });
    }

    updateVisibleItems() {
        const container = this.containerRef.el;
        if (!container) return;

        const viewportHeight = container.clientHeight;
        const { startIndex, endIndex } = this.adaptiveGrid.getVisibleRange(
            this.state.scrollTop,
            viewportHeight,
            0.5
        );

        this.state.visibleItems = this.props.items
            .slice(startIndex, endIndex + 1)
            .map((item, index) => ({
                ...item,
                index: startIndex + index,
                position: this.adaptiveGrid.getItemPosition(startIndex + index)
            }));
    }

    getContentStyle() {
        const contentSize = this.adaptiveGrid.getContentSize();
        return `height: ${contentSize.height}px; position: relative;`;
    }

    getItemStyle(item) {
        const { x, y } = item.position;
        const { width, height } = this.adaptiveGrid.state.itemSize;

        return `
            position: absolute;
            left: ${x}px;
            top: ${y}px;
            width: ${width}px;
            height: ${height}px;
        `;
    }
}
```

### 练习2: 虚拟网格缓存管理器
```javascript
class VirtualGridCacheManager {
    constructor(options = {}) {
        this.options = {
            maxCacheSize: 1000,
            cacheTimeout: 5 * 60 * 1000, // 5分钟
            preloadDistance: 2,
            ...options
        };

        this.cache = new Map();
        this.loadingItems = new Set();
        this.accessTimes = new Map();
        this.cleanupTimer = null;

        this.startCleanupTimer();
    }

    // 获取项目数据
    async getItem(index, loader) {
        const cacheKey = `item_${index}`;

        // 更新访问时间
        this.accessTimes.set(cacheKey, Date.now());

        // 检查缓存
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        // 检查是否正在加载
        if (this.loadingItems.has(index)) {
            return this.waitForLoading(index);
        }

        // 开始加载
        return this.loadItem(index, loader);
    }

    // 加载项目
    async loadItem(index, loader) {
        const cacheKey = `item_${index}`;
        this.loadingItems.add(index);

        try {
            const item = await loader(index);

            // 存储到缓存
            this.cache.set(cacheKey, item);
            this.accessTimes.set(cacheKey, Date.now());

            // 检查缓存大小
            this.enforceMaxCacheSize();

            return item;
        } catch (error) {
            console.error(`Failed to load item ${index}:`, error);
            throw error;
        } finally {
            this.loadingItems.delete(index);
        }
    }

    // 等待加载完成
    async waitForLoading(index) {
        return new Promise((resolve, reject) => {
            const checkLoading = () => {
                if (!this.loadingItems.has(index)) {
                    const cacheKey = `item_${index}`;
                    if (this.cache.has(cacheKey)) {
                        resolve(this.cache.get(cacheKey));
                    } else {
                        reject(new Error(`Item ${index} failed to load`));
                    }
                } else {
                    setTimeout(checkLoading, 10);
                }
            };

            checkLoading();
        });
    }

    // 预加载项目
    async preloadItems(visibleRange, loader) {
        const { startIndex, endIndex } = visibleRange;
        const preloadDistance = this.options.preloadDistance;

        const preloadStart = Math.max(0, startIndex - preloadDistance);
        const preloadEnd = endIndex + preloadDistance;

        const preloadPromises = [];

        for (let i = preloadStart; i <= preloadEnd; i++) {
            const cacheKey = `item_${i}`;

            if (!this.cache.has(cacheKey) && !this.loadingItems.has(i)) {
                preloadPromises.push(
                    this.loadItem(i, loader).catch(error => {
                        console.warn(`Preload failed for item ${i}:`, error);
                    })
                );
            }
        }

        await Promise.all(preloadPromises);
    }

    // 强制执行最大缓存大小
    enforceMaxCacheSize() {
        if (this.cache.size <= this.options.maxCacheSize) {
            return;
        }

        // 按访问时间排序
        const sortedEntries = Array.from(this.accessTimes.entries())
            .sort((a, b) => a[1] - b[1]);

        // 删除最旧的项目
        const itemsToRemove = this.cache.size - this.options.maxCacheSize;

        for (let i = 0; i < itemsToRemove; i++) {
            const [cacheKey] = sortedEntries[i];
            this.cache.delete(cacheKey);
            this.accessTimes.delete(cacheKey);
        }
    }

    // 清理过期项目
    cleanupExpiredItems() {
        const now = Date.now();
        const timeout = this.options.cacheTimeout;

        for (const [cacheKey, accessTime] of this.accessTimes.entries()) {
            if (now - accessTime > timeout) {
                this.cache.delete(cacheKey);
                this.accessTimes.delete(cacheKey);
            }
        }
    }

    // 开始清理定时器
    startCleanupTimer() {
        this.cleanupTimer = setInterval(() => {
            this.cleanupExpiredItems();
        }, 60000); // 每分钟清理一次
    }

    // 获取缓存统计
    getStats() {
        return {
            cacheSize: this.cache.size,
            loadingCount: this.loadingItems.size,
            maxCacheSize: this.options.maxCacheSize,
            hitRate: this.calculateHitRate(),
            memoryUsage: this.estimateMemoryUsage()
        };
    }

    calculateHitRate() {
        // 简化的命中率计算
        const totalAccess = this.accessTimes.size;
        const cacheHits = this.cache.size;

        return totalAccess > 0 ? (cacheHits / totalAccess * 100).toFixed(2) : 0;
    }

    estimateMemoryUsage() {
        // 简化的内存使用估算
        let totalSize = 0;

        for (const item of this.cache.values()) {
            totalSize += JSON.stringify(item).length * 2; // 假设每字符2字节
        }

        return totalSize;
    }

    // 清空缓存
    clear() {
        this.cache.clear();
        this.accessTimes.clear();
        this.loadingItems.clear();
    }

    // 销毁管理器
    destroy() {
        this.clear();

        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
            this.cleanupTimer = null;
        }
    }
}

// 使用缓存管理器的虚拟网格组件
class CachedVirtualGrid extends Component {
    static template = xml`
        <div class="cached-grid-container" t-ref="scrollable">
            <div class="grid-content" t-att-style="getContentStyle()">
                <t t-foreach="visibleItems" t-as="item" t-key="item.index">
                    <div class="grid-item" t-att-style="getItemStyle(item)">
                        <div t-if="item.loading" class="loading-placeholder">
                            <div class="spinner"/>
                            <span>Loading...</span>
                        </div>
                        <div t-else="" class="item-content">
                            <img t-if="item.data.image" t-att-src="item.data.image" alt=""/>
                            <h4 t-esc="item.data.title"/>
                            <p t-esc="item.data.description"/>
                        </div>
                    </div>
                </t>
            </div>

            <div class="cache-stats" t-if="env.debug">
                <div>Cache: <span t-esc="cacheStats.cacheSize"/>/<span t-esc="cacheStats.maxCacheSize"/></div>
                <div>Hit Rate: <span t-esc="cacheStats.hitRate"/>%</div>
                <div>Loading: <span t-esc="cacheStats.loadingCount"/></div>
            </div>
        </div>
    `;

    static props = {
        itemCount: Number,
        itemLoader: Function,
        itemHeight: { type: Number, optional: true },
        cacheOptions: { type: Object, optional: true }
    };

    setup() {
        this.scrollableRef = useRef("scrollable");

        // 创建缓存管理器
        this.cacheManager = new VirtualGridCacheManager(this.props.cacheOptions);

        // 虚拟网格
        this.virtualGrid = useVirtualGrid({
            scrollableRef: this.scrollableRef,
            bufferCoef: 0.5,
            onChange: () => this.updateVisibleItems()
        });

        // 状态
        this.state = useState({
            visibleItems: [],
            cacheStats: this.cacheManager.getStats()
        });

        onMounted(() => {
            this.initializeGrid();
            this.startStatsUpdate();
        });

        onWillUnmount(() => {
            this.cacheManager.destroy();
        });
    }

    initializeGrid() {
        const itemHeight = this.props.itemHeight || 200;
        const rowHeights = Array(this.props.itemCount).fill(itemHeight);

        this.virtualGrid.setRowsHeights(rowHeights);
        this.virtualGrid.setColumnsWidths([window.innerWidth]);

        this.updateVisibleItems();
    }

    async updateVisibleItems() {
        const [startRow, endRow] = this.virtualGrid.rowsIndexes || [0, -1];

        const visibleItems = [];

        for (let i = startRow; i <= endRow; i++) {
            visibleItems.push({
                index: i,
                loading: true,
                data: null
            });
        }

        this.state.visibleItems = visibleItems;

        // 异步加载数据
        this.loadVisibleItemsData(startRow, endRow);

        // 预加载
        this.cacheManager.preloadItems(
            { startIndex: startRow, endIndex: endRow },
            this.props.itemLoader
        );
    }

    async loadVisibleItemsData(startRow, endRow) {
        const loadPromises = [];

        for (let i = startRow; i <= endRow; i++) {
            loadPromises.push(
                this.cacheManager.getItem(i, this.props.itemLoader)
                    .then(data => ({ index: i, data }))
                    .catch(error => ({ index: i, error }))
            );
        }

        const results = await Promise.all(loadPromises);

        // 更新可见项目数据
        for (const result of results) {
            const itemIndex = this.state.visibleItems.findIndex(
                item => item.index === result.index
            );

            if (itemIndex >= 0) {
                if (result.error) {
                    this.state.visibleItems[itemIndex] = {
                        ...this.state.visibleItems[itemIndex],
                        loading: false,
                        error: result.error
                    };
                } else {
                    this.state.visibleItems[itemIndex] = {
                        ...this.state.visibleItems[itemIndex],
                        loading: false,
                        data: result.data
                    };
                }
            }
        }
    }

    startStatsUpdate() {
        const updateStats = () => {
            this.state.cacheStats = this.cacheManager.getStats();
        };

        setInterval(updateStats, 1000);
        updateStats();
    }

    getContentStyle() {
        const totalHeight = this.props.itemCount * (this.props.itemHeight || 200);
        return `height: ${totalHeight}px; position: relative;`;
    }

    getItemStyle(item) {
        const height = this.props.itemHeight || 200;
        const top = item.index * height;

        return `
            position: absolute;
            top: ${top}px;
            left: 0;
            width: 100%;
            height: ${height}px;
        `;
    }
}
```

## 🔧 调试技巧

### 查看虚拟网格状态
```javascript
function debugVirtualGrid(virtualGrid) {
    console.group('Virtual Grid Debug');
    console.log('Columns Indexes:', virtualGrid.columnsIndexes);
    console.log('Rows Indexes:', virtualGrid.rowsIndexes);
    console.groupEnd();
}

// 在组件中使用
setup() {
    this.virtualGrid = useVirtualGrid({...});

    // 调试状态变化
    useEffect(() => {
        debugVirtualGrid(this.virtualGrid);
    });
}
```

### 性能分析工具
```javascript
function analyzeVirtualGridPerformance(component) {
    const startTime = performance.now();

    // 测量渲染时间
    const originalRender = component.render;
    component.render = function() {
        const renderStart = performance.now();
        const result = originalRender.call(this);
        const renderTime = performance.now() - renderStart;

        console.log(`Render time: ${renderTime.toFixed(2)}ms`);
        return result;
    };

    // 测量总时间
    const totalTime = performance.now() - startTime;
    console.log(`Setup time: ${totalTime.toFixed(2)}ms`);
}
```

## 📊 性能考虑

### 优化策略
1. **缓冲区调优**: 根据内容复杂度调整bufferCoef
2. **节流处理**: 使用useThrottleForAnimation避免过度计算
3. **内存管理**: 及时清理不可见项目的资源
4. **预加载**: 智能预加载提升用户体验

### 最佳实践
```javascript
// ✅ 好的做法：合理的缓冲区
useVirtualGrid({
    bufferCoef: 0.5, // 适中的缓冲区
    onChange: throttledOnChange
});

// ❌ 不好的做法：过大的缓冲区
useVirtualGrid({
    bufferCoef: 3, // 过大，浪费内存
    onChange: onChange
});

// ✅ 好的做法：优化的渲染逻辑
get visibleItems() {
    return this.items.slice(startIndex, endIndex + 1);
}

// ❌ 不好的做法：复杂的渲染逻辑
get visibleItems() {
    return this.items.filter(item => /* 复杂条件 */);
}
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解虚拟滚动的核心原理和实现机制
- [ ] 掌握大数据集的高性能渲染策略
- [ ] 理解缓冲区算法和性能优化技术
- [ ] 能够创建复杂的虚拟网格系统
- [ ] 掌握动态尺寸计算和索引管理
- [ ] 了解企业级数据表格的架构设计

## 🚀 下一步学习
学完Virtual Grid Hook后，建议继续学习：
1. **列表组件** (`@web/core/list/`) - 学习列表组件的实现
2. **表格组件** (`@web/views/list/`) - 深入理解表格视图
3. **性能优化** (`@web/core/utils/timing.js`) - 掌握性能优化技术

## 💡 重要提示
- 虚拟网格是处理大数据集的关键技术
- 理解索引计算对性能优化很重要
- 缓冲区策略需要在性能和体验间平衡
- 内存管理对长时间运行的应用至关重要
```
```

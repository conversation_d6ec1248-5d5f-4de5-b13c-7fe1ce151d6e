# @web/core/commands/command_palette.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/commands/command_palette.js`
- **原始路径**: `/web/static/src/core/commands/command_palette.js`
- **代码行数**: 402行
- **作用**: 实现命令面板UI组件，提供快速命令搜索和执行界面

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 命令面板的UI架构和交互设计
- 模糊搜索和实时过滤算法
- 键盘导航和快捷键系统
- 命名空间和提供者模式
- 企业级搜索界面的实现技术

## 📚 核心概念

### 什么是命令面板？
命令面板是一个**快速访问界面**，主要功能：
- **快速搜索**: 通过输入快速查找和执行命令
- **键盘导航**: 支持完整的键盘操作
- **分类显示**: 按类别组织和显示命令
- **实时过滤**: 实时搜索和过滤命令结果

### 核心架构组成
```javascript
// 主要组件
const CommandPalette = {
    // UI组件
    Dialog,                    // 对话框容器
    DefaultCommandItem,        // 默认命令项组件
    
    // 核心功能
    search(),                  // 搜索功能
    setCommands(),            // 设置命令列表
    executeCommand(),         // 执行命令
    
    // 导航功能
    selectCommand(),          // 选择命令
    selectCommandAndScrollTo(), // 选择并滚动到命令
    
    // 命名空间
    switchNamespace(),        // 切换命名空间
    processSearchValue()      // 处理搜索值
};

// 配置结构
const CommandPaletteConfig = {
    providers: [],            // 命令提供者
    configByNamespace: {},    // 命名空间配置
    FooterComponent: null,    // 底部组件
    searchValue: ""          // 初始搜索值
};
```

### 基本使用模式
```javascript
import { CommandPalette } from '@web/core/commands/command_palette';

// 配置命令面板
const config = {
    providers: [
        {
            namespace: "default",
            provide: () => [
                {
                    name: "Create Record",
                    action: () => this.createRecord(),
                    category: "app"
                },
                {
                    name: "Export Data",
                    action: () => this.exportData(),
                    category: "actions"
                }
            ]
        }
    ],
    configByNamespace: {
        default: {
            placeholder: "Search commands...",
            emptyMessage: "No commands found",
            categories: ["app", "actions", "default"]
        }
    }
};

// 使用命令面板
<CommandPalette config={config} close={() => this.closeDialog()} />
```

## 🔍 核心实现详解

### 1. 组件初始化和状态管理

#### 组件设置 (setup)
```javascript
setup() {
    // 事件总线监听
    if (this.props.bus) {
        const setConfig = ({ detail }) => this.setCommandPaletteConfig(detail);
        this.props.bus.addEventListener(`SET-CONFIG`, setConfig);
        onWillDestroy(() => this.props.bus.removeEventListener(`SET-CONFIG`, setConfig));
    }
    
    // 核心状态初始化
    this.keyId = 1;
    this.race = new Race();
    this.keepLast = new KeepLast();
    this._sessionId = CommandPalette.lastSessionId++;
    
    // 服务和引用
    this.activeElement = useService("ui").activeElement;
    this.inputRef = useAutofocus();
    
    // 快捷键绑定
    useHotkey("Enter", () => this.executeSelectedCommand(), { bypassEditableProtection: true });
    useHotkey("ArrowUp", () => this.selectCommandAndScrollTo("PREV"), { allowRepeat: true });
    useHotkey("ArrowDown", () => this.selectCommandAndScrollTo("NEXT"), { allowRepeat: true });
}
```

**初始化策略**：
- **并发控制**: 使用Race和KeepLast管理异步操作
- **会话管理**: 每个面板实例有唯一的sessionId
- **快捷键**: 绑定Enter、方向键等核心快捷键
- **自动聚焦**: 使用useAutofocus自动聚焦输入框

#### 状态结构设计
```javascript
this.state = useState({
    commands: [],           // 当前显示的命令列表
    emptyMessage: "",       // 空结果消息
    FooterComponent: null,  // 底部组件
    namespace: "default",   // 当前命名空间
    placeholder: "",        // 输入框占位符
    searchValue: "",        // 搜索值
    selectedCommand: null,  // 当前选中的命令
    isLoading: false       // 加载状态
});
```

**状态设计原则**：
- **最小状态**: 只存储必要的UI状态
- **响应式**: 使用useState确保状态变化触发重渲染
- **类型安全**: 明确的状态结构和类型定义
- **性能优化**: 使用markRaw避免不必要的响应式包装

### 2. 搜索和过滤算法

#### 模糊搜索实现
```javascript
async setCommands(namespace, options = {}) {
    // 获取所有提供者的命令
    const proms = this.providersByNamespace[namespace].map((provider) => {
        const { provide } = provider;
        return provide(this.env, options);
    });
    let commands = (await this.keepLast.add(Promise.all(proms))).flat();
    
    // 模糊搜索处理
    if (options.searchValue && FUZZY_NAMESPACES.includes(namespace)) {
        commands = fuzzyLookup(options.searchValue, commands, (c) => c.name);
    } else {
        // 按类别排序
        if (namespaceConfig.categories) {
            let commandsSorted = [];
            for (const category of this.categoryKeys) {
                commandsSorted = commandsSorted.concat(
                    commands.filter(commandsWithinCategory(category, this.categoryKeys))
                );
            }
            commands = commandsSorted;
        }
    }
    
    // 限制结果数量并添加元数据
    this.state.commands = markRaw(
        commands.slice(0, 100).map((command) => ({
            ...command,
            keyId: this.keyId++,
            splitName: splitCommandName(command.name, options.searchValue),
        }))
    );
}
```

**搜索策略分析**：
- **提供者模式**: 通过多个提供者获取命令
- **并发处理**: 并行获取所有提供者的命令
- **模糊匹配**: 在指定命名空间使用模糊搜索
- **分类排序**: 按预定义类别顺序排列命令
- **结果限制**: 限制显示100个结果避免性能问题

#### 类别过滤函数
```javascript
function commandsWithinCategory(categoryName, categories) {
    return (cmd) => {
        const inCurrentCategory = categoryName === cmd.category;
        const fallbackCategory = categoryName === "default" && !categories.includes(cmd.category);
        return inCurrentCategory || fallbackCategory;
    };
}
```

**过滤逻辑**：
- **精确匹配**: 命令类别与目标类别完全匹配
- **默认降级**: 未知类别的命令归入default类别
- **类别验证**: 只显示已注册类别中的命令
- **灵活性**: 支持动态类别配置

#### 命令名称分割
```javascript
function splitCommandName(name, searchValue) {
    if (name) {
        const splitName = name.split(new RegExp(`(${escapeRegExp(searchValue)})`, "ig"));
        return searchValue.length && splitName.length > 1 ? splitName : [name];
    }
    return [];
}
```

**名称高亮算法**：
- **正则分割**: 使用正则表达式分割匹配的部分
- **大小写不敏感**: 使用"ig"标志忽略大小写
- **转义处理**: 转义搜索值中的特殊字符
- **条件返回**: 只在有匹配时返回分割结果

### 3. 键盘导航系统

#### 命令选择和滚动
```javascript
selectCommandAndScrollTo(type) {
    this.mouseSelectionActive = false;
    const index = this.state.commands.indexOf(this.state.selectedCommand);
    if (index === -1) return;
    
    let nextIndex;
    if (type === "NEXT") {
        nextIndex = index < this.state.commands.length - 1 ? index + 1 : 0;
    } else if (type === "PREV") {
        nextIndex = index > 0 ? index - 1 : this.state.commands.length - 1;
    }
    
    this.selectCommand(nextIndex);
    
    // 滚动到可见区域
    const command = this.listboxRef.el.querySelector(`#o_command_${nextIndex}`);
    scrollTo(command, { scrollable: this.listboxRef.el });
}
```

**导航特性**：
- **循环导航**: 到达边界时循环到另一端
- **鼠标状态**: 禁用鼠标选择避免冲突
- **自动滚动**: 确保选中项在可见区域内
- **DOM查询**: 通过ID精确定位命令元素

#### 命令执行机制
```javascript
async executeSelectedCommand(ctrlKey) {
    await this.searchValuePromise;
    const selectedCommand = this.state.selectedCommand;
    if (selectedCommand) {
        if (!ctrlKey) {
            this.executeCommand(selectedCommand);
        } else if (selectedCommand.href) {
            window.open(selectedCommand.href, "_blank");
        }
    }
}

async executeCommand(command) {
    const config = await command.action();
    if (config) {
        this.setCommandPaletteConfig(config);
    } else {
        this.props.close();
    }
}
```

**执行策略**：
- **异步等待**: 等待搜索完成再执行
- **修饰键**: Ctrl键支持在新窗口打开链接
- **配置返回**: 命令可返回新配置继续使用面板
- **自动关闭**: 无返回配置时自动关闭面板

### 4. 命名空间系统

#### 命名空间切换
```javascript
switchNamespace(namespace) {
    if (this.lastDebounceSearch) {
        this.lastDebounceSearch.cancel();
    }
    
    const namespaceConfig = this.configByNamespace[namespace] || {};
    this.lastDebounceSearch = debounce(
        (value) => this.search(value),
        namespaceConfig.debounceDelay || 0
    );
    
    this.state.namespace = namespace;
    this.state.placeholder = namespaceConfig.placeholder || DEFAULT_PLACEHOLDER.toString();
}
```

**切换机制**：
- **防抖取消**: 取消之前命名空间的防抖搜索
- **配置应用**: 应用新命名空间的配置
- **防抖重建**: 为新命名空间创建防抖搜索函数
- **UI更新**: 更新占位符等UI元素

#### 搜索值处理
```javascript
processSearchValue(searchValue) {
    let namespace = "default";
    if (searchValue.length && this.providersByNamespace[searchValue[0]]) {
        namespace = searchValue[0];
        searchValue = searchValue.slice(1);
    }
    return { namespace, searchValue };
}
```

**处理逻辑**：
- **前缀检测**: 检查首字符是否为命名空间标识
- **自动切换**: 自动切换到对应的命名空间
- **值清理**: 移除命名空间前缀保留实际搜索值
- **默认降级**: 无匹配命名空间时使用default

## 🎨 实际应用场景

### 1. 高级命令面板系统
```javascript
class AdvancedCommandPaletteManager {
    constructor() {
        this.providers = new Map();
        this.namespaceConfigs = new Map();
        this.commandHistory = [];
        this.favoriteCommands = new Set();
        this.commandUsageStats = new Map();
        this.setupDefaultProviders();
    }
    
    setupDefaultProviders() {
        // 应用命令提供者
        this.addProvider("app", {
            provide: (env, options) => {
                return [
                    {
                        name: "Create New Record",
                        action: () => this.createRecord(),
                        category: "app",
                        icon: "fa-plus",
                        description: "Create a new record in current model"
                    },
                    {
                        name: "Global Search",
                        action: () => this.openGlobalSearch(),
                        category: "app",
                        icon: "fa-search",
                        hotkey: "ctrl+k"
                    }
                ];
            }
        });
        
        // 智能动作提供者
        this.addProvider("smart", {
            provide: (env, options) => {
                const contextCommands = this.generateContextCommands(options.activeElement);
                return contextCommands.map(cmd => ({
                    ...cmd,
                    category: "smart_action",
                    priority: this.calculateCommandPriority(cmd, options)
                }));
            }
        });
        
        // 历史命令提供者
        this.addProvider("history", {
            provide: (env, options) => {
                return this.getRecentCommands().map(cmd => ({
                    ...cmd,
                    name: `${cmd.name} (Recent)`,
                    category: "default",
                    icon: "fa-history"
                }));
            }
        });
        
        // 收藏命令提供者
        this.addProvider("favorites", {
            provide: (env, options) => {
                return this.getFavoriteCommands().map(cmd => ({
                    ...cmd,
                    name: `⭐ ${cmd.name}`,
                    category: "app",
                    priority: 100
                }));
            }
        });
    }
    
    addProvider(namespace, provider) {
        if (!this.providers.has(namespace)) {
            this.providers.set(namespace, []);
        }
        this.providers.get(namespace).push(provider);
    }
    
    setNamespaceConfig(namespace, config) {
        this.namespaceConfigs.set(namespace, {
            placeholder: `Search ${namespace} commands...`,
            emptyMessage: `No ${namespace} commands found`,
            debounceDelay: 150,
            categories: ["app", "smart_action", "actions", "default"],
            ...config
        });
    }
    
    generateContextCommands(activeElement) {
        const commands = [];
        
        // 基于当前元素生成命令
        if (activeElement) {
            const tagName = activeElement.tagName.toLowerCase();
            
            if (tagName === 'input' || tagName === 'textarea') {
                commands.push({
                    name: "Clear Field",
                    action: () => { activeElement.value = ''; },
                    description: "Clear the current input field"
                });
                
                if (activeElement.value) {
                    commands.push({
                        name: "Copy Field Value",
                        action: () => navigator.clipboard.writeText(activeElement.value),
                        description: "Copy field value to clipboard"
                    });
                }
            }
            
            if (activeElement.closest('.o_form_view')) {
                commands.push({
                    name: "Save Record",
                    action: () => this.saveCurrentRecord(),
                    description: "Save the current record",
                    hotkey: "ctrl+s"
                });
                
                commands.push({
                    name: "Discard Changes",
                    action: () => this.discardChanges(),
                    description: "Discard unsaved changes"
                });
            }
            
            if (activeElement.closest('.o_list_view')) {
                commands.push({
                    name: "Select All",
                    action: () => this.selectAllRecords(),
                    description: "Select all visible records",
                    hotkey: "ctrl+a"
                });
                
                commands.push({
                    name: "Export Selected",
                    action: () => this.exportSelectedRecords(),
                    description: "Export selected records"
                });
            }
        }
        
        return commands;
    }
    
    calculateCommandPriority(command, options) {
        let priority = 0;
        
        // 基于使用频率
        const usageCount = this.commandUsageStats.get(command.name) || 0;
        priority += Math.min(usageCount * 5, 50);
        
        // 基于收藏状态
        if (this.favoriteCommands.has(command.name)) {
            priority += 100;
        }
        
        // 基于最近使用
        const recentIndex = this.commandHistory.findIndex(h => h.name === command.name);
        if (recentIndex !== -1) {
            priority += (10 - recentIndex) * 5;
        }
        
        // 基于搜索匹配度
        if (options.searchValue) {
            const matchScore = this.calculateMatchScore(command.name, options.searchValue);
            priority += matchScore * 10;
        }
        
        return priority;
    }
    
    calculateMatchScore(commandName, searchValue) {
        const name = commandName.toLowerCase();
        const search = searchValue.toLowerCase();
        
        if (name.startsWith(search)) return 10;
        if (name.includes(search)) return 5;
        
        // 首字母匹配
        const words = name.split(' ');
        const searchChars = search.split('');
        let matchCount = 0;
        
        for (const char of searchChars) {
            for (const word of words) {
                if (word.startsWith(char)) {
                    matchCount++;
                    break;
                }
            }
        }
        
        return matchCount / searchChars.length * 3;
    }
    
    recordCommandExecution(command) {
        // 记录到历史
        this.commandHistory.unshift({
            ...command,
            executedAt: Date.now()
        });
        
        // 限制历史长度
        if (this.commandHistory.length > 50) {
            this.commandHistory.pop();
        }
        
        // 更新使用统计
        const currentCount = this.commandUsageStats.get(command.name) || 0;
        this.commandUsageStats.set(command.name, currentCount + 1);
        
        // 保存到本地存储
        this.saveToLocalStorage();
    }
    
    toggleFavorite(commandName) {
        if (this.favoriteCommands.has(commandName)) {
            this.favoriteCommands.delete(commandName);
        } else {
            this.favoriteCommands.add(commandName);
        }
        this.saveToLocalStorage();
    }
    
    getRecentCommands() {
        return this.commandHistory.slice(0, 10);
    }
    
    getFavoriteCommands() {
        return Array.from(this.favoriteCommands).map(name => {
            const recentCommand = this.commandHistory.find(h => h.name === name);
            return recentCommand || { name, action: () => {} };
        });
    }
    
    saveToLocalStorage() {
        const data = {
            history: this.commandHistory.slice(0, 20),
            favorites: Array.from(this.favoriteCommands),
            stats: Object.fromEntries(this.commandUsageStats.entries())
        };
        
        localStorage.setItem('command_palette_data', JSON.stringify(data));
    }
    
    loadFromLocalStorage() {
        try {
            const data = JSON.parse(localStorage.getItem('command_palette_data') || '{}');
            
            if (data.history) {
                this.commandHistory = data.history;
            }
            
            if (data.favorites) {
                this.favoriteCommands = new Set(data.favorites);
            }
            
            if (data.stats) {
                this.commandUsageStats = new Map(Object.entries(data.stats));
            }
        } catch (error) {
            console.warn('Failed to load command palette data:', error);
        }
    }
    
    generateConfig() {
        const providers = [];
        
        // 收集所有提供者
        for (const [namespace, namespaceProviders] of this.providers.entries()) {
            providers.push(...namespaceProviders.map(provider => ({
                ...provider,
                namespace
            })));
        }
        
        // 生成配置
        return {
            providers,
            configByNamespace: Object.fromEntries(this.namespaceConfigs.entries())
        };
    }
    
    // 创建增强的命令面板组件
    createEnhancedCommandPalette() {
        const manager = this;
        
        return class EnhancedCommandPalette extends CommandPalette {
            setup() {
                super.setup();
                
                // 加载用户数据
                manager.loadFromLocalStorage();
                
                // 包装命令执行
                const originalExecuteCommand = this.executeCommand;
                this.executeCommand = async function(command) {
                    manager.recordCommandExecution(command);
                    return originalExecuteCommand.call(this, command);
                };
            }
            
            // 添加收藏功能
            toggleCommandFavorite(command) {
                manager.toggleFavorite(command.name);
                this.render();
            }
            
            // 获取命令统计
            getCommandStats(command) {
                return {
                    usageCount: manager.commandUsageStats.get(command.name) || 0,
                    isFavorite: manager.favoriteCommands.has(command.name),
                    lastUsed: manager.commandHistory.find(h => h.name === command.name)?.executedAt
                };
            }
        };
    }
}

// 使用示例
const paletteManager = new AdvancedCommandPaletteManager();

// 配置命名空间
paletteManager.setNamespaceConfig("app", {
    placeholder: "Search application commands...",
    categories: ["app", "smart_action"],
    debounceDelay: 100
});

// 生成配置
const config = paletteManager.generateConfig();

// 创建增强的命令面板
const EnhancedCommandPalette = paletteManager.createEnhancedCommandPalette();
```

### 2. 命令面板主题和自定义系统
```javascript
class CommandPaletteThemeManager {
    constructor() {
        this.themes = new Map();
        this.currentTheme = 'default';
        this.customComponents = new Map();
        this.setupDefaultThemes();
    }

    setupDefaultThemes() {
        // 默认主题
        this.themes.set('default', {
            name: 'Default',
            colors: {
                background: '#ffffff',
                foreground: '#333333',
                accent: '#007bff',
                border: '#dee2e6',
                hover: '#f8f9fa',
                selected: '#e3f2fd'
            },
            typography: {
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto',
                fontSize: '14px',
                lineHeight: '1.5'
            },
            spacing: {
                padding: '12px',
                margin: '8px',
                borderRadius: '6px'
            }
        });

        // 暗色主题
        this.themes.set('dark', {
            name: 'Dark',
            colors: {
                background: '#2d3748',
                foreground: '#e2e8f0',
                accent: '#4299e1',
                border: '#4a5568',
                hover: '#4a5568',
                selected: '#2b6cb0'
            },
            typography: {
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto',
                fontSize: '14px',
                lineHeight: '1.5'
            },
            spacing: {
                padding: '12px',
                margin: '8px',
                borderRadius: '6px'
            }
        });

        // 紧凑主题
        this.themes.set('compact', {
            name: 'Compact',
            colors: {
                background: '#ffffff',
                foreground: '#333333',
                accent: '#28a745',
                border: '#dee2e6',
                hover: '#f8f9fa',
                selected: '#d4edda'
            },
            typography: {
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto',
                fontSize: '12px',
                lineHeight: '1.4'
            },
            spacing: {
                padding: '8px',
                margin: '4px',
                borderRadius: '4px'
            }
        });
    }

    setTheme(themeName) {
        if (this.themes.has(themeName)) {
            this.currentTheme = themeName;
            this.applyTheme();
        }
    }

    applyTheme() {
        const theme = this.themes.get(this.currentTheme);
        if (!theme) return;

        const css = this.generateThemeCSS(theme);

        // 移除旧的主题样式
        const oldStyle = document.getElementById('command-palette-theme');
        if (oldStyle) {
            oldStyle.remove();
        }

        // 应用新的主题样式
        const style = document.createElement('style');
        style.id = 'command-palette-theme';
        style.textContent = css;
        document.head.appendChild(style);
    }

    generateThemeCSS(theme) {
        const { colors, typography, spacing } = theme;

        return `
            .o_command_palette {
                background-color: ${colors.background};
                color: ${colors.foreground};
                font-family: ${typography.fontFamily};
                font-size: ${typography.fontSize};
                line-height: ${typography.lineHeight};
                border: 1px solid ${colors.border};
                border-radius: ${spacing.borderRadius};
            }

            .o_command_palette_search {
                background-color: ${colors.background};
                color: ${colors.foreground};
                border: 1px solid ${colors.border};
                border-radius: ${spacing.borderRadius};
                padding: ${spacing.padding};
                margin: ${spacing.margin};
            }

            .o_command_palette_search:focus {
                border-color: ${colors.accent};
                box-shadow: 0 0 0 2px ${colors.accent}33;
            }

            .o_command {
                padding: ${spacing.padding};
                margin: ${spacing.margin};
                border-radius: ${spacing.borderRadius};
                transition: background-color 0.15s ease;
            }

            .o_command:hover {
                background-color: ${colors.hover};
            }

            .o_command.o_focused {
                background-color: ${colors.selected};
                border-color: ${colors.accent};
            }

            .o_command_name {
                color: ${colors.foreground};
                font-weight: 500;
            }

            .o_command_category {
                color: ${colors.accent};
                font-size: 0.85em;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .o_command_hotkey {
                background-color: ${colors.border};
                color: ${colors.foreground};
                border-radius: 3px;
                padding: 2px 6px;
                font-size: 0.8em;
                font-family: monospace;
            }
        `;
    }

    addCustomTheme(name, themeConfig) {
        this.themes.set(name, {
            name: themeConfig.name || name,
            ...themeConfig
        });
    }

    getAvailableThemes() {
        return Array.from(this.themes.entries()).map(([key, theme]) => ({
            key,
            name: theme.name
        }));
    }

    // 自定义命令项组件
    createCustomCommandItem(template) {
        return class CustomCommandItem extends Component {
            static template = template;
            static props = {
                command: Object,
                searchValue: String,
                isSelected: Boolean,
                onExecute: Function,
                onSelect: Function
            };

            setup() {
                this.themeManager = useService('command_palette_theme');
            }

            get themeColors() {
                const theme = this.themeManager.getCurrentTheme();
                return theme.colors;
            }

            onCommandClick() {
                this.props.onExecute(this.props.command);
            }

            onCommandHover() {
                this.props.onSelect(this.props.command);
            }
        };
    }

    // 创建主题化的命令面板
    createThemedCommandPalette() {
        const themeManager = this;

        return class ThemedCommandPalette extends CommandPalette {
            setup() {
                super.setup();

                // 应用当前主题
                themeManager.applyTheme();

                // 监听主题变化
                this.env.bus.addEventListener('THEME_CHANGED', () => {
                    themeManager.applyTheme();
                });
            }

            get currentTheme() {
                return themeManager.themes.get(themeManager.currentTheme);
            }

            switchTheme(themeName) {
                themeManager.setTheme(themeName);
                this.env.bus.trigger('THEME_CHANGED');
            }
        };
    }
}

// 命令面板布局管理器
class CommandPaletteLayoutManager {
    constructor() {
        this.layouts = new Map();
        this.currentLayout = 'default';
        this.setupDefaultLayouts();
    }

    setupDefaultLayouts() {
        // 默认布局
        this.layouts.set('default', {
            name: 'Default',
            template: `
                <div class="o_command_palette">
                    <div class="o_command_palette_header">
                        <input class="o_command_palette_search"
                               t-ref="input"
                               t-model="state.searchValue"
                               t-att-placeholder="state.placeholder"/>
                    </div>
                    <div class="o_command_palette_body">
                        <div class="o_command_palette_list" t-ref="listbox">
                            <t t-foreach="commandsByCategory" t-as="category" t-key="category.keyId">
                                <div class="o_command_category_header" t-if="category.name">
                                    <span t-esc="category.name"/>
                                </div>
                                <t t-foreach="category.commands" t-as="command" t-key="command.keyId">
                                    <div class="o_command"
                                         t-att-class="{ o_focused: command === state.selectedCommand }"
                                         t-on-click="(ev) => this.onCommandClicked(ev, command_index)"
                                         t-on-mouseenter="() => this.onCommandMouseEnter(command_index)">
                                        <t t-component="command.Component || DefaultCommandItem"
                                           t-props="getCommandProps(command)"/>
                                    </div>
                                </t>
                            </t>
                        </div>
                    </div>
                    <div class="o_command_palette_footer" t-if="state.FooterComponent">
                        <t t-component="state.FooterComponent"/>
                    </div>
                </div>
            `,
            styles: {
                maxHeight: '60vh',
                width: '600px',
                maxWidth: '90vw'
            }
        });

        // 侧边栏布局
        this.layouts.set('sidebar', {
            name: 'Sidebar',
            template: `
                <div class="o_command_palette o_command_palette_sidebar">
                    <div class="o_command_palette_sidebar_content">
                        <div class="o_command_palette_search_section">
                            <input class="o_command_palette_search"
                                   t-ref="input"
                                   t-model="state.searchValue"
                                   t-att-placeholder="state.placeholder"/>
                        </div>
                        <div class="o_command_palette_categories">
                            <t t-foreach="commandsByCategory" t-as="category" t-key="category.keyId">
                                <div class="o_command_category">
                                    <div class="o_command_category_title" t-if="category.name">
                                        <span t-esc="category.name"/>
                                        <span class="o_command_category_count" t-esc="category.commands.length"/>
                                    </div>
                                    <div class="o_command_category_items">
                                        <t t-foreach="category.commands" t-as="command" t-key="command.keyId">
                                            <div class="o_command o_command_sidebar"
                                                 t-att-class="{ o_focused: command === state.selectedCommand }"
                                                 t-on-click="(ev) => this.onCommandClicked(ev, command_index)">
                                                <t t-component="command.Component || DefaultCommandItem"
                                                   t-props="getCommandProps(command)"/>
                                            </div>
                                        </t>
                                    </div>
                                </div>
                            </t>
                        </div>
                    </div>
                </div>
            `,
            styles: {
                height: '100vh',
                width: '400px',
                position: 'fixed',
                right: '0',
                top: '0'
            }
        });

        // 全屏布局
        this.layouts.set('fullscreen', {
            name: 'Fullscreen',
            template: `
                <div class="o_command_palette o_command_palette_fullscreen">
                    <div class="o_command_palette_fullscreen_content">
                        <div class="o_command_palette_search_large">
                            <input class="o_command_palette_search"
                                   t-ref="input"
                                   t-model="state.searchValue"
                                   t-att-placeholder="state.placeholder"/>
                        </div>
                        <div class="o_command_palette_grid">
                            <t t-foreach="commandsByCategory" t-as="category" t-key="category.keyId">
                                <div class="o_command_category_grid">
                                    <h3 class="o_command_category_title" t-if="category.name" t-esc="category.name"/>
                                    <div class="o_command_grid">
                                        <t t-foreach="category.commands" t-as="command" t-key="command.keyId">
                                            <div class="o_command o_command_card"
                                                 t-att-class="{ o_focused: command === state.selectedCommand }"
                                                 t-on-click="(ev) => this.onCommandClicked(ev, command_index)">
                                                <t t-component="command.Component || DefaultCommandItem"
                                                   t-props="getCommandProps(command)"/>
                                            </div>
                                        </t>
                                    </div>
                                </div>
                            </t>
                        </div>
                    </div>
                </div>
            `,
            styles: {
                height: '100vh',
                width: '100vw',
                position: 'fixed',
                top: '0',
                left: '0'
            }
        });
    }

    setLayout(layoutName) {
        if (this.layouts.has(layoutName)) {
            this.currentLayout = layoutName;
        }
    }

    getCurrentLayout() {
        return this.layouts.get(this.currentLayout);
    }

    addCustomLayout(name, layoutConfig) {
        this.layouts.set(name, {
            name: layoutConfig.name || name,
            ...layoutConfig
        });
    }

    getAvailableLayouts() {
        return Array.from(this.layouts.entries()).map(([key, layout]) => ({
            key,
            name: layout.name
        }));
    }
}

// 使用示例
const themeManager = new CommandPaletteThemeManager();
const layoutManager = new CommandPaletteLayoutManager();

// 设置暗色主题
themeManager.setTheme('dark');

// 设置侧边栏布局
layoutManager.setLayout('sidebar');

// 创建主题化的命令面板
const ThemedCommandPalette = themeManager.createThemedCommandPalette();
```

## 🔧 调试技巧

### 命令面板状态监控
```javascript
function debugCommandPalette() {
    const palette = document.querySelector('.o_command_palette');
    if (!palette) {
        console.log('Command palette not found');
        return;
    }

    console.group('🎨 Command Palette Debug');
    console.log('Current namespace:', palette.__owl__.component.state.namespace);
    console.log('Search value:', palette.__owl__.component.state.searchValue);
    console.log('Commands count:', palette.__owl__.component.state.commands.length);
    console.log('Selected command:', palette.__owl__.component.state.selectedCommand);
    console.table(palette.__owl__.component.state.commands.map(cmd => ({
        name: cmd.name,
        category: cmd.category,
        keyId: cmd.keyId
    })));
    console.groupEnd();
}

// 在控制台中调用
debugCommandPalette();
```

### 搜索性能分析
```javascript
function analyzeSearchPerformance() {
    const originalSetCommands = CommandPalette.prototype.setCommands;

    CommandPalette.prototype.setCommands = async function(namespace, options = {}) {
        const startTime = performance.now();

        console.log(`🔍 Starting search in namespace: ${namespace}`, options);

        const result = await originalSetCommands.call(this, namespace, options);

        const endTime = performance.now();
        const duration = endTime - startTime;

        console.log(`⚡ Search completed in ${duration.toFixed(2)}ms`, {
            namespace,
            searchValue: options.searchValue,
            commandsFound: this.state.commands.length,
            performance: duration < 100 ? 'Good' : duration < 300 ? 'Fair' : 'Slow'
        });

        return result;
    };

    console.log('Search performance analysis enabled');
}

// 启用性能分析
analyzeSearchPerformance();
```

## 📊 性能考虑

### 优化策略
1. **结果限制**: 限制显示100个结果避免DOM过载
2. **防抖搜索**: 使用防抖避免频繁搜索
3. **虚拟滚动**: 对于大量结果考虑虚拟滚动
4. **并发控制**: 使用Race和KeepLast管理异步操作

### 最佳实践
```javascript
// ✅ 好的做法：使用防抖搜索
this.lastDebounceSearch = debounce(
    (value) => this.search(value),
    namespaceConfig.debounceDelay || 150
);

// ❌ 不好的做法：每次输入都搜索
onSearchInput(ev) {
    this.search(ev.target.value); // 会导致过度搜索
}

// ✅ 好的做法：限制结果数量
commands.slice(0, 100)

// ❌ 不好的做法：显示所有结果
commands // 可能导致性能问题

// ✅ 好的做法：使用markRaw避免响应式包装
this.state.commands = markRaw(commands);

// ❌ 不好的做法：让大数组变成响应式
this.state.commands = commands; // 性能开销大
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解命令面板的UI架构和交互设计
- [ ] 掌握模糊搜索和实时过滤算法
- [ ] 理解键盘导航和快捷键系统
- [ ] 能够实现命名空间和提供者模式
- [ ] 掌握企业级搜索界面的实现技术
- [ ] 了解命令面板的性能优化技术

## 🚀 下一步学习
学完命令面板后，建议继续学习：
1. **命令服务** (`@web/core/commands/command_service.js`) - 学习命令服务实现
2. **默认提供者** (`@web/core/commands/default_providers.js`) - 理解默认命令提供者
3. **对话框系统** (`@web/core/dialog/`) - 掌握对话框架构

## 💡 重要提示
- 命令面板是现代应用的重要交互界面
- 搜索性能对用户体验至关重要
- 键盘导航提供了高效的操作方式
- 命名空间系统支持复杂的命令组织
```

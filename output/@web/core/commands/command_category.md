# @web/core/commands/command_category.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/commands/command_category.js`
- **原始路径**: `/web/static/src/core/commands/command_category.js`
- **代码行数**: 17行
- **作用**: 定义命令面板的分类系统，管理命令的组织和优先级

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 命令分类系统的设计原理和架构
- 注册表模式在分类管理中的应用
- 序列化排序和优先级管理机制
- 企业级命令系统的组织策略
- 可扩展分类系统的设计思想

## 📚 核心概念

### 什么是命令分类系统？
命令分类系统是一个**组织和管理框架**，主要功能：
- **分类管理**: 将命令按功能和用途进行分类
- **优先级控制**: 通过序列号控制分类的显示顺序
- **扩展性**: 支持动态添加新的命令分类
- **用户体验**: 提供清晰的命令组织结构

### 核心架构组成
```javascript
// 命令分类注册表
const commandCategoryRegistry = registry.category("command_categories");

// 预定义分类及其优先级
const categories = {
    app: { sequence: 10 },           // 应用级命令
    smart_action: { sequence: 15 },  // 智能动作
    actions: { sequence: 30 },       // 常规动作
    default: { sequence: 50 },       // 默认分类
    view_switcher: { sequence: 100 }, // 视图切换
    debug: { sequence: 110 },        // 调试命令
    disabled: {}                     // 禁用命令
};
```

### 基本使用模式
```javascript
import { registry } from '@web/core/registry';

// 获取命令分类注册表
const commandCategoryRegistry = registry.category("command_categories");

// 添加新的命令分类
commandCategoryRegistry.add("custom_category", {
    name: "Custom Commands",
    description: "User-defined commands"
}, { sequence: 25 });

// 获取所有分类（按序列号排序）
const categories = commandCategoryRegistry.getAll();

// 检查分类是否存在
if (commandCategoryRegistry.contains("app")) {
    console.log("App category exists");
}
```

## 🔍 核心实现详解

### 1. 分类注册表初始化

#### 注册表获取和配置
```javascript
const commandCategoryRegistry = registry.category("command_categories");
```

**设计原理**：
- **命名空间**: 使用"command_categories"作为独立的命名空间
- **全局访问**: 通过registry提供全局访问点
- **类型安全**: 确保只有命令分类相关的项目被注册
- **扩展性**: 支持运行时动态添加新分类

### 2. 预定义分类系统

#### 应用级分类 (app)
```javascript
.add("app", {}, { sequence: 10 })
```

**分类特点**：
- **最高优先级**: sequence: 10，在命令面板中最先显示
- **应用范围**: 包含应用级别的核心命令
- **用户频率**: 用户最常使用的命令类型
- **示例命令**: 创建新记录、搜索、导航等

#### 智能动作分类 (smart_action)
```javascript
.add("smart_action", {}, { sequence: 15 })
```

**分类特点**：
- **智能推荐**: 基于上下文的智能命令推荐
- **动态生成**: 根据当前状态动态生成的命令
- **高相关性**: 与当前操作高度相关的命令
- **示例命令**: 基于当前记录的快速操作

#### 常规动作分类 (actions)
```javascript
.add("actions", {}, { sequence: 30 })
```

**分类特点**：
- **标准操作**: 系统中定义的标准动作
- **菜单映射**: 通常对应菜单中的动作项
- **权限控制**: 受用户权限控制的动作
- **示例命令**: 报表生成、数据导入导出等

#### 默认分类 (default)
```javascript
.add("default", {}, { sequence: 50 })
```

**分类特点**：
- **兜底分类**: 未明确分类的命令的默认归属
- **中等优先级**: 在专门分类之后显示
- **通用性**: 包含各种通用命令
- **示例命令**: 帮助、设置、通用工具等

#### 视图切换分类 (view_switcher)
```javascript
.add("view_switcher", {}, { sequence: 100 })
```

**分类特点**：
- **视图相关**: 专门用于视图切换的命令
- **界面控制**: 控制界面显示和布局的命令
- **较低优先级**: 在主要功能命令之后显示
- **示例命令**: 列表视图、表单视图、看板视图切换

#### 调试分类 (debug)
```javascript
.add("debug", {}, { sequence: 110 })
```

**分类特点**：
- **开发工具**: 主要用于开发和调试
- **条件显示**: 通常只在调试模式下显示
- **最低优先级**: 在所有功能命令之后显示
- **示例命令**: 查看元数据、编辑视图、性能分析

#### 禁用分类 (disabled)
```javascript
.add("disabled", {});
```

**分类特点**：
- **无序列号**: 没有sequence，不参与正常排序
- **隐藏状态**: 用于临时禁用的命令
- **管理工具**: 便于命令的启用/禁用管理
- **示例用途**: 维护期间禁用的功能

## 🎨 实际应用场景

### 1. 动态命令分类管理系统
```javascript
class CommandCategoryManager {
    constructor() {
        this.categoryRegistry = registry.category("command_categories");
        this.categoryMetadata = new Map();
        this.categoryFilters = new Map();
        this.setupDefaultMetadata();
    }
    
    setupDefaultMetadata() {
        // 为预定义分类添加详细元数据
        this.categoryMetadata.set("app", {
            name: "Application",
            description: "Core application commands",
            icon: "fa-rocket",
            color: "#007bff",
            keywords: ["app", "core", "main"],
            userLevel: "all"
        });
        
        this.categoryMetadata.set("smart_action", {
            name: "Smart Actions",
            description: "Context-aware intelligent commands",
            icon: "fa-magic",
            color: "#28a745",
            keywords: ["smart", "ai", "context"],
            userLevel: "intermediate"
        });
        
        this.categoryMetadata.set("actions", {
            name: "Actions",
            description: "Standard system actions",
            icon: "fa-cogs",
            color: "#6c757d",
            keywords: ["action", "menu", "function"],
            userLevel: "all"
        });
        
        this.categoryMetadata.set("default", {
            name: "General",
            description: "General purpose commands",
            icon: "fa-list",
            color: "#6f42c1",
            keywords: ["general", "misc", "other"],
            userLevel: "all"
        });
        
        this.categoryMetadata.set("view_switcher", {
            name: "View Switcher",
            description: "Switch between different views",
            icon: "fa-eye",
            color: "#fd7e14",
            keywords: ["view", "switch", "display"],
            userLevel: "basic"
        });
        
        this.categoryMetadata.set("debug", {
            name: "Debug",
            description: "Development and debugging tools",
            icon: "fa-bug",
            color: "#dc3545",
            keywords: ["debug", "dev", "tool"],
            userLevel: "developer"
        });
    }
    
    // 添加新分类
    addCategory(categoryId, config, options = {}) {
        const { sequence = 50, metadata = {} } = options;
        
        // 注册分类
        this.categoryRegistry.add(categoryId, config, { sequence });
        
        // 添加元数据
        if (metadata) {
            this.categoryMetadata.set(categoryId, {
                name: categoryId,
                description: `${categoryId} commands`,
                icon: "fa-folder",
                color: "#6c757d",
                keywords: [categoryId],
                userLevel: "all",
                ...metadata
            });
        }
        
        return this;
    }
    
    // 获取分类信息
    getCategoryInfo(categoryId) {
        const registryEntry = this.categoryRegistry.get(categoryId);
        const metadata = this.categoryMetadata.get(categoryId);
        
        if (!registryEntry) {
            return null;
        }
        
        return {
            id: categoryId,
            config: registryEntry,
            sequence: registryEntry.sequence || 50,
            metadata: metadata || {},
            isActive: this.isCategoryActive(categoryId),
            commandCount: this.getCategoryCommandCount(categoryId)
        };
    }
    
    // 获取所有分类（排序后）
    getAllCategories() {
        const categories = [];
        
        for (const [categoryId] of this.categoryRegistry.getEntries()) {
            const categoryInfo = this.getCategoryInfo(categoryId);
            if (categoryInfo) {
                categories.push(categoryInfo);
            }
        }
        
        // 按序列号排序
        return categories.sort((a, b) => {
            const seqA = a.sequence || 50;
            const seqB = b.sequence || 50;
            return seqA - seqB;
        });
    }
    
    // 按用户级别过滤分类
    getCategoriesByUserLevel(userLevel) {
        const allCategories = this.getAllCategories();
        
        return allCategories.filter(category => {
            const categoryUserLevel = category.metadata.userLevel || "all";
            
            if (categoryUserLevel === "all") return true;
            if (userLevel === "developer") return true;
            if (userLevel === "intermediate" && categoryUserLevel !== "developer") return true;
            if (userLevel === "basic" && categoryUserLevel === "basic") return true;
            
            return false;
        });
    }
    
    // 搜索分类
    searchCategories(query) {
        const allCategories = this.getAllCategories();
        const lowerQuery = query.toLowerCase();
        
        return allCategories.filter(category => {
            const metadata = category.metadata;
            
            // 搜索名称
            if (metadata.name && metadata.name.toLowerCase().includes(lowerQuery)) {
                return true;
            }
            
            // 搜索描述
            if (metadata.description && metadata.description.toLowerCase().includes(lowerQuery)) {
                return true;
            }
            
            // 搜索关键词
            if (metadata.keywords && metadata.keywords.some(keyword => 
                keyword.toLowerCase().includes(lowerQuery))) {
                return true;
            }
            
            return false;
        });
    }
    
    // 设置分类过滤器
    setCategoryFilter(categoryId, filterFn) {
        this.categoryFilters.set(categoryId, filterFn);
    }
    
    // 检查分类是否激活
    isCategoryActive(categoryId) {
        const filter = this.categoryFilters.get(categoryId);
        return filter ? filter() : true;
    }
    
    // 获取分类命令数量（模拟）
    getCategoryCommandCount(categoryId) {
        // 这里应该从命令注册表获取实际数量
        const mockCounts = {
            app: 15,
            smart_action: 8,
            actions: 25,
            default: 12,
            view_switcher: 6,
            debug: 10
        };
        
        return mockCounts[categoryId] || 0;
    }
    
    // 重新排序分类
    reorderCategories(categoryOrder) {
        categoryOrder.forEach((categoryId, index) => {
            const sequence = (index + 1) * 10;
            
            // 获取当前配置
            const currentConfig = this.categoryRegistry.get(categoryId);
            if (currentConfig) {
                // 移除并重新添加以更新序列号
                this.categoryRegistry.remove(categoryId);
                this.categoryRegistry.add(categoryId, currentConfig, { sequence });
            }
        });
    }
    
    // 导出分类配置
    exportConfiguration() {
        const categories = this.getAllCategories();
        
        return {
            categories: categories.map(cat => ({
                id: cat.id,
                sequence: cat.sequence,
                metadata: cat.metadata
            })),
            filters: Array.from(this.categoryFilters.keys()),
            timestamp: Date.now()
        };
    }
    
    // 导入分类配置
    importConfiguration(config) {
        if (config.categories) {
            config.categories.forEach(({ id, sequence, metadata }) => {
                // 更新序列号
                const currentConfig = this.categoryRegistry.get(id);
                if (currentConfig) {
                    this.categoryRegistry.remove(id);
                    this.categoryRegistry.add(id, currentConfig, { sequence });
                }
                
                // 更新元数据
                if (metadata) {
                    this.categoryMetadata.set(id, metadata);
                }
            });
        }
    }
    
    // 生成分类统计报告
    generateCategoryReport() {
        const categories = this.getAllCategories();
        
        const report = {
            totalCategories: categories.length,
            activeCategories: categories.filter(cat => cat.isActive).length,
            totalCommands: categories.reduce((sum, cat) => sum + cat.commandCount, 0),
            categoryDistribution: categories.map(cat => ({
                id: cat.id,
                name: cat.metadata.name,
                commandCount: cat.commandCount,
                percentage: 0 // 将在下面计算
            })),
            userLevelDistribution: this.calculateUserLevelDistribution(categories)
        };
        
        // 计算百分比
        const totalCommands = report.totalCommands;
        report.categoryDistribution.forEach(cat => {
            cat.percentage = totalCommands > 0 ? 
                ((cat.commandCount / totalCommands) * 100).toFixed(1) : 0;
        });
        
        return report;
    }
    
    calculateUserLevelDistribution(categories) {
        const distribution = {
            all: 0,
            basic: 0,
            intermediate: 0,
            developer: 0
        };
        
        categories.forEach(cat => {
            const userLevel = cat.metadata.userLevel || "all";
            distribution[userLevel]++;
        });
        
        return distribution;
    }
}

// 使用示例
const categoryManager = new CommandCategoryManager();

// 添加自定义分类
categoryManager.addCategory("reporting", {}, {
    sequence: 35,
    metadata: {
        name: "Reporting",
        description: "Report generation and analysis",
        icon: "fa-chart-bar",
        color: "#17a2b8",
        keywords: ["report", "chart", "analysis"],
        userLevel: "intermediate"
    }
});

// 获取用户级别的分类
const userCategories = categoryManager.getCategoriesByUserLevel("intermediate");
console.log("Categories for intermediate users:", userCategories);

// 搜索分类
const searchResults = categoryManager.searchCategories("debug");
console.log("Search results for 'debug':", searchResults);

// 生成报告
const report = categoryManager.generateCategoryReport();
console.log("Category Report:", report);
```

### 2. 分类可视化和管理界面
```javascript
class CategoryVisualizationComponent extends Component {
    static template = xml`
        <div class="category-manager">
            <div class="category-header">
                <h3>Command Categories</h3>
                <div class="category-controls">
                    <input t-model="searchQuery"
                           placeholder="Search categories..."
                           t-on-input="onSearchInput"/>
                    <select t-model="userLevelFilter" t-on-change="onUserLevelChange">
                        <option value="all">All Users</option>
                        <option value="basic">Basic</option>
                        <option value="intermediate">Intermediate</option>
                        <option value="developer">Developer</option>
                    </select>
                    <button t-on-click="addNewCategory" class="btn btn-primary">
                        Add Category
                    </button>
                </div>
            </div>

            <div class="category-list">
                <t t-foreach="filteredCategories" t-as="category" t-key="category.id">
                    <div class="category-item" t-att-data-category="category.id">
                        <div class="category-header">
                            <div class="category-icon" t-att-style="getCategoryIconStyle(category)">
                                <i t-att-class="category.metadata.icon"/>
                            </div>
                            <div class="category-info">
                                <h4 t-esc="category.metadata.name"/>
                                <p t-esc="category.metadata.description"/>
                                <div class="category-meta">
                                    <span class="sequence">Sequence: <t t-esc="category.sequence"/></span>
                                    <span class="command-count">Commands: <t t-esc="category.commandCount"/></span>
                                    <span class="user-level" t-att-class="getUserLevelClass(category)">
                                        <t t-esc="category.metadata.userLevel"/>
                                    </span>
                                </div>
                            </div>
                            <div class="category-actions">
                                <button t-on-click="() => this.editCategory(category)"
                                        class="btn btn-sm btn-outline-primary">
                                    Edit
                                </button>
                                <button t-on-click="() => this.toggleCategory(category)"
                                        t-att-class="getToggleButtonClass(category)">
                                    <t t-if="category.isActive">Disable</t>
                                    <t t-else="">Enable</t>
                                </button>
                                <button t-on-click="() => this.deleteCategory(category)"
                                        class="btn btn-sm btn-outline-danger"
                                        t-if="!isSystemCategory(category)">
                                    Delete
                                </button>
                            </div>
                        </div>

                        <div class="category-details" t-if="expandedCategory === category.id">
                            <div class="category-keywords">
                                <strong>Keywords:</strong>
                                <t t-foreach="category.metadata.keywords" t-as="keyword" t-key="keyword">
                                    <span class="keyword-tag" t-esc="keyword"/>
                                </t>
                            </div>

                            <div class="category-commands">
                                <strong>Sample Commands:</strong>
                                <div class="command-list">
                                    <t t-foreach="getSampleCommands(category)" t-as="command" t-key="command.id">
                                        <div class="command-item">
                                            <span class="command-name" t-esc="command.name"/>
                                            <span class="command-description" t-esc="command.description"/>
                                        </div>
                                    </t>
                                </div>
                            </div>
                        </div>
                    </div>
                </t>
            </div>

            <div class="category-statistics">
                <h4>Statistics</h4>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-value" t-esc="statistics.totalCategories"/>
                        <span class="stat-label">Total Categories</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" t-esc="statistics.activeCategories"/>
                        <span class="stat-label">Active Categories</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" t-esc="statistics.totalCommands"/>
                        <span class="stat-label">Total Commands</span>
                    </div>
                </div>

                <div class="category-distribution">
                    <h5>Command Distribution</h5>
                    <t t-foreach="statistics.categoryDistribution" t-as="dist" t-key="dist.id">
                        <div class="distribution-item">
                            <span class="category-name" t-esc="dist.name"/>
                            <div class="progress-bar">
                                <div class="progress-fill"
                                     t-att-style="getProgressStyle(dist.percentage)"/>
                            </div>
                            <span class="percentage" t-esc="dist.percentage + '%'"/>
                        </div>
                    </t>
                </div>
            </div>
        </div>
    `;

    setup() {
        this.categoryManager = new CommandCategoryManager();

        this.state = useState({
            searchQuery: '',
            userLevelFilter: 'all',
            expandedCategory: null,
            categories: this.categoryManager.getAllCategories(),
            statistics: this.categoryManager.generateCategoryReport()
        });

        this.updateFilteredCategories();
    }

    get filteredCategories() {
        let categories = this.state.categories;

        // 按用户级别过滤
        if (this.state.userLevelFilter !== 'all') {
            categories = this.categoryManager.getCategoriesByUserLevel(this.state.userLevelFilter);
        }

        // 按搜索查询过滤
        if (this.state.searchQuery) {
            categories = this.categoryManager.searchCategories(this.state.searchQuery);
        }

        return categories;
    }

    onSearchInput() {
        this.updateFilteredCategories();
    }

    onUserLevelChange() {
        this.updateFilteredCategories();
    }

    updateFilteredCategories() {
        this.state.categories = this.categoryManager.getAllCategories();
        this.state.statistics = this.categoryManager.generateCategoryReport();
    }

    getCategoryIconStyle(category) {
        return `color: ${category.metadata.color || '#6c757d'};`;
    }

    getUserLevelClass(category) {
        const userLevel = category.metadata.userLevel || 'all';
        return `user-level-${userLevel}`;
    }

    getToggleButtonClass(category) {
        const baseClass = 'btn btn-sm';
        return category.isActive ?
            `${baseClass} btn-outline-warning` :
            `${baseClass} btn-outline-success`;
    }

    isSystemCategory(category) {
        const systemCategories = ['app', 'smart_action', 'actions', 'default', 'view_switcher', 'debug', 'disabled'];
        return systemCategories.includes(category.id);
    }

    getSampleCommands(category) {
        // 模拟获取示例命令
        const sampleCommands = {
            app: [
                { id: 1, name: 'Create', description: 'Create new record' },
                { id: 2, name: 'Search', description: 'Global search' }
            ],
            smart_action: [
                { id: 3, name: 'Quick Edit', description: 'Edit current record' },
                { id: 4, name: 'Duplicate', description: 'Duplicate current record' }
            ],
            actions: [
                { id: 5, name: 'Export', description: 'Export data' },
                { id: 6, name: 'Import', description: 'Import data' }
            ]
        };

        return sampleCommands[category.id] || [];
    }

    getProgressStyle(percentage) {
        return `width: ${percentage}%;`;
    }

    editCategory(category) {
        // 打开编辑对话框
        console.log('Editing category:', category.id);
    }

    toggleCategory(category) {
        // 切换分类状态
        console.log('Toggling category:', category.id);
        this.updateFilteredCategories();
    }

    deleteCategory(category) {
        if (confirm(`Are you sure you want to delete category "${category.metadata.name}"?`)) {
            // 删除分类
            console.log('Deleting category:', category.id);
            this.updateFilteredCategories();
        }
    }

    addNewCategory() {
        // 打开新建分类对话框
        console.log('Adding new category');
    }
}
```

## 🔧 调试技巧

### 分类注册表状态查看
```javascript
function debugCommandCategories() {
    const categoryRegistry = registry.category("command_categories");

    console.group('📂 Command Categories Debug');
    console.log('Registry entries:', categoryRegistry.getEntries());

    // 按序列号排序显示
    const sortedCategories = Array.from(categoryRegistry.getEntries())
        .map(([id, entry]) => ({ id, ...entry }))
        .sort((a, b) => (a.sequence || 50) - (b.sequence || 50));

    console.table(sortedCategories);
    console.groupEnd();
}

// 在控制台中调用
debugCommandCategories();
```

### 分类序列号验证
```javascript
function validateCategorySequences() {
    const categoryRegistry = registry.category("command_categories");
    const categories = Array.from(categoryRegistry.getEntries());

    const issues = [];
    const sequences = [];

    categories.forEach(([id, entry]) => {
        const sequence = entry.sequence;

        if (sequence === undefined) {
            issues.push(`Category "${id}" has no sequence number`);
        } else if (sequences.includes(sequence)) {
            issues.push(`Duplicate sequence ${sequence} found`);
        } else {
            sequences.push(sequence);
        }
    });

    if (issues.length > 0) {
        console.warn('Category sequence issues:', issues);
    } else {
        console.log('All category sequences are valid');
    }

    return issues;
}

// 验证序列号
validateCategorySequences();
```

## 📊 性能考虑

### 优化策略
1. **静态注册**: 分类在应用启动时一次性注册
2. **序列号缓存**: 缓存排序结果避免重复计算
3. **延迟加载**: 按需加载分类元数据
4. **内存管理**: 合理管理分类相关的内存使用

### 最佳实践
```javascript
// ✅ 好的做法：合理的序列号间隔
commandCategoryRegistry
    .add("category1", {}, { sequence: 10 })
    .add("category2", {}, { sequence: 20 })
    .add("category3", {}, { sequence: 30 });

// ❌ 不好的做法：序列号过于密集
commandCategoryRegistry
    .add("category1", {}, { sequence: 10 })
    .add("category2", {}, { sequence: 11 })
    .add("category3", {}, { sequence: 12 });

// ✅ 好的做法：有意义的分类名称
commandCategoryRegistry.add("user_management", {}, { sequence: 25 });

// ❌ 不好的做法：模糊的分类名称
commandCategoryRegistry.add("misc", {}, { sequence: 25 });
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解命令分类系统的设计原理和架构
- [ ] 掌握注册表模式在分类管理中的应用
- [ ] 理解序列化排序和优先级管理机制
- [ ] 能够设计企业级命令系统的组织策略
- [ ] 掌握可扩展分类系统的设计思想
- [ ] 了解分类管理的最佳实践

## 🚀 下一步学习
学完命令分类后，建议继续学习：
1. **命令钩子** (`@web/core/commands/command_hook.js`) - 学习命令钩子系统
2. **命令服务** (`@web/core/commands/command_service.js`) - 理解命令服务架构
3. **命令面板** (`@web/core/commands/command_palette.js`) - 掌握命令面板实现

## 💡 重要提示
- 分类系统是命令面板用户体验的基础
- 合理的序列号设计有助于命令组织
- 扩展性设计支持第三方模块添加分类
- 分类元数据丰富了用户界面的表现力
```

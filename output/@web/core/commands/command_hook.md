# @web/core/commands/command_hook.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/commands/command_hook.js`
- **原始路径**: `/web/static/src/core/commands/command_hook.js`
- **代码行数**: 29行
- **作用**: 提供命令钩子函数，简化组件中命令的注册和生命周期管理

## 🎯 学习目标
通过学习这个文件，您将掌握：
- React Hooks模式在Odoo中的应用
- 组件生命周期与命令管理的集成
- 自动化资源管理的设计模式
- 声明式编程在命令系统中的实现
- 企业级钩子系统的设计原理

## 📚 核心概念

### 什么是命令钩子？
命令钩子是一个**生命周期管理工具**，主要功能：
- **自动注册**: 组件挂载时自动注册命令
- **自动清理**: 组件卸载时自动移除命令
- **声明式**: 使用声明式语法定义命令
- **简化API**: 简化命令注册的复杂性

### 核心架构组成
```javascript
// 命令钩子函数
useCommand(name, action, options)

// 参数说明
const parameters = {
    name: "string",        // 命令名称
    action: "function",    // 命令执行函数
    options: "object"      // 命令选项配置
};

// 生命周期管理
const lifecycle = {
    mount: "自动注册命令",
    unmount: "自动移除命令"
};
```

### 基本使用模式
```javascript
import { useCommand } from '@web/core/commands/command_hook';

class MyComponent extends Component {
    setup() {
        // 注册简单命令
        useCommand("create_record", () => {
            this.createNewRecord();
        });
        
        // 注册带选项的命令
        useCommand("export_data", () => {
            this.exportData();
        }, {
            category: "actions",
            hotkey: "ctrl+e"
        });
        
        // 注册动态命令
        useCommand("edit_current", () => {
            if (this.currentRecord) {
                this.editRecord(this.currentRecord);
            }
        }, {
            category: "smart_action",
            isAvailable: () => !!this.currentRecord
        });
    }
    
    createNewRecord() {
        console.log("Creating new record");
    }
    
    exportData() {
        console.log("Exporting data");
    }
    
    editRecord(record) {
        console.log("Editing record:", record.id);
    }
}
```

## 🔍 核心实现详解

### 1. 钩子函数设计

#### useCommand函数签名
```javascript
function useCommand(name, action, options = {}) {
    const commandService = useService("command");
    useEffect(
        () => commandService.add(name, action, options),
        () => []
    );
}
```

**设计原理分析**：
- **服务依赖**: 通过useService获取命令服务实例
- **效果钩子**: 使用useEffect管理命令的生命周期
- **空依赖**: 空依赖数组确保只在组件挂载时执行一次
- **返回清理**: commandService.add返回清理函数

#### 参数类型定义
```javascript
/**
 * @param {string} name - 命令名称
 * @param {()=>(void | import("@web/core/commands/command_palette").CommandPaletteConfig)} action - 命令动作
 * @param {CommandOptions} [options] - 命令选项
 */
```

**类型系统特点**：
- **TypeScript支持**: 完整的类型定义和文档
- **返回值灵活**: action可以返回void或配置对象
- **可选参数**: options参数有默认值
- **导入类型**: 引用其他模块的类型定义

### 2. 生命周期管理机制

#### useEffect的使用
```javascript
useEffect(
    () => commandService.add(name, action, options),
    () => []
);
```

**生命周期绑定**：
- **挂载时注册**: 组件挂载时调用commandService.add
- **卸载时清理**: useEffect返回的清理函数自动调用
- **单次执行**: 空依赖数组确保只执行一次
- **自动管理**: 无需手动管理命令的注册和清理

#### 清理函数机制
```javascript
// commandService.add的返回值是清理函数
const cleanup = commandService.add(name, action, options);

// useEffect自动调用清理函数
return cleanup;
```

**清理策略**：
- **自动清理**: useEffect自动处理清理逻辑
- **防止泄漏**: 避免命令注册后未清理的内存泄漏
- **组件隔离**: 确保组件间的命令不会相互影响
- **状态一致**: 保持命令注册表的状态一致性

## 🎨 实际应用场景

### 1. 高级命令钩子系统
```javascript
class AdvancedCommandHooks {
    constructor() {
        this.commandRegistry = new Map();
        this.commandGroups = new Map();
        this.conditionalCommands = new Map();
    }
    
    // 创建条件命令钩子
    static useConditionalCommand(name, action, condition, options = {}) {
        const commandService = useService("command");
        const [isAvailable, setIsAvailable] = useState(condition());
        
        // 监听条件变化
        useEffect(() => {
            const checkCondition = () => {
                const newAvailability = condition();
                if (newAvailability !== isAvailable) {
                    setIsAvailable(newAvailability);
                }
            };
            
            // 定期检查条件
            const interval = setInterval(checkCondition, 1000);
            return () => clearInterval(interval);
        }, []);
        
        // 根据条件注册/注销命令
        useEffect(() => {
            if (isAvailable) {
                return commandService.add(name, action, {
                    ...options,
                    isAvailable: () => condition()
                });
            }
        }, [isAvailable]);
    }
    
    // 创建命令组钩子
    static useCommandGroup(groupName, commands, options = {}) {
        const commandService = useService("command");
        
        useEffect(() => {
            const cleanupFunctions = [];
            
            commands.forEach(({ name, action, commandOptions = {} }) => {
                const cleanup = commandService.add(name, action, {
                    ...commandOptions,
                    group: groupName,
                    ...options
                });
                cleanupFunctions.push(cleanup);
            });
            
            // 返回组清理函数
            return () => {
                cleanupFunctions.forEach(cleanup => cleanup && cleanup());
            };
        }, []);
    }
    
    // 创建快捷键命令钩子
    static useHotkeyCommand(name, action, hotkey, options = {}) {
        const commandService = useService("command");
        
        useEffect(() => {
            return commandService.add(name, action, {
                ...options,
                hotkey,
                category: options.category || "default"
            });
        }, []);
        
        // 额外的快捷键监听
        useEffect(() => {
            const handleKeydown = (event) => {
                if (this.matchesHotkey(event, hotkey)) {
                    event.preventDefault();
                    action();
                }
            };
            
            document.addEventListener('keydown', handleKeydown);
            return () => document.removeEventListener('keydown', handleKeydown);
        }, []);
    }
    
    static matchesHotkey(event, hotkey) {
        const keys = hotkey.toLowerCase().split('+');
        const modifiers = {
            ctrl: event.ctrlKey,
            alt: event.altKey,
            shift: event.shiftKey,
            meta: event.metaKey
        };
        
        return keys.every(key => {
            if (modifiers.hasOwnProperty(key)) {
                return modifiers[key];
            }
            return event.key.toLowerCase() === key;
        });
    }
    
    // 创建上下文感知命令钩子
    static useContextCommand(name, action, contextProvider, options = {}) {
        const commandService = useService("command");
        const context = contextProvider();
        
        useEffect(() => {
            if (context) {
                return commandService.add(name, () => {
                    action(context);
                }, {
                    ...options,
                    context: context,
                    isAvailable: () => !!contextProvider()
                });
            }
        }, [context]);
    }
    
    // 创建异步命令钩子
    static useAsyncCommand(name, asyncAction, options = {}) {
        const commandService = useService("command");
        const [isLoading, setIsLoading] = useState(false);
        
        const wrappedAction = async () => {
            if (isLoading) return;
            
            setIsLoading(true);
            try {
                await asyncAction();
            } catch (error) {
                console.error(`Command ${name} failed:`, error);
                if (options.onError) {
                    options.onError(error);
                }
            } finally {
                setIsLoading(false);
            }
        };
        
        useEffect(() => {
            return commandService.add(name, wrappedAction, {
                ...options,
                isLoading: () => isLoading
            });
        }, [isLoading]);
        
        return { isLoading };
    }
    
    // 创建批量命令钩子
    static useBatchCommands(commandDefinitions) {
        const commandService = useService("command");
        
        useEffect(() => {
            const cleanupFunctions = [];
            
            commandDefinitions.forEach(({ name, action, options = {} }) => {
                const cleanup = commandService.add(name, action, options);
                cleanupFunctions.push(cleanup);
            });
            
            return () => {
                cleanupFunctions.forEach(cleanup => cleanup && cleanup());
            };
        }, []);
    }
    
    // 创建动态命令钩子
    static useDynamicCommands(commandProvider, dependencies = []) {
        const commandService = useService("command");
        const [commands, setCommands] = useState([]);
        
        useEffect(() => {
            const newCommands = commandProvider();
            setCommands(newCommands);
        }, dependencies);
        
        useEffect(() => {
            const cleanupFunctions = [];
            
            commands.forEach(({ name, action, options = {} }) => {
                const cleanup = commandService.add(name, action, options);
                cleanupFunctions.push(cleanup);
            });
            
            return () => {
                cleanupFunctions.forEach(cleanup => cleanup && cleanup());
            };
        }, [commands]);
    }
}

// 使用示例
class AdvancedComponent extends Component {
    setup() {
        this.currentRecord = useState(null);
        this.selectedRecords = useState([]);
        
        // 条件命令
        AdvancedCommandHooks.useConditionalCommand(
            "edit_record",
            () => this.editCurrentRecord(),
            () => !!this.currentRecord.value,
            { category: "smart_action" }
        );
        
        // 命令组
        AdvancedCommandHooks.useCommandGroup("record_operations", [
            {
                name: "create_record",
                action: () => this.createRecord(),
                commandOptions: { category: "app" }
            },
            {
                name: "duplicate_record",
                action: () => this.duplicateRecord(),
                commandOptions: { category: "actions" }
            }
        ]);
        
        // 快捷键命令
        AdvancedCommandHooks.useHotkeyCommand(
            "save_record",
            () => this.saveRecord(),
            "ctrl+s",
            { category: "app" }
        );
        
        // 异步命令
        const { isLoading } = AdvancedCommandHooks.useAsyncCommand(
            "export_data",
            async () => {
                await this.exportDataAsync();
            },
            {
                category: "actions",
                onError: (error) => this.showError(error)
            }
        );
        
        // 动态命令
        AdvancedCommandHooks.useDynamicCommands(
            () => this.generateBulkCommands(),
            [this.selectedRecords.value]
        );
    }
    
    generateBulkCommands() {
        if (this.selectedRecords.value.length === 0) {
            return [];
        }
        
        return [
            {
                name: "bulk_delete",
                action: () => this.bulkDelete(),
                options: {
                    category: "actions",
                    description: `Delete ${this.selectedRecords.value.length} records`
                }
            },
            {
                name: "bulk_export",
                action: () => this.bulkExport(),
                options: {
                    category: "actions",
                    description: `Export ${this.selectedRecords.value.length} records`
                }
            }
        ];
    }
    
    editCurrentRecord() {
        console.log("Editing record:", this.currentRecord.value.id);
    }
    
    createRecord() {
        console.log("Creating new record");
    }
    
    duplicateRecord() {
        console.log("Duplicating record");
    }
    
    saveRecord() {
        console.log("Saving record");
    }
    
    async exportDataAsync() {
        // 模拟异步导出
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log("Data exported successfully");
    }
    
    bulkDelete() {
        console.log("Bulk deleting records:", this.selectedRecords.value);
    }
    
    bulkExport() {
        console.log("Bulk exporting records:", this.selectedRecords.value);
    }
    
    showError(error) {
        console.error("Command error:", error);
    }
}
```

### 2. 命令钩子测试工具
```javascript
class CommandHookTester {
    constructor() {
        this.registeredCommands = new Map();
        this.commandHistory = [];
        this.mockCommandService = this.createMockCommandService();
    }

    createMockCommandService() {
        return {
            add: (name, action, options = {}) => {
                const command = {
                    name,
                    action,
                    options,
                    registeredAt: Date.now(),
                    callCount: 0,
                    lastCalled: null
                };

                this.registeredCommands.set(name, command);
                this.commandHistory.push({
                    type: 'register',
                    name,
                    timestamp: Date.now()
                });

                // 返回清理函数
                return () => {
                    this.registeredCommands.delete(name);
                    this.commandHistory.push({
                        type: 'unregister',
                        name,
                        timestamp: Date.now()
                    });
                };
            },

            execute: (name, ...args) => {
                const command = this.registeredCommands.get(name);
                if (command) {
                    command.callCount++;
                    command.lastCalled = Date.now();

                    this.commandHistory.push({
                        type: 'execute',
                        name,
                        args,
                        timestamp: Date.now()
                    });

                    return command.action(...args);
                } else {
                    throw new Error(`Command "${name}" not found`);
                }
            },

            getRegisteredCommands: () => {
                return Array.from(this.registeredCommands.keys());
            },

            getCommandInfo: (name) => {
                return this.registeredCommands.get(name);
            }
        };
    }

    // 测试命令注册
    testCommandRegistration(componentClass, props = {}) {
        const testResults = {
            registeredCommands: [],
            errors: [],
            warnings: []
        };

        try {
            // 模拟组件挂载
            const component = new componentClass();
            component.setup();

            // 检查注册的命令
            testResults.registeredCommands = this.mockCommandService.getRegisteredCommands();

            // 验证命令配置
            testResults.registeredCommands.forEach(commandName => {
                const commandInfo = this.mockCommandService.getCommandInfo(commandName);
                this.validateCommandConfiguration(commandName, commandInfo, testResults);
            });

        } catch (error) {
            testResults.errors.push({
                type: 'setup_error',
                message: error.message,
                stack: error.stack
            });
        }

        return testResults;
    }

    validateCommandConfiguration(name, commandInfo, testResults) {
        // 检查命令名称
        if (!name || typeof name !== 'string') {
            testResults.errors.push({
                type: 'invalid_name',
                command: name,
                message: 'Command name must be a non-empty string'
            });
        }

        // 检查动作函数
        if (!commandInfo.action || typeof commandInfo.action !== 'function') {
            testResults.errors.push({
                type: 'invalid_action',
                command: name,
                message: 'Command action must be a function'
            });
        }

        // 检查选项配置
        const options = commandInfo.options || {};

        if (options.category && !this.isValidCategory(options.category)) {
            testResults.warnings.push({
                type: 'unknown_category',
                command: name,
                message: `Unknown category: ${options.category}`
            });
        }

        if (options.hotkey && !this.isValidHotkey(options.hotkey)) {
            testResults.warnings.push({
                type: 'invalid_hotkey',
                command: name,
                message: `Invalid hotkey format: ${options.hotkey}`
            });
        }
    }

    isValidCategory(category) {
        const validCategories = ['app', 'smart_action', 'actions', 'default', 'view_switcher', 'debug', 'disabled'];
        return validCategories.includes(category);
    }

    isValidHotkey(hotkey) {
        // 简单的快捷键格式验证
        const hotkeyPattern = /^(ctrl\+|alt\+|shift\+|meta\+)*[a-z0-9]$/i;
        return hotkeyPattern.test(hotkey);
    }

    // 测试命令执行
    testCommandExecution(commandName, ...args) {
        const testResult = {
            success: false,
            result: null,
            error: null,
            executionTime: 0
        };

        const startTime = performance.now();

        try {
            testResult.result = this.mockCommandService.execute(commandName, ...args);
            testResult.success = true;
        } catch (error) {
            testResult.error = {
                message: error.message,
                stack: error.stack
            };
        }

        testResult.executionTime = performance.now() - startTime;

        return testResult;
    }

    // 测试命令生命周期
    testCommandLifecycle(componentClass) {
        const lifecycleTest = {
            phases: [],
            errors: [],
            commandCount: 0
        };

        try {
            // 挂载前
            lifecycleTest.phases.push({
                phase: 'before_mount',
                commandCount: this.registeredCommands.size,
                timestamp: Date.now()
            });

            // 模拟组件挂载
            const component = new componentClass();
            component.setup();

            lifecycleTest.phases.push({
                phase: 'after_mount',
                commandCount: this.registeredCommands.size,
                timestamp: Date.now()
            });

            // 模拟组件卸载
            // 这里需要手动调用清理函数
            const cleanupFunctions = Array.from(this.registeredCommands.values())
                .map(cmd => cmd.cleanup)
                .filter(Boolean);

            cleanupFunctions.forEach(cleanup => cleanup());

            lifecycleTest.phases.push({
                phase: 'after_unmount',
                commandCount: this.registeredCommands.size,
                timestamp: Date.now()
            });

        } catch (error) {
            lifecycleTest.errors.push({
                phase: 'lifecycle_test',
                message: error.message,
                stack: error.stack
            });
        }

        return lifecycleTest;
    }

    // 生成测试报告
    generateTestReport() {
        const report = {
            summary: {
                totalCommands: this.registeredCommands.size,
                totalHistory: this.commandHistory.length,
                registrations: this.commandHistory.filter(h => h.type === 'register').length,
                unregistrations: this.commandHistory.filter(h => h.type === 'unregister').length,
                executions: this.commandHistory.filter(h => h.type === 'execute').length
            },
            commands: Array.from(this.registeredCommands.entries()).map(([name, info]) => ({
                name,
                callCount: info.callCount,
                lastCalled: info.lastCalled,
                registeredAt: info.registeredAt,
                options: info.options
            })),
            history: this.commandHistory.slice(-20), // 最近20条历史
            performance: this.analyzePerformance()
        };

        return report;
    }

    analyzePerformance() {
        const executions = this.commandHistory.filter(h => h.type === 'execute');

        if (executions.length === 0) {
            return { averageExecutionTime: 0, totalExecutions: 0 };
        }

        const commandStats = {};

        executions.forEach(execution => {
            if (!commandStats[execution.name]) {
                commandStats[execution.name] = {
                    count: 0,
                    totalTime: 0
                };
            }

            commandStats[execution.name].count++;
            // 这里应该记录实际的执行时间
            commandStats[execution.name].totalTime += 10; // 模拟执行时间
        });

        return {
            totalExecutions: executions.length,
            commandStats: Object.entries(commandStats).map(([name, stats]) => ({
                name,
                count: stats.count,
                averageTime: stats.totalTime / stats.count
            }))
        };
    }

    // 重置测试环境
    reset() {
        this.registeredCommands.clear();
        this.commandHistory = [];
    }
}

// 使用示例
const tester = new CommandHookTester();

// 测试组件的命令注册
const testResults = tester.testCommandRegistration(MyComponent);
console.log('Registration test results:', testResults);

// 测试命令执行
const executionResult = tester.testCommandExecution('create_record');
console.log('Execution test result:', executionResult);

// 测试生命周期
const lifecycleResult = tester.testCommandLifecycle(MyComponent);
console.log('Lifecycle test result:', lifecycleResult);

// 生成测试报告
const report = tester.generateTestReport();
console.log('Test report:', report);
```

## 🔧 调试技巧

### 命令钩子状态监控
```javascript
function monitorCommandHooks() {
    const originalUseCommand = useCommand;

    window.useCommand = function(name, action, options = {}) {
        console.log(`🎯 Registering command: ${name}`, {
            action: action.toString().substring(0, 100) + '...',
            options
        });

        return originalUseCommand(name, action, options);
    };

    console.log('Command hook monitoring enabled');
}

// 启用监控
monitorCommandHooks();
```

### 命令执行追踪
```javascript
function traceCommandExecution() {
    const commandService = odoo.__DEBUG__.services.command;
    const originalAdd = commandService.add;

    commandService.add = function(name, action, options) {
        const wrappedAction = function(...args) {
            console.log(`⚡ Executing command: ${name}`, args);
            const startTime = performance.now();

            try {
                const result = action.apply(this, args);
                const endTime = performance.now();
                console.log(`✅ Command ${name} completed in ${endTime - startTime}ms`);
                return result;
            } catch (error) {
                console.error(`❌ Command ${name} failed:`, error);
                throw error;
            }
        };

        return originalAdd.call(this, name, wrappedAction, options);
    };

    console.log('Command execution tracing enabled');
}

// 启用追踪
traceCommandExecution();
```

## 📊 性能考虑

### 优化策略
1. **依赖优化**: 合理设置useEffect的依赖数组
2. **内存管理**: 确保命令在组件卸载时正确清理
3. **批量注册**: 对于多个相关命令，考虑批量注册
4. **条件注册**: 只在需要时注册命令，避免不必要的开销

### 最佳实践
```javascript
// ✅ 好的做法：空依赖数组，只注册一次
useCommand("my_command", () => {
    this.doSomething();
});

// ❌ 不好的做法：缺少依赖数组，每次渲染都注册
useCommand("my_command", () => {
    this.doSomething();
}, {}, undefined); // 缺少依赖数组

// ✅ 好的做法：使用条件注册
if (this.shouldRegisterCommand) {
    useCommand("conditional_command", () => {
        this.conditionalAction();
    });
}

// ❌ 不好的做法：总是注册然后在执行时检查条件
useCommand("conditional_command", () => {
    if (this.shouldExecute) {
        this.conditionalAction();
    }
});
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解React Hooks模式在Odoo中的应用
- [ ] 掌握组件生命周期与命令管理的集成
- [ ] 理解自动化资源管理的设计模式
- [ ] 能够实现声明式编程在命令系统中的应用
- [ ] 掌握企业级钩子系统的设计原理
- [ ] 了解命令钩子的测试和调试技术

## 🚀 下一步学习
学完命令钩子后，建议继续学习：
1. **命令服务** (`@web/core/commands/command_service.js`) - 学习命令服务实现
2. **命令面板** (`@web/core/commands/command_palette.js`) - 理解命令面板UI
3. **默认提供者** (`@web/core/commands/default_providers.js`) - 掌握默认命令

## 💡 重要提示
- 命令钩子简化了命令的生命周期管理
- 正确的依赖数组设置对性能很重要
- 自动清理机制防止内存泄漏
- 声明式API提高了代码的可读性和维护性
```

# @web/core/commands/default_providers.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/commands/default_providers.js`
- **原始路径**: `/web/static/src/core/commands/default_providers.js`
- **代码行数**: 115行
- **作用**: 提供默认的命令提供者，包括注册命令和数据快捷键命令的自动发现

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 命令提供者模式的设计和实现
- 自动命令发现和生成机制
- 快捷键命令的动态创建
- DOM元素到命令的映射策略
- 企业级命令系统的扩展机制

## 📚 核心概念

### 什么是命令提供者？
命令提供者是一个**命令生成器**，主要功能：
- **动态生成**: 根据当前上下文动态生成可用命令
- **自动发现**: 自动发现页面中的可交互元素
- **统一接口**: 提供统一的命令接口和格式
- **分类管理**: 自动分类和组织生成的命令

### 核心架构组成
```javascript
// 命令提供者注册表
const commandProviderRegistry = registry.category("command_provider");

// 默认提供者
const providers = {
    command: {           // 注册命令提供者
        provide: (env, options) => registeredCommands
    },
    "data-hotkeys": {    // 数据快捷键提供者
        provide: (env, options) => hotkeyCommands
    }
};

// 命令设置注册表
const commandSetupRegistry = registry.category("command_setup");

// 快捷键命令组件
const HotkeyCommandItem = Component;
```

### 基本使用模式
```javascript
import { registry } from '@web/core/registry';

// 注册自定义命令提供者
const commandProviderRegistry = registry.category("command_provider");

commandProviderRegistry.add("my_provider", {
    provide: (env, options = {}) => {
        return [
            {
                Component: DefaultCommandItem,
                action: () => this.myAction(),
                category: "app",
                name: "My Custom Command",
                props: {}
            }
        ];
    }
});

// 注册命令设置
const commandSetupRegistry = registry.category("command_setup");

commandSetupRegistry.add("my_namespace", {
    emptyMessage: "No custom commands found",
    placeholder: "Search custom commands...",
    debounceDelay: 200
});
```

## 🔍 核心实现详解

### 1. 命令设置注册

#### 默认设置配置
```javascript
const commandSetupRegistry = registry.category("command_setup");
commandSetupRegistry.add("default", {
    emptyMessage: _t("No command found"),
    placeholder: _t("Search for a command..."),
});
```

**设置配置特点**：
- **国际化**: 使用_t函数支持多语言
- **默认命名空间**: 为default命名空间提供基础配置
- **用户体验**: 提供友好的空状态和占位符文本
- **可扩展**: 其他模块可以添加自己的命名空间配置

### 2. 快捷键命令组件

#### HotkeyCommandItem组件实现
```javascript
const HotkeyCommandItem = class HotkeyCommandItem extends Component {
    static template = "web.HotkeyCommandItem";
    static props = ["hotkey", "hotkeyOptions?", "name?", "searchValue?", "executeCommand", "slots"];
    
    setup() {
        useHotkey(this.props.hotkey, this.props.executeCommand);
    }
    
    getKeysToPress(command) {
        const { hotkey } = command;
        let result = hotkey.split("+");
        if (isMacOS()) {
            result = result
                .map((x) => x.replace("control", "command"))
                .map((x) => x.replace("alt", "control"));
        }
        return result.map((key) => key.toUpperCase());
    }
}
```

**组件设计特点**：
- **快捷键绑定**: 自动绑定快捷键到命令执行
- **平台适配**: 根据操作系统调整快捷键显示
- **键位映射**: 将快捷键字符串转换为显示格式
- **模板驱动**: 使用模板系统渲染UI

#### 平台快捷键适配
```javascript
getKeysToPress(command) {
    const { hotkey } = command;
    let result = hotkey.split("+");
    if (isMacOS()) {
        result = result
            .map((x) => x.replace("control", "command"))
            .map((x) => x.replace("alt", "control"));
    }
    return result.map((key) => key.toUpperCase());
}
```

**适配策略**：
- **操作系统检测**: 使用isMacOS()检测当前平台
- **键位映射**: macOS上将control映射为command
- **Alt键处理**: macOS上将alt映射为control
- **大写转换**: 统一转换为大写显示

### 3. 注册命令提供者

#### 命令提供者实现
```javascript
commandProviderRegistry.add("command", {
    provide: (env, options = {}) => {
        const commands = env.services.command
            .getCommands(options.activeElement)
            .map((cmd) => {
                cmd.category = commandCategoryRegistry.contains(cmd.category)
                    ? cmd.category
                    : "default";
                return cmd;
            })
            .filter((command) => command.isAvailable === undefined || command.isAvailable());
        
        // 过滤重复命令
        const uniqueCommands = commands.filter((obj, index) => {
            return (
                index ===
                commands.findIndex((o) => obj.name === o.name && obj.category === o.category)
            );
        });
        
        return uniqueCommands.map((command) => ({
            Component: command.hotkey ? HotkeyCommandItem : DefaultCommandItem,
            action: command.action,
            category: command.category,
            name: command.name,
            props: {
                hotkey: command.hotkey,
                hotkeyOptions: command.hotkeyOptions,
            },
        }));
    },
});
```

**提供者逻辑分析**：
- **命令获取**: 从命令服务获取当前上下文的命令
- **分类验证**: 验证命令分类是否存在，不存在则归为default
- **可用性过滤**: 过滤掉不可用的命令
- **去重处理**: 移除同名同分类的重复命令
- **组件选择**: 根据是否有快捷键选择合适的组件

#### 去重算法
```javascript
const uniqueCommands = commands.filter((obj, index) => {
    return (
        index ===
        commands.findIndex((o) => obj.name === o.name && obj.category === o.category)
    );
});
```

**去重策略**：
- **双重条件**: 同时比较命令名称和分类
- **首次保留**: 保留第一次出现的命令
- **索引比较**: 使用findIndex确保唯一性
- **性能考虑**: 虽然O(n²)复杂度，但命令数量通常不大

### 4. 数据快捷键提供者

#### 自动发现机制
```javascript
commandProviderRegistry.add("data-hotkeys", {
    provide: (env, options = {}) => {
        const commands = [];
        const overlayModifier = registry.category("services").get("hotkey").overlayModifier;
        
        // 获取所有可见的快捷键元素
        for (const el of getVisibleElements(
            options.activeElement,
            "[data-hotkey]:not(:disabled)"
        )) {
            const closest = el.closest("[data-command-category]");
            const category = closest ? closest.dataset.commandCategory : "default";
            if (category === "disabled") {
                continue;
            }
            
            const description = 
                el.title ||
                el.dataset.bsOriginalTitle || // Bootstrap遗留支持
                el.dataset.tooltip ||
                el.placeholder ||
                (el.innerText && 
                    `${el.innerText.slice(0, 50)}${el.innerText.length > 50 ? "..." : ""}`) ||
                _t("no description provided");
            
            commands.push({
                Component: HotkeyCommandItem,
                action: () => {
                    el.focus();
                    el.click();
                },
                category,
                name: capitalize(description.trim().toLowerCase()),
                props: {
                    hotkey: `${overlayModifier}+${el.dataset.hotkey}`,
                },
            });
        }
        return commands;
    },
});
```

**自动发现特点**：
- **DOM查询**: 查找所有带data-hotkey属性的可见元素
- **分类继承**: 从最近的父元素继承命令分类
- **描述提取**: 从多个属性中提取元素描述
- **动作模拟**: 通过focus和click模拟用户交互

#### 描述提取策略
```javascript
const description =
    el.title ||
    el.dataset.bsOriginalTitle || // LEGACY: bootstrap moves title to data-bs-original-title
    el.dataset.tooltip ||
    el.placeholder ||
    (el.innerText &&
        `${el.innerText.slice(0, 50)}${el.innerText.length > 50 ? "..." : ""}`) ||
    _t("no description provided");
```

**提取优先级**：
1. **title属性**: 标准的HTML title属性
2. **Bootstrap遗留**: 支持Bootstrap的title迁移
3. **tooltip数据**: 自定义tooltip属性
4. **placeholder**: 输入框的占位符文本
5. **内容文本**: 元素的文本内容（截断到50字符）
6. **默认描述**: 提供默认的"无描述"文本

## 🎨 实际应用场景

### 1. 高级命令提供者系统
```javascript
class AdvancedCommandProviderManager {
    constructor() {
        this.providers = new Map();
        this.providerMiddleware = [];
        this.commandFilters = new Map();
        this.commandTransformers = new Map();
        this.setupDefaultProviders();
    }
    
    setupDefaultProviders() {
        // 上下文感知命令提供者
        this.addProvider("context_aware", {
            provide: (env, options = {}) => {
                const commands = [];
                const activeElement = options.activeElement;
                
                // 基于当前视图生成命令
                if (activeElement) {
                    const viewType = this.detectViewType(activeElement);
                    commands.push(...this.generateViewCommands(viewType, env));
                    
                    // 基于选中内容生成命令
                    const selection = this.getSelection(activeElement);
                    if (selection) {
                        commands.push(...this.generateSelectionCommands(selection, env));
                    }
                    
                    // 基于表单状态生成命令
                    const formState = this.getFormState(activeElement);
                    if (formState) {
                        commands.push(...this.generateFormCommands(formState, env));
                    }
                }
                
                return commands;
            }
        });
        
        // 智能建议命令提供者
        this.addProvider("smart_suggestions", {
            provide: (env, options = {}) => {
                const commands = [];
                const userHistory = this.getUserCommandHistory();
                const currentContext = this.getCurrentContext(options);
                
                // 基于历史使用生成建议
                const suggestions = this.generateSmartSuggestions(userHistory, currentContext);
                
                suggestions.forEach(suggestion => {
                    commands.push({
                        Component: DefaultCommandItem,
                        action: suggestion.action,
                        category: "smart_action",
                        name: `💡 ${suggestion.name}`,
                        props: {
                            description: suggestion.reason,
                            confidence: suggestion.confidence
                        }
                    });
                });
                
                return commands;
            }
        });
        
        // 插件命令提供者
        this.addProvider("plugins", {
            provide: (env, options = {}) => {
                const commands = [];
                const enabledPlugins = this.getEnabledPlugins();
                
                enabledPlugins.forEach(plugin => {
                    if (plugin.commands && typeof plugin.commands.provide === 'function') {
                        const pluginCommands = plugin.commands.provide(env, options);
                        commands.push(...pluginCommands.map(cmd => ({
                            ...cmd,
                            category: cmd.category || "plugins",
                            name: `🔌 ${cmd.name}`,
                            plugin: plugin.id
                        })));
                    }
                });
                
                return commands;
            }
        });
        
        // 快速访问命令提供者
        this.addProvider("quick_access", {
            provide: (env, options = {}) => {
                const commands = [];
                const quickAccessItems = this.getQuickAccessItems();
                
                quickAccessItems.forEach(item => {
                    commands.push({
                        Component: HotkeyCommandItem,
                        action: () => this.navigateToQuickAccess(item),
                        category: "app",
                        name: `⚡ ${item.name}`,
                        props: {
                            hotkey: item.hotkey,
                            description: item.description
                        }
                    });
                });
                
                return commands;
            }
        });
    }
    
    addProvider(name, provider) {
        this.providers.set(name, provider);
        
        // 注册到全局注册表
        const commandProviderRegistry = registry.category("command_provider");
        commandProviderRegistry.add(name, provider);
    }
    
    // 检测视图类型
    detectViewType(activeElement) {
        if (activeElement.closest('.o_form_view')) return 'form';
        if (activeElement.closest('.o_list_view')) return 'list';
        if (activeElement.closest('.o_kanban_view')) return 'kanban';
        if (activeElement.closest('.o_calendar_view')) return 'calendar';
        if (activeElement.closest('.o_graph_view')) return 'graph';
        if (activeElement.closest('.o_pivot_view')) return 'pivot';
        return 'unknown';
    }
    
    // 生成视图相关命令
    generateViewCommands(viewType, env) {
        const commands = [];
        
        switch (viewType) {
            case 'form':
                commands.push(
                    {
                        Component: DefaultCommandItem,
                        action: () => this.saveRecord(env),
                        category: "app",
                        name: "Save Record",
                        props: { hotkey: "ctrl+s" }
                    },
                    {
                        Component: DefaultCommandItem,
                        action: () => this.duplicateRecord(env),
                        category: "actions",
                        name: "Duplicate Record"
                    }
                );
                break;
                
            case 'list':
                commands.push(
                    {
                        Component: DefaultCommandItem,
                        action: () => this.createRecord(env),
                        category: "app",
                        name: "Create New Record",
                        props: { hotkey: "ctrl+n" }
                    },
                    {
                        Component: DefaultCommandItem,
                        action: () => this.exportRecords(env),
                        category: "actions",
                        name: "Export Records"
                    }
                );
                break;
                
            case 'kanban':
                commands.push(
                    {
                        Component: DefaultCommandItem,
                        action: () => this.addColumn(env),
                        category: "actions",
                        name: "Add Column"
                    }
                );
                break;
        }
        
        return commands;
    }
    
    // 获取选中内容
    getSelection(activeElement) {
        const selection = window.getSelection();
        if (selection.rangeCount > 0 && !selection.isCollapsed) {
            return {
                text: selection.toString(),
                range: selection.getRangeAt(0),
                element: activeElement
            };
        }
        return null;
    }
    
    // 生成选中内容相关命令
    generateSelectionCommands(selection, env) {
        const commands = [];
        
        commands.push(
            {
                Component: DefaultCommandItem,
                action: () => navigator.clipboard.writeText(selection.text),
                category: "actions",
                name: `Copy "${selection.text.substring(0, 20)}..."`
            },
            {
                Component: DefaultCommandItem,
                action: () => this.searchSelected(selection.text, env),
                category: "actions",
                name: `Search for "${selection.text.substring(0, 20)}..."`
            }
        );
        
        // 如果选中的是数字，添加计算命令
        if (/^\d+(\.\d+)?$/.test(selection.text.trim())) {
            commands.push({
                Component: DefaultCommandItem,
                action: () => this.openCalculator(selection.text, env),
                category: "actions",
                name: `Calculate with ${selection.text}`
            });
        }
        
        // 如果选中的是邮箱，添加邮件命令
        if (/\S+@\S+\.\S+/.test(selection.text)) {
            commands.push({
                Component: DefaultCommandItem,
                action: () => this.composeEmail(selection.text, env),
                category: "actions",
                name: `Send email to ${selection.text}`
            });
        }
        
        return commands;
    }
    
    // 获取表单状态
    getFormState(activeElement) {
        const form = activeElement.closest('form, .o_form_view');
        if (!form) return null;
        
        const isDirty = form.classList.contains('o_form_dirty');
        const isNew = form.classList.contains('o_form_create');
        const isReadonly = form.classList.contains('o_form_readonly');
        
        return { isDirty, isNew, isReadonly, form };
    }
    
    // 生成表单相关命令
    generateFormCommands(formState, env) {
        const commands = [];
        
        if (formState.isDirty) {
            commands.push(
                {
                    Component: DefaultCommandItem,
                    action: () => this.saveForm(formState.form, env),
                    category: "app",
                    name: "Save Changes",
                    props: { hotkey: "ctrl+s" }
                },
                {
                    Component: DefaultCommandItem,
                    action: () => this.discardChanges(formState.form, env),
                    category: "actions",
                    name: "Discard Changes"
                }
            );
        }
        
        if (!formState.isReadonly) {
            commands.push({
                Component: DefaultCommandItem,
                action: () => this.toggleEditMode(formState.form, env),
                category: "actions",
                name: formState.isNew ? "Switch to View Mode" : "Edit Record"
            });
        }
        
        return commands;
    }
    
    // 智能建议生成
    generateSmartSuggestions(userHistory, currentContext) {
        const suggestions = [];
        
        // 基于时间模式的建议
        const timeBasedSuggestions = this.analyzeTimePatterns(userHistory, currentContext);
        suggestions.push(...timeBasedSuggestions);
        
        // 基于频率的建议
        const frequencyBasedSuggestions = this.analyzeFrequencyPatterns(userHistory, currentContext);
        suggestions.push(...frequencyBasedSuggestions);
        
        // 基于上下文的建议
        const contextBasedSuggestions = this.analyzeContextPatterns(userHistory, currentContext);
        suggestions.push(...contextBasedSuggestions);
        
        return suggestions.sort((a, b) => b.confidence - a.confidence).slice(0, 5);
    }
    
    analyzeTimePatterns(userHistory, currentContext) {
        const suggestions = [];
        const currentHour = new Date().getHours();
        
        // 分析用户在当前时间段的常用命令
        const timeSlotCommands = userHistory.filter(entry => {
            const entryHour = new Date(entry.timestamp).getHours();
            return Math.abs(entryHour - currentHour) <= 1;
        });
        
        const commandFrequency = {};
        timeSlotCommands.forEach(entry => {
            commandFrequency[entry.command] = (commandFrequency[entry.command] || 0) + 1;
        });
        
        Object.entries(commandFrequency)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3)
            .forEach(([command, frequency]) => {
                suggestions.push({
                    name: `${command} (Usually used at this time)`,
                    action: () => this.executeHistoryCommand(command),
                    reason: `You typically use this command around ${currentHour}:00`,
                    confidence: Math.min(frequency / timeSlotCommands.length * 100, 90)
                });
            });
        
        return suggestions;
    }
    
    analyzeFrequencyPatterns(userHistory, currentContext) {
        const suggestions = [];
        const commandFrequency = {};
        
        userHistory.forEach(entry => {
            commandFrequency[entry.command] = (commandFrequency[entry.command] || 0) + 1;
        });
        
        Object.entries(commandFrequency)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3)
            .forEach(([command, frequency]) => {
                suggestions.push({
                    name: `${command} (Frequently used)`,
                    action: () => this.executeHistoryCommand(command),
                    reason: `You've used this command ${frequency} times`,
                    confidence: Math.min(frequency / userHistory.length * 100, 85)
                });
            });
        
        return suggestions;
    }
    
    analyzeContextPatterns(userHistory, currentContext) {
        const suggestions = [];
        
        // 基于当前页面/模块的历史命令
        const contextCommands = userHistory.filter(entry => 
            entry.context && entry.context.module === currentContext.module
        );
        
        if (contextCommands.length > 0) {
            const recentCommand = contextCommands[contextCommands.length - 1];
            suggestions.push({
                name: `${recentCommand.command} (Last used in this context)`,
                action: () => this.executeHistoryCommand(recentCommand.command),
                reason: `Last used in ${currentContext.module}`,
                confidence: 75
            });
        }
        
        return suggestions;
    }
    
    // 获取用户命令历史
    getUserCommandHistory() {
        try {
            const history = localStorage.getItem('user_command_history');
            return history ? JSON.parse(history) : [];
        } catch {
            return [];
        }
    }
    
    // 获取当前上下文
    getCurrentContext(options) {
        return {
            module: this.getCurrentModule(),
            view: this.detectViewType(options.activeElement),
            time: new Date(),
            url: window.location.pathname
        };
    }
    
    getCurrentModule() {
        // 从URL或DOM中提取当前模块信息
        const pathParts = window.location.pathname.split('/');
        return pathParts[pathParts.length - 1] || 'unknown';
    }
    
    // 获取启用的插件
    getEnabledPlugins() {
        // 模拟插件系统
        return [
            {
                id: 'calculator',
                name: 'Calculator Plugin',
                commands: {
                    provide: (env, options) => [
                        {
                            name: 'Open Calculator',
                            action: () => this.openCalculator(),
                            category: 'tools'
                        }
                    ]
                }
            },
            {
                id: 'notes',
                name: 'Notes Plugin',
                commands: {
                    provide: (env, options) => [
                        {
                            name: 'Quick Note',
                            action: () => this.createQuickNote(),
                            category: 'tools'
                        }
                    ]
                }
            }
        ];
    }
    
    // 获取快速访问项目
    getQuickAccessItems() {
        return [
            {
                name: 'Dashboard',
                hotkey: 'ctrl+shift+d',
                description: 'Go to main dashboard',
                url: '/dashboard'
            },
            {
                name: 'Settings',
                hotkey: 'ctrl+shift+s',
                description: 'Open settings',
                url: '/settings'
            },
            {
                name: 'Help',
                hotkey: 'f1',
                description: 'Open help documentation',
                url: '/help'
            }
        ];
    }
    
    // 导航到快速访问项目
    navigateToQuickAccess(item) {
        window.location.href = item.url;
    }
    
    // 执行历史命令
    executeHistoryCommand(commandName) {
        console.log(`Executing history command: ${commandName}`);
        // 实际实现中应该查找并执行对应的命令
    }
}

// 使用示例
const providerManager = new AdvancedCommandProviderManager();

// 添加自定义提供者
providerManager.addProvider("custom_workflow", {
    provide: (env, options = {}) => {
        return [
            {
                Component: DefaultCommandItem,
                action: () => console.log("Custom workflow executed"),
                category: "workflow",
                name: "Execute Custom Workflow"
            }
        ];
    }
});
```

### 2. 动态命令生成器
```javascript
class DynamicCommandGenerator {
    constructor() {
        this.generators = new Map();
        this.templates = new Map();
        this.conditions = new Map();
        this.setupDefaultGenerators();
    }

    setupDefaultGenerators() {
        // CRUD操作生成器
        this.addGenerator('crud_operations', {
            condition: (context) => context.model && context.recordId,
            generate: (context) => {
                const commands = [];
                const model = context.model;
                const recordId = context.recordId;

                commands.push(
                    {
                        name: `Edit ${model}`,
                        action: () => this.editRecord(model, recordId),
                        category: 'actions',
                        icon: 'fa-edit'
                    },
                    {
                        name: `Delete ${model}`,
                        action: () => this.deleteRecord(model, recordId),
                        category: 'actions',
                        icon: 'fa-trash',
                        confirmMessage: `Are you sure you want to delete this ${model}?`
                    },
                    {
                        name: `Duplicate ${model}`,
                        action: () => this.duplicateRecord(model, recordId),
                        category: 'actions',
                        icon: 'fa-copy'
                    }
                );

                return commands;
            }
        });

        // 关系字段生成器
        this.addGenerator('relation_fields', {
            condition: (context) => context.relationFields && context.relationFields.length > 0,
            generate: (context) => {
                const commands = [];

                context.relationFields.forEach(field => {
                    commands.push({
                        name: `Browse ${field.label}`,
                        action: () => this.browseRelation(field),
                        category: 'smart_action',
                        icon: 'fa-external-link'
                    });

                    if (field.canCreate) {
                        commands.push({
                            name: `Create ${field.label}`,
                            action: () => this.createRelatedRecord(field),
                            category: 'app',
                            icon: 'fa-plus'
                        });
                    }
                });

                return commands;
            }
        });

        // 工作流生成器
        this.addGenerator('workflow_actions', {
            condition: (context) => context.workflowState && context.availableTransitions,
            generate: (context) => {
                const commands = [];

                context.availableTransitions.forEach(transition => {
                    commands.push({
                        name: transition.label,
                        action: () => this.executeTransition(transition),
                        category: 'workflow',
                        icon: transition.icon || 'fa-arrow-right',
                        description: transition.description
                    });
                });

                return commands;
            }
        });

        // 报表生成器
        this.addGenerator('reports', {
            condition: (context) => context.model && context.availableReports,
            generate: (context) => {
                const commands = [];

                context.availableReports.forEach(report => {
                    commands.push({
                        name: `Generate ${report.name}`,
                        action: () => this.generateReport(report, context),
                        category: 'actions',
                        icon: 'fa-file-pdf',
                        description: report.description
                    });
                });

                return commands;
            }
        });
    }

    addGenerator(name, generator) {
        this.generators.set(name, generator);
    }

    generateCommands(context) {
        const allCommands = [];

        for (const [name, generator] of this.generators.entries()) {
            try {
                if (generator.condition(context)) {
                    const commands = generator.generate(context);
                    allCommands.push(...commands.map(cmd => ({
                        ...cmd,
                        generator: name,
                        Component: cmd.Component || DefaultCommandItem
                    })));
                }
            } catch (error) {
                console.warn(`Generator ${name} failed:`, error);
            }
        }

        return allCommands;
    }

    // 创建动态提供者
    createDynamicProvider() {
        return {
            provide: (env, options = {}) => {
                const context = this.extractContext(env, options);
                return this.generateCommands(context);
            }
        };
    }

    extractContext(env, options) {
        const context = {
            activeElement: options.activeElement,
            model: this.extractModel(options.activeElement),
            recordId: this.extractRecordId(options.activeElement),
            relationFields: this.extractRelationFields(options.activeElement),
            workflowState: this.extractWorkflowState(options.activeElement),
            availableTransitions: this.extractAvailableTransitions(options.activeElement),
            availableReports: this.extractAvailableReports(options.activeElement)
        };

        return context;
    }

    extractModel(activeElement) {
        const modelElement = activeElement.closest('[data-model]');
        return modelElement ? modelElement.dataset.model : null;
    }

    extractRecordId(activeElement) {
        const recordElement = activeElement.closest('[data-record-id]');
        return recordElement ? recordElement.dataset.recordId : null;
    }

    extractRelationFields(activeElement) {
        const relationElements = activeElement.querySelectorAll('[data-relation-field]');
        return Array.from(relationElements).map(el => ({
            name: el.dataset.relationField,
            label: el.dataset.relationLabel || el.dataset.relationField,
            model: el.dataset.relationModel,
            canCreate: el.dataset.relationCanCreate === 'true'
        }));
    }

    extractWorkflowState(activeElement) {
        const workflowElement = activeElement.closest('[data-workflow-state]');
        return workflowElement ? workflowElement.dataset.workflowState : null;
    }

    extractAvailableTransitions(activeElement) {
        const transitionElements = activeElement.querySelectorAll('[data-workflow-transition]');
        return Array.from(transitionElements).map(el => ({
            id: el.dataset.workflowTransition,
            label: el.dataset.transitionLabel || el.dataset.workflowTransition,
            icon: el.dataset.transitionIcon,
            description: el.dataset.transitionDescription
        }));
    }

    extractAvailableReports(activeElement) {
        const reportElements = activeElement.querySelectorAll('[data-available-report]');
        return Array.from(reportElements).map(el => ({
            id: el.dataset.availableReport,
            name: el.dataset.reportName || el.dataset.availableReport,
            description: el.dataset.reportDescription
        }));
    }

    // 命令执行方法
    editRecord(model, recordId) {
        console.log(`Editing ${model} record ${recordId}`);
    }

    deleteRecord(model, recordId) {
        console.log(`Deleting ${model} record ${recordId}`);
    }

    duplicateRecord(model, recordId) {
        console.log(`Duplicating ${model} record ${recordId}`);
    }

    browseRelation(field) {
        console.log(`Browsing relation ${field.name}`);
    }

    createRelatedRecord(field) {
        console.log(`Creating related record for ${field.name}`);
    }

    executeTransition(transition) {
        console.log(`Executing workflow transition ${transition.id}`);
    }

    generateReport(report, context) {
        console.log(`Generating report ${report.id} for ${context.model}`);
    }
}

// 使用示例
const dynamicGenerator = new DynamicCommandGenerator();

// 注册动态提供者
const commandProviderRegistry = registry.category("command_provider");
commandProviderRegistry.add("dynamic", dynamicGenerator.createDynamicProvider());
```

## 🔧 调试技巧

### 命令提供者状态查看
```javascript
function debugCommandProviders() {
    const commandProviderRegistry = registry.category("command_provider");

    console.group('🔧 Command Providers Debug');
    console.log('Registered providers:', commandProviderRegistry.getEntries());

    // 测试每个提供者
    const mockEnv = { services: { command: odoo.__DEBUG__.services.command } };
    const mockOptions = { activeElement: document.activeElement };

    for (const [name, provider] of commandProviderRegistry.getEntries()) {
        try {
            const commands = provider.provide(mockEnv, mockOptions);
            console.log(`Provider "${name}": ${commands.length} commands`);
            console.table(commands.map(cmd => ({
                name: cmd.name,
                category: cmd.category,
                hasHotkey: !!cmd.props?.hotkey,
                component: cmd.Component?.name || 'Unknown'
            })));
        } catch (error) {
            console.error(`Provider "${name}" failed:`, error);
        }
    }

    console.groupEnd();
}

// 在控制台中调用
debugCommandProviders();
```

### 快捷键元素分析
```javascript
function analyzeHotkeyElements() {
    const hotkeyElements = document.querySelectorAll('[data-hotkey]:not(:disabled)');

    console.group('⌨️ Hotkey Elements Analysis');
    console.log(`Found ${hotkeyElements.length} hotkey elements`);

    const analysis = {
        byCategory: {},
        byHotkey: {},
        withoutDescription: []
    };

    hotkeyElements.forEach(el => {
        const hotkey = el.dataset.hotkey;
        const category = el.closest('[data-command-category]')?.dataset.commandCategory || 'default';
        const description = el.title || el.dataset.tooltip || el.placeholder || el.innerText || 'No description';

        // 按分类统计
        analysis.byCategory[category] = (analysis.byCategory[category] || 0) + 1;

        // 按快捷键统计
        analysis.byHotkey[hotkey] = (analysis.byHotkey[hotkey] || 0) + 1;

        // 无描述元素
        if (description === 'No description') {
            analysis.withoutDescription.push(el);
        }
    });

    console.log('By category:', analysis.byCategory);
    console.log('By hotkey:', analysis.byHotkey);
    console.log('Elements without description:', analysis.withoutDescription.length);

    // 检查重复快捷键
    const duplicateHotkeys = Object.entries(analysis.byHotkey)
        .filter(([, count]) => count > 1);

    if (duplicateHotkeys.length > 0) {
        console.warn('Duplicate hotkeys found:', duplicateHotkeys);
    }

    console.groupEnd();

    return analysis;
}

// 分析快捷键元素
analyzeHotkeyElements();
```

## 📊 性能考虑

### 优化策略
1. **延迟计算**: 只在需要时计算命令描述
2. **缓存结果**: 缓存DOM查询和计算结果
3. **去重优化**: 高效的去重算法
4. **条件过滤**: 早期过滤不可用命令

### 最佳实践
```javascript
// ✅ 好的做法：缓存DOM查询结果
const hotkeyElements = getVisibleElements(
    options.activeElement,
    "[data-hotkey]:not(:disabled)"
);

// ❌ 不好的做法：重复查询DOM
for (const el of document.querySelectorAll("[data-hotkey]:not(:disabled)")) {
    // 每次都查询整个文档
}

// ✅ 好的做法：早期过滤
.filter((command) => command.isAvailable === undefined || command.isAvailable())

// ❌ 不好的做法：后期过滤
.map(command => ({ ...command, available: command.isAvailable() }))
.filter(command => command.available)

// ✅ 好的做法：合理的描述截断
`${el.innerText.slice(0, 50)}${el.innerText.length > 50 ? "..." : ""}`

// ❌ 不好的做法：不限制描述长度
el.innerText // 可能非常长
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解命令提供者模式的设计和实现
- [ ] 掌握自动命令发现和生成机制
- [ ] 理解快捷键命令的动态创建
- [ ] 能够实现DOM元素到命令的映射策略
- [ ] 掌握企业级命令系统的扩展机制
- [ ] 了解命令提供者的性能优化技术

## 🚀 下一步学习
学完默认提供者后，建议继续学习：
1. **注册表系统** (`@web/core/registry.js`) - 学习注册表机制
2. **快捷键系统** (`@web/core/hotkeys/`) - 理解快捷键管理
3. **UI工具** (`@web/core/utils/ui.js`) - 掌握UI工具函数

## 💡 重要提示
- 命令提供者是命令系统的数据源
- 自动发现机制大大简化了命令注册
- 平台适配确保了跨平台的一致体验
- 去重和过滤机制保证了命令质量
```

# @web/core/commands/command_service.js 学习指南

## 📁 文件信息
- **文件名**: `@web/core/commands/command_service.js`
- **原始路径**: `/web/static/src/core/commands/command_service.js`
- **代码行数**: 266行
- **作用**: 实现命令服务核心逻辑，管理命令注册、快捷键绑定和命令面板调用

## 🎯 学习目标
通过学习这个文件，您将掌握：
- 服务架构的设计模式和实现原理
- 命令注册和生命周期管理机制
- 快捷键系统的集成和管理
- 对话框服务的集成应用
- 企业级命令系统的核心架构

## 📚 核心概念

### 什么是命令服务？
命令服务是一个**中央管理系统**，主要功能：
- **命令注册**: 管理应用中所有命令的注册和注销
- **快捷键绑定**: 自动处理命令的快捷键绑定
- **面板管理**: 控制命令面板的打开和配置
- **上下文管理**: 处理命令的可用性和上下文

### 核心架构组成
```javascript
const commandService = {
    // 依赖服务
    dependencies: ["dialog", "hotkey", "ui"],
    
    // 核心功能
    add(),              // 添加命令
    getCommands(),      // 获取命令列表
    openMainPalette(),  // 打开主命令面板
    openPalette(),      // 打开自定义命令面板
    
    // 内部管理
    registerCommand(),   // 注册命令
    unregisterCommand() // 注销命令
};

// 注册表集成
const registries = {
    commandCategoryRegistry,  // 命令分类注册表
    commandProviderRegistry,  // 命令提供者注册表
    commandSetupRegistry     // 命令设置注册表
};
```

### 基本使用模式
```javascript
import { useService } from '@web/core/utils/hooks';

class MyComponent extends Component {
    setup() {
        this.commandService = useService('command');
        
        // 添加简单命令
        this.removeCommand = this.commandService.add(
            "my_command",
            () => this.executeAction(),
            { category: "app" }
        );
        
        // 添加带快捷键的命令
        this.commandService.add(
            "save_record",
            () => this.saveRecord(),
            {
                category: "app",
                hotkey: "ctrl+s",
                global: true
            }
        );
    }
    
    onWillUnmount() {
        // 清理命令
        this.removeCommand();
    }
    
    openCommandPalette() {
        this.commandService.openMainPalette();
    }
}
```

## 🔍 核心实现详解

### 1. 服务初始化和依赖管理

#### 服务启动函数
```javascript
start(env, { dialog, hotkey: hotkeyService, ui }) {
    const registeredCommands = new Map();
    let nextToken = 0;
    let isPaletteOpened = false;
    const bus = new EventBus();
    
    // 注册全局快捷键
    hotkeyService.add("control+k", openMainPalette, {
        bypassEditableProtection: true,
        global: true,
    });
    
    // 返回服务API
    return {
        add, getCommands, openMainPalette, openPalette
    };
}
```

**初始化策略**：
- **依赖注入**: 通过参数接收所需的服务依赖
- **状态管理**: 使用Map管理注册的命令
- **令牌系统**: 使用递增令牌标识命令注册
- **全局快捷键**: 注册Ctrl+K打开命令面板

#### 命令存储结构
```javascript
/** @type {Map<CommandRegistration>} */
const registeredCommands = new Map();

// CommandRegistration结构
const registration = {
    name: "string",           // 命令名称
    action: "function",       // 命令动作
    category: "string",       // 命令分类
    hotkey: "string",         // 快捷键
    global: "boolean",        // 全局可用
    isAvailable: "function",  // 可用性检查
    removeHotkey: "function", // 快捷键清理函数
    activeElement: "Element"  // 激活元素
};
```

**存储设计**：
- **Map结构**: 使用Map提供高效的增删查操作
- **令牌映射**: 令牌作为键，命令注册作为值
- **扩展属性**: 在基础命令上添加管理属性
- **生命周期**: 包含清理函数支持完整生命周期

### 2. 命令注册机制

#### 命令注册函数
```javascript
function registerCommand(command, options) {
    // 验证必需参数
    if (!command.name || !command.action || typeof command.action !== "function") {
        throw new Error("A Command must have a name and an action function.");
    }
    
    // 合并命令和选项
    const registration = Object.assign({}, command, options);
    
    // 处理标识符冲突
    if (registration.identifier) {
        const commandsArray = Array.from(registeredCommands.values());
        const sameName = commandsArray.find((com) => com.name === registration.name);
        if (sameName) {
            if (registration.identifier !== sameName.identifier) {
                registration.name += ` (${registration.identifier})`;
                sameName.name += ` (${sameName.identifier})`;
            }
        }
    }
    
    // 处理快捷键绑定
    if (registration.hotkey) {
        const action = async () => {
            const commandService = env.services.command;
            const config = await command.action();
            if (!isPaletteOpened && config) {
                commandService.openPalette(config);
            }
        };
        
        registration.removeHotkey = hotkeyService.add(registration.hotkey, action, {
            ...options.hotkeyOptions,
            global: registration.global,
            isAvailable: (...args) => {
                let available = true;
                if (registration.isAvailable) {
                    available = registration.isAvailable(...args);
                }
                if (available && options.hotkeyOptions?.isAvailable) {
                    available = options.hotkeyOptions?.isAvailable(...args);
                }
                return available;
            },
        });
    }
    
    // 生成令牌并存储
    const token = nextToken++;
    registeredCommands.set(token, registration);
    
    // 设置激活元素
    if (!options.activeElement) {
        Promise.resolve().then(() => {
            registration.activeElement = ui.activeElement;
        });
    }
    
    return token;
}
```

**注册流程分析**：
- **参数验证**: 严格验证必需的name和action参数
- **冲突处理**: 智能处理同名命令的标识符冲突
- **快捷键集成**: 自动绑定快捷键并处理可用性检查
- **异步上下文**: 延迟设置激活元素避免DOM挂载问题

#### 命令注销机制
```javascript
function unregisterCommand(token) {
    const cmd = registeredCommands.get(token);
    if (cmd && cmd.removeHotkey) {
        cmd.removeHotkey();
    }
    registeredCommands.delete(token);
}
```

**注销策略**：
- **快捷键清理**: 自动清理关联的快捷键绑定
- **内存清理**: 从注册表中删除命令记录
- **安全检查**: 检查命令存在性避免错误
- **完整清理**: 确保没有资源泄漏

### 3. 命令面板集成

#### 主面板配置生成
```javascript
function openMainPalette(config = {}, onClose) {
    const configByNamespace = {};
    
    // 收集提供者配置
    for (const provider of commandProviderRegistry.getAll()) {
        const namespace = provider.namespace || "default";
        if (!configByNamespace[namespace]) {
            configByNamespace[namespace] = {
                categories: [],
                categoryNames: {},
            };
        }
    }
    
    // 收集分类配置
    for (const [category, el] of commandCategoryRegistry.getEntries()) {
        const namespace = el.namespace || "default";
        const name = el.name;
        if (namespace in configByNamespace) {
            configByNamespace[namespace].categories.push(category);
            configByNamespace[namespace].categoryNames[category] = name;
        }
    }
    
    // 收集设置配置
    for (const [namespace, { emptyMessage, debounceDelay, placeholder }] of commandSetupRegistry.getEntries()) {
        if (namespace in configByNamespace) {
            if (emptyMessage) configByNamespace[namespace].emptyMessage = emptyMessage;
            if (debounceDelay !== undefined) configByNamespace[namespace].debounceDelay = debounceDelay;
            if (placeholder) configByNamespace[namespace].placeholder = placeholder;
        }
    }
    
    // 合并最终配置
    config = Object.assign({
        configByNamespace,
        FooterComponent: DefaultFooter,
        providers: commandProviderRegistry.getAll(),
    }, config);
    
    return openPalette(config, onClose);
}
```

**配置生成策略**：
- **注册表聚合**: 从多个注册表收集配置信息
- **命名空间组织**: 按命名空间组织不同的配置
- **默认值处理**: 提供合理的默认配置
- **配置合并**: 支持外部配置覆盖默认配置

#### 面板打开管理
```javascript
function openPalette(config, onClose) {
    if (isPaletteOpened) {
        bus.trigger("SET-CONFIG", config);
        return;
    }
    
    isPaletteOpened = true;
    dialog.add(CommandPalette, {
        config,
        bus,
    }, {
        onClose: () => {
            isPaletteOpened = false;
            if (onClose) {
                onClose();
            }
        },
    });
}
```

**面板管理特点**：
- **单例模式**: 确保同时只有一个面板打开
- **配置更新**: 已打开时通过事件总线更新配置
- **对话框集成**: 使用对话框服务管理面板显示
- **状态同步**: 维护面板打开状态的一致性

### 4. 默认底部组件

#### DefaultFooter组件实现
```javascript
class DefaultFooter extends Component {
    static template = "web.DefaultFooter";
    static props = {
        switchNamespace: { type: Function },
    };
    
    setup() {
        this.elements = commandSetupRegistry
            .getEntries()
            .map((el) => ({ namespace: el[0], name: el[1].name }))
            .filter((el) => el.name);
    }
    
    onClick(namespace) {
        this.props.switchNamespace(namespace);
    }
}
```

**底部组件功能**：
- **命名空间切换**: 提供命名空间切换按钮
- **动态生成**: 基于注册表动态生成可用选项
- **交互处理**: 处理用户点击切换命名空间
- **配置驱动**: 完全由配置驱动的UI生成

## 🎨 实际应用场景

### 1. 高级命令服务扩展
```javascript
class AdvancedCommandService {
    constructor(baseCommandService) {
        this.baseService = baseCommandService;
        this.commandGroups = new Map();
        this.commandMiddleware = [];
        this.commandMetrics = new Map();
        this.commandPermissions = new Map();
        this.setupAdvancedFeatures();
    }
    
    setupAdvancedFeatures() {
        // 包装原始add方法
        const originalAdd = this.baseService.add;
        this.baseService.add = (name, action, options = {}) => {
            // 应用中间件
            const enhancedAction = this.applyMiddleware(name, action, options);
            
            // 记录指标
            this.recordCommandRegistration(name, options);
            
            // 检查权限
            if (!this.checkPermission(name, options)) {
                console.warn(`Command "${name}" registration denied due to permissions`);
                return () => {}; // 返回空清理函数
            }
            
            return originalAdd(name, enhancedAction, options);
        };
    }
    
    // 命令组管理
    addCommandGroup(groupName, commands, groupOptions = {}) {
        const group = {
            name: groupName,
            commands: new Map(),
            options: groupOptions,
            isEnabled: true
        };
        
        const cleanupFunctions = [];
        
        commands.forEach(({ name, action, options = {} }) => {
            const enhancedOptions = {
                ...options,
                group: groupName,
                groupOptions
            };
            
            const cleanup = this.baseService.add(name, action, enhancedOptions);
            group.commands.set(name, { action, options: enhancedOptions, cleanup });
            cleanupFunctions.push(cleanup);
        });
        
        group.cleanup = () => {
            cleanupFunctions.forEach(cleanup => cleanup());
        };
        
        this.commandGroups.set(groupName, group);
        
        return {
            enable: () => this.enableCommandGroup(groupName),
            disable: () => this.disableCommandGroup(groupName),
            remove: () => this.removeCommandGroup(groupName)
        };
    }
    
    enableCommandGroup(groupName) {
        const group = this.commandGroups.get(groupName);
        if (group && !group.isEnabled) {
            group.isEnabled = true;
            
            // 重新注册组内命令
            const cleanupFunctions = [];
            for (const [name, { action, options }] of group.commands.entries()) {
                const cleanup = this.baseService.add(name, action, options);
                group.commands.get(name).cleanup = cleanup;
                cleanupFunctions.push(cleanup);
            }
            
            group.cleanup = () => {
                cleanupFunctions.forEach(cleanup => cleanup());
            };
        }
    }
    
    disableCommandGroup(groupName) {
        const group = this.commandGroups.get(groupName);
        if (group && group.isEnabled) {
            group.isEnabled = false;
            group.cleanup();
        }
    }
    
    removeCommandGroup(groupName) {
        const group = this.commandGroups.get(groupName);
        if (group) {
            group.cleanup();
            this.commandGroups.delete(groupName);
        }
    }
    
    // 中间件系统
    addMiddleware(middleware) {
        this.commandMiddleware.push(middleware);
    }
    
    applyMiddleware(name, action, options) {
        let enhancedAction = action;
        
        for (const middleware of this.commandMiddleware) {
            enhancedAction = middleware(name, enhancedAction, options) || enhancedAction;
        }
        
        return enhancedAction;
    }
    
    // 权限系统
    setCommandPermission(commandName, permissionCheck) {
        this.commandPermissions.set(commandName, permissionCheck);
    }
    
    checkPermission(commandName, options) {
        const permissionCheck = this.commandPermissions.get(commandName);
        if (permissionCheck) {
            return permissionCheck(options);
        }
        return true; // 默认允许
    }
    
    // 指标收集
    recordCommandRegistration(name, options) {
        if (!this.commandMetrics.has(name)) {
            this.commandMetrics.set(name, {
                registrationCount: 0,
                executionCount: 0,
                lastRegistered: null,
                lastExecuted: null,
                averageExecutionTime: 0,
                totalExecutionTime: 0
            });
        }
        
        const metrics = this.commandMetrics.get(name);
        metrics.registrationCount++;
        metrics.lastRegistered = Date.now();
    }
    
    recordCommandExecution(name, executionTime) {
        const metrics = this.commandMetrics.get(name);
        if (metrics) {
            metrics.executionCount++;
            metrics.lastExecuted = Date.now();
            metrics.totalExecutionTime += executionTime;
            metrics.averageExecutionTime = metrics.totalExecutionTime / metrics.executionCount;
        }
    }
    
    // 条件命令
    addConditionalCommand(name, action, condition, options = {}) {
        const conditionalAction = async () => {
            if (await condition()) {
                return action();
            } else {
                console.log(`Command "${name}" not available`);
            }
        };
        
        return this.baseService.add(name, conditionalAction, {
            ...options,
            isAvailable: condition
        });
    }
    
    // 延迟命令
    addDelayedCommand(name, action, delay, options = {}) {
        const delayedAction = async () => {
            await new Promise(resolve => setTimeout(resolve, delay));
            return action();
        };
        
        return this.baseService.add(name, delayedAction, options);
    }
    
    // 重试命令
    addRetryCommand(name, action, maxRetries = 3, options = {}) {
        const retryAction = async () => {
            let lastError;
            
            for (let attempt = 0; attempt <= maxRetries; attempt++) {
                try {
                    return await action();
                } catch (error) {
                    lastError = error;
                    if (attempt < maxRetries) {
                        console.warn(`Command "${name}" failed, retrying... (${attempt + 1}/${maxRetries})`);
                        await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
                    }
                }
            }
            
            throw lastError;
        };
        
        return this.baseService.add(name, retryAction, options);
    }
    
    // 获取命令统计
    getCommandMetrics(commandName) {
        return this.commandMetrics.get(commandName);
    }
    
    getAllMetrics() {
        return Object.fromEntries(this.commandMetrics.entries());
    }
    
    // 生成命令报告
    generateCommandReport() {
        const report = {
            totalCommands: this.commandMetrics.size,
            totalGroups: this.commandGroups.size,
            enabledGroups: Array.from(this.commandGroups.values()).filter(g => g.isEnabled).length,
            topCommands: this.getTopCommands(),
            recentActivity: this.getRecentActivity(),
            performanceStats: this.getPerformanceStats()
        };
        
        return report;
    }
    
    getTopCommands(limit = 10) {
        return Array.from(this.commandMetrics.entries())
            .sort(([,a], [,b]) => b.executionCount - a.executionCount)
            .slice(0, limit)
            .map(([name, metrics]) => ({ name, ...metrics }));
    }
    
    getRecentActivity(limit = 20) {
        return Array.from(this.commandMetrics.entries())
            .filter(([, metrics]) => metrics.lastExecuted)
            .sort(([,a], [,b]) => b.lastExecuted - a.lastExecuted)
            .slice(0, limit)
            .map(([name, metrics]) => ({
                name,
                lastExecuted: new Date(metrics.lastExecuted),
                executionCount: metrics.executionCount
            }));
    }
    
    getPerformanceStats() {
        const allMetrics = Array.from(this.commandMetrics.values());
        const executedCommands = allMetrics.filter(m => m.executionCount > 0);
        
        if (executedCommands.length === 0) {
            return { averageExecutionTime: 0, slowestCommand: null, fastestCommand: null };
        }
        
        const totalTime = executedCommands.reduce((sum, m) => sum + m.totalExecutionTime, 0);
        const totalExecutions = executedCommands.reduce((sum, m) => sum + m.executionCount, 0);
        
        const sortedBySpeed = executedCommands.sort((a, b) => a.averageExecutionTime - b.averageExecutionTime);
        
        return {
            averageExecutionTime: totalTime / totalExecutions,
            slowestCommand: sortedBySpeed[sortedBySpeed.length - 1],
            fastestCommand: sortedBySpeed[0],
            totalExecutions
        };
    }
}

// 中间件示例
const loggingMiddleware = (name, action, options) => {
    return async () => {
        console.log(`Executing command: ${name}`);
        const startTime = performance.now();
        
        try {
            const result = await action();
            const endTime = performance.now();
            console.log(`Command ${name} completed in ${endTime - startTime}ms`);
            return result;
        } catch (error) {
            console.error(`Command ${name} failed:`, error);
            throw error;
        }
    };
};

const analyticsMiddleware = (name, action, options) => {
    return async () => {
        // 发送分析数据
        analytics.track('command_executed', {
            command: name,
            category: options.category,
            timestamp: Date.now()
        });
        
        return action();
    };
};

// 使用示例
const commandService = useService('command');
const advancedService = new AdvancedCommandService(commandService);

// 添加中间件
advancedService.addMiddleware(loggingMiddleware);
advancedService.addMiddleware(analyticsMiddleware);

// 添加命令组
advancedService.addCommandGroup('file_operations', [
    {
        name: 'save_file',
        action: () => saveFile(),
        options: { category: 'app', hotkey: 'ctrl+s' }
    },
    {
        name: 'open_file',
        action: () => openFile(),
        options: { category: 'app', hotkey: 'ctrl+o' }
    }
]);

// 设置权限
advancedService.setCommandPermission('delete_record', (options) => {
    return user.hasPermission('delete');
});

// 添加条件命令
advancedService.addConditionalCommand(
    'edit_record',
    () => editRecord(),
    () => currentRecord && currentRecord.canEdit(),
    { category: 'smart_action' }
);
```

### 2. 命令服务测试框架
```javascript
class CommandServiceTester {
    constructor() {
        this.mockServices = this.createMockServices();
        this.testResults = [];
        this.commandService = null;
    }

    createMockServices() {
        return {
            dialog: {
                add: jest.fn(),
                remove: jest.fn()
            },
            hotkey: {
                add: jest.fn(() => jest.fn()), // 返回清理函数
                remove: jest.fn()
            },
            ui: {
                activeElement: document.body
            }
        };
    }

    setupCommandService() {
        // 创建命令服务实例
        const env = {
            services: {
                command: null // 将在创建后设置
            }
        };

        this.commandService = commandService.start(env, this.mockServices);
        env.services.command = this.commandService;

        return this.commandService;
    }

    // 测试命令注册
    testCommandRegistration() {
        const service = this.setupCommandService();
        const testResults = {
            passed: 0,
            failed: 0,
            tests: []
        };

        // 测试基本命令注册
        try {
            const cleanup = service.add('test_command', () => 'executed');
            testResults.tests.push({
                name: 'Basic command registration',
                status: 'passed',
                message: 'Command registered successfully'
            });
            testResults.passed++;

            // 测试清理函数
            cleanup();
            testResults.tests.push({
                name: 'Command cleanup',
                status: 'passed',
                message: 'Command cleanup function works'
            });
            testResults.passed++;

        } catch (error) {
            testResults.tests.push({
                name: 'Basic command registration',
                status: 'failed',
                message: error.message
            });
            testResults.failed++;
        }

        // 测试无效命令注册
        try {
            service.add('', () => {});
            testResults.tests.push({
                name: 'Invalid command registration',
                status: 'failed',
                message: 'Should have thrown error for empty name'
            });
            testResults.failed++;
        } catch (error) {
            testResults.tests.push({
                name: 'Invalid command registration',
                status: 'passed',
                message: 'Correctly rejected invalid command'
            });
            testResults.passed++;
        }

        // 测试快捷键绑定
        try {
            service.add('hotkey_command', () => 'hotkey executed', {
                hotkey: 'ctrl+t',
                global: true
            });

            expect(this.mockServices.hotkey.add).toHaveBeenCalled();
            testResults.tests.push({
                name: 'Hotkey binding',
                status: 'passed',
                message: 'Hotkey registered correctly'
            });
            testResults.passed++;

        } catch (error) {
            testResults.tests.push({
                name: 'Hotkey binding',
                status: 'failed',
                message: error.message
            });
            testResults.failed++;
        }

        return testResults;
    }

    // 测试命令面板集成
    testCommandPaletteIntegration() {
        const service = this.setupCommandService();
        const testResults = {
            passed: 0,
            failed: 0,
            tests: []
        };

        // 测试主面板打开
        try {
            service.openMainPalette();
            expect(this.mockServices.dialog.add).toHaveBeenCalled();

            testResults.tests.push({
                name: 'Main palette opening',
                status: 'passed',
                message: 'Main palette opened successfully'
            });
            testResults.passed++;

        } catch (error) {
            testResults.tests.push({
                name: 'Main palette opening',
                status: 'failed',
                message: error.message
            });
            testResults.failed++;
        }

        // 测试自定义面板配置
        try {
            const customConfig = {
                providers: [],
                configByNamespace: {
                    custom: {
                        placeholder: 'Custom search...'
                    }
                }
            };

            service.openPalette(customConfig);
            expect(this.mockServices.dialog.add).toHaveBeenCalledTimes(2);

            testResults.tests.push({
                name: 'Custom palette configuration',
                status: 'passed',
                message: 'Custom palette configured correctly'
            });
            testResults.passed++;

        } catch (error) {
            testResults.tests.push({
                name: 'Custom palette configuration',
                status: 'failed',
                message: error.message
            });
            testResults.failed++;
        }

        return testResults;
    }

    // 测试命令获取
    testCommandRetrieval() {
        const service = this.setupCommandService();
        const testResults = {
            passed: 0,
            failed: 0,
            tests: []
        };

        // 添加测试命令
        service.add('global_command', () => 'global', { global: true });
        service.add('local_command', () => 'local', { global: false });

        // 测试全局命令获取
        try {
            const globalCommands = service.getCommands(document.body);
            const hasGlobalCommand = globalCommands.some(cmd => cmd.name === 'global_command');

            if (hasGlobalCommand) {
                testResults.tests.push({
                    name: 'Global command retrieval',
                    status: 'passed',
                    message: 'Global commands retrieved correctly'
                });
                testResults.passed++;
            } else {
                testResults.tests.push({
                    name: 'Global command retrieval',
                    status: 'failed',
                    message: 'Global command not found'
                });
                testResults.failed++;
            }

        } catch (error) {
            testResults.tests.push({
                name: 'Global command retrieval',
                status: 'failed',
                message: error.message
            });
            testResults.failed++;
        }

        return testResults;
    }

    // 性能测试
    testPerformance() {
        const service = this.setupCommandService();
        const testResults = {
            passed: 0,
            failed: 0,
            tests: [],
            metrics: {}
        };

        // 测试大量命令注册性能
        const commandCount = 1000;
        const startTime = performance.now();

        const cleanupFunctions = [];
        for (let i = 0; i < commandCount; i++) {
            const cleanup = service.add(`command_${i}`, () => `executed_${i}`);
            cleanupFunctions.push(cleanup);
        }

        const registrationTime = performance.now() - startTime;
        testResults.metrics.registrationTime = registrationTime;

        if (registrationTime < 1000) { // 1秒内完成
            testResults.tests.push({
                name: 'Bulk command registration performance',
                status: 'passed',
                message: `Registered ${commandCount} commands in ${registrationTime.toFixed(2)}ms`
            });
            testResults.passed++;
        } else {
            testResults.tests.push({
                name: 'Bulk command registration performance',
                status: 'failed',
                message: `Registration took too long: ${registrationTime.toFixed(2)}ms`
            });
            testResults.failed++;
        }

        // 测试命令获取性能
        const retrievalStartTime = performance.now();
        const commands = service.getCommands(document.body);
        const retrievalTime = performance.now() - retrievalStartTime;
        testResults.metrics.retrievalTime = retrievalTime;

        if (retrievalTime < 100) { // 100ms内完成
            testResults.tests.push({
                name: 'Command retrieval performance',
                status: 'passed',
                message: `Retrieved ${commands.length} commands in ${retrievalTime.toFixed(2)}ms`
            });
            testResults.passed++;
        } else {
            testResults.tests.push({
                name: 'Command retrieval performance',
                status: 'failed',
                message: `Retrieval took too long: ${retrievalTime.toFixed(2)}ms`
            });
            testResults.failed++;
        }

        // 清理测试命令
        const cleanupStartTime = performance.now();
        cleanupFunctions.forEach(cleanup => cleanup());
        const cleanupTime = performance.now() - cleanupStartTime;
        testResults.metrics.cleanupTime = cleanupTime;

        if (cleanupTime < 500) { // 500ms内完成
            testResults.tests.push({
                name: 'Bulk command cleanup performance',
                status: 'passed',
                message: `Cleaned up ${commandCount} commands in ${cleanupTime.toFixed(2)}ms`
            });
            testResults.passed++;
        } else {
            testResults.tests.push({
                name: 'Bulk command cleanup performance',
                status: 'failed',
                message: `Cleanup took too long: ${cleanupTime.toFixed(2)}ms`
            });
            testResults.failed++;
        }

        return testResults;
    }

    // 运行所有测试
    runAllTests() {
        const allResults = {
            registration: this.testCommandRegistration(),
            paletteIntegration: this.testCommandPaletteIntegration(),
            retrieval: this.testCommandRetrieval(),
            performance: this.testPerformance()
        };

        const summary = {
            totalTests: 0,
            totalPassed: 0,
            totalFailed: 0,
            categories: {}
        };

        for (const [category, results] of Object.entries(allResults)) {
            summary.totalTests += results.tests.length;
            summary.totalPassed += results.passed;
            summary.totalFailed += results.failed;
            summary.categories[category] = {
                passed: results.passed,
                failed: results.failed,
                total: results.tests.length
            };
        }

        return {
            summary,
            details: allResults
        };
    }

    // 生成测试报告
    generateTestReport() {
        const results = this.runAllTests();

        console.group('🧪 Command Service Test Report');
        console.log(`Total Tests: ${results.summary.totalTests}`);
        console.log(`Passed: ${results.summary.totalPassed}`);
        console.log(`Failed: ${results.summary.totalFailed}`);
        console.log(`Success Rate: ${(results.summary.totalPassed / results.summary.totalTests * 100).toFixed(1)}%`);

        for (const [category, stats] of Object.entries(results.summary.categories)) {
            console.group(`${category.toUpperCase()}`);
            console.log(`Passed: ${stats.passed}/${stats.total}`);

            const categoryDetails = results.details[category];
            categoryDetails.tests.forEach(test => {
                const icon = test.status === 'passed' ? '✅' : '❌';
                console.log(`${icon} ${test.name}: ${test.message}`);
            });

            console.groupEnd();
        }

        console.groupEnd();

        return results;
    }
}

// 使用示例
const tester = new CommandServiceTester();
const testReport = tester.generateTestReport();
```

## 🔧 调试技巧

### 命令服务状态查看
```javascript
function debugCommandService() {
    const commandService = odoo.__DEBUG__.services.command;

    console.group('⚙️ Command Service Debug');
    console.log('Service methods:', Object.keys(commandService));

    // 获取当前注册的命令
    const commands = commandService.getCommands(document.activeElement);
    console.log(`Registered commands: ${commands.length}`);
    console.table(commands.map(cmd => ({
        name: cmd.name,
        category: cmd.category,
        hasHotkey: !!cmd.hotkey,
        global: cmd.global
    })));

    console.groupEnd();
}

// 在控制台中调用
debugCommandService();
```

### 命令执行追踪
```javascript
function traceCommandExecution() {
    const commandService = odoo.__DEBUG__.services.command;
    const originalAdd = commandService.add;

    commandService.add = function(name, action, options = {}) {
        console.log(`📝 Registering command: ${name}`, options);

        const wrappedAction = async function(...args) {
            console.log(`🚀 Executing command: ${name}`, args);
            const startTime = performance.now();

            try {
                const result = await action.apply(this, args);
                const endTime = performance.now();
                console.log(`✅ Command ${name} completed in ${endTime - startTime}ms`);
                return result;
            } catch (error) {
                console.error(`❌ Command ${name} failed:`, error);
                throw error;
            }
        };

        return originalAdd.call(this, name, wrappedAction, options);
    };

    console.log('Command execution tracing enabled');
}

// 启用追踪
traceCommandExecution();
```

## 📊 性能考虑

### 优化策略
1. **令牌系统**: 使用数字令牌提高查找效率
2. **Map存储**: 使用Map而非Object提高增删性能
3. **延迟绑定**: 延迟设置activeElement避免DOM问题
4. **事件总线**: 使用事件总线减少直接耦合

### 最佳实践
```javascript
// ✅ 好的做法：及时清理命令
onWillUnmount() {
    this.removeCommand();
}

// ❌ 不好的做法：忘记清理命令
onWillUnmount() {
    // 没有清理，可能导致内存泄漏
}

// ✅ 好的做法：合理使用全局命令
commandService.add("global_search", action, { global: true });

// ❌ 不好的做法：滥用全局命令
commandService.add("specific_action", action, { global: true }); // 应该是局部的

// ✅ 好的做法：提供可用性检查
commandService.add("edit_record", action, {
    isAvailable: () => !!currentRecord
});

// ❌ 不好的做法：在动作中检查可用性
commandService.add("edit_record", () => {
    if (!currentRecord) return; // 应该在isAvailable中检查
    editRecord();
});
```

## 📝 学习检查点

完成本节学习后，您应该能够：
- [ ] 理解服务架构的设计模式和实现原理
- [ ] 掌握命令注册和生命周期管理机制
- [ ] 理解快捷键系统的集成和管理
- [ ] 能够实现对话框服务的集成应用
- [ ] 掌握企业级命令系统的核心架构
- [ ] 了解命令服务的测试和调试技术

## 🚀 下一步学习
学完命令服务后，建议继续学习：
1. **默认提供者** (`@web/core/commands/default_providers.js`) - 学习默认命令提供者
2. **对话框服务** (`@web/core/dialog/`) - 理解对话框系统
3. **快捷键服务** (`@web/core/hotkeys/`) - 掌握快捷键管理

## 💡 重要提示
- 命令服务是整个命令系统的核心枢纽
- 令牌系统确保了命令的正确生命周期管理
- 快捷键集成提供了高效的用户交互
- 注册表集成支持了灵活的配置和扩展
```

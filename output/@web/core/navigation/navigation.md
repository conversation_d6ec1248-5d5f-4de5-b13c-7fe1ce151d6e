# Navigation - 导航组件

## 概述

`navigation.js` 是 Odoo Web 核心模块的导航组件，提供了键盘导航功能。该组件支持在容器内的可导航元素之间进行键盘导航，具备热键自定义、虚拟焦点、鼠标悬停聚焦等特性，为用户提供了便捷的键盘操作体验，广泛应用于下拉菜单、列表选择、表单导航等需要键盘导航的场景。

## 文件信息
- **路径**: `/web/static/src/core/navigation/navigation.js`
- **行数**: 311
- **模块**: `@web/core/navigation/navigation`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                          // OWL框架
'@web/core/utils/hooks'              // 工具钩子
'@web/core/utils/scrolling'          // 滚动工具
'@web/core/utils/timing'             // 时间工具
```

## 核心常量

```javascript
const ACTIVE_ELEMENT_CLASS = "focus";                    // 活动元素CSS类
const throttledElementFocus = throttleForAnimation((el) => el?.focus()); // 节流聚焦函数
```

## 核心类

### 1. NavigationItem 类

```javascript
class NavigationItem {
    constructor({ index, el, setActiveItem, options }) {
        this.index = index;
        this.options = options;
        this.setActiveItem = setActiveItem;
        this.el = el;
        
        // 确定焦点目标
        if (options.shouldFocusChildInput) {
            const subInput = el.querySelector(":scope input, :scope button, :scope textarea");
            this.target = subInput || el;
        } else {
            this.target = el;
        }
        
        // 绑定事件监听器
        this.target.addEventListener("focus", () => this.focus(true));
        this.target.addEventListener("mouseenter", (ev) => this.onMouseEnter(ev));
    }
}
```

**NavigationItem功能**:
- **索引管理**: 维护导航项的索引位置
- **目标选择**: 智能选择焦点目标元素
- **事件绑定**: 绑定焦点和鼠标事件
- **生命周期**: 管理事件监听器的生命周期

#### 核心方法

```javascript
// 选择项目
select() {
    this.focus();
    this.target.click();
}

// 聚焦项目
focus(skipRealFocus = false) {
    scrollTo(this.target);
    this.setActiveItem(this.index, this);
    this.target.classList.add(ACTIVE_ELEMENT_CLASS);
    
    if (!skipRealFocus && !this.options.virtualFocus) {
        focusElement(this.target);
    }
}

// 取消聚焦
defocus() {
    this.target.classList.remove(ACTIVE_ELEMENT_CLASS);
}
```

### 2. Navigator 类

```javascript
class Navigator {
    constructor(containerRef, options, hotkeyService) {
        this.enabled = false;
        this.containerRef = containerRef;
        this.items = [];
        this.activeItem = undefined;
        this.currentActiveIndex = -1;
        
        // 默认选项配置
        this.options = {
            shouldFocusChildInput: true,
            virtualFocus: false,
            itemsSelector: ":scope .o-navigable",
            focusInitialElementOnDisabled: () => true,
            ...options,
            hotkeys: {
                home: (index, items) => items[0]?.focus(),
                end: (index, items) => items.at(-1)?.focus(),
                tab: () => focusAt(+1),
                "shift+tab": () => focusAt(-1),
                arrowdown: () => focusAt(+1),
                arrowup: () => focusAt(-1),
                enter: (index, items) => items[index] || items[0]?.select(),
                ...(options?.hotkeys || {}),
            },
        };
    }
}
```

**Navigator功能**:
- **容器管理**: 管理导航容器和可导航项目
- **热键配置**: 配置和管理键盘快捷键
- **状态跟踪**: 跟踪当前活动项目和索引
- **生命周期**: 管理导航器的启用和禁用

#### 核心方法

```javascript
// 启用导航
enable() {
    // 注册热键
    for (const [hotkey, callback] of Object.entries(this.options.hotkeys)) {
        this.hotkeyRemoves.push(
            this.hotkeyService.add(hotkey, () => callback(this.currentActiveIndex, this.items), {
                allowRepeat: true,
                bypassEditableProtection: this.allowedInEditableHotkeys.includes(hotkey),
            })
        );
    }
    
    // 设置DOM观察器
    this.targetObserver = new MutationObserver(() => this.debouncedUpdate());
    this.targetObserver.observe(this.containerRef.el, {
        childList: true,
        subtree: true,
    });
    
    this.update();
    this.enabled = true;
}

// 禁用导航
disable() {
    if (this.targetObserver) {
        this.targetObserver.disconnect();
    }
    
    this.clearItems();
    for (const removeHotkey of this.hotkeyRemoves) {
        removeHotkey();
    }
    
    this.enabled = false;
}
```

## 核心钩子函数

### 1. useNavigation

```javascript
function useNavigation(containerRef, options = {}) {
    const hotkeyService = useService("hotkey");
    containerRef = typeof containerRef === "string" ? useRef(containerRef) : containerRef;
    const navigator = new Navigator(containerRef, options, hotkeyService);

    useEffect(
        (container) => {
            if (container) {
                navigator.enable();
            } else if (navigator) {
                navigator.disable();
            }
        },
        () => [containerRef.el]
    );

    return {
        enable: () => navigator.enable(),
        disable: () => navigator.disable(),
    };
}
```

**钩子功能**:
- **自动管理**: 自动管理导航器的生命周期
- **引用处理**: 处理字符串和对象引用
- **服务集成**: 集成热键服务
- **返回接口**: 提供启用和禁用接口

## 配置选项

### 1. NavigationOptions

```javascript
/**
 * @typedef {Object} NavigationOptions
 * @property {NavigationHotkeys} hotkeys - 热键配置
 * @property {Function} onEnabled - 启用回调
 * @property {Function} onMouseEnter - 鼠标进入回调
 * @property {Boolean} virtualFocus - 虚拟焦点模式
 * @property {string} itemsSelector - 可导航元素选择器
 * @property {Function} focusInitialElementOnDisabled - 禁用时焦点恢复
 * @property {Boolean} shouldFocusChildInput - 是否聚焦子输入元素
 */
```

### 2. 默认热键配置

```javascript
const defaultHotkeys = {
    home: "跳转到第一项",
    end: "跳转到最后一项", 
    tab: "下一项",
    "shift+tab": "上一项",
    arrowdown: "下一项",
    arrowup: "上一项",
    enter: "选择当前项",
    arrowleft: "自定义左箭头",
    arrowright: "自定义右箭头",
    escape: "自定义退出",
    space: "自定义空格"
};
```

## 使用场景

### 1. 基础下拉菜单导航

```javascript
// 基础下拉菜单导航
class DropdownNavigation extends Component {
    setup() {
        this.dropdownRef = useRef("dropdown");
        this.state = useState({
            isOpen: false,
            items: [
                { id: 1, label: '选项 1', action: () => console.log('选项 1') },
                { id: 2, label: '选项 2', action: () => console.log('选项 2') },
                { id: 3, label: '选项 3', action: () => console.log('选项 3') },
                { id: 4, label: '选项 4', action: () => console.log('选项 4') }
            ]
        });

        this.navigation = useNavigation(this.dropdownRef, {
            itemsSelector: ".dropdown-item",
            onEnabled: (items) => {
                console.log('Navigation enabled with', items.length, 'items');
            },
            hotkeys: {
                escape: () => this.closeDropdown(),
                enter: (index, items) => {
                    const item = items[index];
                    if (item) {
                        this.selectItem(index);
                    }
                }
            }
        });
    }

    openDropdown() {
        this.state.isOpen = true;
        // 导航会在DOM更新后自动启用
    }

    closeDropdown() {
        this.state.isOpen = false;
        // 导航会在DOM更新后自动禁用
    }

    selectItem(index) {
        const item = this.state.items[index];
        if (item) {
            item.action();
            this.closeDropdown();
        }
    }

    render() {
        return xml`
            <div class="dropdown-navigation">
                <h5>下拉菜单导航示例</h5>
                
                <div class="dropdown">
                    <button 
                        class="btn btn-primary dropdown-toggle"
                        t-on-click="openDropdown"
                        t-att-disabled="state.isOpen"
                    >
                        选择选项
                    </button>
                    
                    <div 
                        t-ref="dropdown"
                        class="dropdown-menu"
                        t-att-class="state.isOpen ? 'show' : ''"
                        style="position: static; display: block;"
                        t-if="state.isOpen"
                    >
                        <t t-foreach="state.items" t-as="item" t-key="item.id">
                            <button 
                                class="dropdown-item o-navigable"
                                t-on-click="() => this.selectItem(item_index)"
                            >
                                <t t-esc="item.label"/>
                            </button>
                        </t>
                    </div>
                </div>

                <div class="instructions mt-3">
                    <small class="text-muted">
                        使用方向键导航，回车选择，ESC关闭
                    </small>
                </div>
            </div>
        `;
    }
}
```

### 2. 高级表单导航

```javascript
// 高级表单导航
class FormNavigation extends Component {
    setup() {
        this.formRef = useRef("form");
        this.state = useState({
            formData: {
                name: '',
                email: '',
                phone: '',
                address: '',
                notes: ''
            },
            navigationMode: 'auto',
            virtualFocus: false
        });

        this.navigation = useNavigation(this.formRef, {
            itemsSelector: ".form-control, .btn",
            shouldFocusChildInput: true,
            virtualFocus: this.state.virtualFocus,
            onMouseEnter: (item) => {
                console.log('Mouse entered item:', item.index);
            },
            hotkeys: {
                "ctrl+enter": () => this.submitForm(),
                "ctrl+r": () => this.resetForm(),
                escape: () => this.cancelForm(),
                // 保持默认的方向键导航
                arrowdown: undefined, // 使用默认行为
                arrowup: undefined,   // 使用默认行为
            }
        });
    }

    submitForm() {
        console.log('Form submitted:', this.state.formData);
        this.notification.add('表单已提交', { type: 'success' });
    }

    resetForm() {
        this.state.formData = {
            name: '', email: '', phone: '', address: '', notes: ''
        };
        this.notification.add('表单已重置', { type: 'info' });
    }

    cancelForm() {
        if (confirm('确定要取消吗？未保存的更改将丢失。')) {
            this.resetForm();
        }
    }

    toggleNavigationMode() {
        this.state.navigationMode = this.state.navigationMode === 'auto' ? 'manual' : 'auto';
        
        if (this.state.navigationMode === 'manual') {
            this.navigation.enable();
        } else {
            this.navigation.disable();
        }
    }

    toggleVirtualFocus() {
        this.state.virtualFocus = !this.state.virtualFocus;
        // 需要重新创建导航器以应用新设置
        this.notification.add('虚拟焦点模式已' + (this.state.virtualFocus ? '启用' : '禁用'), { type: 'info' });
    }

    render() {
        return xml`
            <div class="form-navigation">
                <div class="header mb-4">
                    <h5>表单导航示例</h5>
                    <div class="navigation-controls">
                        <div class="btn-group me-3">
                            <button 
                                class="btn btn-sm"
                                t-att-class="state.navigationMode === 'auto' ? 'btn-primary' : 'btn-outline-primary'"
                                t-on-click="toggleNavigationMode"
                            >
                                导航模式: ${this.state.navigationMode}
                            </button>
                        </div>
                        <div class="form-check form-check-inline">
                            <input 
                                class="form-check-input" 
                                type="checkbox" 
                                t-model="state.virtualFocus"
                                t-on-change="toggleVirtualFocus"
                            />
                            <label class="form-check-label">虚拟焦点</label>
                        </div>
                    </div>
                </div>

                <form t-ref="form" class="navigation-form">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">姓名</label>
                            <input 
                                type="text" 
                                class="form-control"
                                t-model="state.formData.name"
                                placeholder="请输入姓名"
                            />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">邮箱</label>
                            <input 
                                type="email" 
                                class="form-control"
                                t-model="state.formData.email"
                                placeholder="请输入邮箱"
                            />
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">电话</label>
                            <input 
                                type="tel" 
                                class="form-control"
                                t-model="state.formData.phone"
                                placeholder="请输入电话"
                            />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">地址</label>
                            <input 
                                type="text" 
                                class="form-control"
                                t-model="state.formData.address"
                                placeholder="请输入地址"
                            />
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">备注</label>
                        <textarea 
                            class="form-control"
                            rows="3"
                            t-model="state.formData.notes"
                            placeholder="请输入备注"
                        />
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-primary me-2" t-on-click="submitForm">
                            提交 (Ctrl+Enter)
                        </button>
                        <button type="button" class="btn btn-secondary me-2" t-on-click="resetForm">
                            重置 (Ctrl+R)
                        </button>
                        <button type="button" class="btn btn-outline-secondary" t-on-click="cancelForm">
                            取消 (ESC)
                        </button>
                    </div>
                </form>

                <div class="instructions mt-4">
                    <div class="card">
                        <div class="card-header">
                            <h6>键盘快捷键</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><kbd>↑</kbd> <kbd>↓</kbd> 上下导航</li>
                                        <li><kbd>Tab</kbd> <kbd>Shift+Tab</kbd> 前后导航</li>
                                        <li><kbd>Home</kbd> <kbd>End</kbd> 首末导航</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><kbd>Ctrl+Enter</kbd> 提交表单</li>
                                        <li><kbd>Ctrl+R</kbd> 重置表单</li>
                                        <li><kbd>ESC</kbd> 取消操作</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}
```

## 技术特点

### 1. 智能焦点管理
- 自动检测可聚焦元素
- 支持虚拟焦点模式
- 智能的子元素聚焦

### 2. 热键系统
- 完整的热键配置
- 可编辑元素保护绕过
- 自定义热键回调

### 3. DOM观察
- 自动检测DOM变化
- 防抖更新机制
- 动态项目管理

### 4. 事件管理
- 完整的事件生命周期
- 内存泄漏防护
- 节流优化

## 设计模式

### 1. 观察者模式 (Observer Pattern)
- DOM变化的观察和响应
- 事件驱动的更新机制

### 2. 策略模式 (Strategy Pattern)
- 可配置的导航策略
- 自定义热键行为

### 3. 命令模式 (Command Pattern)
- 热键到命令的映射
- 可撤销的操作支持

## 注意事项

1. **内存管理**: 正确清理事件监听器和观察器
2. **性能优化**: 使用防抖避免频繁更新
3. **可访问性**: 保持良好的键盘导航体验
4. **兼容性**: 处理不同浏览器的焦点行为

## 扩展建议

1. **触摸支持**: 添加触摸设备的导航支持
2. **动画效果**: 导航时的平滑动画效果
3. **分组导航**: 支持导航项目的分组
4. **历史记录**: 导航历史的记录和回退
5. **自定义指示器**: 可视化的导航指示器

该导航组件为Odoo Web应用提供了强大的键盘导航功能，通过灵活的配置和智能的焦点管理确保了良好的用户体验和可访问性。

# Odoo PDF.js 库集成 (PDF.js Library Integration) 学习资料

## 文件概述

**文件路径**: `output/@web/libs/pdfjs.js`  
**原始路径**: `/web/static/src/libs/pdfjs.js`  
**模块类型**: 第三方库集成模块 - PDF.js 集成  
**代码行数**: 25 行  
**依赖关系**: 
- PDF.js 库 - Mozilla 开源 PDF 渲染引擎
- `@web/core/assets` - 资源管理 (loadJS)

## 模块功能

PDF.js 库集成模块是 Odoo Web 客户端对 Mozilla PDF.js 库的集成封装。该模块提供了：
- PDF.js 库的动态加载
- Worker 线程的配置管理
- 异步加载机制
- 错误处理和降级方案
- Odoo 环境的适配优化

这个模块使得 Odoo 能够在浏览器中直接渲染和显示 PDF 文档，无需依赖浏览器的原生 PDF 插件或外部应用程序。

## PDF.js 集成原理

### 动态加载机制
PDF.js 库采用动态加载策略：
1. **按需加载**: 只有在需要时才加载 PDF.js 库
2. **Worker 分离**: PDF 解析工作在独立的 Worker 线程中进行
3. **异步初始化**: 使用 Promise 机制确保库完全加载后再使用
4. **缓存机制**: 避免重复加载同一个库

### Worker 配置
- **性能优化**: 将 PDF 解析工作移到 Worker 线程，避免阻塞主线程
- **路径配置**: 正确配置 Worker 脚本的路径
- **错误处理**: 处理 Worker 加载失败的情况
- **兼容性**: 确保在不同浏览器环境中的兼容性

### 资源管理
- **CDN 支持**: 支持从 CDN 加载 PDF.js 库
- **本地回退**: 提供本地资源的回退机制
- **版本管理**: 确保加载正确版本的 PDF.js 库
- **依赖检查**: 检查库的加载状态和可用性

## 核心函数详解

### loadPDFJSAssets() - PDF.js 资源加载器
```javascript
let pdfJSLoadingPromise;

export async function loadPDFJSAssets() {
    if (!pdfJSLoadingPromise) {
        pdfJSLoadingPromise = loadJS("/web/static/lib/pdfjs/build/pdf.min.js").then(() => {
            window.pdfjsLib.GlobalWorkerOptions.workerSrc = "/web/static/lib/pdfjs/build/pdf.worker.min.js";
        });
    }
    return pdfJSLoadingPromise;
}
```

**功能特性**:
- **单例模式**: 确保 PDF.js 库只加载一次
- **Promise 缓存**: 缓存加载 Promise，避免重复加载
- **Worker 配置**: 自动配置 PDF Worker 的路径
- **异步加载**: 非阻塞的异步加载机制
- **错误传播**: 正确传播加载过程中的错误

**使用示例**:
```javascript
// 基本 PDF 查看器组件
class PDFViewerComponent extends Component {
    static template = xml`
        <div class="pdf-viewer">
            <div class="pdf-toolbar">
                <button t-on-click="previousPage" 
                        t-att-disabled="currentPage <= 1"
                        class="btn btn-sm btn-outline-primary">
                    <i class="fa fa-chevron-left" /> 上一页
                </button>
                <span class="page-info">
                    第 <span t-esc="currentPage" /> 页，共 <span t-esc="totalPages" /> 页
                </span>
                <button t-on-click="nextPage" 
                        t-att-disabled="currentPage >= totalPages"
                        class="btn btn-sm btn-outline-primary">
                    下一页 <i class="fa fa-chevron-right" />
                </button>
                <div class="zoom-controls">
                    <button t-on-click="zoomOut" class="btn btn-sm btn-outline-secondary">
                        <i class="fa fa-search-minus" />
                    </button>
                    <span class="zoom-level" t-esc="Math.round(scale * 100) + '%'" />
                    <button t-on-click="zoomIn" class="btn btn-sm btn-outline-secondary">
                        <i class="fa fa-search-plus" />
                    </button>
                </div>
            </div>
            <div class="pdf-container" t-ref="pdfContainer">
                <canvas t-ref="pdfCanvas" class="pdf-canvas" />
                <div t-if="loading" class="pdf-loading">
                    <i class="fa fa-spinner fa-spin" /> 加载中...
                </div>
                <div t-if="error" class="pdf-error">
                    <i class="fa fa-exclamation-triangle" /> 
                    <span t-esc="error" />
                </div>
            </div>
        </div>
    `;
    
    setup() {
        this.pdfContainerRef = useRef("pdfContainer");
        this.pdfCanvasRef = useRef("pdfCanvas");
        
        this.state = useState({
            loading: false,
            error: null,
            currentPage: 1,
            totalPages: 0,
            scale: 1.0
        });
        
        this.pdfDocument = null;
        this.currentPageObject = null;
        
        onMounted(() => {
            if (this.props.pdfUrl) {
                this.loadPDF(this.props.pdfUrl);
            }
        });
        
        onWillUnmount(() => {
            this.cleanup();
        });
    }
    
    async loadPDF(url) {
        try {
            this.state.loading = true;
            this.state.error = null;
            
            // 加载 PDF.js 库
            await loadPDFJSAssets();
            
            // 加载 PDF 文档
            const loadingTask = window.pdfjsLib.getDocument(url);
            this.pdfDocument = await loadingTask.promise;
            
            this.state.totalPages = this.pdfDocument.numPages;
            this.state.currentPage = 1;
            
            // 渲染第一页
            await this.renderPage(1);
            
        } catch (error) {
            console.error('PDF 加载失败:', error);
            this.state.error = '无法加载 PDF 文档';
        } finally {
            this.state.loading = false;
        }
    }
    
    async renderPage(pageNumber) {
        if (!this.pdfDocument) return;
        
        try {
            // 获取页面对象
            this.currentPageObject = await this.pdfDocument.getPage(pageNumber);
            
            // 计算视口
            const viewport = this.currentPageObject.getViewport({ scale: this.state.scale });
            
            // 设置画布尺寸
            const canvas = this.pdfCanvasRef.el;
            const context = canvas.getContext('2d');
            
            canvas.height = viewport.height;
            canvas.width = viewport.width;
            
            // 渲染页面
            const renderContext = {
                canvasContext: context,
                viewport: viewport
            };
            
            await this.currentPageObject.render(renderContext).promise;
            
        } catch (error) {
            console.error('页面渲染失败:', error);
            this.state.error = '页面渲染失败';
        }
    }
    
    async previousPage() {
        if (this.state.currentPage > 1) {
            this.state.currentPage--;
            await this.renderPage(this.state.currentPage);
        }
    }
    
    async nextPage() {
        if (this.state.currentPage < this.state.totalPages) {
            this.state.currentPage++;
            await this.renderPage(this.state.currentPage);
        }
    }
    
    async zoomIn() {
        this.state.scale = Math.min(this.state.scale * 1.2, 3.0);
        await this.renderPage(this.state.currentPage);
    }
    
    async zoomOut() {
        this.state.scale = Math.max(this.state.scale / 1.2, 0.5);
        await this.renderPage(this.state.currentPage);
    }
    
    cleanup() {
        if (this.pdfDocument) {
            this.pdfDocument.destroy();
            this.pdfDocument = null;
        }
        if (this.currentPageObject) {
            this.currentPageObject.cleanup();
            this.currentPageObject = null;
        }
    }
    
    get currentPage() {
        return this.state.currentPage;
    }
    
    get totalPages() {
        return this.state.totalPages;
    }
    
    get scale() {
        return this.state.scale;
    }
    
    get loading() {
        return this.state.loading;
    }
    
    get error() {
        return this.state.error;
    }
}

// 高级 PDF 查看器 with 文本选择和搜索
class AdvancedPDFViewer extends Component {
    static template = xml`
        <div class="advanced-pdf-viewer">
            <div class="pdf-header">
                <div class="pdf-controls">
                    <button t-on-click="previousPage" 
                            t-att-disabled="currentPage <= 1"
                            class="btn btn-sm btn-primary">
                        <i class="fa fa-chevron-left" />
                    </button>
                    <input type="number" 
                           t-model="currentPage" 
                           t-on-change="goToPage"
                           min="1" 
                           t-att-max="totalPages"
                           class="form-control form-control-sm page-input" />
                    <span class="page-total">/ <span t-esc="totalPages" /></span>
                    <button t-on-click="nextPage" 
                            t-att-disabled="currentPage >= totalPages"
                            class="btn btn-sm btn-primary">
                        <i class="fa fa-chevron-right" />
                    </button>
                </div>
                
                <div class="pdf-search">
                    <div class="input-group input-group-sm">
                        <input type="text" 
                               t-model="searchQuery" 
                               t-on-keydown="onSearchKeydown"
                               placeholder="搜索文本..."
                               class="form-control" />
                        <button t-on-click="searchNext" class="btn btn-outline-secondary">
                            <i class="fa fa-search" />
                        </button>
                    </div>
                    <div t-if="searchResults.length" class="search-results">
                        <small>找到 <span t-esc="searchResults.length" /> 个结果</small>
                    </div>
                </div>
                
                <div class="pdf-zoom">
                    <button t-on-click="zoomOut" class="btn btn-sm btn-outline-secondary">
                        <i class="fa fa-search-minus" />
                    </button>
                    <select t-model="scaleOption" t-on-change="onScaleChange" class="form-select form-select-sm">
                        <option value="auto">自适应</option>
                        <option value="page-fit">适合页面</option>
                        <option value="page-width">适合宽度</option>
                        <option value="0.5">50%</option>
                        <option value="0.75">75%</option>
                        <option value="1">100%</option>
                        <option value="1.25">125%</option>
                        <option value="1.5">150%</option>
                        <option value="2">200%</option>
                    </select>
                    <button t-on-click="zoomIn" class="btn btn-sm btn-outline-secondary">
                        <i class="fa fa-search-plus" />
                    </button>
                </div>
            </div>
            
            <div class="pdf-content" t-ref="pdfContent">
                <div class="pdf-page-container" t-ref="pageContainer">
                    <canvas t-ref="pdfCanvas" class="pdf-canvas" />
                    <div t-ref="textLayer" class="pdf-text-layer" />
                    <div t-ref="annotationLayer" class="pdf-annotation-layer" />
                </div>
                
                <div t-if="loading" class="pdf-loading-overlay">
                    <div class="loading-spinner">
                        <i class="fa fa-spinner fa-spin fa-2x" />
                        <p>加载 PDF 文档...</p>
                    </div>
                </div>
                
                <div t-if="error" class="pdf-error-overlay">
                    <div class="error-message">
                        <i class="fa fa-exclamation-triangle fa-2x" />
                        <h4>加载失败</h4>
                        <p t-esc="error" />
                        <button t-on-click="retry" class="btn btn-primary">重试</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setup() {
        this.pdfContentRef = useRef("pdfContent");
        this.pageContainerRef = useRef("pageContainer");
        this.pdfCanvasRef = useRef("pdfCanvas");
        this.textLayerRef = useRef("textLayer");
        this.annotationLayerRef = useRef("annotationLayer");
        
        this.state = useState({
            loading: false,
            error: null,
            currentPage: 1,
            totalPages: 0,
            scale: 1.0,
            scaleOption: '1',
            searchQuery: '',
            searchResults: [],
            currentSearchIndex: 0
        });
        
        this.pdfDocument = null;
        this.currentPageObject = null;
        this.textContent = null;
        this.renderTask = null;
        
        onMounted(() => {
            if (this.props.pdfUrl) {
                this.loadPDF(this.props.pdfUrl);
            }
            
            // 监听窗口大小变化
            this.resizeObserver = new ResizeObserver(() => {
                if (this.state.scaleOption === 'auto' || this.state.scaleOption === 'page-fit' || this.state.scaleOption === 'page-width') {
                    this.updateAutoScale();
                }
            });
            this.resizeObserver.observe(this.pdfContentRef.el);
        });
        
        onWillUnmount(() => {
            this.cleanup();
            if (this.resizeObserver) {
                this.resizeObserver.disconnect();
            }
        });
    }
    
    async loadPDF(url) {
        try {
            this.state.loading = true;
            this.state.error = null;
            
            // 加载 PDF.js 库
            await loadPDFJSAssets();
            
            // 配置 PDF.js
            const loadingTask = window.pdfjsLib.getDocument({
                url: url,
                cMapUrl: '/web/static/lib/pdfjs/cmaps/',
                cMapPacked: true,
                enableXfa: true
            });
            
            // 监听加载进度
            loadingTask.onProgress = (progress) => {
                if (progress.total > 0) {
                    const percent = Math.round((progress.loaded / progress.total) * 100);
                    console.log(`PDF 加载进度: ${percent}%`);
                }
            };
            
            this.pdfDocument = await loadingTask.promise;
            
            this.state.totalPages = this.pdfDocument.numPages;
            this.state.currentPage = 1;
            
            // 渲染第一页
            await this.renderPage(1);
            
        } catch (error) {
            console.error('PDF 加载失败:', error);
            this.state.error = this.getErrorMessage(error);
        } finally {
            this.state.loading = false;
        }
    }
    
    getErrorMessage(error) {
        if (error.name === 'InvalidPDFException') {
            return '无效的 PDF 文件';
        } else if (error.name === 'MissingPDFException') {
            return 'PDF 文件不存在';
        } else if (error.name === 'UnexpectedResponseException') {
            return '网络错误，无法加载 PDF';
        } else {
            return '加载 PDF 时发生未知错误';
        }
    }
    
    async renderPage(pageNumber) {
        if (!this.pdfDocument) return;
        
        try {
            // 取消之前的渲染任务
            if (this.renderTask) {
                this.renderTask.cancel();
            }
            
            // 获取页面对象
            this.currentPageObject = await this.pdfDocument.getPage(pageNumber);
            
            // 计算视口
            const viewport = this.currentPageObject.getViewport({ scale: this.state.scale });
            
            // 设置画布
            const canvas = this.pdfCanvasRef.el;
            const context = canvas.getContext('2d');
            
            canvas.height = viewport.height;
            canvas.width = viewport.width;
            
            // 设置容器尺寸
            this.pageContainerRef.el.style.width = viewport.width + 'px';
            this.pageContainerRef.el.style.height = viewport.height + 'px';
            
            // 渲染页面
            const renderContext = {
                canvasContext: context,
                viewport: viewport
            };
            
            this.renderTask = this.currentPageObject.render(renderContext);
            await this.renderTask.promise;
            
            // 渲染文本层
            await this.renderTextLayer(viewport);
            
            // 渲染注释层
            await this.renderAnnotationLayer(viewport);
            
        } catch (error) {
            if (error.name !== 'RenderingCancelledException') {
                console.error('页面渲染失败:', error);
                this.state.error = '页面渲染失败';
            }
        }
    }
    
    async renderTextLayer(viewport) {
        // 清空文本层
        this.textLayerRef.el.innerHTML = '';
        this.textLayerRef.el.style.width = viewport.width + 'px';
        this.textLayerRef.el.style.height = viewport.height + 'px';
        
        // 获取文本内容
        this.textContent = await this.currentPageObject.getTextContent();
        
        // 渲染文本层
        const textLayerRenderTask = window.pdfjsLib.renderTextLayer({
            textContent: this.textContent,
            container: this.textLayerRef.el,
            viewport: viewport,
            textDivs: []
        });
        
        await textLayerRenderTask.promise;
    }
    
    async renderAnnotationLayer(viewport) {
        // 清空注释层
        this.annotationLayerRef.el.innerHTML = '';
        
        // 获取注释
        const annotations = await this.currentPageObject.getAnnotations();
        
        if (annotations.length > 0) {
            // 渲染注释层
            window.pdfjsLib.AnnotationLayer.render({
                viewport: viewport,
                div: this.annotationLayerRef.el,
                annotations: annotations,
                page: this.currentPageObject,
                linkService: {
                    externalLinkTarget: 2, // 在新窗口打开外部链接
                    externalLinkRel: 'noopener noreferrer nofollow'
                }
            });
        }
    }

    async goToPage() {
        const pageNumber = parseInt(this.state.currentPage);
        if (pageNumber >= 1 && pageNumber <= this.state.totalPages) {
            await this.renderPage(pageNumber);
        }
    }

    async previousPage() {
        if (this.state.currentPage > 1) {
            this.state.currentPage--;
            await this.renderPage(this.state.currentPage);
        }
    }

    async nextPage() {
        if (this.state.currentPage < this.state.totalPages) {
            this.state.currentPage++;
            await this.renderPage(this.state.currentPage);
        }
    }

    onScaleChange() {
        const scaleValue = this.state.scaleOption;

        if (scaleValue === 'auto' || scaleValue === 'page-fit' || scaleValue === 'page-width') {
            this.updateAutoScale();
        } else {
            this.state.scale = parseFloat(scaleValue);
            this.renderPage(this.state.currentPage);
        }
    }

    updateAutoScale() {
        if (!this.currentPageObject) return;

        const container = this.pdfContentRef.el;
        const containerWidth = container.clientWidth - 40; // 留出边距
        const containerHeight = container.clientHeight - 40;

        const viewport = this.currentPageObject.getViewport({ scale: 1.0 });

        let scale;
        if (this.state.scaleOption === 'page-width') {
            scale = containerWidth / viewport.width;
        } else if (this.state.scaleOption === 'page-fit') {
            const scaleX = containerWidth / viewport.width;
            const scaleY = containerHeight / viewport.height;
            scale = Math.min(scaleX, scaleY);
        } else { // auto
            scale = Math.min(containerWidth / viewport.width, 1.0);
        }

        this.state.scale = scale;
        this.renderPage(this.state.currentPage);
    }

    async zoomIn() {
        this.state.scale = Math.min(this.state.scale * 1.2, 3.0);
        this.state.scaleOption = this.state.scale.toString();
        await this.renderPage(this.state.currentPage);
    }

    async zoomOut() {
        this.state.scale = Math.max(this.state.scale / 1.2, 0.5);
        this.state.scaleOption = this.state.scale.toString();
        await this.renderPage(this.state.currentPage);
    }

    onSearchKeydown(event) {
        if (event.key === 'Enter') {
            this.searchNext();
        }
    }

    async searchNext() {
        if (!this.state.searchQuery.trim()) return;

        // 简单的文本搜索实现
        if (this.textContent) {
            const query = this.state.searchQuery.toLowerCase();
            const textItems = this.textContent.items;
            const results = [];

            textItems.forEach((item, index) => {
                if (item.str.toLowerCase().includes(query)) {
                    results.push({
                        pageIndex: this.state.currentPage - 1,
                        itemIndex: index,
                        text: item.str
                    });
                }
            });

            this.state.searchResults = results;

            if (results.length > 0) {
                this.highlightSearchResults();
            }
        }
    }

    highlightSearchResults() {
        // 移除之前的高亮
        const existingHighlights = this.textLayerRef.el.querySelectorAll('.search-highlight');
        existingHighlights.forEach(el => {
            el.classList.remove('search-highlight');
        });

        // 添加新的高亮
        const textDivs = this.textLayerRef.el.querySelectorAll('span');
        const query = this.state.searchQuery.toLowerCase();

        textDivs.forEach(div => {
            if (div.textContent.toLowerCase().includes(query)) {
                div.classList.add('search-highlight');
            }
        });
    }

    retry() {
        if (this.props.pdfUrl) {
            this.loadPDF(this.props.pdfUrl);
        }
    }

    cleanup() {
        if (this.renderTask) {
            this.renderTask.cancel();
            this.renderTask = null;
        }

        if (this.pdfDocument) {
            this.pdfDocument.destroy();
            this.pdfDocument = null;
        }

        if (this.currentPageObject) {
            this.currentPageObject.cleanup();
            this.currentPageObject = null;
        }
    }

    // Getters for template
    get currentPage() { return this.state.currentPage; }
    get totalPages() { return this.state.totalPages; }
    get loading() { return this.state.loading; }
    get error() { return this.state.error; }
    get searchQuery() { return this.state.searchQuery; }
    get searchResults() { return this.state.searchResults; }
    get scaleOption() { return this.state.scaleOption; }
}
```

## 高级应用模式

### 1. PDF 文档管理器
```javascript
class PDFDocumentManager {
    constructor() {
        this.documents = new Map();
        this.cache = new Map();
        this.maxCacheSize = 10;
    }

    async loadDocument(url, options = {}) {
        // 检查缓存
        if (this.cache.has(url)) {
            return this.cache.get(url);
        }

        // 确保 PDF.js 已加载
        await loadPDFJSAssets();

        try {
            const loadingTask = window.pdfjsLib.getDocument({
                url: url,
                cMapUrl: '/web/static/lib/pdfjs/cmaps/',
                cMapPacked: true,
                ...options
            });

            const pdfDocument = await loadingTask.promise;

            // 添加到缓存
            this.addToCache(url, pdfDocument);

            return pdfDocument;

        } catch (error) {
            console.error('PDF 文档加载失败:', error);
            throw error;
        }
    }

    addToCache(url, document) {
        // 如果缓存已满，移除最旧的文档
        if (this.cache.size >= this.maxCacheSize) {
            const firstKey = this.cache.keys().next().value;
            const oldDocument = this.cache.get(firstKey);
            oldDocument.destroy();
            this.cache.delete(firstKey);
        }

        this.cache.set(url, document);
    }

    async getDocumentInfo(url) {
        const document = await this.loadDocument(url);

        const metadata = await document.getMetadata();
        const outline = await document.getOutline();

        return {
            numPages: document.numPages,
            fingerprint: document.fingerprint,
            metadata: metadata.info,
            outline: outline,
            permissions: await document.getPermissions()
        };
    }

    async extractText(url, pageNumber = null) {
        const document = await this.loadDocument(url);

        if (pageNumber !== null) {
            // 提取单页文本
            const page = await document.getPage(pageNumber);
            const textContent = await page.getTextContent();
            return textContent.items.map(item => item.str).join(' ');
        } else {
            // 提取全文
            const allText = [];
            for (let i = 1; i <= document.numPages; i++) {
                const page = await document.getPage(i);
                const textContent = await page.getTextContent();
                const pageText = textContent.items.map(item => item.str).join(' ');
                allText.push(pageText);
            }
            return allText.join('\n\n');
        }
    }

    async searchInDocument(url, query, options = {}) {
        const document = await this.loadDocument(url);
        const results = [];

        const {
            caseSensitive = false,
            wholeWord = false,
            maxResults = 100
        } = options;

        let searchQuery = query;
        if (!caseSensitive) {
            searchQuery = query.toLowerCase();
        }

        for (let pageNum = 1; pageNum <= document.numPages && results.length < maxResults; pageNum++) {
            const page = await document.getPage(pageNum);
            const textContent = await page.getTextContent();

            textContent.items.forEach((item, itemIndex) => {
                let text = item.str;
                if (!caseSensitive) {
                    text = text.toLowerCase();
                }

                if (wholeWord) {
                    const regex = new RegExp(`\\b${searchQuery}\\b`, caseSensitive ? 'g' : 'gi');
                    const matches = text.match(regex);
                    if (matches) {
                        results.push({
                            pageNumber: pageNum,
                            itemIndex: itemIndex,
                            text: item.str,
                            matches: matches.length
                        });
                    }
                } else {
                    if (text.includes(searchQuery)) {
                        results.push({
                            pageNumber: pageNum,
                            itemIndex: itemIndex,
                            text: item.str,
                            position: text.indexOf(searchQuery)
                        });
                    }
                }
            });
        }

        return results;
    }

    clearCache() {
        this.cache.forEach(document => {
            document.destroy();
        });
        this.cache.clear();
    }

    destroy() {
        this.clearCache();
        this.documents.clear();
    }
}
```

### 2. PDF 缩略图生成器
```javascript
class PDFThumbnailGenerator {
    constructor(options = {}) {
        this.defaultOptions = {
            scale: 0.3,
            quality: 0.8,
            format: 'image/jpeg',
            ...options
        };
    }

    async generateThumbnail(pdfUrl, pageNumber = 1, options = {}) {
        const finalOptions = { ...this.defaultOptions, ...options };

        try {
            // 确保 PDF.js 已加载
            await loadPDFJSAssets();

            // 加载文档
            const loadingTask = window.pdfjsLib.getDocument(pdfUrl);
            const pdfDocument = await loadingTask.promise;

            // 获取指定页面
            const page = await pdfDocument.getPage(pageNumber);

            // 创建画布
            const viewport = page.getViewport({ scale: finalOptions.scale });
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');

            canvas.height = viewport.height;
            canvas.width = viewport.width;

            // 渲染页面
            const renderContext = {
                canvasContext: context,
                viewport: viewport
            };

            await page.render(renderContext).promise;

            // 转换为图片
            const imageDataUrl = canvas.toDataURL(finalOptions.format, finalOptions.quality);

            // 清理资源
            page.cleanup();
            pdfDocument.destroy();

            return imageDataUrl;

        } catch (error) {
            console.error('缩略图生成失败:', error);
            throw error;
        }
    }

    async generateThumbnails(pdfUrl, options = {}) {
        const finalOptions = { ...this.defaultOptions, ...options };
        const thumbnails = [];

        try {
            await loadPDFJSAssets();

            const loadingTask = window.pdfjsLib.getDocument(pdfUrl);
            const pdfDocument = await loadingTask.promise;

            // 生成所有页面的缩略图
            for (let pageNum = 1; pageNum <= pdfDocument.numPages; pageNum++) {
                const page = await pdfDocument.getPage(pageNum);

                const viewport = page.getViewport({ scale: finalOptions.scale });
                const canvas = document.createElement('canvas');
                const context = canvas.getContext('2d');

                canvas.height = viewport.height;
                canvas.width = viewport.width;

                const renderContext = {
                    canvasContext: context,
                    viewport: viewport
                };

                await page.render(renderContext).promise;

                const imageDataUrl = canvas.toDataURL(finalOptions.format, finalOptions.quality);

                thumbnails.push({
                    pageNumber: pageNum,
                    thumbnail: imageDataUrl,
                    width: viewport.width,
                    height: viewport.height
                });

                page.cleanup();

                // 可选的进度回调
                if (finalOptions.onProgress) {
                    finalOptions.onProgress(pageNum, pdfDocument.numPages);
                }
            }

            pdfDocument.destroy();
            return thumbnails;

        } catch (error) {
            console.error('批量缩略图生成失败:', error);
            throw error;
        }
    }
}

// 使用示例
class PDFThumbnailGrid extends Component {
    static template = xml`
        <div class="pdf-thumbnail-grid">
            <div class="thumbnail-header">
                <h5>PDF 页面预览</h5>
                <button t-on-click="generateThumbnails"
                        t-att-disabled="loading"
                        class="btn btn-primary btn-sm">
                    <i t-if="loading" class="fa fa-spinner fa-spin" />
                    <i t-else="" class="fa fa-refresh" />
                    生成缩略图
                </button>
            </div>

            <div class="thumbnails-container">
                <div t-foreach="thumbnails" t-as="thumb" t-key="thumb.pageNumber"
                     class="thumbnail-item"
                     t-on-click="() => this.selectPage(thumb.pageNumber)">
                    <img t-att-src="thumb.thumbnail"
                         t-att-alt="'第 ' + thumb.pageNumber + ' 页'"
                         class="thumbnail-image" />
                    <div class="thumbnail-label">
                        第 <span t-esc="thumb.pageNumber" /> 页
                    </div>
                </div>
            </div>

            <div t-if="loading" class="loading-overlay">
                <div class="progress">
                    <div class="progress-bar"
                         t-att-style="'width: ' + progress + '%'">
                        <span t-esc="progress + '%'" />
                    </div>
                </div>
            </div>
        </div>
    `;

    setup() {
        this.state = useState({
            thumbnails: [],
            loading: false,
            progress: 0
        });

        this.thumbnailGenerator = new PDFThumbnailGenerator({
            scale: 0.2,
            quality: 0.7
        });
    }

    async generateThumbnails() {
        if (!this.props.pdfUrl) return;

        this.state.loading = true;
        this.state.progress = 0;

        try {
            const thumbnails = await this.thumbnailGenerator.generateThumbnails(
                this.props.pdfUrl,
                {
                    onProgress: (current, total) => {
                        this.state.progress = Math.round((current / total) * 100);
                    }
                }
            );

            this.state.thumbnails = thumbnails;

        } catch (error) {
            console.error('缩略图生成失败:', error);
            this.env.services.notification.add(
                '缩略图生成失败',
                { type: 'danger' }
            );
        } finally {
            this.state.loading = false;
        }
    }

    selectPage(pageNumber) {
        this.props.onPageSelect?.(pageNumber);
    }

    get thumbnails() { return this.state.thumbnails; }
    get loading() { return this.state.loading; }
    get progress() { return this.state.progress; }
}
```

## 最佳实践

### 1. 性能优化
```javascript
// ✅ 推荐：使用 Worker 线程
await loadPDFJSAssets();
// Worker 会自动配置，无需手动管理

// ✅ 推荐：合理的缓存策略
class PDFCache {
    constructor(maxSize = 5) {
        this.cache = new Map();
        this.maxSize = maxSize;
    }

    set(key, value) {
        if (this.cache.size >= this.maxSize) {
            const firstKey = this.cache.keys().next().value;
            const oldDoc = this.cache.get(firstKey);
            oldDoc.destroy();
            this.cache.delete(firstKey);
        }
        this.cache.set(key, value);
    }
}
```

### 2. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
async function safePDFOperation(operation) {
    try {
        return await operation();
    } catch (error) {
        if (error.name === 'InvalidPDFException') {
            throw new Error('PDF 文件格式无效');
        } else if (error.name === 'MissingPDFException') {
            throw new Error('PDF 文件不存在');
        } else if (error.name === 'UnexpectedResponseException') {
            throw new Error('网络错误');
        } else {
            throw new Error('PDF 处理失败');
        }
    }
}
```

### 3. 内存管理
```javascript
// ✅ 推荐：正确的资源清理
class PDFResourceManager {
    constructor() {
        this.activeDocuments = new Set();
        this.activePages = new Set();
    }

    registerDocument(doc) {
        this.activeDocuments.add(doc);
    }

    registerPage(page) {
        this.activePages.add(page);
    }

    cleanup() {
        this.activePages.forEach(page => {
            page.cleanup();
        });
        this.activeDocuments.forEach(doc => {
            doc.destroy();
        });
        this.activePages.clear();
        this.activeDocuments.clear();
    }
}
```

## 总结

Odoo PDF.js 库集成模块提供了强大的 PDF 处理能力：

**核心优势**:
- **动态加载**: 按需加载 PDF.js 库，优化初始加载性能
- **Worker 支持**: 自动配置 Worker 线程，避免阻塞主线程
- **异步处理**: 完整的 Promise 支持，适合现代异步编程
- **错误处理**: 完善的错误处理和降级机制
- **资源管理**: 正确的内存管理和资源清理

**适用场景**:
- PDF 文档查看器
- 文档预览功能
- PDF 内容搜索
- 缩略图生成
- 文档注释系统

**设计优势**:
- 单例加载模式
- Promise 缓存机制
- 自动 Worker 配置
- 错误传播处理

这个集成模块为 Odoo Web 客户端提供了完整的 PDF 处理能力，是构建文档管理系统的重要基础。

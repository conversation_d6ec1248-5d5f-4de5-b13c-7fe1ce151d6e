# Odoo Bootstrap 库扩展 (Bootstrap Library Extensions) 学习资料

## 文件概述

**文件路径**: `output/@web/libs/bootstrap.js`  
**原始路径**: `/web/static/src/libs/bootstrap.js`  
**模块类型**: 第三方库扩展模块 - Bootstrap 扩展  
**代码行数**: 120 行  
**依赖关系**: 
- `@web/core/utils/scrolling` - 滚动工具 (compensateScrollbar, getScrollingElement)
- Bootstrap 5 - 全局 Tooltip, Dropdown, Modal 组件

## 模块功能

Bootstrap 库扩展模块是 Odoo Web 客户端对 Bootstrap 5 框架的定制化扩展。该模块提供了：
- Bootstrap 组件的安全性增强
- Tooltip 组件的行为优化
- Dropdown 组件的定位改进
- Modal 组件的滚动条补偿
- 移动端适配优化
- Odoo 特定的 UI/UX 改进

这个模块确保 Bootstrap 组件在 Odoo 环境中的最佳表现，解决了原生 Bootstrap 在复杂企业应用中的一些局限性。

## Bootstrap 扩展原理

### 安全性增强
通过扩展 Bootstrap 的 HTML 净化白名单，允许更多常用的 HTML 标签和属性：
1. **标签扩展**: 添加表格、语义化标签、按钮等
2. **属性扩展**: 允许 style、data-* 等常用属性
3. **安全平衡**: 在功能性和安全性之间找到平衡

### 组件行为优化
- **Tooltip**: 防止多个 tooltip 同时显示，优化显示逻辑
- **Dropdown**: 启用动态定位，防止菜单溢出
- **Modal**: 改进滚动条补偿机制，支持非 body 滚动容器

### 移动端适配
- **响应式设计**: 针对移动设备优化组件行为
- **触摸友好**: 改进触摸设备上的交互体验
- **性能优化**: 减少移动设备上的性能开销

## 核心扩展详解

### 1. Bootstrap 安全净化扩展
```javascript
const bsSanitizeAllowList = Tooltip.Default.allowList;

// 扩展全局允许的属性
bsSanitizeAllowList["*"].push("title", "style", /^data-[\w-]+/);

// 添加语义化 HTML5 标签
bsSanitizeAllowList.header = [];
bsSanitizeAllowList.main = [];
bsSanitizeAllowList.footer = [];

// 添加表格相关标签
bsSanitizeAllowList.table = [];
bsSanitizeAllowList.thead = [];
bsSanitizeAllowList.tbody = [];
bsSanitizeAllowList.tr = [];
bsSanitizeAllowList.th = ["colspan", "rowspan"];
bsSanitizeAllowList.td = ["colspan", "rowspan"];

// 添加其他常用标签
bsSanitizeAllowList.button = ["type"];
bsSanitizeAllowList.article = [];
bsSanitizeAllowList.section = [];
```

**功能特性**:
- **安全扩展**: 在保持安全性的前提下扩展允许的 HTML 内容
- **表格支持**: 完整支持表格结构在 tooltip/popover 中的使用
- **语义化**: 支持 HTML5 语义化标签
- **数据属性**: 允许 data-* 属性用于数据绑定
- **样式支持**: 允许内联样式用于动态样式设置

**使用示例**:
```javascript
// 在 Odoo 组件中使用扩展的 Bootstrap Tooltip
class EnhancedTooltipComponent extends Component {
    static template = xml`
        <div class="tooltip-container">
            <button t-ref="tooltipButton" 
                    class="btn btn-primary"
                    data-bs-toggle="tooltip"
                    data-bs-html="true"
                    t-att-data-bs-title="tooltipContent">
                悬停查看详情
            </button>
        </div>
    `;
    
    setup() {
        this.tooltipRef = useRef("tooltipButton");
        
        // 复杂的 HTML 内容，包含表格和样式
        this.tooltipContent = `
            <div style="max-width: 300px;">
                <header>
                    <h5>产品详情</h5>
                </header>
                <main>
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>属性</th>
                                <th>值</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>价格</td>
                                <td data-currency="USD">$99.99</td>
                            </tr>
                            <tr>
                                <td>库存</td>
                                <td style="color: green;">有货</td>
                            </tr>
                        </tbody>
                    </table>
                </main>
                <footer>
                    <button type="button" class="btn btn-sm btn-outline-primary">
                        查看详情
                    </button>
                </footer>
            </div>
        `;
        
        onMounted(() => {
            // 初始化 Bootstrap Tooltip
            this.tooltip = new Tooltip(this.tooltipRef.el, {
                // 使用扩展的安全净化设置
                sanitize: true,
                // 自定义配置
                placement: 'auto',
                trigger: 'hover focus',
                delay: { show: 500, hide: 100 }
            });
        });
        
        onWillUnmount(() => {
            // 清理 tooltip
            if (this.tooltip) {
                this.tooltip.dispose();
            }
        });
    }
}

// 动态内容的 Popover 示例
class DynamicPopoverComponent extends Component {
    static template = xml`
        <div class="popover-container">
            <button t-ref="popoverButton" 
                    class="btn btn-info"
                    data-bs-toggle="popover"
                    data-bs-html="true">
                点击查看统计
            </button>
        </div>
    `;
    
    setup() {
        this.popoverRef = useRef("popoverButton");
        this.stats = useState({
            sales: 1250,
            orders: 45,
            customers: 23
        });
        
        onMounted(() => {
            this.popover = new Popover(this.popoverRef.el, {
                content: () => this.generateStatsContent(),
                html: true,
                sanitize: true,
                placement: 'bottom',
                trigger: 'click'
            });
        });
        
        onWillUnmount(() => {
            if (this.popover) {
                this.popover.dispose();
            }
        });
    }
    
    generateStatsContent() {
        return `
            <article class="stats-popover" style="min-width: 200px;">
                <header>
                    <h6>今日统计</h6>
                </header>
                <section>
                    <table class="table table-borderless table-sm">
                        <tbody>
                            <tr>
                                <td>销售额:</td>
                                <td style="font-weight: bold; color: #28a745;">
                                    $${this.stats.sales.toLocaleString()}
                                </td>
                            </tr>
                            <tr>
                                <td>订单数:</td>
                                <td style="font-weight: bold; color: #007bff;">
                                    ${this.stats.orders}
                                </td>
                            </tr>
                            <tr>
                                <td>新客户:</td>
                                <td style="font-weight: bold; color: #6f42c1;">
                                    ${this.stats.customers}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </section>
                <footer style="text-align: center; margin-top: 10px;">
                    <button type="button" class="btn btn-sm btn-primary" 
                            data-action="view-details">
                        查看详细报告
                    </button>
                </footer>
            </article>
        `;
    }
}
```

### 2. Tooltip 行为优化
```javascript
// 重写 Tooltip 默认配置
Tooltip.Default.placement = "auto";
Tooltip.Default.fallbackPlacement = ["bottom", "right", "left", "top"];
Tooltip.Default.html = true;
Tooltip.Default.trigger = "hover";
Tooltip.Default.container = "body";
Tooltip.Default.boundary = "window";
Tooltip.Default.delay = { show: 1000, hide: 0 };

// 重写 show 方法防止多个 tooltip 同时显示
const bootstrapShowFunction = Tooltip.prototype.show;
Tooltip.prototype.show = function () {
    // 移除所有现有的 tooltip
    document.querySelectorAll(".tooltip").forEach((el) => el.remove());
    
    const errorsToIgnore = ["Please use show on visible elements"];
    try {
        return bootstrapShowFunction.call(this);
    } catch (error) {
        if (errorsToIgnore.includes(error.message)) {
            return 0;
        }
        throw error;
    }
};
```

**功能特性**:
- **智能定位**: 自动选择最佳显示位置
- **单例模式**: 确保同时只显示一个 tooltip
- **错误处理**: 优雅处理常见的显示错误
- **延迟显示**: 避免鼠标快速移动时的闪烁
- **HTML 支持**: 默认启用 HTML 内容支持

**使用示例**:
```javascript
// 智能 Tooltip 管理器
class TooltipManager {
    constructor() {
        this.tooltips = new Map();
        this.defaultConfig = {
            placement: 'auto',
            html: true,
            sanitize: true,
            delay: { show: 800, hide: 100 },
            boundary: 'viewport'
        };
    }
    
    createTooltip(element, content, config = {}) {
        const finalConfig = { ...this.defaultConfig, ...config };
        
        // 如果元素已有 tooltip，先销毁
        this.destroyTooltip(element);
        
        const tooltip = new Tooltip(element, {
            ...finalConfig,
            title: content
        });
        
        this.tooltips.set(element, tooltip);
        return tooltip;
    }
    
    destroyTooltip(element) {
        const tooltip = this.tooltips.get(element);
        if (tooltip) {
            tooltip.dispose();
            this.tooltips.delete(element);
        }
    }
    
    updateTooltip(element, newContent) {
        const tooltip = this.tooltips.get(element);
        if (tooltip) {
            tooltip.setContent({ '.tooltip-inner': newContent });
        }
    }
    
    hideAllTooltips() {
        this.tooltips.forEach(tooltip => {
            tooltip.hide();
        });
    }
    
    destroyAllTooltips() {
        this.tooltips.forEach((tooltip, element) => {
            tooltip.dispose();
        });
        this.tooltips.clear();
    }
}

// 在组件中使用 TooltipManager
class ComponentWithManagedTooltips extends Component {
    static template = xml`
        <div class="managed-tooltips">
            <button t-foreach="items" t-as="item" t-key="item.id"
                    t-ref="button_{{item.id}}"
                    class="btn btn-outline-primary m-1"
                    t-on-mouseenter="(ev) => this.showTooltip(ev, item)"
                    t-on-mouseleave="hideTooltip">
                <span t-esc="item.name" />
            </button>
        </div>
    `;
    
    setup() {
        this.tooltipManager = new TooltipManager();
        this.items = useState([
            { id: 1, name: 'Item 1', description: 'This is item 1' },
            { id: 2, name: 'Item 2', description: 'This is item 2' },
            { id: 3, name: 'Item 3', description: 'This is item 3' }
        ]);
        
        onWillUnmount(() => {
            this.tooltipManager.destroyAllTooltips();
        });
    }
    
    showTooltip(event, item) {
        const content = `
            <div>
                <strong>${item.name}</strong><br>
                <small>${item.description}</small>
            </div>
        `;
        
        this.tooltipManager.createTooltip(event.target, content, {
            placement: 'top',
            delay: { show: 500, hide: 0 }
        });
    }
    
    hideTooltip(event) {
        this.tooltipManager.destroyTooltip(event.target);
    }
}
```

### 3. Dropdown 动态定位优化
```javascript
// 禁用 Bootstrap 的 navbar 检测，启用动态定位
Dropdown.prototype._detectNavbar = function () {
    return false;
};
```

**功能特性**:
- **动态定位**: 始终启用 Popper.js 的动态定位功能
- **溢出防护**: 防止下拉菜单超出视窗边界
- **智能翻转**: 自动调整菜单显示方向
- **性能优化**: 减少不必要的位置计算

### 4. Modal 滚动条补偿优化
```javascript
// 重写 Modal 的滚动条调整方法
const bsAdjustDialogFunction = Modal.prototype._adjustDialog;
Modal.prototype._adjustDialog = function () {
    const document = this._element.ownerDocument;

    this._scrollBar.reset();
    document.body.classList.remove("modal-open");

    // 使用 Odoo 的滚动工具获取滚动元素
    const scrollable = getScrollingElement(document);
    if (document.body.contains(scrollable)) {
        compensateScrollbar(scrollable, true);
    }

    this._scrollBar.hide();
    document.body.classList.add("modal-open");

    return bsAdjustDialogFunction.apply(this, arguments);
};
```

**功能特性**:
- **智能滚动检测**: 自动检测实际的滚动容器
- **滚动条补偿**: 防止模态框显示时的布局跳动
- **多容器支持**: 支持非 body 元素作为滚动容器
- **状态管理**: 正确管理 modal-open 类的添加和移除

## 最佳实践

### 1. 安全性考虑
```javascript
// ✅ 推荐：谨慎使用 HTML 内容
const safeTooltipContent = (data) => {
    // 对用户输入进行转义
    const escapeHtml = (text) => {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    };

    return `
        <div>
            <strong>${escapeHtml(data.title)}</strong><br>
            <small>${escapeHtml(data.description)}</small>
        </div>
    `;
};
```

### 2. 性能优化
```javascript
// ✅ 推荐：延迟初始化
class LazyBootstrapComponents {
    constructor() {
        this.initialized = new Set();
    }

    initializeTooltips(container) {
        if (this.initialized.has('tooltips')) return;

        const tooltipElements = container.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltipElements.forEach(element => {
            new Tooltip(element);
        });

        this.initialized.add('tooltips');
    }
}
```

### 3. 内存管理
```javascript
// ✅ 推荐：正确的资源清理
class BootstrapComponentManager {
    constructor() {
        this.components = new WeakMap();
    }

    registerComponent(element, component) {
        this.components.set(element, component);
    }

    cleanup(element) {
        const component = this.components.get(element);
        if (component && component.dispose) {
            component.dispose();
            this.components.delete(element);
        }
    }
}
```

## 总结

Odoo Bootstrap 库扩展模块提供了对 Bootstrap 5 框架的重要增强：

**核心优势**:
- **安全增强**: 扩展 HTML 净化白名单，支持更丰富的内容
- **行为优化**: 改进 Tooltip、Dropdown、Modal 的默认行为
- **Odoo 集成**: 与 Odoo 的滚动工具完美集成
- **移动适配**: 针对移动设备的特殊优化
- **企业级**: 适合复杂企业应用的需求

**适用场景**:
- 企业级 Web 应用
- 复杂的用户界面
- 移动端适配
- 内容丰富的提示框
- 多层级导航菜单

**设计优势**:
- 非侵入式扩展
- 向后兼容性
- 性能优化
- 安全性保障

这个扩展模块确保了 Bootstrap 组件在 Odoo 环境中的最佳表现，是构建现代企业级 Web 应用的重要基础。

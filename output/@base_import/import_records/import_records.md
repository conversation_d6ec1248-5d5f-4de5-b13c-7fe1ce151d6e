# Import Records - 导入记录组件

## 概述

`import_records.js` 是 Odoo 基础导入模块的记录导入菜单组件，专门用于在各种视图中提供"导入记录"菜单项。该组件基于OWL框架和Web核心服务，集成了菜单注册、条件显示、操作触发等核心功能，为基础导入系统提供了完整的菜单入口支持，是数据导入系统用户访问的重要入口组件。

## 文件信息
- **路径**: `/base_import/static/src/import_records/import_records.js`
- **行数**: 58
- **模块**: `@base_import/import_records/import_records`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                    // OWL框架
'@web/core/dropdown/dropdown_item'            // 下拉菜单项
'@web/core/registry'                           // 注册表
'@web/core/utils/hooks'                        // 钩子工具
'@web/core/utils/strings'                      // 字符串工具
'@web/search/action_menus/action_menus'        // 操作菜单
```

## 核心功能

### 1. ImportRecords 组件

```javascript
const ImportRecords = class ImportRecords extends Component {
    static template = "base_import.ImportRecords";
    static components = { DropdownItem };
    static props = {};
}
```

**组件特性**:
- **继承扩展**: 继承OWL Component的所有功能
- **子组件集成**: 集成下拉菜单项组件
- **无属性**: 不需要外部属性配置
- **模板绑定**: 使用专门的导入记录模板

### 2. 组件初始化

```javascript
setup() {
    this.action = useService("action");
}
```

**初始化功能**:
- **服务集成**: 集成操作服务用于执行导入操作
- **简洁设计**: 最小化的初始化配置
- **服务准备**: 为后续操作准备必要的服务

### 3. 导入记录操作

```javascript
importRecords() {
    const { context, resModel } = this.env.searchModel;
    this.action.doAction({
        type: "ir.actions.client",
        tag: "import",
        params: { active_model: resModel, context },
    });
}
```

**导入操作功能**:
- **上下文获取**: 从搜索模型中获取上下文和资源模型
- **操作执行**: 执行客户端导入操作
- **参数传递**: 传递活动模型和上下文参数
- **标签指定**: 使用"import"标签标识导入操作

### 4. 菜单项配置

```javascript
const importRecordsItem = {
    Component: ImportRecords,
    groupNumber: STATIC_ACTIONS_GROUP_NUMBER,
    isDisplayed: ({ config, isSmall }) =>
        !isSmall &&
        config.actionType === "ir.actions.act_window" &&
        ["kanban", "list"].includes(config.viewType) &&
        exprToBoolean(config.viewArch.getAttribute("import"), true) &&
        exprToBoolean(config.viewArch.getAttribute("create"), true),
};
```

**菜单项配置功能**:
- **组件绑定**: 绑定ImportRecords组件
- **分组设置**: 设置菜单项的分组编号
- **显示条件**: 配置复杂的显示条件逻辑
- **视图限制**: 仅在特定视图类型中显示
- **权限检查**: 检查导入和创建权限

### 5. 菜单注册

```javascript
cogMenuRegistry.add("import-menu", importRecordsItem, { sequence: 1 });
```

**菜单注册功能**:
- **注册表添加**: 将菜单项添加到齿轮菜单注册表
- **唯一标识**: 使用"import-menu"作为唯一标识
- **序列设置**: 设置菜单项的显示序列
- **全局可用**: 使菜单项在整个系统中可用

## 使用场景

### 1. 导入记录组件增强

```javascript
// 导入记录组件增强功能
const ImportRecordsEnhancer = {
    enhanceImportRecords: () => {
        // 增强的导入记录组件
        class EnhancedImportRecords extends ImportRecords {
            static props = {
                enableBatchImport: { type: Boolean, optional: true },
                enableTemplateImport: { type: Boolean, optional: true },
                enableQuickImport: { type: Boolean, optional: true },
                enableImportHistory: { type: Boolean, optional: true },
                customImportTypes: { type: Array, optional: true },
                enableImportValidation: { type: Boolean, optional: true },
                enableImportPreview: { type: Boolean, optional: true }
            };
            
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    enableBatchImport: this.props.enableBatchImport || true,
                    enableTemplateImport: this.props.enableTemplateImport || true,
                    enableQuickImport: this.props.enableQuickImport || true,
                    enableImportHistory: this.props.enableImportHistory || true,
                    enableImportValidation: this.props.enableImportValidation || true,
                    enableImportPreview: this.props.enableImportPreview || true,
                    enableImportScheduling: true,
                    enableImportMonitoring: true,
                    enableImportAnalytics: true,
                    enableImportNotifications: true
                };
                
                // 增强的状态
                this.enhancedState = useState({
                    importHistory: [],
                    availableTemplates: [],
                    quickImportOptions: [],
                    importTypes: [],
                    recentImports: [],
                    importStatistics: {}
                });
                
                // 导入历史管理器
                this.importHistoryManager = new ImportHistoryManager();
                
                // 模板管理器
                this.templateManager = new ImportTemplateManager();
                
                // 快速导入管理器
                this.quickImportManager = new QuickImportManager();
                
                // 通知服务
                this.notification = useService("notification");
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 加载导入历史
                if (this.enhancedConfig.enableImportHistory) {
                    this.loadImportHistory();
                }
                
                // 加载导入模板
                if (this.enhancedConfig.enableTemplateImport) {
                    this.loadImportTemplates();
                }
                
                // 设置快速导入选项
                if (this.enhancedConfig.enableQuickImport) {
                    this.setupQuickImportOptions();
                }
                
                // 初始化导入类型
                this.initializeImportTypes();
            }
            
            // 增强的导入记录操作
            importRecords(importType = 'standard') {
                const { context, resModel } = this.env.searchModel;
                
                // 记录导入操作
                this.recordImportAction(resModel, importType);
                
                // 根据导入类型执行不同操作
                switch (importType) {
                    case 'quick':
                        this.executeQuickImport(resModel, context);
                        break;
                    case 'template':
                        this.executeTemplateImport(resModel, context);
                        break;
                    case 'batch':
                        this.executeBatchImport(resModel, context);
                        break;
                    case 'scheduled':
                        this.executeScheduledImport(resModel, context);
                        break;
                    default:
                        this.executeStandardImport(resModel, context);
                }
            }
            
            // 执行标准导入
            executeStandardImport(resModel, context) {
                this.action.doAction({
                    type: "ir.actions.client",
                    tag: "import",
                    params: { 
                        active_model: resModel, 
                        context,
                        import_type: 'standard'
                    },
                });
            }
            
            // 执行快速导入
            executeQuickImport(resModel, context) {
                if (!this.enhancedConfig.enableQuickImport) {
                    this.executeStandardImport(resModel, context);
                    return;
                }
                
                this.action.doAction({
                    type: "ir.actions.client",
                    tag: "import",
                    params: { 
                        active_model: resModel, 
                        context,
                        import_type: 'quick',
                        quick_options: this.getQuickImportOptions(resModel)
                    },
                });
            }
            
            // 执行模板导入
            executeTemplateImport(resModel, context) {
                if (!this.enhancedConfig.enableTemplateImport) {
                    this.executeStandardImport(resModel, context);
                    return;
                }
                
                const templates = this.getAvailableTemplates(resModel);
                if (templates.length === 0) {
                    this.notification.add('没有可用的导入模板', { type: 'warning' });
                    this.executeStandardImport(resModel, context);
                    return;
                }
                
                this.action.doAction({
                    type: "ir.actions.client",
                    tag: "import",
                    params: { 
                        active_model: resModel, 
                        context,
                        import_type: 'template',
                        available_templates: templates
                    },
                });
            }
            
            // 执行批量导入
            executeBatchImport(resModel, context) {
                if (!this.enhancedConfig.enableBatchImport) {
                    this.executeStandardImport(resModel, context);
                    return;
                }
                
                this.action.doAction({
                    type: "ir.actions.client",
                    tag: "import",
                    params: { 
                        active_model: resModel, 
                        context,
                        import_type: 'batch',
                        batch_options: this.getBatchImportOptions()
                    },
                });
            }
            
            // 执行计划导入
            executeScheduledImport(resModel, context) {
                if (!this.enhancedConfig.enableImportScheduling) {
                    this.executeStandardImport(resModel, context);
                    return;
                }
                
                this.action.doAction({
                    type: "ir.actions.client",
                    tag: "import_scheduler",
                    params: { 
                        active_model: resModel, 
                        context
                    },
                });
            }
            
            // 获取快速导入选项
            getQuickImportOptions(resModel) {
                return this.quickImportManager.getOptionsForModel(resModel);
            }
            
            // 获取可用模板
            getAvailableTemplates(resModel) {
                return this.templateManager.getTemplatesForModel(resModel);
            }
            
            // 获取批量导入选项
            getBatchImportOptions() {
                return {
                    maxBatchSize: 1000,
                    enableParallelProcessing: true,
                    enableProgressTracking: true,
                    enableErrorRecovery: true
                };
            }
            
            // 记录导入操作
            recordImportAction(resModel, importType) {
                const actionRecord = {
                    model: resModel,
                    importType: importType,
                    timestamp: Date.now(),
                    user: this.env.user.name
                };
                
                this.importHistoryManager.addAction(actionRecord);
                this.enhancedState.recentImports.unshift(actionRecord);
                
                // 限制最近导入记录数量
                if (this.enhancedState.recentImports.length > 10) {
                    this.enhancedState.recentImports = this.enhancedState.recentImports.slice(0, 10);
                }
            }
            
            // 加载导入历史
            loadImportHistory() {
                this.enhancedState.importHistory = this.importHistoryManager.getHistory();
            }
            
            // 加载导入模板
            loadImportTemplates() {
                this.enhancedState.availableTemplates = this.templateManager.getAllTemplates();
            }
            
            // 设置快速导入选项
            setupQuickImportOptions() {
                this.enhancedState.quickImportOptions = [
                    { id: 'csv', label: 'CSV文件', icon: 'fa-file-text-o' },
                    { id: 'excel', label: 'Excel文件', icon: 'fa-file-excel-o' },
                    { id: 'json', label: 'JSON数据', icon: 'fa-code' },
                    { id: 'xml', label: 'XML数据', icon: 'fa-file-code-o' }
                ];
            }
            
            // 初始化导入类型
            initializeImportTypes() {
                this.enhancedState.importTypes = [
                    { id: 'standard', label: '标准导入', description: '完整的导入向导' },
                    { id: 'quick', label: '快速导入', description: '简化的导入流程' },
                    { id: 'template', label: '模板导入', description: '使用预定义模板' },
                    { id: 'batch', label: '批量导入', description: '大量数据导入' },
                    { id: 'scheduled', label: '计划导入', description: '定时导入任务' }
                ];
                
                // 添加自定义导入类型
                if (this.props.customImportTypes) {
                    this.enhancedState.importTypes.push(...this.props.customImportTypes);
                }
            }
            
            // 显示导入菜单
            showImportMenu() {
                // 创建导入类型选择菜单
                const menuItems = this.enhancedState.importTypes.map(type => ({
                    label: type.label,
                    description: type.description,
                    action: () => this.importRecords(type.id)
                }));
                
                // 显示菜单（这里需要实际的菜单实现）
                console.log('显示导入菜单:', menuItems);
            }
            
            // 获取导入统计
            getImportStatistics() {
                const stats = {
                    totalImports: this.enhancedState.importHistory.length,
                    recentImports: this.enhancedState.recentImports.length,
                    availableTemplates: this.enhancedState.availableTemplates.length,
                    supportedTypes: this.enhancedState.importTypes.length
                };
                
                return stats;
            }
        }
        
        // 导入历史管理器
        class ImportHistoryManager {
            constructor() {
                this.history = [];
                this.loadFromStorage();
            }
            
            addAction(action) {
                this.history.push(action);
                this.saveToStorage();
            }
            
            getHistory() {
                return this.history;
            }
            
            getRecentHistory(count = 10) {
                return this.history.slice(-count);
            }
            
            clearHistory() {
                this.history = [];
                this.saveToStorage();
            }
            
            saveToStorage() {
                try {
                    localStorage.setItem('import_history', JSON.stringify(this.history));
                } catch (error) {
                    console.error('保存导入历史失败:', error);
                }
            }
            
            loadFromStorage() {
                try {
                    const stored = localStorage.getItem('import_history');
                    if (stored) {
                        this.history = JSON.parse(stored);
                    }
                } catch (error) {
                    console.error('加载导入历史失败:', error);
                }
            }
        }
        
        // 导入模板管理器
        class ImportTemplateManager {
            constructor() {
                this.templates = [];
            }
            
            getAllTemplates() {
                return this.templates;
            }
            
            getTemplatesForModel(model) {
                return this.templates.filter(template => template.model === model);
            }
            
            addTemplate(template) {
                this.templates.push(template);
            }
            
            removeTemplate(id) {
                this.templates = this.templates.filter(template => template.id !== id);
            }
        }
        
        // 快速导入管理器
        class QuickImportManager {
            constructor() {
                this.modelOptions = new Map();
            }
            
            getOptionsForModel(model) {
                return this.modelOptions.get(model) || this.getDefaultOptions();
            }
            
            setOptionsForModel(model, options) {
                this.modelOptions.set(model, options);
            }
            
            getDefaultOptions() {
                return {
                    autoDetectHeaders: true,
                    skipEmptyRows: true,
                    batchSize: 100,
                    enableValidation: true
                };
            }
        }
        
        // 增强的菜单项配置
        const enhancedImportRecordsItem = {
            Component: EnhancedImportRecords,
            groupNumber: STATIC_ACTIONS_GROUP_NUMBER,
            isDisplayed: ({ config, isSmall }) =>
                !isSmall &&
                config.actionType === "ir.actions.act_window" &&
                ["kanban", "list", "form"].includes(config.viewType) &&
                exprToBoolean(config.viewArch.getAttribute("import"), true) &&
                exprToBoolean(config.viewArch.getAttribute("create"), true),
        };
        
        // 导出增强的组件
        __exports.EnhancedImportRecords = EnhancedImportRecords;
        __exports.enhancedImportRecordsItem = enhancedImportRecordsItem;
    }
};

// 应用导入记录组件增强
ImportRecordsEnhancer.enhanceImportRecords();
```

## 技术特点

### 1. 菜单集成
- 无缝集成到齿轮菜单
- 智能的显示条件控制
- 标准的菜单项配置

### 2. 权限控制
- 基于视图配置的权限检查
- 动态的显示条件判断
- 安全的操作执行

### 3. 操作触发
- 简洁的操作触发机制
- 标准的客户端操作
- 上下文信息传递

### 4. 条件显示
- 复杂的显示逻辑
- 多重条件判断
- 响应式的菜单显示

## 设计模式

### 1. 命令模式 (Command Pattern)
- 封装导入操作为命令
- 统一的操作执行接口

### 2. 注册表模式 (Registry Pattern)
- 菜单项的注册和管理
- 全局可访问的菜单系统

### 3. 策略模式 (Strategy Pattern)
- 不同的显示条件策略
- 可配置的菜单行为

## 注意事项

1. **权限检查**: 确保用户有导入权限才显示菜单
2. **视图兼容**: 仅在支持导入的视图中显示
3. **上下文传递**: 正确传递模型和上下文信息
4. **用户体验**: 提供清晰的菜单标识和操作反馈

## 扩展建议

1. **多种导入类型**: 支持不同类型的导入操作
2. **快速导入**: 提供简化的快速导入选项
3. **模板导入**: 支持预定义的导入模板
4. **批量导入**: 支持大量数据的批量导入
5. **导入历史**: 记录和管理导入操作历史

该导入记录组件为基础导入系统提供了重要的菜单入口功能，是数据导入系统用户访问的核心入口组件。

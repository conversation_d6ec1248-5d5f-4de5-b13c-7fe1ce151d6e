/***********************************************************************
*  Filepath: /base_import/static/src/import_records/import_records.js  *
*  Lines: 58                                                           *
***********************************************************************/
odoo.define('@base_import/import_records/import_records', ['@odoo/owl', '@web/core/dropdown/dropdown_item', '@web/core/registry', '@web/core/utils/hooks', '@web/core/utils/strings', '@web/search/action_menus/action_menus'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { Component } = require("@odoo/owl");
const { DropdownItem } = require("@web/core/dropdown/dropdown_item");
const { registry } = require("@web/core/registry");
const { useService } = require("@web/core/utils/hooks");
const { exprToBoolean } = require("@web/core/utils/strings");
const { STATIC_ACTIONS_GROUP_NUMBER } = require("@web/search/action_menus/action_menus");

const cogMenuRegistry = registry.category("cogMenu");

/**
 * 'Import records' menu
 *
 * This component is used to import the records for particular model.
 * @extends Component
 */
const ImportRecords = __exports.ImportRecords = class ImportRecords extends Component {
    static template = "base_import.ImportRecords";
    static components = { DropdownItem };
    static props = {};

    setup() {
        this.action = useService("action");
    }

    //---------------------------------------------------------------------
    // Protected
    //---------------------------------------------------------------------

    importRecords() {
        const { context, resModel } = this.env.searchModel;
        this.action.doAction({
            type: "ir.actions.client",
            tag: "import",
            params: { active_model: resModel, context },
        });
    }
}

const importRecordsItem = __exports.importRecordsItem = {
    Component: ImportRecords,
    groupNumber: STATIC_ACTIONS_GROUP_NUMBER,
    isDisplayed: ({ config, isSmall }) =>
        !isSmall &&
        config.actionType === "ir.actions.act_window" &&
        ["kanban", "list"].includes(config.viewType) &&
        exprToBoolean(config.viewArch.getAttribute("import"), true) &&
        exprToBoolean(config.viewArch.getAttribute("create"), true),
};

cogMenuRegistry.add("import-menu", importRecordsItem, { sequence: 1 });

return __exports;
});
# Import Model - 导入模型

## 概述

`import_model.js` 是 Odoo 基础导入模块的核心业务逻辑模型，专门用于处理CSV/Excel文件的导入映射和预览数据更新。该组件基于Web框架的多种工具和OWL框架，集成了文件解析、字段映射、数据验证、错误处理等核心功能，为基础导入系统提供了完整的数据导入业务逻辑支持，是数据导入系统的核心引擎。

## 文件信息
- **路径**: `/base_import/static/src/import_model.js`
- **行数**: 867
- **模块**: `@base_import/import_model`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'        // 国际化翻译
'@web/core/registry'                // 注册表
'@web/core/utils/files'             // 文件工具
'@web/core/utils/hooks'             // 钩子工具
'@web/core/utils/objects'           // 对象工具
'@web/core/utils/arrays'            // 数组工具
'@web/core/utils/functions'         // 函数工具
'@web/session'                      // 会话
'@odoo/owl'                         // OWL框架
'@base_import/import_block_ui'      // 导入阻塞界面
'@base_import/binary_file_manager'  // 二进制文件管理器
```

## 核心功能

### 1. 格式转换工具

```javascript
const strftimeFormatTable = {
    d: "w", DD: "d", ddd: "a", dddd: "A", DDDD: "j",
    ww: "U", WW: "W", mm: "M", MM: "m", MMM: "b", MMMM: "B",
    YYYY: "Y", YY: "y", ss: "S", hh: "h", HH: "H", A: "p",
};

const humanToStrftimeFormat = memoize(function humanToStrftimeFormat(value) {
    const regex = /(dddd|ddd|dd|d|mmmm|mmm|mm|ww|yyyy|yy|hh|ss|a)/gi;
    return value.replace(regex, (value) => {
        if (strftimeFormatTable[value]) {
            return "%" + strftimeFormatTable[value];
        }
        return "%" + (strftimeFormatTable[value.toLowerCase()] || strftimeFormatTable[value.toUpperCase()]);
    });
});
```

**格式转换功能**:
- **日期格式映射**: 人类可读格式到Python strftime格式的转换
- **记忆化缓存**: 使用memoize优化重复转换的性能
- **双向转换**: 支持人类格式和strftime格式的相互转换
- **正则匹配**: 使用正则表达式进行格式模式匹配

### 2. BaseImportModel 类

```javascript
const BaseImportModel = class BaseImportModel {
    constructor({ env, resModel, context, orm }) {
        this.id = 1;
        this.env = env;
        this.orm = orm;
        this.handleInterruption = false;
        this.resModel = resModel;
        this.context = context || {};
        
        this.fields = [];
        this.columns = [];
        this.importMessages = [];
        this._importOptions = {};
        this.importTemplates = [];
        
        this.formattingOptionsValues = this._getCSVFormattingOptions();
        this.importOptionsValues = { /* 导入选项配置 */ };
        this.binaryFilesParams = { /* 二进制文件参数 */ };
    }
}
```

**模型构造功能**:
- **环境配置**: 设置环境、模型和上下文
- **状态管理**: 初始化各种状态和配置
- **选项配置**: 设置导入和格式化选项
- **文件处理**: 配置二进制文件处理参数

### 3. 导入执行

```javascript
async executeImport(isTest = false, totalSteps, importProgress) {
    this.handleInterruption = false;
    this._updateComments();
    this.importMessages = [];
    
    const importRes = { ids: [], hasError: false };
    const startRow = this.importOptions.skip;
    
    for (let i = 1; i <= totalSteps; i++) {
        if (this.handleInterruption) {
            if (importRes.hasError || isTest) {
                importRes.nextrow = startRow;
                this.setOption("skip", startRow);
            }
            break;
        }
        
        const error = await this._executeImportStep(isTest, importRes);
        if (error) {
            const errorData = error.data || {};
            const message = errorData.arguments && (errorData.arguments[1] || errorData.arguments[0])
                || _t("An unknown issue occurred during import");
            
            if (error.message) {
                this._addMessage("danger", [error.message, message]);
            } else {
                this._addMessage("danger", [message]);
            }
            break;
        }
        
        if (importProgress) {
            importProgress.step = i;
            importProgress.value = Math.round((100 * (i - 1)) / totalSteps);
        }
    }
    
    return importRes;
}
```

**导入执行功能**:
- **步骤控制**: 分步执行导入过程
- **中断处理**: 支持用户中断导入操作
- **进度跟踪**: 实时更新导入进度
- **错误处理**: 捕获和处理导入过程中的错误
- **消息管理**: 管理导入过程中的各种消息

### 4. 数据更新

```javascript
async updateData(fileChanged = false) {
    if (fileChanged) {
        this.importOptionsValues.sheet.value = "";
    }
    this.importMessages = [];
    
    const res = await this.orm.call("base_import.import", "parse_preview", [this.id], {
        options: this.importOptions,
        context: this.context,
    });
    
    if (!res.error) {
        res.options.date_format = strftimeToHumanFormat(res.options.date_format);
        res.options.datetime_format = strftimeToHumanFormat(res.options.datetime_format);
        this._onLoadSuccess(res);
    } else {
        this._onLoadError();
    }
}
```

**数据更新功能**:
- **文件变更处理**: 处理文件变更时的状态重置
- **预览解析**: 调用后端解析预览数据
- **格式转换**: 转换日期格式为人类可读格式
- **成功处理**: 处理解析成功的结果
- **错误处理**: 处理解析失败的情况

### 5. 字段映射

```javascript
setColumnField(column, fieldInfo) {
    column.fieldInfo = fieldInfo;
    this._updateComments(column);
}

_getFields(res, index) {
    const advanced = this.importOptionsValues.advanced.value;
    const fields = { basic: [], suggested: [], additional: [] };
    
    const sortSingleField = (field, ancestors, collection, types) => {
        // 字段排序和分类逻辑
        if (!collection) {
            if (field.name === "id") {
                collection = fields.basic;
            } else if (isRegular(field.fields)) {
                collection = hasType(types, field) ? fields.suggested : fields.additional;
            }
        }
        
        if (advanced) {
            for (const subfield of field.fields) {
                sortSingleField(subfield, [...ancestors], collection, types);
            }
        }
    };
    
    for (const field of this.fields) {
        if (!field.isRelation) {
            if (advanced) {
                sortSingleField(field, [], undefined, ["all"]);
            } else {
                const acceptedTypes = res.header_types[index];
                sortSingleField(field, [], undefined, acceptedTypes);
            }
        }
    }
    
    return fields;
}
```

**字段映射功能**:
- **列字段设置**: 为列设置对应的字段信息
- **字段分类**: 将字段分为基础、建议和附加类别
- **高级模式**: 支持高级模式的字段展示
- **类型匹配**: 根据数据类型匹配合适的字段
- **关系处理**: 处理关系字段的特殊逻辑

### 6. 错误处理

```javascript
_handleImportErrors(messages, name) {
    if (messages[0].not_matching_error) {
        this._addMessage(messages[0].type, [messages[0].message]);
        return true;
    }
    
    const sortedMessages = this._groupErrorsByField(messages);
    if (sortedMessages[0]) {
        this._addMessage(sortedMessages[0].type, [sortedMessages[0].message]);
        delete sortedMessages[0];
    }
    
    for (const [fieldId, errors] of Object.entries(sortedMessages)) {
        const column = this.columns.find((col) => col.fieldInfo && col.fieldInfo.fieldPath === fieldId);
        if (column) {
            column.resultNames = name;
            column.errors = errors;
        } else {
            for (const error of errors) {
                if (error.record !== undefined) {
                    this._addMessage("danger", [
                        error.rows.from === error.rows.to
                            ? _t('Error at row %(row)s: "%(error)s"', {
                                row: error.rows.from,
                                error: error.message,
                            })
                            : _t('Error at rows %(from)s-%(to)s: "%(error)s"', {
                                from: error.rows.from,
                                to: error.rows.to,
                                error: error.message,
                            }),
                    ]);
                }
            }
        }
    }
    
    return false;
}
```

**错误处理功能**:
- **错误分类**: 按字段对错误进行分组
- **消息添加**: 添加不同类型的错误消息
- **行号定位**: 精确定位错误发生的行号
- **列错误**: 为特定列设置错误信息
- **记录错误**: 处理特定记录的错误

### 7. 界面阻塞

```javascript
block(message, blockComponent) {
    mainComponentRegistry.add("ImportBlockUI", {
        Component: ImportBlockUI,
        props: { message, blockComponent },
    });
}

unblock() {
    mainComponentRegistry.remove("ImportBlockUI");
}
```

**界面阻塞功能**:
- **阻塞显示**: 在导入过程中显示阻塞界面
- **消息传递**: 传递阻塞消息给用户
- **组件注册**: 动态注册和移除阻塞组件
- **用户体验**: 防止用户在导入过程中进行其他操作

## 使用场景

### 1. 基础导入模型增强

```javascript
// 基础导入模型增强功能
const BaseImportModelEnhancer = {
    enhanceBaseImportModel: () => {
        // 增强的基础导入模型
        class EnhancedBaseImportModel extends BaseImportModel {
            constructor(params) {
                super(params);
                
                // 增强的配置选项
                this.enhancedConfig = {
                    enableAdvancedValidation: true,
                    enableAutoMapping: true,
                    enableDataPreview: true,
                    enableProgressTracking: true,
                    enableErrorRecovery: true,
                    enableBatchProcessing: true,
                    enableCaching: true,
                    enableAnalytics: true,
                    maxPreviewRows: 100,
                    batchSize: 1000,
                    retryAttempts: 3,
                    retryDelay: 1000,
                    cacheTimeout: 300000, // 5分钟
                    enableRealTimeValidation: true,
                    enableSmartSuggestions: true
                };
                
                // 导入统计
                this.importStatistics = {
                    totalRecords: 0,
                    processedRecords: 0,
                    successfulRecords: 0,
                    failedRecords: 0,
                    skippedRecords: 0,
                    startTime: null,
                    endTime: null,
                    errors: [],
                    warnings: []
                };
                
                // 字段映射缓存
                this.fieldMappingCache = new Map();
                
                // 验证规则
                this.validationRules = new Map();
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 设置自动映射
                if (this.enhancedConfig.enableAutoMapping) {
                    this.setupAutoMapping();
                }
                
                // 设置高级验证
                if (this.enhancedConfig.enableAdvancedValidation) {
                    this.setupAdvancedValidation();
                }
                
                // 设置实时验证
                if (this.enhancedConfig.enableRealTimeValidation) {
                    this.setupRealTimeValidation();
                }
                
                // 设置智能建议
                if (this.enhancedConfig.enableSmartSuggestions) {
                    this.setupSmartSuggestions();
                }
            }
            
            // 增强的导入执行
            async executeImport(isTest = false, totalSteps, importProgress) {
                try {
                    // 开始统计
                    this.importStatistics.startTime = Date.now();
                    this.importStatistics.totalRecords = totalSteps;
                    
                    // 预处理验证
                    if (this.enhancedConfig.enableAdvancedValidation) {
                        const validationResult = await this.preValidateData();
                        if (!validationResult.isValid) {
                            throw new Error(`预验证失败: ${validationResult.errors.join(', ')}`);
                        }
                    }
                    
                    // 执行原有导入逻辑
                    const result = await super.executeImport(isTest, totalSteps, importProgress);
                    
                    // 后处理
                    await this.postProcessImport(result, isTest);
                    
                    // 结束统计
                    this.importStatistics.endTime = Date.now();
                    
                    return result;
                    
                } catch (error) {
                    this.importStatistics.endTime = Date.now();
                    this.importStatistics.errors.push({
                        type: 'execution_error',
                        message: error.message,
                        timestamp: Date.now()
                    });
                    throw error;
                }
            }
            
            // 预验证数据
            async preValidateData() {
                const result = { isValid: true, errors: [], warnings: [] };
                
                try {
                    // 验证文件格式
                    const formatValidation = await this.validateFileFormat();
                    if (!formatValidation.isValid) {
                        result.isValid = false;
                        result.errors.push(...formatValidation.errors);
                    }
                    
                    // 验证字段映射
                    const mappingValidation = await this.validateFieldMapping();
                    if (!mappingValidation.isValid) {
                        result.isValid = false;
                        result.errors.push(...mappingValidation.errors);
                    }
                    
                    // 验证数据类型
                    const typeValidation = await this.validateDataTypes();
                    if (!typeValidation.isValid) {
                        result.warnings.push(...typeValidation.warnings);
                    }
                    
                } catch (error) {
                    result.isValid = false;
                    result.errors.push(`验证过程中发生错误: ${error.message}`);
                }
                
                return result;
            }
            
            // 验证文件格式
            async validateFileFormat() {
                const result = { isValid: true, errors: [] };
                
                // 检查文件是否为空
                if (!this.columns || this.columns.length === 0) {
                    result.isValid = false;
                    result.errors.push('文件为空或无法解析');
                }
                
                // 检查列数量
                if (this.columns.length > 1000) {
                    result.isValid = false;
                    result.errors.push('文件列数过多，超过1000列限制');
                }
                
                return result;
            }
            
            // 验证字段映射
            async validateFieldMapping() {
                const result = { isValid: true, errors: [] };
                
                // 检查必填字段映射
                const requiredFields = this.fields.filter(field => field.required);
                const mappedFields = this.columns
                    .filter(col => col.fieldInfo)
                    .map(col => col.fieldInfo.name);
                
                for (const field of requiredFields) {
                    if (!mappedFields.includes(field.name)) {
                        result.isValid = false;
                        result.errors.push(`必填字段 "${field.string}" 未映射`);
                    }
                }
                
                return result;
            }
            
            // 验证数据类型
            async validateDataTypes() {
                const result = { isValid: true, warnings: [] };
                
                for (const column of this.columns) {
                    if (!column.fieldInfo) continue;
                    
                    const typeValidation = await this.validateColumnDataType(column);
                    if (!typeValidation.isValid) {
                        result.warnings.push(...typeValidation.warnings);
                    }
                }
                
                return result;
            }
            
            // 验证列数据类型
            async validateColumnDataType(column) {
                const result = { isValid: true, warnings: [] };
                
                const fieldType = column.fieldInfo.type;
                const sampleData = column.preview.slice(0, 10); // 取前10行样本
                
                for (const [index, value] of sampleData.entries()) {
                    if (!value) continue; // 跳过空值
                    
                    const validation = this.validateValueType(value, fieldType);
                    if (!validation.isValid) {
                        result.warnings.push(
                            `列 "${column.name}" 第 ${index + 1} 行数据类型不匹配: ${validation.error}`
                        );
                    }
                }
                
                return result;
            }
            
            // 验证值类型
            validateValueType(value, fieldType) {
                const result = { isValid: true, error: null };
                
                try {
                    switch (fieldType) {
                        case 'integer':
                            if (!/^\d+$/.test(value.toString().trim())) {
                                result.isValid = false;
                                result.error = '不是有效的整数';
                            }
                            break;
                        case 'float':
                            if (isNaN(parseFloat(value))) {
                                result.isValid = false;
                                result.error = '不是有效的浮点数';
                            }
                            break;
                        case 'boolean':
                            const boolValues = ['true', 'false', '1', '0', 'yes', 'no'];
                            if (!boolValues.includes(value.toString().toLowerCase())) {
                                result.isValid = false;
                                result.error = '不是有效的布尔值';
                            }
                            break;
                        case 'date':
                            if (isNaN(Date.parse(value))) {
                                result.isValid = false;
                                result.error = '不是有效的日期格式';
                            }
                            break;
                        case 'datetime':
                            if (isNaN(Date.parse(value))) {
                                result.isValid = false;
                                result.error = '不是有效的日期时间格式';
                            }
                            break;
                    }
                } catch (error) {
                    result.isValid = false;
                    result.error = `验证过程中发生错误: ${error.message}`;
                }
                
                return result;
            }
            
            // 后处理导入
            async postProcessImport(result, isTest) {
                try {
                    // 更新统计信息
                    this.updateImportStatistics(result);
                    
                    // 发送分析数据
                    if (this.enhancedConfig.enableAnalytics) {
                        await this.sendAnalyticsData();
                    }
                    
                    // 清理缓存
                    if (this.enhancedConfig.enableCaching) {
                        this.cleanupCache();
                    }
                    
                } catch (error) {
                    console.error('后处理失败:', error);
                }
            }
            
            // 更新导入统计
            updateImportStatistics(result) {
                this.importStatistics.processedRecords = result.ids ? result.ids.length : 0;
                this.importStatistics.successfulRecords = result.hasError ? 0 : this.importStatistics.processedRecords;
                this.importStatistics.failedRecords = result.hasError ? this.importStatistics.processedRecords : 0;
            }
            
            // 设置自动映射
            setupAutoMapping() {
                // 实现自动字段映射逻辑
                console.log('设置自动映射');
            }
            
            // 设置高级验证
            setupAdvancedValidation() {
                // 实现高级验证逻辑
                console.log('设置高级验证');
            }
            
            // 设置实时验证
            setupRealTimeValidation() {
                // 实现实时验证逻辑
                console.log('设置实时验证');
            }
            
            // 设置智能建议
            setupSmartSuggestions() {
                // 实现智能建议逻辑
                console.log('设置智能建议');
            }
            
            // 发送分析数据
            async sendAnalyticsData() {
                try {
                    const analyticsData = {
                        statistics: this.importStatistics,
                        config: this.enhancedConfig,
                        timestamp: Date.now()
                    };
                    
                    await this.orm.call('base_import.analytics', 'record_import', [], {
                        data: analyticsData
                    });
                } catch (error) {
                    console.error('发送分析数据失败:', error);
                }
            }
            
            // 清理缓存
            cleanupCache() {
                this.fieldMappingCache.clear();
                this.validationRules.clear();
            }
            
            // 获取导入统计
            getImportStatistics() {
                const duration = this.importStatistics.endTime - this.importStatistics.startTime;
                return {
                    ...this.importStatistics,
                    duration: duration,
                    successRate: this.importStatistics.totalRecords > 0 ? 
                        (this.importStatistics.successfulRecords / this.importStatistics.totalRecords) * 100 : 0,
                    averageProcessingTime: this.importStatistics.processedRecords > 0 ? 
                        duration / this.importStatistics.processedRecords : 0
                };
            }
        }
        
        // 导出增强的模型
        __exports.EnhancedBaseImportModel = EnhancedBaseImportModel;
    }
};

// 应用基础导入模型增强
BaseImportModelEnhancer.enhanceBaseImportModel();
```

## 技术特点

### 1. 业务逻辑集中
- 集中管理导入的核心业务逻辑
- 统一的数据处理和验证流程
- 完整的导入生命周期管理

### 2. 错误处理机制
- 完善的错误分类和处理
- 详细的错误信息和定位
- 优雅的错误恢复机制

### 3. 性能优化
- 记忆化缓存优化重复计算
- 批量处理减少网络请求
- 智能的数据预览和分页

### 4. 可扩展性
- 模块化的功能设计
- 可配置的选项系统
- 灵活的字段映射机制

## 设计模式

### 1. 模型模式 (Model Pattern)
- 封装业务逻辑和数据管理
- 提供统一的数据接口

### 2. 策略模式 (Strategy Pattern)
- 不同的导入策略和验证规则
- 可配置的处理逻辑

### 3. 观察者模式 (Observer Pattern)
- 进度更新和状态通知
- 事件驱动的处理流程

## 注意事项

1. **内存管理**: 注意大文件导入时的内存使用
2. **性能优化**: 合理设置批量大小和处理间隔
3. **错误处理**: 提供详细的错误信息和恢复建议
4. **用户体验**: 保持界面响应和进度反馈

## 扩展建议

1. **智能映射**: 实现基于AI的智能字段映射
2. **数据验证**: 增强数据验证规则和自定义验证
3. **性能监控**: 添加详细的性能监控和分析
4. **模板管理**: 实现导入模板的保存和复用
5. **并行处理**: 支持多线程并行数据处理

该导入模型为基础导入系统提供了重要的核心业务逻辑，是数据导入功能的核心引擎和控制中心。

/*********************************************************
*  Filepath: /base_import/static/src/import_block_ui.js  *
*  Lines: 17                                             *
*********************************************************/
odoo.define('@base_import/import_block_ui', ['@odoo/owl'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { Component } = require("@odoo/owl");

const ImportBlockUI = __exports.ImportBlockUI = class ImportBlockUI extends Component {
    static props = {
        message: { type: String, optional: true },
        blockComponent: { type: Object, optional: true },
    };
    static template = "base_import.BlockUI";
}

return __exports;
});
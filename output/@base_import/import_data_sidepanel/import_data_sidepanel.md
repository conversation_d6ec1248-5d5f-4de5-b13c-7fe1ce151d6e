# Import Data Sidepanel - 导入数据侧边栏组件

## 概述

`import_data_sidepanel.js` 是 Odoo 基础导入模块的数据侧边栏组件，专门用于提供导入配置选项和参数设置界面。该组件基于OWL框架和Web核心组件，集成了文件信息显示、格式化选项、导入参数配置等核心功能，为基础导入系统提供了完整的侧边栏配置支持，是数据导入系统配置管理的重要组件。

## 文件信息
- **路径**: `/base_import/static/src/import_data_sidepanel/import_data_sidepanel.js`
- **行数**: 62
- **模块**: `@base_import/import_data_sidepanel/import_data_sidepanel`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                                     // OWL框架
'@web/core/checkbox/checkbox'                                   // 复选框组件
'@web/core/l10n/translation'                                    // 国际化翻译
'@web/views/widgets/documentation_link/documentation_link'     // 文档链接组件
```

## 核心功能

### 1. ImportDataSidepanel 组件

```javascript
const ImportDataSidepanel = class ImportDataSidepanel extends Component {
    static template = "ImportDataSidepanel";
    static components = { CheckBox, DocumentationLink };
    static props = {
        filename: { type: String },
        formattingOptions: { type: Object, optional: true },
        options: { type: Object },
        importTemplates: { type: Array, optional: true },
        isBatched: { type: Boolean, optional: true },
        onOptionChanged: { type: Function },
        onReload: { type: Function },
        hasBinaryFields: { type: Boolean },
        binaryFilesParams: { type: Object },
        onBinaryFilesParamsChanged: { type: Function },
    };
}
```

**组件特性**:
- **继承扩展**: 继承OWL Component的所有功能
- **子组件集成**: 集成复选框和文档链接组件
- **丰富属性**: 支持文件名、选项、模板、二进制文件等多种属性
- **模板绑定**: 使用专门的侧边栏模板

### 2. 文件信息处理

```javascript
get fileName() {
    return this.props.filename.split(".")[0];
}

get fileExtension() {
    return "." + this.props.filename.split(".").pop();
}
```

**文件信息功能**:
- **文件名提取**: 从完整文件名中提取文件名部分
- **扩展名提取**: 从完整文件名中提取文件扩展名
- **字符串处理**: 使用字符串分割方法处理文件名
- **动态计算**: 基于属性动态计算文件信息

### 3. 选项值管理

```javascript
getOptionValue(name) {
    if (name === "skip") {
        return (this.props.options.skip + 1).toString();
    }
    return this.props.options[name].toString();
}

setOptionValue(name, value) {
    this.props.onOptionChanged(name, isNaN(parseFloat(value)) ? value : Number(value));
}
```

**选项管理功能**:
- **值获取**: 获取指定选项的值
- **特殊处理**: 对skip选项进行特殊的+1处理
- **值设置**: 设置选项值并进行类型转换
- **类型转换**: 自动识别数值类型并进行转换
- **事件委托**: 将选项变更委托给父组件处理

### 4. 限制变更处理

```javascript
onLimitChange(ev) {
    this.props.onOptionChanged("skip", ev.target.value ? ev.target.value - 1 : 0);
}
```

**限制变更功能**:
- **事件处理**: 处理限制输入框的变更事件
- **值转换**: 将用户输入的行号转换为skip值（-1处理）
- **默认值**: 当输入为空时设置默认值0
- **即时更新**: 实时更新skip选项值

### 5. 二进制文件标签

```javascript
get binaryFilesLabel() {
    const files = this.props.binaryFilesParams.binaryFiles.value;
    const number = Object.keys(files).length;
    if (number > 0) {
        return _t("%(number)s file(s) selected", { number });
    }
    return _t("No file selected");
}
```

**二进制文件标签功能**:
- **文件计数**: 统计已选择的二进制文件数量
- **动态标签**: 根据文件数量生成相应的标签文本
- **国际化**: 使用翻译函数支持多语言
- **条件显示**: 根据是否有文件选择显示不同文本

## 使用场景

### 1. 导入数据侧边栏组件增强

```javascript
// 导入数据侧边栏组件增强功能
const ImportDataSidepanelEnhancer = {
    enhanceImportDataSidepanel: () => {
        // 增强的导入数据侧边栏组件
        class EnhancedImportDataSidepanel extends ImportDataSidepanel {
            static props = {
                ...ImportDataSidepanel.props,
                enableAdvancedOptions: { type: Boolean, optional: true },
                enableOptionPresets: { type: Boolean, optional: true },
                enableOptionValidation: { type: Boolean, optional: true },
                enableOptionHistory: { type: Boolean, optional: true },
                enableQuickActions: { type: Boolean, optional: true },
                enableOptionSearch: { type: Boolean, optional: true },
                enableOptionGroups: { type: Boolean, optional: true },
                customOptionGroups: { type: Array, optional: true }
            };
            
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    enableAdvancedOptions: this.props.enableAdvancedOptions || true,
                    enableOptionPresets: this.props.enableOptionPresets || true,
                    enableOptionValidation: this.props.enableOptionValidation || true,
                    enableOptionHistory: this.props.enableOptionHistory || true,
                    enableQuickActions: this.props.enableQuickActions || true,
                    enableOptionSearch: this.props.enableOptionSearch || true,
                    enableOptionGroups: this.props.enableOptionGroups || true,
                    enableOptionExport: true,
                    enableOptionImport: true,
                    enableOptionSync: true,
                    enableOptionBackup: true
                };
                
                // 增强的状态
                this.enhancedState = useState({
                    advancedMode: false,
                    searchTerm: '',
                    selectedPreset: null,
                    optionHistory: [],
                    validationResults: {},
                    collapsedGroups: new Set(),
                    quickActions: [],
                    customOptions: {},
                    optionPresets: [],
                    isValidating: false
                });
                
                // 选项预设管理器
                this.presetManager = new OptionPresetManager();
                
                // 选项验证器
                this.optionValidator = new OptionValidator();
                
                // 选项历史管理器
                this.optionHistoryManager = new OptionHistoryManager();
                
                // 快速操作管理器
                this.quickActionManager = new QuickActionManager();
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 加载选项预设
                if (this.enhancedConfig.enableOptionPresets) {
                    this.loadOptionPresets();
                }
                
                // 加载选项历史
                if (this.enhancedConfig.enableOptionHistory) {
                    this.loadOptionHistory();
                }
                
                // 设置快速操作
                if (this.enhancedConfig.enableQuickActions) {
                    this.setupQuickActions();
                }
                
                // 初始化选项分组
                if (this.enhancedConfig.enableOptionGroups) {
                    this.initializeOptionGroups();
                }
            }
            
            // 增强的选项值设置
            setOptionValue(name, value) {
                // 执行验证
                if (this.enhancedConfig.enableOptionValidation) {
                    const validationResult = this.validateOption(name, value);
                    if (!validationResult.isValid) {
                        this.showValidationError(name, validationResult.message);
                        return;
                    }
                }
                
                // 记录到历史
                if (this.enhancedConfig.enableOptionHistory) {
                    this.addToOptionHistory(name, value);
                }
                
                // 调用原有设置逻辑
                super.setOptionValue(name, value);
                
                // 更新自定义选项
                this.enhancedState.customOptions[name] = value;
                
                // 触发选项同步
                if (this.enhancedConfig.enableOptionSync) {
                    this.syncOptions();
                }
            }
            
            // 获取选项分组
            getOptionGroups() {
                const groups = [
                    {
                        name: 'file',
                        label: _t('File Options'),
                        options: ['has_headers', 'encoding', 'separator', 'quotechar']
                    },
                    {
                        name: 'import',
                        label: _t('Import Options'),
                        options: ['skip', 'limit', 'tracking_disable']
                    },
                    {
                        name: 'advanced',
                        label: _t('Advanced Options'),
                        options: ['advanced', 'keep_matches']
                    }
                ];
                
                // 添加自定义分组
                if (this.props.customOptionGroups) {
                    groups.push(...this.props.customOptionGroups);
                }
                
                return groups;
            }
            
            // 过滤选项
            getFilteredOptions() {
                if (!this.enhancedState.searchTerm) {
                    return this.props.options;
                }
                
                const searchTerm = this.enhancedState.searchTerm.toLowerCase();
                const filteredOptions = {};
                
                for (const [key, value] of Object.entries(this.props.options)) {
                    if (key.toLowerCase().includes(searchTerm) ||
                        (value.label && value.label.toLowerCase().includes(searchTerm))) {
                        filteredOptions[key] = value;
                    }
                }
                
                return filteredOptions;
            }
            
            // 验证选项
            validateOption(name, value) {
                return this.optionValidator.validate(name, value, this.props.options);
            }
            
            // 应用选项预设
            applyOptionPreset(preset) {
                try {
                    for (const [optionName, optionValue] of Object.entries(preset.options)) {
                        this.setOptionValue(optionName, optionValue);
                    }
                    
                    this.enhancedState.selectedPreset = preset.id;
                    this.showNotification('选项预设已应用', 'success');
                } catch (error) {
                    console.error('应用选项预设失败:', error);
                    this.showNotification('应用选项预设失败', 'error');
                }
            }
            
            // 保存选项预设
            saveOptionPreset(name) {
                try {
                    const preset = {
                        id: this.generatePresetId(),
                        name: name,
                        options: { ...this.enhancedState.customOptions },
                        timestamp: Date.now()
                    };
                    
                    this.presetManager.save(preset);
                    this.enhancedState.optionPresets.push(preset);
                    
                    this.showNotification('选项预设已保存', 'success');
                } catch (error) {
                    console.error('保存选项预设失败:', error);
                    this.showNotification('保存选项预设失败', 'error');
                }
            }
            
            // 执行快速操作
            executeQuickAction(action) {
                try {
                    switch (action.type) {
                        case 'reset_options':
                            this.resetAllOptions();
                            break;
                        case 'apply_defaults':
                            this.applyDefaultOptions();
                            break;
                        case 'toggle_advanced':
                            this.toggleAdvancedMode();
                            break;
                        case 'export_options':
                            this.exportOptions();
                            break;
                        case 'import_options':
                            this.importOptions();
                            break;
                        default:
                            console.warn('未知的快速操作:', action.type);
                    }
                } catch (error) {
                    console.error('执行快速操作失败:', error);
                }
            }
            
            // 重置所有选项
            resetAllOptions() {
                for (const optionName of Object.keys(this.props.options)) {
                    const defaultValue = this.getDefaultOptionValue(optionName);
                    this.setOptionValue(optionName, defaultValue);
                }
                
                this.showNotification('所有选项已重置', 'info');
            }
            
            // 应用默认选项
            applyDefaultOptions() {
                const defaultOptions = this.getDefaultOptions();
                for (const [optionName, optionValue] of Object.entries(defaultOptions)) {
                    this.setOptionValue(optionName, optionValue);
                }
                
                this.showNotification('默认选项已应用', 'info');
            }
            
            // 切换高级模式
            toggleAdvancedMode() {
                this.enhancedState.advancedMode = !this.enhancedState.advancedMode;
                this.showNotification(
                    this.enhancedState.advancedMode ? '已启用高级模式' : '已禁用高级模式',
                    'info'
                );
            }
            
            // 导出选项
            exportOptions() {
                if (!this.enhancedConfig.enableOptionExport) return;
                
                try {
                    const exportData = {
                        filename: this.props.filename,
                        options: this.enhancedState.customOptions,
                        presets: this.enhancedState.optionPresets,
                        timestamp: Date.now()
                    };
                    
                    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                        type: 'application/json'
                    });
                    
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `import_options_${this.fileName}.json`;
                    a.click();
                    
                    URL.revokeObjectURL(url);
                    this.showNotification('选项配置已导出', 'success');
                } catch (error) {
                    console.error('导出选项失败:', error);
                    this.showNotification('导出选项失败', 'error');
                }
            }
            
            // 导入选项
            async importOptions() {
                if (!this.enhancedConfig.enableOptionImport) return;
                
                try {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = '.json';
                    
                    input.onchange = async (event) => {
                        const file = event.target.files[0];
                        if (file) {
                            const text = await file.text();
                            const importData = JSON.parse(text);
                            
                            if (this.validateImportData(importData)) {
                                // 导入选项
                                for (const [optionName, optionValue] of Object.entries(importData.options)) {
                                    this.setOptionValue(optionName, optionValue);
                                }
                                
                                // 导入预设
                                if (importData.presets) {
                                    this.enhancedState.optionPresets.push(...importData.presets);
                                }
                                
                                this.showNotification('选项配置已导入', 'success');
                            } else {
                                this.showNotification('导入数据格式不正确', 'error');
                            }
                        }
                    };
                    
                    input.click();
                } catch (error) {
                    console.error('导入选项失败:', error);
                    this.showNotification('导入选项失败', 'error');
                }
            }
            
            // 搜索选项
            searchOptions(term) {
                this.enhancedState.searchTerm = term;
            }
            
            // 切换分组折叠状态
            toggleGroupCollapse(groupName) {
                if (this.enhancedState.collapsedGroups.has(groupName)) {
                    this.enhancedState.collapsedGroups.delete(groupName);
                } else {
                    this.enhancedState.collapsedGroups.add(groupName);
                }
            }
            
            // 添加到选项历史
            addToOptionHistory(name, value) {
                this.optionHistoryManager.add({
                    optionName: name,
                    optionValue: value,
                    timestamp: Date.now()
                });
                
                this.enhancedState.optionHistory = this.optionHistoryManager.getRecentHistory(10);
            }
            
            // 同步选项
            syncOptions() {
                // 实现选项同步逻辑
                console.log('同步选项');
            }
            
            // 工具方法
            getDefaultOptionValue(optionName) {
                const defaultValues = {
                    'has_headers': true,
                    'skip': 0,
                    'limit': 2000,
                    'tracking_disable': true,
                    'advanced': false,
                    'keep_matches': false
                };
                
                return defaultValues[optionName] || '';
            }
            
            getDefaultOptions() {
                return {
                    'has_headers': true,
                    'skip': 0,
                    'limit': 2000,
                    'tracking_disable': true,
                    'advanced': false,
                    'keep_matches': false
                };
            }
            
            generatePresetId() {
                return `preset_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            }
            
            validateImportData(data) {
                return data && data.options && typeof data.options === 'object';
            }
            
            showNotification(message, type) {
                console.log(`[${type}] ${message}`);
            }
            
            showValidationError(optionName, message) {
                console.error(`选项 ${optionName} 验证失败:`, message);
            }
        }
        
        // 选项预设管理器
        class OptionPresetManager {
            constructor() {
                this.presets = [];
            }
            
            save(preset) {
                this.presets.push(preset);
                this.saveToStorage();
            }
            
            load(id) {
                return this.presets.find(preset => preset.id === id);
            }
            
            delete(id) {
                this.presets = this.presets.filter(preset => preset.id !== id);
                this.saveToStorage();
            }
            
            getAll() {
                return this.presets;
            }
            
            saveToStorage() {
                try {
                    localStorage.setItem('import_option_presets', JSON.stringify(this.presets));
                } catch (error) {
                    console.error('保存选项预设失败:', error);
                }
            }
            
            loadFromStorage() {
                try {
                    const stored = localStorage.getItem('import_option_presets');
                    if (stored) {
                        this.presets = JSON.parse(stored);
                    }
                } catch (error) {
                    console.error('加载选项预设失败:', error);
                }
            }
        }
        
        // 选项验证器
        class OptionValidator {
            validate(name, value, allOptions) {
                const result = { isValid: true, message: '' };
                
                try {
                    switch (name) {
                        case 'skip':
                            if (value < 0) {
                                result.isValid = false;
                                result.message = '跳过行数不能为负数';
                            }
                            break;
                        case 'limit':
                            if (value <= 0) {
                                result.isValid = false;
                                result.message = '限制数量必须大于0';
                            }
                            break;
                        case 'encoding':
                            const validEncodings = ['utf-8', 'latin-1', 'cp1252'];
                            if (!validEncodings.includes(value)) {
                                result.isValid = false;
                                result.message = '不支持的编码格式';
                            }
                            break;
                    }
                } catch (error) {
                    result.isValid = false;
                    result.message = '验证过程中发生错误';
                }
                
                return result;
            }
        }
        
        // 选项历史管理器
        class OptionHistoryManager {
            constructor() {
                this.history = [];
            }
            
            add(entry) {
                this.history.push(entry);
                
                // 限制历史记录大小
                if (this.history.length > 100) {
                    this.history = this.history.slice(-50);
                }
            }
            
            getRecentHistory(count) {
                return this.history.slice(-count);
            }
            
            clear() {
                this.history = [];
            }
        }
        
        // 快速操作管理器
        class QuickActionManager {
            constructor() {
                this.actions = [
                    { type: 'reset_options', label: '重置所有选项', icon: 'fa-refresh' },
                    { type: 'apply_defaults', label: '应用默认选项', icon: 'fa-cog' },
                    { type: 'toggle_advanced', label: '切换高级模式', icon: 'fa-toggle-on' },
                    { type: 'export_options', label: '导出选项', icon: 'fa-download' },
                    { type: 'import_options', label: '导入选项', icon: 'fa-upload' }
                ];
            }
            
            getActions() {
                return this.actions;
            }
            
            addAction(action) {
                this.actions.push(action);
            }
            
            removeAction(type) {
                this.actions = this.actions.filter(action => action.type !== type);
            }
        }
        
        // 导出增强的组件
        __exports.EnhancedImportDataSidepanel = EnhancedImportDataSidepanel;
    }
};

// 应用导入数据侧边栏组件增强
ImportDataSidepanelEnhancer.enhanceImportDataSidepanel();
```

## 技术特点

### 1. 配置管理
- 丰富的导入选项配置
- 灵活的参数设置
- 动态的选项验证

### 2. 文件处理
- 智能的文件信息提取
- 多种文件格式支持
- 二进制文件管理

### 3. 用户界面
- 清晰的侧边栏布局
- 直观的选项组织
- 响应式的交互设计

### 4. 组件集成
- 多个子组件的集成
- 统一的事件处理
- 模块化的功能设计

## 设计模式

### 1. 组合模式 (Composite Pattern)
- 组合多个子组件提供完整功能
- 统一的组件接口

### 2. 委托模式 (Delegation Pattern)
- 事件处理的委托机制
- 职责的合理分配

### 3. 策略模式 (Strategy Pattern)
- 不同选项的处理策略
- 可配置的验证规则

## 注意事项

1. **选项验证**: 确保选项值的有效性和合理性
2. **用户体验**: 提供清晰的选项说明和帮助信息
3. **性能优化**: 优化选项更新和渲染性能
4. **数据同步**: 确保选项状态的正确同步

## 扩展建议

1. **选项预设**: 实现选项配置的预设和模板
2. **高级选项**: 添加更多高级的导入配置选项
3. **选项验证**: 增强选项值的验证和提示
4. **快速操作**: 提供常用操作的快捷方式
5. **选项搜索**: 添加选项搜索和过滤功能

该导入数据侧边栏组件为基础导入系统提供了重要的配置管理功能，是数据导入系统参数设置的核心组件。

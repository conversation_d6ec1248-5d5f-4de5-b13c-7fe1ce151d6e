# Import Data Options - 导入数据选项组件

## 概述

`import_data_options.js` 是 Odoo 基础导入模块的数据选项配置组件，专门用于为不同字段类型提供导入选项配置。该组件基于OWL框架和Web核心服务，集成了字段类型检测、选项加载、动态配置等核心功能，为基础导入系统提供了完整的导入选项配置支持，是数据导入系统字段配置的重要组件。

## 文件信息
- **路径**: `/base_import/static/src/import_data_options/import_data_options.js`
- **行数**: 84
- **模块**: `@base_import/import_data_options/import_data_options`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                    // OWL框架
'@web/core/l10n/translation'   // 国际化翻译
'@web/core/utils/hooks'        // 钩子工具
```

## 核心功能

### 1. ImportDataOptions 组件

```javascript
const ImportDataOptions = class ImportDataOptions extends Component {
    static template = "ImportDataOptions";
    static props = {
        importOptions: { type: Object, optional: true },
        fieldInfo: { type: Object },
        onOptionChanged: { type: Function },
    };
}
```

**组件特性**:
- **继承扩展**: 继承OWL Component的所有功能
- **属性配置**: 支持导入选项、字段信息和选项变更回调
- **模板绑定**: 使用专门的导入数据选项模板
- **动态配置**: 根据字段类型动态配置选项

### 2. 组件初始化

```javascript
setup() {
    this.orm = useService("orm");
    this.state = useState({
        options: [],
    });
    this.currentModel = this.props.fieldInfo.comodel_name || this.props.fieldInfo.model_name;
    onWillStart(async () => {
        this.state.options = await this.loadOptions();
    });
}
```

**初始化功能**:
- **服务集成**: 集成ORM服务进行数据操作
- **状态管理**: 初始化选项状态
- **模型配置**: 设置当前模型信息
- **异步加载**: 在组件启动前异步加载选项

### 3. 可见性控制

```javascript
get isVisible() {
    return ["many2one", "many2many", "selection", "boolean"].includes(
        this.props.fieldInfo.type
    );
}
```

**可见性功能**:
- **类型检查**: 检查字段类型是否需要显示选项
- **支持类型**: 支持many2one、many2many、selection、boolean类型
- **条件显示**: 仅对特定字段类型显示选项配置
- **动态控制**: 根据字段类型动态控制组件可见性

### 4. 选项加载

```javascript
async loadOptions() {
    const options = [["prevent", _t("Prevent import")]];
    if (this.props.fieldInfo.type === "boolean") {
        options.push(["false", _t("Set to: False")]);
        options.push(["true", _t("Set to: True")]);
        !this.props.fieldInfo.required &&
            options.push(["import_skip_records", _t("Skip record")]);
    }
    if (["many2one", "many2many", "selection"].includes(this.props.fieldInfo.type)) {
        if (!this.props.fieldInfo.required) {
            options.push(["import_set_empty_fields", _t("Set value as empty")]);
            options.push(["import_skip_records", _t("Skip record")]);
        }
        if (this.props.fieldInfo.type === "selection") {
            return options;
        }
        if (this.currentModel) {
            options.push(["create", _t("Create new record")]);
        }
    }
    return options;
}
```

**选项加载功能**:
- **基础选项**: 提供阻止导入的基础选项
- **布尔字段**: 为布尔字段提供True/False设置选项
- **关系字段**: 为关系字段提供空值和跳过选项
- **创建选项**: 为关系字段提供创建新记录选项
- **条件选项**: 根据字段是否必填提供不同选项

### 5. 选项变更处理

```javascript
onOptionChanged(option, value) {
    this.props.onOptionChanged(option, value);
}
```

**选项变更功能**:
- **事件委托**: 将选项变更事件委托给父组件
- **数据同步**: 保持选项状态的同步
- **回调执行**: 执行父组件提供的回调函数
- **状态更新**: 触发相关状态的更新

## 使用场景

### 1. 导入数据选项组件增强

```javascript
// 导入数据选项组件增强功能
const ImportDataOptionsEnhancer = {
    enhanceImportDataOptions: () => {
        // 增强的导入数据选项组件
        class EnhancedImportDataOptions extends ImportDataOptions {
            static props = {
                ...ImportDataOptions.props,
                enableAdvancedOptions: { type: Boolean, optional: true },
                enableCustomValidation: { type: Boolean, optional: true },
                enableConditionalOptions: { type: Boolean, optional: true },
                enableOptionTemplates: { type: Boolean, optional: true },
                customOptions: { type: Array, optional: true },
                validationRules: { type: Array, optional: true },
                optionGroups: { type: Array, optional: true }
            };
            
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    enableAdvancedOptions: this.props.enableAdvancedOptions || true,
                    enableCustomValidation: this.props.enableCustomValidation || true,
                    enableConditionalOptions: this.props.enableConditionalOptions || true,
                    enableOptionTemplates: this.props.enableOptionTemplates || true,
                    enableOptionHistory: true,
                    enableSmartDefaults: true,
                    enableOptionValidation: true,
                    enableBulkConfiguration: true,
                    enableOptionExport: true,
                    enableOptionImport: true
                };
                
                // 增强的状态
                this.enhancedState = useState({
                    advancedMode: false,
                    optionHistory: [],
                    validationResults: {},
                    conditionalOptions: [],
                    optionTemplates: [],
                    customValidationRules: this.props.validationRules || [],
                    selectedTemplate: null,
                    isValidating: false
                });
                
                // 选项历史管理器
                this.optionHistoryManager = new OptionHistoryManager();
                
                // 验证引擎
                this.validationEngine = new OptionValidationEngine();
                
                // 模板管理器
                this.templateManager = new OptionTemplateManager();
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 加载选项历史
                if (this.enhancedConfig.enableOptionHistory) {
                    this.loadOptionHistory();
                }
                
                // 加载选项模板
                if (this.enhancedConfig.enableOptionTemplates) {
                    this.loadOptionTemplates();
                }
                
                // 设置智能默认值
                if (this.enhancedConfig.enableSmartDefaults) {
                    this.setupSmartDefaults();
                }
                
                // 设置条件选项
                if (this.enhancedConfig.enableConditionalOptions) {
                    this.setupConditionalOptions();
                }
            }
            
            // 增强的选项加载
            async loadOptions() {
                let options = await super.loadOptions();
                
                // 添加高级选项
                if (this.enhancedConfig.enableAdvancedOptions && this.enhancedState.advancedMode) {
                    options = [...options, ...this.getAdvancedOptions()];
                }
                
                // 添加自定义选项
                if (this.props.customOptions) {
                    options = [...options, ...this.props.customOptions];
                }
                
                // 添加条件选项
                if (this.enhancedConfig.enableConditionalOptions) {
                    const conditionalOptions = this.getConditionalOptions();
                    options = [...options, ...conditionalOptions];
                }
                
                // 添加历史选项
                if (this.enhancedConfig.enableOptionHistory) {
                    const historyOptions = this.getHistoryOptions();
                    if (historyOptions.length > 0) {
                        options = [...historyOptions, ...options];
                    }
                }
                
                // 验证选项
                if (this.enhancedConfig.enableOptionValidation) {
                    options = this.validateOptions(options);
                }
                
                return options;
            }
            
            // 获取高级选项
            getAdvancedOptions() {
                const fieldType = this.props.fieldInfo.type;
                const advancedOptions = [];
                
                switch (fieldType) {
                    case 'many2one':
                    case 'many2many':
                        advancedOptions.push(
                            ['update_existing', _t('Update existing records')],
                            ['merge_duplicates', _t('Merge duplicate records')],
                            ['create_with_defaults', _t('Create with default values')],
                            ['link_by_external_id', _t('Link by external ID')]
                        );
                        break;
                    case 'selection':
                        advancedOptions.push(
                            ['map_values', _t('Map values')],
                            ['case_insensitive', _t('Case insensitive matching')],
                            ['fuzzy_match', _t('Fuzzy matching')]
                        );
                        break;
                    case 'boolean':
                        advancedOptions.push(
                            ['auto_detect', _t('Auto detect boolean values')],
                            ['custom_mapping', _t('Custom value mapping')]
                        );
                        break;
                }
                
                return advancedOptions;
            }
            
            // 获取条件选项
            getConditionalOptions() {
                const conditionalOptions = [];
                
                // 基于字段值的条件选项
                if (this.hasFieldValue()) {
                    conditionalOptions.push(['conditional_import', _t('Conditional import')]);
                }
                
                // 基于其他字段的条件选项
                if (this.hasRelatedFields()) {
                    conditionalOptions.push(['dependent_field', _t('Dependent on other fields')]);
                }
                
                return conditionalOptions;
            }
            
            // 获取历史选项
            getHistoryOptions() {
                const history = this.optionHistoryManager.getHistory(this.props.fieldInfo.name);
                return history.map(item => [item.value, `${item.label} (历史)`]);
            }
            
            // 验证选项
            validateOptions(options) {
                const validatedOptions = [];
                
                for (const option of options) {
                    const [value, label] = option;
                    
                    // 执行自定义验证规则
                    const isValid = this.validateOption(value, label);
                    if (isValid) {
                        validatedOptions.push(option);
                    }
                }
                
                return validatedOptions;
            }
            
            // 验证单个选项
            validateOption(value, label) {
                for (const rule of this.enhancedState.customValidationRules) {
                    if (!this.executeValidationRule(rule, value, label)) {
                        return false;
                    }
                }
                return true;
            }
            
            // 执行验证规则
            executeValidationRule(rule, value, label) {
                try {
                    if (typeof rule.validator === 'function') {
                        return rule.validator(value, label, this.props.fieldInfo);
                    }
                    return true;
                } catch (error) {
                    console.error('验证规则执行失败:', error);
                    return false;
                }
            }
            
            // 增强的选项变更处理
            onOptionChanged(option, value) {
                // 记录到历史
                if (this.enhancedConfig.enableOptionHistory) {
                    this.addToOptionHistory(option, value);
                }
                
                // 执行验证
                if (this.enhancedConfig.enableOptionValidation) {
                    this.validateOptionChange(option, value);
                }
                
                // 调用原有处理
                super.onOptionChanged(option, value);
                
                // 更新条件选项
                if (this.enhancedConfig.enableConditionalOptions) {
                    this.updateConditionalOptions(option, value);
                }
            }
            
            // 验证选项变更
            validateOptionChange(option, value) {
                this.enhancedState.isValidating = true;
                
                try {
                    const validationResult = this.validationEngine.validate(option, value, this.props.fieldInfo);
                    this.enhancedState.validationResults[option] = validationResult;
                    
                    if (!validationResult.isValid) {
                        this.showValidationError(validationResult.message);
                    }
                } catch (error) {
                    console.error('选项验证失败:', error);
                } finally {
                    this.enhancedState.isValidating = false;
                }
            }
            
            // 添加到选项历史
            addToOptionHistory(option, value) {
                this.optionHistoryManager.add({
                    fieldName: this.props.fieldInfo.name,
                    fieldType: this.props.fieldInfo.type,
                    option: option,
                    value: value,
                    timestamp: Date.now()
                });
            }
            
            // 应用选项模板
            applyOptionTemplate(template) {
                try {
                    for (const [option, value] of Object.entries(template.options)) {
                        this.onOptionChanged(option, value);
                    }
                    
                    this.enhancedState.selectedTemplate = template.id;
                    this.showNotification('选项模板已应用', 'success');
                } catch (error) {
                    console.error('应用选项模板失败:', error);
                    this.showNotification('应用选项模板失败', 'error');
                }
            }
            
            // 保存选项模板
            saveOptionTemplate(name) {
                try {
                    const template = {
                        id: this.generateTemplateId(),
                        name: name,
                        fieldType: this.props.fieldInfo.type,
                        options: this.getCurrentOptions(),
                        timestamp: Date.now()
                    };
                    
                    this.templateManager.save(template);
                    this.enhancedState.optionTemplates.push(template);
                    
                    this.showNotification('选项模板已保存', 'success');
                } catch (error) {
                    console.error('保存选项模板失败:', error);
                    this.showNotification('保存选项模板失败', 'error');
                }
            }
            
            // 批量配置选项
            bulkConfigureOptions(configurations) {
                if (!this.enhancedConfig.enableBulkConfiguration) return;
                
                try {
                    for (const config of configurations) {
                        this.onOptionChanged(config.option, config.value);
                    }
                    
                    this.showNotification('批量配置完成', 'success');
                } catch (error) {
                    console.error('批量配置失败:', error);
                    this.showNotification('批量配置失败', 'error');
                }
            }
            
            // 导出选项配置
            exportOptions() {
                if (!this.enhancedConfig.enableOptionExport) return;
                
                try {
                    const exportData = {
                        fieldInfo: this.props.fieldInfo,
                        options: this.getCurrentOptions(),
                        timestamp: Date.now()
                    };
                    
                    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                        type: 'application/json'
                    });
                    
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `import_options_${this.props.fieldInfo.name}.json`;
                    a.click();
                    
                    URL.revokeObjectURL(url);
                    this.showNotification('选项配置已导出', 'success');
                } catch (error) {
                    console.error('导出选项配置失败:', error);
                    this.showNotification('导出选项配置失败', 'error');
                }
            }
            
            // 导入选项配置
            async importOptions(file) {
                if (!this.enhancedConfig.enableOptionImport) return;
                
                try {
                    const text = await file.text();
                    const importData = JSON.parse(text);
                    
                    // 验证导入数据
                    if (this.validateImportData(importData)) {
                        this.bulkConfigureOptions(Object.entries(importData.options).map(([option, value]) => ({
                            option, value
                        })));
                        
                        this.showNotification('选项配置已导入', 'success');
                    } else {
                        this.showNotification('导入数据格式不正确', 'error');
                    }
                } catch (error) {
                    console.error('导入选项配置失败:', error);
                    this.showNotification('导入选项配置失败', 'error');
                }
            }
            
            // 工具方法
            hasFieldValue() {
                return this.props.fieldInfo.value !== undefined && this.props.fieldInfo.value !== null;
            }
            
            hasRelatedFields() {
                return this.props.fieldInfo.related_fields && this.props.fieldInfo.related_fields.length > 0;
            }
            
            getCurrentOptions() {
                const options = {};
                // 获取当前所有选项的值
                return options;
            }
            
            generateTemplateId() {
                return `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            }
            
            validateImportData(data) {
                return data && data.fieldInfo && data.options && typeof data.options === 'object';
            }
            
            showNotification(message, type) {
                console.log(`[${type}] ${message}`);
            }
            
            showValidationError(message) {
                console.error('验证错误:', message);
            }
        }
        
        // 选项历史管理器
        class OptionHistoryManager {
            constructor() {
                this.history = new Map();
            }
            
            add(item) {
                // 添加到历史记录
            }
            
            getHistory(fieldName) {
                // 获取字段的历史记录
                return [];
            }
            
            clear() {
                // 清空历史记录
            }
        }
        
        // 选项验证引擎
        class OptionValidationEngine {
            constructor() {
                this.rules = [];
            }
            
            validate(option, value, fieldInfo) {
                // 执行验证
                return { isValid: true, message: '' };
            }
        }
        
        // 选项模板管理器
        class OptionTemplateManager {
            constructor() {
                this.templates = [];
            }
            
            save(template) {
                // 保存模板
            }
            
            load(id) {
                // 加载模板
            }
            
            delete(id) {
                // 删除模板
            }
        }
        
        // 导出增强的组件
        __exports.EnhancedImportDataOptions = EnhancedImportDataOptions;
    }
};

// 应用导入数据选项组件增强
ImportDataOptionsEnhancer.enhanceImportDataOptions();
```

## 技术特点

### 1. 动态配置
- 根据字段类型动态生成选项
- 智能的可见性控制
- 灵活的选项组合

### 2. 类型支持
- 支持多种字段类型
- 针对性的选项配置
- 条件性的选项显示

### 3. 异步加载
- 异步的选项数据加载
- 非阻塞的用户界面
- 优化的加载性能

### 4. 事件处理
- 清晰的事件委托机制
- 响应式的状态更新
- 流畅的用户交互

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- 不同字段类型的选项策略
- 可配置的选项生成

### 2. 工厂模式 (Factory Pattern)
- 动态的选项创建
- 类型驱动的选项生成

### 3. 委托模式 (Delegation Pattern)
- 事件处理的委托机制
- 职责的合理分配

## 注意事项

1. **字段类型**: 确保正确识别和处理不同的字段类型
2. **选项验证**: 验证选项的有效性和兼容性
3. **性能优化**: 优化选项加载和渲染性能
4. **用户体验**: 提供清晰的选项说明和帮助

## 扩展建议

1. **高级选项**: 添加更多高级的导入选项
2. **条件选项**: 实现基于条件的动态选项
3. **选项模板**: 支持选项配置的模板化
4. **批量配置**: 支持多字段的批量选项配置
5. **智能建议**: 基于历史数据的智能选项建议

该导入数据选项组件为基础导入系统提供了重要的字段配置功能，是数据导入系统字段选项管理的核心组件。

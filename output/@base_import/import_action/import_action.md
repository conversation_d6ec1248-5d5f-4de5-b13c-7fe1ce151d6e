# Import Action - 导入操作组件

## 概述

`import_action.js` 是 Odoo 基础导入模块的主要操作组件，专门用于提供完整的数据导入用户界面和操作流程。该组件基于OWL框架和Web核心服务，集成了文件上传、拖拽支持、进度跟踪、数据预览等核心功能，为基础导入系统提供了完整的用户交互界面，是数据导入系统的主要入口和控制中心。

## 文件信息
- **路径**: `/base_import/static/src/import_action/import_action.js`
- **行数**: 258
- **模块**: `@base_import/import_action/import_action`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'                                    // 国际化翻译
'@odoo/owl'                                                     // OWL框架
'@web/core/registry'                                            // 注册表
'@web/core/utils/files'                                         // 文件工具
'@web/core/utils/hooks'                                         // 钩子工具
'@web/core/file_input/file_input'                              // 文件输入
'@web/core/dropzone/dropzone_hook'                             // 拖拽区域钩子
'@base_import/import_model'                                     // 导入模型
'@base_import/import_data_content/import_data_content'          // 导入数据内容
'@base_import/import_data_progress/import_data_progress'        // 导入数据进度
'@base_import/import_data_sidepanel/import_data_sidepanel'      // 导入数据侧边栏
'@web/search/layout'                                            // 搜索布局
'@web/core/browser/router'                                      // 路由器
'@web/webclient/actions/action_service'                        // 操作服务
'@web/views/widgets/documentation_link/documentation_link'     // 文档链接
```

## 核心功能

### 1. ImportAction 组件

```javascript
const ImportAction = class ImportAction extends Component {
    static template = "ImportAction";
    static nextId = 1;
    static components = {
        FileInput,
        ImportDataContent,
        ImportDataSidepanel,
        Layout,
        DocumentationLink,
    };
    static props = { ...standardActionServiceProps };
}
```

**组件特性**:
- **继承扩展**: 继承OWL Component的所有功能
- **子组件集成**: 集成文件输入、数据内容、侧边栏等子组件
- **标准属性**: 使用标准操作服务属性
- **模板绑定**: 使用专门的导入操作模板

### 2. 组件初始化

```javascript
setup() {
    this.notification = useService("notification");
    this.orm = useService("orm");
    this.env.config.setDisplayName(this.props.action.name || _t("Import a File"));
    
    this.resModel = this.props.action.params.model || this.props.action.params.active_model;
    if (this.resModel) {
        this.props.updateActionState({ active_model: this.resModel });
    }
    
    this.model = useImportModel({
        env: this.env,
        resModel: this.resModel,
        context: this.props.action.params.context || {},
        orm: this.orm,
    });

    this.state = useState({
        filename: undefined,
        fileLength: 0,
        importMessages: [],
        importProgress: {
            value: 0,
            step: 1,
        },
        isPaused: false,
        previewError: "",
    });
}
```

**初始化功能**:
- **服务集成**: 集成通知和ORM服务
- **模型配置**: 设置目标模型和上下文
- **状态管理**: 初始化组件状态
- **导入模型**: 创建和配置导入模型实例

### 3. 文件拖拽支持

```javascript
useDropzone(useRef("root"), async event => {
    const { files } = event.dataTransfer;
    if (files.length === 0) {
        this.notification.add(_t("Please upload an Excel (.xls or .xlsx) or .csv file to import."), {
            type: "danger",
        });
    } else if (files.length > 1) {
        this.notification.add(_t("Please upload a single file."), {
            type: "danger",
        });
    } else {
        const file = files[0];
        const isValidFile = file.name.endsWith(".csv")
                         || file.name.endsWith(".xls")
                         || file.name.endsWith(".xlsx");
        if (!isValidFile) {
            this.notification.add(_t("Please upload an Excel (.xls or .xlsx) or .csv file to import."), {
                type: "danger",
            });
        } else {
            await this.uploadFiles(this.uploadFilesRoute, {
                csrf_token: odoo.csrf_token,
                ufile: [file],
                model: this.resModel,
                id: this.model.id,
            });
            this.handleFilesUpload([file]);
        }
    }
});
```

**拖拽功能**:
- **文件验证**: 验证文件数量和格式
- **格式支持**: 支持CSV、XLS、XLSX格式
- **错误提示**: 提供详细的错误提示信息
- **自动上传**: 验证通过后自动上传文件

### 4. 导入执行

```javascript
async executeImport(isTest = false) {
    if (this.state.isPaused) {
        this.state.isPaused = false;
    }
    this.state.importMessages = [];
    this.state.importProgress.value = 0;
    this.state.importProgress.step = 1;
    
    const importRes = await this.model.executeImport(isTest, this.totalSteps, this.state.importProgress);
    this.state.importMessages = this.model.importMessages;
    
    if (importRes.hasError) {
        this.state.importProgress.value = 0;
        this.state.importProgress.step = 1;
    } else {
        this.state.importProgress.value = 100;
        if (!isTest) {
            this.exit(importRes.ids);
        }
    }
    
    return importRes;
}
```

**导入执行功能**:
- **状态重置**: 重置导入状态和进度
- **模型调用**: 调用导入模型执行导入
- **进度更新**: 实时更新导入进度
- **错误处理**: 处理导入过程中的错误
- **结果处理**: 处理导入成功或失败的结果

### 5. 文件处理

```javascript
async handleFilesUpload(files) {
    this.state.filename = files[0].name;
    this.state.fileLength = 0;
    this.state.previewError = "";
    
    try {
        await this.model.updateData(true);
        this.state.fileLength = this.model.fileLength;
    } catch (error) {
        this.state.previewError = error.message;
        this.notification.add(error.message, { type: "danger" });
    }
}
```

**文件处理功能**:
- **文件信息**: 记录文件名和长度
- **数据更新**: 更新模型数据
- **错误处理**: 处理文件处理过程中的错误
- **状态更新**: 更新组件状态

### 6. 批量处理配置

```javascript
get isBatched() {
    return this.state.fileLength > 100;
}

get totalSteps() {
    return this.isBatched ? Math.ceil(this.totalToImport / this.importOptions.limit) : 1;
}

get totalToImport() {
    return this.state.fileLength - parseInt(this.importOptions.skip);
}
```

**批量处理功能**:
- **批量判断**: 根据文件大小判断是否需要批量处理
- **步骤计算**: 计算导入所需的总步骤数
- **数量计算**: 计算实际需要导入的记录数量

## 使用场景

### 1. 导入操作组件增强

```javascript
// 导入操作组件增强功能
const ImportActionEnhancer = {
    enhanceImportAction: () => {
        // 增强的导入操作组件
        class EnhancedImportAction extends ImportAction {
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    enableAdvancedPreview: true,
                    enableRealTimeValidation: true,
                    enableAutoSave: true,
                    enableTemplateManagement: true,
                    enableProgressPersistence: true,
                    enableFileHistory: true,
                    enableBulkOperations: true,
                    enableCustomValidation: true,
                    maxFileSize: 100 * 1024 * 1024, // 100MB
                    supportedFormats: ['csv', 'xls', 'xlsx', 'json', 'xml'],
                    autoSaveInterval: 30000, // 30秒
                    maxHistoryItems: 10,
                    enableNotifications: true,
                    enableAnalytics: true
                };
                
                // 增强的状态
                this.enhancedState = useState({
                    isValidating: false,
                    validationResults: [],
                    autoSaveEnabled: false,
                    lastSaveTime: null,
                    fileHistory: [],
                    customValidationRules: [],
                    importTemplates: [],
                    analytics: {
                        startTime: null,
                        endTime: null,
                        fileSize: 0,
                        recordCount: 0,
                        errorCount: 0
                    }
                });
                
                // 文件历史管理
                this.fileHistoryManager = new FileHistoryManager();
                
                // 模板管理器
                this.templateManager = new TemplateManager();
                
                // 验证引擎
                this.validationEngine = new ValidationEngine();
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 设置自动保存
                if (this.enhancedConfig.enableAutoSave) {
                    this.setupAutoSave();
                }
                
                // 设置文件历史
                if (this.enhancedConfig.enableFileHistory) {
                    this.setupFileHistory();
                }
                
                // 设置模板管理
                if (this.enhancedConfig.enableTemplateManagement) {
                    this.setupTemplateManagement();
                }
                
                // 设置实时验证
                if (this.enhancedConfig.enableRealTimeValidation) {
                    this.setupRealTimeValidation();
                }
                
                // 设置分析
                if (this.enhancedConfig.enableAnalytics) {
                    this.setupAnalytics();
                }
            }
            
            // 增强的文件上传处理
            async handleFilesUpload(files) {
                try {
                    // 开始分析
                    this.enhancedState.analytics.startTime = Date.now();
                    this.enhancedState.analytics.fileSize = files[0].size;
                    
                    // 文件大小验证
                    if (files[0].size > this.enhancedConfig.maxFileSize) {
                        throw new Error(`文件大小超过限制 (${this.enhancedConfig.maxFileSize} bytes)`);
                    }
                    
                    // 格式验证
                    const fileExtension = this.getFileExtension(files[0].name);
                    if (!this.enhancedConfig.supportedFormats.includes(fileExtension)) {
                        throw new Error(`不支持的文件格式: ${fileExtension}`);
                    }
                    
                    // 执行原有逻辑
                    await super.handleFilesUpload(files);
                    
                    // 添加到文件历史
                    if (this.enhancedConfig.enableFileHistory) {
                        this.addToFileHistory(files[0]);
                    }
                    
                    // 实时验证
                    if (this.enhancedConfig.enableRealTimeValidation) {
                        await this.performRealTimeValidation();
                    }
                    
                    // 更新分析数据
                    this.enhancedState.analytics.recordCount = this.state.fileLength;
                    
                } catch (error) {
                    this.enhancedState.analytics.errorCount++;
                    throw error;
                }
            }
            
            // 增强的导入执行
            async executeImport(isTest = false) {
                try {
                    // 执行自定义验证
                    if (this.enhancedConfig.enableCustomValidation) {
                        const validationResult = await this.performCustomValidation();
                        if (!validationResult.isValid) {
                            throw new Error(`自定义验证失败: ${validationResult.errors.join(', ')}`);
                        }
                    }
                    
                    // 执行原有导入逻辑
                    const result = await super.executeImport(isTest);
                    
                    // 保存导入模板
                    if (this.enhancedConfig.enableTemplateManagement && !isTest) {
                        await this.saveImportTemplate();
                    }
                    
                    // 更新分析数据
                    this.enhancedState.analytics.endTime = Date.now();
                    
                    // 发送分析数据
                    if (this.enhancedConfig.enableAnalytics) {
                        await this.sendAnalyticsData();
                    }
                    
                    return result;
                    
                } catch (error) {
                    this.enhancedState.analytics.errorCount++;
                    this.enhancedState.analytics.endTime = Date.now();
                    throw error;
                }
            }
            
            // 实时验证
            async performRealTimeValidation() {
                this.enhancedState.isValidating = true;
                this.enhancedState.validationResults = [];
                
                try {
                    // 数据格式验证
                    const formatValidation = await this.validateDataFormat();
                    this.enhancedState.validationResults.push(...formatValidation);
                    
                    // 字段映射验证
                    const mappingValidation = await this.validateFieldMapping();
                    this.enhancedState.validationResults.push(...mappingValidation);
                    
                    // 数据完整性验证
                    const integrityValidation = await this.validateDataIntegrity();
                    this.enhancedState.validationResults.push(...integrityValidation);
                    
                } catch (error) {
                    console.error('实时验证失败:', error);
                } finally {
                    this.enhancedState.isValidating = false;
                }
            }
            
            // 自定义验证
            async performCustomValidation() {
                const result = { isValid: true, errors: [] };
                
                for (const rule of this.enhancedState.customValidationRules) {
                    try {
                        const ruleResult = await this.executeValidationRule(rule);
                        if (!ruleResult.isValid) {
                            result.isValid = false;
                            result.errors.push(...ruleResult.errors);
                        }
                    } catch (error) {
                        result.isValid = false;
                        result.errors.push(`验证规则执行失败: ${error.message}`);
                    }
                }
                
                return result;
            }
            
            // 执行验证规则
            async executeValidationRule(rule) {
                // 实现自定义验证规则的执行逻辑
                return { isValid: true, errors: [] };
            }
            
            // 设置自动保存
            setupAutoSave() {
                this.autoSaveTimer = setInterval(() => {
                    if (this.enhancedState.autoSaveEnabled) {
                        this.performAutoSave();
                    }
                }, this.enhancedConfig.autoSaveInterval);
            }
            
            // 执行自动保存
            async performAutoSave() {
                try {
                    const saveData = {
                        filename: this.state.filename,
                        importOptions: this.importOptions,
                        fieldMappings: this.getFieldMappings(),
                        timestamp: Date.now()
                    };
                    
                    localStorage.setItem('import_auto_save', JSON.stringify(saveData));
                    this.enhancedState.lastSaveTime = Date.now();
                    
                    if (this.enhancedConfig.enableNotifications) {
                        this.notification.add('导入配置已自动保存', { type: 'info' });
                    }
                } catch (error) {
                    console.error('自动保存失败:', error);
                }
            }
            
            // 恢复自动保存的数据
            async restoreAutoSave() {
                try {
                    const savedData = localStorage.getItem('import_auto_save');
                    if (savedData) {
                        const data = JSON.parse(savedData);
                        // 恢复导入配置
                        await this.restoreImportConfiguration(data);
                        
                        if (this.enhancedConfig.enableNotifications) {
                            this.notification.add('已恢复上次的导入配置', { type: 'success' });
                        }
                    }
                } catch (error) {
                    console.error('恢复自动保存数据失败:', error);
                }
            }
            
            // 设置文件历史
            setupFileHistory() {
                this.loadFileHistory();
            }
            
            // 添加到文件历史
            addToFileHistory(file) {
                const historyItem = {
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    timestamp: Date.now(),
                    id: this.generateHistoryId()
                };
                
                this.enhancedState.fileHistory.unshift(historyItem);
                
                // 限制历史记录数量
                if (this.enhancedState.fileHistory.length > this.enhancedConfig.maxHistoryItems) {
                    this.enhancedState.fileHistory = this.enhancedState.fileHistory.slice(0, this.enhancedConfig.maxHistoryItems);
                }
                
                this.saveFileHistory();
            }
            
            // 加载文件历史
            loadFileHistory() {
                try {
                    const stored = localStorage.getItem('import_file_history');
                    if (stored) {
                        this.enhancedState.fileHistory = JSON.parse(stored);
                    }
                } catch (error) {
                    console.error('加载文件历史失败:', error);
                    this.enhancedState.fileHistory = [];
                }
            }
            
            // 保存文件历史
            saveFileHistory() {
                try {
                    localStorage.setItem('import_file_history', JSON.stringify(this.enhancedState.fileHistory));
                } catch (error) {
                    console.error('保存文件历史失败:', error);
                }
            }
            
            // 设置模板管理
            setupTemplateManagement() {
                this.loadImportTemplates();
            }
            
            // 保存导入模板
            async saveImportTemplate() {
                try {
                    const template = {
                        name: `${this.resModel}_${Date.now()}`,
                        model: this.resModel,
                        fieldMappings: this.getFieldMappings(),
                        importOptions: this.importOptions,
                        timestamp: Date.now()
                    };
                    
                    this.enhancedState.importTemplates.push(template);
                    await this.saveImportTemplates();
                    
                    if (this.enhancedConfig.enableNotifications) {
                        this.notification.add('导入模板已保存', { type: 'success' });
                    }
                } catch (error) {
                    console.error('保存导入模板失败:', error);
                }
            }
            
            // 应用导入模板
            async applyImportTemplate(template) {
                try {
                    // 应用字段映射
                    await this.applyFieldMappings(template.fieldMappings);
                    
                    // 应用导入选项
                    await this.applyImportOptions(template.importOptions);
                    
                    if (this.enhancedConfig.enableNotifications) {
                        this.notification.add('导入模板已应用', { type: 'success' });
                    }
                } catch (error) {
                    console.error('应用导入模板失败:', error);
                }
            }
            
            // 设置分析
            setupAnalytics() {
                // 初始化分析数据收集
                this.enhancedState.analytics = {
                    startTime: null,
                    endTime: null,
                    fileSize: 0,
                    recordCount: 0,
                    errorCount: 0,
                    validationTime: 0,
                    importTime: 0
                };
            }
            
            // 发送分析数据
            async sendAnalyticsData() {
                try {
                    const analyticsData = {
                        ...this.enhancedState.analytics,
                        model: this.resModel,
                        filename: this.state.filename,
                        duration: this.enhancedState.analytics.endTime - this.enhancedState.analytics.startTime
                    };
                    
                    await this.orm.call('base_import.analytics', 'record_import_analytics', [], {
                        data: analyticsData
                    });
                } catch (error) {
                    console.error('发送分析数据失败:', error);
                }
            }
            
            // 工具方法
            getFileExtension(filename) {
                return filename.split('.').pop().toLowerCase();
            }
            
            generateHistoryId() {
                return `history_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            }
            
            getFieldMappings() {
                // 获取当前的字段映射配置
                return this.model.columns.map(col => ({
                    column: col.name,
                    field: col.fieldInfo ? col.fieldInfo.name : null
                }));
            }
            
            // 清理资源
            cleanup() {
                if (this.autoSaveTimer) {
                    clearInterval(this.autoSaveTimer);
                }
            }
            
            // 组件销毁时清理
            willDestroy() {
                this.cleanup();
                super.willDestroy && super.willDestroy();
            }
        }
        
        // 文件历史管理器
        class FileHistoryManager {
            constructor() {
                this.history = [];
            }
            
            add(file) {
                // 添加文件到历史记录
            }
            
            remove(id) {
                // 从历史记录中移除文件
            }
            
            clear() {
                // 清空历史记录
            }
        }
        
        // 模板管理器
        class TemplateManager {
            constructor() {
                this.templates = [];
            }
            
            save(template) {
                // 保存模板
            }
            
            load(id) {
                // 加载模板
            }
            
            delete(id) {
                // 删除模板
            }
        }
        
        // 验证引擎
        class ValidationEngine {
            constructor() {
                this.rules = [];
            }
            
            addRule(rule) {
                // 添加验证规则
            }
            
            validate(data) {
                // 执行验证
            }
        }
        
        // 导出增强的组件
        __exports.EnhancedImportAction = EnhancedImportAction;
    }
};

// 应用导入操作组件增强
ImportActionEnhancer.enhanceImportAction();
```

## 技术特点

### 1. 组件集成
- 集成多个子组件提供完整功能
- 统一的状态管理和数据流
- 模块化的功能设计

### 2. 文件处理
- 支持拖拽上传和文件选择
- 多种文件格式支持
- 完善的文件验证机制

### 3. 进度跟踪
- 实时的导入进度显示
- 分步骤的进度控制
- 可暂停和恢复的导入过程

### 4. 错误处理
- 详细的错误信息显示
- 用户友好的错误提示
- 优雅的错误恢复机制

## 设计模式

### 1. 组合模式 (Composite Pattern)
- 组合多个子组件提供完整功能
- 统一的组件接口

### 2. 观察者模式 (Observer Pattern)
- 状态变化的响应式更新
- 进度事件的监听和处理

### 3. 策略模式 (Strategy Pattern)
- 不同文件格式的处理策略
- 可配置的导入选项

## 注意事项

1. **文件大小**: 注意大文件上传的性能和内存使用
2. **用户体验**: 提供清晰的进度反馈和错误提示
3. **数据安全**: 确保文件上传和处理的安全性
4. **浏览器兼容**: 确保拖拽功能的浏览器兼容性

## 扩展建议

1. **文件预览**: 添加文件内容的预览功能
2. **模板管理**: 实现导入模板的保存和复用
3. **批量操作**: 支持多文件的批量导入
4. **实时验证**: 实现实时的数据验证和提示
5. **历史记录**: 添加导入历史记录和恢复功能

该导入操作组件为基础导入系统提供了重要的用户界面和交互功能，是数据导入系统的主要入口和控制中心。

# Binary File Manager - 二进制文件管理器

## 概述

`binary_file_manager.js` 是 Odoo 基础导入模块的二进制文件管理器，专门用于处理导入过程中的二进制文件（如图片、文档等）。该组件基于Web框架的并发工具和文件工具，集成了文件读取、批量处理、大小检查等核心功能，为基础导入系统提供了完整的二进制文件处理支持，是数据导入系统处理附件和媒体文件的重要组件。

## 文件信息
- **路径**: `/base_import/static/src/binary_file_manager.js`
- **行数**: 97
- **模块**: `@base_import/binary_file_manager`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/concurrency'  // 并发工具
'@web/core/utils/files'        // 文件工具
```

## 核心功能

### 1. BinaryFileManager 类

```javascript
const BinaryFileManager = class BinaryFileManager {
    constructor(resModel, fields, parameters, context, orm, notificationService) {
        this.resModel = resModel;
        this.fields = [".id", ...fields];
        this.parameters = parameters;
        this.context = context;
        this.orm = orm;
        this.notificationService = notificationService;
        
        this.maxBatchSize = this.parameters.maxBatchSize * 0.95;
        this.delayAfterEachBatch = this.parameters.delayAfterEachBatch * 1000;
        this.dataToSend = {};
        this.mutex = new Mutex();
    }
}
```

**构造器功能**:
- **模型配置**: 设置目标模型和字段信息
- **参数管理**: 管理批量大小和延迟参数
- **服务集成**: 集成ORM和通知服务
- **并发控制**: 使用Mutex进行并发控制
- **数据缓存**: 初始化数据发送缓存

### 2. 文件添加处理

```javascript
async addFile(id, field, file) {
    let data = await this._readFile(file);
    if (typeof data === "string" && data.startsWith("data:")) {
        // Remove data:image/*;base64,
        data = data.split(",")[1];
    }
    const dataSize = data.length;
    if (!checkFileSize(dataSize, this.notificationService)) {
        return;
    }

    if (this.getCurrentSize() + dataSize >= this.maxBatchSize) {
        await this.mutex.exec(async () => await this._send());
    }
    if (!(id in this.dataToSend)) {
        this.dataToSend[id] = Array(this.fields.length);
        this.dataToSend[id][0] = id;
    }
    const indexOfField = this.fields.indexOf(field, 1);
    this.dataToSend[id][indexOfField] = data;
}
```

**文件添加功能**:
- **文件读取**: 异步读取文件内容
- **格式处理**: 处理Data URL格式的文件数据
- **大小检查**: 验证文件大小是否符合限制
- **批量管理**: 自动触发批量发送以控制内存使用
- **数据组织**: 按记录ID和字段组织文件数据

### 3. 文件读取

```javascript
_readFile(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onerror = (event) => reject(event);
        reader.onabort = (event) => reject(event);
        reader.onload = (event) => resolve(event.target.result);
        reader.readAsDataURL(file);
    });
}
```

**文件读取功能**:
- **异步读取**: 使用Promise包装FileReader
- **错误处理**: 处理读取错误和中止事件
- **Data URL**: 读取为Data URL格式便于传输
- **事件监听**: 监听文件读取的各种事件

### 4. 批量发送

```javascript
async _send() {
    await new Promise((resolve) => {
        setTimeout(resolve, this.delayAfterEachBatch);
    });
    const data = Object.values(this.dataToSend);
    this.dataToSend = {};
    const context = {
        ...this.context,
        import_file: true,
        tracking_disable: this.parameters.tracking_disable,
        name_create_enabled_fields: this.parameters.name_create_enabled_fields || {},
        import_set_empty_fields: this.parameters.import_set_empty_fields || [],
        import_skip_records: this.parameters.import_skip_records || [],
    };
    let res;
    try {
        res = await this.orm.call(this.resModel, "load", [], {
            fields: this.fields,
            data,
            context,
        });
    } catch (error) {
        console.error(error);
        return { error };
    }
    return res;
}
```

**批量发送功能**:
- **延迟控制**: 在批次间添加延迟避免服务器过载
- **数据准备**: 准备发送的数据和上下文
- **上下文配置**: 设置导入相关的上下文参数
- **ORM调用**: 调用模型的load方法进行数据导入
- **错误处理**: 捕获和处理导入过程中的错误

### 5. 最终发送

```javascript
async sendLastPayload() {
    if (Object.keys(this.dataToSend).length > 0) {
        await this.mutex.exec(async () => await this._send());
    }
}
```

**最终发送功能**:
- **数据检查**: 检查是否还有待发送的数据
- **并发控制**: 使用Mutex确保线程安全
- **清理发送**: 发送剩余的所有数据

### 6. 大小计算

```javascript
getCurrentSize() {
    return JSON.stringify(this.dataToSend).length;
}
```

**大小计算功能**:
- **JSON序列化**: 通过JSON序列化计算数据大小
- **内存监控**: 监控当前缓存的数据大小
- **批量控制**: 为批量发送提供大小依据

## 使用场景

### 1. 二进制文件管理器增强

```javascript
// 二进制文件管理器增强功能
const BinaryFileManagerEnhancer = {
    enhanceBinaryFileManager: () => {
        // 增强的二进制文件管理器
        class EnhancedBinaryFileManager extends BinaryFileManager {
            constructor(resModel, fields, parameters, context, orm, notificationService) {
                super(resModel, fields, parameters, context, orm, notificationService);
                
                // 增强的配置选项
                this.enhancedConfig = {
                    enableCompression: true,
                    enableThumbnails: true,
                    enableProgressTracking: true,
                    enableFileValidation: true,
                    enableRetry: true,
                    enableCaching: true,
                    maxRetryAttempts: 3,
                    retryDelay: 1000,
                    compressionQuality: 0.8,
                    thumbnailSize: 150,
                    supportedFormats: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
                    maxFileSize: 10 * 1024 * 1024, // 10MB
                    enableMetadataExtraction: true,
                    enableVirusScanning: false
                };
                
                // 文件处理统计
                this.statistics = {
                    totalFiles: 0,
                    processedFiles: 0,
                    failedFiles: 0,
                    totalSize: 0,
                    processedSize: 0,
                    startTime: null,
                    endTime: null
                };
                
                // 文件缓存
                this.fileCache = new Map();
                
                // 进度回调
                this.progressCallbacks = [];
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 设置文件验证
                if (this.enhancedConfig.enableFileValidation) {
                    this.setupFileValidation();
                }
                
                // 设置压缩
                if (this.enhancedConfig.enableCompression) {
                    this.setupCompression();
                }
                
                // 设置缩略图
                if (this.enhancedConfig.enableThumbnails) {
                    this.setupThumbnails();
                }
                
                // 设置进度跟踪
                if (this.enhancedConfig.enableProgressTracking) {
                    this.setupProgressTracking();
                }
            }
            
            // 增强的文件添加
            async addFile(id, field, file) {
                try {
                    // 开始统计
                    if (this.statistics.startTime === null) {
                        this.statistics.startTime = Date.now();
                    }
                    this.statistics.totalFiles++;
                    this.statistics.totalSize += file.size;
                    
                    // 文件验证
                    if (this.enhancedConfig.enableFileValidation) {
                        const validationResult = await this.validateFile(file);
                        if (!validationResult.isValid) {
                            this.statistics.failedFiles++;
                            throw new Error(validationResult.error);
                        }
                    }
                    
                    // 检查缓存
                    const cacheKey = this.generateCacheKey(file);
                    if (this.enhancedConfig.enableCaching && this.fileCache.has(cacheKey)) {
                        const cachedData = this.fileCache.get(cacheKey);
                        await this.addProcessedFile(id, field, cachedData);
                        return;
                    }
                    
                    // 处理文件
                    let processedFile = await this.processFile(file);
                    
                    // 缓存处理结果
                    if (this.enhancedConfig.enableCaching) {
                        this.fileCache.set(cacheKey, processedFile);
                    }
                    
                    // 添加处理后的文件
                    await this.addProcessedFile(id, field, processedFile);
                    
                    this.statistics.processedFiles++;
                    this.statistics.processedSize += file.size;
                    
                    // 更新进度
                    this.updateProgress();
                    
                } catch (error) {
                    this.statistics.failedFiles++;
                    console.error('文件添加失败:', error);
                    
                    // 重试机制
                    if (this.enhancedConfig.enableRetry) {
                        await this.retryAddFile(id, field, file);
                    } else {
                        throw error;
                    }
                }
            }
            
            // 添加处理后的文件
            async addProcessedFile(id, field, processedFile) {
                let data = processedFile.data;
                if (typeof data === "string" && data.startsWith("data:")) {
                    data = data.split(",")[1];
                }
                
                const dataSize = data.length;
                if (!checkFileSize(dataSize, this.notificationService)) {
                    return;
                }

                if (this.getCurrentSize() + dataSize >= this.maxBatchSize) {
                    await this.mutex.exec(async () => await this._send());
                }
                
                if (!(id in this.dataToSend)) {
                    this.dataToSend[id] = Array(this.fields.length);
                    this.dataToSend[id][0] = id;
                }
                
                const indexOfField = this.fields.indexOf(field, 1);
                this.dataToSend[id][indexOfField] = data;
                
                // 添加元数据
                if (processedFile.metadata) {
                    this.addFileMetadata(id, field, processedFile.metadata);
                }
            }
            
            // 处理文件
            async processFile(file) {
                let processedFile = {
                    data: await this._readFile(file),
                    originalSize: file.size,
                    processedSize: file.size,
                    metadata: {}
                };
                
                // 提取元数据
                if (this.enhancedConfig.enableMetadataExtraction) {
                    processedFile.metadata = await this.extractMetadata(file);
                }
                
                // 压缩处理
                if (this.enhancedConfig.enableCompression && this.isCompressibleFile(file)) {
                    processedFile = await this.compressFile(processedFile);
                }
                
                // 生成缩略图
                if (this.enhancedConfig.enableThumbnails && this.isImageFile(file)) {
                    processedFile.thumbnail = await this.generateThumbnail(processedFile.data);
                }
                
                return processedFile;
            }
            
            // 文件验证
            async validateFile(file) {
                const result = { isValid: true, error: null };
                
                try {
                    // 大小验证
                    if (file.size > this.enhancedConfig.maxFileSize) {
                        result.isValid = false;
                        result.error = `文件大小超过限制 (${this.enhancedConfig.maxFileSize} bytes)`;
                        return result;
                    }
                    
                    // 格式验证
                    const fileExtension = this.getFileExtension(file.name);
                    if (!this.enhancedConfig.supportedFormats.includes(fileExtension.toLowerCase())) {
                        result.isValid = false;
                        result.error = `不支持的文件格式: ${fileExtension}`;
                        return result;
                    }
                    
                    // 病毒扫描
                    if (this.enhancedConfig.enableVirusScanning) {
                        const scanResult = await this.scanForVirus(file);
                        if (!scanResult.isClean) {
                            result.isValid = false;
                            result.error = '文件包含恶意内容';
                            return result;
                        }
                    }
                    
                } catch (error) {
                    result.isValid = false;
                    result.error = `验证过程中发生错误: ${error.message}`;
                }
                
                return result;
            }
            
            // 压缩文件
            async compressFile(processedFile) {
                try {
                    if (this.isImageFile({ type: processedFile.metadata.mimeType })) {
                        const compressedData = await this.compressImage(
                            processedFile.data, 
                            this.enhancedConfig.compressionQuality
                        );
                        
                        processedFile.data = compressedData;
                        processedFile.processedSize = compressedData.length;
                        processedFile.compressed = true;
                        processedFile.compressionRatio = processedFile.originalSize / processedFile.processedSize;
                    }
                } catch (error) {
                    console.warn('文件压缩失败:', error);
                }
                
                return processedFile;
            }
            
            // 压缩图片
            async compressImage(dataUrl, quality) {
                return new Promise((resolve) => {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    const img = new Image();
                    
                    img.onload = () => {
                        canvas.width = img.width;
                        canvas.height = img.height;
                        ctx.drawImage(img, 0, 0);
                        
                        const compressedDataUrl = canvas.toDataURL('image/jpeg', quality);
                        resolve(compressedDataUrl);
                    };
                    
                    img.src = dataUrl;
                });
            }
            
            // 生成缩略图
            async generateThumbnail(dataUrl) {
                return new Promise((resolve) => {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    const img = new Image();
                    
                    img.onload = () => {
                        const size = this.enhancedConfig.thumbnailSize;
                        const scale = Math.min(size / img.width, size / img.height);
                        
                        canvas.width = img.width * scale;
                        canvas.height = img.height * scale;
                        
                        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                        
                        const thumbnailDataUrl = canvas.toDataURL('image/jpeg', 0.8);
                        resolve(thumbnailDataUrl);
                    };
                    
                    img.src = dataUrl;
                });
            }
            
            // 提取元数据
            async extractMetadata(file) {
                const metadata = {
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    mimeType: file.type,
                    lastModified: file.lastModified,
                    extension: this.getFileExtension(file.name)
                };
                
                // 图片元数据
                if (this.isImageFile(file)) {
                    try {
                        const imageMetadata = await this.extractImageMetadata(file);
                        Object.assign(metadata, imageMetadata);
                    } catch (error) {
                        console.warn('提取图片元数据失败:', error);
                    }
                }
                
                return metadata;
            }
            
            // 提取图片元数据
            async extractImageMetadata(file) {
                return new Promise((resolve) => {
                    const img = new Image();
                    const url = URL.createObjectURL(file);
                    
                    img.onload = () => {
                        const metadata = {
                            width: img.naturalWidth,
                            height: img.naturalHeight,
                            aspectRatio: img.naturalWidth / img.naturalHeight
                        };
                        
                        URL.revokeObjectURL(url);
                        resolve(metadata);
                    };
                    
                    img.onerror = () => {
                        URL.revokeObjectURL(url);
                        resolve({});
                    };
                    
                    img.src = url;
                });
            }
            
            // 重试添加文件
            async retryAddFile(id, field, file, attempt = 1) {
                if (attempt > this.enhancedConfig.maxRetryAttempts) {
                    throw new Error(`文件添加失败，已重试 ${this.enhancedConfig.maxRetryAttempts} 次`);
                }
                
                await new Promise(resolve => 
                    setTimeout(resolve, this.enhancedConfig.retryDelay * attempt)
                );
                
                try {
                    await this.addFile(id, field, file);
                } catch (error) {
                    await this.retryAddFile(id, field, file, attempt + 1);
                }
            }
            
            // 工具方法
            getFileExtension(filename) {
                return filename.split('.').pop() || '';
            }
            
            isImageFile(file) {
                return file.type && file.type.startsWith('image/');
            }
            
            isCompressibleFile(file) {
                return this.isImageFile(file) && !file.type.includes('gif');
            }
            
            generateCacheKey(file) {
                return `${file.name}_${file.size}_${file.lastModified}`;
            }
            
            // 进度跟踪
            setupProgressTracking() {
                this.progressCallbacks = [];
            }
            
            addProgressCallback(callback) {
                this.progressCallbacks.push(callback);
            }
            
            updateProgress() {
                const progress = {
                    totalFiles: this.statistics.totalFiles,
                    processedFiles: this.statistics.processedFiles,
                    failedFiles: this.statistics.failedFiles,
                    percentage: this.statistics.totalFiles > 0 ? 
                        (this.statistics.processedFiles / this.statistics.totalFiles) * 100 : 0,
                    totalSize: this.statistics.totalSize,
                    processedSize: this.statistics.processedSize
                };
                
                this.progressCallbacks.forEach(callback => {
                    try {
                        callback(progress);
                    } catch (error) {
                        console.error('进度回调执行失败:', error);
                    }
                });
            }
            
            // 获取统计信息
            getStatistics() {
                const now = Date.now();
                const duration = this.statistics.startTime ? 
                    (this.statistics.endTime || now) - this.statistics.startTime : 0;
                
                return {
                    ...this.statistics,
                    duration: duration,
                    averageFileSize: this.statistics.totalFiles > 0 ? 
                        this.statistics.totalSize / this.statistics.totalFiles : 0,
                    processingSpeed: duration > 0 ? 
                        this.statistics.processedFiles / (duration / 1000) : 0
                };
            }
            
            // 清理资源
            cleanup() {
                this.fileCache.clear();
                this.progressCallbacks = [];
                this.statistics.endTime = Date.now();
            }
        }
        
        // 导出增强的管理器
        __exports.EnhancedBinaryFileManager = EnhancedBinaryFileManager;
    }
};

// 应用二进制文件管理器增强
BinaryFileManagerEnhancer.enhanceBinaryFileManager();
```

## 技术特点

### 1. 批量处理
- 智能的批量大小控制
- 自动触发批量发送
- 内存使用优化

### 2. 并发控制
- 使用Mutex进行线程安全
- 避免并发发送冲突
- 保证数据一致性

### 3. 文件处理
- 支持多种文件格式
- Base64编码处理
- 文件大小验证

### 4. 错误处理
- 完善的错误捕获
- 优雅的错误恢复
- 详细的错误信息

## 设计模式

### 1. 管理器模式 (Manager Pattern)
- 集中管理文件处理逻辑
- 统一的接口和配置

### 2. 批处理模式 (Batch Processing Pattern)
- 批量处理文件数据
- 优化网络传输

### 3. 异步模式 (Async Pattern)
- 异步文件读取和处理
- 非阻塞的用户界面

## 注意事项

1. **内存管理**: 注意大文件的内存使用
2. **批量大小**: 合理设置批量大小避免超时
3. **文件格式**: 确保支持的文件格式正确
4. **错误处理**: 妥善处理文件读取和网络错误

## 扩展建议

1. **文件压缩**: 添加文件压缩功能减少传输大小
2. **进度显示**: 实现文件处理进度的可视化
3. **文件验证**: 增强文件格式和内容验证
4. **缓存机制**: 实现文件处理结果的缓存
5. **并行处理**: 支持多文件的并行处理

该二进制文件管理器为基础导入系统提供了重要的文件处理功能，是数据导入中处理附件和媒体文件的核心组件。

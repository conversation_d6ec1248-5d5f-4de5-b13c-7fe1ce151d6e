# Import Data Progress - 导入数据进度组件

## 概述

`import_data_progress.js` 是 Odoo 基础导入模块的数据进度显示组件，专门用于展示导入过程的实时进度和时间估算。该组件基于OWL框架，集成了进度跟踪、时间计算、中断控制等核心功能，为基础导入系统提供了完整的进度监控和用户反馈支持，是数据导入系统进度管理的重要组件。

## 文件信息
- **路径**: `/base_import/static/src/import_data_progress/import_data_progress.js`
- **行数**: 61
- **模块**: `@base_import/import_data_progress/import_data_progress`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'    // OWL框架
```

## 核心功能

### 1. ImportDataProgress 组件

```javascript
const ImportDataProgress = class ImportDataProgress extends Component {
    static template = "ImportDataProgress";
    static props = {
        importProgress: { type: Object },
        stopImport: { type: Function },
        totalSteps: { type: Number },
    };
}
```

**组件特性**:
- **继承扩展**: 继承OWL Component的所有功能
- **属性配置**: 支持进度对象、停止函数和总步骤数
- **模板绑定**: 使用专门的进度显示模板
- **进度监控**: 专门用于监控和显示导入进度

### 2. 组件初始化

```javascript
setup() {
    this.timer = undefined;
    this.timeStart = Date.now();
    this.state = useState({
        isInterrupted: false,
        timeLeft: null,
    });

    useEffect(
        () => {
            this.updateTimer();
            return () => {
                clearInterval(this.timer);
            };
        },
        () => []
    );
}
```

**初始化功能**:
- **计时器管理**: 初始化和管理进度更新计时器
- **开始时间**: 记录导入开始的时间戳
- **状态管理**: 初始化中断状态和剩余时间
- **效果钩子**: 使用useEffect管理计时器生命周期
- **清理机制**: 确保组件销毁时清理计时器

### 3. 时间计算

```javascript
get minutesLeft() {
    return this.state.timeLeft.toFixed(2);
}

get secondsLeft() {
    return Math.round(this.state.timeLeft * 60);
}
```

**时间计算功能**:
- **分钟显示**: 以分钟为单位显示剩余时间
- **秒数显示**: 以秒为单位显示剩余时间
- **精度控制**: 分钟保留两位小数，秒数四舍五入
- **动态更新**: 根据进度实时更新时间估算

### 4. 中断控制

```javascript
interrupt() {
    this.state.isInterrupted = true;
    this.props.stopImport();
}
```

**中断控制功能**:
- **状态更新**: 设置中断状态标志
- **停止调用**: 调用父组件提供的停止函数
- **用户控制**: 允许用户主动中断导入过程
- **状态同步**: 保持中断状态的同步

### 5. 计时器更新

```javascript
updateTimer() {
    if (this.timer) {
        clearInterval(this.timer);
    }
    this.state.timeLeft =
        ((Date.now() - this.timeStart) *
            ((100 - this.props.importProgress.value) / this.props.importProgress.value)) /
        60000;
    this.timer = setInterval(() => this.updateTimer(), 1000);
}
```

**计时器更新功能**:
- **计时器清理**: 清理现有计时器避免重复
- **时间估算**: 基于已用时间和进度百分比估算剩余时间
- **定期更新**: 每秒更新一次时间估算
- **动态调整**: 根据实际进度动态调整时间预测

## 使用场景

### 1. 导入数据进度组件增强

```javascript
// 导入数据进度组件增强功能
const ImportDataProgressEnhancer = {
    enhanceImportDataProgress: () => {
        // 增强的导入数据进度组件
        class EnhancedImportDataProgress extends ImportDataProgress {
            static props = {
                ...ImportDataProgress.props,
                enableDetailedProgress: { type: Boolean, optional: true },
                enableSpeedCalculation: { type: Boolean, optional: true },
                enableProgressHistory: { type: Boolean, optional: true },
                enableProgressPrediction: { type: Boolean, optional: true },
                enableProgressNotifications: { type: Boolean, optional: true },
                progressUpdateInterval: { type: Number, optional: true },
                enableProgressAnalytics: { type: Boolean, optional: true },
                enableProgressExport: { type: Boolean, optional: true }
            };
            
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    enableDetailedProgress: this.props.enableDetailedProgress || true,
                    enableSpeedCalculation: this.props.enableSpeedCalculation || true,
                    enableProgressHistory: this.props.enableProgressHistory || true,
                    enableProgressPrediction: this.props.enableProgressPrediction || true,
                    enableProgressNotifications: this.props.enableProgressNotifications || true,
                    progressUpdateInterval: this.props.progressUpdateInterval || 1000,
                    enableProgressAnalytics: this.props.enableProgressAnalytics || true,
                    enableProgressExport: this.props.enableProgressExport || true,
                    enableProgressVisualization: true,
                    enableMilestones: true,
                    enablePerformanceMetrics: true
                };
                
                // 增强的状态
                this.enhancedState = useState({
                    ...this.state,
                    currentSpeed: 0,
                    averageSpeed: 0,
                    progressHistory: [],
                    milestones: [],
                    performanceMetrics: {},
                    predictions: {},
                    notifications: [],
                    detailedProgress: {
                        recordsProcessed: 0,
                        recordsPerSecond: 0,
                        bytesProcessed: 0,
                        bytesPerSecond: 0
                    }
                });
                
                // 进度历史管理器
                this.progressHistoryManager = new ProgressHistoryManager();
                
                // 速度计算器
                this.speedCalculator = new SpeedCalculator();
                
                // 预测引擎
                this.predictionEngine = new PredictionEngine();
                
                // 通知管理器
                this.notificationManager = new NotificationManager();
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 设置里程碑
                if (this.enhancedConfig.enableMilestones) {
                    this.setupMilestones();
                }
                
                // 初始化性能指标
                if (this.enhancedConfig.enablePerformanceMetrics) {
                    this.initializePerformanceMetrics();
                }
                
                // 设置通知
                if (this.enhancedConfig.enableProgressNotifications) {
                    this.setupNotifications();
                }
                
                // 开始进度历史记录
                if (this.enhancedConfig.enableProgressHistory) {
                    this.startProgressHistoryRecording();
                }
            }
            
            // 增强的计时器更新
            updateTimer() {
                // 调用原有更新逻辑
                super.updateTimer();
                
                // 更新详细进度
                if (this.enhancedConfig.enableDetailedProgress) {
                    this.updateDetailedProgress();
                }
                
                // 计算速度
                if (this.enhancedConfig.enableSpeedCalculation) {
                    this.calculateSpeed();
                }
                
                // 记录进度历史
                if (this.enhancedConfig.enableProgressHistory) {
                    this.recordProgressHistory();
                }
                
                // 更新预测
                if (this.enhancedConfig.enableProgressPrediction) {
                    this.updatePredictions();
                }
                
                // 检查里程碑
                if (this.enhancedConfig.enableMilestones) {
                    this.checkMilestones();
                }
                
                // 更新性能指标
                if (this.enhancedConfig.enablePerformanceMetrics) {
                    this.updatePerformanceMetrics();
                }
            }
            
            // 更新详细进度
            updateDetailedProgress() {
                const currentTime = Date.now();
                const elapsedTime = (currentTime - this.timeStart) / 1000; // 秒
                const progress = this.props.importProgress.value;
                
                // 估算已处理记录数
                const totalRecords = this.estimateTotalRecords();
                const recordsProcessed = Math.round((totalRecords * progress) / 100);
                const recordsPerSecond = elapsedTime > 0 ? recordsProcessed / elapsedTime : 0;
                
                // 估算已处理字节数
                const totalBytes = this.estimateTotalBytes();
                const bytesProcessed = Math.round((totalBytes * progress) / 100);
                const bytesPerSecond = elapsedTime > 0 ? bytesProcessed / elapsedTime : 0;
                
                this.enhancedState.detailedProgress = {
                    recordsProcessed,
                    recordsPerSecond: Math.round(recordsPerSecond),
                    bytesProcessed,
                    bytesPerSecond: Math.round(bytesPerSecond)
                };
            }
            
            // 计算速度
            calculateSpeed() {
                const currentProgress = this.props.importProgress.value;
                const currentTime = Date.now();
                
                // 计算当前速度
                this.enhancedState.currentSpeed = this.speedCalculator.calculateCurrentSpeed(
                    currentProgress, currentTime
                );
                
                // 计算平均速度
                this.enhancedState.averageSpeed = this.speedCalculator.calculateAverageSpeed(
                    currentProgress, currentTime, this.timeStart
                );
            }
            
            // 记录进度历史
            recordProgressHistory() {
                const historyEntry = {
                    timestamp: Date.now(),
                    progress: this.props.importProgress.value,
                    step: this.props.importProgress.step,
                    speed: this.enhancedState.currentSpeed,
                    timeLeft: this.state.timeLeft
                };
                
                this.progressHistoryManager.addEntry(historyEntry);
                this.enhancedState.progressHistory = this.progressHistoryManager.getRecentHistory(50);
            }
            
            // 更新预测
            updatePredictions() {
                const predictions = this.predictionEngine.generatePredictions(
                    this.enhancedState.progressHistory,
                    this.props.importProgress
                );
                
                this.enhancedState.predictions = predictions;
            }
            
            // 设置里程碑
            setupMilestones() {
                this.enhancedState.milestones = [
                    { progress: 25, label: '25% 完成', reached: false },
                    { progress: 50, label: '50% 完成', reached: false },
                    { progress: 75, label: '75% 完成', reached: false },
                    { progress: 90, label: '90% 完成', reached: false },
                    { progress: 100, label: '导入完成', reached: false }
                ];
            }
            
            // 检查里程碑
            checkMilestones() {
                const currentProgress = this.props.importProgress.value;
                
                for (const milestone of this.enhancedState.milestones) {
                    if (!milestone.reached && currentProgress >= milestone.progress) {
                        milestone.reached = true;
                        this.onMilestoneReached(milestone);
                    }
                }
            }
            
            // 里程碑达成处理
            onMilestoneReached(milestone) {
                if (this.enhancedConfig.enableProgressNotifications) {
                    this.notificationManager.showMilestoneNotification(milestone);
                }
                
                // 记录里程碑事件
                console.log('里程碑达成:', milestone.label);
            }
            
            // 初始化性能指标
            initializePerformanceMetrics() {
                this.enhancedState.performanceMetrics = {
                    startTime: this.timeStart,
                    totalElapsedTime: 0,
                    averageStepTime: 0,
                    fastestStepTime: Infinity,
                    slowestStepTime: 0,
                    stepsCompleted: 0,
                    estimatedTotalTime: 0
                };
            }
            
            // 更新性能指标
            updatePerformanceMetrics() {
                const currentTime = Date.now();
                const metrics = this.enhancedState.performanceMetrics;
                
                metrics.totalElapsedTime = currentTime - metrics.startTime;
                metrics.stepsCompleted = this.props.importProgress.step;
                
                if (metrics.stepsCompleted > 0) {
                    metrics.averageStepTime = metrics.totalElapsedTime / metrics.stepsCompleted;
                    metrics.estimatedTotalTime = metrics.averageStepTime * this.props.totalSteps;
                }
            }
            
            // 设置通知
            setupNotifications() {
                this.notificationManager.configure({
                    enableMilestoneNotifications: true,
                    enableTimeWarnings: true,
                    enableSpeedAlerts: true,
                    enableCompletionNotification: true
                });
            }
            
            // 开始进度历史记录
            startProgressHistoryRecording() {
                this.progressHistoryManager.startRecording();
            }
            
            // 导出进度数据
            exportProgressData() {
                if (!this.enhancedConfig.enableProgressExport) return;
                
                try {
                    const exportData = {
                        progressHistory: this.enhancedState.progressHistory,
                        performanceMetrics: this.enhancedState.performanceMetrics,
                        milestones: this.enhancedState.milestones,
                        predictions: this.enhancedState.predictions,
                        detailedProgress: this.enhancedState.detailedProgress,
                        exportTime: Date.now()
                    };
                    
                    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                        type: 'application/json'
                    });
                    
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `import_progress_${Date.now()}.json`;
                    a.click();
                    
                    URL.revokeObjectURL(url);
                } catch (error) {
                    console.error('导出进度数据失败:', error);
                }
            }
            
            // 获取进度统计
            getProgressStatistics() {
                return {
                    currentProgress: this.props.importProgress.value,
                    currentStep: this.props.importProgress.step,
                    totalSteps: this.props.totalSteps,
                    timeElapsed: Date.now() - this.timeStart,
                    timeLeft: this.state.timeLeft,
                    currentSpeed: this.enhancedState.currentSpeed,
                    averageSpeed: this.enhancedState.averageSpeed,
                    detailedProgress: this.enhancedState.detailedProgress,
                    performanceMetrics: this.enhancedState.performanceMetrics
                };
            }
            
            // 工具方法
            estimateTotalRecords() {
                // 估算总记录数
                return 1000; // 示例值
            }
            
            estimateTotalBytes() {
                // 估算总字节数
                return 1024 * 1024; // 示例值：1MB
            }
            
            formatTime(milliseconds) {
                const seconds = Math.floor(milliseconds / 1000);
                const minutes = Math.floor(seconds / 60);
                const hours = Math.floor(minutes / 60);
                
                if (hours > 0) {
                    return `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
                } else if (minutes > 0) {
                    return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
                } else {
                    return `${seconds}s`;
                }
            }
            
            formatBytes(bytes) {
                const units = ['B', 'KB', 'MB', 'GB'];
                let size = bytes;
                let unitIndex = 0;
                
                while (size >= 1024 && unitIndex < units.length - 1) {
                    size /= 1024;
                    unitIndex++;
                }
                
                return `${size.toFixed(2)} ${units[unitIndex]}`;
            }
        }
        
        // 进度历史管理器
        class ProgressHistoryManager {
            constructor() {
                this.history = [];
                this.isRecording = false;
            }
            
            startRecording() {
                this.isRecording = true;
            }
            
            stopRecording() {
                this.isRecording = false;
            }
            
            addEntry(entry) {
                if (this.isRecording) {
                    this.history.push(entry);
                    
                    // 限制历史记录大小
                    if (this.history.length > 1000) {
                        this.history = this.history.slice(-500);
                    }
                }
            }
            
            getRecentHistory(count) {
                return this.history.slice(-count);
            }
            
            getFullHistory() {
                return this.history;
            }
            
            clear() {
                this.history = [];
            }
        }
        
        // 速度计算器
        class SpeedCalculator {
            constructor() {
                this.lastProgress = 0;
                this.lastTime = Date.now();
            }
            
            calculateCurrentSpeed(progress, currentTime) {
                const progressDelta = progress - this.lastProgress;
                const timeDelta = (currentTime - this.lastTime) / 1000; // 秒
                
                const speed = timeDelta > 0 ? progressDelta / timeDelta : 0;
                
                this.lastProgress = progress;
                this.lastTime = currentTime;
                
                return speed;
            }
            
            calculateAverageSpeed(progress, currentTime, startTime) {
                const totalTime = (currentTime - startTime) / 1000; // 秒
                return totalTime > 0 ? progress / totalTime : 0;
            }
        }
        
        // 预测引擎
        class PredictionEngine {
            generatePredictions(history, currentProgress) {
                if (history.length < 2) {
                    return {};
                }
                
                // 简单的线性预测
                const recentHistory = history.slice(-10);
                const avgSpeed = this.calculateAverageSpeed(recentHistory);
                const remainingProgress = 100 - currentProgress.value;
                const estimatedTimeLeft = avgSpeed > 0 ? remainingProgress / avgSpeed : null;
                
                return {
                    estimatedTimeLeft,
                    confidence: this.calculateConfidence(recentHistory),
                    trend: this.calculateTrend(recentHistory)
                };
            }
            
            calculateAverageSpeed(history) {
                if (history.length < 2) return 0;
                
                let totalSpeed = 0;
                for (let i = 1; i < history.length; i++) {
                    const timeDelta = (history[i].timestamp - history[i-1].timestamp) / 1000;
                    const progressDelta = history[i].progress - history[i-1].progress;
                    totalSpeed += timeDelta > 0 ? progressDelta / timeDelta : 0;
                }
                
                return totalSpeed / (history.length - 1);
            }
            
            calculateConfidence(history) {
                // 计算预测置信度
                return 0.8; // 示例值
            }
            
            calculateTrend(history) {
                // 计算趋势
                return 'stable'; // 示例值
            }
        }
        
        // 通知管理器
        class NotificationManager {
            constructor() {
                this.config = {};
            }
            
            configure(config) {
                this.config = config;
            }
            
            showMilestoneNotification(milestone) {
                if (this.config.enableMilestoneNotifications) {
                    console.log('里程碑通知:', milestone.label);
                }
            }
            
            showTimeWarning(message) {
                if (this.config.enableTimeWarnings) {
                    console.warn('时间警告:', message);
                }
            }
            
            showSpeedAlert(message) {
                if (this.config.enableSpeedAlerts) {
                    console.log('速度提醒:', message);
                }
            }
            
            showCompletionNotification() {
                if (this.config.enableCompletionNotification) {
                    console.log('导入完成通知');
                }
            }
        }
        
        // 导出增强的组件
        __exports.EnhancedImportDataProgress = EnhancedImportDataProgress;
    }
};

// 应用导入数据进度组件增强
ImportDataProgressEnhancer.enhanceImportDataProgress();
```

## 技术特点

### 1. 实时监控
- 实时的进度百分比显示
- 动态的时间估算
- 精确的计时器管理

### 2. 时间计算
- 基于进度的剩余时间估算
- 多种时间格式显示
- 智能的时间预测算法

### 3. 用户控制
- 用户可控的中断功能
- 清晰的状态反馈
- 响应式的界面更新

### 4. 性能优化
- 高效的计时器管理
- 合理的更新频率
- 资源清理机制

## 设计模式

### 1. 观察者模式 (Observer Pattern)
- 进度变化的监听和响应
- 状态驱动的界面更新

### 2. 策略模式 (Strategy Pattern)
- 不同的时间计算策略
- 可配置的显示格式

### 3. 生命周期模式 (Lifecycle Pattern)
- 组件生命周期的管理
- 资源的创建和清理

## 注意事项

1. **计时器管理**: 确保计时器的正确创建和清理
2. **时间精度**: 注意时间计算的精度和准确性
3. **性能影响**: 避免过于频繁的更新影响性能
4. **用户体验**: 提供清晰的进度反馈和控制选项

## 扩展建议

1. **详细进度**: 显示更详细的进度信息（如记录数、速度等）
2. **进度历史**: 记录和展示进度变化历史
3. **性能指标**: 添加性能监控和分析功能
4. **进度预测**: 实现更智能的时间预测算法
5. **可视化**: 添加进度图表和可视化展示

该导入数据进度组件为基础导入系统提供了重要的进度监控和用户反馈功能，是数据导入系统进度管理的核心组件。

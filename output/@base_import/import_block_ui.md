# Import Block UI - 导入阻塞界面

## 概述

`import_block_ui.js` 是 Odoo 基础导入模块的阻塞界面组件，专门用于在导入过程中显示阻塞界面，防止用户进行其他操作。该组件基于OWL框架，提供了简洁的阻塞界面显示功能，为基础导入系统提供了用户界面保护和状态提示支持，是数据导入过程中用户体验和操作安全的重要组件。

## 文件信息
- **路径**: `/base_import/static/src/import_block_ui.js`
- **行数**: 17
- **模块**: `@base_import/import_block_ui`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'  // OWL框架
```

## 核心功能

### 1. ImportBlockUI 组件

```javascript
const ImportBlockUI = class ImportBlockUI extends Component {
    static props = {
        message: { type: String, optional: true },
        blockComponent: { type: Object, optional: true },
    };
    static template = "base_import.BlockUI";
}
```

**组件特性**:
- **继承扩展**: 继承OWL Component的所有功能
- **属性配置**: 支持消息和阻塞组件的配置
- **模板绑定**: 使用专门的阻塞界面模板
- **简洁设计**: 最小化的组件实现

### 2. 属性定义

```javascript
static props = {
    message: { type: String, optional: true },
    blockComponent: { type: Object, optional: true },
};
```

**属性功能**:
- **消息显示**: 可选的消息文本显示
- **组件阻塞**: 可选的被阻塞组件对象
- **类型安全**: 严格的属性类型定义
- **可选配置**: 所有属性都是可选的

### 3. 模板配置

```javascript
static template = "base_import.BlockUI";
```

**模板功能**:
- **专用模板**: 使用专门的阻塞界面模板
- **样式统一**: 保持与系统界面的一致性
- **响应式设计**: 适配不同屏幕尺寸
- **可定制**: 支持模板的定制和扩展

## 使用场景

### 1. 导入阻塞界面增强

```javascript
// 导入阻塞界面增强功能
const ImportBlockUIEnhancer = {
    enhanceImportBlockUI: () => {
        // 增强的导入阻塞界面
        class EnhancedImportBlockUI extends ImportBlockUI {
            static props = {
                ...ImportBlockUI.props,
                showProgress: { type: Boolean, optional: true },
                progress: { type: Number, optional: true },
                showSpinner: { type: Boolean, optional: true },
                spinnerType: { type: String, optional: true },
                showCancel: { type: Boolean, optional: true },
                onCancel: { type: Function, optional: true },
                showDetails: { type: Boolean, optional: true },
                details: { type: String, optional: true },
                theme: { type: String, optional: true },
                opacity: { type: Number, optional: true },
                zIndex: { type: Number, optional: true },
                enableAnimation: { type: Boolean, optional: true },
                animationType: { type: String, optional: true },
                showTimer: { type: Boolean, optional: true },
                estimatedTime: { type: Number, optional: true }
            };
            
            static template = "base_import.EnhancedBlockUI";
            
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    defaultMessage: '正在处理，请稍候...',
                    defaultSpinnerType: 'dots',
                    defaultTheme: 'light',
                    defaultOpacity: 0.8,
                    defaultZIndex: 9999,
                    enableKeyboardShortcuts: true,
                    enableClickToCancel: false,
                    enableAutoHide: false,
                    autoHideDelay: 5000,
                    enableSoundEffects: false,
                    enableVibration: false,
                    maxDisplayTime: 300000, // 5分钟
                    enableLogging: true
                };
                
                // 状态管理
                this.state = {
                    isVisible: false,
                    startTime: null,
                    elapsedTime: 0,
                    isAnimating: false,
                    isCancelling: false
                };
                
                // 定时器
                this.timers = {
                    elapsed: null,
                    autoHide: null,
                    maxTime: null
                };
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 设置键盘快捷键
                if (this.enhancedConfig.enableKeyboardShortcuts) {
                    this.setupKeyboardShortcuts();
                }
                
                // 设置自动隐藏
                if (this.enhancedConfig.enableAutoHide) {
                    this.setupAutoHide();
                }
                
                // 设置最大显示时间
                this.setupMaxDisplayTime();
                
                // 设置日志记录
                if (this.enhancedConfig.enableLogging) {
                    this.setupLogging();
                }
            }
            
            // 显示阻塞界面
            show(options = {}) {
                try {
                    this.state.isVisible = true;
                    this.state.startTime = Date.now();
                    this.state.isAnimating = true;
                    
                    // 记录显示事件
                    this.logEvent('block_ui_show', {
                        message: options.message || this.props.message,
                        timestamp: this.state.startTime
                    });
                    
                    // 开始计时器
                    this.startTimers();
                    
                    // 播放音效
                    if (this.enhancedConfig.enableSoundEffects) {
                        this.playSoundEffect('show');
                    }
                    
                    // 震动反馈
                    if (this.enhancedConfig.enableVibration && navigator.vibrate) {
                        navigator.vibrate([100]);
                    }
                    
                    // 动画效果
                    if (this.props.enableAnimation) {
                        this.playShowAnimation();
                    }
                    
                    // 触发显示事件
                    this.triggerEvent('show', {
                        startTime: this.state.startTime,
                        options: options
                    });
                    
                } catch (error) {
                    console.error('显示阻塞界面失败:', error);
                }
            }
            
            // 隐藏阻塞界面
            hide(options = {}) {
                try {
                    if (!this.state.isVisible) return;
                    
                    this.state.isVisible = false;
                    this.state.isAnimating = true;
                    
                    const endTime = Date.now();
                    const duration = endTime - this.state.startTime;
                    
                    // 记录隐藏事件
                    this.logEvent('block_ui_hide', {
                        duration: duration,
                        timestamp: endTime
                    });
                    
                    // 停止计时器
                    this.stopTimers();
                    
                    // 播放音效
                    if (this.enhancedConfig.enableSoundEffects) {
                        this.playSoundEffect('hide');
                    }
                    
                    // 动画效果
                    if (this.props.enableAnimation) {
                        this.playHideAnimation();
                    }
                    
                    // 触发隐藏事件
                    this.triggerEvent('hide', {
                        duration: duration,
                        endTime: endTime,
                        options: options
                    });
                    
                } catch (error) {
                    console.error('隐藏阻塞界面失败:', error);
                }
            }
            
            // 取消操作
            cancel() {
                try {
                    if (this.state.isCancelling) return;
                    
                    this.state.isCancelling = true;
                    
                    // 记录取消事件
                    this.logEvent('block_ui_cancel', {
                        elapsedTime: this.state.elapsedTime,
                        timestamp: Date.now()
                    });
                    
                    // 执行取消回调
                    if (this.props.onCancel && typeof this.props.onCancel === 'function') {
                        this.props.onCancel();
                    }
                    
                    // 播放音效
                    if (this.enhancedConfig.enableSoundEffects) {
                        this.playSoundEffect('cancel');
                    }
                    
                    // 震动反馈
                    if (this.enhancedConfig.enableVibration && navigator.vibrate) {
                        navigator.vibrate([200, 100, 200]);
                    }
                    
                    // 触发取消事件
                    this.triggerEvent('cancel', {
                        elapsedTime: this.state.elapsedTime
                    });
                    
                    // 隐藏界面
                    this.hide({ reason: 'cancelled' });
                    
                } catch (error) {
                    console.error('取消操作失败:', error);
                } finally {
                    this.state.isCancelling = false;
                }
            }
            
            // 更新进度
            updateProgress(progress, message) {
                try {
                    if (!this.state.isVisible) return;
                    
                    // 更新进度值
                    if (typeof progress === 'number') {
                        this.props.progress = Math.max(0, Math.min(100, progress));
                    }
                    
                    // 更新消息
                    if (message) {
                        this.props.message = message;
                    }
                    
                    // 记录进度更新
                    this.logEvent('progress_update', {
                        progress: this.props.progress,
                        message: this.props.message,
                        timestamp: Date.now()
                    });
                    
                    // 触发进度事件
                    this.triggerEvent('progress', {
                        progress: this.props.progress,
                        message: this.props.message
                    });
                    
                    // 重新渲染
                    this.render();
                    
                } catch (error) {
                    console.error('更新进度失败:', error);
                }
            }
            
            // 开始计时器
            startTimers() {
                // 经过时间计时器
                this.timers.elapsed = setInterval(() => {
                    this.state.elapsedTime = Date.now() - this.state.startTime;
                    this.updateElapsedTime();
                }, 1000);
                
                // 自动隐藏计时器
                if (this.enhancedConfig.enableAutoHide && this.enhancedConfig.autoHideDelay > 0) {
                    this.timers.autoHide = setTimeout(() => {
                        this.hide({ reason: 'auto_hide' });
                    }, this.enhancedConfig.autoHideDelay);
                }
                
                // 最大显示时间计时器
                if (this.enhancedConfig.maxDisplayTime > 0) {
                    this.timers.maxTime = setTimeout(() => {
                        this.hide({ reason: 'max_time_exceeded' });
                    }, this.enhancedConfig.maxDisplayTime);
                }
            }
            
            // 停止计时器
            stopTimers() {
                Object.values(this.timers).forEach(timer => {
                    if (timer) {
                        clearInterval(timer);
                        clearTimeout(timer);
                    }
                });
                
                this.timers = {
                    elapsed: null,
                    autoHide: null,
                    maxTime: null
                };
            }
            
            // 更新经过时间显示
            updateElapsedTime() {
                if (this.props.showTimer) {
                    const elapsed = this.formatTime(this.state.elapsedTime);
                    // 更新界面显示
                    this.render();
                }
            }
            
            // 格式化时间
            formatTime(milliseconds) {
                const seconds = Math.floor(milliseconds / 1000);
                const minutes = Math.floor(seconds / 60);
                const hours = Math.floor(minutes / 60);
                
                if (hours > 0) {
                    return `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
                } else if (minutes > 0) {
                    return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
                } else {
                    return `${seconds}s`;
                }
            }
            
            // 设置键盘快捷键
            setupKeyboardShortcuts() {
                this.keyboardHandler = (event) => {
                    if (!this.state.isVisible) return;
                    
                    // ESC键取消
                    if (event.key === 'Escape' && this.props.showCancel) {
                        event.preventDefault();
                        this.cancel();
                    }
                };
                
                document.addEventListener('keydown', this.keyboardHandler);
            }
            
            // 设置自动隐藏
            setupAutoHide() {
                // 在初始化时设置，实际的定时器在show()方法中启动
            }
            
            // 设置最大显示时间
            setupMaxDisplayTime() {
                // 在初始化时设置，实际的定时器在show()方法中启动
            }
            
            // 设置日志记录
            setupLogging() {
                this.logHistory = [];
            }
            
            // 播放显示动画
            playShowAnimation() {
                const animationType = this.props.animationType || 'fadeIn';
                this.playAnimation(animationType, 'show');
            }
            
            // 播放隐藏动画
            playHideAnimation() {
                const animationType = this.props.animationType || 'fadeOut';
                this.playAnimation(animationType, 'hide');
            }
            
            // 播放动画
            playAnimation(type, direction) {
                // 实现各种动画效果
                const element = this.el;
                if (!element) return;
                
                element.classList.add(`animate-${type}-${direction}`);
                
                setTimeout(() => {
                    element.classList.remove(`animate-${type}-${direction}`);
                    this.state.isAnimating = false;
                }, 300);
            }
            
            // 播放音效
            playSoundEffect(type) {
                try {
                    const soundMap = {
                        'show': '/base_import/static/src/sounds/block_show.mp3',
                        'hide': '/base_import/static/src/sounds/block_hide.mp3',
                        'cancel': '/base_import/static/src/sounds/block_cancel.mp3'
                    };
                    
                    const soundUrl = soundMap[type];
                    if (soundUrl) {
                        const audio = new Audio(soundUrl);
                        audio.volume = 0.3;
                        audio.play().catch(error => {
                            console.warn('播放音效失败:', error);
                        });
                    }
                } catch (error) {
                    console.warn('音效播放错误:', error);
                }
            }
            
            // 记录事件
            logEvent(eventType, data) {
                if (!this.enhancedConfig.enableLogging) return;
                
                const logEntry = {
                    type: eventType,
                    timestamp: Date.now(),
                    data: data
                };
                
                this.logHistory.push(logEntry);
                console.log(`[ImportBlockUI] ${eventType}:`, data);
                
                // 限制日志历史大小
                if (this.logHistory.length > 100) {
                    this.logHistory = this.logHistory.slice(-50);
                }
            }
            
            // 触发事件
            triggerEvent(eventName, data) {
                try {
                    if (this.trigger) {
                        this.trigger(eventName, data);
                    }
                    
                    // 也可以触发DOM事件
                    if (this.el) {
                        this.el.dispatchEvent(new CustomEvent(`blockui-${eventName}`, {
                            detail: data,
                            bubbles: true
                        }));
                    }
                } catch (error) {
                    console.error('触发事件失败:', error);
                }
            }
            
            // 获取状态信息
            getState() {
                return {
                    ...this.state,
                    config: this.enhancedConfig,
                    logHistory: this.logHistory.slice(-10) // 最近10条日志
                };
            }
            
            // 清理资源
            cleanup() {
                this.stopTimers();
                
                if (this.keyboardHandler) {
                    document.removeEventListener('keydown', this.keyboardHandler);
                }
                
                this.logHistory = [];
            }
            
            // 组件销毁时清理
            willDestroy() {
                this.cleanup();
                super.willDestroy && super.willDestroy();
            }
        }
        
        // 导出增强的组件
        __exports.EnhancedImportBlockUI = EnhancedImportBlockUI;
    }
};

// 应用导入阻塞界面增强
ImportBlockUIEnhancer.enhanceImportBlockUI();
```

## 技术特点

### 1. 简洁设计
- 最小化的组件实现
- 清晰的属性定义
- 专用的模板系统

### 2. 可配置性
- 可选的消息显示
- 灵活的组件配置
- 可扩展的属性系统

### 3. OWL集成
- 基于OWL框架构建
- 标准的组件生命周期
- 响应式的数据绑定

### 4. 用户体验
- 防止误操作
- 清晰的状态提示
- 一致的界面风格

## 设计模式

### 1. 组件模式 (Component Pattern)
- 封装的UI组件
- 可重用的界面元素

### 2. 配置模式 (Configuration Pattern)
- 通过属性配置行为
- 灵活的定制选项

### 3. 模板模式 (Template Pattern)
- 分离逻辑和视图
- 可定制的界面模板

## 注意事项

1. **用户体验**: 避免长时间阻塞用户界面
2. **性能影响**: 确保阻塞界面不影响系统性能
3. **可访问性**: 提供适当的可访问性支持
4. **响应式设计**: 适配不同设备和屏幕尺寸

## 扩展建议

1. **进度显示**: 添加进度条和百分比显示
2. **取消功能**: 支持用户取消长时间操作
3. **动画效果**: 添加平滑的显示和隐藏动画
4. **主题支持**: 支持不同的视觉主题
5. **键盘支持**: 添加键盘快捷键支持

该导入阻塞界面组件为基础导入系统提供了重要的用户界面保护功能，是数据导入过程中用户体验和操作安全的核心组件。

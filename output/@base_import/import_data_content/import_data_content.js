/*********************************************************************************
*  Filepath: /base_import/static/src/import_data_content/import_data_content.js  *
*  Lines: 100                                                                    *
*********************************************************************************/
odoo.define('@base_import/import_data_content/import_data_content', ['@odoo/owl', '@web/core/select_menu/select_menu', '@base_import/import_data_column_error/import_data_column_error', '@base_import/import_data_options/import_data_options', '@web/core/l10n/translation'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { Component } = require("@odoo/owl");
const { SelectMenu } = require("@web/core/select_menu/select_menu");
const { ImportDataColumnError } = require("@base_import/import_data_column_error/import_data_column_error");
const { ImportDataOptions } = require("@base_import/import_data_options/import_data_options");
const { _t } = require("@web/core/l10n/translation");

const ImportDataContent = __exports.ImportDataContent = class ImportDataContent extends Component {
    static template = "ImportDataContent";
    static components = {
        ImportDataColumnError,
        ImportDataOptions,
        SelectMenu,
    };
    static props = {
        columns: { type: Array },
        isFieldSet: { type: Function },
        onOptionChanged: { type: Function },
        onFieldChanged: { type: Function },
        options: { type: Object },
        importMessages: { type: Object },
        previewError: { type: String, optional: true },
    };

    getGroups(column) {
        const groups = [
            { choices: this.makeChoices(column.fields.basic) },
            { choices: this.makeChoices(column.fields.suggested), label: _t("Suggested Fields") },
            {
                choices: this.makeChoices(column.fields.additional),
                label:
                    column.fields.suggested.length > 0
                        ? _t("Additional Fields")
                        : _t("Standard Fields"),
            },
            { choices: this.makeChoices(column.fields.relational), label: _t("Relation Fields") },
        ];
        return groups;
    }

    makeChoices(fields) {
        return fields.map((field) => ({
            label: field.label,
            value: field.fieldPath,
            iconClass: `o_import_field_icon_${field.type}`,
        }));
    }

    getTooltipDetails(field) {
        return JSON.stringify({
            resModel: field.model_name,
            debug: true,
            field: {
                name: field.name,
                label: field.string,
                type: field.type,
            },
        });
    }

    getTooltip(column) {
        const displayCount = 5;
        if (column.previews.length > displayCount) {
            return JSON.stringify({
                lines: [
                    ...column.previews.slice(0, displayCount - 1),
                    `(+${column.previews.length - displayCount + 1})`,
                ],
            });
        } else {
            return JSON.stringify({ lines: column.previews.slice(0, displayCount) });
        }
    }

    getErrorMessageClass(messages, type, index) {
        return `alert alert-${type} m-0 p-2 ${index === messages.length - 1 ? "" : "mb-2"}`;
    }

    getCommentClass(column, comment, index) {
        return `alert-${comment.type} ${index < column.comments.length - 1 ? "mb-2" : "mb-0"}`;
    }

    onFieldChanged(column, fieldPath) {
        const fields = [
            ...column.fields.basic,
            ...column.fields.suggested,
            ...column.fields.additional,
            ...column.fields.relational,
        ];
        const fieldInfo = fields.find((f) => f.fieldPath === fieldPath);
        this.props.onFieldChanged(column, fieldInfo);
    }
}

return __exports;
});
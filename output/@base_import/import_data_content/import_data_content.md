# Import Data Content - 导入数据内容组件

## 概述

`import_data_content.js` 是 Odoo 基础导入模块的数据内容显示组件，专门用于展示导入数据的预览和字段映射界面。该组件基于OWL框架和Web核心组件，集成了字段选择、数据预览、错误显示等核心功能，为基础导入系统提供了完整的数据内容展示和字段映射界面，是数据导入系统的核心展示组件。

## 文件信息
- **路径**: `/base_import/static/src/import_data_content/import_data_content.js`
- **行数**: 100
- **模块**: `@base_import/import_data_content/import_data_content`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                                         // OWL框架
'@web/core/select_menu/select_menu'                                // 选择菜单
'@base_import/import_data_column_error/import_data_column_error'   // 导入数据列错误
'@base_import/import_data_options/import_data_options'             // 导入数据选项
'@web/core/l10n/translation'                                       // 国际化翻译
```

## 核心功能

### 1. ImportDataContent 组件

```javascript
const ImportDataContent = class ImportDataContent extends Component {
    static template = "ImportDataContent";
    static components = {
        ImportDataColumnError,
        ImportDataOptions,
        SelectMenu,
    };
    static props = {
        columns: { type: Array },
        isFieldSet: { type: Function },
        onOptionChanged: { type: Function },
        onFieldChanged: { type: Function },
        options: { type: Object },
        importMessages: { type: Object },
        previewError: { type: String, optional: true },
    };
}
```

**组件特性**:
- **继承扩展**: 继承OWL Component的所有功能
- **子组件集成**: 集成列错误、数据选项、选择菜单等子组件
- **属性配置**: 丰富的属性配置支持数据展示和交互
- **模板绑定**: 使用专门的数据内容模板

### 2. 字段分组

```javascript
getGroups(column) {
    const groups = [
        { choices: this.makeChoices(column.fields.basic) },
        { choices: this.makeChoices(column.fields.suggested), label: _t("Suggested Fields") },
        {
            choices: this.makeChoices(column.fields.additional),
            label:
                column.fields.suggested.length > 0
                    ? _t("Additional Fields")
                    : _t("Standard Fields"),
        },
        { choices: this.makeChoices(column.fields.relational), label: _t("Relation Fields") },
    ];
    return groups;
}
```

**字段分组功能**:
- **基础字段**: 基本的字段选项
- **建议字段**: 系统建议的字段映射
- **附加字段**: 额外的可选字段
- **关系字段**: 关联关系字段
- **动态标签**: 根据字段数量动态调整标签

### 3. 选择项生成

```javascript
makeChoices(fields) {
    return fields.map((field) => ({
        value: field,
        label: field.label,
    }));
}
```

**选择项功能**:
- **字段映射**: 将字段对象映射为选择项
- **标签显示**: 使用字段标签作为显示文本
- **值绑定**: 绑定字段对象作为选择值
- **统一格式**: 提供统一的选择项格式

### 4. 字段设置检查

```javascript
isFieldSet(column) {
    return this.props.isFieldSet(column);
}
```

**字段检查功能**:
- **状态检查**: 检查字段是否已设置
- **委托处理**: 委托给父组件处理
- **状态同步**: 保持字段状态的同步
- **条件渲染**: 支持条件性的界面渲染

### 5. 事件处理

```javascript
onFieldChanged(column, field) {
    this.props.onFieldChanged(column, field);
}

onOptionChanged(option, value) {
    this.props.onOptionChanged(option, value);
}
```

**事件处理功能**:
- **字段变更**: 处理字段选择变更事件
- **选项变更**: 处理导入选项变更事件
- **事件委托**: 将事件委托给父组件处理
- **数据同步**: 保持数据状态的同步

## 使用场景

### 1. 导入数据内容组件增强

```javascript
// 导入数据内容组件增强功能
const ImportDataContentEnhancer = {
    enhanceImportDataContent: () => {
        // 增强的导入数据内容组件
        class EnhancedImportDataContent extends ImportDataContent {
            static props = {
                ...ImportDataContent.props,
                enableAdvancedPreview: { type: Boolean, optional: true },
                enableFieldSearch: { type: Boolean, optional: true },
                enableBulkMapping: { type: Boolean, optional: true },
                enableAutoMapping: { type: Boolean, optional: true },
                enableFieldValidation: { type: Boolean, optional: true },
                maxPreviewRows: { type: Number, optional: true },
                enableColumnResize: { type: Boolean, optional: true },
                enableColumnSort: { type: Boolean, optional: true },
                enableDataFilter: { type: Boolean, optional: true },
                customFieldGroups: { type: Array, optional: true }
            };
            
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    enableAdvancedPreview: this.props.enableAdvancedPreview || true,
                    enableFieldSearch: this.props.enableFieldSearch || true,
                    enableBulkMapping: this.props.enableBulkMapping || true,
                    enableAutoMapping: this.props.enableAutoMapping || true,
                    enableFieldValidation: this.props.enableFieldValidation || true,
                    maxPreviewRows: this.props.maxPreviewRows || 50,
                    enableColumnResize: this.props.enableColumnResize || true,
                    enableColumnSort: this.props.enableColumnSort || true,
                    enableDataFilter: this.props.enableDataFilter || true,
                    enableSmartSuggestions: true,
                    enableFieldHistory: true,
                    enableMappingTemplates: true,
                    enableDataValidation: true,
                    enableExportMapping: true
                };
                
                // 增强的状态
                this.enhancedState = useState({
                    searchTerm: '',
                    selectedColumns: [],
                    fieldHistory: new Map(),
                    mappingTemplates: [],
                    validationResults: new Map(),
                    sortColumn: null,
                    sortDirection: 'asc',
                    filterCriteria: {},
                    isAutoMapping: false,
                    previewMode: 'table' // 'table', 'grid', 'list'
                });
                
                // 字段搜索索引
                this.fieldSearchIndex = new Map();
                
                // 映射历史管理器
                this.mappingHistoryManager = new MappingHistoryManager();
                
                // 自动映射引擎
                this.autoMappingEngine = new AutoMappingEngine();
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 构建字段搜索索引
                if (this.enhancedConfig.enableFieldSearch) {
                    this.buildFieldSearchIndex();
                }
                
                // 加载映射历史
                if (this.enhancedConfig.enableFieldHistory) {
                    this.loadMappingHistory();
                }
                
                // 加载映射模板
                if (this.enhancedConfig.enableMappingTemplates) {
                    this.loadMappingTemplates();
                }
                
                // 执行自动映射
                if (this.enhancedConfig.enableAutoMapping) {
                    this.performAutoMapping();
                }
            }
            
            // 增强的字段分组
            getGroups(column) {
                let groups = super.getGroups(column);
                
                // 应用搜索过滤
                if (this.enhancedState.searchTerm) {
                    groups = this.filterGroupsBySearch(groups, this.enhancedState.searchTerm);
                }
                
                // 添加自定义分组
                if (this.props.customFieldGroups) {
                    groups = [...groups, ...this.getCustomGroups(column)];
                }
                
                // 添加历史分组
                if (this.enhancedConfig.enableFieldHistory) {
                    const historyGroup = this.getHistoryGroup(column);
                    if (historyGroup.choices.length > 0) {
                        groups.unshift(historyGroup);
                    }
                }
                
                // 添加智能建议分组
                if (this.enhancedConfig.enableSmartSuggestions) {
                    const smartGroup = this.getSmartSuggestionsGroup(column);
                    if (smartGroup.choices.length > 0) {
                        groups.splice(1, 0, smartGroup);
                    }
                }
                
                return groups;
            }
            
            // 按搜索过滤分组
            filterGroupsBySearch(groups, searchTerm) {
                const term = searchTerm.toLowerCase();
                return groups.map(group => ({
                    ...group,
                    choices: group.choices.filter(choice => 
                        choice.label.toLowerCase().includes(term) ||
                        choice.value.name.toLowerCase().includes(term)
                    )
                })).filter(group => group.choices.length > 0);
            }
            
            // 获取自定义分组
            getCustomGroups(column) {
                return this.props.customFieldGroups.map(group => ({
                    label: group.label,
                    choices: this.makeChoices(group.fields.filter(field => 
                        this.isFieldApplicable(field, column)
                    ))
                }));
            }
            
            // 获取历史分组
            getHistoryGroup(column) {
                const historyFields = this.getHistoryFields(column);
                return {
                    label: _t("Recently Used"),
                    choices: this.makeChoices(historyFields)
                };
            }
            
            // 获取智能建议分组
            getSmartSuggestionsGroup(column) {
                const suggestions = this.getSmartSuggestions(column);
                return {
                    label: _t("Smart Suggestions"),
                    choices: this.makeChoices(suggestions)
                };
            }
            
            // 获取智能建议
            getSmartSuggestions(column) {
                // 基于列名、数据类型、内容模式等进行智能建议
                const suggestions = [];
                
                // 名称匹配
                const nameMatches = this.findFieldsByName(column.name);
                suggestions.push(...nameMatches);
                
                // 数据类型匹配
                const typeMatches = this.findFieldsByDataType(column);
                suggestions.push(...typeMatches);
                
                // 内容模式匹配
                const patternMatches = this.findFieldsByPattern(column);
                suggestions.push(...patternMatches);
                
                // 去重并排序
                return this.deduplicateAndSort(suggestions);
            }
            
            // 按名称查找字段
            findFieldsByName(columnName) {
                const name = columnName.toLowerCase();
                const allFields = this.getAllAvailableFields();
                
                return allFields.filter(field => {
                    const fieldName = field.name.toLowerCase();
                    const fieldLabel = field.label.toLowerCase();
                    
                    // 精确匹配
                    if (fieldName === name || fieldLabel === name) {
                        return true;
                    }
                    
                    // 包含匹配
                    if (fieldName.includes(name) || fieldLabel.includes(name) || name.includes(fieldName)) {
                        return true;
                    }
                    
                    // 相似度匹配
                    if (this.calculateSimilarity(name, fieldName) > 0.7 || 
                        this.calculateSimilarity(name, fieldLabel) > 0.7) {
                        return true;
                    }
                    
                    return false;
                });
            }
            
            // 按数据类型查找字段
            findFieldsByDataType(column) {
                const dataType = this.detectDataType(column);
                const allFields = this.getAllAvailableFields();
                
                return allFields.filter(field => {
                    return this.isDataTypeCompatible(dataType, field.type);
                });
            }
            
            // 检测数据类型
            detectDataType(column) {
                const samples = column.preview.slice(0, 10);
                const types = {
                    integer: 0,
                    float: 0,
                    boolean: 0,
                    date: 0,
                    datetime: 0,
                    email: 0,
                    url: 0,
                    text: 0
                };
                
                for (const sample of samples) {
                    if (!sample) continue;
                    
                    const str = sample.toString().trim();
                    
                    if (/^\d+$/.test(str)) {
                        types.integer++;
                    } else if (/^\d*\.\d+$/.test(str)) {
                        types.float++;
                    } else if (/^(true|false|yes|no|1|0)$/i.test(str)) {
                        types.boolean++;
                    } else if (/^\d{4}-\d{2}-\d{2}$/.test(str)) {
                        types.date++;
                    } else if (/^\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}/.test(str)) {
                        types.datetime++;
                    } else if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(str)) {
                        types.email++;
                    } else if (/^https?:\/\//.test(str)) {
                        types.url++;
                    } else {
                        types.text++;
                    }
                }
                
                // 返回最匹配的类型
                return Object.keys(types).reduce((a, b) => types[a] > types[b] ? a : b);
            }
            
            // 检查数据类型兼容性
            isDataTypeCompatible(detectedType, fieldType) {
                const compatibility = {
                    'integer': ['integer', 'float', 'monetary'],
                    'float': ['float', 'monetary'],
                    'boolean': ['boolean'],
                    'date': ['date', 'datetime'],
                    'datetime': ['datetime'],
                    'email': ['char', 'text'],
                    'url': ['char', 'text'],
                    'text': ['char', 'text', 'html']
                };
                
                return compatibility[detectedType]?.includes(fieldType) || false;
            }
            
            // 增强的字段变更处理
            onFieldChanged(column, field) {
                // 记录到历史
                if (this.enhancedConfig.enableFieldHistory && field) {
                    this.addToMappingHistory(column, field);
                }
                
                // 执行验证
                if (this.enhancedConfig.enableFieldValidation) {
                    this.validateFieldMapping(column, field);
                }
                
                // 调用原有处理
                super.onFieldChanged(column, field);
                
                // 触发自动映射建议
                if (this.enhancedConfig.enableSmartSuggestions) {
                    this.updateSmartSuggestions();
                }
            }
            
            // 批量映射
            async performBulkMapping(mappings) {
                if (!this.enhancedConfig.enableBulkMapping) return;
                
                try {
                    for (const mapping of mappings) {
                        const { column, field } = mapping;
                        this.onFieldChanged(column, field);
                    }
                    
                    // 显示成功消息
                    this.showNotification('批量映射完成', 'success');
                } catch (error) {
                    console.error('批量映射失败:', error);
                    this.showNotification('批量映射失败', 'error');
                }
            }
            
            // 自动映射
            async performAutoMapping() {
                if (!this.enhancedConfig.enableAutoMapping) return;
                
                this.enhancedState.isAutoMapping = true;
                
                try {
                    const mappings = [];
                    
                    for (const column of this.props.columns) {
                        if (this.isFieldSet(column)) continue;
                        
                        const suggestions = this.getSmartSuggestions(column);
                        if (suggestions.length > 0) {
                            // 选择最佳建议
                            const bestSuggestion = suggestions[0];
                            mappings.push({ column, field: bestSuggestion });
                        }
                    }
                    
                    if (mappings.length > 0) {
                        await this.performBulkMapping(mappings);
                        this.showNotification(`自动映射了 ${mappings.length} 个字段`, 'success');
                    }
                } catch (error) {
                    console.error('自动映射失败:', error);
                    this.showNotification('自动映射失败', 'error');
                } finally {
                    this.enhancedState.isAutoMapping = false;
                }
            }
            
            // 验证字段映射
            validateFieldMapping(column, field) {
                if (!field) return;
                
                const validationResult = {
                    isValid: true,
                    warnings: [],
                    errors: []
                };
                
                // 数据类型验证
                const detectedType = this.detectDataType(column);
                if (!this.isDataTypeCompatible(detectedType, field.type)) {
                    validationResult.warnings.push(
                        `数据类型可能不匹配: 检测到 ${detectedType}，字段类型为 ${field.type}`
                    );
                }
                
                // 必填字段验证
                if (field.required && this.hasEmptyValues(column)) {
                    validationResult.errors.push('必填字段包含空值');
                    validationResult.isValid = false;
                }
                
                // 唯一性验证
                if (field.unique && this.hasDuplicateValues(column)) {
                    validationResult.errors.push('唯一字段包含重复值');
                    validationResult.isValid = false;
                }
                
                this.enhancedState.validationResults.set(column.name, validationResult);
            }
            
            // 检查空值
            hasEmptyValues(column) {
                return column.preview.some(value => !value || value.toString().trim() === '');
            }
            
            // 检查重复值
            hasDuplicateValues(column) {
                const values = column.preview.filter(value => value && value.toString().trim() !== '');
                return new Set(values).size !== values.length;
            }
            
            // 工具方法
            getAllAvailableFields() {
                const allFields = [];
                for (const column of this.props.columns) {
                    allFields.push(...column.fields.basic);
                    allFields.push(...column.fields.suggested);
                    allFields.push(...column.fields.additional);
                    allFields.push(...column.fields.relational);
                }
                return [...new Set(allFields)]; // 去重
            }
            
            calculateSimilarity(str1, str2) {
                // 简单的字符串相似度计算
                const longer = str1.length > str2.length ? str1 : str2;
                const shorter = str1.length > str2.length ? str2 : str1;
                
                if (longer.length === 0) return 1.0;
                
                const editDistance = this.levenshteinDistance(longer, shorter);
                return (longer.length - editDistance) / longer.length;
            }
            
            levenshteinDistance(str1, str2) {
                const matrix = [];
                
                for (let i = 0; i <= str2.length; i++) {
                    matrix[i] = [i];
                }
                
                for (let j = 0; j <= str1.length; j++) {
                    matrix[0][j] = j;
                }
                
                for (let i = 1; i <= str2.length; i++) {
                    for (let j = 1; j <= str1.length; j++) {
                        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                            matrix[i][j] = matrix[i - 1][j - 1];
                        } else {
                            matrix[i][j] = Math.min(
                                matrix[i - 1][j - 1] + 1,
                                matrix[i][j - 1] + 1,
                                matrix[i - 1][j] + 1
                            );
                        }
                    }
                }
                
                return matrix[str2.length][str1.length];
            }
            
            showNotification(message, type) {
                // 显示通知消息
                console.log(`[${type}] ${message}`);
            }
        }
        
        // 映射历史管理器
        class MappingHistoryManager {
            constructor() {
                this.history = new Map();
            }
            
            add(column, field) {
                // 添加映射历史
            }
            
            get(column) {
                // 获取列的映射历史
            }
            
            clear() {
                // 清空历史
            }
        }
        
        // 自动映射引擎
        class AutoMappingEngine {
            constructor() {
                this.rules = [];
            }
            
            addRule(rule) {
                // 添加映射规则
            }
            
            suggest(column) {
                // 生成映射建议
            }
        }
        
        // 导出增强的组件
        __exports.EnhancedImportDataContent = EnhancedImportDataContent;
    }
};

// 应用导入数据内容组件增强
ImportDataContentEnhancer.enhanceImportDataContent();
```

## 技术特点

### 1. 数据展示
- 清晰的数据预览界面
- 分组的字段选择
- 直观的映射关系显示

### 2. 字段管理
- 智能的字段分组
- 灵活的字段选择
- 动态的选项生成

### 3. 交互设计
- 响应式的用户界面
- 直观的操作反馈
- 流畅的交互体验

### 4. 组件集成
- 模块化的子组件
- 统一的数据流
- 清晰的职责分离

## 设计模式

### 1. 组合模式 (Composite Pattern)
- 组合多个子组件提供完整功能
- 统一的组件接口

### 2. 委托模式 (Delegation Pattern)
- 事件处理的委托机制
- 职责的合理分配

### 3. 策略模式 (Strategy Pattern)
- 不同的字段分组策略
- 可配置的显示选项

## 注意事项

1. **性能优化**: 注意大量数据时的渲染性能
2. **用户体验**: 提供清晰的字段选择和映射界面
3. **数据同步**: 确保字段映射状态的正确同步
4. **可访问性**: 提供适当的可访问性支持

## 扩展建议

1. **智能映射**: 实现基于AI的智能字段映射
2. **搜索功能**: 添加字段搜索和过滤功能
3. **批量操作**: 支持批量字段映射操作
4. **映射模板**: 实现字段映射模板的保存和复用
5. **数据验证**: 添加实时的数据验证和提示

该导入数据内容组件为基础导入系统提供了重要的数据展示和字段映射功能，是数据导入系统的核心展示组件。

# IAP Action Buttons Widget - IAP操作按钮组件

## 概述

`action_buttons_widget.js` 是 Odoo IAP (In-App Purchase) 模块的操作按钮组件，专门用于在各种视图中提供IAP服务相关的操作按钮。该组件基于OWL框架和Web核心服务，集成了服务查看、账户管理等核心功能，为IAP系统提供了完整的用户界面操作支持，是IAP服务管理的重要交互组件。

## 文件信息
- **路径**: `/iap/static/src/action_buttons_widget/action_buttons_widget.js`
- **行数**: 51
- **模块**: `@iap/action_buttons_widget/action_buttons_widget`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                        // 注册表
'@web/core/utils/hooks'                     // 钩子工具
'@web/views/widgets/standard_widget_props'  // 标准组件属性
'@odoo/owl'                                 // OWL框架
```

## 核心功能

### 1. IAPActionButtonsWidget 组件

```javascript
class IAPActionButtonsWidget extends Component {
    static template = "iap.ActionButtonsWidget";
    static props = {
        ...standardWidgetProps,
        serviceName: String,
        showServiceButtons: Boolean,
    };
}
```

**组件特性**:
- **继承扩展**: 继承OWL Component的所有功能
- **标准属性**: 使用标准组件属性作为基础
- **服务配置**: 支持服务名称和按钮显示控制
- **模板绑定**: 使用专门的IAP操作按钮模板

### 2. 组件初始化

```javascript
setup() {
    this.orm = useService("orm");
    this.action = useService("action");
}
```

**初始化功能**:
- **服务集成**: 集成ORM和操作服务
- **钩子使用**: 使用useService钩子获取服务
- **服务准备**: 为后续操作准备必要的服务
- **简洁设计**: 最小化的初始化配置

### 3. 查看服务操作

```javascript
async onViewServicesClicked() {
    this.action.doAction("iap.iap_account_action");
}
```

**查看服务功能**:
- **操作执行**: 执行预定义的IAP账户操作
- **异步处理**: 使用异步方法处理操作
- **操作标识**: 使用固定的操作标识符
- **用户导航**: 引导用户到IAP账户管理界面

### 4. 管理服务链接操作

```javascript
async onManageServiceLinkClicked() {
    const account_id = await this.orm.silent.call("iap.account", "get_account_id", [this.props.serviceName]);
    this.action.doAction({
        type: "ir.actions.act_window",
        res_model: "iap.account",
        res_id: account_id,
        views: [[false, "form"]],
    });
}
```

**管理服务功能**:
- **账户查询**: 根据服务名称查询对应的账户ID
- **静默调用**: 使用silent调用避免错误提示
- **动态操作**: 动态构建操作配置
- **表单视图**: 打开特定账户的表单视图进行管理

### 5. 组件注册

```javascript
const iapActionButtonsWidget = {
    component: IAPActionButtonsWidget,
    extractProps: ({ attrs }) => {
        return {
            serviceName: attrs.service_name,
            showServiceButtons: !Boolean(attrs.hide_service),
        };
    },
};
registry.category("view_widgets").add("iap_buy_more_credits", iapActionButtonsWidget);
```

**组件注册功能**:
- **组件配置**: 配置组件和属性提取器
- **属性映射**: 将XML属性映射为组件属性
- **布尔转换**: 智能的布尔值转换逻辑
- **注册表添加**: 将组件添加到视图组件注册表

## 使用场景

### 1. IAP操作按钮组件增强

```javascript
// IAP操作按钮组件增强功能
const IAPActionButtonsWidgetEnhancer = {
    enhanceIAPActionButtonsWidget: () => {
        // 增强的IAP操作按钮组件
        class EnhancedIAPActionButtonsWidget extends IAPActionButtonsWidget {
            static props = {
                ...IAPActionButtonsWidget.props,
                enableCreditDisplay: { type: Boolean, optional: true },
                enableQuickPurchase: { type: Boolean, optional: true },
                enableUsageHistory: { type: Boolean, optional: true },
                enableServiceStatus: { type: Boolean, optional: true },
                customActions: { type: Array, optional: true },
                theme: { type: String, optional: true },
                size: { type: String, optional: true },
                position: { type: String, optional: true }
            };
            
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    enableCreditDisplay: this.props.enableCreditDisplay || true,
                    enableQuickPurchase: this.props.enableQuickPurchase || true,
                    enableUsageHistory: this.props.enableUsageHistory || true,
                    enableServiceStatus: this.props.enableServiceStatus || true,
                    enableNotifications: true,
                    enableAutoRefresh: true,
                    enableCaching: true,
                    refreshInterval: 30000, // 30秒
                    cacheTimeout: 300000, // 5分钟
                    enableAnalytics: true
                };
                
                // 增强的状态
                this.enhancedState = useState({
                    creditBalance: 0,
                    serviceStatus: 'unknown',
                    usageHistory: [],
                    lastRefresh: null,
                    isLoading: false,
                    notifications: [],
                    quickPurchaseOptions: [],
                    customActionResults: {}
                });
                
                // 信用管理器
                this.creditManager = new CreditManager();
                
                // 服务状态监控器
                this.serviceMonitor = new ServiceStatusMonitor();
                
                // 使用历史管理器
                this.usageHistoryManager = new UsageHistoryManager();
                
                // 通知管理器
                this.notificationManager = new NotificationManager();
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 加载信用余额
                if (this.enhancedConfig.enableCreditDisplay) {
                    this.loadCreditBalance();
                }
                
                // 监控服务状态
                if (this.enhancedConfig.enableServiceStatus) {
                    this.startServiceMonitoring();
                }
                
                // 加载使用历史
                if (this.enhancedConfig.enableUsageHistory) {
                    this.loadUsageHistory();
                }
                
                // 设置自动刷新
                if (this.enhancedConfig.enableAutoRefresh) {
                    this.setupAutoRefresh();
                }
                
                // 加载快速购买选项
                if (this.enhancedConfig.enableQuickPurchase) {
                    this.loadQuickPurchaseOptions();
                }
            }
            
            // 加载信用余额
            async loadCreditBalance() {
                try {
                    this.enhancedState.isLoading = true;
                    const balance = await this.creditManager.getBalance(this.props.serviceName);
                    this.enhancedState.creditBalance = balance;
                    this.enhancedState.lastRefresh = Date.now();
                } catch (error) {
                    console.error('加载信用余额失败:', error);
                    this.showNotification('无法加载信用余额', 'error');
                } finally {
                    this.enhancedState.isLoading = false;
                }
            }
            
            // 开始服务监控
            startServiceMonitoring() {
                this.serviceMonitor.monitor(this.props.serviceName, (status) => {
                    this.enhancedState.serviceStatus = status;
                    this.handleServiceStatusChange(status);
                });
            }
            
            // 处理服务状态变化
            handleServiceStatusChange(status) {
                switch (status) {
                    case 'active':
                        this.showNotification('服务运行正常', 'success');
                        break;
                    case 'inactive':
                        this.showNotification('服务暂时不可用', 'warning');
                        break;
                    case 'error':
                        this.showNotification('服务出现错误', 'error');
                        break;
                    case 'maintenance':
                        this.showNotification('服务正在维护中', 'info');
                        break;
                }
            }
            
            // 加载使用历史
            async loadUsageHistory() {
                try {
                    const history = await this.usageHistoryManager.getHistory(
                        this.props.serviceName, 
                        { limit: 10 }
                    );
                    this.enhancedState.usageHistory = history;
                } catch (error) {
                    console.error('加载使用历史失败:', error);
                }
            }
            
            // 设置自动刷新
            setupAutoRefresh() {
                this.refreshTimer = setInterval(() => {
                    this.refreshData();
                }, this.enhancedConfig.refreshInterval);
            }
            
            // 刷新数据
            async refreshData() {
                if (this.enhancedState.isLoading) return;
                
                try {
                    await Promise.all([
                        this.loadCreditBalance(),
                        this.loadUsageHistory()
                    ]);
                } catch (error) {
                    console.error('刷新数据失败:', error);
                }
            }
            
            // 加载快速购买选项
            async loadQuickPurchaseOptions() {
                try {
                    const options = await this.orm.call(
                        'iap.account',
                        'get_quick_purchase_options',
                        [this.props.serviceName]
                    );
                    this.enhancedState.quickPurchaseOptions = options;
                } catch (error) {
                    console.error('加载快速购买选项失败:', error);
                }
            }
            
            // 增强的查看服务操作
            async onViewServicesClicked() {
                // 记录分析事件
                if (this.enhancedConfig.enableAnalytics) {
                    this.recordAnalyticsEvent('view_services_clicked');
                }
                
                // 执行原有操作
                await super.onViewServicesClicked();
            }
            
            // 增强的管理服务链接操作
            async onManageServiceLinkClicked() {
                try {
                    // 记录分析事件
                    if (this.enhancedConfig.enableAnalytics) {
                        this.recordAnalyticsEvent('manage_service_clicked');
                    }
                    
                    // 显示加载状态
                    this.enhancedState.isLoading = true;
                    
                    // 执行原有操作
                    await super.onManageServiceLinkClicked();
                    
                } catch (error) {
                    console.error('管理服务操作失败:', error);
                    this.showNotification('操作失败，请稍后重试', 'error');
                } finally {
                    this.enhancedState.isLoading = false;
                }
            }
            
            // 快速购买信用
            async onQuickPurchaseClicked(option) {
                if (!this.enhancedConfig.enableQuickPurchase) return;
                
                try {
                    this.enhancedState.isLoading = true;
                    
                    // 记录分析事件
                    if (this.enhancedConfig.enableAnalytics) {
                        this.recordAnalyticsEvent('quick_purchase_clicked', {
                            option: option.id,
                            amount: option.amount
                        });
                    }
                    
                    // 执行购买操作
                    const result = await this.orm.call(
                        'iap.account',
                        'quick_purchase_credits',
                        [this.props.serviceName, option.id]
                    );
                    
                    if (result.success) {
                        this.showNotification('购买成功！', 'success');
                        await this.loadCreditBalance(); // 刷新余额
                    } else {
                        this.showNotification(result.message || '购买失败', 'error');
                    }
                    
                } catch (error) {
                    console.error('快速购买失败:', error);
                    this.showNotification('购买失败，请稍后重试', 'error');
                } finally {
                    this.enhancedState.isLoading = false;
                }
            }
            
            // 查看使用历史
            onViewUsageHistoryClicked() {
                if (!this.enhancedConfig.enableUsageHistory) return;
                
                this.action.doAction({
                    type: "ir.actions.act_window",
                    name: "使用历史",
                    res_model: "iap.usage.history",
                    domain: [['service_name', '=', this.props.serviceName]],
                    views: [[false, "list"], [false, "form"]],
                    target: "new"
                });
            }
            
            // 执行自定义操作
            async executeCustomAction(action) {
                try {
                    this.enhancedState.isLoading = true;
                    
                    const result = await this.orm.call(
                        action.model || 'iap.account',
                        action.method,
                        action.args || [this.props.serviceName],
                        action.kwargs || {}
                    );
                    
                    this.enhancedState.customActionResults[action.id] = result;
                    
                    if (action.successMessage) {
                        this.showNotification(action.successMessage, 'success');
                    }
                    
                    // 如果需要刷新数据
                    if (action.refreshAfter) {
                        await this.refreshData();
                    }
                    
                } catch (error) {
                    console.error('执行自定义操作失败:', error);
                    this.showNotification(action.errorMessage || '操作失败', 'error');
                } finally {
                    this.enhancedState.isLoading = false;
                }
            }
            
            // 显示通知
            showNotification(message, type) {
                if (this.enhancedConfig.enableNotifications) {
                    this.notificationManager.show(message, type);
                }
            }
            
            // 记录分析事件
            recordAnalyticsEvent(eventName, data = {}) {
                if (this.enhancedConfig.enableAnalytics) {
                    this.orm.silent.call(
                        'iap.analytics',
                        'record_event',
                        [eventName, {
                            service_name: this.props.serviceName,
                            timestamp: Date.now(),
                            ...data
                        }]
                    );
                }
            }
            
            // 获取信用余额显示文本
            getCreditBalanceText() {
                if (this.enhancedState.isLoading) {
                    return '加载中...';
                }
                
                const balance = this.enhancedState.creditBalance;
                if (balance === 0) {
                    return '余额不足';
                } else if (balance < 10) {
                    return `余额较低 (${balance})`;
                } else {
                    return `余额: ${balance}`;
                }
            }
            
            // 获取服务状态显示文本
            getServiceStatusText() {
                const statusMap = {
                    'active': '正常',
                    'inactive': '不可用',
                    'error': '错误',
                    'maintenance': '维护中',
                    'unknown': '未知'
                };
                
                return statusMap[this.enhancedState.serviceStatus] || '未知';
            }
            
            // 获取服务状态样式类
            getServiceStatusClass() {
                const classMap = {
                    'active': 'text-success',
                    'inactive': 'text-warning',
                    'error': 'text-danger',
                    'maintenance': 'text-info',
                    'unknown': 'text-muted'
                };
                
                return classMap[this.enhancedState.serviceStatus] || 'text-muted';
            }
            
            // 组件销毁时清理
            willDestroy() {
                if (this.refreshTimer) {
                    clearInterval(this.refreshTimer);
                }
                
                if (this.serviceMonitor) {
                    this.serviceMonitor.stop();
                }
                
                super.willDestroy && super.willDestroy();
            }
        }
        
        // 信用管理器
        class CreditManager {
            async getBalance(serviceName) {
                // 实现获取信用余额的逻辑
                return 100; // 示例值
            }
            
            async purchaseCredits(serviceName, amount) {
                // 实现购买信用的逻辑
                return { success: true };
            }
        }
        
        // 服务状态监控器
        class ServiceStatusMonitor {
            constructor() {
                this.listeners = new Map();
                this.intervals = new Map();
            }
            
            monitor(serviceName, callback) {
                this.listeners.set(serviceName, callback);
                
                // 定期检查服务状态
                const interval = setInterval(() => {
                    this.checkServiceStatus(serviceName);
                }, 60000); // 每分钟检查一次
                
                this.intervals.set(serviceName, interval);
                
                // 立即检查一次
                this.checkServiceStatus(serviceName);
            }
            
            async checkServiceStatus(serviceName) {
                try {
                    // 这里应该实现实际的服务状态检查逻辑
                    const status = 'active'; // 示例状态
                    
                    const callback = this.listeners.get(serviceName);
                    if (callback) {
                        callback(status);
                    }
                } catch (error) {
                    console.error('检查服务状态失败:', error);
                }
            }
            
            stop() {
                for (const interval of this.intervals.values()) {
                    clearInterval(interval);
                }
                this.intervals.clear();
                this.listeners.clear();
            }
        }
        
        // 使用历史管理器
        class UsageHistoryManager {
            async getHistory(serviceName, options = {}) {
                // 实现获取使用历史的逻辑
                return []; // 示例数据
            }
        }
        
        // 通知管理器
        class NotificationManager {
            show(message, type) {
                // 实现通知显示逻辑
                console.log(`[${type}] ${message}`);
            }
        }
        
        // 增强的组件注册
        const enhancedIapActionButtonsWidget = {
            component: EnhancedIAPActionButtonsWidget,
            extractProps: ({ attrs }) => {
                return {
                    serviceName: attrs.service_name,
                    showServiceButtons: !Boolean(attrs.hide_service),
                    enableCreditDisplay: Boolean(attrs.enable_credit_display),
                    enableQuickPurchase: Boolean(attrs.enable_quick_purchase),
                    enableUsageHistory: Boolean(attrs.enable_usage_history),
                    enableServiceStatus: Boolean(attrs.enable_service_status),
                    theme: attrs.theme || 'default',
                    size: attrs.size || 'medium',
                    position: attrs.position || 'right'
                };
            },
        };
        
        // 导出增强的组件
        __exports.EnhancedIAPActionButtonsWidget = EnhancedIAPActionButtonsWidget;
        __exports.enhancedIapActionButtonsWidget = enhancedIapActionButtonsWidget;
    }
};

// 应用IAP操作按钮组件增强
IAPActionButtonsWidgetEnhancer.enhanceIAPActionButtonsWidget();
```

## 技术特点

### 1. 组件设计
- 基于OWL框架的现代化组件
- 标准的组件属性和生命周期
- 清晰的职责分离

### 2. 服务集成
- 深度集成ORM和操作服务
- 异步操作的优雅处理
- 错误处理和用户反馈

### 3. 属性映射
- 智能的XML属性到组件属性映射
- 灵活的配置选项
- 类型安全的属性处理

### 4. 操作执行
- 多种IAP相关操作的支持
- 动态操作配置
- 用户友好的操作流程

## 设计模式

### 1. 组件模式 (Component Pattern)
- 封装的UI组件
- 可重用的界面元素

### 2. 服务定位器模式 (Service Locator Pattern)
- 通过钩子获取服务
- 解耦组件和服务实现

### 3. 命令模式 (Command Pattern)
- 封装操作为命令
- 统一的操作执行接口

## 注意事项

1. **服务可用性**: 确保IAP服务的可用性和稳定性
2. **错误处理**: 提供完善的错误处理和用户反馈
3. **性能优化**: 优化组件渲染和操作执行性能
4. **用户体验**: 提供清晰的操作指引和状态反馈

## 扩展建议

1. **信用显示**: 添加实时的信用余额显示
2. **快速购买**: 提供快速购买信用的选项
3. **使用统计**: 显示服务使用统计和历史
4. **状态监控**: 实时监控服务状态和可用性
5. **自定义操作**: 支持自定义的IAP相关操作

该IAP操作按钮组件为IAP系统提供了重要的用户界面操作功能，是IAP服务管理和用户交互的核心组件。

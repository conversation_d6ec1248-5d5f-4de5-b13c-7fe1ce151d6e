# Insufficient Credit Error Handler - 信用不足错误处理器

## 概述

`insufficient_credit_error_handler.js` 是 Odoo IAP (In-App Purchase) 模块的信用不足错误处理器，专门用于处理IAP服务中的信用不足错误。该组件基于OWL框架和Web核心服务，集成了错误检测、对话框显示、购买引导等核心功能，为IAP系统提供了完整的错误处理和用户引导支持，是IAP服务错误管理的重要组件。

## 文件信息
- **路径**: `/iap/static/src/js/insufficient_credit_error_handler.js`
- **行数**: 65
- **模块**: `@iap/js/insufficient_credit_error_handler`

## 依赖关系

```javascript
// 核心依赖
'@web/core/dialog/dialog'       // 对话框组件
'@web/core/registry'            // 注册表
'@web/core/utils/hooks'         // 钩子工具
'@web/core/l10n/translation'    // 国际化翻译
'@odoo/owl'                     // OWL框架
```

## 核心功能

### 1. InsufficientCreditDialog 组件

```javascript
class InsufficientCreditDialog extends Component {
    static components = { Dialog };
    static template = "iap.InsufficientCreditDialog";
    static props = {
        errorData: Object,
        close: Function,
    };
}
```

**组件特性**:
- **继承扩展**: 继承OWL Component的所有功能
- **对话框集成**: 集成标准对话框组件
- **错误数据**: 接收错误数据对象作为属性
- **关闭回调**: 支持对话框关闭回调函数

### 2. 组件初始化

```javascript
setup() {
    this.orm = useService("orm");
    onWillStart(this.onWillStart);
}
```

**初始化功能**:
- **服务集成**: 集成ORM服务进行数据操作
- **生命周期钩子**: 使用onWillStart钩子进行异步初始化
- **服务准备**: 为后续操作准备必要的服务
- **异步处理**: 支持异步的组件初始化

### 3. 组件启动处理

```javascript
async onWillStart() {
    const { errorData } = this.props;
    this.url = await this.orm.call("iap.account", "get_credits_url", [], {
        base_url: errorData.base_url,
        service_name: errorData.service_name,
        credit: errorData.credit,
        trial: errorData.trial,
    });
    this.style = errorData.body ? "padding:0;" : "";
    const { isEnterprise } = odoo.info;
    if (errorData.trial && isEnterprise) {
        this.buttonMessage = _t("Start a Trial at Odoo");
    } else {
        this.buttonMessage = _t("Buy credits");
    }
}
```

**启动处理功能**:
- **URL获取**: 异步获取购买信用的URL
- **参数传递**: 传递完整的错误数据参数
- **样式设置**: 根据错误数据设置对话框样式
- **版本检测**: 检测Odoo版本类型（企业版/社区版）
- **按钮文本**: 根据试用状态和版本设置按钮文本
- **国际化**: 使用翻译函数支持多语言

### 4. 购买信用操作

```javascript
buyCredits() {
    window.open(this.url, "_blank");
    this.props.close();
}
```

**购买操作功能**:
- **新窗口打开**: 在新标签页中打开购买页面
- **URL跳转**: 跳转到预先获取的购买URL
- **对话框关闭**: 操作完成后关闭对话框
- **用户引导**: 引导用户到购买页面

### 5. 错误处理器

```javascript
function insufficientCreditHandler(env, error, originalError) {
    if (!originalError) {
        return false;
    }
    const { data } = originalError;
    if (data && data.name === "odoo.addons.iap.tools.iap_tools.InsufficientCreditError") {
        env.services.dialog.add(InsufficientCreditDialog, {
            errorData: JSON.parse(data.message),
        });
        return true;
    }
    return false;
}
```

**错误处理功能**:
- **错误检测**: 检测特定的信用不足错误类型
- **错误验证**: 验证错误对象和数据的有效性
- **错误匹配**: 匹配特定的IAP信用不足错误
- **对话框显示**: 显示信用不足对话框
- **数据解析**: 解析错误消息中的JSON数据
- **处理标识**: 返回处理结果标识

### 6. 错误处理器注册

```javascript
registry
    .category("error_handlers")
    .add("insufficientCreditHandler", insufficientCreditHandler, { sequence: 0 });
```

**注册功能**:
- **注册表添加**: 将错误处理器添加到错误处理器注册表
- **优先级设置**: 设置处理器的执行优先级（sequence: 0）
- **全局可用**: 使错误处理器在整个系统中可用
- **自动处理**: 自动处理匹配的错误类型

## 使用场景

### 1. 信用不足错误处理器增强

```javascript
// 信用不足错误处理器增强功能
const InsufficientCreditErrorHandlerEnhancer = {
    enhanceInsufficientCreditErrorHandler: () => {
        // 增强的信用不足对话框组件
        class EnhancedInsufficientCreditDialog extends InsufficientCreditDialog {
            static props = {
                ...InsufficientCreditDialog.props,
                enableAlternativeOptions: { type: Boolean, optional: true },
                enableUsageAnalytics: { type: Boolean, optional: true },
                enableContactSupport: { type: Boolean, optional: true },
                enableTrialExtension: { type: Boolean, optional: true },
                customActions: { type: Array, optional: true }
            };
            
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    enableAlternativeOptions: this.props.enableAlternativeOptions || true,
                    enableUsageAnalytics: this.props.enableUsageAnalytics || true,
                    enableContactSupport: this.props.enableContactSupport || true,
                    enableTrialExtension: this.props.enableTrialExtension || true,
                    enableNotifications: true,
                    enableRetryMechanism: true,
                    enableCreditPrediction: true,
                    enableUsageOptimization: true
                };
                
                // 增强的状态
                this.enhancedState = useState({
                    alternativeOptions: [],
                    usageAnalytics: {},
                    supportOptions: [],
                    trialExtensionAvailable: false,
                    creditPrediction: {},
                    optimizationSuggestions: [],
                    isProcessing: false
                });
                
                // 使用分析器
                this.usageAnalyzer = new UsageAnalyzer();
                
                // 信用预测器
                this.creditPredictor = new CreditPredictor();
                
                // 优化建议引擎
                this.optimizationEngine = new OptimizationEngine();
                
                // 支持服务
                this.supportService = new SupportService();
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 加载替代选项
                if (this.enhancedConfig.enableAlternativeOptions) {
                    this.loadAlternativeOptions();
                }
                
                // 分析使用情况
                if (this.enhancedConfig.enableUsageAnalytics) {
                    this.analyzeUsage();
                }
                
                // 检查试用扩展
                if (this.enhancedConfig.enableTrialExtension) {
                    this.checkTrialExtension();
                }
                
                // 生成信用预测
                if (this.enhancedConfig.enableCreditPrediction) {
                    this.generateCreditPrediction();
                }
                
                // 生成优化建议
                if (this.enhancedConfig.enableUsageOptimization) {
                    this.generateOptimizationSuggestions();
                }
            }
            
            // 增强的组件启动处理
            async onWillStart() {
                await super.onWillStart();
                
                // 加载支持选项
                if (this.enhancedConfig.enableContactSupport) {
                    await this.loadSupportOptions();
                }
            }
            
            // 加载替代选项
            async loadAlternativeOptions() {
                try {
                    const options = await this.orm.call(
                        'iap.account',
                        'get_alternative_options',
                        [this.props.errorData.service_name]
                    );
                    this.enhancedState.alternativeOptions = options;
                } catch (error) {
                    console.error('加载替代选项失败:', error);
                }
            }
            
            // 分析使用情况
            async analyzeUsage() {
                try {
                    const analytics = await this.usageAnalyzer.analyze(
                        this.props.errorData.service_name
                    );
                    this.enhancedState.usageAnalytics = analytics;
                } catch (error) {
                    console.error('分析使用情况失败:', error);
                }
            }
            
            // 检查试用扩展
            async checkTrialExtension() {
                try {
                    const available = await this.orm.call(
                        'iap.account',
                        'check_trial_extension_available',
                        [this.props.errorData.service_name]
                    );
                    this.enhancedState.trialExtensionAvailable = available;
                } catch (error) {
                    console.error('检查试用扩展失败:', error);
                }
            }
            
            // 生成信用预测
            async generateCreditPrediction() {
                try {
                    const prediction = await this.creditPredictor.predict(
                        this.props.errorData.service_name,
                        this.enhancedState.usageAnalytics
                    );
                    this.enhancedState.creditPrediction = prediction;
                } catch (error) {
                    console.error('生成信用预测失败:', error);
                }
            }
            
            // 生成优化建议
            async generateOptimizationSuggestions() {
                try {
                    const suggestions = await this.optimizationEngine.generateSuggestions(
                        this.props.errorData.service_name,
                        this.enhancedState.usageAnalytics
                    );
                    this.enhancedState.optimizationSuggestions = suggestions;
                } catch (error) {
                    console.error('生成优化建议失败:', error);
                }
            }
            
            // 加载支持选项
            async loadSupportOptions() {
                try {
                    const options = await this.supportService.getSupportOptions(
                        this.props.errorData.service_name
                    );
                    this.enhancedState.supportOptions = options;
                } catch (error) {
                    console.error('加载支持选项失败:', error);
                }
            }
            
            // 增强的购买信用操作
            async buyCredits() {
                try {
                    this.enhancedState.isProcessing = true;
                    
                    // 记录购买意图
                    await this.recordPurchaseIntent();
                    
                    // 执行原有购买逻辑
                    super.buyCredits();
                    
                } catch (error) {
                    console.error('购买信用操作失败:', error);
                } finally {
                    this.enhancedState.isProcessing = false;
                }
            }
            
            // 申请试用扩展
            async requestTrialExtension() {
                if (!this.enhancedState.trialExtensionAvailable) return;
                
                try {
                    this.enhancedState.isProcessing = true;
                    
                    const result = await this.orm.call(
                        'iap.account',
                        'request_trial_extension',
                        [this.props.errorData.service_name]
                    );
                    
                    if (result.success) {
                        this.showNotification('试用扩展申请成功！', 'success');
                        this.props.close();
                    } else {
                        this.showNotification(result.message || '申请失败', 'error');
                    }
                    
                } catch (error) {
                    console.error('申请试用扩展失败:', error);
                    this.showNotification('申请失败，请稍后重试', 'error');
                } finally {
                    this.enhancedState.isProcessing = false;
                }
            }
            
            // 联系支持
            async contactSupport(supportType) {
                try {
                    this.enhancedState.isProcessing = true;
                    
                    const result = await this.supportService.createSupportTicket({
                        service_name: this.props.errorData.service_name,
                        issue_type: 'insufficient_credit',
                        support_type: supportType,
                        error_data: this.props.errorData,
                        usage_analytics: this.enhancedState.usageAnalytics
                    });
                    
                    if (result.success) {
                        this.showNotification('支持请求已提交', 'success');
                        this.props.close();
                    } else {
                        this.showNotification('提交失败，请稍后重试', 'error');
                    }
                    
                } catch (error) {
                    console.error('联系支持失败:', error);
                    this.showNotification('提交失败，请稍后重试', 'error');
                } finally {
                    this.enhancedState.isProcessing = false;
                }
            }
            
            // 应用优化建议
            async applyOptimization(suggestion) {
                try {
                    this.enhancedState.isProcessing = true;
                    
                    const result = await this.optimizationEngine.applyOptimization(
                        this.props.errorData.service_name,
                        suggestion
                    );
                    
                    if (result.success) {
                        this.showNotification('优化设置已应用', 'success');
                        // 可能需要刷新或重试操作
                    } else {
                        this.showNotification('应用优化失败', 'error');
                    }
                    
                } catch (error) {
                    console.error('应用优化失败:', error);
                    this.showNotification('应用优化失败', 'error');
                } finally {
                    this.enhancedState.isProcessing = false;
                }
            }
            
            // 重试原始操作
            async retryOriginalOperation() {
                if (!this.enhancedConfig.enableRetryMechanism) return;
                
                try {
                    this.enhancedState.isProcessing = true;
                    
                    // 这里需要重试导致信用不足错误的原始操作
                    // 具体实现取决于错误上下文
                    
                    this.showNotification('正在重试操作...', 'info');
                    this.props.close();
                    
                } catch (error) {
                    console.error('重试操作失败:', error);
                    this.showNotification('重试失败', 'error');
                } finally {
                    this.enhancedState.isProcessing = false;
                }
            }
            
            // 记录购买意图
            async recordPurchaseIntent() {
                try {
                    await this.orm.silent.call(
                        'iap.analytics',
                        'record_purchase_intent',
                        [{
                            service_name: this.props.errorData.service_name,
                            credit_needed: this.props.errorData.credit,
                            timestamp: Date.now(),
                            user_agent: navigator.userAgent,
                            referrer: document.referrer
                        }]
                    );
                } catch (error) {
                    console.error('记录购买意图失败:', error);
                }
            }
            
            // 显示通知
            showNotification(message, type) {
                if (this.enhancedConfig.enableNotifications) {
                    // 这里应该使用实际的通知服务
                    console.log(`[${type}] ${message}`);
                }
            }
            
            // 获取信用预测文本
            getCreditPredictionText() {
                const prediction = this.enhancedState.creditPrediction;
                if (!prediction.estimated_usage) return '';
                
                return `根据您的使用模式，预计需要 ${prediction.recommended_credits} 个信用点`;
            }
            
            // 获取优化建议文本
            getOptimizationSuggestionsText() {
                const suggestions = this.enhancedState.optimizationSuggestions;
                if (suggestions.length === 0) return '';
                
                return suggestions.map(s => s.description).join('; ');
            }
        }
        
        // 增强的错误处理器
        function enhancedInsufficientCreditHandler(env, error, originalError) {
            if (!originalError) {
                return false;
            }
            
            const { data } = originalError;
            if (data && data.name === "odoo.addons.iap.tools.iap_tools.InsufficientCreditError") {
                const errorData = JSON.parse(data.message);
                
                // 记录错误发生
                recordErrorOccurrence(env, errorData);
                
                // 显示增强的对话框
                env.services.dialog.add(EnhancedInsufficientCreditDialog, {
                    errorData: errorData,
                    enableAlternativeOptions: true,
                    enableUsageAnalytics: true,
                    enableContactSupport: true,
                    enableTrialExtension: true
                });
                
                return true;
            }
            
            return false;
        }
        
        // 记录错误发生
        async function recordErrorOccurrence(env, errorData) {
            try {
                await env.services.orm.silent.call(
                    'iap.analytics',
                    'record_error_occurrence',
                    [{
                        service_name: errorData.service_name,
                        credit_needed: errorData.credit,
                        timestamp: Date.now(),
                        user_context: env.user || {}
                    }]
                );
            } catch (error) {
                console.error('记录错误发生失败:', error);
            }
        }
        
        // 使用分析器
        class UsageAnalyzer {
            async analyze(serviceName) {
                // 实现使用情况分析逻辑
                return {
                    daily_usage: 50,
                    weekly_usage: 300,
                    monthly_usage: 1200,
                    peak_hours: ['09:00-11:00', '14:00-16:00'],
                    usage_trend: 'increasing'
                };
            }
        }
        
        // 信用预测器
        class CreditPredictor {
            async predict(serviceName, usageAnalytics) {
                // 实现信用预测逻辑
                return {
                    estimated_usage: usageAnalytics.daily_usage * 30,
                    recommended_credits: Math.ceil(usageAnalytics.daily_usage * 30 * 1.2),
                    confidence: 0.85
                };
            }
        }
        
        // 优化建议引擎
        class OptimizationEngine {
            async generateSuggestions(serviceName, usageAnalytics) {
                // 实现优化建议生成逻辑
                return [
                    {
                        id: 'batch_processing',
                        title: '批量处理',
                        description: '将多个请求合并为批量处理可以节省信用点',
                        potential_savings: '20%'
                    },
                    {
                        id: 'off_peak_usage',
                        title: '非高峰时段使用',
                        description: '在非高峰时段使用服务可以获得折扣',
                        potential_savings: '15%'
                    }
                ];
            }
            
            async applyOptimization(serviceName, suggestion) {
                // 实现优化应用逻辑
                return { success: true };
            }
        }
        
        // 支持服务
        class SupportService {
            async getSupportOptions(serviceName) {
                return [
                    { id: 'chat', label: '在线聊天', available: true },
                    { id: 'email', label: '邮件支持', available: true },
                    { id: 'phone', label: '电话支持', available: false }
                ];
            }
            
            async createSupportTicket(ticketData) {
                // 实现支持工单创建逻辑
                return { success: true, ticket_id: 'TICKET-12345' };
            }
        }
        
        // 注册增强的错误处理器
        registry
            .category("error_handlers")
            .add("enhancedInsufficientCreditHandler", enhancedInsufficientCreditHandler, { sequence: -1 });
        
        // 导出增强的组件
        __exports.EnhancedInsufficientCreditDialog = EnhancedInsufficientCreditDialog;
        __exports.enhancedInsufficientCreditHandler = enhancedInsufficientCreditHandler;
    }
};

// 应用信用不足错误处理器增强
InsufficientCreditErrorHandlerEnhancer.enhanceInsufficientCreditErrorHandler();
```

## 技术特点

### 1. 错误处理
- 专门的IAP错误类型检测
- 优雅的错误处理和用户引导
- 完整的错误处理流程

### 2. 对话框设计
- 基于OWL框架的现代化对话框
- 异步数据加载和处理
- 用户友好的界面设计

### 3. 服务集成
- 深度集成ORM服务
- 动态URL生成和跳转
- 版本感知的功能适配

### 4. 国际化支持
- 完整的多语言支持
- 动态的文本内容
- 版本相关的文本适配

## 设计模式

### 1. 处理器模式 (Handler Pattern)
- 专门的错误处理器
- 链式错误处理机制

### 2. 对话框模式 (Dialog Pattern)
- 模态对话框的用户交互
- 异步数据加载和显示

### 3. 服务定位器模式 (Service Locator Pattern)
- 通过钩子获取服务
- 解耦组件和服务实现

## 注意事项

1. **错误识别**: 确保准确识别IAP信用不足错误
2. **用户引导**: 提供清晰的购买引导和操作指示
3. **数据安全**: 安全处理错误数据和用户信息
4. **版本兼容**: 确保与不同Odoo版本的兼容性

## 扩展建议

1. **替代方案**: 提供信用购买的替代方案
2. **使用分析**: 分析用户使用模式提供优化建议
3. **试用扩展**: 支持试用期的扩展申请
4. **支持集成**: 集成客户支持和帮助系统
5. **预测功能**: 预测用户的信用需求

该信用不足错误处理器为IAP系统提供了重要的错误处理和用户引导功能，是IAP服务错误管理和用户体验的核心组件。

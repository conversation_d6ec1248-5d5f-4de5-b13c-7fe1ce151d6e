# Spreadsheet Helpers - 电子表格辅助工具

## 概述

`helpers.js` 是 Odoo Spreadsheet 模块的辅助工具文件，专门用于提供电子表格后端资源所需的辅助功能。该文件基于Odoo的模块系统和Web核心资源管理，主要负责加载电子表格所需的外部依赖库，特别是Chart.js图表库，为电子表格的图表功能提供必要的支持。

## 文件信息
- **路径**: `/spreadsheet/static/src/assets_backend/helpers.js`
- **行数**: 17
- **模块**: `@spreadsheet/assets_backend/helpers`

## 依赖关系

```javascript
// 核心依赖
'@web/core/assets'    // Web核心资源管理
```

## 核心功能

### 1. 模块定义

```javascript
odoo.define('@spreadsheet/assets_backend/helpers', ['@web/core/assets'], function (require) {
'use strict';
let __exports = {};
```

**模块特性**:
- **Odoo模块**: 使用Odoo标准模块定义方式
- **资源依赖**: 依赖Web核心资源管理模块
- **严格模式**: 启用JavaScript严格模式
- **导出对象**: 使用标准的导出对象模式

### 2. 资源加载服务

```javascript
const { loadBundle } = require("@web/core/assets");
```

**资源加载功能**:
- **Bundle加载**: 导入资源包加载函数
- **Web核心**: 使用Web核心的资源管理服务
- **解构导入**: 使用现代JavaScript解构语法
- **异步支持**: 支持异步资源加载

### 3. 电子表格依赖加载

```javascript
/**
 * Load external libraries required for o-spreadsheet
 * @returns {Promise<void>}
 */
__exports.loadSpreadsheetDependencies = loadSpreadsheetDependencies; async function loadSpreadsheetDependencies() {
    await loadBundle("web.chartjs_lib");
}
```

**依赖加载功能**:
- **异步函数**: 使用async/await模式的异步函数
- **Chart.js加载**: 专门加载Chart.js图表库
- **Promise返回**: 返回Promise对象支持链式调用
- **JSDoc注释**: 提供完整的函数文档
- **导出函数**: 将函数添加到导出对象

## 使用场景

### 1. 电子表格辅助工具增强

```javascript
// 电子表格辅助工具增强功能
const SpreadsheetHelpersEnhancer = {
    enhanceSpreadsheetHelpers: () => {
        // 增强的电子表格辅助工具
        const enhancedHelpers = {
            // 增强的依赖加载函数
            async loadSpreadsheetDependencies(options = {}) {
                const {
                    includeCharts = true,
                    includeFormulas = true,
                    includeFilters = true,
                    includeFormatting = true,
                    includePivot = true,
                    includeCollaboration = false,
                    loadingCallback = null,
                    errorCallback = null
                } = options;
                
                try {
                    const dependencies = [];
                    
                    // 基础Chart.js库
                    if (includeCharts) {
                        dependencies.push("web.chartjs_lib");
                    }
                    
                    // 公式计算库
                    if (includeFormulas) {
                        dependencies.push("web.formula_lib");
                    }
                    
                    // 过滤器库
                    if (includeFilters) {
                        dependencies.push("web.filter_lib");
                    }
                    
                    // 格式化库
                    if (includeFormatting) {
                        dependencies.push("web.formatting_lib");
                    }
                    
                    // 数据透视表库
                    if (includePivot) {
                        dependencies.push("web.pivot_lib");
                    }
                    
                    // 协作功能库
                    if (includeCollaboration) {
                        dependencies.push("web.collaboration_lib");
                    }
                    
                    // 加载所有依赖
                    for (let i = 0; i < dependencies.length; i++) {
                        const dependency = dependencies[i];
                        
                        if (loadingCallback) {
                            loadingCallback(dependency, i + 1, dependencies.length);
                        }
                        
                        await loadBundle(dependency);
                    }
                    
                    // 验证依赖加载
                    await this.validateDependencies(dependencies);
                    
                } catch (error) {
                    console.error('加载电子表格依赖失败:', error);
                    
                    if (errorCallback) {
                        errorCallback(error);
                    }
                    
                    throw error;
                }
            },
            
            // 验证依赖是否正确加载
            async validateDependencies(dependencies) {
                const validationResults = {};
                
                for (const dependency of dependencies) {
                    try {
                        validationResults[dependency] = await this.validateDependency(dependency);
                    } catch (error) {
                        validationResults[dependency] = false;
                        console.warn(`依赖验证失败: ${dependency}`, error);
                    }
                }
                
                return validationResults;
            },
            
            // 验证单个依赖
            async validateDependency(dependency) {
                switch (dependency) {
                    case "web.chartjs_lib":
                        return typeof window.Chart !== 'undefined';
                    case "web.formula_lib":
                        return typeof window.FormulaParser !== 'undefined';
                    case "web.filter_lib":
                        return typeof window.FilterEngine !== 'undefined';
                    case "web.formatting_lib":
                        return typeof window.NumberFormat !== 'undefined';
                    case "web.pivot_lib":
                        return typeof window.PivotTable !== 'undefined';
                    case "web.collaboration_lib":
                        return typeof window.CollaborationEngine !== 'undefined';
                    default:
                        return true;
                }
            },
            
            // 获取依赖信息
            getDependencyInfo(dependency) {
                const dependencyMap = {
                    "web.chartjs_lib": {
                        name: "Chart.js",
                        version: "3.9.1",
                        description: "图表渲染库",
                        size: "245KB",
                        features: ["线图", "柱状图", "饼图", "散点图"]
                    },
                    "web.formula_lib": {
                        name: "Formula Parser",
                        version: "2.1.0",
                        description: "公式解析和计算库",
                        size: "128KB",
                        features: ["数学函数", "逻辑函数", "文本函数", "日期函数"]
                    },
                    "web.filter_lib": {
                        name: "Filter Engine",
                        version: "1.5.2",
                        description: "数据过滤引擎",
                        size: "89KB",
                        features: ["条件过滤", "高级过滤", "自定义过滤", "过滤器组合"]
                    },
                    "web.formatting_lib": {
                        name: "Number Format",
                        version: "1.3.1",
                        description: "数字格式化库",
                        size: "67KB",
                        features: ["数字格式", "日期格式", "货币格式", "百分比格式"]
                    },
                    "web.pivot_lib": {
                        name: "Pivot Table",
                        version: "2.0.3",
                        description: "数据透视表库",
                        size: "156KB",
                        features: ["数据聚合", "行列分组", "计算字段", "动态透视"]
                    },
                    "web.collaboration_lib": {
                        name: "Collaboration Engine",
                        version: "1.2.0",
                        description: "协作功能库",
                        size: "203KB",
                        features: ["实时编辑", "冲突解决", "版本控制", "用户状态"]
                    }
                };
                
                return dependencyMap[dependency] || null;
            },
            
            // 预加载关键依赖
            async preloadCriticalDependencies() {
                const criticalDependencies = ["web.chartjs_lib", "web.formula_lib"];
                
                try {
                    await Promise.all(
                        criticalDependencies.map(dep => loadBundle(dep))
                    );
                    
                    console.log('关键依赖预加载完成');
                    return true;
                } catch (error) {
                    console.error('关键依赖预加载失败:', error);
                    return false;
                }
            },
            
            // 按需加载依赖
            async loadDependencyOnDemand(feature) {
                const featureDependencyMap = {
                    'charts': ['web.chartjs_lib'],
                    'formulas': ['web.formula_lib'],
                    'filters': ['web.filter_lib'],
                    'formatting': ['web.formatting_lib'],
                    'pivot': ['web.pivot_lib'],
                    'collaboration': ['web.collaboration_lib']
                };
                
                const dependencies = featureDependencyMap[feature];
                if (!dependencies) {
                    throw new Error(`未知功能: ${feature}`);
                }
                
                for (const dependency of dependencies) {
                    if (!await this.isDependencyLoaded(dependency)) {
                        await loadBundle(dependency);
                    }
                }
            },
            
            // 检查依赖是否已加载
            async isDependencyLoaded(dependency) {
                return await this.validateDependency(dependency);
            },
            
            // 获取加载状态
            getLoadingStatus() {
                const dependencies = [
                    "web.chartjs_lib",
                    "web.formula_lib", 
                    "web.filter_lib",
                    "web.formatting_lib",
                    "web.pivot_lib",
                    "web.collaboration_lib"
                ];
                
                const status = {};
                
                for (const dependency of dependencies) {
                    status[dependency] = this.isDependencyLoaded(dependency);
                }
                
                return status;
            },
            
            // 卸载依赖（如果支持）
            async unloadDependency(dependency) {
                // 注意：大多数情况下JavaScript库无法完全卸载
                // 这里主要是清理全局变量和事件监听器
                
                switch (dependency) {
                    case "web.chartjs_lib":
                        if (window.Chart) {
                            // 清理Chart.js实例
                            Object.values(window.Chart.instances || {}).forEach(chart => {
                                chart.destroy();
                            });
                        }
                        break;
                    case "web.collaboration_lib":
                        if (window.CollaborationEngine) {
                            // 断开协作连接
                            window.CollaborationEngine.disconnect();
                        }
                        break;
                    default:
                        console.warn(`不支持卸载依赖: ${dependency}`);
                }
            },
            
            // 获取依赖大小信息
            getDependencySize(dependency) {
                const info = this.getDependencyInfo(dependency);
                return info ? info.size : 'Unknown';
            },
            
            // 获取所有依赖的总大小
            getTotalDependencySize() {
                const dependencies = [
                    "web.chartjs_lib",
                    "web.formula_lib",
                    "web.filter_lib", 
                    "web.formatting_lib",
                    "web.pivot_lib",
                    "web.collaboration_lib"
                ];
                
                let totalSize = 0;
                const sizeMap = { 'KB': 1, 'MB': 1024 };
                
                for (const dependency of dependencies) {
                    const sizeStr = this.getDependencySize(dependency);
                    if (sizeStr !== 'Unknown') {
                        const match = sizeStr.match(/(\d+)(KB|MB)/);
                        if (match) {
                            const size = parseInt(match[1]);
                            const unit = match[2];
                            totalSize += size * sizeMap[unit];
                        }
                    }
                }
                
                return totalSize > 1024 ? `${(totalSize / 1024).toFixed(1)}MB` : `${totalSize}KB`;
            },
            
            // 性能监控
            async loadWithPerformanceMonitoring(dependency) {
                const startTime = performance.now();
                
                try {
                    await loadBundle(dependency);
                    
                    const endTime = performance.now();
                    const loadTime = endTime - startTime;
                    
                    console.log(`依赖 ${dependency} 加载耗时: ${loadTime.toFixed(2)}ms`);
                    
                    return {
                        success: true,
                        loadTime: loadTime,
                        dependency: dependency
                    };
                } catch (error) {
                    const endTime = performance.now();
                    const loadTime = endTime - startTime;
                    
                    console.error(`依赖 ${dependency} 加载失败，耗时: ${loadTime.toFixed(2)}ms`, error);
                    
                    return {
                        success: false,
                        loadTime: loadTime,
                        dependency: dependency,
                        error: error
                    };
                }
            },
            
            // 批量加载性能监控
            async loadBatchWithMonitoring(dependencies) {
                const results = [];
                const startTime = performance.now();
                
                for (const dependency of dependencies) {
                    const result = await this.loadWithPerformanceMonitoring(dependency);
                    results.push(result);
                }
                
                const endTime = performance.now();
                const totalTime = endTime - startTime;
                
                const summary = {
                    totalTime: totalTime,
                    totalDependencies: dependencies.length,
                    successCount: results.filter(r => r.success).length,
                    failureCount: results.filter(r => !r.success).length,
                    results: results
                };
                
                console.log('批量加载完成:', summary);
                return summary;
            }
        };
        
        // 导出增强的辅助工具
        return enhancedHelpers;
    }
};

// 应用电子表格辅助工具增强
const enhancedHelpers = SpreadsheetHelpersEnhancer.enhanceSpreadsheetHelpers();

// 导出增强的辅助工具
__exports.enhancedHelpers = enhancedHelpers;
```

## 技术特点

### 1. 异步加载
- 使用async/await模式
- 支持Promise链式调用
- 优雅的异步错误处理

### 2. 资源管理
- 集成Web核心资源管理
- 标准的Bundle加载机制
- 依赖关系管理

### 3. 模块化设计
- 清晰的模块定义
- 标准的导出机制
- 良好的代码组织

### 4. 文档完整
- 完整的JSDoc注释
- 清晰的函数说明
- 标准的参数和返回值文档

## 设计模式

### 1. 辅助工具模式 (Helper Pattern)
- 提供通用的辅助功能
- 封装复杂的操作逻辑

### 2. 异步模式 (Async Pattern)
- 非阻塞的资源加载
- 优雅的异步操作处理

### 3. 依赖注入模式 (Dependency Injection Pattern)
- 动态加载外部依赖
- 解耦模块间的依赖关系

## 注意事项

1. **加载顺序**: 确保依赖的正确加载顺序
2. **错误处理**: 提供完善的加载失败处理
3. **性能考虑**: 避免重复加载相同的依赖
4. **兼容性**: 确保与不同版本的Chart.js兼容

## 扩展建议

1. **更多依赖**: 支持加载更多的外部库
2. **版本管理**: 添加依赖版本的管理功能
3. **缓存机制**: 实现依赖的缓存机制
4. **加载优化**: 优化依赖的加载性能
5. **错误恢复**: 实现加载失败的自动重试机制

该电子表格辅助工具文件为电子表格系统提供了重要的依赖管理功能，确保图表等功能所需的外部库能够正确加载。

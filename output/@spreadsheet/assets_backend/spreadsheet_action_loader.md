# Spreadsheet Action Loader - 电子表格操作加载器

## 概述

`spreadsheet_action_loader.js` 是 Odoo Spreadsheet 模块的操作加载器，专门用于实现电子表格操作的懒加载机制。该文件基于Odoo的操作注册系统和资源管理，提供了一个智能的加载策略，只有在需要时才加载电子表格相关的资源包，从而优化应用的启动性能和资源使用效率。

## 文件信息
- **路径**: `/spreadsheet/static/src/assets_backend/spreadsheet_action_loader.js`
- **行数**: 57
- **模块**: `@spreadsheet/assets_backend/spreadsheet_action_loader`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'                    // 国际化翻译
'@web/core/registry'                             // 核心注册表
'@web/core/assets'                               // 资源管理
'@spreadsheet/assets_backend/helpers'           // 电子表格辅助工具
```

## 核心功能

### 1. 模块定义和依赖导入

```javascript
const { _t } = require("@web/core/l10n/translation");
const { registry } = require("@web/core/registry");
const { loadBundle } = require("@web/core/assets");
const { loadSpreadsheetDependencies } = require("@spreadsheet/assets_backend/helpers");

const actionRegistry = registry.category("actions");
```

**依赖导入功能**:
- **翻译服务**: 导入国际化翻译函数
- **注册表服务**: 导入核心注册表系统
- **资源加载**: 导入资源包加载功能
- **电子表格辅助**: 导入电子表格专用的依赖加载器
- **操作注册表**: 获取操作类别的注册表实例

### 2. 懒加载操作添加器

```javascript
/**
 * Add a new function client action which loads the spreadsheet bundle, then
 * launch the actual action.
 * The action should be redefine in the bundle with `{ force: true }`
 * and the actual action component or function
 * @param {string} actionName
 * @param {string} [path]
 * @param {string} [displayName]
 */
__exports.addSpreadsheetActionLazyLoader = addSpreadsheetActionLazyLoader; function addSpreadsheetActionLazyLoader(actionName, path, displayName) {
```

**懒加载添加器功能**:
- **JSDoc文档**: 提供完整的函数文档说明
- **参数定义**: 接收操作名称、路径和显示名称
- **可选参数**: 支持可选的路径和显示名称参数
- **导出函数**: 将函数添加到模块导出对象

### 3. 懒加载操作逻辑

```javascript
const actionLazyLoader = async (env, action) => {
    // load the bundle which should redefine the action in the registry
    await loadSpreadsheetDependencies();
    await loadBundle("spreadsheet.o_spreadsheet");

    if (actionRegistry.get(actionName) === actionLazyLoader) {
        // At this point, the real spreadsheet client action should be loaded and have
        // replaced this function in the action registry. If it's not the case,
        // it probably means that there was a crash in the bundle (e.g. syntax
        // error). In this case, this action will remain in the registry, which
        // will lead to an infinite loop. To prevent that, we push another action
        // in the registry.
        actionRegistry.add(
            actionName,
            () => {
                const msg = _t("%s couldn't be loaded", actionName);
                env.services.notification.add(msg, { type: "danger" });
            },
            { force: true }
        );
    }
    // then do the action again, with the actual definition registered
    return action;
};
```

**懒加载逻辑功能**:
- **异步加载**: 使用async/await模式进行异步资源加载
- **依赖加载**: 首先加载电子表格的外部依赖
- **Bundle加载**: 加载电子表格的主要资源包
- **无限循环防护**: 检测并防止加载失败导致的无限循环
- **错误处理**: 提供加载失败时的错误通知
- **强制替换**: 使用force选项强制替换失败的操作

### 4. 操作属性设置

```javascript
if (path) {
    actionLazyLoader.path = path;
}
if (displayName) {
    actionLazyLoader.displayName = displayName;
}
actionRegistry.add(actionName, actionLazyLoader);
```

**属性设置功能**:
- **路径设置**: 为操作设置可选的路径属性
- **显示名称**: 为操作设置可选的显示名称
- **注册操作**: 将懒加载器注册到操作注册表
- **条件设置**: 只在提供参数时设置相应属性

### 5. 默认操作注册

```javascript
addSpreadsheetActionLazyLoader("action_download_spreadsheet");
```

**默认注册功能**:
- **下载操作**: 注册电子表格下载操作的懒加载器
- **标准操作**: 提供标准的电子表格操作支持
- **即时注册**: 模块加载时立即注册操作

## 使用场景

### 1. 电子表格操作加载器增强

```javascript
// 电子表格操作加载器增强功能
const SpreadsheetActionLoaderEnhancer = {
    enhanceSpreadsheetActionLoader: () => {
        // 增强的电子表格操作加载器
        const enhancedActionLoader = {
            // 增强的懒加载操作添加器
            addSpreadsheetActionLazyLoader(actionName, options = {}) {
                const {
                    path = null,
                    displayName = null,
                    dependencies = [],
                    bundles = ["spreadsheet.o_spreadsheet"],
                    timeout = 30000,
                    retryCount = 3,
                    fallbackAction = null,
                    loadingMessage = null,
                    errorMessage = null,
                    preloadCondition = null,
                    cacheStrategy = 'default'
                } = options;
                
                const actionLazyLoader = async (env, action) => {
                    let retries = 0;
                    const startTime = performance.now();
                    
                    // 显示加载消息
                    let loadingNotification = null;
                    if (loadingMessage) {
                        loadingNotification = env.services.notification.add(
                            loadingMessage,
                            { type: "info", sticky: true }
                        );
                    }
                    
                    try {
                        // 检查预加载条件
                        if (preloadCondition && !preloadCondition(env, action)) {
                            throw new Error('预加载条件不满足');
                        }
                        
                        while (retries <= retryCount) {
                            try {
                                // 加载自定义依赖
                                for (const dependency of dependencies) {
                                    await this.loadCustomDependency(dependency);
                                }
                                
                                // 加载电子表格依赖
                                await loadSpreadsheetDependencies();
                                
                                // 加载所有指定的bundles
                                for (const bundle of bundles) {
                                    await this.loadBundleWithTimeout(bundle, timeout);
                                }
                                
                                break; // 成功加载，跳出重试循环
                                
                            } catch (error) {
                                retries++;
                                if (retries > retryCount) {
                                    throw error;
                                }
                                
                                console.warn(`加载失败，重试 ${retries}/${retryCount}:`, error);
                                await this.delay(1000 * retries); // 递增延迟
                            }
                        }
                        
                        // 验证操作是否正确加载
                        if (actionRegistry.get(actionName) === actionLazyLoader) {
                            // 尝试使用fallback操作
                            if (fallbackAction) {
                                actionRegistry.add(actionName, fallbackAction, { force: true });
                            } else {
                                // 使用默认错误处理
                                actionRegistry.add(
                                    actionName,
                                    () => {
                                        const msg = errorMessage || _t("%s couldn't be loaded", actionName);
                                        env.services.notification.add(msg, { type: "danger" });
                                    },
                                    { force: true }
                                );
                            }
                        }
                        
                        // 记录加载性能
                        const loadTime = performance.now() - startTime;
                        console.log(`操作 ${actionName} 加载完成，耗时: ${loadTime.toFixed(2)}ms`);
                        
                        // 清除加载通知
                        if (loadingNotification) {
                            loadingNotification.close();
                        }
                        
                        return action;
                        
                    } catch (error) {
                        console.error(`操作 ${actionName} 加载失败:`, error);
                        
                        // 清除加载通知
                        if (loadingNotification) {
                            loadingNotification.close();
                        }
                        
                        // 显示错误通知
                        const msg = errorMessage || _t("Failed to load %s: %s", actionName, error.message);
                        env.services.notification.add(msg, { type: "danger" });
                        
                        // 如果有fallback操作，使用它
                        if (fallbackAction) {
                            return fallbackAction(env, action);
                        }
                        
                        throw error;
                    }
                };
                
                // 设置操作属性
                if (path) {
                    actionLazyLoader.path = path;
                }
                if (displayName) {
                    actionLazyLoader.displayName = displayName;
                }
                
                // 添加元数据
                actionLazyLoader.metadata = {
                    actionName,
                    dependencies,
                    bundles,
                    timeout,
                    retryCount,
                    cacheStrategy,
                    createdAt: Date.now()
                };
                
                // 注册操作
                actionRegistry.add(actionName, actionLazyLoader);
                
                // 如果满足预加载条件，立即预加载
                if (preloadCondition && preloadCondition()) {
                    this.preloadAction(actionName);
                }
            },
            
            // 带超时的Bundle加载
            async loadBundleWithTimeout(bundle, timeout) {
                return new Promise((resolve, reject) => {
                    const timer = setTimeout(() => {
                        reject(new Error(`Bundle ${bundle} 加载超时`));
                    }, timeout);
                    
                    loadBundle(bundle)
                        .then(result => {
                            clearTimeout(timer);
                            resolve(result);
                        })
                        .catch(error => {
                            clearTimeout(timer);
                            reject(error);
                        });
                });
            },
            
            // 加载自定义依赖
            async loadCustomDependency(dependency) {
                if (typeof dependency === 'string') {
                    await loadBundle(dependency);
                } else if (typeof dependency === 'function') {
                    await dependency();
                } else if (dependency.type === 'script') {
                    await this.loadScript(dependency.src);
                } else if (dependency.type === 'css') {
                    await this.loadCSS(dependency.href);
                }
            },
            
            // 加载外部脚本
            loadScript(src) {
                return new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = src;
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
            },
            
            // 加载外部CSS
            loadCSS(href) {
                return new Promise((resolve, reject) => {
                    const link = document.createElement('link');
                    link.rel = 'stylesheet';
                    link.href = href;
                    link.onload = resolve;
                    link.onerror = reject;
                    document.head.appendChild(link);
                });
            },
            
            // 延迟函数
            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            },
            
            // 预加载操作
            async preloadAction(actionName) {
                try {
                    const action = actionRegistry.get(actionName);
                    if (action && action.metadata) {
                        console.log(`预加载操作: ${actionName}`);
                        // 这里可以实现预加载逻辑
                    }
                } catch (error) {
                    console.warn(`预加载操作 ${actionName} 失败:`, error);
                }
            },
            
            // 批量注册操作
            addMultipleSpreadsheetActions(actions) {
                for (const [actionName, options] of Object.entries(actions)) {
                    this.addSpreadsheetActionLazyLoader(actionName, options);
                }
            },
            
            // 获取操作加载状态
            getActionLoadingStatus(actionName) {
                const action = actionRegistry.get(actionName);
                if (!action) {
                    return 'not_registered';
                }
                
                if (action.metadata) {
                    return 'lazy_loader';
                }
                
                return 'loaded';
            },
            
            // 获取所有电子表格操作
            getAllSpreadsheetActions() {
                const actions = [];
                const allActions = actionRegistry.getAll();
                
                for (const [name, action] of Object.entries(allActions)) {
                    if (action.metadata && action.metadata.actionName) {
                        actions.push({
                            name,
                            metadata: action.metadata,
                            status: this.getActionLoadingStatus(name)
                        });
                    }
                }
                
                return actions;
            },
            
            // 清理未使用的操作
            cleanupUnusedActions() {
                const allActions = this.getAllSpreadsheetActions();
                const now = Date.now();
                const maxAge = 24 * 60 * 60 * 1000; // 24小时
                
                for (const action of allActions) {
                    if (action.status === 'lazy_loader' && 
                        now - action.metadata.createdAt > maxAge) {
                        console.log(`清理未使用的操作: ${action.name}`);
                        actionRegistry.remove(action.name);
                    }
                }
            }
        };
        
        // 注册常用的电子表格操作
        const commonActions = {
            "action_download_spreadsheet": {
                displayName: _t("Download Spreadsheet"),
                loadingMessage: _t("Loading spreadsheet downloader...")
            },
            "action_create_spreadsheet": {
                displayName: _t("Create Spreadsheet"),
                loadingMessage: _t("Loading spreadsheet creator...")
            },
            "action_edit_spreadsheet": {
                displayName: _t("Edit Spreadsheet"),
                loadingMessage: _t("Loading spreadsheet editor...")
            },
            "action_share_spreadsheet": {
                displayName: _t("Share Spreadsheet"),
                loadingMessage: _t("Loading sharing options...")
            },
            "action_import_spreadsheet": {
                displayName: _t("Import Spreadsheet"),
                loadingMessage: _t("Loading import wizard...")
            },
            "action_export_spreadsheet": {
                displayName: _t("Export Spreadsheet"),
                loadingMessage: _t("Loading export options...")
            }
        };
        
        enhancedActionLoader.addMultipleSpreadsheetActions(commonActions);
        
        // 定期清理未使用的操作
        setInterval(() => {
            enhancedActionLoader.cleanupUnusedActions();
        }, 60 * 60 * 1000); // 每小时清理一次
        
        return enhancedActionLoader;
    }
};

// 应用电子表格操作加载器增强
const enhancedActionLoader = SpreadsheetActionLoaderEnhancer.enhanceSpreadsheetActionLoader();

// 导出增强的操作加载器
__exports.enhancedActionLoader = enhancedActionLoader;
```

## 技术特点

### 1. 懒加载机制
- 按需加载电子表格资源
- 优化应用启动性能
- 减少初始资源消耗

### 2. 错误处理
- 完善的加载失败处理
- 无限循环防护机制
- 用户友好的错误通知

### 3. 异步操作
- 使用async/await模式
- 非阻塞的资源加载
- 优雅的异步错误处理

### 4. 注册表集成
- 深度集成操作注册表
- 动态操作替换机制
- 标准的操作管理

## 设计模式

### 1. 懒加载模式 (Lazy Loading Pattern)
- 延迟加载资源直到需要时
- 优化性能和资源使用

### 2. 代理模式 (Proxy Pattern)
- 懒加载器作为真实操作的代理
- 透明的操作替换

### 3. 注册表模式 (Registry Pattern)
- 集中管理操作定义
- 动态操作注册和替换

## 注意事项

1. **加载顺序**: 确保依赖的正确加载顺序
2. **错误恢复**: 提供加载失败的恢复机制
3. **性能监控**: 监控加载性能和成功率
4. **内存管理**: 避免内存泄漏和资源浪费

## 扩展建议

1. **预加载策略**: 实现智能的预加载策略
2. **缓存机制**: 添加资源缓存机制
3. **加载优化**: 优化资源加载性能
4. **监控统计**: 添加加载统计和监控
5. **版本管理**: 支持不同版本的资源管理

该电子表格操作加载器为电子表格系统提供了重要的性能优化功能，通过懒加载机制显著提升了应用的启动速度和资源使用效率。

# Spreadsheet Constants - 电子表格常量定义

## 概述

`constants.js` 是 Odoo Spreadsheet 模块的常量定义文件，专门用于定义电子表格后端资源中使用的各种常量和配置选项。该文件基于Odoo的模块系统，提供了日期过滤选项和月份选项的标准化定义，为电子表格功能提供了统一的常量管理，是电子表格系统配置和国际化的重要组成部分。

## 文件信息
- **路径**: `/spreadsheet/static/src/assets_backend/constants.js`
- **行数**: 30
- **模块**: `@spreadsheet/assets_backend/constants`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'    // 国际化翻译
```

## 核心功能

### 1. 模块定义

```javascript
odoo.define('@spreadsheet/assets_backend/constants', ['@web/core/l10n/translation'], function (require) {
'use strict';
let __exports = {};
```

**模块特性**:
- **Odoo模块**: 使用Odoo标准模块定义方式
- **依赖管理**: 明确声明对翻译模块的依赖
- **严格模式**: 启用JavaScript严格模式
- **导出对象**: 使用标准的导出对象模式

### 2. 翻译服务集成

```javascript
const { _t } = require("@web/core/l10n/translation");
```

**翻译集成功能**:
- **翻译函数**: 导入标准的翻译函数
- **国际化支持**: 为常量提供多语言支持
- **Web核心**: 使用Web核心的翻译服务
- **解构导入**: 使用现代JavaScript解构语法

### 3. 日期过滤选项

```javascript
const FILTER_DATE_OPTION = __exports.FILTER_DATE_OPTION = {
    quarter: ["first_quarter", "second_quarter", "third_quarter", "fourth_quarter"],
};
```

**日期过滤功能**:
- **季度选项**: 定义四个季度的标识符
- **常量导出**: 将常量添加到导出对象
- **标准化**: 提供标准化的季度标识符
- **扩展性**: 设计为可扩展的日期过滤选项结构

### 4. 月份选项定义

```javascript
const monthsOptions = __exports.monthsOptions = [
    { id: "january", description: _t("January") },
    { id: "february", description: _t("February") },
    { id: "march", description: _t("March") },
    { id: "april", description: _t("April") },
    { id: "may", description: _t("May") },
    { id: "june", description: _t("June") },
    { id: "july", description: _t("July") },
    { id: "august", description: _t("August") },
    { id: "september", description: _t("September") },
    { id: "october", description: _t("October") },
    { id: "november", description: _t("November") },
    { id: "december", description: _t("December") },
];
```

**月份选项功能**:
- **完整月份**: 定义全年12个月的选项
- **双重标识**: 提供ID和描述的双重标识系统
- **国际化**: 所有月份名称都支持翻译
- **结构化**: 使用对象数组的结构化数据格式
- **标准化**: 提供标准的月份标识符和显示名称

## 使用场景

### 1. 电子表格常量系统增强

```javascript
// 电子表格常量系统增强功能
const SpreadsheetConstantsEnhancer = {
    enhanceSpreadsheetConstants: () => {
        // 增强的电子表格常量定义
        const enhancedConstants = {
            // 增强的日期过滤选项
            FILTER_DATE_OPTION: {
                quarter: ["first_quarter", "second_quarter", "third_quarter", "fourth_quarter"],
                semester: ["first_semester", "second_semester"],
                trimester: ["first_trimester", "second_trimester", "third_trimester"],
                year: ["current_year", "previous_year", "next_year"],
                month: ["current_month", "previous_month", "next_month"],
                week: ["current_week", "previous_week", "next_week"],
                day: ["today", "yesterday", "tomorrow"],
                custom: ["date_range", "relative_date", "fixed_date"]
            },
            
            // 增强的月份选项
            monthsOptions: [
                { id: "january", description: _t("January"), number: 1, quarter: 1, season: "winter" },
                { id: "february", description: _t("February"), number: 2, quarter: 1, season: "winter" },
                { id: "march", description: _t("March"), number: 3, quarter: 1, season: "spring" },
                { id: "april", description: _t("April"), number: 4, quarter: 2, season: "spring" },
                { id: "may", description: _t("May"), number: 5, quarter: 2, season: "spring" },
                { id: "june", description: _t("June"), number: 6, quarter: 2, season: "summer" },
                { id: "july", description: _t("July"), number: 7, quarter: 3, season: "summer" },
                { id: "august", description: _t("August"), number: 8, quarter: 3, season: "summer" },
                { id: "september", description: _t("September"), number: 9, quarter: 3, season: "autumn" },
                { id: "october", description: _t("October"), number: 10, quarter: 4, season: "autumn" },
                { id: "november", description: _t("November"), number: 11, quarter: 4, season: "autumn" },
                { id: "december", description: _t("December"), number: 12, quarter: 4, season: "winter" }
            ],
            
            // 季度选项
            quarterOptions: [
                { id: "first_quarter", description: _t("Q1"), months: [1, 2, 3], name: _t("First Quarter") },
                { id: "second_quarter", description: _t("Q2"), months: [4, 5, 6], name: _t("Second Quarter") },
                { id: "third_quarter", description: _t("Q3"), months: [7, 8, 9], name: _t("Third Quarter") },
                { id: "fourth_quarter", description: _t("Q4"), months: [10, 11, 12], name: _t("Fourth Quarter") }
            ],
            
            // 工作日选项
            weekdayOptions: [
                { id: "monday", description: _t("Monday"), number: 1, isWeekend: false },
                { id: "tuesday", description: _t("Tuesday"), number: 2, isWeekend: false },
                { id: "wednesday", description: _t("Wednesday"), number: 3, isWeekend: false },
                { id: "thursday", description: _t("Thursday"), number: 4, isWeekend: false },
                { id: "friday", description: _t("Friday"), number: 5, isWeekend: false },
                { id: "saturday", description: _t("Saturday"), number: 6, isWeekend: true },
                { id: "sunday", description: _t("Sunday"), number: 0, isWeekend: true }
            ],
            
            // 时间单位选项
            timeUnitOptions: [
                { id: "second", description: _t("Second"), plural: _t("Seconds"), factor: 1 },
                { id: "minute", description: _t("Minute"), plural: _t("Minutes"), factor: 60 },
                { id: "hour", description: _t("Hour"), plural: _t("Hours"), factor: 3600 },
                { id: "day", description: _t("Day"), plural: _t("Days"), factor: 86400 },
                { id: "week", description: _t("Week"), plural: _t("Weeks"), factor: 604800 },
                { id: "month", description: _t("Month"), plural: _t("Months"), factor: 2592000 },
                { id: "year", description: _t("Year"), plural: _t("Years"), factor: 31536000 }
            ],
            
            // 数据类型选项
            dataTypeOptions: [
                { id: "text", description: _t("Text"), icon: "fa-font", validation: "string" },
                { id: "number", description: _t("Number"), icon: "fa-hashtag", validation: "number" },
                { id: "date", description: _t("Date"), icon: "fa-calendar", validation: "date" },
                { id: "datetime", description: _t("Date & Time"), icon: "fa-clock-o", validation: "datetime" },
                { id: "boolean", description: _t("Boolean"), icon: "fa-check-square", validation: "boolean" },
                { id: "currency", description: _t("Currency"), icon: "fa-money", validation: "currency" },
                { id: "percentage", description: _t("Percentage"), icon: "fa-percent", validation: "percentage" }
            ],
            
            // 图表类型选项
            chartTypeOptions: [
                { id: "line", description: _t("Line Chart"), icon: "fa-line-chart", category: "trend" },
                { id: "bar", description: _t("Bar Chart"), icon: "fa-bar-chart", category: "comparison" },
                { id: "column", description: _t("Column Chart"), icon: "fa-bar-chart-o", category: "comparison" },
                { id: "pie", description: _t("Pie Chart"), icon: "fa-pie-chart", category: "proportion" },
                { id: "doughnut", description: _t("Doughnut Chart"), icon: "fa-circle-o", category: "proportion" },
                { id: "area", description: _t("Area Chart"), icon: "fa-area-chart", category: "trend" },
                { id: "scatter", description: _t("Scatter Chart"), icon: "fa-circle", category: "correlation" }
            ],
            
            // 格式化选项
            formatOptions: [
                { id: "general", description: _t("General"), pattern: "", example: "123.45" },
                { id: "number", description: _t("Number"), pattern: "#,##0.00", example: "1,234.56" },
                { id: "currency", description: _t("Currency"), pattern: "$#,##0.00", example: "$1,234.56" },
                { id: "percentage", description: _t("Percentage"), pattern: "0.00%", example: "12.34%" },
                { id: "date", description: _t("Date"), pattern: "MM/DD/YYYY", example: "12/31/2023" },
                { id: "time", description: _t("Time"), pattern: "HH:MM:SS", example: "14:30:00" },
                { id: "datetime", description: _t("Date Time"), pattern: "MM/DD/YYYY HH:MM", example: "12/31/2023 14:30" }
            ],
            
            // 聚合函数选项
            aggregationOptions: [
                { id: "sum", description: _t("Sum"), function: "SUM", icon: "fa-plus" },
                { id: "average", description: _t("Average"), function: "AVERAGE", icon: "fa-calculator" },
                { id: "count", description: _t("Count"), function: "COUNT", icon: "fa-list-ol" },
                { id: "max", description: _t("Maximum"), function: "MAX", icon: "fa-arrow-up" },
                { id: "min", description: _t("Minimum"), function: "MIN", icon: "fa-arrow-down" },
                { id: "median", description: _t("Median"), function: "MEDIAN", icon: "fa-sort" },
                { id: "mode", description: _t("Mode"), function: "MODE", icon: "fa-repeat" }
            ],
            
            // 排序选项
            sortOptions: [
                { id: "asc", description: _t("Ascending"), icon: "fa-sort-asc", direction: 1 },
                { id: "desc", description: _t("Descending"), icon: "fa-sort-desc", direction: -1 }
            ],
            
            // 过滤操作符选项
            filterOperatorOptions: [
                { id: "equals", description: _t("Equals"), symbol: "=", types: ["text", "number", "date"] },
                { id: "not_equals", description: _t("Not Equals"), symbol: "≠", types: ["text", "number", "date"] },
                { id: "greater_than", description: _t("Greater Than"), symbol: ">", types: ["number", "date"] },
                { id: "greater_equal", description: _t("Greater or Equal"), symbol: "≥", types: ["number", "date"] },
                { id: "less_than", description: _t("Less Than"), symbol: "<", types: ["number", "date"] },
                { id: "less_equal", description: _t("Less or Equal"), symbol: "≤", types: ["number", "date"] },
                { id: "contains", description: _t("Contains"), symbol: "∋", types: ["text"] },
                { id: "not_contains", description: _t("Does Not Contain"), symbol: "∌", types: ["text"] },
                { id: "starts_with", description: _t("Starts With"), symbol: "^", types: ["text"] },
                { id: "ends_with", description: _t("Ends With"), symbol: "$", types: ["text"] },
                { id: "is_empty", description: _t("Is Empty"), symbol: "∅", types: ["text", "number", "date"] },
                { id: "is_not_empty", description: _t("Is Not Empty"), symbol: "∄", types: ["text", "number", "date"] }
            ],
            
            // 颜色主题选项
            colorThemeOptions: [
                { id: "default", description: _t("Default"), colors: ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd"] },
                { id: "pastel", description: _t("Pastel"), colors: ["#aec7e8", "#ffbb78", "#98df8a", "#ff9896", "#c5b0d5"] },
                { id: "bright", description: _t("Bright"), colors: ["#ff0000", "#00ff00", "#0000ff", "#ffff00", "#ff00ff"] },
                { id: "earth", description: _t("Earth"), colors: ["#8b4513", "#daa520", "#228b22", "#4682b4", "#800080"] },
                { id: "ocean", description: _t("Ocean"), colors: ["#006994", "#0085c3", "#00a6fb", "#7dd3fc", "#bfdbfe"] }
            ],
            
            // 单位转换选项
            unitConversionOptions: {
                length: [
                    { id: "mm", description: _t("Millimeter"), factor: 1 },
                    { id: "cm", description: _t("Centimeter"), factor: 10 },
                    { id: "m", description: _t("Meter"), factor: 1000 },
                    { id: "km", description: _t("Kilometer"), factor: 1000000 }
                ],
                weight: [
                    { id: "g", description: _t("Gram"), factor: 1 },
                    { id: "kg", description: _t("Kilogram"), factor: 1000 },
                    { id: "t", description: _t("Ton"), factor: 1000000 }
                ],
                volume: [
                    { id: "ml", description: _t("Milliliter"), factor: 1 },
                    { id: "l", description: _t("Liter"), factor: 1000 },
                    { id: "m3", description: _t("Cubic Meter"), factor: 1000000 }
                ]
            },
            
            // 默认设置
            defaultSettings: {
                locale: "en_US",
                currency: "USD",
                dateFormat: "MM/DD/YYYY",
                timeFormat: "HH:MM:SS",
                numberFormat: "#,##0.00",
                decimalSeparator: ".",
                thousandsSeparator: ",",
                timezone: "UTC"
            },
            
            // 限制常量
            limits: {
                maxRows: 1000000,
                maxColumns: 16384,
                maxCellLength: 32767,
                maxSheets: 255,
                maxCharts: 100,
                maxFilters: 50,
                maxFormulas: 1000
            },
            
            // 错误代码
            errorCodes: {
                INVALID_FORMULA: { code: "E001", message: _t("Invalid formula") },
                CIRCULAR_REFERENCE: { code: "E002", message: _t("Circular reference") },
                DIVISION_BY_ZERO: { code: "E003", message: _t("Division by zero") },
                INVALID_REFERENCE: { code: "E004", message: _t("Invalid cell reference") },
                TYPE_MISMATCH: { code: "E005", message: _t("Type mismatch") },
                OUT_OF_RANGE: { code: "E006", message: _t("Value out of range") },
                PERMISSION_DENIED: { code: "E007", message: _t("Permission denied") }
            }
        };
        
        // 工具函数
        const utils = {
            // 根据ID获取月份信息
            getMonthById: (id) => {
                return enhancedConstants.monthsOptions.find(month => month.id === id);
            },
            
            // 根据数字获取月份信息
            getMonthByNumber: (number) => {
                return enhancedConstants.monthsOptions.find(month => month.number === number);
            },
            
            // 获取季度的月份
            getQuarterMonths: (quarterId) => {
                const quarter = enhancedConstants.quarterOptions.find(q => q.id === quarterId);
                return quarter ? quarter.months : [];
            },
            
            // 获取数据类型验证函数
            getDataTypeValidator: (typeId) => {
                const dataType = enhancedConstants.dataTypeOptions.find(type => type.id === typeId);
                return dataType ? dataType.validation : null;
            },
            
            // 格式化数值
            formatValue: (value, formatId) => {
                const format = enhancedConstants.formatOptions.find(f => f.id === formatId);
                if (!format) return value;
                
                // 这里应该实现实际的格式化逻辑
                return value;
            },
            
            // 获取颜色主题
            getColorTheme: (themeId) => {
                const theme = enhancedConstants.colorThemeOptions.find(t => t.id === themeId);
                return theme ? theme.colors : enhancedConstants.colorThemeOptions[0].colors;
            },
            
            // 单位转换
            convertUnit: (value, fromUnit, toUnit, category) => {
                const units = enhancedConstants.unitConversionOptions[category];
                if (!units) return value;
                
                const from = units.find(u => u.id === fromUnit);
                const to = units.find(u => u.id === toUnit);
                
                if (!from || !to) return value;
                
                return (value * from.factor) / to.factor;
            }
        };
        
        // 导出增强的常量和工具
        return {
            ...enhancedConstants,
            utils: utils
        };
    }
};

// 应用电子表格常量系统增强
const enhancedConstants = SpreadsheetConstantsEnhancer.enhanceSpreadsheetConstants();

// 导出增强的常量
__exports.enhancedConstants = enhancedConstants;
```

## 技术特点

### 1. 模块化设计
- 使用Odoo标准模块定义
- 清晰的依赖关系管理
- 标准的导出机制

### 2. 国际化支持
- 完整的翻译函数集成
- 所有用户可见文本的翻译
- 多语言环境的支持

### 3. 结构化数据
- 使用对象和数组的结构化格式
- 清晰的数据组织方式
- 易于扩展和维护

### 4. 标准化常量
- 提供标准化的标识符
- 统一的命名约定
- 一致的数据格式

## 设计模式

### 1. 常量模式 (Constants Pattern)
- 集中管理系统常量
- 避免魔法数字和字符串

### 2. 配置模式 (Configuration Pattern)
- 提供系统配置选项
- 支持灵活的配置管理

### 3. 国际化模式 (I18n Pattern)
- 支持多语言环境
- 分离文本和逻辑

## 注意事项

1. **迁移计划**: 注释中提到需要移除映射的迁移计划
2. **数据一致性**: 确保常量定义的一致性
3. **性能考虑**: 避免过度复杂的常量结构
4. **向后兼容**: 保持与现有代码的兼容性

## 扩展建议

1. **更多日期选项**: 添加更多的日期过滤选项
2. **季度详情**: 为季度添加更详细的信息
3. **工作日支持**: 添加工作日相关的常量
4. **时区支持**: 添加时区相关的常量定义
5. **格式化选项**: 添加数字和日期格式化选项

该电子表格常量文件为电子表格系统提供了重要的基础配置和标准化定义，确保系统的一致性和国际化支持。

# Spreadsheet Binary Field - 电子表格二进制字段组件

## 概述

`spreadsheet_binary_field.js` 是 Odoo Spreadsheet 模块的二进制字段组件，专门用于处理电子表格文件的二进制数据显示和操作。该组件基于Odoo的字段系统和二进制字段组件，提供了专门针对电子表格文件的用户界面和交互功能，是电子表格文件管理的重要组成部分。

## 文件信息
- **路径**: `/spreadsheet/static/src/assets_backend/spreadsheet_binary_field/spreadsheet_binary_field.js`
- **行数**: 25
- **模块**: `@spreadsheet/assets_backend/spreadsheet_binary_field/spreadsheet_binary_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                        // 核心注册表
'@web/views/fields/binary/binary_field'    // 二进制字段组件
```

## 核心功能

### 1. 模块定义和依赖导入

```javascript
const { registry } = require("@web/core/registry");
const { BinaryField, binaryField } = require("@web/views/fields/binary/binary_field");
```

**依赖导入功能**:
- **注册表服务**: 导入核心注册表系统
- **二进制字段**: 导入基础二进制字段组件和配置
- **组件继承**: 为组件继承提供基础类
- **字段配置**: 导入字段的基础配置对象

### 2. 电子表格二进制字段组件

```javascript
const SpreadsheetBinaryField = __exports.SpreadsheetBinaryField = class SpreadsheetBinaryField extends BinaryField {
    static template = "spreadsheet.SpreadsheetBinaryField";

    setup() {
        super.setup();
    }

    async onFileDownload() {}
}
```

**组件功能**:
- **继承扩展**: 继承基础二进制字段的所有功能
- **模板定制**: 使用专门的电子表格二进制字段模板
- **生命周期**: 调用父类的setup方法进行初始化
- **下载处理**: 提供文件下载的异步处理方法（待实现）
- **导出组件**: 将组件添加到模块导出对象

### 3. 字段配置对象

```javascript
const spreadsheetBinaryField = __exports.spreadsheetBinaryField = {
    ...binaryField,
    component: SpreadsheetBinaryField,
};
```

**配置功能**:
- **配置继承**: 继承基础二进制字段的所有配置
- **组件替换**: 使用电子表格专用的组件
- **扩展语法**: 使用ES6扩展语法合并配置
- **导出配置**: 将配置对象添加到模块导出

### 4. 字段注册

```javascript
registry.category("fields").add("binary_spreadsheet", spreadsheetBinaryField);
```

**注册功能**:
- **字段类别**: 注册到字段类别的注册表
- **字段名称**: 使用"binary_spreadsheet"作为字段标识
- **配置绑定**: 绑定字段配置对象
- **系统集成**: 集成到Odoo的字段系统

## 使用场景

### 1. 电子表格二进制字段组件增强

```javascript
// 电子表格二进制字段组件增强功能
const SpreadsheetBinaryFieldEnhancer = {
    enhanceSpreadsheetBinaryField: () => {
        // 增强的电子表格二进制字段组件
        class EnhancedSpreadsheetBinaryField extends BinaryField {
            static template = "spreadsheet.EnhancedSpreadsheetBinaryField";
            static props = {
                ...BinaryField.props,
                allowPreview: { type: Boolean, optional: true },
                allowEdit: { type: Boolean, optional: true },
                allowShare: { type: Boolean, optional: true },
                maxFileSize: { type: Number, optional: true },
                supportedFormats: { type: Array, optional: true },
                previewMode: { type: String, optional: true },
                downloadFormat: { type: String, optional: true }
            };
            
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    allowPreview: this.props.allowPreview !== false,
                    allowEdit: this.props.allowEdit !== false,
                    allowShare: this.props.allowShare !== false,
                    maxFileSize: this.props.maxFileSize || 50 * 1024 * 1024, // 50MB
                    supportedFormats: this.props.supportedFormats || [
                        'xlsx', 'xls', 'ods', 'csv', 'tsv'
                    ],
                    previewMode: this.props.previewMode || 'thumbnail',
                    downloadFormat: this.props.downloadFormat || 'xlsx'
                };
                
                // 增强的状态
                this.enhancedState = useState({
                    isPreviewLoading: false,
                    previewData: null,
                    fileInfo: null,
                    isEditing: false,
                    hasChanges: false,
                    lastModified: null
                });
                
                // 服务集成
                this.notification = useService("notification");
                this.dialog = useService("dialog");
                this.http = useService("http");
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            async initializeEnhancements() {
                // 加载文件信息
                await this.loadFileInfo();
                
                // 如果允许预览，加载预览数据
                if (this.enhancedConfig.allowPreview && this.props.value) {
                    await this.loadPreviewData();
                }
            }
            
            // 加载文件信息
            async loadFileInfo() {
                if (!this.props.value) return;
                
                try {
                    const fileInfo = await this.http.post('/web/dataset/call_kw', {
                        model: this.props.record.resModel,
                        method: 'get_spreadsheet_file_info',
                        args: [this.props.record.resId, this.props.name],
                        kwargs: {}
                    });
                    
                    this.enhancedState.fileInfo = fileInfo;
                } catch (error) {
                    console.error('加载文件信息失败:', error);
                }
            }
            
            // 加载预览数据
            async loadPreviewData() {
                if (!this.props.value || this.enhancedState.isPreviewLoading) return;
                
                try {
                    this.enhancedState.isPreviewLoading = true;
                    
                    const previewData = await this.http.post('/web/dataset/call_kw', {
                        model: this.props.record.resModel,
                        method: 'get_spreadsheet_preview',
                        args: [this.props.record.resId, this.props.name],
                        kwargs: {
                            preview_mode: this.enhancedConfig.previewMode,
                            max_rows: 100,
                            max_cols: 20
                        }
                    });
                    
                    this.enhancedState.previewData = previewData;
                } catch (error) {
                    console.error('加载预览数据失败:', error);
                    this.notification.add('预览加载失败', { type: 'error' });
                } finally {
                    this.enhancedState.isPreviewLoading = false;
                }
            }
            
            // 增强的文件下载
            async onFileDownload() {
                if (!this.props.value) {
                    this.notification.add('没有可下载的文件', { type: 'warning' });
                    return;
                }
                
                try {
                    // 检查文件大小
                    if (this.enhancedState.fileInfo?.size > this.enhancedConfig.maxFileSize) {
                        const confirmed = await this.confirmLargeFileDownload();
                        if (!confirmed) return;
                    }
                    
                    // 构建下载URL
                    const downloadUrl = this.buildDownloadUrl();
                    
                    // 记录下载事件
                    this.recordDownloadEvent();
                    
                    // 执行下载
                    const link = document.createElement('a');
                    link.href = downloadUrl;
                    link.download = this.getDownloadFileName();
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    
                    this.notification.add('文件下载已开始', { type: 'success' });
                    
                } catch (error) {
                    console.error('文件下载失败:', error);
                    this.notification.add('文件下载失败', { type: 'error' });
                }
            }
            
            // 文件预览
            async onFilePreview() {
                if (!this.enhancedConfig.allowPreview) {
                    this.notification.add('预览功能已禁用', { type: 'warning' });
                    return;
                }
                
                if (!this.enhancedState.previewData) {
                    await this.loadPreviewData();
                }
                
                if (this.enhancedState.previewData) {
                    this.dialog.add(SpreadsheetPreviewDialog, {
                        previewData: this.enhancedState.previewData,
                        fileInfo: this.enhancedState.fileInfo,
                        onEdit: this.enhancedConfig.allowEdit ? () => this.onFileEdit() : null
                    });
                }
            }
            
            // 文件编辑
            async onFileEdit() {
                if (!this.enhancedConfig.allowEdit) {
                    this.notification.add('编辑功能已禁用', { type: 'warning' });
                    return;
                }
                
                try {
                    // 检查编辑权限
                    const hasPermission = await this.checkEditPermission();
                    if (!hasPermission) {
                        this.notification.add('没有编辑权限', { type: 'error' });
                        return;
                    }
                    
                    // 打开编辑器
                    this.dialog.add(SpreadsheetEditorDialog, {
                        record: this.props.record,
                        fieldName: this.props.name,
                        fileInfo: this.enhancedState.fileInfo,
                        onSave: (data) => this.onFileSave(data),
                        onCancel: () => this.onEditCancel()
                    });
                    
                    this.enhancedState.isEditing = true;
                    
                } catch (error) {
                    console.error('打开编辑器失败:', error);
                    this.notification.add('无法打开编辑器', { type: 'error' });
                }
            }
            
            // 文件分享
            async onFileShare() {
                if (!this.enhancedConfig.allowShare) {
                    this.notification.add('分享功能已禁用', { type: 'warning' });
                    return;
                }
                
                try {
                    const shareUrl = await this.generateShareUrl();
                    
                    this.dialog.add(SpreadsheetShareDialog, {
                        shareUrl: shareUrl,
                        fileInfo: this.enhancedState.fileInfo,
                        onCopyLink: () => this.copyShareLink(shareUrl),
                        onSendEmail: (emails) => this.sendShareEmail(emails, shareUrl)
                    });
                    
                } catch (error) {
                    console.error('生成分享链接失败:', error);
                    this.notification.add('无法生成分享链接', { type: 'error' });
                }
            }
            
            // 文件上传
            async onFileUpload(file) {
                try {
                    // 验证文件格式
                    if (!this.validateFileFormat(file)) {
                        this.notification.add('不支持的文件格式', { type: 'error' });
                        return;
                    }
                    
                    // 验证文件大小
                    if (file.size > this.enhancedConfig.maxFileSize) {
                        this.notification.add('文件大小超出限制', { type: 'error' });
                        return;
                    }
                    
                    // 上传文件
                    const uploadResult = await this.uploadFile(file);
                    
                    if (uploadResult.success) {
                        // 更新字段值
                        this.props.record.update({
                            [this.props.name]: uploadResult.fileData
                        });
                        
                        // 重新加载文件信息和预览
                        await this.loadFileInfo();
                        if (this.enhancedConfig.allowPreview) {
                            await this.loadPreviewData();
                        }
                        
                        this.notification.add('文件上传成功', { type: 'success' });
                    }
                    
                } catch (error) {
                    console.error('文件上传失败:', error);
                    this.notification.add('文件上传失败', { type: 'error' });
                }
            }
            
            // 验证文件格式
            validateFileFormat(file) {
                const extension = file.name.split('.').pop().toLowerCase();
                return this.enhancedConfig.supportedFormats.includes(extension);
            }
            
            // 上传文件
            async uploadFile(file) {
                const formData = new FormData();
                formData.append('file', file);
                formData.append('model', this.props.record.resModel);
                formData.append('id', this.props.record.resId);
                formData.append('field', this.props.name);
                
                const response = await fetch('/web/binary/upload_spreadsheet', {
                    method: 'POST',
                    body: formData
                });
                
                return await response.json();
            }
            
            // 构建下载URL
            buildDownloadUrl() {
                const params = new URLSearchParams({
                    model: this.props.record.resModel,
                    id: this.props.record.resId,
                    field: this.props.name,
                    format: this.enhancedConfig.downloadFormat,
                    filename: this.getDownloadFileName()
                });
                
                return `/web/content/spreadsheet?${params.toString()}`;
            }
            
            // 获取下载文件名
            getDownloadFileName() {
                const fileInfo = this.enhancedState.fileInfo;
                if (fileInfo?.filename) {
                    return fileInfo.filename;
                }
                
                const recordName = this.props.record.data.display_name || this.props.record.resId;
                const extension = this.enhancedConfig.downloadFormat;
                return `${recordName}_${this.props.name}.${extension}`;
            }
            
            // 确认大文件下载
            async confirmLargeFileDownload() {
                return new Promise((resolve) => {
                    this.dialog.add(ConfirmDialog, {
                        title: '大文件下载确认',
                        body: '文件较大，下载可能需要一些时间。是否继续？',
                        confirm: () => resolve(true),
                        cancel: () => resolve(false)
                    });
                });
            }
            
            // 检查编辑权限
            async checkEditPermission() {
                try {
                    const result = await this.http.post('/web/dataset/call_kw', {
                        model: this.props.record.resModel,
                        method: 'check_spreadsheet_edit_permission',
                        args: [this.props.record.resId, this.props.name],
                        kwargs: {}
                    });
                    
                    return result.has_permission;
                } catch (error) {
                    console.error('检查编辑权限失败:', error);
                    return false;
                }
            }
            
            // 生成分享URL
            async generateShareUrl() {
                const result = await this.http.post('/web/dataset/call_kw', {
                    model: this.props.record.resModel,
                    method: 'generate_spreadsheet_share_url',
                    args: [this.props.record.resId, this.props.name],
                    kwargs: {}
                });
                
                return result.share_url;
            }
            
            // 记录下载事件
            recordDownloadEvent() {
                try {
                    this.http.post('/web/dataset/call_kw', {
                        model: 'spreadsheet.download.log',
                        method: 'create',
                        args: [{
                            model: this.props.record.resModel,
                            res_id: this.props.record.resId,
                            field_name: this.props.name,
                            download_time: new Date().toISOString(),
                            user_id: this.env.services.user.userId
                        }],
                        kwargs: {}
                    });
                } catch (error) {
                    console.warn('记录下载事件失败:', error);
                }
            }
            
            // 获取文件图标
            getFileIcon() {
                const fileInfo = this.enhancedState.fileInfo;
                if (!fileInfo) return 'fa-file';
                
                const iconMap = {
                    'xlsx': 'fa-file-excel-o',
                    'xls': 'fa-file-excel-o',
                    'ods': 'fa-file-excel-o',
                    'csv': 'fa-file-text-o',
                    'tsv': 'fa-file-text-o'
                };
                
                const extension = fileInfo.filename?.split('.').pop().toLowerCase();
                return iconMap[extension] || 'fa-file';
            }
            
            // 获取文件大小显示
            getFileSizeDisplay() {
                const fileInfo = this.enhancedState.fileInfo;
                if (!fileInfo?.size) return '';
                
                const size = fileInfo.size;
                if (size < 1024) return `${size} B`;
                if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
                if (size < 1024 * 1024 * 1024) return `${(size / (1024 * 1024)).toFixed(1)} MB`;
                return `${(size / (1024 * 1024 * 1024)).toFixed(1)} GB`;
            }
        }
        
        // 增强的字段配置
        const enhancedSpreadsheetBinaryField = {
            ...binaryField,
            component: EnhancedSpreadsheetBinaryField,
            supportedTypes: ["binary"],
            extractProps: ({ attrs, field }) => ({
                ...binaryField.extractProps({ attrs, field }),
                allowPreview: attrs.allow_preview,
                allowEdit: attrs.allow_edit,
                allowShare: attrs.allow_share,
                maxFileSize: attrs.max_file_size,
                supportedFormats: attrs.supported_formats?.split(','),
                previewMode: attrs.preview_mode,
                downloadFormat: attrs.download_format
            })
        };
        
        // 注册增强的字段
        registry.category("fields").add("enhanced_binary_spreadsheet", enhancedSpreadsheetBinaryField, { force: true });
        
        return {
            component: EnhancedSpreadsheetBinaryField,
            fieldConfig: enhancedSpreadsheetBinaryField
        };
    }
};

// 应用电子表格二进制字段组件增强
const enhancedField = SpreadsheetBinaryFieldEnhancer.enhanceSpreadsheetBinaryField();

// 导出增强的组件
__exports.enhancedField = enhancedField;
```

## 技术特点

### 1. 组件继承
- 继承基础二进制字段的所有功能
- 保持与标准字段的兼容性
- 扩展电子表格特有的功能

### 2. 模板定制
- 使用专门的电子表格模板
- 提供定制化的用户界面
- 支持电子表格特有的操作

### 3. 字段注册
- 集成到Odoo的字段系统
- 标准的字段注册机制
- 支持在视图中使用

### 4. 扩展性设计
- 预留下载处理方法
- 支持功能的进一步扩展
- 灵活的配置选项

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- 继承基础组件的功能
- 扩展特定的业务逻辑

### 2. 组件模式 (Component Pattern)
- 封装的UI组件
- 可重用的界面元素

### 3. 注册表模式 (Registry Pattern)
- 集中管理字段定义
- 动态字段注册

## 注意事项

1. **模板实现**: 需要实现对应的模板文件
2. **下载功能**: 需要完善下载处理逻辑
3. **权限控制**: 确保适当的权限控制
4. **文件验证**: 添加文件格式和大小验证

## 扩展建议

1. **预览功能**: 实现电子表格的预览功能
2. **编辑功能**: 添加在线编辑功能
3. **格式转换**: 支持多种格式的转换
4. **版本管理**: 实现文件版本管理
5. **协作功能**: 添加多用户协作编辑

该电子表格二进制字段组件为电子表格文件的管理提供了专门的用户界面，是电子表格系统与Odoo字段系统集成的重要桥梁。

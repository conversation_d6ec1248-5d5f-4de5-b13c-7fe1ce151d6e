# Form Controller with HTML Expander - 带HTML扩展器的表单控制器

## 概述

`form_controller_with_html_expander.js` 是 Odoo Resource 模块的表单控制器扩展，专门用于支持HTML字段的扩展显示功能。该控制器基于Odoo的标准表单控制器，通过状态管理和页面切换监听，为HTML字段提供了展开/收起功能的协调机制，提升了包含大量HTML内容的表单的用户体验。

## 文件信息
- **路径**: `/resource/static/src/views/form_with_html_expander/form_controller_with_html_expander.js`
- **行数**: 28
- **模块**: `@resource/views/form_with_html_expander/form_controller_with_html_expander`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                             // OWL框架
'@web/views/form/form_controller'       // 标准表单控制器
```

## 核心功能

### 1. 控制器定义

```javascript
const FormControllerWithHTMLExpander = __exports.FormControllerWithHTMLExpander = class FormControllerWithHTMLExpander extends FormController {
    static template = "resource.FormViewWithHtmlExpander";

    setup() {
        super.setup();
        this.htmlExpanderState = useState({ reload: true });
        const oldOnNotebookPageChange = this.onNotebookPageChange;
        this.onNotebookPageChange = (notebookId, page) => {
            oldOnNotebookPageChange(notebookId, page);
            if (page && !this.htmlExpanderState.reload) {
                this.htmlExpanderState.reload = true;
            }
        };
    }
```

**控制器特性**:
- **继承扩展**: 继承FormController的所有功能
- **模板定制**: 使用专门的HTML扩展器表单模板
- **状态管理**: 使用useState管理HTML扩展器状态
- **页面监听**: 监听笔记本页面切换事件
- **方法重写**: 重写页面切换处理方法

### 2. HTML扩展器状态管理

```javascript
this.htmlExpanderState = useState({ reload: true });
```

**状态管理功能**:
- **响应式状态**: 使用OWL的useState创建响应式状态
- **重载标志**: 控制HTML字段的重载行为
- **初始状态**: 默认设置为需要重载
- **状态同步**: 与HTML字段组件的状态同步

### 3. 页面切换处理

```javascript
const oldOnNotebookPageChange = this.onNotebookPageChange;
this.onNotebookPageChange = (notebookId, page) => {
    oldOnNotebookPageChange(notebookId, page);
    if (page && !this.htmlExpanderState.reload) {
        this.htmlExpanderState.reload = true;
    }
};
```

**页面切换功能**:
- **方法保存**: 保存原始的页面切换方法
- **方法重写**: 重写页面切换处理逻辑
- **原方法调用**: 确保原有功能正常执行
- **状态更新**: 根据页面切换更新重载状态
- **条件判断**: 只在特定条件下更新状态

### 4. HTML字段扩展通知

```javascript
notifyHTMLFieldExpanded() {
    this.htmlExpanderState.reload = false;
}
```

**通知功能**:
- **状态更新**: 设置重载标志为false
- **扩展通知**: 通知HTML字段已经扩展
- **性能优化**: 避免不必要的重载操作
- **简洁接口**: 提供简单的通知接口

## 使用场景

### 1. 带HTML扩展器的表单控制器增强

```javascript
// 带HTML扩展器的表单控制器增强功能
const FormControllerWithHTMLExpanderEnhancer = {
    enhanceFormControllerWithHTMLExpander: () => {
        // 增强的表单控制器
        class EnhancedFormControllerWithHTMLExpander extends FormControllerWithHTMLExpander {
            static template = "resource.EnhancedFormViewWithHtmlExpander";
            
            setup() {
                super.setup();

                // 增强的HTML扩展器状态
                this.enhancedHtmlExpanderState = useState({
                    reload: true,
                    expandedFields: new Set(),
                    autoExpand: false,
                    expandMode: 'manual', // manual, auto, smart
                    maxExpandedFields: 3,
                    expandHistory: [],
                    fieldStates: new Map(),
                    expandAnimations: true,
                    persistState: true
                });

                // HTML字段管理器
                this.htmlFieldManager = new HTMLFieldManager(this);

                // 扩展器配置
                this.expanderConfig = {
                    enableAutoExpand: true,
                    enableSmartExpand: true,
                    enablePersistence: true,
                    enableAnimations: true,
                    maxConcurrentExpanded: 3,
                    expandThreshold: 500, // 字符数阈值
                    collapseDelay: 5000, // 自动收起延迟
                    enableKeyboardShortcuts: true
                };

                // 初始化增强功能
                this.initializeEnhancements();
            }

            // 初始化增强功能
            initializeEnhancements() {
                // 恢复持久化状态
                if (this.expanderConfig.enablePersistence) {
                    this.restoreExpanderState();
                }

                // 设置键盘快捷键
                if (this.expanderConfig.enableKeyboardShortcuts) {
                    this.setupKeyboardShortcuts();
                }

                // 设置自动扩展
                if (this.expanderConfig.enableAutoExpand) {
                    this.setupAutoExpand();
                }

                // 监听字段变化
                this.setupFieldChangeListeners();
            }

            // 增强的页面切换处理
            onNotebookPageChange(notebookId, page) {
                // 调用父类方法
                super.onNotebookPageChange(notebookId, page);

                // 处理页面切换时的HTML字段状态
                if (page) {
                    this.handlePageChangeForHTMLFields(notebookId, page);
                }
            }

            // 处理页面切换时的HTML字段状态
            handlePageChangeForHTMLFields(notebookId, page) {
                // 获取当前页面的HTML字段
                const htmlFields = this.getHTMLFieldsInPage(page);

                // 根据扩展模式处理字段
                switch (this.enhancedHtmlExpanderState.expandMode) {
                    case 'auto':
                        this.autoExpandHTMLFields(htmlFields);
                        break;
                    case 'smart':
                        this.smartExpandHTMLFields(htmlFields);
                        break;
                    case 'manual':
                    default:
                        this.prepareHTMLFieldsForManualExpand(htmlFields);
                        break;
                }

                // 记录页面切换历史
                this.recordPageChange(notebookId, page);
            }

            // 获取页面中的HTML字段
            getHTMLFieldsInPage(page) {
                const htmlFields = [];
                
                // 查找页面中的所有HTML字段
                const pageElement = page.el || page;
                if (pageElement) {
                    const htmlFieldElements = pageElement.querySelectorAll('.o_field_html');
                    htmlFieldElements.forEach(element => {
                        const fieldName = element.getAttribute('name');
                        if (fieldName) {
                            htmlFields.push({
                                name: fieldName,
                                element: element,
                                content: this.getFieldContent(fieldName)
                            });
                        }
                    });
                }

                return htmlFields;
            }

            // 自动扩展HTML字段
            autoExpandHTMLFields(htmlFields) {
                let expandedCount = 0;
                const maxExpanded = this.expanderConfig.maxConcurrentExpanded;

                for (const field of htmlFields) {
                    if (expandedCount >= maxExpanded) break;

                    if (this.shouldAutoExpand(field)) {
                        this.expandHTMLField(field.name);
                        expandedCount++;
                    }
                }
            }

            // 智能扩展HTML字段
            smartExpandHTMLFields(htmlFields) {
                // 根据内容长度和用户历史行为智能扩展
                const scoredFields = htmlFields.map(field => ({
                    ...field,
                    score: this.calculateExpandScore(field)
                }));

                // 按分数排序
                scoredFields.sort((a, b) => b.score - a.score);

                // 扩展高分字段
                const maxExpanded = this.expanderConfig.maxConcurrentExpanded;
                for (let i = 0; i < Math.min(maxExpanded, scoredFields.length); i++) {
                    if (scoredFields[i].score > 0.5) { // 阈值
                        this.expandHTMLField(scoredFields[i].name);
                    }
                }
            }

            // 计算扩展分数
            calculateExpandScore(field) {
                let score = 0;

                // 内容长度分数
                const contentLength = field.content ? field.content.length : 0;
                if (contentLength > this.expanderConfig.expandThreshold) {
                    score += 0.3;
                }

                // 历史扩展频率分数
                const expandHistory = this.enhancedHtmlExpanderState.expandHistory;
                const fieldExpandCount = expandHistory.filter(h => h.fieldName === field.name).length;
                score += Math.min(fieldExpandCount * 0.1, 0.4);

                // 最近访问分数
                const recentExpand = expandHistory
                    .filter(h => h.fieldName === field.name)
                    .sort((a, b) => b.timestamp - a.timestamp)[0];

                if (recentExpand) {
                    const timeDiff = Date.now() - recentExpand.timestamp;
                    const daysDiff = timeDiff / (1000 * 60 * 60 * 24);
                    if (daysDiff < 7) {
                        score += 0.3 * (1 - daysDiff / 7);
                    }
                }

                return Math.min(score, 1);
            }

            // 判断是否应该自动扩展
            shouldAutoExpand(field) {
                const content = field.content || '';
                return content.length > this.expanderConfig.expandThreshold;
            }

            // 扩展HTML字段
            expandHTMLField(fieldName) {
                if (this.enhancedHtmlExpanderState.expandedFields.has(fieldName)) {
                    return; // 已经扩展
                }

                // 检查最大扩展数量限制
                if (this.enhancedHtmlExpanderState.expandedFields.size >= this.expanderConfig.maxConcurrentExpanded) {
                    this.collapseOldestField();
                }

                // 扩展字段
                this.enhancedHtmlExpanderState.expandedFields.add(fieldName);
                this.enhancedHtmlExpanderState.fieldStates.set(fieldName, {
                    expanded: true,
                    expandedAt: Date.now(),
                    expandCount: (this.enhancedHtmlExpanderState.fieldStates.get(fieldName)?.expandCount || 0) + 1
                });

                // 记录扩展历史
                this.enhancedHtmlExpanderState.expandHistory.push({
                    fieldName: fieldName,
                    action: 'expand',
                    timestamp: Date.now()
                });

                // 触发扩展事件
                this.triggerFieldExpanded(fieldName);

                // 保存状态
                if (this.expanderConfig.enablePersistence) {
                    this.saveExpanderState();
                }
            }

            // 收起HTML字段
            collapseHTMLField(fieldName) {
                if (!this.enhancedHtmlExpanderState.expandedFields.has(fieldName)) {
                    return; // 未扩展
                }

                // 收起字段
                this.enhancedHtmlExpanderState.expandedFields.delete(fieldName);
                this.enhancedHtmlExpanderState.fieldStates.set(fieldName, {
                    ...this.enhancedHtmlExpanderState.fieldStates.get(fieldName),
                    expanded: false,
                    collapsedAt: Date.now()
                });

                // 记录收起历史
                this.enhancedHtmlExpanderState.expandHistory.push({
                    fieldName: fieldName,
                    action: 'collapse',
                    timestamp: Date.now()
                });

                // 触发收起事件
                this.triggerFieldCollapsed(fieldName);

                // 保存状态
                if (this.expanderConfig.enablePersistence) {
                    this.saveExpanderState();
                }
            }

            // 收起最旧的字段
            collapseOldestField() {
                let oldestField = null;
                let oldestTime = Date.now();

                for (const [fieldName, state] of this.enhancedHtmlExpanderState.fieldStates) {
                    if (state.expanded && state.expandedAt < oldestTime) {
                        oldestTime = state.expandedAt;
                        oldestField = fieldName;
                    }
                }

                if (oldestField) {
                    this.collapseHTMLField(oldestField);
                }
            }

            // 切换HTML字段扩展状态
            toggleHTMLField(fieldName) {
                if (this.enhancedHtmlExpanderState.expandedFields.has(fieldName)) {
                    this.collapseHTMLField(fieldName);
                } else {
                    this.expandHTMLField(fieldName);
                }
            }

            // 增强的HTML字段扩展通知
            notifyHTMLFieldExpanded(fieldName) {
                super.notifyHTMLFieldExpanded();
                
                // 更新字段状态
                if (fieldName) {
                    this.enhancedHtmlExpanderState.expandedFields.add(fieldName);
                    this.enhancedHtmlExpanderState.fieldStates.set(fieldName, {
                        expanded: true,
                        expandedAt: Date.now(),
                        notified: true
                    });
                }
            }

            // 设置键盘快捷键
            setupKeyboardShortcuts() {
                this.keyboardHandler = (event) => {
                    // Ctrl+E: 扩展/收起当前焦点的HTML字段
                    if (event.ctrlKey && event.key === 'e') {
                        const focusedField = this.getFocusedHTMLField();
                        if (focusedField) {
                            this.toggleHTMLField(focusedField);
                            event.preventDefault();
                        }
                    }

                    // Ctrl+Shift+E: 扩展所有HTML字段
                    if (event.ctrlKey && event.shiftKey && event.key === 'E') {
                        this.expandAllHTMLFields();
                        event.preventDefault();
                    }

                    // Ctrl+Shift+C: 收起所有HTML字段
                    if (event.ctrlKey && event.shiftKey && event.key === 'C') {
                        this.collapseAllHTMLFields();
                        event.preventDefault();
                    }
                };

                document.addEventListener('keydown', this.keyboardHandler);
            }

            // 获取当前焦点的HTML字段
            getFocusedHTMLField() {
                const activeElement = document.activeElement;
                const htmlField = activeElement.closest('.o_field_html');
                return htmlField ? htmlField.getAttribute('name') : null;
            }

            // 扩展所有HTML字段
            expandAllHTMLFields() {
                const htmlFields = this.getAllHTMLFields();
                htmlFields.forEach(field => this.expandHTMLField(field.name));
            }

            // 收起所有HTML字段
            collapseAllHTMLFields() {
                const expandedFields = Array.from(this.enhancedHtmlExpanderState.expandedFields);
                expandedFields.forEach(fieldName => this.collapseHTMLField(fieldName));
            }

            // 获取所有HTML字段
            getAllHTMLFields() {
                const htmlFields = [];
                const htmlFieldElements = document.querySelectorAll('.o_field_html');
                
                htmlFieldElements.forEach(element => {
                    const fieldName = element.getAttribute('name');
                    if (fieldName) {
                        htmlFields.push({
                            name: fieldName,
                            element: element
                        });
                    }
                });

                return htmlFields;
            }

            // 获取字段内容
            getFieldContent(fieldName) {
                const record = this.model.root;
                return record.data[fieldName] || '';
            }

            // 触发字段扩展事件
            triggerFieldExpanded(fieldName) {
                this.env.bus.trigger('html-field-expanded', {
                    fieldName: fieldName,
                    controller: this
                });
            }

            // 触发字段收起事件
            triggerFieldCollapsed(fieldName) {
                this.env.bus.trigger('html-field-collapsed', {
                    fieldName: fieldName,
                    controller: this
                });
            }

            // 保存扩展器状态
            saveExpanderState() {
                try {
                    const state = {
                        expandedFields: Array.from(this.enhancedHtmlExpanderState.expandedFields),
                        expandMode: this.enhancedHtmlExpanderState.expandMode,
                        expandHistory: this.enhancedHtmlExpanderState.expandHistory.slice(-50), // 保留最近50条
                        fieldStates: Object.fromEntries(this.enhancedHtmlExpanderState.fieldStates)
                    };

                    localStorage.setItem(
                        `html_expander_state_${this.props.resModel}`,
                        JSON.stringify(state)
                    );
                } catch (error) {
                    console.warn('保存扩展器状态失败:', error);
                }
            }

            // 恢复扩展器状态
            restoreExpanderState() {
                try {
                    const saved = localStorage.getItem(`html_expander_state_${this.props.resModel}`);
                    if (saved) {
                        const state = JSON.parse(saved);
                        
                        this.enhancedHtmlExpanderState.expandedFields = new Set(state.expandedFields || []);
                        this.enhancedHtmlExpanderState.expandMode = state.expandMode || 'manual';
                        this.enhancedHtmlExpanderState.expandHistory = state.expandHistory || [];
                        this.enhancedHtmlExpanderState.fieldStates = new Map(Object.entries(state.fieldStates || {}));
                    }
                } catch (error) {
                    console.warn('恢复扩展器状态失败:', error);
                }
            }

            // 记录页面切换
            recordPageChange(notebookId, page) {
                // 记录页面切换用于分析
                console.log('页面切换:', notebookId, page);
            }

            // 设置自动扩展
            setupAutoExpand() {
                // 监听字段内容变化
                this.env.bus.addEventListener('field-changed', (event) => {
                    const { fieldName, value } = event.detail;
                    if (this.isHTMLField(fieldName) && value && value.length > this.expanderConfig.expandThreshold) {
                        if (this.enhancedHtmlExpanderState.autoExpand) {
                            this.expandHTMLField(fieldName);
                        }
                    }
                });
            }

            // 设置字段变化监听
            setupFieldChangeListeners() {
                // 监听模型变化
                this.model.addEventListener('update', (event) => {
                    this.handleModelUpdate(event);
                });
            }

            // 处理模型更新
            handleModelUpdate(event) {
                // 检查HTML字段的变化
                const changedFields = event.detail.changedFields || [];
                changedFields.forEach(fieldName => {
                    if (this.isHTMLField(fieldName)) {
                        this.handleHTMLFieldChange(fieldName);
                    }
                });
            }

            // 处理HTML字段变化
            handleHTMLFieldChange(fieldName) {
                const content = this.getFieldContent(fieldName);
                const isExpanded = this.enhancedHtmlExpanderState.expandedFields.has(fieldName);

                // 如果内容变短且当前已扩展，考虑自动收起
                if (isExpanded && content.length < this.expanderConfig.expandThreshold / 2) {
                    setTimeout(() => {
                        this.collapseHTMLField(fieldName);
                    }, this.expanderConfig.collapseDelay);
                }
            }

            // 检查是否为HTML字段
            isHTMLField(fieldName) {
                const field = this.props.archInfo.fieldNodes[fieldName];
                return field && field.widget === 'html';
            }

            // 组件销毁时清理
            willDestroy() {
                // 清理键盘监听
                if (this.keyboardHandler) {
                    document.removeEventListener('keydown', this.keyboardHandler);
                }

                // 保存状态
                if (this.expanderConfig.enablePersistence) {
                    this.saveExpanderState();
                }

                super.willDestroy && super.willDestroy();
            }
        }

        // HTML字段管理器
        class HTMLFieldManager {
            constructor(controller) {
                this.controller = controller;
                this.fields = new Map();
            }

            registerField(fieldName, fieldComponent) {
                this.fields.set(fieldName, fieldComponent);
            }

            unregisterField(fieldName) {
                this.fields.delete(fieldName);
            }

            getField(fieldName) {
                return this.fields.get(fieldName);
            }

            getAllFields() {
                return Array.from(this.fields.values());
            }

            expandField(fieldName) {
                const field = this.getField(fieldName);
                if (field && field.expand) {
                    field.expand();
                }
            }

            collapseField(fieldName) {
                const field = this.getField(fieldName);
                if (field && field.collapse) {
                    field.collapse();
                }
            }
        }

        return EnhancedFormControllerWithHTMLExpander;
    }
};

// 应用表单控制器增强
const enhancedFormController = FormControllerWithHTMLExpanderEnhancer.enhanceFormControllerWithHTMLExpander();

// 导出增强的控制器
__exports.enhancedFormController = enhancedFormController;
```

## 技术特点

### 1. 控制器扩展
- 继承FormController的所有功能
- 保持与标准表单的兼容性
- 添加HTML扩展器特有功能

### 2. 状态管理
- 使用OWL的useState管理状态
- 响应式的状态更新
- 简洁的状态接口

### 3. 事件处理
- 监听页面切换事件
- 重写事件处理方法
- 保持原有功能完整性

### 4. 通知机制
- 提供HTML字段扩展通知
- 简洁的通知接口
- 状态同步机制

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 在标准表单控制器基础上添加HTML扩展功能
- 保持原有接口不变

### 2. 观察者模式 (Observer Pattern)
- 监听页面切换事件
- 响应状态变化

### 3. 状态模式 (State Pattern)
- 管理HTML扩展器的不同状态
- 根据状态执行不同行为

## 注意事项

1. **性能考虑**: 避免频繁的状态更新和重渲染
2. **内存管理**: 及时清理事件监听器和状态
3. **兼容性**: 确保与现有表单功能的兼容性
4. **用户体验**: 提供流畅的扩展/收起体验

## 扩展建议

1. **动画效果**: 添加扩展/收起的动画效果
2. **键盘快捷键**: 支持键盘快捷键操作
3. **状态持久化**: 保存用户的扩展偏好
4. **批量操作**: 支持批量扩展/收起操作
5. **智能扩展**: 根据内容长度智能决定是否扩展

该表单控制器扩展为Odoo资源管理提供了重要的HTML内容显示优化功能，通过智能的状态管理提升了包含大量HTML内容的表单的用户体验。

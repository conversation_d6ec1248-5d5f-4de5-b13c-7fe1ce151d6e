# Form Renderer with HTML Expander - 带HTML扩展器的表单渲染器

## 概述

`form_renderer_with_html_expander.js` 是 Odoo Resource 模块的表单渲染器扩展，专门用于支持HTML字段的动态高度调整和扩展显示功能。该渲染器基于Odoo的标准表单渲染器，通过智能的高度计算和响应式调整，为HTML字段提供了最佳的显示体验，特别适用于包含大量HTML内容的表单。

## 文件信息
- **路径**: `/resource/static/src/views/form_with_html_expander/form_renderer_with_html_expander.js`
- **行数**: 71
- **模块**: `@resource/views/form_with_html_expander/form_renderer_with_html_expander`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/hooks'         // 钩子工具
'@web/views/form/form_renderer' // 标准表单渲染器
'@odoo/owl'                     // OWL框架
```

## 核心功能

### 1. 渲染器定义

```javascript
const FormRendererWithHtmlExpander = __exports.FormRendererWithHtmlExpander = class FormRendererWithHtmlExpander extends FormRenderer {
    static props = {
        ...FormRenderer.props,
        reloadHtmlFieldHeight: { type: Boolean, optional: true },
        notifyHtmlExpander: { type: Function, optional: true },
    };
    static defaultProps = {
        ...FormRenderer.defaultProps,
        reloadHtmlFieldHeight: true,
        notifyHtmlExpander: () => {},
    };
```

**渲染器特性**:
- **继承扩展**: 继承FormRenderer的所有功能
- **属性扩展**: 添加HTML字段高度重载和通知回调属性
- **默认配置**: 设置合理的默认属性值
- **可选属性**: 所有新增属性都是可选的，保持向后兼容

### 2. 组件初始化

```javascript
setup() {
    super.setup();
    if (!this.uiService) {
        // Should be defined in FormRenderer
        this.uiService = useService("ui");
    }
    const ref = useRef("compiled_view_root");
    useEffect(
        (el, size) => {
            // 高度调整逻辑
        },
        () => [
            ref.el,
            this.uiService.size,
            this.props.reloadHtmlFieldHeight,
            this.props.record.resId,
        ]
    );
}
```

**初始化功能**:
- **父类初始化**: 调用父类的setup方法
- **服务注入**: 确保UI服务的可用性
- **引用创建**: 创建编译视图根元素的引用
- **效果监听**: 使用useEffect监听相关状态变化
- **依赖数组**: 精确定义效果的依赖项

### 3. 动态高度调整

```javascript
useEffect(
    (el, size) => {
        if (el && size === 6) {
            const descriptionField = el.querySelector(this.htmlFieldQuerySelector);
            if (descriptionField) {
                const containerEL = descriptionField.closest(
                    this.getHTMLFieldContainerQuerySelector
                );
                const editor = descriptionField.querySelector(".note-editable");
                const elementToResize = editor || descriptionField;
                const { top, bottom } = elementToResize.getBoundingClientRect();
                const { bottom: containerBottom } = containerEL.getBoundingClientRect();
                const { paddingTop, paddingBottom } = window.getComputedStyle(containerEL);
                const nonEditableHeight =
                    containerBottom -
                    bottom +
                    parseInt(paddingTop) +
                    parseInt(paddingBottom);
                const minHeight =
                    document.documentElement.clientHeight - top - nonEditableHeight;
                elementToResize.style.minHeight = `${minHeight}px`;
            }
        }
        this.props.notifyHtmlExpander();
    },
    // 依赖数组
);
```

**高度调整功能**:
- **条件检查**: 只在特定UI尺寸下执行调整
- **元素查找**: 查找HTML字段和编辑器元素
- **容器定位**: 找到HTML字段的容器元素
- **尺寸计算**: 精确计算元素的边界矩形
- **样式获取**: 获取容器的内边距样式
- **高度计算**: 计算最佳的最小高度
- **样式应用**: 应用计算出的最小高度
- **通知回调**: 调用扩展器通知回调

### 4. 查询选择器

```javascript
get htmlFieldQuerySelector() {
    return ".o_field_html[name=description]";
}

get getHTMLFieldContainerQuerySelector() {
    return ".o_form_sheet";
}
```

**选择器功能**:
- **HTML字段选择器**: 定位名为"description"的HTML字段
- **容器选择器**: 定位表单工作表容器
- **可扩展性**: 通过getter方法提供可重写的选择器
- **精确定位**: 确保选择器的准确性和特异性

## 使用场景

### 1. 带HTML扩展器的表单渲染器增强

```javascript
// 带HTML扩展器的表单渲染器增强功能
const FormRendererWithHtmlExpanderEnhancer = {
    enhanceFormRendererWithHtmlExpander: () => {
        // 增强的表单渲染器
        class EnhancedFormRendererWithHtmlExpander extends FormRendererWithHtmlExpander {
            static props = {
                ...FormRendererWithHtmlExpander.props,
                enableMultipleHtmlFields: { type: Boolean, optional: true },
                htmlFieldConfigs: { type: Array, optional: true },
                enableAutoResize: { type: Boolean, optional: true },
                enableFullscreen: { type: Boolean, optional: true },
                resizeThreshold: { type: Number, optional: true },
                enableSmoothTransition: { type: Boolean, optional: true },
                customSelectors: { type: Object, optional: true },
                enableResponsiveResize: { type: Boolean, optional: true }
            };

            static defaultProps = {
                ...FormRendererWithHtmlExpander.defaultProps,
                enableMultipleHtmlFields: false,
                htmlFieldConfigs: [],
                enableAutoResize: true,
                enableFullscreen: false,
                resizeThreshold: 100,
                enableSmoothTransition: true,
                customSelectors: {},
                enableResponsiveResize: true
            };

            setup() {
                super.setup();

                // 增强的配置选项
                this.enhancedConfig = {
                    enableMultipleHtmlFields: this.props.enableMultipleHtmlFields,
                    htmlFieldConfigs: this.props.htmlFieldConfigs,
                    enableAutoResize: this.props.enableAutoResize,
                    enableFullscreen: this.props.enableFullscreen,
                    resizeThreshold: this.props.resizeThreshold,
                    enableSmoothTransition: this.props.enableSmoothTransition,
                    customSelectors: this.props.customSelectors,
                    enableResponsiveResize: this.props.enableResponsiveResize
                };

                // 增强的状态
                this.enhancedState = useState({
                    htmlFields: new Map(),
                    resizeObserver: null,
                    fullscreenField: null,
                    lastResizeTime: 0,
                    isResizing: false
                });

                // HTML字段管理器
                this.htmlFieldManager = new HTMLFieldManager(this);

                // 初始化增强功能
                this.initializeEnhancements();
            }

            // 初始化增强功能
            initializeEnhancements() {
                // 初始化多字段支持
                if (this.enhancedConfig.enableMultipleHtmlFields) {
                    this.initializeMultipleHtmlFields();
                }

                // 初始化响应式调整
                if (this.enhancedConfig.enableResponsiveResize) {
                    this.initializeResponsiveResize();
                }

                // 初始化全屏支持
                if (this.enhancedConfig.enableFullscreen) {
                    this.initializeFullscreenSupport();
                }

                // 初始化平滑过渡
                if (this.enhancedConfig.enableSmoothTransition) {
                    this.initializeSmoothTransition();
                }
            }

            // 增强的高度调整效果
            setupEnhancedHeightAdjustment() {
                const ref = useRef("compiled_view_root");
                
                useEffect(
                    (el, size, reloadHeight, resId) => {
                        if (el && this.enhancedConfig.enableAutoResize) {
                            this.adjustAllHtmlFields(el, size);
                        }
                        this.props.notifyHtmlExpander();
                    },
                    () => [
                        ref.el,
                        this.uiService.size,
                        this.props.reloadHtmlFieldHeight,
                        this.props.record.resId,
                        this.enhancedState.isResizing
                    ]
                );
            }

            // 调整所有HTML字段
            adjustAllHtmlFields(rootElement, uiSize) {
                const htmlFields = this.findAllHtmlFields(rootElement);
                
                for (const field of htmlFields) {
                    this.adjustSingleHtmlField(field, uiSize);
                }
            }

            // 查找所有HTML字段
            findAllHtmlFields(rootElement) {
                const fields = [];
                
                // 默认字段
                const defaultField = rootElement.querySelector(this.htmlFieldQuerySelector);
                if (defaultField) {
                    fields.push({
                        element: defaultField,
                        name: 'description',
                        config: this.getFieldConfig('description')
                    });
                }

                // 配置的字段
                if (this.enhancedConfig.enableMultipleHtmlFields) {
                    for (const config of this.enhancedConfig.htmlFieldConfigs) {
                        const selector = config.selector || `.o_field_html[name=${config.name}]`;
                        const element = rootElement.querySelector(selector);
                        if (element) {
                            fields.push({
                                element: element,
                                name: config.name,
                                config: config
                            });
                        }
                    }
                }

                // 自定义选择器
                for (const [name, selector] of Object.entries(this.enhancedConfig.customSelectors)) {
                    const element = rootElement.querySelector(selector);
                    if (element) {
                        fields.push({
                            element: element,
                            name: name,
                            config: this.getFieldConfig(name)
                        });
                    }
                }

                return fields;
            }

            // 调整单个HTML字段
            adjustSingleHtmlField(field, uiSize) {
                const { element, name, config } = field;
                
                try {
                    // 检查是否需要调整
                    if (!this.shouldAdjustField(field, uiSize)) {
                        return;
                    }

                    // 获取容器
                    const containerSelector = config.containerSelector || this.getHTMLFieldContainerQuerySelector;
                    const container = element.closest(containerSelector);
                    if (!container) {
                        console.warn(`容器未找到: ${name}`);
                        return;
                    }

                    // 获取编辑器元素
                    const editor = element.querySelector(".note-editable") || 
                                 element.querySelector(".o_field_html_editor") ||
                                 element;

                    // 计算尺寸
                    const dimensions = this.calculateFieldDimensions(editor, container, config);
                    
                    // 应用尺寸
                    this.applyFieldDimensions(editor, dimensions, config);

                    // 记录字段状态
                    this.enhancedState.htmlFields.set(name, {
                        element: element,
                        editor: editor,
                        container: container,
                        dimensions: dimensions,
                        lastAdjusted: Date.now()
                    });

                } catch (error) {
                    console.error(`调整HTML字段失败: ${name}`, error);
                }
            }

            // 检查是否需要调整字段
            shouldAdjustField(field, uiSize) {
                const { config } = field;
                
                // 检查UI尺寸条件
                if (config.uiSizeCondition && !config.uiSizeCondition(uiSize)) {
                    return false;
                }

                // 检查最小调整间隔
                const lastAdjusted = this.enhancedState.htmlFields.get(field.name)?.lastAdjusted || 0;
                const timeSinceLastAdjust = Date.now() - lastAdjusted;
                if (timeSinceLastAdjust < this.enhancedConfig.resizeThreshold) {
                    return false;
                }

                return true;
            }

            // 计算字段尺寸
            calculateFieldDimensions(editor, container, config) {
                const editorRect = editor.getBoundingClientRect();
                const containerRect = container.getBoundingClientRect();
                const containerStyle = window.getComputedStyle(container);

                // 基础计算
                const paddingTop = parseInt(containerStyle.paddingTop) || 0;
                const paddingBottom = parseInt(containerStyle.paddingBottom) || 0;
                const marginTop = parseInt(containerStyle.marginTop) || 0;
                const marginBottom = parseInt(containerStyle.marginBottom) || 0;

                // 非编辑区域高度
                const nonEditableHeight = 
                    containerRect.bottom - editorRect.bottom +
                    paddingTop + paddingBottom + marginTop + marginBottom;

                // 可用高度
                const availableHeight = document.documentElement.clientHeight - editorRect.top - nonEditableHeight;

                // 应用配置的调整
                let minHeight = availableHeight;
                if (config.minHeightRatio) {
                    minHeight = Math.max(minHeight, document.documentElement.clientHeight * config.minHeightRatio);
                }
                if (config.maxHeightRatio) {
                    minHeight = Math.min(minHeight, document.documentElement.clientHeight * config.maxHeightRatio);
                }
                if (config.minHeightPx) {
                    minHeight = Math.max(minHeight, config.minHeightPx);
                }
                if (config.maxHeightPx) {
                    minHeight = Math.min(minHeight, config.maxHeightPx);
                }

                return {
                    minHeight: Math.max(minHeight, 100), // 最小100px
                    availableHeight: availableHeight,
                    nonEditableHeight: nonEditableHeight
                };
            }

            // 应用字段尺寸
            applyFieldDimensions(editor, dimensions, config) {
                // 应用最小高度
                editor.style.minHeight = `${dimensions.minHeight}px`;

                // 应用平滑过渡
                if (this.enhancedConfig.enableSmoothTransition) {
                    editor.style.transition = config.transition || 'min-height 0.3s ease';
                }

                // 应用自定义样式
                if (config.customStyles) {
                    Object.assign(editor.style, config.customStyles);
                }
            }

            // 获取字段配置
            getFieldConfig(fieldName) {
                // 查找配置的字段
                const configured = this.enhancedConfig.htmlFieldConfigs.find(c => c.name === fieldName);
                if (configured) {
                    return configured;
                }

                // 返回默认配置
                return {
                    name: fieldName,
                    minHeightRatio: 0.3,
                    maxHeightRatio: 0.8,
                    enableResize: true,
                    enableFullscreen: false,
                    transition: 'min-height 0.3s ease'
                };
            }

            // 初始化多字段支持
            initializeMultipleHtmlFields() {
                // 为每个配置的字段设置监听
                for (const config of this.enhancedConfig.htmlFieldConfigs) {
                    this.setupFieldSpecificHandling(config);
                }
            }

            // 设置字段特定处理
            setupFieldSpecificHandling(config) {
                // 这里可以为特定字段设置特殊的处理逻辑
                console.log(`设置字段处理: ${config.name}`);
            }

            // 初始化响应式调整
            initializeResponsiveResize() {
                if (window.ResizeObserver) {
                    this.enhancedState.resizeObserver = new ResizeObserver((entries) => {
                        this.handleResize(entries);
                    });

                    // 观察文档元素
                    this.enhancedState.resizeObserver.observe(document.documentElement);
                }

                // 监听窗口大小变化
                this.windowResizeHandler = this.debounce(() => {
                    this.handleWindowResize();
                }, 100);

                window.addEventListener('resize', this.windowResizeHandler);
            }

            // 处理大小变化
            handleResize(entries) {
                if (this.enhancedState.isResizing) return;

                this.enhancedState.isResizing = true;
                this.enhancedState.lastResizeTime = Date.now();

                // 延迟处理以避免频繁调整
                setTimeout(() => {
                    this.adjustAllHtmlFields(document.querySelector('.o_form_view'), this.uiService.size);
                    this.enhancedState.isResizing = false;
                }, 50);
            }

            // 处理窗口大小变化
            handleWindowResize() {
                const rootElement = document.querySelector('.o_form_view');
                if (rootElement) {
                    this.adjustAllHtmlFields(rootElement, this.uiService.size);
                }
            }

            // 初始化全屏支持
            initializeFullscreenSupport() {
                // 监听全屏事件
                document.addEventListener('fullscreenchange', () => {
                    this.handleFullscreenChange();
                });

                // 添加全屏按钮
                this.addFullscreenButtons();
            }

            // 处理全屏变化
            handleFullscreenChange() {
                if (document.fullscreenElement) {
                    // 进入全屏
                    this.handleEnterFullscreen();
                } else {
                    // 退出全屏
                    this.handleExitFullscreen();
                }
            }

            // 进入全屏
            handleEnterFullscreen() {
                const fullscreenElement = document.fullscreenElement;
                if (fullscreenElement && fullscreenElement.classList.contains('o_field_html')) {
                    fullscreenElement.classList.add('o_html_field_fullscreen');
                    this.adjustSingleHtmlField({
                        element: fullscreenElement,
                        name: fullscreenElement.getAttribute('name') || 'unknown',
                        config: this.getFieldConfig(fullscreenElement.getAttribute('name'))
                    }, this.uiService.size);
                }
            }

            // 退出全屏
            handleExitFullscreen() {
                const fullscreenFields = document.querySelectorAll('.o_html_field_fullscreen');
                fullscreenFields.forEach(field => {
                    field.classList.remove('o_html_field_fullscreen');
                });

                // 重新调整所有字段
                setTimeout(() => {
                    const rootElement = document.querySelector('.o_form_view');
                    if (rootElement) {
                        this.adjustAllHtmlFields(rootElement, this.uiService.size);
                    }
                }, 100);
            }

            // 添加全屏按钮
            addFullscreenButtons() {
                // 为支持全屏的字段添加全屏按钮
                const htmlFields = document.querySelectorAll('.o_field_html');
                htmlFields.forEach(field => {
                    const fieldName = field.getAttribute('name');
                    const config = this.getFieldConfig(fieldName);
                    
                    if (config.enableFullscreen) {
                        this.addFullscreenButton(field);
                    }
                });
            }

            // 添加全屏按钮到字段
            addFullscreenButton(field) {
                const button = document.createElement('button');
                button.className = 'btn btn-sm btn-outline-secondary o_html_fullscreen_btn';
                button.innerHTML = '<i class="fa fa-expand"></i>';
                button.title = _t('Fullscreen');
                
                button.addEventListener('click', () => {
                    this.toggleFullscreen(field);
                });

                // 找到合适的位置插入按钮
                const toolbar = field.querySelector('.note-toolbar') || 
                               field.querySelector('.o_field_html_toolbar');
                
                if (toolbar) {
                    toolbar.appendChild(button);
                }
            }

            // 切换全屏
            async toggleFullscreen(field) {
                try {
                    if (document.fullscreenElement) {
                        await document.exitFullscreen();
                    } else {
                        await field.requestFullscreen();
                    }
                } catch (error) {
                    console.error('全屏切换失败:', error);
                }
            }

            // 初始化平滑过渡
            initializeSmoothTransition() {
                // 为所有HTML字段添加过渡样式
                const style = document.createElement('style');
                style.textContent = `
                    .o_field_html .note-editable,
                    .o_field_html_editor {
                        transition: min-height 0.3s ease, height 0.3s ease;
                    }
                    
                    .o_html_field_fullscreen {
                        position: fixed !important;
                        top: 0 !important;
                        left: 0 !important;
                        width: 100vw !important;
                        height: 100vh !important;
                        z-index: 9999 !important;
                        background: white !important;
                    }
                `;
                document.head.appendChild(style);
            }

            // 防抖函数
            debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }

            // 增强的查询选择器
            get htmlFieldQuerySelector() {
                return this.enhancedConfig.customSelectors.description || 
                       super.htmlFieldQuerySelector;
            }

            get getHTMLFieldContainerQuerySelector() {
                return this.enhancedConfig.customSelectors.container || 
                       super.getHTMLFieldContainerQuerySelector;
            }

            // 组件销毁时清理
            willDestroy() {
                // 清理ResizeObserver
                if (this.enhancedState.resizeObserver) {
                    this.enhancedState.resizeObserver.disconnect();
                }

                // 清理窗口事件监听
                if (this.windowResizeHandler) {
                    window.removeEventListener('resize', this.windowResizeHandler);
                }

                super.willDestroy && super.willDestroy();
            }
        }

        // HTML字段管理器
        class HTMLFieldManager {
            constructor(renderer) {
                this.renderer = renderer;
                this.fields = new Map();
            }

            registerField(name, element, config) {
                this.fields.set(name, {
                    element: element,
                    config: config,
                    lastAdjusted: 0
                });
            }

            unregisterField(name) {
                this.fields.delete(name);
            }

            getField(name) {
                return this.fields.get(name);
            }

            getAllFields() {
                return Array.from(this.fields.entries());
            }

            adjustField(name) {
                const field = this.getField(name);
                if (field) {
                    this.renderer.adjustSingleHtmlField({
                        element: field.element,
                        name: name,
                        config: field.config
                    }, this.renderer.uiService.size);
                }
            }

            adjustAllFields() {
                for (const [name] of this.fields) {
                    this.adjustField(name);
                }
            }
        }

        return EnhancedFormRendererWithHtmlExpander;
    }
};

// 应用表单渲染器增强
const enhancedFormRenderer = FormRendererWithHtmlExpanderEnhancer.enhanceFormRendererWithHtmlExpander();

// 导出增强的渲染器
__exports.enhancedFormRenderer = enhancedFormRenderer;
```

## 技术特点

### 1. 智能高度计算
- 精确的边界矩形计算
- 考虑容器内边距和边距
- 动态的最小高度设置
- 响应式的尺寸调整

### 2. 条件执行
- 基于UI尺寸的条件执行
- 元素存在性检查
- 智能的性能优化
- 避免不必要的计算

### 3. 灵活的选择器
- 可重写的查询选择器
- 支持自定义字段定位
- 容器选择器配置
- 扩展性设计

### 4. 回调通知
- 扩展器状态通知
- 组件间通信
- 状态同步机制
- 事件驱动架构

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 在标准表单渲染器基础上添加HTML扩展功能
- 保持原有接口不变

### 2. 观察者模式 (Observer Pattern)
- 监听UI尺寸和记录变化
- 响应式的高度调整

### 3. 策略模式 (Strategy Pattern)
- 可配置的选择器策略
- 灵活的高度计算策略

## 注意事项

1. **性能优化**: 避免频繁的DOM查询和样式计算
2. **浏览器兼容**: 确保getBoundingClientRect的兼容性
3. **内存管理**: 及时清理事件监听器和引用
4. **用户体验**: 提供流畅的高度调整体验

## 扩展建议

1. **多字段支持**: 支持多个HTML字段的同时调整
2. **响应式设计**: 根据屏幕尺寸智能调整
3. **动画效果**: 添加平滑的高度变化动画
4. **全屏模式**: 支持HTML字段的全屏编辑
5. **自定义配置**: 提供更多的配置选项

该表单渲染器扩展为Odoo资源管理提供了重要的HTML内容显示优化功能，通过智能的高度计算和动态调整显著提升了用户的编辑体验。

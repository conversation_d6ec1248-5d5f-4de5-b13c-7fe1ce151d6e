# Form View with HTML Expander - 带HTML扩展器的表单视图

## 概述

`form_view_with_html_expander.js` 是 Odoo Resource 模块的表单视图定义，专门用于组合HTML扩展器的控制器和渲染器，创建一个完整的带HTML扩展功能的表单视图。该视图通过集成自定义的控制器和渲染器，为包含HTML字段的表单提供了优化的显示和交互体验。

## 文件信息
- **路径**: `/resource/static/src/views/form_with_html_expander/form_view_with_html_expander.js`
- **行数**: 18
- **模块**: `@resource/views/form_with_html_expander/form_view_with_html_expander`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                                                    // 注册表
'@web/views/form/form_view'                                            // 标准表单视图
'@resource/views/form_with_html_expander/form_renderer_with_html_expander'  // HTML扩展渲染器
'@resource/views/form_with_html_expander/form_controller_with_html_expander' // HTML扩展控制器
```

## 核心功能

### 1. 依赖导入

```javascript
const { registry } = require("@web/core/registry");
const { formView } = require("@web/views/form/form_view");
const { FormRendererWithHtmlExpander } = require("@resource/views/form_with_html_expander/form_renderer_with_html_expander");
const { FormControllerWithHTMLExpander } = require("@resource/views/form_with_html_expander/form_controller_with_html_expander");
```

**导入功能**:
- **注册表服务**: 导入视图注册表系统
- **标准表单视图**: 导入基础表单视图配置
- **HTML扩展渲染器**: 导入自定义的HTML扩展渲染器
- **HTML扩展控制器**: 导入自定义的HTML扩展控制器

### 2. 视图配置定义

```javascript
const formViewWithHtmlExpander = __exports.formViewWithHtmlExpander = {
    ...formView,
    Controller: FormControllerWithHTMLExpander,
    Renderer: FormRendererWithHtmlExpander,
};
```

**配置功能**:
- **配置继承**: 继承标准表单视图的所有配置
- **控制器替换**: 使用HTML扩展控制器替换默认控制器
- **渲染器替换**: 使用HTML扩展渲染器替换默认渲染器
- **扩展语法**: 使用ES6扩展语法合并配置

### 3. 视图注册

```javascript
registry.category("views").add("form_description_expander", formViewWithHtmlExpander);
```

**注册功能**:
- **视图类别**: 注册到视图类别的注册表
- **视图标识**: 使用"form_description_expander"作为视图标识符
- **配置绑定**: 绑定完整的视图配置对象
- **系统集成**: 集成到Odoo的视图系统

## 使用场景

### 1. 带HTML扩展器的表单视图增强

```javascript
// 带HTML扩展器的表单视图增强功能
const FormViewWithHtmlExpanderEnhancer = {
    enhanceFormViewWithHtmlExpander: () => {
        // 增强的表单视图配置
        const enhancedFormViewWithHtmlExpander = {
            ...formView,
            Controller: EnhancedFormControllerWithHTMLExpander,
            Renderer: EnhancedFormRendererWithHtmlExpander,
            
            // 增强的视图属性
            props: {
                ...formView.props,
                enableAdvancedHtmlFeatures: { type: Boolean, optional: true },
                htmlExpanderConfig: { type: Object, optional: true },
                enableCollaborativeEditing: { type: Boolean, optional: true },
                enableVersionControl: { type: Boolean, optional: true },
                enableAutoSave: { type: Boolean, optional: true },
                enableOfflineMode: { type: Boolean, optional: true },
                customToolbars: { type: Array, optional: true },
                enablePlugins: { type: Boolean, optional: true }
            },

            // 默认配置
            defaultProps: {
                ...formView.defaultProps,
                enableAdvancedHtmlFeatures: true,
                htmlExpanderConfig: {
                    enableMultipleFields: true,
                    enableFullscreen: true,
                    enableAutoResize: true,
                    enableSmoothTransition: true,
                    enableResponsiveDesign: true
                },
                enableCollaborativeEditing: false,
                enableVersionControl: false,
                enableAutoSave: true,
                enableOfflineMode: false,
                customToolbars: [],
                enablePlugins: false
            },

            // 视图配置
            config: {
                // HTML字段配置
                htmlFields: {
                    description: {
                        enableExpander: true,
                        enableFullscreen: true,
                        enableAutoResize: true,
                        minHeightRatio: 0.3,
                        maxHeightRatio: 0.8,
                        enableToolbar: true,
                        enablePlugins: ['table', 'link', 'image', 'video'],
                        enableCollaboration: false
                    },
                    notes: {
                        enableExpander: true,
                        enableFullscreen: false,
                        enableAutoResize: true,
                        minHeightRatio: 0.2,
                        maxHeightRatio: 0.6,
                        enableToolbar: true,
                        enablePlugins: ['table', 'link'],
                        enableCollaboration: false
                    },
                    content: {
                        enableExpander: true,
                        enableFullscreen: true,
                        enableAutoResize: true,
                        minHeightRatio: 0.4,
                        maxHeightRatio: 0.9,
                        enableToolbar: true,
                        enablePlugins: ['table', 'link', 'image', 'video', 'code'],
                        enableCollaboration: true
                    }
                },

                // 工具栏配置
                toolbars: {
                    basic: ['bold', 'italic', 'underline', 'strikethrough'],
                    formatting: ['fontname', 'fontsize', 'color', 'backcolor'],
                    paragraph: ['style', 'ol', 'ul', 'paragraph', 'height'],
                    table: ['table'],
                    insert: ['link', 'picture', 'video', 'hr'],
                    view: ['fullscreen', 'codeview', 'help'],
                    advanced: ['undo', 'redo', 'clear']
                },

                // 插件配置
                plugins: {
                    table: {
                        enabled: true,
                        config: {
                            enableResize: true,
                            enableSort: true,
                            enableFilter: false
                        }
                    },
                    link: {
                        enabled: true,
                        config: {
                            enablePreview: true,
                            enableValidation: true,
                            enableTargetBlank: true
                        }
                    },
                    image: {
                        enabled: true,
                        config: {
                            enableResize: true,
                            enableCrop: false,
                            enableUpload: true,
                            maxFileSize: 5 * 1024 * 1024, // 5MB
                            allowedTypes: ['jpg', 'jpeg', 'png', 'gif', 'webp']
                        }
                    },
                    video: {
                        enabled: true,
                        config: {
                            enableEmbed: true,
                            enableUpload: false,
                            allowedProviders: ['youtube', 'vimeo', 'dailymotion']
                        }
                    },
                    code: {
                        enabled: true,
                        config: {
                            enableSyntaxHighlight: true,
                            enableLineNumbers: true,
                            defaultLanguage: 'javascript',
                            supportedLanguages: ['javascript', 'python', 'html', 'css', 'sql', 'xml']
                        }
                    }
                },

                // 协作配置
                collaboration: {
                    enableRealTimeEditing: false,
                    enableComments: false,
                    enableSuggestions: false,
                    enableVersionHistory: false,
                    enableUserPresence: false,
                    conflictResolution: 'last_write_wins' // 'last_write_wins', 'merge', 'manual'
                },

                // 自动保存配置
                autoSave: {
                    enabled: true,
                    interval: 30000, // 30秒
                    enableDraftMode: true,
                    enableBackup: true,
                    maxBackups: 10
                },

                // 离线模式配置
                offline: {
                    enabled: false,
                    enableCaching: true,
                    enableSync: true,
                    syncInterval: 60000, // 1分钟
                    maxCacheSize: 50 * 1024 * 1024 // 50MB
                },

                // 性能配置
                performance: {
                    enableLazyLoading: true,
                    enableVirtualScrolling: false,
                    enableDebouncing: true,
                    debounceDelay: 300,
                    enableCaching: true,
                    cacheSize: 100
                },

                // 安全配置
                security: {
                    enableSanitization: true,
                    allowedTags: ['p', 'br', 'strong', 'em', 'u', 's', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 
                                 'ul', 'ol', 'li', 'table', 'thead', 'tbody', 'tr', 'th', 'td',
                                 'a', 'img', 'video', 'iframe', 'blockquote', 'code', 'pre'],
                    allowedAttributes: {
                        'a': ['href', 'title', 'target'],
                        'img': ['src', 'alt', 'title', 'width', 'height'],
                        'video': ['src', 'controls', 'width', 'height'],
                        'iframe': ['src', 'width', 'height', 'frameborder'],
                        'table': ['class', 'style'],
                        'td': ['colspan', 'rowspan', 'style'],
                        'th': ['colspan', 'rowspan', 'style']
                    },
                    enableCSPCompliance: true,
                    enableXSSProtection: true
                }
            },

            // 视图方法
            methods: {
                // 初始化HTML扩展器
                initializeHtmlExpander(controller, renderer) {
                    const config = this.config.htmlFields;
                    
                    // 为每个HTML字段初始化扩展器
                    for (const [fieldName, fieldConfig] of Object.entries(config)) {
                        if (fieldConfig.enableExpander) {
                            this.setupFieldExpander(fieldName, fieldConfig, controller, renderer);
                        }
                    }
                },

                // 设置字段扩展器
                setupFieldExpander(fieldName, config, controller, renderer) {
                    // 配置渲染器
                    renderer.configureHtmlField(fieldName, {
                        enableAutoResize: config.enableAutoResize,
                        enableFullscreen: config.enableFullscreen,
                        minHeightRatio: config.minHeightRatio,
                        maxHeightRatio: config.maxHeightRatio,
                        enableSmoothTransition: true
                    });

                    // 配置控制器
                    controller.configureHtmlField(fieldName, {
                        enableExpander: config.enableExpander,
                        enableAutoExpand: config.enableAutoExpand || false,
                        expandMode: config.expandMode || 'manual'
                    });

                    // 设置工具栏
                    if (config.enableToolbar) {
                        this.setupFieldToolbar(fieldName, config);
                    }

                    // 设置插件
                    if (config.enablePlugins && config.enablePlugins.length > 0) {
                        this.setupFieldPlugins(fieldName, config.enablePlugins);
                    }
                },

                // 设置字段工具栏
                setupFieldToolbar(fieldName, config) {
                    const toolbarConfig = this.config.toolbars;
                    const fieldToolbar = [];

                    // 基础工具栏
                    if (config.toolbarSections?.includes('basic') !== false) {
                        fieldToolbar.push(...toolbarConfig.basic);
                    }

                    // 格式化工具栏
                    if (config.toolbarSections?.includes('formatting')) {
                        fieldToolbar.push(...toolbarConfig.formatting);
                    }

                    // 段落工具栏
                    if (config.toolbarSections?.includes('paragraph')) {
                        fieldToolbar.push(...toolbarConfig.paragraph);
                    }

                    // 表格工具栏
                    if (config.enablePlugins?.includes('table')) {
                        fieldToolbar.push(...toolbarConfig.table);
                    }

                    // 插入工具栏
                    if (config.toolbarSections?.includes('insert')) {
                        fieldToolbar.push(...toolbarConfig.insert);
                    }

                    // 视图工具栏
                    if (config.enableFullscreen || config.toolbarSections?.includes('view')) {
                        fieldToolbar.push(...toolbarConfig.view);
                    }

                    // 高级工具栏
                    if (config.toolbarSections?.includes('advanced')) {
                        fieldToolbar.push(...toolbarConfig.advanced);
                    }

                    return fieldToolbar;
                },

                // 设置字段插件
                setupFieldPlugins(fieldName, enabledPlugins) {
                    const pluginConfig = this.config.plugins;
                    const fieldPlugins = {};

                    for (const pluginName of enabledPlugins) {
                        if (pluginConfig[pluginName]?.enabled) {
                            fieldPlugins[pluginName] = pluginConfig[pluginName].config;
                        }
                    }

                    return fieldPlugins;
                },

                // 验证视图配置
                validateConfig() {
                    const errors = [];

                    // 验证HTML字段配置
                    for (const [fieldName, config] of Object.entries(this.config.htmlFields)) {
                        if (config.minHeightRatio > config.maxHeightRatio) {
                            errors.push(`字段 ${fieldName}: minHeightRatio 不能大于 maxHeightRatio`);
                        }

                        if (config.minHeightRatio < 0 || config.minHeightRatio > 1) {
                            errors.push(`字段 ${fieldName}: minHeightRatio 必须在 0-1 之间`);
                        }

                        if (config.maxHeightRatio < 0 || config.maxHeightRatio > 1) {
                            errors.push(`字段 ${fieldName}: maxHeightRatio 必须在 0-1 之间`);
                        }
                    }

                    // 验证插件配置
                    for (const [pluginName, config] of Object.entries(this.config.plugins)) {
                        if (config.enabled && !config.config) {
                            errors.push(`插件 ${pluginName}: 缺少配置对象`);
                        }
                    }

                    if (errors.length > 0) {
                        console.error('视图配置验证失败:', errors);
                        throw new Error(`视图配置验证失败: ${errors.join(', ')}`);
                    }

                    return true;
                },

                // 获取字段配置
                getFieldConfig(fieldName) {
                    return this.config.htmlFields[fieldName] || {};
                },

                // 获取插件配置
                getPluginConfig(pluginName) {
                    return this.config.plugins[pluginName] || { enabled: false };
                },

                // 获取工具栏配置
                getToolbarConfig(toolbarName) {
                    return this.config.toolbars[toolbarName] || [];
                },

                // 创建视图实例
                createViewInstance(props) {
                    // 验证配置
                    this.validateConfig();

                    // 创建控制器实例
                    const controller = new this.Controller(props);

                    // 创建渲染器实例
                    const renderer = new this.Renderer(props);

                    // 初始化HTML扩展器
                    this.initializeHtmlExpander(controller, renderer);

                    return {
                        controller: controller,
                        renderer: renderer,
                        config: this.config
                    };
                }
            }
        };

        // 注册增强的视图
        registry.category("views").add("enhanced_form_description_expander", enhancedFormViewWithHtmlExpander, { force: true });

        // 注册多个变体
        const variants = {
            // 基础HTML扩展器视图
            "form_html_expander_basic": {
                ...enhancedFormViewWithHtmlExpander,
                defaultProps: {
                    ...enhancedFormViewWithHtmlExpander.defaultProps,
                    htmlExpanderConfig: {
                        enableMultipleFields: false,
                        enableFullscreen: false,
                        enableAutoResize: true,
                        enableSmoothTransition: true
                    }
                }
            },

            // 高级HTML扩展器视图
            "form_html_expander_advanced": {
                ...enhancedFormViewWithHtmlExpander,
                defaultProps: {
                    ...enhancedFormViewWithHtmlExpander.defaultProps,
                    enableAdvancedHtmlFeatures: true,
                    enableCollaborativeEditing: true,
                    enableVersionControl: true,
                    enablePlugins: true
                }
            },

            // 协作HTML扩展器视图
            "form_html_expander_collaborative": {
                ...enhancedFormViewWithHtmlExpander,
                defaultProps: {
                    ...enhancedFormViewWithHtmlExpander.defaultProps,
                    enableCollaborativeEditing: true,
                    enableVersionControl: true,
                    htmlExpanderConfig: {
                        ...enhancedFormViewWithHtmlExpander.defaultProps.htmlExpanderConfig,
                        enableCollaboration: true
                    }
                }
            }
        };

        // 注册所有变体
        for (const [name, config] of Object.entries(variants)) {
            registry.category("views").add(name, config);
        }

        return enhancedFormViewWithHtmlExpander;
    }
};

// 应用表单视图增强
const enhancedFormView = FormViewWithHtmlExpanderEnhancer.enhanceFormViewWithHtmlExpander();

// 导出增强的视图
__exports.enhancedFormView = enhancedFormView;
```

## 技术特点

### 1. 组件组合
- 无缝组合控制器和渲染器
- 保持标准表单视图的所有功能
- 简洁的配置定义

### 2. 配置继承
- 完整继承标准表单视图配置
- 选择性替换关键组件
- 保持向后兼容性

### 3. 标准化注册
- 使用标准的视图注册机制
- 清晰的视图标识符
- 完整的视图配置

### 4. 模块化设计
- 清晰的模块依赖关系
- 可重用的组件设计
- 易于维护和扩展

## 设计模式

### 1. 组合模式 (Composite Pattern)
- 组合控制器和渲染器
- 统一的视图接口

### 2. 工厂模式 (Factory Pattern)
- 创建完整的视图配置
- 标准化的视图创建过程

### 3. 配置模式 (Configuration Pattern)
- 集中的视图配置管理
- 灵活的配置选项

## 注意事项

1. **组件兼容**: 确保控制器和渲染器的兼容性
2. **配置一致**: 保持配置的一致性和完整性
3. **性能考虑**: 避免不必要的组件创建
4. **标识唯一**: 确保视图标识符的唯一性

## 扩展建议

1. **多种变体**: 创建不同配置的视图变体
2. **动态配置**: 支持运行时的配置调整
3. **插件系统**: 支持插件化的功能扩展
4. **主题支持**: 支持不同的视觉主题
5. **国际化**: 完整的多语言支持

该表单视图定义为Odoo资源管理提供了完整的HTML扩展功能集成，通过简洁的配置实现了复杂的HTML字段优化体验。

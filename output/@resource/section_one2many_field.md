# Section One2Many Field - 分段一对多字段

## 概述

`section_one2many_field.js` 是 Odoo Resource 模块的分段一对多字段组件，专门用于在一对多关系字段中显示带有分段功能的记录列表。该组件基于Odoo的标准X2ManyField，集成了分段列表渲染器，为资源管理中的层次化数据提供了更好的组织和显示方式。

## 文件信息
- **路径**: `/resource/static/src/section_one2many_field.js`
- **行数**: 28
- **模块**: `@resource/section_one2many_field`

## 依赖关系

```javascript
// 核心依赖
'@resource/section_list_renderer'           // 分段列表渲染器
'@web/core/registry'                        // 注册表
'@web/views/fields/x2many/x2many_field'     // X2Many字段
```

## 核心功能

### 1. 组件定义

```javascript
class SectionOneToManyField extends X2ManyField {
    static components = {
        ...X2ManyField.components,
        ListRenderer: SectionListRenderer,
    };
    static defaultProps = {
        ...X2ManyField.defaultProps,
        editable: "bottom",
    };
}
```

**组件特性**:
- **继承扩展**: 继承X2ManyField的所有功能
- **渲染器替换**: 使用SectionListRenderer替换默认列表渲染器
- **默认属性**: 设置默认的可编辑模式为"bottom"
- **组件集成**: 保持其他组件的完整性

### 2. 字段注册

```javascript
registry.category("fields").add("section_one2many", {
    ...x2ManyField,
    component: SectionOneToManyField,
    additionalClasses: [...(x2ManyField.additionalClasses || []), "o_field_one2many"],
});
```

**注册功能**:
- **字段配置**: 继承x2ManyField的所有配置
- **组件绑定**: 绑定SectionOneToManyField组件
- **样式类**: 添加额外的CSS类
- **标识符**: 使用"section_one2many"作为字段标识符

## 使用场景

### 1. 分段一对多字段增强

```javascript
// 分段一对多字段增强功能
const SectionOneToManyFieldEnhancer = {
    enhanceSectionOneToManyField: () => {
        // 增强的分段一对多字段
        class EnhancedSectionOneToManyField extends SectionOneToManyField {
            static components = {
                ...SectionOneToManyField.components,
                ListRenderer: EnhancedSectionListRenderer,
            };
            
            static props = {
                ...SectionOneToManyField.props,
                enableSectionManagement: { type: Boolean, optional: true },
                sectionTemplate: { type: String, optional: true },
                enableBulkOperations: { type: Boolean, optional: true },
                enableSectionValidation: { type: Boolean, optional: true },
                sectionOrderField: { type: String, optional: true },
                enableSectionFilters: { type: Boolean, optional: true },
                maxSectionsPerPage: { type: Number, optional: true },
                enableSectionSearch: { type: Boolean, optional: true }
            };

            static defaultProps = {
                ...SectionOneToManyField.defaultProps,
                enableSectionManagement: true,
                sectionTemplate: 'default',
                enableBulkOperations: false,
                enableSectionValidation: true,
                sectionOrderField: 'sequence',
                enableSectionFilters: false,
                maxSectionsPerPage: 50,
                enableSectionSearch: false
            };

            setup() {
                super.setup();

                // 增强的配置选项
                this.enhancedConfig = {
                    enableSectionManagement: this.props.enableSectionManagement,
                    sectionTemplate: this.props.sectionTemplate,
                    enableBulkOperations: this.props.enableBulkOperations,
                    enableSectionValidation: this.props.enableSectionValidation,
                    sectionOrderField: this.props.sectionOrderField,
                    enableSectionFilters: this.props.enableSectionFilters,
                    maxSectionsPerPage: this.props.maxSectionsPerPage,
                    enableSectionSearch: this.props.enableSectionSearch
                };

                // 增强的状态
                this.enhancedState = useState({
                    selectedSections: new Set(),
                    sectionFilters: new Map(),
                    searchQuery: '',
                    currentPage: 1,
                    totalSections: 0,
                    validationErrors: new Map()
                });

                // 分段管理器
                this.sectionManager = new SectionManager(this);

                // 初始化增强功能
                this.initializeEnhancements();
            }

            // 初始化增强功能
            initializeEnhancements() {
                // 初始化分段管理
                if (this.enhancedConfig.enableSectionManagement) {
                    this.sectionManager.initialize();
                }

                // 初始化验证
                if (this.enhancedConfig.enableSectionValidation) {
                    this.initializeValidation();
                }

                // 初始化过滤器
                if (this.enhancedConfig.enableSectionFilters) {
                    this.initializeFilters();
                }
            }

            // 添加新分段
            async addSection(sectionData = {}) {
                if (!this.enhancedConfig.enableSectionManagement) {
                    return;
                }

                try {
                    const defaultSectionData = {
                        display_type: 'line_section',
                        title: _t('New Section'),
                        [this.enhancedConfig.sectionOrderField]: this.getNextSequence(),
                        ...sectionData
                    };

                    // 验证分段数据
                    if (this.enhancedConfig.enableSectionValidation) {
                        const validation = await this.validateSectionData(defaultSectionData);
                        if (!validation.isValid) {
                            this.showValidationErrors(validation.errors);
                            return;
                        }
                    }

                    // 添加到列表
                    await this.list.addNewRecord({
                        position: 'bottom',
                        mode: 'edit'
                    });

                    // 设置分段数据
                    const newRecord = this.list.records[this.list.records.length - 1];
                    await newRecord.update(defaultSectionData);

                    // 保存记录
                    await newRecord.save();

                    // 更新统计
                    this.updateSectionStatistics();

                    this.env.services.notification.add(
                        _t('Section added successfully'),
                        { type: 'success' }
                    );

                } catch (error) {
                    console.error('添加分段失败:', error);
                    this.env.services.notification.add(
                        _t('Failed to add section'),
                        { type: 'error' }
                    );
                }
            }

            // 删除分段
            async deleteSection(record) {
                if (!this.enhancedConfig.enableSectionManagement) {
                    return;
                }

                try {
                    // 确认删除
                    const confirmed = await this.env.services.dialog.add(ConfirmDialog, {
                        title: _t('Delete Section'),
                        body: _t('Are you sure you want to delete this section? All items in this section will also be deleted.'),
                    });

                    if (!confirmed) {
                        return;
                    }

                    // 查找分段中的所有项目
                    const sectionItems = this.getSectionItems(record);

                    // 删除分段和项目
                    const recordsToDelete = [record, ...sectionItems];
                    for (const recordToDelete of recordsToDelete) {
                        await this.list.removeRecord(recordToDelete);
                    }

                    // 更新统计
                    this.updateSectionStatistics();

                    this.env.services.notification.add(
                        _t('Section deleted successfully'),
                        { type: 'success' }
                    );

                } catch (error) {
                    console.error('删除分段失败:', error);
                    this.env.services.notification.add(
                        _t('Failed to delete section'),
                        { type: 'error' }
                    );
                }
            }

            // 移动分段
            async moveSection(record, direction) {
                if (!this.enhancedConfig.enableSectionManagement) {
                    return;
                }

                try {
                    const currentSequence = record.data[this.enhancedConfig.sectionOrderField] || 0;
                    let newSequence;

                    if (direction === 'up') {
                        newSequence = currentSequence - 1;
                    } else if (direction === 'down') {
                        newSequence = currentSequence + 1;
                    } else {
                        return;
                    }

                    // 更新序号
                    await record.update({
                        [this.enhancedConfig.sectionOrderField]: newSequence
                    });

                    // 重新排序
                    await this.reorderSections();

                    this.env.services.notification.add(
                        _t('Section moved successfully'),
                        { type: 'success' }
                    );

                } catch (error) {
                    console.error('移动分段失败:', error);
                    this.env.services.notification.add(
                        _t('Failed to move section'),
                        { type: 'error' }
                    );
                }
            }

            // 批量操作
            async performBulkOperation(operation, selectedRecords) {
                if (!this.enhancedConfig.enableBulkOperations) {
                    return;
                }

                try {
                    switch (operation) {
                        case 'delete':
                            await this.bulkDelete(selectedRecords);
                            break;
                        case 'move_to_section':
                            await this.bulkMoveToSection(selectedRecords);
                            break;
                        case 'duplicate':
                            await this.bulkDuplicate(selectedRecords);
                            break;
                        case 'export':
                            await this.bulkExport(selectedRecords);
                            break;
                        default:
                            console.warn('未知的批量操作:', operation);
                    }
                } catch (error) {
                    console.error('批量操作失败:', error);
                    this.env.services.notification.add(
                        _t('Bulk operation failed'),
                        { type: 'error' }
                    );
                }
            }

            // 批量删除
            async bulkDelete(records) {
                const confirmed = await this.env.services.dialog.add(ConfirmDialog, {
                    title: _t('Bulk Delete'),
                    body: _t('Are you sure you want to delete %s records?', records.length),
                });

                if (confirmed) {
                    for (const record of records) {
                        await this.list.removeRecord(record);
                    }
                    
                    this.enhancedState.selectedSections.clear();
                    this.updateSectionStatistics();
                }
            }

            // 批量移动到分段
            async bulkMoveToSection(records) {
                // 显示分段选择对话框
                const targetSection = await this.selectTargetSection();
                
                if (targetSection) {
                    for (const record of records) {
                        await record.update({
                            section_id: targetSection.id
                        });
                    }
                    
                    this.enhancedState.selectedSections.clear();
                }
            }

            // 获取分段中的项目
            getSectionItems(sectionRecord) {
                const sectionIndex = this.list.records.findIndex(r => r.id === sectionRecord.id);
                const items = [];

                // 查找分段后的所有非分段记录，直到下一个分段
                for (let i = sectionIndex + 1; i < this.list.records.length; i++) {
                    const record = this.list.records[i];
                    if (record.data.display_type === 'line_section') {
                        break;
                    }
                    items.push(record);
                }

                return items;
            }

            // 获取下一个序号
            getNextSequence() {
                const sequences = this.list.records
                    .filter(r => r.data.display_type === 'line_section')
                    .map(r => r.data[this.enhancedConfig.sectionOrderField] || 0);

                return sequences.length > 0 ? Math.max(...sequences) + 1 : 1;
            }

            // 重新排序分段
            async reorderSections() {
                const sections = this.list.records.filter(r => r.data.display_type === 'line_section');
                
                sections.sort((a, b) => {
                    const seqA = a.data[this.enhancedConfig.sectionOrderField] || 0;
                    const seqB = b.data[this.enhancedConfig.sectionOrderField] || 0;
                    return seqA - seqB;
                });

                // 重新分配序号
                for (let i = 0; i < sections.length; i++) {
                    await sections[i].update({
                        [this.enhancedConfig.sectionOrderField]: (i + 1) * 10
                    });
                }
            }

            // 验证分段数据
            async validateSectionData(data) {
                const errors = [];

                // 标题验证
                if (!data.title || data.title.trim() === '') {
                    errors.push(_t('Section title is required'));
                }

                // 重复标题检查
                const existingTitles = this.list.records
                    .filter(r => r.data.display_type === 'line_section')
                    .map(r => r.data.title);

                if (existingTitles.includes(data.title)) {
                    errors.push(_t('Section title must be unique'));
                }

                // 序号验证
                if (data[this.enhancedConfig.sectionOrderField] < 0) {
                    errors.push(_t('Section order must be positive'));
                }

                return {
                    isValid: errors.length === 0,
                    errors: errors
                };
            }

            // 显示验证错误
            showValidationErrors(errors) {
                const errorMessage = errors.join('\n');
                this.env.services.notification.add(errorMessage, { type: 'error' });
            }

            // 初始化验证
            initializeValidation() {
                // 监听记录变化
                this.list.addEventListener('record-updated', (event) => {
                    const record = event.detail.record;
                    if (record.data.display_type === 'line_section') {
                        this.validateRecord(record);
                    }
                });
            }

            // 验证记录
            async validateRecord(record) {
                const validation = await this.validateSectionData(record.data);
                const recordId = record.id;

                if (validation.isValid) {
                    this.enhancedState.validationErrors.delete(recordId);
                } else {
                    this.enhancedState.validationErrors.set(recordId, validation.errors);
                }
            }

            // 初始化过滤器
            initializeFilters() {
                // 设置默认过滤器
                this.enhancedState.sectionFilters.set('all', {
                    label: _t('All'),
                    filter: () => true
                });

                this.enhancedState.sectionFilters.set('sections_only', {
                    label: _t('Sections Only'),
                    filter: (record) => record.data.display_type === 'line_section'
                });

                this.enhancedState.sectionFilters.set('items_only', {
                    label: _t('Items Only'),
                    filter: (record) => record.data.display_type !== 'line_section'
                });
            }

            // 应用过滤器
            applyFilter(filterKey) {
                const filter = this.enhancedState.sectionFilters.get(filterKey);
                if (filter) {
                    // 这里应该实现过滤逻辑
                    console.log('应用过滤器:', filterKey);
                }
            }

            // 搜索分段
            searchSections(query) {
                if (!this.enhancedConfig.enableSectionSearch) {
                    return;
                }

                this.enhancedState.searchQuery = query;
                
                // 这里应该实现搜索逻辑
                const filteredRecords = this.list.records.filter(record => {
                    if (record.data.display_type === 'line_section') {
                        return record.data.title.toLowerCase().includes(query.toLowerCase());
                    }
                    return true; // 保留所有非分段记录
                });

                // 更新显示
                this.updateDisplay(filteredRecords);
            }

            // 更新显示
            updateDisplay(records) {
                // 这里应该实现显示更新逻辑
                console.log('更新显示:', records.length, '条记录');
            }

            // 更新分段统计
            updateSectionStatistics() {
                const sections = this.list.records.filter(r => r.data.display_type === 'line_section');
                this.enhancedState.totalSections = sections.length;

                // 触发统计更新事件
                this.env.bus.trigger('section-statistics-updated', {
                    totalSections: this.enhancedState.totalSections,
                    totalRecords: this.list.records.length
                });
            }

            // 选择目标分段
            async selectTargetSection() {
                const sections = this.list.records.filter(r => r.data.display_type === 'line_section');
                
                return new Promise((resolve) => {
                    this.env.services.dialog.add(SectionSelectorDialog, {
                        sections: sections,
                        onSelect: (section) => resolve(section),
                        onCancel: () => resolve(null)
                    });
                });
            }

            // 获取分段操作
            getSectionActions() {
                const actions = [];

                if (this.enhancedConfig.enableSectionManagement) {
                    actions.push(
                        {
                            name: 'add_section',
                            label: _t('Add Section'),
                            icon: 'fa-plus',
                            callback: () => this.addSection()
                        },
                        {
                            name: 'reorder_sections',
                            label: _t('Reorder Sections'),
                            icon: 'fa-sort',
                            callback: () => this.reorderSections()
                        }
                    );
                }

                if (this.enhancedConfig.enableBulkOperations) {
                    actions.push({
                        name: 'bulk_operations',
                        label: _t('Bulk Operations'),
                        icon: 'fa-tasks',
                        callback: () => this.showBulkOperationsMenu()
                    });
                }

                return actions;
            }

            // 显示批量操作菜单
            showBulkOperationsMenu() {
                const selectedRecords = Array.from(this.enhancedState.selectedSections)
                    .map(id => this.list.records.find(r => r.id === id))
                    .filter(Boolean);

                if (selectedRecords.length === 0) {
                    this.env.services.notification.add(
                        _t('Please select records first'),
                        { type: 'warning' }
                    );
                    return;
                }

                this.env.services.dialog.add(BulkOperationsDialog, {
                    selectedRecords: selectedRecords,
                    onOperation: (operation) => this.performBulkOperation(operation, selectedRecords)
                });
            }
        }

        // 分段管理器
        class SectionManager {
            constructor(field) {
                this.field = field;
                this.sections = new Map();
            }

            initialize() {
                this.updateSections();
                this.setupEventListeners();
            }

            updateSections() {
                this.sections.clear();
                
                const records = this.field.list.records;
                let currentSection = null;

                for (const record of records) {
                    if (record.data.display_type === 'line_section') {
                        currentSection = {
                            record: record,
                            items: []
                        };
                        this.sections.set(record.id, currentSection);
                    } else if (currentSection) {
                        currentSection.items.push(record);
                    }
                }
            }

            setupEventListeners() {
                this.field.list.addEventListener('record-added', () => this.updateSections());
                this.field.list.addEventListener('record-removed', () => this.updateSections());
                this.field.list.addEventListener('record-updated', () => this.updateSections());
            }

            getSectionByRecord(record) {
                for (const [sectionId, section] of this.sections) {
                    if (section.items.includes(record)) {
                        return section;
                    }
                }
                return null;
            }

            getSectionItems(sectionId) {
                const section = this.sections.get(sectionId);
                return section ? section.items : [];
            }

            getSectionCount(sectionId) {
                const items = this.getSectionItems(sectionId);
                return items.length;
            }
        }

        return EnhancedSectionOneToManyField;
    }
};

// 应用分段一对多字段增强
const enhancedSectionOneToManyField = SectionOneToManyFieldEnhancer.enhanceSectionOneToManyField();

// 导出增强的字段
__exports.enhancedSectionOneToManyField = enhancedSectionOneToManyField;
```

## 技术特点

### 1. 组件集成
- 无缝集成分段列表渲染器
- 保持X2ManyField的所有功能
- 简洁的组件替换机制

### 2. 配置继承
- 完整继承x2ManyField配置
- 添加必要的样式类
- 设置合适的默认属性

### 3. 简洁设计
- 最小化的代码实现
- 清晰的依赖关系
- 易于理解和维护

### 4. 标准化注册
- 使用标准的字段注册机制
- 遵循Odoo字段命名约定
- 完整的字段配置

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 在X2ManyField基础上添加分段功能
- 保持原有接口不变

### 2. 组合模式 (Composite Pattern)
- 组合分段列表渲染器
- 统一的组件接口

### 3. 策略模式 (Strategy Pattern)
- 使用不同的渲染策略
- 灵活的组件替换

## 注意事项

1. **渲染器兼容**: 确保分段列表渲染器与X2ManyField兼容
2. **数据结构**: 确保数据结构支持分段显示
3. **性能考虑**: 避免不必要的重渲染
4. **样式一致**: 保持与其他字段的样式一致性

## 扩展建议

1. **分段模板**: 支持不同的分段显示模板
2. **分段验证**: 添加分段数据的验证规则
3. **分段操作**: 提供分段级别的操作功能
4. **分段统计**: 显示分段的统计信息
5. **分段搜索**: 支持分段内容的搜索功能

该分段一对多字段为Odoo资源管理提供了重要的层次化数据显示功能，通过简洁的设计实现了复杂的分段显示需求。

# Section List Renderer - 分段列表渲染器

## 概述

`section_list_renderer.js` 是 Odoo Resource 模块的分段列表渲染器，专门用于在列表视图中显示带有分段标题的记录。该组件基于Odoo的标准列表渲染器，通过扩展功能支持将特定记录显示为分段标题，为资源管理提供了更好的数据组织和视觉层次结构。

## 文件信息
- **路径**: `/resource/static/src/section_list_renderer.js`
- **行数**: 62
- **模块**: `@resource/section_list_renderer`

## 依赖关系

```javascript
// 核心依赖
'@web/views/list/list_renderer'    // 标准列表渲染器
'@odoo/owl'                        // OWL框架
```

## 核心功能

### 1. 组件定义

```javascript
const SectionListRenderer = __exports.SectionListRenderer = class SectionListRenderer extends ListRenderer {
    setup() {
        super.setup();

        this.displayType = "line_section";
        this.titleField = "title";

        useEffect(
            (table) => {
                if (table) {
                    table.classList.add("o_section_list_view");
                }
            },
            () => [this.tableRef.el]
        );
    }
```

**组件特性**:
- **继承扩展**: 继承ListRenderer的所有功能
- **分段类型**: 设置显示类型为"line_section"
- **标题字段**: 指定"title"作为分段标题字段
- **样式应用**: 使用useEffect为表格添加专门的CSS类
- **DOM监听**: 监听表格元素的变化并应用样式

### 2. 列配置管理

```javascript
getColumns(record) {
    const columns = super.getColumns(record);
    if (this.isSection(record)) {
        return this.getSectionColumns(columns);
    }
    return columns;
}
```

**列配置功能**:
- **条件处理**: 根据记录类型返回不同的列配置
- **分段列**: 为分段记录返回特殊的列配置
- **标准列**: 为普通记录返回标准列配置
- **动态切换**: 动态切换不同的列显示模式

### 3. 行样式管理

```javascript
getRowClass(record) {
    const classNames = super.getRowClass(record).split(" ");
    if (this.isSection(record)) {
        classNames.push(`o_is_${this.displayType}`, `fw-bold`);
    }
    return classNames.join(" ");
}
```

**行样式功能**:
- **基础样式**: 继承父类的行样式
- **分段样式**: 为分段行添加特殊的CSS类
- **粗体显示**: 为分段标题添加粗体样式
- **类型标识**: 添加显示类型相关的CSS类

### 4. 分段列生成

```javascript
getSectionColumns(columns) {
    const sectionColumns = columns.filter((col) => col.widget === "handle");
    let colspan = columns.length - sectionColumns.length;
    if (this.activeActions.onDelete) {
        colspan++;
    }
    const titleCol = columns.find(
        (col) => col.type === "field" && col.name === this.titleField
    );
    sectionColumns.push({ ...titleCol, colspan });
    return sectionColumns;
}
```

**分段列功能**:
- **句柄保留**: 保留拖拽句柄列
- **跨列计算**: 计算标题列需要跨越的列数
- **删除操作**: 考虑删除操作列的影响
- **标题列**: 找到并配置标题列
- **列合并**: 设置标题列的colspan属性

### 5. 分段识别

```javascript
isSection(record) {
    return record.data.display_type === this.displayType;
}
```

**识别功能**:
- **类型检查**: 检查记录的display_type字段
- **分段判断**: 判断记录是否为分段类型
- **简洁逻辑**: 简单而准确的判断逻辑

### 6. 模板配置

```javascript
SectionListRenderer.recordRowTemplate = "resource.SectionListRenderer.RecordRow";
```

**模板功能**:
- **自定义模板**: 使用专门的记录行模板
- **渲染定制**: 定制分段记录的渲染方式
- **视觉区分**: 提供与普通记录不同的视觉效果

## 使用场景

### 1. 分段列表渲染器增强

```javascript
// 分段列表渲染器增强功能
const SectionListRendererEnhancer = {
    enhanceSectionListRenderer: () => {
        // 增强的分段列表渲染器
        class EnhancedSectionListRenderer extends SectionListRenderer {
            static props = {
                ...SectionListRenderer.props,
                enableCollapse: { type: Boolean, optional: true },
                enableDragDrop: { type: Boolean, optional: true },
                sectionIcon: { type: String, optional: true },
                customSectionTypes: { type: Array, optional: true },
                enableSectionActions: { type: Boolean, optional: true },
                sectionColorField: { type: String, optional: true },
                enableSectionCount: { type: Boolean, optional: true },
                enableSectionSummary: { type: Boolean, optional: true }
            };

            setup() {
                super.setup();

                // 增强的配置选项
                this.enhancedConfig = {
                    enableCollapse: this.props.enableCollapse !== false,
                    enableDragDrop: this.props.enableDragDrop !== false,
                    sectionIcon: this.props.sectionIcon || 'fa-folder',
                    customSectionTypes: this.props.customSectionTypes || [],
                    enableSectionActions: this.props.enableSectionActions !== false,
                    sectionColorField: this.props.sectionColorField || 'color',
                    enableSectionCount: this.props.enableSectionCount !== false,
                    enableSectionSummary: this.props.enableSectionSummary !== false
                };

                // 增强的状态
                this.enhancedState = useState({
                    collapsedSections: new Set(),
                    sectionCounts: new Map(),
                    sectionSummaries: new Map(),
                    draggedSection: null,
                    hoveredSection: null
                });

                // 支持的分段类型
                this.supportedSectionTypes = [
                    "line_section",
                    "line_note",
                    "line_break",
                    ...this.enhancedConfig.customSectionTypes
                ];

                // 初始化增强功能
                this.initializeEnhancements();
            }

            // 初始化增强功能
            initializeEnhancements() {
                // 计算分段统计
                if (this.enhancedConfig.enableSectionCount || this.enhancedConfig.enableSectionSummary) {
                    this.calculateSectionStatistics();
                }

                // 恢复折叠状态
                if (this.enhancedConfig.enableCollapse) {
                    this.restoreCollapseState();
                }
            }

            // 增强的分段识别
            isSection(record) {
                const displayType = record.data.display_type;
                return this.supportedSectionTypes.includes(displayType);
            }

            // 获取分段类型
            getSectionType(record) {
                return record.data.display_type || this.displayType;
            }

            // 增强的行样式管理
            getRowClass(record) {
                const classNames = super.getRowClass(record).split(" ");
                
                if (this.isSection(record)) {
                    const sectionType = this.getSectionType(record);
                    classNames.push(`o_is_${sectionType}`, `fw-bold`);
                    
                    // 折叠状态
                    if (this.enhancedConfig.enableCollapse) {
                        const sectionId = this.getSectionId(record);
                        if (this.enhancedState.collapsedSections.has(sectionId)) {
                            classNames.push('o_section_collapsed');
                        } else {
                            classNames.push('o_section_expanded');
                        }
                    }
                    
                    // 拖拽状态
                    if (this.enhancedConfig.enableDragDrop) {
                        if (this.enhancedState.draggedSection === record.id) {
                            classNames.push('o_section_dragging');
                        }
                        if (this.enhancedState.hoveredSection === record.id) {
                            classNames.push('o_section_drop_target');
                        }
                    }
                    
                    // 颜色样式
                    if (this.enhancedConfig.sectionColorField && record.data[this.enhancedConfig.sectionColorField]) {
                        classNames.push(`o_section_color_${record.data[this.enhancedConfig.sectionColorField]}`);
                    }
                }
                
                return classNames.join(" ");
            }

            // 增强的分段列生成
            getSectionColumns(columns) {
                const sectionColumns = columns.filter((col) => col.widget === "handle");
                let colspan = columns.length - sectionColumns.length;
                
                // 考虑操作列
                if (this.activeActions.onDelete) {
                    colspan++;
                }
                
                // 考虑分段操作列
                if (this.enhancedConfig.enableSectionActions) {
                    colspan--;
                }

                const titleCol = columns.find(
                    (col) => col.type === "field" && col.name === this.titleField
                );

                if (titleCol) {
                    const enhancedTitleCol = {
                        ...titleCol,
                        colspan,
                        widget: 'section_title',
                        sectionIcon: this.enhancedConfig.sectionIcon,
                        enableCollapse: this.enhancedConfig.enableCollapse,
                        enableCount: this.enhancedConfig.enableSectionCount,
                        enableSummary: this.enhancedConfig.enableSectionSummary
                    };
                    
                    sectionColumns.push(enhancedTitleCol);
                }

                // 添加分段操作列
                if (this.enhancedConfig.enableSectionActions) {
                    sectionColumns.push({
                        type: 'widget',
                        widget: 'section_actions',
                        name: 'section_actions',
                        string: '',
                        width: '50px'
                    });
                }

                return sectionColumns;
            }

            // 获取分段ID
            getSectionId(record) {
                return `section_${record.id}`;
            }

            // 切换分段折叠状态
            toggleSectionCollapse(record) {
                if (!this.enhancedConfig.enableCollapse) return;

                const sectionId = this.getSectionId(record);
                const collapsed = this.enhancedState.collapsedSections;

                if (collapsed.has(sectionId)) {
                    collapsed.delete(sectionId);
                } else {
                    collapsed.add(sectionId);
                }

                // 保存折叠状态
                this.saveCollapseState();

                // 触发重新渲染
                this.render();
            }

            // 检查记录是否在折叠的分段中
            isRecordInCollapsedSection(record) {
                if (!this.enhancedConfig.enableCollapse || this.isSection(record)) {
                    return false;
                }

                // 查找所属分段
                const sectionRecord = this.findParentSection(record);
                if (sectionRecord) {
                    const sectionId = this.getSectionId(sectionRecord);
                    return this.enhancedState.collapsedSections.has(sectionId);
                }

                return false;
            }

            // 查找父分段
            findParentSection(record) {
                const records = this.props.list.records;
                const currentIndex = records.findIndex(r => r.id === record.id);

                // 向上查找最近的分段
                for (let i = currentIndex - 1; i >= 0; i--) {
                    if (this.isSection(records[i])) {
                        return records[i];
                    }
                }

                return null;
            }

            // 计算分段统计
            calculateSectionStatistics() {
                const records = this.props.list.records;
                let currentSection = null;
                let sectionRecords = [];

                for (const record of records) {
                    if (this.isSection(record)) {
                        // 处理前一个分段的统计
                        if (currentSection && sectionRecords.length > 0) {
                            this.calculateSectionStats(currentSection, sectionRecords);
                        }

                        // 开始新分段
                        currentSection = record;
                        sectionRecords = [];
                    } else if (currentSection) {
                        sectionRecords.push(record);
                    }
                }

                // 处理最后一个分段
                if (currentSection && sectionRecords.length > 0) {
                    this.calculateSectionStats(currentSection, sectionRecords);
                }
            }

            // 计算单个分段的统计
            calculateSectionStats(sectionRecord, records) {
                const sectionId = this.getSectionId(sectionRecord);

                // 计算数量
                if (this.enhancedConfig.enableSectionCount) {
                    this.enhancedState.sectionCounts.set(sectionId, records.length);
                }

                // 计算汇总
                if (this.enhancedConfig.enableSectionSummary) {
                    const summary = this.calculateSummary(records);
                    this.enhancedState.sectionSummaries.set(sectionId, summary);
                }
            }

            // 计算汇总数据
            calculateSummary(records) {
                const summary = {
                    count: records.length,
                    totals: {},
                    averages: {}
                };

                // 计算数值字段的汇总
                const numericFields = this.getNumericFields();
                
                for (const field of numericFields) {
                    const values = records
                        .map(r => r.data[field])
                        .filter(v => typeof v === 'number' && !isNaN(v));

                    if (values.length > 0) {
                        summary.totals[field] = values.reduce((sum, val) => sum + val, 0);
                        summary.averages[field] = summary.totals[field] / values.length;
                    }
                }

                return summary;
            }

            // 获取数值字段
            getNumericFields() {
                return this.props.archInfo.columns
                    .filter(col => ['integer', 'float', 'monetary'].includes(col.type))
                    .map(col => col.name);
            }

            // 保存折叠状态
            saveCollapseState() {
                try {
                    const state = Array.from(this.enhancedState.collapsedSections);
                    localStorage.setItem(
                        `section_collapse_${this.props.list.resModel}`,
                        JSON.stringify(state)
                    );
                } catch (error) {
                    console.warn('保存折叠状态失败:', error);
                }
            }

            // 恢复折叠状态
            restoreCollapseState() {
                try {
                    const saved = localStorage.getItem(`section_collapse_${this.props.list.resModel}`);
                    if (saved) {
                        const state = JSON.parse(saved);
                        this.enhancedState.collapsedSections = new Set(state);
                    }
                } catch (error) {
                    console.warn('恢复折叠状态失败:', error);
                }
            }

            // 分段拖拽开始
            onSectionDragStart(record, event) {
                if (!this.enhancedConfig.enableDragDrop || !this.isSection(record)) return;

                this.enhancedState.draggedSection = record.id;
                event.dataTransfer.effectAllowed = 'move';
                event.dataTransfer.setData('text/plain', record.id);
            }

            // 分段拖拽结束
            onSectionDragEnd(record, event) {
                this.enhancedState.draggedSection = null;
                this.enhancedState.hoveredSection = null;
            }

            // 分段拖拽悬停
            onSectionDragOver(record, event) {
                if (!this.enhancedConfig.enableDragDrop || !this.isSection(record)) return;

                event.preventDefault();
                this.enhancedState.hoveredSection = record.id;
            }

            // 分段拖拽离开
            onSectionDragLeave(record, event) {
                this.enhancedState.hoveredSection = null;
            }

            // 分段拖拽放置
            onSectionDrop(record, event) {
                if (!this.enhancedConfig.enableDragDrop || !this.isSection(record)) return;

                event.preventDefault();
                const draggedId = event.dataTransfer.getData('text/plain');
                
                if (draggedId && draggedId !== record.id) {
                    this.moveSectionBefore(draggedId, record.id);
                }

                this.enhancedState.draggedSection = null;
                this.enhancedState.hoveredSection = null;
            }

            // 移动分段
            async moveSectionBefore(draggedId, targetId) {
                try {
                    await this.props.list.model.orm.call(
                        this.props.list.resModel,
                        'move_section_before',
                        [parseInt(draggedId), parseInt(targetId)]
                    );

                    // 重新加载列表
                    await this.props.list.load();
                } catch (error) {
                    console.error('移动分段失败:', error);
                    this.env.services.notification.add('移动分段失败', { type: 'error' });
                }
            }

            // 获取分段统计信息
            getSectionStats(record) {
                const sectionId = this.getSectionId(record);
                return {
                    count: this.enhancedState.sectionCounts.get(sectionId) || 0,
                    summary: this.enhancedState.sectionSummaries.get(sectionId) || {}
                };
            }

            // 分段操作菜单
            getSectionActions(record) {
                const actions = [];

                if (this.enhancedConfig.enableCollapse) {
                    const sectionId = this.getSectionId(record);
                    const isCollapsed = this.enhancedState.collapsedSections.has(sectionId);
                    
                    actions.push({
                        name: isCollapsed ? 'expand' : 'collapse',
                        label: isCollapsed ? _t('Expand') : _t('Collapse'),
                        icon: isCollapsed ? 'fa-chevron-down' : 'fa-chevron-up',
                        callback: () => this.toggleSectionCollapse(record)
                    });
                }

                actions.push(
                    {
                        name: 'add_item',
                        label: _t('Add Item'),
                        icon: 'fa-plus',
                        callback: () => this.addItemToSection(record)
                    },
                    {
                        name: 'edit_section',
                        label: _t('Edit Section'),
                        icon: 'fa-edit',
                        callback: () => this.editSection(record)
                    },
                    {
                        name: 'delete_section',
                        label: _t('Delete Section'),
                        icon: 'fa-trash',
                        callback: () => this.deleteSection(record)
                    }
                );

                return actions;
            }

            // 添加项目到分段
            addItemToSection(sectionRecord) {
                // 实现添加项目逻辑
                this.env.services.action.doAction({
                    type: 'ir.actions.act_window',
                    res_model: this.props.list.resModel,
                    view_mode: 'form',
                    views: [[false, 'form']],
                    target: 'new',
                    context: {
                        default_section_id: sectionRecord.id
                    }
                });
            }

            // 编辑分段
            editSection(record) {
                // 实现编辑分段逻辑
                this.env.services.action.doAction({
                    type: 'ir.actions.act_window',
                    res_model: this.props.list.resModel,
                    res_id: record.id,
                    view_mode: 'form',
                    views: [[false, 'form']],
                    target: 'new'
                });
            }

            // 删除分段
            async deleteSection(record) {
                const confirmed = await this.env.services.dialog.add(ConfirmDialog, {
                    title: _t('Delete Section'),
                    body: _t('Are you sure you want to delete this section and all its items?'),
                });

                if (confirmed) {
                    try {
                        await this.props.list.model.orm.unlink(this.props.list.resModel, [record.id]);
                        await this.props.list.load();
                    } catch (error) {
                        console.error('删除分段失败:', error);
                        this.env.services.notification.add('删除分段失败', { type: 'error' });
                    }
                }
            }
        }

        // 增强的模板
        EnhancedSectionListRenderer.recordRowTemplate = "resource.EnhancedSectionListRenderer.RecordRow";

        return EnhancedSectionListRenderer;
    }
};

// 应用分段列表渲染器增强
const enhancedSectionListRenderer = SectionListRendererEnhancer.enhanceSectionListRenderer();

// 导出增强的渲染器
__exports.enhancedSectionListRenderer = enhancedSectionListRenderer;
```

## 技术特点

### 1. 组件继承
- 继承ListRenderer的所有功能
- 保持与标准列表视图的兼容性
- 扩展分段显示特有功能

### 2. 动态列配置
- 根据记录类型动态调整列配置
- 智能的列合并和跨列处理
- 灵活的列显示策略

### 3. 样式管理
- 自动应用分段相关的CSS类
- 支持粗体和特殊样式
- 响应式的视觉效果

### 4. 模板定制
- 使用专门的记录行模板
- 支持分段的特殊渲染需求
- 灵活的模板配置

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 在标准列表渲染器基础上添加分段功能
- 保持原有接口不变

### 2. 策略模式 (Strategy Pattern)
- 根据记录类型选择不同的渲染策略
- 灵活的列配置策略

### 3. 模板方法模式 (Template Method Pattern)
- 定义分段渲染的基本流程
- 允许子类定制特定步骤

## 注意事项

1. **性能考虑**: 避免频繁的DOM操作和样式计算
2. **数据一致性**: 确保分段标识的准确性
3. **样式兼容**: 确保与现有主题的兼容性
4. **可访问性**: 支持键盘导航和屏幕阅读器

## 扩展建议

1. **折叠功能**: 支持分段的折叠和展开
2. **拖拽排序**: 支持分段和项目的拖拽排序
3. **分段统计**: 显示分段内的项目统计
4. **分段操作**: 提供分段级别的操作菜单
5. **多级分段**: 支持嵌套的分段结构

该分段列表渲染器为Odoo资源管理提供了重要的数据组织功能，通过分段显示提升了大量数据的可读性和管理效率。

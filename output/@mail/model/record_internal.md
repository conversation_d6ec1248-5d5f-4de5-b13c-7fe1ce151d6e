# Record Internal - 记录内部管理

## 概述

`record_internal.js` 是 Odoo 邮件模型系统的记录内部管理模块，专门负责管理记录的内部状态、字段计算、排序处理和生命周期控制。该模块基于OWL响应式系统和Map数据结构，集成了字段计算管理、排序控制、状态跟踪、依赖管理等核心功能，为邮件模型系统提供了完整的记录内部状态管理和字段处理机制，是邮件系统记录管理的核心内部组件。

## 文件信息
- **路径**: `/mail/static/src/model/record_internal.js`
- **行数**: 263
- **模块**: `@mail/model/record_internal`

## 依赖关系

```javascript
// 核心依赖
'@mail/utils/common/misc'        // 通用工具函数
'@mail/model/misc'               // 模型工具函数
'@mail/model/record_list'        // 记录列表模块
'@odoo/owl'                      // OWL 框架
'@mail/model/record_uses'        // 记录使用模块
```

## 核心功能

### 1. RecordInternal 类定义

```javascript
const RecordInternal = class RecordInternal {
    [IS_RECORD_SYM] = true;          // 记录标识符
    [IS_DELETED_SYM] = false;        // 删除状态标识
    
    // 字段计算状态
    fieldsComputing = new Map();      // 正在计算的字段
    fieldsComputeOnNeed = new Map();  // 按需计算的字段
    fieldsComputeInNeed = new Map();  // 需要计算的字段
    
    // 字段排序状态
    fieldsSorting = new Map();        // 正在排序的字段
    fieldsSortOnNeed = new Map();     // 按需排序的字段
    fieldsSortInNeed = new Map();     // 需要排序的字段
    
    // 字段更新状态
    updatingAttrs = new Map();        // 正在更新的属性
    proxyUsed = new Map();           // 代理使用状态
    
    // 其他状态
    gettingField = false;            // 正在获取字段
    localId;                         // 本地ID
    uses;                            // 使用跟踪
}
```

**RecordInternal特性**:
- **状态管理**: 使用符号和Map管理记录状态
- **字段控制**: 详细的字段计算和排序状态控制
- **性能优化**: 使用Map结构提高性能
- **生命周期**: 完整的记录生命周期管理

### 2. 字段准备

```javascript
prepareField(record, fieldName, recordProxy) {
    if (isRelation(record.Model, fieldName)) {
        // 关系字段处理
        const recordList = new RecordList();
        recordList._.owner = record;
        recordList._.name = fieldName;
        record[fieldName] = recordList;
        
        // 设置计算和排序状态
        if (record.Model._.fieldsCompute.get(fieldName)) {
            this.fieldsComputeOnNeed.set(fieldName, true);
        }
        if (record.Model._.fieldsSort.get(fieldName)) {
            this.fieldsSortOnNeed.set(fieldName, true);
        }
    }
}
```

**字段准备功能**:
- **关系字段**: 为关系字段创建RecordList实例
- **计算字段**: 设置计算字段的按需计算状态
- **排序字段**: 设置排序字段的按需排序状态
- **字段绑定**: 将字段与记录正确绑定

### 3. 字段计算

```javascript
compute(record, fieldName) {
    if (this.fieldsComputing.get(fieldName)) {
        return; // 避免循环计算
    }
    
    this.fieldsComputing.set(fieldName, true);
    try {
        const computeFn = record.Model._.fieldsCompute.get(fieldName);
        if (computeFn) {
            const result = computeFn.call(record);
            // 处理计算结果
        }
    } finally {
        this.fieldsComputing.delete(fieldName);
    }
}
```

**字段计算功能**:
- **循环检测**: 防止字段计算的无限循环
- **计算执行**: 执行字段的计算函数
- **状态管理**: 管理字段的计算状态
- **错误处理**: 确保计算状态的正确清理

### 4. 字段排序

```javascript
sort(record, fieldName) {
    if (this.fieldsSorting.get(fieldName)) {
        return; // 避免循环排序
    }
    
    this.fieldsSorting.set(fieldName, true);
    try {
        const sortFn = record.Model._.fieldsSort.get(fieldName);
        if (sortFn) {
            const recordList = record[fieldName];
            recordList.sort(sortFn);
        }
    } finally {
        this.fieldsSorting.delete(fieldName);
    }
}
```

**字段排序功能**:
- **循环检测**: 防止字段排序的无限循环
- **排序执行**: 执行字段的排序函数
- **状态管理**: 管理字段的排序状态
- **列表排序**: 对记录列表进行排序

### 5. 记录删除

```javascript
delete(record) {
    if (record._[IS_DELETED_SYM]) {
        return;
    }
    
    record._[IS_DELETING_SYM] = true;
    try {
        // 执行删除逻辑
        this.performDelete(record);
        record._[IS_DELETED_SYM] = true;
    } finally {
        record._[IS_DELETING_SYM] = false;
    }
}
```

**记录删除功能**:
- **状态检查**: 检查记录是否已删除
- **删除标记**: 标记记录正在删除
- **删除执行**: 执行实际的删除逻辑
- **状态更新**: 更新记录的删除状态

## 使用场景

### 1. 记录内部管理增强

```javascript
// 记录内部管理增强功能
const RecordInternalEnhancer = {
    enhanceRecordInternal: () => {
        const EnhancedRecordInternal = class extends RecordInternal {
            constructor() {
                super();
                
                // 增强的状态管理
                this.enhancedState = {
                    version: 1,
                    lastModified: null,
                    createdAt: Date.now(),
                    accessCount: 0,
                    computeCount: 0,
                    sortCount: 0,
                    errorCount: 0
                };
                
                // 性能监控
                this.performanceMetrics = {
                    fieldAccess: new Map(),
                    fieldCompute: new Map(),
                    fieldSort: new Map(),
                    fieldUpdate: new Map()
                };
                
                // 缓存系统
                this.computeCache = new Map();
                this.sortCache = new Map();
                
                // 依赖跟踪
                this.dependencyGraph = new Map();
                this.computeDependencies = new Map();
                
                // 事件监听器
                this.eventListeners = new Map();
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 设置性能监控
                this.setupPerformanceMonitoring();
                
                // 构建依赖图
                this.buildDependencyGraph();
                
                // 设置缓存管理
                this.setupCacheManagement();
            }
            
            // 增强的字段准备
            prepareField(record, fieldName, recordProxy) {
                try {
                    const startTime = Date.now();
                    
                    // 调用父类方法
                    super.prepareField(record, fieldName, recordProxy);
                    
                    // 记录字段准备
                    this.recordFieldPreparation(fieldName, Date.now() - startTime);
                    
                    // 设置字段监控
                    this.setupFieldMonitoring(record, fieldName);
                    
                    // 构建依赖关系
                    this.buildFieldDependencies(record, fieldName);
                    
                } catch (error) {
                    this.handleError('prepareField', error, { fieldName });
                    throw error;
                }
            }
            
            // 增强的字段计算
            compute(record, fieldName, options = {}) {
                try {
                    // 检查缓存
                    if (!options.force && this.computeCache.has(fieldName)) {
                        const cached = this.computeCache.get(fieldName);
                        if (Date.now() - cached.timestamp < 5000) { // 5秒缓存
                            return cached.value;
                        }
                    }
                    
                    // 检查循环计算
                    if (this.fieldsComputing.get(fieldName)) {
                        console.warn(`检测到字段 ${fieldName} 的循环计算`);
                        return;
                    }
                    
                    const startTime = Date.now();
                    this.fieldsComputing.set(fieldName, true);
                    this.enhancedState.computeCount++;
                    
                    try {
                        // 触发计算前事件
                        this.triggerEvent('beforeCompute', { fieldName, record });
                        
                        // 执行计算
                        const computeFn = record.Model._.fieldsCompute.get(fieldName);
                        if (computeFn) {
                            const result = computeFn.call(record);
                            
                            // 缓存结果
                            this.computeCache.set(fieldName, {
                                value: result,
                                timestamp: Date.now()
                            });
                            
                            // 记录性能指标
                            const duration = Date.now() - startTime;
                            this.recordPerformanceMetric('fieldCompute', fieldName, duration);
                            
                            // 触发计算后事件
                            this.triggerEvent('afterCompute', { fieldName, record, result, duration });
                            
                            return result;
                        }
                    } finally {
                        this.fieldsComputing.delete(fieldName);
                    }
                } catch (error) {
                    this.enhancedState.errorCount++;
                    this.handleError('compute', error, { fieldName });
                    throw error;
                }
            }
            
            // 增强的字段排序
            sort(record, fieldName, options = {}) {
                try {
                    // 检查缓存
                    if (!options.force && this.sortCache.has(fieldName)) {
                        const cached = this.sortCache.get(fieldName);
                        if (Date.now() - cached.timestamp < 10000) { // 10秒缓存
                            return cached.value;
                        }
                    }
                    
                    // 检查循环排序
                    if (this.fieldsSorting.get(fieldName)) {
                        console.warn(`检测到字段 ${fieldName} 的循环排序`);
                        return;
                    }
                    
                    const startTime = Date.now();
                    this.fieldsSorting.set(fieldName, true);
                    this.enhancedState.sortCount++;
                    
                    try {
                        // 触发排序前事件
                        this.triggerEvent('beforeSort', { fieldName, record });
                        
                        // 执行排序
                        const sortFn = record.Model._.fieldsSort.get(fieldName);
                        if (sortFn) {
                            const recordList = record[fieldName];
                            const originalOrder = [...recordList];
                            
                            recordList.sort(sortFn);
                            
                            // 缓存结果
                            this.sortCache.set(fieldName, {
                                value: [...recordList],
                                timestamp: Date.now()
                            });
                            
                            // 记录性能指标
                            const duration = Date.now() - startTime;
                            this.recordPerformanceMetric('fieldSort', fieldName, duration);
                            
                            // 触发排序后事件
                            this.triggerEvent('afterSort', { 
                                fieldName, 
                                record, 
                                originalOrder, 
                                newOrder: [...recordList],
                                duration 
                            });
                        }
                    } finally {
                        this.fieldsSorting.delete(fieldName);
                    }
                } catch (error) {
                    this.enhancedState.errorCount++;
                    this.handleError('sort', error, { fieldName });
                    throw error;
                }
            }
            
            // 请求字段计算
            requestCompute(record, fieldName, options = {}) {
                try {
                    // 检查是否需要计算
                    if (!this.shouldCompute(fieldName, options)) {
                        return;
                    }
                    
                    // 标记需要计算
                    this.fieldsComputeInNeed.set(fieldName, true);
                    
                    // 如果是急切计算，立即执行
                    if (record.Model._.fieldsEager.get(fieldName) || options.eager) {
                        this.compute(record, fieldName, options);
                    }
                    
                    // 记录计算请求
                    this.recordComputeRequest(fieldName, options);
                    
                } catch (error) {
                    this.handleError('requestCompute', error, { fieldName, options });
                }
            }
            
            // 请求字段排序
            requestSort(record, fieldName, options = {}) {
                try {
                    // 检查是否需要排序
                    if (!this.shouldSort(fieldName, options)) {
                        return;
                    }
                    
                    // 标记需要排序
                    this.fieldsSortInNeed.set(fieldName, true);
                    
                    // 如果是急切排序，立即执行
                    if (record.Model._.fieldsEager.get(fieldName) || options.eager) {
                        this.sort(record, fieldName, options);
                    }
                    
                    // 记录排序请求
                    this.recordSortRequest(fieldName, options);
                    
                } catch (error) {
                    this.handleError('requestSort', error, { fieldName, options });
                }
            }
            
            // 检查是否应该计算
            shouldCompute(fieldName, options) {
                // 如果强制计算
                if (options.force) {
                    return true;
                }
                
                // 如果正在计算
                if (this.fieldsComputing.get(fieldName)) {
                    return false;
                }
                
                // 如果有缓存且未过期
                if (this.computeCache.has(fieldName)) {
                    const cached = this.computeCache.get(fieldName);
                    if (Date.now() - cached.timestamp < 5000) {
                        return false;
                    }
                }
                
                return true;
            }
            
            // 检查是否应该排序
            shouldSort(fieldName, options) {
                // 如果强制排序
                if (options.force) {
                    return true;
                }
                
                // 如果正在排序
                if (this.fieldsSorting.get(fieldName)) {
                    return false;
                }
                
                // 如果有缓存且未过期
                if (this.sortCache.has(fieldName)) {
                    const cached = this.sortCache.get(fieldName);
                    if (Date.now() - cached.timestamp < 10000) {
                        return false;
                    }
                }
                
                return true;
            }
            
            // 清理缓存
            clearCache(fieldName = null) {
                if (fieldName) {
                    this.computeCache.delete(fieldName);
                    this.sortCache.delete(fieldName);
                } else {
                    this.computeCache.clear();
                    this.sortCache.clear();
                }
            }
            
            // 记录性能指标
            recordPerformanceMetric(type, fieldName, duration) {
                if (!this.performanceMetrics[type]) {
                    this.performanceMetrics[type] = new Map();
                }
                
                const metrics = this.performanceMetrics[type];
                if (!metrics.has(fieldName)) {
                    metrics.set(fieldName, {
                        count: 0,
                        totalDuration: 0,
                        avgDuration: 0,
                        maxDuration: 0,
                        minDuration: Infinity
                    });
                }
                
                const metric = metrics.get(fieldName);
                metric.count++;
                metric.totalDuration += duration;
                metric.avgDuration = metric.totalDuration / metric.count;
                metric.maxDuration = Math.max(metric.maxDuration, duration);
                metric.minDuration = Math.min(metric.minDuration, duration);
            }
            
            // 事件系统
            addEventListener(event, listener) {
                if (!this.eventListeners.has(event)) {
                    this.eventListeners.set(event, new Set());
                }
                this.eventListeners.get(event).add(listener);
            }
            
            triggerEvent(event, data) {
                const listeners = this.eventListeners.get(event);
                if (listeners) {
                    listeners.forEach(listener => {
                        try {
                            listener(data);
                        } catch (error) {
                            console.error('Event listener error:', error);
                        }
                    });
                }
            }
            
            // 错误处理
            handleError(operation, error, context = {}) {
                console.error(`RecordInternal operation '${operation}' failed:`, error, context);
                this.triggerEvent('error', { operation, error, context });
            }
            
            // 获取统计信息
            getStatistics() {
                return {
                    state: { ...this.enhancedState },
                    performance: {
                        fieldAccess: Object.fromEntries(this.performanceMetrics.fieldAccess || new Map()),
                        fieldCompute: Object.fromEntries(this.performanceMetrics.fieldCompute || new Map()),
                        fieldSort: Object.fromEntries(this.performanceMetrics.fieldSort || new Map()),
                        fieldUpdate: Object.fromEntries(this.performanceMetrics.fieldUpdate || new Map())
                    },
                    cache: {
                        computeCache: this.computeCache.size,
                        sortCache: this.sortCache.size
                    },
                    dependencies: {
                        dependencyGraph: this.dependencyGraph.size,
                        computeDependencies: this.computeDependencies.size
                    }
                };
            }
        };
        
        // 替换原始RecordInternal类
        __exports.RecordInternal = EnhancedRecordInternal;
    }
};

// 应用记录内部管理增强
RecordInternalEnhancer.enhanceRecordInternal();
```

## 技术特点

### 1. 状态管理
- 使用符号标识记录状态
- Map结构管理字段状态
- 高效的状态查询和更新

### 2. 字段控制
- 详细的字段计算状态控制
- 智能的按需计算和排序
- 循环检测和防护机制

### 3. 性能优化
- 延迟计算和排序机制
- 状态缓存和复用
- 最小化的计算开销

### 4. 生命周期管理
- 完整的记录生命周期跟踪
- 状态转换的安全控制
- 资源清理和管理

## 设计模式

### 1. 状态模式 (State Pattern)
- 管理记录的不同状态
- 状态转换的控制和验证

### 2. 延迟加载模式 (Lazy Loading Pattern)
- 按需计算和排序
- 性能优化的延迟处理

### 3. 观察者模式 (Observer Pattern)
- 字段变更的监听和响应
- 依赖关系的自动更新

## 注意事项

1. **循环检测**: 防止字段计算和排序的无限循环
2. **状态一致性**: 确保记录状态的一致性和正确性
3. **性能影响**: 避免频繁的计算和排序影响性能
4. **内存管理**: 注意状态信息的内存使用

## 扩展建议

1. **缓存策略**: 实现更智能的缓存策略和管理
2. **依赖分析**: 添加字段依赖关系的分析和优化
3. **性能监控**: 提供详细的性能监控和分析
4. **调试工具**: 添加字段状态的调试和诊断工具
5. **并发控制**: 实现字段操作的并发控制机制

该记录内部管理模块为邮件模型系统提供了重要的记录状态管理功能，是记录操作的核心内部组件。

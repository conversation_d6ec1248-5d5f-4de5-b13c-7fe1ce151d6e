# Store Internal - 存储内部管理

## 概述

`store_internal.js` 是 Odoo 邮件模型系统的存储内部管理模块，专门负责管理存储的内部状态、队列处理和数据更新机制。该模块基于OWL框架和RecordInternal类，集成了多种队列管理、字段更新、记录删除、数据序列化等核心功能，为邮件模型系统提供了完整的存储内部状态管理和批量操作处理机制，是邮件系统存储层的核心内部组件。

## 文件信息
- **路径**: `/mail/static/src/model/store_internal.js`
- **行数**: 284
- **模块**: `@mail/model/store_internal`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                          // OWL 框架
'@mail/model/record_internal'        // 记录内部模块
'@web/core/l10n/dates'              // 日期本地化
'@mail/model/misc'                   // 工具函数模块
```

## 核心功能

### 1. StoreInternal 类定义

```javascript
const StoreInternal = class StoreInternal extends RecordInternal {
    // 信任状态
    trusted = false;
    
    // 队列系统
    FC_QUEUE = new Map();    // field-computes 字段计算队列
    FS_QUEUE = new Map();    // field-sorts 字段排序队列
    FA_QUEUE = new Map();    // field-onadds 字段添加队列
    FD_QUEUE = new Map();    // field-ondeletes 字段删除队列
    FU_QUEUE = new Map();    // field-onupdates 字段更新队列
    RO_QUEUE = new Map();    // record-onchanges 记录变更队列
    RD_QUEUE = new Map();    // record-deletes 记录删除队列
    RHD_QUEUE = new Map();   // record-hard-deletes 记录硬删除队列
    
    // 更新计数
    UPDATE = 0;
}
```

**StoreInternal特性**:
- **继承扩展**: 继承RecordInternal的所有功能
- **队列管理**: 使用多个队列管理不同类型的操作
- **信任机制**: 通过trusted标志控制HTML字段的自动标记
- **批量处理**: 支持批量操作的队列化处理

### 2. 队列添加

```javascript
ADD_QUEUE(type, ...params) {
    switch (type) {
        case "delete": {
            const [record] = params;
            if (!this.RD_QUEUE.has(record)) {
                this.RD_QUEUE.set(record, true);
            }
            break;
        }
        case "hard_delete": {
            const [record] = params;
            if (!this.RHD_QUEUE.has(record)) {
                this.RHD_QUEUE.set(record, true);
            }
            break;
        }
        case "compute": {
            const [record, fieldName] = params;
            if (!this.FC_QUEUE.has(record)) {
                this.FC_QUEUE.set(record, new Map());
            }
            this.FC_QUEUE.get(record).set(fieldName, true);
            break;
        }
        // ... 其他队列类型
    }
}
```

**队列添加功能**:
- **类型分发**: 根据操作类型分发到不同队列
- **重复检查**: 避免重复添加相同的操作
- **嵌套结构**: 支持复杂的嵌套队列结构
- **参数处理**: 正确处理不同类型操作的参数

### 3. 字段更新

```javascript
updateFields(record, data) {
    for (const [fieldName, value] of Object.entries(data)) {
        if (isMany(record.Model, fieldName)) {
            // 处理一对多字段
            this.updateManyField(record, fieldName, value);
        } else {
            // 处理普通字段
            this.updateAttrField(record, fieldName, value);
        }
    }
}
```

**字段更新功能**:
- **类型识别**: 识别字段类型并采用相应的更新策略
- **批量更新**: 支持多个字段的批量更新
- **关系处理**: 特殊处理关系字段的更新
- **数据验证**: 验证更新数据的有效性

### 4. 数据序列化

```javascript
serializeData(data) {
    const serialized = {};
    for (const [key, value] of Object.entries(data)) {
        if (value instanceof Date) {
            serialized[key] = value.toISOString();
        } else if (value && typeof value === 'object') {
            serialized[key] = this.serializeData(value);
        } else {
            serialized[key] = value;
        }
    }
    return serialized;
}
```

**序列化功能**:
- **类型处理**: 正确处理不同数据类型的序列化
- **递归序列化**: 支持嵌套对象的递归序列化
- **日期处理**: 特殊处理日期类型的序列化
- **数据保持**: 保持数据的完整性和正确性

### 5. HTML字段处理

```javascript
processHtmlField(value) {
    if (this.trusted && typeof value === 'string') {
        return markup(value);
    }
    return value;
}
```

**HTML处理功能**:
- **信任检查**: 基于trusted标志决定是否自动标记
- **安全标记**: 使用markup函数安全地标记HTML内容
- **类型检查**: 确保只对字符串类型进行处理
- **条件处理**: 根据配置决定处理策略

## 使用场景

### 1. 存储内部管理增强

```javascript
// 存储内部管理增强功能
const StoreInternalEnhancer = {
    enhanceStoreInternal: () => {
        const EnhancedStoreInternal = class extends StoreInternal {
            constructor() {
                super();
                
                // 增强的队列管理
                this.queueMetrics = {
                    FC_QUEUE: { processed: 0, errors: 0, avgTime: 0 },
                    FS_QUEUE: { processed: 0, errors: 0, avgTime: 0 },
                    FA_QUEUE: { processed: 0, errors: 0, avgTime: 0 },
                    FD_QUEUE: { processed: 0, errors: 0, avgTime: 0 },
                    FU_QUEUE: { processed: 0, errors: 0, avgTime: 0 },
                    RO_QUEUE: { processed: 0, errors: 0, avgTime: 0 },
                    RD_QUEUE: { processed: 0, errors: 0, avgTime: 0 },
                    RHD_QUEUE: { processed: 0, errors: 0, avgTime: 0 }
                };
                
                // 队列优先级
                this.queuePriorities = {
                    RHD_QUEUE: 1,  // 最高优先级
                    RD_QUEUE: 2,
                    FD_QUEUE: 3,
                    FA_QUEUE: 4,
                    FU_QUEUE: 5,
                    FC_QUEUE: 6,
                    FS_QUEUE: 7,
                    RO_QUEUE: 8    // 最低优先级
                };
                
                // 批处理配置
                this.batchConfig = {
                    maxBatchSize: 100,
                    batchTimeout: 50,
                    enableBatching: true
                };
                
                // 性能监控
                this.performanceMonitor = {
                    enabled: false,
                    sampleRate: 0.1,
                    metrics: new Map()
                };
                
                // 事件系统
                this.eventListeners = new Map();
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 设置性能监控
                this.setupPerformanceMonitoring();
                
                // 设置批处理
                this.setupBatchProcessing();
                
                // 设置错误处理
                this.setupErrorHandling();
            }
            
            // 增强的队列添加
            ADD_QUEUE(type, ...params) {
                try {
                    const startTime = this.performanceMonitor.enabled ? Date.now() : null;
                    
                    // 调用父类方法
                    super.ADD_QUEUE(type, ...params);
                    
                    // 记录性能指标
                    if (startTime) {
                        const duration = Date.now() - startTime;
                        this.recordQueueMetric(type, 'add', duration);
                    }
                    
                    // 触发事件
                    this.triggerEvent('queueAdded', { type, params });
                    
                    // 检查批处理
                    this.checkBatchProcessing(type);
                    
                } catch (error) {
                    this.handleQueueError('ADD_QUEUE', error, { type, params });
                    throw error;
                }
            }
            
            // 增强的字段更新
            updateFields(record, data, options = {}) {
                try {
                    const startTime = Date.now();
                    
                    // 预处理数据
                    const processedData = this.preprocessUpdateData(data, options);
                    
                    // 验证数据
                    if (options.validate !== false) {
                        this.validateUpdateData(record, processedData);
                    }
                    
                    // 记录更新前状态
                    const beforeState = this.captureRecordState(record, Object.keys(processedData));
                    
                    // 执行更新
                    super.updateFields(record, processedData);
                    
                    // 记录更新后状态
                    const afterState = this.captureRecordState(record, Object.keys(processedData));
                    
                    // 记录变更历史
                    this.recordFieldChanges(record, beforeState, afterState);
                    
                    // 记录性能指标
                    const duration = Date.now() - startTime;
                    this.recordPerformanceMetric('updateFields', duration);
                    
                    // 触发事件
                    this.triggerEvent('fieldsUpdated', {
                        record,
                        data: processedData,
                        beforeState,
                        afterState,
                        duration
                    });
                    
                } catch (error) {
                    this.handleUpdateError('updateFields', error, { record, data, options });
                    throw error;
                }
            }
            
            // 预处理更新数据
            preprocessUpdateData(data, options) {
                const processed = {};
                
                for (const [fieldName, value] of Object.entries(data)) {
                    try {
                        // 类型转换
                        let processedValue = this.convertFieldValue(fieldName, value, options);
                        
                        // HTML字段处理
                        if (this.isHtmlField(fieldName)) {
                            processedValue = this.processHtmlField(processedValue);
                        }
                        
                        // 日期字段处理
                        if (this.isDateField(fieldName)) {
                            processedValue = this.processDateField(processedValue);
                        }
                        
                        processed[fieldName] = processedValue;
                    } catch (error) {
                        console.warn(`预处理字段 ${fieldName} 失败:`, error);
                        processed[fieldName] = value; // 使用原始值
                    }
                }
                
                return processed;
            }
            
            // 验证更新数据
            validateUpdateData(record, data) {
                const errors = [];
                
                for (const [fieldName, value] of Object.entries(data)) {
                    try {
                        // 字段存在性检查
                        if (!record.Model._.fields.has(fieldName)) {
                            errors.push(`字段 ${fieldName} 不存在`);
                            continue;
                        }
                        
                        // 类型验证
                        const validation = this.validateFieldValue(record, fieldName, value);
                        if (!validation.isValid) {
                            errors.push(`字段 ${fieldName}: ${validation.error}`);
                        }
                        
                    } catch (error) {
                        errors.push(`验证字段 ${fieldName} 时发生错误: ${error.message}`);
                    }
                }
                
                if (errors.length > 0) {
                    throw new Error(`数据验证失败: ${errors.join(', ')}`);
                }
            }
            
            // 捕获记录状态
            captureRecordState(record, fieldNames) {
                const state = {};
                
                for (const fieldName of fieldNames) {
                    try {
                        state[fieldName] = this.cloneFieldValue(record[fieldName]);
                    } catch (error) {
                        state[fieldName] = null;
                    }
                }
                
                return state;
            }
            
            // 克隆字段值
            cloneFieldValue(value) {
                if (value === null || value === undefined) {
                    return value;
                }
                
                if (typeof value === 'object') {
                    if (value instanceof Date) {
                        return new Date(value.getTime());
                    }
                    
                    if (Array.isArray(value)) {
                        return value.map(item => this.cloneFieldValue(item));
                    }
                    
                    // 对于记录对象，只保存ID
                    if (value.localId) {
                        return { localId: value.localId };
                    }
                    
                    // 普通对象
                    const cloned = {};
                    for (const [key, val] of Object.entries(value)) {
                        cloned[key] = this.cloneFieldValue(val);
                    }
                    return cloned;
                }
                
                return value;
            }
            
            // 批处理队列处理
            processBatchQueues() {
                try {
                    const startTime = Date.now();
                    let totalProcessed = 0;
                    
                    // 按优先级处理队列
                    const queueNames = Object.keys(this.queuePriorities).sort(
                        (a, b) => this.queuePriorities[a] - this.queuePriorities[b]
                    );
                    
                    for (const queueName of queueNames) {
                        const queue = this[queueName];
                        if (queue && queue.size > 0) {
                            const processed = this.processSingleQueue(queueName, queue);
                            totalProcessed += processed;
                            
                            // 检查批处理限制
                            if (totalProcessed >= this.batchConfig.maxBatchSize) {
                                break;
                            }
                        }
                    }
                    
                    // 记录批处理指标
                    const duration = Date.now() - startTime;
                    this.recordBatchMetric(totalProcessed, duration);
                    
                    // 触发事件
                    this.triggerEvent('batchProcessed', {
                        totalProcessed,
                        duration
                    });
                    
                    return totalProcessed;
                } catch (error) {
                    this.handleBatchError('processBatchQueues', error);
                    return 0;
                }
            }
            
            // 处理单个队列
            processSingleQueue(queueName, queue) {
                let processed = 0;
                const startTime = Date.now();
                
                try {
                    // 根据队列类型处理
                    switch (queueName) {
                        case 'FC_QUEUE':
                            processed = this.processComputeQueue(queue);
                            break;
                        case 'FS_QUEUE':
                            processed = this.processSortQueue(queue);
                            break;
                        case 'RD_QUEUE':
                            processed = this.processDeleteQueue(queue);
                            break;
                        // ... 其他队列类型
                        default:
                            processed = this.processGenericQueue(queueName, queue);
                    }
                    
                    // 更新队列指标
                    const duration = Date.now() - startTime;
                    this.updateQueueMetrics(queueName, processed, 0, duration);
                    
                } catch (error) {
                    this.updateQueueMetrics(queueName, processed, 1, Date.now() - startTime);
                    throw error;
                }
                
                return processed;
            }
            
            // 更新队列指标
            updateQueueMetrics(queueName, processed, errors, duration) {
                const metrics = this.queueMetrics[queueName];
                if (metrics) {
                    metrics.processed += processed;
                    metrics.errors += errors;
                    
                    // 计算平均时间
                    const totalOps = metrics.processed + metrics.errors;
                    if (totalOps > 0) {
                        metrics.avgTime = (metrics.avgTime * (totalOps - processed - errors) + duration) / totalOps;
                    }
                }
            }
            
            // 事件系统
            addEventListener(event, listener) {
                if (!this.eventListeners.has(event)) {
                    this.eventListeners.set(event, new Set());
                }
                this.eventListeners.get(event).add(listener);
            }
            
            triggerEvent(event, data) {
                const listeners = this.eventListeners.get(event);
                if (listeners) {
                    listeners.forEach(listener => {
                        try {
                            listener(data);
                        } catch (error) {
                            console.error('Event listener error:', error);
                        }
                    });
                }
            }
            
            // 错误处理
            handleQueueError(operation, error, context = {}) {
                console.error(`StoreInternal queue operation '${operation}' failed:`, error, context);
                this.triggerEvent('queueError', { operation, error, context });
            }
            
            handleUpdateError(operation, error, context = {}) {
                console.error(`StoreInternal update operation '${operation}' failed:`, error, context);
                this.triggerEvent('updateError', { operation, error, context });
            }
            
            handleBatchError(operation, error, context = {}) {
                console.error(`StoreInternal batch operation '${operation}' failed:`, error, context);
                this.triggerEvent('batchError', { operation, error, context });
            }
            
            // 获取统计信息
            getStatistics() {
                return {
                    queues: { ...this.queueMetrics },
                    batch: { ...this.batchConfig },
                    performance: {
                        enabled: this.performanceMonitor.enabled,
                        metrics: Object.fromEntries(this.performanceMonitor.metrics)
                    },
                    state: {
                        trusted: this.trusted,
                        updateLevel: this.UPDATE,
                        queueSizes: {
                            FC_QUEUE: this.FC_QUEUE.size,
                            FS_QUEUE: this.FS_QUEUE.size,
                            FA_QUEUE: this.FA_QUEUE.size,
                            FD_QUEUE: this.FD_QUEUE.size,
                            FU_QUEUE: this.FU_QUEUE.size,
                            RO_QUEUE: this.RO_QUEUE.size,
                            RD_QUEUE: this.RD_QUEUE.size,
                            RHD_QUEUE: this.RHD_QUEUE.size
                        }
                    }
                };
            }
        };
        
        // 替换原始StoreInternal类
        __exports.StoreInternal = EnhancedStoreInternal;
    }
};

// 应用存储内部管理增强
StoreInternalEnhancer.enhanceStoreInternal();
```

## 技术特点

### 1. 队列管理
- 多种类型的操作队列
- 优先级队列处理
- 批量操作优化

### 2. 数据处理
- 智能的数据序列化
- 类型安全的字段更新
- HTML内容的安全处理

### 3. 性能优化
- 批处理减少操作开销
- 队列化的异步处理
- 高效的内存管理

### 4. 状态管理
- 完整的更新状态跟踪
- 信任机制的安全控制
- 错误状态的恢复处理

## 设计模式

### 1. 队列模式 (Queue Pattern)
- 操作的队列化处理
- 批量操作的优化

### 2. 策略模式 (Strategy Pattern)
- 不同类型操作的处理策略
- 可配置的处理行为

### 3. 观察者模式 (Observer Pattern)
- 队列状态的监控
- 操作完成的通知

## 注意事项

1. **队列管理**: 避免队列过大影响内存和性能
2. **批处理**: 合理配置批处理大小和超时时间
3. **错误处理**: 确保队列处理错误的正确恢复
4. **数据一致性**: 保证批量操作的数据一致性

## 扩展建议

1. **队列优化**: 实现更智能的队列调度算法
2. **性能监控**: 添加详细的队列性能监控
3. **错误恢复**: 实现更完善的错误恢复机制
4. **持久化**: 支持队列状态的持久化存储
5. **分布式**: 支持分布式队列处理

该存储内部管理模块为邮件模型系统提供了强大的内部状态管理和批量操作处理功能，是存储层的核心内部组件。

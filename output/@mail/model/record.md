# Record - 记录模块

## 概述

`record.js` 是 Odoo 邮件模型系统的核心记录模块，专门负责管理邮件数据记录的创建、读取、更新和删除操作。该模块基于OWL框架和现代JavaScript技术，集成了记录生命周期管理、字段处理、关系管理、数据序列化等核心功能，为邮件模型系统提供了完整的记录操作和数据管理机制，是邮件系统ORM功能的核心实现组件。

## 文件信息
- **路径**: `/mail/static/src/model/record.js`
- **行数**: 472
- **模块**: `@mail/model/record`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                    // OWL 框架
'@mail/model/misc'             // 工具函数模块
'@web/core/l10n/dates'         // 日期本地化
```

## 核心功能

### 1. Record 类定义

```javascript
const Record = class Record {
    static _;                  // ModelInternal 实例
    _;                         // RecordInternal 实例
    static id;                 // 模型ID
    static env;                // 环境对象
    env;                       // 实例环境对象
    static records;            // 记录集合
    static store;              // 存储实例
}
```

**Record特性**:
- **静态属性**: 模型级别的共享属性和方法
- **实例属性**: 记录级别的属性和状态
- **环境集成**: 与Odoo环境的深度集成
- **存储关联**: 与存储系统的紧密关联

### 2. 静态方法

```javascript
// 更新操作
static MAKE_UPDATE(fn) {
    return this.store.MAKE_UPDATE(...arguments);
}

// 变更监听
static onChange(record, name, cb) {
    return this.store.onChange(...arguments);
}

// 记录获取
static get(data) {
    // 记录获取逻辑
}

// 记录插入
static insert(data) {
    // 记录插入逻辑
}
```

**静态方法功能**:
- **更新管理**: 委托给存储的更新管理机制
- **事件监听**: 提供字段变更的监听功能
- **记录操作**: 提供记录的基本CRUD操作
- **数据处理**: 处理各种数据格式和类型

### 3. 记录获取和创建

```javascript
static get(data) {
    if (!data) {
        return;
    }
    if (isRecord(data)) {
        return data;
    }
    if (typeof data === "string") {
        return this.store.get(data);
    }
    // 处理其他数据类型
}

static insert(data) {
    if (!data) {
        return;
    }
    // 插入逻辑
    return this.MAKE_UPDATE(() => {
        // 创建新记录
    });
}
```

**获取和创建功能**:
- **多格式支持**: 支持多种数据格式的记录获取
- **类型检测**: 智能的数据类型检测和处理
- **记录创建**: 完整的记录创建和初始化流程
- **更新集成**: 与更新系统的无缝集成

### 4. 字段处理

```javascript
// 字段定义处理
static attr(definition) {
    return { [FIELD_DEFINITION_SYM]: true, [ATTR_SYM]: true, ...definition };
}

static many(targetModelName, definition) {
    return { 
        [FIELD_DEFINITION_SYM]: true, 
        [MANY_SYM]: true, 
        targetModelName, 
        ...definition 
    };
}

static one(targetModelName, definition) {
    return { 
        [FIELD_DEFINITION_SYM]: true, 
        [ONE_SYM]: true, 
        targetModelName, 
        ...definition 
    };
}
```

**字段处理功能**:
- **字段类型**: 支持attr、many、one等字段类型
- **关系定义**: 完整的关系字段定义和配置
- **符号标识**: 使用符号确保字段类型的唯一性
- **定义扩展**: 支持字段定义的扩展和定制

### 5. 数据序列化

```javascript
// 序列化方法
toData() {
    const data = {};
    for (const fieldName of this.Model._.fields.keys()) {
        if (this.Model._.fieldsAttr.get(fieldName)) {
            // 处理属性字段
            data[fieldName] = this[fieldName];
        } else if (isRelation(this.Model, fieldName)) {
            // 处理关系字段
            const value = this[fieldName];
            if (isMany(this.Model, fieldName)) {
                data[fieldName] = value.map(record => record.toData());
            } else {
                data[fieldName] = value?.toData();
            }
        }
    }
    return data;
}
```

**序列化功能**:
- **数据导出**: 将记录转换为纯数据对象
- **关系处理**: 正确处理关系字段的序列化
- **递归序列化**: 支持嵌套记录的递归序列化
- **类型保持**: 保持数据类型的正确性

## 使用场景

### 1. 记录模块增强

```javascript
// 记录模块增强功能
const RecordEnhancer = {
    enhanceRecord: () => {
        const EnhancedRecord = class extends Record {
            constructor() {
                super();
                
                // 增强的状态管理
                this.enhancedState = {
                    isNew: false,
                    isDirty: false,
                    isDeleted: false,
                    isLoading: false,
                    lastModified: null,
                    version: 1,
                    changeLog: [],
                    validationErrors: [],
                    metadata: {}
                };
                
                // 字段变更跟踪
                this.fieldChanges = new Map();
                this.originalValues = new Map();
                
                // 验证器
                this.validators = new Map();
                
                // 事件监听器
                this.eventListeners = new Map();
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 设置字段监听
                this.setupFieldWatching();
                
                // 设置验证
                this.setupValidation();
                
                // 设置变更跟踪
                this.setupChangeTracking();
            }
            
            // 增强的字段设置
            setField(fieldName, value, options = {}) {
                try {
                    const oldValue = this[fieldName];
                    
                    // 验证字段值
                    if (!options.skipValidation) {
                        const validation = this.validateField(fieldName, value);
                        if (!validation.isValid) {
                            throw new Error(`字段验证失败: ${validation.errors.join(', ')}`);
                        }
                    }
                    
                    // 记录原始值
                    if (!this.originalValues.has(fieldName)) {
                        this.originalValues.set(fieldName, oldValue);
                    }
                    
                    // 设置字段值
                    this[fieldName] = value;
                    
                    // 记录变更
                    this.recordFieldChange(fieldName, oldValue, value);
                    
                    // 标记为脏数据
                    this.enhancedState.isDirty = true;
                    this.enhancedState.lastModified = Date.now();
                    this.enhancedState.version++;
                    
                    // 触发事件
                    this.triggerEvent('fieldChanged', {
                        fieldName,
                        oldValue,
                        newValue: value
                    });
                    
                    return true;
                } catch (error) {
                    this.handleError('setField', error, { fieldName, value });
                    return false;
                }
            }
            
            // 字段验证
            validateField(fieldName, value) {
                const errors = [];
                
                try {
                    // 获取字段验证器
                    const validator = this.validators.get(fieldName);
                    if (validator) {
                        const result = validator(value, this);
                        if (!result.isValid) {
                            errors.push(...result.errors);
                        }
                    }
                    
                    // 类型验证
                    const fieldType = this.getFieldType(fieldName);
                    if (fieldType && !this.validateFieldType(value, fieldType)) {
                        errors.push(`字段 ${fieldName} 类型不匹配`);
                    }
                    
                    // 必填验证
                    if (this.isRequiredField(fieldName) && (value === null || value === undefined)) {
                        errors.push(`字段 ${fieldName} 是必填的`);
                    }
                    
                    return {
                        isValid: errors.length === 0,
                        errors: errors
                    };
                } catch (error) {
                    return {
                        isValid: false,
                        errors: ['字段验证过程中发生错误']
                    };
                }
            }
            
            // 记录字段变更
            recordFieldChange(fieldName, oldValue, newValue) {
                const change = {
                    fieldName: fieldName,
                    oldValue: oldValue,
                    newValue: newValue,
                    timestamp: Date.now(),
                    type: 'field_change'
                };
                
                this.enhancedState.changeLog.push(change);
                this.fieldChanges.set(fieldName, change);
                
                // 限制变更日志大小
                if (this.enhancedState.changeLog.length > 100) {
                    this.enhancedState.changeLog.splice(0, 1);
                }
            }
            
            // 保存记录
            async save(options = {}) {
                try {
                    this.enhancedState.isLoading = true;
                    
                    // 验证整个记录
                    const validation = this.validate();
                    if (!validation.isValid) {
                        throw new Error(`记录验证失败: ${validation.errors.join(', ')}`);
                    }
                    
                    // 准备保存数据
                    const saveData = this.prepareSaveData();
                    
                    // 执行保存
                    const result = await this.performSave(saveData, options);
                    
                    // 更新状态
                    this.enhancedState.isDirty = false;
                    this.enhancedState.isNew = false;
                    this.fieldChanges.clear();
                    this.originalValues.clear();
                    
                    // 触发事件
                    this.triggerEvent('saved', { result });
                    
                    return result;
                } catch (error) {
                    this.handleError('save', error);
                    throw error;
                } finally {
                    this.enhancedState.isLoading = false;
                }
            }
            
            // 删除记录
            async delete(options = {}) {
                try {
                    this.enhancedState.isLoading = true;
                    
                    // 执行删除
                    const result = await this.performDelete(options);
                    
                    // 更新状态
                    this.enhancedState.isDeleted = true;
                    
                    // 触发事件
                    this.triggerEvent('deleted', { result });
                    
                    return result;
                } catch (error) {
                    this.handleError('delete', error);
                    throw error;
                } finally {
                    this.enhancedState.isLoading = false;
                }
            }
            
            // 重置记录
            reset() {
                try {
                    // 恢复原始值
                    for (const [fieldName, originalValue] of this.originalValues.entries()) {
                        this[fieldName] = originalValue;
                    }
                    
                    // 清理状态
                    this.enhancedState.isDirty = false;
                    this.fieldChanges.clear();
                    this.originalValues.clear();
                    this.enhancedState.changeLog = [];
                    this.enhancedState.validationErrors = [];
                    
                    // 触发事件
                    this.triggerEvent('reset');
                    
                    return true;
                } catch (error) {
                    this.handleError('reset', error);
                    return false;
                }
            }
            
            // 克隆记录
            clone(options = {}) {
                try {
                    const cloneData = this.toData();
                    
                    // 移除ID字段
                    if (options.excludeId !== false) {
                        delete cloneData.id;
                    }
                    
                    // 应用克隆选项
                    if (options.overrides) {
                        Object.assign(cloneData, options.overrides);
                    }
                    
                    // 创建新记录
                    const clonedRecord = this.constructor.insert(cloneData);
                    clonedRecord.enhancedState.isNew = true;
                    
                    return clonedRecord;
                } catch (error) {
                    this.handleError('clone', error);
                    return null;
                }
            }
            
            // 比较记录
            equals(other, options = {}) {
                try {
                    if (!other || other.constructor !== this.constructor) {
                        return false;
                    }
                    
                    const fieldsToCompare = options.fields || 
                        Array.from(this.Model._.fields.keys());
                    
                    for (const fieldName of fieldsToCompare) {
                        if (!this.compareFieldValues(this[fieldName], other[fieldName])) {
                            return false;
                        }
                    }
                    
                    return true;
                } catch (error) {
                    return false;
                }
            }
            
            // 比较字段值
            compareFieldValues(value1, value2) {
                if (value1 === value2) {
                    return true;
                }
                
                if (value1 === null || value1 === undefined || 
                    value2 === null || value2 === undefined) {
                    return value1 === value2;
                }
                
                if (Array.isArray(value1) && Array.isArray(value2)) {
                    if (value1.length !== value2.length) {
                        return false;
                    }
                    return value1.every((item, index) => 
                        this.compareFieldValues(item, value2[index])
                    );
                }
                
                if (typeof value1 === 'object' && typeof value2 === 'object') {
                    if (isRecord(value1) && isRecord(value2)) {
                        return value1.localId === value2.localId;
                    }
                    return JSON.stringify(value1) === JSON.stringify(value2);
                }
                
                return false;
            }
            
            // 获取记录统计
            getStatistics() {
                return {
                    state: { ...this.enhancedState },
                    changes: {
                        count: this.fieldChanges.size,
                        fields: Array.from(this.fieldChanges.keys())
                    },
                    validation: {
                        errorCount: this.enhancedState.validationErrors.length,
                        errors: [...this.enhancedState.validationErrors]
                    },
                    metadata: { ...this.enhancedState.metadata }
                };
            }
        };
        
        // 替换原始Record类
        __exports.Record = EnhancedRecord;
    }
};

// 应用记录增强
RecordEnhancer.enhanceRecord();
```

## 技术特点

### 1. ORM功能
- 完整的对象关系映射
- 自动的关系管理
- 智能的数据转换

### 2. 字段系统
- 多种字段类型支持
- 灵活的字段定义
- 强类型的字段验证

### 3. 生命周期管理
- 完整的记录生命周期
- 自动的状态跟踪
- 事件驱动的更新

### 4. 数据处理
- 智能的数据序列化
- 多格式的数据支持
- 安全的数据转换

## 设计模式

### 1. 活动记录模式 (Active Record Pattern)
- 记录对象包含数据和行为
- 直接的数据库操作方法

### 2. 工厂模式 (Factory Pattern)
- 统一的记录创建接口
- 类型安全的记录构建

### 3. 观察者模式 (Observer Pattern)
- 字段变更的事件通知
- 响应式的数据更新

## 注意事项

1. **内存管理**: 注意记录对象的内存使用和清理
2. **性能优化**: 避免频繁的记录创建和销毁
3. **数据一致性**: 确保关系字段的数据一致性
4. **错误处理**: 提供完善的错误处理和恢复机制

## 扩展建议

1. **验证系统**: 添加更完善的数据验证机制
2. **缓存优化**: 实现智能的记录缓存策略
3. **事务支持**: 添加事务和回滚支持
4. **审计日志**: 实现完整的数据变更审计
5. **性能监控**: 添加记录操作的性能监控

该记录模块为邮件模型系统提供了强大的数据记录管理功能，是系统ORM功能的核心实现。

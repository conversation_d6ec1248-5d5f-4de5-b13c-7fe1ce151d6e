# Record Uses - 记录使用跟踪

## 概述

`record_uses.js` 是 Odoo 邮件模型系统的记录使用跟踪模块，专门负责跟踪和管理记录之间的使用关系。该模块基于Map数据结构，集成了使用计数、关系跟踪、引用管理等核心功能，为邮件模型系统提供了完整的记录依赖关系管理和使用统计机制，是邮件系统记录生命周期管理和内存优化的重要组件。

## 文件信息
- **路径**: `/mail/static/src/model/record_uses.js`
- **行数**: 48
- **模块**: `@mail/model/record_uses`

## 依赖关系

```javascript
// 无外部依赖
// 该模块是独立的工具模块
```

## 核心功能

### 1. RecordUses 类定义

```javascript
const RecordUses = class RecordUses {
    /**
     * Track the uses of a record. Each record contains a single `RecordUses`:
     * - Key: localId of record that uses current record
     * - Value: Map where key is relational field name, and value is number
     *          of time current record is present in this relation.
     *
     * @type {Map<string, Map<string, number>>}
     */
    data = new Map();
}
```

**RecordUses特性**:
- **使用跟踪**: 跟踪记录被其他记录使用的情况
- **嵌套映射**: 使用嵌套Map结构管理复杂的使用关系
- **计数管理**: 精确计算记录在关系字段中的使用次数
- **内存优化**: 为记录的垃圾回收提供依据

### 2. 数据结构

**数据组织方式**:
```
data: Map {
    "record1_localId" => Map {
        "fieldName1" => 2,    // 在fieldName1中使用了2次
        "fieldName2" => 1,    // 在fieldName2中使用了1次
    },
    "record2_localId" => Map {
        "fieldName3" => 1,    // 在fieldName3中使用了1次
    }
}
```

**结构说明**:
- **外层Map**: 键为使用当前记录的记录的localId
- **内层Map**: 键为关系字段名，值为使用次数
- **使用次数**: 记录在特定关系字段中被引用的次数
- **动态管理**: 随着关系的建立和删除动态更新

### 3. 添加使用

```javascript
add(list) {
    const record = list._.owner;
    if (!this.data.has(record.localId)) {
        this.data.set(record.localId, new Map());
    }
    const use = this.data.get(record.localId);
    if (!use.get(list._.name)) {
        use.set(list._.name, 0);
    }
    use.set(list._.name, use.get(list._.name) + 1);
}
```

**添加使用功能**:
- **记录检查**: 检查使用记录是否已存在
- **字段检查**: 检查关系字段是否已存在
- **计数增加**: 将使用次数增加1
- **结构初始化**: 按需初始化嵌套的Map结构

### 4. 删除使用

```javascript
delete(list) {
    const record = list._.owner;
    if (!this.data.has(record.localId)) {
        return;
    }
    const use = this.data.get(record.localId);
    if (!use.get(list._.name)) {
        return;
    }
    use.set(list._.name, use.get(list._.name) - 1);
    if (use.get(list._.name) === 0) {
        use.delete(list._.name);
    }
}
```

**删除使用功能**:
- **存在性检查**: 检查使用记录和字段是否存在
- **计数减少**: 将使用次数减少1
- **零值清理**: 当使用次数为0时删除字段条目
- **内存清理**: 避免保留无用的零值条目

## 使用场景

### 1. 记录使用跟踪增强

```javascript
// 记录使用跟踪增强功能
const RecordUsesEnhancer = {
    enhanceRecordUses: () => {
        const EnhancedRecordUses = class extends RecordUses {
            constructor() {
                super();
                
                // 增强的统计信息
                this.statistics = {
                    totalUses: 0,
                    totalRecords: 0,
                    totalFields: 0,
                    maxUsesPerField: 0,
                    lastModified: null,
                    createdAt: Date.now()
                };
                
                // 使用历史
                this.history = [];
                
                // 事件监听器
                this.eventListeners = new Map();
                
                // 缓存系统
                this.cache = {
                    usageStats: null,
                    dependencyGraph: null,
                    lastCacheUpdate: null
                };
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 设置统计更新
                this.setupStatisticsTracking();
                
                // 设置历史记录
                this.setupHistoryTracking();
                
                // 设置缓存管理
                this.setupCacheManagement();
            }
            
            // 增强的添加使用
            add(list, options = {}) {
                try {
                    const record = list._.owner;
                    const fieldName = list._.name;
                    
                    // 记录操作前状态
                    const beforeCount = this.getUseCount(record.localId, fieldName);
                    
                    // 调用父类方法
                    super.add(list);
                    
                    // 记录操作后状态
                    const afterCount = this.getUseCount(record.localId, fieldName);
                    
                    // 更新统计信息
                    this.updateStatistics('add', record.localId, fieldName, beforeCount, afterCount);
                    
                    // 记录历史
                    this.recordHistory('add', {
                        recordId: record.localId,
                        fieldName: fieldName,
                        beforeCount: beforeCount,
                        afterCount: afterCount,
                        timestamp: Date.now()
                    });
                    
                    // 清理缓存
                    this.invalidateCache();
                    
                    // 触发事件
                    this.triggerEvent('useAdded', {
                        recordId: record.localId,
                        fieldName: fieldName,
                        count: afterCount
                    });
                    
                    return afterCount;
                } catch (error) {
                    this.handleError('add', error, { list, options });
                    throw error;
                }
            }
            
            // 增强的删除使用
            delete(list, options = {}) {
                try {
                    const record = list._.owner;
                    const fieldName = list._.name;
                    
                    // 记录操作前状态
                    const beforeCount = this.getUseCount(record.localId, fieldName);
                    
                    // 调用父类方法
                    super.delete(list);
                    
                    // 记录操作后状态
                    const afterCount = this.getUseCount(record.localId, fieldName);
                    
                    // 更新统计信息
                    this.updateStatistics('delete', record.localId, fieldName, beforeCount, afterCount);
                    
                    // 记录历史
                    this.recordHistory('delete', {
                        recordId: record.localId,
                        fieldName: fieldName,
                        beforeCount: beforeCount,
                        afterCount: afterCount,
                        timestamp: Date.now()
                    });
                    
                    // 清理缓存
                    this.invalidateCache();
                    
                    // 触发事件
                    this.triggerEvent('useDeleted', {
                        recordId: record.localId,
                        fieldName: fieldName,
                        count: afterCount
                    });
                    
                    // 检查是否完全未使用
                    if (afterCount === 0 && this.getTotalUseCount(record.localId) === 0) {
                        this.triggerEvent('recordUnused', {
                            recordId: record.localId
                        });
                    }
                    
                    return afterCount;
                } catch (error) {
                    this.handleError('delete', error, { list, options });
                    throw error;
                }
            }
            
            // 获取使用次数
            getUseCount(recordId, fieldName) {
                const recordUses = this.data.get(recordId);
                if (!recordUses) {
                    return 0;
                }
                return recordUses.get(fieldName) || 0;
            }
            
            // 获取记录的总使用次数
            getTotalUseCount(recordId) {
                const recordUses = this.data.get(recordId);
                if (!recordUses) {
                    return 0;
                }
                
                let total = 0;
                for (const count of recordUses.values()) {
                    total += count;
                }
                return total;
            }
            
            // 获取所有使用该记录的记录
            getUsingRecords(recordId) {
                const usingRecords = [];
                
                for (const [usingRecordId, fields] of this.data.entries()) {
                    if (usingRecordId === recordId) continue;
                    
                    for (const [fieldName, count] of fields.entries()) {
                        if (count > 0) {
                            usingRecords.push({
                                recordId: usingRecordId,
                                fieldName: fieldName,
                                count: count
                            });
                        }
                    }
                }
                
                return usingRecords;
            }
            
            // 检查记录是否被使用
            isRecordUsed(recordId) {
                return this.getTotalUseCount(recordId) > 0;
            }
            
            // 获取未使用的记录
            getUnusedRecords() {
                const unusedRecords = [];
                
                for (const recordId of this.data.keys()) {
                    if (!this.isRecordUsed(recordId)) {
                        unusedRecords.push(recordId);
                    }
                }
                
                return unusedRecords;
            }
            
            // 获取使用统计
            getUsageStatistics() {
                if (this.cache.usageStats && 
                    Date.now() - this.cache.lastCacheUpdate < 5000) {
                    return this.cache.usageStats;
                }
                
                const stats = {
                    totalRecords: this.data.size,
                    totalUses: 0,
                    totalFields: 0,
                    maxUsesPerField: 0,
                    avgUsesPerRecord: 0,
                    unusedRecords: 0,
                    fieldUsage: new Map(),
                    recordUsage: new Map()
                };
                
                for (const [recordId, fields] of this.data.entries()) {
                    let recordTotal = 0;
                    
                    for (const [fieldName, count] of fields.entries()) {
                        stats.totalUses += count;
                        stats.totalFields++;
                        stats.maxUsesPerField = Math.max(stats.maxUsesPerField, count);
                        recordTotal += count;
                        
                        // 字段使用统计
                        if (!stats.fieldUsage.has(fieldName)) {
                            stats.fieldUsage.set(fieldName, { count: 0, records: 0 });
                        }
                        const fieldStat = stats.fieldUsage.get(fieldName);
                        fieldStat.count += count;
                        fieldStat.records++;
                    }
                    
                    if (recordTotal === 0) {
                        stats.unusedRecords++;
                    }
                    
                    stats.recordUsage.set(recordId, recordTotal);
                }
                
                stats.avgUsesPerRecord = stats.totalRecords > 0 ? 
                    stats.totalUses / stats.totalRecords : 0;
                
                // 缓存结果
                this.cache.usageStats = stats;
                this.cache.lastCacheUpdate = Date.now();
                
                return stats;
            }
            
            // 构建依赖图
            buildDependencyGraph() {
                const graph = new Map();
                
                for (const [recordId, fields] of this.data.entries()) {
                    if (!graph.has(recordId)) {
                        graph.set(recordId, {
                            dependencies: new Set(),
                            dependents: new Set()
                        });
                    }
                    
                    for (const [fieldName, count] of fields.entries()) {
                        if (count > 0) {
                            // 这里需要根据实际的关系字段来确定依赖关系
                            // 简化处理，假设fieldName包含目标记录信息
                            graph.get(recordId).dependencies.add(fieldName);
                        }
                    }
                }
                
                return graph;
            }
            
            // 清理未使用的记录
            cleanup(options = {}) {
                const unusedRecords = this.getUnusedRecords();
                let cleanedCount = 0;
                
                for (const recordId of unusedRecords) {
                    if (options.dryRun) {
                        cleanedCount++;
                    } else {
                        this.data.delete(recordId);
                        cleanedCount++;
                        
                        this.triggerEvent('recordCleaned', { recordId });
                    }
                }
                
                if (!options.dryRun) {
                    this.invalidateCache();
                    this.updateStatistics('cleanup', null, null, 0, 0);
                }
                
                return {
                    cleanedCount: cleanedCount,
                    remainingRecords: this.data.size
                };
            }
            
            // 更新统计信息
            updateStatistics(operation, recordId, fieldName, beforeCount, afterCount) {
                this.statistics.lastModified = Date.now();
                
                switch (operation) {
                    case 'add':
                        if (beforeCount === 0) {
                            this.statistics.totalFields++;
                        }
                        this.statistics.totalUses++;
                        break;
                    case 'delete':
                        if (afterCount === 0) {
                            this.statistics.totalFields--;
                        }
                        this.statistics.totalUses--;
                        break;
                    case 'cleanup':
                        // 重新计算统计信息
                        this.recalculateStatistics();
                        break;
                }
            }
            
            // 重新计算统计信息
            recalculateStatistics() {
                this.statistics.totalRecords = this.data.size;
                this.statistics.totalUses = 0;
                this.statistics.totalFields = 0;
                this.statistics.maxUsesPerField = 0;
                
                for (const fields of this.data.values()) {
                    for (const count of fields.values()) {
                        this.statistics.totalUses += count;
                        this.statistics.totalFields++;
                        this.statistics.maxUsesPerField = Math.max(
                            this.statistics.maxUsesPerField, 
                            count
                        );
                    }
                }
            }
            
            // 记录历史
            recordHistory(operation, data) {
                this.history.push({
                    operation: operation,
                    data: data,
                    timestamp: Date.now()
                });
                
                // 限制历史记录大小
                if (this.history.length > 1000) {
                    this.history.splice(0, this.history.length - 1000);
                }
            }
            
            // 清理缓存
            invalidateCache() {
                this.cache.usageStats = null;
                this.cache.dependencyGraph = null;
                this.cache.lastCacheUpdate = null;
            }
            
            // 事件系统
            addEventListener(event, listener) {
                if (!this.eventListeners.has(event)) {
                    this.eventListeners.set(event, new Set());
                }
                this.eventListeners.get(event).add(listener);
            }
            
            triggerEvent(event, data) {
                const listeners = this.eventListeners.get(event);
                if (listeners) {
                    listeners.forEach(listener => {
                        try {
                            listener(data);
                        } catch (error) {
                            console.error('Event listener error:', error);
                        }
                    });
                }
            }
            
            // 错误处理
            handleError(operation, error, context = {}) {
                console.error(`RecordUses operation '${operation}' failed:`, error, context);
                this.triggerEvent('error', { operation, error, context });
            }
            
            // 导出数据
            export() {
                return {
                    data: Object.fromEntries(
                        Array.from(this.data.entries()).map(([key, value]) => [
                            key, 
                            Object.fromEntries(value.entries())
                        ])
                    ),
                    statistics: { ...this.statistics },
                    history: [...this.history]
                };
            }
            
            // 导入数据
            import(exportedData) {
                try {
                    // 清理现有数据
                    this.data.clear();
                    
                    // 导入数据
                    for (const [recordId, fields] of Object.entries(exportedData.data)) {
                        this.data.set(recordId, new Map(Object.entries(fields)));
                    }
                    
                    // 导入统计信息
                    if (exportedData.statistics) {
                        this.statistics = { ...exportedData.statistics };
                    }
                    
                    // 导入历史记录
                    if (exportedData.history) {
                        this.history = [...exportedData.history];
                    }
                    
                    // 清理缓存
                    this.invalidateCache();
                    
                    return true;
                } catch (error) {
                    this.handleError('import', error, { exportedData });
                    return false;
                }
            }
        };
        
        // 替换原始RecordUses类
        __exports.RecordUses = EnhancedRecordUses;
    }
};

// 应用记录使用跟踪增强
RecordUsesEnhancer.enhanceRecordUses();
```

## 技术特点

### 1. 高效数据结构
- 嵌套Map结构提供O(1)查找性能
- 精确的使用次数计算
- 内存友好的数据组织

### 2. 引用计数
- 精确的引用计数管理
- 自动的零值清理
- 内存泄漏防护

### 3. 依赖跟踪
- 完整的记录依赖关系跟踪
- 使用关系的可视化
- 垃圾回收的决策支持

### 4. 性能优化
- 最小化的内存占用
- 高效的查找和更新操作
- 智能的数据清理

## 设计模式

### 1. 引用计数模式 (Reference Counting Pattern)
- 跟踪对象的引用次数
- 自动的内存管理

### 2. 观察者模式 (Observer Pattern)
- 使用关系变化的通知
- 依赖关系的监控

### 3. 统计模式 (Statistics Pattern)
- 使用情况的统计分析
- 性能指标的收集

## 注意事项

1. **内存管理**: 及时清理零值条目避免内存泄漏
2. **并发安全**: 确保多线程环境下的数据一致性
3. **性能影响**: 避免频繁的使用跟踪影响性能
4. **数据一致性**: 确保使用计数的准确性

## 扩展建议

1. **可视化工具**: 提供使用关系的可视化界面
2. **性能监控**: 添加使用跟踪的性能监控
3. **自动清理**: 实现自动的未使用记录清理
4. **依赖分析**: 提供更详细的依赖关系分析
5. **持久化**: 支持使用关系的持久化存储

该记录使用跟踪模块为邮件模型系统提供了重要的依赖关系管理功能，是内存优化和垃圾回收的关键组件。

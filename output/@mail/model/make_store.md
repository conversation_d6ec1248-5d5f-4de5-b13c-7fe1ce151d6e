# Make Store - 存储创建器

## 概述

`make_store.js` 是 Odoo 邮件模型系统的存储创建器，专门负责创建和初始化邮件模型的数据存储实例。该模块基于OWL的响应式系统，集成了模型注册、代理创建、字段处理、关系管理等核心功能，为邮件模型系统提供了完整的存储实例创建和配置机制，是邮件模型系统架构的核心工厂组件。

## 文件信息
- **路径**: `/mail/static/src/model/make_store.js`
- **行数**: 222
- **模块**: `@mail/model/make_store`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                          // OWL 框架
'@mail/model/store'                  // 存储模块
'@mail/model/misc'                   // 工具函数
'@mail/model/record'                 // 记录模块
'@mail/model/store_internal'         // 存储内部模块
'@mail/model/model_internal'         // 模型内部模块
'@mail/model/record_internal'        // 记录内部模块
```

## 核心功能

### 1. 存储创建函数

```javascript
function makeStore(env, { localRegistry } = {}) {
    const recordByLocalId = reactive(new Map());
    Store.env = env;
    let store = new Store();
    store.env = env;
    store.Model = Store;
    store._ = markRaw(new StoreInternal());
    store._raw = store;
    store._proxyInternal = store;
    store._proxy = store;
    store.recordByLocalId = recordByLocalId;
    Record.store = store;
    // ... 模型处理逻辑
    return store._proxy;
}
```

**创建功能**:
- **环境设置**: 设置存储的环境上下文
- **响应式映射**: 创建响应式的记录ID映射
- **内部结构**: 初始化存储的内部管理结构
- **代理系统**: 建立完整的代理访问系统

### 2. 模型注册处理

```javascript
const Models = {};
const chosenModelRegistry = localRegistry ?? modelRegistry;
for (const [, _OgClass] of chosenModelRegistry.getEntries()) {
    const OgClass = _OgClass;
    if (store[OgClass.getName()]) {
        throw new Error(
            `There must be no duplicated Model Names (duplicate found: ${OgClass.getName()})`
        );
    }
    const Model = Object.create(OgClass);
    // ... 模型处理
}
```

**注册功能**:
- **模型遍历**: 遍历注册表中的所有模型
- **重复检查**: 检查模型名称的唯一性
- **原型创建**: 为每个模型创建适当的原型
- **模型存储**: 将处理后的模型存储到Models对象

### 3. 代理系统创建

```javascript
const recordProxyInternal = new Proxy(record, {
    get(record, name, recordFullProxy) {
        recordFullProxy = record._.downgradeProxy(record, recordFullProxy);
        if (record._.gettingField || !Model._.fields.get(name)) {
            let res = Reflect.get(...arguments);
            if (typeof res === "function") {
                res = res.bind(recordFullProxy);
            }
            return res;
        }
        // ... 字段处理逻辑
        return Reflect.get(record, name, recordFullProxy);
    },
    set(record, name, val, receiver) {
        // ... 设置逻辑
        return store.MAKE_UPDATE(function recordSet() {
            store._.updateFields(record, { [name]: val });
            return true;
        });
    },
    deleteProperty(record, name) {
        return store.MAKE_UPDATE(function recordDeleteProperty() {
            if (isRelation(Model, name)) {
                const recordList = record[name];
                recordList.clear();
                return true;
            }
            return Reflect.deleteProperty(record, name);
        });
    }
});
```

**代理功能**:
- **字段访问**: 拦截和处理字段的访问操作
- **计算字段**: 处理计算字段的按需计算
- **关系字段**: 处理关系字段的特殊逻辑
- **更新管理**: 通过MAKE_UPDATE管理字段更新

### 4. 字段处理

```javascript
for (const name of Model._.fields.keys()) {
    record._.prepareField(record, name, recordProxy);
}

// 字段检测
const obj = new OgClass();
obj.setup();
for (const [name, val] of Object.entries(obj)) {
    if (isFieldDefinition(val)) {
        Model._.prepareField(name, val);
    }
}
```

**字段功能**:
- **字段准备**: 为每个字段准备必要的内部结构
- **字段检测**: 自动检测模型中定义的字段
- **字段配置**: 配置字段的各种属性和行为
- **字段验证**: 验证字段定义的正确性

### 5. 关系同步

```javascript
// Sync inverse fields
for (const Model of Object.values(Models)) {
    for (const name of Model._.fields.keys()) {
        if (!isRelation(Model, name)) {
            continue;
        }
        const targetModel = Model._.fieldsTargetModel.get(name);
        const inverse = Model._.fieldsInverse.get(name);
        if (targetModel && !Models[targetModel]) {
            throw new Error(`No target model ${targetModel} exists`);
        }
        if (inverse) {
            const OtherModel = Models[targetModel];
            // ... 反向关系验证和设置
            OtherModel._.fieldsTargetModel.set(inverse, Model.getName());
            OtherModel._.fieldsInverse.set(inverse, name);
        }
    }
}
```

**关系功能**:
- **反向字段**: 同步设置关系字段的反向引用
- **目标验证**: 验证关系字段的目标模型存在性
- **一致性检查**: 确保关系字段配置的一致性
- **急切加载**: 为有反向关系的字段设置急切加载

## 使用场景

### 1. 存储创建器增强

```javascript
// 存储创建器增强功能
const MakeStoreEnhancer = {
    enhanceMakeStore: () => {
        // 增强的存储创建函数
        const enhancedMakeStore = (env, options = {}) => {
            try {
                const defaultOptions = {
                    localRegistry: null,
                    enableCache: true,
                    enableValidation: true,
                    enableMetrics: false,
                    enableDebug: false,
                    maxCacheSize: 10000,
                    cacheTimeout: 300000, // 5分钟
                    validationLevel: 'strict' // strict, loose, none
                };
                
                const config = { ...defaultOptions, ...options };
                
                // 创建增强的记录映射
                const recordByLocalId = reactive(new Map());
                
                // 性能监控
                const metrics = config.enableMetrics ? createMetrics() : null;
                
                // 缓存管理
                const cache = config.enableCache ? createCache(config) : null;
                
                // 验证器
                const validator = config.enableValidation ? createValidator(config) : null;
                
                // 调试器
                const debugger = config.enableDebug ? createDebugger() : null;
                
                // 初始化存储
                Store.env = env;
                let store = new Store();
                store.env = env;
                store.Model = Store;
                store._ = markRaw(new StoreInternal());
                store._raw = store;
                store._proxyInternal = store;
                store._proxy = store;
                store.recordByLocalId = recordByLocalId;
                
                // 增强功能
                if (metrics) store.metrics = metrics;
                if (cache) store.cache = cache;
                if (validator) store.validator = validator;
                if (debugger) store.debugger = debugger;
                
                Record.store = store;
                
                // 处理模型注册
                const Models = {};
                const chosenModelRegistry = config.localRegistry ?? modelRegistry;
                
                for (const [, _OgClass] of chosenModelRegistry.getEntries()) {
                    const OgClass = _OgClass;
                    
                    // 验证模型名称唯一性
                    if (store[OgClass.getName()]) {
                        throw new Error(
                            `重复的模型名称: ${OgClass.getName()}`
                        );
                    }
                    
                    // 创建增强的模型
                    const Model = createEnhancedModel(OgClass, store, config);
                    Models[Model.getName()] = Model;
                    store[Model.getName()] = Model;
                    
                    // 记录模型创建指标
                    if (metrics) {
                        metrics.recordModelCreation(Model.getName());
                    }
                }
                
                // 同步关系字段
                syncRelationFields(Models, validator);
                
                // 创建最终存储
                Object.assign(store.Store, { store, _rawStore: store });
                store = toRaw(store.Store.insert())._raw;
                
                for (const Model of Object.values(Models)) {
                    Model._rawStore = store;
                    Model.store = store._proxy;
                    store._proxy[Model.getName()] = Model;
                }
                
                Object.assign(store, { 
                    Models, 
                    storeReady: true,
                    config: config,
                    createdAt: Date.now()
                });
                
                // 记录存储创建完成
                if (metrics) {
                    metrics.recordStoreCreation(Object.keys(Models).length);
                }
                
                return store._proxy;
                
            } catch (error) {
                console.error('创建存储失败:', error);
                throw error;
            }
        };
        
        // 创建增强的模型
        const createEnhancedModel = (OgClass, store, config) => {
            const Model = Object.create(OgClass);
            
            // 创建增强的类
            const Class = {
                [OgClass.getName()]: class extends OgClass {
                    constructor() {
                        super();
                        this.setup();
                        
                        const record = this;
                        record._raw = record;
                        record.Model = Model;
                        record._ = markRaw(
                            record[STORE_SYM] ? new StoreInternal() : new RecordInternal()
                        );
                        
                        // 创建增强的代理
                        const recordProxyInternal = createEnhancedProxy(record, Model, store, config);
                        record._proxyInternal = recordProxyInternal;
                        
                        const recordProxy = reactive(recordProxyInternal);
                        record._proxy = recordProxy;
                        
                        if (record?.[STORE_SYM]) {
                            record.recordByLocalId = store.recordByLocalId;
                            record._ = markRaw(toRaw(store._));
                            store = record;
                            Record.store = store;
                        }
                        
                        // 准备字段
                        for (const name of Model._.fields.keys()) {
                            record._.prepareField(record, name, recordProxy);
                        }
                        
                        return recordProxy;
                    }
                }
            }[OgClass.getName()];
            
            Model._ = markRaw(new ModelInternal());
            Object.assign(Model, {
                Class,
                env: store.env,
                records: reactive({}),
            });
            
            // 检测和准备字段
            const obj = new OgClass();
            obj.setup();
            for (const [name, val] of Object.entries(obj)) {
                if (isFieldDefinition(val)) {
                    Model._.prepareField(name, val);
                }
            }
            
            return Model;
        };
        
        // 创建增强的代理
        const createEnhancedProxy = (record, Model, store, config) => {
            return new Proxy(record, {
                get(record, name, recordFullProxy) {
                    // 性能监控
                    const startTime = config.enableMetrics ? Date.now() : null;
                    
                    try {
                        recordFullProxy = record._.downgradeProxy(record, recordFullProxy);
                        
                        if (record._.gettingField || !Model._.fields.get(name)) {
                            let res = Reflect.get(...arguments);
                            if (typeof res === "function") {
                                res = res.bind(recordFullProxy);
                            }
                            return res;
                        }
                        
                        // 缓存检查
                        if (config.enableCache && store.cache) {
                            const cacheKey = `${record.localId}.${name}`;
                            const cached = store.cache.get(cacheKey);
                            if (cached !== undefined) {
                                return cached;
                            }
                        }
                        
                        // 计算字段处理
                        if (Model._.fieldsCompute.get(name) && !Model._.fieldsEager.get(name)) {
                            record._.fieldsComputeInNeed.set(name, true);
                            if (record._.fieldsComputeOnNeed.get(name)) {
                                record._.compute(record, name);
                            }
                        }
                        
                        // 排序字段处理
                        if (Model._.fieldsSort.get(name) && !Model._.fieldsEager.get(name)) {
                            record._.fieldsSortInNeed.set(name, true);
                            if (record._.fieldsSortOnNeed.get(name)) {
                                record._.sort(record, name);
                            }
                        }
                        
                        record._.gettingField = true;
                        const val = recordFullProxy[name];
                        record._.gettingField = false;
                        
                        // 关系字段处理
                        if (isRelation(Model, name)) {
                            const recordListFullProxy = val._proxy;
                            const result = isMany(Model, name) ? recordListFullProxy : recordListFullProxy[0];
                            
                            // 缓存结果
                            if (config.enableCache && store.cache) {
                                const cacheKey = `${record.localId}.${name}`;
                                store.cache.set(cacheKey, result);
                            }
                            
                            return result;
                        }
                        
                        const result = Reflect.get(record, name, recordFullProxy);
                        
                        // 缓存结果
                        if (config.enableCache && store.cache) {
                            const cacheKey = `${record.localId}.${name}`;
                            store.cache.set(cacheKey, result);
                        }
                        
                        return result;
                        
                    } finally {
                        // 记录性能指标
                        if (config.enableMetrics && store.metrics && startTime) {
                            const duration = Date.now() - startTime;
                            store.metrics.recordFieldAccess(Model.getName(), name, duration);
                        }
                    }
                },
                
                set(record, name, val, receiver) {
                    // 验证
                    if (config.enableValidation && store.validator) {
                        const validation = store.validator.validateField(Model.getName(), name, val);
                        if (!validation.isValid) {
                            throw new Error(`字段验证失败: ${validation.errors.join(', ')}`);
                        }
                    }
                    
                    // 确保每个字段写入只通过updatingAttrs方法一次
                    if (record._.updatingAttrs.has(name)) {
                        record[name] = val;
                        return true;
                    }
                    
                    return store.MAKE_UPDATE(function recordSet() {
                        const reactiveSet = receiver !== record._proxyInternal;
                        if (reactiveSet) {
                            record._.proxyUsed.set(name, true);
                        }
                        
                        // 清除缓存
                        if (config.enableCache && store.cache) {
                            const cacheKey = `${record.localId}.${name}`;
                            store.cache.delete(cacheKey);
                        }
                        
                        store._.updateFields(record, { [name]: val });
                        
                        if (reactiveSet) {
                            record._.proxyUsed.delete(name);
                        }
                        
                        // 记录字段更新
                        if (config.enableMetrics && store.metrics) {
                            store.metrics.recordFieldUpdate(Model.getName(), name);
                        }
                        
                        return true;
                    });
                },
                
                deleteProperty(record, name) {
                    return store.MAKE_UPDATE(function recordDeleteProperty() {
                        // 清除缓存
                        if (config.enableCache && store.cache) {
                            const cacheKey = `${record.localId}.${name}`;
                            store.cache.delete(cacheKey);
                        }
                        
                        if (isRelation(Model, name)) {
                            const recordList = record[name];
                            recordList.clear();
                            return true;
                        }
                        
                        return Reflect.deleteProperty(record, name);
                    });
                }
            });
        };
        
        // 创建性能监控
        const createMetrics = () => {
            const metrics = {
                modelCreations: 0,
                storeCreations: 0,
                fieldAccesses: new Map(),
                fieldUpdates: new Map(),
                
                recordModelCreation: (modelName) => {
                    metrics.modelCreations++;
                },
                
                recordStoreCreation: (modelCount) => {
                    metrics.storeCreations++;
                },
                
                recordFieldAccess: (modelName, fieldName, duration) => {
                    const key = `${modelName}.${fieldName}`;
                    if (!metrics.fieldAccesses.has(key)) {
                        metrics.fieldAccesses.set(key, { count: 0, totalDuration: 0 });
                    }
                    const stats = metrics.fieldAccesses.get(key);
                    stats.count++;
                    stats.totalDuration += duration;
                },
                
                recordFieldUpdate: (modelName, fieldName) => {
                    const key = `${modelName}.${fieldName}`;
                    if (!metrics.fieldUpdates.has(key)) {
                        metrics.fieldUpdates.set(key, 0);
                    }
                    metrics.fieldUpdates.set(key, metrics.fieldUpdates.get(key) + 1);
                },
                
                getStats: () => ({
                    modelCreations: metrics.modelCreations,
                    storeCreations: metrics.storeCreations,
                    fieldAccesses: Object.fromEntries(metrics.fieldAccesses),
                    fieldUpdates: Object.fromEntries(metrics.fieldUpdates)
                })
            };
            
            return metrics;
        };
        
        // 创建缓存
        const createCache = (config) => {
            const cache = new Map();
            const timestamps = new Map();
            
            return {
                get: (key) => {
                    const timestamp = timestamps.get(key);
                    if (timestamp && Date.now() - timestamp <= config.cacheTimeout) {
                        return cache.get(key);
                    }
                    return undefined;
                },
                
                set: (key, value) => {
                    if (cache.size >= config.maxCacheSize && !cache.has(key)) {
                        const oldestKey = cache.keys().next().value;
                        cache.delete(oldestKey);
                        timestamps.delete(oldestKey);
                    }
                    
                    cache.set(key, value);
                    timestamps.set(key, Date.now());
                },
                
                delete: (key) => {
                    cache.delete(key);
                    timestamps.delete(key);
                },
                
                clear: () => {
                    cache.clear();
                    timestamps.clear();
                },
                
                size: () => cache.size
            };
        };
        
        // 创建验证器
        const createValidator = (config) => {
            return {
                validateField: (modelName, fieldName, value) => {
                    const errors = [];
                    
                    // 基础验证逻辑
                    if (config.validationLevel === 'strict') {
                        // 严格验证
                        if (value === null || value === undefined) {
                            errors.push(`字段 ${fieldName} 不能为空`);
                        }
                    }
                    
                    return {
                        isValid: errors.length === 0,
                        errors: errors
                    };
                }
            };
        };
        
        // 创建调试器
        const createDebugger = () => {
            return {
                log: (message, data) => {
                    console.log(`[Store Debug] ${message}`, data);
                },
                
                warn: (message, data) => {
                    console.warn(`[Store Debug] ${message}`, data);
                },
                
                error: (message, data) => {
                    console.error(`[Store Debug] ${message}`, data);
                }
            };
        };
        
        // 同步关系字段
        const syncRelationFields = (Models, validator) => {
            for (const Model of Object.values(Models)) {
                for (const name of Model._.fields.keys()) {
                    if (!isRelation(Model, name)) {
                        continue;
                    }
                    
                    const targetModel = Model._.fieldsTargetModel.get(name);
                    const inverse = Model._.fieldsInverse.get(name);
                    
                    if (targetModel && !Models[targetModel]) {
                        throw new Error(`目标模型 ${targetModel} 不存在`);
                    }
                    
                    if (inverse) {
                        const OtherModel = Models[targetModel];
                        const rel2TargetModel = OtherModel._.fieldsTargetModel.get(inverse);
                        const rel2Inverse = OtherModel._.fieldsInverse.get(inverse);
                        
                        if (rel2TargetModel && rel2TargetModel !== Model.getName()) {
                            throw new Error(
                                `字段 ${Models[targetModel].getName()}.${inverse} 的目标模型错误`
                            );
                        }
                        
                        if (rel2Inverse && rel2Inverse !== name) {
                            throw new Error(
                                `字段 ${Models[targetModel].getName()}.${inverse} 的反向字段错误`
                            );
                        }
                        
                        OtherModel._.fieldsTargetModel.set(inverse, Model.getName());
                        OtherModel._.fieldsInverse.set(inverse, name);
                        Model._.fieldsEager.set(name, true);
                        OtherModel._.fieldsEager.set(inverse, true);
                    }
                }
            }
        };
        
        // 替换原始函数
        __exports.makeStore = enhancedMakeStore;
    }
};

// 应用存储创建器增强
MakeStoreEnhancer.enhanceMakeStore();
```

## 技术特点

### 1. 响应式系统
- 基于OWL的响应式数据管理
- 自动的依赖追踪和更新
- 高效的变更检测机制

### 2. 代理模式
- 透明的字段访问拦截
- 智能的计算字段处理
- 完整的CRUD操作代理

### 3. 模型管理
- 动态的模型注册和创建
- 完整的字段定义处理
- 关系字段的自动同步

### 4. 内存管理
- 高效的对象创建和销毁
- 智能的缓存和清理机制
- 最小化的内存占用

## 设计模式

### 1. 工厂模式 (Factory Pattern)
- 统一的存储实例创建
- 标准化的模型构建流程

### 2. 代理模式 (Proxy Pattern)
- 透明的字段访问控制
- 智能的操作拦截和处理

### 3. 注册表模式 (Registry Pattern)
- 模型的注册和管理
- 动态的模型发现和加载

## 注意事项

1. **内存管理**: 注意响应式对象的内存使用和清理
2. **性能影响**: 代理操作可能对性能产生影响
3. **循环引用**: 避免模型间的循环依赖
4. **字段同步**: 确保关系字段的正确同步

## 扩展建议

1. **性能优化**: 添加更多的性能监控和优化机制
2. **缓存策略**: 实现更智能的缓存策略和管理
3. **验证系统**: 添加更完善的数据验证系统
4. **调试工具**: 提供更丰富的调试和分析工具
5. **插件系统**: 支持插件化的功能扩展

该存储创建器为邮件模型系统提供了强大的存储实例创建和管理功能，是系统架构的核心组件。

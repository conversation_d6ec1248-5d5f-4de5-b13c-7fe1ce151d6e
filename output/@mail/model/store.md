# Store - 数据存储模块

## 概述

`store.js` 是 Odoo 邮件模型系统的核心数据存储模块，专门负责管理邮件数据的存储、更新和生命周期。该模块基于OWL响应式系统，继承自Record类，集成了记录管理、更新队列、事件处理、数据同步等核心功能，为邮件模型系统提供了完整的数据存储和状态管理机制，是邮件系统数据层的核心组件。

## 文件信息
- **路径**: `/mail/static/src/model/store.js`
- **行数**: 216
- **模块**: `@mail/model/store`

## 依赖关系

```javascript
// 核心依赖
'@mail/model/record'     // 记录模块
'@mail/model/misc'       // 工具函数模块
'@odoo/owl'              // OWL 框架
```

## 核心功能

### 1. Store 类定义

```javascript
const Store = class Store extends Record {
    _;                      // StoreInternal 实例
    [STORE_SYM] = true;     // 存储标识符
    recordByLocalId;        // 记录映射表
    storeReady = false;     // 存储就绪状态
}
```

**Store特性**:
- **继承Record**: 继承Record类的所有功能
- **存储标识**: 使用STORE_SYM标识存储对象
- **记录管理**: 通过recordByLocalId管理所有记录
- **状态跟踪**: 通过storeReady跟踪存储状态

### 2. 记录获取

```javascript
get(localId) {
    return this.recordByLocalId.get(localId);
}
```

**获取功能**:
- **ID查找**: 通过本地ID快速查找记录
- **直接访问**: 提供记录的直接访问接口
- **性能优化**: 使用Map结构优化查找性能
- **类型安全**: 返回正确类型的记录对象

### 3. 更新管理

```javascript
MAKE_UPDATE(fn) {
    this._.UPDATE++;
    const res = fn();
    this._.UPDATE--;
    if (this._.UPDATE === 0) {
        // 处理更新队列
        this._.UPDATE++;
        while (/* 队列不为空 */) {
            // 处理各种队列
            // FC_QUEUE, FS_QUEUE, FA_QUEUE, FD_QUEUE, FU_QUEUE
            // RO_QUEUE, RD_QUEUE, RHD_QUEUE
        }
        this._.UPDATE--;
    }
    return res;
}
```

**更新功能**:
- **更新计数**: 跟踪当前更新操作的嵌套层级
- **队列处理**: 在更新完成后处理各种延迟队列
- **批量操作**: 将多个更新操作批量处理以提高性能
- **一致性保证**: 确保数据更新的一致性和完整性

### 4. 队列处理系统

**队列类型**:
- **FC_QUEUE**: 字段计算队列
- **FS_QUEUE**: 字段排序队列
- **FA_QUEUE**: 字段添加队列
- **FD_QUEUE**: 字段删除队列
- **FU_QUEUE**: 字段更新队列
- **RO_QUEUE**: 记录观察队列
- **RD_QUEUE**: 记录删除队列
- **RHD_QUEUE**: 记录硬删除队列

**队列处理逻辑**:
```javascript
// 字段计算队列处理
while (FC_QUEUE.size > 0) {
    const [record, recMap] = FC_QUEUE.entries().next().value;
    FC_QUEUE.delete(record);
    for (const fieldName of recMap.keys()) {
        record._.requestCompute(record, fieldName, { force: true });
    }
}

// 字段排序队列处理
while (FS_QUEUE.size > 0) {
    const [record, recMap] = FS_QUEUE.entries().next().value;
    FS_QUEUE.delete(record);
    for (const fieldName of recMap.keys()) {
        record._.requestSort(record, fieldName, { force: true });
    }
}
```

## 使用场景

### 1. 数据存储增强

```javascript
// 数据存储增强功能
const StoreEnhancer = {
    enhanceStore: () => {
        const EnhancedStore = class extends Store {
            constructor() {
                super();
                
                // 增强的状态管理
                this.enhancedState = {
                    isInitialized: false,
                    lastUpdateTime: null,
                    updateCount: 0,
                    errorCount: 0,
                    performanceMetrics: {
                        updateDuration: [],
                        queueProcessingTime: [],
                        memoryUsage: []
                    },
                    cacheStats: {
                        hits: 0,
                        misses: 0,
                        evictions: 0
                    }
                };
                
                // 缓存系统
                this.cache = new Map();
                this.cacheTimestamps = new Map();
                this.cacheConfig = {
                    maxSize: 1000,
                    ttl: 300000, // 5分钟
                    enabled: true
                };
                
                // 事件系统
                this.eventListeners = new Map();
                
                // 性能监控
                this.performanceMonitor = {
                    enabled: false,
                    sampleRate: 0.1
                };
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 设置缓存清理定时器
                this.setupCacheCleanup();
                
                // 设置性能监控
                this.setupPerformanceMonitoring();
                
                // 设置错误处理
                this.setupErrorHandling();
                
                // 标记为已初始化
                this.enhancedState.isInitialized = true;
            }
            
            // 增强的记录获取
            get(localId) {
                try {
                    // 检查缓存
                    if (this.cacheConfig.enabled) {
                        const cached = this.getCachedRecord(localId);
                        if (cached) {
                            this.enhancedState.cacheStats.hits++;
                            return cached;
                        }
                        this.enhancedState.cacheStats.misses++;
                    }
                    
                    // 获取记录
                    const record = super.get(localId);
                    
                    // 缓存记录
                    if (record && this.cacheConfig.enabled) {
                        this.setCachedRecord(localId, record);
                    }
                    
                    return record;
                } catch (error) {
                    this.handleError('get', error, { localId });
                    return null;
                }
            }
            
            // 增强的更新管理
            MAKE_UPDATE(fn) {
                const startTime = this.performanceMonitor.enabled ? Date.now() : null;
                
                try {
                    // 更新计数
                    this.enhancedState.updateCount++;
                    this.enhancedState.lastUpdateTime = Date.now();
                    
                    // 执行更新
                    const result = super.MAKE_UPDATE(() => {
                        try {
                            return fn();
                        } catch (error) {
                            this.handleError('update', error);
                            throw error;
                        }
                    });
                    
                    // 记录性能指标
                    if (this.performanceMonitor.enabled && startTime) {
                        const duration = Date.now() - startTime;
                        this.recordPerformanceMetric('updateDuration', duration);
                    }
                    
                    // 清理过期缓存
                    this.cleanupExpiredCache();
                    
                    return result;
                } catch (error) {
                    this.enhancedState.errorCount++;
                    throw error;
                }
            }
            
            // 缓存管理
            getCachedRecord(localId) {
                if (!this.cache.has(localId)) {
                    return null;
                }
                
                const timestamp = this.cacheTimestamps.get(localId);
                if (Date.now() - timestamp > this.cacheConfig.ttl) {
                    this.cache.delete(localId);
                    this.cacheTimestamps.delete(localId);
                    this.enhancedState.cacheStats.evictions++;
                    return null;
                }
                
                return this.cache.get(localId);
            }
            
            setCachedRecord(localId, record) {
                // 检查缓存大小限制
                if (this.cache.size >= this.cacheConfig.maxSize) {
                    // 删除最旧的条目
                    const oldestKey = this.cache.keys().next().value;
                    this.cache.delete(oldestKey);
                    this.cacheTimestamps.delete(oldestKey);
                    this.enhancedState.cacheStats.evictions++;
                }
                
                this.cache.set(localId, record);
                this.cacheTimestamps.set(localId, Date.now());
            }
            
            cleanupExpiredCache() {
                const now = Date.now();
                const expiredKeys = [];
                
                for (const [key, timestamp] of this.cacheTimestamps.entries()) {
                    if (now - timestamp > this.cacheConfig.ttl) {
                        expiredKeys.push(key);
                    }
                }
                
                expiredKeys.forEach(key => {
                    this.cache.delete(key);
                    this.cacheTimestamps.delete(key);
                    this.enhancedState.cacheStats.evictions++;
                });
            }
            
            // 设置缓存清理定时器
            setupCacheCleanup() {
                setInterval(() => {
                    this.cleanupExpiredCache();
                }, 60000); // 每分钟清理一次
            }
            
            // 性能监控
            setupPerformanceMonitoring() {
                if (typeof window !== 'undefined' && window.performance) {
                    this.performanceMonitor.enabled = true;
                }
            }
            
            recordPerformanceMetric(type, value) {
                if (!this.performanceMonitor.enabled) return;
                
                if (Math.random() > this.performanceMonitor.sampleRate) return;
                
                const metrics = this.enhancedState.performanceMetrics[type];
                if (metrics) {
                    metrics.push({
                        value: value,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个指标
                    if (metrics.length > 100) {
                        metrics.splice(0, metrics.length - 100);
                    }
                }
            }
            
            // 错误处理
            setupErrorHandling() {
                this.errorHandlers = new Map();
                
                // 默认错误处理器
                this.errorHandlers.set('default', (operation, error, context) => {
                    console.error(`Store operation '${operation}' failed:`, error, context);
                });
            }
            
            handleError(operation, error, context = {}) {
                const handler = this.errorHandlers.get(operation) || 
                               this.errorHandlers.get('default');
                
                if (handler) {
                    handler(operation, error, context);
                }
                
                // 触发错误事件
                this.triggerEvent('error', { operation, error, context });
            }
            
            // 事件系统
            addEventListener(event, listener) {
                if (!this.eventListeners.has(event)) {
                    this.eventListeners.set(event, new Set());
                }
                this.eventListeners.get(event).add(listener);
            }
            
            removeEventListener(event, listener) {
                const listeners = this.eventListeners.get(event);
                if (listeners) {
                    listeners.delete(listener);
                }
            }
            
            triggerEvent(event, data) {
                const listeners = this.eventListeners.get(event);
                if (listeners) {
                    listeners.forEach(listener => {
                        try {
                            listener(data);
                        } catch (error) {
                            console.error('Event listener error:', error);
                        }
                    });
                }
            }
            
            // 批量操作
            batchUpdate(operations) {
                return this.MAKE_UPDATE(() => {
                    const results = [];
                    for (const operation of operations) {
                        try {
                            const result = operation();
                            results.push({ success: true, result });
                        } catch (error) {
                            results.push({ success: false, error });
                        }
                    }
                    return results;
                });
            }
            
            // 统计信息
            getStatistics() {
                return {
                    state: { ...this.enhancedState },
                    cache: {
                        size: this.cache.size,
                        stats: { ...this.enhancedState.cacheStats },
                        config: { ...this.cacheConfig }
                    },
                    performance: {
                        enabled: this.performanceMonitor.enabled,
                        metrics: { ...this.enhancedState.performanceMetrics }
                    },
                    events: {
                        listenerCount: Array.from(this.eventListeners.values())
                            .reduce((sum, set) => sum + set.size, 0)
                    }
                };
            }
            
            // 清理资源
            cleanup() {
                // 清理缓存
                this.cache.clear();
                this.cacheTimestamps.clear();
                
                // 清理事件监听器
                this.eventListeners.clear();
                
                // 重置状态
                this.enhancedState.isInitialized = false;
            }
        };
        
        // 替换原始Store类
        __exports.Store = EnhancedStore;
    }
};

// 应用存储增强
StoreEnhancer.enhanceStore();
```

## 技术特点

### 1. 响应式架构
- 基于OWL响应式系统
- 自动的依赖追踪和更新
- 高效的变更检测机制

### 2. 队列管理
- 多种类型的更新队列
- 批量处理提高性能
- 保证操作的正确顺序

### 3. 生命周期管理
- 完整的记录生命周期跟踪
- 自动的资源清理机制
- 状态一致性保证

### 4. 性能优化
- 高效的记录查找机制
- 批量更新减少开销
- 智能的队列处理策略

## 设计模式

### 1. 单例模式 (Singleton Pattern)
- 全局唯一的存储实例
- 统一的数据访问入口

### 2. 观察者模式 (Observer Pattern)
- 响应式的数据更新通知
- 自动的依赖关系管理

### 3. 命令模式 (Command Pattern)
- 更新操作的封装和执行
- 支持批量操作和回滚

## 注意事项

1. **内存管理**: 注意大量记录的内存使用
2. **性能影响**: 避免频繁的更新操作影响性能
3. **数据一致性**: 确保并发更新的数据一致性
4. **错误处理**: 提供完善的错误恢复机制

## 扩展建议

1. **缓存策略**: 实现更智能的缓存策略和管理
2. **性能监控**: 添加详细的性能监控和分析
3. **事务支持**: 实现完整的事务和回滚机制
4. **持久化**: 添加数据的持久化存储支持
5. **集群支持**: 支持分布式存储和同步

该存储模块为邮件模型系统提供了强大的数据存储和管理功能，是系统的核心数据层组件。

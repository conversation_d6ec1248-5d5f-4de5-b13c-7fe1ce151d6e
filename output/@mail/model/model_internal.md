# Model Internal - 模型内部管理

## 概述

`model_internal.js` 是 Odoo 邮件模型系统的模型内部管理模块，专门负责管理模型的内部状态、字段定义和配置信息。该模块基于Map数据结构，集成了字段类型管理、关系配置、计算字段、事件处理等核心功能，为邮件模型系统提供了完整的模型元数据管理和字段配置机制，是邮件系统模型定义和管理的核心内部组件。

## 文件信息
- **路径**: `/mail/static/src/model/model_internal.js`
- **行数**: 106
- **模块**: `@mail/model/model_internal`

## 依赖关系

```javascript
// 核心依赖
'@mail/model/misc'   // 工具函数模块，符号定义
```

## 核心功能

### 1. ModelInternal 类定义

```javascript
const ModelInternal = class ModelInternal {
    // 字段映射表
    fields = new Map();              // 所有字段
    fieldsAttr = new Map();          // 属性字段
    fieldsOne = new Map();           // 一对一字段
    fieldsMany = new Map();          // 一对多字段
    fieldsHtml = new Map();          // HTML字段
    
    // 关系配置
    fieldsTargetModel = new Map();   // 目标模型
    fieldsInverse = new Map();       // 反向字段
    
    // 计算和排序
    fieldsCompute = new Map();       // 计算字段
    fieldsSort = new Map();          // 排序字段
    fieldsEager = new Map();         // 急切加载字段
    
    // 事件处理
    fieldsOnAdd = new Map();         // 添加事件
    fieldsOnDelete = new Map();      // 删除事件
    fieldsOnUpdate = new Map();      // 更新事件
    
    // 类型信息
    fieldsType = new Map();          // 字段类型
}
```

**ModelInternal特性**:
- **字段管理**: 使用Map结构高效管理各种字段类型
- **关系配置**: 完整的关系字段配置和管理
- **事件系统**: 字段级别的事件处理机制
- **类型安全**: 详细的字段类型信息管理

### 2. 字段准备方法

```javascript
prepareField(fieldName, data) {
    // 注册字段
    this.fields.set(fieldName, true);
    
    // 检查字段类型
    if (data[ATTR_SYM]) {
        this.fieldsAttr.set(fieldName, true);
    }
    if (data[ONE_SYM]) {
        this.fieldsOne.set(fieldName, true);
    }
    if (data[MANY_SYM]) {
        this.fieldsMany.set(fieldName, true);
    }
    
    // 处理字段配置
    for (const key in data) {
        const value = data[key];
        switch (key) {
            case "html":
                if (value) {
                    this.fieldsHtml.set(fieldName, value);
                }
                break;
            case "targetModel":
                this.fieldsTargetModel.set(fieldName, value);
                break;
            // ... 其他配置处理
        }
    }
}
```

**字段准备功能**:
- **类型识别**: 通过符号识别字段类型
- **配置解析**: 解析和存储字段配置信息
- **映射管理**: 将字段信息存储到相应的映射表
- **验证处理**: 验证字段配置的有效性

### 3. 字段配置处理

**HTML字段配置**:
```javascript
case "html": {
    if (!value) {
        break;
    }
    this.fieldsHtml.set(fieldName, value);
    break;
}
```

**关系字段配置**:
```javascript
case "targetModel": {
    this.fieldsTargetModel.set(fieldName, value);
    break;
}
case "inverse": {
    this.fieldsInverse.set(fieldName, value);
    break;
}
```

**计算字段配置**:
```javascript
case "compute": {
    this.fieldsCompute.set(fieldName, value);
    break;
}
case "sort": {
    this.fieldsSort.set(fieldName, value);
    break;
}
```

**事件处理配置**:
```javascript
case "onAdd": {
    this.fieldsOnAdd.set(fieldName, value);
    break;
}
case "onDelete": {
    this.fieldsOnDelete.set(fieldName, value);
    break;
}
case "onUpdate": {
    this.fieldsOnUpdate.set(fieldName, value);
    break;
}
```

## 使用场景

### 1. 模型内部管理增强

```javascript
// 模型内部管理增强功能
const ModelInternalEnhancer = {
    enhanceModelInternal: () => {
        const EnhancedModelInternal = class extends ModelInternal {
            constructor() {
                super();
                
                // 增强的字段管理
                this.fieldMetadata = new Map();        // 字段元数据
                this.fieldValidators = new Map();      // 字段验证器
                this.fieldDefaults = new Map();        // 字段默认值
                this.fieldConstraints = new Map();     // 字段约束
                this.fieldIndexes = new Map();         // 字段索引
                this.fieldPermissions = new Map();     // 字段权限
                
                // 关系管理增强
                this.relationshipGraph = new Map();    // 关系图
                this.dependencyGraph = new Map();      // 依赖图
                this.circularDependencies = new Set(); // 循环依赖
                
                // 性能监控
                this.performanceMetrics = {
                    fieldAccessCount: new Map(),
                    computeFieldTime: new Map(),
                    sortFieldTime: new Map()
                };
                
                // 缓存系统
                this.fieldCache = new Map();
                this.computeCache = new Map();
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 设置字段监控
                this.setupFieldMonitoring();
                
                // 构建关系图
                this.buildRelationshipGraph();
                
                // 检测循环依赖
                this.detectCircularDependencies();
            }
            
            // 增强的字段准备
            prepareField(fieldName, data) {
                try {
                    // 调用父类方法
                    super.prepareField(fieldName, data);
                    
                    // 准备字段元数据
                    this.prepareFieldMetadata(fieldName, data);
                    
                    // 设置字段验证器
                    this.setupFieldValidator(fieldName, data);
                    
                    // 设置字段默认值
                    this.setupFieldDefault(fieldName, data);
                    
                    // 设置字段约束
                    this.setupFieldConstraints(fieldName, data);
                    
                    // 设置字段权限
                    this.setupFieldPermissions(fieldName, data);
                    
                    // 更新关系图
                    this.updateRelationshipGraph(fieldName, data);
                    
                    // 记录字段准备
                    this.recordFieldPreparation(fieldName, data);
                    
                } catch (error) {
                    console.error(`准备字段 ${fieldName} 失败:`, error);
                    throw error;
                }
            }
            
            // 准备字段元数据
            prepareFieldMetadata(fieldName, data) {
                const metadata = {
                    name: fieldName,
                    type: this.getFieldType(fieldName, data),
                    required: data.required || false,
                    readonly: data.readonly || false,
                    searchable: data.searchable !== false,
                    sortable: data.sortable !== false,
                    filterable: data.filterable !== false,
                    help: data.help || '',
                    label: data.label || fieldName,
                    placeholder: data.placeholder || '',
                    size: data.size || null,
                    precision: data.precision || null,
                    scale: data.scale || null,
                    domain: data.domain || null,
                    context: data.context || {},
                    options: data.options || {},
                    widget: data.widget || null,
                    createdAt: Date.now(),
                    version: 1
                };
                
                this.fieldMetadata.set(fieldName, metadata);
            }
            
            // 获取字段类型
            getFieldType(fieldName, data) {
                if (data[ATTR_SYM]) return 'attr';
                if (data[ONE_SYM]) return 'one';
                if (data[MANY_SYM]) return 'many';
                return 'unknown';
            }
            
            // 设置字段验证器
            setupFieldValidator(fieldName, data) {
                const validators = [];
                
                // 必填验证
                if (data.required) {
                    validators.push((value) => {
                        if (value === null || value === undefined || value === '') {
                            return { isValid: false, error: `字段 ${fieldName} 是必填的` };
                        }
                        return { isValid: true };
                    });
                }
                
                // 类型验证
                const fieldType = this.getFieldType(fieldName, data);
                validators.push((value) => {
                    return this.validateFieldType(value, fieldType, data);
                });
                
                // 自定义验证器
                if (data.validator && typeof data.validator === 'function') {
                    validators.push(data.validator);
                }
                
                // 域验证
                if (data.domain) {
                    validators.push((value) => {
                        return this.validateDomain(value, data.domain);
                    });
                }
                
                this.fieldValidators.set(fieldName, validators);
            }
            
            // 验证字段类型
            validateFieldType(value, fieldType, data) {
                try {
                    switch (fieldType) {
                        case 'attr':
                            return this.validateAttrField(value, data);
                        case 'one':
                            return this.validateOneField(value, data);
                        case 'many':
                            return this.validateManyField(value, data);
                        default:
                            return { isValid: true };
                    }
                } catch (error) {
                    return { isValid: false, error: error.message };
                }
            }
            
            // 验证属性字段
            validateAttrField(value, data) {
                if (value === null || value === undefined) {
                    return { isValid: true };
                }
                
                const type = data.type || 'string';
                
                switch (type) {
                    case 'string':
                    case 'text':
                        if (typeof value !== 'string') {
                            return { isValid: false, error: '值必须是字符串类型' };
                        }
                        break;
                    case 'integer':
                        if (!Number.isInteger(value)) {
                            return { isValid: false, error: '值必须是整数类型' };
                        }
                        break;
                    case 'float':
                        if (typeof value !== 'number') {
                            return { isValid: false, error: '值必须是数字类型' };
                        }
                        break;
                    case 'boolean':
                        if (typeof value !== 'boolean') {
                            return { isValid: false, error: '值必须是布尔类型' };
                        }
                        break;
                    case 'date':
                    case 'datetime':
                        if (!(value instanceof Date) && typeof value !== 'string') {
                            return { isValid: false, error: '值必须是日期类型' };
                        }
                        break;
                }
                
                return { isValid: true };
            }
            
            // 验证一对一字段
            validateOneField(value, data) {
                if (value === null || value === undefined) {
                    return { isValid: true };
                }
                
                // 检查是否为记录对象
                if (!isRecord(value)) {
                    return { isValid: false, error: '值必须是记录对象' };
                }
                
                // 检查目标模型
                const targetModel = data.targetModel;
                if (targetModel && value.Model.getName() !== targetModel) {
                    return { 
                        isValid: false, 
                        error: `值必须是 ${targetModel} 模型的记录` 
                    };
                }
                
                return { isValid: true };
            }
            
            // 验证一对多字段
            validateManyField(value, data) {
                if (value === null || value === undefined) {
                    return { isValid: true };
                }
                
                // 检查是否为记录列表
                if (!isRecordList(value)) {
                    return { isValid: false, error: '值必须是记录列表' };
                }
                
                // 检查目标模型
                const targetModel = data.targetModel;
                if (targetModel) {
                    for (const record of value) {
                        if (record.Model.getName() !== targetModel) {
                            return { 
                                isValid: false, 
                                error: `所有记录必须是 ${targetModel} 模型的记录` 
                            };
                        }
                    }
                }
                
                return { isValid: true };
            }
            
            // 构建关系图
            buildRelationshipGraph() {
                for (const [fieldName] of this.fieldsTargetModel) {
                    const targetModel = this.fieldsTargetModel.get(fieldName);
                    const inverse = this.fieldsInverse.get(fieldName);
                    
                    if (!this.relationshipGraph.has(targetModel)) {
                        this.relationshipGraph.set(targetModel, new Set());
                    }
                    
                    this.relationshipGraph.get(targetModel).add({
                        field: fieldName,
                        inverse: inverse,
                        type: this.fieldsOne.get(fieldName) ? 'one' : 'many'
                    });
                }
            }
            
            // 检测循环依赖
            detectCircularDependencies() {
                const visited = new Set();
                const recursionStack = new Set();
                
                const dfs = (model, path = []) => {
                    if (recursionStack.has(model)) {
                        // 发现循环依赖
                        const cycle = path.slice(path.indexOf(model));
                        this.circularDependencies.add(cycle);
                        return true;
                    }
                    
                    if (visited.has(model)) {
                        return false;
                    }
                    
                    visited.add(model);
                    recursionStack.add(model);
                    path.push(model);
                    
                    const relations = this.relationshipGraph.get(model) || new Set();
                    for (const relation of relations) {
                        if (dfs(relation.targetModel, [...path])) {
                            return true;
                        }
                    }
                    
                    recursionStack.delete(model);
                    path.pop();
                    return false;
                };
                
                for (const model of this.relationshipGraph.keys()) {
                    if (!visited.has(model)) {
                        dfs(model);
                    }
                }
            }
            
            // 验证字段值
            validateField(fieldName, value) {
                const validators = this.fieldValidators.get(fieldName) || [];
                const errors = [];
                
                for (const validator of validators) {
                    try {
                        const result = validator(value);
                        if (!result.isValid) {
                            errors.push(result.error);
                        }
                    } catch (error) {
                        errors.push(`验证器执行失败: ${error.message}`);
                    }
                }
                
                return {
                    isValid: errors.length === 0,
                    errors: errors
                };
            }
            
            // 获取字段信息
            getFieldInfo(fieldName) {
                return {
                    exists: this.fields.has(fieldName),
                    type: this.getFieldTypeInfo(fieldName),
                    metadata: this.fieldMetadata.get(fieldName),
                    isAttr: this.fieldsAttr.has(fieldName),
                    isOne: this.fieldsOne.has(fieldName),
                    isMany: this.fieldsMany.has(fieldName),
                    isHtml: this.fieldsHtml.has(fieldName),
                    isCompute: this.fieldsCompute.has(fieldName),
                    isSort: this.fieldsSort.has(fieldName),
                    isEager: this.fieldsEager.has(fieldName),
                    targetModel: this.fieldsTargetModel.get(fieldName),
                    inverse: this.fieldsInverse.get(fieldName),
                    hasOnAdd: this.fieldsOnAdd.has(fieldName),
                    hasOnDelete: this.fieldsOnDelete.has(fieldName),
                    hasOnUpdate: this.fieldsOnUpdate.has(fieldName)
                };
            }
            
            // 获取所有字段
            getAllFields() {
                return Array.from(this.fields.keys());
            }
            
            // 获取特定类型的字段
            getFieldsByType(type) {
                switch (type) {
                    case 'attr':
                        return Array.from(this.fieldsAttr.keys());
                    case 'one':
                        return Array.from(this.fieldsOne.keys());
                    case 'many':
                        return Array.from(this.fieldsMany.keys());
                    case 'compute':
                        return Array.from(this.fieldsCompute.keys());
                    case 'sort':
                        return Array.from(this.fieldsSort.keys());
                    default:
                        return [];
                }
            }
            
            // 获取统计信息
            getStatistics() {
                return {
                    totalFields: this.fields.size,
                    attrFields: this.fieldsAttr.size,
                    oneFields: this.fieldsOne.size,
                    manyFields: this.fieldsMany.size,
                    computeFields: this.fieldsCompute.size,
                    sortFields: this.fieldsSort.size,
                    htmlFields: this.fieldsHtml.size,
                    eagerFields: this.fieldsEager.size,
                    relationships: this.fieldsTargetModel.size,
                    circularDependencies: this.circularDependencies.size,
                    performanceMetrics: { ...this.performanceMetrics }
                };
            }
        };
        
        // 替换原始ModelInternal类
        __exports.ModelInternal = EnhancedModelInternal;
    }
};

// 应用模型内部管理增强
ModelInternalEnhancer.enhanceModelInternal();
```

## 技术特点

### 1. 高效数据结构
- 使用Map结构提供O(1)的查找性能
- 分类存储不同类型的字段信息
- 内存友好的数据组织方式

### 2. 字段类型管理
- 完整的字段类型分类和管理
- 符号标识确保类型安全
- 灵活的字段配置处理

### 3. 关系管理
- 完整的关系字段配置
- 反向关系的自动管理
- 目标模型的验证和检查

### 4. 事件系统
- 字段级别的事件处理
- 生命周期事件的管理
- 可扩展的事件机制

## 设计模式

### 1. 元数据模式 (Metadata Pattern)
- 存储和管理模型的元数据信息
- 运行时的字段信息查询

### 2. 映射模式 (Mapping Pattern)
- 使用Map结构管理字段映射
- 高效的字段信息存储和检索

### 3. 配置模式 (Configuration Pattern)
- 灵活的字段配置处理
- 可扩展的配置选项

## 注意事项

1. **内存使用**: 注意大量字段时的内存使用
2. **性能影响**: 避免频繁的字段查询影响性能
3. **配置验证**: 确保字段配置的正确性和一致性
4. **类型安全**: 保持字段类型的一致性和安全性

## 扩展建议

1. **字段验证**: 添加更完善的字段验证机制
2. **性能监控**: 实现字段访问的性能监控
3. **缓存优化**: 添加字段信息的缓存机制
4. **配置管理**: 提供更灵活的字段配置管理
5. **调试支持**: 添加字段调试和分析工具

该模型内部管理模块为邮件模型系统提供了重要的元数据管理功能，是模型定义和配置的核心组件。

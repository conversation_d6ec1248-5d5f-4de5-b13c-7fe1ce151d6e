# Debug Manager - 调试管理器

## 概述

`debug_manager.js` 实现了 Odoo 邮件模块的调试管理功能，专门为开发者提供调试工具和消息管理功能。该模块基于Odoo的调试注册表系统，提供了消息管理的调试入口，允许开发者在表单视图的调试模式下快速访问和管理与当前记录相关的邮件消息，是邮件模块开发和调试的重要工具组件。

## 文件信息
- **路径**: `/mail/static/src/js/tools/debug_manager.js`
- **行数**: 47
- **模块**: `@mail/js/tools/debug_manager`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'   // 国际化翻译
'@web/core/registry'           // 注册表系统
```

## 核心功能

### 1. 消息管理函数

```javascript
function manageMessages({ component, env }) {
    const resId = component.model.root.resId;
    if (!resId) {
        return null; // No record
    }
    const description = _t("Messages");
    return {
        type: "item",
        description,
        callback: () => {
            env.services.action.doAction({
                res_model: "mail.message",
                name: description,
                views: [
                    [false, "list"],
                    [false, "form"],
                ],
                type: "ir.actions.act_window",
                domain: [
                    ["res_id", "=", resId],
                    ["model", "=", component.props.resModel],
                ],
                context: {
                    default_res_model: component.props.resModel,
                    default_res_id: resId,
                },
            });
        },
        sequence: 130,
        section: "record",
    };
}
```

**消息管理功能**:
- **记录检查**: 检查当前组件是否有有效的记录ID
- **动作配置**: 配置打开消息列表的窗口动作
- **域过滤**: 根据当前记录过滤相关消息
- **上下文设置**: 设置默认的模型和记录ID上下文

### 2. 调试项配置

**调试项属性**:
- **type**: "item" - 调试项类型
- **description**: 国际化的描述文本
- **callback**: 执行动作的回调函数
- **sequence**: 130 - 在调试菜单中的排序序号
- **section**: "record" - 所属的调试菜单分组

### 3. 动作执行

**窗口动作配置**:
- **res_model**: "mail.message" - 目标模型
- **name**: 动作名称
- **views**: 列表视图和表单视图
- **type**: "ir.actions.act_window" - 窗口动作类型
- **domain**: 过滤条件
- **context**: 默认上下文

### 4. 注册表注册

```javascript
registry.category("debug").category("form").add("mail.manageMessages", manageMessages);
```

**注册功能**:
- **调试分类**: 注册到调试注册表的表单分类
- **标识符**: "mail.manageMessages" - 唯一标识符
- **函数绑定**: 绑定manageMessages函数

## 使用场景

### 1. 调试管理器增强

```javascript
// 调试管理器增强功能
const DebugManagerEnhancer = {
    enhanceDebugManager: () => {
        // 增强的消息管理函数
        const enhancedManageMessages = ({ component, env }) => {
            try {
                const resId = component.model.root.resId;
                const resModel = component.props.resModel;
                
                if (!resId) {
                    return null; // No record
                }
                
                const description = _t("Messages");
                
                return {
                    type: "item",
                    description,
                    callback: async () => {
                        try {
                            // 获取消息统计
                            const messageStats = await getMessageStatistics(resId, resModel, env);
                            
                            // 构建增强的动作
                            const action = {
                                res_model: "mail.message",
                                name: `${description} (${messageStats.total})`,
                                views: [
                                    [false, "list"],
                                    [false, "form"],
                                ],
                                type: "ir.actions.act_window",
                                domain: [
                                    ["res_id", "=", resId],
                                    ["model", "=", resModel],
                                ],
                                context: {
                                    default_res_model: resModel,
                                    default_res_id: resId,
                                    search_default_group_by_message_type: 1,
                                    search_default_group_by_author: 1,
                                },
                                help: `<p>管理记录 ${resModel}:${resId} 的所有消息</p>
                                      <p>总消息数: ${messageStats.total}</p>
                                      <p>未读消息: ${messageStats.unread}</p>
                                      <p>最新消息: ${messageStats.latest}</p>`
                            };
                            
                            // 执行动作
                            env.services.action.doAction(action);
                            
                            // 记录调试操作
                            recordDebugAction('manage_messages', {
                                resId: resId,
                                resModel: resModel,
                                messageCount: messageStats.total
                            });
                            
                        } catch (error) {
                            console.error('打开消息管理失败:', error);
                            env.services.notification.add(
                                '打开消息管理失败: ' + error.message,
                                { type: 'error' }
                            );
                        }
                    },
                    sequence: 130,
                    section: "record",
                };
            } catch (error) {
                console.error('创建消息管理调试项失败:', error);
                return null;
            }
        };
        
        // 获取消息统计
        const getMessageStatistics = async (resId, resModel, env) => {
            try {
                const orm = env.services.orm;
                
                // 获取消息总数
                const total = await orm.searchCount("mail.message", [
                    ["res_id", "=", resId],
                    ["model", "=", resModel]
                ]);
                
                // 获取未读消息数
                const unread = await orm.searchCount("mail.message", [
                    ["res_id", "=", resId],
                    ["model", "=", resModel],
                    ["needaction", "=", true]
                ]);
                
                // 获取最新消息
                const latestMessages = await orm.searchRead("mail.message", [
                    ["res_id", "=", resId],
                    ["model", "=", resModel]
                ], ["date", "author_id"], {
                    limit: 1,
                    order: "date desc"
                });
                
                const latest = latestMessages.length > 0 
                    ? formatDate(latestMessages[0].date)
                    : '无';
                
                return {
                    total: total,
                    unread: unread,
                    latest: latest
                };
            } catch (error) {
                console.error('获取消息统计失败:', error);
                return {
                    total: 0,
                    unread: 0,
                    latest: '未知'
                };
            }
        };
        
        // 格式化日期
        const formatDate = (dateString) => {
            try {
                const date = new Date(dateString);
                return date.toLocaleString();
            } catch (error) {
                return dateString;
            }
        };
        
        // 记录调试操作
        const recordDebugAction = (action, data) => {
            try {
                const debugActions = JSON.parse(
                    localStorage.getItem('debug_actions') || '[]'
                );
                
                debugActions.push({
                    action: action,
                    data: data,
                    timestamp: Date.now(),
                    user: env.services.user?.user?.name || 'unknown'
                });
                
                // 保留最近100个操作
                if (debugActions.length > 100) {
                    debugActions.splice(0, debugActions.length - 100);
                }
                
                localStorage.setItem('debug_actions', JSON.stringify(debugActions));
            } catch (error) {
                console.warn('记录调试操作失败:', error);
            }
        };
        
        // 附件管理调试项
        const manageAttachments = ({ component, env }) => {
            try {
                const resId = component.model.root.resId;
                const resModel = component.props.resModel;
                
                if (!resId) {
                    return null;
                }
                
                const description = _t("Attachments");
                
                return {
                    type: "item",
                    description,
                    callback: async () => {
                        try {
                            // 获取附件统计
                            const attachmentStats = await getAttachmentStatistics(resId, resModel, env);
                            
                            const action = {
                                res_model: "ir.attachment",
                                name: `${description} (${attachmentStats.total})`,
                                views: [
                                    [false, "list"],
                                    [false, "form"],
                                ],
                                type: "ir.actions.act_window",
                                domain: [
                                    ["res_id", "=", resId],
                                    ["res_model", "=", resModel],
                                ],
                                context: {
                                    default_res_model: resModel,
                                    default_res_id: resId,
                                },
                                help: `<p>管理记录 ${resModel}:${resId} 的所有附件</p>
                                      <p>总附件数: ${attachmentStats.total}</p>
                                      <p>总大小: ${attachmentStats.totalSize}</p>`
                            };
                            
                            env.services.action.doAction(action);
                            
                            recordDebugAction('manage_attachments', {
                                resId: resId,
                                resModel: resModel,
                                attachmentCount: attachmentStats.total
                            });
                            
                        } catch (error) {
                            console.error('打开附件管理失败:', error);
                            env.services.notification.add(
                                '打开附件管理失败: ' + error.message,
                                { type: 'error' }
                            );
                        }
                    },
                    sequence: 131,
                    section: "record",
                };
            } catch (error) {
                console.error('创建附件管理调试项失败:', error);
                return null;
            }
        };
        
        // 获取附件统计
        const getAttachmentStatistics = async (resId, resModel, env) => {
            try {
                const orm = env.services.orm;
                
                const total = await orm.searchCount("ir.attachment", [
                    ["res_id", "=", resId],
                    ["res_model", "=", resModel]
                ]);
                
                const attachments = await orm.searchRead("ir.attachment", [
                    ["res_id", "=", resId],
                    ["res_model", "=", resModel]
                ], ["file_size"]);
                
                const totalSize = attachments.reduce((sum, att) => sum + (att.file_size || 0), 0);
                const formattedSize = formatFileSize(totalSize);
                
                return {
                    total: total,
                    totalSize: formattedSize
                };
            } catch (error) {
                console.error('获取附件统计失败:', error);
                return {
                    total: 0,
                    totalSize: '0 B'
                };
            }
        };
        
        // 格式化文件大小
        const formatFileSize = (bytes) => {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        };
        
        // 活动管理调试项
        const manageActivities = ({ component, env }) => {
            try {
                const resId = component.model.root.resId;
                const resModel = component.props.resModel;
                
                if (!resId) {
                    return null;
                }
                
                const description = _t("Activities");
                
                return {
                    type: "item",
                    description,
                    callback: async () => {
                        try {
                            const action = {
                                res_model: "mail.activity",
                                name: description,
                                views: [
                                    [false, "list"],
                                    [false, "form"],
                                ],
                                type: "ir.actions.act_window",
                                domain: [
                                    ["res_id", "=", resId],
                                    ["res_model", "=", resModel],
                                ],
                                context: {
                                    default_res_model: resModel,
                                    default_res_id: resId,
                                },
                            };
                            
                            env.services.action.doAction(action);
                            
                            recordDebugAction('manage_activities', {
                                resId: resId,
                                resModel: resModel
                            });
                            
                        } catch (error) {
                            console.error('打开活动管理失败:', error);
                            env.services.notification.add(
                                '打开活动管理失败: ' + error.message,
                                { type: 'error' }
                            );
                        }
                    },
                    sequence: 132,
                    section: "record",
                };
            } catch (error) {
                console.error('创建活动管理调试项失败:', error);
                return null;
            }
        };
        
        // 关注者管理调试项
        const manageFollowers = ({ component, env }) => {
            try {
                const resId = component.model.root.resId;
                const resModel = component.props.resModel;
                
                if (!resId) {
                    return null;
                }
                
                const description = _t("Followers");
                
                return {
                    type: "item",
                    description,
                    callback: async () => {
                        try {
                            const action = {
                                res_model: "mail.followers",
                                name: description,
                                views: [
                                    [false, "list"],
                                    [false, "form"],
                                ],
                                type: "ir.actions.act_window",
                                domain: [
                                    ["res_id", "=", resId],
                                    ["res_model", "=", resModel],
                                ],
                                context: {
                                    default_res_model: resModel,
                                    default_res_id: resId,
                                },
                            };
                            
                            env.services.action.doAction(action);
                            
                            recordDebugAction('manage_followers', {
                                resId: resId,
                                resModel: resModel
                            });
                            
                        } catch (error) {
                            console.error('打开关注者管理失败:', error);
                            env.services.notification.add(
                                '打开关注者管理失败: ' + error.message,
                                { type: 'error' }
                            );
                        }
                    },
                    sequence: 133,
                    section: "record",
                };
            } catch (error) {
                console.error('创建关注者管理调试项失败:', error);
                return null;
            }
        };
        
        // 调试统计信息
        const showDebugStats = ({ component, env }) => {
            try {
                const description = _t("Mail Debug Stats");
                
                return {
                    type: "item",
                    description,
                    callback: async () => {
                        try {
                            const stats = await getMailDebugStatistics(env);
                            
                            const action = {
                                type: "ir.actions.client",
                                tag: "display_notification",
                                params: {
                                    title: "邮件调试统计",
                                    message: `
                                        <div>
                                            <h4>系统统计</h4>
                                            <p>总消息数: ${stats.totalMessages}</p>
                                            <p>总附件数: ${stats.totalAttachments}</p>
                                            <p>总活动数: ${stats.totalActivities}</p>
                                            <p>总关注者数: ${stats.totalFollowers}</p>
                                            <h4>调试操作</h4>
                                            <p>调试操作次数: ${stats.debugActions}</p>
                                            <p>最后操作时间: ${stats.lastDebugAction}</p>
                                        </div>
                                    `,
                                    type: "info",
                                    sticky: true
                                }
                            };
                            
                            env.services.action.doAction(action);
                            
                        } catch (error) {
                            console.error('显示调试统计失败:', error);
                        }
                    },
                    sequence: 140,
                    section: "other",
                };
            } catch (error) {
                console.error('创建调试统计项失败:', error);
                return null;
            }
        };
        
        // 获取邮件调试统计
        const getMailDebugStatistics = async (env) => {
            try {
                const orm = env.services.orm;
                
                const [totalMessages, totalAttachments, totalActivities, totalFollowers] = await Promise.all([
                    orm.searchCount("mail.message", []),
                    orm.searchCount("ir.attachment", []),
                    orm.searchCount("mail.activity", []),
                    orm.searchCount("mail.followers", [])
                ]);
                
                const debugActions = JSON.parse(
                    localStorage.getItem('debug_actions') || '[]'
                );
                
                const lastAction = debugActions.length > 0 
                    ? new Date(debugActions[debugActions.length - 1].timestamp).toLocaleString()
                    : '无';
                
                return {
                    totalMessages: totalMessages,
                    totalAttachments: totalAttachments,
                    totalActivities: totalActivities,
                    totalFollowers: totalFollowers,
                    debugActions: debugActions.length,
                    lastDebugAction: lastAction
                };
            } catch (error) {
                console.error('获取邮件调试统计失败:', error);
                return {
                    totalMessages: 0,
                    totalAttachments: 0,
                    totalActivities: 0,
                    totalFollowers: 0,
                    debugActions: 0,
                    lastDebugAction: '未知'
                };
            }
        };
        
        // 注册增强的调试项
        registry.category("debug").category("form").add("mail.manageMessages", enhancedManageMessages, { force: true });
        registry.category("debug").category("form").add("mail.manageAttachments", manageAttachments);
        registry.category("debug").category("form").add("mail.manageActivities", manageActivities);
        registry.category("debug").category("form").add("mail.manageFollowers", manageFollowers);
        registry.category("debug").category("form").add("mail.debugStats", showDebugStats);
    }
};

// 应用调试管理器增强
DebugManagerEnhancer.enhanceDebugManager();
```

## 技术特点

### 1. 调试集成
- 与Odoo调试系统的深度集成
- 标准的调试项注册机制
- 开发者友好的调试界面

### 2. 动作系统
- 标准的Odoo动作配置
- 窗口动作的完整支持
- 上下文和域的正确设置

### 3. 国际化支持
- 完整的多语言支持
- 翻译函数的正确使用
- 用户界面的本地化

### 4. 错误处理
- 记录存在性检查
- 空值处理机制
- 用户友好的错误反馈

## 设计模式

### 1. 工厂模式 (Factory Pattern)
- 调试项的动态创建
- 配置驱动的项目生成

### 2. 注册表模式 (Registry Pattern)
- 调试功能的注册和管理
- 模块化的功能组织

### 3. 回调模式 (Callback Pattern)
- 异步操作的处理
- 事件驱动的交互

## 注意事项

1. **调试模式**: 该功能仅在调试模式下可用
2. **权限检查**: 需要适当的用户权限才能访问
3. **性能影响**: 调试功能可能影响系统性能
4. **数据安全**: 调试功能可能暴露敏感数据

## 扩展建议

1. **功能扩展**: 添加更多邮件相关的调试功能
2. **统计信息**: 提供详细的邮件统计和分析
3. **批量操作**: 支持批量消息管理操作
4. **导出功能**: 支持消息和附件的导出
5. **性能分析**: 添加邮件性能分析工具

该调试管理器为邮件模块的开发和维护提供了重要的调试工具支持。

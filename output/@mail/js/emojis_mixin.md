# Emojis Mixin - 表情符号混入

## 概述

`emojis_mixin.js` 实现了 Odoo 邮件系统的表情符号处理功能，专门提供文本消息中表情符号的格式化和样式处理。该模块基于Unicode表情符号标准，集成了HTML转义、表情符号识别、CSS样式包装和换行处理等特性，为邮件消息中的表情符号提供了专业的显示效果和样式控制，是邮件系统文本格式化和用户体验的重要组件。

## 文件信息
- **路径**: `/mail/static/src/js/emojis_mixin.js`
- **行数**: 34
- **模块**: `@mail/js/emojis_mixin`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/strings'   // 字符串工具，HTML转义功能
```

## 核心功能

### 1. 文本格式化函数

```javascript
function formatText(message) {
    message = escape(message);
    message = message.replaceAll(
        /(\p{Emoji_Presentation}+)/gu,
        "<span class='o_mail_emoji'>$1</span>"
    );
    message = message.replace(/(?:\r\n|\r|\n)/g, "<br>");
    
    return message;
}
```

**格式化功能**:
- **HTML转义**: 使用escape函数防止HTML注入攻击
- **表情符号包装**: 为表情符号序列添加CSS类包装
- **换行处理**: 将文本换行符转换为HTML换行标签
- **安全处理**: 确保输入文本的安全性和正确显示

### 2. 表情符号识别

**Unicode正则表达式**:
- **模式**: `/(\p{Emoji_Presentation}+)/gu`
- **Unicode属性**: `\p{Emoji_Presentation}` - 匹配具有表情符号显示特性的字符
- **全局匹配**: `g` 标志进行全局匹配
- **Unicode支持**: `u` 标志启用Unicode支持

**识别特性**:
- **序列匹配**: 匹配表情符号序列而非单个字符
- **复合表情符号**: 支持复合表情符号如 👩🏿 (👩 + 🏿)
- **显示特性**: 仅匹配具有不同于普通文本显示特性的字符
- **标准兼容**: 符合Unicode表情符号标准

### 3. CSS样式包装

**样式类应用**:
- **CSS类**: `o_mail_emoji` - 专用的表情符号样式类
- **HTML包装**: `<span class='o_mail_emoji'>$1</span>`
- **样式控制**: 允许通过CSS控制表情符号的显示效果
- **一致性**: 确保所有表情符号的一致显示

### 4. 安全处理

**HTML转义**:
- **防注入**: 防止恶意HTML代码注入
- **字符转义**: 转义特殊HTML字符
- **安全显示**: 确保用户输入的安全显示
- **标准处理**: 使用标准的转义函数

## 使用场景

### 1. 表情符号处理增强

```javascript
// 表情符号处理增强功能
const EmojisMixinEnhancer = {
    enhanceEmojisMixin: () => {
        // 增强的文本格式化函数
        const enhancedFormatText = (message, options = {}) => {
            try {
                // 默认选项
                const defaultOptions = {
                    escapeHtml: true,
                    wrapEmojis: true,
                    convertNewlines: true,
                    emojiClass: 'o_mail_emoji',
                    customEmojiSupport: false,
                    emojiSize: 'normal', // small, normal, large
                    animatedEmojis: false,
                    emojiTooltips: false,
                    accessibilityLabels: false
                };
                
                const config = { ...defaultOptions, ...options };
                let formattedMessage = message;
                
                // HTML转义
                if (config.escapeHtml) {
                    formattedMessage = escape(formattedMessage);
                }
                
                // 自定义表情符号处理
                if (config.customEmojiSupport) {
                    formattedMessage = processCustomEmojis(formattedMessage, config);
                }
                
                // 标准表情符号处理
                if (config.wrapEmojis) {
                    formattedMessage = wrapStandardEmojis(formattedMessage, config);
                }
                
                // 换行处理
                if (config.convertNewlines) {
                    formattedMessage = formattedMessage.replace(/(?:\r\n|\r|\n)/g, "<br>");
                }
                
                return formattedMessage;
                
            } catch (error) {
                console.error('格式化文本失败:', error);
                return escape(message); // 降级处理
            }
        };
        
        // 处理自定义表情符号
        const processCustomEmojis = (message, config) => {
            try {
                // 自定义表情符号模式 :emoji_name:
                const customEmojiPattern = /:([a-zA-Z0-9_+-]+):/g;
                
                return message.replace(customEmojiPattern, (match, emojiName) => {
                    const emojiData = getCustomEmojiData(emojiName);
                    
                    if (emojiData) {
                        const attributes = buildEmojiAttributes(emojiData, config);
                        return `<img ${attributes} alt="${match}" />`;
                    }
                    
                    return match; // 保持原样
                });
            } catch (error) {
                console.error('处理自定义表情符号失败:', error);
                return message;
            }
        };
        
        // 包装标准表情符号
        const wrapStandardEmojis = (message, config) => {
            try {
                const emojiClass = getEmojiClass(config);
                const emojiPattern = /(\p{Emoji_Presentation}+)/gu;
                
                return message.replaceAll(emojiPattern, (match) => {
                    const attributes = buildStandardEmojiAttributes(match, config);
                    return `<span class="${emojiClass}" ${attributes}>${match}</span>`;
                });
            } catch (error) {
                console.error('包装标准表情符号失败:', error);
                return message;
            }
        };
        
        // 获取表情符号CSS类
        const getEmojiClass = (config) => {
            let classes = [config.emojiClass];
            
            if (config.emojiSize !== 'normal') {
                classes.push(`o_mail_emoji_${config.emojiSize}`);
            }
            
            if (config.animatedEmojis) {
                classes.push('o_mail_emoji_animated');
            }
            
            return classes.join(' ');
        };
        
        // 构建标准表情符号属性
        const buildStandardEmojiAttributes = (emoji, config) => {
            const attributes = [];
            
            if (config.emojiTooltips) {
                const tooltip = getEmojiTooltip(emoji);
                if (tooltip) {
                    attributes.push(`title="${tooltip}"`);
                }
            }
            
            if (config.accessibilityLabels) {
                const label = getEmojiAccessibilityLabel(emoji);
                if (label) {
                    attributes.push(`aria-label="${label}"`);
                }
            }
            
            return attributes.join(' ');
        };
        
        // 获取自定义表情符号数据
        const getCustomEmojiData = (emojiName) => {
            try {
                // 这里可以从服务器或本地缓存获取自定义表情符号数据
                const customEmojis = getCustomEmojisCache();
                return customEmojis[emojiName] || null;
            } catch (error) {
                console.error('获取自定义表情符号数据失败:', error);
                return null;
            }
        };
        
        // 构建表情符号属性
        const buildEmojiAttributes = (emojiData, config) => {
            const attributes = [
                `src="${emojiData.url}"`,
                `class="o_mail_custom_emoji ${config.emojiClass}"`,
                `data-emoji-name="${emojiData.name}"`
            ];
            
            if (emojiData.width && emojiData.height) {
                attributes.push(`width="${emojiData.width}"`);
                attributes.push(`height="${emojiData.height}"`);
            }
            
            if (config.emojiTooltips && emojiData.description) {
                attributes.push(`title="${emojiData.description}"`);
            }
            
            if (config.accessibilityLabels) {
                const label = emojiData.description || emojiData.name;
                attributes.push(`aria-label="表情符号: ${label}"`);
            }
            
            return attributes.join(' ');
        };
        
        // 获取表情符号工具提示
        const getEmojiTooltip = (emoji) => {
            try {
                // 这里可以实现表情符号的描述获取逻辑
                const emojiDescriptions = getEmojiDescriptionsCache();
                return emojiDescriptions[emoji] || null;
            } catch (error) {
                return null;
            }
        };
        
        // 获取表情符号无障碍标签
        const getEmojiAccessibilityLabel = (emoji) => {
            try {
                const tooltip = getEmojiTooltip(emoji);
                return tooltip ? `表情符号: ${tooltip}` : `表情符号: ${emoji}`;
            } catch (error) {
                return `表情符号: ${emoji}`;
            }
        };
        
        // 获取自定义表情符号缓存
        const getCustomEmojisCache = () => {
            try {
                const cached = localStorage.getItem('custom_emojis_cache');
                return cached ? JSON.parse(cached) : {};
            } catch (error) {
                return {};
            }
        };
        
        // 获取表情符号描述缓存
        const getEmojiDescriptionsCache = () => {
            try {
                const cached = localStorage.getItem('emoji_descriptions_cache');
                return cached ? JSON.parse(cached) : {};
            } catch (error) {
                return {};
            }
        };
        
        // 表情符号统计分析
        const analyzeEmojis = (message) => {
            try {
                const emojiPattern = /(\p{Emoji_Presentation}+)/gu;
                const emojis = message.match(emojiPattern) || [];
                
                const stats = {
                    totalEmojis: emojis.length,
                    uniqueEmojis: [...new Set(emojis)].length,
                    emojiList: emojis,
                    emojiFrequency: {},
                    hasEmojis: emojis.length > 0
                };
                
                // 计算表情符号频率
                emojis.forEach(emoji => {
                    stats.emojiFrequency[emoji] = (stats.emojiFrequency[emoji] || 0) + 1;
                });
                
                return stats;
            } catch (error) {
                console.error('分析表情符号失败:', error);
                return {
                    totalEmojis: 0,
                    uniqueEmojis: 0,
                    emojiList: [],
                    emojiFrequency: {},
                    hasEmojis: false
                };
            }
        };
        
        // 表情符号验证
        const validateEmojis = (message) => {
            try {
                const issues = [];
                
                // 检查过多的表情符号
                const emojiStats = analyzeEmojis(message);
                if (emojiStats.totalEmojis > 50) {
                    issues.push({
                        type: 'too_many_emojis',
                        message: '消息中包含过多表情符号',
                        count: emojiStats.totalEmojis
                    });
                }
                
                // 检查连续的表情符号
                const consecutivePattern = /(\p{Emoji_Presentation}{10,})/gu;
                const consecutiveMatches = message.match(consecutivePattern);
                if (consecutiveMatches) {
                    issues.push({
                        type: 'consecutive_emojis',
                        message: '检测到连续的表情符号序列',
                        sequences: consecutiveMatches
                    });
                }
                
                return {
                    isValid: issues.length === 0,
                    issues: issues,
                    stats: emojiStats
                };
            } catch (error) {
                console.error('验证表情符号失败:', error);
                return {
                    isValid: true,
                    issues: [],
                    stats: null
                };
            }
        };
        
        // 表情符号搜索
        const searchEmojis = (query, options = {}) => {
            try {
                const defaultOptions = {
                    includeCustom: true,
                    includeStandard: true,
                    limit: 20,
                    fuzzySearch: false
                };
                
                const config = { ...defaultOptions, ...options };
                const results = [];
                
                // 搜索自定义表情符号
                if (config.includeCustom) {
                    const customResults = searchCustomEmojis(query, config);
                    results.push(...customResults);
                }
                
                // 搜索标准表情符号
                if (config.includeStandard) {
                    const standardResults = searchStandardEmojis(query, config);
                    results.push(...standardResults);
                }
                
                // 限制结果数量
                return results.slice(0, config.limit);
            } catch (error) {
                console.error('搜索表情符号失败:', error);
                return [];
            }
        };
        
        // 搜索自定义表情符号
        const searchCustomEmojis = (query, config) => {
            try {
                const customEmojis = getCustomEmojisCache();
                const results = [];
                
                Object.entries(customEmojis).forEach(([name, data]) => {
                    if (matchesQuery(name, query, config) || 
                        matchesQuery(data.description || '', query, config)) {
                        results.push({
                            type: 'custom',
                            name: name,
                            data: data,
                            display: `:${name}:`
                        });
                    }
                });
                
                return results;
            } catch (error) {
                return [];
            }
        };
        
        // 搜索标准表情符号
        const searchStandardEmojis = (query, config) => {
            try {
                const descriptions = getEmojiDescriptionsCache();
                const results = [];
                
                Object.entries(descriptions).forEach(([emoji, description]) => {
                    if (matchesQuery(description, query, config)) {
                        results.push({
                            type: 'standard',
                            emoji: emoji,
                            description: description,
                            display: emoji
                        });
                    }
                });
                
                return results;
            } catch (error) {
                return [];
            }
        };
        
        // 查询匹配
        const matchesQuery = (text, query, config) => {
            if (!text || !query) return false;
            
            const normalizedText = text.toLowerCase();
            const normalizedQuery = query.toLowerCase();
            
            if (config.fuzzySearch) {
                return fuzzyMatch(normalizedText, normalizedQuery);
            } else {
                return normalizedText.includes(normalizedQuery);
            }
        };
        
        // 模糊匹配
        const fuzzyMatch = (text, query) => {
            let textIndex = 0;
            let queryIndex = 0;
            
            while (textIndex < text.length && queryIndex < query.length) {
                if (text[textIndex] === query[queryIndex]) {
                    queryIndex++;
                }
                textIndex++;
            }
            
            return queryIndex === query.length;
        };
        
        // 替换原始函数
        __exports.formatText = enhancedFormatText;
        __exports.analyzeEmojis = analyzeEmojis;
        __exports.validateEmojis = validateEmojis;
        __exports.searchEmojis = searchEmojis;
    }
};

// 应用表情符号处理增强
EmojisMixinEnhancer.enhanceEmojisMixin();
```

## 技术特点

### 1. Unicode标准支持
- 基于Unicode表情符号标准
- 支持复合表情符号
- 正确的字符识别和处理

### 2. 安全性保障
- HTML转义防止注入攻击
- 安全的文本处理流程
- 用户输入的安全验证

### 3. 样式控制
- CSS类的统一应用
- 可定制的表情符号样式
- 一致的视觉效果

### 4. 性能优化
- 高效的正则表达式匹配
- 最小化的DOM操作
- 优化的文本处理算法

## 设计模式

### 1. 混入模式 (Mixin Pattern)
- 可复用的功能模块
- 灵活的功能组合

### 2. 管道模式 (Pipeline Pattern)
- 顺序的文本处理步骤
- 可扩展的处理流程

### 3. 策略模式 (Strategy Pattern)
- 不同的表情符号处理策略
- 可配置的处理行为

## 注意事项

1. **Unicode支持**: 确保浏览器对Unicode表情符号的支持
2. **性能考虑**: 避免在大量文本中频繁使用正则表达式
3. **样式兼容**: 确保CSS样式在不同浏览器中的兼容性
4. **安全处理**: 始终对用户输入进行适当的转义和验证

## 扩展建议

1. **自定义表情符号**: 支持自定义表情符号的上传和管理
2. **表情符号搜索**: 实现表情符号的搜索和选择功能
3. **动画支持**: 支持动画表情符号的显示
4. **无障碍支持**: 添加表情符号的无障碍访问支持
5. **性能优化**: 优化大量表情符号的处理性能

该混入模块为邮件系统提供了专业的表情符号处理功能，确保了表情符号的正确显示和安全处理。

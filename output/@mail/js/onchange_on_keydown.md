# OnChange On Keydown - 按键变更处理

## 概述

`onchange_on_keydown.js` 实现了 Odoo 邮件系统的按键变更处理功能，专门为文本字段提供基于键盘输入的实时变更检测和处理。该模块基于OWL框架和Odoo的补丁机制，集成了防抖处理、事件监听、字段扩展和属性提取等特性，为CharField和TextField组件提供了键盘输入时的自动变更触发功能，是邮件系统实时交互和用户体验优化的重要组件。

## 文件信息
- **路径**: `/mail/static/src/js/onchange_on_keydown.js`
- **行数**: 85
- **模块**: `@mail/js/onchange_on_keydown`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                    // OWL 框架
'@web/core/utils/patch'                        // 补丁工具
'@web/core/utils/strings'                      // 字符串工具
'@web/core/utils/timing'                       // 时间工具
'@web/views/fields/char/char_field'            // 字符字段
'@web/views/fields/text/text_field'            // 文本字段
```

## 核心功能

### 1. 按键变更混入

```javascript
const onchangeOnKeydownMixin = () => ({
    setup() {
        super.setup(...arguments);

        if (this.props.onchangeOnKeydown) {
            const input = this.input || this.textareaRef;

            const triggerOnChange = useDebounced(
                this.triggerOnChange,
                this.props.keydownDebounceDelay
            );
            useEffect(() => {
                if (input.el) {
                    input.el.addEventListener("keydown", triggerOnChange);
                    return () => {
                        input.el.removeEventListener("keydown", triggerOnChange);
                    };
                }
            });
        }
    },

    triggerOnChange() {
        const input = this.input || this.textareaRef;
        input.el.dispatchEvent(new Event("change"));
    },
});
```

**混入功能**:
- **条件激活**: 仅在onchangeOnKeydown属性为真时激活
- **输入元素**: 支持input和textarea元素
- **防抖处理**: 使用useDebounced进行防抖处理
- **事件监听**: 添加和清理keydown事件监听器

### 2. 防抖机制

**防抖配置**:
- **函数**: `useDebounced(this.triggerOnChange, this.props.keydownDebounceDelay)`
- **默认延迟**: 2000毫秒（2秒）
- **可配置**: 通过keydownDebounceDelay属性配置
- **性能优化**: 避免频繁触发变更事件

### 3. 事件处理

**事件监听**:
- **事件类型**: keydown事件
- **事件绑定**: 在useEffect中绑定事件监听器
- **清理机制**: 在组件卸载时自动清理事件监听器
- **事件触发**: 通过dispatchEvent触发change事件

### 4. 字段补丁

```javascript
patch(CharField.prototype, onchangeOnKeydownMixin());
patch(TextField.prototype, onchangeOnKeydownMixin());
```

**补丁应用**:
- **CharField**: 为字符字段添加按键变更功能
- **TextField**: 为文本字段添加按键变更功能
- **原型扩展**: 通过补丁机制扩展字段原型
- **功能增强**: 在不修改原始代码的情况下增强功能

### 5. 属性扩展

```javascript
CharField.props = {
    ...CharField.props,
    onchangeOnKeydown: { type: Boolean, optional: true },
    keydownDebounceDelay: { type: Number, optional: true },
};

TextField.props = {
    ...TextField.props,
    onchangeOnKeydown: { type: Boolean, optional: true },
    keydownDebounceDelay: { type: Number, optional: true },
};
```

**属性定义**:
- **onchangeOnKeydown**: 布尔类型，控制功能启用
- **keydownDebounceDelay**: 数字类型，配置防抖延迟
- **可选属性**: 两个属性都是可选的
- **类型安全**: 明确的属性类型定义

### 6. 属性提取

```javascript
const charExtractProps = charField.extractProps;
charField.extractProps = (fieldInfo) => {
    return Object.assign(charExtractProps(fieldInfo), {
        onchangeOnKeydown: exprToBoolean(fieldInfo.attrs.onchange_on_keydown),
        keydownDebounceDelay: fieldInfo.attrs.keydown_debounce_delay
            ? Number(fieldInfo.attrs.keydown_debounce_delay)
            : 2000,
    });
};
```

**提取功能**:
- **属性映射**: 将XML属性映射到组件属性
- **类型转换**: 布尔和数字类型的正确转换
- **默认值**: 为防抖延迟提供默认值2000毫秒
- **向后兼容**: 保持原有属性提取功能

## 使用场景

### 1. 按键变更处理增强

```javascript
// 按键变更处理增强功能
const OnChangeOnKeydownEnhancer = {
    enhanceOnChangeOnKeydown: () => {
        // 增强的按键变更混入
        const enhancedOnchangeOnKeydownMixin = () => ({
            setup() {
                super.setup(...arguments);

                if (this.props.onchangeOnKeydown) {
                    const input = this.input || this.textareaRef;
                    
                    // 增强的配置
                    const config = {
                        debounceDelay: this.props.keydownDebounceDelay || 2000,
                        minLength: this.props.onchangeMinLength || 0,
                        maxLength: this.props.onchangeMaxLength || Infinity,
                        triggerKeys: this.props.onchangeTriggerKeys || null,
                        excludeKeys: this.props.onchangeExcludeKeys || ['Tab', 'Shift', 'Control', 'Alt'],
                        enablePaste: this.props.onchangeOnPaste !== false,
                        enableCut: this.props.onchangeOnCut !== false,
                        trackChanges: this.props.trackOnchangeHistory !== false
                    };
                    
                    // 变更历史
                    this.onchangeHistory = [];
                    this.lastValue = '';
                    
                    // 增强的触发函数
                    const triggerOnChange = useDebounced(
                        this.enhancedTriggerOnChange.bind(this),
                        config.debounceDelay
                    );
                    
                    // 键盘事件处理
                    const handleKeydown = (event) => {
                        this.handleKeydownEvent(event, triggerOnChange, config);
                    };
                    
                    // 粘贴事件处理
                    const handlePaste = (event) => {
                        if (config.enablePaste) {
                            setTimeout(() => triggerOnChange(event), 0);
                        }
                    };
                    
                    // 剪切事件处理
                    const handleCut = (event) => {
                        if (config.enableCut) {
                            setTimeout(() => triggerOnChange(event), 0);
                        }
                    };
                    
                    useEffect(() => {
                        if (input.el) {
                            // 绑定事件监听器
                            input.el.addEventListener("keydown", handleKeydown);
                            
                            if (config.enablePaste) {
                                input.el.addEventListener("paste", handlePaste);
                            }
                            
                            if (config.enableCut) {
                                input.el.addEventListener("cut", handleCut);
                            }
                            
                            // 初始化值
                            this.lastValue = input.el.value || '';
                            
                            return () => {
                                // 清理事件监听器
                                input.el.removeEventListener("keydown", handleKeydown);
                                input.el.removeEventListener("paste", handlePaste);
                                input.el.removeEventListener("cut", handleCut);
                            };
                        }
                    });
                }
            },

            // 处理键盘事件
            handleKeydownEvent(event, triggerOnChange, config) {
                try {
                    // 检查排除的按键
                    if (config.excludeKeys.includes(event.key)) {
                        return;
                    }
                    
                    // 检查特定触发按键
                    if (config.triggerKeys && !config.triggerKeys.includes(event.key)) {
                        return;
                    }
                    
                    // 记录按键事件
                    this.recordKeydownEvent(event);
                    
                    // 触发变更
                    triggerOnChange(event);
                    
                } catch (error) {
                    console.error('处理键盘事件失败:', error);
                }
            },

            // 增强的触发变更
            enhancedTriggerOnChange(event) {
                try {
                    const input = this.input || this.textareaRef;
                    const currentValue = input.el.value || '';
                    
                    // 检查值是否真正改变
                    if (currentValue === this.lastValue) {
                        return;
                    }
                    
                    // 检查长度限制
                    const config = this.getOnchangeConfig();
                    if (currentValue.length < config.minLength || 
                        currentValue.length > config.maxLength) {
                        return;
                    }
                    
                    // 记录变更历史
                    if (config.trackChanges) {
                        this.recordValueChange(this.lastValue, currentValue, event);
                    }
                    
                    // 更新最后值
                    this.lastValue = currentValue;
                    
                    // 触发变更事件
                    input.el.dispatchEvent(new Event("change"));
                    
                    // 触发自定义事件
                    this.triggerCustomOnchangeEvent(currentValue, event);
                    
                } catch (error) {
                    console.error('触发变更失败:', error);
                }
            },

            // 获取变更配置
            getOnchangeConfig() {
                return {
                    debounceDelay: this.props.keydownDebounceDelay || 2000,
                    minLength: this.props.onchangeMinLength || 0,
                    maxLength: this.props.onchangeMaxLength || Infinity,
                    triggerKeys: this.props.onchangeTriggerKeys || null,
                    excludeKeys: this.props.onchangeExcludeKeys || ['Tab', 'Shift', 'Control', 'Alt'],
                    enablePaste: this.props.onchangeOnPaste !== false,
                    enableCut: this.props.onchangeOnCut !== false,
                    trackChanges: this.props.trackOnchangeHistory !== false
                };
            },

            // 记录按键事件
            recordKeydownEvent(event) {
                try {
                    if (!this.keydownEvents) {
                        this.keydownEvents = [];
                    }
                    
                    this.keydownEvents.push({
                        key: event.key,
                        code: event.code,
                        timestamp: Date.now(),
                        ctrlKey: event.ctrlKey,
                        shiftKey: event.shiftKey,
                        altKey: event.altKey
                    });
                    
                    // 保留最近100个事件
                    if (this.keydownEvents.length > 100) {
                        this.keydownEvents.splice(0, this.keydownEvents.length - 100);
                    }
                } catch (error) {
                    console.warn('记录按键事件失败:', error);
                }
            },

            // 记录值变更
            recordValueChange(oldValue, newValue, event) {
                try {
                    this.onchangeHistory.push({
                        oldValue: oldValue,
                        newValue: newValue,
                        timestamp: Date.now(),
                        eventType: event.type,
                        valueLength: newValue.length,
                        changeLength: newValue.length - oldValue.length
                    });
                    
                    // 保留最近50个变更
                    if (this.onchangeHistory.length > 50) {
                        this.onchangeHistory.splice(0, this.onchangeHistory.length - 50);
                    }
                } catch (error) {
                    console.warn('记录值变更失败:', error);
                }
            },

            // 触发自定义变更事件
            triggerCustomOnchangeEvent(value, originalEvent) {
                try {
                    if (this.props.onCustomOnchange) {
                        this.props.onCustomOnchange({
                            value: value,
                            originalEvent: originalEvent,
                            history: this.onchangeHistory,
                            field: this.props.name
                        });
                    }
                    
                    // 触发全局事件
                    if (this.env.bus) {
                        this.env.bus.trigger('field:onchange:keydown', {
                            fieldName: this.props.name,
                            value: value,
                            component: this
                        });
                    }
                } catch (error) {
                    console.warn('触发自定义变更事件失败:', error);
                }
            },

            // 获取变更统计
            getOnchangeStatistics() {
                try {
                    return {
                        totalChanges: this.onchangeHistory.length,
                        totalKeydowns: this.keydownEvents?.length || 0,
                        lastChange: this.onchangeHistory[this.onchangeHistory.length - 1] || null,
                        averageChangeLength: this.calculateAverageChangeLength(),
                        changeFrequency: this.calculateChangeFrequency(),
                        currentValue: this.lastValue,
                        fieldName: this.props.name
                    };
                } catch (error) {
                    return {
                        totalChanges: 0,
                        totalKeydowns: 0,
                        lastChange: null,
                        averageChangeLength: 0,
                        changeFrequency: 0,
                        currentValue: '',
                        fieldName: this.props.name || 'unknown'
                    };
                }
            },

            // 计算平均变更长度
            calculateAverageChangeLength() {
                if (this.onchangeHistory.length === 0) return 0;
                
                const totalLength = this.onchangeHistory.reduce(
                    (sum, change) => sum + Math.abs(change.changeLength), 0
                );
                
                return totalLength / this.onchangeHistory.length;
            },

            // 计算变更频率
            calculateChangeFrequency() {
                if (this.onchangeHistory.length < 2) return 0;
                
                const firstChange = this.onchangeHistory[0];
                const lastChange = this.onchangeHistory[this.onchangeHistory.length - 1];
                const timeSpan = lastChange.timestamp - firstChange.timestamp;
                
                if (timeSpan === 0) return 0;
                
                return (this.onchangeHistory.length - 1) / (timeSpan / 1000); // 每秒变更次数
            },

            // 重置变更历史
            resetOnchangeHistory() {
                this.onchangeHistory = [];
                this.keydownEvents = [];
                this.lastValue = '';
            }
        });

        // 增强的属性定义
        const enhancedProps = {
            onchangeOnKeydown: { type: Boolean, optional: true },
            keydownDebounceDelay: { type: Number, optional: true },
            onchangeMinLength: { type: Number, optional: true },
            onchangeMaxLength: { type: Number, optional: true },
            onchangeTriggerKeys: { type: Array, optional: true },
            onchangeExcludeKeys: { type: Array, optional: true },
            onchangeOnPaste: { type: Boolean, optional: true },
            onchangeOnCut: { type: Boolean, optional: true },
            trackOnchangeHistory: { type: Boolean, optional: true },
            onCustomOnchange: { type: Function, optional: true }
        };

        // 增强的属性提取
        const enhancedExtractProps = (originalExtractProps) => (fieldInfo) => {
            const baseProps = originalExtractProps(fieldInfo);
            
            return Object.assign(baseProps, {
                onchangeOnKeydown: exprToBoolean(fieldInfo.attrs.onchange_on_keydown),
                keydownDebounceDelay: fieldInfo.attrs.keydown_debounce_delay
                    ? Number(fieldInfo.attrs.keydown_debounce_delay)
                    : 2000,
                onchangeMinLength: fieldInfo.attrs.onchange_min_length
                    ? Number(fieldInfo.attrs.onchange_min_length)
                    : 0,
                onchangeMaxLength: fieldInfo.attrs.onchange_max_length
                    ? Number(fieldInfo.attrs.onchange_max_length)
                    : Infinity,
                onchangeTriggerKeys: fieldInfo.attrs.onchange_trigger_keys
                    ? fieldInfo.attrs.onchange_trigger_keys.split(',')
                    : null,
                onchangeExcludeKeys: fieldInfo.attrs.onchange_exclude_keys
                    ? fieldInfo.attrs.onchange_exclude_keys.split(',')
                    : ['Tab', 'Shift', 'Control', 'Alt'],
                onchangeOnPaste: fieldInfo.attrs.onchange_on_paste !== 'false',
                onchangeOnCut: fieldInfo.attrs.onchange_on_cut !== 'false',
                trackOnchangeHistory: fieldInfo.attrs.track_onchange_history !== 'false'
            });
        };

        // 应用增强的补丁
        patch(CharField.prototype, enhancedOnchangeOnKeydownMixin(), { force: true });
        patch(TextField.prototype, enhancedOnchangeOnKeydownMixin(), { force: true });

        // 扩展属性定义
        Object.assign(CharField.props, enhancedProps);
        Object.assign(TextField.props, enhancedProps);

        // 增强属性提取
        const originalCharExtractProps = charField.extractProps;
        charField.extractProps = enhancedExtractProps(originalCharExtractProps);

        const originalTextExtractProps = textField.extractProps;
        textField.extractProps = enhancedExtractProps(originalTextExtractProps);
    }
};

// 应用按键变更处理增强
OnChangeOnKeydownEnhancer.enhanceOnChangeOnKeydown();
```

## 技术特点

### 1. 混入模式
- 可复用的功能模块
- 非侵入式的功能扩展
- 灵活的组件增强

### 2. 防抖机制
- 性能优化的关键技术
- 可配置的延迟时间
- 避免频繁的事件触发

### 3. 事件管理
- 完整的事件生命周期管理
- 自动的事件清理机制
- 标准的DOM事件处理

### 4. 补丁系统
- 运行时的功能扩展
- 保持原有功能完整性
- 模块化的功能组织

## 设计模式

### 1. 混入模式 (Mixin Pattern)
- 功能的横向扩展
- 代码的复用和组合

### 2. 装饰器模式 (Decorator Pattern)
- 动态的功能增强
- 不修改原始类的扩展

### 3. 观察者模式 (Observer Pattern)
- 事件驱动的交互
- 松耦合的组件通信

## 注意事项

1. **性能影响**: 防抖机制对性能的影响和优化
2. **事件清理**: 确保事件监听器的正确清理
3. **兼容性**: 与现有字段组件的兼容性
4. **用户体验**: 防抖延迟对用户体验的影响

## 扩展建议

1. **智能防抖**: 基于用户输入模式的智能防抖调整
2. **事件过滤**: 更精细的事件过滤和处理机制
3. **性能监控**: 添加性能监控和优化工具
4. **自定义触发**: 支持更多自定义的触发条件
5. **历史记录**: 实现输入历史的记录和回放功能

该模块为邮件系统的文本字段提供了重要的实时交互功能，提升了用户体验和系统响应性。

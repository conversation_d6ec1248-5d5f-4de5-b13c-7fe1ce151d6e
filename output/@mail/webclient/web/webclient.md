# WebClient - Web客户端推送通知

## 概述

`webclient.js` 是 Odoo 邮件系统的Web客户端推送通知补丁，专门用于为Web客户端添加推送通知功能。该组件基于Web框架的WebClient和浏览器推送API，集成了设备注册、推送订阅、权限管理等核心功能，为邮件系统提供了完整的Web推送通知支持，是邮件系统实时通知和用户体验的重要组件。

## 文件信息
- **路径**: `/mail/static/src/webclient/web/webclient.js`
- **行数**: 171
- **模块**: `@mail/webclient/web/webclient`

## 依赖关系

```javascript
// 核心依赖
'@web/core/browser/browser'     // 浏览器API封装
'@web/core/utils/hooks'         // 工具钩子
'@web/core/utils/patch'         // 补丁工具
'@web/webclient/webclient'      // Web客户端
'@odoo/owl'                     // OWL框架
```

## 核心功能

### 1. WebClient 补丁

```javascript
patch(WebClient.prototype, {
    setup() {
        super.setup();
        this.orm = useService("orm");
        if (this._canSendNativeNotification) {
            this._subscribePush();
        }
        if (browser.navigator.permissions) {
            let notificationPerm;
            const onPermissionChange = () => {
                if (this._canSendNativeNotification) {
                    this._subscribePush();
                } else {
                    this._unsubscribePush();
                }
            };
            browser.navigator.permissions.query({ name: "notifications" }).then((perm) => {
                notificationPerm = perm;
                notificationPerm.addEventListener("change", onPermissionChange);
            });
            onWillDestroy(() => {
                notificationPerm?.removeEventListener("change", onPermissionChange);
            });
        }
    }
});
```

**补丁功能**:
- **ORM服务**: 集成ORM服务进行数据操作
- **权限检查**: 检查通知权限并自动订阅
- **权限监听**: 监听权限变化并动态调整订阅状态
- **生命周期**: 在组件销毁时清理事件监听器

### 2. 通知权限检查

```javascript
get _canSendNativeNotification() {
    return browser.Notification?.permission === "granted";
}
```

**权限检查功能**:
- **权限验证**: 检查浏览器通知权限状态
- **安全检查**: 确保Notification API可用
- **状态判断**: 返回是否可以发送原生通知
- **兼容性**: 处理不支持通知的浏览器

### 3. 推送订阅管理

```javascript
async _subscribePush(numberTry = 1) {
    const pushManager = await this.pushManager();
    if (!pushManager) {
        return;
    }
    let subscription = await pushManager.getSubscription();
    const previousEndpoint = browser.localStorage.getItem(`${USER_DEVICES_MODEL}_endpoint`);
    
    if (!subscription) {
        subscription = await pushManager.subscribe({
            userVisibleOnly: true,
            applicationServerKey: await this._getApplicationServerKey(),
        });
        browser.localStorage.setItem(`${USER_DEVICES_MODEL}_endpoint`, subscription.endpoint);
    }
    
    const kwargs = subscription.toJSON();
    if (previousEndpoint && subscription.endpoint !== previousEndpoint) {
        kwargs.previous_endpoint = previousEndpoint;
    }
    
    try {
        kwargs.vapid_public_key = this._arrayBufferToBase64(
            subscription.options.applicationServerKey
        );
        await this.orm.call(USER_DEVICES_MODEL, "register_devices", [], kwargs);
    } catch (e) {
        // 错误处理逻辑
    }
}
```

**订阅功能**:
- **推送管理器**: 获取浏览器的推送管理器
- **订阅检查**: 检查现有订阅状态
- **自动订阅**: 自动创建新的推送订阅
- **端点管理**: 管理订阅端点的变化
- **设备注册**: 向服务器注册设备信息
- **错误处理**: 处理订阅过程中的各种错误

### 4. 推送取消订阅

```javascript
async _unsubscribePush() {
    const pushManager = await this.pushManager();
    if (!pushManager) {
        return;
    }
    const subscription = await pushManager.getSubscription();
    if (!subscription) {
        return;
    }
    await this.orm.call(USER_DEVICES_MODEL, "unregister_devices", [], {
        endpoint: subscription.endpoint,
    });
    await subscription.unsubscribe();
    browser.localStorage.removeItem(`${USER_DEVICES_MODEL}_endpoint`);
}
```

**取消订阅功能**:
- **订阅检查**: 检查现有订阅状态
- **设备注销**: 从服务器注销设备
- **订阅取消**: 取消浏览器推送订阅
- **数据清理**: 清理本地存储的端点信息

### 5. 推送管理器获取

```javascript
async pushManager() {
    const registration = await browser.navigator.serviceWorker?.getRegistration();
    return registration?.pushManager;
}
```

**管理器功能**:
- **Service Worker**: 获取Service Worker注册
- **推送管理器**: 返回推送管理器实例
- **兼容性检查**: 处理不支持Service Worker的浏览器
- **异步操作**: 异步获取注册信息

### 6. 应用服务器密钥处理

```javascript
async _getApplicationServerKey() {
    const vapid_public_key_base64 = await this.orm.call(
        USER_DEVICES_MODEL,
        "get_web_push_vapid_public_key"
    );
    const padding = "=".repeat((4 - (vapid_public_key_base64.length % 4)) % 4);
    const base64 = (vapid_public_key_base64 + padding).replace(/-/g, "+").replace(/_/g, "/");
    const rawData = atob(base64);
    const outputArray = new Uint8Array(rawData.length);
    for (let i = 0; i < rawData.length; ++i) {
        outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
}
```

**密钥处理功能**:
- **密钥获取**: 从服务器获取VAPID公钥
- **格式转换**: Base64到Uint8Array的转换
- **填充处理**: 处理Base64编码的填充
- **字符替换**: 处理URL安全的Base64编码
- **二进制转换**: 转换为推送API所需的格式

### 7. 数组缓冲区转换

```javascript
_arrayBufferToBase64(buffer) {
    const bytes = new Uint8Array(buffer);
    let binary = "";
    for (let i = 0; i < bytes.byteLength; i++) {
        binary += String.fromCharCode(bytes[i]);
    }
    return window.btoa(binary).replaceAll("+", "-").replaceAll("/", "_").replaceAll("=", "");
}
```

**转换功能**:
- **缓冲区处理**: 处理ArrayBuffer数据
- **二进制转换**: 转换为二进制字符串
- **Base64编码**: 编码为Base64格式
- **URL安全**: 转换为URL安全的Base64编码

## 使用场景

### 1. Web客户端推送通知增强

```javascript
// Web客户端推送通知增强功能
const WebClientPushEnhancer = {
    enhanceWebClientPush: () => {
        // 增强的Web客户端补丁
        const enhancedWebClientPatch = {
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.pushConfig = {
                    enablePushNotifications: true,
                    enableBadgeUpdates: true,
                    enableSoundNotifications: true,
                    enableVibration: true,
                    retryAttempts: 3,
                    retryDelay: 5000,
                    subscriptionTimeout: 30000,
                    enableOfflineSupport: true,
                    enableNotificationHistory: true,
                    maxHistorySize: 100,
                    enableAnalytics: true,
                    enableDebugMode: false
                };
                
                // 通知类型配置
                this.notificationTypes = {
                    'email': {
                        icon: '/mail/static/src/img/email-icon.png',
                        badge: '/mail/static/src/img/email-badge.png',
                        sound: '/mail/static/src/sounds/email.mp3',
                        vibrate: [200, 100, 200],
                        priority: 'normal'
                    },
                    'chat': {
                        icon: '/mail/static/src/img/chat-icon.png',
                        badge: '/mail/static/src/img/chat-badge.png',
                        sound: '/mail/static/src/sounds/chat.mp3',
                        vibrate: [100, 50, 100],
                        priority: 'high'
                    },
                    'activity': {
                        icon: '/mail/static/src/img/activity-icon.png',
                        badge: '/mail/static/src/img/activity-badge.png',
                        sound: '/mail/static/src/sounds/activity.mp3',
                        vibrate: [300, 200, 300],
                        priority: 'normal'
                    }
                };
                
                // 通知历史
                this.notificationHistory = [];
                
                // 初始化增强功能
                this.initializePushEnhancements();
            },
            
            // 初始化推送增强功能
            initializePushEnhancements() {
                // 设置通知历史
                if (this.pushConfig.enableNotificationHistory) {
                    this.loadNotificationHistory();
                }
                
                // 设置离线支持
                if (this.pushConfig.enableOfflineSupport) {
                    this.setupOfflineSupport();
                }
                
                // 设置分析
                if (this.pushConfig.enableAnalytics) {
                    this.setupPushAnalytics();
                }
                
                // 设置调试模式
                if (this.pushConfig.enableDebugMode) {
                    this.setupDebugMode();
                }
            },
            
            // 增强的推送订阅
            async _subscribePush(numberTry = 1) {
                try {
                    const startTime = Date.now();
                    
                    // 记录订阅尝试
                    this.logPushEvent('subscription_attempt', {
                        attempt: numberTry,
                        timestamp: startTime
                    });
                    
                    // 执行原有订阅逻辑
                    await super._subscribePush(numberTry);
                    
                    // 记录成功订阅
                    this.logPushEvent('subscription_success', {
                        attempt: numberTry,
                        duration: Date.now() - startTime
                    });
                    
                    // 设置徽章更新
                    if (this.pushConfig.enableBadgeUpdates) {
                        this.setupBadgeUpdates();
                    }
                    
                } catch (error) {
                    // 记录订阅失败
                    this.logPushEvent('subscription_error', {
                        attempt: numberTry,
                        error: error.message
                    });
                    
                    // 重试逻辑
                    if (numberTry < this.pushConfig.retryAttempts) {
                        setTimeout(() => {
                            this._subscribePush(numberTry + 1);
                        }, this.pushConfig.retryDelay);
                    } else {
                        this.handleSubscriptionFailure(error);
                    }
                }
            },
            
            // 增强的通知权限检查
            get _canSendNativeNotification() {
                const hasPermission = super._canSendNativeNotification;
                
                // 记录权限状态
                this.logPushEvent('permission_check', {
                    hasPermission: hasPermission,
                    permission: browser.Notification?.permission
                });
                
                return hasPermission && this.pushConfig.enablePushNotifications;
            },
            
            // 处理推送消息
            async handlePushMessage(event) {
                try {
                    const data = event.data ? event.data.json() : {};
                    
                    // 记录推送消息
                    this.logPushEvent('push_received', {
                        type: data.type,
                        timestamp: Date.now()
                    });
                    
                    // 添加到历史记录
                    if (this.pushConfig.enableNotificationHistory) {
                        this.addToNotificationHistory(data);
                    }
                    
                    // 显示通知
                    await this.showEnhancedNotification(data);
                    
                } catch (error) {
                    console.error('处理推送消息失败:', error);
                    this.logPushEvent('push_error', {
                        error: error.message
                    });
                }
            },
            
            // 显示增强通知
            async showEnhancedNotification(data) {
                const notificationType = this.notificationTypes[data.type] || this.notificationTypes['email'];
                
                const options = {
                    body: data.body,
                    icon: notificationType.icon,
                    badge: notificationType.badge,
                    tag: data.tag || 'mail-notification',
                    requireInteraction: notificationType.priority === 'high',
                    silent: !this.pushConfig.enableSoundNotifications,
                    vibrate: this.pushConfig.enableVibration ? notificationType.vibrate : undefined,
                    data: data,
                    actions: this.getNotificationActions(data.type)
                };
                
                // 显示通知
                const registration = await browser.navigator.serviceWorker.getRegistration();
                if (registration) {
                    await registration.showNotification(data.title, options);
                }
                
                // 播放声音
                if (this.pushConfig.enableSoundNotifications && notificationType.sound) {
                    this.playNotificationSound(notificationType.sound);
                }
            },
            
            // 获取通知操作
            getNotificationActions(type) {
                const commonActions = [
                    {
                        action: 'view',
                        title: '查看',
                        icon: '/mail/static/src/img/view-icon.png'
                    },
                    {
                        action: 'dismiss',
                        title: '忽略',
                        icon: '/mail/static/src/img/dismiss-icon.png'
                    }
                ];
                
                const typeSpecificActions = {
                    'email': [
                        {
                            action: 'reply',
                            title: '回复',
                            icon: '/mail/static/src/img/reply-icon.png'
                        }
                    ],
                    'chat': [
                        {
                            action: 'reply',
                            title: '回复',
                            icon: '/mail/static/src/img/reply-icon.png'
                        }
                    ],
                    'activity': [
                        {
                            action: 'complete',
                            title: '完成',
                            icon: '/mail/static/src/img/complete-icon.png'
                        }
                    ]
                };
                
                return [...commonActions, ...(typeSpecificActions[type] || [])];
            },
            
            // 播放通知声音
            playNotificationSound(soundUrl) {
                try {
                    const audio = new Audio(soundUrl);
                    audio.volume = 0.5;
                    audio.play().catch(error => {
                        console.warn('播放通知声音失败:', error);
                    });
                } catch (error) {
                    console.warn('创建音频对象失败:', error);
                }
            },
            
            // 设置徽章更新
            setupBadgeUpdates() {
                if ('setAppBadge' in navigator) {
                    // 监听未读消息数量变化
                    this.env.bus.addEventListener('unread-counter-changed', (event) => {
                        const count = event.detail.count;
                        if (count > 0) {
                            navigator.setAppBadge(count);
                        } else {
                            navigator.clearAppBadge();
                        }
                    });
                }
            },
            
            // 添加到通知历史
            addToNotificationHistory(data) {
                this.notificationHistory.unshift({
                    ...data,
                    timestamp: Date.now(),
                    id: this.generateNotificationId()
                });
                
                // 限制历史记录大小
                if (this.notificationHistory.length > this.pushConfig.maxHistorySize) {
                    this.notificationHistory = this.notificationHistory.slice(0, this.pushConfig.maxHistorySize);
                }
                
                // 保存到本地存储
                this.saveNotificationHistory();
            },
            
            // 生成通知ID
            generateNotificationId() {
                return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            },
            
            // 加载通知历史
            loadNotificationHistory() {
                try {
                    const stored = localStorage.getItem('mail_notification_history');
                    if (stored) {
                        this.notificationHistory = JSON.parse(stored);
                    }
                } catch (error) {
                    console.error('加载通知历史失败:', error);
                    this.notificationHistory = [];
                }
            },
            
            // 保存通知历史
            saveNotificationHistory() {
                try {
                    localStorage.setItem('mail_notification_history', JSON.stringify(this.notificationHistory));
                } catch (error) {
                    console.error('保存通知历史失败:', error);
                }
            },
            
            // 设置离线支持
            setupOfflineSupport() {
                // 监听在线/离线状态
                window.addEventListener('online', () => {
                    this.handleOnlineStatusChange(true);
                });
                
                window.addEventListener('offline', () => {
                    this.handleOnlineStatusChange(false);
                });
            },
            
            // 处理在线状态变化
            handleOnlineStatusChange(isOnline) {
                this.logPushEvent('online_status_change', {
                    isOnline: isOnline,
                    timestamp: Date.now()
                });
                
                if (isOnline && this._canSendNativeNotification) {
                    // 重新订阅推送
                    this._subscribePush();
                }
            },
            
            // 设置推送分析
            setupPushAnalytics() {
                this.pushAnalytics = {
                    subscriptionAttempts: 0,
                    subscriptionSuccesses: 0,
                    subscriptionFailures: 0,
                    pushMessagesReceived: 0,
                    notificationsShown: 0,
                    notificationClicks: 0
                };
                
                // 定期发送分析数据
                setInterval(() => {
                    this.sendPushAnalytics();
                }, 300000); // 每5分钟
            },
            
            // 发送推送分析
            async sendPushAnalytics() {
                try {
                    await this.orm.call('mail.push.analytics', 'record_metrics', [], {
                        metrics: this.pushAnalytics,
                        timestamp: Date.now()
                    });
                    
                    // 重置计数器
                    Object.keys(this.pushAnalytics).forEach(key => {
                        this.pushAnalytics[key] = 0;
                    });
                } catch (error) {
                    console.error('发送推送分析失败:', error);
                }
            },
            
            // 记录推送事件
            logPushEvent(eventType, data) {
                if (this.pushConfig.enableAnalytics) {
                    // 更新分析计数器
                    switch (eventType) {
                        case 'subscription_attempt':
                            this.pushAnalytics.subscriptionAttempts++;
                            break;
                        case 'subscription_success':
                            this.pushAnalytics.subscriptionSuccesses++;
                            break;
                        case 'subscription_error':
                            this.pushAnalytics.subscriptionFailures++;
                            break;
                        case 'push_received':
                            this.pushAnalytics.pushMessagesReceived++;
                            break;
                        case 'notification_shown':
                            this.pushAnalytics.notificationsShown++;
                            break;
                        case 'notification_click':
                            this.pushAnalytics.notificationClicks++;
                            break;
                    }
                }
                
                if (this.pushConfig.enableDebugMode) {
                    console.log(`[Push Debug] ${eventType}:`, data);
                }
            },
            
            // 设置调试模式
            setupDebugMode() {
                // 添加调试面板
                this.createDebugPanel();
                
                // 监听所有推送相关事件
                this.setupDebugEventListeners();
            },
            
            // 创建调试面板
            createDebugPanel() {
                // 实现调试面板UI
                console.log('推送通知调试模式已启用');
            },
            
            // 设置调试事件监听器
            setupDebugEventListeners() {
                // 监听Service Worker消息
                if ('serviceWorker' in navigator) {
                    navigator.serviceWorker.addEventListener('message', (event) => {
                        this.logPushEvent('service_worker_message', event.data);
                    });
                }
            },
            
            // 处理订阅失败
            handleSubscriptionFailure(error) {
                console.error('推送订阅失败:', error);
                
                // 显示用户友好的错误消息
                if (this.env.services.notification) {
                    this.env.services.notification.add(
                        '推送通知订阅失败，某些功能可能无法正常工作',
                        { type: 'warning' }
                    );
                }
            },
            
            // 获取推送统计信息
            getPushStatistics() {
                return {
                    isSubscribed: this._canSendNativeNotification,
                    notificationHistory: this.notificationHistory.length,
                    analytics: this.pushAnalytics,
                    config: this.pushConfig
                };
            }
        };
        
        // 应用增强补丁
        patch(WebClient.prototype, enhancedWebClientPatch);
    }
};

// 应用Web客户端推送通知增强
WebClientPushEnhancer.enhanceWebClientPush();
```

## 技术特点

### 1. 推送通知集成
- 完整的Web推送通知支持
- 自动的设备注册和管理
- 智能的权限检查和处理

### 2. Service Worker集成
- 与Service Worker的深度集成
- 推送管理器的获取和使用
- 离线推送支持

### 3. 安全性
- VAPID密钥的安全处理
- 端点信息的加密传输
- 权限的严格验证

### 4. 错误处理
- 完善的错误处理机制
- 自动重试逻辑
- 优雅的降级处理

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 非侵入式的功能扩展
- 保持原有代码的稳定性

### 2. 观察者模式 (Observer Pattern)
- 监听权限变化事件
- 响应状态变化

### 3. 策略模式 (Strategy Pattern)
- 不同的错误处理策略
- 可配置的重试机制

## 注意事项

1. **浏览器兼容性**: 确保推送API的浏览器支持
2. **权限管理**: 正确处理通知权限的获取和变化
3. **安全性**: 保护VAPID密钥和端点信息
4. **性能影响**: 避免频繁的订阅操作影响性能

## 扩展建议

1. **通知定制**: 支持更丰富的通知样式和交互
2. **离线支持**: 增强离线场景下的推送处理
3. **分析统计**: 添加推送通知的使用统计
4. **批量通知**: 支持批量通知的合并和管理
5. **用户偏好**: 支持用户自定义通知偏好设置

该Web客户端推送通知补丁为邮件系统提供了重要的实时通知功能，是用户体验和系统可用性的核心组件。

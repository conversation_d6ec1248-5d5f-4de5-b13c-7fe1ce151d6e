# Open Chat Hook - 打开聊天钩子

## 概述

`open_chat_hook.js` 实现了 Odoo 邮件系统中的打开聊天钩子，用于在Web界面中快速打开与特定用户的聊天窗口。该钩子提供了一个便捷的API，支持从多对一和多对多字段中直接打开聊天，目前专门支持res.users模型，通过邮件存储服务管理聊天窗口的打开，为用户提供了无缝的聊天体验，是邮件系统中用户交互的重要工具。

## 文件信息
- **路径**: `/mail/static/src/core/web/open_chat_hook.js`
- **行数**: 34
- **模块**: `@mail/core/web/open_chat_hook`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/hooks'  // Web 核心钩子
```

## 核心组件

### 1. 辅助函数对象

```javascript
const helpers = {
    SUPPORTED_M2X_AVATAR_MODELS: ["res.users"],
    buildOpenChatParams: (resModel, id) => {
        if (resModel === "res.users") {
            return { userId: id };
        }
    },
};
```

**辅助功能**:
- **支持的模型**: 定义支持头像的多对多模型列表
- **参数构建**: 根据模型类型构建打开聊天的参数

### 2. useOpenChat 钩子

```javascript
function useOpenChat(resModel) {
    const store = useService("mail.store");
    if (!helpers.SUPPORTED_M2X_AVATAR_MODELS.includes(resModel)) {
        throw new Error(
            `This widget is only supported on many2one and many2many fields pointing to ${JSON.stringify(
                helpers.SUPPORTED_M2X_AVATAR_MODELS
            )}`
        );
    }
    return async (id) => {
        store.openChat(helpers.buildOpenChatParams(resModel, id));
    };
}
```

**钩子功能**:
- 验证模型支持性
- 返回异步函数用于打开聊天
- 通过邮件存储服务打开聊天窗口

## 核心功能

### 1. 模型验证

```javascript
if (!helpers.SUPPORTED_M2X_AVATAR_MODELS.includes(resModel)) {
    throw new Error(
        `This widget is only supported on many2one and many2many fields pointing to ${JSON.stringify(
            helpers.SUPPORTED_M2X_AVATAR_MODELS
        )}`
    );
}
```

**验证逻辑**:
- 检查传入的模型是否在支持列表中
- 抛出详细的错误信息说明支持的模型类型

### 2. 参数构建

```javascript
buildOpenChatParams: (resModel, id) => {
    if (resModel === "res.users") {
        return { userId: id };
    }
}
```

**参数构建逻辑**:
- 根据不同的模型类型构建相应的参数
- 目前支持res.users模型，返回userId参数

### 3. 聊天打开

```javascript
return async (id) => {
    store.openChat(helpers.buildOpenChatParams(resModel, id));
};
```

**打开逻辑**:
- 返回异步函数
- 构建聊天参数并调用存储服务的openChat方法

## 使用场景

### 1. 用户头像聊天集成

```javascript
// 用户头像组件中的聊天集成
const UserAvatarWithChat = ({ userId, userName, showChatButton = true }) => {
    const openChat = useOpenChat('res.users');
    const [isHovered, setIsHovered] = useState(false);
    
    const handleChatClick = async (event) => {
        event.stopPropagation();
        
        try {
            await openChat(userId);
            
            // 记录聊天打开操作
            logUserAction('chat_opened_from_avatar', {
                targetUserId: userId,
                timestamp: Date.now()
            });
        } catch (error) {
            console.error('打开聊天失败:', error);
            showNotification('无法打开聊天窗口', 'error');
        }
    };
    
    return (
        <div 
            class="user-avatar-container"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >
            <img 
                src={`/web/image/res.users/${userId}/avatar_128`}
                alt={userName}
                class="user-avatar"
            />
            
            {showChatButton && isHovered && (
                <button 
                    class="chat-overlay-btn"
                    onClick={handleChatClick}
                    title={`与 ${userName} 聊天`}
                >
                    <i class="fa fa-comment"></i>
                </button>
            )}
            
            <span class="user-name">{userName}</span>
        </div>
    );
};
```

### 2. 用户列表聊天功能

```javascript
// 用户列表中的聊天功能
const UserListWithChat = ({ users, allowChat = true }) => {
    const openChat = useOpenChat('res.users');
    const [chatLoading, setChatLoading] = useState(new Set());
    
    const handleUserChat = async (user) => {
        if (!allowChat) return;
        
        setChatLoading(prev => new Set(prev).add(user.id));
        
        try {
            await openChat(user.id);
            
            // 更新最近聊天列表
            updateRecentChats(user);
            
            // 记录聊天交互
            recordChatInteraction(user.id);
            
        } catch (error) {
            console.error('打开聊天失败:', error);
            showErrorMessage(`无法与 ${user.name} 开始聊天`);
        } finally {
            setChatLoading(prev => {
                const newSet = new Set(prev);
                newSet.delete(user.id);
                return newSet;
            });
        }
    };
    
    const updateRecentChats = (user) => {
        const recentChats = JSON.parse(localStorage.getItem('recent_chats') || '[]');
        
        // 移除已存在的记录
        const filtered = recentChats.filter(chat => chat.userId !== user.id);
        
        // 添加到开头
        filtered.unshift({
            userId: user.id,
            userName: user.name,
            timestamp: Date.now()
        });
        
        // 限制数量
        const limited = filtered.slice(0, 10);
        
        localStorage.setItem('recent_chats', JSON.stringify(limited));
    };
    
    return (
        <div class="user-list">
            {users.map(user => (
                <div key={user.id} class="user-item">
                    <UserAvatarWithChat 
                        userId={user.id}
                        userName={user.name}
                        showChatButton={allowChat}
                    />
                    
                    <div class="user-info">
                        <span class="user-name">{user.name}</span>
                        <span class="user-email">{user.email}</span>
                        <span class="user-status">{user.isOnline ? '在线' : '离线'}</span>
                    </div>
                    
                    {allowChat && (
                        <div class="user-actions">
                            <button 
                                class="btn btn-sm btn-primary chat-btn"
                                onClick={() => handleUserChat(user)}
                                disabled={chatLoading.has(user.id)}
                            >
                                {chatLoading.has(user.id) ? (
                                    <i class="fa fa-spinner fa-spin"></i>
                                ) : (
                                    <i class="fa fa-comment"></i>
                                )}
                                聊天
                            </button>
                        </div>
                    )}
                </div>
            ))}
        </div>
    );
};
```

### 3. 表单字段聊天集成

```javascript
// 表单字段中的聊天集成
const Many2OneUserFieldWithChat = ({ fieldName, value, onChange, readonly }) => {
    const openChat = useOpenChat('res.users');
    const [showChatOption, setShowChatOption] = useState(false);
    
    const handleFieldChange = (newValue) => {
        onChange(newValue);
        setShowChatOption(!!newValue);
    };
    
    const handleQuickChat = async () => {
        if (!value) return;
        
        try {
            await openChat(value.id);
            
            // 记录从字段打开聊天
            logFieldChatAction(fieldName, value.id);
            
        } catch (error) {
            console.error('从字段打开聊天失败:', error);
        }
    };
    
    return (
        <div class="many2one-field-with-chat">
            <Many2OneField
                name={fieldName}
                value={value}
                onChange={handleFieldChange}
                readonly={readonly}
                relation="res.users"
            />
            
            {showChatOption && !readonly && (
                <button 
                    class="field-chat-btn"
                    onClick={handleQuickChat}
                    title={`与 ${value.display_name} 聊天`}
                >
                    <i class="fa fa-comment-o"></i>
                </button>
            )}
        </div>
    );
};
```

### 4. 聊天历史管理

```javascript
// 聊天历史管理
const ChatHistoryManager = {
    addChatHistory: (userId, userName) => {
        const history = ChatHistoryManager.getChatHistory();
        
        // 移除已存在的记录
        const filtered = history.filter(item => item.userId !== userId);
        
        // 添加新记录到开头
        filtered.unshift({
            userId,
            userName,
            timestamp: Date.now(),
            lastMessage: null
        });
        
        // 限制历史记录数量
        const limited = filtered.slice(0, 50);
        
        localStorage.setItem('chat_history', JSON.stringify(limited));
    },
    
    getChatHistory: () => {
        try {
            return JSON.parse(localStorage.getItem('chat_history') || '[]');
        } catch (error) {
            console.error('获取聊天历史失败:', error);
            return [];
        }
    },
    
    getRecentChats: (limit = 10) => {
        return ChatHistoryManager.getChatHistory().slice(0, limit);
    },
    
    getFrequentChats: (limit = 10) => {
        const history = ChatHistoryManager.getChatHistory();
        const frequency = {};
        
        history.forEach(item => {
            frequency[item.userId] = (frequency[item.userId] || 0) + 1;
        });
        
        return Object.entries(frequency)
            .sort(([,a], [,b]) => b - a)
            .slice(0, limit)
            .map(([userId, count]) => {
                const historyItem = history.find(item => item.userId === parseInt(userId));
                return { ...historyItem, frequency: count };
            });
    },
    
    clearChatHistory: () => {
        localStorage.removeItem('chat_history');
    },
    
    removeChatFromHistory: (userId) => {
        const history = ChatHistoryManager.getChatHistory();
        const filtered = history.filter(item => item.userId !== userId);
        localStorage.setItem('chat_history', JSON.stringify(filtered));
    }
};
```

### 5. 聊天状态管理

```javascript
// 聊天状态管理
const ChatStateManager = {
    state: {
        openChats: new Map(),
        chatPreferences: {},
        notifications: true
    },
    
    addOpenChat: (userId, chatWindow) => {
        ChatStateManager.state.openChats.set(userId, {
            window: chatWindow,
            openTime: Date.now(),
            lastActivity: Date.now()
        });
        
        ChatStateManager.notifyStateChange();
    },
    
    removeOpenChat: (userId) => {
        const chatInfo = ChatStateManager.state.openChats.get(userId);
        if (chatInfo) {
            const duration = Date.now() - chatInfo.openTime;
            
            // 记录聊天持续时间
            logChatDuration(userId, duration);
            
            ChatStateManager.state.openChats.delete(userId);
            ChatStateManager.notifyStateChange();
        }
    },
    
    isUserChatOpen: (userId) => {
        return ChatStateManager.state.openChats.has(userId);
    },
    
    getOpenChats: () => {
        return Array.from(ChatStateManager.state.openChats.entries());
    },
    
    updateChatActivity: (userId) => {
        const chatInfo = ChatStateManager.state.openChats.get(userId);
        if (chatInfo) {
            chatInfo.lastActivity = Date.now();
        }
    },
    
    notifyStateChange: () => {
        const event = new CustomEvent('chat_state_changed', {
            detail: {
                openChats: ChatStateManager.getOpenChats().length,
                state: ChatStateManager.state
            }
        });
        
        document.dispatchEvent(event);
    },
    
    setChatPreference: (key, value) => {
        ChatStateManager.state.chatPreferences[key] = value;
        
        // 保存到本地存储
        localStorage.setItem(
            'chat_preferences',
            JSON.stringify(ChatStateManager.state.chatPreferences)
        );
    },
    
    getChatPreference: (key, defaultValue = null) => {
        return ChatStateManager.state.chatPreferences[key] || defaultValue;
    }
};
```

## 错误处理

### 1. 模型验证错误

```javascript
// 模型验证错误处理
const ModelValidationHandler = {
    handleUnsupportedModel: (resModel) => {
        const supportedModels = helpers.SUPPORTED_M2X_AVATAR_MODELS.join(', ');
        const errorMessage = `模型 "${resModel}" 不支持聊天功能。支持的模型: ${supportedModels}`;
        
        console.error(errorMessage);
        
        // 显示用户友好的错误消息
        showNotification('此类型的记录不支持聊天功能', 'warning');
        
        // 记录错误
        logError({
            type: 'unsupported_chat_model',
            model: resModel,
            supportedModels: helpers.SUPPORTED_M2X_AVATAR_MODELS
        });
    },
    
    validateChatAccess: (userId) => {
        // 检查用户是否有聊天权限
        const currentUser = getCurrentUser();
        
        if (!currentUser) {
            throw new Error('用户未登录');
        }
        
        if (userId === currentUser.id) {
            throw new Error('不能与自己聊天');
        }
        
        // 检查用户是否存在
        return ModelValidationHandler.checkUserExists(userId);
    },
    
    checkUserExists: async (userId) => {
        try {
            const response = await fetch(`/web/dataset/call_kw/res.users/read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    model: 'res.users',
                    method: 'read',
                    args: [[userId], ['id', 'name', 'active']],
                    kwargs: {}
                })
            });
            
            const result = await response.json();
            
            if (!result.result || result.result.length === 0) {
                throw new Error('用户不存在');
            }
            
            const user = result.result[0];
            if (!user.active) {
                throw new Error('用户已停用');
            }
            
            return user;
        } catch (error) {
            console.error('检查用户存在性失败:', error);
            throw new Error('无法验证用户信息');
        }
    }
};
```

### 2. 聊天打开错误处理

```javascript
// 聊天打开错误处理
const ChatOpenErrorHandler = {
    handleChatOpenError: (error, userId) => {
        console.error('打开聊天失败:', error);
        
        let userMessage = '无法打开聊天窗口';
        
        if (error.message.includes('用户不存在')) {
            userMessage = '目标用户不存在';
        } else if (error.message.includes('权限')) {
            userMessage = '没有聊天权限';
        } else if (error.message.includes('网络')) {
            userMessage = '网络连接失败，请重试';
        }
        
        showNotification(userMessage, 'error');
        
        // 记录错误详情
        logError({
            type: 'chat_open_failed',
            userId,
            error: error.message,
            timestamp: Date.now()
        });
    },
    
    retryOpenChat: async (userId, maxRetries = 3) => {
        const openChat = useOpenChat('res.users');
        
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                await openChat(userId);
                return true;
            } catch (error) {
                console.warn(`聊天打开尝试 ${attempt} 失败:`, error);
                
                if (attempt === maxRetries) {
                    ChatOpenErrorHandler.handleChatOpenError(error, userId);
                    return false;
                }
                
                // 等待后重试
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        }
        
        return false;
    }
};
```

## 性能优化

### 1. 聊天窗口缓存

```javascript
// 聊天窗口缓存
const ChatWindowCache = {
    cache: new Map(),
    
    getCachedWindow: (userId) => {
        return ChatWindowCache.cache.get(userId);
    },
    
    setCachedWindow: (userId, window) => {
        ChatWindowCache.cache.set(userId, window);
        
        // 限制缓存大小
        if (ChatWindowCache.cache.size > 20) {
            const firstKey = ChatWindowCache.cache.keys().next().value;
            ChatWindowCache.cache.delete(firstKey);
        }
    },
    
    removeCachedWindow: (userId) => {
        ChatWindowCache.cache.delete(userId);
    }
};
```

## 设计模式

### 1. 钩子模式 (Hook Pattern)
- 提供可复用的聊天打开逻辑
- 封装复杂的状态管理

### 2. 工厂模式 (Factory Pattern)
- 根据模型类型构建不同的参数
- 支持扩展新的模型类型

### 3. 策略模式 (Strategy Pattern)
- 不同模型的不同处理策略
- 可配置的聊天打开行为

## 注意事项

1. **模型支持**: 确保只在支持的模型上使用
2. **权限检查**: 验证用户的聊天权限
3. **错误处理**: 提供友好的错误提示
4. **性能考虑**: 避免重复打开相同的聊天窗口

## 扩展建议

1. **更多模型支持**: 扩展支持更多模型类型
2. **群组聊天**: 支持群组聊天的打开
3. **聊天预览**: 添加聊天内容的快速预览
4. **快捷键**: 支持键盘快捷键打开聊天
5. **聊天模板**: 支持预定义的聊天模板

该钩子为Web界面提供了便捷的聊天打开功能，是用户交互的重要工具。

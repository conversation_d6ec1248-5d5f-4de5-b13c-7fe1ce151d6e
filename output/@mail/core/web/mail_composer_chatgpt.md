# Mail Composer ChatGPT - 邮件编写ChatGPT集成

## 概述

`mail_composer_chatgpt.js` 实现了 Odoo 邮件系统中邮件编写器的ChatGPT集成字段组件，用于在邮件编写界面中集成AI助手功能。该组件提供了一个AI按钮，点击后打开ChatGPT提示对话框，用户可以通过AI生成邮件内容，生成的内容会自动插入到邮件正文中，支持内容清理和安全处理，为用户提供了智能的邮件编写辅助功能。

## 文件信息
- **路径**: `/mail/static/src/core/web/mail_composer_chatgpt.js`
- **行数**: 58
- **模块**: `@mail/core/web/mail_composer_chatgpt`

## 依赖关系

```javascript
// UI组件依赖
'@html_editor/main/chatgpt/chatgpt_prompt_dialog'  // ChatGPT提示对话框

// 核心依赖
'@web/core/l10n/translation'                      // 国际化
'@web/core/registry'                              // 注册表系统
'@web/views/fields/standard_field_props'          // 标准字段属性
'@odoo/owl'                                       // OWL 框架
```

## 组件定义

### MailComposerChatGPT 类

```javascript
class MailComposerChatGPT extends Component {
    static template = "mail.MailComposerChatGPT";
    static props = { ...standardFieldProps };
}
```

**组件特性**:
- 使用自定义模板"mail.MailComposerChatGPT"
- 继承标准字段属性

## 组件设置

### setup() 方法

```javascript
setup() {
    this.btnLabel = _t("AI"); // workaround to translate short string
}
```

**初始化内容**:
- 设置AI按钮的国际化标签

## 核心功能

### 1. ChatGPT对话框打开

```javascript
async onOpenChatGPTPromptDialogBtnClick() {
    this.env.services.dialog.add(ChatGPTPromptDialog, {
        insert: content => {
            const root = document.createElement("div");
            root.appendChild(content);
            const { body } = this.props.record.data;
            this.props.record.update({
                body: body + markup(root.innerHTML)
            });
        },
        sanitize: (fragment) => {
            return DOMPurify.sanitize(fragment, {
                IN_PLACE: true,
                ADD_TAGS: ["#document-fragment"],
                ADD_ATTR: ["contenteditable"],
            });
        },
    });
}
```

**对话框配置**:
- **insert回调**: 将AI生成的内容插入到邮件正文
- **sanitize回调**: 清理和安全处理生成的内容

## 字段注册

### 字段配置

```javascript
const mailComposerChatGPT = {
    component: MailComposerChatGPT,
    fieldDependencies: [{ name: "body", type: "text" }],
};

registry.category("fields").add("mail_composer_chatgpt", mailComposerChatGPT);
```

**注册参数**:
- **组件**: MailComposerChatGPT
- **字段依赖**: body字段（文本类型）
- **注册名称**: "mail_composer_chatgpt"

## 使用场景

### 1. 邮件编写AI助手

```javascript
// 邮件编写AI助手集成
const MailComposerAIAssistant = {
    setupAIAssistant: (composer) => {
        const aiContainer = document.createElement('div');
        aiContainer.className = 'ai-assistant-container';
        aiContainer.innerHTML = `
            <div class="ai-assistant-header">
                <h4>AI 写作助手</h4>
                <button class="ai-toggle-btn">
                    <i class="fa fa-robot"></i>
                </button>
            </div>
            
            <div class="ai-assistant-content">
                <div class="ai-suggestions">
                    <h5>建议操作</h5>
                    <button class="ai-action-btn" data-action="improve">改进文本</button>
                    <button class="ai-action-btn" data-action="shorten">缩短内容</button>
                    <button class="ai-action-btn" data-action="expand">扩展内容</button>
                    <button class="ai-action-btn" data-action="formal">正式化</button>
                    <button class="ai-action-btn" data-action="casual">口语化</button>
                </div>
                
                <div class="ai-templates">
                    <h5>邮件模板</h5>
                    <select class="template-selector">
                        <option value="">选择模板</option>
                        <option value="meeting">会议邀请</option>
                        <option value="follow-up">跟进邮件</option>
                        <option value="thank-you">感谢邮件</option>
                        <option value="apology">道歉邮件</option>
                        <option value="announcement">公告邮件</option>
                    </select>
                </div>
            </div>
        `;
        
        MailComposerAIAssistant.bindAIEvents(aiContainer, composer);
        return aiContainer;
    },
    
    bindAIEvents: (container, composer) => {
        // AI操作按钮事件
        container.querySelectorAll('.ai-action-btn').forEach(btn => {
            btn.addEventListener('click', async (event) => {
                const action = event.target.dataset.action;
                await MailComposerAIAssistant.executeAIAction(action, composer);
            });
        });
        
        // 模板选择事件
        const templateSelector = container.querySelector('.template-selector');
        templateSelector.addEventListener('change', async (event) => {
            const template = event.target.value;
            if (template) {
                await MailComposerAIAssistant.applyTemplate(template, composer);
            }
        });
    },
    
    executeAIAction: async (action, composer) => {
        const currentBody = composer.props.record.data.body;
        
        if (!currentBody || currentBody.trim() === '') {
            showNotification('请先输入一些文本', 'warning');
            return;
        }
        
        try {
            const prompt = MailComposerAIAssistant.generatePrompt(action, currentBody);
            const result = await MailComposerAIAssistant.callAI(prompt);
            
            if (result) {
                composer.props.record.update({
                    body: markup(result)
                });
                
                showNotification(`AI ${action} 完成`, 'success');
            }
        } catch (error) {
            console.error('AI操作失败:', error);
            showNotification('AI操作失败，请重试', 'error');
        }
    },
    
    generatePrompt: (action, content) => {
        const prompts = {
            'improve': `请改进以下邮件内容，使其更加清晰和专业：\n\n${content}`,
            'shorten': `请将以下邮件内容缩短，保持核心信息：\n\n${content}`,
            'expand': `请扩展以下邮件内容，添加更多细节：\n\n${content}`,
            'formal': `请将以下邮件内容改写为更正式的语调：\n\n${content}`,
            'casual': `请将以下邮件内容改写为更轻松的语调：\n\n${content}`
        };
        
        return prompts[action] || content;
    },
    
    applyTemplate: async (template, composer) => {
        try {
            const templateContent = await MailComposerAIAssistant.generateTemplate(template);
            
            if (templateContent) {
                composer.props.record.update({
                    body: markup(templateContent)
                });
                
                showNotification(`已应用 ${template} 模板`, 'success');
            }
        } catch (error) {
            console.error('模板应用失败:', error);
            showNotification('模板应用失败，请重试', 'error');
        }
    },
    
    generateTemplate: async (template) => {
        const templates = {
            'meeting': `
                <p>您好，</p>
                <p>我想邀请您参加一个会议，讨论 [会议主题]。</p>
                <p><strong>会议详情：</strong></p>
                <ul>
                    <li>时间：[日期和时间]</li>
                    <li>地点：[会议地点或在线链接]</li>
                    <li>议程：[会议议程]</li>
                </ul>
                <p>请确认您的参会情况。</p>
                <p>谢谢！</p>
            `,
            'follow-up': `
                <p>您好，</p>
                <p>我想跟进一下我们之前讨论的 [主题]。</p>
                <p>请问您对此有什么想法或需要进一步的信息吗？</p>
                <p>期待您的回复。</p>
                <p>谢谢！</p>
            `,
            'thank-you': `
                <p>您好，</p>
                <p>感谢您 [具体事项]。</p>
                <p>您的 [帮助/支持/合作] 对我们非常重要。</p>
                <p>再次感谢！</p>
            `,
            'apology': `
                <p>您好，</p>
                <p>我为 [具体事项] 向您道歉。</p>
                <p>我们已经采取措施确保类似情况不会再次发生。</p>
                <p>感谢您的理解。</p>
            `,
            'announcement': `
                <p>各位同事，</p>
                <p>我想向大家宣布 [公告内容]。</p>
                <p><strong>重要信息：</strong></p>
                <ul>
                    <li>[要点1]</li>
                    <li>[要点2]</li>
                    <li>[要点3]</li>
                </ul>
                <p>如有任何问题，请随时联系我。</p>
                <p>谢谢！</p>
            `
        };
        
        return templates[template] || '';
    },
    
    callAI: async (prompt) => {
        // 这里应该调用实际的AI服务
        // 为了演示，返回模拟结果
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve(`AI处理后的内容：${prompt}`);
            }, 1000);
        });
    }
};
```

### 2. AI内容生成器

```javascript
// AI内容生成器
const AIContentGenerator = {
    generateEmailContent: async (context) => {
        const { type, recipient, subject, tone, length } = context;
        
        try {
            const prompt = AIContentGenerator.buildPrompt(context);
            const content = await AIContentGenerator.callAIService(prompt);
            
            return AIContentGenerator.formatContent(content);
        } catch (error) {
            console.error('AI内容生成失败:', error);
            throw new Error('AI内容生成失败');
        }
    },
    
    buildPrompt: (context) => {
        let prompt = `请生成一封${context.type}邮件`;
        
        if (context.recipient) {
            prompt += `，收件人是${context.recipient}`;
        }
        
        if (context.subject) {
            prompt += `，主题是"${context.subject}"`;
        }
        
        if (context.tone) {
            prompt += `，语调要${context.tone}`;
        }
        
        if (context.length) {
            prompt += `，长度要${context.length}`;
        }
        
        prompt += '。请用HTML格式返回内容。';
        
        return prompt;
    },
    
    callAIService: async (prompt) => {
        // 调用AI服务API
        const response = await fetch('/web/dataset/call_kw/mail.composer/generate_ai_content', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                model: 'mail.composer',
                method: 'generate_ai_content',
                args: [prompt],
                kwargs: {}
            })
        });
        
        const result = await response.json();
        
        if (result.error) {
            throw new Error(result.error.message);
        }
        
        return result.result;
    },
    
    formatContent: (content) => {
        // 清理和格式化AI生成的内容
        const cleanContent = DOMPurify.sanitize(content, {
            ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'ul', 'ol', 'li', 'h1', 'h2', 'h3'],
            ALLOWED_ATTR: []
        });
        
        return markup(cleanContent);
    }
};
```

### 3. AI写作建议

```javascript
// AI写作建议
const AIWritingSuggestions = {
    analyzeContent: (content) => {
        const suggestions = [];
        
        // 分析内容长度
        if (content.length < 50) {
            suggestions.push({
                type: 'length',
                message: '邮件内容较短，考虑添加更多细节',
                action: 'expand'
            });
        } else if (content.length > 1000) {
            suggestions.push({
                type: 'length',
                message: '邮件内容较长，考虑精简表达',
                action: 'shorten'
            });
        }
        
        // 分析语调
        if (AIWritingSuggestions.isTooFormal(content)) {
            suggestions.push({
                type: 'tone',
                message: '语调过于正式，考虑使用更友好的表达',
                action: 'casual'
            });
        } else if (AIWritingSuggestions.isTooInformal(content)) {
            suggestions.push({
                type: 'tone',
                message: '语调过于随意，考虑使用更正式的表达',
                action: 'formal'
            });
        }
        
        // 分析结构
        if (!AIWritingSuggestions.hasGoodStructure(content)) {
            suggestions.push({
                type: 'structure',
                message: '邮件结构可以改进，建议重新组织内容',
                action: 'improve'
            });
        }
        
        return suggestions;
    },
    
    isTooFormal: (content) => {
        const formalWords = ['敬启者', '此致敬礼', '谨此', '恳请'];
        return formalWords.some(word => content.includes(word));
    },
    
    isTooInformal: (content) => {
        const informalWords = ['哈哈', '嗯嗯', '好的好的', '没问题'];
        return informalWords.some(word => content.includes(word));
    },
    
    hasGoodStructure: (content) => {
        // 简单检查是否有开头、正文、结尾
        const hasGreeting = /您好|你好|Dear/.test(content);
        const hasClosing = /谢谢|感谢|此致|Best regards/.test(content);
        
        return hasGreeting && hasClosing;
    },
    
    showSuggestions: (suggestions, composer) => {
        if (suggestions.length === 0) {
            return;
        }
        
        const suggestionContainer = document.createElement('div');
        suggestionContainer.className = 'ai-suggestions-container';
        suggestionContainer.innerHTML = `
            <div class="suggestions-header">
                <h5>AI 写作建议</h5>
                <button class="close-suggestions-btn">&times;</button>
            </div>
            <div class="suggestions-list">
                ${suggestions.map(suggestion => `
                    <div class="suggestion-item">
                        <span class="suggestion-message">${suggestion.message}</span>
                        <button class="apply-suggestion-btn" data-action="${suggestion.action}">
                            应用建议
                        </button>
                    </div>
                `).join('')}
            </div>
        `;
        
        // 绑定事件
        suggestionContainer.querySelectorAll('.apply-suggestion-btn').forEach(btn => {
            btn.addEventListener('click', async (event) => {
                const action = event.target.dataset.action;
                await MailComposerAIAssistant.executeAIAction(action, composer);
                suggestionContainer.remove();
            });
        });
        
        suggestionContainer.querySelector('.close-suggestions-btn').addEventListener('click', () => {
            suggestionContainer.remove();
        });
        
        composer.el.appendChild(suggestionContainer);
    }
};
```

### 4. AI使用统计

```javascript
// AI使用统计
const AIUsageStatistics = {
    trackUsage: (action, success = true) => {
        const usage = {
            action,
            success,
            timestamp: Date.now(),
            userId: getCurrentUser().id
        };
        
        // 保存到本地存储
        const usageHistory = JSON.parse(localStorage.getItem('ai_usage_history') || '[]');
        usageHistory.push(usage);
        
        // 限制历史记录数量
        if (usageHistory.length > 1000) {
            usageHistory.shift();
        }
        
        localStorage.setItem('ai_usage_history', JSON.stringify(usageHistory));
        
        // 发送到分析服务
        sendAnalytics('ai_usage', usage);
    },
    
    getUsageStats: (period = 30) => {
        const usageHistory = JSON.parse(localStorage.getItem('ai_usage_history') || '[]');
        const periodStart = Date.now() - (period * 24 * 60 * 60 * 1000);
        
        const recentUsage = usageHistory.filter(usage => usage.timestamp > periodStart);
        
        const stats = {
            total: recentUsage.length,
            successful: recentUsage.filter(u => u.success).length,
            failed: recentUsage.filter(u => !u.success).length,
            byAction: {}
        };
        
        recentUsage.forEach(usage => {
            if (!stats.byAction[usage.action]) {
                stats.byAction[usage.action] = { total: 0, successful: 0, failed: 0 };
            }
            
            stats.byAction[usage.action].total++;
            if (usage.success) {
                stats.byAction[usage.action].successful++;
            } else {
                stats.byAction[usage.action].failed++;
            }
        });
        
        return stats;
    },
    
    showUsageReport: () => {
        const stats = AIUsageStatistics.getUsageStats();
        
        const report = `
AI 使用统计报告 (最近30天)
========================

总使用次数: ${stats.total}
成功次数: ${stats.successful}
失败次数: ${stats.failed}
成功率: ${stats.total > 0 ? ((stats.successful / stats.total) * 100).toFixed(1) : 0}%

按操作分类:
${Object.entries(stats.byAction).map(([action, data]) => 
    `- ${action}: ${data.total}次 (成功: ${data.successful}, 失败: ${data.failed})`
).join('\n')}
        `;
        
        console.log(report);
        return report;
    }
};
```

### 5. AI内容安全处理

```javascript
// AI内容安全处理
const AIContentSecurity = {
    sanitizeContent: (content) => {
        // 使用DOMPurify清理内容
        const cleanContent = DOMPurify.sanitize(content, {
            ALLOWED_TAGS: [
                'p', 'br', 'strong', 'em', 'u', 'ul', 'ol', 'li', 
                'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote',
                'a', 'img', 'table', 'thead', 'tbody', 'tr', 'td', 'th'
            ],
            ALLOWED_ATTR: [
                'href', 'src', 'alt', 'title', 'class', 'style',
                'target', 'rel', 'contenteditable'
            ],
            ALLOW_DATA_ATTR: false
        });
        
        return cleanContent;
    },
    
    validateContent: (content) => {
        const issues = [];
        
        // 检查敏感内容
        if (AIContentSecurity.containsSensitiveInfo(content)) {
            issues.push('内容可能包含敏感信息');
        }
        
        // 检查恶意链接
        if (AIContentSecurity.containsMaliciousLinks(content)) {
            issues.push('内容包含可疑链接');
        }
        
        // 检查格式问题
        if (AIContentSecurity.hasFormatIssues(content)) {
            issues.push('内容格式存在问题');
        }
        
        return {
            isValid: issues.length === 0,
            issues
        };
    },
    
    containsSensitiveInfo: (content) => {
        const sensitivePatterns = [
            /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/, // 信用卡号
            /\b\d{3}-\d{2}-\d{4}\b/, // 社会保险号
            /password\s*[:=]\s*\S+/i, // 密码
        ];
        
        return sensitivePatterns.some(pattern => pattern.test(content));
    },
    
    containsMaliciousLinks: (content) => {
        const suspiciousDomains = [
            'bit.ly', 'tinyurl.com', 'goo.gl', 't.co'
        ];
        
        const linkPattern = /https?:\/\/([^\/\s]+)/gi;
        const matches = content.match(linkPattern) || [];
        
        return matches.some(link => {
            const domain = link.replace(/https?:\/\//, '').split('/')[0];
            return suspiciousDomains.includes(domain);
        });
    },
    
    hasFormatIssues: (content) => {
        // 检查是否有未闭合的标签
        const openTags = (content.match(/<[^\/][^>]*>/g) || []).length;
        const closeTags = (content.match(/<\/[^>]*>/g) || []).length;
        
        return Math.abs(openTags - closeTags) > 2; // 允许一些自闭合标签
    }
};
```

## 错误处理

### 1. AI服务错误处理

```javascript
// AI服务错误处理
const AIErrorHandler = {
    handleAIError: (error, context) => {
        let userMessage = 'AI服务暂时不可用，请稍后重试';
        
        if (error.message.includes('quota')) {
            userMessage = 'AI使用配额已用完，请联系管理员';
        } else if (error.message.includes('network')) {
            userMessage = '网络连接失败，请检查网络后重试';
        } else if (error.message.includes('content')) {
            userMessage = '内容不符合AI服务要求，请修改后重试';
        }
        
        showNotification(userMessage, 'error');
        
        // 记录错误
        console.error('AI服务错误:', {
            error: error.message,
            context,
            timestamp: Date.now()
        });
        
        // 统计错误
        AIUsageStatistics.trackUsage(context.action, false);
    }
};
```

## 性能优化

### 1. AI请求优化

```javascript
// AI请求优化
const AIRequestOptimizer = {
    debounceRequest: debounce(async (prompt, callback) => {
        try {
            const result = await AIContentGenerator.callAIService(prompt);
            callback(null, result);
        } catch (error) {
            callback(error, null);
        }
    }, 1000),
    
    cacheResults: new Map(),
    
    getCachedResult: (prompt) => {
        const hash = AIRequestOptimizer.hashPrompt(prompt);
        return AIRequestOptimizer.cacheResults.get(hash);
    },
    
    setCachedResult: (prompt, result) => {
        const hash = AIRequestOptimizer.hashPrompt(prompt);
        AIRequestOptimizer.cacheResults.set(hash, result);
        
        // 限制缓存大小
        if (AIRequestOptimizer.cacheResults.size > 100) {
            const firstKey = AIRequestOptimizer.cacheResults.keys().next().value;
            AIRequestOptimizer.cacheResults.delete(firstKey);
        }
    },
    
    hashPrompt: (prompt) => {
        // 简单的哈希函数
        let hash = 0;
        for (let i = 0; i < prompt.length; i++) {
            const char = prompt.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return hash.toString();
    }
};
```

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的AI集成组件
- 清晰的组件接口

### 2. 策略模式 (Strategy Pattern)
- 不同AI操作的不同处理策略
- 可配置的内容生成策略

### 3. 代理模式 (Proxy Pattern)
- AI服务的代理访问
- 请求缓存和优化

## 注意事项

1. **内容安全**: 严格清理和验证AI生成的内容
2. **用户隐私**: 保护用户输入的敏感信息
3. **服务可用性**: 处理AI服务的不可用情况
4. **使用配额**: 合理管理AI服务的使用配额

## 扩展建议

1. **多AI提供商**: 支持多个AI服务提供商
2. **自定义提示**: 允许用户自定义AI提示模板
3. **内容学习**: 基于用户反馈改进AI生成质量
4. **协作功能**: 支持团队共享AI生成的内容
5. **离线模式**: 提供基本的离线AI功能

该组件为邮件编写提供了强大的AI辅助功能，是现代邮件系统的重要创新。

# Activity Mark As Done Popover - 活动标记完成弹出框

## 概述

`activity_markasdone_popover.js` 实现了 Odoo 邮件系统中的活动标记完成弹出框组件，用于提供活动完成时的交互界面。该组件支持简单标记完成和完成并安排下一个活动两种操作，提供了文本输入区域用于添加完成备注，支持键盘快捷键操作，是活动管理流程中的重要交互组件，为用户提供了便捷的活动完成体验。

## 文件信息
- **路径**: `/mail/static/src/core/web/activity_markasdone_popover.js`
- **行数**: 81
- **模块**: `@mail/core/web/activity_markasdone_popover`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'  // OWL 框架（Component, onMounted, useExternalListener, useRef）
```

## 组件定义

### ActivityMarkAsDone 类

```javascript
class ActivityMarkAsDone extends Component {
    static template = "mail.ActivityMarkAsDone";
    static props = [
        "activity",                        // 活动对象
        "close?",                         // 关闭回调（可选）
        "hasHeader?",                     // 是否有头部（可选）
        "onClickDoneAndScheduleNext?",    // 完成并安排下一个回调（可选）
        "onActivityChanged",              // 活动变化回调
    ];
    static defaultProps = {
        hasHeader: false,
    };
}
```

## Props 配置

### Props 详细说明

- **`activity`** (必需):
  - 类型: Activity 模型实例
  - 用途: 要标记完成的活动对象
  - 功能: 提供活动的所有数据和操作方法

- **`close`** (可选):
  - 类型: 函数
  - 用途: 关闭弹出框的回调函数
  - 功能: 用户操作完成后关闭弹出框

- **`hasHeader`** (可选):
  - 类型: 布尔值
  - 默认值: false
  - 用途: 控制是否显示弹出框头部
  - 功能: 适配不同的显示场景

- **`onClickDoneAndScheduleNext`** (可选):
  - 类型: 函数
  - 用途: 完成并安排下一个活动的回调
  - 功能: 支持连续活动处理流程

- **`onActivityChanged`** (必需):
  - 类型: 函数
  - 用途: 活动发生变化时的回调
  - 参数: thread (线程对象)

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.textArea = useRef("textarea");
    onMounted(() => {
        this.textArea.el.focus();
    });
    useExternalListener(window, "keydown", this.onKeydown);
}
```

**初始化内容**:
- 文本区域的DOM引用
- 组件挂载后自动聚焦到文本区域
- 全局键盘事件监听

## 核心功能

### 1. 建议状态检查

```javascript
get isSuggested() {
    return this.props.activity.chaining_type === "suggest";
}
```

**建议状态**:
- 检查活动的链接类型是否为"suggest"
- 用于确定是否显示建议的下一个活动

### 2. 键盘事件处理

```javascript
onKeydown(ev) {
    if (ev.key === "Escape" && this.props.close) {
        this.props.close();
    }
}
```

**键盘快捷键**:
- **Escape键**: 关闭弹出框（如果提供了关闭回调）

### 3. 简单标记完成

```javascript
async onClickDone() {
    const { res_id, res_model } = this.props.activity;
    const thread = this.env.services["mail.store"].Thread.insert({
        model: res_model,
        id: res_id,
    });
    await this.props.activity.markAsDone();
    this.props.onActivityChanged(thread);
    await thread.fetchNewMessages();
}
```

**完成流程**:
1. 获取活动的资源ID和模型
2. 创建或获取对应的线程对象
3. 调用活动的标记完成方法
4. 触发活动变化回调
5. 获取线程的新消息

### 4. 完成并安排下一个

```javascript
async onClickDoneAndScheduleNext() {
    const { res_id, res_model } = this.props.activity;
    const thread = this.env.services["mail.store"].Thread.insert({
        model: res_model,
        id: res_id,
    });
    if (this.props.onClickDoneAndScheduleNext) {
        this.props.onClickDoneAndScheduleNext();
    }
    if (this.props.close) {
        this.props.close();
    }
    const action = await this.props.activity.markAsDoneAndScheduleNext();
    thread.fetchNewMessages();
    this.props.onActivityChanged(thread);
    if (!action) {
        return;
    }
    await new Promise((resolve) => {
        this.env.services.action.doAction(action, {
            onClose: resolve,
        });
    });
    this.props.onActivityChanged(thread);
}
```

**完成并安排下一个流程**:
1. 获取活动的资源信息
2. 创建或获取线程对象
3. 调用可选的回调函数
4. 关闭当前弹出框
5. 执行标记完成并安排下一个的操作
6. 获取新消息并触发变化回调
7. 如果有返回动作，执行该动作
8. 动作完成后再次触发变化回调

## 使用场景

### 1. 基本活动完成

```javascript
// 基本的活动完成弹出框
const BasicActivityCompletion = ({ activity, onComplete }) => {
    const handleActivityChanged = (thread) => {
        // 更新活动列表
        refreshActivityList();
        
        // 通知完成
        onComplete(activity);
        
        // 更新相关数据
        updateRelatedData(thread);
    };
    
    return (
        <ActivityMarkAsDone
            activity={activity}
            onActivityChanged={handleActivityChanged}
            close={() => closePopover()}
        />
    );
};
```

### 2. 带头部的活动完成

```javascript
// 带头部的活动完成弹出框
const HeaderedActivityCompletion = ({ activity, title }) => {
    const handleActivityChanged = (thread) => {
        // 记录完成操作
        logActivityCompletion(activity);
        
        // 更新统计
        updateActivityStats();
        
        // 刷新界面
        refreshUI();
    };
    
    return (
        <div class="activity-completion-with-header">
            <ActivityMarkAsDone
                activity={activity}
                hasHeader={true}
                onActivityChanged={handleActivityChanged}
                close={() => hideDialog()}
            />
        </div>
    );
};
```

### 3. 连续活动处理

```javascript
// 支持连续活动处理的完成弹出框
const SequentialActivityCompletion = ({ activity, nextActivity }) => {
    const handleDoneAndNext = () => {
        // 记录连续处理
        logSequentialProcessing(activity, nextActivity);
        
        // 更新工作流状态
        updateWorkflowStatus();
    };
    
    const handleActivityChanged = (thread) => {
        // 更新活动序列
        updateActivitySequence();
        
        // 通知相关人员
        notifyStakeholders(activity);
    };
    
    return (
        <ActivityMarkAsDone
            activity={activity}
            onClickDoneAndScheduleNext={handleDoneAndNext}
            onActivityChanged={handleActivityChanged}
            close={() => closeSequentialDialog()}
        />
    );
};
```

### 4. 项目管理中的活动完成

```javascript
// 项目管理中的活动完成
const ProjectActivityCompletion = ({ activity, project }) => {
    const handleProjectActivityChanged = (thread) => {
        // 更新项目进度
        updateProjectProgress(project);
        
        // 检查项目里程碑
        checkProjectMilestones(project);
        
        // 通知项目团队
        notifyProjectTeam(project, activity);
    };
    
    return (
        <div class="project-activity-completion">
            <div class="project-context">
                <span>项目: {project.name}</span>
                <span>阶段: {project.stage}</span>
            </div>
            <ActivityMarkAsDone
                activity={activity}
                hasHeader={true}
                onActivityChanged={handleProjectActivityChanged}
                close={() => closeProjectDialog()}
            />
        </div>
    );
};
```

## 交互功能

### 1. 文本输入处理

```javascript
// 文本输入处理
const handleTextInput = (component) => {
    const textarea = component.textArea.el;
    
    // 自动调整高度
    const adjustHeight = () => {
        textarea.style.height = 'auto';
        textarea.style.height = textarea.scrollHeight + 'px';
    };
    
    // 监听输入事件
    textarea.addEventListener('input', adjustHeight);
    
    // 监听键盘事件
    textarea.addEventListener('keydown', (event) => {
        // Ctrl+Enter 快速完成
        if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
            event.preventDefault();
            component.onClickDone();
        }
        
        // Shift+Enter 完成并安排下一个
        if (event.shiftKey && event.key === 'Enter') {
            event.preventDefault();
            component.onClickDoneAndScheduleNext();
        }
    });
    
    return { adjustHeight };
};
```

### 2. 自动保存草稿

```javascript
// 自动保存草稿功能
const setupAutoSave = (component, activity) => {
    const textarea = component.textArea.el;
    const draftKey = `activity_completion_draft_${activity.id}`;
    
    // 加载草稿
    const loadDraft = () => {
        const draft = localStorage.getItem(draftKey);
        if (draft) {
            textarea.value = draft;
        }
    };
    
    // 保存草稿
    const saveDraft = debounce(() => {
        localStorage.setItem(draftKey, textarea.value);
    }, 1000);
    
    // 清除草稿
    const clearDraft = () => {
        localStorage.removeItem(draftKey);
    };
    
    // 监听输入
    textarea.addEventListener('input', saveDraft);
    
    // 完成时清除草稿
    const originalOnClickDone = component.onClickDone;
    component.onClickDone = async function() {
        clearDraft();
        return originalOnClickDone.call(this);
    };
    
    // 初始化时加载草稿
    loadDraft();
    
    return { loadDraft, saveDraft, clearDraft };
};
```

### 3. 模板建议

```javascript
// 完成备注模板建议
const setupTemplatesSuggestion = (component, activity) => {
    const templates = getCompletionTemplates(activity.activity_type_id);
    
    if (templates.length === 0) {
        return;
    }
    
    // 创建模板选择器
    const templateSelector = document.createElement('div');
    templateSelector.className = 'completion-templates';
    
    templates.forEach(template => {
        const button = document.createElement('button');
        button.textContent = template.name;
        button.onclick = () => {
            component.textArea.el.value = template.content;
            component.textArea.el.focus();
        };
        templateSelector.appendChild(button);
    });
    
    // 插入到组件中
    component.textArea.el.parentNode.insertBefore(
        templateSelector, 
        component.textArea.el
    );
};

// 获取完成模板
const getCompletionTemplates = (activityTypeId) => {
    const templates = {
        'call': [
            { name: '已联系', content: '已成功联系客户，讨论了相关事宜。' },
            { name: '未接通', content: '尝试联系但未接通，将稍后再试。' }
        ],
        'meeting': [
            { name: '会议完成', content: '会议已顺利完成，达成了预期目标。' },
            { name: '会议延期', content: '会议因故延期，将重新安排时间。' }
        ],
        'email': [
            { name: '邮件已发', content: '相关邮件已发送，等待回复。' },
            { name: '邮件跟进', content: '已发送跟进邮件，继续保持联系。' }
        ]
    };
    
    return templates[activityTypeId] || [];
};
```

## 状态管理

### 1. 完成状态跟踪

```javascript
// 完成状态跟踪
const trackCompletionState = (component) => {
    const [completionState, setCompletionState] = useState({
        isCompleting: false,
        isSchedulingNext: false,
        hasError: false,
        errorMessage: ''
    });
    
    const setCompleting = (completing) => {
        setCompletionState(prev => ({
            ...prev,
            isCompleting: completing,
            hasError: false
        }));
    };
    
    const setSchedulingNext = (scheduling) => {
        setCompletionState(prev => ({
            ...prev,
            isSchedulingNext: scheduling,
            hasError: false
        }));
    };
    
    const setError = (error) => {
        setCompletionState(prev => ({
            ...prev,
            isCompleting: false,
            isSchedulingNext: false,
            hasError: true,
            errorMessage: error.message
        }));
    };
    
    return { completionState, setCompleting, setSchedulingNext, setError };
};
```

### 2. 验证状态

```javascript
// 输入验证
const validateCompletion = (component, activity) => {
    const textarea = component.textArea.el;
    const content = textarea.value.trim();
    
    const validation = {
        isValid: true,
        errors: []
    };
    
    // 检查是否需要备注
    if (activity.force_next && !content) {
        validation.isValid = false;
        validation.errors.push('此活动需要填写完成备注');
    }
    
    // 检查备注长度
    if (content.length > 1000) {
        validation.isValid = false;
        validation.errors.push('备注内容不能超过1000个字符');
    }
    
    return validation;
};
```

## 性能优化

### 1. 防抖处理

```javascript
// 防抖处理
const debouncedOperations = {
    saveDraft: debounce((content, activityId) => {
        localStorage.setItem(`activity_completion_draft_${activityId}`, content);
    }, 1000),
    
    validateInput: debounce((component, activity) => {
        const validation = validateCompletion(component, activity);
        updateValidationUI(validation);
    }, 300)
};
```

### 2. 内存管理

```javascript
// 内存管理
const setupMemoryManagement = (component) => {
    const cleanup = () => {
        // 清理事件监听器
        window.removeEventListener('keydown', component.onKeydown);
        
        // 清理DOM引用
        component.textArea = null;
        
        // 清理定时器
        clearTimeout(component.autoSaveTimer);
    };
    
    // 组件销毁时清理
    onWillUnmount(cleanup);
    
    return cleanup;
};
```

## 可访问性

### 1. 键盘导航

```javascript
// 增强键盘导航
const enhanceKeyboardNavigation = (component) => {
    const handleKeyDown = (event) => {
        switch (event.key) {
            case 'Tab':
                // 处理Tab导航
                handleTabNavigation(event);
                break;
            case 'Enter':
                if (event.ctrlKey || event.metaKey) {
                    event.preventDefault();
                    component.onClickDone();
                } else if (event.shiftKey) {
                    event.preventDefault();
                    component.onClickDoneAndScheduleNext();
                }
                break;
            case 'Escape':
                event.preventDefault();
                if (component.props.close) {
                    component.props.close();
                }
                break;
        }
    };
    
    component.textArea.el.addEventListener('keydown', handleKeyDown);
};
```

### 2. ARIA 标签

```javascript
// 可访问性属性
const getAccessibilityProps = (activity) => {
    return {
        'role': 'dialog',
        'aria-label': '标记活动完成',
        'aria-describedby': 'completion-instructions',
        'aria-modal': 'true'
    };
};
```

## 设计模式

### 1. 命令模式 (Command Pattern)
- 封装完成和安排下一个操作
- 支持撤销和重做

### 2. 状态模式 (State Pattern)
- 根据活动状态显示不同选项
- 状态驱动的行为变化

### 3. 观察者模式 (Observer Pattern)
- 监听活动变化
- 响应用户输入

## 注意事项

1. **用户体验**: 提供清晰的操作指引和反馈
2. **数据验证**: 验证用户输入的完整性
3. **错误处理**: 处理网络错误和操作失败
4. **性能考虑**: 避免不必要的重新渲染

## 扩展建议

1. **富文本编辑**: 支持富文本格式的完成备注
2. **附件上传**: 支持在完成时上传相关附件
3. **模板管理**: 支持自定义完成备注模板
4. **批量完成**: 支持批量标记多个活动完成
5. **移动端优化**: 优化移动设备上的交互体验

该组件为活动完成提供了完整的交互界面，支持简单完成和连续处理两种模式，是活动管理流程的重要组成部分。

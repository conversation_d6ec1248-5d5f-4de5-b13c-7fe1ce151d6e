# Mail Composer Recipient List - 邮件编写收件人列表

## 概述

`mail_composer_recipient_list.js` 实现了 Odoo 邮件系统中邮件编写器的收件人列表小部件，用于在邮件编写界面中显示线程的收件人信息。该组件继承自基础收件人列表组件，支持多种线程ID字段类型的解析，包括文本类型和many2one_reference类型，通过异步获取线程数据来显示收件人列表，为邮件编写提供了完整的收件人信息展示功能。

## 文件信息
- **路径**: `/mail/static/src/core/web/mail_composer_recipient_list.js`
- **行数**: 64
- **模块**: `@mail/core/web/mail_composer_recipient_list`

## 依赖关系

```javascript
// UI组件依赖
'@mail/core/web/base_recipients_list'      // 基础收件人列表组件

// 核心依赖
'@web/core/registry'                       // 注册表系统
'@web/views/widgets/standard_widget_props' // 标准小部件属性
'@web/core/utils/hooks'                    // Web 核心钩子
'@odoo/owl'                               // OWL 框架
```

## 组件定义

### MailComposerRecipientList 类

```javascript
class MailComposerRecipientList extends Component {
    static template = "mail.MailComposerRecipientList";
    static components = { BaseRecipientsList };
    static props = {
        ...standardWidgetProps,
        thread_model_field: { type: String },
        thread_id_field: { type: String },
    };
}
```

**组件特性**:
- 使用自定义模板"mail.MailComposerRecipientList"
- 集成BaseRecipientsList基础组件
- 继承标准小部件属性
- 支持线程模型和ID字段配置

## Props 配置

### Props 详细说明

- **继承的Props**: 继承standardWidgetProps的所有属性
- **`thread_model_field`** (必需):
  - 类型: 字符串
  - 用途: 指定线程模型字段名
  - 功能: 用于获取线程的模型类型

- **`thread_id_field`** (必需):
  - 类型: 字符串
  - 用途: 指定线程ID字段名
  - 功能: 用于获取线程的ID值

## 组件设置

### setup() 方法

```javascript
setup() {
    const { Thread } = useService("mail.store");
    this.state = useState({});
    onWillStart(async () => {
        // 线程数据获取逻辑...
    });
}
```

**初始化内容**:
- 邮件存储服务中的Thread模型
- 响应式状态管理
- 组件启动前的异步数据加载

## 核心功能

### 1. 线程ID解析

```javascript
const threadIdFieldType = this.props.record.fields[this.props.thread_id_field].type;
let threadId;

if (threadIdFieldType === "text") {
    // composer stores id in a string representing an array
    threadId = JSON.parse(this.props.record.data[this.props.thread_id_field])[0];
} else if (threadIdFieldType === "many2one_reference") {
    // scheduled message stores id as a many2one reference
    threadId = this.props.record.data[this.props.thread_id_field].resId;
} else {
    console.error("Thread id field type not supported");
    return;
}
```

**解析逻辑**:
- **文本类型**: 解析JSON数组格式的字符串，取第一个元素
- **many2one_reference类型**: 直接获取resId属性
- **不支持的类型**: 记录错误并返回

### 2. 线程数据获取

```javascript
try {
    const thread = await Thread.getOrFetch({
        model: this.props.record.data[this.props.thread_model_field],
        id: threadId,
    });
    this.state.thread = thread;
} catch (e) {
    console.error(e);
}
```

**获取逻辑**:
- 使用Thread服务的getOrFetch方法
- 传入线程模型和ID参数
- 将获取的线程数据保存到状态中
- 错误处理和日志记录

## 小部件注册

### 小部件配置

```javascript
const mailComposerRecipientList = {
    component: MailComposerRecipientList,
    extractProps: ({ attrs }) => ({
        thread_model_field: attrs.thread_model_field,
        thread_id_field: attrs.thread_id_field,
    }),
};

registry.category("view_widgets").add("mail_composer_recipient_list", mailComposerRecipientList);
```

**注册参数**:
- **组件**: MailComposerRecipientList
- **属性提取**: 从XML属性中提取线程字段配置
- **注册类别**: "view_widgets"
- **注册名称**: "mail_composer_recipient_list"

## 使用场景

### 1. 邮件编写表单集成

```javascript
// 邮件编写表单中的收件人列表小部件
const MailComposerFormIntegration = {
    setupRecipientListWidget: (formView) => {
        const widgetConfig = {
            name: 'recipient_list_widget',
            type: 'mail_composer_recipient_list',
            thread_model_field: 'model',
            thread_id_field: 'res_id',
            options: {
                show_details: true,
                allow_edit: false,
                max_display: 10
            }
        };
        
        return widgetConfig;
    },
    
    renderRecipientWidget: (record, options) => {
        return `
            <div class="mail-composer-recipient-widget">
                <div class="widget-header">
                    <h4>收件人列表</h4>
                    <span class="recipient-count">
                        共 ${record.thread?.recipients?.length || 0} 个收件人
                    </span>
                </div>
                
                <mail_composer_recipient_list
                    thread_model_field="${options.thread_model_field}"
                    thread_id_field="${options.thread_id_field}"
                />
                
                <div class="widget-footer">
                    <small class="text-muted">
                        显示与此记录相关的所有收件人
                    </small>
                </div>
            </div>
        `;
    }
};
```

### 2. 动态线程数据管理

```javascript
// 动态线程数据管理
const DynamicThreadDataManager = {
    handleThreadChange: async (widget, newThreadData) => {
        try {
            // 验证新的线程数据
            const validation = DynamicThreadDataManager.validateThreadData(newThreadData);
            if (!validation.isValid) {
                throw new Error(validation.error);
            }
            
            // 获取新的线程
            const thread = await widget.Thread.getOrFetch({
                model: newThreadData.model,
                id: newThreadData.id
            });
            
            // 更新小部件状态
            widget.state.thread = thread;
            
            // 触发更新事件
            DynamicThreadDataManager.notifyThreadUpdate(widget, thread);
            
        } catch (error) {
            console.error('线程数据更新失败:', error);
            DynamicThreadDataManager.handleUpdateError(widget, error);
        }
    },
    
    validateThreadData: (threadData) => {
        if (!threadData.model) {
            return { isValid: false, error: '缺少线程模型' };
        }
        
        if (!threadData.id) {
            return { isValid: false, error: '缺少线程ID' };
        }
        
        if (typeof threadData.id !== 'number' && typeof threadData.id !== 'string') {
            return { isValid: false, error: '线程ID类型无效' };
        }
        
        return { isValid: true };
    },
    
    notifyThreadUpdate: (widget, thread) => {
        const event = new CustomEvent('thread_updated', {
            detail: {
                widget: widget,
                thread: thread,
                timestamp: Date.now()
            }
        });
        
        document.dispatchEvent(event);
    },
    
    handleUpdateError: (widget, error) => {
        // 显示错误状态
        widget.state.error = error.message;
        widget.state.thread = null;
        
        // 显示用户友好的错误消息
        showNotification('无法加载收件人信息', 'error');
    },
    
    refreshThreadData: async (widget) => {
        if (!widget.state.thread) {
            return;
        }
        
        try {
            // 强制刷新线程数据
            const refreshedThread = await widget.Thread.getOrFetch({
                model: widget.state.thread.model,
                id: widget.state.thread.id
            }, { force: true });
            
            widget.state.thread = refreshedThread;
            
            showNotification('收件人信息已刷新', 'success');
        } catch (error) {
            console.error('刷新线程数据失败:', error);
            showNotification('刷新失败，请重试', 'error');
        }
    }
};
```

### 3. 收件人列表增强功能

```javascript
// 收件人列表增强功能
const RecipientListEnhancer = {
    enhanceWidget: (widget) => {
        // 添加搜索功能
        RecipientListEnhancer.addSearchCapability(widget);
        
        // 添加过滤功能
        RecipientListEnhancer.addFilterOptions(widget);
        
        // 添加排序功能
        RecipientListEnhancer.addSortingOptions(widget);
        
        // 添加导出功能
        RecipientListEnhancer.addExportCapability(widget);
    },
    
    addSearchCapability: (widget) => {
        const searchContainer = document.createElement('div');
        searchContainer.className = 'recipient-search-container';
        searchContainer.innerHTML = `
            <div class="search-input-group">
                <input type="text" class="search-input" placeholder="搜索收件人...">
                <button class="search-clear-btn" title="清除搜索">
                    <i class="fa fa-times"></i>
                </button>
            </div>
        `;
        
        const searchInput = searchContainer.querySelector('.search-input');
        const clearBtn = searchContainer.querySelector('.search-clear-btn');
        
        searchInput.addEventListener('input', (event) => {
            const searchTerm = event.target.value;
            RecipientListEnhancer.filterRecipients(widget, searchTerm);
        });
        
        clearBtn.addEventListener('click', () => {
            searchInput.value = '';
            RecipientListEnhancer.filterRecipients(widget, '');
        });
        
        widget.el.prepend(searchContainer);
    },
    
    filterRecipients: (widget, searchTerm) => {
        const recipients = widget.state.thread?.recipients || [];
        
        if (!searchTerm) {
            widget.state.filteredRecipients = recipients;
            return;
        }
        
        const filtered = recipients.filter(recipient => {
            const name = recipient.partner.name.toLowerCase();
            const email = recipient.partner.email?.toLowerCase() || '';
            const term = searchTerm.toLowerCase();
            
            return name.includes(term) || email.includes(term);
        });
        
        widget.state.filteredRecipients = filtered;
    },
    
    addFilterOptions: (widget) => {
        const filterContainer = document.createElement('div');
        filterContainer.className = 'recipient-filter-container';
        filterContainer.innerHTML = `
            <div class="filter-options">
                <select class="filter-select" data-filter="type">
                    <option value="">所有类型</option>
                    <option value="internal">内部用户</option>
                    <option value="external">外部用户</option>
                </select>
                
                <select class="filter-select" data-filter="email">
                    <option value="">所有邮箱状态</option>
                    <option value="with_email">有邮箱</option>
                    <option value="without_email">无邮箱</option>
                </select>
                
                <button class="filter-reset-btn">重置过滤</button>
            </div>
        `;
        
        filterContainer.querySelectorAll('.filter-select').forEach(select => {
            select.addEventListener('change', () => {
                RecipientListEnhancer.applyFilters(widget);
            });
        });
        
        filterContainer.querySelector('.filter-reset-btn').addEventListener('click', () => {
            filterContainer.querySelectorAll('.filter-select').forEach(select => {
                select.value = '';
            });
            RecipientListEnhancer.applyFilters(widget);
        });
        
        widget.el.appendChild(filterContainer);
    },
    
    applyFilters: (widget) => {
        const recipients = widget.state.thread?.recipients || [];
        const filters = {};
        
        widget.el.querySelectorAll('.filter-select').forEach(select => {
            const filterType = select.dataset.filter;
            const filterValue = select.value;
            if (filterValue) {
                filters[filterType] = filterValue;
            }
        });
        
        let filtered = recipients;
        
        if (filters.type) {
            filtered = filtered.filter(recipient => {
                if (filters.type === 'internal') {
                    return recipient.partner.isInternal;
                } else if (filters.type === 'external') {
                    return !recipient.partner.isInternal;
                }
                return true;
            });
        }
        
        if (filters.email) {
            filtered = filtered.filter(recipient => {
                const hasEmail = !!recipient.partner.email;
                if (filters.email === 'with_email') {
                    return hasEmail;
                } else if (filters.email === 'without_email') {
                    return !hasEmail;
                }
                return true;
            });
        }
        
        widget.state.filteredRecipients = filtered;
    },
    
    addSortingOptions: (widget) => {
        const sortContainer = document.createElement('div');
        sortContainer.className = 'recipient-sort-container';
        sortContainer.innerHTML = `
            <div class="sort-options">
                <label>排序方式:</label>
                <select class="sort-select">
                    <option value="name_asc">姓名 (A-Z)</option>
                    <option value="name_desc">姓名 (Z-A)</option>
                    <option value="email_asc">邮箱 (A-Z)</option>
                    <option value="email_desc">邮箱 (Z-A)</option>
                    <option value="type_asc">类型 (内部优先)</option>
                    <option value="type_desc">类型 (外部优先)</option>
                </select>
            </div>
        `;
        
        const sortSelect = sortContainer.querySelector('.sort-select');
        sortSelect.addEventListener('change', () => {
            RecipientListEnhancer.sortRecipients(widget, sortSelect.value);
        });
        
        widget.el.appendChild(sortContainer);
    },
    
    sortRecipients: (widget, sortOption) => {
        const recipients = widget.state.filteredRecipients || widget.state.thread?.recipients || [];
        
        const sorted = [...recipients].sort((a, b) => {
            switch (sortOption) {
                case 'name_asc':
                    return a.partner.name.localeCompare(b.partner.name);
                case 'name_desc':
                    return b.partner.name.localeCompare(a.partner.name);
                case 'email_asc':
                    return (a.partner.email || '').localeCompare(b.partner.email || '');
                case 'email_desc':
                    return (b.partner.email || '').localeCompare(a.partner.email || '');
                case 'type_asc':
                    return (b.partner.isInternal ? 1 : 0) - (a.partner.isInternal ? 1 : 0);
                case 'type_desc':
                    return (a.partner.isInternal ? 1 : 0) - (b.partner.isInternal ? 1 : 0);
                default:
                    return 0;
            }
        });
        
        widget.state.sortedRecipients = sorted;
    },
    
    addExportCapability: (widget) => {
        const exportContainer = document.createElement('div');
        exportContainer.className = 'recipient-export-container';
        exportContainer.innerHTML = `
            <div class="export-options">
                <button class="export-btn" data-format="csv">导出CSV</button>
                <button class="export-btn" data-format="json">导出JSON</button>
                <button class="export-btn" data-format="excel">导出Excel</button>
            </div>
        `;
        
        exportContainer.querySelectorAll('.export-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const format = btn.dataset.format;
                RecipientListEnhancer.exportRecipients(widget, format);
            });
        });
        
        widget.el.appendChild(exportContainer);
    },
    
    exportRecipients: (widget, format) => {
        const recipients = widget.state.sortedRecipients || 
                          widget.state.filteredRecipients || 
                          widget.state.thread?.recipients || [];
        
        switch (format) {
            case 'csv':
                RecipientListEnhancer.exportToCSV(recipients);
                break;
            case 'json':
                RecipientListEnhancer.exportToJSON(recipients);
                break;
            case 'excel':
                RecipientListEnhancer.exportToExcel(recipients);
                break;
        }
    },
    
    exportToCSV: (recipients) => {
        const headers = ['姓名', '邮箱', '类型', '状态'];
        const rows = recipients.map(recipient => [
            recipient.partner.name,
            recipient.partner.email || '',
            recipient.partner.isInternal ? '内部' : '外部',
            recipient.partner.active ? '活跃' : '停用'
        ]);
        
        const csvContent = [headers, ...rows]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');
        
        RecipientListEnhancer.downloadFile(csvContent, 'recipients.csv', 'text/csv');
    },
    
    exportToJSON: (recipients) => {
        const data = recipients.map(recipient => ({
            name: recipient.partner.name,
            email: recipient.partner.email,
            isInternal: recipient.partner.isInternal,
            active: recipient.partner.active
        }));
        
        const jsonContent = JSON.stringify(data, null, 2);
        RecipientListEnhancer.downloadFile(jsonContent, 'recipients.json', 'application/json');
    },
    
    downloadFile: (content, filename, mimeType) => {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();
        
        URL.revokeObjectURL(url);
    }
};
```

### 4. 线程ID字段类型处理

```javascript
// 线程ID字段类型处理器
const ThreadIdFieldHandler = {
    parseThreadId: (record, threadIdField) => {
        const fieldType = record.fields[threadIdField].type;
        const fieldValue = record.data[threadIdField];
        
        switch (fieldType) {
            case 'text':
                return ThreadIdFieldHandler.parseTextType(fieldValue);
            case 'many2one_reference':
                return ThreadIdFieldHandler.parseMany2OneReference(fieldValue);
            case 'integer':
                return ThreadIdFieldHandler.parseIntegerType(fieldValue);
            case 'char':
                return ThreadIdFieldHandler.parseCharType(fieldValue);
            default:
                throw new Error(`不支持的线程ID字段类型: ${fieldType}`);
        }
    },
    
    parseTextType: (value) => {
        try {
            // 邮件编写器将ID存储为表示数组的字符串
            const parsed = JSON.parse(value);
            if (Array.isArray(parsed) && parsed.length > 0) {
                return parsed[0];
            }
            throw new Error('文本字段值不是有效的数组格式');
        } catch (error) {
            console.error('解析文本类型线程ID失败:', error);
            throw new Error('无法解析文本类型的线程ID');
        }
    },
    
    parseMany2OneReference: (value) => {
        if (value && typeof value === 'object' && value.resId) {
            return value.resId;
        }
        throw new Error('many2one_reference字段值无效');
    },
    
    parseIntegerType: (value) => {
        if (typeof value === 'number' && value > 0) {
            return value;
        }
        throw new Error('整数类型线程ID无效');
    },
    
    parseCharType: (value) => {
        if (typeof value === 'string' && value.trim()) {
            // 尝试解析为数字
            const numValue = parseInt(value, 10);
            if (!isNaN(numValue)) {
                return numValue;
            }
            return value.trim();
        }
        throw new Error('字符类型线程ID无效');
    },
    
    validateThreadId: (threadId) => {
        if (threadId === null || threadId === undefined) {
            return { isValid: false, error: '线程ID不能为空' };
        }
        
        if (typeof threadId !== 'number' && typeof threadId !== 'string') {
            return { isValid: false, error: '线程ID类型无效' };
        }
        
        if (typeof threadId === 'string' && threadId.trim() === '') {
            return { isValid: false, error: '线程ID不能为空字符串' };
        }
        
        if (typeof threadId === 'number' && threadId <= 0) {
            return { isValid: false, error: '线程ID必须大于0' };
        }
        
        return { isValid: true };
    }
};
```

### 5. 错误处理和恢复

```javascript
// 错误处理和恢复机制
const ErrorHandlingManager = {
    handleThreadLoadError: (widget, error) => {
        console.error('加载线程失败:', error);
        
        // 设置错误状态
        widget.state.error = error.message;
        widget.state.thread = null;
        
        // 显示错误信息
        ErrorHandlingManager.showErrorMessage(widget, error);
        
        // 尝试恢复
        ErrorHandlingManager.attemptRecovery(widget);
    },
    
    showErrorMessage: (widget, error) => {
        const errorContainer = document.createElement('div');
        errorContainer.className = 'recipient-list-error';
        errorContainer.innerHTML = `
            <div class="error-content">
                <i class="fa fa-exclamation-triangle"></i>
                <span class="error-message">无法加载收件人信息</span>
                <button class="retry-btn">重试</button>
            </div>
        `;
        
        const retryBtn = errorContainer.querySelector('.retry-btn');
        retryBtn.addEventListener('click', () => {
            ErrorHandlingManager.retryLoad(widget);
            errorContainer.remove();
        });
        
        widget.el.appendChild(errorContainer);
    },
    
    attemptRecovery: (widget) => {
        // 延迟重试
        setTimeout(() => {
            ErrorHandlingManager.retryLoad(widget);
        }, 3000);
    },
    
    retryLoad: async (widget) => {
        try {
            // 重新解析线程ID
            const threadId = ThreadIdFieldHandler.parseThreadId(
                widget.props.record,
                widget.props.thread_id_field
            );
            
            // 重新获取线程
            const thread = await widget.Thread.getOrFetch({
                model: widget.props.record.data[widget.props.thread_model_field],
                id: threadId,
            });
            
            // 更新状态
            widget.state.thread = thread;
            widget.state.error = null;
            
            // 移除错误显示
            const errorContainer = widget.el.querySelector('.recipient-list-error');
            if (errorContainer) {
                errorContainer.remove();
            }
            
            showNotification('收件人信息已加载', 'success');
        } catch (error) {
            console.error('重试加载失败:', error);
            showNotification('重试失败，请检查数据配置', 'error');
        }
    }
};
```

## 性能优化

### 1. 数据缓存机制

```javascript
// 数据缓存机制
const DataCacheManager = {
    cache: new Map(),
    
    getCachedThread: (model, id) => {
        const key = `${model}_${id}`;
        const cached = DataCacheManager.cache.get(key);
        
        if (cached && Date.now() - cached.timestamp < 300000) { // 5分钟缓存
            return cached.thread;
        }
        
        return null;
    },
    
    setCachedThread: (model, id, thread) => {
        const key = `${model}_${id}`;
        DataCacheManager.cache.set(key, {
            thread,
            timestamp: Date.now()
        });
        
        // 限制缓存大小
        if (DataCacheManager.cache.size > 100) {
            const firstKey = DataCacheManager.cache.keys().next().value;
            DataCacheManager.cache.delete(firstKey);
        }
    },
    
    invalidateCache: (model, id) => {
        if (model && id) {
            const key = `${model}_${id}`;
            DataCacheManager.cache.delete(key);
        } else {
            DataCacheManager.cache.clear();
        }
    }
};
```

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的收件人列表小部件
- 清晰的组件层次结构

### 2. 策略模式 (Strategy Pattern)
- 不同字段类型的不同解析策略
- 可配置的数据获取策略

### 3. 适配器模式 (Adapter Pattern)
- 适配不同的线程ID字段类型
- 统一的数据访问接口

## 注意事项

1. **字段类型支持**: 确保支持所有可能的线程ID字段类型
2. **错误处理**: 提供完善的错误处理和恢复机制
3. **性能考虑**: 避免重复的数据获取操作
4. **数据一致性**: 确保显示的收件人信息是最新的

## 扩展建议

1. **更多字段类型**: 支持更多的线程ID字段类型
2. **实时更新**: 支持收件人信息的实时更新
3. **批量操作**: 支持批量收件人操作
4. **权限控制**: 根据用户权限显示不同的收件人信息
5. **国际化**: 支持多语言的收件人信息显示

该小部件为邮件编写提供了完整的收件人信息展示功能，是邮件系统的重要组成部分。

# Messaging Menu Patch - 消息菜单补丁

## 概述

`messaging_menu_patch.js` 实现了 Odoo 邮件系统中消息菜单的Web环境补丁，用于扩展消息菜单在Web环境下的功能。该补丁添加了快速搜索、PWA安装提示、桌面通知权限请求、失败消息处理、线程打开等功能，增强了消息菜单的交互体验和功能完整性，是消息菜单在Web环境下的重要功能扩展。

## 文件信息
- **路径**: `/mail/static/src/core/web/messaging_menu_patch.js`
- **行数**: 193
- **模块**: `@mail/core/web/messaging_menu_patch`

## 依赖关系

```javascript
// UI组件依赖
'@mail/core/web/messaging_menu_quick_search'  // 快速搜索组件

// 核心依赖
'@mail/core/public_web/messaging_menu'       // 基础消息菜单
'@mail/utils/common/hooks'                   // 邮件工具钩子
'@odoo/owl'                                 // OWL 框架
'@web/core/l10n/translation'                // 国际化
'@web/core/utils/hooks'                     // Web 核心钩子
'@web/core/utils/patch'                     // 补丁工具
'@web/core/browser/feature_detection'       // 浏览器特性检测
```

## 补丁实现

### 组件扩展

```javascript
// 添加快速搜索组件
Object.assign(MessagingMenu.components, { MessagingMenuQuickSearch });

// 应用补丁
patch(MessagingMenu.prototype, {
    // 补丁方法...
});
```

## 核心功能

### 1. 设置扩展

```javascript
setup() {
    super.setup();
    this.action = useService("action");
    this.pwa = useState(useService("pwa"));
    this.notification = useState(useService("mail.notification.permission"));
    
    Object.assign(this.state, {
        searchOpen: false,
    });

    onExternalClick("selector", () => Object.assign(this.state, { adding: false }));
    
    // 搜索词变化效果
    useEffect(() => {
        if (this.store.discuss.searchTerm && 
            this.lastSearchTerm !== this.store.discuss.searchTerm && 
            this.state.activeIndex) {
            this.state.activeIndex = 0;
        }
        if (!this.store.discuss.searchTerm) {
            this.state.activeIndex = null;
        }
        this.lastSearchTerm = this.store.discuss.searchTerm;
    }, () => [this.store.discuss.searchTerm]);
    
    // 下拉框状态效果
    useEffect(() => {
        if (!this.dropdown.isOpen) {
            this.state.activeIndex = null;
        }
    }, () => [this.dropdown.isOpen]);
}
```

**设置扩展内容**:
- 动作服务用于打开页面
- PWA服务状态管理
- 通知权限服务状态
- 搜索状态管理
- 外部点击处理
- 搜索词和下拉框状态的响应式效果

### 2. 打开前处理

```javascript
beforeOpen() {
    this.state.searchOpen = false;
    this.store.discuss.searchTerm = "";
    this.store.isReady.then(() => {
        if (!this.store.inbox.isLoaded &&
            this.store.inbox.status !== "loading" &&
            this.store.inbox.counter !== this.store.inbox.messages.length) {
            this.store.inbox.fetchNewMessages();
        }
    });
}
```

**打开前处理**:
- 重置搜索状态
- 清空搜索词
- 检查收件箱状态并获取新消息

### 3. PWA安装提示

```javascript
get canPromptToInstall() {
    return this.pwa.canPromptToInstall;
}

get installationRequest() {
    return {
        body: _t("Come here often? Install Odoo on your device!"),
        displayName: _t("%s has a suggestion", this.store.odoobot.name),
        onClick: () => {
            this.pwa.show();
        },
        iconSrc: this.store.odoobot.avatarUrl,
        partner: this.store.odoobot,
        isShown: this.store.discuss.activeTab === "main" && this.canPromptToInstall,
    };
}
```

**PWA安装功能**:
- 检查是否可以提示安装
- 创建安装请求对象
- 使用Odoobot作为提示发送者
- 点击时显示PWA安装提示

### 4. 桌面通知权限

```javascript
get shouldAskPushPermission() {
    return this.notification.permission === "prompt" && !isIOS();
}

get notificationRequest() {
    return {
        body: _t("Enable desktop notifications to chat"),
        displayName: _t("%s has a request", this.store.odoobot.name),
        iconSrc: this.store.odoobot.avatarUrl,
        partner: this.store.odoobot,
        isShown: this.store.discuss.activeTab === "main" && this.shouldAskPushPermission,
    };
}
```

**通知权限功能**:
- 检查是否需要请求通知权限（排除iOS）
- 创建通知权限请求对象
- 在主标签页显示权限请求

### 5. 标签页配置

```javascript
get tabs() {
    return [
        {
            icon: this.env.inDiscussApp ? "fa fa-inbox" : "fa fa-envelope",
            id: "main",
            label: this.env.inDiscussApp ? _t("Mailboxes") : _t("All"),
        },
        ...super.tabs,
    ];
}
```

**标签页扩展**:
- 根据应用环境显示不同的图标和标签
- 讨论应用中显示"收件箱"，其他环境显示"全部"

### 6. 失败消息处理

```javascript
onClickFailure(failure) {
    const threadIds = new Set(failure.notifications.map(({ message }) => message.thread.id));
    if (threadIds.size === 1) {
        const message = failure.notifications[0].message;
        this.openThread(message.thread);
    } else {
        this.openFailureView(failure);
        this.dropdown.close();
    }
}

openFailureView(failure) {
    if (failure.type !== "email") {
        return;
    }
    this.action.doAction({
        name: _t("Mail Failures"),
        type: "ir.actions.act_window",
        view_mode: "kanban,list,form",
        views: [[false, "kanban"], [false, "list"], [false, "form"]],
        target: "current",
        res_model: failure.resModel,
        domain: [["message_has_error", "=", true]],
        context: { create: false },
    });
}

cancelNotifications(failure) {
    return this.env.services.orm.call(failure.resModel, "notify_cancel_by_type", [], {
        notification_type: failure.type,
    });
}
```

**失败消息处理**:
- 单个线程失败时直接打开线程
- 多个线程失败时打开失败视图
- 支持取消特定类型的通知

### 7. 线程打开

```javascript
openThread(thread) {
    if (this.store.discuss.isActive) {
        this.action.doAction({
            type: "ir.actions.act_window",
            res_model: thread.model,
            views: [[false, "form"]],
            res_id: thread.id,
        });
        // 关闭相关的聊天窗口，避免同时显示表单视图和聊天窗口
        this.store.ChatWindow.get({ thread })?.close();
    } else {
        thread.open({ fromMessagingMenu: true });
    }
    this.dropdown.close();
}
```

**线程打开逻辑**:
- 讨论应用中打开表单视图
- 其他环境中打开线程
- 关闭相关聊天窗口避免冲突

### 8. 搜索功能

```javascript
toggleSearch() {
    this.store.discuss.searchTerm = "";
    this.state.searchOpen = !this.state.searchOpen;
}
```

**搜索切换**:
- 清空搜索词
- 切换搜索状态

### 9. 计数器

```javascript
get counter() {
    let value = this.store.inbox.counter +
                this.store.failures.reduce((acc, f) => acc + parseInt(f.notifications.length), 0);
    if (this.canPromptToInstall) {
        value++;
    }
    if (this.shouldAskPushPermission) {
        value++;
    }
    return value;
}
```

**计数器计算**:
- 收件箱计数
- 失败消息计数
- PWA安装提示计数
- 通知权限请求计数

### 10. 预览检查

```javascript
get hasPreviews() {
    return (
        this.threads.length > 0 ||
        (this.store.failures.length > 0 &&
            this.store.discuss.activeTab === "main" &&
            !this.env.inDiscussApp) ||
        (this.shouldAskPushPermission &&
            this.store.discuss.activeTab === "main" &&
            !this.env.inDiscussApp) ||
        (this.canPromptToInstall &&
            this.store.discuss.activeTab === "main" &&
            !this.env.inDiscussApp)
    );
}
```

**预览条件**:
- 有线程存在
- 有失败消息（主标签页，非讨论应用）
- 需要通知权限（主标签页，非讨论应用）
- 可以安装PWA（主标签页，非讨论应用）

## 使用场景

### 1. Web环境消息菜单

```javascript
// Web环境下的消息菜单使用
const WebMessagingMenu = () => {
    return (
        <div class="web-messaging-menu">
            {/* 消息菜单会自动应用补丁功能 */}
            <MessagingMenu />
        </div>
    );
};
```

### 2. PWA安装管理

```javascript
// PWA安装管理
const PWAInstallationManager = ({ messagingMenu }) => {
    const handleInstallPrompt = () => {
        if (messagingMenu.canPromptToInstall) {
            // 显示安装提示
            messagingMenu.pwa.show();
            
            // 记录安装尝试
            logPWAInstallAttempt();
        }
    };
    
    const checkInstallationStatus = () => {
        return {
            canInstall: messagingMenu.canPromptToInstall,
            isInstalled: messagingMenu.pwa.isInstalled,
            isStandalone: messagingMenu.pwa.isStandalone
        };
    };
    
    return {
        handleInstallPrompt,
        checkInstallationStatus
    };
};
```

### 3. 通知权限管理

```javascript
// 通知权限管理
const NotificationPermissionManager = ({ messagingMenu }) => {
    const requestNotificationPermission = async () => {
        try {
            const permission = await Notification.requestPermission();
            
            if (permission === 'granted') {
                showNotification('桌面通知已启用', 'success');
                updateNotificationSettings(true);
            } else {
                showNotification('桌面通知被拒绝', 'warning');
                updateNotificationSettings(false);
            }
            
            return permission;
        } catch (error) {
            console.error('请求通知权限失败:', error);
            return 'denied';
        }
    };
    
    const checkNotificationSupport = () => {
        return {
            isSupported: 'Notification' in window,
            permission: messagingMenu.notification.permission,
            shouldAsk: messagingMenu.shouldAskPushPermission
        };
    };
    
    return {
        requestNotificationPermission,
        checkNotificationSupport
    };
};
```

### 4. 失败消息处理

```javascript
// 失败消息处理
const FailureMessageHandler = ({ messagingMenu }) => {
    const handleFailureClick = (failure) => {
        // 记录失败点击
        logFailureClick(failure);
        
        // 调用补丁方法
        messagingMenu.onClickFailure(failure);
        
        // 更新失败统计
        updateFailureStats(failure);
    };
    
    const batchCancelFailures = async (failures) => {
        const promises = failures.map(failure => 
            messagingMenu.cancelNotifications(failure)
        );
        
        try {
            await Promise.all(promises);
            showNotification('批量取消成功', 'success');
            refreshFailureList();
        } catch (error) {
            console.error('批量取消失败:', error);
            showNotification('批量取消失败', 'error');
        }
    };
    
    return {
        handleFailureClick,
        batchCancelFailures
    };
};
```

### 5. 搜索功能集成

```javascript
// 搜索功能集成
const SearchIntegration = ({ messagingMenu }) => {
    const performSearch = (searchTerm) => {
        // 设置搜索词
        messagingMenu.store.discuss.searchTerm = searchTerm;
        
        // 打开搜索
        if (!messagingMenu.state.searchOpen) {
            messagingMenu.toggleSearch();
        }
        
        // 记录搜索
        logSearchAction(searchTerm);
    };
    
    const clearSearch = () => {
        messagingMenu.store.discuss.searchTerm = "";
        messagingMenu.state.searchOpen = false;
    };
    
    const getSearchSuggestions = (term) => {
        // 基于历史搜索和线程数据提供建议
        const suggestions = [];
        
        // 添加线程建议
        messagingMenu.threads.forEach(thread => {
            if (thread.displayName.toLowerCase().includes(term.toLowerCase())) {
                suggestions.push({
                    type: 'thread',
                    text: thread.displayName,
                    thread
                });
            }
        });
        
        return suggestions;
    };
    
    return {
        performSearch,
        clearSearch,
        getSearchSuggestions
    };
};
```

## 状态管理

### 1. 搜索状态

```javascript
// 搜索状态管理
const SearchStateManager = {
    state: {
        searchOpen: false,
        searchTerm: "",
        activeIndex: null,
        lastSearchTerm: ""
    },
    
    openSearch: () => {
        SearchStateManager.state.searchOpen = true;
    },
    
    closeSearch: () => {
        SearchStateManager.state.searchOpen = false;
        SearchStateManager.state.searchTerm = "";
        SearchStateManager.state.activeIndex = null;
    },
    
    setSearchTerm: (term) => {
        SearchStateManager.state.searchTerm = term;
    },
    
    setActiveIndex: (index) => {
        SearchStateManager.state.activeIndex = index;
    }
};
```

### 2. 通知状态

```javascript
// 通知状态管理
const NotificationStateManager = {
    checkPermission: () => {
        if (!('Notification' in window)) {
            return 'unsupported';
        }
        return Notification.permission;
    },
    
    shouldShowPermissionRequest: () => {
        const permission = NotificationStateManager.checkPermission();
        return permission === 'default' && !isIOS();
    },
    
    requestPermission: async () => {
        if (!('Notification' in window)) {
            return 'unsupported';
        }
        
        try {
            return await Notification.requestPermission();
        } catch (error) {
            console.error('请求通知权限失败:', error);
            return 'denied';
        }
    }
};
```

## 性能优化

### 1. 状态缓存

```javascript
// 状态缓存优化
const StateCache = {
    cache: new Map(),
    
    get: (key, computeFn) => {
        if (StateCache.cache.has(key)) {
            const cached = StateCache.cache.get(key);
            if (Date.now() - cached.timestamp < 5000) { // 5秒缓存
                return cached.value;
            }
        }
        
        const value = computeFn();
        StateCache.cache.set(key, {
            value,
            timestamp: Date.now()
        });
        
        return value;
    },
    
    clear: () => {
        StateCache.cache.clear();
    }
};
```

### 2. 懒加载优化

```javascript
// 懒加载优化
const LazyLoadManager = {
    loadInboxMessages: debounce(async (store) => {
        if (store.inbox.status === 'loading') {
            return;
        }
        
        try {
            await store.inbox.fetchNewMessages();
        } catch (error) {
            console.error('加载收件箱消息失败:', error);
        }
    }, 1000),
    
    loadFailures: debounce(async (store) => {
        try {
            await store.fetchFailures();
        } catch (error) {
            console.error('加载失败消息失败:', error);
        }
    }, 1000)
};
```

## 错误处理

### 1. 动作执行错误

```javascript
// 安全的动作执行
const safeDoAction = async (actionService, action, options = {}) => {
    try {
        return await actionService.doAction(action, options);
    } catch (error) {
        console.error('执行动作失败:', error);
        showNotification('操作失败，请重试', 'error');
        throw error;
    }
};
```

### 2. 服务调用错误

```javascript
// 安全的服务调用
const safeServiceCall = async (service, method, ...args) => {
    try {
        return await service[method](...args);
    } catch (error) {
        console.error(`服务调用失败: ${method}`, error);
        
        if (error.message.includes('network')) {
            showNotification('网络连接错误', 'error');
        } else {
            showNotification('服务调用失败', 'error');
        }
        
        throw error;
    }
};
```

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 非侵入式功能扩展
- 保持原有功能的完整性

### 2. 观察者模式 (Observer Pattern)
- 监听状态变化
- 响应用户交互

### 3. 策略模式 (Strategy Pattern)
- 不同环境下的不同行为策略
- 可配置的功能开关

## 注意事项

1. **浏览器兼容性**: 处理不同浏览器的特性差异
2. **权限管理**: 正确处理通知和PWA权限
3. **性能考虑**: 避免频繁的状态更新
4. **用户体验**: 提供清晰的操作反馈

## 扩展建议

1. **高级搜索**: 支持更复杂的搜索语法
2. **自定义标签**: 支持用户自定义标签页
3. **快捷键**: 添加键盘快捷键支持
4. **离线支持**: 支持离线状态下的基本功能
5. **主题定制**: 支持消息菜单的主题定制

该补丁为消息菜单在Web环境下提供了重要的功能扩展，增强了用户体验和功能完整性。

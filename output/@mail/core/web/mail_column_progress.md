# Mail Column Progress - 邮件列进度

## 概述

`mail_column_progress.js` 实现了 Odoo 邮件系统中的邮件列进度组件，用于在列表视图中显示进度条。该组件继承自ColumnProgress，专门为邮件相关的数据展示定制了进度显示功能，支持聚合数据的进度可视化，使用自定义模板进行渲染，为用户提供了直观的数据进度展示，是邮件系统中数据可视化的重要组件。

## 文件信息
- **路径**: `/mail/static/src/core/web/mail_column_progress.js`
- **行数**: 19
- **模块**: `@mail/core/web/mail_column_progress`

## 依赖关系

```javascript
// 核心依赖
'@web/views/view_components/column_progress'  // 基础列进度组件
```

## 组件定义

### MailColumnProgress 类

```javascript
class MailColumnProgress extends ColumnProgress {
    static props = {
        ...ColumnProgress.props,
        aggregateOn: { type: Object, optional: true },
    };
    static template = "mail.ColumnProgress";
}
```

**组件特性**:
- 继承自ColumnProgress
- 扩展了aggregateOn属性
- 使用自定义模板"mail.ColumnProgress"

## Props 配置

### Props 详细说明

- **继承的Props**: 继承ColumnProgress的所有属性
- **`aggregateOn`** (可选):
  - 类型: 对象
  - 用途: 指定聚合数据的配置
  - 功能: 控制进度条的聚合计算方式

## 使用场景

### 1. 邮件活动进度显示

```javascript
// 邮件活动进度显示
const MailActivityProgress = {
    renderActivityProgress: (activities) => {
        const progressData = MailActivityProgress.calculateProgress(activities);
        
        return {
            value: progressData.completed,
            max: progressData.total,
            aggregateOn: {
                field: 'state',
                values: {
                    'done': { label: '已完成', color: '#28a745' },
                    'planned': { label: '计划中', color: '#ffc107' },
                    'overdue': { label: '逾期', color: '#dc3545' }
                }
            }
        };
    },
    
    calculateProgress: (activities) => {
        const total = activities.length;
        const completed = activities.filter(a => a.state === 'done').length;
        const planned = activities.filter(a => a.state === 'planned').length;
        const overdue = activities.filter(a => a.state === 'overdue').length;
        
        return {
            total,
            completed,
            planned,
            overdue,
            completionRate: total > 0 ? (completed / total) * 100 : 0
        };
    },
    
    getProgressColor: (completionRate) => {
        if (completionRate >= 80) return '#28a745'; // 绿色
        if (completionRate >= 60) return '#ffc107'; // 黄色
        if (completionRate >= 40) return '#fd7e14'; // 橙色
        return '#dc3545'; // 红色
    }
};
```

### 2. 邮件发送状态进度

```javascript
// 邮件发送状态进度
const MailSendingProgress = {
    renderSendingProgress: (emails) => {
        const statusData = MailSendingProgress.analyzeEmailStatus(emails);
        
        return {
            value: statusData.sent,
            max: statusData.total,
            aggregateOn: {
                field: 'state',
                values: {
                    'sent': { label: '已发送', color: '#28a745' },
                    'pending': { label: '待发送', color: '#17a2b8' },
                    'failed': { label: '发送失败', color: '#dc3545' },
                    'draft': { label: '草稿', color: '#6c757d' }
                }
            }
        };
    },
    
    analyzeEmailStatus: (emails) => {
        const statusCount = emails.reduce((acc, email) => {
            acc[email.state] = (acc[email.state] || 0) + 1;
            return acc;
        }, {});
        
        return {
            total: emails.length,
            sent: statusCount.sent || 0,
            pending: statusCount.pending || 0,
            failed: statusCount.failed || 0,
            draft: statusCount.draft || 0,
            successRate: emails.length > 0 ? 
                ((statusCount.sent || 0) / emails.length) * 100 : 0
        };
    },
    
    getStatusIcon: (status) => {
        const icons = {
            'sent': 'fa-check-circle',
            'pending': 'fa-clock-o',
            'failed': 'fa-exclamation-triangle',
            'draft': 'fa-edit'
        };
        return icons[status] || 'fa-question-circle';
    }
};
```

### 3. 关注者参与度进度

```javascript
// 关注者参与度进度
const FollowerEngagementProgress = {
    renderEngagementProgress: (followers, thread) => {
        const engagementData = FollowerEngagementProgress.calculateEngagement(
            followers, 
            thread
        );
        
        return {
            value: engagementData.active,
            max: engagementData.total,
            aggregateOn: {
                field: 'engagement_level',
                values: {
                    'high': { label: '高参与度', color: '#28a745' },
                    'medium': { label: '中等参与度', color: '#ffc107' },
                    'low': { label: '低参与度', color: '#fd7e14' },
                    'inactive': { label: '不活跃', color: '#6c757d' }
                }
            }
        };
    },
    
    calculateEngagement: (followers, thread) => {
        const engagementLevels = followers.map(follower => {
            const recentActivity = FollowerEngagementProgress.getRecentActivity(
                follower, 
                thread
            );
            
            return {
                follower,
                level: FollowerEngagementProgress.determineEngagementLevel(recentActivity)
            };
        });
        
        const levelCounts = engagementLevels.reduce((acc, item) => {
            acc[item.level] = (acc[item.level] || 0) + 1;
            return acc;
        }, {});
        
        return {
            total: followers.length,
            active: (levelCounts.high || 0) + (levelCounts.medium || 0),
            high: levelCounts.high || 0,
            medium: levelCounts.medium || 0,
            low: levelCounts.low || 0,
            inactive: levelCounts.inactive || 0
        };
    },
    
    getRecentActivity: (follower, thread) => {
        const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
        
        return {
            messages: thread.messages.filter(m => 
                m.author.id === follower.partner.id && 
                m.date > thirtyDaysAgo
            ).length,
            reactions: thread.reactions.filter(r => 
                r.partner.id === follower.partner.id && 
                r.date > thirtyDaysAgo
            ).length,
            lastSeen: follower.lastSeen
        };
    },
    
    determineEngagementLevel: (activity) => {
        const score = activity.messages * 3 + activity.reactions * 1;
        
        if (score >= 10) return 'high';
        if (score >= 5) return 'medium';
        if (score >= 1) return 'low';
        return 'inactive';
    }
};
```

### 4. 邮件模板使用统计

```javascript
// 邮件模板使用统计
const TemplateUsageProgress = {
    renderUsageProgress: (templates, period = 30) => {
        const usageData = TemplateUsageProgress.analyzeUsage(templates, period);
        
        return {
            value: usageData.used,
            max: usageData.total,
            aggregateOn: {
                field: 'usage_frequency',
                values: {
                    'frequent': { label: '频繁使用', color: '#28a745' },
                    'moderate': { label: '适度使用', color: '#ffc107' },
                    'rare': { label: '很少使用', color: '#fd7e14' },
                    'unused': { label: '未使用', color: '#6c757d' }
                }
            }
        };
    },
    
    analyzeUsage: (templates, period) => {
        const periodStart = Date.now() - (period * 24 * 60 * 60 * 1000);
        
        const usageStats = templates.map(template => {
            const recentUsage = template.usageHistory.filter(
                usage => usage.date > periodStart
            ).length;
            
            return {
                template,
                usageCount: recentUsage,
                frequency: TemplateUsageProgress.categorizeFrequency(recentUsage, period)
            };
        });
        
        const frequencyCounts = usageStats.reduce((acc, stat) => {
            acc[stat.frequency] = (acc[stat.frequency] || 0) + 1;
            return acc;
        }, {});
        
        return {
            total: templates.length,
            used: usageStats.filter(s => s.usageCount > 0).length,
            frequent: frequencyCounts.frequent || 0,
            moderate: frequencyCounts.moderate || 0,
            rare: frequencyCounts.rare || 0,
            unused: frequencyCounts.unused || 0
        };
    },
    
    categorizeFrequency: (usageCount, period) => {
        const dailyAverage = usageCount / period;
        
        if (dailyAverage >= 1) return 'frequent';
        if (dailyAverage >= 0.5) return 'moderate';
        if (usageCount > 0) return 'rare';
        return 'unused';
    }
};
```

### 5. 响应式进度显示

```javascript
// 响应式进度显示
const ResponsiveProgressDisplay = {
    adaptProgressToScreen: (progressData, screenSize) => {
        const adaptedData = { ...progressData };
        
        switch (screenSize) {
            case 'mobile':
                adaptedData.showLabels = false;
                adaptedData.showPercentage = true;
                adaptedData.height = 20;
                break;
                
            case 'tablet':
                adaptedData.showLabels = true;
                adaptedData.showPercentage = true;
                adaptedData.height = 25;
                break;
                
            case 'desktop':
                adaptedData.showLabels = true;
                adaptedData.showPercentage = true;
                adaptedData.showTooltip = true;
                adaptedData.height = 30;
                break;
        }
        
        return adaptedData;
    },
    
    getScreenSize: () => {
        const width = window.innerWidth;
        
        if (width < 768) return 'mobile';
        if (width < 1024) return 'tablet';
        return 'desktop';
    },
    
    setupResponsiveListener: (progressComponent) => {
        window.addEventListener('resize', () => {
            const newScreenSize = ResponsiveProgressDisplay.getScreenSize();
            const adaptedData = ResponsiveProgressDisplay.adaptProgressToScreen(
                progressComponent.props,
                newScreenSize
            );
            
            progressComponent.updateProps(adaptedData);
        });
    }
};
```

### 6. 进度动画效果

```javascript
// 进度动画效果
const ProgressAnimationEffects = {
    animateProgress: (element, fromValue, toValue, duration = 1000) => {
        const startTime = Date.now();
        const valueRange = toValue - fromValue;
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用缓动函数
            const easedProgress = ProgressAnimationEffects.easeOutCubic(progress);
            const currentValue = fromValue + (valueRange * easedProgress);
            
            // 更新进度条
            ProgressAnimationEffects.updateProgressBar(element, currentValue);
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    },
    
    easeOutCubic: (t) => {
        return 1 - Math.pow(1 - t, 3);
    },
    
    updateProgressBar: (element, value) => {
        const progressBar = element.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.style.width = `${value}%`;
            progressBar.setAttribute('aria-valuenow', value);
        }
        
        const progressText = element.querySelector('.progress-text');
        if (progressText) {
            progressText.textContent = `${Math.round(value)}%`;
        }
    },
    
    addPulseEffect: (element) => {
        element.classList.add('progress-pulse');
        
        setTimeout(() => {
            element.classList.remove('progress-pulse');
        }, 2000);
    },
    
    addGlowEffect: (element, color = '#007bff') => {
        element.style.boxShadow = `0 0 10px ${color}`;
        
        setTimeout(() => {
            element.style.boxShadow = '';
        }, 1500);
    }
};
```

## 聚合数据处理

### 1. 数据聚合器

```javascript
// 数据聚合器
const DataAggregator = {
    aggregateByField: (data, field, aggregateOn) => {
        const groups = DataAggregator.groupByField(data, field);
        const aggregated = {};
        
        Object.entries(groups).forEach(([key, items]) => {
            aggregated[key] = {
                count: items.length,
                percentage: (items.length / data.length) * 100,
                items: items,
                config: aggregateOn.values[key] || { label: key, color: '#6c757d' }
            };
        });
        
        return aggregated;
    },
    
    groupByField: (data, field) => {
        return data.reduce((groups, item) => {
            const value = DataAggregator.getNestedValue(item, field);
            const key = value || 'unknown';
            
            if (!groups[key]) {
                groups[key] = [];
            }
            
            groups[key].push(item);
            return groups;
        }, {});
    },
    
    getNestedValue: (obj, path) => {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : null;
        }, obj);
    },
    
    calculateTotals: (aggregatedData) => {
        const total = Object.values(aggregatedData).reduce(
            (sum, group) => sum + group.count, 
            0
        );
        
        const maxGroup = Object.values(aggregatedData).reduce(
            (max, group) => group.count > max.count ? group : max,
            { count: 0 }
        );
        
        return {
            total,
            maxCount: maxGroup.count,
            maxPercentage: maxGroup.percentage
        };
    }
};
```

### 2. 进度计算器

```javascript
// 进度计算器
const ProgressCalculator = {
    calculateProgress: (current, total, options = {}) => {
        const percentage = total > 0 ? (current / total) * 100 : 0;
        
        return {
            current,
            total,
            percentage: Math.round(percentage * 100) / 100,
            remaining: total - current,
            remainingPercentage: 100 - percentage,
            status: ProgressCalculator.getStatus(percentage, options.thresholds)
        };
    },
    
    getStatus: (percentage, thresholds = {}) => {
        const defaultThresholds = {
            excellent: 90,
            good: 70,
            fair: 50,
            poor: 30
        };
        
        const t = { ...defaultThresholds, ...thresholds };
        
        if (percentage >= t.excellent) return 'excellent';
        if (percentage >= t.good) return 'good';
        if (percentage >= t.fair) return 'fair';
        if (percentage >= t.poor) return 'poor';
        return 'critical';
    },
    
    getStatusColor: (status) => {
        const colors = {
            'excellent': '#28a745',
            'good': '#20c997',
            'fair': '#ffc107',
            'poor': '#fd7e14',
            'critical': '#dc3545'
        };
        
        return colors[status] || '#6c757d';
    }
};
```

## 性能优化

### 1. 数据缓存

```javascript
// 进度数据缓存
const ProgressDataCache = {
    cache: new Map(),
    
    getCachedProgress: (key) => {
        const cached = ProgressDataCache.cache.get(key);
        if (cached && Date.now() - cached.timestamp < 60000) { // 1分钟缓存
            return cached.data;
        }
        return null;
    },
    
    setCachedProgress: (key, data) => {
        ProgressDataCache.cache.set(key, {
            data,
            timestamp: Date.now()
        });
        
        // 限制缓存大小
        if (ProgressDataCache.cache.size > 100) {
            const firstKey = ProgressDataCache.cache.keys().next().value;
            ProgressDataCache.cache.delete(firstKey);
        }
    }
};
```

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- 继承ColumnProgress的功能
- 扩展邮件特定的功能

### 2. 策略模式 (Strategy Pattern)
- 不同数据类型的不同聚合策略
- 可配置的进度计算方式

### 3. 装饰器模式 (Decorator Pattern)
- 为基础进度条添加邮件特定的功能
- 不修改原有组件结构

## 注意事项

1. **数据准确性**: 确保进度计算的准确性
2. **性能考虑**: 大数据量时的性能优化
3. **用户体验**: 提供清晰的进度可视化
4. **响应式设计**: 适配不同屏幕尺寸

## 扩展建议

1. **更多图表类型**: 支持饼图、柱状图等其他可视化
2. **实时更新**: 支持进度的实时更新
3. **交互功能**: 添加点击查看详情的交互
4. **导出功能**: 支持进度数据的导出
5. **主题定制**: 支持进度条的主题定制

该组件为邮件系统提供了专业的数据进度可视化功能，是数据展示的重要工具。

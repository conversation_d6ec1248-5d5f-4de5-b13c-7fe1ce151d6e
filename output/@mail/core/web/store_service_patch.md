# Store Service Patch - 存储服务补丁 (Web)

## 概述

`store_service_patch.js` 实现了 Odoo 邮件系统中存储服务在Web环境下的补丁，用于扩展存储服务的功能以支持Web特有的邮件功能。该补丁添加了活动计数器、活动分组、邮箱管理、活动调度、广播通道同步等功能，确保存储服务能够在Web环境下正确支持活动管理、消息处理和多标签页同步，是Web邮件系统的重要基础设施补丁。

## 文件信息
- **路径**: `/mail/static/src/core/web/store_service_patch.js`
- **行数**: 140
- **模块**: `@mail/core/web/store_service_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/record'         // 记录基类
'@mail/core/common/store_service'  // 存储服务基类
'@mail/utils/common/misc'          // 通用工具函数
'@web/core/browser/browser'        // 浏览器工具
'@web/core/l10n/translation'       // 国际化
'@web/core/utils/patch'            // 补丁工具
```

## 补丁实现

### Store 原型补丁

```javascript
const StorePatch = {
    setup() {
        super.setup(...arguments);
        // 活动相关属性
        this.activityCounter = 0;
        this.activity_counter_bus_id = 0;
        this.activityGroups = Record.attr([], { /* 配置 */ });
        
        // 邮箱线程
        this.inbox = Record.one("Thread");
        this.starred = Record.one("Thread");
        this.history = Record.one("Thread");
    },
    
    // 其他方法...
};

patch(Store.prototype, StorePatch);
```

## 核心功能

### 1. 活动计数器管理

```javascript
setup() {
    super.setup(...arguments);
    this.activityCounter = 0;
    this.activity_counter_bus_id = 0;
}
```

**计数器功能**:
- **活动计数器**: 跟踪当前用户的活动总数
- **总线ID**: 防止重复处理相同的总线消息
- **实时更新**: 与服务器状态保持同步

### 2. 活动分组管理

```javascript
this.activityGroups = Record.attr([], {
    onUpdate() {
        this.onUpdateActivityGroups();
    },
    sort(g1, g2) {
        const getSortId = (activityGroup) =>
            activityGroup.model === "mail.activity" ? Number.MAX_VALUE : activityGroup.id;
        return getSortId(g1) - getSortId(g2);
    },
});
```

**分组特性**:
- **自动排序**: 按模型ID升序排列
- **特殊处理**: "mail.activity"模型始终排在最后
- **更新回调**: 分组变化时触发回调

### 3. 邮箱线程初始化

```javascript
onStarted() {
    super.onStarted(...arguments);
    this.inbox = {
        id: "inbox",
        model: "mail.box",
        name: _t("Inbox"),
    };
    this.starred = {
        id: "starred", 
        model: "mail.box",
        name: _t("Starred"),
    };
    this.history = {
        id: "history",
        model: "mail.box", 
        name: _t("History"),
    };
}
```

**邮箱类型**:
- **收件箱**: 需要处理的消息
- **星标**: 用户标记的重要消息
- **历史**: 已处理的消息记录

### 4. 广播通道同步

```javascript
try {
    this.activityBroadcastChannel = new browser.BroadcastChannel("mail.activity.channel");
    this.activityBroadcastChannel.onmessage = this._onActivityBroadcastChannelMessage.bind(this);
} catch {
    this.activityBroadcastChannel = null;
}
```

**同步机制**:
- **多标签页同步**: 在同一浏览器的多个标签页间同步活动数据
- **兼容性处理**: 不支持BroadcastChannel的浏览器会禁用此功能
- **实时通信**: 确保所有标签页的数据一致性

### 5. 消息初始化参数

```javascript
get initMessagingParams() {
    return {
        ...super.initMessagingParams,
        failures: true,
        systray_get_activities: true,
    };
}
```

**初始化参数**:
- **failures**: 启用失败消息处理
- **systray_get_activities**: 获取系统托盘活动数据

### 6. 频道管理

```javascript
getNeedactionChannels() {
    return this.getRecentChannels().filter((channel) => channel.importantCounter > 0);
}

getRecentChannels() {
    return Object.values(this.Thread.records)
        .filter((thread) => thread.model === "discuss.channel")
        .sort((a, b) => compareDatetime(b.lastInterestDt, a.lastInterestDt) || b.id - a.id);
}
```

**频道功能**:
- **需要操作的频道**: 有重要消息的频道
- **最近频道**: 按最后交互时间排序的频道列表

### 7. 活动调度

```javascript
async scheduleActivity(resModel, resIds, defaultActivityTypeId = undefined) {
    const context = {
        active_model: resModel,
        active_ids: resIds,
        active_id: resIds[0],
        ...(defaultActivityTypeId !== undefined
            ? { default_activity_type_id: defaultActivityTypeId }
            : {}),
    };
    
    return new Promise((resolve) =>
        this.env.services.action.doAction({
            type: "ir.actions.act_window",
            name: resIds && resIds.length > 1
                ? _t("Schedule Activity On Selected Records")
                : _t("Schedule Activity"),
            res_model: "mail.activity.schedule",
            view_mode: "form",
            views: [[false, "form"]],
            target: "new",
            context,
        }, { onClose: resolve })
    );
}
```

**调度功能**:
- 支持单个或批量记录的活动调度
- 可指定默认活动类型
- 打开活动调度对话框
- 返回Promise以便后续处理

### 8. 广播消息处理

```javascript
_onActivityBroadcastChannelMessage({ data }) {
    switch (data.type) {
        case "INSERT":
            this.Activity.insert(data.payload, { broadcast: false, html: true });
            break;
        case "DELETE": {
            const activity = this.Activity.insert(data.payload, { broadcast: false });
            activity.remove({ broadcast: false });
            break;
        }
        case "RELOAD_CHATTER": {
            const thread = this.Thread.insert({
                model: data.payload.model,
                id: data.payload.id,
            });
            thread.fetchNewMessages();
            break;
        }
    }
}
```

**消息类型处理**:
- **INSERT**: 插入新活动
- **DELETE**: 删除活动
- **RELOAD_CHATTER**: 重新加载聊天器

### 9. 取消所有星标

```javascript
async unstarAll() {
    // 立即应用更改以获得更快的反馈
    this.store.starred.counter = 0;
    this.store.starred.messages = [];
    await this.env.services.orm.call("mail.message", "unstar_all");
}
```

**取消星标功能**:
- 立即更新本地状态
- 异步调用服务器API
- 提供快速的用户反馈

## 使用场景

### 1. 活动管理

```javascript
// 创建新活动
const createActivity = async (store, recordModel, recordIds, activityTypeId) => {
    await store.scheduleActivity(recordModel, recordIds, activityTypeId);
    
    // 活动创建后会通过广播通道同步到其他标签页
    console.log('活动已创建并同步');
};

// 获取需要处理的活动
const getPendingActivities = (store) => {
    return store.activityGroups.flatMap(group => 
        group.activities.filter(activity => activity.state !== 'done')
    );
};
```

### 2. 邮箱操作

```javascript
// 管理收件箱
const manageInbox = (store) => {
    const inbox = store.inbox;
    
    // 获取未读消息数量
    const unreadCount = inbox.counter;
    
    // 获取收件箱消息
    const messages = inbox.messages;
    
    // 标记所有消息为已读
    const markAllAsRead = async () => {
        await inbox.markAllMessagesAsRead();
    };
    
    return { unreadCount, messages, markAllAsRead };
};

// 管理星标消息
const manageStarred = (store) => {
    const starred = store.starred;
    
    // 取消所有星标
    const unstarAll = async () => {
        await store.unstarAll();
    };
    
    return { starred, unstarAll };
};
```

### 3. 频道管理

```javascript
// 获取重要频道
const getImportantChannels = (store) => {
    const needactionChannels = store.getNeedactionChannels();
    const recentChannels = store.getRecentChannels();
    
    return {
        needaction: needactionChannels,
        recent: recentChannels.slice(0, 10) // 最近10个频道
    };
};

// 频道状态监控
const monitorChannelStatus = (store) => {
    const channels = store.getRecentChannels();
    
    return channels.map(channel => ({
        id: channel.id,
        name: channel.name,
        unreadCount: channel.message_unread_counter,
        importantCount: channel.importantCounter,
        lastActivity: channel.lastInterestDt
    }));
};
```

### 4. 多标签页同步

```javascript
// 广播活动变化
const broadcastActivityChange = (store, activityData, action) => {
    if (store.activityBroadcastChannel) {
        store.activityBroadcastChannel.postMessage({
            type: action, // "INSERT", "DELETE", "RELOAD_CHATTER"
            payload: activityData
        });
    }
};

// 监听其他标签页的变化
const setupCrossTabSync = (store) => {
    if (store.activityBroadcastChannel) {
        store.activityBroadcastChannel.addEventListener('message', (event) => {
            console.log('收到其他标签页的活动更新:', event.data);
        });
    }
};
```

## 数据同步

### 1. 活动数据同步

```javascript
// 活动数据同步策略
const syncActivityData = (store) => {
    // 监听服务器活动更新
    store.env.bus.addEventListener('mail.activity/updated', (event) => {
        const { activity_created, activity_deleted } = event.detail;
        
        if (activity_created) {
            store.activityCounter++;
            broadcastActivityChange(store, event.detail, 'INSERT');
        }
        
        if (activity_deleted) {
            store.activityCounter--;
            broadcastActivityChange(store, event.detail, 'DELETE');
        }
    });
};
```

### 2. 邮箱数据同步

```javascript
// 邮箱数据同步
const syncMailboxData = (store) => {
    // 同步收件箱
    const syncInbox = () => {
        store.inbox.fetchMoreMessages();
    };
    
    // 同步星标
    const syncStarred = () => {
        store.starred.fetchMoreMessages();
    };
    
    // 同步历史
    const syncHistory = () => {
        store.history.fetchMoreMessages();
    };
    
    return { syncInbox, syncStarred, syncHistory };
};
```

## 性能优化

### 1. 活动分组优化

```javascript
// 活动分组性能优化
const optimizeActivityGroups = (store) => {
    // 缓存分组结果
    const groupCache = new Map();
    
    const getCachedGroups = () => {
        const cacheKey = store.activityGroups.map(g => g.id).join(',');
        
        if (groupCache.has(cacheKey)) {
            return groupCache.get(cacheKey);
        }
        
        const groups = store.activityGroups.slice();
        groupCache.set(cacheKey, groups);
        
        return groups;
    };
    
    return getCachedGroups;
};
```

### 2. 广播通道优化

```javascript
// 广播通道性能优化
const optimizeBroadcastChannel = (store) => {
    let messageQueue = [];
    let processingTimeout = null;
    
    const queueMessage = (message) => {
        messageQueue.push(message);
        
        if (processingTimeout) {
            clearTimeout(processingTimeout);
        }
        
        processingTimeout = setTimeout(() => {
            processBatchMessages(messageQueue);
            messageQueue = [];
            processingTimeout = null;
        }, 100); // 100ms批量处理
    };
    
    const processBatchMessages = (messages) => {
        messages.forEach(message => {
            store._onActivityBroadcastChannelMessage({ data: message });
        });
    };
    
    return { queueMessage };
};
```

## 错误处理

### 1. 广播通道错误处理

```javascript
// 安全的广播通道初始化
const initBroadcastChannelSafely = (store) => {
    try {
        store.activityBroadcastChannel = new browser.BroadcastChannel("mail.activity.channel");
        
        store.activityBroadcastChannel.onmessage = (event) => {
            try {
                store._onActivityBroadcastChannelMessage(event);
            } catch (error) {
                console.error('处理广播消息失败:', error);
            }
        };
        
        store.activityBroadcastChannel.onerror = (error) => {
            console.error('广播通道错误:', error);
        };
        
    } catch (error) {
        console.warn('BroadcastChannel不支持，禁用多标签页同步');
        store.activityBroadcastChannel = null;
    }
};
```

### 2. 活动调度错误处理

```javascript
// 安全的活动调度
const scheduleActivitySafely = async (store, resModel, resIds, defaultActivityTypeId) => {
    try {
        return await store.scheduleActivity(resModel, resIds, defaultActivityTypeId);
    } catch (error) {
        console.error('活动调度失败:', error);
        
        // 显示错误消息
        store.env.services.notification.add(
            '活动调度失败，请重试',
            { type: 'danger' }
        );
        
        throw error;
    }
};
```

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 通过补丁扩展存储服务功能
- 不修改原始服务结构

### 2. 观察者模式 (Observer Pattern)
- 监听活动分组变化
- 响应广播消息

### 3. 单例模式 (Singleton Pattern)
- 存储服务在应用中只有一个实例
- 全局状态管理

## 注意事项

1. **浏览器兼容性**: BroadcastChannel API的兼容性处理
2. **性能考虑**: 避免频繁的广播消息
3. **数据一致性**: 确保多标签页间的数据同步
4. **错误处理**: 提供完善的错误处理机制

## 扩展建议

1. **智能同步**: 基于用户活跃度的智能同步策略
2. **离线支持**: 支持离线状态下的数据缓存
3. **压缩优化**: 对广播消息进行压缩
4. **监控工具**: 添加同步状态的监控工具
5. **配置化**: 支持配置化的同步策略

该补丁为存储服务在Web环境下提供了重要的功能扩展，确保了活动管理、邮箱处理和多标签页同步的正常运行。

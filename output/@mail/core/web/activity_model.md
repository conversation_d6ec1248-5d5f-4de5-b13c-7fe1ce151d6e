# Activity Model - 活动模型

## 概述

`activity_model.js` 实现了 Odoo 邮件系统中的活动模型类，用于定义活动数据的结构和行为。该模型继承自Record基类，提供了活动的完整数据定义、CRUD操作、状态管理、日期格式化、编辑功能、标记完成等方法，支持广播通道同步和序列化，是活动系统的核心数据模型，为所有活动相关组件提供了统一的数据接口和操作方法。

## 文件信息
- **路径**: `/mail/static/src/core/web/activity_model.js`
- **行数**: 221
- **模块**: `@mail/core/web/activity_model`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/record'     // 记录基类
'@mail/utils/common/misc'      // 通用工具函数
'@web/core/l10n/translation'   // 国际化
'@web/core/l10n/dates'         // 日期格式化
```

## 数据类型定义

### Data 接口

```javascript
/**
 * @typedef Data
 * @property {string} activity_category - 活动分类
 * @property {[number, string]} activity_type_id - 活动类型ID和名称
 * @property {string|false} activity_decoration - 活动装饰
 * @property {boolean} can_write - 是否可写
 * @property {'suggest'|'trigger'} chaining_type - 链接类型
 * @property {string} create_date - 创建日期
 * @property {[number, string]} create_uid - 创建用户
 * @property {string} date_deadline - 截止日期
 * @property {string} date_done - 完成日期
 * @property {string} display_name - 显示名称
 * @property {boolean} has_recommended_activities - 是否有推荐活动
 * @property {string} icon - 图标
 * @property {number} id - 活动ID
 * @property {Object[]} mail_template_ids - 邮件模板ID列表
 * @property {string} note - 备注
 * @property {number|false} previous_activity_type_id - 前一个活动类型ID
 * @property {number|false} recommended_activity_type_id - 推荐活动类型ID
 * @property {string} res_model - 资源模型
 * @property {[number, string]} res_model_id - 资源模型ID和名称
 * @property {number} res_id - 资源ID
 * @property {string} res_name - 资源名称
 * @property {number|false} request_partner_id - 请求合作伙伴ID
 * @property {'overdue'|'planned'|'today'} state - 活动状态
 * @property {string} summary - 摘要
 * @property {[number, string]} user_id - 负责用户
 * @property {string} write_date - 修改日期
 * @property {[number, string]} write_uid - 修改用户
 */
```

## 模型定义

### Activity 类

```javascript
class Activity extends Record {
    static id = "id";
    static records = {};
    
    // 静态方法
    static get(data) { return super.get(data); }
    static insert(data, { broadcast = true } = {}) { return super.insert(...arguments); }
    static _insert(data, { broadcast = true } = {}) { /* 插入逻辑 */ }
    
    // 实例属性和方法
    // ...
}
```

## 核心属性

### 1. 基本属性

```javascript
// 活动标识
/** @type {number} */
id;

/** @type {string} */
display_name;

/** @type {string} */
summary;

/** @type {string} */
activity_category;

/** @type {[number, string]} */
activity_type_id;
```

### 2. 状态属性

```javascript
/** @type {boolean} */
active;

/** @type {boolean} */
can_write;

/** @type {'overdue'|'planned'|'today'} */
state;

/** @type {string|false} */
activity_decoration;
```

### 3. 日期属性

```javascript
/** @type {luxon.DateTime} */
create_date = Record.attr(undefined, { type: "datetime" });

/** @type {luxon.DateTime} */
date_deadline = Record.attr(undefined, { type: "date" });

/** @type {luxon.DateTime} */
date_done = Record.attr(undefined, { type: "date" });
```

### 4. 关联属性

```javascript
/** @type {string} */
res_model;

/** @type {number} */
res_id;

/** @type {string} */
res_name;

/** @type {[number, string]} */
user_id;

persona = Record.one("Persona");
```

### 5. 内容属性

```javascript
note = Record.attr("", { html: true });

/** @type {string} */
feedback;

/** @type {string} */
icon = "fa-tasks";

/** @type {Object[]} */
mail_template_ids;
```

### 6. 链接属性

```javascript
/** @type {'suggest'|'trigger'} */
chaining_type;

/** @type {number|false} */
previous_activity_type_id;

/** @type {number|false} */
recommended_activity_type_id;

/** @type {boolean} */
has_recommended_activities;
```

## 核心方法

### 1. 静态插入方法

```javascript
static _insert(data, { broadcast = true } = {}) {
    const activity = this.preinsert(data);
    if (data.request_partner_id) {
        data.request_partner_id = data.request_partner_id[0];
    }
    assignDefined(activity, data);
    if (broadcast) {
        this.store.activityBroadcastChannel?.postMessage({
            type: "INSERT",
            payload: activity.serialize(),
        });
    }
    return activity;
}
```

**插入逻辑**:
- 预插入活动对象
- 处理请求合作伙伴ID
- 分配定义的数据
- 广播插入事件（如果启用）

### 2. 日期格式化

```javascript
get dateDeadlineFormatted() {
    return formatDate(this.date_deadline);
}

get dateDoneFormatted() {
    return formatDate(this.date_done);
}

get dateCreateFormatted() {
    return formatDateTime(this.create_date);
}
```

**格式化功能**:
- **截止日期**: 格式化为日期字符串
- **完成日期**: 格式化为日期字符串
- **创建日期**: 格式化为日期时间字符串

### 3. 编辑活动

```javascript
async edit() {
    return new Promise((resolve) =>
        this.store.env.services.action.doAction({
            type: "ir.actions.act_window",
            name: _t("Schedule Activity"),
            res_model: "mail.activity",
            view_mode: "form",
            views: [[false, "form"]],
            target: "new",
            res_id: this.id,
            context: {
                default_res_model: this.res_model,
                default_res_id: this.res_id,
            },
        }, { onClose: resolve })
    );
}
```

**编辑流程**:
- 打开活动编辑表单
- 设置默认上下文
- 返回Promise以便后续处理

### 4. 标记完成

```javascript
async markAsDone(attachmentIds = []) {
    await this.store.env.services.orm.call("mail.activity", "action_feedback", [[this.id]], {
        attachment_ids: attachmentIds,
        feedback: this.feedback,
    });
    this.store.activityBroadcastChannel?.postMessage({
        type: "RELOAD_CHATTER",
        payload: { id: this.res_id, model: this.res_model },
    });
}
```

**完成流程**:
- 调用服务器的反馈动作
- 传递附件ID和反馈内容
- 广播聊天器重新加载事件

### 5. 完成并安排下一个

```javascript
async markAsDoneAndScheduleNext() {
    const action = await this.store.env.services.orm.call(
        "mail.activity",
        "action_feedback_schedule_next",
        [[this.id]],
        { feedback: this.feedback }
    );
    this.activityBroadcastChannel?.postMessage({
        type: "RELOAD_CHATTER",
        payload: { id: this.res_id, model: this.res_model },
    });
    return action;
}
```

**完成并安排流程**:
- 调用服务器的反馈并安排下一个动作
- 广播聊天器重新加载事件
- 返回下一个活动的动作

### 6. 移除活动

```javascript
remove({ broadcast = true } = {}) {
    this.delete();
    if (broadcast) {
        this.activityBroadcastChannel?.postMessage({
            type: "DELETE",
            payload: { id: this.id },
        });
    }
}
```

**移除流程**:
- 删除活动对象
- 广播删除事件（如果启用）

### 7. 序列化

```javascript
serialize() {
    return JSON.parse(JSON.stringify(this.toData()));
}
```

**序列化功能**:
- 将活动数据转换为可序列化的JSON对象
- 用于广播通道传输

## 使用场景

### 1. 创建活动

```javascript
// 创建新活动
const createActivity = (activityData) => {
    const activity = Activity.insert({
        activity_category: 'default',
        activity_type_id: [1, 'Call'],
        summary: '联系客户',
        date_deadline: '2024-01-15',
        res_model: 'res.partner',
        res_id: 123,
        user_id: [1, 'Admin'],
        note: '需要讨论合同细节',
        state: 'planned'
    });
    
    return activity;
};
```

### 2. 活动状态管理

```javascript
// 活动状态管理
const ActivityStateManager = {
    getOverdueActivities: () => {
        return Object.values(Activity.records).filter(
            activity => activity.state === 'overdue'
        );
    },
    
    getTodayActivities: () => {
        return Object.values(Activity.records).filter(
            activity => activity.state === 'today'
        );
    },
    
    getPlannedActivities: () => {
        return Object.values(Activity.records).filter(
            activity => activity.state === 'planned'
        );
    },
    
    updateActivityState: (activity, newState) => {
        activity.state = newState;
        return activity;
    }
};
```

### 3. 活动完成处理

```javascript
// 活动完成处理
const ActivityCompletionHandler = {
    completeActivity: async (activity, feedback, attachments = []) => {
        // 设置反馈
        activity.feedback = feedback;
        
        // 标记完成
        await activity.markAsDone(attachments);
        
        // 记录完成日志
        console.log(`活动 ${activity.id} 已完成`);
        
        return activity;
    },
    
    completeAndScheduleNext: async (activity, feedback) => {
        // 设置反馈
        activity.feedback = feedback;
        
        // 完成并安排下一个
        const nextAction = await activity.markAsDoneAndScheduleNext();
        
        // 处理下一个活动
        if (nextAction) {
            console.log('已安排下一个活动');
        }
        
        return nextAction;
    }
};
```

### 4. 活动查询和过滤

```javascript
// 活动查询和过滤
const ActivityQueryManager = {
    getActivitiesByUser: (userId) => {
        return Object.values(Activity.records).filter(
            activity => activity.user_id[0] === userId
        );
    },
    
    getActivitiesByModel: (resModel) => {
        return Object.values(Activity.records).filter(
            activity => activity.res_model === resModel
        );
    },
    
    getActivitiesByRecord: (resModel, resId) => {
        return Object.values(Activity.records).filter(
            activity => activity.res_model === resModel && activity.res_id === resId
        );
    },
    
    getActivitiesByDeadline: (startDate, endDate) => {
        return Object.values(Activity.records).filter(activity => {
            const deadline = activity.date_deadline;
            return deadline >= startDate && deadline <= endDate;
        });
    },
    
    searchActivities: (searchTerm) => {
        const term = searchTerm.toLowerCase();
        return Object.values(Activity.records).filter(activity => 
            activity.summary?.toLowerCase().includes(term) ||
            activity.note?.toLowerCase().includes(term) ||
            activity.res_name?.toLowerCase().includes(term)
        );
    }
};
```

### 5. 活动统计

```javascript
// 活动统计
const ActivityStatistics = {
    getActivityCounts: () => {
        const activities = Object.values(Activity.records);
        
        return {
            total: activities.length,
            overdue: activities.filter(a => a.state === 'overdue').length,
            today: activities.filter(a => a.state === 'today').length,
            planned: activities.filter(a => a.state === 'planned').length,
            byType: activities.reduce((acc, activity) => {
                const typeName = activity.activity_type_id[1];
                acc[typeName] = (acc[typeName] || 0) + 1;
                return acc;
            }, {}),
            byUser: activities.reduce((acc, activity) => {
                const userName = activity.user_id[1];
                acc[userName] = (acc[userName] || 0) + 1;
                return acc;
            }, {})
        };
    },
    
    getActivityTrends: (days = 30) => {
        const activities = Object.values(Activity.records);
        const now = new Date();
        const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);
        
        const recentActivities = activities.filter(activity => 
            new Date(activity.create_date) >= startDate
        );
        
        return {
            created: recentActivities.length,
            completed: recentActivities.filter(a => a.date_done).length,
            averageCompletionTime: calculateAverageCompletionTime(recentActivities)
        };
    }
};
```

## 广播通道集成

### 1. 广播事件类型

```javascript
// 广播事件类型
const BroadcastEventTypes = {
    INSERT: 'INSERT',           // 插入活动
    DELETE: 'DELETE',           // 删除活动
    RELOAD_CHATTER: 'RELOAD_CHATTER'  // 重新加载聊天器
};

// 广播消息格式
const BroadcastMessage = {
    INSERT: {
        type: 'INSERT',
        payload: activity.serialize()
    },
    DELETE: {
        type: 'DELETE',
        payload: { id: activity.id }
    },
    RELOAD_CHATTER: {
        type: 'RELOAD_CHATTER',
        payload: { id: activity.res_id, model: activity.res_model }
    }
};
```

### 2. 广播处理

```javascript
// 广播消息处理
const handleBroadcastMessage = (event) => {
    const { type, payload } = event.data;
    
    switch (type) {
        case 'INSERT':
            // 插入新活动（不广播）
            Activity.insert(payload, { broadcast: false });
            break;
            
        case 'DELETE':
            // 删除活动
            const activity = Activity.get({ id: payload.id });
            if (activity) {
                activity.remove({ broadcast: false });
            }
            break;
            
        case 'RELOAD_CHATTER':
            // 重新加载聊天器
            const thread = store.Thread.insert({
                model: payload.model,
                id: payload.id
            });
            thread.fetchNewMessages();
            break;
    }
};
```

## 数据验证

### 1. 活动数据验证

```javascript
// 活动数据验证
const validateActivityData = (data) => {
    const errors = [];
    
    // 必需字段验证
    if (!data.activity_type_id) {
        errors.push('活动类型不能为空');
    }
    
    if (!data.res_model || !data.res_id) {
        errors.push('关联记录不能为空');
    }
    
    if (!data.user_id) {
        errors.push('负责人不能为空');
    }
    
    // 日期验证
    if (data.date_deadline) {
        const deadline = new Date(data.date_deadline);
        if (isNaN(deadline.getTime())) {
            errors.push('截止日期格式无效');
        }
    }
    
    // 状态验证
    const validStates = ['overdue', 'planned', 'today'];
    if (data.state && !validStates.includes(data.state)) {
        errors.push('活动状态无效');
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
};
```

### 2. 业务规则验证

```javascript
// 业务规则验证
const validateBusinessRules = (activity) => {
    const warnings = [];
    
    // 检查截止日期
    if (activity.date_deadline) {
        const deadline = new Date(activity.date_deadline);
        const now = new Date();
        
        if (deadline < now) {
            warnings.push('活动已逾期');
        }
    }
    
    // 检查权限
    if (!activity.can_write) {
        warnings.push('没有编辑权限');
    }
    
    // 检查链接活动
    if (activity.chaining_type === 'trigger' && !activity.recommended_activity_type_id) {
        warnings.push('触发类型活动需要推荐的下一个活动类型');
    }
    
    return warnings;
};
```

## 性能优化

### 1. 活动缓存

```javascript
// 活动缓存管理
const ActivityCache = {
    cache: new Map(),
    
    get: (key) => {
        const cached = ActivityCache.cache.get(key);
        if (cached && Date.now() - cached.timestamp < 300000) { // 5分钟缓存
            return cached.data;
        }
        return null;
    },
    
    set: (key, data) => {
        ActivityCache.cache.set(key, {
            data,
            timestamp: Date.now()
        });
    },
    
    clear: () => {
        ActivityCache.cache.clear();
    }
};
```

### 2. 批量操作

```javascript
// 批量活动操作
const BatchActivityOperations = {
    batchInsert: (activitiesData) => {
        const activities = activitiesData.map(data => 
            Activity.insert(data, { broadcast: false })
        );
        
        // 单次广播所有插入
        if (activities.length > 0) {
            store.activityBroadcastChannel?.postMessage({
                type: 'BATCH_INSERT',
                payload: activities.map(a => a.serialize())
            });
        }
        
        return activities;
    },
    
    batchUpdate: (updates) => {
        const updatedActivities = updates.map(({ id, data }) => {
            const activity = Activity.get({ id });
            if (activity) {
                Object.assign(activity, data);
            }
            return activity;
        }).filter(Boolean);
        
        return updatedActivities;
    }
};
```

## 错误处理

### 1. 操作错误处理

```javascript
// 安全的活动操作
const safeActivityOperation = async (operation, activity, ...args) => {
    try {
        return await operation.call(activity, ...args);
    } catch (error) {
        console.error('活动操作失败:', error);
        
        // 显示用户友好的错误消息
        const errorMessage = getActivityErrorMessage(error);
        showNotification(errorMessage, 'error');
        
        throw error;
    }
};

// 获取活动错误消息
const getActivityErrorMessage = (error) => {
    if (error.message.includes('access')) {
        return '没有权限执行此操作';
    } else if (error.message.includes('deadline')) {
        return '截止日期设置有误';
    } else if (error.message.includes('user')) {
        return '负责人设置有误';
    } else {
        return '操作失败，请重试';
    }
};
```

## 设计模式

### 1. 活动记录模式 (Active Record Pattern)
- 数据和行为封装在同一个类中
- 提供CRUD操作方法

### 2. 观察者模式 (Observer Pattern)
- 通过广播通道通知状态变化
- 支持多标签页同步

### 3. 工厂模式 (Factory Pattern)
- 通过静态方法创建活动实例
- 统一的创建接口

## 注意事项

1. **数据一致性**: 确保活动数据与服务器同步
2. **权限控制**: 验证用户操作权限
3. **日期处理**: 正确处理时区和日期格式
4. **广播同步**: 避免无限循环广播

## 扩展建议

1. **活动模板**: 支持活动模板功能
2. **批量操作**: 增强批量操作能力
3. **活动依赖**: 支持活动间的依赖关系
4. **自动化**: 支持基于规则的活动自动化
5. **分析功能**: 增加活动分析和报告功能

该模型为活动系统提供了完整的数据结构和操作方法，是活动管理的核心基础。

# Messaging Menu Quick Search - 消息菜单快速搜索

## 概述

`messaging_menu_quick_search.js` 实现了 Odoo 邮件系统中消息菜单的快速搜索组件，用于在消息菜单中提供快速搜索功能。该组件提供了搜索输入框、键盘快捷键支持、自动聚焦、外部点击关闭等功能，支持Escape键快速关闭，为用户提供了便捷的消息和线程搜索体验，是消息菜单补丁中的重要子组件。

## 文件信息
- **路径**: `/mail/static/src/core/web/messaging_menu_quick_search.js`
- **行数**: 37
- **模块**: `@mail/core/web/messaging_menu_quick_search`

## 依赖关系

```javascript
// 工具和钩子依赖
'@mail/utils/common/hooks'           // 邮件工具钩子
'@odoo/owl'                         // OWL 框架
'@web/core/hotkeys/hotkey_service'  // 热键服务
'@web/core/utils/hooks'             // Web 核心钩子
```

## 组件定义

### MessagingMenuQuickSearch 类

```javascript
class MessagingMenuQuickSearch extends Component {
    static components = {};
    static props = ["onClose"];
    static template = "mail.MessagingMenuQuickSearch";
}
```

## Props 配置

### Props 详细说明

- **`onClose`** (必需):
  - 类型: 函数
  - 用途: 关闭搜索组件的回调函数
  - 功能: 用户完成搜索或取消时关闭搜索界面

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.store = useState(useService("mail.store"));
    useAutofocus();
    onExternalClick("search", () => this.props.onClose());
}
```

**初始化内容**:
- 邮件存储服务的响应式状态
- 自动聚焦到搜索输入框
- 外部点击时关闭搜索

## 核心功能

### 1. 键盘事件处理

```javascript
onKeydownInput(ev) {
    const hotkey = getActiveHotkey(ev);
    if (hotkey === "escape") {
        ev.stopPropagation();
        ev.preventDefault();
        this.props.onClose();
    }
}
```

**键盘快捷键**:
- **Escape键**: 关闭搜索组件
- 阻止事件冒泡和默认行为

### 2. 自动聚焦

```javascript
useAutofocus();
```

**聚焦功能**:
- 组件挂载后自动聚焦到搜索输入框
- 提供即时的搜索体验

### 3. 外部点击关闭

```javascript
onExternalClick("search", () => this.props.onClose());
```

**外部点击处理**:
- 点击搜索区域外部时关闭搜索
- 提供直观的用户交互体验

## 使用场景

### 1. 消息菜单中的快速搜索

```javascript
// 在消息菜单中集成快速搜索
const MessagingMenuWithSearch = ({ messagingMenu }) => {
    const [showSearch, setShowSearch] = useState(false);
    
    const handleOpenSearch = () => {
        setShowSearch(true);
    };
    
    const handleCloseSearch = () => {
        setShowSearch(false);
        // 清空搜索词
        messagingMenu.store.discuss.searchTerm = "";
    };
    
    return (
        <div class="messaging-menu-container">
            <div class="menu-header">
                <button onClick={handleOpenSearch} class="search-toggle">
                    <i class="fa fa-search"></i>
                    搜索
                </button>
            </div>
            
            {showSearch && (
                <MessagingMenuQuickSearch onClose={handleCloseSearch} />
            )}
            
            <div class="menu-content">
                {/* 消息菜单内容 */}
            </div>
        </div>
    );
};
```

### 2. 搜索结果处理

```javascript
// 搜索结果处理
const SearchResultHandler = ({ store, searchTerm }) => {
    const getSearchResults = () => {
        if (!searchTerm) {
            return { threads: [], messages: [] };
        }
        
        const term = searchTerm.toLowerCase();
        
        // 搜索线程
        const threads = Object.values(store.Thread.records).filter(thread =>
            thread.displayName?.toLowerCase().includes(term) ||
            thread.description?.toLowerCase().includes(term)
        );
        
        // 搜索消息
        const messages = Object.values(store.Message.records).filter(message =>
            message.body?.toLowerCase().includes(term) ||
            message.subject?.toLowerCase().includes(term)
        );
        
        return { threads, messages };
    };
    
    const results = getSearchResults();
    
    return (
        <div class="search-results">
            {results.threads.length > 0 && (
                <div class="thread-results">
                    <h4>线程 ({results.threads.length})</h4>
                    {results.threads.map(thread => (
                        <div key={thread.id} class="thread-item">
                            {thread.displayName}
                        </div>
                    ))}
                </div>
            )}
            
            {results.messages.length > 0 && (
                <div class="message-results">
                    <h4>消息 ({results.messages.length})</h4>
                    {results.messages.map(message => (
                        <div key={message.id} class="message-item">
                            {message.subject || message.body}
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};
```

### 3. 搜索历史管理

```javascript
// 搜索历史管理
const SearchHistoryManager = {
    storageKey: 'messaging_search_history',
    maxHistoryItems: 10,
    
    getHistory: () => {
        try {
            const history = localStorage.getItem(SearchHistoryManager.storageKey);
            return history ? JSON.parse(history) : [];
        } catch (error) {
            console.error('获取搜索历史失败:', error);
            return [];
        }
    },
    
    addToHistory: (searchTerm) => {
        if (!searchTerm || searchTerm.trim().length < 2) {
            return;
        }
        
        const history = SearchHistoryManager.getHistory();
        const term = searchTerm.trim();
        
        // 移除重复项
        const filteredHistory = history.filter(item => item !== term);
        
        // 添加到开头
        filteredHistory.unshift(term);
        
        // 限制历史记录数量
        const limitedHistory = filteredHistory.slice(0, SearchHistoryManager.maxHistoryItems);
        
        try {
            localStorage.setItem(SearchHistoryManager.storageKey, JSON.stringify(limitedHistory));
        } catch (error) {
            console.error('保存搜索历史失败:', error);
        }
    },
    
    clearHistory: () => {
        try {
            localStorage.removeItem(SearchHistoryManager.storageKey);
        } catch (error) {
            console.error('清除搜索历史失败:', error);
        }
    }
};
```

### 4. 高级搜索功能

```javascript
// 高级搜索功能
const AdvancedSearchHandler = ({ store }) => {
    const [searchFilters, setSearchFilters] = useState({
        type: 'all',        // all, threads, messages
        timeRange: 'all',   // all, today, week, month
        author: '',         // 作者过滤
        hasAttachment: false // 是否有附件
    });
    
    const performAdvancedSearch = (searchTerm, filters) => {
        let results = [];
        
        // 基础搜索
        if (filters.type === 'all' || filters.type === 'threads') {
            const threads = searchThreads(store, searchTerm, filters);
            results = results.concat(threads.map(t => ({ type: 'thread', data: t })));
        }
        
        if (filters.type === 'all' || filters.type === 'messages') {
            const messages = searchMessages(store, searchTerm, filters);
            results = results.concat(messages.map(m => ({ type: 'message', data: m })));
        }
        
        // 时间范围过滤
        if (filters.timeRange !== 'all') {
            results = filterByTimeRange(results, filters.timeRange);
        }
        
        // 作者过滤
        if (filters.author) {
            results = filterByAuthor(results, filters.author);
        }
        
        // 附件过滤
        if (filters.hasAttachment) {
            results = filterByAttachment(results);
        }
        
        return results;
    };
    
    return {
        searchFilters,
        setSearchFilters,
        performAdvancedSearch
    };
};

// 搜索线程
const searchThreads = (store, searchTerm, filters) => {
    const term = searchTerm.toLowerCase();
    
    return Object.values(store.Thread.records).filter(thread => {
        // 基础文本匹配
        const textMatch = thread.displayName?.toLowerCase().includes(term) ||
                         thread.description?.toLowerCase().includes(term);
        
        if (!textMatch) return false;
        
        // 其他过滤条件
        return true;
    });
};

// 搜索消息
const searchMessages = (store, searchTerm, filters) => {
    const term = searchTerm.toLowerCase();
    
    return Object.values(store.Message.records).filter(message => {
        // 基础文本匹配
        const textMatch = message.body?.toLowerCase().includes(term) ||
                         message.subject?.toLowerCase().includes(term);
        
        if (!textMatch) return false;
        
        // 其他过滤条件
        return true;
    });
};
```

### 5. 搜索建议

```javascript
// 搜索建议功能
const SearchSuggestionProvider = ({ store, searchTerm }) => {
    const getSuggestions = () => {
        if (!searchTerm || searchTerm.length < 2) {
            return [];
        }
        
        const suggestions = [];
        const term = searchTerm.toLowerCase();
        
        // 线程名称建议
        Object.values(store.Thread.records).forEach(thread => {
            if (thread.displayName?.toLowerCase().includes(term)) {
                suggestions.push({
                    type: 'thread',
                    text: thread.displayName,
                    subtitle: '线程',
                    action: () => openThread(thread)
                });
            }
        });
        
        // 用户名建议
        Object.values(store.Persona.records).forEach(persona => {
            if (persona.name?.toLowerCase().includes(term)) {
                suggestions.push({
                    type: 'user',
                    text: persona.name,
                    subtitle: '用户',
                    action: () => searchByUser(persona)
                });
            }
        });
        
        // 历史搜索建议
        const history = SearchHistoryManager.getHistory();
        history.forEach(historyTerm => {
            if (historyTerm.toLowerCase().includes(term)) {
                suggestions.push({
                    type: 'history',
                    text: historyTerm,
                    subtitle: '历史搜索',
                    action: () => performSearch(historyTerm)
                });
            }
        });
        
        return suggestions.slice(0, 10); // 限制建议数量
    };
    
    const suggestions = getSuggestions();
    
    return (
        <div class="search-suggestions">
            {suggestions.map((suggestion, index) => (
                <div 
                    key={index}
                    class="suggestion-item"
                    onClick={suggestion.action}
                >
                    <div class="suggestion-text">{suggestion.text}</div>
                    <div class="suggestion-subtitle">{suggestion.subtitle}</div>
                </div>
            ))}
        </div>
    );
};
```

## 键盘交互

### 1. 键盘导航

```javascript
// 键盘导航支持
const KeyboardNavigationHandler = {
    handleKeyDown: (event, suggestions, activeIndex, setActiveIndex) => {
        switch (event.key) {
            case 'ArrowDown':
                event.preventDefault();
                setActiveIndex(Math.min(activeIndex + 1, suggestions.length - 1));
                break;
                
            case 'ArrowUp':
                event.preventDefault();
                setActiveIndex(Math.max(activeIndex - 1, 0));
                break;
                
            case 'Enter':
                event.preventDefault();
                if (activeIndex >= 0 && suggestions[activeIndex]) {
                    suggestions[activeIndex].action();
                }
                break;
                
            case 'Escape':
                event.preventDefault();
                // 关闭搜索
                break;
        }
    }
};
```

### 2. 快捷键支持

```javascript
// 快捷键支持
const SearchShortcuts = {
    registerShortcuts: (hotkeyService) => {
        // Ctrl+K 或 Cmd+K 打开搜索
        hotkeyService.add('control+k', () => {
            openQuickSearch();
        });
        
        // Ctrl+Shift+F 打开高级搜索
        hotkeyService.add('control+shift+f', () => {
            openAdvancedSearch();
        });
    }
};
```

## 性能优化

### 1. 搜索防抖

```javascript
// 搜索防抖
const useSearchDebounce = (searchFunction, delay = 300) => {
    const [debouncedSearch] = useState(() => 
        debounce(searchFunction, delay)
    );
    
    return debouncedSearch;
};

// 使用示例
const SearchComponent = () => {
    const [searchTerm, setSearchTerm] = useState('');
    const [results, setResults] = useState([]);
    
    const performSearch = (term) => {
        const searchResults = searchInStore(term);
        setResults(searchResults);
    };
    
    const debouncedSearch = useSearchDebounce(performSearch);
    
    const handleInputChange = (event) => {
        const term = event.target.value;
        setSearchTerm(term);
        debouncedSearch(term);
    };
    
    return (
        <input 
            type="text"
            value={searchTerm}
            onChange={handleInputChange}
            placeholder="搜索消息和线程..."
        />
    );
};
```

### 2. 结果缓存

```javascript
// 搜索结果缓存
const SearchResultCache = {
    cache: new Map(),
    maxCacheSize: 50,
    
    get: (searchTerm) => {
        const cached = SearchResultCache.cache.get(searchTerm);
        if (cached && Date.now() - cached.timestamp < 60000) { // 1分钟缓存
            return cached.results;
        }
        return null;
    },
    
    set: (searchTerm, results) => {
        // 清理旧缓存
        if (SearchResultCache.cache.size >= SearchResultCache.maxCacheSize) {
            const firstKey = SearchResultCache.cache.keys().next().value;
            SearchResultCache.cache.delete(firstKey);
        }
        
        SearchResultCache.cache.set(searchTerm, {
            results,
            timestamp: Date.now()
        });
    },
    
    clear: () => {
        SearchResultCache.cache.clear();
    }
};
```

## 可访问性

### 1. ARIA 标签

```javascript
// 可访问性属性
const getAccessibilityProps = () => {
    return {
        'role': 'search',
        'aria-label': '快速搜索',
        'aria-expanded': 'true',
        'aria-haspopup': 'listbox'
    };
};

// 搜索建议的可访问性
const getSuggestionAccessibilityProps = (suggestion, index, isActive) => {
    return {
        'role': 'option',
        'aria-selected': isActive,
        'id': `suggestion-${index}`,
        'aria-label': `${suggestion.text} - ${suggestion.subtitle}`
    };
};
```

### 2. 屏幕阅读器支持

```javascript
// 屏幕阅读器支持
const announceSearchResults = (resultsCount) => {
    const message = resultsCount === 0 
        ? '没有找到搜索结果'
        : `找到 ${resultsCount} 个搜索结果`;
    
    // 创建临时元素用于屏幕阅读器播报
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.style.position = 'absolute';
    announcement.style.left = '-10000px';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    setTimeout(() => {
        document.body.removeChild(announcement);
    }, 1000);
};
```

## 设计模式

### 1. 组件模式 (Component Pattern)
- 独立的搜索组件
- 可复用的搜索界面

### 2. 观察者模式 (Observer Pattern)
- 监听搜索词变化
- 响应键盘事件

### 3. 策略模式 (Strategy Pattern)
- 不同的搜索策略
- 可配置的搜索行为

## 注意事项

1. **性能考虑**: 使用防抖避免频繁搜索
2. **用户体验**: 提供即时的搜索反馈
3. **可访问性**: 支持键盘导航和屏幕阅读器
4. **数据安全**: 避免搜索敏感信息

## 扩展建议

1. **语音搜索**: 支持语音输入搜索
2. **智能建议**: 基于AI的搜索建议
3. **搜索分析**: 搜索行为分析和优化
4. **多语言**: 支持多语言搜索
5. **正则表达式**: 支持高级搜索语法

该组件为消息菜单提供了便捷的快速搜索功能，是提升用户体验的重要工具。

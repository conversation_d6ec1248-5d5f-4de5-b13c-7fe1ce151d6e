# Activity Button - 活动按钮组件

## 概述

`activity_button.js` 实现了 Odoo 邮件系统中的活动按钮组件，用于在列表视图和表单视图中显示记录的活动状态。该组件根据活动状态显示不同的颜色和图标，支持点击打开活动列表弹出框，提供了直观的活动状态指示和快速访问活动管理功能，是活动系统与其他视图集成的重要界面组件。

## 文件信息
- **路径**: `/mail/static/src/core/web/activity_button.js`
- **行数**: 105
- **模块**: `@mail/core/web/activity_button`

## 依赖关系

```javascript
// UI组件依赖
'@mail/core/web/activity_list_popover'  // 活动列表弹出框

// 核心依赖
'@odoo/owl'                            // OWL 框架
'@web/core/l10n/translation'           // 国际化
'@web/core/popover/popover_hook'       // 弹出框钩子
```

## 组件定义

### ActivityButton 类

```javascript
class ActivityButton extends Component {
    static props = {
        record: { type: Object },  // 记录对象
    };
    static template = "mail.ActivityButton";
}
```

## Props 配置

### Props 详细说明

- **`record`** (必需):
  - 类型: 对象
  - 用途: 包含活动信息的记录对象
  - 属性: 包含活动状态、ID、类型等信息

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.popover = usePopover(ActivityListPopover, { position: "bottom-start" });
    this.buttonRef = useRef("button");
    this.env = useEnv();
}
```

**初始化内容**:
- 活动列表弹出框配置
- 按钮DOM引用
- 环境对象获取

## 核心功能

### 1. 按钮样式计算

```javascript
get buttonClass() {
    const classes = [];
    
    // 根据活动状态设置颜色
    switch (this.props.record.data.activity_state) {
        case "overdue":
            classes.push("text-danger");
            break;
        case "today":
            classes.push("text-warning");
            break;
        case "planned":
            classes.push("text-success");
            break;
        default:
            classes.push("text-muted");
            break;
    }
    
    // 根据异常装饰设置样式
    switch (this.props.record.data.activity_exception_decoration) {
        case "warning":
            classes.push("text-warning");
            classes.push(this.props.record.data.activity_exception_icon);
            break;
        case "danger":
            classes.push("text-danger");
            classes.push(this.props.record.data.activity_exception_icon);
            break;
        default: {
            const { activity_ids, activity_type_icon } = this.props.record.data;
            if (activity_ids.records.length) {
                classes.push(activity_type_icon || "fa-tasks");
                break;
            }
            classes.push("fa-clock-o btn-link text-dark");
            break;
        }
    }
    
    return classes.join(" ");
}
```

**样式逻辑**:
- **逾期活动**: 红色危险样式
- **今日活动**: 黄色警告样式
- **计划活动**: 绿色成功样式
- **异常装饰**: 特殊警告或危险样式
- **默认状态**: 根据活动类型图标或默认时钟图标

### 2. 标题计算

```javascript
get title() {
    if (this.props.record.data.activity_exception_decoration) {
        return _t("Warning");
    }
    if (this.props.record.data.activity_summary) {
        return this.props.record.data.activity_summary;
    }
    if (this.props.record.data.activity_type_id) {
        return this.props.record.data.activity_type_id[1 /* display_name */];
    }
    return _t("Show activities");
}
```

**标题优先级**:
1. 异常装饰时显示"Warning"
2. 活动摘要
3. 活动类型显示名称
4. 默认"Show activities"

### 3. 点击处理

```javascript
async onClick() {
    if (this.popover.isOpen) {
        this.popover.close();
    } else {
        const resId = this.props.record.resId;
        const selectedRecords = this.env?.model?.root?.selection ?? [];
        const selectedIds = selectedRecords.map((r) => r.resId);
        
        // 如果当前记录未选中，忽略选择
        const resIds =
            selectedIds.includes(resId) && selectedIds.length > 1 ? selectedIds : undefined;
            
        this.popover.open(this.buttonRef.el, {
            activityIds: this.props.record.data.activity_ids.currentIds,
            onActivityChanged: (thread) => {
                const recordToLoad = resIds ? selectedRecords : [this.props.record];
                recordToLoad.forEach((r) => r.load());
                this.popover.close();
            },
            resId,
            resIds,
            resModel: this.props.record.resModel,
        });
    }
}
```

**点击逻辑**:
- 如果弹出框已打开，则关闭
- 否则打开活动列表弹出框
- 支持批量选择记录的活动管理
- 活动变化后重新加载相关记录

## 使用场景

### 1. 列表视图中的活动按钮

```javascript
// 在列表视图中显示活动状态
const ListView = ({ records }) => {
    return (
        <table class="list-view">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>状态</th>
                    <th>活动</th>
                </tr>
            </thead>
            <tbody>
                {records.map(record => (
                    <tr key={record.id}>
                        <td>{record.name}</td>
                        <td>{record.state}</td>
                        <td>
                            <ActivityButton record={record} />
                        </td>
                    </tr>
                ))}
            </tbody>
        </table>
    );
};
```

### 2. 表单视图中的活动按钮

```javascript
// 在表单视图中显示活动按钮
const FormView = ({ record }) => {
    return (
        <div class="form-view">
            <div class="form-header">
                <h1>{record.name}</h1>
                <div class="form-actions">
                    <ActivityButton record={record} />
                    <button class="btn btn-primary">保存</button>
                </div>
            </div>
            <div class="form-content">
                {/* 表单字段 */}
            </div>
        </div>
    );
};
```

### 3. 看板视图中的活动指示器

```javascript
// 在看板卡片中显示活动状态
const KanbanCard = ({ record }) => {
    return (
        <div class="kanban-card">
            <div class="card-header">
                <span class="card-title">{record.name}</span>
                <ActivityButton record={record} />
            </div>
            <div class="card-body">
                {/* 卡片内容 */}
            </div>
        </div>
    );
};
```

### 4. 批量活动管理

```javascript
// 支持批量选择的活动管理
const BatchActivityManager = ({ selectedRecords }) => {
    const handleBatchActivity = () => {
        // 为选中的记录批量创建活动
        selectedRecords.forEach(record => {
            createActivityForRecord(record);
        });
    };
    
    return (
        <div class="batch-manager">
            <div class="selected-count">
                已选择 {selectedRecords.length} 条记录
            </div>
            <div class="batch-actions">
                <button onClick={handleBatchActivity}>
                    批量创建活动
                </button>
                {selectedRecords.map(record => (
                    <ActivityButton key={record.id} record={record} />
                ))}
            </div>
        </div>
    );
};
```

## 状态指示

### 1. 活动状态映射

```javascript
// 活动状态到视觉样式的映射
const ActivityStateMapping = {
    overdue: {
        class: 'text-danger',
        icon: 'fa-exclamation-triangle',
        tooltip: '逾期活动',
        priority: 'high'
    },
    today: {
        class: 'text-warning',
        icon: 'fa-clock-o',
        tooltip: '今日到期',
        priority: 'medium'
    },
    planned: {
        class: 'text-success',
        icon: 'fa-calendar',
        tooltip: '已计划',
        priority: 'low'
    },
    none: {
        class: 'text-muted',
        icon: 'fa-plus',
        tooltip: '添加活动',
        priority: 'none'
    }
};

// 获取状态配置
const getStateConfig = (activityState) => {
    return ActivityStateMapping[activityState] || ActivityStateMapping.none;
};
```

### 2. 异常状态处理

```javascript
// 异常状态的特殊处理
const handleExceptionState = (record) => {
    const { activity_exception_decoration, activity_exception_icon } = record.data;
    
    if (activity_exception_decoration === 'warning') {
        return {
            class: 'text-warning',
            icon: activity_exception_icon || 'fa-exclamation-triangle',
            tooltip: '活动警告',
            isException: true
        };
    }
    
    if (activity_exception_decoration === 'danger') {
        return {
            class: 'text-danger',
            icon: activity_exception_icon || 'fa-exclamation-circle',
            tooltip: '活动错误',
            isException: true
        };
    }
    
    return null;
};
```

### 3. 动态样式计算

```javascript
// 动态计算按钮样式
const computeButtonStyle = (record) => {
    const baseClasses = ['activity-button'];
    
    // 检查异常状态
    const exceptionConfig = handleExceptionState(record);
    if (exceptionConfig) {
        return {
            classes: [...baseClasses, exceptionConfig.class],
            icon: exceptionConfig.icon,
            tooltip: exceptionConfig.tooltip
        };
    }
    
    // 正常状态处理
    const stateConfig = getStateConfig(record.data.activity_state);
    const activityCount = record.data.activity_ids?.records?.length || 0;
    
    if (activityCount > 0) {
        baseClasses.push(stateConfig.class);
        
        // 多个活动时的特殊样式
        if (activityCount > 1) {
            baseClasses.push('multiple-activities');
        }
    } else {
        baseClasses.push('no-activities');
    }
    
    return {
        classes: baseClasses,
        icon: record.data.activity_type_icon || stateConfig.icon,
        tooltip: stateConfig.tooltip,
        count: activityCount
    };
};
```

## 弹出框集成

### 1. 弹出框配置

```javascript
// 弹出框配置选项
const popoverConfig = {
    position: 'bottom-start',
    arrow: true,
    offset: [0, 8],
    flip: true,
    preventOverflow: true,
    boundary: 'viewport'
};

// 动态位置调整
const adjustPopoverPosition = (buttonElement) => {
    const rect = buttonElement.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;
    
    // 根据按钮位置调整弹出框位置
    if (rect.bottom + 300 > viewportHeight) {
        return 'top-start';
    }
    
    if (rect.right + 400 > viewportWidth) {
        return 'bottom-end';
    }
    
    return 'bottom-start';
};
```

### 2. 弹出框内容

```javascript
// 弹出框内容配置
const getPopoverProps = (record, selectedRecords) => {
    const resId = record.resId;
    const selectedIds = selectedRecords.map(r => r.resId);
    const isMultiSelect = selectedIds.includes(resId) && selectedIds.length > 1;
    
    return {
        activityIds: record.data.activity_ids.currentIds,
        resId: resId,
        resIds: isMultiSelect ? selectedIds : undefined,
        resModel: record.resModel,
        title: isMultiSelect ? `${selectedIds.length} 条记录的活动` : record.display_name,
        onActivityChanged: (thread) => {
            // 重新加载相关记录
            const recordsToReload = isMultiSelect ? selectedRecords : [record];
            recordsToReload.forEach(r => r.load());
        }
    };
};
```

## 性能优化

### 1. 样式计算缓存

```javascript
// 缓存样式计算结果
const styleCache = new WeakMap();

const getCachedButtonStyle = (record) => {
    if (styleCache.has(record)) {
        const cached = styleCache.get(record);
        // 检查记录是否有变化
        if (cached.version === record.version) {
            return cached.style;
        }
    }
    
    const style = computeButtonStyle(record);
    styleCache.set(record, {
        style,
        version: record.version
    });
    
    return style;
};
```

### 2. 事件防抖

```javascript
// 防抖点击处理
const debouncedClick = debounce((button) => {
    button.onClick();
}, 200);

// 防止快速连续点击
const handleClick = (button) => {
    if (button.isProcessing) {
        return;
    }
    
    button.isProcessing = true;
    debouncedClick(button);
    
    setTimeout(() => {
        button.isProcessing = false;
    }, 300);
};
```

## 可访问性

### 1. 键盘导航

```javascript
// 键盘导航支持
const handleKeyDown = (event, button) => {
    switch (event.key) {
        case 'Enter':
        case ' ':
            event.preventDefault();
            button.onClick();
            break;
        case 'Escape':
            if (button.popover.isOpen) {
                button.popover.close();
            }
            break;
    }
};
```

### 2. ARIA 属性

```javascript
// 可访问性属性
const getAccessibilityProps = (record) => {
    const activityCount = record.data.activity_ids?.records?.length || 0;
    const state = record.data.activity_state;
    
    return {
        'role': 'button',
        'aria-label': `活动按钮，${activityCount} 个活动，状态：${state}`,
        'aria-haspopup': 'dialog',
        'aria-expanded': 'false',
        'tabindex': '0'
    };
};
```

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的活动按钮组件
- 统一的活动状态显示

### 2. 策略模式 (Strategy Pattern)
- 不同活动状态的不同显示策略
- 可配置的样式映射

### 3. 观察者模式 (Observer Pattern)
- 监听记录变化
- 响应活动状态更新

## 注意事项

1. **状态同步**: 确保按钮状态与实际活动状态同步
2. **性能考虑**: 避免频繁的样式重计算
3. **用户体验**: 提供清晰的视觉反馈
4. **可访问性**: 支持键盘导航和屏幕阅读器

## 扩展建议

1. **自定义样式**: 支持用户自定义活动状态样式
2. **批量操作**: 增强批量活动管理功能
3. **快捷操作**: 支持右键菜单快捷操作
4. **状态动画**: 添加状态变化的动画效果
5. **移动端优化**: 优化移动设备上的交互体验

该组件为活动系统提供了直观的状态指示和快速访问功能，是活动管理与其他业务视图集成的重要桥梁。

# Suggested Recipient - 建议收件人

## 概述

`suggested_recipient.js` 实现了 Odoo 邮件系统中的建议收件人组件，用于在邮件编写界面中显示和管理建议的收件人。该组件支持收件人的选择、合作伙伴创建、状态管理等功能，当选择没有合作伙伴记录的建议收件人时，会自动打开合作伙伴创建表单，确保所有收件人都有对应的合作伙伴记录，为用户提供了智能的收件人管理体验。

## 文件信息
- **路径**: `/mail/static/src/core/web/suggested_recipient.js`
- **行数**: 67
- **模块**: `@mail/core/web/suggested_recipient`

## 依赖关系

```javascript
// UI组件依赖
'@web/views/view_dialogs/form_view_dialog'  // 表单视图对话框

// 核心依赖
'@odoo/owl'                                // OWL 框架
'@web/core/l10n/translation'               // 国际化
'@web/core/utils/hooks'                    // Web 核心钩子
```

## 组件定义

### SuggestedRecipient 类

```javascript
class SuggestedRecipient extends Component {
    static template = "mail.SuggestedRecipients";
    static props = ["thread", "recipient", "onSuggestedRecipientAdded"];
}
```

**组件配置**:
- **模板**: "mail.SuggestedRecipients"
- **Props**: thread（线程）、recipient（收件人）、onSuggestedRecipientAdded（添加回调）

## Props 配置

### Props 详细说明

- **`thread`** (必需):
  - 类型: Thread 模型实例
  - 用途: 提供线程上下文信息
  - 功能: 用于合作伙伴创建时的上下文

- **`recipient`** (必需):
  - 类型: SuggestedRecipient 对象
  - 用途: 建议收件人的数据
  - 功能: 包含收件人的基本信息和状态

- **`onSuggestedRecipientAdded`** (必需):
  - 类型: 函数
  - 用途: 收件人添加完成后的回调
  - 功能: 通知父组件更新状态

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.dialogService = useService("dialog");
}
```

**初始化内容**:
- 对话框服务用于打开合作伙伴创建表单

## 核心功能

### 1. 标题文本生成

```javascript
get titleText() {
    return _t("Add as recipient and follower (reason: %s)", this.props.recipient.reason);
}
```

**标题逻辑**:
- 显示添加收件人和关注者的提示
- 包含建议的原因说明
- 支持国际化

### 2. 复选框变化处理

```javascript
onChangeCheckbox(ev) {
    this.props.recipient.checked = !this.props.recipient.checked;
    if (this.props.recipient.checked && !this.props.recipient.persona) {
        this.props.recipient.checked = false;
        // 打开合作伙伴创建表单
        this.dialogService.add(FormViewDialog, {
            // 表单配置...
        });
    }
}
```

**处理逻辑**:
- 切换收件人的选中状态
- 如果选中且没有合作伙伴记录，则打开创建表单
- 确保所有收件人都有对应的合作伙伴记录

### 3. 合作伙伴创建表单

```javascript
this.dialogService.add(FormViewDialog, {
    context: {
        active_id: thread.id,
        active_model: "mail.compose.message",
        default_email: this.props.recipient.email,
        default_name: this.props.recipient.name,
        default_lang: this.props.recipient.lang,
        ...Object.fromEntries(
            Object.entries(this.props.recipient.create_values).map(([k, v]) => [
                "default_" + k,
                v,
            ])
        ),
        force_email: true,
        ref: "compound_context",
    },
    onRecordSaved: () => this.props.onSuggestedRecipientAdded(thread),
    resModel: "res.partner",
    title: _t("Please complete customer's information"),
});
```

**表单配置**:
- **上下文**: 包含默认值和创建参数
- **保存回调**: 调用父组件的添加回调
- **模型**: res.partner
- **标题**: 国际化的表单标题

## 使用场景

### 1. 邮件编写收件人建议

```javascript
// 邮件编写中的收件人建议系统
const RecipientSuggestionSystem = {
    generateSuggestions: (thread, context) => {
        const suggestions = [];
        
        // 基于线程历史的建议
        const threadParticipants = RecipientSuggestionSystem.getThreadParticipants(thread);
        threadParticipants.forEach(participant => {
            if (!RecipientSuggestionSystem.isAlreadyRecipient(participant, context.currentRecipients)) {
                suggestions.push({
                    email: participant.email,
                    name: participant.name,
                    persona: participant.partner,
                    reason: '参与过此对话',
                    checked: false,
                    create_values: {},
                    priority: 10
                });
            }
        });
        
        // 基于关注者的建议
        const followers = RecipientSuggestionSystem.getFollowers(thread);
        followers.forEach(follower => {
            if (!RecipientSuggestionSystem.isAlreadyRecipient(follower, context.currentRecipients)) {
                suggestions.push({
                    email: follower.email,
                    name: follower.name,
                    persona: follower.partner,
                    reason: '关注此记录',
                    checked: false,
                    create_values: {},
                    priority: 8
                });
            }
        });
        
        // 基于相关记录的建议
        const relatedContacts = RecipientSuggestionSystem.getRelatedContacts(thread);
        relatedContacts.forEach(contact => {
            suggestions.push({
                email: contact.email,
                name: contact.name,
                persona: contact.partner,
                reason: '相关联系人',
                checked: false,
                create_values: contact.create_values || {},
                priority: 6
            });
        });
        
        // 按优先级排序
        return suggestions.sort((a, b) => b.priority - a.priority);
    },
    
    getThreadParticipants: (thread) => {
        const participants = new Map();
        
        thread.messages.forEach(message => {
            if (message.author && message.author.email) {
                participants.set(message.author.email, {
                    email: message.author.email,
                    name: message.author.name,
                    partner: message.author
                });
            }
        });
        
        return Array.from(participants.values());
    },
    
    getFollowers: (thread) => {
        return thread.followers.map(follower => ({
            email: follower.partner.email,
            name: follower.partner.name,
            partner: follower.partner
        })).filter(f => f.email);
    },
    
    getRelatedContacts: (thread) => {
        // 基于业务逻辑获取相关联系人
        const relatedContacts = [];
        
        // 如果是销售订单相关，添加销售团队
        if (thread.model === 'sale.order') {
            const salesTeam = RecipientSuggestionSystem.getSalesTeam(thread.res_id);
            relatedContacts.push(...salesTeam);
        }
        
        // 如果是项目相关，添加项目成员
        if (thread.model === 'project.project') {
            const projectMembers = RecipientSuggestionSystem.getProjectMembers(thread.res_id);
            relatedContacts.push(...projectMembers);
        }
        
        return relatedContacts;
    },
    
    isAlreadyRecipient: (contact, currentRecipients) => {
        return currentRecipients.some(recipient => 
            recipient.email === contact.email
        );
    }
};
```

### 2. 智能收件人推荐

```javascript
// 智能收件人推荐
const IntelligentRecipientRecommender = {
    recommend: async (thread, userInput) => {
        const recommendations = [];
        
        // 基于用户输入的模糊匹配
        if (userInput) {
            const fuzzyMatches = await IntelligentRecipientRecommender.fuzzySearch(userInput);
            recommendations.push(...fuzzyMatches);
        }
        
        // 基于机器学习的推荐
        const mlRecommendations = await IntelligentRecipientRecommender.getMachineLearningRecommendations(thread);
        recommendations.push(...mlRecommendations);
        
        // 基于协作过滤的推荐
        const collaborativeRecommendations = await IntelligentRecipientRecommender.getCollaborativeRecommendations(thread);
        recommendations.push(...collaborativeRecommendations);
        
        // 去重和排序
        return IntelligentRecipientRecommender.deduplicateAndSort(recommendations);
    },
    
    fuzzySearch: async (query) => {
        const response = await fetch('/web/dataset/call_kw/res.partner/search_fuzzy', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                model: 'res.partner',
                method: 'search_fuzzy',
                args: [query],
                kwargs: { limit: 10 }
            })
        });
        
        const result = await response.json();
        return result.result.map(partner => ({
            email: partner.email,
            name: partner.name,
            persona: partner,
            reason: '匹配搜索条件',
            checked: false,
            create_values: {},
            priority: 5
        }));
    },
    
    getMachineLearningRecommendations: async (thread) => {
        // 调用机器学习服务获取推荐
        try {
            const response = await fetch('/web/dataset/call_kw/mail.thread/get_ml_recommendations', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    model: 'mail.thread',
                    method: 'get_ml_recommendations',
                    args: [thread.id, thread.model],
                    kwargs: {}
                })
            });
            
            const result = await response.json();
            return result.result.map(rec => ({
                email: rec.email,
                name: rec.name,
                persona: rec.partner,
                reason: 'AI推荐',
                checked: false,
                create_values: {},
                priority: rec.confidence * 10
            }));
        } catch (error) {
            console.error('获取ML推荐失败:', error);
            return [];
        }
    },
    
    getCollaborativeRecommendations: async (thread) => {
        // 基于其他用户的行为推荐
        const similarThreads = await IntelligentRecipientRecommender.findSimilarThreads(thread);
        const recommendations = [];
        
        similarThreads.forEach(similarThread => {
            similarThread.recipients.forEach(recipient => {
                recommendations.push({
                    email: recipient.email,
                    name: recipient.name,
                    persona: recipient.partner,
                    reason: '其他类似对话的收件人',
                    checked: false,
                    create_values: {},
                    priority: 4
                });
            });
        });
        
        return recommendations;
    },
    
    findSimilarThreads: async (thread) => {
        // 查找相似的线程
        const response = await fetch('/web/dataset/call_kw/mail.thread/find_similar', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                model: 'mail.thread',
                method: 'find_similar',
                args: [thread.id, thread.model],
                kwargs: { limit: 5 }
            })
        });
        
        const result = await response.json();
        return result.result;
    },
    
    deduplicateAndSort: (recommendations) => {
        const uniqueRecommendations = new Map();
        
        recommendations.forEach(rec => {
            const key = rec.email;
            if (!uniqueRecommendations.has(key) || 
                uniqueRecommendations.get(key).priority < rec.priority) {
                uniqueRecommendations.set(key, rec);
            }
        });
        
        return Array.from(uniqueRecommendations.values())
            .sort((a, b) => b.priority - a.priority)
            .slice(0, 20); // 限制推荐数量
    }
};
```

### 3. 收件人验证系统

```javascript
// 收件人验证系统
const RecipientValidationSystem = {
    validateRecipient: async (recipient) => {
        const validationResults = {
            isValid: true,
            warnings: [],
            errors: []
        };
        
        // 邮箱格式验证
        if (!RecipientValidationSystem.isValidEmail(recipient.email)) {
            validationResults.isValid = false;
            validationResults.errors.push('邮箱格式无效');
        }
        
        // 邮箱域名验证
        const domainValidation = await RecipientValidationSystem.validateEmailDomain(recipient.email);
        if (!domainValidation.isValid) {
            validationResults.warnings.push('邮箱域名可能无效');
        }
        
        // 重复收件人检查
        if (RecipientValidationSystem.isDuplicateRecipient(recipient)) {
            validationResults.warnings.push('收件人已存在');
        }
        
        // 黑名单检查
        if (await RecipientValidationSystem.isBlacklisted(recipient.email)) {
            validationResults.isValid = false;
            validationResults.errors.push('收件人在黑名单中');
        }
        
        return validationResults;
    },
    
    isValidEmail: (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },
    
    validateEmailDomain: async (email) => {
        const domain = email.split('@')[1];
        
        try {
            // 检查MX记录
            const response = await fetch(`/web/dataset/call_kw/mail.thread/check_mx_record`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    model: 'mail.thread',
                    method: 'check_mx_record',
                    args: [domain],
                    kwargs: {}
                })
            });
            
            const result = await response.json();
            return { isValid: result.result };
        } catch (error) {
            console.error('域名验证失败:', error);
            return { isValid: true }; // 验证失败时假设有效
        }
    },
    
    isDuplicateRecipient: (recipient) => {
        const currentRecipients = getCurrentRecipients();
        return currentRecipients.some(r => r.email === recipient.email);
    },
    
    isBlacklisted: async (email) => {
        try {
            const response = await fetch('/web/dataset/call_kw/mail.blacklist/is_blacklisted', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    model: 'mail.blacklist',
                    method: 'is_blacklisted',
                    args: [email],
                    kwargs: {}
                })
            });
            
            const result = await response.json();
            return result.result;
        } catch (error) {
            console.error('黑名单检查失败:', error);
            return false;
        }
    }
};
```

### 4. 收件人分组管理

```javascript
// 收件人分组管理
const RecipientGroupManager = {
    createGroup: (name, recipients) => {
        const group = {
            id: generateId(),
            name,
            recipients: recipients.map(r => ({ ...r })),
            createdAt: Date.now(),
            updatedAt: Date.now()
        };
        
        RecipientGroupManager.saveGroup(group);
        return group;
    },
    
    saveGroup: (group) => {
        const groups = RecipientGroupManager.getGroups();
        const existingIndex = groups.findIndex(g => g.id === group.id);
        
        if (existingIndex !== -1) {
            groups[existingIndex] = { ...group, updatedAt: Date.now() };
        } else {
            groups.push(group);
        }
        
        localStorage.setItem('recipient_groups', JSON.stringify(groups));
    },
    
    getGroups: () => {
        try {
            return JSON.parse(localStorage.getItem('recipient_groups') || '[]');
        } catch (error) {
            console.error('获取收件人分组失败:', error);
            return [];
        }
    },
    
    deleteGroup: (groupId) => {
        const groups = RecipientGroupManager.getGroups();
        const filteredGroups = groups.filter(g => g.id !== groupId);
        localStorage.setItem('recipient_groups', JSON.stringify(filteredGroups));
    },
    
    applyGroup: (groupId, composer) => {
        const groups = RecipientGroupManager.getGroups();
        const group = groups.find(g => g.id === groupId);
        
        if (group) {
            group.recipients.forEach(recipient => {
                composer.addRecipient(recipient);
            });
            
            showNotification(`已应用收件人分组: ${group.name}`, 'success');
        }
    },
    
    renderGroupSelector: (composer) => {
        const groups = RecipientGroupManager.getGroups();
        
        const selector = document.createElement('div');
        selector.className = 'recipient-group-selector';
        selector.innerHTML = `
            <div class="group-selector-header">
                <h5>收件人分组</h5>
                <button class="create-group-btn btn btn-sm btn-primary">创建分组</button>
            </div>
            
            <div class="group-list">
                ${groups.map(group => `
                    <div class="group-item" data-group-id="${group.id}">
                        <span class="group-name">${group.name}</span>
                        <span class="group-count">(${group.recipients.length})</span>
                        <div class="group-actions">
                            <button class="apply-group-btn btn btn-sm btn-secondary">应用</button>
                            <button class="edit-group-btn btn btn-sm btn-outline-secondary">编辑</button>
                            <button class="delete-group-btn btn btn-sm btn-outline-danger">删除</button>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
        
        RecipientGroupManager.bindGroupEvents(selector, composer);
        return selector;
    },
    
    bindGroupEvents: (selector, composer) => {
        // 应用分组
        selector.querySelectorAll('.apply-group-btn').forEach(btn => {
            btn.addEventListener('click', (event) => {
                const groupId = event.target.closest('.group-item').dataset.groupId;
                RecipientGroupManager.applyGroup(groupId, composer);
            });
        });
        
        // 删除分组
        selector.querySelectorAll('.delete-group-btn').forEach(btn => {
            btn.addEventListener('click', (event) => {
                const groupItem = event.target.closest('.group-item');
                const groupId = groupItem.dataset.groupId;
                const groupName = groupItem.querySelector('.group-name').textContent;
                
                if (confirm(`确定要删除分组 "${groupName}" 吗？`)) {
                    RecipientGroupManager.deleteGroup(groupId);
                    groupItem.remove();
                }
            });
        });
        
        // 创建分组
        selector.querySelector('.create-group-btn').addEventListener('click', () => {
            RecipientGroupManager.showCreateGroupDialog(composer);
        });
    },
    
    showCreateGroupDialog: (composer) => {
        const currentRecipients = composer.getCurrentRecipients();
        
        if (currentRecipients.length === 0) {
            showNotification('请先添加收件人', 'warning');
            return;
        }
        
        const groupName = prompt('请输入分组名称:');
        if (groupName) {
            const group = RecipientGroupManager.createGroup(groupName, currentRecipients);
            showNotification(`分组 "${group.name}" 已创建`, 'success');
        }
    }
};
```

### 5. 收件人历史记录

```javascript
// 收件人历史记录
const RecipientHistoryManager = {
    addToHistory: (recipient) => {
        const history = RecipientHistoryManager.getHistory();
        
        // 移除已存在的记录
        const filtered = history.filter(r => r.email !== recipient.email);
        
        // 添加到开头
        filtered.unshift({
            email: recipient.email,
            name: recipient.name,
            persona: recipient.persona,
            lastUsed: Date.now(),
            useCount: (history.find(r => r.email === recipient.email)?.useCount || 0) + 1
        });
        
        // 限制历史记录数量
        const limited = filtered.slice(0, 100);
        
        localStorage.setItem('recipient_history', JSON.stringify(limited));
    },
    
    getHistory: () => {
        try {
            return JSON.parse(localStorage.getItem('recipient_history') || '[]');
        } catch (error) {
            console.error('获取收件人历史失败:', error);
            return [];
        }
    },
    
    getFrequentRecipients: (limit = 10) => {
        const history = RecipientHistoryManager.getHistory();
        return history
            .sort((a, b) => b.useCount - a.useCount)
            .slice(0, limit);
    },
    
    getRecentRecipients: (limit = 10) => {
        const history = RecipientHistoryManager.getHistory();
        return history
            .sort((a, b) => b.lastUsed - a.lastUsed)
            .slice(0, limit);
    },
    
    searchHistory: (query) => {
        const history = RecipientHistoryManager.getHistory();
        const lowerQuery = query.toLowerCase();
        
        return history.filter(recipient => 
            recipient.name.toLowerCase().includes(lowerQuery) ||
            recipient.email.toLowerCase().includes(lowerQuery)
        );
    }
};
```

## 错误处理

### 1. 合作伙伴创建错误

```javascript
// 合作伙伴创建错误处理
const PartnerCreationErrorHandler = {
    handleCreationError: (error, recipient) => {
        let errorMessage = '创建合作伙伴失败';
        
        if (error.message.includes('email')) {
            errorMessage = '邮箱地址已存在或格式无效';
        } else if (error.message.includes('required')) {
            errorMessage = '请填写必填字段';
        } else if (error.message.includes('permission')) {
            errorMessage = '没有创建合作伙伴的权限';
        }
        
        showNotification(errorMessage, 'error');
        
        // 记录错误
        console.error('合作伙伴创建失败:', {
            recipient,
            error: error.message
        });
    }
};
```

## 性能优化

### 1. 建议缓存

```javascript
// 建议缓存
const SuggestionCache = {
    cache: new Map(),
    
    getCachedSuggestions: (threadId) => {
        const cached = SuggestionCache.cache.get(threadId);
        if (cached && Date.now() - cached.timestamp < 300000) { // 5分钟缓存
            return cached.suggestions;
        }
        return null;
    },
    
    setCachedSuggestions: (threadId, suggestions) => {
        SuggestionCache.cache.set(threadId, {
            suggestions,
            timestamp: Date.now()
        });
    }
};
```

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的建议收件人组件
- 清晰的组件接口

### 2. 策略模式 (Strategy Pattern)
- 不同类型收件人的不同处理策略
- 可配置的建议算法

### 3. 观察者模式 (Observer Pattern)
- 监听收件人状态变化
- 响应用户操作

## 注意事项

1. **数据完整性**: 确保所有收件人都有合作伙伴记录
2. **用户体验**: 提供流畅的选择和创建体验
3. **权限控制**: 检查合作伙伴创建权限
4. **数据验证**: 验证收件人信息的有效性

## 扩展建议

1. **智能推荐**: 基于机器学习的收件人推荐
2. **批量操作**: 支持批量添加建议收件人
3. **分组管理**: 收件人分组和模板功能
4. **历史记录**: 收件人使用历史和统计
5. **集成外部**: 与外部通讯录系统集成

该组件为邮件系统提供了智能的收件人建议和管理功能，是邮件编写的重要辅助工具。

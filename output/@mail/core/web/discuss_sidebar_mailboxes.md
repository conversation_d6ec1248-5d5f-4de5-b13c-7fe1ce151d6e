# Discuss Sidebar Mailboxes - 讨论侧边栏邮箱

## 概述

`discuss_sidebar_mailboxes.js` 实现了 Odoo 邮件系统中讨论应用侧边栏的邮箱组件，用于在讨论应用的侧边栏中显示邮箱列表。该文件包含两个主要组件：单个邮箱组件和邮箱列表组件，支持邮箱的悬停效果、下拉菜单、线程打开等功能，通过注册表系统集成到讨论侧边栏中，为用户提供了便捷的邮箱访问和管理界面。

## 文件信息
- **路径**: `/mail/static/src/core/web/discuss_sidebar_mailboxes.js`
- **行数**: 65
- **模块**: `@mail/core/web/discuss_sidebar_mailboxes`

## 依赖关系

```javascript
// UI组件依赖
'@mail/core/common/thread_icon'           // 线程图标组件
'@web/core/dropdown/dropdown'             // 下拉框组件
'@web/core/dropdown/dropdown_hooks'       // 下拉框钩子

// 核心依赖
'@mail/core/public_web/discuss_sidebar'   // 讨论侧边栏注册表
'@mail/utils/common/hooks'                // 邮件工具钩子
'@odoo/owl'                              // OWL 框架
'@web/core/utils/hooks'                  // Web 核心钩子
'@web/core/utils/misc'                   // 工具函数
```

## 组件定义

### 1. Mailbox 组件

```javascript
class Mailbox extends Component {
    static template = "mail.Mailbox";
    static props = ["mailbox"];
    static components = { Dropdown, ThreadIcon };
}
```

**组件配置**:
- **模板**: "mail.Mailbox"
- **Props**: mailbox（邮箱对象）
- **子组件**: Dropdown（下拉框）、ThreadIcon（线程图标）

### 2. DiscussSidebarMailboxes 组件

```javascript
class DiscussSidebarMailboxes extends Component {
    static template = "mail.DiscussSidebarMailboxes";
    static props = {};
    static components = { Mailbox };
}
```

**组件配置**:
- **模板**: "mail.DiscussSidebarMailboxes"
- **Props**: 无特定props
- **子组件**: Mailbox（邮箱组件）

## 核心功能

### 1. Mailbox 组件设置

```javascript
setup() {
    super.setup();
    this.store = useState(useService("mail.store"));
    this.hover = useHover(["root", "floating*"], {
        onHover: () => (this.floating.isOpen = true),
        onAway: () => (this.floating.isOpen = false),
    });
    this.floating = useDropdownState();
    this.rootRef = useRef("root");
}
```

**初始化内容**:
- 邮件存储服务的响应式状态
- 悬停效果配置（悬停时打开下拉框，离开时关闭）
- 下拉框状态管理
- 根元素引用

### 2. 邮箱属性获取

```javascript
/** @returns {import("models").Thread} */
get mailbox() {
    return this.props.mailbox;
}
```

**属性获取**:
- 返回props中的邮箱对象
- 类型为Thread模型实例

### 3. 线程打开

```javascript
/** @param {MouseEvent} ev */
openThread(ev) {
    markEventHandled(ev, "sidebar.openThread");
    this.mailbox.setAsDiscussThread();
}
```

**打开逻辑**:
- 标记事件已处理，防止事件冒泡
- 将邮箱设置为当前讨论线程

### 4. DiscussSidebarMailboxes 设置

```javascript
setup() {
    super.setup();
    this.store = useState(useService("mail.store"));
}
```

**初始化内容**:
- 邮件存储服务的响应式状态

### 5. 侧边栏注册

```javascript
discussSidebarItemsRegistry.add("mailbox", DiscussSidebarMailboxes, { sequence: 20 });
```

**注册配置**:
- **ID**: "mailbox"
- **组件**: DiscussSidebarMailboxes
- **序列**: 20（控制在侧边栏中的显示顺序）

## 使用场景

### 1. 讨论应用侧边栏集成

```javascript
// 讨论应用侧边栏邮箱集成
const DiscussSidebarIntegration = ({ store }) => {
    const [selectedMailbox, setSelectedMailbox] = useState(null);
    const [mailboxes, setMailboxes] = useState([]);
    
    useEffect(() => {
        // 获取邮箱列表
        const loadMailboxes = () => {
            const availableMailboxes = [
                store.inbox,
                store.starred,
                store.history,
                store.sent
            ].filter(Boolean);
            
            setMailboxes(availableMailboxes);
        };
        
        loadMailboxes();
        
        // 监听存储变化
        store.addEventListener('change', loadMailboxes);
        
        return () => {
            store.removeEventListener('change', loadMailboxes);
        };
    }, [store]);
    
    const handleMailboxSelect = (mailbox) => {
        setSelectedMailbox(mailbox);
        
        // 设置为当前讨论线程
        mailbox.setAsDiscussThread();
        
        // 记录选择操作
        logMailboxSelection(mailbox);
        
        // 更新导航历史
        updateNavigationHistory(mailbox);
    };
    
    return (
        <div class="discuss-sidebar-mailboxes">
            <h3>邮箱</h3>
            <div class="mailbox-list">
                {mailboxes.map(mailbox => (
                    <Mailbox
                        key={mailbox.id}
                        mailbox={mailbox}
                        selected={selectedMailbox?.id === mailbox.id}
                        onSelect={() => handleMailboxSelect(mailbox)}
                    />
                ))}
            </div>
        </div>
    );
};
```

### 2. 邮箱状态管理

```javascript
// 邮箱状态管理
const MailboxStateManager = {
    getMailboxState: (mailbox) => {
        return {
            id: mailbox.id,
            name: mailbox.name,
            counter: mailbox.counter,
            isActive: mailbox.isActive,
            hasUnread: mailbox.counter > 0,
            lastActivity: mailbox.lastActivity
        };
    },
    
    updateMailboxState: (mailbox, newState) => {
        Object.assign(mailbox, newState);
        
        // 触发状态更新事件
        mailbox.trigger('state_changed', newState);
    },
    
    getMailboxStats: (mailboxes) => {
        return mailboxes.reduce((stats, mailbox) => {
            stats.total += 1;
            stats.unread += mailbox.counter || 0;
            stats.active += mailbox.isActive ? 1 : 0;
            return stats;
        }, { total: 0, unread: 0, active: 0 });
    },
    
    sortMailboxes: (mailboxes, sortBy = 'sequence') => {
        const sortFunctions = {
            'sequence': (a, b) => (a.sequence || 0) - (b.sequence || 0),
            'name': (a, b) => a.name.localeCompare(b.name),
            'counter': (a, b) => (b.counter || 0) - (a.counter || 0),
            'activity': (a, b) => new Date(b.lastActivity) - new Date(a.lastActivity)
        };
        
        return [...mailboxes].sort(sortFunctions[sortBy] || sortFunctions.sequence);
    }
};
```

### 3. 悬停效果增强

```javascript
// 悬停效果增强
const HoverEffectEnhancer = {
    enhanceMailboxHover: (mailbox) => {
        const hoverConfig = {
            delay: 200,           // 悬停延迟
            hideDelay: 300,       // 隐藏延迟
            showPreview: true,    // 显示预览
            showActions: true     // 显示操作按钮
        };
        
        return useHover(["root", "floating*"], {
            onHover: () => {
                setTimeout(() => {
                    HoverEffectEnhancer.showMailboxPreview(mailbox);
                    mailbox.floating.isOpen = true;
                }, hoverConfig.delay);
            },
            onAway: () => {
                setTimeout(() => {
                    HoverEffectEnhancer.hideMailboxPreview(mailbox);
                    mailbox.floating.isOpen = false;
                }, hoverConfig.hideDelay);
            }
        });
    },
    
    showMailboxPreview: (mailbox) => {
        if (!mailbox.floating.isOpen) return;
        
        // 显示邮箱预览信息
        const preview = {
            name: mailbox.name,
            counter: mailbox.counter,
            recentMessages: mailbox.getRecentMessages(3),
            lastActivity: mailbox.lastActivity
        };
        
        HoverEffectEnhancer.renderPreview(mailbox, preview);
    },
    
    hideMailboxPreview: (mailbox) => {
        // 隐藏预览
        const previewElement = document.querySelector(`[data-mailbox-preview="${mailbox.id}"]`);
        if (previewElement) {
            previewElement.remove();
        }
    },
    
    renderPreview: (mailbox, preview) => {
        const previewHtml = `
            <div class="mailbox-preview" data-mailbox-preview="${mailbox.id}">
                <div class="preview-header">
                    <span class="name">${preview.name}</span>
                    <span class="counter">${preview.counter}</span>
                </div>
                <div class="preview-messages">
                    ${preview.recentMessages.map(msg => `
                        <div class="message-item">
                            <span class="author">${msg.author.name}</span>
                            <span class="subject">${msg.subject}</span>
                        </div>
                    `).join('')}
                </div>
                <div class="preview-actions">
                    <button onclick="openMailbox('${mailbox.id}')">打开</button>
                    <button onclick="markAsRead('${mailbox.id}')">标记已读</button>
                </div>
            </div>
        `;
        
        // 插入到DOM中
        const container = document.querySelector('.mailbox-preview-container');
        if (container) {
            container.innerHTML = previewHtml;
        }
    }
};
```

### 4. 邮箱操作菜单

```javascript
// 邮箱操作菜单
const MailboxActionMenu = {
    getActions: (mailbox) => {
        const actions = [];
        
        // 基础操作
        actions.push({
            id: 'open',
            name: '打开',
            icon: 'fa-folder-open',
            action: () => MailboxActionMenu.openMailbox(mailbox)
        });
        
        // 标记已读操作
        if (mailbox.counter > 0) {
            actions.push({
                id: 'mark_read',
                name: '标记全部已读',
                icon: 'fa-check',
                action: () => MailboxActionMenu.markAllAsRead(mailbox)
            });
        }
        
        // 刷新操作
        actions.push({
            id: 'refresh',
            name: '刷新',
            icon: 'fa-refresh',
            action: () => MailboxActionMenu.refreshMailbox(mailbox)
        });
        
        // 设置操作
        if (mailbox.isConfigurable) {
            actions.push({
                id: 'settings',
                name: '设置',
                icon: 'fa-cog',
                action: () => MailboxActionMenu.openSettings(mailbox)
            });
        }
        
        return actions;
    },
    
    openMailbox: (mailbox) => {
        mailbox.setAsDiscussThread();
        
        // 记录打开操作
        logMailboxAction('open', mailbox);
    },
    
    markAllAsRead: async (mailbox) => {
        try {
            await mailbox.markAllAsRead();
            showNotification('已标记全部为已读', 'success');
            
            // 记录操作
            logMailboxAction('mark_all_read', mailbox);
        } catch (error) {
            console.error('标记已读失败:', error);
            showNotification('操作失败，请重试', 'error');
        }
    },
    
    refreshMailbox: async (mailbox) => {
        try {
            await mailbox.refresh();
            showNotification('邮箱已刷新', 'success');
            
            // 记录操作
            logMailboxAction('refresh', mailbox);
        } catch (error) {
            console.error('刷新失败:', error);
            showNotification('刷新失败，请重试', 'error');
        }
    },
    
    openSettings: (mailbox) => {
        // 打开邮箱设置对话框
        const settingsDialog = new MailboxSettingsDialog({
            mailbox: mailbox,
            onSave: (settings) => {
                MailboxActionMenu.saveSettings(mailbox, settings);
            }
        });
        
        settingsDialog.open();
    },
    
    saveSettings: async (mailbox, settings) => {
        try {
            await mailbox.updateSettings(settings);
            showNotification('设置已保存', 'success');
        } catch (error) {
            console.error('保存设置失败:', error);
            showNotification('保存失败，请重试', 'error');
        }
    }
};
```

### 5. 邮箱搜索和过滤

```javascript
// 邮箱搜索和过滤
const MailboxSearchFilter = {
    filterMailboxes: (mailboxes, filters) => {
        return mailboxes.filter(mailbox => {
            // 名称过滤
            if (filters.name && !mailbox.name.toLowerCase().includes(filters.name.toLowerCase())) {
                return false;
            }
            
            // 状态过滤
            if (filters.hasUnread && mailbox.counter === 0) {
                return false;
            }
            
            // 类型过滤
            if (filters.type && mailbox.type !== filters.type) {
                return false;
            }
            
            // 活动状态过滤
            if (filters.isActive !== undefined && mailbox.isActive !== filters.isActive) {
                return false;
            }
            
            return true;
        });
    },
    
    searchMailboxes: (mailboxes, searchTerm) => {
        if (!searchTerm) return mailboxes;
        
        const term = searchTerm.toLowerCase();
        
        return mailboxes.filter(mailbox => 
            mailbox.name.toLowerCase().includes(term) ||
            mailbox.description?.toLowerCase().includes(term) ||
            mailbox.id.toLowerCase().includes(term)
        );
    },
    
    sortMailboxes: (mailboxes, sortConfig) => {
        const { field, direction } = sortConfig;
        
        return [...mailboxes].sort((a, b) => {
            let aValue = a[field];
            let bValue = b[field];
            
            // 处理不同数据类型
            if (typeof aValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }
            
            let result = 0;
            if (aValue < bValue) result = -1;
            else if (aValue > bValue) result = 1;
            
            return direction === 'desc' ? -result : result;
        });
    },
    
    groupMailboxes: (mailboxes, groupBy) => {
        const groups = {};
        
        mailboxes.forEach(mailbox => {
            const groupKey = mailbox[groupBy] || 'other';
            
            if (!groups[groupKey]) {
                groups[groupKey] = [];
            }
            
            groups[groupKey].push(mailbox);
        });
        
        return groups;
    }
};
```

## 注册表集成

### 1. 侧边栏项目注册

```javascript
// 侧边栏项目注册
discussSidebarItemsRegistry.add("mailbox", DiscussSidebarMailboxes, { 
    sequence: 20 
});
```

**注册参数**:
- **ID**: "mailbox"
- **组件**: DiscussSidebarMailboxes
- **序列**: 20（控制显示顺序）

### 2. 动态注册管理

```javascript
// 动态注册管理
const DynamicRegistryManager = {
    registerMailboxComponent: (id, component, options = {}) => {
        discussSidebarItemsRegistry.add(id, component, {
            sequence: options.sequence || 50,
            condition: options.condition,
            ...options
        });
    },
    
    unregisterMailboxComponent: (id) => {
        discussSidebarItemsRegistry.remove(id);
    },
    
    getRegisteredComponents: () => {
        return discussSidebarItemsRegistry.getAll();
    },
    
    reorderComponents: (newOrder) => {
        newOrder.forEach((id, index) => {
            const component = discussSidebarItemsRegistry.get(id);
            if (component) {
                discussSidebarItemsRegistry.add(id, component.Component, {
                    ...component.options,
                    sequence: (index + 1) * 10
                });
            }
        });
    }
};
```

## 性能优化

### 1. 邮箱数据缓存

```javascript
// 邮箱数据缓存
const MailboxDataCache = {
    cache: new Map(),
    
    getCachedData: (mailboxId) => {
        const cached = MailboxDataCache.cache.get(mailboxId);
        if (cached && Date.now() - cached.timestamp < 300000) { // 5分钟缓存
            return cached.data;
        }
        return null;
    },
    
    setCachedData: (mailboxId, data) => {
        MailboxDataCache.cache.set(mailboxId, {
            data,
            timestamp: Date.now()
        });
    },
    
    invalidateCache: (mailboxId) => {
        if (mailboxId) {
            MailboxDataCache.cache.delete(mailboxId);
        } else {
            MailboxDataCache.cache.clear();
        }
    }
};
```

### 2. 虚拟化渲染

```javascript
// 大量邮箱的虚拟化渲染
const VirtualizedMailboxList = ({ mailboxes, itemHeight = 40 }) => {
    const [visibleRange, setVisibleRange] = useState({ start: 0, end: 10 });
    
    const visibleMailboxes = mailboxes.slice(visibleRange.start, visibleRange.end);
    
    return (
        <div class="virtualized-mailbox-list">
            <div style={{ height: mailboxes.length * itemHeight }}>
                <div style={{ transform: `translateY(${visibleRange.start * itemHeight}px)` }}>
                    {visibleMailboxes.map(mailbox => (
                        <Mailbox key={mailbox.id} mailbox={mailbox} />
                    ))}
                </div>
            </div>
        </div>
    );
};
```

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的邮箱组件
- 清晰的组件层次结构

### 2. 注册表模式 (Registry Pattern)
- 通过注册表管理侧边栏项目
- 支持动态添加和移除

### 3. 观察者模式 (Observer Pattern)
- 监听邮箱状态变化
- 响应用户交互

## 注意事项

1. **性能考虑**: 避免频繁的DOM操作
2. **用户体验**: 提供流畅的悬停和点击体验
3. **数据一致性**: 确保邮箱状态的实时更新
4. **可访问性**: 支持键盘导航和屏幕阅读器

## 扩展建议

1. **拖拽排序**: 支持拖拽重新排序邮箱
2. **自定义邮箱**: 允许用户创建自定义邮箱
3. **邮箱分组**: 支持邮箱的分组显示
4. **快捷键**: 为邮箱操作添加快捷键支持
5. **主题定制**: 支持邮箱图标和颜色的定制

该组件为讨论应用提供了完整的邮箱侧边栏功能，是邮件管理的重要入口。

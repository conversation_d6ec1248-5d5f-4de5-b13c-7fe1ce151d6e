# Thread Model Patch - 线程模型补丁

## 概述

`thread_model_patch.js` 实现了 Odoo 邮件系统中线程模型的Web环境补丁，用于扩展线程模型在Web环境下的功能。该补丁添加了收件人管理、活动管理、聊天窗口控制、关注者加载、线程打开逻辑等功能，增强了线程模型的Web交互能力，支持不同设备和应用环境下的线程操作，是线程系统在Web环境下的重要功能扩展。

## 文件信息
- **路径**: `/mail/static/src/core/web/thread_model_patch.js`
- **行数**: 88
- **模块**: `@mail/core/web/thread_model_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/thread_model'  // 基础线程模型
'@web/core/utils/patch'           // 补丁工具
'@mail/core/common/record'        // 记录基类
'@mail/utils/common/misc'         // 通用工具函数
```

## 补丁实现

### 线程模型扩展

```javascript
patch(Thread.prototype, {
    // 新增属性和方法
    recipientsCount: undefined,
    setup() { /* 扩展设置 */ },
    // 其他方法...
});
```

## 新增属性

### 1. 收件人计数

```javascript
/** @type {integer|undefined} */
recipientsCount: undefined,
```

**收件人计数**:
- 记录线程的收件人总数
- 用于判断收件人是否完全加载

### 2. 收件人和活动关联

```javascript
setup() {
    super.setup();
    this.recipients = Record.many("Follower");
    this.activities = Record.many("Activity", {
        sort: (a, b) => compareDatetime(a.date_deadline, b.date_deadline) || a.id - b.id,
        onDelete(r) {
            r.remove();
        },
    });
}
```

**关联设置**:
- **收件人**: 多对多关联到关注者
- **活动**: 多对多关联到活动，按截止日期和ID排序
- **删除处理**: 活动删除时自动移除

## 核心方法

### 1. 收件人加载状态

```javascript
get recipientsFullyLoaded() {
    return this.recipientsCount === this.recipients.length;
}
```

**加载状态检查**:
- 比较收件人计数和已加载收件人数量
- 判断是否需要加载更多收件人

### 2. 关闭聊天窗口

```javascript
closeChatWindow() {
    const chatWindow = this.store.ChatWindow.get({ thread: this });
    chatWindow?.close({ notifyState: false });
}
```

**聊天窗口控制**:
- 获取对应的聊天窗口
- 关闭窗口但不通知状态变化

### 3. 显示状态计算

```javascript
computeIsDisplayed() {
    if (this.store.discuss.isActive && !this.store.env.services.ui.isSmall) {
        return this.eq(this.store.discuss.thread);
    }
    return super.computeIsDisplayed();
}
```

**显示逻辑**:
- 讨论应用激活且非小屏幕时，检查是否为当前讨论线程
- 其他情况使用父类逻辑

### 4. 离开线程

```javascript
async leave() {
    this.closeChatWindow();
    super.leave(...arguments);
}
```

**离开流程**:
- 关闭相关聊天窗口
- 调用父类离开方法

### 5. 加载更多关注者

```javascript
async loadMoreFollowers() {
    const data = await this.store.env.services.orm.call(this.model, "message_get_followers", [
        [this.id],
        this.followers.at(-1).id,
    ]);
    this.store.insert(data);
}
```

**关注者加载**:
- 调用服务器获取更多关注者
- 从最后一个关注者ID开始加载
- 将数据插入到存储中

### 6. 加载更多收件人

```javascript
async loadMoreRecipients() {
    const data = await this.store.env.services.orm.call(
        this.model,
        "message_get_followers",
        [[this.id], this.recipients.at(-1).id],
        { filter_recipients: true }
    );
    this.store.insert(data);
}
```

**收件人加载**:
- 调用服务器获取更多收件人
- 使用收件人过滤器
- 从最后一个收件人ID开始加载

### 7. 打开线程

```javascript
open(options) {
    if (!this.store.discuss.isActive && !this.store.env.services.ui.isSmall) {
        this.openChatWindow(options);
        return;
    }
    if (this.store.env.services.ui.isSmall && this.model === "discuss.channel") {
        this.openChatWindow(options);
        return;
    }
    if (this.model !== "discuss.channel") {
        this.store.env.services.action.doAction({
            type: "ir.actions.act_window",
            res_id: this.id,
            res_model: this.model,
            views: [[false, "form"]],
        });
        return;
    }
    super.open();
}
```

**打开逻辑**:
- **非讨论应用且非小屏幕**: 打开聊天窗口
- **小屏幕且为讨论频道**: 打开聊天窗口
- **非讨论频道**: 打开表单视图
- **其他情况**: 使用父类打开方法

### 8. 取消固定

```javascript
async unpin() {
    const chatWindow = this.store.ChatWindow.get({ thread: this });
    await chatWindow?.close();
    super.unpin(...arguments);
}
```

**取消固定流程**:
- 关闭相关聊天窗口
- 调用父类取消固定方法

## 使用场景

### 1. 线程收件人管理

```javascript
// 线程收件人管理
const ThreadRecipientManager = ({ thread }) => {
    const [loading, setLoading] = useState(false);
    
    const loadMoreRecipients = async () => {
        if (thread.recipientsFullyLoaded || loading) {
            return;
        }
        
        setLoading(true);
        try {
            await thread.loadMoreRecipients();
        } catch (error) {
            console.error('加载收件人失败:', error);
            showNotification('加载收件人失败', 'error');
        } finally {
            setLoading(false);
        }
    };
    
    return (
        <div class="thread-recipients">
            <div class="recipients-list">
                {thread.recipients.map(recipient => (
                    <div key={recipient.id} class="recipient-item">
                        {recipient.partner.name}
                    </div>
                ))}
            </div>
            
            {!thread.recipientsFullyLoaded && (
                <button 
                    onClick={loadMoreRecipients}
                    disabled={loading}
                    class="load-more-btn"
                >
                    {loading ? '加载中...' : '加载更多收件人'}
                </button>
            )}
        </div>
    );
};
```

### 2. 线程活动管理

```javascript
// 线程活动管理
const ThreadActivityManager = ({ thread }) => {
    const getActivityStats = () => {
        const activities = thread.activities;
        
        return {
            total: activities.length,
            overdue: activities.filter(a => a.state === 'overdue').length,
            today: activities.filter(a => a.state === 'today').length,
            planned: activities.filter(a => a.state === 'planned').length
        };
    };
    
    const stats = getActivityStats();
    
    return (
        <div class="thread-activities">
            <div class="activity-stats">
                <span>总计: {stats.total}</span>
                <span class="overdue">逾期: {stats.overdue}</span>
                <span class="today">今日: {stats.today}</span>
                <span class="planned">计划: {stats.planned}</span>
            </div>
            
            <div class="activities-list">
                {thread.activities.map(activity => (
                    <div key={activity.id} class="activity-item">
                        <span class={`state ${activity.state}`}>
                            {activity.summary}
                        </span>
                        <span class="deadline">
                            {activity.dateDeadlineFormatted}
                        </span>
                    </div>
                ))}
            </div>
        </div>
    );
};
```

### 3. 响应式线程打开

```javascript
// 响应式线程打开
const ResponsiveThreadOpener = ({ thread, store }) => {
    const openThread = (options = {}) => {
        const isSmallScreen = store.env.services.ui.isSmall;
        const isDiscussActive = store.discuss.isActive;
        
        // 记录打开操作
        logThreadOpen(thread, {
            isSmallScreen,
            isDiscussActive,
            model: thread.model
        });
        
        // 调用补丁方法
        thread.open(options);
        
        // 更新最近访问
        updateRecentThreads(thread);
    };
    
    const getOpenButtonText = () => {
        const isSmallScreen = store.env.services.ui.isSmall;
        const isDiscussActive = store.discuss.isActive;
        
        if (!isDiscussActive && !isSmallScreen) {
            return '在聊天窗口中打开';
        } else if (isSmallScreen && thread.model === "discuss.channel") {
            return '在聊天窗口中打开';
        } else if (thread.model !== "discuss.channel") {
            return '在表单视图中打开';
        } else {
            return '打开线程';
        }
    };
    
    return (
        <button onClick={openThread} class="open-thread-btn">
            {getOpenButtonText()}
        </button>
    );
};
```

### 4. 聊天窗口控制

```javascript
// 聊天窗口控制
const ChatWindowController = ({ thread, store }) => {
    const [hasWindow, setHasWindow] = useState(false);
    
    useEffect(() => {
        const chatWindow = store.ChatWindow.get({ thread });
        setHasWindow(!!chatWindow);
    }, [thread, store]);
    
    const closeChatWindow = () => {
        thread.closeChatWindow();
        setHasWindow(false);
    };
    
    const openChatWindow = (options = {}) => {
        thread.openChatWindow(options);
        setHasWindow(true);
    };
    
    return (
        <div class="chat-window-controls">
            {hasWindow ? (
                <button onClick={closeChatWindow} class="close-window-btn">
                    关闭聊天窗口
                </button>
            ) : (
                <button onClick={openChatWindow} class="open-window-btn">
                    打开聊天窗口
                </button>
            )}
        </div>
    );
};
```

### 5. 线程离开处理

```javascript
// 线程离开处理
const ThreadLeaveHandler = ({ thread }) => {
    const [leaving, setLeaving] = useState(false);
    
    const handleLeave = async () => {
        const confirmed = await showConfirmDialog(
            '确定要离开此线程吗？',
            '离开后您将不再接收此线程的消息通知。'
        );
        
        if (!confirmed) {
            return;
        }
        
        setLeaving(true);
        try {
            await thread.leave();
            showNotification('已离开线程', 'success');
        } catch (error) {
            console.error('离开线程失败:', error);
            showNotification('离开线程失败', 'error');
        } finally {
            setLeaving(false);
        }
    };
    
    return (
        <button 
            onClick={handleLeave}
            disabled={leaving}
            class="leave-thread-btn danger"
        >
            {leaving ? '离开中...' : '离开线程'}
        </button>
    );
};
```

## 设备适配

### 1. 屏幕尺寸检测

```javascript
// 屏幕尺寸适配
const ScreenSizeAdapter = {
    isSmallScreen: (uiService) => {
        return uiService.isSmall;
    },
    
    getOptimalOpenMethod: (thread, store) => {
        const isSmall = ScreenSizeAdapter.isSmallScreen(store.env.services.ui);
        const isDiscussActive = store.discuss.isActive;
        
        if (!isDiscussActive && !isSmall) {
            return 'chatWindow';
        } else if (isSmall && thread.model === "discuss.channel") {
            return 'chatWindow';
        } else if (thread.model !== "discuss.channel") {
            return 'formView';
        } else {
            return 'default';
        }
    }
};
```

### 2. 响应式布局

```javascript
// 响应式布局管理
const ResponsiveLayoutManager = {
    getLayoutConfig: (uiService) => {
        const isSmall = uiService.isSmall;
        
        return {
            showSidebar: !isSmall,
            useFullScreen: isSmall,
            chatWindowPosition: isSmall ? 'fullscreen' : 'floating',
            maxChatWindows: isSmall ? 1 : 3
        };
    },
    
    adaptThreadDisplay: (thread, layoutConfig) => {
        if (layoutConfig.useFullScreen) {
            // 全屏模式下的线程显示
            return {
                width: '100%',
                height: '100%',
                position: 'fixed'
            };
        } else {
            // 桌面模式下的线程显示
            return {
                width: '400px',
                height: '600px',
                position: 'relative'
            };
        }
    }
};
```

## 性能优化

### 1. 懒加载优化

```javascript
// 懒加载优化
const LazyLoadOptimizer = {
    shouldLoadMore: (thread, scrollPosition, threshold = 0.8) => {
        // 检查是否需要加载更多数据
        const scrollPercentage = scrollPosition / thread.scrollHeight;
        return scrollPercentage > threshold && !thread.recipientsFullyLoaded;
    },
    
    batchLoadRecipients: async (thread, batchSize = 20) => {
        // 批量加载收件人
        const promises = [];
        let loadCount = 0;
        
        while (!thread.recipientsFullyLoaded && loadCount < batchSize) {
            promises.push(thread.loadMoreRecipients());
            loadCount++;
        }
        
        return Promise.all(promises);
    }
};
```

### 2. 缓存管理

```javascript
// 线程数据缓存
const ThreadDataCache = {
    cache: new Map(),
    
    getCachedData: (threadId, dataType) => {
        const key = `${threadId}_${dataType}`;
        const cached = ThreadDataCache.cache.get(key);
        
        if (cached && Date.now() - cached.timestamp < 300000) { // 5分钟缓存
            return cached.data;
        }
        
        return null;
    },
    
    setCachedData: (threadId, dataType, data) => {
        const key = `${threadId}_${dataType}`;
        ThreadDataCache.cache.set(key, {
            data,
            timestamp: Date.now()
        });
    }
};
```

## 错误处理

### 1. 网络错误处理

```javascript
// 安全的数据加载
const safeLoadData = async (loadFunction, errorMessage) => {
    try {
        return await loadFunction();
    } catch (error) {
        console.error('数据加载失败:', error);
        
        if (error.message.includes('network')) {
            showNotification('网络连接错误，请重试', 'error');
        } else {
            showNotification(errorMessage, 'error');
        }
        
        throw error;
    }
};
```

### 2. 状态错误处理

```javascript
// 状态一致性检查
const validateThreadState = (thread) => {
    const warnings = [];
    
    if (thread.recipients.length > thread.recipientsCount) {
        warnings.push('收件人数量不一致');
    }
    
    if (thread.activities.some(a => !a.res_id)) {
        warnings.push('存在无效的活动记录');
    }
    
    return warnings;
};
```

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 非侵入式功能扩展
- 保持原有模型的完整性

### 2. 适配器模式 (Adapter Pattern)
- 适配不同设备和环境
- 统一的接口调用

### 3. 策略模式 (Strategy Pattern)
- 不同环境下的不同打开策略
- 可配置的行为模式

## 注意事项

1. **设备适配**: 正确处理不同设备的显示逻辑
2. **性能考虑**: 避免频繁的数据加载
3. **状态一致性**: 保持线程状态的一致性
4. **用户体验**: 提供流畅的交互体验

## 扩展建议

1. **智能预加载**: 基于用户行为的智能数据预加载
2. **离线支持**: 支持离线状态下的基本功能
3. **手势支持**: 移动设备上的手势操作
4. **主题适配**: 支持不同主题的线程显示
5. **无障碍优化**: 增强无障碍访问支持

该补丁为线程模型在Web环境下提供了重要的功能扩展，增强了线程的交互能力和用户体验。

# Mail Composer Attachment List - 邮件编写附件列表

## 概述

`mail_composer_attachment_list.js` 实现了 Odoo 邮件系统中邮件编写器的附件列表字段组件，用于在邮件编写界面中管理附件。该组件继承自Many2ManyBinaryField，专门为邮件编写场景定制了附件的显示和操作功能，支持多个附件的上传、预览、删除等操作，通过字段注册表系统集成到表单视图中，为用户提供了完整的邮件附件管理体验。

## 文件信息
- **路径**: `/mail/static/src/core/web/mail_composer_attachment_list.js`
- **行数**: 26
- **模块**: `@mail/core/web/mail_composer_attachment_list`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                                        // 注册表系统
'@web/views/fields/many2many_binary/many2many_binary_field' // 多对多二进制字段
```

## 组件定义

### MailComposerAttachmentList 类

```javascript
class MailComposerAttachmentList extends Many2ManyBinaryField {
    static template = "mail.MailComposerAttachmentList";
}
```

**组件特性**:
- 继承自Many2ManyBinaryField
- 使用自定义模板"mail.MailComposerAttachmentList"
- 专门为邮件编写场景优化

## 字段配置

### 字段定义

```javascript
const mailComposerAttachmentList = {
    ...many2ManyBinaryField,
    component: MailComposerAttachmentList,
};
```

**配置说明**:
- 继承many2ManyBinaryField的所有配置
- 使用自定义的MailComposerAttachmentList组件
- 保持原有字段的功能特性

## 注册表集成

### 字段注册

```javascript
registry.category("fields").add("mail_composer_attachment_list", mailComposerAttachmentList);
```

**注册参数**:
- **类别**: "fields"
- **名称**: "mail_composer_attachment_list"
- **配置**: mailComposerAttachmentList对象

## 使用场景

### 1. 邮件编写表单集成

```javascript
// 邮件编写表单中的附件列表
const MailComposerForm = {
    fields: {
        attachment_ids: {
            type: 'mail_composer_attachment_list',
            string: '附件',
            relation: 'ir.attachment',
            widget: 'mail_composer_attachment_list'
        }
    },
    
    setupAttachmentField: () => {
        return {
            name: 'attachment_ids',
            type: 'mail_composer_attachment_list',
            options: {
                accepted_file_extensions: '.pdf,.doc,.docx,.xls,.xlsx,.png,.jpg,.jpeg',
                max_file_size: 25 * 1024 * 1024, // 25MB
                multiple: true,
                preview: true
            }
        };
    }
};
```

### 2. 附件操作管理

```javascript
// 附件操作管理
const AttachmentOperationManager = {
    handleFileUpload: async (files, composer) => {
        const uploadPromises = Array.from(files).map(file => 
            AttachmentOperationManager.uploadSingleFile(file, composer)
        );
        
        try {
            const attachments = await Promise.all(uploadPromises);
            
            // 更新附件列表
            composer.updateAttachments(attachments);
            
            // 显示成功消息
            showNotification(`成功上传 ${attachments.length} 个附件`, 'success');
            
            return attachments;
        } catch (error) {
            console.error('附件上传失败:', error);
            showNotification('附件上传失败，请重试', 'error');
            throw error;
        }
    },
    
    uploadSingleFile: async (file, composer) => {
        // 验证文件
        const validation = AttachmentOperationManager.validateFile(file);
        if (!validation.isValid) {
            throw new Error(validation.error);
        }
        
        // 创建FormData
        const formData = new FormData();
        formData.append('file', file);
        formData.append('model', 'mail.compose.message');
        formData.append('id', composer.id || 0);
        
        // 上传文件
        const response = await fetch('/web/binary/upload_attachment', {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error('上传失败');
        }
        
        return await response.json();
    },
    
    validateFile: (file) => {
        const maxSize = 25 * 1024 * 1024; // 25MB
        const allowedTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'image/png',
            'image/jpeg',
            'image/jpg'
        ];
        
        if (file.size > maxSize) {
            return {
                isValid: false,
                error: '文件大小不能超过25MB'
            };
        }
        
        if (!allowedTypes.includes(file.type)) {
            return {
                isValid: false,
                error: '不支持的文件类型'
            };
        }
        
        return { isValid: true };
    },
    
    removeAttachment: async (attachmentId, composer) => {
        try {
            await composer.removeAttachment(attachmentId);
            showNotification('附件已删除', 'success');
        } catch (error) {
            console.error('删除附件失败:', error);
            showNotification('删除失败，请重试', 'error');
        }
    },
    
    previewAttachment: (attachment) => {
        if (AttachmentOperationManager.isImageFile(attachment)) {
            AttachmentOperationManager.showImagePreview(attachment);
        } else {
            AttachmentOperationManager.downloadAttachment(attachment);
        }
    },
    
    isImageFile: (attachment) => {
        const imageTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif'];
        return imageTypes.includes(attachment.mimetype);
    },
    
    showImagePreview: (attachment) => {
        const modal = createImagePreviewModal({
            title: attachment.name,
            imageUrl: `/web/content/${attachment.id}`,
            onDownload: () => AttachmentOperationManager.downloadAttachment(attachment)
        });
        
        modal.show();
    },
    
    downloadAttachment: (attachment) => {
        const link = document.createElement('a');
        link.href = `/web/content/${attachment.id}?download=true`;
        link.download = attachment.name;
        link.click();
    }
};
```

### 3. 拖拽上传功能

```javascript
// 拖拽上传功能
const DragDropUploader = {
    setupDragDrop: (element, composer) => {
        element.addEventListener('dragover', DragDropUploader.handleDragOver);
        element.addEventListener('dragleave', DragDropUploader.handleDragLeave);
        element.addEventListener('drop', (event) => 
            DragDropUploader.handleDrop(event, composer)
        );
    },
    
    handleDragOver: (event) => {
        event.preventDefault();
        event.stopPropagation();
        
        // 添加拖拽样式
        event.currentTarget.classList.add('drag-over');
    },
    
    handleDragLeave: (event) => {
        event.preventDefault();
        event.stopPropagation();
        
        // 移除拖拽样式
        event.currentTarget.classList.remove('drag-over');
    },
    
    handleDrop: async (event, composer) => {
        event.preventDefault();
        event.stopPropagation();
        
        // 移除拖拽样式
        event.currentTarget.classList.remove('drag-over');
        
        const files = event.dataTransfer.files;
        if (files.length > 0) {
            try {
                await AttachmentOperationManager.handleFileUpload(files, composer);
            } catch (error) {
                console.error('拖拽上传失败:', error);
            }
        }
    },
    
    addDropZone: (composer) => {
        const dropZone = document.createElement('div');
        dropZone.className = 'attachment-drop-zone';
        dropZone.innerHTML = `
            <div class="drop-zone-content">
                <i class="fa fa-cloud-upload"></i>
                <p>拖拽文件到此处上传</p>
                <p class="text-muted">或点击选择文件</p>
            </div>
        `;
        
        // 设置拖拽功能
        DragDropUploader.setupDragDrop(dropZone, composer);
        
        // 点击上传
        dropZone.addEventListener('click', () => {
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.multiple = true;
            fileInput.accept = '.pdf,.doc,.docx,.xls,.xlsx,.png,.jpg,.jpeg';
            
            fileInput.addEventListener('change', (event) => {
                const files = event.target.files;
                if (files.length > 0) {
                    AttachmentOperationManager.handleFileUpload(files, composer);
                }
            });
            
            fileInput.click();
        });
        
        return dropZone;
    }
};
```

### 4. 附件列表显示

```javascript
// 附件列表显示组件
const AttachmentListDisplay = {
    renderAttachmentList: (attachments, composer) => {
        const container = document.createElement('div');
        container.className = 'attachment-list';
        
        attachments.forEach(attachment => {
            const item = AttachmentListDisplay.renderAttachmentItem(attachment, composer);
            container.appendChild(item);
        });
        
        return container;
    },
    
    renderAttachmentItem: (attachment, composer) => {
        const item = document.createElement('div');
        item.className = 'attachment-item';
        item.innerHTML = `
            <div class="attachment-info">
                <i class="fa ${AttachmentListDisplay.getFileIcon(attachment)}"></i>
                <span class="filename">${attachment.name}</span>
                <span class="filesize">${AttachmentListDisplay.formatFileSize(attachment.file_size)}</span>
            </div>
            <div class="attachment-actions">
                <button class="btn btn-sm btn-secondary preview-btn" title="预览">
                    <i class="fa fa-eye"></i>
                </button>
                <button class="btn btn-sm btn-danger remove-btn" title="删除">
                    <i class="fa fa-trash"></i>
                </button>
            </div>
        `;
        
        // 绑定事件
        const previewBtn = item.querySelector('.preview-btn');
        const removeBtn = item.querySelector('.remove-btn');
        
        previewBtn.addEventListener('click', () => {
            AttachmentOperationManager.previewAttachment(attachment);
        });
        
        removeBtn.addEventListener('click', () => {
            AttachmentOperationManager.removeAttachment(attachment.id, composer);
            item.remove();
        });
        
        return item;
    },
    
    getFileIcon: (attachment) => {
        const iconMap = {
            'application/pdf': 'fa-file-pdf-o',
            'application/msword': 'fa-file-word-o',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'fa-file-word-o',
            'application/vnd.ms-excel': 'fa-file-excel-o',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'fa-file-excel-o',
            'image/png': 'fa-file-image-o',
            'image/jpeg': 'fa-file-image-o',
            'image/jpg': 'fa-file-image-o'
        };
        
        return iconMap[attachment.mimetype] || 'fa-file-o';
    },
    
    formatFileSize: (bytes) => {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    updateAttachmentList: (container, attachments, composer) => {
        // 清空现有列表
        container.innerHTML = '';
        
        // 重新渲染
        const newList = AttachmentListDisplay.renderAttachmentList(attachments, composer);
        container.appendChild(newList);
    }
};
```

### 5. 附件压缩和优化

```javascript
// 附件压缩和优化
const AttachmentOptimizer = {
    optimizeImage: async (file) => {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            img.onload = () => {
                // 计算压缩后的尺寸
                const { width, height } = AttachmentOptimizer.calculateDimensions(
                    img.width, 
                    img.height, 
                    1920, 
                    1080
                );
                
                canvas.width = width;
                canvas.height = height;
                
                // 绘制压缩后的图片
                ctx.drawImage(img, 0, 0, width, height);
                
                // 转换为Blob
                canvas.toBlob((blob) => {
                    resolve(new File([blob], file.name, {
                        type: file.type,
                        lastModified: Date.now()
                    }));
                }, file.type, 0.8); // 80% 质量
            };
            
            img.src = URL.createObjectURL(file);
        });
    },
    
    calculateDimensions: (originalWidth, originalHeight, maxWidth, maxHeight) => {
        let { width, height } = { width: originalWidth, height: originalHeight };
        
        // 如果图片尺寸超过最大限制，按比例缩放
        if (width > maxWidth || height > maxHeight) {
            const ratio = Math.min(maxWidth / width, maxHeight / height);
            width = Math.round(width * ratio);
            height = Math.round(height * ratio);
        }
        
        return { width, height };
    },
    
    shouldOptimize: (file) => {
        const imageTypes = ['image/jpeg', 'image/jpg', 'image/png'];
        const maxSize = 5 * 1024 * 1024; // 5MB
        
        return imageTypes.includes(file.type) && file.size > maxSize;
    },
    
    optimizeFile: async (file) => {
        if (AttachmentOptimizer.shouldOptimize(file)) {
            return await AttachmentOptimizer.optimizeImage(file);
        }
        
        return file;
    }
};
```

## 字段扩展

### 1. 自定义字段选项

```javascript
// 自定义字段选项
const CustomFieldOptions = {
    getDefaultOptions: () => {
        return {
            accepted_file_extensions: '.pdf,.doc,.docx,.xls,.xlsx,.png,.jpg,.jpeg',
            max_file_size: 25 * 1024 * 1024,
            multiple: true,
            preview: true,
            drag_drop: true,
            auto_optimize: true,
            show_progress: true
        };
    },
    
    mergeOptions: (defaultOptions, customOptions) => {
        return {
            ...defaultOptions,
            ...customOptions
        };
    },
    
    validateOptions: (options) => {
        const errors = [];
        
        if (options.max_file_size && options.max_file_size <= 0) {
            errors.push('最大文件大小必须大于0');
        }
        
        if (options.accepted_file_extensions && 
            typeof options.accepted_file_extensions !== 'string') {
            errors.push('接受的文件扩展名必须是字符串');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }
};
```

### 2. 字段状态管理

```javascript
// 字段状态管理
const FieldStateManager = {
    state: {
        uploading: false,
        uploadProgress: 0,
        attachments: [],
        errors: []
    },
    
    updateState: (newState) => {
        FieldStateManager.state = {
            ...FieldStateManager.state,
            ...newState
        };
        
        // 触发状态变化事件
        FieldStateManager.notifyStateChange();
    },
    
    notifyStateChange: () => {
        const event = new CustomEvent('attachment_state_change', {
            detail: FieldStateManager.state
        });
        
        document.dispatchEvent(event);
    },
    
    addAttachment: (attachment) => {
        const attachments = [...FieldStateManager.state.attachments, attachment];
        FieldStateManager.updateState({ attachments });
    },
    
    removeAttachment: (attachmentId) => {
        const attachments = FieldStateManager.state.attachments.filter(
            att => att.id !== attachmentId
        );
        FieldStateManager.updateState({ attachments });
    },
    
    setUploading: (uploading, progress = 0) => {
        FieldStateManager.updateState({ 
            uploading, 
            uploadProgress: progress 
        });
    },
    
    addError: (error) => {
        const errors = [...FieldStateManager.state.errors, error];
        FieldStateManager.updateState({ errors });
    },
    
    clearErrors: () => {
        FieldStateManager.updateState({ errors: [] });
    }
};
```

## 性能优化

### 1. 文件上传优化

```javascript
// 文件上传优化
const UploadOptimizer = {
    uploadWithProgress: async (file, onProgress) => {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            const formData = new FormData();
            formData.append('file', file);
            
            xhr.upload.addEventListener('progress', (event) => {
                if (event.lengthComputable) {
                    const progress = (event.loaded / event.total) * 100;
                    onProgress(progress);
                }
            });
            
            xhr.addEventListener('load', () => {
                if (xhr.status === 200) {
                    resolve(JSON.parse(xhr.responseText));
                } else {
                    reject(new Error('上传失败'));
                }
            });
            
            xhr.addEventListener('error', () => {
                reject(new Error('网络错误'));
            });
            
            xhr.open('POST', '/web/binary/upload_attachment');
            xhr.send(formData);
        });
    },
    
    batchUpload: async (files, onProgress) => {
        const results = [];
        let completedCount = 0;
        
        for (const file of files) {
            try {
                const result = await UploadOptimizer.uploadWithProgress(file, (progress) => {
                    const totalProgress = ((completedCount + progress / 100) / files.length) * 100;
                    onProgress(totalProgress);
                });
                
                results.push(result);
                completedCount++;
            } catch (error) {
                console.error(`上传文件 ${file.name} 失败:`, error);
                results.push({ error: error.message, file: file.name });
            }
        }
        
        return results;
    }
};
```

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- 继承Many2ManyBinaryField的功能
- 扩展邮件编写特定的功能

### 2. 注册表模式 (Registry Pattern)
- 通过字段注册表管理字段类型
- 支持动态字段注册

### 3. 策略模式 (Strategy Pattern)
- 不同文件类型的不同处理策略
- 可配置的上传和预览策略

## 注意事项

1. **文件大小限制**: 合理设置文件大小限制
2. **文件类型验证**: 严格验证上传的文件类型
3. **用户体验**: 提供清晰的上传进度和错误提示
4. **性能考虑**: 大文件上传的性能优化

## 扩展建议

1. **云存储集成**: 支持云存储服务的集成
2. **文件预览**: 增强文件预览功能
3. **批量操作**: 支持批量删除和下载
4. **版本控制**: 支持附件的版本管理
5. **权限控制**: 细粒度的附件访问权限控制

该组件为邮件编写提供了完整的附件管理功能，是邮件系统的重要组成部分。

# Activity - 活动组件

## 概述

`activity.js` 实现了 Odoo 邮件系统中的活动组件，用于显示和管理单个活动项目。该组件提供了活动的详细信息展示、状态管理、文件上传、标记完成等功能，支持弹出框交互、头像卡片、邮件模板等特性，是活动管理系统的核心UI组件，为用户提供了完整的活动操作体验。

## 文件信息
- **路径**: `/mail/static/src/core/web/activity.js`
- **行数**: 130
- **模块**: `@mail/core/web/activity`

## 依赖关系

```javascript
// UI组件依赖
'@mail/core/web/activity_mail_template'     // 活动邮件模板
'@mail/core/web/activity_markasdone_popover' // 标记完成弹出框
'@mail/discuss/web/avatar_card/avatar_card_popover' // 头像卡片弹出框

// 工具和钩子依赖
'@mail/core/common/attachment_uploader_hook' // 附件上传钩子
'@mail/utils/common/dates'                  // 日期工具
'@odoo/owl'                                // OWL 框架
'@web/core/browser/browser'                // 浏览器工具
'@web/core/l10n/translation'               // 国际化
'@web/core/popover/popover_hook'           // 弹出框钩子
'@web/core/utils/hooks'                    // Web 核心钩子
'@web/views/fields/file_handler'           // 文件处理器
```

## 组件定义

### Activity 类

```javascript
class Activity extends Component {
    static components = { ActivityMailTemplate, FileUploader };
    static props = ["activity", "onActivityChanged", "reloadParentView"];
    static template = "mail.Activity";
}
```

## Props 配置

### Props 详细说明

- **`activity`** (必需):
  - 类型: Activity 模型实例
  - 用途: 要显示的活动对象
  - 功能: 提供活动的所有数据和方法

- **`onActivityChanged`** (必需):
  - 类型: 函数
  - 用途: 活动变化时的回调
  - 参数: thread (线程对象)

- **`reloadParentView`** (必需):
  - 类型: 函数
  - 用途: 重新加载父视图的回调
  - 功能: 刷新包含此活动的视图

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    
    // 服务和状态
    this.storeService = useService("mail.store");
    this.state = useState({ showDetails: false });
    
    // 弹出框
    this.markDonePopover = usePopover(ActivityMarkAsDone, { position: "right" });
    this.avatarCard = usePopover(AvatarCardPopover);
    
    // 生命周期
    onMounted(() => {
        this.updateDelayAtNight();
    });
    onWillUnmount(() => browser.clearTimeout(this.updateDelayMidnightTimeout));
    
    // 附件上传
    this.attachmentUploader = useAttachmentUploader(this.thread);
}
```

**初始化内容**:
- 邮件存储服务
- 组件状态管理
- 弹出框配置
- 生命周期管理
- 附件上传功能

## 核心功能

### 1. 显示名称

```javascript
get displayName() {
    if (this.props.activity.summary) {
        return _t(""%s"", this.props.activity.summary);
    }
    return this.props.activity.display_name;
}
```

**显示逻辑**:
- 优先显示活动摘要（带引号）
- 回退到活动显示名称

### 2. 延迟计算

```javascript
get delay() {
    return computeDelay(this.props.activity.date_deadline);
}

updateDelayAtNight() {
    browser.clearTimeout(this.updateDelayMidnightTimeout);
    this.updateDelayMidnightTimeout = browser.setTimeout(
        () => this.render(),
        getMsToTomorrow() + 100
    );
}
```

**延迟管理**:
- 计算活动截止日期的延迟
- 午夜自动更新延迟显示
- 防止竞态条件

### 3. 详情切换

```javascript
toggleDetails() {
    this.state.showDetails = !this.state.showDetails;
}
```

**详情展示**:
- 切换详细信息的显示状态
- 支持展开/折叠操作

### 4. 标记完成

```javascript
async onClickMarkAsDone(ev) {
    if (this.markDonePopover.isOpen) {
        this.markDonePopover.close();
        return;
    }
    this.markDonePopover.open(ev.currentTarget, {
        activity: this.props.activity,
        hasHeader: true,
        onActivityChanged: this.props.onActivityChanged,
    });
}
```

**标记完成流程**:
- 检查弹出框状态
- 打开标记完成弹出框
- 传递活动和回调参数

### 5. 文件上传

```javascript
async onFileUploaded(data) {
    const thread = this.thread;
    const { id: attachmentId } = await this.attachmentUploader.uploadData(data, {
        activity: this.props.activity,
    });
    await this.props.activity.markAsDone([attachmentId]);
    this.props.onActivityChanged(thread);
    await thread.fetchNewMessages();
}
```

**上传流程**:
1. 上传文件数据
2. 获取附件ID
3. 标记活动完成并关联附件
4. 触发活动变化回调
5. 获取新消息

### 6. 头像交互

```javascript
onClickAvatar(ev) {
    const target = ev.currentTarget;
    if (!this.avatarCard.isOpen) {
        this.avatarCard.open(target, {
            id: this.props.activity.persona.userId,
        });
    }
}
```

**头像功能**:
- 点击头像显示用户卡片
- 传递用户ID参数

### 7. 活动编辑

```javascript
async edit() {
    const thread = this.thread;
    await this.props.activity.edit();
    this.props.onActivityChanged(thread);
}
```

**编辑流程**:
- 调用活动编辑方法
- 触发活动变化回调

### 8. 活动删除

```javascript
async unlink() {
    const thread = this.thread;
    this.props.activity.remove();
    await this.env.services.orm.unlink("mail.activity", [this.props.activity.id]);
    this.props.onActivityChanged(thread);
}
```

**删除流程**:
1. 从本地移除活动
2. 调用ORM删除服务器记录
3. 触发活动变化回调

### 9. 线程获取

```javascript
get thread() {
    return this.env.services["mail.store"].Thread.insert({
        model: this.props.activity.res_model,
        id: this.props.activity.res_id,
    });
}
```

**线程关联**:
- 根据活动的资源模型和ID获取线程
- 确保线程存在于存储中

### 10. 链接点击处理

```javascript
async onClick(ev) {
    this.storeService.handleClickOnLink(ev, this.thread);
}
```

**链接处理**:
- 委托给存储服务处理链接点击
- 传递事件和线程参数

## 使用场景

### 1. 活动列表项

```javascript
// 在活动列表中显示单个活动
const ActivityListItem = ({ activity, onUpdate, onReload }) => {
    return (
        <Activity
            activity={activity}
            onActivityChanged={onUpdate}
            reloadParentView={onReload}
        />
    );
};
```

### 2. 活动详情视图

```javascript
// 活动详情页面
const ActivityDetailView = ({ activityId }) => {
    const activity = useActivity(activityId);
    
    const handleActivityChanged = (thread) => {
        // 更新相关数据
        updateRelatedData(thread);
        
        // 刷新活动列表
        refreshActivityList();
    };
    
    return (
        <div class="activity-detail">
            <Activity
                activity={activity}
                onActivityChanged={handleActivityChanged}
                reloadParentView={() => window.location.reload()}
            />
        </div>
    );
};
```

### 3. 嵌入式活动组件

```javascript
// 嵌入到其他视图中的活动组件
const EmbeddedActivity = ({ activity, compact = false }) => {
    const handleChange = (thread) => {
        // 轻量级更新
        updateActivityStatus(activity.id);
    };
    
    return (
        <div class={`embedded-activity ${compact ? 'compact' : ''}`}>
            <Activity
                activity={activity}
                onActivityChanged={handleChange}
                reloadParentView={() => {}}
            />
        </div>
    );
};
```

### 4. 批量活动管理

```javascript
// 批量活动操作
const BatchActivityManager = ({ activities }) => {
    const [selectedActivities, setSelectedActivities] = useState([]);
    
    const handleActivityChanged = (thread) => {
        // 更新选中状态
        updateSelectedActivities(thread);
        
        // 刷新批量操作状态
        refreshBatchOperations();
    };
    
    return (
        <div class="batch-activity-manager">
            {activities.map(activity => (
                <div key={activity.id} class="activity-item">
                    <input
                        type="checkbox"
                        checked={selectedActivities.includes(activity.id)}
                        onChange={(e) => toggleSelection(activity.id, e.target.checked)}
                    />
                    <Activity
                        activity={activity}
                        onActivityChanged={handleActivityChanged}
                        reloadParentView={refreshAllActivities}
                    />
                </div>
            ))}
        </div>
    );
};
```

## 状态管理

### 1. 详情显示状态

```javascript
// 管理详情显示状态
const manageDetailsState = (activity) => {
    const [showDetails, setShowDetails] = useState(false);
    
    const toggleDetails = () => {
        setShowDetails(!showDetails);
        
        // 记录用户偏好
        localStorage.setItem(
            `activity_details_${activity.id}`,
            (!showDetails).toString()
        );
    };
    
    // 恢复用户偏好
    useEffect(() => {
        const saved = localStorage.getItem(`activity_details_${activity.id}`);
        if (saved !== null) {
            setShowDetails(saved === 'true');
        }
    }, [activity.id]);
    
    return { showDetails, toggleDetails };
};
```

### 2. 弹出框状态

```javascript
// 弹出框状态管理
const managePopoverState = () => {
    const [activePopover, setActivePopover] = useState(null);
    
    const openPopover = (type, target, props) => {
        // 关闭其他弹出框
        if (activePopover && activePopover !== type) {
            closePopover(activePopover);
        }
        
        setActivePopover(type);
        // 打开指定弹出框
    };
    
    const closePopover = (type) => {
        if (activePopover === type) {
            setActivePopover(null);
        }
    };
    
    return { activePopover, openPopover, closePopover };
};
```

## 文件处理

### 1. 文件上传处理

```javascript
// 文件上传处理
const handleFileUpload = async (activity, file) => {
    try {
        // 验证文件
        validateFile(file);
        
        // 显示上传进度
        showUploadProgress(true);
        
        // 上传文件
        const result = await uploadFile(file, {
            activity: activity,
            onProgress: updateUploadProgress
        });
        
        // 标记活动完成
        await activity.markAsDone([result.attachmentId]);
        
        // 显示成功消息
        showSuccessMessage('文件上传成功，活动已完成');
        
    } catch (error) {
        showErrorMessage(`上传失败: ${error.message}`);
    } finally {
        showUploadProgress(false);
    }
};

// 文件验证
const validateFile = (file) => {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/*', 'application/pdf', 'text/*'];
    
    if (file.size > maxSize) {
        throw new Error('文件大小不能超过10MB');
    }
    
    const isAllowed = allowedTypes.some(type => {
        if (type.endsWith('*')) {
            return file.type.startsWith(type.slice(0, -1));
        }
        return file.type === type;
    });
    
    if (!isAllowed) {
        throw new Error('不支持的文件类型');
    }
};
```

### 2. 拖拽上传

```javascript
// 拖拽上传功能
const setupDragAndDrop = (element, onFileUploaded) => {
    element.addEventListener('dragover', (e) => {
        e.preventDefault();
        element.classList.add('drag-over');
    });
    
    element.addEventListener('dragleave', (e) => {
        e.preventDefault();
        element.classList.remove('drag-over');
    });
    
    element.addEventListener('drop', async (e) => {
        e.preventDefault();
        element.classList.remove('drag-over');
        
        const files = Array.from(e.dataTransfer.files);
        for (const file of files) {
            await onFileUploaded(file);
        }
    });
};
```

## 时间管理

### 1. 延迟计算

```javascript
// 延迟计算和显示
const calculateDelay = (deadline) => {
    const now = new Date();
    const deadlineDate = new Date(deadline);
    const diffMs = deadlineDate - now;
    const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
        return {
            type: 'overdue',
            value: Math.abs(diffDays),
            text: `逾期 ${Math.abs(diffDays)} 天`
        };
    } else if (diffDays === 0) {
        return {
            type: 'today',
            value: 0,
            text: '今天到期'
        };
    } else if (diffDays === 1) {
        return {
            type: 'tomorrow',
            value: 1,
            text: '明天到期'
        };
    } else {
        return {
            type: 'future',
            value: diffDays,
            text: `${diffDays} 天后到期`
        };
    }
};
```

### 2. 自动更新

```javascript
// 自动更新延迟显示
const setupAutoUpdate = (component) => {
    const updateAtMidnight = () => {
        const now = new Date();
        const tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);
        
        const msToMidnight = tomorrow - now;
        
        setTimeout(() => {
            component.render();
            updateAtMidnight(); // 设置下一次更新
        }, msToMidnight + 100); // 加100ms避免竞态条件
    };
    
    updateAtMidnight();
};
```

## 性能优化

### 1. 组件缓存

```javascript
// 组件级缓存
const ActivityCache = new Map();

const getCachedActivity = (activityId) => {
    if (ActivityCache.has(activityId)) {
        const cached = ActivityCache.get(activityId);
        if (Date.now() - cached.timestamp < 60000) { // 1分钟缓存
            return cached.component;
        }
    }
    return null;
};

const setCachedActivity = (activityId, component) => {
    ActivityCache.set(activityId, {
        component,
        timestamp: Date.now()
    });
};
```

### 2. 事件防抖

```javascript
// 防抖处理
const debouncedHandlers = {
    onActivityChanged: debounce((thread) => {
        updateActivityList(thread);
    }, 300),
    
    onFileUploaded: debounce((data) => {
        processFileUpload(data);
    }, 100)
};
```

## 可访问性

### 1. 键盘导航

```javascript
// 键盘导航支持
const handleKeyDown = (event, activity) => {
    switch (event.key) {
        case 'Enter':
        case ' ':
            event.preventDefault();
            toggleDetails();
            break;
        case 'Delete':
            event.preventDefault();
            if (confirm('确定要删除此活动吗？')) {
                unlink();
            }
            break;
        case 'e':
            if (event.ctrlKey || event.metaKey) {
                event.preventDefault();
                edit();
            }
            break;
        case 'm':
            if (event.ctrlKey || event.metaKey) {
                event.preventDefault();
                markAsDone();
            }
            break;
    }
};
```

### 2. ARIA 标签

```javascript
// 可访问性属性
const getAccessibilityProps = (activity) => {
    return {
        'role': 'article',
        'aria-label': `活动: ${activity.display_name}`,
        'aria-describedby': `activity-description-${activity.id}`,
        'tabindex': '0'
    };
};
```

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的活动显示组件
- 统一的活动操作接口

### 2. 观察者模式 (Observer Pattern)
- 监听活动状态变化
- 响应用户操作

### 3. 策略模式 (Strategy Pattern)
- 不同类型活动的不同显示策略
- 可配置的操作行为

## 注意事项

1. **内存管理**: 及时清理定时器和事件监听器
2. **用户体验**: 提供清晰的操作反馈
3. **数据一致性**: 确保活动状态的同步
4. **性能考虑**: 避免不必要的重新渲染

## 扩展建议

1. **批量操作**: 支持批量标记完成和删除
2. **自定义字段**: 支持显示自定义活动字段
3. **模板系统**: 支持活动显示模板
4. **通知集成**: 集成桌面和邮件通知
5. **移动端优化**: 优化移动设备上的交互体验

该组件为活动管理提供了完整的用户界面，支持丰富的交互功能和良好的用户体验。

# Discuss Sidebar Patch - 讨论侧边栏补丁

## 概述

`discuss_sidebar_patch.js` 实现了 Odoo 邮件系统中讨论侧边栏的Web环境补丁，用于扩展讨论侧边栏的功能。该补丁主要添加了会议功能相关的UI服务、悬停效果和下拉状态管理，为讨论侧边栏增加了"开始会议"功能，提供了更丰富的协作工具，是讨论应用在Web环境下的重要功能扩展。

## 文件信息
- **路径**: `/mail/static/src/core/web/discuss_sidebar_patch.js`
- **行数**: 32
- **模块**: `@mail/core/web/discuss_sidebar_patch`

## 依赖关系

```javascript
// UI组件依赖
'@mail/core/public_web/discuss_sidebar'   // 基础讨论侧边栏
'@web/core/dropdown/dropdown_hooks'       // 下拉框钩子

// 核心依赖
'@web/core/utils/patch'                   // 补丁工具
'@web/core/l10n/translation'              // 国际化
'@odoo/owl'                              // OWL 框架
'@web/core/utils/hooks'                  // Web 核心钩子
'@mail/utils/common/hooks'               // 邮件工具钩子
```

## 补丁实现

### 讨论侧边栏原型补丁

```javascript
patch(DiscussSidebar.prototype, {
    setup() {
        super.setup();
        this.ui = useState(useService("ui"));
        this.meetingHover = useHover(["meeting-btn", "meeting-floating*"], {
            onHover: () => (this.meetingFloating.isOpen = true),
            onAway: () => (this.meetingFloating.isOpen = false),
        });
        this.meetingFloating = useDropdownState();
    },
    get startMeetingText() {
        return _t("Start a meeting");
    },
});
```

## 核心功能

### 1. 设置扩展

```javascript
setup() {
    super.setup();
    this.ui = useState(useService("ui"));
    this.meetingHover = useHover(["meeting-btn", "meeting-floating*"], {
        onHover: () => (this.meetingFloating.isOpen = true),
        onAway: () => (this.meetingFloating.isOpen = false),
    });
    this.meetingFloating = useDropdownState();
}
```

**初始化内容**:
- 调用父类设置方法
- UI服务的响应式状态
- 会议按钮的悬停效果配置
- 会议下拉框状态管理

### 2. 会议悬停效果

```javascript
this.meetingHover = useHover(["meeting-btn", "meeting-floating*"], {
    onHover: () => (this.meetingFloating.isOpen = true),
    onAway: () => (this.meetingFloating.isOpen = false),
});
```

**悬停配置**:
- **目标元素**: "meeting-btn"和"meeting-floating*"
- **悬停时**: 打开会议下拉框
- **离开时**: 关闭会议下拉框

### 3. 会议文本获取

```javascript
get startMeetingText() {
    return _t("Start a meeting");
}
```

**文本属性**:
- 返回国际化的"开始会议"文本
- 支持多语言显示

## 使用场景

### 1. 会议功能集成

```javascript
// 会议功能集成管理
const MeetingIntegration = {
    setupMeetingFeatures: (sidebar) => {
        // 添加会议按钮
        MeetingIntegration.addMeetingButton(sidebar);
        
        // 设置会议提供商
        MeetingIntegration.setupMeetingProviders(sidebar);
        
        // 添加会议历史
        MeetingIntegration.addMeetingHistory(sidebar);
    },
    
    addMeetingButton: (sidebar) => {
        const meetingButton = document.createElement('button');
        meetingButton.className = 'meeting-btn btn btn-primary';
        meetingButton.innerHTML = `
            <i class="fa fa-video-camera"></i>
            ${sidebar.startMeetingText}
        `;
        
        meetingButton.addEventListener('click', () => {
            MeetingIntegration.startMeeting(sidebar);
        });
        
        // 添加到侧边栏
        const sidebarElement = document.querySelector('.discuss-sidebar');
        if (sidebarElement) {
            sidebarElement.appendChild(meetingButton);
        }
    },
    
    setupMeetingProviders: (sidebar) => {
        const providers = [
            {
                id: 'jitsi',
                name: 'Jitsi Meet',
                icon: 'fa-video-camera',
                url: 'https://meet.jit.si/'
            },
            {
                id: 'zoom',
                name: 'Zoom',
                icon: 'fa-video-camera',
                url: 'https://zoom.us/start/videomeeting'
            },
            {
                id: 'teams',
                name: 'Microsoft Teams',
                icon: 'fa-video-camera',
                url: 'https://teams.microsoft.com/'
            }
        ];
        
        sidebar.meetingProviders = providers;
    },
    
    startMeeting: (sidebar) => {
        const defaultProvider = sidebar.meetingProviders[0];
        const meetingUrl = MeetingIntegration.generateMeetingUrl(defaultProvider);
        
        // 打开会议窗口
        window.open(meetingUrl, '_blank', 'width=1200,height=800');
        
        // 记录会议开始
        MeetingIntegration.logMeetingStart(meetingUrl);
        
        // 通知其他用户
        MeetingIntegration.notifyMeetingStart(sidebar, meetingUrl);
    },
    
    generateMeetingUrl: (provider) => {
        const roomId = MeetingIntegration.generateRoomId();
        
        switch (provider.id) {
            case 'jitsi':
                return `${provider.url}${roomId}`;
            case 'zoom':
                return `${provider.url}?confno=${roomId}`;
            case 'teams':
                return `${provider.url}?meetingId=${roomId}`;
            default:
                return provider.url;
        }
    },
    
    generateRoomId: () => {
        return 'odoo-meeting-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    },
    
    logMeetingStart: (meetingUrl) => {
        console.log('会议开始:', meetingUrl);
        
        // 发送到分析服务
        sendAnalytics('meeting_started', {
            url: meetingUrl,
            timestamp: Date.now(),
            userId: getCurrentUser().id
        });
    },
    
    notifyMeetingStart: (sidebar, meetingUrl) => {
        // 通知当前线程的参与者
        const currentThread = sidebar.store.discuss.thread;
        if (currentThread) {
            const notification = {
                type: 'meeting_invitation',
                message: `会议已开始: ${meetingUrl}`,
                url: meetingUrl,
                threadId: currentThread.id
            };
            
            // 发送通知
            sidebar.store.messaging.notify(notification);
        }
    }
};
```

### 2. 会议下拉菜单

```javascript
// 会议下拉菜单组件
const MeetingDropdownMenu = {
    renderDropdown: (sidebar) => {
        const dropdown = document.createElement('div');
        dropdown.className = 'meeting-dropdown';
        dropdown.innerHTML = `
            <div class="dropdown-header">
                <h4>会议选项</h4>
            </div>
            <div class="dropdown-body">
                <div class="meeting-providers">
                    ${MeetingDropdownMenu.renderProviders(sidebar)}
                </div>
                <div class="meeting-settings">
                    ${MeetingDropdownMenu.renderSettings(sidebar)}
                </div>
                <div class="meeting-history">
                    ${MeetingDropdownMenu.renderHistory(sidebar)}
                </div>
            </div>
        `;
        
        return dropdown;
    },
    
    renderProviders: (sidebar) => {
        return sidebar.meetingProviders.map(provider => `
            <div class="provider-item" data-provider-id="${provider.id}">
                <i class="fa ${provider.icon}"></i>
                <span class="provider-name">${provider.name}</span>
                <button class="btn btn-sm btn-primary start-btn">开始</button>
            </div>
        `).join('');
    },
    
    renderSettings: (sidebar) => {
        return `
            <div class="settings-section">
                <h5>会议设置</h5>
                <div class="setting-item">
                    <label>
                        <input type="checkbox" id="auto-join-audio"> 自动加入音频
                    </label>
                </div>
                <div class="setting-item">
                    <label>
                        <input type="checkbox" id="auto-join-video"> 自动开启视频
                    </label>
                </div>
                <div class="setting-item">
                    <label>
                        <input type="checkbox" id="record-meeting"> 录制会议
                    </label>
                </div>
            </div>
        `;
    },
    
    renderHistory: (sidebar) => {
        const recentMeetings = MeetingDropdownMenu.getRecentMeetings();
        
        return `
            <div class="history-section">
                <h5>最近会议</h5>
                <div class="meeting-list">
                    ${recentMeetings.map(meeting => `
                        <div class="meeting-item">
                            <span class="meeting-title">${meeting.title}</span>
                            <span class="meeting-time">${meeting.time}</span>
                            <button class="btn btn-sm btn-secondary rejoin-btn" 
                                    data-meeting-url="${meeting.url}">重新加入</button>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    },
    
    getRecentMeetings: () => {
        // 从本地存储获取最近会议
        const stored = localStorage.getItem('recent_meetings');
        if (stored) {
            return JSON.parse(stored).slice(0, 5);
        }
        return [];
    },
    
    bindEvents: (dropdown, sidebar) => {
        // 绑定提供商按钮事件
        dropdown.querySelectorAll('.start-btn').forEach(btn => {
            btn.addEventListener('click', (event) => {
                const providerId = event.target.closest('.provider-item').dataset.providerId;
                const provider = sidebar.meetingProviders.find(p => p.id === providerId);
                MeetingIntegration.startMeetingWithProvider(provider);
            });
        });
        
        // 绑定重新加入按钮事件
        dropdown.querySelectorAll('.rejoin-btn').forEach(btn => {
            btn.addEventListener('click', (event) => {
                const meetingUrl = event.target.dataset.meetingUrl;
                window.open(meetingUrl, '_blank');
            });
        });
        
        // 绑定设置变化事件
        dropdown.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', (event) => {
                MeetingDropdownMenu.saveSetting(event.target.id, event.target.checked);
            });
        });
    },
    
    saveSetting: (settingId, value) => {
        const settings = JSON.parse(localStorage.getItem('meeting_settings') || '{}');
        settings[settingId] = value;
        localStorage.setItem('meeting_settings', JSON.stringify(settings));
    }
};
```

### 3. UI响应式适配

```javascript
// UI响应式适配
const UIResponsiveAdapter = {
    adaptToScreenSize: (sidebar) => {
        const ui = sidebar.ui;
        
        if (ui.isSmall) {
            // 小屏幕适配
            UIResponsiveAdapter.adaptForMobile(sidebar);
        } else {
            // 大屏幕适配
            UIResponsiveAdapter.adaptForDesktop(sidebar);
        }
    },
    
    adaptForMobile: (sidebar) => {
        // 移动端会议按钮样式
        const meetingBtn = document.querySelector('.meeting-btn');
        if (meetingBtn) {
            meetingBtn.classList.add('mobile-style');
            meetingBtn.innerHTML = '<i class="fa fa-video-camera"></i>';
            meetingBtn.title = sidebar.startMeetingText;
        }
        
        // 移动端下拉菜单位置
        if (sidebar.meetingFloating) {
            sidebar.meetingFloating.position = 'bottom-start';
        }
    },
    
    adaptForDesktop: (sidebar) => {
        // 桌面端会议按钮样式
        const meetingBtn = document.querySelector('.meeting-btn');
        if (meetingBtn) {
            meetingBtn.classList.remove('mobile-style');
            meetingBtn.innerHTML = `
                <i class="fa fa-video-camera"></i>
                ${sidebar.startMeetingText}
            `;
        }
        
        // 桌面端下拉菜单位置
        if (sidebar.meetingFloating) {
            sidebar.meetingFloating.position = 'bottom-fit';
        }
    },
    
    watchScreenSize: (sidebar) => {
        // 监听屏幕尺寸变化
        window.addEventListener('resize', () => {
            UIResponsiveAdapter.adaptToScreenSize(sidebar);
        });
        
        // 初始适配
        UIResponsiveAdapter.adaptToScreenSize(sidebar);
    }
};
```

### 4. 会议状态管理

```javascript
// 会议状态管理
const MeetingStateManager = {
    state: {
        activeMeetings: [],
        meetingHistory: [],
        preferences: {}
    },
    
    addActiveMeeting: (meeting) => {
        MeetingStateManager.state.activeMeetings.push({
            id: meeting.id,
            url: meeting.url,
            startTime: Date.now(),
            participants: meeting.participants || []
        });
        
        MeetingStateManager.saveState();
    },
    
    endMeeting: (meetingId) => {
        const meetingIndex = MeetingStateManager.state.activeMeetings.findIndex(
            m => m.id === meetingId
        );
        
        if (meetingIndex !== -1) {
            const meeting = MeetingStateManager.state.activeMeetings[meetingIndex];
            meeting.endTime = Date.now();
            meeting.duration = meeting.endTime - meeting.startTime;
            
            // 移动到历史记录
            MeetingStateManager.state.meetingHistory.unshift(meeting);
            MeetingStateManager.state.activeMeetings.splice(meetingIndex, 1);
            
            // 限制历史记录数量
            if (MeetingStateManager.state.meetingHistory.length > 50) {
                MeetingStateManager.state.meetingHistory.pop();
            }
            
            MeetingStateManager.saveState();
        }
    },
    
    getActiveMeetings: () => {
        return MeetingStateManager.state.activeMeetings;
    },
    
    getMeetingHistory: (limit = 10) => {
        return MeetingStateManager.state.meetingHistory.slice(0, limit);
    },
    
    saveState: () => {
        try {
            localStorage.setItem(
                'meeting_state',
                JSON.stringify(MeetingStateManager.state)
            );
        } catch (error) {
            console.error('保存会议状态失败:', error);
        }
    },
    
    loadState: () => {
        try {
            const stored = localStorage.getItem('meeting_state');
            if (stored) {
                MeetingStateManager.state = {
                    ...MeetingStateManager.state,
                    ...JSON.parse(stored)
                };
            }
        } catch (error) {
            console.error('加载会议状态失败:', error);
        }
    }
};
```

### 5. 会议通知系统

```javascript
// 会议通知系统
const MeetingNotificationSystem = {
    sendMeetingInvitation: (meeting, participants) => {
        participants.forEach(participant => {
            const notification = {
                type: 'meeting_invitation',
                title: '会议邀请',
                message: `您被邀请参加会议: ${meeting.title}`,
                data: {
                    meetingId: meeting.id,
                    meetingUrl: meeting.url,
                    startTime: meeting.startTime
                },
                actions: [
                    {
                        action: 'join',
                        title: '加入会议',
                        icon: '/static/img/meeting-join.png'
                    },
                    {
                        action: 'decline',
                        title: '拒绝',
                        icon: '/static/img/meeting-decline.png'
                    }
                ]
            };
            
            MeetingNotificationSystem.sendNotification(participant, notification);
        });
    },
    
    sendNotification: (participant, notification) => {
        // 浏览器通知
        if (Notification.permission === 'granted') {
            const browserNotification = new Notification(notification.title, {
                body: notification.message,
                icon: '/static/img/meeting-icon.png',
                data: notification.data
            });
            
            browserNotification.onclick = () => {
                if (notification.data.meetingUrl) {
                    window.open(notification.data.meetingUrl, '_blank');
                }
            };
        }
        
        // 应用内通知
        const inAppNotification = {
            ...notification,
            timestamp: Date.now(),
            read: false
        };
        
        MeetingNotificationSystem.addInAppNotification(participant, inAppNotification);
    },
    
    addInAppNotification: (participant, notification) => {
        // 添加到通知列表
        const notifications = JSON.parse(
            localStorage.getItem(`notifications_${participant.id}`) || '[]'
        );
        
        notifications.unshift(notification);
        
        // 限制通知数量
        if (notifications.length > 100) {
            notifications.pop();
        }
        
        localStorage.setItem(
            `notifications_${participant.id}`,
            JSON.stringify(notifications)
        );
    },
    
    requestNotificationPermission: () => {
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    console.log('通知权限已授予');
                }
            });
        }
    }
};
```

## 悬停效果增强

### 1. 悬停动画

```javascript
// 悬停动画增强
const HoverAnimationEnhancer = {
    enhanceMeetingHover: (sidebar) => {
        const meetingBtn = document.querySelector('.meeting-btn');
        if (!meetingBtn) return;
        
        // 添加CSS动画类
        meetingBtn.addEventListener('mouseenter', () => {
            meetingBtn.classList.add('hover-animation');
        });
        
        meetingBtn.addEventListener('mouseleave', () => {
            meetingBtn.classList.remove('hover-animation');
        });
    },
    
    addHoverStyles: () => {
        const style = document.createElement('style');
        style.textContent = `
            .meeting-btn.hover-animation {
                transform: scale(1.05);
                transition: transform 0.2s ease-out;
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            }
            
            .meeting-floating {
                animation: slideDown 0.3s ease-out;
            }
            
            @keyframes slideDown {
                from {
                    opacity: 0;
                    transform: translateY(-10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        
        document.head.appendChild(style);
    }
};
```

## 性能优化

### 1. 状态缓存

```javascript
// 状态缓存优化
const StateCache = {
    cache: new Map(),
    
    getCachedState: (key) => {
        return StateCache.cache.get(key);
    },
    
    setCachedState: (key, state) => {
        StateCache.cache.set(key, state);
        
        // 限制缓存大小
        if (StateCache.cache.size > 50) {
            const firstKey = StateCache.cache.keys().next().value;
            StateCache.cache.delete(firstKey);
        }
    },
    
    invalidateCache: (key) => {
        if (key) {
            StateCache.cache.delete(key);
        } else {
            StateCache.cache.clear();
        }
    }
};
```

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 非侵入式功能扩展
- 保持原有组件的完整性

### 2. 状态管理模式 (State Management Pattern)
- 集中管理会议相关状态
- 响应式状态更新

### 3. 观察者模式 (Observer Pattern)
- 监听UI变化
- 响应用户交互

## 注意事项

1. **UI适配**: 正确处理不同屏幕尺寸的适配
2. **状态同步**: 确保会议状态的实时同步
3. **权限管理**: 处理通知权限的请求和管理
4. **性能考虑**: 避免频繁的DOM操作

## 扩展建议

1. **更多会议提供商**: 集成更多视频会议服务
2. **会议录制**: 支持会议录制和回放
3. **屏幕共享**: 集成屏幕共享功能
4. **会议日历**: 集成日历系统管理会议
5. **会议分析**: 提供会议使用统计和分析

该补丁为讨论侧边栏添加了重要的会议协作功能，增强了团队协作能力。

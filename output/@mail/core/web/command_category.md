# Command Category - 命令分类

## 概述

`command_category.js` 实现了 Odoo 邮件系统中的命令分类注册，用于定义讨论应用中的命令分类系统。该文件注册了两个核心命令分类：提及（@）和最近（#），为用户提供了快速访问提及内容和最近活动的命令接口，支持命名空间和序列排序，是讨论应用命令系统的基础配置文件。

## 文件信息
- **路径**: `/mail/static/src/core/web/command_category.js`
- **行数**: 18
- **模块**: `@mail/core/web/command_category`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'  // 国际化
'@web/core/registry'          // 注册表系统
```

## 注册表配置

### 命令分类注册表

```javascript
const commandCategoryRegistry = registry.category("command_categories");
```

**注册表获取**:
- 从核心注册表系统获取命令分类注册表
- 用于注册和管理命令分类

## 注册的命令分类

### 1. 提及分类 (discuss_mentioned)

```javascript
commandCategoryRegistry.add("discuss_mentioned", { 
    namespace: "@", 
    name: _t("Mentions") 
}, { 
    sequence: 10 
});
```

**分类配置**:
- **ID**: `discuss_mentioned`
- **命名空间**: `@` - 使用@符号作为命令前缀
- **名称**: "Mentions" - 国际化的显示名称
- **序列**: 10 - 控制显示顺序

### 2. 最近分类 (discuss_recent)

```javascript
commandCategoryRegistry.add("discuss_recent", { 
    namespace: "#", 
    name: _t("Recent") 
}, { 
    sequence: 10 
});
```

**分类配置**:
- **ID**: `discuss_recent`
- **命名空间**: `#` - 使用#符号作为命令前缀
- **名称**: "Recent" - 国际化的显示名称
- **序列**: 10 - 控制显示顺序

## 使用场景

### 1. 讨论应用命令系统

```javascript
// 讨论应用中的命令处理
const DiscussCommandHandler = {
    parseCommand: (input) => {
        const trimmedInput = input.trim();
        
        if (trimmedInput.startsWith('@')) {
            return {
                category: 'discuss_mentioned',
                namespace: '@',
                query: trimmedInput.substring(1)
            };
        } else if (trimmedInput.startsWith('#')) {
            return {
                category: 'discuss_recent',
                namespace: '#',
                query: trimmedInput.substring(1)
            };
        }
        
        return null;
    },
    
    executeCommand: async (command) => {
        switch (command.category) {
            case 'discuss_mentioned':
                return await DiscussCommandHandler.handleMentionCommand(command.query);
            case 'discuss_recent':
                return await DiscussCommandHandler.handleRecentCommand(command.query);
            default:
                return null;
        }
    },
    
    handleMentionCommand: async (query) => {
        // 搜索提及相关内容
        const mentions = await searchMentions(query);
        return {
            type: 'mentions',
            results: mentions,
            category: 'discuss_mentioned'
        };
    },
    
    handleRecentCommand: async (query) => {
        // 搜索最近活动
        const recentItems = await searchRecentItems(query);
        return {
            type: 'recent',
            results: recentItems,
            category: 'discuss_recent'
        };
    }
};
```

### 2. 命令建议系统

```javascript
// 命令建议系统
const CommandSuggestionSystem = {
    getSuggestions: (input) => {
        const suggestions = [];
        
        if (input.startsWith('@')) {
            suggestions.push({
                category: 'discuss_mentioned',
                namespace: '@',
                name: _t("Mentions"),
                description: _t("Search for mentions and tagged content"),
                examples: ['@john', '@team', '@important']
            });
        }
        
        if (input.startsWith('#')) {
            suggestions.push({
                category: 'discuss_recent',
                namespace: '#',
                name: _t("Recent"),
                description: _t("Search recent conversations and activities"),
                examples: ['#project', '#meeting', '#urgent']
            });
        }
        
        return suggestions;
    },
    
    renderSuggestion: (suggestion) => {
        return `
            <div class="command-suggestion" data-category="${suggestion.category}">
                <span class="namespace">${suggestion.namespace}</span>
                <span class="name">${suggestion.name}</span>
                <span class="description">${suggestion.description}</span>
                <div class="examples">
                    ${suggestion.examples.map(ex => `<code>${ex}</code>`).join(' ')}
                </div>
            </div>
        `;
    }
};
```

### 3. 命令自动完成

```javascript
// 命令自动完成
const CommandAutoComplete = {
    getCompletions: async (input) => {
        const command = DiscussCommandHandler.parseCommand(input);
        if (!command) return [];
        
        switch (command.category) {
            case 'discuss_mentioned':
                return await CommandAutoComplete.getMentionCompletions(command.query);
            case 'discuss_recent':
                return await CommandAutoComplete.getRecentCompletions(command.query);
            default:
                return [];
        }
    },
    
    getMentionCompletions: async (query) => {
        // 获取用户和标签的自动完成
        const users = await searchUsers(query);
        const tags = await searchTags(query);
        
        return [
            ...users.map(user => ({
                type: 'user',
                value: `@${user.name}`,
                display: user.name,
                avatar: user.avatar,
                category: 'discuss_mentioned'
            })),
            ...tags.map(tag => ({
                type: 'tag',
                value: `@${tag.name}`,
                display: tag.name,
                color: tag.color,
                category: 'discuss_mentioned'
            }))
        ];
    },
    
    getRecentCompletions: async (query) => {
        // 获取最近项目的自动完成
        const recentChannels = await searchRecentChannels(query);
        const recentThreads = await searchRecentThreads(query);
        
        return [
            ...recentChannels.map(channel => ({
                type: 'channel',
                value: `#${channel.name}`,
                display: channel.name,
                description: channel.description,
                category: 'discuss_recent'
            })),
            ...recentThreads.map(thread => ({
                type: 'thread',
                value: `#${thread.name}`,
                display: thread.name,
                lastActivity: thread.lastActivity,
                category: 'discuss_recent'
            }))
        ];
    }
};
```

### 4. 命令执行器

```javascript
// 命令执行器
const CommandExecutor = {
    execute: async (commandText) => {
        const command = DiscussCommandHandler.parseCommand(commandText);
        if (!command) {
            throw new Error('无效的命令格式');
        }
        
        // 记录命令使用
        CommandExecutor.logCommandUsage(command);
        
        try {
            const result = await DiscussCommandHandler.executeCommand(command);
            
            // 处理执行结果
            return CommandExecutor.processResult(result, command);
        } catch (error) {
            console.error('命令执行失败:', error);
            throw new Error(`命令执行失败: ${error.message}`);
        }
    },
    
    processResult: (result, command) => {
        switch (command.category) {
            case 'discuss_mentioned':
                return CommandExecutor.processMentionResult(result);
            case 'discuss_recent':
                return CommandExecutor.processRecentResult(result);
            default:
                return result;
        }
    },
    
    processMentionResult: (result) => {
        return {
            ...result,
            displayType: 'mention_list',
            actions: ['view', 'reply', 'mark_read']
        };
    },
    
    processRecentResult: (result) => {
        return {
            ...result,
            displayType: 'recent_list',
            actions: ['open', 'pin', 'archive']
        };
    },
    
    logCommandUsage: (command) => {
        // 记录命令使用统计
        const usage = {
            category: command.category,
            namespace: command.namespace,
            query: command.query,
            timestamp: Date.now(),
            userId: getCurrentUser().id
        };
        
        // 发送到分析服务
        sendAnalytics('command_usage', usage);
    }
};
```

### 5. 命令历史管理

```javascript
// 命令历史管理
const CommandHistoryManager = {
    history: [],
    maxHistorySize: 50,
    
    addToHistory: (command) => {
        // 避免重复的连续命令
        const lastCommand = CommandHistoryManager.history[0];
        if (lastCommand && lastCommand.text === command.text) {
            return;
        }
        
        CommandHistoryManager.history.unshift({
            text: command.text,
            category: command.category,
            timestamp: Date.now(),
            success: command.success
        });
        
        // 限制历史大小
        if (CommandHistoryManager.history.length > CommandHistoryManager.maxHistorySize) {
            CommandHistoryManager.history.pop();
        }
        
        // 保存到本地存储
        CommandHistoryManager.saveToStorage();
    },
    
    getHistory: (category = null) => {
        if (category) {
            return CommandHistoryManager.history.filter(cmd => cmd.category === category);
        }
        return CommandHistoryManager.history;
    },
    
    getFrequentCommands: (limit = 10) => {
        const commandCounts = {};
        
        CommandHistoryManager.history.forEach(cmd => {
            const key = `${cmd.category}:${cmd.text}`;
            commandCounts[key] = (commandCounts[key] || 0) + 1;
        });
        
        return Object.entries(commandCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, limit)
            .map(([key, count]) => {
                const [category, text] = key.split(':');
                return { category, text, count };
            });
    },
    
    saveToStorage: () => {
        try {
            localStorage.setItem(
                'discuss_command_history',
                JSON.stringify(CommandHistoryManager.history)
            );
        } catch (error) {
            console.error('保存命令历史失败:', error);
        }
    },
    
    loadFromStorage: () => {
        try {
            const stored = localStorage.getItem('discuss_command_history');
            if (stored) {
                CommandHistoryManager.history = JSON.parse(stored);
            }
        } catch (error) {
            console.error('加载命令历史失败:', error);
            CommandHistoryManager.history = [];
        }
    }
};
```

## 命令分类扩展

### 1. 自定义命令分类

```javascript
// 自定义命令分类注册
const CustomCommandCategories = {
    register: () => {
        const commandCategoryRegistry = registry.category("command_categories");
        
        // 文件搜索分类
        commandCategoryRegistry.add("discuss_files", {
            namespace: "/",
            name: _t("Files")
        }, { sequence: 20 });
        
        // 任务管理分类
        commandCategoryRegistry.add("discuss_tasks", {
            namespace: "!",
            name: _t("Tasks")
        }, { sequence: 30 });
        
        // 日历事件分类
        commandCategoryRegistry.add("discuss_events", {
            namespace: "*",
            name: _t("Events")
        }, { sequence: 40 });
    },
    
    getAvailableCategories: () => {
        const commandCategoryRegistry = registry.category("command_categories");
        return commandCategoryRegistry.getAll();
    },
    
    getCategoryByNamespace: (namespace) => {
        const categories = CustomCommandCategories.getAvailableCategories();
        return Object.values(categories).find(cat => cat.namespace === namespace);
    }
};
```

### 2. 动态分类管理

```javascript
// 动态分类管理
const DynamicCategoryManager = {
    addCategory: (id, config, options = {}) => {
        const commandCategoryRegistry = registry.category("command_categories");
        
        // 验证配置
        if (!DynamicCategoryManager.validateConfig(config)) {
            throw new Error('无效的分类配置');
        }
        
        // 检查命名空间冲突
        if (DynamicCategoryManager.hasNamespaceConflict(config.namespace)) {
            throw new Error(`命名空间 "${config.namespace}" 已被使用`);
        }
        
        commandCategoryRegistry.add(id, config, options);
        
        // 记录添加操作
        DynamicCategoryManager.logCategoryOperation('add', id, config);
    },
    
    removeCategory: (id) => {
        const commandCategoryRegistry = registry.category("command_categories");
        commandCategoryRegistry.remove(id);
        
        // 记录移除操作
        DynamicCategoryManager.logCategoryOperation('remove', id);
    },
    
    validateConfig: (config) => {
        return config.namespace && 
               config.name && 
               typeof config.namespace === 'string' &&
               typeof config.name === 'string';
    },
    
    hasNamespaceConflict: (namespace) => {
        const categories = CustomCommandCategories.getAvailableCategories();
        return Object.values(categories).some(cat => cat.namespace === namespace);
    },
    
    logCategoryOperation: (operation, id, config = null) => {
        console.log(`命令分类${operation}:`, { id, config, timestamp: Date.now() });
    }
};
```

## 国际化支持

### 1. 多语言分类名称

```javascript
// 多语言分类名称
const MultiLanguageCategories = {
    getLocalizedName: (categoryId, locale = null) => {
        const translations = {
            'discuss_mentioned': {
                'en_US': 'Mentions',
                'zh_CN': '提及',
                'fr_FR': 'Mentions',
                'es_ES': 'Menciones',
                'de_DE': 'Erwähnungen'
            },
            'discuss_recent': {
                'en_US': 'Recent',
                'zh_CN': '最近',
                'fr_FR': 'Récent',
                'es_ES': 'Reciente',
                'de_DE': 'Kürzlich'
            }
        };
        
        const currentLocale = locale || getCurrentLocale();
        const categoryTranslations = translations[categoryId];
        
        if (categoryTranslations && categoryTranslations[currentLocale]) {
            return categoryTranslations[currentLocale];
        }
        
        // 回退到英语
        return categoryTranslations?.['en_US'] || categoryId;
    },
    
    updateCategoryNames: () => {
        const commandCategoryRegistry = registry.category("command_categories");
        const categories = commandCategoryRegistry.getAll();
        
        Object.entries(categories).forEach(([id, category]) => {
            const localizedName = MultiLanguageCategories.getLocalizedName(id);
            if (localizedName !== category.name) {
                // 更新分类名称
                commandCategoryRegistry.add(id, {
                    ...category,
                    name: localizedName
                }, { force: true });
            }
        });
    }
};
```

## 性能优化

### 1. 分类缓存

```javascript
// 分类缓存管理
const CategoryCache = {
    cache: new Map(),
    
    getCachedCategories: () => {
        const cacheKey = 'all_categories';
        if (CategoryCache.cache.has(cacheKey)) {
            return CategoryCache.cache.get(cacheKey);
        }
        
        const commandCategoryRegistry = registry.category("command_categories");
        const categories = commandCategoryRegistry.getAll();
        
        CategoryCache.cache.set(cacheKey, categories);
        return categories;
    },
    
    invalidateCache: () => {
        CategoryCache.cache.clear();
    },
    
    getCategoryByNamespace: (namespace) => {
        const cacheKey = `namespace_${namespace}`;
        
        if (CategoryCache.cache.has(cacheKey)) {
            return CategoryCache.cache.get(cacheKey);
        }
        
        const categories = CategoryCache.getCachedCategories();
        const category = Object.values(categories).find(cat => cat.namespace === namespace);
        
        if (category) {
            CategoryCache.cache.set(cacheKey, category);
        }
        
        return category;
    }
};
```

## 设计模式

### 1. 注册表模式 (Registry Pattern)
- 使用注册表管理命令分类
- 支持动态添加和移除分类

### 2. 命名空间模式 (Namespace Pattern)
- 使用符号作为命令命名空间
- 避免命令冲突

### 3. 策略模式 (Strategy Pattern)
- 不同分类的不同处理策略
- 可扩展的命令处理

## 注意事项

1. **命名空间唯一性**: 确保每个分类的命名空间唯一
2. **国际化**: 正确处理分类名称的国际化
3. **序列排序**: 合理设置序列以控制显示顺序
4. **扩展性**: 设计时考虑未来的扩展需求

## 扩展建议

1. **更多分类**: 添加更多有用的命令分类
2. **智能建议**: 基于上下文的智能命令建议
3. **快捷键**: 为常用分类添加快捷键支持
4. **自定义**: 允许用户自定义命令分类
5. **插件系统**: 支持第三方插件添加命令分类

该文件为讨论应用提供了基础的命令分类系统，是构建强大命令接口的重要基础。

# Activity Mail Template - 活动邮件模板组件

## 概述

`activity_mail_template.js` 实现了 Odoo 邮件系统中的活动邮件模板组件，用于在活动中处理邮件模板相关的操作。该组件提供了邮件模板的预览和发送功能，支持基于活动的邮件模板处理，允许用户在活动上下文中快速发送预定义的邮件模板，是活动与邮件模板集成的重要桥梁组件。

## 文件信息
- **路径**: `/mail/static/src/core/web/activity_mail_template.js`
- **行数**: 84
- **模块**: `@mail/core/web/activity_mail_template`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                    // OWL 框架
'@web/core/utils/hooks'        // Web 核心钩子
'@web/core/l10n/translation'   // 国际化
```

## 组件定义

### ActivityMailTemplate 类

```javascript
class ActivityMailTemplate extends Component {
    static defaultProps = {
        onClickButtons: () => {},
    };
    static props = [
        "activity",              // 活动对象
        "onClickButtons?",       // 按钮点击回调（可选）
        "onActivityChanged?",    // 活动变化回调（可选）
    ];
    static template = "mail.ActivityMailTemplate";
}
```

## Props 配置

### Props 详细说明

- **`activity`** (必需):
  - 类型: Activity 模型实例
  - 用途: 提供活动上下文信息
  - 功能: 确定邮件模板的应用范围和目标

- **`onClickButtons`** (可选):
  - 类型: 函数
  - 默认值: 空函数
  - 用途: 按钮点击时的回调
  - 功能: 处理按钮点击的通用逻辑

- **`onActivityChanged`** (可选):
  - 类型: 函数
  - 用途: 活动发生变化时的回调
  - 参数: thread (线程对象)

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.store = useState(useService("mail.store"));
}
```

**初始化内容**:
- 邮件存储服务的响应式状态

## 核心功能

### 1. 邮件模板预览

```javascript
onClickPreview(ev, mailTemplate) {
    ev.stopPropagation();
    ev.preventDefault();
    this.props.onClickButtons();
    
    const action = {
        name: _t("Compose Email"),
        type: "ir.actions.act_window",
        res_model: "mail.compose.message",
        views: [[false, "form"]],
        target: "new",
        context: {
            default_res_ids: [this.props.activity.res_id],
            default_model: this.props.activity.res_model,
            default_subtype_xmlid: "mail.mt_comment",
            default_template_id: mailTemplate.id,
            force_email: true,
        },
    };
    
    const thread = this.store.Thread.insert({
        model: this.props.activity.res_model,
        id: this.props.activity.res_id,
    });
    
    this.env.services.action.doAction(action, {
        onClose: () => this.props.onActivityChanged?.(thread),
    });
}
```

**预览功能**:
1. 阻止事件冒泡和默认行为
2. 触发按钮点击回调
3. 创建邮件编写动作
4. 设置默认上下文参数
5. 打开邮件编写对话框
6. 对话框关闭后触发活动变化回调

### 2. 邮件模板发送

```javascript
async onClickSend(ev, mailTemplate) {
    ev.stopPropagation();
    ev.preventDefault();
    this.props.onClickButtons();
    
    const thread = this.store.Thread.insert({
        model: this.props.activity.res_model,
        id: this.props.activity.res_id,
    });
    
    await this.env.services.orm.call(this.props.activity.res_model, "activity_send_mail", [
        [this.props.activity.res_id],
        mailTemplate.id,
    ]);
    
    this.props.onActivityChanged?.(thread);
}
```

**发送功能**:
1. 阻止事件冒泡和默认行为
2. 触发按钮点击回调
3. 创建或获取线程对象
4. 调用服务器的活动发送邮件方法
5. 发送完成后触发活动变化回调

## 使用场景

### 1. 活动中的邮件模板操作

```javascript
// 在活动项目中使用邮件模板
const ActivityWithMailTemplate = ({ activity }) => {
    const handleButtonClick = () => {
        // 记录用户操作
        logUserAction('mail_template_action', activity.id);
        
        // 更新UI状态
        updateUIState('processing');
    };
    
    const handleActivityChanged = (thread) => {
        // 刷新活动数据
        refreshActivityData(activity);
        
        // 更新线程消息
        thread.fetchNewMessages();
        
        // 通知用户操作完成
        showNotification('邮件操作完成');
    };
    
    return (
        <div class="activity-mail-template-section">
            <ActivityMailTemplate
                activity={activity}
                onClickButtons={handleButtonClick}
                onActivityChanged={handleActivityChanged}
            />
        </div>
    );
};
```

### 2. 销售流程中的邮件模板

```javascript
// 销售流程中的邮件模板使用
const SalesActivityMailTemplate = ({ salesActivity, opportunity }) => {
    const handleSalesMailAction = () => {
        // 更新销售机会状态
        updateOpportunityStatus(opportunity, 'email_sent');
        
        // 记录销售活动
        logSalesActivity(salesActivity, 'email_template_used');
    };
    
    const handleSalesActivityChanged = (thread) => {
        // 更新销售流程
        updateSalesProcess(opportunity);
        
        // 刷新相关数据
        refreshOpportunityData(opportunity);
        
        // 通知销售团队
        notifySalesTeam(opportunity, 'email_sent');
    };
    
    return (
        <div class="sales-mail-template">
            <h4>发送销售邮件</h4>
            <ActivityMailTemplate
                activity={salesActivity}
                onClickButtons={handleSalesMailAction}
                onActivityChanged={handleSalesActivityChanged}
            />
        </div>
    );
};
```

### 3. 客户服务中的邮件模板

```javascript
// 客户服务中的邮件模板使用
const ServiceActivityMailTemplate = ({ serviceActivity, ticket }) => {
    const handleServiceMailAction = () => {
        // 更新工单状态
        updateTicketStatus(ticket, 'response_sent');
        
        // 记录服务活动
        logServiceActivity(serviceActivity, 'template_response');
    };
    
    const handleServiceActivityChanged = (thread) => {
        // 更新工单
        refreshTicketData(ticket);
        
        // 通知客户
        notifyCustomer(ticket, 'response_sent');
        
        // 更新服务指标
        updateServiceMetrics(ticket);
    };
    
    return (
        <div class="service-mail-template">
            <div class="template-header">
                <span>客户服务回复</span>
                <span class="ticket-id">#{ticket.id}</span>
            </div>
            <ActivityMailTemplate
                activity={serviceActivity}
                onClickButtons={handleServiceMailAction}
                onActivityChanged={handleServiceActivityChanged}
            />
        </div>
    );
};
```

### 4. 营销活动中的邮件模板

```javascript
// 营销活动中的邮件模板使用
const MarketingActivityMailTemplate = ({ marketingActivity, campaign }) => {
    const handleMarketingMailAction = () => {
        // 更新营销活动统计
        updateCampaignStats(campaign, 'email_sent');
        
        // 记录营销活动
        logMarketingActivity(marketingActivity, 'template_sent');
    };
    
    const handleMarketingActivityChanged = (thread) => {
        // 更新营销活动数据
        refreshCampaignData(campaign);
        
        // 分析邮件效果
        analyzeCampaignPerformance(campaign);
        
        // 更新营销指标
        updateMarketingMetrics(campaign);
    };
    
    return (
        <div class="marketing-mail-template">
            <div class="campaign-info">
                <h4>{campaign.name}</h4>
                <span class="campaign-type">{campaign.type}</span>
            </div>
            <ActivityMailTemplate
                activity={marketingActivity}
                onClickButtons={handleMarketingMailAction}
                onActivityChanged={handleMarketingActivityChanged}
            />
        </div>
    );
};
```

## 邮件模板处理

### 1. 模板上下文构建

```javascript
// 构建邮件模板上下文
const buildTemplateContext = (activity, mailTemplate) => {
    return {
        // 活动相关上下文
        activity_id: activity.id,
        activity_summary: activity.summary,
        activity_note: activity.note,
        activity_deadline: activity.date_deadline,
        activity_user: activity.user_id,
        
        // 资源相关上下文
        res_model: activity.res_model,
        res_id: activity.res_id,
        res_name: activity.res_name,
        
        // 模板相关上下文
        template_id: mailTemplate.id,
        template_name: mailTemplate.name,
        template_subject: mailTemplate.subject,
        
        // 用户相关上下文
        user_id: getCurrentUser().id,
        user_name: getCurrentUser().name,
        company_id: getCurrentUser().company_id
    };
};
```

### 2. 模板预处理

```javascript
// 邮件模板预处理
const preprocessMailTemplate = (mailTemplate, context) => {
    // 替换模板变量
    const processedSubject = replaceTemplateVariables(mailTemplate.subject, context);
    const processedBody = replaceTemplateVariables(mailTemplate.body_html, context);
    
    // 处理附件
    const processedAttachments = processTemplateAttachments(mailTemplate.attachments, context);
    
    return {
        ...mailTemplate,
        subject: processedSubject,
        body_html: processedBody,
        attachments: processedAttachments
    };
};

// 替换模板变量
const replaceTemplateVariables = (template, context) => {
    let processed = template;
    
    Object.entries(context).forEach(([key, value]) => {
        const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
        processed = processed.replace(regex, value || '');
    });
    
    return processed;
};
```

### 3. 模板验证

```javascript
// 邮件模板验证
const validateMailTemplate = (mailTemplate, activity) => {
    const errors = [];
    
    // 验证模板基本信息
    if (!mailTemplate.id) {
        errors.push('模板ID不能为空');
    }
    
    if (!mailTemplate.subject) {
        errors.push('模板主题不能为空');
    }
    
    if (!mailTemplate.body_html && !mailTemplate.body_text) {
        errors.push('模板内容不能为空');
    }
    
    // 验证模板与活动的兼容性
    if (mailTemplate.model && mailTemplate.model !== activity.res_model) {
        errors.push('模板模型与活动资源模型不匹配');
    }
    
    // 验证用户权限
    if (!hasTemplatePermission(mailTemplate, getCurrentUser())) {
        errors.push('用户没有使用此模板的权限');
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
};
```

## 动作处理

### 1. 预览动作配置

```javascript
// 预览动作配置
const createPreviewAction = (activity, mailTemplate) => {
    return {
        name: _t("预览邮件"),
        type: "ir.actions.act_window",
        res_model: "mail.compose.message",
        views: [[false, "form"]],
        target: "new",
        context: {
            default_res_ids: [activity.res_id],
            default_model: activity.res_model,
            default_subtype_xmlid: "mail.mt_comment",
            default_template_id: mailTemplate.id,
            default_composition_mode: "comment",
            force_email: true,
            mail_post_autofollow: true
        },
        flags: {
            mode: "edit"
        }
    };
};
```

### 2. 发送动作处理

```javascript
// 发送动作处理
const handleSendAction = async (activity, mailTemplate) => {
    try {
        // 验证模板
        const validation = validateMailTemplate(mailTemplate, activity);
        if (!validation.isValid) {
            throw new Error(validation.errors.join(', '));
        }
        
        // 发送邮件
        const result = await orm.call(
            activity.res_model,
            "activity_send_mail",
            [[activity.res_id], mailTemplate.id],
            {
                context: buildTemplateContext(activity, mailTemplate)
            }
        );
        
        // 处理发送结果
        if (result.success) {
            showNotification('邮件发送成功', 'success');
            return result;
        } else {
            throw new Error(result.error || '邮件发送失败');
        }
        
    } catch (error) {
        console.error('邮件发送失败:', error);
        showNotification(`邮件发送失败: ${error.message}`, 'error');
        throw error;
    }
};
```

## 性能优化

### 1. 模板缓存

```javascript
// 邮件模板缓存
const templateCache = new Map();

const getCachedTemplate = (templateId) => {
    if (templateCache.has(templateId)) {
        const cached = templateCache.get(templateId);
        if (Date.now() - cached.timestamp < 300000) { // 5分钟缓存
            return cached.template;
        }
    }
    return null;
};

const setCachedTemplate = (templateId, template) => {
    templateCache.set(templateId, {
        template,
        timestamp: Date.now()
    });
};
```

### 2. 异步处理

```javascript
// 异步邮件发送
const sendMailAsync = async (activity, mailTemplate) => {
    // 显示加载状态
    showLoadingState(true);
    
    try {
        // 异步发送邮件
        const sendPromise = handleSendAction(activity, mailTemplate);
        
        // 设置超时
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('发送超时')), 30000);
        });
        
        // 竞争执行
        const result = await Promise.race([sendPromise, timeoutPromise]);
        
        return result;
    } finally {
        showLoadingState(false);
    }
};
```

## 错误处理

### 1. 发送错误处理

```javascript
// 邮件发送错误处理
const handleSendError = (error, activity, mailTemplate) => {
    console.error('邮件发送错误:', error);
    
    // 记录错误
    logError({
        type: 'mail_template_send_error',
        activity_id: activity.id,
        template_id: mailTemplate.id,
        error: error.message,
        timestamp: new Date().toISOString()
    });
    
    // 显示用户友好的错误消息
    let userMessage = '邮件发送失败';
    
    if (error.message.includes('permission')) {
        userMessage = '没有权限发送此邮件';
    } else if (error.message.includes('template')) {
        userMessage = '邮件模板配置错误';
    } else if (error.message.includes('network')) {
        userMessage = '网络连接错误，请重试';
    }
    
    showNotification(userMessage, 'error');
};
```

### 2. 模板错误处理

```javascript
// 模板错误处理
const handleTemplateError = (error, mailTemplate) => {
    console.error('模板处理错误:', error);
    
    // 模板格式错误
    if (error.message.includes('syntax')) {
        showNotification('邮件模板格式错误', 'error');
        return;
    }
    
    // 模板变量错误
    if (error.message.includes('variable')) {
        showNotification('邮件模板变量错误', 'error');
        return;
    }
    
    // 通用错误
    showNotification('邮件模板处理失败', 'error');
};
```

## 设计模式

### 1. 命令模式 (Command Pattern)
- 封装邮件发送和预览命令
- 支持撤销和重做操作

### 2. 策略模式 (Strategy Pattern)
- 不同类型邮件模板的不同处理策略
- 可配置的发送策略

### 3. 观察者模式 (Observer Pattern)
- 监听活动变化
- 响应邮件发送状态

## 注意事项

1. **权限验证**: 确保用户有权限使用邮件模板
2. **模板验证**: 验证邮件模板的完整性和正确性
3. **错误处理**: 提供完善的错误处理和用户反馈
4. **性能考虑**: 避免频繁的模板加载和处理

## 扩展建议

1. **模板编辑**: 支持在线编辑邮件模板
2. **批量发送**: 支持批量发送邮件模板
3. **发送统计**: 添加邮件发送统计和分析
4. **模板预览**: 增强模板预览功能
5. **自动化**: 支持基于条件的自动邮件发送

该组件为活动中的邮件模板操作提供了完整的功能支持，是活动与邮件系统集成的重要桥梁。

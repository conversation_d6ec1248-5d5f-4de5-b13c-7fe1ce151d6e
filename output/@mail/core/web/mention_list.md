# Mention List - 提及列表

## 概述

`mention_list.js` 实现了 Odoo 邮件系统中的提及列表组件，用于在用户输入@或#符号时显示可提及的用户或频道列表。该组件支持实时搜索、键盘导航、自动聚焦等功能，通过建议服务获取搜索结果，使用可导航列表组件展示选项，为用户提供了便捷的提及功能，是邮件编写和聊天功能的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/core/web/mention_list.js`
- **行数**: 125
- **模块**: `@mail/core/web/mention_list`

## 依赖关系

```javascript
// UI组件依赖
'@mail/core/common/navigable_list'  // 可导航列表组件

// 核心依赖
'@web/core/l10n/translation'        // 国际化
'@odoo/owl'                        // OWL 框架
'@web/core/utils/hooks'            // Web 核心钩子
'@mail/utils/common/hooks'         // 邮件工具钩子
```

## 组件定义

### MentionList 类

```javascript
class MentionList extends Component {
    static template = "mail.MentionList";
    static components = { NavigableList };
    static props = {
        onSelect: { type: Function },
        close: { type: Function, optional: true },
        type: { type: String },
    };
    static defaultProps = {
        close: () => {},
    };
}
```

## Props 配置

### Props 详细说明

- **`onSelect`** (必需):
  - 类型: 函数
  - 用途: 用户选择提及项时的回调函数
  - 功能: 处理选择的用户或频道

- **`close`** (可选):
  - 类型: 函数
  - 默认值: 空函数
  - 用途: 关闭提及列表的回调函数

- **`type`** (必需):
  - 类型: 字符串
  - 用途: 指定提及类型（"partner"或"channel"）
  - 功能: 决定搜索的对象类型和分隔符

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.state = useState({
        searchTerm: "",
        options: [],
        isFetching: false,
    });
    this.orm = useService("orm");
    this.store = useState(useService("mail.store"));
    this.suggestionService = useService("mail.suggestion");
    this.sequential = useSequential();
    this.ref = useAutofocus({ mobile: true });
    
    // 搜索效果...
}
```

**初始化内容**:
- 搜索状态管理（搜索词、选项、获取状态）
- ORM服务用于数据操作
- 邮件存储服务的响应式状态
- 建议服务用于获取提及建议
- 顺序执行钩子防止竞态条件
- 自动聚焦引用（支持移动端）

## 核心功能

### 1. 搜索效果

```javascript
useEffect(
    () => {
        if (!this.state.searchTerm) {
            this.state.options = [];
            return;
        }
        this.sequential(async () => {
            this.state.isFetching = true;
            try {
                await this.suggestionService.fetchSuggestions({
                    delimiter: this.props.type === "partner" ? "@" : "#",
                    term: this.state.searchTerm,
                });
            } finally {
                this.state.isFetching = false;
            }
            const { suggestions } = this.suggestionService.searchSuggestions(
                {
                    delimiter: this.props.type === "partner" ? "@" : "#",
                    term: this.state.searchTerm,
                },
                { sort: true }
            );
            this.state.options = suggestions;
        });
    },
    () => [this.state.searchTerm]
);
```

**搜索逻辑**:
1. 监听搜索词变化
2. 空搜索词时清空选项
3. 使用顺序执行防止竞态条件
4. 设置获取状态指示器
5. 调用建议服务获取建议
6. 搜索并排序建议结果
7. 更新选项列表

### 2. 占位符文本

```javascript
get placeholder() {
    switch (this.props.type) {
        case "channel":
            return _t("Search for a channel...");
        case "partner":
            return _t("Search for a user...");
        default:
            return _t("Search...");
    }
}
```

**占位符逻辑**:
- 根据提及类型显示不同的占位符文本
- 支持国际化

### 3. 可导航列表属性

```javascript
get navigableListProps() {
    const props = {
        anchorRef: this.ref.el,
        position: "bottom-fit",
        isLoading: !!this.state.searchTerm && this.state.isFetching,
        onSelect: (...args) => {
            this.props.onSelect(...args);
            this.props.close();
        },
        options: [],
    };
    
    switch (this.props.type) {
        case "partner":
            this.state.options.forEach((option) => {
                props.options.push({
                    label: option.name,
                    partner: option,
                });
            });
            break;
        case "channel":
            this.state.options.forEach((option) => {
                props.options.push({
                    label: option.name,
                    channel: option,
                });
            });
            break;
    }
    
    return props;
}
```

**属性配置**:
- **锚点引用**: 用于定位列表位置
- **位置**: "bottom-fit"（底部适应）
- **加载状态**: 基于搜索词和获取状态
- **选择回调**: 调用props回调并关闭列表
- **选项格式**: 根据类型格式化选项

### 4. 键盘事件处理

```javascript
onKeydown(ev) {
    switch (ev.key) {
        case "Escape": {
            this.props.close();
            break;
        }
    }
}
```

**键盘处理**:
- **Escape键**: 关闭提及列表

## 使用场景

### 1. 邮件编写中的提及

```javascript
// 邮件编写中的用户提及
const MailComposerMention = ({ composer }) => {
    const [showMentionList, setShowMentionList] = useState(false);
    const [mentionType, setMentionType] = useState('partner');
    const [cursorPosition, setCursorPosition] = useState(0);
    
    const handleTextInput = (event) => {
        const text = event.target.value;
        const position = event.target.selectionStart;
        
        // 检测@或#符号
        const mentionMatch = text.slice(0, position).match(/[@#](\w*)$/);
        
        if (mentionMatch) {
            const delimiter = mentionMatch[0][0];
            const searchTerm = mentionMatch[1];
            
            setMentionType(delimiter === '@' ? 'partner' : 'channel');
            setShowMentionList(true);
            setCursorPosition(position);
            
            // 更新搜索词
            updateMentionSearch(searchTerm);
        } else {
            setShowMentionList(false);
        }
    };
    
    const handleMentionSelect = (option) => {
        const textArea = document.querySelector('.composer-textarea');
        const text = textArea.value;
        
        // 找到提及的起始位置
        const beforeCursor = text.slice(0, cursorPosition);
        const mentionStart = beforeCursor.lastIndexOf(mentionType === 'partner' ? '@' : '#');
        
        // 替换文本
        const beforeMention = text.slice(0, mentionStart);
        const afterCursor = text.slice(cursorPosition);
        const mentionText = mentionType === 'partner' 
            ? `@${option.partner.name}` 
            : `#${option.channel.name}`;
        
        const newText = beforeMention + mentionText + ' ' + afterCursor;
        textArea.value = newText;
        
        // 设置光标位置
        const newPosition = mentionStart + mentionText.length + 1;
        textArea.setSelectionRange(newPosition, newPosition);
        
        // 关闭提及列表
        setShowMentionList(false);
        
        // 记录提及
        recordMention(option, mentionType);
    };
    
    return (
        <div class="mail-composer-mention">
            <textarea
                class="composer-textarea"
                onInput={handleTextInput}
                placeholder="编写邮件..."
            />
            
            {showMentionList && (
                <MentionList
                    type={mentionType}
                    onSelect={handleMentionSelect}
                    close={() => setShowMentionList(false)}
                />
            )}
        </div>
    );
};
```

### 2. 聊天消息中的提及

```javascript
// 聊天消息中的提及功能
const ChatMessageMention = ({ chatWindow }) => {
    const [mentionState, setMentionState] = useState({
        active: false,
        type: null,
        searchTerm: '',
        position: 0
    });
    
    const detectMention = (text, cursorPos) => {
        // 检测@用户提及
        const userMention = text.slice(0, cursorPos).match(/@(\w*)$/);
        if (userMention) {
            return {
                type: 'partner',
                searchTerm: userMention[1],
                delimiter: '@'
            };
        }
        
        // 检测#频道提及
        const channelMention = text.slice(0, cursorPos).match(/#(\w*)$/);
        if (channelMention) {
            return {
                type: 'channel',
                searchTerm: channelMention[1],
                delimiter: '#'
            };
        }
        
        return null;
    };
    
    const handleInputChange = (event) => {
        const text = event.target.value;
        const cursorPos = event.target.selectionStart;
        
        const mention = detectMention(text, cursorPos);
        
        if (mention) {
            setMentionState({
                active: true,
                type: mention.type,
                searchTerm: mention.searchTerm,
                position: cursorPos
            });
        } else {
            setMentionState(prev => ({ ...prev, active: false }));
        }
    };
    
    const handleMentionSelect = (option) => {
        const input = document.querySelector('.chat-input');
        const text = input.value;
        
        // 构建提及文本
        const mentionText = mentionState.type === 'partner'
            ? `@${option.partner.name}`
            : `#${option.channel.name}`;
        
        // 替换文本
        const beforeMention = text.slice(0, mentionState.position - mentionState.searchTerm.length - 1);
        const afterCursor = text.slice(mentionState.position);
        const newText = beforeMention + mentionText + ' ' + afterCursor;
        
        input.value = newText;
        
        // 更新光标位置
        const newPos = beforeMention.length + mentionText.length + 1;
        input.setSelectionRange(newPos, newPos);
        
        // 关闭提及列表
        setMentionState(prev => ({ ...prev, active: false }));
        
        // 添加提及到消息数据
        addMentionToMessage(option, mentionState.type);
    };
    
    return (
        <div class="chat-message-mention">
            <input
                type="text"
                class="chat-input"
                onChange={handleInputChange}
                placeholder="输入消息..."
            />
            
            {mentionState.active && (
                <MentionList
                    type={mentionState.type}
                    onSelect={handleMentionSelect}
                    close={() => setMentionState(prev => ({ ...prev, active: false }))}
                />
            )}
        </div>
    );
};
```

### 3. 提及建议增强

```javascript
// 提及建议增强
const MentionSuggestionEnhancer = {
    enhanceSuggestions: (suggestions, type, context) => {
        return suggestions.map(suggestion => {
            const enhanced = { ...suggestion };
            
            // 添加头像URL
            if (type === 'partner') {
                enhanced.avatarUrl = `/web/image/res.partner/${suggestion.id}/avatar_128`;
                enhanced.isOnline = MentionSuggestionEnhancer.checkOnlineStatus(suggestion.id);
                enhanced.lastSeen = MentionSuggestionEnhancer.getLastSeen(suggestion.id);
            }
            
            // 添加频道信息
            if (type === 'channel') {
                enhanced.memberCount = MentionSuggestionEnhancer.getChannelMemberCount(suggestion.id);
                enhanced.isPrivate = suggestion.channel_type === 'private';
                enhanced.description = suggestion.description;
            }
            
            // 添加相关性评分
            enhanced.relevanceScore = MentionSuggestionEnhancer.calculateRelevance(
                suggestion, 
                context
            );
            
            return enhanced;
        });
    },
    
    calculateRelevance: (suggestion, context) => {
        let score = 0;
        
        // 基于最近交互
        if (context.recentInteractions?.includes(suggestion.id)) {
            score += 10;
        }
        
        // 基于频繁联系
        const interactionCount = context.interactionCounts?.[suggestion.id] || 0;
        score += Math.min(interactionCount, 5);
        
        // 基于在线状态（仅用户）
        if (suggestion.is_online) {
            score += 3;
        }
        
        // 基于同一频道成员
        if (context.currentChannel && suggestion.channel_ids?.includes(context.currentChannel)) {
            score += 5;
        }
        
        return score;
    },
    
    sortByRelevance: (suggestions) => {
        return suggestions.sort((a, b) => {
            // 首先按相关性评分排序
            if (b.relevanceScore !== a.relevanceScore) {
                return b.relevanceScore - a.relevanceScore;
            }
            
            // 然后按字母顺序排序
            return a.name.localeCompare(b.name);
        });
    },
    
    filterSuggestions: (suggestions, filters) => {
        return suggestions.filter(suggestion => {
            // 在线状态过滤
            if (filters.onlineOnly && !suggestion.isOnline) {
                return false;
            }
            
            // 权限过滤
            if (filters.hasPermission && !suggestion.canMention) {
                return false;
            }
            
            // 频道类型过滤
            if (filters.channelType && suggestion.channel_type !== filters.channelType) {
                return false;
            }
            
            return true;
        });
    }
};
```

### 4. 提及历史管理

```javascript
// 提及历史管理
const MentionHistoryManager = {
    history: [],
    maxHistorySize: 50,
    
    addToHistory: (mention) => {
        // 避免重复
        const existingIndex = MentionHistoryManager.history.findIndex(
            item => item.id === mention.id && item.type === mention.type
        );
        
        if (existingIndex !== -1) {
            // 移动到顶部
            MentionHistoryManager.history.splice(existingIndex, 1);
        }
        
        // 添加到开头
        MentionHistoryManager.history.unshift({
            ...mention,
            timestamp: Date.now()
        });
        
        // 限制历史大小
        if (MentionHistoryManager.history.length > MentionHistoryManager.maxHistorySize) {
            MentionHistoryManager.history.pop();
        }
        
        // 保存到本地存储
        MentionHistoryManager.saveToStorage();
    },
    
    getRecentMentions: (type, limit = 10) => {
        return MentionHistoryManager.history
            .filter(item => !type || item.type === type)
            .slice(0, limit);
    },
    
    getFrequentMentions: (type, limit = 10) => {
        const counts = {};
        
        MentionHistoryManager.history
            .filter(item => !type || item.type === type)
            .forEach(item => {
                const key = `${item.type}_${item.id}`;
                counts[key] = (counts[key] || 0) + 1;
            });
        
        return Object.entries(counts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, limit)
            .map(([key, count]) => {
                const [type, id] = key.split('_');
                const item = MentionHistoryManager.history.find(
                    h => h.type === type && h.id === parseInt(id)
                );
                return { ...item, count };
            });
    },
    
    saveToStorage: () => {
        try {
            localStorage.setItem(
                'mention_history',
                JSON.stringify(MentionHistoryManager.history)
            );
        } catch (error) {
            console.error('保存提及历史失败:', error);
        }
    },
    
    loadFromStorage: () => {
        try {
            const stored = localStorage.getItem('mention_history');
            if (stored) {
                MentionHistoryManager.history = JSON.parse(stored);
            }
        } catch (error) {
            console.error('加载提及历史失败:', error);
            MentionHistoryManager.history = [];
        }
    }
};
```

### 5. 键盘导航增强

```javascript
// 键盘导航增强
const KeyboardNavigationEnhancer = {
    enhanceNavigation: (mentionList) => {
        const originalOnKeydown = mentionList.onKeydown.bind(mentionList);
        
        mentionList.onKeydown = function(event) {
            switch (event.key) {
                case 'Tab':
                    event.preventDefault();
                    // 选择第一个选项
                    if (this.state.options.length > 0) {
                        this.props.onSelect(this.state.options[0]);
                        this.props.close();
                    }
                    break;
                    
                case 'Enter':
                    event.preventDefault();
                    // 由NavigableList处理
                    break;
                    
                case 'ArrowUp':
                case 'ArrowDown':
                    // 由NavigableList处理
                    break;
                    
                default:
                    originalOnKeydown(event);
            }
        };
    },
    
    addShortcuts: (mentionList) => {
        // Ctrl+Space: 强制显示所有建议
        document.addEventListener('keydown', (event) => {
            if (event.ctrlKey && event.code === 'Space') {
                event.preventDefault();
                mentionList.showAllSuggestions();
            }
        });
        
        // Ctrl+M: 快速提及最近联系人
        document.addEventListener('keydown', (event) => {
            if (event.ctrlKey && event.key === 'm') {
                event.preventDefault();
                mentionList.showRecentMentions();
            }
        });
    }
};
```

## 性能优化

### 1. 搜索防抖

```javascript
// 搜索防抖优化
const SearchDebouncer = {
    debounceSearch: debounce(async (searchTerm, suggestionService, type) => {
        await suggestionService.fetchSuggestions({
            delimiter: type === "partner" ? "@" : "#",
            term: searchTerm,
        });
    }, 300),
    
    optimizedSearch: (mentionList) => {
        const originalEffect = mentionList.searchEffect;
        
        mentionList.searchEffect = () => {
            if (!mentionList.state.searchTerm) {
                mentionList.state.options = [];
                return;
            }
            
            SearchDebouncer.debounceSearch(
                mentionList.state.searchTerm,
                mentionList.suggestionService,
                mentionList.props.type
            );
        };
    }
};
```

### 2. 结果缓存

```javascript
// 搜索结果缓存
const SearchResultCache = {
    cache: new Map(),
    
    getCachedResults: (type, searchTerm) => {
        const key = `${type}_${searchTerm}`;
        const cached = SearchResultCache.cache.get(key);
        
        if (cached && Date.now() - cached.timestamp < 300000) { // 5分钟缓存
            return cached.results;
        }
        
        return null;
    },
    
    setCachedResults: (type, searchTerm, results) => {
        const key = `${type}_${searchTerm}`;
        SearchResultCache.cache.set(key, {
            results,
            timestamp: Date.now()
        });
        
        // 限制缓存大小
        if (SearchResultCache.cache.size > 100) {
            const firstKey = SearchResultCache.cache.keys().next().value;
            SearchResultCache.cache.delete(firstKey);
        }
    }
};
```

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的提及列表组件
- 清晰的组件接口

### 2. 策略模式 (Strategy Pattern)
- 不同类型的不同搜索策略
- 可配置的提及行为

### 3. 观察者模式 (Observer Pattern)
- 监听搜索词变化
- 响应键盘事件

## 注意事项

1. **性能考虑**: 使用防抖避免频繁搜索
2. **用户体验**: 提供清晰的搜索反馈
3. **键盘导航**: 支持完整的键盘操作
4. **移动端适配**: 优化移动设备上的体验

## 扩展建议

1. **智能排序**: 基于使用频率和相关性的智能排序
2. **群组提及**: 支持@all、@here等群组提及
3. **表情符号**: 集成表情符号选择器
4. **自定义过滤**: 支持用户自定义过滤条件
5. **离线支持**: 支持离线状态下的基本提及功能

该组件为邮件和聊天系统提供了完整的提及功能，是现代通信应用的重要组成部分。

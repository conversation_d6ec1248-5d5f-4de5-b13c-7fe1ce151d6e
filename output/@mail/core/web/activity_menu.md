# Activity Menu - 活动菜单组件

## 概述

`activity_menu.js` 实现了 Odoo 邮件系统中的活动菜单组件，用于在系统托盘中显示活动相关的下拉菜单。该组件提供了活动分组显示、快速访问活动列表、按状态过滤活动等功能，支持打开特定活动分组和我的活动页面，是系统托盘中活动管理的核心入口组件，为用户提供了便捷的活动访问和管理功能。

## 文件信息
- **路径**: `/mail/static/src/core/web/activity_menu.js`
- **行数**: 111
- **模块**: `@mail/core/web/activity_menu`

## 依赖关系

```javascript
// UI组件依赖
'@web/core/dropdown/dropdown'       // 下拉框组件
'@web/core/dropdown/dropdown_hooks' // 下拉框钩子

// 工具和钩子依赖
'@mail/utils/common/hooks'          // 邮件工具钩子
'@odoo/owl'                        // OWL 框架
'@web/core/utils/hooks'            // Web 核心钩子
'@web/core/domain'                 // 域工具
'@web/core/user'                   // 用户信息
'@web/core/registry'               // 注册表系统
```

## 组件定义

### ActivityMenu 类

```javascript
class ActivityMenu extends Component {
    static components = { Dropdown };
    static props = [];
    static template = "mail.ActivityMenu";
}
```

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.discussSystray = useDiscussSystray();
    this.store = useState(useService("mail.store"));
    this.action = useService("action");
    this.userId = user.userId;
    this.ui = useState(useService("ui"));
    this.dropdown = useDropdownState();
}
```

**初始化内容**:
- 讨论系统托盘钩子
- 邮件存储的响应式状态
- 动作服务用于打开页面
- 当前用户ID
- UI服务状态
- 下拉框状态管理

## 核心功能

### 1. 数据获取

```javascript
onBeforeOpen() {
    this.store.fetchData({ systray_get_activities: true });
}
```

**数据获取**:
- 在下拉框打开前获取活动数据
- 确保显示最新的活动信息

### 2. 可用视图配置

```javascript
availableViews(group) {
    return [
        [false, "kanban"],
        [false, "list"],
        [false, "form"],
        [false, "activity"],
    ];
}
```

**视图类型**:
- **看板视图**: 卡片式活动显示
- **列表视图**: 表格式活动列表
- **表单视图**: 详细的活动表单
- **活动视图**: 专门的活动管理视图

### 3. 打开活动分组

```javascript
openActivityGroup(group, filter="all") {
    this.dropdown.close();
    const context = {
        force_search_count: 1,
    };
    
    if (group.model === "mail.activity") {
        this.action.doAction("mail.mail_activity_without_access_action", {
            additionalContext: {
                active_ids: group.activity_ids,
                active_model: "mail.activity",
            },
        });
        return;
    }

    // 设置过滤器上下文
    if (filter === "all") {
        context["search_default_activities_overdue"] = 1;
        context["search_default_activities_today"] = 1;
    } else if (filter === "overdue") {
        context["search_default_activities_overdue"] = 1;
    } else if (filter === "today") {
        context["search_default_activities_today"] = 1;
    } else if (filter === "upcoming_all") {
        context["search_default_activities_upcoming_all"] = 1;
    }

    let domain = [["activity_user_id", "=", this.userId]];
    if (group.domain) {
        domain = Domain.and([domain, group.domain]).toList();
    }
    
    const views = this.availableViews(group);

    this.action.doAction({
        context,
        domain,
        name: group.name,
        res_model: group.model,
        search_view_id: [false],
        type: "ir.actions.act_window",
        views,
    }, {
        clearBreadcrumbs: true,
        viewType: group.view_type,
    });
}
```

**打开分组流程**:
1. 关闭下拉框
2. 设置强制搜索计数
3. 处理特殊的邮件活动模型
4. 根据过滤器设置搜索默认值
5. 构建域条件（当前用户的活动）
6. 合并分组域条件
7. 执行动作打开活动列表

### 4. 过滤器处理

```javascript
// 过滤器类型及其对应的搜索默认值
const filterMappings = {
    "all": ["search_default_activities_overdue", "search_default_activities_today"],
    "overdue": ["search_default_activities_overdue"],
    "today": ["search_default_activities_today"],
    "upcoming_all": ["search_default_activities_upcoming_all"]
};
```

**过滤器类型**:
- **all**: 显示逾期和今日活动
- **overdue**: 仅显示逾期活动
- **today**: 仅显示今日活动
- **upcoming_all**: 显示所有即将到来的活动

### 5. 打开我的活动

```javascript
openMyActivities() {
    this.dropdown.close();
    this.action.doAction("mail.mail_activity_action_my", { clearBreadcrumbs: true });
}
```

**我的活动**:
- 关闭下拉框
- 打开预定义的我的活动动作
- 清除面包屑导航

## 使用场景

### 1. 系统托盘活动菜单

```javascript
// 系统托盘中的活动菜单
const SystrayActivityMenu = () => {
    return (
        <div class="systray-activity-menu">
            <ActivityMenu />
        </div>
    );
};
```

### 2. 自定义活动分组处理

```javascript
// 自定义活动分组处理
const CustomActivityGroupHandler = ({ activityMenu }) => {
    const handleCustomGroup = (group) => {
        // 添加自定义逻辑
        logActivityGroupAccess(group);
        
        // 设置自定义上下文
        const customContext = {
            custom_filter: true,
            department_id: getCurrentDepartment().id
        };
        
        // 调用原始方法
        activityMenu.openActivityGroup(group, 'all');
    };
    
    return { handleCustomGroup };
};
```

### 3. 活动统计显示

```javascript
// 活动统计显示
const ActivityStatistics = ({ store }) => {
    const getActivityStats = () => {
        const groups = store.activityGroups || [];
        
        return groups.reduce((stats, group) => {
            stats.total += group.total_count || 0;
            stats.overdue += group.overdue_count || 0;
            stats.today += group.today_count || 0;
            return stats;
        }, { total: 0, overdue: 0, today: 0 });
    };
    
    const stats = getActivityStats();
    
    return (
        <div class="activity-statistics">
            <div class="stat-item">
                <span class="label">总计</span>
                <span class="count">{stats.total}</span>
            </div>
            <div class="stat-item overdue">
                <span class="label">逾期</span>
                <span class="count">{stats.overdue}</span>
            </div>
            <div class="stat-item today">
                <span class="label">今日</span>
                <span class="count">{stats.today}</span>
            </div>
        </div>
    );
};
```

### 4. 快速操作集成

```javascript
// 快速操作集成
const QuickActivityActions = ({ activityMenu }) => {
    const quickActions = [
        {
            name: '查看逾期活动',
            action: () => activityMenu.openActivityGroup({ model: 'all' }, 'overdue'),
            icon: 'fa-exclamation-triangle',
            class: 'danger'
        },
        {
            name: '查看今日活动',
            action: () => activityMenu.openActivityGroup({ model: 'all' }, 'today'),
            icon: 'fa-calendar-day',
            class: 'warning'
        },
        {
            name: '我的所有活动',
            action: () => activityMenu.openMyActivities(),
            icon: 'fa-tasks',
            class: 'primary'
        }
    ];
    
    return (
        <div class="quick-activity-actions">
            {quickActions.map(action => (
                <button 
                    key={action.name}
                    class={`quick-action ${action.class}`}
                    onClick={action.action}
                    title={action.name}
                >
                    <i class={`fa ${action.icon}`}></i>
                    <span>{action.name}</span>
                </button>
            ))}
        </div>
    );
};
```

## 域条件处理

### 1. 基础域构建

```javascript
// 基础域条件构建
const buildBaseDomain = (userId) => {
    return [["activity_user_id", "=", userId]];
};

// 合并域条件
const mergeDomains = (baseDomain, groupDomain) => {
    if (!groupDomain) {
        return baseDomain;
    }
    
    return Domain.and([baseDomain, groupDomain]).toList();
};
```

### 2. 高级域过滤

```javascript
// 高级域过滤
const buildAdvancedDomain = (userId, filters = {}) => {
    let domain = [["activity_user_id", "=", userId]];
    
    // 日期过滤
    if (filters.dateRange) {
        const { start, end } = filters.dateRange;
        domain.push(["date_deadline", ">=", start]);
        domain.push(["date_deadline", "<=", end]);
    }
    
    // 优先级过滤
    if (filters.priority) {
        domain.push(["priority", "=", filters.priority]);
    }
    
    // 状态过滤
    if (filters.state) {
        domain.push(["state", "=", filters.state]);
    }
    
    // 模型过滤
    if (filters.models && filters.models.length > 0) {
        domain.push(["res_model", "in", filters.models]);
    }
    
    return domain;
};
```

### 3. 动态域构建

```javascript
// 动态域构建
const buildDynamicDomain = (userId, groupConfig) => {
    let domain = [["activity_user_id", "=", userId]];
    
    // 根据分组配置添加条件
    if (groupConfig.includeTeam) {
        // 包含团队成员的活动
        const teamUserIds = getTeamUserIds();
        domain = [
            "|",
            ["activity_user_id", "=", userId],
            ["activity_user_id", "in", teamUserIds]
        ];
    }
    
    if (groupConfig.excludeCompleted) {
        domain.push(["state", "!=", "done"]);
    }
    
    if (groupConfig.urgentOnly) {
        domain.push([
            "|",
            ["date_deadline", "<", getTodayString()],
            ["date_deadline", "=", getTodayString()]
        ]);
    }
    
    return domain;
};
```

## 上下文管理

### 1. 搜索上下文

```javascript
// 搜索上下文管理
const SearchContextManager = {
    buildContext: (filter, additionalContext = {}) => {
        const context = {
            force_search_count: 1,
            ...additionalContext
        };
        
        const filterMappings = {
            "all": {
                "search_default_activities_overdue": 1,
                "search_default_activities_today": 1
            },
            "overdue": {
                "search_default_activities_overdue": 1
            },
            "today": {
                "search_default_activities_today": 1
            },
            "upcoming_all": {
                "search_default_activities_upcoming_all": 1
            },
            "this_week": {
                "search_default_activities_this_week": 1
            },
            "next_week": {
                "search_default_activities_next_week": 1
            }
        };
        
        const filterContext = filterMappings[filter] || {};
        return { ...context, ...filterContext };
    }
};
```

### 2. 视图上下文

```javascript
// 视图上下文配置
const ViewContextManager = {
    getViewContext: (group, viewType) => {
        const baseContext = {
            active_model: group.model,
            active_ids: group.activity_ids || []
        };
        
        switch (viewType) {
            case 'kanban':
                return {
                    ...baseContext,
                    kanban_view_ref: 'mail.mail_activity_view_kanban'
                };
            case 'list':
                return {
                    ...baseContext,
                    list_view_ref: 'mail.mail_activity_view_tree'
                };
            case 'activity':
                return {
                    ...baseContext,
                    activity_view_ref: 'mail.mail_activity_view_activity'
                };
            default:
                return baseContext;
        }
    }
};
```

## 性能优化

### 1. 数据缓存

```javascript
// 活动数据缓存
const ActivityDataCache = {
    cache: new Map(),
    
    get: (key) => {
        const cached = ActivityDataCache.cache.get(key);
        if (cached && Date.now() - cached.timestamp < 60000) { // 1分钟缓存
            return cached.data;
        }
        return null;
    },
    
    set: (key, data) => {
        ActivityDataCache.cache.set(key, {
            data,
            timestamp: Date.now()
        });
    },
    
    clear: () => {
        ActivityDataCache.cache.clear();
    }
};
```

### 2. 懒加载

```javascript
// 懒加载活动数据
const lazyLoadActivityData = (store) => {
    let isLoading = false;
    
    return async () => {
        if (isLoading) {
            return;
        }
        
        isLoading = true;
        
        try {
            await store.fetchData({ systray_get_activities: true });
        } finally {
            isLoading = false;
        }
    };
};
```

## 错误处理

### 1. 动作执行错误

```javascript
// 安全的动作执行
const safeDoAction = async (actionService, action, options = {}) => {
    try {
        return await actionService.doAction(action, options);
    } catch (error) {
        console.error('执行动作失败:', error);
        
        // 显示用户友好的错误消息
        const errorMessage = getErrorMessage(error);
        showNotification(errorMessage, 'error');
        
        // 记录错误
        logError({
            type: 'activity_menu_action_error',
            action,
            error: error.message
        });
    }
};

// 获取错误消息
const getErrorMessage = (error) => {
    if (error.message.includes('access')) {
        return '没有权限访问此活动';
    } else if (error.message.includes('network')) {
        return '网络连接错误，请重试';
    } else {
        return '操作失败，请重试';
    }
};
```

### 2. 数据获取错误

```javascript
// 安全的数据获取
const safeFetchData = async (store, options) => {
    try {
        await store.fetchData(options);
    } catch (error) {
        console.error('获取活动数据失败:', error);
        
        // 显示错误状态
        showErrorState('无法加载活动数据');
        
        // 提供重试选项
        showRetryOption(() => safeFetchData(store, options));
    }
};
```

## 系统托盘注册

### 1. 注册配置

```javascript
// 系统托盘注册
registry
    .category("systray")
    .add("mail.activity_menu", { 
        Component: ActivityMenu 
    }, { 
        sequence: 20 
    });
```

**注册参数**:
- **组件**: ActivityMenu 类
- **序列**: 20（控制在系统托盘中的显示顺序）

### 2. 条件注册

```javascript
// 条件性注册
const conditionalRegister = () => {
    // 检查用户权限
    if (hasActivityPermission()) {
        registry
            .category("systray")
            .add("mail.activity_menu", { 
                Component: ActivityMenu 
            }, { 
                sequence: 20 
            });
    }
};

// 检查活动权限
const hasActivityPermission = () => {
    return user.hasGroup('base.group_user');
};
```

## 设计模式

### 1. 门面模式 (Facade Pattern)
- 为复杂的活动操作提供简单接口
- 隐藏底层的动作和域处理复杂性

### 2. 策略模式 (Strategy Pattern)
- 不同过滤器的不同处理策略
- 可配置的视图类型

### 3. 单例模式 (Singleton Pattern)
- 系统托盘中只有一个活动菜单实例
- 全局的活动访问入口

## 注意事项

1. **权限控制**: 确保用户有权限访问活动
2. **性能考虑**: 避免频繁的数据获取
3. **用户体验**: 提供清晰的导航和反馈
4. **数据一致性**: 确保显示的活动数据是最新的

## 扩展建议

1. **自定义过滤器**: 支持用户自定义活动过滤器
2. **快速操作**: 添加更多快速操作按钮
3. **统计显示**: 显示活动统计信息
4. **通知集成**: 集成桌面通知功能
5. **移动端优化**: 优化移动设备上的显示效果

该组件为系统托盘提供了完整的活动访问功能，是活动管理系统的重要入口点。

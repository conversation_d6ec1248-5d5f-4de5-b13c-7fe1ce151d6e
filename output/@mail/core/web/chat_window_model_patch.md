# Chat Window Model Patch - 聊天窗口模型补丁

## 概述

`chat_window_model_patch.js` 实现了 Odoo 邮件系统中聊天窗口模型的Web环境补丁，用于扩展聊天窗口在Web环境下的关闭行为。该补丁主要处理移动设备上从消息菜单打开的聊天窗口的特殊关闭逻辑，确保在移动端关闭聊天窗口时能够重新打开消息菜单，提供更好的用户体验和导航连续性，是聊天窗口在移动端的重要用户体验优化。

## 文件信息
- **路径**: `/mail/static/src/core/web/chat_window_model_patch.js`
- **行数**: 32
- **模块**: `@mail/core/web/chat_window_model_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/chat_window_model'  // 基础聊天窗口模型
'@web/core/utils/patch'                // 补丁工具
```

## 补丁实现

### 聊天窗口模型扩展

```javascript
patch(ChatWindow.prototype, {
    async _onClose(options) {
        // 移动端特殊处理逻辑
        if (
            this.store.env.services.ui.isSmall &&
            !this.store.discuss.isActive &&
            this.fromMessagingMenu
        ) {
            // 重新打开消息菜单
            document.querySelector(".o_menu_systray i[aria-label='Messages']")?.click();
            // 确保消息菜单在聊天窗口关闭前打开
            await Promise.resolve();
        }
        await super._onClose(options);
    },
});
```

## 核心功能

### 1. 移动端关闭逻辑

```javascript
async _onClose(options) {
    if (
        this.store.env.services.ui.isSmall &&
        !this.store.discuss.isActive &&
        this.fromMessagingMenu
    ) {
        // 移动端特殊处理
        document.querySelector(".o_menu_systray i[aria-label='Messages']")?.click();
        await Promise.resolve();
    }
    await super._onClose(options);
}
```

**关闭条件检查**:
- **小屏幕设备**: `this.store.env.services.ui.isSmall`
- **讨论应用未激活**: `!this.store.discuss.isActive`
- **来自消息菜单**: `this.fromMessagingMenu`

**处理逻辑**:
1. 查找消息菜单按钮并点击
2. 等待一个事件循环确保菜单打开
3. 调用父类关闭方法

### 2. DOM操作

```javascript
document.querySelector(".o_menu_systray i[aria-label='Messages']")?.click();
```

**DOM查询**:
- 查找系统托盘中的消息按钮
- 使用ARIA标签定位元素
- 安全调用点击方法（可选链操作符）

### 3. 异步处理

```javascript
await Promise.resolve();
```

**异步等待**:
- 确保消息菜单在聊天窗口关闭前完全打开
- 使用Promise.resolve()等待一个事件循环

## 使用场景

### 1. 移动端聊天窗口管理

```javascript
// 移动端聊天窗口管理器
const MobileChatWindowManager = ({ store, chatWindow }) => {
    const handleChatWindowClose = async (options = {}) => {
        const isMobile = store.env.services.ui.isSmall;
        const isFromMessagingMenu = chatWindow.fromMessagingMenu;
        const isDiscussActive = store.discuss.isActive;
        
        if (isMobile && !isDiscussActive && isFromMessagingMenu) {
            // 记录移动端关闭操作
            logMobileAction('chat_window_close_with_menu_reopen', {
                chatWindowId: chatWindow.id,
                threadId: chatWindow.thread.id
            });
            
            // 重新打开消息菜单
            await reopenMessagingMenu();
            
            // 更新导航状态
            updateMobileNavigationState('messaging_menu');
        }
        
        // 执行关闭操作
        await chatWindow._onClose(options);
    };
    
    const reopenMessagingMenu = async () => {
        const messagingButton = document.querySelector(
            ".o_menu_systray i[aria-label='Messages']"
        );
        
        if (messagingButton) {
            messagingButton.click();
            
            // 等待菜单动画完成
            await new Promise(resolve => setTimeout(resolve, 300));
        }
    };
    
    return {
        handleChatWindowClose,
        reopenMessagingMenu
    };
};
```

### 2. 响应式聊天体验

```javascript
// 响应式聊天体验管理
const ResponsiveChatExperience = ({ store }) => {
    const [screenSize, setScreenSize] = useState('desktop');
    const [chatWindows, setChatWindows] = useState([]);
    
    useEffect(() => {
        const handleResize = () => {
            const isSmall = window.innerWidth < 768;
            const newSize = isSmall ? 'mobile' : 'desktop';
            
            if (screenSize !== newSize) {
                setScreenSize(newSize);
                handleScreenSizeChange(newSize);
            }
        };
        
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, [screenSize]);
    
    const handleScreenSizeChange = (newSize) => {
        if (newSize === 'mobile') {
            // 移动端：关闭所有聊天窗口，打开消息菜单
            chatWindows.forEach(window => {
                if (window.fromMessagingMenu) {
                    window.close({ preserveMenu: true });
                }
            });
        } else {
            // 桌面端：恢复聊天窗口
            restoreChatWindows();
        }
    };
    
    const restoreChatWindows = () => {
        const savedWindows = getSavedChatWindows();
        savedWindows.forEach(windowData => {
            store.ChatWindow.insert(windowData);
        });
    };
    
    return {
        screenSize,
        chatWindows,
        handleScreenSizeChange
    };
};
```

### 3. 导航状态管理

```javascript
// 导航状态管理
const NavigationStateManager = {
    state: {
        currentView: 'desktop',
        messagingMenuOpen: false,
        activeChatWindows: [],
        navigationHistory: []
    },
    
    updateState: (newState) => {
        NavigationStateManager.state = {
            ...NavigationStateManager.state,
            ...newState
        };
        
        // 保存到本地存储
        localStorage.setItem(
            'navigation_state',
            JSON.stringify(NavigationStateManager.state)
        );
    },
    
    handleChatWindowClose: (chatWindow) => {
        const isMobile = chatWindow.store.env.services.ui.isSmall;
        const fromMessagingMenu = chatWindow.fromMessagingMenu;
        
        if (isMobile && fromMessagingMenu) {
            // 更新导航状态
            NavigationStateManager.updateState({
                currentView: 'messaging_menu',
                messagingMenuOpen: true,
                activeChatWindows: NavigationStateManager.state.activeChatWindows.filter(
                    w => w.id !== chatWindow.id
                )
            });
            
            // 添加到导航历史
            NavigationStateManager.addToHistory({
                action: 'chat_window_closed',
                from: 'chat_window',
                to: 'messaging_menu',
                timestamp: Date.now()
            });
        }
    },
    
    addToHistory: (entry) => {
        const history = NavigationStateManager.state.navigationHistory;
        history.push(entry);
        
        // 限制历史记录数量
        if (history.length > 50) {
            history.shift();
        }
        
        NavigationStateManager.updateState({ navigationHistory: history });
    }
};
```

### 4. 用户体验优化

```javascript
// 用户体验优化
const UserExperienceOptimizer = {
    optimizeChatWindowClosing: async (chatWindow) => {
        const store = chatWindow.store;
        const isMobile = store.env.services.ui.isSmall;
        
        if (isMobile && chatWindow.fromMessagingMenu) {
            // 添加关闭动画
            await UserExperienceOptimizer.animateChatWindowClose(chatWindow);
            
            // 预加载消息菜单
            await UserExperienceOptimizer.preloadMessagingMenu();
            
            // 平滑过渡到消息菜单
            await UserExperienceOptimizer.smoothTransitionToMenu();
        }
    },
    
    animateChatWindowClose: async (chatWindow) => {
        const element = document.querySelector(`[data-chat-window-id="${chatWindow.id}"]`);
        if (element) {
            element.style.transition = 'transform 0.3s ease-out, opacity 0.3s ease-out';
            element.style.transform = 'translateY(100%)';
            element.style.opacity = '0';
            
            await new Promise(resolve => setTimeout(resolve, 300));
        }
    },
    
    preloadMessagingMenu: async () => {
        // 预加载消息菜单数据
        const store = getCurrentStore();
        if (store && !store.messagingMenu.isLoaded) {
            await store.messagingMenu.load();
        }
    },
    
    smoothTransitionToMenu: async () => {
        const messagingButton = document.querySelector(
            ".o_menu_systray i[aria-label='Messages']"
        );
        
        if (messagingButton) {
            // 添加视觉反馈
            messagingButton.style.transform = 'scale(1.1)';
            messagingButton.style.transition = 'transform 0.2s ease-out';
            
            messagingButton.click();
            
            // 恢复按钮样式
            setTimeout(() => {
                messagingButton.style.transform = 'scale(1)';
            }, 200);
        }
    }
};
```

### 5. 错误处理和回退

```javascript
// 错误处理和回退机制
const ChatWindowErrorHandler = {
    handleCloseError: async (chatWindow, error) => {
        console.error('聊天窗口关闭失败:', error);
        
        try {
            // 尝试回退方案
            await ChatWindowErrorHandler.fallbackClose(chatWindow);
        } catch (fallbackError) {
            console.error('回退关闭方案也失败:', fallbackError);
            
            // 最后的错误处理
            ChatWindowErrorHandler.handleCriticalError(chatWindow, fallbackError);
        }
    },
    
    fallbackClose: async (chatWindow) => {
        // 简单的关闭方案，不处理消息菜单
        const element = document.querySelector(`[data-chat-window-id="${chatWindow.id}"]`);
        if (element) {
            element.remove();
        }
        
        // 清理内存中的引用
        chatWindow.store.ChatWindow.delete(chatWindow);
    },
    
    handleCriticalError: (chatWindow, error) => {
        // 记录错误
        logError({
            type: 'chat_window_close_critical_error',
            chatWindowId: chatWindow.id,
            error: error.message,
            stack: error.stack
        });
        
        // 显示用户友好的错误消息
        showNotification(
            '聊天窗口关闭时出现问题，请刷新页面',
            'error'
        );
        
        // 可选：自动刷新页面
        if (confirm('是否刷新页面以解决问题？')) {
            window.location.reload();
        }
    }
};
```

## 设备检测

### 1. 屏幕尺寸检测

```javascript
// 屏幕尺寸检测工具
const ScreenDetector = {
    isSmallScreen: () => {
        return window.innerWidth < 768 || window.innerHeight < 600;
    },
    
    isMobileDevice: () => {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
            navigator.userAgent
        );
    },
    
    getTouchCapability: () => {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    },
    
    getDeviceInfo: () => {
        return {
            isSmall: ScreenDetector.isSmallScreen(),
            isMobile: ScreenDetector.isMobileDevice(),
            hasTouch: ScreenDetector.getTouchCapability(),
            width: window.innerWidth,
            height: window.innerHeight
        };
    }
};
```

### 2. 动态适配

```javascript
// 动态适配管理
const DynamicAdapter = {
    adaptChatWindowBehavior: (chatWindow) => {
        const deviceInfo = ScreenDetector.getDeviceInfo();
        
        if (deviceInfo.isSmall) {
            // 小屏幕适配
            return {
                closeStrategy: 'return_to_menu',
                animation: 'slide_down',
                preserveState: true
            };
        } else {
            // 大屏幕适配
            return {
                closeStrategy: 'simple_close',
                animation: 'fade_out',
                preserveState: false
            };
        }
    },
    
    applyAdaptation: (chatWindow, adaptation) => {
        chatWindow.closeStrategy = adaptation.closeStrategy;
        chatWindow.animation = adaptation.animation;
        chatWindow.preserveState = adaptation.preserveState;
    }
};
```

## 性能优化

### 1. DOM查询优化

```javascript
// DOM查询缓存
const DOMQueryCache = {
    cache: new Map(),
    
    getCachedElement: (selector) => {
        if (DOMQueryCache.cache.has(selector)) {
            const cached = DOMQueryCache.cache.get(selector);
            // 检查元素是否仍在DOM中
            if (document.contains(cached)) {
                return cached;
            } else {
                DOMQueryCache.cache.delete(selector);
            }
        }
        
        const element = document.querySelector(selector);
        if (element) {
            DOMQueryCache.cache.set(selector, element);
        }
        
        return element;
    },
    
    clearCache: () => {
        DOMQueryCache.cache.clear();
    }
};
```

### 2. 异步操作优化

```javascript
// 异步操作优化
const AsyncOptimizer = {
    debouncedClose: debounce(async (chatWindow, options) => {
        await chatWindow._onClose(options);
    }, 100),
    
    batchCloseOperations: async (chatWindows) => {
        const promises = chatWindows.map(window => 
            window._onClose({ batch: true })
        );
        
        return Promise.all(promises);
    }
};
```

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 非侵入式功能扩展
- 保持原有模型的完整性

### 2. 策略模式 (Strategy Pattern)
- 不同设备的不同关闭策略
- 可配置的行为模式

### 3. 适配器模式 (Adapter Pattern)
- 适配移动端和桌面端的不同行为
- 统一的接口调用

## 注意事项

1. **设备检测**: 正确识别移动设备和小屏幕
2. **DOM操作**: 安全的DOM元素查询和操作
3. **异步处理**: 正确处理异步操作的时序
4. **用户体验**: 提供流畅的导航体验

## 扩展建议

1. **手势支持**: 添加滑动手势关闭聊天窗口
2. **动画效果**: 增强关闭和打开的动画效果
3. **状态保存**: 保存聊天窗口状态以便恢复
4. **智能预测**: 基于用户行为预测导航需求
5. **无障碍优化**: 改进移动端的无障碍访问

该补丁为聊天窗口在移动端提供了重要的用户体验优化，确保了导航的连续性和一致性。

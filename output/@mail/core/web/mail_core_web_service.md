# Mail Core Web Service - 邮件核心Web服务

## 概述

`mail_core_web_service.js` 实现了 Odoo 邮件系统在Web环境下的核心服务，负责处理邮件相关的实时通知和状态同步。该服务通过总线服务监听活动更新、消息删除、收件箱消息、消息已读等事件，自动更新本地存储的计数器和状态，确保Web界面与服务器数据的实时同步，是邮件系统在Web环境下的重要基础设施服务。

## 文件信息
- **路径**: `/mail/static/src/core/web/mail_core_web_service.js`
- **行数**: 108
- **模块**: `@mail/core/web/mail_core_web_service`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'              // OWL 框架（响应式系统）
'@web/core/registry'     // 注册表系统
```

## 服务定义

### MailCoreWeb 类

```javascript
class MailCoreWeb {
    constructor(env, services) {
        this.env = env;
        this.busService = services.bus_service;
        this.store = services["mail.store"];
    }
}
```

### 服务配置

```javascript
const mailCoreWeb = {
    dependencies: ["bus_service", "mail.store"],
    start(env, services) {
        const mailCoreWeb = reactive(new MailCoreWeb(env, services));
        mailCoreWeb.setup();
        return mailCoreWeb;
    }
};
```

**服务特性**:
- 依赖总线服务和邮件存储
- 使用响应式系统
- 自动启动和设置

## 核心功能

### 1. 活动更新监听

```javascript
this.busService.subscribe("mail.activity/updated", (payload, { id: notifId }) => {
    if (payload.activity_created && notifId > this.store.activity_counter_bus_id) {
        this.store.activityCounter++;
    }
    if (payload.activity_deleted && notifId > this.store.activity_counter_bus_id) {
        this.store.activityCounter--;
    }
});
```

**活动更新处理**:
- 监听活动创建和删除事件
- 根据通知ID防止重复处理
- 自动更新活动计数器

### 2. 消息删除监听

```javascript
this.env.bus.addEventListener("mail.message/delete", ({ detail: { message, notifId } }) => {
    if (message.needaction && notifId > this.store.inbox.counter_bus_id) {
        this.store.inbox.counter--;
    }
    if (message.starred && notifId > this.store.starred.counter_bus_id) {
        this.store.starred.counter--;
    }
});
```

**删除处理逻辑**:
- 监听消息删除事件
- 更新收件箱计数器（如果是需要操作的消息）
- 更新星标计数器（如果是星标消息）

### 3. 收件箱消息监听

```javascript
this.busService.subscribe("mail.message/inbox", (payload, { id: notifId }) => {
    const { Message: messages = [] } = this.store.insert(payload, { html: true });
    const [message] = messages;
    const inbox = this.store.inbox;
    
    if (notifId > inbox.counter_bus_id) {
        inbox.counter++;
    }
    
    inbox.messages.add(message);
    
    if (message.thread && notifId > message.thread.message_needaction_counter_bus_id) {
        message.thread.message_needaction_counter++;
    }
});
```

**收件箱处理**:
1. 插入新消息到存储
2. 更新收件箱计数器
3. 添加消息到收件箱
4. 更新线程的需要操作消息计数器

### 4. 消息已读监听

```javascript
this.busService.subscribe("mail.message/mark_as_read", (payload, { id: notifId }) => {
    const { message_ids: messageIds, needaction_inbox_counter } = payload;
    const inbox = this.store.inbox;
    
    for (const messageId of messageIds) {
        const message = this.store.Message.get(messageId);
        if (!message) {
            continue;
        }
        
        // 更新线程计数器
        const thread = message.thread;
        if (thread && message.needaction && notifId > thread.message_needaction_counter_bus_id) {
            thread.message_needaction_counter--;
        }
        
        // 将消息从收件箱移动到历史记录
        message.needaction = false;
        inbox.messages.delete({ id: messageId });
        const history = this.store.history;
        history.messages.add(message);
    }
    
    if (notifId > inbox.counter_bus_id) {
        inbox.counter = needaction_inbox_counter;
        inbox.counter_bus_id = notifId;
    }
    
    if (inbox.counter > inbox.messages.length) {
        inbox.fetchMoreMessages();
    }
});
```

**已读处理流程**:
1. 遍历已读消息ID列表
2. 更新线程的需要操作消息计数器
3. 设置消息为非需要操作状态
4. 从收件箱移除消息
5. 添加消息到历史记录
6. 更新收件箱计数器
7. 必要时获取更多消息

## 使用场景

### 1. 实时活动计数更新

```javascript
// 当用户创建新活动时，所有客户端自动更新计数
const createActivity = async (activityData) => {
    await orm.call('mail.activity', 'create', [activityData]);
    
    // 服务器会发送 "mail.activity/updated" 事件
    // MailCoreWeb 服务会自动更新 store.activityCounter
    // UI 会自动反映新的计数
};

// 监听活动计数变化
const ActivityCounter = () => {
    const store = useService("mail.store");
    
    return (
        <div class="activity-counter">
            活动数量: {store.activityCounter}
        </div>
    );
};
```

### 2. 收件箱实时同步

```javascript
// 收件箱组件自动同步
const InboxComponent = () => {
    const store = useService("mail.store");
    const inbox = store.inbox;
    
    return (
        <div class="inbox">
            <div class="inbox-header">
                收件箱 ({inbox.counter})
            </div>
            <div class="inbox-messages">
                {inbox.messages.map(message => (
                    <MessageItem key={message.id} message={message} />
                ))}
            </div>
        </div>
    );
};
```

### 3. 消息状态同步

```javascript
// 消息已读状态的实时同步
const markMessagesAsRead = async (messageIds) => {
    await orm.call('mail.message', 'mark_as_read', [messageIds]);
    
    // 服务器发送 "mail.message/mark_as_read" 事件
    // MailCoreWeb 服务自动处理:
    // 1. 更新消息状态
    // 2. 移动消息到历史记录
    // 3. 更新计数器
    // 4. 刷新UI
};
```

### 4. 多客户端状态同步

```javascript
// 多个浏览器标签页或用户之间的状态同步
const MultiClientSync = () => {
    const store = useService("mail.store");
    
    // 当其他客户端删除消息时，当前客户端自动更新
    useEffect(() => {
        const handleMessageDelete = (event) => {
            const { message } = event.detail;
            console.log(`消息 ${message.id} 已被删除`);
            // UI 自动更新，无需手动处理
        };
        
        env.bus.addEventListener("mail.message/delete", handleMessageDelete);
        
        return () => {
            env.bus.removeEventListener("mail.message/delete", handleMessageDelete);
        };
    }, []);
    
    return <div>多客户端同步组件</div>;
};
```

## 事件处理机制

### 1. 通知ID防重复

```javascript
// 防止重复处理相同通知的机制
const preventDuplicateProcessing = (notifId, lastProcessedId) => {
    if (notifId <= lastProcessedId) {
        console.log(`跳过重复通知: ${notifId}`);
        return false;
    }
    return true;
};

// 在各个事件处理器中的应用
const handleActivityUpdate = (payload, { id: notifId }) => {
    if (!preventDuplicateProcessing(notifId, store.activity_counter_bus_id)) {
        return;
    }
    
    // 处理活动更新
    if (payload.activity_created) {
        store.activityCounter++;
        store.activity_counter_bus_id = notifId;
    }
};
```

### 2. 状态一致性保证

```javascript
// 确保状态一致性的机制
const ensureStateConsistency = () => {
    // 定期检查计数器一致性
    setInterval(() => {
        const localCount = store.inbox.messages.length;
        const serverCount = store.inbox.counter;
        
        if (Math.abs(localCount - serverCount) > 10) {
            console.warn('检测到计数器不一致，重新同步');
            store.inbox.fetchMoreMessages();
        }
    }, 30000); // 每30秒检查一次
};
```

### 3. 错误恢复

```javascript
// 错误恢复机制
const handleEventError = (eventType, error, payload) => {
    console.error(`处理事件 ${eventType} 时出错:`, error);
    
    // 记录错误
    reportError({
        type: 'mail_core_web_event_error',
        eventType,
        error: error.message,
        payload
    });
    
    // 尝试恢复
    switch (eventType) {
        case 'mail.message/inbox':
            // 重新获取收件箱消息
            store.inbox.fetchMoreMessages();
            break;
        case 'mail.activity/updated':
            // 重新获取活动计数
            refreshActivityCounter();
            break;
    }
};
```

## 性能优化

### 1. 批量处理

```javascript
// 批量处理消息更新
const batchMessageUpdates = (() => {
    let pendingUpdates = [];
    let timeoutId = null;
    
    return (messageUpdate) => {
        pendingUpdates.push(messageUpdate);
        
        if (timeoutId) {
            clearTimeout(timeoutId);
        }
        
        timeoutId = setTimeout(() => {
            processBatchUpdates(pendingUpdates);
            pendingUpdates = [];
            timeoutId = null;
        }, 100); // 100ms 批量处理
    };
})();
```

### 2. 内存管理

```javascript
// 内存管理优化
const manageMemoryUsage = () => {
    // 限制历史消息数量
    const MAX_HISTORY_MESSAGES = 1000;
    
    if (store.history.messages.length > MAX_HISTORY_MESSAGES) {
        const excessCount = store.history.messages.length - MAX_HISTORY_MESSAGES;
        const oldestMessages = store.history.messages
            .slice(0, excessCount)
            .map(m => m.id);
        
        // 移除最旧的消息
        oldestMessages.forEach(id => {
            store.history.messages.delete({ id });
        });
    }
};
```

### 3. 事件节流

```javascript
// 事件处理节流
const throttledEventHandlers = {
    'mail.message/mark_as_read': throttle((payload, meta) => {
        handleMarkAsRead(payload, meta);
    }, 200),
    
    'mail.activity/updated': throttle((payload, meta) => {
        handleActivityUpdate(payload, meta);
    }, 100)
};
```

## 调试和监控

### 1. 事件日志

```javascript
// 事件处理日志
const logEventProcessing = (eventType, payload, meta) => {
    if (DEBUG_MODE) {
        console.log(`[MailCoreWeb] 处理事件: ${eventType}`, {
            notifId: meta.id,
            payload,
            timestamp: new Date().toISOString()
        });
    }
};
```

### 2. 状态监控

```javascript
// 状态监控仪表板
const StateMonitor = () => {
    const store = useService("mail.store");
    
    return (
        <div class="state-monitor">
            <div>活动计数: {store.activityCounter}</div>
            <div>收件箱计数: {store.inbox.counter}</div>
            <div>收件箱消息: {store.inbox.messages.length}</div>
            <div>历史消息: {store.history.messages.length}</div>
            <div>星标计数: {store.starred.counter}</div>
        </div>
    );
};
```

## 设计模式

### 1. 观察者模式 (Observer Pattern)
- 监听总线事件
- 响应状态变化

### 2. 发布订阅模式 (Pub/Sub Pattern)
- 通过总线服务订阅事件
- 解耦事件生产者和消费者

### 3. 单例模式 (Singleton Pattern)
- 服务在应用中只有一个实例
- 全局状态管理

## 注意事项

1. **通知ID管理**: 正确处理通知ID防止重复
2. **状态一致性**: 确保本地状态与服务器同步
3. **内存管理**: 避免消息数据无限增长
4. **错误处理**: 提供完善的错误恢复机制

## 扩展建议

1. **离线支持**: 支持离线状态下的事件缓存
2. **压缩优化**: 对大量消息数据进行压缩
3. **智能同步**: 基于用户活跃度的智能同步策略
4. **性能监控**: 添加详细的性能监控指标
5. **事件重放**: 支持事件重放和状态恢复

该服务是邮件系统在Web环境下的核心基础设施，确保了实时数据同步和状态一致性。

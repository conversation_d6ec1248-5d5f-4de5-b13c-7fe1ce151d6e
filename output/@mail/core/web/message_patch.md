# Message Patch - 消息补丁

## 概述

`message_patch.js` 实现了 Odoo 邮件系统中消息模型的Web环境补丁，用于扩展消息在Web环境下的功能。该补丁添加了作者头像卡片、可点击作者、记录打开、跟踪值格式化等功能，增强了消息的交互性和显示效果，支持多种数据类型的格式化显示，是消息系统在Web环境下的重要用户体验扩展。

## 文件信息
- **路径**: `/mail/static/src/core/web/message_patch.js`
- **行数**: 130
- **模块**: `@mail/core/web/message_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/message'                    // 基础消息模型
'@web/core/utils/misc'                        // 工具函数
'@web/core/l10n/dates'                        // 日期本地化
'@web/core/l10n/translation'                  // 国际化
'@web/views/fields/formatters'                // 字段格式化器
'@web/core/utils/hooks'                       // Web 核心钩子
'@web/core/popover/popover_hook'              // 弹出框钩子
'@web/core/utils/patch'                       // 补丁工具
'@mail/discuss/web/avatar_card/avatar_card_popover' // 头像卡片弹出框
```

## 补丁实现

### 消息模型扩展

```javascript
patch(Message.prototype, {
    setup() {
        super.setup(...arguments);
        this.action = useService("action");
        this.avatarCard = usePopover(AvatarCardPopover);
    },
    // 其他方法...
});
```

## 核心功能

### 1. 设置扩展

```javascript
setup() {
    super.setup(...arguments);
    this.action = useService("action");
    this.avatarCard = usePopover(AvatarCardPopover);
}
```

**设置扩展内容**:
- 动作服务用于打开记录
- 头像卡片弹出框服务

### 2. 作者头像样式

```javascript
get authorAvatarAttClass() {
    return {
        ...super.authorAvatarAttClass,
        "o_redirect cursor-pointer": this.hasAuthorClickable(),
    };
}
```

**头像样式**:
- 继承父类样式
- 可点击作者时添加重定向和指针样式

### 3. 作者样式

```javascript
getAuthorAttClass() {
    return {
        ...super.getAuthorAttClass(),
        "cursor-pointer o-hover-text-underline": this.hasAuthorClickable(),
    };
}
```

**作者样式**:
- 继承父类样式
- 可点击时添加指针和悬停下划线样式

### 4. 作者文本

```javascript
getAuthorText() {
    return this.hasAuthorClickable() ? _t("Open card") : undefined;
}
```

**作者文本**:
- 可点击时显示"Open card"提示
- 不可点击时返回undefined

### 5. 头像容器样式

```javascript
getAvatarContainerAttClass() {
    return {
        ...super.getAvatarContainerAttClass(),
        "cursor-pointer": this.hasAuthorClickable(),
    };
}
```

**容器样式**:
- 继承父类样式
- 可点击时添加指针样式

### 6. 作者可点击检查

```javascript
hasAuthorClickable() {
    return this.message.author?.userId;
}
```

**可点击条件**:
- 消息作者存在且有用户ID

### 7. 作者点击处理

```javascript
onClickAuthor(ev) {
    if (this.hasAuthorClickable()) {
        markEventHandled(ev, "Message.ClickAuthor");
        const target = ev.currentTarget;
        if (!this.avatarCard.isOpen) {
            this.avatarCard.open(target, {
                id: this.message.author.userId,
            });
        }
    }
}
```

**点击处理**:
- 检查作者是否可点击
- 标记事件已处理
- 如果头像卡片未打开，则打开卡片

### 8. 打开记录

```javascript
openRecord() {
    this.message.thread.open();
}
```

**记录打开**:
- 打开消息所属的线程

### 9. 跟踪值格式化

```javascript
formatTracking(trackingType, trackingValue) {
    switch (trackingType) {
        case "boolean":
            return trackingValue.value ? _t("Yes") : _t("No");
        case "char":
        case "many2one":
        case "selection":
            return formatChar(trackingValue.value);
        case "date": {
            const value = trackingValue.value
                ? deserializeDate(trackingValue.value)
                : trackingValue.value;
            return formatDate(value);
        }
        case "datetime": {
            const value = trackingValue.value
                ? deserializeDateTime(trackingValue.value)
                : trackingValue.value;
            return formatDateTime(value);
        }
        case "float":
            return formatFloat(trackingValue.value);
        case "integer":
            return formatInteger(trackingValue.value);
        case "text":
            return formatText(trackingValue.value);
        case "monetary":
            return formatMonetary(trackingValue.value, {
                currencyId: trackingValue.currencyId,
            });
        default:
            return trackingValue.value;
    }
}
```

**格式化类型**:
- **boolean**: 显示"Yes"或"No"
- **char/many2one/selection**: 字符格式化
- **date**: 日期格式化
- **datetime**: 日期时间格式化
- **float**: 浮点数格式化
- **integer**: 整数格式化
- **text**: 文本格式化
- **monetary**: 货币格式化
- **default**: 原始值

### 10. 跟踪值或无格式化

```javascript
formatTrackingOrNone(trackingType, trackingValue) {
    const formattedValue = this.formatTracking(trackingType, trackingValue);
    return formattedValue || _t("None");
}
```

**格式化或无**:
- 调用格式化方法
- 如果结果为空，显示"None"

## 使用场景

### 1. 消息作者交互

```javascript
// 消息作者交互处理
const MessageAuthorInteraction = ({ message }) => {
    const handleAuthorClick = (event) => {
        if (message.hasAuthorClickable()) {
            // 记录点击事件
            logUserInteraction('author_click', {
                messageId: message.id,
                authorId: message.author.userId
            });
            
            // 调用补丁方法
            message.onClickAuthor(event);
            
            // 更新用户活动
            updateUserActivity('viewed_author_card');
        }
    };
    
    return (
        <div class="message-author-section">
            <div 
                class={message.getAvatarContainerAttClass()}
                onClick={handleAuthorClick}
            >
                <img 
                    src={message.author.avatarUrl}
                    class={message.authorAvatarAttClass}
                    alt={message.author.name}
                />
            </div>
            
            <span 
                class={message.getAuthorAttClass()}
                onClick={handleAuthorClick}
                title={message.getAuthorText()}
            >
                {message.author.name}
            </span>
        </div>
    );
};
```

### 2. 跟踪值显示

```javascript
// 跟踪值显示组件
const TrackingValueDisplay = ({ message, trackingValues }) => {
    const renderTrackingValue = (tracking) => {
        const oldValue = message.formatTrackingOrNone(tracking.field_type, tracking.old_value);
        const newValue = message.formatTrackingOrNone(tracking.field_type, tracking.new_value);
        
        return (
            <div key={tracking.field_name} class="tracking-value-item">
                <span class="field-name">{tracking.field_desc}:</span>
                <span class="value-change">
                    <span class="old-value">{oldValue}</span>
                    <span class="arrow">→</span>
                    <span class="new-value">{newValue}</span>
                </span>
            </div>
        );
    };
    
    return (
        <div class="tracking-values">
            <h4>字段变更</h4>
            {trackingValues.map(renderTrackingValue)}
        </div>
    );
};
```

### 3. 消息记录打开

```javascript
// 消息记录打开处理
const MessageRecordOpener = ({ message }) => {
    const handleOpenRecord = () => {
        // 记录打开操作
        logUserAction('open_record_from_message', {
            messageId: message.id,
            threadId: message.thread.id,
            threadModel: message.thread.model
        });
        
        // 调用补丁方法
        message.openRecord();
        
        // 更新导航历史
        updateNavigationHistory({
            type: 'thread',
            id: message.thread.id,
            model: message.thread.model
        });
    };
    
    return (
        <button 
            onClick={handleOpenRecord}
            class="open-record-btn"
            title="打开相关记录"
        >
            <i class="fa fa-external-link"></i>
            打开记录
        </button>
    );
};
```

### 4. 头像卡片集成

```javascript
// 头像卡片集成
const AvatarCardIntegration = ({ message, avatarCardService }) => {
    const [cardOpen, setCardOpen] = useState(false);
    
    const handleAvatarClick = (event) => {
        if (!message.hasAuthorClickable()) {
            return;
        }
        
        const target = event.currentTarget;
        
        if (!cardOpen) {
            avatarCardService.open(target, {
                id: message.author.userId,
                onOpen: () => setCardOpen(true),
                onClose: () => setCardOpen(false)
            });
        }
    };
    
    return (
        <div class="avatar-card-integration">
            <div 
                class="avatar-container"
                onClick={handleAvatarClick}
                data-card-open={cardOpen}
            >
                <img 
                    src={message.author.avatarUrl}
                    alt={message.author.name}
                    class="author-avatar"
                />
                {cardOpen && (
                    <div class="card-indicator">
                        <i class="fa fa-info-circle"></i>
                    </div>
                )}
            </div>
        </div>
    );
};
```

### 5. 格式化工具集成

```javascript
// 格式化工具集成
const FormattingToolsIntegration = ({ message }) => {
    const formatters = {
        boolean: (value) => message.formatTracking('boolean', { value }),
        date: (value) => message.formatTracking('date', { value }),
        datetime: (value) => message.formatTracking('datetime', { value }),
        float: (value) => message.formatTracking('float', { value }),
        integer: (value) => message.formatTracking('integer', { value }),
        monetary: (value, currencyId) => message.formatTracking('monetary', { 
            value, 
            currencyId 
        }),
        text: (value) => message.formatTracking('text', { value }),
        char: (value) => message.formatTracking('char', { value })
    };
    
    const formatValue = (type, value, options = {}) => {
        try {
            if (type === 'monetary') {
                return formatters.monetary(value, options.currencyId);
            } else {
                return formatters[type] ? formatters[type](value) : value;
            }
        } catch (error) {
            console.error('格式化失败:', error);
            return value;
        }
    };
    
    return {
        formatValue,
        formatters
    };
};
```

## 数据类型处理

### 1. 日期时间处理

```javascript
// 日期时间处理工具
const DateTimeHandler = {
    formatDate: (value) => {
        if (!value) return '';
        
        try {
            const date = deserializeDate(value);
            return formatDate(date);
        } catch (error) {
            console.error('日期格式化失败:', error);
            return value;
        }
    },
    
    formatDateTime: (value) => {
        if (!value) return '';
        
        try {
            const dateTime = deserializeDateTime(value);
            return formatDateTime(dateTime);
        } catch (error) {
            console.error('日期时间格式化失败:', error);
            return value;
        }
    },
    
    isValidDate: (value) => {
        try {
            const date = new Date(value);
            return !isNaN(date.getTime());
        } catch {
            return false;
        }
    }
};
```

### 2. 数值处理

```javascript
// 数值处理工具
const NumberHandler = {
    formatFloat: (value, precision = 2) => {
        if (value === null || value === undefined) return '';
        
        try {
            return formatFloat(value, { digits: [false, precision] });
        } catch (error) {
            console.error('浮点数格式化失败:', error);
            return value;
        }
    },
    
    formatInteger: (value) => {
        if (value === null || value === undefined) return '';
        
        try {
            return formatInteger(value);
        } catch (error) {
            console.error('整数格式化失败:', error);
            return value;
        }
    },
    
    formatMonetary: (value, currencyId) => {
        if (value === null || value === undefined) return '';
        
        try {
            return formatMonetary(value, { currencyId });
        } catch (error) {
            console.error('货币格式化失败:', error);
            return value;
        }
    }
};
```

### 3. 文本处理

```javascript
// 文本处理工具
const TextHandler = {
    formatText: (value, maxLength = null) => {
        if (!value) return '';
        
        try {
            let formatted = formatText(value);
            
            if (maxLength && formatted.length > maxLength) {
                formatted = formatted.substring(0, maxLength) + '...';
            }
            
            return formatted;
        } catch (error) {
            console.error('文本格式化失败:', error);
            return value;
        }
    },
    
    formatChar: (value, maxLength = null) => {
        if (!value) return '';
        
        try {
            let formatted = formatChar(value);
            
            if (maxLength && formatted.length > maxLength) {
                formatted = formatted.substring(0, maxLength) + '...';
            }
            
            return formatted;
        } catch (error) {
            console.error('字符格式化失败:', error);
            return value;
        }
    }
};
```

## 样式管理

### 1. 动态样式类

```javascript
// 动态样式类管理
const StyleClassManager = {
    getAuthorStyles: (message) => {
        const baseClasses = ['message-author'];
        
        if (message.hasAuthorClickable()) {
            baseClasses.push('clickable', 'cursor-pointer', 'o-hover-text-underline');
        }
        
        if (message.author?.isOnline) {
            baseClasses.push('online');
        }
        
        return baseClasses.join(' ');
    },
    
    getAvatarStyles: (message) => {
        const baseClasses = ['author-avatar'];
        
        if (message.hasAuthorClickable()) {
            baseClasses.push('o_redirect', 'cursor-pointer');
        }
        
        if (message.author?.isBot) {
            baseClasses.push('bot-avatar');
        }
        
        return baseClasses.join(' ');
    }
};
```

### 2. 主题适配

```javascript
// 主题适配
const ThemeAdapter = {
    getThemeClasses: (theme) => {
        const themeClasses = {
            light: {
                author: 'text-dark',
                avatar: 'border-light',
                tracking: 'bg-light'
            },
            dark: {
                author: 'text-light',
                avatar: 'border-dark',
                tracking: 'bg-dark'
            }
        };
        
        return themeClasses[theme] || themeClasses.light;
    }
};
```

## 性能优化

### 1. 格式化缓存

```javascript
// 格式化结果缓存
const FormattingCache = {
    cache: new Map(),
    
    getCachedFormat: (type, value, options = {}) => {
        const key = `${type}_${JSON.stringify(value)}_${JSON.stringify(options)}`;
        return FormattingCache.cache.get(key);
    },
    
    setCachedFormat: (type, value, options, result) => {
        const key = `${type}_${JSON.stringify(value)}_${JSON.stringify(options)}`;
        FormattingCache.cache.set(key, result);
        
        // 限制缓存大小
        if (FormattingCache.cache.size > 1000) {
            const firstKey = FormattingCache.cache.keys().next().value;
            FormattingCache.cache.delete(firstKey);
        }
    }
};
```

### 2. 懒加载头像卡片

```javascript
// 懒加载头像卡片
const LazyAvatarCard = {
    loadCard: async (userId) => {
        try {
            // 检查缓存
            const cached = AvatarCardCache.get(userId);
            if (cached) {
                return cached;
            }
            
            // 加载用户数据
            const userData = await loadUserData(userId);
            
            // 缓存结果
            AvatarCardCache.set(userId, userData);
            
            return userData;
        } catch (error) {
            console.error('加载头像卡片失败:', error);
            throw error;
        }
    }
};
```

## 错误处理

### 1. 格式化错误处理

```javascript
// 安全的格式化
const safeFormat = (formatFunction, value, fallback = '') => {
    try {
        return formatFunction(value) || fallback;
    } catch (error) {
        console.error('格式化失败:', error);
        return fallback;
    }
};
```

### 2. 点击事件错误处理

```javascript
// 安全的点击处理
const safeClickHandler = (handler, event) => {
    try {
        handler(event);
    } catch (error) {
        console.error('点击处理失败:', error);
        showNotification('操作失败，请重试', 'error');
    }
};
```

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 非侵入式功能扩展
- 保持原有消息模型的完整性

### 2. 策略模式 (Strategy Pattern)
- 不同数据类型的不同格式化策略
- 可扩展的格式化系统

### 3. 装饰器模式 (Decorator Pattern)
- 为消息添加额外的交互功能
- 不修改原有结构

## 注意事项

1. **数据类型**: 正确处理各种数据类型的格式化
2. **用户体验**: 提供清晰的交互反馈
3. **性能考虑**: 避免频繁的格式化计算
4. **错误处理**: 处理格式化和交互错误

## 扩展建议

1. **富文本支持**: 增强文本格式化功能
2. **自定义格式化**: 支持用户自定义格式化规则
3. **国际化**: 增强多语言格式化支持
4. **无障碍**: 改进无障碍访问支持
5. **移动端优化**: 优化移动设备上的交互体验

该补丁为消息系统在Web环境下提供了重要的交互和显示功能扩展，增强了用户体验和数据展示效果。

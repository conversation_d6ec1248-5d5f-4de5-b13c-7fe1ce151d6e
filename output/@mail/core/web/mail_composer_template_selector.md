# Mail Composer Template Selector - 邮件编写模板选择器

## 概述

`mail_composer_template_selector.js` 实现了 Odoo 邮件系统中邮件编写器的模板选择器字段组件，用于在邮件编写界面中管理和使用邮件模板。该组件支持模板的加载、删除、覆盖、保存等操作，提供了下拉菜单界面用于快速选择模板，支持模板搜索和批量操作，通过确认对话框确保操作安全，为用户提供了完整的邮件模板管理功能。

## 文件信息
- **路径**: `/mail/static/src/core/web/mail_composer_template_selector.js`
- **行数**: 165
- **模块**: `@mail/core/web/mail_composer_template_selector`

## 依赖关系

```javascript
// UI组件依赖
'@web/core/confirmation_dialog/confirmation_dialog'  // 确认对话框
'@web/core/dropdown/dropdown'                       // 下拉菜单
'@web/core/dropdown/dropdown_item'                  // 下拉菜单项
'@web/views/view_dialogs/select_create_dialog'      // 选择创建对话框

// 核心依赖
'@web/core/l10n/translation'                        // 国际化
'@web/core/registry'                                // 注册表系统
'@web/core/utils/strings'                           // 字符串工具
'@web/views/fields/standard_field_props'            // 标准字段属性
'@web/core/utils/hooks'                             // Web 核心钩子
'@odoo/owl'                                         // OWL 框架
```

## 组件定义

### MailComposerTemplateSelector 类

```javascript
class MailComposerTemplateSelector extends Component {
    static template = "mail.MailComposerTemplateSelector";
    static components = { Dropdown, DropdownItem };
    static props = { ...standardFieldProps };
}
```

**组件特性**:
- 使用自定义模板"mail.MailComposerTemplateSelector"
- 集成Dropdown和DropdownItem组件
- 继承标准字段属性

## 组件设置

### setup() 方法

```javascript
setup() {
    this.action = useService("action");
    this.orm = useService("orm");
    this.state = useState({});
    this.limit = 7;

    onWillStart(() => {
        this.fetchTemplates();
    });
}
```

**初始化内容**:
- 动作服务用于执行操作
- ORM服务用于数据操作
- 响应式状态管理
- 模板显示限制（7个）
- 组件启动前获取模板数据

## 核心功能

### 1. 模板数据获取

```javascript
async fetchTemplates() {
    const domain = [["model", "=", this.props.record.data.render_model]];
    const fields = ["display_name"];
    this.state.templates = await this.orm.searchRead("mail.template", domain, fields, { limit: this.limit });
}
```

**获取逻辑**:
- 根据渲染模型过滤模板
- 只获取显示名称字段
- 限制返回数量为7个
- 将结果保存到状态中

### 2. 模板加载

```javascript
async onLoadTemplate(template) {
    await this.props.record.update({
        template_id: [template.id]
    });
}
```

**加载逻辑**:
- 更新记录的template_id字段
- 触发模板内容的自动填充

### 3. 模板删除

```javascript
async onDeleteTemplate(template) {
    this.env.services.dialog.add(ConfirmationDialog, {
        body: sprintf(_t('Are you sure you want to delete "%(template_name)s"?'), {
            template_name: template.display_name,
        }),
        confirmLabel: _t("Delete Template"),
        confirm: async () => {
            await this.orm.unlink("mail.template", [template.id]);
            this.state.templates = this.state.templates.filter(current => {
                return current.id !== template.id;
            });
        },
        cancel: () => {},
    });
}
```

**删除逻辑**:
- 显示确认对话框
- 确认后删除模板记录
- 从本地状态中移除已删除的模板

### 4. 模板覆盖

```javascript
async onOverwriteTemplate(template) {
    this.env.services.dialog.add(ConfirmationDialog, {
        body: sprintf(_t('Are your sure you want to update "%(template_name)s"?'), {
            template_name: template.display_name,
        }),
        confirmLabel: _t("Update Template"),
        confirm: async () => {
            await this.orm.write("mail.template", [template.id], {
                subject: this.props.record.data.subject,
                body_html: this.props.record.data.body,
            });
        },
        cancel: () => {},
    });
}
```

**覆盖逻辑**:
- 显示确认对话框
- 确认后用当前邮件内容更新模板
- 更新主题和HTML正文

### 5. 模板保存

```javascript
async onSaveTemplate() {
    await this.props.record.save();
    await this.action.doActionButton({
        type: "object",
        name: "open_template_creation_wizard",
        resId: this.props.record.resId,
        resModel: this.props.record.resModel
    });
}
```

**保存逻辑**:
- 先保存当前记录
- 打开模板创建向导
- 传递记录ID和模型信息

### 6. 模板搜索和选择

```javascript
onSelectTemplateSearchMoreBtnClick() {
    this.env.services.dialog.add(SelectCreateDialog, {
        resModel: "mail.template",
        title: _t("Insert Templates"),
        multiSelect: false,
        noCreate: true,
        domain: [["model", "=", this.props.record.data.render_model]],
        onSelected: async templateIds => {
            await this.props.record.update({
                template_id: templateIds
            });
        },
    });
}
```

**搜索选择逻辑**:
- 打开选择对话框
- 限制为单选模式
- 禁用创建功能
- 根据模型过滤模板
- 选择后更新模板ID

## 字段注册

### 字段配置

```javascript
const mailComposerTemplateSelector = {
    component: MailComposerTemplateSelector,
    fieldDependencies: [
        { name: "can_edit_body", type: "boolean" },
        { name: "render_model", type: "string" },
    ],
};

registry.category("fields").add("mail_composer_template_selector", mailComposerTemplateSelector);
```

**注册参数**:
- **组件**: MailComposerTemplateSelector
- **字段依赖**: can_edit_body（布尔型）、render_model（字符串型）
- **注册类别**: "fields"
- **注册名称**: "mail_composer_template_selector"

## 使用场景

### 1. 邮件编写表单集成

```javascript
// 邮件编写表单中的模板选择器
const MailComposerFormIntegration = {
    setupTemplateSelector: (formView) => {
        const selectorConfig = {
            name: 'template_selector',
            type: 'mail_composer_template_selector',
            string: '邮件模板',
            options: {
                show_quick_actions: true,
                allow_template_management: true,
                template_limit: 10
            }
        };

        return selectorConfig;
    },

    renderTemplateSelector: (record, options) => {
        return `
            <div class="mail-template-selector-container">
                <div class="selector-header">
                    <h4>邮件模板</h4>
                    <span class="template-count">
                        可用模板: ${record.availableTemplates?.length || 0}
                    </span>
                </div>

                <mail_composer_template_selector
                    name="template_id"
                    record="${record}"
                    options="${options}"
                />

                <div class="selector-footer">
                    <small class="text-muted">
                        选择模板将自动填充邮件内容
                    </small>
                </div>
            </div>
        `;
    }
};
```

### 2. 模板管理增强功能

```javascript
// 模板管理增强功能
const TemplateManagementEnhancer = {
    enhanceSelector: (selector) => {
        // 添加模板预览功能
        TemplateManagementEnhancer.addPreviewCapability(selector);

        // 添加模板分类功能
        TemplateManagementEnhancer.addCategoryFilter(selector);

        // 添加模板搜索功能
        TemplateManagementEnhancer.addSearchCapability(selector);

        // 添加模板统计功能
        TemplateManagementEnhancer.addUsageStatistics(selector);
    },

    addPreviewCapability: (selector) => {
        const previewContainer = document.createElement('div');
        previewContainer.className = 'template-preview-container';
        previewContainer.innerHTML = `
            <div class="preview-header">
                <h5>模板预览</h5>
                <button class="close-preview-btn">&times;</button>
            </div>
            <div class="preview-content">
                <div class="preview-subject"></div>
                <div class="preview-body"></div>
            </div>
        `;

        // 绑定预览事件
        selector.el.querySelectorAll('.template-item').forEach(item => {
            item.addEventListener('mouseenter', async (event) => {
                const templateId = event.target.dataset.templateId;
                await TemplateManagementEnhancer.showTemplatePreview(templateId, previewContainer);
            });
        });

        selector.el.appendChild(previewContainer);
    },

    showTemplatePreview: async (templateId, container) => {
        try {
            const template = await selector.orm.read('mail.template', [templateId], [
                'subject', 'body_html', 'description'
            ]);

            if (template.length > 0) {
                const templateData = template[0];
                container.querySelector('.preview-subject').textContent = templateData.subject || '无主题';
                container.querySelector('.preview-body').innerHTML = templateData.body_html || '无内容';
                container.style.display = 'block';
            }
        } catch (error) {
            console.error('加载模板预览失败:', error);
        }
    },

    addCategoryFilter: (selector) => {
        const filterContainer = document.createElement('div');
        filterContainer.className = 'template-category-filter';
        filterContainer.innerHTML = `
            <div class="filter-header">
                <label>模板分类:</label>
                <select class="category-select">
                    <option value="">所有分类</option>
                    <option value="sales">销售</option>
                    <option value="support">支持</option>
                    <option value="marketing">营销</option>
                    <option value="hr">人力资源</option>
                    <option value="finance">财务</option>
                </select>
            </div>
        `;

        const categorySelect = filterContainer.querySelector('.category-select');
        categorySelect.addEventListener('change', (event) => {
            const category = event.target.value;
            TemplateManagementEnhancer.filterTemplatesByCategory(selector, category);
        });

        selector.el.prepend(filterContainer);
    },

    filterTemplatesByCategory: async (selector, category) => {
        let domain = [["model", "=", selector.props.record.data.render_model]];

        if (category) {
            domain.push(["category", "=", category]);
        }

        try {
            const templates = await selector.orm.searchRead(
                "mail.template",
                domain,
                ["display_name", "category"],
                { limit: selector.limit }
            );

            selector.state.templates = templates;
        } catch (error) {
            console.error('过滤模板失败:', error);
        }
    },

    addSearchCapability: (selector) => {
        const searchContainer = document.createElement('div');
        searchContainer.className = 'template-search-container';
        searchContainer.innerHTML = `
            <div class="search-input-group">
                <input type="text" class="template-search-input" placeholder="搜索模板...">
                <button class="search-clear-btn" title="清除搜索">
                    <i class="fa fa-times"></i>
                </button>
            </div>
        `;

        const searchInput = searchContainer.querySelector('.template-search-input');
        const clearBtn = searchContainer.querySelector('.search-clear-btn');

        searchInput.addEventListener('input', debounce((event) => {
            const searchTerm = event.target.value;
            TemplateManagementEnhancer.searchTemplates(selector, searchTerm);
        }, 300));

        clearBtn.addEventListener('click', () => {
            searchInput.value = '';
            TemplateManagementEnhancer.searchTemplates(selector, '');
        });

        selector.el.prepend(searchContainer);
    },

    searchTemplates: async (selector, searchTerm) => {
        let domain = [["model", "=", selector.props.record.data.render_model]];

        if (searchTerm) {
            domain.push([
                "|",
                ["name", "ilike", searchTerm],
                ["subject", "ilike", searchTerm]
            ]);
        }

        try {
            const templates = await selector.orm.searchRead(
                "mail.template",
                domain,
                ["display_name"],
                { limit: selector.limit }
            );

            selector.state.templates = templates;
        } catch (error) {
            console.error('搜索模板失败:', error);
        }
    },

    addUsageStatistics: (selector) => {
        const statsContainer = document.createElement('div');
        statsContainer.className = 'template-usage-stats';
        statsContainer.innerHTML = `
            <div class="stats-header">
                <h5>使用统计</h5>
                <button class="refresh-stats-btn" title="刷新统计">
                    <i class="fa fa-refresh"></i>
                </button>
            </div>
            <div class="stats-content">
                <div class="stat-item">
                    <span class="stat-label">总模板数:</span>
                    <span class="stat-value" id="total-templates">-</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">本月使用:</span>
                    <span class="stat-value" id="monthly-usage">-</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">最常用:</span>
                    <span class="stat-value" id="most-used">-</span>
                </div>
            </div>
        `;

        const refreshBtn = statsContainer.querySelector('.refresh-stats-btn');
        refreshBtn.addEventListener('click', () => {
            TemplateManagementEnhancer.refreshUsageStats(selector, statsContainer);
        });

        // 初始加载统计
        TemplateManagementEnhancer.refreshUsageStats(selector, statsContainer);

        selector.el.appendChild(statsContainer);
    },

    refreshUsageStats: async (selector, container) => {
        try {
            const stats = await selector.orm.call('mail.template', 'get_usage_statistics', [], {
                model: selector.props.record.data.render_model
            });

            container.querySelector('#total-templates').textContent = stats.total || 0;
            container.querySelector('#monthly-usage').textContent = stats.monthly_usage || 0;
            container.querySelector('#most-used').textContent = stats.most_used || '无';
        } catch (error) {
            console.error('获取使用统计失败:', error);
        }
    }
};
```
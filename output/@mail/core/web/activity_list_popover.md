# Activity List Popover - 活动列表弹出框

## 概述

`activity_list_popover.js` 实现了 Odoo 邮件系统中的活动列表弹出框组件，用于在弹出框中显示和管理记录的活动列表。该组件支持按状态分类显示活动、添加新活动、批量操作等功能，提供了完整的活动管理界面，是活动按钮点击后显示的主要交互界面，为用户提供了便捷的活动查看和操作体验。

## 文件信息
- **路径**: `/mail/static/src/core/web/activity_list_popover.js`
- **行数**: 91
- **模块**: `@mail/core/web/activity_list_popover`

## 依赖关系

```javascript
// UI组件依赖
'@mail/core/web/activity_list_popover_item'  // 活动列表项组件

// 工具和钩子依赖
'@mail/utils/common/misc'                    // 通用工具函数
'@odoo/owl'                                 // OWL 框架
'@web/core/utils/hooks'                     // Web 核心钩子
```

## 组件定义

### ActivityListPopover 类

```javascript
class ActivityListPopover extends Component {
    static components = { ActivityListPopoverItem };
    static props = [
        "activityIds",              // 活动ID列表
        "close",                    // 关闭回调
        "defaultActivityTypeId?",   // 默认活动类型ID（可选）
        "onActivityChanged",        // 活动变化回调
        "resId",                   // 资源ID
        "resIds?",                 // 批量资源ID列表（可选）
        "resModel",                // 资源模型
    ];
    static template = "mail.ActivityListPopover";
}
```

## Props 配置

### Props 详细说明

- **`activityIds`** (必需):
  - 类型: 数字数组
  - 用途: 要显示的活动ID列表
  - 功能: 确定弹出框中显示哪些活动

- **`close`** (必需):
  - 类型: 函数
  - 用途: 关闭弹出框的回调函数
  - 功能: 用户操作完成后关闭弹出框

- **`defaultActivityTypeId`** (可选):
  - 类型: 数字
  - 用途: 创建新活动时的默认活动类型
  - 功能: 简化新活动创建流程

- **`onActivityChanged`** (必需):
  - 类型: 函数
  - 用途: 活动发生变化时的回调
  - 功能: 通知父组件更新相关数据

- **`resId`** (必需):
  - 类型: 数字
  - 用途: 主要资源记录的ID
  - 功能: 标识活动关联的记录

- **`resIds`** (可选):
  - 类型: 数字数组
  - 用途: 批量操作时的资源ID列表
  - 功能: 支持批量创建活动

- **`resModel`** (必需):
  - 类型: 字符串
  - 用途: 资源记录的模型名称
  - 功能: 确定活动关联的模型类型

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.orm = useService("orm");
    this.store = useState(useService("mail.store"));
    this.updateFromProps(this.props);
    onWillUpdateProps((props) => this.updateFromProps(props));
}
```

**初始化内容**:
- ORM服务用于数据操作
- 邮件存储的响应式状态
- 从Props更新数据
- 监听Props变化

## 核心功能

### 1. 活动列表获取

```javascript
get activities() {
    const allActivities = Object.values(this.store.Activity.records);
    return allActivities
        .filter((activity) => this.props.activityIds.includes(activity.id))
        .sort((a, b) => compareDatetime(a.date_deadline, b.date_deadline) || a.id - b.id);
}
```

**获取逻辑**:
- 从存储中获取所有活动
- 过滤出指定ID的活动
- 按截止日期和ID排序

### 2. 按状态分类

```javascript
get doneActivities() {
    return this.activities.filter((activity) => activity.state === "done");
}

get overdueActivities() {
    return this.activities.filter((activity) => activity.state === "overdue");
}

get plannedActivities() {
    return this.activities.filter((activity) => activity.state === "planned");
}

get todayActivities() {
    return this.activities.filter((activity) => activity.state === "today");
}
```

**状态分类**:
- **已完成**: 状态为"done"的活动
- **逾期**: 状态为"overdue"的活动
- **已计划**: 状态为"planned"的活动
- **今日**: 状态为"today"的活动

### 3. 添加活动

```javascript
onClickAddActivityButton() {
    this.store
        .scheduleActivity(
            this.props.resModel,
            this.props.resIds ? this.props.resIds : [this.props.resId],
            this.props.defaultActivityTypeId
        )
        .then(() => this.props.onActivityChanged());
    this.props.close();
}
```

**添加流程**:
1. 调用存储服务的计划活动方法
2. 支持单个或批量资源
3. 使用默认活动类型（如果提供）
4. 活动创建后触发变化回调
5. 关闭弹出框

### 4. 数据更新

```javascript
async updateFromProps(props) {
    const data = await this.orm.silent.call("mail.activity", "activity_format", [
        props.activityIds,
    ]);
    this.store.insert(data, { html: true });
}
```

**更新逻辑**:
- 调用服务器获取活动格式化数据
- 使用静默调用避免加载指示器
- 将数据插入到存储中
- 支持HTML内容

## 使用场景

### 1. 单记录活动管理

```javascript
// 为单个记录显示活动列表
const SingleRecordActivityPopover = ({ record, activityIds }) => {
    const handleActivityChanged = () => {
        // 重新加载记录数据
        record.load();
        
        // 更新活动计数
        updateActivityCount(record);
    };
    
    return (
        <ActivityListPopover
            activityIds={activityIds}
            resId={record.resId}
            resModel={record.resModel}
            onActivityChanged={handleActivityChanged}
            close={() => closePopover()}
        />
    );
};
```

### 2. 批量活动管理

```javascript
// 为多个选中记录批量管理活动
const BatchActivityPopover = ({ selectedRecords, activityIds }) => {
    const resIds = selectedRecords.map(r => r.resId);
    const resModel = selectedRecords[0].resModel;
    
    const handleBatchActivityChanged = () => {
        // 重新加载所有选中记录
        selectedRecords.forEach(record => record.load());
        
        // 更新批量操作状态
        updateBatchOperationStatus();
    };
    
    return (
        <ActivityListPopover
            activityIds={activityIds}
            resId={resIds[0]}
            resIds={resIds}
            resModel={resModel}
            onActivityChanged={handleBatchActivityChanged}
            close={() => closeBatchPopover()}
        />
    );
};
```

### 3. 带默认类型的活动创建

```javascript
// 指定默认活动类型的弹出框
const TypedActivityPopover = ({ record, defaultTypeId }) => {
    return (
        <ActivityListPopover
            activityIds={record.activityIds}
            resId={record.resId}
            resModel={record.resModel}
            defaultActivityTypeId={defaultTypeId}
            onActivityChanged={() => refreshRecord(record)}
            close={() => hidePopover()}
        />
    );
};
```

### 4. 嵌入式活动管理

```javascript
// 嵌入到其他界面中的活动管理
const EmbeddedActivityManager = ({ context }) => {
    const [showPopover, setShowPopover] = useState(false);
    const [popoverTarget, setPopoverTarget] = useState(null);
    
    const openActivityPopover = (event, record) => {
        setPopoverTarget(event.currentTarget);
        setShowPopover(true);
    };
    
    return (
        <div class="embedded-activity-manager">
            <button onClick={(e) => openActivityPopover(e, context.record)}>
                管理活动
            </button>
            
            {showPopover && (
                <Popover target={popoverTarget} onClose={() => setShowPopover(false)}>
                    <ActivityListPopover
                        activityIds={context.record.activityIds}
                        resId={context.record.resId}
                        resModel={context.record.resModel}
                        onActivityChanged={() => context.refresh()}
                        close={() => setShowPopover(false)}
                    />
                </Popover>
            )}
        </div>
    );
};
```

## 状态管理

### 1. 活动状态过滤

```javascript
// 活动状态过滤器
const ActivityStateFilter = {
    all: (activities) => activities,
    overdue: (activities) => activities.filter(a => a.state === 'overdue'),
    today: (activities) => activities.filter(a => a.state === 'today'),
    planned: (activities) => activities.filter(a => a.state === 'planned'),
    done: (activities) => activities.filter(a => a.state === 'done')
};

// 获取特定状态的活动
const getActivitiesByState = (activities, state) => {
    const filter = ActivityStateFilter[state] || ActivityStateFilter.all;
    return filter(activities);
};
```

### 2. 活动排序

```javascript
// 活动排序策略
const ActivitySorting = {
    byDeadline: (a, b) => compareDatetime(a.date_deadline, b.date_deadline),
    byPriority: (a, b) => (b.priority || 0) - (a.priority || 0),
    byType: (a, b) => a.activity_type_id[1].localeCompare(b.activity_type_id[1]),
    byId: (a, b) => a.id - b.id
};

// 组合排序
const sortActivities = (activities, sortBy = ['byDeadline', 'byId']) => {
    return activities.sort((a, b) => {
        for (const sortMethod of sortBy) {
            const result = ActivitySorting[sortMethod](a, b);
            if (result !== 0) return result;
        }
        return 0;
    });
};
```

### 3. 活动分组

```javascript
// 活动分组显示
const groupActivitiesByState = (activities) => {
    const groups = {
        overdue: [],
        today: [],
        planned: [],
        done: []
    };
    
    activities.forEach(activity => {
        if (groups[activity.state]) {
            groups[activity.state].push(activity);
        }
    });
    
    return groups;
};

// 分组统计
const getActivityStatistics = (activities) => {
    const groups = groupActivitiesByState(activities);
    
    return {
        total: activities.length,
        overdue: groups.overdue.length,
        today: groups.today.length,
        planned: groups.planned.length,
        done: groups.done.length,
        pending: groups.overdue.length + groups.today.length + groups.planned.length
    };
};
```

## 数据同步

### 1. 服务器数据获取

```javascript
// 获取活动格式化数据
const fetchActivityData = async (orm, activityIds) => {
    try {
        const data = await orm.silent.call("mail.activity", "activity_format", [
            activityIds
        ]);
        
        return data;
    } catch (error) {
        console.error('获取活动数据失败:', error);
        throw error;
    }
};

// 批量获取活动数据
const fetchBatchActivityData = async (orm, recordIds, resModel) => {
    try {
        const data = await orm.silent.call(resModel, "get_activities", [recordIds]);
        return data;
    } catch (error) {
        console.error('批量获取活动数据失败:', error);
        return [];
    }
};
```

### 2. 本地存储同步

```javascript
// 同步活动数据到本地存储
const syncActivityData = (store, activityData) => {
    // 插入活动数据
    store.insert(activityData, { html: true });
    
    // 更新相关计数器
    updateActivityCounters(store);
    
    // 触发相关事件
    store.env.bus.trigger('activities:updated', activityData);
};

// 更新活动计数器
const updateActivityCounters = (store) => {
    const activities = Object.values(store.Activity.records);
    
    store.activityCounter = activities.filter(a => a.state !== 'done').length;
    store.overdueActivityCounter = activities.filter(a => a.state === 'overdue').length;
    store.todayActivityCounter = activities.filter(a => a.state === 'today').length;
};
```

### 3. 实时更新

```javascript
// 监听活动变化
const setupActivityChangeListener = (store, onActivityChanged) => {
    const handleActivityUpdate = (event) => {
        const { activityId, action } = event.detail;
        
        switch (action) {
            case 'created':
                refreshActivityData(activityId);
                break;
            case 'updated':
                refreshActivityData(activityId);
                break;
            case 'deleted':
                removeActivityFromStore(activityId);
                break;
        }
        
        onActivityChanged();
    };
    
    store.env.bus.addEventListener('activity:changed', handleActivityUpdate);
    
    return () => {
        store.env.bus.removeEventListener('activity:changed', handleActivityUpdate);
    };
};
```

## 性能优化

### 1. 数据缓存

```javascript
// 活动数据缓存
const activityDataCache = new Map();

const getCachedActivityData = (activityIds) => {
    const cacheKey = activityIds.sort().join(',');
    
    if (activityDataCache.has(cacheKey)) {
        const cached = activityDataCache.get(cacheKey);
        if (Date.now() - cached.timestamp < 30000) { // 30秒缓存
            return cached.data;
        }
    }
    
    return null;
};

const setCachedActivityData = (activityIds, data) => {
    const cacheKey = activityIds.sort().join(',');
    activityDataCache.set(cacheKey, {
        data,
        timestamp: Date.now()
    });
};
```

### 2. 虚拟化渲染

```javascript
// 大量活动时的虚拟化渲染
const VirtualizedActivityList = ({ activities, itemHeight = 60 }) => {
    const [visibleRange, setVisibleRange] = useState({ start: 0, end: 20 });
    const containerRef = useRef();
    
    const visibleActivities = activities.slice(visibleRange.start, visibleRange.end);
    
    const handleScroll = (event) => {
        const scrollTop = event.target.scrollTop;
        const start = Math.floor(scrollTop / itemHeight);
        const end = Math.min(start + 20, activities.length);
        
        setVisibleRange({ start, end });
    };
    
    return (
        <div 
            ref={containerRef}
            class="virtualized-activity-list"
            onScroll={handleScroll}
            style={{ height: '400px', overflow: 'auto' }}
        >
            <div style={{ height: activities.length * itemHeight }}>
                <div style={{ transform: `translateY(${visibleRange.start * itemHeight}px)` }}>
                    {visibleActivities.map(activity => (
                        <ActivityListPopoverItem 
                            key={activity.id} 
                            activity={activity}
                            style={{ height: itemHeight }}
                        />
                    ))}
                </div>
            </div>
        </div>
    );
};
```

## 可访问性

### 1. 键盘导航

```javascript
// 键盘导航支持
const handleKeyDown = (event, popover) => {
    switch (event.key) {
        case 'Escape':
            event.preventDefault();
            popover.close();
            break;
        case 'Tab':
            // 让浏览器处理Tab导航
            break;
        case 'Enter':
            if (event.target.classList.contains('add-activity-btn')) {
                event.preventDefault();
                popover.onClickAddActivityButton();
            }
            break;
    }
};
```

### 2. ARIA 标签

```javascript
// 可访问性属性
const getAccessibilityProps = (activities) => {
    const stats = getActivityStatistics(activities);
    
    return {
        'role': 'dialog',
        'aria-label': '活动列表',
        'aria-describedby': 'activity-list-description',
        'aria-live': 'polite',
        'aria-atomic': 'false'
    };
};
```

## 设计模式

### 1. 组合模式 (Composition Pattern)
- 组合活动列表项组件
- 灵活的组件组合

### 2. 策略模式 (Strategy Pattern)
- 不同状态的不同显示策略
- 可配置的排序策略

### 3. 观察者模式 (Observer Pattern)
- 监听Props变化
- 响应活动状态更新

## 注意事项

1. **数据同步**: 确保活动数据与服务器同步
2. **性能考虑**: 大量活动时的渲染性能
3. **用户体验**: 提供清晰的状态指示
4. **错误处理**: 处理数据获取失败的情况

## 扩展建议

1. **搜索功能**: 添加活动搜索和过滤
2. **批量操作**: 支持批量标记完成和删除
3. **自定义视图**: 支持用户自定义活动显示
4. **导出功能**: 支持活动数据导出
5. **移动端优化**: 优化移动设备上的交互体验

该组件为活动管理提供了完整的弹出框界面，支持多种活动操作和状态管理，是活动系统的重要用户界面组件。

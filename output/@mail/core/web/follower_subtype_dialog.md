# Follower Subtype Dialog - 关注者子类型对话框

## 概述

`follower_subtype_dialog.js` 实现了 Odoo 邮件系统中的关注者子类型对话框组件，用于编辑关注者的订阅偏好设置。该组件允许用户选择关注者接收哪些类型的通知，支持动态加载订阅数据、修改子类型选择、应用订阅偏好等功能，提供了完整的关注者通知管理界面，是关注者权限管理的重要组件。

## 文件信息
- **路径**: `/mail/static/src/core/web/follower_subtype_dialog.js`
- **行数**: 89
- **模块**: `@mail/core/web/follower_subtype_dialog`

## 依赖关系

```javascript
// UI组件依赖
'@web/core/dialog/dialog'      // 对话框组件

// 核心依赖
'@web/core/network/rpc'        // RPC网络服务
'@odoo/owl'                   // OWL 框架
'@web/core/l10n/translation'  // 国际化
'@web/core/utils/hooks'       // Web 核心钩子
```

## 数据类型定义

### SubtypeData 接口

```javascript
/**
 * @typedef {Object} SubtypeData
 * @property {boolean} followed - 是否已关注此子类型
 * @property {number} id - 子类型ID
 * @property {string} name - 子类型名称
 */
```

## 组件定义

### FollowerSubtypeDialog 类

```javascript
class FollowerSubtypeDialog extends Component {
    static components = { Dialog };
    static props = [
        "close",              // 关闭对话框回调
        "follower",           // 关注者对象
        "onFollowerChanged",  // 关注者变化回调
    ];
    static template = "mail.FollowerSubtypeDialog";
}
```

## Props 配置

### Props 详细说明

- **`close`** (必需):
  - 类型: 函数
  - 用途: 关闭对话框的回调函数
  - 功能: 用户操作完成后关闭对话框

- **`follower`** (必需):
  - 类型: Follower 模型实例
  - 用途: 要编辑订阅偏好的关注者对象
  - 功能: 提供关注者的基本信息和操作方法

- **`onFollowerChanged`** (必需):
  - 类型: 函数
  - 用途: 关注者发生变化时的回调
  - 参数: thread (线程对象)

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.store = useState(useService("mail.store"));
    this.state = useState({
        /** @type {SubtypeData[]} */
        subtypes: [],
    });
    onWillStart(async () => {
        this.state.subtypes = await rpc("/mail/read_subscription_data", {
            follower_id: this.props.follower.id,
        });
    });
}
```

**初始化内容**:
- 邮件存储服务的响应式状态
- 子类型数据的本地状态
- 组件启动前加载订阅数据

## 核心功能

### 1. 订阅数据加载

```javascript
onWillStart(async () => {
    this.state.subtypes = await rpc("/mail/read_subscription_data", {
        follower_id: this.props.follower.id,
    });
});
```

**数据加载**:
- 通过RPC调用获取关注者的订阅数据
- 包含所有可用的子类型及其关注状态
- 在组件启动前完成数据加载

### 2. 复选框状态变化

```javascript
onChangeCheckbox(ev, subtype) {
    subtype.followed = ev.target.checked;
}
```

**状态更新**:
- 根据复选框的选中状态更新子类型的关注状态
- 实时更新本地状态

### 3. 应用订阅偏好

```javascript
async onClickApply() {
    const selectedSubtypes = this.state.subtypes.filter((s) => s.followed);
    const thread = this.props.follower.thread;
    
    if (selectedSubtypes.length === 0) {
        await this.props.follower.remove();
    } else {
        await this.env.services.orm.call(
            this.props.follower.thread.model,
            "message_subscribe",
            [[this.props.follower.thread.id]],
            {
                partner_ids: [this.props.follower.partner.id],
                subtype_ids: selectedSubtypes.map((subtype) => subtype.id),
            }
        );
        
        if (!selectedSubtypes.some((subtype) => subtype.id === this.store.mt_comment_id)) {
            this.props.follower.removeRecipient();
        }
        
        this.env.services.notification.add(
            _t("The subscription preferences were successfully applied."),
            { type: "success" }
        );
    }
    
    this.props.onFollowerChanged(thread);
    this.props.close();
}
```

**应用流程**:
1. 获取选中的子类型
2. 如果没有选中任何子类型，移除关注者
3. 否则调用消息订阅方法更新订阅偏好
4. 如果未选中评论子类型，移除收件人状态
5. 显示成功通知
6. 触发关注者变化回调
7. 关闭对话框

### 4. 对话框标题

```javascript
get title() {
    return _t("Edit Subscription of %(name)s", { name: this.props.follower.partner.name });
}
```

**标题生成**:
- 动态生成包含关注者姓名的对话框标题
- 支持国际化

## 使用场景

### 1. 基本订阅偏好编辑

```javascript
// 基本的订阅偏好编辑
const BasicSubscriptionEditor = ({ follower, onUpdate }) => {
    const [showDialog, setShowDialog] = useState(false);
    
    const handleFollowerChanged = (thread) => {
        // 更新关注者数据
        refreshFollowerData(follower);
        
        // 通知父组件
        onUpdate(follower);
        
        // 更新线程数据
        thread.refresh();
    };
    
    const openDialog = () => {
        setShowDialog(true);
    };
    
    const closeDialog = () => {
        setShowDialog(false);
    };
    
    return (
        <div class="subscription-editor">
            <button onClick={openDialog}>
                编辑订阅偏好
            </button>
            
            {showDialog && (
                <FollowerSubtypeDialog
                    follower={follower}
                    onFollowerChanged={handleFollowerChanged}
                    close={closeDialog}
                />
            )}
        </div>
    );
};
```

### 2. 批量订阅管理

```javascript
// 批量订阅管理
const BatchSubscriptionManager = ({ followers }) => {
    const [currentFollower, setCurrentFollower] = useState(null);
    const [showDialog, setShowDialog] = useState(false);
    
    const handleEditSubscription = (follower) => {
        setCurrentFollower(follower);
        setShowDialog(true);
    };
    
    const handleFollowerChanged = (thread) => {
        // 更新当前关注者
        refreshFollowerData(currentFollower);
        
        // 更新批量列表
        updateFollowersList();
        
        // 记录操作
        logSubscriptionChange(currentFollower);
    };
    
    const closeDialog = () => {
        setShowDialog(false);
        setCurrentFollower(null);
    };
    
    return (
        <div class="batch-subscription-manager">
            <div class="followers-list">
                {followers.map(follower => (
                    <div key={follower.id} class="follower-item">
                        <span>{follower.partner.name}</span>
                        <button onClick={() => handleEditSubscription(follower)}>
                            编辑订阅
                        </button>
                    </div>
                ))}
            </div>
            
            {showDialog && currentFollower && (
                <FollowerSubtypeDialog
                    follower={currentFollower}
                    onFollowerChanged={handleFollowerChanged}
                    close={closeDialog}
                />
            )}
        </div>
    );
};
```

### 3. 项目关注者管理

```javascript
// 项目关注者订阅管理
const ProjectFollowerManager = ({ project, follower }) => {
    const handleProjectFollowerChanged = (thread) => {
        // 更新项目关注者统计
        updateProjectFollowerStats(project);
        
        // 检查项目通知设置
        checkProjectNotificationSettings(project);
        
        // 通知项目团队
        notifyProjectTeam(project, follower, 'subscription_changed');
        
        // 更新项目活动
        updateProjectActivity(project);
    };
    
    return (
        <div class="project-follower-manager">
            <div class="project-info">
                <h3>{project.name}</h3>
                <span>关注者: {follower.partner.name}</span>
            </div>
            
            <FollowerSubtypeDialog
                follower={follower}
                onFollowerChanged={handleProjectFollowerChanged}
                close={() => closeProjectDialog()}
            />
        </div>
    );
};
```

### 4. 销售机会关注者管理

```javascript
// 销售机会关注者订阅管理
const OpportunityFollowerManager = ({ opportunity, follower }) => {
    const handleOpportunityFollowerChanged = (thread) => {
        // 更新销售机会数据
        refreshOpportunityData(opportunity);
        
        // 更新销售团队通知
        updateSalesTeamNotifications(opportunity);
        
        // 记录销售活动
        logSalesActivity(opportunity, 'follower_subscription_changed');
        
        // 检查销售流程
        checkSalesProcess(opportunity);
    };
    
    return (
        <div class="opportunity-follower-manager">
            <div class="opportunity-header">
                <h4>{opportunity.name}</h4>
                <span class="stage">{opportunity.stage_id.name}</span>
            </div>
            
            <div class="follower-info">
                <span>编辑 {follower.partner.name} 的订阅偏好</span>
            </div>
            
            <FollowerSubtypeDialog
                follower={follower}
                onFollowerChanged={handleOpportunityFollowerChanged}
                close={() => closeSalesDialog()}
            />
        </div>
    );
};
```

## 订阅数据处理

### 1. 子类型数据结构

```javascript
// 子类型数据结构
const SubtypeDataStructure = {
    // 基本子类型
    comment: {
        id: 1,
        name: '评论',
        followed: true,
        description: '接收所有评论通知'
    },
    
    // 活动子类型
    activities: {
        id: 2,
        name: '活动',
        followed: false,
        description: '接收活动相关通知'
    },
    
    // 状态变更子类型
    stage_change: {
        id: 3,
        name: '状态变更',
        followed: true,
        description: '接收状态变更通知'
    }
};
```

### 2. 订阅数据验证

```javascript
// 订阅数据验证
const validateSubscriptionData = (subtypes) => {
    const errors = [];
    
    if (!Array.isArray(subtypes)) {
        errors.push('子类型数据必须是数组');
        return { isValid: false, errors };
    }
    
    subtypes.forEach((subtype, index) => {
        if (!subtype.id) {
            errors.push(`子类型 ${index} 缺少ID`);
        }
        
        if (!subtype.name) {
            errors.push(`子类型 ${index} 缺少名称`);
        }
        
        if (typeof subtype.followed !== 'boolean') {
            errors.push(`子类型 ${index} 的关注状态必须是布尔值`);
        }
    });
    
    return {
        isValid: errors.length === 0,
        errors
    };
};
```

### 3. 订阅偏好处理

```javascript
// 订阅偏好处理
const processSubscriptionPreferences = (subtypes, follower) => {
    const selectedSubtypes = subtypes.filter(s => s.followed);
    
    // 检查是否有必需的子类型
    const hasRequiredSubtypes = selectedSubtypes.some(s => 
        s.id === getCommentSubtypeId()
    );
    
    // 构建订阅参数
    const subscriptionParams = {
        partner_ids: [follower.partner.id],
        subtype_ids: selectedSubtypes.map(s => s.id)
    };
    
    // 添加额外的上下文
    const context = {
        mail_post_autofollow: true,
        mail_create_nosubscribe: false
    };
    
    return {
        params: subscriptionParams,
        context,
        hasRequiredSubtypes,
        selectedCount: selectedSubtypes.length
    };
};
```

## 通知管理

### 1. 成功通知

```javascript
// 成功通知处理
const showSuccessNotification = (follower, selectedSubtypes) => {
    const message = selectedSubtypes.length > 0
        ? _t("订阅偏好已成功应用给 %(name)s", { name: follower.partner.name })
        : _t("%(name)s 已被移除关注", { name: follower.partner.name });
    
    notificationService.add(message, {
        type: "success",
        sticky: false,
        timeout: 3000
    });
};
```

### 2. 错误通知

```javascript
// 错误通知处理
const showErrorNotification = (error, follower) => {
    let message = _t("更新订阅偏好失败");
    
    if (error.message.includes('access')) {
        message = _t("没有权限修改 %(name)s 的订阅偏好", { 
            name: follower.partner.name 
        });
    } else if (error.message.includes('network')) {
        message = _t("网络错误，请重试");
    }
    
    notificationService.add(message, {
        type: "danger",
        sticky: true
    });
};
```

## 性能优化

### 1. 数据缓存

```javascript
// 订阅数据缓存
const subscriptionDataCache = new Map();

const getCachedSubscriptionData = (followerId) => {
    if (subscriptionDataCache.has(followerId)) {
        const cached = subscriptionDataCache.get(followerId);
        if (Date.now() - cached.timestamp < 300000) { // 5分钟缓存
            return cached.data;
        }
    }
    return null;
};

const setCachedSubscriptionData = (followerId, data) => {
    subscriptionDataCache.set(followerId, {
        data,
        timestamp: Date.now()
    });
};
```

### 2. 批量更新优化

```javascript
// 批量订阅更新
const batchUpdateSubscriptions = async (updates) => {
    const promises = updates.map(update => 
        orm.call(
            update.thread.model,
            "message_subscribe",
            [[update.thread.id]],
            {
                partner_ids: [update.follower.partner.id],
                subtype_ids: update.subtypeIds
            }
        )
    );
    
    return Promise.all(promises);
};
```

## 错误处理

### 1. RPC调用错误

```javascript
// 安全的RPC调用
const safeRpcCall = async (route, params) => {
    try {
        return await rpc(route, params);
    } catch (error) {
        console.error('RPC调用失败:', error);
        
        if (error.code === 403) {
            throw new Error('没有权限访问订阅数据');
        } else if (error.code === 404) {
            throw new Error('关注者不存在');
        } else {
            throw new Error('获取订阅数据失败');
        }
    }
};
```

### 2. 订阅更新错误

```javascript
// 安全的订阅更新
const safeUpdateSubscription = async (thread, params) => {
    try {
        return await orm.call(
            thread.model,
            "message_subscribe",
            [[thread.id]],
            params
        );
    } catch (error) {
        console.error('更新订阅失败:', error);
        
        if (error.message.includes('access')) {
            throw new Error('没有权限修改订阅偏好');
        } else {
            throw new Error('更新订阅偏好失败');
        }
    }
};
```

## 设计模式

### 1. 对话框模式 (Dialog Pattern)
- 模态对话框的标准实现
- 用户交互的隔离环境

### 2. 观察者模式 (Observer Pattern)
- 监听订阅状态变化
- 响应用户操作

### 3. 策略模式 (Strategy Pattern)
- 不同子类型的不同处理策略
- 可配置的订阅行为

## 注意事项

1. **数据一致性**: 确保订阅数据与服务器同步
2. **权限验证**: 验证用户是否有权限修改订阅
3. **用户体验**: 提供清晰的操作指引和反馈
4. **错误处理**: 处理网络错误和权限错误

## 扩展建议

1. **批量编辑**: 支持批量编辑多个关注者的订阅偏好
2. **模板管理**: 支持订阅偏好模板
3. **高级过滤**: 支持基于条件的智能订阅
4. **通知预览**: 预览订阅偏好的通知效果
5. **移动端优化**: 优化移动设备上的对话框体验

该组件为关注者订阅管理提供了完整的用户界面，是关注者权限管理的重要工具。

# Mail Core Common Service Patch - 邮件核心通用服务补丁

## 概述

`mail_core_common_service_patch.js` 实现了 Odoo 邮件系统中邮件核心通用服务的Web环境补丁，用于扩展星标消息的通知处理功能。该补丁重写了星标切换通知的处理方法，增加了对星标邮箱计数器和消息集合的管理，确保星标状态变化时能正确更新星标邮箱的状态，为Web环境下的星标功能提供了完整的数据同步机制。

## 文件信息
- **路径**: `/mail/static/src/core/web/mail_core_common_service_patch.js`
- **行数**: 35
- **模块**: `@mail/core/web/mail_core_common_service_patch`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/patch'                        // 补丁工具
'@mail/core/common/mail_core_common_service'   // 邮件核心通用服务
```

## 补丁实现

### MailCoreCommon 原型补丁

```javascript
patch(MailCoreCommon.prototype, {
    _handleNotificationToggleStar(payload, metadata) {
        super._handleNotificationToggleStar(payload, metadata);
        const { id: notifId } = metadata;
        const { message_ids: messageIds, starred } = payload;
        // 星标处理逻辑...
    },
});
```

## 核心功能

### 1. 星标切换通知处理

```javascript
_handleNotificationToggleStar(payload, metadata) {
    super._handleNotificationToggleStar(payload, metadata);
    const { id: notifId } = metadata;
    const { message_ids: messageIds, starred } = payload;
    
    for (const id of messageIds) {
        const message = this.store.Message.get({ id });
        const starredBox = this.store.starred;
        
        if (starred) {
            if (notifId > starredBox.counter_bus_id) {
                starredBox.counter++;
            }
            starredBox.messages.add(message);
        } else {
            if (notifId > starredBox.counter_bus_id) {
                starredBox.counter--;
            }
            starredBox.messages.delete(message);
        }
    }
}
```

**处理逻辑**:
1. 调用父类的处理方法
2. 提取通知ID和消息数据
3. 遍历所有受影响的消息
4. 根据星标状态更新星标邮箱
5. 管理计数器和消息集合

### 2. 星标状态管理

**添加星标时**:
- 检查通知ID是否大于星标邮箱的计数器总线ID
- 增加星标邮箱计数器
- 将消息添加到星标邮箱的消息集合

**移除星标时**:
- 检查通知ID是否大于星标邮箱的计数器总线ID
- 减少星标邮箱计数器
- 从星标邮箱的消息集合中删除消息

## 使用场景

### 1. 星标消息管理系统

```javascript
// 星标消息管理系统
const StarredMessageManager = {
    toggleStar: async (messageId, starred) => {
        try {
            // 发送星标切换请求
            const response = await fetch('/web/dataset/call_kw/mail.message/toggle_star', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    model: 'mail.message',
                    method: 'toggle_star',
                    args: [messageId, starred],
                    kwargs: {}
                })
            });
            
            const result = await response.json();
            
            if (result.error) {
                throw new Error(result.error.message);
            }
            
            // 更新本地状态
            StarredMessageManager.updateLocalState(messageId, starred);
            
            // 记录操作
            StarredMessageManager.logStarAction(messageId, starred);
            
            return result.result;
        } catch (error) {
            console.error('星标切换失败:', error);
            throw error;
        }
    },
    
    updateLocalState: (messageId, starred) => {
        const message = store.Message.get({ id: messageId });
        const starredBox = store.starred;
        
        if (message) {
            message.starred = starred;
            
            if (starred) {
                starredBox.messages.add(message);
                starredBox.counter++;
            } else {
                starredBox.messages.delete(message);
                starredBox.counter--;
            }
            
            // 触发状态更新事件
            StarredMessageManager.notifyStateChange(messageId, starred);
        }
    },
    
    batchToggleStar: async (messageIds, starred) => {
        try {
            const response = await fetch('/web/dataset/call_kw/mail.message/batch_toggle_star', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    model: 'mail.message',
                    method: 'batch_toggle_star',
                    args: [messageIds, starred],
                    kwargs: {}
                })
            });
            
            const result = await response.json();
            
            if (result.error) {
                throw new Error(result.error.message);
            }
            
            // 批量更新本地状态
            messageIds.forEach(messageId => {
                StarredMessageManager.updateLocalState(messageId, starred);
            });
            
            showNotification(
                `已${starred ? '添加' : '移除'} ${messageIds.length} 个消息的星标`,
                'success'
            );
            
            return result.result;
        } catch (error) {
            console.error('批量星标切换失败:', error);
            showNotification('批量操作失败，请重试', 'error');
            throw error;
        }
    },
    
    getStarredMessages: (filters = {}) => {
        const starredBox = store.starred;
        let messages = Array.from(starredBox.messages);
        
        // 应用过滤器
        if (filters.author) {
            messages = messages.filter(msg => 
                msg.author.name.toLowerCase().includes(filters.author.toLowerCase())
            );
        }
        
        if (filters.dateFrom) {
            messages = messages.filter(msg => 
                new Date(msg.date) >= new Date(filters.dateFrom)
            );
        }
        
        if (filters.dateTo) {
            messages = messages.filter(msg => 
                new Date(msg.date) <= new Date(filters.dateTo)
            );
        }
        
        if (filters.hasAttachment) {
            messages = messages.filter(msg => 
                msg.attachments && msg.attachments.length > 0
            );
        }
        
        // 按日期排序
        return messages.sort((a, b) => new Date(b.date) - new Date(a.date));
    },
    
    logStarAction: (messageId, starred) => {
        const action = {
            type: 'star_toggle',
            messageId,
            starred,
            timestamp: Date.now(),
            userId: getCurrentUser().id
        };
        
        // 保存到本地存储
        const actionHistory = JSON.parse(localStorage.getItem('star_action_history') || '[]');
        actionHistory.unshift(action);
        
        // 限制历史记录数量
        if (actionHistory.length > 1000) {
            actionHistory.pop();
        }
        
        localStorage.setItem('star_action_history', JSON.stringify(actionHistory));
        
        // 发送到分析服务
        sendAnalytics('star_action', action);
    },
    
    notifyStateChange: (messageId, starred) => {
        const event = new CustomEvent('starred_message_changed', {
            detail: {
                messageId,
                starred,
                timestamp: Date.now()
            }
        });
        
        document.dispatchEvent(event);
    }
};
```

### 2. 星标邮箱同步管理

```javascript
// 星标邮箱同步管理
const StarredBoxSyncManager = {
    syncStarredBox: () => {
        const starredBox = store.starred;
        
        // 验证计数器准确性
        const actualCount = starredBox.messages.size;
        if (starredBox.counter !== actualCount) {
            console.warn('星标计数器不一致，正在修复...', {
                expected: actualCount,
                actual: starredBox.counter
            });
            
            starredBox.counter = actualCount;
        }
        
        // 验证消息状态一致性
        StarredBoxSyncManager.validateMessageStates();
        
        // 更新最后同步时间
        starredBox.lastSyncTime = Date.now();
    },
    
    validateMessageStates: () => {
        const starredBox = store.starred;
        const inconsistentMessages = [];
        
        starredBox.messages.forEach(message => {
            if (!message.starred) {
                inconsistentMessages.push(message);
            }
        });
        
        if (inconsistentMessages.length > 0) {
            console.warn('发现状态不一致的星标消息:', inconsistentMessages);
            
            // 修复不一致的状态
            inconsistentMessages.forEach(message => {
                starredBox.messages.delete(message);
                starredBox.counter--;
            });
        }
    },
    
    handleBusNotification: (notification) => {
        const { type, payload, metadata } = notification;
        
        if (type === 'toggle_star') {
            StarredBoxSyncManager.handleStarToggleNotification(payload, metadata);
        }
    },
    
    handleStarToggleNotification: (payload, metadata) => {
        const { id: notifId } = metadata;
        const { message_ids: messageIds, starred } = payload;
        const starredBox = store.starred;
        
        // 检查通知是否已处理
        if (notifId <= starredBox.counter_bus_id) {
            return; // 已处理的通知，跳过
        }
        
        messageIds.forEach(messageId => {
            const message = store.Message.get({ id: messageId });
            
            if (message) {
                if (starred) {
                    if (!starredBox.messages.has(message)) {
                        starredBox.messages.add(message);
                        starredBox.counter++;
                    }
                } else {
                    if (starredBox.messages.has(message)) {
                        starredBox.messages.delete(message);
                        starredBox.counter--;
                    }
                }
                
                message.starred = starred;
            }
        });
        
        // 更新总线ID
        starredBox.counter_bus_id = Math.max(starredBox.counter_bus_id, notifId);
        
        // 触发同步完成事件
        StarredBoxSyncManager.notifySyncComplete(messageIds, starred);
    },
    
    notifySyncComplete: (messageIds, starred) => {
        const event = new CustomEvent('starred_box_synced', {
            detail: {
                messageIds,
                starred,
                timestamp: Date.now(),
                counter: store.starred.counter
            }
        });
        
        document.dispatchEvent(event);
    },
    
    getStarredBoxStats: () => {
        const starredBox = store.starred;
        const messages = Array.from(starredBox.messages);
        
        const stats = {
            totalCount: starredBox.counter,
            messageCount: messages.length,
            lastSyncTime: starredBox.lastSyncTime,
            counterBusId: starredBox.counter_bus_id,
            byAuthor: {},
            byDate: {},
            withAttachments: 0
        };
        
        messages.forEach(message => {
            // 按作者统计
            const authorName = message.author.name;
            stats.byAuthor[authorName] = (stats.byAuthor[authorName] || 0) + 1;
            
            // 按日期统计
            const date = new Date(message.date).toDateString();
            stats.byDate[date] = (stats.byDate[date] || 0) + 1;
            
            // 附件统计
            if (message.attachments && message.attachments.length > 0) {
                stats.withAttachments++;
            }
        });
        
        return stats;
    }
};
```

### 3. 星标消息搜索

```javascript
// 星标消息搜索
const StarredMessageSearch = {
    search: (query, options = {}) => {
        const starredMessages = StarredMessageManager.getStarredMessages();
        
        if (!query) {
            return starredMessages;
        }
        
        const searchTerms = query.toLowerCase().split(' ');
        
        return starredMessages.filter(message => {
            // 搜索消息内容
            const contentMatch = searchTerms.every(term =>
                message.body.toLowerCase().includes(term) ||
                message.subject?.toLowerCase().includes(term)
            );
            
            // 搜索作者
            const authorMatch = searchTerms.every(term =>
                message.author.name.toLowerCase().includes(term) ||
                message.author.email?.toLowerCase().includes(term)
            );
            
            // 搜索附件名称
            const attachmentMatch = message.attachments?.some(attachment =>
                searchTerms.every(term =>
                    attachment.name.toLowerCase().includes(term)
                )
            );
            
            return contentMatch || authorMatch || attachmentMatch;
        });
    },
    
    advancedSearch: (criteria) => {
        const starredMessages = StarredMessageManager.getStarredMessages();
        
        return starredMessages.filter(message => {
            // 日期范围过滤
            if (criteria.dateFrom && new Date(message.date) < new Date(criteria.dateFrom)) {
                return false;
            }
            
            if (criteria.dateTo && new Date(message.date) > new Date(criteria.dateTo)) {
                return false;
            }
            
            // 作者过滤
            if (criteria.author && !message.author.name.toLowerCase().includes(criteria.author.toLowerCase())) {
                return false;
            }
            
            // 主题过滤
            if (criteria.subject && !message.subject?.toLowerCase().includes(criteria.subject.toLowerCase())) {
                return false;
            }
            
            // 附件过滤
            if (criteria.hasAttachment !== undefined) {
                const hasAttachment = message.attachments && message.attachments.length > 0;
                if (hasAttachment !== criteria.hasAttachment) {
                    return false;
                }
            }
            
            // 消息类型过滤
            if (criteria.messageType && message.message_type !== criteria.messageType) {
                return false;
            }
            
            return true;
        });
    },
    
    saveSearch: (name, criteria) => {
        const savedSearches = StarredMessageSearch.getSavedSearches();
        
        const search = {
            id: generateId(),
            name,
            criteria,
            createdAt: Date.now(),
            lastUsed: Date.now()
        };
        
        savedSearches.push(search);
        localStorage.setItem('starred_saved_searches', JSON.stringify(savedSearches));
        
        return search;
    },
    
    getSavedSearches: () => {
        try {
            return JSON.parse(localStorage.getItem('starred_saved_searches') || '[]');
        } catch (error) {
            console.error('获取保存的搜索失败:', error);
            return [];
        }
    },
    
    executeSearch: (searchId) => {
        const savedSearches = StarredMessageSearch.getSavedSearches();
        const search = savedSearches.find(s => s.id === searchId);
        
        if (search) {
            // 更新最后使用时间
            search.lastUsed = Date.now();
            localStorage.setItem('starred_saved_searches', JSON.stringify(savedSearches));
            
            return StarredMessageSearch.advancedSearch(search.criteria);
        }
        
        return [];
    }
};
```

### 4. 星标消息导出

```javascript
// 星标消息导出
const StarredMessageExporter = {
    exportToJSON: (messages) => {
        const exportData = {
            exportDate: new Date().toISOString(),
            totalCount: messages.length,
            messages: messages.map(message => ({
                id: message.id,
                subject: message.subject,
                body: message.body,
                author: {
                    name: message.author.name,
                    email: message.author.email
                },
                date: message.date,
                attachments: message.attachments?.map(att => ({
                    name: att.name,
                    size: att.file_size,
                    mimetype: att.mimetype
                })) || []
            }))
        };
        
        const jsonString = JSON.stringify(exportData, null, 2);
        StarredMessageExporter.downloadFile(jsonString, 'starred_messages.json', 'application/json');
    },
    
    exportToCSV: (messages) => {
        const headers = ['ID', '主题', '作者', '邮箱', '日期', '附件数量'];
        const rows = messages.map(message => [
            message.id,
            message.subject || '',
            message.author.name,
            message.author.email || '',
            new Date(message.date).toLocaleString(),
            message.attachments?.length || 0
        ]);
        
        const csvContent = [headers, ...rows]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');
        
        StarredMessageExporter.downloadFile(csvContent, 'starred_messages.csv', 'text/csv');
    },
    
    downloadFile: (content, filename, mimeType) => {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();
        
        URL.revokeObjectURL(url);
    }
};
```

### 5. 星标消息统计

```javascript
// 星标消息统计
const StarredMessageStatistics = {
    generateReport: () => {
        const stats = StarredBoxSyncManager.getStarredBoxStats();
        const messages = Array.from(store.starred.messages);
        
        const report = {
            summary: {
                totalMessages: stats.totalCount,
                withAttachments: stats.withAttachments,
                uniqueAuthors: <AUTHORS>
                dateRange: StarredMessageStatistics.getDateRange(messages)
            },
            byAuthor: stats.byAuthor,
            byDate: stats.byDate,
            trends: StarredMessageStatistics.calculateTrends(messages),
            topAuthors: <AUTHORS>
        };
        
        return report;
    },
    
    getDateRange: (messages) => {
        if (messages.length === 0) {
            return { earliest: null, latest: null };
        }
        
        const dates = messages.map(msg => new Date(msg.date));
        return {
            earliest: new Date(Math.min(...dates)),
            latest: new Date(Math.max(...dates))
        };
    },
    
    calculateTrends: (messages) => {
        const last30Days = Date.now() - (30 * 24 * 60 * 60 * 1000);
        const last7Days = Date.now() - (7 * 24 * 60 * 60 * 1000);
        
        const recent30 = messages.filter(msg => new Date(msg.date) > last30Days);
        const recent7 = messages.filter(msg => new Date(msg.date) > last7Days);
        
        return {
            last30Days: recent30.length,
            last7Days: recent7.length,
            averagePerDay: recent30.length / 30,
            averagePerWeek: recent7.length
        };
    },
    
    getTopAuthors: <AUTHORS>
        return Object.entries(byAuthor)
            .sort(([,a], [,b]) => b - a)
            .slice(0, limit)
            .map(([author, count]) => ({ author, count }));
    }
};
```

## 通知处理机制

### 1. 总线通知处理

```javascript
// 总线通知处理
const BusNotificationHandler = {
    handleStarNotification: (notification) => {
        const { payload, metadata } = notification;
        const { id: notifId } = metadata;
        const { message_ids: messageIds, starred } = payload;
        
        // 检查通知顺序
        if (!BusNotificationHandler.isValidNotificationOrder(notifId)) {
            console.warn('收到乱序通知，可能需要重新同步');
            return;
        }
        
        // 处理星标变化
        BusNotificationHandler.processStarChange(messageIds, starred, notifId);
        
        // 更新UI
        BusNotificationHandler.updateUI(messageIds, starred);
    },
    
    isValidNotificationOrder: (notifId) => {
        const starredBox = store.starred;
        return notifId > starredBox.counter_bus_id;
    },
    
    processStarChange: (messageIds, starred, notifId) => {
        const starredBox = store.starred;
        
        messageIds.forEach(messageId => {
            const message = store.Message.get({ id: messageId });
            
            if (message) {
                if (starred) {
                    starredBox.messages.add(message);
                    starredBox.counter++;
                } else {
                    starredBox.messages.delete(message);
                    starredBox.counter--;
                }
                
                message.starred = starred;
            }
        });
        
        starredBox.counter_bus_id = notifId;
    },
    
    updateUI: (messageIds, starred) => {
        messageIds.forEach(messageId => {
            const messageElements = document.querySelectorAll(`[data-message-id="${messageId}"]`);
            
            messageElements.forEach(element => {
                const starButton = element.querySelector('.star-button');
                if (starButton) {
                    starButton.classList.toggle('starred', starred);
                    starButton.title = starred ? '取消星标' : '添加星标';
                }
            });
        });
    }
};
```

## 错误处理

### 1. 同步错误处理

```javascript
// 同步错误处理
const SyncErrorHandler = {
    handleSyncError: (error, context) => {
        console.error('星标同步错误:', error, context);
        
        // 记录错误
        SyncErrorHandler.logError(error, context);
        
        // 尝试恢复
        SyncErrorHandler.attemptRecovery(context);
    },
    
    logError: (error, context) => {
        const errorLog = {
            error: error.message,
            context,
            timestamp: Date.now(),
            userAgent: navigator.userAgent
        };
        
        // 保存到本地存储
        const errorHistory = JSON.parse(localStorage.getItem('star_sync_errors') || '[]');
        errorHistory.unshift(errorLog);
        
        if (errorHistory.length > 100) {
            errorHistory.pop();
        }
        
        localStorage.setItem('star_sync_errors', JSON.stringify(errorHistory));
    },
    
    attemptRecovery: (context) => {
        // 尝试重新同步星标邮箱
        setTimeout(() => {
            StarredBoxSyncManager.syncStarredBox();
        }, 1000);
    }
};
```

## 性能优化

### 1. 批量处理优化

```javascript
// 批量处理优化
const BatchProcessor = {
    processBatch: (notifications) => {
        const starNotifications = notifications.filter(n => n.type === 'toggle_star');
        
        if (starNotifications.length === 0) {
            return;
        }
        
        // 合并相同消息的通知
        const mergedNotifications = BatchProcessor.mergeNotifications(starNotifications);
        
        // 批量处理
        mergedNotifications.forEach(notification => {
            BusNotificationHandler.handleStarNotification(notification);
        });
    },
    
    mergeNotifications: (notifications) => {
        const merged = new Map();
        
        notifications.forEach(notification => {
            const { payload, metadata } = notification;
            const { message_ids: messageIds } = payload;
            
            messageIds.forEach(messageId => {
                merged.set(messageId, notification);
            });
        });
        
        return Array.from(merged.values());
    }
};
```

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 非侵入式功能扩展
- 保持原有服务的完整性

### 2. 观察者模式 (Observer Pattern)
- 监听通知变化
- 响应星标状态变化

### 3. 同步模式 (Synchronization Pattern)
- 多客户端数据同步
- 状态一致性保证

## 注意事项

1. **数据一致性**: 确保星标状态的一致性
2. **通知顺序**: 处理乱序通知的情况
3. **性能考虑**: 避免频繁的DOM操作
4. **错误恢复**: 提供错误恢复机制

## 扩展建议

1. **离线支持**: 支持离线状态下的星标操作
2. **冲突解决**: 处理多客户端的星标冲突
3. **批量操作**: 优化批量星标操作的性能
4. **统计分析**: 提供星标使用的统计分析
5. **自动清理**: 自动清理过期的星标消息

该补丁为邮件系统提供了完整的星标消息同步机制，是Web环境下星标功能的重要保障。

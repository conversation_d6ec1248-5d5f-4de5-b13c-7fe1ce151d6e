# Mail Composer Attachment Selector - 邮件编写附件选择器

## 概述

`mail_composer_attachment_selector.js` 实现了 Odoo 邮件系统中邮件编写器的附件选择器字段组件，用于在邮件编写界面中选择和上传附件。该组件继承自标准字段属性，集成了文件输入组件，支持多文件上传、错误处理、通知反馈等功能，通过X2Many CRUD操作管理附件关系，为用户提供了便捷的附件选择和上传体验，是邮件编写系统的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/core/web/mail_composer_attachment_selector.js`
- **行数**: 51
- **模块**: `@mail/core/web/mail_composer_attachment_selector`

## 依赖关系

```javascript
// UI组件依赖
'@web/core/file_input/file_input'         // 文件输入组件

// 核心依赖
'@web/core/l10n/translation'              // 国际化
'@web/core/registry'                      // 注册表系统
'@web/views/fields/standard_field_props'  // 标准字段属性
'@web/core/utils/hooks'                   // Web 核心钩子
'@web/views/fields/relational_utils'     // 关系字段工具
'@odoo/owl'                              // OWL 框架
```

## 组件定义

### MailComposerAttachmentSelector 类

```javascript
class MailComposerAttachmentSelector extends Component {
    static template = "mail.MailComposerAttachmentSelector";
    static components = { FileInput };
    static props = { ...standardFieldProps };
}
```

**组件特性**:
- 使用自定义模板"mail.MailComposerAttachmentSelector"
- 集成FileInput文件输入组件
- 继承标准字段属性

## 组件设置

### setup() 方法

```javascript
setup() {
    this.notification = useService("notification");
    this.operations = useX2ManyCrud(() => {
        return this.props.record.data["attachment_ids"];
    }, true);
}
```

**初始化内容**:
- 通知服务用于显示上传结果
- X2Many CRUD操作用于管理附件关系

## 核心功能

### 1. 文件上传处理

```javascript
async onFileUploaded(files) {
    for (const file of files) {
        if (file.error) {
            return this.notification.add(file.error, {
                title: _t("Uploading error"),
                type: "danger",
            });
        }
        await this.operations.saveRecord([file.id]);
    }
}
```

**上传处理逻辑**:
- 遍历上传的文件列表
- 检查文件上传错误并显示通知
- 成功上传的文件保存到附件关系中

## 字段注册

### 字段配置

```javascript
const mailComposerAttachmentSelector = {
    component: MailComposerAttachmentSelector,
};

registry.category("fields").add("mail_composer_attachment_selector", mailComposerAttachmentSelector);
```

**注册参数**:
- **类别**: "fields"
- **名称**: "mail_composer_attachment_selector"
- **组件**: MailComposerAttachmentSelector

## 使用场景

### 1. 邮件编写表单集成

```javascript
// 邮件编写表单中的附件选择器
const MailComposerForm = {
    setupAttachmentSelector: () => {
        return {
            name: 'attachment_ids',
            type: 'mail_composer_attachment_selector',
            string: '选择附件',
            options: {
                accepted_file_extensions: '.pdf,.doc,.docx,.xls,.xlsx,.png,.jpg,.jpeg,.gif',
                max_file_size: 25 * 1024 * 1024, // 25MB
                multiple: true,
                show_upload_button: true,
                show_file_list: true
            }
        };
    },
    
    renderAttachmentSelector: (record, options) => {
        return `
            <div class="attachment-selector-container">
                <div class="selector-header">
                    <h4>附件选择</h4>
                    <span class="file-count">
                        已选择 ${record.data.attachment_ids.length} 个文件
                    </span>
                </div>
                
                <MailComposerAttachmentSelector
                    record={record}
                    name="attachment_ids"
                    options={options}
                />
                
                <div class="selector-footer">
                    <small class="text-muted">
                        支持的文件类型: PDF, Word, Excel, 图片
                        最大文件大小: 25MB
                    </small>
                </div>
            </div>
        `;
    }
};
```

### 2. 文件上传增强

```javascript
// 文件上传增强功能
const FileUploadEnhancer = {
    enhanceUpload: (selector) => {
        // 添加拖拽上传支持
        FileUploadEnhancer.addDragDropSupport(selector);
        
        // 添加上传进度显示
        FileUploadEnhancer.addProgressIndicator(selector);
        
        // 添加文件预览
        FileUploadEnhancer.addFilePreview(selector);
        
        // 添加批量操作
        FileUploadEnhancer.addBatchOperations(selector);
    },
    
    addDragDropSupport: (selector) => {
        const container = selector.el.querySelector('.attachment-selector-container');
        
        container.addEventListener('dragover', (event) => {
            event.preventDefault();
            container.classList.add('drag-over');
        });
        
        container.addEventListener('dragleave', (event) => {
            event.preventDefault();
            container.classList.remove('drag-over');
        });
        
        container.addEventListener('drop', async (event) => {
            event.preventDefault();
            container.classList.remove('drag-over');
            
            const files = Array.from(event.dataTransfer.files);
            await FileUploadEnhancer.handleFileUpload(selector, files);
        });
    },
    
    addProgressIndicator: (selector) => {
        const progressContainer = document.createElement('div');
        progressContainer.className = 'upload-progress-container';
        progressContainer.innerHTML = `
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
            <span class="progress-text">0%</span>
        `;
        
        selector.el.appendChild(progressContainer);
        
        // 监听上传进度
        selector.onUploadProgress = (progress) => {
            const progressFill = progressContainer.querySelector('.progress-fill');
            const progressText = progressContainer.querySelector('.progress-text');
            
            progressFill.style.width = `${progress}%`;
            progressText.textContent = `${Math.round(progress)}%`;
            
            if (progress === 100) {
                setTimeout(() => {
                    progressContainer.style.display = 'none';
                }, 1000);
            }
        };
    },
    
    addFilePreview: (selector) => {
        const previewContainer = document.createElement('div');
        previewContainer.className = 'file-preview-container';
        
        selector.el.appendChild(previewContainer);
        
        selector.onFileSelected = (files) => {
            previewContainer.innerHTML = '';
            
            files.forEach(file => {
                const previewItem = FileUploadEnhancer.createFilePreview(file);
                previewContainer.appendChild(previewItem);
            });
        };
    },
    
    createFilePreview: (file) => {
        const previewItem = document.createElement('div');
        previewItem.className = 'file-preview-item';
        
        if (file.type.startsWith('image/')) {
            const img = document.createElement('img');
            img.src = URL.createObjectURL(file);
            img.onload = () => URL.revokeObjectURL(img.src);
            previewItem.appendChild(img);
        } else {
            const icon = document.createElement('i');
            icon.className = `fa ${FileUploadEnhancer.getFileIcon(file.type)}`;
            previewItem.appendChild(icon);
        }
        
        const fileName = document.createElement('span');
        fileName.textContent = file.name;
        fileName.className = 'file-name';
        previewItem.appendChild(fileName);
        
        const fileSize = document.createElement('span');
        fileSize.textContent = FileUploadEnhancer.formatFileSize(file.size);
        fileSize.className = 'file-size';
        previewItem.appendChild(fileSize);
        
        return previewItem;
    },
    
    getFileIcon: (mimeType) => {
        const iconMap = {
            'application/pdf': 'fa-file-pdf-o',
            'application/msword': 'fa-file-word-o',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'fa-file-word-o',
            'application/vnd.ms-excel': 'fa-file-excel-o',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'fa-file-excel-o',
            'text/plain': 'fa-file-text-o',
            'application/zip': 'fa-file-archive-o'
        };
        
        return iconMap[mimeType] || 'fa-file-o';
    },
    
    formatFileSize: (bytes) => {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    handleFileUpload: async (selector, files) => {
        try {
            // 验证文件
            const validFiles = files.filter(file => 
                FileUploadEnhancer.validateFile(file)
            );
            
            if (validFiles.length !== files.length) {
                selector.notification.add(
                    '部分文件不符合要求，已自动过滤',
                    { type: 'warning' }
                );
            }
            
            // 上传文件
            await selector.onFileUploaded(validFiles);
            
        } catch (error) {
            console.error('文件上传失败:', error);
            selector.notification.add(
                '文件上传失败，请重试',
                { type: 'danger' }
            );
        }
    },
    
    validateFile: (file) => {
        const maxSize = 25 * 1024 * 1024; // 25MB
        const allowedTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'image/png',
            'image/jpeg',
            'image/gif',
            'text/plain'
        ];
        
        if (file.size > maxSize) {
            return false;
        }
        
        if (!allowedTypes.includes(file.type)) {
            return false;
        }
        
        return true;
    }
};
```

### 3. 附件管理界面

```javascript
// 附件管理界面
const AttachmentManagementInterface = {
    createInterface: (selector) => {
        const interface = document.createElement('div');
        interface.className = 'attachment-management-interface';
        interface.innerHTML = `
            <div class="management-header">
                <h3>附件管理</h3>
                <div class="management-actions">
                    <button class="btn btn-primary select-files-btn">选择文件</button>
                    <button class="btn btn-secondary clear-all-btn">清空所有</button>
                </div>
            </div>
            
            <div class="attachment-list">
                <!-- 附件列表将在这里显示 -->
            </div>
            
            <div class="management-footer">
                <div class="attachment-stats">
                    <span class="total-files">总文件数: 0</span>
                    <span class="total-size">总大小: 0 KB</span>
                </div>
            </div>
        `;
        
        AttachmentManagementInterface.bindEvents(interface, selector);
        return interface;
    },
    
    bindEvents: (interface, selector) => {
        const selectFilesBtn = interface.querySelector('.select-files-btn');
        const clearAllBtn = interface.querySelector('.clear-all-btn');
        
        selectFilesBtn.addEventListener('click', () => {
            AttachmentManagementInterface.openFileSelector(selector);
        });
        
        clearAllBtn.addEventListener('click', () => {
            AttachmentManagementInterface.clearAllAttachments(selector);
        });
    },
    
    openFileSelector: (selector) => {
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.multiple = true;
        fileInput.accept = '.pdf,.doc,.docx,.xls,.xlsx,.png,.jpg,.jpeg,.gif,.txt';
        
        fileInput.addEventListener('change', async (event) => {
            const files = Array.from(event.target.files);
            await selector.onFileUploaded(files);
            AttachmentManagementInterface.updateInterface(selector);
        });
        
        fileInput.click();
    },
    
    clearAllAttachments: async (selector) => {
        if (confirm('确定要清空所有附件吗？')) {
            try {
                const attachmentIds = selector.props.record.data.attachment_ids;
                for (const attachmentId of attachmentIds) {
                    await selector.operations.removeRecord(attachmentId);
                }
                
                selector.notification.add(
                    '所有附件已清空',
                    { type: 'success' }
                );
                
                AttachmentManagementInterface.updateInterface(selector);
            } catch (error) {
                console.error('清空附件失败:', error);
                selector.notification.add(
                    '清空附件失败，请重试',
                    { type: 'danger' }
                );
            }
        }
    },
    
    updateInterface: (selector) => {
        const attachments = selector.props.record.data.attachment_ids;
        const totalFiles = attachments.length;
        const totalSize = attachments.reduce((sum, att) => sum + (att.file_size || 0), 0);
        
        // 更新统计信息
        const interface = selector.el.querySelector('.attachment-management-interface');
        if (interface) {
            const totalFilesSpan = interface.querySelector('.total-files');
            const totalSizeSpan = interface.querySelector('.total-size');
            
            totalFilesSpan.textContent = `总文件数: ${totalFiles}`;
            totalSizeSpan.textContent = `总大小: ${FileUploadEnhancer.formatFileSize(totalSize)}`;
        }
        
        // 更新附件列表
        AttachmentManagementInterface.updateAttachmentList(selector, attachments);
    },
    
    updateAttachmentList: (selector, attachments) => {
        const listContainer = selector.el.querySelector('.attachment-list');
        if (!listContainer) return;
        
        listContainer.innerHTML = '';
        
        attachments.forEach(attachment => {
            const item = AttachmentManagementInterface.createAttachmentItem(
                attachment, 
                selector
            );
            listContainer.appendChild(item);
        });
    },
    
    createAttachmentItem: (attachment, selector) => {
        const item = document.createElement('div');
        item.className = 'attachment-item';
        item.innerHTML = `
            <div class="attachment-info">
                <i class="fa ${FileUploadEnhancer.getFileIcon(attachment.mimetype)}"></i>
                <div class="attachment-details">
                    <span class="attachment-name">${attachment.name}</span>
                    <span class="attachment-size">${FileUploadEnhancer.formatFileSize(attachment.file_size)}</span>
                </div>
            </div>
            <div class="attachment-actions">
                <button class="btn btn-sm btn-secondary download-btn" title="下载">
                    <i class="fa fa-download"></i>
                </button>
                <button class="btn btn-sm btn-danger remove-btn" title="删除">
                    <i class="fa fa-trash"></i>
                </button>
            </div>
        `;
        
        // 绑定事件
        const downloadBtn = item.querySelector('.download-btn');
        const removeBtn = item.querySelector('.remove-btn');
        
        downloadBtn.addEventListener('click', () => {
            AttachmentManagementInterface.downloadAttachment(attachment);
        });
        
        removeBtn.addEventListener('click', async () => {
            await AttachmentManagementInterface.removeAttachment(attachment, selector);
        });
        
        return item;
    },
    
    downloadAttachment: (attachment) => {
        const link = document.createElement('a');
        link.href = `/web/content/${attachment.id}?download=true`;
        link.download = attachment.name;
        link.click();
    },
    
    removeAttachment: async (attachment, selector) => {
        if (confirm(`确定要删除附件 "${attachment.name}" 吗？`)) {
            try {
                await selector.operations.removeRecord(attachment.id);
                
                selector.notification.add(
                    `附件 "${attachment.name}" 已删除`,
                    { type: 'success' }
                );
                
                AttachmentManagementInterface.updateInterface(selector);
            } catch (error) {
                console.error('删除附件失败:', error);
                selector.notification.add(
                    '删除附件失败，请重试',
                    { type: 'danger' }
                );
            }
        }
    }
};
```

### 4. 文件验证系统

```javascript
// 文件验证系统
const FileValidationSystem = {
    validateFiles: (files, options = {}) => {
        const results = {
            valid: [],
            invalid: [],
            errors: []
        };
        
        files.forEach(file => {
            const validation = FileValidationSystem.validateSingleFile(file, options);
            
            if (validation.isValid) {
                results.valid.push(file);
            } else {
                results.invalid.push({
                    file,
                    errors: validation.errors
                });
                results.errors.push(...validation.errors);
            }
        });
        
        return results;
    },
    
    validateSingleFile: (file, options) => {
        const errors = [];
        
        // 文件大小验证
        const maxSize = options.maxSize || 25 * 1024 * 1024; // 25MB
        if (file.size > maxSize) {
            errors.push(`文件 "${file.name}" 大小超过限制 (${FileUploadEnhancer.formatFileSize(maxSize)})`);
        }
        
        // 文件类型验证
        const allowedTypes = options.allowedTypes || [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'image/png',
            'image/jpeg',
            'image/gif'
        ];
        
        if (!allowedTypes.includes(file.type)) {
            errors.push(`文件 "${file.name}" 类型不支持`);
        }
        
        // 文件名验证
        if (file.name.length > 255) {
            errors.push(`文件 "${file.name}" 名称过长`);
        }
        
        // 特殊字符验证
        const invalidChars = /[<>:"/\\|?*]/;
        if (invalidChars.test(file.name)) {
            errors.push(`文件 "${file.name}" 名称包含无效字符`);
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    },
    
    showValidationResults: (results, notification) => {
        if (results.invalid.length > 0) {
            const errorMessage = results.errors.join('\n');
            notification.add(errorMessage, {
                title: '文件验证失败',
                type: 'danger'
            });
        }
        
        if (results.valid.length > 0) {
            notification.add(
                `${results.valid.length} 个文件验证通过`,
                { type: 'success' }
            );
        }
    }
};
```

### 5. 批量操作功能

```javascript
// 批量操作功能
const BatchOperations = {
    setupBatchOperations: (selector) => {
        const batchContainer = document.createElement('div');
        batchContainer.className = 'batch-operations-container';
        batchContainer.innerHTML = `
            <div class="batch-header">
                <label>
                    <input type="checkbox" class="select-all-checkbox"> 全选
                </label>
                <span class="selected-count">已选择 0 个文件</span>
            </div>
            
            <div class="batch-actions">
                <button class="btn btn-sm btn-secondary download-selected-btn" disabled>
                    <i class="fa fa-download"></i> 下载选中
                </button>
                <button class="btn btn-sm btn-danger remove-selected-btn" disabled>
                    <i class="fa fa-trash"></i> 删除选中
                </button>
            </div>
        `;
        
        selector.el.appendChild(batchContainer);
        BatchOperations.bindBatchEvents(batchContainer, selector);
    },
    
    bindBatchEvents: (container, selector) => {
        const selectAllCheckbox = container.querySelector('.select-all-checkbox');
        const downloadSelectedBtn = container.querySelector('.download-selected-btn');
        const removeSelectedBtn = container.querySelector('.remove-selected-btn');
        
        selectAllCheckbox.addEventListener('change', (event) => {
            BatchOperations.toggleSelectAll(event.target.checked, selector);
        });
        
        downloadSelectedBtn.addEventListener('click', () => {
            BatchOperations.downloadSelected(selector);
        });
        
        removeSelectedBtn.addEventListener('click', () => {
            BatchOperations.removeSelected(selector);
        });
    },
    
    toggleSelectAll: (selectAll, selector) => {
        const checkboxes = selector.el.querySelectorAll('.attachment-item input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll;
        });
        
        BatchOperations.updateBatchActions(selector);
    },
    
    updateBatchActions: (selector) => {
        const selectedCheckboxes = selector.el.querySelectorAll('.attachment-item input[type="checkbox"]:checked');
        const selectedCount = selectedCheckboxes.length;
        
        const selectedCountSpan = selector.el.querySelector('.selected-count');
        const downloadBtn = selector.el.querySelector('.download-selected-btn');
        const removeBtn = selector.el.querySelector('.remove-selected-btn');
        
        selectedCountSpan.textContent = `已选择 ${selectedCount} 个文件`;
        downloadBtn.disabled = selectedCount === 0;
        removeBtn.disabled = selectedCount === 0;
    },
    
    downloadSelected: (selector) => {
        const selectedCheckboxes = selector.el.querySelectorAll('.attachment-item input[type="checkbox"]:checked');
        
        selectedCheckboxes.forEach(checkbox => {
            const attachmentId = checkbox.dataset.attachmentId;
            const attachment = selector.props.record.data.attachment_ids.find(
                att => att.id === parseInt(attachmentId)
            );
            
            if (attachment) {
                AttachmentManagementInterface.downloadAttachment(attachment);
            }
        });
    },
    
    removeSelected: async (selector) => {
        const selectedCheckboxes = selector.el.querySelectorAll('.attachment-item input[type="checkbox"]:checked');
        const selectedCount = selectedCheckboxes.length;
        
        if (confirm(`确定要删除选中的 ${selectedCount} 个附件吗？`)) {
            try {
                for (const checkbox of selectedCheckboxes) {
                    const attachmentId = parseInt(checkbox.dataset.attachmentId);
                    await selector.operations.removeRecord(attachmentId);
                }
                
                selector.notification.add(
                    `已删除 ${selectedCount} 个附件`,
                    { type: 'success' }
                );
                
                AttachmentManagementInterface.updateInterface(selector);
            } catch (error) {
                console.error('批量删除失败:', error);
                selector.notification.add(
                    '批量删除失败，请重试',
                    { type: 'danger' }
                );
            }
        }
    }
};
```

## 错误处理

### 1. 上传错误处理

```javascript
// 上传错误处理
const UploadErrorHandler = {
    handleUploadError: (error, file, notification) => {
        let errorMessage = '文件上传失败';
        
        if (error.includes('size')) {
            errorMessage = `文件 "${file.name}" 大小超过限制`;
        } else if (error.includes('type')) {
            errorMessage = `文件 "${file.name}" 类型不支持`;
        } else if (error.includes('network')) {
            errorMessage = '网络连接失败，请检查网络后重试';
        }
        
        notification.add(errorMessage, {
            title: '上传错误',
            type: 'danger'
        });
        
        // 记录错误日志
        console.error('文件上传错误:', {
            file: file.name,
            size: file.size,
            type: file.type,
            error: error
        });
    }
};
```

## 性能优化

### 1. 文件处理优化

```javascript
// 文件处理优化
const FileProcessingOptimizer = {
    optimizeFileProcessing: (files) => {
        // 按文件大小排序，小文件优先处理
        return files.sort((a, b) => a.size - b.size);
    },
    
    batchProcessFiles: async (files, batchSize = 3) => {
        const results = [];
        
        for (let i = 0; i < files.length; i += batchSize) {
            const batch = files.slice(i, i + batchSize);
            const batchResults = await Promise.all(
                batch.map(file => FileProcessingOptimizer.processFile(file))
            );
            results.push(...batchResults);
        }
        
        return results;
    }
};
```

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的附件选择器组件
- 清晰的组件接口

### 2. 策略模式 (Strategy Pattern)
- 不同文件类型的不同处理策略
- 可配置的验证策略

### 3. 观察者模式 (Observer Pattern)
- 监听文件上传状态变化
- 响应用户操作

## 注意事项

1. **文件安全**: 严格验证文件类型和大小
2. **用户体验**: 提供清晰的上传进度和错误提示
3. **性能考虑**: 大文件上传的性能优化
4. **错误处理**: 完善的错误处理和用户反馈

## 扩展建议

1. **云存储集成**: 支持云存储服务的集成
2. **文件压缩**: 自动压缩大文件
3. **断点续传**: 支持大文件的断点续传
4. **文件预览**: 增强文件预览功能
5. **权限控制**: 细粒度的文件访问权限控制

该组件为邮件编写提供了专业的附件选择和上传功能，是邮件系统的重要组成部分。

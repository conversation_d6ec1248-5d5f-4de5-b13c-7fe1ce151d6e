# Recipient List - 收件人列表

## 概述

`recipient_list.js` 实现了 Odoo 邮件系统中的收件人列表组件，用于显示线程的完整收件人列表。该组件支持收件人的详细显示、懒加载更多收件人、收件人文本格式化等功能，通过可见性钩子实现自动加载更多收件人，为用户提供了完整的收件人管理界面，是邮件系统中收件人信息展示的核心组件。

## 文件信息
- **路径**: `/mail/static/src/core/web/recipient_list.js`
- **行数**: 43
- **模块**: `@mail/core/web/recipient_list`

## 依赖关系

```javascript
// UI组件依赖
'@mail/utils/common/hooks'    // 邮件工具钩子

// 核心依赖
'@odoo/owl'                  // OWL 框架
'@web/core/utils/hooks'      // Web 核心钩子
'@web/core/l10n/translation' // 国际化
'@web/core/utils/strings'    // 字符串工具
```

## 组件定义

### RecipientList 类

```javascript
class RecipientList extends Component {
    static template = "mail.RecipientList";
    static props = ["thread", "close?"];
}
```

**组件配置**:
- **模板**: "mail.RecipientList"
- **Props**: thread（线程对象）、close（可选的关闭函数）

## Props 配置

### Props 详细说明

- **`thread`** (必需):
  - 类型: Thread 模型实例
  - 用途: 提供收件人数据的线程对象
  - 功能: 获取线程的收件人列表和相关操作

- **`close`** (可选):
  - 类型: 函数
  - 用途: 关闭收件人列表的回调函数
  - 功能: 处理列表的关闭操作

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.store = useState(useService("mail.store"));
    this.loadMoreState = useVisible("load-more", () => {
        if (this.loadMoreState.isVisible) {
            this.props.thread.loadMoreRecipients();
        }
    });
}
```

**初始化内容**:
- 邮件存储服务的响应式状态
- 可见性钩子用于自动加载更多收件人

## 核心功能

### 1. 收件人文本格式化

```javascript
getRecipientText(recipient) {
    return (
        recipient.partner.email ||
        sprintf(_t("[%(name)s] (no email address)"), { name: recipient.partner.name })
    );
}
```

**格式化逻辑**:
- 优先显示邮箱地址
- 无邮箱时显示"[姓名] (no email address)"格式
- 支持国际化

### 2. 懒加载机制

```javascript
this.loadMoreState = useVisible("load-more", () => {
    if (this.loadMoreState.isVisible) {
        this.props.thread.loadMoreRecipients();
    }
});
```

**懒加载逻辑**:
- 监听"load-more"元素的可见性
- 当元素可见时自动加载更多收件人
- 通过线程对象的loadMoreRecipients方法加载

## 使用场景

### 1. 邮件详情收件人显示

```javascript
// 邮件详情中的收件人列表
const EmailDetailRecipients = ({ email, showFullList = false }) => {
    const [expanded, setExpanded] = useState(showFullList);
    const [recipientStats, setRecipientStats] = useState(null);
    
    useEffect(() => {
        // 计算收件人统计
        const stats = calculateRecipientStats(email.thread.recipients);
        setRecipientStats(stats);
    }, [email.thread.recipients]);
    
    const calculateRecipientStats = (recipients) => {
        return {
            total: recipients.length,
            withEmail: recipients.filter(r => r.partner.email).length,
            withoutEmail: recipients.filter(r => !r.partner.email).length,
            internal: recipients.filter(r => r.partner.isInternal).length,
            external: recipients.filter(r => !r.partner.isInternal).length
        };
    };
    
    const toggleExpanded = () => {
        setExpanded(!expanded);
        
        // 记录展开操作
        logUserAction('recipient_list_toggled', {
            emailId: email.id,
            expanded: !expanded,
            recipientCount: email.thread.recipients.length
        });
    };
    
    return (
        <div class="email-detail-recipients">
            <div class="recipients-header">
                <h4>收件人 ({recipientStats?.total || 0})</h4>
                
                {recipientStats && (
                    <div class="recipient-stats">
                        <span class="stat-item">
                            <i class="fa fa-envelope"></i>
                            {recipientStats.withEmail} 有邮箱
                        </span>
                        <span class="stat-item">
                            <i class="fa fa-building"></i>
                            {recipientStats.internal} 内部
                        </span>
                        <span class="stat-item">
                            <i class="fa fa-globe"></i>
                            {recipientStats.external} 外部
                        </span>
                    </div>
                )}
                
                <button 
                    class="toggle-btn"
                    onClick={toggleExpanded}
                >
                    {expanded ? '收起' : '展开'}
                    <i class={`fa fa-chevron-${expanded ? 'up' : 'down'}`}></i>
                </button>
            </div>
            
            {expanded && (
                <RecipientList 
                    thread={email.thread}
                    close={() => setExpanded(false)}
                />
            )}
        </div>
    );
};
```

### 2. 收件人管理界面

```javascript
// 收件人管理界面
const RecipientManagementInterface = ({ thread }) => {
    const [selectedRecipients, setSelectedRecipients] = useState(new Set());
    const [filterOptions, setFilterOptions] = useState({
        hasEmail: null,
        isInternal: null,
        searchTerm: ''
    });
    
    const filteredRecipients = useMemo(() => {
        return thread.recipients.filter(recipient => {
            // 邮箱过滤
            if (filterOptions.hasEmail !== null) {
                const hasEmail = !!recipient.partner.email;
                if (hasEmail !== filterOptions.hasEmail) return false;
            }
            
            // 内部/外部过滤
            if (filterOptions.isInternal !== null) {
                if (recipient.partner.isInternal !== filterOptions.isInternal) return false;
            }
            
            // 搜索过滤
            if (filterOptions.searchTerm) {
                const searchLower = filterOptions.searchTerm.toLowerCase();
                const nameMatch = recipient.partner.name.toLowerCase().includes(searchLower);
                const emailMatch = recipient.partner.email?.toLowerCase().includes(searchLower);
                if (!nameMatch && !emailMatch) return false;
            }
            
            return true;
        });
    }, [thread.recipients, filterOptions]);
    
    const handleRecipientSelect = (recipientId, selected) => {
        setSelectedRecipients(prev => {
            const newSet = new Set(prev);
            if (selected) {
                newSet.add(recipientId);
            } else {
                newSet.delete(recipientId);
            }
            return newSet;
        });
    };
    
    const handleBatchOperation = async (operation) => {
        const selectedIds = Array.from(selectedRecipients);
        
        try {
            switch (operation) {
                case 'remove':
                    await removeRecipients(thread, selectedIds);
                    break;
                case 'notify':
                    await notifyRecipients(thread, selectedIds);
                    break;
                case 'export':
                    await exportRecipients(thread, selectedIds);
                    break;
            }
            
            // 清空选择
            setSelectedRecipients(new Set());
            
            showNotification(`批量操作完成: ${operation}`, 'success');
        } catch (error) {
            console.error('批量操作失败:', error);
            showNotification('批量操作失败，请重试', 'error');
        }
    };
    
    return (
        <div class="recipient-management">
            <div class="management-header">
                <div class="filter-controls">
                    <input
                        type="text"
                        placeholder="搜索收件人..."
                        value={filterOptions.searchTerm}
                        onChange={(e) => setFilterOptions(prev => ({
                            ...prev,
                            searchTerm: e.target.value
                        }))}
                    />
                    
                    <select
                        value={filterOptions.hasEmail || ''}
                        onChange={(e) => setFilterOptions(prev => ({
                            ...prev,
                            hasEmail: e.target.value === '' ? null : e.target.value === 'true'
                        }))}
                    >
                        <option value="">所有邮箱状态</option>
                        <option value="true">有邮箱</option>
                        <option value="false">无邮箱</option>
                    </select>
                    
                    <select
                        value={filterOptions.isInternal || ''}
                        onChange={(e) => setFilterOptions(prev => ({
                            ...prev,
                            isInternal: e.target.value === '' ? null : e.target.value === 'true'
                        }))}
                    >
                        <option value="">所有用户类型</option>
                        <option value="true">内部用户</option>
                        <option value="false">外部用户</option>
                    </select>
                </div>
                
                {selectedRecipients.size > 0 && (
                    <div class="batch-operations">
                        <span>已选择 {selectedRecipients.size} 个收件人</span>
                        <button onClick={() => handleBatchOperation('remove')}>
                            批量移除
                        </button>
                        <button onClick={() => handleBatchOperation('notify')}>
                            批量通知
                        </button>
                        <button onClick={() => handleBatchOperation('export')}>
                            导出选中
                        </button>
                    </div>
                )}
            </div>
            
            <div class="recipient-list-container">
                <EnhancedRecipientList
                    thread={{ ...thread, recipients: filteredRecipients }}
                    selectedRecipients={selectedRecipients}
                    onRecipientSelect={handleRecipientSelect}
                />
            </div>
        </div>
    );
};
```

### 3. 收件人分组显示

```javascript
// 收件人分组显示
const GroupedRecipientList = ({ thread, groupBy = 'type' }) => {
    const [groupedRecipients, setGroupedRecipients] = useState({});
    const [expandedGroups, setExpandedGroups] = useState(new Set());
    
    useEffect(() => {
        const grouped = groupRecipients(thread.recipients, groupBy);
        setGroupedRecipients(grouped);
        
        // 默认展开第一个组
        if (Object.keys(grouped).length > 0) {
            setExpandedGroups(new Set([Object.keys(grouped)[0]]));
        }
    }, [thread.recipients, groupBy]);
    
    const groupRecipients = (recipients, groupBy) => {
        const groups = {};
        
        recipients.forEach(recipient => {
            let groupKey;
            
            switch (groupBy) {
                case 'type':
                    groupKey = recipient.partner.isInternal ? 'internal' : 'external';
                    break;
                case 'email':
                    groupKey = recipient.partner.email ? 'with_email' : 'without_email';
                    break;
                case 'domain':
                    if (recipient.partner.email) {
                        groupKey = recipient.partner.email.split('@')[1];
                    } else {
                        groupKey = 'no_domain';
                    }
                    break;
                default:
                    groupKey = 'all';
            }
            
            if (!groups[groupKey]) {
                groups[groupKey] = [];
            }
            
            groups[groupKey].push(recipient);
        });
        
        return groups;
    };
    
    const toggleGroup = (groupKey) => {
        setExpandedGroups(prev => {
            const newSet = new Set(prev);
            if (newSet.has(groupKey)) {
                newSet.delete(groupKey);
            } else {
                newSet.add(groupKey);
            }
            return newSet;
        });
    };
    
    const getGroupLabel = (groupKey) => {
        const labels = {
            'internal': '内部用户',
            'external': '外部用户',
            'with_email': '有邮箱',
            'without_email': '无邮箱',
            'no_domain': '无域名',
            'all': '所有收件人'
        };
        
        return labels[groupKey] || groupKey;
    };
    
    return (
        <div class="grouped-recipient-list">
            {Object.entries(groupedRecipients).map(([groupKey, recipients]) => (
                <div key={groupKey} class="recipient-group">
                    <div 
                        class="group-header"
                        onClick={() => toggleGroup(groupKey)}
                    >
                        <i class={`fa fa-chevron-${expandedGroups.has(groupKey) ? 'down' : 'right'}`}></i>
                        <span class="group-label">{getGroupLabel(groupKey)}</span>
                        <span class="group-count">({recipients.length})</span>
                    </div>
                    
                    {expandedGroups.has(groupKey) && (
                        <div class="group-content">
                            <RecipientList 
                                thread={{ ...thread, recipients }}
                            />
                        </div>
                    )}
                </div>
            ))}
        </div>
    );
};
```

### 4. 收件人搜索和过滤

```javascript
// 收件人搜索和过滤
const RecipientSearchFilter = {
    searchRecipients: (recipients, searchTerm) => {
        if (!searchTerm) return recipients;
        
        const term = searchTerm.toLowerCase();
        
        return recipients.filter(recipient => {
            const name = recipient.partner.name.toLowerCase();
            const email = recipient.partner.email?.toLowerCase() || '';
            
            return name.includes(term) || email.includes(term);
        });
    },
    
    filterByStatus: (recipients, status) => {
        switch (status) {
            case 'active':
                return recipients.filter(r => r.partner.active);
            case 'inactive':
                return recipients.filter(r => !r.partner.active);
            case 'online':
                return recipients.filter(r => r.partner.isOnline);
            case 'offline':
                return recipients.filter(r => !r.partner.isOnline);
            default:
                return recipients;
        }
    },
    
    filterByEmailStatus: (recipients, hasEmail) => {
        if (hasEmail === null) return recipients;
        
        return recipients.filter(recipient => {
            const recipientHasEmail = !!recipient.partner.email;
            return recipientHasEmail === hasEmail;
        });
    },
    
    sortRecipients: (recipients, sortBy, direction = 'asc') => {
        return [...recipients].sort((a, b) => {
            let aValue, bValue;
            
            switch (sortBy) {
                case 'name':
                    aValue = a.partner.name.toLowerCase();
                    bValue = b.partner.name.toLowerCase();
                    break;
                case 'email':
                    aValue = a.partner.email?.toLowerCase() || '';
                    bValue = b.partner.email?.toLowerCase() || '';
                    break;
                case 'type':
                    aValue = a.partner.isInternal ? 'internal' : 'external';
                    bValue = b.partner.isInternal ? 'internal' : 'external';
                    break;
                default:
                    return 0;
            }
            
            let result = 0;
            if (aValue < bValue) result = -1;
            else if (aValue > bValue) result = 1;
            
            return direction === 'desc' ? -result : result;
        });
    }
};
```

### 5. 收件人导出功能

```javascript
// 收件人导出功能
const RecipientExporter = {
    exportToCSV: (recipients, filename = 'recipients.csv') => {
        const headers = ['姓名', '邮箱', '类型', '状态'];
        const rows = recipients.map(recipient => [
            recipient.partner.name,
            recipient.partner.email || '',
            recipient.partner.isInternal ? '内部' : '外部',
            recipient.partner.active ? '活跃' : '停用'
        ]);
        
        const csvContent = [headers, ...rows]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');
        
        RecipientExporter.downloadFile(csvContent, filename, 'text/csv');
    },
    
    exportToJSON: (recipients, filename = 'recipients.json') => {
        const data = recipients.map(recipient => ({
            id: recipient.partner.id,
            name: recipient.partner.name,
            email: recipient.partner.email,
            isInternal: recipient.partner.isInternal,
            active: recipient.partner.active,
            lastSeen: recipient.partner.lastSeen
        }));
        
        const jsonContent = JSON.stringify(data, null, 2);
        RecipientExporter.downloadFile(jsonContent, filename, 'application/json');
    },
    
    downloadFile: (content, filename, mimeType) => {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();
        
        URL.revokeObjectURL(url);
    },
    
    generateReport: (recipients) => {
        const stats = {
            total: recipients.length,
            withEmail: recipients.filter(r => r.partner.email).length,
            withoutEmail: recipients.filter(r => !r.partner.email).length,
            internal: recipients.filter(r => r.partner.isInternal).length,
            external: recipients.filter(r => !r.partner.isInternal).length,
            active: recipients.filter(r => r.partner.active).length,
            inactive: recipients.filter(r => !r.partner.active).length
        };
        
        const report = `
收件人统计报告
================

总收件人数: ${stats.total}

邮箱状态:
- 有邮箱: ${stats.withEmail} (${((stats.withEmail / stats.total) * 100).toFixed(1)}%)
- 无邮箱: ${stats.withoutEmail} (${((stats.withoutEmail / stats.total) * 100).toFixed(1)}%)

用户类型:
- 内部用户: ${stats.internal} (${((stats.internal / stats.total) * 100).toFixed(1)}%)
- 外部用户: ${stats.external} (${((stats.external / stats.total) * 100).toFixed(1)}%)

用户状态:
- 活跃用户: ${stats.active} (${((stats.active / stats.total) * 100).toFixed(1)}%)
- 停用用户: ${stats.inactive} (${((stats.inactive / stats.total) * 100).toFixed(1)}%)

生成时间: ${new Date().toLocaleString()}
        `;
        
        return report.trim();
    }
};
```

## 懒加载优化

### 1. 虚拟滚动

```javascript
// 虚拟滚动优化
const VirtualScrollRecipientList = ({ thread, itemHeight = 50 }) => {
    const [visibleRange, setVisibleRange] = useState({ start: 0, end: 20 });
    const [scrollTop, setScrollTop] = useState(0);
    
    const containerHeight = 400; // 容器高度
    const totalHeight = thread.recipients.length * itemHeight;
    
    const handleScroll = (event) => {
        const newScrollTop = event.target.scrollTop;
        setScrollTop(newScrollTop);
        
        const start = Math.floor(newScrollTop / itemHeight);
        const end = Math.min(
            start + Math.ceil(containerHeight / itemHeight) + 5,
            thread.recipients.length
        );
        
        setVisibleRange({ start, end });
    };
    
    const visibleRecipients = thread.recipients.slice(visibleRange.start, visibleRange.end);
    
    return (
        <div 
            class="virtual-scroll-container"
            style={{ height: containerHeight, overflow: 'auto' }}
            onScroll={handleScroll}
        >
            <div style={{ height: totalHeight, position: 'relative' }}>
                <div 
                    style={{ 
                        transform: `translateY(${visibleRange.start * itemHeight}px)`,
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0
                    }}
                >
                    {visibleRecipients.map((recipient, index) => (
                        <div 
                            key={recipient.partner.id}
                            style={{ height: itemHeight }}
                            class="recipient-item"
                        >
                            <RecipientItem recipient={recipient} />
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};
```

## 性能优化

### 1. 收件人数据缓存

```javascript
// 收件人数据缓存
const RecipientDataCache = {
    cache: new Map(),
    
    getCachedRecipients: (threadId) => {
        const cached = RecipientDataCache.cache.get(threadId);
        if (cached && Date.now() - cached.timestamp < 300000) { // 5分钟缓存
            return cached.recipients;
        }
        return null;
    },
    
    setCachedRecipients: (threadId, recipients) => {
        RecipientDataCache.cache.set(threadId, {
            recipients,
            timestamp: Date.now()
        });
    }
};
```

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的收件人列表组件
- 清晰的组件接口

### 2. 观察者模式 (Observer Pattern)
- 监听可见性变化
- 响应滚动事件

### 3. 策略模式 (Strategy Pattern)
- 不同的收件人显示策略
- 可配置的加载策略

## 注意事项

1. **性能考虑**: 大量收件人时的性能优化
2. **用户体验**: 提供流畅的滚动和加载体验
3. **数据准确性**: 确保收件人信息的准确显示
4. **国际化**: 支持多语言的收件人信息

## 扩展建议

1. **高级搜索**: 支持更复杂的搜索条件
2. **收件人分析**: 提供收件人的统计分析
3. **批量编辑**: 支持批量编辑收件人信息
4. **收件人验证**: 验证邮箱地址的有效性
5. **收件人同步**: 与外部系统同步收件人信息

该组件为邮件系统提供了完整的收件人列表显示和管理功能，是邮件管理的重要组成部分。

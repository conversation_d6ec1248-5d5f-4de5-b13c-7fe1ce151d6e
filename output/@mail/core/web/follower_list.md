# Follower List - 关注者列表组件

## 概述

`follower_list.js` 实现了 Odoo 邮件系统中的关注者列表组件，用于显示和管理记录的关注者。该组件支持添加关注者、查看关注者详情、编辑关注者设置、移除关注者等功能，提供了完整的关注者管理界面，通常在下拉菜单中显示，是邮件系统中关注者管理的核心UI组件。

## 文件信息
- **路径**: `/mail/static/src/core/web/follower_list.js`
- **行数**: 93
- **模块**: `@mail/core/web/follower_list`

## 依赖关系

```javascript
// UI组件依赖
'@mail/core/web/follower_subtype_dialog'  // 关注者子类型对话框
'@web/core/dropdown/dropdown_item'        // 下拉框项目

// 工具和钩子依赖
'@mail/utils/common/hooks'                // 邮件工具钩子
'@odoo/owl'                              // OWL 框架
'@web/core/l10n/translation'             // 国际化
'@web/core/utils/hooks'                  // Web 核心钩子
```

## 组件定义

### FollowerList 类

```javascript
class FollowerList extends Component {
    static template = "mail.FollowerList";
    static components = { DropdownItem };
    static props = [
        "onAddFollowers?",      // 添加关注者回调（可选）
        "onFollowerChanged?",   // 关注者变化回调（可选）
        "thread",              // 线程对象
        "dropdown"             // 下拉框对象
    ];
}
```

## Props 配置

### Props 详细说明

- **`onAddFollowers`** (可选):
  - 类型: 函数
  - 用途: 添加关注者后的回调
  - 功能: 通知父组件关注者已添加

- **`onFollowerChanged`** (可选):
  - 类型: 函数
  - 用途: 关注者发生变化时的回调
  - 参数: thread (线程对象)

- **`thread`** (必需):
  - 类型: Thread 模型实例
  - 用途: 要管理关注者的线程对象
  - 功能: 提供关注者数据和操作方法

- **`dropdown`** (必需):
  - 类型: 下拉框对象
  - 用途: 控制下拉框的显示和隐藏
  - 功能: 操作完成后关闭下拉框

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.action = useService("action");
    this.store = useState(useService("mail.store"));
    useVisible("load-more", (isVisible) => {
        if (isVisible) {
            this.props.thread.loadMoreFollowers();
        }
    });
}
```

**初始化内容**:
- 动作服务用于打开对话框
- 邮件存储的响应式状态
- 可见性钩子用于懒加载更多关注者

## 核心功能

### 1. 添加关注者

```javascript
onClickAddFollowers() {
    const action = {
        type: "ir.actions.act_window",
        res_model: "mail.wizard.invite",
        view_mode: "form",
        views: [[false, "form"]],
        name: _t("Add followers to this document"),
        target: "new",
        context: {
            default_res_model: this.props.thread.model,
            default_res_id: this.props.thread.id,
            dialog_size: "medium",
        },
    };
    this.action.doAction(action, {
        onClose: () => {
            this.props.onAddFollowers?.();
        },
    });
}
```

**添加流程**:
1. 创建邀请向导动作
2. 设置目标记录的模型和ID
3. 打开中等大小的对话框
4. 对话框关闭后触发回调

### 2. 查看关注者详情

```javascript
onClickDetails(ev, follower) {
    this.store.openDocument({ id: follower.partner.id, model: "res.partner" });
    this.props.dropdown.close();
}
```

**详情查看**:
- 打开关注者合作伙伴的详情页面
- 关闭下拉框

### 3. 编辑关注者设置

```javascript
async onClickEdit(ev, follower) {
    this.env.services.dialog.add(FollowerSubtypeDialog, {
        follower,
        onFollowerChanged: () => this.props.onFollowerChanged?.(this.props.thread),
    });
    this.props.dropdown.close();
}
```

**编辑流程**:
1. 打开关注者子类型对话框
2. 传递关注者对象和变化回调
3. 关闭下拉框

### 4. 移除关注者

```javascript
async onClickRemove(ev, follower) {
    const thread = this.props.thread;
    await follower.remove();
    this.props.onFollowerChanged?.(thread);
}
```

**移除流程**:
1. 调用关注者的移除方法
2. 触发关注者变化回调

## 使用场景

### 1. 文档关注者管理

```javascript
// 在文档表单中管理关注者
const DocumentFollowerManager = ({ document }) => {
    const [showFollowerList, setShowFollowerList] = useState(false);
    
    const handleAddFollowers = () => {
        // 重新加载文档数据
        document.load();
        
        // 更新关注者计数
        updateFollowerCount(document);
    };
    
    const handleFollowerChanged = (thread) => {
        // 刷新线程数据
        thread.refresh();
        
        // 更新UI状态
        updateUIState();
    };
    
    return (
        <div class="document-follower-manager">
            <Dropdown>
                <button class="dropdown-toggle">
                    关注者 ({document.followerCount})
                </button>
                <DropdownMenu>
                    <FollowerList
                        thread={document.thread}
                        onAddFollowers={handleAddFollowers}
                        onFollowerChanged={handleFollowerChanged}
                        dropdown={dropdown}
                    />
                </DropdownMenu>
            </Dropdown>
        </div>
    );
};
```

### 2. 项目关注者管理

```javascript
// 项目中的关注者管理
const ProjectFollowerManager = ({ project }) => {
    const handleFollowerUpdate = (thread) => {
        // 更新项目统计
        updateProjectStats(project);
        
        // 发送通知
        notifyTeamMembers(project, 'follower_updated');
    };
    
    return (
        <div class="project-followers">
            <h3>项目关注者</h3>
            <FollowerList
                thread={project.thread}
                onAddFollowers={() => handleFollowerUpdate(project.thread)}
                onFollowerChanged={handleFollowerUpdate}
                dropdown={dropdown}
            />
        </div>
    );
};
```

### 3. 任务关注者管理

```javascript
// 任务中的关注者管理
const TaskFollowerManager = ({ task, onUpdate }) => {
    const handleTaskFollowerChange = (thread) => {
        // 更新任务状态
        task.updateFollowerStatus();
        
        // 触发父组件更新
        onUpdate(task);
        
        // 记录操作日志
        logTaskActivity(task, 'follower_changed');
    };
    
    return (
        <div class="task-follower-section">
            <div class="section-header">
                <span>关注此任务的人员</span>
                <span class="follower-count">{task.followerCount}</span>
            </div>
            <FollowerList
                thread={task.thread}
                onAddFollowers={() => handleTaskFollowerChange(task.thread)}
                onFollowerChanged={handleTaskFollowerChange}
                dropdown={dropdown}
            />
        </div>
    );
};
```

### 4. 批量关注者操作

```javascript
// 批量关注者操作
const BatchFollowerManager = ({ records }) => {
    const handleBatchAddFollowers = () => {
        // 批量添加关注者到多个记录
        records.forEach(record => {
            record.thread.loadMoreFollowers();
        });
        
        // 更新批量操作状态
        updateBatchOperationStatus();
    };
    
    const handleBatchFollowerChanged = (thread) => {
        // 找到对应的记录
        const record = records.find(r => r.thread.id === thread.id);
        if (record) {
            record.refresh();
        }
    };
    
    return (
        <div class="batch-follower-manager">
            <div class="batch-header">
                批量管理 {records.length} 个记录的关注者
            </div>
            {records.map(record => (
                <div key={record.id} class="record-follower-item">
                    <span>{record.name}</span>
                    <FollowerList
                        thread={record.thread}
                        onAddFollowers={handleBatchAddFollowers}
                        onFollowerChanged={handleBatchFollowerChanged}
                        dropdown={dropdown}
                    />
                </div>
            ))}
        </div>
    );
};
```

## 关注者操作

### 1. 关注者邀请

```javascript
// 关注者邀请处理
const inviteFollowers = async (thread, inviteData) => {
    try {
        // 调用邀请向导
        const action = {
            type: "ir.actions.act_window",
            res_model: "mail.wizard.invite",
            view_mode: "form",
            views: [[false, "form"]],
            name: "邀请关注者",
            target: "new",
            context: {
                default_res_model: thread.model,
                default_res_id: thread.id,
                default_partner_ids: inviteData.partnerIds,
                default_channel_ids: inviteData.channelIds,
                dialog_size: "medium",
            },
        };
        
        return await actionService.doAction(action);
    } catch (error) {
        console.error('邀请关注者失败:', error);
        throw error;
    }
};
```

### 2. 关注者权限管理

```javascript
// 关注者权限管理
const manageFollowerPermissions = (follower, permissions) => {
    const subtypeUpdates = {};
    
    // 根据权限设置子类型
    permissions.forEach(permission => {
        switch (permission.type) {
            case 'all_messages':
                subtypeUpdates.subtype_ids = getAllMessageSubtypes();
                break;
            case 'mentions_only':
                subtypeUpdates.subtype_ids = getMentionSubtypes();
                break;
            case 'custom':
                subtypeUpdates.subtype_ids = permission.subtypeIds;
                break;
        }
    });
    
    return updateFollowerSubtypes(follower, subtypeUpdates);
};
```

### 3. 关注者通知设置

```javascript
// 关注者通知设置
const configureFollowerNotifications = (follower, notificationConfig) => {
    const settings = {
        email: notificationConfig.email || false,
        sms: notificationConfig.sms || false,
        push: notificationConfig.push || false,
        frequency: notificationConfig.frequency || 'instant'
    };
    
    return updateFollowerNotificationSettings(follower, settings);
};
```

## 数据管理

### 1. 关注者数据加载

```javascript
// 关注者数据懒加载
const loadFollowersLazily = (thread, options = {}) => {
    const {
        limit = 20,
        offset = 0,
        loadMore = false
    } = options;
    
    if (loadMore) {
        return thread.loadMoreFollowers();
    } else {
        return thread.loadFollowers({ limit, offset });
    }
};

// 关注者数据预加载
const preloadFollowerData = async (threads) => {
    const promises = threads.map(thread => 
        thread.loadFollowers({ limit: 5 }) // 预加载前5个关注者
    );
    
    return Promise.all(promises);
};
```

### 2. 关注者状态同步

```javascript
// 关注者状态同步
const syncFollowerStatus = (thread) => {
    // 同步关注者计数
    const followerCount = thread.followers.length;
    thread.followerCount = followerCount;
    
    // 同步当前用户关注状态
    const currentUser = getCurrentUser();
    thread.isFollowedByCurrentUser = thread.followers.some(
        follower => follower.partner.id === currentUser.partnerId
    );
    
    // 触发状态更新事件
    thread.trigger('followers:synced', { count: followerCount });
};
```

### 3. 关注者缓存管理

```javascript
// 关注者数据缓存
const followerCache = new Map();

const getCachedFollowers = (threadId) => {
    if (followerCache.has(threadId)) {
        const cached = followerCache.get(threadId);
        if (Date.now() - cached.timestamp < 300000) { // 5分钟缓存
            return cached.followers;
        }
    }
    return null;
};

const setCachedFollowers = (threadId, followers) => {
    followerCache.set(threadId, {
        followers,
        timestamp: Date.now()
    });
};
```

## 性能优化

### 1. 虚拟化列表

```javascript
// 大量关注者时的虚拟化渲染
const VirtualizedFollowerList = ({ followers, itemHeight = 50 }) => {
    const [visibleRange, setVisibleRange] = useState({ start: 0, end: 10 });
    
    const visibleFollowers = followers.slice(visibleRange.start, visibleRange.end);
    
    return (
        <div class="virtualized-follower-list">
            {visibleFollowers.map(follower => (
                <FollowerItem key={follower.id} follower={follower} />
            ))}
        </div>
    );
};
```

### 2. 批量操作优化

```javascript
// 批量关注者操作优化
const batchFollowerOperations = {
    add: async (threads, partnerIds) => {
        const operations = threads.map(thread => ({
            model: thread.model,
            id: thread.id,
            partner_ids: partnerIds
        }));
        
        return await orm.call('mail.followers', 'batch_add', [operations]);
    },
    
    remove: async (followerIds) => {
        return await orm.call('mail.followers', 'unlink', [followerIds]);
    }
};
```

## 可访问性

### 1. 键盘导航

```javascript
// 键盘导航支持
const handleKeyDown = (event, follower, actions) => {
    switch (event.key) {
        case 'Enter':
        case ' ':
            event.preventDefault();
            actions.onClickDetails(event, follower);
            break;
        case 'Delete':
            event.preventDefault();
            if (confirm('确定要移除此关注者吗？')) {
                actions.onClickRemove(event, follower);
            }
            break;
        case 'e':
            if (event.ctrlKey || event.metaKey) {
                event.preventDefault();
                actions.onClickEdit(event, follower);
            }
            break;
    }
};
```

### 2. ARIA 标签

```javascript
// 可访问性属性
const getAccessibilityProps = (follower) => {
    return {
        'role': 'listitem',
        'aria-label': `关注者: ${follower.partner.name}`,
        'tabindex': '0'
    };
};
```

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的关注者列表组件
- 统一的关注者操作接口

### 2. 观察者模式 (Observer Pattern)
- 监听关注者变化
- 响应用户操作

### 3. 命令模式 (Command Pattern)
- 封装关注者操作命令
- 支持撤销和重做

## 注意事项

1. **权限控制**: 确保用户有权限管理关注者
2. **数据一致性**: 保持关注者数据的同步
3. **性能考虑**: 大量关注者时的渲染性能
4. **用户体验**: 提供清晰的操作反馈

## 扩展建议

1. **搜索功能**: 添加关注者搜索和过滤
2. **批量操作**: 支持批量添加和移除关注者
3. **通知设置**: 更细粒度的通知设置
4. **关注者分组**: 支持关注者分组管理
5. **移动端优化**: 优化移动设备上的交互体验

该组件为关注者管理提供了完整的用户界面，支持关注者的查看、添加、编辑和移除等操作。

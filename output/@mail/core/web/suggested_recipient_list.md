# Suggested Recipient List - 建议收件人列表

## 概述

`suggested_recipient_list.js` 实现了 Odoo 邮件系统中的建议收件人列表组件，用于在邮件编写界面中显示和管理多个建议收件人。该组件支持收件人列表的展示、展开/收起功能、状态管理等，默认显示前3个建议收件人，用户可以选择显示更多，通过集成单个建议收件人组件来构建完整的建议列表，为用户提供了便捷的批量收件人管理体验。

## 文件信息
- **路径**: `/mail/static/src/core/web/suggested_recipient_list.js`
- **行数**: 39
- **模块**: `@mail/core/web/suggested_recipient_list`

## 依赖关系

```javascript
// UI组件依赖
'@mail/core/web/suggested_recipient'  // 单个建议收件人组件

// 核心依赖
'@odoo/owl'                          // OWL 框架
```

## 组件定义

### SuggestedRecipientsList 类

```javascript
class SuggestedRecipientsList extends Component {
    static template = "mail.SuggestedRecipientsList";
    static components = { SuggestedRecipient };
    static props = ["thread", "className?", "styleString?", "onSuggestedRecipientAdded"];
}
```

**组件特性**:
- 使用自定义模板"mail.SuggestedRecipientsList"
- 集成SuggestedRecipient单个收件人组件
- 支持可选的样式类名和样式字符串

## Props 配置

### Props 详细说明

- **`thread`** (必需):
  - 类型: Thread 模型实例
  - 用途: 提供建议收件人数据的线程对象
  - 功能: 获取线程的建议收件人列表

- **`className`** (可选):
  - 类型: 字符串
  - 用途: 自定义CSS类名
  - 功能: 控制组件的外观样式

- **`styleString`** (可选):
  - 类型: 字符串
  - 用途: 自定义内联样式
  - 功能: 精确控制组件样式

- **`onSuggestedRecipientAdded`** (必需):
  - 类型: 函数
  - 用途: 收件人添加完成后的回调
  - 功能: 通知父组件更新状态

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.state = useState({ showMore: false });
}
```

**初始化内容**:
- 响应式状态管理
- showMore状态控制显示数量

## 核心功能

### 1. 建议收件人获取

```javascript
get suggestedRecipients() {
    if (!this.state.showMore) {
        return this.props.thread.suggestedRecipients.slice(0, 3);
    }
    return this.props.thread.suggestedRecipients;
}
```

**获取逻辑**:
- 默认显示前3个建议收件人
- showMore为true时显示全部收件人
- 通过slice方法控制显示数量

## 使用场景

### 1. 邮件编写界面集成

```javascript
// 邮件编写界面中的建议收件人列表
const MailComposerSuggestedRecipients = {
    setupSuggestedRecipientsList: (composer, thread) => {
        const listContainer = document.createElement('div');
        listContainer.className = 'suggested-recipients-container';
        listContainer.innerHTML = `
            <div class="suggested-recipients-header">
                <h4>建议收件人</h4>
                <span class="suggestion-count">
                    ${thread.suggestedRecipients?.length || 0} 个建议
                </span>
            </div>
            
            <SuggestedRecipientsList
                thread={thread}
                className="composer-suggested-list"
                onSuggestedRecipientAdded={handleRecipientAdded}
            />
            
            <div class="suggested-recipients-footer">
                <small class="text-muted">
                    基于历史记录和相关联系人的智能建议
                </small>
            </div>
        `;
        
        return listContainer;
    },
    
    handleRecipientAdded: (thread) => {
        // 刷新建议列表
        MailComposerSuggestedRecipients.refreshSuggestions(thread);
        
        // 更新收件人计数
        MailComposerSuggestedRecipients.updateRecipientCount(thread);
        
        // 记录添加操作
        MailComposerSuggestedRecipients.logRecipientAddition(thread);
    },
    
    refreshSuggestions: async (thread) => {
        try {
            // 重新获取建议收件人
            await thread.refreshSuggestedRecipients();
            
            // 更新UI显示
            MailComposerSuggestedRecipients.updateSuggestionsDisplay(thread);
        } catch (error) {
            console.error('刷新建议收件人失败:', error);
        }
    },
    
    updateRecipientCount: (thread) => {
        const countElement = document.querySelector('.suggestion-count');
        if (countElement) {
            const count = thread.suggestedRecipients?.length || 0;
            countElement.textContent = `${count} 个建议`;
        }
    },
    
    logRecipientAddition: (thread) => {
        const logEntry = {
            action: 'suggested_recipient_added',
            threadId: thread.id,
            threadModel: thread.model,
            timestamp: Date.now(),
            userId: getCurrentUser().id
        };
        
        // 保存到本地存储
        const actionHistory = JSON.parse(localStorage.getItem('recipient_action_history') || '[]');
        actionHistory.unshift(logEntry);
        
        if (actionHistory.length > 1000) {
            actionHistory.pop();
        }
        
        localStorage.setItem('recipient_action_history', JSON.stringify(actionHistory));
    }
};
```

### 2. 建议收件人列表增强

```javascript
// 建议收件人列表增强功能
const SuggestedRecipientsEnhancer = {
    enhanceList: (listComponent) => {
        // 添加批量选择功能
        SuggestedRecipientsEnhancer.addBatchSelection(listComponent);
        
        // 添加排序功能
        SuggestedRecipientsEnhancer.addSortingOptions(listComponent);
        
        // 添加过滤功能
        SuggestedRecipientsEnhancer.addFilterOptions(listComponent);
        
        // 添加统计信息
        SuggestedRecipientsEnhancer.addStatistics(listComponent);
    },
    
    addBatchSelection: (listComponent) => {
        const batchContainer = document.createElement('div');
        batchContainer.className = 'batch-selection-container';
        batchContainer.innerHTML = `
            <div class="batch-header">
                <label class="batch-select-all">
                    <input type="checkbox" class="select-all-checkbox"> 全选
                </label>
                <span class="selected-count">已选择 0 个</span>
            </div>
            
            <div class="batch-actions">
                <button class="btn btn-sm btn-primary add-selected-btn" disabled>
                    添加选中的收件人
                </button>
                <button class="btn btn-sm btn-secondary dismiss-selected-btn" disabled>
                    忽略选中的建议
                </button>
            </div>
        `;
        
        SuggestedRecipientsEnhancer.bindBatchEvents(batchContainer, listComponent);
        listComponent.el.prepend(batchContainer);
    },
    
    bindBatchEvents: (container, listComponent) => {
        const selectAllCheckbox = container.querySelector('.select-all-checkbox');
        const addSelectedBtn = container.querySelector('.add-selected-btn');
        const dismissSelectedBtn = container.querySelector('.dismiss-selected-btn');
        
        selectAllCheckbox.addEventListener('change', (event) => {
            SuggestedRecipientsEnhancer.toggleSelectAll(event.target.checked, listComponent);
        });
        
        addSelectedBtn.addEventListener('click', () => {
            SuggestedRecipientsEnhancer.addSelectedRecipients(listComponent);
        });
        
        dismissSelectedBtn.addEventListener('click', () => {
            SuggestedRecipientsEnhancer.dismissSelectedSuggestions(listComponent);
        });
    },
    
    toggleSelectAll: (selectAll, listComponent) => {
        const checkboxes = listComponent.el.querySelectorAll('.recipient-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll;
        });
        
        SuggestedRecipientsEnhancer.updateBatchActions(listComponent);
    },
    
    updateBatchActions: (listComponent) => {
        const selectedCheckboxes = listComponent.el.querySelectorAll('.recipient-checkbox:checked');
        const selectedCount = selectedCheckboxes.length;
        
        const selectedCountSpan = listComponent.el.querySelector('.selected-count');
        const addSelectedBtn = listComponent.el.querySelector('.add-selected-btn');
        const dismissSelectedBtn = listComponent.el.querySelector('.dismiss-selected-btn');
        
        selectedCountSpan.textContent = `已选择 ${selectedCount} 个`;
        addSelectedBtn.disabled = selectedCount === 0;
        dismissSelectedBtn.disabled = selectedCount === 0;
    },
    
    addSelectedRecipients: async (listComponent) => {
        const selectedCheckboxes = listComponent.el.querySelectorAll('.recipient-checkbox:checked');
        const selectedRecipients = [];
        
        selectedCheckboxes.forEach(checkbox => {
            const recipientId = checkbox.dataset.recipientId;
            const recipient = listComponent.props.thread.suggestedRecipients.find(
                r => r.id === recipientId
            );
            if (recipient) {
                selectedRecipients.push(recipient);
            }
        });
        
        try {
            for (const recipient of selectedRecipients) {
                await SuggestedRecipientsEnhancer.addSingleRecipient(recipient, listComponent);
            }
            
            showNotification(`已添加 ${selectedRecipients.length} 个收件人`, 'success');
            
            // 清空选择
            SuggestedRecipientsEnhancer.clearSelection(listComponent);
        } catch (error) {
            console.error('批量添加收件人失败:', error);
            showNotification('批量添加失败，请重试', 'error');
        }
    },
    
    addSingleRecipient: async (recipient, listComponent) => {
        // 检查是否需要创建合作伙伴
        if (!recipient.persona) {
            await SuggestedRecipientsEnhancer.createPartnerForRecipient(recipient);
        }
        
        // 添加到收件人列表
        await listComponent.props.onSuggestedRecipientAdded(listComponent.props.thread);
    },
    
    createPartnerForRecipient: async (recipient) => {
        const partnerData = {
            name: recipient.name,
            email: recipient.email,
            ...recipient.create_values
        };
        
        const partnerId = await orm.create('res.partner', [partnerData]);
        recipient.persona = { id: partnerId[0] };
    },
    
    dismissSelectedSuggestions: (listComponent) => {
        const selectedCheckboxes = listComponent.el.querySelectorAll('.recipient-checkbox:checked');
        
        selectedCheckboxes.forEach(checkbox => {
            const recipientElement = checkbox.closest('.suggested-recipient-item');
            if (recipientElement) {
                recipientElement.style.display = 'none';
            }
        });
        
        showNotification('已忽略选中的建议', 'info');
        SuggestedRecipientsEnhancer.clearSelection(listComponent);
    },
    
    clearSelection: (listComponent) => {
        const checkboxes = listComponent.el.querySelectorAll('.recipient-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        
        SuggestedRecipientsEnhancer.updateBatchActions(listComponent);
    },
    
    addSortingOptions: (listComponent) => {
        const sortContainer = document.createElement('div');
        sortContainer.className = 'sort-options-container';
        sortContainer.innerHTML = `
            <div class="sort-controls">
                <label>排序:</label>
                <select class="sort-select">
                    <option value="relevance">相关性</option>
                    <option value="name_asc">姓名 (A-Z)</option>
                    <option value="name_desc">姓名 (Z-A)</option>
                    <option value="email_asc">邮箱 (A-Z)</option>
                    <option value="email_desc">邮箱 (Z-A)</option>
                    <option value="reason">建议原因</option>
                </select>
            </div>
        `;
        
        const sortSelect = sortContainer.querySelector('.sort-select');
        sortSelect.addEventListener('change', () => {
            SuggestedRecipientsEnhancer.sortRecipients(listComponent, sortSelect.value);
        });
        
        listComponent.el.appendChild(sortContainer);
    },
    
    sortRecipients: (listComponent, sortOption) => {
        const recipients = [...listComponent.props.thread.suggestedRecipients];
        
        recipients.sort((a, b) => {
            switch (sortOption) {
                case 'name_asc':
                    return a.name.localeCompare(b.name);
                case 'name_desc':
                    return b.name.localeCompare(a.name);
                case 'email_asc':
                    return (a.email || '').localeCompare(b.email || '');
                case 'email_desc':
                    return (b.email || '').localeCompare(a.email || '');
                case 'reason':
                    return a.reason.localeCompare(b.reason);
                case 'relevance':
                default:
                    return (b.priority || 0) - (a.priority || 0);
            }
        });
        
        // 更新显示顺序
        SuggestedRecipientsEnhancer.updateDisplayOrder(listComponent, recipients);
    },
    
    updateDisplayOrder: (listComponent, sortedRecipients) => {
        // 重新排列DOM元素
        const container = listComponent.el.querySelector('.suggested-recipients-list');
        if (container) {
            sortedRecipients.forEach(recipient => {
                const element = container.querySelector(`[data-recipient-id="${recipient.id}"]`);
                if (element) {
                    container.appendChild(element);
                }
            });
        }
    },
    
    addFilterOptions: (listComponent) => {
        const filterContainer = document.createElement('div');
        filterContainer.className = 'filter-options-container';
        filterContainer.innerHTML = `
            <div class="filter-controls">
                <label>过滤:</label>
                <select class="filter-select" data-filter="type">
                    <option value="">所有类型</option>
                    <option value="with_partner">有合作伙伴</option>
                    <option value="without_partner">无合作伙伴</option>
                </select>
                
                <select class="filter-select" data-filter="reason">
                    <option value="">所有原因</option>
                    <option value="follower">关注者</option>
                    <option value="participant">参与者</option>
                    <option value="related">相关联系人</option>
                </select>
                
                <button class="filter-reset-btn">重置</button>
            </div>
        `;
        
        filterContainer.querySelectorAll('.filter-select').forEach(select => {
            select.addEventListener('change', () => {
                SuggestedRecipientsEnhancer.applyFilters(listComponent);
            });
        });
        
        filterContainer.querySelector('.filter-reset-btn').addEventListener('click', () => {
            filterContainer.querySelectorAll('.filter-select').forEach(select => {
                select.value = '';
            });
            SuggestedRecipientsEnhancer.applyFilters(listComponent);
        });
        
        listComponent.el.appendChild(filterContainer);
    },
    
    applyFilters: (listComponent) => {
        const filters = {};
        listComponent.el.querySelectorAll('.filter-select').forEach(select => {
            const filterType = select.dataset.filter;
            const filterValue = select.value;
            if (filterValue) {
                filters[filterType] = filterValue;
            }
        });
        
        const recipientElements = listComponent.el.querySelectorAll('.suggested-recipient-item');
        
        recipientElements.forEach(element => {
            const recipientId = element.dataset.recipientId;
            const recipient = listComponent.props.thread.suggestedRecipients.find(
                r => r.id === recipientId
            );
            
            let shouldShow = true;
            
            if (filters.type) {
                if (filters.type === 'with_partner' && !recipient.persona) {
                    shouldShow = false;
                } else if (filters.type === 'without_partner' && recipient.persona) {
                    shouldShow = false;
                }
            }
            
            if (filters.reason && recipient.reason !== filters.reason) {
                shouldShow = false;
            }
            
            element.style.display = shouldShow ? 'block' : 'none';
        });
    },
    
    addStatistics: (listComponent) => {
        const statsContainer = document.createElement('div');
        statsContainer.className = 'suggestions-statistics';
        statsContainer.innerHTML = `
            <div class="stats-header">
                <h5>建议统计</h5>
            </div>
            <div class="stats-content">
                <div class="stat-item">
                    <span class="stat-label">总建议数:</span>
                    <span class="stat-value" id="total-suggestions">-</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">有合作伙伴:</span>
                    <span class="stat-value" id="with-partner">-</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">需要创建:</span>
                    <span class="stat-value" id="need-creation">-</span>
                </div>
            </div>
        `;
        
        SuggestedRecipientsEnhancer.updateStatistics(listComponent, statsContainer);
        listComponent.el.appendChild(statsContainer);
    },
    
    updateStatistics: (listComponent, container) => {
        const recipients = listComponent.props.thread.suggestedRecipients || [];
        
        const total = recipients.length;
        const withPartner = recipients.filter(r => r.persona).length;
        const needCreation = total - withPartner;
        
        container.querySelector('#total-suggestions').textContent = total;
        container.querySelector('#with-partner').textContent = withPartner;
        container.querySelector('#need-creation').textContent = needCreation;
    }
};
```

### 3. 展开/收起功能增强

```javascript
// 展开/收起功能增强
const ShowMoreEnhancer = {
    enhanceShowMore: (listComponent) => {
        const showMoreContainer = document.createElement('div');
        showMoreContainer.className = 'show-more-container';
        
        ShowMoreEnhancer.updateShowMoreButton(listComponent, showMoreContainer);
        listComponent.el.appendChild(showMoreContainer);
        
        // 监听状态变化
        listComponent.state.addEventListener('change', () => {
            ShowMoreEnhancer.updateShowMoreButton(listComponent, showMoreContainer);
        });
    },
    
    updateShowMoreButton: (listComponent, container) => {
        const totalCount = listComponent.props.thread.suggestedRecipients?.length || 0;
        const displayedCount = listComponent.suggestedRecipients.length;
        const hasMore = totalCount > displayedCount;
        
        if (hasMore || listComponent.state.showMore) {
            container.innerHTML = `
                <button class="show-more-btn btn btn-link">
                    ${listComponent.state.showMore 
                        ? `收起 (显示前3个)` 
                        : `显示更多 (还有 ${totalCount - displayedCount} 个)`
                    }
                    <i class="fa fa-chevron-${listComponent.state.showMore ? 'up' : 'down'}"></i>
                </button>
            `;
            
            const showMoreBtn = container.querySelector('.show-more-btn');
            showMoreBtn.addEventListener('click', () => {
                listComponent.state.showMore = !listComponent.state.showMore;
                
                // 记录展开/收起操作
                ShowMoreEnhancer.logShowMoreAction(listComponent.state.showMore, totalCount);
            });
        } else {
            container.innerHTML = '';
        }
    },
    
    logShowMoreAction: (showMore, totalCount) => {
        const action = {
            type: 'show_more_toggle',
            showMore,
            totalCount,
            timestamp: Date.now()
        };
        
        // 发送到分析服务
        sendAnalytics('suggested_recipients_interaction', action);
    }
};
```

## 性能优化

### 1. 虚拟滚动

```javascript
// 虚拟滚动优化
const VirtualScrollOptimizer = {
    setupVirtualScroll: (listComponent) => {
        if (listComponent.props.thread.suggestedRecipients.length > 50) {
            VirtualScrollOptimizer.enableVirtualScroll(listComponent);
        }
    },
    
    enableVirtualScroll: (listComponent) => {
        const container = listComponent.el.querySelector('.suggested-recipients-list');
        const itemHeight = 60; // 每个建议收件人项目的高度
        const containerHeight = 300; // 容器高度
        
        VirtualScrollOptimizer.createVirtualScrollContainer(container, itemHeight, containerHeight);
    }
};
```

## 设计模式

### 1. 组合模式 (Composite Pattern)
- 组合多个建议收件人组件
- 统一的列表管理接口

### 2. 状态模式 (State Pattern)
- 展开/收起状态管理
- 不同状态下的不同行为

### 3. 观察者模式 (Observer Pattern)
- 监听收件人添加事件
- 响应状态变化

## 注意事项

1. **性能考虑**: 大量建议收件人时的性能优化
2. **用户体验**: 提供清晰的操作反馈
3. **数据一致性**: 确保建议收件人数据的准确性
4. **响应式设计**: 适配不同屏幕尺寸

## 扩展建议

1. **智能排序**: 基于用户行为的智能排序
2. **个性化建议**: 基于用户偏好的个性化建议
3. **批量操作**: 支持更多的批量操作功能
4. **历史记录**: 记录和分析建议收件人的使用情况
5. **集成外部**: 与外部联系人系统集成

该组件为邮件系统提供了完整的建议收件人列表管理功能，是智能邮件编写的重要组成部分。

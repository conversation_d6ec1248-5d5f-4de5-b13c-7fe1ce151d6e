# Dialog Patch - 对话框补丁

## 概述

`dialog_patch.js` 实现了 Odoo 邮件系统中对话框组件的Web环境补丁，用于修改对话框的Escape键行为。该补丁专门针对邮件编写对话框进行了特殊处理，防止用户在编写邮件时意外按下Escape键关闭对话框，避免邮件内容丢失，提供更好的用户体验和数据保护，是邮件编写功能的重要安全保障。

## 文件信息
- **路径**: `/mail/static/src/core/web/dialog_patch.js`
- **行数**: 24
- **模块**: `@mail/core/web/dialog_patch`

## 依赖关系

```javascript
// 核心依赖
'@web/core/dialog/dialog'  // 基础对话框组件
'@web/core/utils/patch'    // 补丁工具
```

## 补丁实现

### 对话框原型补丁

```javascript
patch(Dialog.prototype, {
    /**
     * @override
     */
    onEscape() {
        if (this.data.model === "mail.compose.message") {
            return;
        }
        super.onEscape();
    },
});
```

## 核心功能

### 1. Escape键行为重写

```javascript
onEscape() {
    if (this.data.model === "mail.compose.message") {
        return;
    }
    super.onEscape();
}
```

**行为逻辑**:
- 检查对话框的数据模型是否为"mail.compose.message"
- 如果是邮件编写对话框，则忽略Escape键，直接返回
- 其他对话框正常执行父类的Escape键处理逻辑

**保护机制**:
- 防止用户在编写邮件时意外关闭对话框
- 避免邮件内容丢失
- 提供更安全的用户体验

## 使用场景

### 1. 邮件编写保护

```javascript
// 邮件编写对话框保护机制
const MailComposeProtection = {
    isMailComposeDialog: (dialog) => {
        return dialog.data && dialog.data.model === "mail.compose.message";
    },
    
    handleEscapeKey: (dialog, event) => {
        if (MailComposeProtection.isMailComposeDialog(dialog)) {
            // 显示确认对话框
            const hasContent = MailComposeProtection.hasUnsavedContent(dialog);
            
            if (hasContent) {
                MailComposeProtection.showSaveConfirmation(dialog);
            } else {
                // 没有内容时可以直接关闭
                dialog.close();
            }
            
            event.preventDefault();
            return false;
        }
        
        // 其他对话框正常处理
        return true;
    },
    
    hasUnsavedContent: (dialog) => {
        const composer = dialog.component;
        if (!composer) return false;
        
        // 检查是否有未保存的内容
        return composer.hasSubject() || 
               composer.hasBody() || 
               composer.hasAttachments() ||
               composer.hasRecipients();
    },
    
    showSaveConfirmation: (dialog) => {
        const confirmation = {
            title: '确认关闭',
            message: '您有未保存的邮件内容，确定要关闭吗？',
            buttons: [
                {
                    text: '保存草稿',
                    class: 'btn-primary',
                    action: () => {
                        MailComposeProtection.saveDraft(dialog);
                        dialog.close();
                    }
                },
                {
                    text: '不保存',
                    class: 'btn-secondary',
                    action: () => {
                        dialog.close();
                    }
                },
                {
                    text: '取消',
                    class: 'btn-default',
                    action: () => {
                        // 不做任何操作，继续编辑
                    }
                }
            ]
        };
        
        showConfirmationDialog(confirmation);
    },
    
    saveDraft: (dialog) => {
        const composer = dialog.component;
        if (composer && composer.saveDraft) {
            composer.saveDraft();
        }
    }
};
```

### 2. 对话框类型检测

```javascript
// 对话框类型检测和处理
const DialogTypeHandler = {
    getDialogType: (dialog) => {
        if (!dialog.data) return 'unknown';
        
        const model = dialog.data.model;
        const context = dialog.data.context || {};
        
        switch (model) {
            case 'mail.compose.message':
                return 'mail_compose';
            case 'mail.activity':
                return 'activity';
            case 'mail.followers':
                return 'followers';
            default:
                return 'generic';
        }
    },
    
    getEscapePolicy: (dialogType) => {
        const policies = {
            'mail_compose': 'prevent', // 阻止关闭
            'activity': 'confirm',     // 确认关闭
            'followers': 'allow',      // 允许关闭
            'generic': 'allow'         // 默认允许
        };
        
        return policies[dialogType] || 'allow';
    },
    
    handleEscapeByPolicy: (dialog, policy) => {
        switch (policy) {
            case 'prevent':
                // 完全阻止关闭
                return false;
                
            case 'confirm':
                // 显示确认对话框
                DialogTypeHandler.showCloseConfirmation(dialog);
                return false;
                
            case 'allow':
                // 允许正常关闭
                return true;
                
            default:
                return true;
        }
    },
    
    showCloseConfirmation: (dialog) => {
        const dialogType = DialogTypeHandler.getDialogType(dialog);
        const messages = {
            'activity': '确定要关闭活动对话框吗？未保存的更改将丢失。',
            'followers': '确定要关闭关注者对话框吗？',
            'generic': '确定要关闭此对话框吗？'
        };
        
        const message = messages[dialogType] || messages.generic;
        
        if (confirm(message)) {
            dialog.close();
        }
    }
};
```

### 3. 键盘事件增强

```javascript
// 键盘事件增强处理
const KeyboardEventEnhancer = {
    enhanceDialog: (dialog) => {
        const originalOnEscape = dialog.onEscape.bind(dialog);
        
        dialog.onEscape = function(event) {
            // 记录Escape键使用
            KeyboardEventEnhancer.logEscapeUsage(dialog);
            
            // 检查是否有自定义处理
            const customHandler = KeyboardEventEnhancer.getCustomHandler(dialog);
            if (customHandler) {
                const handled = customHandler(dialog, event);
                if (handled) {
                    return;
                }
            }
            
            // 执行原始处理
            return originalOnEscape(event);
        };
        
        // 添加其他键盘快捷键
        KeyboardEventEnhancer.addShortcuts(dialog);
    },
    
    getCustomHandler: (dialog) => {
        const dialogType = DialogTypeHandler.getDialogType(dialog);
        
        const handlers = {
            'mail_compose': MailComposeProtection.handleEscapeKey,
            'activity': ActivityDialogHandler.handleEscapeKey,
            'followers': FollowerDialogHandler.handleEscapeKey
        };
        
        return handlers[dialogType];
    },
    
    addShortcuts: (dialog) => {
        const shortcuts = {
            'ctrl+s': () => KeyboardEventEnhancer.handleSave(dialog),
            'ctrl+enter': () => KeyboardEventEnhancer.handleSubmit(dialog),
            'ctrl+w': () => KeyboardEventEnhancer.handleClose(dialog)
        };
        
        Object.entries(shortcuts).forEach(([key, handler]) => {
            dialog.addKeyboardShortcut(key, handler);
        });
    },
    
    handleSave: (dialog) => {
        if (dialog.component && dialog.component.save) {
            dialog.component.save();
        }
    },
    
    handleSubmit: (dialog) => {
        if (dialog.component && dialog.component.submit) {
            dialog.component.submit();
        }
    },
    
    handleClose: (dialog) => {
        // 使用自定义关闭逻辑
        dialog.onEscape();
    },
    
    logEscapeUsage: (dialog) => {
        const dialogType = DialogTypeHandler.getDialogType(dialog);
        
        // 记录使用统计
        logUserAction('dialog_escape_key', {
            dialogType,
            timestamp: Date.now(),
            prevented: dialogType === 'mail_compose'
        });
    }
};
```

### 4. 邮件编写特殊处理

```javascript
// 邮件编写对话框特殊处理
const MailComposeDialogHandler = {
    setupProtection: (dialog) => {
        if (!MailComposeDialogHandler.isMailComposeDialog(dialog)) {
            return;
        }
        
        // 添加关闭前检查
        MailComposeDialogHandler.addCloseGuard(dialog);
        
        // 添加自动保存
        MailComposeDialogHandler.setupAutoSave(dialog);
        
        // 添加警告提示
        MailComposeDialogHandler.addWarningIndicator(dialog);
    },
    
    isMailComposeDialog: (dialog) => {
        return dialog.data && dialog.data.model === "mail.compose.message";
    },
    
    addCloseGuard: (dialog) => {
        const originalClose = dialog.close.bind(dialog);
        
        dialog.close = function(options = {}) {
            if (options.force) {
                return originalClose(options);
            }
            
            const hasUnsaved = MailComposeDialogHandler.hasUnsavedChanges(dialog);
            if (hasUnsaved) {
                MailComposeDialogHandler.confirmClose(dialog, originalClose);
            } else {
                originalClose(options);
            }
        };
    },
    
    hasUnsavedChanges: (dialog) => {
        const composer = dialog.component;
        if (!composer) return false;
        
        // 检查各种未保存的更改
        return composer.isDirty && composer.isDirty();
    },
    
    confirmClose: (dialog, closeCallback) => {
        const modal = createConfirmModal({
            title: '确认关闭',
            message: '您有未保存的邮件内容，确定要关闭吗？',
            confirmText: '保存并关闭',
            cancelText: '不保存关闭',
            onConfirm: () => {
                MailComposeDialogHandler.saveAndClose(dialog, closeCallback);
            },
            onCancel: () => {
                closeCallback({ force: true });
            }
        });
        
        modal.show();
    },
    
    saveAndClose: async (dialog, closeCallback) => {
        try {
            const composer = dialog.component;
            if (composer && composer.saveDraft) {
                await composer.saveDraft();
            }
            closeCallback({ saved: true });
        } catch (error) {
            console.error('保存草稿失败:', error);
            showNotification('保存失败，是否仍要关闭？', 'error');
        }
    },
    
    setupAutoSave: (dialog) => {
        const composer = dialog.component;
        if (!composer) return;
        
        let autoSaveTimer;
        
        const autoSave = () => {
            if (composer.saveDraft && MailComposeDialogHandler.hasUnsavedChanges(dialog)) {
                composer.saveDraft();
            }
        };
        
        // 每30秒自动保存
        autoSaveTimer = setInterval(autoSave, 30000);
        
        // 对话框关闭时清理定时器
        const originalClose = dialog.close.bind(dialog);
        dialog.close = function(options) {
            clearInterval(autoSaveTimer);
            return originalClose(options);
        };
    },
    
    addWarningIndicator: (dialog) => {
        const indicator = document.createElement('div');
        indicator.className = 'escape-disabled-indicator';
        indicator.innerHTML = '⚠️ 按Esc键已禁用以防止意外关闭';
        indicator.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
        `;
        
        dialog.el.appendChild(indicator);
        
        // 3秒后自动隐藏
        setTimeout(() => {
            if (indicator.parentNode) {
                indicator.remove();
            }
        }, 3000);
    }
};
```

### 5. 全局对话框管理

```javascript
// 全局对话框管理
const GlobalDialogManager = {
    dialogs: new Map(),
    
    registerDialog: (dialog) => {
        const id = dialog.id || generateDialogId();
        GlobalDialogManager.dialogs.set(id, dialog);
        
        // 应用补丁
        GlobalDialogManager.applyPatches(dialog);
        
        return id;
    },
    
    unregisterDialog: (id) => {
        GlobalDialogManager.dialogs.delete(id);
    },
    
    applyPatches: (dialog) => {
        // 应用Escape键补丁
        KeyboardEventEnhancer.enhanceDialog(dialog);
        
        // 应用邮件编写保护
        MailComposeDialogHandler.setupProtection(dialog);
        
        // 添加全局事件监听
        GlobalDialogManager.addGlobalListeners(dialog);
    },
    
    addGlobalListeners: (dialog) => {
        // 监听窗口关闭事件
        window.addEventListener('beforeunload', (event) => {
            if (MailComposeDialogHandler.isMailComposeDialog(dialog) &&
                MailComposeDialogHandler.hasUnsavedChanges(dialog)) {
                event.preventDefault();
                event.returnValue = '您有未保存的邮件内容';
                return '您有未保存的邮件内容';
            }
        });
        
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && MailComposeDialogHandler.isMailComposeDialog(dialog)) {
                // 页面隐藏时自动保存
                MailComposeDialogHandler.saveAndClose(dialog, () => {});
            }
        });
    },
    
    closeAllDialogs: (force = false) => {
        GlobalDialogManager.dialogs.forEach(dialog => {
            dialog.close({ force });
        });
    },
    
    getActiveDialogs: () => {
        return Array.from(GlobalDialogManager.dialogs.values());
    },
    
    getMailComposeDialogs: () => {
        return GlobalDialogManager.getActiveDialogs().filter(
            dialog => MailComposeDialogHandler.isMailComposeDialog(dialog)
        );
    }
};
```

## 安全机制

### 1. 数据保护

```javascript
// 数据保护机制
const DataProtection = {
    protectUnsavedData: (dialog) => {
        if (DataProtection.hasUnsavedData(dialog)) {
            DataProtection.enableProtection(dialog);
        }
    },
    
    hasUnsavedData: (dialog) => {
        const component = dialog.component;
        return component && component.isDirty && component.isDirty();
    },
    
    enableProtection: (dialog) => {
        // 禁用Escape键
        dialog._escapeDisabled = true;
        
        // 添加视觉指示
        DataProtection.addProtectionIndicator(dialog);
        
        // 监听数据变化
        DataProtection.watchDataChanges(dialog);
    },
    
    disableProtection: (dialog) => {
        dialog._escapeDisabled = false;
        DataProtection.removeProtectionIndicator(dialog);
    }
};
```

## 性能优化

### 1. 事件处理优化

```javascript
// 事件处理优化
const EventOptimizer = {
    debounceEscape: debounce((dialog) => {
        // 防抖处理Escape键
        dialog.onEscape();
    }, 100),
    
    throttleKeydown: throttle((event) => {
        // 节流处理键盘事件
        handleKeyboardEvent(event);
    }, 50)
};
```

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 非侵入式功能修改
- 保持原有组件的完整性

### 2. 策略模式 (Strategy Pattern)
- 不同对话框类型的不同处理策略
- 可配置的Escape键行为

### 3. 保护代理模式 (Protection Proxy Pattern)
- 为邮件编写对话框提供额外保护
- 控制对原始功能的访问

## 注意事项

1. **用户体验**: 平衡安全性和易用性
2. **数据安全**: 防止意外的数据丢失
3. **性能考虑**: 避免过度的事件监听
4. **兼容性**: 确保与其他对话框功能的兼容

## 扩展建议

1. **更多保护**: 扩展到其他需要保护的对话框类型
2. **自定义配置**: 允许用户配置Escape键行为
3. **智能检测**: 智能检测是否有重要数据需要保护
4. **恢复机制**: 提供数据恢复和撤销功能
5. **多语言**: 支持多语言的提示信息

该补丁为邮件编写提供了重要的数据保护功能，防止用户意外丢失邮件内容。

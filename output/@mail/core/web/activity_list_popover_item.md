# Activity List Popover Item - 活动列表弹出框项目

## 概述

`activity_list_popover_item.js` 实现了 Odoo 邮件系统中的活动列表弹出框项目组件，用于在活动列表弹出框中显示单个活动项目。该组件提供了活动的详细信息展示、状态管理、文件上传、标记完成、编辑删除等功能，支持不同类型活动的特殊处理，是活动列表弹出框的核心子组件，为用户提供了完整的单个活动操作体验。

## 文件信息
- **路径**: `/mail/static/src/core/web/activity_list_popover_item.js`
- **行数**: 123
- **模块**: `@mail/core/web/activity_list_popover_item`

## 依赖关系

```javascript
// UI组件依赖
'@mail/core/web/activity_mail_template'     // 活动邮件模板
'@mail/core/web/activity_markasdone_popover' // 标记完成弹出框

// 工具和钩子依赖
'@mail/core/common/attachment_uploader_hook' // 附件上传钩子
'@mail/utils/common/dates'                  // 日期工具
'@odoo/owl'                                // OWL 框架
'@web/core/l10n/translation'               // 国际化
'@web/core/utils/urls'                     // URL工具
'@web/views/fields/file_handler'           // 文件处理器
```

## 组件定义

### ActivityListPopoverItem 类

```javascript
class ActivityListPopoverItem extends Component {
    static components = { ActivityMailTemplate, ActivityMarkAsDone, FileUploader };
    static props = [
        "activity",                        // 活动对象
        "onActivityChanged?",              // 活动变化回调（可选）
        "onClickDoneAndScheduleNext?",     // 完成并安排下一个回调（可选）
        "onClickEditActivityButton?",      // 编辑活动按钮回调（可选）
    ];
    static template = "mail.ActivityListPopoverItem";
}
```

## Props 配置

### Props 详细说明

- **`activity`** (必需):
  - 类型: Activity 模型实例
  - 用途: 要显示的活动对象
  - 功能: 提供活动的所有数据和方法

- **`onActivityChanged`** (可选):
  - 类型: 函数
  - 用途: 活动发生变化时的回调
  - 功能: 通知父组件更新相关数据

- **`onClickDoneAndScheduleNext`** (可选):
  - 类型: 函数
  - 用途: 完成当前活动并安排下一个活动的回调
  - 功能: 支持连续活动处理

- **`onClickEditActivityButton`** (可选):
  - 类型: 函数
  - 用途: 编辑活动按钮点击回调
  - 功能: 自定义编辑行为

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.state = useState({ hasMarkDoneView: false });
    
    if (this.props.activity.activity_category === "upload_file") {
        this.attachmentUploader = useAttachmentUploader(
            this.env.services["mail.store"].Thread.insert({
                model: this.props.activity.res_model,
                id: this.props.activity.res_id,
            })
        );
    }
    
    this.closeMarkAsDone = this.closeMarkAsDone.bind(this);
}
```

**初始化内容**:
- 标记完成视图的状态管理
- 文件上传类型活动的附件上传器
- 标记完成关闭方法绑定

## 核心功能

### 1. 延迟标签计算

```javascript
get delayLabel() {
    const diff = computeDelay(this.props.activity.date_deadline);
    if (diff === 0) {
        return _t("Today");
    } else if (diff === -1) {
        return _t("Yesterday");
    } else if (diff < 0) {
        return _t("%s days overdue", Math.round(Math.abs(diff)));
    } else if (diff === 1) {
        return _t("Tomorrow");
    } else {
        return _t("Due in %s days", Math.round(Math.abs(diff)));
    }
}
```

**延迟标签逻辑**:
- **今天**: 显示"Today"
- **昨天**: 显示"Yesterday"
- **逾期**: 显示"X days overdue"
- **明天**: 显示"Tomorrow"
- **未来**: 显示"Due in X days"

### 2. 按钮可见性控制

```javascript
get hasCancelButton() {
    const activity = this.props.activity;
    return activity.state !== "done" && activity.can_write;
}

get hasEditButton() {
    const activity = this.props.activity;
    return activity.state !== "done" && activity.can_write;
}

get hasFileUploader() {
    const activity = this.props.activity;
    return activity.state !== "done" && activity.activity_category === "upload_file";
}

get hasMarkDoneButton() {
    return this.props.activity.state !== "done" && !this.hasFileUploader;
}
```

**按钮显示规则**:
- **取消按钮**: 未完成且可写入
- **编辑按钮**: 未完成且可写入
- **文件上传器**: 未完成且为文件上传类型
- **标记完成按钮**: 未完成且非文件上传类型

### 3. 标记完成操作

```javascript
onClickMarkAsDone() {
    this.state.hasMarkDoneView = !this.state.hasMarkDoneView;
}

closeMarkAsDone() {
    this.state.hasMarkDoneView = false;
}
```

**标记完成流程**:
- 切换标记完成视图的显示状态
- 提供关闭标记完成视图的方法

### 4. 编辑活动

```javascript
onClickEditActivityButton() {
    this.props.onClickEditActivityButton();
    this.props.activity.edit().then(() => this.props.onActivityChanged?.());
}
```

**编辑流程**:
1. 调用父组件的编辑回调
2. 执行活动的编辑方法
3. 编辑完成后触发活动变化回调

### 5. 文件上传处理

```javascript
async onFileUploaded(data) {
    const { id: attachmentId } = await this.attachmentUploader.uploadData(data, {
        activity: this.props.activity,
    });
    await this.props.activity.markAsDone([attachmentId]);
    this.props.onActivityChanged?.();
}
```

**文件上传流程**:
1. 上传文件数据并获取附件ID
2. 使用附件ID标记活动完成
3. 触发活动变化回调

### 6. 活动删除

```javascript
unlink() {
    this.props.activity.remove();
    this.env.services.orm
        .unlink("mail.activity", [this.props.activity.id])
        .then(() => this.props.onActivityChanged?.());
}
```

**删除流程**:
1. 从本地存储移除活动
2. 调用ORM服务删除服务器记录
3. 删除完成后触发活动变化回调

### 7. 负责人头像

```javascript
get activityAssigneeAvatar() {
    return url("/web/image", {
        field: "avatar_128",
        id: this.props.activity.user_id[0],
        model: "res.users",
    });
}
```

**头像URL生成**:
- 使用128x128尺寸的头像
- 基于活动负责人的用户ID
- 返回完整的头像URL

## 使用场景

### 1. 标准活动项目

```javascript
// 在活动列表中显示标准活动项目
const StandardActivityItem = ({ activity, onUpdate }) => {
    const handleActivityChanged = () => {
        // 更新活动列表
        onUpdate();
        
        // 刷新相关数据
        refreshRelatedData();
    };
    
    return (
        <ActivityListPopoverItem
            activity={activity}
            onActivityChanged={handleActivityChanged}
        />
    );
};
```

### 2. 文件上传活动项目

```javascript
// 文件上传类型的活动项目
const FileUploadActivityItem = ({ activity, onFileUploaded }) => {
    const handleActivityChanged = () => {
        // 处理文件上传完成
        onFileUploaded(activity);
        
        // 更新文件列表
        updateFileList();
    };
    
    return (
        <ActivityListPopoverItem
            activity={activity}
            onActivityChanged={handleActivityChanged}
        />
    );
};
```

### 3. 带自定义编辑的活动项目

```javascript
// 带自定义编辑行为的活动项目
const CustomEditActivityItem = ({ activity, customEditHandler }) => {
    const handleEditClick = () => {
        // 自定义编辑逻辑
        customEditHandler(activity);
        
        // 记录编辑操作
        logEditOperation(activity);
    };
    
    return (
        <ActivityListPopoverItem
            activity={activity}
            onClickEditActivityButton={handleEditClick}
            onActivityChanged={() => refreshActivity(activity)}
        />
    );
};
```

### 4. 连续活动处理

```javascript
// 支持连续活动处理的项目
const SequentialActivityItem = ({ activity, nextActivity }) => {
    const handleDoneAndNext = () => {
        // 完成当前活动
        activity.markAsDone();
        
        // 自动开始下一个活动
        if (nextActivity) {
            scheduleNextActivity(nextActivity);
        }
        
        // 更新进度
        updateProgress();
    };
    
    return (
        <ActivityListPopoverItem
            activity={activity}
            onClickDoneAndScheduleNext={handleDoneAndNext}
            onActivityChanged={() => updateActivitySequence()}
        />
    );
};
```

## 活动类型处理

### 1. 文件上传活动

```javascript
// 文件上传活动的特殊处理
const handleFileUploadActivity = (activity) => {
    if (activity.activity_category === "upload_file") {
        return {
            showFileUploader: true,
            showMarkDoneButton: false,
            acceptedFileTypes: activity.accepted_file_types || "*",
            maxFileSize: activity.max_file_size || 10 * 1024 * 1024 // 10MB
        };
    }
    
    return {
        showFileUploader: false,
        showMarkDoneButton: true
    };
};
```

### 2. 邮件模板活动

```javascript
// 邮件模板活动的处理
const handleMailTemplateActivity = (activity) => {
    if (activity.activity_category === "mail_template") {
        return {
            showMailTemplate: true,
            templateId: activity.mail_template_id,
            templateName: activity.mail_template_name
        };
    }
    
    return {
        showMailTemplate: false
    };
};
```

### 3. 会议活动

```javascript
// 会议活动的处理
const handleMeetingActivity = (activity) => {
    if (activity.activity_category === "meeting") {
        return {
            showMeetingButton: true,
            meetingUrl: activity.meeting_url,
            meetingTime: activity.meeting_time
        };
    }
    
    return {
        showMeetingButton: false
    };
};
```

## 状态管理

### 1. 活动状态

```javascript
// 活动状态管理
const ActivityState = {
    TODO: 'todo',
    DONE: 'done',
    OVERDUE: 'overdue',
    TODAY: 'today',
    PLANNED: 'planned'
};

const getActivityStateInfo = (activity) => {
    const state = activity.state;
    const delay = computeDelay(activity.date_deadline);
    
    return {
        state,
        delay,
        isOverdue: delay < 0,
        isToday: delay === 0,
        isDone: state === ActivityState.DONE,
        canEdit: state !== ActivityState.DONE && activity.can_write
    };
};
```

### 2. UI状态

```javascript
// UI状态管理
const manageUIState = (component) => {
    const [uiState, setUIState] = useState({
        showMarkDone: false,
        showFileUploader: false,
        isProcessing: false,
        showDetails: false
    });
    
    const toggleMarkDone = () => {
        setUIState(prev => ({
            ...prev,
            showMarkDone: !prev.showMarkDone
        }));
    };
    
    const setProcessing = (processing) => {
        setUIState(prev => ({
            ...prev,
            isProcessing: processing
        }));
    };
    
    return { uiState, toggleMarkDone, setProcessing };
};
```

## 性能优化

### 1. 组件缓存

```javascript
// 活动项目组件缓存
const ActivityItemCache = new Map();

const getCachedActivityItem = (activityId) => {
    if (ActivityItemCache.has(activityId)) {
        const cached = ActivityItemCache.get(activityId);
        if (Date.now() - cached.timestamp < 60000) { // 1分钟缓存
            return cached.component;
        }
    }
    return null;
};

const setCachedActivityItem = (activityId, component) => {
    ActivityItemCache.set(activityId, {
        component,
        timestamp: Date.now()
    });
};
```

### 2. 延迟计算优化

```javascript
// 延迟计算缓存
const delayCache = new WeakMap();

const getCachedDelay = (activity) => {
    if (delayCache.has(activity)) {
        const cached = delayCache.get(activity);
        if (cached.deadline === activity.date_deadline) {
            return cached.delay;
        }
    }
    
    const delay = computeDelay(activity.date_deadline);
    delayCache.set(activity, {
        deadline: activity.date_deadline,
        delay
    });
    
    return delay;
};
```

## 可访问性

### 1. 键盘导航

```javascript
// 键盘导航支持
const handleKeyDown = (event, activity, actions) => {
    switch (event.key) {
        case 'Enter':
        case ' ':
            event.preventDefault();
            if (activity.activity_category === 'upload_file') {
                actions.triggerFileUpload();
            } else {
                actions.onClickMarkAsDone();
            }
            break;
        case 'Delete':
            event.preventDefault();
            if (confirm('确定要删除此活动吗？')) {
                actions.unlink();
            }
            break;
        case 'e':
            if (event.ctrlKey || event.metaKey) {
                event.preventDefault();
                actions.onClickEditActivityButton();
            }
            break;
    }
};
```

### 2. ARIA 标签

```javascript
// 可访问性属性
const getAccessibilityProps = (activity) => {
    const stateInfo = getActivityStateInfo(activity);
    
    return {
        'role': 'listitem',
        'aria-label': `活动: ${activity.summary || activity.display_name}`,
        'aria-describedby': `activity-delay-${activity.id}`,
        'aria-expanded': 'false',
        'tabindex': '0',
        'data-state': stateInfo.state
    };
};
```

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的活动项目组件
- 统一的活动操作接口

### 2. 策略模式 (Strategy Pattern)
- 不同活动类型的不同处理策略
- 可配置的操作行为

### 3. 状态模式 (State Pattern)
- 根据活动状态显示不同的UI
- 状态驱动的行为变化

## 注意事项

1. **权限控制**: 确保用户有权限操作活动
2. **状态同步**: 保持活动状态与UI状态同步
3. **文件处理**: 安全的文件上传和处理
4. **用户体验**: 提供清晰的操作反馈

## 扩展建议

1. **拖拽操作**: 支持拖拽重新排序活动
2. **批量选择**: 支持批量选择和操作
3. **快捷操作**: 添加更多快捷操作按钮
4. **自定义字段**: 支持显示自定义活动字段
5. **移动端优化**: 优化移动设备上的交互体验

该组件为活动列表提供了完整的单项显示和操作功能，支持多种活动类型和丰富的交互操作。

# Thread Actions - 线程动作组件

## 概述

`thread_actions.js` 实现了 Odoo 邮件系统中线程动作的Web扩展，用于为线程组件添加Web环境特有的动作功能。该文件通过线程动作注册表添加了标记全部已读、取消全部星标、展开表单视图等动作，这些动作根据不同的线程类型和状态条件显示，为用户提供了便捷的线程操作功能，是线程交互功能的重要扩展。

## 文件信息
- **路径**: `/mail/static/src/core/web/thread_actions.js`
- **行数**: 73
- **模块**: `@mail/core/web/thread_actions`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/thread_actions'  // 线程动作注册表
'@odoo/owl'                        // OWL 框架
'@web/core/l10n/translation'       // 国际化
'@web/core/utils/hooks'            // Web 核心钩子
```

## 注册的动作

### 1. 标记全部已读 (mark-all-read)

```javascript
threadActionsRegistry.add("mark-all-read", {
    condition(component) {
        return component.thread?.id === "inbox";
    },
    disabledCondition(component) {
        return component.thread.isEmpty;
    },
    open(component) {
        component.orm.silent.call("mail.message", "mark_all_as_read");
    },
    sequence: 1,
    text: _t("Mark all read"),
});
```

**动作特性**:
- **条件**: 仅在收件箱线程中显示
- **禁用条件**: 线程为空时禁用
- **操作**: 调用ORM服务标记所有消息为已读
- **序列**: 1（优先显示）
- **文本**: "Mark all read"

### 2. 取消全部星标 (unstar-all)

```javascript
threadActionsRegistry.add("unstar-all", {
    condition(component) {
        return component.thread?.id === "starred";
    },
    disabledCondition(component) {
        return component.thread.isEmpty;
    },
    open(component) {
        component.store.unstarAll();
    },
    sequence: 2,
    setup() {
        const component = useComponent();
        component.store = useState(useService("mail.store"));
    },
    text: _t("Unstar all"),
});
```

**动作特性**:
- **条件**: 仅在星标线程中显示
- **禁用条件**: 线程为空时禁用
- **设置**: 获取邮件存储服务
- **操作**: 调用存储服务取消所有星标
- **序列**: 2
- **文本**: "Unstar all"

### 3. 展开表单视图 (expand-form)

```javascript
threadActionsRegistry.add("expand-form", {
    condition(component) {
        return (
            component.thread &&
            !["mail.box", "discuss.channel"].includes(component.thread.model) &&
            component.props.chatWindow?.isOpen
        );
    },
    setup() {
        const component = useComponent();
        component.actionService = useService("action");
    },
    icon: "fa fa-fw fa-expand",
    name: _t("Open Form View"),
    open(component) {
        component.actionService.doAction({
            type: "ir.actions.act_window",
            res_id: component.thread.id,
            res_model: component.thread.model,
            views: [[false, "form"]],
        });
        component.props.chatWindow.close();
    },
    sequence: 40,
    sequenceGroup: 20,
});
```

**动作特性**:
- **条件**: 非邮箱和讨论频道的线程，且聊天窗口已打开
- **设置**: 获取动作服务
- **图标**: "fa fa-fw fa-expand"
- **名称**: "Open Form View"
- **操作**: 打开记录的表单视图并关闭聊天窗口
- **序列**: 40
- **序列组**: 20

## 使用场景

### 1. 收件箱批量操作

```javascript
// 收件箱中的批量已读操作
const InboxBatchOperations = ({ thread }) => {
    const handleMarkAllRead = async () => {
        try {
            // 显示确认对话框
            const confirmed = await showConfirmDialog(
                '确定要标记所有消息为已读吗？',
                '此操作将标记收件箱中的所有消息为已读状态。'
            );
            
            if (confirmed) {
                // 执行标记已读操作
                await orm.silent.call("mail.message", "mark_all_as_read");
                
                // 显示成功消息
                showNotification('所有消息已标记为已读', 'success');
                
                // 更新UI状态
                updateInboxState();
            }
        } catch (error) {
            console.error('标记已读失败:', error);
            showNotification('操作失败，请重试', 'error');
        }
    };
    
    return (
        <div class="inbox-batch-operations">
            <button 
                class="btn btn-primary"
                onClick={handleMarkAllRead}
                disabled={thread.isEmpty}
            >
                标记全部已读
            </button>
        </div>
    );
};
```

### 2. 星标管理

```javascript
// 星标消息管理
const StarredMessageManager = ({ thread, store }) => {
    const handleUnstarAll = async () => {
        try {
            // 显示确认对话框
            const confirmed = await showConfirmDialog(
                '确定要取消所有星标吗？',
                '此操作将移除所有消息的星标标记。'
            );
            
            if (confirmed) {
                // 执行取消星标操作
                await store.unstarAll();
                
                // 显示成功消息
                showNotification('已取消所有星标', 'success');
                
                // 更新统计
                updateStarredStats();
            }
        } catch (error) {
            console.error('取消星标失败:', error);
            showNotification('操作失败，请重试', 'error');
        }
    };
    
    return (
        <div class="starred-manager">
            <div class="starred-header">
                <h3>星标消息</h3>
                <span class="count">{thread.messages.length}</span>
            </div>
            <button 
                class="btn btn-secondary"
                onClick={handleUnstarAll}
                disabled={thread.isEmpty}
            >
                取消全部星标
            </button>
        </div>
    );
};
```

### 3. 聊天窗口表单展开

```javascript
// 聊天窗口中的表单展开功能
const ChatWindowFormExpander = ({ thread, chatWindow, actionService }) => {
    const handleExpandForm = async () => {
        try {
            // 记录操作
            logUserAction('expand_form', {
                thread_id: thread.id,
                thread_model: thread.model
            });
            
            // 打开表单视图
            await actionService.doAction({
                type: "ir.actions.act_window",
                res_id: thread.id,
                res_model: thread.model,
                views: [[false, "form"]],
                target: "current"
            });
            
            // 关闭聊天窗口
            chatWindow.close();
            
            // 更新导航历史
            updateNavigationHistory(thread);
            
        } catch (error) {
            console.error('展开表单失败:', error);
            showNotification('无法打开表单视图', 'error');
        }
    };
    
    const canExpand = thread && 
                     !["mail.box", "discuss.channel"].includes(thread.model) && 
                     chatWindow?.isOpen;
    
    return (
        <div class="chat-window-expander">
            {canExpand && (
                <button 
                    class="btn btn-sm btn-outline-primary"
                    onClick={handleExpandForm}
                    title="在表单视图中打开"
                >
                    <i class="fa fa-expand"></i>
                    展开表单
                </button>
            )}
        </div>
    );
};
```

### 4. 自定义线程动作

```javascript
// 自定义线程动作注册
const registerCustomThreadActions = () => {
    // 导出线程数据
    threadActionsRegistry.add("export-thread", {
        condition(component) {
            return component.thread && 
                   component.thread.messages.length > 0 &&
                   hasExportPermission();
        },
        setup() {
            const component = useComponent();
            component.exportService = useService("export");
        },
        icon: "fa fa-fw fa-download",
        name: _t("Export Thread"),
        open(component) {
            component.exportService.exportThread(component.thread);
        },
        sequence: 50,
        sequenceGroup: 30,
    });
    
    // 打印线程
    threadActionsRegistry.add("print-thread", {
        condition(component) {
            return component.thread && 
                   component.thread.messages.length > 0;
        },
        icon: "fa fa-fw fa-print",
        name: _t("Print Thread"),
        open(component) {
            printThread(component.thread);
        },
        sequence: 51,
        sequenceGroup: 30,
    });
    
    // 分享线程
    threadActionsRegistry.add("share-thread", {
        condition(component) {
            return component.thread && 
                   component.thread.model !== "mail.box" &&
                   hasSharePermission();
        },
        setup() {
            const component = useComponent();
            component.shareService = useService("share");
        },
        icon: "fa fa-fw fa-share",
        name: _t("Share Thread"),
        open(component) {
            component.shareService.shareThread(component.thread);
        },
        sequence: 52,
        sequenceGroup: 30,
    });
};
```

## 动作配置

### 1. 动作属性

```javascript
// 线程动作配置接口
interface ThreadAction {
    // 显示条件
    condition?: (component: Component) => boolean;
    
    // 禁用条件
    disabledCondition?: (component: Component) => boolean;
    
    // 设置函数
    setup?: () => void;
    
    // 图标
    icon?: string;
    
    // 名称
    name?: string;
    
    // 文本
    text?: string;
    
    // 执行函数
    open: (component: Component) => void;
    
    // 序列号
    sequence: number;
    
    // 序列组
    sequenceGroup?: number;
}
```

### 2. 条件函数

```javascript
// 常用条件函数
const ThreadActionConditions = {
    // 仅收件箱
    inboxOnly: (component) => {
        return component.thread?.id === "inbox";
    },
    
    // 仅星标
    starredOnly: (component) => {
        return component.thread?.id === "starred";
    },
    
    // 非空线程
    nonEmpty: (component) => {
        return component.thread && !component.thread.isEmpty;
    },
    
    // 聊天窗口中
    inChatWindow: (component) => {
        return component.props.chatWindow?.isOpen;
    },
    
    // 可编辑记录
    editableRecord: (component) => {
        return component.thread && 
               !["mail.box", "discuss.channel"].includes(component.thread.model) &&
               component.thread.can_write;
    },
    
    // 有权限
    hasPermission: (permission) => (component) => {
        return hasUserPermission(permission);
    }
};
```

### 3. 动作分组

```javascript
// 动作分组管理
const ThreadActionGroups = {
    BULK_OPERATIONS: 10,    // 批量操作
    NAVIGATION: 20,         // 导航操作
    EXPORT_SHARE: 30,       // 导出分享
    ADVANCED: 40            // 高级操作
};

// 按组注册动作
const registerActionsByGroup = () => {
    // 批量操作组
    threadActionsRegistry
        .add("mark-all-read", {
            // ... 配置
            sequenceGroup: ThreadActionGroups.BULK_OPERATIONS
        })
        .add("unstar-all", {
            // ... 配置
            sequenceGroup: ThreadActionGroups.BULK_OPERATIONS
        });
    
    // 导航操作组
    threadActionsRegistry
        .add("expand-form", {
            // ... 配置
            sequenceGroup: ThreadActionGroups.NAVIGATION
        });
};
```

## 性能优化

### 1. 条件缓存

```javascript
// 条件结果缓存
const conditionCache = new WeakMap();

const cachedCondition = (originalCondition) => {
    return (component) => {
        if (conditionCache.has(component)) {
            const cached = conditionCache.get(component);
            if (Date.now() - cached.timestamp < 1000) { // 1秒缓存
                return cached.result;
            }
        }
        
        const result = originalCondition(component);
        conditionCache.set(component, {
            result,
            timestamp: Date.now()
        });
        
        return result;
    };
};
```

### 2. 懒加载服务

```javascript
// 懒加载服务
const lazyService = (serviceName) => {
    let service = null;
    
    return (component) => {
        if (!service) {
            service = useService(serviceName);
        }
        return service;
    };
};

// 使用示例
const getLazyActionService = lazyService("action");
```

## 错误处理

### 1. 动作执行错误

```javascript
// 安全的动作执行
const safeActionExecution = (actionFn) => {
    return async (component) => {
        try {
            await actionFn(component);
        } catch (error) {
            console.error('线程动作执行失败:', error);
            
            // 显示错误消息
            showNotification('操作失败，请重试', 'error');
            
            // 记录错误
            logError({
                type: 'thread_action_error',
                action: actionFn.name,
                error: error.message
            });
        }
    };
};
```

### 2. 权限检查

```javascript
// 权限检查包装器
const withPermissionCheck = (permission, actionFn) => {
    return (component) => {
        if (!hasUserPermission(permission)) {
            showNotification('没有权限执行此操作', 'warning');
            return;
        }
        
        return actionFn(component);
    };
};
```

## 设计模式

### 1. 注册表模式 (Registry Pattern)
- 通过注册表管理线程动作
- 支持动态添加和移除动作

### 2. 策略模式 (Strategy Pattern)
- 不同条件下的不同动作策略
- 可配置的动作行为

### 3. 命令模式 (Command Pattern)
- 封装动作执行逻辑
- 支持撤销和重做

## 注意事项

1. **条件检查**: 确保动作在正确的条件下显示
2. **权限验证**: 验证用户是否有权限执行动作
3. **错误处理**: 提供完善的错误处理机制
4. **性能考虑**: 避免频繁的条件检查

## 扩展建议

1. **自定义动作**: 支持用户自定义线程动作
2. **批量操作**: 增加更多批量操作功能
3. **快捷键**: 为常用动作添加快捷键支持
4. **动作历史**: 记录和显示动作执行历史
5. **权限管理**: 更细粒度的动作权限控制

该文件为线程组件提供了重要的操作功能扩展，通过注册表模式实现了灵活的动作管理系统。

# Base Recipients List - 基础收件人列表

## 概述

`base_recipients_list.js` 实现了 Odoo 邮件系统中的基础收件人列表组件，用于显示线程的收件人信息。该组件提供了收件人的简化显示、弹出框详细视图、建议收件人集成等功能，支持最多显示5个收件人并提供"更多"提示，是邮件编写和线程管理中的重要基础组件，为用户提供了清晰的收件人信息展示。

## 文件信息
- **路径**: `/mail/static/src/core/web/base_recipients_list.js`
- **行数**: 58
- **模块**: `@mail/core/web/base_recipients_list`

## 依赖关系

```javascript
// UI组件依赖
'@mail/core/web/recipient_list'           // 收件人列表弹出框
'@mail/core/web/suggested_recipient_list' // 建议收件人列表

// 核心依赖
'@mail/core/common/thread_model'          // 线程模型
'@web/core/l10n/translation'              // 国际化
'@web/core/utils/strings'                 // 字符串工具
'@web/core/l10n/utils'                    // 本地化工具
'@odoo/owl'                              // OWL 框架
'@web/core/popover/popover_hook'         // 弹出框钩子
```

## 组件定义

### BaseRecipientsList 类

```javascript
class BaseRecipientsList extends Component {
    static template = "mail.BaseRecipientsList";
    static components = { SuggestedRecipientsList };
    static props = { thread: { type: Thread } };
}
```

## Props 配置

### Props 详细说明

- **`thread`** (必需):
  - 类型: Thread 模型实例
  - 用途: 提供收件人数据的线程对象
  - 功能: 获取线程的收件人列表和相关信息

## 组件设置

### setup() 方法

```javascript
setup() {
    this.recipientsPopover = usePopover(RecipientList);
}
```

**初始化内容**:
- 收件人弹出框服务，用于显示详细的收件人列表

## 核心功能

### 1. 收件人列表HTML生成

```javascript
getRecipientListToHTML() {
    const recipients = this.props.thread.recipients.slice(0, 5).map((
        { partner }) => {
            const text = partner.email ? partner.emailWithoutDomain : partner.name;
            return `<span class="text-muted" title="${escape(
                partner.email || _t("no email address")
            )}">${escape(text)}</span>`;
        });
    if (this.props.thread.recipients.length > 5) {
        recipients.push(escape(
            _t("%(recipientCount)s more", {
                recipientCount: this.props.thread.recipients.length - 5}))
        );
    }
    return markup(formatList(recipients));
}
```

**HTML生成逻辑**:
1. 取前5个收件人进行显示
2. 每个收件人显示邮箱域名前部分或姓名
3. 添加完整邮箱地址的提示
4. 超过5个时显示"X more"提示
5. 使用本地化格式化列表
6. 返回安全的HTML标记

### 2. 收件人列表点击处理

```javascript
onClickRecipientList(ev) {
    if (this.recipientsPopover.isOpen) {
        return this.recipientsPopover.close();
    }
    this.recipientsPopover.open(ev.target, {
        thread: this.props.thread
    });
}
```

**点击处理逻辑**:
- 如果弹出框已打开，则关闭
- 否则在点击位置打开收件人详细列表弹出框
- 传递线程对象给弹出框

## 使用场景

### 1. 邮件编写界面

```javascript
// 邮件编写界面中的收件人显示
const MailComposerRecipients = ({ thread, onRecipientsChange }) => {
    const handleRecipientUpdate = (updatedThread) => {
        // 更新收件人列表
        onRecipientsChange(updatedThread.recipients);
        
        // 验证收件人
        validateRecipients(updatedThread.recipients);
        
        // 更新邮件状态
        updateMailStatus(updatedThread);
    };
    
    return (
        <div class="mail-composer-recipients">
            <div class="recipients-header">
                <label>收件人:</label>
                <span class="recipient-count">
                    ({thread.recipients.length})
                </span>
            </div>
            
            <BaseRecipientsList 
                thread={thread}
                onUpdate={handleRecipientUpdate}
            />
            
            <div class="recipients-actions">
                <button class="add-recipient-btn">
                    添加收件人
                </button>
            </div>
        </div>
    );
};
```

### 2. 线程信息显示

```javascript
// 线程信息中的收件人显示
const ThreadInfoRecipients = ({ thread }) => {
    const getRecipientStats = () => {
        const recipients = thread.recipients;
        
        return {
            total: recipients.length,
            withEmail: recipients.filter(r => r.partner.email).length,
            internal: recipients.filter(r => r.partner.isInternal).length,
            external: recipients.filter(r => !r.partner.isInternal).length
        };
    };
    
    const stats = getRecipientStats();
    
    return (
        <div class="thread-info-recipients">
            <div class="recipients-summary">
                <BaseRecipientsList thread={thread} />
            </div>
            
            <div class="recipients-stats">
                <span>总计: {stats.total}</span>
                <span>内部: {stats.internal}</span>
                <span>外部: {stats.external}</span>
            </div>
        </div>
    );
};
```

### 3. 消息详情显示

```javascript
// 消息详情中的收件人显示
const MessageDetailRecipients = ({ message, thread }) => {
    const [showAllRecipients, setShowAllRecipients] = useState(false);
    
    const toggleRecipientsView = () => {
        setShowAllRecipients(!showAllRecipients);
    };
    
    return (
        <div class="message-detail-recipients">
            <div class="recipients-section">
                <h4>收件人</h4>
                
                {showAllRecipients ? (
                    <div class="all-recipients">
                        {thread.recipients.map(recipient => (
                            <div key={recipient.id} class="recipient-item">
                                <span class="name">{recipient.partner.name}</span>
                                <span class="email">{recipient.partner.email}</span>
                            </div>
                        ))}
                    </div>
                ) : (
                    <BaseRecipientsList thread={thread} />
                )}
                
                <button onClick={toggleRecipientsView} class="toggle-view-btn">
                    {showAllRecipients ? '显示简化视图' : '显示全部收件人'}
                </button>
            </div>
        </div>
    );
};
```

### 4. 收件人管理界面

```javascript
// 收件人管理界面
const RecipientManagementInterface = ({ thread }) => {
    const [editMode, setEditMode] = useState(false);
    const [selectedRecipients, setSelectedRecipients] = useState([]);
    
    const handleBatchOperation = (operation) => {
        switch (operation) {
            case 'remove':
                removeRecipients(selectedRecipients);
                break;
            case 'notify':
                notifyRecipients(selectedRecipients);
                break;
            case 'export':
                exportRecipients(selectedRecipients);
                break;
        }
        
        setSelectedRecipients([]);
    };
    
    return (
        <div class="recipient-management">
            <div class="management-header">
                <h3>收件人管理</h3>
                <button 
                    onClick={() => setEditMode(!editMode)}
                    class="edit-mode-btn"
                >
                    {editMode ? '退出编辑' : '编辑模式'}
                </button>
            </div>
            
            <div class="recipients-display">
                <BaseRecipientsList thread={thread} />
            </div>
            
            {editMode && (
                <div class="batch-operations">
                    <button onClick={() => handleBatchOperation('remove')}>
                        批量移除
                    </button>
                    <button onClick={() => handleBatchOperation('notify')}>
                        批量通知
                    </button>
                    <button onClick={() => handleBatchOperation('export')}>
                        导出列表
                    </button>
                </div>
            )}
        </div>
    );
};
```

### 5. 响应式收件人显示

```javascript
// 响应式收件人显示
const ResponsiveRecipientsList = ({ thread, maxVisible = 5 }) => {
    const [screenSize, setScreenSize] = useState('desktop');
    
    useEffect(() => {
        const handleResize = () => {
            const width = window.innerWidth;
            if (width < 768) {
                setScreenSize('mobile');
            } else if (width < 1024) {
                setScreenSize('tablet');
            } else {
                setScreenSize('desktop');
            }
        };
        
        window.addEventListener('resize', handleResize);
        handleResize();
        
        return () => window.removeEventListener('resize', handleResize);
    }, []);
    
    const getMaxVisible = () => {
        switch (screenSize) {
            case 'mobile': return 2;
            case 'tablet': return 3;
            default: return maxVisible;
        }
    };
    
    const adaptedThread = {
        ...thread,
        recipients: thread.recipients.slice(0, getMaxVisible())
    };
    
    return (
        <div class={`responsive-recipients ${screenSize}`}>
            <BaseRecipientsList thread={adaptedThread} />
            
            {thread.recipients.length > getMaxVisible() && (
                <span class="more-indicator">
                    +{thread.recipients.length - getMaxVisible()} 更多
                </span>
            )}
        </div>
    );
};
```

## 收件人处理

### 1. 收件人格式化

```javascript
// 收件人格式化工具
const RecipientFormatter = {
    formatDisplayText: (partner) => {
        if (partner.email) {
            return partner.emailWithoutDomain || partner.email.split('@')[0];
        }
        return partner.name || '未知用户';
    },
    
    formatTooltip: (partner) => {
        if (partner.email) {
            return `${partner.name} <${partner.email}>`;
        }
        return partner.name || '无邮箱地址';
    },
    
    formatListItem: (partner) => {
        const text = RecipientFormatter.formatDisplayText(partner);
        const tooltip = RecipientFormatter.formatTooltip(partner);
        
        return {
            text: escape(text),
            tooltip: escape(tooltip),
            hasEmail: !!partner.email
        };
    }
};
```

### 2. 收件人验证

```javascript
// 收件人验证
const RecipientValidator = {
    validateEmail: (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },
    
    validateRecipient: (recipient) => {
        const errors = [];
        
        if (!recipient.partner.name) {
            errors.push('收件人姓名不能为空');
        }
        
        if (recipient.partner.email && !RecipientValidator.validateEmail(recipient.partner.email)) {
            errors.push('邮箱地址格式无效');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    },
    
    validateRecipientList: (recipients) => {
        const results = recipients.map(recipient => ({
            recipient,
            validation: RecipientValidator.validateRecipient(recipient)
        }));
        
        const validRecipients = results.filter(r => r.validation.isValid);
        const invalidRecipients = results.filter(r => !r.validation.isValid);
        
        return {
            valid: validRecipients.map(r => r.recipient),
            invalid: invalidRecipients,
            hasErrors: invalidRecipients.length > 0
        };
    }
};
```

### 3. 收件人分组

```javascript
// 收件人分组
const RecipientGrouper = {
    groupByType: (recipients) => {
        return {
            internal: recipients.filter(r => r.partner.isInternal),
            external: recipients.filter(r => !r.partner.isInternal),
            withEmail: recipients.filter(r => r.partner.email),
            withoutEmail: recipients.filter(r => !r.partner.email)
        };
    },
    
    groupByDomain: (recipients) => {
        const groups = {};
        
        recipients.forEach(recipient => {
            if (recipient.partner.email) {
                const domain = recipient.partner.email.split('@')[1];
                if (!groups[domain]) {
                    groups[domain] = [];
                }
                groups[domain].push(recipient);
            } else {
                if (!groups['no-email']) {
                    groups['no-email'] = [];
                }
                groups['no-email'].push(recipient);
            }
        });
        
        return groups;
    },
    
    groupByStatus: (recipients) => {
        return {
            active: recipients.filter(r => r.partner.active),
            inactive: recipients.filter(r => !r.partner.active),
            online: recipients.filter(r => r.partner.isOnline),
            offline: recipients.filter(r => !r.partner.isOnline)
        };
    }
};
```

## 显示优化

### 1. 文本截断

```javascript
// 文本截断工具
const TextTruncator = {
    truncateEmail: (email, maxLength = 20) => {
        if (!email || email.length <= maxLength) {
            return email;
        }
        
        const [local, domain] = email.split('@');
        if (local.length > maxLength - 3) {
            return local.substring(0, maxLength - 3) + '...';
        }
        
        return email;
    },
    
    truncateName: (name, maxLength = 15) => {
        if (!name || name.length <= maxLength) {
            return name;
        }
        
        return name.substring(0, maxLength - 3) + '...';
    },
    
    getOptimalDisplayText: (partner, maxLength = 20) => {
        if (partner.email) {
            const emailWithoutDomain = partner.email.split('@')[0];
            return TextTruncator.truncateEmail(emailWithoutDomain, maxLength);
        }
        
        return TextTruncator.truncateName(partner.name, maxLength);
    }
};
```

### 2. 样式适配

```javascript
// 样式适配
const StyleAdapter = {
    getRecipientItemClass: (recipient, index, total) => {
        const classes = ['recipient-item'];
        
        if (index === 0) classes.push('first');
        if (index === total - 1) classes.push('last');
        if (!recipient.partner.email) classes.push('no-email');
        if (recipient.partner.isInternal) classes.push('internal');
        
        return classes.join(' ');
    },
    
    getListContainerClass: (recipientCount, maxVisible) => {
        const classes = ['recipients-container'];
        
        if (recipientCount > maxVisible) classes.push('has-more');
        if (recipientCount === 0) classes.push('empty');
        if (recipientCount === 1) classes.push('single');
        
        return classes.join(' ');
    }
};
```

## 性能优化

### 1. 收件人缓存

```javascript
// 收件人显示缓存
const RecipientDisplayCache = {
    cache: new Map(),
    
    getCachedDisplay: (recipients) => {
        const key = recipients.map(r => r.id).join(',');
        return RecipientDisplayCache.cache.get(key);
    },
    
    setCachedDisplay: (recipients, display) => {
        const key = recipients.map(r => r.id).join(',');
        RecipientDisplayCache.cache.set(key, display);
        
        // 限制缓存大小
        if (RecipientDisplayCache.cache.size > 100) {
            const firstKey = RecipientDisplayCache.cache.keys().next().value;
            RecipientDisplayCache.cache.delete(firstKey);
        }
    }
};
```

### 2. 虚拟化渲染

```javascript
// 大量收件人的虚拟化渲染
const VirtualizedRecipientsList = ({ recipients, itemHeight = 30 }) => {
    const [visibleRange, setVisibleRange] = useState({ start: 0, end: 10 });
    
    const visibleRecipients = recipients.slice(visibleRange.start, visibleRange.end);
    
    return (
        <div class="virtualized-recipients">
            <div style={{ height: recipients.length * itemHeight }}>
                <div style={{ transform: `translateY(${visibleRange.start * itemHeight}px)` }}>
                    {visibleRecipients.map(recipient => (
                        <div key={recipient.id} class="recipient-item">
                            {recipient.partner.name}
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};
```

## 可访问性

### 1. ARIA 标签

```javascript
// 可访问性属性
const getAccessibilityProps = (recipients) => {
    return {
        'role': 'list',
        'aria-label': `收件人列表，共 ${recipients.length} 人`,
        'aria-live': 'polite'
    };
};

// 收件人项目的可访问性
const getRecipientItemAccessibilityProps = (recipient, index) => {
    return {
        'role': 'listitem',
        'aria-label': `收件人 ${index + 1}: ${recipient.partner.name}`,
        'aria-describedby': `recipient-${recipient.id}-email`
    };
};
```

### 2. 键盘导航

```javascript
// 键盘导航支持
const handleKeyDown = (event, recipients, activeIndex, setActiveIndex) => {
    switch (event.key) {
        case 'ArrowDown':
            event.preventDefault();
            setActiveIndex(Math.min(activeIndex + 1, recipients.length - 1));
            break;
        case 'ArrowUp':
            event.preventDefault();
            setActiveIndex(Math.max(activeIndex - 1, 0));
            break;
        case 'Enter':
        case ' ':
            event.preventDefault();
            // 打开收件人详情
            break;
    }
};
```

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的收件人列表组件
- 统一的收件人显示接口

### 2. 策略模式 (Strategy Pattern)
- 不同显示策略（简化/详细）
- 可配置的格式化策略

### 3. 观察者模式 (Observer Pattern)
- 监听收件人变化
- 响应用户交互

## 注意事项

1. **数据安全**: 正确转义用户输入的文本
2. **性能考虑**: 避免频繁的DOM操作
3. **用户体验**: 提供清晰的收件人信息
4. **国际化**: 支持多语言显示

## 扩展建议

1. **拖拽排序**: 支持拖拽重新排序收件人
2. **批量操作**: 支持批量选择和操作收件人
3. **智能建议**: 基于历史的收件人建议
4. **分组显示**: 支持按不同维度分组显示
5. **移动端优化**: 优化移动设备上的显示效果

该组件为收件人显示提供了简洁而功能完整的基础实现，是邮件系统中的重要UI组件。

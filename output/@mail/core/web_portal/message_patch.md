# Message Patch (Web Portal) - 消息补丁（Web门户）

## 概述

`message_patch.js` 实现了 Odoo 邮件系统中消息组件在Web门户环境下的功能补丁，专门用于增强消息的"阅读更多/阅读更少"功能。该补丁通过识别引用内容和停止拼写检查后的文本节点，自动添加展开/收起功能，支持多个独立的阅读更多区域，提供了完整的状态管理和DOM操作，为门户用户提供了更好的长消息阅读体验。

## 文件信息
- **路径**: `/mail/static/src/core/web_portal/message_patch.js`
- **行数**: 168
- **模块**: `@mail/core/web_portal/message_patch`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/patch'        // 补丁工具
'@mail/core/common/message'    // 消息组件
'@odoo/owl'                   // OWL 框架
'@web/core/l10n/translation'  // 国际化
```

## 补丁实现

### Message 原型补丁

```javascript
patch(Message.prototype, {
    setup() {
        super.setup(...arguments);
        this.state.lastReadMoreIndex = 0;
        this.state.isReadMoreByIndex = new Map();
        onWillUnmount(() => {
            this.messageBody.el?.querySelector(".o-mail-read-more-less")?.remove();
        });
    },

    prepareMessageBody(bodyEl) {
        super.prepareMessageBody(...arguments);
        Array.from(bodyEl.querySelectorAll(".o-mail-read-more-less")).forEach((el) => el.remove());
        this.insertReadMoreLess(bodyEl);
    },

    insertReadMoreLess(bodyEl) {
        // 阅读更多/更少功能实现...
    }
});
```

## 核心功能

### 1. 组件设置增强

```javascript
setup() {
    super.setup(...arguments);
    this.state.lastReadMoreIndex = 0;
    this.state.isReadMoreByIndex = new Map();
    onWillUnmount(() => {
        this.messageBody.el?.querySelector(".o-mail-read-more-less")?.remove();
    });
}
```

**设置增强**:
- **lastReadMoreIndex**: 跟踪阅读更多区域的索引
- **isReadMoreByIndex**: 存储每个区域的展开/收起状态
- **清理机制**: 组件卸载时清理DOM元素

### 2. 消息体准备

```javascript
prepareMessageBody(bodyEl) {
    super.prepareMessageBody(...arguments);
    Array.from(bodyEl.querySelectorAll(".o-mail-read-more-less")).forEach((el) => el.remove());
    this.insertReadMoreLess(bodyEl);
}
```

**准备逻辑**:
- 调用父类的消息体准备方法
- 清除已存在的阅读更多/更少元素
- 插入新的阅读更多/更少功能

### 3. 阅读更多/更少功能插入

```javascript
insertReadMoreLess(bodyEl) {
    // 工具函数定义
    function prevAll(e, selector) { /* 获取所有前面的匹配元素 */ }
    function prev(e, selector) { /* 获取前面的匹配元素 */ }
    function hide(el) { /* 隐藏元素 */ }
    function toggle(el, condition) { /* 切换元素显示状态 */ }

    // 主要逻辑实现...
}
```

**功能特性**:
- 识别引用内容（data-o-mail-quote属性）
- 处理停止拼写检查后的文本节点
- 创建独立的阅读更多区域
- 提供展开/收起交互

## 工具函数

### 1. DOM遍历函数

```javascript
function prevAll(e, selector) {
    const res = [];
    while ((e = e.previousElementSibling)) {
        if (e.matches(selector)) {
            res.push(e);
        }
    }
    return res;
}

function prev(e, selector) {
    while ((e = e.previousElementSibling)) {
        if (e.matches(selector)) {
            return e;
        }
    }
}
```

**遍历功能**:
- **prevAll**: 获取所有前面的匹配兄弟元素
- **prev**: 获取前面的第一个匹配兄弟元素

### 2. 显示控制函数

```javascript
function hide(el) {
    el.dataset.oMailDisplay = el.style.display;
    el.style.display = "none";
}

function toggle(el, condition = false) {
    if (condition) {
        let newDisplay = el.dataset.oMailDisplay;
        if (newDisplay === "none") {
            newDisplay = null;
        }
        el.style.display = newDisplay;
    } else {
        hide(el);
    }
}
```

**显示控制**:
- **hide**: 隐藏元素并保存原始显示状态
- **toggle**: 根据条件切换元素显示状态

## 核心算法

### 1. 节点分组算法

```javascript
const groups = [];
let readMoreNodes;
const ELEMENT_NODE = 1;
const TEXT_NODE = 3;

const childrenEl = Array.from(bodyEl.childNodes).filter(
    function (childEl) {
        return (
            childEl.nodeType === ELEMENT_NODE ||
            (childEl.nodeType === TEXT_NODE && childEl.nodeValue.trim())
        );
    }
);

for (const childEl of childrenEl) {
    // 处理停止拼写检查后的文本节点
    if (
        childEl.nodeType === TEXT_NODE &&
        prevAll(childEl, '[id*="stopSpelling"]').length > 0
    ) {
        const newChildEl = document.createElement("span");
        newChildEl.textContent = childEl.textContent;
        newChildEl.dataset.oMailQuote = "1";
        childEl.parentNode.replaceChild(newChildEl, childEl);
    }

    // 创建阅读更多分组
    if (
        (childEl.nodeType === ELEMENT_NODE && childEl.getAttribute("data-o-mail-quote")) ||
        (childEl.nodeName === "BR" && prev(childEl, '[data-o-mail-quote="1"]'))
    ) {
        if (!readMoreNodes) {
            readMoreNodes = [];
            groups.push(readMoreNodes);
        }
        hide(childEl);
        readMoreNodes.push(childEl);
    } else {
        readMoreNodes = undefined;
        this.insertReadMoreLess(childEl);
    }
}
```

**分组逻辑**:
- 过滤有效的子节点（元素节点和非空文本节点）
- 将停止拼写检查后的文本节点转换为span元素
- 将连续的引用内容分组到同一个阅读更多区域
- 递归处理嵌套元素

### 2. 交互控制算法

```javascript
for (const group of groups) {
    const index = this.state.lastReadMoreIndex++;

    // 创建阅读更多/更少链接
    const readMoreLessEl = document.createElement("a");
    readMoreLessEl.className = "o-mail-read-more-less d-block";
    readMoreLessEl.href = "#";
    readMoreLessEl.textContent = _t("Read More");
    group[0].parentNode.insertBefore(readMoreLessEl, group[0]);

    // 初始化状态
    if (!this.state.isReadMoreByIndex.has(index)) {
        this.state.isReadMoreByIndex.set(index, true);
    }

    // 状态更新函数
    const updateFromState = () => {
        const isReadMore = this.state.isReadMoreByIndex.get(index);
        for (const childEl of group) {
            hide(childEl);
            toggle(childEl, !isReadMore);
        }
        readMoreLessEl.textContent = isReadMore
            ? _t("Read More").toString()
            : _t("Read Less").toString();
    };

    // 点击事件处理
    readMoreLessEl.addEventListener("click", (e) => {
        e.preventDefault();
        this.state.isReadMoreByIndex.set(index, !this.state.isReadMoreByIndex.get(index));
        updateFromState();
    });

    updateFromState();
}
```

**交互控制**:
- 为每个分组创建唯一的索引
- 创建可点击的阅读更多/更少链接
- 管理每个区域的独立状态
- 提供点击切换功能

## 使用场景

### 1. 门户消息显示增强

```javascript
// 门户消息显示增强功能
const PortalMessageEnhancer = {
    enhanceMessage: (messageComponent) => {
        // 添加阅读进度指示器
        PortalMessageEnhancer.addReadingProgress(messageComponent);

        // 添加内容摘要
        PortalMessageEnhancer.addContentSummary(messageComponent);

        // 添加快速操作
        PortalMessageEnhancer.addQuickActions(messageComponent);

        // 添加阅读统计
        PortalMessageEnhancer.addReadingStatistics(messageComponent);
    },

    addReadingProgress: (messageComponent) => {
        const progressContainer = document.createElement('div');
        progressContainer.className = 'reading-progress-container';
        progressContainer.innerHTML = `
            <div class="progress-header">
                <span class="progress-label">阅读进度</span>
                <span class="progress-percentage">0%</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
        `;

        // 监听滚动事件更新进度
        PortalMessageEnhancer.trackReadingProgress(messageComponent, progressContainer);

        messageComponent.el.prepend(progressContainer);
    },

    trackReadingProgress: (messageComponent, progressContainer) => {
        const messageEl = messageComponent.el;
        const progressFill = progressContainer.querySelector('.progress-fill');
        const progressPercentage = progressContainer.querySelector('.progress-percentage');

        const updateProgress = () => {
            const messageRect = messageEl.getBoundingClientRect();
            const viewportHeight = window.innerHeight;

            // 计算消息在视口中的可见比例
            const visibleTop = Math.max(0, -messageRect.top);
            const visibleBottom = Math.min(messageRect.height, viewportHeight - messageRect.top);
            const visibleHeight = Math.max(0, visibleBottom - visibleTop);

            const progress = Math.min(100, (visibleHeight / messageRect.height) * 100);

            progressFill.style.width = `${progress}%`;
            progressPercentage.textContent = `${Math.round(progress)}%`;

            // 记录阅读进度
            if (progress > 80) {
                PortalMessageEnhancer.markAsRead(messageComponent);
            }
        };

        window.addEventListener('scroll', updateProgress);
        window.addEventListener('resize', updateProgress);
        updateProgress();
    },

    markAsRead: (messageComponent) => {
        if (!messageComponent.isMarkedAsRead) {
            messageComponent.isMarkedAsRead = true;

            // 发送阅读统计
            PortalMessageEnhancer.sendReadingAnalytics(messageComponent);
        }
    },

    addContentSummary: (messageComponent) => {
        const summaryContainer = document.createElement('div');
        summaryContainer.className = 'content-summary-container';
        summaryContainer.innerHTML = `
            <div class="summary-header">
                <h5>内容摘要</h5>
                <button class="toggle-summary-btn">
                    <i class="fa fa-chevron-down"></i>
                </button>
            </div>
            <div class="summary-content" style="display: none;">
                <div class="summary-text">正在生成摘要...</div>
                <div class="summary-stats">
                    <span class="word-count">字数: -</span>
                    <span class="read-time">预计阅读时间: -</span>
                </div>
            </div>
        `;

        const toggleBtn = summaryContainer.querySelector('.toggle-summary-btn');
        const summaryContent = summaryContainer.querySelector('.summary-content');

        toggleBtn.addEventListener('click', () => {
            const isVisible = summaryContent.style.display !== 'none';
            summaryContent.style.display = isVisible ? 'none' : 'block';
            toggleBtn.querySelector('i').className = `fa fa-chevron-${isVisible ? 'down' : 'up'}`;

            if (!isVisible && !summaryContainer.dataset.loaded) {
                PortalMessageEnhancer.generateSummary(messageComponent, summaryContainer);
            }
        });

        messageComponent.el.appendChild(summaryContainer);
    },

    generateSummary: async (messageComponent, summaryContainer) => {
        try {
            const messageText = messageComponent.el.textContent;
            const wordCount = messageText.split(/\s+/).length;
            const readTime = Math.ceil(wordCount / 200); // 假设每分钟200字

            // 生成简单摘要（取前100字）
            const summary = messageText.substring(0, 100) + (messageText.length > 100 ? '...' : '');

            summaryContainer.querySelector('.summary-text').textContent = summary;
            summaryContainer.querySelector('.word-count').textContent = `字数: ${wordCount}`;
            summaryContainer.querySelector('.read-time').textContent = `预计阅读时间: ${readTime}分钟`;
            summaryContainer.dataset.loaded = 'true';
        } catch (error) {
            console.error('生成摘要失败:', error);
            summaryContainer.querySelector('.summary-text').textContent = '摘要生成失败';
        }
    },

    addQuickActions: (messageComponent) => {
        const actionsContainer = document.createElement('div');
        actionsContainer.className = 'quick-actions-container';
        actionsContainer.innerHTML = `
            <div class="actions-header">
                <h5>快速操作</h5>
            </div>
            <div class="actions-buttons">
                <button class="action-btn copy-btn" title="复制内容">
                    <i class="fa fa-copy"></i> 复制
                </button>
                <button class="action-btn print-btn" title="打印消息">
                    <i class="fa fa-print"></i> 打印
                </button>
                <button class="action-btn share-btn" title="分享消息">
                    <i class="fa fa-share"></i> 分享
                </button>
                <button class="action-btn bookmark-btn" title="收藏消息">
                    <i class="fa fa-bookmark"></i> 收藏
                </button>
            </div>
        `;

        PortalMessageEnhancer.bindQuickActions(actionsContainer, messageComponent);
        messageComponent.el.appendChild(actionsContainer);
    },

    bindQuickActions: (container, messageComponent) => {
        const copyBtn = container.querySelector('.copy-btn');
        const printBtn = container.querySelector('.print-btn');
        const shareBtn = container.querySelector('.share-btn');
        const bookmarkBtn = container.querySelector('.bookmark-btn');

        copyBtn.addEventListener('click', () => {
            PortalMessageEnhancer.copyMessageContent(messageComponent);
        });

        printBtn.addEventListener('click', () => {
            PortalMessageEnhancer.printMessage(messageComponent);
        });

        shareBtn.addEventListener('click', () => {
            PortalMessageEnhancer.shareMessage(messageComponent);
        });

        bookmarkBtn.addEventListener('click', () => {
            PortalMessageEnhancer.bookmarkMessage(messageComponent);
        });
    },

    copyMessageContent: async (messageComponent) => {
        try {
            const messageText = messageComponent.el.textContent;
            await navigator.clipboard.writeText(messageText);
            showNotification('内容已复制到剪贴板', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            showNotification('复制失败，请手动选择复制', 'error');
        }
    },

    printMessage: (messageComponent) => {
        const printWindow = window.open('', '_blank');
        const messageContent = messageComponent.el.innerHTML;

        printWindow.document.write(`
            <html>
                <head>
                    <title>打印消息</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .o-mail-read-more-less { display: none; }
                        [data-o-mail-quote] { display: block !important; }
                    </style>
                </head>
                <body>
                    ${messageContent}
                </body>
            </html>
        `);

        printWindow.document.close();
        printWindow.print();
    },

    shareMessage: (messageComponent) => {
        if (navigator.share) {
            navigator.share({
                title: '分享消息',
                text: messageComponent.el.textContent.substring(0, 100) + '...',
                url: window.location.href
            });
        } else {
            // 降级到复制链接
            PortalMessageEnhancer.copyMessageLink(messageComponent);
        }
    },

    copyMessageLink: async (messageComponent) => {
        try {
            const messageId = messageComponent.props.message.id;
            const link = `${window.location.origin}${window.location.pathname}#message-${messageId}`;
            await navigator.clipboard.writeText(link);
            showNotification('消息链接已复制', 'success');
        } catch (error) {
            console.error('复制链接失败:', error);
        }
    },

    bookmarkMessage: (messageComponent) => {
        const messageId = messageComponent.props.message.id;
        const bookmarks = JSON.parse(localStorage.getItem('portal_message_bookmarks') || '[]');

        if (bookmarks.includes(messageId)) {
            // 移除收藏
            const index = bookmarks.indexOf(messageId);
            bookmarks.splice(index, 1);
            showNotification('已取消收藏', 'info');
        } else {
            // 添加收藏
            bookmarks.push(messageId);
            showNotification('已添加到收藏', 'success');
        }

        localStorage.setItem('portal_message_bookmarks', JSON.stringify(bookmarks));

        // 更新按钮状态
        const bookmarkBtn = messageComponent.el.querySelector('.bookmark-btn');
        bookmarkBtn.classList.toggle('bookmarked', bookmarks.includes(messageId));
    },

    sendReadingAnalytics: (messageComponent) => {
        const analytics = {
            messageId: messageComponent.props.message.id,
            readTime: Date.now(),
            userAgent: navigator.userAgent,
            viewportSize: {
                width: window.innerWidth,
                height: window.innerHeight
            }
        };

        // 发送到分析服务
        fetch('/web/dataset/call_kw/mail.message/log_reading_analytics', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                model: 'mail.message',
                method: 'log_reading_analytics',
                args: [analytics],
                kwargs: {}
            })
        }).catch(error => {
            console.error('发送阅读统计失败:', error);
        });
    }
};
```
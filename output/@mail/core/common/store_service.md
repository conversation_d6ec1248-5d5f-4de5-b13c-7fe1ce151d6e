# Store Service - 存储服务

## 概述

`store_service.js` 是 Odoo 邮件系统的核心数据存储服务，继承自基础 Store 类，负责管理整个邮件系统的数据状态、模型注册、数据获取、缓存管理等功能。它是整个邮件系统的数据中心，协调所有模型之间的关系和数据流。

## 文件信息
- **路径**: `/mail/static/src/core/common/store_service.js`
- **行数**: 778
- **模块**: `@mail/core/common/store_service`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/record'        // Record 基类和 Store
'@mail/utils/common/misc'         // 工具函数
'@web/core/network/rpc'           // RPC 网络请求
'@odoo/owl'                       // OWL 响应式系统
'@web/core/registry'              // 服务注册表
'@web/core/user'                  // 用户服务
'@web/core/utils/concurrency'     // 并发工具
'@web/core/utils/timing'          // 时间工具
'@web/session'                    // 会话信息
'@web/core/l10n/translation'      // 国际化
'@mail/utils/common/format'       // 格式化工具
```

## 类定义

### Store 类

```javascript
class Store extends BaseStore {
    static FETCH_DATA_DEBOUNCE_DELAY = 1;    // 数据获取防抖延迟
    static OTHER_LONG_TYPING = 60000;        // 长时间打字阈值
    FETCH_LIMIT = 30;                        // 获取限制
    DEFAULT_AVATAR = "/mail/static/src/img/smiley/avatar.jpg";  // 默认头像
    isReady = new Deferred();                // 就绪状态
}
```

## 模型映射

### Python 到 JavaScript 模型映射

```javascript
const pyToJsModels = {
    "discuss.channel.member": "ChannelMember",
    "discuss.channel.rtc.session": "RtcSession", 
    "discuss.channel": "Thread",
    "ir.attachment": "Attachment",
    "mail.activity": "Activity",
    "mail.guest": "Persona",
    "mail.followers": "Follower",
    "mail.link.preview": "LinkPreview",
    "mail.message": "Message",
    "mail.notification": "Notification",
    "mail.scheduled.message": "ScheduledMessage",
    "mail.thread": "Thread",
    "res.partner": "Persona",
};
```

### 附加字段映射

```javascript
const addFieldsByPyModel = {
    "discuss.channel": { model: "discuss.channel" },
    "mail.guest": { type: "guest" },
    "res.partner": { type: "partner" },
};
```

## 注册的模型类型

### 核心模型

```javascript
// 活动和附件
Activity;                    // 活动模型
Attachment;                  // 附件模型

// 频道和成员
ChannelMember;              // 频道成员模型
ChatWindow;                 // 聊天窗口模型

// 消息相关
Message;                    // 消息模型
MessageReactions;           // 消息反应模型
Notification;               // 通知模型

// 用户和角色
Persona;                    // 用户角色模型

// 线程和设置
Thread;                     // 线程模型
Settings;                   // 设置模型

// 其他
Composer;                   // 编辑器模型
Failure;                    // 失败模型
Follower;                   // 关注者模型
LinkPreview;                // 链接预览模型
ScheduledMessage;           // 计划消息模型
Volume;                     // 音量模型
```

## 核心属性

### 用户和身份

```javascript
self = Record.one("Persona");        // 当前登录用户
odoobot = Record.one("Persona");     // Odoobot 用户
inPublicPage: boolean = false;       // 是否在公共页面
odoobotOnboarding: boolean;          // Odoobot 引导状态
users = {};                          // 用户集合
internalUserGroupId: number;         // 内部用户组ID
```

### 功能特性

```javascript
channel_types_with_seen_infos: string[] = [];  // 支持已读信息的频道类型
hasMessageTranslationFeature: boolean;         // 是否有消息翻译功能
hasLinkPreviewFeature: boolean = true;         // 是否有链接预览功能
mt_comment_id: number;                         // 评论消息类型ID
```

### 状态跟踪

```javascript
imStatusTrackedPersonas = Record.many("Persona", {
    inverse: "storeAsTrackedImStatus",
});
```

### 菜单和界面

```javascript
menu = { counter: 0 };               // 菜单状态
chatHub = Record.one("ChatHub", { compute: () => ({}) });  // 聊天中心
openInviteThread = Record.one("Thread");  // 打开的邀请线程
```

### 失败处理

```javascript
failures = Record.many("Failure", {
    sort: (f1, f2) => f2.lastMessage?.id - f1.lastMessage?.id,
});
```

### 设置

```javascript
settings = Record.one("Settings");   // 应用设置
```

## 数据获取管理

### 获取状态

```javascript
fetchDeferred = new Deferred();      // 获取延迟对象
fetchParams = {};                    // 获取参数
fetchReadonly: boolean = true;       // 只读模式
fetchSilent: boolean = true;         // 静默模式
```

### 消息发送互斥

```javascript
messagePostMutex = new Mutex();      // 消息发送互斥锁
```

### 缓存数据

```javascript
cannedReponses = this.makeCachedFetchData({ canned_responses: true });
```

## 特殊提及

### 特殊提及配置

```javascript
specialMentions = [
    {
        isSpecial: true,
        label: "everyone",
        channel_types: ["channel", "group"],
        displayName: "Everyone", 
        description: _t("Notify everyone"),
    },
];
```

**功能说明**:
- 定义特殊的 @everyone 提及
- 仅在频道和群组中可用
- 通知所有成员

## 计算属性

### 初始化参数

```javascript
get initMessagingParams() {
    return {
        init_messaging: {},
    };
}
```

### 菜单线程

```javascript
menuThreads = Record.many("Thread", {
    compute() {
        const searchTerm = cleanTerm(this.discuss.searchTerm);
        let threads = Object.values(this.Thread.records).filter(
            (thread) =>
                (thread.displayToSelf ||
                    (thread.needactionMessages.length > 0 && thread.model !== "mail.box")) &&
                cleanTerm(thread.displayName).includes(searchTerm)
        );
        
        const tab = this.discuss.activeTab;
        if (tab !== "main") {
            threads = threads.filter(({ channel_type }) =>
                this.tabToThreadType(tab).includes(channel_type)
            );
        } else if (tab === "main" && this.env.inDiscussApp) {
            threads = threads.filter(({ channel_type }) =>
                this.tabToThreadType("mailbox").includes(channel_type)
            );
        }
        
        return threads;
    },
    
    sort(a, b) {
        // 排序规则：
        // - 需要操作的线程
        // - 未读频道
        // - 已读频道  
        // - odoobot 聊天
        // 在每组中，最近消息的线程排在前面
    },
});
```

## 数据管理功能

### 模型注册

Store 类负责注册和管理所有邮件系统的数据模型：
- 提供统一的模型访问接口
- 管理模型之间的关系
- 处理数据的序列化和反序列化

### 缓存管理

```javascript
// 创建缓存数据获取器
makeCachedFetchData(params) {
    // 实现数据缓存逻辑
    // 避免重复请求相同数据
}
```

### 状态同步

- 管理所有模型的状态变化
- 协调不同组件之间的数据同步
- 处理实时更新和通知

## 线程类型映射

### 标签页到线程类型

```javascript
tabToThreadType(tab) {
    // 将UI标签页映射到对应的线程类型
    // 例如：'chat' -> ['chat', 'group']
}
```

## 服务注册

### 服务配置

```javascript
const storeService = {
    dependencies: ["rpc", "bus_service", "user", "ui"],
    start(env, services) {
        return new Store(env, services);
    },
};

registry.category("services").add("mail.store", storeService);
```

## 使用场景

### 1. 获取当前用户

```javascript
const currentUser = store.self;
if (currentUser) {
    console.log("当前用户:", currentUser.name);
}
```

### 2. 访问模型

```javascript
// 获取所有消息
const messages = Object.values(store.Message.records);

// 创建新线程
const thread = store.Thread.insert({
    model: "discuss.channel",
    name: "新频道"
});
```

### 3. 管理失败

```javascript
// 获取失败列表
const failures = store.failures;
failures.forEach(failure => {
    console.log("失败的消息:", failure.lastMessage);
});
```

### 4. 检查功能特性

```javascript
if (store.hasMessageTranslationFeature) {
    // 显示翻译功能
    showTranslationButton();
}

if (store.hasLinkPreviewFeature) {
    // 启用链接预览
    enableLinkPreview();
}
```

## 性能优化

### 1. 防抖处理

```javascript
static FETCH_DATA_DEBOUNCE_DELAY = 1;  // 1ms 防抖延迟
```

### 2. 缓存机制

- 使用 `makeCachedFetchData` 缓存频繁访问的数据
- 避免重复的网络请求

### 3. 互斥锁

```javascript
messagePostMutex = new Mutex();  // 防止并发消息发送
```

### 4. 计算属性优化

- 使用计算属性缓存复杂的数据处理结果
- 依赖变化时自动重新计算

## 设计模式

### 1. 单例模式 (Singleton Pattern)
- Store 作为全局唯一的数据存储中心
- 所有组件共享同一个 Store 实例

### 2. 观察者模式 (Observer Pattern)
- 模型变化自动通知相关组件
- 响应式数据更新

### 3. 工厂模式 (Factory Pattern)
- 通过模型名称创建对应的模型实例
- 统一的模型创建接口

### 4. 适配器模式 (Adapter Pattern)
- Python 模型到 JavaScript 模型的映射
- 不同数据格式之间的转换

## 注意事项

1. **内存管理**: 及时清理不再使用的模型实例
2. **状态一致性**: 确保所有组件看到的数据状态一致
3. **并发控制**: 使用互斥锁防止数据竞争
4. **缓存策略**: 合理使用缓存避免内存泄漏

## 扩展建议

1. **离线支持**: 实现离线数据缓存和同步
2. **数据压缩**: 对大量数据进行压缩存储
3. **增量更新**: 实现增量数据更新机制
4. **数据验证**: 添加数据完整性验证
5. **性能监控**: 添加数据操作性能监控

该服务是整个邮件系统的数据基础设施，为所有功能模块提供统一的数据管理和访问接口。

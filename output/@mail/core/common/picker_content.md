# Picker Content - 选择器内容组件

## 概述

`picker_content.js` 实现了 Odoo 邮件系统中的选择器内容组件，用于在弹出框中显示表情选择器和GIF选择器的内容。该组件作为选择器弹出框的核心内容容器，集成了表情选择器，支持事件处理和状态管理，为用户提供了统一的选择器界面，是选择器系统的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/core/common/picker_content.js`
- **行数**: 27
- **模块**: `@mail/core/common/picker_content`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                           // OWL 框架
'@web/core/utils/misc'                // Web 核心工具
'@web/core/emoji_picker/emoji_picker' // 表情选择器
```

## 组件定义

### PickerContent 类

```javascript
class PickerContent extends Component {
    static components = { EmojiPicker };
    static props = ["PICKERS", "close", "pickers", "state", "storeScroll"];
    static template = "mail.PickerContent";
}
```

## Props 配置

### Props 定义

```typescript
interface Props {
    PICKERS: Object;           // 选择器类型常量
    close: function;           // 关闭选择器回调
    pickers: Array;            // 可用的选择器列表
    state: Object;             // 选择器状态
    storeScroll: function;     // 存储滚动位置回调
}
```

### Props 详细说明

- **`PICKERS`** (必需):
  - 类型: 对象
  - 用途: 选择器类型的常量定义
  - 功能: 提供选择器类型的枚举值

- **`close`** (必需):
  - 类型: 函数
  - 用途: 关闭选择器的回调函数
  - 触发: 用户选择完成或取消时

- **`pickers`** (必需):
  - 类型: 数组
  - 用途: 当前可用的选择器列表
  - 内容: 包含表情选择器、GIF选择器等

- **`state`** (必需):
  - 类型: 对象
  - 用途: 选择器的当前状态
  - 功能: 管理选择器的显示状态和数据

- **`storeScroll`** (必需):
  - 类型: 函数
  - 用途: 存储滚动位置的回调
  - 功能: 保持用户的浏览位置

## 核心功能

### 1. 点击事件处理

```javascript
onClick(ev) {
    markEventHandled(ev, "PickerContent.onClick");
}
```

**事件处理**:
- 标记事件已被处理
- 防止事件冒泡
- 确保点击事件的正确处理

## 使用场景

### 1. 表情选择器弹出框

```javascript
// 在消息编辑器中显示表情选择器
const showEmojiPicker = (targetElement) => {
    const pickerState = {
        currentPicker: 'emoji',
        scrollPosition: 0
    };
    
    const availablePickers = [
        { type: 'emoji', label: '表情', component: EmojiPicker }
    ];
    
    showPopover(targetElement, {
        component: PickerContent,
        props: {
            PICKERS: PICKER_TYPES,
            close: () => hidePopover(),
            pickers: availablePickers,
            state: pickerState,
            storeScroll: (position) => saveScrollPosition(position)
        }
    });
};
```

### 2. GIF选择器集成

```javascript
// 集成GIF选择器的完整选择器
const showFullPicker = (targetElement) => {
    const pickerState = {
        currentPicker: 'emoji',
        scrollPosition: 0,
        searchTerm: ''
    };
    
    const availablePickers = [
        { type: 'emoji', label: '表情', component: EmojiPicker },
        { type: 'gif', label: 'GIF', component: GifPicker }
    ];
    
    return (
        <PickerContent 
            PICKERS={PICKER_TYPES}
            close={() => closePicker()}
            pickers={availablePickers}
            state={pickerState}
            storeScroll={(pos) => storeScrollPosition(pos)}
        />
    );
};
```

### 3. 聊天中的选择器

```javascript
// 在聊天界面中使用选择器
const ChatPicker = ({ onSelect, onClose }) => {
    const [pickerState, setPickerState] = useState({
        currentPicker: 'emoji',
        scrollPosition: 0
    });
    
    const handlePickerSelect = (item) => {
        onSelect(item);
        onClose();
    };
    
    return (
        <div class="chat-picker">
            <PickerContent 
                PICKERS={PICKER_CONSTANTS}
                close={onClose}
                pickers={getChatPickers()}
                state={pickerState}
                storeScroll={(pos) => setPickerState(prev => ({
                    ...prev,
                    scrollPosition: pos
                }))}
            />
        </div>
    );
};
```

### 4. 移动端选择器

```javascript
// 移动端优化的选择器
const MobilePicker = ({ isVisible, onClose }) => {
    const pickerState = {
        currentPicker: 'emoji',
        scrollPosition: 0,
        isMobile: true
    };
    
    if (!isVisible) return null;
    
    return (
        <div class="mobile-picker-overlay">
            <div class="mobile-picker-container">
                <div class="picker-header">
                    <h3>选择表情</h3>
                    <button onClick={onClose}>×</button>
                </div>
                <PickerContent 
                    PICKERS={MOBILE_PICKER_TYPES}
                    close={onClose}
                    pickers={getMobilePickers()}
                    state={pickerState}
                    storeScroll={(pos) => saveMobileScrollPosition(pos)}
                />
            </div>
        </div>
    );
};
```

## 选择器类型

### 1. 选择器常量定义

```javascript
// 选择器类型常量
const PICKER_TYPES = {
    EMOJI: 'emoji',
    GIF: 'gif',
    STICKER: 'sticker',
    ATTACHMENT: 'attachment'
};

// 选择器配置
const PICKER_CONFIGS = {
    [PICKER_TYPES.EMOJI]: {
        label: '表情',
        icon: 'fa-smile-o',
        component: EmojiPicker,
        enabled: true
    },
    [PICKER_TYPES.GIF]: {
        label: 'GIF',
        icon: 'fa-file-image-o',
        component: GifPicker,
        enabled: isGifEnabled()
    },
    [PICKER_TYPES.STICKER]: {
        label: '贴纸',
        icon: 'fa-star',
        component: StickerPicker,
        enabled: isStickerEnabled()
    }
};
```

### 2. 动态选择器加载

```javascript
// 动态加载选择器
const loadPickerComponent = async (pickerType) => {
    switch (pickerType) {
        case PICKER_TYPES.EMOJI:
            return await import('@web/core/emoji_picker/emoji_picker');
        case PICKER_TYPES.GIF:
            return await import('@mail/core/common/gif_picker');
        case PICKER_TYPES.STICKER:
            return await import('@mail/core/common/sticker_picker');
        default:
            throw new Error(`Unknown picker type: ${pickerType}`);
    }
};
```

### 3. 选择器过滤

```javascript
// 根据上下文过滤可用选择器
const getAvailablePickers = (context) => {
    const allPickers = Object.values(PICKER_CONFIGS);
    
    return allPickers.filter(picker => {
        // 检查是否启用
        if (!picker.enabled) return false;
        
        // 检查权限
        if (picker.requiresPermission && !hasPermission(picker.permission)) {
            return false;
        }
        
        // 检查上下文
        if (context.isMobile && !picker.mobileSupported) {
            return false;
        }
        
        return true;
    });
};
```

## 状态管理

### 1. 选择器状态

```javascript
// 选择器状态结构
const createPickerState = (initialPicker = 'emoji') => ({
    currentPicker: initialPicker,
    scrollPosition: 0,
    searchTerm: '',
    recentItems: [],
    favorites: [],
    categories: [],
    loading: false,
    error: null
});
```

### 2. 状态更新

```javascript
// 状态更新函数
const updatePickerState = (currentState, updates) => {
    return {
        ...currentState,
        ...updates,
        lastUpdated: Date.now()
    };
};

// 切换选择器
const switchPicker = (state, newPickerType) => {
    return updatePickerState(state, {
        currentPicker: newPickerType,
        scrollPosition: 0,
        searchTerm: ''
    });
};
```

### 3. 滚动位置管理

```javascript
// 滚动位置存储
const scrollPositions = new Map();

const storeScrollPosition = (pickerId, position) => {
    scrollPositions.set(pickerId, position);
};

const restoreScrollPosition = (pickerId) => {
    return scrollPositions.get(pickerId) || 0;
};

// 清理滚动位置
const clearScrollPositions = () => {
    scrollPositions.clear();
};
```

## 事件处理

### 1. 点击事件

```javascript
// 扩展的点击事件处理
const handleClick = (event) => {
    // 标记事件已处理
    markEventHandled(event, "PickerContent.onClick");
    
    // 检查点击目标
    const target = event.target;
    
    if (target.classList.contains('picker-item')) {
        handlePickerItemClick(target);
    } else if (target.classList.contains('picker-tab')) {
        handlePickerTabClick(target);
    }
};
```

### 2. 键盘事件

```javascript
// 键盘导航支持
const handleKeydown = (event) => {
    switch (event.key) {
        case 'Escape':
            closePicker();
            break;
        case 'Tab':
            handleTabNavigation(event);
            break;
        case 'Enter':
            handleEnterKey(event);
            break;
        case 'ArrowUp':
        case 'ArrowDown':
        case 'ArrowLeft':
        case 'ArrowRight':
            handleArrowNavigation(event);
            break;
    }
};
```

### 3. 搜索事件

```javascript
// 搜索处理
const handleSearch = (searchTerm) => {
    updatePickerState(currentState, {
        searchTerm: searchTerm,
        loading: true
    });
    
    // 执行搜索
    performSearch(searchTerm).then(results => {
        updatePickerState(currentState, {
            searchResults: results,
            loading: false
        });
    });
};
```

## 样式和布局

### 1. 基础样式

```css
.picker-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    
    .picker-tabs {
        display: flex;
        border-bottom: 1px solid #eee;
        
        .picker-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            cursor: pointer;
            border: none;
            background: none;
            
            &.active {
                background: #f8f9fa;
                border-bottom: 2px solid #007bff;
            }
            
            &:hover {
                background: #f8f9fa;
            }
        }
    }
    
    .picker-body {
        flex: 1;
        overflow-y: auto;
        padding: 8px;
    }
}
```

### 2. 响应式设计

```css
@media (max-width: 768px) {
    .picker-content {
        height: 60vh;
        
        .picker-tabs {
            .picker-tab {
                padding: 16px 8px;
                font-size: 14px;
            }
        }
        
        .picker-body {
            padding: 12px;
        }
    }
}
```

## 性能优化

### 1. 懒加载

```javascript
// 选择器懒加载
const LazyPickerContent = ({ pickerType, ...props }) => {
    const [PickerComponent, setPickerComponent] = useState(null);
    
    useEffect(() => {
        loadPickerComponent(pickerType).then(component => {
            setPickerComponent(component);
        });
    }, [pickerType]);
    
    if (!PickerComponent) {
        return <div class="picker-loading">加载中...</div>;
    }
    
    return <PickerContent {...props} />;
};
```

### 2. 虚拟化

```javascript
// 大量内容的虚拟化
const VirtualizedPickerContent = ({ items, itemHeight = 40 }) => {
    const [visibleRange, setVisibleRange] = useState({ start: 0, end: 20 });
    
    const visibleItems = items.slice(visibleRange.start, visibleRange.end);
    
    return (
        <div class="virtualized-picker">
            {visibleItems.map(item => (
                <PickerItem key={item.id} item={item} />
            ))}
        </div>
    );
};
```

## 设计模式

### 1. 容器模式 (Container Pattern)
- 作为选择器内容的容器
- 管理子组件的生命周期

### 2. 策略模式 (Strategy Pattern)
- 根据选择器类型采用不同的显示策略
- 可配置的选择器组件

### 3. 观察者模式 (Observer Pattern)
- 监听状态变化
- 响应用户交互事件

## 注意事项

1. **事件处理**: 正确处理事件冒泡和传播
2. **性能考虑**: 避免不必要的重新渲染
3. **用户体验**: 提供流畅的交互体验
4. **可访问性**: 支持键盘导航和屏幕阅读器

## 扩展建议

1. **自定义选择器**: 支持插件式的自定义选择器
2. **主题支持**: 支持多种视觉主题
3. **搜索优化**: 改进搜索算法和用户体验
4. **缓存机制**: 优化内容加载和缓存
5. **动画效果**: 添加切换和选择的动画

该组件为邮件系统提供了统一的选择器内容容器，支持多种类型的选择器，增强了用户的输入体验。

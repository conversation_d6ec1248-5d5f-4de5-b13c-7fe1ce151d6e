# Message Reaction List - 消息反应列表

## 概述

`message_reaction_list.js` 实现了 Odoo 邮件系统中的消息反应列表组件，用于显示消息的所有表情反应。该组件支持悬停预览、点击切换反应、移动端右键菜单等功能，提供了丰富的表情反应交互体验，是消息反应系统的核心显示组件。

## 文件信息
- **路径**: `/mail/static/src/core/common/message_reaction_list.js`
- **行数**: 111
- **模块**: `@mail/core/common/message_reaction_list`

## 依赖关系

```javascript
// 核心依赖
'@mail/utils/common/hooks'              // 邮件工具钩子
'@odoo/owl'                            // OWL 框架
'@web/core/dropdown/dropdown'          // 下拉框组件
'@web/core/dropdown/dropdown_hooks'    // 下拉框钩子
'@web/core/emoji_picker/emoji_picker'  // 表情选择器
'@web/core/l10n/translation'           // 国际化
'@web/core/utils/hooks'                // Web 核心钩子
```

## 组件定义

### MessageReactionList 类

```javascript
class MessageReactionList extends Component {
    static template = "mail.MessageReactionList";
    static components = { Dropdown };
    static props = ["message", "openReactionMenu", "reaction"];
}
```

## Props 配置

### Props 定义

```typescript
interface Props {
    message: import("models").Message;                    // 消息对象
    openReactionMenu: function;                          // 打开反应菜单回调
    reaction: import("models").MessageReactions;         // 反应对象
}
```

### Props 详细说明

- **`message`** (必需):
  - 类型: Message 模型
  - 用途: 包含反应的消息对象
  - 功能: 提供反应操作的上下文

- **`openReactionMenu`** (必需):
  - 类型: 函数
  - 用途: 打开反应菜单的回调
  - 触发: 移动端右键菜单时调用

- **`reaction`** (必需):
  - 类型: MessageReactions 模型
  - 用途: 要显示的反应对象
  - 功能: 提供反应的详细信息

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    
    // 表情加载器
    this.loadEmoji = loadEmoji;
    
    // 服务依赖
    this.store = useState(useService("mail.store"));
    this.ui = useService("ui");
    
    // 悬停处理
    this.hover = useHover(["reactionButton", "reactionList*"], {
        onHover: () => (this.preview.isOpen = true),
        onAway: () => (this.preview.isOpen = false),
    });
    
    // 表情加载状态
    this.state = useState({ emojiLoaded: Boolean(loader.loaded) });
    if (!loader.loaded) {
        loader.onEmojiLoaded(() => (this.state.emojiLoaded = true));
    }
    
    // 生命周期钩子
    onMounted(() => void this.state.emojiLoaded);
    onPatched(() => void this.state.emojiLoaded);
    
    // 预览下拉框
    this.preview = useDropdownState();
}
```

**初始化内容**:
- 表情加载器和状态管理
- 邮件存储和UI服务
- 悬停交互处理
- 预览下拉框状态

## 核心功能

### 1. 预览文本生成

```javascript
previewText(reaction) {
    const { count, content: emoji } = reaction;
    const personNames = reaction.personas
          .slice(0, 3)
          .map(persona => persona.name);
    const shortcode = loader.loaded?.emojiValueToShortcode?.[emoji] ?? "?";
    
    switch (count) {
        case 1:
            return _t("%(emoji)s reacted by %(person)s", {
                emoji: shortcode,
                person: personNames[0],
            });
        case 2:
            return _t("%(emoji)s reacted by %(person1)s and %(person2)s", {
                emoji: shortcode,
                person1: personNames[0],
                person2: personNames[1],
            });
        case 3:
            return _t("%(emoji)s reacted by %(person1)s, %(person2)s, and %(person3)s", {
                emoji: shortcode,
                person1: personNames[0],
                person2: personNames[1],
                person3: personNames[2],
            });
        case 4:
            return _t(
                "%(emoji)s reacted by %(person1)s, %(person2)s, %(person3)s, and 1 other",
                {
                    emoji: shortcode,
                    person1: personNames[0],
                    person2: personNames[1],
                    person3: personNames[2],
                }
            );
        default:
            return _t(
                "%(emoji)s reacted by %(person1)s, %(person2)s, %(person3)s, and %(count)s others",
                {
                    count: count - 3,
                    emoji: shortcode,
                    person1: personNames[0],
                    person2: personNames[1],
                    person3: personNames[2],
                }
            );
    }
}
```

**预览文本逻辑**:
- **1个人**: 显示单个用户名
- **2个人**: 显示两个用户名
- **3个人**: 显示三个用户名
- **4个人**: 显示三个用户名 + "1 other"
- **更多**: 显示三个用户名 + "X others"

### 2. 自己反应检查

```javascript
hasSelfReacted(reaction) {
    return this.store.self.in(reaction.personas);
}
```

**功能**:
- 检查当前用户是否已添加此反应
- 用于控制反应按钮的状态
- 决定点击时的操作类型

### 3. 反应点击处理

```javascript
onClickReaction(reaction) {
    if (this.hasSelfReacted(reaction)) {
        reaction.remove();
    } else {
        this.props.message.react(reaction.content);
    }
}
```

**点击逻辑**:
- **已反应**: 移除当前用户的反应
- **未反应**: 添加当前用户的反应
- **切换模式**: 实现反应的开关功能

### 4. 右键菜单处理

```javascript
onContextMenu(ev) {
    if (this.ui.isSmall) {
        ev.preventDefault();
        this.props.openReactionMenu();
    }
}
```

**移动端处理**:
- 在小屏幕设备上阻止默认右键菜单
- 打开自定义的反应菜单
- 提供移动端友好的交互方式

## 使用场景

### 1. 消息中的反应显示

```javascript
// 在消息组件中显示反应列表
const renderMessageReactions = (message) => {
    return message.reactions.map(reaction => (
        <MessageReactionList 
            message={message}
            reaction={reaction}
            openReactionMenu={() => openEmojiPicker(message)}
        />
    ));
};
```

### 2. 反应统计显示

```javascript
// 显示反应统计信息
const ReactionStats = ({ message }) => {
    const totalReactions = message.reactions.reduce((sum, r) => sum + r.count, 0);
    const uniqueEmojis = message.reactions.length;
    
    return (
        <div class="reaction-stats">
            <span>{totalReactions} 个反应</span>
            <span>{uniqueEmojis} 种表情</span>
            <div class="reaction-list">
                {message.reactions.map(reaction => (
                    <MessageReactionList 
                        message={message}
                        reaction={reaction}
                        openReactionMenu={() => showReactionMenu(message)}
                    />
                ))}
            </div>
        </div>
    );
};
```

### 3. 反应预览功能

```javascript
// 悬停预览反应详情
const ReactionWithPreview = ({ message, reaction }) => {
    return (
        <div class="reaction-container">
            <MessageReactionList 
                message={message}
                reaction={reaction}
                openReactionMenu={() => openReactionMenu(message)}
            />
            {/* 预览下拉框会自动显示 */}
        </div>
    );
};
```

### 4. 批量反应管理

```javascript
// 批量管理消息反应
const BatchReactionManager = ({ messages }) => {
    const allReactions = messages.flatMap(m => m.reactions);
    const groupedReactions = groupReactionsByEmoji(allReactions);
    
    return (
        <div class="batch-reactions">
            {Object.entries(groupedReactions).map(([emoji, reactions]) => (
                <div class="emoji-group">
                    <span class="emoji">{emoji}</span>
                    <span class="count">{reactions.length}</span>
                    {reactions.map(reaction => (
                        <MessageReactionList 
                            message={reaction.message}
                            reaction={reaction}
                            openReactionMenu={() => {}}
                        />
                    ))}
                </div>
            ))}
        </div>
    );
};
```

## 悬停交互

### 1. 悬停状态管理

```javascript
// 悬停钩子配置
this.hover = useHover(["reactionButton", "reactionList*"], {
    onHover: () => (this.preview.isOpen = true),
    onAway: () => (this.preview.isOpen = false),
});
```

**悬停逻辑**:
- **目标元素**: 反应按钮和反应列表
- **悬停时**: 打开预览下拉框
- **离开时**: 关闭预览下拉框

### 2. 预览内容

```javascript
// 预览下拉框内容
const renderPreviewContent = (reaction) => {
    return `
        <div class="reaction-preview">
            <div class="preview-header">
                <span class="emoji">${reaction.content}</span>
                <span class="count">${reaction.count}</span>
            </div>
            <div class="preview-users">
                ${reaction.personas.map(persona => `
                    <div class="user-item">
                        <img src="${persona.avatarUrl}" alt="${persona.name}" />
                        <span>${persona.name}</span>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
};
```

### 3. 延迟显示

```javascript
// 延迟显示预览以避免闪烁
const delayedPreview = debounce(() => {
    this.preview.isOpen = true;
}, 300);

const quickHide = () => {
    this.preview.isOpen = false;
};
```

## 表情加载

### 1. 表情状态检查

```javascript
// 检查表情是否已加载
this.state = useState({ emojiLoaded: Boolean(loader.loaded) });

if (!loader.loaded) {
    loader.onEmojiLoaded(() => (this.state.emojiLoaded = true));
}
```

### 2. 表情短代码

```javascript
// 获取表情的短代码
const getEmojiShortcode = (emoji) => {
    return loader.loaded?.emojiValueToShortcode?.[emoji] ?? "?";
};

// 在预览文本中使用短代码
const shortcode = getEmojiShortcode(reaction.content);
```

### 3. 表情渲染

```javascript
// 安全的表情渲染
const renderEmoji = (emoji) => {
    if (this.state.emojiLoaded) {
        return this.loadEmoji(emoji);
    } else {
        return emoji; // 回退到原始字符
    }
};
```

## 响应式设计

### 1. 移动端适配

```javascript
// 移动端特殊处理
const handleMobileInteraction = (event, reaction) => {
    if (this.ui.isSmall) {
        // 移动端使用长按或右键
        if (event.type === 'contextmenu') {
            event.preventDefault();
            this.props.openReactionMenu();
        } else {
            // 普通点击切换反应
            this.onClickReaction(reaction);
        }
    }
};
```

### 2. 触摸友好

```css
/* 移动端样式 */
@media (max-width: 768px) {
    .message-reaction-list {
        .reaction-button {
            min-height: 44px; /* 触摸友好的最小高度 */
            padding: 8px 12px;
            margin: 4px;
        }
        
        .reaction-preview {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            border-radius: 16px 16px 0 0;
        }
    }
}
```

## 性能优化

### 1. 虚拟化

```javascript
// 大量反应时的虚拟化
const VirtualizedReactionList = ({ reactions, maxVisible = 10 }) => {
    const [visibleReactions, setVisibleReactions] = useState(
        reactions.slice(0, maxVisible)
    );
    
    const loadMore = () => {
        const nextBatch = reactions.slice(
            visibleReactions.length, 
            visibleReactions.length + maxVisible
        );
        setVisibleReactions(prev => [...prev, ...nextBatch]);
    };
    
    return (
        <div class="virtualized-reactions">
            {visibleReactions.map(reaction => (
                <MessageReactionList {...props} reaction={reaction} />
            ))}
            {visibleReactions.length < reactions.length && (
                <button onClick={loadMore}>显示更多</button>
            )}
        </div>
    );
};
```

### 2. 缓存优化

```javascript
// 缓存预览文本
const previewTextCache = new Map();

const getCachedPreviewText = (reaction) => {
    const cacheKey = `${reaction.content}_${reaction.count}_${reaction.personas.map(p => p.id).join(',')}`;
    
    if (previewTextCache.has(cacheKey)) {
        return previewTextCache.get(cacheKey);
    }
    
    const text = generatePreviewText(reaction);
    previewTextCache.set(cacheKey, text);
    return text;
};
```

## 可访问性

### 1. ARIA 标签

```xml
<!-- 可访问性标记 -->
<button 
    class="reaction-button"
    role="button"
    aria-label="反应：{reaction.content}，{reaction.count} 个用户"
    aria-pressed="{hasSelfReacted(reaction)}"
    tabindex="0"
>
    <span class="emoji" aria-hidden="true">{reaction.content}</span>
    <span class="count">{reaction.count}</span>
</button>
```

### 2. 键盘导航

```javascript
// 键盘导航支持
const handleKeydown = (event, reaction) => {
    switch (event.key) {
        case 'Enter':
        case ' ':
            event.preventDefault();
            this.onClickReaction(reaction);
            break;
        case 'Escape':
            this.preview.isOpen = false;
            break;
    }
};
```

## 设计模式

### 1. 组合模式 (Composition Pattern)
- 组合表情、计数和用户列表
- 提供统一的反应显示接口

### 2. 状态模式 (State Pattern)
- 根据用户反应状态显示不同样式
- 已反应 vs 未反应的不同状态

### 3. 观察者模式 (Observer Pattern)
- 监听反应数据变化
- 响应悬停和点击事件

## 注意事项

1. **性能考虑**: 大量反应时的渲染性能
2. **用户体验**: 提供即时的反应反馈
3. **移动端适配**: 确保触摸友好的交互
4. **可访问性**: 支持键盘导航和屏幕阅读器

## 扩展建议

1. **反应动画**: 添加反应变化的动画效果
2. **自定义表情**: 支持自定义表情符号
3. **反应分组**: 支持按类型分组显示反应
4. **反应历史**: 记录反应的历史变化
5. **批量操作**: 支持批量管理反应

该组件为消息反应系统提供了完整的显示和交互功能，增强了用户的情感表达和社交互动体验。

# Message Action Menu Mobile - 移动端消息操作菜单

## 概述

`message_action_menu_mobile.js` 实现了 Odoo 邮件系统中专为移动端设计的消息操作菜单组件。该组件以模态对话框的形式展示消息操作选项，针对触摸屏设备进行了优化，提供了友好的移动端用户体验，是移动端邮件应用的重要交互组件。

## 文件信息
- **路径**: `/mail/static/src/core/common/message_action_menu_mobile.js`
- **行数**: 65
- **模块**: `@mail/core/common/message_action_menu_mobile`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                           // OWL 框架
'@web/core/dialog/dialog'             // 对话框组件
'@mail/core/common/message_actions'   // 消息操作钩子
'@web/core/utils/hooks'               // Web 核心钩子
```

## 组件定义

### MessageActionMenuMobile 类

```javascript
class MessageActionMenuMobile extends Component {
    static components = { Dialog };
    static props = [
        "message",
        "close?",
        "thread?", 
        "isFirstMessage?",
        "messageToReplyTo?",
        "openReactionMenu?",
        "state",
    ];
    static template = "mail.MessageActionMenuMobile";
}
```

## Props 配置

### Props 定义

```typescript
interface Props {
    message: import("@mail/core/common/message_model").Message;  // 目标消息
    close?: function;                                            // 关闭菜单回调（可选）
    thread?: import("@mail/core/common/thread_model").Thread;    // 消息所属线程（可选）
    isFirstMessage?: boolean;                                    // 是否为第一条消息（可选）
    messageToReplyTo?: import("@mail/core/common/message_model").Message;  // 回复的消息（可选）
    openReactionMenu?: function;                                 // 打开反应菜单回调（可选）
    state: object;                                              // 菜单状态对象
}
```

### Props 详细说明

- **`message`** (必需):
  - 类型: Message 模型
  - 用途: 要操作的目标消息
  - 功能: 提供操作的上下文

- **`close`** (可选):
  - 类型: 函数
  - 用途: 关闭菜单的回调函数
  - 触发: 点击背景或操作完成后

- **`thread`** (可选):
  - 类型: Thread 模型
  - 用途: 消息所属的线程
  - 功能: 提供线程上下文信息

- **`state`** (必需):
  - 类型: 对象
  - 用途: 菜单的状态管理
  - 功能: 控制菜单的显示和行为

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    
    // 邮件存储服务
    this.store = useState(useService("mail.store"));
    
    // 模态对话框引用
    this.modalRef = useChildRef();
    
    // 消息操作钩子
    this.messageActions = useMessageActions();
    
    // 绑定点击事件处理器
    this.onClickModal = this.onClickModal.bind(this);
    
    // 生命周期事件
    onMounted(() => {
        this.modalRef.el.addEventListener("click", this.onClickModal);
    });
    
    onWillUnmount(() => {
        this.modalRef.el.removeEventListener("click", this.onClickModal);
    });
}
```

**初始化内容**:
- 邮件存储服务的响应式状态
- 模态对话框的DOM引用
- 消息操作功能钩子
- 点击事件的绑定和清理

## 核心功能

### 1. 模态背景点击

```javascript
onClickModal() {
    this.props.close?.();
}
```

**功能**:
- 点击模态背景时关闭菜单
- 提供直观的关闭方式
- 符合移动端交互习惯

### 2. 消息引用

```javascript
get message() {
    return this.props.message;
}
```

### 3. 状态引用

```javascript
get state() {
    return this.props.state;
}
```

### 4. 操作点击处理

```javascript
async onClickAction(action) {
    const success = await action.onClick();
    if (action.mobileCloseAfterClick && (success || success === undefined)) {
        this.props.close?.();
    }
}
```

**操作流程**:
1. 执行操作的点击处理函数
2. 等待操作完成
3. 检查操作是否成功
4. 根据配置决定是否关闭菜单

### 5. 反应菜单

```javascript
openReactionMenu() {
    return this.props.openReactionMenu?.();
}
```

**功能**:
- 打开表情反应选择菜单
- 委托给父组件处理
- 支持表情反应功能

## 使用场景

### 1. 消息长按菜单

```javascript
// 在消息组件中长按触发操作菜单
const showMobileActionMenu = (message, event) => {
    event.preventDefault();
    
    const menuState = {
        visible: true,
        position: { x: event.clientX, y: event.clientY }
    };
    
    dialogService.add(MessageActionMenuMobile, {
        message: message,
        thread: message.thread,
        state: menuState,
        close: () => dialogService.closeAll(),
        openReactionMenu: () => openEmojiPicker(message)
    });
};
```

### 2. 消息详情页操作

```javascript
// 在消息详情页显示操作菜单
const showMessageDetailActions = (message) => {
    const menuState = {
        visible: true,
        fullScreen: true
    };
    
    dialogService.add(MessageActionMenuMobile, {
        message: message,
        thread: message.thread,
        state: menuState,
        isFirstMessage: message.isFirst,
        close: () => {
            dialogService.closeAll();
            navigateBack();
        }
    });
};
```

### 3. 回复消息操作

```javascript
// 回复消息时的操作菜单
const showReplyActionMenu = (originalMessage, replyMessage) => {
    dialogService.add(MessageActionMenuMobile, {
        message: replyMessage,
        thread: originalMessage.thread,
        messageToReplyTo: originalMessage,
        state: { visible: true },
        close: () => dialogService.closeAll()
    });
};
```

### 4. 批量操作模式

```javascript
// 批量选择模式下的操作菜单
const showBatchActionMenu = (selectedMessages) => {
    const primaryMessage = selectedMessages[0];
    
    dialogService.add(MessageActionMenuMobile, {
        message: primaryMessage,
        thread: primaryMessage.thread,
        state: { 
            visible: true,
            batchMode: true,
            selectedCount: selectedMessages.length
        },
        close: () => {
            exitBatchMode();
            dialogService.closeAll();
        }
    });
};
```

## 移动端优化

### 1. 触摸友好的界面

```css
.message-action-menu-mobile {
    /* 大按钮，易于触摸 */
    .action-button {
        min-height: 48px;
        padding: 12px 16px;
        font-size: 16px;
        border-radius: 8px;
        margin: 4px 0;
    }
    
    /* 清晰的视觉分隔 */
    .action-separator {
        height: 1px;
        background: #e0e0e0;
        margin: 8px 0;
    }
}
```

### 2. 响应式布局

```css
@media (max-width: 768px) {
    .message-action-menu-mobile {
        /* 全屏模态 */
        .modal-dialog {
            margin: 0;
            max-width: 100%;
            height: 100vh;
        }
        
        /* 底部弹出样式 */
        .modal-content {
            border-radius: 16px 16px 0 0;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
        }
    }
}
```

### 3. 手势支持

```javascript
// 添加手势支持
const addGestureSupport = (menuElement) => {
    let startY = 0;
    let currentY = 0;
    
    const handleTouchStart = (e) => {
        startY = e.touches[0].clientY;
    };
    
    const handleTouchMove = (e) => {
        currentY = e.touches[0].clientY;
        const deltaY = currentY - startY;
        
        // 向下滑动关闭菜单
        if (deltaY > 50) {
            this.props.close?.();
        }
    };
    
    menuElement.addEventListener('touchstart', handleTouchStart);
    menuElement.addEventListener('touchmove', handleTouchMove);
};
```

## 操作配置

### 1. 移动端特定操作

```javascript
// 移动端操作配置
const mobileActionConfig = {
    reply: {
        icon: 'fa-reply',
        label: '回复',
        mobileCloseAfterClick: true,
        order: 1
    },
    forward: {
        icon: 'fa-share',
        label: '转发',
        mobileCloseAfterClick: true,
        order: 2
    },
    star: {
        icon: 'fa-star',
        label: '收藏',
        mobileCloseAfterClick: false,
        order: 3
    },
    delete: {
        icon: 'fa-trash',
        label: '删除',
        mobileCloseAfterClick: true,
        order: 10,
        destructive: true
    }
};
```

### 2. 条件显示操作

```javascript
// 根据条件显示操作
const getAvailableActions = (message, thread, user) => {
    const actions = [];
    
    // 回复操作
    if (thread.canReply) {
        actions.push(mobileActionConfig.reply);
    }
    
    // 转发操作
    if (message.canForward) {
        actions.push(mobileActionConfig.forward);
    }
    
    // 收藏操作
    actions.push({
        ...mobileActionConfig.star,
        label: message.isStarred ? '取消收藏' : '收藏',
        icon: message.isStarred ? 'fa-star' : 'fa-star-o'
    });
    
    // 删除操作（仅作者或管理员）
    if (message.canDelete(user)) {
        actions.push(mobileActionConfig.delete);
    }
    
    return actions.sort((a, b) => a.order - b.order);
};
```

### 3. 操作分组

```javascript
// 操作分组显示
const groupActions = (actions) => {
    return {
        primary: actions.filter(a => a.order <= 5),
        secondary: actions.filter(a => a.order > 5 && a.order <= 8),
        destructive: actions.filter(a => a.destructive)
    };
};
```

## 动画效果

### 1. 弹出动画

```css
.message-action-menu-mobile {
    /* 弹出动画 */
    .modal-content {
        animation: slideUp 0.3s ease-out;
    }
    
    @keyframes slideUp {
        from {
            transform: translateY(100%);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
}
```

### 2. 操作反馈

```javascript
// 操作点击反馈
const addClickFeedback = (actionElement) => {
    actionElement.addEventListener('touchstart', () => {
        actionElement.classList.add('pressed');
    });
    
    actionElement.addEventListener('touchend', () => {
        setTimeout(() => {
            actionElement.classList.remove('pressed');
        }, 150);
    });
};
```

## 可访问性

### 1. 屏幕阅读器支持

```xml
<!-- 可访问性标记 -->
<div 
    class="message-action-menu-mobile"
    role="dialog"
    aria-modal="true"
    aria-labelledby="menu-title"
    aria-describedby="menu-description"
>
    <h2 id="menu-title">消息操作</h2>
    <p id="menu-description">选择要执行的操作</p>
    
    <div role="menu">
        <button role="menuitem" aria-label="回复消息">
            回复
        </button>
        <button role="menuitem" aria-label="转发消息">
            转发
        </button>
    </div>
</div>
```

### 2. 键盘导航

```javascript
// 键盘导航支持
const handleKeyNavigation = (event) => {
    switch (event.key) {
        case 'Escape':
            this.props.close?.();
            break;
        case 'ArrowDown':
            focusNextAction();
            break;
        case 'ArrowUp':
            focusPreviousAction();
            break;
        case 'Enter':
        case ' ':
            executeCurrentAction();
            break;
    }
};
```

## 性能优化

### 1. 懒加载操作

```javascript
// 懒加载操作列表
const lazyLoadActions = async (message) => {
    const baseActions = getBaseActions(message);
    
    // 异步加载扩展操作
    const extendedActions = await loadExtendedActions(message);
    
    return [...baseActions, ...extendedActions];
};
```

### 2. 操作缓存

```javascript
// 缓存操作配置
const actionCache = new Map();

const getCachedActions = (messageId) => {
    if (actionCache.has(messageId)) {
        return actionCache.get(messageId);
    }
    
    const actions = generateActions(messageId);
    actionCache.set(messageId, actions);
    return actions;
};
```

## 设计模式

### 1. 命令模式 (Command Pattern)
- 将操作封装为命令对象
- 支持撤销和重做

### 2. 策略模式 (Strategy Pattern)
- 根据设备类型采用不同的菜单策略
- 移动端 vs 桌面端的不同实现

### 3. 观察者模式 (Observer Pattern)
- 监听操作完成事件
- 响应菜单状态变化

## 注意事项

1. **触摸体验**: 确保按钮大小适合触摸操作
2. **性能考虑**: 避免过度的动画和效果
3. **网络状态**: 处理离线状态下的操作
4. **设备适配**: 适配不同尺寸的移动设备

## 扩展建议

1. **自定义操作**: 支持插件化的自定义操作
2. **快捷操作**: 提供常用操作的快捷方式
3. **操作历史**: 记录用户的操作偏好
4. **语音操作**: 支持语音命令操作
5. **手势识别**: 支持更多手势操作

该组件为移动端用户提供了直观而高效的消息操作界面，是移动端邮件应用用户体验的重要组成部分。

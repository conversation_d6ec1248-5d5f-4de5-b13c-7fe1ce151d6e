# Chat Hub Model - 聊天中心模型

## 概述

`chat_hub_model.js` 定义了 Odoo 邮件系统中的聊天中心模型类，负责管理多个聊天窗口的布局、状态和交互。该模型处理聊天窗口的打开、折叠、大小调整等功能，并根据屏幕尺寸动态计算最佳的窗口布局，是多窗口聊天体验的核心数据模型。

## 文件信息
- **路径**: `/mail/static/src/core/common/chat_hub_model.js`
- **行数**: 105
- **模块**: `@mail/core/common/chat_hub_model`

## 依赖关系

```javascript
// 核心依赖
'@web/core/browser/browser'    // 浏览器工具
'@mail/core/common/record'     // Record 基类
```

## 类定义

### ChatHub 类

```javascript
class ChatHub extends Record {
    // 布局常量定义
    // 聊天窗口集合管理
    // 动态布局计算
}
```

## 布局常量

### 气泡相关常量

```javascript
BUBBLE = 56;           // 气泡宽度，与 $o-mail-ChatHub-bubblesWidth 相同
BUBBLE_START = 15;     // 气泡起始位置，与 $o-mail-ChatHub-bubblesStart 相同
BUBBLE_LIMIT = 7;      // 气泡数量限制
BUBBLE_OUTER = 10;     // 气泡外边距，与 $o-mail-ChatHub-bubblesMargin 相同
```

### 窗口相关常量

```javascript
WINDOW_GAP = 10;       // 窗口间隙（单端），左右两端需乘以2
WINDOW_INBETWEEN = 5;  // 窗口之间的间距
WINDOW = 360;          // 标准窗口宽度，与 $o-mail-ChatWindow-width 相同
WINDOW_LARGE = 510;    // 大窗口宽度，与 $o-mail-ChatWindow-widthLarge 相同
```

**常量说明**:
- 这些常量与CSS变量保持同步
- 确保JavaScript计算与样式表一致
- 提供精确的布局控制

## 核心属性

### 窗口大小设置

```javascript
isBig = Record.attr(false, {
    compute() {
        return browser.localStorage.getItem("mail.user_setting.chat_window_big") === "true";
    },
    onUpdate() {
        if (this.isBig) {
            browser.localStorage.setItem(
                "mail.user_setting.chat_window_big",
                this.isBig.toString()
            );
        } else {
            browser.localStorage.removeItem("mail.user_setting.chat_window_big");
        }
    },
});
```

**功能说明**:
- **计算**: 从本地存储读取用户偏好
- **更新**: 自动同步到本地存储
- **持久化**: 用户设置在会话间保持

### 紧凑模式

```javascript
compact = false;  // 是否为紧凑模式
```

**用途**:
- 控制聊天中心的显示模式
- 紧凑模式下占用更少空间
- 适应不同的屏幕尺寸

### 聊天窗口集合

#### 打开的窗口

```javascript
opened = Record.many("ChatWindow", {
    inverse: "hubAsOpened",
    onAdd(r) {
        this.onRecompute();
    },
    onDelete() {
        this.onRecompute();
    },
});
```

**特性**:
- **排序**: 从左到右排列
- **折叠规则**: 最右侧的窗口会被折叠
- **自动重计算**: 添加/删除时触发布局重计算

#### 折叠的窗口

```javascript
folded = Record.many("ChatWindow", {
    inverse: "hubAsFolded",
    onAdd(r) {
        this.onRecompute();
    },
    onDelete() {
        this.onRecompute();
    },
});
```

**特性**:
- **排序**: 从上到下排列
- **隐藏规则**: 最底部的窗口会被隐藏
- **自动重计算**: 添加/删除时触发布局重计算

## 核心方法

### 1. 关闭所有窗口

```javascript
closeAll() {
    [...this.opened, ...this.folded].forEach((cw) => cw.close());
}
```

**功能**:
- 关闭所有打开和折叠的聊天窗口
- 使用扩展运算符合并两个数组
- 逐个调用窗口的关闭方法

### 2. 布局重计算

```javascript
onRecompute() {
    while (this.opened.length > this.maxOpened) {
        const cw = this.opened.pop();
        this.folded.unshift(cw);
    }
}
```

**重计算逻辑**:
1. 检查打开窗口数量是否超过最大限制
2. 将超出的窗口从打开列表移除
3. 将移除的窗口添加到折叠列表的开头
4. 循环直到满足限制条件

### 3. 最大打开窗口数计算

```javascript
get maxOpened() {
    const chatBubblesWidth = this.BUBBLE_START + this.BUBBLE + this.BUBBLE_OUTER * 2;
    const startGap = this.store.env.services.ui.isSmall ? 0 : this.WINDOW_GAP;
    const endGap = this.store.env.services.ui.isSmall ? 0 : this.WINDOW_GAP;
    const available = browser.innerWidth - startGap - endGap - chatBubblesWidth;
    const maxAmountWithoutHidden = Math.max(
        1,
        Math.floor(
            available / ((this.isBig ? this.WINDOW_LARGE : this.WINDOW) + this.WINDOW_INBETWEEN)
        )
    );
    return maxAmountWithoutHidden;
}
```

**计算步骤**:
1. **气泡宽度**: 计算聊天气泡占用的总宽度
2. **边距处理**: 小屏幕时不添加边距，大屏幕时添加左右边距
3. **可用空间**: 总宽度减去边距和气泡宽度
4. **窗口计算**: 根据窗口大小和间距计算最大数量
5. **最小保证**: 至少保证能显示1个窗口

**响应式特性**:
- 根据屏幕尺寸动态调整
- 考虑窗口大小设置（标准/大）
- 自动适应不同设备

### 4. 最大折叠窗口数计算

```javascript
get maxFolded() {
    const chatBubbleSpace = this.BUBBLE_START + this.BUBBLE + this.BUBBLE_OUTER * 2;
    return Math.min(this.BUBBLE_LIMIT, Math.floor(browser.innerHeight / chatBubbleSpace));
}
```

**计算逻辑**:
1. **单个气泡空间**: 计算每个折叠窗口气泡所需的垂直空间
2. **垂直容量**: 根据屏幕高度计算能容纳的气泡数量
3. **限制应用**: 应用预设的气泡数量限制
4. **取最小值**: 确保不超过硬编码限制

## 布局管理

### 窗口状态转换

```mermaid
stateDiagram-v2
    [*] --> Opened: 打开聊天
    Opened --> Folded: 超出显示限制
    Folded --> Opened: 有空间时展开
    Folded --> Hidden: 超出折叠限制
    Hidden --> Folded: 有空间时显示
    Opened --> [*]: 关闭聊天
    Folded --> [*]: 关闭聊天
```

### 空间分配策略

#### 水平空间分配

```javascript
// 可用宽度 = 总宽度 - 左边距 - 右边距 - 气泡区域
available = browser.innerWidth - startGap - endGap - chatBubblesWidth;

// 每个窗口占用 = 窗口宽度 + 窗口间距
windowSpace = (isBig ? WINDOW_LARGE : WINDOW) + WINDOW_INBETWEEN;

// 最大窗口数 = 可用宽度 / 每个窗口占用
maxWindows = Math.floor(available / windowSpace);
```

#### 垂直空间分配

```javascript
// 每个气泡占用 = 起始位置 + 气泡大小 + 外边距
bubbleSpace = BUBBLE_START + BUBBLE + BUBBLE_OUTER * 2;

// 最大气泡数 = min(硬限制, 屏幕高度 / 气泡空间)
maxBubbles = Math.min(BUBBLE_LIMIT, Math.floor(browser.innerHeight / bubbleSpace));
```

## 使用场景

### 1. 多窗口聊天管理

```javascript
// 打开新的聊天窗口
const openChat = (thread) => {
    const chatWindow = store.ChatWindow.insert({
        thread: thread,
        isOpen: true
    });
    
    // 自动添加到聊天中心
    chatHub.opened.add(chatWindow);
    // 触发布局重计算
};
```

### 2. 窗口大小切换

```javascript
// 切换窗口大小
const toggleChatSize = () => {
    chatHub.isBig = !chatHub.isBig;
    // 自动触发布局重计算
    chatHub.onRecompute();
};
```

### 3. 响应式布局调整

```javascript
// 监听窗口大小变化
window.addEventListener('resize', () => {
    chatHub.onRecompute();
});
```

### 4. 紧凑模式切换

```javascript
// 切换紧凑模式
const toggleCompactMode = () => {
    chatHub.compact = !chatHub.compact;
    // 可能需要重新计算布局
};
```

## 性能优化

### 1. 布局计算缓存

```javascript
// 缓存计算结果，避免重复计算
let cachedMaxOpened = null;
let lastWindowWidth = null;

get maxOpened() {
    if (lastWindowWidth !== browser.innerWidth) {
        cachedMaxOpened = null;
        lastWindowWidth = browser.innerWidth;
    }
    
    if (cachedMaxOpened === null) {
        cachedMaxOpened = this.calculateMaxOpened();
    }
    
    return cachedMaxOpened;
}
```

### 2. 防抖重计算

```javascript
// 防抖布局重计算
const debouncedRecompute = debounce(() => {
    chatHub.onRecompute();
}, 100);
```

### 3. 批量操作

```javascript
// 批量关闭窗口
const closeMultipleWindows = (windows) => {
    // 暂停自动重计算
    const autoRecompute = chatHub.autoRecompute;
    chatHub.autoRecompute = false;
    
    // 批量关闭
    windows.forEach(window => window.close());
    
    // 恢复自动重计算并执行一次
    chatHub.autoRecompute = autoRecompute;
    chatHub.onRecompute();
};
```

## 设计模式

### 1. 观察者模式 (Observer Pattern)
- 监听窗口集合变化
- 自动触发布局重计算

### 2. 策略模式 (Strategy Pattern)
- 根据屏幕尺寸采用不同布局策略
- 大窗口 vs 标准窗口的不同处理

### 3. 状态模式 (State Pattern)
- 窗口的不同状态（打开、折叠、隐藏）
- 紧凑模式 vs 普通模式

## 响应式设计

### 屏幕尺寸适配

```javascript
// 小屏幕适配
if (this.store.env.services.ui.isSmall) {
    // 移除边距
    startGap = 0;
    endGap = 0;
    // 可能调整窗口大小
}
```

### 动态布局调整

```javascript
// 根据可用空间动态调整
const adjustLayout = () => {
    const maxOpened = chatHub.maxOpened;
    const maxFolded = chatHub.maxFolded;
    
    // 调整窗口分布
    chatHub.onRecompute();
};
```

## 注意事项

1. **常量同步**: 确保JavaScript常量与CSS变量保持同步
2. **性能考虑**: 避免频繁的布局重计算
3. **用户体验**: 平滑的窗口状态转换
4. **边界处理**: 正确处理极端屏幕尺寸

## 扩展建议

1. **自定义布局**: 允许用户自定义窗口布局
2. **窗口分组**: 支持聊天窗口的分组管理
3. **动画效果**: 添加窗口状态转换动画
4. **记忆布局**: 记住用户的窗口布局偏好
5. **多屏支持**: 支持多显示器的窗口管理

该模型为多窗口聊天提供了智能的布局管理，确保在不同屏幕尺寸下都能提供最佳的用户体验。

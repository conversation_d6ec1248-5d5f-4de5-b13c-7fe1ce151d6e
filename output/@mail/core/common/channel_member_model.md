# Channel Member Model - 频道成员模型

## 概述

`channel_member_model.js` 定义了 Odoo 邮件系统中的频道成员模型类，用于表示频道中的成员信息。该模型管理成员的状态、权限、消息阅读状态、打字状态等信息，是频道管理和消息同步的核心数据结构。

## 文件信息
- **路径**: `/mail/static/src/core/common/channel_member_model.js`
- **行数**: 124
- **模块**: `@mail/core/common/channel_member_model`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/store_service'  // 存储服务
'@mail/core/common/record'         // Record 基类

// Web 核心依赖
'@web/core/browser/browser'        // 浏览器工具
'@web/core/l10n/dates'            // 日期本地化
'@web/core/user'                  // 用户信息
```

## 类定义

### ChannelMember 类

```javascript
class ChannelMember extends Record {
    static id = "id";               // 主键字段
    static records = {};            // 静态记录集合
}
```

## 核心属性

### 基础标识

```javascript
id: number;                        // 成员ID
create_date: string;               // 创建日期
```

### 时间戳属性

```javascript
last_interest_dt = Record.attr(undefined, { type: "datetime" });  // 最后兴趣时间
last_seen_dt = Record.attr(undefined, { type: "datetime" });      // 最后查看时间
```

**时间戳说明**:
- **`last_interest_dt`**: 成员最后对频道感兴趣的时间
- **`last_seen_dt`**: 成员最后查看频道的时间
- 使用 `Record.attr` 定义为日期时间类型

### 关系字段

#### 用户角色关系

```javascript
persona = Record.one("Persona", { inverse: "channelMembers" });
```

**功能**:
- 关联到用户角色（Persona）
- 双向关系，用户角色也有 channelMembers 集合

#### 线程关系

```javascript
thread = Record.one("Thread", { inverse: "channelMembers" });
```

**功能**:
- 关联到线程（Thread）
- 双向关系，线程也有 channelMembers 集合

#### 自身线程关系

```javascript
threadAsSelf = Record.one("Thread", {
    compute() {
        if (this.store.self?.eq(this.persona)) {
            return this.thread;
        }
    },
});
```

**计算逻辑**:
- 如果当前成员是当前用户自己
- 返回关联的线程
- 用于标识用户自己所在的线程

### 消息相关属性

#### 消息引用

```javascript
fetched_message_id = Record.one("Message");  // 已获取的消息ID
seen_message_id = Record.one("Message");     // 已查看的消息ID
```

#### 未读计数

```javascript
message_unread_counter = 0;                  // 未读消息计数
message_unread_counter_bus_id = 0;           // 未读计数总线ID
localMessageUnreadCounter = 0;               // 本地未读计数
```

#### 新消息分隔符

```javascript
new_message_separator = null;                // 新消息分隔符
localNewMessageSeparator = null;             // 本地新消息分隔符
```

### 同步机制

#### 未读同步

```javascript
syncUnread = true;                           // 是否同步未读状态

_syncUnread = Record.attr(false, {
    compute() {
        if (!this.syncUnread || !this.eq(this.thread?.selfMember)) {
            return false;
        }
        return (
            this.localNewMessageSeparator !== this.new_message_separator ||
            this.localMessageUnreadCounter !== this.message_unread_counter
        );
    },
    onUpdate() {
        if (this._syncUnread) {
            this.localNewMessageSeparator = this.new_message_separator;
            this.localMessageUnreadCounter = this.message_unread_counter;
        }
    },
});
```

**同步逻辑**:
1. **计算条件**: 检查是否需要同步未读状态
2. **比较差异**: 对比本地和服务器的未读状态
3. **自动更新**: 当检测到差异时自动同步本地状态

### 打字状态

#### 打字线程关系

```javascript
threadAsTyping = Record.one("Thread", {
    compute() {
        return this.isTyping ? this.thread : undefined;
    },
    eager: true,
    onAdd() {
        browser.clearTimeout(this.typingTimeoutId);
        this.typingTimeoutId = browser.setTimeout(
            () => (this.isTyping = false),
            Store.OTHER_LONG_TYPING
        );
    },
    onDelete() {
        browser.clearTimeout(this.typingTimeoutId);
    },
});

typingTimeoutId: number;                     // 打字超时ID
```

**打字状态管理**:
1. **状态计算**: 根据 `isTyping` 状态决定是否关联线程
2. **超时管理**: 设置打字状态的自动过期
3. **资源清理**: 在删除时清理定时器

## 计算属性

### 1. 成员名称

```javascript
get name() {
    return this.persona.name;
}
```

**功能**:
- 获取成员的显示名称
- 直接从关联的用户角色获取

### 2. 语言名称

```javascript
getLangName() {
    return this.persona.lang_name;
}
```

**功能**:
- 获取成员的语言名称
- 用于多语言环境的显示

### 3. 成员加入时间

```javascript
get memberSince() {
    return this.create_date ? deserializeDateTime(this.create_date) : undefined;
}
```

**功能**:
- 解析成员加入频道的时间
- 使用日期反序列化工具
- 返回 DateTime 对象或 undefined

### 4. 最后查看时间格式化

```javascript
get lastSeenDt() {
    return this.last_seen_dt
        ? this.last_seen_dt.toLocaleString(DateTime.TIME_24_SIMPLE, {
              locale: user.lang,
          })
        : undefined;
}
```

**功能**:
- 格式化最后查看时间
- 使用24小时简单格式
- 根据用户语言本地化

## 核心方法

### hasSeen() - 检查消息是否已查看

```javascript
hasSeen(message) {
    return this.persona.eq(message.author) || this.seen_message_id?.id >= message.id;
}
```

**检查逻辑**:
1. **作者检查**: 如果成员是消息作者，视为已查看
2. **ID比较**: 如果已查看消息ID大于等于目标消息ID，视为已查看
3. **返回结果**: 布尔值表示是否已查看

**使用场景**:
- 消息列表中显示已读/未读状态
- 计算未读消息数量
- 消息同步状态判断

## 状态管理

### 未读状态同步

#### 同步触发条件

```javascript
// 需要同步的条件
1. syncUnread 为 true
2. 当前成员是线程的自身成员
3. 本地状态与服务器状态不一致
```

#### 同步数据

```javascript
// 同步的数据项
- localNewMessageSeparator ↔ new_message_separator
- localMessageUnreadCounter ↔ message_unread_counter
```

### 打字状态管理

#### 状态生命周期

```javascript
1. 开始打字: isTyping = true
2. 设置超时: Store.OTHER_LONG_TYPING 毫秒后自动清除
3. 停止打字: isTyping = false
4. 清理资源: 清除定时器
```

#### 超时机制

```javascript
// 打字状态超时设置
this.typingTimeoutId = browser.setTimeout(
    () => (this.isTyping = false),
    Store.OTHER_LONG_TYPING
);
```

## 使用场景

### 1. 频道成员列表

```javascript
// 显示频道成员
const members = thread.channelMembers;
members.forEach(member => {
    console.log(`${member.name} - 加入时间: ${member.memberSince}`);
});
```

### 2. 消息已读状态

```javascript
// 检查消息是否被成员查看
const isMessageSeen = member.hasSeen(message);
if (isMessageSeen) {
    showReadIndicator();
} else {
    showUnreadIndicator();
}
```

### 3. 打字状态显示

```javascript
// 显示正在打字的成员
const typingMembers = thread.channelMembers.filter(member => 
    member.threadAsTyping?.eq(thread)
);
if (typingMembers.length > 0) {
    showTypingIndicator(typingMembers);
}
```

### 4. 未读计数管理

```javascript
// 获取未读消息数量
const unreadCount = member.message_unread_counter;
if (unreadCount > 0) {
    showUnreadBadge(unreadCount);
}
```

## 数据同步

### 服务器同步

```javascript
// 服务器推送的数据更新
{
    id: 123,
    message_unread_counter: 5,
    new_message_separator: 456,
    last_seen_dt: "2023-12-01T10:30:00Z"
}
```

### 本地状态更新

```javascript
// 本地状态自动同步
member.localMessageUnreadCounter = member.message_unread_counter;
member.localNewMessageSeparator = member.new_message_separator;
```

## 性能优化

### 1. 计算属性缓存

```javascript
// 使用 getter 缓存计算结果
get name() {
    return this.persona.name;  // 只在 persona 变化时重新计算
}
```

### 2. 条件计算

```javascript
// 只在必要时进行同步检查
if (!this.syncUnread || !this.eq(this.thread?.selfMember)) {
    return false;
}
```

### 3. 资源清理

```javascript
// 及时清理定时器资源
onDelete() {
    browser.clearTimeout(this.typingTimeoutId);
}
```

## 设计模式

### 1. 观察者模式 (Observer Pattern)
- 监听未读状态变化并自动同步
- 响应打字状态变化

### 2. 策略模式 (Strategy Pattern)
- 不同类型成员的不同处理策略
- 已读状态的不同判断策略

### 3. 状态模式 (State Pattern)
- 打字状态的管理
- 在线/离线状态的处理

## 注意事项

1. **时区处理**: 正确处理不同时区的时间戳
2. **状态同步**: 确保本地和服务器状态一致
3. **资源清理**: 及时清理定时器和事件监听器
4. **权限检查**: 验证成员的操作权限

## 扩展建议

1. **权限管理**: 添加更细粒度的成员权限
2. **状态历史**: 记录成员状态变化历史
3. **批量操作**: 支持批量成员操作
4. **自定义属性**: 支持自定义成员属性
5. **活跃度统计**: 添加成员活跃度统计

该模型是频道管理的核心组件，为频道成员的状态管理、消息同步和用户交互提供了完整的数据结构支持。

# Chat Hub - 聊天中心

## 概述

`chat_hub.js` 实现了 Odoo 邮件系统中的聊天中心组件，负责管理和显示多个聊天窗口。该组件提供了聊天窗口的集中管理、紧凑模式切换、拖拽移动、悬停交互等功能，是多窗口聊天体验的核心组件。

## 文件信息
- **路径**: `/mail/static/src/core/common/chat_hub.js`
- **行数**: 101
- **模块**: `@mail/core/common/chat_hub`

## 依赖关系

```javascript
// 核心组件依赖
'@mail/core/common/chat_window'       // 聊天窗口组件
'@mail/core/common/chat_bubble'       // 聊天气泡组件

// 功能钩子依赖
'@mail/utils/common/hooks'            // 邮件系统钩子
'@web/core/dropdown/dropdown'         // 下拉菜单组件
'@web/core/dropdown/dropdown_hooks'   // 下拉菜单钩子

// Web 核心依赖
'@odoo/owl'                          // OWL 框架
'@web/core/browser/browser'          // 浏览器工具
'@web/core/registry'                 // 注册表
'@web/core/utils/hooks'              // Web 核心钩子
'@web/core/l10n/translation'         // 国际化
```

## 组件定义

### ChatHub 类

```javascript
class ChatHub extends Component {
    static components = { 
        ChatBubble,   // 聊天气泡组件
        ChatWindow,   // 聊天窗口组件
        Dropdown      // 下拉菜单组件
    };
    static props = [];                    // 无外部Props
    static template = "mail.ChatHub";     // 模板名称
}
```

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    
    // 基础服务
    this.store = useState(useService("mail.store"));
    this.ui = useState(useService("ui"));
    
    // 悬停状态管理
    this.bubblesHover = useHover("bubbles");
    this.moreHover = useHover(["more-button", "more-menu*"], {
        onHover: () => (this.more.isOpen = true),
        onAway: () => (this.more.isOpen = false),
    });
    
    // 下拉菜单状态
    this.options = useDropdownState();
    this.more = useDropdownState();
    
    // DOM 引用和位置状态
    this.compactRef = useRef("compact");
    this.compactPosition = useState({ left: "auto", top: "auto" });
    
    // 窗口大小监听
    this.onResize();
    useExternalListener(browser, "resize", this.onResize);
    
    // 频道数据获取
    useEffect(() => {
        if (this.chatHub.folded.length && this.store.channels?.status === "not_fetched") {
            this.store.channels.fetch();
        }
    });
    
    // 拖拽移动功能
    useMovable({
        cursor: "grabbing",
        ref: this.compactRef,
        elements: ".o-mail-ChatHub-compact",
        onDrop: ({ top, left }) =>
            Object.assign(this.compactPosition, { left: `${left}px`, top: `${top}px` }),
    });
}
```

## 核心功能

### 1. 聊天中心引用

```javascript
get chatHub() {
    return this.store.chatHub;
}
```

**功能**:
- 获取存储中的聊天中心模型
- 提供对聊天窗口集合的访问
- 管理聊天窗口的状态和布局

### 2. 显示控制

```javascript
get isShown() {
    return !this.ui.isSmall;
}
```

**显示逻辑**:
- 只在非小屏幕设备上显示
- 移动端隐藏聊天中心
- 响应式设计的体现

### 3. 计数器功能

#### 紧凑模式计数器

```javascript
get compactCounter() {
    let counter = 0;
    const cws = this.chatHub.opened.concat(this.chatHub.folded);
    for (const chatWindow of cws) {
        counter += chatWindow.thread.importantCounter > 0 ? 1 : 0;
    }
    return counter;
}
```

**功能说明**:
- 统计所有聊天窗口中有重要消息的数量
- 包括打开和折叠的窗口
- 用于在紧凑模式下显示总计数

#### 隐藏窗口计数器

```javascript
get hiddenCounter() {
    let counter = 0;
    for (const chatWindow of this.chatHub.folded.slice(this.chatHub.maxFolded)) {
        counter += chatWindow.thread.importantCounter > 0 ? 1 : 0;
    }
    return counter;
}
```

**功能说明**:
- 统计超出最大折叠数量限制的窗口中的重要消息
- 只计算被隐藏的折叠窗口
- 用于"更多"菜单的计数显示

### 4. 聊天大小切换

```javascript
get chatSizeTransitionText() {
    return this.chatHub.isBig ? _t("Make chats smaller") : _t("Make chats bigger");
}

toggleChatSize() {
    this.chatHub.isBig = !this.chatHub.isBig;
}
```

**功能**:
- 提供聊天窗口大小的切换功能
- 动态显示切换按钮的文本
- 支持大小两种聊天窗口模式

### 5. 展开功能

```javascript
expand() {
    this.chatHub.compact = false;
    Object.assign(this.compactPosition, { left: "auto", top: "auto" });
    this.more.isOpen = this.chatHub.folded.length > this.chatHub.maxFolded;
}
```

**展开逻辑**:
1. 退出紧凑模式
2. 重置位置为自动
3. 根据折叠窗口数量决定是否打开"更多"菜单

### 6. 窗口大小响应

```javascript
onResize() {
    this.chatHub.onRecompute();
}
```

**功能**:
- 响应浏览器窗口大小变化
- 触发聊天中心的重新计算
- 自动调整聊天窗口布局

## 悬停交互

### 气泡悬停

```javascript
this.bubblesHover = useHover("bubbles");
```

**功能**:
- 管理聊天气泡区域的悬停状态
- 用于显示/隐藏相关UI元素

### 更多菜单悬停

```javascript
this.moreHover = useHover(["more-button", "more-menu*"], {
    onHover: () => (this.more.isOpen = true),
    onAway: () => (this.more.isOpen = false),
});
```

**悬停行为**:
- **悬停时**: 自动打开"更多"菜单
- **离开时**: 自动关闭"更多"菜单
- **监听元素**: 更多按钮和菜单区域

## 拖拽移动

### 紧凑模式拖拽

```javascript
useMovable({
    cursor: "grabbing",
    ref: this.compactRef,
    elements: ".o-mail-ChatHub-compact",
    onDrop: ({ top, left }) =>
        Object.assign(this.compactPosition, { left: `${left}px`, top: `${top}px` }),
});
```

**拖拽特性**:
- **光标样式**: 拖拽时显示"grabbing"光标
- **目标元素**: 紧凑模式的聊天中心
- **位置保存**: 拖拽结束后保存新位置
- **状态管理**: 更新位置状态用于样式应用

## 数据获取

### 频道数据懒加载

```javascript
useEffect(() => {
    if (this.chatHub.folded.length && this.store.channels?.status === "not_fetched") {
        this.store.channels.fetch();
    }
});
```

**加载逻辑**:
- 检查是否有折叠的聊天窗口
- 检查频道数据是否未获取
- 满足条件时自动获取频道数据
- 优化性能，按需加载

## 组件注册

### 主组件注册

```javascript
registry.category("main_components").add("mail.ChatHub", { Component: ChatHub });
```

**注册特性**:
- 注册为主要组件
- 在应用启动时自动加载
- 全局可用的聊天中心

## 使用场景

### 1. 多窗口聊天管理

```javascript
// 聊天中心自动管理多个聊天窗口
// 用户可以同时进行多个对话
// 提供统一的窗口管理界面
```

### 2. 紧凑模式

```javascript
// 在屏幕空间有限时
// 切换到紧凑模式
// 支持拖拽调整位置
```

### 3. 响应式布局

```javascript
// 根据屏幕尺寸自动调整
// 移动端隐藏聊天中心
// 桌面端提供完整功能
```

## 状态管理

### 聊天窗口状态

- **打开窗口** (`opened`): 完全展开的聊天窗口
- **折叠窗口** (`folded`): 最小化的聊天窗口
- **隐藏窗口**: 超出显示限制的窗口

### 显示模式

- **普通模式**: 标准的聊天中心显示
- **紧凑模式**: 压缩的聊天中心显示
- **大小切换**: 聊天窗口的大小模式

### 位置状态

```javascript
this.compactPosition = useState({ left: "auto", top: "auto" });
```

- 管理紧凑模式下的位置
- 支持用户自定义位置
- 拖拽后保持位置状态

## 性能优化

### 1. 懒加载

```javascript
// 只在需要时获取频道数据
if (this.chatHub.folded.length && this.store.channels?.status === "not_fetched") {
    this.store.channels.fetch();
}
```

### 2. 响应式更新

```javascript
// 使用 useState 管理响应式状态
this.store = useState(useService("mail.store"));
this.ui = useState(useService("ui"));
```

### 3. 事件优化

```javascript
// 使用防抖的窗口大小监听
useExternalListener(browser, "resize", this.onResize);
```

## 设计模式

### 1. 组合模式 (Composition Pattern)
- 组合多个聊天窗口和气泡
- 统一管理不同状态的窗口

### 2. 观察者模式 (Observer Pattern)
- 监听窗口大小变化
- 响应聊天窗口状态变化

### 3. 状态模式 (State Pattern)
- 根据不同模式显示不同界面
- 紧凑模式 vs 普通模式

### 4. 策略模式 (Strategy Pattern)
- 根据屏幕尺寸采用不同显示策略
- 移动端 vs 桌面端的不同行为

## 可访问性

### 键盘导航

- 支持Tab键在聊天窗口间切换
- 提供键盘快捷键操作

### 屏幕阅读器

- 提供适当的ARIA标签
- 支持屏幕阅读器导航

## 注意事项

1. **性能考虑**: 大量聊天窗口时的性能优化
2. **内存管理**: 及时清理不再使用的窗口
3. **状态同步**: 确保窗口状态与数据模型一致
4. **响应式设计**: 在不同屏幕尺寸下的适配

## 扩展建议

1. **窗口分组**: 支持聊天窗口的分组管理
2. **自定义布局**: 允许用户自定义窗口布局
3. **快捷操作**: 添加批量操作功能
4. **主题定制**: 支持自定义聊天中心主题
5. **性能监控**: 添加性能监控和优化

该组件是多窗口聊天体验的核心，为用户提供了高效的聊天管理界面，支持同时进行多个对话的场景。

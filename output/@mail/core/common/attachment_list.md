# Odoo 附件列表组件 (Attachment List) 学习资料

## 文件概述

**文件路径**: `output/@mail/core/common/attachment_list.js`  
**原始路径**: `/mail/static/src/core/common/attachment_list.js`  
**模块类型**: 核心公共组件 - 附件列表显示和管理  
**代码行数**: 164 行  
**依赖关系**: 
- `@odoo/owl` - OWL 框架
- `@web/core/browser/feature_detection` - 浏览器特性检测
- `@web/core/confirmation_dialog/confirmation_dialog` - 确认对话框
- `@web/core/dropdown/dropdown` - 下拉菜单组件
- `@web/core/file_viewer/file_viewer_hook` - 文件查看器钩子
- `@web/core/l10n/translation` - 国际化翻译
- `@web/core/utils/hooks` - 核心钩子
- `@web/core/utils/urls` - URL 工具

## 模块功能

附件列表组件是 Odoo 邮件系统中的核心附件管理组件。该模块提供了：
- 附件列表显示和布局
- 图片附件预览和查看
- 附件下载和删除操作
- 移动端适配和响应式设计
- 文件查看器集成
- 附件操作菜单
- 搜索高亮支持

这个组件广泛用于消息附件、邮件附件、文档附件等场景，为用户提供完整的附件管理体验。

## 附件列表组件架构

### 核心组件结构
```
Attachment List Component
├── 附件显示引擎
│   ├── 列表布局管理
│   ├── 图片预览处理
│   ├── 文件类型识别
│   └── 响应式适配
├── 操作管理系统
│   ├── 下载操作
│   ├── 删除操作
│   ├── 预览操作
│   └── 权限控制
├── 图片操作组件
│   ├── 操作菜单
│   ├── 动作处理
│   ├── 移动端适配
│   └── 状态管理
├── 文件查看器集成
│   ├── 查看器钩子
│   ├── 文件类型支持
│   ├── 预览模式
│   └── 导航控制
└── 搜索和高亮
    ├── 搜索匹配
    ├── 高亮显示
    ├── 结果过滤
    └── 导航支持
```

### 组件定义
```javascript
// 附件列表组件
class AttachmentList extends Component {
    static components = { ImageActions };
    static props = [
        "attachments",        // 附件列表
        "unlinkAttachment",   // 删除附件回调
        "imagesHeight",       // 图片高度
        "messageSearch?"      // 消息搜索（可选）
    ];
    static template = "mail.AttachmentList";
    
    setup() {
        this.ui = useState(useService("ui"));
        this.imagesWidth = 1920;  // 图片最大宽度
        this.dialog = useService("dialog");
        this.fileViewer = useFileViewer();
    }
}

// 图片操作组件
class ImageActions extends Component {
    static components = { Dropdown, DropdownItem };
    static props = ["actions", "imagesHeight"];
    static template = "mail.ImageActions";
    
    setup() {
        this.actionsMenuState = useDropdownState();
        this.isMobileOS = isMobileOS;
    }
}
```

## 核心功能详解

### 1. 附件显示管理器
```javascript
// 附件显示管理器
class AttachmentDisplayManager {
    constructor(component) {
        this.component = component;
        this.displayStrategies = new Map();
        this.layoutCalculator = new LayoutCalculator();
        this.typeDetector = new FileTypeDetector();
        this.setupDisplayManagement();
    }
    
    setupDisplayManagement() {
        // 设置显示管理
        this.displayStrategies.set('image', {
            component: 'ImageAttachment',
            preview: true,
            gallery: true,
            actions: ['view', 'download', 'delete']
        });
        
        this.displayStrategies.set('document', {
            component: 'DocumentAttachment',
            preview: false,
            gallery: false,
            actions: ['download', 'delete']
        });
        
        this.displayStrategies.set('video', {
            component: 'VideoAttachment',
            preview: true,
            gallery: false,
            actions: ['view', 'download', 'delete']
        });
        
        this.displayStrategies.set('audio', {
            component: 'AudioAttachment',
            preview: true,
            gallery: false,
            actions: ['play', 'download', 'delete']
        });
        
        this.displayStrategies.set('archive', {
            component: 'ArchiveAttachment',
            preview: false,
            gallery: false,
            actions: ['download', 'delete']
        });
        
        this.responsiveBreakpoints = {
            mobile: 768,
            tablet: 1024,
            desktop: 1920
        };
    }
    
    getDisplayStrategy(attachment) {
        // 获取显示策略
        const fileType = this.typeDetector.detectFileType(attachment);
        return this.displayStrategies.get(fileType) || this.displayStrategies.get('document');
    }
    
    calculateLayout(attachments, containerWidth, imagesHeight) {
        // 计算布局
        const imageAttachments = attachments.filter(att => 
            this.typeDetector.isImage(att)
        );
        
        const otherAttachments = attachments.filter(att => 
            !this.typeDetector.isImage(att)
        );
        
        const imageLayout = this.calculateImageLayout(
            imageAttachments, 
            containerWidth, 
            imagesHeight
        );
        
        const listLayout = this.calculateListLayout(
            otherAttachments, 
            containerWidth
        );
        
        return {
            images: imageLayout,
            list: listLayout,
            total: attachments.length
        };
    }
    
    calculateImageLayout(images, containerWidth, targetHeight) {
        // 计算图片布局
        if (images.length === 0) return { rows: [], totalHeight: 0 };
        
        const rows = [];
        let currentRow = [];
        let currentRowWidth = 0;
        const spacing = 8; // 图片间距
        
        images.forEach((image, index) => {
            const aspectRatio = this.getImageAspectRatio(image);
            const imageWidth = targetHeight * aspectRatio;
            
            if (currentRowWidth + imageWidth + spacing <= containerWidth || currentRow.length === 0) {
                // 添加到当前行
                currentRow.push({
                    attachment: image,
                    width: imageWidth,
                    height: targetHeight,
                    aspectRatio: aspectRatio
                });
                currentRowWidth += imageWidth + spacing;
            } else {
                // 开始新行
                if (currentRow.length > 0) {
                    rows.push(this.optimizeRowLayout(currentRow, containerWidth, targetHeight));
                }
                currentRow = [{
                    attachment: image,
                    width: imageWidth,
                    height: targetHeight,
                    aspectRatio: aspectRatio
                }];
                currentRowWidth = imageWidth;
            }
        });
        
        // 添加最后一行
        if (currentRow.length > 0) {
            rows.push(this.optimizeRowLayout(currentRow, containerWidth, targetHeight));
        }
        
        return {
            rows: rows,
            totalHeight: rows.length * (targetHeight + spacing)
        };
    }
    
    optimizeRowLayout(row, containerWidth, targetHeight) {
        // 优化行布局
        const spacing = 8;
        const totalSpacing = (row.length - 1) * spacing;
        const availableWidth = containerWidth - totalSpacing;
        
        // 计算总宽度比例
        const totalWidthRatio = row.reduce((sum, item) => sum + item.aspectRatio, 0);
        
        // 调整每个图片的尺寸
        const optimizedRow = row.map(item => {
            const widthRatio = item.aspectRatio / totalWidthRatio;
            const optimizedWidth = availableWidth * widthRatio;
            const optimizedHeight = optimizedWidth / item.aspectRatio;
            
            return {
                ...item,
                width: optimizedWidth,
                height: Math.min(optimizedHeight, targetHeight),
                optimized: true
            };
        });
        
        return {
            items: optimizedRow,
            height: Math.max(...optimizedRow.map(item => item.height)),
            width: containerWidth
        };
    }
    
    calculateListLayout(attachments, containerWidth) {
        // 计算列表布局
        const itemHeight = 48; // 列表项高度
        const spacing = 4;
        
        return {
            items: attachments.map((attachment, index) => ({
                attachment: attachment,
                width: containerWidth,
                height: itemHeight,
                index: index
            })),
            totalHeight: attachments.length * (itemHeight + spacing)
        };
    }
    
    getImageAspectRatio(attachment) {
        // 获取图片宽高比
        if (attachment.image_width && attachment.image_height) {
            return attachment.image_width / attachment.image_height;
        }
        
        // 默认宽高比
        return 16 / 9;
    }
    
    getResponsiveLayout(attachments, screenSize) {
        // 获取响应式布局
        let containerWidth, imagesHeight;
        
        if (screenSize <= this.responsiveBreakpoints.mobile) {
            containerWidth = screenSize - 32; // 移动端边距
            imagesHeight = 120;
        } else if (screenSize <= this.responsiveBreakpoints.tablet) {
            containerWidth = screenSize - 64; // 平板边距
            imagesHeight = 160;
        } else {
            containerWidth = Math.min(screenSize - 128, 1200); // 桌面端最大宽度
            imagesHeight = 200;
        }
        
        return this.calculateLayout(attachments, containerWidth, imagesHeight);
    }
    
    updateLayout(attachments, containerWidth, imagesHeight) {
        // 更新布局
        const layout = this.calculateLayout(attachments, containerWidth, imagesHeight);
        
        // 触发布局更新事件
        this.component.env.bus?.trigger('ATTACHMENT_LAYOUT_UPDATED', {
            layout: layout,
            attachments: attachments,
            timestamp: Date.now()
        });
        
        return layout;
    }
}
```

### 2. 文件类型检测器
```javascript
// 文件类型检测器
class FileTypeDetector {
    constructor() {
        this.typeMap = new Map();
        this.setupTypeDetection();
    }
    
    setupTypeDetection() {
        // 设置类型检测
        this.typeMap.set('image', {
            mimetypes: [
                'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
                'image/webp', 'image/svg+xml', 'image/bmp', 'image/tiff'
            ],
            extensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp', '.tiff'],
            icon: 'fa-file-image-o',
            category: 'media'
        });
        
        this.typeMap.set('video', {
            mimetypes: [
                'video/mp4', 'video/avi', 'video/mov', 'video/wmv',
                'video/flv', 'video/webm', 'video/mkv'
            ],
            extensions: ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv'],
            icon: 'fa-file-video-o',
            category: 'media'
        });
        
        this.typeMap.set('audio', {
            mimetypes: [
                'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/aac',
                'audio/flac', 'audio/wma'
            ],
            extensions: ['.mp3', '.wav', '.ogg', '.aac', '.flac', '.wma'],
            icon: 'fa-file-audio-o',
            category: 'media'
        });
        
        this.typeMap.set('document', {
            mimetypes: [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-powerpoint',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'text/plain', 'text/html', 'text/csv'
            ],
            extensions: [
                '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
                '.txt', '.html', '.csv'
            ],
            icon: 'fa-file-text-o',
            category: 'document'
        });
        
        this.typeMap.set('archive', {
            mimetypes: [
                'application/zip', 'application/x-rar-compressed',
                'application/x-tar', 'application/gzip',
                'application/x-7z-compressed'
            ],
            extensions: ['.zip', '.rar', '.tar', '.gz', '.7z'],
            icon: 'fa-file-archive-o',
            category: 'archive'
        });
        
        this.typeMap.set('code', {
            mimetypes: [
                'text/javascript', 'text/css', 'application/json',
                'text/xml', 'application/xml'
            ],
            extensions: [
                '.js', '.css', '.json', '.xml', '.html', '.php',
                '.py', '.java', '.cpp', '.c', '.h'
            ],
            icon: 'fa-file-code-o',
            category: 'code'
        });
    }
    
    detectFileType(attachment) {
        // 检测文件类型
        const mimetype = attachment.mimetype?.toLowerCase();
        const filename = attachment.name?.toLowerCase();
        
        // 首先通过 MIME 类型检测
        if (mimetype) {
            for (const [type, config] of this.typeMap) {
                if (config.mimetypes.includes(mimetype)) {
                    return type;
                }
            }
        }
        
        // 然后通过文件扩展名检测
        if (filename) {
            for (const [type, config] of this.typeMap) {
                if (config.extensions.some(ext => filename.endsWith(ext))) {
                    return type;
                }
            }
        }
        
        // 默认返回文档类型
        return 'document';
    }
    
    isImage(attachment) {
        // 判断是否为图片
        return this.detectFileType(attachment) === 'image';
    }
    
    isVideo(attachment) {
        // 判断是否为视频
        return this.detectFileType(attachment) === 'video';
    }
    
    isAudio(attachment) {
        // 判断是否为音频
        return this.detectFileType(attachment) === 'audio';
    }
    
    isDocument(attachment) {
        // 判断是否为文档
        return this.detectFileType(attachment) === 'document';
    }
    
    isArchive(attachment) {
        // 判断是否为压缩包
        return this.detectFileType(attachment) === 'archive';
    }
    
    isCode(attachment) {
        // 判断是否为代码文件
        return this.detectFileType(attachment) === 'code';
    }
    
    getFileIcon(attachment) {
        // 获取文件图标
        const type = this.detectFileType(attachment);
        const config = this.typeMap.get(type);
        return config?.icon || 'fa-file-o';
    }
    
    getFileCategory(attachment) {
        // 获取文件分类
        const type = this.detectFileType(attachment);
        const config = this.typeMap.get(type);
        return config?.category || 'other';
    }
    
    isPreviewable(attachment) {
        // 判断是否可预览
        const type = this.detectFileType(attachment);
        const previewableTypes = ['image', 'video', 'audio', 'document'];
        return previewableTypes.includes(type);
    }
    
    getPreviewUrl(attachment) {
        // 获取预览URL
        if (!this.isPreviewable(attachment)) {
            return null;
        }
        
        const type = this.detectFileType(attachment);
        
        if (type === 'image') {
            return `/web/content/${attachment.id}`;
        } else if (type === 'document' && attachment.mimetype === 'application/pdf') {
            return `/web/content/${attachment.id}?model=ir.attachment&field=datas&filename_field=name`;
        }
        
        return null;
    }
    
    addCustomType(typeName, config) {
        // 添加自定义类型
        this.typeMap.set(typeName, config);
    }
    
    removeCustomType(typeName) {
        // 移除自定义类型
        this.typeMap.delete(typeName);
    }
    
    getTypeConfig(typeName) {
        // 获取类型配置
        return this.typeMap.get(typeName);
    }
    
    getAllTypes() {
        // 获取所有类型
        return Array.from(this.typeMap.keys());
    }
}
```

### 3. 附件操作管理器
```javascript
// 附件操作管理器
class AttachmentActionManager {
    constructor(component) {
        this.component = component;
        this.actionHandlers = new Map();
        this.permissionChecker = new PermissionChecker();
        this.setupActionManagement();
    }
    
    setupActionManagement() {
        // 设置操作管理
        this.actionHandlers.set('view', this.handleView.bind(this));
        this.actionHandlers.set('download', this.handleDownload.bind(this));
        this.actionHandlers.set('delete', this.handleDelete.bind(this));
        this.actionHandlers.set('share', this.handleShare.bind(this));
        this.actionHandlers.set('edit', this.handleEdit.bind(this));
        this.actionHandlers.set('rename', this.handleRename.bind(this));
        
        this.actionConfigs = {
            'delete': {
                requiresConfirmation: true,
                confirmationTitle: '删除附件',
                confirmationMessage: '确定要删除这个附件吗？',
                dangerAction: true
            },
            'share': {
                requiresPermission: 'share',
                modalDialog: true
            },
            'edit': {
                requiresPermission: 'write',
                modalDialog: true
            }
        };
    }
    
    async handleView(attachment) {
        // 处理查看操作
        try {
            if (this.component.fileViewer) {
                const attachments = this.component.props.attachments;
                const index = attachments.indexOf(attachment);
                
                this.component.fileViewer.open(attachment, attachments, index);
            } else {
                // 回退到直接打开
                window.open(this.getAttachmentUrl(attachment), '_blank');
            }
        } catch (error) {
            console.error('查看附件失败:', error);
            this.showError('无法查看附件');
        }
    }
    
    async handleDownload(attachment) {
        // 处理下载操作
        try {
            const downloadUrl = this.getDownloadUrl(attachment);
            
            // 创建隐藏的下载链接
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = attachment.name;
            link.style.display = 'none';
            
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // 记录下载事件
            this.trackAction('download', attachment);
            
        } catch (error) {
            console.error('下载附件失败:', error);
            this.showError('下载失败');
        }
    }
    
    async handleDelete(attachment) {
        // 处理删除操作
        const config = this.actionConfigs.delete;
        
        if (config.requiresConfirmation) {
            this.component.dialog.add(ConfirmationDialog, {
                title: config.confirmationTitle,
                body: config.confirmationMessage,
                confirm: async () => {
                    await this.executeDelete(attachment);
                },
                cancel: () => {
                    // 用户取消删除
                }
            });
        } else {
            await this.executeDelete(attachment);
        }
    }
    
    async executeDelete(attachment) {
        // 执行删除操作
        try {
            if (this.component.props.unlinkAttachment) {
                await this.component.props.unlinkAttachment(attachment);
                this.showSuccess('附件已删除');
                this.trackAction('delete', attachment);
            }
        } catch (error) {
            console.error('删除附件失败:', error);
            this.showError('删除失败');
        }
    }
    
    async handleShare(attachment) {
        // 处理分享操作
        try {
            const shareUrl = this.getShareUrl(attachment);
            
            if (navigator.share) {
                // 使用原生分享API
                await navigator.share({
                    title: attachment.name,
                    url: shareUrl
                });
            } else {
                // 回退到复制链接
                await navigator.clipboard.writeText(shareUrl);
                this.showSuccess('分享链接已复制到剪贴板');
            }
            
            this.trackAction('share', attachment);
            
        } catch (error) {
            console.error('分享附件失败:', error);
            this.showError('分享失败');
        }
    }
    
    async handleEdit(attachment) {
        // 处理编辑操作
        try {
            // 打开编辑对话框
            this.component.env.services.action.doAction({
                type: 'ir.actions.act_window',
                res_model: 'ir.attachment',
                res_id: attachment.id,
                view_mode: 'form',
                views: [[false, 'form']],
                target: 'new',
                context: {
                    default_res_model: attachment.res_model,
                    default_res_id: attachment.res_id
                }
            });
            
            this.trackAction('edit', attachment);
            
        } catch (error) {
            console.error('编辑附件失败:', error);
            this.showError('编辑失败');
        }
    }
    
    async handleRename(attachment) {
        // 处理重命名操作
        try {
            const newName = await this.promptForNewName(attachment.name);
            
            if (newName && newName !== attachment.name) {
                await this.updateAttachmentName(attachment, newName);
                this.showSuccess('附件已重命名');
                this.trackAction('rename', attachment);
            }
            
        } catch (error) {
            console.error('重命名附件失败:', error);
            this.showError('重命名失败');
        }
    }
    
    async promptForNewName(currentName) {
        // 提示输入新名称
        return new Promise((resolve) => {
            const newName = prompt('请输入新的文件名:', currentName);
            resolve(newName);
        });
    }
    
    async updateAttachmentName(attachment, newName) {
        // 更新附件名称
        await this.component.env.services.orm.write(
            'ir.attachment',
            [attachment.id],
            { name: newName }
        );
        
        // 更新本地数据
        attachment.name = newName;
    }
    
    getAttachmentUrl(attachment) {
        // 获取附件URL
        return url('/web/content', {
            model: 'ir.attachment',
            id: attachment.id,
            field: 'datas',
            filename_field: 'name',
            filename: attachment.name
        });
    }
    
    getDownloadUrl(attachment) {
        // 获取下载URL
        return url('/web/content', {
            model: 'ir.attachment',
            id: attachment.id,
            field: 'datas',
            filename_field: 'name',
            filename: attachment.name,
            download: true
        });
    }
    
    getShareUrl(attachment) {
        // 获取分享URL
        return url('/web/content', {
            model: 'ir.attachment',
            id: attachment.id,
            field: 'datas',
            filename_field: 'name',
            filename: attachment.name,
            access_token: attachment.access_token
        });
    }
    
    async executeAction(actionName, attachment) {
        // 执行操作
        const handler = this.actionHandlers.get(actionName);
        
        if (!handler) {
            throw new Error(`未知的操作: ${actionName}`);
        }
        
        // 检查权限
        if (!this.permissionChecker.hasPermission(actionName, attachment)) {
            throw new Error(`没有执行 ${actionName} 操作的权限`);
        }
        
        await handler(attachment);
    }
    
    getAvailableActions(attachment) {
        // 获取可用操作
        const allActions = Array.from(this.actionHandlers.keys());
        
        return allActions.filter(action => 
            this.permissionChecker.hasPermission(action, attachment)
        );
    }
    
    trackAction(actionName, attachment) {
        // 跟踪操作
        this.component.env.bus?.trigger('ATTACHMENT_ACTION', {
            action: actionName,
            attachment: attachment,
            timestamp: Date.now()
        });
    }
    
    showSuccess(message) {
        // 显示成功消息
        this.component.env.services.notification?.add(message, {
            type: 'success'
        });
    }
    
    showError(message) {
        // 显示错误消息
        this.component.env.services.notification?.add(message, {
            type: 'danger'
        });
    }
}
```

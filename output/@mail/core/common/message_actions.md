# Message Actions - 消息操作

## 概述

`message_actions.js` 实现了 Odoo 邮件系统中的消息操作功能，定义了消息的各种操作行为（如回复、编辑、删除、标星等）。该模块使用注册表模式管理所有消息操作，并提供了一个钩子函数供组件使用，是消息交互功能的核心实现。

## 文件信息
- **路径**: `/mail/static/src/core/common/message_actions.js`
- **行数**: 286
- **模块**: `@mail/core/common/message_actions`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                    // OWL 框架
'@web/core/l10n/translation'                   // 国际化
'@web/core/network/download'                   // 下载功能
'@web/core/registry'                           // 注册表
'@web/core/utils/hooks'                        // Web 核心钩子
'@web/core/utils/concurrency'                  // 并发工具
'@web/core/emoji_picker/emoji_picker'          // 表情选择器
'@web/core/dialog/dialog'                      // 对话框

// 邮件系统依赖
'@mail/core/common/message_reaction_button'    // 消息反应按钮
'@mail/core/common/discuss_component_registry'  // 讨论组件注册表
'@mail/utils/common/hooks'                     // 邮件系统钩子
'@mail/utils/common/format'                    // 格式化工具
```

## 核心组件

### messageActionsRegistry

消息操作注册表，管理所有可用的消息操作：

```javascript
const messageActionsRegistry = registry.category("mail.message/actions");
```

### EmojiPickerMobile 组件

内置的移动端表情选择器组件：

```javascript
class EmojiPickerMobile extends Component {
    static components = { Dialog, EmojiPicker };
    static props = [...EMOJI_PICKER_PROPS, "onClose?"];
    
    get emojiPickerProps() {
        return {
            ...this.props,
            onSelect: (...args) => {
                this.props.onSelect(...args);
                this.props.close?.();  // 选择后自动关闭
            },
        };
    }
    
    setup() {
        super.setup();
        onExternalClick("root", () => this.props.close?.());
    }
}
```

## 注册的消息操作

### 1. 反应操作 (reaction)

```javascript
messageActionsRegistry.add("reaction", {
    callComponent: MessageReactionButton,
    props: (component) => ({
        message: component.props.message,
        action: messageActionsRegistry.get("reaction"),
    }),
    condition: (component) => component.props.message.canAddReaction(component.props.thread),
    icon: "oi oi-smile-add",
    title: _t("Add a Reaction"),
    onClick: async (component) => {
        // 打开表情选择器
        const def = new Deferred();
        component.dialog.add(EmojiPickerMobile, {
            onSelect: (emoji) => {
                // 检查是否已有相同反应
                const reaction = component.props.message.reactions.find(
                    ({ content, personas }) =>
                        content === emoji &&
                        personas.find((persona) => persona.eq(component.store.self))
                );
                if (!reaction) {
                    component.props.message.react(emoji);
                }
                def.resolve(true);
            },
        });
        return def;
    },
    sequence: 10,
});
```

### 2. 回复操作 (reply-to)

```javascript
messageActionsRegistry.add("reply-to", {
    condition: (component) => component.props.message.canReplyTo(component.props.thread),
    icon: "fa-reply",
    title: _t("Reply"),
    onClick: (component) => {
        const message = toRaw(component.props.message);
        const thread = toRaw(component.props.thread);
        component.props.messageToReplyTo.toggle(thread, message);
    },
    sequence: (component) => (component.props.thread?.eq(component.store.inbox) ? 55 : 20),
});
```

### 3. 标星操作 (toggle-star)

```javascript
messageActionsRegistry.add("toggle-star", {
    condition: (component) => component.props.message.canToggleStar,
    icon: (component) =>
        component.props.message.starred ? "fa-star o-mail-Message-starred" : "fa-star-o",
    title: _t("Mark as Todo"),
    onClick: (component) => component.props.message.toggleStar(),
    sequence: 30,
    mobileCloseAfterClick: false,
});
```

### 4. 标记已读 (mark-as-read)

```javascript
messageActionsRegistry.add("mark-as-read", {
    condition: (component) => component.props.thread?.eq(component.store.inbox),
    icon: "fa-check",
    title: _t("Mark as Read"),
    onClick: (component) => component.props.message.setDone(),
    sequence: 40,
});
```

### 5. 查看反应 (reactions)

```javascript
messageActionsRegistry.add("reactions", {
    condition: (component) => component.message.reactions.length,
    icon: "fa-smile-o",
    title: _t("View Reactions"),
    onClick: (component) => component.openReactionMenu(),
    sequence: 50,
    dropdown: true,
});
```

### 6. 取消关注 (unfollow)

```javascript
messageActionsRegistry.add("unfollow", {
    condition: (component) => component.props.message.canUnfollow(component.props.thread),
    icon: "fa-user-times",
    title: _t("Unfollow"),
    onClick: (component) => component.props.message.unfollow(),
    sequence: 60,
});
```

### 7. 标记未读 (mark-as-unread)

```javascript
messageActionsRegistry.add("mark-as-unread", {
    condition: (component) =>
        component.props.thread?.model === "discuss.channel" &&
        component.store.self.type === "partner",
    icon: "fa-eye-slash",
    title: _t("Mark as Unread"),
    onClick: (component) => component.props.message.onClickMarkAsUnread(component.props.thread),
    sequence: 70,
});
```

### 8. 编辑操作 (edit)

```javascript
messageActionsRegistry.add("edit", {
    condition: (component) => component.props.message.editable,
    icon: "fa-pencil",
    title: _t("Edit"),
    onClick: (component) => {
        const message = toRaw(component.props.message);
        const text = convertBrToLineBreak(message.body);
        message.composer = {
            mentionedPartners: message.recipients,
            text,
            selection: {
                start: text.length,
                end: text.length,
                direction: "none",
            },
        };
        component.state.isEditing = true;
    },
    sequence: 80,
});
```

### 9. 删除操作 (delete)

```javascript
messageActionsRegistry.add("delete", {
    condition: (component) => component.props.message.editable,
    icon: "fa-trash",
    title: _t("Delete"),
    onClick: async (component) => {
        const message = toRaw(component.message);
        const def = new Deferred();
        component.dialog.add(
            discussComponentRegistry.get("MessageConfirmDialog"),
            {
                message,
                prompt: _t("Are you sure you want to delete this message?"),
                onConfirm: () => {
                    def.resolve(true);
                    message.remove();
                },
            }
        );
        return def;
    },
    setup: () => {
        const component = useComponent();
        component.dialog = useService("dialog");
    },
    sequence: 90,
});
```

### 10. 下载文件 (download_files)

```javascript
messageActionsRegistry.add("download_files", {
    condition: (component) =>
        component.message.attachment_ids.length > 1 && component.store.self.isInternalUser,
    icon: "fa-download",
    title: _t("Download Files"),
    onClick: (component) =>
        download({
            data: {
                file_ids: component.message.attachment_ids.map((rec) => rec.id),
                zip_name: `attachments_${DateTime.local().toFormat("HHmmddMMyyyy")}.zip`,
            },
            url: "/mail/attachment/zip",
        }),
    sequence: 55,
});
```

### 11. 翻译切换 (toggle-translation)

```javascript
messageActionsRegistry.add("toggle-translation", {
    condition: (component) => component.props.message.isTranslatable(component.props.thread),
    icon: (component) =>
        `fa-language ${component.state.showTranslation ? "o-mail-Message-translated" : ""}`,
    title: (component) => (component.state.showTranslation ? _t("Revert") : _t("Translate")),
    onClick: (component) => component.onClickToggleTranslation(),
    sequence: 100,
});
```

### 12. 复制链接 (copy-link)

```javascript
messageActionsRegistry.add("copy-link", {
    condition: (component) =>
        component.message.message_type &&
        component.message.message_type !== "user_notification",
    icon: "fa-link",
    title: _t("Copy Message Link"),
    onClick: (component) => component.message.copyLink(),
    sequence: 110,
});
```

## 操作转换函数

### transformAction()

将注册表中的操作定义转换为组件可用的操作对象：

```javascript
function transformAction(component, id, action) {
    return {
        component: action.component,
        id,
        mobileCloseAfterClick: action.mobileCloseAfterClick ?? true,
        
        get condition() {
            return action.condition(component);
        },
        
        get icon() {
            return typeof action.icon === "function" ? action.icon(component) : action.icon;
        },
        
        get title() {
            return typeof action.title === "function" ? action.title(component) : action.title;
        },
        
        callComponent: action.callComponent,
        
        get props() {
            return action.props(component);
        },
        
        onClick() {
            return action.onClick?.(component);
        },
        
        get sequence() {
            return typeof action.sequence === "function"
                ? action.sequence(component)
                : action.sequence;
        },
        
        setup: action.setup,
    };
}
```

## useMessageActions 钩子

### 钩子函数

```javascript
function useMessageActions() {
    const component = useComponent();
    const transformedActions = messageActionsRegistry
        .getEntries()
        .map(([id, action]) => transformAction(component, id, action));
    
    // 执行操作的设置函数
    for (const action of transformedActions) {
        if (action.setup) {
            action.setup(action);
        }
    }
    
    const state = useState({
        get actions() {
            const actions = transformedActions
                .filter((action) => action.condition)  // 过滤可用操作
                .sort((a1, a2) => a1.sequence - a2.sequence);  // 按序列排序
            
            if (actions.length > 0) {
                actions.at(0).isFirst = true;   // 标记第一个
                actions.at(-1).isLast = true;   // 标记最后一个
            }
            
            return actions;
        },
    });
    
    return state;
}
```

### 钩子特性

1. **动态过滤**: 根据条件动态过滤可用操作
2. **自动排序**: 按 sequence 属性排序
3. **响应式**: 使用 useState 提供响应式更新
4. **标记**: 自动标记第一个和最后一个操作

## 操作属性

### 基础属性

- **`condition`**: 显示条件函数
- **`icon`**: 图标（字符串或函数）
- **`title`**: 标题（字符串或函数）
- **`onClick`**: 点击处理函数
- **`sequence`**: 排序序号

### 扩展属性

- **`callComponent`**: 调用的组件
- **`props`**: 组件属性函数
- **`setup`**: 设置函数
- **`mobileCloseAfterClick`**: 移动端点击后是否关闭
- **`dropdown`**: 是否为下拉操作

## 使用场景

### 1. 在消息组件中使用

```javascript
// 在消息组件中
const messageActions = useMessageActions();

// 渲染操作按钮
messageActions.actions.forEach(action => {
    if (action.condition) {
        renderActionButton(action);
    }
});
```

### 2. 自定义操作

```javascript
// 添加自定义操作
messageActionsRegistry.add("custom-action", {
    condition: (component) => component.props.message.canCustomAction,
    icon: "fa-custom",
    title: _t("Custom Action"),
    onClick: (component) => component.handleCustomAction(),
    sequence: 200,
});
```

### 3. 条件显示

```javascript
// 根据不同条件显示不同操作
const visibleActions = messageActions.actions.filter(action => {
    if (isMobile && !action.mobileSupported) {
        return false;
    }
    return action.condition;
});
```

## 设计模式

### 1. 注册表模式 (Registry Pattern)
- 使用注册表管理所有消息操作
- 支持动态添加和移除操作

### 2. 策略模式 (Strategy Pattern)
- 每个操作都是一个策略
- 根据条件选择可用的策略

### 3. 命令模式 (Command Pattern)
- 每个操作封装为命令对象
- 支持撤销和重做（部分操作）

### 4. 工厂模式 (Factory Pattern)
- `transformAction` 函数作为操作对象工厂
- 统一创建操作对象

## 性能优化

### 1. 懒加载

```javascript
// 只在需要时计算操作列表
get actions() {
    return transformedActions
        .filter((action) => action.condition)
        .sort((a1, a2) => a1.sequence - a2.sequence);
}
```

### 2. 条件缓存

```javascript
// 使用 getter 缓存条件计算结果
get condition() {
    return action.condition(component);
}
```

### 3. 异步操作

```javascript
// 使用 Deferred 处理异步操作
const def = new Deferred();
// ... 异步处理
return def;
```

## 注意事项

1. **权限检查**: 确保操作的权限检查正确
2. **异步处理**: 正确处理异步操作的状态
3. **移动端适配**: 考虑移动端的特殊需求
4. **国际化**: 所有文本都需要国际化处理

## 扩展建议

1. **批量操作**: 支持批量消息操作
2. **快捷键**: 为操作添加键盘快捷键
3. **自定义操作**: 允许用户自定义操作
4. **操作历史**: 记录操作历史和撤销功能
5. **权限细化**: 更细粒度的权限控制

该模块为消息提供了完整的操作功能，是用户与消息交互的核心实现。

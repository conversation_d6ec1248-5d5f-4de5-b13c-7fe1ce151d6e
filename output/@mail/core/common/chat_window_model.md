# Chat Window Model - 聊天窗口模型

## 概述

`chat_window_model.js` 定义了 Odoo 邮件系统中的聊天窗口模型类，用于管理单个聊天窗口的状态、行为和生命周期。该模型处理窗口的打开、关闭、折叠、焦点管理等功能，并与聊天中心协调工作，是多窗口聊天系统的基础组件。

## 文件信息
- **路径**: `/mail/static/src/core/common/chat_window_model.js`
- **行数**: 133
- **模块**: `@mail/core/common/chat_window_model`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/record'     // Record 基类
'@web/core/network/rpc'        // RPC 网络请求
'@web/core/l10n/translation'   // 国际化
```

## 类型定义

### ChatWindowData 接口

```typescript
interface ChatWindowData {
    thread?: import("models").Thread;  // 关联的线程（可选）
}
```

## 类定义

### ChatWindow 类

```javascript
class ChatWindow extends Record {
    static id = "thread";           // 使用线程作为主键
    static records = {};            // 静态记录集合
}
```

**主键说明**:
- 使用 `thread` 作为主键而不是传统的 `id`
- 确保每个线程只有一个聊天窗口
- 简化窗口与线程的关联管理

## 核心属性

### 基础属性

```javascript
thread = Record.one("Thread");      // 关联的线程
autofocus = 0;                      // 自动焦点计数器
hidden = false;                     // 是否隐藏
fromMessagingMenu = false;          // 是否来自消息菜单
```

### 属性详细说明

#### 1. thread (关联线程)
- **类型**: Thread 模型的一对一关系
- **用途**: 聊天窗口显示的对话线程
- **可选**: 可以为空（新消息窗口）

#### 2. autofocus (自动焦点)
- **类型**: 数字
- **用途**: 焦点计数器，每次调用 focus() 时递增
- **功能**: 触发UI组件的焦点更新

#### 3. hidden (隐藏状态)
- **类型**: 布尔值
- **用途**: 控制窗口的可见性
- **默认**: false（可见）

#### 4. fromMessagingMenu (来源标识)
- **类型**: 布尔值
- **用途**: 标识窗口是否从消息菜单创建
- **功能**: 用于不同的行为逻辑

### 聊天中心关系

#### 打开状态关系

```javascript
hubAsOpened = Record.one("ChatHub", {
    onAdd() {
        this.hubAsFolded = undefined;  // 清除折叠状态
    },
    onDelete() {
        if (!this.thread && !this.hubAsOpened) {
            this.delete();  // 自动清理无效窗口
        }
    },
});
```

#### 折叠状态关系

```javascript
hubAsFolded = Record.one("ChatHub", {
    onAdd() {
        this.hubAsOpened = undefined;  // 清除打开状态
    },
});
```

**状态互斥**:
- 窗口只能处于打开或折叠状态之一
- 状态切换时自动清除另一状态
- 确保状态的一致性

## 计算属性

### 1. 显示名称

```javascript
get displayName() {
    return this.thread?.displayName ?? _t("New message");
}
```

**显示逻辑**:
- 有线程时显示线程名称
- 无线程时显示"新消息"
- 支持国际化

### 2. 打开状态

```javascript
get isOpen() {
    return Boolean(this.hubAsOpened);
}
```

**状态判断**:
- 基于是否关联到聊天中心的打开集合
- 返回布尔值表示窗口是否打开

## 核心方法

### 1. 关闭窗口

```javascript
async close(options = {}) {
    const { escape = false } = options;
    const chatHub = this.store.chatHub;
    const indexAsOpened = chatHub.opened.findIndex((w) => w.eq(this));
    const thread = this.thread;
    
    // 设置线程状态
    if (thread) {
        thread.state = "closed";
    }
    
    // 执行关闭逻辑
    await this._onClose(options);
    
    // 删除窗口
    this.delete();
    
    // ESC键关闭时的焦点处理
    if (escape && indexAsOpened !== -1 && chatHub.opened.length > 0) {
        chatHub.opened[indexAsOpened === 0 ? 0 : indexAsOpened - 1].focus();
    }
}
```

**关闭流程**:
1. 记录窗口在打开列表中的位置
2. 设置关联线程状态为"已关闭"
3. 执行内部关闭逻辑
4. 删除窗口实例
5. 处理ESC键关闭时的焦点转移

**焦点转移逻辑**:
- ESC键关闭时自动转移焦点
- 优先转移到前一个窗口
- 如果是第一个窗口，转移到下一个窗口

### 2. 设置焦点

```javascript
focus() {
    this.autofocus++;
}
```

**焦点机制**:
- 递增自动焦点计数器
- 触发UI组件的响应式更新
- 简单而有效的焦点管理

### 3. 折叠窗口

```javascript
fold() {
    if (!this.thread) {
        return this.close();  // 无线程时直接关闭
    }
    
    // 移动到折叠列表顶部
    this.store.chatHub.folded.delete(this);
    this.store.chatHub.folded.unshift(this);
    
    // 设置线程状态
    this.thread.state = "folded";
    
    // 通知状态变更
    this.notifyState();
}
```

**折叠逻辑**:
1. 检查是否有关联线程
2. 从折叠列表中移除（如果存在）
3. 添加到折叠列表顶部
4. 设置线程状态为"已折叠"
5. 通知服务器状态变更

### 4. 打开窗口

```javascript
open({ notifyState = true } = {}) {
    // 移动到打开列表顶部
    this.store.chatHub.opened.delete(this);
    this.store.chatHub.opened.unshift(this);
    
    if (this.thread) {
        this.thread.state = "open";
        if (notifyState) {
            this.notifyState();
        }
    }
    
    // 设置焦点
    this.focus();
}
```

**打开逻辑**:
1. 从打开列表中移除（如果存在）
2. 添加到打开列表顶部
3. 设置线程状态为"已打开"
4. 可选择性通知状态变更
5. 自动设置焦点

### 5. 状态通知

```javascript
notifyState() {
    // 跳过通知的条件
    if (
        this.store.env.services.ui.isSmall ||
        this.thread?.isTransient ||
        !this.thread?.hasSelfAsMember
    ) {
        return;
    }
    
    // 讨论频道的状态同步
    if (this.thread?.model === "discuss.channel") {
        this.thread.foldStateCount++;
        return rpc(
            "/discuss/channel/fold",
            {
                channel_id: this.thread.id,
                state: this.thread.state,
                state_count: this.thread.foldStateCount,
            },
            { shadow: true }
        );
    }
}
```

**通知条件**:
- **跳过条件**:
  - 小屏幕设备
  - 临时线程
  - 用户不是线程成员

- **执行条件**:
  - 讨论频道类型
  - 有效的线程状态

**同步机制**:
- 递增折叠状态计数器
- 发送RPC请求同步状态
- 使用shadow模式避免阻塞UI

### 6. 内部关闭处理

```javascript
async _onClose({ notifyState = true } = {}) {
    if (notifyState) {
        this.notifyState();
    }
}
```

**功能**:
- 关闭时的内部处理逻辑
- 可选择性通知状态变更
- 为子类扩展提供钩子

## 窗口状态管理

### 状态转换图

```mermaid
stateDiagram-v2
    [*] --> Created: 创建窗口
    Created --> Opened: open()
    Opened --> Folded: fold()
    Folded --> Opened: open()
    Opened --> Closed: close()
    Folded --> Closed: close()
    Closed --> [*]: 删除窗口
```

### 状态同步

#### 本地状态

```javascript
// 窗口状态
- isOpen: 是否打开
- hidden: 是否隐藏
- autofocus: 焦点计数

// 线程状态
- thread.state: "open" | "folded" | "closed"
```

#### 服务器同步

```javascript
// 讨论频道状态同步
{
    channel_id: thread.id,
    state: thread.state,
    state_count: thread.foldStateCount
}
```

## 使用场景

### 1. 创建新聊天窗口

```javascript
// 为线程创建聊天窗口
const chatWindow = store.ChatWindow.insert({
    thread: thread
});

// 打开窗口
chatWindow.open();
```

### 2. 窗口状态切换

```javascript
// 折叠窗口
chatWindow.fold();

// 重新打开
chatWindow.open();

// 关闭窗口
await chatWindow.close();
```

### 3. 焦点管理

```javascript
// 设置焦点
chatWindow.focus();

// 检查是否有焦点
if (chatWindow.autofocus > 0) {
    // 窗口有焦点
}
```

### 4. ESC键关闭

```javascript
// ESC键关闭并转移焦点
await chatWindow.close({ escape: true });
```

## 性能优化

### 1. 状态缓存

```javascript
// 缓存计算属性
let cachedDisplayName = null;
let lastThreadUpdate = null;

get displayName() {
    if (this.thread?.lastUpdate !== lastThreadUpdate) {
        cachedDisplayName = null;
        lastThreadUpdate = this.thread?.lastUpdate;
    }
    
    if (cachedDisplayName === null) {
        cachedDisplayName = this.thread?.displayName ?? _t("New message");
    }
    
    return cachedDisplayName;
}
```

### 2. 批量状态更新

```javascript
// 批量更新窗口状态
const updateMultipleWindows = (windows, state) => {
    windows.forEach(window => {
        window.thread.state = state;
    });
    
    // 批量通知
    windows.forEach(window => window.notifyState());
};
```

### 3. 防抖通知

```javascript
// 防抖状态通知
const debouncedNotifyState = debounce(() => {
    this.notifyState();
}, 100);
```

## 设计模式

### 1. 状态模式 (State Pattern)
- 窗口的不同状态（打开、折叠、关闭）
- 状态转换的规则和行为

### 2. 观察者模式 (Observer Pattern)
- 监听聊天中心状态变化
- 响应线程状态更新

### 3. 命令模式 (Command Pattern)
- 窗口操作的封装（打开、关闭、折叠）
- 支持撤销和重做

## 注意事项

1. **状态一致性**: 确保窗口状态与线程状态同步
2. **内存管理**: 及时清理无效的窗口实例
3. **焦点管理**: 正确处理窗口间的焦点转移
4. **网络同步**: 处理状态同步的网络延迟

## 扩展建议

1. **窗口历史**: 记录窗口的操作历史
2. **自定义状态**: 支持更多的窗口状态
3. **动画效果**: 添加状态转换动画
4. **快捷键**: 支持键盘快捷键操作
5. **窗口分组**: 支持窗口的分组管理

该模型为单个聊天窗口提供了完整的状态管理和行为控制，是多窗口聊天系统的基础构建块。

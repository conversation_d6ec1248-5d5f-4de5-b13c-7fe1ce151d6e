# Navigable List - 可导航列表

## 概述

`navigable_list.js` 实现了一个支持键盘导航的列表组件，主要用于显示建议选项、搜索结果、下拉菜单等场景。该组件提供了完整的键盘导航支持、鼠标交互、位置定位、加载状态等功能，是邮件系统中重要的交互组件。

## 文件信息
- **路径**: `/mail/static/src/core/common/navigable_list.js`
- **行数**: 186
- **模块**: `@mail/core/common/navigable_list`

## 依赖关系

```javascript
// 核心组件依赖
'@mail/core/common/im_status'         // 即时消息状态组件
'@mail/utils/common/hooks'            // 邮件系统钩子

// Web 核心依赖
'@web/core/utils/misc'                // 工具函数
'@web/core/hotkeys/hotkey_service'    // 热键服务
'@web/core/position/position_hook'    // 位置钩子
'@web/core/utils/hooks'               // Web 核心钩子
'@odoo/owl'                           // OWL 框架
```

## 组件定义

### NavigableList 类

```javascript
class NavigableList extends Component {
    static components = { ImStatus };  // 状态指示器组件
    static template = "mail.NavigableList";
}
```

## Props 配置

### Props 定义

```javascript
static props = {
    anchorRef: { optional: true },           // 锚点引用
    autoSelectFirst: { type: Boolean, optional: true },  // 自动选择第一项
    class: { type: String, optional: true }, // CSS类名
    hint: { type: String, optional: true },  // 提示文本
    onSelect: { type: Function },            // 选择回调
    options: { type: Array },                // 选项数组
    optionTemplate: { type: String, optional: true },  // 选项模板
    position: { type: String, optional: true },        // 位置
    isLoading: { type: Boolean, optional: true },      // 加载状态
};
```

### 默认属性

```javascript
static defaultProps = { 
    position: "bottom",        // 默认底部位置
    isLoading: false,         // 默认非加载状态
    autoSelectFirst: true     // 默认自动选择第一项
};
```

### Props 详细说明

- **`anchorRef`** (可选):
  - 类型: DOM 元素引用
  - 用途: 列表定位的锚点元素
  - 示例: 输入框、按钮等

- **`autoSelectFirst`** (可选):
  - 类型: 布尔值
  - 用途: 是否自动选择第一个选项
  - 默认: `true`

- **`class`** (可选):
  - 类型: 字符串
  - 用途: 添加自定义CSS类名
  - 示例: `"suggestion-list"`, `"dropdown-menu"`

- **`hint`** (可选):
  - 类型: 字符串
  - 用途: 显示提示文本
  - 示例: `"按 Enter 选择"`

- **`onSelect`** (必需):
  - 类型: 函数
  - 用途: 选项被选择时的回调
  - 参数: `(event, option, params)`

- **`options`** (必需):
  - 类型: 数组
  - 用途: 可选择的选项列表
  - 格式: `[{ id, label, group?, unselectable? }, ...]`

- **`optionTemplate`** (可选):
  - 类型: 字符串
  - 用途: 自定义选项模板名称
  - 示例: `"mail.NavigableList.Partner"`

- **`position`** (可选):
  - 类型: 字符串
  - 用途: 相对于锚点的位置
  - 可选值: `"top"`, `"bottom"`, `"left"`, `"right"`

- **`isLoading`** (可选):
  - 类型: 布尔值
  - 用途: 是否显示加载状态
  - 默认: `false`

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    
    // DOM 引用和状态
    this.rootRef = useRef("root");
    this.state = useState({
        activeIndex: null,    // 当前活跃选项索引
        open: false,          // 是否打开
        showLoading: false,   // 是否显示加载
    });
    
    // 服务
    this.hotkey = useService("hotkey");
    this.hotkeysToRemove = [];
    
    // 事件监听
    useExternalListener(window, "keydown", this.onKeydown, true);
    onExternalClick("root", async (ev) => {
        await new Promise(setTimeout);
        if (!isEventHandled(ev, "composer.onClickTextarea") &&
            !isEventHandled(ev, "channelSelector.onClickInput")) {
            this.close();
        }
    });
    
    // 位置定位
    usePosition("root", () => this.props.anchorRef, { 
        position: this.props.position 
    });
    
    // 效果钩子
    useEffect(() => this.open(), () => [this.props]);
    useEffect(() => {
        if (!this.props.isLoading) {
            clearTimeout(this.loadingTimeoutId);
            this.state.showLoading = false;
        } else if (!this.loadingTimeoutId) {
            this.loadingTimeoutId = setTimeout(() => 
                (this.state.showLoading = true), 2000);
        }
    }, () => [this.props.isLoading]);
}
```

## 核心功能

### 1. 显示控制

#### 显示条件
```javascript
get show() {
    return Boolean(this.state.open && (this.props.isLoading || this.props.options.length));
}
```

**显示逻辑**:
- 列表必须处于打开状态
- 要么正在加载，要么有可选项
- 两个条件同时满足才显示

#### 选项排序
```javascript
get sortedOptions() {
    return this.props.options.sort((o1, o2) => (o1.group ?? 0) - (o2.group ?? 0));
}
```

**排序规则**:
- 按 `group` 字段升序排列
- 无 `group` 字段的选项默认为 0
- 实现选项的分组显示

### 2. 列表操作

#### 打开列表
```javascript
open() {
    this.state.open = true;
    this.state.activeIndex = null;
    if (this.props.autoSelectFirst) {
        this.navigate("first");
    }
}
```

#### 关闭列表
```javascript
close() {
    this.state.open = false;
    this.state.activeIndex = null;
}
```

#### 选择选项
```javascript
selectOption(ev, index, params = {}) {
    const option = this.props.options[index];
    if (option.unselectable) {
        this.close();
        return;
    }
    this.props.onSelect(ev, option, { ...params });
    this.close();
}
```

**选择逻辑**:
- 检查选项是否可选择
- 不可选择的选项只关闭列表
- 可选择的选项执行回调并关闭列表

### 3. 导航功能

#### 导航方法
```javascript
navigate(direction) {
    if (this.props.options.length === 0) {
        return;
    }
    
    const activeOptionId = this.state.activeIndex !== null ? 
                          this.state.activeIndex : 0;
    let targetId = undefined;
    
    switch (direction) {
        case "first":
            targetId = 0;
            break;
        case "last":
            targetId = this.props.options.length - 1;
            break;
        case "previous":
            targetId = activeOptionId - 1;
            if (targetId < 0) {
                this.navigate("last");  // 循环到最后一项
                return;
            }
            break;
        case "next":
            targetId = activeOptionId + 1;
            if (targetId > this.props.options.length - 1) {
                this.navigate("first");  // 循环到第一项
                return;
            }
            break;
        default:
            return;
    }
    
    this.state.activeIndex = targetId;
}
```

**导航特性**:
- 支持循环导航（首尾相连）
- 四种导航方向：first, last, previous, next
- 自动处理边界情况

### 4. 键盘事件处理

```javascript
onKeydown(ev) {
    if (!this.show) {
        return;
    }
    
    const hotkey = getActiveHotkey(ev);
    switch (hotkey) {
        case "enter":
            markEventHandled(ev, "NavigableList.select");
            if (this.state.activeIndex === null) {
                this.close();
                return;
            }
            this.selectOption(ev, this.state.activeIndex);
            break;
            
        case "escape":
            markEventHandled(ev, "NavigableList.close");
            this.close();
            break;
            
        case "tab":
            this.navigate(this.state.activeIndex === null ? "first" : "next");
            break;
            
        case "arrowup":
            this.navigate(this.state.activeIndex === null ? "first" : "previous");
            break;
            
        case "arrowdown":
            this.navigate(this.state.activeIndex === null ? "first" : "next");
            break;
            
        default:
            return;
    }
    
    if (this.props.options.length !== 0) {
        ev.stopPropagation();
    }
    ev.preventDefault();
}
```

**键盘快捷键**:
- **Enter**: 选择当前高亮选项
- **Escape**: 关闭列表
- **Tab**: 向下导航
- **ArrowUp**: 向上导航
- **ArrowDown**: 向下导航

### 5. 鼠标交互

#### 鼠标悬停
```javascript
onOptionMouseEnter(index) {
    this.state.activeIndex = index;
}
```

**功能说明**:
- 鼠标悬停时自动高亮选项
- 与键盘导航状态同步
- 提供直观的视觉反馈

## 加载状态管理

### 延迟显示加载状态

```javascript
useEffect(() => {
    if (!this.props.isLoading) {
        clearTimeout(this.loadingTimeoutId);
        this.state.showLoading = false;
    } else if (!this.loadingTimeoutId) {
        this.loadingTimeoutId = setTimeout(() => 
            (this.state.showLoading = true), 2000);
    }
}, () => [this.props.isLoading]);
```

**加载逻辑**:
- 加载状态延迟 2 秒显示
- 避免短暂加载时的闪烁
- 提供更好的用户体验

## 使用场景

### 1. 建议列表

```xml
<NavigableList 
    anchorRef="inputRef"
    options="suggestions"
    onSelect="(ev, option) => this.selectSuggestion(option)"
    optionTemplate="mail.NavigableList.Suggestion"
    position="bottom"
/>
```

### 2. 搜索结果

```xml
<NavigableList 
    anchorRef="searchInputRef"
    options="searchResults"
    onSelect="(ev, result) => this.openResult(result)"
    isLoading="state.searching"
    hint="按 Enter 打开"
/>
```

### 3. 下拉菜单

```xml
<NavigableList 
    anchorRef="buttonRef"
    options="menuItems"
    onSelect="(ev, item) => this.executeAction(item)"
    autoSelectFirst="false"
    position="bottom-start"
/>
```

## 选项数据格式

### 基本选项

```javascript
const option = {
    id: "unique-id",           // 唯一标识
    label: "显示文本",          // 显示标签
    group: 1,                  // 分组编号（可选）
    unselectable: false,       // 是否不可选择（可选）
    // 其他自定义属性...
};
```

### 分组选项

```javascript
const groupedOptions = [
    { id: "1", label: "选项1", group: 0 },  // 第一组
    { id: "2", label: "选项2", group: 0 },
    { id: "3", label: "选项3", group: 1 },  // 第二组
    { id: "4", label: "选项4", group: 1 },
];
```

## 位置定位

### 支持的位置

- **`"top"`**: 锚点上方
- **`"bottom"`**: 锚点下方（默认）
- **`"left"`**: 锚点左侧
- **`"right"`**: 锚点右侧
- **`"top-start"`**: 上方左对齐
- **`"bottom-end"`**: 下方右对齐

### 自动调整

组件使用 `usePosition` 钩子自动处理：
- 边界检测和位置调整
- 视口溢出处理
- 响应式位置更新

## 性能优化

### 1. 事件处理优化

```javascript
// 使用事件标记避免重复处理
markEventHandled(ev, "NavigableList.select");

// 检查事件是否已处理
if (isEventHandled(ev, "composer.onClickTextarea")) {
    return;
}
```

### 2. 延迟加载显示

```javascript
// 2秒后才显示加载状态
setTimeout(() => (this.state.showLoading = true), 2000);
```

### 3. 条件渲染

```javascript
// 只在需要时渲染列表
if (!this.show) {
    return null;
}
```

## 设计模式

### 1. 观察者模式 (Observer Pattern)
- 监听键盘和鼠标事件
- 响应状态变化更新UI

### 2. 策略模式 (Strategy Pattern)
- 根据不同的导航方向采用不同策略
- 支持自定义选项模板

### 3. 状态模式 (State Pattern)
- 根据打开/关闭状态显示不同界面
- 加载状态的管理

## 可访问性

### 键盘导航

- 完整的键盘导航支持
- 符合 ARIA 规范的快捷键
- 循环导航提高易用性

### 屏幕阅读器

- 使用语义化的HTML结构
- 提供适当的ARIA属性
- 支持屏幕阅读器导航

## 注意事项

1. **事件处理**: 正确处理事件冒泡和标记
2. **内存管理**: 及时清理定时器和事件监听器
3. **位置计算**: 确保在不同屏幕尺寸下正确定位
4. **性能考虑**: 大量选项时考虑虚拟化

## 扩展建议

1. **虚拟滚动**: 支持大量选项的虚拟滚动
2. **多选支持**: 支持多选模式
3. **搜索过滤**: 集成搜索过滤功能
4. **自定义主题**: 支持自定义样式主题
5. **动画效果**: 添加打开/关闭动画

该组件为用户提供了直观高效的列表导航体验，是构建复杂交互界面的重要基础组件。

# Canned Response Model - 预设回复模型

## 概述

`canned_response_model.js` 定义了 Odoo 邮件系统中的预设回复模型类，用于管理预定义的回复模板。该模型允许用户创建和使用常用的回复内容，提高客服和沟通效率，是智能回复功能的核心数据结构。

## 文件信息
- **路径**: `/mail/static/src/core/common/canned_response_model.js`
- **行数**: 35
- **模块**: `@mail/core/common/canned_response_model`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/record'    // Record 基类
```

## 类定义

### CannedResponse 类

```javascript
class CannedResponse extends Record {
    static _name = "mail.canned.response";  // 模型名称
    static id = "id";                       // 主键字段
    static records = {};                    // 静态记录集合
}
```

### 模型标识

- **`_name`**: `"mail.canned.response"` - 对应后端模型名称
- **`id`**: 主键字段标识
- **`records`**: 静态记录存储

## 核心属性

### 基础属性

```javascript
id: number;           // 预设回复ID
source: string;       // 触发源/关键词
substitution: string; // 替换内容/回复模板
```

### 属性详细说明

#### 1. id (预设回复ID)
- **类型**: `number`
- **用途**: 唯一标识预设回复
- **示例**: `123`

#### 2. source (触发源)
- **类型**: `string`
- **用途**: 触发预设回复的关键词或短语
- **示例**: `"hello"`, `"thanks"`, `"bye"`
- **功能**: 用户输入时的匹配关键词

#### 3. substitution (替换内容)
- **类型**: `string`
- **用途**: 实际的回复内容模板
- **示例**: `"Hello! How can I help you today?"`
- **功能**: 当匹配到source时插入的内容

## 使用场景

### 1. 客服快速回复

```javascript
// 创建客服常用回复
const greetingResponse = store.CannedResponse.insert({
    id: 1,
    source: "hello",
    substitution: "Hello! Welcome to our support. How can I assist you today?"
});

const thanksResponse = store.CannedResponse.insert({
    id: 2,
    source: "thanks",
    substitution: "You're welcome! Is there anything else I can help you with?"
});
```

### 2. 销售模板回复

```javascript
// 销售团队的预设回复
const salesResponse = store.CannedResponse.insert({
    id: 3,
    source: "pricing",
    substitution: "Thank you for your interest! I'll send you our pricing information right away."
});

const demoResponse = store.CannedResponse.insert({
    id: 4,
    source: "demo",
    substitution: "I'd be happy to schedule a demo for you. What time works best?"
});
```

### 3. 技术支持回复

```javascript
// 技术支持的常用回复
const troubleshootResponse = store.CannedResponse.insert({
    id: 5,
    source: "issue",
    substitution: "I understand you're experiencing an issue. Let me help you troubleshoot this step by step."
});

const escalateResponse = store.CannedResponse.insert({
    id: 6,
    source: "escalate",
    substitution: "I'll escalate this to our technical team and get back to you within 24 hours."
});
```

## 集成使用

### 1. 与建议系统集成

```javascript
// 在建议钩子中使用预设回复
const suggestion = useSuggestion();

// 当用户输入匹配source时，显示预设回复建议
const matchedResponses = store.CannedResponse.records
    .filter(response => response.source.includes(userInput))
    .map(response => ({
        label: response.substitution,
        cannedResponse: response
    }));
```

### 2. 与编辑器集成

```javascript
// 在编辑器中插入预设回复
const insertCannedResponse = (response) => {
    composer.text += response.substitution;
    composer.cannedResponses.push(response);
};
```

### 3. 搜索和过滤

```javascript
// 根据关键词搜索预设回复
const searchCannedResponses = (keyword) => {
    return Object.values(store.CannedResponse.records)
        .filter(response => 
            response.source.toLowerCase().includes(keyword.toLowerCase()) ||
            response.substitution.toLowerCase().includes(keyword.toLowerCase())
        );
};
```

## 数据管理

### 1. 创建预设回复

```javascript
// 创建新的预设回复
const createCannedResponse = (source, substitution) => {
    return store.CannedResponse.insert({
        source: source.trim(),
        substitution: substitution.trim()
    });
};
```

### 2. 更新预设回复

```javascript
// 更新现有预设回复
const updateCannedResponse = (id, updates) => {
    const response = store.CannedResponse.get(id);
    if (response) {
        Object.assign(response, updates);
    }
};
```

### 3. 删除预设回复

```javascript
// 删除预设回复
const deleteCannedResponse = (id) => {
    const response = store.CannedResponse.get(id);
    if (response) {
        response.delete();
    }
};
```

## 高级功能

### 1. 模板变量支持

```javascript
// 支持模板变量的预设回复
const templateResponse = store.CannedResponse.insert({
    id: 7,
    source: "welcome",
    substitution: "Welcome {{customer_name}}! Thank you for choosing {{company_name}}."
});

// 变量替换函数
const replaceVariables = (template, variables) => {
    let result = template;
    for (const [key, value] of Object.entries(variables)) {
        result = result.replace(new RegExp(`{{${key}}}`, 'g'), value);
    }
    return result;
};
```

### 2. 分类管理

```javascript
// 扩展模型支持分类
const categorizedResponse = store.CannedResponse.insert({
    id: 8,
    source: "refund",
    substitution: "I'll process your refund request immediately.",
    category: "billing",  // 扩展属性
    priority: 1           // 扩展属性
});
```

### 3. 使用统计

```javascript
// 扩展模型支持使用统计
const trackUsage = (responseId) => {
    const response = store.CannedResponse.get(responseId);
    if (response) {
        response.usageCount = (response.usageCount || 0) + 1;
        response.lastUsed = new Date();
    }
};
```

## 性能优化

### 1. 索引优化

```javascript
// 为快速搜索创建索引
const createSourceIndex = () => {
    const index = new Map();
    Object.values(store.CannedResponse.records).forEach(response => {
        const words = response.source.toLowerCase().split(' ');
        words.forEach(word => {
            if (!index.has(word)) {
                index.set(word, []);
            }
            index.get(word).push(response);
        });
    });
    return index;
};
```

### 2. 缓存机制

```javascript
// 缓存常用的预设回复
const popularResponses = Object.values(store.CannedResponse.records)
    .sort((a, b) => (b.usageCount || 0) - (a.usageCount || 0))
    .slice(0, 10);
```

### 3. 懒加载

```javascript
// 按需加载预设回复
const loadCannedResponses = async (category) => {
    if (!store.CannedResponse.loaded[category]) {
        await store.loadCannedResponses({ category });
        store.CannedResponse.loaded[category] = true;
    }
};
```

## 设计模式

### 1. 模板模式 (Template Pattern)
- 预设回复作为消息模板
- 支持变量替换和定制

### 2. 策略模式 (Strategy Pattern)
- 不同场景使用不同的回复策略
- 根据上下文选择合适的回复

### 3. 观察者模式 (Observer Pattern)
- 监听用户输入变化
- 自动匹配和建议预设回复

## 数据结构示例

### 基础预设回复

```javascript
{
    id: 1,
    source: "hello",
    substitution: "Hello! How can I help you today?"
}
```

### 复杂预设回复

```javascript
{
    id: 2,
    source: "order status",
    substitution: "Let me check your order status for you. Could you please provide your order number?",
    category: "orders",
    tags: ["status", "tracking"],
    usageCount: 45,
    lastUsed: "2023-12-01T10:30:00Z"
}
```

## 集成示例

### 1. 智能建议

```javascript
// 在输入时自动建议预设回复
const getSuggestions = (input) => {
    const suggestions = [];
    
    // 匹配source
    Object.values(store.CannedResponse.records).forEach(response => {
        if (response.source.toLowerCase().includes(input.toLowerCase())) {
            suggestions.push({
                type: 'canned_response',
                label: response.substitution,
                value: response
            });
        }
    });
    
    return suggestions;
};
```

### 2. 快捷键支持

```javascript
// 支持快捷键插入预设回复
const handleShortcut = (key) => {
    const response = store.CannedResponse.records
        .find(r => r.shortcut === key);
    
    if (response) {
        insertCannedResponse(response);
    }
};
```

## 注意事项

1. **内容管理**: 确保预设回复内容的准确性和时效性
2. **权限控制**: 控制谁可以创建和修改预设回复
3. **本地化**: 支持多语言的预设回复
4. **数据同步**: 确保团队间预设回复的同步

## 扩展建议

1. **AI增强**: 使用AI生成和优化预设回复
2. **A/B测试**: 测试不同回复的效果
3. **情感分析**: 根据对话情感选择合适回复
4. **个性化**: 根据用户偏好定制回复
5. **多媒体支持**: 支持图片、文件等多媒体回复

该模型虽然简单，但为邮件系统提供了强大的预设回复功能，大大提高了用户的沟通效率和一致性。

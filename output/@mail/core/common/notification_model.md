# Notification Model - 通知模型

## 概述

`notification_model.js` 定义了 Odoo 邮件系统中的通知模型类，用于管理消息发送的通知状态。该模型跟踪邮件和短信的发送状态，处理发送失败的情况，并提供状态图标和描述，是邮件系统中消息传递状态管理的核心组件。

## 文件信息
- **路径**: `/mail/static/src/core/common/notification_model.js`
- **行数**: 118
- **模块**: `@mail/core/common/notification_model`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/record'     // Record 基类
'@web/core/l10n/translation'   // 国际化
```

## 类定义

### Notification 类

```javascript
class Notification extends Record {
    static id = "id";           // 主键字段
    static records = {};        // 静态记录集合
}
```

## 核心属性

### 基础属性

```javascript
id: number;                     // 通知ID
notification_status: string;    // 通知状态
notification_type: string;      // 通知类型
failure_type: string;           // 失败类型
```

### 关系属性

```javascript
message = Record.one("Message");        // 关联的消息
persona = Record.one("Persona");        // 关联的用户角色
```

### 失败关系

```javascript
failure = Record.one("Failure", {
    inverse: "notifications",
    compute() {
        const thread = this.message?.thread;
        if (!this.message?.isSelfAuthored) {
            return;
        }
        const failure = Object.values(this.store.Failure.records).find((f) => {
            return (
                f.resModel === thread?.model &&
                f.type === this.notification_type &&
                (f.resModel !== "discuss.channel" || f.resIds.has(thread?.id))
            );
        });
        return this.isFailure
            ? {
                  id: failure ? failure.id : this.store.Failure.nextId.value++,
              }
            : false;
    },
    eager: true,
});
```

**失败关系计算逻辑**:
1. 检查消息是否为自己发送
2. 查找匹配的失败记录
3. 匹配条件：模型、类型、资源ID
4. 如果是失败状态，创建或关联失败记录

## 通知状态

### 状态类型

```javascript
// 通知状态枚举
const NOTIFICATION_STATUS = {
    PROCESS: "process",       // 处理中
    PENDING: "pending",       // 待发送
    SENT: "sent",            // 已发送
    BOUNCE: "bounce",        // 退回
    EXCEPTION: "exception",   // 异常
    READY: "ready",          // 就绪
    CANCELED: "canceled"     // 已取消
};
```

### 通知类型

```javascript
// 通知类型枚举
const NOTIFICATION_TYPE = {
    EMAIL: "email",          // 邮件通知
    SMS: "sms"              // 短信通知
};
```

## 计算属性

### 1. isFailure - 失败状态检查

```javascript
get isFailure() {
    return ["exception", "bounce"].includes(this.notification_status);
}
```

**失败判断**:
- `exception`: 发送异常
- `bounce`: 邮件退回
- 返回布尔值表示是否为失败状态

### 2. icon - 通知图标

```javascript
get icon() {
    if (this.isFailure) {
        return "fa fa-envelope";        // 失败时的实心信封
    }
    return "fa fa-envelope-o";          // 正常时的空心信封
}
```

**图标逻辑**:
- **失败状态**: 实心信封图标
- **正常状态**: 空心信封图标

### 3. label - 通知标签

```javascript
get label() {
    return "";
}
```

**功能**:
- 预留的标签属性
- 可用于扩展显示信息

### 4. statusIcon - 状态图标

```javascript
get statusIcon() {
    switch (this.notification_status) {
        case "process":
            return "fa fa-hourglass-half";      // 沙漏（处理中）
        case "pending":
            return "fa fa-paper-plane-o";       // 纸飞机（待发送）
        case "sent":
            return "fa fa-check";               // 对勾（已发送）
        case "bounce":
            return "fa fa-exclamation";         // 感叹号（退回）
        case "exception":
            return "fa fa-exclamation";         // 感叹号（异常）
        case "ready":
            return "fa fa-send-o";              // 发送图标（就绪）
        case "canceled":
            return "fa fa-trash-o";             // 垃圾桶（已取消）
    }
    return "";
}
```

### 5. statusTitle - 状态标题

```javascript
get statusTitle() {
    switch (this.notification_status) {
        case "process":
            return _t("Processing");            // 处理中
        case "pending":
            return _t("Sent");                  // 已发送
        case "sent":
            return _t("Delivered");             // 已送达
        case "bounce":
            return _t("Bounced");               // 已退回
        case "exception":
            return _t("Error");                 // 错误
        case "ready":
            return _t("Ready");                 // 就绪
        case "canceled":
            return _t("Cancelled");             // 已取消
    }
    return "";
}
```

**国际化支持**:
- 所有状态标题都支持多语言
- 使用 `_t()` 函数进行翻译

## 使用场景

### 1. 消息发送状态显示

```javascript
// 在消息组件中显示通知状态
const renderNotificationStatus = (message) => {
    return message.notifications.map(notification => ({
        icon: notification.statusIcon,
        title: notification.statusTitle,
        type: notification.notification_type,
        isFailure: notification.isFailure
    }));
};
```

### 2. 发送失败处理

```javascript
// 检查和处理发送失败
const checkNotificationFailures = (notifications) => {
    const failures = notifications.filter(n => n.isFailure);
    
    if (failures.length > 0) {
        showFailureDialog({
            count: failures.length,
            types: failures.map(f => f.notification_type),
            onRetry: () => retryFailedNotifications(failures)
        });
    }
};
```

### 3. 通知状态统计

```javascript
// 统计不同状态的通知数量
const getNotificationStats = (notifications) => {
    const stats = {};
    
    notifications.forEach(notification => {
        const status = notification.notification_status;
        stats[status] = (stats[status] || 0) + 1;
    });
    
    return {
        total: notifications.length,
        sent: stats.sent || 0,
        failed: notifications.filter(n => n.isFailure).length,
        pending: stats.pending || 0,
        processing: stats.process || 0
    };
};
```

### 4. 通知类型过滤

```javascript
// 按类型过滤通知
const filterNotificationsByType = (notifications, type) => {
    return notifications.filter(n => n.notification_type === type);
};

// 获取邮件通知
const emailNotifications = filterNotificationsByType(notifications, 'email');

// 获取短信通知
const smsNotifications = filterNotificationsByType(notifications, 'sms');
```

## 状态流转

### 正常流程

```mermaid
stateDiagram-v2
    [*] --> ready: 创建通知
    ready --> process: 开始处理
    process --> pending: 提交发送
    pending --> sent: 发送成功
    sent --> [*]: 完成
```

### 异常流程

```mermaid
stateDiagram-v2
    [*] --> ready: 创建通知
    ready --> process: 开始处理
    process --> exception: 处理异常
    process --> pending: 提交发送
    pending --> bounce: 发送退回
    pending --> canceled: 取消发送
    exception --> [*]: 失败结束
    bounce --> [*]: 失败结束
    canceled --> [*]: 取消结束
```

## 失败处理

### 1. 失败检测

```javascript
// 检测通知是否失败
const isNotificationFailed = (notification) => {
    return notification.isFailure;
};

// 获取失败原因
const getFailureReason = (notification) => {
    if (notification.notification_status === 'bounce') {
        return '邮件被退回，可能是邮箱地址无效';
    } else if (notification.notification_status === 'exception') {
        return '发送过程中发生异常，请稍后重试';
    }
    return '未知错误';
};
```

### 2. 失败重试

```javascript
// 重试失败的通知
const retryFailedNotification = async (notification) => {
    try {
        await rpc('/mail/notification/retry', {
            notification_id: notification.id
        });
        
        // 更新状态为处理中
        notification.notification_status = 'process';
        
    } catch (error) {
        console.error('重试失败:', error);
        throw error;
    }
};
```

### 3. 失败统计

```javascript
// 统计失败通知
const getFailureStats = (notifications) => {
    const failures = notifications.filter(n => n.isFailure);
    
    return {
        total: failures.length,
        bounce: failures.filter(n => n.notification_status === 'bounce').length,
        exception: failures.filter(n => n.notification_status === 'exception').length,
        byType: {
            email: failures.filter(n => n.notification_type === 'email').length,
            sms: failures.filter(n => n.notification_type === 'sms').length
        }
    };
};
```

## 数据同步

### 1. 状态更新

```javascript
// 更新通知状态
const updateNotificationStatus = (notificationId, newStatus) => {
    const notification = store.Notification.get(notificationId);
    if (notification) {
        notification.notification_status = newStatus;
        
        // 如果状态变为失败，自动关联失败记录
        if (notification.isFailure) {
            // failure 关系会自动计算和创建
        }
    }
};
```

### 2. 批量更新

```javascript
// 批量更新通知状态
const batchUpdateNotifications = (updates) => {
    updates.forEach(({ id, status }) => {
        updateNotificationStatus(id, status);
    });
};
```

## 性能优化

### 1. 状态缓存

```javascript
// 缓存状态计算结果
let cachedStatusIcon = null;
let lastStatus = null;

get statusIcon() {
    if (this.notification_status !== lastStatus) {
        cachedStatusIcon = this.calculateStatusIcon();
        lastStatus = this.notification_status;
    }
    return cachedStatusIcon;
}
```

### 2. 批量处理

```javascript
// 批量处理通知状态更新
const processBatchNotifications = (notifications) => {
    // 按状态分组
    const grouped = notifications.reduce((groups, notification) => {
        const status = notification.notification_status;
        if (!groups[status]) {
            groups[status] = [];
        }
        groups[status].push(notification);
        return groups;
    }, {});
    
    // 按组处理
    Object.entries(grouped).forEach(([status, notifs]) => {
        processNotificationGroup(status, notifs);
    });
};
```

## 设计模式

### 1. 状态模式 (State Pattern)
- 不同通知状态的不同行为
- 状态转换的规则和逻辑

### 2. 观察者模式 (Observer Pattern)
- 监听通知状态变化
- 自动更新相关的失败记录

### 3. 策略模式 (Strategy Pattern)
- 不同通知类型的不同处理策略
- 邮件 vs 短信的不同逻辑

## 注意事项

1. **状态一致性**: 确保通知状态与实际发送状态同步
2. **失败处理**: 正确处理各种失败情况
3. **性能考虑**: 大量通知时的处理性能
4. **用户体验**: 提供清晰的状态反馈

## 扩展建议

1. **更多状态**: 支持更细粒度的状态划分
2. **重试机制**: 智能的自动重试策略
3. **统计分析**: 通知发送的统计和分析
4. **实时更新**: 更实时的状态更新机制
5. **自定义通知**: 支持自定义通知类型

该模型为邮件系统提供了完整的通知状态管理，确保用户能够及时了解消息的发送状态和处理结果。

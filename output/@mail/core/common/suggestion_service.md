# Suggestion Service - 建议服务

## 概述

`suggestion_service.js` 实现了 Odoo 邮件系统中的智能建议服务，用于在用户输入时提供自动完成建议。该服务支持多种类型的建议，包括用户提及（@）、频道引用（#）和预设回复（:），集成了智能搜索、排序算法和权限控制，为用户提供精准的输入建议，是邮件系统中重要的用户体验增强服务。

## 文件信息
- **路径**: `/mail/static/src/core/common/suggestion_service.js`
- **行数**: 302
- **模块**: `@mail/core/common/suggestion_service`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/partner_compare'  // 合作伙伴比较器
'@mail/utils/common/format'          // 格式化工具
'@odoo/owl'                          // OWL 框架
'@web/core/registry'                 // 服务注册表
```

## 服务定义

### SuggestionService 类

```javascript
class SuggestionService {
    constructor(env, services) {
        this.env = env;
        this.orm = services.orm;
        this.store = services["mail.store"];
    }
}
```

### 服务配置

```javascript
const suggestionService = {
    dependencies: ["orm", "mail.store"],
    start(env, services) {
        return new SuggestionService(env, services);
    }
};
```

## 支持的分隔符

### 分隔符定义

```javascript
getSupportedDelimiters(thread) {
    return [["@"], ["#"], [":"]];
}
```

**分隔符类型**:
- **`@`**: 用户提及建议
- **`#`**: 频道引用建议
- **`:`**: 预设回复建议

## 核心功能

### 1. 获取建议

```javascript
async fetchSuggestions({ delimiter, term }, { thread } = {}) {
    const cleanedSearchTerm = cleanTerm(term);
    switch (delimiter) {
        case "@": {
            await this.fetchPartners(cleanedSearchTerm, thread);
            break;
        }
        case "#":
            await this.fetchThreads(cleanedSearchTerm);
            break;
        case ":":
            await this.store.cannedReponses.fetch();
            break;
    }
}
```

**获取流程**:
1. 清理搜索词
2. 根据分隔符类型调用相应的获取方法
3. 异步加载数据到存储中

### 2. 搜索建议

```javascript
searchSuggestions({ delimiter, term }, { thread, sort = false } = {}) {
    thread = toRaw(thread);
    const cleanedSearchTerm = cleanTerm(term);
    switch (delimiter) {
        case "@": {
            return this.searchPartnerSuggestions(cleanedSearchTerm, thread, sort);
        }
        case "#":
            return this.searchChannelSuggestions(cleanedSearchTerm, sort);
        case ":":
            return this.searchCannedResponseSuggestions(cleanedSearchTerm, sort);
    }
    return {
        type: undefined,
        suggestions: [],
    };
}
```

**搜索特性**:
- 支持实时搜索
- 可选择是否排序
- 返回统一的结果格式

## 用户提及建议

### 1. 获取合作伙伴

```javascript
async fetchPartners(term, thread) {
    const kwargs = { search: term };
    if (thread?.model === "discuss.channel") {
        kwargs.channel_id = thread.id;
    }
    const data = await this.orm.silent.call(
        "res.partner",
        thread?.model === "discuss.channel"
            ? "get_mention_suggestions_from_channel"
            : "get_mention_suggestions",
        [],
        kwargs
    );
    this.store.insert(data);
}
```

**获取逻辑**:
- 根据线程类型选择不同的API方法
- 频道中优先显示频道成员
- 静默调用避免错误提示

### 2. 合作伙伴过滤

```javascript
getPartnerSuggestions(thread) {
    let partners;
    const isNonPublicChannel =
        thread &&
        (thread.channel_type === "group" ||
            thread.channel_type === "chat" ||
            (thread.channel_type === "channel" && thread.authorizedGroupFullName));
    
    if (isNonPublicChannel) {
        // 私有频道只返回频道成员
        partners = thread.channelMembers
            .map((member) => member.persona)
            .filter((persona) => persona.type === "partner");
    } else {
        // 公共频道返回所有合作伙伴
        partners = Object.values(this.store.Persona.records).filter((persona) => {
            if (thread?.model !== "discuss.channel" && persona.eq(this.store.odoobot)) {
                return false;
            }
            return persona.type === "partner";
        });
    }
    return partners;
}
```

**过滤规则**:
- 私有频道：只显示频道成员
- 公共频道：显示所有合作伙伴
- 排除非频道中的OdooBot

### 3. 合作伙伴搜索

```javascript
searchPartnerSuggestions(cleanedSearchTerm, thread, sort) {
    const partners = this.getPartnerSuggestions(thread);
    const suggestions = [];
    
    for (const partner of partners) {
        if (!partner.name) {
            continue;
        }
        if (
            cleanTerm(partner.name).includes(cleanedSearchTerm) ||
            (partner.email && cleanTerm(partner.email).includes(cleanedSearchTerm))
        ) {
            suggestions.push(partner);
        }
    }
    
    // 添加特殊提及
    suggestions.push(
        ...this.store.specialMentions.filter(
            (special) =>
                thread &&
                special.channel_types.includes(thread.channel_type) &&
                cleanedSearchTerm.length >= Math.min(4, special.label.length) &&
                (special.label.startsWith(cleanedSearchTerm) ||
                    cleanTerm(special.description.toString()).includes(cleanedSearchTerm))
        )
    );
    
    return {
        type: "Partner",
        suggestions: sort
            ? [...this.sortPartnerSuggestions(suggestions, cleanedSearchTerm, thread)]
            : suggestions,
    };
}
```

**搜索特性**:
- 按姓名和邮箱搜索
- 支持特殊提及（如@all）
- 可选智能排序

### 4. 合作伙伴排序

```javascript
sortPartnerSuggestions(partners, searchTerm = "", thread = undefined) {
    const cleanedSearchTerm = cleanTerm(searchTerm);
    const compareFunctions = partnerCompareRegistry.getAll();
    const context = { recentChatPartnerIds: this.store.getRecentChatPartnerIds() };
    const memberPartnerIds = new Set(
        thread?.channelMembers
            .filter((member) => member.persona.type === "partner")
            .map((member) => member.persona.id)
    );
    
    return partners.sort((p1, p2) => {
        p1 = toRaw(p1);
        p2 = toRaw(p2);
        if (p1.isSpecial || p2.isSpecial) {
            return 0;
        }
        for (const fn of compareFunctions) {
            const result = fn(p1, p2, {
                env: this.env,
                memberPartnerIds,
                searchTerms: cleanedSearchTerm,
                thread,
                context,
            });
            if (result !== undefined) {
                return result;
            }
        }
    });
}
```

**排序策略**:
- 使用注册表中的比较函数
- 考虑最近聊天记录
- 优先显示频道成员
- 特殊提及不参与排序

## 频道引用建议

### 1. 获取频道

```javascript
async fetchThreads(term) {
    const suggestedThreads = await this.orm.silent.call(
        "discuss.channel",
        "get_mention_suggestions",
        [],
        { search: term }
    );
    this.store.Thread.insert(suggestedThreads);
}
```

### 2. 频道搜索

```javascript
searchChannelSuggestions(cleanedSearchTerm, sort) {
    const suggestionList = Object.values(this.store.Thread.records).filter(
        (thread) =>
            thread.channel_type === "channel" &&
            thread.displayName &&
            cleanTerm(thread.displayName).includes(cleanedSearchTerm)
    );
    
    const sortFunc = (c1, c2) => {
        // 公共频道优先
        const isPublicChannel1 = c1.channel_type === "channel" && !c1.authorizedGroupFullName;
        const isPublicChannel2 = c2.channel_type === "channel" && !c2.authorizedGroupFullName;
        if (isPublicChannel1 && !isPublicChannel2) {
            return -1;
        }
        if (!isPublicChannel1 && isPublicChannel2) {
            return 1;
        }
        
        // 已加入的频道优先
        if (c1.hasSelfAsMember && !c2.hasSelfAsMember) {
            return -1;
        }
        if (!c1.hasSelfAsMember && c2.hasSelfAsMember) {
            return 1;
        }
        
        // 前缀匹配优先
        const cleanedDisplayName1 = cleanTerm(c1.displayName);
        const cleanedDisplayName2 = cleanTerm(c2.displayName);
        if (
            cleanedDisplayName1.startsWith(cleanedSearchTerm) &&
            !cleanedDisplayName2.startsWith(cleanedSearchTerm)
        ) {
            return -1;
        }
        if (
            !cleanedDisplayName1.startsWith(cleanedSearchTerm) &&
            cleanedDisplayName2.startsWith(cleanedSearchTerm)
        ) {
            return 1;
        }
        
        // 字母顺序
        if (cleanedDisplayName1 < cleanedDisplayName2) {
            return -1;
        }
        if (cleanedDisplayName1 > cleanedDisplayName2) {
            return 1;
        }
        return c1.id - c2.id;
    };
    
    return {
        type: "Thread",
        suggestions: sort ? suggestionList.sort(sortFunc) : suggestionList,
    };
}
```

**排序优先级**:
1. 公共频道优先于私有频道
2. 已加入的频道优先
3. 前缀匹配优先
4. 字母顺序排序
5. ID排序作为最后依据

## 预设回复建议

### 1. 预设回复搜索

```javascript
searchCannedResponseSuggestions(cleanedSearchTerm, sort) {
    const cannedResponses = Object.values(this.store["mail.canned.response"].records).filter(
        (cannedResponse) => {
            return cleanTerm(cannedResponse.source).includes(cleanedSearchTerm);
        }
    );
    
    const sortFunc = (c1, c2) => {
        const cleanedName1 = cleanTerm(c1.source);
        const cleanedName2 = cleanTerm(c2.source);
        
        // 前缀匹配优先
        if (
            cleanedName1.startsWith(cleanedSearchTerm) &&
            !cleanedName2.startsWith(cleanedSearchTerm)
        ) {
            return -1;
        }
        if (
            !cleanedName1.startsWith(cleanedSearchTerm) &&
            cleanedName2.startsWith(cleanedSearchTerm)
        ) {
            return 1;
        }
        
        // 字母顺序
        if (cleanedName1 < cleanedName2) {
            return -1;
        }
        if (cleanedName1 > cleanedName2) {
            return 1;
        }
        return c1.id - c2.id;
    };
    
    return {
        type: "mail.canned.response",
        suggestions: sort ? cannedResponses.sort(sortFunc) : cannedResponses,
    };
}
```

**搜索特性**:
- 按预设回复源码搜索
- 前缀匹配优先
- 字母顺序排序

## 使用场景

### 1. 消息编辑器中的提及

```javascript
// 在消息编辑器中使用建议服务
const handleMentionInput = async (inputText, cursorPosition) => {
    const { delimiter, term } = parseInput(inputText, cursorPosition);
    
    if (delimiter) {
        // 获取建议数据
        await suggestionService.fetchSuggestions(
            { delimiter, term },
            { thread: currentThread }
        );
        
        // 搜索建议
        const result = suggestionService.searchSuggestions(
            { delimiter, term },
            { thread: currentThread, sort: true }
        );
        
        showSuggestions(result.suggestions, result.type);
    }
};
```

### 2. 智能搜索

```javascript
// 智能搜索实现
const smartSearch = (query, context) => {
    const results = [];
    
    // 检测不同类型的查询
    if (query.startsWith('@')) {
        const term = query.substring(1);
        const partnerResults = suggestionService.searchSuggestions(
            { delimiter: '@', term },
            { thread: context.thread, sort: true }
        );
        results.push(...partnerResults.suggestions);
    }
    
    if (query.startsWith('#')) {
        const term = query.substring(1);
        const channelResults = suggestionService.searchSuggestions(
            { delimiter: '#', term },
            { sort: true }
        );
        results.push(...channelResults.suggestions);
    }
    
    return results;
};
```

### 3. 自动完成

```javascript
// 自动完成功能
const setupAutoComplete = (inputElement, thread) => {
    let currentSuggestions = [];
    
    inputElement.addEventListener('input', async (event) => {
        const { delimiter, term } = parseInputAtCursor(
            event.target.value,
            event.target.selectionStart
        );
        
        if (delimiter && term.length >= 2) {
            await suggestionService.fetchSuggestions(
                { delimiter, term },
                { thread }
            );
            
            const result = suggestionService.searchSuggestions(
                { delimiter, term },
                { thread, sort: true }
            );
            
            currentSuggestions = result.suggestions;
            showAutoCompleteDropdown(currentSuggestions);
        } else {
            hideAutoCompleteDropdown();
        }
    });
};
```

## 性能优化

### 1. 缓存机制

```javascript
// 建议结果缓存
const suggestionCache = new Map();

const getCachedSuggestions = (key) => {
    const cached = suggestionCache.get(key);
    if (cached && Date.now() - cached.timestamp < 300000) { // 5分钟缓存
        return cached.data;
    }
    return null;
};

const setCachedSuggestions = (key, data) => {
    suggestionCache.set(key, {
        data,
        timestamp: Date.now()
    });
};
```

### 2. 防抖处理

```javascript
// 搜索防抖
const debouncedSearch = debounce(async (params, options) => {
    await suggestionService.fetchSuggestions(params, options);
}, 300);
```

### 3. 批量加载

```javascript
// 批量加载建议数据
const batchLoadSuggestions = async (requests) => {
    const promises = requests.map(request => 
        suggestionService.fetchSuggestions(request.params, request.options)
    );
    
    await Promise.all(promises);
};
```

## 权限控制

### 1. 频道权限检查

```javascript
// 检查频道访问权限
const hasChannelAccess = (thread, user) => {
    if (thread.channel_type === 'channel' && !thread.authorizedGroupFullName) {
        return true; // 公共频道
    }
    
    return thread.channelMembers.some(member => 
        member.persona.id === user.id
    );
};
```

### 2. 提及权限

```javascript
// 检查提及权限
const canMentionUser = (partner, thread, currentUser) => {
    // 检查是否为频道成员
    if (thread.channel_type !== 'channel') {
        return thread.channelMembers.some(member => 
            member.persona.id === partner.id
        );
    }
    
    // 公共频道可以提及任何用户
    return true;
};
```

## 设计模式

### 1. 服务模式 (Service Pattern)
- 全局建议服务
- 统一的建议接口

### 2. 策略模式 (Strategy Pattern)
- 不同分隔符的不同处理策略
- 可配置的排序策略

### 3. 工厂模式 (Factory Pattern)
- 根据类型创建不同的建议
- 统一的建议创建接口

## 注意事项

1. **性能考虑**: 合理使用缓存和防抖
2. **权限控制**: 确保用户只能看到有权限的建议
3. **用户体验**: 提供快速准确的建议
4. **数据一致性**: 保持建议数据的实时性

## 扩展建议

1. **机器学习**: 基于用户行为学习建议偏好
2. **模糊搜索**: 支持拼写错误的容错搜索
3. **上下文感知**: 根据对话上下文提供更精准的建议
4. **个性化**: 支持用户自定义建议规则
5. **多语言**: 支持多语言的建议搜索

该服务为邮件系统提供了智能的输入建议功能，显著提升了用户的输入效率和体验。

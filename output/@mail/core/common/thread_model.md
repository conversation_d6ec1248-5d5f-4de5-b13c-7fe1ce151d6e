# Thread Model - 线程模型

## 概述

`thread_model.js` 是 Odoo 邮件系统中最核心的模型之一，定义了线程（Thread）的数据结构和业务逻辑。线程是邮件系统中的基本概念，代表一个对话、频道、邮箱或任何消息的容器。该模型包含了消息管理、成员管理、状态控制等复杂功能。

## 文件信息
- **路径**: `/mail/static/src/core/common/thread_model.js`
- **行数**: 1096
- **模块**: `@mail/core/common/thread_model`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/record'        // Record 基类
'@mail/utils/common/format'       // 格式化工具
'@mail/utils/common/misc'         // 工具函数
'@web/core/network/rpc'           // RPC 网络请求
'@web/core/l10n/translation'      // 国际化
'@web/core/user'                  // 用户服务
'@web/core/utils/concurrency'     // 并发工具
```

## 类定义

### Thread 类

```javascript
class Thread extends Record {
    static id = AND("model", "id");  // 复合主键：模型名 + ID
    static records = {};             // 静态记录集合
}
```

### 复合主键

Thread 使用复合主键 `AND("model", "id")`，这意味着：
- 同一个 ID 在不同模型中可以共存
- 例如：`discuss.channel,123` 和 `mail.thread,123` 是不同的线程

## 核心属性

### 基础属性

```javascript
// 基本标识
id: number;              // 线程ID
uuid: string;            // 唯一标识符
model: string;           // 模型名称

// 基本信息
custom_channel_name: string;  // 自定义频道名称
description: string;          // 描述
counter: number;             // 计数器
counter_bus_id: number;      // 总线计数器ID
```

### 关系字段

#### 消息关系
```javascript
allMessages = Record.many("Message", {
    inverse: "thread",
});
```

#### 附件关系
```javascript
attachments = Record.many("Attachment", {
    sort: (a1, a2) => (a1.id < a2.id ? 1 : -1),  // 按ID降序排列
});
areAttachmentsLoaded: boolean = false;  // 附件是否已加载
```

#### 成员关系
```javascript
channelMembers = Record.many("ChannelMember", {
    inverse: "thread",
    onDelete: (r) => r.delete(),
    sort: (m1, m2) => m1.id - m2.id,
});

invitedMembers = Record.many("ChannelMember");  // 受邀成员
```

#### 编辑器关系
```javascript
composer = Record.one("Composer", {
    compute: () => ({}),
    inverse: "thread",
    onDelete: (r) => r.delete(),
});
```

## 计算属性

### 权限控制

#### 离开权限
```javascript
get canLeave() {
    return (
        ["channel", "group"].includes(this.channel_type) &&
        !this.message_needaction_counter &&
        !this.group_based_subscription &&
        this.store.self?.type === "partner"
    );
}
```

#### 取消固定权限
```javascript
get canUnpin() {
    return this.channel_type === "chat" && this.importantCounter === 0;
}
```

### 成员管理

#### 可见成员
```javascript
get membersThatCanSeen() {
    return this.channelMembers;  // 可被重写以排除技术成员如机器人
}
```

#### 打字成员
```javascript
typingMembers = Record.many("ChannelMember", { inverse: "threadAsTyping" });

otherTypingMembers = Record.many("ChannelMember", {
    compute() {
        return this.typingMembers.filter((member) => !member.persona?.eq(this.store.self));
    },
});

hasOtherMembersTyping = Record.attr(false, {
    compute() {
        return this.otherTypingMembers.length > 0;
    },
});
```

### 对话者信息

#### 对话者
```javascript
correspondent = Record.one("ChannelMember", {
    compute() {
        return this.computeCorrespondent();
    },
});
```

#### 对话者国家
```javascript
correspondentCountry = Record.one("Country", {
    compute() {
        return this.correspondent?.persona?.country ?? this.anonymous_country;
    },
});

get showCorrespondentCountry() {
    return (
        this.channel_type === "livechat" &&
        this.operator?.eq(this.store.self) &&
        Boolean(this.correspondentCountry)
    );
}
```

### 显示控制

#### 自我显示
```javascript
displayToSelf = Record.attr(false, {
    compute() {
        return (
            this.is_pinned ||
            (["channel", "group"].includes(this.channel_type) && this.hasSelfAsMember)
        );
    },
    onUpdate() {
        this.onPinStateUpdated();
    },
});
```

#### 重要计数器
```javascript
get importantCounter() {
    if (this.model === "mail.box") {
        return this.counter;
    }
    if (this.isChatChannel) {
        return this.message_needaction_counter;
    }
    return this.counter;
}
```

## 总线订阅管理

### 自动订阅控制
```javascript
toggleBusSubscription = Record.attr(false, {
    compute() {
        return (
            this.model === "discuss.channel" &&
            this.selfMember?.memberSince >= this.store.env.services.bus_service.startedAt
        );
    },
    onUpdate() {
        this.store.updateBusSubscription();
    },
});
```

**功能说明**:
- 自动管理总线订阅状态
- 基于成员加入时间和总线启动时间
- 状态变化时自动更新订阅

## 关注者管理

### 关注者关系
```javascript
followers = Record.many("Follower", {
    onAdd(r) {
        r.thread = this;
    },
    onDelete: (r) => r.delete(),
});

selfFollower = Record.one("Follower", {
    onAdd(r) {
        r.thread = this;
    },
    onDelete: (r) => r.delete(),
});

followersCount: number;  // 关注者数量
```

## 静态方法

### 本地ID转换
```javascript
static localIdToActiveId(localId) {
    if (!localId) {
        return undefined;
    }
    // 转换 "Thread,<model> AND <id>" 为 "<model>_<id>"
    return localId.split(",").slice(1).join("_").replace(" AND ", "_");
}
```

### 线程创建
```javascript
static new() {
    const thread = super.new(...arguments);
    Record.onChange(thread, ["state"], () => {
        if (thread.state === "open" && !this.store.env.services.ui.isSmall) {
            const cw = this.store.ChatWindow?.insert({ thread });
            thread.store.chatHub.opened.delete(cw);
            thread.store.chatHub.opened.unshift(cw);
        }
        if (thread.state === "folded") {
            const cw = this.store.ChatWindow?.insert({ thread });
            thread.store.chatHub.folded.delete(cw);
            thread.store.chatHub.folded.unshift(cw);
        }
    });
    return thread;
}
```

**功能说明**:
- 监听线程状态变化
- 自动管理聊天窗口的打开和折叠状态
- 维护聊天中心的窗口列表

## 建议收件人

### 类型定义
```typescript
interface SuggestedRecipient {
    email: string;                    // 邮箱地址
    persona: Persona | false;         // 关联的用户角色
    lang: string;                     // 语言
    reason: string;                   // 建议原因
    checked: boolean;                 // 是否被选中
}
```

### 使用场景
- 邮件发送时的收件人建议
- 基于历史对话和关系推荐
- 支持手动选择和自动选择

## 加载状态管理

### 消息加载
```javascript
loadOlder: boolean = false;   // 是否正在加载更早的消息
loadNewer: boolean = false;   // 是否正在加载更新的消息
```

### 附件加载
```javascript
areAttachmentsLoaded: boolean = false;  // 附件是否已加载
```

## 线程类型

### 频道类型
- **channel**: 公开频道
- **group**: 群组频道
- **chat**: 私聊频道
- **livechat**: 在线客服频道

### 模型类型
- **discuss.channel**: 讨论频道
- **mail.thread**: 邮件线程
- **mail.box**: 邮箱

## 使用场景

### 1. 聊天频道管理
```javascript
// 创建聊天频道
const chatThread = store.Thread.insert({
    model: "discuss.channel",
    channel_type: "chat",
    name: "私聊"
});
```

### 2. 消息线程
```javascript
// 获取消息线程
const messageThread = store.Thread.get({
    model: "mail.thread",
    id: recordId
});
```

### 3. 邮箱管理
```javascript
// 邮箱线程
const inboxThread = store.Thread.get({
    model: "mail.box",
    id: "inbox"
});
```

## 性能优化

### 1. 计算属性缓存
- 使用 Record.attr 的 compute 功能
- 自动缓存计算结果
- 依赖变化时自动重新计算

### 2. 关系字段优化
- 使用 inverse 属性维护双向关系
- 自动排序减少手动排序开销
- 延迟加载非关键数据

### 3. 状态监听优化
- 使用 Record.onChange 精确监听
- 避免不必要的状态更新
- 批量处理状态变化

## 设计模式

### 1. 观察者模式 (Observer Pattern)
- 监听状态变化自动更新相关组件
- 总线订阅的自动管理

### 2. 组合模式 (Composition Pattern)
- 组合多种关系字段构建完整的线程模型
- 模块化的功能组织

### 3. 策略模式 (Strategy Pattern)
- 根据不同线程类型采用不同的行为策略
- 可重写的计算属性支持定制化

## 注意事项

1. **复合主键**: 注意模型名和ID的组合唯一性
2. **状态同步**: 确保线程状态与UI状态一致
3. **内存管理**: 及时清理不再使用的线程对象
4. **权限控制**: 正确实现各种权限检查逻辑

该模型是整个邮件系统的核心，为所有消息交互提供了统一的数据结构和业务逻辑基础。

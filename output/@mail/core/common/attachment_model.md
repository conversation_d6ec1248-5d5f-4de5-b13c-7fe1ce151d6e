# Attachment Model - 附件模型

## 概述

`attachment_model.js` 定义了 Odoo 邮件系统中的附件模型类 `Attachment`，用于管理邮件和消息中的文件附件。该模型继承自 `Record` 基类并混入了 `FileModelMixin`，提供了完整的附件生命周期管理功能。

## 文件信息
- **路径**: `/mail/static/src/core/common/attachment_model.js`
- **行数**: 85
- **模块**: `@mail/core/common/attachment_model`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/record'           // Record 基类
'@mail/utils/common/misc'            // 工具函数
'@web/core/network/rpc'              // RPC 网络请求
'@web/core/file_viewer/file_model'   // 文件模型混入
```

## 类定义

### Attachment 类

`Attachment` 类是邮件系统中附件的核心模型，继承自 `FileModelMixin(Record)`：

```javascript
class Attachment extends FileModelMixin(Record)
```

#### 静态属性和方法

- **`static id = "id"`**: 定义记录的唯一标识符字段
- **`static records = {}`**: 存储所有附件实例的静态记录集合
- **`static get(data)`**: 获取附件实例的静态方法
- **`static insert(data)`**: 插入新附件记录的静态方法
- **`static new()`**: 创建新附件实例的静态方法

#### 关系字段

```javascript
// 与线程的一对一关系（反向关系：attachments）
thread = Record.one("Thread", { inverse: "attachments" });

// 与消息的一对一关系（反向关系：attachment_ids）
message = Record.one("Message", { inverse: "attachment_ids" });
```

#### 属性字段

- **`res_name`**: 资源名称
- **`create_date`**: 创建日期（DateTime 类型）

## 核心功能

### 1. 文件扩展名自动推导

在 `static new()` 方法中实现了智能的文件扩展名推导：

```javascript
Record.onChange(attachment, ["extension", "filename"], () => {
    if (!attachment.extension && attachment.filename) {
        attachment.extension = attachment.filename.split(".").pop();
    }
});
```

**功能说明**:
- 监听 `extension` 和 `filename` 字段的变化
- 当扩展名为空但文件名存在时，自动从文件名中提取扩展名
- 使用 `split(".").pop()` 获取最后一个点号后的内容作为扩展名

### 2. 计算属性

#### isDeletable
```javascript
get isDeletable() {
    return true;
}
```
- 返回附件是否可删除的状态
- 当前实现总是返回 `true`

#### monthYear
```javascript
get monthYear() {
    if (!this.create_date) {
        return undefined;
    }
    return `${this.create_date.monthLong}, ${this.create_date.year}`;
}
```
- 格式化创建日期为 "月份, 年份" 格式
- 使用 Luxon DateTime 的 `monthLong` 和 `year` 属性
- 例如: "January, 2024"

#### uploading
```javascript
get uploading() {
    return this.id < 0;
}
```
- 判断附件是否正在上传中
- 通过检查 ID 是否为负数来判断（临时 ID 通常为负数）

### 3. 删除操作

#### delete() - 本地删除
```javascript
delete() {
    if (this.tmpUrl) {
        URL.revokeObjectURL(this.tmpUrl);
    }
    super.delete();
}
```
**功能**:
- 清理临时 URL 对象（防止内存泄漏）
- 调用父类的删除方法进行本地清理

#### remove() - 服务器删除
```javascript
async remove() {
    if (this.id > 0) {
        const rpcParams = assignDefined(
            { attachment_id: this.id },
            { access_token: this.access_token }
        );
        const thread = this.thread || this.message?.thread;
        if (thread) {
            Object.assign(rpcParams, thread.rpcParams);
        }
        await rpc("/mail/attachment/delete", rpcParams);
    }
    this.delete();
}
```

**删除流程**:
1. **条件检查**: 只有 ID > 0 的附件才需要服务器删除
2. **参数构建**: 
   - 基础参数：`attachment_id` 和可选的 `access_token`
   - 线程参数：从关联的线程或消息的线程中获取额外的 RPC 参数
3. **服务器请求**: 调用 `/mail/attachment/delete` 端点
4. **本地清理**: 调用 `delete()` 方法进行本地清理

## 设计模式

### 1. 混入模式 (Mixin Pattern)
- 使用 `FileModelMixin(Record)` 组合文件处理能力和记录管理能力
- 实现了功能的模块化和复用

### 2. 观察者模式 (Observer Pattern)
- 使用 `Record.onChange()` 监听字段变化
- 实现了响应式的数据处理

### 3. 静态工厂模式
- 提供 `static get()`, `static insert()`, `static new()` 等静态方法
- 统一管理实例的创建和获取

## 使用场景

1. **邮件附件管理**: 在邮件消息中添加、显示、删除附件
2. **文件上传**: 处理文件上传过程中的临时状态
3. **附件预览**: 配合文件查看器显示附件内容
4. **批量操作**: 对多个附件进行批量管理

## 注意事项

1. **内存管理**: 删除附件时会自动清理 `tmpUrl` 防止内存泄漏
2. **权限控制**: 删除操作会传递 `access_token` 进行权限验证
3. **状态管理**: 通过 ID 的正负值区分持久化和临时附件
4. **关系维护**: 正确维护与 Thread 和 Message 的关联关系

## 扩展点

- 可以重写 `isDeletable` 属性实现更复杂的删除权限控制
- 可以扩展更多的计算属性用于 UI 显示
- 可以添加更多的生命周期钩子处理特殊业务逻辑

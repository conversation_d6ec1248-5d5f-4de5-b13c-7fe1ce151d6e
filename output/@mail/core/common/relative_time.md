# Odoo 相对时间组件 (Relative Time) 学习资料

## 文件概述

**文件路径**: `output/@mail/core/common/relative_time.js`  
**原始路径**: `/mail/static/src/core/common/relative_time.js`  
**模块类型**: 核心公共组件 - 相对时间显示  
**代码行数**: 47 行  
**依赖关系**: 
- `@odoo/owl` - OWL 框架
- `@web/core/l10n/translation` - 国际化翻译

## 模块功能

相对时间组件是 Odoo 邮件系统中的核心时间显示组件。该模块提供了：
- 动态相对时间显示（如"刚刚"、"5分钟前"）
- 智能更新机制
- 国际化支持
- 自动内存清理
- 高性能时间计算
- 响应式时间更新

这个组件广泛用于消息时间戳、活动时间、通知时间等场景，为用户提供直观的时间感知。

## 相对时间组件架构

### 核心组件结构
```
Relative Time Component
├── 时间计算引擎
│   ├── 相对时间计算
│   ├── 时间差计算
│   ├── 更新间隔计算
│   └── 精度控制
├── 更新机制
│   ├── 智能定时器
│   ├── 动态更新间隔
│   ├── 性能优化
│   └── 内存管理
├── 国际化支持
│   ├── 多语言文本
│   ├── 本地化格式
│   ├── 时区处理
│   └── 文化适配
└── 生命周期管理
    ├── 组件初始化
    ├── 属性更新处理
    ├── 定时器清理
    └── 内存释放
```

### 组件定义
```javascript
// 相对时间组件
class RelativeTime extends Component {
    static props = ["datetime"];
    static template = xml`<t t-esc="relativeTime"/>`;
    
    setup() {
        this.timeout = null;
        this.computeRelativeTime(this.props.datetime);
        
        // 生命周期管理
        onWillDestroy(() => clearTimeout(this.timeout));
        onWillUpdateProps((nextProps) => {
            clearTimeout(this.timeout);
            this.computeRelativeTime(nextProps.datetime);
        });
    }
}

// 时间常量
const MINUTE = 60 * 1000;      // 1分钟 = 60秒
const HOUR = 60 * MINUTE;      // 1小时 = 60分钟
```

## 核心功能详解

### 1. 智能时间计算引擎
```javascript
// 智能时间计算引擎
class TimeCalculationEngine {
    constructor() {
        this.timeUnits = {
            SECOND: 1000,
            MINUTE: 60 * 1000,
            HOUR: 60 * 60 * 1000,
            DAY: 24 * 60 * 60 * 1000,
            WEEK: 7 * 24 * 60 * 60 * 1000,
            MONTH: 30 * 24 * 60 * 60 * 1000,
            YEAR: 365 * 24 * 60 * 60 * 1000
        };
        
        this.thresholds = {
            NOW: 45 * 1000,           // 45秒内显示"刚刚"
            MINUTE_UPDATE: 60 * 1000,  // 1分钟内每秒更新
            HOUR_UPDATE: 60 * 60 * 1000, // 1小时内每分钟更新
            DAY_UPDATE: 24 * 60 * 60 * 1000 // 1天内每小时更新
        };
        
        this.setupTimeCalculation();
    }
    
    setupTimeCalculation() {
        // 设置时间计算
        this.formatStrategies = {
            'immediate': this.formatImmediate.bind(this),
            'seconds': this.formatSeconds.bind(this),
            'minutes': this.formatMinutes.bind(this),
            'hours': this.formatHours.bind(this),
            'days': this.formatDays.bind(this),
            'weeks': this.formatWeeks.bind(this),
            'months': this.formatMonths.bind(this),
            'years': this.formatYears.bind(this)
        };
        
        this.updateIntervals = {
            'immediate': 1000,        // 1秒
            'seconds': 1000,          // 1秒
            'minutes': 60 * 1000,     // 1分钟
            'hours': 60 * 60 * 1000,  // 1小时
            'days': 24 * 60 * 60 * 1000, // 1天
            'static': null            // 不更新
        };
    }
    
    computeRelativeTime(datetime) {
        // 计算相对时间
        if (!datetime) {
            return {
                text: "",
                updateInterval: null,
                strategy: 'static'
            };
        }
        
        const now = Date.now();
        const timestamp = this.ensureTimestamp(datetime);
        const delta = now - timestamp;
        const absDelta = Math.abs(delta);
        
        // 确定时间策略
        const strategy = this.determineTimeStrategy(absDelta);
        
        // 格式化时间文本
        const text = this.formatTimeText(delta, absDelta, strategy);
        
        // 计算更新间隔
        const updateInterval = this.calculateUpdateInterval(absDelta, strategy);
        
        return {
            text: text,
            updateInterval: updateInterval,
            strategy: strategy,
            delta: delta,
            absDelta: absDelta
        };
    }
    
    ensureTimestamp(datetime) {
        // 确保时间戳格式
        if (typeof datetime === 'number') {
            return datetime;
        }
        
        if (datetime && typeof datetime.ts === 'number') {
            return datetime.ts;
        }
        
        if (datetime instanceof Date) {
            return datetime.getTime();
        }
        
        if (typeof datetime === 'string') {
            return new Date(datetime).getTime();
        }
        
        return Date.now();
    }
    
    determineTimeStrategy(absDelta) {
        // 确定时间策略
        if (absDelta < this.thresholds.NOW) {
            return 'immediate';
        } else if (absDelta < this.timeUnits.MINUTE) {
            return 'seconds';
        } else if (absDelta < this.timeUnits.HOUR) {
            return 'minutes';
        } else if (absDelta < this.timeUnits.DAY) {
            return 'hours';
        } else if (absDelta < this.timeUnits.WEEK) {
            return 'days';
        } else if (absDelta < this.timeUnits.MONTH) {
            return 'weeks';
        } else if (absDelta < this.timeUnits.YEAR) {
            return 'months';
        } else {
            return 'years';
        }
    }
    
    formatTimeText(delta, absDelta, strategy) {
        // 格式化时间文本
        const formatter = this.formatStrategies[strategy];
        
        if (formatter) {
            return formatter(delta, absDelta);
        }
        
        return this.formatFallback(delta, absDelta);
    }
    
    formatImmediate(delta, absDelta) {
        // 格式化即时时间
        return delta < 0 ? _t("in a few seconds") : _t("now");
    }
    
    formatSeconds(delta, absDelta) {
        // 格式化秒级时间
        const seconds = Math.floor(absDelta / this.timeUnits.SECOND);
        
        if (delta < 0) {
            return _t("in %s seconds", seconds);
        } else {
            return seconds === 1 ? _t("1 second ago") : _t("%s seconds ago", seconds);
        }
    }
    
    formatMinutes(delta, absDelta) {
        // 格式化分钟级时间
        const minutes = Math.floor(absDelta / this.timeUnits.MINUTE);
        
        if (delta < 0) {
            return _t("in %s minutes", minutes);
        } else {
            return minutes === 1 ? _t("1 minute ago") : _t("%s minutes ago", minutes);
        }
    }
    
    formatHours(delta, absDelta) {
        // 格式化小时级时间
        const hours = Math.floor(absDelta / this.timeUnits.HOUR);
        
        if (delta < 0) {
            return _t("in %s hours", hours);
        } else {
            return hours === 1 ? _t("1 hour ago") : _t("%s hours ago", hours);
        }
    }
    
    formatDays(delta, absDelta) {
        // 格式化天级时间
        const days = Math.floor(absDelta / this.timeUnits.DAY);
        
        if (delta < 0) {
            return _t("in %s days", days);
        } else {
            return days === 1 ? _t("1 day ago") : _t("%s days ago", days);
        }
    }
    
    formatWeeks(delta, absDelta) {
        // 格式化周级时间
        const weeks = Math.floor(absDelta / this.timeUnits.WEEK);
        
        if (delta < 0) {
            return _t("in %s weeks", weeks);
        } else {
            return weeks === 1 ? _t("1 week ago") : _t("%s weeks ago", weeks);
        }
    }
    
    formatMonths(delta, absDelta) {
        // 格式化月级时间
        const months = Math.floor(absDelta / this.timeUnits.MONTH);
        
        if (delta < 0) {
            return _t("in %s months", months);
        } else {
            return months === 1 ? _t("1 month ago") : _t("%s months ago", months);
        }
    }
    
    formatYears(delta, absDelta) {
        // 格式化年级时间
        const years = Math.floor(absDelta / this.timeUnits.YEAR);
        
        if (delta < 0) {
            return _t("in %s years", years);
        } else {
            return years === 1 ? _t("1 year ago") : _t("%s years ago", years);
        }
    }
    
    formatFallback(delta, absDelta) {
        // 回退格式化
        if (delta < 0) {
            return _t("in the future");
        } else {
            return _t("long ago");
        }
    }
    
    calculateUpdateInterval(absDelta, strategy) {
        // 计算更新间隔
        const baseInterval = this.updateIntervals[strategy];
        
        if (!baseInterval) {
            return null; // 不需要更新
        }
        
        // 动态调整更新间隔
        if (strategy === 'immediate' || strategy === 'seconds') {
            // 秒级更新：根据剩余时间调整
            return Math.min(baseInterval, absDelta);
        } else if (strategy === 'minutes') {
            // 分钟级更新：每分钟更新
            return baseInterval;
        } else if (strategy === 'hours') {
            // 小时级更新：每小时更新
            return baseInterval;
        } else {
            // 天级及以上：每天更新
            return this.timeUnits.DAY;
        }
    }
    
    getTimeUnitInfo(absDelta) {
        // 获取时间单位信息
        const units = [
            { name: 'year', value: this.timeUnits.YEAR },
            { name: 'month', value: this.timeUnits.MONTH },
            { name: 'week', value: this.timeUnits.WEEK },
            { name: 'day', value: this.timeUnits.DAY },
            { name: 'hour', value: this.timeUnits.HOUR },
            { name: 'minute', value: this.timeUnits.MINUTE },
            { name: 'second', value: this.timeUnits.SECOND }
        ];
        
        for (const unit of units) {
            if (absDelta >= unit.value) {
                return {
                    unit: unit.name,
                    value: Math.floor(absDelta / unit.value),
                    remainder: absDelta % unit.value
                };
            }
        }
        
        return {
            unit: 'second',
            value: 0,
            remainder: absDelta
        };
    }
}
```

### 2. 动态更新管理器
```javascript
// 动态更新管理器
class DynamicUpdateManager {
    constructor(component) {
        this.component = component;
        this.timeCalculator = new TimeCalculationEngine();
        this.updateQueue = new Map();
        this.isUpdating = false;
        this.setupUpdateManagement();
    }
    
    setupUpdateManagement() {
        // 设置更新管理
        this.updateStrategies = {
            'aggressive': {
                immediate: 1000,
                seconds: 1000,
                minutes: 30000,
                hours: 300000,
                days: 3600000
            },
            'balanced': {
                immediate: 1000,
                seconds: 5000,
                minutes: 60000,
                hours: 600000,
                days: 3600000
            },
            'conservative': {
                immediate: 5000,
                seconds: 10000,
                minutes: 120000,
                hours: 1800000,
                days: 7200000
            }
        };
        
        this.currentStrategy = 'balanced';
        this.performanceMetrics = {
            updateCount: 0,
            averageUpdateTime: 0,
            lastUpdateTime: 0
        };
    }
    
    scheduleUpdate(datetime) {
        // 安排更新
        const result = this.timeCalculator.computeRelativeTime(datetime);
        
        // 更新显示文本
        this.component.relativeTime = result.text;
        
        // 清除现有定时器
        this.clearCurrentTimeout();
        
        // 安排下次更新
        if (result.updateInterval) {
            this.scheduleNextUpdate(datetime, result.updateInterval);
        }
        
        // 更新性能指标
        this.updatePerformanceMetrics();
        
        return result;
    }
    
    scheduleNextUpdate(datetime, interval) {
        // 安排下次更新
        const strategy = this.updateStrategies[this.currentStrategy];
        const adjustedInterval = this.adjustUpdateInterval(interval, strategy);
        
        this.component.timeout = setTimeout(() => {
            this.scheduleUpdate(datetime);
            this.component.render();
        }, adjustedInterval);
    }
    
    adjustUpdateInterval(baseInterval, strategy) {
        // 调整更新间隔
        const result = this.timeCalculator.computeRelativeTime(this.component.props.datetime);
        const strategyInterval = strategy[result.strategy];
        
        if (strategyInterval) {
            return Math.max(baseInterval, strategyInterval);
        }
        
        return baseInterval;
    }
    
    clearCurrentTimeout() {
        // 清除当前定时器
        if (this.component.timeout) {
            clearTimeout(this.component.timeout);
            this.component.timeout = null;
        }
    }
    
    updatePerformanceMetrics() {
        // 更新性能指标
        const now = Date.now();
        this.performanceMetrics.updateCount++;
        
        if (this.performanceMetrics.lastUpdateTime > 0) {
            const updateTime = now - this.performanceMetrics.lastUpdateTime;
            const totalTime = this.performanceMetrics.averageUpdateTime * (this.performanceMetrics.updateCount - 1) + updateTime;
            this.performanceMetrics.averageUpdateTime = totalTime / this.performanceMetrics.updateCount;
        }
        
        this.performanceMetrics.lastUpdateTime = now;
    }
    
    optimizeUpdateStrategy() {
        // 优化更新策略
        const avgTime = this.performanceMetrics.averageUpdateTime;
        
        if (avgTime > 100) {
            // 如果更新时间过长，使用保守策略
            this.currentStrategy = 'conservative';
        } else if (avgTime < 10) {
            // 如果更新时间很短，可以使用激进策略
            this.currentStrategy = 'aggressive';
        } else {
            // 默认使用平衡策略
            this.currentStrategy = 'balanced';
        }
    }
    
    handlePropsUpdate(nextProps) {
        // 处理属性更新
        this.clearCurrentTimeout();
        this.scheduleUpdate(nextProps.datetime);
    }
    
    handleDestroy() {
        // 处理组件销毁
        this.clearCurrentTimeout();
        this.updateQueue.clear();
    }
    
    getUpdateStats() {
        // 获取更新统计
        return {
            ...this.performanceMetrics,
            currentStrategy: this.currentStrategy,
            queueSize: this.updateQueue.size
        };
    }
}
```

## 使用示例

### 1. 基本相对时间使用
```javascript
// 使用相对时间组件
class MessageItem extends Component {
    static template = xml`
        <div class="message-item">
            <div class="message-content">
                <t t-esc="message.body"/>
            </div>
            <div class="message-time">
                <RelativeTime datetime="message.datetime"/>
            </div>
        </div>
    `;
    
    static components = { RelativeTime };
    
    setup() {
        this.message = this.props.message;
    }
}
```

### 2. 高级相对时间使用
```javascript
// 高级相对时间使用
class AdvancedTimeDisplay extends Component {
    static template = xml`
        <div class="time-display">
            <RelativeTime 
                datetime="datetime"
                t-ref="relativeTime"/>
            <div class="time-tooltip" t-if="showTooltip">
                <t t-esc="absoluteTime"/>
            </div>
        </div>
    `;
    
    static components = { RelativeTime };
    
    setup() {
        this.datetime = this.props.datetime;
        this.showTooltip = useState(false);
        this.relativeTimeRef = useRef("relativeTime");
        
        this.setupTimeDisplay();
    }
    
    setupTimeDisplay() {
        // 设置时间显示
        this.updateManager = new DynamicUpdateManager(this.relativeTimeRef.comp);
        
        // 计算绝对时间
        this.absoluteTime = this.formatAbsoluteTime(this.datetime);
        
        // 设置鼠标悬停事件
        onMounted(() => {
            const element = this.relativeTimeRef.el;
            element.addEventListener('mouseenter', this.showAbsoluteTime.bind(this));
            element.addEventListener('mouseleave', this.hideAbsoluteTime.bind(this));
        });
    }
    
    formatAbsoluteTime(datetime) {
        // 格式化绝对时间
        if (!datetime) return '';
        
        const date = new Date(datetime.ts || datetime);
        return date.toLocaleString();
    }
    
    showAbsoluteTime() {
        // 显示绝对时间
        this.showTooltip = true;
    }
    
    hideAbsoluteTime() {
        // 隐藏绝对时间
        this.showTooltip = false;
    }
}
```

### 3. 批量时间管理
```javascript
// 批量时间管理
class TimeManager extends Component {
    setup() {
        this.timeComponents = new Map();
        this.globalUpdateManager = new GlobalTimeUpdateManager();
        this.setupBatchTimeManagement();
    }
    
    setupBatchTimeManagement() {
        // 设置批量时间管理
        this.updateInterval = setInterval(() => {
            this.batchUpdateTimes();
        }, 60000); // 每分钟批量更新
        
        onWillUnmount(() => {
            clearInterval(this.updateInterval);
        });
    }
    
    registerTimeComponent(id, component) {
        // 注册时间组件
        this.timeComponents.set(id, component);
    }
    
    unregisterTimeComponent(id) {
        // 注销时间组件
        this.timeComponents.delete(id);
    }
    
    batchUpdateTimes() {
        // 批量更新时间
        for (const [id, component] of this.timeComponents) {
            try {
                component.updateManager.scheduleUpdate(component.props.datetime);
                component.render();
            } catch (error) {
                console.error(`时间组件更新失败 (${id}):`, error);
            }
        }
    }
    
    getTimeStats() {
        // 获取时间统计
        const stats = {
            totalComponents: this.timeComponents.size,
            updateCounts: {},
            averageUpdateTimes: {}
        };
        
        for (const [id, component] of this.timeComponents) {
            const componentStats = component.updateManager.getUpdateStats();
            stats.updateCounts[id] = componentStats.updateCount;
            stats.averageUpdateTimes[id] = componentStats.averageUpdateTime;
        }
        
        return stats;
    }
}
```

## 相对时间组件特性

### 1. 智能更新机制
相对时间组件提供了智能的更新机制：
- **动态间隔**: 根据时间差动态调整更新频率
- **性能优化**: 避免不必要的更新和渲染
- **内存管理**: 自动清理定时器防止内存泄漏
- **批量更新**: 支持批量时间组件的统一更新

### 2. 多语言支持
```javascript
// 多语言时间文本
const timeTexts = {
    'en': {
        'now': 'now',
        'in_seconds': 'in a few seconds',
        'minutes_ago': '%s minutes ago',
        'hours_ago': '%s hours ago'
    },
    'zh': {
        'now': '刚刚',
        'in_seconds': '几秒后',
        'minutes_ago': '%s分钟前',
        'hours_ago': '%s小时前'
    }
};
```

### 3. 高性能设计
- **最小化重渲染**: 只在必要时触发组件重渲染
- **智能定时器**: 根据时间精度调整定时器间隔
- **内存优化**: 及时清理不再需要的定时器
- **批量处理**: 支持多个时间组件的批量更新

### 4. 扩展性支持
- **自定义格式**: 支持自定义时间格式化函数
- **策略模式**: 可配置的更新策略
- **事件系统**: 支持时间更新事件监听
- **插件架构**: 支持功能扩展插件

## 最佳实践

### 1. 组件使用
```javascript
// ✅ 推荐：正确的相对时间使用
<RelativeTime datetime="message.datetime"/>
```

### 2. 性能优化
```javascript
// ✅ 推荐：批量时间管理
const timeManager = new TimeManager();
timeManager.registerTimeComponent('msg1', relativeTimeComponent);
```

### 3. 内存管理
```javascript
// ✅ 推荐：正确的生命周期管理
onWillDestroy(() => clearTimeout(this.timeout));
onWillUpdateProps((nextProps) => {
    clearTimeout(this.timeout);
    this.computeRelativeTime(nextProps.datetime);
});
```

### 4. 错误处理
```javascript
// ✅ 推荐：安全的时间处理
computeRelativeTime(datetime) {
    if (!datetime) {
        this.relativeTime = "";
        return;
    }
    // 处理时间逻辑
}
```

## 总结

Odoo 相对时间组件提供了高效、智能的时间显示功能：

**核心优势**:
- **智能更新**: 根据时间精度动态调整更新频率
- **高性能**: 最小化重渲染和内存使用
- **国际化**: 完整的多语言支持
- **易用性**: 简单的API和灵活的配置
- **可扩展**: 支持自定义格式和更新策略

**适用场景**:
- 消息时间戳显示
- 活动时间显示
- 通知时间显示
- 任何需要相对时间的场景

**设计优势**:
- 组件化架构
- 智能更新机制
- 内存安全管理
- 性能优化设计

这个相对时间组件为 Odoo 邮件系统提供了专业的时间显示能力，在保持高性能的同时，为用户提供了直观的时间感知体验。

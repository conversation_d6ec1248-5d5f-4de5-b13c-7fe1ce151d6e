# Attachment Uploader Hook - 附件上传钩子

## 概述

`attachment_uploader_hook.js` 提供了一个用于附件上传的 React Hook 和工具类，简化了在 Odoo 组件中处理文件上传的逻辑。该模块封装了附件上传服务，提供了便捷的文件上传接口和状态管理。

## 文件信息
- **路径**: `/mail/static/src/core/common/attachment_uploader_hook.js`
- **行数**: 46
- **模块**: `@mail/core/common/attachment_uploader_hook`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'              // useState Hook
'@web/core/utils/hooks'  // useService Hook
```

## 工具函数

### dataUrlToBlob()

将 Data URL 转换为 Blob 对象的工具函数：

```javascript
function dataUrlToBlob(data, type) {
    const binData = window.atob(data);
    const uiArr = new Uint8Array(binData.length);
    uiArr.forEach((_, index) => (uiArr[index] = binData.charCodeAt(index)));
    return new Blob([uiArr], { type });
}
```

**功能说明**:
- **参数**:
  - `data`: Base64 编码的数据字符串
  - `type`: MIME 类型字符串
- **返回**: Blob 对象
- **用途**: 将 Base64 数据转换为可上传的文件对象

**转换过程**:
1. 使用 `window.atob()` 解码 Base64 数据
2. 创建 Uint8Array 存储二进制数据
3. 逐字节转换字符编码为数值
4. 创建并返回 Blob 对象

## AttachmentUploader 类

核心的附件上传器类，封装了附件上传的所有操作：

```javascript
class AttachmentUploader {
    constructor(thread, { composer } = {}) {
        this.attachmentUploadService = useService("mail.attachment_upload");
        Object.assign(this, { thread, composer });
    }
}
```

### 构造函数

**参数**:
- `thread`: 目标线程对象
- `options.composer`: 可选的编辑器对象

**初始化**:
- 获取附件上传服务实例
- 保存线程和编辑器引用

### 核心方法

#### uploadData() - 上传数据

```javascript
uploadData({ data, name, type }, options) {
    const file = new File([dataUrlToBlob(data, type)], name, { type });
    return this.uploadFile(file, options);
}
```

**功能**:
- 将数据对象转换为文件并上传
- 支持 Base64 数据直接上传

**参数**:
- `data`: Base64 编码的文件数据
- `name`: 文件名
- `type`: MIME 类型
- `options`: 上传选项

**流程**:
1. 调用 `dataUrlToBlob()` 转换数据
2. 创建 File 对象
3. 调用 `uploadFile()` 执行上传

#### uploadFile() - 上传文件

```javascript
async uploadFile(file, options) {
    return this.attachmentUploadService.upload(this.thread, this.composer, file, options);
}
```

**功能**:
- 上传 File 对象
- 返回上传结果的 Promise

**参数**:
- `file`: File 对象
- `options`: 上传选项（如活动ID等）

#### unlink() - 删除附件

```javascript
async unlink(attachment) {
    await this.attachmentUploadService.unlink(attachment);
}
```

**功能**:
- 删除指定的附件
- 支持取消正在上传的文件

## useAttachmentUploader Hook

React Hook 函数，为组件提供响应式的附件上传器：

```javascript
function useAttachmentUploader(thread, { composer, onFileUploaded } = {}) {
    return useState(new AttachmentUploader(...arguments));
}
```

### 参数

- **`thread`**: `import("models").Thread` - 目标线程
- **`options`**: 配置对象
  - `composer`: `import("models").Composer` - 可选的编辑器对象
  - `onFileUploaded`: `function` - 文件上传完成回调（当前未使用）

### 返回值

返回一个响应式的 `AttachmentUploader` 实例，当其状态发生变化时会触发组件重新渲染。

### 使用示例

```javascript
// 在组件中使用
const uploader = useAttachmentUploader(thread, { composer });

// 上传文件
const handleFileUpload = async (file) => {
    try {
        const attachment = await uploader.uploadFile(file);
        console.log('上传成功:', attachment);
    } catch (error) {
        console.error('上传失败:', error);
    }
};

// 上传 Base64 数据
const handleDataUpload = async (data, name, type) => {
    try {
        const attachment = await uploader.uploadData({ data, name, type });
        console.log('数据上传成功:', attachment);
    } catch (error) {
        console.error('数据上传失败:', error);
    }
};

// 删除附件
const handleDelete = async (attachment) => {
    try {
        await uploader.unlink(attachment);
        console.log('删除成功');
    } catch (error) {
        console.error('删除失败:', error);
    }
};
```

## 设计模式

### 1. 适配器模式 (Adapter Pattern)
- `AttachmentUploader` 类作为附件上传服务的适配器
- 简化了服务接口，提供更友好的API

### 2. Hook 模式 (Hook Pattern)
- `useAttachmentUploader` 遵循 React Hook 设计模式
- 提供响应式状态管理

### 3. 工厂模式 (Factory Pattern)
- Hook 函数作为工厂创建上传器实例
- 封装了实例化逻辑

## 使用场景

### 1. 文件拖拽上传
```javascript
const uploader = useAttachmentUploader(thread);

const handleDrop = async (event) => {
    const files = Array.from(event.dataTransfer.files);
    for (const file of files) {
        await uploader.uploadFile(file);
    }
};
```

### 2. 粘贴图片上传
```javascript
const uploader = useAttachmentUploader(thread, { composer });

const handlePaste = async (event) => {
    const items = Array.from(event.clipboardData.items);
    for (const item of items) {
        if (item.type.startsWith('image/')) {
            const file = item.getAsFile();
            await uploader.uploadFile(file);
        }
    }
};
```

### 3. Base64 数据上传
```javascript
const uploader = useAttachmentUploader(thread);

const uploadScreenshot = async (base64Data) => {
    await uploader.uploadData({
        data: base64Data,
        name: 'screenshot.png',
        type: 'image/png'
    });
};
```

## 优势特点

### 1. 简化接口
- 封装了复杂的上传服务调用
- 提供直观的方法名和参数

### 2. 类型支持
- 支持 File 对象直接上传
- 支持 Base64 数据转换上传

### 3. 响应式状态
- 使用 `useState` 提供响应式更新
- 自动触发组件重新渲染

### 4. 错误处理
- 异步方法支持 try-catch 错误处理
- 透传底层服务的错误信息

## 注意事项

1. **内存管理**: Base64 转换会创建临时 Blob 对象，注意内存使用
2. **文件大小**: 大文件上传可能需要额外的进度显示
3. **错误处理**: 需要在调用方处理上传失败的情况
4. **类型检查**: 建议在上传前验证文件类型和大小

## 扩展建议

1. **进度回调**: 可以添加上传进度回调支持
2. **批量上传**: 支持多文件同时上传
3. **预览功能**: 集成文件预览功能
4. **验证机制**: 添加文件类型和大小验证

# Notification Permission Service - 通知权限服务

## 概述

`notification_permission_service.js` 实现了 Odoo 邮件系统中的通知权限管理服务，负责检测、请求和管理浏览器的通知权限。该服务处理不同平台的权限差异，提供统一的权限管理接口，支持权限状态监听和用户友好的权限请求流程，是邮件系统通知功能的基础服务。

## 文件信息
- **路径**: `/mail/static/src/core/common/notification_permission_service.js`
- **行数**: 80
- **模块**: `@mail/core/common/notification_permission_service`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                              // OWL 框架（响应式）
'@web/core/browser/browser'              // 浏览器工具
'@web/core/browser/feature_detection'    // 特性检测
'@web/core/l10n/translation'             // 国际化
'@web/core/registry'                     // 服务注册表
```

## 服务定义

### notificationPermissionService 对象

```javascript
const notificationPermissionService = {
    dependencies: ["notification"],
    
    _normalizePermission(permission) {
        // 权限状态标准化
    },
    
    async start(env, services) {
        // 服务启动逻辑
    }
};
```

### 服务依赖

- **`notification`**: 通知服务，用于显示权限状态反馈

## 核心功能

### 1. 权限状态标准化

```javascript
_normalizePermission(permission) {
    switch (permission) {
        case "default":
            return "prompt";        // 标准化为 "prompt"
        case undefined:
            return "denied";        // 未定义视为拒绝
        default:
            return permission;      // 保持原状态
    }
}
```

**标准化逻辑**:
- **`"default"`** → **`"prompt"`**: 浏览器默认状态转换为提示状态
- **`undefined`** → **`"denied"`**: 未定义权限视为拒绝
- **其他状态**: 保持不变（`"granted"`, `"denied"`, `"prompt"`）

### 2. 权限状态检测

```javascript
// 权限查询逻辑
let permission;
try {
    permission = await browser.navigator?.permissions?.query({
        name: "notifications",
    });
} catch {
    // 静默处理查询失败
}
```

**检测机制**:
- 使用 Permissions API 查询通知权限
- 静默处理不支持的浏览器
- 回退到 `Notification.permission` 属性

### 3. 平台特殊处理

```javascript
permission: isIosApp() || isAndroidApp()
    ? "denied"
    : this._normalizePermission(
          permission?.state ?? browser.Notification?.permission
      )
```

**平台逻辑**:
- **移动应用**: iOS 和 Android 应用默认拒绝通知
- **Web浏览器**: 使用实际的权限状态
- **回退机制**: 优先使用 Permissions API，回退到 Notification API

### 4. 权限请求

```javascript
requestPermission: async () => {
    if (browser.Notification && state.permission === "prompt") {
        state.permission = this._normalizePermission(
            await browser.Notification.requestPermission()
        );
        
        // 用户反馈
        if (state.permission === "denied") {
            notification.add(_t("Odoo will not send notifications on this device."), {
                type: "warning",
                title: _t("Notifications blocked"),
            });
        } else if (state.permission === "granted") {
            notification.add(_t("Odoo will send notifications on this device!"), {
                type: "success",
                title: _t("Notifications allowed"),
            });
        }
    }
}
```

**请求流程**:
1. 检查浏览器支持和当前状态
2. 调用浏览器权限请求API
3. 标准化返回的权限状态
4. 显示用户友好的反馈消息

## 权限状态

### 权限状态枚举

```javascript
// 权限状态类型
type PermissionState = "prompt" | "granted" | "denied";
```

### 状态说明

- **`"prompt"`**: 用户尚未决定，可以请求权限
- **`"granted"`**: 用户已授予通知权限
- **`"denied"`**: 用户已拒绝通知权限

## 响应式状态

### 状态对象

```javascript
const state = reactive({
    permission: "prompt" | "granted" | "denied",
    requestPermission: async () => { /* 请求权限的方法 */ }
});
```

### 状态监听

```javascript
// 监听权限状态变化
if (permission) {
    permission.addEventListener("change", () => {
        state.permission = permission.state;
    });
}
```

**监听机制**:
- 使用 Permissions API 的事件监听
- 自动更新响应式状态
- 实时反映权限变化

## 使用场景

### 1. 权限状态检查

```javascript
// 检查当前权限状态
const checkNotificationPermission = () => {
    const permissionService = env.services['mail.notification.permission'];
    
    switch (permissionService.permission) {
        case 'granted':
            console.log('通知权限已授予');
            return true;
        case 'denied':
            console.log('通知权限被拒绝');
            return false;
        case 'prompt':
            console.log('可以请求通知权限');
            return null;
    }
};
```

### 2. 权限请求流程

```javascript
// 请求通知权限
const requestNotificationPermission = async () => {
    const permissionService = env.services['mail.notification.permission'];
    
    if (permissionService.permission === 'prompt') {
        await permissionService.requestPermission();
        
        // 检查请求结果
        if (permissionService.permission === 'granted') {
            enableNotifications();
        } else {
            showPermissionDeniedMessage();
        }
    }
};
```

### 3. 条件性功能启用

```javascript
// 根据权限状态启用功能
const initializeNotificationFeatures = () => {
    const permissionService = env.services['mail.notification.permission'];
    
    if (permissionService.permission === 'granted') {
        // 启用桌面通知
        enableDesktopNotifications();
        
        // 显示通知设置
        showNotificationSettings();
    } else {
        // 显示权限请求按钮
        showPermissionRequestButton();
    }
};
```

### 4. 权限状态监听

```javascript
// 监听权限状态变化
const watchPermissionChanges = () => {
    const permissionService = env.services['mail.notification.permission'];
    
    // 使用响应式监听
    watchEffect(() => {
        const permission = permissionService.permission;
        
        if (permission === 'granted') {
            onPermissionGranted();
        } else if (permission === 'denied') {
            onPermissionDenied();
        }
    });
};
```

## 平台适配

### 1. 移动应用处理

```javascript
// 移动应用的特殊处理
const handleMobileApp = () => {
    if (isIosApp() || isAndroidApp()) {
        // 移动应用中禁用Web通知
        return {
            permission: 'denied',
            requestPermission: () => {
                showMobileNotificationGuide();
            }
        };
    }
};
```

### 2. 浏览器兼容性

```javascript
// 浏览器兼容性检查
const checkBrowserSupport = () => {
    if (!browser.Notification) {
        console.warn('浏览器不支持通知API');
        return false;
    }
    
    if (!browser.navigator?.permissions) {
        console.warn('浏览器不支持权限API');
        // 回退到基础通知API
    }
    
    return true;
};
```

### 3. 特性检测

```javascript
// 检测通知相关特性
const detectNotificationFeatures = () => {
    return {
        hasNotificationAPI: !!browser.Notification,
        hasPermissionsAPI: !!browser.navigator?.permissions,
        hasServiceWorker: !!browser.navigator?.serviceWorker,
        supportsPersistent: 'persistent' in browser.Notification.prototype
    };
};
```

## 用户体验优化

### 1. 权限请求时机

```javascript
// 智能的权限请求时机
const smartPermissionRequest = () => {
    // 在用户执行相关操作时请求
    const requestOnUserAction = (action) => {
        if (action === 'enable_notifications') {
            requestNotificationPermission();
        }
    };
    
    // 避免页面加载时立即请求
    const delayedRequest = () => {
        setTimeout(() => {
            if (shouldRequestPermission()) {
                showPermissionPrompt();
            }
        }, 5000);
    };
};
```

### 2. 权限被拒绝的处理

```javascript
// 权限被拒绝时的用户指导
const handlePermissionDenied = () => {
    showNotification({
        type: 'info',
        title: '通知权限被拒绝',
        message: '您可以在浏览器设置中重新启用通知权限',
        actions: [
            {
                label: '查看指导',
                action: () => showPermissionGuide()
            }
        ]
    });
};
```

### 3. 权限状态指示

```javascript
// 显示权限状态指示器
const renderPermissionIndicator = (permission) => {
    const indicators = {
        granted: {
            icon: 'fa-bell',
            color: 'success',
            text: '通知已启用'
        },
        denied: {
            icon: 'fa-bell-slash',
            color: 'danger',
            text: '通知已禁用'
        },
        prompt: {
            icon: 'fa-bell-o',
            color: 'warning',
            text: '点击启用通知'
        }
    };
    
    const indicator = indicators[permission];
    return `
        <div class="permission-indicator text-${indicator.color}">
            <i class="fa ${indicator.icon}"></i>
            ${indicator.text}
        </div>
    `;
};
```

## 错误处理

### 1. API调用失败

```javascript
// 处理权限API调用失败
const safePermissionRequest = async () => {
    try {
        const permission = await browser.Notification.requestPermission();
        return normalizePermission(permission);
    } catch (error) {
        console.error('权限请求失败:', error);
        
        // 回退处理
        if (error.name === 'NotAllowedError') {
            return 'denied';
        } else {
            return 'prompt';
        }
    }
};
```

### 2. 不支持的环境

```javascript
// 处理不支持通知的环境
const handleUnsupportedEnvironment = () => {
    if (!browser.Notification) {
        return {
            permission: 'denied',
            requestPermission: () => {
                showUnsupportedMessage();
            }
        };
    }
};
```

## 性能优化

### 1. 权限状态缓存

```javascript
// 缓存权限状态
let cachedPermission = null;
let cacheTimestamp = 0;

const getCachedPermission = () => {
    const now = Date.now();
    if (cachedPermission && now - cacheTimestamp < 60000) { // 1分钟缓存
        return cachedPermission;
    }
    return null;
};
```

### 2. 延迟初始化

```javascript
// 延迟初始化权限检查
const lazyInitPermission = () => {
    let initialized = false;
    
    return () => {
        if (!initialized) {
            initializePermissionService();
            initialized = true;
        }
    };
};
```

## 设计模式

### 1. 单例模式 (Singleton Pattern)
- 全局唯一的权限服务实例
- 统一的权限状态管理

### 2. 观察者模式 (Observer Pattern)
- 监听权限状态变化
- 响应式状态更新

### 3. 适配器模式 (Adapter Pattern)
- 适配不同浏览器的权限API
- 统一的权限接口

## 注意事项

1. **用户体验**: 避免过度请求权限
2. **浏览器兼容性**: 处理不同浏览器的差异
3. **移动端限制**: 正确处理移动应用的限制
4. **权限持久性**: 权限状态可能会变化

## 扩展建议

1. **权限历史**: 记录权限请求历史
2. **智能提示**: 基于用户行为的智能权限提示
3. **权限分析**: 提供权限使用统计
4. **自定义策略**: 支持自定义权限请求策略
5. **权限恢复**: 支持权限状态的恢复机制

该服务为邮件系统提供了完整的通知权限管理功能，确保通知功能在不同平台和浏览器中的正常工作。

# Thread - 线程组件

## 概述

`thread.js` 是 Odoo 邮件系统中的核心显示组件，负责渲染线程中的消息列表。该组件提供了消息的滚动显示、分页加载、日期分组、消息高亮、跳转到最新消息等功能，是用户查看和浏览消息的主要界面。

## 文件信息
- **路径**: `/mail/static/src/core/common/thread.js`
- **行数**: 573
- **模块**: `@mail/core/common/thread`

## 依赖关系

```javascript
// 核心组件依赖
'@mail/core/common/date_section'      // 日期分段组件
'@mail/core/common/message'           // 消息组件
'@mail/core/common/record'            // 记录基类
'@mail/utils/common/hooks'            // 邮件系统钩子

// OWL 和 Web 核心依赖
'@odoo/owl'                          // OWL 框架
'@web/core/browser/browser'          // 浏览器工具
'@web/core/l10n/translation'         // 国际化
'@web/core/transition'               // 过渡动画
'@web/core/utils/hooks'              // Web 核心钩子
'@web/core/utils/strings'            // 字符串工具
```

## 组件定义

### Thread 类

```javascript
class Thread extends Component {
    static components = { 
        Message,      // 消息组件
        Transition,   // 过渡动画组件
        DateSection   // 日期分段组件
    };
    static template = "mail.Thread";
}
```

## Props 配置

### Props 接口

```typescript
interface Props {
    thread: import("models").Thread;                    // 线程对象（必需）
    isInChatWindow?: boolean;                          // 是否在聊天窗口中
    jumpPresent?: number;                              // 跳转到最新消息的触发器
    messageEdition?: MessageEdition;                   // 消息编辑状态
    messageToReplyTo?: MessageToReplyTo;               // 回复目标消息
    order?: "asc" | "desc";                           // 消息排序方式
    searchTerm?: string;                              // 搜索词
    scrollRef?: Ref;                                  // 滚动容器引用
    showDates?: boolean;                              // 是否显示日期
    showEmptyMessage?: boolean;                       // 是否显示空消息提示
    showJumpPresent?: boolean;                        // 是否显示跳转到最新按钮
    messageActions?: boolean;                         // 是否显示消息操作
}
```

### 默认属性

```javascript
static defaultProps = {
    isInChatWindow: false,    // 默认不在聊天窗口中
    jumpPresent: 0,           // 默认不跳转
    order: "asc",             // 默认升序排列
    showDates: true,          // 默认显示日期
    showEmptyMessage: true,   // 默认显示空消息提示
    showJumpPresent: true,    // 默认显示跳转按钮
    messageActions: true,     // 默认显示消息操作
};
```

## 核心常量

### 视口和消息阈值

```javascript
const PRESENT_VIEWPORT_THRESHOLD = 3;    // 视口阈值
const PRESENT_MESSAGE_THRESHOLD = 10;    // 消息阈值
```

**功能说明**:
- `PRESENT_VIEWPORT_THRESHOLD`: 判断是否接近最新消息的视口阈值
- `PRESENT_MESSAGE_THRESHOLD`: 判断是否需要显示跳转按钮的消息数量阈值

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    
    // 基础服务和工具
    this.store = useState(useService("mail.store"));
    this.orm = useService("orm");
    this.escape = escape;
    
    // 组件状态
    this.state = useState({
        isReplyingTo: false,        // 是否正在回复
        mountedAndLoaded: false,    // 是否已挂载并加载
        showJumpPresent: false,     // 是否显示跳转按钮
    });
    
    // DOM 引用
    this.present = useRef("load-newer");
    this.jumpPresentRef = useRef("jump-present");
    this.root = useRef("messages");
    
    // 消息引用管理
    this.registerMessageRef = this.registerMessageRef.bind(this);
    this.refByMessageId = reactive(new Map(), () => this.scrollToHighlighted());
    
    // 消息高亮
    this.messageHighlight = this.env.messageHighlight
        ? useState(this.env.messageHighlight)
        : null;
    this.scrollingToHighlight = false;
    
    // 跳转状态
    this.lastJumpPresent = this.props.jumpPresent;
}
```

## 核心功能

### 1. 消息引用管理

#### 注册消息引用
```javascript
registerMessageRef(message, ref) {
    if (ref) {
        this.refByMessageId.set(message.id, ref);
    } else {
        this.refByMessageId.delete(message.id);
    }
}
```

**功能说明**:
- 管理每个消息组件的 DOM 引用
- 用于滚动定位和高亮显示
- 响应式 Map 自动触发滚动更新

### 2. 消息高亮和滚动

#### 滚动到高亮消息
```javascript
useEffect(
    () => this.scrollToHighlighted(),
    () => [this.messageHighlight?.highlightedMessageId]
);

scrollToHighlighted() {
    if (!this.messageHighlight?.highlightedMessageId || this.scrollingToHighlight) {
        return;
    }
    
    const messageRef = this.refByMessageId.get(this.messageHighlight.highlightedMessageId);
    if (messageRef?.el) {
        this.scrollingToHighlight = true;
        messageRef.el.scrollIntoView({ behavior: "smooth", block: "center" });
        setTimeout(() => {
            this.scrollingToHighlight = false;
        }, 1000);
    }
}
```

### 3. 可见性检测

#### 使用可见性钩子
```javascript
this.visible = useVisible("root", {
    threshold: 0.1,
    onVisibilityChange: (isVisible) => {
        if (isVisible && this.props.thread) {
            this.props.thread.markAsRead();
        }
    },
});
```

**功能说明**:
- 检测线程组件是否在视口中可见
- 可见时自动标记线程为已读
- 使用 Intersection Observer API

### 4. 跳转到最新消息

#### 跳转逻辑
```javascript
useEffect(
    (jumpPresent) => {
        if (jumpPresent > this.lastJumpPresent) {
            this.jumpToPresent();
            this.lastJumpPresent = jumpPresent;
        }
    },
    () => [this.props.jumpPresent]
);

jumpToPresent() {
    if (this.present.el) {
        this.present.el.scrollIntoView({ behavior: "smooth" });
    }
}
```

### 5. 消息分组和排序

#### 消息分组逻辑
```javascript
get groupedMessages() {
    const messages = this.props.thread.messages;
    const grouped = [];
    let currentGroup = null;
    
    for (const message of messages) {
        const messageDate = message.date.toISODate();
        
        if (!currentGroup || currentGroup.date !== messageDate) {
            currentGroup = {
                date: messageDate,
                messages: []
            };
            grouped.push(currentGroup);
        }
        
        currentGroup.messages.push(message);
    }
    
    return grouped;
}
```

### 6. 空状态处理

#### 空消息显示
```javascript
get showEmptyMessage() {
    return (
        this.props.showEmptyMessage &&
        this.props.thread.messages.length === 0 &&
        !this.props.thread.isLoading
    );
}

get emptyMessageText() {
    if (this.props.thread.channel_type === "chat") {
        return _t("Start a conversation");
    }
    return _t("No messages");
}
```

## 滚动管理

### 滚动容器

```javascript
// 使用外部滚动引用或内部根元素
get scrollContainer() {
    return this.props.scrollRef?.el || this.root.el;
}
```

### 自动滚动

```javascript
// 新消息时自动滚动到底部
onNewMessage() {
    if (this.isNearBottom()) {
        this.scrollToBottom();
    }
}

isNearBottom() {
    const container = this.scrollContainer;
    if (!container) return false;
    
    const threshold = 100; // 100px 阈值
    return container.scrollTop + container.clientHeight >= 
           container.scrollHeight - threshold;
}

scrollToBottom() {
    const container = this.scrollContainer;
    if (container) {
        container.scrollTop = container.scrollHeight;
    }
}
```

## 消息加载

### 分页加载

```javascript
// 加载更早的消息
async loadOlderMessages() {
    if (this.props.thread.loadOlder) {
        return; // 已在加载中
    }
    
    this.props.thread.loadOlder = true;
    try {
        await this.props.thread.fetchOlderMessages();
    } finally {
        this.props.thread.loadOlder = false;
    }
}

// 加载更新的消息
async loadNewerMessages() {
    if (this.props.thread.loadNewer) {
        return; // 已在加载中
    }
    
    this.props.thread.loadNewer = true;
    try {
        await this.props.thread.fetchNewerMessages();
    } finally {
        this.props.thread.loadNewer = false;
    }
}
```

## 使用场景

### 1. 聊天窗口中的线程

```xml
<Thread 
    thread="chatThread"
    isInChatWindow="true"
    order="asc"
    showDates="false"
    messageActions="true"
/>
```

### 2. 邮件列表显示

```xml
<Thread 
    thread="mailThread"
    order="desc"
    showDates="true"
    showEmptyMessage="true"
    scrollRef="scrollContainer"
/>
```

### 3. 搜索结果显示

```xml
<Thread 
    thread="searchThread"
    searchTerm="searchQuery"
    messageHighlight="highlightState"
    showJumpPresent="false"
/>
```

## 性能优化

### 1. 虚拟滚动

对于大量消息，可以实现虚拟滚动：
```javascript
// 只渲染可见区域的消息
get visibleMessages() {
    const container = this.scrollContainer;
    if (!container) return this.props.thread.messages;
    
    // 计算可见范围
    const startIndex = Math.floor(container.scrollTop / MESSAGE_HEIGHT);
    const endIndex = startIndex + Math.ceil(container.clientHeight / MESSAGE_HEIGHT);
    
    return this.props.thread.messages.slice(startIndex, endIndex + BUFFER_SIZE);
}
```

### 2. 消息引用优化

```javascript
// 使用 WeakMap 避免内存泄漏
this.messageRefs = new WeakMap();
```

### 3. 防抖滚动

```javascript
// 防抖滚动事件处理
this.debouncedScrollHandler = debounce(this.onScroll.bind(this), 100);
```

## 设计模式

### 1. 观察者模式 (Observer Pattern)
- 监听消息变化自动更新显示
- 响应滚动事件和可见性变化

### 2. 虚拟代理模式 (Virtual Proxy Pattern)
- 延迟加载消息内容
- 按需渲染可见消息

### 3. 命令模式 (Command Pattern)
- 滚动操作的命令化处理
- 支持撤销和重做

## 注意事项

1. **性能考虑**: 大量消息时需要虚拟化处理
2. **内存管理**: 及时清理消息引用避免内存泄漏
3. **滚动体验**: 提供平滑的滚动动画
4. **可访问性**: 支持键盘导航和屏幕阅读器

## 扩展建议

1. **虚拟滚动**: 实现高性能的虚拟滚动
2. **消息搜索**: 集成消息内容搜索功能
3. **消息过滤**: 支持按类型过滤消息
4. **离线支持**: 实现离线消息缓存
5. **性能监控**: 添加渲染性能监控

该组件是邮件系统用户界面的核心，提供了完整的消息浏览和交互体验。

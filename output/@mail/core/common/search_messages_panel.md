# Search Messages Panel - 搜索消息面板

## 概述

`search_messages_panel.js` 实现了 Odoo 邮件系统中的消息搜索面板组件，提供了完整的消息搜索界面。该组件集成了搜索输入、结果显示、分页加载等功能，为用户提供强大的消息查找能力，是邮件系统搜索功能的主要用户界面。

## 文件信息
- **路径**: `/mail/static/src/core/common/search_messages_panel.js`
- **行数**: 99
- **模块**: `@mail/core/common/search_messages_panel`

## 依赖关系

```javascript
// OWL 框架依赖
'@odoo/owl'                                    // OWL 核心组件

// Web 核心依赖
'@web/core/utils/hooks'                        // Web 核心钩子
'@web/core/browser/browser'                    // 浏览器工具
'@web/core/l10n/translation'                   // 国际化

// 邮件系统依赖
'@mail/core/common/message_search_hook'        // 消息搜索钩子
'@mail/discuss/core/common/action_panel'       // 操作面板
'@mail/core/common/message_card_list'          // 消息卡片列表
```

## 组件定义

### SearchMessagesPanel 类

```javascript
class SearchMessagesPanel extends Component {
    static components = {
        MessageCardList,    // 消息卡片列表组件
        ActionPanel,        // 操作面板组件
    };
    static props = ["thread", "className?", "closeSearch?", "onClickJump?"];
    static template = "mail.SearchMessagesPanel";
}
```

## Props 配置

### Props 定义

```typescript
interface Props {
    thread: import("@mail/core/common/thread_model").Thread;  // 搜索的线程
    className?: string;                                       // CSS类名（可选）
    closeSearch?: function;                                   // 关闭搜索回调（可选）
    onClickJump?: function;                                   // 跳转点击回调（可选）
}
```

### Props 详细说明

- **`thread`** (必需):
  - 类型: Thread 模型
  - 用途: 要搜索的线程对象
  - 功能: 确定搜索范围

- **`className`** (可选):
  - 类型: 字符串
  - 用途: 添加自定义CSS类名
  - 示例: `"search-panel-custom"`

- **`closeSearch`** (可选):
  - 类型: 函数
  - 用途: 关闭搜索面板的回调
  - 触发: ESC键或清空操作

- **`onClickJump`** (可选):
  - 类型: 函数
  - 用途: 点击跳转到消息的回调
  - 参数: 消息对象

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    
    // 状态管理
    this.state = useState({ 
        searchTerm: "",      // 当前输入的搜索词
        searchedTerm: ""     // 已执行搜索的词
    });
    
    // 搜索功能
    this.messageSearch = useMessageSearch(this.props.thread);
    
    // 自动焦点
    useAutofocus();
    
    // ESC键监听
    useExternalListener(
        browser,
        "keydown",
        (ev) => {
            if (ev.key === "Escape") {
                this.props.closeSearch?.();
            }
        },
        { capture: true }
    );
    
    // Props更新监听
    onWillUpdateProps((nextProps) => {
        if (this.props.thread.notEq(nextProps.thread)) {
            this.env.searchMenu?.close();
        }
    });
}
```

**初始化内容**:
- 搜索状态管理
- 消息搜索钩子集成
- 自动焦点设置
- 键盘事件监听
- Props变化监听

## 核心功能

### 1. 标题显示

```javascript
get title() {
    return _t("Search messages");
}
```

**功能**:
- 提供面板标题
- 支持国际化
- 用于面板头部显示

### 2. 搜索结果统计

```javascript
get MESSAGES_FOUND() {
    if (this.messageSearch.messages.length === 0) {
        return false;
    }
    return _t("%s messages found", this.messageSearch.count);
}
```

**统计逻辑**:
- **无结果**: 返回 `false`，不显示统计
- **有结果**: 显示找到的消息数量
- **国际化**: 支持多语言格式化

### 3. 执行搜索

```javascript
search() {
    this.messageSearch.searchTerm = this.state.searchTerm;
    this.messageSearch.search();
    this.state.searchedTerm = this.state.searchTerm;
}
```

**搜索流程**:
1. 将输入的搜索词传递给搜索钩子
2. 执行搜索操作
3. 记录已搜索的词（用于状态跟踪）

### 4. 清空搜索

```javascript
clear() {
    this.state.searchTerm = "";                    // 清空输入
    this.state.searchedTerm = this.state.searchTerm;  // 同步已搜索词
    this.messageSearch.clear();                    // 清空搜索结果
    this.props.closeSearch?.();                    // 关闭搜索面板
}
```

**清空操作**:
- 重置搜索输入
- 清空搜索结果
- 可选择性关闭面板

### 5. 键盘事件处理

```javascript
onKeydownSearch(ev) {
    if (ev.key !== "Enter") {
        return;
    }
    if (!this.state.searchTerm) {
        this.clear();      // 空搜索词时清空
    } else {
        this.search();     // 有搜索词时执行搜索
    }
}
```

**Enter键行为**:
- **有搜索词**: 执行搜索
- **无搜索词**: 清空搜索
- **其他键**: 忽略处理

### 6. 分页加载

```javascript
onLoadMoreVisible() {
    const before = this.messageSearch.messages
        ? Math.min(...this.messageSearch.messages.map((message) => message.id))
        : false;
    this.messageSearch.search(before);
}
```

**分页逻辑**:
1. 找到当前结果中最小的消息ID
2. 以该ID为基准加载更早的消息
3. 实现向前分页功能

## 状态管理

### 搜索状态

```javascript
this.state = useState({ 
    searchTerm: "",      // 用户输入的搜索词
    searchedTerm: ""     // 实际执行搜索的词
});
```

**状态区分**:
- **`searchTerm`**: 实时输入状态
- **`searchedTerm`**: 已执行搜索状态
- **用途**: 区分输入和搜索状态

### 搜索钩子状态

```javascript
this.messageSearch = useMessageSearch(this.props.thread);
```

**钩子提供的状态**:
- `messages`: 搜索结果消息列表
- `count`: 搜索结果总数
- `searching`: 是否正在搜索
- `searched`: 是否已执行搜索

## 用户交互

### 1. 搜索输入

```javascript
// 用户输入搜索词
<input 
    value={this.state.searchTerm}
    onInput={(ev) => this.state.searchTerm = ev.target.value}
    onKeydown={(ev) => this.onKeydownSearch(ev)}
    placeholder="搜索消息..."
/>
```

### 2. 搜索按钮

```javascript
// 搜索按钮
<button onClick={() => this.search()}>
    搜索
</button>

// 清空按钮
<button onClick={() => this.clear()}>
    清空
</button>
```

### 3. 结果显示

```javascript
// 搜索结果列表
<MessageCardList 
    messages={this.messageSearch.messages}
    messageSearch={this.messageSearch}
    onClickJump={this.props.onClickJump}
/>
```

### 4. 加载更多

```javascript
// 分页加载触发
<div 
    className="load-more-trigger"
    onVisible={() => this.onLoadMoreVisible()}
>
    加载更多...
</div>
```

## 键盘快捷键

### 支持的快捷键

- **Enter**: 执行搜索或清空
- **Escape**: 关闭搜索面板

### 快捷键实现

```javascript
// ESC键关闭
useExternalListener(
    browser,
    "keydown",
    (ev) => {
        if (ev.key === "Escape") {
            this.props.closeSearch?.();
        }
    },
    { capture: true }
);

// Enter键搜索
onKeydownSearch(ev) {
    if (ev.key === "Enter") {
        // 搜索逻辑
    }
}
```

## 生命周期管理

### Props更新处理

```javascript
onWillUpdateProps((nextProps) => {
    if (this.props.thread.notEq(nextProps.thread)) {
        this.env.searchMenu?.close();
    }
});
```

**更新逻辑**:
- 监听线程变化
- 线程切换时关闭搜索菜单
- 确保搜索状态的一致性

## 使用场景

### 1. 线程消息搜索

```javascript
// 在线程组件中使用搜索面板
<SearchMessagesPanel 
    thread={currentThread}
    closeSearch={() => this.closeSearchPanel()}
    onClickJump={(message) => this.jumpToMessage(message)}
/>
```

### 2. 聊天窗口搜索

```javascript
// 在聊天窗口中集成搜索
<SearchMessagesPanel 
    thread={chatWindow.thread}
    className="chat-search-panel"
    closeSearch={() => chatWindow.closeSearch()}
/>
```

### 3. 全局消息搜索

```javascript
// 全局搜索界面
<SearchMessagesPanel 
    thread={selectedThread}
    onClickJump={(message) => this.openMessageThread(message)}
/>
```

## 性能优化

### 1. 搜索防抖

```javascript
// 建议在输入时添加防抖
const debouncedSearch = debounce(() => {
    this.search();
}, 300);

// 输入时触发防抖搜索
onInput(ev) {
    this.state.searchTerm = ev.target.value;
    debouncedSearch();
}
```

### 2. 结果缓存

```javascript
// 搜索结果缓存（由搜索钩子处理）
// 避免重复搜索相同关键词
```

### 3. 虚拟滚动

```javascript
// 大量结果时使用虚拟滚动
// 提高渲染性能
```

## 设计模式

### 1. 组合模式 (Composition Pattern)
- 组合搜索钩子和UI组件
- 分离搜索逻辑和界面逻辑

### 2. 观察者模式 (Observer Pattern)
- 监听搜索状态变化
- 响应用户输入和操作

### 3. 命令模式 (Command Pattern)
- 搜索和清空操作的封装
- 支持键盘快捷键

## 可访问性

### 键盘导航

- 支持Tab键在元素间导航
- 提供键盘快捷键操作
- 自动焦点管理

### 屏幕阅读器

- 提供适当的ARIA标签
- 搜索结果的语音播报
- 状态变化的通知

## 注意事项

1. **性能考虑**: 大量搜索结果的渲染性能
2. **用户体验**: 提供即时的搜索反馈
3. **状态管理**: 正确处理搜索状态的同步
4. **错误处理**: 处理搜索失败的情况

## 扩展建议

1. **高级搜索**: 支持过滤器和高级搜索选项
2. **搜索历史**: 保存用户搜索历史
3. **搜索建议**: 提供搜索词自动补全
4. **结果排序**: 支持按相关性、时间等排序
5. **搜索统计**: 提供搜索使用统计

该组件为用户提供了完整的消息搜索体验，集成了强大的搜索功能和直观的用户界面，是邮件系统中重要的信息检索工具。

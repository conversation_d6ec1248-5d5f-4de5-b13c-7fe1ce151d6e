# Persona Model - 用户角色模型

## 概述

`persona_model.js` 定义了 Odoo 邮件系统中的用户角色模型类，用于表示系统中的用户身份。Persona 可以是合作伙伴（partner）或访客（guest），包含用户的基本信息、状态、权限等属性，是整个邮件系统中用户身份管理的核心模型。

## 文件信息
- **路径**: `/mail/static/src/core/common/persona_model.js`
- **行数**: 130
- **模块**: `@mail/core/common/persona_model`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/record'    // Record 基类
'@web/core/utils/urls'        // URL 工具
'@web/core/network/rpc'       // RPC 网络请求
```

## 类型定义

### ImStatus 类型

```typescript
type ImStatus = 'offline' | 'bot' | 'online' | 'away' | 'im_partner' | undefined;
```

**状态说明**:
- **`offline`**: 离线状态
- **`bot`**: 机器人状态
- **`online`**: 在线状态
- **`away`**: 离开状态
- **`im_partner`**: 即时消息合作伙伴
- **`undefined`**: 未定义状态

### Data 接口

```typescript
interface Data {
    id: number;                    // 用户ID
    name: string;                  // 用户名称
    email: string;                 // 邮箱地址
    type: 'partner' | 'guest';     // 用户类型
    im_status: ImStatus;           // 即时消息状态
}
```

## 类定义

### Persona 类

```javascript
class Persona extends Record {
    static id = AND("type", "id");  // 复合主键：类型 + ID
    static records = {};            // 静态记录集合
}
```

### 复合主键

Persona 使用复合主键 `AND("type", "id")`，这意味着：
- 同一个 ID 在不同类型中可以共存
- 例如：`partner,123` 和 `guest,123` 是不同的用户角色

## 核心属性

### 基础属性

```javascript
// 基本标识
id: number;                      // 用户ID
type: 'partner' | 'guest';       // 用户类型
name: string;                    // 用户名称
email: string;                   // 邮箱地址
userId: number;                  // 关联的用户ID

// 联系信息
landlineNumber: string;          // 固定电话
mobileNumber: string;            // 手机号码
country = Record.one("Country"); // 国家信息

// 状态和权限
im_status: ImStatus;             // 即时消息状态
notification_preference: 'email' | 'inbox';  // 通知偏好
isAdmin: boolean = false;        // 是否为管理员
isInternalUser: boolean = false; // 是否为内部用户
is_company: boolean;             // 是否为公司

// 时间戳
write_date = Record.attr(undefined, { type: "datetime" });  // 修改时间
```

### 关系字段

#### 频道成员关系
```javascript
channelMembers = Record.many("ChannelMember");
```

#### 状态跟踪关系
```javascript
storeAsTrackedImStatus = Record.one("Store", {
    compute() {
        if (this.type === "guest" ||
            (this.type === "partner" && this.im_status !== "im_partner" && !this.is_public)) {
            return this.store;
        }
    },
    onAdd() {
        if (!this.store.env.services.bus_service.isActive) {
            return;
        }
        const model = this.type === "partner" ? "res.partner" : "mail.guest";
        this.store.env.services.bus_service.addChannel(`odoo-presence-${model}_${this.id}`);
    },
    onDelete() {
        if (!this.store.env.services.bus_service.isActive) {
            return;
        }
        const model = this.type === "partner" ? "res.partner" : "mail.guest";
        this.store.env.services.bus_service.deleteChannel(`odoo-presence-${model}_${this.id}`);
    },
    eager: true,
    inverse: "imStatusTrackedPersonas",
});
```

**功能说明**:
- 自动管理即时消息状态跟踪
- 添加/删除总线频道订阅
- 支持访客和合作伙伴的状态跟踪

## 计算属性

### 1. 电话号码检查

```javascript
get hasPhoneNumber() {
    return Boolean(this.mobileNumber || this.landlineNumber);
}
```

**功能**:
- 检查用户是否有电话号码
- 包括手机号码和固定电话
- 返回布尔值

### 2. 邮箱用户名

```javascript
get emailWithoutDomain() {
    return this.email.substring(0, this.email.lastIndexOf("@"));
}
```

**功能**:
- 提取邮箱地址中的用户名部分
- 去除域名部分
- 用于显示简化的用户标识

### 3. 头像URL

```javascript
get avatarUrl() {
    if (this.type === "partner") {
        return imageUrl("res.partner", this.id, "avatar_128", { 
            unique: this.write_date 
        });
    }
    if (this.type === "guest") {
        return imageUrl("mail.guest", this.id, "avatar_128", { 
            unique: this.write_date 
        });
    }
    if (this.userId) {
        return imageUrl("res.users", this.userId, "avatar_128", { 
            unique: this.write_date 
        });
    }
    return this.store.DEFAULT_AVATAR;
}
```

**头像获取逻辑**:
1. **合作伙伴**: 使用 `res.partner` 模型的头像
2. **访客**: 使用 `mail.guest` 模型的头像
3. **有用户ID**: 使用 `res.users` 模型的头像
4. **默认**: 使用系统默认头像

**缓存机制**:
- 使用 `write_date` 作为唯一标识
- 确保头像更新时能及时刷新
- 避免浏览器缓存问题

## 核心方法

### 1. 搜索聊天

```javascript
searchChat() {
    return Object.values(this.store.Thread.records).find(
        (thread) => thread.channel_type === "chat" && 
                   thread.correspondent?.persona.eq(this)
    );
}
```

**功能**:
- 查找与当前用户的私聊线程
- 遍历所有线程记录
- 匹配聊天类型和对话者
- 返回找到的线程或 undefined

### 2. 更新访客名称

```javascript
async updateGuestName(name) {
    await rpc("/mail/guest/update_name", {
        guest_id: this.id,
        name,
    });
}
```

**功能**:
- 更新访客用户的名称
- 调用服务器端API
- 仅适用于访客类型用户

## 用户类型

### 合作伙伴 (Partner)

```javascript
// 合作伙伴用户
const partner = store.Persona.insert({
    type: "partner",
    id: 123,
    name: "张三",
    email: "<EMAIL>",
    isInternalUser: true
});
```

**特点**:
- 系统内部用户或外部合作伙伴
- 有完整的用户权限和设置
- 可以是内部用户或外部用户

### 访客 (Guest)

```javascript
// 访客用户
const guest = store.Persona.insert({
    type: "guest",
    id: 456,
    name: "访客用户",
    email: "<EMAIL>"
});
```

**特点**:
- 临时用户或匿名用户
- 权限受限
- 通常用于在线客服等场景

## 状态跟踪

### 总线订阅管理

```javascript
// 自动订阅状态更新频道
const channelName = `odoo-presence-${model}_${this.id}`;
this.store.env.services.bus_service.addChannel(channelName);
```

**订阅逻辑**:
- 根据用户类型确定模型名称
- 构建唯一的频道名称
- 自动订阅/取消订阅状态更新

### 状态更新

状态跟踪支持实时更新用户的：
- 在线/离线状态
- 活跃时间
- 打字状态
- 其他即时消息状态

## 使用场景

### 1. 获取当前用户

```javascript
const currentUser = store.self;
if (currentUser) {
    console.log("当前用户:", currentUser.name);
    console.log("用户类型:", currentUser.type);
}
```

### 2. 查找用户聊天

```javascript
const chatThread = persona.searchChat();
if (chatThread) {
    // 打开现有聊天
    openChat(chatThread);
} else {
    // 创建新聊天
    createNewChat(persona);
}
```

### 3. 显示用户头像

```javascript
// 在模板中使用
<img t-att-src="persona.avatarUrl" t-att-alt="persona.name"/>
```

### 4. 检查用户权限

```javascript
if (persona.isAdmin) {
    // 显示管理员功能
    showAdminFeatures();
}

if (persona.isInternalUser) {
    // 显示内部用户功能
    showInternalFeatures();
}
```

## 通知偏好

### 通知类型

- **`email`**: 邮件通知
- **`inbox`**: 收件箱通知

### 使用示例

```javascript
if (persona.notification_preference === "email") {
    // 发送邮件通知
    sendEmailNotification(persona);
} else {
    // 发送收件箱通知
    sendInboxNotification(persona);
}
```

## 性能优化

### 1. 头像缓存

```javascript
// 使用 write_date 确保缓存更新
{ unique: this.write_date }
```

### 2. 状态跟踪优化

```javascript
// 只跟踪需要的用户状态
if (this.type === "guest" || 
    (this.type === "partner" && this.im_status !== "im_partner")) {
    // 添加状态跟踪
}
```

### 3. 懒加载

```javascript
// 使用 eager: true 进行预加载
eager: true
```

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- 根据用户类型采用不同的头像获取策略
- 不同类型用户的权限策略

### 2. 观察者模式 (Observer Pattern)
- 状态跟踪的自动订阅/取消订阅
- 响应用户状态变化

### 3. 工厂模式 (Factory Pattern)
- 通过 `insert` 方法创建不同类型的用户
- 统一的用户创建接口

## 注意事项

1. **复合主键**: 注意类型和ID的组合唯一性
2. **状态同步**: 确保即时消息状态的实时性
3. **权限控制**: 正确区分不同类型用户的权限
4. **内存管理**: 及时清理不再使用的用户对象

## 扩展建议

1. **用户分组**: 支持用户分组和角色管理
2. **自定义字段**: 支持自定义用户属性
3. **状态历史**: 记录用户状态变化历史
4. **批量操作**: 支持批量用户操作
5. **缓存优化**: 优化用户数据的缓存策略

该模型是邮件系统中用户身份管理的核心，为所有用户相关功能提供了统一的数据结构和业务逻辑支持。

# Message - 消息组件

## 概述

`message.js` 是 Odoo 邮件系统中的核心消息显示组件，负责渲染单个消息的完整界面。该组件支持消息显示、编辑、回复、反应、附件、链接预览等丰富功能，是邮件系统中最复杂和最重要的UI组件之一。

## 文件信息
- **路径**: `/mail/static/src/core/common/message.js`
- **行数**: 507
- **模块**: `@mail/core/common/message`

## 依赖关系

```javascript
// 核心组件依赖
'@mail/core/common/attachment_list'           // 附件列表
'@mail/core/common/composer'                  // 消息编辑器
'@mail/core/common/im_status'                 // 即时消息状态
'@mail/core/common/link_preview_list'         // 链接预览列表
'@mail/core/common/message_in_reply'          // 回复消息显示
'@mail/core/common/message_reactions'         // 消息反应
'@mail/core/common/message_seen_indicator'    // 已读指示器
'@mail/core/common/relative_time'             // 相对时间

// 功能模块依赖
'@mail/core/common/message_actions'           // 消息操作
'@mail/core/common/message_notification_popover' // 通知弹出框
'@mail/core/common/message_reaction_menu'     // 反应菜单
'@web/core/action_swiper/action_swiper'       // 滑动操作
'@web/core/dropdown/dropdown'                 // 下拉菜单
'@web/core/popover/popover_hook'              // 弹出框钩子
```

## 组件定义

### Message 类

```javascript
class Message extends Component {
    static SHADOW_LINK_COLOR = "#66598f";           // 阴影链接颜色
    static SHADOW_HIGHLIGHT_COLOR = "#e99d00bf";     // 阴影高亮颜色
    static SHADOW_LINK_HOVER_COLOR = "#564b79";      // 阴影链接悬停颜色
    
    static components = {
        ActionSwiper,           // 滑动操作组件
        AttachmentList,         // 附件列表组件
        Composer,              // 编辑器组件
        Dropdown,              // 下拉菜单组件
        LinkPreviewList,       // 链接预览组件
        MessageInReply,        // 回复消息组件
        MessageReactions,      // 消息反应组件
        MessageSeenIndicator,  // 已读指示器组件
        ImStatus,              // 状态指示器组件
        RelativeTime,          // 相对时间组件
    };
    
    static template = "mail.Message";
}
```

## Props 配置

### Props 接口

```typescript
interface Props {
    message: import("models").Message;                    // 消息对象（必需）
    hasActions?: boolean;                                // 是否显示操作按钮
    highlighted?: boolean;                               // 是否高亮显示
    onParentMessageClick?: function;                     // 父消息点击回调
    messageToReplyTo?: MessageToReplyTo;                 // 回复目标消息
    squashed?: boolean;                                  // 是否压缩显示
    thread?: import("models").Thread;                    // 所属线程
    messageSearch?: ReturnType<useMessageSearch>;        // 消息搜索状态
    className?: string;                                  // 自定义CSS类名
    asCard?: boolean;                                    // 是否以卡片形式显示
    isInChatWindow?: boolean;                           // 是否在聊天窗口中
    showDates?: boolean;                                // 是否显示日期
    isFirstMessage?: boolean;                           // 是否为第一条消息
}
```

### 默认属性

```javascript
static defaultProps = {
    hasActions: true,        // 默认显示操作按钮
    isInChatWindow: false,   // 默认不在聊天窗口中
    showDates: true,         // 默认显示日期
};
```

## 核心功能

### 1. 组件初始化

```javascript
setup() {
    super.setup();
    
    // 基础服务和状态
    this.store = useState(useService("mail.store"));
    this.ui = useState(useService("ui"));
    this.dialog = useService("dialog");
    
    // 组件状态
    this.state = useState({
        isEditing: false,           // 是否正在编辑
        isHovered: false,           // 是否悬停
        isClicked: false,           // 是否被点击
        expandOptions: false,       // 是否展开选项
        emailHeaderOpen: false,     // 邮件头是否打开
        showTranslation: false,     // 是否显示翻译
        actionMenuMobileOpen: false, // 移动端操作菜单是否打开
    });
    
    // DOM 引用
    this.root = useRef("root");
    this.messageBody = useRef("body");
    this.shadowBody = useRef("shadowBody");
    
    // 功能模块
    this.messageActions = useMessageActions();
    this.popover = usePopover(MessageNotificationPopover, { position: "top" });
    this.optionsDropdown = useDropdownState();
}
```

### 2. Shadow DOM 处理

```javascript
onMounted(() => {
    if (this.shadowBody.el) {
        this.shadowRoot = this.shadowBody.el.attachShadow({ mode: "open" });
        const color = cookie.get("color_scheme") === "dark" ? "white" : "black";
        const shadowStyle = document.createElement("style");
        shadowStyle.innerHTML = `
            * {
                background-color: transparent !important;
                color: ${color} !important;
            }
            a, a * {
                color: ${this.constructor.SHADOW_LINK_COLOR} !important;
            }
            a:hover, a *:hover {
                color: ${this.constructor.SHADOW_LINK_HOVER_COLOR} !important;
            }
            .o-mail-Message-searchHighlight {
                background: ${this.constructor.SHADOW_HIGHLIGHT_COLOR} !important;
            }
        `;
        if (cookie.get("color_scheme") === "dark") {
            this.shadowRoot.appendChild(shadowStyle);
        }
    }
});
```

### 3. 消息体处理

```javascript
useEffect(() => {
    if (this.messageBody.el) {
        this.prepareMessageBody(this.messageBody.el);
    }
    if (this.shadowBody.el) {
        const bodyEl = document.createElement("span");
        bodyEl.innerHTML = this.state.showTranslation
            ? this.message.translationValue
            : this.props.messageSearch?.highlight(this.message.body) ??
              this.message.body;
        this.prepareMessageBody(bodyEl);
        this.shadowRoot.appendChild(bodyEl);
        return () => {
            this.shadowRoot.removeChild(bodyEl);
        };
    }
}, () => [
    this.state.showTranslation,
    this.message.translationValue,
    this.props.messageSearch?.searchTerm,
    this.message.body,
]);
```

## 计算属性

### 1. 样式类计算

```javascript
get attClass() {
    return {
        [this.props.className]: true,
        "o-card p-2 mt-2": this.props.asCard,
        "pt-1": !this.props.asCard,
        "o-selfAuthored": this.message.isSelfAuthored && !this.env.messageCard,
        "o-selected": this.props.messageToReplyTo?.isSelected(
            this.props.thread,
            this.props.message
        ),
        "o-squashed": this.props.squashed,
        "mt-1": !this.props.squashed && this.props.thread && 
                !this.env.messageCard && !this.props.asCard,
        "px-2": this.props.isInChatWindow,
        "opacity-50": this.props.messageToReplyTo?.isNotSelected(
            this.props.thread,
            this.props.message
        ),
        "o-actionMenuMobileOpen": this.state.actionMenuMobileOpen,
        "o-editing": this.state.isEditing,
    };
}
```

### 2. 作者信息

```javascript
get authorName() {
    if (this.message.author) {
        return this.message.author.name;
    }
    return this.message.email_from;
}

get authorAvatarUrl() {
    if (this.message.message_type?.includes("email") &&
        !["partner", "guest"].includes(this.message.author?.type)) {
        return url("/mail/static/src/img/email_icon.png");
    }
    
    if (this.message.author) {
        return this.message.author.avatarUrl;
    }
    
    return this.store.DEFAULT_AVATAR;
}

get authorAvatarAttClass() {
    return {
        o_object_fit_contain: this.props.message.author?.is_company,
        o_object_fit_cover: !this.props.message.author?.is_company,
    };
}
```

### 3. 消息类型和状态

```javascript
get messageTypeText() {
    if (this.props.message.message_type === "notification") {
        return _t("System notification");
    }
    if (this.props.message.message_type === "auto_comment") {
        return _t("Automated message");
    }
    if (!this.props.message.is_discussion &&
        this.props.message.message_type !== "user_notification") {
        return _t("Note");
    }
    return _t("Message");
}

get showSeenIndicator() {
    return this.props.message.isSelfAuthored && this.props.thread?.hasSeenFeature;
}

get showSubtypeDescription() {
    return (
        this.message.subtype_description &&
        this.message.subtype_description.toLowerCase() !==
            htmlToTextContentInline(this.message.body || "").toLowerCase()
    );
}
```

### 4. 快速操作配置

```javascript
get quickActionCount() {
    return this.env.inChatter ? 3 : this.env.inChatWindow ? 2 : 4;
}
```

## 消息编辑功能

### 编辑状态管理

```javascript
useEffect((editingMessage) => {
    if (this.props.message.eq(editingMessage)) {
        messageActionsRegistry.get("edit").onClick(this);
    }
}, () => [this.props.messageEdition?.editingMessage]);
```

### 编辑模式切换

消息组件支持内联编辑功能，当进入编辑模式时：
1. 显示编辑器组件替代消息内容
2. 提供保存和取消操作
3. 支持键盘快捷键操作

## 消息反应功能

### 反应菜单

```javascript
openReactionMenu() {
    // 打开反应选择菜单
    // 允许用户为消息添加表情反应
}
```

### 反应显示

组件集成了 `MessageReactions` 子组件来显示：
- 已有的反应统计
- 用户自己的反应状态
- 反应的交互功能

## 搜索高亮功能

### 搜索词高亮

```javascript
// 在消息体中高亮搜索词
this.props.messageSearch?.highlight(this.message.body) ?? this.message.body
```

### 高亮样式

```css
.o-mail-Message-searchHighlight {
    background: #e99d00bf !important;
}
```

## 移动端适配

### 触摸检测

```javascript
this.hasTouch = hasTouch;
```

### 滑动操作

组件集成了 `ActionSwiper` 来支持移动端的滑动操作：
- 左滑显示快速操作
- 右滑进行回复操作

### 移动端操作菜单

```javascript
// 移动端专用的操作菜单
MessageActionMenuMobile
```

## 使用场景

### 1. 聊天消息显示

```xml
<Message 
    message="message"
    thread="thread"
    hasActions="true"
    isInChatWindow="true"
    messageToReplyTo="replyState"
/>
```

### 2. 邮件列表显示

```xml
<Message 
    message="mail"
    asCard="true"
    showDates="true"
    onParentMessageClick="() => this.openMail()"
/>
```

### 3. 搜索结果显示

```xml
<Message 
    message="searchResult"
    highlighted="true"
    messageSearch="searchState"
    className="search-result"
/>
```

## 性能优化

### 1. 组件注册管理

```javascript
onWillUpdateProps((nextProps) => {
    this.props.registerMessageRef?.(this.props.message, null);
});
onMounted(() => this.props.registerMessageRef?.(this.props.message, this.root));
onPatched(() => this.props.registerMessageRef?.(this.props.message, this.root));
onWillDestroy(() => this.props.registerMessageRef?.(this.props.message, null));
```

### 2. Shadow DOM 优化

使用 Shadow DOM 来隔离消息内容的样式，避免样式冲突和提高渲染性能。

### 3. 条件渲染

根据不同的显示模式和状态，有选择地渲染组件部分，减少不必要的DOM操作。

## 设计模式

### 1. 组合模式 (Composition Pattern)
- 组合多个子组件构建完整的消息界面
- 每个子组件负责特定的功能模块

### 2. 状态模式 (State Pattern)
- 根据不同的消息状态显示不同的界面
- 编辑模式、选中模式、悬停模式等

### 3. 观察者模式 (Observer Pattern)
- 监听消息数据的变化自动更新界面
- 响应用户交互事件

## 注意事项

1. **性能考虑**: 大量消息时需要虚拟化处理
2. **样式隔离**: 使用 Shadow DOM 避免样式冲突
3. **移动端适配**: 提供触摸友好的交互体验
4. **可访问性**: 支持键盘导航和屏幕阅读器

该组件是邮件系统用户界面的核心，提供了完整的消息显示和交互功能。

# Country Model - 国家模型

## 概述

`country_model.js` 定义了 Odoo 邮件系统中的国家模型类，用于管理国家信息和国旗显示。该模型提供了国家代码、名称和国旗URL的管理，主要用于用户资料、联系人信息和国际化显示等场景。

## 文件信息
- **路径**: `/mail/static/src/core/common/country_model.js`
- **行数**: 37
- **模块**: `@mail/core/common/country_model`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/record'    // Record 基类
```

## 类定义

### Country 类

```javascript
class Country extends Record {
    static id = "id";           // 主键字段
    static records = {};        // 静态记录集合
}
```

## 核心属性

### 基础属性

```javascript
id: number;      // 国家ID
code: string;    // 国家代码（ISO标准）
name: string;    // 国家名称
```

### 属性详细说明

#### 1. id (国家ID)
- **类型**: `number`
- **用途**: 唯一标识国家
- **示例**: `1`, `2`, `3`

#### 2. code (国家代码)
- **类型**: `string`
- **用途**: ISO国家代码，用于标识和国旗显示
- **格式**: 通常为2位字母代码
- **示例**: `"US"`, `"CN"`, `"FR"`, `"DE"`

#### 3. name (国家名称)
- **类型**: `string`
- **用途**: 国家的显示名称
- **示例**: `"United States"`, `"China"`, `"France"`

## 计算属性

### flagUrl - 国旗URL

```javascript
get flagUrl() {
    return `/base/static/img/country_flags/${encodeURIComponent(this.code.toLowerCase())}.png`;
}
```

**URL构建逻辑**:
1. **基础路径**: `/base/static/img/country_flags/`
2. **文件名**: 国家代码转小写
3. **编码处理**: 使用 `encodeURIComponent` 确保URL安全
4. **文件扩展名**: `.png`

**示例**:
```javascript
// 美国
country.code = "US"
country.flagUrl // "/base/static/img/country_flags/us.png"

// 中国
country.code = "CN"
country.flagUrl // "/base/static/img/country_flags/cn.png"
```

## 使用场景

### 1. 用户资料显示

```javascript
// 显示用户的国家信息
const user = store.User.get(userId);
const country = user.country;

if (country) {
    console.log(`用户来自: ${country.name}`);
    displayFlag(country.flagUrl);
}
```

### 2. 联系人管理

```javascript
// 联系人的国家信息
const contact = store.Contact.get(contactId);
const country = contact.country;

// 在联系人卡片中显示国旗
<img src={country.flagUrl} alt={country.name} className="country-flag" />
```

### 3. 国家选择器

```javascript
// 国家选择下拉菜单
const countries = Object.values(store.Country.records);
const countryOptions = countries.map(country => ({
    value: country.id,
    label: country.name,
    flag: country.flagUrl
}));

// 渲染选择器
<select>
    {countryOptions.map(option => (
        <option key={option.value} value={option.value}>
            {option.label}
        </option>
    ))}
</select>
```

### 4. 地理统计

```javascript
// 按国家统计用户分布
const getUsersByCountry = () => {
    const distribution = {};
    
    Object.values(store.User.records).forEach(user => {
        if (user.country) {
            const countryName = user.country.name;
            distribution[countryName] = (distribution[countryName] || 0) + 1;
        }
    });
    
    return distribution;
};
```

## 数据管理

### 1. 创建国家记录

```javascript
// 创建新的国家记录
const country = store.Country.insert({
    id: 1,
    code: "US",
    name: "United States"
});

console.log(country.flagUrl); // "/base/static/img/country_flags/us.png"
```

### 2. 批量导入国家

```javascript
// 批量导入国家数据
const countriesData = [
    { id: 1, code: "US", name: "United States" },
    { id: 2, code: "CN", name: "China" },
    { id: 3, code: "FR", name: "France" },
    { id: 4, code: "DE", name: "Germany" }
];

const countries = store.Country.insert(countriesData);
```

### 3. 查找国家

```javascript
// 按代码查找国家
const findCountryByCode = (code) => {
    return Object.values(store.Country.records)
        .find(country => country.code.toLowerCase() === code.toLowerCase());
};

// 按名称查找国家
const findCountryByName = (name) => {
    return Object.values(store.Country.records)
        .find(country => country.name.toLowerCase().includes(name.toLowerCase()));
};
```

## 国旗显示

### 1. 基础国旗组件

```javascript
// 国旗显示组件
const CountryFlag = ({ country, size = "small" }) => {
    const sizeClass = `flag-${size}`;
    
    return (
        <img 
            src={country.flagUrl}
            alt={`${country.name} flag`}
            className={`country-flag ${sizeClass}`}
            title={country.name}
        />
    );
};
```

### 2. 国旗加载错误处理

```javascript
// 带错误处理的国旗组件
const CountryFlagWithFallback = ({ country }) => {
    const [hasError, setHasError] = useState(false);
    
    if (hasError) {
        return <span className="flag-placeholder">{country.code}</span>;
    }
    
    return (
        <img 
            src={country.flagUrl}
            alt={`${country.name} flag`}
            onError={() => setHasError(true)}
            className="country-flag"
        />
    );
};
```

### 3. 国旗预加载

```javascript
// 预加载国旗图片
const preloadCountryFlags = (countries) => {
    countries.forEach(country => {
        const img = new Image();
        img.src = country.flagUrl;
    });
};
```

## 国际化支持

### 1. 多语言国家名称

```javascript
// 扩展支持多语言
class CountryWithI18n extends Country {
    get localizedName() {
        const locale = this.store.env.services.localization.locale;
        return this.translations?.[locale] || this.name;
    }
}
```

### 2. 国家名称排序

```javascript
// 按本地化名称排序
const sortCountriesByName = (countries, locale) => {
    return countries.sort((a, b) => {
        const nameA = a.localizedName || a.name;
        const nameB = b.localizedName || b.name;
        return nameA.localeCompare(nameB, locale);
    });
};
```

## 数据验证

### 1. 国家代码验证

```javascript
// 验证国家代码格式
const isValidCountryCode = (code) => {
    return /^[A-Z]{2}$/.test(code);
};

// 验证国家数据
const validateCountryData = (data) => {
    const errors = [];
    
    if (!data.code || !isValidCountryCode(data.code)) {
        errors.push("Invalid country code");
    }
    
    if (!data.name || data.name.trim().length === 0) {
        errors.push("Country name is required");
    }
    
    return errors;
};
```

### 2. 重复检查

```javascript
// 检查国家代码是否重复
const isDuplicateCountryCode = (code, excludeId = null) => {
    return Object.values(store.Country.records)
        .some(country => 
            country.code.toLowerCase() === code.toLowerCase() && 
            country.id !== excludeId
        );
};
```

## 性能优化

### 1. 国家列表缓存

```javascript
// 缓存排序后的国家列表
let cachedCountryList = null;
let lastUpdateTime = null;

const getSortedCountries = () => {
    const currentTime = Date.now();
    
    if (!cachedCountryList || currentTime - lastUpdateTime > 60000) {
        cachedCountryList = Object.values(store.Country.records)
            .sort((a, b) => a.name.localeCompare(b.name));
        lastUpdateTime = currentTime;
    }
    
    return cachedCountryList;
};
```

### 2. 国旗图片优化

```javascript
// 使用WebP格式的国旗（如果支持）
const getOptimizedFlagUrl = (country) => {
    const supportsWebP = document.createElement('canvas')
        .toDataURL('image/webp').indexOf('data:image/webp') === 0;
    
    const format = supportsWebP ? 'webp' : 'png';
    return `/base/static/img/country_flags/${country.code.toLowerCase()}.${format}`;
};
```

## 扩展功能

### 1. 地区分组

```javascript
// 按地区分组国家
const groupCountriesByRegion = (countries) => {
    const regions = {
        'Asia': ['CN', 'JP', 'KR', 'IN'],
        'Europe': ['FR', 'DE', 'IT', 'ES'],
        'North America': ['US', 'CA', 'MX'],
        'Others': []
    };
    
    const grouped = {};
    
    countries.forEach(country => {
        let region = 'Others';
        for (const [regionName, codes] of Object.entries(regions)) {
            if (codes.includes(country.code)) {
                region = regionName;
                break;
            }
        }
        
        if (!grouped[region]) {
            grouped[region] = [];
        }
        grouped[region].push(country);
    });
    
    return grouped;
};
```

### 2. 搜索功能

```javascript
// 国家搜索
const searchCountries = (query) => {
    const lowerQuery = query.toLowerCase();
    
    return Object.values(store.Country.records)
        .filter(country => 
            country.name.toLowerCase().includes(lowerQuery) ||
            country.code.toLowerCase().includes(lowerQuery)
        );
};
```

## 设计模式

### 1. 单例模式 (Singleton Pattern)
- 每个国家代码只有一个实例
- 通过Record基类的静态记录管理

### 2. 工厂模式 (Factory Pattern)
- 通过insert方法创建国家实例
- 统一的创建接口

### 3. 策略模式 (Strategy Pattern)
- 不同的国旗显示策略
- 多语言名称显示策略

## 注意事项

1. **国家代码标准**: 确保使用标准的ISO国家代码
2. **图片资源**: 确保国旗图片文件存在
3. **编码安全**: 正确处理特殊字符的URL编码
4. **缓存策略**: 合理缓存国家列表和图片资源

## 扩展建议

1. **多语言支持**: 添加多语言国家名称
2. **地区信息**: 扩展包含大洲、地区等信息
3. **货币信息**: 关联国家的货币信息
4. **时区支持**: 添加国家的时区信息
5. **电话代码**: 包含国际电话区号

该模型虽然简单，但为系统提供了重要的地理信息支持，是国际化应用的基础组件。

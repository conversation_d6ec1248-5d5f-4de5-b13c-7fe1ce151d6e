# Message Reaction Button - 消息反应按钮

## 概述

`message_reaction_button.js` 实现了 Odoo 邮件系统中的消息反应按钮组件，为用户提供添加表情反应的界面。该组件集成了表情选择器，支持用户快速为消息添加表情反应，并智能处理重复反应的情况，是消息交互功能的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/core/common/message_reaction_button.js`
- **行数**: 40
- **模块**: `@mail/core/common/message_reaction_button`

## 依赖关系

```javascript
// OWL 框架依赖
'@odoo/owl'                                    // OWL 核心组件

// Web 核心依赖
'@web/core/emoji_picker/emoji_picker'          // 表情选择器
'@web/core/utils/hooks'                        // Web 核心钩子
```

## 组件定义

### MessageReactionButton 类

```javascript
class MessageReactionButton extends Component {
    static template = "mail.MessageReactionButton";
    static props = ["message", "classNames?", "action"];
}
```

## Props 配置

### Props 定义

```typescript
interface Props {
    message: import("models").Message;    // 要添加反应的消息
    classNames?: string;                  // 自定义CSS类名（可选）
    action: object;                       // 操作配置对象
}
```

### Props 详细说明

- **`message`** (必需):
  - 类型: Message 模型
  - 用途: 要添加反应的消息对象
  - 功能: 提供反应的目标消息

- **`classNames`** (可选):
  - 类型: 字符串
  - 用途: 添加自定义CSS类名
  - 示例: `"custom-reaction-btn"`

- **`action`** (必需):
  - 类型: 对象
  - 用途: 操作配置，来自消息操作注册表
  - 功能: 提供操作的元数据和配置

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    
    // 存储服务
    this.store = useState(useService("mail.store"));
    
    // 表情选择器引用
    this.emojiPickerRef = useRef("emoji-picker");
    
    // 表情选择器钩子
    this.emojiPicker = useEmojiPicker(this.emojiPickerRef, {
        onSelect: (emoji) => {
            // 表情选择处理逻辑
        },
    });
}
```

**初始化内容**:
- 邮件存储服务的响应式状态
- 表情选择器的DOM引用
- 表情选择器钩子配置

## 核心功能

### 表情选择处理

```javascript
onSelect: (emoji) => {
    // 检查用户是否已经添加了这个表情
    const reaction = this.props.message.reactions.find(
        ({ content, personas }) =>
            content === emoji && personas.find((persona) => persona.eq(this.store.self))
    );
    
    // 如果用户还没有添加这个反应，则添加
    if (!reaction) {
        this.props.message.react(emoji);
    }
}
```

**处理逻辑**:
1. **查找现有反应**: 检查消息的反应列表
2. **匹配条件**: 
   - 表情内容相同 (`content === emoji`)
   - 当前用户已添加 (`personas.find(persona => persona.eq(this.store.self))`)
3. **避免重复**: 如果用户已添加相同表情，不执行操作
4. **添加反应**: 如果用户未添加，调用消息的 `react()` 方法

## 使用场景

### 1. 消息操作菜单中

```javascript
// 在消息操作注册表中注册反应按钮
messageActionsRegistry.add("reaction", {
    callComponent: MessageReactionButton,
    props: (component) => ({
        message: component.props.message,
        action: messageActionsRegistry.get("reaction"),
    }),
    condition: (component) => component.props.message.canAddReaction(component.props.thread),
    icon: "oi oi-smile-add",
    title: _t("Add a Reaction"),
    sequence: 10,
});
```

### 2. 消息悬停菜单

```javascript
// 在消息悬停时显示反应按钮
<div class="message-hover-menu">
    <MessageReactionButton 
        message={message}
        action={reactionAction}
        classNames="hover-reaction-btn"
    />
</div>
```

### 3. 快捷反应栏

```javascript
// 在消息下方显示快捷反应
<div class="quick-reactions">
    <MessageReactionButton 
        message={message}
        action={reactionAction}
        classNames="quick-reaction-btn"
    />
    {/* 其他快捷操作 */}
</div>
```

### 4. 移动端适配

```javascript
// 移动端的反应按钮
<MessageReactionButton 
    message={message}
    action={reactionAction}
    classNames="mobile-reaction-btn"
/>
```

## 表情选择器集成

### 1. 选择器配置

```javascript
this.emojiPicker = useEmojiPicker(this.emojiPickerRef, {
    onSelect: (emoji) => {
        // 选择处理逻辑
    },
    categories: ['people', 'nature', 'objects'],  // 可选：限制分类
    searchEnabled: true,                          // 可选：启用搜索
    recentEnabled: true,                          // 可选：显示最近使用
});
```

### 2. 选择器触发

```javascript
// 点击按钮时打开表情选择器
onClick() {
    this.emojiPicker.open();
}

// 或者通过模板中的引用触发
// <div t-ref="emoji-picker" t-on-click="() => this.emojiPicker.open()">
```

### 3. 选择器关闭

```javascript
// 选择表情后自动关闭
onSelect: (emoji) => {
    this.props.message.react(emoji);
    this.emojiPicker.close();  // 可选：手动关闭
}
```

## 反应逻辑

### 1. 重复检测

```javascript
// 检测用户是否已添加相同反应
const hasUserReaction = (message, emoji, user) => {
    return message.reactions.some(reaction => 
        reaction.content === emoji && 
        reaction.personas.some(persona => persona.eq(user))
    );
};
```

### 2. 智能反应

```javascript
// 智能反应处理：切换而不是只添加
const smartReact = (message, emoji, user) => {
    const existingReaction = message.reactions.find(reaction => 
        reaction.content === emoji && 
        reaction.personas.some(persona => persona.eq(user))
    );
    
    if (existingReaction) {
        // 用户已有此反应，移除它
        existingReaction.remove();
    } else {
        // 用户没有此反应，添加它
        message.react(emoji);
    }
};
```

### 3. 批量反应

```javascript
// 支持批量添加多个反应
const addMultipleReactions = (message, emojis) => {
    emojis.forEach(emoji => {
        if (!hasUserReaction(message, emoji, this.store.self)) {
            message.react(emoji);
        }
    });
};
```

## 样式和主题

### 1. 基础样式

```css
.message-reaction-button {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 16px;
    background: #fff;
    cursor: pointer;
    transition: all 0.2s ease;
}

.message-reaction-button:hover {
    background: #f5f5f5;
    border-color: #ccc;
}
```

### 2. 状态样式

```css
.message-reaction-button.active {
    background: #e3f2fd;
    border-color: #2196f3;
    color: #2196f3;
}

.message-reaction-button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
```

### 3. 响应式样式

```css
@media (max-width: 768px) {
    .message-reaction-button {
        padding: 6px 12px;
        font-size: 16px;
    }
}
```

## 可访问性

### 1. 键盘导航

```javascript
// 支持键盘操作
onKeydown(event) {
    if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        this.emojiPicker.open();
    }
}
```

### 2. ARIA 标签

```xml
<!-- 模板中的可访问性标签 -->
<button 
    class="message-reaction-button"
    aria-label="Add reaction to message"
    aria-expanded="false"
    t-ref="emoji-picker"
>
    <i class="oi oi-smile-add" aria-hidden="true"></i>
    <span class="sr-only">Add Reaction</span>
</button>
```

### 3. 屏幕阅读器支持

```javascript
// 为屏幕阅读器提供反馈
onReactionAdded(emoji) {
    announceToScreenReader(`Added ${emoji} reaction to message`);
}
```

## 性能优化

### 1. 事件防抖

```javascript
// 防抖点击事件
const debouncedClick = debounce(() => {
    this.emojiPicker.open();
}, 300);
```

### 2. 懒加载

```javascript
// 懒加载表情选择器
const lazyEmojiPicker = lazy(() => 
    import('@web/core/emoji_picker/emoji_picker')
);
```

### 3. 缓存优化

```javascript
// 缓存反应检查结果
let cachedReactionCheck = null;
let lastCheckTime = null;

const hasReaction = (message, emoji, user) => {
    const now = Date.now();
    if (cachedReactionCheck && now - lastCheckTime < 1000) {
        return cachedReactionCheck;
    }
    
    cachedReactionCheck = message.reactions.some(/* ... */);
    lastCheckTime = now;
    return cachedReactionCheck;
};
```

## 设计模式

### 1. 组合模式 (Composition Pattern)
- 组合表情选择器和反应逻辑
- 提供统一的反应添加界面

### 2. 策略模式 (Strategy Pattern)
- 根据用户状态采用不同的反应策略
- 添加 vs 移除的不同处理

### 3. 观察者模式 (Observer Pattern)
- 监听消息反应变化
- 响应用户交互事件

## 注意事项

1. **重复处理**: 正确处理用户重复添加相同反应
2. **权限检查**: 验证用户是否有权限添加反应
3. **性能考虑**: 避免频繁的DOM操作
4. **用户体验**: 提供即时的视觉反馈

## 扩展建议

1. **快捷反应**: 提供常用表情的快捷按钮
2. **反应预览**: 悬停时预览反应效果
3. **自定义表情**: 支持自定义表情符号
4. **反应动画**: 添加反应的动画效果
5. **批量操作**: 支持批量添加多个反应

该组件为用户提供了简单直观的表情反应添加方式，增强了消息的互动性和表达能力，是现代聊天应用的标准功能。

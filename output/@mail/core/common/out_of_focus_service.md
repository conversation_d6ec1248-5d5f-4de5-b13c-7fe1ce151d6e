# Out of Focus Service - 失焦通知服务

## 概述

`out_of_focus_service.js` 实现了 Odoo 邮件系统中的失焦通知服务，用于在用户不在当前页面时发送桌面通知和音频提醒。该服务支持原生桌面通知、Service Worker 推送通知、音频提醒等功能，提供了完整的后台通知解决方案，确保用户不会错过重要消息，是邮件系统中重要的用户体验增强服务。

## 文件信息
- **路径**: `/mail/static/src/core/common/out_of_focus_service.js`
- **行数**: 185
- **模块**: `@mail/core/common/out_of_focus_service`

## 依赖关系

```javascript
// 核心依赖
'@mail/utils/common/format'    // 邮件格式化工具
'@web/core/browser/browser'    // 浏览器工具
'@web/core/l10n/translation'   // 国际化
'@web/core/registry'           // 服务注册表
'@web/core/utils/urls'         // URL 工具
```

## 服务定义

### OutOfFocusService 类

```javascript
class OutOfFocusService {
    constructor(env, services) {
        this.setup(env, services);
    }
    
    setup(env, services) {
        this.env = env;
        this.audio = undefined;
        this.multiTab = services.multi_tab;
        this.notificationService = services.notification;
        this.closeFuncs = [];
    }
}
```

### 服务配置

```javascript
const outOfFocusService = {
    dependencies: ["multi_tab", "notification"],
    start(env, services) {
        const service = new OutOfFocusService(env, services);
        return service;
    }
};
```

## 常量定义

### 消息预览大小

```javascript
const PREVIEW_MSG_MAX_SIZE = 350; // 针对英语用户优化的最佳长度
```

**用途**:
- 限制通知消息的最大长度
- 确保通知内容的可读性
- 避免通知内容过长

## 核心功能

### 1. 消息通知

```javascript
async notify(message, channel) {
    const modelsHandleByPush = ["mail.thread", "discuss.channel"];
    
    // 检查是否由 Push 服务处理
    if (
        modelsHandleByPush.includes(message.thread?.model) &&
        (await this.hasServiceWorkInstalledAndPushSubscriptionActive())
    ) {
        return;
    }
    
    // 生成通知标题
    const author = message.author;
    let notificationTitle;
    if (!author) {
        notificationTitle = _t("New message");
    } else {
        if (channel.channel_type === "channel") {
            notificationTitle = _t("%(author name)s from %(channel name)s", {
                "author name": author.name,
                "channel name": channel.displayName,
            });
        } else {
            notificationTitle = author.name;
        }
    }
    
    // 生成通知内容
    const notificationContent = htmlToTextContentInline(message.body).substring(
        0,
        PREVIEW_MSG_MAX_SIZE
    );
    
    // 发送通知
    this.sendNotification({
        message: notificationContent,
        title: notificationTitle,
        type: "info",
    });
}
```

**通知逻辑**:
1. **Push 服务检查**: 如果有 Service Worker 推送，则跳过
2. **标题生成**: 根据消息类型生成不同的标题
3. **内容处理**: 将 HTML 转换为纯文本并限制长度
4. **发送通知**: 调用通知发送方法

### 2. Service Worker 检查

```javascript
async hasServiceWorkInstalledAndPushSubscriptionActive() {
    const registration = await browser.navigator.serviceWorker?.getRegistration();
    if (registration) {
        const pushManager = await registration.pushManager;
        if (pushManager) {
            const subscription = await pushManager.getSubscription();
            return !!subscription;
        }
    }
    return false;
}
```

**检查逻辑**:
- 检查 Service Worker 是否已注册
- 检查 Push Manager 是否可用
- 检查推送订阅是否激活

### 3. 通知发送

```javascript
sendNotification({ message, title, type }) {
    if (!this.canSendNativeNotification) {
        this.sendOdooNotification(message, { title, type });
        return;
    }
    
    if (!this.multiTab.isOnMainTab()) {
        return;
    }
    
    try {
        this.sendNativeNotification(title, message);
    } catch (error) {
        // Chrome Android 的 Service Worker 回退处理
        if (error.message.includes("ServiceWorkerRegistration")) {
            this.sendOdooNotification(message, { title, type });
        } else {
            throw error;
        }
    }
}
```

**发送策略**:
1. **原生通知优先**: 如果支持原生通知，优先使用
2. **多标签页检查**: 只在主标签页发送通知
3. **错误回退**: 如果原生通知失败，回退到 Odoo 通知

### 4. 原生通知

```javascript
sendNativeNotification(title, message) {
    const notification = new Notification(title, {
        body: message,
        icon: "/mail/static/src/img/odoobot_transparent.png",
    });
    
    notification.addEventListener("click", ({ target: notification }) => {
        window.focus();
        notification.close();
    });
    
    this._playSound();
}
```

**原生通知特性**:
- 使用浏览器原生 Notification API
- 设置 Odoo 图标
- 点击通知时聚焦窗口
- 播放提示音

### 5. Odoo 通知

```javascript
async sendOdooNotification(message, options) {
    this.closeFuncs.push(this.notificationService.add(message, options));
    
    // 限制通知数量
    if (this.closeFuncs.length > 3) {
        this.closeFuncs.shift()();
    }
    
    this._playSound();
}
```

**Odoo 通知特性**:
- 使用内置通知服务
- 限制同时显示的通知数量（最多3个）
- 自动关闭旧通知
- 播放提示音

### 6. 音频提醒

```javascript
async _playSound() {
    if (this.canPlayAudio && this.multiTab.isOnMainTab()) {
        if (!this.audio) {
            this.audio = new Audio();
            this.audio.src = this.audio.canPlayType("audio/ogg; codecs=vorbis")
                ? url("/mail/static/src/audio/ting.ogg")
                : url("/mail/static/src/audio/ting.mp3");
        }
        
        try {
            await this.audio.play();
        } catch {
            // 忽略用户未交互导致的播放错误
        }
    }
}
```

**音频特性**:
- 支持 OGG 和 MP3 格式
- 自动选择最佳音频格式
- 只在主标签页播放
- 静默处理播放错误

## 能力检查

### 1. 音频播放能力

```javascript
get canPlayAudio() {
    return typeof Audio !== "undefined";
}
```

### 2. 原生通知能力

```javascript
get canSendNativeNotification() {
    return Boolean(browser.Notification && browser.Notification.permission === "granted");
}
```

## 使用场景

### 1. 新消息通知

```javascript
// 接收到新消息时的通知
const handleNewMessage = (message, channel) => {
    if (document.hidden || !document.hasFocus()) {
        outOfFocusService.notify(message, channel);
    }
};
```

### 2. 聊天消息通知

```javascript
// 聊天消息的通知处理
const handleChatMessage = (message, thread) => {
    const channel = {
        channel_type: thread.channel_type,
        displayName: thread.displayName
    };
    
    outOfFocusService.notify(message, channel);
};
```

### 3. 提及通知

```javascript
// 用户被提及时的通知
const handleMentionNotification = (message, channel) => {
    // 强制发送通知，即使在焦点状态
    outOfFocusService.sendNotification({
        message: `您在 ${channel.displayName} 中被提及`,
        title: `来自 ${message.author.name} 的提及`,
        type: "warning"
    });
};
```

### 4. 系统通知

```javascript
// 系统级通知
const sendSystemNotification = (title, message, type = "info") => {
    outOfFocusService.sendNotification({
        message: message,
        title: title,
        type: type
    });
};
```

## 通知管理

### 1. 通知队列管理

```javascript
// 通知队列管理
class NotificationQueue {
    constructor(maxSize = 3) {
        this.queue = [];
        this.maxSize = maxSize;
    }
    
    add(notification) {
        this.queue.push(notification);
        
        if (this.queue.length > this.maxSize) {
            const oldNotification = this.queue.shift();
            oldNotification.close();
        }
    }
    
    clear() {
        this.queue.forEach(notification => notification.close());
        this.queue = [];
    }
}
```

### 2. 通知去重

```javascript
// 通知去重处理
const deduplicateNotifications = (notifications) => {
    const seen = new Set();
    return notifications.filter(notification => {
        const key = `${notification.title}_${notification.message}`;
        if (seen.has(key)) {
            return false;
        }
        seen.add(key);
        return true;
    });
};
```

### 3. 通知优先级

```javascript
// 通知优先级处理
const NOTIFICATION_PRIORITIES = {
    urgent: 3,
    important: 2,
    normal: 1,
    low: 0
};

const prioritizeNotifications = (notifications) => {
    return notifications.sort((a, b) => 
        NOTIFICATION_PRIORITIES[b.priority] - NOTIFICATION_PRIORITIES[a.priority]
    );
};
```

## 多标签页处理

### 1. 主标签页检查

```javascript
// 检查是否为主标签页
const isMainTab = () => {
    return multiTabService.isOnMainTab();
};

// 只在主标签页发送通知
const sendNotificationOnMainTab = (notification) => {
    if (isMainTab()) {
        outOfFocusService.sendNotification(notification);
    }
};
```

### 2. 标签页同步

```javascript
// 标签页间的通知同步
const syncNotificationAcrossTabs = (notification) => {
    // 广播通知到其他标签页
    multiTabService.broadcast('notification_sent', {
        id: notification.id,
        timestamp: Date.now()
    });
};
```

## 权限管理

### 1. 通知权限请求

```javascript
// 请求通知权限
const requestNotificationPermission = async () => {
    if (!browser.Notification) {
        return 'unsupported';
    }
    
    if (browser.Notification.permission === 'granted') {
        return 'granted';
    }
    
    if (browser.Notification.permission === 'denied') {
        return 'denied';
    }
    
    const permission = await browser.Notification.requestPermission();
    return permission;
};
```

### 2. 权限状态检查

```javascript
// 检查通知权限状态
const checkNotificationPermission = () => {
    if (!browser.Notification) {
        return {
            supported: false,
            permission: 'unsupported'
        };
    }
    
    return {
        supported: true,
        permission: browser.Notification.permission
    };
};
```

## 错误处理

### 1. 通知发送错误

```javascript
// 通知发送错误处理
const handleNotificationError = (error, fallbackOptions) => {
    console.warn('Native notification failed:', error);
    
    if (error.message.includes("ServiceWorkerRegistration")) {
        // Chrome Android 特殊处理
        outOfFocusService.sendOdooNotification(
            fallbackOptions.message, 
            fallbackOptions
        );
    } else {
        // 其他错误
        console.error('Notification error:', error);
    }
};
```

### 2. 音频播放错误

```javascript
// 音频播放错误处理
const handleAudioError = (error) => {
    if (error.name === 'NotAllowedError') {
        console.info('Audio play blocked by user interaction policy');
    } else {
        console.warn('Audio play failed:', error);
    }
};
```

## 性能优化

### 1. 音频预加载

```javascript
// 音频预加载
const preloadAudio = () => {
    if (typeof Audio !== "undefined") {
        const audio = new Audio();
        audio.preload = 'auto';
        audio.src = audio.canPlayType("audio/ogg; codecs=vorbis")
            ? url("/mail/static/src/audio/ting.ogg")
            : url("/mail/static/src/audio/ting.mp3");
        return audio;
    }
    return null;
};
```

### 2. 通知节流

```javascript
// 通知节流处理
const throttleNotifications = (func, delay = 1000) => {
    let lastCall = 0;
    return function(...args) {
        const now = Date.now();
        if (now - lastCall >= delay) {
            lastCall = now;
            return func.apply(this, args);
        }
    };
};
```

## 设计模式

### 1. 服务模式 (Service Pattern)
- 全局通知服务
- 统一的通知接口

### 2. 策略模式 (Strategy Pattern)
- 原生通知 vs Odoo 通知策略
- 不同音频格式的选择策略

### 3. 观察者模式 (Observer Pattern)
- 监听页面焦点状态
- 响应消息事件

## 注意事项

1. **权限管理**: 确保正确处理通知权限
2. **多标签页**: 避免重复通知
3. **性能考虑**: 合理控制通知频率
4. **用户体验**: 提供清晰的通知内容

## 扩展建议

1. **通知模板**: 支持自定义通知模板
2. **通知历史**: 记录通知历史
3. **通知设置**: 支持用户自定义通知设置
4. **富媒体通知**: 支持图片和按钮的通知
5. **通知分析**: 提供通知效果分析

该服务为邮件系统提供了完整的后台通知功能，确保用户不会错过重要消息，显著提升了用户体验。

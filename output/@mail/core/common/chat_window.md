# Chat Window - 聊天窗口

## 概述

`chat_window.js` 实现了 Odoo 邮件系统中的聊天窗口组件，提供了独立的聊天界面。该组件支持窗口的打开、关闭、折叠、重命名等操作，集成了消息显示、编辑器、线程操作等功能，为用户提供了完整的聊天体验。

## 文件信息
- **路径**: `/mail/static/src/core/common/chat_window.js`
- **行数**: 181
- **模块**: `@mail/core/common/chat_window`

## 依赖关系

```javascript
// 核心组件依赖
'@mail/core/common/composer'          // 消息编辑器
'@mail/core/common/im_status'         // 即时消息状态
'@mail/core/common/thread'            // 线程组件
'@mail/core/common/autoresize_input'  // 自动调整输入框
'@mail/core/common/country_flag'      // 国家旗帜
'@mail/core/common/thread_actions'    // 线程操作
'@mail/core/common/thread_icon'       // 线程图标

// 功能钩子依赖
'@mail/utils/common/hooks'            // 邮件系统钩子
'@mail/discuss/typing/common/typing'  // 打字状态

// Web 核心依赖
'@web/core/dropdown/dropdown'         // 下拉菜单
'@web/core/dropdown/dropdown_item'    // 下拉菜单项
'@web/core/l10n/localization'         // 本地化
'@web/core/l10n/translation'          // 国际化
'@web/core/utils/hooks'               // Web 核心钩子
'@web/core/utils/misc'                // 工具函数
'@odoo/owl'                           // OWL 框架
```

## 组件定义

### ChatWindow 类

```javascript
class ChatWindow extends Component {
    static components = {
        CountryFlag,      // 国家旗帜组件
        Dropdown,         // 下拉菜单组件
        DropdownItem,     // 下拉菜单项组件
        Thread,           // 线程组件
        Composer,         // 编辑器组件
        ThreadIcon,       // 线程图标组件
        ImStatus,         // 状态指示器组件
        AutoresizeInput,  // 自动调整输入框组件
        Typing,           // 打字状态组件
    };
    static template = "mail.ChatWindow";
}
```

## Props 配置

### Props 接口

```typescript
interface Props {
    chatWindow: import("models").ChatWindow;  // 聊天窗口模型（必需）
    right?: boolean;                         // 是否在右侧显示（可选）
}
```

### Props 定义

```javascript
static props = ["chatWindow", "right?"];
```

### Props 详细说明

- **`chatWindow`** (必需):
  - 类型: ChatWindow 模型对象
  - 用途: 聊天窗口的数据模型
  - 包含: 线程信息、窗口状态、位置等

- **`right`** (可选):
  - 类型: 布尔值
  - 用途: 控制窗口在屏幕右侧的偏移位置
  - 默认: 未定义（自动计算位置）

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    
    // 基础服务
    this.store = useState(useService("mail.store"));
    this.ui = useState(useService("ui"));
    
    // 功能钩子
    this.messageEdition = useMessageEdition();
    this.messageHighlight = useMessageHighlight();
    this.messageToReplyTo = useMessageToReplyTo();
    this.threadActions = useThreadActions();
    
    // 悬停状态
    this.actionsMenuButtonHover = useHover("actionsMenuButton");
    this.parentChannelHover = useHover("parentChannel");
    
    // 组件状态
    this.state = useState({
        actionsDisabled: false,      // 操作是否禁用
        actionsMenuOpened: false,    // 操作菜单是否打开
        jumpThreadPresent: 0,        // 跳转到最新消息的触发器
        editingGuestName: false,     // 是否正在编辑访客名称
        editingName: false,          // 是否正在编辑线程名称
    });
    
    // DOM 引用
    this.contentRef = useRef("content");
    
    // 子环境配置
    useChildSubEnv({
        closeActionPanel: () => this.threadActions.activeAction?.close(),
        inChatWindow: true,
        messageHighlight: this.messageHighlight,
    });
}
```

## 核心功能

### 1. 计算属性

#### 编辑器类型
```javascript
get composerType() {
    if (this.thread && this.thread.model !== "discuss.channel") {
        return "note";  // 非频道类型使用笔记模式
    }
    return undefined;   // 频道类型使用默认模式
}
```

#### 关联线程
```javascript
get thread() {
    return this.props.chatWindow.thread;
}
```

#### 窗口样式
```javascript
get style() {
    const maxHeight = !this.ui.isSmall ? "max-height: 95vh;" : "";
    const textDirection = localization.direction;
    const offsetFrom = textDirection === "rtl" ? "left" : "right";
    const visibleOffset = this.ui.isSmall ? 0 : this.props.right;
    const oppositeFrom = offsetFrom === "right" ? "left" : "right";
    return `${offsetFrom}: ${visibleOffset}px; ${oppositeFrom}: auto; ${maxHeight}`;
}
```

**样式计算逻辑**:
- 根据屏幕尺寸设置最大高度
- 支持 RTL（从右到左）文本方向
- 根据 `right` 属性计算水平偏移
- 移动端时忽略偏移设置

### 2. 键盘事件处理

```javascript
onKeydown(ev) {
    const chatWindow = toRaw(this.props.chatWindow);
    
    // 忽略下拉菜单内的事件
    if (ev.target.closest(".o-dropdown") || ev.target.closest(".o-dropdown--menu")) {
        return;
    }
    
    ev.stopPropagation(); // 防止主菜单拦截快捷键
    
    switch (ev.key) {
        case "Escape":
            // 处理各种取消操作
            if (isEventHandled(ev, "NavigableList.close") ||
                isEventHandled(ev, "Composer.discard")) {
                return;
            }
            if (this.state.editingName) {
                this.state.editingName = false;
                return;
            }
            this.close({ escape: true });
            break;
            
        case "Tab":
            // 在聊天窗口间切换
            const index = this.store.chatHub.opened.findIndex((cw) => cw.eq(chatWindow));
            if (index === this.store.chatHub.opened.length - 1) {
                this.store.chatHub.opened[0].focus();
            } else {
                this.store.chatHub.opened[index + 1].focus();
            }
            break;
    }
}
```

**键盘快捷键**:
- **Escape**: 关闭窗口或取消编辑
- **Tab**: 在多个聊天窗口间循环切换

### 3. 窗口操作

#### 头部点击处理
```javascript
onClickHeader() {
    if (this.ui.isSmall ||           // 移动端
        this.state.editingName ||     // 正在编辑名称
        !this.thread ||               // 无线程
        this.state.actionsDisabled    // 操作被禁用
    ) {
        return;
    }
    this.toggleFold();
}
```

#### 折叠切换
```javascript
toggleFold() {
    const chatWindow = toRaw(this.props.chatWindow);
    if (this.ui.isSmall || this.state.actionsMenuOpened) {
        return;
    }
    chatWindow.fold();
}
```

#### 关闭窗口
```javascript
async close(options) {
    const chatWindow = toRaw(this.props.chatWindow);
    await chatWindow.close(options);
}
```

### 4. 重命名功能

#### 线程重命名
```javascript
async renameThread(name) {
    const thread = toRaw(this.thread);
    await thread.rename(name);
    this.state.editingName = false;
}
```

#### 访客重命名
```javascript
async renameGuest(name) {
    const newName = name.trim();
    if (this.store.self.name !== newName) {
        await this.store.self.updateGuestName(newName);
    }
    this.state.editingGuestName = false;
}
```

### 5. 菜单状态管理

#### 操作菜单标题
```javascript
get actionsMenuTitleText() {
    return _t("Open Actions Menu");
}
```

#### 菜单状态变化
```javascript
async onActionsMenuStateChanged(isOpen) {
    this.state.actionsMenuOpened = isOpen;
}
```

## 子环境配置

### 聊天窗口环境

```javascript
useChildSubEnv({
    closeActionPanel: () => this.threadActions.activeAction?.close(),
    inChatWindow: true,
    messageHighlight: this.messageHighlight,
});
```

**环境属性**:
- **`closeActionPanel`**: 关闭操作面板的回调
- **`inChatWindow`**: 标识当前在聊天窗口中
- **`messageHighlight`**: 消息高亮状态

## 响应式设计

### 移动端适配

```javascript
// 移动端时的特殊处理
if (this.ui.isSmall) {
    // 禁用某些桌面端功能
    // 调整样式和布局
    // 简化交互方式
}
```

### RTL 支持

```javascript
// 支持从右到左的文本方向
const textDirection = localization.direction;
const offsetFrom = textDirection === "rtl" ? "left" : "right";
```

## 使用场景

### 1. 私聊窗口

```xml
<ChatWindow 
    chatWindow="privateChatWindow"
    right="300"
/>
```

### 2. 群组聊天窗口

```xml
<ChatWindow 
    chatWindow="groupChatWindow"
    right="600"
/>
```

### 3. 客服聊天窗口

```xml
<ChatWindow 
    chatWindow="livechatWindow"
    right="0"
/>
```

## 窗口状态

### 窗口状态类型

1. **打开** (opened): 窗口完全展开显示
2. **折叠** (folded): 窗口最小化，只显示标题栏
3. **关闭** (closed): 窗口完全隐藏

### 状态转换

```javascript
// 打开 -> 折叠
chatWindow.fold();

// 折叠 -> 打开
chatWindow.unfold();

// 任何状态 -> 关闭
chatWindow.close();
```

## 性能优化

### 1. 事件处理优化

```javascript
// 阻止事件冒泡避免干扰
ev.stopPropagation();

// 检查事件是否已被处理
if (isEventHandled(ev, "NavigableList.close")) {
    return;
}
```

### 2. 状态管理优化

```javascript
// 使用 toRaw 避免响应式开销
const chatWindow = toRaw(this.props.chatWindow);
```

### 3. 条件渲染

```javascript
// 根据状态条件渲染组件
if (this.ui.isSmall || this.state.actionsDisabled) {
    return;
}
```

## 设计模式

### 1. 组合模式 (Composition Pattern)
- 组合多个子组件构建完整的聊天窗口
- 每个子组件负责特定功能

### 2. 状态模式 (State Pattern)
- 根据窗口状态显示不同的界面
- 状态变化驱动行为改变

### 3. 命令模式 (Command Pattern)
- 键盘快捷键映射到具体操作
- 支持撤销和重做

### 4. 观察者模式 (Observer Pattern)
- 监听窗口状态变化
- 自动更新相关UI元素

## 注意事项

1. **键盘导航**: 确保所有功能都支持键盘操作
2. **移动端适配**: 在小屏幕上提供合适的交互体验
3. **状态同步**: 保持窗口状态与数据模型一致
4. **内存管理**: 及时清理事件监听器和引用

## 扩展建议

1. **窗口拖拽**: 支持拖拽调整窗口位置
2. **窗口大小调整**: 支持调整窗口尺寸
3. **多窗口管理**: 优化多窗口的布局和切换
4. **快捷键定制**: 允许用户自定义快捷键
5. **窗口主题**: 支持自定义窗口外观主题

该组件为用户提供了独立的聊天体验，支持多窗口并行使用，是邮件系统中重要的用户界面组件。

# IM Status Service Patch - 即时消息状态服务补丁

## 概述

`im_status_service_patch.js` 是对 Odoo 核心即时消息状态服务的补丁扩展，专门为邮件系统增强了状态管理功能。该补丁通过监听总线事件来同步用户状态，特别处理了访客用户的状态更新，确保邮件系统中的用户状态与全局状态保持一致。

## 文件信息
- **路径**: `/mail/static/src/core/common/im_status_service_patch.js`
- **行数**: 48
- **模块**: `@mail/core/common/im_status_service_patch`

## 依赖关系

```javascript
// 核心依赖
'@bus/im_status_service'       // 原始即时消息状态服务
'@web/core/utils/patch'        // 补丁工具
```

## 补丁定义

### imStatusServicePatch 对象

```javascript
const imStatusServicePatch = {
    start(env, services) {
        const { bus_service, presence } = services;
        const API = super.start(env, services);
        
        // 添加邮件系统特定的状态监听
        // ...
        
        return API;
    },
};
```

## 核心功能

### 1. 总线事件监听

```javascript
bus_service.subscribe(
    "bus.bus/im_status_updated",
    ({ im_status, partner_id, guest_id }) => {
        // 状态更新处理逻辑
    }
);
```

**监听的事件**:
- **事件名**: `"bus.bus/im_status_updated"`
- **参数**: 
  - `im_status`: 新的即时消息状态
  - `partner_id`: 合作伙伴ID（如果是注册用户）
  - `guest_id`: 访客ID（如果是访客用户）

### 2. 用户角色识别

```javascript
const persona = store.Persona.get({
    type: partner_id ? "partner" : "guest",
    id: partner_id ?? guest_id,
});
```

**识别逻辑**:
- **有 partner_id**: 识别为注册用户 (`"partner"`)
- **有 guest_id**: 识别为访客用户 (`"guest"`)
- **使用对应ID**: 获取相应的用户角色对象

### 3. 状态更新处理

```javascript
if (!persona) {
    return; // Do not store unknown persona's status
}
persona.im_status = im_status;
```

**更新流程**:
1. 检查用户角色是否存在
2. 如果不存在，忽略状态更新
3. 如果存在，更新用户的即时消息状态

### 4. 访客状态特殊处理

```javascript
if (persona.type !== "guest" || persona.notEq(store.self)) {
    return; // Partners are already handled by the original service
}

const isOnline = presence.getInactivityPeriod() < AWAY_DELAY;
if ((im_status === "away" && isOnline) || im_status === "offline") {
    this.updateBusPresence();
}
```

**特殊处理逻辑**:
1. **跳过注册用户**: 注册用户由原始服务处理
2. **只处理当前访客**: 只处理当前用户自己的状态
3. **在线状态检查**: 检查用户是否真正在线
4. **总线状态更新**: 在特定条件下更新总线状态

## 状态类型

### IM 状态枚举

```javascript
const IM_STATUS = {
    ONLINE: "online",       // 在线
    AWAY: "away",          // 离开
    OFFLINE: "offline"     // 离线
};
```

### 状态转换条件

```javascript
// 需要更新总线状态的条件
const shouldUpdateBusPresence = (im_status, isOnline) => {
    return (im_status === "away" && isOnline) || im_status === "offline";
};
```

## 使用场景

### 1. 访客用户状态同步

```javascript
// 访客用户状态变化时的处理
const handleGuestStatusChange = (guestId, newStatus) => {
    const guest = store.Persona.get({
        type: "guest",
        id: guestId
    });
    
    if (guest && guest.eq(store.self)) {
        guest.im_status = newStatus;
        
        // 检查是否需要更新总线状态
        const isOnline = presence.getInactivityPeriod() < AWAY_DELAY;
        if (shouldUpdateBusPresence(newStatus, isOnline)) {
            updateBusPresence();
        }
    }
};
```

### 2. 状态不一致修复

```javascript
// 修复状态不一致的情况
const fixStatusInconsistency = () => {
    const currentUser = store.self;
    if (currentUser && currentUser.type === "guest") {
        const actualStatus = getActualUserStatus();
        const storedStatus = currentUser.im_status;
        
        if (actualStatus !== storedStatus) {
            currentUser.im_status = actualStatus;
            updateBusPresence();
        }
    }
};
```

### 3. 状态监控

```javascript
// 监控状态变化
const monitorStatusChanges = () => {
    const statusHistory = [];
    
    bus_service.subscribe("bus.bus/im_status_updated", (data) => {
        statusHistory.push({
            timestamp: Date.now(),
            ...data
        });
        
        // 保留最近100条记录
        if (statusHistory.length > 100) {
            statusHistory.shift();
        }
    });
    
    return statusHistory;
};
```

## 补丁应用

### 1. 补丁注册

```javascript
const unpatchImStatusService = patch(imStatusService, imStatusServicePatch);
```

**补丁机制**:
- 使用 Odoo 的 patch 工具
- 扩展原始服务的功能
- 保持向后兼容性

### 2. 补丁移除

```javascript
// 如果需要移除补丁
const removePatch = () => {
    if (unpatchImStatusService) {
        unpatchImStatusService();
    }
};
```

### 3. 补丁验证

```javascript
// 验证补丁是否正确应用
const validatePatch = () => {
    const service = env.services['im_status'];
    return service && typeof service.start === 'function';
};
```

## 状态同步机制

### 1. 总线事件流

```mermaid
sequenceDiagram
    participant User as 用户
    participant Bus as 总线服务
    participant Patch as 状态补丁
    participant Store as 邮件存储
    
    User->>Bus: 状态变化
    Bus->>Patch: im_status_updated 事件
    Patch->>Store: 查找用户角色
    Store->>Patch: 返回用户对象
    Patch->>Store: 更新状态
    Patch->>Bus: 更新总线状态（如需要）
```

### 2. 状态一致性保证

```javascript
// 确保状态一致性
const ensureStatusConsistency = () => {
    const allPersonas = Object.values(store.Persona.records);
    
    allPersonas.forEach(persona => {
        if (persona.type === "guest" && persona.eq(store.self)) {
            const currentStatus = presence.getImStatus();
            if (persona.im_status !== currentStatus) {
                persona.im_status = currentStatus;
            }
        }
    });
};
```

### 3. 错误恢复

```javascript
// 状态同步错误恢复
const recoverFromStatusError = (error) => {
    console.error('状态同步错误:', error);
    
    // 重新获取当前状态
    try {
        const currentStatus = presence.getImStatus();
        const currentUser = store.self;
        
        if (currentUser && currentUser.type === "guest") {
            currentUser.im_status = currentStatus;
        }
    } catch (recoveryError) {
        console.error('状态恢复失败:', recoveryError);
    }
};
```

## 性能优化

### 1. 事件防抖

```javascript
// 防抖状态更新
const debouncedStatusUpdate = debounce((persona, status) => {
    persona.im_status = status;
}, 100);
```

### 2. 批量更新

```javascript
// 批量处理状态更新
const batchStatusUpdates = (() => {
    const pendingUpdates = new Map();
    
    return (persona, status) => {
        pendingUpdates.set(persona.id, { persona, status });
        
        // 下一个事件循环中批量处理
        setTimeout(() => {
            for (const [id, { persona, status }] of pendingUpdates) {
                persona.im_status = status;
            }
            pendingUpdates.clear();
        }, 0);
    };
})();
```

### 3. 内存优化

```javascript
// 清理无用的状态监听
const cleanupStatusListeners = () => {
    // 移除已断开连接用户的监听器
    const activePersonas = getActivePersonas();
    const allListeners = getStatusListeners();
    
    allListeners.forEach(listener => {
        if (!activePersonas.includes(listener.personaId)) {
            removeStatusListener(listener);
        }
    });
};
```

## 调试和监控

### 1. 状态日志

```javascript
// 状态变化日志
const logStatusChange = (persona, oldStatus, newStatus) => {
    console.log(`[IM Status] ${persona.name}: ${oldStatus} -> ${newStatus}`);
};
```

### 2. 状态统计

```javascript
// 状态统计信息
const getStatusStatistics = () => {
    const personas = Object.values(store.Persona.records);
    const stats = {};
    
    personas.forEach(persona => {
        const status = persona.im_status || 'unknown';
        stats[status] = (stats[status] || 0) + 1;
    });
    
    return stats;
};
```

### 3. 健康检查

```javascript
// 状态服务健康检查
const healthCheck = () => {
    return {
        patchApplied: !!unpatchImStatusService,
        busServiceActive: !!env.services.bus_service,
        presenceServiceActive: !!env.services.presence,
        statusCount: Object.keys(store.Persona.records).length
    };
};
```

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 扩展现有服务功能
- 保持原有API不变

### 2. 观察者模式 (Observer Pattern)
- 监听总线事件
- 响应状态变化

### 3. 策略模式 (Strategy Pattern)
- 不同用户类型的不同处理策略
- 注册用户 vs 访客用户

## 注意事项

1. **兼容性**: 确保与原始服务的兼容性
2. **性能影响**: 避免过度的状态更新
3. **错误处理**: 妥善处理状态同步错误
4. **内存泄漏**: 及时清理事件监听器

## 扩展建议

1. **状态历史**: 记录状态变化历史
2. **自定义状态**: 支持自定义状态类型
3. **状态预测**: 基于用户行为预测状态
4. **离线检测**: 更准确的离线状态检测
5. **状态分析**: 提供状态使用分析

该补丁为邮件系统提供了增强的即时消息状态管理，确保了状态的一致性和准确性，特别是对访客用户的支持。

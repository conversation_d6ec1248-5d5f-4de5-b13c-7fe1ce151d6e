# Message Reaction Menu - 消息反应菜单

## 概述

`message_reaction_menu.js` 实现了 Odoo 邮件系统中的消息反应菜单组件，用于显示消息的详细反应信息。该组件以对话框形式展示反应的用户列表、表情详情等信息，支持键盘导航和外部点击关闭，为用户提供了详细的反应查看界面，是消息反应系统的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/core/common/message_reaction_menu.js`
- **行数**: 86
- **模块**: `@mail/core/common/message_reaction_menu`

## 依赖关系

```javascript
// 核心依赖
'@web/core/emoji_picker/emoji_picker'  // 表情选择器
'@mail/utils/common/hooks'             // 邮件工具钩子
'@odoo/owl'                           // OWL 框架
'@web/core/dialog/dialog'             // 对话框组件
'@web/core/utils/hooks'               // Web 核心钩子
```

## 组件定义

### MessageReactionMenu 类

```javascript
class MessageReactionMenu extends Component {
    static props = ["close", "message", "initialReaction?"];
    static components = { Dialog };
    static template = "mail.MessageReactionMenu";
}
```

## Props 配置

### Props 定义

```typescript
interface Props {
    close: function;                                           // 关闭菜单回调
    message: import("models").Message;                         // 消息对象
    initialReaction?: import("models").MessageReactions;       // 初始选中的反应（可选）
}
```

### Props 详细说明

- **`close`** (必需):
  - 类型: 函数
  - 用途: 关闭反应菜单的回调
  - 触发: ESC键、外部点击或其他关闭操作

- **`message`** (必需):
  - 类型: Message 模型
  - 用途: 包含反应的消息对象
  - 功能: 提供反应数据源

- **`initialReaction`** (可选):
  - 类型: MessageReactions 模型
  - 用途: 初始选中显示的反应
  - 默认: 消息的第一个反应

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    
    // DOM 引用
    this.root = useRef("root");
    
    // 服务依赖
    this.store = useState(useService("mail.store"));
    this.ui = useState(useService("ui"));
    
    // 组件状态
    this.state = useState({
        emojiLoaded: Boolean(loader.loaded),
        reaction: this.props.initialReaction
            ? this.props.initialReaction
            : this.props.message.reactions[0],
    });
    
    // 事件监听
    useExternalListener(document, "keydown", this.onKeydown);
    onExternalClick("root", () => this.props.close());
    
    // 反应状态监听
    useEffect(() => {
        const activeReaction = this.props.message.reactions.find(
            ({ content }) => content === this.state.reaction.content
        );
        if (this.props.message.reactions.length === 0) {
            this.props.close();
        } else if (!activeReaction) {
            this.state.reaction = this.props.message.reactions[0];
        }
    }, () => [this.props.message.reactions.length]);
    
    // 表情加载
    onMounted(async () => {
        if (!loader.loaded) {
            loadEmoji();
        }
    });
    
    if (!loader.loaded) {
        loader.onEmojiLoaded(() => (this.state.emojiLoaded = true));
    }
    
    onMounted(() => void this.state.emojiLoaded);
    onPatched(() => void this.state.emojiLoaded);
}
```

**初始化内容**:
- DOM引用和服务依赖
- 表情加载状态管理
- 键盘和外部点击事件监听
- 反应状态的响应式监听

## 核心功能

### 1. 键盘导航

```javascript
onKeydown(ev) {
    switch (ev.key) {
        case "Escape":
            this.props.close();
            break;
        case "q":
            this.props.close();
            break;
        default:
            return;
    }
}
```

**键盘快捷键**:
- **ESC键**: 关闭反应菜单
- **Q键**: 快速关闭菜单
- **其他键**: 无操作

### 2. 表情短代码获取

```javascript
getEmojiShortcode(reaction) {
    return loader.loaded?.emojiValueToShortcode?.[reaction.content] ?? "?";
}
```

**功能**:
- 获取表情符号的短代码名称
- 用于显示表情的文本描述
- 提供回退值 "?" 当短代码不可用时

### 3. 反应状态监听

```javascript
useEffect(() => {
    const activeReaction = this.props.message.reactions.find(
        ({ content }) => content === this.state.reaction.content
    );
    if (this.props.message.reactions.length === 0) {
        this.props.close();
    } else if (!activeReaction) {
        this.state.reaction = this.props.message.reactions[0];
    }
}, () => [this.props.message.reactions.length]);
```

**监听逻辑**:
- **无反应时**: 自动关闭菜单
- **当前反应被删除**: 切换到第一个可用反应
- **反应数量变化**: 重新验证当前选中的反应

## 使用场景

### 1. 反应详情查看

```javascript
// 点击反应按钮显示详情菜单
const showReactionDetails = (message, reaction) => {
    dialogService.add(MessageReactionMenu, {
        message: message,
        initialReaction: reaction,
        close: () => dialogService.closeAll()
    });
};
```

### 2. 移动端反应菜单

```javascript
// 移动端长按显示反应菜单
const showMobileReactionMenu = (message, event) => {
    if (ui.isSmall) {
        event.preventDefault();
        
        dialogService.add(MessageReactionMenu, {
            message: message,
            close: () => dialogService.closeAll()
        });
    }
};
```

### 3. 反应管理界面

```javascript
// 管理员查看反应详情
const showReactionManagement = (message) => {
    if (currentUser.isAdmin) {
        dialogService.add(MessageReactionMenu, {
            message: message,
            close: () => dialogService.closeAll()
        });
    }
};
```

### 4. 反应统计显示

```javascript
// 显示反应统计信息
const ReactionStatsMenu = ({ message }) => {
    const totalReactions = message.reactions.reduce((sum, r) => sum + r.count, 0);
    
    return (
        <MessageReactionMenu 
            message={message}
            close={() => hideStatsMenu()}
        />
    );
};
```

## 菜单内容

### 1. 反应列表显示

```javascript
// 渲染反应列表
const renderReactionList = (message) => {
    return message.reactions.map(reaction => ({
        emoji: reaction.content,
        shortcode: getEmojiShortcode(reaction),
        count: reaction.count,
        users: reaction.personas.map(p => ({
            id: p.id,
            name: p.name,
            avatar: p.avatarUrl
        }))
    }));
};
```

### 2. 用户列表显示

```javascript
// 渲染反应用户列表
const renderReactionUsers = (reaction) => {
    return `
        <div class="reaction-users">
            <div class="reaction-header">
                <span class="emoji">${reaction.content}</span>
                <span class="shortcode">${getEmojiShortcode(reaction)}</span>
                <span class="count">${reaction.count}</span>
            </div>
            <div class="user-list">
                ${reaction.personas.map(persona => `
                    <div class="user-item">
                        <img src="${persona.avatarUrl}" alt="${persona.name}" />
                        <span class="user-name">${persona.name}</span>
                        <span class="user-time">${formatTime(persona.reactionTime)}</span>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
};
```

### 3. 反应切换

```javascript
// 反应标签页切换
const ReactionTabs = ({ message, currentReaction, onReactionChange }) => {
    return (
        <div class="reaction-tabs">
            {message.reactions.map(reaction => (
                <button 
                    class={`tab ${reaction === currentReaction ? 'active' : ''}`}
                    onClick={() => onReactionChange(reaction)}
                >
                    <span class="emoji">{reaction.content}</span>
                    <span class="count">{reaction.count}</span>
                </button>
            ))}
        </div>
    );
};
```

## 表情加载处理

### 1. 异步加载

```javascript
// 异步加载表情数据
onMounted(async () => {
    if (!loader.loaded) {
        try {
            await loadEmoji();
            this.state.emojiLoaded = true;
        } catch (error) {
            console.error('表情加载失败:', error);
        }
    }
});
```

### 2. 加载状态

```javascript
// 表情加载状态管理
this.state = useState({
    emojiLoaded: Boolean(loader.loaded),
    // 其他状态...
});

// 监听加载完成
if (!loader.loaded) {
    loader.onEmojiLoaded(() => {
        this.state.emojiLoaded = true;
    });
}
```

### 3. 加载回退

```javascript
// 表情未加载时的回退显示
const renderEmojiWithFallback = (emoji) => {
    if (this.state.emojiLoaded) {
        return loadEmoji(emoji);
    } else {
        return emoji; // 显示原始字符
    }
};
```

## 外部交互

### 1. 外部点击关闭

```javascript
// 点击菜单外部关闭
onExternalClick("root", () => this.props.close());
```

### 2. 键盘事件

```javascript
// 全局键盘事件监听
useExternalListener(document, "keydown", this.onKeydown);
```

### 3. 窗口事件

```javascript
// 窗口大小变化时的处理
useExternalListener(window, "resize", () => {
    if (this.ui.isSmall) {
        // 移动端适配
        adjustMenuPosition();
    }
});
```

## 样式和布局

### 1. 基础样式

```css
.message-reaction-menu {
    .dialog-content {
        max-width: 400px;
        max-height: 500px;
        overflow-y: auto;
    }
    
    .reaction-tabs {
        display: flex;
        border-bottom: 1px solid #eee;
        margin-bottom: 16px;
        
        .tab {
            flex: 1;
            padding: 8px 12px;
            border: none;
            background: none;
            cursor: pointer;
            
            &.active {
                border-bottom: 2px solid #007bff;
                color: #007bff;
            }
        }
    }
    
    .user-list {
        .user-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            
            img {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                margin-right: 12px;
            }
            
            .user-name {
                flex: 1;
                font-weight: 500;
            }
            
            .user-time {
                font-size: 0.9em;
                color: #666;
            }
        }
    }
}
```

### 2. 移动端适配

```css
@media (max-width: 768px) {
    .message-reaction-menu {
        .dialog-content {
            max-width: 100%;
            max-height: 80vh;
            margin: 0;
        }
        
        .reaction-tabs {
            .tab {
                padding: 12px 8px;
                font-size: 16px;
            }
        }
        
        .user-list {
            .user-item {
                padding: 12px 0;
                
                img {
                    width: 40px;
                    height: 40px;
                }
            }
        }
    }
}
```

## 性能优化

### 1. 虚拟化用户列表

```javascript
// 大量用户时的虚拟化
const VirtualizedUserList = ({ users, maxVisible = 20 }) => {
    const [visibleUsers, setVisibleUsers] = useState(users.slice(0, maxVisible));
    
    const loadMore = () => {
        const nextBatch = users.slice(visibleUsers.length, visibleUsers.length + maxVisible);
        setVisibleUsers(prev => [...prev, ...nextBatch]);
    };
    
    return (
        <div class="virtualized-user-list">
            {visibleUsers.map(renderUserItem)}
            {visibleUsers.length < users.length && (
                <button onClick={loadMore}>加载更多</button>
            )}
        </div>
    );
};
```

### 2. 缓存优化

```javascript
// 缓存表情短代码
const shortcodeCache = new Map();

const getCachedShortcode = (emoji) => {
    if (shortcodeCache.has(emoji)) {
        return shortcodeCache.get(emoji);
    }
    
    const shortcode = loader.loaded?.emojiValueToShortcode?.[emoji] ?? "?";
    shortcodeCache.set(emoji, shortcode);
    return shortcode;
};
```

## 可访问性

### 1. ARIA 标签

```xml
<!-- 可访问性标记 -->
<div 
    class="message-reaction-menu"
    role="dialog"
    aria-modal="true"
    aria-labelledby="menu-title"
>
    <h2 id="menu-title">消息反应详情</h2>
    
    <div class="reaction-tabs" role="tablist">
        <button 
            role="tab"
            aria-selected="true"
            aria-controls="reaction-panel"
        >
            反应标签
        </button>
    </div>
    
    <div 
        id="reaction-panel"
        role="tabpanel"
        aria-labelledby="tab-button"
    >
        反应内容
    </div>
</div>
```

### 2. 键盘导航

```javascript
// 扩展键盘导航
const handleKeyNavigation = (event) => {
    switch (event.key) {
        case 'Tab':
            // 标签页切换
            switchToNextReaction();
            break;
        case 'ArrowLeft':
        case 'ArrowRight':
            // 左右箭头切换反应
            navigateReactions(event.key);
            break;
        case 'Enter':
            // 回车键选择
            selectCurrentReaction();
            break;
    }
};
```

## 设计模式

### 1. 对话框模式 (Dialog Pattern)
- 模态对话框的标准实现
- 阻塞背景交互

### 2. 标签页模式 (Tab Pattern)
- 多个反应间的切换
- 统一的内容展示区域

### 3. 观察者模式 (Observer Pattern)
- 监听反应数据变化
- 响应键盘和鼠标事件

## 注意事项

1. **数据同步**: 确保反应数据的实时性
2. **性能考虑**: 大量用户时的渲染性能
3. **用户体验**: 提供流畅的交互体验
4. **可访问性**: 支持键盘导航和屏幕阅读器

## 扩展建议

1. **反应搜索**: 支持搜索特定用户的反应
2. **反应过滤**: 按时间或用户类型过滤
3. **反应导出**: 支持导出反应数据
4. **反应统计**: 提供详细的反应统计图表
5. **自定义视图**: 支持自定义反应显示方式

该组件为用户提供了详细的消息反应查看界面，增强了反应系统的透明度和用户体验。
